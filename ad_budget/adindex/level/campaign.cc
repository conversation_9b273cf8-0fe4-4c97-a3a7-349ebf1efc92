#include "teams/ad/ad_budget/adindex/level/campaign.h"

#include <memory>
#include <set>

#include "teams/ad/ad_budget/budget_status_server/manager/shard_mgr.h"
#include "teams/ad/ad_base/src/flyweight/flyweight.h"

DECLARE_bool(is_target_server);
DECLARE_bool(is_ad_merchant_dsp);
DECLARE_bool(is_detail);
DECLARE_int32(ksp_group_deploy_type);
DECLARE_int32(ad_index_type);
DECLARE_bool(is_universe);
DECLARE_bool(is_search);
DECLARE_bool(is_hosting_server);
DECLARE_bool(disable_amd_material);
DECLARE_bool(enable_erdian_in_thanos);
DECLARE_bool(enable_merchant_in_search);
DECLARE_bool(enable_live_stream_in_search);
DECLARE_bool(enable_fanstop_in_search);

namespace ks {
namespace ad_budget_index {
static const std::set<kuaishou::ad::AdEnum::CampaignType> kFanstopFeature{
    kuaishou::ad::AdEnum::CampaignType::AdEnum_CampaignType_AD_FANSTOP_TO_FANS,
    kuaishou::ad::AdEnum::CampaignType::AdEnum_CampaignType_AD_FANSTOP_TO_ALL,
    kuaishou::ad::AdEnum::CampaignType::AdEnum_CampaignType_AD_FANSTOP_TO_SHOW,
    kuaishou::ad::AdEnum::CampaignType::AdEnum_CampaignType_AD_FANSTOP_LIVE_TO_FANS,
    kuaishou::ad::AdEnum::CampaignType::AdEnum_CampaignType_AD_FANSTOP_LIVE_TO_SHOW,
    kuaishou::ad::AdEnum::CampaignType::AdEnum_CampaignType_AD_FANSTOP_LIVE_TO_ALL};

Campaign::Campaign() : id(0), account_id(0), day_budget(0) {
  type = kuaishou::ad::AdEnum::UNKNOWN_CAMPAIGN_TYPE;
  put_status = kuaishou::ad::AdEnum::UNKNOWN_PUT_STATUS;
  promotion_type = kuaishou::ad::AdEnum::UNKNOWN_PROMOTION;
  delivery_type = kuaishou::ad::AdEnum::UNKNOWN_CAMPAIGN_DELIVERY_TYPE;
  budget_delivery_type = kuaishou::ad::AdEnum::DEFAULT_BUDGET_DELIVERY_TYPE;
  auto_deliver_related_type = kuaishou::ad::AdEnum::NO_AUTO_DELIVERY;
  bid_type = kuaishou::ad::AdEnum::DEFAULT_BID_STRATEGY;
  charge_mode = kuaishou::ad::AdEnum::UNKNOWN_CHARGE_MODE;
  auto_manage = 0;
  project_id = 0;
  gd_status = 0;
}

Campaign &Campaign::operator=(const google::protobuf::Message &pb) {
  const kuaishou::ad::tables::Campaign *pb_ptr = nullptr;
  if (pb.GetDescriptor() == kuaishou::ad::tables::Campaign::default_instance().GetDescriptor()) {
    pb_ptr = dynamic_cast<const kuaishou::ad::tables::Campaign *>(&pb);
    if (pb_ptr == nullptr) {
      return *this;
    }
    AD_SET_FIELD_BY_PB(pb_ptr, id);
    AD_SET_FIELD_BY_PB(pb_ptr, account_id);
    fw_name = ad_base::FlyweightString::Instance().insert(pb_ptr->name());
    AD_SET_FIELD_BY_PB(pb_ptr, type);
    AD_SET_FIELD_BY_PB(pb_ptr, day_budget);
    AD_SET_FIELD_BY_PB(pb_ptr, put_status);
    AD_SET_FIELD_BY_PB(pb_ptr, ad_status);
    AD_SET_FIELD_BY_PB(pb_ptr, promotion_type);
    AD_SET_FIELD_BY_PB(pb_ptr, delivery_type);
    AD_SET_FIELD_BY_PB(pb_ptr, budget_delivery_type);
    AD_SET_FIELD_BY_PB(pb_ptr, auto_deliver_related_type);
    AD_SET_FIELD_BY_PB(pb_ptr, bid_type);
    AD_SET_FIELD_BY_PB(pb_ptr, gd_status);
    AD_SET_FIELD_BY_PB(pb_ptr, create_source_type);
    Set(*this, "budget_schedule", nullptr, nullptr, &(pb_ptr->budget_schedule()));
    AD_SET_FIELD_BY_PB(pb_ptr, auto_manage);
    AD_SET_FIELD_BY_PB(pb_ptr, project_id);
    AD_SET_FIELD_BY_PB(pb_ptr, range_budget);
    AD_SET_FIELD_BY_PB(pb_ptr, range_begin_time);
    AD_SET_FIELD_BY_PB(pb_ptr, range_end_time);
    AD_SET_FIELD_BY_PB(pb_ptr, combo_order_id);
    AD_SET_FIELD_BY_PB(pb_ptr, charge_mode);
    AD_SET_FIELD_BY_PB(pb_ptr, budget_smart_allocation);
    AD_SET_FIELD_BY_PB(pb_ptr, internal_invest_plan_id);
    AD_SET_FIELD_BY_PB(pb_ptr, auto_deliver_related_id);
    AD_SET_FIELD_BY_PB(pb_ptr, scene_oriented_type);

    // 加速探索相关
    AD_SET_FIELD_BY_PB(pb_ptr, explore_bid_type);
    AD_SET_FIELD_BY_PB(pb_ptr, explore_time_period);
    AD_SET_FIELD_BY_PB(pb_ptr, explore_time_unit);
    AD_SET_FIELD_BY_PB(pb_ptr, explore_budget_status);
    AD_SET_FIELD_BY_PB(pb_ptr, explore_budget_start_time);
    AD_SET_FIELD_BY_PB(pb_ptr, explore_put_type);
    AD_SET_FIELD_BY_PB(pb_ptr, explore_budget_threshold);

    // 粉条 fanstop_campaign_support_info 横级联
    if (kFanstopFeature.count(static_cast<kuaishou::ad::AdEnum::CampaignType>(type)) > 0) {
      fanstop_support_info_optional = std::make_unique<CampaignFansTopSupportInfo>();
      if (fanstop_support_info_optional) {
        const auto &fanstop_support_info = pb_ptr->campaign_fanstop_support_info();
        const auto *fanstop_support_info_ptr =
            dynamic_cast<const google::protobuf::Message *>(&fanstop_support_info);
        if (fanstop_support_info_ptr) {
          *fanstop_support_info_optional = *fanstop_support_info_ptr;
        }
      }
    }

    if (pb_ptr->extend_fields().schedule_list_vec_size() > 0) {
      memset(schedule_list, 0, sizeof(schedule_list));
      for (auto& schedule : pb_ptr->extend_fields().schedule_list_vec()) {
        int32_t index = schedule / 64;
        if (index < 0 || index >= kScheduleBitVecNumbers) {
          continue;
        }
        int64_t mask = 1;
        int32_t position = schedule % 64;
        schedule_list[index] |= (mask << position);
      }
    } else {
      memset(schedule_list, 0xFF, sizeof(schedule_list));
    }
  }
  return *this;
}

bool Campaign::AccountInCurrentShard() const {
  return ks::budget_status::BudgetStatusShardMgr::Instance().UnitInCurrentShard(account_id);
}

bool Campaign::IsSkip() const {
  // amd target 过滤掉非电商数据
  // 三方电商待资源到位后迁移
  // type == kuaishou::ad::AdEnum::APP ||
  // type == kuaishou::ad::AdEnum::LANDING_PAGE ||
  // type == kuaishou::ad::AdEnum::SITE_PAGE ||
  // type == kuaishou::ad::AdEnum::APP_ADVANCE);
  auto is_merchant_data =
      (IsMerchantUniqueIndex() || type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE ||
       type == kuaishou::ad::AdEnum::TAOBAO || promotion_type == kuaishou::ad::AdEnum::FLASH_PROMOTION);
  if (FLAGS_is_target_server && FLAGS_is_ad_merchant_dsp && !is_merchant_data) {
    return true;
  }

  // 粉条支持专业推广
  if (promotion_type == kuaishou::ad::AdEnum::SPECIALTY_PROMOTION &&
      ks::ad_utility::any_of(FLAGS_ksp_group_deploy_type, +DeployType::fanstop)) {
    return false;
  }

  // 粉条支持极速推广
  if (promotion_type == kuaishou::ad::AdEnum::FLASH_PROMOTION &&
      ks::ad_utility::any_of(FLAGS_ksp_group_deploy_type, +DeployType::fanstop)) {
    return false;
  }

  // 搜索广告放过支持的电商物料
  auto is_search_allow_merchant = IsSearchAllowMerchant();
  if (FLAGS_is_search && FLAGS_enable_merchant_in_search && is_search_allow_merchant) {
    return false;
  }

  // 搜索广告放过支持的粉条物料
  bool is_fanstop = IsFansTop() || IsFansTopLive();
  if (FLAGS_is_search && FLAGS_enable_fanstop_in_search && is_fanstop) {
    return false;
  }

  // 非电商 target 过滤掉电商直播数据，其它电商数据待推全后下线
  auto is_merchant_unique_data = IsMerchantUniqueIndex();
  if (FLAGS_is_target_server && !FLAGS_is_ad_merchant_dsp && !FLAGS_is_universe && is_merchant_unique_data) {
    return true;
  }

  // 非快享服务，跳过加载快享粉条直播推广的计划 (冷库可投放)
  if (ad_utility::none_of(FLAGS_ad_index_type, +AdIndexType::Default) &&
      type == kuaishou::ad::AdEnum_CampaignType_DETAIL_LIVE_STREAM_PROMOTE_CAMPAIGN &&
      FLAGS_is_target_server) {
    return true;
  }

  // 非电商下掉电商物料
  bool is_skip_merchant_type = ((!FLAGS_enable_erdian_in_thanos && type == kuaishou::ad::AdEnum::TAOBAO) ||
                                type == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE ||
                                type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE);

  if (FLAGS_disable_amd_material && is_skip_merchant_type) {
    return true;
  }

  // 联盟可投放智能托管广告
  if (FLAGS_is_universe && create_source_type == 1) {
    return false;
  }
  return false;
}

ad_base::ModifyType Campaign::JudgeModifyType() const {
  if (FLAGS_is_hosting_server) {
    return ad_base::ModifyType::kInsert;
  }
  if (!IsValid()) {
    return ad_base::ModifyType::kDelete;
  } else if (IsSkip()) {
    return ad_base::ModifyType::kDelete;
  } else if (FilterByUniverse()) {
    return ad_base::ModifyType::kDelete;
  } else if (gd_status == 1) {
    return ad_base::ModifyType::kDelete;
  }
  return ad_base::ModifyType::kInsert;
}

bool Campaign::IsMerchantUniqueIndex() const {
  return type == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE ||
         promotion_type == kuaishou::ad::AdEnum::SPECIALTY_PROMOTION ||
         promotion_type == kuaishou::ad::AdEnum::HAOWU_PROMOTION;
}

bool Campaign::IsSearchAllowMerchant() const {
  if (promotion_type == kuaishou::ad::AdEnum::SPECIALTY_PROMOTION) {
    return type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE ||
           (type == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE && FLAGS_enable_live_stream_in_search);
  }
  if (promotion_type == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
    return type == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE && FLAGS_enable_live_stream_in_search;
  }
  return false;
}

bool Campaign::IsFansTop() const {
  using ::kuaishou::ad::AdEnum;
  return type == AdEnum::AD_FANSTOP_TO_FANS || type == AdEnum::AD_FANSTOP_TO_SHOW ||
         type == AdEnum::AD_FANSTOP_TO_ALL;
}

bool Campaign::IsFansTopLive() const {
  using ::kuaishou::ad::AdEnum;
  return type == AdEnum::AD_FANSTOP_LIVE_TO_FANS || type == AdEnum::AD_FANSTOP_LIVE_TO_SHOW ||
         type == AdEnum::AD_FANSTOP_LIVE_TO_ALL;
}

bool Campaign::FilterByUniverse() const {
  if (!FLAGS_is_universe) {
    return false;
  }
  auto flow_black_list_configs = AdIndexKconf::flowBlackListConfig();
  auto it_flow_black_list_config =
      flow_black_list_configs->config.find(kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNIVERSE);
  if (it_flow_black_list_config == flow_black_list_configs->config.end()) {
    return false;
  }
  return it_flow_black_list_config->second.FilterCampaignAndType(id, type);
}

std::string Campaign::ToString() const {
  nlohmann::json njj;
  to_json(njj);
  return njj.dump();
}

void Campaign::to_json(nlohmann::json &njj) const {
  ad_budget_index::to_json(njj, *this);
  if (this->fanstop_support_info_optional) {
    njj["fanstop_support_info"] = *(this->fanstop_support_info_optional.get());
  }
  njj["budget_schedule_list"] = *fw_budget_schedule_list;
}

std::string Campaign::ToJson() const {
  return ToString();
}

bool CampaignNameParser::Set(Campaign &campaign,
                             const int64_t *int64_value,
                             const double *double_value,
                             const std::string *string_value) {
  if (string_value == nullptr) {
    return false;
  }
  auto &flyweight_string = ad_base::FlyweightString::Instance();
  campaign.fw_name = flyweight_string.insert(*string_value);
  return true;
}

bool CampaignBudgetScheduleParser::Set(Campaign &c,
                                       const int64_t *int64_value,
                                       const double *double_value,
                                       const std::string *string_value) {
  static const std::string &kName{"campaign"};  // NOLINT
  AdIndexCommon::BSLT sl;
  auto status = ParseSchedule(kName, string_value, sl);
  auto &fw =
      ad_base::Flyweight<AdIndexCommon::BSLT, ad_base::flyweight::AbslHash<AdIndexCommon::BSLT>>::Instance();
  c.fw_budget_schedule_list = fw.insert(sl);

  return status;
}

bool Campaign::IsInAccExploreStatus(int64_t now_ms, int32_t* bid_type) const {
  // TODO(yinliang) imp

  int64_t explore_hour = 6 * 3600 * 1000;
  if (explore_bid_type == static_cast<int32_t>(kuaishou::ad::AdEnum::EXPLORE_RECOVERY_BID_TYPE)) {
    explore_hour = explore_time_period * 3600 * 1000;
  } else if (explore_bid_type == static_cast<int32_t>(kuaishou::ad::AdEnum::EXPLORE_BUDGET_BID_TYPE) &&
             explore_time_period > 0) {
    if (explore_time_unit == kuaishou::ad::AdEnum::EXPLORE_TIME_MINUTE) {
      LOG_EVERY_N(INFO, 1000000) << "IsInAccExploreStatus minute campaign_id: " << id
                                 << ", explore_time_unit: " << explore_time_unit
                                 << ", explore_time_period: " << explore_time_period;
      explore_hour = explore_time_period * 60 * 1000;
    } else if (explore_time_unit == kuaishou::ad::AdEnum::EXPLORE_TIME_HOUR) {
      LOG_EVERY_N(INFO, 1000000) << "IsInAccExploreStatus hour campaign_id: " << id
                                 << ", explore_time_unit: " << explore_time_unit
                                 << ", explore_time_period: " << explore_time_period;
      explore_hour = explore_time_period * 3600 * 1000;
    } else {
      LOG_EVERY_N(INFO, 1000000) << "IsInAccExploreStatus error campaign_id: " << id
                                 << ", explore_time_unit: " << explore_time_unit
                                 << ", explore_time_period: " << explore_time_period;
    }
  }

  *bid_type = explore_bid_type;
  return (explore_budget_status == 1 && (now_ms - explore_budget_start_time < explore_hour) &&
          (now_ms > explore_budget_start_time));

  return false;
}

bool Campaign::IsIncrementAcc() const {
  // TODO(yinliang) imp

  int64_t now_ms = base::GetTimestamp() / 1000;
  int32_t explore_bid_type = 0;
  // 先判断状态
  if (!IsInAccExploreStatus(now_ms, &explore_bid_type)) {
    return false;
  }
  // 再判断类型
  if (explore_bid_type != kuaishou::ad::AdEnum::EXPLORE_BUDGET_BID_TYPE) {
    return false;
  }
  return explore_put_type == 3;
}

int64_t Campaign::GetInnerLoopExploreDayBudget() const {
  // TODO(yinliang) imp
  return explore_budget_threshold;
}

}  // namespace ad_budget_index
}  // namespace ks
