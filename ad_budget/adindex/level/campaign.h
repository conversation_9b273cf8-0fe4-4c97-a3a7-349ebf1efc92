#pragma once

#include <array>
#include <memory>
#include <sstream>
#include <string>
#include <vector>

#include "falcon/counter.h"
#include "nlohmann/json.hpp"

#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_base/src/common/deploy_utils.h"
#include "teams/ad/ad_index/framework/access/dynamic_access.h"
#include "teams/ad/ad_index/framework/core/data_access.h"
#include "teams/ad/ad_budget/adindex/api/kconf_util.h"
#include "teams/ad/ad_budget/adindex/api/public.h"
#include "teams/ad/ad_budget/adindex/level/campaign_fanstop_support_info.h"
#include "teams/ad/ad_budget/adindex/level/common.h"
#include "teams/ad/ad_budget/adindex/level/enums.h"

namespace ks {
namespace ad_budget_index {

using ks::ad_base::DeployType;
using ks::ad_base::AdIndexType;
static const int kScheduleBitVecNumbers = 3;

struct Campaign : public ks::ad_base::AssignFromProto {
  AD_DYNAMIC_ACCESS(Campaign);

  Campaign();
  Campaign &operator=(const google::protobuf::Message &pb) override;

  static constexpr int32_t kDayInWeek = 7;

  int64_t id;
  int64_t account_id;
  int64_t day_budget;          // 为 0 默认为预算不限
  int64_t gd_status;           // 0: 非保量广告，1: 保量广告，
  int64_t create_source_type;  // 广告来源
  int32_t auto_manage = 0;
  int32_t budget_smart_allocation = 0;
  // fanstop
  int64_t range_end_time;
  int64_t range_begin_time;
  int64_t range_budget = 0;
  int64_t seq_no = 0;
  int64_t project_id = 0;
  kuaishou::ad::AdEnum::PutStatus put_status;
  kuaishou::ad::AdEnum::CampaignType type;
  kuaishou::ad::AdEnum::CampaignPromotionType promotion_type;  // 推广方式类型
  kuaishou::ad::AdEnum::CampaignDeliveryType delivery_type;    // 计划投放方式
  kuaishou::ad::AdEnum::BidStrategyGroup bid_type;
  kuaishou::ad::AdEnum::BudgetDeliveryType budget_delivery_type;
  kuaishou::ad::AdEnum::AutoDeliverRelatedType auto_deliver_related_type;
  std::shared_ptr<CampaignFansTopSupportInfo> fanstop_support_info_optional;
  const AdIndexCommon::BSLT *fw_budget_schedule_list = &AdIndexCommon::kBudgetScheduleListEmpty;
  const std::string* fw_name = &AdIndexCommon::kEmptyString;
  uint64_t combo_order_id;
  kuaishou::ad::AdEnum::CampaignChargeMode charge_mode;
  int64_t auto_deliver_related_id;  // 自动投放关联 id, 影子 campaign 对应的主 campaign_id
  int64_t internal_invest_plan_id;
  int64_t schedule_list[kScheduleBitVecNumbers];
  int32_t scene_oriented_type = 0;
  int32_t ad_status = 0;

  // 加速探索相关
  int32_t explore_bid_type = kuaishou::ad::AdEnum::UNKNOWN_EXPLORE_BID_TYPE;
  int32 explore_time_period = 0;
  kuaishou::ad::AdEnum::UnitExploreTimeUnit explore_time_unit = kuaishou::ad::AdEnum::EXPLORE_TIME_HOUR;
  int32_t explore_budget_status = 0;
  int64_t explore_budget_start_time = 0;
  int32_t explore_put_type = 0;
  int64_t explore_budget_threshold = 0;

  TT_EQUAL_NOT_INTRUSIVE(Campaign,
                         id,
                         account_id,
                         type,
                         day_budget,
                         gd_status,
                         create_source_type,
                         range_end_time,
                         range_begin_time,
                         range_budget,
                         put_status,
                         ad_status,
                         type,
                         promotion_type,
                         delivery_type,
                         bid_type,
                         fw_budget_schedule_list,
                         fw_name,
                         combo_order_id,
                         internal_invest_plan_id,
                         charge_mode,
                         budget_smart_allocation,
                         auto_manage,
                         project_id,
                         scene_oriented_type,
                         budget_delivery_type,
                         auto_deliver_related_type,
                         auto_deliver_related_id)

  const CampaignFansTopSupportInfo *campaign_fanstop_support_info() const {
    return fanstop_support_info_optional.get();
  }

  void to_json(nlohmann::json &) const;
  std::string ToString() const;
  std::string ToJson() const;
  bool IsFansTop() const;
  bool IsFansTopLive() const;

  int64_t GetDayBudget(size_t wd) const {
    return fw_budget_schedule_list->empty()
               ? day_budget
               : fw_budget_schedule_list->at(wd % fw_budget_schedule_list->size());
  }

  int64_t GetRangeBudget() const {
    return range_budget;
  }

  // pos 表示一周中的第几个小时；从 0 起计，表示周一 0 点
  bool InTheTimeScheduling(size_t pos) const {
    int32_t index = pos / 64;
    if (index < 0 || index >= kScheduleBitVecNumbers) {
      return false;
    }

    int32_t position = pos % 64;
    int64_t mask = 1;

    return schedule_list[index] & (mask << position);
  }

  // 是否锁预算计划
  bool IsLockBudgetCampaign() const {
    return budget_delivery_type == kuaishou::ad::AdEnum::LOCK_BUDEGET_DELIVERY_TYPE_;
  }

  bool IsParentDeliveryCampaign() const {
    return auto_deliver_related_type == kuaishou::ad::AdEnum::PARENT_DELIVERY;
  }

  bool IsChildDeliveryCampaign() const {
    return auto_deliver_related_type == kuaishou::ad::AdEnum::CHILD_DELIVERY;
  }

  // 是否移动端切专推的物料
  bool IsMobile2SpecialtyAd() const {
    return (type == ::kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE ||
            type == ::kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) &&
           charge_mode == ::kuaishou::ad::AdEnum::ORDER;
  }

  int64_t GetHostingCampaignId() const {
    return auto_deliver_related_id;
  }

  bool IsSkip() const;
  ad_base::ModifyType JudgeModifyType() const;
  bool FilterByUniverse() const;
  bool AccountInCurrentShard() const;
  inline bool IsValid() const {
    if (!AccountInCurrentShard()) {
      return false;
    }
    if (AdIndexKconf::enableUseAdStatus() && ad_status == 1) {
      return false;
    }
    return put_status == ::kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN;
  }

  static bool IsValid(const Campaign *const p_campaign) {
    return p_campaign && p_campaign->IsValid();
  }

  inline bool IsApp() const {
    return type == ::kuaishou::ad::AdEnum_CampaignType_APP;
  }
  // 是否下载类
  inline bool IsDownload() const {
    return type == ::kuaishou::ad::AdEnum::APP || type == ::kuaishou::ad::AdEnum::DPA_CAMPAIGN;
  }

  inline bool IsInnerLoop() const {
    if (promotion_type == kuaishou::ad::AdEnum::FLASH_PROMOTION ||
        promotion_type == kuaishou::ad::AdEnum::SPECIALTY_PROMOTION) {
      return true;
    }
    return false;
  }

  bool IsInAccExploreStatus(int64_t now_ms, int32_t* bid_type) const;
  bool IsIncrementAcc() const;
  int64_t GetInnerLoopExploreDayBudget() const;

 private:
  bool IsMerchantUniqueIndex() const;
  bool IsSearchAllowMerchant() const;
};

NLOHMANN_DEFINE_TYPE_NON_INTRUSIVE(Campaign,
                                   id,
                                   account_id,
                                   type,
                                   day_budget,
                                   put_status,
                                   ad_status,
                                   gd_status,
                                   promotion_type,
                                   delivery_type,
                                   bid_type,
                                   create_source_type,
                                   range_begin_time,
                                   range_end_time,
                                   range_budget,
                                   budget_smart_allocation,
                                   charge_mode,
                                   scene_oriented_type,
                                   budget_delivery_type,
                                   auto_deliver_related_type,
                                   auto_deliver_related_id,
                                   seq_no)

class CampaignBudgetScheduleParser : public ::ks::ad_base::DynamicAccessor<Campaign> {
 public:
  bool Set(Campaign &campaign,
           const int64_t *int64_value,
           const double *double_value,
           const std::string *string_value) override;
};

class CampaignNameParser : public ::ks::ad_base::DynamicAccessor<Campaign> {
 public:
  bool Get(const Campaign &campaign,
           int64_t *int64_value,
           double *double_value,
           std::string *string_value) const override {
    if (string_value == nullptr) {
      return false;
    }
    *string_value = *(campaign.fw_name);
    return true;
  }

  bool Set(Campaign &campaign,
           const int64_t *int64_value,
           const double *double_value,
           const std::string *string_value) override;
};

AD_DYNAMIC_FIELD(Campaign, int64_t, id);
AD_DYNAMIC_FIELD(Campaign, int64_t, account_id);
AD_DYNAMIC_FIELD(Campaign, kuaishou::ad::AdEnum::CampaignType, type);
AD_DYNAMIC_FIELD(Campaign, int64_t, day_budget);
AD_DYNAMIC_FIELD(Campaign, kuaishou::ad::AdEnum::PutStatus, put_status);
AD_DYNAMIC_FIELD(Campaign, int32_t, ad_status);
AD_DYNAMIC_FIELD(Campaign, kuaishou::ad::AdEnum::CampaignPromotionType, promotion_type);
AD_DYNAMIC_FIELD(Campaign, kuaishou::ad::AdEnum::CampaignDeliveryType, delivery_type);
AD_DYNAMIC_FIELD(Campaign, kuaishou::ad::AdEnum::BudgetDeliveryType, budget_delivery_type);
AD_DYNAMIC_FIELD(Campaign, kuaishou::ad::AdEnum::AutoDeliverRelatedType, auto_deliver_related_type);
AD_DYNAMIC_FIELD(Campaign, kuaishou::ad::AdEnum::BidStrategyGroup, bid_type);
AD_DYNAMIC_FIELD(Campaign, kuaishou::ad::AdEnum::CampaignChargeMode, charge_mode);

AD_DYNAMIC_FIELD(Campaign, int64_t, gd_status);
AD_DYNAMIC_FIELD(Campaign, int64_t, create_source_type);
AD_DYNAMIC_COMPLEX_FIELD(Campaign, std::string, budget_schedule, CampaignBudgetScheduleParser);
AD_DYNAMIC_COMPLEX_FIELD(Campaign, std::string, name, CampaignNameParser);
AD_DYNAMIC_FIELD(Campaign, int32_t, auto_manage);
AD_DYNAMIC_FIELD(Campaign, int64_t, project_id);
AD_DYNAMIC_FIELD(Campaign, int64_t, range_budget);
AD_DYNAMIC_FIELD(Campaign, int64_t, range_begin_time);
AD_DYNAMIC_FIELD(Campaign, int64_t, range_end_time);
AD_DYNAMIC_FIELD(Campaign, int64_t, auto_deliver_related_id);
AD_DYNAMIC_FIELD(Campaign, int32_t, scene_oriented_type);
AD_DYNAMIC_FIELD(Campaign, int32_t, budget_smart_allocation);

}  // namespace ad_budget_index
}  // namespace ks

namespace ks {
namespace ad_base {

template <> class ModifyTypeJudger<::ks::ad_budget_index::Campaign> {
 public:
  ModifyType operator()(::ks::ad_budget_index::Campaign &data) const {  // NOLINT
    return data.JudgeModifyType();
  }
};

}  // namespace ad_base
}  // namespace ks
