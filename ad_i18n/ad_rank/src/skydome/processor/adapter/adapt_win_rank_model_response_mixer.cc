#include "teams/ad/ad_i18n/ad_rank/src/skydome/processor/adapter/adapt_win_rank_model_response_mixer.h"

#include <google/protobuf/util/json_util.h>
#include <google/protobuf/util/message_differencer.h>

#include <string>
#include <memory>
#include <utility>
#include <unordered_map>

using ad_i18n::ad_rank::AdCommon;
using ad_i18n::ad_rank::PredictProcessParams;

using kuaishou::ad::algorithm::UniversePredictRequest;
using kuaishou::ad::algorithm::UniversePredictResponse;

namespace ad_i18n::ad_rank::dragon {
static ks::ad_base::FNVHash fnv_hashstr_v2;
void AdaptWinRankModelResponseMixer::Mix(ks::platform::AddibleRecoContextInterface* c) {
  int64_t llsid = c->GetIntCommonAttr("llsid").value_or(0);
  auto* response_pb = const_cast<UniversePredictResponse *>(
    c->GetProtoMessagePtrCommonAttr<UniversePredictResponse>(response_attr_));
  if (!response_pb) {
    LOG_EVERY_N(WARNING, FLAGS_log_every_n) << "response_pb is nullptr. llsid: " << llsid;
    return;
  }
  if (response_pb->status() != kuaishou::ad::algorithm::STATUS_OK) {
    c->SetIntCommonAttr("ranking_predict_status", false);
    LOG_EVERY_N(ERROR, FLAGS_log_every_n)
      << "DEBUG.rty response status is Error.llsid: " << llsid
      << ", response status: " << kuaishou::ad::algorithm::PredictStatus_Name(response_pb->status());
    return;
  }
  if (response_pb->predict_result_v2_size() <= 0) {
    LOG_EVERY_N(ERROR, FLAGS_log_every_n)
      << "DEBUG.rty response size is Error.llsid: " << llsid << ", the returned result is empty.";
    return;
  }
  auto predict_process_param = std::make_unique<PredictProcessParams>();
  predict_process_param->enable_universe_win_rate_model_ =
    c->GetIntCommonAttr("enable_universe_win_rate_model_").value_or(0);
  predict_process_param->enable_universe_win_rate_model_gcm_ =
    c->GetIntCommonAttr("enable_universe_win_rate_model_gcm_").value_or(0);
  ad_i18n::ad_rank::RegisterAllDefault(predict_process_param.get());
  auto* cmd_table = c->GetOrInsertDataTable(cmd_table_);
  auto* cmd_attr = cmd_table->GetOrInsertAttr("cmd");
  auto* is_auc_attr = cmd_table->GetOrInsertAttr("is_auc");
  auto* cmd_id_attr = cmd_table->GetOrInsertAttr("cmd_id");
  auto* cmd_key_attr = cmd_table->GetOrInsertAttr("cmd_key");
  auto* rt_list_attr = cmd_table->GetOrInsertAttr("rt");
  auto* pt_list_attr = cmd_table->GetOrInsertAttr("pt");
  auto* r_size_attr = cmd_table->GetOrInsertAttr("r_size");
  auto* item_ids_attr = cmd_table->GetOrInsertAttr("item_ids_list");
  auto* cmd_value_num_attr = cmd_table->GetOrInsertAttr("cmd_value_num");
  auto* mutable_ad_list = c->GetMutablePtrCommonAttr<AdList>("ad_list");
  if (mutable_ad_list == nullptr) { return; }
  for (const auto& cmd_response : response_pb->predict_result_v2()) {
    const auto& item_result_list = cmd_response.item_result();
    std::string response_cmd_key = cmd_response.cmd_key();
    uint64_t item_key = fnv_hashstr_v2(response_cmd_key.data(), response_cmd_key.size());
    auto table_cmd_key = c->GetStringItemAttr(item_key, cmd_key_attr).value_or("");
    if (table_cmd_key.empty() || table_cmd_key != response_cmd_key) {
      LOG_EVERY_N(WARNING, FLAGS_log_every_n)
        << "DEBUG AdaptModelPredictResponseMixer. response.cmd.cmd_key unknow. llsid: " << llsid
        << " cmd_key:" << cmd_response.cmd_key()
        << " cmd: " << cmd_response.cmd();
      return;
    }
    auto cmd_item_ids = c->GetIntListItemAttr(item_key, item_ids_attr).value_or(absl::Span<const int64_t>());
    int64_t cmd_valid_num = c->GetIntItemAttr(item_key, cmd_value_num_attr).value_or(0);
    auto rt_list = c->GetIntListItemAttr(item_key, rt_list_attr).value_or(absl::Span<const int64_t>());
    auto pt_list = c->GetIntListItemAttr(item_key, pt_list_attr).value_or(absl::Span<const int64_t>());
    bool is_auc = c->GetIntItemAttr(item_key, is_auc_attr).value_or(0);
    int32_t cmd_id = static_cast<int32_t>(c->GetIntItemAttr(item_key, cmd_id_attr).value_or(0));
    std::string cmd = std::string(c->GetStringItemAttr(item_key, cmd_attr).value_or(""));
    for (const auto& item_result : item_result_list) {
      if (item_result.value_size() != cmd_valid_num ||
        item_result.value_size() != pt_list.size() ||
        item_result.value_size() != rt_list.size()) {
        LOG_EVERY_N(WARNING, FLAGS_log_every_n)
          << "DEBUG AdaptModelPredictResponseMixer.response.cmd.item_size error.llsid: " << llsid
          << " key:" << cmd_response.cmd_key()
          << " cmd: " << cmd_response.cmd() << " pt.size: " << pt_list.size()
          << " rt.size: " << rt_list.size()
          << " expect: " << cmd_valid_num << " receive: " << item_result.value_size();
          continue;
      }
      uint64_t ps_key_id = mutable_ad_list->GetPsKeyByItemId(item_result.item_id());
      auto* p_ad = mutable_ad_list->GetAdCommonByPsKey(ps_key_id);
      if (p_ad == nullptr) {
        LOG_EVERY_N(WARNING, FLAGS_log_every_n)
          << "DEBUG AdaptModelPredictResponseMixer. not found ad.llsid: " << llsid
          << ", item_id: " << item_result.item_id()  << ", ps_key_id=" << ps_key_id;
        continue;
      }
      // 交叉 AUC CMD 预估分数填充
      if (is_auc) {
        p_ad->ps_score.auc_cmd_id_2_scores[cmd_id].reserve(cmd_valid_num);
        p_ad->ps_score.auc_cmd_id_2_scores[cmd_id].clear();
        p_ad->ps_score.auc_cmd_id_2_scores[cmd_id].assign(
          item_result.value().begin(), item_result.value().end());
        continue;
      }
      // 普通 CMD 预估分数填充
      for (int idx = 0; idx < item_result.value_size(); idx++) {
        auto value = item_result.value(idx);
        p_ad->ps_score.SetScoreByPredictType(pt_list[idx], value, cmd_id);
      }
    }
  }
  for (auto& p_ad : mutable_ad_list->Ads()) {
    ad_i18n::ad_rank::FillUniversalWinRate(p_ad, predict_process_param.get());
  }
  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdaptWinRankModelResponseMixer, AdaptWinRankModelResponseMixer);

}   // namespace ad_i18n::ad_rank::dragon
