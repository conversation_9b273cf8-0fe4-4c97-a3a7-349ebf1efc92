#pragma once

#include <string>
#include "dragon/src/processor/base/common_reco_base_mixer.h"
#include "teams/ad/ad_i18n/ad_rank/src/pslab/init.h"
#include "teams/ad/ad_i18n/ad_rank/src/context_data/context_data.h"
#include "teams/ad/engine_base/dragon_node/skydome_node.h"
#include "teams/ad/engine_base/dragon_node/ad_rpc_proxy.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_i18n/ad_rank/src/ad_rank/plugin/predict_process/fill_cxr_strategy.h"
#include "teams/ad/ad_i18n/ad_rank/src/ad_rank/plugin/predict_process/predict_process_param.h"

using namespace ks::engine_base::dragon;  // NOLINT
namespace ad_i18n::ad_rank::dragon {

class AdaptWinRankModelResponseMixer : public ::ks::platform::CommonRecoBaseMixer {
 public:
  AdaptWinRankModelResponseMixer() {}
  void Mix(ks::platform::AddibleRecoContextInterface* context) override;

 private:
  bool InitProcessor() {
    cmd_table_ = config()->GetString("cmd_table", "cmd_table");
    if (cmd_table_.empty()) {
      LOG(ERROR) << "AdaptWinRankModelResponseMixer init `cmd_table` error!";
      return false;
    }
    response_attr_ = config()->GetString("response_attr", "response_attr");
    if (response_attr_.empty()) {
      LOG(ERROR) << "AdaptWinRankModelResponseMixer init `response_attr` error!";
      return false;
    }
    return true;
  }

 private:
  std::string cmd_table_;
  std::string response_attr_;

 private:
  DISALLOW_COPY_AND_ASSIGN(AdaptWinRankModelResponseMixer);
};

}   // namespace ad_i18n::ad_rank::dragon
