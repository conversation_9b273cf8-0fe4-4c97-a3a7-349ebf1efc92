import json
CmdManagerConfig = [
  {
    "rt":"UNCATEGORIZED",
    "cmd_key":"cmd_ab_key_of_universe_win_rate_v1",
    "pt":[
        "PredictType_universe_win_rate_miu",
        "PredictType_universe_win_rate_sigma",
    ],
    "pv_admit": "request_type == 6 and enable_universe_win_rate_model_ == 1",
  },
  {
    "rt":"UNCATEGORIZED",
    "cmd_key":"cmd_ab_key_of_universe_win_rate_gcm_v1",
    "pt":[
        "PredictType_universe_win_rate_bucket_prob_0",
        "PredictType_universe_win_rate_bucket_prob_1",
        "PredictType_universe_win_rate_bucket_prob_2",
        "PredictType_universe_win_rate_bucket_prob_3",
        "PredictType_universe_win_rate_bucket_prob_4",
        "PredictType_universe_win_rate_bucket_prob_5",
        "PredictType_universe_win_rate_bucket_prob_6",
        "PredictType_universe_win_rate_bucket_prob_7",
        "PredictType_universe_win_rate_bucket_prob_8",
        "PredictType_universe_win_rate_bucket_prob_9",
        "PredictType_universe_win_rate_bucket_prob_10",
        "PredictType_universe_win_rate_bucket_prob_11",
        "PredictType_universe_win_rate_bucket_prob_12",
        "PredictType_universe_win_rate_bucket_prob_13",
        "PredictType_universe_win_rate_bucket_prob_14",
        "PredictType_universe_win_rate_bucket_prob_15",
        "PredictType_universe_win_rate_bucket_prob_16",
        "PredictType_universe_win_rate_bucket_prob_17",
        "PredictType_universe_win_rate_bucket_prob_18",
        "PredictType_universe_win_rate_bucket_prob_19",
        "PredictType_universe_win_rate_bucket_prob_20",
        "PredictType_universe_win_rate_bucket_prob_21",
        "PredictType_universe_win_rate_bucket_prob_22",
        "PredictType_universe_win_rate_bucket_prob_23",
        "PredictType_universe_win_rate_bucket_prob_24",
        "PredictType_universe_win_rate_bucket_prob_25",
        "PredictType_universe_win_rate_bucket_prob_26",
        "PredictType_universe_win_rate_bucket_prob_27",
        "PredictType_universe_win_rate_bucket_prob_28",
        "PredictType_universe_win_rate_bucket_prob_29",
        "PredictType_universe_win_rate_bucket_prob_30",
        "PredictType_universe_win_rate_bucket_prob_31",
        "PredictType_universe_win_rate_bucket_prob_32"
    ],
    "pv_admit": "request_type == 6 and enable_universe_win_rate_model_gcm_ == 1",
  }
]
