from dragonfly.common_leaf_dsl import current_flow as flow
from dragonfly.common_leaf_dsl import Leaf<PERSON>low

from ks_leaf_functional.core.module import module 
from ks_leaf_functional.core.data_manager import ab_param as ab
from ks_leaf_functional.utils.common_utils import get_dynamic_param
from ad_rank_api_mixin import *
from dragonfly.ext.ad_model.ad_model_helper import *
from dragonfly.ext.ad_model.ad_model_mixin import *
from dragonfly.ext.ad_base.ad_base_mixin import *
from framework.model.cmd import *

MODEL_CMD_CONFIG_PATH = 'rerank_data/universe_model_predict/config'

@module()
def build_cmd_field():
  cmd_list = load_cmd_config(MODEL_CMD_CONFIG_PATH)
  flow() \
    .do(_prepare_cmd_config(cmd_list)) \
    .do(_add_cpt_cmds(cmd_list)) \
    .do(_create_cpt_table(cmd_list)) \
    .do(_build_cmd_field(cmd_list)) \

def _prepare_cmd_config(cmd_list):
  flow() \
    .get_abtest_params(
      biz_name = "KWAI_I18N_AD_FEED",
      ab_params=[(cmd._construct_ab_param()) for cmd in cmd_list]
    ) \
    .set_attr_value(
      item_table="infeed_ad_list",
      common_attrs=[(cmd._construct_cmd_key()) for cmd in cmd_list],
      item_attrs=[dict(name="win_rank_cmd_key_list",type="string_list",value=[])]
    )

def _add_cpt_cmds(cmd_list):
  for cmd in cmd_list:
    if cmd.pv_admit == "":
      flow() \
        .append_value(
          item_table="infeed_ad_list",
          select_item_by_expression=cmd.item_admit,
          attrs=[
            dict(from_common="key_"+cmd.cmd_key, append_to_item="win_rank_cmd_key_list")
          ]
        )
    else:
      flow() \
        .if_(cmd.pv_admit) \
          .append_value(
            item_table="infeed_ad_list",
            select_item_by_expression=cmd.item_admit,
            attrs=[
              dict(from_common="key_"+cmd.cmd_key, append_to_item="win_rank_cmd_key_list")
            ]
          ) \
        .end_if_()

def _create_cpt_table(cmd_list):
  flow() \
    .create_logic_table(
      item_table="infeed_ad_list",
      logic_table="win_rank_cmd_logic_table",
      select_attr=["win_rank_cmd_key_list","creative_id","enable_skip_cmd_request","item_mapping_key_id","valid"],
      select_item_by_expression="enable_skip_cmd_request != 1 and valid != 0"
    ) \
    .expand_by(
      from_table="win_rank_cmd_logic_table",
      to_table="win_rank_cmd_expand_table",
      by_attr="win_rank_cmd_key_list",
      expand_source_table_key_as="win_rank_primary_key_id",
      copy_attrs=[
        {"name": "creative_id", "copy_mode" : "OVERWRITE", "as" : "creative_id"},
        {"name": "item_mapping_key_id", "copy_mode": "OVERWRITE", "as": "ps_key_id"}
      ]
    ) \
    .ad_hash_enricher(
      hash_type="FNV_HASH",
      item_table="win_rank_cmd_expand_table",
      input_item_attrs=["win_rank_cmd_key_list"],
      output_item_attrs=["win_rank_cmd_hash_list"]
    ) \
    .group_by(
      from_table="win_rank_cmd_expand_table",
      to_table="win_rank_cmd_cpt_table",
      by_attr="win_rank_cmd_hash_list",
      copy_attrs=[
        {"name": "ps_key_id", "copy_mode": "CONCAT", "as": "item_ids_list"},
        {"name": "win_rank_cmd_key_list", "copy_mode": "OVERWRITE", "as":"cmd_single_key"}]
    )

def _build_cmd_field(cmd_list):
  with AdModelGuard() as model_guard:
    AD_PREDICT_DICT = model_guard.ad_predict_type()
    AD_RESULT_DICT = model_guard.ad_result_type()
    flow() \
      .cmd_table(
        cmd_cpt_table="win_rank_cmd_cpt_table",
        cpt_table_attrs=dict(cmd_key="cmd_single_key", cmd_item_ids_key="item_ids_list"),
        cmd_output_table="win_rank_cmd_table",
        cmd_output_attrs=[
          (cmd._construct_cmd_config(AD_RESULT_DICT, AD_PREDICT_DICT)) for cmd in cmd_list
        ]
      ) \
      .count_reco_result(
        item_table="win_rank_cmd_table",
        save_count_to="win_rank_valid_cmd_count"
      ) \
      .transform_cmd_table_to_protobuf(
        need_item_set=True,
        item_id_set="win_rank_item_id_set",
        cmd_table="win_rank_cmd_table",
        transform_protobuf_attr="universe_win_rank_predict_request",
      )
