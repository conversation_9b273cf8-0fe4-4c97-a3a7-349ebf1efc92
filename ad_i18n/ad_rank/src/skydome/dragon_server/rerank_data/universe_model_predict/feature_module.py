from dragonfly.common_leaf_dsl import current_flow as flow
from dragonfly.common_leaf_dsl import Leaf<PERSON><PERSON>

from ks_leaf_functional.core.module import module 
from ks_leaf_functional.core.data_manager import ab_param as ab
from ks_leaf_functional.utils.common_utils import get_dynamic_param
from ad_rank_api_mixin import *
from dragonfly.ext.ad_model.ad_model_helper import *
from dragonfly.ext.ad_model.ad_model_mixin import *

@module()
def build_feature_field():
  flow() \
    .do(build_user_feature()) \
    .do(build_context_feature()) \
    .do(build_item_feature())

def build_context_feature():
  flow() \
    .count_reco_result(
      item_table="infeed_ad_list",
      save_count_to="win_rank_valid_item_count",
      select_item_by_expression="valid != 0"
    ) \
    .if_("win_rank_valid_item_count > 100") \
      .set_attr_value(
        common_attrs=[dict(name="win_rank_max_pack_num", type="int", value=100)]
      ) \
    .else_() \
      .copy_attr(
        overwrite=True,
        attrs=[dict(from_common="win_rank_valid_item_count", to_common="win_rank_max_pack_num")]
      ) \
    .end_() \
    .pack_item_attr(
      item_table="infeed_ad_list",
      select_item_by_expression="enable_skip_cmd_request != 1 and valid != 0",
      item_source=dict(reco_results=True, total_limit='{{win_rank_max_pack_num}}'),
      mappings=[
        dict(from_item_attr="cpm", to_common_attr="context_feature_cpm_score_list"),
        dict(from_item_attr="creative_id", to_common_attr="context_feature_creative_list"),
      ]
    )

  with AdModelGuard() as model_guard:
    AD_FEATURE_CLASS = model_guard.ad_feature_proto()
    Feature = AD_FEATURE_CLASS.ContextInfoCommonAttr.Name
    Type = AD_FEATURE_CLASS.CommonTypeEnum.AttrType
    flow() \
      .context_feature_table(
        output_table="win_rank_context_feature_table",
        kv_attrs=[
          dict(feature=Feature.I18N_CPM, type=Type.UINT64_LIST_ATTR, first_key="context_feature_cpm_score_list"),
          dict(feature=Feature.I18N_WINRANK_CREATIVE_ID, type=Type.UINT64_LIST_ATTR, first_key="context_feature_creative_list"),
        ]
      ) \
      .transform_context_feature_table_to_protobuf(
        transform_protobuf_attr="universe_win_rank_predict_request",
        context_feature_table="win_rank_context_feature_table"
      )

def build_item_feature():
  flow() \
    .item_feature_table(
      item_table="infeed_ad_list",
      output_table="win_rank_item_feature_table",
      select_item_by_expression="enable_skip_cmd_request != 1 and valid != 0",
      output_item_key="item_mapping_key_id",
      item_info_attrs=[
        dict(source_field="item_mapping_key_id", dest_field="cretive_id"),
        dict(source_field="photo_id", dest_field="photo_id"),
        dict(source_field="item_type", dest_field="item_type"),
        dict(source_field="live_id", dest_field="live_id")
      ]
    ) \
    .transform_item_feature_table_to_protobuf(
      need_item_set=True,
      item_id_set="win_rank_item_id_set",
      item_feature_table="win_rank_item_feature_table",
      transform_protobuf_attr="universe_win_rank_predict_request"
    )

def build_user_feature():
  flow() \
    .do_nothing()
