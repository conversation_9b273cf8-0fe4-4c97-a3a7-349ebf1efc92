from dragonfly.common_leaf_dsl import current_flow as flow
from dragonfly.common_leaf_dsl import Leaf<PERSON><PERSON>

from ks_leaf_functional.core.module import module 
from ks_leaf_functional.core.data_manager import ab_param as ab
from ks_leaf_functional.utils.common_utils import get_dynamic_param
from ad_rank_api_mixin import *

def _prepare_static_field():
  flow() \
    .set_attr_value(
      common_attrs=[
        # kuaishou::ad::algorithm::ItemType::AD_DSP
        # constexpr uint32 CMD_REQ_NEW_VER = 3
        dict(name="model_item_type", type="int", value=1),
        dict(name="model_cmd_version", type="int", value=3),
      ]
    )

@module()
def build_base_field():
  flow() \
    .do(_prepare_static_field()) \
    .build_protobuf(
      class_name="kuaishou::ad::algorithm::UniversePredictRequest",
      inputs=[
        { "common_attr": "llsid", "path": "llsid" },
        { "common_attr": "user_id", "path": "user_id" },
        { "common_attr": "device_id", "path": "ad_user_info.device_id" },
        { "common_attr": "model_item_type", "path": "item_type"},
        { "common_attr": "model_cmd_version", "path": "req_ver"},
        { "common_attr": "ps_common_context_pb", "path": "context"},
      ],
      output_common_attr="universe_win_rank_predict_request",
    )
