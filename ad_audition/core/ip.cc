#include "teams/ad/ad_audition/core/ip.h"

#include <immintrin.h>
#include <stddef.h>

#include <cassert>
#include <cstdint>
#include <string>

#include "base/common/logging.h"
#include "teams/ad/ad_audition/util/spdm/spdm_switches.h"

// 检测是否支持 AVX-512
static inline bool supports_avx512() {
  unsigned int eax, ebx, ecx, edx;

  __asm__ __volatile__("cpuid" : "=a"(eax), "=b"(ebx), "=c"(ecx), "=d"(edx) : "a"(0), "c"(0));
  if (eax < 7)
    return false;

  __asm__ __volatile__("cpuid" : "=a"(eax), "=b"(ebx), "=c"(ecx), "=d"(edx) : "a"(7), "c"(0));
  const unsigned int AVX512F_MASK = (1 << 16);
  if (!(ebx & AVX512F_MASK))
    return false;

  __asm__ __volatile__("xgetbv" : "=a"(eax), "=d"(edx) : "c"(0));
  return (eax & 0xE0) == 0xE0;
}

static inline __m128 masked_read(int d, const float *x) {
  assert(0 <= d && d < 4);
  ALIGNED(16) float buf[4] = {0, 0, 0, 0};
  switch (d) {
    case 3:
      buf[2] = x[2];
    case 2:
      buf[1] = x[1];
    case 1:
      buf[0] = x[0];
  }
  return _mm_load_ps(buf);
  // cannot use AVX2 _mm_mask_set1_epi32
}

float fvec_inner_product(const float* x, const float* y, size_t d) {
  __m256 msum1 = _mm256_setzero_ps();

  while (d >= 8) {
    __m256 mx = _mm256_loadu_ps(x);
    x += 8;
    __m256 my = _mm256_loadu_ps(y);
    y += 8;
    msum1 = _mm256_add_ps(msum1, _mm256_mul_ps(mx, my));
    d -= 8;
  }

  __m128 msum2 = _mm256_extractf128_ps(msum1, 1);
  msum2 = _mm_add_ps(msum2, _mm256_extractf128_ps(msum1, 0));

  if (d >= 4) {
    __m128 mx = _mm_loadu_ps(x);
    x += 4;
    __m128 my = _mm_loadu_ps(y);
    y += 4;
    msum2 = _mm_add_ps(msum2, _mm_mul_ps(mx, my));
    d -= 4;
  }

  if (d > 0) {
    __m128 mx = masked_read(d, x);
    __m128 my = masked_read(d, y);
    msum2 = _mm_add_ps(msum2, _mm_mul_ps(mx, my));
  }

  msum2 = _mm_hadd_ps(msum2, msum2);
  msum2 = _mm_hadd_ps(msum2, msum2);
  return _mm_cvtss_f32(msum2);
}

float optim_fvec_inner_product(const float* x, const float* y, size_t d) {
  // 目前线上机器均支持 avx512，这里为了性能不做检查
  // static const bool enable_avx512_hard = supports_avx512();
  // const bool enable_avx512 = ks::ad_audition::SPDM_enableUseAVX512() && enable_avx512_hard;
  __m512 msum512 = _mm512_setzero_ps();
  while (d >= 16) {
    __m512 mx = _mm512_loadu_ps(x);
    x += 16;
    __m512 my = _mm512_loadu_ps(y);
    y += 16;
    msum512 = _mm512_fmadd_ps(mx, my, msum512);
    d -= 16;
  }
  __m256 msum1 = _mm512_extractf32x8_ps(msum512, 1);
  msum1 = _mm256_add_ps(msum1, _mm512_extractf32x8_ps(msum512, 0));
  while (d >= 8) {
    __m256 mx = _mm256_loadu_ps(x);
    x += 8;
    __m256 my = _mm256_loadu_ps(y);
    y += 8;
    msum1 = _mm256_fmadd_ps(mx, my, msum1);
    d -= 8;
  }

  __m128 msum2 = _mm256_extractf128_ps(msum1, 1);
  msum2 = _mm_add_ps(msum2, _mm256_extractf128_ps(msum1, 0));

  if (d >= 4) {
    __m128 mx = _mm_loadu_ps(x);
    x += 4;
    __m128 my = _mm_loadu_ps(y);
    y += 4;
    msum2 = _mm_fmadd_ps(mx, my, msum2);
    d -= 4;
  }

  if (d > 0) {
    __m128 mx = masked_read(d, x);
    __m128 my = masked_read(d, y);
    msum2 = _mm_add_ps(msum2, _mm_mul_ps(mx, my));
  }

  msum2 = _mm_hadd_ps(msum2, msum2);
  msum2 = _mm_hadd_ps(msum2, msum2);
  return _mm_cvtss_f32(msum2);
}

void fvec_inner_products_ny(float *ip, const float *x, const float *y, size_t d, size_t ny) {
  // BLAS slower for the use cases here
  for (size_t i = 0; i < ny; i++) {
    ip[i] = fvec_inner_product(x, y, d);
    y += d;
  }
}
