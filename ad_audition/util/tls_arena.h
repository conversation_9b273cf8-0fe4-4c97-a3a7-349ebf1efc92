#pragma once

#include <google/protobuf/arena.h>

#define INITIAL_BLOCK_SIZE (512 * 1024)     // 512KB
#define MAX_BLOCK_SIZE (128 * 1024 * 1024)  // 128MB

namespace kuaishou {
namespace ad {
class ThreadLocalArena {
 public:
  static google::protobuf::Arena* Get() {
    thread_local google::protobuf::Arena arena(getArenaOpt());
    return &arena;
  }

  static google::protobuf::ArenaOptions& getArenaOpt() {
    static google::protobuf::ArenaOptions opts;
    static bool initialized = false;
    if (!initialized) {
      opts.initial_block_size = INITIAL_BLOCK_SIZE;
      opts.start_block_size = INITIAL_BLOCK_SIZE;
      opts.max_block_size = MAX_BLOCK_SIZE;
      initialized = true;
    }
    return opts;
  }
  ThreadLocalArena() = delete;
  ~ThreadLocalArena() = delete;
  ThreadLocalArena(const ThreadLocalArena&) = delete;
  ThreadLocalArena& operator=(const ThreadLocalArena&) = delete;
};
}  // namespace ad
}  // namespace kuaishou
