#pragma once

#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace ad_audition {
// SPDM 使用文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIDGfeGPH2sQAxiBwuiaJ4
// 声明 kconf 开关:  DECLARE_SPDM_KCONF_BOOL(key);  // [owner] 开关用途/作用
// 声明 abtest 开关: DECLARE_SPDM_ABTEST_BOOL(key);  // [owner] 开关用途/作用

// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.

// 以下为 kconf 开关声明.
DECLARE_SPDM_KCONF_BOOL(enableUseArena);  // [wangshu<PERSON>ong] 排序部分是否使用 arena
}  // namespace ad_audition
}  // namespace ks
