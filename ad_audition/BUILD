#import os

ad_audition_cppflags = [
    "-mavx2",
    "-mavx512f",
    "-mavx512vl",
    "-mavx512dq",
    "-mfma",
    "-fopenmp",
    "--std=gnu++17"
]

ad_audition_ldflags = [
    "-fopenmp",
]

cc_binary(
    name = "ad_audition_server",
    srcs = [
        "engine/main.cc",
    ],
    cppflags = ad_audition_cppflags,
    ldflags = ad_audition_ldflags,
    deps = [
        ":core",
        ":data",
        ":engine",
        ":serving",
        ":util",
        ":spdm",
        "//base/common/BUILD:base",
        "//infra/kess_grpc-v1100/BUILD:kess-rpc",
        "//ks/serving_util/BUILD:serving_util",
        "//serving_base/server_base/BUILD:server_status",
        "//teams/ad/ad_base/src/jemalloc_hook/BUILD:link_jemalloc",
        "//teams/ad/ad_base/src/kess/BUILD:kess_client",
        "//teams/ad/ad_base/src/ksp/BUILD:ksp",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_audition_service__proto",
        "//third_party/brpc-0.9.6/BUILD:brpc",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_service__proto",
        "//serving_base/thp_component/BUILD:kthp",
        "//third_party/gflags/BUILD:gflags",
    ],
)

cc_library(
    name = "util",
    srcs = [
        "util/errno.cc",
        "util/kess.cc",
        "util/mock/u2c_mgr.cc",
        "util/slowstart_limiter.cc",
    ],
    cppflags = ad_audition_cppflags,
    deps = [
        "//teams/ad/ad_base/src/math/BUILD:random",
        "//third_party/abseil/BUILD:abseil",
    ],
)

cc_library(
    name = "serving",
    srcs = [
        "serving/ad_audition_brpc.cc",
        "serving/ad_audition_grpc.cc",
        "serving/request_handler.cc",
        "serving/web_server.cc",
    ],
    cppflags = ad_audition_cppflags,
    deps = [
        ":engine",
        ":util",
        "//infra/kess_grpc-v1100/BUILD:kess-rpc",
        "//infra/perfutil/BUILD:perfutil",
        "//ks/serving_util/BUILD:serving_util",
        "//teams/ad/ad_base/src/kess/BUILD:kess_client",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_audition_service__proto",
        "//third_party/brpc-0.9.6/BUILD:brpc",
    ],
)

cc_library(
    name = "engine",
    srcs = [
        "engine/framework/context.cc",
        "engine/framework/manager.cc",
        "engine/framework/node/base.cc",
        "engine/framework/node/calc.cc",
        "engine/framework/node/diversity_filter.cc",
        "engine/framework/node/pack.cc",
        "engine/framework/node/unfold.cc",
        "engine/strategy/diversity/photo_ocpx_bid.cc",
        "engine/strategy/diversity/product.cc",
        "engine/strategy/diversity/product_ocpx.cc",
    ],
    cppflags = ad_audition_cppflags,
    deps = [
        ":core",
        ":data",
        ":engine_runtime_config",
        ":strategy_runtime_config",
        ":util",
        "//infra/perfutil/BUILD:perfutil",
        "//teams/ad/ad_base/src/bthread/BUILD:task_group",
        "//teams/ad/ad_base/src/common/BUILD:ad_session_context",
        "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_audition_service__proto",
    ],
)

cc_library(
    name = "core",
    srcs = [
        "core/ip.cc",
        "core/parallel_radix_sort.cc",
        "core/sigmoid_approximate_impl.cc",
    ],
    cppflags = ad_audition_cppflags,
    deps = [
        ":spdm",
    ],
)

cc_library(
    name = "data",
    srcs = [
        "data/bid_mgr/bid_mgr.cc",
        "data/btq_consumer/btq_consumer.cc",
        "data/btq_consumer/manager.cc",
        "data/emb_db_mgr/emb_db.cc",
        "data/emb_db_mgr/emb_db_mgr.cc",
        "util/feasible_set_util.cc",
    ],
    cppflags = ad_audition_cppflags,
    deps = [
        ":cmd_data_config",
        ":util",
        "//base/thread/BUILD:thread",
        "//infra/btq_client/BUILD:dynamic_bt_queue_client",
        "//infra/ccbase/BUILD:ccbase",
        "//serving_base/utility/BUILD:signal",
        "//serving_base/utility/BUILD:system",
        "//teams/ad/ad_index/BUILD:simple_index_manager",
        "//teams/ad/ad_proto/kuaishou/BUILD:brute_retrieval",
        "//teams/ad/engine_base/BUILD:feasible_set",
        "//third_party/abseil/BUILD:abseil",
        "//third_party/boost/BUILD:boost",
        "//third_party/glog/BUILD:glog",
        "//third_party/libcuckoo/BUILD:cuckoo",
    ],
)


cc_library(
    name = 'spdm',
    srcs = ["./util/spdm/*.cc"],
    deps = ["//teams/ad/ad_base/src/spdm_lib/BUILD:spdm_lib"],
)


proto_library(
    name = "engine_runtime_config",
    srcs = [
        "config/runtime/engine.proto",
    ],
)

proto_library(
    name = "strategy_runtime_config",
    srcs = [
        "config/runtime/strategy.proto",
    ],
)

proto_library(
    name = "cmd_data_config",
    srcs = [
        "config/data/data.proto",
    ],
)
