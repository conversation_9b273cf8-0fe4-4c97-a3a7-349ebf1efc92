#include "teams/ad/ad_audition/engine/framework/node/calc.h"

#include <functional>
#include <limits>
#include <memory>
#include <numeric>
#include <utility>
#include <vector>

#include "perfutil/perfutil.h"
#include "teams/ad/ad_audition/config/runtime/kconf.h"
#include "teams/ad/ad_audition/core/ip.h"
#include "teams/ad/ad_audition/core/parallel_radix_sort.h"
#include "teams/ad/ad_audition/core/sigmoid_approximate_impl.h"
#include "teams/ad/ad_audition/data/bid_mgr/bid_mgr.h"
#include "teams/ad/ad_audition/data/emb_db_mgr/emb_db_mgr.h"
#include "teams/ad/ad_audition/util/monitor.h"
#include "teams/ad/ad_base/src/bthread/task_group.h"

namespace kuaishou {
namespace ad {
bool Calc::Process(AuditionContext *ctx) const {
  // Partition of the id field
  // Running bthread_task on every partition
  // Sort the inner product
  // Cut Topk from back to front
  int max_codes = 100000;
  int sort_thread_cnt = 4;
  auto iter = AuditionRuntimeConfig::engineRuntime()->data().conf().find(ctx->req->cmd_queries(0).cmd_name());
  if (iter != AuditionRuntimeConfig::engineRuntime()->data().conf().end()) {
    max_codes =
        iter->second.max_calculated_creatives() > 0 ? iter->second.max_calculated_creatives() : max_codes;
    if (iter->second.enable_sort_thread_cnt_override() == true && iter->second.sort_thread_cnt() > 0) {
      sort_thread_cnt = iter->second.sort_thread_cnt();
    }
  }
  BatchPartitioner bp(ctx->ids.size(), 3000);
  int start = 0;
  int end = 0;
  int already_calculated = 0;
  ks::ad_base::BthreadTaskGroup grp;
  std::vector<CalcClosure> batches;

  auto db =
      EmbeddingDBManager::Instance()->GetDB("default", ctx->req->cmd_queries(0).model_queries(0).version());
  if (db != nullptr) {
    int64_t db_ver = db->model_version;
    int64_t req_ver = ctx->req->cmd_queries(0).model_queries(0).version();
    if (db_ver != req_ver) {
      ks::infra::PerfUtil::IntervalLogStash(req_ver - db_ver, kAuditionServicePerfNs,
                                            "calc_model_version_mismatch_version_diff");
      ks::infra::PerfUtil::IntervalLogStash(1, kAuditionServicePerfNs, "calc_model_version_mismatch");
    } else {
      ks::infra::PerfUtil::IntervalLogStash(1, kAuditionServicePerfNs, "calc_model_version_match");
    }
  }

  int batch_idx = 0;
  while (bp.NextBatch(start, end)) {
    ctx->creative_missed_cnts.push_back(0);
    batches.emplace_back(ctx, start, end, batch_idx, db);
    batch_idx++;
    // do max_code limit
    already_calculated += (end - start) + 1;
    if (already_calculated >= max_codes) {
      // Perf dropped creatives
      break;
    }
  }
  int idx = 0;

  for (auto &&c : batches) {
    auto f = std::bind(c);
    grp.add(idx, std::move(f));
    idx++;
  }
  grp.wait_for_stop();
  parallel_radix_sort::PairSort<float, int64_t> pair_sort;
  // If configured override thread, override it!

  pair_sort.Init(ctx->ids.size(), sort_thread_cnt);
  pair_sort.Sort((ctx->post_calc_score).data(), (ctx->ids).data(), (ctx->ids).size(), sort_thread_cnt);
  // Calculate total_missed_cnt
  int total_missed = std::accumulate(ctx->creative_missed_cnts.begin(), ctx->creative_missed_cnts.end(), 0);
  if (ctx->inner_products.size() != 0) {
    int miss_rate_thousand = (total_missed * 1000) / ctx->inner_products.size();
    ks::infra::PerfUtil::IntervalLogStash(
        miss_rate_thousand, kAuditionServicePerfNs, kAuditionServiceSubtagCalculateCreativeEmbMiss);
  } else {
    // Perf pv miss
  }
  return true;
}

// BatchPartitioner
//
BatchPartitioner::BatchPartitioner(int total, int batchsize)
    : total_(total), batch_size_(batchsize), curr_(0) {
}

bool BatchPartitioner::NextBatch(int &start, int &end) {
  if (curr_ >= total_) {
    start = -1;
    end = -1;
    return false;
  }
  start = curr_;
  end = (curr_ + batch_size_ - 1) > (total_ - 1) ? total_ - 1 : curr_ + batch_size_ - 1;
  curr_ += batch_size_;
  return true;
}

// CalcClosure
//
void CalcClosure::operator()() {
  // auto db = EmbeddingDBManager::Instance()->GetDB("default",
  //                                                 context->req->cmd_queries(0).model_queries(0).version());
  if (db == nullptr) {
    LOG_EVERY_N(ERROR, 5000) << "msg=can't find item embedding db"
                             << "||version=" << context->req->cmd_queries(0).model_queries(0).version();
    return;
  }
  auto hdl = db->handle;  // shared ptr - embdb
  int miss_cnt = 0;

  auto bid = AuditionBidMgr::Instance()->Get();
  auto bid_hdl = bid->handle;  // shared ptr - bid

  for (int64_t idx = s; idx <= e; idx++) {
    const auto iter = hdl->find(context->ids[idx]);
    if (iter == hdl->end()) {
      miss_cnt++;
      LOG_EVERY_N(WARNING, 100000000) << "msg=creative emb miss"
                            << "||creative_id=" << context->ids[idx]
                            << "||query version=" << context->req->cmd_queries(0).model_queries(0).version()
                            << "||db model_version=" << db->model_version
                            << "||db material_version=" << db->material_version;

      continue;
    }
    // check
    context->inner_products[idx] =
        optim_fvec_inner_product(context->query.data(), iter->second.data(),
                                 context->req->cmd_queries(0).model_queries(0).dimension());
    // Calc sigmoid
    if (context->stg_conf_valid && context->stg_conf.calc_enable_post_process() == true) {
      context->post_calc_score[idx] = sigmoid_approximate_segment(context->inner_products[idx]);
      // Bid
      const auto b_iter = bid_hdl->find(context->ids[idx]);
      context->post_calc_score[idx] =
          b_iter == bid_hdl->end() ? -std::numeric_limits<float>::infinity()
                                   : context->post_calc_score[idx] * static_cast<float>(b_iter->second);
    } else {
      context->post_calc_score[idx] = context->inner_products[idx];
    }
  }
  context->creative_missed_cnts[i] = miss_cnt;
}
CalcClosure::CalcClosure(AuditionContext* ctx, int start, int end, int index,
                         std::shared_ptr<struct kuaishou::ad::Version> shared_db)
    : context(ctx), s(start), e(end), i(index), db(shared_db) {}

}  // namespace ad
}  // namespace kuaishou
