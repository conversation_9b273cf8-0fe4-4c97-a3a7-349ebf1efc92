#pragma once

#include <memory>
#include <string>

#include "base/common/logging.h"
#include "teams/ad/ad_audition/data/emb_db_mgr/emb_db_mgr.h"
#include "teams/ad/ad_audition/engine/framework/context.h"
#include "teams/ad/ad_audition/engine/framework/node/base.h"

namespace kuaishou {
namespace ad {

struct CalcClosure {
  CalcClosure(AuditionContext* ctx, int start, int end, int index,
              std::shared_ptr<struct kuaishou::ad::Version> shared_db);
  AuditionContext *context;
  void operator()();
  int s;  // Start of the batch
  int e;  // End of the batch
  int i;  // Batch index
  // Dependency
  std::shared_ptr<struct kuaishou::ad::Version> db;
};

class BatchPartitioner {
 public:
  BatchPartitioner(int total, int batchsize);
  bool NextBatch(int &start, int &end);  // NOLINT

 private:
  const int batch_size_;
  int total_;
  int curr_;
};

class Calc : public NodeBase {
 public:
  bool Process(AuditionContext *ctx) const override;
  std::string Name() {
    return std::string("Calc");
  }
};
}  // namespace ad
}  // namespace kuaishou
