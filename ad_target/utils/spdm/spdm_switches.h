#pragma once

#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace ad_target {

// SPDM 使用文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIDGfeGPH2sQAxiBwuiaJ4

// 声明 kconf 开关:  DECLARE_SPDM_KCONF_BOOL(key);  // [owner] 开关用途/作用
// 声明 abtest 开关: DECLARE_SPDM_ABTEST_BOOL(key);  // [owner] 开关用途/作用

// 所有 spdm 开关在 spdm_switches.h 中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在 spdm_switches.h 中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在 spdm_switches.h 中声明, 在 spdm_switches.cc 中定义.

// 以下为 kconf 开关声明.
DECLARE_SPDM_KCONF_BOOL(enableTargetBiznameControl);  // [weiyilong] target 识别压测标记
DECLARE_SPDM_KCONF_BOOL(enableDuanjuIaaControl);  // [zhoushuaiyin] 短剧激励屏蔽开关
DECLARE_SPDM_KCONF_BOOL(enableSearchMingtouRuleFilter);  // [weiyilong] 搜索直投规则过滤
DECLARE_SPDM_KCONF_BOOL(enableLiveTargetFollowPage);  // [cuiyanliang] 关注页 live target 开关
DECLARE_SPDM_KCONF_BOOL(enableLazyNegativeInit);  // [yanfeng] 负反馈 lazy 初始化 abtest 开关
DECLARE_SPDM_KCONF_BOOL(enableEspMobileAccountFilter);  // [cuiyanliang] 金牛移动版 account 层级审核字段过滤
DECLARE_SPDM_KCONF_BOOL(enableAdHistoryFilterIdUnify);  // [wangjiabin05] 刷次优化开关
DECLARE_SPDM_KCONF_BOOL(picassoDegrade);  // [王昱哲] Picasso 功能降级
DECLARE_SPDM_KCONF_BOOL(enableUniverseInterveneConfig);  // [yinliang] 是否开启联盟搜索人工干预配置
DECLARE_SPDM_KCONF_BOOL(enablePlayableInterstitialBlackSet);  // [尹亮] 联盟插屏广告媒体黑名单开关
DECLARE_SPDM_KCONF_BOOL(enableNewCustomUseDMP);  // [zhangruyuan] 新客定向标签使用 DMP 数据
DECLARE_SPDM_KCONF_BOOL(enableRiskRuleFilter);
DECLARE_SPDM_KCONF_BOOL(enableTransWechatRtaInfo);
DECLARE_SPDM_KCONF_BOOL(enableTransWechatFeatureId);
DECLARE_SPDM_KCONF_BOOL(enableEspBudgetPartition);  // [liming11] 磁力金牛是否切换成新的预算隔离 tag
DECLARE_SPDM_KCONF_BOOL(enableItemKeyToHash);  // [yangyifan10] item_key kconf 切换开关
DECLARE_SPDM_KCONF_BOOL(enableGrossProfitExploreAccountFilter);
DECLARE_SPDM_KCONF_BOOL(enableNewSocialFilter);  // [liming11] 是否允许流量助推 2.0 广告投放
DECLARE_SPDM_KCONF_BOOL(enableXdtRoasPrerank);  // [yueshifeng] 联盟小店通  roas  粗排
DECLARE_SPDM_KCONF_BOOL(enableVirtualGoldFilter);  // [liming11] 虚拟金过滤
DECLARE_SPDM_KCONF_BOOL(universeNewInternalAgentFilter);  // [wanglei10] 联盟使用新逻辑过滤内部代理商开关
DECLARE_SPDM_KCONF_BOOL(enablePrerankDspLive);  // [lizhuo] 行业直播粗排适配
DECLARE_SPDM_KCONF_BOOL(enableTarget2RankSecondIndustryV5);
DECLARE_SPDM_KCONF_BOOL(enableTargetPrerankTransLocal);
DECLARE_SPDM_ABTEST_BOOL(enable_pop_recruit_page_filter);  // [maileyi] B 招页面过滤
DECLARE_SPDM_ABTEST_BOOL(enable_skip_game_banner_dark_budget_control_filter);  // [zhoushuaiyin] 插屏暗投
DECLARE_SPDM_ABTEST_BOOL(enable_pop_open_some_page);  // [yangfukang03] B 招放开部分页面实验
DECLARE_SPDM_ABTEST_BOOL(enable_recruit_similar_position);  // [yangfukang03] 投了又投广告位
DECLARE_SPDM_ABTEST_BOOL(enable_skip_sheild_budget_by_whiteset);  //  [liangyukang] 放开 IAA 白名单账户屏蔽
DECLARE_SPDM_ABTEST_BOOL(enable_skip_sheild_budget_by_version_whitejson);
DECLARE_SPDM_ABTEST_STRING(iaa_acq_gen_version_string);
DECLARE_SPDM_ABTEST_STRING(lcron_one_model_exp_group);
DECLARE_SPDM_ABTEST_BOOL(enable_lcron_tag_factors_init);
DECLARE_SPDM_ABTEST_BOOL(enable_lcron_onemodel_exclude_minigame);
DECLARE_SPDM_ABTEST_BOOL(enable_lcron_one_model_recall);
DECLARE_SPDM_ABTEST_INT64(lcron_one_model_recall_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_skip_expand_by_post_ecpm_unit);
DECLARE_SPDM_ABTEST_BOOL(enable_skip_expand_by_embedding);
DECLARE_SPDM_ABTEST_BOOL(enable_onemodel_recall_expand_by_layer);
DECLARE_SPDM_ABTEST_BOOL(enable_choose_path_id_base_on_exp_group);
DECLARE_SPDM_ABTEST_STRING(one_model_recall_exp_group);
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_effect_layer_quota_dynamically);
DECLARE_SPDM_ABTEST_BOOL(enable_onemodel_dot_gtz);
DECLARE_SPDM_ABTEST_DOUBLE(effect_layer_quota_precentage);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_cancel_user_drop);  // [yuchengyuan] cid 弹窗取消人群过滤
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_black_page_filter);  // [mayanbin] 粉条页面过滤
DECLARE_SPDM_ABTEST_BOOL(enable_pop_live_inspire);  // [maileyi] B 招页面过滤
DECLARE_SPDM_ABTEST_BOOL(enable_self_service);  // [maileyi] 自助平台 接入外循环
DECLARE_SPDM_ABTEST_BOOL(enableCreativeFind);   // [yangyifan10] 优化 forward search creative 查找
DECLARE_SPDM_ABTEST_BOOL(enable_drop_onestep_table);     // [zhangruyuan] 下线一段式表
DECLARE_SPDM_KCONF_BOOL(universeTinyPrerankOpt);   // [wanglei10] 联盟 tiny 部署 粗排优化
DECLARE_SPDM_KCONF_BOOL(enableUniverseDarkExplosionPrevent);    // [wanglei10] 联盟暗投防爆量过滤开关
DECLARE_SPDM_KCONF_BOOL(enableBudgetStatusDataSwitch);    // [zhouting03] budget status 切换开关
DECLARE_SPDM_KCONF_BOOL(enableInnerCIDSepIAAControlFilter)     // [lizuxin] 内循环 cid 激励控比二期开关
DECLARE_SPDM_KCONF_BOOL(enableInnerCIDSepIAAControlAddPlaylet)     // [lizuxin] 内循环 cid 激励控比增加短剧
DECLARE_SPDM_KCONF_BOOL(enableTarget2BidServiceAutoDarkControl);    // [zhouting03] 自动控比标记透传
DECLARE_SPDM_KCONF_BOOL(enableOuterLoopCandiatePerf);  // [heqian] 外循环采样流打点监控
DECLARE_SPDM_KCONF_BOOL(enableTransPathIds);
DECLARE_SPDM_KCONF_BOOL(enableHarmonyWhiteListFilter);  // [heqian] 鸿蒙不可投过滤
DECLARE_SPDM_KCONF_BOOL(enableHarmonyRewardedOuterLive);  // [heqian] 鸿蒙不可投过滤
DECLARE_SPDM_KCONF_BOOL(enableHarmonyOuterLive);  // [heqian] 鸿蒙不可投过滤
DECLARE_SPDM_KCONF_BOOL(enableHarmonyOuterP2l);  // [heqian] 鸿蒙不可投过滤
DECLARE_SPDM_KCONF_BOOL(enableCouponConfigId);  // [heqian] 字段透传
DECLARE_SPDM_KCONF_BOOL(enableCouponPackageId);  // [yanqi08] 字段透传
DECLARE_SPDM_KCONF_BOOL(enableResidenceAdcodeTarget);   // [limiaochen] 居住地域定向
DECLARE_SPDM_KCONF_BOOL(enablePrerankClientRandomSelect);  // [jiangyuzhen03] prerank 随机路由
DECLARE_SPDM_KCONF_BOOL(enableTargetCacheRecord);
// [liuzichen05] target 不再做 reco_user_info 序列化开关
DECLARE_SPDM_KCONF_BOOL(disableSerializeRecoUserInfo);

DECLARE_SPDM_KCONF_BOOL(enableClientAiLocalRerankDebug);   // [wangwenguang] 端智能本地召回打点
DECLARE_SPDM_KCONF_INT32(clientAiLocalRerankDebugEvery);   // [wangwenguang] 端智能本地召回打点间隔
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_perf_base_request);   // [wangwenguang] 端智能打点区分二次请求

DECLARE_SPDM_ABTEST_BOOL(enable_dj_iaa_incentive_dark_budget_control_filter);   // [liyichen05] 短剧暗投控比为 0 过滤   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_follow_page_shield_status_filter);    // [yangyifan10] 内循环关注页隐藏视频屏蔽   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_dj_iaa_skip_incentive_thres);  // [liyichen05] 短剧 iaa 跳过金牛限制开关
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_similar_photo_filter);  // [liyichen05] xifan 相似 photoid 过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_black_filter);  // [liyichen05] 喜番 流量准入开关
DECLARE_SPDM_ABTEST_BOOL(enable_dj_skip_campaign);  // [liyichen05] 短剧 iaa 跳过 campaign = app 限制
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_skip_outer_soft_admit_filter);  // [liyichen05] 喜番 ad  跳过 OuterSoftAdmitFilter 软广限制  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_filter_only_order_mode)   // [tangsiyuan] 粉条相关流控过滤只生效订单制
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_add_phone_call_target)   // [tangsiyuan] 粉条私信线索客户扶持
DECLARE_SPDM_ABTEST_BOOL(enable_biz_fanstop_target)   // [tangsiyuan] b 粉链路补充召回
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_phone_call_hetu_orientation_target);  // [lijun03] 粉条河图人群召回
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_phone_call_population_filter);  // [lijun03] 粉条河图人群定向

DECLARE_SPDM_KCONF_BOOL(enableTargetExpandOpt);       // [houjian] 定向优化开关
DECLARE_SPDM_KCONF_BOOL(enableTargetDistanceRuleFilter);
DECLARE_SPDM_ABTEST_BOOL(enable_creative_dup_in_merge);  // [heqian] 内循环 meger 重复检索优化
DECLARE_SPDM_ABTEST_BOOL(enable_rta_account_set_opt);  // [zhangruyuan] rta account

DECLARE_SPDM_KCONF_BOOL(disableInvertDepend);  // [heqian] 内循环 trigger 去除定向依赖
DECLARE_SPDM_ABTEST_BOOL(disable_invert_depend);  // [heqian] 内循环 trigger 去除定向依赖
DECLARE_SPDM_ABTEST_BOOL(enable_skip_ps_filter_set);  // [heqian] 负向 tag 跳过
DECLARE_SPDM_ABTEST_BOOL(enable_rta_simplified_for_inner);  // [heqian] enable_rta_simplified_for_inner
DECLARE_SPDM_KCONF_BOOL(enableRtaSimplifiedForInner);  // [heqian] enable_rta_simplified_for_inner
DECLARE_SPDM_ABTEST_DOUBLE(trigger_searcher_degrade_ratio);  // [heqian] ps trigger searcher quota 降级
DECLARE_SPDM_ABTEST_BOOL(disable_client_dup_live_photo_id_filter);  // [heqian] 下线策略
DECLARE_SPDM_ABTEST_BOOL(enable_recall_degrade_by_hour);  // [heqian] 召回 & 粗排 降级联动开关
DECLARE_SPDM_ABTEST_BOOL(disable_recall_degrade);  // [heqian] 召回 & 粗排 降级联动开关
DECLARE_SPDM_ABTEST_INT64(extra_merger_quota_OUTER_PHOTO);  // [heqian] 外循环 merger 额外 quota
DECLARE_SPDM_ABTEST_INT64(extra_merger_quota_OUTER_SOFT_PHOTO);  // [heqian] 外循环 merger 额外 quota
DECLARE_SPDM_ABTEST_BOOL(enable_unified_follow_live_prerank_truncate);  // [heqian] live 进粗排统一截断
DECLARE_SPDM_ABTEST_INT64(max_inner_soft_photo_rank_num);  // [heqian] 内循环软广 进粗排数量
DECLARE_SPDM_ABTEST_INT64(max_outer_soft_photo_rank_num);  // [heqian] 外循环软广 进粗排数量
DECLARE_SPDM_ABTEST_BOOL(enable_pass_addr_match_filter);  // [heqian] 不发货地址过滤下线摸底实验
DECLARE_SPDM_ABTEST_DOUBLE(extra_inner_soft_rank_degrade_ratio);  // [heqian] 内循环软广降级系数
DECLARE_SPDM_KCONF_BOOL(enableDragonPrerankSample);  // [heqian]

DECLARE_SPDM_ABTEST_BOOL(enable_prerank_trans_local_exp);   // [houjian] follow_tag
DECLARE_SPDM_ABTEST_BOOL(disable_follow_tag_enricher);   // [houjian] follow_tag
DECLARE_SPDM_ABTEST_BOOL(enable_delete_no_use_attr_from_prerank);

DECLARE_SPDM_ABTEST_BOOL(enable_unify_dark_budget_ratio);   // [houjian] enable_unify_dark_budget_ratio
DECLARE_SPDM_ABTEST_BOOL(inner_soft_to_inner_loop_photo);   // [yanfeng] 图统一开关
DECLARE_SPDM_ABTEST_BOOL(enable_small_game_nodark_budget_control_filter);   // [jiangpeng07] 快小游暗投控比为 0 过滤   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_small_game_soft_nodark_budget_control_filter);   // [jiangpeng07] 快小游软广暗投控比为 0 过滤   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_draw_flow_inner_hard_live_biz_allow);   // [zhangxingyu03] draw 流内循环硬广直播 biz allow   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_filter_b_fans_top);  // [heqian] 激励流量过滤 B 粉
DECLARE_SPDM_ABTEST_BOOL(enable_outer_soft_dcaf);  // [heqian] 外循环软广 quota 调整
DECLARE_SPDM_ABTEST_INT64(outer_soft_max_ad_count_v3);  // [heqian] 外循环软广 recall quota
DECLARE_SPDM_ABTEST_INT64(outer_soft_rank_num_v3);  // [heqian] 外循环软广 prerank quota
DECLARE_SPDM_ABTEST_INT64(outer_photo_default_rank_num_v2);  // [heqian] 外循环硬广 prerank quota
DECLARE_SPDM_ABTEST_INT64(outer_hard_biz_quota);  // [heqian] 外循环硬广 recall quota
DECLARE_SPDM_ABTEST_INT64(creative_dup_set_size_in_merge);  // [heqian] merge 去重 set reserver size
DECLARE_SPDM_ABTEST_BOOL(enable_game_shoufa_filter);  // [heqian] 莉莉丝首发过滤
DECLARE_SPDM_ABTEST_BOOL(enable_left_budget_filter);  // [heqian] left budget 兜底过滤
DECLARE_SPDM_ABTEST_BOOL(disable_left_budget_set_default);  // [lichen30] left budget 默认值兜底
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_whole_left_budget_filter);  // [gaozepeng] 激励 left budget 过滤
DECLARE_SPDM_ABTEST_INT64(incentive_left_budget_thres);  // [gaozepeng] 激励 left budget 兜底过滤阈值
DECLARE_SPDM_ABTEST_INT64(incentive_whole_left_budget_thres);  // [gaozepeng] 激励 left budget 兜底过滤阈值
DECLARE_SPDM_ABTEST_BOOL(enable_skip_iaa_dj_incentive_dark_budget_control_filter);

// [zhangxingyu03]
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_separate_category_purchased_interval);
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_harmony_filter);
DECLARE_SPDM_ABTEST_INT64(incentive_category_purchased_interval);

// [liyichen05] iaa 短剧激励跳过暗投
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iaa_incentive_control_refactor);  // [liyongchang] 短剧 iaa 激励控比重构  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iaa_incentive_filter_flip);  // [liyongchang] 短剧 iaa 激励 0 控比反转  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iaa_blacklist_default_incentive_control);  // [liyongchang] 短剧 iaa 黑名单恢复默认激励控比  // NOLINT
DECLARE_SPDM_ABTEST_INT64(playlet_iaa_open_account_24h_cost_thres);  // [yushengkai] 短剧 IAA 放开控比账户消耗阈值  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iaa_open_not_clue);  // [yushengkai] 短剧 IAA 放开非线索开关
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iaa_open_inner);  // [yushengkai] 短剧 IAA 放开内循环开关
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iaa_holdout_fix);  // [yushengkai] 短剧 IAA 屏蔽修复

DECLARE_SPDM_ABTEST_BOOL(enable_wechat_rta_filter);     // [zhangruyuan] 腾讯 RTA 开关
// [limiaochen] 外循环 RuleEngine 不设置内循环 callback
DECLARE_SPDM_KCONF_BOOL(enableOuterRuleEngineCloseInnerCallback);

DECLARE_SPDM_KCONF_BOOL(enableShortVideoOrderAccelerateDiscount);  // [fengyajuan] 联盟粗排 ecpm 加速探索打折
DECLARE_SPDM_KCONF_BOOL(enableShortVideoOrderAccelerateCount);
DECLARE_SPDM_KCONF_BOOL(disableUniverseDirectEcom);     // [wanglei10] 联盟屏蔽直营电商广告
DECLARE_SPDM_KCONF_BOOL(universeFillMerchantItemId);  //  [wanglei10] 联盟下发商品 id
DECLARE_SPDM_ABTEST_INT64(search_retarget_frequency_control);  // [luochao, chenxian] 搜索重定向专项
DECLARE_SPDM_KCONF_BOOL(useThanosKessGroup);  // [chengyuxuan] default target 访问 ps ann router 带 thanos 后缀  // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableTargetGetUeUserProfile);  //  [wangdaopeng] target 读取用户体验 redis
DECLARE_SPDM_KCONF_BOOL(universeFillLiveStartTimeTarget);  //  [wanglei10] 联盟下发直播开播时间
DECLARE_SPDM_KCONF_BOOL(disableMcbCpaBidAdjust);  //  [songxu] 设置量价实验 MCB 开关
DECLARE_SPDM_KCONF_BOOL(universeInnerSupportOriginLive);  // [wanglei10] 联盟内循环支持原生直播
DECLARE_SPDM_KCONF_BOOL(promotionConsultationFilter);  //  [liming11] 是否可以进行私信咨询链路召回
DECLARE_SPDM_ABTEST_BOOL(enable_change_inspire_live_recall_conf);  // [chenxian] 激励配置且单列
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_new_scene);  // [zengjiangwei03] 商品卡新流量生效单独 sceneid
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_speed_test);  // [zengjiangwei03] 商品卡测品
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_trace_log);  // [heqian] 粗排独立 trace
DECLARE_SPDM_ABTEST_BOOL(disable_unit_over_budget_check);  // [heqian] 关闭兜底超投过滤
DECLARE_SPDM_ABTEST_BOOL(enable_unify_prerank_score);

// [zhangyuyang05] 激励广告 低价值低活人群过滤
DECLARE_SPDM_ABTEST_DOUBLE(incntv_ad_ocpx_filter_v2_cpm_thres);

//  [yuancuili] 三率优化过滤比参数配置
DECLARE_SPDM_ABTEST_STRING(sanlv_cate3_thr_ratio_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_sanlv_cate3_thr_ratio_author_refactor_exp);

// [fukunyang]
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_refund_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_product_refund_filter);
DECLARE_SPDM_ABTEST_DOUBLE(inner_live_refund_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(inner_product_refund_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(inner_live_refund_ratio_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(inner_live_refund_ratio_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(inner_product_refund_ratio_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(inner_product_refund_ratio_lower_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_hema_skip_iaa_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_random_filter);  // [yushengkai] IAA 随机屏蔽
DECLARE_SPDM_ABTEST_STRING(playlet_random_tag);       // [yushengkai] IAA 随机屏蔽实验 tag
DECLARE_SPDM_ABTEST_DOUBLE(inner_prerank_drop_down_ratio);  // [liqinglong] 粗排降级参数
DECLARE_SPDM_ABTEST_BOOL(disable_recall_ensemble_sort);  // [liqinglong] 外循环召回 ensemble_sort
DECLARE_SPDM_ABTEST_BOOL(enable_skip_weak_type);  // [liqinglong] 软广弱负向捞回
DECLARE_SPDM_ABTEST_BOOL(enable_nearline_fix_local_recall_v3);  // [lihantong] 策略打标修复
DECLARE_SPDM_ABTEST_BOOL(enable_target_mobile_fill_soft_ad);  // [lihantong] 策略打标修复
DECLARE_SPDM_ABTEST_STRING(nearline_local_quota_exp_tag);       // [lihantong] 策略打标修复
DECLARE_SPDM_ABTEST_INT64(nearline_local_recall_per_quota);  // [lihantong] 策略打标修复
DECLARE_SPDM_ABTEST_BOOL(enable_skip_live_punish);  // [liqinglong] 行业直播捞回
DECLARE_SPDM_ABTEST_BOOL(enable_skip_p2l_live_punish);  // [liqinglong] 行业直播捞回
DECLARE_SPDM_ABTEST_BOOL(enable_non_amdlive_filter);  // [liqinglong] 行业直播定向过滤
DECLARE_SPDM_ABTEST_BOOL(disable_old_prioritys);  // [liqinglong] 行业直播定向过滤
DECLARE_SPDM_ABTEST_BOOL(enable_forward_layer_quota);  // [liqinglong] 效果和行业分层实验
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_forward_layer_quota);  // [liqinglong] 效果和行业分层实验
DECLARE_SPDM_ABTEST_BOOL(enable_soft_and_hard_tag_exp_feed);      // 流化分实验复制软硬广
DECLARE_SPDM_ABTEST_BOOL(enable_soft_and_hard_tag_exp_rewarded);  // 激励分实验复制软硬广
DECLARE_SPDM_ABTEST_BOOL(enable_fix_soft_hard_copy);              // 上面两个开关的逻辑修复
DECLARE_SPDM_ABTEST_BOOL(enable_nc_path_reweight);   // 外循环召回 nc 场景 factor 重赋值
DECLARE_SPDM_ABTEST_BOOL(enable_soft_zigzag_order);  // [liuzhiqiang08] 软广复制按 zigzag 顺序
DECLARE_SPDM_ABTEST_BOOL(enable_soft_into_hard);  // [liuzhiqiang08] 软广通路可进硬广队列
DECLARE_SPDM_ABTEST_BOOL(enable_rank_adlist_cache);  // [liqinglong] 切换缓存到精排
DECLARE_SPDM_ABTEST_BOOL(enable_degrade_prerank_soft_outer);  // [liqinglong] 降级外循环软广粗排
DECLARE_SPDM_ABTEST_DOUBLE(outer_soft_prerank_ratio);  // [liqinglong] 粗排降级参数
DECLARE_SPDM_ABTEST_BOOL(enable_trans_wechat_rta_info);  // [zhangruyuan] 腾讯 RTA
DECLARE_SPDM_KCONF_BOOL(enableLoadInnerLoopIndex);  // [liubing05] 开屏加载内循环索引
DECLARE_SPDM_ABTEST_BOOL(enable_ind_photo_expore_tag);  // [yuchengyuan] 行业出坡素材打标
DECLARE_SPDM_ABTEST_BOOL(enable_industry_explore_flow);  // [yuchengyuan] 行业人群标记打标
DECLARE_SPDM_KCONF_BOOL(enableRtaUndertakeFilter);  // [wanglei10] rta unit 兜底过滤
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_prerank);  // [chencongzheng] 猜喜粗排开关
DECLARE_SPDM_KCONF_BOOL(enableSearchQueryNormP2p);  // [qianyangchao] 搜索词归一化
DECLARE_SPDM_ABTEST_BOOL(enable_one_step_inroom_merge);  // [chenxian] 一二段式合并实验摸底
DECLARE_SPDM_ABTEST_BOOL(enable_one_step_ecpm_merge);  // [chenxian] 一二段式合并实验摸底
DECLARE_SPDM_ABTEST_BOOL(enable_reward_one_step_merge);  // [chenxian] 激励一二段式合并实验摸底
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_cny_mutex);  // [zhangruyuan] CNY 互斥
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_prerank_add_candidate_feature_v1);  // [cxa] 直播粗排候选落盘开关
DECLARE_SPDM_ABTEST_INT64(inner_live_prerank_candidate_feature_sample_count_v1);  //  直播粗排候选落盘数
// [zhangruyuan]

// 以下为 kconf int32 参数声明
DECLARE_SPDM_KCONF_INT32(riskControlMinUserLevel);  //  [heqian] 风控等级过滤
DECLARE_SPDM_KCONF_INT32(onlyPecSceneId);  // [zhangruyuan] 只出互动场景的场景 ID
DECLARE_SPDM_KCONF_INT32(traceTsmRatio);  //  [zhangruyuan] trace Tsm 放量开关, 防止下发数据过多
DECLARE_SPDM_KCONF_INT32(traceTsmRatioForAd);  //  [zhangruyuan] trace Tsm 放量开关, 防止下发数据过多

// 以下为 kconf int64 参数声明
DECLARE_SPDM_KCONF_INT64(immTriggerTokenMaxQuota);  //  [limingxi] 召回专用 quota
DECLARE_SPDM_KCONF_INT64(immPerTokenExpandMaxQuota);  //  [limingxi] 召回专用 quota
DECLARE_SPDM_KCONF_INT64(immTotalCreativeMaxQuota);  //  [limingxi] 召回专用 quota
DECLARE_SPDM_KCONF_INT64(spuPurchasedFreqControlInterval);  // [wanghongfei] spu 商品复购频控频率
DECLARE_SPDM_KCONF_BOOL(enableAppAdvanceSkipAppFilter);  // [liuwenlong03] 拉活跳过 以安装应用过滤


// 以下为 kconf double 参数声明
DECLARE_SPDM_KCONF_DOUBLE(archimedes_high_cost_ratio_threshold);  // [mateng05] 内粉订单高消耗率阈值
DECLARE_SPDM_KCONF_DOUBLE(splashRealtimeRecallRatio);  // [huangwei06] 开屏召回 ratio

// 以下为 abtest 开关声明.
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_industry_live_diversity);
DECLARE_SPDM_ABTEST_BOOL(enable_pec_switch_to_rule_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_live_target_follow_page_align_rule_merger);  // [yanliang]
DECLARE_SPDM_ABTEST_BOOL(enable_soft_diversity_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_new_holdout_v3);  // [liyongchang]
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iaa_holdout);  // [liyongchang]
DECLARE_SPDM_ABTEST_BOOL(enable_live_target_follow_page_align_rule_filter);  // [yanliang]
DECLARE_SPDM_ABTEST_BOOL(enable_filter_inner_agent_account);  // [liubing05] 过滤内部代理广告开关
DECLARE_SPDM_ABTEST_BOOL(enable_high_quality_photo_awake);  // [liming11] 优质素材唤醒
DECLARE_SPDM_ABTEST_BOOL(enable_diversity_roi_ratio_1000);  // [yushengkai] 多样性 roi_ratio 1000
DECLARE_SPDM_ABTEST_BOOL(enable_ext_merchant_self_ecpm_sort);  // [zhangyiwei03] 商家单独 ecpm 开关
DECLARE_SPDM_ABTEST_BOOL(enable_ext_merchant_new_auto_papo);  // [zhangyiwei03] 商家坡爬新创意灵活调整开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_product_impr_freq);  // [zhangyunfei03] 联盟产品粒度频控 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_product_skip_diversity);  // [yangjinhui] 联盟账户跳过多样性过滤
DECLARE_SPDM_ABTEST_BOOL(enable_audience_diverse_prerank);  // [xiemiao] 直播进人明投爬坡多样性开关
DECLARE_SPDM_ABTEST_BOOL(enable_audience_ecpm_prerank_sort);  // [xiemiao] 直播进人明投爬坡排序开关
DECLARE_SPDM_ABTEST_BOOL(enable_author_ecpm_prerank_sort);  // [xiemiao] 直播爬坡排序开关
DECLARE_SPDM_ABTEST_BOOL(disable_prerank_quota_degrade);  // [zhangruyuan] 粗排 quota 降级留反
DECLARE_SPDM_ABTEST_BOOL(disable_diverse_prerank);  // [xiemiao] 直播爬坡多样性开关
DECLARE_SPDM_ABTEST_BOOL(disable_follow_flow_random);  // [limiaochen] 直播关闭随机层
DECLARE_SPDM_ABTEST_BOOL(enable_spu_account_diverse_prerank);  // [xiemiao] 短视频 spu 爬坡多样性开关
DECLARE_SPDM_ABTEST_BOOL(enable_spu_ecpm_prerank_sort);  // [xiemiao] 短视频 spu 爬坡排序开关

DECLARE_SPDM_ABTEST_BOOL(disable_new_custom_use_dmp);  // [zhangruyuan] 留反实验新客定向

DECLARE_SPDM_ABTEST_BOOL(enable_fix_dmp_cpa_bid);  // [zhaoyi13] 差异化出价回传修复

DECLARE_SPDM_ABTEST_BOOL(enable_universe_smart_compute_dynamic_quota);  //  [liangli] 联盟智能算力动态档位开关
DECLARE_SPDM_ABTEST_BOOL(enable_dif_windows);  // [gongxiaopeng03] 联盟 zigzag
DECLARE_SPDM_ABTEST_BOOL(disable_whole_roi);  //  [tangxiaochao05] 不开启独立召回
DECLARE_SPDM_ABTEST_BOOL(disable_key_word_population);  //  [qipeng] 不开启关键词召回
DECLARE_SPDM_ABTEST_BOOL(enable_preselect_rank_for_rulefilter_diversity);  // [liuzhiqiang08]
DECLARE_SPDM_ABTEST_BOOL(enable_effect_path_set_new);  // [liuzhiqiang08] 新版效果层通路集合，支持实验
DECLARE_SPDM_ABTEST_BOOL(enable_outer_selected_path);  // [liuzhiqiang08] 只生效筛选的通路，其他通路失效
DECLARE_SPDM_ABTEST_STRING(outer_selected_path_feed);       // [liuzhiqiang08] 筛选的通路列表-feed
DECLARE_SPDM_ABTEST_STRING(outer_selected_path_rewarded);   // [liuzhiqiang08] 筛选的通路列表-rewarded
DECLARE_SPDM_ABTEST_BOOL(enable_target_industry_explore_append);   // [guanpingyin] 行业人群探索爬坡
DECLARE_SPDM_ABTEST_BOOL(enable_similar_photo_freq_delivery);
DECLARE_SPDM_ABTEST_BOOL(enable_amd_photo_p2l_prerank_input_quota_refactor);  // [linwei] amd photo p2l input quota 融合实验 // NOLINT
DECLARE_SPDM_ABTEST_INT64(inner_hard_photo_p2l_ranking_quota);  // [linwei] amd photo p2l input quota 融合实验 // NOLINT
DECLARE_SPDM_ABTEST_INT64(inner_hard_reward_photo_p2l_ranking_quota);  // [linwei] amd photo p2l input quota 融合实验 // NOLINT
DECLARE_SPDM_ABTEST_INT64(cache_inner_hard_photo_p2l_ranking_quota);  // [linwei] amd photo p2l input quota 融合实验 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_thanos_switch_risk);  // [zhangruyuan] 信息流风控切换
DECLARE_SPDM_ABTEST_INT64(inner_hard_photo_ranking_quota);  // [heqian] amdranking 阶段 photo 主路 quota
DECLARE_SPDM_ABTEST_INT64(inner_hard_p2l_ranking_quota);  // [heqian] amdranking 阶段 p2l 主路 quota
DECLARE_SPDM_ABTEST_INT64(inner_hard_photo_ecology_append_ranking_quota);  // [heqian] amdranking 阶段生态层爬坡 quota // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_close_wide_material_filter);  // [zhangyunhao03] 关闭素材类型过滤逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ali_browsed_info_freq);  // [tanghaihong] 联盟对阿里产品基于 browse set 中下发时间的频控 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_prerank_user_tag);   // [shenchong03] 粗排用户分层开关
DECLARE_SPDM_ABTEST_BOOL(enable_ad_recall_new_material_filter);  // [ruanjing] 召回新多样性过滤策略
DECLARE_SPDM_ABTEST_INT64(seven_pay_max_skip_num_per_account);  // [ruanjing] 召回高价值人群七日付费次数跳过多样性策略  // NOLINT
DECLARE_SPDM_ABTEST_INT64(wechat_game_max_skip_num_per_account);  // [ruanjing] 召回高价值人群小游戏跳过多样性策略  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_installed_filter_target_fix_v1);  // [zengzhengda] 联盟已安装过滤定向修复  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_filter_creative_type_not_match);  // [tanghaihong] 联盟下线 CREATIVE_TYPE_NOT_MATCH 过滤 // NOLINT
// [liuchangjin] 粗排完全排序计费分离开关
DECLARE_SPDM_ABTEST_BOOL(enable_preranking_thorough_billing_separation);
DECLARE_SPDM_ABTEST_BOOL(enable_middle_page_industry_filter);  // [liubing05] 中间页商品相关性过滤
DECLARE_SPDM_ABTEST_BOOL(enable_buyer_home_live_cart_filter);  // [huangzhaokai] 买家首页直播购物车过滤 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_merchant_campaign_filter);  // [liubing05] 电商中间页过滤非内循环硬广广告 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_corp_growth_se_preranking);  // [yechen05] 客户增长 SE 项目粗排扶持开关
DECLARE_SPDM_ABTEST_BOOL(enable_boyle_plus_customer);  // [yaokangping] 客户增长 PLUS 项目  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_explore_prerank_papo);  // [chenwu03] 小店探索召回爬坡
DECLARE_SPDM_ABTEST_BOOL(enable_union_shortvideo_dark_prerank_papo);  // [chenwu03] 联盟短视频暗投策略
DECLARE_SPDM_ABTEST_BOOL(disable_hosting_unit);   //  [lizhipeng03] 屏蔽智能托管广告开关
DECLARE_SPDM_ABTEST_BOOL(enable_live_stream_author_filter);  // [liuwenlong03] live_stream.user_id 频控过滤开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_esp_image_filter);  // [wanglei10] 联盟内循环图文创意过滤
DECLARE_SPDM_ABTEST_BOOL(enable_target_fix_soft_resort);

DECLARE_SPDM_ABTEST_BOOL(enable_budget_throttling);  // [liming11] 是否进行预算节流实验
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_ecology_rookie_merchant);   //  [wanghongfei] 内循环召回生态新手村限单过滤 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_explore_ensemble);   //  [wanghongfei] 召回探索分
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_ecology_order_pay);   //  [wanghongfei] 内循环召回生态带货开关
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_cold_filter);   //  [wanghongfei] 内循环召回原创视频冷启动过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ori_filter_close);   //  [wanghongfei] 内循环召回原创视频过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ecology_merchant_filter);  // [wanghongfei] 商家生态商品过滤策略 开关
DECLARE_SPDM_ABTEST_BOOL(enable_ecology_new);  // [wanghongfei] 商家生态商品过滤策略 开关
DECLARE_SPDM_ABTEST_STRING(explore_exp_tag);  // [wanghongfei] 商家生态商品过滤策略 开关
DECLARE_SPDM_ABTEST_STRING(roas_vs_order_exp_tag);  // [wangyang10] 托管下订单、roi 对比实验
DECLARE_SPDM_ABTEST_BOOL(enable_ecology_merchant_filter_fanstop);  // [wanghongfei] 商家生态商品过滤策略 开关
DECLARE_SPDM_ABTEST_BOOL(enable_md5_photo_filter);  // [wanghongfei] 重复素材过滤 开关
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_inspire);  // [tangxiaochao05] 全站推广半互斥中的全互斥实验组 开关
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_unified_rate);  // [zhangjunmin] 请求主站粗排统一模型
DECLARE_SPDM_ABTEST_BOOL(
    enable_prerank_unified_rate_fans_live);  // [zhangjunmin] 主站粗排统一模型作用于 行业直播
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_unified_rate_jinjian);  // [zhangjunmin] 主站粗排统一模型作用于完件
DECLARE_SPDM_ABTEST_BOOL(enable_budget_server_show_cnt);  //  [chenqi07] 软广低 cpm 过滤策略实验
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_nobid_unit_tail_exp);  //  [gaowei03] 联盟内循环 nobid 尾号开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_del_dup_account);  //  [gongxiaopeng03] 联盟多样性过滤开关
DECLARE_SPDM_ABTEST_BOOL(gfp_skip_owe_control);
DECLARE_SPDM_ABTEST_BOOL(enable_bs_sort_weight_symmetry);
DECLARE_SPDM_ABTEST_BOOL(enable_risk_split_ad_type);
DECLARE_SPDM_ABTEST_BOOL(disable_incentive_item_click_nofollow_filter);
DECLARE_SPDM_ABTEST_BOOL(disable_incentive_item_click_u0_filter);

DECLARE_SPDM_ABTEST_BOOL(enable_universe_sug_api_async);     //  [yinliang] 联盟异步调用 sug
DECLARE_SPDM_ABTEST_BOOL(enable_sug_query_type_fix);     //  [yinliang] sug 打标 bug fix
DECLARE_SPDM_ABTEST_BOOL(enable_universe_sug_search_a_2_b);     //  [yinliang] 联盟 sug 搜 a 出 b 优化
DECLARE_SPDM_ABTEST_BOOL(
    enable_universe_sug_search_a_2_b_floor);  //  [yinliang] 联盟 sug 搜 a 出 b 优化, 兜底
DECLARE_SPDM_ABTEST_BOOL(enable_sug_query_type_optimize);     //  [yinliang] 联盟 sug 打分优化
DECLARE_SPDM_ABTEST_BOOL(enable_expandbyrelevance_remove_ratio);     //  [yinliang] 联盟搜索多样性优化
DECLARE_SPDM_ABTEST_BOOL(enable_universe_huge_migrate_tsm_fix);  // [gongxiaopeng03] 联盟大系统迁移 TSM fix
DECLARE_SPDM_ABTEST_DOUBLE(universe_adjust_brecall_quato_ratio);  // [yinliang] 调整召回通路的 quato ratio
DECLARE_SPDM_ABTEST_DOUBLE(universe_adjust_recall_quato_ratio);  // [yinliang] 联盟引流进人非粉粗排打压
DECLARE_SPDM_ABTEST_BOOL(enable_at_skip_rta_table_universe_tiny);   // [qianyangchao] 联盟小系统绕过 rta_table
DECLARE_SPDM_ABTEST_BOOL(enable_universe_prerank_quota_explore);  //  [liangli]  联盟粗排 quota 摸底实验
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ecom_brand_baosong);  // [daidongyang] 品牌电商保送
DECLARE_SPDM_ABTEST_BOOL(enable_outer_loop_e2e_used_item_recall);  // [chenqi07] 外循环下发率召回采样
DECLARE_SPDM_ABTEST_BOOL(enable_outer_loop_e2e_used_item_prerank);  // [chenqi07] 外循环下发率粗排采样
//  [gaowei03] 联盟 gettopn 是否允许海选和 ensemble_sort
DECLARE_SPDM_ABTEST_BOOL(enable_select_brand_by_recall_tag);   // [daidongyang] 根据召回的标签选择品牌电商
DECLARE_SPDM_ABTEST_BOOL(prerank_live_enable_author_valued_fans_recall_baosong);   // [huangzhaokai] 直播粗排高价值粉丝召回通道保送开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_hard_photo_guaranteed_preranking);   // [huangzhaokai] 移动端硬广短视频扶持 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_hard_p2l_guaranteed_preranking);   // [huangzhaokai] 移动端硬广引流扶持 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_fanstop_photo_prerank_num);   // [huangzhaokai]  调增软广作品粗排开关// NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_photo_hard_amd_ranking_compatible);   // [huangzhaokai] 移动版硬广引流 amd_ranking 逻辑兼容 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_live_prerank_white_baosong);    // [qipeng] 直播粗排白名单保送开关  // NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_app_search_prerank_diversity_uniq_quota); // [niejinlong] 厂商搜索粗排多样性 quota  // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_skip_new_creative_for_inspire_merchant_live_ad);  // [liubing05] 激励电商直播跳过新创意过滤  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(follow_ad_queue_type_soft);  // [limiaochen] 关注页行业直播设置软广  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_session_field_global_app_id);  // [limiaochen] 关注页行业直播设置软广  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_high_bid_low_budget_filter);  // [qipeng] 高出价低预算过滤  // NOLINT
DECLARE_SPDM_ABTEST_INT64(mobile_budget_threshold);  // [tangweiqi] 移动端低预算过滤 预算阈值 // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(mobile_drop_ratio_threshold);  // [tangweiqi] 移动端低预算过滤 drop ratio 阈值 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_delivery_interval_seconds_inspire_merchant_live_ad);  // [liubing05] 激励电商直播独立下发间隔  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_live_ad_individual_quota); // [liubing05] 激励电商直播多样性独立 quota  // NOLINT
DECLARE_SPDM_ABTEST_INT64(follow_quality_user_effective_filter_type);  // [lishaozhe] 涨粉质量 U 类客户过滤类型 // NOLINT
DECLARE_SPDM_ABTEST_INT64(recall_cate3_length_threshold);  // [linwei] recall_cate3_length_threshold
DECLARE_SPDM_ABTEST_INT64(recall_cid_cate3_length_threshold);  // [wucunlin] recall_cid_cate3_length_threshold
DECLARE_SPDM_ABTEST_BOOL(enable_tdm_reco_rank);        // [chenwancheng03] TDM 跳粗排开关
DECLARE_SPDM_ABTEST_INT64(recall_cate3_time_threshold);  // [linwei] recall_cate3_time_threshold
DECLARE_SPDM_ABTEST_INT64(recall_cid_cate3_time_threshold);  // [wucunlin] recall_cid_cate3_time_threshold
DECLARE_SPDM_ABTEST_BOOL(use_nebula_prerank_purchase);  //  [zhangjunmin] 粗排付费使用 nebula_prerank_purchase

DECLARE_SPDM_ABTEST_BOOL(enable_ctcvr_photo_live_prerank);        // [yewencai] 粉条作品 & 直播 ctcvr 粗排开关

DECLARE_SPDM_ABTEST_BOOL(enable_outer_photo_rule_engine_check);  // [zhangruyuan] 外循环短视频规则引擎 check
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_retrieval_check);  // [zhangruyuan] 外循环直播仅从直播召回 check

DECLARE_SPDM_ABTEST_BOOL(enable_other_photo_live_prerank);        // [yewencai] 粉条作品 & 直播其他粗排开关
DECLARE_SPDM_ABTEST_BOOL(enable_ctcvr_roi_fix);                   // [yewencai] 粉条作品 & 直播 roi 订单修复
DECLARE_SPDM_ABTEST_BOOL(enable_soft_photo_live_quota);           // [yewencai] 软广共享 quota 开关
DECLARE_SPDM_ABTEST_BOOL(enable_soft_prerank_ensemble_sort);      // [yewencai] 软广粗排 ensemeble sort 开关
DECLARE_SPDM_ABTEST_BOOL(enable_soft_prerank_use_dup_filter);     // [yewencai] 软广粗排去重开关
DECLARE_SPDM_ABTEST_BOOL(enable_hard_prerank_ctcvr_fix);          // [yewencai] 硬广 ctcvr 回归粗排开关
DECLARE_SPDM_ABTEST_BOOL(ab_switch_enable_user_ad_risk_control);  // [heqian] 用户风控开关
DECLARE_SPDM_ABTEST_BOOL(universe_site_assembly_filter_opt);  // [wanglei10] 联盟建站组件屏蔽优化
DECLARE_SPDM_ABTEST_BOOL(enable_amd_photo_p2l_prerank_input_quota_refactor);  // [linwei] amd photo p2l input quota 融合实验  // NOLINT
DECLARE_SPDM_ABTEST_INT64(inner_hard_photo_p2l_ranking_quota);  // [linwei] amd photo p2l input quota 融合实验  // NOLINT
DECLARE_SPDM_ABTEST_INT64(inner_hard_reward_photo_p2l_ranking_quota);  // [linwei] amd photo p2l input quota 融合实验  // NOLINT
DECLARE_SPDM_ABTEST_INT64(cache_inner_hard_photo_p2l_ranking_quota);  // [linwei] amd photo p2l input quota 融合实验  // NOLINT
// [liuxiaoyan] 系统缓存频控
DECLARE_SPDM_ABTEST_BOOL(enable_client_industry_freq);
DECLARE_SPDM_ABTEST_BOOL(enable_client_product_freq);
DECLARE_SPDM_ABTEST_INT64(target_author_diversity_quota);
DECLARE_SPDM_ABTEST_INT64(client_industry_freq_imps_num);
DECLARE_SPDM_ABTEST_INT64(client_product_freq_imps_num);

DECLARE_SPDM_ABTEST_BOOL(enable_roas_block_no_sale_ad);  // [wangyang10] 直播 ROAS 不挂车的屏蔽
DECLARE_SPDM_ABTEST_DOUBLE(roas_block_no_fans_ratio);  // [wangyang10] 直播 ROAS 不挂车的屏蔽非粉

// [jiangjiaxin]
DECLARE_SPDM_ABTEST_BOOL(enable_use_esp_coldstart_tag_realtime);  // 金牛粗排扶持新口径
// [cuihongyi]
DECLARE_SPDM_ABTEST_BOOL(enable_inner_inspire_live_order_no_auto);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_inspire_live_order_no_dark);

// [wangpeng16] 外粉 merge 数量控制
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_merge_quota_control);
DECLARE_SPDM_ABTEST_INT64(fanstop_merge_max_quota);
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_merge_max_ratio);

// [lixu05]
DECLARE_SPDM_ABTEST_STRING(realtime_recall_tag_key);  // 实时召回实验组别
DECLARE_SPDM_ABTEST_STRING(brand_fans_recall_tag_key);  // 品牌粉丝召回组别
DECLARE_SPDM_ABTEST_BOOL(enable_shallow_cls);           // cls 浅度屏蔽
DECLARE_SPDM_ABTEST_STRING(shallow_ocpx_cls_list);      // 浅度的 cls 屏蔽目标
DECLARE_SPDM_ABTEST_BOOL(enable_true_cid_revert_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_ranking_replace_photo_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_ranking_replace_ocpx_exp);
DECLARE_SPDM_ABTEST_INT64(merge_storewide_spu_ocpx_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_merge_all_cvr_ctcvr_multi);

DECLARE_SPDM_ABTEST_BOOL(universe_inner_loop_fans_target_expand);  // [yingyuanxiang] 联盟内循环粉丝定向拓展开关  // NOLINT
// [tengwei] 新内粉,剔除电商素材
DECLARE_SPDM_ABTEST_BOOL(enable_load_outerloop_interest_retarget_product);  //  [yangfukang03] 填充用户画像
DECLARE_SPDM_ABTEST_BOOL(enable_load_llm_user_prod_interest_list);  //  [yangfukang03] 填充用户画像
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_nc_target_strategy);  //  [yangfukang03] 外循环 nc 策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_low_active_ac_pass);  //  [yangfukang03] 低活 ac 标签
DECLARE_SPDM_ABTEST_BOOL(enable_update_outerloop_nc_kconf);  //  [yangfukang03] 调整 kconf 配置
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_nc_limit_ocpx);  //  [yangfukang03] nc 限制优化目标
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_nc_model_score);  //  [yangfukang03] nc 模型加载
DECLARE_SPDM_ABTEST_STRING(inner_bonus_exp_tag);   // [wanghongfei]  blue sea fix 实验
DECLARE_SPDM_ABTEST_BOOL(enable_high_return_user_filter);   // [lihantong] 过滤品退用户
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_v3_flow_control);   // [lihantong] 流控
DECLARE_SPDM_ABTEST_INT64(high_return_order_filter_thresh);  // [lihantong] 过滤品退用户
DECLARE_SPDM_ABTEST_BOOL(disable_merchant_inspire_inner_filter);   // [maxiu] 电商激励虚拟金过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_skip_inspire_video_esp_mobile_plc_filter);   // [jiangfeng06] 激励视频移动版跳过小黄车过滤 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_skip_inspire_video_fanstop_plc_filter);   // [liqinglong] 激励视频粉条跳过组件过滤 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_skip_black_list_filter_for_inspire_merchant);   // [liubing] 激励电商跳过黑名单过滤 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_close_week_stay_retarget);
DECLARE_SPDM_ABTEST_BOOL(enable_intelligence_population_break_soft);
DECLARE_SPDM_ABTEST_BOOL(enable_close_stay_retarget);
DECLARE_SPDM_ABTEST_INT64(week_stay_retarget_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_target_load_nc_second_industry);
// [moqi] 激励直播屏蔽功能对内循环都生效
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_shield_with_all_inner);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_loop_photo_stable_index_fix);  // [chenwancheng03] 召回稳定排序 fix 临时实验 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_loop_live_stable_index_fix);   // [chenwancheng03] 召回稳定排序 fix 临时实验 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_nobid_strategy_fix);                 // [chenwancheng03] 召回稳定排序 fix 临时实验 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_item_photo_ctrl);   // [wanghongfei] 召回多样性过滤门槛
DECLARE_SPDM_ABTEST_INT64(p2l_roas_quota);
// [chenxian] 召回 merge 阶段 photoOcpx 全局多样性 quota
DECLARE_SPDM_ABTEST_INT64(photo_ocpx_global_diversity_quota);
// [chenxian] 召回 merge 阶段 photo 全局多样性 quota
DECLARE_SPDM_ABTEST_INT64(photo_global_diversity_quota);

DECLARE_SPDM_ABTEST_BOOL(
    enable_fanstop_recruit_live_skip_filter);   // [mayanbin] 粉条招工招聘直播间跳过一些 filter
DECLARE_SPDM_ABTEST_BOOL(
    enable_fanstop_recruit_population_filter);   // [caolin] 粉条招工人群定向实验开关
DECLARE_SPDM_ABTEST_BOOL(disable_pop_population_filter);   // [yangfukang03] 招工定向
DECLARE_SPDM_ABTEST_BOOL(enable_app_ls_use_new_population);   // [yangfukang03] 招工定向

DECLARE_SPDM_KCONF_BOOL(enableMonitorPopRecruit);  // [maileyi] 磁力快招 B 端监控开关

DECLARE_SPDM_ABTEST_BOOL(
    enable_iap_core_population_recall_filter);   // [chenxian] iap 核心人群保护
DECLARE_SPDM_ABTEST_BOOL(
    enable_iap_potential_population_recall_filter);   // [chenxian] iap 潜力人群保护
DECLARE_SPDM_ABTEST_BOOL(recruit_population_skip_explore_thanos);   // [caolin] 粉条招工定向跳过发现页实验
DECLARE_SPDM_ABTEST_BOOL(recruit_population_skip_explore_normal);   // [caolin] 粉条招工定向跳过发现页实验
DECLARE_SPDM_ABTEST_BOOL(
    diable_fanstop_photo_recruit_population_filter);  // [caolin] 粉条招工作品定向实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_live_cold_start_tag);  // [caolin] 粉条直播冷启动开关
DECLARE_SPDM_ABTEST_BOOL(enable_esp_mobile_live_cold_start_tag);  // [chencongzheng] 移动端冷启动打标
DECLARE_SPDM_ABTEST_BOOL(enable_esp_live_one_step_recall_gpm_factor);  // [lihantong] 粗排 gpm ensemble
DECLARE_SPDM_ABTEST_DOUBLE(esp_live_one_step_gpm_ratio);  // [lihantong] 粗排 gpm ensemble
// [moqi] 只有直投直播冷启动标签单独计算
DECLARE_SPDM_ABTEST_BOOL(enable_only_direct_live_cold_start_tag_cal);
// [moqi] 激励直播白名单客户控比放开
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_control_whitelist_off);
DECLARE_SPDM_ABTEST_BOOL(enable_shop_link_thanos_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_pc_shop_link_filter);

// [nizhihao]
DECLARE_SPDM_ABTEST_BOOL(enable_commercial_user_info_pass);
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_litter_anchor_adjust);
DECLARE_SPDM_ABTEST_BOOL(enable_kai_game_iaa_retrieval);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_redis_parse);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_retarget_recall);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_stra_field);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_stra_field_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_retarget_game_recall_monitor);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_inspire_block_iap);
DECLARE_SPDM_ABTEST_BOOL(enable_game_big_r_explore_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_iaap_7r_retrieval);
DECLARE_SPDM_ABTEST_STRING(enable_mini_game_big_r_strategy_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_new_big_r_field);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_params_parse);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_payment_window_30d);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_use_long_value);
DECLARE_SPDM_ABTEST_DOUBLE(default_iap_payment_constraint);
DECLARE_SPDM_ABTEST_DOUBLE(default_iap_long_value_constraint);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_strategy_by_30d);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_strategy_by_24h);
DECLARE_SPDM_ABTEST_INT64(mini_game_big_r_ee_strategy_24h_thrd);
DECLARE_SPDM_ABTEST_INT64(mini_game_big_r_ee_strategy_30d_thrd);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_uv_monitor);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_uv_monitor_new);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_explore_skip_diversity);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_explore_add_quota);
DECLARE_SPDM_ABTEST_INT64(big_r_explore_add_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_monitor_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_skip_invert_target);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_user_update);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_explore_end);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_explore_skip_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_game_conv_user_freq);
DECLARE_SPDM_ABTEST_INT64(game_conv_user_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_game_user_level_diversity);
DECLARE_SPDM_ABTEST_DOUBLE(game_user_level_diversity_thrd);
// [wangzixu05]
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_trigger_add_outer_quota);
DECLARE_SPDM_ABTEST_INT64(inner_explore_trigger_add_outer_quota);
DECLARE_SPDM_ABTEST_INT64(inner_explore_trigger_outer_path_id);
DECLARE_SPDM_ABTEST_BOOL(enable_effect_path_set_inner_explore);
DECLARE_SPDM_ABTEST_STRING(inner_explore_effect_path_group);
// [wengrunze]
DECLARE_SPDM_ABTEST_STRING(private_message_retarget_retrieval_efficient_group);

DECLARE_SPDM_ABTEST_BOOL(enable_univ_prerank_live_follow_model);  //  [gaowei03] >直播粗排涨粉模型开关
// [lichao21]
DECLARE_SPDM_ABTEST_BOOL(enable_kai_game_iaap_retrieval);

// [zhaoyi13]
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_not_na_user_recall);

// [zengzhengda] 联盟活跃 app 补充定向已激活 v2
DECLARE_SPDM_ABTEST_BOOL(enable_universe_action_app_fill_conv_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_live_item);  // [huangzhaokai] 猜你喜欢投放直播物料开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_photo_filter_feature);  // [liubing05] 低质 photo 过滤功能开关
DECLARE_SPDM_ABTEST_BOOL(enable_kesu_account_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_kesu_industry_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_account_type_filter);   // [liuxiaoyan]  聚星频控 1.0
DECLARE_SPDM_ABTEST_DOUBLE(short_play_time_interval);   // [liuxiaoyan]  新快滑广告播放时长
DECLARE_SPDM_ABTEST_INT64(short_play_photo_check_seconds);  // [liuxiaoyan]  快滑广告多长时间不出
DECLARE_SPDM_ABTEST_INT64(short_play_photo_industry_check_seconds);  // [liuxiaoyan]  快滑广告多长时间不出
DECLARE_SPDM_ABTEST_INT64(short_play_photo_first_industry_check_seconds);  // [rentingyu]  一级行业快滑广告多长时间不出 // NOLINT
// [liuxiaoyan] 短播策略优化
DECLARE_SPDM_ABTEST_BOOL(enable_product_filter_calc);
DECLARE_SPDM_ABTEST_INT64(short_play_time_interval_v3);
DECLARE_SPDM_ABTEST_INT64(click_check_seconds);
DECLARE_SPDM_ABTEST_INT64(product_watch_times);
DECLARE_SPDM_ABTEST_DOUBLE(short_play_ratio);

// [bianfeifei]
DECLARE_SPDM_ABTEST_BOOL(enable_x7_rookie_seller_filter);
DECLARE_SPDM_ABTEST_BOOL(new_enable_x7_rookie_seller_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_category_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_ocpx_action_type_filter);  // [yangyanxi] 激励优化目标过滤
DECLARE_SPDM_ABTEST_BOOL(enable_mingtou_recall_product_filter);  // [yangyanxi] 激励明投召回白名单过滤
DECLARE_SPDM_ABTEST_BOOL(enable_refresh_count_manage);  // [yangyanxi] 刷次调度质量过滤
DECLARE_SPDM_ABTEST_BOOL(enable_refresh_count_move_ahead);  // [yangyanxi] 刷次调度过滤前置
DECLARE_SPDM_ABTEST_BOOL(enable_refresh_count_use_bidinfo);  // [yangyanxi] 刷次调度低出价过滤
DECLARE_SPDM_ABTEST_BOOL(enable_feed_product_name_blacklist);  // [yangyanxi] 刷次调度低出价过滤
DECLARE_SPDM_ABTEST_BOOL(enable_zhutui_all_flow_filter);  // [yangyanxi] 流量助推广告过滤实验
DECLARE_SPDM_ABTEST_BOOL(prerank_ctcvr_cover_all_ad);  // [zhangjunmin] 粗排 ctcvr 模型覆盖所有广告物料
DECLARE_SPDM_ABTEST_BOOL(prerank_deep_unified_cover_other_ad);  // [zhangjunmin] 粗排深度统一模型兜底
DECLARE_SPDM_ABTEST_BOOL(enable_inner_author_freq_filter);  // [gaowei03] 联盟内循环作者频控开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_age_merge_filter);     // [liuyibo05] 联盟年龄合并白名单实验
DECLARE_SPDM_ABTEST_BOOL(enable_universe_age_expand);   // [liuyibo05] 联盟年龄定向扩展实验
DECLARE_SPDM_ABTEST_BOOL(enable_inner_author_freq_filter_without_follow);
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_invoked_product_retrieval);  // [gaozepeng] 激励唤端产品召回
DECLARE_SPDM_ABTEST_BOOL(enable_special_account_type_filter);
// [gaowei03] 联盟内循环涨粉过作者频控开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_photo_id_freq_filter);
// [gaowei03] 联盟内循环短视频 photo_id 频控开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_live_id_freq_filter);
// [gaowei03] 联盟内循环直播 live_id 频控开关
DECLARE_SPDM_ABTEST_BOOL(enable_gross_profit_explore_account_filter);  // [zhoumingsong] 毛利探索策略
DECLARE_SPDM_ABTEST_BOOL(enable_select_high_quality);  // [caining] 素材扶优开关 召回
DECLARE_SPDM_ABTEST_BOOL(video_roas_use_new_ctcvr_bid);   //  [gaowei03] 联盟短视频 ctcvr bid
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_new_cpa_bid_60);   //  [gongxiaopeng03] 联盟 ctcvr bid 60
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ctcvr_auto_cpa_bid);   //  [fengyajuan] 联盟 ctcvr
DECLARE_SPDM_ABTEST_BOOL(enable_promotion_climb_new_frame);     // [yaokangping] 大促爬坡新框架开关
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_native_prerank_append);    // [yaokangping] 原生广告粗排爬坡开关
DECLARE_SPDM_ABTEST_BOOL(enable_innerloop_item_card_prerank_append);    // [luwei] 商品卡广告粗排爬坡开关
DECLARE_SPDM_ABTEST_BOOL(enable_auto_campaign);    // [denghuiyang] 智能操盘投放广告粗排爬坡开关
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_append_operation);     // [yaokangping] 粗排爬坡运营层开关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_native_special_support_f1_preranking);    // [guoqi03] 专项一层原生注册开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_app_filter_white_list);  // [liuwenlong03] 白名单用户跳过 以安装应用过滤
DECLARE_SPDM_ABTEST_BOOL(enable_live_target_ecpm_and_delivery);  // [fangmeiling] 金牛直播召回拆分 ecpm 和 delivery 队列 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(live_target_delivery_use_auto_roas);  // [fangmeiling] 金牛直播召回 delivery 队列是否用 auto_roas // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_live_target_delivery_recall_rank);  // [fangmeiling] 金牛直播召回 recall delivery 队列排序 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_merge);  // [wubo05] 激励直播走 LiveMerge 开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_action_target_product_filter_2);  //  [songxu] 用户目标行为过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_action_target_account_filter);  //  [songxu] 用户目标行为过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_splash_action_target_product_filter);  //  [songxu] 用户目标行为过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_action_target_product_filter_group);  //  [songxu] 用户目标行为过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_ee_search_prerank_quota_boost);   // [songxu] 人群探索请求 精排开关
DECLARE_SPDM_ABTEST_BOOL(enable_target_search_linucb_score_exp);  // [songxu] 人群探索请求 预估开关
DECLARE_SPDM_ABTEST_BOOL(enable_cid_prerank_cvr_boost_exp);       // [songxu] cid 粗排 cvr 调整系数
DECLARE_SPDM_ABTEST_DOUBLE(cid_prerank_cvr_boost_ratio);          // [songxu] cid 粗排 cvr 调整系数
DECLARE_SPDM_ABTEST_INT64(action_target_account_filter_tag);      // [songxu] 账户频控 tag
DECLARE_SPDM_ABTEST_BOOL(enable_cid_pay_refund_filter);           // [songxu] cid 频控
DECLARE_SPDM_ABTEST_BOOL(enable_cid_pay_refund_industry_filter);           // [songxu] cid 频控
DECLARE_SPDM_ABTEST_BOOL(enable_action_filter_diff_scene_control);      // [zengdi] 频控分资源位单独配置开关
DECLARE_SPDM_ABTEST_BOOL(enable_cid_pay_refund_industry_filter_flow);        // [songxu] cid 频控

DECLARE_SPDM_ABTEST_BOOL(delivery_auto_cpabid_or_roas_filter);  // [fangmeiling] 金牛直播召回 preselect expander 使用 min_bid 过滤 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_universe_origin_live);     // [wanglei10] 联盟不支持原生直播实验
DECLARE_SPDM_ABTEST_BOOL(enable_cid_spu_strategy_exp);                 // [songxu] cid spu 实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_cid_corp_default_strategy_exp);      // [songxu] cid 客户无标记默认提价策略
DECLARE_SPDM_ABTEST_DOUBLE(cid_corp_default_bid_ratio);              // [songxu] cid 客户无标记默认提价系数
DECLARE_SPDM_ABTEST_BOOL(enable_cid_quality_strategy_exp);
DECLARE_SPDM_ABTEST_INT64(convert_filter_switch_rule_status);       // [zhangruyuan] 已转化过滤切换 rule
DECLARE_SPDM_ABTEST_BOOL(enable_cid_refund_ctrl_exp);
DECLARE_SPDM_ABTEST_DOUBLE(cid_refund_corp_weight);
DECLARE_SPDM_ABTEST_DOUBLE(cid_refund_spu_weight);
DECLARE_SPDM_ABTEST_DOUBLE(cid_refund_user_weight);
DECLARE_SPDM_ABTEST_DOUBLE(cid_refund_filter_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_refund_conv_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_refund_filter_thr_reward);
DECLARE_SPDM_ABTEST_DOUBLE(cid_refund_filter_thr_reward);

DECLARE_SPDM_ABTEST_BOOL(fanstop_live_admit_exp_filter);  // [cuiyanliang] livetarget 软广直播投放  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_retention_to_middle_platform);  //  [zhanshenxin] 次留粗排切中台模型
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_key_action_to_middle_platform);  //  [zhanshenxin] 关键行为粗排切中台模型  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ad_purchase_to_middle_platform);  //  [zhanshenxin] 付费粗排切中台模型
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_diff_middle_platform);  //  [limiao03] 中台模型临时实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_convpay_deep_to_middle_platform);  //  [limiao03] 付费深度双粗排切中台  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_middle_platform);  //  [limiao03] 粗排中台模型流量控制
DECLARE_SPDM_ABTEST_BOOL(enable_double11_skip_diversity_filter);  // [sixianbo] 双 11 白名单跳过多样性过滤  // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_live_target_delivery_use_auto_bid);  // [fangmeiling] 金牛直播一段式召回 delivery 队列用 auto bid // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_jvxing_negtive_tag_filter);  // [zhangruyuan] 流量助推负向过滤实验
DECLARE_SPDM_ABTEST_BOOL(enable_target_request_pid_server);  // [zhangruyuan] Target请求PID Server // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_merger_stop_watch);  // [zhangruyuan] fanstop 使用 stop watch
DECLARE_SPDM_ABTEST_BOOL(enable_thanos_skip_cpm_filt);  // [zhangruyuan] 单列跳过 CPM 过滤 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_target2rank_second_industry_5);  // [zhangruyuan] 透传行业 5.0 二级行业 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_thanos_skip_cpc_filt);  // [songxu] 单列跳过 CPM 过滤 // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_inner_loop_splash_ad_queue);  // [liubing05] 开屏内循环队列开关
DECLARE_SPDM_ABTEST_BOOL(enable_splash_multi_retrieval);  // [huangwei06] 开屏多路召回开关
DECLARE_SPDM_ABTEST_BOOL(enable_splash_rtb_prefetch_realtime_check);  // [huangwei06] 开屏 check 逻辑服用
DECLARE_SPDM_ABTEST_STRING(splash_realtime_exp_name);
DECLARE_SPDM_ABTEST_STRING(promotion_type_switch_exp_tag);  // [zhangruyuan] esp 经营版切换专推实验参数
DECLARE_SPDM_ABTEST_INT64(splash_w_user_level_thr);  // [liubing05] 开屏实时链路用户价值分门槛
DECLARE_SPDM_ABTEST_INT64(splash_cache_ids_thr);  // [liubing05] 开屏实时链路缓存门槛

DECLARE_SPDM_ABTEST_BOOL(enable_soft_negative_author_filter);        // [mengfangyuan] 软广负反馈作者频控
DECLARE_SPDM_ABTEST_BOOL(enable_reco_mutual_black_author_filter);    // [huangzhaokai] reco 双向拉黑过滤
DECLARE_SPDM_ABTEST_BOOL(enable_outer_reco_mutual_black_author_filter);  // [huangzhaokai] 外循环 reco 双向拉黑过滤  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_soft_negative_hetu_filter);        // [mengfangyuan] 软广负反馈河图标签频控
DECLARE_SPDM_ABTEST_BOOL(enable_dup_photo_distinct_filter);     // [mengfangyuan] dup photo 去重
DECLARE_SPDM_ABTEST_BOOL(enable_native_author_seq_filter);  // [huangzhaokai] 软广曝光序列 author 维度频控
DECLARE_SPDM_ABTEST_BOOL(enable_browsed_photo_freq_spec);        // [mengfangyuan] 支持 618 大促特定频控
DECLARE_SPDM_ABTEST_BOOL(enable_browsed_photo_freq_spec_outer);        // [mengfangyuan] 支持 618 大促特定频控
DECLARE_SPDM_ABTEST_BOOL(enable_browsed_photo_freq_spec_sensitive_cat);  // [mengfangyuan] 敏感类目

DECLARE_SPDM_ABTEST_BOOL(enable_user_exp_recall_predict);       // [rentingyu] 用户体验粗排预估
DECLARE_SPDM_ABTEST_BOOL(enable_neg_feed_filter);       // [rentingyu] 用户体验粗排负反馈退出策略
DECLARE_SPDM_ABTEST_BOOL(enable_block_second_industry_id);       // [rentingyu] 指定二级行业过滤
DECLARE_SPDM_ABTEST_BOOL(enable_block_second_industry_id_v2);       // [guanpingyin] 指定二级行业过滤
DECLARE_SPDM_ABTEST_BOOL(enable_block_kgame_sub_class);   // [jiangpeng07] 快小游子类过滤
DECLARE_SPDM_ABTEST_BOOL(enable_block_product_name);       // [rentingyu] 指定产品过滤
DECLARE_SPDM_ABTEST_BOOL(enable_block_product_quick_app);
DECLARE_SPDM_ABTEST_BOOL(enable_shop_score_exp_filter);       // [rentingyu] 新模型低观感素材过滤升级
DECLARE_SPDM_ABTEST_BOOL(enable_low_quality_filter_v3);       // [rentingyu] 低质素材模型升级低观感素材过滤
DECLARE_SPDM_ABTEST_BOOL(enable_upgrade_behavior_interest);   // [zhangruyuan] 行为意向升级 4.0
DECLARE_SPDM_ABTEST_DOUBLE(shop_score_exp_theshhold);  // [rentingyu] 商品分过滤阈值
DECLARE_SPDM_ABTEST_DOUBLE(low_quality_score_theshhold);  // [rentingyu] 新模型低观感素材过滤阈值
DECLARE_SPDM_ABTEST_DOUBLE(low_quality_score_theshhold_v3);  // [rentingyu] 低质素材模型升级低观感素材过滤阈值
DECLARE_SPDM_ABTEST_BOOL(enable_ue_ensemble);       // [rentingyu] 粗排 ensemble 添加体验分
DECLARE_SPDM_ABTEST_INT64(explore_user_tag);       // [rentingyu] 规则圈人群 tag
DECLARE_SPDM_ABTEST_BOOL(enable_third_category_personalize_freq);       // [rentingyu] 曝光频次个性化频控  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_industry_negative_freq);       // [qiaolin] 行业快滑频控开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_industry_negative_freq_v2);       // [qiaolin] 行业快滑频控开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_category_negative_freq_outter);       // [panshunda] 三级类目快滑频控外循环开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_category_negative_freq_inner);       // [panshunda] 三级类目快滑频控内循环开关  // NOLINT
DECLARE_SPDM_ABTEST_INT64(industry_negative_check_seconds);       // [qiaolin] 行业快滑频控时间限制  // NOLINT
DECLARE_SPDM_ABTEST_INT64(industry_negative_check_seconds_v2);       // [qiaolin] 行业快滑频控时间限制  // NOLINT
DECLARE_SPDM_ABTEST_INT64(category_negative_outter_check_seconds);       // [panshunda] 外循环类目快滑频控时间限制  // NOLINT
DECLARE_SPDM_ABTEST_INT64(category_negative_inner_check_seconds);       // [panshunda] 内循环类目快滑频控时间限制  // NOLINT
DECLARE_SPDM_ABTEST_INT64(industry_negative_played_duration);       // [qiaolin]  行业快滑频控快滑时间  // NOLINT
DECLARE_SPDM_ABTEST_INT64(industry_negative_check_cnt);       // [qiaolin] 行业快滑频控次数限制  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(industry_negative_check_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(category_negative_outter_check_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(category_negative_inner_check_ratio);
DECLARE_SPDM_ABTEST_INT64(industry_negative_total_num_limit);
DECLARE_SPDM_ABTEST_INT64(category_negative_outter_total_num_limit);
DECLARE_SPDM_ABTEST_INT64(category_negative_inner_total_num_limit);

DECLARE_SPDM_ABTEST_BOOL(enable_personalize_freq_del_short_play);       // [rentingyu] 曝光频次个性化频控豁免短播条件  // NOLINT
DECLARE_SPDM_ABTEST_INT64(third_category_imp_personalize_check_seconds);       // [rentingyu] 曝光频次个性化频控曝光次数时间限制  // NOLINT
DECLARE_SPDM_ABTEST_INT64(third_category_imp_personalize_played_duration);       // [rentingyu] 曝光频次个性化频控短播检查  // NOLINT
DECLARE_SPDM_ABTEST_INT64(third_category_imp_personalize_check_cnt);       // [rentingyu] 曝光频次个性化频控曝光次数检查  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_product_negative_duration_personalize_freq);       // [rentingyu] 产品短播个性化频控  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_category_negative_duration_personalize_freq);       // [rentingyu] 产品短播个性化频控  // NOLINT
DECLARE_SPDM_ABTEST_INT64(negative_personalize_check_seconds);       // [rentingyu] 产品短播个性化频控时间阈值  // NOLINT
DECLARE_SPDM_ABTEST_INT64(negative_personalize_played_duration);       // [rentingyu] 产品短播个性化频控短播阈值  // NOLINT
DECLARE_SPDM_ABTEST_INT64(negative_personalize_check_cnt);       // [rentingyu] 产品短播个性化频控总次数阈值  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(negative_personalize_check_neg_ratio);  // [rentingyu] 产品短播个性化频控负反馈阈值  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_soft_photo_imp_freq);       // [rentingyu] 内循环软广曝光频控  // NOLINT
DECLARE_SPDM_ABTEST_INT64(inner_soft_photo_imp_freq_check_seconds);      // [rentingyu] 内循环软广曝光频控时间间隔  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_block_account_id);       // [guanpingyin] 账户过滤
DECLARE_SPDM_ABTEST_STRING(block_account_exp_tag);       // [guanpingyin] 账户过滤
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_dark_account_refund_filter);       // [gaozepeng] 激励暗投账户退单过滤  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_explicit_account_refund_filter);       // [gaozepeng] 激励明投账户退单过滤  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_auto_account_refund_filter);       // [gaozepeng] 激励账户退单过滤自动化  // NOLINT
DECLARE_SPDM_ABTEST_STRING(inspire_auto_account_refund_filter_thres_conf_str);       // [gaozepeng] 激励账户退单过滤自动化生效门槛配置  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inspire_auto_account_refund_rate_coef);       // [gaozepeng] 激励账户退单率系数  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inspire_account_refund_rate_diff_root);       // [gaozepeng] 激励账户退单率差值根方  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_live_purchased_freq_short_play);       // [rentingyu] 叶子类目已购频控直播优化  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_photo_purchased_freq_short_play);       // [rentingyu] 叶子类目已购频控短视频优化  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_transfer_category_a_to_b);       // [qiaolin] 叶子类目已购频控类目切换  // NOLINT
DECLARE_SPDM_ABTEST_INT64(purchased_freq_short_play_check_seconds);       // [rentingyu] 叶子类目短播队列间隔  // NOLINT
DECLARE_SPDM_ABTEST_INT64(purchased_freq_short_play_played_duration);       // [rentingyu] 叶子类目短播时间  // NOLINT
DECLARE_SPDM_ABTEST_INT64(purchased_freq_short_play_check_cnt);       // [rentingyu] 叶子类目短播次数  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_white_product_name);       // [rentingyu] 产品名频控白名单  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_white_author_id);       // [rentingyu] author 频控白名单  // NOLINT

DECLARE_SPDM_ABTEST_INT64(universe_live_fans_max_num);  // [huangwenbin] 联盟内循环直播粉丝量最大值截断
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_fans_short_video);  // [huangwenbin] 联盟内循环粉丝短视频开关
DECLARE_SPDM_ABTEST_BOOL(enable_univ_prerank_live_audience_no_fans_filter);  // [yingyuanxiang] 联盟进人非粉粗排打压  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_live_follow_dp);  // [xiemiao] 联盟内循环直播粉丝召回开关
DECLARE_SPDM_ABTEST_DOUBLE(live_audience_no_fans_ratio);  // [yingyuanxiang] 联盟直投进人非粉粗排打压
DECLARE_SPDM_ABTEST_DOUBLE(merchant_prerank_cpm_order_ratio);  // [fengyajuan] 联盟短视频系数
DECLARE_SPDM_ABTEST_DOUBLE(merchant_prerank_cpm_roas_ratio);  // [fengyajuan] 联盟短视频系数
DECLARE_SPDM_ABTEST_DOUBLE(live_audience_no_fans_ratio_p2l);  // [yingyuanxiang] 联盟引流进人非粉粗排打压
DECLARE_SPDM_ABTEST_BOOL(enable_universe_wt_live_target);   // [yingyuanxiang] 联盟接入直播宽表  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_live_pay_yellow_chart_filter);     // [yingyuanxiang] 联盟直播订单未挂车过滤  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_live_roas_yellow_chart_filter);    // [yingyuanxiang] 联盟直播 ROAS 未挂车过滤 // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_corporation_ceiling_preranking);    // [guoqi03] 客户天花板扶持开关
DECLARE_SPDM_ABTEST_BOOL(enable_photo_ceiling_preranking);    // [guoqi03] 素材天花板扶持开关

DECLARE_SPDM_ABTEST_BOOL(enable_whitelist_guaranteed_preranking);   // [yaokangping] 粗排爬坡强保层白名单爬坡开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_special_kwai_promotion_local_store_order);  // [yechen05] 本地推扶持开关
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_append_exp_trace);  // [liuqiaoyang] 粗排爬坡自动调参 trace 开关
DECLARE_SPDM_ABTEST_BOOL(enable_guaranteed_preranking_f4_wechat_game);   // [guanpingyin] 小游戏粗排爬坡 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_esp_live_roas_prerank_atv_v2);  // [wangyuan11] 粗排 ROAS 为转化率 * 客单价的形式  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(recall_live_auto_bid_weight);  // [wangyuan11] 直播召回一段式排序 bid 的权重
DECLARE_SPDM_ABTEST_BOOL(enable_esp_live_pc_roas_filter_exp);  // [wangyuan11] 金牛直播 pc roas 不出摸底实验

DECLARE_SPDM_ABTEST_DOUBLE(roas_one_step_ecpm_boost_coef);  // [hutao]  one step ecpm roas 类 boost 权重

DECLARE_SPDM_ABTEST_BOOL(enable_close_mcb_append);  //  [yaokangping] 粗排爬坡新框架关闭 mcb 爬坡开关
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_append_tag_logging);    //  [yaokangping] 粗排爬坡策略标记透传 rank 开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_new_merchant_filter);    // [chencongzheng] 新客过滤实验 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_individual_quota_for_inspire_merchant);   // [liubing05] 激励电独立 quota 开关
DECLARE_SPDM_ABTEST_BOOL(enable_remove_negapp)  // [sunkang] 移除 neg app 历史逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_split_cache_list_for_fanstop);  // [sunkang] 缓存队列拆分
DECLARE_SPDM_ABTEST_INT64(fanstop_dragon_prerank_timeout);  // [sunkang] fanstion 粗排超时设置
DECLARE_SPDM_ABTEST_INT64(dragon_outer_predict_prerank_timeout);  // [houjian] 粗排预估+排序超时设置
DECLARE_SPDM_ABTEST_INT64(dragon_prerank_timeout);  // [sunkang] 粗排超时设置
DECLARE_SPDM_ABTEST_BOOL(skip_fanstop_v2_tag_filter);  // [mateng05] 粉条跳过内容安全审过滤
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_for_inner);  // [liqikun] 万合「内循环短视频引流」放开
DECLARE_SPDM_ABTEST_BOOL(enable_context_init_optimization_exp);  // [liqikun] fanstop context data 初始化优化
DECLARE_SPDM_KCONF_BOOL(enable_context_init_optimization);  // [liqikun] fanstop context data 初始化优化
DECLARE_SPDM_ABTEST_BOOL(enable_inner_rta_live_exp);  // [liqikun] 行业直播接入 rta
DECLARE_SPDM_KCONF_BOOL(enable_inner_rta_live);  // [liqikun] 行业直播接入 rta
DECLARE_SPDM_KCONF_BOOL(enable_live_skip_dragon_bid_handler);  // [liqikun] 行业直播接入 rta 动态出价
DECLARE_SPDM_KCONF_BOOL(enable_live_dragon_bid_handler);  // [liqikun] 行业直播接入 rta 动态出价队列精简
DECLARE_SPDM_ABTEST_BOOL(enable_ios_inspire_image_set_filter_exp);  // [liqikun] IOS 下载以及拉活屏蔽图集广告
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_quick_app_filter_exp);  // [chenxin21] 屏蔽快应用广告
DECLARE_SPDM_ABTEST_BOOL(enable_ios_wanhe_for_inner);  // [liqikun] 万合「内循环短视频引流」放开
DECLARE_SPDM_KCONF_BOOL(enable_clear_unlogin_user_id);  // [liqikun] 切换 Uid 实验
DECLARE_SPDM_ABTEST_BOOL(enable_simplify_fanstop_context);  // [liqikun] 治理 FansTopSessionData
DECLARE_SPDM_ABTEST_BOOL(enable_soft_freq_context_migrate);  // [liqikun] 治理 enable_soft_freq_context_migrate // NOLINT
DECLARE_SPDM_KCONF_STRING(adx_spu_redis_name);  //  [liqikun] adx_spu_redis redis 名
DECLARE_SPDM_KCONF_STRING(adx_spu_table_name);  //  [liqikun] adx_spu_redis 倒排名
DECLARE_SPDM_KCONF_INT32(adx_spu_timeout);  // [liqikun] adx_spu_redis redis 超时
DECLARE_SPDM_KCONF_INT32(adx_spu_creative_quota);  // [liqikun] adx_spu_redis 队列填充的 quota
DECLARE_SPDM_KCONF_INT32(adx_spu_token_quota);   // [liqikun] adx_spu_redis token 的 quota
DECLARE_SPDM_ABTEST_BOOL(enable_follow_page_daitou_filter);  // [liqikun] 内循环关注页过滤代投广告
DECLARE_SPDM_ABTEST_BOOL(enable_wechat_rta_origin_id);  // [liqikun] 腾讯 rta 支持 origin id
DECLARE_SPDM_ABTEST_BOOL(enable_q3_hc_crm_center);  // [yishijie] hc 新增专项
DECLARE_SPDM_ABTEST_BOOL(enable_meituan_rta);  // [liqikun] 广告队列复制逻辑下移实验
DECLARE_SPDM_ABTEST_BOOL(enable_wechat_rta_auth_origin_id);  // [liqikun] 腾讯 rta 支持 origin 明滤
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_filter_shorter_than_1S);  // [liqikun] 激励过滤时长小于 1S 的广告
DECLARE_SPDM_ABTEST_BOOL(disable_ue_strategy);  // [liqikun] 实验下掉 ue 相关逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_for_money_main_rule_filter);  // [liqikun] 激励赚钱页广告流量接入
DECLARE_SPDM_ABTEST_BOOL(enable_micro_game_ios_filter);  // [liqikun] 快小游插屏支持 ios 端
DECLARE_SPDM_ABTEST_BOOL(enable_micro_app_long_video_filter);  // [liqikun] 快手小程序插屏广告物料时长限制
DECLARE_SPDM_ABTEST_BOOL(enable_unified_risk_filter);  // [liqikun] 统一风控 ab 开关
DECLARE_SPDM_ABTEST_BOOL(enable_sample_base_exp);  // [liqikun] 采样 base 实验开关

DECLARE_SPDM_ABTEST_BOOL(enable_outer_fiction_target_filter);  // [jiayalong] 外循环闭环小说过滤
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_low_active_ac_mark_update);  // [jiayalong] 外循环低活 AC 逻辑调整
DECLARE_SPDM_ABTEST_INT64(outerloop_low_active_ac_thresh);  // [jiayalong] 外循环低活 AC 转化频率阈值

DECLARE_SPDM_KCONF_BOOL(enable_new_fields_from_forward_diff);  // [liqikun] 粗排返回字段替换 diff 开关
DECLARE_SPDM_KCONF_BOOL(enable_inspire_dark_budget_product_filter);  // [liqikun] 激励暗投：产品名 * pos_id 控比 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_dark_budget_product_filter_exp);  // [liqikun] 激励暗投：产品名 * pos_id 控比，实验 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_dark_budget_control_filter);  // [liqikun] 喜番暗投预算控比;
DECLARE_SPDM_ABTEST_BOOL(enable_charge_tag_switch_to_5);  // [liqikun] 激励暗投控比切换 charge_tag 实验;
DECLARE_SPDM_ABTEST_BOOL(enable_nagtive_tag_blacklist);  // [liqikun] 支持风控部分 tag 下线实验;
DECLARE_SPDM_ABTEST_BOOL(enable_nagtive_tag_blacklist_1);  // [liqikun] 支持风控部分 tag 下线实验 1;
DECLARE_SPDM_ABTEST_BOOL(enable_nagtive_tag_blacklist_2);  // [liqikun] 支持风控部分 tag 下线实验 2;
DECLARE_SPDM_ABTEST_STRING(unified_risk_ab_group);  // [liqikun] 统一风控切新实验框架
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_random_drop);  // [liqikun] 激励直播快速进人打压
DECLARE_SPDM_ABTEST_BOOL(enable_auto_budget_control);  // [liqikun] 激励暗投智能控比
DECLARE_SPDM_ABTEST_BOOL(enable_auto_budget_control_outer);  // [liqikun] 激励暗投智能控比外循环


DECLARE_SPDM_ABTEST_BOOL(enable_inspire_zero_filter);
DECLARE_SPDM_ABTEST_BOOL(delete_prerank_fields_exp);

DECLARE_SPDM_ABTEST_BOOL(enable_storewide_gmv_level_to_prerank_trans);  // [fukunyang] L 层级字段透传到 prerank // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_rel_type_to_prerank_trans);  // [fukunyang] 多素材追投字段透传 prerank // NOLINT

// [shoulifu03] 买首直播统一进软广队列
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_live_soft_queue);
// [yangxibo] 开屏屏蔽行业实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_splash_shield_industry);
DECLARE_SPDM_ABTEST_BOOL(enable_filter_splash_outer_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_filter_splash_inner_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_filter_recall_path);

DECLARE_SPDM_ABTEST_BOOL(enable_reward_outer_ranking_quota);  // [jiangfeng06] 激励外循环粗排单独 quota
DECLARE_SPDM_ABTEST_BOOL(enable_outer_high_cost_skip_diversity);   // [guojiangwei] 外循环素材跑量跳过多样性
DECLARE_SPDM_ABTEST_BOOL(enable_diversity1_unify);             // [liuzhiqiang08] 多样性 1 软硬广一起进行
DECLARE_SPDM_ABTEST_BOOL(enable_diversity2_sdpa_wo_product);  // [liuzhiqiang08]
DECLARE_SPDM_ABTEST_BOOL(enable_diversity2_sdpa_city_id);  // [liuzhiqiang08]
DECLARE_SPDM_ABTEST_BOOL(enable_diversity2_sdpa_city_id_all);  // [liuzhiqiang08]
DECLARE_SPDM_ABTEST_STRING(diversity2_sdpa_city_id_industry2_list);  // [liuzhiqiang08]

DECLARE_SPDM_ABTEST_BOOL(enable_mock_crm_center);

// [jianghao07] photo_id to reco
DECLARE_SPDM_ABTEST_BOOL(enable_pass_photo_id_to_adprerank);

// [jianghao07] Adjust frequency
DECLARE_SPDM_ABTEST_BOOL(enable_fans_adjust_frequency);

// [shishaoyun] fix fanstop_truncate_size 0.3 exp
DECLARE_SPDM_ABTEST_BOOL(enable_fix_fanstop_truncate_size);
// ---------------------- abtest int64 参数声明 -------------------
DECLARE_SPDM_ABTEST_INT64(dup_cost_filter_thresh);   //  [wanghongfei] 内循环召回原创视频消耗过滤
DECLARE_SPDM_ABTEST_INT64(amd_originality_photo_thresh);   //  [wanghongfei] 内循环召回原创视频过滤
DECLARE_SPDM_ABTEST_INT64(ecology_merchant_cv_thresh);   // [wanghongfei] 内循环生态商品转化过滤
DECLARE_SPDM_ABTEST_INT64(archimedes_target_retrieval_normal_feed_quota);  // [mateng05] 内粉服务双列召回 quota // NOLINT
DECLARE_SPDM_ABTEST_INT64(prerank_ecom_brand_baosong_num);   // [daidongyang] 粗排品牌电商保送个数
DECLARE_SPDM_ABTEST_INT64(merchant_prerank_ecom_brand_tag);   // [daidongyang] 品牌电商 tag 值
DECLARE_SPDM_ABTEST_INT64(non_live_brand_append_quota);    // [daidongyang] 召回进粗排品牌电商增加 quota 值
DECLARE_SPDM_ABTEST_INT64(boyle_group_test_tag);    // [chengyuxuan] boyle group tag 参数
DECLARE_SPDM_ABTEST_INT64(live_prerank_max_quota);   // [guoyuan03] 直播粗排精简，quota
DECLARE_SPDM_ABTEST_INT64(inspire_live_prerank_max_quota);   // [guoyuan03] 直播粗排精简，quota
DECLARE_SPDM_ABTEST_INT64(inspire_merchant_base_line_quota);   // [liubing05] 激励电独立 quota
DECLARE_SPDM_ABTEST_INT64(splash_inner_prerank_quota);  // [liubing05] 开屏内循环 quota
DECLARE_SPDM_ABTEST_INT64(session_cache_ttl);  // [sunkang] 粗排缓存过期时间，单位：秒
DECLARE_SPDM_ABTEST_INT64(session_cache_quota);  // [sunkang] 缓存广告 quota
DECLARE_SPDM_ABTEST_INT64(outer_photo_hard_cache_quota);  // [sunkang] 外循环视频硬广缓存 quota
DECLARE_SPDM_ABTEST_INT64(outer_photo_soft_cache_quota);  // [sunkang] 外循环视频软广缓存 quota
DECLARE_SPDM_ABTEST_INT64(inner_photo_hard_cache_quota);  // [sunkang] 内循环视频硬广缓存 quota
DECLARE_SPDM_ABTEST_INT64(inner_p2l_hard_cache_quota);  // [sunkang] 内循环 p2l 硬广缓存 quota
DECLARE_SPDM_ABTEST_INT64(inner_photo_soft_cache_quota);  // [sunkang] 内循环视频软广缓存 quota
DECLARE_SPDM_ABTEST_INT64(inner_p2l_soft_cache_quota);  // [sunkang] 内循环 p2l 软广缓存 quota
DECLARE_SPDM_ABTEST_INT64(cache_admit_quota);  // [sunkang] 缓存准入 quota 控制
DECLARE_SPDM_ABTEST_INT64(cache_exit_quota);  // [sunkang] 缓存退场 quota 控制
DECLARE_SPDM_ABTEST_INT64(rank_cache_exit_quota);  // [huliren] 精排缓存退场 quota 控制
DECLARE_SPDM_ABTEST_INT64(reward_outer_ranking_quota);  // [jiangfeng06] 激励外循环粗排单独 quota  // NOLINT
DECLARE_SPDM_ABTEST_INT64(cache_outer_extra_ranking_quota);  // [sunkang] 命中缓存流量 外循环额外粗排入口 quota  // NOLINT
DECLARE_SPDM_ABTEST_INT64(cache_inner_hard_p2l_extra_ranking_quota);  // [sunkang] 命中缓存流量 内循环硬广告 p2l 额外粗排入口 quota  // NOLINT
DECLARE_SPDM_ABTEST_INT64(cache_inner_hard_photo_extra_ranking_quota);  // [sunkang] 命中缓存流量 内循环硬广告 photo 额外粗排入口 quota  // NOLINT
DECLARE_SPDM_ABTEST_INT64(session_cache_target_type);  // [sunkang] 粗排缓存 服务生效控制
DECLARE_SPDM_ABTEST_INT64(live_target_ecpm_max_quota);  //  [fangmeiling] 金牛直播 live target 一段式召回 ecpm 队列 quota // NOLINT
DECLARE_SPDM_ABTEST_INT64(live_target_delivery_max_quota);  // [fangmeiling] 金牛直播 live target 一段式召回 delivery 队列 quota // NOLINT
DECLARE_SPDM_ABTEST_INT64(native_ad_photo_delivery_min);    // [mengfangyuan] 软广photo下发频控间隔 // NOLINT
DECLARE_SPDM_ABTEST_INT64(spec_ad_photo_delivery_min);    // [mengfangyuan] 下发频控间隔 for 618 // NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_native_ad_photo_delivery_min);    // [gaozepeng] 激励软广photo下发频控间隔 // NOLINT
DECLARE_SPDM_ABTEST_INT64(soft_ad_spu_purchase_hour);    // [mengfangyuan] 软广购买后spu频控间隔 // NOLINT
DECLARE_SPDM_ABTEST_INT64(fanstop_whitelist_freq_interval_min);    // [tengwei] 粉条白名单频控间隔 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_follow_page_ad_creative_path);       // [panshunda] 关注页走 creative 召回 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_follow_page_ad_author);       // [panshunda] 关注页切换关注数据源 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_70_path_reco_follow_set);       // [panshunda] 70 通路切换关注数据源 // NOLINT
DECLARE_SPDM_ABTEST_INT64(live_auhtor_max_prerank_num_delivery);  //  [fangmeiling] 金牛直播 live target 一段式召回 delivery 队列 author 下最大创意数量 // NOLINT
DECLARE_SPDM_ABTEST_INT64(non_live_p2l_append_quota);    // [jiangfeng06] p2l 迁移召回进粗排单独 quota 值
DECLARE_SPDM_ABTEST_INT64(non_live_p2l_append_feed_quota);    // [jiangfeng06] feed 页 p2l 迁移召回进粗排单独 quota 值 // NOLINT
DECLARE_SPDM_ABTEST_INT64(non_live_p2l_append_reward_quota);    // [jiangfeng06] reward p2l 迁移召回进粗排单独 quota 值 // NOLINT
DECLARE_SPDM_ABTEST_INT64(non_live_amd_reward_quota);    // [jiangfeng06] reward amd 召回进粗排单独 quota 值 // NOLINT
DECLARE_SPDM_ABTEST_INT64(fanstop_live_coldstart_tag_tolerance_minutes);    // [caolin]  粉条直播新创意冷启动标签获取延迟最长时间 // NOLINT
DECLARE_SPDM_ABTEST_INT64(InspireMerchant_prerank_max_quota);    // [guoyuan03]  激励电商粗排 quota // NOLINT
DECLARE_SPDM_ABTEST_INT64(mobile_hard_photo_guaranteed_preranking_tag);   // [huangzhaokai] 移动端硬广短视频扶持通路标签 // NOLINT
DECLARE_SPDM_ABTEST_INT64(mobile_hard_p2l_guaranteed_preranking_tag);   // [huangzhaokai] 移动端硬广引流扶持通路标签 // NOLINT
DECLARE_SPDM_ABTEST_INT64(hard_photo_target_p2l_mobile_independent_quota);   // [huangzhaokai] 移动端硬广引流粗排 quota // NOLINT
DECLARE_SPDM_ABTEST_INT64(hard_photo_target_photo_mobile_quota);   // [huangzhaokai] 移动端硬广短视频粗排 quota // NOLINT
DECLARE_SPDM_ABTEST_INT64(outer_soft_max_ad_count_v2);  // [zhangmengxin] 外循环软广展开 quota v2
DECLARE_SPDM_ABTEST_INT64(outer_soft_rank_num_v2);  // [zhangmengxin] 外循环软广粗排 quota v2
DECLARE_SPDM_ABTEST_INT64(outer_soft_rank_base_line_num_v2);  // [zhangmengxin] 外循环软广精排 quota v2
DECLARE_SPDM_ABTEST_INT64(new_inner_fanstop_rule_filter_quota);   // [mayanbin] 新内粉 quota 限制
// [yangfukang03] 优先粉条 quota 限制
DECLARE_SPDM_ABTEST_INT64(new_inner_fanstop_rule_filter_priority_quota);
DECLARE_SPDM_ABTEST_INT64(new_inner_fanstop_padding_quota);   // [mayanbin] 新内粉 quota 填充限制
// ---------------------- abtest double 参数声明 -------------------
DECLARE_SPDM_ABTEST_DOUBLE(high_quality_photo_adjust);  // [liming11] 优质素材 bid 调价系数
DECLARE_SPDM_ABTEST_DOUBLE(univ_prerank_live_follow_ratio);  // [gaowei03]  粗排直播涨粉预估调整
DECLARE_SPDM_ABTEST_DOUBLE(universe_live_follow_prerank_ecpm_ratio);  // [gaowei03] 粗排直播涨粉后验 ecpm 调整
DECLARE_SPDM_ABTEST_DOUBLE(live_paid_rate_default);  // [gaowei03] 粗排直播默认付费率
DECLARE_SPDM_ABTEST_DOUBLE(live_ctr_default);  // [gaowei03] 粗排直播默认 ctr
DECLARE_SPDM_ABTEST_DOUBLE(live_ltv_default);  // [gaowei03] 粗排直播默认 ltv
DECLARE_SPDM_ABTEST_DOUBLE(live_merchant_follow_default);  // [gaowei03] 粗排直播默认
DECLARE_SPDM_ABTEST_DOUBLE(prerank_live_ensemble_ecpm_weight);    // [guoyuan03] 粗排精简--weight
DECLARE_SPDM_ABTEST_DOUBLE(prerank_live_ensemble_e2e_weight);    // [guoyuan03] 粗排精简--weight
DECLARE_SPDM_ABTEST_DOUBLE(prerank_pc_live_ctcvr_factor);    // [wubo05] 粗排金牛 PC 直播 CTCVR 加权因子
DECLARE_SPDM_ABTEST_DOUBLE(prerank_roi_live_ctcvr_factor);    // [wubo05] 粗排金牛 PC 直播 CTCVR 加权因子
DECLARE_SPDM_ABTEST_DOUBLE(prerank_pc_roas_live_ctcvr_factor);    // [wubo05] 粗排金牛 PC 直播 CTCVR 加权因子
DECLARE_SPDM_ABTEST_DOUBLE(prerank_mobile_live_ctcvr_factor);    // [wubo05] 粗排金牛移动直播 CTCVR 加权因子
DECLARE_SPDM_ABTEST_INT64(prerank_roas_live_ctcvr_factor);    // [wubo05] 粗排直播 ROAS 加权因子
DECLARE_SPDM_ABTEST_INT64(inspire_merchant_min_live_delivery_interval_seconds);    // [liubing05] 激励电商直播广告间隔时间  // NOLINT
DECLARE_SPDM_ABTEST_INT64(inspire_merchant_min_ad_size_for_live_ad);    // [liubing05] 激励电商直播广告间隔最小广告数量  // NOLINT
DECLARE_SPDM_ABTEST_INT64(download_ad_disk_free_threshold);  // [zhangruyuan] 下载类广告要求内存大小阈值

DECLARE_SPDM_ABTEST_INT64(amd_target_biz_quota);  //  amd target biz_quota 控制参数 // NOLINT
DECLARE_SPDM_ABTEST_INT64(fanstop_target_biz_quota);  //  fanstop target biz_quota 控制参数 // NOLINT
DECLARE_SPDM_ABTEST_STRING(amd_target_zigzag_ratio_vec);  // amd target zigzag ratio vec
DECLARE_SPDM_ABTEST_STRING(fanstop_target_zigzag_ratio_vec);  // fanstop target zigzag ratio vec

// [wubo05] 直播移动版粗排精简--weight
DECLARE_SPDM_ABTEST_DOUBLE(prerank_mobile_live_ensemble_ecpm_weight);
// [wubo05] 直播移动版粗排精简--weight
DECLARE_SPDM_ABTEST_DOUBLE(prerank_mobile_live_ensemble_e2e_weight);
DECLARE_SPDM_ABTEST_BOOL(enbale_prerank_live_idx_fix);    // [wubo05] 粗排直播独立 index
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_live_startegy_live_ocpx);    // [wubo05] 粗排直播多样性控制
// [wubo05] 粗排直播两段式多样性控制
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_live_startegy_ocpx_two_step_pc);
DECLARE_SPDM_ABTEST_INT64(live_ocpx_pc_thresh);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_live_separate_billing);    // [wubo05] 粗排直播计费分离
DECLARE_SPDM_ABTEST_DOUBLE(prerank_live_bid_price_ratio);    // [wubo05] 粗排直播计费分离
DECLARE_SPDM_ABTEST_DOUBLE(prerank_live_auto_bid_weight);    // [wubo05] 粗排直播计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_live_prerank_nobid_exp);    // [wubo05] 直播粗排 NOBID 跳过计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_live_prerank_mobile_bid_exp);    // [wubo05] 直播移动版粗排调价分转厘实验
// PC 直播粉丝召回保送策略
DECLARE_SPDM_ABTEST_BOOL(enable_pc_live_follow_retrieval_strategy);
DECLARE_SPDM_ABTEST_INT64(pc_follow_retrieval_max_num);
DECLARE_SPDM_ABTEST_BOOL(enable_pc_live_prerank_add_bonus);
// 移动直播添加 ECPM 粗排
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_live_prerank_add_ecpm);
// [liuchangjin] 粗排排序计费完全分离计费比系数
DECLARE_SPDM_ABTEST_DOUBLE(preranking_thorough_bid_price_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(default_billing_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(billing_saparate_owe_ratio_thrd);
DECLARE_SPDM_ABTEST_DOUBLE(bs_owe_control_begin);
DECLARE_SPDM_ABTEST_DOUBLE(bs_max_weight);
DECLARE_SPDM_ABTEST_DOUBLE(auto_bid_weight);

// [tiangeng] 七日出价系列开关
DECLARE_SPDM_ABTEST_BOOL(enable_bidding_7days_pay_times_prerank);
DECLARE_SPDM_ABTEST_DOUBLE(bidding_7days_pay_times_prerank_ratio);
DECLARE_SPDM_ABTEST_BOOL(skip_auto_target_pure_roaring);

DECLARE_SPDM_ABTEST_STRING(dcaf_rank_version_cache);  // [sunkang] 动态缓存版本控制
DECLARE_SPDM_ABTEST_STRING(ecom_seller_adjust_exp_name_adrank);  // [wanghongfei] 商家生态实验名称
DECLARE_SPDM_ABTEST_STRING(nearby_zigzag_expand);  // [xuyanyan03] 客增 SE 同城 zigzag_expand 系数
// [shoulifu03] 买首直播召回以及粗排 gpm 因子系数
DECLARE_SPDM_ABTEST_DOUBLE(merchant_live_one_step_gpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(non_order_type_default_gpm);

// ---------------------- 内粉专用 参数声明区域（防 git 冲突） -------------------
// ---------------------- abtest bool 参数声明 -------------------
// [nanning]
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_inspirelive_flashbomb_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_little_live_order_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_live_little_order_fc);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_photo_reco_prerank);  // 请求 reco 粗排
// [luoqiang] 内粉本地生活订单确保按预期召回
DECLARE_SPDM_ABTEST_BOOL(enable_inner_polaris_order_pass);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_region_skip_filter_exp);
// [litianshi03] 内粉粗排 quota 独立设置实验
DECLARE_SPDM_ABTEST_BOOL(enable_archimedes_prerank_quota_exp);
// [litianshi03] 内粉 CPM 优先拿量通路
DECLARE_SPDM_ABTEST_BOOL(enable_inner_cpm_quota_exp);
// [litianshi03] 新粗排逻辑中优先订单判断
DECLARE_SPDM_ABTEST_BOOL(archimedes_new_prerank_priority_check);
// [litianshi03] 内粉过滤 p2l 暗投直播订单
DECLARE_SPDM_ABTEST_BOOL(archimedes_p2l_live_filter);
// [mateng05] 内粉召回过滤 (流控相关) 关闭实验
DECLARE_SPDM_ABTEST_BOOL(archimedes_disable_fanstop_fc_filter);
DECLARE_SPDM_ABTEST_BOOL(archimedes_disable_live_spam_filter);
DECLARE_SPDM_ABTEST_BOOL(archimedes_disable_live_bad_cpm_filter);
DECLARE_SPDM_ABTEST_BOOL(archimedes_disable_little_anchor_reduce);
DECLARE_SPDM_ABTEST_BOOL(archimedes_enable_new_flow_control);
DECLARE_SPDM_ABTEST_BOOL(archimedes_pacing_filter);

// [cuiyanliang] 金牛移动端投硬广开关
DECLARE_SPDM_ABTEST_BOOL(enable_esp_mobile_placing_hard_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_esp_mobile_hard_photo_admit_ocpx);
DECLARE_SPDM_ABTEST_BOOL(enable_esp_mobile_hard_p2l_admit_ocpx);
DECLARE_SPDM_ABTEST_BOOL(enable_kg_freq_rule);  // [wanghongfei] 短视频已购频控升级
DECLARE_SPDM_ABTEST_BOOL(enable_v3_freq_rule);  // [wanghongfei] 短视频已购频控升级
DECLARE_SPDM_ABTEST_BOOL(enable_kgfix_freq_rule);  // [wanghongfei] 短视频已购频控升级
DECLARE_SPDM_ABTEST_BOOL(enable_source_freq_rule);  // [wanghongfei] 短视频已购频控升级
DECLARE_SPDM_ABTEST_BOOL(enable_category_live_freq_rule);  // [luwei] 直播已购叶子类目频控
DECLARE_SPDM_ABTEST_BOOL(enable_category_freq_rule_v2);  // [panshunda] 叶子类目已购频控
DECLARE_SPDM_ABTEST_BOOL(enable_category_photo_freq_rule);  // [luwei] 短视频已购叶子类目频控
DECLARE_SPDM_ABTEST_INT64(category_purchased_live_interval);  // [luwei] 直播已购叶子类目频控周期
DECLARE_SPDM_ABTEST_INT64(category_purchased_interval_v2);  // [panshunda] 已购叶子类目频控周期
DECLARE_SPDM_ABTEST_INT64(category_purchased_photo_interval);  // [luwei] 短视频已购叶子类目频控周期
DECLARE_SPDM_ABTEST_BOOL(disable_other_purchase_freq_rule);  // [luwei] 关闭非叶子类目已购频控
DECLARE_SPDM_ABTEST_BOOL(purchase_freq_rule_change_spu_source);  // [panshunda] 已购频控切换 spu 来源
DECLARE_SPDM_ABTEST_BOOL(enable_customer_purchase_freq_check);  // [luwei] 叶子类目频控周期
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_merchant_category_freq_rule);  // [zengjiangwe03] 货架电商叶子类目已购频控  // NOLINT
DECLARE_SPDM_ABTEST_INT64(shelf_merchant_category_freq_interval)  // [zengjiangwei03] 货架电商叶子类目已购频控周期  // NOLINT

// [qiyifan] 粉条 item&bid_type&ocpx 重复过滤 迁移到召回
DECLARE_SPDM_ABTEST_BOOL(enable_universe_dark_skip_celebrity_label);  // [wanglei10] 联盟跳过暗投网红定向

DECLARE_SPDM_ABTEST_BOOL(is_universe_inner_nobid_discount);    //   [gaowei03]  联盟内循环 nobid 打折
DECLARE_SPDM_ABTEST_BOOL(enable_universe_relevance_merge);    // [niejinlong]  联盟小系统搜索场景合并策略：相关性优先  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_query_retarget_merge);   // [liangli]  联盟小系统搜索场景合并策略：相关性优先  // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_wechat_game_tmp_trans);     // [guoqi03] 小游戏临时爬坡实验
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_fanstop_live_flash_bomb);  // [mateng05] 粉条直播闪光弹粗排扶持
// ---------------------- abtest int64 参数声明 -------------------
DECLARE_SPDM_ABTEST_INT64(select_high_quality_quota);  // [caining] 素材扶优 quota 召回
DECLARE_SPDM_ABTEST_INT64(inner_fanstop_mutil_recall_reason);  // [luoqiang] 内粉多路召回 reason
DECLARE_SPDM_ABTEST_INT64(purchased_freq_rule_interval);  // [wanghongfei] 短视频已购频控升级
// [nanning]
DECLARE_SPDM_ABTEST_INT64(inner_request_reco_prerank_tm);  // 请求 reco 粗排超时时间
// [guoyuan03]  直播粗排精简 -- thresh
DECLARE_SPDM_ABTEST_INT64(prerank_live_diversity_unit_thresh);
DECLARE_SPDM_ABTEST_INT64(prerank_live_diversity_account_thresh);
DECLARE_SPDM_ABTEST_INT64(prerank_live_diversity_creative_thresh);
DECLARE_SPDM_ABTEST_INT64(prerank_live_diversity_live_id_thresh);
DECLARE_SPDM_ABTEST_INT64(fanstop_rank_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_dup_new);  // [wanghongfei] 升级 dup photo
DECLARE_SPDM_ABTEST_BOOL(enable_coldboot_new);  // [wanghongfei] 升级 cold boot
DECLARE_SPDM_ABTEST_BOOL(enable_coldboot_new_dragon);  // [wanghongfei] 升级 cold boot
DECLARE_SPDM_ABTEST_INT64(prerank_live_author_fans_baosong_num);   // [huangzhaokai] 直播粗排粉丝保送数
DECLARE_SPDM_ABTEST_INT64(hard_photo_target_non_live_p2l_inspire_live_quota);   // [huangzhaokai] 激励直播 p2l 独立 quota // NOLINT
DECLARE_SPDM_ABTEST_INT64(fanstop_photo_prerank_num);   // [huangzhaokai] 软广作品粗排数
DECLARE_SPDM_ABTEST_INT64(amd_corporation_filter_thresh);   //  [wanghongfei] 内循环召回原创过滤阈值
DECLARE_SPDM_ABTEST_INT64(live_tag_negative_thre);   // [mengfangyuan] 直播直投负反馈泛化过滤阈值
DECLARE_SPDM_ABTEST_INT64(live_tag_negative_check_day);
DECLARE_SPDM_ABTEST_INT64(live_tag_negative_ban_day);
// [litianshi03] 内粉本地生活作品 quota
DECLARE_SPDM_ABTEST_INT64(inner_trunc_target_max_region_photo);
// [litianshi03] 内粉粗排作品 quota
DECLARE_SPDM_ABTEST_INT64(inner_trunc_target_max_rank_photo);
// [litianshi03] 内粉粗排直播 quota
DECLARE_SPDM_ABTEST_INT64(inner_trunc_target_max_rank_live);
// [dengchijun] 主站发现页双列低观感治理
DECLARE_SPDM_ABTEST_INT64(ue_feed_explore_tag);
DECLARE_SPDM_ABTEST_INT64(ue_feed_inner_explore_tag);
// [liaibao] 内外循环软广观感低质过滤 100% 策略 tag
DECLARE_SPDM_ABTEST_INT64(ue_feed_softad_impress_tag);
// [liaibao] 特批素材放行评估实验
DECLARE_SPDM_ABTEST_INT64(ue_pass_filter_exp_group);
DECLARE_SPDM_ABTEST_BOOL(enable_ue_specialpass_filter);
// [liaibao] 分级分发流量控制
DECLARE_SPDM_ABTEST_BOOL(enable_adue_risklevel_dispense);
DECLARE_SPDM_ABTEST_BOOL(enable_adue_risklevel_risklabel_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_adue_risklevel_base_filter);
DECLARE_SPDM_ABTEST_INT64(adue_risklevel_dispense_exp_group);
DECLARE_SPDM_ABTEST_INT64(adue_risklevel_kuaishou_limittag);
DECLARE_SPDM_ABTEST_INT64(adue_risklevel_nebula_limittag);
// [liaibao] 分级分发评估实验 1.0
DECLARE_SPDM_ABTEST_BOOL(enable_adue_risklevel_filter);
DECLARE_SPDM_ABTEST_INT64(adue_risklevel_first_industryid);
DECLARE_SPDM_ABTEST_INT64(adue_risklevel_filter_levelthr);
DECLARE_SPDM_ABTEST_INT64(adue_risklevel_filter_exp_group);
// [liaibao] 分级分发风险评估实验 3.0
DECLARE_SPDM_ABTEST_BOOL(enable_adue_risklevel_evaluate);
DECLARE_SPDM_ABTEST_BOOL(enable_adue_risklevel_eval_filter_skip);
// [liaibao] 规则引擎过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ue_ruleengine_filter);
DECLARE_SPDM_ABTEST_INT64(adue_ruleengine_filter_exp_group);
// 单账户过滤策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_ue_single_account_filter);
DECLARE_SPDM_ABTEST_INT64(single_account_filter_exp_group);
// [dengchijun] 高举报账户过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ue_risk_account_filter);
DECLARE_SPDM_ABTEST_INT64(flow_grading_report_filter_exp_group);
DECLARE_SPDM_ABTEST_BOOL(enable_ue_sensitive_user_filter);
// 分级分发举报敏感用户过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ue_grading_report_user_filter);
// 举报敏感用户 redis
DECLARE_SPDM_ABTEST_STRING(user_report_prob_prefix);
// 用户价值标签 redis 前缀
DECLARE_SPDM_ABTEST_STRING(user_value_group_tag_prefix);
// 用户价值标签 redis 读取开关
DECLARE_SPDM_ABTEST_BOOL(enable_user_value_group_tag_redis);
// 用户价值体验保护策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_user_value_riskphoto_filter);
DECLARE_SPDM_ABTEST_INT64(user_value_exp_group);

// [dengchijun] 单列场景低观感软广过滤
DECLARE_SPDM_ABTEST_INT64(ue_thanos_soft_tag);
// [dengchijun] 低质过滤豁免
DECLARE_SPDM_ABTEST_INT64(ue_limit_free_tag);
// [litianshi03] 内粉已有 cpm 订单优先透出 reason
DECLARE_SPDM_ABTEST_INT64(inner_cpm_priority_reason);
DECLARE_SPDM_ABTEST_INT64(universe_retarget_retrieval_author_num);  //  [gaowei03]  联盟重定向选取的作者数
DECLARE_SPDM_ABTEST_INT64(universe_retarget_retrieval_creative_num);   //  [gaowei03]  联盟重定向选取的创意数
// [mateng05] 内粉粗排结果数 quota
DECLARE_SPDM_ABTEST_INT64(archimedes_photo_to_rank_quota);
DECLARE_SPDM_ABTEST_INT64(archimedes_live_to_rank_quota);
DECLARE_SPDM_ABTEST_INT64(archimedes_priority_photo_to_rank_quota);
DECLARE_SPDM_ABTEST_INT64(archimedes_region_photo_to_rank_quota);
DECLARE_SPDM_ABTEST_INT64(archimedes_general_priority_photo_to_rank_quota);
DECLARE_SPDM_ABTEST_INT64(innerloop_follow_data_time_window);  //  [shoulifu03]  涨粉数据时间窗口
// ---------------------- abtest double 参数声明 -------------------
// [nanning]
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_inspirelive_flashbomb_filter_param);
// [litianshi03] 内粉北极星订单占粗排 quota 比例
DECLARE_SPDM_ABTEST_DOUBLE(inner_trunc_target_max_polaris_ratio_photo);
// [litianshi03] 内粉扶新弱作品作品占粗排 quota 比例
DECLARE_SPDM_ABTEST_DOUBLE(inner_bad_quality_ratio_photo);
// [litianshi03] 内粉 CPM 优先拿量通路占粗排 quota 比例
DECLARE_SPDM_ABTEST_DOUBLE(inner_cpm_priority_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_nobid_roas_discount);
//  [gaowei03]  联盟内循环 nobid roas 折扣
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_nobid_cpa_discount);
//  [gaowei03]  联盟内循环 nobid cpa 折扣

// ---------------------- abtest string 参数声明 -------------------
// [litianshi03] 跳过内粉 ecpm 过滤的组织 id
DECLARE_SPDM_ABTEST_STRING(inner_skip_ecpm_wtr_filter_orgid_exp);
// [liuzhaocheng] 粗排帕累托度+偏好两级排序开关
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_epsp_sort);
// [liuzhaocheng] 粗排帕累托度+偏好两级排序最大截断数 (综合进精排个数和效率考虑)
DECLARE_SPDM_ABTEST_INT64(prerank_epsp_trunc_max_num);
// [liuzhaocheng] 粗排帕累托度-三通路信任度
DECLARE_SPDM_ABTEST_DOUBLE(prerank_epsp_cpm_confidence);
DECLARE_SPDM_ABTEST_DOUBLE(prerank_epsp_e2e_confidence);
DECLARE_SPDM_ABTEST_DOUBLE(prerank_epsp_ltr_confidence);
// [liuzhaocheng] 粗排帕累托度-细粒度偏好是否使用 ensemble sort
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_epsp_ensemble_preference);
DECLARE_SPDM_ABTEST_STRING(esp_mobile_hard_p2l_admit_ocpx_ab_string);    // [huangzhaokai] 移动端硬广引流投放目标准入  // NOLINT
DECLARE_SPDM_ABTEST_STRING(esp_mobile_hard_photo_admit_ocpx_ab_string);  // [huangzhaokai] 移动端硬广短视频投放目标准入  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ecpm_secondary_sort);  // [zhangjunmin] 粗排 ECPM 二次排序
DECLARE_SPDM_ABTEST_STRING(prerank_ecpm_secondary_sort_exp_name);  // [zhangjunmin] 粗排 ECPM 二次排序实验名
DECLARE_SPDM_ABTEST_STRING(useless_path_ab_tag);  // [heqian] 无用通路清理实验 tag
DECLARE_SPDM_ABTEST_STRING(first_industry_block_exp_tag);  // [shoulfu03] 一级行业屏蔽实验组
DECLARE_SPDM_ABTEST_STRING(second_industry_block_exp_tag);  // [rentingyu] 二级行业屏蔽实验组
DECLARE_SPDM_ABTEST_STRING(second_industry_block_exp_tag_v2);  // [guanpingyin] 二级行业屏蔽实验组
DECLARE_SPDM_ABTEST_STRING(block_kgame_sub_class_tag);  // [jiangpeng07] 快小游子类屏蔽实验组
DECLARE_SPDM_ABTEST_STRING(product_name_block_exp_tag);  // [rentingyu] 产品屏蔽实验组
DECLARE_SPDM_ABTEST_STRING(skip_media_ssp_sync_creative_blacklist);  // [shanminghui] 跳过 ssp 同步的创意黑名单过滤的 uid 名单 // NOLINT
// [lishaoqi] 外循环软广分人群 quota 的实验组名称
DECLARE_SPDM_ABTEST_STRING(user_group_quota_outer_soft_exp);
// [lishaoqi] 外循环硬广分人群 quota 的实验组名称
DECLARE_SPDM_ABTEST_STRING(user_group_quota_outer_hard_exp);
// [lishaoqi] 内循环硬广分人群 quota 的实验组名称
DECLARE_SPDM_ABTEST_STRING(user_group_quota_inner_hard_exp);
// [lishaoqi] 内循环硬广激励分人群 quota 的实验组名称
DECLARE_SPDM_ABTEST_STRING(user_group_quota_inner_hard_reward_exp);
// [lishaoqi] 内循环软广分人群 quota 的实验组名称
DECLARE_SPDM_ABTEST_STRING(user_group_quota_inner_soft_exp);
DECLARE_SPDM_ABTEST_BOOL(fix_live_inner_quota);  // [dingyiming05] 直播内流接广调 quota
DECLARE_SPDM_ABTEST_STRING(cpm_thr_08_exp_name);  // [dingyiming05] 缓存实验 tag
DECLARE_SPDM_ABTEST_BOOL(enable_sdpa_outer_ranking_extra_quota);  // [wuwei03] 商品分发粗排候选 quota 实验开关
DECLARE_SPDM_ABTEST_INT64(sdpa_outer_ranking_extra_quota);  // [wuwei03] 商品分发实验粗排候选增量 quota

// 智能算力分配相关参数 Intelligent Computation Allocation
DECLARE_SPDM_ABTEST_BOOL(enable_target_prerank_ica_outer);
DECLARE_SPDM_ABTEST_BOOL(enable_target_prerank_ica_inner);
DECLARE_SPDM_ABTEST_BOOL(enable_target_prerank_ica_live);
DECLARE_SPDM_ABTEST_STRING(target_prerank_ica_user_value_model_name);
DECLARE_SPDM_ABTEST_STRING(target_prerank_ica_lambda_model_name);
DECLARE_SPDM_ABTEST_STRING(target_prerank_ica_enable_page_ids_exp_name);
DECLARE_SPDM_ABTEST_STRING(target_prerank_ica_fallback_exp_name);
DECLARE_SPDM_ABTEST_STRING(target_prerank_ica_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_ica_use_queue_lambda);
// 粗排到精排的 ICA 开关
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_rank_ica_outer);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_rank_ica_inner);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_rank_ica_live);
DECLARE_SPDM_ABTEST_STRING(prerank_rank_ica_user_value_model_name);
DECLARE_SPDM_ABTEST_STRING(prerank_rank_ica_lambda_model_name);
DECLARE_SPDM_ABTEST_STRING(prerank_rank_ica_enable_page_ids_exp_name);
DECLARE_SPDM_ABTEST_STRING(prerank_rank_ica_fallback_exp_name);
DECLARE_SPDM_ABTEST_BOOL(enable_ica_y_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_ica_y_weight_sigmoid);
DECLARE_SPDM_ABTEST_BOOL(enable_ica_w_weight);
DECLARE_SPDM_ABTEST_STRING(ica_w_coef_rank);
DECLARE_SPDM_ABTEST_STRING(ica_w_coef_prerank);
DECLARE_SPDM_ABTEST_BOOL(enable_ica_y_weight_target);
DECLARE_SPDM_ABTEST_BOOL(enable_ica_fallback_kconf_page);
DECLARE_SPDM_ABTEST_BOOL(enable_ica_quota_shaping);
DECLARE_SPDM_ABTEST_DOUBLE(ica_quota_shaping_s);
DECLARE_SPDM_ABTEST_DOUBLE(ica_quota_shaping_r);
DECLARE_SPDM_ABTEST_BOOL(enable_ica_quota_shaping_conf);
DECLARE_SPDM_ABTEST_STRING(ica_quota_shaping_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_ica_kconf_page);

// ---------------------- kconf int64 参数声明 -------------------

DECLARE_SPDM_ABTEST_BOOL(prerank_ensemble_sqrt_sort);  // [zhangjunmin] 粗排 ensemble 采用 1/sqrt 加权
DECLARE_SPDM_ABTEST_BOOL(prerank_ensemble_log_sort);  // [zhangjunmin] 粗排 ensemble 采用 1/log 加权
DECLARE_SPDM_ABTEST_BOOL(enable_universe_live_no_bid);  // [gaowei03] 联盟直播 nobid 开关

DECLARE_SPDM_ABTEST_BOOL(
    enable_prerank_purchase_conversion);  // [zhangjunmin] AD_PURCHASE_CONVERSION 请求 conv2_purchase
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff);  // [zhangjunmin] ctcvr fix bid diff
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_cpm_accuracy);  // [zhangjunmin] ctcvr fix cpm accuracy
// [wangyunli] enable 7 day pay times model in preranking
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_seven_day_pay_times);

// [wangzhiqiang03] prerank roas upgrade v202303
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_roas_v202303);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_7roas_v202303);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_first_day_v202303);

// [wangzhiqiang03] prerank upgrade v202305
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_other_v202305);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_lps_v202305);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_order_submit_v202306);

DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_conv);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_conv2ltv1);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_conv2ltv7);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_conv2next);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_conv2key);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_lps);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_pay);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_os);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_rest);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_ltv1);
DECLARE_SPDM_ABTEST_DOUBLE(pre_sc_k0_ltv7);

// [wangzhiqiang03] prerank supplier score
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_sp_sort);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_sp_bid);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_sp_ctr);
DECLARE_SPDM_ABTEST_DOUBLE(prerank_sp_weight0);
DECLARE_SPDM_ABTEST_DOUBLE(prerank_sp_min0);

// [wangzhiqiang03] prerank cvr upgrade. 202305
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_cvr_order_submit_v202305);
// [koushaowei] fill prerank real_action_score
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_real_action_score);

// //  [panjianfei03] 行业直播 rule filter 开关
// DECLARE_SPDM_ABTEST_BOOL(enable_skip_industry_live_diversity_rule);

// [zhangjunmin] ctcvr fix bid diff
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff_ad_roas);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff_ad_seven_day_roas);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff_event_7_day_pay_times);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff_event_pay_first_day);
DECLARE_SPDM_ABTEST_BOOL(enable_recall_limit_cate3_nums);  // [linwei] enable_recall_limit_cate3_nums
DECLARE_SPDM_ABTEST_BOOL(enable_recall_limit_cid_cate3_nums);
// [wucunlin] enable_recall_limit_cid_cate3_nums
DECLARE_SPDM_ABTEST_DOUBLE(live_item_addr_filter_rate);
DECLARE_SPDM_ABTEST_BOOL(enable_pass_addr_filter_follow);
DECLARE_SPDM_ABTEST_BOOL(enable_pass_addr_filter_kuaishou_explore);
DECLARE_SPDM_ABTEST_INT64(live_item_addr_filter_max);
// [yuanwei09] 生态分过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ecology_score_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_ecology_score_filter_exp);
DECLARE_SPDM_ABTEST_DOUBLE(ecology_badcomn_filter_thr);
DECLARE_SPDM_ABTEST_DOUBLE(ecology_refund_filter_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_ecology_score_white_list);
DECLARE_SPDM_ABTEST_BOOL(enable_ecology_score_item_adjust);
DECLARE_SPDM_ABTEST_INT64(ecology_score_white_list_max_size);
DECLARE_SPDM_ABTEST_DOUBLE(ecology_score_white_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(ecology_score_white_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(ecology_score_photo_adjust_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(ecology_score_live_adjust_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(ecology_score_p2l_adjust_ratio);
// [yuanwei09] 商品全站 quota 展开系数
DECLARE_SPDM_ABTEST_DOUBLE(merchant_storewide_quota_ratio);
// [tengwei]
DECLARE_SPDM_ABTEST_BOOL(enable_lsp_geo_recall_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_lsp_geo_live_ids_to_prerank_v2);
DECLARE_SPDM_ABTEST_STRING(locallife_skip_dark_inspire_cf_group_name);

// [jinhui05] 主站发现内循环直播间调频控
DECLARE_SPDM_ABTEST_BOOL(enable_kuaishou_explore_live_browsed_freq_min);
DECLARE_SPDM_ABTEST_INT64(kuaishou_explore_live_browsed_freq_min);
DECLARE_SPDM_ABTEST_INT64(inner_explore_live_browsed_freq_min);
// [zhaokun03] 内流 trgger emb 信息透传到 prerank
DECLARE_SPDM_ABTEST_BOOL(enable_fill_inner_explore_hetu_emb);
// [wuyonghong] 万合是否过滤掉无行业信息的广告
DECLARE_SPDM_ABTEST_BOOL(disable_unknown_industry_for_side_window);
DECLARE_SPDM_ABTEST_BOOL(enable_local_store_order);
// [jinhui05] 关注页过滤优化
DECLARE_SPDM_ABTEST_BOOL(enable_follow_author_freq_day_factor2);
DECLARE_SPDM_ABTEST_DOUBLE(follow_author_freq_day_factor);
// [jinhui05] 关注页 CLS
DECLARE_SPDM_ABTEST_STRING(follow_cls_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_cls_item_type_v2);
DECLARE_SPDM_ABTEST_INT64(follow_cls_item_type);
// [zhaokun03] 内循环聚合大卡二次请求过滤
DECLARE_SPDM_ABTEST_BOOL(enable_inner_bigcard_second_ads_filter);
// [zhaokun03] 万合 P 页皮肤黑名单广告过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ads_filter_for_profile_skin);
// [zhaokun03] 万合流量黑名单账户过滤
DECLARE_SPDM_ABTEST_BOOL(enable_account_filter_for_wanhe);
// [zhaokun03] 万合创作者相关广告本地召回
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_creator_class_recall);
// [zhaokun03] 万合创作者相关广告本地召回升级
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_creator_class_recall_opt);
// [zhaokun03] 万合创作者相关广告本地召回单个 key 召回数量
DECLARE_SPDM_ABTEST_INT64(wanhe_local_recall_per_key_quota);
// [zhaokun03] 创新流量召回生效范围限制
DECLARE_SPDM_ABTEST_BOOL(enable_invo_traffic_recall);
// [zhaokun03] 万合创作者行业屏蔽策略修复
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_creator_forbidden_industry_fix);
// [zhaokun03] P 页关系链过滤修复
DECLARE_SPDM_ABTEST_BOOL(enable_fix_profile_relation_link_filter);

// [zhaokun03] 万合营业执照维度过滤
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_corporation_name_filter);

DECLARE_SPDM_ABTEST_BOOL(outer_native_photo_holdout_new);  // [wuyinhao] 外循环原生素材全流量 holdout
DECLARE_SPDM_ABTEST_BOOL(outer_native_business_holdout_new);  // [wuyinhao] 外循环原生业务 holdout
DECLARE_SPDM_ABTEST_BOOL(enable_select_by_unit_cost_live);  // [linwei] enable_select_by_unit_cost_live
DECLARE_SPDM_ABTEST_INT64(industry_live_retrieval_diversity_quota);  // [zhangmengxin] 行业直播多样性 QUOTA
DECLARE_SPDM_ABTEST_INT64(outer_live_ocpx_action_limit);  // [zhangmengxin] 行业直播展开阶段 live_ocpx quota
DECLARE_SPDM_ABTEST_BOOL(enable_game_all_outer_native);  // [zhangmengxin] 游戏全软广实验
DECLARE_SPDM_ABTEST_BOOL(enable_load_game_similar_product_retarget);  // [zhangmengxin] 加载游戏相似品重定向  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_outer_native_only_soft_block);  // [zhangmengxin] 原生仅软广反向对照  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_tube_skip_outer_native_author_neg);  // [yuandepeng] 付费短剧豁免
DECLARE_SPDM_ABTEST_BOOL(enable_tube_skip_outer_native_author_neg_v2);  // [yuandepeng] 付费短剧豁免
DECLARE_SPDM_ABTEST_BOOL(enable_select_by_unit_cost);  // [linwei] enable_select_by_unit_cost
DECLARE_SPDM_ABTEST_BOOL(enable_select_by_cid_unit_cost);  // [wucunlin] enable_select_by_cid_unit_cost
DECLARE_SPDM_ABTEST_BOOL(enable_select_by_cid_unit_cost_or_item_order);
// [wucunlin] enable_select_by_cid_unit_cost_or_item_order

// [duanxinning] 透传刷次信息到 prerank
DECLARE_SPDM_ABTEST_BOOL(enable_fill_refresh_times);
DECLARE_SPDM_ABTEST_STRING(feed_triggle_exp_tag);  // [duanxinning] 发现页相关性得分打点 tag
//  [limiaochen] RTA 已安装过滤规则启用白名单
DECLARE_SPDM_ABTEST_BOOL(enable_rta_installed_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_only_7d_roi_exp);  // [linwei] enable_only_7d_roi_exp
// [wangyunli] 粗排七日付费次数扶持
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_seven_day_pay_times_support);
DECLARE_SPDM_ABTEST_DOUBLE(prerank_seven_day_pay_times_support_ratio);
// [wangyunli] prerank ensemble sort new
DECLARE_SPDM_ABTEST_DOUBLE(prerank_e2e_ensemble_weight_new);
DECLARE_SPDM_ABTEST_DOUBLE(prerank_cpm_ltr_ensemble_weight_new);
// [zhangmengxin] 外循环行业直播过滤
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_punish_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_punish_code_blacklist);
DECLARE_SPDM_ABTEST_BOOL(enable_block_outer_digital_live);
// [wangyunli] prerank ltr new
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_cpm_ltr_cmd_new);
// [shoulifu03] 业务中心屏蔽实验
DECLARE_SPDM_ABTEST_BOOL(enable_block_sale_depart_center);
DECLARE_SPDM_ABTEST_STRING(block_sale_depart_center_exp_tag);
// [wangyunli] prerank ctcvr deprecate
DECLARE_SPDM_ABTEST_BOOL(enable_deprecate_outer_prerank_ctcvr_cmd);
// [wangyunli] outer delivery sampler 相关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_delivery_sample_merge_ad_and_record_count);
DECLARE_SPDM_ABTEST_BOOL(enable_full_lib_sample_config_from_kconf);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_sampler_pre_filter);
//  [wangkai26]
DECLARE_SPDM_ABTEST_INT64(shelf_hard_rank_quota);
DECLARE_SPDM_ABTEST_INT64(shelf_soft_rank_quota);
DECLARE_SPDM_ABTEST_INT64(shelf_all_rank_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_ad_order_cate_freq_rule);
DECLARE_SPDM_ABTEST_INT64(shelf_ad_order_cate_freq_interval);
DECLARE_SPDM_ABTEST_INT64(shelf_dislike_freq_interval);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_item_global_variety_filter);
DECLARE_SPDM_ABTEST_INT64(shelf_item_global_variety_quota);
DECLARE_SPDM_ABTEST_INT64(shelf_item_ocpx_variety_quota);
DECLARE_SPDM_ABTEST_INT64(shelf_item_photoid_variety_quota);
DECLARE_SPDM_ABTEST_INT64(shelf_item_material_variety_quota);
DECLARE_SPDM_ABTEST_INT64(shelf_item_is_new_variety_quota);
DECLARE_SPDM_ABTEST_INT64(shelf_item_type_variety_quota);
DECLARE_SPDM_ABTEST_STRING(exp_tag_in_item_card_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_holdout_all_exp1);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_holdout_hard_exp1);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_holdout_soft_exp1);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_forprerank_variety);
DECLARE_SPDM_ABTEST_INT64(shelf_forprerank_item_vquota);
DECLARE_SPDM_ABTEST_INT64(shelf_forprerank_item_photo_vquota);
DECLARE_SPDM_ABTEST_INT64(shelf_forprerank_item_material_vquota);
DECLARE_SPDM_ABTEST_STRING(exp_tag_in_item_card_exp_new);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_forprerank_variety_v1);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_forprerank_variety_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_search_recall_up);
DECLARE_SPDM_ABTEST_INT64(shelf_search_recall_limit_nums);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_cate3_recall_up);
DECLARE_SPDM_ABTEST_INT64(shelf_cate3_recall_limit_nums);
DECLARE_SPDM_ABTEST_INT64(shelf_cate3_recall_limit_time);

// [zengjiangwei03] 全域点击重定向使用 seller_id
DECLARE_SPDM_ABTEST_BOOL(enable_click_retarget_by_seller_id);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_merchant_black_list_filter);
DECLARE_SPDM_ABTEST_INT64(fx_retarget_time_interval_hour);

// [guochangyu]
// 本地流量对投召回阶段过滤
DECLARE_SPDM_ABTEST_BOOL(enable_recall_local_life_dui_tou_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_recall_local_life_dui_tou_quota_limit);

//  [xusimin]
DECLARE_SPDM_ABTEST_DOUBLE(live_storewide_hard_quota_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(live_storewide_soft_quota_ratio);
// [zhangwei26] 内循环人群包召回
DECLARE_SPDM_ABTEST_BOOL(enable_orientation_retrieve_account);
// [zhangwei26] 内循环人群召回推全开关
DECLARE_SPDM_ABTEST_BOOL(enable_orientation_retrieve_account_base);
// [zhangwei26] 新客增长品牌 R3 人群召回
DECLARE_SPDM_ABTEST_BOOL(enable_new_customer_growth_brand_r3_recall);
// [qianyangchao] 联盟小系统跳过已转化过滤
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_skip_converted_filter);
// [liuzichen05] ps 实验名称
DECLARE_SPDM_ABTEST_STRING(target_ps_adjust_exp_tag);
// [rongyu03] 游戏行业重定向开关
DECLARE_SPDM_ABTEST_BOOL(enable_game_olp_retarget_pv);
// [rongyu03] 游戏行业重定向新增通路开关
DECLARE_SPDM_ABTEST_BOOL(enable_game_olp_retarget_new_path);
DECLARE_SPDM_ABTEST_BOOL(enable_game_olp_retarget_new_path_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_upgrade_retarget_frame);
// [caijiawen] uax hold out 实验过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_uax_hold_out_filter);
DECLARE_SPDM_ABTEST_INT64(uax_hold_out_filter_time);
// [jiangjinling] NA 小说重定向开关 - 同 unit
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_olp_unit_retarget);
// [jiangjinling] NA 小说重定向 -  同 account
DECLARE_SPDM_ABTEST_BOOL(enable_target_fiction_olp_account_retarget_new);
// [jiangjinling] NA 小说重定向泛化范围扶持
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_retarget_generalize);
// [jiangjinling] NA 小说重定向人群 tag 限制
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_retarget_limit_crowd_tag);
// [guojiangwei] 激励明投图集本地召回通路
DECLARE_SPDM_ABTEST_BOOL(enable_reward_altas_local_recall);
// [lizemin] ps 请求清理 reco user info 细粒度裁剪
DECLARE_SPDM_ABTEST_BOOL(enable_reco_user_info_trim_for_ps);
// [limiaochen] 原生 & 短剧账号封禁过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_native_short_drama_ban_account_filter);
// [limiaochen] 外循环关注页接入原生广告
DECLARE_SPDM_ABTEST_BOOL(enable_follow_page_outer_native_ad);
// [limiaochen] 外循环关注页接入原生广告: 物料不允许外跳
DECLARE_SPDM_ABTEST_BOOL(is_not_deeplink);
// [limiaochen] 外循环关注页接入原生广告: 是否启用视频屏蔽过滤
DECLARE_SPDM_ABTEST_BOOL(enable_photo_shield_status_filter);
// [limiaochen] 外循环关注页接入原生广告: 是否放开关注页外循环行业直播物料软硬广限制
DECLARE_SPDM_ABTEST_BOOL(enable_fans_live_stream_promote_outer_live_follow_page);
// [panshunda] 外循环关注页接入原生广告: 物料不允许外跳 v2
DECLARE_SPDM_ABTEST_BOOL(is_not_deeplink_v2);
// [shuchengchun] 内循环 Zigzag Merge 是否使用 admit 检查
DECLARE_SPDM_ABTEST_BOOL(enable_zigzag_admit_check);
// [shuchengchun] 内循环短视频 AmdRanking 跳过 SortModelLayer
DECLARE_SPDM_ABTEST_BOOL(enable_sort_model_layer_skip);
// [zhangmengxin] 直播召回裁剪调整开关
DECLARE_SPDM_ABTEST_BOOL(close_outer_live_ranking_truncate);
// [zhangmengxin] 快小游白盒召回开关
DECLARE_SPDM_ABTEST_BOOL(enable_kwai_minigame_add_recall);
// [jiangpeng07]  快小游增加限定转化目标的白盒召回开关
DECLARE_SPDM_ABTEST_BOOL(enable_kwai_minigame_add_white_recall);
// [limiaochen] 审核中单元召回侧兜底
DECLARE_SPDM_ABTEST_BOOL(enable_audit_unit_filter);
// [wangshengyu] 一段式 ECPM 排序计费分离
DECLARE_SPDM_ABTEST_DOUBLE(recall_live_bid_price_ratio);
// [zhaoqilong] 行业 boost 召回通路开关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_industry_boost);
// [zhaoqilong] 行业 boost 召回通路配置实验名
DECLARE_SPDM_ABTEST_STRING(outer_industry_boost_name);
// [limiaochen] P 页关系链预算范围改动开关
DECLARE_SPDM_ABTEST_BOOL(enable_new_profile_relation_link_admit);
DECLARE_SPDM_ABTEST_BOOL(
    enable_outer_loop_live_e2e_used_item_recall);  // [limiaochen] 外循环行业直播下发率召回采样
DECLARE_SPDM_ABTEST_BOOL(
    enable_outer_loop_live_e2e_used_item_prerank);  // [limiaochen] 外循环行业直播下发率粗排采样
// [limiaochen] 外循环行业直播 delivery sampler 相关, copyed from wangyunli
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_delivery_sample_merge_ad_and_record_count);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_full_lib_sample_config_from_kconf);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_sampler_pre_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_prerank_add_carm_feature);
DECLARE_SPDM_ABTEST_INT64(enable_outer_prerank_carm_feature_hard_sample_count);
DECLARE_SPDM_ABTEST_INT64(enable_outer_prerank_carm_feature_soft_sample_count);
// [limiaochen] 原生 & 短剧链路账号封禁，解除特定场景的硬广打压
DECLARE_SPDM_ABTEST_BOOL(enable_skip_ban_account_native_situation);
// [wangshengyu] 一段式 ECPM 召回支持移动版投放目标
DECLARE_SPDM_ABTEST_BOOL(enable_ecpm_support_mobile_ocpx);
// [wangzixu05] trigger 相关性，特征接入
DECLARE_SPDM_ABTEST_BOOL(enable_fill_inner_trigger_emb_feature_target);
// [limiaochen] 外循环行业直播反作弊过滤
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_anti_cheat_filter);
// [limiaochen] 外循环发现页产品名黑名单
DECLARE_SPDM_ABTEST_BOOL(enable_outer_explore_product_name_blacklist);
// [lichunchi] 在 adtarget 中 set 账户创建时间
DECLARE_SPDM_ABTEST_BOOL(enable_set_account_create_time);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_purchase_sign);
// [limiaochen] 关注页外循环账户产品名黑名单过滤
DECLARE_SPDM_ABTEST_BOOL(enable_outer_follow_account_product_blacklist);
// [zhaijianwei] 外循环 X 账户黑名单过滤
DECLARE_SPDM_ABTEST_BOOL(enable_outer_x_account_blacklist);
// [limiaochen] 新账户自动屏蔽激励视频暗投
DECLARE_SPDM_ABTEST_BOOL(enable_new_account_encourage_video_filter);
// [wangshengyu] 排序计费分离兼容召回全站
DECLARE_SPDM_ABTEST_BOOL(enable_live_target_separate_billing);
// [yishijie] 频控策略增加点击次数开关
DECLARE_SPDM_ABTEST_BOOL(enable_pinkong_add_clickcnt);
DECLARE_SPDM_ABTEST_BOOL(enable_pinkong_add_blacks);
// [yishijie] 频控策略增加清零时间开关
DECLARE_SPDM_ABTEST_BOOL(enable_pinkong_add_clearhour);
DECLARE_SPDM_ABTEST_BOOL(enable_pinkong_add_clearhour_set_percent);
DECLARE_SPDM_ABTEST_BOOL(enable_pinkong_add_clickcnt_set_percent);
// [wangshengyu] 类目多样性控制
DECLARE_SPDM_ABTEST_INT64(live_ocpx_global_diversity_quota);
// [limiaochen] 激励低商品分过滤-关注页开关
DECLARE_SPDM_ABTEST_BOOL(enable_shop_score_exp_filter_for_follow);
// [limiaochen] 外循环关注页新圈定不外跳物料
DECLARE_SPDM_ABTEST_BOOL(enable_new_involved_outer_follow_ad);
// [limiaochen] UDF Rule 切换执行实验
DECLARE_SPDM_ABTEST_BOOL(enable_rule_engine_execute_udf_rule);
// [limiaochen] Ann Trigger 降级实验
DECLARE_SPDM_ABTEST_BOOL(enable_degrade_all_ann_trigger);
// [limiaochen] Redis Trigger 平迁标签召回实验
DECLARE_SPDM_ABTEST_BOOL(enable_redis_trigger_mapping_to_tag_retr);
// [limiaochen] 标签召回通路治理实验
DECLARE_SPDM_ABTEST_BOOL(enable_close_marked_clean_path);
// [limiaochen] 切换到新的商品分字段
DECLARE_SPDM_ABTEST_BOOL(enable_switch_to_new_shop_score_filter);
// [limiaochen] spu_id_v3 切换到 photo_ecom_spu_id 实验
DECLARE_SPDM_ABTEST_BOOL(enable_switch_to_photo_ecom_spu_id);
// [limiaochen] 激励广告接入外循环软广开关
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_admit_outer_soft);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_filter_outer_soft);
// [limiaochen] 小说广告位 OCPX 行业黑名单过滤
DECLARE_SPDM_ABTEST_BOOL(enable_novel_ocpx_industry_blacklist);
// [zhangyuemeng] 本地推直播全站跳过计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_LSPStorewide_recall_skip_bs);
// [sixianbo] 外循环新素材创意拆分 ab 参数
DECLARE_SPDM_ABTEST_BOOL(enable_outer_new_creative_new_param);
DECLARE_SPDM_ABTEST_INT64(outer_new_creative_exp_id);
DECLARE_SPDM_ABTEST_INT64(outer_new_creative_type_id);
// [limiaochen] 修复行业直播粗排 bid 数据填充问题
DECLARE_SPDM_ABTEST_BOOL(enable_fix_outer_live_prerank_type);
// [liuzichen05] 外循环 sdpa 层 quota
DECLARE_SPDM_ABTEST_INT64(sdpa_layer_quota);
// [liuzichen05] 外循环 sdpa 层是否在效果层前面
DECLARE_SPDM_ABTEST_BOOL(enable_sdpa_layer_before_effect_layer);
// [limiaochen] 反作弊超投治理
DECLARE_SPDM_ABTEST_BOOL(enable_anti_cheat_over_budget);
// [limiaochen] IAA 小说广告位版控需求
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_novel_cover_param_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_novel_app_version_control);
// [limiaochen] IAA 小说插屏广告位额外准入控制
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_novel_extra_filter);
// [limiaochen] 外循环生态海选修复实验
DECLARE_SPDM_ABTEST_BOOL(enable_outer_eco_preselect_fix);
// [limiaochen] IAP 小说 IOS 版本控制
DECLARE_SPDM_ABTEST_BOOL(enable_iap_novel_ios_version_control);
// [limiaochen] trigger_server 实验
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_server_exp);
// [limiaochen] 外循环软广激励安卓客户端准入
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_admit_outer_soft_3);
// [limiaochen] 外循环软广激励软广判断修复
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_admit_outer_soft_4);
// [wanglei42] 清理给 prerank 的无用字段
DECLARE_SPDM_ABTEST_BOOL(remove_useless_field_to_prerank);
// [zhangzhen24] 外循环下发率软广全库负采样
DECLARE_SPDM_ABTEST_BOOL(enable_full_lib_sample_soft_queue);
// [liyu26] 隔离出价字段透传
DECLARE_SPDM_ABTEST_BOOL(enable_no_ltv_bid_target);
// [linyuhao03] CPL2 营业执照过滤是否下移到 rank
DECLARE_SPDM_ABTEST_BOOL(enable_cpl2_corp_filter_target2rank);
// [limiaochen] CPL2 基于单元召回
DECLARE_SPDM_ABTEST_BOOL(enable_cpl2_recall_by_unit);
// [limiaochen] 私信屏蔽状态过滤
DECLARE_SPDM_ABTEST_BOOL(enable_sixin_open_status_filter);
// [yuhanzhang] 快小暗投黑名单
DECLARE_SPDM_ABTEST_BOOL(enable_kminigame_darkctrl_accountid_new);
DECLARE_SPDM_ABTEST_BOOL(enable_innerloop_cid_cancel_user_drop);
// [huliren]
DECLARE_SPDM_ABTEST_BOOL(enable_adlist_cache_inner_soft);  // 缓存内软开关
DECLARE_SPDM_ABTEST_BOOL(enable_fix_cache_info_mark);  // 缓存内软标记修复
DECLARE_SPDM_KCONF_BOOL(enablePrerankSizeCacheDot);  // 缓存打点标记
// [lichen27]
DECLARE_SPDM_ABTEST_BOOL(enable_simplify_response_item_data);  // 精简返回 item_table 字段开关
// [liuzichen05] target 不再做 reco_user_info 序列化开关
DECLARE_SPDM_ABTEST_BOOL(disable_serialize_reco_user_info);
// [yuhanzhang]
DECLARE_SPDM_ABTEST_BOOL(enable_first_industry_id_target_to_prerank_trans);
// [xuhao15] 磁盘空间过滤
DECLARE_SPDM_ABTEST_DOUBLE(ad_package_size_n_times);
// [liuzichen05] 喜番放开涨粉类广告开关
DECLARE_SPDM_ABTEST_BOOL(adIncFansReservationExp);
// [liuzichen05] 内循环召回 quota 调权系数
DECLARE_SPDM_ABTEST_DOUBLE(searcher_inner_photo_quota_up_ratio);
// [liuzichen05] 字段清理实验
DECLARE_SPDM_ABTEST_BOOL(remove_unused_fields);
// [liuzichen05] 内循环行业 redis 下线实验
DECLARE_SPDM_ABTEST_BOOL(remove_industry_redis);
// [liuzichen05] 喜番激励接入 IAA 短剧
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_inspire_iaa);
// [liujiahui10] 近线相关
DECLARE_SPDM_ABTEST_BOOL(enable_nearline_change_photo_creativeids);
DECLARE_SPDM_ABTEST_BOOL(enable_user_population_account_filter);
// [liuzichen05] 磁力金牛新客屏蔽喜番流量
DECLARE_SPDM_ABTEST_BOOL(filter_xifan_for_esp_new_author);
// [liuzichen05] 内循环分片倒排 search quota ratio 调整系数
DECLARE_SPDM_ABTEST_DOUBLE(searcher_inner_photo_search_quota_up_ratio);
// [yangyifan10] 召回切 item_key 实验
DECLARE_SPDM_ABTEST_BOOL(enable_item_key_to_hash);
// [huangting05] 内循环召回队列 quota 调整实验
DECLARE_SPDM_ABTEST_BOOL(enable_biz_name_quota_adjust);
// [wangshuaisong] 内循环 searcher path 间并行处理
DECLARE_SPDM_KCONF_BOOL(enable_searcher_async_between_paths);
// [liuzichen05] 内循环分片召回出口阶段调整系数
DECLARE_SPDM_ABTEST_DOUBLE(searcher_inner_photo_retrieval_quota_ratio);
// [wangshuaisong] 关闭 AdPerf 打点
DECLARE_SPDM_KCONF_BOOL(disableAllPerf);

// [liweijie06] 货架商城打开粉丝召回
DECLARE_SPDM_ABTEST_BOOL(enable_fans_recall_in_mall);
// [liweijie06] 货架商品卡猜喜详情页类目过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_cate2_filter);

}  // namespace ad_target
}  // namespace ks
