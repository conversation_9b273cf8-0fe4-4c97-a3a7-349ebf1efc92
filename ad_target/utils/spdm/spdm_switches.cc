#include "teams/ad/ad_target/utils/spdm/spdm_switches.h"

namespace ks {
namespace ad_target {

// 定义 kconf 开关:  SPDM_KCONF_BOOL(path, key);
// 定义 abtest 开关: SPDM_ABTEST_BOOL(key, AbtestBiz);

// 以下为 kconf 开关定义.
SPDM_KCONF_BOOL(ad.adtarget3, enableTargetBiznameControl);
SPDM_KCONF_BOOL(ad.adtarget3, enableDuanjuIaaControl);
SPDM_KCONF_BOOL(ad.adtarget3, enableSearchMingtouRuleFilter);
SPDM_KCONF_BOOL(ad.adtarget2, enableLiveTargetFollowPage);
SPDM_KCONF_BOOL(ad.adtarget2, enableLazyNegativeInit);
SPDM_KCONF_BOOL(ad.adtarget2, enableEspMobileAccountFilter);
SPDM_KCONF_BOOL(ad.adtarget, picassoDegrade);
SPDM_KCONF_BOOL(ad.adtarget3, enableAdHistoryFilterIdUnify);
SPDM_KCONF_BOOL(ad.adtarget2, enableUniverseInterveneConfig);
SPDM_KCONF_BOOL(ad.adtarget, enablePlayableInterstitialBlackSet);
SPDM_KCONF_BOOL(ad.adtarget, enableGrossProfitExploreAccountFilter);
SPDM_KCONF_BOOL(ad.adtarget, enableRiskRuleFilter);
SPDM_KCONF_BOOL(ad.adtarget, enableXdtRoasPrerank);
SPDM_KCONF_BOOL(ad.adFlowControl, enableVirtualGoldFilter);
SPDM_KCONF_BOOL(ad.adFlowControl, enableEspBudgetPartition);
SPDM_KCONF_BOOL(ad.adtarget3, enableTransWechatFeatureId);
SPDM_KCONF_BOOL(ad.adtarget, universeNewInternalAgentFilter);
SPDM_KCONF_BOOL(ad.adtarget2, enablePrerankDspLive);
SPDM_KCONF_BOOL(ad.adtarget3, enableNewCustomUseDMP);
SPDM_KCONF_BOOL(ad.adtarget2, enableShortVideoOrderAccelerateDiscount);
SPDM_KCONF_BOOL(ad.adtarget2, enableShortVideoOrderAccelerateCount);
SPDM_KCONF_BOOL(ad.adtarget2, disableUniverseDirectEcom);
SPDM_KCONF_BOOL(ad.adtarget2, enableNewSocialFilter);
SPDM_KCONF_BOOL(ad.adtarget2, enableTarget2RankSecondIndustryV5);
SPDM_KCONF_BOOL(ad.adtarget, useThanosKessGroup);
SPDM_KCONF_BOOL(ad.adtarget2, universeFillMerchantItemId);
SPDM_KCONF_BOOL(ad.adtarget, enableTargetGetUeUserProfile);
SPDM_KCONF_BOOL(ad.adtarget2, universeFillLiveStartTimeTarget);
SPDM_KCONF_BOOL(ad.adtarget2, disableMcbCpaBidAdjust);
SPDM_KCONF_BOOL(ad.adtarget2, universeInnerSupportOriginLive);
SPDM_KCONF_BOOL(ad.adtarget2, promotionConsultationFilter);
SPDM_KCONF_BOOL(ad.adtarget2, enableLoadInnerLoopIndex);
SPDM_KCONF_BOOL(ad.adRank, enableMonitorPopRecruit);
SPDM_KCONF_BOOL(ad.adtarget2, enableRtaUndertakeFilter);
SPDM_KCONF_BOOL(ad.adtarget2, enableSearchQueryNormP2p);
SPDM_KCONF_BOOL(ad.adtarget2, enableBudgetStatusDataSwitch);
SPDM_KCONF_BOOL(ad.adtarget3, enableInnerCIDSepIAAControlAddPlaylet);
SPDM_KCONF_BOOL(ad.adtarget3, enableInnerCIDSepIAAControlFilter);
SPDM_KCONF_BOOL(ad.adtarget2, enableTarget2BidServiceAutoDarkControl);
SPDM_KCONF_BOOL(ad.adtarget3, enableTransWechatRtaInfo);
SPDM_KCONF_BOOL(ad.adtarget3, enableTargetDistanceRuleFilter);
SPDM_KCONF_BOOL(ad.adtarget3, enableOuterLoopCandiatePerf);
SPDM_KCONF_BOOL(ad.adtarget3, enableHarmonyWhiteListFilter);
SPDM_KCONF_BOOL(ad.adtarget3, enableCouponConfigId);
SPDM_KCONF_BOOL(ad.adtarget3, enableCouponPackageId);
SPDM_KCONF_BOOL(ad.adtarget3, enableResidenceAdcodeTarget);
SPDM_KCONF_BOOL(ad.adtarget3, enablePrerankClientRandomSelect);
SPDM_KCONF_BOOL(ad.adRank3, enableTargetCacheRecord);
SPDM_KCONF_BOOL(ad.adtarget3, disableSerializeRecoUserInfo);
SPDM_KCONF_BOOL(ad.adtarget3, enableTargetPrerankTransLocal);
// [wangwenguang]
SPDM_KCONF_BOOL(ad.adtarget3, enableClientAiLocalRerankDebug);
SPDM_KCONF_INT32(ad.adtarget3, clientAiLocalRerankDebugEvery, 100000);
SPDM_ABTEST_BOOL(enable_client_ai_perf_base_request, ks::AbtestBiz::AD_DSP);

// [tangsiyuan]
SPDM_ABTEST_BOOL(enable_fanstop_filter_only_order_mode, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_add_phone_call_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_biz_fanstop_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_account_set_opt, ks::AbtestBiz::AD_DSP);  // [zhangruyuan] rta account
SPDM_ABTEST_BOOL(enable_skip_game_banner_dark_budget_control_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_phone_call_hetu_orientation_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_phone_call_population_filter, ks::AbtestBiz::AD_DSP);

// [heqian]
SPDM_ABTEST_BOOL(enable_creative_dup_in_merge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_delete_no_use_attr_from_prerank, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_INT64(creative_dup_set_size_in_merge, ks::AbtestBiz::AD_DSP, 0);
SPDM_KCONF_BOOL(ad.adtarget3, enableHarmonyRewardedOuterLive);
SPDM_KCONF_BOOL(ad.adtarget3, enableHarmonyOuterLive);
SPDM_KCONF_BOOL(ad.adtarget3, enableHarmonyOuterP2l);
SPDM_KCONF_BOOL(ad.adtarget3, disableInvertDepend);
SPDM_ABTEST_BOOL(disable_invert_depend, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_ps_filter_set, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_simplified_for_inner, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adtarget3, enableRtaSimplifiedForInner);
SPDM_KCONF_BOOL(ad.adtarget3, enableItemKeyToHash);
SPDM_ABTEST_DOUBLE(trigger_searcher_degrade_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(disable_client_dup_live_photo_id_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_recall_degrade_by_hour, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_recall_degrade, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(extra_merger_quota_OUTER_PHOTO, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(extra_merger_quota_OUTER_SOFT_PHOTO, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(max_inner_soft_photo_rank_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(max_outer_soft_photo_rank_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_unified_follow_live_prerank_truncate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pass_addr_match_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(extra_inner_soft_rank_degrade_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_clear_useless_item, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adtarget3, enableDragonPrerankSample);

// [liuzhiqiang08]
SPDM_ABTEST_BOOL(enable_effect_path_set_new, ks::AbtestBiz::AD_DSP);  // 新版效果层通路集合，支持实验
SPDM_ABTEST_BOOL(enable_outer_selected_path, ks::AbtestBiz::AD_DSP);  // 只生效筛选的通路 - feed/reward 共用
SPDM_ABTEST_STRING(outer_selected_path_feed, ks::AbtestBiz::AD_DSP, "");      //  只生效的通路列表 - feed
SPDM_ABTEST_STRING(outer_selected_path_rewarded, ks::AbtestBiz::AD_DSP, "");  // 只生效的通路列表 - rewarded

// [zhangruyuan]
SPDM_ABTEST_BOOL(enable_universe_smart_compute_dynamic_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pop_recruit_page_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_recruit_similar_position, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pop_open_some_page, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_black_page_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pop_live_inspire, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(inner_soft_to_inner_loop_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target2rank_second_industry_5, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_trans_wechat_rta_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_shoufa_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_left_budget_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_left_budget_set_default, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_whole_left_budget_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(incentive_left_budget_thres, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(incentive_whole_left_budget_thres, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_cid_cancel_user_drop, ks::AbtestBiz::AD_DSP);

// [liyongchang]
SPDM_ABTEST_BOOL(enable_playlet_iaa_incentive_control_refactor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_iaa_incentive_filter_flip, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_iaa_blacklist_default_incentive_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(playlet_iaa_open_account_24h_cost_thres, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_BOOL(enable_playlet_iaa_open_not_clue, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_iaa_open_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_iaa_holdout_fix, ks::AbtestBiz::AD_DSP);

// [liyichen05]
SPDM_ABTEST_BOOL(enable_skip_iaa_dj_incentive_dark_budget_control_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dj_iaa_incentive_dark_budget_control_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dj_iaa_skip_incentive_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dj_skip_campaign, ks::AbtestBiz::AD_DSP);

// [liyichen05] xifan
SPDM_ABTEST_BOOL(enable_xifan_black_filter, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_similar_photo_filter, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_skip_outer_soft_admit_filter, ks::AbtestBiz::XIFAN_PLAY_APPS);
// [liqikun]
SPDM_ABTEST_BOOL(enable_wanhe_for_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_context_init_optimization_exp, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adtarget2, enable_context_init_optimization);
SPDM_KCONF_BOOL(ad.adtarget3, enableTransPathIds);
SPDM_ABTEST_BOOL(enable_inner_rta_live_exp, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adtarget2, enable_inner_rta_live);
SPDM_KCONF_BOOL(ad.adtarget2, enable_live_skip_dragon_bid_handler);
SPDM_KCONF_BOOL(ad.adtarget2, enable_live_dragon_bid_handler);
SPDM_ABTEST_BOOL(enable_inspire_quick_app_filter_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ios_inspire_image_set_filter_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ios_wanhe_for_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_follow_flow_random, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_unify_prerank_score, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_hema_skip_iaa_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_random_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(playlet_random_tag, ks::AbtestBiz::AD_DSP, "all_test");
SPDM_KCONF_BOOL(ad.adtarget2, enable_clear_unlogin_user_id);
SPDM_ABTEST_BOOL(enable_simplify_fanstop_context, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_freq_context_migrate, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_STRING(ad.adtarget2, adx_spu_redis_name, "");
SPDM_KCONF_STRING(ad.adtarget2, adx_spu_table_name, "");
SPDM_KCONF_INT32(ad.adtarget2, adx_spu_timeout, 4);
SPDM_KCONF_INT32(ad.adtarget2, adx_spu_creative_quota, 10);
SPDM_KCONF_INT32(ad.adtarget2, adx_spu_token_quota, 10);
SPDM_ABTEST_BOOL(enable_follow_page_daitou_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wechat_rta_origin_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_q3_hc_crm_center, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_meituan_rta, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wechat_rta_auth_origin_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_filter_shorter_than_1S, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ue_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_for_money_main_rule_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_micro_game_ios_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_micro_app_long_video_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_unified_risk_filter, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adtarget3, enable_inspire_dark_budget_product_filter);
SPDM_ABTEST_BOOL(enable_inspire_dark_budget_product_filter_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_xifan_dark_budget_control_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_charge_tag_switch_to_5, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nagtive_tag_blacklist, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nagtive_tag_blacklist_1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nagtive_tag_blacklist_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_zero_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(delete_prerank_fields_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sample_base_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_auto_budget_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_auto_budget_control_outer, ks::AbtestBiz::AD_DSP);

// [fukunyang]
SPDM_ABTEST_BOOL(enable_storewide_gmv_level_to_prerank_trans, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_rel_type_to_prerank_trans, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_outer_photo_rule_engine_check,
                 ks::AbtestBiz::AD_DSP);  // [zhangruyuan] 外循环短视频规则引擎 check
SPDM_ABTEST_BOOL(enable_outer_live_retrieval_check,
                 ks::AbtestBiz::AD_DSP);  // [zhangruyuan] 外循环直播仅从直播召回 check

SPDM_ABTEST_STRING(unified_risk_ab_group, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_inspire_live_random_drop, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_drop_onestep_table, ks::AbtestBiz::AD_DSP);     // [zhangruyuan] 下线一段式表

SPDM_KCONF_BOOL(ad.adtarget3, enable_new_fields_from_forward_diff);

SPDM_KCONF_INT32(ad.frontserver, riskControlMinUserLevel, 4);
SPDM_KCONF_INT32(ad.adtarget2, traceTsmRatio, 0);
SPDM_KCONF_INT32(ad.adtarget3, traceTsmRatioForAd, 0);

SPDM_KCONF_INT32(ad.adtarget2, onlyPecSceneId, 3);
// 以下为 kconf int64 参数声明
SPDM_KCONF_INT64(ad.adtarget, immTriggerTokenMaxQuota, 5000);
SPDM_KCONF_INT64(ad.adtarget, immPerTokenExpandMaxQuota, 200);
SPDM_KCONF_INT64(ad.adtarget, immTotalCreativeMaxQuota, 50000);
SPDM_KCONF_INT64(ad.adtarget, spuPurchasedFreqControlInterval, 604800);  // 7 d
// [liuwenlong03] 拉活跳过 以安装应用过滤
SPDM_KCONF_BOOL(ad.adtarget2, enableAppAdvanceSkipAppFilter);

SPDM_KCONF_BOOL(ad.adtarget2, universeTinyPrerankOpt);

// 以下为 kconf double 参数声明
SPDM_KCONF_DOUBLE(reco.inner_fanstop, archimedes_high_cost_ratio_threshold, 0.9);
SPDM_KCONF_DOUBLE(ad.adtarget2, splashRealtimeRecallRatio, 0.1);

// [yuancuili]
// 三率优化过滤比
SPDM_ABTEST_BOOL(enable_sanlv_cate3_thr_ratio_author_refactor_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(sanlv_cate3_thr_ratio_exp_tag, ks::AbtestBiz::AD_DSP, "")

SPDM_ABTEST_BOOL(enable_inner_live_refund_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_product_refund_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_live_refund_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_product_refund_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_live_refund_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_live_refund_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 0.9);
SPDM_ABTEST_DOUBLE(inner_product_refund_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_product_refund_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 0.9);
SPDM_ABTEST_BOOL(enable_prerank_industry_live_diversity, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_target_follow_page_align_rule_merger, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_target_follow_page_align_rule_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_cny_mutex, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_diversity_roi_ratio_1000, ks::AbtestBiz::AD_DSP);  // [yushengkai] 多样性 roi_ratio 1000  // NOLINT
SPDM_ABTEST_BOOL(enable_soft_diversity_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_filter_inner_agent_account, ks::AbtestBiz::AD_DSP);  // [liubing05] 过滤内部代理广告开关  // NOLINT
SPDM_ABTEST_BOOL(enable_ext_merchant_self_ecpm_sort,
                 ks::AbtestBiz::AD_DSP);  // [zhangyiwei03] 商家外投单独 ecpm 粗排开关
SPDM_ABTEST_BOOL(enable_prerank_trans_local_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_double11_skip_diversity_filter,
                      ks::AbtestBiz::AD_DSP);  // [sixianbo] 双 11 白名单跳过多样性过滤
SPDM_ABTEST_BOOL(enable_ext_merchant_new_auto_papo,
                 ks::AbtestBiz::AD_DSP);  // [zhangyiwei03] 商家坡爬新创意灵活调整开关
SPDM_ABTEST_BOOL(enable_merchant_explore_prerank_papo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_union_shortvideo_dark_prerank_papo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_nobid_unit_tail_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_del_dup_account, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_age_expand, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_product_impr_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dif_windows, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(disable_prerank_quota_degrade, ks::AbtestBiz::AD_DSP);


// [yangfukang03]
SPDM_ABTEST_BOOL(enable_load_outerloop_interest_retarget_product, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_load_llm_user_prod_interest_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_nc_target_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_low_active_ac_pass, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_update_outerloop_nc_kconf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_nc_limit_ocpx, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_nc_model_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_week_stay_retarget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_installed_filter_target_fix_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_stay_retarget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_filter_b_fans_top, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(week_stay_retarget_tag, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_outer_soft_dcaf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(outer_soft_max_ad_count_v3, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outer_soft_rank_num_v3, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outer_photo_default_rank_num_v2, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outer_hard_biz_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_skip_sheild_budget_by_whiteset, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_sheild_budget_by_version_whitejson, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(iaa_acq_gen_version_string, ks::AbtestBiz::AD_DSP, "version1,version2");
SPDM_ABTEST_STRING(lcron_one_model_exp_group, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_lcron_tag_factors_init, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_lcron_onemodel_exclude_minigame, ks::AbtestBiz::AD_DSP);

// [jiayalong]
SPDM_ABTEST_BOOL(enable_outer_fiction_target_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_low_active_ac_mark_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(outerloop_low_active_ac_thresh, ks::AbtestBiz::AD_DSP, 30);

// [nizhihao]
SPDM_ABTEST_BOOL(enable_commercial_user_info_pass, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kai_game_iaa_retrieval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_redis_parse, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_retarget_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_stra_field, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_stra_field_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_retarget_game_recall_monitor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_inspire_block_iap, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_big_r_explore_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_iaap_7r_retrieval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(enable_mini_game_big_r_strategy_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_mini_game_new_big_r_field, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_params_parse, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iap_payment_window_30d, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_r_use_long_value, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(default_iap_payment_constraint, ks::AbtestBiz::AD_DSP, 180.0);
SPDM_ABTEST_DOUBLE(default_iap_long_value_constraint, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_strategy_by_30d, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_strategy_by_24h, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mini_game_big_r_ee_strategy_24h_thrd, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(mini_game_big_r_ee_strategy_30d_thrd, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_uv_monitor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_uv_monitor_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_r_explore_skip_diversity, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_r_explore_add_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(big_r_explore_add_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_big_r_monitor_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_r_skip_invert_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_user_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_explore_end, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_r_explore_skip_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_conv_user_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(game_conv_user_tag, ks::AbtestBiz::AD_DSP, 100000);
SPDM_ABTEST_BOOL(enable_game_user_level_diversity, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_user_level_diversity_thrd, ks::AbtestBiz::AD_DSP, 10.0);
// [wangzixu05]
SPDM_ABTEST_BOOL(enable_inner_explore_trigger_add_outer_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_explore_trigger_add_outer_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(inner_explore_trigger_outer_path_id, ks::AbtestBiz::AD_DSP, 525);
SPDM_ABTEST_BOOL(enable_effect_path_set_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inner_explore_effect_path_group, ks::AbtestBiz::AD_DSP, "exp1");
// [wengrunze]
SPDM_ABTEST_STRING(private_message_retarget_retrieval_efficient_group, ks::AbtestBiz::AD_DSP, "");
// [lichao]
SPDM_ABTEST_BOOL(enable_kai_game_iaap_retrieval, ks::AbtestBiz::AD_DSP);

// [yangfukang03]
SPDM_ABTEST_BOOL(enable_target_load_nc_second_industry, ks::AbtestBiz::AD_DSP);

// [bianfeifei]
SPDM_ABTEST_BOOL(enable_x7_rookie_seller_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(new_enable_x7_rookie_seller_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_category_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pec_switch_to_rule_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(convert_filter_switch_rule_status, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_BOOL(enable_universe_product_skip_diversity, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_audience_diverse_prerank, ks::AbtestBiz::AD_DSP);  // [xiemiao] 直播多样性开关
SPDM_ABTEST_BOOL(enable_audience_ecpm_prerank_sort, ks::AbtestBiz::AD_DSP);  // [xiemiao] 直播爬坡排序开关
SPDM_ABTEST_BOOL(enable_author_ecpm_prerank_sort, ks::AbtestBiz::AD_DSP);  // [xiemiao] 直播爬坡排序开关
SPDM_ABTEST_BOOL(disable_diverse_prerank, ks::AbtestBiz::AD_DSP);  // [xiemiao] 直播爬坡开关
SPDM_ABTEST_BOOL(enable_spu_account_diverse_prerank, ks::AbtestBiz::AD_DSP);  // [xiemiao] spu 爬坡多样性
SPDM_ABTEST_BOOL(enable_spu_ecpm_prerank_sort, ks::AbtestBiz::AD_DSP);  // [xiemiao] 短视频 spu 爬坡排序开关
SPDM_ABTEST_BOOL(enable_high_quality_photo_awake, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_preselect_rank_for_rulefilter_diversity, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_new_holdout_v3, ks::AbtestBiz::AD_DSP);  // [liyongchang]
SPDM_ABTEST_BOOL(enable_playlet_iaa_holdout, ks::AbtestBiz::AD_DSP);  // [liyongchang]

SPDM_ABTEST_BOOL(enable_target_industry_explore_append, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_wide_material_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_age_merge_filter, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_similar_photo_freq_delivery, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_whole_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_key_word_population, ks::AbtestBiz::AD_DSP);
// [tiangeng] 七日出价系列开关
SPDM_ABTEST_BOOL(enable_bidding_7days_pay_times_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(skip_auto_target_pure_roaring, ks::AbtestBiz::AD_DSP);

// [liuchangjin] 粗排完全排序计费分离开关
SPDM_ABTEST_BOOL(enable_preranking_thorough_billing_separation, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_middle_page_industry_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_buyer_home_live_cart_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_merchant_campaign_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_hosting_unit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_stream_author_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_select_high_quality, ks::AbtestBiz::AD_DSP);  // [caining] 素材扶优开关 召回
// [zhangruyuan]
SPDM_ABTEST_BOOL(enable_jvxing_negtive_tag_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target_request_pid_server, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_merger_stop_watch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_thanos_skip_cpm_filt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_thanos_skip_cpc_filt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_prerank_drop_down_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_ind_photo_expore_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_recall_ensemble_sort, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_weak_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target_mobile_fill_soft_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_live_punish, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_p2l_live_punish, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_new_custom_use_dmp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_non_amdlive_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_old_prioritys, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_forward_layer_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_degrade_prerank_soft_outer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outer_soft_prerank_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_rewarded_forward_layer_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_and_hard_tag_exp_feed, ks::AbtestBiz::AD_DSP);      // 流化分实验复制软硬广
SPDM_ABTEST_BOOL(enable_soft_and_hard_tag_exp_rewarded, ks::AbtestBiz::AD_DSP);  // 激励分实验复制软硬广
SPDM_ABTEST_BOOL(enable_fix_soft_hard_copy, ks::AbtestBiz::AD_DSP);              // 上面两个开关的逻辑修复
SPDM_ABTEST_BOOL(enable_nc_path_reweight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_zigzag_order, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_into_hard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_adlist_cache, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_change_inspire_live_recall_conf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gyl_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_one_step_inroom_merge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_new_scene, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_speed_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_trace_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_unit_over_budget_check, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_one_step_ecpm_merge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_small_game_nodark_budget_control_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_small_game_soft_nodark_budget_control_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reward_one_step_merge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_prerank_add_candidate_feature_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_live_prerank_candidate_feature_sample_count_v1, ks::AbtestBiz::AD_DSP, 0);

// [zhangyuyang05] 激励广告 低价值低活人群过滤
SPDM_ABTEST_DOUBLE(incntv_ad_ocpx_filter_v2_cpm_thres, ks::AbtestBiz::AD_DSP, 0.0);

// [yechen05] 客户增长 SE 项目粗排扶持开关
SPDM_ABTEST_BOOL(enable_corp_growth_se_preranking, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_boyle_plus_customer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecology_merchant_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target_fix_soft_resort, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecology_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(explore_exp_tag,  ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(roas_vs_order_exp_tag,  ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_high_return_user_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecology_merchant_filter_fanstop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_ecology_order_pay, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_cold_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_md5_photo_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_inspire, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ori_filter_close, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wechat_rta_filter, ks::AbtestBiz::AD_DSP);
// [gaozepeng] 激励跳过历史过滤逻辑
SPDM_ABTEST_BOOL(disable_incentive_item_click_nofollow_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_incentive_item_click_u0_filter, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_prerank_unified_rate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_unified_rate_fans_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_unified_rate_jinjian, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_budget_server_show_cnt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(outer_native_photo_holdout_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(outer_native_business_holdout_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(industry_live_retrieval_diversity_quota, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(outer_live_ocpx_action_limit, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_BOOL(enable_game_all_outer_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_load_game_similar_product_retarget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_native_only_soft_block, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tube_skip_outer_native_author_neg, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tube_skip_outer_native_author_neg_v2, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_INT64(inner_hard_photo_ranking_quota, ks::AbtestBiz::AD_DSP, 650);
SPDM_ABTEST_INT64(inner_hard_p2l_ranking_quota, ks::AbtestBiz::AD_DSP, 240);
SPDM_ABTEST_INT64(inner_hard_photo_ecology_append_ranking_quota, ks::AbtestBiz::AD_DSP, 100);

SPDM_ABTEST_BOOL(gfp_skip_owe_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bs_sort_weight_symmetry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(default_billing_ratio, ks::AbtestBiz::AD_DSP, 0.65);
SPDM_ABTEST_DOUBLE(billing_saparate_owe_ratio_thrd, ks::AbtestBiz::AD_DSP, 0.85);
SPDM_ABTEST_DOUBLE(bs_owe_control_begin, ks::AbtestBiz::AD_DSP, 0.95);
SPDM_ABTEST_DOUBLE(bs_max_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(auto_bid_weight, ks::AbtestBiz::AD_DSP, 0.5);

SPDM_ABTEST_BOOL(enable_budget_throttling, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_sug_api_async, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sug_query_type_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_sug_search_a_2_b, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_sug_search_a_2_b_floor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sug_query_type_optimize, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enableCreativeFind, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_expandbyrelevance_remove_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_huge_migrate_tsm_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_adjust_brecall_quato_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_adjust_recall_quato_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_at_skip_rta_table_universe_tiny, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_prerank_quota_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_prerank_live_audience_no_fans_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_live_fans_max_num, ks::AbtestBiz::AD_DSP, 300);
SPDM_ABTEST_BOOL(enable_universe_inner_fans_short_video, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_live_follow_dp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(live_audience_no_fans_ratio_p2l, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(live_audience_no_fans_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_prerank_cpm_order_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_prerank_cpm_roas_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_universe_action_app_fill_conv_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_inner_loop_fans_target_expand, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_photo_filter_feature, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_loop_e2e_used_item_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_loop_e2e_used_item_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_esp_image_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_live_item, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_app_search_prerank_diversity_uniq_quota, ks::AbtestBiz::AD_DSP, 1);

// [liuxiaoyan] 系统缓存频控
SPDM_ABTEST_BOOL(enable_client_industry_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_product_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(target_author_diversity_quota, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(client_industry_freq_imps_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(client_product_freq_imps_num, ks::AbtestBiz::AD_DSP, 0);

// [chenwancheng03] TDM 实验使用参数
SPDM_ABTEST_BOOL(enable_tdm_reco_rank, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(use_nebula_prerank_purchase, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_univ_prerank_live_follow_model, ks::AbtestBiz::AD_DSP);
// [yewencai] 粉条作品 & 直播粗排开关
SPDM_ABTEST_BOOL(enable_ctcvr_photo_live_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_other_photo_live_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ctcvr_roi_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_photo_live_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_prerank_ensemble_sort, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_prerank_use_dup_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hard_prerank_ctcvr_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(ab_switch_enable_user_ad_risk_control, ks::AbtestBiz::AD_DSP);

// [daidongyang] 品牌电商粗排保送策略（短视频）
SPDM_ABTEST_BOOL(enable_prerank_ecom_brand_baosong, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_select_brand_by_recall_tag, ks::AbtestBiz::AD_DSP);

// [huangzhaokai] 移动版硬广引流 amd_ranking 逻辑兼容
SPDM_ABTEST_BOOL(enable_mobile_photo_hard_amd_ranking_compatible, ks::AbtestBiz::AD_DSP);

// [huangzhaokai] 直播粗排高价值粉丝召回通道保送开关
SPDM_ABTEST_BOOL(prerank_live_enable_author_valued_fans_recall_baosong, ks::AbtestBiz::AD_DSP);

// [huangzhaokai] 移动端硬广短视频扶持
SPDM_ABTEST_BOOL(enable_mobile_hard_photo_guaranteed_preranking, ks::AbtestBiz::AD_DSP);

// [huangzhaokai] 移动端硬广引流扶持
SPDM_ABTEST_BOOL(enable_mobile_hard_p2l_guaranteed_preranking, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_adjust_fanstop_photo_prerank_num, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_skip_new_creative_for_inspire_merchant_live_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(follow_ad_queue_type_soft, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_session_field_global_app_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_delivery_interval_seconds_inspire_merchant_live_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_ad_individual_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_prerank_white_baosong, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_high_bid_low_budget_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mobile_budget_threshold, ks::AbtestBiz::AD_DSP, 100000);
SPDM_ABTEST_DOUBLE(mobile_drop_ratio_threshold, ks::AbtestBiz::AD_DSP, 0.5);

// [lishaozhe] 涨粉质量 U 类客户过滤类型
SPDM_ABTEST_INT64(follow_quality_user_effective_filter_type, ks::AbtestBiz::AD_DSP, 0);
// [luochao, chenxian] 搜索重定向专项，query 频控次数
SPDM_ABTEST_INT64(search_retarget_frequency_control, ks::AbtestBiz::AD_DSP, 1);

SPDM_ABTEST_BOOL(enable_roas_block_no_sale_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(roas_block_no_fans_ratio, ks::AbtestBiz::AD_DSP, 1.0);

// [jiangjiaxin]
SPDM_ABTEST_BOOL(enable_use_esp_coldstart_tag_realtime, ks::AbtestBiz::AD_DSP);

// [cuihongyi]
SPDM_ABTEST_BOOL(enable_inner_inspire_live_order_no_auto, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_inspire_live_order_no_dark, ks::AbtestBiz::AD_DSP);

// [wangpeng16]
SPDM_ABTEST_BOOL(enable_fanstop_merge_quota_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fanstop_merge_max_quota, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_DOUBLE(fanstop_merge_max_ratio, ks::AbtestBiz::AD_DSP, 1.0);

// [lixu05]
SPDM_ABTEST_STRING(realtime_recall_tag_key,  ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(brand_fans_recall_tag_key,  ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_shallow_cls, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(shallow_ocpx_cls_list,  ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_true_cid_revert_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ranking_replace_photo_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ranking_replace_ocpx_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(merge_storewide_spu_ocpx_quota, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_BOOL(enable_inner_merge_all_cvr_ctcvr_multi, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_self_service, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_intelligence_population_break_soft, ks::AbtestBiz::AD_DSP);
// [lihantong] 非复制计划广告替换实验
SPDM_ABTEST_BOOL(enable_mobile_v3_flow_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_loop_photo_stable_index_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_loop_live_stable_index_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(p2l_roas_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_nobid_strategy_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_photo_ctrl, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_account_type_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kesu_account_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kesu_industry_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(short_play_time_interval, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(short_play_photo_check_seconds, ks::AbtestBiz::AD_DSP, 14400);
SPDM_ABTEST_INT64(short_play_photo_industry_check_seconds, ks::AbtestBiz::AD_DSP, 3600);
SPDM_ABTEST_INT64(short_play_photo_first_industry_check_seconds, ks::AbtestBiz::AD_DSP, 3600);
SPDM_ABTEST_BOOL(enable_product_filter_calc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(short_play_time_interval_v3, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(click_check_seconds, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(product_watch_times, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(short_play_ratio, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_INT64(high_return_order_filter_thresh, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(inner_bonus_exp_tag, ks::AbtestBiz::AD_DSP, "default");
// [maxiu] 电商激励虚拟金过滤开关
SPDM_ABTEST_BOOL(disable_merchant_inspire_inner_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_inspire_video_esp_mobile_plc_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_inspire_video_fanstop_plc_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_black_list_filter_for_inspire_merchant, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_retarget_retrieval_creative_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(universe_retarget_retrieval_author_num, ks::AbtestBiz::AD_DSP, 0);
// [moq] 激励直播屏蔽功能对内循环都生效
SPDM_ABTEST_BOOL(enable_inspire_live_shield_with_all_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_wt_live_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_live_pay_yellow_chart_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_live_roas_yellow_chart_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_amd_photo_p2l_prerank_input_quota_refactor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_hard_photo_p2l_ranking_quota, ks::AbtestBiz::AD_DSP, 970);
SPDM_ABTEST_INT64(inner_hard_reward_photo_p2l_ranking_quota, ks::AbtestBiz::AD_DSP, 1250);
SPDM_ABTEST_INT64(cache_inner_hard_photo_p2l_ranking_quota, ks::AbtestBiz::AD_DSP, 0);
// [houjian]
SPDM_KCONF_BOOL(ad.adtarget2, enableTargetExpandOpt);
SPDM_ABTEST_BOOL(disable_follow_tag_enricher, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_unify_dark_budget_ratio, ks::AbtestBiz::AD_DSP);

// [chenxian]
SPDM_ABTEST_INT64(photo_ocpx_global_diversity_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(photo_global_diversity_quota, ks::AbtestBiz::AD_DSP, 25);
SPDM_ABTEST_BOOL(enable_iap_core_population_recall_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iap_potential_population_recall_filter, ks::AbtestBiz::AD_DSP);

// [caolin] 粉条招工人群定向实验开关
SPDM_ABTEST_BOOL(enable_fanstop_recruit_population_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_pop_population_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_app_ls_use_new_population, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(recruit_population_skip_explore_thanos, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(recruit_population_skip_explore_normal, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(diable_fanstop_photo_recruit_population_filter, ks::AbtestBiz::AD_DSP);
// [caolin] 粉条直播冷启动实验开关
SPDM_ABTEST_BOOL(enable_fanstop_live_cold_start_tag, ks::AbtestBiz::AD_DSP);
// [chencongzheng] 移动端冷启动打标
SPDM_ABTEST_BOOL(enable_esp_mobile_live_cold_start_tag, ks::AbtestBiz::AD_DSP);
// [moqi] 只有直投直播冷启动标签单独计算
SPDM_ABTEST_BOOL(enable_only_direct_live_cold_start_tag_cal, ks::AbtestBiz::AD_DSP);
// [moqi] 激励直播白名单客户控比放开
SPDM_ABTEST_BOOL(enable_inspire_live_control_whitelist_off, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shop_link_thanos_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pc_shop_link_filter, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_fanstop_litter_anchor_adjust, ks::AbtestBiz::AD_DSP);

// [yangyanxi] 激励视频白名单过滤
SPDM_ABTEST_BOOL(enable_mingtou_recall_product_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ocpx_action_type_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_refresh_count_manage, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_refresh_count_move_ahead, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_refresh_count_use_bidinfo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_feed_product_name_blacklist, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_zhutui_all_flow_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_recall_limit_cate3_nums, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_recall_limit_cid_cate3_nums, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(prerank_ctcvr_cover_all_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(prerank_deep_unified_cover_other_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_select_by_unit_cost_live, ks::AbtestBiz::AD_DSP);
// [gaozepeng]
SPDM_ABTEST_BOOL(enable_incentive_invoked_product_retrieval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_special_account_type_filter, ks::AbtestBiz::AD_DSP);  // 虚拟金账户、内部账户过滤
SPDM_ABTEST_BOOL(enable_select_by_unit_cost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_select_by_cid_unit_cost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_select_by_cid_unit_cost_or_item_order, ks::AbtestBiz::AD_DSP);
// [zhangxingyu03]
SPDM_ABTEST_BOOL(enable_only_7d_roi_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_draw_flow_inner_hard_live_biz_allow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_separate_category_purchased_interval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(incentive_category_purchased_interval, ks::AbtestBiz::AD_DSP, 1800);
SPDM_ABTEST_BOOL(enable_incentive_harmony_filter, ks::AbtestBiz::AD_DSP);
// [shoulifu03] 金牛直播粗排使用动态 quota
SPDM_ABTEST_BOOL(enable_inner_author_freq_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_author_freq_filter_without_follow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_photo_id_freq_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_live_id_freq_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gross_profit_explore_account_filter, ks::AbtestBiz::AD_DSP);  // [zhoumingsong]
SPDM_ABTEST_BOOL(video_roas_use_new_ctcvr_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_new_cpa_bid_60, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_ctcvr_auto_cpa_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_ali_browsed_info_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_site_assembly_filter_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_filter_creative_type_not_match, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_promotion_climb_new_frame, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_append_operation, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_native_special_support_f1_preranking, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_native_prerank_append, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_innerloop_item_card_prerank_append, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_auto_campaign, ks::AbtestBiz::AD_DSP);

// [liuwenlong03] 白名单用户跳过 以安装应用过滤
SPDM_ABTEST_BOOL(enable_app_filter_white_list, ks::AbtestBiz::AD_DSP);

// [fangmeiling] 金牛直播召回拆分 ecpm 和 delivery 队列
SPDM_ABTEST_BOOL(enable_live_target_ecpm_and_delivery, ks::AbtestBiz::AD_DSP);

// [wubo05] 激励直播走 LiveMerge 开关
SPDM_ABTEST_BOOL(enable_inspire_live_merge, ks::AbtestBiz::AD_DSP);

// [cuiyanliang] 金牛移动端投硬广开关
SPDM_ABTEST_BOOL(enable_esp_mobile_placing_hard_ad, ks::AbtestBiz::AD_DSP);
// [huangzhaokai] 金牛移动端硬广短视频投 ocpx 开关
SPDM_ABTEST_BOOL(enable_esp_mobile_hard_photo_admit_ocpx, ks::AbtestBiz::AD_DSP);
// [huangzhaokai] 金牛移动端硬广引流投 ocpx 开关
SPDM_ABTEST_BOOL(enable_esp_mobile_hard_p2l_admit_ocpx, ks::AbtestBiz::AD_DSP);
// [fangmeiling] 金牛直播召回 delivery 队列是否用 auto_roas
SPDM_ABTEST_BOOL(live_target_delivery_use_auto_roas, ks::AbtestBiz::AD_DSP);
// [fangmeiling] 金牛直播召回 recall delivery 队列排序
SPDM_ABTEST_BOOL(enable_live_target_delivery_recall_rank, ks::AbtestBiz::AD_DSP);
// [songxu] 用户目标行为过滤开关 新
SPDM_ABTEST_BOOL(enable_action_target_product_filter_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_action_target_account_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_action_target_product_filter_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_action_target_product_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ee_search_prerank_quota_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target_search_linucb_score_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_prerank_cvr_boost_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(action_target_account_filter_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(cid_prerank_cvr_boost_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_cid_pay_refund_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_pay_refund_industry_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_action_filter_diff_scene_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_pay_refund_industry_filter_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_refund_ctrl_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_refund_conv_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cid_refund_corp_weight, ks::AbtestBiz::AD_DSP, 0.7);
SPDM_ABTEST_DOUBLE(cid_refund_spu_weight, ks::AbtestBiz::AD_DSP, 0.2);
SPDM_ABTEST_DOUBLE(cid_refund_user_weight, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(cid_refund_filter_thr, ks::AbtestBiz::AD_DSP, 0.88);
SPDM_ABTEST_BOOL(enable_cid_refund_filter_thr_reward, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cid_refund_filter_thr_reward, ks::AbtestBiz::AD_DSP, 0.6);

// [fangmeiling] 金牛直播召回 preselect expander 使用 min_bid 过滤
SPDM_ABTEST_BOOL(delivery_auto_cpabid_or_roas_filter, ks::AbtestBiz::AD_DSP);
// [songxu] rta 召回通路 boost
SPDM_ABTEST_BOOL(enable_cid_spu_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_corp_default_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cid_corp_default_bid_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_cid_quality_strategy_exp, ks::AbtestBiz::AD_DSP);

// [fangmeiling] 金牛直播一段式召回 delivery 队列用 auto bid
SPDM_ABTEST_BOOL(enable_live_target_delivery_use_auto_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_lcron_one_model_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(lcron_one_model_recall_quota, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_skip_expand_by_post_ecpm_unit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_expand_by_embedding, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_onemodel_recall_expand_by_layer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_choose_path_id_base_on_exp_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(one_model_recall_exp_group, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_adjust_effect_layer_quota_dynamically, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(effect_layer_quota_precentage, ks::AbtestBiz::AD_DSP, 1.0);
// [zhanshenxin] 中台模型
SPDM_ABTEST_BOOL(enable_prerank_retention_to_middle_platform, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_key_action_to_middle_platform, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_ad_purchase_to_middle_platform, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_diff_middle_platform, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_convpay_deep_to_middle_platform, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_middle_platform, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(is_universe_inner_nobid_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_relevance_merge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_query_retarget_merge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_onemodel_dot_gtz, ks::AbtestBiz::AD_DSP);

// [xuyanyan03] 同城在 TableSearch 过滤本地行业广告
SPDM_ABTEST_BOOL(enable_inner_loop_splash_ad_queue, ks::AbtestBiz::AD_DSP);

// [huangwei06] 开屏多路召回
SPDM_ABTEST_BOOL(enable_splash_multi_retrieval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_rtb_prefetch_realtime_check, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(splash_realtime_exp_name, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(promotion_type_switch_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_INT64(splash_w_user_level_thr, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(splash_cache_ids_thr, ks::AbtestBiz::AD_DSP, 30);

// [zhaoyi13]
SPDM_ABTEST_BOOL(enable_fiction_not_na_user_recall, ks::AbtestBiz::AD_DSP);

// [mengfangyuan] 直播直投负反馈泛化过滤开关
SPDM_ABTEST_BOOL(enable_soft_negative_author_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reco_mutual_black_author_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_reco_mutual_black_author_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_negative_hetu_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_author_seq_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dup_photo_distinct_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_browsed_photo_freq_spec, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_browsed_photo_freq_spec_outer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_browsed_photo_freq_spec_sensitive_cat, ks::AbtestBiz::AD_DSP);

// [rentingyu] 用户体验相关开关
SPDM_ABTEST_BOOL(enable_user_exp_recall_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_neg_feed_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_block_second_industry_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_block_second_industry_id_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_block_kgame_sub_class, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_block_product_name, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_block_product_quick_app, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ue_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_block_account_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_low_quality_filter_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shop_score_exp_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(shop_score_exp_theshhold, ks::AbtestBiz::AD_DSP, 3.5);
SPDM_ABTEST_DOUBLE(low_quality_score_theshhold, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(low_quality_score_theshhold_v3, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(block_account_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(explore_user_tag, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_third_category_personalize_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_negative_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_negative_freq_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_category_negative_freq_outter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_category_negative_freq_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_personalize_freq_del_short_play, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(third_category_imp_personalize_check_seconds, ks::AbtestBiz::AD_DSP, ********);
SPDM_ABTEST_INT64(industry_negative_check_seconds, ks::AbtestBiz::AD_DSP, ********);
SPDM_ABTEST_INT64(industry_negative_check_seconds_v2, ks::AbtestBiz::AD_DSP, ********);
SPDM_ABTEST_INT64(category_negative_outter_check_seconds, ks::AbtestBiz::AD_DSP, ********);
SPDM_ABTEST_INT64(category_negative_inner_check_seconds, ks::AbtestBiz::AD_DSP, ********);
SPDM_ABTEST_INT64(industry_negative_played_duration, ks::AbtestBiz::AD_DSP, 2000);
SPDM_ABTEST_INT64(industry_negative_check_cnt, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_DOUBLE(industry_negative_check_ratio, ks::AbtestBiz::AD_DSP, 0.99);
SPDM_ABTEST_DOUBLE(category_negative_outter_check_ratio, ks::AbtestBiz::AD_DSP, 0.99);
SPDM_ABTEST_DOUBLE(category_negative_inner_check_ratio, ks::AbtestBiz::AD_DSP, 0.99);
SPDM_ABTEST_INT64(industry_negative_total_num_limit, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(category_negative_outter_total_num_limit, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(category_negative_inner_total_num_limit, ks::AbtestBiz::AD_DSP, 10);

SPDM_ABTEST_INT64(third_category_imp_personalize_played_duration, ks::AbtestBiz::AD_DSP, 3000);
SPDM_ABTEST_INT64(third_category_imp_personalize_check_cnt, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_BOOL(enable_product_negative_duration_personalize_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_category_negative_duration_personalize_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(negative_personalize_check_seconds, ks::AbtestBiz::AD_DSP, ********);
SPDM_ABTEST_INT64(negative_personalize_played_duration, ks::AbtestBiz::AD_DSP, 3000);
SPDM_ABTEST_INT64(negative_personalize_check_cnt, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_DOUBLE(negative_personalize_check_neg_ratio, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_BOOL(enable_inspire_dark_account_refund_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_explicit_account_refund_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_auto_account_refund_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inspire_auto_account_refund_filter_thres_conf_str, ks::AbtestBiz::AD_DSP, "10,0.5;5,0.8");
SPDM_ABTEST_DOUBLE(inspire_auto_account_refund_rate_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inspire_account_refund_rate_diff_root, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_live_purchased_freq_short_play, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_photo_purchased_freq_short_play, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_transfer_category_a_to_b, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(purchased_freq_short_play_check_seconds, ks::AbtestBiz::AD_DSP, ********);
SPDM_ABTEST_INT64(purchased_freq_short_play_played_duration, ks::AbtestBiz::AD_DSP, 3000);
SPDM_ABTEST_INT64(purchased_freq_short_play_check_cnt, ks::AbtestBiz::AD_DSP, 6);
SPDM_ABTEST_BOOL(enable_white_product_name, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_white_author_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_soft_photo_imp_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_soft_photo_imp_freq_check_seconds, ks::AbtestBiz::AD_DSP, ********);
SPDM_ABTEST_BOOL(enable_upgrade_behavior_interest, ks::AbtestBiz::AD_DSP);

// [houjian] 软广直播迁移参数
SPDM_ABTEST_BOOL(fanstop_live_admit_exp_filter, ks::AbtestBiz::AD_DSP);

// [guoqi03] 客户 / 素材天花板粗排开关
SPDM_ABTEST_BOOL(enable_corporation_ceiling_preranking, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_photo_ceiling_preranking, ks::AbtestBiz::AD_DSP);

// [yaokangping] 粗排爬坡新框架融合排序开关
SPDM_ABTEST_BOOL(enable_whitelist_guaranteed_preranking, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guaranteed_preranking_f4_wechat_game, ks::AbtestBiz::AD_DSP);
// [yechen05] 本地推短视频和直播扶持开关
SPDM_ABTEST_BOOL(enable_special_kwai_promotion_local_store_order, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_append_exp_trace, ks::AbtestBiz::AD_DSP);
// [wangyuan11] 磁力金牛直播 PC ROAS 不出摸底实验开关
SPDM_ABTEST_BOOL(enable_esp_live_roas_prerank_atv_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(roas_one_step_ecpm_boost_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(recall_live_auto_bid_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_esp_live_pc_roas_filter_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_mcb_append, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_append_tag_logging, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_ecology_rookie_merchant, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_individual_quota_for_inspire_merchant, ks::AbtestBiz::AD_DSP);
// [chencongzheng] 新客激励流量过滤实验
SPDM_ABTEST_BOOL(enable_new_merchant_filter, ks::AbtestBiz::AD_DSP);

// photo 策略数据切宽表实验
SPDM_ABTEST_BOOL(enable_wechat_game_tmp_trans, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(skip_fanstop_v2_tag_filter, ks::AbtestBiz::AD_DSP);

// [jiangfeng06] 磁力原生开关
SPDM_ABTEST_BOOL(enable_reward_outer_ranking_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_live_soft_queue, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_risk_split_ad_type, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_esp_live_one_step_recall_gpm_factor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_shield_industry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_filter_splash_outer_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_filter_splash_inner_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_filter_recall_path, ks::AbtestBiz::AD_DSP);
// ------------------------------abtest int64  参数定义  ----------------------------------
SPDM_ABTEST_INT64(dup_cost_filter_thresh, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(amd_originality_photo_thresh, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(ecology_merchant_cv_thresh, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_INT64(archimedes_target_retrieval_normal_feed_quota, ks::AbtestBiz::AD_DSP, 0);  // [mateng05] 内粉服务双列召回quota // NOLINT
// [daidongyang] 品牌电商粗排保送策略（短视频)
SPDM_ABTEST_INT64(prerank_ecom_brand_baosong_num, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(merchant_prerank_ecom_brand_tag, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(non_live_brand_append_quota, ks::AbtestBiz::AD_DSP, 30);

// [jiangfeng06] p2l 迁移召回单独 quota
SPDM_ABTEST_INT64(non_live_p2l_append_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(non_live_p2l_append_feed_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(non_live_p2l_append_reward_quota, ks::AbtestBiz::AD_DSP, 360);
SPDM_ABTEST_INT64(non_live_amd_reward_quota, ks::AbtestBiz::AD_DSP, 650);

// [caolin] 粉条直播新创意冷启动标签获取延迟最长时间
SPDM_ABTEST_INT64(fanstop_live_coldstart_tag_tolerance_minutes, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(new_inner_fanstop_rule_filter_quota, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(new_inner_fanstop_rule_filter_priority_quota, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(new_inner_fanstop_padding_quota, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(recall_cate3_length_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(recall_cid_cate3_length_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(boyle_group_test_tag, ks::AbtestBiz::AD_DSP, -999);
SPDM_ABTEST_INT64(recall_cate3_time_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(recall_cid_cate3_time_threshold, ks::AbtestBiz::AD_DSP, 0);
// [guoyuan03]  直播粗排精简 -- thresh
SPDM_ABTEST_INT64(prerank_live_diversity_unit_thresh, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(prerank_live_diversity_account_thresh, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(prerank_live_diversity_creative_thresh, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(prerank_live_diversity_live_id_thresh, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(fanstop_rank_quota, ks::AbtestBiz::AD_DSP, 130);
SPDM_ABTEST_INT64(live_prerank_max_quota, ks::AbtestBiz::AD_DSP, 60);
SPDM_ABTEST_INT64(inspire_live_prerank_max_quota, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(splash_inner_prerank_quota, ks::AbtestBiz::AD_DSP, 70);
SPDM_ABTEST_INT64(inspire_merchant_base_line_quota, ks::AbtestBiz::AD_DSP, 60);
// [huangzhaokai] 直播粗排粉丝保送数
SPDM_ABTEST_INT64(prerank_live_author_fans_baosong_num, ks::AbtestBiz::AD_DSP, 0);
// [huangzhaokai] 激励直播 p2l 独立 quota
SPDM_ABTEST_INT64(hard_photo_target_non_live_p2l_inspire_live_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(fanstop_photo_prerank_num, ks::AbtestBiz::AD_DSP, 240);
SPDM_ABTEST_INT64(session_cache_ttl, ks::AbtestBiz::AD_DSP, 180);
SPDM_ABTEST_INT64(session_cache_quota, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(outer_photo_hard_cache_quota, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(outer_photo_soft_cache_quota, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(inner_photo_hard_cache_quota, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(inner_p2l_hard_cache_quota, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(inner_photo_soft_cache_quota, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(inner_p2l_soft_cache_quota, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(cache_admit_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(cache_exit_quota, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(rank_cache_exit_quota, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(reward_outer_ranking_quota, ks::AbtestBiz::AD_DSP, 1500);
SPDM_ABTEST_INT64(cache_outer_extra_ranking_quota, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(cache_inner_hard_p2l_extra_ranking_quota, ks::AbtestBiz::AD_DSP, 50);
SPDM_ABTEST_INT64(cache_inner_hard_photo_extra_ranking_quota, ks::AbtestBiz::AD_DSP, 24);
SPDM_ABTEST_INT64(session_cache_target_type, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(amd_corporation_filter_thresh, ks::AbtestBiz::AD_DSP, 2);
// [tengwei] 粉条 photo 白名单频控时间
SPDM_ABTEST_INT64(fanstop_whitelist_freq_interval_min, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_BOOL(enable_lsp_geo_recall_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_lsp_geo_live_ids_to_prerank_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(locallife_skip_dark_inspire_cf_group_name, ks::AbtestBiz::AD_DSP, "default");

SPDM_ABTEST_INT64(amd_target_biz_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(fanstop_target_biz_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(amd_target_zigzag_ratio_vec, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(fanstop_target_zigzag_ratio_vec, ks::AbtestBiz::AD_DSP, "");
// [panshunda] 关注页切换 trigger 数据源
SPDM_ABTEST_BOOL(enable_follow_page_ad_author, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_70_path_reco_follow_set, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_page_ad_creative_path, ks::AbtestBiz::AD_DSP);
// [fangmeiling] 金牛直播 live target 一段式召回 ecpm or delivery 队列 quota
SPDM_ABTEST_INT64(live_target_ecpm_max_quota, ks::AbtestBiz::AD_DSP, 15);
SPDM_ABTEST_INT64(live_target_delivery_max_quota, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(native_ad_photo_delivery_min, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(spec_ad_photo_delivery_min, ks::AbtestBiz::AD_DSP, 120);
SPDM_ABTEST_INT64(incentive_native_ad_photo_delivery_min, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(soft_ad_spu_purchase_hour, ks::AbtestBiz::AD_DSP, 168);
SPDM_ABTEST_INT64(live_auhtor_max_prerank_num_delivery, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(InspireMerchant_prerank_max_quota, ks::AbtestBiz::AD_DSP, 120);
// [huangzhaokai] 移动端硬广短视频扶持通路标签
SPDM_ABTEST_INT64(mobile_hard_photo_guaranteed_preranking_tag, ks::AbtestBiz::AD_DSP, 200);
// [huangzhaokai] 移动端硬广引流扶持通路标签
SPDM_ABTEST_INT64(mobile_hard_p2l_guaranteed_preranking_tag, ks::AbtestBiz::AD_DSP, 42);
// [huangzhaokai] 移动端硬广引流粗排 quota
SPDM_ABTEST_INT64(hard_photo_target_p2l_mobile_independent_quota, ks::AbtestBiz::AD_DSP, 0);
// [huangzhaokai] 移动端硬广短视频粗排 quota
SPDM_ABTEST_INT64(hard_photo_target_photo_mobile_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(inspire_merchant_min_live_delivery_interval_seconds, ks::AbtestBiz::AD_DSP, 60);
SPDM_ABTEST_INT64(inspire_merchant_min_ad_size_for_live_ad, ks::AbtestBiz::AD_DSP, 200);
// [mengfangyuan] 直播直投负反馈泛化过滤阈值
SPDM_ABTEST_INT64(live_tag_negative_thre, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(live_tag_negative_check_day, ks::AbtestBiz::AD_DSP, 7);
SPDM_ABTEST_INT64(live_tag_negative_ban_day, ks::AbtestBiz::AD_DSP, 14);
SPDM_ABTEST_INT64(innerloop_follow_data_time_window, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(outer_soft_max_ad_count_v2, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outer_soft_rank_num_v2, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outer_soft_rank_base_line_num_v2, ks::AbtestBiz::AD_DSP, 0);
// ------------------------------abtest double 参数定义 ----------------------------------
SPDM_ABTEST_DOUBLE(high_quality_photo_adjust, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(univ_prerank_live_follow_ratio, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_DOUBLE(universe_live_follow_prerank_ecpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(live_paid_rate_default, ks::AbtestBiz::AD_DSP, 0.003);
SPDM_ABTEST_DOUBLE(live_ctr_default, ks::AbtestBiz::AD_DSP, 0.04);
SPDM_ABTEST_DOUBLE(live_ltv_default, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(live_merchant_follow_default, ks::AbtestBiz::AD_DSP, 0.005);
SPDM_ABTEST_DOUBLE(universe_inner_nobid_roas_discount, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_inner_nobid_cpa_discount, ks::AbtestBiz::AD_DSP, 1.0);
// [liuchangjin] 粗排排序计费完全分离计费比系数
SPDM_ABTEST_DOUBLE(preranking_thorough_bid_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);

// [tiangeng] 七日出价系列开关
SPDM_ABTEST_DOUBLE(bidding_7days_pay_times_prerank_ratio, ks::AbtestBiz::AD_DSP, 1.0);

// [guoyuan03] 直播粗排精简--weight
SPDM_ABTEST_DOUBLE(prerank_live_ensemble_ecpm_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(prerank_live_ensemble_e2e_weight, ks::AbtestBiz::AD_DSP, 1.0);
// [wubo05] 直播粗排精简--weight
SPDM_ABTEST_DOUBLE(prerank_pc_live_ctcvr_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(prerank_mobile_live_ctcvr_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(prerank_roas_live_ctcvr_factor, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(prerank_pc_roas_live_ctcvr_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(prerank_roi_live_ctcvr_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(prerank_mobile_live_ensemble_ecpm_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(prerank_mobile_live_ensemble_e2e_weight, ks::AbtestBiz::AD_DSP, 1.0);
// [wubo05] 直播粗排独立 index
SPDM_ABTEST_BOOL(enbale_prerank_live_idx_fix, ks::AbtestBiz::AD_DSP);
// [wubo05] 直播多样新控制
SPDM_ABTEST_BOOL(enable_prerank_live_startegy_live_ocpx, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_live_startegy_ocpx_two_step_pc, ks::AbtestBiz::AD_DSP);
// [wubo05] 直播粗排计费分离
SPDM_ABTEST_BOOL(enable_prerank_live_separate_billing, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(prerank_live_bid_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(prerank_live_auto_bid_weight, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_INT64(live_ocpx_pc_thresh, ks::AbtestBiz::AD_DSP, 100);
// 直播移动版粗排调价分转厘实验
SPDM_ABTEST_BOOL(enable_live_prerank_mobile_bid_exp, ks::AbtestBiz::AD_DSP);
// 直播粗排 NOBID 跳过计费分离
SPDM_ABTEST_BOOL(enable_live_prerank_nobid_exp, ks::AbtestBiz::AD_DSP);
// PC 直播粉丝召回保送
SPDM_ABTEST_BOOL(enable_pc_live_follow_retrieval_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(pc_follow_retrieval_max_num, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_BOOL(enable_pc_live_prerank_add_bonus, ks::AbtestBiz::AD_DSP);
// 移动直播添加 ECPM 粗排
SPDM_ABTEST_BOOL(enable_mobile_live_prerank_add_ecpm, ks::AbtestBiz::AD_DSP);
// [chencongzheng] 商品质量过滤阈值

// [lishaozhe] 高质量用户涨粉扶持参数

SPDM_ABTEST_DOUBLE(merchant_live_one_step_gpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(esp_live_one_step_gpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(non_order_type_default_gpm, ks::AbtestBiz::AD_DSP, 1);
// ------------------------------abtest string 参数定义 ----------------------------------
SPDM_ABTEST_STRING(ecom_seller_adjust_exp_name_adrank, ks::AbtestBiz::AD_DSP, "default");
// [xuyanyan03] 同城 zigzag_expand 系数
SPDM_ABTEST_STRING(nearby_zigzag_expand, ks::AbtestBiz::AD_DSP, "");
// ---------------------- 内粉专用 参数声明区域（防 git 冲突） -------------------
// ---------------------- abtest bool 参数声明 -------------------
// [nanning]
SPDM_ABTEST_BOOL(enable_fanstop_inspirelive_flashbomb_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_little_live_order_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_live_little_order_fc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_photo_reco_prerank, ks::AbtestBiz::AD_DSP);  // 请求 reco 粗排
// [luoqiang] 内粉本地生活订单确保按预期召回
SPDM_ABTEST_BOOL(enable_inner_polaris_order_pass, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_region_skip_filter_exp, ks::AbtestBiz::AD_DSP);
// [litianshi03] 内粉粗排 quota 独立设置实验
SPDM_ABTEST_BOOL(enable_archimedes_prerank_quota_exp, ks::AbtestBiz::AD_DSP);
// [litianshi03] 内粉 CPM 优先拿量通路
SPDM_ABTEST_BOOL(enable_inner_cpm_quota_exp, ks::AbtestBiz::AD_DSP);
// [litianshi03] 新粗排逻辑中优先订单判断
SPDM_ABTEST_BOOL(archimedes_new_prerank_priority_check, ks::AbtestBiz::AD_DSP);
// [litianshi03] 内粉过滤 p2l 暗投直播订单
SPDM_ABTEST_BOOL(archimedes_p2l_live_filter, ks::AbtestBiz::AD_DSP);
// [mateng05] 内粉关闭召回过滤 (forward_search 流控相关)
SPDM_ABTEST_BOOL(archimedes_disable_fanstop_fc_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(archimedes_disable_live_spam_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(archimedes_disable_live_bad_cpm_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(archimedes_disable_little_anchor_reduce, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(archimedes_enable_new_flow_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(archimedes_pacing_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_dark_skip_celebrity_label, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_cache_list_for_fanstop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_remove_negapp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_key_to_hash, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_thanos_switch_risk, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_INT64(fanstop_dragon_prerank_timeout, ks::AbtestBiz::AD_DSP, 30);
SPDM_ABTEST_INT64(dragon_outer_predict_prerank_timeout, ks::AbtestBiz::AD_DSP, 120);
SPDM_ABTEST_INT64(dragon_prerank_timeout, ks::AbtestBiz::AD_DSP, 20);

// [guoyuan03] diveristy
SPDM_ABTEST_BOOL(enable_kg_freq_rule, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_v3_freq_rule, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kgfix_freq_rule, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_category_live_freq_rule, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_category_freq_rule_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_customer_purchase_freq_check, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_category_photo_freq_rule, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(category_purchased_live_interval, ks::AbtestBiz::AD_DSP, 86400);
SPDM_ABTEST_INT64(category_purchased_interval_v2, ks::AbtestBiz::AD_DSP, 86400);
SPDM_ABTEST_INT64(category_purchased_photo_interval, ks::AbtestBiz::AD_DSP, 86400);
SPDM_ABTEST_BOOL(disable_other_purchase_freq_rule, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(purchase_freq_rule_change_spu_source, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_source_freq_rule, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(purchased_freq_rule_interval, ks::AbtestBiz::AD_DSP, 604800);
SPDM_ABTEST_BOOL(enable_shelf_merchant_category_freq_rule, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(shelf_merchant_category_freq_interval, ks::AbtestBiz::AD_DSP, 604800);

// [jianghao07] photo_id to reco
SPDM_ABTEST_BOOL(enable_pass_photo_id_to_adprerank, ks::AbtestBiz::AD_DSP);

// [jianghao07] Adjust frequency
SPDM_ABTEST_BOOL(enable_fans_adjust_frequency, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_prerank_fanstop_live_flash_bomb, ks::AbtestBiz::AD_DSP);

// [shishaoyun] fix fanstop_truncate_size 0.3 exp
SPDM_ABTEST_BOOL(enable_fix_fanstop_truncate_size, ks::AbtestBiz::AD_DSP);
// ---------------------- abtest int64 参数声明 -------------------
SPDM_ABTEST_INT64(select_high_quality_quota, ks::AbtestBiz::AD_DSP, 0);  // [caining] 素材扶优 quota 召回
SPDM_ABTEST_INT64(inner_fanstop_mutil_recall_reason, ks::AbtestBiz::AD_DSP,
                  318);  // [luoqiang] 内粉多路召回 reason
// [nanning]
SPDM_ABTEST_INT64(inner_request_reco_prerank_tm, ks::AbtestBiz::AD_DSP, 50);
SPDM_ABTEST_BOOL(enable_inner_dup_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_coldboot_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_coldboot_new_dragon, ks::AbtestBiz::AD_DSP);
// [zhangruyuan]
SPDM_ABTEST_INT64(download_ad_disk_free_threshold, ks::AbtestBiz::AD_DSP, 0);


// [litianshi03] 内粉本地生活作品 quota
SPDM_ABTEST_INT64(inner_trunc_target_max_region_photo, ks::AbtestBiz::AD_DSP, 300);
// [litianshi03] 内粉粗排作品 quota
SPDM_ABTEST_INT64(inner_trunc_target_max_rank_photo, ks::AbtestBiz::AD_DSP, 100);
// [litianshi03] 内粉粗排直播 quota
SPDM_ABTEST_INT64(inner_trunc_target_max_rank_live, ks::AbtestBiz::AD_DSP, 60);
// [litianshi03] 内粉已有 cpm 订单优先透出 reason
SPDM_ABTEST_INT64(inner_cpm_priority_reason, ks::AbtestBiz::AD_DSP, 120);

// [dengchijun] 主站发现页双列低观感治理
SPDM_ABTEST_INT64(ue_feed_explore_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(ue_feed_inner_explore_tag, ks::AbtestBiz::AD_DSP, 0);
// [dengchijun] 单列场景低观感软广过滤
SPDM_ABTEST_INT64(ue_thanos_soft_tag, ks::AbtestBiz::AD_DSP, 0);
// [dengchijun] 低质过滤豁免
SPDM_ABTEST_INT64(ue_limit_free_tag, ks::AbtestBiz::AD_DSP, 0);
// [liaibao] 内外循环软广观感低质过滤 100% 策略 tag
SPDM_ABTEST_INT64(ue_feed_softad_impress_tag, ks::AbtestBiz::AD_DSP, 0);
// [liaibao] 特批素材放行评估实验
SPDM_ABTEST_INT64(ue_pass_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_ue_specialpass_filter, ks::AbtestBiz::AD_DSP);
// [liaibao] 分级分发流量控制
SPDM_ABTEST_BOOL(enable_adue_risklevel_dispense, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adue_risklevel_risklabel_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adue_risklevel_base_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(adue_risklevel_dispense_exp_group, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adue_risklevel_kuaishou_limittag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adue_risklevel_nebula_limittag, ks::AbtestBiz::AD_DSP, 0);
// [liaibao] 分级分发评估实验 1.0
SPDM_ABTEST_BOOL(enable_adue_risklevel_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(adue_risklevel_first_industryid, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adue_risklevel_filter_levelthr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adue_risklevel_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
// [liaibao] 分级分发评估实验 3.0
SPDM_ABTEST_BOOL(enable_adue_risklevel_evaluate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adue_risklevel_eval_filter_skip, ks::AbtestBiz::AD_DSP);

// [liaibao] 规则引擎过滤
SPDM_ABTEST_BOOL(enable_ue_ruleengine_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(adue_ruleengine_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
// 单账户过滤策略开关
SPDM_ABTEST_BOOL(enable_ue_single_account_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(single_account_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
// [dengchijun] 高举报账户过滤
SPDM_ABTEST_BOOL(enable_ue_risk_account_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(flow_grading_report_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_ue_sensitive_user_filter, ks::AbtestBiz::AD_DSP);
// [dengchijun] 分级分发举报敏感用户过滤
SPDM_ABTEST_BOOL(enable_ue_grading_report_user_filter, ks::AbtestBiz::AD_DSP);
// [dengchijun] 举报敏感用户 redis
SPDM_ABTEST_STRING(user_report_prob_prefix, ks::AbtestBiz::AD_DSP, "user_tag_predict_report_submit:");
// [liaibao] 用户价值分层 redis 前缀
SPDM_ABTEST_STRING(user_value_group_tag_prefix, ks::AbtestBiz::AD_DSP, "user_value_w_group_tag:");
// 用户价值分层标签 redis 读取开关
SPDM_ABTEST_BOOL(enable_user_value_group_tag_redis, ks::AbtestBiz::AD_DSP);
// 用户价值体验保护策略开关
SPDM_ABTEST_BOOL(enable_user_value_riskphoto_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(user_value_exp_group, ks::AbtestBiz::AD_DSP, 0);

// [mateng05] 内粉粗排结果 quota
SPDM_ABTEST_INT64(archimedes_photo_to_rank_quota, ks::AbtestBiz::AD_DSP, 80);
SPDM_ABTEST_INT64(archimedes_live_to_rank_quota, ks::AbtestBiz::AD_DSP, 60);
SPDM_ABTEST_INT64(archimedes_priority_photo_to_rank_quota, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(archimedes_region_photo_to_rank_quota, ks::AbtestBiz::AD_DSP, 300);
SPDM_ABTEST_INT64(archimedes_general_priority_photo_to_rank_quota, ks::AbtestBiz::AD_DSP, 20);

// ---------------------- abtest double 参数声明 -------------------
// [nanning]
SPDM_ABTEST_DOUBLE(fanstop_inspirelive_flashbomb_filter_param, ks::AbtestBiz::AD_DSP, 0.8);
// [litianshi03] 内粉北极星订单占粗排 quota 比例
SPDM_ABTEST_DOUBLE(inner_trunc_target_max_polaris_ratio_photo, ks::AbtestBiz::AD_DSP, 0.2);
// [litianshi03] 内粉扶新弱作品作品占粗排 quota 比例
SPDM_ABTEST_DOUBLE(inner_bad_quality_ratio_photo, ks::AbtestBiz::AD_DSP, 0.2);
// [litianshi03] 内粉 CPM 优先拿量通路占粗排 quota 比例
SPDM_ABTEST_DOUBLE(inner_cpm_priority_ratio, ks::AbtestBiz::AD_DSP, 0.1);
// ------------------------------abtest string 参数定义 ------------
SPDM_ABTEST_STRING(inner_skip_ecpm_wtr_filter_orgid_exp, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(useless_path_ab_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(dcaf_rank_version_cache, ks::AbtestBiz::AD_DSP, "default");

// [liuzhaocheng] 粗排帕累托度+偏好两级排序开关
SPDM_ABTEST_BOOL(enable_prerank_epsp_sort, ks::AbtestBiz::AD_DSP);
// [liuzhaocheng] 粗排帕累托度-细粒度偏好是否使用 ensemble sort
SPDM_ABTEST_BOOL(enable_prerank_epsp_ensemble_preference, ks::AbtestBiz::AD_DSP);
// [liuzhaocheng] 粗排帕累托度-三通路置信度
SPDM_ABTEST_DOUBLE(prerank_epsp_cpm_confidence, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(prerank_epsp_e2e_confidence, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(prerank_epsp_ltr_confidence, ks::AbtestBiz::AD_DSP, 1.0);
// [liuzhaocheng] 粗排帕累托度+偏好两级排序最大截断数 (综合进精排个数和效率考虑)
SPDM_ABTEST_STRING(esp_mobile_hard_p2l_admit_ocpx_ab_string, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(esp_mobile_hard_photo_admit_ocpx_ab_string, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(prerank_epsp_trunc_max_num, ks::AbtestBiz::AD_DSP, 100000);
SPDM_ABTEST_BOOL(enable_prerank_ecpm_secondary_sort, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(prerank_ecpm_secondary_sort_exp_name, ks::AbtestBiz::AD_DSP, "ecpm_secondary_exp_1");
SPDM_ABTEST_STRING(first_industry_block_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(second_industry_block_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(second_industry_block_exp_tag_v2, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(block_kgame_sub_class_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(product_name_block_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(skip_media_ssp_sync_creative_blacklist, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(user_group_quota_outer_soft_exp, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(user_group_quota_outer_hard_exp, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(user_group_quota_inner_hard_exp, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(user_group_quota_inner_hard_reward_exp, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(user_group_quota_inner_soft_exp, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(fix_live_inner_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(cpm_thr_08_exp_name, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_sdpa_outer_ranking_extra_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(sdpa_outer_ranking_extra_quota, ks::AbtestBiz::AD_DSP, 200);

// 智能算力分配相关参数 Intelligent Computation Allocation
SPDM_ABTEST_BOOL(enable_target_prerank_ica_outer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target_prerank_ica_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target_prerank_ica_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(target_prerank_ica_user_value_model_name, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(target_prerank_ica_lambda_model_name, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(target_prerank_ica_enable_page_ids_exp_name, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(target_prerank_ica_fallback_exp_name, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(target_prerank_ica_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_ica_use_queue_lambda, ks::AbtestBiz::AD_DSP);
// 粗排到精排的 ICA 开关
SPDM_ABTEST_BOOL(enable_prerank_rank_ica_outer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_rank_ica_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_rank_ica_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(prerank_rank_ica_user_value_model_name, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(prerank_rank_ica_lambda_model_name, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(prerank_rank_ica_enable_page_ids_exp_name, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(prerank_rank_ica_fallback_exp_name, ks::AbtestBiz::AD_DSP, "rank");
SPDM_ABTEST_BOOL(enable_ica_y_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ica_y_weight_sigmoid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ica_w_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(ica_w_coef_rank, ks::AbtestBiz::AD_DSP, "1.0_1.0_1.0_1.0_1.0_1.0");
SPDM_ABTEST_STRING(ica_w_coef_prerank, ks::AbtestBiz::AD_DSP, "1.0_1.0_1.0_1.0_1.0_1.0");
SPDM_ABTEST_BOOL(enable_ica_y_weight_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ica_fallback_kconf_page, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ica_quota_shaping, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ica_quota_shaping_s, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ica_quota_shaping_r, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_ica_quota_shaping_conf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(ica_quota_shaping_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_ica_kconf_page, ks::AbtestBiz::AD_DSP);

// ---------------------- kconf int64 参数声明 -------------------

SPDM_ABTEST_BOOL(disable_universe_origin_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(prerank_ensemble_sqrt_sort, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(prerank_ensemble_log_sort, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_live_no_bid, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_prerank_purchase_conversion, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_cpm_accuracy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_seven_day_pay_times, ks::AbtestBiz::AD_DSP);

// [wangzhiqiang03] prerank ROI upgrade v202303
SPDM_ABTEST_BOOL(enable_prerank_roas_v202303, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_7roas_v202303, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_first_day_v202303, ks::AbtestBiz::AD_DSP);

// [wangzhiqiang03] prerank upgrade v202305
SPDM_ABTEST_BOOL(enable_prerank_other_v202305, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_lps_v202305, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_order_submit_v202306, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(pre_sc_k0_conv, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_conv2ltv1, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_conv2ltv7, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_conv2next, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_conv2key, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_lps, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_pay, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_os, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_rest, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_ltv1, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(pre_sc_k0_ltv7, ks::AbtestBiz::AD_DSP, 0.0);

// [wangzhiqiang03] prerank supplier score
SPDM_ABTEST_BOOL(enable_prerank_sp_sort, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_sp_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_sp_ctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(prerank_sp_weight0, ks::AbtestBiz::AD_DSP, 0.05);
SPDM_ABTEST_DOUBLE(prerank_sp_min0, ks::AbtestBiz::AD_DSP, 1e-7);

// [wangzhiqiang03] prerank cvr upgrade v202305
SPDM_ABTEST_BOOL(enable_prerank_cvr_order_submit_v202305, ks::AbtestBiz::AD_DSP);
// [koushaowei] fill prerank real_action_score
SPDM_ABTEST_BOOL(enable_prerank_real_action_score, ks::AbtestBiz::AD_DSP);

// //  [panjianfei03] 行业直播 rule filter 开关
// SPDM_ABTEST_BOOL(enable_skip_industry_live_diversity_rule, ks::AbtestBiz::AD_DSP);

// [zhangjunmin] ctcvr fix bid diff
SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff_ad_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff_ad_seven_day_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff_event_7_day_pay_times, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_ctcvr_fix_bid_diff_event_pay_first_day, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(live_item_addr_filter_rate, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_BOOL(enable_pass_addr_filter_follow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pass_addr_filter_kuaishou_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(live_item_addr_filter_max, ks::AbtestBiz::AD_DSP, 5);
// [yuanwei09] 体验分过滤作用用户层级
SPDM_ABTEST_BOOL(enable_ecology_score_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecology_score_filter_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ecology_badcomn_filter_thr, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_DOUBLE(ecology_refund_filter_thr, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_BOOL(enable_ecology_score_white_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecology_score_item_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ecology_score_white_list_max_size, ks::AbtestBiz::AD_DSP, 1500);
SPDM_ABTEST_DOUBLE(ecology_score_white_upper_bound, ks::AbtestBiz::AD_DSP, 3.0);
SPDM_ABTEST_DOUBLE(ecology_score_white_lower_bound, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(ecology_score_photo_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ecology_score_live_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ecology_score_p2l_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// [yuanwei09] 商品全站 quota 展开系数
SPDM_ABTEST_DOUBLE(merchant_storewide_quota_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// [wangkai26]
SPDM_ABTEST_INT64(shelf_hard_rank_quota, ks::AbtestBiz::AD_DSP, 700);
SPDM_ABTEST_INT64(shelf_soft_rank_quota, ks::AbtestBiz::AD_DSP, 700);
SPDM_ABTEST_INT64(shelf_all_rank_quota, ks::AbtestBiz::AD_DSP, 1400);
SPDM_ABTEST_BOOL(enable_shelf_ad_order_cate_freq_rule, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(shelf_ad_order_cate_freq_interval, ks::AbtestBiz::AD_DSP, 604800);
SPDM_ABTEST_INT64(shelf_dislike_freq_interval, ks::AbtestBiz::AD_DSP, 604800);
SPDM_ABTEST_BOOL(enable_shelf_item_global_variety_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(shelf_item_global_variety_quota, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(shelf_item_photoid_variety_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(shelf_item_ocpx_variety_quota, ks::AbtestBiz::AD_DSP, 7)
SPDM_ABTEST_INT64(shelf_item_material_variety_quota, ks::AbtestBiz::AD_DSP, 15);
SPDM_ABTEST_INT64(shelf_item_is_new_variety_quota, ks::AbtestBiz::AD_DSP, 2500);
SPDM_ABTEST_INT64(shelf_item_type_variety_quota, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_STRING(exp_tag_in_item_card_exp, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_shelf_holdout_all_exp1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_holdout_hard_exp1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_holdout_soft_exp1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_forprerank_variety, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(shelf_forprerank_item_vquota, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(shelf_forprerank_item_photo_vquota, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(shelf_forprerank_item_material_vquota, ks::AbtestBiz::AD_DSP, 3)
SPDM_ABTEST_STRING(exp_tag_in_item_card_exp_new, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_shelf_forprerank_variety_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_forprerank_variety_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_search_recall_up, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(shelf_search_recall_limit_nums, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_BOOL(enable_shelf_cate3_recall_up, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(shelf_cate3_recall_limit_nums, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(shelf_cate3_recall_limit_time, ks::AbtestBiz::AD_DSP, 86400);

// [zengjiangwei03] 全域点击重定向使用 seller_id
SPDM_ABTEST_BOOL(enable_click_retarget_by_seller_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_merchant_black_list_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fx_retarget_time_interval_hour, ks::AbtestBiz::AD_DSP, 24);

// [guochangyu]
SPDM_ABTEST_BOOL(enable_recall_local_life_dui_tou_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_recall_local_life_dui_tou_quota_limit, ks::AbtestBiz::AD_DSP);

// [xusimin]
SPDM_ABTEST_DOUBLE(live_storewide_hard_quota_ratio, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(live_storewide_soft_quota_ratio, ks::AbtestBiz::AD_DSP, 1.2);
// [zhangwei26] 内循环人群包召回
SPDM_ABTEST_BOOL(enable_orientation_retrieve_account, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_orientation_retrieve_account_base, ks::AbtestBiz::AD_DSP);
// [zhangwei26] 新客增长品牌 R3 人群召回
SPDM_ABTEST_BOOL(enable_new_customer_growth_brand_r3_recall, ks::AbtestBiz::AD_DSP);
// [jinhui05] 主站发现内循环直播间调频控
SPDM_ABTEST_BOOL(enable_kuaishou_explore_live_browsed_freq_min, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(kuaishou_explore_live_browsed_freq_min, ks::AbtestBiz::AD_DSP, 60);
SPDM_ABTEST_INT64(inner_explore_live_browsed_freq_min, ks::AbtestBiz::AD_DSP, 60);
// [zhaokun03] 内流 trgger emb 信息透传到 prerank
SPDM_ABTEST_BOOL(enable_fill_inner_explore_hetu_emb, ks::AbtestBiz::AD_DSP);
// [wuyonghong] 万合是否过滤掉无行业信息的广告
SPDM_ABTEST_BOOL(disable_unknown_industry_for_side_window, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_local_store_order, ks::AbtestBiz::AD_DSP);
// [jinhui05] 关注页过滤优化
SPDM_ABTEST_BOOL(enable_follow_author_freq_day_factor2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(follow_author_freq_day_factor, ks::AbtestBiz::AD_DSP, 1.0);
// [jinhui05] 关注页 CLS
SPDM_ABTEST_STRING(follow_cls_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_follow_cls_item_type_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(follow_cls_item_type, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_inner_bigcard_second_ads_filter, ks::AbtestBiz::AD_DSP);
// [zhaokun03] 万合 P 页皮肤广告过滤
SPDM_ABTEST_BOOL(enable_ads_filter_for_profile_skin, ks::AbtestBiz::AD_DSP);
// [zhaokun03] 万合流量黑名单账户过滤
SPDM_ABTEST_BOOL(enable_account_filter_for_wanhe, ks::AbtestBiz::AD_DSP);
// [zhaokun03] 万合创作者相关广告本地召回
SPDM_ABTEST_BOOL(enable_wanhe_creator_class_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wanhe_creator_class_recall_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invo_traffic_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(wanhe_local_recall_per_key_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_wanhe_creator_forbidden_industry_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_profile_relation_link_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wanhe_corporation_name_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nearline_fix_local_recall_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(nearline_local_recall_per_quota, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_STRING(nearline_local_quota_exp_tag,  ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_industry_explore_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_prerank_user_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_recall_new_material_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(seven_pay_max_skip_num_per_account, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(wechat_game_max_skip_num_per_account, ks::AbtestBiz::AD_DSP, 0);

// [duanxinning] 透传刷次信息到 prerank
SPDM_ABTEST_BOOL(enable_fill_refresh_times, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(feed_triggle_exp_tag,  ks::AbtestBiz::AD_DSP, "");
// [limiaochen] RTA 已安装过滤规则启用白名单
SPDM_ABTEST_BOOL(enable_rta_installed_filter, ks::AbtestBiz::AD_DSP);
// [wangyunli] 粗排七日付费次数扶持
SPDM_ABTEST_BOOL(enable_prerank_seven_day_pay_times_support, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(prerank_seven_day_pay_times_support_ratio, ks::AbtestBiz::AD_DSP, 0);
// [wangyunli] prerank ensemble sort new
SPDM_ABTEST_DOUBLE(prerank_e2e_ensemble_weight_new, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(prerank_cpm_ltr_ensemble_weight_new, ks::AbtestBiz::AD_DSP, 4.0);
SPDM_ABTEST_BOOL(enable_outer_live_punish_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_live_punish_code_blacklist, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_block_outer_digital_live, ks::AbtestBiz::AD_DSP);
// [wangyunli] prerank ltr new
SPDM_ABTEST_BOOL(enable_prerank_cpm_ltr_cmd_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_block_sale_depart_center, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(block_sale_depart_center_exp_tag, ks::AbtestBiz::AD_DSP, "base");
// [wangyunli] prerank ctcvr deprecate
SPDM_ABTEST_BOOL(enable_deprecate_outer_prerank_ctcvr_cmd, ks::AbtestBiz::AD_DSP);
// [wangyunli] outer delivery sampler 相关
SPDM_ABTEST_BOOL(enable_outer_delivery_sample_merge_ad_and_record_count, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_full_lib_sample_config_from_kconf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_sampler_pre_filter, ks::AbtestBiz::AD_DSP);
// [guojiangwei] 外循环素材跑量跳过多样性
SPDM_ABTEST_BOOL(enable_outer_high_cost_skip_diversity, ks::AbtestBiz::AD_DSP);
// [liuzhiqiang08]
SPDM_ABTEST_BOOL(enable_diversity1_unify, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_diversity2_sdpa_wo_product, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_diversity2_sdpa_city_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_diversity2_sdpa_city_id_all, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(diversity2_sdpa_city_id_industry2_list, ks::AbtestBiz::AD_DSP, "");
// [chenchen13] 透传 ad_rank playlet_name_hash
SPDM_ABTEST_BOOL(enable_mock_crm_center, ks::AbtestBiz::AD_DSP);
// [qianyangchao] 联盟小系统跳过已转化过滤
SPDM_ABTEST_BOOL(enable_universe_tiny_skip_converted_filter, ks::AbtestBiz::AD_DSP);
// [liuzichen05] ps 实验名称
SPDM_ABTEST_STRING(target_ps_adjust_exp_tag, ks::AbtestBiz::AD_DSP, "");
// [rongyu03] 游戏行业重定向开关
SPDM_ABTEST_BOOL(enable_game_olp_retarget_pv, ks::AbtestBiz::AD_DSP);
// [rongyu03] 游戏行业重定向新增通路开关
SPDM_ABTEST_BOOL(enable_game_olp_retarget_new_path, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_olp_retarget_new_path_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_upgrade_retarget_frame, ks::AbtestBiz::AD_DSP);
// [caijiawen] uax hold out 实验过滤开关
SPDM_ABTEST_BOOL(enable_uax_hold_out_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(uax_hold_out_filter_time, ks::AbtestBiz::AD_DSP, **********);
// [jiangjinling] na 小说重定向开关
SPDM_ABTEST_BOOL(enable_fiction_olp_unit_retarget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target_fiction_olp_account_retarget_new, ks::AbtestBiz::AD_DSP);
// [jiangjingling] na 小说重定向泛化优化
SPDM_ABTEST_BOOL(enable_fiction_retarget_generalize, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_retarget_limit_crowd_tag, ks::AbtestBiz::AD_DSP);
// [zhaoyi13] 差异化出价回传修复
SPDM_ABTEST_BOOL(enable_fix_dmp_cpa_bid, ks::AbtestBiz::AD_DSP);
// [guojiangwei] 新增激励明投图集本地召回
SPDM_ABTEST_BOOL(enable_reward_altas_local_recall, ks::AbtestBiz::AD_DSP);
// [lizemin] ps 请求清理 reco user info 细粒度裁剪
SPDM_ABTEST_BOOL(enable_reco_user_info_trim_for_ps, ks::AbtestBiz::AD_DSP);
// [limiaochen] 原生 & 短剧账号封禁过滤开关
SPDM_ABTEST_BOOL(enable_native_short_drama_ban_account_filter, ks::AbtestBiz::AD_DSP);
// [limiaochen] 外循环关注页接入原生广告
SPDM_ABTEST_BOOL(enable_follow_page_outer_native_ad, ks::AbtestBiz::AD_DSP);
// [limiaochen] 外循环关注页接入原生广告: 物料不允许外跳
SPDM_ABTEST_BOOL(is_not_deeplink, ks::AbtestBiz::AD_DSP);
// [limiaochen] 外循环关注页接入原生广告: 是否启用视频屏蔽过滤
SPDM_ABTEST_BOOL(enable_photo_shield_status_filter, ks::AbtestBiz::AD_DSP);
// [limiaochen] 外循环关注页接入原生广告: 是否放开关注页外循环行业直播物料软硬广限制
SPDM_ABTEST_BOOL(enable_fans_live_stream_promote_outer_live_follow_page, ks::AbtestBiz::AD_DSP);
// [panshunda] 外循环关注页接入原生广告: 物料不允许外跳 v2
SPDM_ABTEST_BOOL(is_not_deeplink_v2, ks::AbtestBiz::AD_DSP);
// [shuchengchun] 内循环 Zigzag Merge 是否使用 admit 检查
SPDM_ABTEST_BOOL(enable_zigzag_admit_check, ks::AbtestBiz::AD_DSP);
// [shuchengchun] 内循环短视频 AmdRanking 跳过 SortModelLayer
SPDM_ABTEST_BOOL(enable_sort_model_layer_skip, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(close_outer_live_ranking_truncate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kwai_minigame_add_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kwai_minigame_add_white_recall, ks::AbtestBiz::AD_DSP);
// [limiaochen] 审核中单元召回侧兜底
SPDM_ABTEST_BOOL(enable_audit_unit_filter, ks::AbtestBiz::AD_DSP);
// [wangshengyu] 一段式 ECPM 排序计费分离
SPDM_ABTEST_DOUBLE(recall_live_bid_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// [zhaoqilong] 行业 boost 召回通路开关
SPDM_ABTEST_BOOL(enable_outer_industry_boost, ks::AbtestBiz::AD_DSP);
// [zhaoqilong] 行业 boost 召回通路配置实验名
SPDM_ABTEST_STRING(outer_industry_boost_name, ks::AbtestBiz::AD_DSP, "");
// [limiaochen] P 页关系链预算范围改动开关
SPDM_ABTEST_BOOL(enable_new_profile_relation_link_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_loop_live_e2e_used_item_recall,
                 ks::AbtestBiz::AD_DSP);  // [limiaochen] 外循环行业直播下发率召回采样
SPDM_ABTEST_BOOL(enable_outer_loop_live_e2e_used_item_prerank,
                 ks::AbtestBiz::AD_DSP);  // [limiaochen] 外循环行业直播下发率粗排采样
// [limiaochen] 外循环行业直播 delivery sampler 相关, copyed from wangyunli
SPDM_ABTEST_BOOL(enable_outer_live_delivery_sample_merge_ad_and_record_count, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_live_full_lib_sample_config_from_kconf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_live_sampler_pre_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_prerank_add_carm_feature, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(enable_outer_prerank_carm_feature_hard_sample_count, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(enable_outer_prerank_carm_feature_soft_sample_count, ks::AbtestBiz::AD_DSP, 0);
// [limiaochen] 原生 & 短剧链路账号封禁，解除特定场景的硬广打压
SPDM_ABTEST_BOOL(enable_skip_ban_account_native_situation, ks::AbtestBiz::AD_DSP);
// [wangshengyu] 一段式 ECPM 召回支持移动版投放目标
SPDM_ABTEST_BOOL(enable_ecpm_support_mobile_ocpx, ks::AbtestBiz::AD_DSP);
// [wangzixu05] trigger 相关性，特征接入
SPDM_ABTEST_BOOL(enable_fill_inner_trigger_emb_feature_target, ks::AbtestBiz::AD_DSP);
// [limiaochen] 外循环行业直播反作弊过滤
SPDM_ABTEST_BOOL(enable_follow_page_shield_status_filter, ks::AbtestBiz::AD_DSP);
// [yangyifan10] 内循环关注页隐藏视频屏蔽
SPDM_ABTEST_BOOL(enable_outer_live_anti_cheat_filter, ks::AbtestBiz::AD_DSP);
// [limiaochen] 外循环发现页产品名黑名单
SPDM_ABTEST_BOOL(enable_outer_explore_product_name_blacklist, ks::AbtestBiz::AD_DSP);
// [lichunchi] 在 adtarget 中 set 账户创建时间
SPDM_ABTEST_BOOL(enable_set_account_create_time, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_purchase_sign, ks::AbtestBiz::AD_DSP);
// [limiaochen] 关注页外循环账户产品名黑名单过滤
SPDM_ABTEST_BOOL(enable_outer_follow_account_product_blacklist, ks::AbtestBiz::AD_DSP);
// [zhaijianwei] 外循环 X 账户黑名单过滤
SPDM_ABTEST_BOOL(enable_outer_x_account_blacklist, ks::AbtestBiz::AD_DSP);
// [limiaochen] 新账户自动屏蔽激励视频暗投
SPDM_ABTEST_BOOL(enable_new_account_encourage_video_filter, ks::AbtestBiz::AD_DSP);
// [wangshengyu] 排序计费分离兼容召回全站
SPDM_ABTEST_BOOL(enable_live_target_separate_billing, ks::AbtestBiz::AD_DSP);
// [yishijie] 频控策略增加点击次数开关
SPDM_ABTEST_BOOL(enable_pinkong_add_clickcnt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pinkong_add_blacks, ks::AbtestBiz::AD_DSP);
// [yishijie] 频控策略增加清零时间开关
SPDM_ABTEST_BOOL(enable_pinkong_add_clearhour, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pinkong_add_clearhour_set_percent, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pinkong_add_clickcnt_set_percent, ks::AbtestBiz::AD_DSP);
// [wangshengyu] 类目多样性控制
SPDM_ABTEST_INT64(live_ocpx_global_diversity_quota, ks::AbtestBiz::AD_DSP, 0);
// [limiaochen] 激励低商品分过滤-关注页开关
SPDM_ABTEST_BOOL(enable_shop_score_exp_filter_for_follow, ks::AbtestBiz::AD_DSP);
// [limiaochen] 循环 RuleEngine 不设置内循环 callback
SPDM_KCONF_BOOL(ad.adtarget3, enableOuterRuleEngineCloseInnerCallback);
// [limiaochen] 外循环关注页新圈定不外跳物料
SPDM_ABTEST_BOOL(enable_new_involved_outer_follow_ad, ks::AbtestBiz::AD_DSP);
// [limiaochen] UDF Rule 切换执行实验
SPDM_ABTEST_BOOL(enable_rule_engine_execute_udf_rule, ks::AbtestBiz::AD_DSP);
// [limiaochen] Ann Trigger 降级实验
SPDM_ABTEST_BOOL(enable_degrade_all_ann_trigger, ks::AbtestBiz::AD_DSP);
// [limiaochen] Redis Trigger 平迁标签召回实验
SPDM_ABTEST_BOOL(enable_redis_trigger_mapping_to_tag_retr, ks::AbtestBiz::AD_DSP);
// [limiaochen] 激励广告接入外循环软广开关
SPDM_ABTEST_BOOL(enable_inspire_admit_outer_soft, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_filter_outer_soft, ks::AbtestBiz::AD_DSP);
// [limiaochen] 标签召回通路治理实验
SPDM_ABTEST_BOOL(enable_close_marked_clean_path, ks::AbtestBiz::AD_DSP);
// [limiaochen] 切换到新的商品分字段
SPDM_ABTEST_BOOL(enable_switch_to_new_shop_score_filter, ks::AbtestBiz::AD_DSP);
// [limiaochen] spu_id_v3 切换到 photo_ecom_spu_id 实验
SPDM_ABTEST_BOOL(enable_switch_to_photo_ecom_spu_id, ks::AbtestBiz::AD_DSP);
// [limiaochen] 小说广告位 OCPX 行业黑名单过滤
SPDM_ABTEST_BOOL(enable_novel_ocpx_industry_blacklist, ks::AbtestBiz::AD_DSP);
// [zhangyuemeng] 本地推直播全站跳过计费分离
SPDM_ABTEST_BOOL(enable_LSPStorewide_recall_skip_bs, ks::AbtestBiz::AD_DSP);
// [sixianbo] 外循环新素材创意拆分 ab 参数
SPDM_ABTEST_BOOL(enable_outer_new_creative_new_param, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(outer_new_creative_exp_id, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(outer_new_creative_type_id, ks::AbtestBiz::AD_DSP, 31);
// [limiaochen] 修复行业直播粗排 bid 数据填充问题
SPDM_ABTEST_BOOL(enable_fix_outer_live_prerank_type, ks::AbtestBiz::AD_DSP);
// [liuzichen05] 外循环 sdpa 层 quota
SPDM_ABTEST_INT64(sdpa_layer_quota, ks::AbtestBiz::AD_DSP, 0);
// [liuzichen05] 外循环 sdpa 层是否在效果层前面
SPDM_ABTEST_BOOL(enable_sdpa_layer_before_effect_layer, ks::AbtestBiz::AD_DSP);
// [limiaochen] 反作弊超投治理
SPDM_ABTEST_BOOL(enable_anti_cheat_over_budget, ks::AbtestBiz::AD_DSP);
// [limiaochen] IAA 小说广告位版控需求
SPDM_ABTEST_BOOL(enable_iaa_novel_cover_param_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_novel_app_version_control, ks::AbtestBiz::AD_DSP);
// [limiaochen] IAA 小说插屏广告位额外准入控制
SPDM_ABTEST_BOOL(enable_iaa_novel_extra_filter, ks::AbtestBiz::AD_DSP);
// [limiaochen] 外循环生态海选修复实验
SPDM_ABTEST_BOOL(enable_outer_eco_preselect_fix, ks::AbtestBiz::AD_DSP);
// [limiaochen] IAP 小说 IOS 版本控制
SPDM_ABTEST_BOOL(enable_iap_novel_ios_version_control, ks::AbtestBiz::AD_DSP);
// [limiaochen] trigger_server 实验
SPDM_ABTEST_BOOL(enable_trigger_server_exp, ks::AbtestBiz::AD_DSP);
// [limiaochen] 外循环软广激励安卓客户端准入
SPDM_ABTEST_BOOL(enable_inspire_admit_outer_soft_3, ks::AbtestBiz::AD_DSP);
// [limiaochen] 外循环软广激励软广判断修复
SPDM_ABTEST_BOOL(enable_inspire_admit_outer_soft_4, ks::AbtestBiz::AD_DSP);
// [wanglei42] 清理给 prerank 的无用字段
SPDM_ABTEST_BOOL(remove_useless_field_to_prerank, ks::AbtestBiz::AD_DSP);
// [zhangzhen24] 外循环下发率软广全库负采样
SPDM_ABTEST_BOOL(enable_full_lib_sample_soft_queue, ks::AbtestBiz::AD_DSP);
// [liyu26] 隔离出价字段透传
SPDM_ABTEST_BOOL(enable_no_ltv_bid_target, ks::AbtestBiz::AD_DSP);
// [linyuhao03] CPL2 营业执照过滤是否下移到 rank
SPDM_ABTEST_BOOL(enable_cpl2_corp_filter_target2rank, ks::AbtestBiz::AD_DSP);
// [limiaochen] CPL2 基于单元召回
SPDM_ABTEST_BOOL(enable_cpl2_recall_by_unit, ks::AbtestBiz::AD_DSP);
// [limiaochen] 私信屏蔽状态过滤
SPDM_ABTEST_BOOL(enable_sixin_open_status_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kminigame_darkctrl_accountid_new, ks::AbtestBiz::AD_DSP);
// [lizuxin] cid 内循环高弹窗人群屏蔽
SPDM_ABTEST_BOOL(enable_innerloop_cid_cancel_user_drop, ks::AbtestBiz::AD_DSP);
// [huliren]
SPDM_ABTEST_BOOL(enable_adlist_cache_inner_soft, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_cache_info_mark, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adtarget3, enablePrerankSizeCacheDot);
// [lichen27]
SPDM_ABTEST_BOOL(enable_simplify_response_item_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_serialize_reco_user_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(adIncFansReservationExp, ks::AbtestBiz::XIFAN_PLAY_APPS);
// [yuhanzhang]
SPDM_ABTEST_BOOL(enable_first_industry_id_target_to_prerank_trans, ks::AbtestBiz::AD_DSP);
// [xuhao15]
SPDM_ABTEST_DOUBLE(ad_package_size_n_times, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(searcher_inner_photo_quota_up_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// [liuzichen05] 字段清理实验
SPDM_ABTEST_BOOL(remove_unused_fields, ks::AbtestBiz::AD_DSP);
// [liuzichen05] 内循环行业 redis 下线实验
SPDM_ABTEST_BOOL(remove_industry_redis, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_xifan_inspire_iaa, ks::AbtestBiz::XIFAN_PLAY_APPS);
// [liujiahui10] 近线相关
SPDM_ABTEST_BOOL(enable_nearline_change_photo_creativeids, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(filter_xifan_for_esp_new_author, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_population_account_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(searcher_inner_photo_search_quota_up_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// [huangting05]
SPDM_ABTEST_BOOL(enable_biz_name_quota_adjust, ks::AbtestBiz::AD_DSP);
// [wangshuaisong] 内循环 searcher path 间并行处理
SPDM_KCONF_BOOL(ad.adtarget3, enable_searcher_async_between_paths);
SPDM_ABTEST_DOUBLE(searcher_inner_photo_retrieval_quota_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// [liweijie06] 货架商城打开粉丝召回
SPDM_ABTEST_BOOL(enable_fans_recall_in_mall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_cate2_filter, ks::AbtestBiz::AD_DSP);

// [wangshuaisong] 关闭 AdPerf 打点
SPDM_KCONF_BOOL(ad.adtarget3, disableAllPerf);
}  // namespace ad_target
}  // namespace ks
