import copy

inner_photo_target_hard_recv_attrs = [
    "creative_id",
    "photo_id",
    "dup_photo_id",
    "cover_id",
    "dup_cover_id",
    "creative_material_type",
    "live_creative_type",
    "circulation_type",
    "target_id",
    "account_id",
    "campaign_id",
    "unit_id",
    "convert_type",
    "resource_id",
    "ad_queue_copy_type",
    "campaign_type",
    "study_status",
    "is_in_acc_explore_status",
    "explore_bid_type",
    "explore_put_type",
    "resource_ids_mask",
    "app_id",
    "unit_type",
    "agent_id",
    "account_type",
    "account_user_id",
    "live_user_id",
    "charge_tag",
    "duration",
    "index_live_stream_id",
    "live_event_time",
    "campaign_sub_type",
    "industry_id",
    "new_industry_id",
    "industry_id_v3",
    "city_product_id",
    "product_name",
    "new_industry_parent_id",
    "industry_parent_id_v3",
    "first_industry_id_v5",
    "second_industry_id_v5",
    "retrieval_rank_score",
    "retrieval_post_score",
    "new_creative_tag",
    "new_creative_tag_realtime",
    "is_expansion",
    "pred_score",
    "ecpm_ctr_score",
    "ecpm_cvr_score",
    "recall_sort_type",
    "ad_strategy_tag",
    "retrieval_tag_new",
    "multi_overlay_tag",
    "multi_overlay_tag_extend",
    "multi_retrieval_tag",
    "is_fake_soft_ad",
    "is_ai_hosting_ad",
    "dcaf_prerank_queue_type",
    "ad_queue_type",
    "scene_oriented_type",
    "target_type",
    "is_live",
    "bid_strategy",
    "payer_id",
    "item_id",
    "product_label",
    "item_type",
    "spu_id",
    "merchant_product_id",
    "extends_account_id",
    "extends_unit_id",
    "pay_type",
    "simultaneous_optimization_selected",
    "simultaneous_optimization_type",
    "product_label_type",
    "product_label_end_time",
    "pos_premium_flag",
    "thanos_premium_coefficient",
    "feed_premium_coefficient",
    "similar_photo_id",
    "similar_photo_score",
    "ecom_is_operation_author",
    "crm_agent_type",
    "material_similar_score",
    "material_similar_type",
    "material_similar_type_v2",
    "material_similar_type_v3",
    "material_similar_photo_id",
    "is_patchwork_ad",
    "is_mobile_soft_to_hard",
    "community_review_status",
    "is_new_merchant",
    "new_spu_tag",
    "product_min_price",
    "product_max_price",
    "delivery_addr_miss_match",
    "merchant_tag_recall_sid",
    "target_shard_type",
    "promotion_type",
    "is_one_step_recall_ad",
    "is_brand_boost_cljn",
    "bid",
    "index_cpa_bid",
    "index_roi_ratio",
    "speed_type",
    "ocpc_stage",
    "ocpx_action_type",
    "bid_type",
    "deep_conversion_type",
    "deep_conversion_bid",
    "enhance_conversion_type",
    "cpa_bid",
    "roi_ratio",
    "auto_bid_explore",
    "cost_ratio",
    "cost_cap_p",
    "cost_cap_q",
    "twin_bid_strategy",
    "pattern_type",
    "is_content_relative_creative",
    "is_photo_bd_creative",
    "creative_impression",
    "creative_photo_impression",
    "creative_cover_impression",
    "creative_photo_click",
    "is_skip_bid_server",
    "is_effect_first",
    "delivery_type",
    "prerank_type",
    "account_post_cpa_bid",
    "account_post_ltv",
    "auto_cpa_bid",
    "auto_cpa_bid_modify_tag",
    "group_idx",
    "exp_start_ts",
    "ad_bid_server_group_tag",
    "deep_group_idx",
    "auto_roas",
    "auto_roas_modify_tag",
    "auto_atv",
    "deep_flow_control_rate",
    "deep_min_coef",
    "deep_min_bid_coef",
    "backflow_cv",
    "auto_deep_cpa_bid",
    "calibration_ratio",
    "is_calibration",
    "price_bid_ratio",
    "price_separate_ratio",
    "price_ratio",
    "twin_bid_coef",
    "raw_auto_cpa_bid",
    "acc_cold_start_coef",
    "first_coef",
    "second_coef",
    "ctr",
    "delivery_rate",
    "conv_nextstay",
    "conversion_rate",
    "landingpage_submit_rate",
    "prerank_universe_deep",
    "conv2_purchase",
    "prerank_universe_invoke",
    "score",
    "ecpm",
    "pos_in_prerank",
    "droped_in_prerank",
    "ueq",
    "click2_lps",
    "prerank_thanos_xdt_order_paied",
    "click2_deep_rate",
    "click2_conv",
    "nebula_prerank_merchant_follow",
    "nebula_prerank_purchase",
    "server_show_cvr",
    "unify_ctr",
    "unify_cvr",
    "prerank_cpm_ltr",
    "ensemble_score",
    "play3s",
    "ntr",
    "deep_rate",
    "conv_key_action_rate",
    "feed_prerank_merchant_follow",
    "feed_prerank_purchase",
    "pred_ad_time",
    "merchant_ltv",
    "return_idx",
    "dynamic_from",
    "dynamic_idx",
    "dynamic_exp_tag",
    "photo_to_live_prerank_p3s",
    "shop_live_prerank_p3s",
    "photo_to_live_item_imp_wtr",
    "live_prerank_p3s_wtr",
    "prerank_p3s_goods_view",
    "prerank_p3s_pay_rate",
    "live_prerank_inroom_nebula",
    "live_prerank_inroom_feed",
    "photo_to_live_item_pay_rate",
    "live_prerank_p3s_ltv",
    "predict_auc_score_base",
    "predict_auc_score_exp",
    "live_prerank_stay_time",
    "ltr",
    "prerank_cmd_key_ids",
    "merchant_payer_id",
    "ad_target_trans_info",
    "ad_target_to_rank",
    "pec_style_ad_info",
    "key_action_switch",
    "crowd_tag",
    "new_creative_tags_realtime",
    "shop_coupon_config_id",
    "item_coupon_config_id",
    "shop_coupon_package_id",
    "item_coupon_package_id",
    "p_gmv_per_order_30",
    "author_recent_live_ctr",
    "author_recent_live_deliver_ctr",
    "author_recent_p2l_ctr",
    "author_recent_p2l_deliver_ctr",
    "nearline_auto_cpa_bid_average",
    "nearline_auto_roas_average",
    "rl_pay_amount_per_order",
    "nearline_atv",
    "trigger_relative_score",
    "trigger_relative_score_idx",
    "fans_top_extra_delivery",
    "inner_photo_queue_type",
    "strategy_tag",
    "ad_monitor_type",
]

unifiy_retrieval_enrich = [
    "account_compress_key",
    "bid_assist_type",
    "account_id",
    "account_type",
    "ad_queue_type",
    "ad_strategy_tag",
    "agent_id",
    "is_brand_order",
    "aggr_bidding_has_x_info",
    "aggr_bidding_x_account_id",
    "aggr_bidding_x_creative_id",
    "aggr_bidding_x_photo_id",
    "aggr_bidding_x_site_id",
    "aggregate_mode",
    "app_id",
    "author_id",
    "auto_manage",
    "bid",
    "bid_type",
    "campaign_id",
    "payer_id",
    "campaign_type",
    "city_product_id",
    "constraint_cpa",
    "convert_id",
    "convert_type",
    "cover_id",
    "cpa_bid",
    "cpa_bid_coef",
    "cpa_bid_original",
    "creative_id",
    "crm_center",
    "crowd_tag",
    "dcaf_prerank_queue_type",
    "dcaf_pv_score",
    "deep_conversion_type",
    "delivery_addr_miss_match",
    "dup_cover_id",
    "dup_photo_id",
    "ecom_item_ids",
    "ecpm_ctr_score",
    "ecpm_cvr_score",
    "explore_budget_status",
    "fans_top_liked_biz_type",
    "fd_ACCOUNT_merchant_goods_price",
    "first_industry_id",
    "first_industry_id_v4",
    "first_industry_id_v5",
    "game_retarget_purchase",
    "gmv_level",
    "rel_type",
    "material_similar_score",
    "hit_cache_level",
    "index_cpa_bid",
    "index_live_stream_id",
    "index_roi_ratio",
    "inner_photo_queue_type",
    "internal_invest_plan_id",
    "is_expansion",
    "is_game_live",
    "is_live",
    "is_local_life_mark",
    "is_mobile_soft_to_hard",
    "is_new_merchant",
    "is_no_sale_live_stream_ad",
    "is_one_step_recall_ad",
    "is_outer_loop_native",
    "is_outer_loop_native_photo",
    "is_pc_live",
    "is_small_seller_ocpc",
    "is_weak",
    "item_coupon_config_id",
    "item_coupon_package_id",
    "ks_brand_name_hash",
    "landing_page_component",
    "licence_id_num",
    "live_creative_type",
    "live_event_time",
    "live_is_domestic",
    "live_stream_id",
    "live_user_id",
    "material_similar_photo_id",
    "material_similar_type",
    "merchant_product_id",
    "merchant_tag_recall_sid",
    "multi_overlay_tag",
    "multi_overlay_tag_extend",
    "multi_retrieval_tag",
    "new_creative_tag",
    "new_creative_tag_realtime",
    "new_creative_tags_realtime",
    "ocpx_action_type",
    "path_shiba_exp_name",
    "pec_coin_switch",
    "pec_right_id",
    "pec_scene_id_for_item",
    "photo_id",
    "photo_source",
    "playlet_name_hash",
    "pred_score",
    "prerank_ad_addr",
    "prerank_retarget_tag",
    "price_bid_ratio",
    "product_max_price",
    "product_min_price",
    "project_ocpx_action_type",
    "real_time_type",
    "recall_path_uuids",
    "recall_sort_type",
    "reject_type",
    "replace_photo_id",
    "reported_trigger",
    "roi_ratio",
    "rta_feature_id",
    "rta_quality_score",
    "rta_source_type",
    "rta_strategy_id",
    "rta_trace_req_id",
    "second_industry_id_v5",
    "similar_photo_score",
    "spu_id",
    "storewide_incompatible_type",
    "study_status",
    "tag_type",
    "target_hard_fill_flag",
    "target_id",
    "target_shard_type",
    "target_soft_fill_flag",
    "unit_id",
    "unit_put_type",
    "user_id_for_item",
    "wechat_bid_ratio",
    "wechat_feature_id",
    "campaign_sub_type",
    "packed_boolean_value"
    "explore_put_type",
    "new_industry_parent_id",
    "photo_promotable_status",
    "product_label",
    "merchant_payer_id",
    "charge_tag",
    "unit_recent_24h_live_cost",
    "rl_pay_amount_per_order",
    "nearline_atv",
    "nearline_auto_roas_average",
    "nearline_auto_cpa_bid_average",
    "nearline_wt_auto_bid",
    "nearline_wt_auto_roas",
]
unifiy_prerank_enrich = [
    "first_industry_id",
    "explore_budget_status",
    "gmv_level",
    "rel_type",
    "explore_put_type",
    "begin_time",
    "ad_queue_type_for_copy",
    "ad_queue_type_for_prerank",
    "ad_strategy_tag",
    "allocated_budget_unify",
    "attr",
    "author_id_for_prerank",
    "auto_atv",
    "auto_dark_control",
    "auto_manage",
    "bid",
    "bid_price",
    "explore_put_type",
    "bid_strategy",
    "bid_strategy_group",
    "budget_smart_allocation",
    "cap_bid_type",
    "charge_mode",
    "cid_quality_level",
    "city_product_id",
    "clue_sdpa_category_id",
    "constraint_action_type",
    "cpa_bid_coef",
    "cpa_bid_for_prerank",
    "cpa_bid_int",
    "cpa_ratio",
    "creative_material_type",
    "deep_cpa_bid",
    "delivery_pred_score",
    "fans_count",
    "fanstop_category",
    "first_hetu_v3_category_id",
    "game_retarget_status",
    "industry_id_v3",
    "industry_parent_id_v3",
    "is_auto_campaign",
    "is_brand",
    "is_face_id_match",
    "is_fan_follow",
    "is_game_interest_retarget_product",
    "is_in_acc_explore_status",
    "is_inner_ad",
    "is_inner_green_channel",
    "is_live_for_prerank",
    "is_llm_user_prod_interest",
    "is_mix_list_one_step",
    "is_new_inner_delivery",
    "is_new_model_creative",
    "is_no_sale_live_stream_ad",
    "is_one_two_step_recall",
    "is_original_photo",
    "is_outer_industry_boost_account",
    "is_outer_industry_boost_product",
    "is_outer_loop_native",
    "is_outerloop_interest_retarget_product",
    "is_outerloop_nc_product_ad",
    "is_outerloop_nc_retrieval_ad",
    "is_spu_id_match",
    "item_type_for_prerank",
    "live_launch_type",
    "live_stream_id_for_prerank",
    "live_user_id_for_prerank",
    "merchant_product_id",
    "multi_model_opt_tag",
    "multi_overlay_tag",
    "multi_overlay_tag_extend",
    "new_creative_tag_for_prerank",
    "new_creative_tag_realtime",
    "new_creative_tags_realtime_3",
    "new_spu_tag",
    "outerloop_nc_model_score_ecpc",
    "payment_per_order",
    "photo_id",
    "pred_score",
    "project_ocpx_action_type",
    "promotion_type",
    "retrieval_ecpm",
    "retrieval_idx",
    "roi_ratio_for_prerank",
    "rta_bid",
    "rta_ratio",
    "rta_sta_tag",
    "scene_oriented_type",
    "second_industry_id_v6",
    "site_id",
    "speed",
    "storewide_incompatible_type",
    "unit_feature",
    "unit_source_type",
    "ad_dsp_type",
    "ad_monitor_type",
    "pos_in_ranking",
    "is_outer_loop_native",
]

prerank_sort_attr = [
    "prerank_reason",
    "prerank_idx",
]

auto_bid_info_attr = [
    "budget_coef",
    "calibration_ratio",
    "price_separate_ratio",
    "cost_ratio",
    "backflow_cv",
    "twin_bid_coef",
    "first_coef",
    "second_coef",
    "allocated_budget_unify",
    "price_ratio",
    "mcb_cpa_bid",
    "mcb_roi_ratio",
    "performance_fix_ratio",
    "product_cpa_bid",
    "deep_flow_control_rate",
    "deep_min_coef",
    "auto_deep_cpa_bid",
    "target_factor",
    "cpa_coef",
    "product_roi_ratio",
    "constraint_roi",
]


prerank_send_common_define = [
    "debug_creative_id",
    "lat",
    "strategy_crowd_info",
    "refresh_times",
    "deep_group_tag",
    "platform",
    "last_explore_pv_timestamp",
    "medium_sub_industry_id",
    "flow_type_str",
    "source_photo_id",
    "medium_sub_industry_id_v2",
    "medium_industry_id",
    "is_game_interest_retarget_request",
    "user_imp_product_hash_24h",
    "app_id",
    "medium_attribute",
    "buyer_effective_type",
    "consum_power_tag",
    "render_type",
    "is_lahuo_user",
    "ad_request_times",
    "device_info_and_interest",
    "rewarded_num",
    "region",
    "template_id",
    "is_merchant_inspire",
    "commercial_user_group_tag",
    {"name": "outer_soft_candidate_creative_id_list_prerank", "as": "outer_soft_candidate_creative_id_list"},
    {"name": "outer_hard_candidate_creative_id_list_prerank", "as": "outer_hard_candidate_creative_id_list"},
    "is_universe_fake_user",
    "inner_loop_fanstop_retrieval",
    "lsp_geo_live_list",
    "is_buyer_home_page_traffic",
    "show_order_inspire",
    "request_flow_type",
    "qa_tag",
    "fanstop_400_phone_call_user_score",
    "network",
    "user_gmv",
    "inner_buyer_ui_type",
    "platform_version",
    "gender",
    "last_explore_pv_page_size",
    "lng",
    "user_level_v2_risk",
    "user_imp_product_hash_30d",
    "device_stat_diskfree",
    "device_stat_memory",
    "medium_game_category_id",
    "industry_explore_list",
    "game_big_r_status",
    "refresh_direction",
    "is_splash_traffic",
    "language",
    "enable_dragon_predict",
    "fake_type",
    "follow_userid_list",
    "last_inspire_amount",
    "user_value_group_tag",
    "is_follow_live_inner",
    "is_follow_traffic",
    "order_conv_type",
    "parent_control_enabled",
    "is_unlogin_user",
    "medium_industry_id_v2",
    "prms_leads_outer_avg_pcvr",
    "group_tag_enum",
    "cooperation_mode",
    "prms_msg_outer_avg_pcvr",
    "is_follow",
    "is_outerloop_potential_nc_user",
    "target_deploy",
    "source",
    "updown_page_label",
    "is_inner_explore",
    "show_activate_inspire",
    "is_merchant_request",
    "current_view_count",
    "inner_trigger_hetu_emb_str",
    "interactive_form",
    "user_imp_product_cnt_24h",
    "is_shelf_merchant_traffic",
    "ad_style",
    "live_days",
    "rewarded_type",
    "is_outerloop_low_active_user",
    "deep_coef_group_tag_enum",
    "user_imp_product_cnt_30d",
    "first_screen_ad_shw_timestamp",
    "inner_trigger_item_hetu_tag",
    "pos_id",
    "device_stat_battery",
    "split_test_tag",
    {"name": "is_rewarded_for_prerank", "as": "is_rewarded"},
    {"name": "age_segment_for_prerank", "as": "age_segment"},
    {"name": "mini_game_ee_product_ids_list_for_prerank", "as": "mini_game_ee_product_ids_list"},
    "target_ps_adjust_exp_tag",
    "last_explore_pv_last_ad_pos",
    "trace_log_sampling_flag_v2_table",
]

prerank_send_common_pass = [
    "enable_prerank_rank_ica",
    "ica_rank_quota_inner_hard",
    "ica_rank_quota_inner_live",
    "ica_rank_quota_inner_soft",
    "ica_rank_quota_outer_hard",
    "ica_rank_quota_outer_live",
    "ica_rank_quota_outer_soft",
    "ad_router_ad_fantop_action",
    "ad_router_ad_user_realtime_action_attr_map_pb",
    "ad_router_ad_user_realtime_action_attr_map_bs",
    "ad_router_ad_user_realtime_action_pb",
    "ad_router_eb_ad_user_realtime_action_pb",
    "ad_router_ad_user_realtime_action_bs",
    "ad_router_ad_context",
    "ad_router_user_info_bin",
    "ad_router_truncated_merge_user_info_bs",
    "user_info.id",
    "user_info.device_id",
    "medium_uid",
    "sub_page_id",
    "page_id",
    "browse_type",
    "industry_explore_flow",
    "llsid",
    "is_kuaishou_thanos",
    "is_thanos_request",
    "is_inspire_mix",
    "age",
    "is_inspire_live",
    "is_mini_game_big_r_user",
    "is_trace_log",
    "is_one_model_nearline_flow",
    "target_trace_timestamp",
]

merge_trans_attr = [
    "fanstop_guaranteed_ratio",
    "cpm_bound",
    "fanstop_cost_ratio",
    "auto_bid_explore",
    "bid_gimbal_type",
    "bid_gimbal_ratio",
    "hit_white_account_explore_cost",
    "sub_bid_coef",
    "total_cost",
    "total_target_cost",
    "aigc_bid_coef",
    "use_ap_gimbal_ratio",
    "wentou_campaign_calibration",
    "account_wentou_gimbal_ratio_str",
    "account_wentou_bid_coef_hit_flag_str",
    "wentou_campaign_gimbal_ratio",
    "bonus_amount_coef",
    "target_factor",
    "dark_acc_increment_status",
    "is_new_model_creative",
    "plc_biz_types",
    "creative_id",
    "range_budget",
    "bid_strategy_group",
    "campaign_type",
    "bid_strategy",
    "is_inner_boost_bid",
    "wechat_feature_id",
    "ks_brand_name_hash",
    "reported_trigger",
    "cpa_bid_coef",
    "cpa_bid_original",
    "account_type",
    "merchant_product_id",
    "unit_put_type",
    "is_outer_loop_native_photo",
    "is_outer_loop_native",
    "replace_photo_id",
    "prerank_retarget_tag",
    "is_no_sale_live_stream_ad",
    "outer_loop_native",
    "playlet_name_hash",
    "recall_path_uuids",
    "path_shiba_exp_name",
    "pec_right_id",
    "item_id",
    "dcaf_pv_score",
    "auto_deep_cpa_bid_from_prerank",
    "bid_assist_type",
    "ecom_item_ids",
    "target_hard_fill_flag",
    "target_soft_fill_flag",
    "speed",
    "cpa_bid_from_prerank",
    "roi_ratio_from_prerank",
    "bid_type",
    "promotion_type",
    "bid",
    "real_time_type",
    "rta_source_type",
    "rta_strategy_id",
    "rta_quality_score",
    "rta_feature_id",
    "rta_trace_req_id",
    "landing_page_component",
    "reject_type",
    "project_ocpx_action_type",
    "storewide_incompatible_type",
    "second_industry_id_v5",
    "crm_center",
    "auto_manage",
    "ocpx_action_type",
    "min_distance_poi_id",
    "is_local_life_mark",
    "account_tag",
    "live_component_type",
    "first_industry_id_v5",
    "pec_coin_switch",
    "pec_scene_id_for_item",
    "game_retarget_purchase",
    "internal_invest_plan_id",
    "photo_source",
    "ad_select_type",
    "layer_select_type",
    "is_yanhang",
    "prerank_real_action_weight",
    "prerank_real_action_score",
    "real_action_idx",
    "ensemble_score_idx",
    "cid_quality_level",
    "purchase_ltv",
    "ad_direct_merchant_stage",
    "ad_direct_merchant_biz",
    "ad_direct_merchant_price_ratio",
    "ad_direct_merchant_bid_ratio",
    "fd_ACCOUNT_merchant_goods_price",
    "create_source_type",
    "material_similar_type",
    "auto_cpa_bid",
    "circulation_type",
    "payer_id",
    "auto_roas",
    "merchant_payer_id",
    "is_game_sdk",
]


prerank_common_fields = copy.deepcopy(prerank_send_common_define)
prerank_common_fields.extend(prerank_send_common_pass)

def get_rename_attr(attr_mapping: list):
    ret = []
    for mapping in attr_mapping:
        if isinstance(mapping, dict):
            ret.append(mapping["as"])
        else:
            ret.append(mapping)
    return ret

def remove_qa_tag(rpc_params):
    ret = copy.deepcopy(rpc_params)
    ret["recv_common_attrs"].remove("qa_tag")
    return ret

def get_base_attr(attr_mapping: list):
    ret = []
    for mapping in attr_mapping:
        if isinstance(mapping, dict):
            ret.append(mapping["name"])
        else:
            ret.append(mapping)
    return ret
