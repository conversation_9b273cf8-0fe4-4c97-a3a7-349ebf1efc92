#include "teams/ad/ad_target/common/ad_table_trigger.h"

#include <string>
#include <vector>
#include <algorithm>
#include <set>
#include <unordered_map>
#include <utility>
#include <unordered_set>

#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_table/table_env/loader.h"
#include "teams/ad/ad_table/table/dataframe.h"
#include "teams/ad/ad_table/table/key128.h"
#include "teams/ad/ad_target/utils/kconf/kconf.h"
#include "teams/ad/engine_base/tsm_context/data/multi_path_context.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/ad_target/utils/common/fanstop_util.h"
#include "teams/ad/ad_base/src/common/os_version.h"

namespace ks {
namespace ad_target {
const char kIndexAccountId[] = "campaign_id.account_id.id";
const char kIndexAccountType[] = "campaign_id.account_id.account_type";
const char kIndexAccountUserId[] = "campaign_id.account_id.user_id";
const char kIndexOcpxActionType[] = "ocpx_action_type";
const char kIndexLiveUserId[] = "live_user_id";
const char kIndexCampaignType[] = "campaign_id.type";
const char kIndexParentId[] = "campaign_id.account_id.industry_id_v3.parent_id";
const char kIndexProductId[] = "campaign_id.account_id.city_product_id";
const char kIndexUnitSpeed[] = "speed";

ks::ad_table::DataFrame* GetUnitDataFrame(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!session_data->get_data_frame_env()) {
    return nullptr;
  }
  auto* dataframe = session_data->mutable_unit_df();
  if (!dataframe) {
    return nullptr;
  }
  // unit 层级通过 unit_id 表展开
  auto& tag_opt = path->GetPathConfig();
  tag_opt.set_table_name("unit_id_index");
  return dataframe;
}

ks::ad_table::DataFrame* GetCreativeDataFrame(ContextData* session_data,
                                              engine_base::tsm::PathContext* path) {
  if (!session_data->get_data_frame_env()) {
    return nullptr;
  }
  auto* dataframe = session_data->mutable_creative_df();
  if (!dataframe) {
    return nullptr;
  }
  return dataframe;
}

void ExpandTokens(ks::ad_table::DataFrame& result, engine_base::tsm::PathContext* path) {    // NOLINT
  int32_t dataframe_size = result.RowSize();
  int32_t match_quota = path->GetPathConfig().TriggerTokenMaxQuota();

  auto search_num = std::min(match_quota, dataframe_size);
  auto& token_list = path->token_list;
  token_list.reserve(search_num);

  auto trunc_df = result.RandomTrunc(match_quota);
  const auto expand_id_desc = trunc_df.Column("id");
  for (size_t i = 0; i < trunc_df.RowSize(); ++i) {
    int64_t primary_key_id = trunc_df.GetValueUnsafe<int64_t>(trunc_df.Row(i), expand_id_desc);
    token_list.emplace_back(primary_key_id, 0.0);
  }
}

void ExpandUnitTailTokensExp(ks::ad_table::DataFrame& result, engine_base::tsm::PathContext* path) {    // NOLINT
  int32_t dataframe_size = result.RowSize();
  int32_t match_quota = path->GetPathConfig().TriggerTokenMaxQuota();

  auto search_num = std::min(match_quota, dataframe_size);
  auto& token_list = path->token_list;
  token_list.reserve(search_num);

  auto trunc_df = result.RandomTrunc(match_quota);
  const auto expand_id_desc = trunc_df.Column("id");
  const auto under_cost_recall_tail = AdKconfUtil::UnderCostRecallUnitTail();
  for (size_t i = 0; i < trunc_df.RowSize(); ++i) {
    int64_t primary_key_id = trunc_df.GetValueUnsafe<int64_t>(trunc_df.Row(i), expand_id_desc);
    if (under_cost_recall_tail && under_cost_recall_tail->count(primary_key_id % 100) > 0) {
      token_list.emplace_back(primary_key_id, 0.0);
      VLOG_EVERY_N(4, 1) << " ad_table_trigger xusimin_debug"
                        << " dataframe_size=" << dataframe_size
                        << " match_quota=" << match_quota
                        << " search_num=" << search_num
                        << " trunc_df_size=" << trunc_df.RowSize()
                        << " unit_id=" << primary_key_id;
    }
  }
}

void ExpandTokensFilter(ContextData* session_data, ks::ad_table::DataFrame& result, engine_base::tsm::PathContext* path) {    // NOLINT
  int32_t dataframe_size = result.RowSize();
  int32_t match_quota = path->GetPathConfig().TriggerTokenMaxQuota();

  result &= session_data->get_targeting_data().unit_roaring_bitmap.dataframe;
  auto search_num = std::min(match_quota, dataframe_size);
  auto& token_list = path->token_list;
  token_list.reserve(search_num);

  auto trunc_df = result.RandomTrunc(match_quota);
  if (path->GetPathConfig().PathId() == 138) {
    trunc_df = trunc_df.RandomTrunc(match_quota);
  }
  const auto expand_id_desc = trunc_df.Column("id");
  for (size_t i = 0; i < trunc_df.RowSize(); ++i) {
    int64_t primary_key_id = trunc_df.GetValueUnsafe<int64_t>(trunc_df.Row(i), expand_id_desc);
    token_list.emplace_back(primary_key_id, 0.0);
  }
}

void ExpandTokensWanheBidSort(ks::ad_table::DataFrame& result, engine_base::tsm::PathContext* path, int64_t per_quota) {    // NOLINT
  int32_t dataframe_size = result.RowSize();
  int32_t match_quota = path->GetPathConfig().TriggerTokenMaxQuota();

  auto search_num = std::min(match_quota, dataframe_size);
  auto& token_list = path->token_list;
  token_list.reserve(search_num);
  absl::flat_hash_map<int64_t, int32_t> key_map_nums;

  auto trunc_df = result.RandomTrunc(match_quota);
  const auto expand_id_desc = trunc_df.Column("id");
  const auto expand_bid_desc = trunc_df.Column("cpa_bid");
  const auto expand_duo_key_desc = trunc_df.Column("account_id");
  for (size_t i = 0; i < trunc_df.RowSize(); ++i) {
    auto row_id = trunc_df.Row(i);
    int64_t primary_key_id = trunc_df.GetValueUnsafe<int64_t>(row_id, expand_id_desc);
    int64_t cpa_bid = trunc_df.GetValueUnsafe<int64_t>(row_id, expand_bid_desc);
    int64_t dup_key_id = trunc_df.GetValueUnsafe<int64_t>(row_id, expand_duo_key_desc);
    if (key_map_nums[dup_key_id] < per_quota) {
      key_map_nums[dup_key_id] += 1;
      token_list.emplace_back(primary_key_id, cpa_bid);
    }
  }
  auto post_cmp = [&](const ks::engine_base::tsm::Token& a, const ks::engine_base::tsm::Token& b) -> bool {
    return std::make_tuple(a.token_score.score, a.token.subtoken_1) >
           std::make_tuple(b.token_score.score, b.token.subtoken_1);
  };
  std::sort(token_list.begin(), token_list.end(), post_cmp);
}

void AdTableAnalyzeIndexDataByNMLiveStream(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    kCreativeLiveCreativeType,
                    static_cast<int64_t>(kuaishou::ad::AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT));
  result |= all_valid_creative->Eq(kCreativeLiveCreativeType,
      static_cast<int64_t>(kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT));
  ExpandTokens(result, path);
}

void AdTableAnalyzeIndexDataByItemCardStream(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    kCreativeCreativeMaterialType,
                    static_cast<int64_t>(kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD));
  ExpandTokens(result, path);
}

void AdTableOuterNonAmdLiveDirectLiveRetrieval(ContextData* session_data,
                                               engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    kCreativeLiveCreativeType,
                    static_cast<int64_t>(kuaishou::ad::AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT));
  ExpandTokens(result, path);
}

void AdTableMcbRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("bid_type",
                                   static_cast<int64_t>(kuaishou::ad::AdEnum::MCB));
  ExpandTokens(result, path);
}

void AdTableAnalyzeIndexDataByWatchTimes(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  auto result = all_valid_unit->In(kOcpxActionType,
                                  {static_cast<int>(kuaishou::ad::EVENT_AD_WATCH_TIMES),
                                   static_cast<int>(kuaishou::ad::EVENT_AD_WATCH_5_TIMES),
                                   static_cast<int>(kuaishou::ad::EVENT_AD_WATCH_10_TIMES),
                                   static_cast<int>(kuaishou::ad::EVENT_AD_WATCH_20_TIMES)});
  ExpandTokens(result, path);
}

void AdTableAnalyzeIndexDataByRetarget(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (session_data->get_retarget_account_set().size() <= 0 ||
      session_data->get_retarget_account_set().size() >= 200) {
    return;
  }
  const auto &account_set = session_data->get_retarget_account_set();
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  std::vector<int64_t> account_vec;
  account_vec.assign(account_set.begin(), account_set.end());

  auto result = all_valid_unit->In(kIndexAccountId, account_vec);
  ExpandTokens(result, path);
}

void AdTableWechatGameRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  bool enable_kwai_minigame_add_recall = SPDM_enable_kwai_minigame_add_recall(session_data->get_spdm_ctx());
  bool enable_kwai_minigame_add_white_recall =
    SPDM_enable_kwai_minigame_add_white_recall(session_data->get_spdm_ctx());
  const auto& kminigame_recall_ocpx_type_set = AdKconfUtil::kminigameRecallOcpxTypeSet();
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  ad_table::DataFrame result;
  if (enable_kwai_minigame_add_white_recall) {
    if (kminigame_recall_ocpx_type_set == nullptr) {
      return;
    }
    if (!kminigame_recall_ocpx_type_set->empty()) {
      for (const auto& str_ocpc_action_type : *kminigame_recall_ocpx_type_set) {
        kuaishou::ad::AdActionType ocpc_action_type = kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
        kuaishou::ad::AdActionType_Parse(str_ocpc_action_type, &ocpc_action_type);
        result |= all_valid_unit->Eq(kIndexParentId, 1018).
          Eq(kIndexCampaignType, kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE).
          Eq(kIndexOcpxActionType, static_cast<kuaishou::ad::AdActionType>(ocpc_action_type));
      }
    }
  } else {
    if (enable_kwai_minigame_add_recall) {
      result |= all_valid_unit->Eq(kIndexParentId, 1018).
        Eq(kIndexCampaignType, kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE);
    }
  }
  ExpandTokens(result, path);
}

void AdTableGameInterestRetargetRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  std::vector<int64_t> product_ids;
  static ad_table::TargetKeyConvertor str_2_int64_;
  for (const auto &product_name : session_data->get_game_interest_retarget_product()) {
    product_ids.emplace_back(str_2_int64_(product_name));
  }
  if (product_ids.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In(kIndexProductId, product_ids);
  ExpandTokens(result, path);
}

void AdTableOuterNativeFictionRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  auto result = all_valid_unit->Eq(kIndexCampaignType, AdEnum::AD_KWAI_FICTION_PROMOTION);;
  ExpandTokens(result, path);
}

void AdTableFictionNotNaUserRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_fiction_not_na_user_recall(session_data->get_spdm_ctx())) {
    return;
  }
  // 获取用户兴趣题材列表
  if (session_data->get_ad_request()->ad_user_info().user_interest_fiction_tags_size() <= 0) {
    session_data->get_dot()->Count(1, "OuterloopFictionNotNaUserRetrieval", "empty_ud_data");
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  // 从 kconf 获取各题材 top unit_id 集合
  std::vector<int64_t> user_interest_tag_unit_ids;
  const auto& tag_unit_ids_conf = engine_base::AdKconfUtil::fictionNativeInterestExploreUnitIds();
  if (tag_unit_ids_conf == nullptr) {
    return;
  }
  const auto& tag_unit_id_map = tag_unit_ids_conf->data().tag_top_unit_id_map();
  if (tag_unit_id_map.empty()) {
    return;
  }
  for (const auto &tag : session_data->get_ad_request()->ad_user_info().user_interest_fiction_tags()) {
    auto iter = tag_unit_id_map.find(std::to_string(tag));
    if (iter != tag_unit_id_map.end()) {
      const auto& top_unit_ids = iter->second.unit_id();
      user_interest_tag_unit_ids.insert(user_interest_tag_unit_ids.end(),
                                        top_unit_ids.begin(),
                                        top_unit_ids.end());
    }
  }
  session_data->get_dot()->Interval(user_interest_tag_unit_ids.size(),
                                    "OuterloopFictionNotNaUserRetrievalUnitIdSize");
  auto search_num = path->GetPathConfig().TriggerTokenMaxQuota();
  auto &token_list = path->token_list;
  token_list.reserve(search_num);
  int32_t unit_cnt = 0;
  for (const auto& unit_id : user_interest_tag_unit_ids) {
    unit_cnt++;
    if (unit_cnt > search_num) {
      break;
    }
    token_list.emplace_back(unit_id, 0.0);
  }
  if (token_list.size() > 0) {
    session_data->get_dot()->Interval(token_list.size(),
      "OuterloopFictionNotNaUserRetrieval", "succeed");
  } else {
    session_data->get_dot()->Count(1, "OuterloopFictionNotNaUserRetrieval", "invalid_pv");
  }
}

void AdTableKaiGameIAPRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  // 快小游 iap/iaa 准入
  absl::flat_hash_set<int64_t> target_ocpx_action_type_set =
                {kuaishou::ad::AD_ROAS, kuaishou::ad::AD_PURCHASE,
                kuaishou::ad::EVENT_7_DAY_PAY_TIMES, kuaishou::ad::AD_SEVEN_DAY_ROAS};

  if (SPDM_enable_kai_game_iaap_retrieval(session_data->get_spdm_ctx())) {
    target_ocpx_action_type_set.insert(
              {kuaishou::ad::AD_ROAS_IAAP});
  }
  if (SPDM_enable_kai_game_iaa_retrieval(session_data->get_spdm_ctx())) {
    target_ocpx_action_type_set.insert(
              {kuaishou::ad::AD_IAA_ROAS, kuaishou::ad::EVENT_KEY_INAPP_ACTION});
  }
  if (SPDM_enable_mini_game_iaap_7r_retrieval(session_data->get_spdm_ctx())) {
    target_ocpx_action_type_set.insert({kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP});
  }
  std::vector<int64_t> target_ocpx_action_type_vec;
  target_ocpx_action_type_vec.assign(
              target_ocpx_action_type_set.begin(), target_ocpx_action_type_set.end());

  auto result = all_valid_unit->In(kIndexOcpxActionType, target_ocpx_action_type_vec).
                Eq(kIndexParentId, 1018).
                Eq(kIndexCampaignType, kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE);
  ExpandTokens(result, path);
}

void AdTableMiniGameBigRRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  if (!session_data->get_is_mini_game_big_r_user()) {
      session_data->get_dot()->Count(1, "AdTableMiniGameBigRRetrieval_BIGR");
    return;
  }
  session_data->get_dot()->Count(1, "AdTableMiniGameBigRRetrieval_INIT");
  if (session_data->mutable_mini_game_ee_product_ids_list()->empty()) {
    return;
  }
  session_data->get_dot()->Count(1, "AdTableMiniGameBigRRetrieval_ADMIT");
  std::vector<int64_t> target_product_ids = *(session_data->mutable_mini_game_ee_product_ids_list());
  auto result = all_valid_unit->In(kIndexProductId, target_product_ids);
  ExpandTokens(result, path);
}

void AdTableOuterLLMProdInterestRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  std::vector<int64_t> product_ids;
  static ad_table::TargetKeyConvertor str_2_int64;
  if (session_data->get_ad_request()->ad_user_info().llm_user_prod_interest_list_size() <= 0) {
    return;
  }
  for (const auto &product_name :
        session_data->get_ad_request()->ad_user_info().llm_user_prod_interest_list()) {
    product_ids.emplace_back(str_2_int64(product_name));
  }
  if (product_ids.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In(kIndexProductId, product_ids);
  ExpandTokens(result, path);
}

void AdTableOuterloopInterestRetargetRetrieval(ContextData* session_data,
                                               engine_base::tsm::PathContext* path) {
  std::vector<int64_t> product_ids;
  static ad_table::TargetKeyConvertor str_2_int64_;
  for (const auto &product_name : session_data->get_outerloop_interest_retarget_product()) {
    product_ids.emplace_back(str_2_int64_(product_name));
  }
  if (product_ids.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In(kIndexProductId, product_ids);
  ExpandTokens(result, path);
}

void AdTableOuterPopRecruitLiveRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  auto result = all_valid_unit->In(kIndexCampaignType,
      {kuaishou::ad::AdEnum::AD_POP_RECRUIT_PHOTO, kuaishou::ad::AdEnum::AD_POP_RECRUIT_LIVE});
  ExpandTokens(result, path);
}

void AdTableSelfServiceRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  auto result = all_valid_unit->Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_SELF_SERVICE);
  ExpandTokens(result, path);
}

void AdTableAnalyzeIndexDataByPureSoft(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  auto result = all_valid_unit->Eq("queue_type", ks::ad_table::deprecated::QueueType::kSoft);
  ExpandTokens(result, path);
}

void AdTableRewardAtlasRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  // 判断 resource_id 筛选激励明投
  // auto result_unit = all_valid_unit->In(kResourceIds, {24});
  // 判断 creative_material_type 筛选图集广告
  auto result = all_valid_creative->Eq(
      kCreativeCreativeMaterialType,
      static_cast<int64_t>(kuaishou::ad::AdEnum_CreativeMaterialType_ATLAS));
  ExpandTokens(result, path);
}

void AdTableMessageRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::string efficient_groups_idx =
                    SPDM_private_message_retarget_retrieval_efficient_group(session_data->get_spdm_ctx());

  const auto& orientations = session_data->get_target_profiler().user_orientation_set;
  if (orientations.empty()) {
    return;
  }
  auto orientation_config = AdKconfUtil::PrivateMessageAndWechatRetargetWhiteList();
  if (!orientation_config) {
    return;
  }

  std::set<std::string> enabled_groups;
  if (orientation_config->data().enabled_groups().find(efficient_groups_idx) ==
                                orientation_config->data().enabled_groups().end()) {
    auto enabled_groups_iter = orientation_config->data().enabled_groups().find("default");
    enabled_groups.insert(enabled_groups_iter->second.group_list().begin(),
                                                      enabled_groups_iter->second.group_list().end());
  } else {
    auto enabled_groups_iter = orientation_config->data().enabled_groups().find(efficient_groups_idx);
    enabled_groups.insert(enabled_groups_iter->second.group_list().begin(),
                                                      enabled_groups_iter->second.group_list().end());
  }

  int ocpc_state = 0;
  int groups_num = 20;
  ad_table::DataFrame result;
  int64_t target_ocpx_action_type = kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT;
  for (const auto& group : orientation_config->data().groups()) {
    groups_num--;
    if (groups_num < 0) {
      break;
    }
    if (enabled_groups.find(group.first) == enabled_groups.end()) {
      continue;
    }
    int64_t orientation = group.second.orientation();
    if (orientations.contains(orientation)) {
      if (group.second.ocpc_action_type() == "LEADS_SUBMIT") {
        ocpc_state |= 1;
        target_ocpx_action_type = kuaishou::ad::LEADS_SUBMIT;
      } else if (group.second.ocpc_action_type() == "EVENT_WECHAT_CONNECTED") {
        target_ocpx_action_type = kuaishou::ad::EVENT_WECHAT_CONNECTED;
      } else if (group.second.ocpc_action_type() == "EVENT_PRIVATE_MESSAGE_SENT") {
        ocpc_state |= 2;
      } else {
        continue;
      }
      int ids_num = 40;
      std::vector<int64_t> industry_id_list;
      for (const auto& industry_id : group.second.second_industry_id()) {
        industry_id_list.emplace_back(industry_id);
        ids_num -= 1;
        if (ids_num <= 0) {
          break;
        }
      }
      result |= all_valid_unit->Eq(kIndexOcpxActionType, target_ocpx_action_type)
                    .In("campaign_id.account_id.second_industry_id_v5", industry_id_list);
    }
  }

  if ((ocpc_state & 1) == 0) {
    result |= all_valid_unit->In(kIndexOcpxActionType, {kuaishou::ad::LEADS_SUBMIT});
  }
  if ((ocpc_state & 2) == 0) {
    result |= all_valid_unit->In(kIndexOcpxActionType, {kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT});
  }
  ExpandTokens(result, path);
}

void AdTableCampaignAutoRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  auto result = all_valid_unit->Eq("campaign_id.auto_manage", kuaishou::ad::AdEnum::AUTO_MANAGE_OPEN).
                                Eq("campaign_id.auto_adjust", kuaishou::ad::AdEnum::AUTO_ADJUST_OPEN);
  ExpandTokens(result, path);
  return;
}

void AdTableTargetExpand(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  auto result = all_valid_unit->Eq("campaign_id.auto_manage", kuaishou::ad::AdEnum::AUTO_MANAGE_OPEN).
                                Eq("campaign_id.auto_adjust", kuaishou::ad::AdEnum::AUTO_ADJUST_OPEN);
  ExpandTokens(result, path);
  return;
}

void AdTableDachangLiveFansRetr(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto dachang_live_fans_author_set_ = AdKconfUtil::innerWhiteListAuthorSet();
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> follow_author_vec;
  if (dachang_live_fans_author_set_) {
    for (const auto& follow : session_data->get_ad_request()->reco_user_info().follow_list()) {
      if (dachang_live_fans_author_set_->count(follow.user().id()) > 0) {
        follow_author_vec.push_back(follow.user().id());
      }
    }
  }
  auto result = all_valid_unit->Eq(kIndexCampaignType, AdEnum::LIVE_STREAM_PROMOTE).
                                In(kIndexAccountUserId, follow_author_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableFollowAuthorRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_follow_page_outer_native_ad(session_data->get_spdm_ctx()) ||
      !session_data->get_pos_manager_base().IsFollowOuter()) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  std::vector<int64_t> rec_follow_list(session_data->get_reco_follow_set().begin(),
                                       session_data->get_reco_follow_set().end());
  auto result = all_valid_creative->In("photo_author_id", rec_follow_list);
  ExpandTokens(result, path);
  return;
}

void AdTableFollowAuthorRetrievalLive(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_follow_page_outer_native_ad(session_data->get_spdm_ctx()) ||
      !session_data->get_pos_manager_base().IsFollowOuter()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> rec_follow_list(session_data->get_reco_follow_set().begin(),
                                       session_data->get_reco_follow_set().end());
  auto result = all_valid_unit->In(kIndexLiveUserId, rec_follow_list) &
                all_valid_unit->Eq(kIndexCampaignType, AdEnum::FANS_LIVE_STREAM_PROMOTE);
  ExpandTokens(result, path);
  return;
}

void AdTableFollowKolRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_follow_page_outer_native_ad(session_data->get_spdm_ctx()) ||
      !session_data->get_pos_manager_base().IsFollowOuter()) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  std::vector<int64_t> rec_follow_list(session_data->get_reco_follow_set().begin(),
                                       session_data->get_reco_follow_set().end());
  auto result = all_valid_creative->In("kol_user_id", rec_follow_list);
  ExpandTokens(result, path);
  return;
}

void AdTableCPLIndustryAuthorRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto request_photo_author_id = session_data->get_ad_request()->home_page_user_id();
  auto industry_ids = session_data->get_ad_request()->ad_industry_id();
  std::vector<int64_t> industry_id_list;
  for (auto id : industry_ids) {
    industry_id_list.push_back(id);
  }
  auto is_service_tab =
      AdKconfUtil::serviceTabSubPageIds()->count(session_data->get_ad_request()->sub_page_id()) > 0;
  if (!is_service_tab) {
    auto result = all_valid_creative->In("unit_id.campaign_id.account_id.industry_id_v3", industry_id_list)
                      .Eq("cpl2", 1);
    ExpandTokens(result, path);
  } else {
    auto result = all_valid_creative->Eq("photo_author_id", request_photo_author_id).Eq("cpl2", 1);
    ExpandTokens(result, path);
  }
  return;
}

void AdTableCPLIndustryAuthorRetrievalByUnit(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  const auto& request_photo_author_id = session_data->get_ad_request()->home_page_user_id();
  const auto& industry_ids = session_data->get_ad_request()->ad_industry_id();
  std::vector<int64_t> industry_id_list;
  for (const auto& id : industry_ids) { industry_id_list.push_back(id); }
  bool is_service_tab =
      AdKconfUtil::serviceTabSubPageIds()->count(session_data->get_ad_request()->sub_page_id()) > 0;
  if (!is_service_tab) {
    auto result = all_valid_creative->In("unit_id.campaign_id.account_id.industry_id_v3", industry_id_list)
                      .Eq("unit_id.cpl2", 1);
    ExpandTokens(result, path);
  } else {
    auto result = all_valid_creative->Eq("photo_author_id", request_photo_author_id).Eq("unit_id.cpl2", 1);
    ExpandTokens(result, path);
  }
  return;
}

void AdTableMacIndustry(ContextData* session_data, engine_base::tsm::PathContext* path) {
  std::set<int64_t> mac_industry_set;
  for (auto id : session_data->get_ad_request()->ad_user_info().mac_outer_industry()) {
    if (id > 0) {
      mac_industry_set.insert(id);
    }
  }
  if (mac_industry_set.empty()) {
    return;
  }
  session_data->get_dot()->Interval(mac_industry_set.size(), "mac_industry_set");

  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> tmp_vec;
  tmp_vec.assign(mac_industry_set.begin(), mac_industry_set.end());
  std::vector<ad_table::Key128> index_keys;
  for (auto v : tmp_vec) { index_keys.push_back(ad_table::HashOf(v)); }
  auto result = all_valid_unit->In("account.second_industry_id_v5", index_keys)
                .In(kIndexAccountType, {kuaishou::ad::AdEnum::ACCOUNT_LSP, kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                        kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableMacGroupIdx(ContextData* session_data, engine_base::tsm::PathContext* path) {
  std::set<int64_t> group_idx_set;
  for (auto id : session_data->get_ad_request()->ad_user_info().mac_outer_group_idx()) {    // NOLINT
    if (!id.empty()) {
      int64_t idx = 0;
      if (absl::SimpleAtoi(id, &idx) && idx != 0) {
        group_idx_set.insert(idx);
      }
    }
  }
  if (group_idx_set.empty()) {
    return;
  }
  session_data->get_dot()->Interval(group_idx_set.size(), "mac_group_idx_set");

  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> tmp_vec;
  tmp_vec.assign(group_idx_set.begin(), group_idx_set.end());
  auto result = all_valid_unit->In("campaign_id.account_id.wt_account_id.group_idx",
                                   tmp_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableReservation(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto author_set = AdKconfUtil::innerWhiteListReservationAuthorSet();
  if (author_set == nullptr || author_set->empty()) {
    return;
  }

  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> tmp_vec;
  tmp_vec.assign(author_set->begin(), author_set->end());
  auto result = all_valid_unit->In(kIndexLiveUserId, tmp_vec).
                                Eq(kIndexOcpxActionType, kuaishou::ad::AD_ITEM_CLICK).
                                Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_ESP).
                                Eq(kIndexCampaignType, AdEnum::MERCHANT_RECO_PROMOTE);
  ExpandTokens(result, path);
  return;
}

void AdTableLocalLifePopulation(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto population_set = AdKconfUtil::localLifePopulationRecalSet();
  if (population_set == nullptr || population_set->empty()) {
    return;
  }
  std::vector<int64_t> hit_population;
  const auto& user_info = session_data->get_ad_request()->ad_user_info();
  for (auto id : user_info.orientation()) {
    if (population_set->find(id) != population_set->end()) {
      hit_population.emplace_back(id);
    }
  }
  if (hit_population.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexOcpxActionType, kuaishou::ad::EVENT_ORDER_PAIED).
                                Eq(kIndexCampaignType, AdEnum::FANS_LIVE_STREAM_PROMOTE);

  std::vector<ad_table::Key128> keys_vec;
  for (auto& population : hit_population) {
    keys_vec.push_back(ad_table::HashOf(population));
  }
  result &= all_valid_unit->In("PopulationTargetModify", keys_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableLocalLifeAllPhoto(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexCampaignType,
                                   AdEnum::KWAI_PROMOTION_LOCAL_STORE_ORDER);
  ExpandTokens(result, path);
  return;
}

void AdTableLocalLifeAllPhoto2Live(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexCampaignType, AdEnum::KWAI_PROMOTION_LOCAL_STORE_ORDER).
                                Eq(kIndexOcpxActionType, kuaishou::ad::EVENT_ORDER_PAIED);
  ExpandTokens(result, path);
  return;
}

void AdTableAdAppointmentReach(ContextData* session_data, engine_base::tsm::PathContext* path) {
  // 直播预约重触达
  if (session_data->get_ad_request()->ad_user_info().appointment_author_list_size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> appointment_author_vec;
  appointment_author_vec.reserve(
      session_data->get_ad_request()->ad_user_info().appointment_author_list().size());
  for (const auto &author : session_data->get_ad_request()->ad_user_info().appointment_author_list()) {
    appointment_author_vec.push_back(author.author_id());
  }
  auto result = all_valid_unit->In(kIndexLiveUserId, appointment_author_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableClientAiRerankReqRecallOldItem(ContextData* session_data, engine_base::tsm::PathContext* path) {
  // 端智能二次请求增加一路召回，召回老广告
  // 非二次请求跳过
  if (!(session_data->get_ad_request()->is_live_rerank_req() ||
    session_data->get_ad_request()->is_photo_rerank_req())) {
    return;
  }
  // 无原广告，跳过
  int64_t origin_cid = session_data->get_ad_request()->photo_rerank_req_info().creative_id();
  if (origin_cid == 0) {
    return;
  }
  auto& token_list = path->token_list;
  token_list.emplace_back(origin_cid, 0.0);
  return;
}

void AdTableSpuIdList7d(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& adx_bid_spu_id_list_7d =
    session_data->get_ad_request()->ad_user_info().adx_bid_spu_id_list_7d();
  if (adx_bid_spu_id_list_7d.size() <= 0) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  absl::flat_hash_set<int64_t> adx_bid_spu_id_7d_set;
  for (int i = 0; i < adx_bid_spu_id_list_7d.size(); i++) {
    if (adx_bid_spu_id_7d_set.size() >= max_len) {
      break;
    }
    adx_bid_spu_id_7d_set.insert(adx_bid_spu_id_list_7d[i]);
  }
  std::vector<int64_t> adx_bid_spu_id_7d_vec;
  adx_bid_spu_id_7d_vec.assign(adx_bid_spu_id_7d_set.begin(), adx_bid_spu_id_7d_set.end());
  auto result = all_valid_creative->In("unit_id.ocpx_action_type",
                                        {kuaishou::ad::CID_EVENT_ORDER_PAID, kuaishou::ad::CID_ROAS}).
                                      In("wt_photo_id.spu_id_v3", adx_bid_spu_id_7d_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableSpuIdList7dCid(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& adx_bid_spu_id_list_7d =
    session_data->get_ad_request()->ad_user_info().adx_bid_spu_id_list_7d();
  if (adx_bid_spu_id_list_7d.size() <= 0) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  absl::flat_hash_set<int64_t> adx_bid_spu_id_7d_set;
  for (int i = 0; i < adx_bid_spu_id_list_7d.size(); i++) {
    if (adx_bid_spu_id_7d_set.size() >= max_len) {
      break;
    }
    adx_bid_spu_id_7d_set.insert(adx_bid_spu_id_list_7d[i]);
  }
  std::vector<int64_t> adx_bid_spu_id_7d_vec;
  adx_bid_spu_id_7d_vec.assign(adx_bid_spu_id_7d_set.begin(), adx_bid_spu_id_7d_set.end());
  auto result = all_valid_creative->In("wt_photo_id.spu_id_v3", adx_bid_spu_id_7d_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableCatLevel3IdList7d(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& adx_bid_category_level3_id_list_7d =
    session_data->get_ad_request()->ad_user_info().adx_bid_category_level3_id_list_7d();
  if (adx_bid_category_level3_id_list_7d.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> adx_bid_category_level3_id_7d_set;
  for (int i = 0; i < adx_bid_category_level3_id_list_7d.size(); i++) {
    if (adx_bid_category_level3_id_7d_set.size() >= max_len) {
      break;
    }
    adx_bid_category_level3_id_7d_set.insert(adx_bid_category_level3_id_list_7d[i]);
  }
  std::vector<int64_t> adx_bid_category_level3_id_7d_vec;
  adx_bid_category_level3_id_7d_vec.assign(adx_bid_category_level3_id_7d_set.begin(),
      adx_bid_category_level3_id_7d_set.end());
  auto result = all_valid_unit->In("ocpx_action_type",
      {kuaishou::ad::CID_EVENT_ORDER_PAID, kuaishou::ad::CID_ROAS}).
      In("item_id.mmu_a_category3_id", adx_bid_category_level3_id_7d_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableCatLevel3IdList7dCid(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& adx_bid_category_level3_id_list_7d =
    session_data->get_ad_request()->ad_user_info().adx_bid_category_level3_id_list_7d();
  if (adx_bid_category_level3_id_list_7d.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> adx_bid_category_level3_id_7d_set;
  for (int i = 0; i < adx_bid_category_level3_id_list_7d.size(); i++) {
    if (adx_bid_category_level3_id_7d_set.size() >= max_len) {
      break;
    }
    adx_bid_category_level3_id_7d_set.insert(adx_bid_category_level3_id_list_7d[i]);
  }
  std::vector<int64_t> adx_bid_category_level3_id_7d_vec;
  adx_bid_category_level3_id_7d_vec.assign(adx_bid_category_level3_id_7d_set.begin(),
      adx_bid_category_level3_id_7d_set.end());
  auto result = all_valid_unit->In("item_id.mmu_a_category3_id", adx_bid_category_level3_id_7d_vec);
  ExpandTokens(result, path);
  return;
}

void AdTablePhotoSidL3(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& photo_sid_l3_list =
    session_data->get_ad_request()->ad_user_info().hist_action_sid_level3();
  if (photo_sid_l3_list.size() <= 0) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  absl::flat_hash_set<int64_t> sid_l3_set;
  for (int i = 0; i < photo_sid_l3_list.size(); i++) {
    if (sid_l3_set.size() >= max_len) {
      break;
    }
    sid_l3_set.insert(photo_sid_l3_list[i]);
  }
  std::vector<int64_t> sid_l3_vec;
  sid_l3_vec.assign(sid_l3_set.begin(), sid_l3_set.end());
  auto result = all_valid_creative->In("wt_photo_id.photo_semantic_id_level3", sid_l3_vec);
  ExpandTokens(result, path);
  return;
}

void AdTablePhotoSidL2(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& photo_sid_l2_list =
    session_data->get_ad_request()->ad_user_info().hist_action_sid_level2();
  if (photo_sid_l2_list.size() <= 0) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  absl::flat_hash_set<int64_t> sid_l2_set;
  for (int i = 0; i < photo_sid_l2_list.size(); i++) {
    if (sid_l2_set.size() >= max_len) {
      break;
    }
    sid_l2_set.insert(photo_sid_l2_list[i]);
  }
  std::vector<int64_t> sid_l2_vec;
  sid_l2_vec.assign(sid_l2_set.begin(), sid_l2_set.end());
  auto result = all_valid_creative->In("wt_photo_id.photo_semantic_id_level2", sid_l2_vec);
  ExpandTokens(result, path);
  return;
}

void AdTablePhotoSidL1(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& photo_sid_l1_list =
    session_data->get_ad_request()->ad_user_info().hist_action_sid_level1();
  if (photo_sid_l1_list.size() <= 0) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  absl::flat_hash_set<int64_t> sid_l1_set;
  for (int i = 0; i < photo_sid_l1_list.size(); i++) {
    if (sid_l1_set.size() >= max_len) {
      break;
    }
    sid_l1_set.insert(photo_sid_l1_list[i]);
  }
  std::vector<int64_t> sid_l1_vec;
  sid_l1_vec.assign(sid_l1_set.begin(), sid_l1_set.end());
  auto result = all_valid_creative->In("wt_photo_id.photo_semantic_id_level1", sid_l1_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableAdxAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& adx_seller_list =
    session_data->get_ad_request()->ad_user_info().adx_action_map_inner_seller_ids();
  if (adx_seller_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> adx_seller_set;
  for (int i = 0; i < adx_seller_list.size(); i++) {
    if (adx_seller_set.size() >= max_len) {
      break;
    }
    adx_seller_set.insert(adx_seller_list[i]);
  }
  std::vector<int64_t> seller_vec;
  seller_vec.assign(adx_seller_set.begin(), adx_seller_set.end());
  auto result = all_valid_unit->In(kIndexLiveUserId, seller_vec).
                              In(kIndexAccountType,
                                {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                  kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                  kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableMixRankAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (session_data->get_ad_request()->ad_user_info().mix_rank_input_hist_results_size() > 0) {
    auto* all_valid_unit = GetUnitDataFrame(session_data, path);
    if (!all_valid_unit) {
      return;
    }
    std::set<int64_t> author_set;
    int32_t max_len = 50;
    size_t len = session_data->get_ad_request()->ad_user_info().mix_rank_input_hist_results_size();
    const auto& hist_results = session_data->get_ad_request()->ad_user_info().mix_rank_input_hist_results();
    for (size_t i = 0; i < len; ++i) {
      const auto& hist_result = hist_results[i];
      size_t pv_len = hist_result.item_id_size();
      if (pv_len != hist_result.author_id_size()) {
        continue;
      }
      for (size_t j = 0; j < pv_len; ++j) {
        if (hist_result.author_id()[j] <= 0) {
          continue;
        }
        author_set.insert(hist_result.author_id()[j]);
      }
      if (author_set.size() >= max_len) {
        break;
      }
    }
    if (author_set.size() <= 0) {
      return;
    }
    std::vector<int64_t> author_vec;
    author_vec.assign(author_set.begin(), author_set.end());

    auto result = all_valid_unit->In(kIndexLiveUserId, author_vec).
                                In(kIndexAccountType,
                                    {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  }
  return;
}

void AdTableCidAccount(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (session_data->get_ad_request()->ad_user_info().inner_account_item_ids_list_size() > 0) {
    auto* all_valid_unit = GetUnitDataFrame(session_data, path);
    if (!all_valid_unit) {
      return;
    }
    // cid account_id 映射关系
    auto account_id_redirect_map = AdKconfUtil::innerCidAccountRedirectConfig();
    if (!account_id_redirect_map) {
      return;
    }

    std::set<int64_t> photo_set;
    int32_t max_len = 50;
    size_t len = session_data->get_ad_request()->ad_user_info().inner_account_item_ids_list_size();
    const auto& hist_results = session_data->get_ad_request()->ad_user_info().inner_account_item_ids_list();
    for (size_t i = 0; i < len; ++i) {
      int64_t tmp_account_id = 0;
      if (absl::SimpleAtoi(hist_results[i], &tmp_account_id)) {
        for (auto iter = account_id_redirect_map->begin(); iter != account_id_redirect_map->end(); iter++) {
          std::vector<std::string> outer_account_id_list = absl::StrSplit(iter->second, ",");

          if (std::find(outer_account_id_list.begin(), outer_account_id_list.end(),
              std::to_string(tmp_account_id))
              != outer_account_id_list.end()) {
            photo_set.insert(std::stoll(iter->first));
          }
        }
      }
      if (photo_set.size() >= max_len) {
        break;
      }
    }
    if (photo_set.size() <= 0) {
      return;
    }
    std::vector<int64_t> photo_vec;
    photo_vec.assign(photo_set.begin(), photo_set.end());

    auto result = all_valid_unit->In(kIndexOcpxActionType,
                                    {kuaishou::ad::CID_EVENT_ORDER_PAID, kuaishou::ad::CID_ROAS}).
                                  In("campaign_id.account_id.id", photo_vec);
    ExpandTokens(result, path);
  }
  return;
}

void AdTableCidDupPhoto(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (session_data->get_ad_request()->ad_user_info().inner_dup_photo_item_ids_list_size() > 0) {
    auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
    if (!all_valid_creative) {
      return;
    }
    std::set<int64_t> photo_set;
    int32_t max_len = 50;
    size_t len = session_data->get_ad_request()->ad_user_info().inner_dup_photo_item_ids_list_size();
    const auto& hist_results = session_data->get_ad_request()->ad_user_info().inner_dup_photo_item_ids_list();
    for (size_t i = 0; i < len; ++i) {
      int64_t tmp_dp_id = 0;
      if (absl::SimpleAtoi(hist_results[i], &tmp_dp_id)) {
        photo_set.insert(tmp_dp_id);
      }
      if (photo_set.size() >= max_len) {
        break;
      }
    }
    if (photo_set.size() <= 0) {
      return;
    }
    std::vector<int64_t> photo_vec;
    photo_vec.assign(photo_set.begin(), photo_set.end());

    auto result = all_valid_creative->In("unit_id.ocpx_action_type",
                                        {kuaishou::ad::CID_EVENT_ORDER_PAID, kuaishou::ad::CID_ROAS}).
                                      In("dup_photo_id", photo_vec);
    ExpandTokens(result, path);
  }
  return;
}

void AdTableMixRankPhoto(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (session_data->get_ad_request()->ad_user_info().mix_rank_input_hist_results_size() > 0) {
    auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
    if (!all_valid_creative) {
      return;
    }
    std::set<int64_t> photo_set;
    int32_t max_len = 50;
    size_t len = session_data->get_ad_request()->ad_user_info().mix_rank_input_hist_results_size();
    const auto& hist_results = session_data->get_ad_request()->ad_user_info().mix_rank_input_hist_results();
    for (size_t i = 0; i < len; ++i) {
      const auto& hist_result = hist_results[i];
      size_t pv_len = hist_result.item_id_size();
      if (pv_len != hist_result.is_live_size()) {
        continue;
      }
      for (size_t j = 0; j < pv_len; ++j) {
        if (hist_result.is_live()[j] > 0) {
          continue;
        }
        if (hist_result.item_id(j) <= 0) {
          continue;
        }
        photo_set.insert(hist_result.item_id(j));
      }
      if (photo_set.size() >= max_len) {
        break;
      }
    }
    if (photo_set.size() <= 0) {
      return;
    }
    std::vector<int64_t> photo_vec;
    photo_vec.assign(photo_set.begin(), photo_set.end());

    auto result = all_valid_creative->In("photo_id", photo_vec);
    ExpandTokens(result, path);
  }
  return;
}

void AdTableAllDomainClickRealSeller(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto iter = click_info.find("real_seller_id");
  if (SPDM_enable_click_retarget_by_seller_id(session_data->get_spdm_ctx())) {
    iter = click_info.find("seller_id");
  }
  if (iter == click_info.end()) {
    return;
  }
  auto real_seller_list = iter->second.int_list_value();
  if (real_seller_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::set<int64_t> real_seller_set;
  for (int i = 0; i < real_seller_list.size(); i++) {
    real_seller_set.insert(real_seller_list[i]);
  }
  std::vector<int64_t> real_seller_vec;
  real_seller_vec.assign(real_seller_set.begin(), real_seller_set.end());

  auto result = all_valid_unit->In(kIndexLiveUserId, real_seller_vec).
                                In(kIndexAccountType,
                                    {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableOrderRealSeller(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& order_info = session_data->get_ad_request()->ad_user_info().colossus_order_data_map();
  auto iter = order_info.find("real_seller_id");
  if (iter == order_info.end()) {
    return;
  }
  auto real_seller_list = iter->second.int_list_value();
  if (real_seller_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::set<int64_t> real_seller_set;
  for (int i = 0; i < real_seller_list.size(); i++) {
    real_seller_set.insert(real_seller_list[i]);
  }
  std::vector<int64_t> real_seller_vec;
  real_seller_vec.assign(real_seller_set.begin(), real_seller_set.end());

  auto result = all_valid_unit->In(kIndexLiveUserId, real_seller_vec).
                                In(kIndexAccountType,
                                    {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableOrderSeller(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& order_info = session_data->get_ad_request()->ad_user_info().colossus_order_data_map();
  auto iter = order_info.find("seller_id");
  if (iter == order_info.end()) {
    return;
  }
  const auto& real_seller_list = iter->second.int_list_value();
  if (real_seller_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> real_seller_set;
  for (int i = 0; i < real_seller_list.size(); i++) {
    real_seller_set.insert(real_seller_list[i]);
  }
  std::vector<int64_t> real_seller_vec;
  real_seller_vec.assign(real_seller_set.begin(), real_seller_set.end());
  auto result = all_valid_unit->In(kIndexLiveUserId, real_seller_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableSearchPagePhoto(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& order_info =
    session_data->get_ad_request()->ad_user_info().colossus_search_action_item_data_map();
  auto iter = order_info.find("photo_id");
  if (iter == order_info.end()) {
    return;
  }
  auto photo_list = iter->second.int_list_value();
  if (photo_list.size() <= 0) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  std::set<int64_t> photo_set;
  for (int i = 0; i < photo_list.size(); i++) {
    photo_set.insert(photo_list[i]);
  }
  std::vector<int64_t> photo_vec;
  photo_vec.assign(photo_set.begin(), photo_set.end());
  auto result = all_valid_creative->In("photo_id", photo_vec).
                                In(kIndexAccountType,
                                    {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableSearchPageAuthor(ContextData* session_data,
  engine_base::tsm::PathContext* path) {
  const auto& order_info =
    session_data->get_ad_request()->ad_user_info().colossus_search_action_item_data_map();
  auto iter = order_info.find("author_id");
  if (iter == order_info.end()) {
    return;
  }
  const auto& real_seller_list = iter->second.int_list_value();
  if (real_seller_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> real_seller_set;
  for (int i = 0; i < real_seller_list.size(); i++) {
    real_seller_set.insert(real_seller_list[i]);
  }
  std::vector<int64_t> real_seller_vec;
  real_seller_vec.assign(real_seller_set.begin(), real_seller_set.end());
  auto result = all_valid_unit->In(kIndexLiveUserId, real_seller_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfAllDomainClickRealSeller(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto iter = click_info.find("real_seller_id");
  if (iter == click_info.end()) {
    return;
  }
  auto real_seller_list = iter->second.int_list_value();
  if (real_seller_list.size() <= 0) {
    return;
  }
  std::set<int64_t> real_seller_set;
  for (int i = 0; i < real_seller_list.size(); i++) {
    real_seller_set.insert(real_seller_list[i]);
  }
  std::vector<int64_t> real_seller_vec;
  real_seller_vec.assign(real_seller_set.begin(), real_seller_set.end());
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    kCreativeCreativeMaterialType,
                    static_cast<int64_t>(kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD)).
                    In("unit_id.live_user_id", real_seller_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableAllDomainClickSeller(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto iter = click_info.find("seller_id");
  if (iter == click_info.end()) {
    return;
  }
  const auto& real_seller_list = iter->second.int_list_value();
  if (real_seller_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> real_seller_set;
  for (int i = 0; i < real_seller_list.size(); i++) {
    real_seller_set.insert(real_seller_list[i]);
  }
  std::vector<int64_t> real_seller_vec;
  real_seller_vec.assign(real_seller_set.begin(), real_seller_set.end());
  auto result = all_valid_unit->In(kIndexLiveUserId, real_seller_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfAllDomainClickSeller(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto iter = click_info.find("seller_id");
  if (iter == click_info.end()) {
    return;
  }
  const auto& real_seller_list = iter->second.int_list_value();
  if (real_seller_list.size() <= 0) {
    return;
  }
  absl::flat_hash_set<int64_t> real_seller_set;
  for (int i = 0; i < real_seller_list.size(); i++) {
    real_seller_set.insert(real_seller_list[i]);
  }
  std::vector<int64_t> real_seller_vec;
  real_seller_vec.assign(real_seller_set.begin(), real_seller_set.end());
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    kCreativeCreativeMaterialType,
                    static_cast<int64_t>(kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD)).
                    In("unit_id.live_user_id", real_seller_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableAllDomainClickItem(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto iter = click_info.find("item_id");
  if (iter == click_info.end()) {
    return;
  }
  auto item_id_list = iter->second.int_list_value();
  if (item_id_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::set<int64_t> item_set;
  for (int i = 0; i < item_id_list.size(); i++) {
    item_set.insert(item_id_list[i]);
  }
  std::vector<int64_t> item_vec;
  item_vec.assign(item_set.begin(), item_set.end());
  auto result = all_valid_unit->In("item_id", item_vec).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfAllDomainClickItem(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto iter = click_info.find("item_id");
  if (iter == click_info.end()) {
    return;
  }
  auto item_id_list = iter->second.int_list_value();
  if (item_id_list.size() <= 0) {
    return;
  }
  std::set<int64_t> item_set;
  for (int i = 0; i < item_id_list.size(); i++) {
    item_set.insert(item_id_list[i]);
  }
  std::vector<int64_t> item_vec;
  item_vec.assign(item_set.begin(), item_set.end());
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    kCreativeCreativeMaterialType,
                    static_cast<int64_t>(kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD)).
                    In("unit_id.item_id", item_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableAllDomainClickItemLive(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto iter = click_info.find("item_id");
  if (iter == click_info.end()) {
    return;
  }
  const auto& item_id_list = iter->second.int_list_value();
  if (item_id_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> item_set;
  for (int i = 0; i < item_id_list.size(); i++) {
    item_set.insert(item_id_list[i]);
  }
  std::vector<int64_t> item_vec;
  item_vec.assign(item_set.begin(), item_set.end());
  auto result = all_valid_unit->In("live_wt_live_id.ecom_item_ids", item_vec).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfAllDomainClickItemLive(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto iter = click_info.find("item_id");
  if (iter == click_info.end()) {
    return;
  }
  const auto& item_id_list = iter->second.int_list_value();
  if (item_id_list.size() <= 0) {
    return;
  }
  absl::flat_hash_set<int64_t> item_set;
  for (int i = 0; i < item_id_list.size(); i++) {
    item_set.insert(item_id_list[i]);
  }
  std::vector<int64_t> item_vec;
  item_vec.assign(item_set.begin(), item_set.end());
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    kCreativeCreativeMaterialType,
                    static_cast<int64_t>(kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD)).
                    In("unit_id.live_wt_live_id.ecom_item_ids", item_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableEcomActionCate3(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  const auto& order_info = session_data->get_ad_request()->ad_user_info().colossus_order_data_map();
  auto click_iter = click_info.find("category_a");
  auto order_iter = order_info.find("category_a");
  auto click_time_iter = click_info.find("click_timestamp");
  auto order_time_iter = order_info.find("pay_order_time");
  int64 current_time_stamp = base::GetTimestamp() / 1000000;
  int64 cate3_cnt = 0;
  if (click_iter == click_info.end() && order_iter == order_info.end()) {
    return;
  }
  if (click_time_iter == click_info.end() && order_time_iter == order_info.end()) {
    return;
  }
  absl::flat_hash_set<int64_t> cate3_set;
  if (order_iter != order_info.end() && order_time_iter != order_info.end()) {
    const auto& order_cate_list = order_iter->second.int_list_value();
    const auto& order_time_list = order_time_iter->second.int_list_value();
    for (int i = 0; i < order_cate_list.size(); i++) {
      if (SPDM_enable_recall_limit_cate3_nums(session_data->get_spdm_ctx())
       && cate3_cnt < SPDM_recall_cate3_length_threshold(session_data->get_spdm_ctx())
       && i < order_time_list.size()
       && (current_time_stamp - order_time_list[i])
        <= SPDM_recall_cate3_time_threshold(session_data->get_spdm_ctx())) {
        const auto& ret = cate3_set.insert((order_cate_list[i]>>16) & 0xffff);
        if (ret.second) {
          cate3_cnt++;
        }
      } else {
        cate3_set.insert((order_cate_list[i]>>16) & 0xffff);  // 1 级类目 | 2 级类目 | 3 级类目 | 4 级类目
      }
    }
  }
  if (click_iter != click_info.end() && click_time_iter != click_info.end()) {
    const auto& click_cate_list = click_iter->second.int_list_value();
    const auto& click_time_list = click_time_iter->second.int_list_value();
    for (int i = 0; i < click_cate_list.size(); i++) {
      if (SPDM_enable_recall_limit_cate3_nums(session_data->get_spdm_ctx())
       && cate3_cnt < SPDM_recall_cate3_length_threshold(session_data->get_spdm_ctx())
       && i < click_time_list.size()
       && (current_time_stamp - click_time_list[i])
        <= SPDM_recall_cate3_time_threshold(session_data->get_spdm_ctx())) {
        const auto& ret = cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
        if (ret.second) {
          cate3_cnt++;
        }
      } else {
        cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
      }
    }
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> cate3_vec;
  cate3_vec.assign(cate3_set.begin(), cate3_set.end());
  if (SPDM_enable_select_by_unit_cost(session_data->get_spdm_ctx())) {
    auto result = all_valid_unit->In("item_id.mmu_a_category3_id", cate3_vec).
                                  Eq("wt_unit_id.cost_total_inner_unit_40_min", 1).
                                  In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  } else {
    auto result = all_valid_unit->In("item_id.mmu_a_category3_id", cate3_vec).
                                  Eq("item_id.order_cnt_product_1_day", 1).
                                  In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  }
  return;
}

void AdTableEcomActionCate3Cid(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  const auto& order_info = session_data->get_ad_request()->ad_user_info().colossus_order_data_map();
  auto click_iter = click_info.find("category_a");
  auto order_iter = order_info.find("category_a");
  auto click_time_iter = click_info.find("click_timestamp");
  auto order_time_iter = order_info.find("pay_order_time");
  int64 current_time_stamp = base::GetTimestamp() / 1000000;
  int64 cate3_cnt = 0;
  if (click_iter == click_info.end() && order_iter == order_info.end()) {
    return;
  }
  if (click_time_iter == click_info.end() && order_time_iter == order_info.end()) {
    return;
  }
  absl::flat_hash_set<int64_t> cate3_set;
  bool enable_cid = SPDM_enable_recall_limit_cid_cate3_nums(session_data->get_spdm_ctx());
  int64_t cid_length_threshold = SPDM_recall_cid_cate3_length_threshold(session_data->get_spdm_ctx());
  int64_t cid_time_threshold = SPDM_recall_cid_cate3_time_threshold(session_data->get_spdm_ctx());
  if (order_iter != order_info.end() && order_time_iter != order_info.end()) {
    const auto& order_cate_list = order_iter->second.int_list_value();
    const auto& order_time_list = order_time_iter->second.int_list_value();
    for (int i = 0; i < order_cate_list.size(); i++) {
      if (enable_cid) {
        if (cate3_cnt < cid_length_threshold
          && i < order_time_list.size()
          && (current_time_stamp - order_time_list[i]) <= cid_time_threshold) {
            const auto& ret = cate3_set.insert((order_cate_list[i]>>16) & 0xffff);
            if (ret.second) {
              cate3_cnt++;
            }
          }
      } else {
        cate3_set.insert((order_cate_list[i]>>16) & 0xffff);  // 1 级类目 | 2 级类目 | 3 级类目 | 4 级类目
      }
    }
  }
  if (click_iter != click_info.end() && click_time_iter != click_info.end()) {
    const auto& click_cate_list = click_iter->second.int_list_value();
    const auto& click_time_list = click_time_iter->second.int_list_value();
    for (int i = 0; i < click_cate_list.size(); i++) {
      if (enable_cid) {
        if (cate3_cnt < cid_length_threshold
          && i < click_time_list.size()
          && (current_time_stamp - click_time_list[i]) <= cid_time_threshold) {
            const auto& ret = cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
            if (ret.second) {
              cate3_cnt++;
            }
          }
      } else {
        cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
      }
    }
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> cate3_vec;
  cate3_vec.assign(cate3_set.begin(), cate3_set.end());
  if (SPDM_enable_select_by_cid_unit_cost_or_item_order(session_data->get_spdm_ctx())) {
    if (SPDM_enable_select_by_cid_unit_cost(session_data->get_spdm_ctx())) {
      auto result = all_valid_unit->In(kIndexOcpxActionType,
                                      {kuaishou::ad::CID_EVENT_ORDER_PAID, kuaishou::ad::CID_ROAS}).
                                    In("item_id.mmu_a_category3_id", cate3_vec).
                                    Eq("wt_unit_id.cost_total_inner_unit_40_min", 1).
                                    In(kIndexAccountType,
                                    {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                      kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                      kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
      ExpandTokens(result, path);
    } else {
      auto result = all_valid_unit->In(kIndexOcpxActionType,
                                      {kuaishou::ad::CID_EVENT_ORDER_PAID, kuaishou::ad::CID_ROAS}).
                                    In("item_id.mmu_a_category3_id", cate3_vec).
                                    Eq("item_id.order_cnt_product_1_day", 1).
                                    In(kIndexAccountType,
                                    {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                      kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                      kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
      ExpandTokens(result, path);
    }
  } else {
    auto result = all_valid_unit->In(kIndexOcpxActionType,
                                    {kuaishou::ad::CID_EVENT_ORDER_PAID, kuaishou::ad::CID_ROAS}).
                                  In("item_id.mmu_a_category3_id", cate3_vec).
                                  In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  }
  return;
}

void AdTableInnerItemTest(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  // item_test_stage = 1 COLD
  // scene_oriented_type = 31 跑品测品产品
  auto result = all_valid_unit->Eq("campaign_id.scene_oriented_type", 31).
                                Eq("wt_unit_id.item_test_stage", 1);
  ExpandTokens(result, path);
  return;
}

void AdTableInnerShelfExpressStorewide(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(kCreativeCreativeMaterialType,
    static_cast<int64_t>(kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD)).
                                    Eq("unit_id.campaign_id.scene_oriented_type", 24);
  ExpandTokens(result, path);
  return;
}

void AdTableShelfAllSearchSeller(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& seller_list =
    session_data->get_ad_request()->ad_user_info().shelf_search_action_author_id_list();
  if (seller_list.size() <= 0) {
    return;
  }
  absl::flat_hash_set<int64_t> seller_set;
  for (int i = 0; i < seller_list.size(); i++) {
    seller_set.insert(seller_list[i]);
  }
  std::vector<int64_t> real_seller_vec;
  real_seller_vec.assign(seller_set.begin(), seller_set.end());
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In(kIndexLiveUserId, real_seller_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfAllSearchItem(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& item_id_list =
    session_data->get_ad_request()->ad_user_info().shelf_search_action_item_id_list();
  if (item_id_list.size() <= 0) {
    return;
  }
  std::set<int64_t> item_set;
  for (int i = 0; i < item_id_list.size(); i++) {
    item_set.insert(item_id_list[i]);
  }
  std::vector<int64_t> item_vec;
  item_vec.assign(item_set.begin(), item_set.end());
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In("item_id", item_vec).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfCate3(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  const auto& order_info = session_data->get_ad_request()->ad_user_info().colossus_order_data_map();
  auto click_iter = click_info.find("category_a");
  auto order_iter = order_info.find("category_a");
  auto click_time_iter = click_info.find("click_timestamp");
  auto order_time_iter = order_info.find("pay_order_time");
  int64 current_time_stamp = base::GetTimestamp() / 1000000;
  int64 cate3_cnt = 0;
  if (click_iter == click_info.end() && order_iter == order_info.end()) {
    return;
  }
  if (click_time_iter == click_info.end() && order_time_iter == order_info.end()) {
    return;
  }
  absl::flat_hash_set<int64_t> cate3_set;
  if (order_iter != order_info.end() && order_time_iter != order_info.end()) {
    const auto& order_cate_list = order_iter->second.int_list_value();
    const auto& order_time_list = order_time_iter->second.int_list_value();
    for (int i = 0; i < order_cate_list.size(); i++) {
      if (SPDM_enable_shelf_cate3_recall_up(session_data->get_spdm_ctx())) {
        if (cate3_cnt < SPDM_shelf_cate3_recall_limit_nums(session_data->get_spdm_ctx())
          && i < order_time_list.size()
          && (current_time_stamp - order_time_list[i])
          <= SPDM_shelf_cate3_recall_limit_time(session_data->get_spdm_ctx())) {
          const auto& ret = cate3_set.insert((order_cate_list[i]>>16) & 0xffff);
          if (ret.second) {
            cate3_cnt++;
          }
        }
      } else {
        cate3_set.insert((order_cate_list[i]>>16) & 0xffff);
      }
    }
  }
  if (click_iter != click_info.end() && click_time_iter != click_info.end()) {
    const auto& click_cate_list = click_iter->second.int_list_value();
    const auto& click_time_list = click_time_iter->second.int_list_value();
    for (int i = 0; i < click_cate_list.size(); i++) {
      if (SPDM_enable_shelf_cate3_recall_up(session_data->get_spdm_ctx())) {
        if (cate3_cnt < SPDM_shelf_cate3_recall_limit_nums(session_data->get_spdm_ctx())
          && i < click_time_list.size()
          && (current_time_stamp - click_time_list[i])
          <= SPDM_shelf_cate3_recall_limit_time(session_data->get_spdm_ctx())) {
          const auto& ret = cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
          if (ret.second) {
            cate3_cnt++;
          }
        }
      } else {
        cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
      }
    }
  }
  std::vector<int64_t> cate3_vec;
  cate3_vec.assign(cate3_set.begin(), cate3_set.end());
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In("item_id.mmu_a_category3_id", cate3_vec).
                                Eq("item_id.is_hot_shelf_ad", 1).
                                In(kIndexAccountType,
                                {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                  kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                  kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfCate3Live(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  const auto& order_info = session_data->get_ad_request()->ad_user_info().colossus_order_data_map();
  auto click_iter = click_info.find("category_a");
  auto order_iter = order_info.find("category_a");
  auto click_time_iter = click_info.find("click_timestamp");
  auto order_time_iter = order_info.find("pay_order_time");
  int64 current_time_stamp = base::GetTimestamp() / 1000000;
  int64 cate3_cnt = 0;
  if (click_iter == click_info.end() && order_iter == order_info.end()) {
    return;
  }
  if (click_time_iter == click_info.end() && order_time_iter == order_info.end()) {
    return;
  }
  absl::flat_hash_set<int64_t> cate3_set;
  if (order_iter != order_info.end() && order_time_iter != order_info.end()) {
    const auto& order_cate_list = order_iter->second.int_list_value();
    const auto& order_time_list = order_time_iter->second.int_list_value();
    for (int i = 0; i < order_cate_list.size(); i++) {
      if (SPDM_enable_shelf_cate3_recall_up(session_data->get_spdm_ctx())) {
        if (cate3_cnt < SPDM_shelf_cate3_recall_limit_nums(session_data->get_spdm_ctx())
          && i < order_time_list.size()
          && (current_time_stamp - order_time_list[i])
          <= SPDM_shelf_cate3_recall_limit_time(session_data->get_spdm_ctx())) {
          const auto& ret = cate3_set.insert((order_cate_list[i]>>16) & 0xffff);
          if (ret.second) {
            cate3_cnt++;
          }
        }
      } else {
        cate3_set.insert((order_cate_list[i]>>16) & 0xffff);
      }
    }
  }
  if (click_iter != click_info.end() && click_time_iter != click_info.end()) {
    const auto& click_cate_list = click_iter->second.int_list_value();
    const auto& click_time_list = click_time_iter->second.int_list_value();
    for (int i = 0; i < click_cate_list.size(); i++) {
      if (SPDM_enable_shelf_cate3_recall_up(session_data->get_spdm_ctx())) {
        if (cate3_cnt < SPDM_shelf_cate3_recall_limit_nums(session_data->get_spdm_ctx())
          && i < click_time_list.size()
          && (current_time_stamp - click_time_list[i])
          <= SPDM_shelf_cate3_recall_limit_time(session_data->get_spdm_ctx())) {
          const auto& ret = cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
          if (ret.second) {
            cate3_cnt++;
          }
        }
      } else {
        cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
      }
    }
  }
  std::vector<int64_t> cate3_vec;
  cate3_vec.assign(cate3_set.begin(), cate3_set.end());
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In("live_wt_live_id.mmu_a_category3_id_list", cate3_vec).
                                Eq("item_id.is_hot_shelf_ad", 1).
                                In(kIndexAccountType,
                                {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                  kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                  kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableInnerUnderCost(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  int64_t cost_rate_thresh = AdKconfUtil::InnerRecallCostRateThresh();
  auto result = all_valid_unit->Eq(kIndexCampaignType, kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE).
                                In(kIndexOcpxActionType,
                                  {kuaishou::ad::EVENT_ORDER_PAIED, kuaishou::ad::AD_MERCHANT_ROAS}).
                                In("campaign_id.scene_oriented_type",
                                  {kuaishou::ad::AdEnum::ORIENTED_DEFAULT_TYPE,
                                    kuaishou::ad::AdEnum::ORIENTED_FANS_GROWTH,
                                    kuaishou::ad::AdEnum::ORIENTED_FANS_REACH,
                                    kuaishou::ad::AdEnum::ORIENTED_LIVE_PURCHASE,
                                    kuaishou::ad::AdEnum::ORIENTED_NEW_CUSTOMER_GROWTH,
                                    kuaishou::ad::AdEnum::ORIENTED_CUSTOMER_REACH}).
                                Eq(kIndexUnitSpeed, kuaishou::ad::AdEnum::SPEED_FAST).
                                NotEq("wt_unit_id.pc_target_cost_today_sum_cost_rate", 0).
                                Le("wt_unit_id.pc_target_cost_today_sum_cost_rate", cost_rate_thresh);
  ExpandUnitTailTokensExp(result, path);
  return;
}

// 卡消耗近线召回
void AdTableInnerNearlineRecall(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  int64_t cost_rate_thresh = AdKconfUtil::InnerNearlineRecallCostRateThresh();
  bool enable_nearline_fix_local_recall_v3 =
    SPDM_enable_nearline_fix_local_recall_v3(session_data->get_spdm_ctx());
  auto result = all_valid_unit->Eq(kIndexCampaignType, kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE).
                                In(kIndexOcpxActionType,
                                  {kuaishou::ad::EVENT_ORDER_PAIED, kuaishou::ad::AD_MERCHANT_ROAS,
                                  kuaishou::ad::AD_STOREWIDE_ROAS, kuaishou::ad::AD_MERCHANT_T7_ROI}).
                                Ge("wt_unit_id.unit_recent_24h_live_cost", cost_rate_thresh);
  int64_t per_quota = SPDM_nearline_local_recall_per_quota(session_data->get_spdm_ctx());
  if (enable_nearline_fix_local_recall_v3) {
    ExpandTokensNearlineCostSortV3(result, path, session_data, per_quota);
  } else {
    ExpandTokensNearlineCostSortV2(result, path, per_quota);
  }
  return;
}

void ExpandTokensNearlineCostSortV2(ks::ad_table::DataFrame& result, engine_base::tsm::PathContext* path, int64_t per_quota) {  // NOLINT
  int32_t dataframe_size = result.RowSize();
  int32_t match_quota = path->GetPathConfig().TriggerTokenMaxQuota();

  auto search_num = std::min(match_quota, dataframe_size);
  auto& token_list = path->token_list;
  token_list.reserve(search_num);
  // 保存每个 (live_id, ocpx_type) 对应的最高消耗 token
  absl::flat_hash_map<int64_t, std::pair<int64_t, int64_t>> best_token_map;
  auto trunc_df = result.RandomTrunc(match_quota);
  const auto expand_id_desc = trunc_df.Column("id");
  for (size_t i = 0; i < trunc_df.RowSize(); ++i) {
    auto row_id = trunc_df.Row(i);
    int64_t primary_key_id = trunc_df.GetValueUnsafe<int64_t>(row_id, expand_id_desc);
    int64_t cost = 0;
    int64_t live_author_id = 0;
    int64_t ocpx_type = 0;
    const auto& row_wrapper = trunc_df.Find(primary_key_id);
    auto p_unit = row_wrapper.GetStruct<ad_table::Unit>();
    if (p_unit) {
      ocpx_type = p_unit->ocpx_action_type();
      live_author_id = p_unit->live_user_id();
      auto* p_wt_unit = p_unit->wt_unit_id_ref_WTUnit();
      if (p_wt_unit) {
        cost = p_wt_unit->unit_recent_24h_live_cost();
      }
      VLOG_EVERY_N(4, 1) << " ad_table_trigger lihantong_debug"
                        << " dataframe_size=" << dataframe_size
                        << " match_quota=" << match_quota
                        << " search_num=" << search_num
                        << " trunc_df_size=" << trunc_df.RowSize()
                        << " unit_id=" << primary_key_id
                        << " cost " << cost
                        << " live_author_id " << live_author_id
                        << " ocpx_type " << ocpx_type
                        << " cost thresh " << AdKconfUtil::InnerNearlineRecallCostRateThresh();
    }
    // 拼接 key
    int64_t key = live_author_id * 100000 + ocpx_type;
    // 保留每个 key 的最大 cost
    auto it = best_token_map.find(key);
    if (it == best_token_map.end() || cost > it->second.second) {
      best_token_map[key] = {primary_key_id, cost};
    }
  }
  // 填充 token_list
  for (const auto& kv : best_token_map) {
    const auto& [primary_key_id, cost] = kv.second;
    token_list.emplace_back(primary_key_id, cost);
  }
  auto post_cmp = [&](const ks::engine_base::tsm::Token& a, const ks::engine_base::tsm::Token& b) -> bool {
    return std::make_tuple(a.token_score.score, a.token.subtoken_1) >
           std::make_tuple(b.token_score.score, b.token.subtoken_1);
  };
  ks::ad_base::AdPerf::IntervalLogStash(dataframe_size, "ad.ad_target",
      "nearline", "nearline_local_dataframe_size");
  ks::ad_base::AdPerf::IntervalLogStash(match_quota, "ad.ad_target",
      "nearline", "nearline_local_match_quota");
  ks::ad_base::AdPerf::IntervalLogStash(search_num, "ad.ad_target",
      "nearline", "nearline_local_search_num");
  ks::ad_base::AdPerf::IntervalLogStash(token_list.size(), "ad.ad_target",
      "nearline", "nearline_local_token_size");
  std::sort(token_list.begin(), token_list.end(), post_cmp);
}

void ExpandTokensNearlineCostSortV3(ks::ad_table::DataFrame& result,  // NOLINT
                                    engine_base::tsm::PathContext* path,
                                    ContextData* session_data,
                                    int64_t per_quota) {
  int32_t dataframe_size = result.RowSize();
  int32_t match_quota = path->GetPathConfig().TriggerTokenMaxQuota();
  auto search_num = std::min(match_quota, dataframe_size);
  auto& token_list = path->token_list;
  token_list.reserve(search_num);
  struct TokenInfo {
    int64_t primary_key_id = 0;
    int64_t ocpx_action_type = 0;
    int64_t cost = 0;
    int64_t key = 0;
  };
  std::string nearline_local_quota_exp_tag =
      SPDM_nearline_local_quota_exp_tag(session_data->get_spdm_ctx());
  absl::flat_hash_map<int, int> ocpx_quota_map;
  absl::flat_hash_map<int, int> ocpx_cost_map;
  auto quota_conf = AdKconfUtil::nearlineLocalRecallLiveOcpxQuotaMap();
  if (quota_conf) {
    auto& kconf = quota_conf->data();
    auto& conf = kconf.confs();
    auto iter = conf.find(nearline_local_quota_exp_tag);
    if (iter != conf.end()) {
      ocpx_quota_map[kuaishou::ad::AdActionType::EVENT_ORDER_PAIED] = iter->second.order_paid_quota();
      ocpx_quota_map[kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS] = iter->second.storewide_quota();
      ocpx_quota_map[kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI] = iter->second.t7_roi_quota();
      ocpx_quota_map[kuaishou::ad::AdActionType::AD_MERCHANT_ROAS] = iter->second.roas_quota();
      ocpx_cost_map[kuaishou::ad::AdActionType::EVENT_ORDER_PAIED] = iter->second.order_paid_cost();
      ocpx_cost_map[kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS] = iter->second.storewide_cost();
      ocpx_cost_map[kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI] = iter->second.t7_roi_cost();
      ocpx_cost_map[kuaishou::ad::AdActionType::AD_MERCHANT_ROAS] = iter->second.roas_cost();
    }
  }
  std::vector<TokenInfo> candidates;
  auto trunc_df = result.RandomTrunc(match_quota);
  const auto expand_id_desc = trunc_df.Column("id");
  for (size_t i = 0; i < trunc_df.RowSize(); ++i) {
    auto row_id = trunc_df.Row(i);
    int64_t primary_key_id = trunc_df.GetValueUnsafe<int64_t>(row_id, expand_id_desc);
    int64_t cost = 0;
    int64_t live_author_id = 0;
    int64_t ocpx_type = 0;
    const auto& row_wrapper = trunc_df.Find(primary_key_id);
    auto p_unit = row_wrapper.GetStruct<ad_table::Unit>();
    if (p_unit) {
      ocpx_type = p_unit->ocpx_action_type();
      live_author_id = p_unit->live_user_id();
      auto* p_wt_unit = p_unit->wt_unit_id_ref_WTUnit();
      if (p_wt_unit) {
        cost = p_wt_unit->unit_recent_24h_live_cost();
      }
    }
    int64_t key = live_author_id * 100000 + ocpx_type;
    candidates.push_back({primary_key_id, ocpx_type, cost, key});
  }
  // 按 cost 降序排
  std::sort(candidates.begin(), candidates.end(), [](const TokenInfo& a, const TokenInfo& b) {
    return a.cost > b.cost;
  });
  // 聚合 + 控制 quota per key
  absl::flat_hash_map<int64_t, int> key_counter;
  std::unordered_set<int64_t> greater_live_ocpx_cnt;
  for (const auto& item : candidates) {
    auto& cnt = key_counter[item.key];
    if (cnt == 0) {
      token_list.emplace_back(item.primary_key_id, item.cost);
      ++cnt;
    } else if (cnt < ocpx_quota_map[item.ocpx_action_type] &&
              item.cost > ocpx_cost_map[item.ocpx_action_type]) {
      token_list.emplace_back(item.primary_key_id, item.cost);
      greater_live_ocpx_cnt.insert(item.key);
      ++cnt;
    }
  }

  // 打点
  ks::ad_base::AdPerf::IntervalLogStash(dataframe_size,
    "ad.ad_target", "nearline", "nearline_local_dataframe_size_v3");
  ks::ad_base::AdPerf::IntervalLogStash(match_quota, "ad.ad_target",
    "nearline", "nearline_local_match_quota_v3");
  ks::ad_base::AdPerf::IntervalLogStash(search_num, "ad.ad_target",
    "nearline", "nearline_local_search_num_v3");
  ks::ad_base::AdPerf::IntervalLogStash(token_list.size(), "ad.ad_target",
    "nearline", "nearline_local_token_size_v3");
  ks::ad_base::AdPerf::IntervalLogStash(key_counter.size(), "ad.ad_target",
    "nearline", "nearline_local_live_author_size_v3");
  ks::ad_base::AdPerf::IntervalLogStash(greater_live_ocpx_cnt.size(), "ad.ad_target",
    "nearline", "nearline_local_great_live_author_size_v3");
}

// 卡消耗近线召回 短带短引
void AdTableInnerNearlineRecallItemPhoto(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  // 短带
  int64_t cost_rate_thresh_item_photo = AdKconfUtil::InnerNearlineRecallItemPhotoCostRateThresh();
  auto result = all_valid_creative->In("unit_id.campaign_id.account_id.account_type",
                      {kuaishou::ad::AdEnum::ACCOUNT_ESP, kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE}).
                       Eq("unit_id.campaign_id.type", kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE).
                       Ge("wt_photo_id.photo_recent_24h_item_photo_cost",
                       cost_rate_thresh_item_photo);
  bool enable_nearline_change_photo_creativeids =
    SPDM_enable_nearline_change_photo_creativeids(session_data->get_spdm_ctx());
  if (enable_nearline_change_photo_creativeids) {
    ExpandTokensNearlineCostSortItemPhotoV2(result, path);
  } else {
    ExpandTokensNearlineCostSortItemPhoto(result, path, 1);
  }
  return;
}
void AdTableInnerNearlineRecallItemPhotoToLive(ContextData* session_data,
  engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  // 短引
  int64_t cost_rate_thresh_item_p2l = AdKconfUtil::InnerNearlineRecallItemPhotoToLiveCostRateThresh();
  auto result = all_valid_creative->In("unit_id.campaign_id.account_id.account_type",
                      {kuaishou::ad::AdEnum::ACCOUNT_ESP, kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE}).
                       Eq("unit_id.campaign_id.type", kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE).
                       Ge("wt_photo_id.photo_recent_24h_item_photo_to_live_cost",
                       cost_rate_thresh_item_p2l);
  ExpandTokensNearlineCostSortItemPhoto(result, path, 2);
  return;
}
void ExpandTokensNearlineCostSortItemPhoto(ks::ad_table::DataFrame& result, engine_base::tsm::PathContext* path, int32_t ad_type) {  // NOLINT
  int32_t dataframe_size = result.RowSize();
  int32_t match_quota = path->GetPathConfig().TriggerTokenMaxQuota();

  auto search_num = std::min(match_quota, dataframe_size);
  auto& token_list = path->token_list;
  token_list.reserve(search_num);
  // 保存每个 (photo_id, ocpx_type) 对应的最高消耗 token
  absl::flat_hash_map<int64_t, std::pair<int64_t, int64_t>> best_token_map;
  absl::flat_hash_map<int64_t, std::pair<int64_t, int64_t>> best_cost_token_map;
  auto trunc_df = result.RandomTrunc(match_quota);
  const auto expand_id_desc = trunc_df.Column("id");
  for (size_t i = 0; i < trunc_df.RowSize(); ++i) {
    auto row_id = trunc_df.Row(i);
    int64_t primary_key_id = trunc_df.GetValueUnsafe<int64_t>(row_id, expand_id_desc);
    int64_t cost = 0;
    int64_t photo_id = 0;
    int64_t ocpx_type = 0;
    int64_t cpa_bid = 0;
    double roi_ratio = 0.0;
    double final_bid = 0.0;
    const auto& row_wrapper = trunc_df.Find(primary_key_id);
    if (!row_wrapper) {
      continue;
    }
    auto p_creative = row_wrapper.GetStruct<ad_table::Creative>();
    auto p_unit = p_creative->unit_id_ref_Unit();
    if (p_unit && p_creative) {
      ocpx_type = p_unit->ocpx_action_type();
      photo_id = p_creative->photo_id();
      auto* p_wt_photo = p_creative->wt_photo_id_ref_WTPhoto();
      if (p_wt_photo) {
        cost = (ad_type == 1) ? p_wt_photo->photo_recent_24h_item_photo_cost() :
                                p_wt_photo->photo_recent_24h_item_photo_to_live_cost();
      }
      cpa_bid = p_unit->cpa_bid();
      roi_ratio = p_unit->roi_ratio() > 0 ? p_unit->roi_ratio() : 2;
      final_bid = cpa_bid > 0 ? cpa_bid : 1 / roi_ratio;
      VLOG_EVERY_N(4, 1) << " ad_table_trigger liujiahui_debug"
                         << " dataframe_size=" << dataframe_size
                         << " match_quota=" << match_quota
                         << " search_num=" << search_num
                         << " trunc_df_size=" << trunc_df.RowSize()
                         << " unit_id=" << primary_key_id
                         << " cost " << cost
                         << " photo_id " << photo_id
                         << " ocpx_type " << ocpx_type
                         << " ad_type :" << ad_type;
    }
    // 拼接 key
    int64_t key = photo_id * 100000 + ocpx_type;
    // 保留每个 key 的最大 cost
    auto it = best_token_map.find(key);
    if (it == best_token_map.end() || final_bid > it->second.second ||
          (final_bid == it->second.second && primary_key_id < it->second.first)) {
      best_token_map[key] = {primary_key_id, final_bid};
      best_cost_token_map[key] = {primary_key_id, cost};
    }
  }
  // 填充 token_list
  for (const auto& kv : best_cost_token_map) {
    const auto& [primary_key_id, cost] = kv.second;
    token_list.emplace_back(primary_key_id, cost);
  }
  auto post_cmp = [&](const ks::engine_base::tsm::Token& a, const ks::engine_base::tsm::Token& b) -> bool {
    return std::make_tuple(a.token_score.score, a.token.subtoken_1) >
           std::make_tuple(b.token_score.score, b.token.subtoken_1);
  };
  ks::ad_base::AdPerf::IntervalLogStash(dataframe_size, "ad.ad_target",
      "nearline_photo", "nearline_photo_local_dataframe_size");
  ks::ad_base::AdPerf::IntervalLogStash(match_quota, "ad.ad_target",
      "nearline_photo", "nearline_photo_local_match_quota");
  ks::ad_base::AdPerf::IntervalLogStash(search_num, "ad.ad_target",
      "nearline_photo", "nearline_photo_local_search_num");
  ks::ad_base::AdPerf::IntervalLogStash(token_list.size(), "ad.ad_target",
      "nearline_photo", "nearline_photo_local_token_size");
  std::sort(token_list.begin(), token_list.end(), post_cmp);
}

void ExpandTokensNearlineCostSortItemPhotoV2(ks::ad_table::DataFrame& result, engine_base::tsm::PathContext* path) {  // NOLINT
  int32_t dataframe_size = result.RowSize();
  int32_t match_quota = path->GetPathConfig().TriggerTokenMaxQuota();

  auto search_num = std::min(match_quota, dataframe_size);
  auto& token_list = path->token_list;
  token_list.reserve(search_num);
  // 保存每个 (photo_id, ocpx_type) 对应的最高消耗 token
  absl::flat_hash_map<int64_t, std::pair<int64_t, int64_t>> best_token_map;
  absl::flat_hash_map<int64_t, std::pair<int64_t, int64_t>> best_cost_token_map;
  auto trunc_df = result.RandomTrunc(match_quota);
  const auto expand_id_desc = trunc_df.Column("id");
  for (size_t i = 0; i < trunc_df.RowSize(); ++i) {
    auto row_id = trunc_df.Row(i);
    int64_t primary_key_id = trunc_df.GetValueUnsafe<int64_t>(row_id, expand_id_desc);
    int64_t cost = 0;
    int64_t photo_id = 0;
    int64_t ocpx_type = 0;
    int64_t cpa_bid = 0;
    double roi_ratio = 0.0;
    double final_bid = 0.0;
    const auto& row_wrapper = trunc_df.Find(primary_key_id);
    if (!row_wrapper) {
      continue;
    }
    auto p_creative = row_wrapper.GetStruct<ad_table::Creative>();
    auto p_unit = p_creative->unit_id_ref_Unit();
    if (!p_unit || !p_creative) {
      continue;
    }
    auto* p_wt_photo = p_creative->wt_photo_id_ref_WTPhoto();
    if (!p_wt_photo) {
      continue;
    }
    ocpx_type = p_unit->ocpx_action_type();
    photo_id = p_creative->photo_id();
    cost = p_wt_photo->photo_recent_24h_item_photo_cost();
    auto& good_creative_ids = p_wt_photo->select_good_creatives();
    cpa_bid = p_unit->cpa_bid();
    roi_ratio = p_unit->roi_ratio() > 0 ? p_unit->roi_ratio() : 2;
    final_bid = cpa_bid > 0 ? cpa_bid : 1 / roi_ratio;
    VLOG_EVERY_N(4, 1) << " ad_table_trigger liujiahui_debug"
                       << " dataframe_size=" << dataframe_size
                       << " match_quota=" << match_quota
                       << " search_num=" << search_num
                       << " trunc_df_size=" << trunc_df.RowSize()
                       << " creative_id=" << primary_key_id
                       << " cost= " << cost
                       << " photo_id= " << photo_id
                       << " ocpx_type= " << ocpx_type
                       << " good_creative_size= " << good_creative_ids.size();
    if (ocpx_type == 944) {
      bool flag = false;
      for (const auto &good_creative_id : good_creative_ids) {
        if (good_creative_id == primary_key_id) {
          flag = true;
          break;
        }
      }
      if (flag) {
        best_cost_token_map[primary_key_id] = {primary_key_id, cost};
      }
    } else {
      // 拼接 key
      int64_t key = photo_id * 100000 + ocpx_type;
      // 保留每个 key 的最大 cost
      auto it = best_token_map.find(key);
      if (it == best_token_map.end() || final_bid > it->second.second ||
            (final_bid == it->second.second && primary_key_id < it->second.first)) {
        best_token_map[key] = {primary_key_id, final_bid};
        best_cost_token_map[key] = {primary_key_id, cost};
      }
    }
  }
  // 填充 token_list
  for (const auto& kv : best_cost_token_map) {
    const auto& [primary_key_id, cost] = kv.second;
    token_list.emplace_back(primary_key_id, cost);
  }
  auto post_cmp = [&](const ks::engine_base::tsm::Token& a, const ks::engine_base::tsm::Token& b) -> bool {
    return std::make_tuple(a.token_score.score, a.token.subtoken_1) >
           std::make_tuple(b.token_score.score, b.token.subtoken_1);
  };
  ks::ad_base::AdPerf::IntervalLogStash(dataframe_size, "ad.ad_target",
      "nearline_photo", "nearline_photo_local_dataframe_size");
  ks::ad_base::AdPerf::IntervalLogStash(match_quota, "ad.ad_target",
      "nearline_photo", "nearline_photo_local_match_quota");
  ks::ad_base::AdPerf::IntervalLogStash(search_num, "ad.ad_target",
      "nearline_photo", "nearline_photo_local_search_num");
  ks::ad_base::AdPerf::IntervalLogStash(token_list.size(), "ad.ad_target",
      "nearline_photo", "nearline_photo_local_token_size");
  std::sort(token_list.begin(), token_list.end(), post_cmp);
}
// 掉量素材召回
void AdTableInnerCostDropPhoto(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }

  auto author_set = AdKconfUtil::p2lShowCaseWhiteAuthorListRetrieval();
  if (author_set == nullptr || author_set->empty()) {
    return;
  }
  std::vector<int64_t> tmp_vec;
  tmp_vec.assign(author_set->begin(), author_set->end());

  auto result = all_valid_creative->In("unit_id.live_user_id.user_id", tmp_vec)
                                .Eq("unit_id.item_type", kuaishou::ad::AdEnum::ITEM_PHOTO_TO_LIVE)
                                .Eq("wt_photo_id.is_cost_drop", 1);
  ExpandTokens(result, path);
}

void AdTableEcomActionCate3Live(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  const auto& order_info = session_data->get_ad_request()->ad_user_info().colossus_order_data_map();
  auto click_iter = click_info.find("category_a");
  auto order_iter = order_info.find("category_a");
  auto click_time_iter = click_info.find("click_timestamp");
  auto order_time_iter = order_info.find("pay_order_time");
  int64 current_time_stamp = base::GetTimestamp() / 1000000;
  int64 cate3_cnt = 0;
  if (click_iter == click_info.end() && order_iter == order_info.end()) {
    return;
  }
  if (click_time_iter == click_info.end() && order_time_iter == order_info.end()) {
    return;
  }
  absl::flat_hash_set<int64_t> cate3_set;
  if (order_iter != order_info.end() && order_time_iter != order_info.end()) {
    const auto& order_cate_list = order_iter->second.int_list_value();
    const auto& order_time_list = order_time_iter->second.int_list_value();
    for (int i = 0; i < order_cate_list.size(); i++) {
      if (SPDM_enable_recall_limit_cate3_nums(session_data->get_spdm_ctx())
       && cate3_cnt < SPDM_recall_cate3_length_threshold(session_data->get_spdm_ctx())
       && i < order_time_list.size()
       && (current_time_stamp - order_time_list[i])
        <= SPDM_recall_cate3_time_threshold(session_data->get_spdm_ctx())) {
        const auto& ret = cate3_set.insert((order_cate_list[i]>>16) & 0xffff);
        if (ret.second) {
          cate3_cnt++;
        }
      } else {
        cate3_set.insert((order_cate_list[i]>>16) & 0xffff);  // 1 级类目 | 2 级类目 | 3 级类目 | 4 级类目
      }
    }
  }
  if (click_iter != click_info.end() && click_time_iter != click_info.end()) {
    const auto& click_cate_list = click_iter->second.int_list_value();
    const auto& click_time_list = click_time_iter->second.int_list_value();
    for (int i = 0; i < click_cate_list.size(); i++) {
      if (SPDM_enable_recall_limit_cate3_nums(session_data->get_spdm_ctx())
       && cate3_cnt < SPDM_recall_cate3_length_threshold(session_data->get_spdm_ctx())
       && i < click_time_list.size()
       && (current_time_stamp - click_time_list[i])
        <= SPDM_recall_cate3_time_threshold(session_data->get_spdm_ctx())) {
        const auto& ret = cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
        if (ret.second) {
          cate3_cnt++;
        }
      } else {
        cate3_set.insert((click_cate_list[i]>>16) & 0xffff);
      }
    }
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> cate3_vec;
  cate3_vec.assign(cate3_set.begin(), cate3_set.end());
  if (SPDM_enable_select_by_unit_cost_live(session_data->get_spdm_ctx())) {
    auto result = all_valid_unit->In("live_wt_live_id.mmu_a_category3_id_list", cate3_vec).
                                  Eq("wt_unit_id.cost_total_inner_unit_40_min", 1).
                                  In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  } else {
    auto result = all_valid_unit->In("live_wt_live_id.mmu_a_category3_id_list", cate3_vec).
                                  Eq("item_id.order_cnt_product_1_day", 1).
                                  In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  }
  return;
}

void AdTableRecoActionAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& video_info = session_data->get_ad_request()->ad_user_info().colossus_video_item_data_map();
  const auto& live_info = session_data->get_ad_request()->ad_user_info().colossus_auto_live_item_data_map();
  auto video_author_iter = video_info.find("author_id");
  auto video_label_iter = video_info.find("label");
  auto video_playtime_iter = video_info.find("play_time");
  auto video_duration_iter = video_info.find("duration");
  auto live_author_iter = live_info.find("author_id");
  auto live_label_iter = live_info.find("label");
  auto live_playtime_iter = live_info.find("play_time");

  if (video_author_iter == video_info.end() && live_author_iter == live_info.end()) {
    return;
  }
  absl::flat_hash_set<int64_t> author_set;
  if (live_author_iter != live_info.end() && live_label_iter != live_info.end()
       && live_playtime_iter != live_info.end()) {
    const auto& live_author = live_author_iter->second.int_list_value();
    const auto& live_label = live_label_iter->second.int_list_value();
    const auto& live_playtime = live_playtime_iter->second.int_list_value();
    if (live_author.size() <= 0 || live_author.size() != live_label.size() ||
     live_author.size() != live_playtime.size()) {
      return;
    }
    for (int i = 0; i < live_author.size(); i++) {
      int64_t label = live_label[i];
      int64_t flag = (label & 1) + ((label >> 2) & 1) + ((label >> 3) & 1)
                     + ((label >> 5) & 1) + ((label >> 7) & 1) + ((label >> 9) & 1);
      if (flag > 0 || live_playtime[i] > 10) {
        author_set.insert(live_author[i]);
      }
    }
  }
  if (video_author_iter != video_info.end() && video_label_iter != video_info.end()
       && video_playtime_iter != video_info.end() && video_duration_iter != video_info.end()) {
    const auto& video_author = video_author_iter->second.int_list_value();
    const auto& video_label = video_label_iter->second.int_list_value();
    const auto& video_playtime = video_playtime_iter->second.int_list_value();
    const auto& video_duration = video_duration_iter->second.int_list_value();
    if (video_author.size() <= 0 || video_author.size() != video_label.size() ||
     video_author.size() != video_playtime.size() || video_author.size() != video_duration.size()) {
      return;
    }
    for (int i = 0; i < video_author.size(); i++) {
      int64_t label = video_label[i];
      int64_t flag = (label & 1) + ((label >> 1) & 1) + ((label >> 2) & 1)
                     + ((label >> 4) & 1) + ((label >> 6) & 1);
      int64_t time_flag = 0;
      if (video_duration[i] >= 18 && video_playtime[i] >= 18 ||
       (video_duration[i] >= 3 && video_duration[i] < 18) && (video_playtime[i] == video_duration[i])) {
        time_flag = 1;
       }
      if (flag > 0 || time_flag > 0) {
        author_set.insert(video_author[i]);
      }
    }
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> author_vec;
  author_vec.assign(author_set.begin(), author_set.end());
  auto result = all_valid_unit->In(kIndexLiveUserId, author_vec).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfRecoActionAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& video_info = session_data->get_ad_request()->ad_user_info().colossus_video_item_data_map();
  const auto& live_info = session_data->get_ad_request()->ad_user_info().colossus_auto_live_item_data_map();
  auto video_author_iter = video_info.find("author_id");
  auto video_label_iter = video_info.find("label");
  auto video_playtime_iter = video_info.find("play_time");
  auto video_duration_iter = video_info.find("duration");
  auto live_author_iter = live_info.find("author_id");
  auto live_label_iter = live_info.find("label");
  auto live_playtime_iter = live_info.find("play_time");
  if (video_author_iter == video_info.end() && live_author_iter == live_info.end()) {
    return;
  }
  absl::flat_hash_set<int64_t> author_set;
  if (live_author_iter != live_info.end() && live_label_iter != live_info.end()
       && live_playtime_iter != live_info.end()) {
    const auto& live_author = live_author_iter->second.int_list_value();
    const auto& live_label = live_label_iter->second.int_list_value();
    const auto& live_playtime = live_playtime_iter->second.int_list_value();
    if (live_author.size() <= 0 || live_author.size() != live_label.size() ||
     live_author.size() != live_playtime.size()) {
      return;
    }
    for (int i = 0; i < live_author.size(); i++) {
      int64_t label = live_label[i];
      int64_t flag = (label & 1) + ((label >> 2) & 1) + ((label >> 3) & 1)
                     + ((label >> 5) & 1) + ((label >> 7) & 1) + ((label >> 9) & 1);
      if (flag > 0 || live_playtime[i] > 10) {
        author_set.insert(live_author[i]);
      }
    }
  }
  if (video_author_iter != video_info.end() && video_label_iter != video_info.end()
       && video_playtime_iter != video_info.end() && video_duration_iter != video_info.end()) {
    const auto& video_author = video_author_iter->second.int_list_value();
    const auto& video_label = video_label_iter->second.int_list_value();
    const auto& video_playtime = video_playtime_iter->second.int_list_value();
    const auto& video_duration = video_duration_iter->second.int_list_value();
    if (video_author.size() <= 0 || video_author.size() != video_label.size() ||
     video_author.size() != video_playtime.size() || video_author.size() != video_duration.size()) {
      return;
    }
    for (int i = 0; i < video_author.size(); i++) {
      int64_t label = video_label[i];
      int64_t flag = (label & 1) + ((label >> 1) & 1) + ((label >> 2) & 1)
                     + ((label >> 4) & 1) + ((label >> 6) & 1);
      int64_t time_flag = 0;
      if (video_duration[i] >= 18 && video_playtime[i] >= 18 ||
       (video_duration[i] >= 3 && video_duration[i] < 18) && (video_playtime[i] == video_duration[i])) {
        time_flag = 1;
       }
      if (flag > 0 || time_flag > 0) {
        author_set.insert(video_author[i]);
      }
    }
  }
  std::vector<int64_t> author_vec;
  author_vec.assign(author_set.begin(), author_set.end());
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    kCreativeCreativeMaterialType,
                    static_cast<int64_t>(kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD)).
                    In("unit_id.live_user_id", author_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableZCUserLongPlayAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& long_play_list =
    session_data->get_ad_request()->ad_user_info().zhongcao_long_play_author_id_list();
  if (long_play_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> long_play_set;
  for (int i = 0; i < long_play_list.size(); i++) {
    if (long_play_set.size() >= max_len) {
      break;
    }
    long_play_set.insert(long_play_list[i]);
  }
  std::vector<int64_t> long_play_vec;
  long_play_vec.assign(long_play_set.begin(), long_play_set.end());
  if (SPDM_enable_only_7d_roi_exp(session_data->get_spdm_ctx())) {
    auto result = all_valid_unit->In(kIndexLiveUserId, long_play_vec).
                                Eq(kIndexOcpxActionType, kuaishou::ad::AD_MERCHANT_T7_ROI).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  } else {
    auto result = all_valid_unit->In(kIndexLiveUserId, long_play_vec).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  }
  return;
}

void AdTableZCUserInteractAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& interact_list =
    session_data->get_ad_request()->ad_user_info().zhongcao_interact_author_id_list();
  if (interact_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> interact_set;
  for (int i = 0; i < interact_list.size(); i++) {
    if (interact_set.size() >= max_len) {
      break;
    }
    interact_set.insert(interact_list[i]);
  }
  std::vector<int64_t> interact_vec;
  interact_vec.assign(interact_set.begin(), interact_set.end());
  if (SPDM_enable_only_7d_roi_exp(session_data->get_spdm_ctx())) {
    auto result = all_valid_unit->In(kIndexLiveUserId, interact_vec).
                                Eq(kIndexOcpxActionType, kuaishou::ad::AD_MERCHANT_T7_ROI).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  } else {
    auto result = all_valid_unit->In(kIndexLiveUserId, interact_vec).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
    ExpandTokens(result, path);
  }
  return;
}

void AdTableZCUserInteractAuthorHourly(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto& interact_list =
    session_data->get_ad_request()->ad_user_info().zhongcao_interact_author_id_list_hourly();
  if (interact_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> interact_set;
  for (int i = 0; i < interact_list.size(); i++) {
    if (interact_set.size() >= max_len) {
      break;
    }
    interact_set.insert(interact_list[i]);
  }
  std::vector<int64_t> interact_vec;
  interact_vec.assign(interact_set.begin(), interact_set.end());
  auto result = all_valid_unit->In(kIndexLiveUserId, interact_vec).
                              Eq(kIndexOcpxActionType, kuaishou::ad::AD_MERCHANT_T7_ROI).
                              In(kIndexAccountType,
                                {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                  kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                  kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableAllDomainBuyShop(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto buy_shop_list = session_data->get_ad_request()->ad_user_info().purchase_shop_id_list();
  if (buy_shop_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::set<int64_t> purchase_shop_set;
  for (int i = 0; i < buy_shop_list.size(); i += 2) {
    if (purchase_shop_set.size() >= max_len) {
      break;
    }
    purchase_shop_set.insert(buy_shop_list[i]);
  }
  std::vector<int64_t> purchase_shop_vec;
  purchase_shop_vec.assign(purchase_shop_set.begin(), purchase_shop_set.end());
  auto result = all_valid_unit->In(kIndexLiveUserId, purchase_shop_vec).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfAllDomainBuyShop(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t max_len = 50;
  const auto buy_shop_list = session_data->get_ad_request()->ad_user_info().purchase_shop_id_list();
  if (buy_shop_list.size() <= 0) {
    return;
  }
  std::set<int64_t> purchase_shop_set;
  for (int i = 0; i < buy_shop_list.size(); i += 2) {
    if (purchase_shop_set.size() >= max_len) {
      break;
    }
    purchase_shop_set.insert(buy_shop_list[i]);
  }
  std::vector<int64_t> purchase_shop_vec;
  purchase_shop_vec.assign(purchase_shop_set.begin(), purchase_shop_set.end());
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    kCreativeCreativeMaterialType,
                    static_cast<int64_t>(kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD)).
                    In("unit_id.live_user_id", purchase_shop_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableItemSpeedTestAllRetr(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  // 判断创意来源
  auto result = all_valid_unit->Eq("campaign_id.create_source_type",
      kuaishou::ad::AdEnum::CAMPAIGN_ITEM_TEST);
  ExpandTokens(result, path);
  return;
}

void AdTableAdMerchantFansReach(ContextData* session_data, engine_base::tsm::PathContext* path) {
  // 通过商业化涨粉的粉丝重触达
  if (session_data->get_ad_request()->ad_user_info().last_ad_merchant_follow_list_size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> follow_account_vec;
  follow_account_vec.reserve(
      session_data->get_ad_request()->ad_user_info().appointment_author_list().size());
  for (const auto &follow : session_data->get_ad_request()->ad_user_info().last_ad_merchant_follow_list()) {
    follow_account_vec.push_back(follow.author_id());
  }
  auto result = all_valid_unit->In(kIndexLiveUserId, follow_account_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableInnerCeiling(ContextData* session_data, engine_base::tsm::PathContext* path) {
  // 获取加白账户
  const auto &product_white_list =
    AdKconfUtil::innerCeilingWhiteList()->data().product_ids;
  const auto &account_white_list =
    AdKconfUtil::innerCeilingWhiteList()->data().account_ids;
  if (account_white_list.empty() && product_white_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  ks::ad_table::DataFrame result;
  std::vector<int64_t> tmp_vec;
  if (!account_white_list.empty()) {
    tmp_vec.assign(account_white_list.begin(), account_white_list.end());
    result = all_valid_unit->In(kIndexAccountId, tmp_vec);
  }
  if (!product_white_list.empty()) {
    tmp_vec.clear();
    tmp_vec.assign(product_white_list.begin(), product_white_list.end());
    result = all_valid_unit->In(kIndexProductId, tmp_vec);
  }
  ExpandTokens(result, path);
  return;
}

void AdTableAmdFans(ContextData* session_data, engine_base::tsm::PathContext* path) {
  bool enable_buyer_homepage_use_ad_follow_data =
      AdKconfUtil::merchantTrafficPageIds()->count(session_data->get_page_id()) > 0;
  if (SPDM_enable_fans_recall_in_mall(session_data->get_spdm_ctx())) {
    const auto merchant_pageids = AdKconfUtil::merchantTrafficAllPageIds();
    if (merchant_pageids != nullptr && !merchant_pageids->empty()) {
      enable_buyer_homepage_use_ad_follow_data = merchant_pageids->count(session_data->get_page_id()) > 0;
    }
  }
  if (session_data->get_ad_request()->reco_user_info().follow_list_size() <= 0 &&
      (!enable_buyer_homepage_use_ad_follow_data || session_data->get_ad_follow_set().size() <= 0)) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  ks::ad_table::DataFrame result;
  std::vector<int64_t> tmp_vec;
  if (enable_buyer_homepage_use_ad_follow_data) {
    tmp_vec.assign(session_data->get_ad_follow_set().begin(), session_data->get_ad_follow_set().end());
  } else {
    for (const auto& follow : session_data->get_ad_request()->reco_user_info().follow_list()) {
      tmp_vec.push_back(follow.user().id());
    }
  }
  //通路切换数据源
  if (SPDM_enable_70_path_reco_follow_set(session_data->get_spdm_ctx())) {
    tmp_vec.assign(session_data->get_reco_follow_set().begin(),
    session_data->get_reco_follow_set().end());
  }
  result = all_valid_unit->In(kIndexLiveUserId, tmp_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableFanstopBrand(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("campaign_id.fanstop_category", AdEnum::CATEGORY_BRAND_FANSTOP);
  ExpandTokens(result, path);
}

void AdTableFanstopLiveOrder(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("item_type", AdEnum::ITEM_LIVE);
  ExpandTokens(result, path);
  return;
}

void AdTableEspLiveRandom(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexCampaignType, kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE);
  ExpandTokens(result, path);
  return;
}

void AdTableFanstopLiveRandom(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (session_data->get_fanstop_flow_type() != "fanstop_explore") {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("item_type", AdEnum::ITEM_LIVE);
  ExpandTokens(result, path);
  return;
}

void AdTableBizFanstopPhotoTarget(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  if (!SPDM_enable_biz_fanstop_target(session_data->get_spdm_ctx())) {
    return;
  }
  // 判断 B 粉/流量助推 走粉条浅度目标物料, 补充召回
  auto result = all_valid_unit
      ->In(kIndexAccountType, {kuaishou::ad::AdEnum::ACCOUNT_CPC,
                               kuaishou::ad::AdEnum::ACCOUNT_SOCIAL})
      .In(kIndexCampaignType, {kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL,
                               kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS,
                               kuaishou::ad::AdEnum::AD_FANSTOP_TO_SHOW});
  ExpandTokens(result, path);
  return;
}
void AdTableFanstopMessageLeadsTarget(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  if (SPDM_enable_fanstop_add_phone_call_target(session_data->get_spdm_ctx())) {
    // 添加电话拨打优化目标
    auto result = all_valid_unit->In("ocpx_action_type",
        {kuaishou::ad::EVENT_PHONE_CALL,
         kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT,
         kuaishou::ad::LEADS_SUBMIT,
         kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED})
        .Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2)
        .Eq("item_type", kuaishou::ad::AdEnum::ITEM_PHOTO);
    ExpandTokens(result, path);
  } else {
    // 判断 私信优化目标
    auto result = all_valid_unit->In("ocpx_action_type",
        {kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT,
        kuaishou::ad::LEADS_SUBMIT,
        kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED}).
        Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2).
        Eq("item_type", kuaishou::ad::AdEnum::ITEM_PHOTO);
    ExpandTokens(result, path);
  }
  return;
}

void AdTableFanstopPhoneCallTarget(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  if (SPDM_enable_fanstop_phone_call_hetu_orientation_target(session_data->get_spdm_ctx())) {
    // 添加电话拨打河图人群包召回
    std::vector<int64_t> hetu_cate_vec;
    auto hetu_population_map = AdKconfUtil::phoneCallHetuPopulationMap();
    const auto& user_population_set = session_data->get_target_profiler().user_orientation_set;
    if (!hetu_population_map) {
      return;
    }
    int config_cnt = 0;
    int max_config_len = 100;
    for (auto iter = hetu_population_map->begin(); iter != hetu_population_map->end(); iter++) {
      config_cnt += 1;
      if (config_cnt >= max_config_len) {
        break;
      }
      if (user_population_set.find(iter->first) != user_population_set.end()) {
        hetu_cate_vec.push_back(iter->second);
      }
    }
    auto result = all_valid_unit->In("ocpx_action_type",
        {kuaishou::ad::EVENT_PHONE_CALL})
        .Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2)
        .Eq("item_type", kuaishou::ad::AdEnum::ITEM_PHOTO)
        .In("campaign_id.account_id.user_id.fanstop_hetu_cate1_id", hetu_cate_vec);
    ExpandTokens(result, path);
  }
  return;
}

void AdTableFanstopLiveRecruit(ContextData* session_data, engine_base::tsm::PathContext* path) {
  using kuaishou::ad::AdEnum;
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("item_type", AdEnum::ITEM_LIVE);
  ExpandTokens(result, path);
  return;
}

void AdTableInspireLive(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!session_data->get_pos_manager_base().IsInspireLive()) {
    return;
  }

  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  const auto& live_types = GetAllFanstopLiveCampaign<int64_t>();
  auto result = all_valid_unit->In(kIndexCampaignType, live_types).
      Eq(kIndexOcpxActionType, kuaishou::ad::AdActionType::AD_AUDIENCE_FAST);
  ExpandTokens(result, path);
  return;
}

void AdTableKeywordPopulation(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (SPDM_disable_key_word_population(session_data->get_spdm_ctx())) {
    return;
  }
  std::vector<int64_t> keyword;
  const auto& user_ecom_interest = session_data->get_ad_request()->ad_user_info().ecom_interest();
  if (user_ecom_interest.empty()) {
    return;
  }
  session_data->get_dot()->Interval(user_ecom_interest.size(), "user_keyword_cnt");
  int64_t max_tag_cnt = AdKconfUtil::maxTargetRecallUserTag();
  for (int i = 0; i < user_ecom_interest.size() && i < max_tag_cnt; ++i) {
    int64_t id = user_ecom_interest[i];
    if ((id >= 60000001 && id <= 60500000) ||
        (id >= 85000001 && id <= 85300000)) {
      keyword.emplace_back(id);
    }
  }
  if (keyword.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<ad_table::Key128> keys_vec;
  for (auto& keys_keyword : keyword) {
    keys_vec.push_back(ad_table::HashOf(keys_keyword));
  }
  auto result = all_valid_unit->In("target.population", keys_vec).
      In(kIndexCampaignType, {AdEnum::LIVE_STREAM_PROMOTE, AdEnum::MERCHANT_RECO_PROMOTE});
  ExpandTokens(result, path);
  return;
}

void AdTableWholeRoi(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (SPDM_disable_whole_roi(session_data->get_spdm_ctx())) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexOcpxActionType, kuaishou::ad::AD_STOREWIDE_ROAS);
  ExpandTokens(result, path);
  return;
}

void AdTableLocalLife(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexOcpxActionType, kuaishou::ad::EVENT_ORDER_PAIED).
      Eq(kIndexCampaignType, AdEnum::FANS_LIVE_STREAM_PROMOTE);

  if (session_data->get_pos_manager_base().IsFollow()) {
    std::vector<int64_t> reco_follow_vec;
    reco_follow_vec.assign(session_data->get_reco_follow_set().begin(),
      session_data->get_reco_follow_set().end());
    result = result.In(kIndexLiveUserId, reco_follow_vec);
  }
  ExpandTokens(result, path);
  return;
}

void AdTableShopOrient(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& user_info = session_data->get_ad_request()->ad_user_info();
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }

  std::vector<ad_table::Key128> orientation_vec;
  for (auto id : user_info.orientation()) {
    if ((id >= 30000000 && id < 40000000) ||
        (id >= 47300001 && id <= 47699999)) {
      orientation_vec.push_back(ad_table::HashOf(id));
    }
  }
  auto result = all_valid_unit->In("target.population", orientation_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableCbo(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("campaign_id.cbo", 1);
  ExpandTokens(result, path);
  return;
}

void AdTableProductHostHighCostUnitTrigger(ContextData* session_data,
                                           engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("wt_unit_id.special_unit_type", 2);
  session_data->get_dot()->Interval(result.RowSize(), "high_cost_special_unit");
  ExpandTokens(result, path);
  return;
}

void AdTableProductHostNewPhotoUnitTrigger(ContextData* session_data,
                                           engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("wt_unit_id.special_unit_type", 1);
  session_data->get_dot()->Interval(result.RowSize(), "new_photo_special_unit");
  ExpandTokens(result, path);
  return;
}

void AdTableFenyuStrategy(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  auto account_id_list = AdKconfUtil::fenyuStrategyAccountList();
  if (!all_valid_unit || !account_id_list) {
    return;
  }

  bool is_fenyu_orientation =
      session_data->get_target_profiler().user_orientation_set.contains(AdKconfUtil::fenyuOrientationId());

  session_data->get_dot()->Interval(1, "fenyu_strategy_orientation_hit", absl::StrCat(is_fenyu_orientation));

  if (!is_fenyu_orientation) {
    return;
  }

  session_data->get_dot()->Interval(account_id_list->size(), "fenyu_strategy_account_id_size");

  auto result = all_valid_unit->In(kIndexAccountId, *account_id_list)
                    .In(kIndexOcpxActionType,
                        {kuaishou::ad::AD_MERCHANT_FOLLOW, kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY,
                         kuaishou::ad::AD_FANS_TOP_FOLLOW, kuaishou::ad::AD_MERCHANT_FOLLOW_FAST,
                         kuaishou::ad::AD_MERCHANT_FOLLOW_ROI});
  session_data->get_dot()->Interval(result.RowSize(), "fenyu_strategy_df_size");
  ExpandTokens(result, path);
  return;
}

void AdTableNewCustomerGrowth(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t dataframe_size = 0;
  std::set<int64_t> buy_shop_id;
  const auto buy_list = session_data->get_ad_request()->ad_user_info().purchase_shop_id_list();
  if (buy_list.size() >= 0 && buy_list.size() % 2 == 0) {
    for (int i = 0; i < buy_list.size(); i += 2) {
      buy_shop_id.insert(buy_list[i]);
    }
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("target_type", AdEnum::APPEAL_NEW_CUSTOMER_GROWTH);
  if (!buy_shop_id.empty()) {
    std::vector<int64_t> buy_shop_id_vec;
    buy_shop_id_vec.assign(buy_shop_id.begin(), buy_shop_id.end());
    result = result.NotIn(kIndexLiveUserId, buy_shop_id_vec);
  }
  dataframe_size = result.RowSize();
  session_data->get_dot()->Interval(dataframe_size, "new_customer_growth_ads_cnt");
  ExpandTokens(result, path);
  return;
}

// 店铺新客增长 R3 人群召回
void AdTableNewCustomerGrowthBrandR3Retr(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_new_customer_growth_brand_r3_recall(session_data->get_spdm_ctx())) {
    return;
  }
  const auto& strategy_info_list = session_data->get_ad_request()->ad_user_info().strategy_crowd_info();
  if (strategy_info_list.empty()) {
    return;
  }
  std::vector<int64_t> author_list;
  for (const kuaishou::ad::StrategyCrowdTag& strategy_crowd_info : strategy_info_list) {
    for (size_t i = 0; i < strategy_crowd_info.tag_size(); ++i) {
      if (strategy_crowd_info.tag(i) == kuaishou::ad::StrategyCrowdTag::BRAND_OFFLINE_R3) {
        author_list.emplace_back(strategy_crowd_info.author_id());
      }
    }
  }
  if (author_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("target_type", AdEnum::APPEAL_NEW_CUSTOMER_GROWTH).
        In(kIndexLiveUserId, author_list);
  ExpandTokens(result, path);
  return;
}

void AdTableFanstopLiveAllRetr(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq("item_type", kuaishou::ad::AdEnum::ITEM_LIVE).
      Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2);
  ExpandTokens(result, path);
  return;
}

void AdTableFanstopWhiteListRetr(ContextData* session_data, engine_base::tsm::PathContext* path) {
  // fasntop account white list
  const auto account_white_list = AdKconfUtil::fanstopAccountWhiteListRetrieval();
  if (account_white_list == nullptr || account_white_list->empty()) {
    return;
  }

  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> account_white_vec;
  account_white_vec.assign(account_white_list->begin(), account_white_list->end());
  auto result = all_valid_unit->In(kIndexAccountId, account_white_vec).
      Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2);
  ExpandTokens(result, path);
}

void AdTableFreshOuterProductExploreRetrieval(ContextData* session_data,
                                              engine_base::tsm::PathContext* path) {
  // fasntop account white list
  const auto explore_account_list = AdKconfUtil::freshOuterProductExploreRetrieval();
  if (explore_account_list == nullptr || explore_account_list->empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> account_white_vec;
  account_white_vec.assign(explore_account_list->begin(), explore_account_list->end());
  auto result = all_valid_unit->In(kIndexAccountId, account_white_vec);
  ExpandTokens(result, path);
}

void AdTableFreshInnerProductExploreRetrieval(ContextData* session_data,
                                              engine_base::tsm::PathContext* path) {
  // fasntop account white list
  const auto explore_account_list = AdKconfUtil::freshInnerProductExploreRetrieval();
  if (explore_account_list == nullptr || explore_account_list->empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> account_white_vec;
  account_white_vec.assign(explore_account_list->begin(), explore_account_list->end());
  auto result = all_valid_unit->In(kIndexAccountId, account_white_vec);
  ExpandTokens(result, path);
}

void AdTableNonAmdLiveDirectLiveRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexCampaignType,
                                   kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE);
  if (SPDM_enable_non_amdlive_filter(session_data->get_spdm_ctx())) {
    ExpandTokensFilter(session_data, result, path);
  } else {
    ExpandTokens(result, path);
  }
  return;
}

// aigc 召回
void AdTableInnerAdAigcPhotoRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    "creative_photo_source",
                    static_cast<int64_t>(kuaishou::ad::AdEnum::AIGC_CREATIVE_PHOTO_SOURCE));
  ExpandTokens(result, path);
}

void AdTableOuterAdAigcPhotoRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    "creative_type",
                    static_cast<int64_t>(kuaishou::ad::AdEnum::CREATIVE_STRATEGY_AIGC));
  ExpandTokens(result, path);
}

// 直播剪辑召回
void AdTableInnerAdLiveClipPhotoRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  auto result = all_valid_creative->Eq(
                    "photo_id.photo_source",
                    static_cast<int64_t>(kuaishou::ad::AdEnum::PHOTO_CREATIVE_CENTER))
                    .Eq(
                    "photo_id.photo_sub_source",
                    8);
  ExpandTokens(result, path);
}

// 行业重定向 local 召回
void AdTableAnalyzeIndexDataByOlpRetarget(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto &account_set = session_data->get_olp_retarget_account_set();
  session_data->get_dot()->Interval(account_set.size(), "ad_target.olp_retarget_account_set");
  if (account_set.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> account_vec;
  account_vec.assign(account_set.begin(), account_set.end());
  auto result = all_valid_unit->In(kIndexAccountId, account_vec);
  ExpandTokens(result, path);
  return;
}

// 游戏重定向 local 召回
void AdTableAnalyzeIndexDataByGameRetarget(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto &account_set = session_data->get_game_retarget_account_set();
  session_data->get_dot()->Interval(account_set.size(), "ad_target.game_retarget_account_set");
  if (account_set.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> account_vec;
  account_vec.assign(account_set.begin(), account_set.end());
  auto result = all_valid_unit->In(kIndexAccountId, account_vec);

  // 小游戏 base 组快小重定向
  if (SPDM_enable_mini_game_retarget_recall(session_data->get_spdm_ctx())) {
    result = all_valid_unit->In(kIndexAccountId, account_vec).
                      NotEq(kIndexCampaignType, kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE);
  }
  ExpandTokens(result, path);
  return;
}


// NA 小说重定向 local 召回
void AdTableAnalyzeIndexDataByFictionRetarget(ContextData* session_data,
                                              engine_base::tsm::PathContext* path) {
  // 增加人群限制
  auto crowd_tag = session_data->get_ad_request()->ad_user_info().native_fiction_user_group_tag();
  if (SPDM_enable_fiction_retarget_limit_crowd_tag(session_data->get_spdm_ctx()) &&
      crowd_tag != kuaishou::ad::PRICE_SENSITIVE) {
    session_data->get_dot()->Interval(1, "fiction-retarget-filter-by-crowd-tag");
    return;
  }
  if (SPDM_enable_fiction_olp_unit_retarget(session_data->get_spdm_ctx())) {
    const auto unit_set =
        session_data->Attr(CommonIdx::fiction_retarget_unit_set).GetPtrValue<std::unordered_set<int64_t>>(0);
    if (unit_set == nullptr || unit_set->size() <= 0) {
      return;
    }
    session_data->get_dot()->Interval(unit_set->size(), "fiction_retarget_unit_set");
    auto search_num = path->GetPathConfig().TriggerTokenMaxQuota();
    auto &token_list = path->token_list;
    token_list.reserve(search_num);
    int32_t unit_cnt = 0;
    for (const auto& unit_id : *unit_set) {
      unit_cnt++;
      if (unit_cnt > search_num) {
        break;
      }
      token_list.emplace_back(unit_id, 0.0);
    }
  } else if (SPDM_enable_target_fiction_olp_account_retarget_new(session_data->get_spdm_ctx())) {
    const auto account_set = session_data->Attr(CommonIdx::fiction_retarget_account_set)
                                  .GetPtrValue<std::unordered_set<int64_t>>(0);
    if (account_set == nullptr || account_set->size() <= 0) {
      return;
    }
    session_data->get_dot()->Interval(account_set->size(), "fiction_retarget_account_set");
    auto* all_valid_unit = GetUnitDataFrame(session_data, path);
    if (!all_valid_unit) {
      return;
    }
    std::vector<int64_t> account_vec;
    account_vec.assign(account_set->begin(), account_set->end());
    auto result = all_valid_unit->In(kIndexAccountId, account_vec);
    ExpandTokens(result, path);
  }
  return;
}

// 客户天花板摸高召回扶持
void AdTableCorporationCeilingRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  // 获取加白账户
  const auto &product_white_list =
    AdKconfUtil::corporationCeilingWhiteList()->data().product_ids;
  const auto &account_white_list =
    AdKconfUtil::corporationCeilingWhiteList()->data().account_ids;
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> account_white_vec;
  account_white_vec.assign(account_white_list.begin(), account_white_list.end());
  std::vector<int64_t> product_white_vec;
  product_white_vec.assign(product_white_list.begin(), product_white_list.end());
  auto result_account = all_valid_unit->In(kIndexAccountId, account_white_vec);
  auto result_product = all_valid_unit->In(kIndexProductId, product_white_vec);
  auto result = result_account | result_product;
  ExpandTokens(result, path);
}

// 游戏头客追击召回独立通路
void AdTableGameTouKeRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  // 获取加白账户
  const auto &product_white_list =
    AdKconfUtil::gameTouKeWhiteList()->data().product_ids;
  const auto &account_white_list =
    AdKconfUtil::gameTouKeWhiteList()->data().account_ids;
  if (!account_white_list.empty() || !product_white_list.empty()) {
    auto* all_valid_unit = GetUnitDataFrame(session_data, path);
    if (!all_valid_unit) {
      return;
    }
    std::vector<int64_t> account_white_vec;
    account_white_vec.assign(account_white_list.begin(), account_white_list.end());
    std::vector<int64_t> product_white_vec;
    product_white_vec.assign(product_white_list.begin(), product_white_list.end());
    auto result_account = all_valid_unit->In(kIndexAccountId, account_white_vec);
    auto result_product = all_valid_unit->In(kIndexProductId, product_white_vec);
    auto result = result_account | result_product;
    ExpandTokens(result, path);
  }
}

// 行业 boost 召回通路
void AdTableOuterIndusrtyBoostRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_outer_industry_boost(session_data->get_spdm_ctx())) {
    return;
  }
  const auto& orientations = session_data->get_target_profiler().user_orientation_set;
  if (orientations.empty()) {
    return;
  }
  const auto &outer_industry_boost_config = AdKconfUtil::outerIndustryBoostConfig()->data().exp_config();
  if (outer_industry_boost_config.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
      return;
  }

  auto exp_config_itr = outer_industry_boost_config.find(
    SPDM_outer_industry_boost_name(session_data->get_spdm_ctx()));

  if (exp_config_itr != outer_industry_boost_config.end()) {
    static ad_table::TargetKeyConvertor str_2_int64_;
    const auto& product_set = session_data->get_outer_industry_boost_retarget_product_set();
    auto &account_id_set = session_data->get_outer_industry_boost_retarget_account_set();

    std::vector<int64_t> account_id_vec(account_id_set.begin(), account_id_set.end());
    std::vector<int64_t> product_id_vec;
    for (const auto& product_name : product_set) {
      product_id_vec.emplace_back(str_2_int64_(product_name));
    }
    auto result_product = all_valid_unit->In(kIndexProductId, product_id_vec);
    auto result_account = all_valid_unit->In(kIndexAccountId, account_id_vec);
    auto result = result_account | result_product;
    ExpandTokens(result, path);
  }
}

// 万合创作者垂类本地召回
void AdTableWanheRecallByCreatorClass(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_wanhe_creator_class_recall(session_data->get_spdm_ctx()) ||
      !(session_data->get_pos_manager_base().IsSideWindow() ||
        session_data->get_pos_manager_base().IsProfileSkin())) {
    return;
  }
  const auto &product_set = session_data->get_wanhe_creator_product_set();
  session_data->get_dot()->Interval(product_set.size(), "ad_target.wanhe_product_set");
  if (product_set.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> product_vec;
  product_vec.assign(product_set.begin(), product_set.end());
  auto result = all_valid_unit->In(kIndexProductId, product_vec);
  if (SPDM_enable_wanhe_creator_class_recall_opt(session_data->get_spdm_ctx())) {
    int64_t per_quota = SPDM_wanhe_local_recall_per_key_quota(session_data->get_spdm_ctx());
    ExpandTokensWanheBidSort(result, path, per_quota);
  } else {
    ExpandTokens(result, path);
  }
  return;
}

// 激励直播预约本地召回
void AdTableIncentiveLiveReservationRecallByAuthor(ContextData* session_data,
                                                   engine_base::tsm::PathContext* path) {
  if (session_data->get_pos_manager_base().request_imp_infos.size() <= 0) {
    return;
  }
  int64_t pos_id = session_data->get_pos_manager_base().request_imp_infos[0].pos_id;
  if (!(pos_id == 87846 || pos_id == 87847)) {
    return;
  }
  const auto &authors = AdKconfUtil::incentiveLiveReservationAuthors();
  session_data->get_dot()->Interval(authors->size(), "IncentiveLiveReservationRecallByAuthor.author_size");
  if (authors->size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> author_vec;
  author_vec.assign(authors->begin(), authors->end());
  auto result = all_valid_unit->In(kIndexAccountUserId, author_vec);
  ExpandTokens(result, path);
  return;
}

// 激励唤端产品名本地召回
void AdTableIncentiveInvokedProductRetrieval(ContextData* session_data,
                                            engine_base::tsm::PathContext* path) {
  if (session_data->get_pos_manager_base().request_imp_infos.size() <= 0) {
    return;
  }
  int64_t pos_id = session_data->get_pos_manager_base().request_imp_infos[0].pos_id;
  if (!(pos_id == 210728 || pos_id == 210729) ||
      !SPDM_enable_incentive_invoked_product_retrieval(session_data->get_spdm_ctx())) {
    return;
  }
  session_data->get_dot()->Count(1, "IncentiveInvokedProductRetri.entry");
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> products = session_data->get_incentive_invoked_product_list();
  if (products.empty()) {
    return;
  }
  session_data->get_dot()->Interval(products.size(), "IncentiveInvokedProductRetri.cnt");
  const auto &campaign_type = AdKconfUtil::incentiveInvokedProductCampaignType();
  std::vector<int64_t> campaign_type_vec;
  campaign_type_vec.assign(campaign_type->begin(), campaign_type->end());
  const auto &ocpx_action_type = AdKconfUtil::incentiveInvokedProductOcpxActionType();
  std::vector<int64_t> ocpx_action_type_vec;
  ocpx_action_type_vec.assign(ocpx_action_type->begin(), ocpx_action_type->end());
  auto result = all_valid_unit->In(kIndexProductId, products).
                                In(kIndexCampaignType, campaign_type_vec).
                                In(kIndexOcpxActionType, ocpx_action_type_vec);
  ExpandTokens(result, path);
  return;
}

// 激励沉默粉丝召回任务本地召回
void AdTableIncentiveSilentFansRecallTaskRecallByAuthor(ContextData* session_data,
                                                   engine_base::tsm::PathContext* path) {
  if (session_data->get_pos_manager_base().request_imp_infos.size() <= 0) {
    return;
  }
  int64_t pos_id = session_data->get_pos_manager_base().request_imp_infos[0].pos_id;
  if (!(pos_id == 181313 || pos_id == 181314)) {
    return;
  }
  const auto &target_author_id =
    session_data->get_ad_request()->ad_user_info().incentive_silent_fans_recall_target_author_id();
  session_data->get_dot()->Count(1, "AdTableIncentiveSilentFansRecallTaskRecallByAuthor", absl::StrCat(target_author_id != 0));  //NOLINT
  if (target_author_id <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> author_vec;
  author_vec.push_back(target_author_id);
  auto result = all_valid_unit->In(kIndexAccountUserId, author_vec);
  ExpandTokens(result, path);
  return;
}

// 每留重定向 local 召回
void AdTableRetentionDaysRetarget(ContextData* session_data, engine_base::tsm::PathContext* path) {
  // 获取加白账户
  const auto &account_set = session_data->get_retention_retarget_account_set();
  session_data->get_dot()->Interval(account_set.size(), "ad_target.retention_days_retarget_account");
  if (account_set.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> account_vec;
  account_vec.assign(account_set.begin(), account_set.end());
  auto result = all_valid_unit->In(kIndexAccountId, account_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableIndustryDebutRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  // 获取加白账户
  auto industry_kconf = AdKconfUtil::industryDebutWhiteList()->data();
  const auto& product_white_list = industry_kconf.product_ids;
  const auto& account_white_list = industry_kconf.account_ids;
  const auto& photo_white_list = industry_kconf.photo_ids;

  if (!photo_white_list.empty()) {
    auto search_num = path->GetPathConfig().TriggerTokenMaxQuota();
    auto &token_list = path->token_list;
    token_list.reserve(search_num);
    int32_t photo_cnt = 0;
    for (auto& photo_id : photo_white_list) {
      photo_cnt++;
      if (photo_cnt > search_num) {
        break;
      }
      token_list.emplace_back(photo_id, 0.0);
    }
  } else {
    auto* all_valid_unit = GetUnitDataFrame(session_data, path);
    if (!all_valid_unit) {
      return;
    }
    std::vector<int64_t> account_white_vec;
    account_white_vec.assign(account_white_list.begin(), account_white_list.end());
    std::vector<int64_t> product_white_vec;
    product_white_vec.assign(product_white_list.begin(), product_white_list.end());
    auto result = all_valid_unit->In(kIndexAccountId, account_white_vec).
        In(kIndexProductId, product_white_vec);
    ExpandTokens(result, path);
  }
}

void AdTableHistoryLiveCrowdTrigger(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& strategy_info_list = session_data->get_ad_request()->ad_user_info().strategy_crowd_info();
  if (strategy_info_list.empty()) {
    return;
  }
  std::vector<int64_t> author_list;
  for (const kuaishou::ad::StrategyCrowdTag& strategy_crowd_info : strategy_info_list) {
    for (size_t i = 0; i < strategy_crowd_info.tag_size(); ++i) {
      if (strategy_crowd_info.tag(i) == kuaishou::ad::StrategyCrowdTag::HISTORY_LIVE_TOUCH) {
        author_list.emplace_back(strategy_crowd_info.author_id());
      }
    }
  }
  if (author_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In(kIndexLiveUserId, author_list).
      In(kIndexOcpxActionType, {kuaishou::ad::AD_MERCHANT_FOLLOW, kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY,
                              kuaishou::ad::AD_MERCHANT_FOLLOW_ROI, kuaishou::ad::AD_FANS_TOP_FOLLOW});
  ExpandTokens(result, path);
  return;
}

void AdTableLocalLifeRetargetTrigger(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& inner_recall_tag_list = session_data->get_ad_request()->ad_user_info().inner_recall_tag();
  if (inner_recall_tag_list.empty()) {
    return;
  }
  std::vector<int64_t> author_list;
  for (const kuaishou::ad::InnerRecallTag& crowd_info : inner_recall_tag_list) {
    if (crowd_info.tag_type() == kuaishou::ad::InnerRecallTag::LOCALLIFE_INTEREST_RETARGET) {
      for (const auto& author_id : crowd_info.author_id_list()) {
        author_list.emplace_back(author_id);
      }
    }
  }

  if (author_list.empty()) {
    return;
  }

  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  ks::ad_table::DataFrame result;

  if (!author_list.empty()) {
    std::vector<int64_t> tmp_vec_author;
    tmp_vec_author.assign(author_list.begin(), author_list.end());
    result |= all_valid_unit->In(kIndexLiveUserId, tmp_vec_author);
  }

  auto lsp = all_valid_unit->Eq(kIndexOcpxActionType, kuaishou::ad::EVENT_ORDER_PAIED).
                             Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_LSP);
  result &= lsp;
  ExpandTokens(result, path);
  return;
}

void AdTableLSPStorewide(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexCampaignType, kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE).
                                Eq("campaign_id.scene_oriented_type",
                                   kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE).
                                Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_LSP);
  ExpandTokens(result, path);
  return;
}

void AdTableLSPGeohash(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::string lives_str;
  auto ret = session_data->mutable_redis_result()->lsp_geo_lives_retarget.Get(&lives_str);
  if (ret != ks::infra::KS_INF_REDIS_NO_ERROR || lives_str == "") {
    return;
  }
  std::vector<int64_t> lives_set;
  std::vector<std::string> lives_str_set = absl::StrSplit(lives_str, ",");
  for (const std::string& live_id_str : lives_str_set) {
    int64_t tmp_live_id = 0;
    std::string tmp_live_id_str = live_id_str;
    if (SPDM_enable_lsp_geo_live_ids_to_prerank_v2(session_data->get_spdm_ctx())) {
      std::vector<std::string> tmp_live_id_str_info = absl::StrSplit(live_id_str, "=");
      if (tmp_live_id_str_info.size() > 0) {
        tmp_live_id_str = tmp_live_id_str_info[0];
      }
    }
    if (absl::SimpleAtoi(tmp_live_id_str, &tmp_live_id)) {
      lives_set.emplace_back(tmp_live_id);
    }
  }
  auto result = all_valid_unit->In("live_user_id.live_stream_id", lives_set).
                                Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_LSP);
  ExpandTokens(result, path);
}

void AdTableLSPGreenChannel(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto config = AdKconfUtil::LSPGreenChannelConfigKconf();
  if (config == nullptr) {
    return;
  }
  auto config_data = AdKconfUtil::LSPGreenChannelConfigKconf()->data();
  const auto& author_id_list = config_data.author_id();
  const auto& account_id_list = config_data.account_id();
  if (author_id_list.empty() && account_id_list.empty()) {
    return;
  }

  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  ks::ad_table::DataFrame result;

  if (!author_id_list.empty()) {
    std::vector<int64_t> tmp_vec_author;
    tmp_vec_author.assign(author_id_list.begin(), author_id_list.end());
    result |= all_valid_unit->In(kIndexLiveUserId, tmp_vec_author);
  }

  if (!account_id_list.empty()) {
    std::vector<int64_t> tmp_vec_account;
    tmp_vec_account.assign(account_id_list.begin(), account_id_list.end());
    result |= all_valid_unit->In(kIndexAccountId, tmp_vec_account);
  }

  ks::ad_table::DataFrame lsp;
  lsp = all_valid_unit->Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_LSP);
  result &= lsp;
  ExpandTokens(result, path);
  return;
}

void AdTableLSPItemRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  ks::ad_table::DataFrame lsp_unit;
  lsp_unit = all_valid_unit->Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_LSP);

  ks::ad_table::DataFrame result;
  result |= lsp_unit.Eq("wt_unit_id.locallife_has_boost_items", 1);
  result |= lsp_unit.Eq("wt_unit_id.locallife_has_high_traffic_items", 1);
  result |= lsp_unit.Eq("wt_unit_id.locallife_has_subsidy_items", 1);
  result |= lsp_unit.Eq("wt_unit_id.locallife_has_mkl_beat_items", 1);

  ExpandTokens(result, path);
  return;
}

void AdTableLSP(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexAccountType, kuaishou::ad::AdEnum::ACCOUNT_LSP);
  ExpandTokens(result, path);
  return;
}

void AdTableSevenDaysRoas(ContextData* session_data, engine_base::tsm::PathContext* path) {
  int32_t dataframe_size = 0;
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->Eq(kIndexOcpxActionType, kuaishou::ad::AD_MERCHANT_T7_ROI);
  dataframe_size = result.RowSize();
  session_data->get_dot()->Interval(dataframe_size, "seven_roas_ads_cnt");
  ExpandTokens(result, path);
  return;
}

void AdTableFollowEsp(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> tmp_vec;
  tmp_vec.assign(session_data->get_reco_follow_set().begin(),
    session_data->get_reco_follow_set().end());
  auto result = all_valid_unit->In(kIndexLiveUserId, tmp_vec);
  ExpandTokens(result, path);
  return;
}

void AdTableFollowLSP(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!session_data->get_pos_manager_base().IsFollow()) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  std::vector<int64_t> rec_follow_list(session_data->get_reco_follow_set().begin(),
                                       session_data->get_reco_follow_set().end());
  auto result = all_valid_creative->In("photo_author_id", rec_follow_list)
                                    .Eq("unit_id.campaign_id.account_id.account_type",
                                        kuaishou::ad::AdEnum::ACCOUNT_LSP)
                                    .Eq("unit_id.put_type", 2)
                                    .Eq("unit_id.item_type", kuaishou::ad::AdEnum::ITEM_PHOTO);
  ExpandTokens(result, path);
  return;
}

void AdTableFollowLSPV2(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_follow_page_ad_creative_path(session_data->get_spdm_ctx())) {
    return;
  }
  auto* all_valid_creative = GetCreativeDataFrame(session_data, path);
  if (!all_valid_creative) {
    return;
  }
  std::vector<int64_t> rec_follow_list(session_data->get_reco_follow_set().begin(),
                                       session_data->get_reco_follow_set().end());
  auto result = all_valid_creative->In("photo_author_id", rec_follow_list);

  ExpandTokens(result, path);
  return;
}

void AdTableSmbFollowEsp(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> tmp_vec;
  tmp_vec.assign(session_data->get_reco_follow_set().begin(),
    session_data->get_reco_follow_set().end());
  // 中小新客=4 非中小新客=5
  auto result = all_valid_unit->In(kIndexLiveUserId, tmp_vec)
                               .In("campaign_id.account_id.wt_account_id.smb_level_tag", {4, 5});
  ExpandTokens(result, path);
  return;
}

void AdTableRetrieveParticularProducts(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& orientations = session_data->get_target_profiler().user_orientation_set;
  if (orientations.empty()) {
    return;
  }
  auto orientation_config = AdKconfUtil::orientationsForParticularProductsRetrieval();
  if (!orientation_config) {
    return;
  }
  bool hit = false;
  for (auto orientation : *orientation_config) {
    if (orientations.contains(orientation)) {
      hit = true;
      break;
    }
  }
  if (!hit) {
    return;
  }
  auto product_ids_config = AdKconfUtil::productIdsForParticularProductsRetrieval();
  if (!product_ids_config) {
    return;
  }
  std::vector<ad_table::Key128> index_keys;
  for (auto product_id : *product_ids_config) {
    index_keys.push_back(ad_table::HashOf(product_id));
  }
  auto* unit_df = GetUnitDataFrame(session_data, path);
  if (!unit_df) {
    return;
  }
  auto res = unit_df->In("account.city_product_id", index_keys);
  ExpandTokens(res, path);
}

void AdTableOrientationRetrieveAccount(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_orientation_retrieve_account(session_data->get_spdm_ctx())) {
    return;
  }
  const auto& orientations = session_data->get_target_profiler().user_orientation_set;
  if (orientations.empty()) {
    return;
  }

  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
      return;
  }
  ks::ad_table::DataFrame result;

  if (AdKconfUtil::orientationRetrievalAccountConfig() == nullptr) {
    return;
  }
  const auto&  orientation_account_map =
    AdKconfUtil::orientationRetrievalAccountConfig()->data().GetExpOrientation2AccountsMap();
  if (!orientation_account_map.empty()) {
    std::unordered_set<int64_t> account_set;
    for (const auto& it : orientation_account_map) {
      if (orientations.contains(it.first)) {
        const auto& id_set = it.second;
        account_set.insert(id_set.begin(), id_set.end());
      }
    }
    if (!account_set.empty()) {
      std::vector<int64_t> tmp_vec;
      tmp_vec.assign(account_set.begin(), account_set.end());
      if (tmp_vec.size() > 100) {
        tmp_vec.resize(100);
      }
      result |= all_valid_unit->In("campaign_id.account_id.id",
                                        tmp_vec);
    }
  }

  const auto&  orientation_user_map =
    AdKconfUtil::orientationRetrievalAccountConfig()->data().GetExpOrientation2UsersMap();
  if (!orientation_user_map.empty()) {
    std::unordered_set<int64_t> user_set;
    for (const auto& it : orientation_user_map) {
      if (orientations.contains(it.first)) {
        const auto& id_set = it.second;
        user_set.insert(id_set.begin(), id_set.end());
      }
    }
    if (!user_set.empty()) {
      std::vector<int64_t> tmp_vec;
      tmp_vec.assign(user_set.begin(), user_set.end());
      if (tmp_vec.size() > 100) {
        tmp_vec.resize(100);
      }
      result |= all_valid_unit->In("campaign_id.account_id.user_id",
                                        tmp_vec);
    }
  }

  const auto&  orientation_frist_industry_map =
    AdKconfUtil::orientationRetrievalAccountConfig()->data().GetExpOrientation2FirstIndustrysMap();
  if (!orientation_frist_industry_map.empty()) {
    std::unordered_set<int64_t> first_industry_set;
    for (const auto& it : orientation_frist_industry_map) {
      if (orientations.contains(it.first)) {
        const auto& id_set = it.second;
        first_industry_set.insert(id_set.begin(), id_set.end());
      }
    }
    if (!first_industry_set.empty()) {
      std::vector<int64_t> tmp_vec;
      tmp_vec.assign(first_industry_set.begin(), first_industry_set.end());
      if (tmp_vec.size() > 100) {
        tmp_vec.resize(100);
      }
      result |= all_valid_unit->In("campaign_id.account_id.first_industry_id_v6",
                                        tmp_vec);
    }
  }

  const auto&  orientation_second_industry_map =
    AdKconfUtil::orientationRetrievalAccountConfig()->data().GetExpOrientation2SecondIndustrysMap();
  if (!orientation_second_industry_map.empty()) {
    std::unordered_set<int64_t> second_industry_set;
    for (const auto& it : orientation_second_industry_map) {
      if (orientations.contains(it.first)) {
        const auto& id_set = it.second;
        second_industry_set.insert(id_set.begin(), id_set.end());
      }
    }
    if (!second_industry_set.empty()) {
      std::vector<int64_t> tmp_vec;
      tmp_vec.assign(second_industry_set.begin(), second_industry_set.end());
      if (tmp_vec.size() > 100) {
        tmp_vec.resize(100);
      }
      result |= all_valid_unit->In("campaign_id.account_id.second_industry_id_v6",
                                        tmp_vec);
    }
  }
  result &= all_valid_unit->In("campaign_id.account_id.account_type",
                                     {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                      kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                      kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
}

void AdTableOrientationRetrieveAccountBase(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!SPDM_enable_orientation_retrieve_account_base(session_data->get_spdm_ctx())) {
    return;
  }
  const auto& orientations = session_data->get_target_profiler().user_orientation_set;
  if (orientations.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
      return;
  }
  ks::ad_table::DataFrame result;

  if (AdKconfUtil::orientationRetrievalAccountConfig() == nullptr) {
    return;
  }
  const auto&  orientation_account_map =
    AdKconfUtil::orientationRetrievalAccountConfig()->data().GetBaseOrientation2AccountsMap();
  if (!orientation_account_map.empty()) {
    std::unordered_set<int64_t> account_set;
    for (const auto& it : orientation_account_map) {
      if (orientations.contains(it.first)) {
        const auto& id_set = it.second;
        account_set.insert(id_set.begin(), id_set.end());
      }
    }
    if (!account_set.empty()) {
      std::vector<int64_t> tmp_vec;
      tmp_vec.assign(account_set.begin(), account_set.end());
      if (tmp_vec.size() > 100) {
        tmp_vec.resize(100);
      }
      result |= all_valid_unit->In("campaign_id.account_id.id",
                                        tmp_vec);
    }
  }

  const auto&  orientation_user_map =
    AdKconfUtil::orientationRetrievalAccountConfig()->data().GetBaseOrientation2UsersMap();
  if (!orientation_user_map.empty()) {
    std::unordered_set<int64_t> user_set;
    for (const auto& it : orientation_user_map) {
      if (orientations.contains(it.first)) {
        const auto& id_set = it.second;
        user_set.insert(id_set.begin(), id_set.end());
      }
    }
    if (!user_set.empty()) {
      std::vector<int64_t> tmp_vec;
      tmp_vec.assign(user_set.begin(), user_set.end());
      if (tmp_vec.size() > 100) {
        tmp_vec.resize(100);
      }
      result |= all_valid_unit->In("campaign_id.account_id.user_id",
                                        tmp_vec);
    }
  }

  const auto&  orientation_frist_industry_map =
    AdKconfUtil::orientationRetrievalAccountConfig()->data().GetBaseOrientation2FirstIndustrysMap();
  if (!orientation_frist_industry_map.empty()) {
    std::unordered_set<int64_t> first_industry_set;
    for (const auto& it : orientation_frist_industry_map) {
      if (orientations.contains(it.first)) {
        const auto& id_set = it.second;
        first_industry_set.insert(id_set.begin(), id_set.end());
      }
    }
    if (!first_industry_set.empty()) {
      std::vector<int64_t> tmp_vec;
      tmp_vec.assign(first_industry_set.begin(), first_industry_set.end());
      if (tmp_vec.size() > 100) {
        tmp_vec.resize(100);
      }
      result |= all_valid_unit->In("campaign_id.account_id.first_industry_id_v6",
                                        tmp_vec);
    }
  }

  const auto&  orientation_second_industry_map =
    AdKconfUtil::orientationRetrievalAccountConfig()->data().GetBaseOrientation2SecondIndustrysMap();
  if (!orientation_second_industry_map.empty()) {
    std::unordered_set<int64_t> second_industry_set;
    for (const auto& it : orientation_second_industry_map) {
      if (orientations.contains(it.first)) {
        const auto& id_set = it.second;
        second_industry_set.insert(id_set.begin(), id_set.end());
      }
    }
    if (!second_industry_set.empty()) {
      std::vector<int64_t> tmp_vec;
      tmp_vec.assign(second_industry_set.begin(), second_industry_set.end());
      if (tmp_vec.size() > 100) {
        tmp_vec.resize(100);
      }
      result |= all_valid_unit->In("campaign_id.account_id.second_industry_id_v6",
                                        tmp_vec);
    }
  }
  result &= all_valid_unit->In("campaign_id.account_id.account_type",
                                     {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                      kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                      kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
}

void AdTableRetrieveForXifan(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* unit_df = GetUnitDataFrame(session_data, path);
  if (!unit_df) {
    return;
  }
  auto res = unit_df->Eq("ad_kstube_tube_pool.tube_id", ad_table::Key128::Key4All());
  ExpandTokens(res, path);
}

void AdTableOuterloopCrossIndEnhanceRetrieval(ContextData* session_data,
                                              engine_base::tsm::PathContext* path) {
  std::vector<int64_t> product_ids;
  std::vector<int64_t> industry_ids;
  static ad_table::TargetKeyConvertor str_2_int64_;
  auto ocpx_industry_map_config = AdKconfUtil::OuterLoopAcIndustry();
  for (const auto &product_name : session_data->get_outerloop_interest_retarget_product()) {
    product_ids.emplace_back(str_2_int64_(product_name));
  }
  for (const auto &industry_id_str : session_data->get_outerloop_interest_industry()) {
    int64_t industry_id;
    if (absl::SimpleAtoi(industry_id_str, &industry_id)) {
      industry_ids.emplace_back(industry_id);
    }
  }
  if (product_ids.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In(kIndexProductId, product_ids)
                              .NotIn(kIndexParentId, industry_ids);
  ExpandTokens(result, path);
}

void AdTableOuterloopUniverseRetargetRetrieval(ContextData* session_data,
                                               engine_base::tsm::PathContext* path) {
  std::vector<int64_t> product_ids;
  for (const auto &product_id :
    session_data->get_ad_request()->ad_user_info().universe_user_query_recalled_product_ids()) {
    product_ids.emplace_back(product_id);
  }
  if (product_ids.empty()) {
    session_data->get_dot()->Count(1, "OuterloopUniverseRetargetRetrieval", "empty_data");
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In(kIndexProductId, product_ids);
  if (result.RowSize() > 0) {
    session_data->get_dot()->Interval(result.RowSize(),
      "OuterloopUniverseRetargetRetrieval", "succeed");
  } else {
    session_data->get_dot()->Count(1, "OuterloopUniverseRetargetRetrieval", "invalid_pv");
  }
  ExpandTokens(result, path);
}

void AdTablePlayletAggrCardSecondRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  // 参考 ad_rank/common/ad_item_boolean_func.cc 中 is_paid_duanju_ad_v3，只使用 campain_type 判断
  auto result = all_valid_unit->Eq(kIndexCampaignType, kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION);
  ExpandTokens(result, path);
}

void AdTableOuterloopAppListSignalRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  std::vector<int64_t> industry_ids;
  for (const auto &industry_id_str : session_data->get_outerloop_interest_second_industry()) {
    int64_t industry_id;
    if (absl::SimpleAtoi(industry_id_str, &industry_id)) {
      industry_ids.emplace_back(industry_id);
    }
  }
  if (industry_ids.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  auto result = all_valid_unit->In(
      "campaign_id.account_id.second_industry_id_v5",
      industry_ids);
  ExpandTokens(result, path);
}

void AdTableKconfAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  std::set<int64_t> author_id_set;
  const auto author_author_map = ks::ad_target::AdKconfUtil::adAuthor2Author();
  if (author_author_map == nullptr || author_author_map->empty()) {
    return;
  }
  for (auto key_id : session_data->get_ad_request()->ad_user_info().rank_author_list()) {
    std::string key_author = absl::StrCat(key_id);
    if (author_author_map->find(key_author) != author_author_map->end()) {
      std::string target_author_list = author_author_map->at(key_author);
      std::vector<std::string> author_ids;
      base::SplitString(target_author_list, ",", &author_ids);
      for (auto id : author_ids) {
        int64_t author_id = 0;
        if (absl::SimpleAtoi(id, &author_id) && author_id != 0) {
          author_id_set.insert(author_id);
        }
      }
    }
  }
  if (author_id_set.empty()) {
    return;
  }
  session_data->get_dot()->Interval(author_id_set.size(), "author_id_set size");

  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> tmp_vec;
  tmp_vec.assign(author_id_set.begin(), author_id_set.end());
  auto result = all_valid_unit->In(kIndexAccountUserId,
                                   tmp_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableHUserV1SimClkItemPhoto(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& id_list = session_data->get_ad_request()->ad_user_info()
                                            .inner_user_h_sim_clk_item_list();
  if (id_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> id_vec;
  for (int i = 0; i < id_list.size() && i < 200; i++) {
    int64_t tmp_id = 0;
    if (absl::SimpleAtoi(id_list[i], &tmp_id) && tmp_id != 0) {
      id_vec.emplace_back(tmp_id);
    }
  }
  auto result = all_valid_unit->In("item_id", id_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableHUserV2SimClkItemPhoto(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& id_list = session_data->get_ad_request()->ad_user_info()
                                            .inner_user_h_v2_sim_clk_item_list();
  if (id_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> id_vec;
  for (int i = 0; i < id_list.size() && i < 200; i++) {
    int64_t tmp_id = 0;
    if (absl::SimpleAtoi(id_list[i], &tmp_id) && tmp_id != 0) {
      id_vec.emplace_back(tmp_id);
    }
  }
  auto result = all_valid_unit->In("item_id", id_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableHUserV1SimClkItemLive(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& id_list = session_data->get_ad_request()->ad_user_info()
            .inner_user_h_sim_clk_item_list();
  if (id_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> id_vec;
  for (int i = 0; i < id_list.size() && i < 200; i++) {
    int64_t tmp_id = 0;
    if (absl::SimpleAtoi(id_list[i], &tmp_id) && tmp_id != 0) {
      id_vec.emplace_back(tmp_id);
    }
  }
  auto result = all_valid_unit->In("live_wt_live_id.ecom_item_ids", id_vec).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableHUserV2SimClkItemLive(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& id_list = session_data->get_ad_request()->ad_user_info()
            .inner_user_h_v2_sim_clk_item_list();
  if (id_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> id_vec;
  for (int i = 0; i < id_list.size() && i < 200; i++) {
    int64_t tmp_id = 0;
    if (absl::SimpleAtoi(id_list[i], &tmp_id) && tmp_id != 0) {
      id_vec.emplace_back(tmp_id);
    }
  }
  auto result = all_valid_unit->In("live_wt_live_id.ecom_item_ids", id_vec).
                                In(kIndexAccountType,
                                  {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfMixAuctionItem(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& id_list = session_data->get_ad_request()->ad_user_info()
                                            .inner_user_shelf_mix_auction_item_list();
  if (id_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> id_vec;
  for (int i = 0; i < id_list.size(); i++) {
    int64_t tmp_id = 0;
    if (absl::SimpleAtoi(id_list[i], &tmp_id) && tmp_id != 0) {
      id_vec.emplace_back(tmp_id);
    }
  }
  auto result = all_valid_unit->In("item_id", id_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfMixAuctionAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& id_list = session_data->get_ad_request()->ad_user_info()
                                            .inner_user_shelf_mix_auction_author_list();
  if (id_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> id_vec;
  for (int i = 0; i < id_list.size(); i++) {
    int64_t tmp_id = 0;
    if (absl::SimpleAtoi(id_list[i], &tmp_id) && tmp_id != 0) {
      id_vec.emplace_back(tmp_id);
    }
  }
  auto result = all_valid_unit->In(kIndexAccountUserId, id_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfLiveMixAuctionItem(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& id_list = session_data->get_ad_request()->ad_user_info()
                                            .inner_user_shelf_live_mix_auction_item_list();
  if (id_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> id_vec;
  for (int i = 0; i < id_list.size() && i < 200; i++) {
    int64_t tmp_id = 0;
    if (absl::SimpleAtoi(id_list[i], &tmp_id) && tmp_id != 0) {
      id_vec.emplace_back(tmp_id);
    }
  }
  auto result = all_valid_unit->In("live_user_id.live_stream_id", id_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfLiveMixAuctionAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& id_list = session_data->get_ad_request()->ad_user_info()
                                            .inner_user_shelf_live_mix_auction_author_list();
  if (id_list.empty()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  std::vector<int64_t> id_vec;
  for (int i = 0; i < id_list.size() && i < 200; i++) {
    int64_t tmp_id = 0;
    if (absl::SimpleAtoi(id_list[i], &tmp_id) && tmp_id != 0) {
      id_vec.emplace_back(tmp_id);
    }
  }
  auto result = all_valid_unit->In(kIndexAccountUserId, id_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfShowItem(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& good_show_info =
    session_data->get_ad_request()->ad_user_info().colossus_merchant_good_show_item_data_map();
  auto iter = good_show_info.find("commodity_id");
  if (iter == good_show_info.end()) {
    return;
  }
  const auto& item_list = iter->second.int_list_value();
  if (item_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> item_set;
  for (int i = 0; i < item_list.size(); i++) {
    item_set.insert(item_list[i]);
  }
  std::vector<int64_t> item_vec;
  item_vec.assign(item_set.begin(), item_set.end());
  auto result = all_valid_unit->In("item_id", item_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfSearchItem(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& good_search_info =
    session_data->get_ad_request()->ad_user_info().colossus_good_search_item_data_map();
  auto iter = good_search_info.find("item_id");
  if (iter == good_search_info.end()) {
    return;
  }
  const auto& item_list = iter->second.int_list_value();
  if (item_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> item_set;
  int64 item_cnt = 0;
  for (int i = 0; i < item_list.size(); i++) {
    if (SPDM_enable_shelf_search_recall_up(session_data->get_spdm_ctx())) {
      if (item_cnt < SPDM_shelf_search_recall_limit_nums(session_data->get_spdm_ctx())) {
        const auto& ret = item_set.insert(item_list[i]);
        if (ret.second) {
          item_cnt++;
        }
      }
    } else {
      item_set.insert(item_list[i]);
    }
  }
  std::vector<int64_t> item_vec;
  item_vec.assign(item_set.begin(), item_set.end());
  auto result = all_valid_unit->In("item_id", item_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableShelfSearchAuthor(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& good_search_info =
    session_data->get_ad_request()->ad_user_info().colossus_good_search_item_data_map();
  auto iter = good_search_info.find("seller_id");
  if (iter == good_search_info.end()) {
    return;
  }
  const auto& author_list = iter->second.int_list_value();
  if (author_list.size() <= 0) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> author_set;
  int64 author_cnt = 0;
  for (int i = 0; i < author_list.size(); i++) {
    if (SPDM_enable_shelf_search_recall_up(session_data->get_spdm_ctx())) {
      if (author_cnt < SPDM_shelf_search_recall_limit_nums(session_data->get_spdm_ctx())) {
        const auto& ret = author_set.insert(author_list[i]);
        if (ret.second) {
          author_cnt++;
        }
      }
    } else {
      author_set.insert(author_list[i]);
    }
  }
  std::vector<int64_t> author_vec;
  author_vec.assign(author_set.begin(), author_set.end());
  auto result = all_valid_unit->In(kIndexAccountUserId, author_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableFXClickItem(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto seller_iter = click_info.find("seller_id");  // 货主
  auto real_seller_iter = click_info.find("real_seller_id");
  auto timestamp_iter = click_info.find("click_timestamp");
  auto item_iter = click_info.find("item_id");
  int64 current_time_stamp = base::GetTimestamp() / 1000000;
  int64 time_interval = 3600 * SPDM_fx_retarget_time_interval_hour(session_data->get_spdm_ctx());
  if (seller_iter == click_info.end() || real_seller_iter == click_info.end() ||
      timestamp_iter == click_info.end() || item_iter == click_info.end()) {
    return;
  }
  const auto& seller_list = seller_iter->second.int_list_value();
  const auto& real_seller_list = real_seller_iter->second.int_list_value();
  const auto& timestamp_list = timestamp_iter->second.int_list_value();
  const auto& item_list = item_iter->second.int_list_value();
  if (seller_list.size() <= 0 || seller_list.size() != real_seller_list.size() ||
      seller_list.size() != timestamp_list.size() || seller_list.size() != item_list.size()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> item_set;
  for (int i = 0; i < seller_list.size(); i++) {
    if (timestamp_list[i] < current_time_stamp - time_interval) {
      break;
    }
    if (seller_list[i] != real_seller_list[i]) {
      item_set.insert(item_list[i]);
    }
  }
  std::vector<int64_t> item_vec;
  item_vec.assign(item_set.begin(), item_set.end());
  auto result = all_valid_unit->In("item_id", item_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableFXClickSeller(ContextData* session_data, engine_base::tsm::PathContext* path) {
  const auto& click_info = session_data->get_ad_request()->ad_user_info().colossus_data_map();
  auto seller_iter = click_info.find("seller_id");  // 货主
  auto real_seller_iter = click_info.find("real_seller_id");
  auto timestamp_iter = click_info.find("click_timestamp");
  int64 current_time_stamp = base::GetTimestamp() / 1000000;
  int64 time_interval = 3600 * SPDM_fx_retarget_time_interval_hour(session_data->get_spdm_ctx());
  if (seller_iter == click_info.end() || real_seller_iter == click_info.end() ||
      timestamp_iter == click_info.end()) {
    return;
  }
  const auto& seller_list = seller_iter->second.int_list_value();
  const auto& real_seller_list = real_seller_iter->second.int_list_value();
  const auto& timestamp_list = timestamp_iter->second.int_list_value();
  if (seller_list.size() <= 0 || seller_list.size() != real_seller_list.size() ||
      seller_list.size() != timestamp_list.size()) {
    return;
  }
  auto* all_valid_unit = GetUnitDataFrame(session_data, path);
  if (!all_valid_unit) {
    return;
  }
  absl::flat_hash_set<int64_t> seller_set;
  for (int i = 0; i < seller_list.size(); i++) {
    if (timestamp_list[i] < current_time_stamp - time_interval) {
      break;
    }
    if (seller_list[i] != real_seller_list[i]) {
      seller_set.insert(seller_list[i]);
    }
  }
  std::vector<int64_t> seller_vec;
  seller_vec.assign(seller_set.begin(), seller_set.end());
  auto result = all_valid_unit->In(kIndexAccountUserId, seller_vec).
                                In(kIndexAccountType,
                                   {kuaishou::ad::AdEnum::ACCOUNT_LSP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP,
                                    kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE});
  ExpandTokens(result, path);
  return;
}

void AdTableXifanIaaRetrieval(ContextData* session_data, engine_base::tsm::PathContext* path) {
  if (!session_data->get_pos_manager_base().IsXifanInspire()) {
    return;
  }
  // 实验
  if (!SPDM_enable_xifan_inspire_iaa(session_data->get_spdm_ctx())) {
    return;
  }
  // 版控
  auto config = AdKconfUtil::xifanInspireIaaVersion();
  if (!config) {
    return;
  }
  int64_t app_version = ad_base::OsVersionTrans(session_data->app_version());
  if (app_version < ad_base::OsVersionTrans(*config)) {
    return;
  }
  // IOS 不支持 IAA 短剧, 不放开
  if (session_data->get_is_ios_platform()) {
    return;
  }
  auto* unit_df = GetUnitDataFrame(session_data, path);
  if (!unit_df) {
    return;
  }
  std::vector<ad_table::Key128> ocpx_action_type_keys{
      ad_table::HashOf(kuaishou::ad::AdActionType::EVENT_KEY_INAPP_ACTION),
      ad_table::HashOf(kuaishou::ad::AdActionType::AD_IAA_ROAS),
      ad_table::HashOf(kuaishou::ad::AdActionType::AD_SERIAL_IAA_ROAS)};
  auto res = unit_df->Eq("ad_kstube_tube_pool.tube_id", ad_table::Key128::Key4All())
                 .Eq("ad_kstube_tube_pool.source", ad_table::HashOf(7))
                 .Eq("ad_kstube_tube_pool.pay_type", ad_table::HashOf(1))
                 .Eq("campaign.type", ad_table::HashOf(kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION))
                 .In("unit.ocpx_action_type", ocpx_action_type_keys);
  ExpandTokens(res, path);
}

}  // namespace ad_target
}  // namespace ks
