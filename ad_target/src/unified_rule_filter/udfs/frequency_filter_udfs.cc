#include <unordered_set>
#include <algorithm>
#include <limits>
#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"
#include "teams/ad/ad_target/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_target/utils/kconf/kconf.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Campaign.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Creative.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Account.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Unit.h"
#include "teams/ad/ad_target/utils/table_extension/ad_table_wrapper.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/AdMagicSitePageDas.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/AdAppRelease.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/IndustryV3.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Target.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/WTLive.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/WTAccount.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Agent.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/LiveStreamUserInfo.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/PhotoStatus.h"
#include "teams/ad/ad_target/utils/common/ad_table_util.h"
#include "teams/ad/ad_target/src/unified_rule_filter/src/rule_filter_udf.h"
#include "teams/ad/ad_target/src/unified_rule_filter/src/rule_filter_unified_manager.h"
#include "teams/ad/ad_target/utils/component/inner_loop/utils/flow_type_utils.h"
#include "teams/ad/ad_proto/kuaishou/fanstop/common/fans_top_enums.pb.h"

namespace ks {
namespace ad_target {
using component::inner_loop::FlowType;
using kuaishou::fanstop::FansTopEnum;
static std::vector<std::string> photo_freq_control_tag_list = {"dup_photo", "spu", "face_id"};
static std::vector<std::string> live_freq_control_tag_list = {"spu", "face_id"};
enum ConvertType {
  UNIT_DATA = 0,
  ACCOUNT_DATA,
  CAMPAIGN_DATA,
  APP_DATA,
  CORPORATION_DATA,
  DEFINE_PRODUCT_DATA,
  PACKAGE_DATA,
};

struct ConvertedItem {
  int ocpx_action_type = 0;
  int range_type = 0;
};

struct ConvertedInfo {
  // new action 参考 ocpx
  absl::flat_hash_map<int, int> ocpx_action_type;
  // 老的只管 range_type
  int range_type = std::numeric_limits<int>::max();
  // 标识状态
  bool status = false;

  void Merge(const ConvertedItem& r) {
    status = true;
    auto it = ocpx_action_type.find(r.ocpx_action_type);
    if (it == ocpx_action_type.end()) {
      ocpx_action_type[r.ocpx_action_type] = r.range_type;
    } else {
      it->second = std::min(r.range_type, it->second);
    }
    range_type = r.range_type;
  }

  bool Hit(bool new_action, int m_ocpx_action_type, int m_range_type) const {
    if (new_action) {
      auto it = ocpx_action_type.find(m_ocpx_action_type);
      if (it == ocpx_action_type.end()) {
        return false;
      }
      return m_range_type >= it->second;
    }
    return m_range_type >= range_type;
  }
};

int ConvertedTimeRange(int64_t time) {
  static int64_t kNinetyDay = (int64_t)90 * 24 * 3600 * 1000;
  static int64_t kSixtyDay = (int64_t)60 * 24 * 3600 * 1000;
  static int64_t kThirtyDay = (int64_t)30 * 24 * 3600 * 1000;
  static int64_t days_180 = 180 * 24 * 3600 * 1000LL;
  static int64_t days_365 = 365 * 24 * 3600 * 1000LL;

  if (time > days_365) {
    return -1;
  } else if (time > days_180) {
    return 4;
  } else if (time > kNinetyDay) {
    return 3;
  } else if (time > kSixtyDay) {
    return 2;
  } else if (time > kThirtyDay) {
    return 1;
  } else {
    return 0;
  }
  return -1;
}
void GetTagSuffix(const std::string& tag_prefix, const RetrievalAdCommon& ad,
                                     std::vector<std::string>* tag_list,
                                     ks::platform::AddibleRecoContextInterface* context) {
  auto* session_data = context->GetMutablePtrCommonAttr<ContextData>("session_data");
  tag_list->clear();
  if (tag_prefix == "dup_photo") {
    int64_t dup_photo_id = ad.item_ids.dup_photo_id_a;
    if (dup_photo_id > 0) {
      tag_list->push_back(std::to_string(dup_photo_id));
    }
  } else if (tag_prefix == "spu") {
    if (!FlowType::IsExplore(session_data) && !FlowType::IsSelected(session_data) &&
        !FlowType::IsThanos(session_data)) {
      return;
    }
    if (session_data->get_enable_context_init_optimization() && ad.base.p_wt_live) {
      const auto& spu_ids = ad.base.p_wt_live->spu_ids_list();
      for (int64_t spu_id : spu_ids) {
        if (spu_id > 0) {
          tag_list->push_back(std::to_string(spu_id));
        }
      }
    }
  } else if (tag_prefix == "face_id") {
    int64_t face_id = 0;
    int64_t face_id_two = 0;
    if (session_data->get_enable_context_init_optimization() && ad.base.p_wt_live) {
      face_id = ad.base.p_wt_live->face_id();
      face_id_two = ad.base.p_wt_live->face_id_two();
    }
    if (face_id > 0) {
      tag_list->push_back(std::to_string(face_id));
    }
    if (face_id_two > 0) {
      tag_list->push_back(std::to_string(face_id_two));
    }
  }
}
//---------------------------------------- 分割线，以下为过滤算子 ------------------------------------

void FrequencyCoverIdFilter(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (!session_data->get_targeting_params().p_fre_prepare_result_) {
    return;
  }

  if (session_data->get_pos_manager_base().IsNebulaTaskEntranceTraffic() &&
       AdKconfUtil::enableTaskEntranceSkipFilter()) {
    return;
  }
  std::unordered_set<int64_t> freq_cover_merged_;
  const auto &prepare_result = *session_data->get_targeting_params().p_fre_prepare_result_;

  int64 browsed_cover_size = prepare_result.browsed_cover.size();
  int64 adlog_cover_size = prepare_result.adlog_cover.size();
  int64 browsed_sp_cover_size = prepare_result.browsed_sp_cover.size();
  int64 adlog_sp_cover_size = prepare_result.adlog_sp_cover.size();
  int64 hate_cover_size = prepare_result.hate_cover.size();

  if (browsed_cover_size > 0) {
    freq_cover_merged_.insert(prepare_result.browsed_cover.begin(),
                              prepare_result.browsed_cover.end());
  }
  if (adlog_cover_size > 0) {
    freq_cover_merged_.insert(prepare_result.adlog_cover.begin(),
                              prepare_result.adlog_cover.end());
  }
  if (browsed_sp_cover_size > 0) {
    freq_cover_merged_.insert(prepare_result.browsed_sp_cover.begin(),
                              prepare_result.browsed_sp_cover.end());
  }
  if (adlog_sp_cover_size > 0) {
    freq_cover_merged_.insert(prepare_result.adlog_sp_cover.begin(),
                              prepare_result.adlog_sp_cover.end());
  }
  if (hate_cover_size > 0) {
    freq_cover_merged_.insert(prepare_result.hate_cover.begin(),
                              prepare_result.hate_cover.end());
  }

  if (!(!freq_cover_merged_.empty() && AdKconfUtil::enableFeedFreqCoverFilter() &&
      session_data->get_pos_manager_base().GetInteractiveForm() ==
        kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_FEED)) {
    return;
  }

  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    if (freq_cover_merged_.find(ad.cover_id()) != freq_cover_merged_.end()) {
      return FILTERED;
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}

RegistorRuleFilter(FrequencyCoverIdFilter,
            kuaishou::log::ad::AdTraceFilterCondition::FREQ_COVER_SET_FLITER);

void FrequencyDupCoverIdFilter(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  // 构建 freq_dup_cover_merged_
  if (!session_data->get_targeting_params().p_fre_prepare_result_) {
    return;
  }
  if (session_data->get_pos_manager_base().IsNebulaTaskEntranceTraffic() &&
       AdKconfUtil::enableTaskEntranceSkipFilter()) {
    return;
  }
  std::unordered_set<int64_t> freq_dup_cover_merged_;
  const auto &prepare_result = *session_data->get_targeting_params().p_fre_prepare_result_;

  int64 browsed_dup_cover_size = prepare_result.browsed_dup_cover.size();
  int64 adlog_dup_cover_size = prepare_result.adlog_dup_cover.size();
  int64 hate_cover_size = prepare_result.hate_cover.size();
  int64 browsed_sp_dup_cover_size = prepare_result.browsed_sp_dup_cover.size();
  int64 adlog_sp_dup_cover_size = prepare_result.adlog_sp_dup_cover.size();

  if (browsed_dup_cover_size > 0) {
    freq_dup_cover_merged_.insert(prepare_result.browsed_dup_cover.begin(),
                            prepare_result.browsed_dup_cover.end());
  }
  if (adlog_dup_cover_size > 0) {
    freq_dup_cover_merged_.insert(prepare_result.adlog_dup_cover.begin(),
                                  prepare_result.adlog_dup_cover.end());
  }
  if (hate_cover_size > 0) {
    freq_dup_cover_merged_.insert(prepare_result.hate_cover.begin(),
                                  prepare_result.hate_cover.end());
  }
  if (browsed_sp_dup_cover_size > 0) {
    freq_dup_cover_merged_.insert(prepare_result.browsed_sp_dup_cover.begin(),
                            prepare_result.browsed_sp_dup_cover.end());
  }
  if (adlog_sp_dup_cover_size > 0) {
    freq_dup_cover_merged_.insert(prepare_result.adlog_sp_dup_cover.begin(),
                            prepare_result.adlog_sp_dup_cover.end());
  }

  if (!(!freq_dup_cover_merged_.empty() && AdKconfUtil::enableFeedFreqCoverFilter() &&
    session_data->get_pos_manager_base().GetInteractiveForm() ==
      kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_FEED)) {
    return;
  }

  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    if (freq_dup_cover_merged_.find(ad.dup_cover_id()) != freq_dup_cover_merged_.end()) {
      return FILTERED;
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}

RegistorRuleFilter(FrequencyDupCoverIdFilter,
            kuaishou::log::ad::AdTraceFilterCondition::FREQ_COVER_SET_FLITER);

void ConvertedDataFilter(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (session_data->get_pos_manager_base().IsNebulaTaskEntranceTraffic() &&
       AdKconfUtil::enableTaskEntranceSkipFilter()) {
    return;
  }
  int64_t wrong_filt_num_ = 0;
  int64_t not_filt_num_ = 0;
  int64_t cnt = 0;
  auto start_us = base::GetTimestamp();
  int64_t cur_ms_ = start_us / 1000;
  const auto& ad_cov_event = session_data->get_ad_request()->ad_user_info().ad_cov_event();
  using ConvertType = absl::flat_hash_map<int, absl::flat_hash_map<int64_t, std::array<ConvertedInfo, 2>>>;
  ConvertType conv_data_;

  auto& UNIT_DATA_ = conv_data_[UNIT_DATA];
  auto& ACCOUNT_DATA_ = conv_data_[ACCOUNT_DATA];
  auto& CAMPAIGN_DATA_ = conv_data_[CAMPAIGN_DATA];
  auto& APP_DATA_ = conv_data_[APP_DATA];
  auto& CORPORATION_DATA_ = conv_data_[CORPORATION_DATA];
  auto& DEFINE_PRODUCT_DATA_ = conv_data_[DEFINE_PRODUCT_DATA];
  auto& PACKAGE_DATA_ = conv_data_[PACKAGE_DATA];
  auto convert_filter_switch_rule_status =
    SPDM_convert_filter_switch_rule_status(session_data->get_spdm_ctx());

  const auto* unit_df = session_data->get_unit_df();
  const auto* account_df = session_data->get_account_df();
  auto fill = [&](const auto& item, bool new_action) {
    kuaishou::ad::AdActionType ocpx_type;
    if (new_action && (!kuaishou::ad::AdActionType_Parse(item.event_type(),
                                                         &ocpx_type))) {  // 只有新版数据考虑 action type
      LOG(ERROR) << "ConvertedAdFilter parse redis EventType error:" << item.event_type();
      return;
    }
    int64_t unit_id = item.unit_id();
    const ad_table::Unit* unit = nullptr;
    if (unit_df) {
      const auto& row_wrapper = unit_df->Find(unit_id);
      if (row_wrapper) {
        unit = row_wrapper.GetStruct<ad_table::Unit>();
      }
    }
    // 先使用 item 里面的 account_id 和 campaign_id, 如果没有则索引里查
    int64_t account_id = item.account_id() > 0 ? item.account_id() : unit ? unit->account_id() : 0;
    const ad_table::Account* account = nullptr;
    if (account_df) {
      const auto& row_wrapper = account_df->Find(account_id);
      if (row_wrapper) {
        account = row_wrapper.GetStruct<ad_table::Account>();
      }
    }
    int32_t range_type = ConvertedTimeRange(cur_ms_ - item.timestamp());
    // retarget skip filter
    if (range_type < 0 || session_data->get_retarget_account_set().find(account_id)
         != session_data->get_retarget_account_set().end()) {
      return;
    }
    if (!item.product_name().empty()) {
      APP_DATA_[TargetCityHashId(item.product_name())][new_action].Merge(
          {ocpx_type, range_type});
    }
    if (!item.package_name().empty()) {
      APP_DATA_[TargetCityHashId(item.package_name())][new_action].Merge(
          {ocpx_type, range_type});
    }
    if (!item.define_product().empty()) {
      DEFINE_PRODUCT_DATA_[TargetCityHashId(item.define_product())][new_action]
          .Merge({ocpx_type, range_type});
    }
    if (!item.corporation_name().empty()) {
      CORPORATION_DATA_[TargetCityHashId(item.corporation_name())][new_action]
          .Merge({ocpx_type, range_type});
    }
    if (unit != nullptr) {
      UNIT_DATA_[unit->id()][new_action].Merge({ocpx_type, range_type});
    }
    // campaign level
    int64_t campaign_id = item.campaign_id() > 0 ? item.campaign_id() : unit ? unit->campaign_id() : 0;
    if (campaign_id != 0) {
      CAMPAIGN_DATA_[campaign_id][new_action].Merge({ocpx_type, range_type});
    }
    // account level
    if (account_id != 0) {
      ACCOUNT_DATA_[account_id][new_action].Merge({ocpx_type, range_type});
    }
    if (account) {
      // corporation level
      if (account->city_corporation_id() != 0) {
        CORPORATION_DATA_[account->city_corporation_id()][new_action].Merge({ocpx_type, range_type});
      }
      // app level
      if (account->city_product_id()) {
        APP_DATA_[account->city_product_id()][new_action].Merge({ocpx_type, range_type});
      }
      if (unit && unit->package_id() > 0) {
        const auto* p_app = AdTableWrapper::GetAdAppRelease(unit->package_id());
        if (p_app) {
          PACKAGE_DATA_[p_app->package_name_id()][new_action].Merge({ocpx_type, range_type});
        }
      }
      // 用户定义产品名
      if (account->define_product_hash() != 0) {
        DEFINE_PRODUCT_DATA_[account->define_product_hash()][new_action].Merge({ocpx_type, range_type});
      }
    }
  };
  for (const auto& item : ad_cov_event.event_infos()) {
    fill(item, false);
  }
  for (const auto& item : ad_cov_event.action_infos()) {
    fill(item, true);
  }
  absl::flat_hash_map<int64_t, int64_t> wechat_id_to_last_convert_time;
  for (const auto& item : ad_cov_event.corp_action_infos()) {
    int64_t wechat_id = TargetCityHashId(item.corp_id());
    auto iter = wechat_id_to_last_convert_time.find(wechat_id);
    if (iter == wechat_id_to_last_convert_time.end()) {
      wechat_id_to_last_convert_time[wechat_id] = item.timestamp();
    } else {
      iter->second = std::max<int64_t>(item.timestamp(), iter->second);
    }
  }
  session_data->get_dot()->Interval(base::GetTimestamp() - start_us, "converted_data_prepare_cost_us");

  auto filter_func = [&](RetrievalAdCommon& ad) {
    auto* unit = ad.base.p_unit;
    auto* account = ad.base.p_account;
    if (!unit || !account) {
      return true;
    }

    auto* target = ad.base.p_target;
    if (!target) {
      return true;
    }

    int32_t level = target->filter_converted_level();
    int32_t filter_time_range = target->filter_time_range();

    auto* industry = ad.base.p_industry_v3;
    if (!industry || level == 0) {
      return false;
    }

    auto ocpx_action_type = unit->ocpx_action_type();
    if (industry && industry->parent_id() == 1018 && ad.base.p_campaign &&
        CampaignIsApp(ad.base.p_campaign)) {
      ocpx_action_type = 180;
    }

    auto judge = [&](int64_t id, int64_t data_level, int ocpx_action_type) {
      auto iter = conv_data_.find(data_level);
      if (iter == conv_data_.end()) {
        return false;
      }
      auto it = iter->second.find(id);
      if (it == iter->second.end()) {
        return false;
      }
      if (it->second[0].status && it->second[0].Hit(false, ocpx_action_type, filter_time_range)) {
        return true;
      }
      if (it->second[1].status && it->second[1].Hit(true, ocpx_action_type, filter_time_range)) {
        return true;
      }
      return false;
    };

    switch (level) {
      case kuaishou::ad::AdEnum::UNIT_LEVEL:
        return judge(ad.unit_id(), UNIT_DATA, ocpx_action_type);
      case kuaishou::ad::AdEnum::CAMPAIGN_LEVEL:
        return judge(ad.campaign_id(), CAMPAIGN_DATA, ocpx_action_type);
      case kuaishou::ad::AdEnum::ACCOUNT_LEVEL:
        return judge(ad.account_id(), ACCOUNT_DATA, ocpx_action_type);
      case kuaishou::ad::AdEnum::CORPORATION_LEVEL:
        return judge(ad.base.p_account->city_corporation_id(), CORPORATION_DATA, ocpx_action_type);
      case kuaishou::ad::AdEnum::APP_LEVEL: {
        auto ret1 = judge(ad.base.p_account->city_product_id(), APP_DATA, ocpx_action_type);
        auto ret2 = judge(ad.base.p_unit->city_app_package_name_id(), PACKAGE_DATA, ocpx_action_type);
        const auto* p_app = AdTableWrapper::GetAdAppRelease(unit->package_id());
        if (ret2 == false && p_app) {
          ret2 = judge(p_app->package_name_id(), PACKAGE_DATA, ocpx_action_type);
        }
        if (ret1 == true || ret2 == true) {
          return true;
        }
      }
      case kuaishou::ad::AdEnum::DEFINE_PRODUCT_LEVEL:
        return judge(ad.base.p_account->define_product_hash(), DEFINE_PRODUCT_DATA, ocpx_action_type);
        break;
      case kuaishou::ad::AdEnum::WECHAT_ID_LEVEL: {
        if (!ad.base.p_target) {
          return false;
        }
        const auto& wechat_ids = ad.base.p_target->wechat_ids();
        for (const auto& id : wechat_ids) {
          auto iter = wechat_id_to_last_convert_time.find(id);
          if (iter == wechat_id_to_last_convert_time.end()) {
            continue;
          } else {
            auto convert_time_range = ConvertedTimeRange(cur_ms_ - iter->second);
            if (convert_time_range == -1 || filter_time_range < convert_time_range) {
              continue;
            }
            return true;
          }
        }
        return false;
      }
      default:
        return false;
    }
    return false;
  };

  int64_t begin_time = base::GetTimestamp() / 1000;

  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto ret = filter_func(ad);
    if (ret == true) {
      cnt++;
      return FILTERED;
    }

    if (convert_filter_switch_rule_status == 1) {
      if (ret == true) {
        wrong_filt_num_++;
      }
    }
    return PASS;
  };

  DO_FILTER_PROCESS();
  session_data->get_dot()->Interval(base::GetTimestamp() / 1000 - begin_time, "converted_data_filter_cost");
}

RegistorRuleFilter(ConvertedDataFilter,
            kuaishou::log::ad::AdTraceFilterCondition::CONVERTED_USER_FILTER);

void ClientDupLivePhotoIdFilter(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (session_data == nullptr ||
      SPDM_disable_client_dup_live_photo_id_filter(session_data->get_spdm_ctx())) {
    return;
  }
  const auto& request = *(session_data->get_ad_request());
  std::unordered_set<std::string> live_stream_id_set;
  std::unordered_set<std::string> photo_id_set;
  if (request.has_ad_user_info()) {
    if (request.ad_user_info().encrypt_live_stream_ids_size() > 0) {
      for (const auto& live_stream_id : request.ad_user_info().encrypt_live_stream_ids()) {
        live_stream_id_set.insert(live_stream_id);
      }
    }
    if (request.ad_user_info().encrypt_photo_ids_size() > 0) {
      for (const auto& photo_id : request.ad_user_info().encrypt_photo_ids()) {
        photo_id_set.insert(photo_id);
      }
    }
  }

  int client_live_stream_filter_num = 0;
  int client_photo_filter_num = 0;
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    if (ad.base.p_live_stream_user_info) {
      auto& live_stream_id_encrypted = ad.base.p_live_stream_user_info->live_stream_id_encrypted();
      if (live_stream_id_set.find(live_stream_id_encrypted) != live_stream_id_set.end()) {
        client_live_stream_filter_num += 1;
        return kuaishou::log::ad::AdTraceFilterCondition::CLIENT_DUP_LIVE_STREAM_ID_FILTER;
      }
    }
    if (ad.base.p_photo_status) {
      auto& encrypted_photo_id = ad.base.p_photo_status->encrypted_photo_id();
      if (photo_id_set.find(encrypted_photo_id) != photo_id_set.end()) {
        client_photo_filter_num += 1;
        return kuaishou::log::ad::AdTraceFilterCondition::CLIENT_DUP_PHOTO_ID_FILTER;
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}
RegistorRuleFilter(ClientDupLivePhotoIdFilter,
            kuaishou::log::ad::AdTraceFilterCondition::CLIENT_DUP_LIVE_STREAM_ID_FILTER);

void SpuPurchasedFilterFull(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (SPDM_disable_other_purchase_freq_rule(session_data->get_spdm_ctx())) {
    return;
  }
  bool purchase_freq_rule_change_spu_source =
  SPDM_purchase_freq_rule_change_spu_source(session_data->get_spdm_ctx());
  std::unordered_set<int64_t> purchased_spu_photo;
  const auto& request = *(session_data->get_ad_request());
  int32 miss_cv_cnt = 0;
  int32 total_cv_cnt = 0;
  if (request.has_ad_user_info() && request.ad_user_info().ad_convert_info_size() > 0) {
    int64 freq_check_range = SPDM_spuPurchasedFreqControlInterval();
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    for (const auto& ad_convert_info : request.ad_user_info().ad_convert_info()) {
      auto timestamp = ad_convert_info.timestamp();
      if (timestamp > check_start_ts_ms) {
        for (const auto& ad_convert_info_item : ad_convert_info.ad_convert_info_item()) {
          auto& ad_spu_ids = ad_convert_info_item.ad_spu_ids();
          auto product_id = ad_convert_info_item.product_id();
          const auto* p_wt_product = AdTableWrapper::GetWTProduct(product_id);
          int64_t spu_cluster_1 = ad_convert_info_item.x7_entity_id();
          purchased_spu_photo.insert(spu_cluster_1);
          if (product_id > 0) {
            total_cv_cnt++;
            if (spu_cluster_1 == 0) {
              miss_cv_cnt++;
            }
          }
        }
      }
    }
  }
  if (purchase_freq_rule_change_spu_source) {
    purchased_spu_photo.clear();
    int64 freq_check_range = SPDM_spuPurchasedFreqControlInterval();
    const auto& feasury_data_map = request.ad_user_info().feasury_data_map();
    auto spu_iter = feasury_data_map.find("uAdOrderProductSpuIdV2List");
    auto time_iter = feasury_data_map.find("uMerchantAdOrderTimeV2List");
    if (spu_iter != feasury_data_map.end() && time_iter != feasury_data_map.end()) {
      const auto& spu_id_vec = spu_iter->second.int_list_value();
      const auto& timestamp_vec = time_iter->second.int_list_value();
      int64 now_ms = base::GetTimestamp() / 1000;
      int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
      int size = std::min(spu_id_vec.size(), timestamp_vec.size());
      int c_index = spu_id_vec.size() - 1;
      int t_index = timestamp_vec.size() - 1;
      for (int i = 0; i < size; i++) {
        auto spu_id = spu_id_vec[c_index - i];
        auto timestamp = timestamp_vec[t_index - i];
        if (spu_id > 0 && timestamp > check_start_ts_ms) {
          purchased_spu_photo.insert(spu_id);
        }
      }
    }
  }
  if (purchased_spu_photo.size() == 0) {
    return;
  }
  int32 filter_num = 0;
  int32 miss_cnt = 0;
  int32 invalid_product_cnt = 0;
  int32 total_cnt = 0;
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto unit = p_ad->base.p_unit;
    if (!unit || !unit->has_merchant_small_shop_support_info_optional()) {
      return PASS;
    }
    if (ad.bid_info.ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
        ad.bid_info.ocpx_action_type == kuaishou::ad::AD_MERCHANT_T7_ROI ||
        ad.bid_info.ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS ||
        ad.bid_info.ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
        ad.bid_info.ocpx_action_type == kuaishou::ad::AD_FANS_TOP_ROI ||
        ad.bid_info.ocpx_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        ad.bid_info.ocpx_action_type == kuaishou::ad::CID_ROAS) {
      int64_t product_id = unit->item_id();
      if (product_id == 0) {
        return PASS;
      }
      const auto* p_wt_product = ad.base.p_wt_product;
      const auto* p_wt_photo = ad.base.p_wt_photo;
      total_cnt++;
      int64_t spu_cluster_1 = p_wt_product ? p_wt_product->x7_entity_id() : 0;
      if (!p_wt_product) {
        invalid_product_cnt++;
      } else if (spu_cluster_1 == 0) {
        miss_cnt++;
      }
      if (spu_cluster_1 == 0 || purchased_spu_photo.find(spu_cluster_1) == purchased_spu_photo.end()) {
        return PASS;
      }
      filter_num++;
      return FILTERED;
    }
    return PASS;
  };
  DO_FILTER_PROCESS();

  session_data->get_dot()->Interval(filter_num, "amd_spu_purchased_freq_ctrl", "filter_num");
  session_data->get_dot()
      ->Interval(invalid_product_cnt, "amd_spu_purchased_freq_ctrl", "invalid_product_cnt");
  session_data->get_dot()->Interval(miss_cnt, "amd_spu_purchased_freq_ctrl", "miss_cnt");
  session_data->get_dot()->Interval(total_cnt, "amd_spu_purchased_freq_ctrl", "total_cnt");
  session_data->get_dot()->Interval(total_cv_cnt, "amd_spu_purchased_freq_ctrl", "total_cv_cnt");
  session_data->get_dot()->Interval(miss_cv_cnt, "amd_spu_purchased_freq_ctrl", "miss_cv_cnt");
}
RegistorRuleFilter(SpuPurchasedFilterFull,
            kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_SPU_FREQ_CNTRL);

void SpuPurchasedFilterV3(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (!SPDM_enable_v3_freq_rule(session_data->get_spdm_ctx())) {
    return;
  }
  absl::flat_hash_set<int64_t> purchased_spu_photo;
  absl::flat_hash_set<int64_t> purchased_kg;
  absl::flat_hash_set<int64_t> purchased_spu_v3;
  absl::flat_hash_set<int64_t> purchased_ecom_spu;
  absl::flat_hash_set<int64_t> purchased_spu_fix;
  absl::flat_hash_set<int64_t> purchased_kg_fix;
  absl::flat_hash_set<int64_t> purchased_author;
  auto author_list = AdKconfUtil::authorFreqRuleList();
  bool kg_fix = SPDM_enable_kgfix_freq_rule(session_data->get_spdm_ctx());
  const auto& request = *(session_data->get_ad_request());
  if (request.has_ad_user_info() && request.ad_user_info().ad_convert_info_size() > 0) {
    int64 freq_check_range = SPDM_spuPurchasedFreqControlInterval();
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    for (const auto& ad_convert_info : request.ad_user_info().ad_convert_info()) {
      auto timestamp = ad_convert_info.timestamp();
      if (timestamp > check_start_ts_ms) {
        for (const auto& ad_convert_info_item : ad_convert_info.ad_convert_info_item()) {
          auto& ad_spu_ids = ad_convert_info_item.ad_spu_ids();
          auto& kg_list = ad_convert_info_item.kg_tag_ids();
          auto& kg_fix_list = ad_convert_info_item.order_product_kg_tag_ids();
          int64_t author_id = ad_convert_info_item.author_id();
          int64_t spu_id_v3 = ad_convert_info_item.spu_id_v3();
          int64_t ecom_spu_id = ad_convert_info_item.photo_ecom_spu_id();
          int64_t product_spu_id = ad_convert_info_item.product_spu_id();
          purchased_spu_photo.insert(ad_spu_ids.begin(), ad_spu_ids.end());
          purchased_kg.insert(kg_list.begin(), kg_list.end());
          purchased_kg_fix.insert(kg_fix_list.begin(), kg_fix_list.end());
          purchased_spu_v3.insert(spu_id_v3);
          purchased_spu_fix.insert(product_spu_id);
          purchased_ecom_spu.emplace(std::move(ecom_spu_id));
          if (author_list->find(author_id) != author_list->end()) {
            purchased_author.insert(author_id);
          }
        }
      }
    }
  }
  if (purchased_spu_photo.size() + purchased_kg.size() + purchased_author.size() + purchased_spu_v3.size() +
          purchased_spu_fix.size() + purchased_kg_fix.size() ==
      0) {
    return;
  }
  int32 filter_num = 0;
  int32 filter_num_v2 = 0;
  int32 filter_num_v3 = 0;
  int32 filter_num_author = 0;
  int32 miss_cnt = 0;
  int32 total_cnt = 0;
  auto kg_freq_list = AdKconfUtil::kgFreqBizBlackList();
  bool enable_switch_to_photo_ecom_spu_id =
      SPDM_enable_switch_to_photo_ecom_spu_id(session_data->get_spdm_ctx());
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    if (ad.is_inner_loop_ad() && ad.dsp_item_type() != AdEnum::ITEM_LIVE) {
      const auto* p_wt_product = ad.base.p_wt_product;
      const auto* p_wt_photo = ad.base.p_wt_photo;
      if (!p_wt_photo) {
        return PASS;
      }
      total_cnt++;
      if (ad.base.p_wt_author && p_wt_product &&
          !(kg_freq_list && (kg_freq_list->count(ad.base.p_wt_author->biz_center()) > 0))) {
        for (const int64_t& kg_id : p_wt_product->kg_tag_ids()) {
          if (kg_fix && purchased_kg_fix.count(kg_id) > 0) {
            filter_num_v2++;
            return FILTERED;
          }
          if (purchased_kg.count(kg_id) > 0) {
            filter_num_v2++;
            return FILTERED;
          }
        }
      }
      int64_t author_id = ad.author_id(session_data->get_is_esp_daitou());
      if (ad.base.p_unit->put_type() == kuaishou::ad::AdEnum::LSP_KOL_TYPE ||
          (ad.base.p_creative->creative_photo_ascription() == kuaishou::ad::AdEnum::PHOTO_ASCRIPTION_OTHER)) {
        author_id = ad.base.p_creative->photo_author_id();
      }
      if (purchased_author.size() > 0 && (author_list->find(author_id) != author_list->end())) {
        filter_num_author++;
        return FILTERED;
      }
      int64_t spu_cluster_1 = (p_wt_photo->ad_spu_ids().size() > 0) ? (p_wt_photo->ad_spu_ids())[0] : 0;
      if (spu_cluster_1 > 0 && purchased_spu_photo.size() > 0 &&
          purchased_spu_photo.count(spu_cluster_1) > 0) {
        filter_num++;
        return FILTERED;
      }
      if (enable_switch_to_photo_ecom_spu_id) {
        int64_t ecom_spu_id = p_wt_photo->ecom_spu_id();
        if (ecom_spu_id > 0 && purchased_ecom_spu.size() > 0 && purchased_ecom_spu.count(ecom_spu_id) > 0) {
          filter_num++;
          return FILTERED;
        }
      } else {
        int64_t spu_id_v3 = p_wt_photo->spu_id_v3();
        if (spu_id_v3 > 0 && purchased_spu_v3.size() > 0 && purchased_spu_v3.count(spu_id_v3) > 0) {
          filter_num_v3++;
          return FILTERED;
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
  session_data->get_dot()->Interval(filter_num, "SpuPurchasedFilterV3", "filter_num");
  session_data->get_dot()->Interval(filter_num_v2, "SpuPurchasedFilterV3", "filter_num_v2");
  session_data->get_dot()->Interval(filter_num_author, "SpuPurchasedFilterV3", "filter_num_author");
  session_data->get_dot()->Interval(filter_num_v3, "SpuPurchasedFilterV3", "filter_num_v3");
  session_data->get_dot()->Interval(miss_cnt, "SpuPurchasedFilterV3", "miss_cnt");
  session_data->get_dot()->Interval(total_cnt, "SpuPurchasedFilterV3", "total_cnt");
}

RegistorRuleFilter(SpuPurchasedFilterV3,
            kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_SPU_FREQ_CNTRL);

void CategoryFilter(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  bool enable_category_live_freq_rule = SPDM_enable_category_live_freq_rule(session_data->get_spdm_ctx());
  bool enable_category_photo_freq_rule = SPDM_enable_category_photo_freq_rule(session_data->get_spdm_ctx());
  if (!enable_category_live_freq_rule && !enable_category_photo_freq_rule) {
    return;
  }
  // 只生效公域单列
  if (session_data->get_sub_page_id() != 10011001 && session_data->get_sub_page_id() != 11001001) {
    return;
  }

  int64 live_freq_check_range = SPDM_category_purchased_live_interval(session_data->get_spdm_ctx());
  int64 photo_freq_check_range = SPDM_category_purchased_photo_interval(session_data->get_spdm_ctx());
  bool enable_customer_freq_check_live =
      SPDM_enable_customer_purchase_freq_check(session_data->get_spdm_ctx());
  bool enable_customer_freq_check_photo = true;
  const auto category_customize_freq_ctrl_map = AdKconfUtil::categoryCustomizeFreqCtrlMap();
  // 短播参数
  bool enable_live_purchased_freq_short_play =
      SPDM_enable_live_purchased_freq_short_play(session_data->get_spdm_ctx());
  bool enable_photo_purchased_freq_short_play =
      SPDM_enable_photo_purchased_freq_short_play(session_data->get_spdm_ctx());
  bool enable_transfer_category_a_to_b =
      SPDM_enable_transfer_category_a_to_b(session_data->get_spdm_ctx());
  int64_t check_seconds = SPDM_purchased_freq_short_play_check_seconds(session_data->get_spdm_ctx());
  int64_t check_duration = SPDM_purchased_freq_short_play_played_duration(session_data->get_spdm_ctx());
  int check_freq = SPDM_purchased_freq_short_play_check_cnt(session_data->get_spdm_ctx());

  absl::flat_hash_set<int64_t> purchased_category_for_live;
  absl::flat_hash_set<int64_t> purchased_category_for_photo;
  const auto& request = *(session_data->get_ad_request());
  if (request.has_ad_user_info() && request.ad_user_info().ecom_convert_info_size() > 0) {
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 live_check_start_ts_ms = now_ms - live_freq_check_range * 1000;
    int64 photo_check_start_ts_ms = now_ms - photo_freq_check_range * 1000;
    for (const auto& ecom_convert_info : request.ad_user_info().ecom_convert_info()) {
      auto timestamp = ecom_convert_info.timestamp();
      // live check
      for (const auto& ad_convert_info_item : ecom_convert_info.ad_convert_info_item()) {
        auto mmu_category_id = ad_convert_info_item.mmu_category_id();
        if (mmu_category_id > 0) {
          if (enable_customer_freq_check_live) {
            auto freq_ctrl_iter = category_customize_freq_ctrl_map->find(mmu_category_id);
            if (freq_ctrl_iter != category_customize_freq_ctrl_map->end() &&
                timestamp > (now_ms - freq_ctrl_iter->second * 1000)) {
              if (enable_live_purchased_freq_short_play) {
                if (!request.ad_session_response_pack().response().has_user_action_info()) {
                  continue;
                }
                std::unordered_map<int64_t, int> short_play_map;
                const auto& user_action_info =
                  request.ad_session_response_pack().response().user_action_info();
                for (int i = 0; i < user_action_info.ad_action_info_size(); i++) {
                  auto curr_seq = user_action_info.ad_action_info(i);
                  if (curr_seq.action_type() == kuaishou::ad::AdActionType::AD_DETAIL_PAGE_CLOSED) {
                    for (int j = 0; j < curr_seq.ad_detail_info_size(); j++) {
                      const auto &detail_info = curr_seq.ad_detail_info(j);
                      if (detail_info.timestamp() > now_ms - check_seconds &&
                          detail_info.played_duration() > 0 &&
                          detail_info.played_duration() < check_duration) {
                        if (detail_info.category_id_size() > 0) {
                          for (int k = 0; k < std::min(detail_info.category_id_size(), 3); k++) {
                            if (detail_info.category_id(k) > 0) {
                              short_play_map[detail_info.category_id(k)] += 1;
                            }
                          }
                        }
                      }
                    }
                  }
                }
                if (short_play_map.find(mmu_category_id) == short_play_map.end() ||
                    (short_play_map.find(mmu_category_id) != short_play_map.end() &&
                    short_play_map[mmu_category_id] <= check_freq)) {
                  continue;
                }
              }
              purchased_category_for_live.insert(mmu_category_id);
            }
          } else if (enable_category_live_freq_rule) {
            if (timestamp > live_check_start_ts_ms) {
              purchased_category_for_live.insert(mmu_category_id);
            }
          }
        }
      }
      // photo check
      for (const auto& ad_convert_info_item : ecom_convert_info.ad_convert_info_item()) {
        auto mmu_category_id = ad_convert_info_item.mmu_category_id();
        if (mmu_category_id > 0) {
          if (enable_customer_freq_check_photo) {
            auto freq_ctrl_iter = category_customize_freq_ctrl_map->find(mmu_category_id);
            if (freq_ctrl_iter != category_customize_freq_ctrl_map->end() &&
             timestamp > (now_ms - freq_ctrl_iter->second * 1000)) {
              if (enable_photo_purchased_freq_short_play) {
                if (!request.ad_session_response_pack().response().has_user_action_info()) {
                  continue;
                }
                std::unordered_map<int64_t, int> short_play_map;
                const auto& user_action_info =
                  request.ad_session_response_pack().response().user_action_info();
                for (int i = 0; i < user_action_info.ad_action_info_size(); i++) {
                  auto curr_seq = user_action_info.ad_action_info(i);
                  if (curr_seq.action_type() == kuaishou::ad::AdActionType::AD_DETAIL_PAGE_CLOSED) {
                    for (int j = 0; j < curr_seq.ad_detail_info_size(); j++) {
                      const auto &detail_info = curr_seq.ad_detail_info(j);
                      if (detail_info.timestamp() > now_ms - check_seconds &&
                          detail_info.played_duration() > 0 &&
                          detail_info.played_duration() < check_duration) {
                        if (detail_info.category_id_size() > 0 &&
                          detail_info.category_id(detail_info.category_id_size() - 1) > 0) {
                          short_play_map[detail_info.category_id(detail_info.category_id_size() - 1)] += 1;
                        }
                      }
                    }
                  }
                }
                if (short_play_map.find(mmu_category_id) == short_play_map.end() ||
                    (short_play_map.find(mmu_category_id) != short_play_map.end() &&
                    short_play_map[mmu_category_id] <= check_freq)) {
                  continue;
                }
              }
              purchased_category_for_photo.insert(mmu_category_id);
            }
          } else if (enable_category_photo_freq_rule) {
            if (timestamp > photo_check_start_ts_ms) {
              purchased_category_for_photo.insert(mmu_category_id);
            }
          }
        }
      }
    }
  }
  if (purchased_category_for_live.size() + purchased_category_for_photo.size() == 0) {
    return;
  }
  int32 filter_photo_num = 0;
  int32 filter_live_num = 0;
  int32 filter_p2l_num = 0;
  int32 live_total_cnt = 0;
  int32 photo_total_cnt = 0;
  int32 p2l_total_cnt = 0;

  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto unit = p_ad->base.p_unit;
    if (unit == nullptr || !ad.is_inner_loop_ad()) {
      return PASS;
    }
    // 短视频
    if (ad.dsp_item_type() == AdEnum::ITEM_PHOTO && enable_category_photo_freq_rule) {
      photo_total_cnt++;
      const auto* p_wt_photo = ad.base.p_wt_photo;
      if (p_wt_photo) {
        int64_t mmu_category_id = p_wt_photo->mmu_category_id_a();
        if (enable_transfer_category_a_to_b) {
          mmu_category_id = p_wt_photo->mmu_category_id_b();
        }
        if (mmu_category_id > 0 && purchased_category_for_photo.size() > 0 &&
            purchased_category_for_photo.count(mmu_category_id) > 0) {
          filter_photo_num++;
          return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL;
        }
      }
      const auto* p_wt_product = ad.base.p_wt_product;
      if (p_wt_product) {
        int64_t mmu_category_id = p_wt_product->mmu_a_category_id();
        if (enable_transfer_category_a_to_b) {
          mmu_category_id = p_wt_product->mmu_b_category_id();
        }
        if (mmu_category_id > 0 && purchased_category_for_photo.size() > 0 &&
            purchased_category_for_photo.count(mmu_category_id) > 0) {
          filter_photo_num++;
          return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL;
        }
      }
    }
    // 引流
    if (ad.dsp_item_type() == AdEnum::ITEM_PHOTO_TO_LIVE) {
      p2l_total_cnt++;
      if (enable_category_photo_freq_rule) {
        const auto* p_wt_photo = ad.base.p_wt_photo;
        if (p_wt_photo) {
          int64_t mmu_category_id = p_wt_photo->mmu_category_id_a();
          if (enable_transfer_category_a_to_b) {
            mmu_category_id = p_wt_photo->mmu_category_id_b();
          }
          if (mmu_category_id > 0 && purchased_category_for_photo.size() > 0 &&
              purchased_category_for_photo.count(mmu_category_id) > 0) {
            filter_p2l_num++;
            return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL;
          }
        }
        const auto* p_wt_product = ad.base.p_wt_product;
        if (p_wt_product) {
          int64_t mmu_category_id = p_wt_product->mmu_a_category_id();
          if (enable_transfer_category_a_to_b) {
            mmu_category_id = p_wt_product->mmu_b_category_id();
          }
          if (mmu_category_id > 0 && purchased_category_for_photo.size() > 0 &&
              purchased_category_for_photo.count(mmu_category_id) > 0) {
            filter_photo_num++;
            return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL;
          }
        }
      }
      if (enable_category_live_freq_rule) {
        const auto* p_wt_live = ad.base.p_wt_live;
        if (p_wt_live) {
          int32_t cnt = 3;
          const auto& mmu_category_id_list = (enable_transfer_category_a_to_b)
            ? p_wt_live->mmu_category_id_b_list()
            : p_wt_live->mmu_category_id_a_list();
          for (auto it = mmu_category_id_list.cbegin(); cnt > 0 && it != mmu_category_id_list.cend();
               it++) {
            cnt--;
            auto category_id = *it;
            if (category_id > 0 && purchased_category_for_live.size() > 0 &&
                purchased_category_for_live.count(category_id) > 0) {
              filter_p2l_num++;
              return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_LIVE_FREQ_CNTRL;
            }
          }
        }
      }
    }
    // 直播直投
    if (ad.dsp_item_type() == AdEnum::ITEM_LIVE && enable_category_live_freq_rule) {
      live_total_cnt++;
      const auto* p_wt_live = ad.base.p_wt_live;
      if (p_wt_live) {
        int32_t cnt = 3;
        const auto& mmu_category_id_list = (enable_transfer_category_a_to_b)
            ? p_wt_live->mmu_category_id_b_list()
            : p_wt_live->mmu_category_id_a_list();
        for (auto it = mmu_category_id_list.cbegin(); cnt > 0 && it != mmu_category_id_list.cend();
             it++) {
          cnt--;
          auto category_id = *it;
          if (category_id > 0 && purchased_category_for_live.size() > 0 &&
              purchased_category_for_live.count(category_id) > 0) {
            filter_live_num++;
            return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_LIVE_FREQ_CNTRL;
          }
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
  session_data->get_dot()->Interval(filter_photo_num, "CategoryFilter", "filter_photo_num");
  session_data->get_dot()->Interval(filter_live_num, "CategoryFilter", "filter_live_num");
  session_data->get_dot()->Interval(filter_p2l_num, "CategoryFilter", "filter_p2l_num");
  session_data->get_dot()->Interval(live_total_cnt, "CategoryFilter", "live_total_cnt");
  session_data->get_dot()->Interval(photo_total_cnt, "CategoryFilter", "photo_total_cnt");
  session_data->get_dot()->Interval(p2l_total_cnt, "CategoryFilter", "p2l_total_cnt");
}
RegistorRuleFilter(CategoryFilter,
            kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL);

void CategoryFilterV2(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  bool enable_category_freq_rule = SPDM_enable_category_freq_rule_v2(session_data->get_spdm_ctx());
  bool enable_incentive_separate_category_purchased_interval =
    SPDM_enable_incentive_separate_category_purchased_interval(session_data->get_spdm_ctx());
  // 激励流量单独控制，如有问题联系 zhangxingyu03
  if (!enable_category_freq_rule && !(enable_incentive_separate_category_purchased_interval &&
      (session_data->get_is_rewarded() || session_data->get_pos_manager_base().IsInspireLive() ||
      session_data->get_is_inspire_mix()))) {
    return;
  }

  int64 freq_check_range = SPDM_category_purchased_interval_v2(session_data->get_spdm_ctx());
  if (enable_incentive_separate_category_purchased_interval && ((session_data->get_is_rewarded() ||
      session_data->get_pos_manager_base().IsInspireLive() || session_data->get_is_inspire_mix()))) {
    freq_check_range = SPDM_incentive_category_purchased_interval(session_data->get_spdm_ctx());
  }

  absl::flat_hash_set<int64_t> purchased_category;

  const auto& request = *(session_data->get_ad_request());
  const auto& feasury_data_map = request.ad_user_info().feasury_data_map();
  auto cate_iter = feasury_data_map.find("uMerchantAdOrderMmuBLeafCateIdV2List");
  auto time_iter = feasury_data_map.find("uMerchantAdOrderTimeV2List");
  if (cate_iter != feasury_data_map.end() && time_iter != feasury_data_map.end()) {
    const auto& cate_id_vec = cate_iter->second.int_list_value();
    const auto& timestamp_vec = time_iter->second.int_list_value();
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    int size = std::min(cate_id_vec.size(), timestamp_vec.size());
    for (int i = 0; i < size; i++) {
      auto cate_id = cate_id_vec[i];
      auto timestamp = timestamp_vec[i];
      if (cate_id > 0 && timestamp > check_start_ts_ms) {
        purchased_category.insert(cate_id);
        session_data->get_dot()->Count(1, "CategoryFilterV2Num");
      }
    }
  }
  if (purchased_category.size() == 0) {
    return;
  }
  int32 filter_photo_num = 0;
  int32 filter_live_num = 0;
  int32 filter_p2l_num = 0;
  int32 live_total_cnt = 0;
  int32 photo_total_cnt = 0;
  int32 p2l_total_cnt = 0;

  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto unit = p_ad->base.p_unit;
    if (unit == nullptr || !ad.is_inner_loop_ad()) {
      return PASS;
    }
    // 短视频
    if (ad.dsp_item_type() == AdEnum::ITEM_PHOTO) {
      photo_total_cnt++;
      const auto* p_wt_photo = ad.base.p_wt_photo;
      if (p_wt_photo) {
        int64_t mmu_category_id = p_wt_photo->mmu_category_id_b();
        if (mmu_category_id > 0 && purchased_category.size() > 0 &&
            purchased_category.count(mmu_category_id) > 0) {
          filter_photo_num++;
          return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL;
        }
      }
      const auto* p_wt_product = ad.base.p_wt_product;
      if (p_wt_product) {
        int64_t mmu_category_id = p_wt_product->mmu_b_category_id();
        if (mmu_category_id > 0 && purchased_category.size() > 0 &&
            purchased_category.count(mmu_category_id) > 0) {
          filter_photo_num++;
          return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL;
        }
      }
    }
    // 引流
    if (ad.dsp_item_type() == AdEnum::ITEM_PHOTO_TO_LIVE) {
      p2l_total_cnt++;
      const auto* p_wt_photo = ad.base.p_wt_photo;
      if (p_wt_photo) {
        int64_t mmu_category_id = p_wt_photo->mmu_category_id_b();
        if (mmu_category_id > 0 && purchased_category.size() > 0 &&
            purchased_category.count(mmu_category_id) > 0) {
          filter_p2l_num++;
          return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL;
        }
      }
      const auto* p_wt_product = ad.base.p_wt_product;
      if (p_wt_product) {
        int64_t mmu_category_id = p_wt_product->mmu_b_category_id();
        if (mmu_category_id > 0 && purchased_category.size() > 0 &&
            purchased_category.count(mmu_category_id) > 0) {
          filter_photo_num++;
          return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL;
        }
      }
      const auto* p_wt_live = ad.base.p_wt_live;
      if (p_wt_live) {
        int32_t cnt = 3;
        const auto& mmu_category_id_list = p_wt_live->mmu_category_id_b_list();
        for (auto it = mmu_category_id_list.cbegin(); cnt > 0 && it != mmu_category_id_list.cend();
              it++) {
          cnt--;
          auto category_id = *it;
          if (category_id > 0 && purchased_category.size() > 0 &&
              purchased_category.count(category_id) > 0) {
            filter_p2l_num++;
            return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_LIVE_FREQ_CNTRL;
          }
        }
      }
    }
    // 直播直投
    if (ad.dsp_item_type() == AdEnum::ITEM_LIVE) {
      live_total_cnt++;
      const auto* p_wt_live = ad.base.p_wt_live;
      if (p_wt_live) {
        int32_t cnt = 3;
        const auto& mmu_category_id_list = p_wt_live->mmu_category_id_b_list();
        for (auto it = mmu_category_id_list.cbegin(); cnt > 0 && it != mmu_category_id_list.cend();
             it++) {
          cnt--;
          auto category_id = *it;
          if (category_id > 0 && purchased_category.size() > 0 &&
              purchased_category.count(category_id) > 0) {
            filter_live_num++;
            return kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_LIVE_FREQ_CNTRL;
          }
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
  session_data->get_dot()->Interval(filter_photo_num, "CategoryFilterV2", "filter_photo_num");
  session_data->get_dot()->Interval(filter_live_num, "CategoryFilterV2", "filter_live_num");
  session_data->get_dot()->Interval(filter_p2l_num, "CategoryFilterV2", "filter_p2l_num");
  session_data->get_dot()->Interval(live_total_cnt, "CategoryFilterV2", "live_total_cnt");
  session_data->get_dot()->Interval(photo_total_cnt, "CategoryFilterV2", "photo_total_cnt");
  session_data->get_dot()->Interval(p2l_total_cnt, "CategoryFilterV2", "p2l_total_cnt");
}
RegistorRuleFilter(CategoryFilterV2,
            kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL);

void ShelfMerchantCategoryFilter(ks::platform::AddibleRecoContextInterface* context,
                           RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  bool enable_shelf_merchant_freq_rule =
    SPDM_enable_shelf_merchant_category_freq_rule(session_data->get_spdm_ctx());
  if (!enable_shelf_merchant_freq_rule) {
    return;
  }
  // 生效货架电商流量
  if (!session_data->get_pos_manager_base().IsGuessYouLike() &&
      !session_data->get_pos_manager_base().IsMallTraffic() &&
      !session_data->get_pos_manager_base().IsBuyerHomePageTraffic() &&
      !session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    return;
  }

  int64 freq_check_range =
    SPDM_shelf_merchant_category_freq_interval(session_data->get_spdm_ctx());

  absl::flat_hash_set<int64_t> purchased_category;
  const auto& request = *(session_data->get_ad_request());
  if (request.has_ad_user_info() && request.ad_user_info().ecom_convert_info_size() > 0) {
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    for (const auto& ecom_convert_info : request.ad_user_info().ecom_convert_info()) {
      auto timestamp = ecom_convert_info.timestamp();
      // category check
      for (const auto& ad_convert_info_item : ecom_convert_info.ad_convert_info_item()) {
        auto mmu_category_id = ad_convert_info_item.mmu_category_id();
        if (mmu_category_id > 0 && timestamp > check_start_ts_ms) {
          purchased_category.insert(mmu_category_id);
        }
      }
    }
  }
  if (purchased_category.size() == 0) {
    return;
  }
  int32 filter_photo_num = 0;
  int32 photo_total_cnt = 0;
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto unit = p_ad->base.p_unit;
    if (unit == nullptr || !ad.is_inner_loop_ad()) {
      return PASS;
    }
    // 短视频
    if (ad.dsp_item_type() == AdEnum::ITEM_PHOTO) {
      photo_total_cnt++;
      const auto* p_wt_photo = ad.base.p_wt_photo;
      if (p_wt_photo) {
        int64_t mmu_category_id_a = p_wt_photo->mmu_category_id_a();
        if (mmu_category_id_a > 0 && purchased_category.count(mmu_category_id_a) > 0) {
          filter_photo_num++;
          return FILTERED;
        }
      }
      const auto* p_wt_product = ad.base.p_wt_product;
      if (p_wt_product) {
        int64_t mmu_a_category_id = p_wt_product->mmu_a_category_id();
        if (mmu_a_category_id > 0 && purchased_category.count(mmu_a_category_id) > 0) {
          filter_photo_num++;
          return FILTERED;
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}
RegistorRuleFilter(ShelfMerchantCategoryFilter,
            kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_CATEGORY_PHOTO_FREQ_CNTRL);

void ShelfOrderSpuidFilter(ks::platform::AddibleRecoContextInterface* context,
                                      RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  bool enable_shelf_order_spuid_freq_rule = false;
  if (!enable_shelf_order_spuid_freq_rule) {
    return;
  }
  // 生效货架电商流量
  if (!session_data->get_pos_manager_base().IsGuessYouLike() &&
      !session_data->get_pos_manager_base().IsMallTraffic() &&
      !session_data->get_pos_manager_base().IsBuyerHomePageTraffic() &&
      !session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    return;
  }

  int64 freq_check_range = 604800;

  absl::flat_hash_set<int64_t> spu_id_set;
  const auto& request = *(session_data->get_ad_request());
  if (request.has_ad_user_info() && request.ad_user_info().ecom_convert_info_size() > 0) {
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    for (const auto& ecom_convert_info : request.ad_user_info().ecom_convert_info()) {
      auto timestamp = ecom_convert_info.timestamp();
      for (const auto& ad_convert_info_item : ecom_convert_info.ad_convert_info_item()) {
        auto product_spu_id = ad_convert_info_item.product_spu_id();
        if (product_spu_id > 0 && timestamp > check_start_ts_ms) {
          spu_id_set.insert(product_spu_id);
        }
      }
    }
  }
  if (spu_id_set.size() == 0) {
    return;
  }
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto unit = p_ad->base.p_unit;
    if (unit == nullptr || !ad.is_inner_loop_ad()) {
      return PASS;
    }
    // 短视频
    if (ad.dsp_item_type() == AdEnum::ITEM_PHOTO) {
      const auto* p_wt_product = ad.base.p_wt_product;
      if (p_wt_product) {
        int64_t ecom_spu_id = p_wt_product->ecom_spu_id();
        if (ecom_spu_id > 0 && spu_id_set.size() > 0 && spu_id_set.count(ecom_spu_id) > 0) {
          return kuaishou::log::ad::AdTraceFilterCondition::SHELF_ORDER_SPUID_FILTER;
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}
RegistorRuleFilter(ShelfOrderSpuidFilter,
            kuaishou::log::ad::AdTraceFilterCondition::SHELF_ORDER_SPUID_FILTER);

void ShelfOrderProductidFilter(ks::platform::AddibleRecoContextInterface* context,
                                      RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  bool enable_shelf_order_productid_freq_rule = false;
  if (!enable_shelf_order_productid_freq_rule) {
    return;
  }
  // 生效货架电商流量
  if (!session_data->get_pos_manager_base().IsGuessYouLike() &&
      !session_data->get_pos_manager_base().IsMallTraffic() &&
      !session_data->get_pos_manager_base().IsBuyerHomePageTraffic() &&
      !session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    return;
  }

  int64 freq_check_range = 604800;

  absl::flat_hash_set<int64_t> product_id_set;
  const auto& request = *(session_data->get_ad_request());
  if (request.has_ad_user_info() && request.ad_user_info().ecom_convert_info_size() > 0) {
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    for (const auto& ecom_convert_info : request.ad_user_info().ecom_convert_info()) {
      auto timestamp = ecom_convert_info.timestamp();
      for (const auto& ad_convert_info_item : ecom_convert_info.ad_convert_info_item()) {
        auto product_id = ad_convert_info_item.product_id();
        if (product_id > 0 && timestamp > check_start_ts_ms) {
          product_id_set.insert(product_id);
        }
      }
    }
  }
  if (product_id_set.size() == 0) {
    return;
  }
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto unit = p_ad->base.p_unit;
    if (unit == nullptr || !ad.is_inner_loop_ad()) {
      return PASS;
    }
    // 短视频
    if (ad.dsp_item_type() == AdEnum::ITEM_PHOTO) {
      const auto* p_wt_product = ad.base.p_wt_product;
      if (p_wt_product) {
        int64_t product_id = p_wt_product->product_id();
        if (product_id > 0 && product_id_set.size() > 0 && product_id_set.count(product_id) > 0) {
          return FILTERED;
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}
RegistorRuleFilter(ShelfOrderProductidFilter,
            kuaishou::log::ad::AdTraceFilterCondition::SHELF_ORDER_PRODUCTID_FILTER);

void ShelfAllOrderCategoryFilter(ks::platform::AddibleRecoContextInterface* context,
                                      RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  bool enable_shelf_all_order_cate_freq_rule = false;
  if (!enable_shelf_all_order_cate_freq_rule) {
    return;
  }
  // 生效货架电商流量
  if (!session_data->get_pos_manager_base().IsGuessYouLike() &&
      !session_data->get_pos_manager_base().IsMallTraffic() &&
      !session_data->get_pos_manager_base().IsBuyerHomePageTraffic() &&
      !session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    return;
  }
  // 生效具体页面
  const auto enbale_page_id_conf = AdKconfUtil::shelfOrderCatePageid();
  if (enbale_page_id_conf->count(session_data->get_sub_page_id()) == 0 &&
      enbale_page_id_conf->count(session_data->get_page_id()) == 0) {
    return;
  }

  int64 freq_check_range = 604800;

  absl::flat_hash_set<int64_t> purchased_category;
  const auto& request = *(session_data->get_ad_request());
  if (request.has_ad_user_info() && request.ad_user_info().ecom_convert_info_size() > 0) {
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    for (const auto& ecom_convert_info : request.ad_user_info().ecom_convert_info()) {
      auto timestamp = ecom_convert_info.timestamp();
      // category check
      for (const auto& ad_convert_info_item : ecom_convert_info.ad_convert_info_item()) {
        auto mmu_category_id = ad_convert_info_item.mmu_category_id();
        if (mmu_category_id > 0 && timestamp > check_start_ts_ms) {
          purchased_category.insert(mmu_category_id);
        }
      }
    }
  }
  if (purchased_category.size() == 0) {
    return;
  }

  int32 filter_photo_num = 0;
  int32 photo_total_cnt = 0;
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto unit = p_ad->base.p_unit;
    if (unit == nullptr || !ad.is_inner_loop_ad()) {
      return PASS;
    }
    // 短视频
    if (ad.dsp_item_type() == AdEnum::ITEM_PHOTO) {
      photo_total_cnt++;
      const auto* p_wt_photo = ad.base.p_wt_photo;
      if (p_wt_photo) {
        int64_t mmu_category_id_a = p_wt_photo->mmu_category_id_a();
        if (mmu_category_id_a > 0 && purchased_category.count(mmu_category_id_a) > 0) {
          filter_photo_num++;
          return FILTERED;
        }
      }
      const auto* p_wt_product = ad.base.p_wt_product;
      if (p_wt_product) {
        int64_t mmu_a_category_id = p_wt_product->mmu_a_category_id();
        if (mmu_a_category_id > 0 && purchased_category.count(mmu_a_category_id) > 0) {
          filter_photo_num++;
          return FILTERED;
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}

RegistorRuleFilter(ShelfAllOrderCategoryFilter,
            kuaishou::log::ad::AdTraceFilterCondition::SHELF_ORDER_CATEGORY_FILTER);

void ShelfAdOrderCategoryFilter(ks::platform::AddibleRecoContextInterface* context,
                                      RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  bool enable_shelf_ad_order_cate_freq_rule =
    SPDM_enable_shelf_ad_order_cate_freq_rule(session_data->get_spdm_ctx());
  if (!enable_shelf_ad_order_cate_freq_rule) {
    return;
  }
  auto enable_author_set = AdKconfUtil::ShelfOrderForUseridSet();
  if (enable_author_set == nullptr) {
    return;
  }
  // 生效货架电商流量
  if (!session_data->get_pos_manager_base().IsGuessYouLike() &&
      !session_data->get_pos_manager_base().IsMallTraffic() &&
      !session_data->get_pos_manager_base().IsBuyerHomePageTraffic() &&
      !session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    return;
  }
  // 生效具体页面
  const auto enbale_page_id_conf = AdKconfUtil::shelfOrderCatePageid();
  if (enbale_page_id_conf->count(session_data->get_sub_page_id()) == 0 &&
      enbale_page_id_conf->count(session_data->get_page_id()) == 0) {
    return;
  }
  int64 freq_check_range =
    SPDM_shelf_ad_order_cate_freq_interval(session_data->get_spdm_ctx());
  absl::flat_hash_set<int64_t> purchased_category;
  const auto& request = *(session_data->get_ad_request());
  const auto& feasury_data_map = request.ad_user_info().feasury_data_map();
  auto cate_iter = feasury_data_map.find("uMerchantAdOrderMmuALeafCateIdList");
  auto time_iter = feasury_data_map.find("uMerchantAdOrderTimeList");
  cate_iter = feasury_data_map.find("uMerchantAdOrderMmuBLeafCateIdV2List");
  time_iter = feasury_data_map.find("uMerchantAdOrderTimeV2List");
  if (cate_iter != feasury_data_map.end() && time_iter != feasury_data_map.end()) {
    const auto& cate_id_vec = cate_iter->second.int_list_value();
    const auto& timestamp_vec = time_iter->second.int_list_value();
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    int size = std::min(cate_id_vec.size(), timestamp_vec.size());
    for (int i = 0; i < size; i++) {
      auto cate_id = cate_id_vec[i];
      auto timestamp = timestamp_vec[i];
      if (cate_id > 0 && timestamp > check_start_ts_ms) {
        purchased_category.insert(cate_id);
        session_data->get_dot()->Count(1, "ShelfAdOrderCategoryFilterNum");
      }
    }
  }
  if (purchased_category.size() == 0) {
    return;
  }
  int32 filter_photo_num = 0;
  int32 photo_total_cnt = 0;
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    if (ad.base.live_creative_type() == 0) {
      photo_total_cnt++;
      const auto* p_wt_photo = ad.base.p_wt_photo;
      if (p_wt_photo) {
        int64_t mmu_category_id_b = p_wt_photo->mmu_category_id_b();
        if (mmu_category_id_b > 0 && purchased_category.count(mmu_category_id_b) > 0) {
          filter_photo_num++;
          return FILTERED;
        }
      }
      const auto* p_wt_product = ad.base.p_wt_product;
      if (p_wt_product) {
        int64_t mmu_b_category_id = p_wt_product->mmu_b_category_id();
        if (mmu_b_category_id > 0 && purchased_category.count(mmu_b_category_id) > 0) {
          filter_photo_num++;
          return FILTERED;
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}

RegistorRuleFilter(ShelfAdOrderCategoryFilter,
            kuaishou::log::ad::AdTraceFilterCondition::SHELF_ORDER_CATEGORY_FILTER);

void ShelfDislikeProductidFilter(ks::platform::AddibleRecoContextInterface* context,
                                      RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  // 生效货架电商流量
  if (!session_data->get_pos_manager_base().IsGuessYouLike() &&
      !session_data->get_pos_manager_base().IsMallTraffic() &&
      !session_data->get_pos_manager_base().IsBuyerHomePageTraffic() &&
      !session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    return;
  }

  int64 freq_check_range =
    SPDM_shelf_dislike_freq_interval(session_data->get_spdm_ctx());
  absl::flat_hash_set<int64_t> product_id_set;
  const auto& request = *(session_data->get_ad_request());
  const auto& feasury_data_map = request.ad_user_info().feasury_data_map();
  auto item_iter =
    feasury_data_map.find("uStandardMerchantNegtiveFeedbackGoodsAllIdList");
  auto time_iter =
    feasury_data_map.find("uStandardMerchantNegtiveFeedbackGoodsAllTimeList");
  if (item_iter != feasury_data_map.end() && time_iter != feasury_data_map.end()) {
    const auto& item_id_vec = item_iter->second.int_list_value();
    const auto& timestamp_vec = time_iter->second.int_list_value();
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    int size = std::min(item_id_vec.size(), timestamp_vec.size());
    for (int i = 0; i < size; i++) {
      auto item_id = item_id_vec[i];
      auto timestamp = timestamp_vec[i];
      if (item_id > 0 && timestamp > check_start_ts_ms) {
        product_id_set.insert(item_id);
        session_data->get_dot()->Count(1, "ShelfDislikeProductidFilterNum");
      }
    }
  }
  if (product_id_set.size() == 0) {
    return;
  }
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto unit = p_ad->base.p_unit;
    if (unit == nullptr || !ad.is_inner_loop_ad()) {
      return PASS;
    }
    // 短视频
    if (ad.dsp_item_type() == AdEnum::ITEM_PHOTO) {
      const auto* p_wt_product = ad.base.p_wt_product;
      if (p_wt_product) {
        int64_t product_id = p_wt_product->product_id();
        if (product_id > 0 && product_id_set.size() > 0 && product_id_set.count(product_id) > 0) {
          return FILTERED;
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}
RegistorRuleFilter(ShelfDislikeProductidFilter,
            kuaishou::log::ad::AdTraceFilterCondition::SHELF_DISLIKE_PRODUCTID_FILTER);

void SpuPurchasedFilter(ks::platform::AddibleRecoContextInterface* context,
                                      RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (SPDM_disable_other_purchase_freq_rule(session_data->get_spdm_ctx())) {
    return;
  }

  bool purchase_freq_rule_change_spu_source =
    SPDM_purchase_freq_rule_change_spu_source(session_data->get_spdm_ctx());
  absl::flat_hash_set<int64_t> purchased_spu_photo;
  absl::flat_hash_set<int64_t> purchased_kg;
  absl::flat_hash_set<int64_t> purchased_author;
  auto author_list = AdKconfUtil::authorFreqRuleList();
  const auto& request = *(session_data->get_ad_request());
  if (request.has_ad_user_info() && request.ad_user_info().ad_convert_info_size() > 0) {
    int64 freq_check_range = SPDM_spuPurchasedFreqControlInterval();
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    for (const auto& ad_convert_info : request.ad_user_info().ad_convert_info()) {
      auto timestamp = ad_convert_info.timestamp();
      if (timestamp > check_start_ts_ms) {
        for (const auto& ad_convert_info_item : ad_convert_info.ad_convert_info_item()) {
          auto& ad_spu_ids = ad_convert_info_item.ad_spu_ids();
          auto& kg_list = ad_convert_info_item.kg_tag_ids();
          int64_t author_id = ad_convert_info_item.author_id();
          purchased_spu_photo.insert(ad_spu_ids.begin(), ad_spu_ids.end());
          purchased_kg.insert(kg_list.begin(), kg_list.end());
          if (author_list->find(author_id) != author_list->end()) {
            purchased_author.insert(author_id);
          }
        }
      }
    }
  }
  if (purchase_freq_rule_change_spu_source) {
    purchased_spu_photo.clear();
    int64 freq_check_range = SPDM_spuPurchasedFreqControlInterval();
    const auto& feasury_data_map = request.ad_user_info().feasury_data_map();
    auto spu_iter = feasury_data_map.find("uAdOrderProductSpuIdV2List");
    auto time_iter = feasury_data_map.find("uMerchantAdOrderTimeV2List");
    if (spu_iter != feasury_data_map.end() && time_iter != feasury_data_map.end()) {
      const auto& spu_id_vec = spu_iter->second.int_list_value();
      const auto& timestamp_vec = time_iter->second.int_list_value();
      int64 now_ms = base::GetTimestamp() / 1000;
      int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
      int size = std::min(spu_id_vec.size(), timestamp_vec.size());
      int c_index = spu_id_vec.size() - 1;
      int t_index = timestamp_vec.size() - 1;
      for (int i = 0; i < size; i++) {
        auto spu_id = spu_id_vec[c_index - i];
        auto timestamp = timestamp_vec[t_index - i];
        if (spu_id > 0 && timestamp > check_start_ts_ms) {
          purchased_spu_photo.insert(spu_id);
        }
      }
    }
  }

  if (purchased_spu_photo.size() + purchased_kg.size() + purchased_author.size() == 0) {
    return;
  }
  int32 filter_num = 0;
  int32 filter_num_v2 = 0;
  int32 filter_num_live = 0;
  int32 filter_num_author = 0;
  int32 miss_cnt = 0;
  int32 total_cnt = 0;
  auto kg_freq_list = AdKconfUtil::kgFreqBizBlackList();
  bool enable_kg_freq_rule = SPDM_enable_kg_freq_rule(session_data->get_spdm_ctx());
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    // auto merchant_support_info = p_ad->base.p_merchant_support_info;
    auto unit = p_ad->base.p_unit;
    if (unit == nullptr || !unit->has_merchant_small_shop_support_info_optional()) {
      return PASS;
    }
    if (ad.IsEspPhotoPromotion() && (ad.bid_info.ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
                                     ad.bid_info.ocpx_action_type == kuaishou::ad::AD_MERCHANT_T7_ROI ||
                                     ad.bid_info.ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS ||
                                     ad.bid_info.ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
                                     ad.bid_info.ocpx_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID ||
                                     ad.bid_info.ocpx_action_type == kuaishou::ad::CID_ROAS)) {
      int64_t product_id = unit->item_id();
      if (product_id == 0) {
        return PASS;
      }
      const auto* p_wt_product = ad.base.p_wt_product;
      if (!p_wt_product) {
        return PASS;
      }
      total_cnt++;
      //  kg tags freq ctrl
      if (enable_kg_freq_rule) {
        if (ad.base.p_wt_author &&
            !(kg_freq_list && (kg_freq_list->count(ad.base.p_wt_author->biz_center()) > 0))) {
          for (const int64_t& kg_id : p_wt_product->kg_tag_ids()) {
            if (purchased_kg.count(kg_id) > 0) {
              filter_num_v2++;
              return FILTERED;
            }
          }
        }
      }
      //  author freq ctrl
      int64_t author_id = ad.author_id(session_data->get_is_esp_daitou());
      if (ad.base.p_unit->put_type() == kuaishou::ad::AdEnum::LSP_KOL_TYPE ||
          (ad.base.p_creative->creative_photo_ascription() == kuaishou::ad::AdEnum::PHOTO_ASCRIPTION_OTHER)) {
        author_id = ad.base.p_creative->photo_author_id();
      }
      if (purchased_author.size() > 0 && (author_list->find(author_id) != author_list->end())) {
        filter_num_author++;
        return FILTERED;
      }
      //  spu v2 freq ctrl
      const auto* p_wt_photo = ad.base.p_wt_photo;
      int64_t spu_cluster_1 =
          (p_wt_photo && p_wt_photo->ad_spu_ids().size() > 0) ? p_wt_photo->ad_spu_ids()[0] : 0;
      if (spu_cluster_1 > 0 && purchased_spu_photo.size() > 0 &&
          purchased_spu_photo.count(spu_cluster_1) > 0) {
        filter_num++;
        return FILTERED;
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
  session_data->get_dot()->Interval(filter_num, "amd_spu_purchased_freq_ctrl", "filter_num");
  session_data->get_dot()->Interval(filter_num_v2, "amd_spu_purchased_freq_ctrl", "filter_num_v2");
  session_data->get_dot()->Interval(filter_num_author,
    "amd_spu_purchased_freq_ctrl", "filter_num_author");
  session_data->get_dot()->Interval(filter_num_live, "amd_spu_purchased_freq_ctrl", "filter_num_live");
  session_data->get_dot()->Interval(miss_cnt, "amd_spu_purchased_freq_ctrl", "miss_cnt");
  session_data->get_dot()->Interval(total_cnt, "amd_spu_purchased_freq_ctrl", "total_cnt");
}
RegistorRuleFilter(SpuPurchasedFilter,
            kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_SPU_FREQ_CNTRL);

void DupAuthorIdFilter(ks::platform::AddibleRecoContextInterface* context,
                                      RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (!ks::ad_target::AdKconfUtil::enableDupAuthorIdFilter()) {
    return;
  }

  uint32_t recent_author_fanstop_filter_count = 0;
  uint32_t author_one_day_fanstop_filter_count = 0;
  uint32_t recent_author_native_ad_filter_count = 0;
  uint32_t author_one_day_native_ad_filter_count = 0;
  uint64_t browsed_author_max_num = AdKconfUtil::authorFreqDailyLimit();
  // 关注页天级频控因子
  if (session_data->get_pos_manager_base().IsFollow() &&
      SPDM_enable_follow_author_freq_day_factor2(session_data->get_spdm_ctx())) {
    browsed_author_max_num = static_cast<uint64_t>(
        browsed_author_max_num * SPDM_follow_author_freq_day_factor(session_data->get_spdm_ctx()));
  }

  auto* session_context = session_data->get_session_context();
  bool enable_native_ad_author_dup_filter =
      session_context->TryGetBoolean("enable_native_ad_author_dup_filter", true);
  bool enable_suppress_negative_a_level_exp =
      session_context->TryGetBoolean("enable_suppress_negative_a_level_exp", false);

  uint64_t browsed_author_max_num_level_a =
      session_context->TryGetInteger("browsed_author_max_num_level_a", 4);

  uint64_t browsed_author_max_num_level_b =
      session_context->TryGetInteger("browsed_author_max_num_level_b", 4);
  uint64_t browsed_author_max_num_level_c =
      session_context->TryGetInteger("browsed_author_max_num_level_c", 4);
  uint64_t browsed_author_max_num_level_d =
      session_context->TryGetInteger("browsed_author_max_num_level_d", 4);
  uint64_t follow_browsed_author_max_num_level_d =
      session_data->get_session_context()->TryGetInteger("follow_browsed_author_max_num_level_d", 4);
  bool enable_inner_region_skip_filter_exp =
    SPDM_enable_inner_region_skip_filter_exp(session_data->get_spdm_ctx());
  const std::string& region_org_types =
    SPDM_inner_skip_ecpm_wtr_filter_orgid_exp(session_data->get_spdm_ctx());
  if (FlowType::IsFollow(session_data)) {
    browsed_author_max_num_level_d = follow_browsed_author_max_num_level_d;
  }
  bool follow_page_align_rule_filter =
      SPDM_enable_live_target_follow_page_align_rule_filter(session_data->get_spdm_ctx());

  // p2p 数据 : 获取 author_id 粉条负反馈等级
  const std::string& app_id = session_data->get_pos_manager_base().GetRequestAppId();
  std::string key_prefix = absl::Substitute("fanstop_freq_$0", app_id);

  const SoftAdFreqContextData* soft_freq_data_tmp;

  if (SPDM_enable_soft_freq_context_migrate(session_data->get_spdm_ctx())) {
    soft_freq_data_tmp = context->GetPtrCommonAttr<SoftAdFreqContextData>("soft_ad_freq_context");
  } else {
    soft_freq_data_tmp = &session_data->get_soft_ad_freq_ctx();
  }
  if (!soft_freq_data_tmp) {
    return;
  }

  const SoftAdFreqContextData& soft_freq_data = *soft_freq_data_tmp;

  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    RetrievalAdCommon& ad = *p_ad;
    if (!session_data->get_pos_manager_base().IsFollow() || !follow_page_align_rule_filter) {
      bool is_direct_live_ad = ad.base.live_creative_type() == AdEnum::LIVE_STREAM_CREATIVE_TYPE ||
                             ad.base.live_creative_type() == AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT;
      if (is_direct_live_ad && !ad.IsFanstopLive()) {
        return PASS;
      }
    }
    // Note(luoqiang) 内粉本地生活跳过过滤实验
    std::string org_id = std::to_string(ad.payer_id()).substr(0, 4);
    if (enable_inner_region_skip_filter_exp && ad.is_inner_delivery() && org_id.size() == 4 &&
        region_org_types.find(org_id) != std::string::npos) {
      return PASS;
    }

    bool enable_author_dup_filter = ad.IsFanstop() || (ad.IsEsp() && enable_native_ad_author_dup_filter);
    if (!enable_author_dup_filter)
      return PASS;
    auto author_id = ad.author_id(session_data->get_is_esp_daitou());
    if (ad.base.p_unit->put_type() == kuaishou::ad::AdEnum::LSP_KOL_TYPE ||
        (ad.base.p_creative->creative_photo_ascription() == kuaishou::ad::AdEnum::PHOTO_ASCRIPTION_OTHER)) {
      author_id = ad.base.p_creative->photo_author_id();
    }
    if (ad.item_type() == FansTopEnum::ITEM_PHOTO) {
      // 作者频控：t 时间内不看重复作者
      if (soft_freq_data.recent_author_filter_set.count(author_id)) {
        if (ad.IsFanstop())
          recent_author_fanstop_filter_count++;
        if (ad.IsEsp())
          recent_author_native_ad_filter_count++;
        return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_AUTHOR_FILTER;
      }
      // 作者频控：限制单日看同一作者最大次数
      auto iter = soft_freq_data.author_set_in_one_day.find(author_id);
      if (iter != soft_freq_data.author_set_in_one_day.end() &&
          iter->second.size() >= browsed_author_max_num) {
        return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_AUTHOR_DAY_FILTER;
      }
    }
    // 直播作者频控（与作品共用）
    if (ad.item_type() == FansTopEnum::ITEM_LIVE) {
      // 直播作者频控: t 时间内不看同一作者
      if (!soft_freq_data.recent_author_filter_set.empty() &&
          soft_freq_data.recent_author_filter_set.count(author_id)) {
        if (ad.IsFanstop())
          recent_author_fanstop_filter_count++;
        if (ad.IsEsp())
          recent_author_native_ad_filter_count++;
        return kuaishou::log::ad::AdTraceFilterCondition::FREQ_LIVE_BROWSED_AUTHOR_FILTER;
      }
      // 直播作者频控: 限制单日看同一作者最大次数
      if (!soft_freq_data.author_set_in_one_day.empty()) {
        auto iter = soft_freq_data.author_set_in_one_day.find(author_id);
        if (iter != soft_freq_data.author_set_in_one_day.end() &&
            iter->second.size() >= browsed_author_max_num) {
          return kuaishou::log::ad::AdTraceFilterCondition::FREQ_LIVE_BROWSED_AUTHOR_DAY_FILTER;
        }
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}

RegistorRuleFilter(DupAuthorIdFilter,
            kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_AUTHOR_FILTER);

void DupTagFilter(ks::platform::AddibleRecoContextInterface* context,
                                              RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  int32_t browsed_weak_max_num = session_data->get_session_context()->TryGetInteger(
    "browsed_weak_max_num", 2);
  int32_t browsed_no_comment_max_num =
      session_data->get_session_context()->TryGetInteger("browsed_no_comment_max_num", 10000);
  int32_t browsed_pc_live_max_num =
      session_data->get_session_context()->TryGetInteger("browsed_pc_live_max_num", 10000);
  int32_t browsed_game_live_max_num =
      session_data->get_session_context()->TryGetInteger("browsed_game_live_max_num", 10000);
  int32_t browsed_low_fans_cnt_max_num =
      session_data->get_session_context()->TryGetInteger("browsed_low_fans_cnt_max_num", 100);
  int32_t browsed_hetu_max_num = session_data->get_session_context()->TryGetInteger(
    "browsed_hetu_max_num", 4);
  int32_t browsed_mmu_cluster_max_num =
      session_data->get_session_context()->TryGetInteger("browsed_mmu_cluster_max_num", 10000);
  int32_t recent_m_mmu_cluster_max_num = 1;
  if (FlowType::IsNebula(session_data)) {
    recent_m_mmu_cluster_max_num =
        session_data->get_session_context()->TryGetInteger("recent_m_mmu_cluster_max_num_nebula", 10);
  } else {
    recent_m_mmu_cluster_max_num =
        session_data->get_session_context()->TryGetInteger("recent_m_mmu_cluster_max_num_kuaishou", 10);
  }
  bool enable_fanstop_mmu_cluster_browsed_freq =
      session_data->get_session_context()->TryGetBoolean("enable_fanstop_mmu_cluster_browsed_freq", true);
  auto game_live_white_list = AdKconfUtil::gameLiveWhiteList();
  auto skip_payer_white_list = AdKconfUtil::fanstopSkipExpPayerList();
  bool skip_fanstop_game_freq_control =
      session_data->get_session_context()->TryGetBoolean("skip_fanstop_game_freq_control", false);
  bool enable_jinniu_tag_freq =
    session_data->get_session_context()->TryGetBoolean("enable_jinniu_tag_freq", true);
  bool enable_inner_region_skip_filter_exp =
    SPDM_enable_inner_region_skip_filter_exp(session_data->get_spdm_ctx());
  const std::string& region_org_types =
    SPDM_inner_skip_ecpm_wtr_filter_orgid_exp(session_data->get_spdm_ctx());
  bool enable_weak_filter = AdKconfUtil::enableFreqWeakFilter();
  absl::flat_hash_map<std::string, bool> photo_tag_freq_control_switch_map;
  absl::flat_hash_map<std::string, int64_t> photo_tag_freq_control_max_num_map;
  absl::flat_hash_map<std::string, bool> live_tag_freq_control_switch_map;
  absl::flat_hash_map<std::string, int64_t> live_tag_freq_control_max_num_map;

  for (std::string tag_prefix : photo_freq_control_tag_list) {
    photo_tag_freq_control_switch_map[tag_prefix] =
        session_data->get_session_context()->TryGetBoolean(
          "enable_" + tag_prefix + "_browsed_freq", false);
    photo_tag_freq_control_max_num_map[tag_prefix] =
        session_data->get_session_context()->TryGetInteger("browsed_" + tag_prefix + "_max_num", 4);
  }
  for (std::string tag_prefix : live_freq_control_tag_list) {
    live_tag_freq_control_switch_map[tag_prefix] =
        session_data->get_session_context()->TryGetBoolean(
          "enable_" + tag_prefix + "_browsed_freq", false);
    live_tag_freq_control_max_num_map[tag_prefix] =
        session_data->get_session_context()->TryGetInteger("browsed_" + tag_prefix + "_max_num", 4);
  }

  auto skip_exp = [&skip_payer_white_list, &skip_fanstop_game_freq_control](RetrievalAdCommon* ad) -> bool {
    return skip_fanstop_game_freq_control && skip_payer_white_list != nullptr &&
           skip_payer_white_list->count(ad->payer_id());
  };

  const SoftAdFreqContextData* soft_freq_data_tmp;

  if (SPDM_enable_soft_freq_context_migrate(session_data->get_spdm_ctx())) {
    soft_freq_data_tmp = context->GetPtrCommonAttr<SoftAdFreqContextData>("soft_ad_freq_context");
  } else {
    soft_freq_data_tmp = &session_data->get_soft_ad_freq_ctx();
  }
  if (!soft_freq_data_tmp) {
    return;
  }

  const SoftAdFreqContextData& soft_freq_data = *soft_freq_data_tmp;
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    bool is_direct_live_ad = ad.base.live_creative_type() == AdEnum::LIVE_STREAM_CREATIVE_TYPE ||
                             ad.base.live_creative_type() == AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT;
    if (is_direct_live_ad && !ad.IsFanstopLive()) {
      return PASS;
    }
    // Note(luoqiang) 内粉本地生活跳过过滤实验
    std::string org_id = std::to_string(ad.payer_id()).substr(0, 4);
    if (enable_inner_region_skip_filter_exp && ad.is_inner_delivery() && org_id.size() == 4 &&
        region_org_types.find(org_id) != std::string::npos) {
      return PASS;
    }

    if (!enable_jinniu_tag_freq && (p_ad->base.p_campaign->promotion_type() == AdEnum::FLASH_PROMOTION ||
                                    p_ad->base.p_campaign->promotion_type() == AdEnum::SPECIALTY_PROMOTION)) {
      return PASS;
    }
    // 弱负向频控
    if (enable_weak_filter && p_ad->item_type() == FansTopEnum::ITEM_PHOTO && p_ad->is_weak()) {
      // t 时间内不看弱负向作品
      if (soft_freq_data.recent_weak_photo_filter) {
        return kuaishou::log::ad::AdTraceFilterCondition::PHOTO_STATUS_WEAK_FILTER;
      }
      // 次数频控：限制单日看弱负向最大次数
      if (enable_weak_filter && soft_freq_data.weak_set_in_one_day.size() >= browsed_weak_max_num) {
        return kuaishou::log::ad::AdTraceFilterCondition::PHOTO_STATUS_WEAK_FILTER;
      }
    }
    // 无评论频控
    auto* fans_photo = p_ad->base.p_photo_status;
    if (fans_photo) {
      const auto& photo_promotable_status_vec = fans_photo->photo_promotable_status();
      if (p_ad->item_type() == FansTopEnum::ITEM_PHOTO &&
          std::count(photo_promotable_status_vec.begin(), photo_promotable_status_vec.end(),
                     FansTopEnum::PHOTO_USER_CLOSE_COMMENT_FLAG)) {
        // t 时间内不看无评论作品
        if (soft_freq_data.recent_no_comment_photo_filter) {
          return kuaishou::log::ad::AdTraceFilterCondition::USER_CLOSE_COMMENT_FILTER;
        }
        // 次数频控：限制单日看无评论最大次数
        if (!soft_freq_data.no_comment_set_in_one_day.empty() &&
            soft_freq_data.no_comment_set_in_one_day.size() >= browsed_no_comment_max_num) {
          return kuaishou::log::ad::AdTraceFilterCondition::USER_CLOSE_COMMENT_FILTER;
        }
      }
    }

    // 标签频控
    if (ad.base.live_creative_type() != AdEnum::LIVE_STREAM_CREATIVE_TYPE) {
      for (std::string tag_prefix : photo_freq_control_tag_list) {
        if (photo_tag_freq_control_switch_map[tag_prefix]) {
          std::vector<std::string> tag_suffix_vec;
          GetTagSuffix(tag_prefix, ad, &tag_suffix_vec, context);
          for (std::string tag_suffix : tag_suffix_vec) {
            // t 时间间隔频控
            if (!soft_freq_data.recent_tag_filter_set.empty() &&
                soft_freq_data.recent_tag_filter_set.count(tag_prefix + tag_suffix)) {
              return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_TAG_FILTER;
            }
            // 限制单日观看最大次数
            if (!soft_freq_data.tag_set_in_one_day.empty()) {
              auto iter = soft_freq_data.tag_set_in_one_day.find(tag_prefix + tag_suffix);
              if (iter != soft_freq_data.tag_set_in_one_day.end() &&
                  iter->second.size() >= photo_tag_freq_control_max_num_map[tag_prefix]) {
                return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_TAG_FILTER;
              }
            }
          }
        }
      }
    }
    if (ad.base.live_creative_type() == AdEnum::LIVE_STREAM_CREATIVE_TYPE) {
      for (std::string tag_prefix : live_freq_control_tag_list) {
        if (live_tag_freq_control_switch_map[tag_prefix]) {
          std::vector<std::string> tag_suffix_vec;
          GetTagSuffix(tag_prefix, ad, &tag_suffix_vec, context);
          for (std::string tag_suffix : tag_suffix_vec) {
            // t 时间间隔频控
            if (!soft_freq_data.recent_tag_filter_set.empty() &&
                soft_freq_data.recent_tag_filter_set.count(tag_prefix + tag_suffix)) {
              return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_TAG_FILTER;
            }
            // 限制单日观看最大次数
            if (!soft_freq_data.tag_set_in_one_day.empty()) {
              auto iter = soft_freq_data.tag_set_in_one_day.find(tag_prefix + tag_suffix);
              if (iter != soft_freq_data.tag_set_in_one_day.end() &&
                  iter->second.size() >= live_tag_freq_control_max_num_map[tag_prefix]) {
                return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_TAG_FILTER;
              }
            }
          }
        }
      }
    }

    if (session_data->get_enable_context_init_optimization()) {
      // 河图标签频控：
      bool find = false;
      if (ad.item_type() == FansTopEnum::ITEM_PHOTO) {
        if (ad.base.p_wt_photo) {
          find = true;
        }
      } else if (ad.item_type() == FansTopEnum::ITEM_LIVE) {
        if (ad.base.p_wt_live && ad.base.p_wt_photo) {
          find = true;
        }
      }

      if (find) {
        // 粉条二级河图标签频控
        const auto& hetu_level_two = ad.base.p_wt_photo->hetu_level_two_list();
        if (hetu_level_two.size() > 0) {
          for (int j = 0; j < hetu_level_two.size(); j++) {
            // t 时间内不看重复河图标签的视频
            if (!soft_freq_data.recent_hetu_filter_set.empty() &&
                soft_freq_data.recent_hetu_filter_set.count(hetu_level_two[j])) {
              return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_HETU_FILTER;
            }
            // 限制单日看同一河图标签视频的最大次数
            if (!soft_freq_data.hetu_set_in_one_day.empty()) {
              auto iter = soft_freq_data.hetu_set_in_one_day.find(hetu_level_two[j]);
              if (iter != soft_freq_data.hetu_set_in_one_day.end() &&
                  iter->second.size() >= browsed_hetu_max_num) {
                return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_HETU_DAY_FILTER;
              }
            }
          }
        }
        // mmu cluster_id 频控, 仅针对作品粉条生效
        if (enable_fanstop_mmu_cluster_browsed_freq && ad.item_type() == FansTopEnum::ITEM_PHOTO) {
          int32 mmu_cluster_id = ad.base.p_wt_photo->mmu_cluster_id();
          // t 时间内不看重复 cluster_id 的视频
          if (!soft_freq_data.recent_mmu_cluster_set.empty() &&
              soft_freq_data.recent_mmu_cluster_set.count(mmu_cluster_id)) {
            return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_MMU_CLUSTER_FILTER;
          }
          // 限制单日看同一 cluster_id 视频的最大次数
          if (!soft_freq_data.mmu_cluster_set_in_one_day.empty()) {
            auto iter = soft_freq_data.mmu_cluster_set_in_one_day.find(mmu_cluster_id);
            if (iter != soft_freq_data.mmu_cluster_set_in_one_day.end() &&
                iter->second.size() >= browsed_mmu_cluster_max_num) {
              return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_MMU_CLUSTER_DAY_FILTER;
            }
          }
          // 最近观看 N 个 cluster_id 出 M 次策略
          if (!soft_freq_data.recent_n_mmu_cluster_id.empty()) {
            auto iter = soft_freq_data.recent_n_mmu_cluster_id.find(mmu_cluster_id);
            if (iter != soft_freq_data.recent_n_mmu_cluster_id.end() &&
                iter->second.size() >= recent_m_mmu_cluster_max_num) {
              return kuaishou::log::ad::AdTraceFilterCondition::FREQ_BROWSED_MMU_CLUSTER_FILTER;
            }
          }
        }
      }
      auto author_id = ad.author_id(session_data->get_is_esp_daitou());
      if (ad.base.p_unit->put_type() == kuaishou::ad::AdEnum::LSP_KOL_TYPE ||
          (ad.base.p_creative->creative_photo_ascription() == kuaishou::ad::AdEnum::PHOTO_ASCRIPTION_OTHER)) {
        author_id = ad.base.p_creative->photo_author_id();
      }
      if (game_live_white_list != nullptr &&
          !game_live_white_list->count(author_id) &&
            !skip_exp(p_ad)) {
        bool is_recruiting_live =
            ad.base.p_live_stream_user_info != nullptr &&
            ad.base.p_live_stream_user_info->is_recruiting_live() == kuaishou::ad::AdEnum::RECRUIT_LIVE;
        // PC 直播频控, 仅对非电商作者（last_30d_pay_item_num_cnt == -1）生效
        if (p_ad->is_pc_live() && !(p_ad->is_inner_delivery()) &&
            !(ad.base.p_wt_author && ad.base.p_wt_author->last_30d_pay_item_num_cnt() >= 0) &&
            !is_recruiting_live) {
          // t 时间内不看 PC 直播
          if (soft_freq_data.recent_pc_live_filter) {
            return kuaishou::log::ad::AdTraceFilterCondition::UNSUPPORT_LIVE_TYPE;
          }
          // 次数频控：限制单日看 PC 直播最大次数
          if (!soft_freq_data.pc_live_set_in_one_day.empty() &&
              soft_freq_data.pc_live_set_in_one_day.size() >= browsed_pc_live_max_num) {
            return kuaishou::log::ad::AdTraceFilterCondition::UNSUPPORT_LIVE_TYPE;
          }
        }
        bool is_game = p_ad->is_game_live() || p_ad->is_hetu_game_live();
        // 游戏直播频控
        if (is_game) {
          // t 时间内不看游戏直播
          if (soft_freq_data.recent_game_live_filter) {
            return kuaishou::log::ad::AdTraceFilterCondition::HIGH_FREQ_GAME_LIVE_FILTER;
          }
          // 次数频控：限制单日看游戏直播最大次数
          if (!soft_freq_data.game_live_set_in_one_day.empty() &&
              soft_freq_data.game_live_set_in_one_day.size() >= browsed_game_live_max_num) {
            return kuaishou::log::ad::AdTraceFilterCondition::HIGH_FREQ_GAME_LIVE_FILTER;
          }
        }
      }
    }
    // 低粉非认证 OCPC 频控
    if (p_ad->is_small_seller_ocpc() && !p_ad->is_inner_delivery()) {
      // t 时间内不看低粉非认证 OCPC
      if (soft_freq_data.recent_small_seller_ocpc_filter) {
        return kuaishou::log::ad::AdTraceFilterCondition::LOW_FANS_CNT_FREQ_CONTROL_FILTER;
      }
      // 次数频控：限制单日看低粉非认证 OCPC 最大次数
      if (!soft_freq_data.small_seller_ocpc_set_in_one_day.empty() &&
          soft_freq_data.small_seller_ocpc_set_in_one_day.size() >= browsed_low_fans_cnt_max_num) {
        return kuaishou::log::ad::AdTraceFilterCondition::LOW_FANS_CNT_FREQ_CONTROL_FILTER;
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}

RegistorRuleFilter(DupTagFilter,
            kuaishou::log::ad::AdTraceFilterCondition::PHOTO_STATUS_WEAK_FILTER);

void NativePhotoIdFreq(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (session_data->get_fans_browsed_photo_ids().empty()) {
    return;
  }
  int pos_id = 0;
  if (session_data->get_pos_manager_base().request_imp_infos.size() > 0) {
    pos_id = session_data->get_pos_manager_base().request_imp_infos[0].pos_id;
  }
  if ((pos_id == 87846 || pos_id == 87847) && AdKconfUtil::enableLiveReservationPageSkipFilter()) {
    return;
  }
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    if (CreativeIsLive(ad.base.p_creative)) {
      return PASS;
    }
    if (SPDM_enable_fans_adjust_frequency(session_data->get_spdm_ctx())) {
      if (session_data->get_fans_frequency_photo_set().count(ad.photo_id())) {
        return FILTERED;
      }
    } else {
      if (session_data->get_fans_browsed_photo_ids().count(ad.photo_id())) {
        return FILTERED;
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
}
RegistorRuleFilter(NativePhotoIdFreq,
            kuaishou::log::ad::AdTraceFilterCondition::FREQ_PHOTO_SET_FLITER);

void MerchantPurchasedFilter(ks::platform::AddibleRecoContextInterface* context,
                           RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (SPDM_disable_other_purchase_freq_rule(session_data->get_spdm_ctx())) {
    return;
  }
  bool enable_product_purchased_freq =
      session_data->get_session_context()->TryGetBoolean("enable_product_purchased_freq", false);
  bool enable_product_purchased_freq_merchant =
      session_data->get_session_context()->TryGetBoolean("enable_product_purchased_freq_merchant", false);
  if (!enable_product_purchased_freq) {
    return;
  }
  std::unordered_set<int64_t> purchased_product;
  const auto& request = *(session_data->get_ad_request());
  int visitor_id = 0;
  if (request.has_ad_user_info() && request.ad_user_info().ad_convert_info_size() > 0) {
    int64 freq_check_range =
        session_data->get_session_context()->TryGetInteger("product_purchased_freq_check_range", 86400);
    int64 now_ms = base::GetTimestamp() / 1000;
    int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
    for (const auto& ad_convert_info : request.ad_user_info().ad_convert_info()) {
      auto timestamp = ad_convert_info.timestamp();
      if (timestamp > check_start_ts_ms) {
        for (const auto& ad_convert_info_item : ad_convert_info.ad_convert_info_item()) {
          purchased_product.insert(ad_convert_info_item.product_id());
        }
      }
    }
    visitor_id = request.ad_user_info().id();
  }
  if (purchased_product.size() == 0) {
    return;
  }
  int32 filter_num = 0;
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    auto unit = p_ad->base.p_unit;
    if (unit == nullptr || !unit->has_merchant_small_shop_support_info_optional()) {
      return PASS;
    }
    if (ad.IsDirectEcom() &&
        ad.ocpc_action_type() == kuaishou::ad::AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED &&
        purchased_product.find(unit->product_id()) != purchased_product.end()) {
      filter_num += 1;
      return FILTERED;
    }
    if (enable_product_purchased_freq_merchant && ad.IsEspPhotoPromotion() &&  // lingdian
        (ad.bid_info.ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
         ad.bid_info.ocpx_action_type == kuaishou::ad::AD_MERCHANT_T7_ROI ||
         ad.bid_info.ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS ||
         ad.bid_info.ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
         ad.bid_info.ocpx_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID ||
         ad.bid_info.ocpx_action_type == kuaishou::ad::CID_ROAS) &&
        purchased_product.find(unit->item_id()) != purchased_product.end()) {
      filter_num += 1;
      return FILTERED;
    }
    return PASS;
  };
  DO_FILTER_PROCESS();

  session_data->get_dot()->Interval(filter_num, "merchant_purchased_filter_ads_num",
    std::to_string(visitor_id));
}
RegistorRuleFilter(MerchantPurchasedFilter,
            kuaishou::log::ad::AdTraceFilterCondition::PURCHASED_ITEM_SET);

void MerchantFollowPhotoFilter(ks::platform::AddibleRecoContextInterface* context,
                           RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (session_data->get_pos_manager_base().GetAdRequestType() != AdEnum::FLOW_FOLLOW) {
    return;
  }
  absl::flat_hash_set<int64> photo_set;  // 关注页流量浏览过的 photot_id
  if (session_data->get_pos_manager_base().GetAdRequestType() ==
      kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_FOLLOW) {
    for (auto photo_id : session_data->get_ad_request()->reco_user_info().browsed_photo_ids()) {
      photo_set.insert(photo_id);
    }
  }
  int before_filter_cnt = retrieval_ad_list->Size();
  int merchant_ads_cnt = 0, photo_to_live_ads_cnt = 0, filtered_cnt = 0;
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    if (p_ad->IsEspPhotoPromotion()) {
      merchant_ads_cnt++;
      if (photo_set.find(p_ad->photo_id()) != photo_set.end()) {
        return FILTERED;
      }
    }

    if (ad.base.live_creative_type() ==
        kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) {
      photo_to_live_ads_cnt++;
      if (photo_set.find(p_ad->photo_id()) != photo_set.end()) {
        return FILTERED;
      }
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
  filtered_cnt = before_filter_cnt - retrieval_ad_list->Size();  // NOLIINT
  session_data->get_dot()->Interval(before_filter_cnt, "amd_rule_filter", "merchant_follow_freq",
                              "before_filter_cnt");
  session_data->get_dot()->Interval(merchant_ads_cnt, "amd_rule_filter", "merchant_follow_freq",
                              "merchant_ads_cnt");
  session_data->get_dot()->Interval(photo_to_live_ads_cnt, "amd_rule_filter", "merchant_follow_freq",
                              "photo_to_live_ads_cnt");
  session_data->get_dot()->Interval(filtered_cnt, "amd_rule_filter",
    "merchant_follow_freq", "filtered_cnt");
  session_data->get_dot()->Interval(photo_set.size(), "amd_rule_filter", "merchant_follow_freq",
                              "watch_photo_before");
}
RegistorRuleFilter(MerchantFollowPhotoFilter,
            kuaishou::log::ad::AdTraceFilterCondition::BROWSED_SET);

void SpecPhotoIdFreq(ks::platform::AddibleRecoContextInterface* context,
                          RetrievalAdList* retrieval_ad_list, FilterInfo* filter_info) {
  INIT();
  if (!SPDM_enable_browsed_photo_freq_spec(session_data->get_spdm_ctx())) {
    return;
  }
  int pos_id = 0;
  if (session_data->get_pos_manager_base().request_imp_infos.size() > 0) {
    pos_id = session_data->get_pos_manager_base().request_imp_infos[0].pos_id;
  }
  if ((pos_id == 87846 || pos_id == 87847) && AdKconfUtil::enableLiveReservationPageSkipFilter()) {
    return;
  }
  auto white_pos_set = AdKconfUtil::specFreqPosSet();
  if (white_pos_set != nullptr && white_pos_set->size() > 0 && !white_pos_set->count(pos_id)) {
    return;
  }
  auto sensitive_cat_set = AdKconfUtil::sensitiveThirdCategorySet();
  int before_filter_cnt = retrieval_ad_list->Size();
  std::unordered_set<int64_t> spec_browsed_photo_ids;
  std::unordered_set<int64_t> spec_browsed_dup_photo_ids;
  int64 del_interval_sec_new = SPDM_spec_ad_photo_delivery_min(session_data->get_spdm_ctx()) * 60;
  const auto &browsed_infos = session_data->get_ad_request()->ad_user_info().ad_browsed_info();
  auto now_sec = base::GetTimestamp() / 1000000;
  bool enable_browsed_photo_freq_spec_sensitive_cat =
      SPDM_enable_browsed_photo_freq_spec_sensitive_cat(session_data->get_spdm_ctx());
  bool enable_browsed_photo_freq_spec_outer =
      SPDM_enable_browsed_photo_freq_spec_outer(session_data->get_spdm_ctx());
  for (auto &info : browsed_infos) {
    uint64_t ts_sec = info.timestamp() / 1000000;
    if (del_interval_sec_new >= 0 && (now_sec - ts_sec) > del_interval_sec_new) {
      continue;
    }
    for (auto &ad : info.ad_detail_info()) {
      if (white_pos_set != nullptr && white_pos_set->size() > 0 &&
          !white_pos_set->count(ad.pos_id())) {
        continue;
      }
      if (ad.photo_id() > 0) {
        spec_browsed_photo_ids.insert(ad.photo_id());
      }
      if (ad.dup_photo_id() > 1) {
        spec_browsed_dup_photo_ids.insert(ad.dup_photo_id());
      }
    }
  }
  auto filter = [&] (RetrievalAdCommon* p_ad) -> int {
    auto& ad = *p_ad;
    if (CreativeIsLive(ad.base.p_creative)) {
      return PASS;
    }
    const auto* p_wt_account = ad.base.p_wt_account;
    const auto* p_account = ad.base.p_account;
    if (!p_wt_account || !p_account) {
      return PASS;
    }
    bool is_target_ad = false;
    // 外循环非综平
    if (enable_browsed_photo_freq_spec_outer && !ad.is_inner_loop_ad() && p_wt_account->crm_center()!= 1) {
      is_target_ad = true;
    }
    // 敏感三级类目
    if (enable_browsed_photo_freq_spec_sensitive_cat && sensitive_cat_set != nullptr) {
      for (auto &third_id : p_account->stable_category_third_id()) {
        if (sensitive_cat_set->count(third_id)) {
          is_target_ad = true;
          break;
        }
      }
    }
    if (is_target_ad && spec_browsed_photo_ids.count(ad.photo_id())) {
      return FILTERED;
    }
    if (is_target_ad && spec_browsed_dup_photo_ids.count(ad.dup_photo_id())) {
      return FILTERED;
    }
    return PASS;
  };
  DO_FILTER_PROCESS();
  int filtered_cnt = before_filter_cnt - retrieval_ad_list->Size();  // NOLIINT
  session_data->get_dot()->Interval(filtered_cnt, "SpecPhotoIdFreq", "filtered_cnt");
}
RegistorRuleFilter(SpecPhotoIdFreq,
            kuaishou::log::ad::AdTraceFilterCondition::FREQ_PHOTO_SET_FLITER);
}  // namespace ad_target
}  // namespace ks
