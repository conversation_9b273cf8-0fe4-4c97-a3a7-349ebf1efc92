#include "teams/ad/ad_target/src/context_prepare/processors/trace_log_mixer.h"

#include <memory>
#include "ks/util/json.h"
#include "teams/ad/ad_target/common/context_data.h"
#include "teams/ad/ad_target/client/kafka/trace_manager.h"

namespace ks {
namespace ad_target {

bool TraceLogMixer::InitProcessor() {
  biz_ = config()->GetString("biz_name");
  base::Json* json = config()->Get("multi_retrieval_ad_list");
  if (json && json->IsObject()) {
    for (auto &object : json->objects()) {
      if (!object.second || !object.second->IsString()) {
        continue;
      }
      std::string biz = object.second->StringValue("");
      multi_retrieval_ad_list_[object.first] = biz;
      multi_retrieval_ad_list_vec_.push_back(object.first);
    }
  }
  json = config()->Get("multi_prerank_ad_list");
  if (json && json->IsObject()) {
    for (auto &object : json->objects()) {
      if (!object.second || !object.second->IsString()) {
        continue;
      }
      std::string biz = object.second->StringValue("");
      multi_prerank_ad_list_[object.first] = biz;
      multi_prerank_ad_list_vec_.push_back(object.first);
    }
  }
  LOG(INFO) << "TraceLogMixer::InitProcessor, biz=" << biz_;
  std::stringstream ss;
  for (const auto & [list, biz] : multi_retrieval_ad_list_) {
    ss << "list " << list << " biz: " << biz << "; ";
  }
  for (const auto & [list, biz] : multi_prerank_ad_list_) {
    ss << "list " << list << " biz: " << biz << "; ";
  }
  LOG(INFO) << "TraceLogMixer::InitProcessor " << ss.str();
  is_item_card_ = config()->GetBoolean("is_item_card", false);
  base::GetTimestamp();
  return true;
}

void TraceLogMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  session_data_ = context->GetMutablePtrCommonAttr<ks::ad_target::ContextData>("session_data");
  if (!session_data_) {
    return;
  }
  *session_data_->mutable_trace_log() = std::make_unique<AdTraceLogItem>();
  (*session_data_->mutable_trace_log())->StartTrace(session_data_);
  auto& simple_trace = (*session_data_->mutable_trace_log())->SimplifyTrace();
  if (!biz_.empty()) {
    simple_trace.InitBiz(biz_);
  }

  session_data_->set_enable_trace_tsm_for_ad(
      ks::ad_base::AdRandom::GetInt(0, 99) < SPDM_traceTsmRatioForAd() &&
      simple_trace.IsSample2Hive());
  session_data_->set_enable_trace_tsm(simple_trace.IsSample2Hive());
  session_data_->Attr(CommonIdx::is_trace_log)
      .SetIntValue(0, !(simple_trace.IsSample2Hive() || simple_trace.IsSample2Hawkeye()), false, false);

  if (biz_ == tsm::kTsmSceneOuterPhoto && session_data_->get_outer_loop_ad_rank_infos() &&
      session_data_->get_outer_loop_ad_rank_infos()->IsPrerankSample()) {
    session_data_->Attr(CommonIdx::is_trace_log).SetIntValue(0, 0, false, false);
  }
  if (biz_ == tsm::kTsmSceneLive && session_data_->get_outer_live_ad_rank_infos() &&
      session_data_->get_outer_live_ad_rank_infos()->IsPrerankSample()) {
    session_data_->Attr(CommonIdx::is_trace_log).SetIntValue(0, 0, false, false);
  }

  enable_prerank_trace_ = AdKconfUtil::enableTraceBase();
}

void TraceLogMixer::BuildContextDataRetrievalAdList(ks::platform::ReadableRecoContextInterface* ctx) {
  if (!ctx) {
    return;
  }
  session_data_->mutable_targeting_data()->retrieval_ad_list.Clear();
  for (const auto& ad_list_name : multi_retrieval_ad_list_vec_) {
    auto* retrieval_ad_list =
    ctx->GetPtrCommonAttr<ks::ad_target::RetrievalAdList>(ad_list_name);
    if (!retrieval_ad_list) {
      LOG(ERROR) << "invalid retrieval_ad_list" << ad_list_name;
      continue;
    }
    if (multi_retrieval_ad_list_vec_.size() == 1) {
      session_data_->mutable_targeting_data()->retrieval_ad_list = *retrieval_ad_list;
      return;
    }
    for (auto* ad : retrieval_ad_list->Ads()) {
      session_data_->mutable_targeting_data()->retrieval_ad_list.SplitAdd(ad);
    }
  }
}

void TraceLogMixer::BuildContextDataPrerankAdList(ks::platform::ReadableRecoContextInterface* ctx) {
  if (!ctx) {
    return;
  }
  *session_data_->mutable_prerank_ad_list() =
    std::make_shared<ks::ad_target::PrerankAdList>(session_data_->mutable_pb_arena());
  for (const auto& prerank_ad_list_name : multi_prerank_ad_list_vec_) {
    auto* prerank_ad_list = ctx->GetPtrCommonAttr<ks::ad_target::PrerankAdList>(prerank_ad_list_name);
    if (!prerank_ad_list) {
      LOG(ERROR) << "invalid prerank_ad_list" << prerank_ad_list_name;
      continue;
    }
    if (multi_prerank_ad_list_vec_.size() == 1) {
      *session_data_->mutable_prerank_ad_list() =
        std::make_shared<ks::ad_target::PrerankAdList>(*prerank_ad_list);
      return;
    }
    for (auto* ad : prerank_ad_list->FullAds()) {
      (*session_data_->mutable_prerank_ad_list())->Add(ad);
    }
  }
  (*session_data_->mutable_prerank_ad_list())->Compact();
}

void TraceLogMixer::BuildContextDataAdList(ks::platform::ReadableRecoContextInterface* context) {
  BuildContextDataRetrievalAdList(context);
  BuildContextDataPrerankAdList(context);
}

void TraceLogMixer::OnPipelineExit(ks::platform::ReadableRecoContextInterface* context) {
  if (AdKconfUtil::disableNearlineTrace() || !context || !session_data_ || !session_data_->get_trace_log()) {
    return;
  }
  BuildContextDataAdList(context);
  if (enable_prerank_trace_) {
    TraceWithoutPrerank(context);
    return;
  }
  if (is_item_card_) {
    ItemCardTrace(context);
    return;
  }
  absl::flat_hash_set<int64_t> prerank_cids;
  prerank_cids.clear();
  auto trace_prerank_adlist = [&](const PrerankAdList* adlist) {
    if (!adlist) {
      return;
    }
    for (auto p_ad : adlist->FullAds()) {
      prerank_cids.insert(p_ad->creative_id());
      if (session_data_->get_is_target_time_out()) {
        p_ad->SetAdInValid(kuaishou::log::ad::AdTraceFilterCondition::PRERANKING_SORT_FILTER,
                           kuaishou::ad::AdServerNodeType::PRERANKING_TYPE);
      } else if (p_ad->IsValid()) {
        p_ad->SetAdValid();
      }
      session_data_->get_trace_log()->SimplifyTrace().TracePrerankAd(*p_ad, *session_data_);
    }
  };
  auto trace_adlist = [&](RetrievalAdList* adlist) {
    for (auto p_ad : adlist->RawAds()) {
      auto& ad = *p_ad;
      // 命中粗排 cids, Trace 信息已经在粗排 list 打过了
      if (!prerank_cids.count(ad.creative_id())) {
          session_data_->get_trace_log()->SimplifyTrace().TraceAdResult(ad, *session_data_);
      }
      if (!ad.is_invalid()) {
        continue;
      }
      session_data_->mutable_targeting_data()->trace_stat.TraceCreative(ad.strategy_base.filter_cond,
                                                                        ad.creative_id());
    }
  };

  if (!biz_.empty()) {
    if (biz_ == tsm::kTsmSceneInnerSoftPhoto) {
      auto& ad_list = session_data_->mutable_targeting_data()->retrieval_ad_list.RawAds();
      for (auto p_ad : ad_list) {
        prerank_cids.insert(p_ad->creative_id());
        if (session_data_->get_is_target_time_out()) {
          p_ad->SetFilterInfo(kuaishou::log::ad::AdTraceFilterCondition::PRERANKING_SORT_FILTER);
        }
        session_data_->get_trace_log()->SimplifyTrace().TraceFanstopPrerankAd(*p_ad, *session_data_);
      }
    } else {
      trace_prerank_adlist(session_data_->mutable_prerank_ad_list()->get());
    }
    session_data_->mutable_targeting_data()->QuitRelease(prerank_cids, *session_data_->get_trace_log(),
                                    *session_data_, false);
  } else {
    for (const auto& [name, biz] : multi_prerank_ad_list_) {
      session_data_->get_trace_log()->SimplifyTrace().InitBiz(biz);
      auto* ad_list = context->GetPtrCommonAttr<PrerankAdList>(name);
      if (!ad_list) {
        continue;
      }
      if (biz == tsm::kTsmSceneInnerSoftPhoto) {
        for (auto p_ad : ad_list->Ads()) {
          prerank_cids.insert(p_ad->creative_id());
          session_data_->get_trace_log()->SimplifyTrace().TraceFanstopPrerankAd(*p_ad->p_retrieval_ad,
                                                                                *session_data_);
        }
      } else {
        trace_prerank_adlist(ad_list);
      }
    }
    for (const auto& [name, biz] : multi_retrieval_ad_list_) {
      session_data_->get_trace_log()->SimplifyTrace().InitBiz(biz);
      auto* ad_list = context->GetPtrCommonAttr<RetrievalAdList>(name);
      if (!ad_list) {
        continue;
      }
      trace_adlist(const_cast<RetrievalAdList*>(ad_list));
    }
  }
  session_data_->get_trace_log()->SimplifyTrace().TraceCounter(
      *session_data_, session_data_->get_targeting_data().retrieval_ad_list.Size());
  session_data_->get_trace_log()->SimplifyTrace().TraceMultiCounter(*session_data_);
  session_data_->get_trace_log()->EndTrace(session_data_, false);

  // 打点过滤原因
  for (const auto& [k, v] : session_data_->mutable_targeting_data()->trace_stat.other) {
    if (v == 0) {
      continue;
    }
    if (session_data_->get_dot()) {
      session_data_->get_dot()->Count(
          v, "ad_filter",
          AdTraceFilterCondition_Name(static_cast<kuaishou::log::ad::AdTraceFilterCondition>(k)));
    }
  }
  return;
}

void TraceLogMixer::ItemCardTrace(ks::platform::ReadableRecoContextInterface* context) {
  absl::flat_hash_set<int64_t> prerank_cids;
  prerank_cids.clear();
  for (const auto& [name, biz] : multi_prerank_ad_list_) {
    auto* ad_list = context->GetPtrCommonAttr<PrerankAdList>(name);
    if (!ad_list) {
      continue;
    }
    for (auto p_ad : ad_list->FullAds()) {
      prerank_cids.insert(p_ad->creative_id());
      if (session_data_->get_is_target_time_out()) {
        p_ad->SetAdInValid(kuaishou::log::ad::AdTraceFilterCondition::PRERANKING_SORT_FILTER,
                           kuaishou::ad::AdServerNodeType::PRERANKING_TYPE);
      } else if (p_ad->IsValid()) {
        p_ad->SetAdValid();
      }
      if (p_ad->p_retrieval_ad->others.hard_ad_delivery) {
        session_data_->get_trace_log()->SimplifyTrace().InitBiz(tsm::kTsmSceneInnerHardPhoto);
        session_data_->get_trace_log()->SimplifyTrace().TracePrerankAd(*p_ad, *session_data_);
      } else {
        session_data_->get_trace_log()->SimplifyTrace().InitBiz(tsm::kTsmSceneInnerSoftPhoto);
        session_data_->get_trace_log()->SimplifyTrace().TraceFanstopPrerankAd(*p_ad->p_retrieval_ad,
                                                                                *session_data_);
      }
    }
  }
  for (const auto& [name, biz] : multi_retrieval_ad_list_) {
    RetrievalAdList* ad_list = const_cast<RetrievalAdList*>(context->GetPtrCommonAttr<RetrievalAdList>(name));
    if (!ad_list) {
      continue;
    }
    for (auto* p_ad : ad_list->RawAds()) {
      auto& ad = *p_ad;
      if (p_ad->others.hard_ad_delivery) {
        session_data_->get_trace_log()->SimplifyTrace().InitBiz(tsm::kTsmSceneInnerHardPhoto);
      } else {
        session_data_->get_trace_log()->SimplifyTrace().InitBiz(tsm::kTsmSceneInnerSoftPhoto);
      }
      if (!prerank_cids.count(ad.creative_id())) {
          session_data_->get_trace_log()->SimplifyTrace().TraceAdResult(ad, *session_data_);
      }
      if (!ad.is_invalid()) {
        continue;
      }
      session_data_->mutable_targeting_data()->trace_stat.TraceCreative(
        ad.strategy_base.filter_cond, ad.creative_id());
    }
  }
  session_data_->get_trace_log()->SimplifyTrace().TraceCounter(
        *session_data_, session_data_->get_targeting_data().retrieval_ad_list.Size());
  session_data_->get_trace_log()->SimplifyTrace().TraceMultiCounter(*session_data_);
  session_data_->get_trace_log()->EndTrace(session_data_, false);
  // 打点过滤原因
  for (const auto& [k, v] : session_data_->mutable_targeting_data()->trace_stat.other) {
    if (v == 0) {
      continue;
    }
    if (session_data_->get_dot()) {
      session_data_->get_dot()->Count(
          v, "ad_filter",
          AdTraceFilterCondition_Name(static_cast<kuaishou::log::ad::AdTraceFilterCondition>(k)));
    }
  }
}

void TraceLogMixer::TraceWithoutPrerank(ks::platform::ReadableRecoContextInterface* context) {
  for (const auto& [name, biz] : multi_retrieval_ad_list_) {
    RetrievalAdList* ad_list = const_cast<RetrievalAdList*>(context->GetPtrCommonAttr<RetrievalAdList>(name));
    if (!ad_list) {
      continue;
    }
    session_data_->get_trace_log()->SimplifyTrace().InitBiz(biz);

    for (auto* p_ad : ad_list->RawAds()) {
      auto& ad = *p_ad;
      if (is_item_card_) {
        if (p_ad->others.hard_ad_delivery) {
          session_data_->get_trace_log()->SimplifyTrace().InitBiz(tsm::kTsmSceneInnerHardPhoto);
        } else {
          session_data_->get_trace_log()->SimplifyTrace().InitBiz(tsm::kTsmSceneInnerSoftPhoto);
        }
      }
      session_data_->get_trace_log()->SimplifyTrace().TraceAdResultMove(ad, *session_data_);
      if (!ad.is_invalid()) {
        continue;
      }
      session_data_->mutable_targeting_data()->trace_stat.TraceCreative(ad.strategy_base.filter_cond,
                                                                        ad.creative_id());
    }
  }
  session_data_->get_trace_log()->SimplifyTrace().TraceCounter(
      *session_data_, session_data_->get_targeting_data().retrieval_ad_list.Size());
  session_data_->get_trace_log()->SimplifyTrace().TraceMultiCounter(*session_data_);
  session_data_->get_trace_log()->EndTrace(session_data_, enable_prerank_trace_);

  // 打点过滤原因
  for (const auto& [k, v] : session_data_->mutable_targeting_data()->trace_stat.other) {
    if (v == 0) {
      continue;
    }
    if (session_data_->get_dot()) {
      session_data_->get_dot()->Count(
          v, "ad_filter",
          AdTraceFilterCondition_Name(static_cast<kuaishou::log::ad::AdTraceFilterCondition>(k)));
    }
  }
  return;
}

}  // namespace ad_target
}  // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, TraceLogMixer, ::ks::ad_target::TraceLogMixer);
