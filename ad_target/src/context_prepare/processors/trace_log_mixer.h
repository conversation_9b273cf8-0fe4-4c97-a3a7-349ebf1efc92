#pragma once
#include <string>
#include <list>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"

namespace ks {
namespace ad_target {
class ContextData;
class TraceLogMixer : public ks::platform::CommonRecoBaseMixer {
 public:
  TraceLogMixer() = default;
  bool InitProcessor() override;

  void Mix(ks::platform::AddibleRecoContextInterface* context) override;
  void OnPipelineExit(ks::platform::ReadableRecoContextInterface* context) override;

 public:
  void BuildContextDataAdList(ks::platform::ReadableRecoContextInterface* context);
  void BuildContextDataRetrievalAdList(ks::platform::ReadableRecoContextInterface* ctx);
  void BuildContextDataPrerankAdList(ks::platform::ReadableRecoContextInterface* ctx);
  void TraceWithoutPrerank(ks::platform::ReadableRecoContextInterface* context);

 private:
  std::string biz_;
  absl::flat_hash_map<std::string, std::string> multi_retrieval_ad_list_;
  absl::flat_hash_map<std::string, std::string> multi_prerank_ad_list_;

  std::vector<std::string> multi_retrieval_ad_list_vec_;
  std::vector<std::string> multi_prerank_ad_list_vec_;

  ContextData* session_data_ = nullptr;
  void ItemCardTrace(ks::platform::ReadableRecoContextInterface* context);
  bool is_item_card_ = false;
  bool enable_prerank_trace_ = false;;
  DISALLOW_COPY_AND_ASSIGN(TraceLogMixer);
};

}  // namespace ad_target
}  // namespace ks
