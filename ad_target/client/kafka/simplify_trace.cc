#include "teams/ad/ad_target/client/kafka/simplify_trace.h"

#include <algorithm>
#include <cctype>
#include <string>
#include <unordered_map>
#include <utility>
#include <memory>
#include <set>
#include <map>

#include "absl/strings/str_cat.h"
#include "base/time/timestamp.h"
#include "glog/logging.h"
#include "serving_base/utility/system_util.h"

#include "teams/ad/ad_base/src/kafka/kafka_client.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/pb_helper/pb_helper.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_base/src/common/utility.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Account.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Campaign.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Creative.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Unit.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/WTProduct.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/LiveStreamUserInfo.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_target/common/prerank_ad_list.h"
#include "teams/ad/ad_target/common/context_data.h"
#include "teams/ad/ad_target/common/retrieval_base_data.h"
#include "teams/ad/ad_target/client/kafka/trace_manager.h"
#include "teams/ad/ad_target/utils/kconf/kconf.h"
#include "teams/ad/ad_target/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"
#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_target/src/tsrm/processors/tsm_stat.h"

DECLARE_int32(ksp_group_deploy_type);
DECLARE_int32(trace_service_deploy_type);
DEFINE_bool(open_autotest_trace_log, false, "open autotest trace log");

namespace ks {
namespace ad_target {

using google::protobuf::Arena;
using ks::ad_base::DeployType;
using kuaishou::ad::AdTraceDspType;
using kuaishou::ad::ServiceDeployName;
using kuaishou::ad::TraceLogSamplingFlag;

AdEngineTraceType GetTraceType(const kuaishou::ad::TraceLogSamplingFlag &trace_sampling_flag,
                                                const int64 &user_id) {
  if (trace_sampling_flag == TraceLogSamplingFlag::SELECTIVE_SAMPLING &&
      (user_id % 1000 < AdKconfUtil::adTargetSimplifyTracePercent())) {
    return AdEngineTraceType::ALL_DATA;
  }
  if (trace_sampling_flag == TraceLogSamplingFlag::FORCE_SAMPLING) {
    return AdEngineTraceType::ALL_DATA;
  }
  return AdEngineTraceType::SKIP;
}

bool AdSimplifyTrace::IsSendTraceLogV2(const kuaishou::ad::AdRequest &ad_request) {
  if (ad_request.trace_log_sampling_flag_v2_table() != kuaishou::ad::TraceLogSamplingFlag::SKIP_SAMPLING) {
    return true;
  }
  return false;
}

void AdSimplifyTrace::StartTrace(ContextData &ctx) {
  if (ctx.get_ad_request() == nullptr) {
    return;
  }
  // 测试环境压测标为 ad_diff 时，trace_type=AdEngineTraceType::ALL_DATA
  trace_type = GetTraceType(ctx.get_ad_request()->trace_log_sampling_flag(), ctx.get_user_id());
  auto biz_name_set_ = AdKconfUtil::autoTestBizNameSet();
  std::string biz_name = \
  ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName();
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_autotest_trace_log \
      && biz_name_set_ && biz_name_set_->count(biz_name) > 0) {
    trace_type = AdEngineTraceType::ALL_DATA;
  }

  if (trace_type != AdEngineTraceType::SKIP && !GAdTraceManager::Instance().GetSimplifyTraceProducer()) {
    LOG(ERROR) << "init simplify trace sample partial log producer failed: ";
    trace_type = AdEngineTraceType::SKIP;
  }

  if (trace_type != AdEngineTraceType::SKIP) {
    log_ = Arena::CreateMessage<kuaishou::ad::AdTargetSimplifyTraceLog>(ctx.mutable_pb_arena());
  }
  if (IsSendTraceLogV2(*(ctx.get_ad_request()))) {
    log_v2_table_ = Arena::CreateMessage<kuaishou::ad::AdTargetSimplifyTraceLog>(ctx.mutable_pb_arena());
  }
}
void AdSimplifyTrace::InitBiz(const std::string& biz) {
  if (biz == tsm::kTsmSceneInnerSoftPhoto) {
    is_fanstop_ = true;
    is_amd_ = false;
    is_live_ = false;
    is_inner_loop_ = true;
  }

  if (biz == tsm::kTsmSceneInnerHardPhoto) {
    is_fanstop_ = false;
    is_amd_ = true;
    is_live_ = false;
    is_inner_loop_ = true;
  }
  if (biz == tsm::kTsmSceneLive) {
    is_fanstop_ = false;
    is_amd_ = false;
    is_live_ = true;
    is_inner_loop_ = true;
  }
  return;
}

void AdSimplifyTrace::EndTrace(ContextData &ctx, bool enable_move) {
  if (nullptr == log_ && nullptr == log_v2_table_) {
    return;
  }
  if (enable_move && AdKconfUtil::enableMovetraceToPrerankPerf() && log_) {
    std::map<int, int> stage_map;
    LOG_EVERY_N(INFO, 100) << "ShortDebugString: " << log_->ShortDebugString();
    for (const auto& iter : log_->target_stage_info().ad_item_info()) {
      stage_map[iter.ad_stage_type()]++;
    }
    for (auto [k, v] : stage_map) {
      ctx.get_dot()->Interval(v, "trace_stage", std::to_string(k));
    }
  }
  // 清理未通过审核的字段
  CleanExcludedField();
  // 测试环境压测标为 ad_diff 时,kafka 上报前置为空，然后再回写压测标
  ::ks::infra::kenv::RpcStressTestContext stress_test_ctx;
  auto trace_context = ::ks::infra::kenv::ServiceMeta::GetRpcTraceContext();
  auto biz_name_set_ = AdKconfUtil::autoTestBizNameSet();
  std::string biz_name = \
  ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName();
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && \
  FLAGS_open_autotest_trace_log && biz_name_set_ && biz_name_set_->count(biz_name) > 0) {
    stress_test_ctx.SetBizName("");
    trace_context.SetRpcStressTestCtx(stress_test_ctx);
    ::ks::infra::kenv::ServiceMeta::SetRpcTraceContext(trace_context);
  }
  if (nullptr != log_) {
    auto ts = log_->mutable_ad_request_base_info()->timestamp();
    std::string message_key = absl::StrCat(serving_base::GetHostName(), ts);
    std::string log;
    log_->SerializeToString(&log);
    if (GAdTraceManager::Instance().GetSimplifyTraceProducer()) {
      GAdTraceManager::Instance().GetSimplifyTraceProducer()->Produce(message_key, log);
    }
  }
  if (GAdTraceManager::Instance().GetSimplifyTraceV2Producer() && nullptr != log_v2_table_) {
    std::string log_v2;
    log_v2_table_->SerializeToString(&log_v2);
    auto ts = log_v2_table_->mutable_ad_request_base_info()->timestamp();
    std::string message_key = absl::StrCat(serving_base::GetHostName(), ts);
    GAdTraceManager::Instance().GetSimplifyTraceV2Producer()->Produce(message_key, log_v2);
  }
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() \
    && FLAGS_open_autotest_trace_log && biz_name_set_ && biz_name_set_->count(biz_name) > 0) {
    stress_test_ctx.SetBizName(biz_name);
    trace_context.SetRpcStressTestCtx(stress_test_ctx);
    ::ks::infra::kenv::ServiceMeta::SetRpcTraceContext(trace_context);
  }
  log_ = nullptr;
  log_v2_table_ = nullptr;
  trace_type = AdEngineTraceType::SKIP;
}

void AdSimplifyTrace::TraceAdResult(const RetrievalAdCommon &ad, const ContextData &ctx) {
  auto stage_type = (ad.strategy_base.filter_cond !=
                        kuaishou::log::ad::AdTraceFilterCondition::PRERANKING_SORT_FILTER)
                        ? kuaishou::ad::TargetStageInfo::RECALL
                        : kuaishou::ad::TargetStageInfo::PRERANKING;
  if (is_inner_loop_) {
    // 部分业务没有走粗排,特殊判断
    if (!ad.is_invalid()) {
      stage_type = kuaishou::ad::TargetStageInfo::RANKING;
    } else {
      stage_type = static_cast<kuaishou::ad::TargetStageInfo_AdStageType>(ad.others.stage_type);
    }
  }
  // 广告级别数据, 跳过或只采集 pv 数据时时不写入, 写 v1 表数据, 白盒用
  if (trace_type == AdEngineTraceType::ALL_DATA && nullptr != log_) {
    auto *item = log_->mutable_target_stage_info()->add_ad_item_info();
    TraceAdInfo(ad, stage_type, item, ctx);
  }
  // 写 v2 表数据, 数仓用
  if (nullptr != log_v2_table_) {
    auto *item = log_v2_table_->mutable_target_stage_info()->add_ad_item_info();
    TraceAdInfo(ad, stage_type, item, ctx);
  }
}

void AdSimplifyTrace::TraceAdResultMove(const RetrievalAdCommon &ad, const ContextData &ctx) {
  auto stage_type = kuaishou::ad::TargetStageInfo::RECALL;
  if (!ad.is_invalid()) {
    stage_type = kuaishou::ad::TargetStageInfo::POST_RECALL;
  }
  // 广告级别数据, 跳过或只采集 pv 数据时时不写入, 写 v1 表数据, 白盒用
  if (trace_type == AdEngineTraceType::ALL_DATA && nullptr != log_) {
    auto *item = log_->mutable_target_stage_info()->add_ad_item_info();
    TraceAdInfo(ad, stage_type, item, ctx);
  }
  // 写 v2 表数据, 数仓用
  if (nullptr != log_v2_table_) {
    auto *item = log_v2_table_->mutable_target_stage_info()->add_ad_item_info();
    TraceAdInfo(ad, stage_type, item, ctx);
  }
}

void AdSimplifyTrace::TracePrerankAd(const PrerankAdCommon &prerank_ad, const ContextData &ctx) {
  if (!prerank_ad.p_retrieval_ad) {
    LOG_EVERY_N(WARNING, 100) << "request_type " << ctx.get_pos_manager_base().GetAdRequestType()
                              << "preranking's retrieval ad is nullptr";
    return;
  }
  auto stage_type = prerank_ad.p_retrieval_ad->is_invalid() ? kuaishou::ad::TargetStageInfo::PRERANKING
                                                            : kuaishou::ad::TargetStageInfo::RANKING;

  // 广告级别数据, 跳过或只采集 pv 数据时时不写入，白盒用
  if (trace_type == AdEngineTraceType::ALL_DATA && nullptr != log_) {
    auto *item = log_->mutable_target_stage_info()->add_ad_item_info();
    TraceAdInfo(*prerank_ad.p_retrieval_ad, stage_type, item, ctx);
    TracePrerankAdInfo(prerank_ad, item, ctx);
  }
  // v2 表数据，发给数仓用
  if (nullptr != log_v2_table_) {
    auto *item = log_v2_table_->mutable_target_stage_info()->add_ad_item_info();
    TraceAdInfo(*prerank_ad.p_retrieval_ad, stage_type, item, ctx);
    TracePrerankAdInfo(prerank_ad, item, ctx);
  }
}

void AdSimplifyTrace::TraceFanstopPrerankAd(const RetrievalAdCommon& ad, const ContextData& ctx) {
  auto stage_type = kuaishou::ad::TargetStageInfo::UNKNOWN;
  if (!ad.is_invalid()) {
    stage_type = kuaishou::ad::TargetStageInfo::RANKING;
  } else if (ad.strategy_base.filter_cond ==
             kuaishou::log::ad::AdTraceFilterCondition::PRERANKING_SORT_FILTER) {
    stage_type = kuaishou::ad::TargetStageInfo::PRERANKING;
  } else {
    stage_type = kuaishou::ad::TargetStageInfo::RECALL;
  }
  // 广告级别数据, 跳过或只采集 pv 数据时时不写入, 白盒用
  if (trace_type == AdEngineTraceType::ALL_DATA && nullptr != log_) {
    auto *item = log_->mutable_target_stage_info()->add_ad_item_info();
    TraceAdInfo(ad, stage_type, item, ctx);
    TraceFanstopPrerankAdInfo(ad, item);
  }
  // 数仓用
  if (nullptr != log_v2_table_) {
    auto *item = log_v2_table_->mutable_target_stage_info()->add_ad_item_info();
    TraceAdInfo(ad, stage_type, item, ctx);
    TraceFanstopPrerankAdInfo(ad, item);
  }
}

void AdSimplifyTrace::TraceAdInfo(const RetrievalAdCommon &ad,
                                  const kuaishou::ad::TargetStageInfo::AdStageType stage_type,
                                  kuaishou::ad::TargetStageInfo::AdTraceItemInfo *item,
                                  const ContextData &ctx) {
  if (!item) {
    return;
  }

  auto *ad_base_info = item->mutable_ad_base_info();
  item->set_is_alive((stage_type == kuaishou::ad::TargetStageInfo::RANKING) ? true : !ad.is_invalid());
  if (stage_type == kuaishou::ad::TargetStageInfo::POST_RECALL) {
    item->set_is_alive(false);
  }
  ad_base_info->set_account_id(ad.account_id());
  ad_base_info->set_product_name(ad.product_name());
  ad_base_info->set_campaign_id(ad.campaign_id());
  ad_base_info->set_unit_id(ad.unit_id());
  ad_base_info->set_creative_id(ad.creative_id());
  ad_base_info->set_first_industry_id(ad.get_first_industry_id() >= 0 ? ad.get_first_industry_id() : 0);
  ad_base_info->set_new_creative_tag(ad.new_creative_tag());
  ad_base_info->set_item_type(ks::ad_base::GetItemType(ad.campaign_type(), ad.base.live_creative_type()));
  ad_base_info->set_multi_retrieval_tag(ad.strategy_base.multi_retrieval_tag);
  ad_base_info->set_campaign_type(ad.campaign_type());
  ad_base_info->set_target_id(ad.base.p_unit->target_id());
  ad_base_info->set_cover_id(ad.cover_id());
  if (ad.base.p_account) {
    ad_base_info->set_account_type(kuaishou::ad::AdEnum::AdDspAccountType(ad.base.p_account->account_type()));
  }
  if (ad.base.p_campaign) {
    ad_base_info->set_campaign_promotion_type(kuaishou::ad::AdEnum_CampaignPromotionType(
            ad.base.p_campaign->promotion_type()));
  }
  if (ad.IsFanstop()) {
    ad_base_info->set_ad_source_type(kuaishou::ad::FANS_TOP_V2);
  } else {
    ad_base_info->set_ad_source_type(kuaishou::ad::DSP);
  }
  // ad_base_info->set_adx_source_type(ad.adx_source_type());    // target 无

  if (is_fanstop_) {
    if (ad.is_inner_delivery()) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::INNER_FANSTOP_DSP_TYPE);
    } else if (ad.is_new_inner_delivery()) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::INNER_FANSTOP_V2_DSP_TYPE);
    } else if (ad.base.p_account && ad.base.p_account->account_type() == AdEnum::ACCOUNT_ESP_MOBILE) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::ESP_MOBILE_SOFT_DSP_TYPE);
    } else if (ad.IsEspFlashPromotion()) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::FLASH_PROMOTION_SOFT_DSP_TYPE);
    } else if (ad.IsEspSpecialtyPromotion()) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::SPECIALTY_PROMOTION_SOFT_DSP_TYPE);
    } else {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::NORMAL_FANSTOP_DSP_TYPE);
    }
  } else if (is_amd_ || is_live_) {
    if (ad.base.p_account && ad.base.p_account->account_type() == AdEnum::ACCOUNT_ESP_MOBILE) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::ESP_MOBILE_HARD_DSP_TYPE);
    } else if (ad.base.p_campaign
               && ad.base.p_campaign->promotion_type() == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::FLASH_PROMOTION_HARD_DSP_TYPE);
    } else if (ad.base.p_campaign
               && ad.base.p_campaign->promotion_type() == kuaishou::ad::AdEnum::SPECIALTY_PROMOTION) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::SPECIALTY_PROMOTION_HARD_DSP_TYPE);
    } else if (ad.base.p_account && ad.base.p_account->account_type() == AdEnum::ACCOUNT_FANSTOP_V2) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::NORMAL_FANSTOP_DSP_TYPE);
    } else if (ad.strategy_base.is_outer_loop_native && ad.is_outer_loop_ad()) {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::NORMAL_DSP_TYPE);
    } else {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::NORMAL_DSP_TYPE);
    }
  } else {
    if (ad.strategy_base.is_outer_loop_native) {
      // 外循环原生广告
      if (ad.strategy_base.queue_type == 2) {
        ad_base_info->set_ad_dsp_type(kuaishou::ad::AdTraceDspType::SOFT_AD_DSP_TYPE);
      } else {
        ad_base_info->set_ad_dsp_type(kuaishou::ad::AdTraceDspType::NATIVE_AD_DSP_TYPE);
      }
    } else {
      ad_base_info->set_ad_dsp_type(AdTraceDspType::NORMAL_DSP_TYPE);
    }
    ad_base_info->set_is_outer_loop_native(ad.strategy_base.is_outer_loop_native);
  }
  ad_base_info->set_ad_queue_type(GetAdQueueType(ad, ctx));
  ad_base_info->set_agent_id(ad.agent_id());

  if (ad.base.p_creative) {
    ad_base_info->set_photo_id(ad.base.p_creative->photo_id());
    ad_base_info->set_kol_user_type(ad.base.p_creative->kol_user_type());
  }
  if (ad.base.p_unit && ad.base.p_unit->has_merchant_small_shop_support_info_optional()) {
    ad_base_info->set_merchant_product_id(std::to_string(ad.base.p_unit->product_id()));
  }
  if (ad.base.p_wt_product) {
    ad_base_info->set_category_level_1_id(ad.base.p_wt_product->category_level_1_id());
    ad_base_info->set_category_level_2_id(ad.base.p_wt_product->category_level_2_id());
    ad_base_info->set_category_level_3_id(ad.base.p_wt_product->category_level_3_id());
  }


  if (ad.base.live_creative_type() != kuaishou::ad::AdEnum_LiveCreativeType_UNKNOWN_LIVE_CREATIVE_TYPE) {
    ad_base_info->set_live_stream_id(ad.live_stream_id());
    if (ad.base.p_live_stream_user_info) {
      int64_t live_start_ts = ad.base.p_live_stream_user_info->event_time();
      item->mutable_ad_extend_info()->set_live_start_gap(base::GetTimestamp() / 1000 - live_start_ts);
    }
  }

  ad_base_info->set_author_id(ad.author_id());
  if (ad.is_inner_loop_ad()) {
    for (int32_t tag : ad.crowd_tag) {
      ad_base_info->add_crowd_tag(tag);
    }
  }

  if (ad.is_invalid()) {
    item->mutable_ad_filter_info()->set_node_stage_type(ad.strategy_base.filter_node_type);
    item->mutable_ad_filter_info()->set_filter_reason(
        static_cast<kuaishou::log::ad::AdTraceFilterCondition>(ad.strategy_base.filter_cond));
  }

  // 如果命中扶持策略只会有一个大于 0
  int32_t strategy_tag = std::max(ad.strategy_base.ad_policy_tag, ad.strategy_base.ad_ecology_tag);
  item->mutable_strategy_info()->set_strategy_tag(strategy_tag);
  item->mutable_strategy_info()->set_pos_in_multi_tag(ad.others.rank_in_multi_tag);
  item->mutable_strategy_info()->set_pos_in_ranking(ad.others.pos_in_ranking);
  item->mutable_strategy_info()->set_ocpx_action_type(ad.ocpx_action_type());
  item->mutable_strategy_info()->set_retr_type(
      static_cast<kuaishou::ad::TargetStageInfo_RetrType>(ad.strategy_base.retr_type));
  item->mutable_ad_extend_info()->set_prerank_retarget_tag(ad.others.prerank_retarget_tag);
  item->mutable_ad_extend_info()->set_merchant_tag_recall_sid(ad.others.merchant_tag_recall_sid);
  item->mutable_ad_extend_info()->set_is_fan_follow(ad.others.is_fan_follow);
  item->mutable_ad_extend_info()->set_multi_overlay_tag(ad.strategy_base.multi_overlay_tag);
  item->mutable_ad_extend_info()->set_multi_overlay_tag_extend(ad.strategy_base.multi_overlay_tag_extend);
  item->mutable_ad_extend_info()->set_multi_overlay_tag_extend1(ad.strategy_base.multi_overlay_tag_extend1);
  item->mutable_ad_extend_info()->set_multi_overlay_tag_extend2(ad.strategy_base.multi_overlay_tag_extend2);
  item->mutable_ad_extend_info()->set_multi_overlay_tag_extend3(ad.strategy_base.multi_overlay_tag_extend3);
  item->mutable_ad_extend_info()->set_multi_overlay_tag_extend4(ad.strategy_base.multi_overlay_tag_extend4);
  item->mutable_ad_extend_info()->set_multi_overlay_tag_extend5(ad.strategy_base.multi_overlay_tag_extend5);
  item->mutable_ad_extend_info()->set_pred_score(ad.pred_score());
  item->mutable_ad_extend_info()->set_retrieval_post_score(ad.strategy_base.retrieval_post_score);
  item->mutable_ad_extend_info()->set_retrieval_rank_score(ad.strategy_base.retrieval_rank_score);
  item->mutable_ad_extend_info()->set_rta_source_type(ad.others.rta_source_type);
  item->mutable_ad_extend_info()->set_rta_trace_id(ad.others.rta_trace_request_id);
  item->mutable_ad_extend_info()->set_ecpm_ctr_score(ad.others.ecpm_ctr_score);
  item->mutable_ad_extend_info()->set_ecpm_cvr_score(ad.others.ecpm_cvr_score);
  item->mutable_ad_extend_info()->set_ad_strategy_tag(ad.strategy_base.ad_strategy_tag);
  item->mutable_ad_extend_info()->set_is_inner_rewarded_mingtou(ad.is_inner_rewarded_mingtou_ads());
  // 空字段，直接删除测试有 diff
  item->mutable_ad_extend_info()->set_before_swap_creative_id(0);
  item->mutable_ad_extend_info()->set_swap_reason(0);
  item->mutable_ad_extend_info()->set_explore_score(ad.others.explore_score);
  item->mutable_ad_extend_info()->set_ad_monitor_type(GetAdMonitorType(ad));
  item->mutable_ad_extend_info()->set_is_game_retarget_ad(ad.strategy_base.is_game_retarget_ad);
  item->mutable_ad_extend_info()->set_game_retarget_status(ad.strategy_base.game_retarget_status);
  item->mutable_ad_extend_info()->set_is_paid_fiction_retarget(ad.strategy_base.is_paid_fiction_retarget);
  item->mutable_ad_extend_info()->set_incentive_account_refund_rate(
      ad.strategy_base.incentive_account_refund_rate);
  item->mutable_ad_extend_info()->set_incentive_account_filter_rate(
      ad.strategy_base.incentive_account_filter_rate);
  item->set_ad_stage_type(stage_type);
  item->mutable_ad_extend_info()->set_sort_type(
      static_cast<kuaishou::ad::TargetStageInfo_AdExtendInfo_OneStepSortType>(ad.others.sort_type));
  if (ctx.get_enable_trace_tsm_for_ad()) {
    for (auto& channel : ad.channel_datas) {
      auto* model = item->mutable_ad_extend_info()->add_tsm_model_meta_list();
      model->set_cmd_key(channel.path->GetPathConfig().path_exp());
    }
  }
}

int AdSimplifyTrace::GetAdMonitorType(const RetrievalAdCommon &ad) {
  if (ad.is_inner_delivery()) {
    return kuaishou::ad::AdEnum_AdMonitorType_FANSTOP_INNER;
  } else if (ad.is_new_inner_delivery()) {
    return kuaishou::ad::AdEnum_AdMonitorType_FANSTOP_INNER_V2;
  }
  const auto &cct = static_cast<kuaishou::ad::tables::CreativeExtendScore_CreativeCirculationType>(
      ad.creative_circulation_type());
  if (cct == kuaishou::ad::tables::CreativeExtendScore_CreativeCirculationType_UNKNOWN_CIRCULATION_TYPE) {
    // unknown 默认外循环
    return kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT;
  }
  if (cct == kuaishou::ad::tables::CreativeExtendScore_CreativeCirculationType_INTERNAL_CIRCULATION_TYPE) {
    // 内循环
    return kuaishou::ad::AdEnum_AdMonitorType_INNER_LOOP_DEFAULT;
  }
  // 外循环
  return kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT;
}

void AdSimplifyTrace::TraceRequest(const ContextData &ctx) {
  // pv 级别数据, 跳过时不写入
  if (nullptr != log_) {
    TraceRequestCommon(log_->mutable_ad_request_base_info(), ctx);
  }
  if (nullptr != log_v2_table_) {
    TraceRequestCommon(log_v2_table_->mutable_ad_request_base_info(), ctx);
  }
}

void AdSimplifyTrace::TraceRequestCommon(kuaishou::ad::AdRequestBaseInfo *ad_request_base_info,
                                         const ContextData &ctx) {
  ad_request_base_info->set_user_id(ctx.get_user_id());
  ad_request_base_info->set_llsid(ctx.get_llsid());
  ad_request_base_info->set_page_id(ctx.get_page_id());
  ad_request_base_info->set_app_id(ctx.get_pos_manager_base().GetRequestAppId());
  ad_request_base_info->set_pos_id(
      ctx.get_pos_manager_base().request_imp_infos.empty() ?
        0 : ctx.get_pos_manager_base().request_imp_infos[0].pos_id);
  ad_request_base_info->set_sub_page_id(ctx.get_sub_page_id());
  ad_request_base_info->set_timestamp(ctx.get_target_trace_timestamp());

  ad_request_base_info->set_service_deploy_name(
      static_cast<kuaishou::ad::ServiceDeployName>(FLAGS_trace_service_deploy_type));
  ad_request_base_info->set_ad_request_flow_type(ctx.get_pos_manager_base().GetAdRequestType());
  ad_request_base_info->mutable_host_info()->set_service_stage(getenv("KWS_SERVICE_STAGE"));
  ad_request_base_info->mutable_host_info()->set_service_region(getenv("KWS_SERVICE_REGION"));
  ad_request_base_info->mutable_host_info()->set_service_az(getenv("KWS_SERVICE_AZ"));
  ad_request_base_info->mutable_host_info()->set_host_name(serving_base::GetHostName());
  if (AdKconfUtil::enableTargetRequestFlag()) {
    ad_request_base_info->set_is_target_request(true);
  }
  if (nullptr != ctx.get_ad_request()) {
    const auto *ad_request = ctx.get_ad_request();
    ad_request_base_info->set_ad_request_flow_type(ad_request->ad_request_flow_type());
    ad_request_base_info->set_platform(ad_request->ad_user_info().platform());
    ad_request_base_info->set_interactive_form(ad_request->interactive_form());
    ad_request_base_info->set_reco_client_browse_type(
        ad_request->reco_request_info().browse_type());
    ad_request_base_info->set_medium_attribute(ks::ad_base::GetMediumAttribute(*ad_request));
    ad_request_base_info->set_medium_uid(ad_request->universe_ad_request_info().medium_uid());
    ad_request_base_info->set_device_id(ad_request->ad_user_info().device_id());
    ad_request_base_info->set_age(ad_request->ad_user_info().age());
    ad_request_base_info->set_gender(ks::ad_base::GenderConvert(
        ad_request->ad_user_info().gender()));
    ad_request_base_info->set_trace_log_sampling_flag(ad_request->trace_log_sampling_flag());
  }
  {
    //  ad_request_extend
    auto *ad_request_extend = ad_request_base_info->mutable_ad_request_extend();
    ad_request_extend->set_is_flink_task_drop(
        ks::ad_base::IsEngineSendTraceLogV2(ctx.get_user_id(),
          ctx.get_ad_request()->ad_request_flow_type(), trace_type,
          ks::ad_base::AdEngineTraceServiceType::AdTargetServer));
    if (SPDM_enableTargetCacheRecord()) {
      auto& mutable_ctx = const_cast<ContextData&>(ctx);
      int32_t is_hit_cache_info = 0;
      if (mutable_ctx.BizPrerankCacheRef(tsm::kTsmSceneOuterPhoto).is_hit_cache) {
        is_hit_cache_info += 1;
      } else {
        if (mutable_ctx.BizPrerankCacheRef(tsm::kTsmSceneInnerHardPhoto).is_hit_cache) {
          is_hit_cache_info += 4;
        }
        if (mutable_ctx.BizPrerankCacheRef(tsm::kTsmSceneInnerSoftPhoto).is_hit_cache) {
          is_hit_cache_info += 2;
        }
      }
      ad_request_extend->set_is_hit_cache_info(is_hit_cache_info);
    }
  }

  ad_request_base_info->set_explore_strategy(
      static_cast<int>(kuaishou::ad::DEFAULT_EXPLORE_STRATEGY));
}

void AdSimplifyTrace::TraceCounter(const ContextData &ctx, int32_t ad_list_size) {
  // pv 级别数据, 跳过时不写入
  if (nullptr != log_) {
    TraceCounterCommon(log_->mutable_target_stage_info(), ctx, ad_list_size);
  }
  if (nullptr != log_v2_table_) {
    TraceCounterCommon(log_v2_table_->mutable_target_stage_info(), ctx, ad_list_size);
  }
}

void AdSimplifyTrace::TraceCounterCommon(kuaishou::ad::TargetStageInfo *stage_info,
                                         const ContextData &ctx,
                                         int32_t ad_list_size) {
#define COUNTER(node_type, val)                        \
  do {                                                 \
    auto *target_cnt = stage_info->add_node_counter(); \
    target_cnt->set_node_stage_type(node_type);        \
    target_cnt->set_ad_list_size(val);                 \
  } while (0)

  COUNTER(kuaishou::ad::AdTargetNodeType::INVERT_TARGET_TYPE, ctx.get_target_counter().avail_cnt);
  COUNTER(kuaishou::ad::AdTargetNodeType::FORWARD_SEARCH_TYPE, ctx.get_target_counter().expand);
  COUNTER(kuaishou::ad::AdTargetNodeType::RULE_FILTER_TYPE, ctx.get_target_counter().rule);
  COUNTER(kuaishou::ad::AdTargetNodeType::GET_TOP_ADS, ctx.get_target_counter().rank);

  stage_info->set_ad_list_num(ad_list_size);
  if (SPDM_enable_prerank_append_exp_trace(ctx.get_spdm_ctx())) {
    for (int i = 0; i < ctx.get_preranking_info_params().auto_param_info.exp_id_size(); i++) {
      stage_info->add_auto_param_exp_id(ctx.get_preranking_info_params().auto_param_info.exp_id(i));
    }
  }
}

void AdSimplifyTrace::TraceMultiCounter(const ContextData &ctx) {
  // 广告级别数据, 只采集 pv 数据时不写入
  if (trace_type == AdEngineTraceType::ALL_DATA && log_ != nullptr) {
    TraceMultiCounterCommon(log_->mutable_target_stage_info(), ctx);
  }
  if (nullptr != log_v2_table_) {
    TraceMultiCounterCommon(log_v2_table_->mutable_target_stage_info(), ctx);
  }
}

void AdSimplifyTrace::TraceMultiCounterCommon(kuaishou::ad::TargetStageInfo *stage_info,
                                              const ContextData &ctx) {
  auto cmd_map = AdKconfUtil::model_cmd();

  for (auto* path : ctx.get_multi_path_context().path_list) {
    auto &param = path->GetPathConfig();
    auto *counter = stage_info->add_multi_counter();
    counter->set_tag_id(param.PathId());
    counter->set_search_num(param.TriggerTokenMaxQuota());
    counter->set_quota(param.MergeCreativeMaxQuota());
    counter->set_intersect_result_size(path->ResultSize());
    counter->set_request_type(param.RankerType());
    counter->set_recall_candidate_size(path->token_list.size());
    if (cmd_map && cmd_map->count(param.cmd())) {
      counter->set_cmd_id((*cmd_map)[param.cmd()]);
    } else {
      counter->set_cmd_id(-1);
    }
  }
  if (ctx.get_enable_trace_tsm()) {
    auto& mutable_ctx = const_cast<ContextData&>(ctx);
    for (auto& [path, info] : mutable_ctx.get_tsm_path_stat()) {
      stage_info->add_tsm_path_stat()->Swap(info);
    }
    mutable_ctx.get_dot()->Interval(mutable_ctx.get_tsm_path_stat().size(), "tsm_path_stat_size");
  }

  for (auto& [k, v] : ctx.get_tsm_trigger_stat()) {
    auto* recall_stat = stage_info->add_recall_stat();
    recall_stat->set_stage(kuaishou::ad::TargetStageInfo::TRIGGER);
    recall_stat->set_cmd_key(k.cmd_key);
    recall_stat->set_cmd(k.cmd);
    recall_stat->set_quota(0);
    recall_stat->set_item_num(v);
  }

  for (auto& [k, v] : ctx.get_tsm_searcher_stat()) {
    auto* recall_stat = stage_info->add_recall_stat();
    recall_stat->set_stage(kuaishou::ad::TargetStageInfo::SEARCHER);
    recall_stat->set_cmd_key(k.cmd_key);
    recall_stat->set_cmd(k.cmd);
    recall_stat->set_quota(0);
    recall_stat->set_item_num(v);
  }

  for (auto& [k, v] : ctx.get_tsm_merger_stat()) {
    auto* recall_stat = stage_info->add_recall_stat();
    recall_stat->set_stage(kuaishou::ad::TargetStageInfo::MERGER);
    recall_stat->set_cmd_key(k.cmd_key);
    recall_stat->set_cmd(k.cmd);
    recall_stat->set_quota(0);
    recall_stat->set_item_num(v);
  }
}

void AdSimplifyTrace::TraceGraphCost(int64_t start_ts,
                                     const std::unordered_map<std::string, int64_t> &graph_cost) {
  if (nullptr != log_) {
    TraceGraphCostCommon(log_->mutable_ad_request_base_info(), start_ts, graph_cost);
  }
  if (nullptr != log_v2_table_) {
    TraceGraphCostCommon(log_v2_table_->mutable_ad_request_base_info(), start_ts, graph_cost);
  }
}

void AdSimplifyTrace::TraceGraphCostCommon(kuaishou::ad::AdRequestBaseInfo *ad_request_base_info,
                                           int64_t start_ts,
                                           const std::unordered_map<std::string, int64_t> &graph_cost) {
  std::string node_name;
  kuaishou::ad::AdTargetNodeType node_type;
  for (const auto &p_node_cost : graph_cost) {
    node_name = p_node_cost.first;
    std::transform(node_name.begin(), node_name.end(), node_name.begin(), ::toupper);
    node_name = absl::StrCat(node_name, "_TYPE");

    if (AdTargetNodeType_Parse(node_name, &node_type)) {
      auto *cost = ad_request_base_info->mutable_perf_info()->add_node_cost();
      cost->set_node_type(static_cast<int32_t>(node_type));
      cost->set_time_cost_ms(p_node_cost.second);
    }
  }
  ad_request_base_info->mutable_perf_info()->set_server_time_cost_ms((base::GetTimestamp() - start_ts) /
                                                                     1000);
}

// 从 ad_server 迁移过来
void AdSimplifyTrace::TracePrerankAdInfo(const PrerankAdCommon& prerank_ad,
                                         kuaishou::ad::TargetStageInfo::AdTraceItemInfo* trace_ad,
                                         const ContextData& ctx) {
  if (trace_ad == nullptr) {
    LOG(ERROR) << "trace_ad is nullptr";
    return;
  }

  auto *p_trace_prerank = trace_ad->mutable_preranking_info();
  // bid
  auto *p_bid = p_trace_prerank->mutable_bid_info();
  const auto &bid_info = prerank_ad.bid_info;
  p_bid->set_auto_cpa_bid(bid_info.GetAutoCpaBid());
  p_bid->set_cpa_bid(prerank_ad.cpa_bid());
  p_bid->set_auto_roas(bid_info.GetAutoRoas());
  p_bid->set_roi_ratio(prerank_ad.roi_ratio());
  p_bid->set_bid(prerank_ad.bid());
  p_bid->set_auto_deep_cpa_bid(bid_info.auto_deep_cpa_bid);
  p_bid->set_deep_cpa_bid(prerank_ad.deep_cpa_bid());
  p_bid->set_bid_type(static_cast<kuaishou::ad::AdEnum_BidType>(prerank_ad.bid_type()));
  p_bid->set_ocpc_action_type(prerank_ad.ocpx_action_type());
  p_bid->set_deep_conversion_type(prerank_ad.deep_conversion_type());
  p_bid->set_ocpc_bid_modify_tag(bid_info.GetAutoCpaBidModifyTag());
  p_bid->set_speed_type(static_cast<kuaishou::ad::AdEnum::SpeedType>(prerank_ad.speed()));
  // ad_cxr
  static int64_t cxr_ratio = 1000000;
  auto *ad_cxr_info = p_trace_prerank->mutable_ad_cxr_info();
  ad_cxr_info->set_ocpc_action_type(prerank_ad.ocpx_action_type());
  ad_cxr_info->set_ecpm_ctr(0.0);
  ad_cxr_info->set_ecpm_cvr(0.0);
  ad_cxr_info->set_unify_ctr(prerank_ad.prerank.unify_ctr * cxr_ratio);
  ad_cxr_info->set_unify_cvr(prerank_ad.prerank.unify_cvr * cxr_ratio);
  ad_cxr_info->set_unify_deep_cvr(prerank_ad.prerank.unify_deep_cvr * cxr_ratio);
  ad_cxr_info->set_prerank_cpm_ltr(prerank_ad.prerank.prerank_cpm_ltr * cxr_ratio);
  ad_cxr_info->set_prerank_ecpm(prerank_ad.prerank.ecpm);
  ad_cxr_info->set_prerank_delivery_rate(prerank_ad.prerank.delivery_rate * cxr_ratio);
  ad_cxr_info->set_prerank_ensemble_score(prerank_ad.prerank.ensemble_score * cxr_ratio);
  ad_cxr_info->set_prerank_ctcvr_score(prerank_ad.prerank.prerank_ctcvr_score);
  ad_cxr_info->set_prerank_ctcvr(prerank_ad.prerank.prerank_ctcvr);
  ad_cxr_info->set_neg_rate(prerank_ad.prerank.neg_rate);
  ad_cxr_info->set_prerank_real_action(prerank_ad.prerank.prerank_real_action_score);
  ad_cxr_info->set_prerank_gpm(prerank_ad.prerank.prerank_gpm);
  ad_cxr_info->set_prerank_mix_ltr_score(prerank_ad.prerank.prerank_mix_ltr_score);

  // strategy_info
  auto *ad_strategy_info = p_trace_prerank->mutable_strategy_info();
  auto *main_strategy_info = ad_strategy_info->mutable_main_strategy();
  main_strategy_info->set_from_tag(prerank_ad.main_strategy_info.tag);
  main_strategy_info->set_pos(prerank_ad.main_strategy_info.index);
  main_strategy_info->set_score(prerank_ad.main_strategy_info.score);
  if (kuaishou::ad::PrerankTagType_IsValid(prerank_ad.main_strategy_info.tag)) {
    main_strategy_info->set_prerank_tag(
        static_cast<kuaishou::ad::PrerankTagType>(prerank_ad.main_strategy_info.tag));
  }

  auto append_strategy_info = ad_strategy_info->mutable_support_strategy();
  append_strategy_info->set_from_group(prerank_ad.append_strategy_info.append_group);
  append_strategy_info->set_from_tag(prerank_ad.append_strategy_info.append_tag);
  append_strategy_info->set_sort_method_tag(prerank_ad.append_strategy_info.sort_tag);
  append_strategy_info->set_pos(prerank_ad.append_strategy_info.index);
  append_strategy_info->set_score(prerank_ad.append_strategy_info.score);
  if (kuaishou::ad::PrerankAppendGroupType_IsValid(prerank_ad.append_strategy_info.append_group)) {
    append_strategy_info->set_group_type(
        static_cast<kuaishou::ad::PrerankAppendGroupType>(prerank_ad.append_strategy_info.append_group));
  }
  // 目前只有主路里有该序，爬坡无该序
  ad_strategy_info->set_pos_in_prerank(prerank_ad.prerank.pos_in_prerank);
  // 粗排相关 extend info
  auto *ad_extend_info = trace_ad->mutable_ad_extend_info();
  ad_extend_info->set_delivery_rate(prerank_ad.prerank.delivery_rate);
  ad_extend_info->set_prerank_score(prerank_ad.prerank.score);
  ad_extend_info->set_prerank_ecpm(prerank_ad.prerank.ecpm);
  ad_extend_info->set_prerank_ctcvr(prerank_ad.prerank.prerank_ctcvr);
  ad_extend_info->set_rta_bid_ratio(prerank_ad.rta_ratio());
  ad_extend_info->set_rta_sta_tag(prerank_ad.rta_sta_tag());
  ad_extend_info->set_rta_bid(prerank_ad.rta_bid());
  ad_extend_info->set_bonus_rate(0.0);
  ad_extend_info->set_delivery_pred_score(prerank_ad.delivery_pred_score());
  ad_extend_info->set_retrieval_ecpm(prerank_ad.retrieval_ecpm());
  ad_extend_info->set_ecpm_idx(prerank_ad.prerank.ecpm_idx);
  ad_extend_info->set_e2e_idx(prerank_ad.prerank.e2e_idx);
  ad_extend_info->set_cpm_ltr_idx(prerank_ad.prerank.cpm_ltr_idx);
  ad_extend_info->set_neg_idx(prerank_ad.prerank.neg_idx);
  ad_extend_info->set_real_action_idx(prerank_ad.prerank.real_action_idx);
  ad_extend_info->set_gpm_idx(prerank_ad.prerank.gpm_idx);
  if (prerank_ad.p_retrieval_ad) {
    FillModelMeta(*prerank_ad.p_retrieval_ad, trace_ad);
    for (auto& skip_info : prerank_ad.p_retrieval_ad->flow_control_skip_info) {
      auto *flow_control_skip_info = ad_extend_info->add_flow_control_skip_info();
      flow_control_skip_info->set_flow_control_type(skip_info.flow_control_type());
      flow_control_skip_info->set_flow_control_skip_type(skip_info.flow_control_skip_type());
    }
  }
  return;
}

void AdSimplifyTrace::FillModelMeta(const RetrievalAdCommon& ad,
                                    kuaishou::ad::TargetStageInfo::AdTraceItemInfo* trace_ad) {
  auto* ad_extend_info = trace_ad->mutable_ad_extend_info();
  for (auto& cmd : ad.cmd_key2cmd_id_list) {
    auto* meta = ad_extend_info->add_model_meta_list();
    meta->set_cmd_key_id(cmd.first);
    meta->set_cmd_id(cmd.second);
  }
}

// 从 ad_server 迁移过来
void AdSimplifyTrace::TraceFanstopPrerankAdInfo(const RetrievalAdCommon& ad,
                  kuaishou::ad::TargetStageInfo::AdTraceItemInfo* trace_ad) {
  if (trace_ad == nullptr) {
    LOG(ERROR) << "trace_ad is nullptr";
    return;
  }

  auto *p_trace_prerank = trace_ad->mutable_preranking_info();
  // bid
  auto *p_bid = p_trace_prerank->mutable_bid_info();
  const auto& bid_info = ad.bid_info;
  p_bid->set_auto_cpa_bid(bid_info.GetAutoCpaBid());
  p_bid->set_cpa_bid(ad.cpa_bid());
  p_bid->set_roi_ratio(ad.roi_ratio());
  p_bid->set_auto_roas(bid_info.GetAutoRoas());
  p_bid->set_bid(ad.bid());
  p_bid->set_bid_type(static_cast<kuaishou::ad::AdEnum_BidType>(ad.bid_type()));
  p_bid->set_ocpc_action_type(ad.ocpx_action_type());
  p_bid->set_ocpc_bid_modify_tag(bid_info.GetAutoCpaBidModifyTag());
  p_bid->set_speed_type(static_cast<kuaishou::ad::AdEnum::SpeedType>(ad.base.p_unit->speed()));
  // ad_cxr
  static int64_t cxr_ratio = 1000000;
  auto *ad_cxr_info = p_trace_prerank->mutable_ad_cxr_info();
  ad_cxr_info->set_ocpc_action_type(ad.ocpx_action_type());
  ad_cxr_info->set_ecpm_ctr(ad.others.prerank_ctr * cxr_ratio);
  ad_cxr_info->set_unify_ctr(ad.others.prerank_ctr * cxr_ratio);
  ad_cxr_info->set_prerank_ensemble_score(ad.others.ensemble_score * cxr_ratio);
  ad_cxr_info->set_prerank_cpm_ltr(ad.others.prerank_cpm_ltr * cxr_ratio);
  ad_cxr_info->set_prerank_gpm(ad.others.prerank_gpm);
  ad_cxr_info->set_prerank_mix_ltr_score(ad.others.prerank_mix_ltr_score);
  // 粗排相关 extend info
  auto* ad_extend_info = trace_ad->mutable_ad_extend_info();
  ad_extend_info->set_delivery_rate(ad.others.delivery_rate);
  ad_extend_info->set_prerank_score(ad.others.prerank_score);
  ad_extend_info->set_prerank_ecpm(ad.others.prerank_ecpm);
  ad_extend_info->set_prerank_ctcvr(ad.others.prerank_ctcvr);
  ad_extend_info->set_ecpm_idx(ad.others.ecpm_idx);
  ad_extend_info->set_e2e_idx(ad.others.e2e_idx);
  ad_extend_info->set_gpm_idx(ad.others.gpm_idx);

  FillModelMeta(ad, trace_ad);

  return;
}

void AdSimplifyTrace::CleanExcludedField() {
  auto white_list_kconf = AdKconfUtil::targetTraceLogAdExtendInfoWhiteList();
  absl::flat_hash_set<std::string> white_list(white_list_kconf->begin(), white_list_kconf->end());
  auto is_invalid_field = [&](const std::string& name) -> bool { return white_list.count(name) == 0; };

  if (nullptr != log_) {
    auto *item_info = log_->mutable_target_stage_info()->mutable_ad_item_info();
    auto item_iter = item_info->begin();
    for (; item_iter != item_info->end(); ++item_iter) {
      ks::ad_base::Proto::CleanFiledByFun(item_iter->mutable_ad_extend_info(), is_invalid_field);
    }
  }
  if (nullptr != log_v2_table_) {
    auto *item_info = log_v2_table_->mutable_target_stage_info()->mutable_ad_item_info();
    auto item_iter = item_info->begin();
    for (; item_iter != item_info->end(); ++item_iter) {
      ks::ad_base::Proto::CleanFiledByFun(item_iter->mutable_ad_extend_info(), is_invalid_field);
    }
  }
}

kuaishou::ad::AdEnum::AdQueueType AdSimplifyTrace::GetAdQueueType(const RetrievalAdCommon &ad,
                                                                  const ContextData &ctx) const {
  // 默认硬广
  kuaishou::ad::AdEnum_AdQueueType ad_queue_type = kuaishou::ad::AdEnum::HARD_AD_QUEUE;
  if (is_fanstop_) {
    ad_queue_type = kuaishou::ad::AdEnum::SOFT_AD_QUEUE;
    if (ad.is_inner_delivery()) {
      ad_queue_type = kuaishou::ad::AdEnum::INNER_FANSTOP;
    }
  } else if (is_amd_) {
    if (ctx.IsFollowTraffic()) {
      ad_queue_type = kuaishou::ad::AdEnum::SOFT_AD_QUEUE;
    }
  } else if (ad.others.ad_queue_type == 2) {  // 外循环软广 photo
    ad_queue_type = kuaishou::ad::AdEnum::SOFT_AD_QUEUE;
  }
  return ad_queue_type;
}

}  // namespace ad_target
}  // namespace ks
