#pragma once
#include <string>
#include "teams/ad/query_retrieval/src/engine/context_data/context_data.h"
#include "teams/ad/query_retrieval/src/engine/server/init.h"
#include "teams/ad/query_retrieval/src/engine/utility/redis/async_redis_helper.h"
#include "teams/ad/query_retrieval/src/engine/utility/redis/async_redis_params.h"
namespace ks {
namespace query_retrieval {

class QueryInfoCollection final : public AdAsyncNode, public AdNodeItemBase<QueryInfoCollection> {
 public:
  explicit QueryInfoCollection(AdContext& context);  // NOLINT
  ~QueryInfoCollection();

  static constexpr AdNodeType class_name() { return AdNodeType::QueryInfoCollectionType; }

 protected:
  bool Prepare();
  bool ProcessInner() override;
  bool WaitInner() override;
  void Clear() override;

 private:
  ContextData* session_data_{nullptr};  // 请求上下文
  AsyncRedisHelper async_redis_helper_;
  kuaishou::ad::QueryParserResponse* ad_response_;
  SearchQueryUserList search_query_user_list_;
  SearchLiveQueryTag search_live_query_tag_;
  SearchLiveTabWhiteList search_live_tab_white_list_;
  SearchLiveBigVCard search_live_big_v_card_;
  SearchLiveBigVCardDirect search_live_big_v_card_direct_;
  SearchUserEmbedding search_user_embedding_;
  SearchQueryFeature search_query_feature_;
  SearchQueryCategory search_query_category_;
  SearchQueryGoodsIntent search_query_goods_intent_;
  SearchQueryIndustryId seacrh_query_industry_id_;
  SearchQueryFeatureCount seacrh_query_feature_count_;
  AppCardQuery app_card_query_;
  AppCardPlatformQuery app_card_platform_query_;
  SearchQueryFaceAuthor search_query_face_author_;
  FormCardExactQuery form_card_exact_query_;
  FormCardBroadQuery form_card_broad_query_;
  FormInnerPlayletQuery form_inner_playlet_query_;
  FormDpaStrongCard dpa_form_strong_card_;
  AppDpaStrongCard dpa_strong_card_;
  QueryCategory query_category_;
  SearchAkgQueryEmbedding search_akg_query_embedding_;
  SearchLiveBigVCardSug search_live_big_v_card_sug_;
  AppCardQuerySug app_card_query_sug_;
  FormCardQuerySug form_card_query_sug_;
  SearchLiveBigVCardWithType search_live_big_v_card_with_type_;
  SearchGoodsQueryRewrite search_goods_query_rewrite_;
  SearchKboxQuery search_kbox_query_;
  SearchAigcProduct search_aigc_product_;
  SearchQueryHotSequenceFeature search_query_hot_sequence_feature_;
  OriQueryQuantizeId ori_query_quantize_id_;
  SearchSingleColQuery search_single_col_query_;
  SearchDoubleColUser search_double_col_user_;
  GoodsBigvQuery goods_bigv_query_;
  std::string query;
  std::string sug_query;
  bool enable_sug_bigv_style;
  bool enable_sug_app_style;
  bool enable_sug_submit_style;
  bool enable_submit_exact_broad;
  bool enable_bigv_style;
  bool enable_bigv_style_with_type = false;
  bool enable_bigv_style_direct = false;
  bool enable_app_style;
  bool enable_submit_style;
  bool enable_goods_query_rewrite = false;
  bool enable_form_inner_playlet_query = false;
  bool enable_dpa_strong_card = false;
  bool enable_dpa_form_strong_card = false;
  bool enable_aigc_product = false;
  bool enable_aigc_product_form = false;
  bool enable_all_app_card_exact = false;
  bool enable_ori_query_quantize_id = false;
  bool enable_search_prefer_single_ads = false;
  bool enable_search_goods_bigv_query = false;

  void FillSearchLiveQueryTag();
  void FillSearchQueryUserList();
  void FillSearchLiveTabWhiteList();
  void FillBigVCardInfo();
  void FillQueryEmbeddingInfo();
  void FillSearchQueryCategory();
  void FillQueryCategory();
  void FillSearchQueryFeature();
  void FillSearchQueryGoodsIntent();
  void FillSearchQueryIndustryId();
  void FillSearchQueryFeatureCount();
  void FillAppCardQuery();
  void FillSearchQueryFaceAuthor();
  void FillFormCardQuery();
  void FillSeriesCardQuery();
  void FillSearchAkgQueryEmbedding();
  void AddBigVCardFromSug();
  void FillRealtimeActionList();
  void FillSugOriginQuery();
  void FillBigVCardInfoSug();
  void FillAppCardQuerySug();
  void FillFormCardQuerySug();
  void FillQueryAdDiversionInfo();
  void FillBigVCardInfoWithType();
  void FillGoodsQueryRewrite();
  void FillSearchKboxQuery();
  void FillAigcProduct();
  void FillSearchQueryHotSequenceFeature();
  void FillOriQueryQuantizeId();
  void FillSearchPreferSingleCol();
};

}  // namespace query_retrieval
}  // namespace ks
