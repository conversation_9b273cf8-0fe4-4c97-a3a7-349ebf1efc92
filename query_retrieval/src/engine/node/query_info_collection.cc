#include "teams/ad/query_retrieval/src/engine/node/query_info_collection.h"

#include <algorithm>
#include <functional>
#include <set>
#include <string>
#include <vector>
#include <unordered_set>

#include "base/common/logging.h"
#include "base/encoding/base64.h"
#include "base/strings/string_number_conversions.h"
#include "infra/utility/src/utility/encrypted_id_cipher.h"
#include "ks/base/abtest/abtest_instance.h"
#include "teams/ad/ad_base/src/pb_helper/pb_helper.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/search_ads/query_user_celebrity.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/search_ads/search_query_feature_info.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/search_ads/query_photo.pb.h"
#include "teams/ad/ad_proto/kuaishou/search/search_client_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/search_ads/query_extend.pb.h"
#include "teams/ad/query_retrieval/src/engine/utility/query_diversion_decode.h"
#include "teams/ad/query_retrieval/src/engine/utility/utilty.h"
#include "teams/ad/query_retrieval/src/util/kconf/kconf.h"
#include "teams/ad/query_retrieval/src/util/perf.h"
#include "teams/ad/query_retrieval/src/util/scope_time_recorder.h"
#include "teams/ad/query_retrieval/src/util/spdm/spdm_switches.h"

using kuaishou::ad::SearchAdDiversionInfo;
using kuaishou::ad::DpaStrongCardInfo;
using kuaishou::ad::QueryHotSequenceInfo;
using ::kuaishou::ad::search_ads::QueryExtendList;
using ::kuaishou::ad::search_ads::PlayletItem;

namespace ks {
namespace query_retrieval {

QueryInfoCollection::QueryInfoCollection(AdContext& context) : AdAsyncNode(context, "query_extend") {}

QueryInfoCollection::~QueryInfoCollection() { Clear(); }

void QueryInfoCollection::Clear() {
  session_data_ = nullptr;
  enable_sug_bigv_style = false;
  enable_sug_app_style = false;
  enable_sug_submit_style = false;
  enable_submit_exact_broad = false;
  enable_bigv_style = true;
  enable_bigv_style_with_type = false;
  enable_app_style = true;
  enable_submit_style = true;
  enable_goods_query_rewrite = false;
  enable_form_inner_playlet_query = false;
  enable_dpa_strong_card = false;
  enable_dpa_form_strong_card = false;
  enable_aigc_product = false;
  enable_aigc_product_form = false;
  enable_all_app_card_exact = false;
  enable_ori_query_quantize_id = false;
  enable_search_prefer_single_ads = false;
  enable_search_goods_bigv_query = false;
  sug_query.clear();
  query.clear();
}

bool QueryInfoCollection::Prepare() {
  session_data_ = context_->GetMutableContextData<ContextData>();
  ad_response_ = context_->GetRPCResponse<kuaishou::ad::QueryParserResponse>();
  if (!session_data_) {
    LOG(ERROR) << "session_data_ is nullptr!";
    return false;
  }

  if (!session_data_->ad_request) {
    LOG(ERROR) << "ad_request is nullptr!";
    return false;
  }
  sug_query = session_data_->sug_origin_query;
  transform(sug_query.begin(), sug_query.end(), sug_query.begin(), ::tolower);
  query = session_data_->ad_request->search_info().query();
  transform(query.begin(), query.end(), query.begin(), ::tolower);

  enable_bigv_style = (AdKconfUtil::bigVCardBlackQueryList()->count(query) == 0);
  enable_app_style = (AdKconfUtil::appcardBlackQueryList()->count(query) == 0);
  enable_submit_style = (AdKconfUtil::formCardBlackQueryList()->count(query) == 0);
  enable_all_app_card_exact = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_all_app_card_exact",
                          session_data_->abtest_user_info,
                          false);
  enable_sug_bigv_style = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_sug_bigv_style",
                          session_data_->abtest_user_info,
                          false) &&
                          (AdKconfUtil::bigVCardBlackQueryList()->count(sug_query) == 0);
  enable_sug_app_style = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_sug_app_style",
                          session_data_->abtest_user_info,
                          false) &&
                          (AdKconfUtil::appcardBlackQueryList()->count(sug_query) == 0);
  enable_sug_submit_style = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_sug_submit_style",
                          session_data_->abtest_user_info,
                          false) &&(AdKconfUtil::formCardBlackQueryList()->count(sug_query) == 0);
  enable_submit_exact_broad = AdKconfUtil::enableNewStrongCardRel() ?
                          ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_form_card_exact_broad", session_data_->abtest_user_info, false) :
                          ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_submit_exact_broad", session_data_->abtest_user_info, false);
  enable_bigv_style_with_type = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_bigv_style_with_type",
                          session_data_->abtest_user_info,
                          false);
  enable_bigv_style_direct = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_bigv_style_direct",
                          session_data_->abtest_user_info,
                          false);
  enable_bigv_style_with_type = enable_bigv_style_with_type && enable_bigv_style;
  enable_goods_query_rewrite = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_goods_query_rewrite",
                          session_data_->abtest_user_info,
                          false);
  enable_form_inner_playlet_query = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_form_inner_playlet_query",
                          session_data_->abtest_user_info,
                          false);
  enable_dpa_strong_card = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_dpa_strong_card",
                          session_data_->abtest_user_info,
                          false);
  enable_dpa_form_strong_card = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_dpa_form_strong_card",
                          session_data_->abtest_user_info,
                          false);
  enable_aigc_product = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_aigc_product",
                          session_data_->abtest_user_info,
                          false);
  enable_aigc_product_form = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_aigc_product_form",
                          session_data_->abtest_user_info,
                          false);
  enable_ori_query_quantize_id = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_ori_query_quantize_id",
                          session_data_->abtest_user_info,
                          false);
  enable_search_prefer_single_ads = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_search_prefer_single_ads",
                          session_data_->abtest_user_info,
                          false) && (session_data_->ad_request->search_info().search_source() ==
          kuaishou::ad::SearchInfo::ALADDIN_COMBO_SEARCH);
  enable_search_goods_bigv_query = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                          "enable_search_goods_bigv_query",
                          session_data_->abtest_user_info,
                          true) && (session_data_->ad_request->search_info().search_source() ==
          kuaishou::ad::SearchInfo::GOODS_SEARCH);
  return true;
}

bool QueryInfoCollection::ProcessInner() {
  if (!AdKconfUtil::enableQueryInfoCollection()) {
    return true;
  }
  if (!Prepare()) {
    return true;
  }

  PROCESS_TIME_RECORD;
  // 直播 Tab 白名单
  search_live_tab_white_list_.Init(session_data_);
  async_redis_helper_.AsyncGet(&search_live_tab_white_list_);

  // 大 V 卡
  if (enable_bigv_style) {
    if (!enable_bigv_style_direct) {
      search_live_big_v_card_.Init(session_data_);
      async_redis_helper_.AsyncGet(&search_live_big_v_card_);
    } else {
      search_live_big_v_card_direct_.Init(session_data_);
      async_redis_helper_.AsyncGet(&search_live_big_v_card_direct_);
    }
  }
  if (enable_bigv_style_with_type) {
    search_live_big_v_card_with_type_.Init(session_data_);
    async_redis_helper_.AsyncGet(&search_live_big_v_card_with_type_);
  }
  if (enable_aigc_product) {
    search_aigc_product_.Init(session_data_);
    async_redis_helper_.AsyncGet(&search_aigc_product_);
  }

  // query embedding
  search_user_embedding_.Init(session_data_);
  async_redis_helper_.AsyncGet(&search_user_embedding_);

  // query feature
  search_query_feature_.Init(session_data_);
  async_redis_helper_.AsyncGet(&search_query_feature_);

  // query category
  search_query_category_.Init(session_data_);
  async_redis_helper_.AsyncGet(&search_query_category_);

  // query goods intent
  search_query_goods_intent_.Init(session_data_);
  async_redis_helper_.AsyncGet(&search_query_goods_intent_);

  // query industry
  seacrh_query_industry_id_.Init(session_data_);
  async_redis_helper_.AsyncGet(&seacrh_query_industry_id_);

  seacrh_query_feature_count_.Init(session_data_);
  async_redis_helper_.AsyncGet(&seacrh_query_feature_count_);

  query_category_.Init(session_data_);
  async_redis_helper_.AsyncGet(&query_category_);

  search_query_hot_sequence_feature_.Init(session_data_);
  async_redis_helper_.AsyncGet(&search_query_hot_sequence_feature_);

  if (enable_ori_query_quantize_id) {
    ori_query_quantize_id_.Init(session_data_);
    async_redis_helper_.AsyncGet(&ori_query_quantize_id_);
  }

  if (enable_app_style) {
    // download query product name
    app_card_query_.Init(session_data_);
    async_redis_helper_.AsyncGet(&app_card_query_);

    // download platform query product name
    app_card_platform_query_.Init(session_data_);
    async_redis_helper_.AsyncGet(&app_card_platform_query_);


    if (enable_dpa_strong_card) {
      dpa_strong_card_.Init(session_data_);
      async_redis_helper_.AsyncGet(&dpa_strong_card_);
    }
  }

  // form query product name
  if (enable_submit_style) {
    if (enable_submit_exact_broad) {
      form_card_exact_query_.Init(session_data_);
      async_redis_helper_.AsyncGet(&form_card_exact_query_);
      form_card_broad_query_.Init(session_data_);
      async_redis_helper_.AsyncGet(&form_card_broad_query_);
    }
    if (enable_dpa_form_strong_card) {
      dpa_form_strong_card_.Init(session_data_);
      async_redis_helper_.AsyncGet(&dpa_form_strong_card_);
    }
  }

  if (enable_form_inner_playlet_query) {
    form_inner_playlet_query_.Init(session_data_);
    async_redis_helper_.AsyncGet(&form_inner_playlet_query_);
  }

  search_query_face_author_.Init(session_data_);
  async_redis_helper_.AsyncGet(&search_query_face_author_);

  // Akg 搜索图 embedding
  search_akg_query_embedding_.Init(session_data_);
  async_redis_helper_.AsyncGet(&search_akg_query_embedding_);

  if (enable_sug_app_style) {
    app_card_query_sug_.Init(session_data_);
    async_redis_helper_.AsyncGet(&app_card_query_sug_);
  }

  if (enable_sug_bigv_style) {
    search_live_big_v_card_sug_.Init(session_data_);
    async_redis_helper_.AsyncGet(&search_live_big_v_card_sug_);
  }
  if (enable_sug_submit_style) {
    form_card_query_sug_.Init(session_data_);
    async_redis_helper_.AsyncGet(&form_card_query_sug_);
  }
  if (enable_goods_query_rewrite) {
    search_goods_query_rewrite_.Init(session_data_);
    async_redis_helper_.AsyncGet(&search_goods_query_rewrite_);
  }
  search_kbox_query_.Init(session_data_);
  async_redis_helper_.AsyncGet(&search_kbox_query_);
  if (enable_search_prefer_single_ads) {
    search_single_col_query_.Init(session_data_);
    async_redis_helper_.AsyncGet(&search_single_col_query_);
    search_double_col_user_.Init(session_data_);
    async_redis_helper_.AsyncGet(&search_double_col_user_);
  }
  if (enable_search_goods_bigv_query) {
    goods_bigv_query_.Init(session_data_);
    async_redis_helper_.AsyncGet(&goods_bigv_query_);
  }
  return true;
}


void QueryInfoCollection::FillSearchLiveTabWhiteList() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_live_tab_white_list_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    session_data_->is_live_tab_white_list = true;
  }
}

void QueryInfoCollection::FillBigVCardInfo() {
  if (!enable_bigv_style) {
    return;
  }
  std::string value;
  if (!enable_bigv_style_direct) {
    auto ret = async_redis_helper_.Wait(&search_live_big_v_card_, &value);
    if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
      std::string redis_decode_str;
      if (base::Base64Decode(value, &redis_decode_str)) {
        kuaishou::ad::search_ads::QueryUserCelebrityList query_user_celebrity_list;
        query_user_celebrity_list.ParseFromString(redis_decode_str);
        ad_response_->mutable_search_query_transport_info()->mutable_query_user_celebrity_list()->CopyFrom(
            query_user_celebrity_list);
      }
    }
  } else {
    auto ret = async_redis_helper_.Wait(&search_live_big_v_card_direct_, &value);
    if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
      std::vector<std::string> authors;
      base::SplitString(value, ",", &authors);
      for (auto &author : authors) {
        int64_t author_id = 0;
        bool convert = absl::SimpleAtoi(author, &author_id);
        if (convert && author_id > 0) {
          ad_response_->mutable_search_query_transport_info()->mutable_query_user_celebrity_list()
          ->add_user_ids(author_id);
        }
      }
    }
  }


  bool enable_sug = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
                  "enable_sug_uid_bigv_card",
                  session_data_->abtest_user_info,
                  false);
  if (enable_sug) {
    AddBigVCardFromSug();
  }
  bool clear = false;
  auto bigv_query_user_id_config = AdKconfUtil::bigVCardQueryUserWhiteList();
  auto& query_2_user_id_list = bigv_query_user_id_config->data().query_2_user_id_list();
  auto it = query_2_user_id_list.find(query);
  if (it != query_2_user_id_list.end()) {
    ad_response_->mutable_search_query_transport_info()->mutable_query_user_celebrity_list()->Clear();
    clear = true;
    ad_response_->mutable_search_query_transport_info()
    ->mutable_query_user_celebrity_list()->set_query_key(query);
    for (int64_t user_id : it->second.id()) {
      ad_response_->mutable_search_query_transport_info()
      ->mutable_query_user_celebrity_list()->add_user_ids(user_id);
    }
  }
  std::string group = ks::abtest::AbtestInstance::GetString(ks::AbtestBiz::AD_DSP,
                          "bigv_query_user_white_list_group",
                          session_data_->abtest_user_info,
                          "default");
  auto search_bigv_exp_blackCity = AdKconfUtil::searchBigvExpBlackCity();
  bool enable_bigv_exp = (!search_bigv_exp_blackCity->count(
     session_data_->ad_request->ad_user_info().adcode().city()));

  auto bigv_query_user_id_exp_config = AdKconfUtil::bigVCardQueryUserExpWhiteList();
  auto group_it = bigv_query_user_id_exp_config->data().abtest().find(group);
  if (enable_bigv_exp && group_it != bigv_query_user_id_exp_config->data().abtest().end()) {
    auto& query_2_user_id_list = group_it->second.query_2_user_id_list();
    auto query_it = query_2_user_id_list.find(query);
    if (query_it != query_2_user_id_list.end()) {
      if (!clear) {
        ad_response_->mutable_search_query_transport_info()->mutable_query_user_celebrity_list()->Clear();
      }
      ad_response_->mutable_search_query_transport_info()
      ->mutable_query_user_celebrity_list()->set_query_key(query);
      for (int64_t user_id : query_it->second.id()) {
        ad_response_->mutable_search_query_transport_info()
        ->mutable_query_user_celebrity_list()->add_user_ids(user_id);
      }
    }
  }
  if (enable_search_goods_bigv_query) {
    value.clear();
    auto ret = async_redis_helper_.Wait(&goods_bigv_query_, &value);
    if (!(ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty())) {
      ad_response_->mutable_search_query_transport_info()->mutable_query_user_celebrity_list()->Clear();
    }
  }
  int user_size = ad_response_->search_query_transport_info().query_user_celebrity_list().user_ids().size();
  if (user_size > 0) {
    std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_bigv_card_prefix", session_data_->abtest_user_info, "locald_");
    perf::Interval(user_size, "search_bigv_size", prefix);
  }
}
void QueryInfoCollection::FillAigcProduct() {
  if (!enable_aigc_product) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_aigc_product_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    kuaishou::ad::search_ads::QueryAigcProducts aigc_product_list;
    aigc_product_list.ParseFromString(value);
    for (const auto &product_info : aigc_product_list.product_info()) {
      if (product_info.type() ==
        kuaishou::ad::search_ads::StrongCardType::STRONG_CARD_TYPE_APP) {
        ad_response_->mutable_search_query_transport_info()->mutable_aigc_app_product_text()->insert(
          {product_info.product_name(), product_info.text()});
      } else if (product_info.type() ==
        kuaishou::ad::search_ads::StrongCardType::STRONG_CARD_TYPE_FORM) {
        if (!enable_aigc_product_form) {
          continue;
        }
        ad_response_->mutable_search_query_transport_info()->mutable_aigc_form_product_text()->insert(
          {product_info.product_name(), product_info.text()});
      }
    }
  }
}

void QueryInfoCollection::FillBigVCardInfoWithType() {
  if (!enable_bigv_style_with_type) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_live_big_v_card_with_type_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::string redis_decode_str;
    if (base::Base64Decode(value, &redis_decode_str)) {
      kuaishou::ad::search_ads::BigVCardInfoList big_v_card_info_list;
      big_v_card_info_list.ParseFromString(redis_decode_str);
      for (const auto &big_v_card_info : big_v_card_info_list.big_v_card_info()) {
        if (big_v_card_info.type() ==
          kuaishou::ad::search_ads::BigVMatchType::BIGV_MATCH_FLAGSHIP_SHOP_TYPE) {
          ad_response_->mutable_search_query_transport_info()->mutable_bigv_user_with_type()->insert(
            {(int64_t)big_v_card_info.user_id(),
            kuaishou::ad::AdEnum_SearchStrongCardType_EXACT_BIGV_FLAGSHIP_SHOP});
        } else if (big_v_card_info.type() ==
          kuaishou::ad::search_ads::BigVMatchType::BIGV_MATCH_NOT_FLAGSHIP_SHOP_TYPE) {
          ad_response_->mutable_search_query_transport_info()->mutable_bigv_user_with_type()->insert(
            {(int64_t)big_v_card_info.user_id(),
            kuaishou::ad::AdEnum_SearchStrongCardType_EXACT_BIGV_NOT_FLAGSHIP_SHOP});
        } else if (big_v_card_info.type() == kuaishou::ad::search_ads::BigVMatchType::BIGV_MATCH_PRE_EXACT ||
          big_v_card_info.type() == kuaishou::ad::search_ads::BigVMatchType::BIGV_MATCH_POST_EXACT) {
          ad_response_->mutable_search_query_transport_info()->mutable_bigv_user_with_type()->insert(
            {(int64_t)big_v_card_info.user_id(),
            kuaishou::ad::AdEnum_SearchStrongCardType_EXACT_STRONG_CARD_TYPE});
        } else {
          ad_response_->mutable_search_query_transport_info()->mutable_bigv_user_with_type()->insert(
            {(int64_t)big_v_card_info.user_id(),
            kuaishou::ad::AdEnum_SearchStrongCardType_NOT_EXACT_STRONG_CARD_TYPE});
        }
      }
    }
  }
  auto bigv_query_user_id_config = AdKconfUtil::bigVCardQueryUserWhiteList();
  auto& query_2_user_id_list = bigv_query_user_id_config->data().query_2_user_id_list();
  auto it = query_2_user_id_list.find(query);
  if (it != query_2_user_id_list.end()) {
    ad_response_->mutable_search_query_transport_info()->mutable_bigv_user_with_type()->clear();
    for (int64_t user_id : it->second.id()) {
      ad_response_->mutable_search_query_transport_info()->mutable_bigv_user_with_type()->insert(
      {user_id, kuaishou::ad::AdEnum_SearchStrongCardType_EXACT_STRONG_CARD_TYPE});
    }
  }
}


void QueryInfoCollection::AddBigVCardFromSug() {
  int32_t from_page = session_data_->ad_request->search_info().from_page();
  const auto& ext_params = session_data_->ad_request->search_info().
  combo_search_params().ext_params();
  if (ext_params.empty()) {
    return;
  }

  auto& stratege_data = session_data_->ad_request->search_info().combo_search_params().strategy_data();
  auto strategy_data_iter = stratege_data.find("signalParams");
  if (strategy_data_iter == stratege_data.end()) {
    return;
  }
  int64_t sug_recommend_uid = 0;
  int64_t ad_author_id = 0;
  std::string signal_params_encode = strategy_data_iter->second;
  std::string signal_params_decode;
  ::kuaishou::search::SeClientRealTimeActionList signal_params_pb;
  if (signal_params_encode != "" && base::Base64Decode(signal_params_encode, &signal_params_decode)) {
    if (signal_params_pb.ParseFromString(signal_params_decode)) {
      const auto& se_inner_ex_param = signal_params_pb.search_inner_signal().inner_params_list();
      for (const auto& se_inner_param : se_inner_ex_param) {
        if (se_inner_param.key() == "author_id") {
          base::StringToInt64(se_inner_param.value(), &sug_recommend_uid);
        } else if (se_inner_param.key() == "ad_author_id") {
          base::StringToInt64(se_inner_param.value(), &ad_author_id);
          ad_response_->mutable_search_query_transport_info()->set_gys_author_id(ad_author_id);
        }
      }
    }
  }

  int32_t sug_type = 0;
  base::Json ext_params_json(StringToJson(ext_params));
  if (ext_params_json.IsObject()) {
    sug_type = ext_params_json.GetInt("sugType", 0);
  }

  auto gys_author_id = ad_response_->search_query_transport_info().gys_author_id();
  if (gys_author_id > 0) {
    perf::Count(1, "search_gys_author",
        std::to_string(from_page), std::to_string(sug_type));
  }

  if (!(from_page == 2 && sug_type == 1)) {  // 不是 sug 直达场景
    return;
  }
  if (sug_recommend_uid == 0) {
    return;
  }
  bool exists = false;
  auto &celebrity_list = ad_response_->search_query_transport_info().query_user_celebrity_list();
  for (int i = 0; i < celebrity_list.user_ids_size(); ++i) {
    if (celebrity_list.user_ids(i) == sug_recommend_uid) {
      exists = true;
      break;
    }
  }
  if (!exists) {
    ad_response_->mutable_search_query_transport_info()
    ->mutable_query_user_celebrity_list()->add_user_ids(sug_recommend_uid);
  }
}

void QueryInfoCollection::FillRealtimeActionList() {
  auto& stratege_data = session_data_->ad_request->search_info().combo_search_params().strategy_data();
  auto strategy_data_iter = stratege_data.find("signalParams");
  if (strategy_data_iter == stratege_data.end()) {
    return;
  }
  std::string signal_params_encode = strategy_data_iter->second;
  std::string signal_params_decode;
  ::kuaishou::search::SeClientRealTimeActionList signal_params_pb;
  if (signal_params_encode != "" && base::Base64Decode(signal_params_encode, &signal_params_decode)) {
    if (signal_params_pb.ParseFromString(signal_params_decode)) {
      if (signal_params_pb.action_list_size() > 0) {
        auto* p_query_fea = ad_response_->mutable_search_query_transport_info()->add_query_fea_infos();
        p_query_fea->set_name_value(::kuaishou::ad::search_ads::QueryFeaItemInfo::REALTIME_ACTION_LIST);
        for (const auto& action_list : signal_params_pb.action_list()) {
          int64_t parse_photo_id;
          if (!utility::DecryptPhotoId(action_list.content_id(), &parse_photo_id)) {
            continue;
          }
          auto* add_action_list = p_query_fea->add_action_list();
          add_action_list->set_author_id(action_list.author_id());
          add_action_list->set_timestamp(action_list.timestamp());
          add_action_list->set_photo_id(parse_photo_id);
          add_action_list->set_content_type(action_list.content_type());
          add_action_list->set_signal_type(action_list.signal_type());
          add_action_list->set_signal_value(action_list.signal_value());
        }
      }
      const auto& trace_list = signal_params_pb.search_source_traces().trace_list();
      for (const auto& trace_info : trace_list) {
        // 只记录 TraceType = KS_TRACE_TYPE_ENTRY_ID_INFO
        if (static_cast<int>(trace_info.trace_type()) == 3) {
          auto* search_source_traces =
              ad_response_->mutable_search_query_transport_info()->mutable_search_source_traces();
          search_source_traces->set_ref_photo_id(std::to_string(signal_params_pb.refer_photo_id()));
          search_source_traces->set_ref_author_id(trace_info.author_id());
          search_source_traces->set_stg_recall_source(trace_info.stg_recall_source());
        }
      }
    }
  }
}

void QueryInfoCollection::FillSearchQueryFaceAuthor() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_query_face_author_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::vector<std::string> authors;
    base::SplitString(value, ",", &authors);
    for (auto &author : authors) {
      std::vector<std::string> authorinfo;
      base::SplitString(author, ":", &authorinfo);
      if (authorinfo.size() < 2) {
        LOG_EVERY_N(WARNING, 100) << "authorinfo valid:" << author;
        continue;
      }
      std::string id = authorinfo[0];
      ad_response_->mutable_search_query_transport_info()->add_author_ids(id);
    }
  }
}

void QueryInfoCollection::FillQueryEmbeddingInfo() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_user_embedding_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    ad_response_->mutable_search_query_transport_info()->set_query_relevance_embedding_lite(value);
    std::string redis_decode_str;
  }
}

void QueryInfoCollection::FillSearchQueryFeature() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_query_feature_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::string redis_decode_str;
    if (base::Base64Decode(value, &redis_decode_str)) {
      kuaishou::ad::search_ads::QueryFeaInfo query_fea_info;
      query_fea_info.ParseFromString(redis_decode_str);
      for (auto query_fea : query_fea_info.query_fea_infos()) {
        auto* p_query_fea = ad_response_->mutable_search_query_transport_info()->add_query_fea_infos();
        p_query_fea->CopyFrom(query_fea);
      }
      perf::Interval(query_fea_info.query_fea_infos_size(), "query_feature_size");
    }
  }
}

void QueryInfoCollection::FillSearchAkgQueryEmbedding() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_akg_query_embedding_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::string redis_decode_str;
    if (base::Base64Decode(value, &redis_decode_str)) {
      kuaishou::ad::search_ads::QueryFeaInfo query_fea_info;
      query_fea_info.ParseFromString(redis_decode_str);
      LOG_EVERY_N(INFO, 100000) << "akg_query_embedding_fea_info: " << redis_decode_str;
      for (auto query_fea : query_fea_info.query_fea_infos()) {
        auto* p_query_fea = ad_response_->mutable_search_query_transport_info()->add_query_fea_infos();
        p_query_fea->CopyFrom(query_fea);
      }
      perf::Interval(query_fea_info.query_fea_infos_size(), "akg_query_embedding_size");
    }
  }
}

void QueryInfoCollection::FillSearchQueryCategory() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_query_category_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::set<std::string> query_category_string_list = absl::StrSplit(value, ",", absl::SkipEmpty());
    for (const auto& category_str : query_category_string_list) {
      int64_t category_id = 0;
      base::StringToInt64(category_str, &category_id);
      if (category_id != 0) {
        ad_response_->mutable_search_query_transport_info()->add_query_category(category_id);
      }
    }
  }
}
void QueryInfoCollection::FillQueryCategory() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&query_category_, &value);
  auto* query_knowledge = ad_response_->mutable_search_query_transport_info()
                            ->mutable_query_knowledge();
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::vector<std::string> category_list = absl::StrSplit(value, ";", absl::SkipEmpty());
    for (const auto& category : category_list) {
      std::vector<std::string> tokens = absl::StrSplit(category, ":", absl::SkipEmpty());
      if (tokens.size() != 2) {
        LOG_EVERY_N(INFO, 10000) << "QueryCategory size error";
        continue;
      }
      float score = 0.0;
      std::vector<std::string> category_level_list = absl::StrSplit(tokens[0], "_", absl::SkipEmpty());
      if (category_level_list.size() == 3 &&
        absl::SimpleAtof(tokens[1], &score)) {
        auto* query_category = query_knowledge->add_query_category();
        query_category->set_score(score);
        query_category->set_category_level_1(category_level_list[0]);
        query_category->set_category_level_2(category_level_list[1]);
        query_category->set_category_level_3(category_level_list[2]);
      }
    }
  }
  perf::Count(1, "query_feature_info",
  (query_knowledge->query_category_size() > 0 ? "has_category" : "empty_category"));
}
void QueryInfoCollection::FillSearchPreferSingleCol() {
  if (!enable_search_prefer_single_ads) {
    return;
  }
  bool prefer_single = false;
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_single_col_query_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    prefer_single = true;
  }
  value.clear();
  ret = async_redis_helper_.Wait(&search_double_col_user_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    prefer_single = false;
  }
  ad_response_->mutable_search_query_transport_info()->set_prefer_single_item_style(prefer_single);
}

void QueryInfoCollection::FillAppCardQuery() {
  if (!enable_app_style) {
    return;
  }
  std::unordered_set<std::string> product_set;
  {
    std::string value;
    auto ret = async_redis_helper_.Wait(&app_card_query_, &value);
    if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
      std::set<std::string> product_list = absl::StrSplit(value, ",", absl::SkipEmpty());
      for (const auto& product : product_list) {
        ad_response_->mutable_search_query_transport_info()->add_app_card_product_name(product);
        product_set.insert(product);
      }
    }
  }
  {
    std::string value;
    auto ret = async_redis_helper_.Wait(&app_card_platform_query_, &value);
    if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
      std::set<std::string> product_list = absl::StrSplit(value, ",", absl::SkipEmpty());
      for (const auto& product : product_list) {
        if (enable_all_app_card_exact) {
          if (product_set.find(product) == product_set.end()) {
            ad_response_->mutable_search_query_transport_info()->add_app_card_product_name(product);
          }
        } else {
          ad_response_->mutable_search_query_transport_info()->mutable_app_card_pname_with_type()->insert(
            {product, kuaishou::ad::AdEnum_SearchStrongCardType_NOT_EXACT_STRONG_CARD_TYPE});
        }
      }
    }
  }
  if (enable_dpa_strong_card) {
    std::string value;
    auto ret = async_redis_helper_.Wait(&dpa_strong_card_, &value);
    if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
      DpaStrongCardInfo dpa_strong_card_result;
      if (ks::ad_base::pb_utils::ParseBase64PB(dpa_strong_card_result, value)) {
        auto& pnames = dpa_strong_card_result.pnames();
        auto& dpa_ids = dpa_strong_card_result.dpa_ids_v2();
        auto* platform_strong_card_info = ad_response_->mutable_search_query_transport_info()
            ->mutable_platform_strong_card_info();
        for (const auto& pname : pnames) {
          platform_strong_card_info->add_pnames(pname);
        }
        for (const auto& dpa_id : dpa_ids) {
          platform_strong_card_info->add_dpa_ids_v2(dpa_id);
        }
      }
    }
  }
  auto app_query_pname_config = AdKconfUtil::appCardQueryProductnameWhiteList();
  auto& query_2_pname_list = app_query_pname_config->data().query_2_pname_list();
  auto it = query_2_pname_list.find(query);
  if (it != query_2_pname_list.end()) {
    for (std::string pname : it->second.pname()) {
      if (ad_response_->search_query_transport_info().app_card_pname_with_type().find(pname) !=
        ad_response_->search_query_transport_info().app_card_pname_with_type().end()) {
        (*ad_response_->mutable_search_query_transport_info()->mutable_app_card_pname_with_type())[pname]
         = kuaishou::ad::AdEnum_SearchStrongCardType_EXACT_STRONG_CARD_TYPE;
      } else {
        ad_response_->mutable_search_query_transport_info()->mutable_app_card_pname_with_type()->insert(
         {pname, kuaishou::ad::AdEnum_SearchStrongCardType_EXACT_STRONG_CARD_TYPE});
      }
      ad_response_->mutable_search_query_transport_info()->add_app_card_product_name(pname);
    }
  }
  int app_size = ad_response_->search_query_transport_info().app_card_product_name().size();
  if (app_size > 0) {
    std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "app_card_query_prefix", session_data_->abtest_user_info, "app_");
    perf::Interval(app_size, "search_app_size", prefix);
  }
  int app_plat_size = ad_response_->search_query_transport_info().app_card_pname_with_type().size();
  if (app_plat_size > 0) {
    std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "app_card_platform_query_prefix",
      session_data_->abtest_user_info, "app_plat_v1_");
    perf::Interval(app_plat_size, "search_app_plat_size", prefix);
  }
}

void QueryInfoCollection::FillFormCardQuery() {
  if (!enable_submit_style) {
    return;
  }
  if (enable_submit_exact_broad) {
    {
      std::string value;
      auto ret = async_redis_helper_.Wait(&form_card_exact_query_, &value);
      if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
        std::set<std::string> product_list = absl::StrSplit(value, ",", absl::SkipEmpty());
        for (const auto& product : product_list) {
          ad_response_->mutable_search_query_transport_info()->add_form_submit_product_name(product);
        }
      }
    }
    {
      std::string value;
      auto ret = async_redis_helper_.Wait(&form_card_broad_query_, &value);
      if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
        std::set<std::string> product_list = absl::StrSplit(value, ",", absl::SkipEmpty());
        for (const auto& product : product_list) {
          ad_response_->mutable_search_query_transport_info()->mutable_form_submit_pname_with_type()->insert(
              {product, kuaishou::ad::AdEnum_SearchStrongCardType_NOT_EXACT_STRONG_CARD_TYPE});
        }
      }
    }
  }
  if (enable_dpa_form_strong_card) {
    std::string value;
    auto ret = async_redis_helper_.Wait(&dpa_form_strong_card_, &value);
    if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
      DpaStrongCardInfo dpa_strong_card_result;
      if (ks::ad_base::pb_utils::ParseBase64PB(dpa_strong_card_result, value)) {
        auto& dpa_ids = dpa_strong_card_result.dpa_ids_v2();
        for (const auto& dpa_id : dpa_ids) {
          ad_response_->mutable_search_query_transport_info()->add_form_strong_card_dpa_ids(dpa_id);
        }
      }
    }
  }
  auto form_query_pname_config = AdKconfUtil::formCardQueryProductnameWhiteList();
  auto& query_2_pname_list = form_query_pname_config->data().query_2_pname_list();
  auto it = query_2_pname_list.find(query);
  if (it != query_2_pname_list.end()) {
    for (std::string pname : it->second.pname()) {
      if (ad_response_->search_query_transport_info().form_submit_pname_with_type().find(pname) !=
        ad_response_->search_query_transport_info().form_submit_pname_with_type().end()) {
        (*ad_response_->mutable_search_query_transport_info()->mutable_form_submit_pname_with_type())[pname]
         = kuaishou::ad::AdEnum_SearchStrongCardType_EXACT_STRONG_CARD_TYPE;
      } else {
        ad_response_->mutable_search_query_transport_info()->mutable_form_submit_pname_with_type()->insert(
         {pname, kuaishou::ad::AdEnum_SearchStrongCardType_EXACT_STRONG_CARD_TYPE});
      }
      ad_response_->mutable_search_query_transport_info()->add_form_submit_product_name(pname);
    }
  }
  int form_exact_size = ad_response_->search_query_transport_info().app_card_product_name().size();
  if (form_exact_size > 0) {
    std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "form_card_exact_query_prefix", session_data_->abtest_user_info, "form_exact_");
    perf::Interval(form_exact_size, "search_form_exact_size", prefix);
  }
  int form_broad_size = ad_response_->search_query_transport_info().app_card_pname_with_type().size();
  if (form_broad_size > 0) {
    std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "form_card_broad_query_prefix", session_data_->abtest_user_info, "form_broad_");
    perf::Interval(form_broad_size, "search_form_broad_size", prefix);
  }
}

void QueryInfoCollection::FillSeriesCardQuery() {
  if (!enable_form_inner_playlet_query) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&form_inner_playlet_query_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    PlayletItem playlet_result;
    if (ks::ad_base::pb_utils::ParseBase64PB(playlet_result, value)) {
      auto& series_ids = playlet_result.dpa_ids();
      for (const auto& series_id : series_ids) {
        ad_response_->mutable_search_query_transport_info()->add_duanju_series_ids(series_id);
      }
    }
  }
}

void QueryInfoCollection::FillSearchQueryHotSequenceFeature() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_query_hot_sequence_feature_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    QueryHotSequenceInfo query_info;
    if (ks::ad_base::pb_utils::ParseBase64PB(query_info, value)) {
      for (auto& query_info_piece : query_info.query_hot_sequence_detail_info()) {
        uint64_t photo_id = query_info_piece.photo_id();
        uint64_t photo_imp_count = query_info_piece.photo_imp_count();
        uint64_t photo_clk_count = query_info_piece.photo_clk_count();
        uint64_t info_source = query_info_piece.info_source();
        uint64_t item_type = query_info_piece.item_type();
        auto* query_hot_sequence_info = ad_response_->mutable_search_query_transport_info()
                                        ->mutable_query_hot_sequence_info()
                                        ->add_query_hot_sequence_detail_info();
        query_hot_sequence_info->set_photo_id(photo_id);
        query_hot_sequence_info->set_photo_imp_count(photo_imp_count);
        query_hot_sequence_info->set_photo_clk_count(photo_clk_count);
        query_hot_sequence_info->set_info_source(info_source);
        query_hot_sequence_info->set_item_type(item_type);
      }
    }
  }
}

void QueryInfoCollection::FillOriQueryQuantizeId() {
  if (!enable_ori_query_quantize_id) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&ori_query_quantize_id_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    ad_response_->mutable_search_query_transport_info()->set_ori_query_quantize_id(value);
  } else {
    ad_response_->mutable_search_query_transport_info()->set_ori_query_quantize_id("0");
  }
}

void QueryInfoCollection::FillFormCardQuerySug() {
  if (!enable_sug_submit_style) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&form_card_query_sug_, &value);
  if (ad_response_->search_query_transport_info().form_submit_product_name_size() > 0
  || ad_response_->search_query_transport_info().app_card_pname_with_type_size() > 0
  || ad_response_->search_query_transport_info().app_card_product_name_size() > 0) {
    return;
  }
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::set<std::string> product_list = absl::StrSplit(value, ",", absl::SkipEmpty());
    for (const auto& product : product_list) {
      ad_response_->mutable_search_query_transport_info()->add_form_submit_product_name(product);
    }
  }
}

void QueryInfoCollection::FillSearchQueryGoodsIntent() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_query_goods_intent_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    ad_response_->mutable_search_query_transport_info()->set_is_goods_query(true);
  } else {
    ad_response_->mutable_search_query_transport_info()->set_is_goods_query(false);
  }
}
void QueryInfoCollection::FillSearchQueryIndustryId() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&seacrh_query_industry_id_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::set<std::string> query_industry_string_list = absl::StrSplit(value, ",", absl::SkipEmpty());
    for (const auto& industry_str : query_industry_string_list) {
      int64_t industry_id = 0;
      base::StringToInt64(industry_str, &industry_id);
      if (industry_id != 0) {
        ad_response_->mutable_search_query_transport_info()->add_second_industry_ids(industry_id);
      }
    }
  }
}

void QueryInfoCollection::FillSearchQueryFeatureCount() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&seacrh_query_feature_count_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::string redis_decode_str;
    if (base::Base64Decode(value, &redis_decode_str)) {
      kuaishou::ad::search_ads::QueryFeaInfo query_fea_info;
      query_fea_info.ParseFromString(redis_decode_str);
      LOG_EVERY_N(INFO, 10000) << "search_query_feature_count: " << redis_decode_str;
      for (auto query_fea : query_fea_info.query_fea_infos()) {
        auto* p_query_fea = ad_response_->mutable_search_query_transport_info()->add_query_fea_infos();
        p_query_fea->CopyFrom(query_fea);
      }
      perf::Interval(query_fea_info.query_fea_infos_size(), "search_query_feature_count");
    }
  }
}
void QueryInfoCollection::FillSugOriginQuery() {
  ad_response_->mutable_search_query_transport_info()
  ->set_sug_origin_query(session_data_->sug_origin_query);
}

void QueryInfoCollection::FillBigVCardInfoSug() {
  if (!enable_sug_bigv_style) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_live_big_v_card_sug_, &value);
  if (ad_response_->search_query_transport_info().query_user_celebrity_list()
  .user_ids_size() > 0 ||
  ad_response_->search_query_transport_info().form_submit_product_name_size() > 0
  || ad_response_->search_query_transport_info().app_card_pname_with_type_size() > 0
  || ad_response_->search_query_transport_info().app_card_product_name_size() > 0) {
    return;
  }
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::vector<std::string> authors;
    base::SplitString(value, ",", &authors);
    for (auto &author : authors) {
      int64_t author_id = 0;
      bool convert = absl::SimpleAtoi(author, &author_id);
      if (convert && author_id > 0) {
        ad_response_->mutable_search_query_transport_info()->mutable_query_user_celebrity_list()
        ->add_user_ids(author_id);
      }
    }
  }
}
void QueryInfoCollection::FillAppCardQuerySug() {
  if (!enable_sug_app_style) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&app_card_query_sug_, &value);
  if (ad_response_->search_query_transport_info().form_submit_product_name_size() > 0
  || ad_response_->search_query_transport_info().app_card_pname_with_type_size() > 0
  || ad_response_->search_query_transport_info().app_card_product_name_size() > 0) {
    return;
  }
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    std::set<std::string> product_list = absl::StrSplit(value, ",", absl::SkipEmpty());
    for (const auto& product : product_list) {
      ad_response_->mutable_search_query_transport_info()->add_app_card_product_name(product);
    }
  }
}

void QueryInfoCollection::FillQueryAdDiversionInfo() {
  int32_t from_page = session_data_->ad_request->search_info().from_page();
  std::string from_page_str =  kuaishou::search::SearchFromPage_Name(from_page);
  std::string entry_source =
      from_page == 0 ? session_data_->ad_request->search_info().combo_search_params().enter_source()
                     : from_page_str;
  bool has_signal_diversion = false;
  // 先判断 signal 有没有
  do {
    const auto& ext_params = session_data_->ad_request->search_info().combo_search_params().ext_params();
    if (ext_params.empty()) {
      break;
    }
    auto& stratege_data = session_data_->ad_request->search_info().combo_search_params().strategy_data();
    auto strategy_data_iter = stratege_data.find("signalParams");
    if (strategy_data_iter == stratege_data.end()) {
      break;
    }
    std::string ad_query_source;
    std::string signal_params_encode = strategy_data_iter->second;
    std::string signal_params_decode;
    ::kuaishou::search::SeClientRealTimeActionList signal_params_pb;
    if (signal_params_encode != "" && base::Base64Decode(signal_params_encode, &signal_params_decode)) {
      if (signal_params_pb.ParseFromString(signal_params_decode)) {
        const auto& se_inner_ex_param = signal_params_pb.search_inner_signal().inner_params_list();
        for (const auto& se_inner_param : se_inner_ex_param) {
          if (se_inner_param.key() == "adQuerySource") {
            ad_query_source = se_inner_param.value();
            auto* search_ad_diversion_info =
                ad_response_->mutable_search_query_transport_info()->mutable_search_ad_diversion_info();
            DecodeDiversionInfo(ad_query_source, entry_source, search_ad_diversion_info);
            has_signal_diversion = true;
            break;;
          }
        }
        break;
      }
    }
  } while (0);

  // 再判断 kwai 链场景
  if (!has_signal_diversion) {
    std::string ad_query_source = session_data_->ad_request->search_info().ad_query_source();
    auto* search_ad_diversion_info =
        ad_response_->mutable_search_query_transport_info()->mutable_search_ad_diversion_info();
    DecodeDiversionInfo(ad_query_source, entry_source, search_ad_diversion_info);
  }

  LOG_EVERY_N(INFO, 10000)
      << " search_ad_diversion_info : "
      << ad_response_->search_query_transport_info().search_ad_diversion_info().ShortDebugString();
  perf::Count(
      1, "search_ad_diversion_info",
      ad_response_->search_query_transport_info().search_ad_diversion_info().source_trace().recall_source(),
      has_signal_diversion ? "signal" : "ext_param");
  if (session_data_->ad_request->search_info().page_num() == 0 &&
      session_data_->ad_request->search_info().search_source() !=
          kuaishou::ad::SearchInfo::INNER_STREAM_COMBO_SEARCH) {
    auto& source_trace =
        ad_response_->search_query_transport_info().search_ad_diversion_info().source_trace();
    perf::Count(1, "search_ad_diversion_pv",
                kuaishou::ad::AdEnum::AdQueryRecommendSourceType_Name(source_trace.diversion_source()),
                source_trace.recall_source(), std::to_string(source_trace.is_commerce()));
  }
}

void QueryInfoCollection::FillSearchKboxQuery() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_kbox_query_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    ad_response_->mutable_search_query_transport_info()->set_is_kbox_query(true);
  } else {
    ad_response_->mutable_search_query_transport_info()->set_is_kbox_query(false);
  }
}


void QueryInfoCollection::FillGoodsQueryRewrite() {
  if (!enable_goods_query_rewrite) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&search_goods_query_rewrite_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    QueryExtendList q2q_result;
    if (ks::ad_base::pb_utils::ParseBase64PB(q2q_result, value)) {
      for (auto& item : q2q_result.query_extend_infos()) {
        RewriteQuery rq;
        rq.extend_query = item.extend_query();
        rq.extend_query_sign =
            base::CityHash64(rq.extend_query.c_str(), rq.extend_query.length());
        rq.extend_score = item.extend_score();
        rq.extend_type = ExtendType::GOODS_QTYPE;
        session_data_->goods_query_rewrite_cache_list.emplace_back(rq);
        auto* rewrite_query = ad_response_->mutable_search_query_transport_info()
          ->add_goods_query_rewrite_list();
        rewrite_query->set_extend_query(rq.extend_query);
        rewrite_query->set_extend_query_sign(rq.extend_query_sign);
        rewrite_query->set_extend_type(rq.extend_type);
        rewrite_query->set_sub_extend_type(rq.sub_extend_type);
        rewrite_query->set_extend_score(rq.extend_score);
      }
      perf::Interval(session_data_->goods_query_rewrite_cache_list.size(),
                     "goods_query_rewrite_size", "from_redis",
                     std::to_string(session_data_->goods_query_rewrite_cache_list.empty()));
    }
  }
}

bool QueryInfoCollection::WaitInner() {
  if (!AdKconfUtil::enableQueryInfoCollection()) {
    return true;
  }
  WAIT_TIME_RECORD;
  FillSearchKboxQuery();
  FillSugOriginQuery();
  FillSearchLiveTabWhiteList();
  FillBigVCardInfo();
  FillRealtimeActionList();
  FillQueryEmbeddingInfo();
  FillSearchQueryCategory();
  FillQueryCategory();
  FillSearchQueryFeature();
  FillSearchQueryGoodsIntent();
  FillSearchQueryIndustryId();
  FillSearchQueryFeatureCount();
  FillAppCardQuery();
  FillSearchQueryFaceAuthor();
  FillFormCardQuery();
  FillSeriesCardQuery();
  FillSearchAkgQueryEmbedding();
  FillBigVCardInfoSug();
  FillAppCardQuerySug();
  FillFormCardQuerySug();
  FillQueryAdDiversionInfo();
  FillBigVCardInfoWithType();
  FillGoodsQueryRewrite();
  FillAigcProduct();
  FillSearchQueryHotSequenceFeature();
  FillOriQueryQuantizeId();
  FillSearchPreferSingleCol();
  return true;
}

}  // namespace query_retrieval
}  // namespace ks
