#include <algorithm>
#include "teams/ad/query_retrieval/src/engine/utility/redis/async_redis_params.h"
#include "base/strings/string_util.h"
#include "teams/ad/query_retrieval/src/util/kconf/kconf.h"


namespace ks {
namespace query_retrieval {

void SearchQueryUserList::SetKey(ContextData* session_data) {
  std::string search_user_embedding_group = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_user_embedding_group", session_data->abtest_user_info, "base");

  const auto query_user_config = AdKconfUtil::adQueryUserEmbeddingRedisConfig()->data();
  auto query_config_iter = query_user_config.abtest().find(search_user_embedding_group);
  std::string prefix{};
  if (query_config_iter != query_user_config.abtest().end()) {
    prefix = query_config_iter->second.prefix();
  }
  std::string query_key = session_data->ad_request->search_info().query();
  std::string query_sign = std::to_string(base::CityHash64(query_key.c_str(), query_key.length()));
  this->key_ = absl::Substitute("$0$1", prefix, query_sign);
}

void SearchQueryUserList::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void SearchQueryUserList::SetTable(ContextData* session_data) { this->table_ = "adSearchUserEmbedding"; }

void SearchLiveQueryTag::SetKey(ContextData* session_data) {
  std::string query_live_tag_group = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "query_live_tag_group", session_data->abtest_user_info, "base");
  const auto query_live_tag_config = AdKconfUtil::adQueryLiveStreamTagRedisConfig()->data();
  auto query_config_iter = query_live_tag_config.abtest().find(query_live_tag_group);
  std::string prefix{};
  if (query_config_iter != query_live_tag_config.abtest().end()) {
    prefix = query_config_iter->second.prefix();
  }
  std::string query_key = session_data->ad_request->search_info().query();
  std::string query_sign = std::to_string(base::CityHash64(query_key.c_str(), query_key.length()));
  this->key_ = absl::Substitute("$0$1", prefix, query_sign);
}

void SearchLiveQueryTag::SetTimeout(ContextData* session_data) {
  const auto query_live_tag_config = AdKconfUtil::adQueryLiveStreamTagRedisConfig()->data();
  auto timeout = query_live_tag_config.timeout_ms();
  if (timeout > 0) {
    this->timeout_ = timeout;
  } else {
    this->timeout_ = 10;
  }
}

void SearchLiveQueryTag::SetTable(ContextData* session_data) {
  const auto query_live_tag_config = AdKconfUtil::adQueryLiveStreamTagRedisConfig()->data();
  auto kcc_name = query_live_tag_config.kcc_name();
  if (kcc_name != "") {
    this->table_ = kcc_name;
  } else {
    this->table_ = "adSearchLiveEmbedding";
  }
}
void GoodsBigvQuery::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_goods_bigv_query_prefix", session_data->abtest_user_info, "goodsbigv_");
  std::string query_key = session_data->ad_request->search_info().query();
  this->key_ = absl::Substitute("$0$1", prefix, query_key);
}

void GoodsBigvQuery::SetTimeout(ContextData* session_data) {
  this->timeout_ = 10;
}

void GoodsBigvQuery::SetTable(ContextData* session_data) {
  this->table_ = "adSearchPlc";
}

void SearchLiveTabWhiteList::SetKey(ContextData* session_data) {
  std::string query_key = session_data->ad_request->search_info().query();
  std::string search_live_tab_group = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_live_tab_group", session_data->abtest_user_info, "base");
  const auto live_tab_query_config = AdKconfUtil::liveTabMerchantQueryConf()->data();
  auto query_config_iter = live_tab_query_config.abtest().find(search_live_tab_group);
  std::string prefix = "v1_";
  if (query_config_iter != live_tab_query_config.abtest().end()) {
    prefix = query_config_iter->second.prefix();
  }
  query_key = prefix + query_key;
  this->key_ = query_key;
}

void SearchLiveTabWhiteList::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void SearchLiveTabWhiteList::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}

void SearchLiveBigVCard::SetKey(ContextData* session_data) {
  std::string search_vip_card_group = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_vip_card_group", session_data->abtest_user_info, "base");
  const auto query_vip_card_config = AdKconfUtil::livestreamQueryCelebrityConf()->data();
  auto query_config_iter = query_vip_card_config.abtest().find(search_vip_card_group);
  std::string prefix{};
  if (query_config_iter != query_vip_card_config.abtest().end()) {
    prefix = query_config_iter->second.prefix();
  }
  std::string query_key = session_data->ad_request->search_info().query();
  std::string query_sign = std::to_string(base::CityHash64(query_key.c_str(), query_key.length()));
  this->key_ = absl::Substitute("$0$1", prefix, query_sign);
}

void SearchLiveBigVCard::SetTimeout(ContextData* session_data) {
  const auto query_vip_card_config = AdKconfUtil::livestreamQueryCelebrityConf()->data();
  auto timeout = query_vip_card_config.timeout_ms();
  if (timeout > 0) {
    this->timeout_ = timeout;
  } else {
    this->timeout_ = 10;
  }
}

void SearchLiveBigVCard::SetTable(ContextData* session_data) {
  const auto query_vip_card_config = AdKconfUtil::livestreamQueryCelebrityConf()->data();
  auto kcc_name = query_vip_card_config.kcc_name();
  if (kcc_name != "") {
    this->table_ = kcc_name;
  } else {
    this->table_ = "adSearchPlc";
  }
}

void SearchLiveBigVCardDirect::SetKey(ContextData* session_data) {
  std::string search_bigv_card_prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_bigv_card_prefix", session_data->abtest_user_info, "locald_");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  this->key_ = absl::Substitute("$0$1", search_bigv_card_prefix, query_key);
}

void SearchLiveBigVCardDirect::SetTimeout(ContextData* session_data) {
  this->timeout_ = 10;
}

void SearchLiveBigVCardDirect::SetTable(ContextData* session_data) {
  this->table_ = "adSearchPlc";
}

void SearchLiveBigVCardWithType::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "bigv_card_type_prefix",
      session_data->abtest_user_info, "bigv_");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  this->key_ = absl::Substitute("$0$1", prefix, query_key);
}

void SearchLiveBigVCardWithType::SetTimeout(ContextData* session_data) {
  this->timeout_ = 10;
}

void SearchLiveBigVCardWithType::SetTable(ContextData* session_data) {
  this->table_ = "adSearchPlc";
}
void SearchAigcProduct::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "aigc_product_title_prefix",
      session_data->abtest_user_info, "kwaiyi_title_");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  this->key_ = absl::Substitute("$0$1", prefix, query_key);
}

void SearchAigcProduct::SetTimeout(ContextData* session_data) {
  this->timeout_ = 10;
}

void SearchAigcProduct::SetTable(ContextData* session_data) {
  this->table_ = "queryCategory";
}

void SearchQueryHotSequenceFeature::SetKey(ContextData* session_data) {
  std::string query_key = session_data->ad_request->search_info().query();
  auto hash = base::CityHash64(query_key.c_str(), query_key.length());
  query_key = "src_q_" + std::to_string(hash);
  this->key_ = query_key;
}
void SearchQueryHotSequenceFeature::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void SearchQueryHotSequenceFeature::SetTable(ContextData* session_data) {
  this->table_ = "adSearchQueryHotSequence";
}

void OriQueryQuantizeId::SetKey(ContextData* session_data) {
  std::string query_key = session_data->ad_request->search_info().query();
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "query_quantize_id_prefix", session_data->abtest_user_info, "");
  this->key_ = absl::Substitute("$0$1", prefix, query_key);
}

void OriQueryQuantizeId::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void OriQueryQuantizeId::SetTable(ContextData* session_data) { this->table_ = "adSearchUserEmbedding"; }

void SearchKboxQuery::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_kbox_query_prefix",
      session_data->abtest_user_info, "kbox_");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  this->key_ = absl::Substitute("$0$1", prefix, query_key);
}

void SearchKboxQuery::SetTimeout(ContextData* session_data) {
  this->timeout_ = 10;
}

void SearchKboxQuery::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}

void SearchGoodsQueryRewrite::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "goods_query_rewrite_prefix",
      session_data->abtest_user_info, "goodsqr");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  this->key_ = absl::Substitute("$0$1", prefix, query_key);
}

void SearchGoodsQueryRewrite::SetTimeout(ContextData* session_data) {
  this->timeout_ = 10;
}

void SearchGoodsQueryRewrite::SetTable(ContextData* session_data) {
  this->table_ = "adSearchPlc";
}


void SearchLiveBigVCardSug::SetKey(ContextData* session_data) {
  this->key_.clear();
  std::string query_key = session_data->sug_origin_query;
  if (query_key.length() < 6) {
    return;
  }
  std::string search_bigv_card_prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_bigv_card_prefix", session_data->abtest_user_info, "locald_");
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  this->key_ = absl::Substitute("$0$1", search_bigv_card_prefix, query_key);
}

void SearchLiveBigVCardSug::SetTimeout(ContextData* session_data) {
  this->timeout_ = 10;
}

void SearchLiveBigVCardSug::SetTable(ContextData* session_data) {
  this->table_ = "adSearchPlc";
}




void SearchUserEmbedding::SetKey(ContextData* session_data) {
  const auto shared = AdKconfUtil::newRelevanceConfig();
  const auto& kconf = shared->data().abtest();
  const auto& param = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "new_relevance_config", session_data->abtest_user_info, "base");
  const auto iter = kconf.find(param);
  if (iter == kconf.end()) {
    LOG(ERROR) << "relevance config error: " << param;
    return;
  }
  std::string prefix{};
  prefix = iter->second.rele_emb_ver();
  this->key_ = absl::Substitute("$0_$1", prefix, session_data->ad_request->search_info().query());
}

void SearchUserEmbedding::SetTimeout(ContextData* session_data) {
    this->timeout_ = 10;
}

void SearchUserEmbedding::SetTable(ContextData* session_data) {
  this->table_ = "adSearchEmbeddingTable";
}

void SearchAkgQueryEmbedding::SetKey(ContextData* session_data) {
  std::string query = session_data->ad_request->search_info().query();
  auto hash = base::CityHash64(query.c_str(), query.length());
  this->key_ = "akg_" + std::to_string(hash);
}

void SearchAkgQueryEmbedding::SetTimeout(ContextData* session_data) {
  this->timeout_ = 10;
}

void SearchAkgQueryEmbedding::SetTable(ContextData* session_data) {
  this->table_ = "adSearchQueryFeature";
}

void SearchQueryFeatureCount::SetKey(ContextData* session_data) {
  std::string query = session_data->ad_request->search_info().query();
  auto hash = base::CityHash64(query.c_str(), query.length());
  this->key_ = "fcount_" + std::to_string(hash);
}

void SearchQueryFeatureCount::SetTimeout(ContextData* session_data) {
  this->timeout_ = 10;
}

void SearchQueryFeatureCount::SetTable(ContextData* session_data) {
  this->table_ = "adSearchQueryFeature";
}

void SearchQueryFeature::SetKey(ContextData* session_data) {
  std::string query = session_data->ad_request->search_info().query();
  auto hash = base::CityHash64(query.c_str(), query.length());
  this->key_ = "fcat_" + std::to_string(hash);
}

void SearchQueryFeature::SetTimeout(ContextData* session_data) {
    this->timeout_ = 10;
}

void SearchQueryFeature::SetTable(ContextData* session_data) {
  this->table_ = "adSearchQueryFeature";
}

void SearchQueryFaceAuthor::SetKey(ContextData* session_data) {
  std::string query = session_data->ad_request->search_info().query();
  this->key_ = query;
}

void SearchQueryFaceAuthor::SetTimeout(ContextData* session_data) {
    this->timeout_ = 10;
}

void SearchQueryFaceAuthor::SetTable(ContextData* session_data) {
  this->table_ = "adSearchQueryAuthorMerchant";
}


void SearchQueryCategory::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_query_merchant_cate",
      session_data->abtest_user_info, "cate_");
  std::string query_key = session_data->ad_request->search_info().query();
  query_key = prefix + query_key;
  this->key_ = query_key;
}


void AppCardQuery::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void AppCardQuery::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void AppCardQuery::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "app_card_query_prefix",
      session_data->abtest_user_info, "app_");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  query_key = prefix + query_key;
  this->key_ = query_key;
}

void SearchSingleColQuery::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void SearchSingleColQuery::SetTable(ContextData* session_data) {
  this->table_ = "adSearchRelevancePhotoEmb";
}
void SearchSingleColQuery::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "single_col_query_prefix",
      session_data->abtest_user_info, "singleq_");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  query_key = prefix + query_key;
  this->key_ = query_key;
}

void SearchDoubleColUser::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void SearchDoubleColUser::SetTable(ContextData* session_data) {
  this->table_ = "adSearchRelevancePhotoEmb";
}
void SearchDoubleColUser::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "double_col_user_prefix",
      session_data->abtest_user_info, "du_");
  std::string id_key = std::to_string(session_data->ad_request->ad_user_info().id());
  id_key = prefix + id_key;
  this->key_ = id_key;
}


void AppCardQuerySug::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void AppCardQuerySug::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void AppCardQuerySug::SetKey(ContextData* session_data) {
  this->key_.clear();
  std::string query_key = session_data->sug_origin_query;
  if (query_key.length() < 6) {
    return;
  }
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "app_card_query_prefix",
      session_data->abtest_user_info, "app_");

  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  query_key = prefix + query_key;
  this->key_ = query_key;
}

void AppCardPlatformQuery::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void AppCardPlatformQuery::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void AppCardPlatformQuery::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "app_card_platform_query_prefix",
      session_data->abtest_user_info, "app_plat_v1_");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  query_key = prefix + query_key;
  this->key_ = query_key;
}

void FormInnerPlayletQuery::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void FormInnerPlayletQuery::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void FormInnerPlayletQuery::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "inner_playlet_query_prefix",
      session_data->abtest_user_info, "inner_playlet_v0_");
  std::string query_key = session_data->ad_request->search_info().query();
  std::string query_sign = std::to_string(base::CityHash64(query_key.c_str(), query_key.length()));
  this->key_ = absl::Substitute("$0$1", prefix, query_sign);
}

void FormDpaStrongCard::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void FormDpaStrongCard::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void FormDpaStrongCard::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "dpa_form_strong_card_prefix",
      session_data->abtest_user_info, "dpa_form_v1_");
  std::string query_key = session_data->ad_request->search_info().query();
  std::string query_sign = std::to_string(base::CityHash64(query_key.c_str(), query_key.length()));
  this->key_ = absl::Substitute("$0$1", prefix, query_sign);
}

void AppDpaStrongCard::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void AppDpaStrongCard::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void AppDpaStrongCard::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "dpa_strong_card_prefix",
      session_data->abtest_user_info, "dpa_platform_v1_");
  std::string query_key = session_data->ad_request->search_info().query();
  std::string query_sign = std::to_string(base::CityHash64(query_key.c_str(), query_key.length()));
  this->key_ = absl::Substitute("$0$1", prefix, query_sign);
}

void QueryCategory::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void QueryCategory::SetTable(ContextData* session_data) {
  this->table_ = "queryCategory";
}
void QueryCategory::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_query_category_prefix",
      session_data->abtest_user_info, "v1_");
  std::string query_key = session_data->ad_request->search_info().query();
  this->key_ = absl::Substitute("$0$1", prefix, query_key);
}

void FormCardExactQuery::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void FormCardExactQuery::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void FormCardExactQuery::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "form_card_exact_query_prefix",
      session_data->abtest_user_info, "form_exact_");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  query_key = prefix + query_key;
  this->key_ = query_key;
}

void FormCardBroadQuery::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void FormCardBroadQuery::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void FormCardBroadQuery::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "form_card_broad_query_prefix",
      session_data->abtest_user_info, "form_broad_");
  std::string query_key = session_data->ad_request->search_info().query();
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  query_key = prefix + query_key;
  this->key_ = query_key;
}

void FormCardQuerySug::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void FormCardQuerySug::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void FormCardQuerySug::SetKey(ContextData* session_data) {
  this->key_.clear();
  std::string query_key = session_data->sug_origin_query;
  if (query_key.length() < 6) {
    return;
  }
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "form_card_exact_query_prefix",
      session_data->abtest_user_info, "form_exact_");
  transform(query_key.begin(), query_key.end(), query_key.begin(), ::tolower);
  query_key = prefix + query_key;
  this->key_ = query_key;
}


void SearchQueryCategory::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void SearchQueryCategory::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}

void SearchQueryGoodsIntent::SetKey(ContextData* session_data) {
  std::string prefix = "goods_";
  std::string query_key = session_data->ad_request->search_info().query();
  query_key = prefix + query_key;
  this->key_ = query_key;
}

void SearchQueryGoodsIntent::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void SearchQueryGoodsIntent::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}
void SearchQueryIndustryId::SetKey(ContextData* session_data) {
  std::string prefix = "industry_";
  std::string query_key = session_data->ad_request->search_info().query();
  query_key = prefix + query_key;
  this->key_ = query_key;
}

void SearchQueryIndustryId::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void SearchQueryIndustryId::SetTable(ContextData* session_data) {
  this->table_ = "adLiveStreamSearchMerchantQuery";
}

void AdBidwordExtend::SetKey(ContextData* session_data) {
  std::string prefix = ks::abtest::AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "search_ad_bidword_extend_prefix",
      session_data->abtest_user_info, "br_v0_");
  std::string query_hash = std::to_string(base::CityHash64(session_data->norm_lower_query.c_str(),
        session_data->norm_lower_query.length()));
  std::string query_key = prefix + query_hash;
  this->key_ = query_key;
}

void AdBidwordExtend::SetTimeout(ContextData* session_data) { this->timeout_ = 10; }

void AdBidwordExtend::SetTable(ContextData* session_data) {
  this->table_ = "adBidwordExtend";
}

}  // namespace query_retrieval
}  // namespace ks
