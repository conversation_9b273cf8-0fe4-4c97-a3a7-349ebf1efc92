#pragma once
#include <string>

#include "teams/ad/query_retrieval/src/engine/context_data/context_data.h"
#include "teams/ad/query_retrieval/src/engine/utility/redis/redis_wrapper.h"

namespace ks {
namespace query_retrieval {

class BaseParams {
 public:
  virtual void SetTable(ContextData* sesssion_data) = 0;
  virtual void SetKey(ContextData* sesssion_data) = 0;
  virtual void SetTimeout(ContextData* sesssion_data) = 0;
  void SetDependDataParams(ad_base::DependDataParams dparams) { dparams_ = dparams; }
  std::string GetTable() { return table_; }
  std::string GetKey() { return key_; }
  int32_t GetTimeout() { return timeout_; }
  int64_t GetRange() { return range_; }

 public:
  std::string table_;
  std::string key_;
  ad_base::DependDataParams dparams_{ad_base::DependDataLevel::WEAK_DEPEND};
  int32_t timeout_{10};  // ms
  int64_t range_{0};     // 当使用 zset 时，需要 range
};

#define ASYNC_REDIS_PARAM_REGISTER(class_name)            \
  class class_name : public BaseParams {                  \
   public:                                                \
    void SetTable(ContextData* sesssion_data) override;   \
    void SetKey(ContextData* sesssion_data) override;     \
    void SetTimeout(ContextData* sesssion_data) override; \
    void Init(ContextData* sesssion_data) {               \
      SetTable(sesssion_data);                            \
      SetKey(sesssion_data);                              \
      SetTimeout(sesssion_data);                          \
    }                                                     \
  };

ASYNC_REDIS_PARAM_REGISTER(SearchQueryUserList);
ASYNC_REDIS_PARAM_REGISTER(SearchLiveQueryTag);
ASYNC_REDIS_PARAM_REGISTER(SearchLiveTabWhiteList);
ASYNC_REDIS_PARAM_REGISTER(SearchLiveBigVCard);  // 直播大 V 卡
ASYNC_REDIS_PARAM_REGISTER(SearchLiveBigVCardDirect);  // 直播大 V 卡
ASYNC_REDIS_PARAM_REGISTER(SearchLiveBigVCardWithType);  // 直播大 V 卡
ASYNC_REDIS_PARAM_REGISTER(SearchLiveBigVCardSug);  // 直播大 V 卡
ASYNC_REDIS_PARAM_REGISTER(SearchUserEmbedding);  // 用户 embedding
ASYNC_REDIS_PARAM_REGISTER(SearchQueryCategory);  // query 类目
ASYNC_REDIS_PARAM_REGISTER(SearchQueryGoodsIntent);  // query 商品意图
ASYNC_REDIS_PARAM_REGISTER(SearchQueryIndustryId);  // query 行业分类
ASYNC_REDIS_PARAM_REGISTER(SearchQueryFeatureCount);  // query 统计类特征
ASYNC_REDIS_PARAM_REGISTER(AdBidwordExtend);  // query bidword extend
ASYNC_REDIS_PARAM_REGISTER(AppCardQuery);  // 下载 query 对应的 productlist
ASYNC_REDIS_PARAM_REGISTER(AppCardQuerySug);  // 下载 query 对应的 productlist
ASYNC_REDIS_PARAM_REGISTER(AppCardPlatformQuery);  // 下载平台类 query 对应的 productlist
ASYNC_REDIS_PARAM_REGISTER(SearchQueryFeature);  // query 特征
ASYNC_REDIS_PARAM_REGISTER(SearchQueryFaceAuthor);  // query 人脸对应的历史 author
ASYNC_REDIS_PARAM_REGISTER(FormCardExactQuery);  // 表单 query 对应的精确 productlist
ASYNC_REDIS_PARAM_REGISTER(FormCardBroadQuery);  // 表单 query 对应的非精确 productlist
ASYNC_REDIS_PARAM_REGISTER(FormCardQuerySug);  // 表单 query 对应的 productlist
ASYNC_REDIS_PARAM_REGISTER(SearchAkgQueryEmbedding);  // Akg 搜索图 embedding
ASYNC_REDIS_PARAM_REGISTER(SearchGoodsQueryRewrite);  // 电商 query 改写
ASYNC_REDIS_PARAM_REGISTER(FormInnerPlayletQuery);  // 表单列表 query 对应的自建链路短剧 list
ASYNC_REDIS_PARAM_REGISTER(FormDpaStrongCard);  // 表单类 dpa 强样式
ASYNC_REDIS_PARAM_REGISTER(AppDpaStrongCard);  // 下载类 dpa 强样式
ASYNC_REDIS_PARAM_REGISTER(QueryCategory);  // query 类目信息
ASYNC_REDIS_PARAM_REGISTER(SearchKboxQuery);  // query 类目信息
ASYNC_REDIS_PARAM_REGISTER(SearchAigcProduct);  // query aigc 产品名对应文本
ASYNC_REDIS_PARAM_REGISTER(SearchQueryHotSequenceFeature);  // query hot sequence 特征
ASYNC_REDIS_PARAM_REGISTER(OriQueryQuantizeId);  // 原始 query 量化 id
ASYNC_REDIS_PARAM_REGISTER(SearchSingleColQuery);  // 单列广告 query
ASYNC_REDIS_PARAM_REGISTER(SearchDoubleColUser);  // 双列广告 user
ASYNC_REDIS_PARAM_REGISTER(GoodsBigvQuery);  // 商品 tab 直播大卡词
}  // namespace query_retrieval
}  // namespace ks
