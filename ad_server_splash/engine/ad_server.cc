#include <kess/common/logger.h>
#include <kess/rpc/rpc_facade.h>
#include <kess/rpc/grpc/grpc_server_builder.h>
#include <stdlib.h>

#include <algorithm>
#include <iterator>
#include <memory>
#include <ostream>
#include <string>
#include <utility>
#include <vector>

/********************** 不要移动 *******************************/
// #include "teams/ad/ad_server_splash/engine/server/init.h"
// #include "teams/ad/ad_server_splash/engine/server/node_register.h"
// #include "teams/ad/ad_server_splash/engine/strategy/init.h"
// #include "teams/ad/engine_base/frequency_capping/plugin/init.h"
/***************************************************/
#include "teams/ad/ad_server_splash/engine/bg_task.h"
#include "teams/ad/ad_server_splash/engine/warm_up.h"
#include "base/common/base.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "teams/ad/ad_base/src/abtest/abtest_mocker.h"
#include "teams/ad/ad_base/src/common/gracefull_exit.h"
#include "teams/ad/ad_base/src/common/deploy_utils.h"
#include "teams/ad/ad_base/src/profile/hotspots_service.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/ksp/ad_web_request_handler.h"
#include "teams/ad/ad_base/src/ksp/dynamic_port.h"
#include "teams/ad/ad_base/src/traffic_record/traffic_record.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_base/src/taskflow/task_group.h"

#include "teams/ad/ad_server_splash/engine/ad_grpc.h"
#include "teams/ad/ad_server_splash/engine/ad_service.h"
#include "teams/ad/ad_server_splash/util/kconf/kconf.h"
#include "src/kuaishou_lib/support.h"
#include "base/common/sleep.h"
#include "gflags/gflags.h"
#include "glog/logging.h"
#include "kess/rpc/failover_policy.h"
#include "net/web_server/web_server.h"
#include "kconf/kconf_flags.h"

#include "dragon/src/common_reco_handler.h"
#include "dragon/src/common_reco_handler_queue.h"
#include "dragon/src/common_reco_grpc_service.h"
#include "dragon/src/core/sub_flow_thread_pool_manager.h"

#include "serving_base/thp_component/kthp.h"

extern bool ks_flag_grpc_force_use_epollsig;
extern bool ks_flag_grpc_disable_http2_hpack;
extern bool ks_flag_grpc_server_as_observer;

DEFINE_int32(seconds_waiting_for_kess_unregister, 120, "seconds waiting for kess unregister");
DEFINE_int32(grpc_server_port, 20082, "the port for grpc service");
DEFINE_int32(web_server_port, 20080, "the port for web service");
DEFINE_int32(grpc_thread_num, 100, "the number of threads for grpc service");
DEFINE_int32(web_thread_num, 1, "the number of threads for web service");
DEFINE_bool(disable_all_perf, false, "disable all perf");
DECLARE_int32(ksp_group_deploy_type);
DECLARE_string(p2p_release_kconf_node);
DECLARE_int64(attr_value_size_growth_threshold);
DECLARE_double(attr_value_size_growth_factor);
DECLARE_int64(attr_value_size_growth_step);
DECLARE_bool(zero_copy_subflow_output);
DECLARE_double(sub_flow_thread_num_per_worker);

DEFINE_bool(is_nodiff_amd_deploy, false, "amd nodiff switch");
DEFINE_INF_KCONF_bool("ad.adtarget3.enableZeroCopySubflowOutput",
                      enableZeroCopySubflowOutput,
                      false,
                      "enable zero_copy_subflow_output");

DEFINE_INF_KCONF_int32("ad.adserver.rpcThreadNum",
                      rpcThreadNum,
                      0,
                      "rpcThreadNum");

DEFINE_INF_KCONF_int32("ad.adserver.sub_flow_thread_num_per_worker",
                      sub_flow_thread_num_per_worker,
                      0,
                      "sub_flow_thread_num_per_worker");


namespace common = ks::kess::common;
namespace scheduler = ks::kess::scheduler;
namespace rpc = ks::kess::rpc;

namespace ks {
namespace ad_server {
net::WebServer::Options init_web_server_option() {
  net::WebServer::Options web_server_option;
  web_server_option.port = ks::ad_base::DynamicPort::Instance().Port("AUTO_PORT0");
  if (web_server_option.port <= 0) {
    web_server_option.port = FLAGS_web_server_port;
  }
  web_server_option.backlog = 1024;
  return web_server_option;
}

}  // namespace ad_server
}  // namespace ks

int main(int argc, char* argv[]) {
  ks_flag_grpc_force_use_epollsig = true;
  ks_flag_grpc_enable_tcp_user_timeout = true;
  ks_flag_grpc_tcp_user_timeout_ms = 1000;

  base::InitApp(&argc, &argv, "ad server");
  if (ks::ad_server::AdKconfUtil::enableKthpv2()) {
    CHECK(serving_base::KTHP::Init()) << "init thp2.0 failed";
  }
  if (ks::ad_server::print_dragon_node()) {
    return 0;
  }
  if (ks::ad_server::AdKconfUtil::enableDragonResize()) {
    FLAGS_attr_value_size_growth_threshold = ks::ad_server::AdKconfUtil::dragonResizeThreshold();
    FLAGS_attr_value_size_growth_step = ks::ad_server::AdKconfUtil::dragonResizeStep();
  }
  FLAGS_zero_copy_subflow_output = kconf_enableZeroCopySubflowOutput();
  ks::ad_base::abtest_mocker::InitAbtestMocker();

  falcon::SetMaxCounterNumber(40960);
  LOG(INFO) << "start to warm up";
  ::ks::ad_server::ad_warm_up();
  LOG(INFO) << "warm up finished";
  ::google::FlushLogFiles(::google::INFO);
  auto ksp_group = ks::ad_base::AdKessClient::Instance().GetKspGroup();
  FLAGS_ksp_group_deploy_type = ks::ad_base::GetDeployType(ksp_group);

  LOG(INFO) << "deploy type :" << FLAGS_ksp_group_deploy_type;
  if (FLAGS_ksp_group_deploy_type  == 0) {
    LOG(FATAL) << "deploy type unknown";
    return -1;
  }

  if (!ks::ad_server::StartBgTask()) {
    LOG(INFO) << "start_bg_task failed";
    return -1;
  }
  LOG(INFO) << "start_bg_task finished";

  // 启动 web service
  ks::ad_base::DefaultAdWebService web_service(FLAGS_web_thread_num,
    []()-> serving_base::WebRequestHandlerDict* {
      return ks::ad_server::AdWebServiceHandlerDict::CreateHandler();
    });
  net::WebServer::Options web_server_option = ks::ad_server::init_web_server_option();

  net::WebServer web_server(web_server_option, &web_service);
  web_server.Start();
  LOG(INFO) << "web server started";

  // combo 流量录制
  ks::infra::traffic::grpc::FilterPlugin::Init(
    std::make_unique<ks::ad_base::GrpcRecordPlugin>());

  // 启动 grpc service
  ks::ad_base::AdKessClient &kess_instance = ks::ad_base::AdKessClient::Instance();
  ::ks::ad_server::warm_up_component();

  // dragon service
  auto thread_num = ks::ad_base::AdKessClient::Instance().get_server().thread_num;
  if (kconf_rpcThreadNum()) {
    thread_num = kconf_rpcThreadNum();
  }
  if (kconf_sub_flow_thread_num_per_worker()) {
    FLAGS_sub_flow_thread_num_per_worker = kconf_sub_flow_thread_num_per_worker() / 1000.0;
  }
  auto grpc_cq_num = ks::ad_base::AdKessClient::Instance().get_server().grpc_cq_num;
  int handle_queue_capacity = thread_num;
  if (grpc_cq_num > 1) {
    handle_queue_capacity *= grpc_cq_num;
  }

  auto task_group_thread_num = ks::ad_server::AdKconfUtil::taskGroupThreadNum();
  if (task_group_thread_num < 1 || task_group_thread_num > 64) {
    ks::ad_base::TaskGroup::set_thread_num(2);
  } else {
    ks::ad_base::TaskGroup::set_thread_num(task_group_thread_num);
  }

  int32_t worker_thread_num =
      static_cast<int32_t>(ks::ad_server::AdKconfUtil::grpcWorkerThreadRatio() * thread_num);
  int32_t subflow_thread_num =
      static_cast<int32_t>(ks::ad_server::AdKconfUtil::dragonSubflowThreadRatio() * thread_num);
  LOG(INFO) << "worker_thread_num: " << worker_thread_num << ", subflow_thread_num: " << subflow_thread_num;
  ks::platform::GlobalHolder::SetWorkerThreadNum(subflow_thread_num);
  if (FLAGS_parallel_init_pipeline_concurrency > 0) {
    CHECK(ks::platform::DualLevelThreadPool::GetInstance()->Initialize());
  }
  ks::platform::SubFlowThreadPoolManager::GetInstance()->Initialize();

  auto handler_queue = std::make_shared<ks::platform::CommonRecoHandlerQueue<
      ks::platform::CommonRecoHandler>>(handle_queue_capacity);
  auto df_grpc_service =
      std::make_unique<ks::platform::CommonRecoGrpcServiceImpl>(worker_thread_num, handler_queue);
  // 启动自己和 dragon
  std::vector<ks::kess::rpc::grpc::Service*> grpc_service_list;
  grpc_service_list.reserve(1);
  grpc_service_list.push_back(df_grpc_service.get());
  if (handler_queue) {
    handler_queue->WaitForInitDone();
  }
  LOG(INFO) << "start kess service";
  ::google::FlushLogFiles(::google::INFO);
  kess_instance.StartService(grpc_service_list, web_server_option.port);

  // grpc service 停止
  kess_instance.StopService();

  // web server 停止
  web_server.Stop();

  // 后台线程 停止
  ks::ad_server::StopBgTask();
  LOG(INFO) << "ad server safely quit";
  ::google::FlushLogFiles(::google::INFO);
  ::google::ShutdownGoogleLogging();
  ks::ad_base::GracefulShutdown();
  return 0;
}
