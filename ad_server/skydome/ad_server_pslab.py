# coding=utf-8
from ks_leaf_functional.core.module import module
from dragonfly.common_leaf_dsl import LeafFlow
from dragonfly.decorators import parallel, async_retrieve, async_enrich, async_mix
from ad_server_mixin import AdServerMixin
from rpc_client.ad_rank_client import param as ad_rank_rpc_params
from rpc_client.ad_rank_client import param_with_attr as ad_rank_rpc_param_with_attr
from rpc_client.ad_rank_client import param_with_split_attr as ad_rank_rpc_param_with_split_attr
from rpc_client.ad_rank_client import param_with_attr_diff as ad_rank_rpc_param_with_attr_diff
from rpc_client.ad_target_client import *
from rpc_client.ad_prerank_client import *
from component.prerank_rpc_mixin import PrerankRpcMixin
from ad_rpc_client_mixin import AdRpcClientMixin  # teams/ad/engine_base/dragon_node/py/ad_rpc_client_mixin.py
from dragonfly.ext.ad_router.ad_router_mixin import AdRouterApiMixin

class TargetCommonDefine:
    kTsmSceneOuterPhoto = "outer_photo"
    kTsmSceneInnerHardPhoto = "inner_hard_photo"
    kTsmSceneInnerSoftPhoto = "inner_soft_photo"
    kTsmSceneLive = "live"

class AdServerPsLab(LeafFlow, AdServerMixin, AdRpcClientMixin, AdRouterApiMixin, PrerankRpcMixin):
  def __init__(self, name, item_table):
    LeafFlow.__init__(self, name=name, item_table=item_table)
    # 策略放 QuotaLimit 前面 !!!
    self.inner_photo_tsm_strategies = [
        "AutoParam",
        "BasicCheck",
        "Degrade",
        "CacheLimit",
        "UserLevelCheck",
        "OpCheck",
        "InnerMobileV3IndexExp",
        "InnovationFlowLimit",
        "LowUserLimit",
        "QuotaLimit",
    ]
    self.live_tsm_strategies = [
        "AutoParam",
        "BasicCheck",
        "Degrade",
        "CacheLimit",
        "OpCheck",
        "InnerMobileV3IndexExp",
        "InspireLiveRatioFit",
        "InnovationFlowLimit",
        "LowUserLimit",
        "QuotaLimit",
    ]
    self.default_tsm_strategies = [
        "AutoParam",
        "BasicCheck",
        "Degrade",
        "CacheLimit",
        "InnovationFlowLimit",
        "QuotaLimit",
    ]
    self.item_card_tsm_strategies = [
        "AutoParam",
        "BasicCheck",
        "Degrade",
        "CacheLimit",
        "UserLevelCheck",
        "OpCheck",
        "InnerMobileV3IndexExp",
        "InnovationFlowLimit",
        "LowUserLimit",
        "QuotaLimit",
    ]
    self.common_tsm_input_attr = common_tsm_input_attrs + user_feature_common_attrs
    self.live_tsm_output_attr = [
        "live_tsm_context_pb",
        "live_mpc_path_token_list",
        "live_mpc_bucket_size",
        "trigger_token_compression_level",
        "live_trigger_token",
        "live_prerank_redis_cache",
        "live_ensemble_params_str",
        "live_config_str",
        "eco_preselect_params",
        "effect_preselect_params",
        "photo_effect_preselect_params",
        "auto_param_expid",        
    ]
    self.inner_photo_tsm_output_attr = [
        "inner_soft_photo_tsm_context_pb",
        "inner_hard_photo_tsm_context_pb",
        "inner_photo_mpc_path_token_list",
        "inner_photo_mpc_bucket_size",
        "trigger_token_compression_level",
        "inner_photo_trigger_token",
        "inner_hard_photo_prerank_redis_cache",
        "inner_soft_photo_prerank_redis_cache",
        "inner_hard_photo_ensemble_params_str",
        "inner_soft_photo_ensemble_params_str",
        "inner_hard_photo_config_str",
        "inner_soft_photo_config_str",
        "eco_preselect_params",
        "effect_preselect_params",
        "photo_effect_preselect_params",
        "auto_param_expid",        
    ]
    self.outer_photo_tsm_output_attr = [
        "outer_photo_tsm_context_pb",
        "outer_photo_mpc_path_token_list",
        "outer_photo_mpc_bucket_size",
        "trigger_token_compression_level",
        "outer_photo_trigger_token",
        "outer_photo_prerank_redis_cache",
        "outer_photo_ensemble_params_str",
        "outer_photo_config_str",
        "eco_preselect_params",
        "effect_preselect_params",
        "photo_effect_preselect_params",
        "auto_param_expid",        
    ]
    self.item_card_tsm_output_attr = [
        "inner_soft_photo_tsm_context_pb",
        "inner_hard_photo_tsm_context_pb",
        "item_card_mpc_path_token_list",
        "item_card_mpc_bucket_size",
        "trigger_token_compression_level",
        "item_card_trigger_token",
        "inner_hard_photo_prerank_redis_cache",
        "inner_hard_photo_ensemble_params_str",
        "inner_soft_photo_ensemble_params_str",
        "inner_hard_photo_config_str",
        "inner_soft_photo_config_str",
        "eco_preselect_params",
        "effect_preselect_params",
        "photo_effect_preselect_params",
        "auto_param_expid",
    ]
    for i in range(50):
      self.outer_photo_tsm_output_attr.append(
          f"outer_photo_mpc_path_token_list_{i}",
      )
      self.outer_photo_tsm_output_attr.append(
          f"outer_photo_mpc_path_token_list_{i}_size",
      )
      self.live_tsm_output_attr.append(
          f"live_mpc_path_token_list_{i}",
      )
      self.live_tsm_output_attr.append(
          f"live_mpc_path_token_list_{i}_size",
      )
      self.inner_photo_tsm_output_attr.append(
          f"inner_photo_mpc_path_token_list_{i}",
      )
      self.inner_photo_tsm_output_attr.append(
          f"inner_photo_mpc_path_token_list_{i}_size",
      )
      self.item_card_tsm_output_attr.append(
          f"item_card_mpc_path_token_list_{i}",
      )
      self.item_card_tsm_output_attr.append(
          f"item_card_mpc_path_token_list_{i}_size",
      )


  @module()
  def ranking_use_rpc_client(self):
    self\
      .ranking_prepare()\
      .ranking()\
      .if_("enable_rank_migration_stage ~= nil and enable_rank_migration_stage > 0")\
        .post_process()\
      .end_()\
      .if_("enable_rank_migration_stage == nil or enable_rank_migration_stage < 4")\
        .if_("ad_rank_ksn ~= nil")\
          .if_("ad_server_to_rank_item_attr_flag == 1")\
            .ad_rpc_client_mixer(** ad_rank_rpc_param_with_attr_diff)\
          .else_if_("ad_server_to_rank_item_attr_flag > 1")\
            .if_("enable_split_ad_server_attr == 1") \
              .ad_rpc_client_mixer(** ad_rank_rpc_param_with_split_attr)\
            .else_()\
              .ad_rpc_client_mixer(** ad_rank_rpc_param_with_attr)\
            .end_()\
          .else_()\
            .ad_rpc_client_mixer(** ad_rank_rpc_params)\
          .end_()\
        .end_()\
      .end_()
    return self

  @module()
  def predict_process(self):
    self\
      .predict_feature_build_mixer(
        user_table_name = "user_feature_table",
        context_table_name = "context_feature_table"
      )\
      .ad_router_bs_trans_enricher(save_async_status_to = "bs_trans_async_status")\
      .ad_router_request_prepare(
        item_table_name = "predict_item_table",
        predict_request_attr_name = "predict_request",
        arena_attr_name = "rpc_arena",
      )\
      .cmd_manager_enricher(
        input_table_name = "predict_item_table",
        output_table_name= "predict_cmd_table"
      )\
      .cmd_model_register(
        register_table = [
          dict(
            item_table_name = "predict_item_table",
            cmd_table_name = "predict_cmd_table"
          )
        ]
      )\
      .item_feature_build_mixer(
        item_table_name = "predict_item_table",
        cmd_table_name = "predict_cmd_table",
        item_feature_table_name = "item_feature_table"
      )\
      .ad_router_send_request_mixer(
        predict_request_attr_name = "predict_request",
        arena_attr_name = "rpc_arena",
        cmd_table_name = "predict_cmd_table",
        merge_user_info_name = "merge_user_info",
        predict_response_attr_name = "predict_response",
        save_async_status_to = "predict_async_status",
        item_table_name = "predict_item_table",
        user_feature_table_name = "user_feature_table",
        context_feature_table_name = "context_feature_table",
        item_feature_table_name = "item_feature_table",
        async_status_attr = "bs_trans_async_status",
      )\
      .ad_router_response_proc(
        cmd_table_name = "predict_cmd_table",
        item_table_name = "predict_item_table",
        predict_request_attr_name = "predict_request",
        predict_response_attr_name = "predict_response",
        async_status_attr = "predict_async_status"
      )
    return self

  @module()
  def pslab_graph_ad_server_splash_no_bid(self, name):
    """
    .case_("AdServerSplashNoBid").pslab_graph_ad_server_splash_no_bid(name = "AdServerSplashNoBid")\
    """
    self\
      .ad_admit_node()\
      .splash_adx_handler()\
      .splash_adx_check()\
      .target_handler()\
      .splash_bid_handler()\
      .if_("is_prefetch_splash_traffic == 1") \
        .j_k_plan_handler()\
      .else_()\
        .j_k_plan_splash_handler()\
      .end_if_()\
      .ranking_use_rpc_client()\
      .if_("enable_rank_migration_stage == nil or enable_rank_migration_stage <= 2")\
        .splash_data_post_proc()\
        .post_proc()\
      .end_if_()

    return self

  def outer_photo_global_filter(self, item_table, guarantee_quota):
    # 创建逻辑表, 记录过滤原因 filter_condition
    self.set_attr_default_value(
      item_table=item_table,
      item_attrs=[
        {
          "name": "filter_condition",
          "type": "int",
          "value": 0,
        }
      ] 
    ).create_logic_table(
      logic_table=f"{item_table}_logic_table",
      item_table=item_table,
      select_attr=["filter_condition"],
    )
    # 分组优选, 对齐 ad_target 的多样性过滤
    self.keep_top_n_by_group(
      item_table=item_table,
      guarantee_quota=guarantee_quota,
    ).rta()
    self.perflog_attr_value(
      item_table=f"{item_table}_logic_table",
      check_point=f"{item_table}_after_filter",
      item_attrs=["filter_condition"],
      aggregator="count",
    )
    return self
  
  def inner_photo_global_filter(self, item_table, prerank_quota, enable_p2l_in_photo):
    # 创建逻辑表, 记录过滤原因 filter_condition
    self.set_attr_default_value(
      item_table=item_table,
      item_attrs=[
        {
          "name": "filter_condition",
          "type": "int",
          "value": 0,
        }
      ]
    ).create_logic_table(
      logic_table=f"{item_table}_logic_table",
      item_table=item_table,
      select_attr=["filter_condition"],
    )
    # 分组优选, 对齐 ad_target_inner_photo 的多样性过滤
    self.keep_top_n_by_group_for_inner_photo(
      item_table=item_table,
      prerank_quota=prerank_quota,
      enable_p2l_in_photo=enable_p2l_in_photo,
    )
    self.perflog_attr_value(
      item_table=f"{item_table}_logic_table",
      check_point="after_filter",
      item_attrs=["filter_condition"],
      aggregator="count",
    )
    return self  

  def perflog_outer_photo_item_table_size(self, stage):
    self.count_reco_result(
      item_table="outer_hard_photo_item_table",
      save_count_to="outer_hard_photo_item_table_size",
    ).perflog(
      mode="interval",
      value="{{outer_hard_photo_item_table_size}}",
      namespace="ad.ad_server",
      subtag="outer_hard_photo_item_table_size",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    ).count_reco_result(
      item_table="outer_soft_photo_item_table",
      save_count_to="outer_soft_photo_item_table_size",           
    ).perflog(
      mode="interval",
      value="{{outer_soft_photo_item_table_size}}",
      namespace="ad.ad_server",
      subtag="outer_soft_photo_item_table_size",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    )
    return self
  
  def perflog_outer_photo_item_table_empty(self, stage):
    self.count_reco_result(
      item_table="outer_hard_photo_item_table",
      save_count_to="outer_hard_photo_item_table_size",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'outer_hard_photo_item_table_size_gt0_' .. tostring(outer_hard_photo_item_table_size > 0)}}",
      extra1=stage,
      extra6="{{sub_page_id_str}}",
    ).count_reco_result(
      item_table="outer_soft_photo_item_table",
      save_count_to="outer_soft_photo_item_table_size",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'outer_soft_photo_item_table_size_gt0_' .. tostring(outer_soft_photo_item_table_size > 0)}}",
      extra1=stage,
      extra6="{{sub_page_id_str}}",
    )
    return self

  def perflog_outer_photo_item_table_size_one_model(self, stage):
    self.count_reco_result(
      item_table="outer_hard_photo_one_model_online_item_table",
      save_count_to="outer_hard_photo_item_table_size",
    ).perflog(
      mode="interval",
      value="{{outer_hard_photo_item_table_size}}",
      namespace="ad.ad_server",
      subtag="outer_hard_photo_item_table_size_one_model",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'rome_oh_photo_item_table_size_gt0_' .. tostring(outer_hard_photo_item_table_size > 0)}}",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    ).count_reco_result(
      item_table="outer_soft_photo_one_model_online_item_table",
      save_count_to="outer_soft_photo_item_table_size",           
    ).perflog(
      mode="interval",
      value="{{outer_soft_photo_item_table_size}}",
      namespace="ad.ad_server",
      subtag="outer_soft_photo_item_table_size_one_model",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'rome_os_photo_item_table_size_gt0_' .. tostring(outer_soft_photo_item_table_size > 0)}}",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    )
    return self
  
  def perflog_outer_photo_item_table_empty_one_model(self, stage):
    self.count_reco_result(
      item_table="outer_hard_photo_one_model_online_item_table",
      save_count_to="outer_hard_photo_item_table_size",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'outer_hard_photo_one_model_online_item_table_size_gt0_' .. tostring(outer_hard_photo_item_table_size > 0)}}",
      extra1=stage,
      extra6="{{sub_page_id_str}}",
    ).count_reco_result(
      item_table="outer_soft_photo_one_model_online_item_table",
      save_count_to="outer_soft_photo_item_table_size",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'outer_soft_photo_one_model_online_item_table_size_gt0_' .. tostring(outer_soft_photo_item_table_size > 0)}}",
      extra1=stage,
      extra6="{{sub_page_id_str}}",
    )
    return self

  def perflog_inner_photo_item_table_size(self, stage):
    self.count_reco_result(
      item_table="inner_photo_hard_item_table",
      save_count_to="inner_photo_hard_item_table_size",
    ).perflog(
      mode="interval",
      value="{{inner_photo_hard_item_table_size}}",
      namespace="ad.ad_server",
      subtag="inner_photo_hard_item_table_size",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    ).count_reco_result(
      item_table="inner_photo_soft_item_table",
      save_count_to="inner_photo_soft_item_table_size",
    ).perflog(
      mode="interval",
      value="{{inner_photo_soft_item_table_size}}",
      namespace="ad.ad_server",
      subtag="inner_photo_soft_item_table_size",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    )
    return self
  
  def perflog_inner_photo_fanstop_size(self, stage):
    self.count_reco_result(
      item_table="inner_photo_hard_item_table",
      save_count_to="inner_photo_hard_item_table_fanstop_size",
      target_item={
        "campaign_type": [17, 18, 20, 21, 22, 23]
      },
    ).perflog(
      mode="interval",
      value="{{inner_photo_hard_item_table_fanstop_size}}",
      namespace="ad.ad_server",
      subtag="inner_photo_hard_item_table_fanstop_size",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    ).count_reco_result(
      item_table="inner_photo_soft_item_table",
      save_count_to="inner_photo_soft_item_table_fanstop_size",
      target_item={
        "campaign_type": [17, 18, 20, 21, 22, 23]
      },      
    ).perflog(
      mode="interval",
      value="{{inner_photo_soft_item_table_fanstop_size}}",
      namespace="ad.ad_server",
      subtag="inner_photo_soft_item_table_fanstop_size",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    )
    return self
  
  def perflog_inner_photo_item_table_empty(self, stage):
    self.count_reco_result(
      item_table="inner_photo_hard_item_table",
      save_count_to="inner_photo_hard_item_table_size",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'inner_photo_hard_item_table_size_gt0_' .. tostring(inner_photo_hard_item_table_size > 0)}}",
      extra1=stage,
      extra6="{{sub_page_id_str}}",
    ).count_reco_result(
      item_table="inner_photo_soft_item_table",
      save_count_to="inner_photo_soft_item_table_size",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'inner_photo_soft_item_table_size_gt0_' .. tostring(inner_photo_soft_item_table_size > 0)}}",
      extra1=stage,
      extra6="{{sub_page_id_str}}",
    )
    return self
  
  def perflog_inner_photo_item_table_size_one_model(self, stage):
    self.count_reco_result(
      item_table="inner_photo_hard_one_model_online_item_table",
      save_count_to="inner_photo_hard_item_table_size",
    ).perflog(
      mode="interval",
      value="{{inner_photo_hard_item_table_size}}",
      namespace="ad.ad_server",
      subtag="inner_photo_hard_item_table_size_one_model",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    ).count_reco_result(
      item_table="inner_photo_soft_one_model_online_item_table",
      save_count_to="inner_photo_soft_item_table_size",
    ).perflog(
      mode="interval",
      value="{{inner_photo_soft_item_table_size}}",
      namespace="ad.ad_server",
      subtag="inner_photo_soft_item_table_size_one_model",
      extra1=stage,
      extra2="{{searcher_exp_tag}}",
      extra6="{{sub_page_id_str}}",
    )
    return self

  def perflog_inner_photo_item_table_empty_one_model(self, stage):
    self.count_reco_result(
      item_table="inner_photo_hard_one_model_online_item_table",
      save_count_to="inner_photo_hard_item_table_size",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'inner_photo_hard_one_model_online_item_table_size_gt0_' .. tostring(inner_photo_hard_item_table_size > 0)}}",
      extra1=stage,
      extra6="{{sub_page_id_str}}",
    ).count_reco_result(
      item_table="inner_photo_soft_one_model_online_item_table",
      save_count_to="inner_photo_soft_item_table_size",
    ).perflog(
      mode="count",
      value=1,
      namespace="ad.ad_server",
      subtag = "{{return 'inner_photo_soft_one_model_online_item_table_size_gt0_' .. tostring(inner_photo_soft_item_table_size > 0)}}",
      extra1=stage,
      extra6="{{sub_page_id_str}}",
    )
    return self
