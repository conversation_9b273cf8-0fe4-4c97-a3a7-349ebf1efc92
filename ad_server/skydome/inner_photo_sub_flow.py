from ad_server_pslab import AdServer<PERSON><PERSON>ab, TargetCommonDefine
from dragonfly.common_leaf_util import extract_attr_names
from rpc_client.ad_target_client import *
from component.inner_hard_prerank_move_params import (
  hard_prerank_process,
  hard_prerank_call_rpc,
  hard_prerank_proc_rpc,
)
from component.inner_soft_prerank_move_params import(
  soft_prerank_process,
  soft_prerank_call_rpc,
  soft_prerank_proc_rpc,
)


# 对应 ad_target inner_photo_flow
inner_photo_sub_flow = AdServerPsLab(name="inner_photo", item_table="item_table")
inner_photo_sub_flow.namespace_("inner_photo")

# 对应 MODE_MERGE
inner_photo_sub_flow.if_(
  "inner_photo_target_normal_admit == 1 and inner_photo_target_native_admit == 1"
)

inner_photo_sub_flow.namespace_("merge", True)
inner_photo_sub_flow.prerank_redis_cache_init(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
  key_biz_prefix="inner_hard",
  cache_redis_cluster_name="adEnginePrerankCache",
  serialize_to="inner_hard_photo_prerank_redis_cache",
).prerank_redis_cache_init(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
  key_biz_prefix="inner_soft",
  cache_redis_cluster_name="adEnginePrerankCache",
  serialize_to="inner_soft_photo_prerank_redis_cache",
).ensemble_params_prepare(
  biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
  ensemble_params_attr=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_ensemble_params",
  serialize_to="inner_hard_photo_ensemble_params_str",
).ensemble_params_prepare(
  biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
  ensemble_params_attr=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_ensemble_params",
  serialize_to="inner_soft_photo_ensemble_params_str",
).tsm_param_prepare(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
  multi_path_config=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config",
  shiba_ab_biz=["AMD"],
).tsm_param_prepare(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
  multi_path_config=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config",
  shiba_ab_biz=["FANSTOP"],
)
for strategy in inner_photo_sub_flow.inner_photo_tsm_strategies:
  inner_photo_sub_flow.tsm_path_check(
    name=f"inner_merge_hard_photo_path_check_{strategy}",
    tsm_biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
    path_strategy=strategy,
    shiba_ab_biz=["AMD"],
    multi_path_config=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config",
  )
  inner_photo_sub_flow.tsm_path_check(
    name=f"inner_merge_soft_photo_path_check_{strategy}",
    tsm_biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
    path_strategy=strategy,
    shiba_ab_biz=["FANSTOP"],
    multi_path_config=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config",
  )
inner_photo_sub_flow.serialize_multi_path_config(
  multi_path_config_attr=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config",
  serialize_to=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config_str",
).serialize_multi_path_config(
  multi_path_config_attr=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config",
  serialize_to=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config_str",
)
inner_photo_sub_flow.if_("move_trigger_to_ad_server_mode > 0")
inner_photo_sub_flow.build_multi_path_context(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
  ensemble_params_attr=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_ensemble_params",
  multi_path_config_attr=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config",
  multi_path_context_attr="inner_photo_mpc",
).build_multi_path_context(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
  ensemble_params_attr=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_ensemble_params",
  multi_path_config_attr=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config",
  multi_path_context_attr="inner_photo_mpc",
).trigger(
  name="ps_trigger",
  trigger_type="ps",
  output="inner_photo_ps_trigger",
  multi_path_context_attr="inner_photo_mpc",
).trigger(
  name="kvc_trigger",
  trigger_type="kvc",
  output="inner_photo_kvc_trigger",
  multi_path_context_attr="inner_photo_mpc",
)
inner_photo_sub_flow.log_debug_info(
  common_attrs=["inner_photo_ps_trigger", "inner_photo_kvc_trigger"]
)
inner_photo_sub_flow.serialize_path_token_list(
  multi_path_context_attr="inner_photo_mpc",
  serialize_to="inner_photo_path_token_list_str",
)
inner_photo_sub_flow.end_if_()
inner_photo_sub_flow.namespace_()

# 对应 MODE_HARD
inner_photo_sub_flow.else_if_("inner_photo_target_normal_admit == 1")

inner_photo_sub_flow.namespace_("hard", True)
inner_photo_sub_flow.prerank_redis_cache_init(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
  key_biz_prefix="inner_hard",
  cache_redis_cluster_name="adEnginePrerankCache",
  serialize_to="inner_hard_photo_prerank_redis_cache",
).ensemble_params_prepare(
  biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
  ensemble_params_attr=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_ensemble_params",
  serialize_to="inner_hard_photo_ensemble_params_str",
).tsm_param_prepare(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
  multi_path_config=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config",
  shiba_ab_biz=["AMD"],
)
for strategy in inner_photo_sub_flow.inner_photo_tsm_strategies:
  inner_photo_sub_flow.tsm_path_check(
    tsm_biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
    path_strategy=strategy,
    shiba_ab_biz=["AMD"],
    multi_path_config=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config",
  )
inner_photo_sub_flow.serialize_multi_path_config(
  multi_path_config_attr=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config",
  serialize_to=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config_str",
)

inner_photo_sub_flow.if_("move_trigger_to_ad_server_mode > 0")
inner_photo_sub_flow.build_multi_path_context(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerHardPhoto,
  ensemble_params_attr=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_ensemble_params",
  multi_path_config_attr=f"{TargetCommonDefine.kTsmSceneInnerHardPhoto}_config",
  multi_path_context_attr="inner_photo_mpc",
).trigger(
  name="ps_trigger",
  trigger_type="ps",
  output="inner_photo_hard_ps_trigger",
  multi_path_context_attr="inner_photo_mpc",
).trigger(
  name="kvc_trigger",
  trigger_type="kvc",
  output="inner_photo_hard_kvc_trigger",
  multi_path_context_attr="inner_photo_mpc",
)
inner_photo_sub_flow.log_debug_info(
  common_attrs=["inner_photo_hard_ps_trigger", "inner_photo_hard_kvc_trigger"]
)
inner_photo_sub_flow.serialize_path_token_list(
  multi_path_context_attr="inner_photo_mpc",
  serialize_to="inner_photo_path_token_list_str",
)
inner_photo_sub_flow.end_if_()
inner_photo_sub_flow.namespace_()

# 对应 MODE_SOFT
inner_photo_sub_flow.else_if_("inner_photo_target_native_admit == 1")

inner_photo_sub_flow.namespace_("soft", True)
inner_photo_sub_flow.prerank_redis_cache_init(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
  key_biz_prefix="inner_soft",
  cache_redis_cluster_name="adEnginePrerankCache",
  serialize_to="inner_soft_photo_prerank_redis_cache",
).ensemble_params_prepare(
  biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
  ensemble_params_attr=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_ensemble_params",
  serialize_to="inner_soft_photo_ensemble_params_str",
).tsm_param_prepare(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
  multi_path_config=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config",
  shiba_ab_biz=["FANSTOP"],
)
for strategy in inner_photo_sub_flow.inner_photo_tsm_strategies:
  inner_photo_sub_flow.tsm_path_check(
    tsm_biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
    path_strategy=strategy,
    shiba_ab_biz=["FANSTOP"],
    multi_path_config=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config",
  )
inner_photo_sub_flow.serialize_multi_path_config(
  multi_path_config_attr=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config",
  serialize_to=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config_str",
)
inner_photo_sub_flow.if_("move_trigger_to_ad_server_mode > 0")
inner_photo_sub_flow.build_multi_path_context(
  tsm_biz=TargetCommonDefine.kTsmSceneInnerSoftPhoto,
  ensemble_params_attr=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_ensemble_params",
  multi_path_config_attr=f"{TargetCommonDefine.kTsmSceneInnerSoftPhoto}_config",
  multi_path_context_attr="inner_photo_mpc",
).trigger(
  name="ps_trigger",
  trigger_type="ps",
  output="inner_photo_soft_ps_trigger",
  multi_path_context_attr="inner_photo_mpc",
).trigger(
  name="kvc_trigger",
  trigger_type="kvc",
  output="inner_photo_soft_kvc_trigger",
  multi_path_context_attr="inner_photo_mpc",
)
inner_photo_sub_flow.log_debug_info(
  common_attrs=["inner_photo_soft_ps_trigger", "inner_photo_soft_kvc_trigger"]
)
inner_photo_sub_flow.serialize_path_token_list(
  multi_path_context_attr="inner_photo_mpc",
  serialize_to="inner_photo_path_token_list_str",
)
inner_photo_sub_flow.end_if_()
inner_photo_sub_flow.namespace_()

inner_photo_sub_flow.end_if_()

inner_photo_sub_flow.if_("enable_nearline_write_ads == 1 and enable_inner_photo_nearline == 1")  # 近线 if
inner_photo_sub_flow.prepare_inner_photo_target_rpc(
  request_attr=inner_photo_target_request
).ad_rpc_client_mixer(
  **inner_photo_target_rpc_params_nearline
)
inner_photo_sub_flow.else_()  # 近线 else

inner_photo_sub_flow.if_("enable_prerank_trans_exp == 1")  # 粗排上移实验 if

inner_photo_sub_flow.if_("switch_inner_photo_target_to_searcher == 1")  # 分片实验 if
inner_photo_sub_flow.prepare_inner_photo_searcher_rpc().ad_shard_rpc_client(
  **inner_photo_searcher_rpc_params
).log_debug_info(
  common_attrs=["ad-searcher-inner-photo.status"]
).merge_shard_common_attrs(
  shard_num=2,
  shard_id_prefix="",
).set_attr_value(
  common_attrs=[
    {
      "name": "searcher_exp_tag",
      "type": "string",
      "value": "exp",
    }
  ]
).perflog_inner_photo_item_table_size(
  stage="after_retrieve"
).perflog_inner_photo_fanstop_size(
  stage="after_retrieve"
).inner_photo_global_filter(
  item_table="inner_photo_hard_item_table",
  prerank_quota="{{inner_photo_hard_prerank_quota}}",
  enable_p2l_in_photo=True,
).inner_photo_global_filter(
  item_table="inner_photo_soft_item_table",
  prerank_quota="{{inner_photo_soft_prerank_quota}}",
  enable_p2l_in_photo=False,
)
inner_photo_sub_flow.else_()  # 分片实验 else
inner_photo_sub_flow.prepare_inner_photo_target_rpc(
  request_attr=inner_photo_target_request,
).ad_rpc_client_mixer(
  **inner_photo_target_rpc_params_without_prerank
).set_attr_value(
  common_attrs=[
    {
      "name": "searcher_exp_tag",
      "type": "string",
      "value": "base",
    }
  ]
).perflog_inner_photo_item_table_size(
  stage="after_retrieve"
).perflog_inner_photo_fanstop_size(
  stage="after_retrieve"
)
inner_photo_sub_flow.end_if_()  # 分片实验 end if

inner_photo_sub_flow.perflog_inner_photo_item_table_size(
  stage="before_prerank"
).perflog_inner_photo_fanstop_size(
  stage="before_prerank"
)

inner_photo_sub_flow.if_(
  "use_packed_boolean_value == 1"
).parse_packed_boolean_value(
  item_table="inner_photo_hard_item_table"
).parse_packed_boolean_value(
  item_table="inner_photo_soft_item_table"
).end_if_()
inner_photo_sub_flow.if_(
  "inner_photo_target_normal_admit == 1 and inner_photo_target_native_admit == 1"
).count_reco_result(
  item_table="inner_photo_hard_item_table",
  save_count_to="inner_photo_hard_item_table_size",
).count_reco_result(
  item_table="inner_photo_soft_item_table",
  save_count_to="inner_photo_soft_item_table_size",
).if_(
  "enable_prerank_trans_filter_empty_request ~= 1 or inner_photo_hard_item_table_size > 0"
).do(
  hard_prerank_call_rpc(flow=inner_photo_sub_flow, save_async_status_to="hard_prerank_async")
).end_if_(
).if_(
  "enable_prerank_trans_filter_empty_request ~= 1 or inner_photo_soft_item_table_size > 0"
).do(
  soft_prerank_call_rpc(flow=inner_photo_sub_flow, save_async_status_to="soft_prerank_async")
).end_if_(
).log_debug_info(
  common_attrs=["soft_prerank_async", "hard_prerank_async"]
).if_(
  "enable_prerank_trans_filter_empty_request ~= 1 or inner_photo_hard_item_table_size > 0"
).do(
  hard_prerank_proc_rpc(flow=inner_photo_sub_flow)
).end_if_(
).if_(
  "enable_prerank_trans_filter_empty_request ~= 1 or inner_photo_soft_item_table_size > 0"
).do(
  soft_prerank_proc_rpc(flow=inner_photo_sub_flow)
).end_if_(
).else_if_(
  "inner_photo_target_native_admit == 1"
).count_reco_result(
  item_table="inner_photo_soft_item_table",
  save_count_to="inner_photo_soft_item_table_size",
).if_(
  "enable_prerank_trans_filter_empty_request ~= 1 or inner_photo_soft_item_table_size > 0"
).do(
  soft_prerank_process(inner_photo_sub_flow)
).end_if_(
).else_if_(
  "inner_photo_target_normal_admit == 1"
).count_reco_result(
  item_table="inner_photo_hard_item_table",
  save_count_to="inner_photo_hard_item_table_size",
).if_(
  "enable_prerank_trans_filter_empty_request ~= 1 or inner_photo_hard_item_table_size > 0"
).do(
  hard_prerank_process(inner_photo_sub_flow)
).end_if_(
).end_if_()

inner_photo_sub_flow.perflog_inner_photo_item_table_size(
  stage="after_prerank"
).perflog_inner_photo_fanstop_size(
  stage="after_prerank"
)
inner_photo_sub_flow.else_()  # 粗排上移实验 else

inner_photo_sub_flow.prepare_inner_photo_target_rpc(request_attr=inner_photo_target_request)
inner_photo_sub_flow.ad_rpc_client_mixer(**inner_photo_target_rpc_params)

inner_photo_sub_flow.end_if_()  # 粗排上移实验 end if
inner_photo_sub_flow.end_if_()  # 近线 end if

inner_photo_sub_flow_pass_common_attrs = inner_photo_sub_flow.common_tsm_input_attr + [
  "inner_photo_mpc",
  "enable_inner_photo_nearline",
  "inner_photo_target_normal_admit",
  "inner_photo_target_native_admit",
]

def merge_conflict_common_attr_rename(attrs):
  ret = list(extract_attr_names(attrs, "as"))
  ret.remove("enable_prerank_rank_ica")
  ret.append(
    {
      "name": "enable_prerank_rank_ica",
      "as": "inner_photo.enable_prerank_rank_ica",
    }
  )
  ret.remove("tab_type")
  ret.append(
    {
      "name": "tab_type",
      "as": "inner_photo.tab_type",
    }
  )
  return ret

inner_photo_sub_flow_merge_common_attrs = merge_conflict_common_attr_rename(inner_photo_target_recv_common_attrs) + [
  "inner_photo_sub_flow_output",
  "request_success_target_inner_photo",
  "request_target_inner_photo_time_cost",
  "request_status_target_inner_photo",
  "debug_creative_reason",
]
inner_photo_sub_flow_retrieve_tables = [
  {
    "table_name": "inner_photo_soft_item_table",
    "attrs": inner_photo_target_soft_recv_attrs,
  },
  {
    "table_name": "inner_photo_hard_item_table",
    "attrs": inner_photo_target_hard_recv_attrs,
  },
]
