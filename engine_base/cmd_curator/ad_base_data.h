#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "float.h"  //  NOLINT

#include "absl/hash/hash.h"
#include "absl/container/flat_hash_map.h"
#include "absl/types/optional.h"
#include "glog/logging.h"
#include "base/time/timestamp.h"

#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/engine_base/cmd_curator/ad_enum.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"

namespace ks {
namespace engine_base {

struct CmdFuncInner {
  int32_t order;
  bool next_op_is_and;
  bool expect_value;
};
struct CmdPatternChunkInner {
  std::vector<CmdFuncInner> cmd_func;
  bool next_op_is_and;
  bool expect_value;
};
struct CmdPatternInner {
  std::vector<CmdPatternChunkInner> cmd_pattern_chunk;
  bool next_op_is_and;
  bool expect_value;
};
struct AdCmdConfigInner {
  std::vector<CmdPatternInner> pv_cmd_pattern;
  std::vector<CmdPatternInner> creative_cmd_pattern;
  std::vector<int32_t> predict_type;
};

// 请求源类型
enum class AdRequestType {
  GRPC_GetAdResult = 0,
  GRPC_GetAdPhotoResponse = 1,
  GRPC_GetAdCreative = 2,
  WEB_RetrievalDebug = 10,
};

/*
BETTER_ENUM(PredictType,
  int32_t,
  PredictType_Unknown = 0,
  PredictType_ctr = 1,
  PredictType_cvr = 2,
  PredictType_play3s = 3,
  PredictType_ltr = 4,
  PredictType_wtr = 5,
  PredictType_delivery_rate = 6,
  // unuse PredictType_htr = 7,
  PredictType_app_conversion_rate = 8,
  PredictType_c1_conv_fix = 9,  // 一跳激活修偏预估值
  PredictType_cpr = 10,
  PredictType_ncer = 11,
  PredictType_landingpage_submit = 12,
  PredictType_retention = 13,
  PredictType_purchase = 14,
  // unuse PredictType_cpa_bid = 15,
  // PredictType_multitask_play3s = 16,
  // PredictType_multitask_ltr = 17,
  // PredictType_multitask_wtr = 18,
  PredictType_credit = 19,
  PredictType_click_credit = 20,
  PredictType_click_wanjian = 21,
  PredictType_credit_conv_grant = 22,
  PredictType_credit_click2 = 23,
  PredictType_credit_eval = 24,
  PredictType_click_insur_purchase = 25,  // 保险点击付费
  PredictType_c2_insur_purchase = 26,     // 保险二跳付费
  PredictType_ntr = 27,
  PredictType_app_conversion_rate_calibration = 28,
  PredictType_click_retention = 29,
  PredictType_click_purchase = 30,
  PredictType_apr = 31,                     // 淘系电商付费概率
  PredictType_prod_apr = 32,                // 淘系电商产品化 apr
  PredictType_shop_action = 33,             // 电商加购 & 下单 & 购买概率
  PredictType_special_lps = 34,             // 单独表单模型预测的表单概率
  PredictType_deep_credit_spec_model = 35,  // 深度授信 cold_start
  PredictType_server_show_cvr = 36,         // server show 到二跳的概率
  PredictType_adx_scvr = 37,                // adx scvr 的概率
  PredictType_click2_conv = 38,             // click2 --> conv
  PredictType_click2_lps = 39,              // click2 --> lps
  PredictType_click2_prod_apr = 40,         // click2 --> prod_apr
  PredictType_conv2_purchase = 41,          // conv --> purchase
  PredictType_lps2_purchase = 42,           // conv --> purchase
  PredictType_clientshow_click2 = 43,       // clientshow --> click2
  PredictType_click2_shop_action = 44,      // click2 --> shop_action
  PredictType_click2_purchase = 45,         // click2 --> purchase
  PredictType_click2_nextstay = 46,         // click2 --> stay
  PredictType_server_show_ctr = 47,         // server_show 到一跳的概率
  PredictType_deep_rate = 48,               // 注册率
  PredictType_click2_deep_rate = 49,        // 注册率
  PredictType_click2_purchase_device = 50,
  PredictType_click_purchase_device = 51,
  PredictType_conv_ltv = 52,                         // 内部产品激活 ltv 预估值
  PredictType_purchase_ltv = 53,                     // 联盟付费 ltv 预估值
  // PredictType_twin_purchase_ltv = 54,                // 联盟付费 ROI 双出价 ltv 预估值
  PredictType_ug_conv_pltv7 = 55,                    // 联盟增长七日 ltv 预估值
  PredictType_conv_key_inapp_action_rate = 56,       // 联盟精排激活关键行为预估值
  PredictType_7_day_pay_times = 57,                  // 付费次数
  PredictType_game_purchase_ltv = 58,                // 游戏付费 ltv 预估值
  PredictType_preranking_purchase_ltv = 59,          // 付费 ltv 预估值粗排模型
  PredictType_inner_cpr = 60,                        // 内部产品激活付费率预估
  PredictType_c1_merchant_follow = 61,               // 一跳到 follow 的预估
  PredictType_nebula_merchant_follow = 62,           // **trick**, 现为 item_impression->follow
  PredictType_merchant_follow_ocpm = 63,             // server_show->follow , ocpm
  PredictType_c1_order_paid = 64,                    // 一跳到购买的预估
  PredictType_c2_order_paid = 65,                    // 2 跳到商品购买的预估
  PredictType_c2_button_click = 66,                  // 2 跳到按钮点击
  PredictType_c1_button_click = 67,                  // 1 跳到按钮点击
  PredictType_click_purchase_rate_single_bid = 68,   // 一跳付费单出价
  PredictType_click2_purchase_rate_single_bid = 69,  // 二跳付费单出价
  PredictType_conv_nextstay = 70,                    // 激活到次留概率
  PredictType_c1_prerank_unified = 71,               // 一跳粗排统一模型预估值
  PredictType_c2_prerank_unified = 72,               // 二跳粗排统一模型预估值
  PredictType_prerank_ecpm_ctr = 73,                 // 粗排 ecpm ctr
  PredictType_prerank_ecpm_live_ctr = 74,            // 粗排 ecpm ctr
  PredictType_prerank_ecpm_cvr = 75,                 // 粗排 ecpm cvr
  PredictType_nebula_prerank_merchant_follow = 76,   // 单列粗排小店通涨粉模型
  PredictType_nebula_prerank_purchase = 77,          // 单列粗排付费模型
  PredictType_prerank_thanos_xdt_order_paied = 78,   // 单列粗排小店通订单支付模型
  PredictType_jinniu_prerank_score = 79,             // 粗排金牛模型
  // PredictType_feed_prerank_merchant_follow = 80,     // 双列粗排小店通涨粉模型
  // PredictType_feed_prerank_purchase = 81,            // 双列粗排付费模型
  PredictType_server_client_show_rate = 82,          // server_show -> client_show
  PredictType_live_goods_view = 83,                  // 链接点击
  PredictType_live_pay_rate = 84,                    // 直播购买
  PredictType_live_audience = 85,                    // 直播间进人
  PredictType_live_play3s = 86,
  PredictType_live_server_show_play3s_feed = 87,   // server_show-> live_p3s  feed
  PredictType_live_server_show_play3s_slide = 88,  // server_show-> live_p3s  slide
  PredictType_live_server_show_wtr = 89,           // server_show-> wtr
  PredictType_live_p3s_wtr = 90,                   // live_p3s -> wtr
  PredictType_item_impression_wtr = 91,            // item_imp -> wtr
  PredictType_game_appoint_rate = 92,              // 一跳到游戏预约率
  PredictType_c2_game_appoint_rate = 93,           // 二跳到游戏预约率
  PredictType_profile_tr = 94,
  // PredictType_shop_cart_tr = 95,
  // unuse PredictType_cmt_tr = 96,
  // unuse PredictType_plc_tr = 97,
  // PredictType_photo2live_tr = 98,
  // PredictType_live_play5s = 99,
  PredictType_new_creative_delivery_rate = 100,     // 新创意粗排下发率
  // PredictType_new_creative_cvr = 101,               // 新创意粗排转化率
  PredictType_merchant_ltv = 102,                   // 电商 ltv 预估值
  PredictType_live_p3s_ltv = 103,                   // 电商直播 ltv 预估值
  PredictType_shop_live_prerank_p3s = 104,          // 小店直播 p3s 粗排模型
  PredictType_photo_to_live_prerank_p3s = 105,      // 短视频直播 p3s
  PredictType_live_prerank_inroom_nebula = 106,     // 短视频直播进房率 滑滑
  PredictType_live_prerank_inroom_feed = 107,       // 短视频直播进房率 双列
  PredictType_live_prerank_p3s_wtr = 108,           // 直播粗排 p3s -> follow
  PredictType_photo_to_live_item_imp_wtr = 109,     // 短视频直播粗排 item_imp -> follow
  // PredictType_photo_to_live_item_pay_rate = 110,    // 短视频直播粗排 item_imp -> order
  PredictType_photo_to_live_server_show_ctr = 111,  // 短视频直播粗排 server_show -> item_imp
  PredictType_photo_to_live_item_imp_p3s = 112,     // 短视频直播粗排 item_imp -> p3s
  // PredictType_prerank_p3s_goods_view = 113,         // 直播 p3s -> 商品访问 粗排
  PredictType_prerank_p3s_pay_rate = 114,           // 直播 p3s -> 直播购买 粗排
  PredictType_prerank_live_p3s_ltv = 115,           // 直播 p3s -> ltv 粗排
  PredictType_click_app_invoked = 116,              // 一跳到唤端
  PredictType_click2_app_invoked = 117,             // 二跳到唤端
  // unuse PredictType_asso_shop_cart_tr = 118,              // 作品引流的购物车点击率
  // PredictType_thanos_prerank_conv = 119,
  // PredictType_thanos_prerank_lps = 120,
  // PredictType_thanos_prerank_follow = 121,
  // PredictType_thanos_prerank_purchase = 122,
  // PredictType_thanos_prerank_deep = 123,
  PredictType_gmv = 124,  // 用户小店购买 gmv
  // unuse PredictType_thanos_prerank_scvr = 125,
  PredictType_universe_prerank_deep = 126,
  PredictType_prerank_ctr = 127,                   // 粉条粗排 ctr
  PredictType_prerank_wtr = 128,                   // 粉条粗排 wtr
  PredictType_prerank_photo2live_tr = 129,         // 粉条粗排 photo2live_tr
  PredictType_prerank_reco_score = 130,            // 粉条粗排 reco_score
  PredictType_federated_cvr = 131,                 // 联邦学习转化率
  PredictType_edu_lps2pay_cvr = 132,               // 教育 ecpc 表单付费率
  PredictType_edu_pay_ecpc_cvr = 133,              // 教育付费 ecpc
  // unuse PredictType_interact = 134,                      // 互动率
  PredictType_lps_valid_clues = 135,               // LA 行业表单提交->后端转化预估
  PredictType_prerank_pred_ad_time = 136,          // 预估广告播放时长
  // unuse PredictType_live_deep_cvr = 137,                 //  deep cvr    live
  PredictType_prerank_p2l_shop_tr = 138,           // 粉条粗排 photo2live_tr 卡片点击率
  PredictType_prerank_gmv = 139,                   // 粉条粗排 用户小店购买 gmv
  PredictType_playtime = 140,                      // 播放时长
  // unuse PredictType_live_jump_ctr = 141,                 // 简易直播间跳转标准直播间
  PredictType_download_ctr = 142,                  // 下载点击
  PredictType_consult_ctr = 143,                   // 咨询点击
  // unuse PredictType_clue_ctr = 144,                      // 线索提交
  PredictType_order_deal_tr = 145,                 // 订单支付率
  // PredictType_prerank_wanjian_shouxin_ecpc = 146,  //  完件单出价粗排单拆
  // PredictType_prerank_biaodan_shouxin_rate = 147,  //  粗排授信单出价的表单切分
  PredictType_universe_prerank_invoke = 148,       //联盟唤端粗排
  PredictType_prerank_live_gmv = 149,              //粉条直播直投 ecpm 粗排 gmv
  PredictType_prerank_live_p3r = 150,              //粉条直播直投 ecpm 粗排 p3r
  PredictType_prerank_live_audience = 151,         //粉条直播直投 ecpm 粗排直播间进人
  PredictType_prerank_live_wtr = 152,              // 粉条直播直投 ecpm 粗排涨粉
  PredictType_prerank_live_shop_cart_tr = 153,     // 粉条直播直投 ecpm 粗排小黄车点击
  // PredictType_ecom_quality = 154,                  // 联盟泛化模型预估分
  PredictType_prerank_cpm_ltr = 155,
  // unuse PredictType_paycnt = 156,                         // rank 下单次数预估
  PredictType_conv_key_action = 157,                // 激活关键行为率
  // unuse PredictType_photo2live_tr_bypass = 158,           // 作品引流直播间模型旁路模型
  // PredictType_prerank_new_creative_lps = 159,       // 粗排冷启动表单
  // PredictType_prerank_new_creative_conv = 160,      // 粗排冷启动激活
  // PredictType_prerank_new_creative_deep = 161,      // 粗排冷启动深度
  PredictType_up_model_no_pay_rate = 162,           // up 模型不付费的概率
  PredictType_up_model_low_pay_rate = 163,          // up 模型小 R 的概率，付费 6 元以下
  PredictType_up_model_medium_pay_rate = 164,       // up 模型中 R 的概率，付费 6-200 元以下
  PredictType_up_model_high_pay_rate = 165,         // up 模型大 R 的概率，付费大于 200 元以下
  PredictType_inner_loop_ecpc_imp_conv_rate = 166,  // 内循环 ecpc imp_conv 模型预估值
  PredictType_video_play_7s = 167,                  // 播放 7s 模型预估值，直播头辅助短视频
  PredictType_short_video_play_7s = 168,            // 播放 7s 模型预估值，仅是短视频
  PredictType_effective_play_7s = 169,  // 播放 7s 模型预估值，有效播放维度下，直播与短视频一个头
  PredictType_video_effective_play_7s = 170,  // 播放 7s 模型预估值，有效播放维度下，直播头辅助短视频
  PredictType_short_video_effective_play_7s = 171,  // 播放 7s 模型预估值，有效播放维度下，仅是短视频
  // unuse PredictType_live_play5s_bypass = 172,             // 直播直投直播间进人直播间模型旁路模型
  // unuse PredictType_shop_cart_tr_bypass = 173,            // 直播直投商品点击直播间模型旁路模型
  // unuse PredictType_wtr_bypass = 174,                     // 粉条涨粉旁路模型
  // unuse PredictType_live_rez_succ_r = 175,                // 直播预约成功率
  PredictType_universe_prerank_event_order = 176,         // 联盟短视频订单
  PredictType_universe_prerank_ad_merchant_follow = 177,  //联盟粗排涨粉
  PredictType_up_model_high_ltv_rate = 178,               // 联盟 up 模型高 ltv 用户概率
  PredictType_merchant_model_ltv_rate = 179,              // 联盟 内循环 模型 ltv 模型
  PredictType_universe_prerank_key_action = 180,          // 联盟关键行为
  PredictType_universe_prerank_item_click = 181,          // 联盟二跳
  PredictType_universe_prerank_ctcvr = 182,               // 联盟回归 ctcvr
  PredictType_up_model_conv_key_inapp_rate = 183,         // up 模型激活关键行为率预估值
  PredictType_universe_prerank_jiuniu_roas = 184,         // 联盟金牛 roas 目标
  // unuse PredictType_live_play_1m_r = 185,                       // 粉条直播长播 1 分钟
  PredictType_up_model_lps_intention_rate = 186,          // up 模型表单到意向确认率
  PredictType_up_model_conv_retention_rate = 187,          // 激活次留 mcda up 助攻模型
  PredictType_auc_model_base = 188,                        // 交叉 auc base
  PredictType_auc_model_exp = 189,                         // 交叉 auc exp
  PredictType_prerank_orderpay = 190,                   // 粉条粗排 用户小店订单支付
  PredictType_live_prerank_stay_time = 191,                  // 直播粗排 直播间停留时长
  PredictType_prerank_follow_ltv = 192,                 // 粗排电商 follow_ltv 预估值
  PredictType_univ_prerank_xdt_live_audience = 193,  //联盟直播粗排进人
  PredictType_univ_prerank_xdt_live_pay = 194,  //联盟直播粗排订单
  PredictType_univ_prerank_xdt_live_roas = 195,  //联盟直播粗排 roas
  PredictType_click2_unified_rate = 196,                  // 粗排统一 cvr
  PredictType_prerank_ctcvr = 197,                   // 粉条粗排 ctcvr 模型
  PredictType_prerank_other = 198,                   // 粉条粗排其他模型（如：cpm or ltr 等）
  PredictType_prerank_unify_ltr = 199,                   // 粗排统一 ltr
  PredictType_middle_platform = 200,                   // 中台模型
  PredictType_prerank_hard_live_ctcvr = 201,           // 硬广直播 ctcvr 模型
  PredictType_cascade_pctr = 202,  // 内粉请求 reco 粗排 pctr 分数
  PredictType_cascade_pltr = 203,  // 内粉请求 reco 粗排 pltr 分数
  PredictType_cascade_pwtr = 204,  // 内粉请求 reco 粗排 pwtr 分数
  PredictType_cascade_plvtr = 205,  // 内粉请求 reco 粗排 plvtr 分数
  PredictType_cascade_psvtr = 206,  // 内粉请求 reco 粗排 psvtr 分数
  PredictType_merchant_follow_ltv = 207,  // 涨粉 ltv 分数
  PredictType_7_day_game_conv_ltv = 208,  // 激活之后的 7 日 ltv 预估值
  PredictType_key_action_ltv0 = 209,  // 关键行为 ltv0 预估值
  PredictType_non_merchant_live_ctr = 210,  // 非电商直播 ctr
  PredictType_non_merchant_live_sctr = 211,  // 非电商直播推广 scr 模型预估值
  PredictType_click2_purchase_mt = 212,  // 多触点二跳付费单出价
  PredictType_non_merchant_live_deep_cvr = 213,  // 非电商直播推广 deep cvr 预>估值
  PredictType_non_merchant_live_cvr = 214,  // 非电商直播 cvr
  PredictType_conv_24h_stay = 215,  // 24 小时留存预估值
  PredictType_conv_7_day_stay = 216,  // 激活到 7 留预估值
  PredictType_lps_phone_connect_rate = 217,  // 表单到电话建联预估值
  PredictType_p3r = 218,  // p3s 预估值
  PredictType_third_party_up_model_predict_rate = 219,  // up 三方数据助攻模型>预估值
  PredictType_click_novel_score = 220,  // 小说行业阅读辅助模型
  PredictType_conv_quality_score = 221,  //  转化质量分模型
  PredictType_appinvoke_nextstay = 222,
  PredictType_game_conv_ltv = 223,  // 激活之后的 ltv 预估值
  PredictType_app_conversion_reward = 224,  // 激励视频级联 激活
  PredictType_landingpage_submit_reward = 225,  // 激励视频级联 表单
  PredictType_deep_credit = 226,
  PredictType_insurance_surrender = 227,
  PredictType_merchant_order_ltv = 228,   // 订单支付 ltv 预估值
  PredictType_photo2live_pay_rate = 229,  // live_p3s -> photo2live_paied
  PredictType_bk_server_show_ctr = 230,  // sctr 预估值
  PredictType_game_purchase_7_day_ltv = 231,  // 游戏付费 7 日 ltv 预估值
  PredictType_brand_order_paid = 232,  // 电商品牌一跳到购买的预估
  PredictType_spu_order_paid = 233,   // spu 粒度预估购买率
  PredictType_author_order_paid = 234,    // 店铺粒度预估购买率
  PredictType_order_paid = 235,  // 订单支付
  PredictType_item_impression_nextstay = 236,  // item impression 下的次留概率
  PredictType_item_conv_duration = 237,  // item conv 后的用户停留时长
  // PredictType_stay_time_5p5 = 238,  // 间隔为 5.5 时当天停留时长
  // PredictType_stay_time_6 = 239,  // 间隔为 6 时当天停留时长
  // PredictType_stay_time_7 = 240,  // 间隔为 7 时当天停留时长
  PredictType_assemble_page_ctr = 241,  // 聚合页点击率预估
  PredictType_assemble_pk_cvr = 242,  // 聚合页 cvr 预估，用于与单品页 pk
  // PredictType_general_ntr = 245,  // 精排泛化 ntr 模型
  PredictType_purchase_confid_q = 246,  // 付费置信度模型
  PredictType_item_ctr = 247,  // 精排 item_click 模型
  PredictType_first_click_score = 248,  // 外循环电商点击归因曝光到二跳模型
  PredictType_multi_touch_score = 249,  // 外循环电商多触点模型
  PredictType_shop_jump = 250,  // 商品点击
  PredictType_merchant_auxiliary_cvr = 251,  // 电商辅助 cvr 预估值
  PredictType_click2_irreg_alllesson = 252,  // 正价课 二跳 模型预估值
  PredictType_live_booking_rate = 253,  // 直播预约模型预估值
  PredictType_universe_lps_2deep = 255,  // 联盟 ecpc - 表单超深度预估值
  PredictType_jinniu_lps = 256,  // 联盟金牛表单提交
  PredictType_predict_future_cvr = 257,  // 内循环电商未来 24 小时 cvr 预估值
  PredictType_live_room_stay_1m = 258,  //  直播间停留 1 m
  PredictType_universe_mcda_united_type_1_rate = 259,  // 联盟 mcda 统一模型一方数据预估值
  PredictType_universe_mcda_united_type_2_rate = 260,  // 联盟 mcda 统一模型二方数据预估值
  PredictType_universe_mcda_united_type_3_rate = 261,  // 联盟 mcda 统一模型三方数据预估值
  PredictType_plc = 262,  // plc 点击
  PredictType_live_res_succ = 263,  // 直播预约成功
  PredictType_leads_submit = 264,  // 线索提交
  PredictType_live_reward = 265,  // 直播打赏
  PredictType_cmtr = 266,  // 评论
  PredictType_live_play_1m = 267,  // 粉条长播 1m
  PredictType_universe_conv_register_rate = 268,  // 联盟 ecpc 激活注册率
  PredictType_universe_appinvoke_nextstay_rate = 269,  // 联盟 ecpc 唤端注册率
  PredictType_rule_id1_score = 270,  // format model 预估值
  PredictType_rule_id2_score = 271,
  PredictType_rule_id3_score = 272,
  PredictType_rule_id4_score = 273,
  PredictType_rule_id5_score = 274,
  PredictType_rule_id6_score = 275,
  PredictType_rule_id7_score = 276,
  PredictType_rule_id8_score = 277,
  PredictType_rule_id9_score = 278,
  PredictType_rule_id10_score = 279,
  PredictType_auto_params_cpm_factor = 280,  // 精排排序系数
  PredictType_search_sctr_pos1_sctr1 = 281,  // 搜索分位置预估 V2 pos1 滑滑下发率
  PredictType_search_sctr_pos1_sctr2 = 282,  // 搜索分位置预估 V2 pos1 点击下发率
  PredictType_search_sctr_pos2_sctr1 = 283,  // 搜索分位置预估 V2 pos2 滑滑下发率
  PredictType_search_sctr_pos2_sctr2 = 284,  // 搜索分位置预估 V2 pos2 点击下发率
  PredictType_search_sctr_pos5_sctr1 = 285,  // 搜索分位置预估 V2 pos5 滑滑下发率
  PredictType_search_sctr_pos5_sctr2 = 286,  // 搜索分位置预估 V2 pos5 点击下发率
  PredictType_search_sctr_pos7_sctr1 = 287,  // 搜索分位置预估 V2 pos7 滑滑下发率
  PredictType_search_sctr_pos7_sctr2 = 288,  // 搜索分位置预估 V2 pos7 点击下发率
  PredictType_search_sctr_pos8_sctr1 = 289,  // 搜索分位置预估 V2 pos8 滑滑下发率
  PredictType_search_sctr_pos8_sctr2 = 290,  // 搜索分位置预估 V2 pos8 点击下发率
  PredictType_search_sctr_page1_sctr1 = 291,  // 搜索分位置预估 V2 pos 其他 滑滑下发率
  PredictType_search_sctr_page1_sctr2 = 292,  // 搜索分位置预估 V2 pos 其他 点击下发率
  // PredictType_pec_coupon_discount5_order_paied_rate = 293,   // 小店短视频 pec 95 折优惠券 订单支付预估值
  // PredictType_pec_coupon_discount10_order_paied_rate = 294,   // 小店短视频 pec 9 折优惠券 订单支付预估值
  // PredictType_pec_coupon_discount15_order_paied_rate  = 295,   // 小店短视频 pec 85 折优惠券 订单支付预估值
  // PredictType_pec_coupon_discount20_order_paied_rate = 296,   // 小店短视频 pec 8 折优惠券 订单支付预估值
  // PredictType_pec_coupon_no_discount_order_paied_rate = 297,  // 小店短视频 pec 不发优惠券 订单支付预估值
  PredictType_fan_longterm_active_rate = 298,    // 涨粉后互动率 (t0 涨粉 -> tn 互动)
  // PredictType_unify_click_rate = 299,
  PredictType_tx_up_deep_rate = 300,  // 淘系 mcda 模型预估
  PredictType_up_model_indus_live_rate = 301,  // up 模型行业直播预估值
  // PredictType_sdpa_novel_score = 302,  // sdpa ecpc 泛化模型
  // PredictType_unified_conv_rate = 303,  // 统一中台模型预估的浅度转化率
  // PredictType_deep_unified_conv_rate = 304,  // 统一中台模型预估的深度转化率
  // PredictType_c1_order_paid_p2l = 306,  // 一跳到购买作品引流的的预估
  // PredictType_c1_conv_crowd_quality = 307,  //激活人群质量分预估值
  // unuse PredictType_auto_params_ctr_factor = 308,  // 自动调参系数,暂时不用
  // unuse PredictType_auto_params_cvr_factor = 309,  // 自动调参系数,暂时不用
  // PredictType_search_pos_ctr_base = 315,  // 搜索分位置预估
  // PredictType_search_pos_ctr_1 = 316,  // 搜索分位置预估
  PredictType_rnd_explore_score = 317,  // rnd 探索预估值
  PredictType_universe_order_submit_rate = 318,  // 联盟订单提交率
  PredictType_user_experience_score1 = 319,  // 用户体验预估 (p3s)
  PredictType_user_experience_score2 = 320,  // 用户体验预估 (p5s)
  PredictType_user_experience_score3 = 321,  // 用户体验预估 (p10s)
  PredictType_user_experience_score4 = 322,  // 用户体验预估 (p15s)
  PredictType_user_experience_score5 = 323,  // 用户体验预估 (p18s)
  PredictType_user_experience_score6 = 324,  // 用户体验预估 (p30s)
  PredictType_user_experience_score7 = 325,  // 用户体验预估 (p60s)
  PredictType_user_experience_score8 = 326,  // 用户体验预估 (playtime)
  PredictType_click_app_invoked_sdpa_ensemble = 327,  // SDPA 统一模型唤端目标 ensemble 预估值
  PredictType_app_conversion_rate_sdpa_ensemble = 328,  // SDPA 统一模型激活目标 ensemble 预估值
  PredictType_univ_non_merchant_live_cvr = 329,  // 联盟外循环直播 cvr 模型预估值
  PredictType_univ_non_merchant_live_deep_cvr = 330,  // 联盟外循环直播 deep_cvr 模型预估值
  PredictType_negative_ratio = 331,  //  用户体验预估 负反馈率
  // unuse PredictType_refund_rate = 332,  //  电商体验预估 品退率
  // unuse PredictType_bad_comment_rate = 333,  //  电商体验预估 差评率
  PredictType_app_conversion_rate_sdpa_ecom_ensemble = 334,  // SDPA ecom conv ensemble 预估值
  PredictType_ee_search_score = 335,  // 人群探索分
  PredictType_internal_ind_explore_score = 336,  // 行业探索预估率
  PredictType_inner_live_gmv = 337,  // 直播 gmv
  PredictType_inner_live_pay = 338,  // 直播 pay
  PredictType_inner_live_roas = 339,  // 直播 roas
  PredictType_car_ensemble_imp_lps = 340,  // 交通行业表单模型
  PredictType_prerank_neg = 341,  // 粗排负反馈预估
  // unuse PredictType_model_uncertainty = 342,   // 不确定度预估
  PredictType_building_conversion_rate = 343,  // 房地产行业激活模型
  PredictType_finance_ensemble_imp_lps = 344,  // 金融行业表单模型
  PredictType_edu_ensemble_imp_lps = 345,  // 教育行业表单模型
  PredictType_wangfu_purchase = 346,  // 网服行业付费模型
  PredictType_industry_pay_ltv = 347,  // 行业付费 ltv
  PredictType_industry_clk2_pay = 348,  // 短剧行业二跳付费
  PredictType_industry_invoked_pay = 349,  // 短剧行业唤端付费
  PredictType_prerank_interest = 350,  // 粗排兴趣分
  PredictType_industry_server_show_cvr = 351,  // 短剧行业曝光二跳
  PredictType_lsp_photo_item_click = 352,  // 本地推短视组件点击率率
  PredictType_car_purchase = 353,  // 交通行业付费模型
  PredictType_non_merchant_live_button_click = 354,  // 行业直播组件点击
  PredictType_fiction_invoked_pay = 355,  // 付费小说行业唤端付费
  PredictType_fiction_pay_ltv = 356,  // 付费小说行业唤端付费
  PredictType_miniapp_iaa_roas_ltv = 357,  // 联盟 iaa 广告 roi
  PredictType_prerank_shelf_ltr = 358,  // 商城粗排 LTR
  PredictType_jump_out_rate = 359,  // 商城粗排 LTR
  PredictType_prerank_multi_interest_a = 360,  // 内循环多兴趣粗排 行为分 a
  PredictType_prerank_multi_interest_b = 361,  // 内循环多兴趣粗排 行为分 b
  PredictType_prerank_multi_interest_c = 362,  // 内循环多兴趣粗排 行为分 c
  PredictType_inner_item_click_hard = 363,  // 内循环硬广 imp->item_click
  PredictType_inner_item_click_native = 364,  // 内循环软广 imp->item_click
  PredictType_click_app_invoked_ecom = 365,  // 电商唤端模型
  PredictType_reco_hard_order_paid = 366,    //硬广电商口径CVR
  PredictType_reco_soft_order_paid = 367,    //软广电商口径CVR
  PredictType_direct_hard_order_paid = 368,    //硬广直接转化CVR
  PredictType_direct_soft_order_paid = 369,    //软广直接转化CVR
  PredictType_inner_refund = 370,    // 内循环退货率
  PredictType_reco_merchant_ltv = 371,    // 硬广电商ltv
  PredictType_reco_gmv = 372,    // 软广电商ltv
  PredictType_click_app_invoked_ecom_exp = 373,  // 电商唤端旁路模型
  PredictType_c1_app_invoked = 374, // inner cid c1 invoke
  PredictType_c2_app_invoked = 375, // inner cid c2 invoke
  PredictType_inner_roas_hard_cvr_param = 377,  // 内循环短带 roas 硬广联调参数
  PredictType_inner_roas_soft_cvr_param = 378,  // 内循环短带 roas 软广联调参数
  // 预占 400 ~ 450 海外模型预估类型
  PredictType_prerank_ctcvr_i18n = 400,  // 海外粗排 ctcvr
  PredictType_prerank_ltr_i18n = 401,   // 海外粗排 ltr
  PredictType_mtl_ctr = 402,
  PredictType_mtl_p3s = 403,
  PredictType_mtl_p5s = 404,
  PredictType_mtl_ped = 405,
  PredictType_mtl_neg = 406,
  PredictType_mtl_sctr = 407,
  PredictType_mtl_thru_play = 408,
  PredictType_ueq_dur = 409,  //  实验暂停
  PredictType_conversion_cvr = 410,
  PredictType_re_engage_cvr = 411,
  PredictType_content_view_cvr = 412,
  PredictType_complete_registration_cvr = 413,
  PredictType_add_to_cart_cvr = 414,
  PredictType_retention_1_day_cvr = 415,
  PredictType_purchase_cvr = 416,
  PredictType_level_achieved_cvr = 417,
  PredictType_retention_1_day_platform_cvr = 418,
  PredictType_loan_application_cvr = 419,
  PredictType_ad_view_cvr = 420,
  PredictType_others_cvr = 421,
  PredictType_key_inapp_event_cvr = 422,
  PredictType_conv_24h_ltv = 423,
  PredictType_conv_24h_ltv_prob = 424,
  PredictType_i18n_follow_rate = 425,     // 海外涨粉目标
  PredictType_ind_key_inapp_event_cvr = 426,   // 海外行业关键行为目标
  PredictType_prerank_ctr_i18n = 427,   // 海外粗排 ctr
  PredictType_prerank_cvr_i18n = 428,   // 海外粗排 cvr
  PredictType_key_inapp_event_1_cvr = 429,
  PredictType_key_inapp_event_2_cvr = 430,
  PredictType_key_inapp_event_3_cvr = 431,
  PredictType_prerank_delivery_rate_i18n = 432,   // 海外粗排下发率
  PredictType_prerank_cpm_i18n = 433,
  PredictType_prerank_action_i18n = 434,
  PredictType_inspire_ctr_0 = 435,
  PredictType_inspire_ctr_1 = 436,
  PredictType_inspire_ctr_2 = 437,
  PredictType_inspire_ctr_3 = 438,
  PredictType_inspire_ctr_4 = 439,
  PredictType_inspire_cvr_0 = 440,
  PredictType_inspire_cvr_1 = 441,
  PredictType_inspire_cvr_2 = 442,
  PredictType_inspire_cvr_3 = 443,
  PredictType_inspire_cvr_4 = 444,
  PredictType_inspire_ctr_10 = 445,  // 海外金币优选 qcpx ctr_10
  PredictType_inspire_ctr_11 = 446,  // 海外金币优选 qcpx ctr_11
  PredictType_inspire_ctr_12 = 447,  // 海外金币优选 qcpx ctr_12
  PredictType_inspire_ctr_13 = 448,  // 海外金币优选 qcpx ctr_13
  PredictType_inspire_ctr_14 = 449,  // 海外金币优选 qcpx ctr_14
  // PredictType_i18n_max = 450,
  PredictType_search_prerank_ctcvr = 451,
  PredictType_universe_ug_plt = 452,
  PredictType_universe_ug_parpu = 453,
  PredictType_playtime_prob_p3s = 454,    // 行业探索预估率播放相关
  PredictType_playtime_prob_p5s = 455,    // 行业探索预估率播放相关
  PredictType_playtime_prob_p10s = 456,   // 行业探索预估率播放相关
  PredictType_playtime_prob_p15s = 457,   // 行业探索预估率播放相关
  PredictType_playtime_prob_p18s = 458,   // 行业探索预估率播放相关
  PredictType_playtime_prob_p30s = 459,   // 行业探索预估率播放相关
  PredictType_playtime_click_prob = 460,  // 行业探索预估率商品点击率
  PredictType_estimated_playtime = 461,   // 行业探索预估率播放时长
  PredictType_fin_edu_sub_obtain_cvr = 462,
  PredictType_universe_dpa_purchase_rate = 463,
  PredictType_middle_cpm1 = 464,
  PredictType_lps_acquisition = 465,
  PredictType_real_action = 466,  // 粗排真实行为预估
  PredictType_follow_fnl = 467,   // 内循环涨粉互动率
  // unuse PredictType_nday_refund_rate = 468,   // 内循环支付后 N 天内退款率
  PredictType_ue_score = 469,  // 用户体验分
  PredictType_fin_use_credit_rate = 470,  // 金融用信率
  PredictType_30_day_game_conv_ltv = 471,  // 30r ltv
  PredictType_universe_ecom_interest_score = 472,  // 联盟电商兴趣分
  PredictType_1_day_pay_times = 473,  // 首日付费次数
  PredictType_2_7_day_pay_times = 474,  // 2~7 日付费次数
  PredictType_inner_rank_sv_uplift_cvr_rate = 475,  // 内循环短视频增效模型 cvr
  PredictType_live_play_15s = 476,  // 粉条长播 15s
  PredictType_live_play_30s = 477,  // 粉条长播 30s
  PredictType_imp2purchase = 478,  // 曝光付费模型
  PredictType_purchase_7d_pay_times = 479,  // ecpc 用的付费七日付费模型
  PredictType_native_gmv = 480,  // roas 软广
  PredictType_order_paid_explore = 481,  //订单探索预估值
  PredictType_order_paid_item_avg = 482,  //订单探索 item 预估均值
  PredictType_order_paid_model_score = 483,  //订单探索模型主动学习分
  PredictType_cid_mcda_score = 484,  //  cid mada 辅助模型预估分
  PredictType_1_day_pay_amount = 485,  // 7ROI 首日付费金额
  PredictType_2_7_day_pay_amount = 486,    // 7ROI 2-7 日付费金额
  PredictType_preranking_purchase_ltv7 = 487,  // 付费 ltv 7 日预估值粗排模型
  PredictType_preranking_order_submit = 488,  // 粗排 order submit
  PredictType_fin_jinjian_credit_rate = 489,  // 金融完件授信率
  PredictType_auc_model_base_fusion = 490,   // 融合模型中的第二个 base 模型的类型
  PredictType_auc_model_exp_fusion = 491,   // 融合模型中的第二个 exp 模型的类型
  // unuse PredictType_mt_game_click2install_up = 492,  //  游戏安装事件模型 未找到依赖，商品全站增量腾挪
  PredictType_zhugong_cvr = 493,    // 助攻模型
  PredictType_game_30d_ltv = 494,    // 游戏行业 30 日 ltv 预估模型
  PredictType_everyday_stay = 495,  // 每日留存模型
  PredictType_game_industry_pay_ltv = 496,  // 游戏行业付费 ltv 模型
  PredictType_universe_prerank_ltr = 497,  // 联盟粗排 ltr 模型
  PredictType_mix_unify_gpm = 498,  // gpm 模型
  PredictType_inner_live_roas_7days = 499,  // 内循环 7 日直播 roi
  PredictType_roas_pay_ratio = 500,   // ROI 模型预估激活付费率
  PredictType_2day_stay = 501,  // 激活二留率
  PredictType_3day_stay = 502,  // 激活三留率
  PredictType_4day_stay = 503,  // 激活四留率
  PredictType_5day_stay = 504,  // 激活五留率
  PredictType_6day_stay = 505,  // 激活六留率
  PredictType_1day_stay = 506,  // 激活一留率
  PredictType_7day_stay = 507,  // 激活七留率
  PredictType_industry_clk2_pay_other = 508,  // 短剧行业其他二跳付费
  PredictType_industry_server_show_cvr_other = 509,  // 短剧行业其他曝光二跳
  PredictType_universe_quality_score = 510,  // 联盟质量分
  PredictType_i18n_purchase_left_cvr = 600,  // 海外付费 left
  PredictType_i18n_purchase_other_cvr = 601,  // 海外付费 other
  PredictType_i18n_ctr_left = 602,  // 海外 ctr left
  PredictType_i18n_ctr_other = 603,  // 海外 ctr other
  PredictType_search_sctr_pos0_sctr2 = 604,  // 搜索分位置预估 V2 pos0 点击下发率
  PredictType_search_sctr_pos4_sctr2 = 605,  // 搜索分位置预估 V2 pos4 点击下发率
  PredictType_search_sctr_pos6_sctr2 = 606,  // 搜索分位置预估 V2 pos6 点击下发率
  PredictType_search_sctr_pos9_sctr2 = 607,  // 搜索分位置预估 V2 pos9 点击下发率
  PredictType_search_sctr_pos10_sctr2 = 608,  // 搜索分位置预估 V2 pos10 点击下发率
  PredictType_search_sctr_pos_inner = 609,  // 搜索分位置预估 V2 内流下发率
  PredictType_inner_live_roas_7days_0_2h = 610,  // 内循环 7 日直播 roi, 0_2h
  PredictType_inner_live_roas_7days_2h_3d = 611,  // 内循环 7 日直播 roi, 2h_3d
  PredictType_inner_live_roas_7days_3d_7d = 612,  // 内循环 7 日直播 roi, 3d_7d
  PredictType_game_palyer_first_charge_rate = 613,  // 游戏首日充值预测模型
  PredictType_inner_live_roas_front = 614,  // 直播 roas 前 15 min
  PredictType_inner_live_roas_end = 615,  // 直播 roas 后 2 h
  PredictType_inner_live_roi_gmv_front = 616,  // 直播 roas gmv 前 15 min
  PredictType_inner_live_roi_gmv_end = 617,  // 直播 roas gmv 后 2 h
  PredictType_inner_live_roi_pay_front = 618,  // 直播 roas pay 前 15 min
  PredictType_inner_live_roi_pay_end = 619,  // 直播 roas pay 后 2 h
  PredictType_inner_live_roi_ext = 620,  // 直播 roas ext
  PredictType_inner_live_roas_hard = 621,  // 直播 roas 硬广
  PredictType_edu_lps_deep_rate = 622,  // 教育行业表单到深度预估值 正价课目标
  PredictType_outer_u_ctr = 623,  // 外循环统一 ctr 模型预估值
  PredictType_outer_u_cvr = 624,  // 外循环统一 cvr 模型预估值
  PredictType_outer_u_noctcvr = 625,  // 外循环统一无点击 cvr 模型预估值
  // unuse PredictType_wangfu_purchase_exp = 626,  // 网服行业付费模型
  PredictType_non_merchant_live_lps_realtime_cvr = 627,  // 非电商直播 lps cvr
  PredictType_invo_traffic_score = 628,  // 创新流量价值分
  PredictType_industry_conv_pay = 629,  // 短剧行业激活付费
  PredictType_ecom_app_conversion_rate = 630,  // 电商平台行业激活模型
  PredictType_edu_lps_deep_all_class_rate = 631,  // 教育行业表单到深度预估值 高沉浸目标
  PredictType_7_day_purchase_ltv = 632,  // 7 日付费 ltv
  PredictType_landingpage_submit_rate_c2 = 633,  // c2 表单
  PredictType_playlet_pay_panel = 634,  // 短剧行业面板
  PredictType_playlet_pay_panel_purchase = 635,  // 短剧行业面板付费
  PredictType_adx_thanos_cvr_exp = 636,  // adx cpc 模型预估值
  PredictType_clue_ensemble_imp_lps = 637,  // 线索表单模型
  PredictType_inner_qcpx_uplift_ratio = 638,  // 内循环 qcpx cvr uplift_ratio
  PredictType_lps_acquisition_generalization = 639,  // 有效获客 泛化模型
  PredictType_fin_credit_roi_rate = 640,  // 金融小贷用信金额
  PredictType_clue_ensemble_clk_lps = 641,  // 线索二跳表单模型
  PredictType_industry_game_iaa_ltv = 642,  // 游戏 rnd 探索模型
  PredictType_preranking_advertise_ltv = 643,  // 广告 ltv 预估值粗排模型
  PredictType_lps_acquisition_clk_lps = 644,  // 有效获客 点击_表单
  PredictType_playlet_pay_panel_ltv = 645,  // 短剧行业面板 ltv 模型
  PredictType_industry_playlet_iaa_ltv = 646,  // 短剧行业 iaa ltv 模型
  PredictType_inner_qcpx_photo_cvr_elastic_c0 = 647,  //  @fandi inner_qcpx photo cvr 弹性系数 c0
  PredictType_inner_qcpx_photo_cvr_elastic_c1 = 648,  //  @fandi inner qcpx photo cvr 弹性系数 c1
  PredictType_inner_qcpx_live_uplift_cvr_ratio = 649,  // 内循环 直播 qcpx cvr uplift_ratio
  PredictType_inner_qcpx_live_cvr_elastic_c0 = 650,  //  inner_qcpx live cvr 弹性系数 c0
  PredictType_inner_qcpx_live_cvr_elastic_c1 = 651,  //  inner qcpx live cvr 弹性系数 c1
  PredictType_unified_ctr_auc_base = 652,  // 统一 ctr auc base
  PredictType_unified_ctr_auc_exp = 653,  // 统一 ctr auc exp
  PredictType_inner_storewide_live_uplift_prob1 = 654,  // 内循环直播全站增量模型 广告下发 prob1
  PredictType_inner_storewide_live_uplift_prob2 = 655,  // 内循环直播全站增量模型 广告不下发 prob2
  PredictType_game_conv_ltv_c_subsidy = 656,  // ROAS 出价 有 C 补模型预估字段
  PredictType_inner_qcpx_photo_cvr_elastic_piecewise_c0 = 657,  // @wanpengcheng inner_qcpx photo cvr 分段线性模型弹性系数 c0  // NOLINT
  PredictType_inner_qcpx_photo_cvr_elastic_piecewise_c1 = 658,  // @wanpengcheng inner_qcpx photo cvr 分段线性模型弹性系数 c1  // NOLINT
  PredictType_inner_qcpx_photo_cvr_elastic_piecewise_c2 = 659,  // @wanpengcheng inner_qcpx photo cvr 分段线性模型弹性系数 c2  // NOLINT
  PredictType_inner_qcpx_photo_cvr_elastic_piecewise_c3 = 660,  // @wanpengcheng inner_qcpx photo cvr 分段线性模型弹性系数 c3  // NOLINT
  PredictType_inner_qcpx_photo_cvr_elastic_piecewise_c4 = 661,  // @wanpengcheng inner_qcpx photo cvr 分段线性模型弹性系数 c4  // NOLINT
  PredictType_inner_qcpx_photo_cvr_elastic_piecewise_c5 = 662,  // @wanpengcheng inner_qcpx photo cvr 分段线性模型弹性系数 c5  // NOLINT
  PredictType_inner_storewide_merchant_uplift_prob1 = 663,  // 内循环商品全站增量模型 广告下发 prob1
  PredictType_inner_storewide_merchant_uplift_prob2 = 664,  // 内循环商品全站增量模型 广告不下发 prob2
  //unuse PredictType_inner_qcpx_live_pec_elastic_c0 = 665,  // inner_qcpx pec live cvr 弹性系数 c0
  //unuse PredictType_inner_qcpx_live_pec_elastic_c1 = 666,  // inner_qcpx pec live cvr 弹性系数 c1
  //unuse PredictType_inner_qcpx_live_open_elastic_c0 = 667,  // inner_qcpx open live cvr 弹性系数 c0
  //unuse PredictType_inner_qcpx_live_open_elastic_c1 = 668,  // inner_qcpx open live cvr 弹性系数 c1
  PredictType_prm_leads_submit = 669, // 私信留资模型
  PredictType_leads_message_industry = 670, // 拆分 cmd key
  PredictType_leads_message_healthcare = 845, // 医疗行业分数cmd key
  PredictType_lps_leads_integration_leads_prob = 671,  // 落地页优选留资预估
  PredictType_lps_leads_integration_lps_prob = 672,  // 落地页优选表单预估
  PredictType_game_conv_ltv_c_subsidy1 = 673,  // 有 C 补 档位一面板ltv
  PredictType_game_conv_ltv_c_subsidy2 = 674,  // 有 C 补 档位二面板ltv
  PredictType_game_conv_ltv_c_subsidy3 = 675,  // 有 C 补 档位三面板ltv
  PredictType_game_conv_ltv_c_subsidy4 = 676,  // 有 C 补 档位四面板ltv
  PredictType_game_conv_ltv_c_subsidy5 = 677,  // 有 C 补 档位五面板ltv
  PredictType_game_conv_ltv_c_subsidy6 = 678,  // 有 C 补 档位六面板ltv
  PredictType_game_conv_ltv_c_subsidy0 = 679,  // 无 C 补 ltv
  PredictType_pm_integration_single_treat_prob = 680,  // 落地页优选uplift单预估值
  PredictType_locallife_prmn_leadssubmit_local = 681,  // 门店私信链路私信留资本地概率
  PredictType_locallife_prmn_leadssubmit_other = 682,  // 门店私信链路私信留资非本地概率
  PredictType_smart_offer_multi_origin_cvr0 = 724,  // 无 C 补 面板付费率
  PredictType_smart_offer_multi_uplift_cvr1 = 725,  // 有 C 补 档位一面板付费率
  PredictType_smart_offer_multi_uplift_cvr2 = 726,  // 有 C 补 档位二面板付费率
  PredictType_smart_offer_multi_uplift_cvr3 = 727,  // 有 C 补 档位三面板付费率
  PredictType_smart_offer_multi_uplift_cvr4 = 728,  // 有 C 补 档位四面板付费率
  PredictType_smart_offer_multi_uplift_cvr5 = 729,  // 有 C 补 档位五面板付费率
  PredictType_smart_offer_multi_uplift_cvr6 = 730,  // 有 C 补 档位六面板付费率
  PredictType_smart_offer_multi_origin_ltv0 = 731,  // 无 C 补 付费金额
  PredictType_smart_offer_multi_uplift_ltv1 = 732,  // 有 C 补 档位一付费金额
  PredictType_smart_offer_multi_uplift_ltv2 = 733,  // 有 C 补 档位二付费金额
  PredictType_smart_offer_multi_uplift_ltv3 = 734,  // 有 C 补 档位三付费金额
  PredictType_smart_offer_multi_uplift_ltv4 = 735,  // 有 C 补 档位四付费金额
  PredictType_smart_offer_multi_uplift_ltv5 = 736,  // 有 C 补 档位五付费金额
  PredictType_smart_offer_multi_uplift_ltv6 = 737,  // 有 C 补 档位六付费金额
  PredictType_inner_qcpx_photo_cvr_main_join_elastic_c0 = 738,  // @wanpengcheng inner_qcpx photo cvr 主模型联合预估实验弹性系数 c0  // NOLINT
  PredictType_inner_qcpx_photo_cvr_main_join_elastic_c1 = 739,  // @wanpengcheng inner_qcpx photo cvr 主模型联合预估实验弹性系数 c1  // NOLINT

  // 预占 900 ~ 1000 海外模型预估类型
  PredictType_inspire_cvr_10 = 900,  // 海外金币优选 qcpx cvr_10
  PredictType_inspire_cvr_11 = 901,  // 海外金币优选 qcpx cvr_11
  PredictType_inspire_cvr_12 = 902,  // 海外金币优选 qcpx cvr_12
  PredictType_inspire_cvr_13 = 903,  // 海外金币优选 qcpx cvr_13
  PredictType_inspire_cvr_14 = 904,  // 海外金币优选 qcpx cvr_14
  PredictType_ctr_bc = 905,  // 海外金币优选 qcpx blind coin ctr
  PredictType_cvr_bc = 906,  // 海外金币优选 qcpx blind coin cvr
  PredictType_universe_win_rate_miu = 907,  // 海外联盟竞胜率 miu
  PredictType_universe_win_rate_sigma = 908,  // 海外联盟竞胜率 sigma
  PredictType_outer_uplift_ltv_base = 909,  // 小游戏和小说 uplift 模型 base
  PredictType_outer_uplift_ltv_uplift0 = 910,  // 小游戏和小说 uplift 模型 ltv0
  PredictType_incentive_quit_rate_0 = 911,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_1 = 912,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_2 = 913,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_3 = 914,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_4 = 915,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_5 = 916,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_6 = 917,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_7 = 918,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_8 = 919,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_9 = 920,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_10 = 921,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_11 = 922,  // 激励广告观看退出率
  PredictType_incentive_quit_rate_12 = 923,  // 激励广告观看退出率
  PredictType_incentive_invoked_uplift_cvr_0 = 925,  // 激励广告唤端 treatment_0 cvr
  PredictType_incentive_invoked_uplift_cvr_1 = 926,  // 激励广告唤端 treatment_1 cvr
  PredictType_incentive_invoked_uplift_cvr_2 = 927,  // 激励广告唤端 treatment_2 cvr
  PredictType_incentive_invoked_uplift_cvr_3 = 928,  // 激励广告唤端 treatment_3 cvr
  PredictType_inner_qcpx_live_cvr_elastic_piecewise_c0 = 939,  // @jiangjiaxin inner_qcpx live cvr 分段线性 c0  // NOLINT
  PredictType_inner_qcpx_live_cvr_elastic_piecewise_c1 = 940,  // @jiangjiaxin inner_qcpx live cvr 分段线性 c1  // NOLINT
  PredictType_inner_qcpx_live_cvr_elastic_piecewise_c2 = 941,  // @jiangjiaxin inner_qcpx live cvr 分段线性 c2  // NOLINT
  PredictType_inner_qcpx_live_cvr_elastic_piecewise_c3 = 942,  // @jiangjiaxin inner_qcpx live cvr 分段线性 c3  // NOLINT
  PredictType_inner_qcpx_live_cvr_elastic_piecewise_c4 = 943,  // @jiangjiaxin inner_qcpx live cvr 分段线性 c4  // NOLINT
  PredictType_inner_qcpx_live_cvr_elastic_piecewise_c5 = 944,  // @jiangjiaxin inner_qcpx live cvr 分段线性 c5  // NOLINT
  PredictType_inner_qcpx_live_cvr_elastic_piecewise_c6 = 945,  // @jiangjiaxin inner_qcpx live cvr 分段线性 c6  // NOLINT
  PredictType_inner_qcpx_live_cvr_elastic_piecewise_c7 = 946,  // @jiangjiaxin inner_qcpx live cvr 分段线性 c7  // NOLINT
  PredictType_prerank_dnc_ctcvr = 948,  // 粗排 dnc 用户首次转化广告概率
  PredictType_outer_game_conv_pay_cnt_7 = 949,  // 游戏 7 日付费次数预估值
  PredictType_outer_u_cvr_cross_auc = 950, // 激活 auc 有点转化
  PredictType_outer_u_noctcvr_cross_auc = 951, //激活 auc 无点转化
  PredictType_mid_playlet_iaa_ltv=1182,  // 中台IAA短剧ltv预估值
  PredictType_max = 1000  // 最大到 1000, 且每个枚举值不能超过 63 个字符长
);
*/

#include "ad_predict_type.inl"  //  NOLINT

#define ALL_PREDICT_EMBEDDING_TYPE(_Enum_) \
  _Enum_(PredictEmbeddingType_Unknown, 0), \
  _Enum_(PredictEmbeddingType_live_audience_toplayer, 1), \
  _Enum_(PredictEmbeddingType_inner_merge_toplayer, 2), \
  _Enum_(PredictEmbeddingType_outer_ctr_conv_toplayer, 3), \
  _Enum_(PredictEmbeddingType_outer_ctr_itemclick_toplayer, 4), \
  _Enum_(PredictEmbeddingType_outer_ctr_showocpm_toplayer, 5), \
  _Enum_(PredictEmbeddingType_outer_ctr_live_item_emb, 6), \
  _Enum_(PredictType_outer_live_rl_output, 7), \
  _Enum_(PredictEmbeddingType_outer_prm_ctr_toplayer, 8), \
  _Enum_(PredictEmbeddingType_outer_prm_pcvr, 9), \
  _Enum_(PredictEmbeddingType_outer_leads_pcvr, 10), \
  _Enum_(PredictEmbeddingType_outer_live_sid_vec, 11), \
  _Enum_(PredictEmbeddingType_invo_traffic_cali_top_layer_emb, 12), \
  _Enum_(PredictEmbeddingType_outer_prm_cvr_toplayer, 13), \
  _Enum_(PredictEmbeddingType_outer_leads_cvr_toplayer, 14), \
  _Enum_(PredictEmbeddingType_outer_ctr_iaa_item_emb, 15), \
  _Enum_(PredictEmbeddingType_live_audience_toplayer_auc_base, 16), \
  _Enum_(PredictEmbeddingType_live_audience_toplayer_auc_exp, 17), \
  _Enum_(Predict_outer_live_recall_emb_vec, 18), \
  _Enum_(PredictType_outer_iaa_rl_output, 19), \
  _Enum_(PredictEmbeddingType_max, 1024)  \

_AD_ENUM_(PredictEmbeddingType, ALL_PREDICT_EMBEDDING_TYPE,
  int32_t, PredictEmbeddingType_Unknown)

#include "ad_result_type.inl" //  NOLINT

enum class PvRecallReason {
  NORMAL = 0,              // 正常召回
  TEST = 1,                // 测试召回
  DSP_WHITE = 2,           // DSP 白名单召回
  ADX_WHITE = 3,           // ADX 白名单召回
  TRACE_API = 4,           // TRACE API 召回
  STRESS_TEST = 5,         // 压测召回
  NEBULA_INSPIRE = 6,      // 极速版激励视频召回
  SMALL_GAME_INSPIRE = 7,  // 小游戏激励视频召回
  OPERATION_INSPIRE = 8,   // 运营类激励视频召回
};

struct PredictResult {
  PredictResult()
      : ctr_(0.0),
        cvr_(0.0),
        play3s_(0.7),
        ltr_(0.0),
        wtr_(0.0),
        delivery_rate_(0.0),
        htr_(0.0),
        app_conversion_rate_(0.0),
        retention_rate_(0.0),
        purchase_rate_(0.0),
        credit_rate_(0.0),
        server_show_cvr_(0.0),
        adx_scvr_(0.0),
        click2_conv_(0.0),
        click2_lps_(0.0),
        click2_prod_apr_(0.0),
        conv2_purchase_(0.0),
        clientshow_click2_(0.0),
        click2_shop_action_(0.0),
        click2_purchase_(0.0),
        click2_nextstay_(0.0),
        server_show_ctr_(0.0),
        deep_rate_(0.0),
        click2_deep_rate_(0.0),
        click2_purchase_device_(0.0),
        click_purchase_device_(0.0),
        conv_ltv_(0.0),
        purchase_ltv_(0.0),
        purchase_ltv7_(0.0),
        order_submit_(0.0),
        ug_conv_pltv7_(0.0),
        inner_cpr_(0.0),
        c1_merchant_follow_(0.0),
        c2_button_click_(0.0),
        c1_button_click_(0.0),
        pay_times_(0.0),
        nebula_merchant_follow_(0.0),
        c1_order_paied_(0.0),
        c2_order_paied_(0.0),
        click_purchase_rate_single_bid_(0.0),
        click2_purchase_rate_single_bid_(0.0),
        conv_nextstay_(0.0),
        server_client_show_rate_(0.0),
        c1_prerank_unified_(0.0),
        c2_prerank_unified_(0.0),
        nebula_prerank_merchant_follow_(0.0),
        nebula_prerank_purchase_(0.0),
        feed_prerank_merchant_follow_(0.0),
        feed_prerank_purchase_(0.0),
        server_show_merchant_follow_rate_(0.0),
        live_goods_view_(0.0),
        live_pay_rate_(0.0),
        live_server_show_play3s_feed_(0.0),
        live_server_show_play3s_slide_(0.0),
        live_server_show_wtr_(0.0),
        game_appoint_rate_(0.0),
        c2_game_appoint_rate_(0.0),
        live_p3s_wtr_(0.0),
        item_impression_wtr_(0.0),
        new_creative_delivery_rate_(0.0),
        new_creative_cvr_(0.0),
        click_app_invoked_(0.0),
        click2_app_invoked_(0.0),
        shop_live_prerank_p3s_(0.0),
        merchant_ltv_(0.0),
        live_p3s_ltv_(0.0),
        jinniu_prerank_score(0.0),
        photo_to_live_prerank_p3s_(0.0),
        live_prerank_p3s_wtr_(0.0),
        photo_to_live_item_imp_wtr_(0.0),
        prerank_p3s_goods_view_(0.0),
        prerank_p3s_pay_rate_(0.0),
        photo_to_live_server_show_ctr_(0.0),
        photo_to_live_item_imp_p3s_(0.0),
        live_prerank_inroom_nebula_(0.0),
        live_prerank_inroom_feed_(0.0),
        prerank_thanos_xdt_order_paied_(0.0),
        prerank_ecpm_ctr_(0.0),
        prerank_ecpm_cvr_(0.0),
        pred_ad_time_(0.0),
        lps2_purchase_(0.0),
        live_prerank_p3s_ltv_(0.0),
        prerank_ecpm_live_ctr_(0.0),
        prerank_wanjian_shouxin_rate_ecpc_(0.0),
        prerank_biaodan_shouxin_rate_(0.0),
        prerank_universe_invoke_(0.0),
        photo_to_live_item_pay_rate_(0.0),
        prerank_cpm_ltr_(0.0),
        prerank_real_action_rate_(0.0),
        conv_key_action_rate_(0.0),
        prerank_new_creative_lps_(0.0),
        prerank_new_creative_conv_(0.0),
        prerank_new_creative_deep_(0.0),
        prerank_universe_ctcvr_(0.0),
        prerank_universe_ltr_{0.0},
        prerank_search_ctcvr_(0.0),
        up_model_no_pay_rate_(0.0),
        up_model_low_pay_rate_(0.0),
        up_model_medium_pay_rate_(0.0),
        up_model_high_pay_rate_(0.0),
        live_audience_(0.0),
        conv_key_inapp_action_rate_{0.0},
        inner_loop_ecpc_imp_conv_rate_(0.0),
        video_play_7s_(0.0),
        short_video_play_7s_(0.0),
        effective_play_7s_(0.0),
        video_effective_play_7s_(0.0),
        short_video_effective_play_7s_(0.0),
        c1_event_order_(0.0),
        universe_prepank_merchant_follow_(0.0),
        univ_xdt_audience_rate_(0.0),
        univ_xdt_pay_rate_(0.0),
        univ_xdt_roas_rate_(0.0),
        up_model_high_ltv_rate_{0.0},
        universe_merchant_ltv_rate_(0.0),
        prerank_imp_key_inapp_rate_(0.0),
        up_model_conv_key_inapp_rate_(0.0),
        universe_prerank_item_click_(0.0),
        prerank_jiuniu_roas_rate_(0.0),
        up_model_lps_intention_rate_(0.0),
        up_model_conv_retention_rate_(0.0),
        predict_auc_score_base_(0.0),
        predict_auc_score_exp_(0.0),
        live_prerank_stay_time_(0.0),
        prerank_follow_ltv_(0.0),
        click2_unified_(0.0),
        prerank_unify_ltr_(0.0),
        middle_platform_(0.0),
        inner_live_gmv_(0.0),
        ee_score_(0.0),
        ue_score_(0.0),
        inner_item_click_hard_(0.0),
        inner_item_click_native_(0.0),
        reco_hard_order_paid_(0.0),
        reco_soft_order_paid_(0.0),
        direct_hard_order_paid_(0.0),
        direct_soft_order_paid_(0.0),
        inner_refund_(0.0),
        c1_app_invoked_(0.0),
        c2_app_invoked_(0.0),
        inner_cid_order_paid_value1_(0.0),
        inner_cid_order_paid_value2_(0.0),
        inner_cid_order_paid_value3_(0.0),
        inner_cid_order_paid_value4_(0.0),
        inner_cid_roas_value1_(0.0),
        inner_cid_roas_value2_(0.0),
        inner_cid_roas_value3_(0.0),
        inner_cid_roas_value4_(0.0),
        reco_merchant_ltv_(0.0),
        reco_gmv_(0.0),
        inner_roas_hard_cvr_param_(0.0),
        inner_roas_soft_cvr_param_(0.0),
        advertise_ltv_(0.0),
        prerank_dnc_ctcvr_(0.0),
        lps_acquisition_(0.0),
        inner_shelf_order_pay_cvr_pos1_(0.0),
        inner_shelf_order_pay_cvr_pos2_(0.0),
        inner_shelf_order_pay_cvr_pos3_(0.0),
        inner_shelf_order_pay_cvr_pos4_(0.0),
        inner_shelf_order_pay_cvr_pos5_(0.0),
        inner_shelf_order_pay_cvr_pos6_(0.0),
        inner_shelf_order_pay_cvr_pos7_(0.0),
        inner_shelf_order_pay_cvr_pos8_(0.0),
        inner_shelf_order_pay_cvr_pos9_(0.0),
        inner_shelf_order_pay_cvr_pos10_(0.0) {
  }
  bool valid_{false};
  bool result_not_null_{false};
  double ctr_;
  double cvr_;
  double play3s_;
  double ltr_;
  double wtr_;
  double delivery_rate_;
  double htr_;
  double app_conversion_rate_;
  double c1_conv_fix_;
  double retention_rate_ = 0.0;
  double universe_merchant_ltv_rate_ = 0.0;
  double credit_rate_ = 0.0;
  double click_credit_rate_ = 0.0;
  double click_wanjian_rate_ = 0.0;
  double credit_conv_grant_rate_ = 0.0;
  double click2_credit_rate_ = 0.0;
  double credit_eval_rate_ = 0.0;
  double click_insur_purchase_rate_ = 0.0;
  double c2_insur_purchase_rate_ = 0.0;
  double purchase_rate_ = 0.0;
  double click_retention_rate_ = 0.0;
  double click_purchase_rate_ = 0.0;
  double prerank_jiuniu_roas_rate_ = 0.0;
  double ncer_ = 0.0;
  double landingpage_submit_rate_ = 0.0;
  double multitask_play3s_ = 0.0;
  double multitask_ltr_ = 0.0;
  double multitask_wtr_ = 0.0;
  double ntr_ = 0.0;
  double app_calibration_conversion_rate_ = 0.0;
  double apr_ = 0.0;
  double prod_apr_ = 0.0;
  double shop_action_rate_ = 0.0;
  double special_lps_ = 0.0;
  double deep_credit_spec_rate_ = 0.0;
  double server_show_cvr_ = 0.0;
  double adx_scvr_ = 0.0;
  double click2_conv_ = 0.0;
  double click2_lps_ = 0.0;
  double click2_prod_apr_ = 0.0;
  double conv2_purchase_ = 0.0;
  double lps2_purchase_ = 0.0;
  double clientshow_click2_ = 0.0;
  double click2_shop_action_ = 0.0;
  double click2_purchase_ = 0.0;
  double click2_purchase_device_ = 0.0;
  double click_purchase_device_ = 0.0;
  double click2_nextstay_ = 0.0;
  double server_show_ctr_ = 0.0;
  double deep_rate_ = 0.0;
  double c1_event_order_ = 0.0;
  double prerank_imp_key_inapp_rate_ = 0.0;
  double universe_prerank_item_click_ = 0.0;
  double universe_prepank_merchant_follow_ = 0.0;
  double univ_xdt_audience_rate_ = 0.0;
  double univ_xdt_pay_rate_ = 0.0;
  double univ_xdt_roas_rate_ = 0.0;
  double click2_deep_rate_ = 0.0;
  double conv_ltv_ = 0.0;
  double purchase_ltv_ = 0.0;
  double purchase_ltv7_ = 0.0;
  double order_submit_ = 0.0;
  double ug_conv_pltv7_ = 0.0;
  double pay_times_ = 0.0;
  double inner_cpr_ = 0.0;
  double c1_merchant_follow_ = 0.0;
  double nebula_merchant_follow_ = 0.0;
  double c1_order_paied_ = 0.0;
  double c2_order_paied_ = 0.0;
  double c2_button_click_ = 0.0;
  double c1_button_click_ = 0.0;
  double click_purchase_rate_single_bid_ = 0.0;
  double click2_purchase_rate_single_bid_ = 0.0;
  double conv_nextstay_ = 0.0;
  double server_client_show_rate_ = 0.0;
  double c1_prerank_unified_ = 0.0;
  double c2_prerank_unified_ = 0.0;
  double nebula_prerank_merchant_follow_ = 0.0;
  double nebula_prerank_purchase_ = 0.0;
  double prerank_thanos_xdt_order_paied_ = 0.0;
  double feed_prerank_merchant_follow_ = 0.0;
  double feed_prerank_purchase_ = 0.0;
  double server_show_merchant_follow_rate_ = 0.0;
  double live_goods_view_ = 0.0;
  double live_pay_rate_ = 0.0;
  double live_audience_ = 0.0;
  double live_play_3s_ = 0.0;
  double live_server_show_play3s_feed_ = 0.0;
  double live_server_show_play3s_slide_ = 0.0;
  double live_server_show_wtr_ = 0.0;
  double game_appoint_rate_ = 0.0;
  double c2_game_appoint_rate_ = 0.0;
  double live_p3s_wtr_ = 0.0;
  double item_impression_wtr_ = 0.0;
  double new_creative_delivery_rate_ = 0.0;
  double new_creative_cvr_ = 0.0;
  double click_app_invoked_ = 0.0;
  double click2_app_invoked_ = 0.0;
  double merchant_ltv_ = 0.0;
  double live_p3s_ltv_ = 0.0;
  double shop_live_prerank_p3s_ = 0.0;
  double photo_to_live_prerank_p3s_ = 0.0;    // 短视频直播 p3s
  double live_prerank_inroom_nebula_ = 0.0;   // 短视频直播进房率滑滑
  double live_prerank_inroom_feed_ = 0.0;     // 短视频直播进房率双列
  double live_prerank_p3s_wtr_ = 0.0;         // 直播粗排 p3s -> follow
  double photo_to_live_item_imp_wtr_ = 0.0;   // 短视频直播粗排 item_imp -> follow
  double photo_to_live_item_pay_rate_ = 0.0;  // 短视频直播粗排 item_imp -> order
  double prerank_p3s_goods_view_ = 0.0;       // 直播 p3s -> 商品访问 粗排
  double prerank_p3s_pay_rate_ = 0.0;
  double photo_to_live_server_show_ctr_ = 0.0;
  double photo_to_live_item_imp_p3s_ = 0.0;
  double prerank_thanos_conv_ = 0.0;
  double prerank_thanos_lps_ = 0.0;
  double prerank_thanos_follow_ = 0.0;
  double prerank_thanos_purchase_ = 0.0;
  double prerank_thanos_deep_ = 0.0;
  double prerank_thanos_scvr_ = 0.0;
  double federated_cvr_ = 0.0;
  double edu_lps2pay_cvr_ = 0.0;
  double edu_pay_ecpc_cvr_ = 0.0;
  double prerank_universe_deep_ = 0.0;
  double prerank_ecpm_ctr_ = 0.0;  // 粗排 ecpm 预估 ctr
  double prerank_ecpm_cvr_ = 0.0;  // 粗排 ecpm 预估 cvr
  double prerank_ecpm_live_ctr_ = 0.0;
  double lps_valid_clues_ = 0.0;
  double pred_ad_time_ = 0.0;          // 预估广告播放时长
  double live_prerank_p3s_ltv_ = 0.0;  // 直播 p3s -> ltv 粗排
  double prerank_wanjian_shouxin_rate_ecpc_ = 0.0;
  double prerank_biaodan_shouxin_rate_ = 0.0;
  double prerank_universe_invoke_ = 0.0;  // 联盟 item_imp -> invoke 粗排
  double prerank_universe_ctcvr_ = 0.0;
  double prerank_universe_ltr_ = 0.0;
  double prerank_search_ctcvr_ = 0.0;  // 搜索拟合的 ctcvr
  double jinniu_prerank_score = 0.0;
  double ecom_quality_ = 0.0;
  double prerank_cpm_ltr_ = 0.0;
  double prerank_real_action_rate_ = 0.0;
  double prerank_dnc_ctcvr_ = 0.0;
  double conv_key_action_rate_ = 0.0;
  double prerank_new_creative_lps_ = 0.0;
  double prerank_new_creative_conv_ = 0.0;
  double prerank_new_creative_deep_ = 0.0;
  double up_model_no_pay_rate_ = 0.0;
  double up_model_low_pay_rate_ = 0.0;
  double up_model_medium_pay_rate_ = 0.0;
  double up_model_high_pay_rate_ = 0.0;
  double inner_loop_ecpc_imp_conv_rate_ = 0.0;
  double video_play_7s_ = 0.0;
  double short_video_play_7s_ = 0.0;
  double effective_play_7s_ = 0.0;
  double video_effective_play_7s_ = 0.0;
  double short_video_effective_play_7s_ = 0.0;
  double conv_key_inapp_action_rate_ = 0.0;  // 联盟精排激活关键行为预估值
  double up_model_high_ltv_rate_ = 0.0;      //  联盟 up_model 高 ltv 人群概率
  double up_model_conv_key_inapp_rate_ = 0.0;
  double up_model_lps_intention_rate_ = 0.0;
  double up_model_conv_retention_rate_ = 0.0;
  double predict_auc_score_base_ = 0.0;
  double predict_auc_score_exp_ = 0.0;
  double live_prerank_stay_time_ = 0.0;
  double prerank_follow_ltv_ = 0.0;
  double click2_unified_ = 0.0;
  double prerank_unify_ltr_ = 0.0;
  double middle_platform_ = 0.0;
  double prerank_hard_live_ctcvr_ = 0.0;
  double inner_live_gmv_ = 0.0;
  double ee_score_ = 0.0;
  double ue_score_ = 0.0;
  double advertise_ltv_ = 0.0;
  double inner_item_click_hard_ = 0.0;
  double inner_item_click_native_ = 0.0;
  double reco_hard_order_paid_ = 0.0;
  double inner_refund_ = 0.0;
  double c1_app_invoked_ = 0.0;
  double c2_app_invoked_ = 0.0;
  double inner_cid_order_paid_value1_ = 0.0;
  double inner_cid_order_paid_value2_ = 0.0;
  double inner_cid_order_paid_value3_ = 0.0;
  double inner_cid_order_paid_value4_ = 0.0;
  double inner_cid_roas_value1_ = 0.0;
  double inner_cid_roas_value2_ = 0.0;
  double inner_cid_roas_value3_ = 0.0;
  double inner_cid_roas_value4_ = 0.0;
  double reco_merchant_ltv_ = 0.0;
  double reco_gmv_ = 0.0;
  double inner_roas_hard_cvr_param_ = 0.0;
  double inner_roas_soft_cvr_param_ = 0.0;
  double reco_soft_order_paid_ = 0.0;
  double direct_hard_order_paid_ = 0.0;
  double direct_soft_order_paid_ = 0.0;
  double lps_acquisition_ = 0.0;
  double ltv_1_day_pay_amount_ = 0.0;
  double ltv_2_7_day_pay_amount_ = 0.0;
  double inner_shelf_order_pay_cvr_pos1_ = 0.0;
  double inner_shelf_order_pay_cvr_pos2_ = 0.0;
  double inner_shelf_order_pay_cvr_pos3_ = 0.0;
  double inner_shelf_order_pay_cvr_pos4_ = 0.0;
  double inner_shelf_order_pay_cvr_pos5_ = 0.0;
  double inner_shelf_order_pay_cvr_pos6_ = 0.0;
  double inner_shelf_order_pay_cvr_pos7_ = 0.0;
  double inner_shelf_order_pay_cvr_pos8_ = 0.0;
  double inner_shelf_order_pay_cvr_pos9_ = 0.0;
  double inner_shelf_order_pay_cvr_pos10_ = 0.0;

  int set_field(const PredictType pt, double value, std::string cmd = "", int32_t cmd_id = 0) {
    // NOTE 加新字段需要在上面的 PredictType 里加上枚举
    switch (pt) {
      case PredictType::PredictType_ctr:
        ctr_ = value;
        break;
      case PredictType::PredictType_cvr:
        cvr_ = value;
        break;
      case PredictType::PredictType_play3s:
        play3s_ = value;
        break;
      case PredictType::PredictType_ltr:
        ltr_ = value;
        break;
      case PredictType::PredictType_wtr:
        wtr_ = value;
        break;
      case PredictType::PredictType_universe_prerank_event_order:
        c1_event_order_ = value;
        break;
      case PredictType::PredictType_universe_prerank_jiuniu_roas:
        prerank_jiuniu_roas_rate_ = value;
        break;
      case PredictType::PredictType_universe_prerank_key_action:
        prerank_imp_key_inapp_rate_ = value;
        break;
      case PredictType::PredictType_universe_prerank_item_click:
        universe_prerank_item_click_ = value;
        break;
      case PredictType::PredictType_universe_prerank_ad_merchant_follow:
        universe_prepank_merchant_follow_ = value;
        break;
      case PredictType::PredictType_univ_prerank_xdt_live_audience:
        univ_xdt_audience_rate_ = value;
        break;
      case PredictType::PredictType_univ_prerank_xdt_live_pay:
        univ_xdt_pay_rate_ = value;
        break;
      case PredictType::PredictType_univ_prerank_xdt_live_roas:
        univ_xdt_roas_rate_ = value;
        break;
      case PredictType::PredictType_delivery_rate:
        delivery_rate_ = value;
        break;
      // case PredictType::PredictType_htr:
      //   htr_ = value;
      //   break;
      case PredictType::PredictType_merchant_model_ltv_rate:
        universe_merchant_ltv_rate_ = value;
        break;
      case PredictType::PredictType_app_conversion_rate:
        app_conversion_rate_ = value;
        break;
      case PredictType::PredictType_c1_conv_fix:
        c1_conv_fix_ = value;
        break;
      case PredictType::PredictType_ncer:
        ncer_ = value;
        break;
      case PredictType::PredictType_landingpage_submit:
        landingpage_submit_rate_ = value;
        break;
      case PredictType::PredictType_retention:
        retention_rate_ = value;
        break;
      case PredictType::PredictType_purchase:
        purchase_rate_ = value;
        break;
      case PredictType::PredictType_click_retention:
        click_retention_rate_ = value;
        break;
      case PredictType::PredictType_click_purchase:
        click_purchase_rate_ = value;
        break;
      // case PredictType::PredictType_multitask_play3s:
      //   multitask_play3s_ = value;
      //   break;
      // case PredictType::PredictType_multitask_ltr:
      //   multitask_ltr_ = value;
      //   break;
      // case PredictType::PredictType_multitask_wtr:
      //   multitask_wtr_ = value;
      //   break;
      case PredictType::PredictType_credit:
        credit_rate_ = value;
        break;
      case PredictType::PredictType_click_credit:
        click_credit_rate_ = value;
        break;
      case PredictType::PredictType_click_wanjian:
        click_wanjian_rate_ = value;
        break;
      case PredictType::PredictType_credit_conv_grant:
        credit_conv_grant_rate_ = value;
        break;
      case PredictType::PredictType_credit_click2:
        click2_credit_rate_ = value;
        break;
      case PredictType::PredictType_federated_cvr:
        federated_cvr_ = value;
        break;
      case PredictType::PredictType_edu_lps2pay_cvr:
        edu_lps2pay_cvr_ = value;
        break;
      case PredictType::PredictType_edu_pay_ecpc_cvr:
        edu_pay_ecpc_cvr_ = value;
        break;
      case PredictType::PredictType_lps_valid_clues:
        lps_valid_clues_ = value;
        break;
      case PredictType::PredictType_credit_eval:
        credit_eval_rate_ = value;
        break;
      case PredictType::PredictType_click_insur_purchase:
        click_insur_purchase_rate_ = value;
        break;
      case PredictType::PredictType_c2_insur_purchase:
        c2_insur_purchase_rate_ = value;
        break;
      case PredictType::PredictType_ntr:
        ntr_ = value;
        break;
      case PredictType::PredictType_app_conversion_rate_calibration:
        app_calibration_conversion_rate_ = value;
        break;
      case PredictType::PredictType_apr:
        apr_ = value;
        break;
      case PredictType::PredictType_prod_apr:
        prod_apr_ = value;
        break;
      case PredictType::PredictType_deep_rate:
        deep_rate_ = value;
        break;
      case PredictType::PredictType_shop_action:
        shop_action_rate_ = value;
        break;
      case PredictType::PredictType_special_lps:
        special_lps_ = value;
        break;
      case PredictType::PredictType_deep_credit_spec_model:
        deep_credit_spec_rate_ = value;
        break;
      case PredictType::PredictType_server_show_cvr:
        server_show_cvr_ = value;
        break;
      case PredictType::PredictType_click2_conv:
        click2_conv_ = value;
        break;
      case PredictType::PredictType_click2_lps:
        click2_lps_ = value;
        break;
      case PredictType::PredictType_click2_prod_apr:
        click2_prod_apr_ = value;
        break;
      case PredictType::PredictType_adx_scvr:
        adx_scvr_ = value;
        break;
      case PredictType::PredictType_conv2_purchase:
        conv2_purchase_ = value;
        break;
      case PredictType::PredictType_lps2_purchase:
        lps2_purchase_ = value;
        break;
      case PredictType::PredictType_clientshow_click2:
        clientshow_click2_ = value;
        break;
      case PredictType::PredictType_click2_shop_action:
        click2_shop_action_ = value;
        break;
      case PredictType::PredictType_click2_nextstay:
        click2_nextstay_ = value;
        break;
      case PredictType::PredictType_click2_purchase:
        click2_purchase_ = value;
        break;
      case PredictType::PredictType_click2_deep_rate:
        click2_deep_rate_ = value;
        break;
      case PredictType::PredictType_server_show_ctr:
        server_show_ctr_ = value;
        break;
      case PredictType::PredictType_click2_purchase_device:
        click2_purchase_device_ = value;
        break;
      case PredictType::PredictType_click_purchase_device:
        click_purchase_device_ = value;
        break;
      case PredictType::PredictType_conv_ltv:
        conv_ltv_ = value;
        break;
      case PredictType::PredictType_purchase_ltv:
        purchase_ltv_ = value;
        break;
      // case PredictType::PredictType_twin_purchase_ltv:
      //   purchase_ltv_ = value;
      //   break;
      case PredictType::PredictType_ug_conv_pltv7:
        ug_conv_pltv7_ = value;
        break;
      case PredictType::PredictType_conv_key_inapp_action_rate:
        conv_key_inapp_action_rate_ = value;
        break;
      case PredictType::PredictType_7_day_pay_times:
        pay_times_ = value;
        break;
      case PredictType::PredictType_game_purchase_ltv:
        purchase_ltv_ = value;
        break;
      case PredictType::PredictType_preranking_purchase_ltv:
        purchase_ltv_ = value;
        break;
      case PredictType::PredictType_preranking_purchase_ltv7:
        purchase_ltv7_ = value;
        break;
      case PredictType::PredictType_preranking_order_submit:
        order_submit_ = value;
        break;
      case PredictType::PredictType_inner_cpr:
        inner_cpr_ = value;
        break;
      case PredictType::PredictType_c1_merchant_follow:
        c1_merchant_follow_ = value;
        break;
      case PredictType::PredictType_nebula_merchant_follow:
        nebula_merchant_follow_ = value;
        break;
      case PredictType::PredictType_c1_order_paid:
        c1_order_paied_ = value;
        break;
      case PredictType::PredictType_c2_order_paid:
        c2_order_paied_ = value;
        break;
      case PredictType::PredictType_c2_button_click:
        c2_button_click_ = value;
        break;
      case PredictType::PredictType_c1_button_click:
        c1_button_click_ = value;
        break;
      case PredictType::PredictType_click_purchase_rate_single_bid:
        click_purchase_rate_single_bid_ = value;
        break;
      case PredictType::PredictType_click2_purchase_rate_single_bid:
        click2_purchase_rate_single_bid_ = value;
        break;
      case PredictType::PredictType_conv_nextstay:
        conv_nextstay_ = value;
        retention_rate_ = value;
        break;
      case PredictType::PredictType_server_client_show_rate:
        server_client_show_rate_ = value;
        break;
      case PredictType::PredictType_c1_prerank_unified:
        c1_prerank_unified_ = value;
        break;
      case PredictType::PredictType_c2_prerank_unified:
        c2_prerank_unified_ = value;
        break;
      case PredictType::PredictType_nebula_prerank_merchant_follow:
        nebula_prerank_merchant_follow_ = value;
        break;
      case PredictType::PredictType_nebula_prerank_purchase:
        nebula_prerank_purchase_ = value;
        break;
      case PredictType::PredictType_prerank_thanos_xdt_order_paied:
        prerank_thanos_xdt_order_paied_ = value;
        break;
      // case PredictType::PredictType_feed_prerank_merchant_follow:
      //   feed_prerank_merchant_follow_ = value;
      //   break;
      // case PredictType::PredictType_feed_prerank_purchase:
      //   feed_prerank_purchase_ = value;
      //   break;
      case PredictType::PredictType_merchant_follow_ocpm:
        server_show_merchant_follow_rate_ = value;
        break;
      case PredictType::PredictType_live_goods_view:
        live_goods_view_ = value;
        break;
      case PredictType::PredictType_live_pay_rate:
        live_pay_rate_ = value;
        break;
      case PredictType::PredictType_live_audience:
        live_audience_ = value;
        break;
      case PredictType::PredictType_live_play3s:
        live_play_3s_ = value;
        break;
      case PredictType::PredictType_live_server_show_play3s_feed:
        live_server_show_play3s_feed_ = value;
        break;
      case PredictType::PredictType_live_server_show_play3s_slide:
        live_server_show_play3s_slide_ = value;
        break;
      case PredictType::PredictType_live_server_show_wtr:
        live_server_show_wtr_ = value;
        break;
      case PredictType::PredictType_live_p3s_wtr:
        live_p3s_wtr_ = value;
        break;
      case PredictType::PredictType_item_impression_wtr:
        item_impression_wtr_ = value;
        break;
      case PredictType::PredictType_game_appoint_rate:
        game_appoint_rate_ = value;
        break;
      case PredictType::PredictType_c2_game_appoint_rate:
        c2_game_appoint_rate_ = value;
        break;
      case PredictType::PredictType_new_creative_delivery_rate:
        new_creative_delivery_rate_ = value;
        break;
      case PredictType::PredictType_click_app_invoked:
        click_app_invoked_ = value;
        break;
      case PredictType::PredictType_click2_app_invoked:
        click2_app_invoked_ = value;
        break;
      case PredictType::PredictType_merchant_ltv:
        merchant_ltv_ = value;
        break;
      case PredictType::PredictType_live_p3s_ltv:
        live_p3s_ltv_ = value;
        break;
      case PredictType::PredictType_inner_live_gmv:
        inner_live_gmv_ = value;
        break;
      case PredictType::PredictType_live_prerank_inroom_nebula:
        live_prerank_inroom_nebula_ = value;
        break;
      case PredictType::PredictType_live_prerank_inroom_feed:
        live_prerank_inroom_feed_ = value;
        break;
      case PredictType::PredictType_live_prerank_p3s_wtr:
        live_prerank_p3s_wtr_ = value;
        break;  // 直播粗排 p3s -> follow
      case PredictType::PredictType_photo_to_live_item_imp_wtr:
        photo_to_live_item_imp_wtr_ = value;
        break;  // 短视频直播粗排 item_imp -> order
      case PredictType::PredictType_prerank_live_p3s_ltv:
        live_prerank_p3s_ltv_ = value;
        break;  // 直播 p3s -> ltv 粗排
      case PredictType::PredictType_prerank_p3s_pay_rate:
        prerank_p3s_pay_rate_ = value;
        break;
      // case PredictType::PredictType_thanos_prerank_conv:
      //   prerank_thanos_conv_ = value;
      //   break;
      // case PredictType::PredictType_thanos_prerank_lps:
      //   prerank_thanos_lps_ = value;
      //   break;
      // case PredictType::PredictType_thanos_prerank_follow:
      //   prerank_thanos_follow_ = value;
      //   break;
      // case PredictType::PredictType_thanos_prerank_purchase:
      //   prerank_thanos_purchase_ = value;
      //   break;
      // case PredictType::PredictType_thanos_prerank_deep:
      //   prerank_thanos_deep_ = value;
      //   break;
      // case PredictType::PredictType_thanos_prerank_scvr:
      //   prerank_thanos_scvr_ = value;
      //   break;
      case PredictType::PredictType_universe_prerank_deep:
        prerank_universe_deep_ = value;
        break;
      case PredictType::PredictType_prerank_ecpm_ctr:
        prerank_ecpm_ctr_ = value;
        break;
      case PredictType::PredictType_prerank_ecpm_cvr:
        prerank_ecpm_cvr_ = value;
        break;
      case PredictType::PredictType_prerank_ecpm_live_ctr:
        prerank_ecpm_live_ctr_ = value;
        break;
      case PredictType::PredictType_universe_prerank_invoke:
        prerank_universe_invoke_ = value;
        break;
      case PredictType::PredictType_universe_prerank_ctcvr:
        prerank_universe_ctcvr_ = value;
        break;
      case PredictType::PredictType_universe_prerank_ltr:
        prerank_universe_ltr_ = value;
        break;
      case PredictType::PredictType_search_prerank_ctcvr:
        prerank_search_ctcvr_ = value;
        break;
      case PredictType::PredictType_jinniu_prerank_score:
        jinniu_prerank_score = value;
        break;
      case PredictType::PredictType_prerank_pred_ad_time:
        pred_ad_time_ = value;
        break;
      case PredictType::PredictType_prerank_cpm_ltr:
        prerank_cpm_ltr_ = value;
        break;
      case PredictType::PredictType_real_action:
        prerank_real_action_rate_ = value;
        break;
      case PredictType::PredictType_prerank_dnc_ctcvr:
        prerank_dnc_ctcvr_ = value;
        break;
      case PredictType::PredictType_conv_key_action:
        conv_key_action_rate_ = value;
        break;
      // case PredictType::PredictType_prerank_new_creative_lps:
      //   prerank_new_creative_lps_ = value;
      //   break;
      // case PredictType::PredictType_prerank_new_creative_conv:
      //   prerank_new_creative_conv_ = value;
      //   break;
      // case PredictType::PredictType_prerank_new_creative_deep:
      //   prerank_new_creative_deep_ = value;
      //   break;
      case PredictType::PredictType_up_model_no_pay_rate:
        up_model_no_pay_rate_ = value;
        break;
      case PredictType::PredictType_up_model_low_pay_rate:
        up_model_low_pay_rate_ = value;
        break;
      case PredictType::PredictType_up_model_medium_pay_rate:
        up_model_medium_pay_rate_ = value;
        break;
      case PredictType::PredictType_up_model_high_pay_rate:
        up_model_high_pay_rate_ = value;
        break;
      case PredictType::PredictType_inner_loop_ecpc_imp_conv_rate:
        inner_loop_ecpc_imp_conv_rate_ = value;
        break;
      case PredictType::PredictType_video_play_7s:
        video_play_7s_ = value;
        break;
      case PredictType::PredictType_short_video_play_7s:
        short_video_play_7s_ = value;
        break;
      case PredictType::PredictType_effective_play_7s:
        effective_play_7s_ = value;
        break;
      case PredictType::PredictType_video_effective_play_7s:
        video_effective_play_7s_ = value;
        break;
      case PredictType::PredictType_short_video_effective_play_7s:
        short_video_effective_play_7s_ = value;
        break;
      case PredictType::PredictType_up_model_high_ltv_rate:
        up_model_high_ltv_rate_ = value;
        break;
      case PredictType::PredictType_up_model_conv_key_inapp_rate:
        up_model_conv_key_inapp_rate_ = value;
        break;
      case PredictType::PredictType_up_model_lps_intention_rate:
        up_model_lps_intention_rate_ = value;
        break;
      case PredictType::PredictType_up_model_conv_retention_rate:
        up_model_conv_retention_rate_ = value;
        break;
      case PredictType::PredictType_auc_model_base:
        predict_auc_score_base_ = value;
        break;
      case PredictType::PredictType_auc_model_exp:
        predict_auc_score_exp_ = value;
        break;
      case PredictType::PredictType_live_prerank_stay_time:
        live_prerank_stay_time_ = value;
        break;
      case PredictType::PredictType_prerank_follow_ltv:
        prerank_follow_ltv_ = value;
        break;
      case PredictType::PredictType_click2_unified_rate:
        click2_unified_ = value;
        break;
      case PredictType::PredictType_prerank_unify_ltr:
        prerank_unify_ltr_ = value;
      case PredictType::PredictType_middle_platform:
        middle_platform_ = value;
        break;
      case PredictType::PredictType_prerank_hard_live_ctcvr:
        prerank_hard_live_ctcvr_ = value;
        break;
      case PredictType::PredictType_ee_search_score:
        ee_score_ = value;
        break;
      case PredictType::PredictType_ue_score:
        ue_score_ = value;
        break;
      case PredictType::PredictType_preranking_advertise_ltv:
        advertise_ltv_ = value;
        break;
     case PredictType::PredictType_inner_item_click_hard:
      inner_item_click_hard_ = value;
      break;
     case PredictType::PredictType_inner_item_click_native:
      inner_item_click_native_ = value;
      break;
     case PredictType::PredictType_reco_hard_order_paid:
      reco_hard_order_paid_ = value;
      break;
     case PredictType::PredictType_reco_soft_order_paid:
      reco_soft_order_paid_ = value;
      break;
     case PredictType::PredictType_direct_hard_order_paid:
      direct_hard_order_paid_ = value;
      break;
     case PredictType::PredictType_direct_soft_order_paid:
      direct_soft_order_paid_ = value;
      break;
     case PredictType::PredictType_reco_merchant_ltv:
      reco_merchant_ltv_ = value;
      break;
     case PredictType::PredictType_reco_gmv:
      reco_gmv_ = value;
      break;
     case PredictType::PredictType_inner_roas_hard_cvr_param:
      inner_roas_hard_cvr_param_ = value;
      break;
     case PredictType::PredictType_inner_roas_soft_cvr_param:
      inner_roas_soft_cvr_param_ = value;
      break;
     case PredictType::PredictType_lps_acquisition:
      lps_acquisition_ = value;
      break;
     case PredictType::PredictType_inner_refund:
      inner_refund_ = value;
      break;
     case PredictType::PredictType_c1_app_invoked:
      c1_app_invoked_ = value;
      break;
     case PredictType::PredictType_c2_app_invoked:
      c2_app_invoked_ = value;
      break;
     case PredictType::PredictType_1_day_pay_amount:
      ltv_1_day_pay_amount_ = value;
      break;
     case PredictType::PredictType_2_7_day_pay_amount:
      ltv_2_7_day_pay_amount_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos1:
      inner_shelf_order_pay_cvr_pos1_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos2:
      inner_shelf_order_pay_cvr_pos2_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos3:
      inner_shelf_order_pay_cvr_pos3_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos4:
      inner_shelf_order_pay_cvr_pos4_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos5:
      inner_shelf_order_pay_cvr_pos5_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos6:
      inner_shelf_order_pay_cvr_pos6_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos7:
      inner_shelf_order_pay_cvr_pos7_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos8:
      inner_shelf_order_pay_cvr_pos8_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos9:
      inner_shelf_order_pay_cvr_pos9_ = value;
      break;
     case PredictType::PredictType_inner_shelf_order_pay_cvr_pos10:
      inner_shelf_order_pay_cvr_pos10_ = value;
      break;
      default:
        LOG_EVERY_N(ERROR, 1000) << "unknown field name: " << pt;
        return -1;
    }
    valid_ = true;
    if (std::fabs(value) > FLT_EPSILON) {
      result_not_null_ = true;
    }
    return 0;
  }
};

enum CmdSessionContextSource {
  CMD_SOURCE_JSON = 0,
  CMD_SOURCE_AD_DSP = 1,
  CMD_SOURCE_AD_ADX = 2,
  CMD_SOURCE_FANS_TOP = 3,
  CMD_SOURCE_KWAI_I18N_AD_FEED = 4
};

enum CmdConfigType {
  kAdDspDefaultPrerank = 0,
  kAdI18nDefaultPrerank = 100,
  kAdI18nDefaultRank = 101,
};

enum CmdStrategyTag {
  DEFAULT_TAG = 0,  // 默认，占位
  INNER_MULTI_PREDICT_TAG = 1,  // 内循环软硬双头预估
  FIRST_STAGE_PREDICT_TAG = 2   // 两阶段模型第一阶段
};

// preranking 和 ranking_prepare 使用
struct CmdWrapper {
  PredictType pt_ = PredictType::PredictType_Unknown;  // 对应的 PredictType
  std::string pt_str_;  // pt_ 对应的 name
  std::string cmd_config_key_;   // conf 文件里的 cmd key
  std::string cmd_;              // cmd 全名
  std::string cmd_key_tag_{""};      // cmd key 所属分类
  std::string cmd_key_main_scene_{""};  // cmd key 场景
  int cmd_id_;
  int cmd_key_id_ = 0;
  int32_t session_context_source_type = CMD_SOURCE_JSON;

  CmdWrapper(PredictType pt, const std::string &key) : pt_(pt), cmd_config_key_(key) {
    pt_str_ = pt_._to_string();
  }

  CmdWrapper(PredictType pt, const std::string &key, int32_t source_type)
      : pt_(pt), cmd_config_key_(key), session_context_source_type(source_type) {
    pt_str_ = pt_._to_string();
  }

  static int64_t to_id(const CmdWrapper &cmd) {
    static absl::Hash<CmdWrapper> hash;
    return hash(cmd);
  }

 private:
  template <typename H> friend H AbslHashValue(H h, const CmdWrapper &c) {
    auto state = std::move(h);
    state = H::combine(std::move(state), c.cmd_config_key_, c.pt_, c.session_context_source_type);
    return state;
  }
};

struct CmdWrapperV2 {
  std::vector<RType> rt_;  // 对应的 RType
  std::vector<std::string> rt_str_;
  std::vector<PredictType> pt_;  // 对应的 PredictType, 兼容精排单 cmd 多 predict type 情况
  std::vector<PredictEmbeddingType> pet_;  // 对应的 PredictEmbeddingType
  std::vector<std::string> pt_str_;
  std::string cmd_key_;   // conf 文件里的 cmd key
  std::string cmd_;              // cmd 全名
  std::string cmd_key_tag_{""};      // cmd key 所属分类
  std::string cmd_key_main_scene_{""};  // cmd key 场景
  int32_t cmd_value_num_ = 1;  //  本次请求返回几个值
  int32_t cmd_id_ = 0;
  int32_t cmd_key_id_ = 0;
  int32_t session_context_source_type = CMD_SOURCE_AD_DSP;
  bool auc_cmd_{false};
  bool multi_predict_cmd_{false};
  CmdStrategyTag strategy_tag_ = CmdStrategyTag::DEFAULT_TAG;
  int32_t need_predict_embedding_ = -1;  // 是否预估 embedding，-1 为普通模型，0 为不预估，1 为预估

  CmdWrapperV2(RType r_type, PredictType pt, const std::string &key)
    : rt_(1, r_type), pt_(1, pt), cmd_key_(key),
      cmd_value_num_(1) {}
  CmdWrapperV2(bool is_auc, RType r_type, PredictType pt, const std::string &key)
    : rt_(1, r_type), pt_(1, pt), cmd_key_(key),
      cmd_value_num_(1), auc_cmd_(true) {}

  CmdWrapperV2(RType r_type, PredictType pt, const std::string &key, int32_t source_type)
    : rt_(1, r_type), pt_(1, pt), cmd_key_(key),
      session_context_source_type(source_type),
      cmd_value_num_(1) {}
  CmdWrapperV2(bool is_auc, RType r_type, PredictType pt, const std::string &key, int32_t source_type)
    : rt_(1, r_type), pt_(1, pt), cmd_key_(key),
      session_context_source_type(source_type),
      cmd_value_num_(1), auc_cmd_(true) {}

  CmdWrapperV2(const std::vector<RType>& rts, const std::vector<PredictType>& pts, const std::string &key)
    : rt_(rts), pt_(pts), cmd_key_(key),
      cmd_value_num_(pt_.size()) {}
  CmdWrapperV2(bool is_auc, const std::vector<RType>& rts, const std::vector<PredictType>& pts,
               const std::string &key)
    : rt_(rts), pt_(pts), cmd_key_(key),
      cmd_value_num_(pt_.size()), auc_cmd_(true) {}


  CmdWrapperV2(const std::vector<RType>& rts, const std::vector<PredictType>& pts,
               const std::string &key, int32_t source_type)
    : rt_(rts), pt_(pts), cmd_key_(key),
      session_context_source_type(source_type),
      cmd_value_num_(pt_.size()) {}
  CmdWrapperV2(bool is_auc, const std::vector<RType>& rts, const std::vector<PredictType>& pts,
               const std::string &key, int32_t source_type)
    : rt_(rts), pt_(pts), cmd_key_(key),
      session_context_source_type(source_type),
      cmd_value_num_(pt_.size()), auc_cmd_(true) {}

  // 支持扩展策略接口
  CmdWrapperV2(CmdStrategyTag strategy_tag, RType r_type, PredictType pt,
               const std::string &key, int32_t source_type)
    : strategy_tag_(strategy_tag), rt_(1, r_type), pt_(1, pt), cmd_key_(key),
      session_context_source_type(source_type),
      cmd_value_num_(1) {}
  CmdWrapperV2(CmdStrategyTag strategy_tag, const std::vector<RType>& rts, const std::vector<PredictType>& pts,  // NOLINT
               const std::string &key, int32_t source_type)
    : strategy_tag_(strategy_tag), rt_(rts), pt_(pts), cmd_key_(key),
      session_context_source_type(source_type),
      cmd_value_num_(pt_.size()), auc_cmd_(true) {}

    CmdWrapperV2(CmdStrategyTag strategy_tag, const std::vector<RType>& rts,
               const std::vector<PredictType>& pts,
               const std::vector<PredictEmbeddingType>& pets,
               const std::string &key, int32_t source_type, int32_t need_predict_embedding = 1)
    : strategy_tag_(strategy_tag), rt_(rts), pt_(pts), pet_(pets), cmd_key_(key),
      session_context_source_type(source_type),
      cmd_value_num_(pt_.size()),
      need_predict_embedding_(need_predict_embedding) {}

  const std::string& pt_str(int32_t cmd_index) const {
    static const std::string kUnknown = "unknown";
    return (cmd_index < pt_str_.size() && cmd_index >= 0) ? pt_str_[cmd_index] : kUnknown;
  }
};

static absl::flat_hash_map<std::string, kuaishou::ad::algorithm::RequestScene> REQ_SCENE_MAP = {
  {"REQ_SCENE_UNKNOWN", kuaishou::ad::algorithm::RequestScene::REQ_SCENE_UNKNOWN},
  {"REQ_SCENE_OUTER", kuaishou::ad::algorithm::RequestScene::REQ_SCENE_OUTER},
  {"REQ_SCENE_INNER", kuaishou::ad::algorithm::RequestScene::REQ_SCENE_INNER},
  {"REQ_SCENE_SEARCH", kuaishou::ad::algorithm::RequestScene::REQ_SCENE_SEARCH},
  {"REQ_SCENE_UNIVERSE", kuaishou::ad::algorithm::RequestScene::REQ_SCENE_UNIVERSE},
  {"REQ_SCENE_AUC", kuaishou::ad::algorithm::RequestScene::REQ_SCENE_AUC}
};

}  // namespace engine_base
}  // namespace ks
