#pragma once
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/engine_base/table_schema/utils/feature_sdk_config.h"
namespace ks {
namespace engine_base {
namespace table_schema {

class KconfUtil {
 public:
  DEFINE_PROTOBUF_NODE_KCONF(ks::engine_base::table_schema::FeatureSdkConfig, ad.adEngineDataPlatform,
                             userDataFeatureSdkConfig)
  DEFINE_PROTOBUF_NODE_KCONF(ks::engine_base::table_schema::FeatureSdkConfig, ad.dataLink,
                             userDataFeatureSdkConfigV2)
  DEFINE_STRING_KCONF_NODE(ad.dataLink, userDataCenterKessName)
};

}  // namespace table_schema
}  // namespace engine_base
}  // namespace ks
