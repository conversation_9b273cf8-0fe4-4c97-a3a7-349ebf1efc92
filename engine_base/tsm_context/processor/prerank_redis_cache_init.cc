#include "teams/ad/engine_base/tsm_context/processor/prerank_redis_cache_init.h"

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/util/logging_util.h"
#include "glog/logging.h"
#include "teams/ad/engine_base/tsm_context/tsm_context.pb.h"
#include "teams/ad/engine_base/tsm_context/util/session_cache_ad_mgr.h"
#include "teams/ad/ad_target/utils/common/ad_utils.h"
#include "teams/ad/engine_base/session_cache/session_cache_redis.pb.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/ad_target/framework/tsm/common.h"
#include "teams/ad/ad_base/src/common/ad_session_context.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/engine_base/tsm_context/data/session_cache_ad.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_base.h"

namespace ks {
namespace engine_base {

bool PrerankRedisCacheInitMixer::InitProcessor() {
  tsm_biz_ = config()->GetString("tsm_biz", "");
  cache_redis_cluster_name_ = config()->GetString("cache_redis_cluster_name", "");
  key_biz_prefix_ = config()->GetString("key_biz_prefix", "");
  parse_from_ = config()->GetString("parse_from");
  serialize_to_ = config()->GetString("serialize_to");
  if (!parse_from_.empty() && !serialize_to_.empty()) {
    LOG(ERROR) << "Set parse_from and serialize_to at the same time is not allowed!";
    return false;
  }
  return true;
}

void PrerankRedisCacheInitMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  if (!context) {
    return;
  }
  auto* prerank_caches =
      context->GetMutablePtrCommonAttr<absl::flat_hash_map<std::string, SessionCacheAdParams>>(
          "prerank_caches");
  if (!prerank_caches) {
    LOG_EVERY_N(WARNING, 500) << "prerank_caches not found.";
    return;
  }
  SessionCacheAdParams& param = (*prerank_caches)[tsm_biz_];
  // 上移后 ad_target 基于 ad_server 序列化的数据解析
  if (!parse_from_.empty()) {
    auto str = context->GetStringCommonAttr(parse_from_).value_or("");
    ks::engine_base::CacheParams src;
    bool ok = src.ParseFromArray(str.data(), str.size());
    if (!ok) {
      LOG(ERROR) << "Parse prerank redis cache from " << parse_from_ << " failed!";
      return;
    }
    param.enable_session_cache = src.enable_session_cache();
    param.is_hit_cache = src.is_hit_cache();
    param.dcaf_pv_score = src.dcaf_pv_score();

    std::vector<ks::ad_target::SessionCacheAd> selected_ad_list;
    for (const auto& cache_ad : src.selected_ad_list()) {
      selected_ad_list.emplace_back(cache_ad.unit_id(), cache_ad.creative_id(), cache_ad.retrieval_tag());
    }
    auto ads = std::make_shared<ks::ad_target::SessionCacheAdList>();
    ads->ads.swap(selected_ad_list);
    param.accessor = ads;

    for (const auto& item : param.accessor->ads) {
      param.creative_id_2_retrieval_tag_map.try_emplace(item.creative_id, item.retrieval_tag);
    }
  } else {
    // base 逻辑
    InitRedisSessionCache(&param, context);
  }
  // 上移后 ad_server 将数据序列化
  if (!serialize_to_.empty()) {
    if (!param.accessor) {
      return;
    }
    ks::engine_base::CacheParams dst;
    dst.set_enable_session_cache(param.enable_session_cache);
    dst.set_is_hit_cache(param.is_hit_cache);
    dst.set_dcaf_pv_score(param.dcaf_pv_score);
    for (const auto& cache_ad : param.accessor->ads) {
      auto* cache_ad_pb = dst.add_selected_ad_list();
      cache_ad_pb->set_creative_id(cache_ad.creative_id);
      cache_ad_pb->set_unit_id(cache_ad.unit_id);
      cache_ad_pb->set_retrieval_tag(cache_ad.retrieval_tag);
    }
    context->SetStringCommonAttr(serialize_to_, dst.SerializeAsString());
    LOG_IF(INFO, platform::LoggingUtil::IsLoggingEnabled())
        << "Serialize prerank redis cache to attr " << serialize_to_ << ", content "
        << dst.ShortDebugString();
  }
  return;
}

void PrerankRedisCacheInitMixer::InitRedisSessionCache(SessionCacheAdParams* param,
                                                       ks::platform::AddibleRecoContextInterface* context) {
  if (!param) {
    LOG_EVERY_N(WARNING, 500) << "SessionCacheAdParams is nullptr.";
    return;
  }
  auto* ad_request = context->GetPtrCommonAttr<kuaishou::ad::AdRequest>("ad_request");
  if (!ad_request) {
    LOG_EVERY_N(WARNING, 500) << "ad_request is nullptr.";
    return;
  }
  if (ad_request->is_aggr_second_req()) {
    return;
  }
  if (ad_request->is_serial_aggr_card()) {
    return;
  }
  if (ad_request->is_common_card_second_request()) {
    return;
  }
  auto* dot = context->GetMutablePtrCommonAttr<ks::ad_base::Dot>("tsm_dot");
  if (!dot) {
    LOG_EVERY_N(WARNING, 500) << "tsm_dot is nullptr.";
    return;
  }
  auto* session_context = context->GetPtrCommonAttr<ks::SessionContext>("session_context");
  if (!session_context) {
    LOG_EVERY_N(WARNING, 500) << "session_context is nullptr.";
    return;
  }
  // session cache 准入判断
  // 对齐原本地 cache 准入逻辑
  int32_t target_type = 1;  // defaule
  if (tsm_biz_ == ks::ad_target::tsm::kTsmSceneInnerSoftPhoto) {
    target_type = 2;  // fanstop
  } else if (tsm_biz_ == ks::ad_target::tsm::kTsmSceneInnerHardPhoto) {
    target_type = 4;  // AMD
  } else if (tsm_biz_ == ks::ad_target::tsm::kTsmSceneLive) {
    target_type = 8;  // live
    return;
  }

  param->enable_session_cache = session_context->TryGetInteger("session_cache_target_type", 0) & target_type;
  param->enable_session_cache = param->enable_session_cache &&
       engine_base::AdKconfUtil::enableNearlinePassCache();
  if (!param->enable_session_cache) {
    return;
  }

  int64_t page_id = context->GetIntCommonAttr("page_id").value_or(0);
  int64_t sub_page_id = context->GetIntCommonAttr("sub_page_id").value_or(0);
  auto device_id = context->GetStringCommonAttr("device_id").value_or("");
  param->enable_session_cache =
      session_context->TryGetBoolean(absl::Substitute("enable_session_cache_page_id_$0", page_id), false);
  if (!param->enable_session_cache) {
    return;
  }

  // 页面首刷不走缓存
  if (session_context->TryGetBoolean("enable_cache_up_refresh_filter", false)) {
    auto* pos_manager_base = context->GetPtrCommonAttr<ks::ad_base::PosManagerBase>("pos_manager_base");
    if (!pos_manager_base) {
      LOG_EVERY_N(WARNING, 500) << "pos_manager_base is nullptr.";
      return;
    }

    param->enable_session_cache =
      !(session_context->
        TryGetBoolean(absl::Substitute("enable_cache_exclude_up_refresh_$0", sub_page_id), false)
      && pos_manager_base->GetRefreshDirection() == kuaishou::ad::AdEnum_RefreshDirection_UP_REFRESH);

    if (!param->enable_session_cache) {
      dot->Count(1, "session_cache_up_refresh_exclude", std::to_string(sub_page_id));
      return;
    }
  }

  if (device_id.empty()) {
    param->enable_session_cache = false;
    return;
  }

  auto client = ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
      cache_redis_cluster_name_, engine_base::DependDataLevel::WEAK_DEPEND);
  if (!client) {
    LOG(ERROR) << cache_redis_cluster_name_ << ":CreateRpcFail";
    return;
  }
  std::string key = absl::Substitute(key_biz_prefix_ + "$0" + "$1", sub_page_id, device_id);
  if (session_context->TryGetBoolean("enable_rank_adlist_cache", false)) {
    auto user_id = ad_request->reco_user_info().id();
    std::string new_key_biz_prefix;

    if (session_context->TryGetBoolean("enable_adlist_cache_inner_soft", false)) {
      if (key_biz_prefix_ == "outer") {
        new_key_biz_prefix = "outer_hard_photo_rank";
      } else if (key_biz_prefix_ == "inner_hard") {
        new_key_biz_prefix = "inner_hard_photo_rank";
      } else if (key_biz_prefix_ == "inner_soft") {
        new_key_biz_prefix = "inner_soft_photo_rank";
      }
    } else {
      if (key_biz_prefix_ == "outer") {
        new_key_biz_prefix = "outer_hard_photo_rank";
      } else if (key_biz_prefix_ == "inner_hard") {
        new_key_biz_prefix = "inner_hard_photo_rank";
      }
    }

    key = absl::Substitute(new_key_biz_prefix + "$0" + "$1" + "$2",
        sub_page_id, user_id, device_id);
  }
  std::string page_id_string = base::Int64ToString(page_id);
  std::string redis_result;
  auto status = client->Get(key, &redis_result, 10);

  bool is_hit_cache = true;
  if ((status != ks::infra::RedisErrorCode::REDIS_NO_ERROR &&
       status != ks::infra::RedisErrorCode::REDIS_ERR_NONEXIST) || redis_result.empty()) {
    is_hit_cache = false;
  }
  dot->Interval(is_hit_cache, "hit_cache_ratio", "redis_cache", page_id_string);
  if (!is_hit_cache) {
    return;
  }

  kuaishou::ad::SessionCacheAd session_cache_data;
  session_cache_data.ParseFromString(redis_result);

  std::vector<ad_target::SessionCacheAd> selected_ad_list;
  auto ads = std::make_shared<ad_target::SessionCacheAdList>();

  for (int i = 0; i < session_cache_data.cache_ad_info_size(); ++i) {
    const auto& item = session_cache_data.cache_ad_info(i);
    selected_ad_list.emplace_back(0, item.creative_id(), item.retrieval_tag());
  }
  dot->Interval(selected_ad_list.size(), "read_cache_size", absl::StrCat(target_type),
                page_id_string, session_context->TryGetString("cpm_thr_08_exp_name", ""));

  ads->ads.swap(selected_ad_list);
  param->accessor = ads;
  param->enable_session_cache = true;
  param->is_hit_cache = true;
  param->creative_id_2_retrieval_tag_map.reserve(param->accessor->ads.size() * 2);
  param->dcaf_pv_score = session_cache_data.dcaf_pv_score();
  for (const auto& item : param->accessor->ads) {
    param->creative_id_2_retrieval_tag_map.try_emplace(item.creative_id,
                                                item.retrieval_tag);
  }
  dot->Interval(param->accessor->ads.size(), "cache_ad_size", "redis_cache", page_id_string);
}
}  // namespace engine_base
}  // namespace ks
