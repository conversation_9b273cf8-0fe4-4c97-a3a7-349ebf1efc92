#include "teams/ad/ad_storewide/src/processor/prerank/mixer/storewide_prerank_select_and_fill_mixer.h"

#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <memory>
#include <algorithm>
#include "perfutil/perfutil.h"
#include "teams/ad/ad_storewide/utils/kconf_utils.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/ad_base/src/common/enum.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"

namespace ks {
namespace ad_storewide {

bool StorewidePrerankSelectAndFillMixer::InitProcessor() {
  item_table_name_ = GetTableName();
  storewide_auto_roas_adjust_weight_conf_ = config()->Get("storewide_auto_roas_adjust_weight");
  storewide_auto_roas_adjust_weight_merchant_conf_ =
      config()->Get("storewide_auto_roas_adjust_weight_merchant");
  storewide_auto_roas_adjust_weight_follow_conf_ =
      config()->Get("storewide_auto_roas_adjust_weight_follow");
  photo_storewide_roas_adjust_weight_conf_ = config()->Get("photo_storewide_roas_adjust_weight");
  photo_storewide_roas_adjust_weight_merchant_conf_ =
      config()->Get("photo_storewide_roas_adjust_weight_merchant");
  photo_storewide_roas_adjust_weight_follow_conf_ =
      config()->Get("photo_storewide_roas_adjust_weight_follow");
  enable_merchant_storewide_reco_page_cali_conf_ =
      config()->Get("enable_merchant_storewide_reco_page_cali");
  enable_merchant_storewide_reco_follow_cali_conf_ =
    config()->Get("enable_merchant_storewide_reco_follow_cali");
  enable_merchant_storewide_reco_nebula_follow_cali_conf_ =
    config()->Get("enable_merchant_storewide_reco_nebula_follow_cali");
  enable_merchant_storewide_reco_follow_inner_cali_conf_ =
    config()->Get("enable_merchant_storewide_reco_follow_inner_cali");
  enable_storewide_check_campaign_type_conf_ =
      config()->Get("enable_storewide_check_campaign_type");
  enable_roi_ratio_pk_conf_ =
      config()->Get("enable_roi_ratio_pk");
  enable_merchant_living_for_agent_conf_ =
      config()->Get("enable_merchant_living_for_agent");
  return true;
}

void StorewidePrerankSelectAndFillMixer::OnPipelineExit(ks::platform::ReadableRecoContextInterface* context) {
  attr_is_valid_ = nullptr;
  attr_ad_roi_item_type_ = nullptr;
  attr_ad_roi_trans_info_ = nullptr;
  attr_ad_roi_creative_id_ = nullptr;
  attr_ad_roi_auto_roi_ = nullptr;
  attr_ad_roi_budget_status_ = nullptr;
  return;
}

bool StorewidePrerankSelectAndFillMixer::CheckDaitouValid(int64_t origin_photo_id, const AdItem& ad_item) {
  if (!AdKconfUtil::enableDaitouFilter()) {
    return true;
  }
  if (origin_photo_id <= 0) {
    return true;
  }
  if (ad_item.put_type != 1) {
    return true;
  }
  if (ad_item.photo_list.empty()) {
    return false;
  }
  base::Json json(base::StringToJson(ad_item.photo_list));
  if (!json.IsArray()) {
    return false;
  }
  std::unordered_set<int64_t> photo_id_set;
  for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
    int64 photo_id = 0;
    if ((*iter)->IntValue(&photo_id)) {
      if (photo_id <= 0) {
        continue;
      }
      photo_id_set.insert(photo_id);
    }
  }
  if (photo_id_set.find(origin_photo_id) == photo_id_set.end()) {
    return false;
  }
  return true;
}

void StorewidePrerankSelectAndFillMixer::GetResultMap(
    ks::platform::AddibleRecoContextInterface* context) {
  auto* ad_table = context->GetOrInsertDataTable("ad_table");
  if (ad_table == nullptr) {
    return;
  }
  int64_t valid_cnt = 0;
  auto& item_list = ad_table->GetCommonRecoResults();
  std::for_each(item_list.begin(), item_list.end(), [&](const platform::CommonRecoResult& item) {
    auto is_valid = context->GetIntItemAttr(item, attr_ad_is_valid_).value_or(0);
    if (!is_valid) {
      return;
    }
    valid_cnt++;
    int64_t creative_id = context->GetIntItemAttr(item, attr_ad_creative_id_).value_or(0);
    int64_t unit_id = context->GetIntItemAttr(item, attr_ad_unit_id_).value_or(0);
    int64_t campaign_id = context->GetIntItemAttr(item, attr_ad_campaign_id_).value_or(0);
    int64_t account_id = context->GetIntItemAttr(item, attr_ad_account_id_).value_or(0);
    int64_t campaign_type = context->GetIntItemAttr(item, attr_ad_campaign_type_).value_or(0);
    int32_t scene_oriented_type = context->GetIntItemAttr(item, attr_ad_scene_oriented_type_).value_or(0);
    int64_t live_id = context->GetIntItemAttr(item, attr_ad_live_id_).value_or(0);
    int64_t author_id = context->GetIntItemAttr(item, attr_ad_author_id_).value_or(0);
    int64_t product_id = context->GetIntItemAttr(item, attr_ad_merchant_item_id_).value_or(0);
    FillAdAutoRoi(context, item, campaign_type, scene_oriented_type, campaign_id);
    if (enable_auto_roi_guard_) {
      AdTableGuardAutoRoi(context, item);
    }
    AdItem ad_item;
    ad_item.creative_id = context->GetIntItemAttr(item, attr_ad_creative_id_).value_or(0);
    ad_item.unit_id = context->GetIntItemAttr(item, attr_ad_unit_id_).value_or(0);
    ad_item.campaign_id = context->GetIntItemAttr(item, attr_ad_campaign_id_).value_or(0);
    ad_item.account_id = context->GetIntItemAttr(item, attr_ad_account_id_).value_or(0);
    ad_item.auto_roi = context->GetDoubleItemAttr(item, attr_ad_ad_roi_auto_roi_).value_or(0.0);
    ad_item.roi_ratio = context->GetDoubleItemAttr(item, attr_ad_roi_ratio_).value_or(0.0);
    ad_item.is_live = context->GetIntItemAttr(item, attr_ad_is_live_).value_or(0);
    ad_item.scene_oriented_type = context->GetIntItemAttr(item, attr_ad_scene_oriented_type_).value_or(0);
    ad_item.put_type = context->GetIntItemAttr(item, attr_ad_put_type_).value_or(0);
    ad_item.photo_list = std::string(context->GetStringItemAttr(item, attr_ad_photo_list_).value_or(""));
    if (product_id > 0 && author_id > 0 &&
        (campaign_type == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
         !enable_storewide_check_campaign_type_)) {
      std::string product_author_id = absl::StrCat(author_id, "_", product_id);
      if (author_product_map_.find(product_author_id) == author_product_map_.end()) {
        author_product_map_[product_author_id] = ad_item;
      }
    }

    if (live_id > 0 &&
        (campaign_type == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
          !enable_storewide_check_campaign_type_)) {
      // 关注页直播优先级大于关注页本地直播优先级
      if (context->GetRequestType() == "new_storewide_follow_outside_flow" ||
          context->GetRequestType() == "old_storewide_follow_outside_flow" ||
          context->GetRequestType() == "new_storewide_follow_inner_flow" ||
          context->GetRequestType() == "old_storewide_follow_inner_flow") {
        if (live_map_.find(live_id) == live_map_.end() ||
            (live_map_[live_id].scene_oriented_type == 30 && ad_item.scene_oriented_type == 21)) {
          live_map_[live_id] = ad_item;
        }
      } else {
        if (live_map_.find(live_id) == live_map_.end()) {
          live_map_[live_id] = ad_item;
        }
      }
    }
    ks::infra::PerfUtil::IntervalLogStash(1, "ad.storewide_server", "fill_res_debug",
        "ap_live_des", absl::StrCat(product_id > 0 && author_id > 0, "_", live_id > 0, "_", campaign_type),
        context->GetRequestType());
  });
  ks::infra::PerfUtil::IntervalLogStash(item_list.size(), "ad.storewide_server", "fill_res",
      "ad_cnt", "ad_table_size",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(valid_cnt, "ad.storewide_server", "fill_res",
      "ad_cnt", "ad_table_valid_cnt",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(author_product_map_.size(), "ad.storewide_server", "fill_res",
      "ad_cnt", "ap_size",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(live_map_.size(), "ad.storewide_server", "fill_res",
      "ad_cnt", "live_size",
      context->GetRequestType());
}

void StorewidePrerankSelectAndFillMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  attr_is_valid_ = context->GetOrInsertItemAttrFromTable("is_valid", item_table_name_);
  attr_ad_roi_item_type_ =
      context->GetOrInsertItemAttrFromTable("ad_roi_item_type", item_table_name_);
  attr_item_type_ = context->GetOrInsertItemAttrFromTable("item_type", item_table_name_);
  attr_ad_roi_trans_info_ = context->GetOrInsertItemAttrFromTable("ad_roi_trans_info", item_table_name_);
  attr_ad_roi_creative_id_ = context->GetOrInsertItemAttrFromTable("ad_roi_creative_id", item_table_name_);
  attr_ad_roi_auto_roi_ = context->GetOrInsertItemAttrFromTable("ad_roi_auto_roi", item_table_name_);
  attr_ad_roi_budget_status_ =
      context->GetOrInsertItemAttrFromTable("ad_roi_budget_status", item_table_name_);
  attr_ad_roi_live_id_ = context->GetOrInsertItemAttrFromTable("ad_roi_live_id", item_table_name_);
  attr_ad_roi_item_id_ = context->GetOrInsertItemAttrFromTable("ad_roi_item_id", item_table_name_);
  attr_visit_ad_bid_server_ = context->GetOrInsertItemAttrFromTable("visit_ad_bid_server", item_table_name_);
  attr_visit_bid_server_id_ = context->GetOrInsertItemAttrFromTable("visit_bid_server_id", item_table_name_);
  attr_live_id_ = context->GetOrInsertItemAttrFromTable("live_id", item_table_name_);
  attr_photo_id_ = context->GetOrInsertItemAttrFromTable("photo_id", item_table_name_);
  attr_is_merchant_cart_ = context->GetOrInsertItemAttrFromTable("is_merchant_cart", item_table_name_);
  attr_is_merchant_living_ = context->GetOrInsertItemAttrFromTable("is_merchant_living", item_table_name_);
  attr_scene_oriented_type_ = context->GetOrInsertItemAttrFromTable("scene_oriented_type", item_table_name_);
  attr_creative_id_ = context->GetOrInsertItemAttrFromTable("creative_id", item_table_name_);
  attr_unit_id_ = context->GetOrInsertItemAttrFromTable("unit_id", item_table_name_);
  attr_campaign_id_ = context->GetOrInsertItemAttrFromTable("campaign_id", item_table_name_);
  attr_account_id_ = context->GetOrInsertItemAttrFromTable("account_id", item_table_name_);
  attr_campaign_type_ = context->GetOrInsertItemAttrFromTable("campaign_type", item_table_name_);
  attr_roi_ratio_ = context->GetOrInsertItemAttrFromTable("roi_ratio", item_table_name_);
  attr_is_fill_bid_info_ = context->GetOrInsertItemAttrFromTable("is_fill_bid_info", item_table_name_);
  attr_author_id_ = context->GetOrInsertItemAttrFromTable("author_id", item_table_name_);
  attr_merchant_item_id_ = context->GetOrInsertItemAttrFromTable("merchant_item_id", item_table_name_);
  attr_page_achieve_info_key_ =
      context->GetOrInsertItemAttrFromTable("page_achieve_info_key", item_table_name_);
  attr_page_achieve_info_value_ =
      context->GetOrInsertItemAttrFromTable("page_achieve_info_value", item_table_name_);
  // ad table
  attr_ad_is_valid_ = context->GetOrInsertItemAttrFromTable("is_valid", "ad_table");
  attr_ad_ad_roi_item_type_ =
      context->GetOrInsertItemAttrFromTable("ad_roi_item_type", "ad_table");
  attr_ad_item_type_ = context->GetOrInsertItemAttrFromTable("item_type", "ad_table");
  attr_ad_ad_roi_trans_info_ = context->GetOrInsertItemAttrFromTable("ad_roi_trans_info", "ad_table");
  attr_ad_ad_roi_creative_id_ = context->GetOrInsertItemAttrFromTable("ad_roi_creative_id", "ad_table");
  attr_ad_ad_roi_auto_roi_ = context->GetOrInsertItemAttrFromTable("ad_roi_auto_roi", "ad_table");
  attr_ad_ad_roi_budget_status_ =
      context->GetOrInsertItemAttrFromTable("ad_roi_budget_status", "ad_table");
  attr_ad_ad_roi_live_id_ = context->GetOrInsertItemAttrFromTable("ad_roi_live_id", "ad_table");
  attr_ad_ad_roi_item_id_ = context->GetOrInsertItemAttrFromTable("ad_roi_item_id", "ad_table");
  attr_ad_live_id_ = context->GetOrInsertItemAttrFromTable("live_id", "ad_table");
  attr_ad_is_live_ = context->GetOrInsertItemAttrFromTable("is_live", "ad_table");
  attr_ad_scene_oriented_type_ = context->GetOrInsertItemAttrFromTable("scene_oriented_type", "ad_table");
  attr_ad_put_type_ = context->GetOrInsertItemAttrFromTable("put_type", "ad_table");
  attr_ad_photo_list_ = context->GetOrInsertItemAttrFromTable("photo_list", "ad_table");
  attr_ad_creative_id_ = context->GetOrInsertItemAttrFromTable("creative_id", "ad_table");
  attr_ad_unit_id_ = context->GetOrInsertItemAttrFromTable("unit_id", "ad_table");
  attr_ad_campaign_id_ = context->GetOrInsertItemAttrFromTable("campaign_id", "ad_table");
  attr_ad_account_id_ = context->GetOrInsertItemAttrFromTable("account_id", "ad_table");
  attr_ad_campaign_type_ = context->GetOrInsertItemAttrFromTable("campaign_type", "ad_table");
  attr_ad_roi_ratio_ = context->GetOrInsertItemAttrFromTable("roi_ratio", "ad_table");
  attr_ad_is_fill_bid_info_ = context->GetOrInsertItemAttrFromTable("is_fill_bid_info", "ad_table");
  attr_ad_author_id_ = context->GetOrInsertItemAttrFromTable("author_id", "ad_table");
  attr_ad_merchant_item_id_ = context->GetOrInsertItemAttrFromTable("merchant_item_id", "ad_table");
  attr_ad_live_stream_type_ = context->GetOrInsertItemAttrFromTable("live_stream_type", "ad_table");
  attr_ad_page_achieve_info_key_ =
      context->GetOrInsertItemAttrFromTable("page_achieve_info_key", "ad_table");
  attr_ad_page_achieve_info_value_ =
      context->GetOrInsertItemAttrFromTable("page_achieve_info_value", "ad_table");

  storewide_auto_roas_adjust_weight_ =
      GetDoubleProcessorParameter(context, storewide_auto_roas_adjust_weight_conf_, 1.0);
  storewide_auto_roas_adjust_weight_merchant_ =
      GetDoubleProcessorParameter(context, storewide_auto_roas_adjust_weight_merchant_conf_, 1.0);
  storewide_auto_roas_adjust_weight_follow_ =
      GetDoubleProcessorParameter(context, storewide_auto_roas_adjust_weight_follow_conf_, 1.0);
  photo_storewide_roas_adjust_weight_ =
      GetDoubleProcessorParameter(context, photo_storewide_roas_adjust_weight_conf_, 1.0);
  photo_storewide_roas_adjust_weight_merchant_ =
      GetDoubleProcessorParameter(context, photo_storewide_roas_adjust_weight_merchant_conf_, 1.0);
  photo_storewide_roas_adjust_weight_follow_ =
      GetDoubleProcessorParameter(context, photo_storewide_roas_adjust_weight_follow_conf_, 1.0);
  enable_merchant_storewide_reco_page_cali_ =
      GetBoolProcessorParameter(context, enable_merchant_storewide_reco_page_cali_conf_, false);
  enable_merchant_storewide_reco_follow_cali_ =
      GetBoolProcessorParameter(context, enable_merchant_storewide_reco_follow_cali_conf_, false);
  enable_merchant_storewide_reco_nebula_follow_cali_ =
      GetBoolProcessorParameter(context, enable_merchant_storewide_reco_nebula_follow_cali_conf_, false);
  enable_merchant_storewide_reco_follow_inner_cali_ =
      GetBoolProcessorParameter(context, enable_merchant_storewide_reco_follow_inner_cali_conf_, false);
  enable_roi_ratio_pk_ =
      GetBoolProcessorParameter(context, enable_roi_ratio_pk_conf_, false);
  enable_merchant_living_for_agent_ =
      GetBoolProcessorParameter(context, enable_merchant_living_for_agent_conf_, false);
  enable_storewide_check_campaign_type_ =
      GetBoolProcessorParameter(context, enable_storewide_check_campaign_type_conf_, false);
  enable_mock_reco_bidding_ =
      GetBoolProcessorParameter(context, enable_mock_reco_bidding_conf_, false);

  if (context->GetRequestType() == "new_storewide_follow_outside_flow" ||
      context->GetRequestType() == "old_storewide_follow_outside_flow" ||
      context->GetRequestType() == "new_storewide_follow_inner_flow" ||
      context->GetRequestType() == "old_storewide_follow_inner_flow") {
    enable_storewide_check_campaign_type_ = true;
  }

  enable_auto_roi_guard_ = AdKconfUtil::enableMerchantRoiGuard() &&
      AdKconfUtil::lanuchWithIndexLoading();
  int64_t res_cnt = 0;
  int64_t product_res_cnt = 0;
  int64_t live_res_cnt = 0;
  int64_t living_cnt = 0;
  int64_t not_living_cnt = 0;
  int64_t not_living_but_ad_living_cnt = 0;
  int64_t living_but_not_ad_living_cnt = 0;
  int64_t auto_roi_too_small_cnt = 0;
  int64_t is_live_postive_cnt = 0;
  int64_t is_live_negative_cnt = 0;
  int64_t is_live_zero_cnt = 0;
  live_map_.clear();
  author_product_map_.clear();
  GetResultMap(context);
  auto* item_table = context->GetOrInsertDataTable(item_table_name_);
  if (item_table == nullptr) {
    return;
  }
  auto& item_list = item_table->GetCommonRecoResults();
  std::for_each(item_list.begin(), item_list.end(), [&](const platform::CommonRecoResult& item) {
    int64_t item_type = 0;
    int64_t product_id = context->GetIntItemAttr(item, attr_merchant_item_id_).value_or(0);
    int64_t author_id = context->GetIntItemAttr(item, attr_author_id_).value_or(0);
    int64_t live_id = context->GetIntItemAttr(item, attr_live_id_).value_or(0);
    int64_t photo_id = context->GetIntItemAttr(item, attr_photo_id_).value_or(0);
    // 全站直播状态，部分场景未传设置为 1 不生效
    int64_t is_merchant_living = context->GetIntItemAttr(item, attr_is_merchant_living_).value_or(1);
    AdItem* p_ad_item = nullptr;
    if (author_id > 0 && product_id > 0) {
      std::string product_author_id = absl::StrCat(author_id, "_", product_id);
      auto ap_iter = author_product_map_.find(product_author_id);
      if (ap_iter != author_product_map_.end()) {
        p_ad_item = &(ap_iter->second);
        item_type = 2;
      }
    }
    if (live_id > 0) {
      if (is_merchant_living || (!enable_merchant_living_for_agent_)) {
        auto live_iter = live_map_.find(live_id);
        if (live_iter != live_map_.end() && (p_ad_item == nullptr ||
            p_ad_item->roi_ratio >= live_iter->second.roi_ratio) &&
            CheckDaitouValid(photo_id, live_iter->second)) {
          p_ad_item = &(live_iter->second);
          item_type = 1;
          if (live_iter->second.is_live <= 0) {
            living_but_not_ad_living_cnt++;
          }
        }

        if (live_iter != live_map_.end()) {
          if (live_iter->second.is_live < 0) {
            is_live_negative_cnt++;
          } else if (live_iter->second.is_live == 0) {
            is_live_zero_cnt++;
          } else {
            is_live_postive_cnt++;
          }
        }
        living_cnt++;
      } else {
        not_living_cnt++;
        auto live_iter_debug = live_map_.find(live_id);
        if (live_iter_debug != live_map_.end() && live_iter_debug->second.is_live > 0) {
          LOG_EVERY_N(INFO, 100) << "live is open in ad but close in merchant, liveid, " << live_id;
          not_living_but_ad_living_cnt++;
          is_live_postive_cnt++;
        } else if (live_iter_debug != live_map_.end() && live_iter_debug->second.is_live < 0) {
          is_live_negative_cnt++;
        } else if (live_iter_debug != live_map_.end() && live_iter_debug->second.is_live == 0) {
          is_live_zero_cnt++;
        }
      }
    }
    if (p_ad_item == nullptr) {
      return;
    }
    if (p_ad_item->auto_roi <= 0.001) {
      auto_roi_too_small_cnt++;
      return;
    }
    if (item_type == 2) {
      context->SetIntItemAttr(item, attr_ad_roi_item_type_, 2);
      context->SetIntItemAttr(item, attr_ad_roi_item_id_, product_id);
      context->SetIntItemAttr(item, attr_visit_bid_server_id_, product_id);
      product_res_cnt++;
    } else if (item_type == 1) {
      context->SetIntItemAttr(item, attr_ad_roi_item_type_, 1);
      context->SetIntItemAttr(item, attr_ad_roi_live_id_, live_id);
      context->SetIntItemAttr(item, attr_ad_roi_item_id_, live_id);
      context->SetIntItemAttr(item, attr_visit_bid_server_id_, live_id);
      live_res_cnt++;
    }
    res_cnt++;
    context->SetIntItemAttr(item, attr_ad_roi_creative_id_, p_ad_item->creative_id);
    std::string trans_info_string = "{";
    trans_info_string += "\"creative_id\":" + absl::StrCat(p_ad_item->creative_id) + ",";
    trans_info_string += "\"unit_id\":" + absl::StrCat(p_ad_item->unit_id) + ",";
    trans_info_string += "\"campaign_id\":" + absl::StrCat(p_ad_item->campaign_id) + ",";
    trans_info_string += "\"account_id\":" + absl::StrCat(p_ad_item->account_id);
    trans_info_string += "}";
    context->SetStringItemAttr(item, attr_ad_roi_trans_info_, trans_info_string);
    context->SetDoubleItemAttr(item, attr_ad_roi_auto_roi_, p_ad_item->auto_roi);
    context->SetDoubleItemAttr(item, attr_roi_ratio_, p_ad_item->roi_ratio);
    context->SetIntItemAttr(item, attr_scene_oriented_type_, p_ad_item->scene_oriented_type);
    context->SetIntItemAttr(item, attr_ad_roi_budget_status_, 1);
    context->SetIntItemAttr(item, attr_visit_ad_bid_server_, 1);
  });
  ks::infra::PerfUtil::IntervalLogStash(res_cnt, "ad.storewide_server", "fill_res",
      "res_cnt", "total",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(live_res_cnt, "ad.storewide_server", "fill_res",
      "res_cnt", "live_cnt",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(product_res_cnt, "ad.storewide_server", "fill_res",
      "res_cnt", "product_cnt",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(living_cnt, "ad.storewide_server", "fill_res",
      "res_cnt", "living_cnt",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(not_living_cnt, "ad.storewide_server", "fill_res",
      "res_cnt", "not_living_cnt",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(not_living_but_ad_living_cnt, "ad.storewide_server", "fill_res",
      "res_cnt", "not_living_but_ad_living_cnt",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(living_but_not_ad_living_cnt, "ad.storewide_server", "fill_res",
      "res_cnt", "living_but_not_ad_living_cnt",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(auto_roi_too_small_cnt, "ad.storewide_server", "fill_res",
      "res_cnt", "auto_roi_too_small_cnt",
      context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(is_live_postive_cnt, "ad.storewide_server", "fill_res",
      "live_num", "postive", context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(is_live_negative_cnt, "ad.storewide_server", "fill_res",
      "live_num", "negative", context->GetRequestType());
  ks::infra::PerfUtil::IntervalLogStash(is_live_zero_cnt, "ad.storewide_server", "fill_res",
      "live_num", "zero", context->GetRequestType());
  return;
}

void StorewidePrerankSelectAndFillMixer::AdTableGuardAutoRoi(
    ks::platform::AddibleRecoContextInterface* context,
    const ks::platform::CommonRecoResult &item) {
  double auto_roi = item.GetDoubleAttr(attr_ad_ad_roi_auto_roi_).value_or(0.0);
  if (auto_roi < 1e-6) {
    auto roi_ratio = context->GetDoubleItemAttr(item, attr_ad_roi_ratio_).value_or(0.0);
    if (roi_ratio > 0.0) {
      item.SetDoubleAttr(attr_ad_ad_roi_auto_roi_, roi_ratio);
      context->SetIntItemAttr(item, attr_ad_is_fill_bid_info_, 1);
    }
  }
}

void StorewidePrerankSelectAndFillMixer::FillAdAutoRoi(ks::platform::AddibleRecoContextInterface* context,
      const ks::platform::CommonRecoResult &item,
      int64_t campaign_type, int32_t scene_oriented_type, int64_t campaign_id) {
  const auto& storewide_nature_key = AdKconfUtil::storewideNatureDict();
  const auto& storewide_nature_cali_tail = AdKconfUtil::liveStorewideNatureBidCaliCamTail();
  const auto& storewide_mock_reco_cali_ratio = AdKconfUtil::storewideMockRecoCaliRatio();
  double auto_roas = context->GetDoubleItemAttr(item, attr_ad_ad_roi_auto_roi_).value_or(0.0);
  int32_t roi_traffic_source = context->GetIntCommonAttr("roi_traffic_source").value_or(0);
  int32_t live_type = context->GetIntItemAttr(item, attr_ad_live_stream_type_).value_or(-1);
  ks::infra::PerfUtil::IntervalLogStash(auto_roas * 1e4, "ad.storewide_server", "fill_res",
      "roi_traffic_source_v2",
      absl::StrCat(roi_traffic_source),
      context->GetRequestType());
  if (context->GetIntItemAttr(item, attr_ad_is_fill_bid_info_).value_or(0) > 0) {
    return;
  }
  int64_t author_id = item.GetIntAttr(attr_ad_author_id_).value_or(0);

  // 全互斥实验乘系数
  double auto_roas_adjust = storewide_auto_roas_adjust_weight_;
  if (context->GetRequestType() == "storewide_prerank_flow" ||
      context->GetRequestType() == "storewide_1pp_flow" ||
      context->GetRequestType() == "shelf_storewide_goods_flow" ||
      context->GetRequestType() == "shelf_storewide_live_flow" ||
      context->GetRequestType() == "storewide_search_photo_flow" ||
      context->GetRequestType() == "storewide_search_live_flow" ||
      context->GetRequestType() == "storewide_search_goods_flow" ||
      context->GetRequestType() == "live_inner_flow" ||
      context->GetRequestType() == "local_life_storewide_flow" ||
      context->GetRequestType() == "degrade_local_life_storewide_flow") {
    auto_roas_adjust = storewide_auto_roas_adjust_weight_merchant_;
  } else if (context->GetRequestType() == "new_storewide_follow_outside_flow" ||
          context->GetRequestType() == "old_storewide_follow_outside_flow" ||
          context->GetRequestType() == "new_storewide_follow_inner_flow" ||
          context->GetRequestType() == "old_storewide_follow_inner_flow") {
    auto_roas_adjust = storewide_auto_roas_adjust_weight_follow_;
  }
  // 商品全站调整系数
  if (campaign_type == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
    auto_roas_adjust = photo_storewide_roas_adjust_weight_;
    if (context->GetRequestType() == "storewide_prerank_flow" ||
        context->GetRequestType() == "storewide_1pp_flow" ||
        context->GetRequestType() == "shelf_storewide_goods_flow" ||
        context->GetRequestType() == "shelf_storewide_live_flow" ||
        context->GetRequestType() == "storewide_search_photo_flow" ||
        context->GetRequestType() == "storewide_search_live_flow" ||
        context->GetRequestType() == "storewide_search_goods_flow" ||
        context->GetRequestType() == "live_inner_flow" ||
        context->GetRequestType() == "local_life_storewide_flow" ||
        context->GetRequestType() == "degrade_local_life_storewide_flow") {
      auto_roas_adjust = photo_storewide_roas_adjust_weight_merchant_;
    } else if (context->GetRequestType() == "new_storewide_follow_outside_flow" ||
        context->GetRequestType() == "old_storewide_follow_outside_flow" ||
        context->GetRequestType() == "new_storewide_follow_inner_flow" ||
        context->GetRequestType() == "old_storewide_follow_inner_flow") {
      auto_roas_adjust = photo_storewide_roas_adjust_weight_follow_;
    }
  }
  // 直播全站非商业化流量出价校准
  if (campaign_type == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE &&
      storewide_nature_key && storewide_nature_cali_tail) {
    double cali_ratio = 1.0;
    double lower_bound = 0.99;
    double upper_bound = 1.01;
    auto traffic_key = absl::Substitute("$0", roi_traffic_source);
    auto iter = storewide_nature_key->find(traffic_key);
    if (iter != storewide_nature_key->end()) {
      auto roas_traffic_key = iter->second;
      auto author_mod_key = 100;
      auto author_mod_iter = storewide_nature_cali_tail->find("author_mod_key");
      if (author_mod_iter != storewide_nature_cali_tail->end()) {
        author_mod_key = author_mod_iter->second;
      }
      auto campaign_mod_key = 100;
      auto campaign_mod_iter = storewide_nature_cali_tail->find("campaign_mod_key");
      if (campaign_mod_iter != storewide_nature_cali_tail->end()) {
        campaign_mod_key = campaign_mod_iter->second;
      }
      auto author_tail = author_id % author_mod_key;
      auto author_key = absl::Substitute("author_$0_$1", roas_traffic_key, author_tail);
      auto author_iter = storewide_nature_cali_tail->find(author_key);
      if (author_iter != storewide_nature_cali_tail->end()) {
        auto storewide_cali_key = absl::Substitute("$0_storewide", roas_traffic_key);
        GetPageCaliBidInfo(context, item, storewide_cali_key, &cali_ratio);
      }
      auto campaign_tail = campaign_id % campaign_mod_key;
      auto campaign_key = absl::Substitute("campaign_$0_$1", roas_traffic_key, campaign_tail);
      auto campaign_iter = storewide_nature_cali_tail->find(campaign_key);
      if (campaign_iter != storewide_nature_cali_tail->end()) {
        auto storewide_cali_key = absl::Substitute("$0_storewide", roas_traffic_key);
        GetPageCaliBidInfo(context, item, storewide_cali_key, &cali_ratio);
      }
      if (enable_merchant_storewide_reco_follow_cali_ && roas_traffic_key == "FOLLOW_TRAFFIC") {
        auto storewide_cali_key = absl::Substitute("$0_storewide", roas_traffic_key);
        GetPageCaliBidInfo(context, item, storewide_cali_key, &cali_ratio);
      }
      if (enable_merchant_storewide_reco_nebula_follow_cali_ && roas_traffic_key == "NEBULA_FOLLOW_TRAFFIC") {
        auto storewide_cali_key = absl::Substitute("$0_storewide", roas_traffic_key);
        GetPageCaliBidInfo(context, item, storewide_cali_key, &cali_ratio);
      }
      if (enable_merchant_storewide_reco_follow_inner_cali_ && roas_traffic_key == "FOLLOW_INNER_TRAFFIC") {
        auto storewide_cali_key = absl::Substitute("$0_storewide", roas_traffic_key);
        GetPageCaliBidInfo(context, item, storewide_cali_key, &cali_ratio);
      }
      auto upper_key = absl::Substitute("$0_upper_bound", roas_traffic_key);
      auto lower_key = absl::Substitute("$0_lower_bound", roas_traffic_key);
      auto upper_iter = storewide_nature_cali_tail->find(upper_key);
      if (upper_iter != storewide_nature_cali_tail->end()) {
        upper_bound = upper_iter->second;
      }
      auto lower_iter = storewide_nature_cali_tail->find(lower_key);
      if (lower_iter != storewide_nature_cali_tail->end()) {
        lower_bound = lower_iter->second;
      }
      cali_ratio = std::max(cali_ratio, lower_bound);
      cali_ratio = std::min(cali_ratio, upper_bound);
    }

    if (enable_mock_reco_bidding_ && storewide_mock_reco_cali_ratio) {
      double mock_cali_ratio = 1.0;
      std::string cali_ratio_key = absl::Substitute("$0_live_type_$1", roi_traffic_source, live_type);
      auto cali_ratio_iter = storewide_mock_reco_cali_ratio->find(cali_ratio_key);
      if (cali_ratio_iter != storewide_mock_reco_cali_ratio->end()) {
        mock_cali_ratio = cali_ratio_iter->second;
      }
      ks::infra::PerfUtil::IntervalLogStash(mock_cali_ratio * 1e4,
        "ad.storewide_server", "fill_res", "mock_cali_ratio",
        absl::StrCat(roi_traffic_source),
        context->GetRequestType());
      cali_ratio *= mock_cali_ratio;
    }
    auto_roas_adjust *= cali_ratio;
  }
  // 商品全站非商业化流量出价校准
  double page_ratio = 1.0;
  std::string storewide_cali_key = "storewide_nature";
  if (campaign_type == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      enable_merchant_storewide_reco_page_cali_) {
    GetPageCaliBidInfo(context, item, storewide_cali_key, &page_ratio);
    ks::infra::PerfUtil::IntervalLogStash(page_ratio * 1e4,
        "ad.storewide_server", "fill_res", "roi_traffic_source_page_ratio",
        absl::StrCat(roi_traffic_source),
        context->GetRequestType());
    auto_roas_adjust *= page_ratio;
  }
  // 本地推全站
  if (scene_oriented_type == 30) {
    auto_roas_adjust = 1.0;
  }
  if (auto_roas_adjust != 1.0) {
    if (campaign_type == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
      item.SetDoubleAttr(attr_ad_ad_roi_auto_roi_, auto_roas * auto_roas_adjust);
      ks::infra::PerfUtil::IntervalLogStash(auto_roas_adjust * 1e4, "ad.storewide_server", "fill_res",
                               "store_wide_roi.adjust_auto_roi_v2",
                               context->GetRequestType());
    }
    if (campaign_type == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
      item.SetDoubleAttr(attr_ad_ad_roi_auto_roi_, auto_roas * auto_roas_adjust);
      ks::infra::PerfUtil::IntervalLogStash(auto_roas_adjust * 1e4, "ad.storewide_server", "fill_res",
                               "store_wide_photo_roi.adjust_auto_roi_v2",
                               context->GetRequestType());
    }
  }
  context->SetIntItemAttr(item, attr_ad_is_fill_bid_info_, 1);
  ks::infra::PerfUtil::IntervalLogStash(item.GetDoubleAttr(attr_ad_ad_roi_auto_roi_).value_or(0.0) * 1e4,
                              "ad.storewide_server", "fill_res", "roi_traffic_source_end_v2",
                              absl::StrCat(roi_traffic_source),
                              context->GetRequestType());
}

bool StorewidePrerankSelectAndFillMixer::GetPageCaliBidInfo(
    ks::platform::AddibleRecoContextInterface* context,
    const ks::platform::CommonRecoResult &item,
    const std::string& key, double* value) {
  auto key_list = context->GetStringListItemAttr(item, attr_ad_page_achieve_info_key_)
                      .value_or(std::vector<absl::string_view>());
  auto val_list = context->GetDoubleListItemAttr(item, attr_ad_page_achieve_info_value_)
                      .value_or(absl::Span<const double>());
  if (key_list.empty() || val_list.empty()) {
    return false;
  }
  if (key_list.size() != val_list.size()) {
    return false;
  }
  for (auto i = 0; i < key_list.size(); ++i) {
    if (key_list[i] == key) {
      *value = val_list[i];
      return true;
    }
  }
  return false;
}

using JsonFactoryClass = base::JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, StorewidePrerankSelectAndFillMixer, StorewidePrerankSelectAndFillMixer);

}  // namespace ad_storewide
}  // namespace ks
