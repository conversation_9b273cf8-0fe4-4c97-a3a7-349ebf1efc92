#pragma once

#include <string>
#include <vector>
#include <memory>
#include <utility>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

#include "gflags/gflags.h"
#include "perfutil/perfutil.h"
#include "google/protobuf/arena.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"

#ifdef UNIVERSE_RANK_FLAGS
namespace ks::ad_base {
  DECLARE_string(perf_name_space);
}  // namespace ks::ad_base
using ks::ad_base::FLAGS_perf_name_space;
#else
DECLARE_string(perf_name_space);
#endif

namespace ks {
namespace platform {

class TransformCmdTableEnricher : public CommonRecoBaseEnricher {
 public:
  TransformCmdTableEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 public:
  bool InitProcessor() override {
    transform_protobuf_attr_ = config()->GetString("transform_protobuf_attr");
    if (transform_protobuf_attr_.empty()) {
      LOG(ERROR) << "TransformCmdTableEnricher init failed!"
        << "field `transform_protobuf_attr` should not be empty.";
      return false;
    }
    cmd_table_ = config()->GetString("cmd_table");
    if (cmd_table_.empty()) {
      LOG(ERROR) << "TransformCmdTableEnricher init failed!"
        << "field `cmd_table` should not be empty.";
      return false;
    }
    need_item_set_ = config()->GetBoolean("need_item_set", false);
    if (need_item_set_) {
      item_id_set_attr_ = config()->GetString("item_id_set", "item_id_set");
    }
    return true;
  }

 private:
  bool need_item_set_ = false;
  std::string item_id_set_attr_{};
  std::string cmd_table_{};
  std::string transform_protobuf_attr_{};

 private:
  DISALLOW_COPY_AND_ASSIGN(TransformCmdTableEnricher);
};

}  // namespace platform
}  // namespace ks
