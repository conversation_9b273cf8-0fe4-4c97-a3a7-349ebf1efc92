#include "teams/ad/dragon_exts/src/ad_model/predict_prepare/cmd_table/transform_cmd_table_enricher.h"

namespace ks {
namespace platform {

using ::google::protobuf::Descriptor;
using ::google::protobuf::Reflection;
using ::google::protobuf::FieldDescriptor;
using kuaishou::ad::algorithm::CmdType;
using kuaishou::ad::algorithm::RequestScene;
using kuaishou::ad::algorithm::UniversePredictRequest;

void TransformCmdTableEnricher::Enrich(MutableRecoContextInterface *context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  auto* request_pb = const_cast<UniversePredictRequest *>(
    context->GetProtoMessagePtrCommonAttr<UniversePredictRequest>(transform_protobuf_attr_));
  if (!request_pb) {
    LOG_EVERY_N(ERROR, 1000) << "TransformCmdTableEnricher input universe_predict_request is nullptr!"
      << "predict_msg_key: " << transform_protobuf_attr_;
    ks::infra::PerfUtil::CountLogStash(1, FLAGS_perf_name_space, "TransformCmdTableEnricher",
      "intput_pb_attr_nullptr");
    return;
  }
  auto cmd_table = context->GetOrInsertDataTable(cmd_table_);
  auto* cmd_attr = cmd_table->GetOrInsertAttr("cmd");
  auto* cmd_key_attr = cmd_table->GetOrInsertAttr("cmd_key");
  auto* cmd_type_attr = cmd_table->GetOrInsertAttr("cmd_type");
  auto* item_ids_attr = cmd_table->GetOrInsertAttr("item_ids_list");
  auto* cmd_key_scene_attr = cmd_table->GetOrInsertAttr("cmd_key_scene");
  auto* cmd_value_num_attr = cmd_table->GetOrInsertAttr("cmd_value_num");
  const std::vector<CommonRecoResult> &cmd_results = cmd_table->GetCommonRecoResults();
  absl::flat_hash_set<int64_t> item_id_set;
  for (const auto& result : cmd_results) {
    std::string cmd = std::string(context->GetStringItemAttr(result, cmd_attr).value_or(""));
    std::string cmd_key = std::string(context->GetStringItemAttr(result, cmd_key_attr).value_or(""));
    int64_t cmd_type = context->GetIntItemAttr(result, cmd_type_attr).value_or(0);
    auto item_ids_list = context->GetIntListItemAttr(result, item_ids_attr);
    int64_t cmd_value_num = context->GetIntItemAttr(result, cmd_value_num_attr).value_or(0);
    int64_t cmd_key_scene = context->GetIntItemAttr(result, cmd_key_scene_attr).value_or(0);
    request_pb->add_cmd(cmd);
    auto *cmd_item_ptr = request_pb->add_cmd_item_mapping();
    for (const auto& item_id : *item_ids_list) {
      if (need_item_set_) { item_id_set.insert(item_id); }
      cmd_item_ptr->add_item_id(item_id);
    }
    cmd_item_ptr->add_cmd(cmd);
    cmd_item_ptr->add_cmdkey(cmd_key);
    cmd_item_ptr->add_cmd_value_num(cmd_value_num);
    cmd_item_ptr->add_cmd_type(static_cast<CmdType>(cmd_type));
    cmd_item_ptr->add_cmdkey_scene(static_cast<RequestScene>(cmd_key_scene));
    ks::infra::PerfUtil::CountLogStash(1, FLAGS_perf_name_space,
      "TransformCmdTableEnricher", "transform_register_cmd_key_count", cmd_key);
    ks::infra::PerfUtil::IntervalLogStash(item_ids_list->size(), FLAGS_perf_name_space,
      "TransformCmdTableEnricher", "transform_cmd_item_list_size", cmd_key);
  }
  if (need_item_set_) {
    std::vector<int64_t> item_id_list {item_id_set.begin(), item_id_set.end()};
    context->SetIntListCommonAttr(item_id_set_attr_, std::move(item_id_list));
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, TransformCmdTableEnricher, TransformCmdTableEnricher);

}  // namespace platform
}  // namespace ks
