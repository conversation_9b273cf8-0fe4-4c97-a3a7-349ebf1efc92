#include "teams/ad/dragon_exts/src/ad_model/predict_prepare/feature_table/item_feature_table_enricher.h"

#include <memory>
#include <vector>

namespace ks {
namespace platform {

void ItemFeatureTableEnricher::Enrich(MutableRecoContextInterface *context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  auto* input_table = context->GetOrInsertDataTable(GetTableName());
  auto* output_table = context->GetOrInsertDataTable(output_table_);
  auto* ocpx_action_type_attr = input_table->GetOrInsertAttr(callback_event_.ocpx_action_type);
  auto* deep_conversion_type_attr = input_table->GetOrInsertAttr(callback_event_.deep_conversion_type);
  auto* output_table_key_attr = input_table->GetOrInsertAttr(output_item_key_);
  absl::flat_hash_set<uint64_t> item_id_set;
  std::for_each(begin, end, [&](const CommonRecoResult& result) {
    uint64_t output_table_item_key = static_cast<uint64_t>(
      context->GetIntItemAttr(result, output_table_key_attr).value_or(0));
    if (item_id_set.count(output_table_item_key) > 0) {
      LOG_EVERY_N(WARNING, 100000) << "duplicated output table item key.item_table key: "
        << result.item_key << ", duplicate key: " << output_table_item_key;
      return;
    }
    item_id_set.insert(output_table_item_key);
    auto& item_feature = output_table->AddCommonRecoResult(result.item_key, 0, 0, 0);
    // step1. 处理 callback_event.
    if (callback_event_.need_add_event) {
      auto ocpx_action_type = context->GetIntItemAttr(result, ocpx_action_type_attr).value_or(0);
      auto deep_conversion_type = context->GetIntItemAttr(result, deep_conversion_type_attr).value_or(0);
      auto* callback_event_attr = output_table->GetOrInsertAttr("callback_event");
      item_feature.SetIntAttr(callback_event_attr,
        GetCallbackLogEvent(ocpx_action_type, deep_conversion_type));
    }
    // step2. 处理 item_id_info.
    if (item_info_list_.size() > 0) {
      std::shared_ptr<kuaishou::ad::algorithm::ItemComponentInfo> item_info_pb =
        std::make_shared<kuaishou::ad::algorithm::ItemComponentInfo>();
      auto* des = item_info_pb->GetDescriptor();
      auto* ref = item_info_pb->GetReflection();
      for (const auto& item_info : item_info_list_) {
        auto* source_field_attr = input_table->GetOrInsertAttr(item_info.source_field);
        const auto* f_des = des->FindFieldByName(item_info.dest_field);
        if (f_des == nullptr) {
          LOG_EVERY_N(INFO, 10000) << "msg not find name" << ", name:" << item_info.dest_field;
          ks::infra::PerfUtil::CountLogStash(1, FLAGS_perf_name_space,
            "ItemFeatureTableEnricher", "item_feature_pb_reflect_error", item_info.dest_field);
          continue;
        }
        switch (f_des->cpp_type()) {
          case FieldDescriptor::CPPTYPE_INT32: {
            auto value = context->GetIntItemAttr(result, source_field_attr).value_or(0);
            ref->SetInt32(&(*item_info_pb), f_des, value);
            break;
          }
          case FieldDescriptor::CPPTYPE_INT64: {
            auto value = context->GetIntItemAttr(result, source_field_attr).value_or(0);
            ref->SetInt64(&(*item_info_pb), f_des, value);
            break;
          }
          case FieldDescriptor::CPPTYPE_UINT32: {
            auto value = context->GetIntItemAttr(result, source_field_attr).value_or(0);
            ref->SetUInt32(&(*item_info_pb), f_des, value);
            break;
          }
          case FieldDescriptor::CPPTYPE_UINT64: {
            auto value = context->GetIntItemAttr(result, source_field_attr).value_or(0);
            ref->SetUInt64(&(*item_info_pb), f_des, value);
            break;
          }
          case FieldDescriptor::CPPTYPE_BOOL: {
            auto value = context->GetIntItemAttr(result, source_field_attr).value_or(0);
            ref->SetBool(&(*item_info_pb), f_des, value);
            break;
          }
          case FieldDescriptor::CPPTYPE_ENUM: {
            auto value = context->GetIntItemAttr(result, source_field_attr).value_or(0);
            ref->SetEnumValue(&(*item_info_pb), f_des, value);
            break;
          }
          case FieldDescriptor::CPPTYPE_DOUBLE: {
            auto value = context->GetDoubleItemAttr(result, source_field_attr).value_or(0.0);
            ref->SetDouble(&(*item_info_pb), f_des, value);
            break;
          }
          case FieldDescriptor::CPPTYPE_FLOAT: {
            auto value = context->GetDoubleItemAttr(result, source_field_attr).value_or(0.0);
            ref->SetFloat(&(*item_info_pb), f_des, value);
            break;
          }
          case FieldDescriptor::CPPTYPE_STRING: {
            auto value = context->GetStringItemAttr(result, source_field_attr).value_or("");
            ref->SetString(&(*item_info_pb), f_des, std::string(value));
            break;
          }
          default: {
            LOG_EVERY_N(WARNING, 1000) << "unsupported pb_type. field: " << item_info.dest_field;
            break;
          }
        }
      }
      auto* item_info_attr = output_table->GetOrInsertAttr("item_id_info");
      item_feature.SetExtraAttr(item_info_attr, item_info_pb);
    }
  });
  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass,
    ItemFeatureTableEnricher, ItemFeatureTableEnricher);

}  // namespace platform
}  // namespace ks
