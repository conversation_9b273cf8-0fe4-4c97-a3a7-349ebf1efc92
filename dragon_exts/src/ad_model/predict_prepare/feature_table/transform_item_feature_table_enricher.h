#pragma once

#include <string>
#include "absl/container/flat_hash_map.h"

#include "dragon/src/processor/base/common_reco_base_enricher.h"

#include "google/protobuf/message.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.grpc.pb.h"

#include "gflags/gflags.h"
#include "perfutil/perfutil.h"

#ifdef UNIVERSE_RANK_FLAGS
namespace ks::ad_base {
  DECLARE_string(perf_name_space);
}  // namespace ks::ad_base
using ks::ad_base::FLAGS_perf_name_space;
#else
DECLARE_string(perf_name_space);
#endif

namespace ks {
namespace platform {

using ::google::protobuf::Descriptor;
using ::google::protobuf::Reflection;
using ::google::protobuf::FieldDescriptor;
using kuaishou::ad::algorithm::Context;
using kuaishou::ad::algorithm::RequestScene;
using kuaishou::ad::algorithm::UniversePredictRequest;

class TransformItemFeatureTableEnricher : public CommonRecoBaseEnricher {
 public:
  TransformItemFeatureTableEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 public:
  bool InitProcessor() override {
    transform_protobuf_attr_ = config()->GetString("transform_protobuf_attr", "transform_protobuf_attr");
    item_feature_table_ = config()->GetString("item_feature_table", "item_feature_table");
    need_item_set_ = config()->GetBoolean("need_item_set", false);
    if (need_item_set_) {
      item_id_set_attr_ = config()->GetString("item_id_set", "item_id_set");
    }
    return true;
  }

 private:
  bool need_item_set_ = false;
  std::string item_id_set_attr_{""};
  std::string transform_protobuf_attr_{""};
  std::string item_feature_table_{""};

  DISALLOW_COPY_AND_ASSIGN(TransformItemFeatureTableEnricher);
};

}  // namespace platform
}  // namespace ks
