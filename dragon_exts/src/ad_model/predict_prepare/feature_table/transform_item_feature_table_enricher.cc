#include "teams/ad/dragon_exts/src/ad_model/predict_prepare/feature_table/transform_item_feature_table_enricher.h"

#include <memory>
#include <vector>

namespace ks {
namespace platform {

void TransformItemFeatureTableEnricher::Enrich(MutableRecoContextInterface *context,
  RecoResultConstIter begin, RecoResultConstIter end) {
  auto* request_pb = const_cast<UniversePredictRequest *>(
    context->GetProtoMessagePtrCommonAttr<UniversePredictRequest>(transform_protobuf_attr_));
  if (!request_pb) {
    LOG_EVERY_N(ERROR, 1000) << "TransformItemFeatureTableEnricher input universe_predict_request is nullptr!"
      << "predict_msg_key: " << transform_protobuf_attr_;
    return;
  }
  auto item_feature_table = context->GetOrInsertDataTable(item_feature_table_);
  std::vector<CommonRecoResult>* common_reco_result = item_feature_table->GetRecoResults();
  if (common_reco_result->empty()) {
    LOG_EVERY_N(WARNING, 10000) << "get item feature table failed: " << item_feature_table_;
    ks::infra::PerfUtil::CountLogStash(1, FLAGS_perf_name_space,
      "TransformItemFeatureTableEnricher", "intput_pb_attr_nullptr");
    return;
  }
  auto item_id_filter_list = context->GetIntListCommonAttr(item_id_set_attr_);
  absl::flat_hash_set<int64_t> item_id_set;
  if (item_id_filter_list) {
    for (const auto& item_id : *item_id_filter_list) {
      item_id_set.insert(item_id);
    }
  }
  const auto& attrs = item_feature_table->GetAllItemAttrs();
  std::for_each(common_reco_result->begin(), common_reco_result->end(),
    [&](const platform::CommonRecoResult& item) {
    if (need_item_set_ && item_id_set.count(item.item_key) <= 0) { return; }
    auto* item_context = request_pb->add_item_context();
    item_context->set_item_id(item.item_key);
    for (const auto* attr : attrs) {
      uint64_t feature_length = 0;
      if (attr->name() == "_REASON_") {
        continue;
      } else if (attr->name() == "callback_event") {
        if (auto int_val = attr->GetIntValue(item.GetAttrIndex())) {
          item_context->set_callback_event(
            static_cast<kuaishou::ad::AdCallbackLog_EventType>(int_val.value()));
          feature_length = item_context->ByteSizeLong();
        }
        ks::infra::PerfUtil::IntervalLogStash(feature_length, FLAGS_perf_name_space,
          "TransformItemFeatureTableEnricher", "item_feature_length", "callback_event");
      } else if (attr->name() == "item_id_info") {
        const auto *extra_val = attr->GetExtraValue(item.GetAttrIndex());
        if (extra_val) {
          auto* item_id_info = item_context->mutable_item_id_info();
          item_id_info->CopyFrom(*boost::any_cast<
            std::shared_ptr<kuaishou::ad::algorithm::ItemComponentInfo>>(*extra_val));
          feature_length = item_id_info->ByteSizeLong();
        }
        ks::infra::PerfUtil::IntervalLogStash(feature_length, FLAGS_perf_name_space,
          "TransformItemFeatureTableEnricher", "item_feature_length", "item_id_info");
      }
    }
  });
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass,
    TransformItemFeatureTableEnricher, TransformItemFeatureTableEnricher);

}  // namespace platform
}  // namespace ks
