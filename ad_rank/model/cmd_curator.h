#pragma once
#pragma once
#include <map>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include "teams/ad/ad_base/src/smr/smr.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/engine_base/cmd_curator/cmd_config_updater.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "third_party/tbb/include/tbb/concurrent_hash_map.h"

namespace ks {
namespace ad_rank {

class MovingData {
 public:
  ~MovingData() {}
  static MovingData *GetInstance() {
    static MovingData instance;
    return &instance;
  }
  tbb::concurrent_hash_map<std::string, double> predict_scores_;
  double CheckMovingAvgScore(ContextData* session, const std::string& cmd_key,
                const std::string& cmd, const std::string& predict_type_name, const double& predict_score);
};

struct CmdCuratorContext {
  ~CmdCuratorContext() {
    if (func_ret_vec_) {
      delete [] func_ret_vec_;
    }
  }

  int64_t llsid() const {
    return session_data_->get_llsid();
  }

  int64_t user_id() const {
    return session_data_->get_user_id();
  }

  const auto* kconf_session_context() const {
    return &session_data_->get_spdm_ctx();
  }

  ContextData* session_data_ {nullptr};
  const ks::spdm::Context* session_context {nullptr};
  AdList* ad_list_ {nullptr};
  bool *func_ret_vec_ {nullptr};
  size_t func_ret_vec_size_ {0};
  int64_t get_llsid() {
    return llsid();
  }
  absl::flat_hash_set<int64_t> non_live_hosting_set;
  std::shared_ptr<engine_base::CmdConfigSnapshot> cmd_config_snapshot_;
  std::unordered_set<std::string> pv_accept_cmd_key_;
  std::shared_ptr<const ad_base::ScopedSmrPtr<std::unordered_set<int64_t>>> mutil_model_creativeid_map_data_;
  // 精排
  int64 random_select_photoid_for_stay_time;
  bool need_predict_user_next_stay_time{false};
  bool enable_adx_large_creatives_model_{false};
  bool enable_reco_ad_merchant_follow_double_bid_ = false;
  bool enable_auc_sample_opt = {false};
  bool enable_sdpa_ensemble_invoked_ = {false};
  bool enable_sdpa_ensemble_conversion_ = {false};
  bool enable_sdpa_ecom_conv_main_pred_ = {false};
  bool enable_finedu_obtain_cvr_ecpc_ = {false};
  bool enable_invo_traffic_predict = {false};
  bool enable_invo_traffic_cali_top_layer_emb_predict = {false};
  bool enable_invo_traffic_cali_ad_conversion = {false};
  bool enable_invo_traffic_cali_event_order_paied = {false};
  bool enable_lps_acquisition_generalization_ = {false};
  bool enable_iaa_acquisition_generalization_ = {false};
  bool iaa_acq_gen_page_flag_ = {false};
  bool enable_iaa_acq_gen_ecpc_on_skip_sheild_budget_ = {false};
  bool enable_skip_sheild_budget_by_version_whitejson_ = {false};
  std::string iaa_acq_gen_version_str_ = {""};
  std::vector<std::string> iaa_acq_gen_version_list_;
  bool enable_customer_acquisition_cvr_request_ = {false};
  bool enable_car_ensemble_imp_lps_ = {false};
  bool enable_edu_ensemble_imp_lps_ = {false};
  bool enable_c2_lps_ = {false};
  bool enable_c2_lps_ownctr_ = {false};
  bool enable_use_credit_cvr_ = {false};
  bool enable_fin_credit_roi_ecpc_ = {false};
  bool enable_use_jinjian_credit_cvr_ = {false};
  bool enable_car_purchase_single_cmd_ = {false};
  bool enable_car_conv_purchase_cmd_ = {false};
  bool enable_car_purchase_single_ensemble_ = {false};
  bool enable_car_conv_purchase_ensemble_ = {false};
  bool enable_invoked_ecom_cmd_ = {false};
  bool enable_invoked_ecom_exp_cmd_ = {false};
  bool enable_use_invoke_promotion_model_ = {false};
  bool enable_invoked_ecom_multi_cmd_ = {false};
  bool enable_wangfu_purchase_cmd_ = {false};
  bool enable_use_x18_purchase_model_ = {false};
  bool enable_purchase_ecom_cmdkey_ = {false};
  bool enable_use_purchase_promotion_model_ = {false};
  bool enable_use_purchase_exp_model_ = {false};
  bool enable_purchase_ee_cmdkey_ = {false};
  bool enable_ecom_conv_ensemble_ = {false};
  bool enable_long_ratio_ad_roas_ = {false};
  bool enable_long_ratio_ad_roas_search_2_7_amt_ = {false};
  bool enable_long_ratio_ad_roas_splash_2_7_amt_ = {false};
  bool enable_deep_cvr_shelve_ = {false};
  bool enable_long_ratio_roas_twin_game_ad_ = {false};
  bool enable_request_game_30d_ltv_hc_ = {false};
  bool enable_conv_q_ecpc_splash_ = {false};
  bool enable_game_roi_request_ltv27 = {false};
  bool enable_outer_game_invoke_link = {false};
  bool enable_search_duanju_independent = {false};
  bool enable_request_game_player_only_first_charge_ecpc_ = {false};
  bool enable_edu_lps_deep_ecpc_ = {false};
  bool enable_edu_lps_deep_ecpc_other_ind_ = {false};
  bool fix_hard_unify_sctr_click_ = {false};
  bool fix_hard_unify_sctr_ = {false};
  bool enable_clue_ensemble_imp_lps_ = {false};
  bool enable_product_simple_promotion_ensemble_ = {false};
  bool enable_merge_clue_and_edu_ = {false};
  bool enable_sdpa_llm_generative_ecpc_ = {false};
  bool enable_clue_llm_generative_ecpc_ = {false};
  bool enable_clue_llm_fwh_ecpc_ = {false};
  bool enable_inner_explore_sim_rank_ {false};
  bool enable_live_item_emb_ {false};
  bool enable_iaa_item_emb_ {false};
  bool enable_iaa_rl_emb_ {false};
  bool enable_iaa_rl_page_pos_ {false};
  bool enable_outer_live_sid_ {false};
  bool enable_outer_live_recall_emb_ {false};
  bool enable_live_rl_ecpc_ {false};
  bool enable_live_rl_onemodel_ {false};
  bool enable_wechat_game_rnd_explore_outer = false;
  bool enable_fiction_conv_ltv_model = {false};
  bool fix_fiction_uplift_model_combine = {false};
  bool enable_fiction_pay_ltv_model = {false};
  bool enable_industry_fiction_iaa_ltv_ensemble = {false};
  bool enable_search_series_iaa_conv = {false};
  bool enable_fiction_iap_pay_ltv_multi_head = {false};
  bool enable_fiction_iap_price_multi_head_v2 = {false};
  bool enable_shelf_live_order_pltv_model_exp = {false};

  // end
  double high_atv_thre{500000.0};

  std::shared_ptr<absl::flat_hash_set<int64>> special_lps_unit_set_;
  std::shared_ptr<absl::flat_hash_map<int64_t, double>> ltv_account_map_;
  std::shared_ptr<absl::flat_hash_map<int64_t, double>> seven__days_ltv_account_map_;
  std::shared_ptr<absl::flat_hash_map<std::string, double>> seven_days_secret_ltv_product_map_;
  std::shared_ptr<absl::flat_hash_map<std::string, double>> seven_days_pay_times_product_map_;
  std::shared_ptr<absl::flat_hash_map<std::string, double>> car_lps_whitelis_product_map_;
  std::shared_ptr<absl::flat_hash_map<std::string, double>> game_invoke_link_config;
  std::shared_ptr<absl::flat_hash_set<std::string>> mini_app_roas_invoked_product_set_;
  std::shared_ptr<absl::flat_hash_set<std::int64_t>> mini_app_roas_invoked_account_set_;
  std::shared_ptr<absl::flat_hash_set<std::int64_t>> iaa_rl_page_id_;
  std::shared_ptr<absl::flat_hash_set<std::int64_t>> iaa_rl_pos_id_;
  std::shared_ptr<absl::flat_hash_set<std::string>> seven_days_purchase_pay_times_product_set_;
  std::shared_ptr<absl::flat_hash_map<std::string, double>> fiction_app_conversion_purchase_product_map_;
  std::shared_ptr<absl::flat_hash_map<int64_t, double>> fiction_app_conversion_purchase_account_map_;
  std::shared_ptr<std::map<int64_t, double>> key_action_account_map_;
  std::shared_ptr<std::map<int64_t, double>> key_action_account_map2_;
  std::shared_ptr<absl::flat_hash_set<std::string>> key_action_use_ltv_product_set_;

  std::shared_ptr<std::map<int64_t, double>> key_action_cold_start_extreme_account_map_;
  std::shared_ptr<std::map<int64_t, double>> key_action_retention_account_map_;
  std::shared_ptr<absl::flat_hash_set<int64>> multiChuDian_test_account_list_;
  std::shared_ptr<absl::flat_hash_map<int64_t, double>> phone_connect_account_map_;
  std::shared_ptr<absl::flat_hash_map<int64_t, std::string>> ad_order_submit_cold_start_account_product_map_;
  std::shared_ptr<absl::flat_hash_set<int64>> aliOuterDeliveryAccountList;
  const ks::ad_rank::kconf::OutsideEcpcConf *ad_roas_drop_admit_ = nullptr;
  const ks::ad_rank::kconf::CidCustomizeDataV2 *cid_customize = nullptr;
  std::shared_ptr<absl::flat_hash_set<std::int64_t>> short_play_clk2pay_account_list_;
  std::shared_ptr<absl::flat_hash_set<std::int64_t>> short_play_clk2pay_account_roas_list_;
  std::shared_ptr<absl::flat_hash_set<std::string>> outside_ecpc_regiester_set_;
  std::shared_ptr<absl::flat_hash_set<std::int64_t>> playlet_model_conv_tail_;
  std::shared_ptr<absl::flat_hash_set<std::int64_t>> playlet_model_panel_tail_;
  std::shared_ptr<::ks::infra::TailNumberV2> esp_roas_ensemble_tail_;
  std::shared_ptr<::ks::infra::TailNumberV2> is_playlet_invoked_tail_;
  // 次留双出价白名单模型 kconf
  std::shared_ptr<absl::flat_hash_set<int64>> fin_edu_obtain_account_;
  std::shared_ptr<absl::flat_hash_set<int64>> fin_finance_lps_ensemble_industry_;
  std::shared_ptr<absl::flat_hash_set<std::string>> fin_use_credit_product_;
  std::shared_ptr<absl::flat_hash_set<std::string>> fin_credit_roi_white_product_;
  std::shared_ptr<absl::flat_hash_set<std::string>> fin_jinjian_credit_product_;
  std::shared_ptr<absl::flat_hash_set<std::string>> edu_lps_deep_ecpc_white_product_;
  std::shared_ptr<absl::flat_hash_set<std::string>> edu_lps_deep_ecpc_all_class_white_product_;
  std::shared_ptr<absl::flat_hash_set<std::string>> acquisition_generalization_whiteproduct_list_;
  std::shared_ptr<absl::flat_hash_set<std::string>> acquisition_generalization_whiteproduct_list_forC_;
  std::shared_ptr<absl::flat_hash_set<int64>> iaa_acquisition_generalization_whiteocpx_set_;
  std::shared_ptr<absl::flat_hash_set<int64>> skip_sheild_budget_whiteset_;
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<ks::ad_rank::kconf::OutsideEcpcConf>>
      ad_roas_drop_admit_conf_;
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<ks::ad_rank::kconf::CidCustomizeDataV2>>
      cid_customize_conf;
  std::shared_ptr<absl::flat_hash_map<std::string, std::string>> yanhang_pro_product_map;
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<ks::ad_rank::kconf::MerchantPlatformHcConf>>
      merchant_platform_hc_conf_;
  const ks::ad_rank::kconf::MerchantPlatformHcConf_GpmDoubleValue
      *merchant_platform_hc_conf_gpm_ = nullptr;
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<ks::ad_rank::kconf::DirectMerchantMcdaConf>>
      direct_merchant_mcda_conf_;
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<ks::ad_rank::kconf::DirectMerchantMcdaConf>>
      direct_merchant_search_conf_;

  // end
  // 深度授信白名单模型 kconf
  // end

  // 网服二级行业列表
  // 进人白名单
  std::shared_ptr<absl::flat_hash_set<int64_t>> audience_white_account_set_ = nullptr;

  std::shared_ptr<std::unordered_set<std::string>> game_shoufa_product_name_set_ =
    std::make_shared<std::unordered_set<std::string>>();

  // 外循环迁移开关
  bool enable_mini_app_roas_invoked = false;
  bool enable_seven_day_ltv = false;
  bool kpo_roi_gray = false;
  bool kpo_roi_test = false;
  bool kpo_roi_launch = false;
  bool enable_mt_shouxin_launch = false;
  bool enable_mt_cov_exp_tmp = false;
  bool enable_shallow_unify_sdpa = false;
  bool enable_10b_1009_auc = false;
  bool enable_rewarded_cmd_lps = false;
  bool enable_ad_rank_clk2purchase_predict = false;
  bool enable_ad_rank_industry_purchase_predict = false;
  bool enable_ad_rank_industry_purchase_predict_new = false;
  bool enable_ad_rank_playlet_pay_panel_purchase = false;
  bool enable_ad_rank_playlet_click_pay_purchase = false;
  bool disable_playlet_uplift_cvr_cmd = false;
  bool enable_ad_dsp_playlet_iaa_ltv_transform = false;
  bool enable_ad_rank_playlet_conversion_unify = false;
  bool enable_fiction_pay_realtime_model = false;
  bool enable_fiction_ltv_realtime_model = false;
  bool enable_ad_rank_industry_invoked_purchase_ltv_predict = false;
  bool enable_ad_rank_industry_invoked_purchase_ltv_predict_new = false;
  bool enable_only_playlet_invoked_model_predict = false;
  bool enable_only_playlet_invoked_model_predict_ltv_new_normal = false;
  bool enable_only_playlet_invoked_model_predict_ltv_old = false;
  bool enable_only_playlet_invoked_model_predict_account = false;
  bool enable_ad_rank_industry_conv_purchase_ltv_predict = false;
  bool enable_playlet_inovked_predict = false;
  bool enable_playlet_click_app_invoked = false;
  bool enable_playlet_conversion_predict = false;
  bool enable_conv_invoked_pay_online = false;
  bool enable_ad_rank_clk2purchase_roas_predict = false;
  bool enable_rewarded_change_lps = false;
  bool enable_cross_auc_lps = false;
  bool ocpm_cut_by_item_imp_app_invoked = false;
  bool enable_conv_quality_ecpc = false;
  bool enable_mt_lps_exp_tmp = false;
  bool enable_mt_lps_launch = false;
  bool enable_mcda_up_purchase_third_party_ecpc_exp = false;
  bool enable_mcda_up_purchase_third_party_ecpc_launch = false;
  bool enable_mt_purchase_exp_tmp = false;
  bool enable_mt_purchase_launch = false;
  bool enable_pre_mt_purchase = false;
  bool enable_self_service_request_purchase = false;
  bool enable_jiaotong_low_pay_cvr_filter = false;
  bool enable_jiaotong_dynamic_low_pay_filter = false;
  bool enable_baokuanju_dynamic_coef_adjustment = false;
  bool enable_ecom_conv_cross_auc = {false};
  bool enable_invoke_cross_auc = {false};
  bool enable_adx_thanos_cvr_multihead = {false};
  bool enable_conv_nextstay_ecpc = {false};
  bool enable_cid_order_cross_auc = {false};
  bool enable_cid_order_live_cross_auc = {false};
  bool enable_cid_order_live_cmdkey_v2 = {false};
  bool enable_invoke_splash_cross_auc = {false};
  bool enable_ad_dsp_playlet_purchase_ltv = {false};
  bool enable_ad_dsp_live_button_click = {false};
  bool enable_ad_dsp_live_button_click_test = {false};
  bool enable_ad_dsp_live_playtime = {false};
  bool enable_ad_dsp_live_lps_playtime = {false};
  bool enable_outer_live_playtime_strategy = {false};
  bool enable_ad_dsp_live_wx = {false};
  bool enable_ad_dsp_live_wx2 = {false};
  bool enable_ad_dsp_live_lps_photo_to_live = {false};
  bool enable_ad_dsp_live_lps_direct_live = {false};
  bool enable_ad_dsp_item_imp_lps_message_local = {false};
  bool enable_ad_dsp_item_imp_lps_leadssubmit_local = {false};
  bool enable_healthcare_single_tower = {false};

  // amd 直播
  bool disable_feed_server_show_p3s_model = {false};
  bool enable_server_show_live_started_feed_search = {false};
  bool enable_search_mini_app = {false};
  bool disable_slide_server_show_p3s_model = {false};
  bool enable_server_show_live_started_slide_search = {false};
  bool enable_slide_server_show_ctr_search = {false};
  bool enable_only_keep_roas = {false};
  bool enable_inner_hard_gpm_score = {false};
  bool enable_hc_inner_live_independent_gmv = {false};
  bool enable_hard_roas_live_pay_rate = {false};
  bool enable_roas_live_high_atv = {false};
  bool enable_soft_roas_live_pay_rate = {false};
  bool enable_esp_live_inspire_gmv = {false};
  bool enable_live_ltv_use_auc = {false};
  bool enable_live_audience_single_use_auc = {false};
  bool enable_live_audience_feed_use_auc = {false};
  bool enable_context_info_completion = {false};
  bool enable_sv_rank_candidante = {false};
  bool enable_live_pay_use_auc = {false};
  bool enable_merchant_roas_use_auc = {false};
  bool enable_merchant_t7_roi_use_auc = {false};
  bool enable_live_pay_follow_tab_exp = {false};
  bool enable_p2l_pay_follow_tab_exp = {false};
  bool enable_feed_ctr_fanstop_ad = {false};
  bool enable_mobile_live_pay_follow_tab_exp = {false};
  bool enable_mobile_p2l_pay_follow_tab_exp = {false};
  bool enable_live_pay_single_tab = {false};
  bool enable_live_pay_p2l_single_tab = {false};
  bool enable_fans_follow_use_auc = {false};
  bool enable_uplift_order_paied = {false};
  bool enable_native_uplift_order_paied = {false};
  bool enable_uplift_valid_page = {false};
  bool enable_lsp_storewide_flow_gmv_model_ensemble = {false};
  bool enable_lsp_storewide_search_gmv_model_ensemble = {false};
  bool enable_sv_roas_gpm_cmd = {false};
  bool enable_roas_native_use_imp_ltv_switch_new = {false};
  bool enable_roas_multi_cmd = {false};
  bool enable_live_p2l_thanos_split = {false};
  bool enable_p2l_use_auc = {false};
  bool enable_live_order_item_live_split = {false};
  bool enable_inner_live_atomization_order = {false};
  bool enable_inner_live_atomization_roas = {false};
  bool enable_inner_live_atomization_net_roas = {false};
  bool enable_inner_live_atomization_switch = {false};
  bool enable_inner_live_atomization_t7_switch = {false};
  bool enable_inner_live_atomization_t7roas = {false};
  bool enable_roas_gpm_cmd = {false};
  bool enable_roas_7days_cmd = {false};
  bool enable_roas_7days_0_2h_cmd = {false};
  bool enable_roas_7days_2h_3d_cmd = {false};
  bool enable_roas_7days_3d_7d_cmd = {false};
  bool enable_roas_7days_relative_predict_cmd = {false};
  bool enable_use_new_order_pay_cmd_for_guess_you_like = {false};
  bool enable_use_new_order_paied_cmd_for_mall = {false};
  bool enable_use_new_order_paied_cmd_for_zhuanqian = {false};
  bool enable_use_new_order_paied_cmd_for_buyer_home = {false};
  bool enable_real_deep_wechat_connected_lps_cvr_set = {false};
  bool enable_shelf_photo_cmd_fix = {false};
  bool enable_order_paied_cvr_pos_cmd_for_gyl = {false};
  bool enable_order_paied_cvr_pos_cmd_for_bh = {false};
  bool enable_order_paied_cvr_pos_cmd_for_mall = {false};
  bool enable_inner_photo_shelf_ctr_cmd = {false};
  bool enable_inner_live_qcpx_order = {false};
  bool enable_inner_live_qcpx_roas = {false};
  bool enable_inner_live_qcpx_t7roas = {false};

  // 搜索直播
  bool amd_enable_search_photo2live_single_sctr = {false};

  // amd 短视频
  bool enable_item_card = {false};
  bool enable_neixunhuan_photo_roi_cvr_reset = {false};
  bool enable_hc_gpm_order_pay_independent = {false};
  bool enable_search_roi_model = {false};
  bool enable_search_jinniu_moble_roi_model = {false};
  bool enable_search_jinniu_pc_photo_roi_model = {false};
  bool enable_reco_follow_use_auc_exp = {false};
  bool enable_conv_search_cmd = {false};
  bool enable_search_adbox_info_conv_model = {false};
  bool enable_search_adbox_order_pay_model = {false};
  bool enable_style_distinguish_strategy = {false};
  bool enable_live_roas_style_distinguish_strategy = {false};
  bool enable_search_seven_day_roas = {false};
  bool enable_lps_search_cmd = {false};
  bool enable_search_first_day_roi = {false};
  bool enable_roi_purchase_cold = {false};
  bool enable_game_purchase_request_roi = {false};
  bool enable_click_purchase_single_bid_search = {false};
  bool enable_app_purchase_c1_purchase_cold = {false};
  bool enable_lps_click_purchase_single_bid_search = {false};
  bool enable_lps_rank_click2_purchase_single_bid_search = {false};
  bool enable_rank_click2_purchase_single_bid_search = {false};
  bool enable_search_nextday_stay = {false};
  bool enable_conv_rention_search_cmd = {false};
  bool enable_ad_dsp_cvr_cmd_search = {false};
  bool enable_search_key_action = {false};
  bool enable_server_show_item_imp_ocpm_search_cmd = {false};
  bool enable_c1_order_paid_search = {false};
  bool enable_c1_order_paid_search_goods = {false};
  bool close_c1_order_paid_search_goods = {false};
  bool close_search_goods_roi = {false};
  bool enable_live_server_show_ctr_search = {false};
  bool enable_photo2live_server_show_ctr_search = {false};
  bool enable_photo2live_server_show_rate_search = {false};
  bool enable_photo2live_ctr_feed_search = {false};
  bool enable_search_ctr_to_fanstop = {false};
  bool ad_merchant_dsp_switch = {false};
  bool enable_live_pay_rate_search = {false};
  bool enable_photo2live_pay_rate_search = {false};
  bool enable_photo2live_order = {false};
  bool enable_search_sctr_inner = {false};
  bool enable_photo_goods_search{false};
  bool enable_esp_live_roas_ensemble2{false};
  bool disable_esp_live_roas_ensemble{false};
  bool enable_live_storewide_roas_ensemble{false};
  bool enable_live_pay_rate_goods_search{false};
  bool enable_card_live_T7{false};
  bool enable_goods_p2l{false};
  bool enable_search_independent_industry_live{false};

  std::shared_ptr<absl::flat_hash_set<int64_t>> outer_live_twin_bid_account_white = nullptr;
  bool enable_outer_live_twin_bid_flow = {false};

  std::shared_ptr<absl::flat_hash_set<int64_t>> esp_live_roas_atv_tail = nullptr;
  std::shared_ptr<absl::flat_hash_set<int64_t>> esp_live_hosting_roas_atv_tail = nullptr;
  std::shared_ptr<absl::flat_hash_set<int64_t>> esp_live_storewide_roas_atv_tail = nullptr;
  bool enable_merchant_video_auc_cross_test = {false};
  bool enable_merchant_photo_roas_cvr_auc_cross_test = {false};

  bool enable_leverage_score_use_post_gmv = {false};
  bool enable_live_calc_vtr_ueq = {false};
  bool enable_request_industry_pay_ltv = {false};
  bool disable_request_industry_pay_ltv = {false};
  bool enable_request_industry_pay_ltv_new = {false};
  bool enable_request_game_industry_pay_ltv = {false};
  bool enable_request_minigame_industry_pay_ltv = {false};
  bool enable_request_game_industry_pay_ltv_fliter = {false};
  bool enable_request_direct_live_lps_realtime = {false};

  // game
  bool enable_request_industry_game_iaa_ltv7 = {false};
  bool enable_iaap_request_iaa_ltv7 = false;
  bool enable_iaap_7r_request_iaa_ltv7 = false;

  bool enable_request_industry_game_iaa_ltv = {false};
  bool enable_iaap_request_iaa_ltv = false;
  // pec 优惠券
  // 开屏
  bool enable_ecpc_conv_predict_more_sta = {false};
  bool enable_ecpc_click_2_purchase_exp = {false};
  bool enable_ecpc_add_weekstay_model = {false};
  bool enable_ecpc_add_acquisition_model = {false};

  bool enable_cid_qcpx_cmd = {false};

  // 预估对齐
  bool enable_conv_boost_roas_request_order_paid = {false};

  // cid roas && gpm 实验
  bool enable_order_submit_gpm_use_ltv_model = {false};

  // 外循环商家新账户单拆
  bool enable_order_submit_cold_split_cmd = {false};
  bool enable_cid_search_boost_exp = {false};
  int64_t cid_search_boost_exp_tag = {0};
  bool enable_cid_roi_imp_gmv_cmd_fix_auc_exp = {false};

  // 拼多多原生广告激活
  bool enable_pdd_native_cmd = {false};
  // 外循环行业直播
  bool enable_dsplive_direct_conv_bypass_auc = {false};
  bool enable_dsplive_direct_lps_bypass_auc = {false};
  bool enable_dsplive_ctr_bypass_auc = {false};
  bool enable_dsplive_p2l_conv_bypass_auc = {false};
  bool enable_dsplive_p2l_lps_bypass_auc = {false};
  bool enable_dsplive_p2l_ctr_bypass_auc = {false};
  // 移动端短视频硬广
  bool enable_mobile_photo_soft_model = {false};
  // 本地短视频订单
  bool enable_lsp_photo_order_paied_model = {false};
  // 直播涨粉
  bool enable_inner_live_follow_rm_invalid_request_2 = {false};
  bool enable_model_explore = {false};

  bool enable_game_shoufa_pay_ecpc_boost = {false};

  // 首发游戏
  bool enable_request_shoufa_game_ltv = {false};
  bool enable_request_shoufa_game_ltv_test = {false};
  bool enable_request_shoufa_game_ltv_hold = {false};
  bool enable_white_acount_request_industry_game_iaa_ltv7 = {false};
  bool enable_request_industry_game_iaa_ltv7_all_product = {false};

  void Init(ContextData* ctx_data) {
    session_data_ = ctx_data;
    session_context = &session_data_->get_spdm_ctx();
    ad_list_ = session_data_->mutable_ad_list();
    enable_adx_large_creatives_model_ =
        session_data_->get_spdm_ctx().TryGetBoolean("enable_adx_large_creatives_model", true);
    non_live_hosting_set = {
          kuaishou::ad::AdEnum::ORIENTED_DEFAULT_TYPE, kuaishou::ad::AdEnum::ORIENTED_FANS_GROWTH,
          kuaishou::ad::AdEnum::ORIENTED_FANS_REACH, kuaishou::ad::AdEnum::ORIENTED_LIVE_PURCHASE,
          kuaishou::ad::AdEnum::ORIENTED_NEW_CUSTOMER_GROWTH, kuaishou::ad::AdEnum::ORIENTED_CUSTOMER_REACH
      };
    multiChuDian_test_account_list_ = RankKconfUtil::ecomMultiChuDianTestAccountSet();
    special_lps_unit_set_ = RankKconfUtil::specialLpsUnits();
    ltv_account_map_ = RankKconfUtil::innerTestAccountRoasMap();
    seven__days_ltv_account_map_ = RankKconfUtil::sevenDaysLtvAcountMap();
    seven_days_secret_ltv_product_map_ = RankKconfUtil::sevenDaysLtvSecretProductMap();
    seven_days_pay_times_product_map_ = RankKconfUtil::sevenDaysPayTimesProductMap();
    car_lps_whitelis_product_map_ = RankKconfUtil::carLpsWhiteList();
    game_invoke_link_config = RankKconfUtil::outerGameInvokeLinkConfig();
    fiction_app_conversion_purchase_product_map_ = RankKconfUtil::fictionConvPayWhiteList();
    fiction_app_conversion_purchase_account_map_ = RankKconfUtil::fictionConvPayAccountWhiteList();
    mini_app_roas_invoked_product_set_ = RankKconfUtil::miniAppRoasInvokedProductWhiteList();
    mini_app_roas_invoked_account_set_ = RankKconfUtil::miniAppRoasInvokedAccountWhiteList();
    iaa_rl_page_id_ = RankKconfUtil::iaaIncentivePageIds();
    iaa_rl_pos_id_ = RankKconfUtil::iaaIncentivePosIds();
    if (session_data_->get_is_rewarded() &&  // 是激励广告
        // page_id 或 pos_id 在指定的 kconf 中
      ((iaa_rl_page_id_ && !iaa_rl_page_id_->empty() &&
      iaa_rl_page_id_->count(session_data_->get_page_id()) > 0) ||
      (session_data_->get_pos_manager_base().request_imp_infos.size() > 0 &&
      iaa_rl_pos_id_ && !iaa_rl_pos_id_->empty() &&
        iaa_rl_pos_id_->count(
          session_data_->get_pos_manager_base().request_imp_infos[0].pos_id) > 0))) {
        enable_iaa_rl_page_pos_ = true;
      }
    short_play_clk2pay_account_list_ = RankKconfUtil::shortPlayClk2PayAccountList();
    short_play_clk2pay_account_roas_list_ = RankKconfUtil::shortPlayClk2PayRoasAccountList();
    seven_days_purchase_pay_times_product_set_ = RankKconfUtil::sevenDaysPurchasePayTimesProductWhiteList();
    playlet_model_conv_tail_ = RankKconfUtil::playletModelConvTail();
    playlet_model_panel_tail_ = RankKconfUtil::playletModelPanelTail();
    esp_roas_ensemble_tail_ = RankKconfUtil::enableEspRoasEmsembleTail();
    is_playlet_invoked_tail_ = RankKconfUtil::isPlayletInvokedTail();

    key_action_account_map_ = RankKconfUtil::keyActionAccountMap();
    key_action_account_map2_ = RankKconfUtil::keyActionAccountMap2();
    key_action_use_ltv_product_set_ = RankKconfUtil::keyActionUseLtvProductSet();
    key_action_cold_start_extreme_account_map_ = RankKconfUtil::keyActionColdStartExtremeAccountMap();
    key_action_retention_account_map_ = RankKconfUtil::keyActionRetentionAccountMap();
    phone_connect_account_map_ = RankKconfUtil::phoneConnectAccountMap();
    ad_order_submit_cold_start_account_product_map_ =
                            RankKconfUtil::adOrderSubmitColdStartAccountProductMap();
    aliOuterDeliveryAccountList = RankKconfUtil::aliOuterDeliveryAccountList();
    outside_ecpc_regiester_set_ = RankKconfUtil::outsideEcpcRegisterConf();
    esp_live_roas_atv_tail = RankKconfUtil::EspLiveRoasAtvTail();
    esp_live_hosting_roas_atv_tail = RankKconfUtil::EspLiveHostingRoasAtvTail();
    esp_live_storewide_roas_atv_tail = RankKconfUtil::EspLiveStorewideRoasAtvTail();
    random_select_photoid_for_stay_time = session_data_->get_random_select_photoid_for_stay_time();
    need_predict_user_next_stay_time = session_data_->get_need_predict_user_next_stay_time();
    // 首日 roi drop 策略白名单
    ad_roas_drop_admit_conf_ = RankKconfUtil::adRoasDropConf();
    ad_roas_drop_admit_ = &(ad_roas_drop_admit_conf_->data());
    cid_customize_conf = RankKconfUtil::cidCustomizeDataV2();
    cid_customize =  &(cid_customize_conf->data());
    enable_ecpc_conv_predict_more_sta = SPDM_enableEcpcConvPredict();
    enable_ecpc_add_weekstay_model = SPDM_enable_ecpc_add_weekstay_model(session_data_->get_spdm_ctx());
    enable_ecpc_add_acquisition_model = SPDM_enable_ecpc_add_acquisition_model(session_data_->get_spdm_ctx());
    // enable_cid_qcpx_cmd = SPDM_enable_cid_qcpx_cmd(session_data_->get_spdm_ctx());
    enable_ecpc_click_2_purchase_exp = SPDM_enable_ecpc_click_2_purchase_exp(session_data_->get_spdm_ctx());
    enable_conv_boost_roas_request_order_paid =
        SPDM_enable_conv_boost_roas_request_order_paid(session_data_->get_spdm_ctx());
    merchant_platform_hc_conf_ = RankKconfUtil::merchantPlatformHcConf();
    merchant_platform_hc_conf_gpm_ = &(merchant_platform_hc_conf_->data().gpm_info());
    direct_merchant_mcda_conf_ = RankKconfUtil::directMerchantMcdaConf();
    direct_merchant_search_conf_ = RankKconfUtil::directMerchantSearchConf();
    enable_long_ratio_ad_roas_ = SPDM_enable_long_ratio_ad_roas(session_data_->get_spdm_ctx());
    enable_esp_live_roas_ensemble2 = SPDM_enable_esp_live_roas_ensemble2(session_data_->get_spdm_ctx());
    disable_esp_live_roas_ensemble = SPDM_disable_esp_live_roas_ensemble(session_data_->get_spdm_ctx());
    enable_live_storewide_roas_ensemble =
        SPDM_enable_live_storewide_roas_ensemble(session_data_->get_spdm_ctx());
    enable_long_ratio_ad_roas_search_2_7_amt_ =
        SPDM_enable_long_ratio_ad_roas_search_2_7_amt(session_data_->get_spdm_ctx());
    enable_long_ratio_ad_roas_splash_2_7_amt_ =
        SPDM_enable_long_ratio_ad_roas_splash_2_7_amt_(session_data_->get_spdm_ctx());
    enable_deep_cvr_shelve_ =
        SPDM_enable_deep_cvr_shelve(session_data_->get_spdm_ctx());
    enable_long_ratio_roas_twin_game_ad_ = SPDM_enable_long_ratio_roas_twin_game_ad(session_data_->get_spdm_ctx());  // NOLINT
    enable_game_roi_request_ltv27 = SPDM_enable_game_roi_request_ltv27(session_data_->get_spdm_ctx());
    enable_request_game_30d_ltv_hc_ = SPDM_enable_request_game_30d_ltv_hc(session_data_->get_spdm_ctx());
    enable_request_game_player_only_first_charge_ecpc_ =
        SPDM_enable_request_game_player_only_first_charge_ecpc(session_data_->get_spdm_ctx());
    enable_wechat_game_rnd_explore_outer =
        SPDM_enable_wechat_game_rnd_explore_outer(session_data_->get_spdm_ctx());
    enable_edu_lps_deep_ecpc_ =
        SPDM_enable_edu_lps_deep_ecpc(session_data_->get_spdm_ctx());
    enable_edu_lps_deep_ecpc_other_ind_ =
        SPDM_enable_edu_lps_deep_ecpc_other_ind(session_data_->get_spdm_ctx());
    enable_outer_game_invoke_link = SPDM_enable_outer_game_invoke_link(session_data_->get_spdm_ctx());
    enable_car_ensemble_imp_lps_ = SPDM_enable_car_ensemble_imp_lps(session_data_->get_spdm_ctx());
    enable_auc_sample_opt = SPDM_enable_auc_sample_opt(session_data_->get_spdm_ctx());
    enable_edu_ensemble_imp_lps_ = SPDM_enable_edu_ensemble_imp_lps(session_data_->get_spdm_ctx());
    enable_c2_lps_ = SPDM_enable_c2_lps(session_data_->get_spdm_ctx());
    enable_c2_lps_ownctr_ = SPDM_enable_c2_lps_ownctr(session_data_->get_spdm_ctx());
    enable_car_purchase_single_cmd_ = SPDM_enable_car_purchase_single_cmd(session_data_->get_spdm_ctx());
    enable_car_conv_purchase_cmd_ = SPDM_enable_car_conv_purchase_cmd(session_data_->get_spdm_ctx());
    enable_car_purchase_single_ensemble_ = SPDM_enable_car_purchase_single_ensemble(session_data_->get_spdm_ctx());  // NOLINT
    enable_car_conv_purchase_ensemble_ = SPDM_enable_car_conv_purchase_ensemble(session_data_->get_spdm_ctx());  // NOLINT
    enable_clue_ensemble_imp_lps_ = SPDM_enable_clue_ensemble_imp_lps(session_data_->get_spdm_ctx());  // NOLINT
    enable_product_simple_promotion_ensemble_ = SPDM_enable_product_simple_promotion_ensemble(session_data_->get_spdm_ctx());  // NOLINT
    enable_merge_clue_and_edu_ = SPDM_enable_merge_clue_and_edu(session_data_->get_spdm_ctx());  // NOLINT
    enable_sdpa_llm_generative_ecpc_ = SPDM_enable_sdpa_llm_generative_ecpc(session_data_->get_spdm_ctx());  // NOLINT
    enable_clue_llm_generative_ecpc_ = SPDM_enable_clue_llm_generative_ecpc(session_data_->get_spdm_ctx());  // NOLINT
    enable_clue_llm_fwh_ecpc_ = SPDM_enable_clue_llm_fwh_ecpc(session_data_->get_spdm_ctx());  // NOLINT
    enable_inner_explore_sim_rank_ = SPDM_enable_inner_explore_sim_rank(session_data_->get_spdm_ctx()) &&
                                     session_data_->get_is_explore_feed_inner() &&
                                     !session_data_->get_rank_request()->ad_request().feed_triggle_hetu_info().score().empty();  // NOLINT
    enable_invoked_ecom_cmd_ =
     SPDM_enable_click_app_invoked_ecom_cmd(session_data_->get_spdm_ctx());
    enable_invoked_ecom_exp_cmd_ =
     SPDM_enable_click_app_invoked_ecom_exp_cmd(session_data_->get_spdm_ctx());
    enable_use_invoke_promotion_model_ =
     SPDM_enable_use_invoke_promotion_model(session_data_->get_spdm_ctx());
    enable_live_item_emb_ = SPDM_enable_live_item_emb(session_data_->get_spdm_ctx());
    enable_iaa_item_emb_ = SPDM_enable_iaa_item_emb(session_data_->get_spdm_ctx());
    enable_iaa_rl_emb_ = SPDM_enable_iaa_rl_ecpc(session_data_->get_spdm_ctx());
    enable_outer_live_sid_ = SPDM_enable_outer_live_sid(session_data_->get_spdm_ctx());
    enable_outer_live_recall_emb_ = SPDM_enable_outer_live_recall_emb(session_data_->get_spdm_ctx());
    enable_live_rl_ecpc_ = SPDM_enable_live_rl_ecpc(session_data_->get_spdm_ctx());
    enable_live_rl_onemodel_ = SPDM_enable_live_rl_onemodel(session_data_->get_spdm_ctx());
    enable_wangfu_purchase_cmd_ = SPDM_enable_wangfu_purchase_cmd(session_data_->get_spdm_ctx());
    enable_use_x18_purchase_model_ = SPDM_enable_use_x18_purchase_model(session_data_->get_spdm_ctx());
    enable_purchase_ecom_cmdkey_ = SPDM_enable_purchase_ecom_cmdkey(session_data_->get_spdm_ctx());
    enable_use_purchase_promotion_model_ =
      SPDM_enable_use_purchase_promotion_model(session_data_->get_spdm_ctx());
    enable_use_purchase_exp_model_ = SPDM_enable_use_purchase_exp_model(session_data_->get_spdm_ctx());
    enable_purchase_ee_cmdkey_ = SPDM_enable_purchase_ee_cmdkey(session_data_->get_spdm_ctx());
    enable_ecom_conv_ensemble_ = SPDM_enable_ecom_conv_ensemble(session_data_->get_spdm_ctx());
    enable_sdpa_ensemble_invoked_ = SPDM_enable_sdpa_ensemble_invoked(session_data_->get_spdm_ctx());
    enable_finedu_obtain_cvr_ecpc_ = true;
    enable_lps_acquisition_generalization_ = SPDM_enable_lps_acquisition_generalization(session_data_->get_spdm_ctx());  // NOLINT
    enable_iaa_acquisition_generalization_ =
        SPDM_enable_iaa_acquisition_generalization(session_data_->get_spdm_ctx());  // NOLINT
    enable_iaa_acq_gen_ecpc_on_skip_sheild_budget_ =
        SPDM_enable_iaa_acq_gen_ecpc_on_skip_sheild_budget(session_data_->get_spdm_ctx());
    enable_skip_sheild_budget_by_version_whitejson_ =
        SPDM_enable_skip_sheild_budget_by_version_whitejson(session_data_->get_spdm_ctx());
    iaa_acq_gen_version_str_ =
        SPDM_iaa_acq_gen_version_string(session_data_->get_spdm_ctx());;
    iaa_acq_gen_version_list_ =
        absl::StrSplit(iaa_acq_gen_version_str_, ",", absl::SkipEmpty());;
    enable_customer_acquisition_cvr_request_ = SPDM_enable_customer_acquisition_cvr_request(session_data_->get_spdm_ctx());  // NOLINT
    enable_use_credit_cvr_ = SPDM_enable_use_credit_cvr(session_data_->get_spdm_ctx());
    enable_fin_credit_roi_ecpc_ = SPDM_enable_fin_credit_roi_ecpc(session_data_->get_spdm_ctx());
    enable_use_jinjian_credit_cvr_ = SPDM_enable_use_jinjian_credit_cvr(session_data_->get_spdm_ctx());
    enable_sdpa_ensemble_conversion_ = SPDM_enable_sdpa_ensemble_conversion(session_data_->get_spdm_ctx());
    enable_sdpa_ecom_conv_main_pred_ = SPDM_enable_sdpa_ecom_conv_main_pred(session_data_->get_spdm_ctx());
    enable_conv_q_ecpc_splash_ = SPDM_enable_conv_q_ecpc_splash(session_data_->get_spdm_ctx());
    enable_search_duanju_independent = SPDM_enable_search_duanju_independent(session_data_->get_spdm_ctx());
    enable_leverage_score_use_post_gmv = session_data_->get_is_thanos_mix_request();
    outer_live_twin_bid_account_white = RankKconfUtil::outerLiveTwinBidAccountWhite();
    enable_outer_live_twin_bid_flow = SPDM_enable_outer_live_twin_bid_flow(session_data_->get_spdm_ctx());
    enable_live_calc_vtr_ueq =  SPDM_enable_live_calc_vtr_ueq(session_data_->get_spdm_ctx());
    enable_game_shoufa_pay_ecpc_boost = SPDM_enable_game_shoufa_pay_ecpc_boost(session_data_->get_spdm_ctx());
    fix_hard_unify_sctr_click_ = SPDM_fix_hard_unify_sctr_click(session_data_->get_spdm_ctx());
    fix_hard_unify_sctr_ = SPDM_fix_hard_unify_sctr(session_data_->get_spdm_ctx());
    bool is_inspire = session_data_->get_is_rewarded();
    enable_request_industry_pay_ltv
        = SPDM_enable_request_industry_pay_ltv(session_data_->get_spdm_ctx());
    disable_request_industry_pay_ltv
        = SPDM_disable_request_industry_pay_ltv(session_data_->get_spdm_ctx());
    enable_request_industry_pay_ltv_new
        = SPDM_enable_request_industry_pay_ltv_new(session_data_->get_spdm_ctx());
    enable_request_game_industry_pay_ltv =
        SPDM_enable_request_game_industry_pay_ltv(session_data_->get_spdm_ctx());
    enable_request_direct_live_lps_realtime =
        SPDM_enable_request_direct_live_lps_realtime(session_data_->get_spdm_ctx());
    enable_request_minigame_industry_pay_ltv =
        SPDM_enable_request_minigame_industry_pay_ltv(session_data_->get_spdm_ctx());
    enable_request_game_industry_pay_ltv_fliter =
        SPDM_enable_request_game_industry_pay_ltv_fliter(session_data_->get_spdm_ctx());
    enable_fiction_conv_ltv_model =
        SPDM_enable_fiction_conv_ltv_model(session_data_->get_spdm_ctx());
    fix_fiction_uplift_model_combine =
        SPDM_fix_fiction_uplift_model_combine(session_data_->get_spdm_ctx());
    enable_invo_traffic_predict = SPDM_enable_invo_traffic_predict(session_data_->get_spdm_ctx()) &&
                                  EnableInovTrafficPredict();
    enable_invo_traffic_cali_top_layer_emb_predict = EnableInovTrafficPredict() &&
                    SPDM_enable_invo_traffic_cali_top_layer_emb_predict(session_data_->get_spdm_ctx());
    enable_invo_traffic_cali_ad_conversion = EnableInovTrafficPredict() &&
                    SPDM_enable_invo_traffic_cali_ad_conversion(session_data_->get_spdm_ctx());
    enable_invo_traffic_cali_event_order_paied = EnableInovTrafficPredict() &&
                    SPDM_enable_invo_traffic_cali_event_order_paied(session_data_->get_spdm_ctx());
    enable_fiction_pay_ltv_model =
        SPDM_enable_fiction_pay_ltv_model(session_data_->get_spdm_ctx());
    enable_fiction_iap_pay_ltv_multi_head =
        SPDM_enable_fiction_iap_pay_ltv_multi_head(session_data_->get_spdm_ctx());
    enable_industry_fiction_iaa_ltv_ensemble =
        SPDM_enable_industry_fiction_iaa_ltv_ensemble(session_data_->get_spdm_ctx());
    enable_search_series_iaa_conv = SPDM_enable_search_series_iaa_conv(session_data_->get_spdm_ctx());
    yanhang_pro_product_map = RankKconfUtil::adPurchaseYanhangProProductMap();
    fin_edu_obtain_account_ =
        RankKconfUtil::adFinEduObtainAccount();
    fin_finance_lps_ensemble_industry_ =  RankKconfUtil::adFinFinanceLpsEnsembleIndustry();
    fin_use_credit_product_ = RankKconfUtil::adFinUseCreditProduct();
    fin_credit_roi_white_product_ = RankKconfUtil::adFinCreditRoiProduct();
    fin_jinjian_credit_product_ = RankKconfUtil::adFinJinjianCreditProduct();
    edu_lps_deep_ecpc_white_product_ = RankKconfUtil::adEduLpsEcpcWhiteProduct();
    edu_lps_deep_ecpc_all_class_white_product_ = RankKconfUtil::adEduLpsAllClassEcpcWhiteProduct();
    acquisition_generalization_whiteproduct_list_ = RankKconfUtil::lpsAcquisitionGeneralizationWhiteproduct();
    acquisition_generalization_whiteproduct_list_forC_ = RankKconfUtil::lpsAcquisitionGeneralizationWhiteproductForC();  // NOLINT
    iaa_acquisition_generalization_whiteocpx_set_ = RankKconfUtil::IaaAcquisitionGeneralizationWhiteocpxSet();
    skip_sheild_budget_whiteset_ = RankKconfUtil::closedLoopIaaSkipSheildBudgetWhiteSet();
    game_shoufa_product_name_set_->clear();
    const auto& game_shoufa_conf = RankKconfUtil::gameShouFaConf()->data();
    auto product_name_conf = game_shoufa_conf.product_name();
    game_shoufa_product_name_set_->insert(product_name_conf.begin(), product_name_conf.end());

    high_atv_thre = RankKconfUtil::adRankLiveRoasHighAtvThre();
    if (session_data_->get_is_search_request()) {
      amd_enable_search_photo2live_single_sctr = session_data_->get_spdm_ctx()
        .TryGetBoolean("amd_enable_search_photo2live_single_sctr", false);
      enable_conv_search_cmd = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_conv_search_cmd", false);
      enable_search_adbox_info_conv_model = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_search_adbox_info_conv_model", false);
      enable_search_adbox_order_pay_model = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_search_adbox_order_pay_model", false);
      enable_style_distinguish_strategy = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_style_distinguish_strategy", false);
      enable_live_roas_style_distinguish_strategy =
      SPDM_enable_live_roas_style_distinguish_strategy(session_data_->get_spdm_ctx());
      enable_search_seven_day_roas = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_search_seven_day_roas", false);
      enable_lps_search_cmd = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_lps_search_cmd", false);
      enable_search_first_day_roi = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_search_first_day_roi", false);
      enable_roi_purchase_cold = false;
      enable_game_purchase_request_roi = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_game_purchase_request_roi", false);
      enable_click_purchase_single_bid_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_click_purchase_single_bid_search", false);
      enable_app_purchase_c1_purchase_cold = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_app_purchase_c1_purchase_cold", false);
      enable_lps_click_purchase_single_bid_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_lps_click_purchase_single_bid_search", false);
      enable_lps_rank_click2_purchase_single_bid_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_lps_rank_click2_purchase_single_bid_search", false);
      enable_rank_click2_purchase_single_bid_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_rank_click2_purchase_single_bid_search", false);
      enable_conv_rention_search_cmd = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_conv_rention_search_cmd", false);
      enable_ad_dsp_cvr_cmd_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_ad_dsp_cvr_cmd_search", false);
      enable_search_key_action = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_search_key_action", false);
      enable_server_show_item_imp_ocpm_search_cmd = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_server_show_item_imp_ocpm_search_cmd", false);
      enable_c1_order_paid_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_c1_order_paid_search", false);
      enable_c1_order_paid_search_goods = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_c1_order_paid_search_goods", false);
      close_c1_order_paid_search_goods = SPDM_close_c1_order_paid_search_goods(session_data_->get_spdm_ctx());
      close_search_goods_roi = session_data_->get_spdm_ctx()
        .TryGetBoolean("close_search_goods_roi", false);
      enable_live_server_show_ctr_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_live_server_show_ctr_search", false);
      enable_photo2live_server_show_ctr_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_photo2live_server_show_ctr_search", false);
      enable_photo2live_server_show_rate_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_photo2live_server_show_rate_search", false);
      enable_photo2live_ctr_feed_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_photo2live_ctr_feed_search", false);
      enable_search_ctr_to_fanstop = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_search_ctr_to_fanstop", false);
      ad_merchant_dsp_switch = session_data_->get_spdm_ctx()
        .TryGetBoolean("ad_merchant_dsp_switch", false);
      enable_live_pay_rate_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_live_pay_rate_search", false);
      enable_photo2live_order = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_photo2live_order", false);
      enable_photo2live_pay_rate_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_photo2live_pay_rate_search", false);
      enable_search_roi_model = true;
      enable_search_jinniu_moble_roi_model = SPDM_enable_search_jinniu_moble_roi_model(
          session_data_->get_spdm_ctx());
      enable_search_nextday_stay = SPDM_enable_search_nextday_stay(session_data_->get_spdm_ctx());
      enable_search_jinniu_pc_photo_roi_model = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_search_jinniu_pc_photo_roi_model", true);
      enable_search_mini_app =
          SPDM_enable_search_mini_app(session_data_->get_spdm_ctx());
      // 该开关 必须与 搜索内流流量均 True 才会为 True
      enable_search_sctr_inner =
        session_data_->get_spdm_ctx().TryGetBoolean("enable_search_sctr_inner", false)
        && session_data_->get_is_search_inner_stream();
    }
    if (session_data_->get_is_default_deploy() || session_data_->get_is_search_request()) {
      // 外循环迁移
      enable_seven_day_ltv = true;
      kpo_roi_gray = session_data_->get_spdm_ctx()
          .TryGetBoolean("kpo_roi_gray", false);
      kpo_roi_test = session_data_->get_spdm_ctx()
          .TryGetBoolean("kpo_roi_test", false);
      kpo_roi_launch = session_data_->get_spdm_ctx()
          .TryGetBoolean("kpo_roi_launch", false);
      enable_roi_purchase_cold = false;
      enable_pdd_native_cmd = false;
      enable_mt_shouxin_launch = SPDM_enable_mt_shouxin_launch(session_data_->get_spdm_ctx());
      enable_self_service_request_purchase = RankKconfUtil::enableSelfServiceRequestPurchase();
      enable_ad_rank_clk2purchase_predict =
                    SPDM_enable_ad_rank_clk2purchase_predict(session_data_->get_spdm_ctx());
      enable_request_shoufa_game_ltv =
                    SPDM_enable_request_shoufa_game_ltv(session_data_->get_spdm_ctx());
      enable_request_shoufa_game_ltv_test =
                    SPDM_enable_request_shoufa_game_ltv_test(session_data_->get_spdm_ctx());
      enable_request_shoufa_game_ltv_hold =
                    SPDM_enable_request_shoufa_game_ltv_hold(session_data_->get_spdm_ctx());
      enable_ad_rank_industry_purchase_predict =
                    SPDM_enable_ad_rank_industry_purchase_predict(session_data_->get_spdm_ctx());
      enable_ad_rank_industry_purchase_predict_new =
                    SPDM_enable_ad_rank_industry_purchase_predict_new(session_data_->get_spdm_ctx());
      enable_ad_rank_playlet_pay_panel_purchase =
                    SPDM_enable_ad_rank_playlet_pay_panel_purchase(session_data_->get_spdm_ctx());
      enable_baokuanju_dynamic_coef_adjustment =
                    SPDM_enable_baokuanju_dynamic_coef_adjustment(session_data_->get_spdm_ctx());
      enable_ad_rank_playlet_click_pay_purchase =
                    SPDM_enable_ad_rank_playlet_click_pay_purchase(session_data_->get_spdm_ctx());
      disable_playlet_uplift_cvr_cmd =
                    SPDM_disable_playlet_uplift_cvr_cmd(session_data_->get_spdm_ctx());
      enable_ad_dsp_playlet_iaa_ltv_transform =
                    SPDM_enable_ad_dsp_playlet_iaa_ltv_transform(session_data_->get_spdm_ctx());
      enable_request_industry_game_iaa_ltv =
                    SPDM_enable_request_industry_game_iaa_ltv(session_data_->get_spdm_ctx());
      enable_search_independent_industry_live =
                    SPDM_enable_search_independent_industry_live(session_data_->get_spdm_ctx());
      enable_request_industry_game_iaa_ltv7 =
                    SPDM_enable_request_industry_game_iaa_ltv7(session_data_->get_spdm_ctx());
      enable_iaap_request_iaa_ltv7 = SPDM_enable_iaap_request_iaa_ltv7(session_data_->get_spdm_ctx());
      enable_iaap_7r_request_iaa_ltv7 =
                                  SPDM_enable_iaap_7r_request_iaa_ltv7(session_data_->get_spdm_ctx());
      enable_request_industry_game_iaa_ltv7_all_product =
                    SPDM_enable_request_industry_game_iaa_ltv7_all_product(session_data_->get_spdm_ctx());
      enable_white_acount_request_industry_game_iaa_ltv7 =
                    SPDM_enable_white_acount_request_industry_game_iaa_ltv7(session_data_->get_spdm_ctx());
      enable_iaap_request_iaa_ltv = SPDM_enable_iaap_request_iaa_ltv(session_data_->get_spdm_ctx());
      enable_ad_rank_playlet_conversion_unify =
                    SPDM_enable_ad_rank_playlet_conversion_unify(session_data_->get_spdm_ctx());
      enable_fiction_pay_realtime_model =
                    SPDM_enable_fiction_pay_realtime_model(session_data_->get_spdm_ctx());
      enable_fiction_ltv_realtime_model =
                    SPDM_enable_fiction_ltv_realtime_model(session_data_->get_spdm_ctx());
      enable_ad_rank_industry_invoked_purchase_ltv_predict =
          SPDM_enable_ad_rank_industry_invoked_purchase_ltv_predict(session_data_->get_spdm_ctx());
      enable_only_playlet_invoked_model_predict =
          SPDM_enable_only_playlet_invoked_model_predict(session_data_->get_spdm_ctx());
      enable_only_playlet_invoked_model_predict_ltv_new_normal =
          SPDM_enable_only_playlet_invoked_model_predict_ltv_new_normal(session_data_->get_spdm_ctx());
      enable_only_playlet_invoked_model_predict_ltv_old =
          SPDM_enable_only_playlet_invoked_model_predict_ltv_old(session_data_->get_spdm_ctx());
      enable_only_playlet_invoked_model_predict_account =
          SPDM_enable_only_playlet_invoked_model_predict_account(session_data_->get_spdm_ctx());
      enable_ad_rank_industry_invoked_purchase_ltv_predict_new =
          SPDM_enable_ad_rank_industry_invoked_purchase_ltv_predict_new(session_data_->get_spdm_ctx());
      enable_ad_rank_industry_conv_purchase_ltv_predict =
          SPDM_enable_ad_rank_industry_conv_purchase_ltv_predict(session_data_->get_spdm_ctx());
      enable_playlet_inovked_predict =
                    SPDM_enable_playlet_inovked_predict(session_data_->get_spdm_ctx());
      enable_playlet_click_app_invoked =
                    SPDM_enable_playlet_click_app_invoked(session_data_->get_spdm_ctx());
      enable_playlet_conversion_predict =
                    SPDM_enable_playlet_conversion_predict(session_data_->get_spdm_ctx());
      enable_conv_invoked_pay_online =
                    SPDM_enable_conv_invoked_pay_online(session_data_->get_spdm_ctx());
      enable_ad_rank_clk2purchase_roas_predict =
                    SPDM_enable_ad_rank_clk2purchase_roas_predict(session_data_->get_spdm_ctx());
      enable_ecom_conv_cross_auc =
          SPDM_enable_ecom_conv_cross_auc(session_data_->get_spdm_ctx());
      enable_invoke_cross_auc =
          SPDM_enable_invoke_cross_auc(session_data_->get_spdm_ctx());
      enable_invoke_splash_cross_auc =
          SPDM_enable_invoke_splash_cross_auc(session_data_->get_spdm_ctx());
      enable_ad_dsp_playlet_purchase_ltv =
          SPDM_enable_ad_dsp_playlet_purchase_ltv(session_data_->get_spdm_ctx()); // NOLINT
      enable_ad_dsp_live_button_click =
          SPDM_enable_ad_dsp_live_button_click(session_data_->get_spdm_ctx());
      enable_ad_dsp_live_button_click_test =
          SPDM_enable_ad_dsp_live_button_click_test(session_data_->get_spdm_ctx());
      enable_ad_dsp_live_playtime =
          SPDM_enable_ad_dsp_live_playtime(session_data_->get_spdm_ctx());
      enable_ad_dsp_live_lps_playtime =
          SPDM_enable_ad_dsp_live_lps_playtime(session_data_->get_spdm_ctx());
      enable_outer_live_playtime_strategy =
          SPDM_enable_outer_live_playtime_strategy(session_data_->get_spdm_ctx());
      enable_ad_dsp_live_wx =
          SPDM_enable_ad_dsp_live_wx(session_data_->get_spdm_ctx()); // NOLINT
      enable_ad_dsp_live_wx2 =
          SPDM_enable_ad_dsp_live_wx2(session_data_->get_spdm_ctx());
      enable_ad_dsp_live_lps_photo_to_live =
          SPDM_enable_ad_dsp_live_lps_photo_to_live(session_data_->get_spdm_ctx());
      enable_ad_dsp_live_lps_direct_live =
          SPDM_enable_ad_dsp_live_lps_direct_live(session_data_->get_spdm_ctx());
      enable_ad_dsp_item_imp_lps_message_local =
          SPDM_enable_ad_dsp_item_imp_lps_message_local(session_data_->get_spdm_ctx());
      enable_ad_dsp_item_imp_lps_leadssubmit_local =
          SPDM_enable_ad_dsp_item_imp_lps_leadssubmit_local(session_data_->get_spdm_ctx());
      enable_healthcare_single_tower =
          SPDM_enable_healthcare_single_tower(session_data_->get_spdm_ctx());
      enable_adx_thanos_cvr_multihead =
          SPDM_enable_adx_thanos_cvr_multihead(session_data_->get_spdm_ctx());
      enable_conv_nextstay_ecpc =
          SPDM_enable_conv_nextstay_ecpc(session_data_->get_spdm_ctx());  //NOLINT
      enable_cid_order_cross_auc =
          SPDM_enable_cid_order_cross_auc(session_data_->get_spdm_ctx());
      enable_cid_order_live_cross_auc =
          SPDM_enable_cid_order_live_cross_auc(session_data_->get_spdm_ctx());
      enable_cid_order_live_cmdkey_v2 =
          SPDM_enable_cid_order_live_cmdkey_v2(session_data_->get_spdm_ctx());
      enable_mt_cov_exp_tmp =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_mt_cov_exp_tmp", false);
      enable_shallow_unify_sdpa =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_shallow_unify_sdpa", false);
       enable_10b_1009_auc =
           session_data_->get_spdm_ctx().TryGetBoolean("enable_10b_1009_auc", false);
      enable_rewarded_cmd_lps =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_rewarded_cmd_lps", false);
      enable_rewarded_change_lps =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_rewarded_change_lps", false);
      enable_cross_auc_lps =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_cross_auc_lps", false);
      ocpm_cut_by_item_imp_app_invoked =
          session_data_->get_spdm_ctx().TryGetBoolean("ocpm_cut_by_item_imp_app_invoked", false);
      enable_conv_quality_ecpc =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_conv_quality_ecpc", false);
      enable_mt_lps_exp_tmp =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_mt_lps_exp_tmp", false);
      enable_mt_lps_launch =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_mt_lps_launch", false);
      enable_mcda_up_purchase_third_party_ecpc_exp =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_mcda_up_purchase_third_party_ecpc_exp", false);  // NOLINT
      enable_mcda_up_purchase_third_party_ecpc_launch =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_mcda_up_purchase_third_party_ecpc_launch", false);  // NOLINT
      enable_mt_purchase_exp_tmp =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_mt_purchase_exp_tmp", false);
      enable_mt_purchase_launch =
          session_data_->get_spdm_ctx().TryGetBoolean("enable_mt_purchase_launch", false);
      enable_pre_mt_purchase = SPDM_enable_pre_mt_purchase(session_data_->get_spdm_ctx());
    }
    disable_feed_server_show_p3s_model = session_data_->get_spdm_ctx()
        .TryGetBoolean("disable_feed_server_show_p3s_model", false);
    enable_server_show_live_started_feed_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_server_show_live_started_feed_search", false);
    disable_slide_server_show_p3s_model = session_data_->get_spdm_ctx()
        .TryGetBoolean("disable_slide_server_show_p3s_model", false);
    enable_slide_server_show_ctr_search = true;
    enable_server_show_live_started_slide_search = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_server_show_live_started_slide_search", false);
    enable_only_keep_roas = true;
    enable_inner_hard_gpm_score = SPDM_enable_inner_hard_gpm_score(session_data_->get_spdm_ctx()) ||
        (session_data_->get_pos_manager_base().IsGuessYouLike() &&
         SPDM_enable_use_gpm_for_guess_you_like_hard(session_data_->get_spdm_ctx()));
    enable_hc_inner_live_independent_gmv =
        SPDM_enable_hc_inner_live_independent_gmv(session_data_->get_spdm_ctx()); // NOLINT
    enable_roas_live_high_atv = SPDM_enable_roas_live_high_atv(session_data_->get_spdm_ctx());
    enable_esp_live_inspire_gmv = SPDM_enable_esp_live_inspire_gmv(session_data_->get_spdm_ctx());
    enable_live_ltv_use_auc =
        SPDM_enable_live_ltv_use_auc(session_data_->get_spdm_ctx());
    enable_live_audience_single_use_auc =
        SPDM_enable_live_audience_single_use_auc(session_data_->get_spdm_ctx());
    enable_live_audience_feed_use_auc =
        SPDM_enable_live_audience_feed_use_auc(session_data_->get_spdm_ctx());
    enable_context_info_completion =
        SPDM_enable_context_info_completion(session_data_->get_spdm_ctx());
    enable_sv_rank_candidante =
        SPDM_enable_sv_rank_candidante(session_data_->get_spdm_ctx());
    enable_roas_native_use_imp_ltv_switch_new =
        SPDM_enable_roas_native_use_imp_ltv_switch_new(session_data_->get_spdm_ctx());
    enable_live_pay_use_auc =
        SPDM_enable_live_pay_use_auc(session_data_->get_spdm_ctx());
    enable_merchant_roas_use_auc =
        SPDM_enable_merchant_roas_use_auc(session_data_->get_spdm_ctx());
    enable_merchant_t7_roi_use_auc =
        SPDM_enable_merchant_t7_roi_use_auc(session_data_->get_spdm_ctx());
    enable_live_pay_follow_tab_exp =
        SPDM_enable_live_pay_follow_tab_exp(session_data_->get_spdm_ctx());
    enable_p2l_pay_follow_tab_exp =
        SPDM_enable_p2l_pay_follow_tab_exp(session_data_->get_spdm_ctx());
    enable_feed_ctr_fanstop_ad =
        SPDM_enable_feed_ctr_fanstop_ad(session_data_->get_spdm_ctx());
    enable_mobile_live_pay_follow_tab_exp =
        SPDM_enable_mobile_live_pay_follow_tab_exp(session_data_->get_spdm_ctx());
    enable_mobile_p2l_pay_follow_tab_exp =
        SPDM_enable_mobile_p2l_pay_follow_tab_exp(session_data_->get_spdm_ctx());
    enable_live_pay_single_tab =
        SPDM_enable_live_pay_single_tab(session_data_->get_spdm_ctx());
    enable_live_pay_p2l_single_tab =
        SPDM_enable_live_pay_p2l_single_tab(session_data_->get_spdm_ctx());
    enable_uplift_order_paied =
        SPDM_enable_uplift_order_paied(session_data_->get_spdm_ctx());
    enable_native_uplift_order_paied =
        SPDM_enable_native_uplift_order_paied(session_data_->get_spdm_ctx());
    enable_uplift_valid_page =
        SPDM_enable_uplift_valid_page(session_data_->get_spdm_ctx());
    enable_lsp_storewide_flow_gmv_model_ensemble =
        SPDM_enable_lsp_storewide_flow_gmv_model_ensemble(session_data_->get_spdm_ctx());
    enable_lsp_storewide_search_gmv_model_ensemble =
        SPDM_enable_lsp_storewide_search_gmv_model_ensemble(session_data_->get_spdm_ctx());
    enable_sv_roas_gpm_cmd =
        SPDM_enable_sv_roas_gpm_cmd(session_data_->get_spdm_ctx());
    enable_roas_multi_cmd =
        SPDM_enable_roas_multi_cmd(session_data_->get_spdm_ctx());
    enable_live_p2l_thanos_split =
        SPDM_enable_live_p2l_thanos_split(session_data_->get_spdm_ctx());
    enable_p2l_use_auc =
        SPDM_enable_p2l_use_auc(session_data_->get_spdm_ctx());
    enable_live_order_item_live_split =
        SPDM_enable_live_order_item_live_split(session_data_->get_spdm_ctx());
    enable_inner_live_atomization_order =
        SPDM_enable_inner_live_atomization_order(session_data_->get_spdm_ctx());
    enable_inner_live_atomization_roas =
        SPDM_enable_inner_live_atomization_roas(session_data_->get_spdm_ctx());
    enable_inner_live_atomization_net_roas =
        SPDM_enable_inner_live_atomization_net_roas(session_data_->get_spdm_ctx());
    enable_inner_live_atomization_switch =
        SPDM_enable_inner_live_atomization_switch(session_data_->get_spdm_ctx());
    enable_inner_live_atomization_t7_switch =
        SPDM_enable_inner_live_atomization_t7_switch(session_data_->get_spdm_ctx());
    enable_inner_live_atomization_t7roas =
        SPDM_enable_inner_live_atomization_t7roas(session_data_->get_spdm_ctx());
    enable_inner_live_qcpx_order =
        SPDM_enable_inner_live_qcpx_order(session_data_->get_spdm_ctx());
    enable_inner_live_qcpx_roas =
        SPDM_enable_inner_live_qcpx_roas(session_data_->get_spdm_ctx());
    enable_inner_live_qcpx_t7roas =
        SPDM_enable_inner_live_qcpx_t7roas(session_data_->get_spdm_ctx());
    enable_roas_gpm_cmd =
        SPDM_enable_roas_gpm_cmd(session_data_->get_spdm_ctx());
    enable_roas_7days_cmd =
        SPDM_enable_roas_7days_cmd(session_data_->get_spdm_ctx());
    enable_roas_7days_0_2h_cmd =
        SPDM_enable_roas_7days_0_2h_cmd(session_data_->get_spdm_ctx());
    enable_roas_7days_2h_3d_cmd =
        SPDM_enable_roas_7days_2h_3d_cmd(session_data_->get_spdm_ctx());
    enable_roas_7days_3d_7d_cmd =
        SPDM_enable_roas_7days_3d_7d_cmd(session_data_->get_spdm_ctx());
    enable_roas_7days_relative_predict_cmd =
            SPDM_enable_roas_7days_relative_predict_cmd(session_data_->get_spdm_ctx());
    enable_shelf_live_order_pltv_model_exp =
            SPDM_enable_shelf_live_order_pltv_model_exp(session_data_->get_spdm_ctx());

    // amd 短视频队列
    enable_item_card = SPDM_enable_item_card(session_data_->get_spdm_ctx());  // NOLINT
    enable_fans_follow_use_auc =
        SPDM_enable_fans_follow_use_auc(session_data_->get_spdm_ctx());
    enable_neixunhuan_photo_roi_cvr_reset = session_data_->get_spdm_ctx()
        .TryGetBoolean("enable_neixunhuan_photo_roi_cvr_reset", false);
    enable_hc_gpm_order_pay_independent = SPDM_enable_hc_gpm_order_pay_independent(session_data_->get_spdm_ctx());  // NOLINT
    enable_merchant_video_auc_cross_test =
        SPDM_enable_merchant_video_auc_cross_test(session_data_->get_spdm_ctx());
    enable_reco_follow_use_auc_exp =
        SPDM_enable_reco_follow_use_auc_exp(session_data_->get_spdm_ctx());
    enable_merchant_photo_roas_cvr_auc_cross_test =
        SPDM_enable_merchant_photo_roas_cvr_auc_cross_test(session_data_->get_spdm_ctx());
    // 外循环行业直播
    enable_dsplive_direct_conv_bypass_auc =
        SPDM_enable_dsplive_direct_conv_bypass_auc(session_data_->get_spdm_ctx());
    enable_dsplive_direct_lps_bypass_auc =
        SPDM_enable_dsplive_direct_lps_bypass_auc(session_data_->get_spdm_ctx());
    enable_dsplive_ctr_bypass_auc =
        SPDM_enable_dsplive_ctr_bypass_auc(session_data_->get_spdm_ctx());
    enable_dsplive_p2l_conv_bypass_auc =
        SPDM_enable_dsplive_p2l_conv_bypass_auc(session_data_->get_spdm_ctx());
    enable_dsplive_p2l_lps_bypass_auc =
        SPDM_enable_dsplive_p2l_lps_bypass_auc(session_data_->get_spdm_ctx());
    enable_dsplive_p2l_ctr_bypass_auc =
        SPDM_enable_dsplive_p2l_ctr_bypass_auc(session_data_->get_spdm_ctx());
    enable_real_deep_wechat_connected_lps_cvr_set =
        SPDM_enable_real_deep_wechat_connected_lps_cvr_set(session_data_->get_spdm_ctx());
    // 直播涨粉
    enable_inner_live_follow_rm_invalid_request_2 =
        SPDM_enable_inner_live_follow_rm_invalid_request_2(session_data_->get_spdm_ctx());
    enable_model_explore =
        SPDM_enable_model_explore(session_data_->get_spdm_ctx());
    enable_mobile_photo_soft_model =
        SPDM_enable_mobile_photo_soft_model(session_data_->get_spdm_ctx());
    enable_lsp_photo_order_paied_model =
        SPDM_enable_lsp_photo_order_paied_model(session_data_->get_spdm_ctx());
    enable_order_submit_gpm_use_ltv_model =
        SPDM_enable_order_submit_gpm_use_ltv_model(session_data_->get_spdm_ctx());
    enable_photo_goods_search =
        SPDM_enable_photo_goods_search(session_data_->get_spdm_ctx());
    enable_live_pay_rate_goods_search =
        SPDM_enable_live_pay_rate_goods_search(session_data_->get_spdm_ctx());
    enable_card_live_T7 =
        SPDM_enable_card_live_T7(session_data_->get_spdm_ctx());
    enable_goods_p2l =
        SPDM_enable_goods_p2l(session_data_->get_spdm_ctx());
    enable_order_submit_cold_split_cmd =
        SPDM_enable_order_submit_cold_split_cmd(session_data_->get_spdm_ctx());
    enable_cid_roi_imp_gmv_cmd_fix_auc_exp =
        SPDM_enable_cid_roi_imp_gmv_cmd_fix_auc_exp(session_data_->get_spdm_ctx());
    enable_cid_search_boost_exp = SPDM_enable_cid_search_boost_exp(session_data_->get_spdm_ctx());
    cid_search_boost_exp_tag = SPDM_cid_search_boost_exp_tag(session_data_->get_spdm_ctx());
    enable_use_new_order_pay_cmd_for_guess_you_like =
        SPDM_enable_use_new_order_pay_cmd_for_guess_you_like(session_data_->get_spdm_ctx());
    enable_use_new_order_paied_cmd_for_mall =
        SPDM_enable_use_new_order_paied_cmd_for_mall(session_data_->get_spdm_ctx());
    enable_use_new_order_paied_cmd_for_zhuanqian =
        SPDM_enable_use_new_order_paied_cmd_for_zhuanqian(session_data_->get_spdm_ctx());
    enable_use_new_order_paied_cmd_for_buyer_home =
        SPDM_enable_use_new_order_paied_cmd_for_buyer_home(session_data_->get_spdm_ctx());
    enable_shelf_photo_cmd_fix =
        SPDM_enable_shelf_photo_cmd_fix(session_data_->get_spdm_ctx());
    enable_order_paied_cvr_pos_cmd_for_gyl =
        SPDM_enable_order_paied_cvr_pos_cmd_for_gyl(session_data_->get_spdm_ctx());
    enable_order_paied_cvr_pos_cmd_for_bh =
        SPDM_enable_order_paied_cvr_pos_cmd_for_bh(session_data_->get_spdm_ctx());
    enable_order_paied_cvr_pos_cmd_for_mall =
        SPDM_enable_order_paied_cvr_pos_cmd_for_mall(session_data_->get_spdm_ctx());
    enable_inner_photo_shelf_ctr_cmd =
        SPDM_enable_inner_photo_shelf_ctr_cmd(session_data_->get_spdm_ctx());
    ad_predict_stat_ptr_ = nullptr;
    auto pos_id = session_data_->get_pos_manager_base().GetMediumPosId();
    auto page_id = session_data_->get_page_id();
    if (pos_id == 25994 || pos_id == 25993 || pos_id == 34421 || pos_id == 34420 ||
        pos_id == 37323 || pos_id == 37324 || pos_id == 27217 || pos_id == 27218 ||
        pos_id == 79115 || pos_id == 79116) {
        iaa_acq_gen_page_flag_ = true;
    }
    if (pos_id == 34498 || pos_id == 34499 || pos_id == 34626 || pos_id == 34627 ||
        pos_id == 35080 || pos_id == 35079 || pos_id == 35078 || pos_id == 35077 ||
        pos_id == 53101 || pos_id == 53100 || pos_id == 83286 || pos_id == 83289) {
        iaa_acq_gen_page_flag_ = true;
    }
    if (page_id == 13001 || page_id == 100012264 || page_id == 100012266 ||
        page_id == 100039328 || page_id == 100073111 || page_id == 100073121) {
        iaa_acq_gen_page_flag_ = true;
    }
  }

  absl::flat_hash_map<std::string, kuaishou::ad::RankStageInfo::AdPredictStat>*
    ad_predict_stat_ptr_ = nullptr;
  void SetAdPredictStat(absl::flat_hash_map<std::string, kuaishou::ad::RankStageInfo::AdPredictStat>* ptr) {
    ad_predict_stat_ptr_ = ptr;
  }
  absl::flat_hash_map<std::string, kuaishou::ad::RankStageInfo::AdPredictStat>* ad_predict_stat_ptr() {
    return ad_predict_stat_ptr_ != nullptr ? ad_predict_stat_ptr_ : session_data_->ad_predict_stat_ptr();
  }
  bool IsDetailNebulaTraffic() {
    return false;
  }

  bool IsDetailThanosTraffic() {
    return false;
  }

  bool isEcpcRegisterAd(const AdCommon* ad) {
    return outside_ecpc_regiester_set_ != nullptr
           && ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION
           && outside_ecpc_regiester_set_->count(ad->get_product_name()) > 0;
  }

  bool IsOcpc2Unit(const AdCommon *ad) {
    return ((session_data_->get_is_thanos_request() || session_data_->get_is_rewarded_merchant())
             && (IS_OCPX(ad->get_bid_type()) || ad->get_bid_type() == kuaishou::ad::AdEnum::CPA));
  }

  bool EnableInovTrafficPredict() {
    const auto& invoTrafficModelSubPageIdConf = RankKconfUtil::invoTrafficModelSubPageIdConf();
    int64_t sub_page_id = session_data_->get_pos_manager_base().GetSubPageId();
    if (invoTrafficModelSubPageIdConf->count(sub_page_id) > 0) {
      return true;
    }
    return false;
  }

  bool IsAdxLargeCreatives(const AdCommon *ad) {
    return ad->get_ad_source_type() == kuaishou::ad::ADX && ad->get_large_amount_creative() &&
           enable_adx_large_creatives_model_;
  }

  bool IsMerchantPlatformInvokedAd(const AdCommon *ad) {
    return ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED
           && merchant_platform_hc_conf_gpm_ != nullptr
           && merchant_platform_hc_conf_gpm_->app_invoked().find(ad->get_product_name())
           != merchant_platform_hc_conf_gpm_->app_invoked().end();
  }

  bool IsMerchantPlatformConversiondAd(const AdCommon* ad) {
    return ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION
           && merchant_platform_hc_conf_gpm_ != nullptr
           && merchant_platform_hc_conf_gpm_->ad_conversion().find(ad->get_product_name())
           != merchant_platform_hc_conf_gpm_->ad_conversion().end();
  }
  bool IsMerchantPlatformPaytimesAd(const AdCommon* ad) {
    return (ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION
    || ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED
    || ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE)
    && merchant_platform_hc_conf_gpm_ != nullptr
    && merchant_platform_hc_conf_gpm_->ad_paytimes().find(ad->get_product_name())
    != merchant_platform_hc_conf_gpm_->ad_paytimes().end();
  }
  bool IsCidMcdaAd(const AdCommon* ad) {
    return ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
    && direct_merchant_mcda_conf_ != nullptr
    && direct_merchant_mcda_conf_->data().admit().find(ad->get_product_name())
    != direct_merchant_mcda_conf_->data().admit().end();
  }

  bool IsCidSearchAd(const AdCommon* ad) {
    return ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
    && direct_merchant_search_conf_ != nullptr
    && (direct_merchant_search_conf_->data().admit().find(absl::StrCat(ad->get_product_name(),
                  "#", cid_search_boost_exp_tag))
    != direct_merchant_search_conf_->data().admit().end()
    || direct_merchant_search_conf_->data().admit_account().find(absl::StrCat(ad->get_account_id(),
                  "#", cid_search_boost_exp_tag))
    != direct_merchant_search_conf_->data().admit_account().end());
  }

  bool IsEspLiveAtvTail(const AdCommon* ad) {
    if (esp_live_roas_atv_tail == nullptr || esp_live_hosting_roas_atv_tail == nullptr ||
        esp_live_storewide_roas_atv_tail == nullptr) {
      return false;
    } else {
      bool is_esp_live_atv_tail = !session_data_->get_is_search_request()
      &&((esp_live_roas_atv_tail->count(ad->get_unit_id() % 100) || enable_esp_live_roas_ensemble2) &&
        !disable_esp_live_roas_ensemble)
      && ((ad->get_bid_strategy() == 1 &&
       ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE)
       || ad->get_speed() == 1)
       && ad->get_scene_oriented_type() != kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT;
      bool is_esp_live_hosting_atv_tail = !session_data_->get_is_search_request()
      && esp_live_hosting_roas_atv_tail->count(ad->get_campaign_id() % 100)
      && ((ad->get_bid_strategy() == 1 &&
       ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE)
       || ad->get_speed() == 1)
       && ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT;
      bool is_esp_live_storewide_atv_tail = !session_data_->get_is_search_request()
      && (esp_live_storewide_roas_atv_tail->count(ad->get_campaign_id() % 100) ||
          enable_live_storewide_roas_ensemble)
      && ((ad->get_bid_strategy() == 1 &&
       ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE)
       || ad->get_speed() == 1)
       && ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE;
      return is_esp_live_atv_tail || is_esp_live_hosting_atv_tail || is_esp_live_storewide_atv_tail;
    }
  }

  bool IsLspLiveAtv(const AdCommon* ad) {
    return !session_data_->get_is_search_request() &&
           ad->get_speed() == 1 &&
           ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE &&
           ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS;
  }

  bool IsLspStorewideFlowGMVModelEnsemble(const AdCommon* ad) {
    return enable_lsp_storewide_flow_gmv_model_ensemble &&
           !session_data_->get_is_search_request() &&
           ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE;
  }
  bool IsLspStorewideSearchGMVModelEnsemble(const AdCommon* ad) {
    return enable_lsp_storewide_search_gmv_model_ensemble &&
           session_data_->get_is_search_request() &&
           ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE;
  }
};

using CommandPredictType = std::function<bool(const AdCommon*, CmdCuratorContext*)>;

class CmdCurator {
  struct CmdItemsMapping {
    bool from_config {false};
    CmdWrapper* cmd_info {nullptr};
    CommandPredictType cpt = [](const AdCommon*, CmdCuratorContext*) {
      return false;
    };

    std::unordered_set<uint64_t> item_id_set;
  };

 private:
  int64_t total_skip_times_{0};
  int64_t total_repeated_times_{0};
  std::vector<int64_t> pt_2_register_cmd_key_total_;
  std::unordered_map<int64_t, std::unordered_map<std::string, int64_t>> pt_2_conflict_cmd_keys_;
  std::unordered_map<int32_t, std::string> req_cmd_id_2_str;  // 本次请求的 <cmd_id, cmd>

  std::unordered_set<std::string> register_cmd_keys_;
  std::vector<CmdItemsMapping> cmd_list_;
  std::unique_ptr<CmdCuratorContext> p_cmd_curator_context_;
  engine_base::CmdRegister<AdCommon, CmdCuratorContext> *cmd_register_{nullptr};
  std::unordered_map<uint64_t, std::vector<std::pair<CmdWrapper *, int64_t>>> item_2_cmd_wrappers_;
  std::unordered_map<uint64_t, absl::flat_hash_map<int64_t, int64_t>> item_cmd_2_wrapper_indexs_;
  std::unordered_set<std::string> pv_not_accept_cmd_keys_;
  std::string node_name_;
  std::unordered_map<int64_t, CmdItemsMapping*> cmd_id_2_cmd_info;
  std::unordered_map<uint64_t, AdCommon*> item_id_2_ad_map;  // <cid, p_ad>
  std::unordered_set<uint64_t> inserted_item_set_;
  std::unordered_map<std::string, CmdItemsMapping*> cmd_key_2_cmd_info_;

 private:
  void RunPvLevelCallback();
  bool AcceptCmdPattern(const std::string &cmd_key,
                        const std::vector<engine_base::CmdPatternInner> &);

 public:
  explicit CmdCurator(const std::string &node_name)
     : cmd_register_(engine_base::CmdRegister<AdCommon, CmdCuratorContext>::GetSingleton()),
       pt_2_register_cmd_key_total_(engine_base::PredictType::PredictType_max, 0),
       node_name_(node_name) {
  }
  ~CmdCurator() {
    int64_t total_call_times = total_skip_times_ + total_repeated_times_;
    for (auto& cmd : cmd_list_) {
      delete cmd.cmd_info;
      cmd.cmd_info = nullptr;
    }
    cmd_key_2_cmd_info_.clear();
    item_id_2_ad_map.clear();
    cmd_id_2_cmd_info.clear();
  }

  void Reset(std::unique_ptr<CmdCuratorContext> cmd_curator_context) {
    p_cmd_curator_context_.swap(cmd_curator_context);
    for (auto& cmd : cmd_list_) {
      cmd.item_id_set.clear();
    }
    cmd_key_2_cmd_info_.clear();
    cmd_id_2_cmd_info.clear();
  }

  // cmd 重构填充 UniversePredictRequest
  int PrepareCmds(const ContextData* p_context_data,
                  const std::string& node_name);
  int FillPredictRequest(const std::string& node_name,
                         kuaishou::ad::algorithm::UniversePredictRequest* request,
                         kuaishou::ad::algorithm::RequestScene scene,
                         bool use_request_v3 = false);
  // 用来取代 FillPredictResultMap
  int FillPredictResult(const kuaishou::ad::algorithm::UniversePredictResponse& response);
  int FillPredictResultV2(const kuaishou::ad::algorithm::UniversePredictResponse& response);

  // cmd 重构填充 PredictMap
  int FillPredictResultMap(const kuaishou::ad::algorithm::UniversePredictResponse& response,
                           std::unordered_map<int64, PredictResult>* predict_map);
  int FillPredictResultMapV2(const kuaishou::ad::algorithm::UniversePredictResponse& response,
                           std::unordered_map<int64, PredictResult>* predict_map);

  void CmdResultStatMonitor(const kuaishou::ad::algorithm::UniversePredictResponse& response);
  bool RegisterCmd(CmdWrapper*, CommandPredictType);
  int Accept(AdCommon*);
  int Accept(AdCommon*, int64_t);

  void Debug() const;
  void CmdRequestStatMonitor() const;
  int64 GetPsRequestItemSize() const;   // 本次 pv 请求 ps 去重后的 item 数量

  std::vector<std::pair<CmdWrapper *, int64_t>> GetAdCmds(int64_t item_id) const;
  std::string GetAdPredictStatKey(int32 cmd_id, int32 pt, int32 cmd_key_id);

  const std::vector<CmdItemsMapping>& GetCmdItemsMaping() {
    return cmd_list_;
  }
};

}  // namespace ad_rank
}  // namespace ks
