#include "teams/ad/ad_rank/${udf_dir}/${name_snake}.h"

namespace ks::ad_rank {
bool ${name_camel}::Initialize() {
  // pv 级初始化；return false 则该次请求不生效此 udf
  return true;
}

bool ${name_camel}::IsAdmit(const AdCommon* p_ad) {
  // return false 则该条广告不生效此 udf
  return false;
}

int64_t ${name_camel}::Compute(const AdCommon* p_ad) {
  // 返回 ecpm_lower_bound 值；多个策略取 max
  return INT64_MIN;
}

REGISTER_FACTOR_UDF(${name_camel}, int64_t)
}  // namespace ks::ad_rank
