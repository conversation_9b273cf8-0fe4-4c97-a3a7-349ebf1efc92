#include "teams/ad/ad_rank/${udf_dir}/${name_snake}.h"

namespace ks::ad_rank {
bool ${name_camel}::Initialize() {
  // pv 级初始化；return false 则该次请求不生效此 udf
  return true;
}

bool ${name_camel}::IsAdmit(const AdCommon* p_ad) {
  // return false 则该条广告不生效此 udf
  return false;
}

int64_t ${name_camel}::Compute(const AdCommon* p_ad) {
  // 返回 ecpm_adjust 值；
  // 策略计算在原始 ecpm 之后，可以通过 p_ad->get_auction_bid() 获取原始 ecpm；
  // 多个策略累加；这里获取的原始 ecpm 是所有 ecpm_adjust 累加前的值。
  return 0;
}

REGISTER_FACTOR_UDF(${name_camel}, int64_t)
}  // namespace ks::ad_rank
