from argparse import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RawDescriptionHelpFormatter
from typing import Li<PERSON>, <PERSON><PERSON>, TypedDict, Dict
from pathlib import Path, PurePath
import re
from string import Template

class StrategyConfig(TypedDict):
    tag_class: str
    dsl_module_file: str
    udf_dir: str

STRATEGY_CONFIGS: Dict[str, StrategyConfig] = {
    "server_client_show_rate": {
        "tag_class": "ServerCientShowRateTag",
        "dsl_module_file": "server_show_factor.py",
        "udf_dir": "processor/factor/server_client_show_rate_strategy"
    },
    "has_deep_cvr": {
        "tag_class": "HasDeepCvrTag",
        "dsl_module_file": "deep_cvr_factor.py",
        "udf_dir": "processor/factor/cxr/has_deep_cvr_strategy"
    },
    "ecpm_upper_bound": {
        "tag_class": "EcpmUpperBound",
        "dsl_module_file": "cpm_factor.py",
        "udf_dir": "processor/factor/ecpm/ecpm_upper_bound"
    },
    "ecpm_lower_bound": {
        "tag_class": "EcpmLowerBound",
        "dsl_module_file": "cpm_factor.py",
        "udf_dir": "processor/factor/ecpm/ecpm_lower_bound"
    },
    "ecpm_adjust": {
        "tag_class": "EcpmAdjust",
        "dsl_module_file": "ecpm_adjust_factor.py",
        "udf_dir": "processor/factor/ecpm/ecpm_adjust"
    }
}

class Args:
    name: str
    name_snake: str
    name_camel: str
    tag: str
    owner: str
    strategy: str

    ROOT = Path(__file__).parent.parent.parent
    CUR_DIR = Path(__file__).parent
    TAG_PATH = ROOT / "skydome/module/enum/factor_type_enum.py"
    UDF_TEMPLATE_PATH = CUR_DIR / "templates/factor_udf.py.template"

    @property
    def cc_file_path(self) -> Path:
        return self.udf_dir / f"{self.name_snake}.cc"

    @property
    def h_file_path(self) -> Path:
        return self.udf_dir / f"{self.name_snake}.h"

    @property
    def tag_class(self) -> str:
        return STRATEGY_CONFIGS[self.strategy]["tag_class"]

    @property
    def dsl_module_path(self) -> Path:
        return self.ROOT / "skydome/module/factor" / STRATEGY_CONFIGS[self.strategy]["dsl_module_file"]

    @property
    def udf_dir(self) -> Path:
        return self.ROOT / STRATEGY_CONFIGS[self.strategy]["udf_dir"]

    @property
    def rel_udf_dir(self) -> str:
        return STRATEGY_CONFIGS[self.strategy]["udf_dir"]

    @property
    def cc_template_path(self) -> Path:
        return self.CUR_DIR / f"templates/{self.strategy}.cc.template"

    @property
    def h_template_path(self) -> Path:
        return self.CUR_DIR / f"templates/{self.strategy}.h.template"

def get_args() -> Args:
    parser = ArgumentParser(
        description="添加策略 udf",
        formatter_class=RawDescriptionHelpFormatter
    )
    parser.add_argument("-s", "--strategy", required=True, choices=list(STRATEGY_CONFIGS.keys()), help="策略类型")
    parser.add_argument("-n", "--name", required=True, help="策略名称")
    parser.add_argument("-o", "--owner", required=True, help="策略owner")
    parser.add_argument("-t", "--tag", required=False, help="策略tag，默认为策略名称")
    args = Args()
    parser.parse_args(namespace=args)
    args.name_camel, args.name_snake = get_names(args.name)
    if not args.tag:
        args.tag = args.name_camel
    return args

def get_names(name: str) -> Tuple[str, str]:
    def to_snake_case(s: str) -> str:
        s = re.sub(r'(?<!^)(?=[A-Z])', '_', s).lower()
        return s

    def to_camel_case(s: str) -> str:
        parts = s.split('_')
        return ''.join(word.capitalize() for word in parts)

    if '_' in name:
        snake = name.lower()
        camel = to_camel_case(snake)
    else:
        camel = name[0].upper() + name[1:]
        snake = to_snake_case(camel)

    return camel, snake

def find_line(file_path: Path, line_pattern: str) -> int:
    with file_path.open("r", encoding="utf-8") as file:
        lines = file.readlines()

    for i, line in enumerate(lines):
        if re.match(line_pattern, line):
            return i

    raise ValueError(f"{file_path} 中未找到匹配的行模式: {line_pattern}")

def insert_line(file_path: Path, line: int, content: str) -> None:
    with file_path.open("r+", encoding="utf-8") as file:
        lines = file.readlines()
        lines.insert(line, content + "\n")  # 在指定行号上方插入内容
        file.seek(0)
        file.writelines(lines)

def read_line(file_path: Path, line: int) -> str:
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()
            return lines[line].rstrip("\n") if 0 <= line < len(lines) else ""
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
        return ""
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return ""

def gen_cc_file(args: Args) -> None:
    with open(args.cc_template_path, 'r') as template_file:
        template = Template(template_file.read())
    content = template.substitute(
        name_camel=args.name_camel,
        name_snake=args.name_snake,
        udf_dir=args.rel_udf_dir
    )
    with open(args.cc_file_path, 'w') as cc_file:
        cc_file.write(content)
    print(f"generate: {args.cc_file_path}")

def gen_h_file(args: Args) -> None:
    with open(args.h_template_path, 'r') as template_file:
        template = Template(template_file.read())
    content = template.substitute(
        name_camel=args.name_camel
    )
    with open(args.h_file_path, 'w') as h_file:
        h_file.write(content)
    print(f"generate: {args.h_file_path}")

def modify_dsl_module(args: Args) -> None:
    with open(args.UDF_TEMPLATE_PATH, 'r') as template_file:
        template = Template(template_file.read())
    content = template.substitute(
        name_camel=args.name_camel,
        tag=args.tag,
        owner=args.owner,
        tag_class=args.tag_class
    )
    line = find_line(args.dsl_module_path, rf".*# ⬆️ {args.strategy} 策略在此注册")
    insert_line(args.dsl_module_path, line, content)
    print(f"append: {args.dsl_module_path}")

def add_tag(args: Args) -> None:
    line = find_line(args.TAG_PATH, rf".*# ⬆️ {args.strategy} 策略 tag")
    last_tag_str = read_line(args.TAG_PATH, line - 1)
    last_tag = int(last_tag_str.split("=")[1].strip())
    content = f"  {args.tag} = {last_tag + 1}"
    insert_line(args.TAG_PATH, line, content)
    print(f"append: {args.TAG_PATH}")

if __name__ == "__main__":
    args = get_args()
    print(f"add {args.strategy} 策略: {args.name_camel}({args.name_snake}) in directory {args.udf_dir}.")
    gen_h_file(args)
    gen_cc_file(args)
    modify_dsl_module(args)
    add_tag(args)