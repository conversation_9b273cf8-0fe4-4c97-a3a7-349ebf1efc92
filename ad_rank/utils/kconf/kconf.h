#pragma once
#ifndef IMPL_BLOCK_BEGIN        /* auto generated by reset_kconf.py, you can ignore. */
  #define IMPL_BLOCK_BEGIN 0    /* auto generated by reset_kconf.py, you can ignore. */
#endif                          /* auto generated by reset_kconf.py, you can ignore. */
#ifndef IMPL_BLOCK_END          /* auto generated by reset_kconf.py, you can ignore. */
  #define IMPL_BLOCK_END 10000  /* auto generated by reset_kconf.py, you can ignore. */
#endif                          /* auto generated by reset_kconf.py, you can ignore. */

#include <string>

#include "base/common/basic_types.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_cmd_proxy.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_0.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_1.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_2.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_3.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_4.pb.h"
#include "teams/ad/engine_base/kconf/app_version_control.h"
#include "teams/ad/engine_base/kconf/kconf.h"

// 实现 cc 中带实现， header 中只带声明
#ifndef KCONF_CC_WITH_IMPL
  #undef  DEFINE_KCONF_NODE
  #define DEFINE_KCONF_NODE(type, config_path, config_key, default_value)                    \
    API_KCONF_NODE(type, config_path, config_key, default_value)

  #undef DEFINE_KCONF_NODE_LOAD
  #define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key)                              \
    API_KCONF_NODE_LOAD(type, config_path, config_key)

  #undef DEFINE_SET_NODE_KCONF
  #define DEFINE_SET_NODE_KCONF(type, config_path, config_key)                                  \
    API_SET_NODE_KCONF(type, config_path, config_key)
  #undef DEFINE_KCONF_MAP_KCONF
  #define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
    API_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key)
#else
  #undef DEFINE_KCONF_NODE_ATTR
  #define DEFINE_KCONF_NODE_ATTR __attribute__ ((used))
#endif

namespace ks {
namespace ad_rank {

struct AppPageFormMaterialCpmThrConfig;
struct NearbyCpmThrConfig;
struct PoQuanPidStrategyKconf;
struct OuterLoopCalibratedRatioForPrerank;
struct IndustryGatherEcpcConfig;
struct SysDftConfigParam;
struct ShallowSortUnitConfig;
struct NebulaExpTradeCpmThrConfig;
struct CtrThrConfig;
struct NewSdkAccountConfig;
struct CpmBonusWhitelistConfig;
struct ThanosExpTradeCpmThrConfig;
struct ThanosFSExpTradeCpmThrConfig;
struct ThanosInnerFlowExpTradeCpmThrConfig;
struct ExpTradeCpmThrConfig;
struct MinbidConfig;
struct AdFlowUeqRatioConfig;
struct AdFlowCpmThrConfig;
struct AdFlowCxrThrConfig;
struct GdtAdCompanyCpmThrConfig;
struct ExpProductCpmThrConfig;
struct ExpAccountCpmThrConfig;
struct ExpAccountMinbidConfig;
struct OnlineCpmThrConfig;
struct CtrCvrThrDiscountConfig;
struct NewCreativeFeedParamsConfig;
struct NewCreativeFeedParentIdParamsConfig;
struct NewCreativeFeedDynamicBonusParamsConfig;
struct NewCreativeFeedDynamicParamsConfig;
struct NearbyRankAdmitWhiteList;
struct MinBidWhiteList;
struct CreditGrantDeepCpaConfig;
struct SocialIndustryExploreData;
struct McdaUpModelEcpcWhitelistData;
struct McdaUpModelEcpcLaunchWhitelistData;
struct InnerRecruimentPlcTailExpConfig;
struct MultiRewardedCoinDataList;
struct IncentiveAdUnifyCalcCoinUpperList;
struct MultiIncntvAdUnifyCalcCoinMdpLtvKeyList;
struct DeepRewardedCoinDataList;
struct RewardedCoinScalingByAccountList;
struct MultiRewardedCoinSearchInspireAdBoxDataList;
struct DeepRewardedCoinSearchInspireAdBoxDataList;
struct CorporationCeilingWhiteListConfig;
struct SearchAdBoxSingleColOcpxExpBoost;
struct SearchAdBoxSingleColProductExpBoost;
struct SearchAdBoxSingleColRequestSceneExpBoost;
struct SearchAdBoxSingleColItemTypeExpBoost;
struct SearchInspirePvAdmitConfig;
struct SearchInspireDeepRewardConfig;
struct IncntvAdPredictNext1ViewValue2Coef;
struct MultiIncntvAdUnifyCalcCoinMdpLtvKeyValueList;
struct IaaAcqGenStruct;

using namespace ks::ad_rank::kconf;    //  NOLINT

class RankKconfUtil {
 public:
  // set<int64>
#if 0 >= IMPL_BLOCK_BEGIN and 0 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT32_KCONF_NODE(ad.adRank, rankTopKCountK, 0);
  DEFINE_INT32_KCONF_NODE(ad.adRank, max_refresh_count_filter_ad_map_size, 120);
  DEFINE_INT32_KCONF_NODE(ad.adRank, max_refresh_count_filter_ad_set_size, 120);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, refreshCountPosIdSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, degradeAbtestWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, newCreativeAckKafkaLlsidSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, retentionAccountList)  // 调用次留模型的 AccountID List
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, purchaseAccountList)   // 调用付费模型的 AccountID List
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, specialLpsUnits)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, blackRealItemIdSet)     // outdate maybe
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, adxJdCtrRatioUnitList)  // outdate maybe
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, aliOuterDeliveryAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, aliOuterAbLearningAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, rewardedCloseClientCpmCvrAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspirePecStylePageIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, reco_htr_filter_whitelist_pos)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireConvStyleSubPageIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireInvokedStyleSubPageIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireStyleMainMixAdSubPageIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireCoinPecStyleSubPageIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, BilingSeparateOcpx)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, BilingSeparateDeep)
  DEFINE_HASH_SET_NODE_KCONF(std::string,  ad.adRank, indRetargetStaList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, industryHcProductSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, ColdStartWechatProductName)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget2, outerloopNcProductSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget3, xifanProductWhitelistSet);  // 喜番免费短剧产品白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget3, jumpOutOcpcShieldListV2);  // 外跳打压转化类型
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget2, outerloopNcProductSetV2);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, outerloopNcRetrievalSet);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, outerloopNcRetrievalSetV2);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, outerloopNcOcpxSet);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adRank3, outerloopResFocusedOcpxSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, outerloopNcGoalOcpxSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, newPhotoOcpcCommit);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adRank2, nearbyFilterOcpxSet);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adRank2, nearbyFilterPageId);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, nearbyUserGroupLevelConfig);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adRank2, nearbyCpmPageIdConfig);
  DEFINE_PROTOBUF_NODE_KCONF(OuterloopNcMaxOcpxBlackSetStruct, ad.adRank2, OuterloopNcMaxOcpxBlackSet);
  DEFINE_PROTOBUF_NODE_KCONF(OuterloopNcMaxWlevelConfigStruct, ad.adRank2, OuterloopNcMaxWlevelConfig);
  DEFINE_PROTOBUF_NODE_KCONF(OuterLoopAcIndustryStruct, ad.adtarget3, OuterLoopAcIndustry);
  DEFINE_PROTOBUF_NODE_KCONF(EcpmUnifyBoundConfig, ad.adRank3, ecpmUnifyBoundConfig);
  DEFINE_PROTOBUF_NODE_KCONF(OuterSelfServiceEcpcConfigStruct, ad.adDmp, adStarorbitEcpcConfig);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, outerloopNcMaxProductBlackList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adData, adBackendConversionAutoBlockProducts);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerloopDncLtv);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerloopRoasDncLtvRatio);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerloopCCIndDncLtv);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerLoopDncLtvLgbm3f);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerloopDncCemExploreParam);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerloopBoostRatioExplore);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerloopBoostRatioExploreV2);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerloopBoostRatioExploreV3);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerloopDncEEParam);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerloopDncCemExploitParam);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerloopNcProductMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerloopNcRetrievalMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerloopAcCvrThreshold);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, pinpaizhutuiUnitCvrThres);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, pinpaizhutuiOcpxCvrThres);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerloopNcRoasPosteriorPurchaseRate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerloopNcRoasLtvRatio);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, BrandFanstopOptPcxrConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, BrandFanstopOptHcConfig);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, outerloopIndDncList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, outerloopContentConsumptionIndList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, outerloopIndDncOcpxList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, outerloopDncRoasOcpxSet);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerloopIndDncHcConf);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerloopContentConsumptionHcConf);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enableFanstopBrandProfitWhitelist, false);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.bidServer3, fanstopBrandProfitWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, outerloopMacOcpxSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, outerloopMacIndSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, outerloopAcRetrievalSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, outerloopAcProductSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, outerloopLowActiveOcpxBlackSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, industryHcAccountSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, industryHcRtaSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, ocpcActionTypesWithIncentiveCpmBound);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, bonusAccountTypeSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, springFestivalHealthAccountWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, bonusCampaignTypeSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, RewardedCoinSubPageidList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, subPageIdsForGrossMaximization)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, subPageIdsForRecoverServerCoin)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, bidTypesForGrossMaximization)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, adPhoto2liveUnitTailSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, sevenDayStayTwinAccountWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, conversionNextdayStayEcpmBlackList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, conversionNextdayStayEcpmWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, WeekRetentionEcpcAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, adxForceWhiteUserSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, PenicillinConvPurchaseWhitelist)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, priceSeparateTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, priceSeparateTailCreditGrant)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, priceSeparateAccount)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, meituanX18accounts)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, EcomConvNoEnsembleAccounts)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, EcomConvNoEnsembleProductNames)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, priceSeparateOcpcActionType)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, liveAudienceExcludeSubPageIdList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, merchantMcbUnitWhiteList)  // mcb 暗投白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, roiMcbUnitTail1)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, sevenDaysPayTimesAccountSet)  // 七日出价系列开关
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, storeNoT7ConvertCampaignTail)  // 全站暗投 T7 不用系数
  DEFINE_SET_NODE_KCONF(int64, firefly.bidServer, accountEvent7DaySet);  // 七日出价白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, firefly.bidServer, accountBackflowSet);  // 回流预估加白
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, costCapV3PayerTailSet)  // 按照尾号区分不同调价策略 payer
  DEFINE_HASH_SET_NODE_KCONF(
    int64, ad.bidServer, costCapV3PayerRoiTailSet)  // 按照尾号区分不同调价策略 payer roi
  DEFINE_HASH_SET_NODE_KCONF(
    int64, ad.bidServer, costCapV3PayerDefTailSet)  // 按照尾号区分不同调价策略 payer 其他
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, mobileUnitWhiteList)  // 移动端 costcap 暗投白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, mobileUnit2PcOcpmWhiteList)  // 移动端调价字段迁移白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, cpmSpeedPayerTailSet)  // 速推 CPM 计费实验白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, RtaSecondPredictRtaSourceTypeWhiteList)  // rta 二次请求 rta_source_type 白名单 // NOLINT
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(
    ad.bidServer2, espLiveEopAuthorInspireLiveRoi)  // 直播订单支付作者维度 ROI
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(
    ad.adRank3, iaaAcqGenShallowEcpc)  // 直播订单支付作者维度 ROI
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.bidServer2, espLiveEopAuthorInspireLiveRoiBound)  // 直播订单支付作者维度 ROI bound // NOLINT
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.bidServer2, espPhotoEffectivePlayAuthorRatio)  // 短视频有效播放系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.bidServer2, espPhotoEffectivePlayAuthorRatioBound)  // 短视频有效播放系数 bound // NOLINT
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espPhotoEffectivePlayEcpcTail, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableEspMobileLiveRoasBsTail, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableEspRoasEmsembleTail, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableEspRoasAuthorPcocTail, "100;;;")
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.bidServer2, EspRoasEnsembleAuthorPcoc);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableInnerLoopStorewideUpliftFea, false);
  DEFINE_PROTOBUF_NODE_KCONF(StorewideLiveULevelConf, ad.adRank3, storewideLiveULevelConf);  // 直播全站分 u 校准系数配置  // NOLINT
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, upliftBoundMap);  // 直播全站 uplift bound 设置
  DEFINE_PROTOBUF_NODE_KCONF(StorewideLivePageConf, ad.adRank3, storewideLiveUpliftPageLimit);  // 直播全站 uplift 分页面请求白名单  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(StorewideLiveUpliftConf, ad.adRank3, storewideLiveUpliftPageConf);  // 直播全站 uplift 分页面校准系数配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(StorewideLiveUpliftEspConf, ad.adRank3, storewideLiveUpliftEspConf);  // 直播全站 uplift 分客户类型校准系数配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(StorewideLiveUpliftConf, ad.adRank3, storewideLiveUpliftPricePageConf);  // 直播全站 uplift 计费分页面校准系数配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(StorewideLiveUpliftEspConf, ad.adRank3, storewideLiveUpliftPriceEspConf);  // 直播全站 uplift 计费分客户类型校准系数配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(StorewideLiveUpliftBoundConf, ad.adRank3, storewideLiveUpliftBoundConf);  // 直播全站 uplift 上下界限制配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(StorewideLiveUpliftAuthorExpTagConf, ad.adRank3, storewideLiveUpliftAuthorExpTag);  // 直播全站客户尾号 exptag 配置  // NOLINT
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, storewideUpliftTail, "100;;;");  // 直播全站 uplift 客户尾号白名单
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, storewideLiveUpliftRTail, "100;;;");  // 直播全站 uplift R 模型客户尾号白名单  // NOLINT
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, storewideLiveUpliftQTail, "100;;;");  // 直播全站 uplift Q 模型客户尾号白名单  // NOLINT
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, storewideLiveUpliftPriceTail, "100;;;");  // 直播全站 uplift 计费客户尾号白名单  // NOLINT
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, storewideLiveUpliftPriceRTail, "100;;;");  // 直播全站 uplift 计费 R 模型客户尾号白名单  // NOLINT
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, storewideLiveUpliftPriceQTail, "100;;;");  // 直播全站 uplift 计费 Q 模型客户尾号白名单  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(InnerLiveAuthorExpTagConf, ad.adRank3, innerLiveAuthorExpTag);  // 内循环直播客户尾号 exptag 配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(InnerLiveCoefConf, ad.adRank3, innerLiveCoefConf);  // 内循环直播打折系数配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(InnerLiveCoefConf, ad.adRank3, innerLiveUpliftEcpcCoefConf);  // 内循环直播 uplift ecpc 系数配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(InnerLiveAdmitConf, ad.adRank3, innerLiveUpliftEcpcAdmitConf);  // 内循环直播 uplift ecpc 准入配置  // NOLINT
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopBrandClientShowPayerSet, "100;;;");  // 粉条品牌曝光计费白名单
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, storewideMerchantUpliftTail, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, fanstopNewPayerHoldoutTail, "100;;;")  // 粉条补贴留反尾号
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, enableStorewideRequestByFlowTail, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, fanstopGuaranteedRankingPayerSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.pidserver, industryHcSplitTestDateList);
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopNewinnerNobidSkipCpmboundWhitelist, "100;;;");
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, fanstopNobidOcpxEnableCpmboundSet);
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, perfFeatureColumnRatio, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, itemCardSelectedFlowDiscountTail, "1000;;;");
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, itemCardSelectedFlowCampaignDiscountRatio);
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 1 >= IMPL_BLOCK_BEGIN and 1 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, fanstopRealSctrRatio, 1.0);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopPrmsDiscountMap);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, storewideMaxHcRatio, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, storewideHcCoef, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, splashRouterDoubleDiffEpsilon, 1e-5);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, outerLoopBonusTagDelete);  // 外循环可下线的 bonus tag
  DEFINE_HASH_SET_NODE_KCONF(int64, reco.inner_fanstop, cpmToLcWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adAutoParam, ZYaccountfilterset)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adAutoParam, BigPromotionSupportAdmit)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, account_id_cpm_thr_v2)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, fanstopCostCapTestAuthorSet);  // 粉条招聘 cost_cap 白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, fanstopCostCapPayerSet);  // 粉条招聘 cost_cap 尾号白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, MobileFastAudienceUnit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, MobileFastAudienceUnitTail);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, mobileAutoBidLiUnitSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, mobileAutoBidLiCampaignSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveDeepCoinOcpxs);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveQuitRateAdmitPageIds);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveQuitRateAdmitSubPageIds);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, incentiveLtvAdmitSubPageIds);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, incentiveCostlySubPageIds);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, incentiveNotCostlySubPageIds);
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileAutoBidLiOcpxUnitTailMap);
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer2, fanstopv2AutoBidLiOcpxUnitTailMap);
  DEFINE_PROTOBUF_NODE_KCONF(AggregationMappingConf, ad.adRank2, aggregationXInfoMappingConf);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableBizFanstopAutobidliExpRanksvr, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableAutobidliExpAll, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableRankDotDebug, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableEspMobileAutobidliExpAll, false);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adserver2, stayKminiGameDarkctrlProductName);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget4, stayKminiGameDarkctrlAccountIdV2)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, stayPlayletIaaDarkctrlAccountId);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget3, stayPlayletIaaDarkctrlProductNameSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget3, djIaaIncentiveBlockSet);
  DEFINE_PROTOBUF_NODE_KCONF(HiddenCostThresholdConfig, ad.adRank, hiddenCostThresholdConfig);
  DEFINE_PROTOBUF_NODE_KCONF(HiddenCostThresholdConfig, ad.adRank, gylHiddenCostThresholdConfig);
  DEFINE_PROTOBUF_NODE_KCONF(CorporationCeilingWhiteList, ad.adtarget, corporationCeilingWhiteList);
  DEFINE_PROTOBUF_NODE_KCONF(RetentionDaysDropListConfig, ad.adRank, RetentionDaysDropList);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, fanstopCostCapCvrBalancer, 1.0)  // 粉条招聘 cost_cap pCvr 平衡指数
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, fanstopLowerEcpmThrRatio, 1.0);  // 粉条特殊降门槛打折系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, adMerchantT7ROIBonusRatio);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, fanstopCvrThresholdWhitelist);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.bidServer, fanstopBrandBaseProfitRate);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, adRankEspLiveAtvValue, 50.0)  // atv 兜底
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, adRankLiveRoasHighAtvThre, 500000.0)  // 直播 ROAS 高客单价门槛
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, bossDegradeRatio, 1.0)  // 白名单用户降级
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, innerFollwAccV2UnitSet, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, innerStoreWideDarkAccountTail, "100;;;")
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, accountBidMcbNodeOcpxDeep);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, ZhugongEcpcProductNameSet);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank2, ZhugongEcpcProductNameMap);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, DoubleEndProductNameSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, DoubleEndIndustrytSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, searchProductNameSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, searchAuthorIdSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, searchCpmThreshBlackCity);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, ZhugongEcpcSecIndSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, coinOrderPaiedCampaignType);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, coinOrderPaiedOcpcActionType);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, coinOrderPaiedIndustryIdV3);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, coinOrderPaiedBlackAuthorList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.algorithm, adRetargetConvertionProductListForStay);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, retentionRetargetEcpcRatio, 1.0);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, clientDebugUserWhitelist);
  DEFINE_PROTOBUF_NODE_KCONF(AdSmartOfferValues, ad.frontserver2, adSmartOfferValues);  // 短剧C补实验参数配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(AdSmartOfferConfig, ad.frontserver2, adSmartOfferConfig);  // 短剧C补实验参数配置  // NOLINT
  // la 超深度转化，账户黑名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, lpsValidClusAccountBlackList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, ecomMultiChuDianTestAccountSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.fanstopServer, FanstopPvDebugWhiteVisitorID)  // 粉条 pv debug 白名单
  // 磁力金牛专推 no bid
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, auctionLogTestUnitSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, splashDebugUserList)
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.adRank, NobidProductPriceInitUnitTailV2);
  DEFINE_PROTOBUF_NODE_KCONF(MultiNobidEcpcExpConfig, ad.adRank3, multiNobidEcpcExpConfig);
  // 跳过计费分离摸底实验单元尾号
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, skipBillingSeparateUnitTail);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, roasSkipBillingSeparateUnitTail);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, roasSkipBillingSeparateUnitTailRank);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, storewideSkipBillingSeparateCampaignTailRank);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, liveHostingSkipBillingSeparateCampaignTail);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, enableLiveHostingPhotoToRedisCampaignAndScene);
  DEFINE_SET_NODE_KCONF(int64, ad.adRank, skipAccountBiddingWhiteAccount);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, skipBsExpWhiteAccount);
  DEFINE_SET_NODE_KCONF(std::string, ad.adRank, skipAccountBiddingWhiteProduct);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, innerAdxRecallSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, innerHealthPowerIndustrySet);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, innerHealthPowerKconf);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, smbSkipBillingSeparateUserTail);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, smbSkipBillingSeparateAccountTail);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, smbHcTails);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, IMAccountMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, QWAccountMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, SXAccountMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(firefly.bonus, mockCrmCenterIndustryHc)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, ImqiweiHcCtrlWeight);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerControlFeidanlieHcConfig);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, ZixunAccountMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, SixinAccountMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, LaoqiweiAccountMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, newCustomerBsTails);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, pureNewCustomerGMVBSTail);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, gameNewProductPriceRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(firefly.playset, playsetPriceRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(firefly.playset, playletPriceRatioOpt)
  // [lijunze03] 添加短剧折扣自动下线配置
  DEFINE_PROTOBUF_NODE_KCONF(PlayletPriceRatioOpt2Pb, ad.adRank3, playletPriceRatioOpt2)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, playletSdpaCampaignPriceRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, playletCampaignTypePriceRatio)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, playletSdpaBlackPriceRatio)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, playletDiscountHourRatio, 1.0)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, playletDiscountByHour)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, pureNewCustomerBSTail);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, selfServiceBsTail)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, smbNewCusDiscountConfig);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank2, selfService1ADiscountConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, selfServiceBonusChargeModeSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, consumPowerDiscountTag);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, consumPowerUlevelDiscountSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, selfServiceCaliTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, R3WhiteListAuthor);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, searchBonusAccountBlackSet);
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 2 >= IMPL_BLOCK_BEGIN and 2 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, enableFanstopFollowPriceTail, "100;;;");
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, fanstopFollowBoundtail);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.frontserver, cidPriceDiscountConfig)
  DEFINE_PROTOBUF_NODE_KCONF(NewProductDiscount, ad.adserver, newProductDiscount);
  DEFINE_KCONF_NODE_LOAD(OnlinePriceDiscountConfig, ad.frontserver, innerPriceDiscountConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, pcSelectPriceDiscountUnitTail)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, pcSelectPriceDiscountConf);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, lspNoDiscountAccountSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, itemCardPhotoBsUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, pcLiveAllPageBsUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, storewideBsCampaignTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, skipStorewideLiveBsTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, t7roasSkipBillingSeparateUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, skipBsAutobidPriceRatioUnitTail)
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, liveHostingBsTail, "100;;;");
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, InnerLoopBsRatioTransToPackUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, InnerLoopBsRatioTransToPackCampaignTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, eopSkipBillingSeparateUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, pcLiveSkipHardPriceProtectUnitTail)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 3 >= IMPL_BLOCK_BEGIN and 3 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_KCONF_NODE(ad.frontserver, SysMaxPrice, 100000)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, SysMaxCpaPrice, 100000)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, SysMinPrice, 1)
  DEFINE_PROTOBUF_NODE_KCONF(RewardedCoinScalingByAccountList, ad.frontserver,
      rewardedCoinScalingByAccountList);
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, fanstopBsRatioTail, "");
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver2, fanstopBsRatioForSptail);
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver2, fanstopBillingOnCostRatioTail, "");
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, fanstopBsOcpxBlacklist);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espMobileLiveDeepBsPriceRatio, "10;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espMobileNativeBsTail, "10;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, fanstopLiveCapExpAccountWhitelist, "");
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, fanstopTestWhitelistOcpx);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, fanstopBsCapExpWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, cartoonProductNameSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.miniSeries, cartoonSeriesProductNameSet);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.bidServer, fanstopBsCapCustombidMap)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, produceMpcLogRatio, 0.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, duanjuDefaulMcbRoiRatio, 1.08)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 4 >= IMPL_BLOCK_BEGIN and 4 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT32_KCONF_NODE(ad.adRank, sendMpcLogRatio, 100)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, produceMpcLogDirect, false)
  DEFINE_INT64_KCONF_NODE(ad.adRank, adRewardedRankingListExpireHours, 168)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, maxInnerInvoTrafficRankingListSize, 50)
  DEFINE_INT32_KCONF_NODE(ad.adRank3, maxInnerCardRedisRankingListSize, 200)
  DEFINE_INT32_KCONF_NODE(ad.adRank3, maxInnerCardRedisExpireHours, 24)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, commonSecondInvalidCardIndustry)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adtarget, adAuthor2Author)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, authorRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, maxAuthorRankingLisSize, 100)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableSkipStorewideLiveBsTail, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableFanstopBsCurrentCpaBase, false);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, gameShoufaForceShowProduct);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, kMiniGameAppBlackAccountSet);
  DEFINE_PROTOBUF_NODE_KCONF(ks::ad_rank::kconf::StorewideMerchantUpliftEspConf, ad.adRank3, StorewideMerchantUpliftEspConf);  // 商品全站 uplift 分客户类型校准系数配置  // NOLINT

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, innerLiveHighAtvAuthor);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, innerLiveHighAtvAccountTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, mergeBidAccountIdTail);

  // 外循环不顶价策略相关
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, outerloopGspSkipSameAgentBidtypeSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, outerloopGspSkipSameAgentOcpxSet);

  // 粉条自定义出价接入计费分离尾号
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopOcpmEnableBsWhitelist, "");

  // 粉条头客补贴
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, fanstopHeadCustomerBonusAccountSet);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopHeadCustomerBonusOcpxRatio);

  // 粉条客户分层补贴
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopCustomerLevelBonusMap);
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 5 >= IMPL_BLOCK_BEGIN and 5 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopComboTypeBonusRatio);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopPlayOcpxLowCvrFixMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopSceneOrientedTypeBonusMap);

  // 粉条电话拨打扶持
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, fanstopPhoneCallBonusRatio, 1.0);

  // 粉条私信线索扶持
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopMessageLeadsBonusOcpxRatio);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopMessageLeadsHcOcpxRatio);

  // 自助头客补贴
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, selfServHeadCustomerBonusAccountSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, newcusHeadAuthorSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, smbNewcusAuthorid);

  DEFINE_PROTOBUF_NODE_KCONF(NewCusMultiStageData, ad.adRank2, newCusMultiStageConfig);

  // 自助 HC 配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerSelfServHCKconf);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerSelfServOpmHCKconf);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, SmbColdstartHCKconf);

  // 内循环人群校准配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerCaliDailyKconf);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerSmbCaliDailyKconf);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerVideoHighCtrMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerCaliDailyKconfRefine);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerCaliDailyKconfWpage);
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank, innerNobidGenderAgeKeyConf);
  // 内循环中小冷启动
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, smbauthoridLiveRate);
  DEFINE_INT32_KCONF_NODE(ad.adRank, smbauthoridAdmit, 0);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, hcsmbauthoridLiveRate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, hcsmbbonuslevelbucket1Rate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, hcsmbbonuslevelbucket2Rate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, hcsmbbonuslevelbucket3Rate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, hcsmbbonuslevelbucket4Rate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, newcushcsmbbonuslevelbucket1Rate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, newcushcsmbbonuslevelbucket2Rate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, newcushcsmbbonuslevelbucket3Rate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, newcushcsmbbonuslevelbucket4Rate);
  DEFINE_INT32_KCONF_NODE(ad.adRank, hcsmbauthoridAdmit, 0);

  // 商家生态打压 blacklist and whitelist
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, EcologySellerRankLiveWhiteList);
  // ad_calibration_task
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, validAdCalibrationIdSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, BrandAuthorWhiteList);
  // 内粉圈人作品白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.fanstopServer, packageRecallInnerPhotoWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.fanstopServer, InnerPolarisFollowOrgSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.fanstopServer, InnerFilterPlcOrgSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.fanstopServer, InnerPolarisTailExpSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.fanstopServer, packageRecallInnerUnitWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.fanstopServer, packageRecallInnerRegionPhotoWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.fanstopServer, innerBadQualitySupportReason)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.fanstopServer, innerRefreshCitySet)  // 内粉进行刷次实验的城市
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, costCapAutoBidV2UnitList)  // cost cap v2 unit 白名单
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.fanstopServer, hetuFilterDupTagSet)  // 粉条河图标签去重集合
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adtarget3, similarProductMap)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget3, incentiveInvokedProductCampaignType)
  // 小程序广告
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, MicroAppNoCvrPageidList)

  // 激励营销 PEC
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, ModifyBidForRewardPosid)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, ModifyBidForRewardOcpx)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, ModifyBidForRewardAccount)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, ModifyBidForRewardAccountWhite)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, ModifyBidForRewardType)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, ModifyBidForRewardBidType)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, ModifyBidForRewardCampaign)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, PecFrequencyPosid)

  // 喜番计费打折 kconf
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, xifanIncentiveDiscountRatio, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, xifanFeedDiscountRatio, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, xifanSplashDiscountRatio, 1.0);

  // 爆品的跟品打压
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, sameMerchantProductPenalty)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, appInvokedPurchaseAccountWhiteList)
  // 电商优惠券策略
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, merchantCouponAuthorWhiteList)  // 商家快手 id 白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, InspireStyleInvokedMerchantProductIdSet)

  // 种草人群实验
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, followEcpcLowerBound, 0.1);             // 涨粉 ecpc 调权下限
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, followEcpcUpperBound, 4.0);             // 涨粉 ecpc 调权上限
  DEFINE_PROTOBUF_NODE_KCONF(StrategyCrowdBoostConf, ad.adRank, strategyCrowdBoostConf);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, strategyCrowdBoostUpperBound, 3.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, strategyCrowdBoostLowerBound, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, deepCoefLowerBound, 0.01);

  // 电话建联 实验账户
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, phoneConnectAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, bonusSplitTestProjectWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireIndustryLiveUserid);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireMerchantTestUserid);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inSpireIndustryLiveSubPage);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adBonusByLxy, bonusTagAccoutWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, accountTailNumberSets);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, highQualityAccountTailNumberSets);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, smoothExploreAccountTailNumberSets);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, LeveragePhotoTailNumberSets);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, duanjuNewPhotoList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, duanjuNewNameBaseSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, duanjuNewNameSet);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, LeverageFilterOcpxSets);
  // 行业直播
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireIndustryLiveAccountCpmthrLow);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireIndustryLiveAccountCpmthrHigh);
  // here zhoushuaiyin
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, bonusProjectInteractiveForm);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, bonusProjectRequestType);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, cpmBasedBonusProjectTagSet);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, ButtonCLickCpaAuthorSet);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, LeadsSubmitCpaAuthorSet);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, juxingSupplementExpAccount);
  DEFINE_INT32_KCONF_NODE(ad.adRank, cpmBasedBonusProjectAccountTail, 0);
  // set<string>
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, ModifyBidForRewardProduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, ModifyBidForRewardProductWhite)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, ModifyBidForRewardProductDetailWhite)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, RepeatRewardProductWhite)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, PecBonusRewardProductWhite)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, rewardedCloseClientCpmCvrProductList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, innerHardCmdKeyList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, storyAdPurchaseWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, pddHelpProductWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, pddNativeProductWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, irregAlllessonWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.fanstopServer, mcbOptActionTypeSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, novelEcpcWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, SdpaEcpcWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, predictTypeWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adserver, productNoBiddingAmongAgentSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, LiveSepcialtyOcpxConfig);
  DEFINE_HASH_SET_NODE_KCONF(std::string, adqa.adCheckEnv, searchCheckQuery)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, searchSkipFilterQuery)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, appInvokedPurchaseProductWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, miniAppRoasInvokedProductWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, financeEnsembleProductWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.bidServer, shortPlayClk2PayAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.bidServer, shortPlayClk2PayRoasAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, outerBonusProjectBlacklist)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 6 >= IMPL_BLOCK_BEGIN and 6 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, miniAppRoasInvokedAccountWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, shortPlayImp2payAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, adFinFinanceLpsEnsembleIndustry)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, sevenDaysPurchasePayTimesProductWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, adFinEduObtainAccount)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adFinUseCreditProduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adFinCreditRoiProduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adFinJinjianCreditProduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adEduLpsEcpcWhiteProduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adEduLpsAllClassEcpcWhiteProduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adFinJinjianCreditFilterProduct)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, adFinDeepOptAccount)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adFinDeepOptProduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adFinDeepEcpcProduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, lpsAcquisitionGeneralizationWhiteproduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, lpsAcquisitionGeneralizationWhiteproductForC)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, IaaAcquisitionGeneralizationWhiteocpxSet)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, closedLoopIaaSkipSheildBudgetWhiteSet)

  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableInnerCIDPriceTagProdTail, false)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, innerCIDPriceTagProdMap);

  DEFINE_SET_NODE_KCONF(std::string, ad.adRank3, innerCIDluhcUlist)
  DEFINE_SET_NODE_KCONF(std::string, ad.adRank3, innerCIDluhcClist);

  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, ctcvrSkipCpmThdOcpcActionType)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, ctcvrSkipCpmThdOcpcActionTypeEvent)
  // 传奇游戏名
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, abChuanqiProductNameList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, searchSkipCpmThrKconf)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adCmdConfigs, dpaCmdKeyList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, adData.realtime, rankTraceLogAdExtendInfoWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, liveColdstartBonusTailList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, outsideEcpcRegisterConf)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopLiveCSBonusRatioMap);
  // 粉条成本折扣 account 尾号
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, enableFanstopCostDiscountPayerKconf, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, enableFanstopCostDiscountPayerKconf2, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, enableFanstopPddUserPackagePhotoKconf, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, enableFanstopPddUserPackageAccountKconf, "100;;;");
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopNewAccountStageBonusRatioMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopPddUserPackageKconfMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopCostDiscountHCKconfMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopCostDiscountHCKconfMap2);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopOrientationTagConfig);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, gpmBalanceAuthorIds);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, wanheChargeActionTypeSubPageId);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, invoTrafficModelSubPageIdConf)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, exploreFeedJumpOutShield);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, exploreFeedJumpOutShieldSubPageId);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, innovationTrafficAdjustPrice);
  DEFINE_SET_NODE_KCONF(int64, ad.adRank, playletROIFormulaAccountTail);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, industryLiveNativeCpmThrdAccount);
  // 关注页 116 活动 boost 作者名单
  DEFINE_HASH_SET_NODE_KCONF(int64, reco.follow, mixAdFirstWhiteAuthorSet);
  // 大健康行业作者名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, bigHealthAuthor);
  // mcb 次留
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.bidServer, mcbRetentionProductsList);
  // AI 传媒次留优化 hc 分产品调控配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, AIMediaStayHcRatioMap);
  // AI 传媒次留优化 hc 分产品过滤配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, AIMediaStayDropRatioMap);
  // 激励广告
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, rewardCoinAdTypeAdjustCoef);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, rewardCoinAdTypeAdjustCoefV2);
  DEFINE_LIST_NODE_KCONF(int32, ad.adRank, adShallowIncentiveCoinTreatmentList);
  DEFINE_LIST_NODE_KCONF(int32, ad.adRank, adDeepIncentiveCoinTreatmentList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, UnifyCalcCoinRoiCoefBiasExpTag);
  DEFINE_LIST_NODE_KCONF(int32, ad.adtarget2, coldbootTagList);
  DEFINE_LIST_NODE_KCONF(double, ad.adRank, adShallowIncentiveCoinPercentTreatmentList);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, unifyCalcCoinAuctionBid2PriceCaliCoefMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveAdUnifyCalcCoinSkipSubPageIds);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, incntvAdUnifyCalcCoinMdpLtv);
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.adRank2, incntvAdSubPageIdToEntry);
  DEFINE_PROTOBUF_NODE_KCONF(IncentiveAdUnifyCalcCoinUpperList, ad.adRank2,
                             incntvAdUnifyCalcCoinSubPageIdToCoinUpperV2);
  // 货架 retarget 分段扶持
  DEFINE_PROTOBUF_NODE_KCONF(ShelfRetargetSegCoefConfigV2, ad.adRank2, shelfRetargetSegCoefConfigV2);
  //货架
  DEFINE_PROTOBUF_NODE_KCONF(ShelfRecallTagFcCoefConfig, ad.adRank2, shelfRecallTagFcCoefConfig);
  // 货架 daily auto ecpc
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, shelfPageOcpxCostRatioConfigV1);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, shelfPageOcpxCostRatioConfigV2);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, shelfPageOcpxCostRatioConfigV3);
  // 货架直播卡召回通路 ecpc 配置
  DEFINE_PROTOBUF_NODE_KCONF(ShelfLiveCardRecallEcpcConfig, ad.adRank2, shelfLiveCardRecallEcpcConfig);
  // 货架直播卡召回通路 ecpc 配置
  DEFINE_PROTOBUF_NODE_KCONF(ShelfLiveCardRecallEcpcConfig, ad.adRank2, shelfItemCardRecallEcpcConfig);
  // 货架 gpm 过滤扶持相关配置
  DEFINE_PROTOBUF_NODE_KCONF(ShelfGpmConfig, ad.adRank2, shelfItemGpmConfig);
  DEFINE_PROTOBUF_NODE_KCONF(ShelfGpmConfig, ad.adRank2, shelfLiveGpmConfig);
  // 货架商城流量扶持
  DEFINE_PROTOBUF_NODE_KCONF(ShelfLiveUOcpxEcpcBoostConfig, ad.adRank2, shelfScLiveUOcpxEcpcBoostConfig);
  // 货架买首流量扶持
  DEFINE_PROTOBUF_NODE_KCONF(ShelfLiveUOcpxEcpcBoostConfig, ad.adRank2, shelfMsLiveUOcpxEcpcBoostConfig);
  // 货架猜喜流量扶持
  DEFINE_PROTOBUF_NODE_KCONF(ShelfLiveUOcpxEcpcBoostConfig, ad.adRank2, shelfCxLiveUOcpxEcpcBoostConfig);
  // 货架粉丝个性化扶持相关配置
  DEFINE_PROTOBUF_NODE_KCONF(ShelfLiveFansEcpcConfig, ad.adRank2, shelfLiveFansEcpcConfig);
  DEFINE_PROTOBUF_NODE_KCONF(ShelfItemCardBoostHcConfig, ad.adRank3, shelfItemCardBoostHcConfig);
  // 货架接入 context 特征
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableInnerShelfMerchantFea, false);
  // 外循环激励直播直投无简易直播间 ctr 矫正
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerLiveIncentiveCtrFixMap);
  // 外循环直播直投 ctr 矫正
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adAutoParam, dspLiveFollowSubpageCtrCali);
  // 外循环直播短引 ctr 矫正
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adAutoParam, dspLiveFollowSubpageP2LCtrCali);
  // 外循环直播短引 sctr 矫正
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adAutoParam, dspLiveSubpageP2LSctrCali);
  // 外循环直播开播优化
  DEFINE_PROTOBUF_NODE_KCONF(DspLiveIndustryEcpcConfig, ad.adAutoParam, dspLiveIndustryEcpcConfig);
  DEFINE_PROTOBUF_NODE_KCONF(IaaRlParameterControl, ad.adRank3, iaaRlParameterControl);
  // 外循环引入评论区模型预估 xtr
  DEFINE_PROTOBUF_NODE_KCONF(OuterRankCmtEcpcConfig, ad.adAutoParam, outerRankCmtEcpcConfig);
  // 外循环优质原生 hc 系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerNativeGoodHcRatioMap);
  // 激励视频
  DEFINE_PROTOBUF_NODE_KCONF(MultiRewardedCoinDataList, ad.frontserver, multiRewardedCoinDataList);
  DEFINE_PROTOBUF_NODE_KCONF(DeepRewardedCoinDataList, ad.frontserver, deepRewardedCoinDataList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.pidserver, pidInspirePhotoRoiOcpcActionTypeSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveAutoDarkBtrOcpxSet);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, inspireThanosSctrMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, inspireFeedSubPageIdsUsedNewSctr);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveSctrPredSupPages);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver2, incentiveSctrPageMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver2, incentiveSctrPosMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, inspireFeedSoftAdSctrMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerLiveFeedPageCorrectRatio);
  // 激励广告 MDP LTV 对应 Key 分层
  DEFINE_PROTOBUF_NODE_KCONF(MultiIncntvAdUnifyCalcCoinMdpLtvKeyList, ad.adRank2,
                             multiIncntvAdUnifyCalcCoinMdpLtvKeyListCpm);
  DEFINE_PROTOBUF_NODE_KCONF(MultiIncntvAdUnifyCalcCoinMdpLtvKeyList, ad.adRank2,
                             multiIncntvAdUnifyCalcCoinMdpLtvKeyListQuitRate);
  DEFINE_PROTOBUF_NODE_KCONF(MultiIncntvAdUnifyCalcCoinMdpLtvKeyValueList, ad.adRank3,
                             incntvAdUnifyCalcCoinMdpLtvV2);
  // 激励暗投控比
  DEFINE_PROTOBUF_NODE_KCONF(IndustryCampaignTypeDarkControlConfigStruct, ad.adFlowControl,
                             inspireIndustryCampaignTypeDarkConfig);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, fanstopInnerSkipAuthorSet);
  // 短剧 iaa 激励再拦截一道在 rank 侧
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, djIaaIncentiveRankBlackAccountId);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, djIaaIncentiveRankBlackAccountIdAutomatic);
  // 短剧分销撮合账户
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, playletSalesMatchingAccountIdAutomatic);

  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, explorePageEcpcConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, overlapUserExploreConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, exploreAdvvCalibConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, triggerPhotoTagRatioConfig);

  // [zhaorongsheng] 搜索广告激励
  DEFINE_PROTOBUF_NODE_KCONF(MultiRewardedCoinSearchInspireAdBoxDataList, ad.adRank,
                            multiRewardedCoinSearchInspireAdBoxDataList);
  DEFINE_PROTOBUF_NODE_KCONF(DeepRewardedCoinSearchInspireAdBoxDataList, ad.adRank,
                            deepRewardedCoinSearchInspireAdBoxDataList);
  DEFINE_INT64_KCONF_NODE(ad.adRank, searchInspireAdBoxCpmThreshold, 3000);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchAdBoxOcpxBoost);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, ProductPackageNameMap);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, ProductPackageNameMapConv);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, ProductPackageNameMapInvoked);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchAdBoxProductBoost);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxOrderOcpxAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxOrderCampaignAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxConvOcpxAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxConvCampaignAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxInvokeOcpxAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxInvokeCampaignAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxSingleColOrderOcpxAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxSingleColOrderCampaignAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxSingleColConvOcpxAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxSingleColConvCampaignAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxSingleColInvokeOcpxAdmit);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, searchAdBoxSingleColInvokeCampaignAdmit);
  DEFINE_PROTOBUF_NODE_KCONF(SearchActivateAdvertiserBlackConfig, ad.adRank,
    searchActivateAdvertiserBlackConfig);
  DEFINE_PROTOBUF_NODE_KCONF(SearchInvokedAdvertiserBlackConfig, ad.adRank,
    searchInvokedAdvertiserBlackConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchAdBoxSingleColRequestSceneBoost);
  DEFINE_PROTOBUF_NODE_KCONF(SearchAdBoxSingleColOcpxExpBoost, ad.adRank,
    searchAdBoxSingleColOcpxExpBoost);
  DEFINE_PROTOBUF_NODE_KCONF(SearchAdBoxSingleColProductExpBoost, ad.adRank,
    searchAdBoxSingleColProductExpBoost);
  DEFINE_PROTOBUF_NODE_KCONF(SearchAdBoxSingleColRequestSceneExpBoost, ad.adRank,
    searchAdBoxSingleColRequestSceneExpBoost);
    DEFINE_PROTOBUF_NODE_KCONF(SearchAdBoxSingleColItemTypeExpBoost, ad.adRank3,
    searchAdBoxSingleColItemTypeExpBoost);
  DEFINE_PROTOBUF_NODE_KCONF(SearchInspirePvAdmitConfig, ad.adRank3, searchInspirePvAdmitConfig);
  DEFINE_PROTOBUF_NODE_KCONF(SearchInspireDeepRewardConfig, ad.adRank3, searchInspireDeepRewardConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, searchInspireDuanjuThanosOcpxBoost);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, searchInspireDuanjuThanosProductBoost);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, searchInspireDuanjuThanosRequestSceneBoost);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, searchAdBoxUserValueLevel);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, searchAdBoxUserValueLevelCoinLowerRatio);

  // [zhangxingyu03]
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 7 >= IMPL_BLOCK_BEGIN and 7 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, deepRewardAppVersion)  // 版控相关 kconf
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveAdlessPageIdSet)  // 免广告激励准入 page_id
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveAdlessSubPageIdSet)  // 免广告激励准入 sub_page_id
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveAdlessPosIdSet)  // 免广告激励准入 pos_id
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, adlessCampaignTypeSet)  // 免广告激励准入营销目标
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, invokedAdmitSubPageIdSet)  // 免广告激励准入营销目标
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, adlessAccountIdBlackList)  // 免广告激励黑名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, watchVideoSubpageSet)  // 看视频 sub_page_id
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, watchVideoPageSet)  // 看视频 page_id
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, deepCoinAdjustOcpcActionRatio)  // 深度金币分优化目标调控
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, invokedProductNameBlacklist);  // 拉活唤端产品名黑名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, incentiveCoinPecAdmitSubPageIdSet)  // 币 pec 准入 sub page id
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, orderAdmitSubPageIdSet)  // 下单激励准入 sub page id
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, adlessIAATimeFreqControlCampaignType)  // 免广告 IAA 时间频控计划类型  //NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, convAdmitSubPageIdSet)  // 下单激励准入 sub page id
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, qcpxCouponSubPageIdSet)  // qcpx 券准入页面
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank3, grossMaxSubPageIdBlackList)  // 毛利最大化黑名单
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, convCidProduceName);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, purchaseIncentiveWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, purchaseIncentiveBlackList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, invokedCidProduceName);
  DEFINE_PROTOBUF_NODE_KCONF(IncentiveConvDeepIncentiveRatioConfig, ad.adRank3,
    incentiveConvDeepIncentiveRatioConfig);
  DEFINE_PROTOBUF_NODE_KCONF(DeepIncentiveConvCoinCoefConfig, ad.adRank3,
    deepIncentiveConvCoinCoefConfig);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, convOcpcWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, invokedOcpcWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, liveOrderPayAdmitSubPageIds)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, liveRoasAdmitSubPageIds)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, shortPlayDeepIncentivePosIds)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank3, liveOrderDeepIncentiveTreatmentMap)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank3, liveRoasDeepIncentiveTreatmentMap)
  DEFINE_PROTOBUF_NODE_KCONF(IncentiveInovkedDeepIncentiveRatioConfig, ad.adRank3,
    incentiveInvokedDeepIncentiveRatioConfig);
  DEFINE_PROTOBUF_NODE_KCONF(DeepIncentiveSpecificConfig, ad.adRank3,
    deepIncentiveSpecificConfig);
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.adRank3, outerMedicalAccountAdTag)
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.adRank3, deepIncnetiveFreqControlConfigForPromotion)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, incentiveUpliftDecisionSitePageProductNameWhiteList)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, specificDeepCoinCoefConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, drawFlowPriceRatioConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, incentiveDishSubPageId)
  DEFINE_PROTOBUF_NODE_KCONF(DeepIncentiveRctConfig, ad.adRank3,
    deepIncentiveRctConfig);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, xifanDeepIncentivePosIds)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, LiveOrderBlackAuthorList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, deepIncentiveAllOcpc);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, deepIncentiveInnerCidAccountIds);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, orderDeepIncentiveSpecificIndustryIds);

  // 激励视频作品引流 ctr 打折流量  pageid  || sub_pageid 均支持
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, rewardVideoP2lCtrDiscountPage);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adtarget2, juxingAppAdvanceAccountWhiteList);
  // 移动端 rank 不生效 bound
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, mobileLiveSkipBoundPayerTailSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, p2lShowCaseWhiteAuthorList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, espLiveHighAtvCaliWhiteAuthor)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, gameSimilarProductBonusProject);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, gameSimilarProductPreciseBonusProject);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, ocpcActionTypeKminiGameIAA);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, productCpmThresholdRatio);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, gameHighValueBonusProjectList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, gameHighLtvBonusProjectList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, kminigameHighLtvBonusProjectList);
  // 大游接入 sdk 产品白名单账户  // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adAutoParam, bigGameSdkWhiteAccountConf)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, IndustryLtvHcIndustryList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, DefaultIndustryOrientationList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, defaultIndustryOrientationStrategyList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, EspAuthorFansCaliOcpx);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, dncHcTags);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, dncHcFactorTags);
  // 商家投放相关
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, cidHcExpAccountTailList);
  // 付费短剧
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, playletAccountTailList);
  // 交通行业深度预估 drop 配置
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, jiaotongLowCvrDropProductList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, jiaotongLowCvrDropAccountList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, jiaotongDynamicLowPayFilterProduct)
  DEFINE_PROTOBUF_NODE_KCONF(JiaotongIndDeepOptConf, ad.adRank, jiaotongIndDeepOptConf)
  DEFINE_PROTOBUF_NODE_KCONF(JiaotongIndDeepOptConf, ad.adRank, nativeJiaotongIndDeepOptConf)
  // 交通行业深度优化配置
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adJtPayRateForDeepOptProduct)
  DEFINE_PROTOBUF_NODE_KCONF(JiaotongOcpxUserTagIndConf, ad.adRank, jiaotongOcpxUserTagIndConf)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, jiaotongDeepModelHcOptProduct)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, jiaotongUserHcRatio)
  // 线索聚合竞价项目配置
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, clueAggrByCpmDarklyProduct)
  DEFINE_PROTOBUF_NODE_KCONF(ClueAggrByCpmDarklyOptConf, ad.adRank, clueAggrByCpmDarklyOptConf)
  DEFINE_PROTOBUF_NODE_KCONF(ClueAggrByCpmBrightlyFilter, ad.adRank, clueAggrByCpmBrightlyFilter)
  DEFINE_PROTOBUF_NODE_KCONF(ClueAggrByCpmBrightlyOptConf, ad.adRank, clueAggrByCpmBrightlyOptConf)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, simplePromotionAccountBonusMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, clueAggrBrightlyThirdSiteAccount)
  // 本地投项目配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, localLifePromotionAccountBonusMap);

  // 内循环直播跳过激励压价配置
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, liveRoasOcpmSkipIncentiveEcpcTail, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, liveEopOcpmSkipIncentiveEcpcTail, "100;;;");

  // 电商平台 相关配置
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, InspireStyleConvMerchantProductIdSet)

  // 直播硬广 ecpc 框架生效 tag
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, amdEcpcFrameworkEnableTagList);
  // 商品力抄袭品策略
  DEFINE_PROTOBUF_NODE_KCONF(CompetingItemConfigMap, ad.adRank2, competingItemConfigMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, competingSpuSet);
  // 中小
  DEFINE_PROTOBUF_NODE_KCONF(SmbHcConfigMap, ad.adRank2, smbHcConfigMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, nobidCaliExpConfig);
  // 内循环跑品
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerSelfServNonConvKconf);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerSmbBonusKconf);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerSmbDiscountKconf);
  DEFINE_TAILNUMBERV2_KCONF(ad.prerank_server, newCusAuthorIDTailAdmit, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.prerank_server, newCusAuthorIDTailAdmit_2, "");

  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, innerCtcvrCaliFollowItemBuyerConf);

  DEFINE_PROTOBUF_NODE_KCONF(SmbColdStartPageConf, ad.adRank, smbcoldstartpageConf)
  DEFINE_PROTOBUF_NODE_KCONF(SmbColdStartBuyerConf, ad.adRank, smbcoldstartbuyerConf)
  DEFINE_PROTOBUF_NODE_KCONF(HCSmbColdStartPageConf, ad.adRank, hcsmbcoldstartpageConf)
  DEFINE_PROTOBUF_NODE_KCONF(HCSmbColdStartBuyerConf, ad.adRank, hcsmbcoldstartbuyerConf)
  DEFINE_PROTOBUF_NODE_KCONF(HCSmbColdStartBuyerConf, ad.adRank3, innerSlideGfpThreshPageConf)
  DEFINE_PROTOBUF_NODE_KCONF(SmbColdStartBuyerConf, ad.adRank, smbULevelHCConf)
  DEFINE_PROTOBUF_NODE_KCONF(SmbColdStartBuyerConf, ad.adRank, smbPageHCConf)
  DEFINE_PROTOBUF_NODE_KCONF(SmbColdStartBuyerConf, ad.adRank, smbGMVLevelHCConf)
  DEFINE_PROTOBUF_NODE_KCONF(SmbColdStartBuyerConf, ad.adRank, smbGMVCVRHCConf)
  DEFINE_PROTOBUF_NODE_KCONF(SmbColdStartBuyerConf, ad.adRank3, innerUEGPMConf)
  DEFINE_PROTOBUF_NODE_KCONF(SmbColdStartBuyerConf, ad.adRank3, innerIndustryGPMCaliConf)
  DEFINE_PROTOBUF_NODE_KCONF(InnerIndustryUEConf, ad.adRank3, innerIndustryUEConf)
  DEFINE_PROTOBUF_NODE_KCONF(InnerIndustryGPMCaliConbineConf, ad.adRank3, innerIndustryGPMCaliConbineConf)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, innerIndustryCPMCaliCombineHiveConf)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank3, innerGpmUEpos);
  DEFINE_PROTOBUF_NODE_KCONF(InnerIndustryGPMCaliConbineConf, ad.adRank3,
                            innerIndustryCtcvrCaliCombineConf);
  // 内循环分人群 boost
  DEFINE_PROTOBUF_NODE_KCONF(InnerRetargetBoostConf, ad.adRank2, innerRetargetBoostConf);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, innerAdHostingSceneType);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, shelfExpressWhitelistBonustAccountIdList);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerShelfExpressBonus);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerShelfExpressBonusNew);
  DEFINE_LIST_NODE_KCONF(int64, ad.adRank2, innerShelfRetargetTagConf);
  // 内循环本地 Bonus account 白名单
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 8 >= IMPL_BLOCK_BEGIN and 8 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.adRank2, innerLspAccountWhiteListBonusMap);
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.adRank2, lspAccountWhiteListStorewideBonusMap);

  // 线索行业反哺计划白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, clueSupportAccountSet);
  // 每日留存重定向 ecpc 策略
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, EventRetentionDaysRetargetSet)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, EventRetentionDaysEcpcRatio, 1.0);

  // 内循环直播加速探索召回、粗排感知 Account 白名单
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, accIncrementAccountTailAdmit, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, accIncrementRoiWhite, "100;;;")

  // 生效校准模型范围配置
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, feedTrafficSkipCaliConfig);

  // 矩阵流量计费系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, MatrixFlowPriceAdjust);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, cpmFilterMatrixAppId);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, MatrixAppCpmThreMap);

  // 发现页外流 sctr 校准系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, feedSctrCalibrationConf);

  // 发现页 cvr 校准系数
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, cvrCaliModelConfigKey);

  // 关注页 [jiyang]
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, follow_subpageid_top3);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, follow_subpageid);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, followAddHardEpccConf);
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, followStoreWideUseGSP, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, followInnerNonStoreWideLiveUseGSP, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, followInnerNonStoreWideP2LUseGSP, "100;;;")
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, liveInnerEcpcConf);

  // 同城页 [liumingzong]
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, nearby_subpageid);

  // 外循环电商激活模型分桶加权
  DEFINE_PROTOBUF_NODE_KCONF(OuterEcomConvBucketWeightsConfig, ad.adRank2, outerEcomConvBucketWeightsConfig)

  // wengrunze
  DEFINE_PROTOBUF_NODE_KCONF(FormPmIntegrationWhiteListConf, ad.adRank2, formPmIntegrationModelWhiteSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, lpsUserSplitEcpcBlackAccountList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, lpsUserSplitEcpcBlackCorporationList);

  // yemengqing03
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, cluesUserSplitEcpcRatioParamMap);

  // uax 链路优选 [libingjie03]
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, formPmIntegrationAccountSet);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, fixedExploreRate, 1.0);
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, enableFixedExploreStrategy, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, enableModifyExploreStrategy, false);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, formPmIntegrationFirstIndustrySet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, formPmIntegrationSecondIndustrySet);
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, enableAddRecallChannel, false);

  // json
  DEFINE_JSON_NODE_KCONF(ad.adRank, adUserInfoClean)
  DEFINE_JSON_NODE_KCONF(ad.adserver2, recoUserInfoWhiteList)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank3, IaaAcquisitionGeneralizationHstScoreJson)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank3, IaaAcquisitionGeneralizationHstCmdIdJson)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, filterNativeRecallNegativeStatus)
  DEFINE_PROTOBUF_NODE_KCONF(PremiumSoftAdReviewConfig, ad.adMix, softAdDisableAdMarkWithReviewConf)
  DEFINE_PROTOBUF_NODE_KCONF(InspireStyleMultiPriorityConf, ad.adRank, inspireStyleMultiPriorityConf)
  DEFINE_PROTOBUF_NODE_KCONF(IncentiveEcpcConf, ad.adRank2, incentiveEcpcConf);

  // map<int64, double>
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 9 >= IMPL_BLOCK_BEGIN and 9 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(firefly.bonus, hardBonusProjectRatioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, rewardedEcpcPosIdSet)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adserver, accountRoiAdjust)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adserver, convBarUnitWhiteList)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, adJinniuRoiCaliConfig)
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.adRank, keyActionAccountMap)
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.adRank, keyActionAccountMap2)
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.adRank, keyActionColdStartExtremeAccountMap)
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.adRank, keyActionRetentionAccountMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, adPhoto2liveUnitTailSctrMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adserver, innerTestAccountRoasMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, fictionConvPayAccountWhiteList)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, sevenDaysLtvAcountMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, billingRatioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, AccountIdBonusMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, AccountIdRewardedRatioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, PhotoColdStartOcpxRaioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, PhotoColdStartOcpxImpsThrMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, rewardGameEcpcRatioMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, wegameReturntimeEcpcRatioMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, kMiniGameProductEcpcRatioMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, kMiniGameInspireEcpcV2RatioMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, kMiniGameInnerLiveProductEcpcRatioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, gameRoi7PriceRatio)
  DEFINE_PROTOBUF_NODE_KCONF(GameIaaRoi7DynamicAdjustConfig, ad.adRank2, gameIaaRoi7DynamicAdjustConfig);
  DEFINE_PROTOBUF_NODE_KCONF(GameIapLtv7DeprioritizeConfig, ad.adRank2, gameIapLtv7DeprioritizeConfig);
  DEFINE_PROTOBUF_NODE_KCONF(GameCtcvrFilterConf, ad.adRank2, gameCtcvrFilterConf);
  DEFINE_PROTOBUF_NODE_KCONF(GameCtcvrFilterConfbyOcpc, ad.adRank2, gameCtcvrFilterConfbyOcpc);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, gameIaaRoi7WhiteProductSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, rewardGamePages);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, overissueEcpcIndustryWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, overissueEcpcProductWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, keyActionUseLtvProductSet)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, overissueEcpcRatioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, overissueEcpcRatioGameMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, overissueEcpcRatioNcMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, phoneConnectAccountMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, RewardedCpm2PriceMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, gameLongValueProductPriorBid)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, gameShortValueProductRoiMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, indProductHcRatio);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, EduLpsEcpcThresMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, EduLpsDeepAllClassEcpcThresMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, adConvNextstayEcpcWhiteProduct);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, adConvNextstayEcpcWhiteAccount);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, adConvNextstayEcpcThresMap);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, indDropCreditRatio);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, indRtaHcRatio);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, gameLongValueProductListAuto)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, outerLiveGameConvEcpcAccountList)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerLiveGameConvEcpcAccountRatioMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adAutoParam, outerLiveFanEcpcRatioMap);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, gameLongValueProductLaunchList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, gameLongValueProductListManual)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, skipGpmHcIndustry)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, hc7rAccountList)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(firefly.bonus, softBonusProjectRatioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(firefly.bonus, polarisAccountHcRatio)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, orderPaiedJingXuanCaliMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, roasJingXuanCaliMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, gpmFansDaysCaliMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, NativeBonusAccountBoostMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.bidServer2, EspAuthorFansRatioBound)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, privateMessageAccountBonusMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, ClueEcologyBonusAccountSetInt);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, clueEcologyBonusWhiteOcpx);
  // 微信小程序扶持
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, accountWechatBidRatio)
  // 微小游爆品补贴
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, weGameNcExploreProductList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, weGameNcExploreOrientation)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, wegameNcExploreBonusProjectList);
    // 搜索广告 account 粒度出价系数调整
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchAccountBidBoost)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchBidwordAccountBidBoost)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, searchBidwordAccountBidBoostAuto)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, searchMobleAccountBidBoost)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchBidwordOcpcActionTypeBidBoost)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchAuthorCostRatio)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchIndustryCostRatio)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchQuickSearchAccountCostRatio)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchBidwordSearchAccountCostRatio)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchBidwordSearchOcpcCostRatio)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchRelevanceBadcaseRate)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, uePredictCacheSet)

  // 双列 drop high ctr account
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, dropHighCtrAccountMap)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank, inspireStylePriorityConf)
  // 小程序广告
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank, MicroAppCvrThrMap)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, playletModelConvTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, playletModelPanelTail)

  // ROI 预估值调整 account:campagintype,double
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, adRoasAccountModifyRateMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, adRoasProductModifyRateMap)

  DEFINE_LIST_NODE_KCONF(std::string, ad.model, adRoasDuanjuCalibrate)
  DEFINE_LIST_NODE_KCONF(std::string, ad.model, adRoasDuanjuCalibrateRealtime)
  DEFINE_LIST_NODE_KCONF(std::string, ad.model, adConvRoasCalibrate)
  DEFINE_LIST_NODE_KCONF(std::string, ad.model, adConvRoasCalibrateMiniApp)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, adEduLpsEnsembleBlackCorp)
  // [tengwei]
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopEcpcAccountWhiteListConfig)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopHCAccountWhiteListConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, FanstopEcommerceFollowULevelEcpcConfig)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, innerAccountBonusWhiteListRatio, 1.0)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, adInnerP2LProjectAccountOcpxBonus)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adtarget2, lspGeoRecallConf)
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, enableLocalLifePOISendSampleFix, true);
  DEFINE_PROTOBUF_NODE_KCONF(LocallifeHcToolsConf, ad.adRank2, locallifeHcToolsConf);
  DEFINE_PROTOBUF_NODE_KCONF(LocalLifeUserExploreScoreConf, ad.adRank3, OuterMedicalExploreScoreConf)
  DEFINE_PROTOBUF_NODE_KCONF(LocalLifeUserExploreMatchConf, ad.adRank3, OuterMedicalExploreMatchConf)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, OuterMedicalExploreOcpxConf)

#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 10 >= IMPL_BLOCK_BEGIN and 10 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, adInnerP2LBonusInvalidBizCenter)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerLSPUserBLevelEcpcRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, lspSearchPriceRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, adMerchantT7ROIPhotoBonusRatio);
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, enableLspPriceRatioAutoEcpcUnitTail, "100;0-99;;")

  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, qcpxBonusConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, InnerWhiteAccountDiscountRatio)

  DEFINE_JSON_NODE_KCONF(ad.adMix, softAdDisableAdMarkWithReviewJson);


  // map<string, double>
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, laProductAutobid)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, autoParamRatioMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, autoParamOneForceStart)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, autoParamOneForceStop)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, laProductOtherAutobid)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, retentionEveningProductBidCoef)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, laWchLpsRankParams)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, mcbEcpcCalibrationInit)
  DEFINE_STRING_DOUBLE_MAP_KCONF(firefly.bidServer, productOcpxSearchAndInspireWeight)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, insuranceRoiAgeLtvMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, NativeLowestCostLowerConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, NativeLowestCostUpperConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, NativeLowestCostLowerUnifyConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, NativeLowestCostUpperUnifyConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, specialtyLowestCostLowerConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, specialtyLowestCostUpperConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, specialtyLowestCostAccountLowerConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, specialtyLowestCostAccountUpperConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, specialtyLowestCostInitConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, specialtyLiveLowestCostLowerConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, specialtyLiveLowestCostUpperConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver, largePaymentUserProbStatMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver, creditGrantCpaRatioParams)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver, deepPredictRatioDiscountParams)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver, creditGrantOptParams)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, creditGrantProductRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver, creditGrantOptUpdateParams)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver, creditGrantOptUpdateMybParams)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver, LoanCreditRateDropMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, sevenDaysLtvSecretProductMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, sevenDaysPayTimesProductMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, fictionConvPayWhiteList)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.adRank, keyActionPostTargetRoi)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, carLpsWhiteList)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, adxOcpmDspWhiteListMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, adxOcpmDyCpmDspWhiteListMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adserver2, singleCreditScvrEcpcParams)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, ProductNameRewardedRatioMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, OcpcActionTypeLtvMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, OcpcActionTypeCvrThrMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, liveBonusParamMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, RankUnifyThrMap)  // [dingyiming05] rank 统一门槛配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, LiveInnerCpmThrMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, RankInnerUnifyThrMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, UeExplorePxrMap)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank2, adRankRecoPxtrModelGrpcMap)
  // 京快 阿里外投预测值调整
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, splashProductCpmThr)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, splashAccountCpmThr)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, EcomBonusMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, ProductNameBonusMap)
  // 搜索广告 product_name 粒度出价系数调整
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, searchBidwordProductBidBoost)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchQuickSearchProductBidBoost)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchQuickSearchProductBidBoostNew)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, searchQuickSearchProductBidBoostAuto)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, searchQuickSearchProductBidBoostAutoV2)
  DEFINE_PROTOBUF_NODE_KCONF(QuickSearchOcpxProductBoostManual,
     ad.adRank2, quickSearchOcpxProductBoostManual)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchQuickSearchProductCostRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchDspAmdProductBidBoost);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, FansTopMcbAlphaConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, FansTopMcbBetaConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, FansTopFollowMcbAlphaConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, FansTopFollowMcbBetaConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, amdCostCapAlphaConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, amdCostCapBetaConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, amdMcbAlphaConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, amdMcbBetaConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, fanstopSearchBoostRationByOcpxActionType)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, fanstopSearchBoostRationByOcpxActionTypeFeed)
  DEFINE_PROTOBUF_NODE_KCONF(SearchOcpxTypeBoostConfig, ad.adRank, searchFanstopOcpxTypeBoostConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchAdtypeCrossBoostConfig, ad.adRank2, searchAdtypeCrossBoostConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchBidtypeProductBoostConfig, ad.adRank3, searchBidtypeProductBoostConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, polarisLowWtrThresholdMap)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, searchQuickSearchProductBoostAutoV2Abtest)
  // 内粉 eCPC 作品订单固定 cpa bid
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, innerEcpcCpaBidSelect)  // 精选
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, innerEcpcCpaBidNebula)  // 极速
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, innerEcpcCpaBidNormal)  // 双列
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, LivePvMinuteCalcProportion)  // 流量分布比例累计
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, GlobalRateThreshold);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, onlineUnifyCxrCaliCommonBound);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, onlineUnifyCxrCaliManuallyBound);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adModelCalibrate, OfflineRateIntervention);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, adxCpcModelCaliRate);
  // 内粉圈人作品白名单 ecmp 调整配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, packageRecallInnerPhotoUpdateConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, InnerRefreshExpThresholdConfig)
  // 内粉北极星订单均价配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, innerFanstopPolarisBidMap)
  // 内粉各目标流量价值配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(reco.inner_fanstop, archimedesRankValuesMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(reco.inner_fanstop, expArchimedesRankPxtrFilterMap)  // 过滤阈值实验
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(
    reco.inner_fanstop, archimedesEcommerceRankPxtrFilterMap)  // 新内粉电商过滤
  // 内循环直播 ROAS
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, espLiveRoasGuardConfig)  // 内循环直播 roas 预测值兜底
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, espLiveRoasCaliConfig)  // 内循环直播 roas cali config
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, espLiveHighAtvUserCaliConfig)  // 内循环直播高客单价分层校准

  // 小程序广告打折
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, microAppDiscountConf)
  // 搜索 goods search ctr 系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchGoodsSearchLiveCpmRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchGoodsSearchPhotoCpmRatio)
  // 联盟
  // 垂类行业人群探索 付费短剧
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(
    ad.adRank, industryExploreSocialUserValueRatio)  // 分层人群价值系数 先验

  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, fanstopRankPxtrFilterMap)  // 粉条 cvr 过滤阈值
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, outerNativeDspCaliConfig)  // 外循环软广校准打折配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerHardDspCaliConfig)  // 外循环硬广校准打折配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, outerCtrIndustryCaliConfig)  // 外循环短剧行业 ctr 校准配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerGameInvokeLinkConfig)  // 外循环小游戏唤端链路配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(
    ad.adRank2, kMiniGameNativeEcpcAdjustConfig)  // 快小游软广 ecpc 修正系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(
    ad.adRank2, kMiniGameProductOffAvgLtv)  // 快小游关键行为下发 ltv 统计均值
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(
    ad.adRank2, playletProductOffAvgLtv)  // 付费短剧关键行为下发 ltv 统计均值
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2,
                                 nonCloseIaaProductOffAvgLtv)  // 非闭环 iaa 关键行为下发 ltv 统计均值
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(firefly.bidServer, outerNativeDspCaliConfigFireFly)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, storewideRoiHcCrowdConfig)  // 全店 ROI hc 分人群配置
  //  首发产品 hc 系数配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, shoufaCpmHcConf)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, searchBidwordFeedBonusConfig)  // 搜后推 bonus 扶持
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, searchAutoEcpcRatioBasedCostRate)  // 搜索基于成本自动 ecpc
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, searchOcpxTypeAdTypeBoost)  // 转化目标搜索广告类型 boost
  // 动态直播大卡相关性阈值
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, searchBigVCardDynamicRelThreshMap)

  // 商品卡模型离线校准系数配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, itemCardCmdPageCaliMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, itemCardCmdPageCaliMapV1)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, shelfP2lOfflineCvrCaliMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, shelfP2lOfflineLtvCaliMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, shelfPhotoMingtouOfflineCaliMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, shelfPhotoAntouOfflineCaliMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, shelfGylFeedOfflineCaliMap)
  // 货架精品池 list
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, shelfItemCardAuthorEcpcMap)
  // 直播卡 author ecpc 系数配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, shelfLiveWhiteEcpcAuthorMapV1)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, shelfLiveWhiteEcpcAuthorMapV2)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, shelfLiveWhiteEcpcAuthorMapV3)

  // 激励软广校准参数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, inspireSoftRoasCaliMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, inspireSoftOrderPaiedCaliMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, inspireSoftStorewideCaliMap)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, inspireSoftCaliPageIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, incentiveSimpleLivSupPages)

  // 商品卡猜喜校准系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, itemCardCaliMap)

  // map<int64, int64>
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 11 >= IMPL_BLOCK_BEGIN and 11 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.engine, inspireLiveSubpageConf);  // 激励直播 sub_pae_id 映射 page_id
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.adRank, pecRewardedTypeExitTime);  // PEC 激励营销不同任务截止时间
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.adRank, rankUeqThrMap);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank3, cacheHitGroupMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, fanstopLiveAudienceXtrThrMap);  // 粉条直播进人 Xtr 过滤阈值
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopLiveAudienceXtrUnitTails, "100;;;");  // 粉条直播进人 Xtr 过滤尾号实验  //NOLINT
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopLiveAudienceXtrAuthorTails, "100;;;");  // 粉条直播进人 Xtr 过滤尾号实验  //NOLINT

  // 小店赏罚门槛配置
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.adserver, twinBidStrategy)

  // 北极星人群包准入配置
  DEFINE_HASH_SET_NODE_KCONF(std::string, firefly.bonus, polarisUserOrientation);

  // 通信开卡 hc 策略 白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, firefly.bidServer, outerTongxinHcConfigFireFly);
  DEFINE_HASH_SET_NODE_KCONF(std::string, firefly.bidServer, OuterTongxinWhiteLIstConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(firefly.bidServer, TongxinHcRatio);

  // 金融深链路产品配置
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, indOnlineModelMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, rtaBonusRatioLimit);
  DEFINE_PROTOBUF_NODE_KCONF(FinanceUserTagIndConf, ad.adRank, financeUserTagIndConf)
  DEFINE_PROTOBUF_NODE_KCONF(FinanceProjectBonusConf, ad.adRank, financeProjectBonusConf)
  DEFINE_PROTOBUF_NODE_KCONF(NewPhotoGradeConf, ad.adRank, newPhotoGradeConf)
  DEFINE_PROTOBUF_NODE_KCONF(PreNewPhotoGradeConf, ad.adRank, preNewPhotoGradeConf)


  // 产品名到包名映射
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, ProductActivePackageName);
  // map<int64, string>
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.adRank, adPurchaseColdstartAccountProductMap)
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.adRank, adOrderSubmitColdStartAccountProductMap)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adModelCalibrate, pageIdWhiteMap)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adModelCalibrate, OcpcActionTypeWhiteList)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, espLiveHighAtvAdBuyerTypeCali);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank2, enableSearchOcpxUnifyR);  // 搜索 ocpx 独立 R 值
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.adRank, jiChenAccountList);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, exploreMobileSoftCaliMap);
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 12 >= IMPL_BLOCK_BEGIN and 12 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, uaxHcAdjustRatioMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, uaxHcAdjustRatioMapSecondIndustry);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, uaxHcAdjustRatioMapProductName);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, uaxHcAdjustRatioMapAccountId);

  DEFINE_INT64_KCONF_NODE(ad.adtarget2, IapCorePopulation, *********)
  DEFINE_INT64_KCONF_NODE(ad.adtarget2, IapPotentialPopulation, **********)
  // map<string, int32>
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.predict, model_cmd)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank, laBidEnhancementProductWhiteList);
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank, laBidEnhancementDefaultWhiteList);
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank, roasKpoWhiteProductNameType);
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adModelCalibrate, cmdIdWhiteList)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adModelCalibrate, OfflineIndustryList)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank, WangfuIndustryList)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank3, EcomInvokePromotionProductNames);
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank3, EcomPurchasePromotionProductNames);
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank, innerloopBonusProjectMap)
  DEFINE_PROTOBUF_NODE_KCONF(NoTagAdReviewConfig, ad.adRank, noTagAdReviewConfig);

  // map<string, bool> : (namespace, node_name)
  // redis 业务降级开关，重大故障紧急操作, TRUE=不降级，FALSE=降级
  DEFINE_STRING_BOOL_HASH_MAP_KCONF(ad.adRank, redisDropdownMap);

  // map<string, int64>
  DEFINE_STRING_INT64_MAP_KCONF(ad.adserver, abtestForAdForms)
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.adCmdConfigs, model_cmd_key)
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.adRank, NonConversionVisitorCvrOcpcMap)
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.adRank, pecFreqControlConfig)  // pec 深度激励频控
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.adRank2, outerLiveOcpxColdStartCost)

  // 游戏行业子领域补贴配置
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, gameOcpxCampaignTypeBonusConfig)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, gameProductBonusList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, miniGameExploreOrientation)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, miniGameExploreOrientationV2)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, miniGameExploreOrientationV3)


  // 教育行业深度优化配置
  DEFINE_PROTOBUF_NODE_KCONF(EduClueDeepOptConf, ad.adRank2, eduDeepOptHcConf)

  // map<string, string>
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 13 >= IMPL_BLOCK_BEGIN and 13 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, indDeepOptProductMap)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.model, adPurchaseYanhangProProductMap)

  // 20210709 add by zhouxuan06, plc source sign
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adRank, SearchAbDotName, "search_rank_bidword_model_exp_name")

  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, photoCrossFeatureRedis, "adSearchQueryPhotoStat")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, liveCrossFeatureRedis, "adSearchQueryLiveStat")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, q2AFeatureRedis, "adSearchQueryHotSequence")

  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adRank, rankPredictCacheRedis, "adEnginePrerankCache")

  // bool
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disablePvDataTraceLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerRankRawForRecallModel, true);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerRankGpmForRecallModel, true);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableNewGrayMonitor, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableRefreshCountManager, true);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enablePipelineSend, true);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableDotConflictPtCmdKey, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, openAmdAdRank, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, disableUnifyR, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableAdRankResultPass, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableMerchantROAS, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableAliPredict, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableModelCalibrate, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableBrandAdsCompare, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, usePriceDiscount, true)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, skipFilterDupCover, true)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableCpmGfp, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableFakeUserInfo, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableRealItemPs, true)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableDeepTargetRoi, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableDeepSingleBidBound, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableAuctionRecordDirect, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableIndustryExploreConvCostProtect, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, disableCmdTimeCostMonitor, false)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableTransferFanstopCandidate, true)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableTransferNativeCandidate, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableFanstopAuctionLog, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableForwardLinkBonusSwitch, true);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableAsyncGetData, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableNewPredictFill, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableDryrunLog, true);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableSpecialtyLiveLowestCostInitConfig, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, disableLiveAppDownload, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, useFollowLTVModel, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableSearchRecordDirect, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableTestEnvSkipFilter, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableSelfServiceRequestPurchase, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableOuterHighCostBonus, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerLoopAtvFea, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerLoopQcpxFea, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerLiveRoasFea, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enbleFillInfoQcpxLiveThresholdPrice, false);
  DEFINE_STRING_INT64_MAP_KCONF(ad.adRank, qcpxLivePecStyleAbtestForms);
  DEFINE_STRING_INT64_MAP_KCONF(ad.adRank, qcpxP2lSoftPecStyleAbtestForms);
  DEFINE_STRING_INT64_MAP_KCONF(ad.adRank, qcpxP2lHardPecStyleAbtestForms);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerQcpxLivePecStyleFea, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableQcpxLiveCtrLog, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerQcpxLiveCvrOneModelTagFea, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerQcpxAbtestParameterFea, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerQcpxLiveCvrOneModelBaggingTagFea, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerQcpxLiveCtrP2lOneModelTagFea, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableQcpxLiveGmvLog, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerQcpxLiveUseMainLtvFea, false);
  DEFINE_STRING_INT64_MAP_KCONF(ad.adRank, qcpxAbtestParameterForms);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableQcpxPhotoCouponCostLog, false);
  // 开屏相关
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableSplashGraph, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableSplashFilterAds, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableSplashToThanos, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableSplashModifyPs, true)
  // 用户体验框架下 ueq 系数调整开关
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableMicroAppCmd, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableMicroAppCmd2, true)
  // dpa 广告不走 ueq 过滤开关
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableDpaIsolation, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableAdPredictStatLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableNewKsnConfig, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableNonMerchantCvrCtr, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableNonMerchantTestLog, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableRankCopyFullPsContext, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableRankCopyTabType, true);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableFixNonMerchantLiveAuctionBidBug, false);
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableRankCandidateDelivery, false) /*下发率数据流开关*/
  // 下发率数据流是否加入品牌样本
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableCandidateBrandLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableAmdMcbStrategy, true)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableAmdMcbSkipEcpc, true)
  // 是否开启准入阶段的有效 posid 过滤
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableCandidateFollowData, false)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableFanstopLiveCandidateControl, false)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableFanstopPhotoCandidateControl, false)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableNearbySetupInnerFlow, false)
  // 内粉相关
  DEFINE_BOOL_KCONF_NODE(ad.inner_fanstop, enableInnerRegionLiveOverPhoto,
                         false);  // 内粉本地生活作品优先级最高开关
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableNewInnerRankDot, false);
  // 是否允许关注页使用 cpm / auto_cpa_bid 比例上限
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableArchimedesStandaloneTrace, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerSkipAuthorSet, false)
  // 搜索广告相关
  DEFINE_BOOL_KCONF_NODE(ad.adRank, searchGoodsTabPhotoCpmRemoveCtr, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, enableSearchAdxQueryRewriteOpt, false)
  // 曝光系数相关
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableServerShowRateInit, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableEspLiveRoasOrderCali, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableCostCapV2, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableRoiCostCapV2, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableOrderPayCostCapV2, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableDefCostCapV2, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, disableOuterNativeCandidate, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerloopLiveAllDomainSample, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerloopHardPhotoDiffSample, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerloopSoftPhotoDiffSample, false)

  // 词典平台管理类启动
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableDictManager, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, enableRankParallelInitProcessor, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableDictManagerLoad, false)

  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableAdxThanosServerShowCtr, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableSearchBidwordSupportDot, true)

  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableMobileCostcap, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, skipDiscountRatio, false)

  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableOtherHcTag, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, admitLpsAcquisitionGeneralization, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableEnsembleScoreRecord, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableFixModelCmdPtCheck, false);

  // int64
  DEFINE_INT64_KCONF_NODE(ad.adRank, maxRecordCmdNum, 10)
  DEFINE_INT64_KCONF_NODE(ad.adRank, maxAdCommonCount, 400)
  DEFINE_INT64_KCONF_NODE(ad.adRank, maxSearchStyleAdCommonCount, 100)
  DEFINE_INT64_KCONF_NODE(ad.adRank, rankingListExpireHours, 72)
  DEFINE_INT64_KCONF_NODE(ad.adRank, splashRankingListExpireHours, 72)
  DEFINE_INT64_KCONF_NODE(ad.adRank, knewsRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, rewardedRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, nativeRankingListExpire, 72)
  DEFINE_INT64_KCONF_NODE(ad.adRank, xdtAmdLiveRankingListExpire, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, dpaRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, processorRunTimeRecordRate, 10)
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, nativeAdMaxPrice, 100);
  DEFINE_INT64_KCONF_NODE(ad.adRank, retargetHcBranchId, 135);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, clueaggregatepageTopSameIndustryCount, 3)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 14 >= IMPL_BLOCK_BEGIN and 14 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, ocpmAccIncrementAccountIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, ocpmAccIncrementUnitIdSet)

  DEFINE_INT32_KCONF_NODE(ad.adserver2, maxSearchLiveSample, 2)
  DEFINE_INT32_KCONF_NODE(ad.adserver2, maxSearchPrerankHardSample, 4)
  DEFINE_INT32_KCONF_NODE(ad.adserver2, maxBidwordRecallExtraLive, 4)
  DEFINE_INT32_KCONF_NODE(ad.adserver2, maxBidwordRecallExtraPhoto, 8)
  DEFINE_INT32_KCONF_NODE(ad.adserver2, maxBidwordRecallExtra, 12)
  DEFINE_INT32_KCONF_NODE(ad.adserver2, maxSearchRecallExtraLive, 2)
  DEFINE_INT32_KCONF_NODE(ad.adserver2, maxSearchRecallExtraPhoto, 2)
  DEFINE_INT32_KCONF_NODE(ad.adserver2, maxSearchRecallExtra, 4)

  DEFINE_INT32_KCONF_NODE(ad.adserver2, bottomSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adserver2, maxExtraSampleSize, 0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver2, AckRankInfoSampleRate, 0.2)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, splashMonitorUnit);  // 开屏监控的 unit
  DEFINE_HASH_SET_NODE_KCONF(int64, firefly.bidServer, accountBiddingAccountSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, middlePageAccountSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, thanosMiddlePageAccountSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, DisableBonusDiscountProjectList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, ecpcAjustTagAvailableSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, gamePayModeAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, ecpcAjustTagAvailableSetMcb)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, promotionPcocCaliTool)
  // 平台电商门槛过滤总开发
  DEFINE_INT64_KCONF_NODE(ad.Rank, enableRequestEdsRatio, 100);
  DEFINE_INT64_KCONF_NODE(ad.adRank, searchPriceBound, 500)
  // 同城 粉条引擎 ack 放入 redis 降级开关
  // here zhoushuaiyin
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, innerLivePriorityAuctionBid, 0)
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, innerLiveNebulaPriorityAuctionBid, 0)
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, exploreMaxPsPhoto, 500)  // 发现页 请求 ps 的最大短视频数量
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, liveOrderPriceScale, 5)               // 直播订单降价比例
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, LowestCostStandardBudgetFen, 10000)
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, maxPsLive, 500)  // 请求 ps 的最大直播间数量 同城页 && 关注
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, nearbyMaxPsPhoto, 500)  // 同城页 请求 ps 的最大短视频数量
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, recordTracePercent, 10)  // 记录多少百分比的用户的详细 trace 信息
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, userDefinePhotoBidLow, 40)  // 用户自定义作品的直播 降价幅度
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, userDefinePhotoMinBid, 10)  // 用户自定义作品的直播 最低价格
  DEFINE_INT64_KCONF_NODE(ad.adRank, costCapV2UnitTail, 0)  // cost cap v2 尾号实验
  DEFINE_INT64_KCONF_NODE(ad.adRank, costCapV2RoiUnitTail, 0)  // cost cap v2 尾号实验 ROI
  DEFINE_INT64_KCONF_NODE(ad.adRank, costCapV2DefUnitTail, 0)  // cost cap v2 尾号实验 其他
  DEFINE_INT64_KCONF_NODE(ad.adRank2, searchStrongCardDynamicCpmThresh, 30143599)
  DEFINE_INT64_KCONF_NODE(ad.adRank2, searchBigVCardDynamicCpmThresh, 10000000);
  // int32
  DEFINE_INT32_KCONF_NODE(ad.adRank, refreshPageSize, 80)
  DEFINE_INT32_KCONF_NODE(ad.adrank, unifyMonitorSampleRatio, 5)
  DEFINE_INT32_KCONF_NODE(ad.adserver, predictMissRatio, 0)
  DEFINE_INT32_KCONF_NODE(ad.adserver, adServerSplashRankPredictProcessRatio, 100)
  DEFINE_INT32_KCONF_NODE(ad.adserver, deepCreditTwinBidStrategyDefault, 0)
  DEFINE_INT32_KCONF_NODE(ad.adserver, auctionLogRecordRatio, 100)
  DEFINE_INT32_KCONF_NODE(ad.adRank, valueIfAdFlowToolsRefreshTimeRecordNonExist, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, aliReqRatio, 100)
  DEFINE_INT32_KCONF_NODE(ad.adRank, searchLogFreq, 20)
  DEFINE_INT32_KCONF_NODE(ad.adRank, searchAdPositionOffset, 2)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, searchInnerFirstAdPosition, 3)
  DEFINE_INT32_KCONF_NODE(ad.adRank2, searchInnerFirstAdPositionOne, 1)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, searchInnerAdPositionInterval, 6)
  DEFINE_INT32_KCONF_NODE(ad.adRank, searchInnerFirstAdDownPosition, 5)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, searchAdPositionInterval, 6)
  DEFINE_INT32_KCONF_NODE(ad.adRank, searchLogRecordRatio, 100)
  DEFINE_INT32_KCONF_NODE(ad.adRank, searchDspAccountNewDay, 15)
  DEFINE_INT32_KCONF_NODE(ad.adRank, searchAccountBoost2CtrTail, 1)
  DEFINE_INT32_KCONF_NODE(ad.adRank, fillFanstopPostLivePcvrInfoByRpcRatio, 0);
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, searchRankSampleLogRatio, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, callAdFeatureIndex, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopCpmThresholdExpTailSet, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopLowerdownCpmthrWhitelist, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, t7ROISkipBillingSeparateUnitTail, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, EspAuthorFansPcocTail, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, EspEventOrderPaiedIsPayTail, "100;;;")
  // 降级相关开关
  DEFINE_INT32_KCONF_NODE(ad.adRank, monitorRecordRatio, 10)          // 监控打点比例降级
  DEFINE_INT32_KCONF_NODE(ad.adRank, bonusExceptionMonitorRatio, 30)  // 监控打点比例降级
  DEFINE_INT32_KCONF_NODE(ad.adRank, perfUnifySampleRatio, 10)        // 监控打点比例降级
  DEFINE_INT32_KCONF_NODE(ad.adserver2, enableReqPostServerRatio, 0)
  DEFINE_INT32_KCONF_NODE(ad.fanstopServer, FollowOrderProtectFansCnt, 100) /*关注页保护粉丝数阈值*/
  DEFINE_INT32_KCONF_NODE(ad.fanstopServer, rankCandidateDeliveryNeg, 9)    // 下发率数据流负样本数
  DEFINE_INT32_KCONF_NODE(ad.adModelCalibrate, accessCalibrateSvrRatio, 100)  // 访问校准服务流量比例
  // 流控 server show 写入 kafka 比例
  // 采样运行时的 perf 日志的千分位采样率
  DEFINE_INT32_KCONF_NODE(ad.fanstopServer, runtimePerfSamplePermillage, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, reqPidServerRatio, 100)
  DEFINE_INT32_KCONF_NODE(ad.adRank, reqRecoModelRatio, 101)
  DEFINE_INT32_KCONF_NODE(ad.adRank, rankStyleServerDropRatio, 101)
  DEFINE_INT32_KCONF_NODE(ad.adRank, revenueOptimizeBaseThrd, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, InnerloopSampleTopN, 1)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopLiveAddPositiveSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopLiveAddRankingTailSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopLiveRankingTotalLength, 30)
  // 硬广短视频差异性采样参数
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopHardPhotoAddDiffPosRankIdx, 5)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopHardPhotoAddDiffNegRankIdx, 80)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopHardPhotoAddDiffPosPrerankIdx, 100)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopHardPhotoAddDiffNegPrerankIdx, 20)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopHardPhotoAddDiffPosSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopHardPhotoAddDiffNegSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopHardPhotoAddTopPosSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopHardPhotoAddTailNegSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, rankBonusDiffCompareSample, 10000)

  // 软广短视频差异性采样参数
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopSoftPhotoAddDiffPosRankIdx, 5)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopSoftPhotoAddTopPosSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adRank, innerLoopSoftPhotoAddTailNegSampleSize, 0)

  DEFINE_INT32_KCONF_NODE(ad.adRank, rankfeatureIndexDiffRatio, 1)


  // double
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, fanstopPhotoAvgLtvEcpcScore, 1.05);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, smallGamePriceRatio, 1.2);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, merchantLtvUpperBound, 1000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, merchantLtvLowerBound, 0.001)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, cpaProductPriceRatioBound, 0.8)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, updownSlideDefaultCtr, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, servershowCvrForceRatio, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, roasUpperBound, 3.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, deepTargetRoasUpperBoundRatio, 4)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, deepTargetRoasLowerBoundRatio, 0.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, jkPriceDiscountRatio, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, globalConvCost, 100.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, aliDeepCvrDimRatio, 1000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, mcbConvNextstayRatio, 4.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, minAutoBidExploreCoef, 1e-4)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, duanjuMinAutoBidExploreCoef, 1e-4)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, twinBidCoefMax, 1.2)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, twinBidThresholdMax, 1.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, twinBidThresholdMin, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, twinBidCoefMin, 0.8)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, lowerCpmZoomFactorBoundary, 0.8);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, upperCpmZoomFactorBoundary, 1.5);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, ualSmoothConfTailList);
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, nativePhotoUpperGmv, 500.0)  // 软广短视频 gmv 上界
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, nativeLiveUpperGmv, 4.0)  // 软广直播 gmv 上界
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, nativeLiveThanosUpperGmv, 2.0)  // 软广直播单列 gmv 上界
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, merchantProductPriceLimit, 20000.0)  // 限时抢购商品价格过滤
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, ptdsEcomScoreExpendRatio, 1.0);  // 调整泛化模型预估值的区间
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, nativeRankCandidateRatio, 0.1);  // 软广数据流采样比例
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, roasTwinConvLtvBidUpperBound, 5.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, roasTwinDeepLtvBidUpperBound, 5.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, roiMcbQInit, 15.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, deepMiddleModelEnsambleRate, 1.0);
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 15 >= IMPL_BLOCK_BEGIN and 15 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_PROTOBUF_NODE_KCONF(NativeStatsAutoParamConfig, ad.adRank, nativeStatsAutoParamConfig)
  DEFINE_PROTOBUF_NODE_KCONF(NativeUnifyCpmThresholdConf, ad.adRank, nativeUnifyCpmThresholdConfig)
  // [yanqi08]
  // **** qcpx ****
  DEFINE_PROTOBUF_NODE_KCONF(QcpxCouponConfig, ad.adRank2, qcpxPhotoCouponConfig)
  DEFINE_PROTOBUF_NODE_KCONF(QcpxCouponConfig, ad.adRank2, qcpxLiveCouponConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxSubpageConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxShelfLiveAdmitSubpageid)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxFollowAdmitSubpageid)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxShelfPhotoAdmitSubpageid)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxRewardAdmitSubpageid)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxAddSubpageConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, qcpxFilterAuthorList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, qcpxFilterItemList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, qcpxOtherRoiAdjustAuthorList)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, qcpxPhotoRoiHourMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, qcpxLiveRoiHourMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank2, qcpxP2lRoiHourMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank3, qcpxP2lHardCvrCalibMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank3, qcpxP2lSoftCvrCalibMap)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxLiveAddSubpageConfig)
  DEFINE_PROTOBUF_NODE_KCONF(QcpxPackageConfigRelation, ad.adRank3, qcpxPackageConfigRelation)
  DEFINE_PROTOBUF_NODE_KCONF(QcpxPackageConfigRelationShelf, ad.adRank3, qcpxPackageConfigRelationShelf)
  DEFINE_PROTOBUF_NODE_KCONF(QcpxPackageConfigRelationReward, ad.adRank3, qcpxPackageConfigRelationReward)
  DEFINE_PROTOBUF_NODE_KCONF(QcpxPhotoShelfTargetStoreDiscConfig, ad.adRank3, qcpxPhotoShelfTargetStoreDiscConfig)  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(QcpxPackageConfigRelationShelfTarget, ad.adRank3, qcpxPackageConfigRelationShelfTarget)  // NOLINT
  // open author
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxLiveAuthorFilterList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxPhotoAuthorFilterList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxSpecificUserList)
  // dark author
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, qcpxLiveDarkAuthorList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxVauthorAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxVauthorPriceInterval)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, qcpxVauthorCateList)
  // live
  DEFINE_PROTOBUF_NODE_KCONF(QcpxCouponLib, ad.adRank2, qcpxCouponLib)
  DEFINE_PROTOBUF_NODE_KCONF(QcpxCouponAccess, ad.adRank2, qcpxCouponAccess)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, qcpxLiveUserRoiCoefMap)
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableQcpxLiveCtr, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableQcpxLiveP2lCtr, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableQcpxLiveP2lDiscCtr, false)
  // photo
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, qcpxPhotoUserRoiCoefMap)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableQcpxPhotoThresholdModeItemPrice, false)
  // other
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, qcpxPromotionAuthorList)

  // // [zhangyiwei03] cid qcpx
  // DEFINE_PROTOBUF_NODE_KCONF(CidQcpxStatStrategyConfig, ad.adRank2, cidQcpxStatStrategyConfig)
  // DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, cidQcpxAccountAdmitSet)
  // DEFINE_PROTOBUF_NODE_KCONF(CidQcpxExpConf , ad.adRank2, cidQcpxExpConf)
  // DEFINE_PROTOBUF_NODE_KCONF(CidQcpxFilterExpConf , ad.adRank2, cidQcpxFilterExpConf)

  // [wucunlin] cid 内循环 qcpx
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, cidCorporationNameSet)
  DEFINE_PROTOBUF_NODE_KCONF(CidQcpxExpConf , ad.adRank2, cidQcpxExpConf)  //业务形态未定，先复用

  // [linbowei]
  // promotion
  DEFINE_PROTOBUF_NODE_KCONF(QcpxStatStrategyConfig, ad.adRank2, qcpxPromotionStrategyConfig)

  DEFINE_PROTOBUF_NODE_KCONF(kconf::DspRewardedStyleFreqConf, ad.adRank, dspRewardedStyleFreqConf)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::SplashPriceDiscountConf, ad.adRank, splashPriceDiscountConf)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::SplashColdStartCoefConf, ad.adRank, splashColdStartCoefConf)
  DEFINE_TAILNUMBERV2_KCONF(ad.adtarget2, fanstopFcHoldBaseSet, "100;;;")  // 共用开关
  DEFINE_TAILNUMBERV2_KCONF(ad.adtarget2, fanstopRankFcPreSet, "100;;;")
  DEFINE_PROTOBUF_NODE_KCONF(SeqAdjustConf, ad.adRank, seqAdjustConfV2)
  DEFINE_PROTOBUF_NODE_KCONF(DiversityStrategyData, ad.adserver2, diversityStrategyData)
  DEFINE_PROTOBUF_NODE_KCONF(DiversityStrategyExp, ad.adserver2, diversityStrategyExp)
  DEFINE_PROTOBUF_NODE_KCONF(IndustryExpConfig, ad.adRank, industryExpConfig)
  DEFINE_PROTOBUF_NODE_KCONF(GuessLikeSubPageIdGmvRatioExpConf, ad.adRank, guessLikeSubPageIdGmvRatioExpConf)
  DEFINE_PROTOBUF_NODE_KCONF(GuessLikeSpuIdRatioExpConf, ad.adRank2, guessLikeSpuIdRatioExpConf)
  DEFINE_PROTOBUF_NODE_KCONF(SplashSpuIdRatioExpConf, ad.adRank, splashSpuIdRatioExpConf)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, initFlowControlRoas, 0.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, cltvBiasUpperBoundRatio, 2.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, mcbLpsSuperDeepFactor, 4.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, mcbRewardedKeyActionDiscount, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, mcbSearchDiscount, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, bizClick2BidRatio, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, auctionLogSendRatio, 0.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, minPriceRaiseRatio, 1.05);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, maxPriceRaiseRatio, 1.50);
  // [lixu05] 涨粉分关注长度调价屏蔽名单
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, espT7RoiConvRatioUpper, 3.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, espT7RoiConvRatiolower, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, espT7RoiConvRatioInit, 1.5);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, newSpuBoostTagList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, highBuyerTypeList);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank3, cidProductPriceMap);
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 16 >= IMPL_BLOCK_BEGIN and 16 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, shallowOcpxHcRatioConf);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, shallowDefaultHcRatio, 0.2);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, storewideBoostRatio, 1.0);

  // roas pltv 校准
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, photoRoasAtvUpper, 250.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, photoRoasAtvLower, 0.001);
  DEFINE_INT64_KCONF_NODE(ad.adRank, photoRoasMergeThresh, 30);
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, photoRoasLtvTailNew, "100;;;");
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, enablePhotoRoasPltvCaliExp, false)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank2, photoRoasPltvCaliTailExpState);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, photoRoasPltvCaliTailExpCoefMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, innerRoasCaliCloseTailSet);
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, enableInnerRoasCaliCloseTailExp, false)

  // 内粉相关
  DEFINE_DOUBLE_KCONF_NODE(ad.inner_fanstop, innerRegionLiveCtrThreshold, 0.006);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, storewideColdStartTail);
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 17 >= IMPL_BLOCK_BEGIN and 17 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.bidServer2, storewideColdStartState);
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, costDeltaMaxSecond, 600);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, fanstopShallowOcpxHcRatioConf);
  // 电商 roas 相关
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, merchantROIAdjustLTVRatio, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, merchantROASBoundAdjustRate, 1000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, ltvColdStartPriceThresh, 500.0)
  // 电商涨粉相关
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, merchantFollowConfigROI, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, merchantFollowROIAdjustParameter, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, merchantFollowLTVAlpha, 0.5)
  // 搜索广告相关
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchPriceOverBid, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchBidBoost, 80.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchLogSendRatio, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchGoodsTabPhotoCpmRatio, 0.00001)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchMingtouGoodsTabPhotoCpmRatio, 0.00001)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchGoodsTabLiveCpmRatio, 0.00001)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchBidwordSearchBounusCpabidCmpRatio, 2.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, pidRelevaneThreshold, 1.4)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, Pos2ReleThresPidUpperBound, 1.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, Pos2ReleThresPidLowerBound, 1.3)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, SearchPostitionLambda1, 0.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, SearchPostitionLambda2, 0.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, SearchPostitionReleMix1, 1.8)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, SearchPostitionReleMix2, 1.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, SearchPostitionBias1, 0.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, SearchPostitionBias2, 0.0)

  // 搜索广告 auction_bid 为 0 兜底逻辑参数
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, manualSetAuctionBidProb, 0.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, manualSetAuctionBidVal, 5.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchAdMaxBoost, 5.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchAdAppCardMaxBoost, 20.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, SearchLiveP3sLtvUpperBound, 0.0);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchCpcPriceRatioBound);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, searchBidwordSupportCpabidRatio, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, searchBidwordSupportMcbCpabidRatio, 0.5);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, searchStrongCardDynamicRelThresh, 2.0);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, searchDynamicStrongCardAccountSupport);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, searchSeriesCardDynamicRelThresh, 2.8);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, searchBigVCardDynamicRelThresh, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, nobidCvrResetInitCoef, 0.001)  // roas cvr 重置时 nobid 使用的初始化系数
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, nobidRoasInitRoi, 1)  // nobid roas 初始化 roi 系数
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, nobidRoiLowerBound, 0.1)  // nobid roas 的 roi 下限

  // 联盟 ecpc
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, ecpcAuctionLowerBoundRatio, 0.1);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, ecpcAuctionUpperBoundRatio, 4.0);

  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, rankCaliUpperBound, 5.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, rankCaliLowerBound, 0.2);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, rankModelCalibrationLowerBound, 0.75);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, rankModelCalibrationUpperBound, 1.25);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, nonMerchantAuctionBidMaxValue, 25.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, LowestCostPhotoThreshold, 640.0)     /*粉条 LowestCost 门槛*/
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, ctrFlowControlWeight, 0.007)         /*ctr 流控分权重*/
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, wtrFlowControlWeight, 1.0)           /*wtr 流控权重*/
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, roiLiveMaxGmv, 100000.0)      // ROI 直播预估 gmv 上限
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, roiAssoLiveMaxGmv, 200000.0)  // ROI 作品引流预估 gmv 上限
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, costCapAutoBidAlpha, 1.0)     // Cost Cap 预算参数
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, costCapAutoBidBeta, 1.0)      // Cost Cap 成本参数
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, amdMcbAutoBidAlpha, 1.0)     // amd mcb 预算参数
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, amdMcbAutoBidBeta, 1.0)      // amd mcb 成本参数
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, costCapAutoBidUpperBound, 1.0)  // Cost Cap 上限
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, costCapAutoBidLowerBound, 1.0)  // Cost Cap 下限
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, costCapAutoBidV2UpperBound, 1.0)  // Cost Cap V2 上限
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, costCapAutoBidV2LowerBound, 1.0)  // Cost Cap V2 下限
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, costCapCpcUpperBound, 1.0)  // Cost Cap V2 上限
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, costCapCpcLowerBound, 1.0)  // Cost Cap V2 下限
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, mobileLiveBidUpperBoundRate, 4.0)  //  移动端系统出价上限
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, mobileLiveBidLowerBoundRate, 0.1)  //  移动端系统出价下限
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, mobileCostCapInitBidRatio, 1.0)  // 移动端 Cost Cap 初始化出价
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, amdCostCapAutoBidUpperBound, 2.0)  // amd costcap 上限
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, amdCostCapAutoBidLowerBound, 0.1)  // amd costcap 下限
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, amdCostCapPUpperBound, 100.0)  // amd p upper bound
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, amdCostCapPLowerBound, 10.0)  // amd p lower bound
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, amdMcbAutoBidUpperBound, 2.0)  // amd mcb 上限
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, amdMcbAutoBidLowerBound, 0.1)  // amd mcb 下限
  // cpm / auto_cpa_bid 关注页系数上限
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, amdMcbPurfRatio, 1.0)          // amd mcb 日志打印频率
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, ueqUpperBound, 500.0)           // ueq 上界，单位分
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, ueqLowerBound, -500.0)          // ueq 下界，单位分
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, bxxlPhoto, 0.1)                 // UEQ：作品变现效率
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, bxxlLive, 0.5)                  // UEQ：直播变现效率
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, kNtrUeq, -150.0)                // UEQ：负反馈系数
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, playtimePhoto, 20.0)            // UEQ：作品播放时长
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, playtimeLive, 6.0)              // UEQ：直播播放时长
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, defaultSctrExplore, 0.6)     // 发现页默认 sctr
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, defaultSctrSelected, 0.8)    // 精选页默认 sctr
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, priorityLevelSctrExplore, 0.6)     // 流量券发现页默认 sctr
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, priorityLevelSctrSelected, 0.8)    // 流量券精选页默认 sctr
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, defaultSctrFollow, 0.85)     // 关注页默认 sctr
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, defaultSctrNearby, 0.4)      // 同城页默认 sctr
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopOcpmMaxEcpm, 5000.0)  // ocpm 粉条的最大 ecpm
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.fanstopServer, fanstopNobidMaxEcpmMap)  // 粉条 Nobid 个性化最大 ecpm
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, innerFanstopOcpmMaxEcpm, 3000.0)  // ocpm 内粉的最大 ecpm
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopLcMaxEcpmInner, 5000.0)  // ocpm 粉条的最大 ecpm
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopOcpmMaxEcpmInnerSelected, 5000.0)  // ocpm 粉条的最大 ecpm
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopOcpmMaxEcpmInnerNebula, 5000.0)  // ocpm 粉条的最大 ecpm
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopOcpmMaxEcpmInnerNormal, 5000.0)  // ocpm 粉条的最大 ecpm
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopEcpcMaxEcpmInnerSelected, 5000.0)  // ocpm 粉条的最大 ecpm
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopEcpcMaxEcpmInnerNebula, 5000.0)  // ocpm 粉条的最大 ecpm
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopEcpcMaxEcpmInnerNormal, 5000.0)  // ocpm 粉条的最大 ecpm
  // ocpm 内粉 直播 粉条 最大 ecpm
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopOcpmMaxEcpmInnerLiveSelected, 5000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopOcpmMaxEcpmInnerLiveNebula, 5000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopOcpmMaxEcpmInnerLiveNormal, 5000.0)
  DEFINE_DOUBLE_KCONF_NODE(reco.inner_fanstop, InnerLcExpMaxEcpm, 6000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fanstopFollowMixRankEcpmThreshold, 300.0)  // 关注页混排门槛
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, rankCandidateSampleRate, 0.0)  //  下发率数据流采样比例
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, LowestCostPassRatioCoeff, 0.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, LowestCostPhotoThresholdThanos, 1000.0)  // 单列门槛
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, fansOcpmLowerThresRatio, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, juxingSupplementMaxFeedEcpmV2, 280.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, juxingSupplementMaxThanosEcpmV2, 560.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, juxingSupplementPrice, 2.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, juxingSctr, 0.85)
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, mobilePhotoRoasInitRoi, 1.0)  // 移动端短视频 roas 初始 roi
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, FansCpmBrandSctr, 0.5)   //  品牌保量灵活调整 sctr
  DEFINE_DOUBLE_KCONF_NODE(ad.fanstopServer, FansCpmBrandLcMaxRatio, 2.0)   // 品牌保量接 LC 最大竞价
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, defaultBonusProjectAlpha, 0.000001);  // 默认补贴活动调节 alpha
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, nativeUnifyCxrPerfRatio, 0.1);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, AvgCvrLeadsCoeff, 0.2);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, EspLiveRoasAtvTail);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, EspLiveHostingRoasAtvTail);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, EspLiveStorewideRoasAtvTail);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adtarget3, innerProductTargetRefundRatioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adtarget3, innerAuthorTargetRefundRatioMap)
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.adRank3, innerProductIndustryRefundRatioMap)
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.adRank3, innerAuthorIndustryRefundRatioMap)
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.adRank3, innerProductIndustryRefundSupportRatioMap)
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.adRank3, innerAuthorIndustryRefundSupportRatioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.bidServer3, innerProductRefundSupportRatioMap)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.bidServer3, innerAuthorRefundSupportRatioMap)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, T7RoiNewCustomerSet);
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 18 >= IMPL_BLOCK_BEGIN and 18 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.bidServer2, EspLiveRoasEopEnsembleModel);

  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, poQuanCpmAvg, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, outerloopEeBoostRatioUpper, 7.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, outerloopEeBoostRatioLower, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, outerloopProdCaliRatioUpper, 1.5);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, outerloopProdCaliRatioLower, 0.7);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, adDuanjuPurchaseRatio, 1.0);  //短剧付费曝光打平


  // json load
  DEFINE_KCONF_NODE_LOAD(AppPageFormMaterialCpmThrConfig, ad.adserver, appPageFormMaterialCpmThrConfig)
  DEFINE_KCONF_NODE_LOAD(NebulaExpTradeCpmThrConfig, ad.adserver, nebulaExpTradeCpmThrConfig)
  DEFINE_KCONF_NODE_LOAD(ThanosInnerFlowExpTradeCpmThrConfig, ad.adserver,
                         thanosInnerFlowExpTradeCpmThrConfig)
  DEFINE_KCONF_NODE_LOAD(ExpTradeCpmThrConfig, ad.adserver, expTradeCpmThrConfig)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 19 >= IMPL_BLOCK_BEGIN and 19 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_KCONF_NODE_LOAD(OnlinePriceDiscountConfig, ad.adserver, onlinePriceDiscountConfig)
  DEFINE_KCONF_NODE_LOAD(DeepConvParamConfig, ad.adserver, deepConvParamConfig);
  DEFINE_KCONF_NODE_LOAD(engine_base::AppVersionControlConfig, ad.fanstopServer, appVersionControlConfig);
  DEFINE_KCONF_NODE_LOAD(InnerRecruimentPlcTailExpConfig, ad.fanstopServer, innerRecruimentPlcExpConfig)
  DEFINE_KCONF_NODE_LOAD(NearbyCpmThrConfig, ad.adRank2, nearbyCpmThrConfig)

  // protobuf
  DEFINE_PROTOBUF_NODE_KCONF(CidCustomizeDataV2, ad.adRank2, cidCustomizeDataV2)
  DEFINE_PROTOBUF_NODE_KCONF(SitePageInvokedAdWhiteConfig, ad.adRank, sitePageInvokedAdWhiteConfig)
  DEFINE_PROTOBUF_NODE_KCONF(ActivateAdvertiserWhiteConfig, ad.adRank, activateAdvertiserWhiteConfig)
  DEFINE_PROTOBUF_NODE_KCONF(ActivateAdvertiserBlackConfig, ad.adRank, activateAdvertiserBlackConfig)
  DEFINE_PROTOBUF_NODE_KCONF(LiveAdvertiserWhiteConf, ad.adRank, LiveAdvertiserWhiteConfig)
  DEFINE_PROTOBUF_NODE_KCONF(LiveAdvertiserBlackConf, ad.adRank, LiveAdvertiserBlackConfig)
  DEFINE_PROTOBUF_NODE_KCONF(InvokedAdvertiserBlackConfig, ad.adRank, invokedAdvertiserBlackConfig)
  DEFINE_PROTOBUF_NODE_KCONF(InspireFormAdvertiserWhiteConfig, ad.adRank, inspireFormAdvertiserWhiteConfig)
  DEFINE_PROTOBUF_NODE_KCONF(RewardedWhiteListFilterConf, ad.adRank, rewardedWhiteListFilterExpConf)
  DEFINE_PROTOBUF_NODE_KCONF(RewardedWhiteListFilterConf, ad.adRank, rewardedWhiteListFilterConf)
  DEFINE_PROTOBUF_NODE_KCONF(ChangeWatcherConfig, ad.adRank, ChangeWatcherConf)
  DEFINE_PROTOBUF_NODE_KCONF(InnerLoopCandidateConf, ad.adRank, innerLoopCandidateConf)
  DEFINE_PROTOBUF_NODE_KCONF(OlpRetargetConf , ad.algorithm, OlpRetargetConfigV2)
  DEFINE_PROTOBUF_NODE_KCONF(OlpRetargetDetails , ad.algorithm, OlpRetargetDetail)
  DEFINE_PROTOBUF_NODE_KCONF(IndDeepOptConf , ad.adRank, indDeepOptConf)
  DEFINE_PROTOBUF_NODE_KCONF(IndDeepOptConfV2 , ad.adRank, indDeepOptConfV2)
  DEFINE_PROTOBUF_NODE_KCONF(FinCreditRoiEcpcConf , ad.adRank, FinCreditRoiEcpcThres)
  DEFINE_PROTOBUF_NODE_KCONF(FinCreditRoiEcpcProductConf , ad.adRank, finCreditRoiEcpcProductConf)
  DEFINE_PROTOBUF_NODE_KCONF(UseCreditConf , ad.adRank, useCreditConf)
  DEFINE_PROTOBUF_NODE_KCONF(UserModelScoreConf , ad.adRank, userModelScoreConf)
  DEFINE_PROTOBUF_NODE_KCONF(UserModelScoreProductConf , ad.adRank, userModelScoreProductConf)
  DEFINE_PROTOBUF_NODE_KCONF(UpgradedIndustryModelEcpcConfClass , ad.adRank3, upgradedIndustryModelEcpcConf)
  DEFINE_PROTOBUF_NODE_KCONF(IndustryExporeUserConf , ad.adRank, industryExporeUserConf)
  DEFINE_PROTOBUF_NODE_KCONF(IndustrySocialExporeUserConf , ad.adRank, industrySocialExporeUserConf)
  DEFINE_PROTOBUF_NODE_KCONF(IndustryJinjiaoExporeUserConf , ad.adRank, industryJinjiaoExporeUserConf)
  DEFINE_PROTOBUF_NODE_KCONF(IndustryJiaotongExporeUserConf , ad.adRank, industryJiaotongExporeUserConf)
  DEFINE_PROTOBUF_NODE_KCONF(ClueAcquisitionHcConf , ad.adserver2, clueAcquisitionHc)
  DEFINE_PROTOBUF_NODE_KCONF(RealestateDeepOptConfig, ad.adRank, realestateDeepOptConf)
  DEFINE_PROTOBUF_NODE_KCONF(OlpTagBoostConf, ad.adtarget, olpTagBoostConf)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableInnerLoopUnifyCandidate, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableOuterLoopUnifyCandidate, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableOuterLoopLiveUnifyCandidate, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableOuterLoopCandidatePVHighValueSample, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableOuterLoopLiveCandidatePVHighValueSample, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, disableInnerFanstopInInnerLoopCandidate, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableDiffTestAddCmdInfo, true);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableIaapConditionFix, false);
  DEFINE_PROTOBUF_NODE_KCONF(MtProductAccountExp, ad.adRank, mtProductAccountExp);
  DEFINE_PROTOBUF_NODE_KCONF(MtProductAccountLaunch, ad.adRank, mtProductAccountLaunch);
  DEFINE_PROTOBUF_NODE_KCONF(PreMtProductAccount, ad.adRank, preMtProductAccount);
  DEFINE_PROTOBUF_NODE_KCONF(InnerMcdaProductAccountExp, ad.adRank, innerMcdaProductAccountExp);
  DEFINE_PROTOBUF_NODE_KCONF(InnerMcdaProductAccountLaunch, ad.adRank, innerMcdaProductAccountLaunch);
  DEFINE_PROTOBUF_NODE_KCONF(NovelConvPurchaseWhiteConf, ad.adRank, novelConvPurchaseWhiteConf);
  DEFINE_PROTOBUF_NODE_KCONF(McbEcpcUnitRange, ad.adRank, mcbEcpcUnitRange);
  DEFINE_PROTOBUF_NODE_KCONF(GameOptWhitelistConf, ad.adRank, gameOptWhitelistConf);
  DEFINE_PROTOBUF_NODE_KCONF(FristnPriceRatio, ad.adRank, accountFristnPriceRatio)
  DEFINE_PROTOBUF_NODE_KCONF(McdaUpExtraWhiteList, ad.adRank, mcdaUpExtraWhiteList)
  DEFINE_PROTOBUF_NODE_KCONF(RLRankEcpmAutoParam, ad.adAutoParam, RankEcpmAutoParamRL);
  DEFINE_PROTOBUF_NODE_KCONF(DefaultMiniGameCSubsidy, ad.adAutoParam, defaultMiniGameCSubsidy);
  DEFINE_PROTOBUF_NODE_KCONF(AdloadPredictArm, ad.adAutoParam, adloadpredictarm);
  DEFINE_PROTOBUF_NODE_KCONF(AdloadControl, ad.adAutoParam, adloadControlFollow);
  DEFINE_PROTOBUF_NODE_KCONF(AdloadControl, ad.adAutoParam, adloadControlFollowConf);
  DEFINE_PROTOBUF_NODE_KCONF(AdloadControl, ad.adAutoParam, adloadControlInnerExploreConf);
  DEFINE_PROTOBUF_NODE_KCONF(FastAdloadControlHP, ad.adRank, fastAdloadControlHP);
  DEFINE_PROTOBUF_NODE_KCONF(OuterLiveAdForceConfig, ad.adRank2, outerLiveAdForceConfig);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData2, ad.adAutoParam, totalUnifyAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData2, ad.adAutoParam, ftUnifyAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData2, ad.adAutoParam, adUnifyAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData2, ad.adAutoParam, InnerFanstopAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData2, ad.adAutoParam, IndustryAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(AdProphetConf, ad.adRank, adProphetConf);
  DEFINE_PROTOBUF_NODE_KCONF(WechatGameBonus, ad.adAutoParam, wechatGameBonus);
  DEFINE_PROTOBUF_NODE_KCONF(KuaiMiniGameBonus, ad.adAutoParam, KuaixiaoGameBonus);
  DEFINE_PROTOBUF_NODE_KCONF(GameShouFaConf, ad.adAutoParam, gameShouFaConf);
  // [nizhihao]
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adserver2, KminiGameDarkctrlSkipProductName);
  DEFINE_PROTOBUF_NODE_KCONF(MiniGameRtaSignMap, ad.adAutoParam, miniGameRtaSignMap);
  DEFINE_PROTOBUF_NODE_KCONF(MiniGamePlayTimeSignMap, ad.adAutoParam, miniGamePlayTimeSignMap);
  DEFINE_PROTOBUF_NODE_KCONF(MiniGameSubsidyControlMap, ad.adAutoParam, miniGameSubsidyControlMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adAutoParam, miniGameBigRExploreWhiteList);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adAutoParam, miniGameCpmThrdList);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adAutoParam, miniGameSubsidyThrdConf);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adAutoParam, bigGameSdkWhiteConf);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adAutoParam, social7RWhiteConf);
  DEFINE_PROTOBUF_NODE_KCONF(MiniGameRtaSignBucket, ad.adAutoParam, miniGameRtaSignBucket);
  DEFINE_PROTOBUF_NODE_KCONF(MiniGameRankSubSidyConf, ad.adAutoParam, miniGameRankSubSidyConf)
  DEFINE_PROTOBUF_NODE_KCONF(AdMapExpConf, ad.adAutoParam, autoPurchaseRoiCoef);
  DEFINE_PROTOBUF_NODE_KCONF(AdMapExpConf, ad.adRank, nativeQualityBonusCoef);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData2, ad.adRank, industryOrientationAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData2, ad.adRank2, gameShoufaForceAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData2, ad.adRank2, duanjuForceAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData2, ad.adRank2, playletForceHcControl);
  DEFINE_PROTOBUF_NODE_KCONF(IndustryPolarisStrategy, ad.adRank, industryPolarisStrategy);
  DEFINE_PROTOBUF_NODE_KCONF(IndustryPolarisTagScoreConfigConf, ad.adRank2, IndustryPolarisTagScoreConfig);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlExpConfig, ad.adAutoParam, unifyAdloadControlExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(UnifyAdloadControlData, ad.adAutoParam, unifyAdloadControlOutput);
  DEFINE_PROTOBUF_NODE_KCONF(DuanJuXinJuAutomaticKconf, ad.adRank, duanJuXinJuAutomaticKconf);
  DEFINE_PROTOBUF_NODE_KCONF(PoQuanPidStrategyKconf, ad.adRank2, poQuanPidStrategyKconf);
  DEFINE_PROTOBUF_NODE_KCONF(OuterLoopCalibratedRatioForPrerank, ad.adRank3, calibratedRatioForPrerank);
  DEFINE_PROTOBUF_NODE_KCONF(BigPromotionGpmHcConf, ad.adAutoParam, bigPromotionGpmHcConf);
  DEFINE_PROTOBUF_NODE_KCONF(MixRankBidControlParams, ad.adAutoParam, mixRankBidControlParams);
  DEFINE_PROTOBUF_NODE_KCONF(InnerAccountAdmitClass, ad.adAutoParam, innerAccountAdmit);
  DEFINE_PROTOBUF_NODE_KCONF(CommercialUserGroupClass, ad.adAutoParam, commercialUserGroup);
  DEFINE_PROTOBUF_NODE_KCONF(KuaiGameInspireConfClass, ad.adAutoParam, kuaiGameInspireConf);
  DEFINE_PROTOBUF_NODE_KCONF(ModelBasedAdloadControl, ad.adAutoParam, modelBasedAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(ModelBasedAdloadControl, ad.adAutoParam, modelBasedIndustryAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(ModelBasedAdloadControl, ad.adAutoParam, modelBasedUserAdloadControl);
  DEFINE_PROTOBUF_NODE_KCONF(BillingSeparateAccountExpConf, ad.frontserver, billingSeparateAccountExpConf);
  DEFINE_PROTOBUF_NODE_KCONF(InvokeEcpcConf, ad.adRank, invokeEcpcConf);
  DEFINE_PROTOBUF_NODE_KCONF(UserGroupThrExpConf, ad.adRank, userGroupThrExpConf);
  DEFINE_PROTOBUF_NODE_KCONF(UserGroupThrExpConf, ad.adRank2, splashUserGroupThrExpConf);
  DEFINE_PROTOBUF_NODE_KCONF(SplashRTBSensitiveUserCpmThrExpConf, ad.adRank2,
                             splashRTBSensitiveUserCpmThrExpConf);
  DEFINE_PROTOBUF_NODE_KCONF(HighPcvrMerchantMacUserConfig, ad.adRank, highPcvrMerchantMacUserConfig);
  DEFINE_PROTOBUF_NODE_KCONF(UserAdLtvLevelRndHcRatio, ad.adRank2, userAdLtvLevelRndHcRatio);
  DEFINE_PROTOBUF_NODE_KCONF(RndPhotoColdStartFactor, ad.adRank2, rndPhotoColdStartFactor);
  DEFINE_PROTOBUF_NODE_KCONF(engine_base::PurchaseRoiFuseConf, ad.adRank, purchaseRoiFuseConf);
  DEFINE_PROTOBUF_NODE_KCONF(PurchaseRoiHcConf, ad.adRank, purchaseRoiHcConf);
  DEFINE_PROTOBUF_NODE_KCONF(Cpm2TtlBucket, ad.adRank2, cpm2TtlBucket);
  DEFINE_PROTOBUF_NODE_KCONF(MultiPredictBoundValueConfigPb, ad.adRank, multiPredictBoundValueConfig);
  DEFINE_PROTOBUF_NODE_KCONF(UserVauleGroupEcpcConf, ad.adRank2, userVauleGroupEcpcConf);
  DEFINE_PROTOBUF_NODE_KCONF(HcHoldoutConfig, ad.adRank2, hcHoldoutConfig);
  DEFINE_PROTOBUF_NODE_KCONF(RankAdlistCacheQuotaConfig, ad.adRank2, rankAdlistCacheQuotaConfig);
  DEFINE_PROTOBUF_NODE_KCONF(RankAdlistCacheEntranceConfig, ad.adRank2, rankAdlistCacheEntranceConfig);
  DEFINE_PROTOBUF_NODE_KCONF(RankCacheEntranceConfig, ad.adRank3, rankScoreCacheEntranceConfig);
  DEFINE_PROTOBUF_NODE_KCONF(Cpl2CorpFilterIndustryConfig, ad.adRank2, cpl2CorpFilterIndustryConfig);
  // 统一 ctr/cvr 纠偏工具
  DEFINE_PROTOBUF_NODE_KCONF(OnlineUnifyCtrCaliManuallyConfig, ad.adRank, onlineUnifyCtrCaliManuallyConfig)
  DEFINE_PROTOBUF_NODE_KCONF(OnlineUnifyCvrCaliManuallyConfig, ad.adRank, onlineUnifyCvrCaliManuallyConfig)
  DEFINE_PROTOBUF_NODE_KCONF(OnlineUnifyDcvrCaliManuallyConfig, ad.adRank, onlineUnifyDcvrCaliManuallyConfig)
  DEFINE_PROTOBUF_NODE_KCONF(OnlineUnifyLtvCaliManuallyConfig, ad.adRank, onlineUnifyLtvCaliManuallyConfig)
  DEFINE_PROTOBUF_NODE_KCONF(CommonOfflineCalibrateWhite, ad.adRank, commonOfflineCalibrateWhite)
  DEFINE_PROTOBUF_NODE_KCONF(CommonOfflineCalibrateBlack, ad.adRank, commonOfflineCalibrateBlack)
  // 外循环直播计费打折工具
  DEFINE_PROTOBUF_NODE_KCONF(OuterLivePriceDiscountConfig, ad.adRank2, outerLivePriceDiscountConfig)
  DEFINE_PROTOBUF_NODE_KCONF(WhiteListMapForEcpc, ad.adRank3, whiteListMapForEcpc)
  DEFINE_PROTOBUF_NODE_KCONF(WhiteListMapForRtaBid, ad.adRank3, whiteListMapForRtaBid)
  // 精排原始预估值校准配置
  DEFINE_PROTOBUF_NODE_KCONF(ModelOriginScoreCalibrationConfig, ad.adRank3, modelOriginScoreCalibrationConfig)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, modelOriginScoreCalibrationMax, 1.2)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, modelOriginScoreCalibrationMin, 0.8)

  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, unifyModelIndustryBoundConfig);
  // 统一 cxr drop 工具
  DEFINE_PROTOBUF_NODE_KCONF(OnlineUnifyCtrDropManuallyConfig, ad.adRank, onlineUnifyCtrDropManuallyConfig)
  DEFINE_PROTOBUF_NODE_KCONF(OnlineUnifyCvrDropManuallyConfig, ad.adRank, onlineUnifyCvrDropManuallyConfig)
  DEFINE_PROTOBUF_NODE_KCONF(OnlineUnifyDcvrDropManuallyConfig, ad.adRank, onlineUnifyDcvrDropManuallyConfig)
  DEFINE_PROTOBUF_NODE_KCONF(AdxCpmTaxesConf, ad.adRank, AdxCpmTaxesConfig)
  // 海外流量产品屏蔽工具
  DEFINE_PROTOBUF_NODE_KCONF(OverseasTrafficManuallyConfig, ad.adRank2, overseasTrafficManuallyConf)
  // 金牛移动端 OCPC 出价实验
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileCostcapOcpxUnitTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileLiveTransPcOcpmOcpxUnitTail)
  DEFINE_PROTOBUF_NODE_KCONF(UalDefConfStruct, ad.adems, ualDefConf);  // UAL 定义配置

  // 本地行业 ecpc
  DEFINE_PROTOBUF_NODE_KCONF(LALpsValidCluesProductEcpcConfig, ad.adRank,
                             laLpsValidCluesProductEcpcConfig)

  // 多触点
  DEFINE_PROTOBUF_NODE_KCONF(FederatedWhitelistConfig, ad.adRank, federatedWhitelistConfig)

  DEFINE_PROTOBUF_NODE_KCONF(OutsideEcpcConf, ad.adRank, adRoasDropConf)
  DEFINE_PROTOBUF_NODE_KCONF(TargetSearchKconfLoadConf, ad.frontserver, targetSearchKconfLoadConf)
  DEFINE_PROTOBUF_NODE_KCONF(SearchAdDynamicPositionConfig, ad.frontserver, searchAdDynamicPositionConfig);
  DEFINE_PROTOBUF_NODE_KCONF(OutsideEcpcConf, ad.adRank, outsideEcpcConf)
  DEFINE_PROTOBUF_NODE_KCONF(OutsideEcpcConfDetail, ad.adRank, outsideEcpcConfDetail)
  DEFINE_PROTOBUF_NODE_KCONF(OutsideEcpcConf, ad.adRank, convQualityEcpcParams)

  DEFINE_PROTOBUF_NODE_KCONF(MerchantPlatformHcConf, ad.adRank, merchantPlatformHcConf)
  DEFINE_PROTOBUF_NODE_KCONF(DirectMerchantMcdaConf, ad.adRank2, directMerchantMcdaConf)
  DEFINE_PROTOBUF_NODE_KCONF(DirectMerchantMcdaConf, ad.adRank2, convQualityEcpcStatConf)
  DEFINE_PROTOBUF_NODE_KCONF(DirectMerchantMcdaConf, ad.adRank2, directMerchantSearchConf)
  DEFINE_PROTOBUF_NODE_KCONF(CidGoodsTypeSupportNewConf, ad.adserver2, cidGoodsTypeSupportNewConf)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 20 >= IMPL_BLOCK_BEGIN and 20 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver2, cidGoodsTypeAccountConf)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.model, iaaIncentivePosIds)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.model, iaaIncentivePageIds)
  DEFINE_PROTOBUF_NODE_KCONF(RewardCvrReviseConf, ad.adRank2, rewardCvrReviseConf)
  DEFINE_PROTOBUF_NODE_KCONF(HcPidKconf, ad.adRank2, hcPidKconf)
  DEFINE_PROTOBUF_NODE_KCONF(SdpaItemExploreConf, ad.adRank, sdpaItemExploreHcConf)
  DEFINE_PROTOBUF_NODE_KCONF(CidSearchConf, ad.adRank2, cidSearchConf)

  DEFINE_PROTOBUF_NODE_KCONF(AdPositionColdStartRerankData, ad.adRank, adPositionColdStartRerankConfig)
  DEFINE_PROTOBUF_NODE_KCONF(AppPersonalRatioRerankConfig, ad.adRank, appPersonalRatioRerankConfig)
  DEFINE_PROTOBUF_NODE_KCONF(RetentionThresholdToolConfig, ad.adRank, mainRetentionTool);
  DEFINE_PROTOBUF_NODE_KCONF(EcpcProductAndUnitConfigData, ad.adRank, ecpcProductAndUnitConfig)
  DEFINE_PROTOBUF_NODE_KCONF(MerchantMcbOcpxUnitTailMap, ad.bidServer, merchantMcbOcpxUnitTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(HighQualityPhotoRatioConfigData, ad.adRank, HighQualityPhotoRatioConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SkipChargeWhiteListConfigData, ad.adRank, skipChargeWhiteListConfig)
  DEFINE_PROTOBUF_NODE_KCONF(WanheCpmWhitelistConf, ad.adRank, WanheCpmWhitelist)
  DEFINE_PROTOBUF_NODE_KCONF(ChargePriceDefenderConfig, ad.adRank, chargePriceDefender)

  DEFINE_PROTOBUF_NODE_KCONF(AbTestName, ad.adRank, abSearchTestName)
  DEFINE_PROTOBUF_NODE_KCONF(ProphetEcpcConfigs, ad.adRank, ProphetEcpcConfig);



  DEFINE_PROTOBUF_NODE_KCONF(MtPayProductWhitelistConfig, ad.adRank, mtPayProductWhitelistBidConfig)
  DEFINE_PROTOBUF_NODE_KCONF(MtLpsProductWhitelistConfig, ad.adRank, mtLpsProductWhitelistBidConfig)
  DEFINE_PROTOBUF_NODE_KCONF(MtCovProductWhitelistConfig, ad.adRank, mtCovProductWhitelistBidConfig)

  // 正价课
  DEFINE_PROTOBUF_NODE_KCONF(IrregAllLessonProductWhitelistConfig, ad.adRank,
                             irregAllLessonProductWhitelistConfig)

  // 搜索广告 cpm 阈值管理
  DEFINE_PROTOBUF_NODE_KCONF(RankSearchCpmThrConfig, ad.adRank, rankSearchCpmThrConfig)
  DEFINE_PROTOBUF_NODE_KCONF(RankSearchCpmTagConfig, ad.adRank2, rankSearchCpmTagConf)
  DEFINE_PROTOBUF_NODE_KCONF(RankSearchCpmThrAdjustConfig, ad.adRank2, rankSearchCpmThrAdjustConfig)
  DEFINE_PROTOBUF_NODE_KCONF(RankSearchMultilistCpmThrConfig, ad.adRank2, rankSearchMultilistCpmThrConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchSuperStrongAccount, ad.adRank3, searchSuperStrongAccount)
  DEFINE_PROTOBUF_NODE_KCONF(SplashInnerOcpxCaliCoefConfig, ad.adRank, splashInnerOcpxCaliCoefConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SplashInnerOcpxCpmThrConfig, ad.adRank, splashInnerOcpxCpmThrConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SplashBoostConfig, ad.adRank, splashBoostConfig)
  DEFINE_PROTOBUF_NODE_KCONF(BonusProjectAccountIdMap, ad.adRank, bonusProjectAccountIdMap)
  // 搜索广告 rele x cpm 阈值【实验使用，无推全预期】
  DEFINE_PROTOBUF_NODE_KCONF(RankSearchCpmThrWithReleConfig, ad.adRank, rankSearchCpmThrWithReleConfig)
  // 搜索广告分导流源分召回策略 cpm 阈值配置
  DEFINE_PROTOBUF_NODE_KCONF(SearchDiversionSourceCpmThrConfig, ad.adRank, searchDiversionSourceCpmThrConfig)
  // 搜索广告 account 粒度 cpm 阈值
  DEFINE_PROTOBUF_NODE_KCONF(SearchCpmThrConfig, ad.adRank, searchCpmThrConfig)
  // 搜索广告明投 bonus
  DEFINE_PROTOBUF_NODE_KCONF(SearchBidwordBonusConfig, ad.adRank, searchBidwordBonusConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchBidwordBonusConfig, ad.adRank, searchQuickBonusConfig)
  // 搜索广告内循环 bonsu
  DEFINE_PROTOBUF_NODE_KCONF(SearchInnerBonusConfig, ad.adRank, searchInnerBonusConfig)
  // 搜索广告同账户保留 creative 个数
  DEFINE_PROTOBUF_NODE_KCONF(SearchUniqCreativeConfig, ad.adRank, searchUniqCreativeConfig)
  // 搜索广告相关性 bonus
  DEFINE_PROTOBUF_NODE_KCONF(SearchRelevanceBonusConfig, ad.adRank, searchRelevanceBonusConfig)
  // 搜索广告相关性 age cpm threshold [gaokaiming]
  DEFINE_PROTOBUF_NODE_KCONF(SearchAdsAgeCpmThreshold, ad.adRank, searchAdsAgeCpmThreshold)
  // 小游戏直跳暗改实验配置
  DEFINE_PROTOBUF_NODE_KCONF(SmallGameForceDirectAbTestConf, ad.adRank, smallGameForceDirectAbTestConf)
  // 搜索广告明投扶持 v3 配置参数
  DEFINE_PROTOBUF_NODE_KCONF(SearchBidwordExtraRankBenefitConfig, ad.adRank,
                            searchBidwordExtraRankBenefitConfig)
  // 搜索广告购物频道扶持参数
  DEFINE_PROTOBUF_NODE_KCONF(SearchGoodTabBonusConfig, ad.adRank,
                             searchGoodTabBonusConfig)
  // 搜索广告 kbox 扶持参数
  DEFINE_PROTOBUF_NODE_KCONF(SearchItemKboxBonusConfig, ad.adRank2,
                       searchItemKboxBonusConfig)
  // 搜索广告购物频道扶持参数
  DEFINE_PROTOBUF_NODE_KCONF(SearchRecommendQueryBonusConfig, ad.adRank2,
                             searchRecommendQueryBonusConfig)
  // 搜索广告明投跨产品扶持白名单
  DEFINE_PROTOBUF_NODE_KCONF(SearchBidwordSupportDictConfig, ad.adRank,
                             searchBidwordSupportDictConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchBidwordSupportSetConfig, ad.adRank2,
                             searchBidwordSupportSetConfig)

  // 搜索分位置预估 bias
  DEFINE_PROTOBUF_NODE_KCONF(SearchPosSctrBiasConfig, ad.adRank,
                             searchPosSctrBiasConfig)

  // 搜索广告分位置权重配置
  DEFINE_PROTOBUF_NODE_KCONF(SearchPosDiscountConfig, ad.adRank,
                             searchPosDiscountConfig)
  // 搜索广告分 ocpc_action_type 的调价
  DEFINE_PROTOBUF_NODE_KCONF(SearchOcpxTypeBoostConfig, ad.adRank, searchOcpxTypeBoostConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchStrongCardOcpxTypeBoostConfig, ad.adRank2,
                             searchStrongCardOcpxTypeBoostConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchOcpxTypeBoostConfig, ad.adRank, searchLiveOcpxTypeBoostConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchOcpxTypeBoostConfig, ad.adRank, searchLiveOcpxBidwordBoostConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchIndustryBoostConfig, ad.adRank, searchIndustryBoostConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchAuthorBoostConfig, ad.adRank, searchAuthorBoostConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchProductNameTwinAchieveConf, ad.adRank,
                                  SearchTwinAchieveRateControlWhiteList)

  // 搜索广告分 搜索来源 的调价
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, searchEnterSourceBidBoost)
  DEFINE_PROTOBUF_NODE_KCONF(SearchDiversionSourceBoostConfig, ad.adRank, searchDiversionSourceBidBoostConfig)

  // 搜索广告内流分策略 top4 相关性阈值
  DEFINE_PROTOBUF_NODE_KCONF(SearchInnerStreamPosConfig, ad.adRank2, searchInnerStreamPosConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchProductNameBoundedBoost, ad.adRank2, searchProductNameBoundedBoost)
  // DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, searchProductNameBoundedBoost)

  DEFINE_PROTOBUF_NODE_KCONF(SearchDynamicStrongCardConfig, ad.adRank2, searchDynamicStrongCardConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SearchDynamicStrongCardAccountBoostConfig, ad.adRank3,
                      searchDynamicStrongCardAccountBoostConfig)
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableBigvDynamicThreshAbtest, true);
  // 搜索广告强样式黑名单
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.queryRetrieval, appcardBlackQueryList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.queryRetrieval, formCardBlackQueryList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.queryRetrieval, seriesCardBlackQueryList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.queryRetrieval, bigVCardBlackQueryList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.queryRetrieval, dynamicStrongCardBlackQueryList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, dynamicBigvWhiteQueryList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, searchDynamicAppCardBlackProductname);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, subsidyIaapWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adRank2, searchDynamicStrongCardBlackRecall);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.queryRetrieval, searchBigvExpBlackCity);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchImageTextCardMinVersion, "12.4.10")
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, searchBigvLiveFromPageWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, searchBigvLiveOtherSearchSource);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, searchBigvLiveAuthorIdBlacklist);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adRank3, searchRankExpName, "search_engine_group_name");
  // 搜索直投创意优选池
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, recoAfterSearchProductNameBlacklist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adCreativeServer, searchMingtouProductNameBlacklist);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adCreativeServer, searchMingtouAccountIdWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, ocpcPosidFilterConf);
  DEFINE_PROTOBUF_NODE_KCONF(SearchKboxReleThreshConf, ad.frontserver, searchKboxReleThreshConf)

  DEFINE_BOOL_KCONF_NODE(ad.adrank, enableSearchDebugCpc, false)
  DEFINE_BOOL_KCONF_NODE(ad.adrank, disableArchimedesFillBonusInfo, true)
  // pec 金币选择
  DEFINE_PROTOBUF_NODE_KCONF(PecPersonalizationCoinNum, ad.adRank, pecPersonalizationCoinNum)  // 金币配置
  DEFINE_LIST_NODE_KCONF(double, ad.adRank, pecInvokedPersonalizationCoinConfig)  // 拉活配置
  DEFINE_LIST_NODE_KCONF(double, ad.adRank, pecConvPersonalizationCoinConfig)  // 激活配置
  DEFINE_LIST_NODE_KCONF(double, ad.adRank, pecPayPersonalizationCoinConfig)  // 付费配置
  DEFINE_PROTOBUF_NODE_KCONF(PecParamsConfig, ad.adRank, pecParamsConfig)
  DEFINE_PROTOBUF_NODE_KCONF(PecCouponDiscountConfig, ad.adRank2, pecCouponDiscountConfig)
  DEFINE_PROTOBUF_NODE_KCONF(PecCouponDiscountConfig, ad.adRank2, DeepRewardedCoinConfig)
  DEFINE_PROTOBUF_NODE_KCONF(PecRewardedPointMockConfig, ad.adRank, pecRewardedPointMockConfig)
  DEFINE_PROTOBUF_NODE_KCONF(IncentiveAdMockConfig, ad.adRank, incentiveAdMockConfig)
  DEFINE_PROTOBUF_NODE_KCONF(MicroVideoAdvertiserWhiteConf, ad.adRank, MicroVideoAdvertiserWhiteConfig)
  DEFINE_PROTOBUF_NODE_KCONF(InnerLoopProjectBonusConf, ad.adRank, innerLoopProjectBonusConf)
  DEFINE_PROTOBUF_NODE_KCONF(RevertBonusTagsConf, ad.adRank, revertBonusTags)
  DEFINE_PROTOBUF_NODE_KCONF(RevertBonusTagsConf, ad.adRank3, adForceRecoValidTags)
  DEFINE_PROTOBUF_NODE_KCONF(GuessYouLikeMobileSctrConf, ad.adRank, guessYouLikeMobileSctrConf)
  DEFINE_PROTOBUF_NODE_KCONF(TieredColdstartStrategyConf, ad.adRank, tieredColdstartStrategyConf)

  // [wangzixu05]
  DEFINE_PROTOBUF_NODE_KCONF(SensitiveThirdCategoryConfig, ad.adRank2, sensitiveThirdCategoryConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UserValueLevelCpmQuantile, ad.adRank2, userValueLevelCpmQuantile)

  // [xutaotao03]
  DEFINE_PROTOBUF_NODE_KCONF(ClueFwhIntentRatioConf, ad.adRank, ClueFwhIntentRatio)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, ClueFwhOcpcActionType)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adRank, InvalidCluesEcpcIndustry)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableDuanjuKimbal, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enableAddOutloopGimabl, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableGimbalFloat, false)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, lpsAcquisitionGeneralizationEcpcParam)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, iaaAcquisitionGeneralizationEcpcParam)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.bidServer2, wenTouCampaignGimbalRatio)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 21 >= IMPL_BLOCK_BEGIN and 21 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.bidServer2, wenTouCampaignGimbalRatioDynamic)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, campaignGimbalDynamicTail, "100;;;")
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, campaignGimbalDynamicOcpx)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.bidServer2, rtaAccountGimbalRatio)

  // [xusimin]
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, InnerLoopT7RoiSmkvhEcpcParam)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, StorewideAuthorTargetEcpcParam)
  DEFINE_PROTOBUF_NODE_KCONF(T7RoiHighQualityEcpcConfig, ad.adRank3, InnerLoopT7RoiHighQualityEcpcParam);
  DEFINE_PROTOBUF_NODE_KCONF(T7RoiHighQualityEcpcExpConfig, ad.adRank3, T7RoiHighQualityEcpcParam2);
  DEFINE_INT64_KCONF_NODE(ad.adRank3, SwTcpAtvNum, 5);
  DEFINE_INT64_KCONF_NODE(ad.adRank3, SwTcpBudgetUpper, 400000);
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, SwTcpDynamicBudgetAuthorTail, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, SwTcpCaliAuthorTail, "100;;;");
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, SwTcpCaliLower, 0.1);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, SwTcpCaliUpper, 20.0);

  // [guochangyu]
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, disableInnerProductEETail, false)
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.adRank2, innerLSPAggrPageGoodsCateID)
  DEFINE_PROTOBUF_NODE_KCONF(InnerLSPAggrPageMultiGoodsCateConf, ad.adRank3, innerLSPAggrPageMultiGoodsCateConf)  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(InnerLSPAggrPageMultiGoodsCateConf, ad.adRank3, innerLSPAggrPageMultiGoodsCateConfFix)  // NOLINT
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank3, lspUserInterestedBehaviorEcpcConf)
  DEFINE_PROTOBUF_NODE_KCONF(IndustryLocalLifeUserLayeredHCConf, ad.adRank3, industryLocalLifeUserLayeredHCConf)  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(LocalLifeUserLayeredBonusConf, ad.adRank3, localLifeUserLayeredBonusConf)  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(LSPNativeUserLayeredExploreThrConf, ad.adRank3, lspNativeUserLayeredExploreThrConf)  // NOLINT
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank3, lspStorewideAuthorPcocCaliConf)
  DEFINE_PROTOBUF_NODE_KCONF(LocalLifeUserExploreMatchConf, ad.adRank3, localLifeUserExploreMatchConf)
  DEFINE_PROTOBUF_NODE_KCONF(LocalLifeUserExploreScoreConf, ad.adRank3, localLifeUserExploreScoreConf)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adRank3, lspStorewideAuthorSearchPcocCaliConf)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, lspStorewideAuthorAvgGmvFlowConf)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, lspStorewideAuthorAvgGmvSearchConf)
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.adRank3, localLifeUserExploreSecondIndustryMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, localLifeConsumPowerEcpcConf)

  // [luyuanquan]
  // 门店私信智能优选账户
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, localLifePromotionSelectedSet);
  // 门店私信智能优选时本地 cvr 扶持系数
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, localLifePromotionCvrRatio, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, localLifePromotionCvrRatioV3, 1.0);

  // [cuihongyi]
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, qcpxAuthorPacingSet)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, southEEAuthorSet);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, southEECate3Set);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableInnerProductEETailExp, false)
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, innerProductEETailExp1, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, innerProductEETailExp2, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, innerProductEETailExp3, "100;;;")
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, innerProductEELowRoiCategory3)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, photoAndP2lRankingListExpireHours, 24)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, photoAndP2lRankingListSize, 50)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, lspBonusAccountSet);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, liveOpmHcRatio, 67000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, photoOpmHcRatio, 37000.0)
  DEFINE_PROTOBUF_NODE_KCONF(InnerAdxEEOrientationConfPB, ad.adRank3, innerAdxEEOrientationConf)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, outerMedicalSecondIncHcFlattenRatio);
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, enableOuterLiveTwinBidAccount, false)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, outerLiveTwinBidOcpxSet)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adAutoParam, outerLiveStreamIdTailSet)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank2, outerLiveTwinBidAccountTailSet)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adAutoParam, outerLiveTwinBidAccountWhite)
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.adAutoParam, buttonClickAccountCpaMap)
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.adRank, nearbyIndustryList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, adDspLiveAdRankNoSimplifiedLiveRoomSubPage)
  DEFINE_PROTOBUF_NODE_KCONF(OuterLiveDspComponentClickConfig,
    ad.adAutoParam, outerLiveDspComponentClickConfig)
  DEFINE_PROTOBUF_NODE_KCONF(OuterLiveDspComponentClickActionTypeConfig,
    ad.adAutoParam, outerLiveDspComponentClickActionTypeConfig)
  DEFINE_PROTOBUF_NODE_KCONF(OuterLivePlaytimeStrategyConfig,
    ad.adAutoParam, outerLivePlaytimeStrategyConfig)

  DEFINE_PROTOBUF_NODE_KCONF(GameHighValuePopulationConfig, ad.adRank, gameHighValuePopulationConfig)
  DEFINE_PROTOBUF_NODE_KCONF(GameHighValuePopulationConfig, ad.adRank, kminigameHighValuePopulationConfig)
  DEFINE_PROTOBUF_NODE_KCONF(IaaAcqGenStruct, ad.adRank3, iaaAcqGenAccountWhiteListAndEcpcParams)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, iaaPlayletPosIdSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, iaaMinigamePageIdSet);

  // [jiangjiaxin]
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, newStoreSpuBoostTagList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, newPlatFormSpuBoostTagList);
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, qcpxPhotoNewSpuTail, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, qcpxPhotoNewSpuHoldTail, "100;;;")

  // [chenzhenhuan]
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adRank3, qcpxExpandVersionControlMinRequiredVersion, "12.11.10");
  DEFINE_SET_NODE_KCONF(std::string, kwaishop.marketingPromotionService, canNotUsePlatformCouponShopIdSet);
  DEFINE_SET_NODE_KCONF(std::string, kwaishop.marketingPromotionService, canNotUsePlatformCouponItemIdSet);

  // [wangqi12]
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, innerStorewideOrientationConf, 0.0)
  // [liujiahui19]
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, livecoldstartGimbalRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerControlFeidanlieHcConfig);

  DEFINE_PROTOBUF_NODE_KCONF(RankCmdKeyManager, ad.adRank, rankCmdKeyManager)      // 精排 cmd key 管理

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireLivePidControlPosId)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, nativeGspProtectAccountTail)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, rankCandidateRatio, 0.0)  // 精排候选数据流 pv 采样率
  // 搜索 QCPX 相关配置
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableSearchQcpxSkipShopCouponStatus, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableSearchQcpxUserCouponDeliveryInterval, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableSearchQcpxCouponRcvFreqControl, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableSearchQcpxU0Filter, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableSearchLiveQcpxSellerIdFreqControl, true)
  DEFINE_INT32_KCONF_NODE(ad.adRank3, qcpxSearchLiveMinCouponAmountYuan, 2)
  DEFINE_INT32_KCONF_NODE(ad.adRank3, qcpxSearchLiveMaxCouponAmountYuan, 30)
  DEFINE_INT32_KCONF_NODE(ad.adRank3, searchValueLiveQcpxFreqControlPayNd, 3)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, valueSearchQcpxLivePriceCouponRatio, 8)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, qcpxSearchFilterAuthorSet)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, qcpxSearchFilterItemSet)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.adRank3, qcpxSearchLiveAllocateFlow)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 22 >= IMPL_BLOCK_BEGIN and 22 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT32_KCONF_NODE(ad.adRank, rankCandidateNum, 2)  // 精排候选数据流 pv 采样个数
  DEFINE_INT32_KCONF_NODE(ad.adRank, prerankCandidateNum, 300)  // 粗排候选数据流 pv 采样个数
  DEFINE_INT32_KCONF_NODE(ad.adcounter, maxPidServerIdSizeOneReq, 650)  // 单次请求 PID 服务的 item 上限
  DEFINE_INT32_KCONF_NODE(ad.adRank, forceRecoTagSampleRatio, 101)  // 强出框架打点采样率

  // 剧量计划实时流
  DEFINE_INT64_KCONF_NODE(ad.adRank, duanJuXinJuAutomaticPotentialCostThr, 2000)
  DEFINE_INT64_KCONF_NODE(ad.adRank, duanJuXinJuAutomaticHotCostThr, 50000)
  DEFINE_INT64_KCONF_NODE(ad.adRank, duanJuXinJuAutomaticHotPayThr, 5000)

  // 开屏领券中心导流广告账户 id 名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, splashCouponCollectionCenterAccountIds)

  // 蓝海新品补贴类目名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, whiteBonusProject)

  DEFINE_PROTOBUF_NODE_KCONF(AdForceRecoTagConfig, ad.adRank, adForceRecoTagConf)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableAdForceRecoTag, true)
  // 搜索广告统计特征时间窗口
  // List string
  DEFINE_INT64_KCONF_NODE(ad.adRank, changeWatcherControl, 100000)  // 出价信息记录频率控制
  // 涨粉用户分层调权参数
  DEFINE_PROTOBUF_NODE_KCONF(MerchantFollowBuyerEffectiveAdjustConfig, ad.adRank,
                             merchantFollowBuyerEffectiveAdjustParams)
  DEFINE_PROTOBUF_NODE_KCONF(MerchantFollowNumAdjustConfig, ad.adRank,
                             merchantFollowNumAdjustConfig)
  // 内循环 qcpx 短带分价格带调权参数
  DEFINE_PROTOBUF_NODE_KCONF(EspQcpxPhotoPriceRangeAdjustConfig, ad.adRank3,
                             espQcpxPhotoPriceRangeAdjustConfig)
  // 内循环货架 qcpx 短带分价格带发固定券
  DEFINE_PROTOBUF_NODE_KCONF(ShelfQcpxPhotoPriceRangeFullConfig, ad.adRank3,
                             shelfQcpxPhotoPriceRangeFullConfig)
  // 内循环 qcpx 短带分价格带调权 boost 参数
  DEFINE_PROTOBUF_NODE_KCONF(EspQcpxPhotoPriceRangeBoostConfig, ad.adRank3,
                             espQcpxPhotoPriceRangeBoostConfig)
  // 粉条电商客户涨粉 ECPC 参数配置
  DEFINE_PROTOBUF_NODE_KCONF(SegmentedParametersConfig, ad.adRank,
                             fanstopEcommerceFollowLTVEcpcListLenConfig)
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopEcommerceFollowLTVEcpcListLenTail, "100;;;")
  DEFINE_PROTOBUF_NODE_KCONF(SegmentedParametersConfig, ad.adRank,
                             fanstopEcommerceFollowLTVEcpcLiveConsumeConfig)
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopEcommerceFollowLTVEcpcLiveConsumeTail, "100;;;")
  DEFINE_PROTOBUF_NODE_KCONF(MerchantFollowPageAdjustConfigV2, ad.adRank,
                             merchantFollowPageAdjustConfigV2)
  DEFINE_PROTOBUF_NODE_KCONF(ForceTagTestWhiteListConf, ad.adRank, forceTagTestWhiteListConf)
  DEFINE_PROTOBUF_NODE_KCONF(FollowInnerCrowdFilter, ad.adRank, followInnerCrowdFilter)
  // 磁力万合敏感度标签过滤
  DEFINE_PROTOBUF_NODE_KCONF(SideWindowCpmFilterConf, ad.adRank, sideWindowCpmFilter);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, predictBoundTotalDotSampleRate, 1e-4)  // [jiangyuzhen] 预测兜底总流量打点采样率 //NOLINT
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, predictBoundedRatioEmaConst, 1e-3)  // [jiangyuzhen] 预测兜底率指数平滑系数 //NOLINT
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, predictBoundedRatioThres, 5e-2)  // [jiangyuzhen] 预测兜底率打点阈值 //NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(SideWindowPosConf, ad.adRank, sideWindowPosConf);
  // 磁力万合 uplift 人群策略优化
  DEFINE_PROTOBUF_NODE_KCONF(SideWindowUpliftConf, ad.adRank, sideWindowUpliftParams)
  // 磁力万合 sctr 策略优化
  DEFINE_PROTOBUF_NODE_KCONF(SideWindowSctrOptConf, ad.adRank, sideWindowSctrOptParams)
  // 磁力万合 boost 策略优化
  DEFINE_PROTOBUF_NODE_KCONF(SideWindowBoostConf, ad.adRank, sideWindowBoostParams)
  // 万合 P 页皮肤 boost 策略优化
  DEFINE_PROTOBUF_NODE_KCONF(WanheProfileBoostConf, ad.adRank, wanheProfileBoostParams)
  // 磁力万合成本优化
  DEFINE_PROTOBUF_NODE_KCONF(SideWindowAchieveOptConf, ad.adRank, sideWindowEcpcParams)
  // 万合 cpm 门槛打平系数配置
  DEFINE_PROTOBUF_NODE_KCONF(WanheCpmThRatioConf, ad.adRank, wanheCpmThRatio)
  // 万合匹配效率系数配置
  DEFINE_PROTOBUF_NODE_KCONF(WanheMatchingEfficiencyConfig, ad.adRank, wanheMatchingEfficiencyConfig)
  // 万合出价目标维度成本率校准配置
  DEFINE_PROTOBUF_NODE_KCONF(WanheActionTypeCostRatioConfig, ad.adRank, wanheActionTypeCostRatioConfig)
  // 发现页内外流出价目标维度成本率校准配置
  DEFINE_PROTOBUF_NODE_KCONF(WanheActionTypeCostRatioConfig, ad.adRank, exploreActionTypeCostRatioConfig)
  // 短剧强出配置支持配置生效时间
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 23 >= IMPL_BLOCK_BEGIN and 23 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, duanjuForcePlayletNameConf)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(firefly.playset, duanjuForcePlayletNameConfV2)
  // target_cost_pro 配置
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProEcpcConfig, ad.adRank, targetCostProEcpcConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProUnitTailV2);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProUnitTailV2Roas);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProUnitTailV2T7);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProUnitTailV2Exp2);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProUnitTailV2Exp2Roas);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProUnitTailV2Exp2T7);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProUnitTailV2Exp3);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProUnitTailV2Exp3Roas);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProUnitTailV2Exp3T7);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPcocUnitTail);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, targetCostProBoostMap);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, targetCostProPcocCali, 1.2);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, targetCostProPcocLower, 0.8);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, outerLoopDncLtvFlatteningCeof, 1.0);
  // 直播成本保护升级版二期, 订单 参数配置
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProOrderExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProOrderExpConfigExp2);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProOrderExpConfigExp3);
  // 直播成本保护升级版二期, ROAS 参数配置
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProRoasExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProRoasExpConfigExp2);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProRoasExpConfigExp3);
  // 直播成本保护升级版二期, T7 参数配置
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProT7ExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProT7ExpConfigExp2);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProT7ExpConfigExp3);
  //直播成本保护升级版二期, 托管配置
  DEFINE_PROTOBUF_NODE_KCONF(MultiTargetCostProExpConfig, ad.adRank3, targetCostProCampaignMultiConfig);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProEcpcConfig, ad.adRank3, targetCostProCampaignEcpcConfig)
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProEcpcConfig, ad.adRank3, targetCostProStorewideEcpcConfig);
  // 直播成本保护升级版二期, 托管和全站, 实验尾号, campaign
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProCampaignTailOrderExp1, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProCampaignTailOrderExp2, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProCampaignTailRoasExp1, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProCampaignTailRoasExp2, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProCampaignTailT7Exp1, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProCampaignTailT7Exp2, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProCampaignTailStoreWideExp1, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProCampaignTailStoreWideExp2, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProStoreWideMoveLowC, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProStoreWideZhongxiao, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, storeWideLowClevelEcpc, "100;;;");
  DEFINE_INT64_KCONF_NODE(ad.adRank3, storeWideLowCIndex, 0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, storeWideLowClevelCostThreshold, 400);
  // 打折效率实验
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, storewidePriceEfficiencyExp, "100;;;");
  // target_cost_pro 短带配置
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProEcpcConfig, ad.adRank, targetCostProPhotoEcpcConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTail);
  // 短视频成本保护升级版二期实验尾号
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTailV2Exp1);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTailV2Exp2);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTailV2Exp3);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTailV2Exp1Order);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTailV2Exp2Order);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTailV2Exp3Order);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTailV2Exp1Roas);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTailV2Exp2Roas);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, targetCostProPhotoUnitTailV2Exp3Roas);

  // 短视频成本保护升级版二期, 订单 参数配置
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProPhotoOrderExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProPhotoOrderExp1Config);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProPhotoOrderExp2Config);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProPhotoOrderExp3Config);
  // 短视频成本保护升级版二期, ROAS 参数配置
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProPhotoRoasExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProPhotoRoasExp1Config);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProPhotoRoasExp2Config);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProExpConfig, ad.adRank, targetCostProPhotoRoasExp3Config);
  // 短视频成本保护升级版二期, 账户类型是否支持移动版
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 24 >= IMPL_BLOCK_BEGIN and 24 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_BOOL_KCONF_NODE(ad.adRank, targetCostProPhotoIsAdmitMobile, true);

  // 短视频成本保护升级版二期, 托管和全站配置
  DEFINE_PROTOBUF_NODE_KCONF(MultiTargetCostProExpConfig, ad.adRank3, targetCostProPhotoCampaignMultiConfig);
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProEcpcConfig, ad.adRank3, targetCostProPhotoCampaignEcpcConfig)
  DEFINE_PROTOBUF_NODE_KCONF(TargetCostProEcpcConfig, ad.adRank3, targetCostProPhotoStorewideEcpcConfig);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, targetCostProPhotoStorewideThresWeight, 1.0);

  // 短视频成本保护升级版二期, 托管和全站, 实验尾号, campaign
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProPhotoCampaignTailOrderExp1, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProPhotoCampaignTailOrderExp2, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProPhotoCampaignTailRoasExp1, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProPhotoCampaignTailRoasExp2, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProPhotoCampaignTailStorewideExp1, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProPhotoCampaignTailStorewideExp2, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, targetCostProPhotoCampaignTailStorewideZhongxiao, "100;;;");

  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, targetCostProPhotoThres, 25.0);
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, photoStorewideStageHcProductTail, "100;;;");
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank, targetCostProPhotoBoostMap);
  // target cost pro 账户白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, targetCostProAccountWhitelist)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, targetCostProIncludeT7Roi, false)

  // 流量校准策略优化
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, trafficCalibrationOptConf);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank3, trafficCalibrationOptCpmConf);

  // 创新流量 ecpc 探索配置
  DEFINE_PROTOBUF_NODE_KCONF(InnovationTrafficEcpcConfig, ad.adRank, innovationTrafficEcpcConfig)
  DEFINE_PROTOBUF_NODE_KCONF(InnovationTrafficEcpcConfigUpdateV3, ad.adRank2,
                             innovationTrafficEcpcConfigUpdateV3)
  // 先知产品维度放量
  DEFINE_PROTOBUF_NODE_KCONF(ZhugongProductNameGroup, ad.adRank2, zhugongProductNameGroupConf)
  // 耗时分析配置
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank, perfNodeCostLatencyMergeName);
  // 磁力万合垂类 boost
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, sideWindowCrossSectionPidTags);
  // 电商直播观看占比调权
  DEFINE_PROTOBUF_NODE_KCONF(MerchantFollowUserLiveAdjustConfig, ad.adRank,
                             merchantFollowUserLiveAdjustConfig)
  // 涨粉分页面成本调整
  DEFINE_PROTOBUF_NODE_KCONF(MerchantFollowPageBoundConfig, ad.adRank,
                             merchantFollowPageBoundConfig)
  // 游戏重定向通路
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, gameForceMultiTagidConf);
  // 关注页成本率优化账户黑名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, followCostRatioAccountBlackList);
  // 内流 trigger item 对应的河图标签
  DEFINE_PROTOBUF_NODE_KCONF(TriggerItemRecallConfig, ad.adtarget2, triggerItemRecallConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innerTriggerItemTagMap)
  DEFINE_PROTOBUF_NODE_KCONF(ExploreRelativeScoreConfig, ad.adRank3, exploreRelativeScoreConfig)
  DEFINE_PROTOBUF_NODE_KCONF(ExploreCpmRelativeConfig, ad.adRank3, exploreCpmRelativeConfig)
  // 流量 cpm 分档配置
  DEFINE_PROTOBUF_NODE_KCONF(TrafficCalibrationOptDynamicCpmConf, ad.adRank3,
        trafficCalibrationOptDynamicCpmConf);
  // 统一 rank benefit 门槛过滤
  DEFINE_PROTOBUF_NODE_KCONF(UnifyFinalRbThresholdFilterConfig, ad.adRank2, unifyFinalRbThresholdFilterConfig)

  DEFINE_PROTOBUF_NODE_KCONF(OuterIndustryBoostConfig, ad.adtarget2, outerIndustryBoostConfig)
  // 跳过人群优化 ECPC 策略 CPM 门槛系数策略 sub_page_id
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, userGroupEcpcThrSkipSubPageId);

  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, rankPsRouterRequestDiffField)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, rankPsRouterRequestCompareExcludeField)

  // UAX 相关配置
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, UaaSecondIndustryWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, UaaAgentIdList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, UaaProductNameList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, UaaAccountIdList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, UalSecondIndustryWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, UalAgentIdList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, UalProductNameList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, UalAccountIdList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, AdDawnCampaignTypeList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, AdDawnOcpxActionTypeList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, firefly.bidServer, uaaBonusWhiteList);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adAutoParam, uaxCpmLevelMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adAutoParam, uaxHcRatioMap)
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, skipUaaBonusWhiteList, false)
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, skipUalBonusWhiteList, false)
  DEFINE_PROTOBUF_NODE_KCONF(UaxModifyHcScoreWhiteList, ad.adRank2, uaxModifyHcScoreWhiteList)
  // 原生素材审核键位
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 25 >= IMPL_BLOCK_BEGIN and 25 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adRank2, nativeReviewStatusSet);
  // 模型干预调节系数配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, cmdkeyInterventionAdjustRatioMap);

  // AIGC 盘古短视频补贴相关配置
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, skipAIGCBonusWhiteList, false);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, AIGCBonusAccountIdList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, AIGCBonusSecondIndustryWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, AIGCBonusAgentIdList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, AIGCBonusPhotoIdList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, AIGCBonusProductNameList);
  // AIGC 女娲直播数字人补贴相关配置
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, skipLiveDigitalBonusWhiteList, false);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, LiveDigitalBonusAccountIdList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, LiveDigitalBonusSecondIndustryWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, LiveDigitalBonusAgentIdList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, LiveDigitalBonusLiveIdList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, LiveDigitalBonusProductNameList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, poQuanExtraDiscountProductNameList) // E-E破圈计费打折额外折扣 // NOLINT
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, EnableGetOuterLoopEEProductUserClusterHisImpCnt, false);
  // 短剧 ecpc 配置
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(firefly.playset, djEcpcIaaDynamicConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(firefly.playset, djEcpcIaaDynamicConfigXifan);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(firefly.playset, djEcpcIaaDynamicConfigXifanSoftPre);
  DEFINE_PROTOBUF_NODE_KCONF(PlayletEcpcWhitelistConfigNew, ad.adRank, potentialPlayletEcpcWhitelistConfig);
  DEFINE_PROTOBUF_NODE_KCONF(PlayletIaaEcpcConfig, ad.adRank3, playletIaaEcpcConfig);
  DEFINE_PROTOBUF_NODE_KCONF(DjBonusConfig, ad.adRank2, djBonusConfigNew);
  DEFINE_PROTOBUF_NODE_KCONF(PlayletIaaOriEcpcConfig, ad.adRank3, playletIaaOriEcpcConfig);
  // 短剧剧名动态调价配置
  DEFINE_PROTOBUF_NODE_KCONF(PlayletDynamicAdjustConfig, ad.adRank2, playletDynamicAdjustConfig);
  DEFINE_PROTOBUF_NODE_KCONF(PlayletRecoRetargetConfig, firefly.playset, playletRecoRetargetConfig);
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver2, duanjuInnerKimbalTail, "100;;;");
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, incrementExploreHitSet);
  // 短剧激活切唤端尾号实验
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, isPlayletInvokedTail, "100;;;")
  // 外循环 ROI 浅带深调权配置
  DEFINE_PROTOBUF_NODE_KCONF(AdRoasStdCoef, ad.adRank2, adRoasStdCoef);
  // 外循环 IAA 浅带深调权配置
  DEFINE_PROTOBUF_NODE_KCONF(AdIaaStdCoef, ad.adRank2, adIaaStdCoef);
  // 外循环游戏探索顶价配置
  DEFINE_PROTOBUF_NODE_KCONF(OuterGameExploreEcpcConfig, ad.adRank2, outerGameExploreEcpcConfig)
  // 首发游戏名白名单配置
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank2, ShouFaProductNameList);

  DEFINE_PROTOBUF_NODE_KCONF(PlayletYali1CdxEcpc, firefly.playset, playletyalicdxecpc);
  DEFINE_PROTOBUF_NODE_KCONF(PlayletRealtimeChongdingxiang, firefly.playset, playletRealtimeChongdingxiang1);
  // [jiangjinling] na 小说 ecpc 系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adAutoParam, paidFictionRetargetEcpcRatio);
  // [jiangjinling] na 小说重定向召回通路
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adRank2, outerNativeFictionRetargetTagSet);

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, fictionHotBookEcpcStrategyWhiteList);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, fictionHotBookEcpcStrategyManualControl);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, fictionHotBookDiscountManualControl);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, fictionIaaPcocAdjustParams);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, fictionBookPayManualControl);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, fictionPanelPriceProductNameWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, fictionPanelPriceProductNameBlackList)
  DEFINE_HASH_SET_NODE_KCONF(double, ad.adRank3, fictionUpliftPayRateSubsidyRatioSet);
  DEFINE_HASH_SET_NODE_KCONF(double, ad.adRank3, fictionUpliftRealPayPriceSet);

  DEFINE_PROTOBUF_NODE_KCONF(ColdStartPageConf, ad.adRank, coldstartpageConf)
  DEFINE_PROTOBUF_NODE_KCONF(DisableFactorConfig, ad.adRank2, disableFactorConfig)
  DEFINE_PROTOBUF_NODE_KCONF(CommonInterventionEcpcConfig, ad.adRank3, InnerCommonInterventionEcpcConfig)
  DEFINE_PROTOBUF_NODE_KCONF(CommonInterventionEcpcConfig, ad.adRank3, OuterCommonInterventionEcpcConfig)
  DEFINE_PROTOBUF_NODE_KCONF(GoodItemAuthorStrategySet, ad.adRank, goodItemStrategyConf);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, fictionIaaPosEcpcBlackAccountSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, fictionIaaPosEcpcBlackProductNameSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, nativefictionIaaPosIds);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, fictionIaaPosEcpcProductRatioMap);
  DEFINE_PROTOBUF_NODE_KCONF(GameIaaTimeZoneEcpcDefaultConf, ad.adRank3, fictionIaaTimeZoneEcpcDefaultConf);
  // 游戏 iaa 变现
  DEFINE_PROTOBUF_NODE_KCONF(IndustryIaaEcpcConf, ad.adRank3, kminiGameIaaFirstIndustryEcpcConf);
  DEFINE_PROTOBUF_NODE_KCONF(IndustryIaaEcpcConf, ad.adRank3, kminiGameIaaInspireProductEcpcConf);
  DEFINE_PROTOBUF_NODE_KCONF(IndustryIaaEcpcConf, ad.adRank3, kminiGameIaaInspireProductEcpcConfAB);
  DEFINE_PROTOBUF_NODE_KCONF(IndustryIaaEcpcConf, ad.adRank3, kminiGameIaaGameProductEcpcConf);
  DEFINE_PROTOBUF_NODE_KCONF(IndustryIaaEcpcConf, ad.adRank3, kminiGameIaaInspireAccountEcpcConf);
  DEFINE_PROTOBUF_NODE_KCONF(IndustryIaaEcpcConf, ad.adRank3, kminiGameIaaInspireCampaignEcpcConf);
  DEFINE_PROTOBUF_NODE_KCONF(GameIaaTimeZoneEcpcDefaultConf, ad.adRank3, gameIaaTimeZoneEcpcDefaultConf);
  DEFINE_PROTOBUF_NODE_KCONF(IndustryIaaEcpcConf, ad.adRank3, fictionIaaPutBookEcpcConf);
  DEFINE_PROTOBUF_NODE_KCONF(FictionIaaSourceTargetConf, ad.adRank3, fictionIaaPosEcpcSourceAndRewardConf);

  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, gameIaaRoi7AccountTail, "101;;;");  // 粉条直播进人 Xtr 过滤尾号实验  //NOLINT
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, ReqIndexByInstanceIdTail, "100;;;");


  DEFINE_PROTOBUF_NODE_KCONF(LpsAcquisitionGeneralizeAutoparmPb, ad.adRank3, lpsAcquisitionGeneralizationEcpcAutoParam);  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(PmFwhEeParaStruct, ad.adRank3, pmFwhEePara);  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(PmPoiSearchParaStruct, ad.adRank3, pmPoiSeachEcpcPara);  // NOLINT
  // 行业人群 ecpc 系数
  DEFINE_PROTOBUF_NODE_KCONF(IndustryUserTagEcpcConfig, ad.adRank3, industryUserTagEcpcConfig)
  DEFINE_PROTOBUF_NODE_KCONF(IndustrySoftAdEcpcConfig, ad.adRank3, industrySoftAdEcpcConfig);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, ColdStartBonusControl);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, liveColdStartSellerControl);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, coldStartTopkControl);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, authoridLiveRate);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, authoridPhotoRate);
  DEFINE_PROTOBUF_NODE_KCONF(innerMixScoreConf, ad.adRank3, InnerMixScoreConf);
  DEFINE_STRING_BOOL_HASH_MAP_KCONF(ad.adRank3, upItemsControl);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, InnerLiveDigitalBonusAuthorIdList);  // 直播白名单 kconf
  // qcpx 测试用户
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, qcpxTestUserList);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableQcpxTestUserList, false);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, qcpxTestUserBoostRatio, 1.0);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableQcpxFixAtomFea, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableQcpxCtrBound, false);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank3, qcpxTmpIdList);

  DEFINE_PROTOBUF_NODE_KCONF(AdBoundConfig, ad.adRank3, adBoundConf);

  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableKthp, false)   // rank 开启 Kthp

  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, cidBonusCorporationNameSet);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, cidBonusCorporationNameDefaultRatio, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, cidBonusGreatSaleDefaultRatio, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, cidBonusNewProductDefaultRatio, 1.0);
  // rank cache force ttl
  DEFINE_BOOL_KCONF_NODE(ad.adRank2, rankAdlistCacheForceTTLSwitch, false);
  DEFINE_INT64_KCONF_NODE(ad.adRank2, rankAdlistCacheForceTTLValue, 600);

  // rank score cache
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, rankScoreCacheCheckSceneList);
  DEFINE_INT64_KCONF_NODE(ad.adRank3, rankScoreCacheQuota, 50);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adRank3, scoreCacheHitGroupMap);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableAdRouterPredictPrometheus, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableArenaOptimization, false);
  DEFINE_BOOL_KCONF_NODE(ad.router, adRouterGetKessNameIgnoreEnv, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, closeGrpcForceUseEpollsig, false)   // close grpc force use epollsig

  // adlist cache by corpus
  DEFINE_PROTOBUF_NODE_KCONF(Cpm2TtlBucket, ad.adRank3, cpm2TtlBucketV2);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, cacheHitRatioMap);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank3, customCacheQuotaRatioMap);

  // 内循环打折控制
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableInnerloopDiscountControl, false);
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.bidServer2, innerloopDiscountTagBudget);
  DEFINE_PROTOBUF_NODE_KCONF(TagDiscountCost, ad.bidServer2, tagDiscountCost);
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.adRank3, NewTopkTag);
  DEFINE_SET_NODE_KCONF(int32_t, ad.adRank3, DiscountColdTag);
  DEFINE_SET_NODE_KCONF(int32, ad.adRank3, externalJumpOcpxSet);

  // UAX bonus tag
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, UAXCIDBonusAccountIdList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, UAXCIDBonusLicenseList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, UAXCIDBonusProductList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank3, UAXLiveBonusProductList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, UAXLiveBonusAccountIdList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, UAXLiveBonusLicenseList);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, InnerCidSctrPosDecayRatio, 0.02);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableCidEcpcIncrementSign, false);
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, enableCidEcpcIncrementSignFix, false);

  // 短视频精排生效混排乘系数
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank2, adInnerPhotoModelMixGpmRatio, 1.0);

  // 混排 item set 特征 最大 item 个数
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, adMixModelItemSetTopKNum, 30);

  // 表单场景无点击转化
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, lpsAddNoctcvrAccountWhiteList);
  // cid 校准 尾号开关
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank3, innerCIDModelCalibrateTailNumber, "100;;;");
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
};

}  // namespace ad_rank
}  // namespace ks

#ifndef KCONF_CC_WITH_IMPL
  #undef  DEFINE_KCONF_NODE
  #define DEFINE_KCONF_NODE(type, config_path, config_key, default_value)                    \
    DEFINE_KCONF_NODE_BODY(type, config_path, config_key, default_value)

  #undef DEFINE_KCONF_NODE_LOAD
  #define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key)                              \
    DEFINE_KCONF_NODE_LOAD_BODY(type, config_path, config_key)

  #undef DEFINE_SET_NODE_KCONF
  #define DEFINE_SET_NODE_KCONF(type, config_path, config_key)                                  \
    DEFINE_SET_NODE_KCONF_BODY(type, config_path, config_key)

  #undef DEFINE_KCONF_MAP_KCONF
  #define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
    DEFINE_KCONF_MAP_KCONF_BODY(key_type, value_type, config_path, config_key)
#else
  #undef DEFINE_KCONF_NODE_ATTR
  #define DEFINE_KCONF_NODE_ATTR
#endif
