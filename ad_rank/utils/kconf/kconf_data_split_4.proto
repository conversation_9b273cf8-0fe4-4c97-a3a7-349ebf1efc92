syntax  = "proto3";
option cc_enable_arenas = true;
package ks.ad_rank.kconf;

import "teams/ad/ad_proto/kuaishou/fanstop/common/fans_top_enums.proto";

message SideWindowAchieveOptConf {
  map<int64, double> account_id_list = 1;
  map<string, double> product_name_list = 2;
  map<int64, double> page_id_list = 3;
  double adjust_ratio_lower_bound = 4;
  double adjust_ratio_upper_bound = 5;
}

message ZhugongProductNameGroup {
  message ProductNameList {
    repeated string product_name_list = 1;
  }
  map<int64, ProductNameList> product_name_group_map = 1;
};

message NoTagAdReviewConfig {
  map<int64, int32> general_review_map = 1;
  map<int64, int32> hot_review_map = 2;
  map<int64, int32> topk_review_map = 3;
}

message TriggerItemRecallConfig {
  map<string, bool> selected_biz_name = 1;
  map<int32, bool> selected_path_id = 2;
  map<int32, bool> allow_sub_page_id = 3;
}

message UalDefConf {
  message ConfItem {
    string campaign_type = 1;
    repeated string ocpx_action_type = 2;
  }
  repeated ConfItem conf_items = 1;
}

message RetentionDaysDropList {
  message RetentionDaysDropIter {
    repeated string product_name = 1;
    repeated int32 account_id = 2;
    double threshold = 3;
  }
  map<string, RetentionDaysDropIter> threshold_tool = 1;
}

message SegmentedParametersConfig{
  message ExpConfig {
    repeated double segment_range = 1;  // 关注数分段
    repeated double segment_score = 2;  // 调整权重
  }
  map<string, ExpConfig> exp_conf = 1;
}

message PlayletIaaOriEcpcConfig {
  message ExpConfig {
    uint64 ori_id = 1;
    double ecpc_ratio = 2;
  }
  map<string, ExpConfig> exp_map = 1;
}

message PlayletEcpcWhitelistConfigNew {
  message ExpConfig {
    string ori_id = 1;  // 人群包id
    double ratio  = 2;  // 权重系数
  }
  message ReVector {
    repeated ExpConfig single = 1;
  }
  message WhiteListMap {
    map<string, ReVector> whitelist_map = 1;
  }
  map<string, WhiteListMap> exp_list = 1;
}

message DjBonusConfig {
  message ExpConfig {
    repeated int64 account_id_set = 1;
    repeated string playlet_name_set = 2;
    repeated string action_type_set = 3;
    repeated string campaign_type_set = 4;
    repeated int32 is_sales_matching_set = 5;
    repeated int32 is_cold_start_set = 6;
    double ratio = 7;
    repeated string product_name_set = 8;
  }
  message ReVector {
    repeated ExpConfig single = 1;
  }
  message WhiteListMap {
    map<int64, ReVector> whitelist_map = 1;
  }
  map<string, WhiteListMap> exp_list = 1;
}

message PlayletIaaEcpcConfig {
  message ExpConfig {
    map<string, double> ecpc_map = 1;
  }
  map<string, ExpConfig> exp_map = 1;
}

message PlayletDynamicAdjustConfig {
  message ExpConfig {
    double upper_bound = 1;  // 调价上限
    double lower_bound = 2;  // 调价下限
    double decay_weight = 3;  // EMA衰减比例
    double req_threshold = 4;  // EMA生效阈值
    double scale_ratio = 5; // 缩放系数
    repeated double disable_left_bound = 6;  // 禁用左边界
    repeated double disable_right_bound = 7;  // 禁用右边界
    double avg_scale_ratio = 8; // 均值缩放系数
  }
  map<string, ExpConfig> exp_list = 1;
}
message GameIaaRoi7DynamicAdjustConfig {
  message ExpConfig {
    double upper_bound = 1;  // 调价上限
    double lower_bound = 2;  // 调价下限
    double decay_weight = 3;  // EMA衰减比例
    double req_threshold = 4;  // EMA生效阈值
    double scale_ratio = 5; // 缩放系数
    repeated double disable_left_bound = 6;  // 禁用左边界
    repeated double disable_right_bound = 7;  // 禁用右边界
    double avg_scale_ratio = 8; // 均值缩放系数
  }
  map<string, ExpConfig> exp_list = 1;
}

message GameIapLtv7DeprioritizeConfig {
  message ExpConfig {
    double upper_bound = 1;  // 调价上限
    double lower_bound = 2;  // 调价下限
    double decay_weight = 3;  // EMA衰减比例
    double req_threshold = 4;  // EMA生效阈值
    double scale_ratio = 5; // 缩放系数
  }
  map<string, ExpConfig> exp_list = 1;
}

message GameCtcvrFilterConf {
  message ExpConfig {
    double game_ctr_threshold = 1;
    double game_cvr_threshold = 2;
    double game_ctcvr_threshold = 3;
  }
  map<string, ExpConfig> exp_list = 1;
}

message GameCtcvrFilterConfbyOcpc {
  message ExpConfig {
    repeated int64 campaign_type = 1;
    repeated int64 ocpx_action_type = 2;
    double game_ctr_threshold = 3;
    double game_cvr_threshold = 4;
    double game_ctcvr_threshold = 5;
  }
  map<string, ExpConfig> exp_list = 1;
}

message PlayletYali1CdxEcpc {
    message ExpConfig {
        int32 total_list_thres_pre = 1;
        int32 total_list_thres_later = 2;
        int32 ad_list_thres_pre = 3;
        int32 ad_list_thres_later = 4;
        double yali1_total_ratio1 = 5;
        double yali1_total_ratio2 = 6;
        double yali1_ad_ratio1 = 7;
        double yali1_ad_ratio2= 8;
      }
      message ReVector {
        repeated ExpConfig single = 1;
      }
      message WhiteListMap {
        map<string, ReVector> whitelist_map = 1;
      }
      map<string, WhiteListMap> exp_list = 1;
}

message PlayletRealtimeChongdingxiang {
  message ExpConfig {
    double second = 1;
    double third = 2;
    double fourth = 3;
    double fifth = 4;
    double zhesuan_ratio2 = 5;
    double zhesuan_ratio3 = 6;
    double zhesuan_ratio4 = 7;
    double zhesuan_ratio5 = 8;
  }
  message ReVector {
    repeated ExpConfig single = 1;
  }
  message WhiteListMap {
    map<string, ReVector> whitelist_map = 1;
  }
  map<string, WhiteListMap> exp_list = 1;
}

message AdRoasStdCoef {
  message ProcessConfig {
    double cvr_w = 1;
    double cvr_bias = 2;
    double cvr_exp = 3;
    double ltv_w = 4;
    double ltv_bias = 5;
    double ltv_exp = 6;
  }
  message ConfigMap {
    map<string, ProcessConfig> progress_config = 1;
  }
  map<string, ConfigMap> exp_list = 1;
}

message AdIaaStdCoef {
  message ProcessConfig {
    double cvr_w = 1;
    double cvr_bias = 2;
    double cvr_exp = 3;
    double ltv_w = 4;
    double ltv_bias = 5;
    double ltv_exp = 6;
  }
  message ConfigMap {
    map<string, ProcessConfig> progress_config = 1;
  }
  map<string, ConfigMap> exp_list = 1;
}

message PlayletRecoRetargetConfig {
  message ProgressConfig {
    double duration_thr = 1;
    double cnt_thr = 2;
    double ecpc_ratio = 3;
  }
  message ConfigMap {
    map<string, ProgressConfig> progress_config = 1;
  }
  map<string, ConfigMap> exp_list = 1;
}

message UserVauleGroupEcpcConf {
  map<string, double> ecpc_conf = 1;
  map<string, double> cpm_thr_conf = 2;
}

message HcHoldoutConfig {
  map<string, double> traffic_hc_config = 1;
  map<string, double> industry_hc_config = 2;
  map<string, double> other_hc_config = 3;
}

message UaxModifyHcScoreWhiteList {
  message Value {
    map<int64, double> hc_modify_tag_ids = 1;
  }
  map<int64, Value> first_industry_ids = 1;
}

message OuterloopNcMaxOcpxBlackSet {
  message ExpConfig {
    string exp_bucket = 1;
    repeated int64 ocpx_action_type = 2;
  }
  repeated ExpConfig exp_config = 1;
}

message IndustryCampaignTypeDarkControlConfig {
  message CampaignTypeConfig {
    map<string, double> campaign_type = 1;
  }
  map<int64, CampaignTypeConfig> industry_id = 1;
  map<string, double> corporation_name = 2;
}


message IndustryLocalLifeUserLayeredHCConf {
  message TagRatioConf {
    repeated string user_tags = 1;
    float tags_hc_ratio = 2;
  }

  message ExperimentConf {
    bool enable_default = 1;
    float default_hc_ratio = 2;
    string hc_calc_strategy = 3;
    repeated TagRatioConf tag_ratio_configs = 4;
  }

  message ProductConf {
    map<string, ExperimentConf> exp_list = 1;
  }

  bool enable = 1;
  map<string, ProductConf> product_configs = 2;
}

message LocalLifeUserExploreMatchConf {
  message UserAdMatchConf {
    repeated string user_tags = 1;
    repeated string ad_tags = 2;
    optional float layer_ecpc_upper_bound = 3;
    optional float layer_ecpc_lower_bound = 4;
    optional float intervene_ecpc_ratio = 5;
  }

  message ExperimentConf {
    float exp_ecpc_upper_bound = 1;
    float exp_ecpc_lower_bound = 2;
    repeated UserAdMatchConf user_ad_match_configs = 3;
    bool enable_exp_explore_discount_ratio = 4;
    float exp_explore_discount_ratio = 5;
  }

  message ProductConf {
    map<string, ExperimentConf> exp_list = 1;
  }

  bool enable = 1;
  map<string, ProductConf> product_configs = 2;
}

message LocalLifeUserExploreScoreConf {
  message ExperimentConf {
    map<string, float> scores = 1;
  }
  map<string, ExperimentConf> exp_list = 1;
}

message LocalLifeUserLayeredBonusConf {
  message TagRatioConf {
    repeated string user_tags = 1;
    double tags_bonus_ratio = 2;
  }

  message ExperimentConf {
    bool enable_default = 1;
    double default_bonus_ratio = 2;
    string bonus_calc_strategy = 3;
    repeated TagRatioConf tag_ratio_configs = 4;
  }

  map<string, ExperimentConf> exp_list = 1;
}

message LSPNativeUserLayeredExploreThrConf {
  message TagThrConf {
    repeated string user_tags = 1;
    double tags_ecpm_thr = 2;
  }

  message ExperimentConf {
    bool enable_default = 1;
    double default_ecpm_thr = 2;
    repeated TagThrConf tag_thr_configs = 3;
  }
  map<string, ExperimentConf> exp_list = 1;
}

message OuterloopNcMaxWlevelConfig {
  message ExpConfig {
    string exp_bucket = 1;
    map<string, int64> topn = 2;
    map<string, double> hc_ratio = 3;
  }
  repeated ExpConfig exp_config = 1;
}

message OuterLoopAcIndustryConfig {
  message ExpConfig {
    int64 ocpx_action_type = 1;
    repeated int64 first_industry_ids = 2;
  }
  repeated ExpConfig exp_config = 1;
}

message UnifyFinalRbThresholdFilterConfig {
  message RbThrMap {
    map<int64, double> rb_thr_map = 1;
    double rb_thr_otherwise = 2;
  }
  map<string, RbThrMap> tag_map = 1;
}

message ColdStartPageConf {
  message ColdStartPageCoef {
    map<int64, double> photo_coef = 1;
    map<int64, double> p2l_coef = 2;
    map<int64, double> live_coef = 3;
  }
  map<string, ColdStartPageCoef> confs = 1;
}

message SmbColdStartPageConf {
  message SmbColdStartPageCoef {
    map<int64, double> photo_coef = 1;
    map<int64, double> p2l_coef = 2;
    map<int64, double> live_coef = 3;
  }
  map<string, SmbColdStartPageCoef> confs = 1;
}

message SmbColdStartBuyerConf {
  message SmbColdStartBuyerCoef {
    map<string, double> photo_coef = 1;
    map<string, double> p2l_coef = 2;
    map<string, double> live_coef = 3;
  }
  map<string, SmbColdStartBuyerCoef> confs = 1;
}

message HCSmbColdStartPageConf {
  message HCSmbColdStartPageCoef {
    map<int64, double> photo_coef = 1;
    map<int64, double> p2l_coef = 2;
    map<int64, double> live_coef = 3;
  }
  map<string, HCSmbColdStartPageCoef> confs = 1;
}

message HCSmbColdStartBuyerConf {
  message HCSmbColdStartBuyerCoef {
    map<string, double> photo_coef = 1;
    map<string, double> p2l_coef = 2;
    map<string, double> live_coef = 3;
  }
  map<string, HCSmbColdStartBuyerCoef> confs = 1;
}

message InnerIndustryUEConf {
  repeated string ue_photo_keys = 1;
  repeated string ue_live_keys = 2;
  map<string, UEMetricConfig> photo_conf = 3;
  map<string, UEMetricConfig> live_conf  = 4;
}

message UEMetricConfig {
  double weight = 1;
  double bias   = 2;
  double exp    = 3;
}

message InnerIndustryGPMCaliConbineConf {
  message CaliCombineConfig {
    map<string, double> u_level = 1;
    map<string, double> author_industry = 2;
    map<string, double> ad_queue = 3;
    map<string, double> ad_request_times = 4;
    map<string, double> item_type = 5;
    map<string, double> req_hour = 6;
    map<string, double> gpm_thres = 7;
    map<string, double> ad_gmv_level = 8;
    map<string, double> gpm_bucket = 9;
    map<string, double> ctcvr_level = 10;
    map<string, double> page_id = 11;
    map<string, double> buyer_type_lower_bound = 12;
    map<string, double> cpm_level = 13;
    map<string, double> low_cpm_level = 14;
    map<string, double> scene_oriented_type = 15;
    map<string, double> auto_bid_ratio = 16;
    map<string, double> low_load_tag = 17;
  }
  map<string, CaliCombineConfig> confs = 1;
}

message SplashRTBSensitiveUserCpmThrExpConfPb {
  message SensitiveUserScope {
    string age_segment = 1;
    repeated string phone_price = 2;
    string user_active_degree = 3;
    bool is_unlogin_user = 4;
    bool is_down_active_user = 5;
  }
  message SensitiveUserCpmThrConf {
    SensitiveUserScope sensitive_user_scope = 1;
    string app_id = 2;
    double cpm_thr = 3;
  }
  map<string, SensitiveUserCpmThrConf> exp_config = 1;
}

message DisableFactorConfigPb {
  repeated string udf = 1;
  repeated string ecpc = 2;
  repeated string filter = 3;
}

message OuterSelfServiceEcpcConfigPb {
  message ExpConfig {
    int64 tar_type = 1;
    repeated string tar_key = 2;
    map<int64, double> calib_info = 3;
  }
  repeated ExpConfig exp_configs = 1;
}

message GoodItemAuthorStrategySet {
  message GoodItemAuthorCoef {
    map<int64, double> author_coef = 1;
  }
  map<string, GoodItemAuthorCoef> confs = 1;
}

message NewCusMultiStageConfig {
  repeated int64 s1_ids = 1;
  repeated int64 s2_ids = 2;
  repeated double industry_cvr = 3;
  repeated double industry = 4;
}

message OuterEcomConvBucketWeightsConfig {
  repeated double buckets = 1;
  repeated double weights = 2;
}

message LpsAcquisitionGeneralizeAutoparmPb {
  message Autoparm {
    int64 expid = 1;
    double alpha_lower = 2;
    double beta_lower = 3;
    double alpha_upper = 4;
    double beta_upper = 5;
    double upper_bound = 6;
    double lower_bound = 7;
  }
  map<int64, Autoparm> bucket = 1;
  string seed = 2;
  int64 bucketnum = 4;
}

message CommonInterventionEcpcConfigPb {
  bool enable = 1;
  repeated int64 page_id_white_list = 2;
  string type_select = 3;
  map<string, double> item_type = 4;
  map<string, double> account_type = 5;
  map<string, double> campaign_type = 6;
  map<string, double> ocpx_action_type = 7;
}

message IndustryUserTagEcpcConfig {
  message ExpConfig {
    map<int64, ControlDetail> orientation_config = 1;
  }
  message ControlDetail {
    repeated string product_set = 1;
    repeated int64 account_id_set = 2;
    double ecpc_ratio = 3;
    bool enable_dynamic_ecpc_ratio = 4;
    repeated string playlet_name_set = 5;
    repeated int64 book_id_set = 6;
  }
  map<string, ExpConfig> exp_config = 1;
}

message DspLiveIndustryEcpcConfig {
  message ExpConfig {
    map<string, ConfigDetail> industry_config = 1;
  }
  message ConfigDetail {
    double ecpc_ratio_lower = 1;
    double ecpc_ratio_upper = 2;
    double adjust_time = 3;
    double time_weight = 4;
    double ctr_weight = 5;
    double vtr_weight = 6;
    double decay_weight = 7;
    int64 req_threshold = 8;
  }
  map<string, ExpConfig> exp_config = 1;
}

message IaaRlParameterControl {
  message ExpConfig {
    repeated ConfigDetail pageid_config = 1;
    repeated ConfigDetail posid_config = 2;
    repeated ConfigDetail ocpc_config = 3;
    repeated ConfigDetail fircate_config = 4;
  }
  message ConfigDetail {
    map<string, int32> values = 1;
    double ecpc_upper = 2;
    double ecpc_lower = 3;
    bool enable_ecpc_pow = 4;
    double weight_ecpc_pow = 5;
    bool enable_ecpc_linear = 6;
    double weight_ecpc_linear = 7;
    bool enable_ecpc_add = 8;
    double weight_ecpc_add = 9;
  }
  map<string, ExpConfig> exp_config = 1;
}

message OuterRankCmtEcpcConfig {
  message ExpConfig {
    map<string, ConfigDetail> configs = 1;
  }
  message ConfigDetail {
    double ecpc_ratio_lower = 1;
    double ecpc_ratio_upper = 2;
    double cestr_weight = 3;
    double clk_cmt_weight = 4;
    double cmt_st_weight = 5;
    double decay_weight = 6;
    int64 req_threshold = 7;
  }
  map<string, ExpConfig> exp_config = 1;
}

message OuterLivePriceDiscountConfig {
  double default_ratio = 1;
  map<string, double> account_itemtype_ratio = 2;
  map<string, double> account_ratio = 3;
  map<string, double> corporation_product_ratio = 4;
  map<string, double> account_itemtype_resourcetype_ratio = 5;
  map<string, double> account_resourcetype_ratio = 6;
  map<string, double> corporation_product_resourcetype_ratio = 7;
}

message WhiteListMapForEcpc {
  map<int64, double> account_id_set = 1;
}
message WhiteListMapForRtaBid {
  map<int64, double> account_id_set = 1;
}

message UpgradedIndustryModelEcpcConf {
  message AdjustConf {
    double min_score = 1;
    double max_score = 2;
    double ecpc_weight = 3;
  }
  message DetailConf {
    string predict_id = 1;
    double ecpc_alpha = 2;
    double industry_model_ecpc_upper = 3;
    double industry_model_ecpc_lower = 4;
    repeated AdjustConf manual_adjust_conf = 5;
  }
  map<string, DetailConf> detail_conf = 1;
}

message Cpm2TtlBucket {
  message ExpConfig {
    map<int64, int64> ttl_bucket = 1;
  }
  map<string, ExpConfig> exp_config = 1;
}

message TagDiscountCost {
  message DiscountCost {
    int32 price_tag = 1;
    int64 update_timestamp = 2;
    int64 tag_discount_cost = 3;
  }
  map<string, DiscountCost> values = 1;
}

message IndustrySoftAdEcpcConfig {
  map<string, double> product_ratio = 1;
  map<int64, double> account_ratio = 2;
}

message RankAdlistCacheQuotaConfig {
  message ExpConfig {
    map<string, int64> queue_quota = 1;
  }
  map<string, ExpConfig> exp_config = 1;
}

message StorewideLiveUpliftConf {
  message ExpConfig {
    map<int64, double> page_coef = 1;
  }
  map<string, ExpConfig> exp_config = 1;
}

message Cpl2CorpFilterIndustryConfig {
  map<string, int64> industry_num_config = 1;
}

message AdBoundConfig {
  message int64_tBoundConf {
    bool enable = 1;
    int64 lower = 2;
    int64 upper = 3;
  }
  message doubleBoundConf {
    bool enable = 1;
    double lower = 2;
    double upper = 3;
  }
  message BoundConf {
    map<string, int64_tBoundConf> int64_t_bound =1;
    map<string, doubleBoundConf> double_bound =2;
  }
  map<string, BoundConf> bounds = 1;
}

message StorewideLiveUpliftEspConf {
  message ExpConfig {
    map<int32, double> esp_coef = 1;
  }
  map<string, ExpConfig> exp_config = 1;
}

message StorewideLiveUpliftAuthorExpTagConf {
  int32 div_num = 1;
  map<string, string> exp_config = 2;
}

message InnerLiveAuthorExpTagConf {
  int32 campaign_div_num = 1;
  int32 unit_div_num = 2;
  map<string, string> exp_config = 3;
}

message InnerLiveCoefConf {
  message ExpConfig {
    map<int64, double> esp_coef = 1;
    map<int64, double> page_coef = 2;
    map<int64, double> gmv_level_coef = 3;
  }
  map<string, ExpConfig> exp_config = 1;
}

message InnerLiveAdmitConf {
  message ExpConfig {
    repeated int64 page_ids = 1;
    repeated string item_types = 2;
  }
  map<string, ExpConfig> exp_config = 1;
}

message StorewideMerchantUpliftEspConf {
  message ExpConfig {
    map<int32, double> esp_coef = 1;
  }
  map<string, ExpConfig> exp_config = 1;
}

message FormPmIntegrationWhiteListConf {
  map<int64, bool> account_set = 1;
  map<int64, bool> unit_set = 2;
  map<string, bool>product_names = 3;
}

message StorewideLiveULevelConf {
  message ExpConfig {
    map<string, double> u_level_coef = 1;
  }
  map<string, ExpConfig> exp_config = 1;
}

message StorewideLivePageConf {
  message ExpConfig {
    repeated int64 page_ids = 1;
  }
  map<string, ExpConfig> exp_config = 1;
}

message StorewideLiveUpliftBoundConf {
  message ExpConfig {
    map<string, double> bound = 1;
  }
  map<string, ExpConfig> exp_config = 1;
}

message TrafficCalibrationOptDynamicCpmConf {
  message cpmGroupVal {
    repeated double value = 1;
  }
  map<string, cpmGroupVal> exp_config = 1;
}

message ExploreRelativeScoreConfig {
  message BucketConfig {
    int32 buckets = 1;
    double max_score = 2;
    double min_score = 3;
    double avg_score = 4;
    map<int64, double> bucket_coef = 5;
  }
  map<string, BucketConfig> bucket_config = 1;
}

message ExploreCpmRelativeConfig {
  message ExpConfig {
    repeated int64 cpm_list = 1;
    repeated double relative_list = 2;
    map<string, double> cpm_coef_map = 3;
  }
  map<string, ExpConfig> exp_config = 1;
}

message EcpmUnifyBoundConfigPb {
  message Value {
    int64 value = 1;  // 单位 厘
    string ab_key = 2;
  }
  Value upper_bound = 1;
  Value lower_bound = 2;
}

message RankAdlistCacheEntranceConfig {
  message EntranceConfig {
    int64 start_time = 1;
    int64 end_time = 2;
    map<int64, double> exclude_sub_page_id = 3;
  }
  map<string, EntranceConfig> entrance_config = 1;
}

message RankCacheEntranceConfig {
  message EntranceConfig {
    repeated string time_range = 1;
    map<int64, double> exclude_sub_page_id = 2;
  }
  map<string, EntranceConfig> entrance_config = 1;
}

message ModelOriginScoreCalibrationConfig {
  message CmdKeyCalibrationConfig {
    bool enable = 1;
    string onwer = 2;
    string context = 3;
    int64 index_num = 4;
  }
  map<string, CmdKeyCalibrationConfig> calibration_config = 1;
}

message innerMixScoreConf {
  map<string, ScoreMetricConfig> photo_conf = 1;
  map<string, ScoreMetricConfig> live_conf  = 2;
}
message ScoreMetricConfig {
  double weight = 1;
  double bias   = 2;
  double exp    = 3;
}

message LocallifeHcToolsConf {
  message HcToolsConf {
    string source_type = 1;
    string campaign_type = 2;
    string ocpx_action_type = 3;
    int64 account_id = 4;
    double ratio = 5;
  }

  double upper_bound = 1;
  double lower_bound = 2;
  repeated HcToolsConf conf_map = 3;
}

message IncntvAdBinToLinerCoef {
  double lower = 1;
  double upper = 2;
  double weight = 3;
  double bias = 4;
}
message IncntvAdPredictNext1ViewValue2CoefProto {
  message IncntvAdBinToLinerCoefList {
    repeated IncntvAdBinToLinerCoef bin_wb_list = 1;
  }
  map<string, IncntvAdBinToLinerCoefList> incntv_ad_predict_next_1view_value_2_coef = 1;
}

message PmFwhEeParaPb {
  message Autoparm {
    repeated double low = 1;
    repeated double mid = 2;
    repeated double high = 3; 
  }
  repeated int64 white_industry = 1;
  double upper_bound = 2;
  double lower_bound = 3;
  map<int64, Autoparm> group = 4;
}

message PmPoiSearchParaPb {
  message Autoparm {
    double low = 1;
    double mid = 2;
    double high = 3; 
  }
  repeated int64 white_industry = 1;
  int64 account_threshold = 2; 
  double upper_bound = 3;
  double lower_bound = 4;
  map<int64, Autoparm> group = 5;
}

message PlayletPriceRatioOpt2Pb {
  message EndTimeRatio {
    double ratio = 1;
    string end_time = 2;
  }
  map<string, EndTimeRatio> playlet_end_time_discount_ratio = 1;
}

message IaaAcqGenStructPb {
  message IaaAcqGenVersionUnit {
    repeated int64 account_id = 1;
    double alpha_lower = 2;
    double beta_lower = 3;
    double alpha_upper = 4;
    double beta_upper = 5;
    double upper_bound = 6;
    double lower_bound = 7;
    double bucket_number = 8;
    double total_factor = 9;
    double lower_factor = 10;
    double upper_factor = 11;
  }
  map<string, IaaAcqGenVersionUnit> version = 1;
}
// end
