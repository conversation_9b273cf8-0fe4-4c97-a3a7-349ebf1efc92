#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafMixer
from dragonfly.common_leaf_util import check_arg, strict_types
from schema_manager.item_attr_schema import get_all_item_attrs

class RankHandlerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_rank_mixer"

class AdRankProcessPrepareMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_rank_process_prepare"

class AdRankObserveMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_rank_observe_mixer"

class ReturnResponseMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "return_response_mixer"

class CplSortMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "cpl_sort_mixer"

class RankAdlistCacheMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rank_adlist_cache_mixer"
  
class ScoreCacheMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "score_cache_mixer"

class MultiRequestCacheMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "multi_request_cache_mixer"

class RankServerPostProc(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rank_server_post_proc"

class MergeAdListMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merge_ad_list_mixer"

class RecoverAdListMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "recover_ad_list_mixer"

class NormalSortMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_sort_mixer"

class NativeSortMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_sort_mixer"

class UnifySortMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_sort_mixer"
  
class UnifyAuctionPrepareMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_auction_prepare_mixer"

class NormalCalNextBenifitMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_cal_next_benifit_mixer"

class NativeCalNextBenifitMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_cal_next_benifit_mixer"

class UnifyCalNextBenifitMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_cal_next_benifit_mixer"

class NormalRecordPriceResultMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_record_price_result_mixer"

class NativeRecordPriceResultMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_record_price_result_mixer"

class UnifyRecordPriceResultMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_record_price_result_mixer"

class PriceBound(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "price_bound"

class NormalSendAuctionLogMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "normal_send_auction_log_mixer"

class UnifySendAuctionLogMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "unify_send_auction_log_mixer"
class NormalSendMpcAuctionLogMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "normal_send_mpc_auction_log_mixer"
  
class UnifySendMpcAuctionLogMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "unify_send_mpc_auction_log_mixer"

class NativeSendAuctionLogMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "native_send_auction_log_mixer"

class NormalAppendRankListMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "normal_append_rank_list_mixer"

class NativeAppendRankListMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "native_append_rank_list_mixer"
  
class UnifyAppendRankListMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "unify_append_rank_list_mixer"

class NormalForceAdMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "normal_force_ad_mixer"

class NativeForceAdMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "native_force_ad_mixer"

class NormalRandomRerankMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "normal_random_rerank_mixer"

class MoveMobileSoftToHardMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "move_mobile_soft_to_hard_mixer"

class NormalPhotoDiversityMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "normal_photo_diversity_mixer"
class UnifyPhotoDiversityMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "unify_photo_diversity_mixer"
class NormalProductDiversityMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "normal_product_diversity_mixer"
class UnifyProductDiversityMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "unify_product_diversity_mixer"
class UnifyPvDiversityMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "unify_pv_diversity_mixer"
class CommonPvDiversityMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "common_pv_diversity_mixer"

class NativeHetuAndAuthorDiversityMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "native_hetu_and_author_diversity_mixer"
class UnifyHetuAndAuthorDiversityMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
      return "unify_hetu_and_author_diversity_mixer"

class TransListToPriceTableMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "trans_list_to_price_table_mixer"

class UnifyTransListToPriceTableMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_trans_list_to_price_table_mixer"

class CalcCustomerHcRatioMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_customer_hc_ratio_mixer"

class UnifyCalcCustomerHcRatioMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_calc_customer_hc_ratio_mixer"

class SetCachePredictProcess(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "set_predict_cache"

class GetCachePredictProcess(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "get_predict_cache"

class CalcPrice(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_price"

class GimbalPriceMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gimbal_price_mixer"

class UnifyGimbalPriceMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_gimbal_price_mixer"

class HcRecoverMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "hc_recover_mixer"

class UnifyHcRecoverMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_hc_recover_mixer"

class NormalCalCoinMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_cal_coin_mixer"

class NativeCalCoinMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_cal_coin_mixer"

class UnifyCalCoinMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_native_cal_coin_mixer"

class BuildRankResponseMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_rank_response_mixer"

class CommonSelectMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_select_mixer"

class ForceSelectMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "force_select_mixer"

class ParseRequest(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_request"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    """ 获取当前 Processor 产生的 item_attr 输出 """
    return set([f'global_ad_table::{attr_name}' for attr_name in get_all_item_attrs()])

class PredictProcess(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_process"

class RankServerPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rank_server_prepare"

class CalcSubsidyProcessor(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_subsidy_processor"

class InitParams(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "init_params"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"post_client_result",
                "feature_index_client_result",
                "qcpx_sky_fall_coupon_rpc"})

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.update(self._config.get("extract_item_attrs", []))
    return ret
class InspireStyle(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inspire_style"

class AmdCounter(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "amd_counter"

class AliPredict(LeafMixer):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)
    self.dragon_async = self._config.get("dragon_async", False)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ali_predict"
  @strict_types
  def is_async(self) -> bool:
    return self.dragon_async

class RtaSecondPredict(LeafMixer):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)
    self.dragon_async = self._config.get("dragon_async", False)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rta_second_predict"
  @strict_types
  def is_async(self) -> bool:
    return self.dragon_async

class AdStyleHandler(LeafMixer):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)
    self.dragon_async = self._config.get("dragon_async", False)
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_style_handler"
  @strict_types
  def is_async(self) -> bool:
    return self.dragon_async

class PredictWait(LeafMixer):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)
    self.use_predict_mixer = self._config.get("use_predict_mixer", False)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_wait"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    tmp = set({"ali_predict_result",
                  "rta_second_predict_result",
                  "ad_style_handler_result"})
    if self.use_predict_mixer:
      return tmp.union(set({"inner_native_ps", "inner_normal_ps",  "outer_normal_ps"}));
    else:
      return tmp

class UnifyCxr(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_cxr"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"ps_parallel_result"})

class ModelCalibrate(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "model_calibrate"

class CalcServerShowRatio(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_server_show_ratio"

class CalcCpm(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_cpm"

class CalcMerchantCouponMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_merchant_coupon"

class CalcNormalQcpxStrategy(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_normal_qcpx_strategy"

class CalcNativeQcpxStrategy(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_native_qcpx_strategy"

class CalcCidNormalQcpxStrategy(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_cid_normal_qcpx_strategy"

class NonConversionVisitorExplore(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "non_conversion_visitor_explore"

class MerchantEcpc(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_ecpc"

class RunUdf(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "run_udf_factor"

class RunPriceUdf(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "run_price_udf_factor"

class RegUdf(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "reg_dsl_factor"

class CalcIndustryHcFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_industry_hc_formula"

class CalcSupportHcFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_support_hc_formula"

class CalcSuppressHcFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_suppress_hc_formula"

class CalcOtherHcFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_other_hc_formula"

class CalcCustomerHcFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_customer_hc_formula"

class CalcTotalHcFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_total_hc_formula"

class BeforeBonusTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "before_bonus_transform"

class NormalHcAdmit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_hc_admit"

class NativeHcAdmit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_hc_admit"

class NormalOuterBonusAdmit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_outer_bonus_admit"

class NormalInnerBonusAdmit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_inner_bonus_admit"
class NativeInnerBonusAdmit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_inner_bonus_admit"

class NativeOuterBonusAdmit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_outer_bonus_admit"

class CalcBonusFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_bonus_formula"

class BeforeHcTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "before_hc_transform"

class AfterBonusTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "after_bonus_transform"

class AfterHcTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "after_hc_transform"

class RankUnifyFilterNormal(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "rank_unify_filter_normal"

class NativeCalcCoin(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_calc_coin"

class NativeCalcAdload(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_calc_adload"

class FormulaDotMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "formula_dot_mixer"

class SplashOuterRankingPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_outer_ranking_prepare"

class SplashInnerRankingPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_inner_ranking_prepare"

class SplashBoostCoefPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_boost_coef_prepare"

class SplashMergeAdList(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_merge_ad_list"

class SplashUnifyCxr(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_unify_cxr"

class SplashAuctionNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_auction_node"

class UniverseUnifyCxr(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_unify_cxr"

class UniverseRankingPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_ranking_prepare"

class UniverseRankingPrepareAsync(LeafMixer):
  @strict_types
  def __init__(self, config: dict):
    super().__init__(config)
    self.dragon_async = self._config.get("dragon_async", False)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_ranking_prepare_async"

  @strict_types
  def is_async(self) -> bool:
    return self.dragon_async

class UniverseCalculateBenefit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_calculate_benefit"

class UniverseAuction(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_auction"

class PostClientMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "post_client_mixer"

  @strict_types
  def is_async(self) -> bool:
    return True

class FeatureIndexClientMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "feature_index_client_mixer"

  @property
  @strict_types
  def output_common_attrs(self) -> set:
      attrs = []
      if self.is_async():
          attrs.append(self._config.get("save_async_status_to"))
      return attrs

  @strict_types
  def is_async(self) -> bool:
    return self._config.get("save_async_status_to") != None

class CalcCpmFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_cpm_formula"

class MultiplicationChainFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "multiplication_chain_formula"

class SumFactorProcessor(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sum_factor_processor"

class CommonFactorProcessor(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_factor_processor"

class SumFormulaProcessor(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "sum_formula_processor"

class CommonFormulaProcessor(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_formula_processor"

class AfterCpmTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "after_cpm_transform"

class BeforeCpmBoostTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "before_cpm_boost_transform"

class BeforeOriginCpmTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "before_origin_cpm_transform"

class BeforeServerClientShowRateTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "before_server_client_show_rate_transform"

class BeforeRankBenefitTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "before_rank_benefit_transform"

class SearchBeforeCalRankBenefitTransform(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_before_rank_benefit_transform"

class SearchFillLabelInfo(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_fill_label_info"

class CalcRankBenefitFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_rank_benefit_formula"

class CalculateBenefitAfterNormal(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_rank_benefit_after"

class CommonSwitchMockMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_switch_mock_mixer"

class AddUdfFactor(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "add_udf_factor"

class AdCommonFactor(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_common_factor"

class AddUdfFactorList(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "add_udf_factor_list"
class NormalSort(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_sort"

class NormalFilterPost(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_filter_post"

class NormalPrepareThreshold(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_prepare_threshold"

class NativeSort(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_sort"

class NativeSample(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_sample"

class NativeFilterPost(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_filter_post"

class RunUdfFilter(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "run_udf_filter"

class AdQuotaMarkMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_quota_mark_mixer"

class NormalFilterPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_filter_prepare"

class NativeFilterPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_filter_prepare"

class OutlierWatcher(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "outlier_watcher"

class MerchantLiveMinusContextAndItemMocker(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_live_minus_context_and_item_mocker"

class BonusCpmForInnerNativePhotoContextAndItemMocker(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "bonus_cpm_for_inner_native_photo_context_and_item_mocker"

class DemoP2pMocker(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "demo_p2p_mocker"

class AttrFromJsonEnricher(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "attr_from_json_enricher"

class AbtestMockerBase(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "abtest_mocker"

class CommonInit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_init"

class FillBid(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "fill_bid"

class ForceRecoAfterPredict(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "force_reco_after_predict"

class CalcNormalBenefitPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_normal_benefit_prepare"

class NormalCalcEcpmAdx(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_adx"

class NormalCalcEcpmCpmLive(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_cpm_live"

class NormalCalcEcpmCpcLive(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_cpc_live"

class NormalCalcEcpmCpc2Live(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_cpc2_live"

class NormalCalcEcpmCpaLive(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_cpa_live"

class NormalCalcEcpmOcpmLive(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_ocpm_live"

class NormalCalcEcpmOcpcLive(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_ocpc_live"

class NormalCalcEcpmCpmPhoto(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_cpm_photo"

class NormalCalcEcpmCpcPhoto(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_cpc_photo"

class NormalCalcEcpmCpaPhoto(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_cpa_photo"

class NormalCalcEcpmOcpmPhoto(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_ocpm_photo"

class NormalCalcEcpmMcb(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_calc_ecpm_mcb"

class CommonContextFeatureBuilder(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_context_feature_builder"

class InnerNativeItemFillMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_native_item_fill_mixer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    tmp = set({"inner_native_mp"})
    return tmp

class InnerNormalItemFillMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_normal_item_fill_mixer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    tmp = set({"inner_normal_mp"})
    return tmp
  
class OuterItemFillMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "outer_item_fill_mixer"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    tmp = set({"outer_normal_mp"})
    return tmp

class SplashInnerItemFillMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_inner_item_fill_mixer"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    tmp = set({"splash_inner_mp"})
    return tmp
  
class SplashOuterItemFillMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_outer_item_fill_mixer"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    tmp = set({"splash_outer_mp"})
    return tmp

class OuterContextFeatureBuilder(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "outer_context_feature_builder"

class InnerNormalContextFeatureBuilder(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_normal_context_feature_builder"

class InnerNativeContextFeatureBuilder(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_native_context_feature_builder"

class UserFeatureBuildMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_feature_build_mixer"

class ItemFeatureBuildMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "item_feature_build_mixer"

class ModelRegister(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "model_register"

class PredictPostProc(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_post_proc"

class ModelPredict(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "model_predict"
  @strict_types
  def is_async(self) -> bool:
    return True

class SearchModelPredictAsync(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "search_model_predict_async"
  @strict_types
  def is_async(self) -> bool:
    return self._config.get("save_async_status_to") != None

class AdRouterResponseProc(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_router_response_proc"
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    async_status_attr = self._config.get("async_status_attr", "")
    attrs = set()
    if async_status_attr:
      attrs.add(async_status_attr)
    return attrs

class AdRouterRequestPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_router_request_prepare"

class PredictPrepare(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_prepare"

class SecondStagePredict(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "second_stage_predict"

class SecondStagePredictWait(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "second_stage_predict_wait"

class CollectSampleNearline(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "collect_sample_nearline"

class InnerNativeCmdManagerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_native_cmd_manager_mixer"

class InnerNormalCmdManagerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "inner_normal_cmd_manager_mixer"

class OuterCmdManagerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "outer_cmd_manager_mixer"

class SplashInnerCmdManagerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_inner_cmd_manager_mixer"

class SplashOuterCmdManagerMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "splash_outer_cmd_manager_mixer"

class PredictValueWatcher(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "predict_value_watcher"

class NativeUnifyCalcPecCoupon(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_unify_calc_pec_coupon"

class NativeUnifyCalcEcpmRatio(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_unify_calc_ecpm_ratio"

class NativeUnifyCalcEcpm(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "native_calc_ecpm_extra_bid_type"

class ForceRecoMarkMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "force_reco_mark_mixer"

class UnifyAdloadControl(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_adload_control"

class AdloadAllocateOptim(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "adload_allocate_optim"

class SwitchTubeToInnerLoop(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "switch_tube_to_inner_loop"

class ModifyItemAttr(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "modify_item_attr"

class SetItemAttrByIndex(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "set_item_attr_by_index"

class ParseItemAttr(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "parse_item_attr"

class AggregateBidding(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "aggregate_bidding"

class CalcCpmForQpon(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_cpm_for_qpon"

class CalcCpmForBcee(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_cpm_for_bcee"

class CalcCalibratedCpmForPrerank(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_calibrated_cpm_for_prerank"

class CalcCalibrationCpm(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_calibration_cpm"

class CalibratePriceForQpon(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calibrate_price_for_qpon"

class CalcClientAiRerankScore(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_client_ai_rerank_score"

class UnifyEcpmBoundMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_ecpm_bound_mixer"

class UnifyCalcEcpm(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "unify_calc_ecpm"

class NormalResetUnifySctrMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "normal_reset_unify_sctr_mixer"

class MultiplySctrIntoCtrMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "multiply_sctr_into_ctr_mixer"

class CalcServerClientShowRate(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_server_client_show_rate"

class CalcHasDeepCvr(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_has_deep_cvr"

class CalcEcpmAdjust(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_ecpm_adjust"

class CalcEcpmUpperBound(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_ecpm_upper_bound"

class CalcEcpmLowerBound(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_ecpm_lower_bound"

class QcpxSkyFallCouponMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "qcpx_sky_fall_coupon_mixer"

  @strict_types
  def is_async(self) -> bool:
    return self._config.get("save_async_status_to") != None
