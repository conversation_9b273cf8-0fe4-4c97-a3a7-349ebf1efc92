
#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from ad_rank_dragonfly.ad_rank_mixer import *
from dragonfly.common_leaf_dsl import CommonApiMixin
class AdRankMixin(CommonApiMixin, CommonLeafBaseMixin):
  def ad_rank_mixer(self, **kwargs):
    """
    RankHandlerMixer
    """
    self._add_processor(RankHandlerMixer(kwargs))
    return self

  def ad_rank_process_prepare(self, **kwargs):
    """
    AdRankProcessPrepare
    rank 图化 Request 处理部分
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .ad_rank_process_prepare()
    ```
    """
    self._add_processor(AdRankProcessPrepareMixer(kwargs))
    return self

  def cpl_sort_mixer(self, **kwargs):
    """
    CplSortMixer
    cpl 排序
    参数配置
    ------

    调用示例
    ------
    ``` python
    .cpl_sort_mixer()
    ```
    """
    self._add_processor(CplSortMixer(kwargs))
    return self

  def ad_rank_observe_mixer(self, **kwargs):
    """
    AdRankObserveMixer
    response 返回后日志 打点
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .ad_rank_observe_mixer()
    ```
    """
    self._add_processor(AdRankObserveMixer(kwargs))
    return self

  def return_response_mixer(self, **kwargs):
    """
    ReturnResponseMixer
    填充 rank_response
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .return_response_mixer()
    ```
    """
    self._add_processor(ReturnResponseMixer(kwargs))
    return self
    
  def rank_adlist_cache_mixer(self, **kwargs):
    """
    RankAdlistCacheMixer
    缓存精排队列
    参数配置
    ------
    

    调用示例
    ------
    ``` python
    .rank_adlist_cache_mixer()
    ```
    """
    self._add_processor(RankAdlistCacheMixer(kwargs))
    return self
    
  def multi_request_cache_mixer(self, **kwargs):
    """
    MultiRequestCacheMixer
    多请求缓存精排队列
    参数配置
    ------
    
    调用示例
    ------
    ``` python
    .multi_request_cache_mixer()
    ```
    """
    self._add_processor(MultiRequestCacheMixer(kwargs))
    return self
  
  def score_cache_mixer(self, is_nearline: bool = False, **kwargs):
    """
    ScoreCacheMixer
    缓存精排打分
    参数配置
    ------
    

    调用示例
    ------
    ``` python
    .score_cache_mixer()
    ```
    """
    if not is_nearline:
      self._add_processor(ScoreCacheMixer(kwargs))
    return self

  def rank_server_post_proc(self, **kwargs):
    """
    RankServerPostProc
    post proc 打点
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .rank_server_post_proc()
    ```
    """
    self._add_processor(RankServerPostProc(kwargs))
    return self

  def merge_ad_list_mixer(self, **kwargs):
    """
    MergeAdListMixer
    队列合并
    参数配置
    ------

    调用示例
    ------
    ``` python
    .merge_ad_list_mixer()
    ```
    """
    self._add_processor(MergeAdListMixer(kwargs))
    return self

  def recover_ad_list_mixer(self, **kwargs):
    """
    RecoverAdListMixer
    队列还原
    参数配置
    ------

    调用示例
    ------
    ``` python
    .recover_ad_list_mixer()
    ```
    """
    self._add_processor(RecoverAdListMixer(kwargs))
    return self

  def normal_sort_mixer(self, **kwargs):
    """
    NormalSortMixer
    front 硬广排序
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_sort_mixer()
    ```
    """
    self._add_processor(NormalSortMixer(kwargs))
    return self

  def native_sort_mixer(self, **kwargs):
    """
    NativeSortMixer
    front 软广排序
    参数配置
    ------

    调用示例
    ------
    ``` python
    .native_sort_mixer()
    ```
    """
    self._add_processor(NativeSortMixer(kwargs))
    return self

  def unify_sort_mixer(self, **kwargs):
    """
    UnifySortMixer
    front 统一排序
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_sort_mixer()
    ```
    """
    self._add_processor(UnifySortMixer(kwargs))
    return self
  
  def unify_auction_prepare_mixer(self, **kwargs):
    """
    UnifyAuctionPrepareMixer
    竞价前prepare
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_auction_prepare_mixer()
    ```
    """
    self._add_processor(UnifyAuctionPrepareMixer(kwargs))
    return self

  def normal_cal_next_benifit_mixer(self, **kwargs):
    """
    NormalCalNextBenifitMixer
    front 硬广计算 nextrb
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_cal_next_benifit_mixer()
    ```
    """
    self._add_processor(NormalCalNextBenifitMixer(kwargs))
    return self

  def native_cal_next_benifit_mixer(self, **kwargs):
    """
    NativeCalNextBenifitMixer
    front 软广计算 nextrb
    参数配置
    ------

    调用示例
    ------
    ``` python
    .native_cal_next_benifit_mixer()
    ```
    """
    self._add_processor(NativeCalNextBenifitMixer(kwargs))
    return self

  def unify_cal_next_benifit_mixer(self, **kwargs):
    """
    UnifyCalNextBenifitMixer
    front 统一计算 nextrb
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_cal_next_benifit_mixer()
    ```
    """
    self._add_processor(UnifyCalNextBenifitMixer(kwargs))
    return self

  def trans_list_to_price_table_mixer(self, **kwargs):
    """
    TransListToPriceTableMixer
    item table 转化
    参数配置
    ------

    调用示例
    ------
    ``` python
    .trans_list_to_price_table_mixer()
    ```
    """
    self._add_processor(TransListToPriceTableMixer(kwargs))
    return self

  def unify_trans_list_to_price_table_mixer(self, **kwargs):
    """
    UnifyTransListToPriceTableMixer
    item table 转化
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_trans_list_to_price_table_mixer()
    ```
    """
    self._add_processor(UnifyTransListToPriceTableMixer(kwargs))
    return self

  def normal_record_price_result_mixer(self, **kwargs):
    """
    NormalRecordPriceResultMixer
    记录计费结果
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_record_price_result_mixer()
    ```
    """
    self._add_processor(NormalRecordPriceResultMixer(kwargs))
    return self

  def native_record_price_result_mixer(self, **kwargs):
    """
    NativeRecordPriceResultMixer
    记录计费结果
    参数配置
    ------

    调用示例
    ------
    ``` python
    .native_record_price_result_mixer()
    ```
    """
    self._add_processor(NativeRecordPriceResultMixer(kwargs))
    return self

  def unify_record_price_result_mixer(self, **kwargs):
    """
    UnifyRecordPriceResultMixer
    记录计费结果
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_record_price_result_mixer()
    ```
    """
    self._add_processor(UnifyRecordPriceResultMixer(kwargs))
    return self

  def hc_recover_mixer(self, **kwargs):
    """
    HcRecoverMixer
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .hc_recover_mixer()
    ```
    """
    self._add_processor(HcRecoverMixer(kwargs))
    return self

  def unify_hc_recover_mixer(self, **kwargs):
    """
    UnifyHcRecoverMixer
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_hc_recover_mixer()
    ```
    """
    self._add_processor(UnifyHcRecoverMixer(kwargs))
    return self

  def gimbal_price_mixer(self, **kwargs):
    """
    GimbalPriceMixer
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .gimbal_price_mixer()
    ```
    """
    self._add_processor(GimbalPriceMixer(kwargs))
    return self

  def unify_gimbal_price_mixer(self, **kwargs):
    """
    UnifyGimbalPriceMixer
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_gimbal_price_mixer()
    ```
    """
    self._add_processor(UnifyGimbalPriceMixer(kwargs))
    return self

  def normal_cal_coin_mixer(self, **kwargs):
    """
    NormalCalCoinMixer
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_cal_coin_mixer()
    ```
    """
    self._add_processor(NormalCalCoinMixer(kwargs))
    return self

  def native_cal_coin_mixer(self, **kwargs):
    """
    NativeCalCoinMixer
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .native_cal_coin_mixer()
    ```
    """
    self._add_processor(NativeCalCoinMixer(kwargs))
    return self

  def unify_cal_coin_mixer(self, **kwargs):
    """
    UnifyCalCoinMixer
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_cal_coin_mixer()
    ```
    """
    self._add_processor(UnifyCalCoinMixer(kwargs))
    return self

  def calc_customer_hc_ratio_mixer(self, **kwargs):
    """
    CalcCustomerHcRatioMixer
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_customer_hc_ratio_mixer()
    ```
    """
    self._add_processor(CalcCustomerHcRatioMixer(kwargs))
    return self

  def unify_calc_customer_hc_ratio_mixer(self, **kwargs):
    """
    UnifyCalcCustomerHcRatioMixer
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_calc_customer_hc_ratio_mixer()
    ```
    """
    self._add_processor(UnifyCalcCustomerHcRatioMixer(kwargs))
    return self

  def calc_price(self, **kwargs):
    """
    CalcPrice
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_price()
    ```
    """
    self._add_processor(CalcPrice(kwargs))
    return self

  def get_predict_cache(self, is_nearline: bool = False, **kwargs):
    """
    GetCachePredictProcess
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .get_predict_cache()
    ```
    """
    if not is_nearline:
      self._add_processor(GetCachePredictProcess(kwargs))
    return self

  def set_predict_cache(self, is_nearline: bool = False, **kwargs):
    """
    setCachePredictProcess
    出价计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .set_predict_cache()
    ```
    """
    if not is_nearline:
      self._add_processor(SetCachePredictProcess(kwargs))
    return self

  def price_bound(self, **kwargs):
    """
    PriceBound
    出价 bound
    参数配置
    ------

    调用示例
    ------
    ``` python
    .price_bound()
    ```
    """
    self._add_processor(PriceBound(kwargs))
    return self

  def normal_send_auction_log_mixer(self, **kwargs):
    """
    NormalSendAuctionLogMixer
    竞价日志发送
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_send_auction_log_mixer()
    ```
    """
    self._add_processor(NormalSendAuctionLogMixer(kwargs))
    return self
  
  def unify_send_auction_log_mixer(self, **kwargs):
    """
    UnifySendAuctionLogMixer
    竞价日志发送
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_send_auction_log_mixer()
    ```
    """
    self._add_processor(UnifySendAuctionLogMixer(kwargs))
    return self

  def native_send_auction_log_mixer(self, **kwargs):
    """
    NativeSendAuctionLogMixer
    竞价日志发送
    参数配置
    ------

    调用示例
    ------
    ``` python
    .native_send_auction_log_mixer()
    ```
    """
    self._add_processor(NativeSendAuctionLogMixer(kwargs))
    return self

  def normal_send_mpc_auction_log_mixer(self, **kwargs):
    """
    NormalSendMpcAuctionLogMixer
    竞价日志发送
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_send_mpc_auction_log_mixer()
    ```
    """
    self._add_processor(NormalSendMpcAuctionLogMixer(kwargs))
    return self
  
  def unify_send_mpc_auction_log_mixer(self, **kwargs):
    """
    UnifySendMpcAuctionLogMixer
    竞价日志发送
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_send_mpc_auction_log_mixer()
    ```
    """
    self._add_processor(UnifySendMpcAuctionLogMixer(kwargs))
    return self

  def normal_append_rank_list_mixer(self, **kwargs):
    """
    NormalAppendRankListMixer
    增加竞价日志
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_append_rank_list_mixer()
    ```
    """
    self._add_processor(NormalAppendRankListMixer(kwargs))
    return self

  def native_append_rank_list_mixer(self, **kwargs):
    """
    NativeAppendRankListMixer
    增加竞价日志
    参数配置
    ------

    调用示例
    ------
    ``` python
    .native_append_rank_list_mixer()
    ```
    """
    self._add_processor(NativeAppendRankListMixer(kwargs))
    return self
  
  def unify_append_rank_list_mixer(self, **kwargs):
    """
    UnifyAppendRankListMixer
    增加竞价日志
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_append_rank_list_mixer()
    ```
    """
    self._add_processor(UnifyAppendRankListMixer(kwargs))
    return self

  def normal_force_ad_mixer(self, **kwargs):
    """
    NormalForceAdMixer
    硬广强出重排
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_force_ad_mixer()
    ```
    """
    self._add_processor(NormalForceAdMixer(kwargs))
    return self
  
  def native_force_ad_mixer(self, **kwargs):
    """
    NativeForceAdMixer
    软广强出重排
    参数配置
    ------

    调用示例
    ------
    ``` python
    .native_force_ad_mixer()
    ```
    """
    self._add_processor(NativeForceAdMixer(kwargs))
    return self

  def normal_random_rerank_mixer(self, **kwargs):
    """
    NormalRandomRerankMixer
    软广强出重排
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_random_rerank_mixer()
    ```
    """
    self._add_processor(NormalRandomRerankMixer(kwargs))
    return self

  def move_mobile_soft_to_hard_mixer(self, **kwargs):
    """
    MoveMobileSoftToHardMixer
    软广合并成软广
    参数配置
    ------

    调用示例
    ------
    ``` python
    .move_mobile_soft_to_hard_mixer()
    ```
    """
    self._add_processor(MoveMobileSoftToHardMixer(kwargs))
    return self

  def normal_photo_diversity_mixer(self, **kwargs):
    """
    NormalPhotoDiversityMixer
    photo 多样性过滤
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_photo_diversity_mixer()
    ```
    """
    self._add_processor(NormalPhotoDiversityMixer(kwargs))
    return self
  
  def unify_photo_diversity_mixer(self, **kwargs):
    """
    UnifyPhotoDiversityMixer
    photo 多样性过滤
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_photo_diversity_mixer()
    ```
    """
    self._add_processor(UnifyPhotoDiversityMixer(kwargs))
    return self

  def unify_pv_diversity_mixer(self, **kwargs):
    """
    UnifyPvDiversityMixer
    pv 多样性过滤
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_pv_diversity_mixer()
    ```
    """
    self._add_processor(UnifyPvDiversityMixer(kwargs))
    return self

  def common_pv_diversity_mixer(self, **kwargs):
    """
    CommonPvDiversityMixer
    pv 多样性过滤
    参数配置
    ------

    调用示例
    ------
    ``` python
    .common_pv_diversity_mixer()
    ```
    """
    self._add_processor(CommonPvDiversityMixer(kwargs))
    return self

  def normal_product_diversity_mixer(self, **kwargs):
    """
    NormalProductDiversityMixer
    product 多样性过滤
    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_product_diversity_mixer()
    ```
    """
    self._add_processor(NormalProductDiversityMixer(kwargs))
    return self
  
  def unify_product_diversity_mixer(self, **kwargs):
    """
    UnifyProductDiversityMixer
    product 多样性过滤
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_product_diversity_mixer()
    ```
    """
    self._add_processor(UnifyProductDiversityMixer(kwargs))
    return self

  def native_hetu_and_author_diversity_mixer(self, **kwargs):
    """
    NativeHetuAndAuthorDiversityMixer
    软广多样性过滤
    参数配置
    ------

    调用示例
    ------
    ``` python
    .native_hetu_and_author_diversity_mixer()
    ```
    """
    self._add_processor(NativeHetuAndAuthorDiversityMixer(kwargs))
    return self
  
  def unify_hetu_and_author_diversity_mixer(self, **kwargs):
    """
    UnifyHetuAndAuthorDiversityMixer
    统一多样性过滤
    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_hetu_and_author_diversity_mixer()
    ```
    """
    self._add_processor(UnifyHetuAndAuthorDiversityMixer(kwargs))
    return self

  def build_rank_response_mixer(self, **kwargs):
    """
    BuildRankResponseMixer
    队列合并
    参数配置
    ------

    调用示例
    ------
    ``` python
    .build_rank_response_mixer()
    ```
    """
    self._add_processor(BuildRankResponseMixer(kwargs))
    return self

  def common_select_mixer(self, **kwargs):
    """
    CommonSelectMixer
    通用优选
    参数配置
    ------

    调用示例
    ------
    ``` python
    .common_select_mixer()
    ```
    """
    self._add_processor(CommonSelectMixer(kwargs))
    return self

  def force_select_mixer(self, **kwargs):
    """
    ForceSelectMixer
    强出优选
    参数配置
    ------

    调用示例
    ------
    ``` python
    .force_select_mixer()
    ```
    """
    self._add_processor(ForceSelectMixer(kwargs))
    return self

  def parse_request(self, **kwargs):
    """
    ParseRequest
    解析 request
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .parse_request()
    ```
    """
    self._add_processor(ParseRequest(kwargs))
    return self

  def predict_process(self, **kwargs):
    """
    PredictProcess
    预估服务处理
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .predict_process()
    ```
    """
    self._add_processor(PredictProcess(kwargs))
    return self

  def rank_server_prepare(self, **kwargs):
    """
    RankServerPrepare
    排序数据准备
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .rank_server_prepare()
    ```
    """
    self._add_processor(RankServerPrepare(kwargs))
    return self

  def calc_subsidy_processor(self, **kwargs):
    """
    CalcSubsidyProcessor
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .calc_subsidy_processor(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcSubsidyProcessor(kwargs))
    return self

  def init_params(self, **kwargs):
    """
    InitParams
    初始化参数
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .init_params()
    ```
    """
    self._add_processor(InitParams(kwargs))
    return self

  def inspire_style(self, **kwargs):
    """
    InspireStyle
    激励样式准入策略
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .inspire_style()
    ```
    """
    self._add_processor(InspireStyle(kwargs))
    return self

  def amd_counter(self, **kwargs):
    """
    AmdCounter
    电商直播广告处理
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .amd_counter()
    ```
    """
    self._add_processor(AmdCounter(kwargs))
    return self

  def ali_predict(self, **kwargs):
    """
    AliPredict
    阿里外投广告预估服务
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .ali_predict()
    ```
    """
    self._add_processor(AliPredict(kwargs))
    return self

  def rta_second_predict(self, **kwargs):
    """
    RtaSecondPredict
    请求 RTA 二次预估
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .rta_second_predict()
    ```
    """
    self._add_processor(RtaSecondPredict(kwargs))
    return self

  def ad_style_handler(self, **kwargs):
    """
    AdStyleHandler
    获取广告样式
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .ad_style_handler()
    ```
    """
    self._add_processor(AdStyleHandler(kwargs))
    return self

  def predict_wait(self, **kwargs):
    """
    PredictWait
    处理 cmd 预估服务结果
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .predict_wait()
    ```
    """
    self._add_processor(PredictWait(kwargs))
    return self

  def unify_cxr(self, **kwargs):
    """
    UnifyCxr
    统一 R 值
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .unify_cxr()
    ```
    """
    self._add_processor(UnifyCxr(kwargs))
    return self

  def model_calibrate(self, **kwargs):
    """
    ModelCalibrate
    请求模型校准服务
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .model_calibrate()
    ```
    """
    self._add_processor(ModelCalibrate(kwargs))
    return self

  def calc_server_show_ratio(self, **kwargs):
    """
    CalcServerShowRatio
    计算 server show ratio
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .calc_server_show_ratio()
    ```
    """
    self._add_processor(CalcServerShowRatio(kwargs))
    return self

  def calc_cpm(self, **kwargs):
    """
    CalcCpm
    计算 cpm
     参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .calc_cpm()
    ```
    """
    self._add_processor(CalcCpm(kwargs))
    return self

  def calc_merchant_coupon(self, **kwargs):
    """
    CalcMerchantCouponMixer
    计算电商优惠券
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .calc_merchant_coupon()
    ```
    """
    self._add_processor(CalcMerchantCouponMixer(kwargs))
    return self

  def calc_normal_qcpx_strategy(self, **kwargs):
    """
    CalcNormalQcpxStrategy
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .calc_normal_qcpx_strategy()
    ```
    """
    self._add_processor(CalcNormalQcpxStrategy(kwargs))
    return self

  def calc_native_qcpx_strategy(self, **kwargs):
    """
    CalcNativeQcpxStrategy
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .calc_native_qcpx_strategy()
    ```
    """
    self._add_processor(CalcNativeQcpxStrategy(kwargs))
    return self

  def calc_cid_normal_qcpx_strategy(self, **kwargs):
      """
      CalcCidNormalQcpxStrategy
      参数配置
      ------
      原有node算子迁移，暂无参数
      调用示例
      ------
      ``` python
      .calc_cid_normal_qcpx_strategy()
      ```
      """
      self._add_processor(CalcCidNormalQcpxStrategy(kwargs))
      return self


  def non_conversion_visitor_explore(self, **kwargs):
    """
    NonConversionVisitorExplore
    未转化人群策略
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .non_conversion_visitor_explore()
    ```
    """
    self._add_processor(NonConversionVisitorExplore(kwargs))
    return self

  def merchant_ecpc(self, **kwargs):
    """
    MerchantEcpc
    电商 ecpc
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .merchant_ecpc()
    ```
    """
    self._add_processor(MerchantEcpc(kwargs))
    return self

  def normal_outer_bonus_admit(self, **kwargs):
    """
    NormalOuterBonusAdmit
    硬广 - 外循环 - 通用准入逻辑
    参数配置
    -----
    `item_table`: [string] 必配项 item表名
    `formula_type_list`: [list] 必配项 准入结果生效的bonus公式枚举list

    调用示例
    --------
    ``` python
   .normal_outer_bonus_admit(
      item_table = "ad_list_item_table",
      formula_type_list = [
        get_enum(Formula.NormalOuterBonus)])

    """
    self._add_processor(NormalOuterBonusAdmit(kwargs))
    return self

  def native_outer_bonus_admit(self, **kwargs):
    """
    NativeOuterBonusAdmit
    软广 - 外循环 - 通用准入逻辑
    参数配置
    -----
    `item_table`: [string] 必配项 item表名
    `formula_type_list`: [list] 必配项 准入结果生效的bonus公式枚举list

    调用示例
    --------
    ``` python
   .native_outer_bonus_admit(
      item_table = item_table,
      formula_type_list = [
        get_enum(Formula.NativeOuterBonus)])

    """
    self._add_processor(NativeOuterBonusAdmit(kwargs))
    return self

  def native_inner_bonus_admit(self, **kwargs):
    """
    NativeInnerBonusAdmit
    软广 - 内循环 - 通用准入逻辑
    参数配置
    -----
    `item_table`: [string] 必配项 item表名
    `formula_type_list`: [list] 必配项 准入结果生效的bonus公式枚举list

    调用示例
    --------
    ``` python
   .native_inner_bonus_admit(
      item_table = item_table,
      formula_type_list = [
        get_enum(Formula.NativeInnerBonus)])

    """
    self._add_processor(NativeInnerBonusAdmit(kwargs))
    return self

  def normal_inner_bonus_admit(self, **kwargs):
    """
    NormalInnerBonusAdmit
    硬广 - 内循环 - 通用准入逻辑
    参数配置
    -----
    `item_table`: [string] 必配项 item表名
    `formula_type_list`: [list] 必配项 准入结果生效的bonus公式枚举list

    调用示例
    --------
    ``` python
   .normal_inner_bonus_admit(
      item_table = "ad_list_item_table",
      formula_type_list = [
        get_enum(Formula.NormalInnerBonus)])

    """
    self._add_processor(NormalInnerBonusAdmit(kwargs))
    return self

  def normal_hc_admit(self, **kwargs):
    """
    NormalHcAdmit
    硬广hc通用准入逻辑
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `formula_type_list`: [list] 必配项 准入结果生效的hc公式枚举list

    调用示例
    ------
    ``` python
   .native_hc_admit(
      item_table = "ad_list_item_table",
      formula_type_list = [
        get_enum(Formula.NormalIndustryHcFormula),
        get_enum(Formula.NormalTrafficSupportHcFormula),
        get_enum(Formula.NormalTrafficSuppressHcFormula),
        get_enum(Formula.NormalOtherHcFormula)])
    ```
    """
    self._add_processor(NormalHcAdmit(kwargs))
    return self

  def native_hc_admit(self, **kwargs):
    """
    NativeHcAdmit
    软广hc通用准入逻辑
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `formula_type_list`: [list] 必配项 准入结果生效的hc公式枚举list

    调用示例
    ------
    ``` python
   .native_hc_admit(
      item_table = "ad_list_item_table",
      formula_type_list = [
        get_enum(Formula.NativeIndustryHcFormula),
        get_enum(Formula.NativeTrafficSupportHcFormula),
        get_enum(Formula.NativeTrafficSuppressHcFormula),
        get_enum(Formula.NativeOtherHcFormula)])
    ```
    """
    self._add_processor(NativeHcAdmit(kwargs))
    return self

  def calc_industry_hc_formula(self, **kwargs):
    """
    CalcIndustryHcFormula
    行业hc公式计算, 内部包含: bound、max、行业hc调控
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 输入hc因子的公式枚举list

    `output_formula_type`: [enum] 必配项 输出hc结果的公式枚举

    `hc_ad_admit_attr`: [string] 必配项 hc ad维度准入结果列名

    `hc_admit_attr`: [string] 必配项  hc 请求维度准入结果列名

    调用示例
    ------
    ``` python
    .calc_industry_hc_formula(
      item_table = "ad_list_item_table",
      input_formula_type_list=[get_enum(Formula.NormalIndustryHcFormula)],
      output_formula_type = get_enum(Formula.CalcIndustryHiddenCostFormula),
      hc_ad_admit_attr = "normal_hc_ad_admit.res",
      hc_admit_attr="normal_hc_admit.res")
    ```
    """
    self._add_processor(CalcIndustryHcFormula(kwargs))
    return self

  def calc_support_hc_formula(self, **kwargs):
    """
    CalcSupportHcFormula
    扶持类 hc公式计算, 内部包含: bound、max
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 输入hc因子的公式枚举list

    `output_formula_type`: [enum] 必配项 输出hc结果的公式枚举

    `hc_ad_admit_attr`: [string] 必配项 hc ad维度准入结果列名

    `hc_admit_attr`: [string] 必配项  hc 请求维度准入结果列名

    调用示例
    ------
    ``` python
   .calc_support_hc_formula(
      item_table = "ad_list_item_table",
      input_formula_type_list=[get_enum(Formula.NativeTrafficSupportHcFormula)],
      output_formula_type = get_enum(Formula.CalcSupportHiddenCostFormula),
      hc_ad_admit_attr = "native_hc_ad_admit.res",
      hc_admit_attr="native_hc_admit.res")
    ```
    """
    self._add_processor(CalcSupportHcFormula(kwargs))
    return self

  def calc_suppress_hc_formula(self, **kwargs):
    """
    CalcSuppressHcFormula
    打压类 hc公式计算, 内部包含: bound、min
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 输入hc因子的公式枚举list

    `output_formula_type`: [enum] 必配项 输出hc结果的公式枚举

    `hc_ad_admit_attr`: [string] 必配项 hc ad维度准入结果列名

    `hc_admit_attr`: [string] 必配项  hc 请求维度准入结果列名

    调用示例
    ------
    ``` python
   .calc_suppress_hc_formula(
      item_table = "ad_list_item_table",
      input_formula_type_list=[get_enum(Formula.NativeTrafficSupportHcFormula)],
      output_formula_type = get_enum(Formula.CalcSupportHiddenCostFormula),
      hc_ad_admit_attr = "native_hc_ad_admit.res",
      hc_admit_attr="native_hc_admit.res")
    ```
    """
    self._add_processor(CalcSuppressHcFormula(kwargs))
    return self

  def calc_other_hc_formula(self, **kwargs):
    """
    CalcOtherHcFormula
    其他类 hc公式计算, 内部包含: bound、sum
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 输入hc因子的公式枚举list

    `output_formula_type`: [enum] 必配项 输出hc结果的公式枚举

    `hc_ad_admit_attr`: [string] 必配项 hc ad维度准入结果列名

    `hc_admit_attr`: [string] 必配项  hc 请求维度准入结果列名

    调用示例
    ------
    ``` python
   .calc_other_hc_formula(
      item_table = "ad_list_item_table",
      input_formula_type_list=[get_enum(Formula.NormalOtherHcFormula)],
      output_formula_type = get_enum(Formula.CalcOtherHiddenCostFormula),
      hc_ad_admit_attr = "normal_hc_ad_admit.res",
      hc_admit_attr="normal_hc_admit.res")
    ```
    """
    self._add_processor(CalcOtherHcFormula(kwargs))
    return self

  def calc_customer_hc_formula(self, **kwargs):
    """
    CalcCustomerHcFormula
    客户类类 hc公式计算, 内部包含: bound、sum
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 输入hc因子的公式枚举list

    `output_formula_type`: [enum] 必配项 输出hc结果的公式枚举

    `hc_ad_admit_attr`: [string] 必配项 hc ad维度准入结果列名

    `hc_admit_attr`: [string] 必配项  hc 请求维度准入结果列名

    调用示例
    ------
    ``` python
   .calc_other_hc_formula(
      item_table = "ad_list_item_table",
      input_formula_type_list=[get_enum(Formula.NormalCustomerHcFormula)],
      output_formula_type = get_enum(Formula.CalcCustomerHiddenCostFormula),
      hc_ad_admit_attr = "normal_hc_ad_admit.res",
      hc_admit_attr="normal_hc_admit.res")
    ```
    """
    self._add_processor(CalcCustomerHcFormula(kwargs))
    return self


  def calc_total_hc_formula(self, **kwargs):
    """
    CalcTotalHcFormula
    扶持类 hc公式计算, 内部包含: sum 和 total hc调控
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 输入hc因子的公式枚举list

    `output_formula_type`: [enum] 必配项 输出hc结果的公式枚举

    `hc_ad_admit_attr`: [string] 必配项 hc ad维度准入结果列名

    `hc_admit_attr`: [string] 必配项  hc 请求维度准入结果列名

    调用示例
    ------
    ``` python
   .calc_total_hc_formula(
      item_table = "ad_list_item_table",
      input_formula_type_list = [
        get_enum(Formula.CalcIndustryHiddenCostFormula),
        get_enum(Formula.CalcSupportHiddenCostFormula),
        get_enum(Formula.CalcSuppressHiddenCostFormula),
        get_enum(Formula.CalcOtherHiddenCostFormula)],
      output_formula_type = get_enum(Formula.CalcTotalHiddenCostFormula),
      hc_ad_admit_attr = "native_hc_ad_admit.res",
      hc_admit_attr="native_hc_admit.res")
    ```
    """
    self._add_processor(CalcTotalHcFormula(kwargs))
    return self

  def run_price_udf_factor(self, **kwargs):
    """
    RunPriceUdf
    udf 子执行算子
    参数配置
    ------
    `name`: [string] 必填项 因子名称 描述

    `item_table`: [string] 必配项 item表名

    `formula_type`: [enum] 必填项 因子所属公式的枚举

    `rank_factor_type`: [enum] 必填项 因子枚举

    `admit`: [string] 必配项 因子的准入函数名

    `compute`: [string] 必配项 因子的算分函数名

    调用示例
    ------
    ``` python
    run_price_udf_factor(
      name = "Native_S_DiversityExplore",
      item_table = item_table,
      formula_type = get_enum(Formula.NativeTrafficSuppressHcFormula),
      rank_factor_type = get_enum(SuppressHCTag.S_DiversityExplore),
      admit = "CalcDiversityExploreHC_Admit",
      compute = "CalcDiversityExploreHC_Compute")
    ```
    """
    self._add_processor(RunPriceUdf(kwargs))
    return self

  def run_udf_factor(self, **kwargs):
    """
    RunUdf
    udf 子执行算子
    参数配置
    ------
    `name`: [string] 必填项 因子名称 描述

    `item_table`: [string] 必配项 item表名

    `formula_type`: [enum] 必填项 因子所属公式的枚举

    `rank_factor_type`: [enum] 必填项 因子枚举

    `admit`: [string] 必配项 因子的准入函数名

    `compute`: [string] 必配项 因子的算分函数名

    调用示例
    ------
    ``` python
    run_udf_factor(
      name = "Native_S_DiversityExplore",
      item_table = item_table,
      formula_type = get_enum(Formula.NativeTrafficSuppressHcFormula),
      rank_factor_type = get_enum(SuppressHCTag.S_DiversityExplore),
      admit = "CalcDiversityExploreHC_Admit",
      compute = "CalcDiversityExploreHC_Compute")
    ```
    """
    self._add_processor(RunUdf(kwargs))
    return self

  def reg_dsl_factor(self, **kwargs):
    """
    RegUdf

    注册 udf 计算算子

    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 公式输入字段

    `output_formula_type`: [string] 必配项 公式结果

    `op_type`: [string] 必配项 运算类型，支持 "SUM", ”MULTI", "MAX", "MIN"

    调用示例
    ------
    ``` python
    .reg_dsl_factor(
      formula_type = formula_type,
      rank_factor_type = rank_factor_type
    )
    ```
    """
    self._add_processor(RegUdf(kwargs))
    return self

  def before_bonus_transform(self, **kwargs):
    """
    BeforeBonusTransform
    bouns 迁移预处理(暂时没有用到)
    """
    self._add_processor(BeforeBonusTransform(kwargs))
    return self

  def after_bonus_transform(self, **kwargs):
    """
    AfterBonusTransform
    bouns 迁移后处理
    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `bonus_cpm_name`: [string] 最后一个公式输出cpm列名，默认为$CalcFinalBonusFormula.formula_compute

    `bonus_formula_tag_name`: [string] 最后一个公式输出tag列名，默认为CalcFinalBonusFormula默认为CalcFinalBonusFormula.final_rank_factor_type"

    调用示例
    ------
    ``` python
    .after_bonus_transform(
      item_table = "ad_list_item_table",
      bonus_cpm_name = "$CalcFinalBonusFormula.formula_compute",
      bonus_formula_tag_name = "$CalcFinalBonusFormula.final_rank_factor_type")
    ```
    """
    self._add_processor(AfterBonusTransform(kwargs))
    return self

  def calc_bonus_formula(self, **kwargs):
    """
    CalcBonusFormula
    bouns 计算公式
    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 计算所用到的公式

    `output_formula_type`: [enum] 输出当前计算的公式

    调用示例
    ------
    ``` python
    .calc_bonus_formula(
      item_table = "ad_list_item_table",
      input_formula_type_list=[get_enum(Formula.NormalBonusFormula)],
      output_formula_type = get_enum(Formula.CalcFinalBonusFormula))
    ```
    """
    self._add_processor(CalcBonusFormula(kwargs))
    return self

  def before_hc_transform(self, **kwargs):
    """
    BeforeHcTransform
    hc 迁移预处理
    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .before_hc_transform(
        item_table = "ad_list_item_table")
    ```
    """
    self._add_processor(BeforeHcTransform(kwargs))
    return self

  def after_hc_transform(self, **kwargs):
    """
    AfterHcTransform
    hc 迁移后处理算子, 将DataFrame化hc结果转换成原结构
    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `total_hc_name`: [string] 总hc attr列名

    `suppress_hc_name`: [string] 流量打压hc attr列名

    `traffic_hc_name`: [string] 流量扶持hc attr列名

    `industry_hc_name`: [string] 行业hc attr列名

    `other_hc_name`: [string] 其他类hc attr列名

    `industry_hc_tag_name`: [string] 行业hc胜出因子tag列名

    `suppress_hc_tag_name`: [string] 流量打压hc胜出因子tag列名

    `traffic_hc_tag_name`: [string] 流量扶持hc胜出因子tag列名

    `strategy_manager_list`: [list] hc依赖的因子公式名

    调用示例
    ------
    ``` python
    .after_hc_transform(
        item_table = "ad_list_item_table",
        total_hc_name = "$CalcTotalHiddenCostFormula.formula_compute",
        suppress_hc_name = "$CalcSuppressHiddenCostFormula.formula_compute",
        traffic_hc_name = "$CalcSupportHiddenCostFormula.formula_compute",
        industry_hc_name = "$CalcIndustryHiddenCostFormula.formula_compute",
        other_hc_name = "$CalcOtherHiddenCostFormula.formula_compute",
        industry_hc_tag_name = "$CalcIndustryHiddenCostFormula.final_rank_factor_type",
        suppress_hc_tag_name = "$CalcSuppressHiddenCostFormula.final_rank_factor_type",
        traffic_hc_tag_name = "$CalcSupportHiddenCostFormula.final_rank_factor_type",
        strategy_manager_list=[
          "CalcOtherHiddenCostFormula",
          "CalcSuppressHiddenCostFormula",
          "CalcSupportHiddenCostFormula",
          "CalcIndustryHiddenCostFormula"])
    ```
    """
    self._add_processor(AfterHcTransform(kwargs))
    return self

  def native_calc_coin(self, **kwargs):
    """
    NativeCalcCoin
    软广计算金币
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .native_calc_coin()
    ```
    """
    self._add_processor(NativeCalcCoin(kwargs))
    return self

  def native_calc_final_benefit(self, **kwargs):
    """
    NativeCalcFinalBenefit
    软广计算最终rank benefit
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .native_calc_final_benefit()
    ```
    """
    self._add_processor(NativeCalcFinalBenefit(kwargs))
    return self

  def native_calc_adload(self, **kwargs):
    """
    NativeCalcAdload
    软广计算 adload
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .native_calc_adload()
    ```
    """
    self._add_processor(NativeCalcAdload(kwargs))
    return self

  def formula_dot_mixer(self, **kwargs):
    """
    FormulaDotMixer
    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `dot_info`: [json] 必配项 打点详细信息

    调用示例
    ------
    ``` python
    .formula_dot_mixer(
      item_table = "ad_list_item_table",
      dot_info = [{
          "item_attr" : "NormalCpm", # 需打印字段
          "dot_name" : "Cpm",
          "describe": "[=OriginCpm*Ratio]",
          "formula" : "RankBenefit",  #所属公式
          "coefficient" : 1}])
    ```
    """
    self._add_processor(FormulaDotMixer(kwargs))
    return self

  def splash_outer_ranking_prepare(self, **kwargs):
    """
    SplashOuterRankingPrepare
    外循环开屏预处理
    ------
    参数配置
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    self.splash_outer_ranking_prepare()
    ```
    """
    self._add_processor(SplashOuterRankingPrepare(kwargs))
    return self

  def splash_inner_ranking_prepare(self, **kwargs):
    """
    SplashInnerRankingPrepare
    内循环开屏预处理
    ------
    参数配置
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    self.splash_inner_ranking_prepare()
    ```
    """
    self._add_processor(SplashInnerRankingPrepare(kwargs))
    return self
  
  def splash_boost_coef_prepare(self, **kwargs):
    """
    SplashBoostCoefPrepare
    splash_boost_coef 处理逻辑
    ------
    参数配置
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    self.splash_boost_coef_prepare()
    ```
    """
    self._add_processor(SplashBoostCoefPrepare(kwargs))
    return self

  def splash_merge_ad_list(self, **kwargs):
    """
    SplashMergeAdList
    开屏合并live队列到硬广队列
    ------
    参数配置
    无

    调用示例
    ------
    ``` python
    self.splash_merge_ad_list()
    ```
    """
    self._add_processor(SplashMergeAdList(kwargs))
    return self

  def splash_unify_cxr(self, **kwargs):
    """
    SplashUnifyCxr
    开屏统一 R 值
    ------
    参数配置
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    self.splash_unify_cxr()
    ```
    """
    self._add_processor(SplashUnifyCxr(kwargs))
    return self

  def splash_auction_node(self, **kwargs):
    """
    SplashAuctionNode
    开屏计算费用
    ------
    参数配置
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    self.splash_auction_node()
    ```
    """
    self._add_processor(SplashAuctionNode(kwargs))
    return self

  def universe_unify_cxr(self, **kwargs):
    """
    UniverseUnifyCxr
    联盟广告 R 值统一算子
    ------
    参数配置
    暂无参数

    调用示例
    ------
    ``` python
    self.universe_unify_cxr()
    ```
    """
    self._add_processor(UniverseUnifyCxr(kwargs))
    return self

  def universe_ranking_prepare(self, **kwargs):
    """
    UniverseRankingPrepare
    联盟排序预处理
    ------
    参数配置
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    self.universe_ranking_prepare()
    ```
    """
    self._add_processor(UniverseRankingPrepare(kwargs))
    return self

  def universe_ranking_prepare_async(self, **kwargs):
    """
    UniverseRankingPrepareAsync
    联盟排序预处理 异步版本
    ------
    参数配置
    原有node算子重构, 暂无参数

    调用示例
    ------
    ``` python
    self.universe_ranking_prepare_async()
    ```
    """
    self._add_processor(UniverseRankingPrepareAsync(kwargs))
    return self

  def universe_calculate_benefit(self, **kwargs):
    """
    UniverseCalculateBenefit
    联盟计算 rank benefit
    ------
    参数配置
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    self.universe_calculate_benefit()
    ```
    """
    self._add_processor(UniverseCalculateBenefit(kwargs))
    return self

  def universe_auction(self, **kwargs):
    """
    UniverseAuction
    联盟计算费用
    ------
    参数配置
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    self.universe_auction()
    ```
    """
    self._add_processor(UniverseAuction(kwargs))
    return self

  def post_client_mixer(self, **kwargs):
    """
    PostClientMixer
    rank 请求 Post & Pid
    参数配置
    ------
    `save_async_status_to`: [string] === "post_client_result",  InitParams 节点会等待

    调用示例
    ------
    ``` python
    .post_client_mixer(save_async_status_to = "post_client_result")
    ```
    """
    self._add_processor(PostClientMixer(kwargs))
    return self

  def feature_index_client_mixer(self, **kwargs):
    """
    FeatureIndexClientMixer
    策略正排请求客户端
    参数配置
    ------
    `save_async_status_to`: [string] === "feature_index_client_result",  InitParams 节点会等待

    调用示例
    ------
    ``` python
    .feature_index_client_mixer(save_async_status_to = "feature_index_client_result")
    ```
    """
    self._add_processor(FeatureIndexClientMixer(kwargs))
    return self

  def calc_cpm_formula(self, **kwargs):
    """
    CalcCpmFormula
    cpm 计算公式
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 公式输入字段

    `output_formula_type`: [string] 必配项 公式结果

    调用示例
    ------
    ``` python
    .calc_cpm_formula(
      item_table="ad_list_item_table",
      input_formula_type_list = [
        "ServerClientShowRate",
        "OriginCpm",
        "CpmBoost"
      ],
      output_formula_type = "Cpm"
    )
    ```
    """
    self._add_processor(CalcCpmFormula(kwargs))
    return self

  def multiplication_chain_formula(self, **kwargs):
    """
    MultiplicationChainFormula
    连乘公式
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 公式输入字段

    `output_formula_type`: [string] 必配项 公式结果

    调用示例
    ------
    ``` python
    # ServerClientShowRate = ServerClientShowRate * ratio1 * ratio2, 其中ratio x为$ormalServerClientShowRateFormula下面因子列表计算得出.
    .multiplication_chain_formula(
      item_table = "ad_list_item_table",
      input_formula_type_list=["NormalServerClientShowRateFormula"],
      output_formula_type = "ServerClientShowRate"
    )
    ```
    """
    self._add_processor(MultiplicationChainFormula(kwargs))
    return self

  def sum_factor_processor(self, **kwargs):
    """
    SumFactorProcessor
    因子求和算子
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 公式输入字段

    `output_formula_type`: [string] 必配项 公式结果

    调用示例
    ------
    ``` python
    .sum_factor_processor(
      item_table="ad_list_item_table",
      input_formula_type_list=["OtherRbSumScore"],
      output_formula_type = "OtherRbSumScore"
    )
    ```
    """
    self._add_processor(SumFactorProcessor(kwargs))
    return self

  def common_factor_processor(self, **kwargs):
    """
    CommonFactorProcessor
    因子求和算子
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 公式输入字段

    `output_formula_type`: [string] 必配项 公式结果

    `op_type`: [string] 必配项 运算类型，支持 "SUM", ”MULTI", "MAX", "MIN", "PRIORITY"

    调用示例
    ------
    ``` python
    .common_factor_processor(
      item_table="ad_list_item_table",
      input_formula_type_list=["OtherRbSumScore"],
      output_formula_type = "OtherRbSumScore",
      op_type = "SUM"
    )
    ```
    """
    self._add_processor(CommonFactorProcessor(kwargs))
    return self

  def common_formula_processor(self, **kwargs):
    """
    CommonFormulaProcessor

    通用公式计算算子

    用于公式计算，根据输入的子公式 & 操作类型，输出目标公式的值

    当前操作类型，支持 加和、连乘、最大值、最小值

    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `op_type`: [string] 必配项 运算类型，支持 "SUM", ”MULTI", "MAX", "MIN"

    `input_formula_type_list`: [list] 必配项 参与运算的子公式类型列表

    `output_formula_type`: [string] 必配项 输出的公式类型

    调用示例
    ------
    ``` python
    .common_formula_processor(
      item_table = "ad_list_item_table",
      op_type = "SUM",
      input_formula_type_list = ["OriginCpm", "CpmBoost"],
      output_formula_type = "Cpm"
      )
    """
    self._add_processor(CommonFormulaProcessor(kwargs))
    return self

  def sum_formula_processor(self, **kwargs):
    """
    SumFormulaProcessor

    公式求和算子

    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 参与运算的子公式类型列表

    `output_formula_type`: [string] 必配项 输出的公式类型

    调用示例
    ------
    ``` python
    .sum_formula_processor(
      item_table = "ad_list_item_table",
      input_formula_type_list = ["OriginCpm", "CpmBoost"],
      output_formula_type = "Cpm"
      )

    """
    self._add_processor(SumFormulaProcessor(kwargs))
    return self

  def after_cpm_transform(self, **kwargs):
    """
    AfterCpmTransform
    cpm 迁移后处理
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `cpm_name`: [string] 必配项 双写的字段

    调用示例
    ------
    ``` python
    .after_cpm_transform(
      item_table = "ad_list_item_table",
      cpm_name = "$Cpm.formula_compute"
    )
    ```
    """
    self._add_processor(AfterCpmTransform(kwargs))
    return self

  def before_cpm_boost_transform(self, **kwargs):
    """
    BeforeCpmBoostTransform
    cpm_boost 计算预处理
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .before_cpm_boost_transform(
      item_table = "ad_list_item_table"
    )
    ```
    """
    self._add_processor(BeforeCpmBoostTransform(kwargs))
    return self

  def before_origin_cpm_transform(self, **kwargs):
    """
    BeforeOriginCpmTransform
    origin_cpm 计算预处理
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .before_origin_cpm_transform(
      item_table = "ad_list_item_table"
    )
    ```
    """
    self._add_processor(BeforeOriginCpmTransform(kwargs))
    return self

  def before_server_client_show_rate_transform(self, **kwargs):
    """
    BeforeServerClientShowRateTransform
    server_client_show_rate 计算预处理
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .before_server_client_show_rate_transform(
      item_table = "ad_list_item_table"
    )
    ```
    """
    self._add_processor(BeforeServerClientShowRateTransform(kwargs))
    return self

  def before_rank_benefit_transform(self, **kwargs):
    """
    BeforeRankBenefitTransform
    rank_benefit 计算预处理
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .before_rank_benefit_transform(
      item_table = "ad_list_item_table"
    )
    ```
    """
    self._add_processor(BeforeRankBenefitTransform(kwargs))
    return self

  def search_before_rank_benefit_transform(self, **kwargs):
    """
    SearchBeforeCalRankBenefitTransform
    搜索 rank_benefit 计算预处理
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .search_before_rank_benefit_transform(
      item_table = "ad_list_item_table"
    )
    ```
    """
    self._add_processor(SearchBeforeCalRankBenefitTransform(kwargs))
    return self

  def search_fill_label_info(self, **kwargs):
    """
    SearchFillLabelInfo
    搜索样本数据采集
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .search_fill_label_info(
      item_table = "ad_list_item_table"
    )
    ```
    """
    self._add_processor(SearchFillLabelInfo(kwargs))
    return self

  def calc_rank_benefit_formula(self, **kwargs):
    """
    CalcRankBenefitFormula
    rank_benefit 计算公式

    ------
    参数配置
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 公式输入字段

    `output_formula_type`: [string] 必配项 公式结果
    ------

    调用示例
    ------
    ``` python
    .calc_rank_benefit_formula(
        item_table=item_table,
        input_formula_type_list = input_formula_type_list,
        output_formula_type = output_formula_type
      )

    """
    self._add_processor(CalcRankBenefitFormula(kwargs))
    return self

  def calc_rank_benefit_after(self, **kwargs):
    """
    CalculateBenefitAfterNormal

    RB 计算后处理算子，包括样本数据采集等

    ------
    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_rank_benefit_after()

    """
    self._add_processor(CalculateBenefitAfterNormal(kwargs))
    return self

  def common_switch_mock_mixer(self, **kwargs):
    '''
    CommonSwitchMockMixer
    ------
    通用开关mock算子

    参数配置
    ------

    调用示例
    ------
    ``` python
    .common_switch_mock_mixer()
    ```
    '''
    self._add_processor(CommonSwitchMockMixer(kwargs))
    return self

  def add_udf_factor(self, **kwargs):
    '''
    AddUdfFactor
    ------
    通用 因子类 算子

    参数配置
    -------
    `item_table`: [string] 必配项 item表名

    `udf_name`: [string] 必配项 udf name

    `formula_type`:  [enum] 必填项 因子所属公式的枚举

    `rank_factor_type`:  [enum] 必填项 因子所属公式的枚举

    调用示例
    -------
    ``` python
    .add_udf_factor(
      udf_name = "WanheProfileEcpcStrategy",
      item_table = item_table,
      formula_type = get_enum(Formula.NormalEcpcFormula),
      rank_factor_type = get_enum(EcpcAdjustTag.WanheProfileEcpc))
    ```
    '''
    self._add_processor(AddUdfFactor(kwargs))
    return self

  def ad_common_factor(self, **kwargs):
    """
    AdCommonFactor
    广告基本通用因子计算算子
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 必配项 公式输入字段

    `output_formula_type`: [string] 必配项 公式结果

    `op_type`: [string] 必配项 运算类型，支持 "EcpcProduct", "PRIORITY"

    调用示例
    ------
    ``` python
    .ad_common_factor(
      item_table="ad_list_item_table",
      input_formula_type_list=["EcpcProduct"],
      output_formula_type = "EcpcProduct",
      op_type = "Product"
    )
    ```
    """
    self._add_processor(AdCommonFactor(kwargs))
    return self

  def add_udf_factor_list(self, **kwargs):
    """
    AddUdfFactorList
    ------
    通用因子类注册算子 支持配置多个udf

    参数配置
    -------
    `item_table`: [string] 必配项 item表名

    `formula_type`:  [enum] 必填项 因子所属公式的枚举

    `udf_facotor_info`: [json] 必配项 udf 因子信息 由udf_name[stirng],rank_factor_type[int],owner[string]组成

    调用示例
    -------
    ``` python
    .add_udf_factor_list(
      name = "EcpcProductRegister",
      item_table = item_table,
      formula_type = get_enum(Formula.NormalEcpcProductFormula),
      udf_factor_info = [
        {"udf_name":"HotLiveBoost","rank_factor_type":get_enum(EcpcAdjustTag.HotLiveBoost,"owner":"moqi"},
        {"udf_name":"AEcpcStrategy","rank_factor_type":get_enum(EcpcAdjustTag.AEcpcStrategy,"owner":"sunkang"}]
      )
    ```
    """
    self._add_processor(AddUdfFactorList(kwargs))
    return self

  def normal_sort(self, **kwargs):
    """
    NormalSort
    硬广排序
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .normal_sort()
    ```
    """
    self._add_processor(NormalSort(kwargs))
    return self

  def normal_filter_post(self, **kwargs):
    """
    NormalFilterPost
    硬广过滤后处理
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .normal_filter_post()
    ```
    """
    self._add_processor(NormalFilterPost(kwargs))
    return self

  def normal_prepare_threshold(self, **kwargs):
    """
    NormalPrepareThreshold
    硬广设置门槛
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .normal_prepare_threshold()
    ```
    """
    self._add_processor(NormalPrepareThreshold(kwargs))
    return self

  def native_sort(self, **kwargs):
    """
    NativeSort
    软广排序算子
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .native_sort()
    ```
    """
    self._add_processor(NativeSort(kwargs))
    return self

  def native_sample(self, **kwargs):
    """
    NativeSample
    软广分队列采样算子
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .native_sample()
    ```
    """
    self._add_processor(NativeSample(kwargs))
    return self

  def native_filter_post(self, **kwargs):
    """
    NativeFilterPost
    软广过滤后处理，包含队列合并和统一采样
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .native_filter_post()
    ```
    """
    self._add_processor(NativeFilterPost(kwargs))
    return self

  def run_udf_filter(self, **kwargs):
    """
    RunUdfFilter
    过滤策略执行算子
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `filter_group`: [enum] 必配项 过滤规则归属的 group (cpm门槛, bid门槛, 多样性等）

    `filter_rule`: [enum] 必配项 过滤规则type， 与 UDF 函数名对应

    `filter_reason`: [enum] 必配项 过滤原因

    调用示例
    ------
    ``` python
    .run_udf_filter(
        item_table = item_table,
        filter_group = "CpmThresholdGroup",
        filter_rule = "EcpmFollowPageFilter",
        filter_reason = "NATIVE_UNIFY_CPM_THR_FILTER"
      )
    ```
    """
    self._add_processor(RunUdfFilter(kwargs))
    return self

  def ad_quota_mark_mixer(self, **kwargs):
    """
    AdQuotaMarkMixer
    quota 打标算子
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .ad_quota_mark_mixer(item_table="native_ad_list_item_table")
    ```
    """
    self._add_processor(AdQuotaMarkMixer(kwargs))
    return self

  def native_filter_prepare(self, **kwargs):
    """
    NativeFilterPrepare
    软广过滤预处理算子
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .native_filter_prepare(item_table="native_ad_list_item_table")
    ```
    """
    self._add_processor(NativeFilterPrepare(kwargs))
    return self

  def normal_filter_prepare(self, **kwargs):
    """
    NormalFilterPrepare
    软广过滤预处理算子
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    调用示例
    ------
    ``` python
    .normal_filter_prepare(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(NormalFilterPrepare(kwargs))
    return self

  def outlier_watcher(self, **kwargs):
    """
    OutlierWatcher
    多因子框架异常值监控算子
    参数配置
    ------
    `item_table`: [string] 必配项 item表名
    `watch_info`: [json] 必配项 监控信息
    调用示例
    ------
    ``` python
    .outlier_watcher(
        item_table="ad_list_item_table"
        watch_info = [{
          "item_attr" : "NormalCpm", # 需打印字段
          "dot_name" : "Cpm"}]
    )
    ```
    """
    self._add_processor(OutlierWatcher(kwargs))
    return self

  def merchant_live_minus_context_and_item_mocker(self, **kwargs):
    """
    MerchantLiveMinusContextAndItemMocker
    merchant live minus ecpc context/item mocker-ut 算子
    参数配置
    ------
    调用示例
    ------
    ``` python
    .merchant_live_minus_context_and_item_mocker()
    ```
    """
    self._add_processor(MerchantLiveMinusContextAndItemMocker(kwargs))
    return self


  def bonus_cpm_for_inner_native_photo_context_and_item_mocker(self, **kwargs):
    """
    BonusCpmForInnerNativePhotoContextAndItemMocker
    inner_native_photo_bonus context/item mocker-ut 算子
    参数配置
    ------
    调用示例
    ------
    ``` python
    .inner_native_photo_bonus_context_and_item_mocker()
    ```
    """
    self._add_processor(BonusCpmForInnerNativePhotoContextAndItemMocker(kwargs))
    return self

  def abtest_mocker(self, **kwargs):
    """
    AbtestMockerBase
    abtest mocker-ut 算子
    参数配置
    ------
    调用示例
    ------
    ``` python
    .abtest_mocker(abtest_mock_kv = "{}")
    ```
    """
    self._add_processor(AbtestMockerBase(kwargs))
    return self

  def demo_p2p_mocker(self, **kwargs):
    """
    DemoP2pMocker
    demo p2p mocker-ut 算子
    参数配置
    ------
    调用示例
    ------
    ``` python
    .demo_p2p_mocker()
    ```
    """
    self._add_processor(DemoP2pMocker(kwargs))
    return self
  
  def attr_from_json_enricher(self, **kwargs):
    """
    AttrFromJsonEnricher
    参数配置
    ------
    调用示例
    ------
    ``` python
    .item_attr_from_json_enricher()
    ```
    """
    self._add_processor(AttrFromJsonEnricher(kwargs))
    return self

  def common_init(self, **kwargs):
    """
    CommonInit
    公共的 CommonAttr 初始化算子
    调用示例
    ------
    ``` python
    .common_init()
    ```
    """
    self._add_processor(CommonInit(kwargs))
    return self

  def fill_bid(self, **kwargs):
    """
    FillBid
    出价信息填充算子
    调用示例
    ------
    ``` python
    .fill_bid()
    ```
    """
    self._add_processor(FillBid(kwargs))
    return self

  def force_reco_after_predict(self, **kwargs):
    """
    ForceRecoAfterPredict
    模型预估后强出算子
    依赖模型预估值的强出策略使用
    调用示例
    ------
    ``` python
    .force_reco_after_predict()
    ```
    """
    self._add_processor(ForceRecoAfterPredict(kwargs))
    return self

  def calc_normal_benefit_prepare(self, **kwargs):
      """
      CalcNormalBenefitPrepare
      CPM 回调点
      调用示例
      ------
      ``` python
      .calc_normal_benefit_prepare()
      ```
      """
      self._add_processor(CalcNormalBenefitPrepare(kwargs))
      return self

  def normal_calc_ecpm_adx(self, **kwargs):
      """
      NormalCalcEcpmAdx
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_adx()
      ```
      """
      self._add_processor(NormalCalcEcpmAdx(kwargs))
      return self

  def normal_calc_ecpm_cpm_live(self, **kwargs):
      """
      NormalCalcEcpmCpmLive
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_cpm_live()
      ```
      """
      self._add_processor(NormalCalcEcpmCpmLive(kwargs))
      return self

  def normal_calc_ecpm_cpc_live(self, **kwargs):
      """
      NormalCalcEcpmCpcLive
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_cpc_live()
      ```
      """
      self._add_processor(NormalCalcEcpmCpcLive(kwargs))
      return self

  def normal_calc_ecpm_cpc2_live(self, **kwargs):
      """
      NormalCalcEcpmCpc2Live
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_cpc2_live()
      ```
      """
      self._add_processor(NormalCalcEcpmCpc2Live(kwargs))
      return self

  def normal_calc_ecpm_cpa_live(self, **kwargs):
      """
      NormalCalcEcpmCpaLive
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_cpa_live()
      ```
      """
      self._add_processor(NormalCalcEcpmCpaLive(kwargs))
      return self

  def normal_calc_ecpm_ocpm_live(self, **kwargs):
      """
      NormalCalcEcpmOcpmLive
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_ocpm_live()
      ```
      """
      self._add_processor(NormalCalcEcpmOcpmLive(kwargs))
      return self

  def normal_calc_ecpm_ocpc_live(self, **kwargs):
      """
      NormalCalcEcpmOcpcLive
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_ocpc_live()
      ```
      """
      self._add_processor(NormalCalcEcpmOcpcLive(kwargs))
      return self

  def normal_calc_ecpm_cpm_photo(self, **kwargs):
      """
      NormalCalcEcpmCpmPhoto
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_cpm_photo()
      ```
      """
      self._add_processor(NormalCalcEcpmCpmPhoto(kwargs))
      return self

  def normal_calc_ecpm_cpc_photo(self, **kwargs):
      """
      NormalCalcEcpmCpcPhoto
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_cpc_photo()
      ```
      """
      self._add_processor(NormalCalcEcpmCpcPhoto(kwargs))
      return self

  def normal_calc_ecpm_cpa_photo(self, **kwargs):
      """
      NormalCalcEcpmCpaPhoto
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_cpa_photo()
      ```
      """
      self._add_processor(NormalCalcEcpmCpaPhoto(kwargs))
      return self

  def normal_calc_ecpm_ocpm_photo(self, **kwargs):
      """
      NormalCalcEcpmOcpmPhoto
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_ocpm_photo()
      ```
      """
      self._add_processor(NormalCalcEcpmOcpmPhoto(kwargs))
      return self

  def normal_calc_ecpm_mcb(self, **kwargs):
      """
      NormalCalcEcpmMcb
      CPM 回调点
      调用示例
      ------
      ``` python
      .normal_calc_ecpm_mcb()
      ```
      """
      self._add_processor(NormalCalcEcpmMcb(kwargs))
      return self

  def common_context_feature_builder(self, **kwargs):
    self._add_processor(CommonContextFeatureBuilder(kwargs))
    return self

  def inner_native_item_fill_mixer(self, **kwargs):
    self._add_processor(InnerNativeItemFillMixer(kwargs))
    return self

  def inner_normal_item_fill_mixer(self, **kwargs):
    self._add_processor(InnerNormalItemFillMixer(kwargs))
    return self

  def outer_item_fill_mixer(self, **kwargs):
    self._add_processor(OuterItemFillMixer(kwargs))
    return self
  
  def splash_inner_item_fill_mixer(self, **kwargs):
    self._add_processor(SplashInnerItemFillMixer(kwargs))
    return self
  
  def splash_outer_item_fill_mixer(self, **kwargs):
    self._add_processor(SplashOuterItemFillMixer(kwargs))
    return self

  def outer_context_feature_builder(self, **kwargs):
    self._add_processor(OuterContextFeatureBuilder(kwargs))
    return self

  def inner_normal_context_feature_builder(self, **kwargs):
    self._add_processor(InnerNormalContextFeatureBuilder(kwargs))
    return self

  def inner_native_context_feature_builder(self, **kwargs):
    self._add_processor(InnerNativeContextFeatureBuilder(kwargs))
    return self

  def user_feature_build_mixer(self, **kwargs):
    self._add_processor(UserFeatureBuildMixer(kwargs))
    return self

  def item_feature_build_mixer(self, **kwargs):
    self._add_processor(ItemFeatureBuildMixer(kwargs))
    return self

  def model_register(self, **kwargs):
    self._add_processor(ModelRegister(kwargs))
    return self

  def predict_post_proc(self, **kwargs):
    self._add_processor(PredictPostProc(kwargs))
    return self

  def model_predict(self, **kwargs):
    self._add_processor(ModelPredict(kwargs))
    return self

  def search_model_predict_async(self, **kwargs):
    self._add_processor(SearchModelPredictAsync(kwargs))
    return self

  def ad_router_response_proc(self, **kwargs):
    self._add_processor(AdRouterResponseProc(kwargs))
    return self
  
  def ad_router_request_prepare(self, **kwargs):
    self._add_processor(AdRouterRequestPrepare(kwargs))
    return self

  def predict_prepare(self, **kwargs):
    self._add_processor(PredictPrepare(kwargs))
    return self

  def second_stage_predict(self, **kwargs):
    self._add_processor(SecondStagePredict(kwargs))
    return self

  def second_stage_predict_wait(self, **kwargs):
    self._add_processor(SecondStagePredictWait(kwargs))
    return self

  def collect_sample_nearline(self, **kwargs):
    self._add_processor(CollectSampleNearline(kwargs))
    return self

  def inner_native_cmd_manager_mixer(self, **kwargs):
    self._add_processor(InnerNativeCmdManagerMixer(kwargs))
    return self

  def inner_normal_cmd_manager_mixer(self, **kwargs):
    self._add_processor(InnerNormalCmdManagerMixer(kwargs))
    return self

  def outer_cmd_manager_mixer(self, **kwargs):
    self._add_processor(OuterCmdManagerMixer(kwargs))
    return self
  
  def splash_inner_cmd_manager_mixer(self, **kwargs):
    self._add_processor(SplashInnerCmdManagerMixer(kwargs))
    return self
  
  def splash_outer_cmd_manager_mixer(self, **kwargs):
    self._add_processor(SplashOuterCmdManagerMixer(kwargs))
    return self

  def predict_value_watcher(self, **kwargs):
    """
      PredictValueWatcher
      模型预估值异常监控
      调用示例
      ------
      ``` python
      .predict_value_watcher(
        item_table_name_ = 'outer_predict_item_table'
      )
      ```
    """
    self._add_processor(PredictValueWatcher(kwargs))
    return self

  def native_unify_calc_pec_coupon(self, **kwargs):
      """
      NativeUnifyCalcPecCoupon
      Native CPM 插件
      调用示例
      ------
      ``` python
      .native_unify_calc_pec_coupon()
      ```
      """
      self._add_processor(NativeUnifyCalcPecCoupon(kwargs))
      return self

  def native_unify_calc_ecpm_ratio(self, **kwargs):
      """
      NativeUnifyCalcEcpmRatio
      Native CPM 插件
      调用示例
      ------
      ``` python
      .native_unify_calc_ecpm_ratio()
      ```
      """
      self._add_processor(NativeUnifyCalcEcpmRatio(kwargs))
      return self

  def native_calc_ecpm_extra_bid_type(self, **kwargs):
      """
      NativeUnifyCalcEcpm
      Native CPM 插件
      调用示例
      ------
      ``` python
      .native_calc_ecpm_extra_bid_type()
      ```
      """
      self._add_processor(NativeUnifyCalcEcpm(kwargs))
      return self

  def force_reco_mark_mixer(self, **kwargs):
      """
      ForceRecoMarkMixer
      统一强出打标
      调用示例
      ------
      ``` python
      .force_reco_mark_mixer()
      ```
      """
      self._add_processor(ForceRecoMarkMixer(kwargs))
      return self

  def unify_adload_control(self, **kwargs):
      """
      UnifyAdloadControl
      Native CPM 插件
      调用示例
      ------
      ``` python
      .unify_adload_control()
      ```
      """
      self._add_processor(UnifyAdloadControl(kwargs))
      return self

  def adload_allocate_optim(self, **kwargs):
      """
      AdloadAllocateOptim
      Native CPM 插件
      调用示例
      ------
      ``` python
      .adload_allocate_optim()
      ```
      """
      self._add_processor(AdloadAllocateOptim(kwargs))
      return self

  def switch_tube_to_inner_loop(self, **kwargs):
    """
    SwitchTubeToInnerLoop
    短剧切内循环实验
    参数配置
    ------
    `enable`: [bool] 必配项 为 true 切到内循环，为 false 切回外循环
    `stage`: [str] 必配项 阶段，用于标记打点

    调用示例
    ------
    ``` python
    .switch_tube_to_inner_loop(enable=True)
    ```
    """
    self._add_processor(SwitchTubeToInnerLoop(kwargs))
    return self

  def modify_item_attr(self, **kwargs):
    """
    ModifyItemAttr
    广告item字段修改
    参数配置
    ------
    `item_table_name`: [str] 必配项
    调用示例
    ------
    ``` python
    .modify_item_attr(
      item_table_name = "global_ad_table"
    )
    ```
    """
    self._add_processor(ModifyItemAttr(kwargs))
    return self

  def set_item_attr_by_index(self, **kwargs):
    """
    SetItemAttrByIndex
    广告item字段修改 by 正排
    参数配置
    ------
    `item_table_name`: [str] 必配项
    调用示例
    ------
    ``` python
    .set_item_attr_by_index(
      item_table_name = "global_ad_table"
    )
    ```
    """
    self._add_processor(SetItemAttrByIndex(kwargs))
    return self

  def parse_item_attr(self, **kwargs):
    """
    ParseItemAttr
    广告item字段解析
    参数配置
    ------
    `item_table_name`: [str] 必配项
    调用示例
    ------
    ``` python
    .parse_item_attr(
      item_table_name = "global_ad_table"
    )
    ```
    """
    self._add_processor(ParseItemAttr(kwargs))
    return self

  def aggregate_bidding(self, **kwargs):
    """
    AggregateBidding
    聚合竞价处理算子
    参数配置
    ------
    `item_table`: [string] 广告队列

    调用示例
    ------
    ``` python
    .aggregateB_bidding(item_table="ad_list_table")
    ```
    """
    self._add_processor(AggregateBidding(kwargs))
    return self

  def calc_cpm_for_qpon(self, **kwargs):
    """
    CalcCpmForQpon
    智能优惠券 cpm 计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_cpm_for_qpon(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcCpmForQpon(kwargs))
    return self

  def calc_cpm_for_bcee(self, **kwargs):
    """
    CalcCpmForBcee
    破圈探索 cpm 计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_cpm_for_bcee(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcCpmForBcee(kwargs))
    return self

  def calc_calibrated_cpm_for_prerank(self, **kwargs):
    """
    CalcCalibratedCpmForPrerank
    外循环粗排排序目标校准, 不改动 cpm, 不影响排序和计费
    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_calibrated_cpm_for_prerank(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcCalibratedCpmForPrerank(kwargs))
    return self
  
  def calc_calibration_cpm(self, **kwargs):
    """
    CalcCalibrationCpm
    流量粗粒度下 cpm 校准
    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_calibration_cpm(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcCalibrationCpm(kwargs))
    return self

  def calibrate_price_for_qpon(self, **kwargs):
    """
    CalibratePriceForQpon
    智能优惠券计费修正
    参数配置
    ------

    调用示例
    ------
    ``` python
    .calibrate_price_for_qpon(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalibratePriceForQpon(kwargs))
    return self

  def calc_client_ai_rerank_score(self, **kwargs):
    """
    CalcClientAiRerankScore
    端智能重请求分数计算
    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_client_ai_rerank_score(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcClientAiRerankScore(kwargs))
    return self

  def unify_ecpm_bound_mixer(self, **kwargs):
    """
    UnifyEcpmBoundMixer

    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_ecpm_bound_mixer(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(UnifyEcpmBoundMixer(kwargs))
    return self

  def unify_calc_ecpm(self, **kwargs):
    """
    UnifyCalcEcpm

    参数配置
    ------

    调用示例
    ------
    ``` python
    .unify_calc_ecpm(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(UnifyCalcEcpm(kwargs))
    return self

  def normal_reset_unify_sctr_mixer(self, **kwargs):
    """
    NormalResetUnifySctrMixer

    为统一 sctr 策略框架，硬广下 unify_sctr 统一置为 1（实际上 sctr 已乘入 ctr）

    参数配置
    ------

    调用示例
    ------
    ``` python
    .normal_reset_unify_sctr_mixer(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(NormalResetUnifySctrMixer(kwargs))
    return self

  def multiply_sctr_into_ctr_mixer(self, **kwargs):
    """
    MultiplySctrIntoCtrMixer

    软硬广对齐，统一将 unify_sctr 乘入 unify_ctr

    参数配置
    ------

    调用示例
    ------
    ``` python
    .multiply_sctr_into_ctr_mixer(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(MultiplySctrIntoCtrMixer(kwargs))
    return self

  def calc_server_client_show_rate(self, **kwargs):
    """
    CalcServerClientShowRate

    曝光系数策略框架

    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_server_client_show_rate(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcServerClientShowRate(kwargs))
    return self

  def calc_has_deep_cvr(self, **kwargs):
    """
    CalcHasDeepCvr

    deep_cvr 准入标策略框架，最终准入为所有 udf 取或

    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_has_deep_cvr(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcHasDeepCvr(kwargs))
    return self

  def calc_ecpm_adjust(self, **kwargs):
    """
    CalcEcpmAdjust

    ecpm_adjust 准入标策略框架，最终准入为所有 udf 累加

    参数配置
    ------

    调用示例
    ------
    ``` python
    .calc_ecpm_adjust(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcEcpmAdjust(kwargs))
    return self

  def calc_ecpm_upper_bound(self, **kwargs):
    """
    CalcEcpmUpperBound

    ecpm_upper_bound 准入标策略框架，最终准入为所有 udf 取 min

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ecpm_upper_bound(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcEcpmUpperBound(kwargs))
    return self

  def calc_ecpm_lower_bound(self, **kwargs):
    """
    CalcEcpmLowerBound

    ecpm_lower_bound 准入标策略框架，最终准入为所有 udf 取 max

    参数配置
    ------

    调用示例
    ------
    ``` python
    .ecpm_lower_bound(item_table="ad_list_item_table")
    ```
    """
    self._add_processor(CalcEcpmLowerBound(kwargs))
    return self

  def qcpx_sky_fall_coupon_mixer(self, **kwargs):
    """
    QcpxSkyFallCouponMixer
    rank 请求 SkyFallCouponPreCheck 服务
    参数配置
    ------
    `rpc_kess_name`: [string], SkyFallCouponPreCheck 服务 kess_name
    `save_async_status_to`: [string], 选参, 使用后算子自动转为异步算子

    调用示例
    ------
    ``` python
    .qcpx_sky_fall_coupon_mixer(
      save_async_status_to = "qcpx_sky_fall_coupon_rpc",
      rpc_kess_name = "sky-fall-coupon-pre-check"
    )
    ```
    """
    self._add_processor(QcpxSkyFallCouponMixer(kwargs))
    return self
