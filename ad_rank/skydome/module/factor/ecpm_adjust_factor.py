#!/usr/bin/env python3
# coding=utf-8

from ad_rank_dragonfly.ad_api_mixin import *
from dragonfly.modular.module import module
from dragonfly.modular.data_manager import data_manager, ab_param as ab, kconf_param as kconf
from module.factor.common_factor import CommonFactor
from module.enum.formula_type_enum import get_enum
from module.enum.factor_type_enum import EcpmAdjust

class EcpmAdjustFactor(CommonFactor):
  def calc_ecpm_adjust_module(self, item_table: str):
    (self
    .calc_ecpm_adjust(
      item_table=item_table,
      udf_list=[
        {"name": "EcpmDeepTwinBid", "factor_tag": get_enum(EcpmAdjust.EcpmDeepTwinBid), "owner": "zhangmengxin"}, # 迁移自 NativeUnifyCalcEcpmPlugin::CalcEcpmDeepTwinBid
        # ⬆️ ecpm_adjust 策略在此注册
        # 最终准入为所有 udf 累加
      ]
    )
    )

    return self
