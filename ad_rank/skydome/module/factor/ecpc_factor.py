#!/usr/bin/env python3
# coding=utf-8

from ad_rank_dragonfly.ad_api_mixin import *
from dragonfly.modular.module import module
from dragonfly.modular.data_manager import data_manager, ab_param as ab, kconf_param as kconf
from module.factor.common_factor import CommonFactor
from module.enum.formula_type_enum import Formula, get_enum
from module.enum.factor_type_enum import EcpcAdjustTag

class EcpcFactor(CommonFactor):
  # ecpc策略注册 24策略重构
  # NOTINE：新增 ecpc 必须加到 common_ecpc_factor
  # NOTINE: 重构期间 也需要同步到 ecpc_max_min_common_factor
  # 策略会同时对软硬广生效，若预期仅对部分物料生效，请在 admit 函数中添加对应业务逻辑
  def common_ecpc_factor(self, item_table, formula_type):
    self.add_udf_factor_list(
      name = "EcpcCommonRegister",
      item_table = item_table,
      formula_type = formula_type,
      udf_factor_info = [
        {"udf_name":"EspPhotoEffectivePlayEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.EspPhotoEffectivePlay), "owner" : "wangyuan11"},
        {"udf_name":"WanheMatchingEfficiencyEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.WanheMatchingEfficiencyEcpc), "owner" : "zhaokun03"},
        {"udf_name":"WanheActionTypeCostRatioEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.WanheActionTypeCostRatioEcpc), "owner" : "zhaokun03"},
        {"udf_name":"ExploreActionTypeCostRatioEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ExploreActionTypeCostRatioEcpc), "owner" : "yesiqi"},
        {"udf_name":"DeepRewardedCoinEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.DeepRewardedCoinEcpc), "owner" : "xuxu"},
        {"udf_name":"InnerTriggerItemEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerTriggerItemEcpc), "owner" : "jinhui05"},
        {"udf_name":"GamePlayerOnlyFirstChargeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GamePlayerOnlyFirstChargeEcpc), "owner" : "zhaoqilong"},
        {"udf_name":"OuterIndustryBoostEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterIndustryBoostEcpc), "owner" : "zhaoqilong"},
        {"udf_name":"OuterGameExploreEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.OuterGameExploreEcpc), "owner" : "yuhanzhang"},
        {"udf_name":"InspireQualityAudienceSupress" , "rank_factor_type" : get_enum(EcpcAdjustTag.InspireQualityAudienceSuppress), "owner" : "lixu05"},
        {"udf_name":"PlayletRetargetEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.PlayletRetargetEcpc), "owner" : "yangxuan06"},
        {"udf_name":"OuterLiveOrientationToolEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLiveOrientationToolEcpc), "owner" : "zhangmengxin"},
        {"udf_name":"EduLpsDeepEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.EduLpsDeepEcpc), "owner" : "linyuhao03"},
        {"udf_name":"EduLpsDeepAllClassEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.EduLpsDeepAllClassEcpc), "owner" : "linyuhao03"},
        {"udf_name":"FinCreditRoiEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FinCreditRoiEcpc), "owner" : "liuxinyi08"},
        {"udf_name":"InnerAccountSuppressEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerAccountSuppressEcpc), "owner" : "nizhihao"},
        {"udf_name":"RewardGameEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.RewardGameEcpc), "owner" : "jiangnan07"},
        {"udf_name":"IncentiveEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.IncentiveEcpc), "owner" : "gaozepeng"},
        {"udf_name":"GameRetargetEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameRetargetEcpc), "owner" : "rongyu03"},
        {"udf_name":"WanheProfileEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.WanheProfileEcpc), "owner" : "zhaokun03"},
        {"udf_name":"SideWindowEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.SideWindowEcpc), "owner" : "zhaokun03"},
        {"udf_name":"WeekstayRetentionEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.WeekRetention), "owner" : "niehui"},
        {"udf_name":"CrowdEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.StrategyCrowdEcpc), "owner" : "lixu05"},
        {"udf_name":"ConvQualityEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.ConvQuality), "owner" : "dinghe03"},
        {"udf_name": "ConvQualityEcpcStrategyNew", "rank_factor_type": get_enum(EcpcAdjustTag.ConvQualityNew), "owner": "zhangrui30"},
        {"udf_name":"KnewsColdStartStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.KnewsColdStart), "owner" : "yangyanxi"},
        {"udf_name":"EcomEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.EcomEcpc), "owner" : "songxu"},
        {"udf_name":"LALpsValidCluesEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.LALpsValidClues), "owner" : "pengshiyuan"},
        {"udf_name":"MerchantEcpmStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.AdMerchantEcpc), "owner" : "wangtao21"},
        {"udf_name":"LpsdeepEcpmStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.LpsDeepEcpc), "owner" : "xuyanyan03"},
        {"udf_name":"PrivateMessageEcpmStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.PrivateMessageEcpc), "owner" : "zhaijianwei"},
        {"udf_name":"OuterOverissueEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterOverissueEcpc), "owner" : "yangfukang03"},
        {"udf_name":"OuterDncNcModelEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterDncNcModelEcpc), "owner" : "yangfukang03"},
        {"udf_name":"UserVauleGroupEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.UserVauleGroupEcpc), "owner" : "dingyiming05"},
        {"udf_name":"InnovationTrafficEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnovationTrafficEcpc), "owner" : "zhaokun03"},
        {"udf_name":"OuterLiveGameConvEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLiveGameConvEcpc), "owner" : "zhangmengxin"},
        {"udf_name":"ConvNextstayEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ConvNextstayEcpc), "owner" : "yishijie"},
        {"udf_name":"ClueFwhIntentEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ClueFwhIntentEcpc), "owner" : "xutaotao03"},
        {"udf_name":"LpsAcqGenEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.LpsAcqGenEcpc), "owner" : "xutaotao03"},
        {"udf_name":"IaaAcqGenEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IaaAcqGenEcpc), "owner" : "liangyukang"},
        {"udf_name":"WeGameReturnTimeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.WeGameReturnTimeEcpc), "owner" : "jiangpeng07"},
        {"udf_name":"OuterloopNcMaxEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterloopNcMaxEcpc), "owner" : "jiayalong"},
        {"udf_name":"KGameEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.KGameEcpc), "owner" : "jiangpeng07"},
        {"udf_name":"CidQcpxCpmEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.CidQcpxCpmEcpc), "owner" : "zhangyiwei03"},
        {"udf_name":"OuterLiveVtrEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLiveVtrEcpc), "owner" : "nizhihao"},
        {"udf_name":"GameRtaSignEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameRtaSignEcpc), "owner" : "nizhihao"},
        {"udf_name":"PaySkitLtvRectifyEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.PaySkitLtvRectifyEcpc), "owner" : "jiyang"},
        {"udf_name":"GameShouFaEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameShouFaEcpc), "owner" : "nizhihao"},
        {"udf_name":"KeyActionEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.KeyActionEcpc), "owner" : "zhouqifei"},\
        {"udf_name":"EspLiveCalibration" , "rank_factor_type" : get_enum(EcpcAdjustTag.EspLiveCalibration), "owner" : "wangyuan"},
        {"udf_name":"EcpcWithAuthorStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.EcpcWithAuthorStrategy), "owner" : "wanghongzi"},
        {"udf_name":"UserExporeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.UserExporeEcpc), "owner" : "yuchengyuan"},
        {"udf_name":"IndustryModelEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IndustryModelEcpc), "owner" : "yuchengyuan"},
        {"udf_name":"UpgradedIndustryModelEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.UpgradedIndustryModelEcpc), "owner" : "yishijie"},
        #{"udf_name":"RevenueOptimiseEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.RevenueOptimiseEcpc), "owner" : "wangtao15"},
        {"udf_name":"MicroappDiscountEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.MicroappDiscountEcpc), "owner" : "xuxu"},
        {"udf_name":"KnewsColdStartEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.KnewsColdStartEcpc), "owner" : "wangtao15"},
        #{"udf_name":"StatsAutoParam" , "rank_factor_type" : get_enum(EcpcAdjustTag.StatsAutoParam), "owner" : "liubing"},
        #{"udf_name":"SplashBoostCoefEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.SplashBoostCoefEcpc), "owner" : "liubing05"},
        #{"udf_name":"SplashEyemaxDiscountEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.SplashEyemaxDiscountEcpc), "owner" : "liubing05"},
        {"udf_name":"T7ROIZhongCaoEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.T7ROIZhongCaoEcpc), "owner" : "fukunyang"},
        {"udf_name":"OuterNativeAppAdvanceDiscount" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterNativeAppAdvanceDiscount), "owner" : "zhangmengxin"},
        {"udf_name":"FollowPageBoost" , "rank_factor_type" : get_enum(EcpcAdjustTag.FollowPageBoost), "owner" : "wangyang10"},
        {"udf_name":"OuterDspCaliByConfig" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterDspCaliByConfig), "owner" : "zhangmengxin"},
        {"udf_name":"FanstopUATagEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FanstopUATagEcpc), "owner" : "tengwei"},
        {"udf_name":"FanstopPddUserPackageRedirectEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FanstopPddUserPackageRedirectEcpc), "owner" : "tengwei"},
        {"udf_name":"FanstopGameUserPackageEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FanstopGameUserPackageEcpc), "owner" : "lichunchi"},
        {"udf_name":"InnerLSPEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerLSPEcpc), "owner" : "tengwei"},
        {"udf_name":"OuterMedicalExploreEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.OuterMedicalExploreEcpc), "owner": "tengwei"},
        {"udf_name":"InnerProductEEEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerProductEEEcpc), "owner" : "guochangyu"},
        {"udf_name":"InnerAdxEEEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerAdxEEEcpc), "owner" : "cuihongyi"},
        {"udf_name":"InnerAdxEEOrientationEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerAdxEEOrientationEcpc), "owner" : "cuihongyi"},
        {"udf_name":"InnerStorewideOrientationEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerStorewideOrientationEcpc), "owner" : "wangqi12"},
        {"udf_name":"InnerLSPAggrPageEcpc", "rank_factor_type": get_enum(EcpcAdjustTag.InnerLSPAggrPageEcpc), "owner": "guochangyu"},
        {"udf_name":"InnerLspUserBehaviorEcpc", "rank_factor_type": get_enum(EcpcAdjustTag.InnerLspUserBehaviorEcpc), "owner": "guochangyu"},
        {"udf_name":"LocalLifeUserExploreEcpc", "rank_factor_type": get_enum(EcpcAdjustTag.LocalLifeUserExploreEcpc), "owner": "guochangyu"},
        {"udf_name":"LocalLifeConsumPowerEcpc", "rank_factor_type": get_enum(EcpcAdjustTag.LocalLifeConsumPowerEcpc), "owner": "guochangyu"},
        {"udf_name":"FictionRetargetEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterFictionRetargetEcpc), "owner" : "jiangjinling"},
        {"udf_name":"OuterSelfServiceToolEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterSelfServiceToolEcpc), "owner" : "jiayalong"},
        {"udf_name":"PoiDistanceAdjustStrategy", "rank_factor_type" : get_enum(EcpcAdjustTag.PoiDistanceEcpc), "owner" : "lianghaoqiang"},
        {"udf_name":"InnerLSPUserBLevelEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerLSPUserBLevelEcpc), "owner" : "tengwei"},
        {"udf_name":"IAADJRaiseEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IAADJRaiseEcpc), "owner" : "liyichen05"},
        {"udf_name":"StorewideUpliftEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.StorewideUpliftEcpc), "owner" : "tangxiaochao05"},
        {"udf_name":"StorewideMerchantUpliftEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.StorewideMerchantUpliftEcpc), "owner" : "lizuxin"},
        {"udf_name":"IapCorePopulationProtectEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IapCorePopulationProtectEcpc), "owner" : "chenxian"},
        {"udf_name":"FictionSubsidyEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionSubsidyEcpc), "owner" : "yangxuan06"},
        {"udf_name":"T7ROIHighQualityEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.T7ROIHighQualityEcpc), "owner" : "xusimin"},
        {"udf_name":"StoreWideAuthorTargetEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.StoreWideAuthorTargetEcpc), "owner" : "xusimin"},
        {"udf_name":"StoreLowClevelEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.StoreLowClevelEcpc), "owner" : "fuzhenqiang03"},
        {"udf_name":"NonCloseIaaEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.NonCloseIaaEcpc), "owner" : "jiangjinling"},
        {"udf_name":"GameBigRExploreEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameBigRExploreEcpc), "owner" : "nizhihao"},
        {"udf_name":"IndustryIaaRoasEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IndustryIaaRoasEcpc), "owner" : "jiangjinling"},
        {"udf_name":"InnerCommonInterventionEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerCommonInterventionEcpc), "owner" : "xiaoyuhao"},
        {"udf_name":"OuterCommonInterventionEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterCommonInterventionEcpc), "owner" : "xiaoyuhao"},
        {"udf_name":"PrivateMessageGroupEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.PrivateMessageAndWechatGroupEcpc), "owner" : "wengrunze"},
        {"udf_name":"IndustryUserTagEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IndustryUserTagEcpc), "owner" : "yangxuan06"},
        {"udf_name":"InnerIndustryUserEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerIndustryUserEcpc), "owner" : "lihantong"},
        {"udf_name":"InnerNewOrderEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerNewOrderEcpc), "owner" : "lichunchi"},
        {"udf_name":"InnerNewGuardNEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerNewGuardNEcpc), "owner" : "guoshenghao"},
        {"udf_name":"ExplorePageEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ExplorePageEcpc), "owner" : "duanxinning"},
        {"udf_name":"InnerExploreRelativeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerExploreRelativeEcpc), "owner" : "duanxinning"},
        {"udf_name":"LeadsSubmitCalibration" , "rank_factor_type" : get_enum(EcpcAdjustTag.LeadsSubmitCalibrationEcpc), "owner" : "wengrunze"},
        {"udf_name":"MiniGameSubsidyEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.MiniGameSubsidyEcpc), "owner" : "yangzhao07"},
        {"udf_name":"OuterCmtRankEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterCmtRankEcpc), "owner" : "zhaoyi13"},
        {"udf_name":"FictionHotBookEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionHotBookEcpcStrategy), "owner" : "jiangjinling"},
        {"udf_name":"GameIaaIpuEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameIaaIpuEcpc), "owner" : "gaoyuan21"},
        {"udf_name":"FictionIaaEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionIaaEcpcStrategy), "owner" : "jiangjinling"},
        {"udf_name":"LeadsSubmitUserSplitEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.HighUserLevelEcpc), "owner" : "wengrunze"},
        {"udf_name":"FollowPageEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FollowPageEcpc), "owner" : "jiyang"},
        {"udf_name":"LiveInnerEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.LiveInnerEcpc), "owner" : "dingyiming05"},
        {"udf_name":"FictionIaaIpuEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionIaaIpuEcpc), "owner" : "zhaoyi13"},
        {"udf_name":"OuterLiveRlEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLiveRlEcpc), "owner" : "yishijie"},
        {"udf_name":"OuterIaaRlEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterIaaRlEcpc), "owner" : "liuxingchen07"},
        {"udf_name":"FictionIapNcEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionIapNcEcpcStrategy), "owner" : "jiangjinling"},
        {"udf_name":"PmFwhEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.PmFwhEcpc), "owner" : "xuyanyan03"},
        {"udf_name":"PmPoiSearchEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.PmPoiSearchEcpc), "owner" : "xutaotao03"},
        {"udf_name":"InnerLiveUpliftEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerLiveUpliftEcpc), "owner" : "linbowei"},
        {"udf_name":"FictionNotNaEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionNotNaEcpc), "owner" : "zhaoyi13"},
        {"udf_name":"GameCxrEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameCxrEcpc), "owner" : "lichao22"},
        {"udf_name":"OuterLlmGenerativeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLlmGenerativeEcpc), "owner" : "huwenkang03"},
        {"udf_name":"OuterLlmFwhEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLlmFwhEcpc), "owner" : "litianfeng"},
        ])
    return self

  @module()
  def ecpc_common_module(self, name, item_table, formula_type):
    self.common_ecpc_factor(item_table = item_table, formula_type = formula_type) \
    .ad_common_factor(
      item_table = item_table,
      op_type = "MAX_MIN",
      input_formula_type_list=[formula_type],
      output_formula_type = get_enum(Formula.EcpcMaxMin)
    ) \
    .formula_dot_mixer(
      item_table = item_table,
      dot_info = [{
          "item_attr" : "$EcpcMaxMin.formula_compute", # 需打印字段
          "dot_name" : "EcpcMaxMin",  #打印时展示的key
          "formula" : "Cpm",  #所属公式
          "coefficient" : 1000}])
    return self

  # NOTINE：24策略重构，新增 ecpc 需同步加到 common_ecpc_factor
  # ecpc max-min common注册（同时在软硬广生效）
  def ecpc_max_min_common_factor(self, item_table, formula_type):
    self.add_udf_factor_list(
      name = "EcpcCommonRegister",
      item_table = item_table,
      formula_type = formula_type,
      udf_factor_info = [
        {"udf_name":"EspPhotoEffectivePlayEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.EspPhotoEffectivePlay), "owner" : "wangyuan11"},
        {"udf_name":"WanheMatchingEfficiencyEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.WanheMatchingEfficiencyEcpc), "owner" : "zhaokun03"},
        {"udf_name":"WanheActionTypeCostRatioEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.WanheActionTypeCostRatioEcpc), "owner" : "zhaokun03"},
        {"udf_name":"ExploreActionTypeCostRatioEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ExploreActionTypeCostRatioEcpc), "owner" : "yesiqi"},
        {"udf_name":"DeepRewardedCoinEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.DeepRewardedCoinEcpc), "owner" : "xuxu"},
        {"udf_name":"InnerTriggerItemEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerTriggerItemEcpc), "owner" : "jinhui05"},
        {"udf_name":"GamePlayerOnlyFirstChargeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GamePlayerOnlyFirstChargeEcpc), "owner" : "zhaoqilong"},
        {"udf_name":"OuterIndustryBoostEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterIndustryBoostEcpc), "owner" : "zhaoqilong"},
        {"udf_name":"OuterGameExploreEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.OuterGameExploreEcpc), "owner" : "yuhanzhang"},
        {"udf_name":"InspireQualityAudienceSupress" , "rank_factor_type" : get_enum(EcpcAdjustTag.InspireQualityAudienceSuppress), "owner" : "lixu05"},
        {"udf_name":"PlayletRetargetEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.PlayletRetargetEcpc), "owner" : "yangxuan06"},
        {"udf_name":"OuterLiveOrientationToolEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLiveOrientationToolEcpc), "owner" : "zhangmengxin"},
        {"udf_name":"EduLpsDeepEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.EduLpsDeepEcpc), "owner" : "linyuhao03"},
        {"udf_name":"EduLpsDeepAllClassEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.EduLpsDeepAllClassEcpc), "owner" : "linyuhao03"},
        {"udf_name":"FinCreditRoiEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FinCreditRoiEcpc), "owner" : "liuxinyi08"},
        {"udf_name":"InnerAccountSuppressEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerAccountSuppressEcpc), "owner" : "nizhihao"},
        {"udf_name":"RewardGameEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.RewardGameEcpc), "owner" : "jiangnan07"},
        {"udf_name":"IncentiveEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.IncentiveEcpc), "owner" : "gaozepeng"},
        {"udf_name":"GameRetargetEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameRetargetEcpc), "owner" : "rongyu03"},
        {"udf_name":"WanheProfileEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.WanheProfileEcpc), "owner" : "zhaokun03"},
        {"udf_name":"SideWindowEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.SideWindowEcpc), "owner" : "zhaokun03"},
        {"udf_name":"WeekstayRetentionEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.WeekRetention), "owner" : "niehui"},
        {"udf_name":"CrowdEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.StrategyCrowdEcpc), "owner" : "lixu05"},
        {"udf_name":"ConvQualityEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.ConvQuality), "owner" : "dinghe03"},
        {"udf_name": "ConvQualityEcpcStrategyNew", "rank_factor_type": get_enum(EcpcAdjustTag.ConvQualityNew), "owner": "zhangrui30"},
        {"udf_name":"KnewsColdStartStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.KnewsColdStart), "owner" : "yangyanxi"},
        {"udf_name":"EcomEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.EcomEcpc), "owner" : "songxu"},
        {"udf_name":"LALpsValidCluesEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.LALpsValidClues), "owner" : "pengshiyuan"},
        {"udf_name":"MerchantEcpmStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.AdMerchantEcpc), "owner" : "wangtao21"},
        {"udf_name":"LpsdeepEcpmStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.LpsDeepEcpc), "owner" : "xuyanyan03"},
        {"udf_name":"PrivateMessageEcpmStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.PrivateMessageEcpc), "owner" : "zhaijianwei"},
        {"udf_name":"OuterOverissueEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterOverissueEcpc), "owner" : "yangfukang03"},
        {"udf_name":"OuterDncNcModelEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterDncNcModelEcpc), "owner" : "yangfukang03"},
        {"udf_name":"UserVauleGroupEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.UserVauleGroupEcpc), "owner" : "dingyiming05"},
        {"udf_name":"InnovationTrafficEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnovationTrafficEcpc), "owner" : "zhaokun03"},
        {"udf_name":"OuterLiveGameConvEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLiveGameConvEcpc), "owner" : "zhangmengxin"},
        {"udf_name":"ConvNextstayEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ConvNextstayEcpc), "owner" : "yishijie"},
        {"udf_name":"ClueFwhIntentEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ClueFwhIntentEcpc), "owner" : "xutaotao03"},
        {"udf_name":"LpsAcqGenEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.LpsAcqGenEcpc), "owner" : "xutaotao03"},
        {"udf_name":"IaaAcqGenEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IaaAcqGenEcpc), "owner" : "liangyukang"},
        {"udf_name":"WeGameReturnTimeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.WeGameReturnTimeEcpc), "owner" : "jiangpeng07"},
        {"udf_name":"OuterloopNcMaxEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterloopNcMaxEcpc), "owner" : "jiayalong"},
        {"udf_name":"KGameEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.KGameEcpc), "owner" : "jiangpeng07"},
        {"udf_name":"CidQcpxCpmEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.CidQcpxCpmEcpc), "owner" : "zhangyiwei03"},
        {"udf_name":"OuterLiveVtrEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLiveVtrEcpc), "owner" : "nizhihao"},
        {"udf_name":"GameRtaSignEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameRtaSignEcpc), "owner" : "nizhihao"},
        {"udf_name":"PaySkitLtvRectifyEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.PaySkitLtvRectifyEcpc), "owner" : "jiyang"},
        {"udf_name":"GameShouFaEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameShouFaEcpc), "owner" : "nizhihao"},
        {"udf_name":"InnerLSPEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerLSPEcpc), "owner" : "tengwei"},
        {"udf_name":"OuterMedicalExploreEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.OuterMedicalExploreEcpc), "owner": "tengwei"},
        {"udf_name":"InnerProductEEEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerProductEEEcpc), "owner" : "guochangyu"},
        {"udf_name":"InnerAdxEEEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerAdxEEEcpc), "owner" : "cuihongyi"},
        {"udf_name":"InnerAdxEEOrientationEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerAdxEEOrientationEcpc), "owner" : "cuihongyi"},
        {"udf_name":"InnerStorewideOrientationEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerStorewideOrientationEcpc), "owner" : "wangqi12"},
        {"udf_name":"InnerLSPAggrPageEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerLSPAggrPageEcpc), "owner" : "guochangyu"},
        {"udf_name":"InnerLspUserBehaviorEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerLspUserBehaviorEcpc), "owner": "guochangyu"},
        {"udf_name":"LocalLifeUserExploreEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.LocalLifeUserExploreEcpc), "owner": "guochangyu"},
        {"udf_name":"LocalLifeConsumPowerEcpc", "rank_factor_type": get_enum(EcpcAdjustTag.LocalLifeConsumPowerEcpc), "owner": "guochangyu"},
        {"udf_name":"FictionRetargetEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterFictionRetargetEcpc), "owner" : "jiangjinling"},
        {"udf_name":"OuterSelfServiceToolEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterSelfServiceToolEcpc), "owner" : "jiayalong"},
        {"udf_name":"PoiDistanceAdjustStrategy", "rank_factor_type" : get_enum(EcpcAdjustTag.PoiDistanceEcpc), "owner" : "lianghaoqiang"},
        {"udf_name":"InnerLSPUserBLevelEcpc", "rank_factor_type" : get_enum(EcpcAdjustTag.InnerLSPUserBLevelEcpc), "owner" : "tengwei"},
        {"udf_name":"IAADJRaiseEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IAADJRaiseEcpc), "owner" : "liyichen05"},
        {"udf_name":"StorewideUpliftEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.StorewideUpliftEcpc), "owner" : "tangxiaochao05"},
        {"udf_name":"StoreWideAuthorTargetEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.StoreWideAuthorTargetEcpc), "owner" : "xusimin"},
        {"udf_name":"StoreLowClevelEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.StoreLowClevelEcpc), "owner" : "fuzhenqiang03"},
        {"udf_name":"StorewideMerchantUpliftEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.StorewideMerchantUpliftEcpc), "owner" : "lizuxin"},
        {"udf_name":"IapCorePopulationProtectEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IapCorePopulationProtectEcpc), "owner" : "chenxian"},
        {"udf_name":"FictionSubsidyEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionSubsidyEcpc), "owner" : "yangxuan06"},
        {"udf_name":"NonCloseIaaEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.NonCloseIaaEcpc), "owner" : "jiangjinling"},
        {"udf_name":"GameBigRExploreEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameBigRExploreEcpc), "owner" : "nizhihao"},
        {"udf_name":"IndustryIaaRoasEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IndustryIaaRoasEcpc), "owner" : "jiangjinling"},
        {"udf_name":"InnerCommonInterventionEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerCommonInterventionEcpc), "owner" : "xiaoyuhao"},
        {"udf_name":"OuterCommonInterventionEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterCommonInterventionEcpc), "owner" : "xiaoyuhao"},
        {"udf_name":"PrivateMessageGroupEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.PrivateMessageAndWechatGroupEcpc), "owner" : "wengrunze"},
        {"udf_name":"IndustryUserTagEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IndustryUserTagEcpc), "owner" : "yangxuan06"},
        {"udf_name":"InnerIndustryUserEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerIndustryUserEcpc), "owner" : "lihantong"},
        {"udf_name":"InnerNewOrderEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerNewOrderEcpc), "owner" : "lichunchi"},
        {"udf_name":"InnerNewGuardNEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerNewGuardNEcpc), "owner" : "guoshenghao"},
        {"udf_name":"ExplorePageEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ExplorePageEcpc), "owner" : "duanxinning"},
        {"udf_name":"InnerExploreRelativeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerExploreRelativeEcpc), "owner" : "duanxinning"},
        {"udf_name":"LeadsSubmitCalibration" , "rank_factor_type" : get_enum(EcpcAdjustTag.LeadsSubmitCalibrationEcpc), "owner" : "wengrunze"},
        {"udf_name":"MiniGameSubsidyEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.MiniGameSubsidyEcpc), "owner" : "yangzhao07"},
        {"udf_name":"OuterCmtRankEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterCmtRankEcpc), "owner" : "zhaoyi13"},
        {"udf_name":"FictionHotBookEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionHotBookEcpcStrategy), "owner" : "jiangjinling"},
        {"udf_name":"GameIaaIpuEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.GameIaaIpuEcpc), "owner" : "gaoyuan21"},
        {"udf_name":"FictionIaaEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionIaaEcpcStrategy), "owner" : "jiangjinling"},
        {"udf_name":"LeadsSubmitUserSplitEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.HighUserLevelEcpc), "owner" : "wengrunze"},
        {"udf_name":"FollowPageEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FollowPageEcpc), "owner" : "jiyang"},
        {"udf_name":"LiveInnerEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.LiveInnerEcpc), "owner" : "dingyiming05"},
        {"udf_name":"FictionIaaIpuEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionIaaIpuEcpc), "owner" : "zhaoyi13"},
        {"udf_name":"OuterLiveRlEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLiveRlEcpc), "owner" : "yishijie"},
        {"udf_name":"OuterIaaRlEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterIaaRlEcpc), "owner" : "liuxingchen07"},
        {"udf_name":"FictionIapNcEcpcStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionIapNcEcpcStrategy), "owner" : "jiangjinling"},
        {"udf_name":"PmFwhEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.PmFwhEcpc), "owner" : "xuyanyan03"},
        {"udf_name":"PmPoiSearchEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.PmPoiSearchEcpc), "owner" : "xutaotao03"},
        {"udf_name":"InnerLiveUpliftEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.InnerLiveUpliftEcpc), "owner" : "linbowei"},
        {"udf_name":"FictionNotNaEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FictionNotNaEcpc), "owner" : "zhaoyi13"},
        {"udf_name":"OuterLlmGenerativeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLlmGenerativeEcpc), "owner" : "huwenkang03"},
        {"udf_name":"OuterLlmFwhEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterLlmFwhEcpc), "owner" : "litianfeng"},
        ])
    return self
  # NOTINE：24策略重构，新增 ecpc 需同步加到 common_ecpc_factor

  # NOTINE：24策略重构，新增 ecpc 需同步加到 common_ecpc_factor
  # ecpc max-min 硬广注册 禁止新增
  def ecpc_max_min_normal_factor(self, item_table, formula_type):
    self.add_udf_factor_list(
      name = "EcpcNormalRegister",
      item_table = item_table,
      formula_type = formula_type,
      udf_factor_info = [
        {"udf_name":"KeyActionEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.KeyActionEcpc), "owner" : "zhouqifei"},\
        {"udf_name":"EspLiveCalibration" , "rank_factor_type" : get_enum(EcpcAdjustTag.EspLiveCalibration), "owner" : "wangyuan"},
        {"udf_name":"EcpcWithAuthorStrategy" , "rank_factor_type" : get_enum(EcpcAdjustTag.EcpcWithAuthorStrategy), "owner" : "wanghongzi"},
        {"udf_name":"UserExporeEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.UserExporeEcpc), "owner" : "yuchengyuan"},
        {"udf_name":"IndustryModelEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.IndustryModelEcpc), "owner" : "yuchengyuan"},
        {"udf_name":"UpgradedIndustryModelEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.UpgradedIndustryModelEcpc), "owner" : "yishijie"},
        {"udf_name":"RevenueOptimiseEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.RevenueOptimiseEcpc), "owner" : "wangtao15"},
        {"udf_name":"MicroappDiscountEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.MicroappDiscountEcpc), "owner" : "xuxu"},
        {"udf_name":"KnewsColdStartEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.KnewsColdStartEcpc), "owner" : "wangtao15"},
        {"udf_name":"StatsAutoParam" , "rank_factor_type" : get_enum(EcpcAdjustTag.StatsAutoParam), "owner" : "liubing"},
        {"udf_name":"SplashBoostCoefEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.SplashBoostCoefEcpc), "owner" : "liubing05"},
        {"udf_name":"SplashEyemaxDiscountEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.SplashEyemaxDiscountEcpc), "owner" : "liubing05"},
        {"udf_name":"T7ROIZhongCaoEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.T7ROIZhongCaoEcpc), "owner" : "fukunyang"},
        ])
    return self

  # NOTINE：24策略重构，新增 ecpc 需同步加到 common_ecpc_factor
  # ecpc max-min 软广注册,禁止新增
  def ecpc_max_min_native_factor(self, item_table, formula_type):
    self.add_udf_factor_list(
      name = "EcpcNativeRegister",
      item_table = item_table,
      formula_type = formula_type,
      udf_factor_info = [
        {"udf_name":"OuterNativeAppAdvanceDiscount" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterNativeAppAdvanceDiscount), "owner" : "zhangmengxin"},
        {"udf_name":"FollowPageBoost" , "rank_factor_type" : get_enum(EcpcAdjustTag.FollowPageBoost), "owner" : "wangyang10"},
        {"udf_name":"EspLiveCalibration" , "rank_factor_type" : get_enum(EcpcAdjustTag.EspLiveCalibration), "owner" : "wangyuan11"},
        {"udf_name":"OuterDspCaliByConfig" , "rank_factor_type" : get_enum(EcpcAdjustTag.OuterDspCaliByConfig), "owner" : "zhangmengxin"},
        {"udf_name":"FanstopUATagEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FanstopUATagEcpc), "owner" : "tengwei"},
        {"udf_name":"FanstopPddUserPackageRedirectEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FanstopPddUserPackageRedirectEcpc), "owner" : "tengwei"},
        {"udf_name":"FanstopGameUserPackageEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FanstopGameUserPackageEcpc), "owner" : "lichunchi"},
        {"udf_name":"KeyActionEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.KeyActionEcpc), "owner" : "jiangpeng07"},
        {"udf_name":"T7ROIZhongCaoEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.T7ROIZhongCaoEcpc), "owner" : "fukunyang"},
        {"udf_name":"FanstopPrivateMessageUserEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FanstopPrivateMessageUserEcpc), "owner" : "jiayalong"},
        {"udf_name":"FanstopPhoneCallUserEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.FanstopPhoneCallUserEcpc), "owner" : "lijun03"},
        ])
    return self

  @module()
  def native_ecpc_max_min_module(self, name, item_table, formula_type):
    self.ecpc_max_min_common_factor(item_table = item_table, formula_type = formula_type) \
    .ecpc_max_min_native_factor(item_table = item_table, formula_type = formula_type) \
    .ad_common_factor(
      item_table = item_table,
      op_type = "MAX_MIN",
      input_formula_type_list=[formula_type],
      output_formula_type = get_enum(Formula.EcpcMaxMin)
    ) \
    .formula_dot_mixer(
      item_table = item_table,
      dot_info = [{
          "item_attr" : "$EcpcMaxMin.formula_compute", # 需打印字段
          "dot_name" : "EcpcMaxMin",  #打印时展示的key
          "formula" : "Cpm",  #所属公式
          "coefficient" : 1000}])
    return self

  @module()
  def normal_ecpc_max_min_module(self, name, item_table, formula_type):
    self.ecpc_max_min_common_factor(item_table = item_table, formula_type = formula_type) \
    .ecpc_max_min_normal_factor(item_table = item_table, formula_type = formula_type) \
    .ad_common_factor(
      item_table = item_table,
      op_type = "MAX_MIN",
      input_formula_type_list=[formula_type],
      output_formula_type = get_enum(Formula.EcpcMaxMin)
    ) \
    .formula_dot_mixer(
      item_table = item_table,
      dot_info = [{
          "item_attr" : "$EcpcMaxMin.formula_compute", # 需打印字段
          "dot_name" : "EcpcMaxMin",  #打印时展示的key
          "formula" : "Cpm",  #所属公式
          "coefficient" : 1000}])
    return self
  
  def ecpc_max_min_merchant_factor(self, item_table, formula_type):
    self.add_udf_factor_list(
      name = "EcpcMerchantRegister",
      item_table = item_table,
      formula_type = formula_type,
      udf_factor_info = [
        {"udf_name":"ShelfLiveCardRecallEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ShelfLiveCardRecallEcpc), "owner" : "yuancuili"},
        {"udf_name":"ShelfItemCardRecallEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ShelfItemCardRecallEcpc), "owner" : "yuancuili"},
        {"udf_name":"ShelfLiveWhiteAuthorEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ShelfLiveWhiteAuthorEcpc), "owner" : "yuancuili"},
        {"udf_name":"ShelfItemCardWhiteAuthorEcpc" , "rank_factor_type" : get_enum(EcpcAdjustTag.ShelfItemCardWhiteAuthorEcpc), "owner" : "chenziping"}
      ])
    return self

  @module()
  def merchant_ecpc_max_min_module(self, name, item_table, formula_type):
    self.ecpc_max_min_merchant_factor(item_table = item_table, formula_type = formula_type) \
    .ad_common_factor(
      item_table = item_table,
      op_type = "MAX_MIN",
      input_formula_type_list=[formula_type],
      output_formula_type = get_enum(Formula.EcpcMaxMin)
    ) \
    .formula_dot_mixer(
      item_table = item_table,
      dot_info = [{
          "item_attr" : "$EcpcMaxMin.formula_compute", # 需打印字段
          "dot_name" : "EcpcMaxMin",  #打印时展示的key
          "formula" : "Cpm",  #所属公式
          "coefficient" : 1000}])
    return self

