#!/usr/bin/env python3
# coding=utf-8

from ad_rank_dragonfly.ad_api_mixin import *
from dragonfly.modular.module import module
from dragonfly.modular.data_manager import data_manager, ab_param as ab, kconf_param as kconf
from module.factor.common_factor import CommonFactor
from module.enum.formula_type_enum import Formula, get_enum
from module.enum.factor_type_enum import PriceTag
class PriceFactor(CommonFactor):
  def normal_calc_origin_price(self):
    self.run_price_udf_factor(
      name = "CalcNormalOriginPrice",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalOriginPrice),
      rank_factor_type = get_enum(PriceTag.NormalOriginCalcPriceDragon),
      admit = "CalcNormalOriginPrice_Admit",
      compute = "CalcNormalOriginPrice_Compute")
    return self

  def native_calc_origin_price(self):
    self.run_price_udf_factor(
      name = "CalcNativeOriginPrice",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativeOriginPrice),
      rank_factor_type = get_enum(PriceTag.NativeOriginCalcPriceDragon),
      admit = "CalcNativeOriginPrice_Admit",
      compute = "CalcNativeOriginPrice_Compute")
    return self

  def unify_calc_origin_price(self):
    self.run_price_udf_factor(
      name = "CalcUnifyOriginPrice",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyOriginPrice),
      rank_factor_type = get_enum(PriceTag.UnifyOriginCalcPriceDragon),
      admit = "CalcUnifyOriginPrice_Admit",
      compute = "CalcUnifyOriginPrice_Compute")
    return self

  @module()
  def calc_normal_price_ratio(self):
    self.run_price_udf_factor(
      name = "Normal_PriceLpsdeep_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.LpsdeepPriceDiscount),
      admit = "AdjustPriceLpsdeep_Admit",
      compute = "AdjustPriceLpsdeep_Compute")\
    .run_price_udf_factor(
      name = "Normal_BillingSeparate_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.BillingSeperate),
      admit = "HardBillingSeparate_Admit",
      compute = "HardBillingSeparate_Compute")\
    .run_price_udf_factor(
      name = "Normal_PriceExploreInner_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.ExploreInnerPrice),
      admit = "AdjustPriceExploreInner_Admit",
      compute = "AdjustPriceExploreInner_Compute")\
    .run_price_udf_factor(
      name = "Normal_AdjustPriceSmbPriceRatio_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.SmbPriceDiscount),
      admit = "AdjustPriceSmbPriceRatio_Admit",
      compute = "AdjustPriceSmbPriceRatio_Compute")\
    .run_price_udf_factor(
      name = "Normal_AdjustInnerSelfServicePrice",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.SelfServicePriceDiscount),
      admit = "AdjustInnerSelfServicePrice_Admit",
      compute = "AdjustInnerSelfServicePrice_Compute")\
    .run_price_udf_factor(
      name = "Normal_AdjustFanstopFollowPrice_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.FanstopFollowPrice),
      admit = "AdjustFanstopFollowPrice_Admit",
      compute = "AdjustFanstopFollowPrice_Compute")\
    .run_price_udf_factor(
      name = "Normal_AdjustPriceGYL_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.GuessYouLikePriceDiscount),
      admit = "AdjustPriceGYL_Admit",
      compute = "AdjustPriceGYL_Compute")\
    .run_price_udf_factor(
      name = "Normal_PriceCidStra_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.CidPriceDiscount),
      admit = "PriceCidStra_Admit",
      compute = "PriceCidStra_Compute")\
    .run_price_udf_factor(
      name = "Normal_PriceCidStra_v3",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.CidPriceDiscountV3),
      admit = "PriceCidStraGoodsType_Admit",
      compute = "PriceCidStraGoodsType_Compute")\
    .run_price_udf_factor(
      name = "Normal_PriceNewProduct_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.NewProductDiscount),
      admit = "AdjustPriceNewProduct_Admit",
      compute = "AdjustPriceNewProduct_Compute")\
    .run_price_udf_factor(
      name = "Normal_InnerPriceDiscount_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerPriceDiscount),
      admit = "InnerPriceDiscount_Admit",
      compute = "InnerPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_LocalLifePriceDiscount_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.LocalLifePriceDiscount),
      admit = "LocalLifePriceDiscount_Admit",
      compute = "LocalLifePriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_NewSpuPriceDiscount_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.NewSpuPriceDiscount),
      admit = "NewSpuPriceDiscount_Admit",
      compute = "NewSpuPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_NewSpuPriceDiscount_v3",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.NewSpuCrowdDiscount),
      admit = "NewSpuPriceDiscountBuyer_Admit",
      compute = "NewSpuPriceDiscountBuyer_Compute")\
    .run_price_udf_factor(
      name = "Normal_PriceDiscount_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.PriceDiscount),
      admit = "PriceDiscount_Admit",
      compute = "PriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_DuanjuPriceDiscount_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.DuanjuPriceDiscount),
      admit = "DuanjuPriceDiscount_Admit",
      compute = "DuanjuPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_ClientAiLiveAdRerankPriceNew_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.ClientAiLiveAdRerankPrice),
      admit = "AdjustPriceClientAiLiveAdRerankNew_Admit",
      compute = "AdjustPriceClientAiLiveAdRerankNew_Compute")\
    .run_price_udf_factor(
      name = "ClientAiPhotoAdRerankPrice",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.ClientAiPhotoAdRerankPrice),
      admit = "AdjustPriceClientAiPhotoAdRerankNew_Admit",
      compute = "AdjustPriceClientAiPhotoAdRerank_Compute")\
    .run_price_udf_factor(
      name = "Normal_ClientAiP0RerankPriceNew_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.ClientAiP0RerankPrice),
      admit = "AdjustPriceClientAiP0RerankNew_Admit",
      compute = "AdjustPriceClientAiP0RerankNew_Compute")\
    .run_price_udf_factor(
      name = "Normal_AdMerchantPriceDiscount_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdMerchantPriceDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_StoreWideLiveWhiteListDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.StoreWideLiveWhiteListDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_StoreWideMerchantWhiteListDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.StoreWideMerchantWhiteListDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_InnerWhiteAccountDiscount_new",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerWhiteAccountDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_InnerCIDPriceDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.CidPriceDiscountV3),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_UpItemsPriceDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.UpItemsPrice),
      admit = "AdjustPriceUpItems_Admit",
      compute = "AdjustPriceUpItems_Compute")\
    .run_price_udf_factor(
      name = "Normal_HighTrPriceDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.LowTrIndustryDiscount),
      admit = "AdjustPriceHighTr_Admit",
      compute = "AdjustPriceHighTr_Compute")\
    .run_price_udf_factor(
      name = "Normal_ColdStartPriceDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.ColdStartPrice),
      admit = "AdjustPriceColdStart_Admit",
      compute = "AdjustPriceColdStart_Compute")\
    .run_price_udf_factor(
      name = "Normal_InnerHighValuePriceDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerHighValuePrice),
      admit = "AdjustInnerHighValue_Admit",
      compute = "AdjustInnerHighValue_Compute")\
    .run_price_udf_factor(
      name = "Normal_InnerWhiteAccountDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerWhiteAccountDiscount),
      admit = "InnerWhiteAccountDiscount_Admit",
      compute = "InnerWhiteAccountDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_PriceBadPhoto_v2",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.PriceWithTax),
      admit = "AdjustPriceBadPhotoRatio_Admit",
      compute = "AdjustPriceBadPhotoRatio_Compute")\
    .run_price_udf_factor(
      name = "Normal_PoQuanPriceDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.PoQuanPriceDiscount),
      admit = "PoQuanPriceDiscount_Admit",
      compute = "PoQuanPriceDiscount_Compute")\
   .run_price_udf_factor(
      name = "Normal_IAAPGameROIDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.IAAPGameROIDiscount),
      admit = "IAAPGameROIDiscount_Admit",
      compute = "IAAPGameROIDiscount_Compute")\
   .run_price_udf_factor(
      name = "Normal_GameBigRExplore",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.GameBigRExplore),
      admit = "GameBigRExplore_Admit",
      compute = "GameBigRExplore_Compute")\
    .run_price_udf_factor(
      name = "Normal_GameStabilityDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.GameStabilityDiscount),
      admit = "GameStabilityDiscount_Admit",
      compute = "GameStabilityDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_KuaiGameInspireDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.KuaiGameInspireDiscount),
      admit = "KuaiGameInspireDiscount_Admit",
      compute = "KuaiGameInspireDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_InnerProductEEPriceDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerProductEEPriceDiscount),
      admit = "InnerProductEEPriceDiscount_Admit",
      compute = "InnerProductEEPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_MatrixAppPriceDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.MatrixAppPriceDiscount),
      admit = "MatrixAppPriceDiscount_Admit",
      compute = "MatrixAppPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_InnerAdxEEOrientationDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerAdxEEOrientationDiscount),
      admit = "InnerAdxEEOrientationDiscount_Admit",
      compute = "InnerAdxEEOrientationDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_FanstopPrivateMessageLeadsDiscount",
      item_table =  "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.FanstopPrivateMessageLeadsDiscount),
      admit = "FanstopPrivateMessageLeadsDiscount_Admit",
      compute = "FanstopPrivateMessageLeadsDiscount_Compute")\
    .run_price_udf_factor(
      name = "Normal_AdjustPricePrivateMessage",
      item_table =  "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustPricePrivateMessage),
      admit = "AdjustPricePrivateMessage_Admit",
      compute = "AdjustPricePrivateMessage_Compute") \
    .run_price_udf_factor(
      name = "Normal_AdjustPriceSimplePromotion",
      item_table =  "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustPriceSimplePromotion),
      admit = "AdjustPriceSimplePromotion_Admit",
      compute = "AdjustPriceSimplePromotion_Compute") \
    .run_price_udf_factor(
      name = "Normal_OuterLivePriceDiscount",
      item_table =  "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.OuterLivePriceDiscount),
      admit = "OuterLivePriceDiscount_Admit",
      compute = "OuterLivePriceDiscount_Compute") \
    .run_price_udf_factor(
      name = "Normal_AdjustIncentivePrice",
      item_table =  "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustIncentivePrice),
      admit = "AdjustIncentivePrice_Admit",
      compute = "AdjustIncentivePrice_Compute") \
    .run_price_udf_factor(
      name = "Normal_AdStorewideUplift",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdStorewideUplift),
      admit = "AdStorewideUplift_Admit",
      compute = "AdStorewideUplift_Compute") \
    .run_price_udf_factor(
      name = "AdjustPriceInnerLoopBigCardSecondRequest",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerLoopBigCard),
      admit = "AdjustPriceInnerLoopBigCardSecondRequest_Admit",
      compute = "AdjustPriceInnerLoopBigCardSecondRequest_Compute") \
    .run_price_udf_factor(
      name = "Normal_FictionHotBookDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.FictionHotBookDiscount),
      admit = "FictionHotBookDiscount_Admit",
      compute = "FictionHotBookDiscount_Compute") \
    .run_price_udf_factor(
      name = "Normal_InnerNewOrderDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerNewOrderDiscount),
      admit = "InnerNewOrderDiscount_Admit",
      compute = "InnerNewOrderDiscount_Compute") \
    .run_price_udf_factor(
      name = "Normal_InnerHighQualityDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerHighQualityDiscount),
      admit = "InnerHighQualityPrice_Admit",
      compute = "InnerHighQualityPrice_Compute") \
    .run_price_udf_factor(
      name = "Normal_InnerNewGuardNDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerNewGuardNDiscount),
      admit = "InnerNewGuardN_Admit",
      compute = "InnerNewGuardN_Compute") \
    .run_price_udf_factor(
      name = "Normal_InnerCtcvrCaliDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerCtcvrCaliDiscount),
      admit = "InnerCtcvrCaliPrice_Admit",
      compute = "InnerCtcvrCaliPrice_Compute") \
    .run_price_udf_factor(
      name = "Normal_InnerLivePrice",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerLivePrice),
      admit = "InnerLivePrice_Admit",
      compute = "InnerLivePrice_Compute") \
    .run_price_udf_factor(
      name = "Normal_StorewideLowClevelDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.StorewideLowClevelDiscount),
      admit = "StorewideLowClevelDiscount_Admit",
      compute = "StorewideLowClevelDiscount_Compute") \
    .run_price_udf_factor(
      name = "Normal_NewTopkPriceDiscount",
      item_table = "front_normal_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.ColdStartPrice),
      admit = "AdjustPriceNewTopk_Admit",
      compute = "AdjustPriceNewTopk_Compute")\

    return self

  @module()
  def calc_native_price_ratio(self):
    self.run_price_udf_factor(
      name = "Native_PriceLpsdeep_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.LpsdeepPriceDiscount),
      admit = "AdjustPriceLpsdeep_Admit",
      compute = "AdjustPriceLpsdeep_Compute")\
    .run_price_udf_factor(
      name = "Native_PriceExploreInner_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.ExploreInnerPrice),
      admit = "AdjustPriceExploreInner_Admit",
      compute = "AdjustPriceExploreInner_Compute")\
    .run_price_udf_factor(
      name = "Native_AdjustPriceSmbPriceRatio_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.SmbPriceDiscount),
      admit = "AdjustPriceSmbPriceRatio_Admit",
      compute = "AdjustPriceSmbPriceRatio_Compute")\
    .run_price_udf_factor(
      name = "Native_AdjustInnerSelfServicePrice",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.SelfServicePriceDiscount),
      admit = "AdjustInnerSelfServicePrice_Admit",
      compute = "AdjustInnerSelfServicePrice_Compute")\
    .run_price_udf_factor(
      name = "Native_AdjustFanstopFollowPrice_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.FanstopFollowPrice),
      admit = "AdjustFanstopFollowPrice_Admit",
      compute = "AdjustFanstopFollowPrice_Compute")\
    .run_price_udf_factor(
      name = "Native_AdjustPriceGYL_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.GuessYouLikePriceDiscount),
      admit = "AdjustPriceGYL_Admit",
      compute = "AdjustPriceGYL_Compute")\
    .run_price_udf_factor(
      name = "NativeBS_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.BillingSeperate),
      admit = "NativeBS_Admit",
      compute = "NativeBS_Compute")\
    .run_price_udf_factor(
      name = "Native_PriceNewProduct_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.NewProductDiscount),
      admit = "AdjustPriceNewProduct_Admit",
      compute = "AdjustPriceNewProduct_Compute")\
    .run_price_udf_factor(
      name = "Native_PriceCidStra_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.CidPriceDiscount),
      admit = "PriceCidStra_Admit",
      compute = "PriceCidStra_Compute")\
    .run_price_udf_factor(
      name = "Native_PriceCidStra_v3",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.CidPriceDiscountV3),
      admit = "PriceCidStraGoodsType_Admit",
      compute = "PriceCidStraGoodsType_Compute")\
    .run_price_udf_factor(
      name = "NativeFanstopV2NobidBS_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.FanstopBillingSeperate),
      admit = "NativeFanstopV2NobidBS_Admit",
      compute = "NativeFanstopV2NobidBS_Compute")\
    .run_price_udf_factor(
      name = "Native_InnerPriceDiscount_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerPriceDiscount),
      admit = "InnerPriceDiscount_Admit",
      compute = "InnerPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_LocalLifePriceDiscount_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.LocalLifePriceDiscount),
      admit = "LocalLifePriceDiscount_Admit",
      compute = "LocalLifePriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_NewSpuPriceDiscount_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.NewSpuPriceDiscount),
      admit = "NewSpuPriceDiscount_Admit",
      compute = "NewSpuPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_NewSpuPriceDiscount_v3",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.NewSpuCrowdDiscount),
      admit = "NewSpuPriceDiscountBuyer_Admit",
      compute = "NewSpuPriceDiscountBuyer_Compute")\
    .run_price_udf_factor(
      name = "Native_PriceDiscount_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.NativeOuterPriceDiscount),
      admit = "PriceDiscount_Admit",
      compute = "PriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_DuanjuPriceDiscount_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.DuanjuNativeOuterPriceDiscount),
      admit = "DuanjuPriceDiscount_Admit",
      compute = "DuanjuPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_ClientAiLiveAdRerankPriceNew_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.ClientAiLiveAdRerankPrice),
      admit = "AdjustPriceClientAiLiveAdRerankNew_Admit",
      compute = "AdjustPriceClientAiLiveAdRerankNew_Compute")\
    .run_price_udf_factor(
      name = "Native_ClientAiPhotoAdRerankPrice",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.ClientAiPhotoAdRerankPrice),
      admit = "AdjustPriceClientAiPhotoAdRerankNew_Admit",
      compute = "AdjustPriceClientAiPhotoAdRerank_Compute")\
    .run_price_udf_factor(
      name = "Native_ClientAiP0RerankPriceNew_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.ClientAiP0RerankPrice),
      admit = "AdjustPriceClientAiP0RerankNew_Admit",
      compute = "AdjustPriceClientAiP0RerankNew_Compute")\
    .run_price_udf_factor(
      name = "Native_AdMerchantPriceDiscount_v2",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.AdMerchantPriceDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute") \
    .run_price_udf_factor(
      name = "Native_StoreWideLiveWhiteListDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.StoreWideLiveWhiteListDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute") \
    .run_price_udf_factor(
      name = "Native_StoreWideMerchantWhiteListDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.StoreWideMerchantWhiteListDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute") \
    .run_price_udf_factor(
      name = "Native_InnerWhiteAccountDiscount_new",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerWhiteAccountDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute") \
    .run_price_udf_factor(
      name = "Native_InnerCIDPriceDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.CidPriceDiscountV3),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_UpItemsPriceDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.UpItemsPrice),
      admit = "AdjustPriceUpItems_Admit",
      compute = "AdjustPriceUpItems_Compute")\
    .run_price_udf_factor(
      name = "Native_HighTrPriceDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.LowTrIndustryDiscount),
      admit = "AdjustPriceHighTr_Admit",
      compute = "AdjustPriceHighTr_Compute")\
    .run_price_udf_factor(
      name = "Native_ColdStartPriceDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.ColdStartPrice),
      admit = "AdjustPriceColdStart_Admit",
      compute = "AdjustPriceColdStart_Compute")\
    .run_price_udf_factor(
      name = "Native_InnerHighValuePriceDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerHighValuePrice),
      admit = "AdjustInnerHighValue_Admit",
      compute = "AdjustInnerHighValue_Compute")\
    .run_price_udf_factor(
      name = "Native_InnerWhiteAccountDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerWhiteAccountDiscount),
      admit = "InnerWhiteAccountDiscount_Admit",
      compute = "InnerWhiteAccountDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_PoQuanPriceDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.PoQuanPriceDiscount),
      admit = "PoQuanPriceDiscount_Admit",
      compute = "PoQuanPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_IAAPGameROIDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.IAAPGameROIDiscount),
      admit = "IAAPGameROIDiscount_Admit",
      compute = "IAAPGameROIDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_GameBigRExplore",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.GameBigRExplore),
      admit = "GameBigRExplore_Admit",
      compute = "GameBigRExplore_Compute")\
    .run_price_udf_factor(
      name = "Native_GameStabilityDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.GameStabilityDiscount),
      admit = "GameStabilityDiscount_Admit",
      compute = "GameStabilityDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_KuaiGameInspireDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.KuaiGameInspireDiscount),
      admit = "KuaiGameInspireDiscount_Admit",
      compute = "KuaiGameInspireDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_InnerProductEEPriceDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerProductEEPriceDiscount),
      admit = "InnerProductEEPriceDiscount_Admit",
      compute = "InnerProductEEPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_MatrixAppPriceDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.MatrixAppPriceDiscount),
      admit = "MatrixAppPriceDiscount_Admit",
      compute = "MatrixAppPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_InnerAdxEEOrientationDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerAdxEEOrientationDiscount),
      admit = "InnerAdxEEOrientationDiscount_Admit",
      compute = "InnerAdxEEOrientationDiscount_Compute")\
    .run_price_udf_factor(
      name = "Native_FanstopPrivateMessageLeadsDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.FanstopPrivateMessageLeadsDiscount),
      admit = "FanstopPrivateMessageLeadsDiscount_Admit",
      compute = "FanstopPrivateMessageLeadsDiscount_Compute") \
    .run_price_udf_factor(
      name = "Native_AdjustPricePrivateMessage",
      item_table =  "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustPricePrivateMessage),
      admit = "AdjustPricePrivateMessage_Admit",
      compute = "AdjustPricePrivateMessage_Compute") \
    .run_price_udf_factor(
      name = "Native_AdjustPriceSimplePromotion",
      item_table =  "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustPriceSimplePromotion),
      admit = "AdjustPriceSimplePromotion_Admit",
      compute = "AdjustPriceSimplePromotion_Compute") \
    .run_price_udf_factor(
      name = "Native_OuterLivePriceDiscount",
      item_table =  "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.OuterLivePriceDiscount),
      admit = "OuterLivePriceDiscount_Admit",
      compute = "OuterLivePriceDiscount_Compute") \
    .run_price_udf_factor(
      name = "Native_AdjustIncentivePrice",
      item_table =  "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustIncentivePrice),
      admit = "AdjustIncentivePrice_Admit",
      compute = "AdjustIncentivePrice_Compute") \
    .run_price_udf_factor(
      name = "Native_AdStorewideUplift",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.AdStorewideUplift),
      admit = "AdStorewideUplift_Admit",
      compute = "AdStorewideUplift_Compute") \
    .run_price_udf_factor(
      name = "AdjustPriceInnerLoopBigCardSecondRequest",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerLoopBigCard),
      admit = "AdjustPriceInnerLoopBigCardSecondRequest_Admit",
      compute = "AdjustPriceInnerLoopBigCardSecondRequest_Compute") \
    .run_price_udf_factor(
      name = "Native_FictionHotBookDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.FictionHotBookDiscount),
      admit = "FictionHotBookDiscount_Admit",
      compute = "FictionHotBookDiscount_Compute") \
    .run_price_udf_factor(
      name = "Native_InnerNewOrderDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerNewOrderDiscount),
      admit = "InnerNewOrderDiscount_Admit",
      compute = "InnerNewOrderDiscount_Compute") \
    .run_price_udf_factor(
      name = "Native_InnerHighQualityDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerHighQualityDiscount),
      admit = "InnerHighQualityPrice_Admit",
      compute = "InnerHighQualityPrice_Compute") \
    .run_price_udf_factor(
      name = "Native_InnerNewGuardNDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerNewGuardNDiscount),
      admit = "InnerNewGuardN_Admit",
      compute = "InnerNewGuardN_Compute") \
    .run_price_udf_factor(
      name = "Native_InnerCtcvrCaliDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerCtcvrCaliDiscount),
      admit = "InnerCtcvrCaliPrice_Admit",
      compute = "InnerCtcvrCaliPrice_Compute") \
    .run_price_udf_factor(
      name = "Native_InnerLivePrice",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerLivePrice),
      admit = "InnerLivePrice_Admit",
      compute = "InnerLivePrice_Compute") \
    .run_price_udf_factor(
      name = "Native_StorewideLowClevelDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.StorewideLowClevelDiscount),
      admit = "StorewideLowClevelDiscount_Admit",
      compute = "StorewideLowClevelDiscount_Compute") \
    .run_price_udf_factor(
      name = "Native_NewTopkPriceDiscount",
      item_table = "front_native_item_table",
      formula_type = get_enum(Formula.NativePriceRatio),
      rank_factor_type = get_enum(PriceTag.ColdStartPrice),
      admit = "AdjustPriceNewTopk_Admit",
      compute = "AdjustPriceNewTopk_Compute")
    return self

  @module()
  def calc_unify_price_ratio(self):
    self.run_price_udf_factor(
      name = "Unify_PriceLpsdeep_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.LpsdeepPriceDiscount),
      admit = "AdjustPriceLpsdeep_Admit",
      compute = "AdjustPriceLpsdeep_Compute")\
    .run_price_udf_factor(
      name = "Unify_BillingSeparate_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.UnifyHardBillingSeparate),
      admit = "HardBillingSeparate_Admit",
      compute = "HardBillingSeparate_Compute")\
    .run_price_udf_factor(
      name = "Unify_PriceExploreInner_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.ExploreInnerPrice),
      admit = "AdjustPriceExploreInner_Admit",
      compute = "AdjustPriceExploreInner_Compute")\
    .run_price_udf_factor(
      name = "Unify_AdjustPriceSmbPriceRatio_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.SmbPriceDiscount),
      admit = "AdjustPriceSmbPriceRatio_Admit",
      compute = "AdjustPriceSmbPriceRatio_Compute")\
    .run_price_udf_factor(
      name = "Unify_AdjustInnerSelfServicePrice",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.SelfServicePriceDiscount),
      admit = "AdjustInnerSelfServicePrice_Admit",
      compute = "AdjustInnerSelfServicePrice_Compute")\
    .run_price_udf_factor(
      name = "Unify_AdjustFanstopFollowPrice_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.FanstopFollowPrice),
      admit = "AdjustFanstopFollowPrice_Admit",
      compute = "AdjustFanstopFollowPrice_Compute")\
    .run_price_udf_factor(
      name = "Unify_AdjustPriceGYL_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.GuessYouLikePriceDiscount),
      admit = "AdjustPriceGYL_Admit",
      compute = "AdjustPriceGYL_Compute")\
    .run_price_udf_factor(
      name = "Unify_PriceCidStra_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.CidPriceDiscount),
      admit = "PriceCidStra_Admit",
      compute = "PriceCidStra_Compute")\
    .run_price_udf_factor(
      name = "Unify_PriceCidStra_v3",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.CidPriceDiscountV3),
      admit = "PriceCidStraGoodsType_Admit",
      compute = "PriceCidStraGoodsType_Compute")\
    .run_price_udf_factor(
      name = "Unify_PriceNewProduct_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.NewProductDiscount),
      admit = "AdjustPriceNewProduct_Admit",
      compute = "AdjustPriceNewProduct_Compute")\
    .run_price_udf_factor(
      name = "Unify_InnerPriceDiscount_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerPriceDiscount),
      admit = "InnerPriceDiscount_Admit",
      compute = "InnerPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_LocalLifePriceDiscount_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.LocalLifePriceDiscount),
      admit = "LocalLifePriceDiscount_Admit",
      compute = "LocalLifePriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_NewSpuPriceDiscount_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.NewSpuPriceDiscount),
      admit = "NewSpuPriceDiscount_Admit",
      compute = "NewSpuPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_NewSpuPriceDiscount_v3",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.NewSpuCrowdDiscount),
      admit = "NewSpuPriceDiscountBuyer_Admit",
      compute = "NewSpuPriceDiscountBuyer_Compute")\
    .run_price_udf_factor(
      name = "Unify_PriceDiscount_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.PriceDiscount),
      admit = "PriceDiscount_Admit",
      compute = "PriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_DuanjuPriceDiscount_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.DuanjuPriceDiscount),
      admit = "DuanjuPriceDiscount_Admit",
      compute = "DuanjuPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_ClientAiLiveAdRerankPriceNew_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.ClientAiLiveAdRerankPrice),
      admit = "AdjustPriceClientAiLiveAdRerankNew_Admit",
      compute = "AdjustPriceClientAiLiveAdRerankNew_Compute")\
    .run_price_udf_factor(
      name = "Unify_ClientAiPhotoAdRerankPrice",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.ClientAiPhotoAdRerankPrice),
      admit = "AdjustPriceClientAiPhotoAdRerankNew_Admit",
      compute = "AdjustPriceClientAiPhotoAdRerank_Compute")\
    .run_price_udf_factor(
      name = "Unify_ClientAiP0RerankPriceNew_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.ClientAiP0RerankPrice),
      admit = "AdjustPriceClientAiP0RerankNew_Admit",
      compute = "AdjustPriceClientAiP0RerankNew_Compute")\
    .run_price_udf_factor(
      name = "Unify_AdMerchantPriceDiscount_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdMerchantPriceDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_StoreWideLiveWhiteListDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.StoreWideLiveWhiteListDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_StoreWideMerchantWhiteListDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.StoreWideMerchantWhiteListDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_InnerWhiteAccountDiscount_new",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerWhiteAccountDiscount),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_InnerCIDPriceDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.CidPriceDiscountV3),
      admit = "AdMerchantPriceDiscount_Admit",
      compute = "AdMerchantPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_UpItemsPriceDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.UpItemsPrice),
      admit = "AdjustPriceUpItems_Admit",
      compute = "AdjustPriceUpItems_Compute")\
    .run_price_udf_factor(
      name = "Unify_HighTrPriceDiscount",
      item_table = "front_Unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.LowTrIndustryDiscount),
      admit = "AdjustPriceHighTr_Admit",
      compute = "AdjustPriceHighTr_Compute")\
    .run_price_udf_factor(
      name = "Unify_ColdStartPriceDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.ColdStartPrice),
      admit = "AdjustPriceColdStart_Admit",
      compute = "AdjustPriceColdStart_Compute")\
    .run_price_udf_factor(
      name = "Unify_InnerHighValuePriceDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerHighValuePrice),
      admit = "AdjustInnerHighValue_Admit",
      compute = "AdjustInnerHighValue_Compute")\
    .run_price_udf_factor(
      name = "Unify_InnerWhiteAccountDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerWhiteAccountDiscount),
      admit = "InnerWhiteAccountDiscount_Admit",
      compute = "InnerWhiteAccountDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_PriceBadPhoto_v2",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.PriceWithTax),
      admit = "AdjustPriceBadPhotoRatio_Admit",
      compute = "AdjustPriceBadPhotoRatio_Compute")\
    .run_price_udf_factor(
      name = "Unify_PoQuanPriceDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.PoQuanPriceDiscount),
      admit = "PoQuanPriceDiscount_Admit",
      compute = "PoQuanPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_IAAPGameROIDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.IAAPGameROIDiscount),
      admit = "IAAPGameROIDiscount_Admit",
      compute = "IAAPGameROIDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_GameBigRExplore",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.GameBigRExplore),
      admit = "GameBigRExplore_Admit",
      compute = "GameBigRExplore_Compute")\
    .run_price_udf_factor(
      name = "Unify_GameStabilityDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.GameStabilityDiscount),
      admit = "GameStabilityDiscount_Admit",
      compute = "GameStabilityDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_KuaiGameInspireDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.KuaiGameInspireDiscount),
      admit = "KuaiGameInspireDiscount_Admit",
      compute = "KuaiGameInspireDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_UnifyNativeBS",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.UnifyNativeBS),
      admit = "UnifyNativeBS_Admit",
      compute = "UnifyNativeBS_Compute")\
    .run_price_udf_factor(
      name = "Unify_UnifyNativeFanstopV2NobidBS",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.UnifyNativeFanstopV2NobidBS),
      admit = "UnifyNativeFanstopV2NobidBS_Admit",
      compute = "UnifyNativeFanstopV2NobidBS_Compute")\
    .run_price_udf_factor(
      name = "Unify_FanstopPrivateMessageLeadsDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.FanstopPrivateMessageLeadsDiscount),
      admit = "FanstopPrivateMessageLeadsDiscount_Admit",
      compute = "FanstopPrivateMessageLeadsDiscount_Compute") \
    .run_price_udf_factor(
      name = "Unify_InnerProductEEPriceDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerProductEEPriceDiscount),
      admit = "InnerProductEEPriceDiscount_Admit",
      compute = "InnerProductEEPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_MatrixAppPriceDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.NormalPriceRatio),
      rank_factor_type = get_enum(PriceTag.MatrixAppPriceDiscount),
      admit = "MatrixAppPriceDiscount_Admit",
      compute = "MatrixAppPriceDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_InnerAdxEEOrientationDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerAdxEEOrientationDiscount),
      admit = "InnerAdxEEOrientationDiscount_Admit",
      compute = "InnerAdxEEOrientationDiscount_Compute")\
    .run_price_udf_factor(
      name = "Unify_AdjustPricePrivateMessage",
      item_table =  "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustPricePrivateMessage),
      admit = "AdjustPricePrivateMessage_Admit",
      compute = "AdjustPricePrivateMessage_Compute") \
    .run_price_udf_factor(
      name = "Unify_AdjustPriceSimplePromotion",
      item_table =  "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustPriceSimplePromotion),
      admit = "AdjustPriceSimplePromotion_Admit",
      compute = "AdjustPriceSimplePromotion_Compute") \
    .run_price_udf_factor(
      name = "Unify_OuterLivePriceDiscount",
      item_table =  "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.OuterLivePriceDiscount),
      admit = "OuterLivePriceDiscount_Admit",
      compute = "OuterLivePriceDiscount_Compute") \
    .run_price_udf_factor(
      name = "Unify_AdjustIncentivePrice",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustIncentivePrice),
      admit = "AdjustIncentivePrice_Admit",
      compute = "AdjustIncentivePrice_Compute") \
    .run_price_udf_factor(
      name = "Unify_AdjustExplorePrice",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdjustExplorePrice),
      admit = "AdjustExplorePrice_Admit",
      compute = "AdjustExplorePrice_Compute") \
    .run_price_udf_factor(
      name = "Unify_AdStorewideUplift",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdStorewideUplift),
      admit = "AdStorewideUplift_Admit",
      compute = "AdStorewideUplift_Compute") \
    .run_price_udf_factor(
      name = "Unify_AdStorewideMerchantUplift",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.AdStorewideMerchantUplift),
      admit = "AdStorewideMerchantUplift_Admit",
      compute = "AdStorewideMerchantUplift_Compute") \
    .run_price_udf_factor(
      name = "AdjustPriceInnerLoopBigCardSecondRequest",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerLoopBigCard),
      admit = "AdjustPriceInnerLoopBigCardSecondRequest_Admit",
      compute = "AdjustPriceInnerLoopBigCardSecondRequest_Compute") \
    .run_price_udf_factor(
      name = "Unify_FictionHotBookDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.FictionHotBookDiscount),
      admit = "FictionHotBookDiscount_Admit",
      compute = "FictionHotBookDiscount_Compute") \
    .run_price_udf_factor(
      name = "Unify_InnerHighQualityDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerHighQualityDiscount),
      admit = "InnerHighQualityPrice_Admit",
      compute = "InnerHighQualityPrice_Compute") \
    .run_price_udf_factor(
      name = "Unify_InnerNewGuardNDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerNewGuardNDiscount),
      admit = "InnerNewGuardN_Admit",
      compute = "InnerNewGuardN_Compute") \
    .run_price_udf_factor(
      name = "Unify_InnerCtcvrCaliDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerCtcvrCaliDiscount),
      admit = "InnerCtcvrCaliPrice_Admit",
      compute = "InnerCtcvrCaliPrice_Compute") \
    .run_price_udf_factor(
      name = "Unify_InnerNewOrderDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerNewOrderDiscount),
      admit = "InnerNewOrderDiscount_Admit",
      compute = "InnerNewOrderDiscount_Compute") \
    .run_price_udf_factor(
      name = "Unify_ItemCardSelectedFlowDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.ItemCardSelectedFlowDiscount),
      admit = "ItemCardSelectedFlowDiscount_Admit",
      compute = "ItemCardSelectedFlowDiscount_Compute") \
    .run_price_udf_factor(
      name = "Unify_InnerLivePrice",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.InnerLivePrice),
      admit = "InnerLivePrice_Admit",
      compute = "InnerLivePrice_Compute") \
    .run_price_udf_factor(
      name = "Unify_StorewideLowClevelDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.StorewideLowClevelDiscount),
      admit = "StorewideLowClevelDiscount_Admit",
      compute = "StorewideLowClevelDiscount_Compute") \
    .run_price_udf_factor(
      name = "Unify_NewTopkPriceDiscount",
      item_table = "front_unify_item_table",
      formula_type = get_enum(Formula.UnifyPriceRatio),
      rank_factor_type = get_enum(PriceTag.ColdStartPrice),
      admit = "AdjustPriceNewTopk_Admit",
      compute = "AdjustPriceNewTopk_Compute")
    return self

