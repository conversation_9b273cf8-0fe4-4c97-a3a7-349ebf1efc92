from enum import Enum, unique

@unique
class SuppressHCTag(Enum):
  UnknownSuppressTag = 0
  S_DiversityExplore = 1
  S_SoftFreqDiversity = 2
  S_IndustryDiversity = 3
  S_OriginalPhoto = 4
  S_Coldstart = 5
  S_JumpOutRatio = 6
  S_PecCoupon = 7
  S_InpsireQualityAudience = 8
  S_OuterloopNcMax = 9
  S_OuterOverIssue = 10
  S_JumpOutRate = 11
  S_Ntr = 12
  S_BrandFanstopR3 = 13
  S_RelativeSuppress = 14
  S_EspRefundSuppress = 15
  S_AdSensitiveUserGroupRctExp = 16
  S_UeScore = 17
  S_ExternalJump = 18
@unique
class TrafficHCTag(Enum):
  UnknownTrafficTag = 0
  T_Experience = 1
  T_RND = 2
  T_RewardedCoinTransfer = 3
  T_WanheSupport = 5
  T_FanstopAdBoost = 6
  T_DiversityExplore = 7
  T_IndustryDiversity = 8
  T_UEQ = 9
  T_HcFanstopPxtr = 10
  T_SoftFreqDiversity = 11
  T_InnerSelfBoost = 12
  T_ZhugongEcpc = 13
  T_BrandLevelBoost = 14
  T_DoubleEndBoost = 15
  T_OuterHighCostBoost = 16
  T_QualityScoreBoost = 17
  T_WGroupUEQ = 18
  T_TIE = 19
  T_HighPvrForce = 20
  T_UeScore = 21
  T_LongValueAutoParam = 22
  T_CalcPayskitSupportHc = 23
  T_AdUserExperience = 24
  T_TrafficHcHoldout = 25
  T_JumpOutRate = 26
  T_InnerExploreTriggleOpt = 27
  T_HealthScore = 28
  T_Ntr = 29
  T_PostGpm = 30
  T_UeExplore = 31
  T_EspRefundSupport = 32
  T_AdSensitiveUserGroupRctExp = 33
  T_ExternalJump = 34
@unique
class IndustryHCTag(Enum):
  UnknownIndustryTag = 0
  I_ClueHealth = 1
  I_GPM = 2
  I_IndustryExplore = 3
  I_InnerIndustryExplore = 4
  I_SocialIndustryExplore = 5
  I_SplashInnerLoopSupport = 6
  I_GameExploreHighLtv = 7
  I_StorewideRoi = 8
  I_Realtime = 9
  I_NdayRefund = 10
  I_DirectMerchantExplore = 11
  I_EeSearchExplore = 12
  I_NewCustomerGrowth = 13
  I_KeywordTarget = 14
  I_DuanjuHighLtv = 15
  I_ColorCrowd = 16
  I_QualityRefund = 17
  I_NewInnerFanstopHc = 18
  I_DuanJuIndustryExplore = 19  # 短剧行业人群探索
  I_WeChatGameIndustryExplore = 20  # 小游戏人群探索
  I_JiaoTongIndustryExplore = 21  # 交通行业人群探索
  I_BrandCrowd = 22
  I_HcClueAcquisition = 23  #  线索有效获客
  I_OriginalPhoto = 24  # 原创视频
  I_ChuanqiGameIndustryExplore = 25  # 传奇游戏人群探索
  I_IndustryHighLtv = 26
  I_RealestateIndustryExplore = 27  # 房地产行业人群探索
  I_GameLongValue = 28   # 游戏长期价值探索
  I_AccountBiddingStable = 29  # 账户稳定性
  I_Coldstart = 30
  I_IndustryPolaris = 31  # 行业北极星
  I_MerchantPlatformGpm = 32   # 综合平台行业 gpm hc
  I_NewSpu = 33  # 新品 hc
  I_AdxShop = 34  # adx 扩品 hc
  I_AudienceExploreHc = 35  # 三方人群探索
  I_DuanjuUncertaintyExplore = 36   # 短剧不确定度 hc
  I_SdpaItemExplore = 37   # 商品库爆款商品 hc
  I_KeywordExplore = 38   # 关键词人群探索 hc
  I_FanstopRecruit = 39    # 粉条快招工 hc
  I_DuanjuRndExplore = 40   # 短剧 rnd hc
  I_BrandCrowdR2 = 41          # 品牌 R2 hc
  I_GameMtInstallHc = 42     # 游戏安装列表 hc
  I_FanstopCostDiscount = 43  # 粉条成本折扣 hc
  I_FanstopFollowRecall = 44  #粉条粉丝 boost
  I_LocallifeLiveGmvHc = 45    # 本地生活 GMV hc
  I_IndustryQualityScore = 46  # 行业质量分
  I_Game30dLtvHc = 47    # 游戏 30 日 LTV hc
  I_ClueSupportHc = 48   # 线索行业反哺计划 hc
  I_EspFollow7dLtvHc = 49    # 涨粉 7 日 LTV hc
  I_SmbHc = 50    # 内循环中小 hdc
  I_DuanjuRelativeHc = 51    # 短剧相关性 hc
  I_TongxinExporeHc = 52    # 通信开卡率探索 hc
  I_KuaiXiaoIndustryHc = 53    # 快消行业 hc
  I_FanstopHighValueUserHc = 54    # 秀场主播扶持 hc
  I_GameColdStartProductHC = 55    # 游戏扶持产品最优用户分配 hc
  I_PlayletForceHc = 56    # 短剧强出 hc
  I_PlayletClkHC = 57    # 短剧点击模型 hc
  I_GYLItemCardHc = 58    # 猜喜明投 hc
  I_FanstopWhiteListAccountHc = 59  # 粉调白名单扶持 HC
  I_FanstopUATagMatchingHc = 60     # 粉条 User Author tag HC
  I_LSPHc = 61  # 磁力本地推 HC
  I_FanstopUAFollow = 62            # 粉条已关流量 HC
  I_PhotoStorewideHC = 63            # 商品全站 HC
  I_MerchantInterestHc = 64  # 电商兴趣 HC
  I_OutloopRecruitUserPackageHc = 65  # 外循环招工 HC
  I_FanstopPDDAdConversionHc = 66  # 粉条 PDD 激活 HC
  I_GameRecoInterestHC = 67   # 游戏自然流量兴趣 HC
  I_TongxinCallCardHc = 68 # 通信号卡 HC
  I_OuterLoopDncOptHc = 69 # 外循环 DNC 优化 HC
  I_OuterLoopNcUserHC = 70 # 外循环 nc 潜在用户 HC
  I_HcClueRetarget = 71 # 线索重定向 HC
  I_SpringFestivalHealthHC = 72 # 春节不打烊大健康 HC
  I_UalIndustryHc = 73          # ual 平滑冷启动 hc
  I_CalcBrandPromisingR3HC = 74 # 品牌助推种草目标 HC
  I_PhotoEspAigcHC = 75 # 内循环 AIGC HC
  I_HotProductHc = 76 # 内循环爆品 HC
  I_CalcLiveBigRGameHc = 77 # 直播大 R 用户 扶持游戏行业 HC
  I_OuterLoopDncMaxHc = 78 # 外循环 DNC 最大化排序 HC
  I_OuterLoopCrossIndEnhanceHc = 80 # 外循环跨行业兴趣强化 HC
  I_MiniGameI2IEnhanceHc = 81 # 小游戏 I2I 强化 HC
  I_NewPhotoIndustryHc = 82 # 新素材冷却 平滑冷启动 hc
  I_UaxIndustryHc = 83 # uax unify hc
  I_NcTopAuthorHc = 84 # nc 高占比 author hc
  I_MacHc = 85 # 内循环 MAC HC
  I_EspNewSpuHc = 86 # 金牛新品 hc
  I_InnerHighPriceHc = 87 # 内循环高价品 hc
  I_OuterLoopTopnHc = 88 # 外循环 topn 提高 HC 实验
  I_RetargetBranchHC = 89 # 内循环重召回通路 hc
  I_T7NewCustomerHC = 90 # 内循环 T7 ROI 新客 hc
  I_OuterNcInterestExploreHC = 91 # 外循环 dnc 外部数据兴趣探索
  I_CalcPayskitHc = 92 # 短剧行业外转内循环补贴hc
  I_InnerItemTestHc = 93 # 内循环跑品通补 hc
  I_EduClueDeepConvHC = 94 # 教育行业超深度线索优化 HC 
  I_MacChannleHc = 95  # 内循环 MAC HC
  I_InnerHc = 96  # 内循环 HC
  I_InnerSsHc = 97  # 内循环自助 HC
  I_FanstopMessageLeadsHc = 98  # 粉条私信线索 HC
  I_PhotoItemPriceHC = 99  # 商品价格 hc
  I_DspLiveCtrHc = 100  # 外循环直播 ctr HC
  I_IndustryHcHoldout = 101  # 行业 hc holdout
  I_OuterloopUniverseRetargetHC = 102  # 外循环联盟搜索重定向 HC
  I_GYLOrderPaiedHC = 103  # 猜喜订单支付 HC
  I_InnerRetargetBoostHc = 104  # 内循环重定向人群 boost HC
  I_OuterLoopDncCemHc = 105  # 外循环 DNC 策略统合 HC 
  I_InnerAudienctHc = 106  # 内循环进人 HC
  I_InnerRetargetTagHc = 107  # 内循环全域重定向 tag HC
  I_AIMediaStayHc = 108  # 外循环传媒行业AI产品次留优化 HC
  I_InnerHighPriceLikeVHc = 109 # 类V高价品 hc
  I_HealthIndustryRetargetHc = 110  # 大健康行业重定向 HC
  I_OuterAllocationOptHc = 111   # 外循环效率 HC
  I_BrandSpuGrassHc = 112  # 品牌助推商品种草 HC
  I_P2lHighCtrHc = 113  # 引流高 ctr HC
  I_InnerSsOpmHc = 114  # 内循环自助 OPM HC
  I_InnerHighPriceLikeVHcV2 = 115 # 类V高价品 hc v2
  I_FanstopPhoneCallHc = 116 # 粉条电话拨打 hc
  I_InnerP2lColdStartHighPriceHc = 117 # 短引冷启动高价品 hc
  I_InnerLiveColdStarFansHc = 118 # 直投直播冷启动粉丝 hc
  I_OuterloopMacCemHc = 119 # 外循环 MAC 策略统一 HC
  I_ShelfHotItemHc = 120  # 货架爆品 HC
  I_OuterNcPrerankModelHc = 121 # 外循环 DNC 粗排模型 HC
  I_OuterMacPrerankModelHc = 122 # 外循环 DAC 粗排模型 HC
  I_FanstopExtraDeliveryHc = 123  # 粉条浅带深 hc
  I_BrandFanstopOptHc = 124  # 品牌助推效果优化 HC
  I_InnerHUserSpuHc = 125 # 内循环高价品 hc
  I_ShelfProductPriceHc = 126 # 货架低价商品 HC
  I_OuterloopIndDncHc = 127 # 外循环行业首转 HC
  I_OuterloopContentConsumptionHc = 129 # 外循环内容消费冲量 HC
  I_FanstopNewUserHc = 130  # 粉条新客下单页 HC
  I_InnerShelfLiveRetargetFSHc = 131  # 内循环重货架直播卡粉丝召回 HC
  I_InnerShelfLiveRetargetRecallHc = 132  # 内循环重货架直播卡重定向召回 HC
  I_ShelfP2lHc = 133  # 货架短引 hc
  I_NewCusHc = 134  # 新客 hc
  I_OuterloopCCIndAcHc = 135  # 外循环内容消费行业复购 HC
  I_OuterMiniGameIaaRoi7Hc = 136 # 游戏 iaa 7r HC
  I_SmbColdstartHc = 137  # 中小冷启动 HC
  I_IndustryLocalLifeHC = 138  # 本地人群分层 HC
  I_ShelfUserProdPriceHc = 139  # 货架流量 人-货-价多档位 HC
  I_InnerGPMUEHC = 140  # 内循环 GPM UE
  I_OuterMiniGameIaaIpuHc = 141  # 游戏 iaa ipu HC
  I_OuterBigGameRoi7Hc = 143 # 大游7r HC
  I_MerchantHighCtrHC = 144  # 商品高 ctr HC
  I_OuterWentouNewCustomerHc = 145  # 外循环省心投新客 HC
@unique
class OtherHCTag(Enum):
  UnknownOtherTag = 0
  O_SearchReTargetSupport = 1
  O_ModelExplore = 2  # 模型主动探索
  O_BigPromotionGpm = 3  #  大促 gpm hc
  O_SpuReTargetSupport = 4  #  Spu 重定向
  O_PecCoupon = 5  #  Pec 券
  O_ShoufaCpm = 6  #  首发 cpm hc
  O_NTR = 7  #  ntr
  O_OtherHcHoldout = 8  # 均匀补贴留反实验
  O_JumpOutRate = 9 # 外跳率
  O_TIELongTerm = 10 # TIE 长期探索
@unique
class CustomerHCTag(Enum):
  UnknownOtherTag = 0
  C_DemoHC = 1
  C_RndPhotoColdStart = 2
  C_TargetCostPro = 3
  C_PhotoTargetCostPro = 4
  C_OuterNewPhoto = 5
  C_PhotoTargetCostProCbo = 6
  C_TargetCostProCbo = 7

@unique
class PriceTag(Enum):
  UnknownOtherPriceTag = -1
  GspPrice = 0
  GfpPrice = 1 
  BillingSeperate = 2
  RevenueOptimize = 3 
  AuthorDiscount = 4
  Merchant = 5
  MinCpm = 6
  Minbid = 7 
  SiteDiy = 8 
  MaxPrice = 9 
  PriceDiscount = 10
  ProbOne = 11
  ProbZero = 12
  Bottom = 13
  RetargetPrice = 14
  OldPriceSeparate = 15
  Esp = 16
  PriceSeparate = 17
  UnifyBillingRatio = 18
  BadPhotoTax = 19
  RewardedRevenueOptimize = 20
  BillingSeparateGfpPrice = 21
  FollowPrice = 22
  InspireLiveFeedPrice = 23
  InspireMerchantPrice = 24
  SmallGamePrice = 25
  MinPriceProtection = 26
  ADXPrice = 27
  MinAuctionBid = 28
  RetargetPriceDiscount = 29
  RewardedPriceDiscount = 30
  LowFilled = 31
  OcpcMaxThr = 32
  NativeMaxBound = 33
  MinThrPrice = 34
  MaxThrPrice = 35
  CpmProtection = 36
  ProtectPrice = 37
  OriginAuctionFinal = 38
  AuctionFinal = 39
  PostProcFinal = 40
  AddReservePrice = 41
  PecCoinEffect = 42
  PriceWithTax = 43
  RecruitPriceDiscount = 44
  IndustryLivePrice = 45
  MobileDiscount = 46
  NobidProtect = 47
  LiveSelectedPrice = 48
  FtFairnessCorrectionV1 = 49
  FtFairnessCorrectionV2 = 50
  FanstopBillingSeperate = 51
  CidPriceDiscount = 52
  MiddlePagePriceDiscount = 53
  NativeOuterPriceDiscount = 54
  JuxingPrice = 55
  FollowMaxPrice = 56
  FanstopCostDiscountExp = 57
  InnerPriceDiscount = 58
  AdMerchantPriceDiscount = 60
  LpsdeepPriceDiscount = 61
  QudaoCluesPriceDiscount = 62
  SmbPriceDiscount = 63
  FanstopAuctionBidPrice = 64
  WanheMaxPrice = 65
  FanstopFollowPrice = 66
  GuessYouLikePriceDiscount = 67
  MaxBound = 68
  MinBound = 69
  ExploreInnerPrice = 70
  ClientAiLiveAdRerankPrice = 71
  ClientAiP0RerankPrice = 72
  SelfServicePriceDiscount = 73
  NewProductDiscount = 74
  PoQuanPriceDiscount = 75
  ReservePrice = 76
  GameStabilityDiscount = 77
  CidPriceDiscountV3 = 78
  UnifyHardBillingSeparate = 79
  UnifyNativeBS = 80
  UnifyNativeFanstopV2NobidBS = 81
  FanstopPrivateMessageLeadsDiscount = 82
  AdjustPricePrivateMessage = 83
  AdjustExplorePrice = 84
  InnerProductEEPriceDiscount = 85
  AdStorewideUplift = 86
  AdjustPriceSimplePromotion = 87
  InnerWhiteAccountDiscount = 88
  InnerT7RoiDiscount = 89
  AdStorewideMerchantUplift = 90
  IAAPGameROIDiscount = 91
  GameBigRExplore = 92
  NewSpuPriceDiscount = 93
  AdjustPricePrivateMessageGroup = 94
  AdjustIncentivePrice = 95
  UpItemsPrice = 96
  InnerCrmEEPriceDiscount = 97
  InnerSouthEEPriceDiscount = 98
  NewCustomerHostingDiscount = 99
  InnerLoopBigCard = 100
  MatrixAppPriceDiscount = 101
  StoreWideLiveWhiteListDiscount = 102
  StoreWideMerchantWhiteListDiscount = 103
  KuaiGameInspireDiscount = 104
  InnerTieEEPriceDiscount = 105
  FictionHotBookDiscount = 106
  NewSpuCrowdDiscount = 107
  ItemCardSelectedFlowDiscount = 108
  ColdStartPrice = 109
  LocalLifePriceDiscount = 110
  InnerHighValuePrice = 111
  OuterLivePriceDiscount = 112
  InnerNewOrderDiscount = 113
  DuanjuNativeOuterPriceDiscount = 114
  DuanjuPriceDiscount = 115
  InnerLivePrice = 116
  LowTrIndustryDiscount = 117
  InnerAdxEEOrientationDiscount = 118
  ClientAiPhotoAdRerankPrice = 119
  StorewideLowClevelDiscount = 120
  InnerHighQualityDiscount = 121
  InnerCtcvrCaliDiscount = 122
  InnerNewGuardNDiscount = 123
  NormalOriginCalcPriceDragon = 10000
  NativeOriginCalcPriceDragon = 10001
  UnifyOriginCalcPriceDragon = 10002

@unique
class BonusCpmTag(Enum):
  GLOBAL_BONUS_CPM = 0  # 全局 BonusCpm 配置
  NEW_CREATIVE_FEED_TAG = 1  # 新创意瀑布流 pbsu 设置
  NEW_CREATIVE_COVER_TAG = 2  # 新创意封面 BonusCpm
  NEW_CREATIVE_PHOTO_TAG = 3  # 新素材封面 BonusCpm
  NEW_SDK_TAG = 4  # sdk BonusCpm
  NEW_FIRST_INDUSTRY_TAG = 5  # 一级行业 BonusCpm;废弃代码已删除;bixiaodong;信息流;所有广告
  NEW_UNIT_TAG = 6  # unit 粒度 BonusCpm
  NEW_ACCOUNT_TAG = 7  # 账户粒度 BonusCpm
  NEW_WHITE_LIST_TAG = 8  # 白名单 BonusCpm
  NEW_GREEN_HARD_CPM_TAG = 9  # 绿色通道 BonusCpm    [相关逻辑已下线]
  NEW_CTR_CVR_BEST_TAG = 11  # ctr*cvr 流量优选
  NEW_PID_BONUS_TAG = 12  # 可配置 bonus
  NEW_CREATIVE_PRERANK_ADJUST_RATIO = 13  # 不清楚;废弃代码已删除;bixiaodong;信息流;所有广告
  NEW_GAME_HARD_CPM_TAG = 14  # 新游戏首发
  NEW_CREATIVE_HELP_EXPLORE_TAG = 15  # 新创意辅助探索 bonus
  CORPORATION_BONUS_CPM = 17  # 给特定扶持营业执照的 bonus
  REWARD_VIDEO_BONUS_CPM = 18  # 极速版奖励视频流量为 bonus
  THIRD_PARTY_PAID_AUDIENCE_BONUS_CPM = 19  # 三方付费人群 bonus
  NEW_CPA_BID_ADJUST_BONUS_CPM = 20  # 跟 cpa_bid 相关的 bonus
  FIRST_N_CPM_BONUS_TAG = 21  # 前 n 刷必保策略 cpm bonus
  RETENTION_TWIN_BONUS = 22    # 次留双出价扶持 bonus     [相关逻辑已下线]
  ECON_BONUS_CPM = 23    #电商行业直营电商 bonusCpm 策略
  MERCHANT_BONUS_CPM = 24    #电商行业小店 bonusCpm 策略
  MERCHANT_BONUS_CPM_NEW_ITEM = 25    #电商行业新品 bonusCpm 策略
  XDT_HARD_BONUS_TAG = 26    #小店通绿通广告硬补贴策略
  XDT_CPM_ADJUST_BONUS_TAG = 27     #小店通绿通广告根据 cpm 自调节补贴策略
  ECON_NEW_ITEM_BONUS_CPM = 28     #直营电商新品 bonusCpm 策略
  ECON_ITEM_PUNISH = 29      #直营电商特定商品打压 bonusCpm 策略
  ECON_ITEM_PUNISH_ALL = 30      #直营电商商品 bonusCpm 打压策略
  MERCHANT_ACCOUNT_BONUS_CPM = 31       #小店账户 bonusCpm 策略
  MERCHANT_ACCOUNT_PUNISH = 32     #小店特定账户 bonusCpm 打压策略
  MERCHANT_ACCOUNT_PUNISH_ALL = 33      #小店账户 bonusCpm 打压策略
  MERCHANT_UNIT_DROP_BONUS_CPM = 34      #电商行业直营电商掉量 bonusCpm 策略
  ECOM_RECENT_PVR_BONUS = 35                # 电商 recent pvr bonus 策略
  ADVERSITER_CPM_BONUS_RATIO = 36         # cpm ratio 进行 bonus 激励策略
  ACCOUNT_BONUS_FLOW_TAG = 37             # 小店通补贴 pacing 标签
  SMB_HARD_CPM_TAG = 38         # 本地行业进行 bonus 激励策略
  XDT_LIVE_HARD_BONUS_TAG = 39        # 小店直播推广绿通硬补贴      [相关逻辑已下线]
  XDT_LIVE_ADJUST_BONUS_TAG = 40       # 小店直播推广 bonusCpm 自调节目
  NEW_ACCOUNT_CTR_CVR_BEST_TAG = 41  # account ctr*cvr 流量优选
  NEW_ADVERTISER_CPA_BID_BONUS_CPM = 42  # 针对广告主的 cpa bid bonus
  KNEWS_BONUS_TAG = 43   # 快看点明投广告 bonus
  NEW_GAME_BID_CPM_TAG = 44  # 游戏首发按照 bid 方式调整 bonus 策略       [相关逻辑已下线]
  MERCHANT_NEW_CREATIVE_BONUS = 45  # 小店通新创意 bonus 策略
  OCPC_ACTION_TYPE_BONUS = 46
  SUPPORT_PROJECT_BONUS_TAG = 47  # 起量工具 bonus 策略
  OCPC_ACTION_TYPE_BONUS_BY_RATIO = 48  #   [相关逻辑已下线]
  SPRING_FESTIVAL_BONUS_TAG = 49  # 春节流量补贴 bonus 策略
  INDUSTRY_NEW_CREATIVE_BONUS_TAG = 50  # 行业新创意 cpm bonus 策略;wanghongzhi;激励视频;adx+dsp
  REWARDED_BONUS_TAG = 51   # 激励视频 bonus
  PLUMMENT_BONUS_TAG = 52   # 衰减广告的 bonus 策略
  WECHAT_BONUS_TAG = 53      # 快手微信小程序 bonus 策略
  PTDS_RQ_SEARCH_TAG = 54  #   外循环电商人群探索 bonus 策略       [相关逻辑已下线]
  NEW_CREATIVE_CTR_CVR_BEST_TAG = 55  #  creative ctr*cvr 流量优选
  NEW_CREATIVE_SMOOTH_EXPLORE_TAG = 56    # 冷启动平滑探索 策略
  BACKFLOW_START_BONUS_TAG = 57  #  回流预估单元起步阶段 策略
  COMMON_EXPLORE_TAG = 58    # 探索补贴策略
  XDT_LIVE_RECENT_PVR_BONUS = 59    # 小店通直播 recent pvr bonus 策略
  INSPIRE_RESET_BONUS_TAG = 60      # 激励视频取消补贴策略;xuxu;激励视频广告
  PTDS_BONUS_DROP_TAG = 61     #  外循环电商衰减补贴标记       [相关逻辑已下线]
  INDUSTRY_AUDIENCE_BONUS_TAG = 62  # 联盟 行业-人群 bonusCpm
  LAHUO_BONUS_TAG = 63  # 拉活 bonusCpm 为 0
  PTDS_BONUS_NEW_TAG = 64  #  [相关逻辑已下线]
  COMP_FORWARDLINK_BONUS_TAG = 65   # 外循环电商前链路重复曝光补贴
  EXT_MERCHANT_BONUS_TAG = 66   # 商家投放 bonus 策略      [相关逻辑已下线]
  BONUS_EXCEPTION_TAG = 67    #  异常 bonus 处理 tag
  HIGH_QUALITY_BONUS_TAG = 68    #  优质素材 bous 策略
  FANS_TOP_BONUS = 69  # 软广运营补贴需求
  SPARK_CUSTOMER_BONUS_TAG = 70    #  星火专项腰尾部客户 bonus 策略             [相关逻辑已下线]
  SEARCH_BIDWORD_BONUS_TAG = 71   # 搜索广告明投 bonus 策略
  SEARCH_HIGH_RELEVANCE_BONUS_TAG = 72   # 搜索广告明投 bonus 策略            [相关逻辑已下线]
  CAR_CAMPANY_CPM_BONUS_TAG = 73     # 汽车厂商 bonus 扶持策略            [相关逻辑已下线]
  FLOW_LAYER_DIVIDE_TAG = 74   # 流量分层无 bonus 流量层
  AMD_FLOW_LAYER_DIVIDE_TAG = 75   # amd 流量分层无 bonus 流量层
  LOW_QUALITY_BONUS_TAG = 76   # 劣质广告 bonus 策略
  ACCOUNT_ADJUST_TAG = 77  # 根据账户性质 bonus 调节            [相关逻辑已下线]
  AD_HOSTING_BONUS_TAG = 78  # 托管广告 bonus 策略            [相关逻辑已下线]
  BOYlE_PROJECT_CPM_BONUS_TAG = 79  # 波义耳项目 bonus 策略             [相关逻辑已下线]
  AUCTION_WITHOUT_BONUS_TAG = 80    # 全局去除 bonus
  EXT_MERCHNAT_DELETE_BONUS_TAG = 81    # 商家外投清理 bonus
  SEARCH_AMD_DEFAULT_BONUS_TAG = 82    # 搜索广告电商默认 bonus 策略
  PHOTO_DIMENSION_BONUS_TAG = 83    # photo 维度扶持策略             [相关逻辑已下线]
  BCB_BONUS_TAG = 84    # 补贴框架 bonus
  SEARCH_LIVE_INTENTION_BONUS_TAG = 85    # 搜索广告直播意图 bonus 策略
  FANS_ESP_MOBILE_TAG = 86    # 随心推补贴              [相关逻辑已下线]
  FANS_POTENTIAL_ANCHOR_TAG = 87   # 潜力主播补贴
  IDLE_TRAFFIC_EXPLORATION_BONUS_TAG = 88  # 空闲人群探索 bonus 策略
  RETARGET_CPM_BONUS_TAG = 89          #重定向补贴策略              [相关逻辑已下线]
  SEARCH_CELEBRITY_BONUS_TAG = 90   # 搜索广告大 V 卡 bonus 策略
  AUTHOR_QUALITY_BONUS_TAG = 91    # 电商短视频广告商家质量评分 bonus 策略              [相关逻辑已下线]
  LOW_QUALITY_PHOTO_BONUS_TAG = 92   # 低质视频 bonus 为 0
  SEARCH_GOOD_TAB_BONUS_TAG = 93    #  搜索广告购物 tab 页面 bonus 策略
  ECOM_SELLER_BONUS = 94          # 优质商家补贴
  MMU_HIGH_QUALITY_BONUS_TAG = 95        # MMU 优质素材补贴
  HIGH_SHOP_QUALITY_BONUS_TAG = 96       # 商品质量分高分补贴                [相关逻辑已下线]
  LONG_TERM_VALUE_ECPM_BONUS = 97        # 长期价值模型补贴
  PRIZE_WINNING_PHOTO_BONUS = 98        # 爆量素材扶持
  BRAND_NEW_PHOTO_BONUS_TAG = 99    # 电商短视频广告品牌客户冷启动补贴
  BRAND_MATURE_PHOTO_BONUS_TAG = 100    # 电商短视频广告品牌客户成熟期补贴
  BCB_BONUS_TAG_NO_ACCOUNT = 101    #  外循环 补贴框架非圈 account 补贴 tag
  SEARCH_LIVE_EXPLAIN_BONUS_TAG = 102    #  搜索广告直播正在讲解 bonus 策略              [相关逻辑已下线]
  INDUSTRY_LIVE_COLD_BONUS_TAG = 103
  INNERLOOP_PROJECT_BONUS_TAG = 104   # 内循环 project bonuss
  PHOTO_A_QUALITY_BONUS_TAG = 105   #  外循环 photo A 标准补贴
  UNIVERSE_INDUSTRY_ROI_CPM_BONUS = 106  # 联盟基于 roi 的 bonuscpm
  UNIVERSE_NEW_CREATIVE_BONUS_TAG = 107  # 联盟新创意 bounus
  UNIVERSE_RETARGET_BONUS_TAG = 108  # 联盟重定向补贴
  UNIVERSE_INNER_LOOP_AUDIENCE_BONUS_TAG = 109  # 联盟内循环人群触达策略 bonusCpm
  UNIVERSE_IAA_PRODUCT_BONUS_TAG = 110  # 联盟 iaa 产品 bonusCpm
  PHOTO_A_QUALITY_FRAME_BONUS_TAG = 111   #  外循环 photo A 标准补贴
  INNER_REWARDED_MINGTOU_BONUS_TAG = 113  # 内循环激励视频明投补贴
  INNER_REWARDED_LIVE_MINGTOU_BONUS_TAG = 114  # 激励直播明投补贴
  ECOM_ORIGINAL_PHOTO_BONUS_TAG = 115  # 内循环短视频电商原创素材补贴;wanghongfei;短视频带货;短视频带货广告
  PLATFORM_NEW_PRODUCT_BONUS = 116  # 内循环短视频电商平台新品补贴 chencongzheng
  ECOM_ORIGINAL_MATURE_PHOTO_BONUS_TAG = 117  # 内循环短视频电商原创素材成熟期补贴;wanghongfei
  ECOM_DUP_PHOTO_REVOKE_BONUS_TAG = 118  # 内循环短视频电商原创素材抄袭取消补贴;wanghongfei
  ECOM_AUTHOR_BONUS_TAG = 119  # 内循环短视频电商经营商家补贴;wanghongfei
  SEARCH_INNER_BONUS_TAG = 120  # 搜索广告内循环 bonus
  PLUS_NEW_CUSTOMER_BONUS_TAG = 121  # plus 新手任务常驻补贴任务 bonus tag
  ECOM_AD_FANS_BONUS_TAG = 122  # 商业化带来粉丝重触达补贴;lishaozhe
  FANSOP_RECRUIT_LIVE_BONUS_TAG = 123  # 招聘直播间补贴;mayanbin
  NEW_MERCHANT_BONUS = 124  # 内循环客增新客补贴;chencongzheng
  LIVE_APPOINTMENT_BONUS_TAG = 125  # 直播预约重触达补贴;fukunyang
  ECOM_L_AUTHOR_BONUS_TAG = 126  # 内循环青云商家补贴;wanghongfei
  NEW_MERCHANT_BONUS_TAG = 127  # 内循环客增新客补贴;chencongzheng
  PEC_BONUS_TAG = 128  # 外循环 pec 样式补贴;xuxu
  PLUS_4M_OLD_CUSTOMER_BONUS = 129    # 外循环 plus 4M 客户补贴; guoqi03
  SEARCH_APP_CARD_BONUS_TAG = 130    # 搜索广告下载卡补贴
  RND_EXPLORE_BONUS_TAG = 131    # rnd 探索补贴 rentingyu
  ACCOUNT_STABLE_BONUS_TAG = 132  # 账户视角稳定性补贴;yushengkai
  POP_RECRUIT_BONUS_TAG = 133  # 外循环磁力快招补贴;maileyi
  INNERLOOP_ACCOUNT_TRANS_BONUS_TAG = 134  # 内循环客户跃迁补贴;chencongzheng
  ACCOUNT_CAMPAIGN_BONUS_TAG = 135  # 流量助推补贴;lining
  INNERLOOP_ACCOUNT_TASK_BONUS_TAG = 136  # 内循环客户任务中心补贴;luwei
  INNERLOOP_ACCOUNT_TASK_BONUS_TAG_BY_LEVEL = 137  # 内循环客户任务中心分层补贴;luwei
  MAX_CONV_BONUS_TAG = 138  # 最大化撬动转化补贴;lining
  NEWLANDS_BONUS_TAG = 139  # 纽兰兹素材扶持策略;liuguoyu
  SEARCH_FORM_CARD_BONUS_TAG = 140  # 搜索广告表单卡补贴
  OUTER_SELF_SERVICE_BONUS_TAG = 141  # 外循环自助项目补贴;tangsiyuan
  BRAND_EFFECTIVE_BONUS_TAG = 142  # 年货节品效追投补贴;lixu05
  FANSTOP_NEW_PHOTO_BONUS_TAG = 143  # 粉条作品冷启动补贴;maileyi
  COLOR_CPM_BONUS_TAG = 144  # 种草实验补贴;cuihongyi
  SEARCH_RETARGET_BONUS_TAG = 145  # 搜索重定向扶持;mengfangyuan
  SPLASH_BOOST_BONUS_TAG = 146  # 开屏起量扶持(内循环);liubing05
  GAME_HIGH_VALUE_EXPLORE = 147  # 游戏高价值人群探索;zhangmengxin
  SPLASH_INNER_AD_BONUS_TAG = 149  # 开屏内循环补贴;liubing05
  SPLASH_OUTER_AD_BONUS_TAG = 150  # 开屏外循环补贴;liubing05
  STOREWIDE_ROI_BONUS_TAG = 151  # 全店 ROI 补贴;shengmingyang
  NEW_CUSTOMER_GROWTH_BONUS_TAG = 152  # 新客增长补贴;cuihongyi
  MIDDLE_PAGE_BOOST_BONUS_TAG = 153  # 电商中间页补贴;liubing05
  SPLASH_BCB_BONUS_TAG = 154    # 开屏补贴框架 bonus
  REWARDED_MINGTOU_TRANSFER_BONUS_TAG = 155  # 激励视频明投补贴迁移;xuxu
  REWARDED_BCB_BONUS_TAG = 157  # 激励视频补贴框架 bonus
  OUTER_LOOP_NATIVE_BONUS_TAG = 158  # 原生素材补贴;lining
  MCB_OPERATION_BONUS_TAG = 159  # MCB 科学投放补贴;wangtao21
  PROPHET_BONUS_TAG = 160  # 先知补贴;lining
  FANSTOP_NEW_PRODUCT_BONUS_TAG = 161  # 粉条新产品补贴;tengwei
  INNERLOOP_FOLLOW_BONUS_TAG = 162  # 短视频涨粉补贴;luwei
  INNERLOOP_AD_ITEM_CLICK_BONUS_TAG = 163  # 短视频预约补贴;luwei
  NEW_DUANJU_BONUS_TAG = 164  # 新剧补贴;lining
  INNERLOOP_PHOTO_ORDER_BONUS_TAG = 165  # 短视频带货补贴;luwei
  SEARCH_RECOMMEND_QUERY_BONUS_TAG = 166  # 搜索导流补贴;niejinlong
  BLUE_SEA_CATEGORY_BONUS_TAG = 167  # 蓝海新品类目补贴;yuanwei09
  AUTHOR_GROWTH_BONUS_TAG = 168  # 客户增长补贴;yushengkai
  BLUE_SEA_CROWD_BONUS_TAG = 169  # 蓝海人群补贴;yuanwei09
  FANSTOP_HEAD_CUSTOMER_BONUS_TAG = 178  # 粉条头客补贴; tangsiyuan
  INNERLOOP_CROWD_BONUS_TAG = 184  # 内循环人群嗅探补贴;wangtao21
  SE_NEW_CUSTOMER_BONUS_TAG = 185  # se 新手任务常驻补贴任务;xuyanyan03
  INNER_CARD_BONUS_TAG = 186       # 内循环商品卡补贴;luwei
  INNER_NATIVE_PHOTO_BONUS_TAG = 187  # 内循环原生素材补贴;luwei
  LSP_BONUS_TAG = 188  # 磁力本地推补贴;cuihongyi
  UAX_BONUS_TAG = 190  # 外循环 UAX 直播计划补贴;renjianlong
  CAOPAN_BONUS_TAG = 191  # 外循环新操盘计划补贴;zhaoziyou
  OUTER_LOOP_HIGHCOST_BONUS_TAG = 189  # 外循环素材跑量 bonus; guojiangwei
  BLUE_SEA_NEW_BONUS_TAG = 192  # 新蓝海补贴;yuanwei09
  AD_MERCHANT_T7_ROI_BONUS_TAG = 193  # roi7补贴;wangtao21
  LPS_CUSTOMER_BONUS_TAG = 194 # 表单辅助事件回传客户补贴;xutaotao03
  UAA_BONUS_TAG = 195  # 外循环 UAA 计划补贴;rongyu03
  UAL_BONUS_TAG = 196  # 外循环 UAL 计划补贴;rongyu03
  ATLAS_BONUS_TAG = 200 # 外循环 图集 补贴;zengdi
  SEARCH_ITEM_KBOX_BONUS_TAG = 201 # 搜索广告商播卡补贴;zhangchaoyi03
  CLUE_MESSAGE_BONUS_TAG = 202 # 线索私信投放补贴;xutaotao03
  SDPA_HIDDEN_BINDING_BOUNS_TAG = 203 # spda 暗绑补贴; zengdi
  CLUE_WECHAT_BONUS_TAG = 205 # 线索企微投放补贴;xutaotao03
  RETENTION_DAYS_BONUS_TAG = 206 # 每日留存补贴;dengrujia03
  PURCHASE_PAY_BONUS_TAG = 207 # 付费 ROI 双出价补贴;dengrujia03
  OUTER_LOOP_COSTCAP_BONUS_TAG = 208 # 外循环 costcap 补贴;zhangtuchao
  FINANCE_DEEP_BUNUS_TAG = 209   #外循环金融行业深度补贴;yuchengyuan
  OUTER_NATIVE_FICTION_BONUS_TAG = 211   #外循环原生小说链路补贴;zhangmengxin
  FANSTOP_PDD_BONUS_TAG = 215    # 粉条 PDD 补贴;tengwei
  INNER_SELF_SERVICE_TAG = 216   # 内循环自助客户补贴;tengwei
  PRODUCT_HOSTING_BONUS_TAG = 217   # 商品托管补贴;lixu05
  PAID_COURSE_BONUS_TAG = 218    # 付费课堂投流补贴;wuwei03
  AIGC_PHOTO_BONUS_TAG = 219 # AIGC 盘古素材补贴;caoxipeng
  AIGC_LIVE_BONUS_TAG = 220 # 直播数字人 女娲素材补贴;caoxipeng
  LOW_QUALITY_PHOTO_WITHOUT_BONUS = 221  # 低质素材无补贴;liubing05
  INNER_STOREWIDE_LIVE_BONUS_TAG = 222  # 全站直播 补贴; tangsiyuan
  INNER_STOREWIDE_PRODUCT_BONUS_TAG = 223  # 全站商品 补贴; tangsiyuan
  INNER_ROI_BONUS_TAG = 224  # 金牛 ROI 补贴; tangsiyuan
  INNER_NEW_CUSTOMER_BONUS_TAG = 225  # 内循环新客增长补贴; tangsiyuan
  INNER_ACC_EXPLORE_V2_BONUS_TAG = 226  # 内循环加速探索 2.0 补贴; tangsiyuan
  INNER_NEW_PC_VERSION_BONUS_TAG = 227  # 内循环PC新版本补贴; tangsiyuan
  INNER_LIVE_HOSTING_BONUS_TAG = 228  # 直播托管 补贴; tangsiyuan
  AD_MERCHANT_T7_ROI_BONUS_V2_TAG = 229  # T7ROI 补贴; lihantong
  INNER_LSP_LEADS_BONUS_TAG = 230         # 内循环线索补贴; tengwei
  PRIVATE_MESSAGE_BONUS_TAG = 231  # 私信非圈 account 补贴; xuyanyan03
  CLUE_BONUS_TAG = 232  # 本地线索补贴; xuyanyan03
  INNER_V_PHOTO_ROI_BONUS_TAG = 233  # 内循环 V 客户作品 ROI补贴; tengwei
  VHIGH_PRICI_BONUS_TAG = 234  # 内循环V客户高客单价补贴; wangkai26
  MINIGAME_INDUSTRY_BONUS_TAG = 235   # 快小游/微小行业补贴;jiangpeng07
  LSP_STOREWIDE_BONUS_TAG = 236       # 本地推全站补贴; tengwei  
  INNER_ITEM_TEST_BONUS_TAG = 238  # 内循环跑品分级补贴; wangkai26
  CLUE_ECOLOGY_BONUS_TAG = 239   # 外循环 本地线索-跑量扶持 [zhangbuang]
  INNER_ITEM_TEST_ALL_BONUS_TAG = 240  # 内循环跑品通用补贴; wangkai26
  INNER_WHITELIST_BONUS_TAG = 241      # 内循环白名单补贴; tengwei
  INNER_P2L_BONUS_TAG = 242      # 内循环短引补贴；liujiahui10
  WECHAT_GAME_BONUS_TAG = 243      # 外循环微小游补贴; nizhihao
  FANSTOP_MESSAGE_LEADS_BONUS_TAG = 244      # 粉条私信&线索表单类客户补贴; linhuihuang
  KUAI_XIAO_IAP_GAME_BONUS_TAG = 245      # 外循环快小游IAP补贴; lichao21
  INNER_AIGC_PHOTO_BONUS_TAG = 246        # 内循环 AIGC PHOTO 补贴; tengwei
  INNER_P2L_PROJECT_BONUS_TAG = 247        # 内循环 P2L 专项补贴; tengwei
  INNER_OLDPRODUCT_RETRY_BONUS_TAG = 248        # 内循环老品复投专项补贴; xiongtianyu
  INNER_RETARGET_BOOST_BONUS_TAG = 249         # 内循环重定向人群 boost 补贴; wangkai26
  PLAYLET_POTENTIAL_AUDIENCES_BONUS_TAG = 250  # 付费短剧潜力受众补贴；liyongchang
  SIMPLE_PROMOTION_BONUS_TAG = 251         # 聚合竞价极简创编补贴; zhaijianwei
  FANSTOP_COMBO_TYPE_BONUS_TAG = 252  # 粉条套餐包补贴; tangsiyuan
  INNER_ITEM_TEST_PROJECT_BONUS_TAG = 353  # 跑品活动补贴； lixu05
  INNER_QCPX_BONUS_TAG = 254                 # QCPX 补贴; tengwei
  AD_MERCHANT_T7_ROI_PHOTO_BONUS_TAG = 255   # 金牛 T7_ROI PHOTO 补贴; tengwei
  INNER_SELF_SERVICE_TC_TAG = 256    # 内循环自助客户补贴;lichunchi
  INNER_SELF_SERVICE_STOREWIDE_TAG = 257    # 内循环自助客户补贴;lichunchi
  FANSTOP_PHONE_CALL_BONUS_TAG = 258    # 粉条400电话拨打类客户补贴; lijun03
  LOCAL_LIFE_PROMOTION_BONUS_TAG = 259      # 本地投补贴; zhaijianwei
  NEW_PRODUCT_BONUS = 260  # 短带新品补贴;lixu05
  SEARCH_BIDWORD_INNER_BONUS_TAG = 261     # 内循环搜后推补贴; haozhitong
  SEARCH_BIDWORD_OUTER_BONUS_TAG = 262     # 外循环搜后推补贴; haozhitong
  PLAYLET_COLD_START_BONUS_TAG = 263     # 短剧新剧冷启动补贴; liyongchang
  PLAYLET_SALES_MATCHING_BONUS_TAG = 264     # 短剧分销撮合平台补贴; liyongchang
  XIAOSHUO_ZHITOU_BONUS_TAG = 265       #UAX 小说智投补贴; caijiawen
  UAX_AIGC_BONUS_TAG = 266       # UAX CID 补贴; renjianlong
  FANSTOP_NEW_PRMS_AND_LEADS_BONUS_TAG = 267  # 粉条私信&线索新账户补贴; cuipeng03
  INNER_AIGC_LIVE_BONUS_TAG = 268   # 内循环直播 AIGC 补贴; tengwei
  AD_DAWN_BONUS_TAG = 269  # 稳投中小补贴; lining
  CID_CORP_NAME_BONUS_TAG = 270  # cid 营业执照定向补贴; lizuxin
  SHELF_ITEM_CARD_WHITELIST_BONUS_TAG = 271  # 货架商品卡白名单客户补贴; wangkai26
  INNER_CID_GREAT_SALE_BONUS_TAG = 272  # cid 大促补贴; lizuxin
  INNER_CID_NEW_PRODUCT_BONUS_TAG = 273  # cid 新品扶持补贴; lizuxin
  AmdEcomBonusStrategyNormal = 10001  # 迁移产物 不要使用
  CalcCpmBonusDragon = 10002  # 迁移产物 不要使用
  NativeUnifyCalcBonusDragon = 10003  # 迁移产物 不要使用
  NormalOriginCalcBonusDragon = 10004  # 迁移产物 不要使用
  NativeOriginCalcBonusDragon = 10005  # 迁移产物 不要使用

@unique
class ServerCientShowRateTag(Enum):
  DefaultServerClientShowRate = 1
  AdxServerClientShowRate = 2
  RecordCpmServerShowRatio = 3
  # ⬆️ server_client_show_rate 策略 tag 在此注册

@unique
class HasDeepCvrTag(Enum):
  Unknown = 0
  HasSetUnifyDeepCvr = 1
  # ⬆️ has_deep_cvr 策略 tag 在此注册

@unique
class EcpmUpperBound(Enum):
  Unknown = 0
  UnifyEcpmUpperBound = 1
  # ⬆️ ecpm_upper_bound 策略 tag 在此注册

@unique
class EcpmLowerBound(Enum):
  Unknown = 0
  UnifyEcpmLowerBound = 1
  # ⬆️ ecpm_lower_bound 策略 tag 在此注册

@unique
class EcpmAdjust(Enum):
  Unknown = 0
  EcpmDeepTwinBid = 1
  # ⬆️ ecpm_adjust 策略 tag 在此注册

@unique
class CpmTag(Enum):
  DefaultOriginCpm = 1
  MergeExploreCpm = 2

@unique
class CpmBoostTag(Enum):
  DefaultCpmBoost = 1
  GuessLikeAmdCategoryPriorityBoost = 2
  PlayletVisitorCalibrate = 3

@unique
class LeverageScoreTag(Enum):
  LeverageScore = 1
  CoinBiasGross = 2
  IncentiveNextValue = 3

@unique
class EcpcAdjustTag(Enum):
  Unknown = 0
  HotLiveBoost = 1
  GameHighValue = 2
  StatsAutoParam = 3
  MtPurchase = 4
  MtLps = 5
  MtCreditGrant = 6
  MtConv = 7
  NovelPurchase = 9
  McdaUpPurchase = 10
  McdaUpRoas = 11
  PurchaseLtv = 12
  TaoxiUp = 13
  LALpsValidClues = 14
  EcomEcpc = 15
  McdaKeyInapp = 16
  InnerIndepImpConv = 17
  RewardedCalibrate = 18
  KnewsColdStart = 19
  EduLesson = 20
  RoiLowPurchaseOrRetention = 21
  McdaUpConvRetention = 22
  McdaUpConvThirdPartyEcpc = 24
  McdaUpPurchaseThirdPartyEcpc = 25
  RewardedMingtou = 26
  McdaUpKeyInAppThirdPartyEcpc = 27
  MerchantPhotoMinus = 28
  MerchantLiveMinus = 29
  InnerRewardedMingTou = 30
  InnerRewardedLiveMingTou = 31
  GameRewarded = 40
  RewardedNotGame = 41
  FanstopRecruitLiveAudienceEcpc = 42
  OuterNativeAppAdvanceDiscount = 43
  FollowPageBoost = 44
  FollowWhiteAuthorBoost = 45
  EspLiveCalibration = 46
  OuterPopRecruitCaliProcess = 47
  NativeSeqAdjust = 48
  OuterDspCaliByConfig = 49
  OlpRetarget = 50
  RewardedAdsKeyInapp = 51
  RewardedAdsRoi = 52
  SdpaUp = 53
  RewardedConversion = 55
  ConvQuality = 56
  WechatSmallGameLowPurchaseDrop = 57
  InvokeEcpcAccount = 58
  # 策略人群 ecpc
  StrategyCrowdEcpc = 59
  # 定向覆盖人数 ecpc
  AudienceNumEcpc = 60
  # 长期价值
  LongTermConvPurEcpc = 61
  # 收入优化
  RevenueOptimize = 62
  # 内循环计费打折定价
  InnerEcpmEcpc = 63
  InnerPriceEnhance = 64
  AdRoasDropTag = 65
  # 游戏长期价值 ecpc 策略
  GameLongValueEcpc = 66
  # 游戏保平台高价值用户 ecpc 策略
  GamePlatformHvEcpc = 67
  # 七留 ecpc 策略
  WeekRetention = 68
  # 短剧人群探索
  DuanjuExplore = 69
  OrderConvTypeEcpc = 70
  FanstopPhotoEcpc = 71
  FanstopNewInnerEcommerceOrderPaidEcpc = 73
  InnerBuyerEcpc = 74
  FanstopWhiteListAuthorEcpc = 75
  FanstopUATagEcpc = 76
  FanstopUAFollowEcpc = 77
  WechatGameUserCaliEcpc = 78
  MerchantNewFansEcpc = 79
  HighQualityPhotoRatioEcpc = 80
  KeyActionCoef = 81
  InvokeEcpc = 82
  KeyActionEcpc = 83
  T7RoiEcpc = 84
  FanstopEcommerceLTVEcpc = 85
  EcpcWithAuthorStrategy = 86
  EcomSellerEcpc = 87
  ColdStartCoefficientEcpc = 88
  RevenueOptimiseEcpc = 89
  UserExporeEcpc = 90
  NativeStatsAutoParamEcpc = 91
  InnerAccountSuppressEcpc = 93
  MicroappDiscountEcpc = 94
  KnewsColdStartEcpc = 95
  NonConversionVisitorExplore = 96
  InspireQualityAudienceSuppress = 97
  IndustryModelEcpc = 98
  FanstopPddUserPackageRedirectEcpc = 99
  InnerLSPEcpc = 100

  # 联盟 ecpc 策略
  UniverseKeyActionEcpc = 101
  UniverseUnitedMcdaEcpc = 102  # 联盟 mcda 统一助攻策略
  # 品牌会员boost
  BrandVip = 103
  InnerFanstopBoostEcpc = 104

  # 开屏boost
  SplashBoostCoefEcpc = 105
  SplashEyemaxDiscountEcpc = 106

  # 粉条 ecpc 策略
  FollowXtrForFanstopEcpc = 201  # 涨粉目标行为率调整策略

  # 粉条 招聘直播间投进人 以组件点击率进行 ecpc 策略
  FanstopRecruitLiveClickEcpc = 202
  AdMerchantFollow = 203

  # reco 本地生活短视频 cvr 辅助 ecpc 策略
  RecoLocallifeCvrEcpc = 204
  # 金教表单获客 ecpc 策略
  FinEduSubmitObtainEcpc = 205
  # 先知外部数据 ecpc
  ProphetScoreEcpc = 206
  # 美妆行业白名单客户 ecpc
  AdMerchantBeauty = 207
  AdMerchantAudience = 208
  AdMerchantBeautyU0 = 209
  AdMerchantBeautyU1 = 210
  AdMerchantBeautyU3 = 211
  AdMerchantBeautyU4 = 212
  AdGoodQuality = 213
  PecCouponEcpc = 214
  # 掉量素材 ecpc
  DropItemEcpc = 215
  # reco 本地生活直播 ecpc 策略
  RecoLocallifeLiveEcpc = 216
  GuessLikeDoubleColEcpc = 217
  # 磁力万合 ecpc
  SideWindowEcpc = 218
  # 激励电商 ecpc
  InspireMerchantEcpc = 219
  # 万合 P 页皮肤广告 ecpc
  WanheProfileEcpc = 220
  # 买家首页 ecpc
  BuyerHomePageEcpc = 221
  # 金牛环 ecpc 策略
  AdMerchantEcpc = 222
  # 有效获客 ecpc 策略
  LpsDeepEcpc = 223
  GameRetargetEcpc = 225
  # 激励视频优化目标 ecpc 策略
  IncentiveEcpc = 226
  # 短视频有效播放
  EspPhotoEffectivePlay = 227
  # 万合垂类效率匹配优化
  WanheMatchingEfficiencyEcpc = 228
  # 万合出价目标成本率优化
  WanheActionTypeCostRatioEcpc = 229
  # 激励视频深度激励 ecpc 策略
  DeepRewardedCoinEcpc = 230
  # 内流 trigger item 河图标签 ecpc
  InnerTriggerItemEcpc = 231
  # 首充党 ecpc 策略
  GamePlayerOnlyFirstChargeEcpc = 232
  # 短剧重定向 ecpc 策略
  PlayletRetargetEcpc = 233
  # 外循环直播粉丝 ecpc 策略
  OuterLiveFanEcpc = 234
  # 每留增强次留 ecpc 策略
  EverydayRetentionEcpc = 235
  # 教育行业表单深度 ecpc 策略 正价课
  EduLpsDeepEcpc = 236
  # 游戏ecpc策略
  FanstopGameUserPackageEcpc = 237
  # 快小游激励
  RewardGameEcpc = 238
  # 人群广告曝光率优化 ecpc 策略
  UserImpOptEcpc = 239
  # 行业 boost 策略
  OuterIndustryBoostEcpc = 240
  #  短视频券 ecpc 策略
  PecCouponVideoEcpc = 241
  # 外循环直播人群包 ecpc 工具
  OuterLiveOrientationToolEcpc = 242
  # w 人群优化 ecpc 策略
  UserVauleGroupEcpc = 243
  # 创新流量 ecpc 策略
  InnovationTrafficEcpc = 244
  # 本地人群探索 ecpc 策略
  LocalLifeUserExploreEcpc = 245
  # 本地消费力分层 ecpc 策略
  LocalLifeConsumPowerEcpc = 246
  # 搜索 ecpc 策略
  SearchPhotoCalcBoostEcpc = 301
  SearchLiveCalcBoostEcpc = 302
  SearchOcpxEcpc = 303
  SearchOcpxPhotoEcpc = 304
  SearchAuthorEcpc = 305
  SearchIndustryEcpc = 306
  SearchPhotoBidwordEcpc = 307
  SearchDiversionSourceEcpc = 308
  SearchEnterSourceEcpc = 309
  SearchGoodTabEcpc = 310
  SearchLiveEcpmRecallEcpc = 311
  SearchLiveOcpxExpEcpc = 312
  OuterOverissueEcpc = 313
  # 外循环直播游戏降低激活成本 ecpc 策略
  OuterLiveGameConvEcpc = 314
  # 主站激活次留 ecpc 策略
  ConvNextstayEcpc = 315
  # 线索服务号 ecpc 策略
  ClueFwhIntentEcpc = 316
  # 外循环 DNC 最大化 ecpc 策略
  OuterloopNcMaxEcpc = 317
  # 微小游跳返时长 ecpc 策略
  WeGameReturnTimeEcpc = 318
  # 快小游 iap 人群探索 ecpc 策略
  KGameEcpc = 319
  # 外循环用首次转化承接策略
  OuterDncFollowUpEcpc = 320
  # 发现页出价目标成本率优化
  ExploreActionTypeCostRatioEcpc = 321
  # [yanqi08] qcpx
  QcpxCpmEcpc = 322
  # 冷启动分级校准策略
  ColdStartCalibrateEcpc = 323
  # 教育深度高沉浸 ecpc
  EduLpsDeepAllClassEcpc = 324
  # T7 ROI 种草人群 boost
  T7ROIZhongCaoEcpc = 325
  # 预约 LTV 调权
  GameAppointEcpc = 326
  # 直播 vtr 调权
  OuterLiveVtrEcpc = 327
  # 微小游 rta 调权
  GameRtaSignEcpc = 328
  # 金融小贷 ROI ecpc
  FinCreditRoiEcpc = 329
  # 获客泛化 ecpc
  LpsAcqGenEcpc = 330
  # T7 ROI 高价值流量 ecpc
  T7ROIHighQualityEcpc = 331
  # 头客追击-游戏行业破圈 ecpc
  PoQuanEcpc = 332
  # 付费短剧ltv ecpc
  PaySkitLtvRectifyEcpc = 333
  # 游戏首发 ecpc
  GameShouFaEcpc = 335
  # 粉条买卖通优质用户 ecpc
  FanstopPrivateMessageUserEcpc = 336
  # dnc 模型 优选 boost
  OuterDncNcModelEcpc = 337
  # na 小说重定向
  OuterFictionRetargetEcpc = 338
  # 私信 ecpc
  PrivateMessageEcpc = 339
  # ecpc自助工具
  OuterSelfServiceToolEcpc = 340
  # 商家距离Ecpc
  PoiDistanceEcpc = 341
  # 内循环探索 ecpc
  InnerProductEEEcpc = 342
  # 本地用户 B 分层 Ecpc
  InnerLSPUserBLevelEcpc = 343
  # 粉条人群包 ecpc 工具
  FanstopOrientationTagEcpc = 344
  # IAA 短剧提价 Ecpc
  IAADJRaiseEcpc = 345
  # 全站 uplift Ecpc
  StorewideUpliftEcpc = 346
  # IAA/IAP/IAAP探索
  OuterGameExploreEcpc = 347
  # 本地推聚合页 ecpc
  InnerLSPAggrPageEcpc = 348
  # 全站商品 uplift Ecpc
  StorewideMerchantUpliftEcpc = 349
  # 付费小说 C 补 Ecpc
  FictionSubsidyEcpc = 350
  # 非闭环 iaa ecpc
  NonCloseIaaEcpc = 351
  # 游戏/小说 iaa 变现 ecpc
  IndustryIaaRoasEcpc = 352
  # 内循环探索 ecpc
  InnerCrmEEEcpc = 353
  InnerSouthEEEcpc = 354
  InnerTieEEEcpc = 355
  InnerAdxEEEcpc = 356
  InnerAdxEEOrientationEcpc = 357
  # IAP 核心用户保护
  IapCorePopulationProtectEcpc = 359
    # 粉条电话拨打 ecpc
  FanstopPhoneCallUserEcpc = 360
  # 游戏人群探索 ecpc
  GameBigRExploreEcpc = 361
  # cid qcpx ecpc
  CidQcpxCpmEcpc = 362
  # 内循环通用干预 ecpc
  InnerCommonInterventionEcpc = 363
  # 外循环通用干预 ecpc
  OuterCommonInterventionEcpc = 364
  # 私信企微 ecpc
  PrivateMessageAndWechatGroupEcpc = 365
  # 行业人群 ecpc
  IndustryUserTagEcpc = 366
  # 私信纠偏 ecpc
  LeadsSubmitCalibrationEcpc = 367
  # 行业老客
  InnerIndustryUserEcpc = 368
  # 游戏IAP C 补 Ecpc
  MiniGameSubsidyEcpc = 369
  # 直播全站定向人群 ecpc
  StoreWideAuthorTargetEcpc = 371
  # 外循环引入进评率
  OuterCmtRankEcpc = 372
  # 本地用户兴趣 ecpc
  InnerLspUserBehaviorEcpc = 373
  # na 小说爆款书 ecpc
  FictionHotBookEcpcStrategy = 374
  # 快小IAA IPU ECPC
  GameIaaIpuEcpc = 375
  # na 小说 iaa 校准
  FictionIaaEcpcStrategy = 376
  # 发现页匹配效率 ecpc
  ExplorePageEcpc = 377
  # 发现页内流相关性 ecpc
  InnerExploreRelativeEcpc = 378
  # 高 U 分层ecpc
  HighUserLevelEcpc = 379
  # 货架直播卡召回 ecpc
  ShelfLiveCardRecallEcpc = 380
  # 货架商品卡召回 ecpc
  ShelfItemCardRecallEcpc = 381
  # 短视频带货 ctr Ecpc
  MerchantPhotoCtrBoost = 382
  # 人群定向单元 ecpc
  AudienceTargetUnitEcpc = 383
  # 关注页 ecpc
  FollowPageEcpc = 384
  # 直播内流 ecpc
  LiveInnerEcpc = 385
  # 新质量分 ecpc
  ConvQualityNew = 386
  # 小说 IAA IPU ECPC
  FictionIaaIpuEcpc = 387
  # 货架人货价匹配 ecpc
  ShelfUserProdPriceEcpc = 388
  # 产品校准
  OuterloopProdCaliEcpc = 389
  # 外循环直播强化学习 ecpc
  OuterLiveRlEcpc = 391
  # IAA 跨产品泛化 ecpc
  IaaAcqGenEcpc = 392
  # 货架直播卡白名单author ecpc
  ShelfLiveWhiteAuthorEcpc = 393
  # 货架精品池 ecpc
  ShelfTopProdAuthorEcpc = 394
  # 小说 nc 探索策略 ecpc
  FictionIapNcEcpcStrategy = 395
  # 私信服务号 ecpc
  PmFwhEcpc = 396
  # 内循环直播增量出价 ecpc
  InnerLiveUpliftEcpc = 397
  # 首单 ecpc
  InnerNewOrderEcpc = 398
  # 升级版联合建模 ecpc
  UpgradedIndustryModelEcpc = 399
  # 全站低 C ecpc
  StoreLowClevelEcpc = 400
  # 用户消费力分层ecpc
  UserConsumLevelEcpc = 401
  # 货架商品卡白名单 author ecpc
  ShelfItemCardWhiteAuthorEcpc = 402
  # 外循环医疗行业 ECPC
  OuterMedicalExploreEcpc = 403
  # 外循环 iaa 强化学习 ecpc
  OuterIaaRlEcpc = 404
  # 小说自然流量 ECPC
  FictionNotNaEcpc = 405
  # 游戏 CXR ECPC
  GameCxrEcpc = 406
  # 外循环 LLM 生成式 ECPC
  OuterLlmGenerativeEcpc = 407
  # 外循环 服务号 ECPC
  OuterLlmFwhEcpc = 408
  # 内循环全站人群包 ECPC
  InnerStorewideOrientationEcpc = 409
  # 私信到店&搜索 ecpc
  PmPoiSearchEcpc = 410
  # 保n单 ecpc
  InnerNewGuardNEcpc = 411
  EcpcMaxTag = 1024
  




@unique
class AutoCpaBidTag(Enum):
  SetAutoCpaBid = 1
  AmdLiveCalcMobileLiveAutoBidNormal = 2
  AmdLiveMinBidFilterNormal = 3
  AmdLiveRoasModifyStrategyNormal = 4
  AmdLiveNoBidStrategyNormal = 5
  AmdRoasModifyAutoBidNormal = 6

@unique
class ModifyRTag(Enum):
  ModifyUnifyCxrInfoNormal = 1
  ModifyRValuePlugin = 2
  CalibrationWithCmdCorePlugin = 3
  CvrOfflineCalibratePlugin = 4
  CalibrationWithCmdNativePlugin = 5
  ManualCalibrationPlugin = 6
  CalibrationSplashInnerAdPlugin = 7
  CommonOfflineCalibratePlugin = 8
  CalibrationInnerSelfServAdPlugin = 9
  ShelfMerchantOfflineCalibrationPlugin = 10
  CalibrationInnerAdPlugin = 11
  CalibrationInnerColdStartLivePlugin = 12
  CalibrationInnerNobidPlugin = 13
  CalibrationFeedSctrPlugin = 14
  CalibrationModelCvrPlugin = 15
  CalibrationInnerCtcvrPlugin = 16
  CalibrationInnerCpmPlugin = 17
@unique
class ExploreEcpmTag(Enum):
  ExploreEcpm = 1

#  搜索 - 多队列rank benefit - 分位置 Sctr
@unique
class SearchPosSctrTag(Enum):
  Pos0 = 1   # 选取位置 pos0
  Pos1 = 2   # 选取位置 pos1
  Pos2 = 3   # 选取位置 pos2
  Pos5 = 4   # 选取位置 pos5
  Pos7 = 5   # 选取位置 pos7
  Pos8 = 6   # 选取位置 pos8
  Page1 = 7  # 选取位置 Page1
  VitualPos1 = 8

#  搜索 - 多队列rank benefit - 分位置 ctr
@unique
class SearchPosCtrTag(Enum):
  pos1 = 1   # 选取位置 pos1 的 bias(内外同流策略)
  pos2 = 2   # 选取位置 pos2
  pos5 = 3   # 选取位置 pos5
  pos7 = 4   # 选取位置 pos7
  pos8 = 5   # 选取位置 pos8
  page1 = 6  # 选取位置 page1
  vitual_pos1 = 7
  DefaultPosUnifyCtr = 101 # 最大值; pos_ctr = unify_ctr; 无位置

@unique
class SearchVitualCpmTag(Enum):
  Pos1 = 1

#  搜索 - 多队列rank benefit - 分位置 cvr
@unique
class SearchPosCvrTag(Enum):
  SearchModifyP2LPosUnifyCvr = 1 # p2l 统一 R 值多乘 cvr 和 live_audience
  SearchModifyP2LVitualPosUnifyCvr = 2 # 用于相关性上移阶段计算pos1 cvr

#  搜索 - 多队列rank benefit - 分位置 boost
class SearchPosBoostTag(Enum):
  SearchDefaultBoost = 1    # 基础 rb 阶段 boost
  SearchStyleBoost = 2      # 多队列阶段 带样式 & 位置 的 boost
  SearchCelebrityBoost = 3  # 直播大卡 boost
  SearchAppCardBoost = 4    # 下载强样式 boost
  SearchFormCardBoost = 5   # 表单强样式 boost
  SearchSeriesCardBoost = 6 # 短剧卡 boost

#  搜索 - 多队列rank benefit - 分位置 modify_ratio
class SearchModifyRatioTag(Enum):
  SearchModifyCtrCvr = 1       # 内外同流调整 ctr & 统一 R 调整 cvr
  SearchMRLowerUpperBound = 2  # 上下界截断
  SearchModifyCtrCvrStrong = 3 # 强卡调整 ctr

# 搜索 - 多队列rank benefit - 分位置 bonus
class SearchPosBonusTag(Enum):
  SearchBonus = 1 # 
  SearchItemCardBonus = 2  # 无素材商品卡 bonus
  SearchCelebrityBonus = 3 # 直播大卡 bonus
  SearchAppCardBonus = 4   # 下载强样式 bonus
  SearchFormCardBonus = 5  # 表单强样式 bonus

# 搜索 - 多队列rank benefit - 分位置 hc
class SearchPosHcTag(Enum):
  SearchHc = 1 # 

# 搜索 - 多队列rank benefit - 设置计费字段
class SearchSetPriceInfoTag(Enum):
  SearchSetPriceInfo = 1    # 计费字段
  SearchSetCtrCvrInfo = 2   # 预估字段
  SearchSetUselessField = 3 # 无用字段 modify_ratio(待下线)

# 搜索 - 多队列rank benefit - 排序
class SearchMultiListSortTag(Enum):
  SearchPosRBGreaterSort = 1                      # 通过分位置 rb 从高到低排序
  SearchInterveneAdFirst = 2                      # 干预广告出在首位
  # 11 ~ 20 综搜
  SearchComboSearchPos1Relevance = 11             # 综搜设置推荐广告位:
  SearchComboSearchTopQueryForbidDynamicPos = 12  #
  SearchComboSearchLowRelevanceAdPos5 = 13        #
  SearchComboSearchRecallStrategyPos5 = 14        #
  SearchComboSearchPnameEqual2QueryPos1 = 15      #
  SearchComboSearchPos1RelevanceCPM = 16
  # 21 ~ 30 内流
  SearchInnerStreamSetSuggestPos = 21             # 内流设置推荐广告位
  SearchInnerStreamBestPairEcpmAdFirst = 22       # 内流计算最优 ecpm 广告对, 并挪至队首
  # 31 ~ 40 购物 tab
  SearchGoodTabSetSuggestPos = 31                 # 购物 Tab 设置推荐广告位
  # 41 ~ 50 直播 tab
  SearchLiveTabSetSuggestPos = 41                 # 直播 Tab 设置推荐广告位
