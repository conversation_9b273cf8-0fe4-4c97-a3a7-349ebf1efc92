import copy

global_ad_table_config = {
    "item_tables": ["global_ad_table"],
    "remote_table_relation": {
        "wt_creative": {
            "local_field_alias": {
                "first_index_time": "fd_CREATIVE_first_index_time"
            },
            "item_table_key_attr": "creative_id"
        },
        "ad_dsp_photo": {
          "local_field_alias": {
            "cpm_tag_1102": "fd_PHOTO_cpm_tag_1102",
            "cpm_tag_1101": "fd_PHOTO_cpm_tag_1101",
            "native_strict_status": "fd_PHOTO_native_strict_status",
            "shield_status": "fd_PHOTO_shield_status",
            "md5": "fd_PHOTO_md5",
            "photo_sub_source": "fd_PHOTO_photo_sub_source"
          },
          "item_table_key_attr": "photo_id"
      },
        "wt_photo": {
            "local_field_alias": {
                "first_index_time": "fd_PHOTO_first_index_time",
                "general_review": "fd_PHOTO_general_review",
                "hot_review": "fd_PHOTO_hot_review",
                "topk_review": "fd_PHOTO_topk_review",
                "photo_promotable_status": "fd_PHOTO_photo_promotable_status",
                "jpk_id_v1": "fd_PHOTO_jpk_id_v1",
                "similar_type_new": "fd_PHOTO_similar_type_new",
                "item_impression": "fd_PHOTO_item_impression",
                "played_ns": "fd_PHOTO_played_ns",
                "goods_view": "fd_PHOTO_goods_view",
                "order_paied": "fd_PHOTO_order_paied",
                "first_hetu_v3_category_id": "fd_PHOTO_first_hetu_v3_category_id",
                "perception_quality": "fd_PHOTO_perception_quality",
                "high_exposure_quality": "fd_PHOTO_high_exposure_quality",
                "ad_items": "fd_PHOTO_ad_items",
                "job_id": "fd_PHOTO_job_id",
                "org_id": "fd_PHOTO_org_id",
                "job_category": "fd_PHOTO_job_category",
                "job_address": "fd_PHOTO_job_address",
                "crc_ue_low": "fd_PHOTO_crc_ue_low",
                "ad_spu_ids": "fd_PHOTO_ad_spu_ids",
                "game_id": "fd_PHOTO_game_id",
                "item_impression_7_day": "fd_PHOTO_item_impression_7_day",
                "photo_bounce_rate": "fd_PHOTO_photo_bounce_rate",
                "photo_bounce_rate_v2": "fd_PHOTO_photo_bounce_rate_v2",
                "clue_sdpa_category_id": "fd_PHOTO_clue_sdpa_category_id",
                "e_ad_item_impression_7day": "fd_PHOTO_e_ad_item_impression_7day",
                "e_ad_item_click_7day": "fd_PHOTO_e_ad_item_click_7day",
                "conversion_num_7day": "fd_PHOTO_conversion_num_7day",
                "ue_check_tag": "fd_PHOTO_ue_check_tag",
                "comment_cnt": "fd_PHOTO_comment_cnt",
                "origin_similar_photo_id": "fd_PHOTO_origin_similar_photo_id",
                "origin_similar_photo_score": "fd_PHOTO_origin_similar_photo_score",
                "photo_delta_treatment": "fd_PHOTO_photo_delta_treatment",
                "item_photo_score": "fd_PHOTO_item_photo_score",
                "item_p2l_score": "fd_PHOTO_item_p2l_score",
                "ecom_spu_id": "fd_PHOTO_ecom_spu_id",
                "photo_semantic_id_level1": "fd_PHOTO_photo_semantic_id_level1",
                "photo_semantic_id_level2": "fd_PHOTO_photo_semantic_id_level2",
                "photo_semantic_id_level3": "fd_PHOTO_photo_semantic_id_level3",
                "native_degree_tag": "fd_PHOTO_native_degree_tag",
                "inner_photo_coldstart_tag": "fd_PHOTO_inner_photo_coldstart_tag",
                "inner_p2l_coldstart_tag": "fd_PHOTO_inner_p2l_coldstart_tag",
                "photo_coldstart_item_score": "fd_PHOTO_photo_coldstart_item_score",
                "p2l_coldstart_item_score": "fd_PHOTO_p2l_coldstart_item_score",
                "up_items_group": "fd_PHOTO_up_items_group",
                "up_items_tag": "fd_PHOTO_up_items_tag",
                "photo_coldstart_predict_target_cost_fix": "fd_PHOTO_photo_coldstart_predict_target_cost_fix",
                "photo_coldstart_target_cost_fix": "fd_PHOTO_photo_coldstart_target_cost_fix",
                "p2l_coldstart_predict_target_cost_fix": "fd_PHOTO_p2l_coldstart_predict_target_cost_fix",
                "p2l_coldstart_target_cost_fix": "fd_PHOTO_p2l_coldstart_target_cost_fix",
                "sid": "fd_PHOTO_sid"
              },
              "item_table_key_attr": "photo_id"
          },
        "wt_author": {
            "local_field_alias": {
                "group_rel_level": "fd_AUTHOR_group_rel_level",
                "group_strong_rel_level": "fd_AUTHOR_group_strong_rel_level",
                "seller_category": "fd_AUTHOR_seller_category",
                "user_fans_count": "fd_AUTHOR_user_fans_count",
                "t30_last_live": "fd_AUTHOR_t30_last_live",
                "fanstop_author_flag": "fd_AUTHOR_fanstop_author_flag",
                "brand_level": "fd_AUTHOR_brand_level",
                "shop_score_v3": "fd_AUTHOR_shop_score_v3",
                "nature_ad_tag": "fd_AUTHOR_nature_ad_tag",
                "like_v_tag": "fd_AUTHOR_like_v_tag",
                "first_user_type_for_bm": "fd_AUTHOR_first_user_type_for_bm",
                "master_score": "fd_AUTHOR_master_score",
                "shop_dist_type": "fd_AUTHOR_shop_dist_type",
                "biz_center": "fd_AUTHOR_biz_center",
                "roi_convert_ratio": "fd_AUTHOR_roi_convert_ratio",
                "roi_convert_ratio_p2l": "fd_AUTHOR_roi_convert_ratio_p2l",
                "roi_convert_ratio_live": "fd_AUTHOR_roi_convert_ratio_live",
                "last_30d_cost": "fd_AUTHOR_last_30d_cost",
                "gmv_level": "fd_AUTHOR_gmv_level",
                "is_vh": "fd_AUTHOR_is_vh",
                "author_today_cost" : "fd_AUTHOR_author_today_cost",
                "author_today_target_cost" : "fd_AUTHOR_author_today_target_cost",
                "author_today_discount_ratio" : "fd_AUTHOR_author_today_discount_ratio",
                "new_first_cost_date" : "fd_AUTHOR_new_first_cost_date",
                "account_sale_depart_level2": "fd_AUTHOR_account_sale_depart_level2",
                "photo_coupon_id": "fd_AUTHOR_photo_coupon_id",
                "live_coupon_id": "fd_AUTHOR_live_coupon_id",
                "history_roi0": "fd_AUTHOR_history_roi0",
                "first_cost_date": "fd_AUTHOR_first_cost_date",
                "industry": "fd_AUTHOR_industry",
                "author_to_call_card_type": "fd_AUTHOR_author_to_call_card_type",
                "is_ecommerce": "fd_AUTHOR_is_ecommerce",
                "is_leads": "fd_AUTHOR_is_leads",
                "item_price_mode": "fd_AUTHOR_item_price_mode",
                "gmv_mode": "fd_AUTHOR_gmv_mode",
                "shop_coupon_status": "fd_AUTHOR_shop_coupon_status",
                "photo_roi_param": "fd_AUTHOR_photo_roi_param",
                "live_roi_param": "fd_AUTHOR_live_roi_param",
                "pcoc_by_gender_age": "fd_AUTHOR_pcoc_by_gender_age",
                "pcoc_by_pred_cvr": "fd_AUTHOR_pcoc_by_pred_cvr",
                "cost_level_92d_int": "fd_AUTHOR_cost_level_92d_int",
                "is_smb": "fd_AUTHOR_is_smb",
                "is_92d_new_cur": "fd_AUTHOR_is_92d_new_cur",
                "is_xd_cost_cur": "fd_AUTHOR_is_xd_cost_cur",
                "inner_author_bonus_level_cost": "fd_AUTHOR_inner_author_bonus_level_cost",
                "inner_author_bonus_level_gmv": "fd_AUTHOR_inner_author_bonus_level_gmv",
                "inner_author_bonus_level_cost_2": "fd_AUTHOR_inner_author_bonus_level_cost_2",
                "inner_author_bonus_level_gmv_2": "fd_AUTHOR_inner_author_bonus_level_gmv_2",
                "inner_author_bonus_level_newcus1": "fd_AUTHOR_inner_author_bonus_level_newcus1",
                "inner_author_bonus_level_newcus2": "fd_AUTHOR_inner_author_bonus_level_newcus2",
                "inner_author_bonus_level_newcus3": "fd_AUTHOR_inner_author_bonus_level_newcus3",
                "inner_author_bonus_level_newcus4": "fd_AUTHOR_inner_author_bonus_level_newcus4",
                "inner_author_today_cost": "fd_AUTHOR_inner_author_today_cost",
                "inner_author_last_29d_cost_outer": "fd_AUTHOR_inner_author_last_29d_cost_outer",
                "imp_1d": "fd_AUTHOR_imp_1d",
                "cost_7d": "fd_AUTHOR_cost_7d",
                "gmv_7d": "fd_AUTHOR_gmv_7d",
                "fcd": "fd_AUTHOR_fcd",
                "max_sir_pctr": "fd_AUTHOR_max_sir_pctr",
                "avg_sir_pctr": "fd_AUTHOR_avg_sir_pctr",
                "avg_sir_ctr": "fd_AUTHOR_avg_sir_ctr",
                "max_sir_pcvr": "fd_AUTHOR_max_sir_pcvr",
                "avg_sir_pcvr": "fd_AUTHOR_avg_sir_pcvr",
                "avg_sir_cvr": "fd_AUTHOR_avg_sir_cvr",
                "tc_acc_explore_author_info": "fd_AUTHOR_tc_acc_explore_author_info",
                "live_uniq_item_num_7d": "fd_AUTHOR_live_uniq_item_num_7d",
                "live_order_num_7d": "fd_AUTHOR_live_order_num_7d",
                "live_gmv_7d": "fd_AUTHOR_live_gmv_7d",
                "live_gmv_price_ratio_7d": "fd_AUTHOR_live_gmv_price_ratio_7d",
                "live_uniq_item_num_1d": "fd_AUTHOR_live_uniq_item_num_1d",
                "live_order_num_1d": "fd_AUTHOR_live_order_num_1d",
                "live_gmv_1d": "fd_AUTHOR_live_gmv_1d",
                "live_gmv_price_ratio_1d": "fd_AUTHOR_live_gmv_price_ratio_1d",
                "live_coldstart_stage": "fd_AUTHOR_live_coldstart_stage",
                "coldstart_p2l_limit": "fd_AUTHOR_coldstart_p2l_limit",
                "coldstart_photo_limit": "fd_AUTHOR_coldstart_photo_limit",
                "coldstart_live_limit": "fd_AUTHOR_coldstart_live_limit",
                "coldstart_p2l_limit_usage": "fd_AUTHOR_coldstart_p2l_limit_usage",
                "coldstart_photo_limit_usage": "fd_AUTHOR_coldstart_photo_limit_usage",
                "coldstart_live_limit_usage": "fd_AUTHOR_coldstart_live_limit_usage",
                "avg_sw_sir_pctcvr_list": "fd_AUTHOR_avg_sw_sir_pctcvr_list",
                "avg_sw_sir_ctcvr_list": "fd_AUTHOR_avg_sw_sir_ctcvr_list",
                "avg_sw_order_sir_pctcvr_list": "fd_AUTHOR_avg_sw_order_sir_pctcvr_list",
                "avg_sw_order_sir_ctcvr_list": "fd_AUTHOR_avg_sw_order_sir_ctcvr_list",
                "coldstart_photo_limit_v2": "fd_AUTHOR_coldstart_photo_limit_v2",
                "coldstart_p2l_limit_v2": "fd_AUTHOR_coldstart_p2l_limit_v2",
                "pay_amount_author_3_h": "fd_AUTHOR_pay_amount_author_3_h",
                "real_pay_amount_author_3_h": "fd_AUTHOR_real_pay_amount_author_3_h",
                "platform_bear_amount_author_3_h": "fd_AUTHOR_platform_bear_amount_author_3_h",
                "item_num_author_3_h": "fd_AUTHOR_item_num_author_3_h",
                "order_num_author_3_h": "fd_AUTHOR_order_num_author_3_h",
                "item_promotion_author_3_h": "fd_AUTHOR_item_promotion_author_3_h",
                "shop_promotion_author_3_h": "fd_AUTHOR_shop_promotion_author_3_h",
                "min_gmv_author_3_h": "fd_AUTHOR_min_gmv_author_3_h",
                "max_gmv_author_3_h": "fd_AUTHOR_max_gmv_author_3_h"
            },
            "item_table_key_attr": "author_id"
        },
        "wt_account": {
            "local_field_alias": {
                "account_advertiser_risk_label": "fd_ACCOUNT_account_advertiser_risk_label",
                "crm_agent_type": "fd_ACCOUNT_crm_agent_type",
                "pay_mode": "fd_ACCOUNT_pay_mode",
                "plus_new_customer_bonus": "fd_ACCOUNT_plus_new_customer_bonus",
                "smb_level_tag": "fd_ACCOUNT_smb_level_tag",
                "client_second_industry_name": "fd_ACCOUNT_client_second_industry_name",
                "tongxin_brand_name": "fd_ACCOUNT_tongxin_brand_name",
                "tongxin_second_category": "fd_ACCOUNT_tongxin_second_category",
                "tongxin_directional_traffic": "fd_ACCOUNT_tongxin_directional_traffic",
                "tongxin_general_traffic": "fd_ACCOUNT_tongxin_general_traffic",
                "tongxin_price": "fd_ACCOUNT_tongxin_price",
                "pv_drop_model_id": "fd_ACCOUNT_pv_drop_model_id",
                "maa_cost_level_90d": "fd_ACCOUNT_maa_cost_level_90d",
                "crm_center": "fd_ACCOUNT_crm_center",
                "crm_center_v2": "fd_ACCOUNT_crm_center_v2",
                "pv_drop_model_type": "fd_ACCOUNT_pv_drop_model_type",
                "pv_drop_rule_strength": "fd_ACCOUNT_pv_drop_rule_strength",
                "tongxin_expore_hc_limit": "fd_ACCOUNT_tongxin_expore_hc_limit",
                "lps_acq_gen_cmd_id_list": "fd_ACCOUNT_lps_acq_gen_cmd_id_list",
                "lps_acq_gen_score_list": "fd_ACCOUNT_lps_acq_gen_score_list",
                "pm_fwh_account_cost_info": "fd_ACCOUNT_pm_fwh_account_cost_info",
                "account_cost_info": "fd_ACCOUNT_account_cost_info",
                "ecpc_model_predict_id": "fd_ACCOUNT_ecpc_model_predict_id",
                "ecpc_model_type": "fd_ACCOUNT_ecpc_model_type",
                "ecpc_rule_strength": "fd_ACCOUNT_ecpc_rule_strength",
                "self_service_account_order_cnt": "fd_ACCOUNT_self_service_account_order_cnt",
                "cid_goods_type": "fd_ACCOUNT_cid_goods_type",
                "group_idx": "fd_ACCOUNT_group_idx",
                "clue_aggr_define_client_name": "fd_ACCOUNT_clue_aggr_define_client_name",
                "health_score": "fd_ACCOUNT_health_score",
                "first_cost_date_account": "fd_ACCOUNT_first_cost_date_account",
                "account_7d_imp": "fd_ACCOUNT_account_7d_imp",
                "account_7d_cost": "fd_ACCOUNT_account_7d_cost",
                "account_7d_gmv": "fd_ACCOUNT_account_7d_gmv",
                "account_7d_fct": "fd_ACCOUNT_account_7d_fct",
                "prms_conversion_cnt": "fd_ACCOUNT_prms_conversion_cnt",
                "leads_conversion_cnt": "fd_ACCOUNT_leads_conversion_cnt",
                "fanstop_first_category_name_author": "fd_ACCOUNT_fanstop_first_category_name_author",
                "fanstop_customer_level_tag": "fd_ACCOUNT_fanstop_customer_level_tag",
                "fanstop_account_is_xiaodian": "fd_ACCOUNT_fanstop_account_is_xiaodian",
                "iaa_game_6d_reward_cost": "fd_ACCOUNT_iaa_game_6d_reward_cost",
                "iaa_game_today_reward_cost": "fd_ACCOUNT_iaa_game_today_reward_cost",
                "iaa_game_6d_reward_cost_no_game": "fd_ACCOUNT_iaa_game_6d_reward_cost_no_game",
                "iaa_game_today_reward_cost_no_game": "fd_ACCOUNT_iaa_game_today_reward_cost_no_game"
            },
            "item_table_key_attr": "account_id"
        },
        "ad_dsp_account": {
          "local_field_alias": {
              "corporation_name": "fd_ACCOUNT_corporation_name",
              "clue_optimize_switch": "fd_ACCOUNT_clue_optimize_switch",
              "create_source": "fd_ACCOUNT_create_source",
              "auto_manage_type": "fd_ACCOUNT_auto_manage_type",
              "account_auto_manage": "fd_ACCOUNT_account_auto_manage"
          },
          "item_table_key_attr": "account_id"
        },
        "wt_product": {
            "local_field_alias": {
                "category_level_1_id": "fd_PRODUCT_category_level_1_id",
                "category_level_2_id": "fd_PRODUCT_category_level_2_id",
                "category_level_3_id": "fd_PRODUCT_category_level_3_id",
                "good_quality": "fd_PRODUCT_good_quality",
                "payment_per_order": "fd_PRODUCT_payment_per_order",
                "spu_id_v1": "fd_PRODUCT_spu_id_v1",
                "kg_tag_ids": "fd_PRODUCT_kg_tag_ids",
                "eco_minus_rate": "fd_PRODUCT_eco_minus_rate",
                "product_tag": "fd_PRODUCT_product_tag",
                "event_order_paid_cnt": "fd_PRODUCT_event_order_paid_cnt",
                "rl_pay_amount_per_order": "fd_PRODUCT_rl_pay_amount_per_order",
                "is_ghot": "fd_PRODUCT_is_ghot",
                "is_ghot_v2": "fd_PRODUCT_is_ghot_v2",
                "basic_price": "fd_PRODUCT_basic_price",
                "item_price": "fd_PRODUCT_item_price",
                "avg_product_min_price": "fd_PRODUCT_avg_product_min_price",
                "mode_gmv": "fd_PRODUCT_mode_gmv",
                "order_paid_cnt": "fd_PRODUCT_order_paid_cnt",
                "gmv_greater_min_price_cnt": "fd_PRODUCT_gmv_greater_min_price_cnt",
                "mode_gmv_cnt": "fd_PRODUCT_mode_gmv_cnt",
                "avg_gmv": "fd_PRODUCT_avg_gmv",
                "median_gmv": "fd_PRODUCT_median_gmv",
                "mode_gmv_greater_min_price_cnt": "fd_PRODUCT_mode_gmv_greater_min_price_cnt",
                "order_cnt_7days": "fd_PRODUCT_order_cnt_7days",
                "ee_discount_30days": "fd_PRODUCT_ee_discount_30days",
                "qcpx_discount_cnt": "fd_PRODUCT_qcpx_discount_cnt",
                "qcpx_discount_avg_amount": "fd_PRODUCT_qcpx_discount_avg_amount",
                "item_coupon_status": "fd_PRODUCT_item_coupon_status",
                "ecom_spu_id": "fd_PRODUCT_ecom_spu_id",
                "mode_item_price": "fd_PRODUCT_mode_item_price",
                "mode_item_price_frequency": "fd_PRODUCT_mode_item_price_frequency",
                "item_order_count": "fd_PRODUCT_item_order_count",
                "price_percent": "fd_PRODUCT_price_percent",
                "price_percent_cate_id": "fd_PRODUCT_price_percent_cate_id",
                "item_origin_price": "fd_PRODUCT_item_origin_price",
                "shop_promotion_product_3_h": "fd_PRODUCT_shop_promotion_product_3_h",
                "sku_price_high_rate" : "fd_PRODUCT_sku_price_high_rate",
                "order_num_product_3_h": "fd_PRODUCT_order_num_product_3_h",
                "seller_bear_amt": "fd_PRODUCT_seller_bear_amt",
            },
            "item_table_key_attr": "merchant_product_id"
        },
        "wt_live": {
            "local_field_alias": {
                "item_impression": "fd_LIVE_item_impression",
                "played_ns": "fd_LIVE_played_ns",
                "goods_view": "fd_LIVE_goods_view",
                "order_paied": "fd_LIVE_order_paied",
                "job_id": "fd_LIVE_job_id",
                "org_id": "fd_LIVE_org_id",
                "job_category": "fd_LIVE_job_category",
                "job_address": "fd_LIVE_job_address",
                "cost_total_3_hour": "fd_LIVE_cost_total_3_hour",
                "is_digital": "fd_LIVE_is_digital",
                "inner_live_coldstart_tag": "fd_LIVE_inner_live_coldstart_tag",
                "first_index_time": "fd_LIVE_first_index_time",
                "live_coldstart_rl_hc_ratio": "fd_LIVE_live_coldstart_rl_hc_ratio",
                "live_coldstart_rl_hc_ratio_v2": "fd_LIVE_live_coldstart_rl_hc_ratio_v2",
                "pay_amount_live_stream_1_h": "fd_LIVE_pay_amount_live_stream_1_h",
                "real_pay_amount_live_stream_1_h": "fd_LIVE_real_pay_amount_live_stream_1_h",
                "platform_bear_amount_live_stream_1_h": "fd_LIVE_platform_bear_amount_live_stream_1_h",
                "item_num_live_stream_1_h": "fd_LIVE_item_num_live_stream_1_h",
                "order_num_live_stream_1_h": "fd_LIVE_order_num_live_stream_1_h",
                "item_promotion_live_stream_1_h": "fd_LIVE_item_promotion_live_stream_1_h",
                "shop_promotion_live_stream_1_h": "fd_LIVE_shop_promotion_live_stream_1_h",
                "min_gmv_live_stream_1_h": "fd_LIVE_min_gmv_live_stream_1_h",
                "max_gmv_live_stream_1_h": "fd_LIVE_max_gmv_live_stream_1_h"
            },
            "item_table_key_attr": "author_id"
        },
        "wt_unit": {
            "local_field_alias": {
                "first_index_time": "fd_UNIT_first_index_time",
                "event_order_paid_cnt": "fd_UNIT_event_order_paid_cnt",
                "playlet_name": "fd_UNIT_playlet_name",
                "playlet_plot": "fd_UNIT_playlet_plot",
                "playlet_theme": "fd_UNIT_playlet_theme",
                "playlet_start_time": "fd_UNIT_playlet_start_time",
                "playlet_status": "fd_UNIT_playlet_status",
                "playlet_coin_tag": "fd_UNIT_playlet_coin_tag",
                "playlet_free_episode_num": "fd_UNIT_playlet_free_episode_num",
                "playlet_commodity_id_str": "fd_UNIT_playlet_commodity_id_str",
                "playlet_price_str": "fd_UNIT_playlet_price_str",
                "playlet_lessons_str": "fd_UNIT_playlet_lessons_str",
                "cost_total_7_day": "fd_UNIT_cost_total_7_day",
                "cost_7_day": "fd_UNIT_cost_7_day",
                "target_cost_7_day": "fd_UNIT_target_cost_7_day",
                "item_impression_7_day": "fd_UNIT_item_impression_7_day",
                "callback_purchase_amount_7_day": "fd_UNIT_callback_purchase_amount_7_day",
                "live_pcvr_sum_3_hour": "fd_UNIT_live_pcvr_sum_3_hour",
                "live_pcvr_count_3_hour": "fd_UNIT_live_pcvr_count_3_hour",
                "live_pcvr_bucket_count_3_hour_list": "fd_UNIT_live_pcvr_bucket_count_3_hour_list",
                "pc_target_cost_total_sum_price": "fd_UNIT_pc_target_cost_total_sum_price",
                "pc_target_cost_total_num_order": "fd_UNIT_pc_target_cost_total_num_order",
                "playlet_third_part_show_count": "fd_UNIT_playlet_third_part_show_count",
                "clue_aggr_third_cate_id": "fd_UNIT_clue_aggr_third_cate_id",
                "clue_aggr_second_cate_id": "fd_UNIT_clue_aggr_second_cate_id",
                "local_life_ad_layered_tags": "fd_UNIT_local_life_ad_layered_tags",
                "submitted_landing_ctr": "fd_UNIT_submitted_landing_ctr",
                "submitted_landing_cvr": "fd_UNIT_submitted_landing_cvr",
                "leads_submitted_ctr": "fd_UNIT_leads_submitted_ctr",
                "leads_submitted_cvr": "fd_UNIT_leads_submitted_cvr",
                "game_duration_avg_1d": "fd_UNIT_game_duration_avg_1d",
                "ltv_num_avg_1d": "fd_UNIT_ltv_num_avg_1d",
                "target_ecpc_status": "fd_UNIT_target_ecpc_status",
                "locallife_item_promotion_flag": "fd_UNIT_locallife_item_promotion_flag",
                "locallife_has_boost_items": "fd_UNIT_locallife_has_boost_items",
                "locallife_has_high_traffic_items": "fd_UNIT_locallife_has_high_traffic_items",
                "locallife_has_kvi_items": "fd_UNIT_locallife_has_kvi_items",
                "locallife_has_subsidy_items": "fd_UNIT_locallife_has_subsidy_items",
                "locallife_has_mkl_beat_items": "fd_UNIT_locallife_has_mkl_beat_items"
            },
            "item_table_key_attr": "unit_id"
        },
        "ad_dsp_unit": {
            "local_field_alias": {
                "user_intention_type": "fd_UNIT_user_intention_type",
                "ocpx_action_support_type": "fd_UNIT_ocpx_action_support_type",
                "creative_build_type": "fd_UNIT_creative_build_type",
                "explore_put_type": "fd_UNIT_explore_put_type",
                "brand_intention_code": "fd_UNIT_brand_intention_code",
                "brand_brand_code": "fd_UNIT_brand_brand_code",
                "brand_industry_code": "fd_UNIT_brand_industry_code",
                "dpa_product_brand_name": "fd_UNIT_dpa_product_brand_name",
                "series_id": "fd_UNIT_series_id",
                "qcpx_put_type": "fd_UNIT_qcpx_put_type",
                "combo_type": "fd_UNIT_combo_type",
                "fanstop_spu_id": "fd_UNIT_fanstop_spu_id",
                "auto_deliver_type": "fd_UNIT_auto_deliver_type",
                "kwai_book_id": "fd_UNIT_kwai_book_id",
                "link_integration_type": "fd_UNIT_link_integration_type",
                "excycle_skip": "fd_UNIT_excycle_skip",
            },
            "item_table_key_attr": "unit_id"
        },
        "wt_campaign": {
            "local_field_alias": {
                "first_index_time": "fd_CAMPAIGN_first_index_time",
                "is_pass_cold_start": "fd_CAMPAIGN_is_pass_cold_start",
                "is_pm_pass_cold_start": "fd_CAMPAIGN_is_pm_pass_cold_start",
                "pc_target_cost_total_sum_price": "fd_CAMPAIGN_pc_target_cost_total_sum_price",
                "pc_target_cost_today_sum_timestamp": "fd_CAMPAIGN_pc_target_cost_today_sum_timestamp",
                "order_campaign_cost": "fd_CAMPAIGN_order_campaign_cost",
                "order_campaign_target_cost": "fd_CAMPAIGN_order_campaign_target_cost",
                "target_cost_pro_total_sum_price": "fd_CAMPAIGN_target_cost_pro_total_sum_price",
                "target_cost_pro_total_num_order": "fd_CAMPAIGN_target_cost_pro_total_num_order",
                "main_ecpm_auto_24h": "fd_CAMPAIGN_main_ecpm_auto_24h",
                "main_ecpm_24h": "fd_CAMPAIGN_main_ecpm_24h",
                "qcpx_ecpm_auto_24h": "fd_CAMPAIGN_qcpx_ecpm_auto_24h",
                "qcpx_ecpm_24h": "fd_CAMPAIGN_qcpx_ecpm_24h",
                "q_cost_24h": "fd_CAMPAIGN_q_cost_24h",
                "order_newcus_benefit_conversion_cnt": "fd_CAMPAIGN_order_newcus_benefit_conversion_cnt",
            },
            "item_table_key_attr": "campaign_id"
        },
        "ad_dsp_campaign": {
            "local_field_alias": {
                "budget_schedule": "fd_CAMPAIGN_budget_schedule",
                "day_budget": "fd_CAMPAIGN_day_budget",
                "periodic_delivery_type": "fd_CAMPAIGN_periodic_delivery_type",
                "auto_manage": "fd_CAMPAIGN_auto_manage",
                "auto_build": "fd_CAMPAIGN_auto_build",
                "auto_adjust": "fd_CAMPAIGN_auto_adjust",
                "charge_mode": "fd_CAMPAIGN_charge_mode",
                "fans_top_extra_delivery": "fd_CAMPAIGN_fans_top_extra_delivery",
                "fans_top_new_user": "fd_CAMPAIGN_fans_top_new_user",
                "benefit" : "fd_CAMPAIGN_benefit",
            },
            "item_table_key_attr": "campaign_id"
        },
        "wt_tube_table": {
            "local_field_alias": {
                "cost_total_72_hour": "fd_wt_tube_table_cost_total_72_hour",
                "callback_purchase_amount_72_hour": "fd_wt_tube_table_callback_purchase_amount_72_hour",
                "tube_status_72_hour": "fd_wt_tube_table_tube_status_72_hour",
            },
            "item_table_key_attr": "playlet_name_hash"
        },
        "ad_magicsite_page_das": {
            "local_field_alias": {
                "conversion_path": "fd_ad_magicsite_page_das_conversion_path",
                "sub_conversion_path": "fd_ad_magicsite_page_das_sub_conversion_path"
            },
            "item_table_key_attr": "site_id"
        },
        "wt_ad_coupon_template_rel_x7": {
            "local_field_alias": {
                "coupon_type": "fd_ad_coupon_template_coupon_type",
                "status": "fd_ad_coupon_template_status",
                "rule": "fd_ad_coupon_template_rule",
            },
            "item_table_key_attr": "coupon_config_id",
        },
        "wt_multi_table": {
            "local_field_alias": {
                "double_value_1": "fd_wt_multi_table_double_value_1",
            },
            "item_table_key_attr": "pre_pcoc_id",
        }
    },
    "local_field_type": {
        "fd_CREATIVE_first_index_time": "int",
        "fd_PHOTO_first_index_time": "int",
        "fd_PHOTO_general_review": "int",
        "fd_PHOTO_hot_review": "int",
        "fd_PHOTO_topk_review": "int",
        "fd_PHOTO_photo_promotable_status": "int_list",
        "fd_PHOTO_jpk_id_v1": "int",
        "fd_PHOTO_similar_type_new": "int",
        "fd_PHOTO_item_impression": "int",
        "fd_PHOTO_played_ns": "int",
        "fd_PHOTO_goods_view": "int",
        "fd_PHOTO_order_paied": "int",
        "fd_PHOTO_first_hetu_v3_category_id": "int",
        "fd_PHOTO_perception_quality": "double",
        "fd_PHOTO_high_exposure_quality": "double",
        "fd_PHOTO_ad_items": "int_list",
        "fd_PHOTO_job_id": "int_list",
        "fd_PHOTO_org_id": "int_list",
        "fd_PHOTO_job_category": "int_list",
        "fd_PHOTO_job_address": "string_list",
        "fd_PHOTO_cpm_tag_1102": "int",
        "fd_PHOTO_cpm_tag_1101": "int",
        "fd_PHOTO_native_strict_status": "int",
        "fd_PHOTO_crc_ue_low": "int",
        "fd_PHOTO_shield_status": "int",
        "fd_PHOTO_ad_spu_ids": "int_list",
        "fd_AUTHOR_group_rel_level": "int",
        "fd_AUTHOR_group_strong_rel_level": "int",
        "fd_AUTHOR_seller_category": "int",
        "fd_AUTHOR_user_fans_count": "int",
        "fd_AUTHOR_t30_last_live": "int",
        "fd_AUTHOR_fanstop_author_flag": "int",
        "fd_AUTHOR_brand_level": "string",
        "fd_AUTHOR_shop_score_v3": "double",
        "fd_AUTHOR_nature_ad_tag": "int",
        "fd_AUTHOR_like_v_tag": "int",
        "fd_AUTHOR_first_user_type_for_bm": "int",
        "fd_AUTHOR_master_score": "double",
        "fd_AUTHOR_shop_dist_type": "int",
        "fd_AUTHOR_biz_center": "int",
        "fd_AUTHOR_roi_convert_ratio": "double",
        "fd_AUTHOR_roi_convert_ratio_p2l": "double",
        "fd_AUTHOR_roi_convert_ratio_live": "double",
        "fd_AUTHOR_last_30d_cost": "double",
        "fd_AUTHOR_gmv_level": "int",
        "fd_AUTHOR_is_vh": "int",
        "fd_AUTHOR_author_today_cost": "int",
        "fd_AUTHOR_author_today_target_cost": "int",
        "fd_AUTHOR_author_today_discount_ratio": "double",
        "fd_AUTHOR_new_first_cost_date": "int",
        "fd_AUTHOR_account_sale_depart_level2": "int",
        "fd_AUTHOR_photo_coupon_id": "int",
        "fd_AUTHOR_live_coupon_id": "int",
        "fd_AUTHOR_pcoc_by_gender_age": "double_list",
        "fd_AUTHOR_pcoc_by_pred_cvr": "double_list",
        "fd_AUTHOR_max_sir_pctr": "double_list",
        "fd_AUTHOR_avg_sir_pctr": "double_list",
        "fd_AUTHOR_avg_sir_ctr": "double_list",
        "fd_AUTHOR_max_sir_pcvr": "double_list",
        "fd_AUTHOR_avg_sir_pcvr": "double_list",
        "fd_AUTHOR_avg_sir_cvr": "double_list",
        "fd_AUTHOR_tc_acc_explore_author_info": "double_list",
        "fd_AUTHOR_avg_sw_sir_pctcvr_list": "double_list",
        "fd_AUTHOR_avg_sw_sir_ctcvr_list": "double_list",
        "fd_AUTHOR_avg_sw_order_sir_pctcvr_list": "double_list",
        "fd_AUTHOR_avg_sw_order_sir_ctcvr_list": "double_list",
        "fd_ACCOUNT_account_advertiser_risk_label": "string",
        "fd_ACCOUNT_crm_agent_type": "int",
        "fd_ACCOUNT_pay_mode": "int",
        "fd_ACCOUNT_plus_new_customer_bonus": "int",
        "fd_ACCOUNT_smb_level_tag": "int",
        "fd_ACCOUNT_auto_manage_type": "int",
        "fd_ACCOUNT_account_auto_manage": "int",
        "fd_PRODUCT_category_level_1_id": "int",
        "fd_PRODUCT_category_level_2_id": "int",
        "fd_PRODUCT_category_level_3_id": "int",
        "fd_PRODUCT_good_quality": "int",
        "fd_PRODUCT_payment_per_order": "double",
        "fd_PRODUCT_spu_id_v1": "int",
        "fd_PRODUCT_kg_tag_ids": "int_list",
        "fd_PRODUCT_eco_minus_rate": "double",
        "fd_PRODUCT_product_tag": "int",
        "fd_PRODUCT_event_order_paid_cnt": "int",
        "fd_PRODUCT_rl_pay_amount_per_order": "double",
        "fd_PRODUCT_shop_promotion_product_3_h": "double",
        "fd_PRODUCT_order_num_product_3_h": "double",
        "fd_PRODUCT_sku_price_high_rate": "double",
        "fd_LIVE_item_impression": "int",
        "fd_LIVE_played_ns": "int",
        "fd_LIVE_goods_view": "int",
        "fd_LIVE_order_paied": "int",
        "fd_LIVE_job_id": "int_list",
        "fd_LIVE_org_id": "int_list",
        "fd_LIVE_job_category": "int_list",
        "fd_LIVE_job_address": "string_list",
        "fd_LIVE_first_index_time": "int",
        "fd_UNIT_first_index_time": "int",
        "fd_UNIT_event_order_paid_cnt": "int",
        "fd_UNIT_playlet_name": "string",
        "fd_UNIT_playlet_plot": "string",
        "fd_UNIT_playlet_theme": "string",
        "fd_UNIT_playlet_start_time": "int",
        "fd_UNIT_playlet_status": "int",
        "fd_UNIT_playlet_coin_tag": "int",
        "fd_UNIT_playlet_free_episode_num": "int",
        "fd_UNIT_playlet_commodity_id_str": "int_list",
        "fd_UNIT_playlet_price_str": "int_list",
        "fd_UNIT_playlet_lessons_str": "int_list",
        "fd_UNIT_user_intention_type": "int",
        "fd_UNIT_cost_total_7_day": "int",
        "fd_UNIT_cost_7_day": "int",
        "fd_UNIT_target_cost_7_day": "int",
        "fd_UNIT_item_impression_7_day": "int",
        "fd_UNIT_callback_purchase_amount_7_day": "double",
        "fd_UNIT_live_pcvr_sum_3_hour": "double",
        "fd_UNIT_live_pcvr_count_3_hour": "int",
        "fd_UNIT_live_pcvr_bucket_count_3_hour_list": "int_list",
        "fd_UNIT_ocpx_action_support_type": "int",
        "fd_UNIT_pc_target_cost_total_sum_price": "int",
        "fd_UNIT_pc_target_cost_total_num_order": "int",
        "fd_CAMPAIGN_target_cost_pro_total_sum_price": "double",
        "fd_CAMPAIGN_target_cost_pro_total_num_order": "double",
        "fd_CAMPAIGN_first_index_time": "int",
        "fd_CAMPAIGN_budget_schedule": "int_list",
        "fd_CAMPAIGN_day_budget": "int",
        "fd_CAMPAIGN_periodic_delivery_type": "int",
        "fd_CAMPAIGN_auto_manage": "int",
        "fd_CAMPAIGN_auto_build": "int",
        "fd_CAMPAIGN_auto_adjust": "int",
        "fd_wt_tube_table_cost_total_72_hour": "double",
        "fd_wt_tube_table_callback_purchase_amount_72_hour": "double",
        "fd_wt_tube_table_tube_status_72_hour": "int",
        "fd_UNIT_explore_put_type": "int",
        "fd_UNIT_brand_intention_code": "int",
        "fd_UNIT_brand_brand_code": "int",
        "fd_UNIT_brand_industry_code": "int",
        "fd_UNIT_dpa_product_brand_name": "string",
        "fd_ACCOUNT_client_second_industry_name": "string",
        "fd_ACCOUNT_tongxin_brand_name": "string",
        "fd_ACCOUNT_tongxin_second_category": "string",
        "fd_ACCOUNT_tongxin_directional_traffic": "double",
        "fd_ACCOUNT_tongxin_general_traffic": "double",
        "fd_ACCOUNT_tongxin_price": "int",
        "fd_ACCOUNT_crm_center": "int",
        "fd_ACCOUNT_crm_center_v2": "int",
        "fd_ACCOUNT_pv_drop_model_id": "string",
        "fd_ACCOUNT_maa_cost_level_90d": "int",
        "fd_ACCOUNT_pv_drop_model_type": "int",
        "fd_ACCOUNT_pv_drop_rule_strength": "int",
        "fd_ACCOUNT_tongxin_expore_hc_limit": "double",
        "fd_ACCOUNT_corporation_name": "string",
        "fd_ACCOUNT_lps_acq_gen_cmd_id_list": "int_list",
        "fd_ACCOUNT_lps_acq_gen_score_list": "double_list",
        "fd_ACCOUNT_pm_fwh_account_cost_info": "double_list",
        "fd_ACCOUNT_account_cost_info": "double_list",
        "fd_AUTHOR_history_roi0": "double",
        "fd_PRODUCT_is_ghot": "int",
        "fd_PRODUCT_is_ghot_v2": "int",
        "fd_PRODUCT_basic_price": "int",
        "fd_PRODUCT_item_price": "double",
        "fd_AUTHOR_first_cost_date": "int",
        "fd_ACCOUNT_first_cost_date_account": "int",
        "fd_AUTHOR_industry": "int",
        "fd_ACCOUNT_account_7d_imp": "int",
        "fd_ACCOUNT_account_7d_cost": "int",
        "fd_ACCOUNT_account_7d_gmv": "double",
        "fd_ACCOUNT_account_7d_fct": "int",
        "fd_AUTHOR_cost_level_92d_int": "int",
        "fd_AUTHOR_is_smb": "int",
        "fd_AUTHOR_is_92d_new_cur": "int",
        "fd_AUTHOR_is_xd_cost_cur": "int",
        "fd_AUTHOR_inner_author_bonus_level_cost": "int",
        "fd_AUTHOR_inner_author_bonus_level_gmv": "int",
        "fd_AUTHOR_inner_author_bonus_level_cost_2": "int",
        "fd_AUTHOR_inner_author_bonus_level_gmv_2": "int",
        "fd_AUTHOR_inner_author_bonus_level_newcus1": "int",
        "fd_AUTHOR_inner_author_bonus_level_newcus2": "int",
        "fd_AUTHOR_inner_author_bonus_level_newcus3": "int",
        "fd_AUTHOR_inner_author_bonus_level_newcus4": "int",
        "fd_AUTHOR_inner_author_today_cost": "int",
        "fd_AUTHOR_inner_author_last_29d_cost_outer": "int",
        "fd_AUTHOR_imp_1d": "int",
        "fd_AUTHOR_cost_7d": "int",
        "fd_AUTHOR_gmv_7d": "int",
        "fd_AUTHOR_fcd": "int",
        "fd_AUTHOR_author_to_call_card_type": "int",
        "fd_PHOTO_game_id": "string",
        "fd_ACCOUNT_ecpc_model_predict_id": "string",
        "fd_ACCOUNT_ecpc_model_type": "int",
        "fd_ACCOUNT_ecpc_rule_strength": "int",
        "fd_ACCOUNT_health_score": "double",
        "fd_AUTHOR_is_ecommerce": "int",
        "fd_AUTHOR_is_leads": "int",
        "fd_CAMPAIGN_is_pass_cold_start": "int",
        "fd_CAMPAIGN_is_pm_pass_cold_start": "int",
        "fd_PHOTO_item_impression_7_day": "int",
        "fd_LIVE_cost_total_3_hour": "int",
        "fd_PHOTO_photo_bounce_rate": "double",
        "fd_PHOTO_photo_bounce_rate_v2": "double",
        "fd_ACCOUNT_create_source": "int",
        "fd_ACCOUNT_self_service_account_order_cnt": "int",
        "fd_ACCOUNT_cid_goods_type": "int",
        "fd_ACCOUNT_group_idx": "int",
        "fd_LIVE_is_digital": "int",
        "fd_CAMPAIGN_pc_target_cost_total_sum_price": "int",
        "fd_CAMPAIGN_pc_target_cost_today_sum_timestamp": "int",
        "fd_CAMPAIGN_order_campaign_cost": "double",
        "fd_CAMPAIGN_order_campaign_target_cost": "double",
        "fd_CAMPAIGN_order_newcus_benefit_conversion_cnt": "int",
        "fd_CAMPAIGN_charge_mode": "int",
        "fd_PHOTO_clue_sdpa_category_id": "int",
        "fd_PHOTO_e_ad_item_impression_7day": "int",
        "fd_PHOTO_e_ad_item_click_7day": "int",
        "fd_PHOTO_conversion_num_7day": "int",
        "fd_PHOTO_ue_check_tag": "int",
        "fd_PHOTO_comment_cnt": "int",
        "fd_ACCOUNT_clue_aggr_define_client_name": "int",
        "fd_PHOTO_origin_similar_photo_id": "int",
        "fd_PHOTO_origin_similar_photo_score": "double",
        "fd_PHOTO_photo_delta_treatment": "double",
        "fd_PHOTO_item_photo_score": "double",
        "fd_PHOTO_item_p2l_score": "double",
        "fd_UNIT_clue_aggr_third_cate_id": "int",
        "fd_UNIT_clue_aggr_second_cate_id": "int",
        "fd_UNIT_series_id": "int",
        "fd_UNIT_playlet_third_part_show_count": "int",
        "fd_ad_magicsite_page_das_conversion_path": 'int',
        "fd_ad_magicsite_page_das_sub_conversion_path": 'int',
        "fd_UNIT_qcpx_put_type": "int",
        "fd_ad_coupon_template_coupon_type": 'int',
        "fd_ad_coupon_template_status": 'int',
        "fd_ad_coupon_template_rule": 'string',
        "fd_PRODUCT_avg_product_min_price": 'double',
        "fd_PRODUCT_mode_gmv": 'double',
        "fd_PRODUCT_order_paid_cnt": 'int',
        "fd_PRODUCT_gmv_greater_min_price_cnt": 'int',
        "fd_PRODUCT_mode_gmv_cnt": 'int',
        "fd_PRODUCT_avg_gmv": 'double',
        "fd_PRODUCT_median_gmv": 'double',
        "fd_PRODUCT_mode_gmv_greater_min_price_cnt": 'int',
        "fd_PRODUCT_order_cnt_7days": "int",
        "fd_PRODUCT_ee_discount_30days": "int",
        "fd_PRODUCT_qcpx_discount_cnt": 'int',
        "fd_PRODUCT_qcpx_discount_avg_amount": 'double',
        "fd_UNIT_combo_type": "int",
        "fd_wt_multi_table_double_value_1": 'double',
        "fd_AUTHOR_item_price_mode": "double",
        "fd_AUTHOR_gmv_mode": "double",
        "fd_ACCOUNT_clue_optimize_switch": "int_list",
        "fd_UNIT_fanstop_spu_id": "int",
        "fd_AUTHOR_shop_coupon_status": "int",
        "fd_PRODUCT_item_coupon_status": "int",
        "fd_PRODUCT_ecom_spu_id": "int",
        "fd_PHOTO_ecom_spu_id": "int",
        "fd_UNIT_creative_build_type": "int",
        "fd_AUTHOR_photo_roi_param": "double",
        "fd_AUTHOR_live_roi_param": "double",
        "fd_PHOTO_photo_semantic_id_level1": "int",
        "fd_PHOTO_photo_semantic_id_level2": "int",
        "fd_PHOTO_photo_semantic_id_level3": "int",
        "fd_PHOTO_md5": "int",
        "fd_PHOTO_native_degree_tag": "int",
        "fd_LIVE_inner_live_coldstart_tag": "int",
        "fd_UNIT_local_life_ad_layered_tags": "int_list",
        "fd_UNIT_submitted_landing_ctr": "double",
        "fd_UNIT_submitted_landing_cvr": "double",
        "fd_UNIT_leads_submitted_ctr": "double",
        "fd_UNIT_leads_submitted_cvr": "double",
        "fd_UNIT_game_duration_avg_1d": "int",
        "fd_UNIT_ltv_num_avg_1d": "int",
        "fd_PRODUCT_mode_item_price": "int",
        "fd_PRODUCT_mode_item_price_frequency": "int",
        "fd_PRODUCT_item_order_count": "int",
        "fd_CAMPAIGN_fans_top_extra_delivery": "int",
        "fd_CAMPAIGN_fans_top_new_user": "int",
        "fd_CAMPAIGN_benefit": "int",
        "fd_ACCOUNT_prms_conversion_cnt": "int",
        "fd_ACCOUNT_leads_conversion_cnt": "int",
        "fd_ACCOUNT_fanstop_first_category_name_author": "string",
        "fd_ACCOUNT_fanstop_customer_level_tag": "int",
        "fd_ACCOUNT_fanstop_account_is_xiaodian": "int",
        "fd_PHOTO_inner_photo_coldstart_tag": "int",
        "fd_PHOTO_inner_p2l_coldstart_tag": "int",
        "fd_PHOTO_photo_sub_source": "int",
        "fd_UNIT_auto_deliver_type": "int",
        "fd_UNIT_kwai_book_id": "int",
        "fd_UNIT_link_integration_type": "int",
        "fd_UNIT_excycle_skip": "int",
        "fd_ACCOUNT_iaa_game_6d_reward_cost": "double",
        "fd_ACCOUNT_iaa_game_today_reward_cost": "int",
        "fd_ACCOUNT_iaa_game_6d_reward_cost_no_game": "double",
        "fd_ACCOUNT_iaa_game_today_reward_cost_no_game": "int",
        "fd_PHOTO_photo_coldstart_item_score": "int",
        "fd_PHOTO_p2l_coldstart_item_score": "int",
        "fd_PRODUCT_price_percent": "double",
        "fd_PRODUCT_price_percent_cate_id": "int",
        "fd_AUTHOR_live_uniq_item_num_7d": "int",
        "fd_AUTHOR_live_order_num_7d": "int",
        "fd_AUTHOR_live_gmv_7d": "double",
        "fd_AUTHOR_live_gmv_price_ratio_7d": "double",
        "fd_AUTHOR_live_uniq_item_num_1d": "int",
        "fd_AUTHOR_live_order_num_1d": "int",
        "fd_AUTHOR_live_gmv_1d": "double",
        "fd_AUTHOR_live_gmv_price_ratio_1d": "double",
        "fd_UNIT_target_ecpc_status": "int",
        "fd_AUTHOR_live_coldstart_stage": "int",
        "fd_UNIT_locallife_item_promotion_flag": "int",
        "fd_UNIT_locallife_has_boost_items": "int",
        "fd_UNIT_locallife_has_high_traffic_items": "int",
        "fd_UNIT_locallife_has_kvi_items": "int",
        "fd_UNIT_locallife_has_subsidy_items": "int",
        "fd_UNIT_locallife_has_mkl_beat_items": "int",
        "fd_AUTHOR_coldstart_p2l_limit_usage": "double",
        "fd_AUTHOR_coldstart_photo_limit_usage": "double",
        "fd_AUTHOR_coldstart_live_limit_usage": "double",
        "fd_AUTHOR_coldstart_p2l_limit": "double",
        "fd_AUTHOR_coldstart_photo_limit": "double",
        "fd_AUTHOR_coldstart_live_limit": "double",
        "fd_PRODUCT_item_origin_price": "double",
        "fd_PHOTO_up_items_group": "int",
        "fd_PHOTO_up_items_tag": "int",
        "fd_PHOTO_photo_coldstart_predict_target_cost_fix": "double",
        "fd_PHOTO_photo_coldstart_target_cost_fix": "double",
        "fd_PHOTO_p2l_coldstart_predict_target_cost_fix": "double",
        "fd_PHOTO_p2l_coldstart_target_cost_fix": "double",
        "fd_PHOTO_sid": "int",
        "fd_LIVE_live_coldstart_rl_hc_ratio": "double",
        "fd_LIVE_live_coldstart_rl_hc_ratio_v2": "double",
        "fd_AUTHOR_coldstart_photo_limit_v2": "double",
        "fd_AUTHOR_coldstart_p2l_limit_v2": "double",
        "fd_PRODUCT_seller_bear_amt": "double",
        "fd_AUTHOR_pay_amount_author_3_h": "double",
        "fd_AUTHOR_real_pay_amount_author_3_h": "double",
        "fd_AUTHOR_platform_bear_amount_author_3_h": "double",
        "fd_AUTHOR_item_num_author_3_h": "double",
        "fd_AUTHOR_order_num_author_3_h": "double",
        "fd_AUTHOR_item_promotion_author_3_h": "double",
        "fd_AUTHOR_shop_promotion_author_3_h": "double",
        "fd_LIVE_pay_amount_live_stream_1_h": "double",
        "fd_LIVE_real_pay_amount_live_stream_1_h": "double",
        "fd_LIVE_platform_bear_amount_live_stream_1_h": "double",
        "fd_LIVE_item_num_live_stream_1_h": "double",
        "fd_LIVE_order_num_live_stream_1_h": "double",
        "fd_LIVE_item_promotion_live_stream_1_h": "double",
        "fd_LIVE_shop_promotion_live_stream_1_h": "double",
        "fd_CAMPAIGN_main_ecpm_auto_24h": "double",
        "fd_CAMPAIGN_main_ecpm_24h": "double",
        "fd_CAMPAIGN_qcpx_ecpm_auto_24h": "double",
        "fd_CAMPAIGN_qcpx_ecpm_24h": "double",
        "fd_CAMPAIGN_q_cost_24h": "double",
        "fd_LIVE_min_gmv_live_stream_1_h": "double",
        "fd_LIVE_max_gmv_live_stream_1_h": "double",
        "fd_AUTHOR_min_gmv_author_3_h": "double",
        "fd_AUTHOR_max_gmv_author_3_h": "double",
    }
}

ad_dsp_creative_fields = {
    "ad_dsp_creative": {
        "local_field_alias": {
            "first_audit_passtime": "fd_CREATIVE_first_audit_passtime",
            "new_creative_key": "fd_CREATIVE_new_creative_key",
            "new_creative_value": "fd_CREATIVE_new_creative_value",
            "creative_photo_source": "fd_CREATIVE_creative_photo_source",
            "kol_user_type": "fd_CREATIVE_kol_user_type",
            "unit_exp_feature_id": "fd_CREATIVE_unit_exp_feature_id",
        },
        "item_table_key_attr": "creative_id",
    }
}

wt_photo_fields = {
    "first_level_category_id": "fd_PHOTO_first_level_category_id",
    "second_level_category_id": "fd_PHOTO_second_level_category_id",
    "third_level_category_id": "fd_PHOTO_third_level_category_id",
    "product_cluster_id_v1": "fd_PHOTO_product_cluster_id_v1",
    "product_cluster_id_v2": "fd_PHOTO_product_cluster_id_v2",
    "is_youzhi": "fd_PHOTO_is_youzhi",
    "similar_photo_id": "fd_PHOTO_similar_photo_id",
    "similar_photo_id_new": "fd_PHOTO_similar_photo_id_new",
    "similar_score": "fd_PHOTO_similar_score",
    "similar_score_new": "fd_PHOTO_similar_score_new",
    "similar_type": "fd_PHOTO_similar_type",
    "spu_id_v3": "fd_PHOTO_spu_id_v3",
    "photo_quality_score": "fd_PHOTO_photo_quality_score",
    "old_similar_score": "fd_PHOTO_old_similar_score",
}

ad_dsp_photo_fields = {
    "photo_source": "fd_PHOTO_photo_source",
}

author_fields = {
    "exp_score": "fd_AUTHOR_exp_score",
    "rainbow_level": "fd_AUTHOR_rainbow_level",
}

ad_dsp_account_fields = {
    "create_time": "fd_ACCOUNT_create_time",
    "account_mark": "fd_ACCOUNT_account_mark",
    "stable_category_third_id": "fd_ACCOUNT_stable_category_third_id",
    "licence_id_num": "fd_ACCOUNT_licence_id_num",
    "first_industry_id": "fd_ACCOUNT_first_industry_id",
}

wt_account_fields = {
    "agent_id": "fd_ACCOUNT_agent_id",
    "price_adjust_ratio" : "fd_ACCOUNT_price_adjust_ratio",
    "ad_direct_merchant_stage" : "fd_ACCOUNT_ad_direct_merchant_stage",
    "ad_direct_merchant_biz": "fd_ACCOUNT_ad_direct_merchant_biz",
    "bid_adjust_ratio": "fd_ACCOUNT_bid_adjust_ratio",
    "merchant_goods_price": "fd_ACCOUNT_merchant_goods_price",
    "transfer_stage": "fd_ACCOUNT_transfer_stage",
    "cid_spu_id": "fd_ACCOUNT_cid_spu_id",
    "cid_spu_type": "fd_ACCOUNT_cid_spu_type",
    "cid_spu_bid_ratio": "fd_ACCOUNT_cid_spu_bid_ratio",
    "cid_spu_price_ratio": "fd_ACCOUNT_cid_spu_price_ratio",
}

product_fields = {
    "x7_entity_id": "fd_PRODUCT_x7_entity_id",
    "ks_brand_name_hash": "fd_PRODUCT_ks_brand_name_hash",
    "mmu_a_category_id": "fd_PRODUCT_mmu_a_category_id",
    "spu_entity_cluster_id": "fd_PRODUCT_spu_entity_cluster_id",
    "category_level_4_id": "fd_PRODUCT_category_level_4_id",
}

merchant_product_remote_table_relation = {
    "ad_dsp_merchant_product_info": {
        "local_field_alias": {
            "product_max_price": "fd_PRODUCT_product_max_price",
            "product_min_price": "fd_PRODUCT_product_min_price",
        },
        "item_table_key_attr": "merchant_product_id",
    },
}

live_remote_table_relation = {
    "ad_dsp_live_stream_user_info": {
        "local_field_alias": {
            "is_live": "fd_LIVE_is_live",
            "event_time": "fd_LIVE_event_time",
        },
        "item_table_key_attr": "author_id",
    },
}

ad_dsp_unit_fields = {
    "mcb_value_type": "fd_UNIT_mcb_value_type",
    "has_merchant_small_shop_support_info_optional": "fd_UNIT_has_merchant_small_shop_support_info_optional",
    "product_id": "fd_UNIT_product_id",
    "put_type": "fd_UNIT_put_type",
    "item_id": "fd_UNIT_item_id",
    "cpa_bid": "fd_UNIT_cpa_bid",
    "has_unit_support_info_optional": "fd_UNIT_has_unit_support_info_optional",
    "fiction_id": "fd_UNIT_fiction_id",
    "new_spu_tag": "fd_UNIT_new_spu_tag",
    "simultaneous_optimization_selected": "fd_UNIT_simultaneous_optimization_selected",
    "simultaneous_optimization_type": "fd_UNIT_simultaneous_optimization_type",
}

ad_dsp_campaign_fields = {
    "internal_invest_plan_id": "fd_CAMPAIGN_internal_invest_plan_id",
    "storewide_incompatible_type": "fd_CAMPAIGN_storewide_incompatible_type",
    "create_source_type": "fd_CAMPAIGN_create_source_type",
}

app_release_remote_table_relation = {
    "ad_app_release": {
        "local_field_alias": {
            "global_app_id": "fd_app_release_global_app_id",
        },
        "item_table_key_attr": "package_id",
    }
}

ecom_hosting_project_remote_table_relation = {
    "ad_dsp_ecom_hosting_project": {
        "local_field_alias": {
            "ocpx_action_type": "fd_ecom_hosting_project_ocpx_action_type",
            "roi_ratio": "fd_ecom_hosting_project_roi_ratio",
        },
        "item_table_key_attr": "campaign_id",
    },
}

account_status_remote_table_relation = {
    "account_status": {
        "local_field_alias": {
            "reject_type": "fd_account_status_reject_type",
        },
        "item_table_key_attr": "account_id",
    },
}

new_fields_type = {
    "fd_app_release_global_app_id": "int",
    "fd_ecom_hosting_project_ocpx_action_type": "int",
    "fd_ecom_hosting_project_roi_ratio": "double",
    "fd_ACCOUNT_create_time": "int",
    "fd_ACCOUNT_account_mark": "int",
    "fd_ACCOUNT_stable_category_third_id": "int_list",
    "fd_ACCOUNT_price_adjust_ratio": "double",
    "fd_ACCOUNT_ad_direct_merchant_stage": "int",
    "fd_ACCOUNT_agent_id": "int",
    "fd_ACCOUNT_licence_id_num": "int",
    "fd_ACCOUNT_ad_direct_merchant_biz": "int",
    "fd_ACCOUNT_bid_adjust_ratio": "double",
    "fd_ACCOUNT_merchant_goods_price": "double",
    "fd_ACCOUNT_transfer_stage": "int",
    "fd_ACCOUNT_cid_spu_id": "int",
    "fd_ACCOUNT_cid_spu_type": "int",
    "fd_ACCOUNT_cid_spu_bid_ratio": "double",
    "fd_ACCOUNT_cid_spu_price_ratio": "double",
    "fd_ACCOUNT_first_industry_id": "int",
    "fd_account_status_reject_type": "int",
    "fd_CREATIVE_first_audit_passtime": "int",
    "fd_PHOTO_similar_photo_id": "int",
    "fd_PHOTO_similar_photo_id_new": "int",
    "fd_PHOTO_first_level_category_id": "int",
    "fd_PHOTO_second_level_category_id": "int",
    "fd_PHOTO_third_level_category_id": "int",
    "fd_PHOTO_product_cluster_id_v1": "int",
    "fd_PHOTO_product_cluster_id_v2": "int",
    "fd_PHOTO_is_youzhi": "int",
    "fd_PHOTO_similar_score": "double",
    "fd_PHOTO_similar_score_new": "double",
    "fd_PHOTO_similar_type": "int",
    "fd_PHOTO_spu_id_v3": "int",
    "fd_PHOTO_photo_quality_score": "double",
    "fd_PHOTO_old_similar_score": "int",
    "fd_PHOTO_photo_source": "int",
    "fd_AUTHOR_exp_score": "double",
    "fd_CREATIVE_new_creative_key": "int_list",
    "fd_CREATIVE_new_creative_value": "int_list",
    "fd_CREATIVE_creative_photo_source": "int",
    "fd_CREATIVE_kol_user_type": "int",
    "fd_CREATIVE_unit_exp_feature_id": "int_list",
    "fd_UNIT_has_unit_support_info_optional": "int",
    "fd_UNIT_fiction_id": "int",
    "fd_UNIT_new_spu_tag": "int",
    "fd_PRODUCT_product_max_price": "int",
    "fd_PRODUCT_product_min_price": "int",
    "fd_PRODUCT_x7_entity_id": "int",
    "fd_PRODUCT_ks_brand_name_hash": "int",
    "fd_PRODUCT_mmu_a_category_id": "int",
    "fd_PRODUCT_spu_entity_cluster_id": "int",
    "fd_UNIT_simultaneous_optimization_selected": "int",
    "fd_UNIT_simultaneous_optimization_type": "int",
    "fd_LIVE_is_live": "int",
    "fd_LIVE_event_time": "int",
    "fd_AUTHOR_rainbow_level": "int",
    "fd_PRODUCT_category_level_4_id": "int",
    "fd_UNIT_put_type": "int",
    "fd_UNIT_item_id": "int",
    "fd_UNIT_cpa_bid": "int",
    "fd_UNIT_mcb_value_type": "int",
    "fd_UNIT_has_merchant_small_shop_support_info_optional": "int",
    "fd_UNIT_product_id": "int",
    "fd_CAMPAIGN_internal_invest_plan_id": "int",
    "fd_CAMPAIGN_storewide_incompatible_type": "int",
    "fd_CAMPAIGN_create_source_type": "int",
}


coupon_table_config = {
    "item_tables": ["ad_coupon_table"],
    "remote_table_relation": {
        "wt_ad_coupon_template_rel_x7": {
            "local_field_alias": {
                "coupon_type": "fd_ad_coupon_template_coupon_type",
                "status": "fd_ad_coupon_template_status",
                "rule": "fd_ad_coupon_template_rule",
            },
            "item_table_key_attr": "coupon_config_id",
        },     
    },
    "local_field_type":{
        "fd_ad_coupon_template_coupon_type": 'int',
        "fd_ad_coupon_template_status": 'int',
        "fd_ad_coupon_template_rule": 'string',
    }
}

feature_config = {
    "remote_service": "ad-feature-proxy",
    "feature_use_cache": True,
    **global_ad_table_config,
}
feature_multi_table_config = {
    "remote_service": "ad-feature-proxy",
    "feature_use_cache": True,
    "request_tables": {
        "global_ad_table": global_ad_table_config,
        "ad_coupon_table": coupon_table_config,
    }
}

def gen_item_attr(config:dict):
    """
    Purpose: 转换 schema 到 rank schema 格式
    """
    attrs = []
    for field_name, f_type in config['local_field_type'].items():
        attr = {}
        attr[field_name] = f_type
        attrs.append(attr)
    return attrs

def gen_diff_config(config:dict):
    """
    Purpose: 转换 schema 到 diff 格式
    """
    ret = copy.deepcopy(config)
    request_table = ret['request_tables']['global_ad_table']
    request_table['local_field_type'] = {f"{key}_zhangruyuan_diff": request_table['local_field_type'][key] for key in request_table['local_field_type'].keys()}
    for table,table_config in request_table['remote_table_relation'].items():
        table_config['local_field_alias'] = {key: f"{table_config['local_field_alias'][key]}_zhangruyuan_diff" for key in table_config['local_field_alias'].keys()}
    return ret

def get_local_attr(config:dict):
    """
    Purpose: 获取本地列
    """
    ret = []
    ret = [key for key in config['local_field_type'].keys()]
    return ret

def add_new_fields_for_table(table_config, ad_dsp_creative, wt_photo, ad_dsp_photo, author, wt_account, ad_dsp_account, product,
        unit, campaign, app_release, ecom_hosting_project, account_status, merchant_product, live, new_fields_type):
    table_config_new = copy.deepcopy(table_config)
    table_config_new["remote_table_relation"].update(ad_dsp_creative)
    table_config_new["remote_table_relation"]["wt_photo"]["local_field_alias"].update(wt_photo)
    table_config_new["remote_table_relation"]["ad_dsp_photo"]["local_field_alias"].update(ad_dsp_photo)
    table_config_new["remote_table_relation"]["wt_author"]["local_field_alias"].update(author)
    table_config_new["remote_table_relation"]["wt_account"]["local_field_alias"].update(wt_account)
    table_config_new["remote_table_relation"]["ad_dsp_account"]["local_field_alias"].update(ad_dsp_account)
    table_config_new["remote_table_relation"]["wt_product"]["local_field_alias"].update(product)
    table_config_new["remote_table_relation"]["ad_dsp_unit"]["local_field_alias"].update(unit)
    table_config_new["remote_table_relation"]["ad_dsp_campaign"]["local_field_alias"].update(campaign)
    table_config_new["remote_table_relation"].update(app_release)
    table_config_new["remote_table_relation"].update(ecom_hosting_project)
    table_config_new["remote_table_relation"].update(account_status)
    table_config_new["remote_table_relation"].update(merchant_product)
    table_config_new["remote_table_relation"].update(live)
    table_config_new["local_field_type"].update(new_fields_type)
    return table_config_new

attrs = gen_item_attr(feature_config)
feature_diff_config = gen_diff_config(feature_multi_table_config)

new_global_ad_table_config = add_new_fields_for_table(global_ad_table_config, ad_dsp_creative_fields, wt_photo_fields, ad_dsp_photo_fields,
    author_fields, wt_account_fields, ad_dsp_account_fields, product_fields, ad_dsp_unit_fields, ad_dsp_campaign_fields,
    app_release_remote_table_relation, ecom_hosting_project_remote_table_relation,
    account_status_remote_table_relation, merchant_product_remote_table_relation,
    live_remote_table_relation, new_fields_type)

new_feature_config = {
    "remote_service": "ad-feature-proxy",
    "feature_use_cache": True,
    **new_global_ad_table_config,
}

new_feature_multi_table_config = {
    "remote_service": "ad-feature-proxy",
    "feature_use_cache": True,
    "request_tables": {
        "global_ad_table": new_global_ad_table_config,
        "ad_coupon_table": coupon_table_config,
    }
}

search_global_ad_table_config = {
    "item_tables": ["global_ad_table"],
    "remote_table_relation": {
        "PRODUCT": {
            "local_field_alias": {
                "payment_per_order": "fd_PRODUCT_payment_per_order",
                "item_price": "fd_PRODUCT_item_price",
                "min_price": "fd_PRODUCT_min_price"
            },
            "item_table_key_attr": "merchant_product_id"
        },
        "LIVE": {
            "local_field_alias": {
                "is_digital": "fd_LIVE_is_digital"
            },
            "item_table_key_attr": "author_id"
        },
        "AUTHOR": {
            "local_field_alias": {
                "item_price_mode": "fd_AUTHOR_item_price_mode",
                "gmv_mode": "fd_AUTHOR_gmv_mode"
            },
            "item_table_key_attr": "author_id"
        }
    },
    "local_field_type": {
        "fd_PRODUCT_payment_per_order": "double",
        "fd_PRODUCT_item_price": "double",
        "fd_PRODUCT_min_price": "int",
        "fd_LIVE_is_digital": "int",
        "fd_AUTHOR_item_price_mode": "double",
        "fd_AUTHOR_gmv_mode": "double"
    }
}

search_new_feature_multi_table_config = {
    "remote_service": "ad-feature-proxy",
    "feature_use_cache": True,
    "request_tables": {
        "global_ad_table": search_global_ad_table_config,
        "ad_coupon_table": coupon_table_config,
    }
}

new_attrs = gen_item_attr(new_feature_config)
new_feature_diff_config = gen_diff_config(new_feature_multi_table_config)


def gen_diff_config_opt(config:dict, suffix = "_diff"):
    """
    Purpose: 转换 schema 到 diff 格式
    """
    ret = copy.deepcopy(config)
    tmp = {}
    for table, request_table in ret['request_tables'].items():
      request_table['local_field_type'] = {f"{key}{suffix}": request_table['local_field_type'][key] for key in request_table['local_field_type'].keys()}
      for sub_table, table_config in request_table['remote_table_relation'].items():
        table_config['local_field_alias'] = {key: f"{table_config['local_field_alias'][key]}{suffix}" for key in table_config['local_field_alias'].keys()}
      tmp[f"{table}{suffix}"] = request_table
    ret['request_tables'] = tmp

    return ret

def get_diff_attr_opt(config:dict, base_suffix = "", exp_suffix = "_diff"):
    """
    Purpose: 为 diff 算子提供配置
    """
    ret = []
    for table, request_table in config['request_tables'].items():
      one_table = {}
      one_table["table_name"] = table
      one_table["item_tables"] = request_table["item_tables"]
      diff = []
      for table, table_config in request_table['remote_table_relation'].items():
         ref_col = table_config["item_table_key_attr"]
         for col in table_config['local_field_alias'].values():
            diff += [{"base_col": f'{col}{base_suffix}', "diff_col": f'{col}{exp_suffix}', "ref_col": ref_col}]
      one_table["diff_attrs"] = diff
      ret.append(one_table)
    return ret
