#pragma once

#include <string>
#include <memory>
#include <vector>
#include "dragon/src/processor/base/common_reco_base_mixer.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_rank/common/factor_udf.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "teams/ad/ad_rank/processor/framework/common_defs.h"


namespace ks {
namespace ad_rank {

template<typename T>
struct UdfInfo {
  UdfInfo(
      std::string udf_name, int factor_tag, const std::string& factor_tag_name, const std::string& owner)
    :udf_name(udf_name), factor_tag(factor_tag), factor_tag_name(factor_tag_name), owner(owner) { }

  std::string udf_name;
  int factor_tag;
  std::string factor_tag_name;
  std::string owner;
  bool is_initialized = false;
  std::unique_ptr<FactorUdf<T>> udf = nullptr;
  int64_t process_cnt = 0;
};

template<typename T>
class FactorUdfManager : public ks::platform::CommonRecoBaseMixer {
 public:
  FactorUdfManager() = default;
  virtual ~FactorUdfManager() = default;

 public:
  void Mix(ks::platform::AddibleRecoContextInterface* context) override {
    if (!Initialize(context)) {
      return;
    }
    ad_list_->ForEach([&] (AdCommon* p_ad) {
      ASSERT_NOT_NULL_OTHERWISE_RETURN(p_ad);
      CalculateFactor(p_ad);
    });
    PostProcess();
    for (const auto& udf_info : udf_info_list_) {
      session_data_->dot_perf->Interval(
        udf_info.process_cnt, "factor_udf_manager.process_count",
        udf_info.udf_name, udf_info.factor_tag_name, table_name_);
      session_data_->dot_perf->Count(
        1, "factor_udf_manager.initialize", udf_info.udf_name, udf_info.factor_tag_name, table_name_,
        udf_info.is_initialized ? "true" : "false");
    }
  }

  bool InitProcessor() override {
    table_name_ = GetTableName();
    ASSERT_OTHERWISE_RETURN_WITH(utility::IsTableNameValid(table_name_), false);
    auto udf_list = config()->Get("udf_list");
    if (!udf_list || !udf_list->IsArray()) {
      LOG(ERROR) << GetName()
                 << " init failed! Missing \"udf_list\" config"
                 << " or it is not an array.";
      return false;
    }
    for (const auto* udf_json : udf_list->array()) {
      if (!udf_json || !udf_json->IsObject()) {
        LOG(ERROR) << GetName()
                   << " init failed! Item of mappings should be a dict!"
                   << " Value found: " << udf_json->ToString();
        return false;
      }
      auto udf_name = udf_json->GetString("name", "");
      if (udf_name.empty()) {
        LOG(ERROR) << GetName()
                   << " init failed! udf_list.name should not be empty!";
        return false;
      }
      if (!FactorUdf<T>::Factory::IsRegistered(udf_name)) {
        LOG(ERROR) << GetName()
                   << " init failed! udf " << udf_name
                   << " is not registered, call `REGISTER_FACTOR_UDF(IMPL, OUTPUT_TYPE)` first.";
        return false;
      }
      int64_t factor_tag = 0;
      std::string factor_tag_name;
      const auto* factor_json = udf_json->Get("factor_tag");
      get_enum_by_json(factor_json, &factor_tag_name, &factor_tag);
      if (factor_tag_name.empty() || factor_tag == INVALID_ENUM) {
        LOG(ERROR) << GetName()
                   << " init failed! udf " << udf_name
                   << " has no 'factor_tag'";
        return false;
      }
      std::string owner = udf_json->GetString("owner", "");
      if (owner.empty()) {
        LOG(ERROR) << GetName()
                   << " init failed! udf " << udf_name
                   << " has no 'owner'";
        return false;
      }
      udf_info_list_.emplace_back(udf_name, factor_tag, factor_tag_name, owner);
    }
    return InitProcessorImpl();
  }

  void OnPipelineExit(ks::platform::ReadableRecoContextInterface *context) override {
    session_data_ = nullptr;
    ad_list_ = nullptr;
    for (auto& udf_info : udf_info_list_) {
      udf_info.udf = nullptr;
      udf_info.is_initialized = false;
      udf_info.process_cnt = 0;
    }
    OnPipelineExitImpl(context);
  }

 private:
  bool Initialize(ks::platform::AddibleRecoContextInterface* context) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(context, false);
    session_data_ = utility::GetContextData(context);
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(session_data_, false);
    ad_list_ = utility::GetAdListByTable(session_data_, table_name_);
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(ad_list_, false);
    for (auto& udf_info : udf_info_list_) {
      udf_info.udf = std::unique_ptr<FactorUdf<T>>(
        FactorUdf<T>::Factory::NewInstance(udf_info.udf_name, *session_data_));
      ASSERT_NOT_NULL_OTHERWISE_CONTINUE(udf_info.udf);
      if (!udf_info.udf->Initialize()) {
        continue;
      }
      udf_info.is_initialized = true;
    }

    return InitializeImpl(context);
  }

 private:
  virtual inline std::string GetName() const = 0;
  virtual void DealValue(AdCommon* p_ad, T value, const UdfInfo<T>& udf_info) = 0;

  virtual bool InitProcessorImpl() { return true; }
  virtual bool InitializeImpl(ks::platform::AddibleRecoContextInterface* context) { return true; }
  virtual void OnPipelineExitImpl(ks::platform::ReadableRecoContextInterface *context) { }
  virtual void PostProcess() { }
  virtual bool AdPreProcess(AdCommon* p_ad) { return true; }

  virtual void CalculateFactor(AdCommon* p_ad) {
    if (!AdPreProcess(p_ad)) {
      return;
    }
    for (auto& udf_info : udf_info_list_) {
      if (!udf_info.is_initialized) {
        continue;
      }
      const auto& udf = udf_info.udf;
      ASSERT_NOT_NULL_OTHERWISE_CONTINUE(udf);
      if (!udf->IsAdmit(p_ad)) {
        continue;
      }
      ++udf_info.process_cnt;
      T value = udf->Compute(p_ad);
      DealValue(p_ad, value, udf_info);
    }
  }

 protected:
  ContextData* session_data_ = nullptr;
  AdList* ad_list_ = nullptr;
  std::string table_name_;
  std::vector<UdfInfo<T>> udf_info_list_;

 private:
  DISALLOW_COPY_AND_ASSIGN(FactorUdfManager);
};

}  // namespace ad_rank
}  // namespace ks
