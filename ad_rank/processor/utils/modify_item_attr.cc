#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <set>
#include "teams/ad/ad_rank/processor/utils/modify_item_attr.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/engine_base/utils/ad_utility.h"
#include "teams/ad/ad_rank/data/p2p_data/dup_photo_id_list/dup_photo_id_list.h"
namespace ks {
namespace ad_rank {

using BidStrategyGroupType = kuaishou::ad::AdEnum::BidStrategyGroup;

bool ModifyItemAttr::InitProcessor() {
  item_table_name_ = config()->GetString("item_table_name", "");
  if (item_table_name_.empty()) {
    LOG(ERROR) << "ModifyItemAttr init failed! item_table_name is empty";
    return false;
  }
  return true;
}

bool ModifyItemAttr::PvInit(ks::platform::AddibleRecoContextInterface* context) {
  // todo: add switch
  reco_context = context;
  auto ps_context_wrapper = context->GetMutablePtrCommonAttr<AdContextWrapper>("ad_context_wrapper");
  if (!ps_context_wrapper || !ps_context_wrapper->Get()) {
    return false;
  }
  auto ps_context = ps_context_wrapper->Get();
  session_data = ps_context->GetMutableContextData<ContextData>();
  if (!session_data) {return false;}
  return true;
}

void ModifyItemAttr::Mix(ks::platform::AddibleRecoContextInterface* context) {
  if (!PvInit(context)) {
    LOG_EVERY_N(ERROR, 10000) << "failed to init ModifyItemAttr";
    return;
  }
  ModifyItem(context);
}


void ModifyItemAttr::ModifyItem(ks::platform::AddibleRecoContextInterface* context) {
  absl::flat_hash_set<int64> purchase_shop_id_set;  //  用户购买商品集合，用来判断是否店铺新客
  if (session_data->get_rank_request()->ad_request().ad_user_info().purchase_shop_id_list_size() > 0) {
    const auto& purchase_shop_id_list =
        session_data->get_rank_request()->ad_request().ad_user_info().purchase_shop_id_list();
    purchase_shop_id_set.insert(purchase_shop_id_list.begin(), purchase_shop_id_list.end());
  }

  absl::flat_hash_set<int64> brother_fans_follow_set;
  absl::flat_hash_set<int64> new_fans_follow_set;
  const auto& user_info =  session_data->get_rank_request()->ad_request().ad_user_info();
  for (size_t i = 0; i < user_info.strategy_crowd_info_size(); ++i) {
    const kuaishou::ad::StrategyCrowdTag &strategy_crowd_info = user_info.strategy_crowd_info(i);
    for (size_t j = 0; j < strategy_crowd_info.tag_size(); ++j) {
      if (strategy_crowd_info.tag(j) == kuaishou::ad::StrategyCrowdTag::BROTHER_FANS) {
        brother_fans_follow_set.insert(strategy_crowd_info.author_id());
      }
      if (strategy_crowd_info.tag(j) == kuaishou::ad::StrategyCrowdTag::NEW_FANS_FROM_ESP) {
        new_fans_follow_set.insert(strategy_crowd_info.author_id());
      }
    }
  }

  // 实时关注关系
  absl::flat_hash_set<int64> seven_day_follow_set;
  absl::flat_hash_set<int64> one_day_follow_set;
  if (user_info.has_innerloop_user_data() &&
      user_info.innerloop_user_data().has_il_uds_target_response()) {
    for (auto &follow : user_info.innerloop_user_data().il_uds_target_response().follow()) {
      if (follow.time_windonw() ==
          kuaishou::ad::innerloop::userdata::UmdcTargetData_TimeWindowType_TIME_WINDOWN_1_DAYS) {
        for (const auto &follow_author : follow.author_list()) {
          seven_day_follow_set.insert(follow_author);
        }
      }
      if (follow.time_windonw() ==
          kuaishou::ad::innerloop::userdata::UmdcTargetData_TimeWindowType_TIME_WINDOWN_7_DAYS) {
        for (const auto &follow_author : follow.author_list()) {
          one_day_follow_set.insert(follow_author);
        }
      }
    }
  }

  auto unique_id_2_ad_map = std::make_shared<std::unordered_map<uint64_t, AdCommon*>>();
  std::shared_ptr<absl::flat_hash_set<int64_t>>
    middle_page_account_set = RankKconfUtil::middlePageAccountSet();

  int cost_rank_thr = SPDM_new_spu_dup_photo_cost_rank_thre(session_data->get_spdm_ctx());
  auto dup_photo_id_list = DupPhotoIdList::GetInstance();
  bool enable_fix_dup_photo_id = SPDM_enableFixDupPhotoId();

  bool enable_q3_hc_crm_center =
        SPDM_enable_q3_hc_crm_center(session_data->get_spdm_ctx());
  auto mock_crm_center_industry_hc = RankKconfUtil::mockCrmCenterIndustryHc();
  auto qw_account_map = RankKconfUtil::QWAccountMap();
  auto sx_account_map = RankKconfUtil::SXAccountMap();
  bool enable_outside_zhuanxiang = false;
  bool enable_outside_zhuanxiang_1000001 = false;
  bool enable_outside_zhuanxiang_1000002 = false;
  bool enable_outside_zhuanxiang_1000003 = false;
  bool enable_outside_zhuanxiang_1000005 = false;
  if (mock_crm_center_industry_hc != nullptr) {
    auto iter_g = mock_crm_center_industry_hc->find("0_mock_crm_center");
    if (iter_g != mock_crm_center_industry_hc->end() &&
        iter_g->second > 0) {
      enable_outside_zhuanxiang = true;
      auto iter = mock_crm_center_industry_hc->find("0_1000001");
      if (iter != mock_crm_center_industry_hc->end() &&
          iter->second > 0) {
        enable_outside_zhuanxiang_1000001 = true;
      }
      iter = mock_crm_center_industry_hc->find("0_1000002");
      if (iter != mock_crm_center_industry_hc->end() &&
          iter->second > 0) {
        enable_outside_zhuanxiang_1000002 = true;
      }
      iter = mock_crm_center_industry_hc->find("0_1000003");
      if (iter != mock_crm_center_industry_hc->end() &&
          iter->second > 0) {
        enable_outside_zhuanxiang_1000003 = true;
      }
      iter = mock_crm_center_industry_hc->find("0_1000005");
      if (iter != mock_crm_center_industry_hc->end() &&
          iter->second > 0) {
        enable_outside_zhuanxiang_1000005 = true;
      }
    }
  }
  bool enable_move_crm_center_code =
   SPDM_enable_move_crm_center_code(session_data->get_spdm_ctx());

  auto* item_table = context->GetMutableTable(item_table_name_);
  if (!item_table) {
    LOG_EVERY_N(ERROR, 1000) << "ModelRegister: can not get item table";
    return;
  }
  auto& item_list = item_table->GetCommonRecoResults();
  if (item_list.empty()) {
    // LOG_EVERY_N(ERROR, 1000) << "ModelRegister: item table [" << item_table_name_ << "] is empty.";
    return;
  }
  auto* ad_common_attr = item_table->GetOrInsertAttr("ad_common_ptr");
  bool is_shelf = session_data->get_pos_manager_base().IsShelfMerchantTraffic();
  int64_t subpage_id = session_data->get_sub_page_id();
  bool is_reward = RankKconfUtil::qcpxRewardAdmitSubpageid()->count(subpage_id) > 0;
  std::unordered_map<int64_t, std::unordered_set<int64_t>> pkg2coupons;
  auto qcpxRewardPkg = RankKconfUtil::qcpxPackageConfigRelationReward();  // NOLINT
  if (RankKconfUtil::qcpxPackageConfigRelation() != nullptr) {
    pkg2coupons = RankKconfUtil::qcpxPackageConfigRelation()->data().pkg2coupons;
  }
  if (is_shelf && RankKconfUtil::qcpxPackageConfigRelationShelf() != nullptr) {  // NOLINT
    pkg2coupons = RankKconfUtil::qcpxPackageConfigRelationShelf()->data().pkg2coupons_shelf;
  }
  if (is_reward && SPDM_enable_qcpx_reward_use_independent_coupon_relation(session_data->get_spdm_ctx()) && qcpxRewardPkg!= nullptr) {  // NOLINT
    pkg2coupons = qcpxRewardPkg->data().pkg2coupons_reward;
  }
  for (auto &item : item_list) {
    auto* p_ad = ad_common_attr->GetMutablePtrValue<AdCommon>(item.GetAttrIndex());
    if (!p_ad) continue;

    p_ad->Attr(ItemIdx::is_inner_loop_ad).SetIntValue(
      p_ad->AttrIndex(),
      engine_base::ad_utility::IsInnerLoopCampaign(p_ad->get_campaign_type()) ? 1 : 0,
      false, false);

    // qcpx
    int64_t tmp_id = 0;
    if (p_ad->Is(AdFlag::is_merchant_live)) {
      tmp_id = p_ad->Attr(ItemIdx::shop_coupon_config_id).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    } else if (p_ad->Is(AdFlag::is_merchant_photo)) {
      tmp_id = p_ad->Attr(ItemIdx::item_coupon_config_id).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    }
    p_ad->Attr(ItemIdx::coupon_config_id).SetIntValue(p_ad->AttrIndex(), tmp_id, false, false); // NOLINT
    // qcpx 券包
    int64_t pkg_id = 0;
    if (p_ad->Is(AdFlag::is_merchant_live)) {
      pkg_id = p_ad->Attr(ItemIdx::shop_coupon_package_id).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    } else if (p_ad->Is(AdFlag::is_merchant_photo)) {
      pkg_id = p_ad->Attr(ItemIdx::item_coupon_package_id).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    }
    p_ad->Attr(ItemIdx::coupon_package_id).SetIntValue(p_ad->AttrIndex(), pkg_id, false, false); // NOLINT
    if (!pkg2coupons.empty()) {
      auto iter = pkg2coupons.find(pkg_id);
      if (iter != pkg2coupons.end()) {
        p_ad->Attr(ItemIdx::coupon_config_id_list).SetIntListValue(p_ad->AttrIndex(), {iter->second.begin(), iter->second.end()}, false, false); // NOLINT
      }
    }

    // wt_multi_table -> pre_pcoc_id
    std::string s_author_pcoc = "";
    s_author_pcoc = absl::StrCat(absl::StrCat(p_ad->get_author_id()),
                                 kuaishou::ad::AdEnum_ItemType_Name(p_ad->get_item_type()),
                                 kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()), "T6648255");
    int64_t key_author_pcoc = base::CityHash64(s_author_pcoc.c_str(), s_author_pcoc.size());
    key_author_pcoc = std::abs(key_author_pcoc);
    p_ad->Attr(ItemIdx::pre_pcoc_id).SetIntValue(p_ad->AttrIndex(), key_author_pcoc, false, false); // NOLINT

    p_ad->set_enable_pacing_tag(false);

    if (enable_fix_dup_photo_id) {
      if (dup_photo_id_list) {
        p_ad->set_is_dup_photo(dup_photo_id_list->IsDupPhoto(p_ad->get_photo_id()));
      }
    }

    // 经营版诉求
    // 店铺新客
    if (session_data->get_is_splash_request() &&
        middle_page_account_set && middle_page_account_set->count(p_ad->get_account_id()) > 0) {
      p_ad->set_is_splash_middle_page(true);
    }

    if (session_data->get_is_splash_request()
        && RankKconfUtil::splashCouponCollectionCenterAccountIds()->count(p_ad->get_account_id()) > 0) {
      p_ad->set_is_splash_coupon_collection_center_ad(true);
    }

    std::string hash = absl::StrCat(p_ad->get_creative_id(), p_ad->get_ad_queue_type());
    p_ad->set_unique_id_for_cmd(base::CityHash64(hash.c_str(), hash.length()));
    if (unique_id_2_ad_map) {
      unique_id_2_ad_map->insert({p_ad->get_unique_id_for_cmd(), p_ad});
    }

    const auto ad_shop_id = p_ad->get_author_id();
    if (purchase_shop_id_set.count(ad_shop_id) > 0) {
      p_ad->set_is_shop_new_customer(true);
    }
    p_ad->set_is_brother_fan(brother_fans_follow_set.count(p_ad->get_author_id()) > 0);
    p_ad->set_is_new_fan(new_fans_follow_set.count(p_ad->get_author_id()) > 0);

    if (one_day_follow_set.count(p_ad->get_author_id()) > 0) {
      p_ad->set_follow_days(1);
    } else if (seven_day_follow_set.count(p_ad->get_author_id()) > 0) {
      p_ad->set_follow_days(7);
    }

    // 非 fanstop 填充默认值
    if (!utility::IsFanstopCampaign(p_ad->get_campaign_type())) {
      p_ad->set_priority_level(0);
      p_ad->set_is_new_operation_unit(false);
      p_ad->set_is_cost_cap(false);
      p_ad->set_is_brand(false);
      p_ad->set_is_lowest_cost(false);
      p_ad->set_is_inner_ecpc(false);
      p_ad->set_budget(0);
      p_ad->set_live_is_domestic(0);
      p_ad->set_cost_cap_type(static_cast<kuaishou::fanstop::FansTopEnum::CostCapType>(0));
      p_ad->set_live_launch_type(0);
      p_ad->set_unit_begin_time(0);
      p_ad->set_is_operation_unit(false);
      p_ad->set_polaris_delivery(0);
      p_ad->set_unit_end_time(0);
      p_ad->set_inner_fanstop_reason(0);
      p_ad->set_is_recruit_photo(false);
      p_ad->set_fanstop_category(0);
      p_ad->set_delivery_type(0);
      p_ad->set_is_new_operation_merchant_unit(false);
      p_ad->set_is_inner_budget(0);
      p_ad->set_item_ext_attr("");
      p_ad->set_is_ecommerce(0);
    }
    if (!(utility::IsFanstopCampaign(p_ad->get_campaign_type()) ||
          (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE &&
          (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE ||
           p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2)))) {
      p_ad->set_fanstop_payer_id(0);
    }

    // 加速探索 bid 链路感知
    bool is_select_page = (ks::ad_base::IsSelectedRequest(session_data->get_sub_page_id()) ||
          ks::ad_base::IsNebulaExploreRequest(session_data->get_sub_page_id()));
    if (RankKconfUtil::accIncrementAccountTailAdmit()->IsOnFor(p_ad->get_account_id())
        && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED
        && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
        && p_ad->get_explore_bid_type() == 1
        && (p_ad->get_explore_put_type() == 2 || p_ad->get_explore_put_type() == 3)
        && p_ad->get_is_in_acc_explore_status()
        && is_select_page) {
        session_data->mutable_ad_bounder()->SetAutoCpaBidWithIntTag(p_ad,
          p_ad->get_auto_cpa_bid() - p_ad->get_auto_bid_explore(), p_ad->get_auto_cpa_bid_modify_tag());
        session_data->dot_perf->Count(1,
                            "ad_rank.IsInnerLoopAccIncrement.valid");
    } else {
       session_data->mutable_ad_bounder()->SetAutoCpaBidWithIntTag(p_ad, p_ad->get_auto_cpa_bid(),
        p_ad->get_auto_cpa_bid_modify_tag());
    }
    if (RankKconfUtil::accIncrementRoiWhite()->IsOnFor(p_ad->get_account_id())
        && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
        ||  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI
        ||  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)
        && p_ad->get_explore_bid_type() == 1
        && (p_ad->get_explore_put_type() == 2 || p_ad->get_explore_put_type() == 3)
        && p_ad->get_is_in_acc_explore_status()
        && is_select_page) {
      if (p_ad->get_auto_roas() > 0.01 && p_ad->get_auto_bid_explore() > 0.01) {
        p_ad->set_before_explore_bid(p_ad->get_auto_roas());
        double actual_roi = (p_ad->get_auto_bid_explore() * p_ad->get_auto_roas()) /
                            (p_ad->get_auto_bid_explore() - p_ad->get_auto_roas());
        if (actual_roi < 0) {
          actual_roi = p_ad->get_roi_ratio();
        }
        p_ad->SetAutoRoasIntTag(actual_roi, p_ad->get_auto_roas_modify_tag());
          session_data->dot_perf->Count(1,
                              "ad_rank.IsInnerLoopAccIncrementRoi.valid");
      }
    } else {
      p_ad->SetAutoRoasIntTag(p_ad->get_auto_roas(), p_ad->get_auto_roas_modify_tag());
    }

    // 短视频 cid 加速探索适配
    if (SPDM_enablePhotoCidAccRank()) {
      // cid 加速探索，不限制页面
      is_select_page = true;
    }
    bool is_photo_cid_acc =
        p_ad->get_explore_bid_type() == 1 &&
        (p_ad->get_explore_put_type() == 2 || p_ad->get_explore_put_type() == 3) &&
        p_ad->get_is_in_acc_explore_status() &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
        p_ad->get_scene_oriented_type() == 38 &&
        is_select_page;
    if (is_photo_cid_acc && p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
      if (p_ad->get_auto_roas() > 0.01 && p_ad->get_auto_bid_explore() > 0.01) {
        p_ad->set_before_explore_bid(p_ad->get_auto_roas());
        double actual_roi = 0.0;
        if (std::abs(p_ad->get_auto_bid_explore() - p_ad->get_auto_roas()) > 1e-6) {
          actual_roi = (p_ad->get_auto_bid_explore() * p_ad->get_auto_roas()) /
                 (p_ad->get_auto_bid_explore() - p_ad->get_auto_roas());
        }
        if (actual_roi < 0) {
          actual_roi = p_ad->get_roi_ratio();
        }
        p_ad->SetAutoRoasIntTag(actual_roi, p_ad->get_auto_roas_modify_tag());
          session_data->dot_perf->Count(1,
                              "ad_rank.IsInnerLoopAccIncrementRoi.valid");
      } else {
        p_ad->SetAutoRoasIntTag(p_ad->get_auto_roas(), p_ad->get_auto_roas_modify_tag());
      }
    } else if (is_photo_cid_acc && p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) {
      if (p_ad->get_auto_bid_explore() > 0.01) {
        session_data->mutable_ad_bounder()->SetAutoCpaBidWithIntTag(p_ad,
          p_ad->get_auto_cpa_bid() - p_ad->get_auto_bid_explore(),
        p_ad->get_auto_cpa_bid_modify_tag());
        session_data->dot_perf->Count(1,
                            "ad_rank.IsInnerLoopAccIncrement.valid");
      } else {
         session_data->mutable_ad_bounder()->SetAutoCpaBidWithIntTag(p_ad,
          p_ad->get_auto_cpa_bid(), p_ad->get_auto_cpa_bid_modify_tag());
      }
    }

    p_ad->set_has_merchant(true);
    p_ad->set_origin_deep_conversion_type(p_ad->get_deep_conversion_type());
    p_ad->set_community_review_status_v2(p_ad->get_community_review_status());
    p_ad->set_rta_bid_prerank(p_ad->get_rta_bid());

    // 更新 crm center
    if ((!enable_move_crm_center_code) && enable_q3_hc_crm_center &&
        p_ad->Is(AdFlag::is_outer_loop_ad)) {
      if (enable_outside_zhuanxiang) {  // 外循环有预算腾挪才进行 mock
        // 私信
        if (enable_outside_zhuanxiang_1000001) {
          if (p_ad->get_ad_source_type() == kuaishou::ad::DSP &&
              p_ad->get_account_type() != kuaishou::ad::AdEnum::ACCOUNT_CPM &&
              p_ad->get_account_type() != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP &&
              ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION) ||
              ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) &&
                p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_PRIVATE_MESSAGE_SENT &&
                p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SELF_SERVICE) ||
              (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION &&
                p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::LEADS_SUBMIT &&
                p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SELF_SERVICE) ||
              (sx_account_map != nullptr &&
                sx_account_map->count(p_ad->get_account_id()) > 0))) {
            p_ad->set_crm_center(1000001);
          }
        }
        // UAX
        if (enable_outside_zhuanxiang_1000002) {
            if (p_ad->get_auto_manage() == kuaishou::ad::AdEnum::AUTO_MANAGE_OPEN) {
              p_ad->set_crm_center(1000002);
            }
        }
        // 企微
        if (enable_outside_zhuanxiang_1000003) {
          if (qw_account_map != nullptr &&
              qw_account_map->count(p_ad->get_account_id()) > 0) {
            p_ad->set_crm_center(1000003);
          }
        }
        // 付费短剧
        if (enable_outside_zhuanxiang_1000005) {
          if (p_ad->get_second_industry_id_v5() == 2012) {
            p_ad->set_crm_center(1000005);
          }
        }
      }
    }
  }

  if (unique_id_2_ad_map) {
    session_data->common_w_->SetPtrCommonAttr("unique_id_2_ad_map", unique_id_2_ad_map);
  }

  // MCB 出价获取兜底 & 调价系数变动限制
  GuardBidInfo(session_data->mutable_ad_list(), session_data);
  GuardBidInfo(session_data->mutable_native_ad_list(), session_data);
}

void ModifyItemAttr::GuardBidInfo(AdList* ad_list, ContextData* session_data) {
  if (!ad_list) {
    return;
  }

  int64_t mcb_default_cpa_bid = ks::engine_base::AdKconfUtil::mcbDefaultCpaBid();
  double mcb_default_roi_ratio = ks::engine_base::AdKconfUtil::mcbDefaultRoiRatio();
  double deep_min_bid_coef = engine_base::AdKconfUtil::deepMinBidCoefDefaultValue();
  int64_t deep_min_bid_coef_miss = 0;
  int64_t deep_min_coef_miss = 0;

  std::unordered_map<BidStrategyGroupType, int64_t> mcb_initial_miss_ad_target;
  for (auto p_ad : ad_list->Ads()) {
    BidStrategyGroupType bid_strategy_group = p_ad->get_bid_strategy_group();
    if (bid_strategy_group == kuaishou::ad::AdEnum::MAX_CONV_STRATEGY) {
      // mcb 兜底
      if (std::fabs(p_ad->get_product_cpa_bid()) <= FLT_EPSILON &&
            std::fabs(p_ad->get_product_roi_ratio()) <= FLT_EPSILON) {
        // 兜底
        p_ad->set_product_cpa_bid(mcb_default_cpa_bid);
        p_ad->set_product_roi_ratio(mcb_default_roi_ratio);

        // 打点计数
        auto iter = mcb_initial_miss_ad_target.find(bid_strategy_group);
        if (iter == mcb_initial_miss_ad_target.end()) {
          mcb_initial_miss_ad_target[bid_strategy_group] = 1;
        } else {
          ++mcb_initial_miss_ad_target[bid_strategy_group];
        }
      }
    }
    // ocpm 双出价部分兜底
    if (p_ad->get_deep_min_bid_coef() <= 0) {
      p_ad->set_deep_min_bid_coef(deep_min_bid_coef);
      deep_min_coef_miss++;
    }
  }
  session_data->dot_perf->Count(deep_min_bid_coef_miss,
    "rank.retention_miss_target_guard", "deep_min_bid_coef");
  for (auto& iter : mcb_initial_miss_ad_target) {
    session_data->dot_perf->Count(iter.second,
      "mcb.initial.miss_ad_target",
      kuaishou::ad::AdEnum_BidStrategyGroup_Name(iter.first));
  }
  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ModifyItemAttr, ::ks::ad_rank::ModifyItemAttr);

}   // namespace ad_rank
}   // namespace ks
