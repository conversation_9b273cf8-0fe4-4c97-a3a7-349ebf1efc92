#pragma once

#include <string>
#include "teams/ad/ad_rank/processor/framework/factor_udf_manager.h"

namespace ks {
namespace ad_rank {

class CalcHasDeepCvr : public FactorUdfManager<bool> {
 public:
  CalcHasDeepCvr() = default;

 private:
  inline std::string GetName() const override {
    return "CalcHasDeepCvr";
  }

  void DealValue(AdCommon* p_ad, bool value, const UdfInfo<bool>& udf_info) override;
};
}  // namespace ad_rank
}  // namespace ks
