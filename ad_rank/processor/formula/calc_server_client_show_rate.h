#pragma once

#include <string>
#include "teams/ad/ad_rank/processor/framework/factor_udf_manager.h"

namespace ks {
namespace ad_rank {

class CalcServerClientShowRate : public FactorUdfManager<double> {
 public:
  CalcServerClientShowRate() = default;

 private:
  inline std::string GetName() const override {
    return "CalcServerClientShowRate";
  }

  void DealValue(AdCommon* p_ad, double value, const UdfInfo<double>& udf_info) override;
};
}  // namespace ad_rank
}  // namespace ks
