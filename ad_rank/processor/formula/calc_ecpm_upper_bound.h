#pragma once

#include <string>
#include "teams/ad/ad_rank/processor/framework/factor_udf_manager.h"

namespace ks {
namespace ad_rank {

class CalcEcpmUpperBound : public FactorUdfManager<int64_t> {
 public:
  CalcEcpmUpperBound() = default;

 private:
  inline std::string GetName() const override {
    return "CalcEcpmUpperBound";
  }

  void DealValue(AdCommon* p_ad, int64_t value, const UdfInfo<int64_t>& udf_info) override;
};
}  // namespace ad_rank
}  // namespace ks
