#pragma once

#include <string>
#include "teams/ad/ad_rank/processor/framework/factor_udf_manager.h"

namespace ks {
namespace ad_rank {

class CalcEcpmAdjust : public FactorUdfManager<int64_t> {
 public:
  CalcEcpmAdjust() = default;

 private:
  inline std::string GetName() const override {
    return "CalcEcpmAdjust";
  }

  bool InitializeImpl(ks::platform::AddibleRecoContextInterface* context) override;

  void DealValue(AdCommon* p_ad, int64_t value, const UdfInfo<int64_t>& udf_info) override;
  bool AdPreProcess(AdCommon* p_ad) override;

 private:
  int64_t udf_cnt_ = 0;
};
}  // namespace ad_rank
}  // namespace ks
