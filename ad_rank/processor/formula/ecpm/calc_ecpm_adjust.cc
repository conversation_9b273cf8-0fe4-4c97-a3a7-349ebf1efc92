#include "teams/ad/ad_rank/processor/formula/ecpm/calc_ecpm_adjust.h"

#include "teams/ad/ad_base/src/common/common.h"
#include "teams/ad/ad_rank/processor/framework/udf_manager.h"
#include "teams/ad/ad_rank/processor/framework/common_defs.h"

namespace ks {
namespace ad_rank {
bool CalcEcpmAdjust::InitializeImpl(ks::platform::AddibleRecoContextInterface* context) {
  udf_cnt_ = 0;
  for (const auto& udf_info : udf_info_list_) {
    if (udf_info.is_initialized) {
      ++udf_cnt_;
    }
  }
  return true;
}

bool CalcEcpmAdjust::AdPreProcess(AdCommon* p_ad) {
  p_ad->ResetEcpmAdjust(udf_cnt_);
  return true;
}

void CalcEcpmAdjust::DealValue(AdCommon* p_ad, int64_t value, const UdfInfo<int64_t>& udf_info) {
  p_ad->AddEcpmAdjust(value, udf_info.factor_tag);
}

using JsonFactoryClass = base::JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CalcEcpmAdjust, CalcEcpmAdjust);
}  // namespace ad_rank
}  // namespace ks
