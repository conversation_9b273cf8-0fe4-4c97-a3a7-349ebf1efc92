#include "teams/ad/ad_rank/processor/remote_call/qcpx_sky_fall_coupon_mixer.h"

#include <utility>
#include <string>

#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/engine/server/init.h"
#include "teams/ad/ad_rank/processor/framework/ad_context_wrapper.h"


namespace ks::ad_rank {

bool QcpxSkyFallCouponMixer::InitProcessor() {
  if (!config()) { return false; }
  rpc_kess_name_ = config()->GetString("rpc_kess_name");
  if (rpc_kess_name_.empty()) {
    LOG(ERROR) << "rpc_kess_name empty";
    return false;
  }
  return true;
}

void QcpxSkyFallCouponMixer::OnPipelineExit(ks::platform::ReadableRecoContextInterface* context) {
  rpc_timeout_ = 0;
  session_data_ = nullptr;
  arena_request_ = nullptr;
  arena_response_ = nullptr;
  author_ids_.clear();
  activity_config_info_map_.clear();
}

void QcpxSkyFallCouponMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  if (!PvInit(context)) { return; }
  if (!Admit()) { return;}
  if (!Prepare()) { return; }
  FillRequest();
  SendAndPost();
}

bool QcpxSkyFallCouponMixer::PvInit(ks::platform::AddibleRecoContextInterface* context) {
  if (!context) { return false; }
  context_ = context;
  auto ps_context_wrapper = context->GetMutablePtrCommonAttr<AdContextWrapper>("ad_context_wrapper");
  if (!ps_context_wrapper || !ps_context_wrapper->Get()) { return false; }
  auto ps_context = ps_context_wrapper->Get();
  session_data_ = ps_context->GetMutableContextData<ContextData>();
  if (!session_data_) { return false; }
  session_data_->mutable_author_ecom_coupon_list()->clear();

  rpc_timeout_ = SPDM_qcpx_sky_fall_coupon_rpc_timeout(session_data_->get_spdm_ctx());
  auto rpc_arena = session_data_->mutable_rpc_arena();
  if (!rpc_arena) { return false; }
  arena_request_ = google::protobuf::Arena::CreateMessage<SkyFallCouponPreCheckRequest>(rpc_arena);
  arena_response_ = google::protobuf::Arena::CreateMessage<SkyFallCouponPreCheckResponse>(rpc_arena);
  if (!(arena_request_ && arena_response_)) { return false; }
  return true;
}

bool QcpxSkyFallCouponMixer::Admit() {
  if (!(SPDM_enable_qcpx_sky_fall_coupon_rpc(session_data_->get_spdm_ctx()) ||
      SPDM_enableQcpxSkyFallCouponRpc())) {
    return false;
  }
  if (session_data_->get_user_id() <= 0) {
    return false;
  }
  if (SPDM_enable_inner_coupon_holdout(session_data_->get_spdm_ctx())) {
    return false;
  }

  if (SPDM_enable_inner_live_coupon_holdout(session_data_->get_spdm_ctx())) {
    return false;
  }

  if (SPDM_enable_qcpx_live_u0_filter_potential(session_data_->get_spdm_ctx())) {
    auto& buyer_effective_type =
        session_data_->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
    if (buyer_effective_type.empty()) {
      return false;
    }
  }
  const auto sub_page_id = session_data_->get_sub_page_id();
  static const absl::flat_hash_set<int64_t> admit_sub_page_ids = {
    10011001, 100012194, 100013100, 11001001, 100016771, 10002001, 10008001 };
  if (admit_sub_page_ids.end() != admit_sub_page_ids.find(sub_page_id)) {
    return true;
  }
  return false;
}

bool QcpxSkyFallCouponMixer::Prepare() {
  auto filter_list = RankKconfUtil::qcpxFilterAuthorList();
  auto not_use_list = RankKconfUtil::canNotUsePlatformCouponShopIdSet();
  if (!(filter_list && not_use_list)) { return false; }
  const auto max_size = SPDM_qcpx_sky_fall_coupon_rpc_max_size(session_data_->get_spdm_ctx());

  auto collect_author_id = [&](AdList* adlist, auto cond) {
    if (!adlist) { return; }
    for (const auto* p_ad : adlist->Ads()) {
      if (0 < max_size && max_size <= author_ids_.size()) { return; }  // 上限控制
      if (!p_ad || !(p_ad->Is(AdFlag::is_esp_ad) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED))) {
        continue;
      }
      int64_t author_id = p_ad->get_author_id();
      if (filter_list->count(author_id)) { continue; }
      if (not_use_list->count(absl::StrCat(author_id))) { continue; }
      if (cond(p_ad)) {
        author_ids_.emplace(author_id);
      }
    }
  };
  auto live_cond = [](const AdCommon* p_ad) {
    return p_ad && kuaishou::ad::AdEnum::ITEM_LIVE == p_ad->get_item_type();
  };
  auto not_live_cond = [](const AdCommon* p_ad) {
    return p_ad && kuaishou::ad::AdEnum::ITEM_LIVE != p_ad->get_item_type();
  };

  // 上限 max_size, 优先选直投, 再选非直投补
  collect_author_id(session_data_->mutable_native_ad_list(), live_cond);
  collect_author_id(session_data_->mutable_ad_list(), live_cond);
  collect_author_id(session_data_->mutable_fanstop_ad_list(), live_cond);
  collect_author_id(session_data_->mutable_native_ad_list(), not_live_cond);
  collect_author_id(session_data_->mutable_ad_list(), not_live_cond);
  collect_author_id(session_data_->mutable_fanstop_ad_list(), not_live_cond);

  RANK_DOT_STATS(session_data_, author_ids_.size(), "qcpx_sky_fall_coupon_author_ids_size",
      absl::StrCat(max_size));
  return !author_ids_.empty();
}

void QcpxSkyFallCouponMixer::FillRequest() {
  start_us_ = base::GetTimestamp();
  arena_request_->set_buyer_id(session_data_->get_user_id());
  arena_request_->set_request_source(87);  // 服务端指定 id
  auto* client_info = arena_request_->mutable_client_info();
  if (!client_info) { return; }
  const auto& ad_request = session_data_->get_rank_request()->ad_request();
  client_info->set_user_id(session_data_->get_user_id());
  client_info->set_device_id(ad_request.ad_user_info().device_id());
  client_info->set_sysver(ad_request.ad_user_info().platform_version());
  for (const auto& id : author_ids_) {
    arena_request_->add_anchor_id(id);
  }
}

void QcpxSkyFallCouponMixer::SendAndPost() {
  auto client =
    ks::ad_base::AdKessClient::ClientOfKey<KwaishopMarketingPremCommercialService>(rpc_kess_name_);
  auto options = ks::ad_base::OptionsFromMilli(
      5 < rpc_timeout_ ? rpc_timeout_ : client.first->time_out);
  if (IsAsync()) {
    auto promise = std::make_shared<std::promise<bool *>>();
    client.second->SelectOne()->
        AsyncSkyFallCouponPreCheck(options, *arena_request_, arena_response_, client.second->GetEventLoop())
        .Submit([this, promise](const ::grpc::Status& status, SkyFallCouponPreCheckResponse* reply) {
          end_us_ = base::GetTimestamp();
          static bool TRUE = true;
          static bool FALSE = false;
          bool success = status.ok() && kuaishou::plateco::merchant::MerchantResponseCode::SUCCESS ==
              arena_response_->base_resp_info().resp_code();
          if (!success) {
            LOG_EVERY_N(WARNING, 1000) << "QcpxSkyFallCouponMixer rpc status: " << status.error_code()
                << ", resp_code: " << arena_response_->base_resp_info().resp_code();
          }
          promise->set_value(success ? &TRUE : &FALSE);
        });
    auto callback = [this] (bool *ret) {
      RANK_DOT_STATS(session_data_, end_us_ - start_us_, "qcpx_sky_fall_coupon_rpc_latency");
      if (ret && *ret) { ProcResponse(); }
    };
    RegisterLocalAsyncCallback(context_, std::move(promise->get_future()), std::move(callback),
        "qcpx_sky_fall_coupon_rpc");
  } else {
    auto status = client.second->SelectOne()->
        SkyFallCouponPreCheck(options, *arena_request_, arena_response_);
    end_us_ = base::GetTimestamp();
    RANK_DOT_STATS(session_data_, end_us_ - start_us_, "qcpx_sky_fall_coupon_rpc_latency");
    bool success = status.ok() && kuaishou::plateco::merchant::MerchantResponseCode::SUCCESS ==
        arena_response_->base_resp_info().resp_code();
    if (success) {
      ProcResponse();
    } else {
      LOG_EVERY_N(WARNING, 1000) << "QcpxSkyFallCouponMixer rpc status: " << status.error_code()
          << ", resp_code: " << arena_response_->base_resp_info().resp_code();
    }
  }
}

void QcpxSkyFallCouponMixer::ProcResponse() {
  auto author_ecom_coupon_list = session_data_->mutable_author_ecom_coupon_list();
  if (author_ecom_coupon_list == nullptr) {
    return;
  }

  author_ecom_coupon_list->clear();
  for (const auto& info : arena_response_->activity_config_info()) {
    author_ecom_coupon_list->emplace(info.anchor_id(), &info);
  }
}


}  // namespace ks::ad_rank

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, QcpxSkyFallCouponMixer, ::ks::ad_rank::QcpxSkyFallCouponMixer);
