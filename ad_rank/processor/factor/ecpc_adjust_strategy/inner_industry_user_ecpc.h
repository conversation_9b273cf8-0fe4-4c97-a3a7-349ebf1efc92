#pragma once

#include <memory>
#include <string>
#include <set>
#include <map>
#include <chrono>
#include <iomanip>

#include "teams/ad/ad_rank/strategy/ecpc_adjust_ratio_strategy.h"

namespace ks {
namespace ad_rank {

class InnerIndustryUserEcpc : public EcpcAdjustRatioStrategy {
 public:
  InnerIndustryUserEcpc(std::string owner, std::string name) : EcpcAdjustRatioStrategy(owner, name) {}
  InnerIndustryUserEcpc() = default;
  ~InnerIndustryUserEcpc() {}
  void Init(ContextData* session_data) override;
  bool Admit(ContextData* session_data, AdCommon* ad) override;
  double Process(ContextData* session_data, AdCommon* ad) override;
  std::string GetGpmBucket(double gpm);

 private:
  bool enable_first_n_ecpc_inner = false;
  bool enable_industry_gpm_cali = false;
  bool enable_industry_gpm_cali_by_u_level = false;
  bool enable_industry_gpm_cali_cpm_only = false;
  bool enable_industry_gpm_cali_by_author_industry = false;
  bool enable_industry_gpm_cali_by_ad_queue_type = false;
  bool enable_industry_gpm_cali_by_concat_key = false;
  bool enable_ad_gmv_shentou_tag = false;
  bool enable_inner_low_load_ratio = false;
  bool enable_inner_calib_combine = false;
  bool enable_inner_ctcvr_calib_combine = false;
  bool enable_inner_mix_unify_gpm_cali = false;
  bool enable_combine_only = false;
  bool enable_combine_only_ctcvr = false;
  std::string second_exp_tag_;
  std::string exp_tag;
  std::string exp_ctcvr_cali_tag;
  bool enable_combine_second_exp_config_;
  int64_t first_n_thresh = 0;
  double default_ratio = 1.0;
  double inner_cali_cpm_thresh = 0.0;
  double inner_cali_gpm_thresh = 0.0;
  double inner_low_load_thresh = 1.0;
  double ad_gmv_level_ratio_thres_A3 = 0.8;
  double ad_gmv_level_ratio_thres_A2 = 0.2;
  bool use_daily_pcoc_params;
  double pcoc_weight_lower_bound;
  double pcoc_weight_upper_bound;
  double ctcvr_thres_live;
  double ctcvr_thres_photo;
  double ctcvr_thres_photo_to_live;
  bool enable_daily_unify_cpm_thres_;
  bool enable_daily_ctcvr_thres_;
  bool enable_unify_cpm_thres_p50_;
  bool enable_unify_cpm_thres_p90_;
  bool enable_unify_cpm_ctcvr_thres_p50_;
  bool enable_unify_cpm_ctcvr_thres_p90_;
  bool enable_ctcvr_daily_thres_;
  bool enable_daily_thres_p90_;
  bool enable_daily_thres_p99_;
  bool enable_exp7_inner_cpm_cali;
  double exp7_ratio = 1.0;
  bool enable_origin_cvr_pred_value;
  bool enable_qcpx_cvr_value;
  bool enable_fix_gpm_ue_hc_calc_v1;
  bool enable_buyer_type_lower_bound_;
  bool enable_ctcvr_daily_multi_percentile_thres_;
  bool enable_ctcvr_bid_level_concat;
  double bid_p90_thres;
  double bid_p75_thres;
  double bid_p50_thres;
  double low_cpm_thres_1_10011;
  double low_cpm_thres_1_11001;
  double low_cpm_thres_2_10011;
  double low_cpm_thres_2_11001;
  bool enable_new_discount_tag;
};

}  // namespace ad_rank
}  // namespace ks
