#pragma once
#include <memory>
#include <string>
#include <set>
#include <map>
#include "teams/ad/ad_rank/strategy/ecpc_adjust_ratio_strategy.h"
namespace ks {
namespace ad_rank {
class InnerNewGuardNEcpc : public EcpcAdjustRatioStrategy {
 public:
  InnerNewGuardNEcpc(std::string owner, std::string name) : EcpcAdjustRatioStrategy(owner, name) {}
  InnerNewGuardNEcpc() = default;
  ~InnerNewGuardNEcpc() {}
  void Init(ContextData* session_data) override;
  bool Admit(ContextData* session_data, AdCommon* ad) override;
  double Process(ContextData* session_data, AdCommon* ad) override;
 private:
  bool enable_inner_new_guard_n_ecpc = false;
  double default_ratio = 1.0;
  int conversion_cnt_threshold = 1;
};
}  // namespace ad_rank
}  // namespace ks
