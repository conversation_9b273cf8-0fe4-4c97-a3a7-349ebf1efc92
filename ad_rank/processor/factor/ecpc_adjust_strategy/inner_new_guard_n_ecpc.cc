#include "teams/ad/ad_rank/processor/factor/ecpc_adjust_strategy/inner_new_guard_n_ecpc.h"
#include <algorithm>
#include <memory>
#include <map>
#include <vector>
#include "teams/ad/ad_rank/processor/factor/ranking_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/processor/framework/udf_manager.h"

namespace ks {
namespace ad_rank {
void InnerNewGuardNEcpc::Init(ContextData* session_data) {
  enable_inner_new_guard_n_ecpc =
    SPDM_enable_inner_new_guard_n_ecpc(session_data->get_spdm_ctx());
  default_ratio =
    SPDM_inner_new_guard_n_ecpc_ratio(session_data->get_spdm_ctx());
  conversion_cnt_threshold =
    SPDM_inner_new_guard_n_ecpc_conversion_cnt_threshold(session_data->get_spdm_ctx());
}
bool InnerNewGuardNEcpc::Admit(ContextData* session_data, AdCommon* p_ad) {
  bool is_admit = false;
  if (enable_inner_new_guard_n_ecpc) {
    // 准入逻辑 退出逻辑
    auto campaign_conversion_cnt =
      p_ad->Attr(ItemIdx::fd_CAMPAIGN_order_newcus_benefit_conversion_cnt).GetIntValue(p_ad->AttrIndex()).value_or(-1);  // NOLINT

    if (campaign_conversion_cnt >= 0 && campaign_conversion_cnt < conversion_cnt_threshold) {
      is_admit = true;
    }
  }
  return is_admit;
}
double InnerNewGuardNEcpc::Process(ContextData* session_data, AdCommon* ad) {
  return default_ratio;
}
RegisterClass(UdfFactor, InnerNewGuardNEcpc);
}  // namespace ad_rank
}  // namespace ks
