#include "teams/ad/ad_rank/processor/factor/ecpc_adjust_strategy/inner_industry_user_ecpc.h"
#include <algorithm>
#include <memory>
#include <map>
#include <vector>

#include "teams/ad/ad_rank/processor/factor/ranking_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/processor/framework/udf_manager.h"
#include "teams/ad/ad_rank/data/p2p_data/universe_conv_ratio_author_post_data_p2p/universe_conv_ratio_author_post_data_p2p.h"

using kuaishou::ad::AdEnum;

namespace ks {
namespace ad_rank {

void InnerIndustryUserEcpc::Init(ContextData* session_data) {
  enable_first_n_ecpc_inner =
    SPDM_enable_first_n_ecpc_inner(session_data->get_spdm_ctx());
  enable_industry_gpm_cali =
    SPDM_enable_industry_gpm_cali(session_data->get_spdm_ctx());
  first_n_thresh =
    SPDM_first_n_ecpc_thresh(session_data->get_spdm_ctx());
  enable_industry_gpm_cali_by_u_level =
    SPDM_enable_industry_gpm_cali_by_u_level(session_data->get_spdm_ctx());
  enable_industry_gpm_cali_cpm_only =
    SPDM_enable_industry_gpm_cali_cpm_only(session_data->get_spdm_ctx());
  enable_industry_gpm_cali_by_author_industry =
    SPDM_enable_industry_gpm_cali_by_author_industry(session_data->get_spdm_ctx());
  enable_industry_gpm_cali_by_ad_queue_type =
    SPDM_enable_industry_gpm_cali_by_ad_queue_type(session_data->get_spdm_ctx());
  enable_industry_gpm_cali_by_concat_key =
    SPDM_enable_industry_gpm_cali_by_concat_key(session_data->get_spdm_ctx());
  enable_ad_gmv_shentou_tag =
    SPDM_enable_ad_gmv_shentou_tag(session_data->get_spdm_ctx());
  enable_inner_low_load_ratio =
    SPDM_enable_inner_low_load_ratio(session_data->get_spdm_ctx());
  default_ratio =
    SPDM_nobid_history_buyer_default_ratio(session_data->get_spdm_ctx());
  exp_tag =
    SPDM_inner_industry_gpm_cali_exp_tag(session_data->get_spdm_ctx());
  exp_ctcvr_cali_tag =
    SPDM_inner_industry_gpm_ctcvr_cali_exp_tag(session_data->get_spdm_ctx());
  enable_inner_calib_combine =
    SPDM_enable_inner_calib_combine(session_data->get_spdm_ctx());
  enable_inner_ctcvr_calib_combine =
    SPDM_enable_inner_ctcvr_calib_combine(session_data->get_spdm_ctx());
  inner_cali_cpm_thresh =
    SPDM_inner_industry_gpm_cali_cpm_thresh(session_data->get_spdm_ctx());
  inner_cali_gpm_thresh =
    SPDM_inner_industry_gpm_cali_gpm_thresh(session_data->get_spdm_ctx());
  inner_low_load_thresh =
    SPDM_inner_low_load_thresh(session_data->get_spdm_ctx());
  ad_gmv_level_ratio_thres_A3 =
    SPDM_ad_gmv_level_ratio_thres_A3(session_data->get_spdm_ctx());
  ad_gmv_level_ratio_thres_A2 =
    SPDM_ad_gmv_level_ratio_thres_A2(session_data->get_spdm_ctx());
  enable_combine_only =
    SPDM_enable_combine_only(session_data->get_spdm_ctx());
  enable_combine_only_ctcvr =
    SPDM_enable_combine_only_ctcvr(session_data->get_spdm_ctx());
  enable_combine_second_exp_config_ =
    SPDM_enable_combine_second_exp_config(session_data->get_spdm_ctx());
  second_exp_tag_ = SPDM_second_exp_tag(session_data->get_spdm_ctx());
  enable_inner_mix_unify_gpm_cali =
    SPDM_enable_inner_mix_unify_gpm_cali(session_data->get_spdm_ctx());
  use_daily_pcoc_params = SPDM_use_daily_pcoc_params(session_data->get_spdm_ctx());
  pcoc_weight_lower_bound = SPDM_pcoc_weight_lower_bound(session_data->get_spdm_ctx());
  pcoc_weight_upper_bound = SPDM_pcoc_weight_upper_bound(session_data->get_spdm_ctx());
  ctcvr_thres_live = SPDM_ctcvr_thres_live(session_data->get_spdm_ctx());
  ctcvr_thres_photo = SPDM_ctcvr_thres_photo(session_data->get_spdm_ctx());
  ctcvr_thres_photo_to_live = SPDM_ctcvr_thres_photo_to_live(session_data->get_spdm_ctx());
  enable_daily_unify_cpm_thres_ = SPDM_enable_daily_unify_cpm_thres(session_data->get_spdm_ctx());
  enable_daily_ctcvr_thres_ = SPDM_enable_daily_ctcvr_thres(session_data->get_spdm_ctx());
  enable_unify_cpm_thres_p50_ = SPDM_enable_unify_cpm_thres_p50(session_data->get_spdm_ctx());
  enable_unify_cpm_thres_p90_ = SPDM_enable_unify_cpm_thres_p90(session_data->get_spdm_ctx());
  enable_unify_cpm_ctcvr_thres_p50_ = SPDM_enable_unify_cpm_ctcvr_thres_p50(session_data->get_spdm_ctx());
  enable_unify_cpm_ctcvr_thres_p90_ = SPDM_enable_unify_cpm_ctcvr_thres_p90(session_data->get_spdm_ctx());
  enable_ctcvr_daily_thres_ = SPDM_enable_ctcvr_daily_thres(session_data->get_spdm_ctx());
  enable_daily_thres_p90_ = SPDM_enable_daily_thres_p90(session_data->get_spdm_ctx());
  enable_daily_thres_p99_ = SPDM_enable_daily_thres_p99(session_data->get_spdm_ctx());
  enable_exp7_inner_cpm_cali = SPDM_enable_exp7_inner_cpm_cali(session_data->get_spdm_ctx());
  enable_qcpx_cvr_value = SPDM_enable_qcpx_cvr_value(session_data->get_spdm_ctx());
  enable_origin_cvr_pred_value = SPDM_enable_origin_cvr_pred_value(session_data->get_spdm_ctx());
  enable_fix_gpm_ue_hc_calc_v1 = SPDM_enable_fix_gpm_ue_hc_calc_v1(session_data->get_spdm_ctx());
  enable_ctcvr_daily_multi_percentile_thres_ =
    SPDM_enable_ctcvr_daily_multi_percentile_thres(session_data->get_spdm_ctx());
  enable_buyer_type_lower_bound_ =
    SPDM_enable_buyer_type_lower_bound(session_data->get_spdm_ctx());
  enable_ctcvr_bid_level_concat = SPDM_enable_ctcvr_bid_level_concat(session_data->get_spdm_ctx());
  bid_p90_thres = SPDM_bid_p90_thres(session_data->get_spdm_ctx());
  bid_p75_thres = SPDM_bid_p75_thres(session_data->get_spdm_ctx());
  bid_p50_thres = SPDM_bid_p50_thres(session_data->get_spdm_ctx());
  low_cpm_thres_1_10011 = SPDM_low_cpm_thres_1_10011(session_data->get_spdm_ctx());
  low_cpm_thres_1_11001 = SPDM_low_cpm_thres_1_11001(session_data->get_spdm_ctx());
  low_cpm_thres_2_10011 = SPDM_low_cpm_thres_2_10011(session_data->get_spdm_ctx());
  low_cpm_thres_2_11001 = SPDM_low_cpm_thres_2_11001(session_data->get_spdm_ctx());
  enable_new_discount_tag = SPDM_enable_new_discount_tag(session_data->get_spdm_ctx());
  const auto& pcoc_conf = RankKconfUtil::innerIndustryCPMCaliCombineHiveConf();
  auto iter_pcoc = pcoc_conf->find("exp7");
  if (iter_pcoc != pcoc_conf->end() && pcoc_weight_lower_bound <= pcoc_weight_upper_bound) {
    exp7_ratio = std::clamp(iter_pcoc->second, pcoc_weight_lower_bound, pcoc_weight_upper_bound);
  }
}

bool InnerIndustryUserEcpc::Admit(ContextData* session_data, AdCommon* ad) {
  bool flag = enable_industry_gpm_cali && ad->Is(AdFlag::is_inner_loop_deep_ad);
  return flag;
}

double InnerIndustryUserEcpc::Process(ContextData* session_data, AdCommon* ad) {
  double ratio = 1.0;
  double unify_gpm = ad->get_mix_unify_gpm();
  double gpm_thresh = inner_cali_gpm_thresh;
  double cpm_thresh = inner_cali_cpm_thresh;
  const auto& inner_buyer_industry_ids =
      session_data_->get_rank_request()->ad_request().ad_user_info().inner_buyer_industry_id_list();
  const auto& ad_gmv_distribution_list =
      session_data_->get_rank_request()->ad_request().ad_user_info().plateco_ue_distribution_list();
  const auto& ad_ctcvr_distribution_list =
      session_data->get_rank_request()->ad_request().ad_user_info().plateco_gpm_distribution_list();
  const auto& inner_low_load_list =
      session_data_->get_rank_request()->ad_request().ad_user_info().ad_buyer_effective_feature1_list();
  double gpm = ad->Attr(
    ItemIdx::multi_head_mix_gpm).GetDoubleValue(ad->AttrIndex()).value_or(0.0);
  double gpm_bucket_num = gpm / 1000000;
  int64 author_industry =
      ad->Attr(ItemIdx::fd_AUTHOR_industry).GetIntValue(ad->AttrIndex()).value_or(-1);
  int64 ad_request_times =
      session_data_->get_rank_request()->ad_request().ad_user_session_info().ad_request_times();
  auto buyer_effective_type = session_data_->get_rank_request()
        ->ad_request().ad_user_info().buyer_effective_type();
  struct tm tm_info;
  std::string page_id_str = absl::StrCat(session_data->get_page_id());
  time_t current_time = base::GetTimestamp() / 1000000;
  localtime_r(&current_time, &tm_info);
  std::string current_hour = absl::StrCat(tm_info.tm_hour);
  std::string ad_queue_type = AdEnum::AdQueueType_Name(ad->get_ad_queue_type());
  std::string item_type = AdEnum::ItemType_Name(ad->get_item_type());
  std::string cali_key;
  std::string cali_tag;
  std::string author_industry_str = absl::StrCat(author_industry);
  std::string ad_request_times_str = absl::StrCat(ad_request_times);
  std::string ad_gmv_level = "A1";
  double cpm = 0.0;
  double ad_ctr = 1.0;
  double model_predict_cvr = 1.0;  // 模型预估值
  double atv = 20.0;  // 后验 price 值
  double inner_live_default_gpm = 20;
  auto ocpc_action_type = ad->get_ocpx_action_type();
  double inner_qcpx_cvr_uplift_ratio = 1.0;
  if (ad->Is(AdFlag::is_photo_ad)) {
    inner_qcpx_cvr_uplift_ratio = ad->Attr(
      ItemIdx::inner_qcpx_photo_cvr_uplift_ratio).GetDoubleValue(ad->AttrIndex()).value_or(1.0);
      if (ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
        model_predict_cvr = ad->get_predict_score(PredictType::PredictType_reco_hard_order_paid);
        if (enable_origin_cvr_pred_value) {
          model_predict_cvr = ad->get_predict_score(PredictType::PredictType_c1_order_paid);
        } else if (enable_fix_gpm_ue_hc_calc_v1) {
          model_predict_cvr = ad->get_unify_cvr_info().value;
        }
        atv = ad->get_predict_score(PredictType::PredictType_merchant_ltv);
      } else {
        model_predict_cvr = ad->get_predict_score(PredictType::PredictType_reco_soft_order_paid);
        if (enable_origin_cvr_pred_value) {
          model_predict_cvr = ad->get_predict_score(PredictType::PredictType_order_paid);
        } else if (enable_fix_gpm_ue_hc_calc_v1) {
          model_predict_cvr = ad->get_unify_cvr_info().value;
        }
        atv = ad->get_predict_score(PredictType::PredictType_gmv);
    }
  } else {
    auto instance = UnivEspLiveAtvLoader::GetInstance();
    if (instance) {
      instance->GetPostGmvPerOrder(ad->get_author_id(),
                                    kuaishou::ad::AdActionType_Name(ocpc_action_type),
                                    &atv);  // NOLINT
    }
    atv = (atv > 0) ? atv / 1000 : inner_live_default_gpm;
    ad_ctr = ad->get_live_audience();
    if (ad->Is(AdFlag::is_p2l_ad)) {  // 短引
      if (ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
        ad_ctr = ad->get_cvr();
      }
    }
    inner_qcpx_cvr_uplift_ratio = ad->Attr(
      ItemIdx::inner_qcpx_live_cvr_uplift_ratio).GetDoubleValue(ad->AttrIndex()).value_or(1.0);
    model_predict_cvr = ad->get_predict_score(PredictType::PredictType_inner_live_atom_paycnt_front) +
                      ad->get_predict_score(PredictType::PredictType_inner_live_atom_paycnt_end);
  }
  inner_qcpx_cvr_uplift_ratio = std::max(inner_qcpx_cvr_uplift_ratio, 1.0);
  auto cpa_bid = ad->get_auto_cpa_bid() > 0 ? ad->get_auto_cpa_bid() : ad->get_cpa_bid();
  double roi_ratio =  ad->get_auto_roas() > 0 ? ad->get_auto_roas() : ad->get_roi_ratio();
  roi_ratio = std::max(roi_ratio, 0.0001);
  auto inner_ctcvr = std::clamp(ad_ctr * model_predict_cvr, 0.0, 1.0);
  if (enable_qcpx_cvr_value) {
    inner_ctcvr *= inner_qcpx_cvr_uplift_ratio;
  }
  double ori_roi_ratio = std::max(ad->get_roi_ratio(), 0.0001);
  double bid_for_compare = 1.0;
  double auto_cpa_bid = 1.0;
  double auto_roi_ratio = std::max(ad->get_auto_roas(), 0.0001);
  double t_low_load_ratio = 0.0;
  double t1_low_load_ratio = 0.0;
  double t2_low_load_ratio = 0.0;
  if (ocpc_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
      ocpc_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID) {
    cpm = ad_ctr * model_predict_cvr * cpa_bid;
    bid_for_compare = ad->get_cpa_bid() / 1000;
  } else if (ocpc_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
      ocpc_action_type == kuaishou::ad::AD_MERCHANT_T7_ROI ||
      ocpc_action_type == kuaishou::ad::AD_STOREWIDE_ROAS ||
      ocpc_action_type == kuaishou::ad::CID_ROAS ||
      ocpc_action_type == kuaishou::ad::AD_FANS_TOP_ROI) {
    cpm = ad_ctr * model_predict_cvr * atv / roi_ratio * 1000;
    bid_for_compare = atv / ori_roi_ratio;
    auto_cpa_bid = atv / auto_roi_ratio;
  }
  std::string auto_bid_ratio = "0.0";
  if (bid_for_compare > 0) {
    if (auto_cpa_bid / bid_for_compare < 0.8) {
      auto_bid_ratio = "0.8";
    } else if (auto_cpa_bid / bid_for_compare < 0.9) {
      auto_bid_ratio = "0.9";
    } else if (auto_cpa_bid / bid_for_compare < 1.0) {
      auto_bid_ratio = "1.0";
    } else if (auto_cpa_bid / bid_for_compare < 1.1) {
      auto_bid_ratio = "1.1";
    } else if (auto_cpa_bid / bid_for_compare < 1.2) {
      auto_bid_ratio = "1.2";
    } else if (auto_cpa_bid / bid_for_compare < 1.3) {
      auto_bid_ratio = "1.3";
    }
  }
  std::string low_load_tag = "no_low_load";
  if (enable_inner_low_load_ratio && inner_low_load_list.size() == 6) {
    if (inner_low_load_list.at(1) > 3 && inner_low_load_list.at(3) > 3 && inner_low_load_list.at(5) > 3) {
      t_low_load_ratio = inner_low_load_list.at(0) / inner_low_load_list.at(1);
      t1_low_load_ratio = inner_low_load_list.at(2) / inner_low_load_list.at(3);
      t2_low_load_ratio = inner_low_load_list.at(4) / inner_low_load_list.at(5);
      if (t_low_load_ratio > inner_low_load_thresh &&
          t1_low_load_ratio > inner_low_load_thresh &&
          t2_low_load_ratio > inner_low_load_thresh) {
        low_load_tag = "low_load_three_days";
      } else if (t_low_load_ratio > inner_low_load_thresh &&
                 t1_low_load_ratio > inner_low_load_thresh) {
        low_load_tag = "low_load_two_days";
      }
    }
  }

  std::string bid_level = "BID0";
  if (bid_for_compare > bid_p90_thres) {
    bid_level = "BID90";
  } else if (bid_for_compare > bid_p75_thres) {
    bid_level = "BID75";
  } else if (bid_for_compare > bid_p50_thres) {
    bid_level = "BID50";
  }
  double ctcvr_thres = ctcvr_thres_live;
  std::string ctcvr_level = "LESS";
  if (ad->Is(AdFlag::is_photo_ad)) {
    ctcvr_thres = ctcvr_thres_photo;
  } else if (ad->Is(AdFlag::is_p2l_ad)) {
    ctcvr_thres = ctcvr_thres_photo_to_live;
  }

  if (enable_ctcvr_daily_thres_ && ad_ctcvr_distribution_list.size() == 31) {
    int64_t index_number = 15;
    if (ad->Is(AdFlag::is_photo_ad)) {
      index_number = 29;
    } else if (ad->Is(AdFlag::is_p2l_ad)) {
      index_number = 22;
    }
    if (enable_daily_thres_p90_) {
      index_number -= 1;
    } else if (enable_daily_thres_p99_) {
      index_number += 1;
    }
    ctcvr_thres = ad_ctcvr_distribution_list.at(index_number);
    if (ctcvr_thres <= 1e-6 || ctcvr_thres >= 1.0) {
      ctcvr_thres = 1.0;
    }
  }
  if (inner_ctcvr > ctcvr_thres) {
    ctcvr_level = "OVER";
  }
  if (enable_ctcvr_daily_multi_percentile_thres_ && ad_ctcvr_distribution_list.size() == 31) {
    int64_t p95_index_number = 15;
    if (ad->Is(AdFlag::is_photo_ad)) {
      p95_index_number = 29;
    } else if (ad->Is(AdFlag::is_p2l_ad)) {
      p95_index_number = 22;
    }
    int64_t p90_index_number = p95_index_number - 1;
    int64_t p99_index_number = p95_index_number + 1;
    int64_t p75_index_number = p90_index_number - 1;
    int64_t p50_index_number = p75_index_number - 1;
    int64_t p25_index_number = p50_index_number - 1;
    if (p25_index_number >= 0 && p99_index_number < ad_ctcvr_distribution_list.size()
      && ad_ctcvr_distribution_list.at(p90_index_number) > 1e-6) {
      if (inner_ctcvr > ad_ctcvr_distribution_list.at(p99_index_number)) {
        ctcvr_level = "P99";
      } else if (inner_ctcvr > ad_ctcvr_distribution_list.at(p95_index_number)) {
        ctcvr_level = "P95";
      } else if (inner_ctcvr > ad_ctcvr_distribution_list.at(p90_index_number)) {
        ctcvr_level = "P90";
      } else if (inner_ctcvr > ad_ctcvr_distribution_list.at(p75_index_number)) {
        ctcvr_level = "P75";
      } else if (inner_ctcvr > ad_ctcvr_distribution_list.at(p50_index_number)) {
        ctcvr_level = "P50";
      } else if (inner_ctcvr > ad_ctcvr_distribution_list.at(p25_index_number)) {
        ctcvr_level = "P25";
      }
    }
  }
  if (enable_ctcvr_bid_level_concat) {
    ctcvr_level = absl::StrCat(ctcvr_level, "_", bid_level);
  }
  std::string cpm_level = "CPM0";
  if (cpm > 1e-6 && ad_ctcvr_distribution_list.size() == 31) {
    int64_t p75_index_number = 3;
    if (ad->Is(AdFlag::is_live_ad)) {
      p75_index_number = 8;
    }
    int64_t p50_index_number = p75_index_number - 1;
    int64_t p90_index_number = p75_index_number + 1;
    if (ad_ctcvr_distribution_list.at(p50_index_number) > 1e-6) {
      if (cpm > ad_ctcvr_distribution_list.at(p90_index_number)) {
        cpm_level = "CPM90";
      } else if (cpm > ad_ctcvr_distribution_list.at(p75_index_number)) {
        cpm_level = "CPM75";
      } else if (cpm > ad_ctcvr_distribution_list.at(p50_index_number)) {
        cpm_level = "CPM50";
      }
    }
  }
  double low_cpm_thres_1 = low_cpm_thres_1_10011;
  double low_cpm_thres_2 = low_cpm_thres_2_10011;
  if (page_id_str == "11001") {
    low_cpm_thres_1 = low_cpm_thres_1_11001;
    low_cpm_thres_2 = low_cpm_thres_2_11001;
  }
  std::string low_cpm_level = "None";
  if (cpm > 1e-6) {
    if (cpm < low_cpm_thres_1) {
      low_cpm_level = "Stage1";
    } else if (cpm < low_cpm_thres_2) {
      low_cpm_level = "Stage2";
    }
  }
  if (enable_buyer_type_lower_bound_ && (RankKconfUtil::innerIndustryGPMCaliConbineConf() != nullptr)) {
    double buyer_type_lower_bound = 0.0;
    auto& combine_kconf = RankKconfUtil::innerIndustryGPMCaliConbineConf()->data();
    auto& combine_conf = combine_kconf.confs();
    auto combine_iter = combine_conf.find(exp_tag);
    if (combine_iter != combine_conf.end()) {
      auto& config = combine_iter->second;
      auto it = config.buyer_type_lower_bound().find(buyer_effective_type);
      if (it != config.buyer_type_lower_bound().end()) {
        buyer_type_lower_bound = it->second;
        if (inner_ctcvr < buyer_type_lower_bound) {
          ctcvr_level = "LESS";
        }
      }
    }
  }
  if (ad_gmv_distribution_list.size() == 2) {
    double ad_gmv = std::max(ad_gmv_distribution_list.at(0), 0.0);
    double gmv_total = std::max(ad_gmv_distribution_list.at(1), 0.001);
    double ad_gmv_ratio = ad_gmv / gmv_total;
    if (ad_gmv_ratio >= ad_gmv_level_ratio_thres_A3) {
      ad_gmv_level = "A3";
    } else if (ad_gmv_ratio >= ad_gmv_level_ratio_thres_A2) {
      ad_gmv_level = "A2";
    }
  }
  if (enable_ad_gmv_shentou_tag && session_data->get_rank_request() &&
    session_data->get_rank_request()->has_ad_request() &&
    session_data->get_rank_request()->ad_request().has_ad_user_info()) {
    ad_gmv_level = absl::StrCat(session_data->get_rank_request()
    ->ad_request().ad_user_info().ad_gmv_shentou_tag());
  }
  if (enable_first_n_ecpc_inner && ad_request_times <= first_n_thresh) {
    cali_key = "first_n_thresh";
  }
  if (enable_industry_gpm_cali_by_u_level) {
    cali_key = buyer_effective_type;
  }
  if (enable_industry_gpm_cali_by_author_industry) {
    cali_key = absl::StrCat(author_industry);
  }
  if (enable_industry_gpm_cali_by_ad_queue_type) {
    cali_key = ad_queue_type;
  }
  if (enable_industry_gpm_cali_by_concat_key) {
    cali_key = absl::StrCat(ad_request_times, ",", buyer_effective_type, ",",
                            author_industry, ",", ad_queue_type);
  }
  std::string scene_oriented_type = absl::StrCat(ad->get_scene_oriented_type());
  bool combine_not_match = false;
  if (enable_inner_calib_combine && (RankKconfUtil::innerIndustryGPMCaliConbineConf() != nullptr)) {
    auto& kconf = RankKconfUtil::innerIndustryGPMCaliConbineConf()->data();
    auto& conf = kconf.confs();
    auto iter = conf.find(exp_tag);
    const auto& pcoc_conf = RankKconfUtil::innerIndustryCPMCaliCombineHiveConf();
    if (iter != conf.end()) {
      auto& config = iter->second;
      auto update_ratio = [&](const auto& map, const std::string& key) {
        auto it = map.find(key);
        if (it != map.end()) {
          ratio *= it->second;
        } else if (!map.empty() && enable_combine_only) {
          combine_not_match = true;
        }
      };
      update_ratio(config.u_level(), buyer_effective_type);
      update_ratio(config.author_industry(), author_industry_str);
      update_ratio(config.ad_queue(), ad_queue_type);
      update_ratio(config.ad_request_times(), ad_request_times_str);
      update_ratio(config.item_type(), item_type);
      update_ratio(config.req_hour(), current_hour);
      update_ratio(config.ad_gmv_level(), ad_gmv_level);
      update_ratio(config.ctcvr_level(), ctcvr_level);
      update_ratio(config.page_id(), page_id_str);
      update_ratio(config.cpm_level(), cpm_level);
      update_ratio(config.low_cpm_level(), low_cpm_level);
      update_ratio(config.scene_oriented_type(), scene_oriented_type);
      update_ratio(config.auto_bid_ratio(), auto_bid_ratio);
      if (use_daily_pcoc_params && !combine_not_match && pcoc_conf && !pcoc_conf->empty()) {
        auto iter_pcoc = pcoc_conf->find(exp_tag);
        if (iter_pcoc != pcoc_conf->end() && pcoc_weight_lower_bound <= pcoc_weight_upper_bound) {
          ratio = std::clamp(iter_pcoc->second, pcoc_weight_lower_bound, pcoc_weight_upper_bound);
        }
      }
      if (!combine_not_match && !enable_new_discount_tag) {
          ad->Attr(ItemIdx::is_inner_high_quality_ecpc).SetIntValue(ad->AttrIndex(), 1, false, false); // NOLINT
      }
      if (enable_combine_second_exp_config_ && combine_not_match) {
        auto iter_second = conf.find(second_exp_tag_);
        if (iter_second != conf.end()) {
          ratio = 1.0;
          combine_not_match = false;
          auto& second_config = iter_second->second;
          update_ratio(second_config.u_level(), buyer_effective_type);
          update_ratio(second_config.author_industry(), author_industry_str);
          update_ratio(second_config.ad_queue(), ad_queue_type);
          update_ratio(second_config.ad_request_times(), ad_request_times_str);
          update_ratio(second_config.item_type(), item_type);
          update_ratio(second_config.req_hour(), current_hour);
          update_ratio(second_config.ad_gmv_level(), ad_gmv_level);
          update_ratio(second_config.page_id(), page_id_str);
          if (use_daily_pcoc_params && !combine_not_match && pcoc_conf && !pcoc_conf->empty()) {
            auto iter_pcoc_second = pcoc_conf->find(second_exp_tag_);
            if (iter_pcoc_second != pcoc_conf->end() && pcoc_weight_lower_bound <= pcoc_weight_upper_bound) {
              ratio = std::clamp(iter_pcoc_second->second, pcoc_weight_lower_bound, pcoc_weight_upper_bound);
            }
          }
        }
      }
    }
    if (combine_not_match) {
      ratio = 1.0;
    }
    if (enable_daily_unify_cpm_thres_ && ad_ctcvr_distribution_list.size() == 31) {
      int64_t unify_cpm_index = 3;
      if (ad->Is(AdFlag::is_live_ad)) {
        unify_cpm_index = 8;
      }
      if (enable_unify_cpm_thres_p50_) {
        unify_cpm_index -= 1;
      } else if (enable_unify_cpm_thres_p90_) {
        unify_cpm_index += 1;
      }
      double unify_cpm_thres = ad_ctcvr_distribution_list.at(unify_cpm_index);
      if (unify_cpm_thres >= 1e-6 && cpm > unify_cpm_thres) {
        ratio = 1.0;
      }
    }
    if (ratio != 1.0 && enable_new_discount_tag) {
      ad->Attr(ItemIdx::is_inner_high_quality_ecpc).SetIntValue(ad->AttrIndex(), 1, false, false); // NOLINT
    }
  } else if (RankKconfUtil::innerIndustryGPMCaliConf() != nullptr) {
    auto& kconf = RankKconfUtil::innerIndustryGPMCaliConf()->data();
    auto& conf = kconf.confs();
    auto iter = conf.find(exp_tag);
    if (iter != conf.end()) {
      auto& cali_coef = ad->Is(AdFlag::is_photo_ad) ? iter->second.photo_coef() :
        (ad->Is(AdFlag::is_p2l_ad) ? iter->second.p2l_coef() : iter->second.live_coef());
      auto iter_coef = cali_coef.find(cali_key);
      if (iter_coef != cali_coef.end()) {
        ratio = iter_coef->second;
      }
      auto iter_gpm = cali_coef.find("gpm_thresh");
      if (iter_gpm != cali_coef.end()) {
        gpm_thresh = iter_gpm->second;
      }
      if (enable_industry_gpm_cali_cpm_only) {
        auto iter_cpm = cali_coef.find("cpm_thresh");
        if (iter_cpm != cali_coef.end()) {
          cpm_thresh = iter_cpm->second;
        }
      }
    }
  }

  double ctcvr_ratio = 1.0;
  if (enable_inner_ctcvr_calib_combine && (RankKconfUtil::innerIndustryCtcvrCaliCombineConf() != nullptr)) {
    auto& kconf = RankKconfUtil::innerIndustryCtcvrCaliCombineConf()->data();
    auto& conf = kconf.confs();
    combine_not_match = false;
    auto iter = conf.find(exp_ctcvr_cali_tag);
    if (iter != conf.end()) {
      auto& config = iter->second;
      auto update_ratio = [&](const auto& map, const std::string& key) {
        auto it = map.find(key);
        if (it != map.end()) {
          ctcvr_ratio *= it->second;
        } else if (!map.empty() && enable_combine_only_ctcvr) {
          combine_not_match = true;
        }
      };
      update_ratio(config.u_level(), buyer_effective_type);
      update_ratio(config.author_industry(), author_industry_str);
      update_ratio(config.ad_queue(), ad_queue_type);
      update_ratio(config.ad_request_times(), ad_request_times_str);
      update_ratio(config.item_type(), item_type);
      update_ratio(config.req_hour(), current_hour);
      update_ratio(config.ad_gmv_level(), ad_gmv_level);
      update_ratio(config.ctcvr_level(), ctcvr_level);
      update_ratio(config.page_id(), page_id_str);
      update_ratio(config.cpm_level(), cpm_level);
      update_ratio(config.low_cpm_level(), low_cpm_level);
      update_ratio(config.scene_oriented_type(), scene_oriented_type);
      update_ratio(config.auto_bid_ratio(), auto_bid_ratio);
      update_ratio(config.low_load_tag(), low_load_tag);
      if (!combine_not_match && !enable_new_discount_tag) {
          ad->Attr(ItemIdx::is_inner_industry_ctcvr_cali_ecpc).SetIntValue(ad->AttrIndex(), 1, false, false); // NOLINT
      }
      if (combine_not_match) {
        ctcvr_ratio = 1.0;
      }
      if (enable_daily_ctcvr_thres_ && ad_ctcvr_distribution_list.size() == 31) {
        int64_t unify_cpm_index = 3;
        if (ad->Is(AdFlag::is_live_ad)) {
          unify_cpm_index = 8;
        }
        if (enable_unify_cpm_ctcvr_thres_p50_) {
          unify_cpm_index -= 1;
        } else if (enable_unify_cpm_ctcvr_thres_p90_) {
          unify_cpm_index += 1;
        }
        double unify_cpm_thres = ad_ctcvr_distribution_list.at(unify_cpm_index);
        if (unify_cpm_thres >= 1e-6 && cpm > unify_cpm_thres) {
          ctcvr_ratio = 1.0;
        }
      }
      if (ctcvr_ratio != 1.0 && enable_new_discount_tag) {
        ad->Attr(ItemIdx::is_inner_industry_ctcvr_cali_ecpc).SetIntValue(ad->AttrIndex(), 1, false, false); // NOLINT
      }
    }
  }
  ratio *= ctcvr_ratio;
  if (enable_industry_gpm_cali_cpm_only) {
    cali_tag = "CPM";
    if (ad->get_cpm() < cpm_thresh) {
      ratio = 1.0;
    }
  } else {
    if (unify_gpm < gpm_thresh) {
      ratio = 1.0;
    }
    cali_tag = "GPM";
    ad->set_mix_unify_gpm(ad->get_mix_unify_gpm() * ratio);
  }
  if (enable_exp7_inner_cpm_cali) {
    if ((buyer_effective_type == "U4" || buyer_effective_type == "U4+") && (item_type == "ITEM_LIVE")) {
      ratio *= exp7_ratio;
    }
  }
  const auto& inner_smb_cali_daily_conf = RankKconfUtil::innerSmbCaliDailyKconf();
  if (enable_inner_mix_unify_gpm_cali && inner_smb_cali_daily_conf) {
    double gpm_cali_ratio = 1.0;
    if (gpm > FLT_EPSILON && gpm < 1e9) {
      std::string gpm_bucket = GetGpmBucket(gpm_bucket_num);
      std::string key = absl::StrCat(gpm_bucket,
                                "_", item_type,
                                "_", buyer_effective_type);
      auto it_cali_ratio = inner_smb_cali_daily_conf->find(key);
      if (it_cali_ratio != inner_smb_cali_daily_conf->end()) {
        gpm_cali_ratio = it_cali_ratio->second;
      }
      gpm *= gpm_cali_ratio;
      ad->Attr(ItemIdx::multi_head_mix_gpm).SetDoubleValue(
            ad->AttrIndex(), gpm, false, false);
    }
  }
  RANK_DOT_STATS(session_data, ratio * 1e3, "industry_gpm_hc_cali_ratio",
                item_type, buyer_effective_type, cali_tag);
  RANK_DOT_STATS(session_data, unify_gpm * 1e3, "industry_gpm_hc_gpm_ratio",
                item_type, buyer_effective_type, cali_tag);
  return ratio;
}

std::string InnerIndustryUserEcpc::GetGpmBucket(double gpm) {
  if (gpm > 0.0 && gpm <= 20.0) {
    return "<=20";
  } else if (gpm > 20.0 && gpm <= 40.0) {
    return "<=40";
  } else if (gpm > 40.0 && gpm <= 60.0) {
    return "<=60";
  } else if (gpm > 60.0 && gpm <= 80.0) {
    return "<=80";
  } else if (gpm > 80.0 && gpm <= 100.0) {
    return "<=100";
  } else if (gpm > 100.0 && gpm <= 150.0) {
    return "<=150";
  } else if (gpm > 150.0 && gpm <= 200.0) {
    return "<=200";
  } else if (gpm > 200.0 && gpm <= 250.0) {
    return "<=250";
  } else if (gpm > 250.0 && gpm <= 300.0) {
    return "<=300";
  } else if (gpm > 300.0) {
    return "300+";
  } else {
    return "unknown";
  }
}

RegisterClass(UdfFactor, InnerIndustryUserEcpc);
}  // namespace ad_rank
}  // namespace ks

