#include <string>
#include <utility>
#include <algorithm>
#include <memory>
#include <vector>
#include <tuple>

#include "ks/base/abtest/session_context.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/engine_base/dragon_node/skydome_node.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/engine_base/p2p_cache_loader/industry_playlet_sdpa.h"
#include "teams/ad/engine_base/spdm/spdm_switches.h"
#include "teams/ad/engine_base/billing_separate/billing_separate_weight_manager.h"
#include "teams/ad/ad_rank/processor/framework/function_manager.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/data/p2p_data/universe_conv_ratio_author_post_data_p2p/universe_conv_ratio_author_post_data_p2p.h"

using kuaishou::ad::AdEnum;

namespace ks {
namespace ad_rank {


bool FindStateName(ContextData* session_data,
      AdCommon* p_ad,
      const std::string& first_industry_id,
      const std::string& second_industry_id,
      const std::string& author_id,
      const std::string& photo_id,
      const std::string& account_id,
      const std::string& corporation_name,
      std::vector<std::string>* p_state_name_vec,
      bool is_storewide_cost_order_author) {
  // 投放配置
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<
    ::ks::engine_base::kconf::AdMerchantConf>> ad_merchant_conf_ = engine_base::AdKconfUtil::adMerchantConf();
  if (nullptr == ad_merchant_conf_) {
    return false;
  }
  //  全站冷启
  int64_t cost_delta_max_second = RankKconfUtil::costDeltaMaxSecond();
  int64_t cost = p_ad->Attr(ItemIdx::fd_CAMPAIGN_pc_target_cost_total_sum_price).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  int64_t update_timestamp = p_ad->Attr(ItemIdx::fd_CAMPAIGN_pc_target_cost_today_sum_timestamp).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  int64_t diff_time = session_data->get_current_timestamp_nodiff() / 1000000 - update_timestamp;
  if ((p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE ||
       p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE) &&
      p_ad->get_storewide_incompatible_type() == 1 && diff_time < cost_delta_max_second &&
      (RankKconfUtil::storewideColdStartTail()->count(p_ad->get_author_id() % 100) ||
       RankKconfUtil::storewideColdStartTail()->count(p_ad->get_author_id()))) {
    std::string state_name = "";
    auto storewide_cold_start_state = RankKconfUtil::storewideColdStartState();
    for (auto it = storewide_cold_start_state->begin(); it != storewide_cold_start_state->end(); ++it) {
      if (cost < it->first) {
          state_name = it->second;
          break;
      }
    }
    LOG_EVERY_N(INFO, 10000) << "debug storewide_cold_start_state_ rank price."
                          << " ,state_name: " << state_name
                          << " ,cost: " << cost
                          << ", diff_time: " << diff_time
                          << ", update_timestamp: " << update_timestamp
                          << " ,author_id: " << p_ad->get_author_id()
                          << " ,campaign_id: " << p_ad->get_campaign_id();
    if (state_name.size() > 0) {
      p_state_name_vec->push_back(state_name);
    }
  }

  /**商品全站：rank获取计费打折系数逻辑begin**/
  if (p_ad->get_scene_oriented_type() == 24 &&
      p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
    bool enable_merchant_storewide_bs_tag =
        engine_base::SPDM_enable_merchant_storewide_bs_tag(session_data->get_spdm_ctx());
    std::string merchant_storewide_bs_tag =
        engine_base::SPDM_merchant_storewide_bs_tag(session_data->get_spdm_ctx());
    if (enable_merchant_storewide_bs_tag && merchant_storewide_bs_tag != "") {
      p_state_name_vec->push_back(merchant_storewide_bs_tag);
    }
  }

  // 过滤器
  std::vector<std::string> ad_merchant_state_tag_;
  ad_merchant_state_tag_.clear();
  bool enable_opt_tag_set = engine_base::SPDM_enable_opt_tag_set(session_data->get_spdm_ctx());
  std::string ad_merchant_state_tag_str =
    engine_base::SPDM_ad_merchant_state_tag(session_data->get_spdm_ctx());
  if (enable_opt_tag_set) {
    std::string ad_merchant_opt_state_tag_str =
        engine_base::SPDM_ad_merchant_opt_state_tag(session_data->get_spdm_ctx());
    if (ad_merchant_opt_state_tag_str.size() > 0) {
      std::vector<absl::string_view> opt_tag_set = absl::StrSplit(
        ad_merchant_opt_state_tag_str, ",", absl::SkipEmpty());
      std::for_each(opt_tag_set.begin(), opt_tag_set.end(), [&] (const absl::string_view& tag_view) {
        ad_merchant_state_tag_.push_back({tag_view.begin(), tag_view.end()});
    });
  }} else {
    if (ad_merchant_state_tag_str.size() > 0) {
      std::vector<absl::string_view> tag_set = absl::StrSplit(
                  ad_merchant_state_tag_str, ",", absl::SkipEmpty());
        std::for_each(tag_set.begin(), tag_set.end(), [&] (const absl::string_view& tag_view) {
          ad_merchant_state_tag_.push_back({tag_view.begin(), tag_view.end()});
      });
    }
  }

  auto& conf_map = ad_merchant_conf_->data().conf_map();  // map
  // account > author > corporation > ab_tag > second_industry > first_industry
  auto iter = conf_map.account_conf().find(account_id);
  if (iter != conf_map.account_conf().end()) {
    p_state_name_vec->push_back(iter->second);
  }

  iter = conf_map.author_conf().find(author_id);
  if (iter != conf_map.author_conf().end()) {
    p_state_name_vec->push_back(iter->second);
  }

  iter = conf_map.photo_conf().find(photo_id);
  if (iter != conf_map.photo_conf().end()) {
    p_state_name_vec->push_back(iter->second);
  }

  iter = conf_map.corporation_conf().find(corporation_name);
  if (iter != conf_map.corporation_conf().end()) {
    p_state_name_vec->push_back(iter->second);
  }

  for (auto tag : ad_merchant_state_tag_) {
    auto iter_tag = conf_map.tag_conf().find(tag);
    if (iter_tag != conf_map.tag_conf().end()) {
      p_state_name_vec->push_back(iter_tag->second);
    }
  }

  bool enable_inner_coldstart_tag =
      engine_base::SPDM_enable_inner_coldstart_tag(session_data->get_spdm_ctx());
  std::string inner_coldstart_tag =
      engine_base::SPDM_inner_coldstart_tag(session_data->get_spdm_ctx());
  int64_t inner_coldstart_tag_id_start =
        engine_base::SPDM_inner_coldstart_tag_id_start(session_data->get_spdm_ctx());
  int64_t inner_coldstart_tag_id_end =
        engine_base::SPDM_inner_coldstart_tag_id_end(session_data->get_spdm_ctx());
  bool enable_coldstart_adjust_price =
        SPDM_enable_coldstart_adjust_price(session_data->get_spdm_ctx());
  // 冷启动 tag
  if (enable_inner_coldstart_tag && !enable_coldstart_adjust_price &&  inner_coldstart_tag != "" &&
    (p_ad->get_author_id() % 100) >= inner_coldstart_tag_id_start &&
    (p_ad->get_author_id() % 100) < inner_coldstart_tag_id_end ) {
    std::vector<std::string> tag_set = absl::StrSplit(inner_coldstart_tag, ",", absl::SkipEmpty());
    for (auto tag : tag_set) {
      p_state_name_vec->push_back(tag);
    }
  }

  // 高价值 tag
  bool enable_consum_power_price_discount =
      SPDM_enable_consum_power_price_discount(session_data->get_spdm_ctx());
  auto enable_consum_power_price_discount_separate =
      SPDM_enable_consum_power_price_discount_separate(session_data->get_spdm_ctx());
  std::string inner_high_value_visitor_tag =
      SPDM_inner_high_value_visitor_tag(session_data->get_spdm_ctx());
  bool enable_high_value_u_level =
      SPDM_enable_high_value_u_level(session_data->get_spdm_ctx());
  double high_value_atv_thresh =
      SPDM_high_value_atv_thresh(session_data->get_spdm_ctx());
  const auto& consum_power_valid_set = RankKconfUtil::consumPowerDiscountTag();
  const auto& u_level_valid_set = RankKconfUtil::consumPowerUlevelDiscountSet();
  float user_phone_price = session_data->get_rank_request()->ad_request().ad_user_info().phone_price();
  std::string consum_power_tag =
      session_data->get_rank_request()->ad_request().ad_user_info().consum_power_tag();
  std::string buyer_effective_type =
      session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
  if (enable_consum_power_price_discount && inner_high_value_visitor_tag != "" &&
      p_ad->Is(AdFlag::is_inner_loop_deep_ad) && consum_power_valid_set &&
      consum_power_valid_set->count(consum_power_tag)) {
    bool valid = true;
    if (enable_high_value_u_level) {
      valid = u_level_valid_set && u_level_valid_set->count(buyer_effective_type);
    }
    if (enable_consum_power_price_discount_separate) {
      valid = false;
    }
    if (valid) {
      p_state_name_vec->push_back(inner_high_value_visitor_tag);
    }
  }

  std::string inner_up_items_tag =
      engine_base::SPDM_inner_up_items_tag(session_data->get_spdm_ctx());
  int64_t inner_up_items_tag_id_start =
        engine_base::SPDM_inner_up_items_tag_id_start(session_data->get_spdm_ctx());
  int64_t inner_up_items_tag_id_end =
        engine_base::SPDM_inner_up_items_tag_id_end(session_data->get_spdm_ctx());
  // 起量素材 tag
  if (inner_up_items_tag != "" &&
    (p_ad->get_author_id() % 100) >= inner_up_items_tag_id_start &&
    (p_ad->get_author_id() % 100) < inner_up_items_tag_id_end ) {
    p_state_name_vec->push_back(inner_up_items_tag);
  }

  iter = conf_map.second_industry_conf().find(second_industry_id);
  if (iter != conf_map.second_industry_conf().end()) {
    p_state_name_vec->push_back(iter->second);
  }

  iter = conf_map.first_industry_conf().find(first_industry_id);
  if (iter != conf_map.first_industry_conf().end()) {
    p_state_name_vec->push_back(iter->second);
  }
  return p_state_name_vec->size() > 0;
}

bool ExecGetPriceRatio(
    const bool enable_innerloop_discount_control,
    absl::flat_hash_map<int64_t, bool> innerloop_discount_budget_over_tag,
    std::shared_ptr<
      ks::ad_base::kconf::ProtoKconf<::ks::engine_base::kconf::AdMerchantConf>> ad_merchant_conf_,
    const int64_t req_price_tag,
    const std::string& state_name,
    const std::string& ocpx_action_type,
    const std::string& scene_oriented_type,
    const std::string& speed_type,
    const std::string& bid_strategy,
    const std::string& item_type,
    const int64_t& storewide_incompatible_type,
    const std::string& account_type,
    const int64_t& is_live_cold_start,
    const std::string& page_id,
    const int64_t& is_author_fans,
    const int64_t& is_crowd_tag_r3,
    const int32_t& seller_category,
    const int32_t& native_degree_tag,
    double* p_price_ratio) {
  auto state_iter = ad_merchant_conf_->data().state_info().find(state_name);
  if (state_iter == ad_merchant_conf_->data().state_info().end()) {
    return false;
  }
  auto &state_info = state_iter->second;
  auto &ocpx_map = state_info.ocpx_action_type();
  auto &st_map = state_info.scene_oriented_type();
  auto &speed_type_map = state_info.speed_type();
  auto &bid_strategy_map = state_info.bid_strategy();
  auto &item_type_map = state_info.item_type();
  auto &incompatible_type_map = state_info.storewide_incompatible_type();
  auto &account_type_map = state_info.account_type();
  auto &is_live_cold_start_map = state_info.live_coldstart();
  auto &page_id_map = state_info.page_id();
  auto &is_fans_map = state_info.is_fans_follow_type();
  auto &is_r3_map = state_info.is_crowd_tag_r3_type();
  auto &seller_category_map = state_info.seller_category_price();
  auto &native_degree_tag_map = state_info.native_degree_tag();
  int64_t kconf_price_tag = state_info.price_tag();

  // kconf_price_tag 没填则默认取 60，如果配置了其他的，则需要和请求时的对应才继续往下走
  if (kconf_price_tag == 0) {
    kconf_price_tag = AdjustPriceTag::AdMerchantPriceDiscount;
  }
  if (kconf_price_tag != req_price_tag) {
    return false;
  }


  bool ocpx_valid = ocpx_map.size() == 0 || ocpx_map.find(ocpx_action_type) != ocpx_map.end();
  bool st_valid = st_map.size() == 0 || st_map.find(scene_oriented_type) != st_map.end();
  bool speed_type_valid =
    speed_type_map.size() == 0 || speed_type_map.find(speed_type) != speed_type_map.end();
  bool bid_strategy_valid =
    bid_strategy_map.size() == 0 || bid_strategy_map.find(bid_strategy) != bid_strategy_map.end();
  bool item_type_valid = item_type_map.size() == 0 || item_type_map.find(item_type) != item_type_map.end();
  bool incompatible_valid = incompatible_type_map.size() == 0 ||
    incompatible_type_map.find(storewide_incompatible_type) != incompatible_type_map.end();
  bool account_type_valid = account_type_map.size() == 0 ||
    account_type_map.find(account_type) != account_type_map.end();
  bool is_live_cold_start_valid = is_live_cold_start_map.size() == 0 ||
    is_live_cold_start_map.find(is_live_cold_start) != is_live_cold_start_map.end();;
  bool is_page_valid = page_id_map.size() == 0 || page_id_map.find(page_id) != page_id_map.end();
  bool is_fans = is_fans_map.size() == 0 || is_fans_map.find(is_author_fans) != is_fans_map.end();
  bool is_r3 = is_r3_map.size() == 0 || is_r3_map.find(is_crowd_tag_r3) != is_r3_map.end();
  bool is_seller_category_empty = seller_category_map.size() == 0;
  bool is_native_degree_tag_valid = native_degree_tag_map.size() == 0 ||
    native_degree_tag_map.find(native_degree_tag) != native_degree_tag_map.end();

  if (!(ocpx_valid && st_valid && speed_type_valid && bid_strategy_valid && item_type_valid &&
        incompatible_valid && account_type_valid && is_live_cold_start_valid && is_page_valid &&
        is_fans && is_r3 && is_native_degree_tag_valid)) {
    return false;
  }
  // tag 打折金额用光后，不再顶价打折
  if (enable_innerloop_discount_control &&
      innerloop_discount_budget_over_tag.count(kconf_price_tag) > 0) {
    ks::infra::PerfUtil::IntervalLogStash(1, "ad.ad_rank", "innerloop_discount_cost",
                                          "tag_budget_over", absl::StrCat(kconf_price_tag));
    return false;
  }
  // 分 L 客户单独设 bid
  if (!is_seller_category_empty) {
    auto iter = seller_category_map.find(seller_category);
    if (iter != seller_category_map.end()) {
      double price_ratio = iter->second;
      *p_price_ratio = price_ratio;
      return true;
    }
  }
  *p_price_ratio = state_info.price_ratio();
  return true;
}

void AdjustPriceSmbPriceRatio_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto new_cust_bs_tail = RankKconfUtil::newCustomerBsTails();
  auto pure_new_customer_gmv_bs_tail = RankKconfUtil::pureNewCustomerGMVBSTail();
  auto pure_new_customer_bs_tail = RankKconfUtil::pureNewCustomerBSTail();
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    auto& ad = *p_ad;
    // 新客准入
    if (p_ad->Is(AdFlag::is_inner_loop_ad) &&
        !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) && p_ad->get_smb_level_tag() == 4 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        new_cust_bs_tail && new_cust_bs_tail->count(p_ad->get_account_id() % 100) > 0) {
      return true;
    }
    // 纯新客准入
    bool pure_new_cust_bs = p_ad->Is(AdFlag::is_inner_loop_ad) && !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        p_ad->Attr(ItemIdx::last_30d_cost).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0) < 0.01 &&
        p_ad->Attr(ItemIdx::unit_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0) != 1 &&
        pure_new_customer_gmv_bs_tail &&
        pure_new_customer_gmv_bs_tail->count(
        p_ad->Attr(ItemIdx::gmv_level).GetIntValue(p_ad->AttrIndex()).value_or(0)) > 0 &&
        p_ad->Attr(ItemIdx::is_vh).GetIntValue(p_ad->AttrIndex()).value_or(0) != 1 &&
        pure_new_customer_bs_tail &&
        pure_new_customer_bs_tail->count(p_ad->get_account_id() % 100);
    if (pure_new_cust_bs) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceSmbPriceRatio_Compute(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_migrate_new_cust_bs =
      SPDM_enable_migrate_new_cust_bs(session_data->get_spdm_ctx());
  auto new_cust_bs_tail = RankKconfUtil::newCustomerBsTails();
  auto pure_new_customer_gmv_bs_tail = RankKconfUtil::pureNewCustomerGMVBSTail();
  auto pure_new_customer_bs_tail = RankKconfUtil::pureNewCustomerBSTail();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    ad.set_record_gsp_price(ad.get_price());
    // 计费系数
    double ratio = 1.0;
    bool pure_new_cust_bs = p_ad->Is(AdFlag::is_inner_loop_ad) && !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        p_ad->Attr(ItemIdx::last_30d_cost).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0) < 0.01 &&
        p_ad->Attr(ItemIdx::unit_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0) != 1 &&
        pure_new_customer_gmv_bs_tail &&
        pure_new_customer_gmv_bs_tail->count(
        p_ad->Attr(ItemIdx::gmv_level).GetIntValue(p_ad->AttrIndex()).value_or(0)) > 0 &&
        p_ad->Attr(ItemIdx::is_vh).GetIntValue(p_ad->AttrIndex()).value_or(0) != 1 &&
        pure_new_customer_bs_tail &&
        pure_new_customer_bs_tail->count(p_ad->get_account_id() % 100);

    if ((enable_migrate_new_cust_bs && p_ad->Is(AdFlag::is_inner_loop_ad) &&
        !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) && p_ad->get_smb_level_tag() == 4 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        new_cust_bs_tail && new_cust_bs_tail->count(p_ad->get_account_id() % 100) > 0) ||
        pure_new_cust_bs) {
      ratio = ad.get_price_ratio() < 0.01 || ad.get_price_ratio() >= 1.0 ? 1.0 :
              ad.get_price_ratio();
      if (ad.get_price_ratio() > 1.0) {
        ratio = ad.get_price_ratio();
      }
     }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustInnerSelfServicePrice_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto self_service_bs_tail = RankKconfUtil::selfServiceBsTail();
  bool enable_inner_white_box_new_customer_discount =
      SPDM_enable_inner_white_box_new_customer_discount(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
      return false;
    }
    int fd_CAMPAIGN_benefit
        = p_ad->Attr(ItemIdx::fd_CAMPAIGN_benefit)
          .GetIntValue(p_ad->AttrIndex()).value_or(0);
    if (enable_inner_white_box_new_customer_discount &&
        fd_CAMPAIGN_benefit == 1) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustInnerSelfServicePrice_Compute(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 0.5;
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceLpsdeep_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_lpsdeep_support = SPDM_enable_lpsdeep_support(session_data->get_spdm_ctx());
  bool enable_edu_lps_deep_support = SPDM_enable_edu_lps_deep_support(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (SPDM_enableDebugAuctionMove()) {
      LOG(INFO) << "debug wangjiabin, AdjustPriceLpsdeep admit, llsid, " << session_data->get_llsid()
        << " cid, " << p_ad->creative_id() << " enable_lpsdeep_support, " << enable_lpsdeep_support
        << " dct, " << p_ad->get_origin_deep_conversion_type()
        << " oat, " << p_ad->get_ocpx_action_type();
    }
    // 教育深度正价课准入
    if (enable_edu_lps_deep_support &&
        p_ad->get_deep_conversion_type() ==
        kuaishou::ad::AdCallbackLog_EventType_EVENT_HIGH_PRICE_CLASS_PAY) {
      return true;
    }
    if (!enable_lpsdeep_support) {
      return false;
    }
    // 出价类型准入
    if (p_ad->get_origin_deep_conversion_type() ==
        kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceLpsdeep_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto lps_deep_conf = engine_base::AdKconfUtil::lpsdeepSupportConf();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    if (lps_deep_conf == nullptr) {
      return 1.0;
    }
    double ratio = 1.0;

    // 优先级 : account > ocpx
    const std::string& ocpx_action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    const std::string& lpsdeep_support_decay_group =
        SPDM_enable_lpsdeep_support_decay(session_data->get_spdm_ctx());
    if (SPDM_enableDebugAuctionMove()) {
      LOG(INFO) << "debug wangjiabin, AdjustPriceLpsdeep compute, llsid, " << session_data->get_llsid()
          << " cid, " << p_ad->creative_id()
          << " dct, " << p_ad->get_origin_deep_conversion_type()
          << " oat, " << p_ad->get_ocpx_action_type()
          << " lpsdeep_support_decay_group, " << lpsdeep_support_decay_group
          << " acid, " << p_ad->get_account_id();
    }
    auto iter_ocpx = lps_deep_conf->data().ocpx().find(ocpx_action_type);
    if (iter_ocpx != lps_deep_conf->data().ocpx().end()) {
      ratio = iter_ocpx->second.price_discount();
    }
    if (SPDM_enable_lps_acquisition_support_exp_decay(session_data->get_spdm_ctx())
       && lpsdeep_support_decay_group != "" && lpsdeep_support_decay_group != "default") {
      auto iter_exp = lps_deep_conf->data().ocpx_exp().find(lpsdeep_support_decay_group);
      if (iter_exp != lps_deep_conf->data().ocpx_exp().end()) {
        // exp 优先级：mcb > c_customer > ocpx
        // 1. ocpx
        auto iter_ocpx_group = iter_exp->second.ocpx().find(ocpx_action_type);
        if (iter_ocpx_group != iter_exp->second.ocpx().end()) {
          ratio = iter_ocpx_group->second.price_discount();
        }
        // 2. c_customer
        int maa_cost_level_90d = p_ad->Attr(
          ItemIdx::fd_ACCOUNT_maa_cost_level_90d).GetIntValue(p_ad->AttrIndex()).value_or(0);
        maa_cost_level_90d--;
        std::string maa_cost_level_90d_key = absl::StrCat("C", maa_cost_level_90d);
        auto iter_cx_group = iter_exp->second.c_customer().find(maa_cost_level_90d_key);
        if (iter_cx_group != iter_exp->second.c_customer().end()) {
          ratio = iter_cx_group->second.price_discount();
        }
        // 3. mcb
        std::string bid_type_key = "none";
        if (p_ad->get_bid_type() == kuaishou::ad::AdEnum::MCB) {
          bid_type_key = "mcb";
        }
        auto iter_bid_group = iter_exp->second.bid_type().find(bid_type_key);
        if (iter_bid_group != iter_exp->second.bid_type().end()) {
          ratio = iter_bid_group->second.bid_inc();
        }

        if (ratio != 1.0) {
          RANK_DOT_COUNT(session_data, 1, "LpsdeepPriceStrategyExp",
            ocpx_action_type, maa_cost_level_90d_key, bid_type_key);
        }
      }
    }
    auto iter_account = lps_deep_conf->data().account().find(p_ad->get_account_id());
    if (iter_account != lps_deep_conf->data().account().end()) {
      ratio = iter_account->second.price_discount();
    }
    if (ratio != 1.0 && p_ad->get_deep_conversion_type() ==
      kuaishou::ad::AdCallbackLog_EventType_EVENT_HIGH_PRICE_CLASS_PAY) {
     RANK_DOT_COUNT(session_data, 1, "EduHighClassPriceDownEcpmStrategy", ocpx_action_type);
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPricePrivateMessage_Admit(ks::platform::AddibleRecoContextInterface* context,
                                     platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_adjust_price_private_message =
      SPDM_enable_adjust_price_private_message(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    if (!enable_adjust_price_private_message) {
      return false;
    }
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 出价类型准入
    // 一期，支持私信实时获取部分
    int64_t conversion_path = p_ad->Attr(ItemIdx::fd_ad_magicsite_page_das_conversion_path).GetIntValue(
                                  p_ad->AttrIndex()).value_or(0);
    int64_t sub_conversion_path = p_ad->Attr(
         ItemIdx::fd_ad_magicsite_page_das_sub_conversion_path).GetIntValue(p_ad->AttrIndex()).value_or(0);
    bool no_realtime_flag = ((conversion_path & 2) == 2 && (sub_conversion_path & 4) == 4);
    auto sx_account_map = RankKconfUtil::SXAccountMap();
    bool account_in_list = (sx_account_map != nullptr && sx_account_map->count(p_ad->get_account_id()) > 0);
    if (((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION)
         || (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_PRIVATE_MESSAGE_SENT &&
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE &&
             p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SELF_SERVICE)
         || ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_LOCAL_LIFE_PROMOTION ||
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_DAWN_PROJECT_SPEEDY ||
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_DAWN_PROJECT_PRODUCT)) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_PRIVATE_MESSAGE_SENT ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::LEADS_SUBMIT))
         || no_realtime_flag || account_in_list)
    ) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPricePrivateMessage_Compute(ks::platform::AddibleRecoContextInterface* context,
                                       ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  // 统一打折系数配置
  auto private_message_price_discount_conf =
      engine_base::AdKconfUtil::privateMessagePriceDiscountConf();

  const std::string& private_message_support_decay_group =
        SPDM_private_message_support_decay_group(session_data->get_spdm_ctx());
  bool enable_explore_discount_ratio =
      SPDM_enable_explore_discount_ratio(session_data->get_spdm_ctx());
  std::string outer_medical_explore_exp_name =
      SPDM_outer_medical_explore_exp_name(session_data->get_spdm_ctx());
  const auto& user_layered_tag_scores = session_data->get_local_life_user_layered_tags();
  auto explore_match_conf = RankKconfUtil::OuterMedicalExploreMatchConf();

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;

    if (!private_message_price_discount_conf) {
      return ratio;
    }
    // 优先级 : account > ocpx
    const std::string& ocpx_action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    // 统一打折系数配置分组设置
    if (private_message_support_decay_group != "") {
      auto iter_ocpx_exp =
          private_message_price_discount_conf->data().ocpx_exp().find(private_message_support_decay_group);
      if (iter_ocpx_exp != private_message_price_discount_conf->data().ocpx_exp().end()) {
        auto iter_ocpx_group = iter_ocpx_exp->second.ocpx().find(ocpx_action_type);
        if (iter_ocpx_group != iter_ocpx_exp->second.ocpx().end()) {
          ratio = iter_ocpx_group->second.price_discount();
        }
      }
    }
    auto maa_cost_level_90d = p_ad->Attr(
        ItemIdx::fd_ACCOUNT_maa_cost_level_90d).GetIntValue(p_ad->AttrIndex()).value_or(0);
    if (maa_cost_level_90d != 0) {
      std::string maa_cost_level_90d_key = "C" + std::to_string(maa_cost_level_90d);
      auto iter_ocpx_exp_maa =
          private_message_price_discount_conf->data().ocpx_exp().find(maa_cost_level_90d_key);
      if (iter_ocpx_exp_maa != private_message_price_discount_conf->data().ocpx_exp().end()) {
        auto iter_ocpx_group_maa = iter_ocpx_exp_maa->second.ocpx().find(ocpx_action_type);
        if (iter_ocpx_group_maa != iter_ocpx_exp_maa->second.ocpx().end()) {
          ratio = iter_ocpx_group_maa->second.price_discount();
        }
      }
    }
    std::string decay_group = "MCB";
    auto iter_ocpx_exp_mcb =
            private_message_price_discount_conf->data().ocpx_exp().find(decay_group);
    if (iter_ocpx_exp_mcb != private_message_price_discount_conf->data().ocpx_exp().end()) {
      auto iter_ocpx_group_mcb = iter_ocpx_exp_mcb->second.ocpx().find(ocpx_action_type);
      if (iter_ocpx_group_mcb != iter_ocpx_exp_mcb->second.ocpx().end() &&
                                        p_ad->get_bid_type() == kuaishou::ad::AdEnum::MCB) {
        ratio = iter_ocpx_group_mcb->second.price_discount();
      }
    }
    auto iter_account = private_message_price_discount_conf->data().account().find(p_ad->get_account_id());
    if (iter_account != private_message_price_discount_conf->data().account().end()) {
      ratio = iter_account->second.price_discount();
    }
    if (enable_explore_discount_ratio &&
        !user_layered_tag_scores.empty() &&  // 准入意向人群
        p_ad->get_first_industry_id_v5() == 1017 &&  // 准入医疗行业
        explore_match_conf != nullptr &&
        explore_match_conf->data().enable()) {
      std::string product_name = "outer_medical";
      const auto& product_configs = explore_match_conf->data().product_configs();
      auto product_configs_iter = product_configs.find(product_name);
      if (product_configs_iter != product_configs.end()) {
        const auto& product_config = product_configs_iter->second;
        const auto& exp_list = product_config.exp_list();
        auto exp_list_iter = exp_list.find(outer_medical_explore_exp_name);
        if (exp_list_iter != exp_list.end()) {
          const auto& exp_config = exp_list_iter->second;
          bool enable_exp_explore_discount_ratio =
              exp_config.enable_exp_explore_discount_ratio();
          double exp_explore_discount_ratio =
              exp_config.exp_explore_discount_ratio();
          int64_t max_ecpc_tag =
              p_ad->Attr(ItemIdx::max_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
          double max_ecpc_ratio =
              p_ad->Attr(ItemIdx::max_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
          if (enable_exp_explore_discount_ratio && max_ecpc_tag == 403) {
            session_data->dot_perf->Interval(static_cast<int64>(ratio * 1000),
                                             "medical_explore_discount_calc", "raw_discount_ratio",
                                             outer_medical_explore_exp_name);
            ratio *= exp_explore_discount_ratio;
            session_data->dot_perf->Interval(static_cast<int64>(ratio * 1000),
                                             "medical_explore_discount_calc", "final_discount_ratio",
                                             outer_medical_explore_exp_name);
            session_data->dot_perf->Interval(static_cast<int64>(max_ecpc_ratio * 1000),
                                             "medical_explore_discount_calc", "max_ecpc_ratio",
                                             outer_medical_explore_exp_name);
          }
        }
      }
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceSimplePromotion_Admit(ks::platform::AddibleRecoContextInterface* context,
                                     platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_adjust_price_simple_promotion =
      SPDM_enable_adjust_price_simple_promotion(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    if (!enable_adjust_price_simple_promotion) {
      return false;
    }
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 出价类型准入
    if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_PRODUCT_SIMPLE_PROMOTION) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceSimplePromotion_Compute(ks::platform::AddibleRecoContextInterface* context,
                                       ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  // 统一打折系数配置
  auto simple_promotion_price_discount_conf =
      engine_base::AdKconfUtil::simplePromotionPriceDiscountConf();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    if (!simple_promotion_price_discount_conf) {
      return ratio;
    }
    // 优先级 : account > ocpx
    const std::string& ocpx_action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    // 统一打折系数配置分组设置
    const std::string& simple_promotion_support_decay_group =
        SPDM_simple_promotion_support_decay_group(session_data->get_spdm_ctx());
    if (simple_promotion_support_decay_group != "") {
      auto iter_ocpx_exp =
          simple_promotion_price_discount_conf->data().ocpx_exp().find(simple_promotion_support_decay_group);
      if (iter_ocpx_exp != simple_promotion_price_discount_conf->data().ocpx_exp().end()) {
        auto iter_ocpx_group = iter_ocpx_exp->second.ocpx().find(ocpx_action_type);
        if (iter_ocpx_group != iter_ocpx_exp->second.ocpx().end()) {
          ratio = iter_ocpx_group->second.price_discount();
        }
      }
    }
    auto iter_account = simple_promotion_price_discount_conf->data().account().find(p_ad->get_account_id());
    if (iter_account != simple_promotion_price_discount_conf->data().account().end()) {
      ratio = iter_account->second.price_discount();
    }
    if (ratio != 1.0) {
      RANK_DOT_COUNT(session_data, 1, "AdjustPriceSimplePromotion", ocpx_action_type);
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void OuterLivePriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
  platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_outer_live_price_discount =
  SPDM_enable_outer_live_price_discount(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    if (!enable_outer_live_price_discount) {
      return false;
    }
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (p_ad->Is(AdFlag::is_outer_loop_ad) &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void OuterLivePriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  // 统一打折系数配置
  const OuterLivePriceDiscountConfig& outer_live_price_discount_config
    = RankKconfUtil::outerLivePriceDiscountConfig()->data();
  bool enable_outer_live_price_discount_add_resourcetype =
  SPDM_enable_outer_live_price_discount_add_resourcetype(session_data->get_spdm_ctx());

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    std::string itemtype = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
    std::string corporation = std::string(
      p_ad->Attr(ItemIdx::fd_ACCOUNT_corporation_name).GetStringValue(ad.AttrIndex()).value_or(""));
    std::string product = p_ad->get_product_name();
    std::string account = absl::StrCat(p_ad->get_account_id());
    std::string corporation_product = absl::StrCat(corporation, "_", product);
    std::string account_itemtype = absl::StrCat(account, "_", itemtype);

    double ratio = 1.0;
    if (outer_live_price_discount_config.default_ratio() > 0) {
      ratio = outer_live_price_discount_config.default_ratio();
    }

    auto iter_corporation_product =
     outer_live_price_discount_config.corporation_product_ratio().find(corporation_product);
    if (iter_corporation_product != outer_live_price_discount_config.corporation_product_ratio().end() &&
             0 < iter_corporation_product->second) {
        ratio = iter_corporation_product->second;
    }
    auto iter_account =
    outer_live_price_discount_config.account_ratio().find(account);
    if (iter_account != outer_live_price_discount_config.account_ratio().end() &&
              0 < iter_account->second) {
        ratio = iter_account->second;
    }
    auto iter_account_itemtype =
     outer_live_price_discount_config.account_itemtype_ratio().find(account_itemtype);
    if (iter_account_itemtype != outer_live_price_discount_config.account_itemtype_ratio().end() &&
             0 < iter_account_itemtype->second) {
        ratio = iter_account_itemtype->second;
    }

    if (enable_outer_live_price_discount_add_resourcetype) {
      std::vector<std::string> resourcetype_list;
      resourcetype_list.reserve(9);
      if (p_ad->get_resource_type().is_opt) {
        resourcetype_list.push_back("opt");
      }
      if (p_ad->get_resource_type().is_feed) {
        resourcetype_list.push_back("feed");
      }
      if (p_ad->get_resource_type().is_detail) {
        resourcetype_list.push_back("detail");
      }
      if (p_ad->get_resource_type().is_universe) {
        resourcetype_list.push_back("universe");
      }
      if (p_ad->get_resource_type().is_cover_feed) {
        resourcetype_list.push_back("cover_feed");
      }
      if (p_ad->get_resource_type().is_thanos) {
        resourcetype_list.push_back("thanos");
      }
      if (p_ad->get_resource_type().is_rewarded) {
        resourcetype_list.push_back("rewarded");
      }
      if (p_ad->get_resource_type().is_universe_opt) {
        resourcetype_list.push_back("universe_opt");
      }
      if (p_ad->get_resource_type().is_inner_rewarded) {
        resourcetype_list.push_back("inner_rewarded");
      }
      for (auto& resourcetype : resourcetype_list) {
        std::string corporation_product_resourcetype =
                    absl::StrCat(corporation_product, "_", resourcetype);
        std::string account_resourcetype = absl::StrCat(account, "_", resourcetype);
        std::string account_itemtype_resourcetype =
                    absl::StrCat(account_itemtype, "_", resourcetype);
        auto iter_corporation_product_resourcetype =
        outer_live_price_discount_config.corporation_product_resourcetype_ratio().find(
          corporation_product_resourcetype);
        if (iter_corporation_product_resourcetype !=
            outer_live_price_discount_config.corporation_product_resourcetype_ratio().end() &&
                0 < iter_corporation_product_resourcetype->second) {
            ratio = iter_corporation_product_resourcetype->second;
        }
        auto iter_account_resourcetype =
        outer_live_price_discount_config.account_resourcetype_ratio().find(account_resourcetype);
        if (iter_account_resourcetype !=
            outer_live_price_discount_config.account_resourcetype_ratio().end() &&
                  0 < iter_account_resourcetype->second) {
            ratio = iter_account_resourcetype->second;
        }
        auto iter_account_itemtype_resourcetype =
        outer_live_price_discount_config.account_itemtype_resourcetype_ratio().find(
                   account_itemtype_resourcetype);
        if (iter_account_itemtype_resourcetype !=
            outer_live_price_discount_config.account_itemtype_resourcetype_ratio().end() &&
                0 < iter_account_itemtype_resourcetype->second) {
            ratio = iter_account_itemtype_resourcetype->second;
        }
      }
    }

    if (ratio != 1.0) {
      RANK_DOT_COUNT(session_data, 1, "OuterLivePriceDiscount", account);
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void InnerNewOrderDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
  platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_inner_new_order_discount =
  SPDM_enable_inner_new_order_discount(session_data->get_spdm_ctx());
  bool enable_inner_new_order_discount_tail_exp =
  SPDM_enable_inner_new_order_discount_tail_exp(session_data->get_spdm_ctx());
  auto inner_new_order_discount_tail_exp_group_v2 =
      SPDM_inner_new_order_discount_tail_exp_group_v2(session_data->get_spdm_ctx());
  bool enable_inner_new_order_discount_tail_exp_v2 =
  SPDM_enable_inner_new_order_discount_tail_exp_v2(session_data->get_spdm_ctx());
  bool enable_inner_new_order_discount_fix =
  SPDM_enable_inner_new_order_discount_fix(session_data->get_spdm_ctx());
  bool enable_inner_new_order_discount_info_collect =
  SPDM_enable_inner_new_order_discount_info_collect(session_data->get_spdm_ctx());
  bool enable_smb_maa_holdout =
      SPDM_enable_smb_maa_holdout(session_data->get_spdm_ctx());
  bool enable_newcus_kconf_time_admit =
  SPDM_enable_newcus_kconf_time_admit(session_data->get_spdm_ctx());
  auto enable_inner_new_order_discount_flex_upper =
      SPDM_enable_inner_new_order_discount_flex_upper(session_data->get_spdm_ctx());
  auto enable_inner_new_order_discount_add_p2l =
      SPDM_enable_inner_new_order_discount_add_p2l(session_data->get_spdm_ctx());
  auto enable_smb_newcus_dazhe_newscope =
      SPDM_enable_smb_newcus_dazhe_newscope(session_data->get_spdm_ctx());
  const auto newcus_author_id_set = RankKconfUtil::smbNewcusAuthorid();
  const auto& discount_config_smb = RankKconfUtil::innerSmbDiscountKconf();
  const auto& new_cus_author_id_tail = RankKconfUtil::newCusAuthorIDTailAdmit();

  auto admit = [&] (auto& item) -> bool {
    if (!enable_inner_new_order_discount) {
      return false;
    }
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (enable_smb_newcus_dazhe_newscope) {
      bool is_admit_base = false;
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) {
        is_admit_base = true;
      } else {
        is_admit_base = false;
      }
      // 准入逻辑 退出逻辑
      int32_t is_92d_new_cur =
            p_ad->Attr(ItemIdx::fd_AUTHOR_is_92d_new_cur)
            .GetIntValue(p_ad->AttrIndex()).value_or(0);
      int32_t is_xd_cost_cur =
            p_ad->Attr(ItemIdx::fd_AUTHOR_is_xd_cost_cur)
            .GetIntValue(p_ad->AttrIndex()).value_or(0);
      int64 author_7d_gmv =
            p_ad->Attr(ItemIdx::fd_AUTHOR_gmv_7d)
            .GetIntValue(p_ad->AttrIndex()).value_or(-1);
      auto author_gmv_thres = 500;
      auto it_thres = discount_config_smb->find(absl::StrCat("author_gmv_thres"));
      if (it_thres != discount_config_smb->end()) {
        author_gmv_thres = it_thres->second;
      }
      if ((is_92d_new_cur || is_xd_cost_cur) && author_7d_gmv <= author_gmv_thres && is_admit_base
      && new_cus_author_id_tail && new_cus_author_id_tail->IsOnFor(p_ad->get_author_id())) {
        return true;
      }
    } else {
      // 深转生效
      bool is_admit_base = true;
      if (enable_inner_new_order_discount_info_collect) {
        if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          is_admit_base = false;
        }
        if (enable_inner_new_order_discount_fix) {
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
              is_admit_base = false;
            }
        }
        if (enable_inner_new_order_discount_tail_exp) {
          if (inner_new_order_discount_tail_exp_group_v2 == "base") {
            is_admit_base = false;
          }
        }
        if (enable_inner_new_order_discount_tail_exp_v2) {
          auto tail_number = p_ad->get_author_id() % 1000 / 100;
          if (tail_number < 5) {
            is_admit_base = false;
          }
        }
      } else {
        if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        if (enable_inner_new_order_discount_fix) {
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
              return false;
            }
        }
        if (enable_inner_new_order_discount_tail_exp) {
          if (inner_new_order_discount_tail_exp_group_v2 == "base") {
            return false;
          }
        }
        if (enable_inner_new_order_discount_tail_exp_v2) {
          auto tail_number = p_ad->get_author_id() % 1000 / 100;
          if (tail_number < 5) {
            return false;
          }
        }
      }

      bool is_admit_not_holdout = true;
      if (enable_smb_maa_holdout) {
        auto holdout_lower = 90;
        auto holdout_upper = 99;
        auto it_holdout = discount_config_smb->find(absl::StrCat("holdout_upper"));
        if (it_holdout != discount_config_smb->end()) {
          holdout_upper = it_holdout->second;
        }
        it_holdout = discount_config_smb->find(absl::StrCat("holdout_lower"));
        if (it_holdout != discount_config_smb->end()) {
          holdout_lower = it_holdout->second;
        }
        auto tail_number = p_ad->get_author_id() % 100000 / 1000;
        if (tail_number <= holdout_upper && tail_number >= holdout_lower) {
          is_admit_not_holdout = false;
        }
      }

      bool is_admit_time = false;
      bool is_admit_time_v2 = false;
      bool is_admit_discount_upper = false;
      bool is_admit_conv = false;

      int64 author_7d_fct = p_ad->Attr(ItemIdx::fd_AUTHOR_fcd).GetIntValue(p_ad->AttrIndex()).value_or(0);
      int64 real_first_cost_time = author_7d_fct;
      bool is_today_new_cus = false;
      if (real_first_cost_time >= 4072142475) {
        real_first_cost_time = -1;
        RANK_DOT_COUNT(session_data, 1,
                          "ad_rank.lcctest_max_newcus");
      } else if (real_first_cost_time == 0) {
        is_today_new_cus = true;
        RANK_DOT_COUNT(session_data, 1,
                          "ad_rank.lcctest_miss_newcus");
      }
      int64 now_time = session_data->get_current_timestamp_nodiff() / 1000;
      int64 discount_days = 2;
      if (real_first_cost_time * 1000 >= now_time - discount_days * 86400 * 1000 || is_today_new_cus) {
        is_admit_time = true;
        RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.lcctest_timeok_newcusall");
      }
      // debug 用
      if (real_first_cost_time * 1000 >= now_time - discount_days * 86400 * 1000) {
        RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.lcctest_timeok_newcus1");
      }
      if (is_today_new_cus) {
        RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.lcctest_timeok_newcus2");
      }
      if (enable_newcus_kconf_time_admit) {
        // newcus 白名单准入
        if (newcus_author_id_set != nullptr && newcus_author_id_set->count(p_ad->get_author_id()) > 0) {
          is_admit_time_v2 = true;
          RANK_DOT_COUNT(session_data, 1,
              "ad_rank.lcctest_timeok_newcus3");
        }
      }

      // 行业 x 消耗
      auto first_industry_id = p_ad->get_first_industry_id_v5();
      double today_discount_ratio =
            p_ad->Attr(ItemIdx::fd_AUTHOR_author_today_discount_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
      int64_t today_cost =
            p_ad->Attr(ItemIdx::fd_AUTHOR_author_today_cost).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT 
      // 转化
      int64_t today_conv =
            p_ad->Attr(ItemIdx::fd_AUTHOR_author_today_target_cost).GetIntValue(p_ad->AttrIndex()).value_or(-1);  // NOLINT
      today_cost = today_cost / 1000;
      double original_cost = today_cost / std::clamp(today_discount_ratio, 0.2, 2.0);
      double discount_amount = original_cost - today_cost;

      if (enable_inner_new_order_discount_flex_upper) {
        auto discount_upper = 50;
        auto it_discount_upper = discount_config_smb->find(absl::StrCat("discount_upper"));
        if (it_discount_upper != discount_config_smb->end()) {
          discount_upper = it_discount_upper->second;
        }
        if (discount_amount < discount_upper) {
          is_admit_discount_upper = true;
        }
      } else {
        if (discount_amount < 50) {
          is_admit_discount_upper = true;
        }
      }
      int64 cost_level = -1;
      if (today_cost < 50) {
        cost_level = 1;
      } else if (today_cost < 100) {
        cost_level = 2;
      } else if (today_cost < 150) {
        cost_level = 3;
      } else if (today_cost < 200) {
        cost_level = 4;
      }

      int64 targetconv = 1;
      auto prefix = "live";
      if (p_ad->Is(AdFlag::is_live_ad)) {
        prefix = "live";
      } else {
        prefix = "photo";
      }

      if (enable_inner_new_order_discount_add_p2l) {
        if (p_ad->Is(AdFlag::is_p2l_ad)) {
          prefix = "live";
        }
      }

      auto it_discount = discount_config_smb
        ->find(absl::StrCat(prefix, "_targetconv_", first_industry_id, "_", cost_level));
      if (it_discount != discount_config_smb->end()) {
        targetconv = it_discount->second;
        RANK_DOT_COUNT(session_data, 1,
                          absl::StrCat("ad_rank.lcctest_look_kconf_newcus_",
                          "targetconv_", first_industry_id, "_", cost_level));
      }
      if (today_conv >= 0 && today_conv < targetconv) {
        is_admit_conv = true;
      }
      bool is_admit_conv_v2 = false;
      if (enable_inner_new_order_discount_fix) {
        if (today_conv < targetconv) {
          is_admit_conv_v2 = true;
        }
      }

      if (is_admit_time) {
        RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.lcctest_timeok");
      }

      if (is_admit_discount_upper) {
        RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.lcctest_discountok");
      }
      if (is_admit_conv) {
        RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.lcctest_convok");
      }
      if (enable_inner_new_order_discount_info_collect) {
        p_ad->set_new_author_today_cost(today_cost);
        p_ad->set_new_author_today_conv(today_conv);
        p_ad->set_new_author_today_discount_ratio(today_discount_ratio);
        p_ad->set_new_author_today_is_achieve(is_admit_conv_v2);
      }

      if (enable_inner_new_order_discount_fix) {
        if (enable_inner_new_order_discount_info_collect) {
          if (enable_newcus_kconf_time_admit) {
            if (enable_smb_maa_holdout) {
              if (is_admit_base && is_admit_time_v2 && is_admit_discount_upper
                && is_admit_conv_v2 && is_admit_not_holdout) {
                RANK_DOT_COUNT(session_data, 1,
                                  "ad_rank.lcctest_allok2_not_hold");
                return true;
              }
            } else {
              if (is_admit_base && is_admit_time_v2 && is_admit_discount_upper && is_admit_conv_v2) {
                RANK_DOT_COUNT(session_data, 1,
                                  "ad_rank.lcctest_allok2");
                return true;
              }
            }
          } else {
            if (is_admit_base && is_admit_time && is_admit_discount_upper && is_admit_conv_v2) {
              RANK_DOT_COUNT(session_data, 1,
                                "ad_rank.lcctest_allok2");
              return true;
            }
          }
        } else {
          if (is_admit_time && is_admit_discount_upper && is_admit_conv_v2) {
            RANK_DOT_COUNT(session_data, 1,
                              "ad_rank.lcctest_allok2");
            return true;
          }
        }

      } else {
        if (is_admit_time && is_admit_discount_upper && is_admit_conv) {
          RANK_DOT_COUNT(session_data, 1,
                            "ad_rank.lcctest_allok");
          return true;
        }
      }
    }



    return false;
  };
  FINISH_ADMIT()
}

void InnerNewOrderDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()

  bool enable_inner_new_order_discount_stage_ratio =
  SPDM_enable_inner_new_order_discount_stage_ratio(session_data->get_spdm_ctx());
  const auto& discount_config_smb = RankKconfUtil::innerSmbDiscountKconf();

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    double ratio = 1.0;
    if (enable_inner_new_order_discount_stage_ratio) {
      double today_discount_ratio =
            p_ad->Attr(ItemIdx::fd_AUTHOR_author_today_discount_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
      int64_t today_cost =
            p_ad->Attr(ItemIdx::fd_AUTHOR_author_today_cost).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT 
      today_cost = today_cost / 1000;
      double original_cost = today_cost / std::clamp(today_discount_ratio, 0.2, 2.0);
      double discount_amount = original_cost - today_cost;

      double dicount_stage1_upper = 5;
      double dicount_stage2_upper = 20;
      double dicount_stage3_upper = 50;

      double dicount_stage1_ratio = 0.9;
      double dicount_stage2_ratio = 0.8;
      double dicount_stage3_ratio = 0.5;

      auto it_discount = discount_config_smb->find("dicount_stage1_upper");
      if (it_discount != discount_config_smb->end()) {
        dicount_stage1_upper = it_discount->second;
      }
      it_discount = discount_config_smb->find("dicount_stage2_upper");
      if (it_discount != discount_config_smb->end()) {
        dicount_stage2_upper = it_discount->second;
      }
      it_discount = discount_config_smb->find("dicount_stage3_upper");
      if (it_discount != discount_config_smb->end()) {
        dicount_stage3_upper = it_discount->second;
      }

      it_discount = discount_config_smb->find("dicount_stage1_ratio");
      if (it_discount != discount_config_smb->end()) {
        dicount_stage1_ratio = it_discount->second;
      }
      it_discount = discount_config_smb->find("dicount_stage2_ratio");
      if (it_discount != discount_config_smb->end()) {
        dicount_stage2_ratio = it_discount->second;
      }
      it_discount = discount_config_smb->find("dicount_stage3_ratio");
      if (it_discount != discount_config_smb->end()) {
        dicount_stage3_ratio = it_discount->second;
      }
      if (discount_amount < dicount_stage1_upper) {
        ratio = dicount_stage1_ratio;
        RANK_DOT_COUNT(session_data, 1, "ad_rank.lcctest_stage1");
      } else if (discount_amount < dicount_stage2_upper) {
        ratio = dicount_stage2_ratio;
        RANK_DOT_COUNT(session_data, 1, "ad_rank.lcctest_stage2");
      } else if (discount_amount < dicount_stage3_upper) {
        ratio = dicount_stage3_ratio;
        RANK_DOT_COUNT(session_data, 1, "ad_rank.lcctest_stage3");
      } else {
        // 这里不应该准入
        RANK_DOT_COUNT(session_data, 1, "ad_rank.lcctest_stage4");
      }
    } else {
      auto it_discount = discount_config_smb->find("default_ratio");
      auto it_discount_2 = discount_config_smb->find("default_ratio_2");
      const std::unordered_set<int64_t> target_page_ids = {100011056, 100011251, 100011344, 100011347,
        100012065, 100012068, 11101};
      const auto& new_cus_author_id_tail_2 = RankKconfUtil::newCusAuthorIDTailAdmit_2();
      auto buyer_effective_type =
          session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
      auto daily_new_smb_newcus_discount_ratio =
        SPDM_daily_new_smb_newcus_discount_ratio(session_data->get_spdm_ctx());
      auto daily_new_smb_newcus_discount_ratio_2 =
        SPDM_daily_new_smb_newcus_discount_ratio_2(session_data->get_spdm_ctx());
      auto smb_exp_tag = SPDM_smb_exp_tag(session_data->get_spdm_ctx());
      auto enable_smb_adj_by_user_level =
        SPDM_enable_smb_adj_by_user_level(session_data->get_spdm_ctx());
      auto disable_jili_page_discount = SPDM_disable_jili_page_discount(session_data->get_spdm_ctx());
      int32_t is_92d_new_cur =
              p_ad->Attr(ItemIdx::fd_AUTHOR_is_92d_new_cur).GetIntValue(p_ad->AttrIndex()).value_or(0);
      if (it_discount != discount_config_smb->end()) {
        ratio = it_discount->second;
      }
      if (is_92d_new_cur) {
        ratio = daily_new_smb_newcus_discount_ratio;
      }
      if (enable_smb_adj_by_user_level) {
        auto kconf_tag = absl::StrCat(smb_exp_tag, "_", buyer_effective_type, "_discount_1");
        auto iter_discount = discount_config_smb->find(kconf_tag);
        if (iter_discount != discount_config_smb->end()) {
          ratio *= iter_discount->second;
        }
      }
      if (new_cus_author_id_tail_2 && new_cus_author_id_tail_2->IsOnFor(p_ad->get_author_id())) {
        if (it_discount_2 != discount_config_smb->end()) {
          ratio = it_discount_2->second;
        }
        if (is_92d_new_cur) {
          ratio = daily_new_smb_newcus_discount_ratio_2;
        }
        if (enable_smb_adj_by_user_level) {
          auto kconf_tag2 = absl::StrCat(smb_exp_tag, "_", buyer_effective_type, "_discount_2");
          auto iter_discount2 = discount_config_smb->find(kconf_tag2);
          if (iter_discount2 != discount_config_smb->end()) {
            ratio *= iter_discount2->second;
          }
        }
      }
      if (disable_jili_page_discount) {
        if (target_page_ids.find(session_data->get_page_id()) != target_page_ids.end()) {
          ratio = 1.0;
        }
      }
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void InnerNewGuardN_Admit(ks::platform::AddibleRecoContextInterface* context,
              platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_inner_new_guard_n_ecpc =
    SPDM_enable_inner_new_guard_n_ecpc(session_data->get_spdm_ctx());
  int conversion_cnt_threshold =
    SPDM_inner_new_guard_n_ecpc_conversion_cnt_threshold(session_data->get_spdm_ctx());

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    auto campaign_conversion_cnt =
    p_ad->Attr(ItemIdx::fd_CAMPAIGN_order_newcus_benefit_conversion_cnt).GetIntValue(p_ad->AttrIndex()).value_or(-1);  // NOLINT
    if (!enable_inner_new_guard_n_ecpc) {
      return false;
    }
    if (campaign_conversion_cnt >= 0 && campaign_conversion_cnt < conversion_cnt_threshold) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void InnerNewGuardN_Compute(ks::platform::AddibleRecoContextInterface* context,
  ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double default_discount_ratio =
    SPDM_inner_new_guard_n_discount_ratio(session_data->get_spdm_ctx());
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    return default_discount_ratio;
  };
  FINISH_COMPUTE()
}

void InnerHighQualityPrice_Admit(ks::platform::AddibleRecoContextInterface* context,
              platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_inner_high_quality_discount =
    SPDM_enable_inner_high_quality_discount(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (!enable_inner_high_quality_discount) {
      return false;
    }
    if (p_ad->Is(AdFlag::is_inner_loop_ad)) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void InnerHighQualityPrice_Compute(ks::platform::AddibleRecoContextInterface* context,
  ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double inner_high_quality_discount_ratio =
    SPDM_inner_high_quality_discount_ratio(session_data->get_spdm_ctx());
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    auto is_inner_high_quality_ecpc = p_ad->Attr(ItemIdx::is_inner_high_quality_ecpc).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    if (is_inner_high_quality_ecpc > 0) {
      return inner_high_quality_discount_ratio;
    }
    return 1.0;
  };
  FINISH_COMPUTE()
}

void InnerCtcvrCaliPrice_Admit(ks::platform::AddibleRecoContextInterface* context,
              platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_inner_ctcvr_cali =
    SPDM_enable_inner_ctcvr_cali(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (!enable_inner_ctcvr_cali) {
      return false;
    }
    if (p_ad->Is(AdFlag::is_inner_loop_ad)) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void InnerCtcvrCaliPrice_Compute(ks::platform::AddibleRecoContextInterface* context,
  ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double is_inner_industry_ctcvr_cali_ratio =
    SPDM_is_inner_industry_ctcvr_cali_ratio(session_data->get_spdm_ctx());
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    auto is_inner_industry_ctcvr_cali_ecpc = p_ad->Attr(ItemIdx::is_inner_industry_ctcvr_cali_ecpc).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    if (is_inner_industry_ctcvr_cali_ecpc > 0) {
      return is_inner_industry_ctcvr_cali_ratio;
    }
    return 1.0;
  };
  FINISH_COMPUTE()
}


void AdjustIncentivePrice_Admit(ks::platform::AddibleRecoContextInterface* context,
                                   platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable = session_data->get_is_incentive();
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    return enable;
  };
  FINISH_ADMIT()
}

void AdjustIncentivePrice_Compute(ks::platform::AddibleRecoContextInterface* context,
                                     ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_inspire_thanos_sctr_price_discount =
      !SPDM_enable_inspire_thanos_sctr_price_discount_new(session_data->get_spdm_ctx());
  const auto& page_sctr_discount_map = engine_base::AdKconfUtil::incentivePageSctrPriceDiscountMap();
  const auto& sub_page_sctr_discount_map = engine_base::AdKconfUtil::incentiveSubPageSctrPriceDiscountMap();
  double thanos_default_ratio = engine_base::AdKconfUtil::incentiveThanosDefaultSctrPriceDiscount();
  const auto& draw_flow_price_ratio_config = RankKconfUtil::drawFlowPriceRatioConfig();
  double draw_flow_price_ratio = 1;
  if (SPDM_enable_draw_flow_price_ratio(session_data->get_spdm_ctx()) && draw_flow_price_ratio_config &&
      draw_flow_price_ratio_config->find(absl::StrCat(session_data->get_sub_page_id())) !=
      draw_flow_price_ratio_config->end()) {
    draw_flow_price_ratio =
      draw_flow_price_ratio_config->find(absl::StrCat(session_data->get_sub_page_id()))->second;
  }
  bool enable_mv_draw_flow_price_ratio = SPDM_enable_mv_draw_flow_price_ratio(session_data->get_spdm_ctx());
  bool enable_conv_d_i_coin_coef_for_specific_customer =
    SPDM_enable_conv_d_i_coin_coef_for_specific_customer(session_data->get_spdm_ctx());

  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    if (session_data->get_is_rewarded() &&
        !session_data->get_pos_manager_base().IsInspireMerchant() &&
        !RankKconfUtil::incentiveSctrPredSupPages()->count(session_data->get_sub_page_id()) &&
        session_data->get_is_thanos_request() && enable_inspire_thanos_sctr_price_discount) {
      ratio = thanos_default_ratio;
      auto iter = page_sctr_discount_map->find(absl::StrCat(session_data->get_page_id()));
      if (iter != page_sctr_discount_map->end()) {
        ratio = iter->second;
      }
      iter = page_sctr_discount_map->find(absl::StrCat(session_data->get_page_id(), "_",
        p_ad->get_ocpx_action_type()));
      if (iter != page_sctr_discount_map->end()) {
        ratio = iter->second;
      }
      auto sub_iter = sub_page_sctr_discount_map->find(absl::StrCat(session_data->get_sub_page_id()));
      if (sub_iter != sub_page_sctr_discount_map->end()) {
        ratio = sub_iter->second;
      }
      sub_iter = sub_page_sctr_discount_map->find(absl::StrCat(session_data->get_sub_page_id(), "_",
        p_ad->get_ocpx_action_type()));
      if (sub_iter != sub_page_sctr_discount_map->end()) {
        ratio = sub_iter->second;
      }
      RANK_DOT_STATS(session_data, ratio * 1000, "AdjustIncentivePrice.sctr_discount_ratio",
          kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
      if (!enable_mv_draw_flow_price_ratio) {
        ratio *= draw_flow_price_ratio;
      }
    }
    if (enable_mv_draw_flow_price_ratio) {
      ratio *= draw_flow_price_ratio;
    }
    if (enable_conv_d_i_coin_coef_for_specific_customer && p_ad->get_price() > 0) {
      double origin_price = p_ad->get_price();
      double additional_price = p_ad->get_incentive_additional_price();
      double additional_price_ratio = (origin_price + additional_price) / origin_price;
      ratio *= additional_price_ratio;
      RANK_DOT_STATS(session_data, additional_price_ratio * 1000,
        "AdjustIncentivePrice.additional_price_ratio",
        kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()),
        p_ad->get_product_name());
    }
    RANK_DOT_STATS(session_data, ratio * 1000, "AdjustIncentivePrice.ratio",
        kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustExplorePrice_Admit(ks::platform::AddibleRecoContextInterface* context,
                                   platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable = SPDM_enable_adjust_price_explore(session_data->get_spdm_ctx()) &&
             (session_data->IsThanosMixTraffic() ||
              session_data->get_pos_manager_base().IsInnerExplore() ||
              session_data->get_sub_page_id() != 10002001);
  bool enableThanosMixTraffic = SPDM_enable_adjust_price_ThanosMix(session_data->get_spdm_ctx());
  if (enableThanosMixTraffic) {
    enable = session_data->IsThanosMixTraffic();
  }
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    return enable;
  };
  FINISH_ADMIT()
}

void AdjustExplorePrice_Compute(ks::platform::AddibleRecoContextInterface* context,
                                     ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double hard_ratio = SPDM_explore_hard_price_ratio_for_refactor(session_data->get_spdm_ctx());
  double soft_ratio = SPDM_explore_soft_price_ratio_for_refactor(session_data->get_spdm_ctx());

  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
        ratio = hard_ratio;
    } else if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
        ratio = soft_ratio;
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceExploreInner_Admit(ks::platform::AddibleRecoContextInterface* context,
                                   platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable = SPDM_enable_adjust_price_explore_inner(session_data->get_spdm_ctx()) &&
      session_data->get_pos_manager_base().IsInnerExplore();
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    return enable && p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP;
  };
  FINISH_ADMIT()
}

void AdjustPriceExploreInner_Compute(ks::platform::AddibleRecoContextInterface* context,
                                     ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double hard_photo = SPDM_gamora_model_sctr_price_correct_weight(session_data->get_spdm_ctx());
  double hard_live = SPDM_mix_dsp_live_price_correct_ratio_gamora(session_data->get_spdm_ctx());
  double soft_photo = SPDM_gamora_mix_softad_reprice_weight(session_data->get_spdm_ctx());
  double soft_live = SPDM_gamora_mix_soft_live_price_correct_ratio(session_data->get_spdm_ctx());
  // 放大缩小上述因子到 1 之间的距离，>=0
  double rel_ratio = SPDM_adjust_price_explore_inner_rel_ratio(session_data->get_spdm_ctx());
  hard_photo = (hard_photo - 1) * rel_ratio + 1;
  hard_live = (hard_live - 1) * rel_ratio + 1;
  soft_photo = (soft_photo - 1) * rel_ratio + 1;
  soft_live = (soft_live - 1) * rel_ratio + 1;

  // 打折系数，和上述独立
  double abs_ratio = SPDM_adjust_price_explore_inner_abs_ratio(session_data->get_spdm_ctx());

  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = abs_ratio;
    if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
      if (p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
        ratio *= hard_live;
      } else {
        ratio *= hard_photo;
      }
    } else if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
      if (p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
        ratio *= soft_live;
      } else {
        ratio *= soft_photo;
      }
    }
    return ratio;
  };
  FINISH_COMPUTE()
}
void AdjustFanstopFollowPrice_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_fanstop_follow_price_tail = RankKconfUtil::enableFanstopFollowPriceTail();
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_FANS_TOP_FOLLOW &&
        p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO &&
        p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE &&
        p_ad->get_bid_strategy() == kuaishou::ad::AdEnum_BidStrategy_CUSTOM_BID_STRATEGY) {
      if (enable_fanstop_follow_price_tail &&
          enable_fanstop_follow_price_tail->IsOnFor(p_ad->get_unit_id())) {
        return true;
      }
    }
    return false;
  };
  FINISH_ADMIT()
}
void AdjustFanstopFollowPrice_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto fanstop_follow_price_lower_bound = 1.0;
  auto fanstop_follow_price_upper_bound = 1.0;
  auto fanstop_follow_price_coef = 1.0;
  auto enable_fanstop_follow_bound_tail = RankKconfUtil::fanstopFollowBoundtail();
  auto enable_fanstop_follow_price_tail = RankKconfUtil::enableFanstopFollowPriceTail();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    if (enable_fanstop_follow_price_tail && enable_fanstop_follow_bound_tail &&
        enable_fanstop_follow_price_tail->IsOnFor(p_ad->get_unit_id())) {
      auto unit_tail = ad.get_unit_id() % 100;
      const std::string& lower_bound_key = absl::Substitute("lower_$0", unit_tail);
      const std::string& upper_bound_key = absl::Substitute("upper_$0", unit_tail);
      const std::string& coef_key = absl::Substitute("coef_$0", unit_tail);
      auto iter_low = enable_fanstop_follow_bound_tail->find(lower_bound_key);
      if (iter_low != enable_fanstop_follow_bound_tail->end()) {
        fanstop_follow_price_lower_bound = iter_low->second;
      }
      auto iter_up = enable_fanstop_follow_bound_tail->find(upper_bound_key);
      if (iter_up != enable_fanstop_follow_bound_tail->end()) {
        fanstop_follow_price_upper_bound = iter_up->second;
      }
      auto iter_coef = enable_fanstop_follow_bound_tail->find(coef_key);
      if (iter_coef != enable_fanstop_follow_bound_tail->end()) {
        fanstop_follow_price_coef = iter_coef->second;
      }
    }
    ad.set_record_gsp_price(ad.get_price());
    // 计费系数
    double ratio = 1.0;
    ratio = ad.get_price_ratio() * fanstop_follow_price_coef;
    ratio = std::max(ratio, fanstop_follow_price_lower_bound);
    ratio = std::min(ratio, fanstop_follow_price_upper_bound);
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceGYL_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (session_data->get_pos_manager_base().IsGuessYouLike()) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceGYL_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto gyl_price_discount_ratio =
      SPDM_gyl_price_discount_ratio(session_data->get_spdm_ctx());
  auto enable_gyl_item_card_price_discount =
       SPDM_enable_gyl_item_card_price_discount(session_data->get_spdm_ctx());
  auto gyl_item_card_price_discount_ratio =
      SPDM_gyl_item_card_price_discount_ratio(session_data->get_spdm_ctx());
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    ad.set_record_gsp_price(ad.get_price());
    // 计费系数
    double ratio = 1.0;
    if (session_data->get_pos_manager_base().IsGuessYouLike() &&
        gyl_price_discount_ratio > 0) {
      ratio = gyl_price_discount_ratio;
      if (enable_gyl_item_card_price_discount &&
          ad.get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD) {
        ratio = gyl_item_card_price_discount_ratio;
      }
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void PriceCidStra_Admit(ks::platform::AddibleRecoContextInterface* context,
                              platform::DataFrame* item_table,
                              const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_cid_corp_default_strategy_exp_ =
      SPDM_enable_cid_corp_default_strategy_exp(session_data->get_spdm_ctx());
  auto enable_cid_bid_boost_roas_exp_ =
      SPDM_enable_cid_bid_boost_roas_exp(session_data->get_spdm_ctx());
  auto disable_cid_price_strategy_exp_ =
      SPDM_disable_cid_price_strategy_exp(session_data->get_spdm_ctx());
  auto enable_cid_use_goods_type_support =
      SPDM_enable_cid_use_goods_type_support(session_data->get_spdm_ctx());
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    auto& ad = *p_ad;
    std::string corporation_name = std::string(
      p_ad->Attr(ItemIdx::fd_ACCOUNT_corporation_name).GetStringValue(p_ad->AttrIndex()).value_or(""));
    if (enable_cid_use_goods_type_support
      || disable_cid_price_strategy_exp_) {
      return false;
    }
    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPM &&
        ad.get_ad_source_type() == kuaishou::ad::AdSourceType::DSP) {
      return false;
    }
    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA &&
        (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
         ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE)) {
      return false;
    }
    if (ad.get_unit_source_type() == kuaishou::ad::AdEnum_UnitSourceType_UNIT_SOURCE_TYPE_FURIOUS
      && ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA) {
      return false;
    }
    if (enable_cid_bid_boost_roas_exp_) {
      if (p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_SUBMIT
        && p_ad->get_ocpx_action_type() != kuaishou::ad::AD_CID_ROAS) {
        return false;
      }
    } else {
      if (p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_SUBMIT) {
        return false;
      }
    }
    if (enable_cid_corp_default_strategy_exp_) {
      return true;
    } else {
      if (p_ad->get_ad_direct_merchant_biz() == kuaishou::ad::AdEnum::MERCH_BIZ_NONE ||
          p_ad->get_ad_direct_merchant_stage() == kuaishou::ad::AdEnum::ACCOUNT_STAGE_NONE) {
        return false;
      }
    }
    return true;
  };
  FINISH_ADMIT()
}

void PriceCidStra_Compute(ks::platform::AddibleRecoContextInterface* context,
                                ks::platform::DataFrame* item_table,
                                const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enhance_ecpm_strategy_default_tag_ =
      SPDM_enhance_ecpm_strategy_default_tag(session_data->get_spdm_ctx());
  auto inspire_video_discount_ratio_lower_ =
      SPDM_inspire_video_discount_ratio_lower(session_data->get_spdm_ctx());
  // 商家投放配置
  auto biz_stage_info_ = engine_base::AdKconfUtil::directMerchantConf();
  auto enable_cid_spu_strategy_exp_ =
      SPDM_enable_cid_spu_strategy_exp(session_data->get_spdm_ctx());
  auto enable_cid_corp_default_strategy_exp_ =
      SPDM_enable_cid_corp_default_strategy_exp(session_data->get_spdm_ctx());
  auto cid_corp_default_price_ratio_ =
      SPDM_cid_corp_default_price_ratio(session_data->get_spdm_ctx());
  auto cid_discount_ratio_lower_ =
      SPDM_cid_discount_ratio_lower(session_data->get_spdm_ctx());
  auto cid_discount_ratio_upper_ =
      SPDM_cid_discount_ratio_upper(session_data->get_spdm_ctx());
  auto enable_cid_global_ratio_exp_ =
      SPDM_enable_cid_global_ratio_exp(session_data->get_spdm_ctx());
  auto enable_cid_spu_restart_strategy_exp_ =
      SPDM_enable_cid_spu_restart_strategy_exp(session_data->get_spdm_ctx());
  auto enable_cid_bid_boost_roas_exp_ = SPDM_enable_cid_bid_boost_roas_exp(session_data->get_spdm_ctx());
  auto enable_cid_quality_strategy_exp_ = SPDM_enable_cid_quality_strategy_exp(session_data->get_spdm_ctx());
  auto cid_quality_strategy_tag_ = SPDM_cid_quality_strategy_tag(session_data->get_spdm_ctx());
  auto cid_spu_strategy_tag_ = SPDM_cid_spu_strategy_tag(session_data->get_spdm_ctx());
  auto enable_cid_ab_global_ratio_exp_ = SPDM_enable_cid_ab_global_ratio_exp(session_data->get_spdm_ctx());
  auto cid_ab_global_price_ratio_ = SPDM_cid_ab_global_price_ratio(session_data->get_spdm_ctx());
  auto cid_price_discount_config_ = RankKconfUtil::cidPriceDiscountConfig();
  auto cid_global_price_ratio = biz_stage_info_->data().cid_global_price_ratio();
  auto enable_cid_account_zk_strategy_ocpx_exp_ =
               SPDM_enable_cid_account_zk_strategy_ocpx_exp(session_data->get_spdm_ctx());
  auto enable_cid_price_record_rewrited_ =
      SPDM_enable_cid_price_record_rewrited(session_data->get_spdm_ctx());
  if (enable_cid_ab_global_ratio_exp_) {
    cid_global_price_ratio = cid_global_price_ratio * cid_ab_global_price_ratio_;
  }
  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double discount_ratio = 1.0;      // 客户维度折扣
    double spu_discount_ratio = 1.0;  // spu 折扣
    double corp_discount_ratio = 1.0;
    int64_t enhance_ecpm_strategy_default_tag = enhance_ecpm_strategy_default_tag_;
    auto key_str = absl::StrCat(kuaishou::ad::AdEnum_AdDirectMerchantBiz_Name(
                                    p_ad->get_ad_direct_merchant_biz()),
                                "#", kuaishou::ad::AdEnum_AdDirectMerchantStage_Name(
                                  p_ad->get_ad_direct_merchant_stage()),
                                "#", enhance_ecpm_strategy_default_tag);
    auto key_str_quality = absl::StrCat(kuaishou::ad::AdEnum_CidQualityLevel_Name(
                                            p_ad->get_cid_quality_level()),
                                        "#", cid_quality_strategy_tag_);
    auto key_str_spu = absl::StrCat(kuaishou::ad::AdEnum_CidSpuType_Name(
                                        p_ad->get_cid_spu_type()),
                                    "#", cid_spu_strategy_tag_);
    double price_ratio = 0.0;
    double target_price_ratio = 0.0;
    double spu_new_price_ratio = 0.0;
    double target_spu_new_price_ratio = 0.0;
    bool enable_unify_ratio = false;
    double quailty_discount_ratio = 1.0;
    bool is_account_sta = false;
    if (cid_price_discount_config_ != nullptr) {
      auto iter_cid = cid_price_discount_config_->find(p_ad->get_account_id());
      if (iter_cid != cid_price_discount_config_->end()) {
        discount_ratio = iter_cid->second;
        is_account_sta = true;
      }
    }
    // 命中白名单就不走这里
    if (!is_account_sta) {
      auto iter_biz = biz_stage_info_->data().biz_stage_info().find(key_str);
      if (iter_biz != biz_stage_info_->data().biz_stage_info().end()) {
        price_ratio = iter_biz->second.price_ratio();
        target_price_ratio = iter_biz->second.target_price_ratio();
        discount_ratio = price_ratio;
        spu_new_price_ratio = iter_biz->second.spu_new_price_ratio();
        target_spu_new_price_ratio = iter_biz->second.target_spu_new_price_ratio();
      } else if (enable_cid_corp_default_strategy_exp_) {
        discount_ratio = cid_corp_default_price_ratio_;
      }
      switch (p_ad->get_ad_direct_merchant_stage()) {
        // 过渡期间
        case kuaishou::ad::AdEnum::ACCOUNT_STAGE_FRESHMAN_2_SOPHOMORE:
        case kuaishou::ad::AdEnum::ACCOUNT_STAGE_SOPHOMOR_2_JUNIOR:
        case kuaishou::ad::AdEnum::ACCOUNT_STAGE_JUNIOR_2_SENIOR:
          if (price_ratio > 0 && target_price_ratio > 0 &&
              p_ad->Attr(ItemIdx::ad_direct_merchant_price_ratio).GetDoubleValue(
              p_ad->AttrIndex()).value_or(0.0) > 0) {
            auto ad_direct_merchant_price_ratio = std::min(1.0,
                p_ad->Attr(ItemIdx::ad_direct_merchant_price_ratio).GetDoubleValue(
                p_ad->AttrIndex()).value_or(0.0));
            auto delta_ratio = target_price_ratio - price_ratio;
            discount_ratio = price_ratio + delta_ratio * ad_direct_merchant_price_ratio;
          }
          break;
        default:
          break;
      }
      if (enable_cid_quality_strategy_exp_) {
        auto iter_quality_biz = biz_stage_info_->data().quality_level_info().find(key_str_quality);
        if (iter_quality_biz != biz_stage_info_->data().quality_level_info().end()) {
          double quality_level_price_ratio = iter_quality_biz->second.quality_level_price_ratio();
          enable_unify_ratio = iter_quality_biz->second.enable_unify_ratio();
          if (quality_level_price_ratio > 0) {
            quailty_discount_ratio = quality_level_price_ratio;
          }
        }
        if (enable_cid_spu_strategy_exp_) {
          auto iter_spu_biz = biz_stage_info_->data().spu_type_info().find(key_str_spu);
          if (iter_spu_biz != biz_stage_info_->data().spu_type_info().end()) {
            spu_new_price_ratio = iter_spu_biz->second.spu_new_price_ratio();
            target_spu_new_price_ratio = iter_spu_biz->second.target_spu_new_price_ratio();
          }
        }
      }
      // 新品策略
      if (enable_cid_spu_strategy_exp_) {
        if (enable_cid_spu_restart_strategy_exp_) {
          switch (p_ad->get_cid_spu_type()) {
            // 过渡期间
            case kuaishou::ad::AdEnum::CID_SPU_NEW:
            case kuaishou::ad::AdEnum::CID_SPU_EX:
            case kuaishou::ad::AdEnum::CID_SPU_OT:
              if (spu_new_price_ratio > 0) {
                spu_discount_ratio = spu_new_price_ratio;
              }
              break;
            case kuaishou::ad::AdEnum::CID_SPU_OLD_2_NEW:
              // 过渡期 折扣
              if (target_spu_new_price_ratio > 0 && spu_new_price_ratio > 0
                && p_ad->get_cid_spu_price_ratio() > 0) {
                auto cid_spu_price_ratio = std::min(1.0, p_ad->get_cid_spu_price_ratio());
                auto delta_ratio = target_spu_new_price_ratio - spu_new_price_ratio;
                spu_discount_ratio = spu_new_price_ratio + delta_ratio * cid_spu_price_ratio;
              }
              break;
            default:
              break;
          }
        } else {
          // 新品才会有折扣
          if (p_ad->get_cid_spu_type() == kuaishou::ad::AdEnum::CID_SPU_NEW) {
            if (spu_new_price_ratio > 0) {
              spu_discount_ratio = spu_new_price_ratio;
            }
          } else if (p_ad->get_cid_spu_type() == kuaishou::ad::AdEnum::CID_SPU_OLD_2_NEW) {
            // 过渡期 折扣
            if (target_spu_new_price_ratio > 0 && spu_new_price_ratio > 0
              && p_ad->get_cid_spu_price_ratio() > 0) {
              auto cid_spu_price_ratio = std::min(1.0, p_ad->get_cid_spu_price_ratio());
              auto delta_ratio = target_spu_new_price_ratio - spu_new_price_ratio;
              spu_discount_ratio = spu_new_price_ratio + delta_ratio * cid_spu_price_ratio;
            }
          }
        }
      }
      corp_discount_ratio = discount_ratio;
      if (enable_cid_quality_strategy_exp_) {
        if (enable_unify_ratio) {
          discount_ratio = quailty_discount_ratio;
        } else {
          discount_ratio = discount_ratio * spu_discount_ratio * quailty_discount_ratio;
        }
      } else {
        discount_ratio = discount_ratio * spu_discount_ratio;
      }
    }
    if (enable_cid_global_ratio_exp_ && cid_global_price_ratio > 0) {
      discount_ratio = discount_ratio * cid_global_price_ratio;
    }
    auto account_iter = biz_stage_info_->data().account_zk_info().find(p_ad->get_account_id());
    if (account_iter != biz_stage_info_->data().account_zk_info().end()) {
      if (enable_cid_account_zk_strategy_ocpx_exp_) {
        for (auto ocpx : account_iter->second.ocpxs()) {
          if (ocpx == p_ad->get_ocpx_action_type()) {
            discount_ratio = account_iter->second.price_ratio();
          }
        }
      } else {
        discount_ratio = account_iter->second.price_ratio();
      }
    }

    // 激励流量对 EVENT_ORDER_SUBMIT 做计费打折系数控制
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
      || (enable_cid_bid_boost_roas_exp_ && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CID_ROAS)) {
      if (session_data->get_is_rewarded()) {
        discount_ratio = std::max(discount_ratio, inspire_video_discount_ratio_lower_);
      }
    }

    discount_ratio = std::max(discount_ratio, cid_discount_ratio_lower_);
    discount_ratio = std::min(discount_ratio, cid_discount_ratio_upper_);
    double origin_price = p_ad->get_price();
    auto cut_price = std::max(p_ad->get_price() * 1.0, 1.0);
    if (enable_cid_price_record_rewrited_) {
      cut_price = std::max(p_ad->get_price() * discount_ratio, 1.0);
    }
    origin_price = std::max(origin_price, 1.0);
    p_ad->Attr(ItemIdx::cid_spu_price_rate).SetDoubleValue(p_ad->AttrIndex(),
          origin_price, false, false);
    p_ad->Attr(ItemIdx::cid_biz_price_rate).SetDoubleValue(p_ad->AttrIndex(),
          cut_price, false, false);
    return discount_ratio;
  };
  FINISH_COMPUTE()
}

void PriceCidStraGoodsType_Admit(ks::platform::AddibleRecoContextInterface* context,
                              platform::DataFrame* item_table,
                              const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_cid_use_goods_type_support =
      SPDM_enable_cid_use_goods_type_support(session_data->get_spdm_ctx());
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    auto& ad = *p_ad;
    std::string corporation_name = std::string(
      p_ad->Attr(ItemIdx::fd_ACCOUNT_corporation_name).GetStringValue(p_ad->AttrIndex()).value_or(""));
    if (!enable_cid_use_goods_type_support) {
      return false;
    }
    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPM &&
        ad.get_ad_source_type() == kuaishou::ad::AdSourceType::DSP) {
      return false;
    }
    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA &&
        (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
         ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE)) {
      return false;
    }
    if (ad.get_unit_source_type() == kuaishou::ad::AdEnum_UnitSourceType_UNIT_SOURCE_TYPE_FURIOUS
      && ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA) {
      return false;
    }
    if (p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_SUBMIT
      && p_ad->get_ocpx_action_type() != kuaishou::ad::AD_CID_ROAS) {
      return false;
    }
    return true;
  };
  FINISH_ADMIT()
}

// cid 折扣策略 迁移 商品状态池维度 补贴
void PriceCidStraGoodsType_Compute(ks::platform::AddibleRecoContextInterface* context,
                                ks::platform::DataFrame* item_table,
                                const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  // 商家投放配置
  auto inspire_video_discount_ratio_lower =
      SPDM_inspire_video_discount_ratio_lower(session_data->get_spdm_ctx());
  const auto& cid_goods_info = engine_base::AdKconfUtil::cidGoodsTypeSupportConf();
  auto cid_goods_global_price_ratio = cid_goods_info->data().cid_goods_global_price_ratio();
  auto cid_ab_global_price_ratio = SPDM_cid_ab_global_price_ratio(session_data->get_spdm_ctx());
  cid_goods_global_price_ratio = cid_goods_global_price_ratio * cid_ab_global_price_ratio;
  const auto& cid_goods_info_new = RankKconfUtil::cidGoodsTypeSupportNewConf();
  const auto& cid_goods_type_account = RankKconfUtil::cidGoodsTypeAccountConf();
  bool enable_cid_use_goods_type_new_support =
                SPDM_enable_cid_use_goods_type_new_support(session_data->get_spdm_ctx());
  auto cid_goods_global_price_ratio_new = cid_goods_info_new->data().cid_goods_global_price_ratio();
  auto cid_ab_global_price_ratio_new = cid_ab_global_price_ratio;
  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    int64_t account_id = p_ad->get_account_id();
    bool is_select_account = false;
      if (cid_goods_type_account != nullptr && cid_goods_type_account->count(account_id) > 0) {
      is_select_account = true;
      }
    double discount_ratio = 1.0;      // 客户维度折扣
    double price_ratio = 0.0;
    int64_t cid_goods_type = 0;
    if (enable_cid_use_goods_type_new_support &&is_select_account) {
    //新逻辑
      if (cid_goods_info_new == nullptr) {
        return 1.0;
      }
      cid_goods_type = p_ad->Attr(ItemIdx::fd_ACCOUNT_cid_goods_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
      auto corporation_name = std::string(
      p_ad->Attr(ItemIdx::fd_ACCOUNT_corporation_name).GetStringValue(p_ad->AttrIndex()).value_or(""));
      auto iter_biz = cid_goods_info_new->data().goods_type_info().find(std::to_string(cid_goods_type));
      if (iter_biz != cid_goods_info_new->data().goods_type_info().end()) {
        price_ratio = iter_biz->second.price_ratio();
        discount_ratio = price_ratio;
      }
      cid_goods_global_price_ratio_new = cid_goods_global_price_ratio_new * cid_ab_global_price_ratio_new;
      if (cid_goods_global_price_ratio_new > 0) {
        discount_ratio = discount_ratio * cid_goods_global_price_ratio_new;
      }
      // 白名单逻辑
      auto iter_corpor = cid_goods_info_new->data().corporation_zk_info().find(corporation_name);
      auto iter_account = cid_goods_info_new->data().account_zk_info().find(std::to_string(account_id));
      if (iter_corpor != cid_goods_info_new->data().corporation_zk_info().end()) {
          for (auto ocpx : iter_corpor->second.ocpxs()) {
          if (ocpx == p_ad->get_ocpx_action_type()) {
            discount_ratio = iter_corpor->second.price_ratio();
          }
          }
      } else if (iter_account != cid_goods_info_new->data().account_zk_info().end()) {
          for (auto ocpx : iter_account->second.ocpxs()) {
          if (ocpx == p_ad->get_ocpx_action_type()) {
            discount_ratio = iter_account->second.price_ratio();
          }
          }
      }
    } else {  //老逻辑
    if (cid_goods_info == nullptr) {
      return 1.0;
    }
    cid_goods_type = p_ad->Attr(ItemIdx::fd_ACCOUNT_cid_goods_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    auto corporation_name = std::string(
    p_ad->Attr(ItemIdx::fd_ACCOUNT_corporation_name).GetStringValue(p_ad->AttrIndex()).value_or(""));
    auto iter_biz = cid_goods_info->data().goods_type_info().find(std::to_string(cid_goods_type));
    if (iter_biz != cid_goods_info->data().goods_type_info().end()) {
      price_ratio = iter_biz->second.price_ratio();
      discount_ratio = price_ratio;
    }
    if (cid_goods_global_price_ratio > 0) {
      discount_ratio = discount_ratio * cid_goods_global_price_ratio;
    }
    // 白名单逻辑
    auto iter_corpor = cid_goods_info->data().corporation_zk_info().find(corporation_name);
    auto iter_account = cid_goods_info->data().account_zk_info().find(std::to_string(account_id));
    if (iter_corpor != cid_goods_info->data().corporation_zk_info().end()) {
        for (auto ocpx : iter_corpor->second.ocpxs()) {
        if (ocpx == p_ad->get_ocpx_action_type()) {
          discount_ratio = iter_corpor->second.price_ratio();
        }
        }
    } else if (iter_account != cid_goods_info->data().account_zk_info().end()) {
        for (auto ocpx : iter_account->second.ocpxs()) {
        if (ocpx == p_ad->get_ocpx_action_type()) {
          discount_ratio = iter_account->second.price_ratio();
        }
        }
    }
    }
    // 激励流量对 EVENT_ORDER_SUBMIT 做计费打折系数控制
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
      || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CID_ROAS) {
      if (session_data->get_is_rewarded()) {
        discount_ratio = std::max(discount_ratio, inspire_video_discount_ratio_lower);
      }
    }
    RANK_DOT_STATS(session_data, static_cast<int64_t>(discount_ratio * 100),
                   "ad.front_server.DirectMerchantDiscount_v4",
                   std::to_string(cid_goods_type));
    double origin_price = p_ad->get_price();
    auto cut_price = std::max(p_ad->get_price() * 1.0, 1.0);
    cut_price = std::max(p_ad->get_price() * discount_ratio, 1.0);
    origin_price = std::max(origin_price, 1.0);
    p_ad->Attr(ItemIdx::cid_spu_price_rate).SetDoubleValue(p_ad->AttrIndex(),
          origin_price, false, false);
    p_ad->Attr(ItemIdx::cid_biz_price_rate).SetDoubleValue(p_ad->AttrIndex(),
          cut_price, false, false);
    return discount_ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceNewProduct_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_new_product_strategy_ =
      SPDM_enable_new_product_strategy(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (p_ad == nullptr || session_data == nullptr) {
      return false;
    }
    auto& ad = *p_ad;
    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPM &&
         ad.get_ad_source_type() == kuaishou::ad::AdSourceType::DSP) {
      return false;
    }
    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA &&
        (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
        ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE)) {
      return false;
    }
    if (ad.get_unit_source_type() == kuaishou::ad::AdEnum_UnitSourceType_UNIT_SOURCE_TYPE_FURIOUS
          && ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA) {
      return false;
    }
    if (!SPDM_enable_new_product_discount_strategy(session_data->get_spdm_ctx())) {
      return false;
    }
    return enable_new_product_strategy_;
  };
  FINISH_ADMIT()
}

void AdjustPriceNewProduct_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto new_product_strategy_exp_ =
      SPDM_new_product_strategy_exp(session_data->get_spdm_ctx());
  auto new_product_discount_info_ = RankKconfUtil::newProductDiscount();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double account_discount_ratio = 1.0;  // 账户维度折扣
    double product_discount_ratio = 1.0;   // 客户维度折扣
    double discount_ratio = 1.0;  // 最终折扣
    auto& discount_conf_map = new_product_discount_info_->data().discount_conf();
    auto iter_exp = discount_conf_map.find(new_product_strategy_exp_);
    std::string product_name = p_ad->get_product_name();
    if (iter_exp !=  discount_conf_map.end()) {
      auto& account_map = iter_exp->second.account_discount_info();
      auto& product_map = iter_exp->second.product_discount_info();
      // 高优账户维度
      auto iter_account = account_map.find(p_ad->get_account_id());
      if (iter_account !=  account_map.end()) {
        account_discount_ratio = iter_account->second;
      }
      // 次优先级产品维度
      auto iter_product = product_map.find(product_name);
      if (iter_product !=  product_map.end()) {
        product_discount_ratio = iter_product->second;
      }
      discount_ratio = std::min(account_discount_ratio, product_discount_ratio);
      return discount_ratio;
    }
    return discount_ratio;
  };
  FINISH_COMPUTE()
}

void InnerPriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 本地自适应顶价+打折实验，不走内循环计费打折
    if (p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_LSP) {
      return false;
    }

    // 内循环打折不考虑代投
    if (p_ad->Attr(ItemIdx::unit_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0) == 1 &&
        p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_LSP) {
      return false;
    }
    if (p_ad->Is(AdFlag::is_inner_loop_ad)) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void PriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_LOCAL_STORE_ORDER ||
         (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED))) {
      return false;
    }
    return true;
  };
  FINISH_ADMIT()
}

// 短剧业务用打折准入
void DuanjuPriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void NewSpuPriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto tag_list = RankKconfUtil::newSpuBoostTagList();
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (!tag_list || tag_list->find(p_ad->get_new_spu_tag()) == tag_list->end()) {
      return false;
    }
    if (p_ad->get_campaign_type() != kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
      return false;
    }
    if (p_ad->get_scene_oriented_type() != 24) {
      return false;
    }
    if (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
      return false;
    }
    return true;
  };
  FINISH_ADMIT()
}
REGISTER_FUNCTION(NewSpuPriceDiscount_Admit, FunctionType::Admit)

void NewSpuPriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double new_spu_boost_ratio = SPDM_new_spu_boost_ratio(session_data->get_spdm_ctx());
  double storewide_boost_ratio = RankKconfUtil::storewideBoostRatio();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double discount_ratio = 1.0;
    if (new_spu_boost_ratio != 0) {
      discount_ratio = 1.0 / new_spu_boost_ratio;
    }
    // 全站叠加顶价计费打折的部分系数
    if (p_ad->get_scene_oriented_type() == 24) {
      discount_ratio *= storewide_boost_ratio;
    }
    return discount_ratio;
  };
  FINISH_COMPUTE()
}

REGISTER_FUNCTION(NewSpuPriceDiscount_Compute, FunctionType::Compute)


void NewSpuPriceDiscountBuyer_Admit(ks::platform::AddibleRecoContextInterface* context,
  platform::DataFrame* item_table, const FactorInfo& factor_info) {
GET_PRICE_FACTOR_AD_CONTEXT()
auto tag_list = RankKconfUtil::newSpuBoostTagList();
double buyer_boost_ratio = SPDM_buyer_boost_ratio(session_data->get_spdm_ctx());
auto buyer_type_list = RankKconfUtil::highBuyerTypeList();
auto buyer_effective_type =
    session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
auto admit = [&] (auto& item) -> bool {
  GET_PRICE_FACTOR_ADMIT_AD_PTR()
  if (!tag_list || tag_list->find(p_ad->get_new_spu_tag()) == tag_list->end()) {
    return false;
  }
  if (p_ad->get_campaign_type() != kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
    return false;
  }
  if (p_ad->get_scene_oriented_type() != 24) {
    return false;
  }
  if (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
      p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
      p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
    return false;
  }
  if (buyer_boost_ratio == 1.0) {
    return false;
  }
  if (buyer_type_list &&
      buyer_type_list->find(buyer_effective_type) != buyer_type_list->end()) {
    return true;
  }
  return false;
};
FINISH_ADMIT()
}
REGISTER_FUNCTION(NewSpuPriceDiscountBuyer_Admit, FunctionType::Admit)

void NewSpuPriceDiscountBuyer_Compute(ks::platform::AddibleRecoContextInterface* context,
  ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
GET_PRICE_FACTOR_AD_CONTEXT()
double buyer_boost_ratio = SPDM_buyer_boost_ratio(session_data->get_spdm_ctx());
double storewide_boost_ratio = RankKconfUtil::storewideBoostRatio();
auto compute = [&] (auto& item) -> double {
  GET_PRICE_FACTOR_COMPUTE_AD_PTR()
  double discount_ratio = 1.0;
  if (buyer_boost_ratio != 0) {
    discount_ratio = 1.0 / buyer_boost_ratio;
  }
  // 全站叠加顶价计费打折的部分系数
  if (p_ad->get_scene_oriented_type() == 24) {
    discount_ratio *= storewide_boost_ratio;
  }
  RANK_DOT_STATS(session_data, discount_ratio * 1000, "NewSpuPriceDiscountBuyer_ratio",
    kuaishou::ad::AdEnum_AdQueueType_Name(p_ad->get_ad_queue_type()),
    kuaishou::ad::AdEnum_AdSceneOrientedTypeEnum_Name(p_ad->get_scene_oriented_type()),
    kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
  return discount_ratio;
};
FINISH_COMPUTE()
}

REGISTER_FUNCTION(NewSpuPriceDiscountBuyer_Compute, FunctionType::Compute)

void InnerPriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto p_price_discount_config_ = RankKconfUtil::innerPriceDiscountConfig();
  auto pc_select_price_discount_tails_ = RankKconfUtil::pcSelectPriceDiscountUnitTail();
  auto pc_select_price_discount_conf_ = RankKconfUtil::pcSelectPriceDiscountConf();
  auto lsp_no_discount_account_set = RankKconfUtil::lspNoDiscountAccountSet();
  // 本地顶价自适应配置，历史原因复用搜索 Kconf，不改名字了
  auto lsp_ecpc_discount_union_conf = RankKconfUtil::lspSearchPriceRatio();
  auto enable_lsp_auto_ecpc_unit_tail = RankKconfUtil::enableLspPriceRatioAutoEcpcUnitTail();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double discount_ratio = 1.0;
    // 本地顶价自适应打折
    if (enable_lsp_auto_ecpc_unit_tail->IsOnFor(p_ad->get_unit_id()) &&
        lsp_ecpc_discount_union_conf &&
        p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_LSP) {
        // 全局默认打折系数
        std::string kconf_key = "default";
        auto iter_d = lsp_ecpc_discount_union_conf->find(kconf_key);
        if (iter_d != lsp_ecpc_discount_union_conf->end()) {
          discount_ratio = iter_d->second;
        }
        // account 粒度参数
        kconf_key = absl::StrCat(p_ad->get_account_id());
        auto iter_acc = lsp_ecpc_discount_union_conf->find(kconf_key);
        if (iter_acc != lsp_ecpc_discount_union_conf->end()) {
          discount_ratio = iter_acc->second;
        }
        // account + ocpx 粒度参数
        kconf_key = absl::StrCat(p_ad->get_account_id(), "_",
              kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
        auto iter_acc_ocpx = lsp_ecpc_discount_union_conf->find(kconf_key);
        if (iter_acc_ocpx != lsp_ecpc_discount_union_conf->end()) {
          discount_ratio = iter_acc_ocpx->second;
        }
    }

    if (p_price_discount_config_) {
      // 优先级 3
      auto iter_account =
          p_price_discount_config_->account_discount_map.find(p_ad->get_account_id());
      if (iter_account != p_price_discount_config_->account_discount_map.end() && 0 < iter_account->second) {
        discount_ratio = iter_account->second;
      }
      // 优先级 2
      auto iter_campaign =
          p_price_discount_config_->campaign_discount_map.find(p_ad->get_campaign_id());
      if (iter_campaign != p_price_discount_config_->campaign_discount_map.end() &&
          iter_campaign->second > 0) {
        discount_ratio = iter_campaign->second;
      }
      // 优先级 1
      auto iter_unit =
          p_price_discount_config_->unit_discount_map.find(p_ad->get_unit_id());
      if (iter_unit != p_price_discount_config_->unit_discount_map.end() &&
          iter_unit->second > 0) {
        discount_ratio = iter_unit->second;
      }
    }
    if (SPDM_enable_lsp_no_discount(session_data->get_spdm_ctx()) &&
        p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_LSP &&
        lsp_no_discount_account_set != nullptr &&
        lsp_no_discount_account_set->count(p_ad->get_account_id()) > 0) {
      discount_ratio = 1.0;
    }
    return discount_ratio;
  };
  FINISH_COMPUTE()
}

void LocalLifePriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
  platform::DataFrame* item_table, const FactorInfo& factor_info) {
GET_PRICE_FACTOR_AD_CONTEXT()
auto admit = [&] (auto& item) -> bool {
  GET_PRICE_FACTOR_ADMIT_AD_PTR()
  if (p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_LSP) {
    return true;
  }
  return false;
};
FINISH_ADMIT()
}

void LocalLifePriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
  ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
GET_PRICE_FACTOR_AD_CONTEXT()
auto lsp_no_discount_account_set = RankKconfUtil::lspNoDiscountAccountSet();
// 本地顶价自适应配置，历史原因复用搜索 Kconf，不改名字了
auto lsp_ecpc_discount_union_conf = RankKconfUtil::lspSearchPriceRatio();
auto enable_lsp_auto_ecpc_unit_tail = RankKconfUtil::enableLspPriceRatioAutoEcpcUnitTail();
auto compute = [&] (auto& item) -> double {
  GET_PRICE_FACTOR_COMPUTE_AD_PTR()
  double discount_ratio = 1.0;
  // 本地顶价自适应打折
  if (enable_lsp_auto_ecpc_unit_tail->IsOnFor(p_ad->get_unit_id()) &&
      lsp_ecpc_discount_union_conf &&
      p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_LSP) {
      // 全局默认打折系数
      std::string kconf_key = "default";
      auto iter_d = lsp_ecpc_discount_union_conf->find(kconf_key);
      if (iter_d != lsp_ecpc_discount_union_conf->end()) {
        discount_ratio = iter_d->second;
      }
      // account 粒度参数
      kconf_key = absl::StrCat(p_ad->get_account_id());
      auto iter_acc = lsp_ecpc_discount_union_conf->find(kconf_key);
      if (iter_acc != lsp_ecpc_discount_union_conf->end()) {
        discount_ratio = iter_acc->second;
      }
      // account + ocpx 粒度参数
      kconf_key = absl::StrCat(p_ad->get_account_id(), "_",
            kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
      auto iter_acc_ocpx = lsp_ecpc_discount_union_conf->find(kconf_key);
      if (iter_acc_ocpx != lsp_ecpc_discount_union_conf->end()) {
        discount_ratio = iter_acc_ocpx->second;
      }
  }

  if (p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_LSP &&
      lsp_no_discount_account_set != nullptr &&
      lsp_no_discount_account_set->count(p_ad->get_account_id()) > 0) {
    discount_ratio = 1.0;
  }
  return discount_ratio;
};
FINISH_COMPUTE()
}

// 主要承载运营手动填报
void DuanjuPriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_playlet_discount_auto_offline =
    SPDM_enable_playlet_discount_auto_offline(session_data->get_spdm_ctx());
  bool enable_playlet_discount_monitor = SPDM_enable_playlet_discount_monitor(session_data->get_spdm_ctx());
  auto playlet_config_ = RankKconfUtil::playletPriceRatioOpt();
  auto playlet_config2 = RankKconfUtil::playletPriceRatioOpt2();  // 自动下线配置
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double discount_ratio = 1.0;
    if (playlet_config_) {
      const std::string& campaign_type =
          kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type());
      // campaign_type
      auto iter_campaign_type = playlet_config_->find(campaign_type);
      if (iter_campaign_type != playlet_config_->end() && iter_campaign_type->second > 0) {
        discount_ratio = iter_campaign_type->second;
      }
      const std::string& campaign_type_ocpx =
            absl::Substitute("$0_$1",
            kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type()),
            kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
      auto iter_campaign_type_ocpx = playlet_config_->find(campaign_type_ocpx);
      if (iter_campaign_type_ocpx != playlet_config_->end() && iter_campaign_type_ocpx->second > 0) {
        discount_ratio = iter_campaign_type_ocpx->second;
      }
      // 剧粒度
      const std::string& playlet_key = p_ad->get_playlet_name();
      auto iter_playlet_name = playlet_config_->find(playlet_key);
      if (iter_playlet_name != playlet_config_->end() && iter_playlet_name->second > 0) {
        discount_ratio = iter_playlet_name->second;
      }
      // 账户
      auto iter_account_2 =
          playlet_config_->find(absl::StrCat(p_ad->get_account_id()));
      if (iter_account_2 != playlet_config_->end() && iter_account_2->second > 0) {
        discount_ratio = iter_account_2->second;
      }
    }
    // 剧粒度 + 自动下线
    if (enable_playlet_discount_auto_offline && playlet_config2) {
      const std::string& playlet_key = p_ad->get_playlet_name();
      auto values = playlet_config2->data().playlet_end_time_discount_ratio();
      auto iter_playlet_name = values.find(playlet_key);
      if (iter_playlet_name != values.end()) {
        const std::string& end_time = iter_playlet_name->second.end_time();
        int64_t now_s = session_data->get_current_timestamp_nodiff() / 1000000;
        if (utility::StrTimeCompare(end_time, now_s)) {
          discount_ratio = iter_playlet_name->second.ratio();
          // 监控是否生效
          if (enable_playlet_discount_monitor) {
            session_data->dot_perf->Count(static_cast<int64>(discount_ratio * 1000), "playlet_discount_ratio", playlet_key);  // NOLINT
          }
        }
      }
    }
    return discount_ratio;
    };
  FINISH_COMPUTE()
}

void PriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto minigame_price_config_ = RankKconfUtil::gameNewProductPriceRatio();
  auto minigame_roi7_price_config = RankKconfUtil::gameRoi7PriceRatio();
  bool enable_wechat_new_product_auto_price_discount =
      SPDM_enable_wechat_new_product_auto_price_discount(session_data->get_spdm_ctx());
  auto playlet_config_ = RankKconfUtil::playsetPriceRatio();
  auto inspire_video_discount_ratio_lower_ =
      SPDM_inspire_video_discount_ratio_lower(session_data->get_spdm_ctx());
  auto enable_close_na_fiction_price_discount =
      SPDM_enable_close_na_fiction_price_discount(session_data->get_spdm_ctx());
  bool enable_fiction_hot_book_discount = SPDM_enable_fiction_hot_book_discount(session_data->get_spdm_ctx());
  auto playlet_sdpa_ = ks::engine_base::IndustryPlayletSdpaIns().GetData();
  auto playlet_sdpa_name_discount_config_ = RankKconfUtil::duanjuNewNameSet();
  auto playlet_sdpa_campaign_config_ = RankKconfUtil::playletSdpaCampaignPriceRatio();
  auto playlet_campaign_config_ = RankKconfUtil::playletCampaignTypePriceRatio();
  auto playlet_sdpa_black_config_ = RankKconfUtil::playletSdpaBlackPriceRatio();
  auto cartoon_product_set_ = RankKconfUtil::cartoonProductNameSet();
  if (SPDM_enableSwitchCartoonKconf()) {
    cartoon_product_set_ = RankKconfUtil::cartoonSeriesProductNameSet();
  }
  auto enable_playlet_sdpa_discount_ = SPDM_enable_playlet_sdpa_discount(session_data->get_spdm_ctx());
  auto enable_playlet_sdpa_discount_skip_mcb =
        SPDM_enable_playlet_sdpa_discount_skip_mcb(session_data->get_spdm_ctx());
  auto enable_playlet_total_discount_ = SPDM_enable_playlet_total_discount(session_data->get_spdm_ctx());
  auto enable_playlet_total_discount_sup_mcb_ =
        SPDM_enable_playlet_total_discount_sup_mcb(session_data->get_spdm_ctx());
  double iaa_playlet_stream_discount = SPDM_iaa_playlet_stream_discount(session_data->get_spdm_ctx());
  double iap_playlet_stream_discount = SPDM_iap_playlet_stream_discount(session_data->get_spdm_ctx());
  bool enable_iaa_playlet_stream_discount = SPDM_enable_iaa_playlet_stream_discount(session_data->get_spdm_ctx());  // NOLINT
  bool enable_iap_playlet_stream_discount = SPDM_enable_iap_playlet_stream_discount(session_data->get_spdm_ctx());  // NOLINT
  bool disable_playlet_campaign_type_discount =
        SPDM_disable_playlet_campaign_type_discount(session_data->get_spdm_ctx());
  bool enable_playlet_reset_undiscount = SPDM_enable_playlet_reset_undiscount(session_data->get_spdm_ctx());
  bool enable_cartoon_discount = SPDM_enable_cartoon_discount(session_data->get_spdm_ctx());
  bool enable_adjust_origin_discount = SPDM_enable_adjust_origin_discount(session_data->get_spdm_ctx());
  auto fiction_adjust_discount_ratio = SPDM_fiction_adjust_discount_ratio(session_data->get_spdm_ctx());
  bool enable_game_iaa_roi1_price_discount =
    SPDM_enable_game_iaa_roi1_price_discount(session_data->get_spdm_ctx());
  bool enable_game_iaa_roi1_replace_price_discount =
    SPDM_enable_game_iaa_roi1_replace_price_discount(session_data->get_spdm_ctx());
  double game_iaa_roi1_price_discount_ratio_coef =
    SPDM_game_iaa_roi1_price_discount_ratio_coef(session_data->get_spdm_ctx());
  std::string minigame_roi1_price_discount_exp_tag =
        SPDM_minigame_roi1_price_discount_exp_tag(session_data->get_spdm_ctx());
  bool enable_game_iaa_roi7_price_discount =
    SPDM_enable_game_iaa_roi7_price_discount(session_data->get_spdm_ctx());
  bool enable_game_iaa_roi7_gtm_price_discount =
      SPDM_enable_game_iaa_roi7_gtm_price_discount(session_data->get_spdm_ctx());
  uint64_t now_time =  session_data->get_current_timestamp_nodiff()/ 1e6;
  uint64_t day_start =
      now_time / 86400 * 86400 - 8 * 60 * 60;  // 时区为东八区 时间戳为 UTC 0 时区
  uint64_t day_sec = 24 * 60 * 60;
  if (now_time - day_start > day_sec) {
    day_start += day_sec;
  }
  int32_t hour_idx = (now_time - day_start) / 3600;

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    int64_t old_price = p_ad->get_price();
    const auto p_price_discount_config = RankKconfUtil::onlinePriceDiscountConfig();
    double discount_ratio = 1.0;
    bool is_paid_duanju_ad_res = p_ad->get_second_industry_id_v5() == 2012 ||
      (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION &&
       SPDM_enablePlayletCampaignTypeDiscount());
    bool is_na_fiction = p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_FICTION_PROMOTION;
    // NA 小说屏蔽实验
    if ((enable_close_na_fiction_price_discount ||
      (enable_fiction_hot_book_discount && !enable_adjust_origin_discount)) && is_na_fiction) {
      return discount_ratio;
    }
    if (p_price_discount_config && playlet_config_) {
      // 优先级 3
      auto iter_account =
          p_price_discount_config->account_discount_map.find(p_ad->get_account_id());
      if (iter_account != p_price_discount_config->account_discount_map.end() && 0 < iter_account->second) {
        discount_ratio = iter_account->second;
      }
      // 优先级 2
      auto iter_campaign =
          p_price_discount_config->campaign_discount_map.find(p_ad->get_campaign_id());
      if (iter_campaign != p_price_discount_config->campaign_discount_map.end() &&
          0 < iter_campaign->second) {
        discount_ratio = iter_campaign->second;
      }
      // 优先级 1
      auto iter_unit =
          p_price_discount_config->unit_discount_map.find(p_ad->get_unit_id());
      if (iter_unit != p_price_discount_config->unit_discount_map.end() && 0 < iter_unit->second) {
        discount_ratio = iter_unit->second;
      }
      const std::string& campaign_type =
          kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type());
      // campaign_type
      auto iter_campaign_type = playlet_config_->find(campaign_type);
      if (iter_campaign_type != playlet_config_->end() && iter_campaign_type->second > 0 &&
          is_paid_duanju_ad_res &&
          !disable_playlet_campaign_type_discount) {
        discount_ratio = iter_campaign_type->second;
      }
      const std::string& campaign_type_ocpx =
            absl::Substitute("$0_$1",
            kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type()),
            kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
      auto iter_campaign_type_ocpx = playlet_config_->find(campaign_type_ocpx);
      if (iter_campaign_type_ocpx != playlet_config_->end() && iter_campaign_type_ocpx->second > 0 &&
          is_paid_duanju_ad_res) {
        discount_ratio = iter_campaign_type_ocpx->second;
      }
      if (enable_cartoon_discount && cartoon_product_set_ && cartoon_product_set_->find(p_ad->get_product_name()) != cartoon_product_set_->end()) {  // NOLINT
        const std::string& campaign_type =
            absl::Substitute("cartoon_$0",
            kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type()));
        // campaign_type
        auto iter_campaign_type = playlet_config_->find(campaign_type);
        if (iter_campaign_type != playlet_config_->end() && iter_campaign_type->second > 0 &&
          is_paid_duanju_ad_res &&
          !disable_playlet_campaign_type_discount) {
          discount_ratio = iter_campaign_type->second;
        }
        const std::string& campaign_type_ocpx =
            absl::Substitute("cartoon_$0_$1",
            kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type()),
            kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
        auto iter_campaign_type_ocpx = playlet_config_->find(campaign_type_ocpx);
        if (iter_campaign_type_ocpx != playlet_config_->end() && iter_campaign_type_ocpx->second > 0 &&
          is_paid_duanju_ad_res) {
          discount_ratio = iter_campaign_type_ocpx->second;
        }
      }
      // 剧粒度
      const std::string& playlet_key = p_ad->get_playlet_name();
      auto iter_playlet_name = playlet_config_->find(playlet_key);
      if (iter_playlet_name != playlet_config_->end() && iter_playlet_name->second > 0
          && is_paid_duanju_ad_res) {
        discount_ratio = iter_playlet_name->second;
      }
      // 账户
      auto iter_account_2 =
          playlet_config_->find(absl::StrCat(p_ad->get_account_id()));
      if (iter_account_2 != playlet_config_->end() && iter_account_2->second > 0) {
        discount_ratio = iter_account_2->second;
      }

      if (enable_playlet_total_discount_ && is_paid_duanju_ad_res) {
        if ((enable_playlet_total_discount_sup_mcb_ &&
            p_ad->get_bid_type() == kuaishou::ad::AdEnum::MCB) ||
            p_ad->get_bid_type() != kuaishou::ad::AdEnum::MCB) {
          auto iter_total_type = playlet_campaign_config_->find(campaign_type);
          if (iter_total_type != playlet_campaign_config_->end() && iter_total_type->second > 0) {
            discount_ratio = iter_total_type->second;
          }
          if (enable_iaa_playlet_stream_discount) {
            if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS) {
              discount_ratio = iaa_playlet_stream_discount;
            }
          }
          if (enable_iap_playlet_stream_discount) {
            if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS) {
              discount_ratio = iap_playlet_stream_discount;
            }
          }
        }
      }

      // 小游戏新品打折
      if (enable_wechat_new_product_auto_price_discount && minigame_price_config_ &&
        p_ad->get_industry_parent_id_v3() == 1018 &&
        (p_ad->get_landing_page_component() == 1 || p_ad->get_landing_page_component() == 2)) {
          auto iter_product = minigame_price_config_->find(p_ad->get_product_name());
          if (iter_product != minigame_price_config_->end()) {
            discount_ratio = std::min(discount_ratio, iter_product->second);
        }
      }

      // game iaa 7r 计费打折
      bool is_iaa_roi7_target_account = utility::IsGameIaaRoi7TargetAccount(session_data, ad);
      std::string minigame_roi7_exp_tag =
        SPDM_minigame_roi7_exp_tag(session_data->get_spdm_ctx());
      if ((enable_game_iaa_roi7_price_discount && is_iaa_roi7_target_account) ||
          (enable_game_iaa_roi7_gtm_price_discount
            && p_ad->get_first_industry_id_v5() == 1018
            && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE
            && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS)) {
        // 明投系数
        double game_ad_iaa_7day_roas_price_discount_ratio_coef =
            SPDM_game_ad_iaa_7day_roas_price_discount_ratio_coef(session_data->get_spdm_ctx());
        auto iter_account = minigame_roi7_price_config->find(p_ad->get_account_id());
        if (iter_account != minigame_roi7_price_config->end()) {
          double game_discount_ratio = iter_account->second;
          // 暗测系数
          double game_iaa_roi7_price_discount_ratio_coef =
            SPDM_game_iaa_roi7_price_discount_ratio_coef(session_data->get_spdm_ctx());
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS) {
            discount_ratio = std::min(discount_ratio,
                   game_discount_ratio * game_ad_iaa_7day_roas_price_discount_ratio_coef);
          } else {
            discount_ratio = std::min(discount_ratio,
                   game_discount_ratio * game_iaa_roi7_price_discount_ratio_coef);
          }
        } else {
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS) {
            discount_ratio = discount_ratio * game_ad_iaa_7day_roas_price_discount_ratio_coef;
            bool enable_game_iaa_7day_roas_price_discount_replace =
              SPDM_enable_game_iaa_7day_roas_price_discount_replace(session_data->get_spdm_ctx());
            if (enable_game_iaa_7day_roas_price_discount_replace) {
              discount_ratio = game_ad_iaa_7day_roas_price_discount_ratio_coef;
            }
          }
        }
        RANK_DOT_STATS(session_data, discount_ratio * 1000, "minigame_iaa_roi7_discount_ratio",
                   absl::StrCat(minigame_roi7_exp_tag),
                   absl::StrCat(kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type())));
      }

      // 游戏 iaa 首 r 独立打折
      if (p_ad->get_first_industry_id_v5() == 1018
          && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE
          && ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS) {
        if (enable_game_iaa_roi1_price_discount) {
          // 融合
          if (enable_game_iaa_roi1_replace_price_discount) {
            discount_ratio = game_iaa_roi1_price_discount_ratio_coef;
          } else {
            // 直接替换
            discount_ratio *= game_iaa_roi1_price_discount_ratio_coef;
          }
        }
        RANK_DOT_STATS(session_data, discount_ratio * 1000, "minigame_iaa_roi1_discount_ratio_final",
                    absl::StrCat(minigame_roi1_price_discount_exp_tag),
                    absl::StrCat(kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type())));
      }
      // game iaa 关键行为计费打折
      std::string minigame_iaa_event_key_inapp_action_price_discount_exp_tag =
        SPDM_minigame_iaa_event_key_inapp_action_price_discount_exp_tag(session_data->get_spdm_ctx());
      bool enable_game_iaa_event_key_inapp_action_price_discount =
        SPDM_enable_game_iaa_event_key_inapp_action_price_discount(session_data->get_spdm_ctx());
      double iaa_event_key_inapp_action_price_discount_ratio_coef =
        SPDM_iaa_event_key_inapp_action_price_discount_ratio_coef(session_data->get_spdm_ctx());
      if (enable_game_iaa_event_key_inapp_action_price_discount) {
        if (p_ad->get_first_industry_id_v5() == 1018
          && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE
          && ad.get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION) {
              discount_ratio = iaa_event_key_inapp_action_price_discount_ratio_coef;
          }
          RANK_DOT_STATS(session_data, discount_ratio * 1000,
                      "game_iaa_event_key_inapp_action_price_discount_final",
                      absl::StrCat(minigame_iaa_event_key_inapp_action_price_discount_exp_tag),
                      absl::StrCat(kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type())));
      }

      // 新剧打折
      auto unit_id = p_ad->get_unit_id();
      if (enable_playlet_sdpa_discount_ && playlet_sdpa_ && playlet_sdpa_.get() &&
          playlet_sdpa_name_discount_config_ &&
          playlet_sdpa_campaign_config_ && playlet_sdpa_black_config_ &&
          is_paid_duanju_ad_res) {
        auto playlet_sdpa_ptr = playlet_sdpa_->find(unit_id);
        if (playlet_sdpa_ptr != playlet_sdpa_->end()) {
          auto& name = playlet_sdpa_ptr->second.playlet_name;
          auto playlet_sdpa_campaign_ptr = playlet_sdpa_campaign_config_->find(campaign_type);
          auto playlet_black_ptr = playlet_sdpa_black_config_->find(name);
          if (playlet_sdpa_name_discount_config_->find(name) != playlet_sdpa_name_discount_config_->end() &&
              playlet_sdpa_campaign_ptr != playlet_sdpa_campaign_config_->end() &&
              playlet_black_ptr == playlet_sdpa_black_config_->end()) {
            if (!(enable_playlet_sdpa_discount_skip_mcb &&
                  p_ad->get_bid_type() == kuaishou::ad::AdEnum::MCB)) {
              discount_ratio = std::min(playlet_sdpa_campaign_ptr->second, discount_ratio);
            }
          }
        }
      }

      // 激励流量对 EVENT_ORDER_SUBMIT 做计费打折系数控制
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT) {
        if (session_data->get_is_rewarded()) {
          discount_ratio = std::max(discount_ratio, inspire_video_discount_ratio_lower_);
        }
      }
      if (enable_playlet_reset_undiscount) {
        p_ad->set_price_undiscount(old_price);
      }

      // 小说非爆款书折扣调整
      if (enable_fiction_hot_book_discount && enable_adjust_origin_discount &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_FICTION_PROMOTION) {
        discount_ratio *= fiction_adjust_discount_ratio;
      }
    }
    return discount_ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceBadPhotoRatio_Admit(ks::platform::AddibleRecoContextInterface* context,
                                   platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    auto& ad = *p_ad;
    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPM &&
        ad.get_ad_source_type() == kuaishou::ad::AdSourceType::DSP) {
      return false;
    }
    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA &&
        (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
         ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE)) {
      return false;
    }
    if (ad.get_unit_source_type() == kuaishou::ad::AdEnum_UnitSourceType_UNIT_SOURCE_TYPE_FURIOUS
        && ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA) {
      return false;
    }
    if (nullptr == p_ad ||
        !p_ad->Attr(ItemIdx::is_low_quality_photo).GetIntValue(p_ad->AttrIndex()).value_or(0)) {
      return false;
    }
    return true;
  };
  FINISH_ADMIT()
}

void AdjustPriceBadPhotoRatio_Compute(ks::platform::AddibleRecoContextInterface* context,
                                     ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto max_price_raise_ratio = RankKconfUtil::maxPriceRaiseRatio();
  auto min_price_raise_ratio = RankKconfUtil::minPriceRaiseRatio();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    if (p_ad->Attr(ItemIdx::is_low_quality_photo).GetIntValue(p_ad->AttrIndex()).value_or(0)) {
      int64_t raw_price = ad.get_price();
      auto tax_rate = p_ad->Attr(ItemIdx::tax_rate).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
      double tmp_new_price = static_cast<int64>(raw_price * tax_rate);
      // price 不能超过太多
      int64_t new_price = static_cast<int64>(std::max(min_price_raise_ratio * ad.get_auction_bid(),
        std::min(tmp_new_price, max_price_raise_ratio * ad.get_auction_bid())));
      p_ad->Attr(ItemIdx::tax_amount).SetIntValue(p_ad->AttrIndex(), new_price - raw_price, false, false);
      if (raw_price > 0) {
        ratio = (new_price * 1.0) / raw_price;
      }
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void PoQuanPriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
                               platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_po_quan_bcee_strategy = SPDM_enable_po_quan_bcee_strategy(session_data->get_spdm_ctx());
  auto enable_outerloop_boost_ratio_explore =
      SPDM_enable_outerloop_boost_ratio_explore(session_data->get_spdm_ctx());
  auto enable_outerloop_boost_ratio_explore_v2 =
      SPDM_enable_outerloop_boost_ratio_explore_v2(session_data->get_spdm_ctx());
  auto enable_outerloop_boost_ratio_explore_v3 =
      SPDM_enable_outerloop_boost_ratio_explore_v3(session_data->get_spdm_ctx());
  const auto& res_focused_ocpx_set = RankKconfUtil::outerloopResFocusedOcpxSet();
  bool is_thanos_mix = session_data->get_is_thanos_mix_request();
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    RANK_DOT_COUNT(session_data, 1, "PoQuanPriceDiscount_Admit.total_cnt",
                   absl::StrCat(p_ad->get_ad_queue_type()),
                   SPDM_po_quan_strategy_versions(session_data->get_spdm_ctx()));
    int64_t max_ecpc_tag = p_ad->Attr(ItemIdx::max_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double max_ecpc_ratio =
        p_ad->Attr(ItemIdx::max_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    int64_t min_ecpc_tag = p_ad->Attr(ItemIdx::min_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double min_ecpc_ratio =
        p_ad->Attr(ItemIdx::min_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    double outerloop_ee_boost_ratio = p_ad->get_outerloop_ee_boost_ratio();
    auto ocpx_action_type = p_ad->get_ocpx_action_type();
    // PoQuanEcpc = 332
    if ((max_ecpc_tag == 332 && max_ecpc_ratio > 1.0) || (min_ecpc_tag == 332 && min_ecpc_ratio > 1.0)) {
      RANK_DOT_COUNT(session_data, 1, "PoQuanPriceDiscount_Admit.admit_success_cnt",
                     absl::StrCat(p_ad->get_ad_queue_type()),
                     SPDM_po_quan_strategy_versions(session_data->get_spdm_ctx()));
      return true;
    }
    // 破圈 ecpc ratio 后置策略
    if (enable_po_quan_bcee_strategy && outerloop_ee_boost_ratio > 1.0) {
      RANK_DOT_COUNT(session_data, 1, "PoQuanPriceDiscount_Admit.admit_success_cnt",
                     absl::StrCat(p_ad->get_ad_queue_type()),
                     SPDM_po_quan_strategy_versions(session_data->get_spdm_ctx()),
                     "CalcCpmForBcee");
      return true;
    }
    if ((enable_outerloop_boost_ratio_explore || enable_outerloop_boost_ratio_explore_v2 ||
        enable_outerloop_boost_ratio_explore_v3) &&
         outerloop_ee_boost_ratio > 1.0) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void PoQuanPriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
                                 ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto po_quan_price_discount_extra_discount =
      SPDM_po_quan_price_discount_extra_discount(session_data->get_spdm_ctx());
  auto enable_outerloop_boost_ratio_explore =
      SPDM_enable_outerloop_boost_ratio_explore(session_data->get_spdm_ctx());
  auto enable_outerloop_boost_ratio_explore_v2 =
      SPDM_enable_outerloop_boost_ratio_explore_v2(session_data->get_spdm_ctx());
  auto enable_outerloop_boost_ratio_explore_v3 =
      SPDM_enable_outerloop_boost_ratio_explore_v3(session_data->get_spdm_ctx());
  auto enable_po_quan_extra_discount_for_v0 =
      SPDM_enable_po_quan_extra_discount_for_v0(session_data->get_spdm_ctx());
  double po_quan_extra_discount_for_v0 =
      SPDM_po_quan_extra_discount_for_v0(session_data->get_spdm_ctx());
  auto enable_po_quan_bcee_strategy = SPDM_enable_po_quan_bcee_strategy(session_data->get_spdm_ctx());
  auto po_quan_price_discount_min_discount =
      SPDM_po_quan_price_discount_min_discount(session_data->get_spdm_ctx());
  auto po_quan_extra_discount_product_name_list = RankKconfUtil::poQuanExtraDiscountProductNameList();
  auto po_quan_price_discount_product_name_extra_discount =
      SPDM_po_quan_price_discount_product_name_extra_discount(session_data->get_spdm_ctx());
  auto res_focused_for_mix_price_discount =
      SPDM_res_focused_for_mix_price_discount(session_data->get_spdm_ctx());
  const auto& res_focused_ocpx_set = RankKconfUtil::outerloopResFocusedOcpxSet();
  bool is_thanos_mix = session_data->get_is_thanos_mix_request();
  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    int64_t max_ecpc_tag = p_ad->Attr(ItemIdx::max_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double max_ecpc_ratio =
        p_ad->Attr(ItemIdx::max_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    int64_t min_ecpc_tag = p_ad->Attr(ItemIdx::min_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double min_ecpc_ratio =
        p_ad->Attr(ItemIdx::min_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    int64_t strategy_type =
        p_ad->Attr(ItemIdx::ee_strategy_type).GetIntValue(p_ad->AttrIndex()).value_or(0);
    auto ocpx_action_type = p_ad->get_ocpx_action_type();

    auto po_quan_price_discount_extra_discount_org = po_quan_price_discount_extra_discount;
    double outerloop_ee_boost_ratio = p_ad->get_outerloop_ee_boost_ratio();
    if ((enable_outerloop_boost_ratio_explore || enable_outerloop_boost_ratio_explore_v2 ||
        enable_outerloop_boost_ratio_explore_v3) &&
      outerloop_ee_boost_ratio > 1.0) {
      double ratio = 1.0 / outerloop_ee_boost_ratio;
      RANK_DOT_STATS(session_data, ratio * 1000, "PoQuanPriceDiscount_Compute.ratio",
                     "boost_ratio_explore");
      return ratio;
    }
    if (enable_po_quan_extra_discount_for_v0 && strategy_type == 0) {
      po_quan_price_discount_extra_discount_org = po_quan_extra_discount_for_v0;
    }
    auto product_name = p_ad->get_product_name();
    if (po_quan_extra_discount_product_name_list &&
        po_quan_extra_discount_product_name_list->find(product_name) !=
            po_quan_extra_discount_product_name_list->end()) {
      po_quan_price_discount_extra_discount_org =
          po_quan_price_discount_extra_discount_org * po_quan_price_discount_product_name_extra_discount;
      RANK_DOT_COUNT(session_data, 1, "PoQuanPriceDiscount_Compute.po_quan_extra_discount_product_name",
                     product_name);
    }

    std::string strategy_version = "";
    double ratio = 1.0;
    // PoQuanEcpc = 332
    if ((max_ecpc_tag == 332 && max_ecpc_ratio > 1.0) || (min_ecpc_tag == 332 && min_ecpc_ratio > 1.0)) {
      if (max_ecpc_tag == 332 && max_ecpc_ratio > 1.0) {
        ratio = 1.0 / max_ecpc_ratio;
      } else if (min_ecpc_tag == 332 && min_ecpc_ratio > 1.0) {
        ratio = 1.0 / min_ecpc_ratio;
      }

      ratio =
          std::max(ratio * po_quan_price_discount_extra_discount_org, po_quan_price_discount_min_discount);
    }
    // 如果将 ecpc 策略延后
    if (enable_po_quan_bcee_strategy && outerloop_ee_boost_ratio > 1.0) {
      ratio = 1.0 / outerloop_ee_boost_ratio;
      strategy_version = "CalcCpmForBcee";
      ratio =
          std::max(ratio * po_quan_price_discount_extra_discount_org, po_quan_price_discount_min_discount);
    }

    RANK_DOT_STATS(session_data, ratio * 1000, "PoQuanPriceDiscount_Compute.ratio",
                   absl::StrCat(p_ad->get_ad_queue_type()),
                   SPDM_po_quan_strategy_versions(session_data->get_spdm_ctx()),
                   strategy_version);
    return ratio;
  };
  FINISH_COMPUTE()
}

void InnerProductEEPriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
                                       platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT();
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR();
    RANK_DOT_COUNT(session_data, 1, "InnerProductEEPriceDiscount_Admit.total_cnt",
      kuaishou::ad::AdEnum::AdQueueType_Name(p_ad->get_ad_queue_type()));
    int64_t max_ecpc_tag =
      p_ad->Attr(ItemIdx::max_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double max_ecpc_ratio =
      p_ad->Attr(ItemIdx::max_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    int64_t min_ecpc_tag =
      p_ad->Attr(ItemIdx::min_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double min_ecpc_ratio =
      p_ad->Attr(ItemIdx::min_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);

    // InnerProductEEEcpc = 342
    if ((max_ecpc_tag == 342 && max_ecpc_ratio > 1.0) || (min_ecpc_tag == 342 && min_ecpc_ratio > 1.0)) {
      RANK_DOT_COUNT(session_data, 1,
        "InnerProductEEPriceDiscount_Admit.admit_success_cnt",
        kuaishou::ad::AdEnum::AdQueueType_Name(p_ad->get_ad_queue_type()));
      return true;
    }

    return false;
  };
  FINISH_ADMIT();
}

void InnerProductEEPriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
                                         ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR();
    int64_t max_ecpc_tag =
      p_ad->Attr(ItemIdx::max_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double max_ecpc_ratio =
      p_ad->Attr(ItemIdx::max_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    int64_t min_ecpc_tag =
      p_ad->Attr(ItemIdx::min_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double min_ecpc_ratio =
      p_ad->Attr(ItemIdx::min_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);

    double ratio = 1.0;
    // InnerProductEEEcpc = 342
    if ((max_ecpc_tag == 342 && max_ecpc_ratio > 1.0) || (min_ecpc_tag == 342 && min_ecpc_ratio > 1.0)) {
      if (max_ecpc_tag == 342 && max_ecpc_ratio > 1.0) {
        ratio = 1.0 / max_ecpc_ratio;
      } else if (min_ecpc_tag == 342 && min_ecpc_ratio > 1.0) {
        ratio = 1.0 / min_ecpc_ratio;
      }
    }

    ratio = std::max(ratio, 0.5);

    RANK_DOT_STATS(session_data, ratio * 1000,
      "InnerProductEEPriceDiscount_Compute.ratio",
      kuaishou::ad::AdEnum::AdQueueType_Name(p_ad->get_ad_queue_type()));

    return ratio;
  };

  FINISH_COMPUTE();
}

void InnerAdxEEOrientationDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
                                       platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT();

  bool enable_discount = SPDM_enable_inner_adx_ee_orientation(session_data->get_spdm_ctx());

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR();
    if (enable_discount &&
        session_data->get_is_thanos_mix_request() &&
        p_ad->Is(AdFlag::is_esp_ad) &&
        p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT();
}

void InnerAdxEEOrientationDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
                                         ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT();

  std::unordered_map<int64_t, double> map_author_ratio;
  const auto& user_orientation = session_data->get_rank_request()->ad_request().ad_user_info().orientation();

  auto conf_ptr = RankKconfUtil::innerAdxEEOrientationConf();
  if (conf_ptr != nullptr) {
    const auto& conf_config = conf_ptr->data().config();
    // 查用户是否命中实验人群包
    for (const auto& user_orientation_id : user_orientation) {
      const auto& conf_config_iter = conf_config.find(user_orientation_id);
      if (conf_config_iter != conf_config.end()) {
        const auto& discount_ratio = conf_config_iter->second.discount_ratio();
        const auto& author_list = conf_config_iter->second.author_list();
        if (discount_ratio >= 1.0) {
          continue;
        }
        for (int64_t author_id : author_list) {
          map_author_ratio.insert({author_id, discount_ratio});
        }
      }
    }
  }

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR();
    double ratio = 1.0;
    const auto& iter = map_author_ratio.find(p_ad->get_author_id());
    if (iter != map_author_ratio.end()) {
      ratio = iter->second;
    }
    return ratio;
  };

  FINISH_COMPUTE();
}

void IAAPGameROIDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
                               platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_iaap_game_roi_discount = SPDM_enable_iaap_game_roi_discount(session_data->get_spdm_ctx());
  bool enable_mini_game_iaap_7r_admit_discount =
                             SPDM_enable_mini_game_iaap_7r_admit_discount(session_data->get_spdm_ctx());
  auto mini_game_roi_map_conf = engine_base::AdKconfUtil::miniGameROIMap()->data();
  auto iaap_price_ratio_map = mini_game_roi_map_conf.iaap_price_ratio_map;
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 快小游 iaap
    auto product_iter = iaap_price_ratio_map.find(p_ad->get_product_name());
    auto account_iter = iaap_price_ratio_map.find(absl::StrCat(p_ad->get_account_id()));
    if (enable_iaap_game_roi_discount && p_ad->Is(AdFlag::is_iaap_game_ad)) {
      if (product_iter != iaap_price_ratio_map.end()) {
        RANK_DOT_COUNT(session_data, 1, "IAAPGameROIDiscount_admit_product", product_iter->first);
        return true;
      }
      if (account_iter != iaap_price_ratio_map.end()) {
        RANK_DOT_COUNT(session_data, 1, "IAAPGameROIDiscount_admit_account", account_iter->first);
        return true;
      }
    }

    if (enable_mini_game_iaap_7r_admit_discount && p_ad->Is(AdFlag::is_iap_7r_game_ad)) {
      RANK_DOT_COUNT(session_data, 1, "IAAPGameROIDiscount_admit_7r");
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void IAAPGameROIDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
                                 ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double iaap_game_roi_lower_bound = SPDM_iaap_game_roi_lower_bound(session_data->get_spdm_ctx());
  double iaap_game_roi_upper_bound = SPDM_iaap_game_roi_upper_bound(session_data->get_spdm_ctx());
  double default_iap_7r_bid_discount_ratio =
                             SPDM_default_iap_7r_bid_discount_ratio(session_data->get_spdm_ctx());
  bool enable_iaap_game_7r_discount = SPDM_enable_iaap_game_7r_discount(session_data->get_spdm_ctx());
  std::string iap_game_twin_bid_group_tag = SPDM_iap_game_twin_bid_group_tag(session_data->get_spdm_ctx());
  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double price_ratio = 1.0;
    auto mini_game_roi_map_conf = engine_base::AdKconfUtil::miniGameROIMap()->data();
    auto iaap_price_ratio_map = mini_game_roi_map_conf.iaap_price_ratio_map;
    auto product_iter = iaap_price_ratio_map.find(p_ad->get_product_name());
    auto account_iter = iaap_price_ratio_map.find(absl::StrCat(p_ad->get_account_id()));
    // 产品粒度
    if (product_iter != iaap_price_ratio_map.end()) {
      price_ratio = product_iter->second;
      RANK_DOT_STATS(session_data, price_ratio * 1000,
                                "IAAPGameROIDiscount_Compute_Product", product_iter->first);
    }
    // 账户粒度
    if (account_iter != iaap_price_ratio_map.end()) {
      price_ratio = account_iter->second;
      RANK_DOT_STATS(session_data, price_ratio * 1000,
                                "IAAPGameROIDiscount_Compute_Account", account_iter->first);
    }

    // 7r 出价调整首 r
    double iap_7r_bid_price_discount = default_iap_7r_bid_discount_ratio;
    const auto& iap_game_twin_bid_conf = engine_base::AdKconfUtil::iapGameTwinBidConf()->data();
    const auto& iap_twin_bid_discount_conf = iap_game_twin_bid_conf.iap_twin_bid_discount_map;
    auto iap_twin_bid_discount_map_iter = iap_twin_bid_discount_conf.find(iap_game_twin_bid_group_tag);
    if (iap_twin_bid_discount_map_iter != iap_twin_bid_discount_conf.end()) {
      auto& iap_twin_bid_discount_map = iap_twin_bid_discount_map_iter->second;
      auto product_iter = iap_twin_bid_discount_map.find(p_ad->get_product_name());
      auto account_iter = iap_twin_bid_discount_map.find(absl::StrCat(p_ad->get_account_id()));
      // 产品粒度
      if (product_iter != iap_twin_bid_discount_map.end()) {
        iap_7r_bid_price_discount = product_iter->second;
        RANK_DOT_STATS(session_data, price_ratio * 1000,
                                  "IAAPGameROIDiscount_Compute_Product_7R", product_iter->first);
      }
      // 账户粒度
      if (account_iter != iap_twin_bid_discount_map.end()) {
        iap_7r_bid_price_discount = account_iter->second;
        RANK_DOT_STATS(session_data, price_ratio * 1000,
                                  "IAAPGameROIDiscount_Compute_Account_7R", account_iter->first);
      }
    }
    if (enable_iaap_game_7r_discount) {
      price_ratio *= iap_7r_bid_price_discount;
    }

    price_ratio = std::min(std::max(
                    price_ratio, iaap_game_roi_lower_bound), iaap_game_roi_upper_bound);
    RANK_DOT_STATS(session_data, price_ratio * 1000, "IAAPGameROIDiscount_Compute_Final");
    return price_ratio;
  };
  FINISH_COMPUTE()
}

void FictionHotBookDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
                               platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_close_na_fiction_price_discount =
      SPDM_enable_close_na_fiction_price_discount(session_data->get_spdm_ctx());
  bool enable_fiction_hot_book_discount = SPDM_enable_fiction_hot_book_discount(session_data->get_spdm_ctx());
  auto fiction_hot_book_discount_manual_control = RankKconfUtil::fictionHotBookDiscountManualControl();
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    bool is_na_fiction = p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_FICTION_PROMOTION;
    if (!enable_fiction_hot_book_discount || enable_close_na_fiction_price_discount || !is_na_fiction) {
      return false;
    }
    int64_t max_ecpc_tag = p_ad->Attr(ItemIdx::max_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double max_ecpc_ratio =
        p_ad->Attr(ItemIdx::max_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    int64_t min_ecpc_tag = p_ad->Attr(ItemIdx::min_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double min_ecpc_ratio =
        p_ad->Attr(ItemIdx::min_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    if ((max_ecpc_tag == 374 && max_ecpc_ratio > 1.0) || (min_ecpc_tag == 374 && min_ecpc_ratio > 1.0)) {
      RANK_DOT_COUNT(session_data, 1, "FictionHotBookDiscount_ecpc_tag_Admit",
                     absl::StrCat(p_ad->get_ad_queue_type()));
      return true;
    }
    if (fiction_hot_book_discount_manual_control != nullptr) {
      int64_t kwai_book_id =
        p_ad->Attr(ItemIdx::fd_UNIT_kwai_book_id).GetIntValue(p_ad->AttrIndex()).value_or(0);
      auto iter = fiction_hot_book_discount_manual_control->find(absl::StrCat(kwai_book_id));
      if (iter != fiction_hot_book_discount_manual_control->end()) {
        RANK_DOT_COUNT(session_data, 1, "FictionHotBookDiscount_white_Admit",
                     absl::StrCat(p_ad->get_ad_queue_type()));
        return true;
      }
    }
    return false;
  };
  FINISH_ADMIT()
}

void FictionHotBookDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
                                 ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double fiction_hot_book_discount_max_thresh =
      SPDM_fiction_hot_book_discount_max_thresh(session_data->get_spdm_ctx());
  double fiction_hot_book_discount_min_thresh =
      SPDM_fiction_hot_book_discount_min_thresh(session_data->get_spdm_ctx());
  double fiction_hot_book_discount_offset =
      SPDM_fiction_hot_book_discount_offset(session_data->get_spdm_ctx());
  auto fiction_hot_book_discount_manual_control = RankKconfUtil::fictionHotBookDiscountManualControl();
  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    double ratio = 1.0;
    int64_t kwai_book_id =
        p_ad->Attr(ItemIdx::fd_UNIT_kwai_book_id).GetIntValue(p_ad->AttrIndex()).value_or(0);
    int64_t max_ecpc_tag = p_ad->Attr(ItemIdx::max_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double max_ecpc_ratio =
        p_ad->Attr(ItemIdx::max_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    int64_t min_ecpc_tag = p_ad->Attr(ItemIdx::min_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double min_ecpc_ratio =
        p_ad->Attr(ItemIdx::min_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    if (max_ecpc_tag == 374 && max_ecpc_ratio > 1.0) {
      ratio = 1.0 / max_ecpc_ratio;
    } else if (min_ecpc_tag == 374 && min_ecpc_ratio > 1.0) {
      ratio = 1.0 / min_ecpc_ratio;
    }

    if (fiction_hot_book_discount_manual_control != nullptr) {
      auto iter = fiction_hot_book_discount_manual_control->find(absl::StrCat(kwai_book_id));
      if (iter != fiction_hot_book_discount_manual_control->end()) {
        ratio = iter->second;
      }
    }

    ratio = std::min(std::max(ratio * fiction_hot_book_discount_offset, fiction_hot_book_discount_min_thresh),
                     fiction_hot_book_discount_max_thresh);
    RANK_DOT_STATS(session_data, ratio * 1000, "FictionHotBookDiscount_Compute",
                   absl::StrCat(p_ad->get_ad_queue_type()));
    return ratio;
  };
  FINISH_COMPUTE()
}


void GameBigRExplore_Admit(ks::platform::AddibleRecoContextInterface* context,
                               platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_game_big_r_explore_discount =
                    SPDM_enable_game_big_r_explore_discount(session_data->get_spdm_ctx());
  bool enable_game_big_r_explore_discount_v2 =
                    SPDM_enable_game_big_r_explore_discount_v2(session_data->get_spdm_ctx());
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (enable_game_big_r_explore_discount && p_ad->get_game_big_r_explore_ratio() > 1.0) {
      RANK_DOT_STATS(session_data, 1,
                  "GameBigRExplore_Admit", absl::StrCat(p_ad->Is(AdFlag::is_iaap_game_ad)));
      return true;
    }
    if (enable_game_big_r_explore_discount_v2 && p_ad->get_final_big_r_explore()) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void GameBigRExplore_Compute(ks::platform::AddibleRecoContextInterface* context,
                                 ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double game_big_r_explore_discount_lower_bound =
                    SPDM_game_big_r_explore_discount_lower_bound(session_data->get_spdm_ctx());
  double game_big_r_explore_discount_upper_bound =
                    SPDM_game_big_r_explore_discount_upper_bound(session_data->get_spdm_ctx());
  double game_big_r_explore_discount_offset =
                    SPDM_game_big_r_explore_discount_offset(session_data->get_spdm_ctx());
  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double price_ratio = 1.0;
    if (p_ad->get_game_big_r_explore_ratio() > 1.0) {
      price_ratio = 1 / p_ad->get_game_big_r_explore_ratio() * game_big_r_explore_discount_offset;
    }

    if (p_ad->get_explore_ecpc_ratio() > 1.0) {
      price_ratio = 1 / p_ad->get_explore_ecpc_ratio() * game_big_r_explore_discount_offset;
      RANK_DOT_STATS(session_data, price_ratio * 1000, "GameBigRExploreV2_Compute");
    }
    price_ratio = std::min(std::max(price_ratio,
            game_big_r_explore_discount_lower_bound), game_big_r_explore_discount_upper_bound);
    RANK_DOT_STATS(session_data, price_ratio * 1000, "GameBigRExplore_Compute");
    return price_ratio;
  };
  FINISH_COMPUTE()
}

void GameStabilityDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
                               platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_game_stability_discount =
                  SPDM_enable_game_stability_discount(session_data->get_spdm_ctx());
  auto game_stability_account_conf = engine_base::AdKconfUtil::gameClientConfMap()->data();
  auto game_client_conf_map = game_stability_account_conf.game_client_conf_map;
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 小游戏整体
    if (enable_game_stability_discount && p_ad->get_first_industry_id_v5() == 1018 &&
        (p_ad->get_landing_page_component() == 1 || p_ad->get_landing_page_component() == 2)) {
      for (auto [tag, tail_set] : game_client_conf_map) {
        // 配置的账户尾号都准入
        if (tail_set.count(p_ad->get_account_id() % 100) > 0) {
          RANK_DOT_COUNT(session_data, 1,
                    "game_account_admit_rank", tag, absl::StrCat(p_ad->get_account_id() % 100));
          return true;
        }
      }
    }
    return false;
  };
  FINISH_ADMIT()
}

void GameStabilityDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
                                 ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double game_stability_lower_bound = SPDM_game_stability_lower_bound(session_data->get_spdm_ctx());
  auto game_stability_upper_bound = SPDM_game_stability_upper_bound(session_data->get_spdm_ctx());
  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    // 解析配置
    auto game_stability_account_conf = engine_base::AdKconfUtil::gameClientConfMap()->data();
    auto game_client_conf_map = game_stability_account_conf.game_client_conf_map;
    auto game_client_weight_map = game_stability_account_conf.game_client_weight_map;
    auto game_client_switch_map = game_stability_account_conf.game_client_switch_map;

    auto account_discount_conf = engine_base::AdKconfUtil::gameStabilityAccountMap()->data();
    auto account_discount_map = account_discount_conf.account_discount_map;

    // 账户解析，打平组调参
    double game_stability_weight = 1.0;
    bool enable_advertiser_exp = false;
    std::string account_tag = "";
    int64_t account_tail = p_ad->get_account_id() % 100;
    for (auto [tag_key, tail_set] : game_client_conf_map) {
      if (tail_set.count(account_tail) > 0) {
        account_tag = tag_key;
        auto weight_iter = game_client_weight_map.find(tag_key);
        if (weight_iter != game_client_weight_map.end()) {
          game_stability_weight = weight_iter->second;
        }
        auto admit_iter = game_client_switch_map.find(tag_key);
        if (admit_iter != game_client_switch_map.end()) {
          enable_advertiser_exp = admit_iter->second;
        }
      }
    }

    // 计费打折策略系数
    double price_ratio = 1.0;
    auto account_iter = account_discount_map.find(p_ad->get_account_id());
    if (account_iter != account_discount_map.end() && enable_advertiser_exp) {
      price_ratio = account_iter->second;
      RANK_DOT_STATS(session_data, price_ratio * 1000,
                                "game_stability_origin_price_ratio_rank", account_tag);
    }

    RANK_DOT_STATS(session_data, price_ratio * 1000, "game_stability_price_ratio_rank", account_tag);
    RANK_DOT_STATS(session_data, game_stability_weight * 1000, "game_stability_weight_rank", account_tag);
    price_ratio *= game_stability_weight;
    RANK_DOT_STATS(session_data, price_ratio * 1000, "game_stability_final_price_rate_rank", account_tag);
    price_ratio = std::min(std::max(
                    price_ratio, game_stability_lower_bound), game_stability_upper_bound);
    return price_ratio;
  };
  FINISH_COMPUTE()
}

void KuaiGameInspireDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
                               platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_mini_game_inspire_admit =
                  SPDM_enable_mini_game_inspire_admit(session_data->get_spdm_ctx());
  auto kuai_game_inspire_conf = RankKconfUtil::kuaiGameInspireConf()->data();
  auto page_id_set = kuai_game_inspire_conf.page_id_set;
  auto pos_id_set = kuai_game_inspire_conf.pos_id_set;
  auto admit_advertiser_map = kuai_game_inspire_conf.admit_advertiser_map;
  int64_t medium_pos_id = session_data->get_pos_manager_base().GetMediumPosId();
  auto admit = [&](auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 小游戏整体
    auto product_name_iter = admit_advertiser_map.find(p_ad->get_product_name());
    auto account_id_iter = admit_advertiser_map.find(absl::StrCat(p_ad->get_account_id()));
    if (enable_mini_game_inspire_admit && (page_id_set.count(session_data->get_page_id()) ||
     pos_id_set.count(medium_pos_id)) && (product_name_iter != admit_advertiser_map.end() ||
                                          account_id_iter != admit_advertiser_map.end())) {
      RANK_DOT_COUNT(session_data, 1, "kuai_game_inspire_discount",
                    absl::StrCat(session_data->get_page_id()), absl::StrCat(medium_pos_id));
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void KuaiGameInspireDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
                                 ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double mini_game_inspire_lower_bound =
                              SPDM_mini_game_inspire_lower_bound(session_data->get_spdm_ctx());
  double mini_game_inspire_upper_bound =
                              SPDM_mini_game_inspire_upper_bound(session_data->get_spdm_ctx());
  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    auto kuai_game_inspire_conf = RankKconfUtil::kuaiGameInspireConf()->data();
    auto admit_advertiser_map = kuai_game_inspire_conf.admit_advertiser_map;
    auto product_name_iter = admit_advertiser_map.find(p_ad->get_product_name());
    auto account_id_iter = admit_advertiser_map.find(absl::StrCat(p_ad->get_account_id()));
    double price_ratio = 1.0;
    if (account_id_iter != admit_advertiser_map.end()) {
      price_ratio = account_id_iter->second;
      RANK_DOT_STATS(session_data, price_ratio * 1000,
                                  "mini_game_inspire_account", account_id_iter->first);
    }
    if (product_name_iter != admit_advertiser_map.end()) {
      price_ratio = product_name_iter->second;
      RANK_DOT_STATS(session_data, price_ratio * 1000,
                                "mini_game_inspire_product", product_name_iter->first);
    }
    price_ratio = std::min(std::max(price_ratio,
                        mini_game_inspire_lower_bound), mini_game_inspire_upper_bound);
    return price_ratio;
  };
  FINISH_COMPUTE()
}

void AdStorewideUplift_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_storewide_live_uplift_model_cmd =
      SPDM_enable_storewide_live_uplift_model_cmd(session_data->get_spdm_ctx());
  bool enable_storewide_live_uplift_price =
      SPDM_enable_storewide_live_uplift_price(session_data->get_spdm_ctx());
  const std::string& buyer_effective_type = session_data->get_rank_request()
      ->ad_request().ad_user_info().buyer_effective_type();
  auto storewide_live_uplift_price_tail = RankKconfUtil::storewideLiveUpliftPriceTail();
  const int64_t& page_id = session_data->get_page_id();
  std::string exp_tag = SPDM_storewide_live_uplift_exp_tag(session_data->get_spdm_ctx());

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 客户实验打 exp_tag （客户侧实验优先级高于流量侧实验）
    if (SPDM_enable_storewide_live_uplift_author_exp_tag(session_data->get_spdm_ctx())) {
      const auto& author_conf_ptr = RankKconfUtil::storewideLiveUpliftAuthorExpTag();
      if (author_conf_ptr) {
        int32_t div_num = author_conf_ptr->data().div_num();
        const auto& exp_config = author_conf_ptr->data().exp_config();
        if (div_num > 0) {
          std::string author_tail_num = std::to_string(p_ad->get_author_id() % div_num);
          const auto& iter = exp_config.find(author_tail_num);
          if (iter != exp_config.end()) {
            exp_tag = iter->second;
          }
        }
      }
    }

    // 直播全站 全互斥
    if (p_ad->get_scene_oriented_type() != kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE ||
        p_ad->get_storewide_incompatible_type() != 1) {
      return false;
    }
    // 模型预估值请求
    if (!enable_storewide_live_uplift_model_cmd) {
      return false;
    }
    // 低 u 策略准入
    if (SPDM_enable_storewide_live_uplift_block_lower_u(session_data->get_spdm_ctx())) {
      if (buyer_effective_type == "" || buyer_effective_type == "U0" || buyer_effective_type == "U1") {
        return false;
      }
    }
    // 分页面准入（非屏蔽逻辑）
    if (SPDM_enable_storewide_live_uplift_price_page_limit(session_data->get_spdm_ctx())) {
      const auto& kconf = RankKconfUtil::storewideLiveUpliftPageLimit();
      if (kconf) {
        const auto& exp_config = kconf->data().exp_config();
        if (!exp_config.empty()) {
          const auto& iter = exp_config.find(exp_tag);
          if (iter != exp_config.end()) {
            const auto& page_ids = iter->second.page_ids();
            if (std::find(page_ids.begin(), page_ids.end(), page_id) == page_ids.end()) {
              return false;
            }
          }
        }
      }
    }
    // 流量策略准入 && 客户策略准入
    if (enable_storewide_live_uplift_price ||
        (storewide_live_uplift_price_tail && storewide_live_uplift_price_tail->IsOnFor(p_ad->get_author_id()))) {  // NOLINT
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdStorewideUplift_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_storewide_live_uplift_r =
      SPDM_enable_storewide_live_uplift_r(session_data->get_spdm_ctx());
  bool enable_storewide_live_uplift_q =
      SPDM_enable_storewide_live_uplift_q(session_data->get_spdm_ctx());
  auto uplift_bound_map = RankKconfUtil::upliftBoundMap();
  auto storewide_live_uplift_price_r_tail = RankKconfUtil::storewideLiveUpliftPriceRTail();
  auto storewide_live_uplift_price_q_tail = RankKconfUtil::storewideLiveUpliftPriceQTail();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double p1 = p_ad->get_predict_score(PredictType::PredictType_inner_storewide_live_uplift_prob1);
    double p2 = p_ad->get_predict_score(PredictType::PredictType_inner_storewide_live_uplift_prob2);
    double p2_weight = SPDM_storewide_live_uplift_p2_weight(session_data->get_spdm_ctx());
    double price_ratio = 1.0;
    const int64_t& page_id = session_data->get_page_id();
    const int64_t& nature_ad_tag =
        p_ad->Attr(ItemIdx::fd_AUTHOR_nature_ad_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    std::string exp_tag = SPDM_storewide_live_uplift_exp_tag(session_data->get_spdm_ctx());

    // 客户实验打 exp_tag （客户侧实验优先级高于流量侧实验）
    if (SPDM_enable_storewide_live_uplift_author_exp_tag(session_data->get_spdm_ctx())) {
      const auto& author_conf_ptr = RankKconfUtil::storewideLiveUpliftAuthorExpTag();
      if (author_conf_ptr) {
        int32_t div_num = author_conf_ptr->data().div_num();
        const auto& exp_config = author_conf_ptr->data().exp_config();
        if (div_num > 0) {
          std::string author_tail_num = std::to_string(p_ad->get_author_id() % div_num);
          const auto& iter = exp_config.find(author_tail_num);
          if (iter != exp_config.end()) {
            exp_tag = iter->second;
          }
        }
      }
    }

    // 分页面校准 p2
    const auto& p2_page_kconf = RankKconfUtil::storewideLiveUpliftPricePageConf();
    if (p2_page_kconf) {
      const auto& exp_config = p2_page_kconf->data().exp_config();
      if (!exp_config.empty()) {
        const auto& iter = exp_config.find(exp_tag);
        if (iter != exp_config.end()) {
          const auto& coef_map = iter->second.page_coef();
          const auto& iter_coef = coef_map.find(page_id);
          if (iter_coef != coef_map.end()) {
            p2_weight = p2_weight * iter_coef->second;
          }
        }
      }
    }

    if (enable_storewide_live_uplift_r || (storewide_live_uplift_price_r_tail && storewide_live_uplift_price_r_tail->IsOnFor(p_ad->get_author_id()))) {  // NOLINT
      // R 模型
      price_ratio = 1.0 + p1;
    } else if (enable_storewide_live_uplift_q || (storewide_live_uplift_price_q_tail && storewide_live_uplift_price_q_tail->IsOnFor(p_ad->get_author_id()))) {  // NOLINT
      // Q 模型
      price_ratio = 1.0 - p2 * p2_weight;
    } else {
      // R - Q 模型
      price_ratio = 1.0 + p1 - p2 * p2_weight;
    }

    // 分页面校准
    const auto& page_kconf = RankKconfUtil::storewideLiveUpliftPricePageConf();
    if (page_kconf) {
      const auto& exp_config = page_kconf->data().exp_config();
      if (!exp_config.empty()) {
        const auto& iter = exp_config.find(exp_tag);
        if (iter != exp_config.end()) {
          const auto& coef_map = iter->second.page_coef();
          const auto& iter_coef = coef_map.find(page_id);
          if (iter_coef != coef_map.end()) {
            price_ratio = price_ratio * iter_coef->second;
          }
        }
      }
    }
    // 分客户类型校准
    const auto& esp_kconf = RankKconfUtil::storewideLiveUpliftPriceEspConf();
    if (esp_kconf) {
      const auto& exp_config = esp_kconf->data().exp_config();
      if (!exp_config.empty()) {
        const auto& iter = exp_config.find(exp_tag);
        if (iter != exp_config.end()) {
          const auto& coef_map = iter->second.esp_coef();
          const auto& iter_coef = coef_map.find(nature_ad_tag);
          if (iter_coef != coef_map.end()) {
            price_ratio = price_ratio * iter_coef->second;
          }
        }
      }
    }

    // 上下界限制
    auto iter_ratio_upper = uplift_bound_map->find("price_ratio_upper");
    if (iter_ratio_upper != uplift_bound_map->end()) {
      price_ratio = std::min(iter_ratio_upper->second, price_ratio);
    }
    auto iter_ratio_lower = uplift_bound_map->find("price_ratio_lower");
    if (iter_ratio_lower != uplift_bound_map->end()) {
      price_ratio = std::max(iter_ratio_lower->second, price_ratio);
    }

    RANK_DOT_STATS(session_data, price_ratio * 1e3,
                "storewide_uplift_ratio",
                "price_final_ratio",
                absl::StrCat(exp_tag),
                kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()),
                kuaishou::ad::AdEnum_AdSceneOrientedTypeEnum_Name(p_ad->get_scene_oriented_type()));

    return price_ratio;
  };
  FINISH_COMPUTE()
}

void AdStorewideMerchantUplift_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_storewide_merchant_uplift_model_cmd =
      SPDM_enable_storewide_merchant_uplift_model_cmd(session_data->get_spdm_ctx());
  bool enable_storewide_merchant_uplift_flow_exp =
    SPDM_enable_storewide_merchant_uplift_flow_exp(session_data->get_spdm_ctx());
  auto storewide_merchant_uplift_tail = RankKconfUtil::storewideMerchantUpliftTail();

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (p_ad->get_scene_oriented_type() != kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_PRODUCT) {
      return false;
    }
    if ((storewide_merchant_uplift_tail->IsOnFor(p_ad->get_campaign_id()) ||
          enable_storewide_merchant_uplift_flow_exp)
          && enable_storewide_merchant_uplift_model_cmd) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdStorewideMerchantUplift_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double p1_weight = SPDM_storewide_merchant_uplift_p1_weight(session_data->get_spdm_ctx());
  double p2_weight = SPDM_storewide_merchant_uplift_p2_weight(session_data->get_spdm_ctx());
  double bias =  SPDM_storewide_merchant_uplift_bias_discount(session_data->get_spdm_ctx());
  double ratio_upper_bound = SPDM_storewide_merchant_uplift_ratio_discount_upper_bound(session_data->get_spdm_ctx());  // NOLINT
  double ratio_lower_bound = SPDM_storewide_merchant_uplift_ratio_discount_lower_bound(session_data->get_spdm_ctx());  // NOLINT
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double p1 = p_ad->get_predict_score(PredictType::PredictType_inner_storewide_merchant_uplift_prob1);
    double p2 = p_ad->get_predict_score(PredictType::PredictType_inner_storewide_merchant_uplift_prob2);
    double ratio = 1.0 - (p1 * p1_weight - p2 * p2_weight) + bias;

    ratio = std::min(ratio_upper_bound, ratio);
    ratio = std::max(ratio_lower_bound, ratio);

    RANK_DOT_STATS(session_data, ratio * 1e3,
                "storewide_merchant_uplift_ratio",
                "price_final_ratio",
                kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()),
                kuaishou::ad::AdEnum_AdSceneOrientedTypeEnum_Name(p_ad->get_scene_oriented_type()));
    return ratio;
  };
  FINISH_COMPUTE()
}

void MatrixAppPriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
                                       platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT();
  const std::string& app_id = session_data->get_pos_manager_base().GetRequestAppId();
  bool enable_adjust_price_for_matrix_flow =
      SPDM_enable_adjust_price_for_matrix_flow_new(session_data->get_spdm_ctx()) &&
      engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(app_id);
  auto matrix_flow_price_adjust_conf = RankKconfUtil::MatrixFlowPriceAdjust();
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR();
    RANK_DOT_COUNT(session_data, 1, "MatrixAppPriceDiscount_Admit.total_cnt", app_id);
    if (enable_adjust_price_for_matrix_flow && matrix_flow_price_adjust_conf) {
      RANK_DOT_COUNT(session_data, 1,
          "MatrixAppPriceDiscount_Admit.admit_success_cnt", app_id);
      return true;
    }
    return false;
  };
  FINISH_ADMIT();
}

void MatrixAppPriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
                                         ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT();
  const std::string& app_id = session_data->get_pos_manager_base().GetRequestAppId();
  const std::string& matrix_flow_exp_tag =
      SPDM_adjust_price_matrix_flow_exp_tag(session_data->get_spdm_ctx());
  auto matrix_flow_price_adjust_conf = RankKconfUtil::MatrixFlowPriceAdjust();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR();
    auto ocpx_action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    auto campaign_type = kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type());
    auto bid_type = kuaishou::ad::AdEnum::BidType_Name(p_ad->get_bid_type());
    auto key = absl::StrCat(matrix_flow_exp_tag, "|", app_id, "|", ocpx_action_type, "|",
                              campaign_type, "|", bid_type);
    const auto& iter = matrix_flow_price_adjust_conf->find(key);
    double ratio = 1.0;
    if (iter != matrix_flow_price_adjust_conf->end()) {
      ratio = iter->second;
      RANK_DOT_STATS(session_data, ratio * 1000,
          "MatrixAppPriceDiscount_Compute.ratio", app_id);
    }
    return ratio;
  };

  FINISH_COMPUTE();
}


void AdMerchantPriceDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_merchant_price_strategy_ = SPDM_enable_merchant_price_ratio(session_data->get_spdm_ctx());
  // 投放配置
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<
      ::ks::engine_base::kconf::AdMerchantConf>> ad_merchant_conf_ =
      engine_base::AdKconfUtil::adMerchantConf();
  int64_t req_price_tag = factor_info.rank_factor_type_;

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()

    // 全站直播半互斥计费打折跳过
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE &&
        p_ad->get_storewide_incompatible_type() != 1) {
      return false;
    }
    if (!enable_merchant_price_strategy_) {
      return false;
    }
    if (!p_ad->Is(AdFlag::is_inner_loop_ad)) {
      return false;
    }
    if (nullptr == ad_merchant_conf_) {
      return false;
    }

    return true;
  };
  FINISH_ADMIT()
}

void UpdateDiscountBudgetOverTag(ks::platform::AddibleRecoContextInterface* context,
    bool enable_innerloop_discount_control,
    absl::flat_hash_map<int64_t, bool> *innerloop_discount_budget_over_tag) {
  auto innerloop_discount_tag_budget = RankKconfUtil::innerloopDiscountTagBudget();
  auto tag_discount_cost_conf = RankKconfUtil::tagDiscountCost();
  if (!enable_innerloop_discount_control) {
    return;
  }
  (*innerloop_discount_budget_over_tag).clear();
  if (innerloop_discount_tag_budget != nullptr && tag_discount_cost_conf != nullptr) {
    auto values = tag_discount_cost_conf->data().values();
    for (auto &iter : *innerloop_discount_tag_budget) {
      std::string tag = iter.first;
      int64 tag_discount_budget = iter.second;
      const auto& iter2 = values.find(tag);
      if (iter2 == values.end()) {
        continue;
      }
      const auto& value = iter2->second;
      int64_t price_tag = value.price_tag();
      int64_t tag_discount_cost = value.tag_discount_cost();
      int64_t update_timestamp = value.update_timestamp();
      int64_t diff_time = utility::GetTimestampNoDiff() / 1000000 - update_timestamp;
      LOG_EVERY_N(INFO, 100000) << "debug innerloop_discount_cost:"
                                << ", tag: " << tag
                                << ", tag_discount_budget: " << tag_discount_budget
                                << ", update_timestamp: " << update_timestamp
                                << ", time_now: " << utility::GetTimestampNoDiff() / 1000000
                                << ", diff_time" << diff_time
                                << ", tag_discount_cost: " << tag_discount_cost;
      if (absl::StrCat(price_tag) != tag || diff_time > 60 * 60) {
        continue;
      }
      if (tag_discount_cost > tag_discount_budget) {
        (*innerloop_discount_budget_over_tag)[price_tag] = true;
      }
    }
  }
}

void AdMerchantPriceDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  // 投放配置
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<
      ::ks::engine_base::kconf::AdMerchantConf>> ad_merchant_conf_ =
      engine_base::AdKconfUtil::adMerchantConf();
  std::shared_ptr<std::set<int64_t>> storewide_cost_order_author =
      engine_base::AdKconfUtil::storewideCostRatioOrderAuthorTail();
  bool enable_storewide_cost_align =
      engine_base::SPDM_enable_storewide_cost_align(session_data->get_spdm_ctx());
  int64_t req_price_tag = factor_info.rank_factor_type_;
  bool is_holdout_live_storewide_ecpc =
      SPDM_is_holdout_live_storewide_ecpc(session_data->get_spdm_ctx());
  bool is_holdout_photo_storewide_ecpc =
      SPDM_is_holdout_photo_storewide_ecpc(session_data->get_spdm_ctx());
  auto storewide_price_efficiency = RankKconfUtil::storewidePriceEfficiencyExp();
  bool enable_price_efficiency = SPDM_enablePriceEfficiencyExp();

  // 内循环打折预算控制
  absl::flat_hash_map<int64_t, bool> innerloop_discount_budget_over_tag;
  bool enable_innerloop_discount_control = RankKconfUtil::enableInnerloopDiscountControl();
  UpdateDiscountBudgetOverTag(context, enable_innerloop_discount_control,
      &innerloop_discount_budget_over_tag);

  // 打折优化
  bool enable_discount_roi_opt = SPDM_enable_discount_roi_opt(session_data->get_spdm_ctx());
  double discount_roi_opt_ratio = SPDM_discount_roi_opt_ratio(session_data->get_spdm_ctx());
  auto discount_roi_opt_state_name_set = engine_base::AdKconfUtil::discountRoiOptStateNameList();
  bool enable_discount_score_opt = SPDM_enable_discount_score_opt(session_data->get_spdm_ctx());
  auto discount_roi_opt_item_type_set = engine_base::AdKconfUtil::discountRoiOptItemTypeList();
  double discount_adjust_ratio = SPDM_discount_adjust_ratio(session_data->get_spdm_ctx());
  bool enable_discount_ratio_opt = SPDM_enable_discount_ratio_opt(session_data->get_spdm_ctx());
  bool enable_discount_cpm_gpm_opt = SPDM_enable_discount_cpm_gpm_opt(session_data->get_spdm_ctx());
  bool enable_discount_roi_opt_v3 = SPDM_enable_discount_roi_opt_v3(session_data->get_spdm_ctx());
  double price_adjust_ratio = SPDM_price_adjust_ratio(session_data->get_spdm_ctx());
  double cpm_gpm_price_adjust_ratio = SPDM_cpm_gpm_price_adjust_ratio(session_data->get_spdm_ctx());

  // 内循环 cid 顶价打折单独实验系数
  bool enable_inner_cid_prod_price_ratio =
    SPDM_enable_inner_cid_prod_price_ratio(session_data->get_spdm_ctx());
  double inner_cid_prod_price_ratio = SPDM_inner_cid_prod_price_ratio(session_data->get_spdm_ctx());
  auto enable_inner_cid_price_tag_prod_tail = RankKconfUtil::enableInnerCIDPriceTagProdTail();
  auto inner_cid_price_tag_prod_map = RankKconfUtil::innerCIDPriceTagProdMap();

  // 内循环 cid 补贴效率优化
  bool enable_cid_rank_discount_exp = SPDM_enable_cid_rank_discount_exp(session_data->get_spdm_ctx());

  // 内循环 cid 低 u 高 c luhc 探索
  bool enable_cid_luhc_discount_exp = SPDM_enable_cid_luhc_discount_exp(session_data->get_spdm_ctx());
  double inner_cid_luhc_price_ratio = SPDM_inner_cid_luhc_price_ratio(session_data->get_spdm_ctx());
  std::shared_ptr<std::set<std::string>> inner_cid_luhc_u_list = RankKconfUtil::innerCIDluhcUlist();
  std::shared_ptr<std::set<std::string>> inner_cid_luhc_c_list = RankKconfUtil::innerCIDluhcClist();

  auto is_hosting = [&](auto& p_ad) -> bool {
    return (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_SINGLE_GOODS ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_CID_SUPER_PRODUCT ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_GOODS ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_OPTIONAL_GOODS);
  };

  // 尾号/流量实验开关开启时， 且为内循环 cid 相关目标 且生效在 tag=78 时生效
  auto is_cid_discount = [&](auto& p_ad) -> bool {
    return (req_price_tag == 78)
           && (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID
               || p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)
           && (enable_inner_cid_prod_price_ratio
               || enable_inner_cid_price_tag_prod_tail);
  };

  // R3 人群
  absl::flat_hash_map<int64_t, bool> author_crowd_info;
  const auto& strategy_crowd_info =
      session_data->get_rank_request()->ad_request().ad_user_info().strategy_crowd_info();
  author_crowd_info.clear();
  for (const auto& crowd_info : strategy_crowd_info) {
    int64_t author_id = crowd_info.author_id();
    for (const auto& crowd_tag : crowd_info.tag()) {
      if (crowd_tag == kuaishou::ad::StrategyCrowdTag::BRAND_OFFLINE_R3) {
        author_crowd_info.insert(std::make_pair(author_id, true));
        break;
      }
    }
  }

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    std::string state_name;
    std::vector<std::string> state_name_vec;
    std::string first_industry_id = absl::StrCat(p_ad->get_industry_parent_id_v3());
    std::string second_industry_id = absl::StrCat(p_ad->get_second_industry_id_v5());
    std::string author_id = absl::StrCat(p_ad->get_author_id());
    std::string photo_id = absl::StrCat(p_ad->get_photo_id());
    std::string account_id = absl::StrCat(p_ad->get_account_id());
    std::string corporation_name = std::string(
      p_ad->Attr(ItemIdx::fd_ACCOUNT_corporation_name).GetStringValue(p_ad->AttrIndex()).value_or(""));

    const std::string& ocpx_action_type =
        kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    const std::string& scene_oriented_type = kuaishou::ad::AdEnum_AdSceneOrientedTypeEnum_Name(
        static_cast<kuaishou::ad::AdEnum::AdSceneOrientedTypeEnum>(
        p_ad->get_scene_oriented_type()));
    const std::string& speed_type = kuaishou::ad::AdEnum_SpeedType_Name(
        static_cast<kuaishou::ad::AdEnum::SpeedType>(p_ad->get_speed()));
    const std::string& bid_strategy = kuaishou::ad::AdEnum_BidStrategy_Name(
        static_cast<kuaishou::ad::AdEnum::BidStrategy>(p_ad->get_bid_strategy()));
    const std::string& item_type = kuaishou::ad::AdEnum_ItemType_Name(p_ad->get_item_type());
    const std::string& account_type = kuaishou::ad::AdEnum_AdDspAccountType_Name(p_ad->get_account_type());
    const std::string& page_id = absl::StrCat(session_data->get_page_id());
    int64_t is_live_cold_start =
        p_ad->Attr(ItemIdx::fd_LIVE_inner_live_coldstart_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    int64_t is_author_fans = p_ad->get_is_fan_follow() ? 1 : 0;
    int64_t is_crowd_tag_r3 = author_crowd_info.find(p_ad->get_author_id()) != author_crowd_info.end();
    int32_t seller_category = p_ad->get_seller_category();
    int32_t native_degree_tag =
      p_ad->Attr(ItemIdx::fd_PHOTO_native_degree_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    // 全站 holdout 实验
    if (is_holdout_live_storewide_ecpc && p_ad->get_scene_oriented_type() == 21
        || is_holdout_photo_storewide_ecpc && p_ad->get_scene_oriented_type() == 24) {
        return 1.0;
    }
    bool is_storewide_cost_order_author = false;
    if (storewide_cost_order_author != nullptr) {
      is_storewide_cost_order_author =
          storewide_cost_order_author->find(p_ad->get_author_id()) != storewide_cost_order_author->end() ||
          storewide_cost_order_author->find(p_ad->get_author_id() % 100) !=
          storewide_cost_order_author->end();
    }
    is_storewide_cost_order_author = is_storewide_cost_order_author || enable_storewide_cost_align;
    FindStateName(session_data, p_ad,
        first_industry_id, second_industry_id, author_id, photo_id,
        account_id, corporation_name, &state_name_vec,
        is_storewide_cost_order_author);
    if (state_name_vec.size() == 0) {
      return 1.0;
    }

    double price_ratio = 1.0;
    bool get_price_ratio_suc = false;
    RANK_DOT_STATS(session_data, static_cast<int64_t>(price_ratio * 100),
        "DiscountScoreOptComTest",
        absl::StrCat(p_ad->get_discount_score_status()));
    for (auto sn : state_name_vec) {
      if (enable_price_efficiency && storewide_price_efficiency &&
          storewide_price_efficiency->IsOnFor(p_ad->get_campaign_id())
          && p_ad->get_scene_oriented_type() == 21 && req_price_tag == 60) {
        sn = sn + "_efficiency";
      }
      get_price_ratio_suc = ExecGetPriceRatio(
          enable_innerloop_discount_control,
          innerloop_discount_budget_over_tag,
          ad_merchant_conf_,
          req_price_tag,
          sn,
          ocpx_action_type,
          scene_oriented_type,
          speed_type,
          bid_strategy,
          item_type,
          p_ad->get_storewide_incompatible_type(),
          account_type,
          is_live_cold_start,
          page_id,
          is_author_fans,
          is_crowd_tag_r3,
          seller_category,
          native_degree_tag,
          &price_ratio);
      if (get_price_ratio_suc) {
        state_name = sn;
        break;
      }
    }
    if (!get_price_ratio_suc) {
      return 1.0;
    }

    if (is_cid_discount(p_ad)) {
      // cid 额外调整 tag 顶价系数流量实验
      double cid_exp_ratio = 1.0;
      if (enable_inner_cid_prod_price_ratio) {
        cid_exp_ratio *= inner_cid_prod_price_ratio;
      }

      // cid 额外调整 tag 顶价系数尾号实验
      if (enable_inner_cid_price_tag_prod_tail && inner_cid_price_tag_prod_map) {
        auto key = (is_hosting(p_ad) ? p_ad->get_campaign_id() : p_ad->get_unit_id()) % 100;
        auto iter = inner_cid_price_tag_prod_map->find(absl::StrCat(key));
        if (iter != inner_cid_price_tag_prod_map->end()) {
          cid_exp_ratio *= iter->second;
        }
      }

      if (cid_exp_ratio != 1.0) {
        price_ratio *= cid_exp_ratio;
      }
    }

    // cid 补贴效率优化--个性化打折系数
    if (enable_cid_rank_discount_exp) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID) {
            auto bidservice_bid_ratio = p_ad->Attr(ItemIdx::bidservice_bid_ratio)
                                        .GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
            if (bidservice_bid_ratio > 0.0) {
              price_ratio = 1.0 / bidservice_bid_ratio;
            }
        }
     }

    // 低 u 高 c  cid 人群打折实验
    if (enable_cid_luhc_discount_exp &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID)) {
          std::string consum_power_tag =
              session_data->get_rank_request()->ad_request().ad_user_info().consum_power_tag();
          std::string buyer_effective_type =
              session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
          if (inner_cid_luhc_u_list && inner_cid_luhc_u_list->size() > 0 &&
              inner_cid_luhc_u_list->find(buyer_effective_type) != inner_cid_luhc_u_list->end() &&
              inner_cid_luhc_c_list && inner_cid_luhc_c_list->size() > 0 &&
              inner_cid_luhc_c_list->find(consum_power_tag) != inner_cid_luhc_c_list->end()) {
                price_ratio *= inner_cid_luhc_price_ratio;
              }
        }

    RANK_DOT_STATS(session_data, static_cast<int64_t>(price_ratio * 100),
                   absl::StrCat("AdMerchantBidServiceEcpc.", state_name),
                   ocpx_action_type,
                   scene_oriented_type,
                   speed_type,
                   item_type,
                   absl::StrCat(req_price_tag));

    if (enable_discount_roi_opt) {
      // 白名单控制
      if ((discount_roi_opt_state_name_set->count(state_name) > 0) &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::CID_ROAS &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID) {
          if (p_ad->get_discount_roi_status() == 1) {
            price_ratio *= discount_roi_opt_ratio;
          }
          if (p_ad->get_discount_roi_status() == 2) {
            price_ratio = 1.0;
          }
        }
    }

    if (enable_discount_score_opt) {
      std::string ad_type = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
      // 白名单控制
      if (p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::CID_ROAS &&
          p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID &&
          (discount_roi_opt_state_name_set->count(state_name) > 0) &&
          (discount_roi_opt_item_type_set->count(ad_type) > 0)) {
          if (p_ad->get_discount_score_status() == 1) {
            price_ratio = 1.0;
          }
          if (p_ad->get_discount_score_status() == 2 && p_ad->get_makeup_score_opt_ratio() != 0.0) {
            double discount_roi_score_ratio = 1.0/p_ad->get_makeup_score_opt_ratio();
            if (enable_discount_ratio_opt) {
              discount_roi_score_ratio = std::min(1.0,
                discount_roi_score_ratio * discount_adjust_ratio);
            }
            price_ratio *= discount_roi_score_ratio;
          }
          RANK_DOT_STATS(session_data, static_cast<int64_t>(price_ratio * 100),
              absl::StrCat("DiscountScoreOptCom.", state_name),
              absl::StrCat(p_ad->get_discount_score_status()));
      }
    }

    if (enable_discount_cpm_gpm_opt) {
      // 白名单控制
      if (p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::CID_ROAS &&
          p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID &&
          (discount_roi_opt_state_name_set->count(state_name) > 0)) {
        if (p_ad->get_discount_cpm_gpm_status() == 1) {
          price_ratio = 1.0;
        }
        if (p_ad->get_discount_cpm_gpm_status() == 2 && p_ad->get_makeup_cpm_gpm_ratio() != 0.0) {
          double discount_cpm_gpm_ratio = 1.0 / p_ad->get_makeup_cpm_gpm_ratio();
          discount_cpm_gpm_ratio = std::min(1.0,
            discount_cpm_gpm_ratio * cpm_gpm_price_adjust_ratio);
          price_ratio *= discount_cpm_gpm_ratio;
        }
      }
    }

    if (enable_discount_roi_opt_v3) {
      // 白名单控制
      if (p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::CID_ROAS &&
          p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID &&
          (discount_roi_opt_state_name_set->count(state_name) > 0) &&
          (discount_roi_opt_item_type_set->count(item_type) > 0)) {
        if (p_ad->get_cpm_discount_status() == 1) {
          price_ratio = 1.0;
        }
        if (p_ad->get_cpm_discount_status() == 2 && p_ad->get_cpm_makeup_ratio() != 0.0) {
          double discount_cpm_ratio = 1.0/p_ad->get_cpm_makeup_ratio();
          discount_cpm_ratio = std::min(1.0,
            discount_cpm_ratio * price_adjust_ratio);
          price_ratio *= discount_cpm_ratio;
        }
      }
    }

    return price_ratio;
  };
  FINISH_COMPUTE()
}

void InnerWhiteAccountDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_inner_white_account_discount =
      SPDM_enable_inner_white_account_discount(session_data->get_spdm_ctx());

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 全站直播半互斥计费打折跳过
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE &&
        p_ad->get_storewide_incompatible_type() != 1) {
      return false;
    }
    if (!enable_inner_white_account_discount) {
      return false;
    }
    if (!p_ad->Is(AdFlag::is_inner_loop_ad)) {
      return false;
    }
    if (p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI) {
      return false;
    }
    return true;
  };
  FINISH_ADMIT()
}

void InnerWhiteAccountDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  // 投放配置
  const auto account_discount_ratio_map = RankKconfUtil::InnerWhiteAccountDiscountRatio();

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    std::string author_id = absl::StrCat(p_ad->get_author_id());
    std::string account_id = absl::StrCat(p_ad->get_account_id());
    double price_ratio = 1.0;
    // 先查找 author_id
    auto it = account_discount_ratio_map->find(author_id);
    if (it != account_discount_ratio_map->end()) {
      price_ratio = it->second;
      RANK_DOT_STATS(session_data, price_ratio * 1000, "inner_white_account_discount",
                     "InnerWhiteAccountDiscount_Compute", author_id);
    }
    // 再查找 account_id
    it = account_discount_ratio_map->find(account_id);
    if (it != account_discount_ratio_map->end()) {
      price_ratio = it->second;
      RANK_DOT_STATS(session_data, price_ratio * 1000, "inner_white_account_discount",
                     "InnerWhiteAccountDiscount_Compute", account_id);
    }
    return price_ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceClientAiLiveAdRerankNew_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_client_ai_live_reduce_price_all =
      SPDM_enable_client_ai_live_reduce_price_all(session_data->get_spdm_ctx());
  auto enable_fix_client_ai_live_reduce_price_version =
      SPDM_enable_fix_client_ai_live_reduce_price_version(session_data->get_spdm_ctx());
  auto enable_client_ai_p1_live_ad_price_reduce =
      SPDM_enable_client_ai_p1_live_ad_price_reduce(session_data->get_spdm_ctx());
  auto enable_client_ai_add_is_high_value =  // 判断是否是端智能高价值人群
      SPDM_enable_client_ai_add_is_high_value(session_data->get_spdm_ctx());
  int64_t client_ai_use_cpm_or_rank_benefit =
      SPDM_client_ai_high_value_use_cpm_or_rankbenifit(session_data->get_spdm_ctx());
  auto enable_client_ai_is_high_value_use_threshold =
      SPDM_enable_client_ai_is_high_value_use_threshold(session_data->get_spdm_ctx());
  auto client_ai_high_value_use_threshold_value =
      SPDM_client_ai_high_value_use_threshold_value(session_data->get_spdm_ctx());
  auto enable_client_ai_is_high_value_use_profile =
      SPDM_enable_client_ai_is_high_value_use_profile(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()

    // 版本控制
    if (enable_fix_client_ai_live_reduce_price_version) {
      if (engine_base::CompareAppVersion(session_data->get_app_version(), "11.10.40") < 0) {
        return false;
      }
    } else {
      if (engine_base::CompareAppVersion(
          session_data->get_rank_request()->ad_request().reco_user_info().app_version(),
          "11.10.40") < 0) {
        return false;
      }
    }

    // 非精选页 or 非极速版发现页，跳过
    if (session_data->get_sub_page_id() != 11001001 && session_data->get_sub_page_id() != 10011001) {
      return false;
    }
    // 判断是否是端智能高价值人群
    bool is_client_high_value = false;
    if (enable_client_ai_is_high_value_use_threshold) {
      if (client_ai_use_cpm_or_rank_benefit == 1) {
        if (client_ai_high_value_use_threshold_value <= p_ad->get_cpm()) {
          is_client_high_value = true;
        } else {
          is_client_high_value = false;
        }
      } else {
        if (client_ai_high_value_use_threshold_value <= p_ad->get_rank_benifit()) {
          is_client_high_value = true;
        } else {
          is_client_high_value = false;
        }
      }
    } else if (enable_client_ai_is_high_value_use_profile) {
       // front 传字段
       is_client_high_value =
           session_data->get_rank_request()->ad_request().client_ai_is_high_value_user();
    }

    if (p_ad->Is(AdFlag::is_live_ad) && enable_client_ai_p1_live_ad_price_reduce) {
      if (enable_client_ai_live_reduce_price_all) {
        return true;
      }
      if (session_data->get_is_live_rerank_req()) {
        return true;
      } else if (enable_client_ai_add_is_high_value && is_client_high_value) {
        return true;
      } else {
        return false;
      }
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceClientAiLiveAdRerankNew_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto client_ai_p1_live_ad_price_reduce_ratio =  // 正常请求直播广告下发计费下调系数
      SPDM_client_ai_p1_live_ad_price_reduce_ratio(session_data->get_spdm_ctx());
  auto client_ai_p1_rerank_live_ad_price_reduce_ration =  // 直播 rerank 广告下发计费下调系数
      SPDM_client_ai_p1_rerank_live_ad_price_reduce_ration(session_data->get_spdm_ctx());
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    if (session_data->get_is_live_rerank_req()) {
      ratio = client_ai_p1_rerank_live_ad_price_reduce_ration;
    } else {
      ratio = client_ai_p1_live_ad_price_reduce_ratio;
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceClientAiPhotoAdRerankNew_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_client_ai_photo_ad_price_reduce =
      SPDM_enable_client_ai_photo_ad_price_reduce(session_data->get_spdm_ctx());
  auto enable_client_ai_photo_reduce_price_all =
      SPDM_enable_client_ai_photo_reduce_price_all(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 开关
    if (!enable_client_ai_photo_ad_price_reduce) {
      return false;
    }
    // 版本控制
    if (engine_base::CompareAppVersion(session_data->get_app_version(), "11.10.40") < 0) {
      return false;
    }
    // 非精选页 or 非极速版发现页，跳过
    if (session_data->get_sub_page_id() != 11001001 && session_data->get_sub_page_id() != 10011001) {
      return false;
    }
    if (p_ad->Is(AdFlag::IsHardAd) || p_ad->Is(AdFlag::is_photo_or_p2l)) {
      if (session_data->get_rank_request()->ad_request().is_photo_rerank_req()) {
        return true;
      }
      if (enable_client_ai_photo_reduce_price_all) {
        return true;
      }
    }
    return false;
  };
  FINISH_ADMIT()
}


void AdjustPriceClientAiPhotoAdRerank_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto client_ai_photo_ad_price_reduce_ratio =  // 正常请求硬广视频广告下发计费下调系数
      SPDM_client_ai_photo_ad_price_reduce_ratio(session_data->get_spdm_ctx());
  auto client_ai_rerank_photo_ad_price_reduce_ratio =  // 短视频 rerank 广告下发计费下调系数
      SPDM_client_ai_rerank_photo_ad_price_reduce_ratio(session_data->get_spdm_ctx());
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    if (session_data->get_rank_request()->ad_request().is_photo_rerank_req()) {
      ratio = client_ai_rerank_photo_ad_price_reduce_ratio;
    } else {
      ratio = client_ai_photo_ad_price_reduce_ratio;
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceClientAiP0RerankNew_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_client_ai_p0_adjust_price =
      SPDM_enable_client_ai_p0_adjust_price(session_data->get_spdm_ctx());
  auto enable_fix_client_ai_live_reduce_price_version =
      SPDM_enable_fix_client_ai_live_reduce_price_version(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 版本控制
    if (enable_fix_client_ai_live_reduce_price_version) {
      if (engine_base::CompareAppVersion(session_data->get_app_version(), "11.10.40") < 0) {
        return false;
      }
    } else {
        if (engine_base::CompareAppVersion(
            session_data->get_rank_request()->ad_request().reco_user_info().app_version(),
            "11.10.20") < 0) {
          return false;
        }
    }
    // 非精选页 or 非极速版发现页，跳过
    if (session_data->get_sub_page_id() != 11001001 && session_data->get_sub_page_id() != 10011001) {
      return false;
    }
    if (enable_client_ai_p0_adjust_price) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceClientAiP0RerankNew_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto client_ai_p0_rerank_adjust_price_ratio =
      SPDM_client_ai_p0_rerank_adjust_price_ratio(session_data->get_spdm_ctx());
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    return client_ai_p0_rerank_adjust_price_ratio;
  };
  FINISH_COMPUTE()
}

void UnifyHardBillingSeparate_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto close_billing_separate = SPDM_close_billing_separate(session_data->get_spdm_ctx());
  auto enable_inner_new_customer_bs =
      SPDM_enable_inner_new_customer_bs(session_data->get_spdm_ctx());
  auto enable_billing_separate_rewarded_video =
      SPDM_enable_billing_separate_rewarded_video(session_data->get_spdm_ctx());
  auto enable_fanstop_skip_bs_fix =
      SPDM_enable_fanstop_skip_bs_fix(session_data->get_spdm_ctx());
  auto enable_migrate_new_cust_bs =
      SPDM_enable_migrate_new_cust_bs(session_data->get_spdm_ctx());
  auto enable_gyk_item_card_bs_spdm =
    SPDM_enable_gyk_item_card_bs_spdm(session_data->get_spdm_ctx());
  auto enable_storewide_live_bs_ab = SPDM_enable_storewide_live_bs_ab(session_data->get_spdm_ctx());
  auto item_card_photo_bs_tail = RankKconfUtil::itemCardPhotoBsUnitTail();
  auto pc_live_new_bs_tail = RankKconfUtil::pcLiveAllPageBsUnitTail();
  auto new_cust_bs_tail = RankKconfUtil::newCustomerBsTails();
  auto storewide_bs_campaign_tail = RankKconfUtil::storewideBsCampaignTail();
  auto storewide_bs_order_author_tail = engine_base::AdKconfUtil::storewideBillingSepOrderAuthorTail();
  bool enable_storewide_bs_align =
      engine_base::SPDM_enable_storewide_bs_align(session_data->get_spdm_ctx());
  auto enable_t7_roi_skip_bs = SPDM_enableT7ROISkipBillingSeparate();
  bool enable_rank_bs_exp = SPDM_enable_rank_bs_exp(session_data->get_spdm_ctx());
  bool enable_rank_bs_exp_inner_roi = SPDM_enable_rank_bs_exp_inner_roi(session_data->get_spdm_ctx());
  auto t7_roi_skip_bs_unit_tail =
      RankKconfUtil::t7ROISkipBillingSeparateUnitTail();
  auto enable_lsp_storewide_skip_bs = SPDM_enable_lsp_storewide_skip_bs(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    auto& ad = *p_ad;
    // 只处理硬广
    if (!p_ad->Is(AdFlag::IsHardAd)) {
      return false;
    }
    // (dingyiming05) 计费摸底实验
    if (enable_rank_bs_exp && session_data->get_is_thanos_mix_request() &&
        p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
       !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) && (!(p_ad->get_auto_roas() > 0) ||
       (p_ad->Is(AdFlag::is_inner_loop_ad) && enable_rank_bs_exp_inner_roi)) &&
        !RankKconfUtil::skipBsExpWhiteAccount()->count(p_ad->get_account_id())) {
      return p_ad->get_is_billing_separate_account();
    }
    // 全站直播半互斥计费分离跳过
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE &&
        p_ad->get_storewide_incompatible_type() != 1) {
      return false;
    }

    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI &&
        enable_t7_roi_skip_bs &&
        nullptr != t7_roi_skip_bs_unit_tail &&
        t7_roi_skip_bs_unit_tail->IsOnFor(p_ad->get_unit_id())) {
          return false;
    }
    // 本地全站跳过计费分离
    if (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE &&
        enable_lsp_storewide_skip_bs) {
      return false;
    }

    // 新客计费分离新实验
    if (enable_inner_new_customer_bs && p_ad->Is(AdFlag::is_inner_loop_ad) &&
        !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) && p_ad->get_smb_level_tag() == 4 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        new_cust_bs_tail && new_cust_bs_tail->count(p_ad->get_account_id() % 100) > 0) {
      if (enable_migrate_new_cust_bs) {
        return false;
      }
      return true;
    }

    bool enable_pc_live_new_bs = p_ad->Is(AdFlag::is_merchant_live) &&
           p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
           p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
           ((p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED) &&
             pc_live_new_bs_tail &&
            pc_live_new_bs_tail->count(p_ad->get_unit_id() % 100) > 0);
    // pc 控成本直播新计费分离实验
    if (enable_pc_live_new_bs) {
      return true;
    }
    bool enable_pc_photo_roas_new_bs = p_ad->Is(AdFlag::is_merchant_photo) &&
           (p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST ||
            p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_COST_CAP) &&
           p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
            p_ad->get_project_ocpx_action_type() ==  kuaishou::ad::AD_MERCHANT_ROAS);
    // pc 控成本短视频 ROAS 新计费分离实验
    if (enable_pc_photo_roas_new_bs) {
      return true;
    }
    bool enable_merchant_storewide_new_bs = p_ad->Is(AdFlag::is_merchant_photo) &&
           p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
            ad.get_scene_oriented_type() == 24);
    // 商品全站新计费分离实验
    if (enable_merchant_storewide_new_bs) {
      return true;
    }

    bool enable_gyk_item_card_bs =
        session_data->get_pos_manager_base().IsGuessYouLike() &&
        p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
        p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
        p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP && item_card_photo_bs_tail &&
        item_card_photo_bs_tail->count(p_ad->get_unit_id() % 100) > 0;
    if (enable_gyk_item_card_bs_spdm && enable_gyk_item_card_bs) {
      return true;
    }

    // 加速探索增量计费跳过
    if (p_ad->Attr(ItemIdx::is_increment_explore).GetIntValue(p_ad->AttrIndex()).value_or(0)) {
      return false;
    }

    // 全站直播全互斥订单部分跳过
    bool is_storewide_bs_order_author = false;
    if (storewide_bs_order_author_tail != nullptr) {
      is_storewide_bs_order_author =
          storewide_bs_order_author_tail->find(p_ad->get_author_id()) !=
          storewide_bs_order_author_tail->end() ||
          storewide_bs_order_author_tail->find(p_ad->get_author_id() % 100) !=
          storewide_bs_order_author_tail->end();
    }
    is_storewide_bs_order_author = is_storewide_bs_order_author || enable_storewide_bs_align;
    // 全站直播默认跳过
    if (p_ad->Is(AdFlag::is_merchant_live) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
        (is_storewide_bs_order_author && (p_ad->get_scene_oriented_type() ==
        kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE)))) {
      if (p_ad->get_storewide_incompatible_type() == 1 &&
          (enable_storewide_live_bs_ab || (storewide_bs_campaign_tail &&
          storewide_bs_campaign_tail->count(p_ad->get_author_id() % 100) > 0))) {
        // 全互斥命中流量侧或作者侧实验，则走计费分离
        return true;
      }
      return false;
    }

    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPM &&
         ad.get_ad_source_type() == kuaishou::ad::AdSourceType::DSP) {
      return false;
    }
    if (ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA &&
        (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
        ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE)) {
      return false;
    }

    if (ad.get_unit_source_type() == kuaishou::ad::AdEnum_UnitSourceType_UNIT_SOURCE_TYPE_FURIOUS
          && ad.get_bid_type() == kuaishou::ad::AdEnum_BidType_CPA) {
      return false;
    }

    if (close_billing_separate && !p_ad->get_is_account_bidding()) {
      return false;
    }
    if (session_data->get_is_rewarded() && !p_ad->get_is_account_bidding() &&
        !enable_billing_separate_rewarded_video) {
      return false;
    }

    if (p_ad->get_bid_type() != kuaishou::ad::AdEnum::OCPM_DSP) {
      return false;
    }

    // 账户调价
    if (p_ad->get_is_account_bidding()) {
      return true;
    }

    // 排序标记
    if (p_ad->get_is_billing_separate_account()) {
      return true;
    }

    // 粉条自定义出价 temu 账户
    if (p_ad->get_account_type()
         == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) {
      auto bid_strategy = p_ad->get_bid_strategy();
      if (bid_strategy == kuaishou::ad::AdEnum_BidStrategy_CUSTOM_BID_STRATEGY) {
        if (ad.get_price() < 1) {
          return false;
        }
        return true;
      }
      if (enable_fanstop_skip_bs_fix) {
        return false;
      }
    }

    return true;
  };
  FINISH_ADMIT()
}

void UnifyHardBillingSeparate_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto billing_separate_price_boost_base =
      session_data->get_spdm_ctx().TryGetDouble("billing_separate_price_boost_base", 0.1);
  auto enable_inner_smb_bid_unit_price = true;
  auto enable_inner_smb_bid_unit_price_skip_all = true;
  auto enable_storewide_live_bs_ab = SPDM_enable_storewide_live_bs_ab(session_data->get_spdm_ctx());
  auto enable_storewide_live_bs_ab_v2 = SPDM_enable_storewide_live_bs_ab_v2(session_data->get_spdm_ctx());
  auto enable_storewide_live_skip_bs_ab = SPDM_enable_storewide_live_skip_bs_ab(session_data->get_spdm_ctx());
  auto enable_storewide_live_bs_control = SPDM_enable_storewide_live_bs_control(session_data->get_spdm_ctx());
  auto storewide_live_bs_overcharge_ratio =
      SPDM_storewide_live_bs_overcharge_ratio(session_data->get_spdm_ctx());
  auto storewide_live_bs_discount_ratio = SPDM_storewide_live_bs_discount_ratio(session_data->get_spdm_ctx());
  auto enable_gyk_item_card_bs_spdm =
    SPDM_enable_gyk_item_card_bs_spdm(session_data->get_spdm_ctx());
  auto item_card_photo_bs_tail = RankKconfUtil::itemCardPhotoBsUnitTail();
  auto gyk_item_card_bs_ratio = SPDM_gyk_item_card_bs_ratio(session_data->get_spdm_ctx());
  auto t7_roas_skip_bs_tails = RankKconfUtil::t7roasSkipBillingSeparateUnitTail();
  auto skip_bs_autobid_price_ratio_tails = RankKconfUtil::skipBsAutobidPriceRatioUnitTail();
  auto smb_skip_tails = RankKconfUtil::smbSkipBillingSeparateUserTail();
  auto new_cust_bs_tail = RankKconfUtil::newCustomerBsTails();
  auto smb_skip_account_tails = RankKconfUtil::smbSkipBillingSeparateAccountTail();
  auto storewide_bs_campaign_tail = RankKconfUtil::storewideBsCampaignTail();
  auto storewide_bs_order_author_tail = engine_base::AdKconfUtil::storewideBillingSepOrderAuthorTail();
  bool enable_storewide_bs_align =
      engine_base::SPDM_enable_storewide_bs_align(session_data->get_spdm_ctx());
  auto live_hosting_bs_tail = RankKconfUtil::liveHostingBsTail();
  auto enable_live_hosting_skip_bid_bs_spdm =
    SPDM_enable_live_hosting_skip_bid_bs_spdm(session_data->get_spdm_ctx());
  auto billing_separate_lower =
      session_data->get_spdm_ctx().TryGetDouble("billing_separate_lower", 0.0);
  auto billing_separate_upper =
      session_data->get_spdm_ctx().TryGetDouble("billing_separate_upper", 4.0);
  auto enable_bs_dynamix_ratio =
      SPDM_enable_bs_dynamix_ratio(session_data->get_spdm_ctx());
  bool enable_inner_smb_account_price_skip_all = true;
  auto enable_inner_new_customer_bs =
      SPDM_enable_inner_new_customer_bs(session_data->get_spdm_ctx());
  auto enable_inner_new_customer_bs_backup =
      SPDM_enable_inner_new_customer_bs_backup(session_data->get_spdm_ctx());
  auto new_cust_bs_weight_constant = 1.0;
  double separate_billing_ratio_lower = SPDM_hard_separate_billing_ratio_lower(session_data->get_spdm_ctx());
  double separate_billing_ratio_upper = SPDM_hard_separate_billing_ratio_upper(session_data->get_spdm_ctx());
  if (SPDM_enable_rank_bs_exp(session_data->get_spdm_ctx())) {
    separate_billing_ratio_lower = SPDM_hard_separate_billing_ratio_lower_v2(session_data->get_spdm_ctx());
    separate_billing_ratio_upper = SPDM_hard_separate_billing_ratio_upper_v2(session_data->get_spdm_ctx());
  }
  double separate_billing_ratio_lower_outer =
      SPDM_hard_separate_billing_ratio_lower_outer(session_data->get_spdm_ctx());
  double separate_billing_ratio_upper_outer =
      SPDM_hard_separate_billing_ratio_upper_outer(session_data->get_spdm_ctx());
  auto enable_fanstop_bs_ratio_ =
      SPDM_enable_fanstop_bs_ratio(session_data->get_spdm_ctx());
  auto fanstop_bs_price_ratio_lower_ =
      SPDM_fanstop_bs_price_ratio_lower(session_data->get_spdm_ctx());
  auto fanstop_bs_price_ratio_upper_ =
      SPDM_fanstop_bs_price_ratio_upper(session_data->get_spdm_ctx());
  auto fanstop_bs_ratio_tail = RankKconfUtil::fanstopBsRatioTail();
  auto enable_fanstop_specific_bs_ratio =
      SPDM_enable_fanstop_specific_bs_ratio(session_data->get_spdm_ctx());
  auto fanstop_bs_ratio_for_sptail = RankKconfUtil::fanstopBsRatioForSptail();
  auto enable_fanstop_billing_on_cost_ratio =
      SPDM_enable_fanstop_billing_on_cost_ratio(session_data->get_spdm_ctx());
  auto fanstop_billing_on_cost_ratio_tail = RankKconfUtil::fanstopBillingOnCostRatioTail();
  auto fanstop_bs_ocpx_blacklist = RankKconfUtil::fanstopBsOcpxBlacklist();
  auto enable_skip_storewide_live_bs_tail = RankKconfUtil::enableSkipStorewideLiveBsTail();
  auto skip_storewide_live_bs_tail = RankKconfUtil::skipStorewideLiveBsTail();
  std::shared_ptr<::ks::infra::TailNumberV2> esp_mobile_live_deep_bs_price_ratio =
      RankKconfUtil::espMobileLiveDeepBsPriceRatio();
  bool enable_rank_bs_outer_ratio = SPDM_enable_rank_bs_outer_ratio(session_data->get_spdm_ctx());
  ks::engine_base::BillingSeparateWeightManager weight_manager;
  auto trans_unit_tail = RankKconfUtil::InnerLoopBsRatioTransToPackUnitTail();
  auto trans_campaign_tail = RankKconfUtil::InnerLoopBsRatioTransToPackCampaignTail();
  // 托管 + 全站
  static absl::flat_hash_set<int64_t> hosting_set {
      AdEnum::ORIENTED_SMART_GOODS,
      AdEnum::ORIENTED_SMART_OPTIONAL_GOODS,
      AdEnum::ORIENTED_SMART_SINGLE_GOODS,
      AdEnum::ORIENTED_CID_SUPER_PRODUCT,
      AdEnum::ORIENTED_STORE_WIDE_LIVE,
      AdEnum::ORIENTED_LIVE_HOSTING_PROJECT,
      AdEnum::ORIENTED_STORE_WIDE_PRODUCT,
      AdEnum::ORIENTED_SEARCH_WORD_PROJECT,
      AdEnum::STORE_NEW_CUSTOMER_HOSTING,
      AdEnum::ORIENTED_SEARCH_WORD_LIVE_PROJECT,
      AdEnum::ORIENTED_ESP_MOBILE_HOSTING,
      AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE,
      AdEnum::ORIENTED_PRODUCT_TEST_HOSTING
  };
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    double dynamic_ratio = 1.0;
    if (ad.get_separate_billing_ratio() != 0.0) {
      double bid_server_ratio = utility::GetBidServerRatio(p_ad);
      double separate_billing_ratio = ad.get_separate_billing_ratio();
      separate_billing_ratio = std::min(ad.get_separate_billing_ratio(), separate_billing_ratio_upper);
      separate_billing_ratio = std::max(ad.get_separate_billing_ratio(), separate_billing_ratio_lower);
      if (enable_rank_bs_outer_ratio && ad.Is(AdFlag::is_outer_loop_ad)) {
        separate_billing_ratio =
            std::min(ad.get_separate_billing_ratio(), separate_billing_ratio_upper_outer);
        separate_billing_ratio =
            std::max(ad.get_separate_billing_ratio(), separate_billing_ratio_lower_outer);
      }
      double k = 1 / separate_billing_ratio;
      weight_manager.GetPriceWeightV2(ad.get_auto_bid_weight(), bid_server_ratio, k, &dynamic_ratio);
    }

    // 计费系数
    double ratio = 1.0;
    // 内循环跳过账户调价计费
    bool is_inner = p_ad->GetAdMonitorType() != kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT;
    bool is_inner_skip_account_bidding = is_inner;
    if (ad.get_is_account_bidding() && !is_inner_skip_account_bidding) {
      ratio = ad.get_price_ratio();
      if (enable_bs_dynamix_ratio && ad.get_is_billing_separate_account()) {
        ratio *= dynamic_ratio;
      }
    } else if (ad.get_is_billing_separate_account() && ad.get_separate_billing_ratio() != 0) {
      ratio = dynamic_ratio;
    }

    if ((p_ad->get_account_type()
         == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) && enable_fanstop_bs_ratio_) {
      if (fanstop_bs_ratio_tail != nullptr && fanstop_bs_ratio_tail->IsOnFor(ad.get_unit_id())) {
        billing_separate_lower = fanstop_bs_price_ratio_lower_;
        billing_separate_upper = fanstop_bs_price_ratio_upper_;
      }
      if (enable_fanstop_specific_bs_ratio && fanstop_bs_ratio_for_sptail != nullptr) {
        std::string key =
            absl::StrCat(ad.get_ocpx_action_type(), "_", ad.get_unit_id() / 10 % 10, "_upper");
        auto iter = fanstop_bs_ratio_for_sptail->find(key);
        if (iter != fanstop_bs_ratio_for_sptail->end()) {
          billing_separate_upper = iter->second;
        }
        key = absl::StrCat(
            ad.get_ocpx_action_type(), "_", ad.get_unit_id() / 10 % 10, "_lower");
        iter = fanstop_bs_ratio_for_sptail->find(key);
        if (iter != fanstop_bs_ratio_for_sptail->end()) {
          billing_separate_lower = iter->second;
        }
      }
      if (enable_fanstop_billing_on_cost_ratio && fanstop_billing_on_cost_ratio_tail != nullptr
          && fanstop_billing_on_cost_ratio_tail->IsOnFor(ad.get_unit_id())
          && ((fanstop_bs_ocpx_blacklist != nullptr
              && fanstop_bs_ocpx_blacklist->count(ad.get_ocpx_action_type()) == 0))) {
        double ratio_temp =
            p_ad->Attr(ItemIdx::fanstop_cost_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
        if (ratio_temp != 0) {
          ratio = ratio_temp;
        }
      }
    }

    if (!(ad.get_is_account_bidding() && !is_inner_skip_account_bidding)) {
      if (ratio < billing_separate_lower) {
        ratio = billing_separate_lower;
      }
      if (ratio > billing_separate_upper) {
        ratio = billing_separate_upper;
      }
    }

    // PC 直播 ROAS 使用 auto_bid 计费
    bool roas_adjust_bs = ad.Is(AdFlag::is_merchant_live) &&
                          ad.get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
        (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
        (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI &&
        t7_roas_skip_bs_tails->count(ad.get_unit_id() % 100)));
    bool eop_adjust_bs = ad.Is(AdFlag::is_merchant_live) &&
                    ad.get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
                    ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED;
    bool is_esp_mobile_deep_bs = esp_mobile_live_deep_bs_price_ratio &&
        esp_mobile_live_deep_bs_price_ratio->IsOnFor(ad.get_unit_id()) &&
        p_ad->Is(AdFlag::IsEspMobileTarget) &&
        (p_ad->Is(AdFlag::is_fanstop_live_order) || p_ad->Is(AdFlag::is_fanstop_live_roas));
    if (roas_adjust_bs || eop_adjust_bs || is_esp_mobile_deep_bs) {
      double old_ratio = ratio;
      double auto_cpa_bid = p_ad->get_auto_cpa_bid();
      if (ad.get_billing_separate_bid() > 0) {
        ratio = auto_cpa_bid / ad.get_billing_separate_bid();
      }
      if (ad.get_scene_oriented_type() != kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT
          && skip_bs_autobid_price_ratio_tails->count(ad.get_unit_id() % 100) > 0) {
        ratio = 1.0;
      }
      if (ad.get_price_ratio() > 0) {
        ratio *= ad.get_price_ratio();
      }
    }
    bool is_live_hosting =
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT &&
        ((live_hosting_bs_tail && live_hosting_bs_tail->IsOnFor(p_ad->get_campaign_id())) ||
        enable_live_hosting_skip_bid_bs_spdm);
    if (is_live_hosting) {
      ratio = ad.get_price_ratio() < 0.001 ? 1.0 : ad.get_price_ratio();
    }
    bool is_new_cust_bs = false;
    if (enable_inner_new_customer_bs && p_ad->Is(AdFlag::is_inner_loop_ad) &&
        !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) && p_ad->get_smb_level_tag() == 4 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        new_cust_bs_tail->count(p_ad->get_account_id() % 100) > 0) {
      ratio = ad.get_smb_billing_control_ratio() < 0.01 ? 1.0 : ad.get_smb_billing_control_ratio();
      if (enable_inner_new_customer_bs_backup) {
        ratio = ad.get_price_ratio() < 0.01 || ad.get_price_ratio() > 1.0 ? 1.0 :
                ad.get_price_ratio() * new_cust_bs_weight_constant;
      }
      is_new_cust_bs = true;
    }

    // 全站直播全互斥订单部分
    bool is_storewide_bs_order_author = false;
    if (storewide_bs_order_author_tail != nullptr) {
      is_storewide_bs_order_author =
          storewide_bs_order_author_tail->find(ad.get_author_id()) != storewide_bs_order_author_tail->end() ||
          storewide_bs_order_author_tail->find(ad.get_author_id() % 100) !=
          storewide_bs_order_author_tail->end();
    }
    is_storewide_bs_order_author = is_storewide_bs_order_author || enable_storewide_bs_align;
    // 全站直播全互斥
    if (ad.Is(AdFlag::is_merchant_live) &&
        (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
        (is_storewide_bs_order_author && (ad.get_scene_oriented_type() ==
        kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE))) &&
        ad.get_storewide_incompatible_type() == 1 &&
        ad.get_price_ratio() > 0) {
      if (!enable_storewide_live_skip_bs_ab) {
        // 流量侧 或 作者侧实验（复用之前计划侧实验的配置）总开关
        if (enable_storewide_live_bs_ab || (storewide_bs_campaign_tail &&
            storewide_bs_campaign_tail->count(ad.get_author_id() % 100) > 0)) {
          if (enable_storewide_live_bs_ab_v2) {  // 子开关控制具体策略分支
            if (ad.get_price_ratio() > 1.2) {
              ratio = storewide_live_bs_overcharge_ratio;
            } else if (ad.get_price_ratio() < 0.8) {
              ratio = storewide_live_bs_discount_ratio;
              if (enable_storewide_live_bs_control &&
                  p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
                    ratio = 1.0;
                  }
              if (enable_skip_storewide_live_bs_tail) {
                if (skip_storewide_live_bs_tail &&
                    skip_storewide_live_bs_tail->count(ad.get_campaign_id() % 100) > 0 &&
                    p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
                      ratio = 1.0;
                    }
                  }
            } else {
              ratio = 1.0;
            }
          } else {
            ratio = ad.get_price_ratio();
          }
        }
      }
    }
    // PC 短视频 ROAS
    bool pc_photo_roas_adjust_bs = ad.Is(AdFlag::is_merchant_photo) &&
        (p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST ||
         p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_COST_CAP) &&
        (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
         p_ad->get_project_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS);
    if (pc_photo_roas_adjust_bs) {
      ratio = ad.get_price_ratio() > 0.01 ? ad.get_price_ratio() : 1.0;
    }

    // 商品全站控成本
    bool merchant_storewide_adjust_bs = ad.Is(AdFlag::is_merchant_photo) &&
        ad.get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
        (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
         ad.get_scene_oriented_type() == 24);
    if (merchant_storewide_adjust_bs) {
      ratio = ad.get_price_ratio() > 0.01 ? ad.get_price_ratio() : 1.0;
    }

    bool enable_gyk_item_card_bs =
        session_data->get_pos_manager_base().IsGuessYouLike() &&
        p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
        p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
        p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP && item_card_photo_bs_tail &&
        item_card_photo_bs_tail->count(p_ad->get_unit_id() % 100) > 0;
    if (enable_gyk_item_card_bs_spdm && enable_gyk_item_card_bs) {
      ratio = ad.get_price_ratio() <= 1 ? gyk_item_card_bs_ratio : 1.0;
    }
    // Note(lixu05): 短带订单不适用 price_ratio
    bool is_photo_campaign_order_ad =
      p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_SINGLE_GOODS ||
      p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_CID_SUPER_PRODUCT ||
      p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::STORE_NEW_CUSTOMER_HOSTING;
    bool enable_pc_photo_order_new_bs = p_ad->Is(AdFlag::is_merchant_photo) &&
      p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
      p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
      p_ad->get_scene_oriented_type() != 24 &&
      p_ad->get_project_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED;
    if (enable_pc_photo_order_new_bs) {
      ratio = 1.0;
    }
    // cid 排序计费分离单独生效
    if ((ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS
          || ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID)) {
      ratio = ad.get_price_ratio() > 0 ? ad.get_price_ratio() : 1.0;
    }
    const std::string& ocpx_str = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    RANK_DOT_STATS(session_data, ratio * 1e3,
          "inner_billing_seperate_ratio", "normal", ocpx_str,
          absl::StrCat(session_data->get_page_id()), ratio >= 1.0? "add" : "discount");
    // 内循环计费分离系数移到 adpack 乘
    bool inner_loop_bs_trans_to_adpack =
        (ad.Is(AdFlag::is_merchant_live) || ad.Is(AdFlag::is_merchant_item_photo)) &&
        ad.get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
        (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS);
    bool is_exp_tail;
    if (hosting_set.count(ad.get_scene_oriented_type()) > 0) {
      is_exp_tail = trans_campaign_tail && trans_campaign_tail->count(ad.get_campaign_id() % 100) > 0;
    } else {
      is_exp_tail = trans_unit_tail && trans_unit_tail->count(ad.get_unit_id() % 100) > 0;
    }
    inner_loop_bs_trans_to_adpack = inner_loop_bs_trans_to_adpack && is_exp_tail;
    if (inner_loop_bs_trans_to_adpack) {
      // 全站直播重新设置
      if (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE ||
          p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE) {
        ratio = ad.get_price_ratio() > 0.01 ? ad.get_price_ratio() : 1.0;
      }
      ad.set_gimbal_ratio(ratio);
      ad.set_gimbal_type(kuaishou::ad::GimbalType::GIMBAL_TYPE_INNER_LOOP);
      ratio = 1.0;
    }

    return ratio;
  };
  FINISH_COMPUTE()
}

void UnifyNativeBS_Admit(ks::platform::AddibleRecoContextInterface* context,
                                     ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_native_ad_billing_seperate_ =
      SPDM_enable_native_ad_billing_seperate(session_data->get_spdm_ctx());
  auto enable_inner_new_customer_bs =
      SPDM_enable_inner_new_customer_bs(session_data->get_spdm_ctx());
  auto enable_fanstop_skip_bs_fix =
      SPDM_enable_fanstop_skip_bs_fix(session_data->get_spdm_ctx());
  auto enable_migrate_new_cust_bs =
      SPDM_enable_migrate_new_cust_bs(session_data->get_spdm_ctx());
  auto enable_storewide_live_bs_ab = SPDM_enable_storewide_live_bs_ab(session_data->get_spdm_ctx());
  auto new_cust_bs_tail = RankKconfUtil::newCustomerBsTails();
  auto storewide_skip_bs_tails_rank = RankKconfUtil::storewideSkipBillingSeparateCampaignTailRank();
  auto pc_live_new_bs_tail = RankKconfUtil::pcLiveAllPageBsUnitTail();
  auto enable_gyk_item_card_bs_spdm =
    SPDM_enable_gyk_item_card_bs_spdm(session_data->get_spdm_ctx());
  auto item_card_photo_bs_tail = RankKconfUtil::itemCardPhotoBsUnitTail();
  auto storewide_bs_campaign_tail = RankKconfUtil::storewideBsCampaignTail();
  auto storewide_bs_order_author_tail = engine_base::AdKconfUtil::storewideBillingSepOrderAuthorTail();
  bool enable_storewide_bs_align =
      engine_base::SPDM_enable_storewide_bs_align(session_data->get_spdm_ctx());
  std::shared_ptr<::ks::infra::TailNumberV2> esp_mobile_native_bs_tail =
      RankKconfUtil::espMobileNativeBsTail();
  auto enable_t7_roi_skip_bs = SPDM_enableT7ROISkipBillingSeparate();
  auto t7_roi_skip_bs_unit_tail =
      RankKconfUtil::t7ROISkipBillingSeparateUnitTail();
  auto enable_lsp_storewide_skip_bs = SPDM_enable_lsp_storewide_skip_bs(session_data->get_spdm_ctx());
  bool enable_rank_bs_exp = SPDM_enable_rank_bs_exp(session_data->get_spdm_ctx());
  bool enable_rank_bs_exp_inner_roi = SPDM_enable_rank_bs_exp_inner_roi(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    auto& ad = *p_ad;
    // 只处理软广
    if (p_ad->Is(AdFlag::IsHardAd)) {
      return false;
    }
    // (dingyiming05) 计费摸底实验
    if (enable_rank_bs_exp && session_data->get_is_thanos_mix_request() &&
        p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
        !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) && (!(p_ad->get_auto_roas() > 0) ||
        (p_ad->Is(AdFlag::is_inner_loop_ad) && enable_rank_bs_exp_inner_roi)) &&
        !RankKconfUtil::skipBsExpWhiteAccount()->count(p_ad->get_account_id())) {
      return ad.get_is_billing_separate_account();
    }
    // 全站直播半互斥计费分离跳过
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE &&
        p_ad->get_storewide_incompatible_type() != 1) {
      return false;
    }

    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI &&
        enable_t7_roi_skip_bs &&
        nullptr != t7_roi_skip_bs_unit_tail &&
        t7_roi_skip_bs_unit_tail->IsOnFor(p_ad->get_unit_id())) {
          return false;
    }
    bool enable_pc_live_new_bs = p_ad->Is(AdFlag::is_merchant_live) &&
      p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
      p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
      ((p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
       p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED) &&
       pc_live_new_bs_tail &&
       pc_live_new_bs_tail->count(p_ad->get_unit_id() % 100) > 0);
    bool is_esp_mobile_native_bs_exp = esp_mobile_native_bs_tail &&
                                       esp_mobile_native_bs_tail->IsOnFor(p_ad->get_unit_id()) &&
                                        p_ad->Is(AdFlag::IsEspMobileTarget);
    // 本地全站跳过计费分离
    if (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE &&
        enable_lsp_storewide_skip_bs) {
      return false;
    }
    // 新客计费分离新实验
    if (enable_inner_new_customer_bs && p_ad->Is(AdFlag::is_inner_loop_ad) &&
        !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) && p_ad->get_smb_level_tag() == 4 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        new_cust_bs_tail->count(p_ad->get_account_id() % 100) > 0) {
      if (enable_migrate_new_cust_bs) {
        return false;
      }
      return true;
    }

    bool enable_gyk_item_card_bs =
        session_data->get_pos_manager_base().IsGuessYouLike() &&
        p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
        p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
        p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP && item_card_photo_bs_tail &&
        item_card_photo_bs_tail->count(p_ad->get_unit_id() % 100) > 0;
    if (enable_gyk_item_card_bs_spdm && enable_gyk_item_card_bs) {
      return true;
    }
    // pc 短视频 ROAS 新计费分离
    bool enable_pc_photo_roas_new_bs = p_ad->Is(AdFlag::is_merchant_photo) &&
           (p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST ||
            p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_COST_CAP) &&
           p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
            p_ad->get_project_ocpx_action_type() ==  kuaishou::ad::AD_MERCHANT_ROAS);
    if (enable_pc_photo_roas_new_bs) {
      return true;
    }
    bool enable_merchant_storewide_new_bs = p_ad->Is(AdFlag::is_merchant_photo) &&
           p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
            ad.get_scene_oriented_type() == 24);
    // 商品全站新计费分离实验
    if (enable_merchant_storewide_new_bs) {
      return true;
    }

    // pc 控成本直播新计费分离实验
    if (enable_pc_live_new_bs) {
      return true;
    }

    // 加速探索增量计费跳过
    if (p_ad->Attr(ItemIdx::is_increment_explore).GetIntValue(p_ad->AttrIndex()).value_or(0)) {
      return false;
    }

    // 全站直播全互斥订单部分
    bool is_storewide_bs_order_author = false;
    if (storewide_bs_order_author_tail != nullptr) {
      is_storewide_bs_order_author =
          storewide_bs_order_author_tail->find(p_ad->get_author_id()) !=
          storewide_bs_order_author_tail->end() ||
          storewide_bs_order_author_tail->find(p_ad->get_author_id() % 100) !=
          storewide_bs_order_author_tail->end();
    }
    is_storewide_bs_order_author = is_storewide_bs_order_author || enable_storewide_bs_align;
    // 全站直播默认跳过
    if (p_ad->Is(AdFlag::is_merchant_live) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
        (is_storewide_bs_order_author && (p_ad->get_scene_oriented_type() ==
        kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE)))) {
      if (p_ad->get_storewide_incompatible_type() == 1 &&
          (enable_storewide_live_bs_ab || (storewide_bs_campaign_tail &&
          storewide_bs_campaign_tail->count(p_ad->get_author_id() % 100) > 0))) {
        // 全互斥命中流量侧或作者侧实验，则走计费分离
        return true;
      }
      return false;
    }

    auto req_type = session_data->get_pos_manager_base().GetAdRequestType();
    if (req_type == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_FOLLOW) {
      return false;
    }
    // 非 ocpm 跳过
    if (ad.get_bid_type() != kuaishou::ad::AdEnum::OCPM_DSP) {
      return false;
    }
    auto account_type = ad.get_account_type();
    auto bid_strategy = ad.get_bid_strategy();
    bool is_fanstopv2_account = kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2;
    bool is_temu_account = kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU;

    // 粉条的 campaign_type (多种账户) + temu 账户
    if ((ad.Is(AdFlag::is_fanstop) || is_temu_account)
        && (bid_strategy == kuaishou::ad::AdEnum_BidStrategy_SMART_BID_STRATEGY)) {
      return false;
    }

    if ((is_fanstopv2_account || is_temu_account)) {
      if (bid_strategy == kuaishou::ad::AdEnum_BidStrategy_CUSTOM_BID_STRATEGY) {
        if (ad.get_price() < 1) {
          // 避免小于 1 厘概率计费被兜底到 1 厘
          return false;
        }
        return true;
      }
    }

    if (ad.Is(AdFlag::is_fanstop) &&
        (ad.get_bid_strategy() ==
             kuaishou::ad::AdEnum_BidStrategy_COST_CAP_BID_STRATEGY ||
         ad.get_bid_strategy() ==
             kuaishou::ad::AdEnum_BidStrategy_CUSTOM_BID_STRATEGY) &&
        !is_esp_mobile_native_bs_exp) {
      return false;
    }
    // 磁力金牛 pc 的 nobid 跳过
    if (!ad.Is(AdFlag::is_fanstop) && ad.get_speed() == kuaishou::ad::AdEnum::SPEED_NO_BID) {
      return false;
    }
    if (!ad.Is(AdFlag::is_fanstop) && ad.get_speed() == kuaishou::ad::AdEnum::SPEED_COST_CAP) {
      return false;
    }

    // 外循环广告 fix billing seperate
    if (ad.Is(AdFlag::is_outer_loop_ad)) {
      // 账户调价圈尾号走 billing seperate
      if (ad.get_is_account_bidding()) {
        return true;
      }

      // 非账户调价精简逻辑，与硬广拉齐
      if (ad.get_is_billing_separate_account()) {
        return true;
      } else {
        return false;
      }
    }
    if (req_type == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_EXPLORE ||
        req_type == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEARBY ||
        req_type == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEBULA_NEARBY ||
        session_data->get_pos_manager_base().IsInspireLive() ||
        session_data->get_pos_manager_base().GetInteractiveForm() ==
            kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS) {
      return enable_native_ad_billing_seperate_;
    }
    return false;
  };
  FINISH_ADMIT()
}

void UnifyNativeBS_Compute(ks::platform::AddibleRecoContextInterface* context,
                                     ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto bs_enable_native_price_autocpa_diff_ =
      SPDM_native_ad_enable_native_price_autocpa_diff(session_data->get_spdm_ctx());
  bs_enable_native_price_autocpa_diff_ =
      SPDM_native_ad_enable_native_price_autocpa_diff_v2(session_data->get_spdm_ctx());
  auto billing_separate_price_ratio_lower_ =
      SPDM_native_ad_bs_price_ratio_lower(session_data->get_spdm_ctx());
  auto billing_separate_price_ratio_upper_ =
      SPDM_native_ad_bs_price_ratio_upper(session_data->get_spdm_ctx());
  auto enable_fanstop_bs_ratio_ =
      SPDM_enable_fanstop_bs_ratio(session_data->get_spdm_ctx());
  auto fanstop_bs_price_ratio_lower_ =
      SPDM_fanstop_bs_price_ratio_lower(session_data->get_spdm_ctx());
  auto fanstop_bs_price_ratio_upper_ =
      SPDM_fanstop_bs_price_ratio_upper(session_data->get_spdm_ctx());
  auto enable_inner_smb_bid_unit_price = true;
  auto enable_inner_smb_account_price_skip_all = true;
  auto enable_gyk_item_card_bs_spdm =
    SPDM_enable_gyk_item_card_bs_spdm(session_data->get_spdm_ctx());
  auto gyk_item_card_bs_ratio = SPDM_gyk_item_card_bs_ratio(session_data->get_spdm_ctx());
  auto enable_storewide_live_bs_ab = SPDM_enable_storewide_live_bs_ab(session_data->get_spdm_ctx());
  auto enable_storewide_live_bs_ab_v2 = SPDM_enable_storewide_live_bs_ab_v2(session_data->get_spdm_ctx());
  auto enable_storewide_live_skip_bs_ab = SPDM_enable_storewide_live_skip_bs_ab(session_data->get_spdm_ctx());
  auto enable_storewide_live_bs_control = SPDM_enable_storewide_live_bs_control(session_data->get_spdm_ctx());
  auto storewide_live_bs_overcharge_ratio =
      SPDM_storewide_live_bs_overcharge_ratio(session_data->get_spdm_ctx());
  auto storewide_live_bs_discount_ratio = SPDM_storewide_live_bs_discount_ratio(session_data->get_spdm_ctx());
  auto item_card_photo_bs_tail = RankKconfUtil::itemCardPhotoBsUnitTail();
  auto t7_roas_skip_bs_tails = RankKconfUtil::t7roasSkipBillingSeparateUnitTail();
  auto skip_bs_autobid_price_ratio_tails = RankKconfUtil::skipBsAutobidPriceRatioUnitTail();
  auto new_cust_bs_tail = RankKconfUtil::newCustomerBsTails();
  auto storewide_bs_campaign_tail = RankKconfUtil::storewideBsCampaignTail();
  auto storewide_bs_order_author_tail = engine_base::AdKconfUtil::storewideBillingSepOrderAuthorTail();
  bool enable_storewide_bs_align =
      engine_base::SPDM_enable_storewide_bs_align(session_data->get_spdm_ctx());
  auto smb_skip_account_tails = RankKconfUtil::smbSkipBillingSeparateAccountTail();
  auto fanstop_bs_ratio_tail = RankKconfUtil::fanstopBsRatioTail();
  auto fanstop_bs_ocpx_blacklist = RankKconfUtil::fanstopBsOcpxBlacklist();
  auto live_hosting_bs_tail = RankKconfUtil::liveHostingBsTail();
  auto enable_fanstop_specific_bs_ratio =
      SPDM_enable_fanstop_specific_bs_ratio(session_data->get_spdm_ctx());
  auto enable_live_hosting_skip_bid_bs_spdm =
    SPDM_enable_live_hosting_skip_bid_bs_spdm(session_data->get_spdm_ctx());
  auto fanstop_bs_ratio_for_sptail = RankKconfUtil::fanstopBsRatioForSptail();
  auto enable_fanstop_billing_on_cost_ratio =
      SPDM_enable_fanstop_billing_on_cost_ratio(session_data->get_spdm_ctx());
  auto fanstop_billing_on_cost_ratio_tail = RankKconfUtil::fanstopBillingOnCostRatioTail();
  auto enable_inner_new_customer_bs =
      SPDM_enable_inner_new_customer_bs(session_data->get_spdm_ctx());
  auto enable_inner_new_customer_bs_backup =
      SPDM_enable_inner_new_customer_bs_backup(session_data->get_spdm_ctx());
  auto new_cust_bs_weight_constant = 1.0;
  auto billing_separate_price_lower_ =
      SPDM_native_ad_bs_price_lower(session_data->get_spdm_ctx());
  auto billing_separate_price_upper_ =
      SPDM_native_ad_bs_price_upper(session_data->get_spdm_ctx());
  double separate_billing_ratio_lower =
      SPDM_native_separate_billing_ratio_lower(session_data->get_spdm_ctx());
  double separate_billing_ratio_upper =
      SPDM_native_separate_billing_ratio_upper(session_data->get_spdm_ctx());
  if (SPDM_enable_rank_bs_exp(session_data->get_spdm_ctx())) {
    separate_billing_ratio_lower = SPDM_native_separate_billing_ratio_lower_v2(session_data->get_spdm_ctx());
    separate_billing_ratio_upper = SPDM_native_separate_billing_ratio_upper_v2(session_data->get_spdm_ctx());
  }
  double separate_billing_ratio_lower_outer =
      SPDM_native_separate_billing_ratio_lower_outer(session_data->get_spdm_ctx());
  double separate_billing_ratio_upper_outer =
      SPDM_native_separate_billing_ratio_upper_outer(session_data->get_spdm_ctx());
  std::shared_ptr<::ks::infra::TailNumberV2> esp_mobile_live_deep_bs_price_ratio =
      RankKconfUtil::espMobileLiveDeepBsPriceRatio();
  auto enable_skip_storewide_live_bs_tail = RankKconfUtil::enableSkipStorewideLiveBsTail();
  auto skip_storewide_live_bs_tail = RankKconfUtil::skipStorewideLiveBsTail();
  bool enable_rank_bs_outer_ratio = SPDM_enable_rank_bs_outer_ratio(session_data->get_spdm_ctx());
  bool enable_bs_dynamix_ratio = SPDM_enable_bs_dynamix_ratio_native(session_data->get_spdm_ctx());
  ks::engine_base::BillingSeparateWeightManager weight_manager;
  auto trans_unit_tail = RankKconfUtil::InnerLoopBsRatioTransToPackUnitTail();
  auto trans_campaign_tail = RankKconfUtil::InnerLoopBsRatioTransToPackCampaignTail();

  auto cpa_autocpa_diff_func = [&] (AdCommon* ad) -> double {
    if (ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
        ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI ||
        ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
        ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_FOLLOW_ROI ||
        ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_ROAS ||
        ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_SEVEN_DAY_ROAS) {
      if (ad->get_auto_roas() > 0 && ad->get_roi_ratio() > 0) {
        return ad->get_roi_ratio() / ad->get_auto_roas();
      }
    } else {
      if (ad->get_auto_cpa_bid() > 0 && ad->get_cpa_bid() > 0) {
        return ad->get_auto_cpa_bid() / ad->get_cpa_bid();
      }
    }
    return 1.0;
  };
  // 托管 + 全站
  static absl::flat_hash_set<int64_t> hosting_set {
      AdEnum::ORIENTED_SMART_GOODS,
      AdEnum::ORIENTED_SMART_OPTIONAL_GOODS,
      AdEnum::ORIENTED_SMART_SINGLE_GOODS,
      AdEnum::ORIENTED_CID_SUPER_PRODUCT,
      AdEnum::ORIENTED_STORE_WIDE_LIVE,
      AdEnum::ORIENTED_LIVE_HOSTING_PROJECT,
      AdEnum::ORIENTED_STORE_WIDE_PRODUCT,
      AdEnum::ORIENTED_SEARCH_WORD_PROJECT,
      AdEnum::STORE_NEW_CUSTOMER_HOSTING,
      AdEnum::ORIENTED_SEARCH_WORD_LIVE_PROJECT,
      AdEnum::ORIENTED_ESP_MOBILE_HOSTING,
      AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE,
      AdEnum::ORIENTED_PRODUCT_TEST_HOSTING
  };
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    double cpa_autocpa_diff = cpa_autocpa_diff_func(p_ad);
    ad.set_separate_billing_ratio(
        std::min(ad.get_separate_billing_ratio(), separate_billing_ratio_upper));
    ad.set_separate_billing_ratio(
        std::max(ad.get_separate_billing_ratio(), separate_billing_ratio_lower));
    if (enable_rank_bs_outer_ratio && ad.Is(AdFlag::is_outer_loop_ad)) {
      ad.set_separate_billing_ratio(
          std::min(ad.get_separate_billing_ratio(), separate_billing_ratio_upper_outer));
      ad.set_separate_billing_ratio(
          std::max(ad.get_separate_billing_ratio(), separate_billing_ratio_lower_outer));
    }
    double billing_ratio = 1.0;
    if (ad.get_separate_billing_ratio() > 0) {
      billing_ratio = 1 / ad.get_separate_billing_ratio();
    }
    double weight = ad.get_auto_bid_weight();
    // 二期计费系数计算，欠收控制统一放在 adRank, front 仅计算 weight
    weight_manager.GetPriceWeightV2(weight, cpa_autocpa_diff, billing_ratio, &ratio);
    if (ad.get_is_account_bidding() && ad.Is(AdFlag::is_outer_loop_ad)) {
      double dynamic_ratio = ratio;
      ratio = ad.get_price_ratio();
      if (enable_bs_dynamix_ratio && ad.get_is_billing_separate_account()) {
        ratio *= dynamic_ratio;
      }
    } else if (bs_enable_native_price_autocpa_diff_ && cpa_autocpa_diff > 0.0) {
      weight_manager.GetPriceWeightV2(weight, cpa_autocpa_diff, billing_ratio, &ratio);
    } else if (p_ad->get_price_separate_ratio() > 0.0) {
      weight_manager.GetPriceWeightV2(weight, p_ad->get_price_separate_ratio(), billing_ratio, &ratio);
    }

    if (ad.Is(AdFlag::is_fanstop) &&
        (ad.get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
         ad.get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU)
        && enable_fanstop_bs_ratio_) {
      if (fanstop_bs_ratio_tail->IsOnFor(ad.get_unit_id())) {
        billing_separate_price_ratio_lower_ = fanstop_bs_price_ratio_lower_;
        billing_separate_price_ratio_upper_ = fanstop_bs_price_ratio_upper_;
      }
      if (enable_fanstop_specific_bs_ratio && fanstop_bs_ratio_for_sptail != nullptr) {
        std::string key = absl::StrCat(ad.get_ocpx_action_type(), "_",
            ad.get_unit_id() / 10 % 10, "_upper");
        auto iter = fanstop_bs_ratio_for_sptail->find(key);
        if (iter != fanstop_bs_ratio_for_sptail->end()) {
          billing_separate_price_ratio_upper_ = iter->second;
        }
        key = absl::StrCat(ad.get_ocpx_action_type(), "_",
            ad.get_unit_id() / 10 % 10, "_lower");
        iter = fanstop_bs_ratio_for_sptail->find(key);
        if (iter != fanstop_bs_ratio_for_sptail->end()) {
          billing_separate_price_ratio_lower_ = iter->second;
        }
      }
      if (enable_fanstop_billing_on_cost_ratio && fanstop_billing_on_cost_ratio_tail != nullptr
          && fanstop_billing_on_cost_ratio_tail->IsOnFor(ad.get_unit_id())
          && ((fanstop_bs_ocpx_blacklist != nullptr
              && fanstop_bs_ocpx_blacklist->count(ad.get_ocpx_action_type()) == 0))) {
        double ratio_temp =
            p_ad->Attr(ItemIdx::fanstop_cost_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);;
        if (ratio_temp != 0) {
          ratio = ratio_temp;
        }
      }
    }
    ratio = std::min(std::max(ratio, billing_separate_price_ratio_lower_),
        billing_separate_price_ratio_upper_);

    // PC 直播 ROAS 使用 auto_bid 计费
    bool roas_adjust_bs = ad.Is(AdFlag::is_merchant_live) &&
                          ad.get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
         (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
         (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI &&
          t7_roas_skip_bs_tails->count(ad.get_unit_id() % 100)));
    bool eop_adjust_bs = ad.Is(AdFlag::is_merchant_live) &&
                    ad.get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
                    ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED;
    bool is_esp_mobile_deep_bs = esp_mobile_live_deep_bs_price_ratio &&
        esp_mobile_live_deep_bs_price_ratio->IsOnFor(ad.get_unit_id()) &&
        ad.Is(AdFlag::IsEspMobileTarget) &&
        (ad.Is(AdFlag::is_fanstop_live_order) || ad.Is(AdFlag::is_fanstop_live_roas));
    if (roas_adjust_bs || eop_adjust_bs || is_esp_mobile_deep_bs) {
      double old_ratio = ratio;
      double auto_cpa_bid = ad.get_auto_cpa_bid();
      double pred_target_cost = ad.get_pred_target_cost();
      double finetune_ratio = 1.0;
      if (ad.get_billing_separate_bid() > 0) {
        ratio = auto_cpa_bid / ad.get_billing_separate_bid();
      }
      if (ad.get_scene_oriented_type() != kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT
          && skip_bs_autobid_price_ratio_tails->count(ad.get_unit_id() % 100) > 0) {
        ratio = 1.0;
      }
      if (ad.get_price_ratio() > 0) {
        ratio *= ad.get_price_ratio();
      }
    }
    bool is_live_hosting =
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT &&
        ((live_hosting_bs_tail && live_hosting_bs_tail->IsOnFor(p_ad->get_campaign_id())) ||
        enable_live_hosting_skip_bid_bs_spdm);
    if (is_live_hosting) {
      ratio = ad.get_price_ratio() < 0.001 ? 1.0 : ad.get_price_ratio();
    }
    bool is_new_cust_bs = false;
    if (enable_inner_new_customer_bs && p_ad->Is(AdFlag::is_inner_loop_ad) &&
        !p_ad->Is(AdFlag::IsEspUnifyNobidAdV2) && p_ad->get_smb_level_tag() == 4 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED &&
        new_cust_bs_tail->count(p_ad->get_account_id() % 100) > 0) {
      ratio = ad.get_smb_billing_control_ratio() < 0.01 ? 1.0 : ad.get_smb_billing_control_ratio();
      if (enable_inner_new_customer_bs_backup) {
        ratio = ad.get_price_ratio() < 0.01 || ad.get_price_ratio() > 1.0 ? 1.0 :
                ad.get_price_ratio() * new_cust_bs_weight_constant;
      }
      is_new_cust_bs = true;
    }

    // 全站直播全互斥订单部分跳过
    bool is_storewide_bs_order_author = false;
    if (storewide_bs_order_author_tail != nullptr) {
      is_storewide_bs_order_author =
          storewide_bs_order_author_tail->find(ad.get_author_id()) !=
          storewide_bs_order_author_tail->end() ||
          storewide_bs_order_author_tail->find(ad.get_author_id() % 100) !=
          storewide_bs_order_author_tail->end();
    }
    is_storewide_bs_order_author = is_storewide_bs_order_author || enable_storewide_bs_align;
    // 全站直播全互斥
    if (ad.Is(AdFlag::is_merchant_live) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
        (is_storewide_bs_order_author && (p_ad->get_scene_oriented_type() ==
        kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE))) &&
        ad.get_storewide_incompatible_type() == 1 &&
        ad.get_price_ratio() > 0) {
      if (!enable_storewide_live_skip_bs_ab) {
        // 流量侧 或 作者侧实验（复用之前计划侧实验的配置）总开关
        if (enable_storewide_live_bs_ab || (storewide_bs_campaign_tail &&
            storewide_bs_campaign_tail->count(ad.get_author_id() % 100) > 0)) {
          if (enable_storewide_live_bs_ab_v2) {  // 子开关控制具体策略分支
            if (ad.get_price_ratio() > 1.2) {
              ratio = storewide_live_bs_overcharge_ratio;
            } else if (ad.get_price_ratio() < 0.8) {
              ratio = storewide_live_bs_discount_ratio;
              if (enable_storewide_live_bs_control &&
                  p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
                    ratio = 1.0;
                  }
              if (enable_skip_storewide_live_bs_tail) {
                if (skip_storewide_live_bs_tail &&
                    skip_storewide_live_bs_tail->count(ad.get_campaign_id() % 100) > 0 &&
                    p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
                      ratio = 1.0;
                    }
                  }
            } else {
              ratio = 1.0;
            }
          } else {
            ratio = ad.get_price_ratio();
          }
        }
      }
    }

    // PC 短视频 ROAS 控成本
    bool pc_photo_roas_adjust_bs = ad.Is(AdFlag::is_merchant_photo) &&
        (ad.get_speed() == kuaishou::ad::AdEnum::SPEED_FAST ||
         ad.get_speed() == kuaishou::ad::AdEnum::SPEED_COST_CAP) &&
        (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
         ad.get_project_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS);
    if (pc_photo_roas_adjust_bs) {
      ratio = ad.get_price_ratio() > 0.01 ? ad.get_price_ratio() : 1.0;
    }

    // 商品全站控成本
    bool merchant_storewide_adjust_bs = ad.Is(AdFlag::is_merchant_photo) &&
        ad.get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
        (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
         ad.get_scene_oriented_type() == 24);
    if (merchant_storewide_adjust_bs) {
      ratio = ad.get_price_ratio() > 0.01 ? ad.get_price_ratio() : 1.0;
    }

    bool enable_gyk_item_card_bs =
        session_data->get_pos_manager_base().IsGuessYouLike() &&
        p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
        p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
        p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP && item_card_photo_bs_tail &&
        item_card_photo_bs_tail->count(p_ad->get_unit_id() % 100) > 0;
    if (enable_gyk_item_card_bs_spdm && enable_gyk_item_card_bs) {
      ratio = ad.get_price_ratio() <= 1 ? gyk_item_card_bs_ratio : 1.0;
    }
    // Note(lixu05): 短带订单不适用 price_ratio
    bool enable_pc_photo_order_new_bs = p_ad->Is(AdFlag::is_merchant_photo) &&
      p_ad->get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
      p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
      p_ad->get_scene_oriented_type() != 24 &&
      p_ad->get_project_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED;
    if (enable_pc_photo_order_new_bs) {
      ratio = 1.0;
    }

    // cid 排序计费分离单独生效
    if ((ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS
          || ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID)) {
      ratio = ad.get_price_ratio() > 0 ? ad.get_price_ratio() : 1.0;
    }
    const std::string& ocpx_str = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    RANK_DOT_STATS(session_data, ratio * 1e3,
          "inner_billing_seperate_ratio", "native", ocpx_str,
          absl::StrCat(session_data->get_page_id()), ratio >= 1.0? "add" : "discount");
    // 内循环计费分离系数移到 adpack 乘
    bool inner_loop_bs_trans_to_adpack =
        (ad.Is(AdFlag::is_merchant_live) || ad.Is(AdFlag::is_merchant_item_photo)) &&
        ad.get_speed() == kuaishou::ad::AdEnum::SPEED_FAST &&
        (ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_EVENT_ORDER_PAID ||
         ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS);
    bool is_exp_tail;
    if (hosting_set.count(ad.get_scene_oriented_type()) > 0) {
      is_exp_tail = trans_campaign_tail && trans_campaign_tail->count(ad.get_campaign_id() % 100) > 0;
    } else {
      is_exp_tail = trans_unit_tail && trans_unit_tail->count(ad.get_unit_id() % 100) > 0;
    }
    inner_loop_bs_trans_to_adpack = inner_loop_bs_trans_to_adpack && is_exp_tail;
    if (inner_loop_bs_trans_to_adpack) {
      // 全站直播重新设置
      if (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE ||
          p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE) {
        ratio = ad.get_price_ratio() > 0.01 ? ad.get_price_ratio() : 1.0;
      }
      ad.set_gimbal_ratio(ratio);
      ad.set_gimbal_type(kuaishou::ad::GimbalType::GIMBAL_TYPE_INNER_LOOP);
      ratio = 1.0;
    }

    return ratio;
  };
  FINISH_COMPUTE()
}

void UnifyNativeFanstopV2NobidBS_Admit(ks::platform::AddibleRecoContextInterface* context,
                                  platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  std::shared_ptr<::ks::infra::TailNumberV2> fanstop_live_cap_exp_whitelist =
      RankKconfUtil::fanstopLiveCapExpAccountWhitelist();
  std::shared_ptr<absl::flat_hash_set<int64_t>> fanstop_test_whitelist_ocpx =
      RankKconfUtil::fanstopTestWhitelistOcpx();
  std::shared_ptr<absl::flat_hash_set<int64_t>> fanstop_bs_cap_exp_whitelist =
      RankKconfUtil::fanstopBsCapExpWhitelist();

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 只处理软广
    if (p_ad->Is(AdFlag::IsHardAd)) {
      return false;
    }
    bool is_nobid = p_ad->get_bid_type() == kuaishou::ad::AdEnum_BidType_OCPM_DSP &&
                    p_ad->get_bid_strategy() == kuaishou::ad::AdEnum_BidStrategy_SMART_BID_STRATEGY;
    // 仅 nobid
    if (!is_nobid) return false;
    // 粉条广告准入
    if (p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_FANSTOP_V2) {
      return false;
    }
    // 曝光计费的品牌助推, 作品速推屏蔽
    if (p_ad->get_fanstop_category() == kuaishou::ad::AdEnum::CATEGORY_BRAND_FANSTOP ||
        p_ad->get_fanstop_category() == kuaishou::ad::AdEnum::CATEGORY_SPEED_FANSTOP_PHOTO) {
      return false;
    }

    // 实验测试用
    int64_t account_id = p_ad->get_account_id();
    int64_t ocpx = p_ad->get_ocpx_action_type();
    if (fanstop_test_whitelist_ocpx
        && fanstop_test_whitelist_ocpx->count(ocpx) > 0
        && fanstop_live_cap_exp_whitelist
        && fanstop_live_cap_exp_whitelist->IsOnFor(account_id)) {
      return true;
    }
    // 目标优化目标 + 产品(闪光弹、热门卡)
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_AUDIENCE_FAST &&
        p_ad->get_fanstop_category() == kuaishou::ad::AdEnum::CATEGORY_FLASHBOMB_LIVE) {
      return true;
    }
    // 多种类型结合白名单
    // key: fanstop_category + photo0/live1 + ocpx + unit_tail
    // 0174012: 0(normal) 1(live) 740(ad_live_audience) 12(unit_tail)
    bool is_live = p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_FANS ||
                   p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_SHOW ||
                   p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_ALL;
    int64_t key = ((static_cast<int64_t>(p_ad->get_fanstop_category()) * 10 + (is_live?1:0)) * 1000
                   + static_cast<int64_t>(p_ad->get_ocpx_action_type())) * 100
                   + (p_ad->get_unit_id()%100);
    if (fanstop_bs_cap_exp_whitelist
        && fanstop_bs_cap_exp_whitelist->count(key) > 0) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}


void UnifyNativeFanstopV2NobidBS_Compute(ks::platform::AddibleRecoContextInterface* context,
                                    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double fanstop_global_price_ratio_ =
      SPDM_fanstop_global_price_ratio(session_data->get_spdm_ctx());
  double fanstop_price_ratio_upper_ =
      SPDM_fanstop_price_ratio_upper(session_data->get_spdm_ctx());
  double fanstop_price_ratio_lower_ =
      SPDM_fanstop_price_ratio_lower(session_data->get_spdm_ctx());
  bool disable_fanstopbs_price_ratio_bound =
      SPDM_disable_fanstopbs_price_ratio_bound(session_data->get_spdm_ctx());
  int64_t billing_separate_price_lower_ =
      SPDM_native_ad_bs_price_lower(session_data->get_spdm_ctx());
  int64_t billing_separate_price_upper_ =
      SPDM_native_ad_bs_price_upper(session_data->get_spdm_ctx());

  double fanstop_nobid_bs_ratio_lower_ =
      SPDM_fanstop_nobid_bs_ratio_lower(session_data->get_spdm_ctx());
  double fanstop_nobid_bs_ratio_upper_ =
      SPDM_fanstop_nobid_bs_ratio_upper(session_data->get_spdm_ctx());

  bool enable_fanstop_bscap_custom_bid_v2 =
      SPDM_enable_fanstop_bscap_custom_bid_v2(session_data->get_spdm_ctx());
  std::shared_ptr<absl::flat_hash_map<int64_t, double>> fanstop_bscap_custombid_map =
      RankKconfUtil::fanstopBsCapCustombidMap();
  bool enable_fanstop_bs_currentcpa_base =
      RankKconfUtil::enableFanstopBsCurrentCpaBase();

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    // 计算计费调整系数
    //   price' = price * (bid / auto_cpa_bid) * price_ratio
    //      cpa_auto_cpa_diff := auto_cpa_bid / bid
    //      price' = price / cpa_auto_cpa_diff * price_ratio
    double cpa_autocpa_diff = 1.0;
    if (fanstop_bscap_custombid_map == nullptr) return 1.0;

    double target_bid = p_ad->get_bid();
    auto iter_targetbid = fanstop_bscap_custombid_map->find(ad.get_author_id());
    if (iter_targetbid != fanstop_bscap_custombid_map->end()) {
      target_bid = iter_targetbid->second;
    } else {
      auto iter_targetbid = fanstop_bscap_custombid_map->find(ad.get_ocpx_action_type());
      if (iter_targetbid != fanstop_bscap_custombid_map->end()) {
        target_bid = iter_targetbid->second;
      }
    }
    if (enable_fanstop_bs_currentcpa_base) {
      target_bid = p_ad->get_auto_cpa_bid();
    }
    if (p_ad->get_auto_cpa_bid() != 0 && target_bid != 0)  {
      cpa_autocpa_diff = 1.0 * p_ad->get_auto_cpa_bid() / target_bid;
    }
    if (ad.get_price_ratio() == 0) {
      ad.set_price_ratio(1.0);
    }
    if (!disable_fanstopbs_price_ratio_bound) {
      ad.set_price_ratio(std::min(ad.get_price_ratio(),
          fanstop_price_ratio_upper_));
      ad.set_price_ratio(std::max(ad.get_price_ratio(),
          fanstop_price_ratio_lower_));
    }
    double ratio = ad.get_price_ratio() / cpa_autocpa_diff
        * fanstop_global_price_ratio_;
    bool is_photo = p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS ||
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_TO_SHOW ||
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL;
    if (enable_fanstop_bscap_custom_bid_v2 && is_photo
        && p_ad->get_auto_cpa_bid() != 0 && target_bid != 0) {
      double max_autobid = std::min(static_cast<double>(p_ad->get_auto_cpa_bid()),
          target_bid * ad.get_price_ratio());
      ratio = 1.0 / p_ad->get_auto_cpa_bid()
          * max_autobid
          * fanstop_global_price_ratio_;
    }

    // 调整计费
    int64_t old_price = ad.get_price();
    int64_t new_price = static_cast<int64_t>(ad.get_price() * ratio + 0.5);
    // 取 bound
    int64_t lower_new_price = std::max(billing_separate_price_lower_,
        static_cast<int64_t>(fanstop_nobid_bs_ratio_lower_ * old_price));
    int64_t upper_new_price = std::min(billing_separate_price_upper_,
        static_cast<int64_t>(fanstop_nobid_bs_ratio_upper_ * old_price));
    new_price = std::max(lower_new_price, std::min(upper_new_price, new_price));
    if (old_price > 0) {  // 重新计算一下取 bound 之后的 ratio
      ratio = 1.0 * new_price / old_price;
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void FanstopPrivateMessageLeadsDiscount_Admit(
          ks::platform::AddibleRecoContextInterface* context,
          platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_fanstop_prms_discount =
                  SPDM_enable_fanstop_prms_discount(session_data->get_spdm_ctx());
  auto admit = [&](auto& item) -> bool {
    if (!enable_fanstop_prms_discount) {
      return false;
    }

    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    auto ocpx = p_ad->get_ocpx_action_type();
    return p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 &&
           p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO &&
           (ocpx == kuaishou::ad::AdActionType::EVENT_PRIVATE_MESSAGE_SENT ||
            ocpx == kuaishou::ad::AdActionType::LEADS_SUBMIT);
  };
  FINISH_ADMIT()
}

void FanstopPrivateMessageLeadsDiscount_Compute(
          ks::platform::AddibleRecoContextInterface* context,
          ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto discount_ratio_map = RankKconfUtil::fanstopPrmsDiscountMap();
  bool enable_fanstop_discount_by_ft_type =
                  SPDM_enable_fanstop_discount_by_ft_type(session_data->get_spdm_ctx());
  double price_ratio_common = 1.0;
  double lowerbound = 0.55;
  auto get_price_ratio = [&](const std::string& key, double default_value) -> double {
    auto iter_sub = discount_ratio_map->find(key);
    if (iter_sub != discount_ratio_map->end()) {
      return iter_sub->second;
    }
    return default_value;
  };

  // weekday
  const std::string& weekday_str = absl::StrCat("weekday_", utility::GetWeekday());
  price_ratio_common *= get_price_ratio(weekday_str, 1.0);
  // lowerbound
  lowerbound = get_price_ratio("lowerbound", lowerbound);

  auto compute = [&](auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    double price_ratio = 1.0;

    if (!discount_ratio_map) return price_ratio;

    const std::string& ocpx_str = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    const std::string& account_id_str = absl::StrCat(p_ad->get_account_id());

    double account_ratio = get_price_ratio(account_id_str, -1.0);
    if (account_ratio > 0) {
      price_ratio = std::max(account_ratio, lowerbound);  // 这里不考虑 price_ratio_common
      RANK_DOT_STATS(session_data, price_ratio * 1000,
                      "fanstop_prms_discount_price_ratio", "account_id_str");
      return price_ratio;
    }
    // 排除掉 is_xiaodian == 1 的账户
    if (enable_fanstop_discount_by_ft_type) {
      int is_xiaodian = p_ad->Attr(ItemIdx::fd_ACCOUNT_fanstop_account_is_xiaodian)
                            .GetIntValue(p_ad->AttrIndex()).value_or(0);
      if (is_xiaodian == 1) {
        RANK_DOT_STATS(session_data, 1.0 * 1000,
                        "fanstop_prms_discount_price_ratio", "is_xiaodian");
        return 1.0;
      }
    }
    int combo_type = p_ad->Attr(ItemIdx::fd_UNIT_combo_type)
                          .GetIntValue(p_ad->AttrIndex()).value_or(0);
    const std::string& combo_type_str = absl::StrCat("combo_", combo_type);
    double combo_ratio = get_price_ratio(combo_type_str, -1.0);
    if (combo_ratio > 0) {
      price_ratio = std::max(combo_ratio*price_ratio_common, lowerbound);
      RANK_DOT_STATS(session_data, price_ratio * 1000,
                    "fanstop_prms_discount_price_ratio", combo_type_str);
      return price_ratio;
    }
    const std::string& scene_oriented_type_str = absl::StrCat("scene_oriented_type_",
                                                              p_ad->get_scene_oriented_type());
    double scene_oriented_ratio = get_price_ratio(scene_oriented_type_str, -1.0);
    if (scene_oriented_ratio > 0) {
      price_ratio = std::max(scene_oriented_ratio*price_ratio_common, lowerbound);
      RANK_DOT_STATS(session_data, price_ratio * 1000,
                    "fanstop_prms_discount_price_ratio", scene_oriented_type_str);
      return price_ratio;
    }
    double ocpx_ratio = get_price_ratio(ocpx_str, -1.0);
    if (ocpx_ratio > 0) {
      price_ratio = std::max(ocpx_ratio*price_ratio_common, lowerbound);
      RANK_DOT_STATS(session_data, price_ratio * 1000,
                    "fanstop_prms_discount_price_ratio", ocpx_str);
      return price_ratio;
    }
    return std::max(price_ratio, lowerbound);
  };
  FINISH_COMPUTE()
}

void AdjustPriceUpItems_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_up_items_adjust_price =
      SPDM_enable_up_items_adjust_price(session_data->get_spdm_ctx());
  int64_t page_id = session_data->get_page_id();

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()

    if (!p_ad->Is(AdFlag::is_inner_loop_ad)) {
      return false;
    }
    if (page_id != 10011 && page_id != 10002 && page_id != 11001 && page_id != 10008
        && page_id != 10003 && page_id != 11002) {
      return false;
    }
    if (enable_up_items_adjust_price) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceUpItems_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto up_items_price_ratio =
      SPDM_up_items_adjust_price_ratio(session_data->get_spdm_ctx());

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    int32_t native_degree_tag =
      p_ad->Attr(ItemIdx::fd_PHOTO_native_degree_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    if (native_degree_tag == 2) {
      ratio = up_items_price_ratio;
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceNewTopk_Admit(ks::platform::AddibleRecoContextInterface* context,
  platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_new_topk_adjust_price =
      SPDM_enable_new_topk_adjust_price(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    if (!p_ad->Is(AdFlag::is_inner_loop_ad)) {
      return false;
    }
    if (enable_new_topk_adjust_price) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceNewTopk_Compute(ks::platform::AddibleRecoContextInterface* context,
  ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    // 区分短带短引
    int cold_tag = 0;
    int32_t new_topk_tag = 0;
    if (p_ad->Is(AdFlag::is_photo_ad)) {
      cold_tag = p_ad->Attr(ItemIdx::fd_PHOTO_inner_photo_coldstart_tag).
          GetIntValue(p_ad->AttrIndex()).value_or(0);
      new_topk_tag = p_ad->Attr(ItemIdx::fd_PHOTO_photo_coldstart_item_score).GetIntValue(
        p_ad->AttrIndex()).value_or(0);
    } else if (p_ad->Is(AdFlag::is_p2l)) {
      cold_tag = p_ad->Attr(ItemIdx::fd_PHOTO_inner_p2l_coldstart_tag).
          GetIntValue(p_ad->AttrIndex()).value_or(0);
      new_topk_tag = p_ad->Attr(ItemIdx::fd_PHOTO_p2l_coldstart_item_score).GetIntValue(
        p_ad->AttrIndex()).value_or(0);
    }
    const auto &new_topk_tag_set = RankKconfUtil::NewTopkTag();
    auto enable_discount_cold_tag = SPDM_enable_discount_cold_tag(session_data->get_spdm_ctx());
    const auto &cold_tag_set = RankKconfUtil::DiscountColdTag();
    auto new_topk_adjust_price_ratio =
        SPDM_new_topk_adjust_price_ratio(session_data->get_spdm_ctx());
    double ratio = 1.0;
    if (enable_discount_cold_tag) {
      if (new_topk_tag_set->find(new_topk_tag) != new_topk_tag_set->end()
        && cold_tag_set->find(cold_tag) != cold_tag_set->end()) {
        ratio = new_topk_adjust_price_ratio;
      }
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceColdStart_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_coldstart_adjust_price =
      SPDM_enable_coldstart_adjust_price(session_data->get_spdm_ctx());

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()

    if (!p_ad->Is(AdFlag::is_inner_loop_ad) || !p_ad->Is(AdFlag::is_live_ad)) {
      return false;
    }
    if (enable_coldstart_adjust_price) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceColdStart_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    std::shared_ptr<ks::ad_base::kconf::ProtoKconf<
    ::ks::engine_base::kconf::AdMerchantConf>> ad_merchant_conf_ = engine_base::AdKconfUtil::adMerchantConf();
    if (nullptr == ad_merchant_conf_) {
      return ratio;
    }
    int64_t is_live_cold_start =
      p_ad->Attr(ItemIdx::fd_LIVE_inner_live_coldstart_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    int32_t seller_category = p_ad->get_seller_category();
    const std::string& page_id = absl::StrCat(session_data->get_page_id());
    std::string inner_coldstart_tag = engine_base::SPDM_inner_coldstart_tag(session_data->get_spdm_ctx());
    std::vector<std::string> tag_set = absl::StrSplit(inner_coldstart_tag, ",", absl::SkipEmpty());
    for (auto tag : tag_set) {
      auto state_iter = ad_merchant_conf_->data().state_info().find(tag);
      if (state_iter == ad_merchant_conf_->data().state_info().end()) {
        continue;
      }
      auto &state_info = state_iter->second;
      auto &seller_category_map = state_info.seller_category_price();
      auto &page_id_map = state_info.page_id();
      auto &is_live_cold_start_map = state_info.live_coldstart();

      bool is_seller_category_empty = seller_category_map.size() == 0;
      bool is_live_cold_start_valid = is_live_cold_start_map.size() == 0 ||
        is_live_cold_start_map.find(is_live_cold_start) != is_live_cold_start_map.end();
      bool is_page_valid = page_id_map.size() == 0 || page_id_map.find(page_id) != page_id_map.end();
      if (!(is_live_cold_start_valid && is_page_valid)) {
        continue;
      }
      if (!is_seller_category_empty) {
        auto iter = seller_category_map.find(seller_category);
        if (iter != seller_category_map.end()) {
          ratio = iter->second;
        }
      }
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceHighTr_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_high_tr_control =
      engine_base::SPDM_enable_high_tr_control(session_data->get_spdm_ctx());
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()

    if (!p_ad->Is(AdFlag::is_inner_loop_ad)) {
      return false;
    }
    if (enable_high_tr_control) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustPriceHighTr_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto high_tr_price_ratio =
      SPDM_high_tr_price_ratio(session_data->get_spdm_ctx());
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    if (p_ad->get_low_tr_bid_price_tag() == 1) {
      ratio = high_tr_price_ratio;
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustInnerHighValue_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto enable_inner_health_power_tag_ecpc =
      SPDM_enable_inner_health_power_tag_ecpc(session_data->get_spdm_ctx());
  auto enable_inner_adx_recall_boost =
      SPDM_enable_inner_adx_recall_boost(session_data->get_spdm_ctx());

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()

    if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
      return false;
    }
    if (enable_inner_health_power_tag_ecpc) {
      return true;
    }
    if (enable_inner_adx_recall_boost) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void AdjustInnerHighValue_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double ratio = 1.0;
    double inner_adx_recall_discount_ratio =
        SPDM_inner_adx_recall_discount_ratio(session_data->get_spdm_ctx());
    bool enable_inner_health_power_tag_ecpc =
        SPDM_enable_inner_health_power_tag_ecpc(session_data->get_spdm_ctx());
    bool enable_inner_adx_recall_boost =
        SPDM_enable_inner_adx_recall_boost(session_data->get_spdm_ctx());
    const std::string& health_power_tag =
        session_data->get_rank_request()->ad_request().ad_user_info().health_power_tag();
    std::string health_power_exp_tag =
      SPDM_health_power_exp_tag(session_data->get_spdm_ctx());
    auto inner_adx_recall_set =
        RankKconfUtil::innerAdxRecallSet();
    auto health_power_kconf =
        RankKconfUtil::innerHealthPowerKconf();
    auto health_power_industry_set =
        RankKconfUtil::innerHealthPowerIndustrySet();
    if (enable_inner_health_power_tag_ecpc &&
        health_power_tag != "" && health_power_exp_tag != "" &&
        health_power_industry_set->count(p_ad->get_second_industry_id_v5()) &&
        p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
      auto iter = health_power_kconf->find(
          absl::StrCat(health_power_tag, "_", health_power_exp_tag, "_discount"));
      if (iter != health_power_kconf->end()) {
        ratio = iter->second;
      }
      RANK_DOT_STATS(session_data, ratio * 1e3,
            "inner_health_power_discount", "discount_ratio",
            absl::StrCat(health_power_tag, health_power_exp_tag));
    }
    if (enable_inner_adx_recall_boost &&
        inner_adx_recall_set->count(p_ad->get_multi_retrieval_tag())) {
      ratio = inner_adx_recall_discount_ratio;
      RANK_DOT_STATS(session_data, ratio * 1e3,
            "inner_adx_recall_discount", "discount_ratio");
    }
    return ratio;
  };
  FINISH_COMPUTE()
}

void AdjustPriceInnerLoopBigCardSecondRequest_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  bool enable_adjust_price_innerloopbigcard_secondreq = SPDM_enable_adjust_price_innerloopbigcard_secondreq(session_data->get_spdm_ctx());  // NOLINT
  auto &ad_request = session_data->get_rank_request()->ad_request();
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    RANK_DOT_COUNT(session_data, 1, "AdjustPriceInnerLoopBigCardSecondRequest", "base");
    if (enable_adjust_price_innerloopbigcard_secondreq && ad_request.is_common_card_second_request() && session_data->get_rank_request()->ad_request().common_card_request_info().feed_card_ind() == kuaishou::ad::AdEnum::INNER_LOOP) {  // NOLINT
      RANK_DOT_COUNT(session_data, 1, "AdjustPriceInnerLoopBigCardSecondRequest", "true");
      return true;
    } else {
      return false;
    }
  };
  FINISH_ADMIT()
}

void AdjustPriceInnerLoopBigCardSecondRequest_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  double adjust_price_innerloopbigcard_secondreq_ratio = SPDM_adjust_price_innerloopbigcard_secondreq_ratio(session_data->get_spdm_ctx());  // NOLINT
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    return adjust_price_innerloopbigcard_secondreq_ratio;
  };
  FINISH_COMPUTE()
}

void ItemCardSelectedFlowDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto item_card_selected_flow_discount_tail = RankKconfUtil::itemCardSelectedFlowDiscountTail();

  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 商品卡非明投
    if (p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD
        && p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
      return false;
    }

    // 限定单双列
    if (session_data->get_is_thanos_request() == false) {
      return false;
    }

    // 在实验尾号中
    if (item_card_selected_flow_discount_tail
        && item_card_selected_flow_discount_tail->IsOnFor(p_ad->get_campaign_id())) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void ItemCardSelectedFlowDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto selected_flow_discount_ratio =  RankKconfUtil::itemCardSelectedFlowCampaignDiscountRatio();

  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()

    double ratio = 1.0;
    if (selected_flow_discount_ratio == nullptr)
      return ratio;

    int curr_tail_number = p_ad->get_campaign_id()  % 1000 / 100;
    // 未配置打折率 默认 1.0
    auto iter = selected_flow_discount_ratio->find(curr_tail_number);
    if (iter == selected_flow_discount_ratio->end())
      return ratio;
    ratio = iter->second;

    RANK_DOT_STATS(session_data, ratio * 1e3,
                "item_card.selected_flow_discount",
                "discount_ratio",
                kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()),
                kuaishou::ad::AdEnum_AdSceneOrientedTypeEnum_Name(p_ad->get_scene_oriented_type()));
    return ratio;
  };
  FINISH_COMPUTE()
}

void StorewideLowClevelDiscount_Admit(ks::platform::AddibleRecoContextInterface* context,
                               platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto storeWideLowClevelEcpc = RankKconfUtil::storeWideLowClevelEcpc();
  bool enable_storewide_low_clevel = SPDM_enableStorewideLowClevelEcpc();
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    bool store_wide_low_clevel = storeWideLowClevelEcpc &&
         storeWideLowClevelEcpc->IsOnFor(p_ad->get_author_id());
    int64_t max_ecpc_tag =
        p_ad->Attr(ItemIdx::max_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double max_ecpc_ratio =
        p_ad->Attr(ItemIdx::max_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    if (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE &&
     enable_storewide_low_clevel && store_wide_low_clevel && max_ecpc_tag == 400
     && max_ecpc_ratio > 1.0) {
      return true;
    }
    return false;
  };
  FINISH_ADMIT()
}

void StorewideLowClevelDiscount_Compute(ks::platform::AddibleRecoContextInterface* context,
                                 ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT();
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR();
    double max_ecpc_ratio =
      p_ad->Attr(ItemIdx::max_ecpc_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    int64_t max_ecpc_tag =
      p_ad->Attr(ItemIdx::max_ecpc_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double ratio = 1.0;
    if (max_ecpc_ratio > 1.0 && max_ecpc_tag == 400) {
      ratio = 1 / max_ecpc_ratio;
    }
    return ratio;
  };
  FINISH_COMPUTE();
}

void InnerLivePrice_Admit(ks::platform::AddibleRecoContextInterface* context,
    platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  auto admit = [&] (auto& item) -> bool {
    GET_PRICE_FACTOR_ADMIT_AD_PTR()
    // 内循环直播 剔除全站
    if (((p_ad->Is(AdFlag::is_inner_loop_deep_ad) && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) &&  // NOLINT
          !(p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE))) {
      if (SPDM_enable_inner_live_price(session_data->get_spdm_ctx())) {
        return true;
      }
    }
    return false;
  };
  FINISH_ADMIT()
}

void InnerLivePrice_Compute(ks::platform::AddibleRecoContextInterface* context,
    ks::platform::DataFrame* item_table, const FactorInfo& factor_info) {
  GET_PRICE_FACTOR_AD_CONTEXT()
  static absl::flat_hash_set<int64_t> hosting_without_storewide_set {
      AdEnum::ORIENTED_SMART_GOODS,
      AdEnum::ORIENTED_SMART_OPTIONAL_GOODS,
      AdEnum::ORIENTED_SMART_SINGLE_GOODS,
      AdEnum::ORIENTED_CID_SUPER_PRODUCT,
      AdEnum::ORIENTED_LIVE_HOSTING_PROJECT,
      AdEnum::ORIENTED_SEARCH_WORD_PROJECT,
      AdEnum::STORE_NEW_CUSTOMER_HOSTING,
      AdEnum::ORIENTED_SEARCH_WORD_LIVE_PROJECT,
      AdEnum::ORIENTED_ESP_MOBILE_HOSTING,
      AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE,
      AdEnum::ORIENTED_PRODUCT_TEST_HOSTING
  };
  auto compute = [&] (auto& item) -> double {
    GET_PRICE_FACTOR_COMPUTE_AD_PTR()
    double price_ratio = 1.0;
    const int64_t& page_id = session_data->get_page_id();
    const int64_t& nature_ad_tag =
        p_ad->Attr(ItemIdx::fd_AUTHOR_nature_ad_tag).GetIntValue(p_ad->AttrIndex()).value_or(0);
    const std::int64_t& gmv_level =
        p_ad->Attr(ItemIdx::gmv_level).GetIntValue(p_ad->AttrIndex()).value_or(0);
    std::string exp_tag = "";
    // 客户实验打 exp_tag
    const auto& author_conf_ptr = RankKconfUtil::innerLiveAuthorExpTag();
    if (author_conf_ptr) {
      int32_t campaign_div_num = author_conf_ptr->data().campaign_div_num();
      int32_t unit_div_num = author_conf_ptr->data().unit_div_num();
      std::string author_tail_num = "";
      const auto& exp_config = author_conf_ptr->data().exp_config();
      // 托管用 campaign_id 非托管用 unit_id
      if (hosting_without_storewide_set.count(p_ad->get_scene_oriented_type()) > 0) {
        if (campaign_div_num > 0) {
          author_tail_num = absl::StrCat("campaign_", p_ad->get_campaign_id() % campaign_div_num);  // NOLINT
        }
      } else {
        if (unit_div_num > 0) {
          author_tail_num = absl::StrCat("unit_", p_ad->get_unit_id() % unit_div_num);
        }
      }
      const auto& iter = exp_config.find(author_tail_num);
      if (iter != exp_config.end()) {
        exp_tag = iter->second;
      }
    }
    // 系数配置
    const auto& coef_kconf = RankKconfUtil::innerLiveCoefConf();
    if (coef_kconf) {
      const auto& exp_config = coef_kconf->data().exp_config();
      if (!exp_config.empty()) {
        const auto& iter = exp_config.find(exp_tag);
        if (iter != exp_config.end()) {
          if (!SPDM_enable_follow_cls_discount_by_gmv_level(session_data->get_spdm_ctx())) {
            // 分客户类型
            const auto& esp_coef_map = iter->second.esp_coef();
            const auto& esp_iter_coef = esp_coef_map.find(nature_ad_tag);
            if (esp_iter_coef != esp_coef_map.end()) {
              price_ratio = price_ratio * esp_iter_coef->second;
            }
          }
          // 分页面
          const auto& page_coef_map = iter->second.page_coef();
          const auto& page_iter_coef = page_coef_map.find(page_id);
          if (page_iter_coef != page_coef_map.end()) {
            price_ratio = price_ratio * page_iter_coef->second;
            // by gmv_level for cls
            if (SPDM_enable_follow_cls_discount_by_gmv_level(session_data->get_spdm_ctx())) {
              const auto& gmv_level_coef_map = iter->second.gmv_level_coef();
              const auto& gmv_level_coef_iter = gmv_level_coef_map.find(gmv_level);
              if (gmv_level_coef_iter != gmv_level_coef_map.end()) {
                price_ratio = price_ratio * gmv_level_coef_iter->second;
              }
              const auto& esp_coef_map = iter->second.esp_coef();
              const auto& esp_iter_coef = esp_coef_map.find(nature_ad_tag);
              if (esp_iter_coef != esp_coef_map.end()) {
                price_ratio = price_ratio * esp_iter_coef->second;
              }
            }
          }
        }
      }
    }
    RANK_DOT_STATS(session_data, price_ratio * 1e3,
                "inner_live_price_ratio",
                kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type()),
                kuaishou::ad::AdEnum_ItemType_Name(p_ad->get_item_type()),
                absl::StrCat(session_data->get_page_id()));
    return price_ratio;
  };
  FINISH_COMPUTE()
}

REGISTER_FUNCTION(AdjustPriceClientAiP0RerankNew_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceClientAiP0RerankNew_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceClientAiLiveAdRerankNew_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceClientAiLiveAdRerankNew_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceClientAiPhotoAdRerankNew_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceClientAiPhotoAdRerank_Compute, FunctionType::Compute)

REGISTER_FUNCTION(AdjustPriceBadPhotoRatio_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceBadPhotoRatio_Compute, FunctionType::Compute)
REGISTER_FUNCTION(PoQuanPriceDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(PoQuanPriceDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(IAAPGameROIDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(IAAPGameROIDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(GameBigRExplore_Admit, FunctionType::Admit)
REGISTER_FUNCTION(GameBigRExplore_Compute, FunctionType::Compute)
REGISTER_FUNCTION(InnerAdxEEOrientationDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(InnerAdxEEOrientationDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(InnerProductEEPriceDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(InnerProductEEPriceDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(PriceDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(PriceDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(DuanjuPriceDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(DuanjuPriceDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceLpsdeep_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceLpsdeep_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPricePrivateMessage_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPricePrivateMessage_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceSimplePromotion_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceSimplePromotion_Compute, FunctionType::Compute)
REGISTER_FUNCTION(OuterLivePriceDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(OuterLivePriceDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(InnerNewOrderDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(InnerNewOrderDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceExploreInner_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceExploreInner_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustExplorePrice_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustExplorePrice_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustIncentivePrice_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustIncentivePrice_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdStorewideUplift_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdStorewideUplift_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdStorewideMerchantUplift_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdStorewideMerchantUplift_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustFanstopFollowPrice_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustFanstopFollowPrice_Compute, FunctionType::Compute)
REGISTER_FUNCTION(InnerLivePrice_Admit, FunctionType::Admit)
REGISTER_FUNCTION(InnerLivePrice_Compute, FunctionType::Compute)
REGISTER_FUNCTION(InnerNewGuardN_Admit, FunctionType::Admit)
REGISTER_FUNCTION(InnerNewGuardN_Compute, FunctionType::Compute)
REGISTER_FUNCTION(InnerHighQualityPrice_Admit, FunctionType::Admit)
REGISTER_FUNCTION(InnerHighQualityPrice_Compute, FunctionType::Compute)
REGISTER_FUNCTION(InnerCtcvrCaliPrice_Admit, FunctionType::Admit)
REGISTER_FUNCTION(InnerCtcvrCaliPrice_Compute, FunctionType::Compute)
REGISTER_FUNCTION(MatrixAppPriceDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(MatrixAppPriceDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceNewProduct_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceNewProduct_Compute, FunctionType::Compute)
REGISTER_FUNCTION(InnerPriceDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(LocalLifePriceDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(InnerPriceDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(LocalLifePriceDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceGYL_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceGYL_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceSmbPriceRatio_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceSmbPriceRatio_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustInnerSelfServicePrice_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustInnerSelfServicePrice_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdMerchantPriceDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdMerchantPriceDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(InnerWhiteAccountDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(InnerWhiteAccountDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(PriceCidStra_Admit, FunctionType::Admit)
REGISTER_FUNCTION(PriceCidStra_Compute, FunctionType::Compute)
REGISTER_FUNCTION(PriceCidStraGoodsType_Admit, FunctionType::Admit)
REGISTER_FUNCTION(PriceCidStraGoodsType_Compute, FunctionType::Compute)
REGISTER_FUNCTION(UnifyHardBillingSeparate_Admit, FunctionType::Admit)
REGISTER_FUNCTION(UnifyHardBillingSeparate_Compute, FunctionType::Compute)
REGISTER_FUNCTION(UnifyNativeBS_Admit, FunctionType::Admit)
REGISTER_FUNCTION(UnifyNativeBS_Compute, FunctionType::Compute)
REGISTER_FUNCTION(UnifyNativeFanstopV2NobidBS_Admit, FunctionType::Admit)
REGISTER_FUNCTION(UnifyNativeFanstopV2NobidBS_Compute, FunctionType::Compute)
REGISTER_FUNCTION(GameStabilityDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(GameStabilityDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(KuaiGameInspireDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(KuaiGameInspireDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(FanstopPrivateMessageLeadsDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(FanstopPrivateMessageLeadsDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceUpItems_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceUpItems_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceColdStart_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceColdStart_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustInnerHighValue_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustInnerHighValue_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceInnerLoopBigCardSecondRequest_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceInnerLoopBigCardSecondRequest_Compute, FunctionType::Compute)
REGISTER_FUNCTION(FictionHotBookDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(FictionHotBookDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(ItemCardSelectedFlowDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(ItemCardSelectedFlowDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceHighTr_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceHighTr_Compute, FunctionType::Compute)
REGISTER_FUNCTION(StorewideLowClevelDiscount_Admit, FunctionType::Admit)
REGISTER_FUNCTION(StorewideLowClevelDiscount_Compute, FunctionType::Compute)
REGISTER_FUNCTION(AdjustPriceNewTopk_Admit, FunctionType::Admit)
REGISTER_FUNCTION(AdjustPriceNewTopk_Compute, FunctionType::Compute)
}   // namespace ad_rank
}   // namespace ks
