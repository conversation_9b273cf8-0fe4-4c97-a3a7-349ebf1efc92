// 迁移自 NativeUnifyCalcEcpmPlugin::CalcEcpmDeepTwinBid
#include "teams/ad/ad_rank/processor/factor/ecpm/ecpm_adjust/ecpm_deep_twin_bid.h"

#include <algorithm>
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_base/src/common/common.h"

namespace ks::ad_rank {

bool EcpmDeepTwinBid::Initialize() {
  return true;
}

bool EcpmDeepTwinBid::IsAdmit(const AdCommon* p_ad) {
  if (!p_ad->Is(AdFlag::IsNativeAd)) {
    return false;
  }
  return p_ad->Is(AdFlag::is_deep_bid_twin) &&
         p_ad->get_twin_bid_strategy() == kuaishou::ad::AdEnum::DEEP_MIN_BID;
}

int64_t EcpmDeepTwinBid::Compute(const AdCommon* p_ad) {
  double pdcvr = p_ad->get_unify_deep_cvr_info().value;
  double ecpm = p_ad->get_auction_bid();
  if (pdcvr <= 1e-5 || pdcvr >= 1.0 || p_ad->get_cpa_bid() <= 0 || p_ad->get_deep_cpa_bid() <= 0) {
    return 0;  // 原 return 0，直接过滤广告；如果 deep_cvr 不合法，不使用深度双出价也合理。
  }
  double target_rate = 1.0 * p_ad->get_cpa_bid() / p_ad->get_deep_cpa_bid();
  auto coef = p_ad->get_deep_min_bid_coef();

  // 计算 ecpm
  if (!(target_rate > 0)) {
    return 0;
  }
  double adjust_ratio = coef / target_rate * (pdcvr - target_rate);
  adjust_ratio = std::min(std::max(adjust_ratio, -1.0), 1.0);
  return ecpm * adjust_ratio;
}

REGISTER_FACTOR_UDF(EcpmDeepTwinBid, int64_t)
}  // namespace ks::ad_rank
