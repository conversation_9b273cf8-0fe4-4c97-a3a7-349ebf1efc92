#include "teams/ad/ad_rank/processor/factor/qcpx_strategy_live/qcpx_strategy.h"

#include <algorithm>
#include <cstdint>
#include <memory>
#include <numeric>
#include <string>
#include <vector>

#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/ad_proto/maven/ad/coupon/ad_coupon_cache.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"

using kuaishou::ad::AdEnum;
namespace ks {
namespace ad_rank {

const char* QcpxLiveStrategy::Name() {
  return "qcpx_live_strategy";
}

void QcpxLiveStrategy::InitParams() {
  // *********** request 粒度 参数 & 变量 ***********
  params_.sub_page_id = session_data_->get_sub_page_id();
  params_.is_thanos_request = session_data_->get_is_thanos_request();
  // 内部测试字段 修改请确认 @fandi
  params_.user_id = session_data_->get_user_id();
  if (RankKconfUtil::enableQcpxTestUserList() && RankKconfUtil::qcpxTestUserList() != nullptr) {  // NOLINT
    params_.is_qcpx_test_user = RankKconfUtil::qcpxTestUserList()->count(params_.user_id) > 0;
    params_.qcpx_test_user_boost_ratio = RankKconfUtil::qcpxTestUserBoostRatio();
  }
  params_.enable_qcpx_ctr_bound = RankKconfUtil::enableQcpxCtrBound();
  // spdm
  params_.enable_inner_pec_holdout = SPDM_enable_inner_pec_holdout(session_data_->get_spdm_ctx());
  params_.enable_inner_qcpx_open_holdout = SPDM_enable_inner_qcpx_open_holdout(session_data_->get_spdm_ctx());

  params_.enable_qcpx_live_full_stage_model =
    SPDM_enable_qcpx_live_full_stage_model(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_low_coupon_amount_adjust_run =
    SPDM_enable_qcpx_live_low_coupon_amount_adjust_run(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_low_coupon_amount_adjust =
    SPDM_enable_qcpx_live_low_coupon_amount_adjust(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_low_coupon_amount_tag =
    SPDM_enable_qcpx_live_low_coupon_amount_tag(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_a_thres_lower =
    SPDM_value_qcpx_live_roi_tag_a_thres_lower(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_a_thres_upper =
    SPDM_value_qcpx_live_roi_tag_a_thres_upper(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_a_ori_q =
    SPDM_value_qcpx_live_roi_tag_a_ori_q(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_a_new_q =
    SPDM_value_qcpx_live_roi_tag_a_new_q(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_b_thres_lower =
    SPDM_value_qcpx_live_roi_tag_b_thres_lower(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_b_thres_upper =
    SPDM_value_qcpx_live_roi_tag_b_thres_upper(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_b_ori_q =
    SPDM_value_qcpx_live_roi_tag_b_ori_q(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_b_new_q =
    SPDM_value_qcpx_live_roi_tag_b_new_q(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_c_thres_lower =
    SPDM_value_qcpx_live_roi_tag_c_thres_lower(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_c_thres_upper =
    SPDM_value_qcpx_live_roi_tag_c_thres_upper(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_c_ori_q =
    SPDM_value_qcpx_live_roi_tag_c_ori_q(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_tag_c_new_q =
    SPDM_value_qcpx_live_roi_tag_c_new_q(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_low_ori_cvr_coupon_amount_rise =
    SPDM_enable_qcpx_live_low_ori_cvr_coupon_amount_rise(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_low_ori_cvr_thres =
    SPDM_value_qcpx_live_low_ori_cvr_thres(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_low_ori_cvr_cpa_bid_n =
    SPDM_value_qcpx_live_low_ori_cvr_cpa_bid_n(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_low_ori_cvr_no_thres_coupon =
    SPDM_enable_qcpx_live_low_ori_cvr_no_thres_coupon(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_low_ori_cvr_effect_u0 =
    SPDM_enable_qcpx_live_low_ori_cvr_effect_u0(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_price_coupon_ratio =
    SPDM_value_qcpx_live_price_coupon_ratio(session_data_->get_spdm_ctx());
  params_.qcpx_live_min_coupon_amount_yuan =
    SPDM_qcpx_live_min_coupon_amount_yuan(session_data_->get_spdm_ctx());
  params_.qcpx_live_max_coupon_amount_yuan =
    SPDM_qcpx_live_max_coupon_amount_yuan(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_model_use_storewide_roas =
    SPDM_enable_qcpx_live_model_use_storewide_roas(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_model_use_t7_roi =
    SPDM_enable_qcpx_live_model_use_t7_roi(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_only_orderpay =
    SPDM_enable_qcpx_live_only_orderpay(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_bspline_model =
    SPDM_enable_qcpx_live_bspline_model(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_taylor_bspline_model =
    SPDM_enable_qcpx_live_taylor_bspline_model(session_data_->get_spdm_ctx());
  params_.enable_random_coloring_uplift =
    SPDM_enable_random_coloring_uplift(session_data_->get_spdm_ctx());
  params_.random_coloring_flow_percent =
    SPDM_random_coloring_flow_percent(session_data_->get_spdm_ctx());
  params_.enable_qcpx_random_color_v2 =
    SPDM_enable_qcpx_random_color_v2(session_data_->get_spdm_ctx());
  params_.enable_qcpx_before_bagging_cvr_log =
    SPDM_enable_qcpx_before_bagging_cvr_log(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_sigmoid_transform =
    SPDM_value_qcpx_live_sigmoid_transform(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_sigmoid_transform =
    SPDM_enable_qcpx_live_sigmoid_transform(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_clip_uplift =
    SPDM_enable_qcpx_live_clip_uplift(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_clip_uplift =
    SPDM_value_qcpx_live_clip_uplift(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_orderpay_pcvr_update =
    SPDM_enable_qcpx_live_orderpay_pcvr_update(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_rct_check_update =
    SPDM_enable_qcpx_live_rct_check_update(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_orderpay_bid_update =
    SPDM_enable_qcpx_live_orderpay_bid_update(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_orderpay_roas_update =
    SPDM_enable_qcpx_live_orderpay_roas_update(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_orderpay_storewide_update =
    SPDM_enable_qcpx_live_orderpay_storewide_update(session_data_->get_spdm_ctx());

  params_.qcpx_live_coupon_adjust_roi_bound =
    SPDM_qcpx_live_coupon_adjust_roi_bound(session_data_->get_spdm_ctx());
  params_.qcpx_live_coupon_adjust_roi_bound_open =
    SPDM_value_qcpx_live_whitebox_roi_bound(session_data_->get_spdm_ctx());
  params_.qcpx_live_roas_reduce_roi_bound =
    SPDM_qcpx_live_roas_reduce_roi_bound(session_data_->get_spdm_ctx());
  params_.qcpx_live_order_discount_roi_bound =
    SPDM_qcpx_live_order_discount_roi_bound(session_data_->get_spdm_ctx());
  params_.shelf_merchant_live_qcpx_roi_ratio =
    SPDM_shelf_merchant_live_qcpx_roi_ratio(session_data_->get_spdm_ctx());
  params_.shelf_merchant_live_qcpx_roi_ratio_v1 =
    SPDM_shelf_merchant_live_qcpx_roi_ratio_v1(session_data_->get_spdm_ctx());
  params_.follow_live_qcpx_roi_ratio =
    SPDM_follow_live_qcpx_roi_ratio(session_data_->get_spdm_ctx());

  params_.enable_live_qcpx_other_coupon_thre =
    SPDM_enable_live_qcpx_other_coupon_thre(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_coupon_thre_type =
    SPDM_value_qcpx_live_coupon_thre_type(session_data_->get_spdm_ctx());
  params_.enable_qcpx_holdout_no_rct =
    SPDM_enable_qcpx_holdout_no_rct(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_roas_update_disable_live =
    SPDM_enable_qcpx_live_roas_update_disable_live(session_data_->get_spdm_ctx());
  params_.specific_amount_coupon_id =
    SPDM_value_qcpx_live_specific_amount_coupon_id(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_rate_coupon_roi_bound =
    SPDM_value_qcpx_live_rate_coupon_roi_bound(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_rate_coupon_roi_bound_white =
    SPDM_value_qcpx_live_rate_coupon_roi_bound_white(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_roas_only_rate_coupon =
    SPDM_enable_qcpx_live_roas_only_rate_coupon(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_roas_use_rate_coupon =
    SPDM_enable_qcpx_live_roas_use_rate_coupon(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_order_use_disc_coupon =
    SPDM_enable_qcpx_live_order_use_disc_coupon(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_roas_use_reduce_coupon =
    SPDM_enable_qcpx_live_roas_use_reduce_coupon(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_roas_use_realtime_atv_for_bid =
    SPDM_enable_qcpx_live_roas_use_realtime_atv_for_bid(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_cvr_order_disc_onemodel_bagging =
    SPDM_enable_qcpx_live_cvr_order_disc_onemodel_bagging(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_cvr_roas_reduce_onemodel_bagging =
    SPDM_enable_qcpx_live_cvr_roas_reduce_onemodel_bagging(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_rate_coupon_only_live =
    SPDM_enable_qcpx_live_rate_coupon_only_live(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_rate_coupon_with_capped =
    SPDM_enable_qcpx_live_rate_coupon_with_capped(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_bound_rate_coupon_rct =
    SPDM_enable_qcpx_live_bound_rate_coupon_rct(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_bound_rate_coupon_model =
    SPDM_enable_qcpx_live_bound_rate_coupon_model(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_bound_rate_coupon_rct_right =
    SPDM_value_qcpx_live_bound_rate_coupon_rct_right(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_bound_rate_coupon_rct_left =
    SPDM_value_qcpx_live_bound_rate_coupon_rct_left(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_bound_rate_coupon_model_right =
    SPDM_value_qcpx_live_bound_rate_coupon_model_right(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_bound_rate_coupon_model_left =
    SPDM_value_qcpx_live_bound_rate_coupon_model_left(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_model_flow_percent =
    SPDM_value_qcpx_live_model_flow_percent(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_rct_amount_control_flow_percent =
    SPDM_value_qcpx_live_rct_amount_control_flow_percent(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_rct_amount_treatment_flow_percent =
    SPDM_value_qcpx_live_rct_amount_treatment_flow_percent(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_rct_rate_control_flow_percent =
    SPDM_value_qcpx_live_rct_rate_control_flow_percent(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_rct_rate_treatment_flow_percent =
    SPDM_value_qcpx_live_rct_rate_treatment_flow_percent(session_data_->get_spdm_ctx());
  params_.enable_qcpx_p2l_cvr_calib =
    SPDM_enable_qcpx_p2l_cvr_calib(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_max_uplift_cvr =
    SPDM_enable_qcpx_live_max_uplift_cvr(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_specific_amount_coupon_id =
    SPDM_enable_qcpx_live_specific_amount_coupon_id(session_data_->get_spdm_ctx());
  params_.enable_rct_pred_to_log =
    SPDM_enable_rct_pred_to_log(session_data_->get_spdm_ctx());
  params_.enable_change_p2l_thre_when_delivery =
    SPDM_enable_change_p2l_thre_when_delivery(session_data_->get_spdm_ctx());
  params_.enable_change_live_thre_when_delivery =
    SPDM_enable_change_live_thre_when_delivery(session_data_->get_spdm_ctx());
  params_.value_p2l_thre_ratio_when_delivery =
    SPDM_value_p2l_thre_ratio_when_delivery(session_data_->get_spdm_ctx());
  params_.value_live_thre_ratio_when_delivery =
    SPDM_value_live_thre_ratio_when_delivery(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_cpa_bid_amt_thre_order =
    SPDM_enable_qcpx_live_cpa_bid_amt_thre_order(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_cpa_bid_amt_thre_roas =
    SPDM_enable_qcpx_live_cpa_bid_amt_thre_roas(session_data_->get_spdm_ctx());
  params_.qcpx_live_cpa_bid_amt_thre_lower_bound =
    SPDM_qcpx_live_cpa_bid_amt_thre_lower_bound(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_roas_update_cvr_cali =
    SPDM_enable_qcpx_live_roas_update_cvr_cali(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roas_update_live_cvr_cali =
    SPDM_value_qcpx_live_roas_update_live_cvr_cali(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roas_update_p2l_cvr_cali =
    SPDM_value_qcpx_live_roas_update_p2l_cvr_cali(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_cvr_disc_roas_multi_head_model =
    SPDM_enable_qcpx_live_cvr_disc_roas_multi_head_model(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_skip_low_roi =
    SPDM_enable_qcpx_live_skip_low_roi(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_skip_low_roi_threshold =
    SPDM_value_qcpx_live_skip_low_roi_threshold(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_skip_low_roi_roas =
    SPDM_enable_qcpx_live_skip_low_roi_roas(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_skip_low_roi_order =
    SPDM_enable_qcpx_live_skip_low_roi_order(session_data_->get_spdm_ctx());
  params_.enable_bspline_to_log =
    SPDM_enable_bspline_to_log(session_data_->get_spdm_ctx());
  params_.enable_thre_to_log =
    SPDM_enable_thre_to_log(session_data_->get_spdm_ctx());
  params_.enable_live_qcpx_p2l_ctr_uplift =
    SPDM_enable_live_qcpx_p2l_ctr_uplift(session_data_->get_spdm_ctx());
  params_.enable_live_qcpx_live_ctr_one_model_uplift =
    SPDM_enable_live_qcpx_live_ctr_one_model_uplift(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_ctr_adjust =
    SPDM_enable_qcpx_live_ctr_adjust(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_ctr_disc_adjust_coef =
    SPDM_value_qcpx_live_ctr_disc_adjust_coef(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_ctr_reduce_adjust_coef =
    SPDM_value_qcpx_live_ctr_reduce_adjust_coef(session_data_->get_spdm_ctx());
  params_.enable_qcpx_rewarded =
    SPDM_enable_qcpx_rewarded(session_data_->get_spdm_ctx());
  params_.enable_live_qcpx_live_ctr_one_model_piecewise_uplift =
    SPDM_enable_live_qcpx_live_ctr_one_model_piecewise_uplift(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_roas_rate_coupon_use_customer_bid =
    SPDM_enable_qcpx_live_roas_rate_coupon_use_customer_bid(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_roas_rate_coupon_not_minus_c =
    SPDM_enable_qcpx_live_roas_rate_coupon_not_minus_c(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_ctr_ratio_bound =
    SPDM_value_qcpx_live_ctr_ratio_bound(session_data_->get_spdm_ctx());
  params_.enable_qcpx_upper_bound_cpa_bid_coef =
    SPDM_enable_qcpx_upper_bound_cpa_bid_coef(session_data_->get_spdm_ctx());
  params_.value_qcpx_upper_bound_cpa_bid_coef =
    SPDM_value_qcpx_upper_bound_cpa_bid_coef(session_data_->get_spdm_ctx());
  params_.value_qcpx_upper_bound_cpa_bid_coef_open =
    SPDM_value_qcpx_upper_bound_cpa_bid_coef_open(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_cpa_bid_amt_thre_order_rct =
    SPDM_enable_qcpx_live_cpa_bid_amt_thre_order_rct(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_no_threshold =
    SPDM_enable_qcpx_live_no_threshold(session_data_->get_spdm_ctx());
  params_.enable_qcpx_discount_coupon_gmv_price_ratio =
    SPDM_enable_qcpx_discount_coupon_gmv_price_ratio(session_data_->get_spdm_ctx());
  params_.value_qcpx_discount_coupon_gmv_price_ratio_1d_weight =
    SPDM_value_qcpx_discount_coupon_gmv_price_ratio_1d_weight(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_storewide_use_auto_roas =
    SPDM_enable_qcpx_live_storewide_use_auto_roas(session_data_->get_spdm_ctx());
  params_.enable_qcpx_expire_minutes = false;
  params_.enable_p2l_multi_predict =
    SPDM_enable_p2l_multi_predict(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_cvr_uplift_bound =
    SPDM_enable_qcpx_live_cvr_uplift_bound(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_cvr_uplift_bound =
    SPDM_value_qcpx_live_cvr_uplift_bound(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_roas_sep_roi_coef =
    SPDM_enable_qcpx_live_roas_sep_roi_coef(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_merchant_roas_sep_roi_coef =
    SPDM_value_qcpx_live_merchant_roas_sep_roi_coef(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_t7_roas_sep_roi_coef =
    SPDM_value_qcpx_live_t7_roas_sep_roi_coef(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_storewide_roas_sep_roi_coef =
    SPDM_value_qcpx_live_storewide_roas_sep_roi_coef(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roas_sep_roi_coef_non_open_ratio =
    SPDM_value_qcpx_live_roas_sep_roi_coef_non_open_ratio(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_add_nonq_reason =
    SPDM_enable_qcpx_live_add_nonq_reason(session_data_->get_spdm_ctx());
  params_.disable_shelf_qcpx_live_disc_coupon =
    SPDM_disable_shelf_qcpx_live_disc_coupon(session_data_->get_spdm_ctx()) && session_data_->get_pos_manager_base().IsShelfMerchantTraffic();  // NOLINT
  params_.enable_qcpx_no_thres_blacklist =
    SPDM_enable_qcpx_no_thres_blacklist(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_actual_storewide_bid_update =
    SPDM_enable_qcpx_live_actual_storewide_bid_update(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_amt_cpm_bid_update =
    SPDM_enable_qcpx_live_amt_cpm_bid_update(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_nobid_bid_update =
    SPDM_enable_qcpx_live_nobid_bid_update(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_p2l_roi_bound_seperate =
    SPDM_enable_qcpx_live_p2l_roi_bound_seperate(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_bound_p2l_open =
    SPDM_value_qcpx_live_roi_bound_p2l_open(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_roi_bound_p2l_pec =
    SPDM_value_qcpx_live_roi_bound_p2l_pec(session_data_->get_spdm_ctx());
  params_.value_qcpx_unify_ROI_pacing_p2l_storewide =
    SPDM_value_qcpx_unify_ROI_pacing_p2l_storewide(session_data_->get_spdm_ctx());
  params_.value_qcpx_unify_ROI_pacing_p2l_t7 =
    SPDM_value_qcpx_unify_ROI_pacing_p2l_t7(session_data_->get_spdm_ctx());
  params_.enable_qcpx_p2l_ctr_cali =
    SPDM_enable_qcpx_p2l_ctr_cali(session_data_->get_spdm_ctx());
  params_.value_qcpx_p2l_ctr_cali_coef =
    SPDM_value_qcpx_p2l_ctr_cali_coef(session_data_->get_spdm_ctx());
  params_.enable_qcpx_use_realtime_author_gmv_price_ratio =
    SPDM_enable_qcpx_use_realtime_author_gmv_price_ratio(session_data_->get_spdm_ctx());
  params_.enable_fix_qcpx_live_disc_bid_ratio =
    SPDM_enable_fix_qcpx_live_disc_bid_ratio(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_allocate_flow_v2 =
    SPDM_enable_qcpx_live_allocate_flow_v2(session_data_->get_spdm_ctx());
  // 实时数据兜底开关
  params_.enable_qcpx_live_realtime_atv_3h_safety =
    SPDM_enable_qcpx_live_realtime_atv_3h_safety(session_data_->get_spdm_ctx());
  // 订单发折扣券低 bid 屏蔽 (单位：元)
  params_.enable_qcpx_live_order_disc_bid_shield =
    SPDM_enable_qcpx_live_order_disc_bid_shield(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_order_disc_bid_threshold =
    SPDM_value_qcpx_live_order_disc_bid_threshold(session_data_->get_spdm_ctx());
  // order disc bid ratio fix
  params_.enable_qcpx_live_order_disc_use_gmv_price_ratio =
    SPDM_enable_qcpx_live_order_disc_use_gmv_price_ratio(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_order_disc_antou_reuse_roas =
    SPDM_enable_qcpx_live_order_disc_antou_reuse_roas(session_data_->get_spdm_ctx());
  // ctr shield depress
  params_.enable_qcpx_live_ctr_shield_depress =
    SPDM_enable_qcpx_live_ctr_shield_depress(session_data_->get_spdm_ctx());
  // Onemodel 相关
  params_.enable_inner_live_qcpx_roas =
    SPDM_enable_inner_live_qcpx_roas(session_data_->get_spdm_ctx());
  params_.enable_inner_live_qcpx_order =
    SPDM_enable_inner_live_qcpx_order(session_data_->get_spdm_ctx());
  params_.enable_inner_live_qcpx_t7roas =
    SPDM_enable_inner_live_qcpx_t7roas(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_cvr_onemodel_bagging = false;
  params_.enable_qcpx_random_color_log_cf_q =
    SPDM_enable_qcpx_random_color_log_cf_q(session_data_->get_spdm_ctx());
  params_.inner_qcpx_order_cvr_bagging_ratio =
    SPDM_inner_qcpx_order_cvr_bagging_ratio(session_data_->get_spdm_ctx());
  params_.inner_qcpx_roas_cvr_bagging_ratio =
    SPDM_inner_qcpx_roas_cvr_bagging_ratio(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_high_gmv_diff_depress_roas_reduce =
    SPDM_enable_qcpx_live_high_gmv_diff_depress_roas_reduce(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_pgmv_min_diff_ratio_threshold =
    SPDM_value_qcpx_live_pgmv_min_diff_ratio_threshold(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_high_gmv_diff_order_count_threshold =
    SPDM_value_qcpx_live_high_gmv_diff_order_count_threshold(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_high_gmv_diff_roi_pacing_coef_slope =
    SPDM_value_qcpx_live_high_gmv_diff_roi_pacing_coef_slope(session_data_->get_spdm_ctx());
  if (SPDM_enable_shelf_merchant_live_qcpx_roi(session_data_->get_spdm_ctx())
    && session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
      params_.qcpx_live_coupon_adjust_roi_bound *=
        params_.shelf_merchant_live_qcpx_roi_ratio;
      params_.qcpx_live_coupon_adjust_roi_bound_open *=
        params_.shelf_merchant_live_qcpx_roi_ratio;
      params_.value_qcpx_live_rate_coupon_roi_bound *=
        params_.shelf_merchant_live_qcpx_roi_ratio;
      params_.value_qcpx_live_rate_coupon_roi_bound_white *=
        params_.shelf_merchant_live_qcpx_roi_ratio;
  } else if (SPDM_enable_follow_live_qcpx_roi(session_data_->get_spdm_ctx())
    && session_data_->get_pos_manager_base().IsFollow()) {
      params_.qcpx_live_coupon_adjust_roi_bound *=
        params_.follow_live_qcpx_roi_ratio;
      params_.qcpx_live_coupon_adjust_roi_bound_open *=
        params_.follow_live_qcpx_roi_ratio;
      params_.value_qcpx_live_rate_coupon_roi_bound *=
        params_.follow_live_qcpx_roi_ratio;
      params_.value_qcpx_live_rate_coupon_roi_bound_white *=
        params_.follow_live_qcpx_roi_ratio;
  }

  if (SPDM_enable_shelf_merchant_live_qcpx_roi_v1(session_data_->get_spdm_ctx())
    && session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
      params_.qcpx_live_coupon_adjust_roi_bound *=
        params_.shelf_merchant_live_qcpx_roi_ratio_v1;
      params_.qcpx_live_coupon_adjust_roi_bound_open *=
        params_.shelf_merchant_live_qcpx_roi_ratio_v1;
      params_.value_qcpx_live_rate_coupon_roi_bound *=
        params_.shelf_merchant_live_qcpx_roi_ratio_v1;
      params_.value_qcpx_live_rate_coupon_roi_bound_white *=
        params_.shelf_merchant_live_qcpx_roi_ratio_v1;
  }
  // 暗投优化
  params_.enable_qcpx_live_antou_order_confident_when_antou_ge_cpa =
    SPDM_enable_qcpx_live_antou_order_confident_when_antou_ge_cpa(session_data_->get_spdm_ctx());  // NOLINT
  params_.enable_qcpx_live_antou_order_confident_when_antou_le_cpa =
    SPDM_enable_qcpx_live_antou_order_confident_when_antou_le_cpa(session_data_->get_spdm_ctx());  // NOLINT
  params_.enable_qcpx_live_antou_order_unconfident_when_antou_ge_cpa =
    SPDM_enable_qcpx_live_antou_order_unconfident_when_antou_ge_cpa(session_data_->get_spdm_ctx());  // NOLINT
  params_.enable_qcpx_live_antou_order_unconfident_when_antou_le_cpa =
    SPDM_enable_qcpx_live_antou_order_unconfident_when_antou_le_cpa(session_data_->get_spdm_ctx());  // NOLINT
  params_.enable_qcpx_live_minmax_use_unify_bid =
    SPDM_enable_qcpx_live_minmax_use_unify_bid(session_data_->get_spdm_ctx());  // NOLINT
  params_.enable_qcpx_rct_disc_skip_low_cpa =
    SPDM_enable_qcpx_rct_disc_skip_low_cpa(session_data_->get_spdm_ctx());  // NOLINT
  // 商家白名单 调整系数
  params_.enable_qcpx_author_roi_pacing =
    SPDM_enable_qcpx_author_roi_pacing(session_data_->get_spdm_ctx());
  params_.value_qcpx_author_roi_pacing =
    SPDM_value_qcpx_author_roi_pacing(session_data_->get_spdm_ctx());
  params_.author_roi_pacing_set = RankKconfUtil::qcpxAuthorPacingSet();
  params_.enable_qcpx_live_optimal_style_disable_feed_card_rct =
    SPDM_enable_qcpx_live_optimal_style_disable_feed_card_rct(session_data_->get_spdm_ctx());
  params_.enable_qcpx_live_search_and_push =
    SPDM_enable_qcpx_live_search_and_push(session_data_->get_spdm_ctx());
  params_.value_qcpx_live_optimal_style_disable_feed_card_rct_pp =
    SPDM_value_qcpx_live_optimal_style_disable_feed_card_rct_pp(session_data_->get_spdm_ctx());
  // config
  params_.coupon_config = RankKconfUtil::qcpxLiveCouponConfig();
  params_.coupon_lib = RankKconfUtil::qcpxCouponLib();
  params_.coupon_access = RankKconfUtil::qcpxCouponAccess();
  params_.dark_author_list = RankKconfUtil::qcpxLiveDarkAuthorList();
  params_.author_filter_list = RankKconfUtil::qcpxLiveAuthorFilterList();
  params_.specific_user_list = RankKconfUtil::qcpxSpecificUserList();
  params_.user_roi_coef_map = RankKconfUtil::qcpxLiveUserRoiCoefMap();
  params_.live_hour_roi_map = RankKconfUtil::qcpxLiveRoiHourMap();
  params_.p2l_hour_roi_map = RankKconfUtil::qcpxP2lRoiHourMap();
  params_.p2l_hard_calib_map = RankKconfUtil::qcpxP2lHardCvrCalibMap();
  params_.p2l_soft_calib_map = RankKconfUtil::qcpxP2lSoftCvrCalibMap();
  // buffer
  params_.coupon_select.clear();
  params_.pctr_uplift_ratios.clear();
  params_.pcvr_uplift_ratios.clear();
  params_.rate_multi_head_output.clear();
  // coupon_table
  params_.coupon_table = session_data_->common_r_->GetTable("ad_coupon_table");
  if (params_.coupon_table) {
    params_.coupon_type_attr = params_.coupon_table->GetAttr("fd_ad_coupon_template_coupon_type");
    params_.coupon_status_attr = params_.coupon_table->GetAttr("fd_ad_coupon_template_status");
    params_.coupon_rule_attr = params_.coupon_table->GetAttr("fd_ad_coupon_template_rule");
  }
  // 监控
  params_.coupon_delivery_cnt.clear();
  // roas bagging tmp
  params_.bagging_residual_cvr_ratio = 0.0;
  params_.bagging_onemodel_cvr_ratio = 0.0;
}

void QcpxLiveStrategy::InitAdParams(AdCommon* p_ad) {
  // *********** ad 粒度 参数 & 变量 ***********
  // 搜后推
  params_.inner_qcpx_search_and_push_ratio = p_ad->get_inner_qcpx_search_and_push_ratio();  // NOLINT
  // ad 设置
  params_.ad_queue_type = p_ad->get_ad_queue_type();
  params_.item_type = p_ad->get_item_type();
  params_.ocpx_type = p_ad->get_ocpx_action_type();
  params_.project_roi_ratio = p_ad->Attr(ItemIdx::fd_ecom_hosting_project_roi_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  int64_t project_ocpx_action_type = p_ad->Attr(ItemIdx::fd_ecom_hosting_project_ocpx_action_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  params_.is_valid_antou_order = p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
                                 params_.project_roi_ratio > 0 &&
                                 (project_ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
                                  project_ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS ||
                                  project_ocpx_action_type == kuaishou::ad::AD_MERCHANT_T7_ROI);
  auto atv = GetAtv(p_ad);
  if (params_.ocpx_type == kuaishou::ad::EVENT_ORDER_PAIED) {
    params_.mock_cpa_bid = p_ad->get_cpa_bid() > 0 ? p_ad->get_cpa_bid() : p_ad->get_auto_cpa_bid();
  } else {
    params_.mock_cpa_bid = (atv > 0 ? atv : 1e3) / std::max(p_ad->get_roi_ratio(), 0.1);  // NOLINT
  }
  // realtime data
  params_.min_gmv_live_stream_1_h = p_ad->Attr(ItemIdx::fd_LIVE_min_gmv_live_stream_1_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.max_gmv_live_stream_1_h = p_ad->Attr(ItemIdx::fd_LIVE_max_gmv_live_stream_1_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.min_gmv_author_3_h = p_ad->Attr(ItemIdx::fd_AUTHOR_min_gmv_author_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.max_gmv_author_3_h = p_ad->Attr(ItemIdx::fd_AUTHOR_max_gmv_author_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.pay_amount_author_3_h = p_ad->Attr(ItemIdx::fd_AUTHOR_pay_amount_author_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.real_pay_amount_author_3_h = p_ad->Attr(ItemIdx::fd_AUTHOR_real_pay_amount_author_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.platform_bear_amount_author_3_h = p_ad->Attr(ItemIdx::fd_AUTHOR_platform_bear_amount_author_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.item_num_author_3_h = p_ad->Attr(ItemIdx::fd_AUTHOR_item_num_author_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.order_num_author_3_h = p_ad->Attr(ItemIdx::fd_AUTHOR_order_num_author_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.item_promotion_author_3_h = p_ad->Attr(ItemIdx::fd_AUTHOR_item_promotion_author_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.shop_promotion_author_3_h = p_ad->Attr(ItemIdx::fd_AUTHOR_shop_promotion_author_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.pay_amount_live_stream_1_h = p_ad->Attr(ItemIdx::fd_LIVE_pay_amount_live_stream_1_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.real_pay_amount_live_stream_1_h = p_ad->Attr(ItemIdx::fd_LIVE_real_pay_amount_live_stream_1_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.platform_bear_amount_live_stream_1_h = p_ad->Attr(ItemIdx::fd_LIVE_platform_bear_amount_live_stream_1_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.item_num_live_stream_1_h = p_ad->Attr(ItemIdx::fd_LIVE_item_num_live_stream_1_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.order_num_live_stream_1_h = p_ad->Attr(ItemIdx::fd_LIVE_order_num_live_stream_1_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.item_promotion_live_stream_1_h = p_ad->Attr(ItemIdx::fd_LIVE_item_promotion_live_stream_1_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.shop_promotion_live_stream_1_h = p_ad->Attr(ItemIdx::fd_LIVE_shop_promotion_live_stream_1_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.live_realtime_atv_1_h = 0.0;
  if (params_.order_num_live_stream_1_h > 0 && params_.pay_amount_live_stream_1_h > 0) {
    params_.live_realtime_atv_1_h = params_.pay_amount_live_stream_1_h * 1000 / params_.order_num_live_stream_1_h;  // NOLINT
  }
  params_.origin_price_consider_shop_promotion_author_3_h = params_.pay_amount_author_3_h + params_.shop_promotion_author_3_h;  // NOLINT
  if (params_.origin_price_consider_shop_promotion_author_3_h > 0) {
    params_.author_gmv_price_ratio_3h = params_.pay_amount_author_3_h / params_.origin_price_consider_shop_promotion_author_3_h;  // NOLINT
  }
  if (params_.enable_qcpx_live_realtime_atv_3h_safety) {
    params_.realtime_atv_3_h = 0.0;
    double gmv_1d = p_ad->Attr(ItemIdx::fd_AUTHOR_live_gmv_1d).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);     // NOLINT
    double order_1d = p_ad->Attr(ItemIdx::fd_AUTHOR_live_order_num_1d).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    double gmv_7d = p_ad->Attr(ItemIdx::fd_AUTHOR_live_gmv_7d).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);     // NOLINT
    double order_7d = p_ad->Attr(ItemIdx::fd_AUTHOR_live_order_num_7d).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    if (gmv_7d > 0 && order_7d > 0) {
      params_.realtime_atv_3_h = gmv_7d * 1000 / order_7d;
    }
    if (gmv_1d > 0 && order_1d > 0) {
      params_.realtime_atv_3_h = gmv_1d * 1000 / order_1d;
    }
  }
  if (params_.order_num_author_3_h > 0) {
    params_.realtime_atv_3_h = params_.pay_amount_author_3_h * 1000 / params_.order_num_author_3_h;
  }
  // 优惠券设置
  InitCouponParams(p_ad);
}

void QcpxLiveStrategy::InitCouponParams(AdCommon* p_ad) {
  // *********** ad 粒度 参数 & 变量 ***********
  params_.inner_qcpx_cause = 0;
  // 优惠券设置
  // params_.product_price = int64_t(p_ad->get_auto_atv());
  // 先保持现状无门槛过滤
  params_.product_price = 0;
  params_.coupon_threshold = 0;
  params_.coupon_amount = 0;
  params_.bid_ratio = 1.0;
  params_.pcvr_ratio = 1.0;
  params_.pctr_ratio = 1.0;
  params_.cpm_ratio = 1.0;
}

void QcpxLiveStrategy::Process(AdCommon* p_ad) {
  InitAdParams(p_ad);
  if (AdmitV3(p_ad)) {
    RunArchStrategyV3(p_ad);
    FillCouponInfoV3(p_ad);
  }
  return;
}

void QcpxLiveStrategy::LogPredict(AdCommon* p_ad) {
  if (params_.enable_qcpx_live_cvr_disc_roas_multi_head_model) {
    // 折扣券多头预估值落日志
    double cvr_disc_roas_multi_head_950_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_950_logit);  // NOLINT
    double cvr_disc_roas_multi_head_900_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_900_logit);  // NOLINT
    double cvr_disc_roas_multi_head_850_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_850_logit);  // NOLINT
    double cvr_disc_roas_multi_head_800_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_800_logit);  // NOLINT
    double cvr_disc_roas_multi_head_750_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_750_logit);  // NOLINT
    double cvr_disc_roas_multi_head_700_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_700_logit);  // NOLINT
    double cvr_disc_roas_multi_head_650_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_650_logit);  // NOLINT
    double cvr_disc_roas_multi_head_600_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_600_logit);  // NOLINT
    p_ad->Attr(ItemIdx::qcpx_live_disc_950_logit).SetDoubleValue(p_ad->AttrIndex(), cvr_disc_roas_multi_head_950_logit, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::qcpx_live_disc_900_logit).SetDoubleValue(p_ad->AttrIndex(), cvr_disc_roas_multi_head_900_logit, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::qcpx_live_disc_850_logit).SetDoubleValue(p_ad->AttrIndex(), cvr_disc_roas_multi_head_850_logit, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::qcpx_live_disc_800_logit).SetDoubleValue(p_ad->AttrIndex(), cvr_disc_roas_multi_head_800_logit, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::qcpx_live_disc_750_logit).SetDoubleValue(p_ad->AttrIndex(), cvr_disc_roas_multi_head_750_logit, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::qcpx_live_disc_700_logit).SetDoubleValue(p_ad->AttrIndex(), cvr_disc_roas_multi_head_700_logit, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::qcpx_live_disc_650_logit).SetDoubleValue(p_ad->AttrIndex(), cvr_disc_roas_multi_head_650_logit, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::qcpx_live_disc_600_logit).SetDoubleValue(p_ad->AttrIndex(), cvr_disc_roas_multi_head_600_logit, false, false);  // NOLINT
  }
  // 满减券多头预估值落日志
  if (params_.enable_bspline_to_log) {
    // 样条回归
    double elastic_bspline_c0 = \
    p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c0);
    double elastic_bspline_c1 = \
    p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c1);
    double elastic_bspline_c2 = \
    p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c2);
    double elastic_bspline_c3 = \
    p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c3);
    double elastic_bspline_c4 = \
    p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c4);
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c0).SetDoubleValue(p_ad->AttrIndex(), \
    elastic_bspline_c0, false, false);
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c1).SetDoubleValue(p_ad->AttrIndex(), \
    elastic_bspline_c1, false, false);
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c2).SetDoubleValue(p_ad->AttrIndex(), \
    elastic_bspline_c2, false, false);
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c3).SetDoubleValue(p_ad->AttrIndex(), \
    elastic_bspline_c3, false, false);
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c4).SetDoubleValue(p_ad->AttrIndex(), \
    elastic_bspline_c4, false, false);
  }
  if (params_.enable_qcpx_live_taylor_bspline_model) {
    // 泰勒样条回归预估值落日志
    double taylor_elastic_bspline_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c0);  // NOLINT
    double taylor_elastic_bspline_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c1);  // NOLINT
    double taylor_elastic_bspline_c2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c2);  // NOLINT
    double taylor_elastic_bspline_c3 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c3);  // NOLINT
    double taylor_elastic_bspline_c4 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c4);  // NOLINT
    double taylor_elastic_bspline_c1_prime = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c1_prime);  // NOLINT
    if (taylor_elastic_bspline_c1_prime != 0.) {
      p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c0).SetDoubleValue(p_ad->AttrIndex(), taylor_elastic_bspline_c0, false, false);  // NOLINT
      p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c1).SetDoubleValue(p_ad->AttrIndex(), taylor_elastic_bspline_c1, false, false);  // NOLINT
      p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c2).SetDoubleValue(p_ad->AttrIndex(), taylor_elastic_bspline_c2, false, false);  // NOLINT
      p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c3).SetDoubleValue(p_ad->AttrIndex(), taylor_elastic_bspline_c3, false, false);  // NOLINT
      p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c4).SetDoubleValue(p_ad->AttrIndex(), taylor_elastic_bspline_c4, false, false);  // NOLINT
      p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_taylor_elastic_bspline_c1_prime).SetDoubleValue(p_ad->AttrIndex(), taylor_elastic_bspline_c1_prime, false, false);  // NOLINT
    }
  }
  // 满减券全链路模型预估值落日志
  if (params_.enable_qcpx_live_full_stage_model) {
    double elastic_c0_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c0_stage1);  // NOLINT
    double elastic_c0_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c0_stage2);  // NOLINT
    double elastic_c1_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c1_stage1);  // NOLINT
    double elastic_c1_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c1_stage2);  // NOLINT
    double elastic_c2_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c2_stage1);  // NOLINT
    double elastic_c2_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c2_stage2);  // NOLINT
    double elastic_c3_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c3_stage1);  // NOLINT
    double elastic_c3_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c3_stage2);  // NOLINT
    double elastic_c4_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c4_stage1);  // NOLINT
    double elastic_c4_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c4_stage2);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c0_stage1).SetDoubleValue(p_ad->AttrIndex(), elastic_c0_stage1, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c0_stage2).SetDoubleValue(p_ad->AttrIndex(), elastic_c0_stage2, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c1_stage1).SetDoubleValue(p_ad->AttrIndex(), elastic_c1_stage1, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c1_stage2).SetDoubleValue(p_ad->AttrIndex(), elastic_c1_stage2, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c2_stage1).SetDoubleValue(p_ad->AttrIndex(), elastic_c2_stage1, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c2_stage2).SetDoubleValue(p_ad->AttrIndex(), elastic_c2_stage2, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c3_stage1).SetDoubleValue(p_ad->AttrIndex(), elastic_c3_stage1, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c3_stage2).SetDoubleValue(p_ad->AttrIndex(), elastic_c3_stage2, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c4_stage1).SetDoubleValue(p_ad->AttrIndex(), elastic_c4_stage1, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c4_stage2).SetDoubleValue(p_ad->AttrIndex(), elastic_c4_stage2, false, false);  // NOLINT
  }
  // ctr 落表
  double origin_live_audience = GetLiveAudience(p_ad);
  p_ad->Attr(ItemIdx::inner_qcpx_live_use_main_ctr).SetDoubleValue(p_ad->AttrIndex(), origin_live_audience, false, false);  // NOLINT
  double ctr_p2l_elastic_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_c0);  // NOLINT
  double ctr_p2l_elastic_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_c1);  // NOLINT
  double ctr_p2l_elastic_d0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_d0);  // NOLINT
  double ctr_p2l_elastic_d1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_d1);  // NOLINT
  double ctr_live_elastic_c1 = p_ad->get_predict_score(PredictType::PredictType_live_audience_qcpx_c1);  // NOLINT
  double ctr_live_elastic_d1 = p_ad->get_predict_score(PredictType::PredictType_live_audience_qcpx_d1);  // NOLINT
  double ctr_live_elastic_c0 = p_ad->get_predict_score(PredictType::PredictType_live_audience_qcpx_c0);  // NOLINT
  double ctr_live_elastic_d0 = p_ad->get_predict_score(PredictType::PredictType_live_audience_qcpx_d0);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_p2l_live_audience_elastic_c0).SetDoubleValue(p_ad->AttrIndex(), ctr_p2l_elastic_c0, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_p2l_live_audience_elastic_c1).SetDoubleValue(p_ad->AttrIndex(), ctr_p2l_elastic_c1, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_p2l_live_audience_elastic_d0).SetDoubleValue(p_ad->AttrIndex(), ctr_p2l_elastic_d0, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_p2l_live_audience_elastic_d1).SetDoubleValue(p_ad->AttrIndex(), ctr_p2l_elastic_d1, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_live_live_audience_elastic_c1).SetDoubleValue(p_ad->AttrIndex(), ctr_live_elastic_c1, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_live_live_audience_elastic_d1).SetDoubleValue(p_ad->AttrIndex(), ctr_live_elastic_d1, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_live_live_audience_elastic_c0).SetDoubleValue(p_ad->AttrIndex(), ctr_live_elastic_c0, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_live_live_audience_elastic_d0).SetDoubleValue(p_ad->AttrIndex(), ctr_live_elastic_d0, false, false);  // NOLINT
}

void QcpxLiveStrategy::ProcessThreshold(AdCommon* p_ad) {
  if (p_ad->Is(AdFlag::is_p2l_ad_inner)) {
    params_.coupon_threshold = params_.coupon_amount * params_.value_p2l_thre_ratio_when_delivery - 200;  // NOLINT
  }
  if (p_ad->Is(AdFlag::is_live_ad_inner)) {
    params_.coupon_threshold = params_.coupon_amount * params_.value_live_thre_ratio_when_delivery - 200;  // NOLINT
  }
  params_.coupon_threshold = std::max(params_.coupon_threshold, static_cast<int64_t>(5000));
  bool is_no_thres_valid_author = true;
  // 立减券回退 黑名单
  // int64_t author_id = p_ad->get_author_id();
  // if (params_.enable_qcpx_no_thres_blacklist && RankKconfUtil::qcpxTmpIdList() != nullptr) {
  //   if (RankKconfUtil::qcpxTmpIdList()->count(author_id) > 0) {
  //     is_no_thres_valid_author = false;
  //   }
  // }
  if (params_.enable_qcpx_live_no_threshold && is_no_thres_valid_author) {
    params_.coupon_threshold = params_.coupon_amount + 100;
  }
}

void QcpxLiveStrategy::Monitor() {
  for (auto it = params_.coupon_delivery_cnt.begin(); it != params_.coupon_delivery_cnt.end(); ++it) {  // NOLINT
    const std::string& item_type = absl::StrCat(it->first);
    const std::string& ad_queue_type = absl::StrCat(params_.ad_queue_type);
    RANK_DOT_COUNT(session_data_, it->second,
      absl::StrCat(this->Name(), ".coupon_delivery_cnt"),
      ad_queue_type, item_type);
  }
}

}  // namespace ad_rank
}  // namespace ks
