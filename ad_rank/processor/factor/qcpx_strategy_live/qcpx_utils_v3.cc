#include "teams/ad/ad_rank/processor/factor/qcpx_strategy_live/qcpx_strategy.h"

#include <algorithm>
#include <memory>
#include <numeric>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <random>

#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_rank/data/p2p_data/inner_qcpx_coupon_online_p2p/inner_qcpx_coupon_online_p2p.h"
#include "teams/ad/ad_rank/data/p2p_data/universe_conv_ratio_author_post_data_p2p/universe_conv_ratio_author_post_data_p2p.h"

using kuaishou::ad::AdEnum;
namespace ks {
namespace ad_rank {

void QcpxLiveStrategy::InitCouponParamsV3(AdCommon* p_ad) {
  // *********** ad 粒度 参数 & 变量 ***********
  InitCouponParams(p_ad);
  // 优惠券设置
  params_.qpon_type = kuaishou::ad::AdEnum_QponType_UNKNOWN_QPON_TYPE;
  params_.coupon_type = 0;
  params_.amount_coupon_id = 0;
  params_.rate_coupon_id = 0;
  params_.rate_coupon_map.clear();
  params_.rate_coupon_list.clear();
  params_.rate_coupon_rct_list.clear();
  auto coupon_id_list = p_ad->Attr(ItemIdx::coupon_config_id_list).GetIntListValue(p_ad->AttrIndex()).value_or(absl::Span<const int64_t>());  // NOLINT
  bool is_exist_specific_amount_coupon_id = false;
  for (auto coupon_id : coupon_id_list) {
    auto index = params_.coupon_table->GetItemAttrIndex(coupon_id);
    if (!index.has_value()) continue;
    int32_t coupon_template_status = params_.coupon_status_attr->GetIntValue(index.value()).value_or(0);
    if (coupon_template_status != 1) continue;
    int32_t coupon_type = params_.coupon_type_attr->GetIntValue(index.value()).value_or(0);
    const auto& coupon_rule = params_.coupon_rule_attr->GetStringValue(index.value()).value_or("");  // NOLINT
    if (coupon_type == 1) {
      // 满减券
      params_.amount_coupon_id = coupon_id;
      if (params_.enable_qcpx_live_specific_amount_coupon_id) {
        if (coupon_id == params_.specific_amount_coupon_id) {
          is_exist_specific_amount_coupon_id = true;
        }
        if (is_exist_specific_amount_coupon_id) {
          params_.amount_coupon_id = params_.specific_amount_coupon_id;
        }
      }
    } else if (coupon_type == 2) {
      // 折扣券
      ::Json::Reader reader;
      ::Json::Value value;
      if (!reader.parse(std::string(coupon_rule), value)) continue;
      if (!value.isObject()) continue;
      uint64_t coupon_rate = value["reduceAmount"].asUInt64();
      uint64_t capped_amount = value["cappedAmount"].asUInt64();
      QcpxLiveCouponInfoV3 tmp_info = {coupon_rate, capped_amount};
      params_.rate_coupon_map.insert({coupon_id, tmp_info});
      params_.rate_coupon_list.push_back(coupon_id);
      if (params_.enable_qcpx_live_bound_rate_coupon_rct
          && coupon_rate >= params_.value_qcpx_live_bound_rate_coupon_rct_left
          && coupon_rate <= params_.value_qcpx_live_bound_rate_coupon_rct_right) {
        params_.rate_coupon_rct_list.push_back(coupon_id);
      }
    }
  }
}

bool QcpxLiveStrategy::AdmitV3(AdCommon* p_ad) {
  auto coupon_id_list = p_ad->Attr(ItemIdx::coupon_config_id_list).GetIntListValue(p_ad->AttrIndex()).value_or(absl::Span<const int64_t>());  // NOLINT
  if (coupon_id_list.empty()) {
    p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_COUPON_INFO);
  }
  if (coupon_id_list.empty()) return false;
  if (params_.coupon_table == nullptr) return false;
  bool flag = true;
  flag &= Admit(p_ad);
  // 包内状态 1 正常 2 删除
  int32_t coupon_status = p_ad->Attr(ItemIdx::fd_AUTHOR_shop_coupon_status).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  flag &= coupon_status == 1;
  // 投放类型 1 白盒 0 黑盒
  int32_t qcpx_put_type = p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  RANK_DOT_STATS(session_data_, static_cast<int>(flag),
    absl::StrCat(this->Name(), ".v3_admit_dot"),
    absl::StrCat(qcpx_put_type),
    absl::StrCat(p_ad->get_item_type()),
    absl::StrCat(p_ad->get_ad_queue_type()),
    absl::StrCat(p_ad->get_ocpx_action_type()));
  return flag;
}

void QcpxLiveStrategy::RunArchStrategyV3(AdCommon* p_ad) {
  LogPredict(p_ad);
  InitCouponParamsV3(p_ad);
  if (params_.amount_coupon_id == 0 && params_.rate_coupon_list.empty()) return;
  if (params_.enable_qcpx_live_allocate_flow_v2) {
    AllocateFlowV2(p_ad);
  } else {
    AllocateFlow(p_ad);
  }
  if (IsHoldoutV3(p_ad)) {
    return;
  }
  int32_t qcpx_put_type = p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  params_.qpon_type = kuaishou::ad::AdEnum_QponType_INNER_QCPX_PEC;
  if (qcpx_put_type == 1) {
    params_.qpon_type = kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN;
  }
  if (params_.inner_qcpx_cause == 10 || params_.inner_qcpx_cause == 11) {
    params_.coupon_type = 0;
  } else if (params_.inner_qcpx_cause == 20) {
    params_.coupon_type = 1;
    DecideByRandom(p_ad);
  } else if (params_.inner_qcpx_cause == 21) {
    params_.coupon_type = 2;
    DecideByRandomV3(p_ad);
  } else if (params_.inner_qcpx_cause == 30) {
    params_.coupon_type = 1;
    if (AdmitRoasUpdate(p_ad)) {
      DecideRoasRateCoupon(p_ad);
      if (params_.enable_qcpx_live_roas_use_reduce_coupon) {
        DecideRoasReduceCoupon(p_ad);
      }
    } else {
      DecideByMaxRatio(p_ad);
      if (params_.enable_qcpx_live_order_use_disc_coupon) {
        DecideOrderDiscCoupon(p_ad);
      }
    }
  }
  // 填充 rct 字段
  p_ad->set_inner_qcpx_cause(params_.inner_qcpx_cause);
  if (params_.enable_qcpx_live_add_nonq_reason && (params_.inner_qcpx_cause == 31 || params_.inner_qcpx_cause == 10)) {  // NOLINT
    p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::STRATEGY_MODEL);
  }
}

void QcpxLiveStrategy::AllocateFlow(AdCommon* p_ad) {
  int32_t sum_flow = 0;
  // 10
  int32_t amount_control_flow_percent = params_.value_qcpx_live_rct_amount_control_flow_percent;
  sum_flow += amount_control_flow_percent;
  int32_t sum_flow_10 = sum_flow;
  // 20
  int32_t amount_treatment_flow_percent = params_.value_qcpx_live_rct_amount_treatment_flow_percent;
  sum_flow += amount_treatment_flow_percent;
  int32_t sum_flow_20 = sum_flow;
  // 11
  int32_t rate_control_flow_percent = params_.value_qcpx_live_rct_rate_control_flow_percent;
  sum_flow += rate_control_flow_percent;
  int32_t sum_flow_11 = sum_flow;
  // 21
  int32_t rate_treatment_flow_percent = params_.value_qcpx_live_rct_rate_treatment_flow_percent;
  sum_flow += rate_treatment_flow_percent;
  int32_t sum_flow_21 = sum_flow;
  // 30
  int32_t model_flow_percent = params_.value_qcpx_live_model_flow_percent;
  sum_flow += model_flow_percent;
  int32_t sum_flow_30 = sum_flow;
  int32_t max_flow = 100;
  // 流量和检查
  if (sum_flow > max_flow) return;
  int32_t mod_num = ((session_data_->get_llsid() % 100511) + (p_ad->get_creative_id() % 100511)) % max_flow;
  // 因果
  params_.inner_qcpx_cause = 0;
  if (mod_num < sum_flow_10) {
    params_.inner_qcpx_cause = 10;  // 满减 holdout
  } else if (mod_num < sum_flow_20) {
    params_.inner_qcpx_cause = 20;  // 满减 exploration
  } else if (mod_num < sum_flow_11) {
    params_.inner_qcpx_cause = 11;  // 折扣 holdout
  } else if (mod_num < sum_flow_21) {
    params_.inner_qcpx_cause = 21;  // 折扣 exploration
  } else if (mod_num < sum_flow_30) {
    params_.inner_qcpx_cause = 30;
  }
}

void QcpxLiveStrategy::AllocateFlowV2(AdCommon* p_ad) {
  // 30
  int32_t model_flow_percent = params_.value_qcpx_live_model_flow_percent;
  int32_t max_flow = 100;
  // 流量和检查
  if (model_flow_percent > max_flow) return;
  int32_t mod_num = (session_data_->get_llsid() % 1000003) % max_flow;
  // 因果
  params_.inner_qcpx_cause = 0;
  if (mod_num < model_flow_percent) {
    params_.inner_qcpx_cause = 30;  // 策略流量
  } else {
    int32_t sum_flow = 0;
    // 10
    int32_t amount_control_flow_percent = params_.value_qcpx_live_rct_amount_control_flow_percent;
    sum_flow += amount_control_flow_percent;
    int32_t sum_flow_10 = sum_flow;
    // 20
    int32_t amount_treatment_flow_percent = params_.value_qcpx_live_rct_amount_treatment_flow_percent;
    sum_flow += amount_treatment_flow_percent;
    int32_t sum_flow_20 = sum_flow;
    // 11
    int32_t rate_control_flow_percent = params_.value_qcpx_live_rct_rate_control_flow_percent;
    sum_flow += rate_control_flow_percent;
    int32_t sum_flow_11 = sum_flow;
    // 21
    int32_t rate_treatment_flow_percent = params_.value_qcpx_live_rct_rate_treatment_flow_percent;
    sum_flow += rate_treatment_flow_percent;
    int32_t sum_flow_21 = sum_flow;
    if (sum_flow > max_flow) return;
    int32_t rct_mod_num = ((session_data_->get_llsid() % 1000003) + (p_ad->get_creative_id() % 1000003)) % max_flow; // NOLINT
    if (rct_mod_num < sum_flow_10) {
      params_.inner_qcpx_cause = 10;  // 满减 holdout
    } else if (rct_mod_num < sum_flow_20) {
      params_.inner_qcpx_cause = 20;  // 满减 exploration
    } else if (rct_mod_num < sum_flow_11) {
      params_.inner_qcpx_cause = 11;  // 折扣 holdout
    } else if (rct_mod_num < sum_flow_21) {
      params_.inner_qcpx_cause = 21;  // 折扣 exploration
    }
  }
}

void QcpxLiveStrategy::DecideByRandomV3(AdCommon* p_ad) {
  auto& tmp_list = params_.rate_coupon_list;
  if (params_.enable_qcpx_live_bound_rate_coupon_rct) {
    tmp_list = params_.rate_coupon_rct_list;
  }
  if (tmp_list.empty()) return;
  if (params_.enable_qcpx_rct_disc_skip_low_cpa && params_.ocpx_type == kuaishou::ad::EVENT_ORDER_PAIED && p_ad->get_cpa_bid() < 5e3) return;  // NOLINT
  uint32_t select_idx = ks::ad_base::AdRandom::GetInt(0, tmp_list.size() - 1);  // NOLINT
  if (select_idx < tmp_list.size()) {
    params_.rate_coupon_id = tmp_list[select_idx];
  }
}

void QcpxLiveStrategy::DecideRoasRateCoupon(AdCommon* p_ad) {
  params_.inner_qcpx_cause = 31;
  auto& tmp_map = params_.rate_coupon_map;
  // Support Uplift for CTR @fandi
  double origin_live_audience = GetLiveAudience(p_ad);
  double tmp_ctr_ratio = 1.0;
  double tmp_pcvr_ratio = 1.0;
  double tmp_bid_ratio = 1.0;
  double tmp_decay_bid_ratio = 1.0;
  double tmp_cpm_ratio = 1.0;
  double roi_bound = RateCouponGetRoiBound(p_ad);
  double live_ltv = 0.0;
  if (params_.ocpx_type == kuaishou::ad::AD_MERCHANT_T7_ROI) {
    live_ltv = p_ad->get_inner_live_roas_7days_0_2h();
  } else {
    if (SPDM_enable_use_new_pred(session_data_->get_spdm_ctx()) && !(session_data_->get_is_search_request()
      && SPDM_enable_search_live_roas_filter(session_data_->get_spdm_ctx()))) {
      live_ltv = p_ad->get_inner_live_roas();
    } else {
      live_ltv = p_ad->get_gmv();
    }
  }
  p_ad->Attr(ItemIdx::inner_qcpx_live_use_main_ltv).SetDoubleValue(p_ad->AttrIndex(), live_ltv, false, false);  // NOLINT
  for (auto iter = tmp_map.begin(); iter != tmp_map.end(); iter++) {
    const auto& coupon_info = iter->second;
    if (params_.enable_qcpx_live_bound_rate_coupon_model) {
      if (coupon_info.coupon_rate < params_.value_qcpx_live_bound_rate_coupon_model_left
          || coupon_info.coupon_rate > params_.value_qcpx_live_bound_rate_coupon_model_right) {
        continue;
      }
    }
    tmp_pcvr_ratio = RateCouponGetPcvrRatio(p_ad, coupon_info.coupon_rate, coupon_info.capped_amount);
    tmp_decay_bid_ratio = RateCouponGetRoasBidRatio(p_ad, coupon_info.coupon_rate, coupon_info.capped_amount, roi_bound, params_.enable_qcpx_live_roas_rate_coupon_use_customer_bid);  // NOLINT
    tmp_ctr_ratio = CalcDiscountCouponLiveAudienceRatio(p_ad, coupon_info.coupon_rate, coupon_info.capped_amount);  // NOLINT
    if (params_.enable_qcpx_live_ctr_shield_depress) {
      tmp_ctr_ratio = std::max(tmp_ctr_ratio, 1.0);
    }
    if (params_.enable_live_qcpx_p2l_ctr_uplift && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.is_thanos_request ||  // NOLINT
        params_.enable_live_qcpx_live_ctr_one_model_uplift && p_ad->Is(AdFlag::is_merchant_item_live) && params_.is_thanos_request) {  // NOLINT
      tmp_cpm_ratio = tmp_ctr_ratio * tmp_pcvr_ratio * tmp_decay_bid_ratio;
    } else {
      tmp_cpm_ratio = tmp_pcvr_ratio * tmp_decay_bid_ratio;
    }
    tmp_bid_ratio = RateCouponGetRoasBidRatio(p_ad, coupon_info.coupon_rate, coupon_info.capped_amount, 1.0, false);  // NOLINT
    // 内部测试 修改请确认 @fandi
    if (params_.is_qcpx_test_user) {
      tmp_pcvr_ratio *= params_.qcpx_test_user_boost_ratio;
      tmp_cpm_ratio = tmp_pcvr_ratio;
      tmp_bid_ratio = 1.0;
    }
    if (tmp_cpm_ratio > params_.cpm_ratio) {
      params_.cpm_ratio = tmp_cpm_ratio;
      params_.pcvr_ratio = tmp_pcvr_ratio;
      params_.bid_ratio = tmp_bid_ratio;
      params_.pctr_ratio = tmp_ctr_ratio;
      // 折扣券覆盖满减券
      params_.coupon_type = 2;
      params_.rate_coupon_id = iter->first;
      params_.inner_qcpx_cause = 32;
      p_ad->Attr(ItemIdx::inner_qcpx_live_ctr).SetDoubleValue(p_ad->AttrIndex(), origin_live_audience * params_.pctr_ratio, false, false);  // NOLINT
      p_ad->Attr(ItemIdx::inner_qcpx_disc_live_ltv).SetDoubleValue(p_ad->AttrIndex(), live_ltv * params_.pcvr_ratio, false, false);  // NOLINT
      if (params_.enable_qcpx_before_bagging_cvr_log) {
        // before bagging to log
        p_ad->Attr(ItemIdx::inner_qcpx_live_bagging_residual_ltv).SetDoubleValue(p_ad->AttrIndex(), live_ltv * params_.bagging_residual_cvr_ratio, false, false);  // NOLINT
        p_ad->Attr(ItemIdx::inner_qcpx_live_bagging_onemodel_ltv).SetDoubleValue(p_ad->AttrIndex(), live_ltv * params_.bagging_onemodel_cvr_ratio, false, false);  // NOLINT
      }
    }
  }
  if (params_.enable_qcpx_live_roas_use_reduce_coupon) {
    if (params_.coupon_type == 2 && params_.inner_qcpx_cause == 32) {
      RANK_DOT_COUNT(session_data_, 1,
        "ROAS_disc_delivery",
        absl::StrCat(p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0)),
        absl::StrCat(p_ad->get_item_type()),
        absl::StrCat(p_ad->get_ad_queue_type()),
        absl::StrCat(p_ad->get_ocpx_action_type()));
    }
  }
}

void QcpxLiveStrategy::DecideRoasReduceCoupon(AdCommon* p_ad) {
  double ori_inner_qcpx_cause = params_.inner_qcpx_cause;
  double origin_live_audience = GetLiveAudience(p_ad);
  double tmp_ctr_ratio = 1.0;
  double tmp_pcvr_ratio = 1.0;
  double tmp_bid_ratio = 1.0;
  double tmp_decay_bid_ratio = 1.0;
  double tmp_cpm_ratio = 1.0;
  double roi_bound = RateCouponGetRoiBound(p_ad);   // 默认 ROI 约束策略
  double live_ltv = 0.0;
  double ori_pcvr = GetPcvr(p_ad);
  if (params_.ocpx_type == kuaishou::ad::AD_MERCHANT_T7_ROI) {
    live_ltv = p_ad->get_inner_live_roas_7days_0_2h();
  } else {
    if (SPDM_enable_use_new_pred(session_data_->get_spdm_ctx()) && !(session_data_->get_is_search_request()
      && SPDM_enable_search_live_roas_filter(session_data_->get_spdm_ctx()))) {
      live_ltv = p_ad->get_inner_live_roas();
    } else {
      live_ltv = p_ad->get_gmv();
    }
  }
  // 直播高价格差异屏蔽 ROAS 发满减券
  if (params_.enable_qcpx_live_high_gmv_diff_depress_roas_reduce &&
      params_.order_num_author_3_h >= params_.value_qcpx_live_high_gmv_diff_order_count_threshold &&
      live_ltv > 0.0 && ori_pcvr > 0.0 && ori_pcvr < 1.0) {
    double paycnt = ori_pcvr / (1.0 - ori_pcvr);
    double pGMV = live_ltv / paycnt;
    double min_gmv = 0.0;
    if (!(std::fabs(params_.min_gmv_author_3_h) < params_.epsilon || std::fabs(params_.min_gmv_author_3_h - 99999.0) < params_.epsilon)) {  // NOLINT
      min_gmv = params_.min_gmv_author_3_h;
    }
    if (!(std::fabs(params_.min_gmv_live_stream_1_h) < params_.epsilon || std::fabs(params_.min_gmv_live_stream_1_h - 99999.0) < params_.epsilon)) {  // NOLINT
      min_gmv = params_.min_gmv_live_stream_1_h;
    }
    double pgmv_min_diff_ratio = (pGMV - min_gmv) / pGMV;
    if (pgmv_min_diff_ratio > params_.value_qcpx_live_pgmv_min_diff_ratio_threshold &&
        min_gmv > 0) {
      double roi_pacing_coef = std::exp(params_.value_qcpx_live_high_gmv_diff_roi_pacing_coef_slope * pgmv_min_diff_ratio);   // NOLINT
      roi_bound *= roi_pacing_coef;
    }
  }
  p_ad->Attr(ItemIdx::inner_qcpx_live_use_main_ltv).SetDoubleValue(p_ad->AttrIndex(), live_ltv, false, false);  // NOLINT
  auto atv = GetAtv(p_ad);
  p_ad->set_inner_qcpx_live_thre(atv);
  // 最大最小券金设定
  int32_t price_coupon_ratio = params_.value_qcpx_live_price_coupon_ratio;
  if (price_coupon_ratio == 0) return;
  int32_t min_coupon_amount_yuan = params_.qcpx_live_min_coupon_amount_yuan;
  int32_t max_coupon_amount_yuan = params_.qcpx_live_max_coupon_amount_yuan;
  min_coupon_amount_yuan = std::max(min_coupon_amount_yuan, static_cast<int>(std::ceil(0.05 * atv / 1e3)));  // NOLINT
  max_coupon_amount_yuan = std::min(max_coupon_amount_yuan, static_cast<int>((atv + 200) / 1e3 / price_coupon_ratio));  // NOLINT   
  // roas 用 bid 做最大最小券金
  bool ret = true;
  if (params_.enable_qcpx_live_cpa_bid_amt_thre_roas &&
      p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
    ret = GetMinMaxCouponAmtBid(p_ad, &min_coupon_amount_yuan, &max_coupon_amount_yuan);
  }
  if (max_coupon_amount_yuan < min_coupon_amount_yuan || !ret) {
    return;
  }
  // Core Strategy Part
  // Start Qpon Search
  for (int32_t i = min_coupon_amount_yuan; i <= max_coupon_amount_yuan; ++i) {
    double q_yuan = i;  // 单位 元
    double q = i * 1e3;
    int32_t coupon_threshold = q * price_coupon_ratio;
    // =================================== CalcReduceCouponCVRRatio Start ===================================
    tmp_pcvr_ratio = RoasReduceGetPcvrRatio(p_ad, q_yuan);
    tmp_decay_bid_ratio = RoasReduceGetBidRatio(p_ad, q_yuan, roi_bound, params_.enable_qcpx_live_roas_rate_coupon_use_customer_bid);  // NOLINT
    tmp_ctr_ratio = CalcReduceCouponLiveAudienceRatio(p_ad, q_yuan / 10.0);  // NOLINT
    if (params_.enable_qcpx_live_ctr_shield_depress) {
      tmp_ctr_ratio = std::max(tmp_ctr_ratio, 1.0);
    }
    if (params_.enable_live_qcpx_p2l_ctr_uplift && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.is_thanos_request ||  // NOLINT
        params_.enable_live_qcpx_live_ctr_one_model_uplift && p_ad->Is(AdFlag::is_merchant_item_live) && params_.is_thanos_request) {  // NOLINT
      tmp_cpm_ratio = tmp_ctr_ratio * tmp_pcvr_ratio * tmp_decay_bid_ratio;
    } else {
      tmp_cpm_ratio = tmp_pcvr_ratio * tmp_decay_bid_ratio;
    }
    tmp_bid_ratio = RoasReduceGetBidRatio(p_ad, q_yuan, 1.0, false);  // NOLINT
    if (tmp_cpm_ratio > params_.cpm_ratio) {
      params_.cpm_ratio = tmp_cpm_ratio;
      params_.pcvr_ratio = tmp_pcvr_ratio;
      params_.bid_ratio = tmp_bid_ratio;
      params_.pctr_ratio = tmp_ctr_ratio;
      // ROAS 满减券覆盖折扣券
      params_.coupon_threshold = coupon_threshold - 200;
      params_.coupon_amount = q;
      params_.coupon_type = 1;
      params_.inner_qcpx_cause = 32;
      p_ad->Attr(ItemIdx::inner_qcpx_live_ctr).SetDoubleValue(p_ad->AttrIndex(), origin_live_audience * params_.pctr_ratio, false, false);  // NOLINT
      p_ad->Attr(ItemIdx::inner_qcpx_disc_live_ltv).SetDoubleValue(p_ad->AttrIndex(), live_ltv * params_.pcvr_ratio, false, false);  // NOLINT
      if (params_.enable_qcpx_before_bagging_cvr_log) {
        // before bagging to log
        p_ad->Attr(ItemIdx::inner_qcpx_live_bagging_residual_ltv).SetDoubleValue(p_ad->AttrIndex(), live_ltv * params_.bagging_residual_cvr_ratio, false, false);  // NOLINT
        p_ad->Attr(ItemIdx::inner_qcpx_live_bagging_onemodel_ltv).SetDoubleValue(p_ad->AttrIndex(), live_ltv * params_.bagging_onemodel_cvr_ratio, false, false);  // NOLINT
      }
    }
  }
  if (params_.coupon_type == 1 && params_.inner_qcpx_cause == 32) {
    RANK_DOT_COUNT(session_data_, 1,
      "ROAS_reudce_delivery",
      absl::StrCat(p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0)),
      absl::StrCat(p_ad->get_item_type()),
      absl::StrCat(p_ad->get_ad_queue_type()),
      absl::StrCat(p_ad->get_ocpx_action_type()));
    if (ori_inner_qcpx_cause == 32) {
      RANK_DOT_COUNT(session_data_, 1,
        "ROAS_reudce_replace",
        absl::StrCat(p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0)),
        absl::StrCat(p_ad->get_item_type()),
        absl::StrCat(p_ad->get_ad_queue_type()),
        absl::StrCat(p_ad->get_ocpx_action_type()));
    }
  }
}

double QcpxLiveStrategy::RoasReduceGetBidRatio(AdCommon* p_ad, double q_yuan, double roi_bound, bool is_customer) {  // NOLINT
  double roi_ratio = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
  if (is_customer) {
    roi_ratio = p_ad->get_roi_ratio() > 0.0 ? p_ad->get_roi_ratio() : p_ad->get_auto_roas();
    // 全站特殊处理
    if (params_.ocpx_type == kuaishou::ad::AD_STOREWIDE_ROAS && params_.enable_qcpx_live_storewide_use_auto_roas) {  // NOLINT
      roi_ratio = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
    }
    // 全站暗投七日 使用系统出价
    if (params_.enable_qcpx_live_actual_storewide_bid_update && p_ad->get_scene_oriented_type() == 21 && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {  // NOLINT
      roi_ratio = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
    }
    // NOBID 部分 使用系统出价
    if (params_.enable_qcpx_live_nobid_bid_update && p_ad->Is(AdFlag::IsEspUnifyNobidAdV2)) {
      roi_ratio = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
    }
  }
  double ori_pltv = p_ad->get_inner_live_roas();
  if (roi_ratio <= 0.0 || ori_pltv <= 0.0) return 0.0;
  double ori_ecpm = ori_pltv / roi_ratio;
  double ori_pcvr = GetPcvr(p_ad);
  if (params_.enable_qcpx_live_roas_use_realtime_atv_for_bid) {
    if (!(params_.enable_qcpx_live_realtime_atv_3h_safety && std::fabs(params_.realtime_atv_3_h) < params_.epsilon)) {  // NOLINT
      return 1.0 - roi_bound * q_yuan * 1000 * roi_ratio / params_.realtime_atv_3_h;
    }
  }
  return 1.0 - roi_bound * q_yuan * ori_pcvr / ori_ecpm;
}

double QcpxLiveStrategy::RoasReduceGetPcvrRatio(AdCommon* p_ad, double q_yuan) {  // NOLINT
  double q_10y = q_yuan / 10.0;
  double ori_pcvr = GetPcvr(p_ad);
  if (ori_pcvr <= 0.0) return 1.0;
  double qcpx_pcvr = ori_pcvr;
  if (p_ad->Is(AdFlag::is_merchant_live_roas)) {
    // 预估值
    double elastic_bspline_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c0);  // NOLINT
    double elastic_bspline_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c1);  // NOLINT
    double elastic_bspline_c2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c2);  // NOLINT
    double elastic_bspline_c3 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c3);  // NOLINT
    double elastic_bspline_c4 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c4);  // NOLINT
    // clip
    double ori_pcvr_clip = std::clamp(ori_pcvr, 1e-18, 0.9999996941);
    double logit_divisor = 1 - ori_pcvr_clip;
    if (logit_divisor <= 0.0) return 1.0;
    double ori_logit = std::log(ori_pcvr_clip / logit_divisor);
    double qcpx_logit = 0.0;
    if (params_.enable_qcpx_live_bspline_model) {
      if (q_yuan < 9) {
        qcpx_logit = elastic_bspline_c0 + elastic_bspline_c1 * q_10y;
      } else if (q_yuan < 16) {
        qcpx_logit = elastic_bspline_c0 + elastic_bspline_c1 * 0.9 + elastic_bspline_c2 * (q_10y - 0.9);  // NOLINT
      } else if (q_yuan < 23) {
        qcpx_logit = elastic_bspline_c0 + elastic_bspline_c1 * 0.9 + elastic_bspline_c2 * 0.7 + elastic_bspline_c3 * (q_10y - 1.6);  // NOLINT
      } else {
        qcpx_logit = elastic_bspline_c0 + elastic_bspline_c1 * 0.9 + elastic_bspline_c2 * 0.7 + elastic_bspline_c3 * 0.7 + elastic_bspline_c4 * (q_10y - 2.3);  // NOLINT
      }
    }
    if (params_.enable_qcpx_live_sigmoid_transform) {
      qcpx_logit = std::log(params_.value_qcpx_live_sigmoid_transform * std::exp(qcpx_logit) / (1 + std::exp(qcpx_logit)));  // NOLINT
    } else {
      qcpx_logit = std::log(std::exp(std::min(std::max(qcpx_logit, -3.0), 3.0)));
    }
    qcpx_pcvr = std::exp(ori_logit + qcpx_logit) / (1 + std::exp(ori_logit + qcpx_logit));
  }
  double cvr_uplift_ratio = qcpx_pcvr / ori_pcvr;
  // Onemodel CVR bagging 优先级最高
  double amt_w = p_ad->get_predict_score(PredictType::PredictType_inner_live_atom_paycnt_bak4);  // NOLINT
  double amt_b = p_ad->get_predict_score(PredictType::PredictType_inner_live_atom_paycnt_bak5);  // NOLINT
  double paycnt_front_predict_base = p_ad->get_inner_live_atom_paycnt_front();
  double paycnt_end_predict_base = p_ad->get_inner_live_atom_paycnt_end();
  bool is_cvr_roas_onemodel = false;
  if ((params_.enable_inner_live_qcpx_roas && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)) ||  // NOLINT
    (params_.enable_inner_live_qcpx_t7roas && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI)) {  // NOLINT
    is_cvr_roas_onemodel = true;
  }
  if (params_.enable_qcpx_live_cvr_roas_reduce_onemodel_bagging) {   // ROAS 发满减券 onemodel 开关
    if (is_cvr_roas_onemodel && params_.enable_qcpx_live_cvr_onemodel_bagging) {
      if ((paycnt_front_predict_base + paycnt_end_predict_base <= 0) || paycnt_front_predict_base < 0) return 1.0;  // NOLINT
      // prob = sigmoid(logit)
      // pred = prob / (1 - prob) = exp(logit)
      // logit = ln(pred)
      double qcpx_front_logit = std::log(paycnt_front_predict_base) + amt_w * q_10y + amt_b;
      double qcpx_front_predict = std::exp(qcpx_front_logit);
      if (std::fabs(1.0 + qcpx_front_predict) < 1e-9) return 1.0;
      double qcpx_front_prob = qcpx_front_predict / (1.0 + qcpx_front_predict);
      if (std::fabs(1.0 + paycnt_front_predict_base) < 1e-9) return 1.0;
      double front_prob_base = paycnt_front_predict_base / (1.0 + paycnt_front_predict_base);
      if (std::fabs(1.0 + paycnt_end_predict_base) < 1e-9) return 1.0;
      double end_prob_base = paycnt_end_predict_base / (1.0 + paycnt_end_predict_base);
      if (std::fabs(front_prob_base + end_prob_base) < 1e-9) return 1.0;
      double onemodel_ratio = (qcpx_front_prob + end_prob_base) / (front_prob_base + end_prob_base);  // NOLINT
      params_.bagging_residual_cvr_ratio = cvr_uplift_ratio;
      params_.bagging_onemodel_cvr_ratio = onemodel_ratio;
      // cvr_uplift_ratio = (onemodel_ratio + cvr_uplift_ratio) / 2.0;
      double bagging_coef = params_.inner_qcpx_roas_cvr_bagging_ratio;  // 默认 0.5
      cvr_uplift_ratio = bagging_coef * onemodel_ratio + (1 - bagging_coef) * cvr_uplift_ratio;
    }
  }
  // CVR uplift ratio UB
  if (params_.enable_qcpx_live_cvr_uplift_bound) {
    cvr_uplift_ratio = std::min(cvr_uplift_ratio, params_.value_qcpx_live_cvr_uplift_bound);
  }
  return cvr_uplift_ratio;
}

double QcpxLiveStrategy::GetPcvr(AdCommon* p_ad) {
  double ori_pcvr = p_ad->get_unify_cvr_info().value;
  if (p_ad->Is(AdFlag::is_merchant_live_roas)) {
    ori_pcvr = p_ad->get_inner_live_roi_pay_front() + p_ad->get_inner_live_roi_pay_end();
    // 直播原子化
    double ori_pcvr_atom = p_ad->get_inner_live_atom_paycnt_front() + p_ad->get_inner_live_atom_paycnt_end();
    if (ori_pcvr <= 0 || ori_pcvr >= 1) {
      ori_pcvr_atom = std::max(ori_pcvr_atom, 0.0);
      ori_pcvr = ori_pcvr_atom / (1.0 + ori_pcvr_atom);
    }
  } else {
    ori_pcvr = ori_pcvr / (1.0 + ori_pcvr);
  }
  return ori_pcvr;
}

double QcpxLiveStrategy::RateCouponGetRoiBound(AdCommon* p_ad) {
  double roi_bound = params_.qcpx_live_coupon_adjust_roi_bound;
  if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
    roi_bound = params_.qcpx_live_coupon_adjust_roi_bound_open;
  }
  double unify_roi_pacing_coef = 1.0;
  // 直投 短引 系数拆分
  if (params_.enable_qcpx_live_p2l_roi_bound_seperate && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.value_qcpx_live_roi_bound_p2l_open > 0) {  // NOLINT
    unify_roi_pacing_coef *= params_.value_qcpx_live_roi_bound_p2l_open;
  }
  if (p_ad->Is(AdFlag::is_merchant_live_roas)) {
    roi_bound = params_.value_qcpx_live_rate_coupon_roi_bound;
    if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
      roi_bound = params_.value_qcpx_live_rate_coupon_roi_bound_white;
    }
    // ROAS 发满减券用单独的系数
    if (params_.enable_qcpx_live_roas_use_reduce_coupon && params_.qcpx_live_roas_reduce_roi_bound > 0.0) {  // NOLINT
      roi_bound = params_.qcpx_live_roas_reduce_roi_bound;
      if (params_.qpon_type != kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
        roi_bound *= 1.2;
      }
    }
    // ROAS 类 ROI 系数拆分
    if (params_.enable_qcpx_live_roas_sep_roi_coef) {
      if (params_.ocpx_type == kuaishou::ad::AD_STOREWIDE_ROAS && params_.value_qcpx_live_storewide_roas_sep_roi_coef > 0) {  // NOLINT
        unify_roi_pacing_coef *= params_.value_qcpx_live_storewide_roas_sep_roi_coef;
      } else if (params_.ocpx_type == kuaishou::ad::AD_MERCHANT_ROAS && params_.value_qcpx_live_merchant_roas_sep_roi_coef > 0) { // NOLINT
        unify_roi_pacing_coef *= params_.value_qcpx_live_merchant_roas_sep_roi_coef;
      } else if (params_.ocpx_type == kuaishou::ad::AD_MERCHANT_T7_ROI && params_.value_qcpx_live_t7_roas_sep_roi_coef > 0) {  // NOLINT
        unify_roi_pacing_coef *= params_.value_qcpx_live_t7_roas_sep_roi_coef;
      }
      if (params_.value_qcpx_unify_ROI_pacing_p2l_storewide > 0 && params_.ocpx_type == kuaishou::ad::AD_STOREWIDE_ROAS && p_ad->Is(AdFlag::is_merchant_item_p2l)) {  // NOLINT
        unify_roi_pacing_coef *= params_.value_qcpx_unify_ROI_pacing_p2l_storewide;
      }
      if (params_.value_qcpx_unify_ROI_pacing_p2l_t7 > 0 && params_.ocpx_type == kuaishou::ad::AD_MERCHANT_T7_ROI && p_ad->Is(AdFlag::is_merchant_item_p2l)) {  // NOLINT
        unify_roi_pacing_coef *= params_.value_qcpx_unify_ROI_pacing_p2l_t7;
      }
    }
  }
  // 商家白名单 调整系数
  if (params_.enable_qcpx_author_roi_pacing &&
      params_.author_roi_pacing_set != nullptr &&
      params_.author_roi_pacing_set->count(p_ad->get_author_id())) {
    unify_roi_pacing_coef *= params_.value_qcpx_author_roi_pacing;
  }
  roi_bound *= unify_roi_pacing_coef;
  return roi_bound;
}

double QcpxLiveStrategy::RateCouponGetRoasBidRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount, double roi_bound, bool is_customer) {  // NOLINT
  double live_gmv_price_ratio = GetLiveAuthorGmvPriceRatio(p_ad);
  double roi_ratio = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
  if (is_customer) {
    roi_ratio = p_ad->get_roi_ratio() > 0.0 ? p_ad->get_roi_ratio() : p_ad->get_auto_roas();
    // 全站特殊处理
    if (params_.ocpx_type == kuaishou::ad::AD_STOREWIDE_ROAS && params_.enable_qcpx_live_storewide_use_auto_roas) {  // NOLINT
      roi_ratio = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
    }
    // 全站暗投七日 使用系统出价
    if (params_.enable_qcpx_live_actual_storewide_bid_update && p_ad->get_scene_oriented_type() == 21 && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {  // NOLINT
      roi_ratio = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
    }
    // NOBID 部分 使用系统出价
    if (params_.enable_qcpx_live_nobid_bid_update && p_ad->Is(AdFlag::IsEspUnifyNobidAdV2)) {
      roi_ratio = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
    }
  }
  double ori_pltv = p_ad->get_inner_live_roas();
  if (roi_ratio <= 0.0 || ori_pltv <= 0.0) return 0.0;
  double ori_ecpm = ori_pltv / roi_ratio;
  double discount_rate = 1.0 - coupon_rate / 1000.0;
  double discount_amt = ori_pltv * discount_rate;
  if (params_.enable_qcpx_live_rate_coupon_with_capped) {
    discount_amt = std::min(discount_amt, capped_amount * GetPcvr(p_ad) / 1000.0);
  }
  if (params_.enable_qcpx_discount_coupon_gmv_price_ratio) {
    if (params_.enable_fix_qcpx_live_disc_bid_ratio && live_gmv_price_ratio > 0) {
      return 1 - roi_bound * discount_amt / ori_ecpm / live_gmv_price_ratio;
    }
    return live_gmv_price_ratio - roi_bound * discount_amt / ori_ecpm;
  }
  return 1.0 - roi_bound * discount_amt / ori_ecpm;
}

double QcpxLiveStrategy::RateCouponGetPcvrRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount) {  // NOLINT
  double ori_pcvr = GetPcvr(p_ad);
  if (ori_pcvr <= 0.0) return 1.0;
  double qcpx_pcvr = ori_pcvr;
  if (p_ad->Is(AdFlag::is_merchant_live_roas) || params_.enable_qcpx_live_order_use_disc_coupon) {
    // 预估值
    double cvr_disc_roas_multi_head_950_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_950_logit);  // NOLINT
    double cvr_disc_roas_multi_head_900_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_900_logit);  // NOLINT
    double cvr_disc_roas_multi_head_850_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_850_logit);  // NOLINT
    double cvr_disc_roas_multi_head_800_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_800_logit);  // NOLINT
    double cvr_disc_roas_multi_head_750_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_750_logit);  // NOLINT
    double cvr_disc_roas_multi_head_700_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_700_logit);  // NOLINT
    double cvr_disc_roas_multi_head_650_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_650_logit);  // NOLINT
    double cvr_disc_roas_multi_head_600_logit = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_600_logit);  // NOLINT
    // clip
    double ori_pcvr_clip = std::clamp(ori_pcvr, 1e-18, 0.9999996941);
    double logit_divisor = 1 - ori_pcvr_clip;
    if (logit_divisor <= 0.0) return 1.0;
    double ori_logit = std::log(ori_pcvr_clip / logit_divisor);
    double qcpx_logit = ori_logit;
    if (params_.enable_qcpx_live_cvr_disc_roas_multi_head_model) {
      if (coupon_rate == 950) qcpx_logit += cvr_disc_roas_multi_head_950_logit;
      else if (coupon_rate == 900) qcpx_logit += cvr_disc_roas_multi_head_900_logit;
      else if (coupon_rate == 850) qcpx_logit += cvr_disc_roas_multi_head_850_logit;
      else if (coupon_rate == 800) qcpx_logit += cvr_disc_roas_multi_head_800_logit;
      else if (coupon_rate == 750) qcpx_logit += cvr_disc_roas_multi_head_750_logit;
      else if (coupon_rate == 700) qcpx_logit += cvr_disc_roas_multi_head_700_logit;
      else if (coupon_rate == 650) qcpx_logit += cvr_disc_roas_multi_head_650_logit;
      else if (coupon_rate == 600) qcpx_logit += cvr_disc_roas_multi_head_600_logit;
    }
    qcpx_logit = std::min(std::max(qcpx_logit, -15.0), 15.0);
    qcpx_pcvr = std::exp(qcpx_logit) / (1 + std::exp(qcpx_logit));
  }
  double cvr_uplift_ratio = qcpx_pcvr / ori_pcvr;
  // Onemodel CVR
  double d10 = (1000.0 - coupon_rate) / 1000.0 * 50.0 / 10.0;  // 对齐模型
  double disc_w = p_ad->get_predict_score(PredictType::PredictType_inner_live_atom_paycnt_bak6);  // NOLINT
  double disc_b = p_ad->get_predict_score(PredictType::PredictType_inner_live_atom_paycnt_bak7);  // NOLINT
  double paycnt_front_predict_base = p_ad->get_inner_live_atom_paycnt_front();
  double paycnt_end_predict_base = p_ad->get_inner_live_atom_paycnt_end();
  bool is_cvr_roas_onemodel = false;
  if ((params_.enable_inner_live_qcpx_roas && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)) ||  // NOLINT
    (params_.enable_inner_live_qcpx_t7roas && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI)) {  // NOLINT
    is_cvr_roas_onemodel = true;
  }
  bool is_cvr_order_onemodel = false;
  if (params_.enable_qcpx_live_order_use_disc_coupon && params_.enable_inner_live_qcpx_order && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {  // NOLINT
    is_cvr_order_onemodel = true;
  }
  // Onemodel CVR bagging 优先级最高
  if (params_.enable_qcpx_live_cvr_order_disc_onemodel_bagging || p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {   // 订单发折扣券 onemodel 开关  // NOLINT
    if ((is_cvr_roas_onemodel || is_cvr_order_onemodel) && params_.enable_qcpx_live_cvr_onemodel_bagging) {
      if ((paycnt_front_predict_base + paycnt_end_predict_base <= 0) || paycnt_front_predict_base < 0 || paycnt_end_predict_base < 0) return 1.0;  // NOLINT
      // prob = sigmoid(logit)
      // pred = prob / (1 - prob) = exp(logit)
      // logit = ln(pred)
      double qcpx_front_logit = std::log(paycnt_front_predict_base) + disc_w * d10 + disc_b;
      double qcpx_front_predict = std::exp(qcpx_front_logit);
      if (std::fabs(1.0 + qcpx_front_predict) < 1e-9) return 1.0;
      double qcpx_front_prob = qcpx_front_predict / (1.0 + qcpx_front_predict);
      if (std::fabs(1.0 + paycnt_front_predict_base) < 1e-9) return 1.0;
      double front_prob_base = paycnt_front_predict_base / (1.0 + paycnt_front_predict_base);
      if (std::fabs(1.0 + paycnt_end_predict_base) < 1e-9) return 1.0;
      double end_prob_base = paycnt_end_predict_base / (1.0 + paycnt_end_predict_base);
      if (std::fabs(front_prob_base + end_prob_base) < 1e-9) return 1.0;
      double onemodel_ratio = (qcpx_front_prob + end_prob_base) / (front_prob_base + end_prob_base);  // NOLINT
      params_.bagging_residual_cvr_ratio = cvr_uplift_ratio;
      params_.bagging_onemodel_cvr_ratio = onemodel_ratio;
      cvr_uplift_ratio = (onemodel_ratio + cvr_uplift_ratio) / 2.0;
    }
  }
  // 搜后推
  if (params_.enable_qcpx_live_search_and_push) {
    cvr_uplift_ratio *= params_.inner_qcpx_search_and_push_ratio;
  }
  // CVR uplift ratio UB
  if (params_.enable_qcpx_live_cvr_uplift_bound) {
    cvr_uplift_ratio = std::min(cvr_uplift_ratio, params_.value_qcpx_live_cvr_uplift_bound);
  }
  return cvr_uplift_ratio;
}

double QcpxLiveStrategy::CalcDiscountCouponLiveAudienceRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount) {  // NOLINT
  // 激励短引不走 ctr_uplift
  if (params_.enable_qcpx_rewarded &&
    p_ad->Is(AdFlag::is_live_ad_inner) &&
    (session_data_->get_is_rewarded() || session_data_->get_is_inspire_live_request())) {
      return 1.0;
  }

  double ctr_ratio = 1.0;
  double origin_live_audience = GetLiveAudience(p_ad);
  double d10 = (1000.0 - coupon_rate) / 1000.0 * 50.0 / 10.0;  // 对齐模型
  if (origin_live_audience >= 1.0 || origin_live_audience <= 0.0) return 1.0;
  double epsilon = 1e-8;
  if (params_.enable_p2l_multi_predict) {
    origin_live_audience = std::min(std::max(origin_live_audience, epsilon), 1 - epsilon);
  }
  double origin_logit_divisor = 1.0 - origin_live_audience;
  if (origin_logit_divisor == 0) return 1.0;
  double origin_logit = std::log(origin_live_audience / origin_logit_divisor);
  // 异常情况 直接返回 1.0
  double ctr_elastic_d0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_d0);  // NOLINT
  double ctr_elastic_d1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_d1);  // NOLINT
  // onemodel
  if (params_.enable_p2l_multi_predict) {
    ctr_elastic_d0 = p_ad->get_predict_score(PredictType::PredictType_p2l_qcpx_d0);  // NOLINT
    ctr_elastic_d1 = p_ad->get_predict_score(PredictType::PredictType_p2l_qcpx_d1);  // NOLINT
  }
  double qcpx_logit = origin_logit + ctr_elastic_d0 + ctr_elastic_d1 * d10;
  if (params_.enable_live_qcpx_live_ctr_one_model_uplift && p_ad->Is(AdFlag::is_merchant_item_live)) {
    double ctr_live_elastic_d1 = p_ad->get_predict_score(PredictType::PredictType_live_audience_qcpx_d1);  // NOLINT
    double ctr_live_elastic_d0 = p_ad->get_predict_score(PredictType::PredictType_live_audience_qcpx_d0);  // NOLINT
    qcpx_logit = origin_logit + ctr_live_elastic_d1 * d10;
    if (params_.enable_live_qcpx_live_ctr_one_model_piecewise_uplift) {
      qcpx_logit = origin_logit + ctr_live_elastic_d0 + ctr_live_elastic_d1 * d10;
    }
  }
  if (params_.enable_qcpx_ctr_bound) {
    qcpx_logit = std::min(std::max(qcpx_logit, -50.0), 15.0);
  } else {
    qcpx_logit = std::min(std::max(qcpx_logit, -15.0), 15.0);  //  -15, 15
  }
  double qcpx_ctr = std::exp(qcpx_logit) / (1 + std::exp(qcpx_logit));
  if (params_.enable_qcpx_live_ctr_adjust && p_ad->Is(AdFlag::is_merchant_item_live)) {
    qcpx_ctr *= params_.value_qcpx_live_ctr_disc_adjust_coef;
  }
  ctr_ratio = qcpx_ctr / origin_live_audience;
  // p2l ctr cali 非 onemodel
  if (params_.enable_qcpx_p2l_ctr_cali && !params_.enable_p2l_multi_predict && p_ad->Is(AdFlag::is_p2l_ad_inner)) {  // NOLINT
    ctr_ratio *= params_.value_qcpx_p2l_ctr_cali_coef;
  }
  if (params_.enable_qcpx_ctr_bound) {
    ctr_ratio = std::min(ctr_ratio, params_.value_qcpx_live_ctr_ratio_bound);
  }
  return ctr_ratio;
}

void QcpxLiveStrategy::FillCouponInfoV3(AdCommon* p_ad) {
  bool flag = true;
  flag &= params_.coupon_type > 0;
  if (!flag) return;
  int32_t qcpx_put_type = p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  if (params_.coupon_type == 1) {
    flag &= params_.amount_coupon_id > 0;
    flag &= params_.coupon_amount > 0;
    ProcessThreshold(p_ad);
    flag &= params_.coupon_threshold > params_.coupon_amount;
    if (!flag) return;
    p_ad->set_coupon_template_id(params_.amount_coupon_id);
    p_ad->set_coupon_type(params_.coupon_type);
    p_ad->set_threshold(params_.coupon_threshold);
    p_ad->set_coupon_discount_amount(params_.coupon_amount);
    p_ad->set_threshold_type(1);
    p_ad->set_threshold_upper(8000000);
    p_ad->set_discount_amount_upper(150000);
    p_ad->set_reduce_amount(0);
    p_ad->set_capped_amount(0);
    if (params_.enable_qcpx_expire_minutes) {
      p_ad->set_expire_minutes(30);
      // 若命中短有效期 ID kconf 修改为 15
      if (RankKconfUtil::qcpxTmpIdList() != nullptr) {
        if (RankKconfUtil::qcpxTmpIdList()->count(params_.amount_coupon_id) > 0) {
          p_ad->set_expire_minutes(15);
        }
      }
    }
  } else if (params_.coupon_type == 2) {
    flag &= params_.rate_coupon_id > 0;
    flag &= params_.rate_coupon_map.count(params_.rate_coupon_id) > 0;
    if (!flag) return;
    p_ad->set_coupon_template_id(params_.rate_coupon_id);
    p_ad->set_coupon_type(params_.coupon_type);
    p_ad->set_reduce_amount(params_.rate_coupon_map.at(params_.rate_coupon_id).coupon_rate);
    p_ad->set_capped_amount(params_.rate_coupon_map.at(params_.rate_coupon_id).capped_amount);
    p_ad->set_threshold(0);
    p_ad->set_coupon_discount_amount(0);
    p_ad->set_threshold_upper(8000000);
    p_ad->set_discount_amount_upper(150000);
    if (params_.enable_qcpx_expire_minutes) {
      p_ad->set_expire_minutes(30);
      // 若命中短有效期 ID kconf 修改为 15
      if (RankKconfUtil::qcpxTmpIdList() != nullptr) {
        if (RankKconfUtil::qcpxTmpIdList()->count(params_.amount_coupon_id) > 0) {
          p_ad->set_expire_minutes(15);
        }
      }
    }
  }

  // 命中染色逻辑策略，清空券信息
  bool is_need_rerank = true;
  if (params_.enable_random_coloring_uplift &&
      params_.random_coloring_flow_percent > 0 &&
      params_.random_coloring_flow_percent <= 50 &&
      params_.inner_qcpx_cause == 32) {
    double random_ratio = ks::ad_base::AdRandom::GetDouble() * 100;
    if (random_ratio < params_.random_coloring_flow_percent) {
      // 命中染色逻辑，不发券
      if (params_.enable_qcpx_random_color_log_cf_q) {
        p_ad->Attr(ItemIdx::qcpx_cf_discount_amount).SetIntValue(p_ad->AttrIndex(), p_ad->get_coupon_discount_amount(), false, false);  // NOLINT
        p_ad->Attr(ItemIdx::qcpx_cf_reduce_amount).SetIntValue(p_ad->AttrIndex(), p_ad->get_reduce_amount(), false, false);  // NOLINT
      }
      p_ad->set_coupon_template_id(0);
      p_ad->set_coupon_type(0);
      p_ad->set_reduce_amount(0);
      p_ad->set_capped_amount(0);
      p_ad->set_threshold(0);
      p_ad->set_coupon_discount_amount(0);
      p_ad->set_threshold_type(0);
      p_ad->set_threshold_upper(0);
      p_ad->set_discount_amount_upper(0);
      // set cause
      p_ad->set_inner_qcpx_cause(41);
    } else if (random_ratio < params_.random_coloring_flow_percent * 2) {
      // 命中染色逻辑，正常发券
      // set cause
      p_ad->set_inner_qcpx_cause(42);
    } else if (params_.enable_qcpx_random_color_v2 && random_ratio < params_.random_coloring_flow_percent * 3) {  // NOLINT
      p_ad->set_coupon_template_id(0);
      p_ad->set_coupon_type(0);
      p_ad->set_reduce_amount(0);
      p_ad->set_capped_amount(0);
      p_ad->set_threshold(0);
      p_ad->set_coupon_discount_amount(0);
      p_ad->set_threshold_type(0);
      p_ad->set_threshold_upper(0);
      p_ad->set_discount_amount_upper(0);
      // set cause
      params_.inner_qcpx_cause = 43;
      p_ad->set_inner_qcpx_cause(43);
      is_need_rerank = false;
    }
  }
  // 券样式优选一期 RCT
  if (params_.enable_qcpx_live_optimal_style_disable_feed_card_rct &&
      params_.inner_qcpx_cause == 32 &&
      params_.value_qcpx_live_optimal_style_disable_feed_card_rct_pp > 0) {
    double random_ratio = ks::ad_base::AdRandom::GetDouble() * 100;
    if (random_ratio < params_.value_qcpx_live_optimal_style_disable_feed_card_rct_pp) {
      // 正常发券 修改因果标签
      // todo: 样本过滤需要适配
      p_ad->set_inner_qcpx_cause(322);
    } else if (random_ratio < params_.value_qcpx_live_optimal_style_disable_feed_card_rct_pp * 2) {
      // 无样式发券 修改因果标签
      p_ad->set_inner_qcpx_cause(321);
      p_ad->set_optimal_style_disable_feed_card(1);
    }
  }

  double uplift_ctr_ratio = 1.0;
  if (params_.enable_live_qcpx_p2l_ctr_uplift && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.is_thanos_request ||  // NOLINT
      params_.enable_live_qcpx_live_ctr_one_model_uplift && p_ad->Is(AdFlag::is_merchant_item_live) && params_.is_thanos_request) {  // NOLINT
    uplift_ctr_ratio = params_.pctr_ratio;
  }
  if (is_need_rerank) {
    p_ad->SetQponInfo(params_.qpon_type, 1.0, params_.pcvr_ratio, uplift_ctr_ratio);
    p_ad->set_coupon_scope(1);
    p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_uplift_ratio).SetDoubleValue(p_ad->AttrIndex(), params_.pcvr_ratio, false, false);  // NOLINT
  }
}

bool QcpxLiveStrategy::IsHoldoutV3(AdCommon* p_ad) {
  if (params_.enable_qcpx_holdout_no_rct && params_.inner_qcpx_cause < 30) {
    return true;
  }
  int32_t qcpx_put_type = p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  if (params_.enable_inner_pec_holdout && qcpx_put_type == 0) {
    return true;
  }
  if (params_.enable_inner_qcpx_open_holdout && qcpx_put_type == 1) {
    return true;
  }
  return false;
}


}  // namespace ad_rank
}  // namespace ks
