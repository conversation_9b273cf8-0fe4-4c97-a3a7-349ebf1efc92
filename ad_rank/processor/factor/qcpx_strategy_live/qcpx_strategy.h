#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <set>
#include <vector>
#include <map>
#include <unordered_map>
#include <unordered_set>

#include "base/common/basic_types.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_proto/maven/ad/coupon/ad_coupon_cache.pb.h"
#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_0.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_1.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_2.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_3.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_4.pb.h"

namespace ks {
namespace ad_rank {

struct AdCommon;
struct ContextData;

struct QcpxLiveCouponInfoV3 {
  int64_t coupon_rate = 0;
  int64_t capped_amount = 0;
};

struct QcpxLiveParams {
  // *********** request 粒度 参数 & 变量 ***********
  // 广告属性
  kuaishou::ad::AdEnum_AdQueueType ad_queue_type;  // normal=1,native=2
  kuaishou::ad::AdEnum::ItemType item_type;  // photo=0,live=1,p2l=2
  kuaishou::ad::AdActionType ocpx_type;  // EVENT_ORDER_PAIED=395,AD_MERCHANT_ROAS=192,AD_STOREWIDE_ROAS=944
  int64_t sub_page_id;
  double mock_cpa_bid;
  bool is_thanos_request = false;
  bool is_valid_antou_order = false;
  double project_roi_ratio = 0.0;
  // 内部测试字段 修改请确认 @fandi
  int64_t user_id = 0;
  bool is_qcpx_test_user = false;
  double qcpx_test_user_boost_ratio = 1.0;
  // spdm
  int32_t value_qcpx_live_model_flow_percent = 0;
  // kconf
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<QcpxCouponConfig>> coupon_config;
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<QcpxCouponLib>> coupon_lib;
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<QcpxCouponAccess>> coupon_access;
  std::shared_ptr<absl::flat_hash_map<std::string, double>> user_roi_coef_map;
  std::shared_ptr<absl::flat_hash_map<int64_t, double>> live_hour_roi_map;
  std::shared_ptr<absl::flat_hash_map<int64_t, double>> p2l_hour_roi_map;
  std::shared_ptr<absl::flat_hash_map<int64_t, double>> p2l_hard_calib_map;
  std::shared_ptr<absl::flat_hash_map<int64_t, double>> p2l_soft_calib_map;
  std::shared_ptr<absl::flat_hash_set<int64_t>> dark_author_list;
  std::shared_ptr<absl::flat_hash_set<int64_t>> author_filter_list;
  std::shared_ptr<absl::flat_hash_set<int64_t>> specific_user_list;
  // buffer
  std::unordered_set<int32_t> coupon_select;
  std::unordered_map<int32_t, double> pctr_uplift_ratios;
  std::unordered_map<int32_t, double> pcvr_uplift_ratios;
  std::unordered_map<int32_t, double> rate_multi_head_output;
  // 监控
  std::unordered_map<uint64_t, int64_t> coupon_delivery_cnt;
  // *********** ad 粒度 参数 & 变量 ***********
  // 优惠券设置
  kuaishou::ad::AdEnum::QponType qpon_type = kuaishou::ad::AdEnum_QponType_UNKNOWN_QPON_TYPE;
  int64_t product_price = 0;
  int64_t coupon_threshold = 0;
  int64_t coupon_amount = 0;
  double bid_ratio = 1.0;
  double pcvr_ratio = 1.0;
  double pctr_ratio = 1.0;
  double cpm_ratio = 1.0;
  // 优惠券设置 v3
  int32_t coupon_type = 0;
  int64_t amount_coupon_id = 0;
  int64_t rate_coupon_id = 0;
  std::vector<int64_t> rate_coupon_list;
  std::vector<int64_t> rate_coupon_rct_list;
  std::unordered_map<int64_t, QcpxLiveCouponInfoV3> rate_coupon_map;
  const ks::platform::AttrTable* coupon_table = nullptr;
  ks::platform::ItemAttr* coupon_type_attr = nullptr;
  ks::platform::ItemAttr* coupon_status_attr = nullptr;
  ks::platform::ItemAttr* coupon_rule_attr = nullptr;

  bool enable_inner_pec_holdout = false;
  bool enable_inner_qcpx_open_holdout = false;
  bool enable_qcpx_live_low_ori_cvr_coupon_amount_rise = false;
  double value_qcpx_live_low_ori_cvr_thres = 0.0;
  int64_t value_qcpx_live_low_ori_cvr_cpa_bid_n = 3;
  bool enable_qcpx_live_low_ori_cvr_no_thres_coupon = false;
  bool enable_qcpx_live_low_ori_cvr_effect_u0 = false;
  int64_t qcpx_live_min_coupon_amount_yuan = 2;
  int64_t qcpx_live_max_coupon_amount_yuan = 15;
  int64_t value_qcpx_live_price_coupon_ratio = 5;
  int64_t inner_qcpx_cause = 0;
  bool enable_qcpx_live_model_use_storewide_roas = false;
  bool enable_qcpx_live_model_use_t7_roi = false;
  double qcpx_live_coupon_adjust_roi_bound = 1.0;
  double qcpx_live_coupon_adjust_roi_bound_open = 1.0;
  double qcpx_live_roas_reduce_roi_bound = 1.0;
  double qcpx_live_order_discount_roi_bound = 1.0;
  double shelf_merchant_live_qcpx_roi_ratio = 1.0;
  double shelf_merchant_live_qcpx_roi_ratio_v1 = 1.0;
  double follow_live_qcpx_roi_ratio = 1.0;
  bool enable_live_qcpx_other_coupon_thre = false;
  int64_t value_qcpx_live_coupon_thre_type = 0;
  bool enable_rct_pred_to_log = false;
  bool enable_qcpx_live_only_orderpay = false;
  bool enable_qcpx_live_orderpay_bid_update = false;
  bool enable_qcpx_live_orderpay_roas_update = false;
  bool enable_qcpx_live_orderpay_storewide_update = false;
  bool enable_qcpx_live_orderpay_pcvr_update = false;
  double value_qcpx_live_sigmoid_transform = 1.0;
  bool enable_qcpx_live_sigmoid_transform = false;
  bool enable_qcpx_live_fix_bspline_model = false;
  bool enable_qcpx_live_rct_check_update = false;
  bool enable_qcpx_live_clip_uplift = false;
  double value_qcpx_live_clip_uplift = 1.5;
  bool enable_qcpx_live_specific_amount_coupon_id = false;
  bool enable_qcpx_live_roas_only_rate_coupon = false;
  bool enable_qcpx_live_roas_use_rate_coupon = false;
  bool enable_qcpx_live_order_use_disc_coupon = false;
  bool enable_qcpx_live_roas_use_reduce_coupon = false;
  bool enable_qcpx_live_roas_use_realtime_atv_for_bid = false;
  bool enable_qcpx_live_cvr_order_disc_onemodel_bagging = false;
  bool enable_qcpx_live_cvr_roas_reduce_onemodel_bagging = false;
  bool enable_qcpx_live_rate_coupon_only_live = false;
  bool enable_qcpx_live_rate_coupon_with_capped = false;
  bool enable_qcpx_holdout_no_rct = false;
  bool enable_qcpx_live_roas_update_disable_live = false;
  double value_qcpx_live_rate_coupon_roi_bound = 1.0;
  double value_qcpx_live_rate_coupon_roi_bound_white = 1.0;
  bool enable_qcpx_live_max_uplift_cvr = false;
  bool enable_qcpx_live_bspline_model = false;
  bool enable_qcpx_live_taylor_bspline_model = false;
  bool enable_random_coloring_uplift = false;
  int64_t random_coloring_flow_percent = 0;
  bool enable_qcpx_random_color_v2 = false;  // qcpx 支持反事实染色
  bool enable_qcpx_before_bagging_cvr_log = false;
  bool enable_change_p2l_thre_when_delivery = false;
  bool enable_change_live_thre_when_delivery = false;
  bool enable_qcpx_p2l_cvr_calib = false;
  int64_t value_p2l_thre_ratio_when_delivery = 4;
  int64_t value_live_thre_ratio_when_delivery = 4;
  int64_t specific_amount_coupon_id = 0;
  int32_t value_qcpx_live_rct_amount_control_flow_percent = 0;
  int32_t value_qcpx_live_rct_amount_treatment_flow_percent = 0;
  int32_t value_qcpx_live_rct_rate_control_flow_percent = 0;
  int32_t value_qcpx_live_rct_rate_treatment_flow_percent = 0;
  bool enable_qcpx_live_bound_rate_coupon_rct = false;
  bool enable_qcpx_live_bound_rate_coupon_model = false;
  int64_t value_qcpx_live_bound_rate_coupon_rct_right = 1000;
  int64_t value_qcpx_live_bound_rate_coupon_rct_left = 1000;
  int64_t value_qcpx_live_bound_rate_coupon_model_right = 1000;
  int64_t value_qcpx_live_bound_rate_coupon_model_left = 1000;
  bool enable_qcpx_live_cpa_bid_amt_thre_order = false;
  bool enable_qcpx_live_cpa_bid_amt_thre_roas = false;
  double qcpx_live_cpa_bid_amt_thre_lower_bound = 0.0;
  bool enable_qcpx_live_cvr_disc_roas_multi_head_model = false;
  bool enable_qcpx_live_full_stage_model = false;
  bool enable_qcpx_live_low_coupon_amount_adjust_run = false;
  bool enable_qcpx_live_low_coupon_amount_adjust = false;
  bool enable_qcpx_live_low_coupon_amount_tag = false;
  int64_t value_qcpx_live_roi_tag_a_thres_lower = 0;
  int64_t value_qcpx_live_roi_tag_a_thres_upper = 0;
  int64_t value_qcpx_live_roi_tag_a_ori_q = 0;
  int64_t value_qcpx_live_roi_tag_a_new_q = 0;
  int64_t value_qcpx_live_roi_tag_b_thres_lower = 0;
  int64_t value_qcpx_live_roi_tag_b_thres_upper = 0;
  int64_t value_qcpx_live_roi_tag_b_ori_q = 0;
  int64_t value_qcpx_live_roi_tag_b_new_q = 0;
  int64_t value_qcpx_live_roi_tag_c_thres_lower = 0;
  int64_t value_qcpx_live_roi_tag_c_thres_upper = 0;
  int64_t value_qcpx_live_roi_tag_c_ori_q = 0;
  int64_t value_qcpx_live_roi_tag_c_new_q = 0;
  bool enable_qcpx_live_roas_update_cvr_cali = false;
  double value_qcpx_live_roas_update_live_cvr_cali = 1.0;
  double value_qcpx_live_roas_update_p2l_cvr_cali = 1.0;
  bool enable_qcpx_live_skip_low_roi = false;
  double value_qcpx_live_skip_low_roi_threshold = 1.0;
  bool enable_qcpx_live_skip_low_roi_roas = false;
  bool enable_qcpx_live_skip_low_roi_order = false;
  bool enable_thre_to_log = false;
  bool enable_bspline_to_log = false;
  bool enable_live_qcpx_p2l_ctr_uplift = false;
  bool enable_live_qcpx_live_ctr_one_model_uplift = false;
  bool enable_qcpx_live_ctr_adjust = false;
  double value_qcpx_live_ctr_disc_adjust_coef = 1.0;
  double value_qcpx_live_ctr_reduce_adjust_coef = 1.0;
  bool enable_qcpx_rewarded = false;
  bool enable_live_qcpx_live_ctr_one_model_piecewise_uplift = false;
  bool enable_qcpx_live_roas_rate_coupon_use_customer_bid = false;
  bool enable_qcpx_live_roas_rate_coupon_not_minus_c = false;
  bool enable_qcpx_ctr_bound = false;
  double value_qcpx_live_ctr_ratio_bound = 200.;
  bool enable_qcpx_upper_bound_cpa_bid_coef = false;
  double value_qcpx_upper_bound_cpa_bid_coef = 1.0;
  double value_qcpx_upper_bound_cpa_bid_coef_open = 1.0;
  bool enable_qcpx_live_cpa_bid_amt_thre_order_rct = false;
  bool enable_qcpx_live_no_threshold = false;
  bool enable_qcpx_discount_coupon_gmv_price_ratio = false;
  double value_qcpx_discount_coupon_gmv_price_ratio_1d_weight = 0.5;
  bool enable_qcpx_live_storewide_use_auto_roas = false;
  bool enable_qcpx_expire_minutes = false;
  bool enable_p2l_multi_predict = false;
  bool enable_qcpx_live_cvr_uplift_bound = false;
  double value_qcpx_live_cvr_uplift_bound = 5.0;
  bool enable_qcpx_live_roas_sep_roi_coef = false;
  double value_qcpx_live_merchant_roas_sep_roi_coef = 1.0;
  double value_qcpx_live_t7_roas_sep_roi_coef = 1.0;
  double value_qcpx_live_storewide_roas_sep_roi_coef = 1.0;
  double value_qcpx_live_roas_sep_roi_coef_non_open_ratio = 1.2;
  bool enable_qcpx_live_add_nonq_reason = false;
  bool disable_shelf_qcpx_live_disc_coupon = false;
  bool enable_qcpx_no_thres_blacklist = false;
  bool enable_qcpx_live_actual_storewide_bid_update = false;
  bool enable_qcpx_live_amt_cpm_bid_update = false;
  bool enable_qcpx_live_nobid_bid_update = false;
  bool enable_qcpx_live_p2l_roi_bound_seperate = false;
  double value_qcpx_live_roi_bound_p2l_open = 1.0;
  double value_qcpx_live_roi_bound_p2l_pec = 1.0;
  double value_qcpx_unify_ROI_pacing_p2l_storewide = 1.0;
  double value_qcpx_unify_ROI_pacing_p2l_t7 = 1.0;
  bool enable_inner_live_qcpx_t7roas = false;
  bool enable_inner_live_qcpx_order = false;
  bool enable_inner_live_qcpx_roas = false;
  bool enable_qcpx_live_cvr_onemodel_bagging = false;
  bool enable_qcpx_p2l_ctr_cali = false;
  double value_qcpx_p2l_ctr_cali_coef = 1.0;
  double bagging_residual_cvr_ratio = 0.0;
  double bagging_onemodel_cvr_ratio = 0.0;
  bool enable_qcpx_use_realtime_author_gmv_price_ratio = false;
  double realtime_atv_3_h = 0.0;
  double min_gmv_live_stream_1_h = 0.0;
  double max_gmv_live_stream_1_h = 0.0;
  double min_gmv_author_3_h = 0.0;
  double max_gmv_author_3_h = 0.0;
  bool enable_qcpx_live_high_gmv_diff_depress_roas_reduce = false;
  double value_qcpx_live_pgmv_min_diff_ratio_threshold = 1.0;
  double value_qcpx_live_high_gmv_diff_order_count_threshold = 0.0;
  double value_qcpx_live_high_gmv_diff_roi_pacing_coef_slope = 0.0;
  double pay_amount_author_3_h = 0.0;
  double real_pay_amount_author_3_h = 0.0;
  double platform_bear_amount_author_3_h = 0.0;
  double item_num_author_3_h = 0.0;
  double order_num_author_3_h = 0.0;
  double item_promotion_author_3_h = 0.0;
  double shop_promotion_author_3_h = 0.0;
  double pay_amount_live_stream_1_h = 0.0;
  double real_pay_amount_live_stream_1_h = 0.0;
  double platform_bear_amount_live_stream_1_h = 0.0;
  double item_num_live_stream_1_h = 0.0;
  double order_num_live_stream_1_h = 0.0;
  double item_promotion_live_stream_1_h = 0.0;
  double shop_promotion_live_stream_1_h = 0.0;
  double live_realtime_atv_1_h = 0.0;
  double origin_price_consider_shop_promotion_author_3_h = 0.0;
  double author_gmv_price_ratio_3h = 0.0;
  bool enable_fix_qcpx_live_disc_bid_ratio = false;
  bool enable_qcpx_live_allocate_flow_v2 = false;
  bool enable_qcpx_random_color_log_cf_q = false;
  bool enable_qcpx_live_realtime_atv_3h_safety = false;
  bool enable_qcpx_live_order_disc_bid_shield = false;
  double value_qcpx_live_order_disc_bid_threshold = 0.0;
  bool enable_qcpx_live_order_disc_use_gmv_price_ratio = false;
  bool enable_qcpx_live_order_disc_antou_reuse_roas = false;
  bool enable_qcpx_live_ctr_shield_depress = false;
  const double epsilon = 1e-9;  // std::fabs(x) < params_.epsilon 用于比较浮点数 x 是否等于 0  // NOLINT
  double inner_qcpx_order_cvr_bagging_ratio = 0.5;
  double inner_qcpx_roas_cvr_bagging_ratio = 0.5;
  bool enable_qcpx_live_antou_order_confident_when_antou_ge_cpa = false;
  bool enable_qcpx_live_antou_order_confident_when_antou_le_cpa = false;
  bool enable_qcpx_live_antou_order_unconfident_when_antou_ge_cpa = false;
  bool enable_qcpx_live_antou_order_unconfident_when_antou_le_cpa = false;
  bool enable_qcpx_live_minmax_use_unify_bid = false;
  bool enable_qcpx_rct_disc_skip_low_cpa = false;
  // 商家白名单 调整系数
  std::shared_ptr<absl::flat_hash_set<int64_t>> author_roi_pacing_set;
  bool enable_qcpx_author_roi_pacing = false;
  double value_qcpx_author_roi_pacing = 1.0;
  double inner_qcpx_search_and_push_ratio = 1.0;
  bool enable_qcpx_live_optimal_style_disable_feed_card_rct = false;
  bool enable_qcpx_live_search_and_push = false;
  int64_t value_qcpx_live_optimal_style_disable_feed_card_rct_pp = 0;
};

class QcpxLiveStrategy {
 public:
  explicit QcpxLiveStrategy(ContextData* session_data) : session_data_(session_data) {}
  virtual const char* Name();
  void InitParams();
  bool Admit(AdCommon* p_ad);
  void Process(AdCommon* p_ad);
  void Monitor();

 protected:
  // init
  void InitAdParams(AdCommon* p_ad);
  void InitCouponParams(AdCommon* p_ad);
  void InitCouponParamsV3(AdCommon* p_ad);
  // admit
  bool AdmitRoasUpdate(AdCommon* p_ad);
  bool AdmitV3(AdCommon* p_ad);
  bool IsHoldoutV3(AdCommon* p_ad);
  // common strategy
  void RunRctStrategy(AdCommon* p_ad);
  void RunModelStrategy(AdCommon* p_ad);
  void RunOpenStrategy(AdCommon* p_ad);
  void RunRoasUpdateStrategy(AdCommon* p_ad);
  void RunArchStrategyV3(AdCommon* p_ad);
  // fill coupon
  void FillCouponInfo(AdCommon* p_ad);
  void FillCouponInfoV3(AdCommon* p_ad);
  // 变量
  ContextData* session_data_;
  QcpxLiveParams params_;

 private:
  // calc
  double CalcUnifyPcvr(AdCommon* p_ad);
  double CalcUnifyBid(AdCommon* p_ad);
  double GetAtv(AdCommon* p_ad);
  double GetPcvr(AdCommon* p_ad);
  void GetModelResult(AdCommon* p_ad);
  bool GetMinMaxCouponAmtBid(AdCommon* p_ad, int32_t* min, int32_t* max);
  double RateCouponGetPcvrRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount);
  double RateCouponGetRoasBidRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount, double roi_bound, bool is_customer);  // NOLINT
  double RateCouponGetRoiBound(AdCommon* p_ad);
  double GetLiveAudience(AdCommon* p_ad);
  double CalcReduceCouponLiveAudienceRatio(AdCommon* p_ad, double q10);
  double CalcDiscountCouponLiveAudienceRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount);
  double GetLiveAuthorGmvPriceRatio(AdCommon* p_ad);
  double OrderDiscGetBidRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount, double roi_bound);
  double RoasReduceGetBidRatio(AdCommon* p_ad, double q_yuan, double roi_bound, bool is_customer);
  double RoasReduceGetPcvrRatio(AdCommon* p_ad, double q_yuan);
  // decide method
  void DecideByMaxRatio(AdCommon* p_ad);  // NOLINT
  void DecideByRandom(AdCommon* p_ad);
  void DecideByRandomV3(AdCommon* p_ad);
  void DecideRoasRateCoupon(AdCommon* p_ad);
  void AllocateFlow(AdCommon* p_ad);
  void AllocateFlowV2(AdCommon* p_ad);
  void LogPredict(AdCommon* p_ad);
  void DecideRoasReduceCoupon(AdCommon* p_ad);
  void DecideOrderDiscCoupon(AdCommon* p_ad);
  // other
  void ProcessThreshold(AdCommon* p_ad);
};

}  // namespace ad_rank
}  // namespace ks
