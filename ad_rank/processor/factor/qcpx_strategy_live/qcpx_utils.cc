#include "teams/ad/ad_rank/processor/factor/qcpx_strategy_live/qcpx_strategy.h"

#include <algorithm>
#include <cstdint>
#include <memory>
#include <numeric>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <random>

#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_rank/data/p2p_data/inner_qcpx_coupon_online_p2p/inner_qcpx_coupon_online_p2p.h"
#include "teams/ad/ad_rank/data/p2p_data/universe_conv_ratio_author_post_data_p2p/universe_conv_ratio_author_post_data_p2p.h"

using kuaishou::ad::AdEnum;
namespace ks {
namespace ad_rank {

bool QcpxLiveStrategy::Admit(AdCommon* p_ad) {
  bool flag = true;
  // match item_type
  flag &= p_ad->Is(AdFlag::is_merchant_live);
  flag &= p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner);
  // match ocpx_action_type
  flag &= p_ad->Is(AdFlag::is_merchant_product);
  if (!params_.enable_qcpx_live_model_use_storewide_roas) {
    flag &= params_.ocpx_type != kuaishou::ad::AD_STOREWIDE_ROAS;
  }
  if (!params_.enable_qcpx_live_model_use_t7_roi) {
    flag &= params_.ocpx_type != kuaishou::ad::AD_MERCHANT_T7_ROI;
  }
  // match account type
  flag &= p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP
          || p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP_MOBILE;
  return flag;
}

bool QcpxLiveStrategy::AdmitRoasUpdate(AdCommon* p_ad) {
  if (!Admit(p_ad)) {
    return false;
  }
  // 货架不发折扣券
  if (params_.disable_shelf_qcpx_live_disc_coupon) {
    return false;
  }
  if (params_.enable_qcpx_live_roas_use_rate_coupon) {
    if (params_.ocpx_type == kuaishou::ad::AD_STOREWIDE_ROAS ||
      params_.ocpx_type == kuaishou::ad::AD_MERCHANT_ROAS ||
      params_.ocpx_type == kuaishou::ad::AD_MERCHANT_T7_ROI) {
      return true;
    }
  }
  return false;
}

void QcpxLiveStrategy::DecideByRandom(AdCommon* p_ad) {
  // 可选集内随机发券
  auto atv = GetAtv(p_ad);
  p_ad->set_inner_qcpx_live_thre(atv);
  int32_t price_coupon_ratio = params_.value_qcpx_live_price_coupon_ratio;
  if (price_coupon_ratio == 0) return;
  int32_t min_coupon_amount_yuan = params_.qcpx_live_min_coupon_amount_yuan;
  int32_t max_coupon_amount_yuan = params_.qcpx_live_max_coupon_amount_yuan;
  min_coupon_amount_yuan = std::max(min_coupon_amount_yuan, static_cast<int>(std::ceil(0.05 * atv / 1e3)));  // NOLINT
  max_coupon_amount_yuan = std::min(max_coupon_amount_yuan, static_cast<int>((atv + 200) / 1e3 / price_coupon_ratio));  // NOLINT
  bool ret = true;
  if (params_.enable_qcpx_live_cpa_bid_amt_thre_order
    && params_.enable_qcpx_live_cpa_bid_amt_thre_order_rct
    && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
    ret = GetMinMaxCouponAmtBid(p_ad, &min_coupon_amount_yuan, &max_coupon_amount_yuan);
  }
  if (max_coupon_amount_yuan < min_coupon_amount_yuan || !ret) {
    params_.inner_qcpx_cause = 0;
    p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::STRATEGY_MINPRICE);
    return;
  }

  int32_t random_coupon_amount = ks::ad_base::AdRandom::GetInt(min_coupon_amount_yuan, max_coupon_amount_yuan) * 1e3;  // NOLINT
  int32_t random_coupon_threshold = random_coupon_amount * price_coupon_ratio;  // NOLINT
  params_.coupon_threshold = random_coupon_threshold - 200;
  params_.coupon_amount = random_coupon_amount;
  params_.cpm_ratio = 1.0;  // 不改竞价
  ks::ad_base::AdPerf::CountLogStash(1,
        "ad.ad_rank", "Inner_live_qcpx_uplift_rct_exp",
        absl::StrCat(random_coupon_amount));
}

void QcpxLiveStrategy::DecideByMaxRatio(AdCommon* p_ad) {  // NOLINT
  if (params_.enable_qcpx_live_only_orderpay &&
      p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) return;  // NOLINT
  double bid = CalcUnifyBid(p_ad);
  double pctr_ratio = 1.0;
  double pcvr_ratio = 1.0;
  double bid_ratio = 1.0;
  double cpm_ratio = 1.0;
  int64_t min_threshold = 0;
  int64_t tmp_amount = 0;
  auto atv = GetAtv(p_ad);
  p_ad->set_inner_qcpx_live_thre(atv);
  params_.inner_qcpx_cause = 30;  // model

  int32_t price_coupon_ratio = params_.value_qcpx_live_price_coupon_ratio;
  if (price_coupon_ratio == 0) return;
  int32_t min_coupon_amount_yuan = params_.qcpx_live_min_coupon_amount_yuan;
  int32_t max_coupon_amount_yuan = params_.qcpx_live_max_coupon_amount_yuan;
  min_coupon_amount_yuan = std::max(min_coupon_amount_yuan, static_cast<int>(std::ceil(0.05 * atv / 1e3)));  // NOLINT
  max_coupon_amount_yuan = std::min(max_coupon_amount_yuan, static_cast<int>((atv + 200) / 1e3 / price_coupon_ratio));  // NOLINT   

  bool ret = true;
  if (params_.enable_qcpx_live_cpa_bid_amt_thre_order &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
    ret = GetMinMaxCouponAmtBid(p_ad, &min_coupon_amount_yuan, &max_coupon_amount_yuan);
  }
  if (params_.enable_qcpx_live_cpa_bid_amt_thre_roas &&
      p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
    ret = GetMinMaxCouponAmtBid(p_ad, &min_coupon_amount_yuan, &max_coupon_amount_yuan);
  }
  if (max_coupon_amount_yuan < min_coupon_amount_yuan || !ret) {
    params_.inner_qcpx_cause = 0;
    p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::STRATEGY_MINPRICE);
    return;
  }

  // 商家白名单 调整系数
  double unify_roi_pacing_coef_author = 1.0;
  if (params_.enable_qcpx_author_roi_pacing &&
      params_.author_roi_pacing_set != nullptr &&
      params_.author_roi_pacing_set->count(p_ad->get_author_id())) {
    unify_roi_pacing_coef_author = params_.value_qcpx_author_roi_pacing;
  }

  // 一次回归
  double elastic_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c0);  // NOLINT
  double elastic_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c1);  // NOLINT
  p_ad->set_inner_qcpx_live_cvr_elastic_c0_front(elastic_c0);
  p_ad->set_inner_qcpx_live_cvr_elastic_c1_front(elastic_c1);

  // 样条回归
  double elastic_bspline_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c0);  // NOLINT
  double elastic_bspline_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c1);  // NOLINT
  double elastic_bspline_c2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c2);  // NOLINT
  double elastic_bspline_c3 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c3);  // NOLINT
  double elastic_bspline_c4 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c4);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c0).SetDoubleValue(p_ad->AttrIndex(), elastic_bspline_c0, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c1).SetDoubleValue(p_ad->AttrIndex(), elastic_bspline_c1, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c2).SetDoubleValue(p_ad->AttrIndex(), elastic_bspline_c2, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c3).SetDoubleValue(p_ad->AttrIndex(), elastic_bspline_c3, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_live_cvr_elastic_bspline_c4).SetDoubleValue(p_ad->AttrIndex(), elastic_bspline_c4, false, false);  // NOLINT

  // 全链路模型
  double elastic_c0_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c0_stage1);  // NOLINT
  double elastic_c0_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c0_stage2);  // NOLINT
  double elastic_c1_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c1_stage1);  // NOLINT
  double elastic_c1_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c1_stage2);  // NOLINT
  double elastic_c2_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c2_stage1);  // NOLINT
  double elastic_c2_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c2_stage2);  // NOLINT
  double elastic_c3_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c3_stage1);  // NOLINT
  double elastic_c3_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c3_stage2);  // NOLINT
  double elastic_c4_stage1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c4_stage1);  // NOLINT
  double elastic_c4_stage2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_elastic_c4_stage2);  // NOLINT

  // 泰勒样条回归模型
  double taylor_elastic_bspline_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c0);  // NOLINT
  double taylor_elastic_bspline_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c1);  // NOLINT
  double taylor_elastic_bspline_c2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c2);  // NOLINT
  double taylor_elastic_bspline_c3 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c3);  // NOLINT
  double taylor_elastic_bspline_c4 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c4);  // NOLINT
  double taylor_elastic_bspline_c1_prime = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c1_prime);  // NOLINT
  // Onemodel CVR
  bool is_cvr_order_onemodel = params_.enable_inner_live_qcpx_order && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;  // NOLINT
  double amt_w = p_ad->get_predict_score(PredictType::PredictType_inner_live_atom_paycnt_bak4);  // NOLINT
  double amt_b = p_ad->get_predict_score(PredictType::PredictType_inner_live_atom_paycnt_bak5);  // NOLINT
  double paycnt_front_predict_base = p_ad->get_inner_live_atom_paycnt_front();
  double paycnt_end_predict_base = p_ad->get_inner_live_atom_paycnt_end();
  // Core Strategy Part
  double ori_pcvr = 0.0;
  if (p_ad->Is(AdFlag::is_inner_loop_order_ad)) {
    ori_pcvr = p_ad->get_unify_cvr_info().value;
  } else {
    ori_pcvr = p_ad->get_unify_ltv_info().value;
  }
  if (ori_pcvr <= 0) return;
  // Support Uplift for CTR @fandi
  double origin_live_audience = GetLiveAudience(p_ad);
  bool original_ctr_is_valid = origin_live_audience < 1.0 && origin_live_audience > 0.0;
  double tmp_max_profit_ctr_ratio = 1.0;
  double clip_ori_pcvr = ori_pcvr;
  int32_t tmp_max_profit_i = 0;
  int32_t tmp_max_profit_coupon_amount = 0;
  int32_t tmp_max_profit_coupon_threshold = 0;
  double tmp_max_profit_ecpm_ratio = 1.0;
  double tmp_max_profit_cvr = clip_ori_pcvr;
  double tmp_stage1_uplift_paycnt = 0.0;
  double tmp_stage2_uplift_paycnt = 0.0;
  // Start Qpon Search
  for (int32_t i = min_coupon_amount_yuan; i <= max_coupon_amount_yuan; ++i) {
    double q_10y = i / 10.0;  // 单位 十元
    double q = i * 1e3;
    // @fandi skip low roi
    if (params_.enable_qcpx_live_skip_low_roi && params_.mock_cpa_bid <= q * params_.value_qcpx_live_skip_low_roi_threshold) {  // NOLINT
      if (params_.enable_qcpx_live_skip_low_roi_roas && p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {  // NOLINT
        break;
      }
      if (params_.enable_qcpx_live_skip_low_roi_order && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {  // NOLINT
        break;
      }
    }
    // Support Uplift for CTR @fandi
    double ctr_ratio = CalcReduceCouponLiveAudienceRatio(p_ad, q_10y);

    if (params_.enable_qcpx_live_ctr_shield_depress) {
      ctr_ratio = std::max(ctr_ratio, 1.0);
    }
    double ctcvr_ratio = 1.0;
    double new_ecpm_ratio = 1.0;
    double ratio = 1.0;
    double stage1_ratio = 1.0;
    double stage2_ratio = 1.0;
    double qcpx_logit = 0.0;
    double qcpx_logit_stage1 = 0.0;
    double qcpx_logit_stage2 = 0.0;
    double stage1_uplift_paycnt = 0.0;
    double stage2_uplift_paycnt = 0.0;
    int32_t coupon_threshold = q * price_coupon_ratio;
    double orderpay_value_live_roi_bound = 1.0;
    double bagging_residual_cvr_ratio = 0.0;
    double bagging_onemodel_cvr_ratio = 0.0;
    // =================================== CalcReduceCouponCVRRatio Start ===================================
    if (params_.enable_qcpx_live_taylor_bspline_model) {
      double taylor_elastic_bspline_c2_prime = taylor_elastic_bspline_c1_prime + 0.9 * taylor_elastic_bspline_c1;  // NOLINT
      double taylor_elastic_bspline_c3_prime = taylor_elastic_bspline_c1_prime + 0.9 * taylor_elastic_bspline_c1 + 0.7 * taylor_elastic_bspline_c2;  // NOLINT
      double taylor_elastic_bspline_c4_prime = taylor_elastic_bspline_c1_prime + 0.9 * taylor_elastic_bspline_c1 + 0.7 * taylor_elastic_bspline_c2 + 0.7 * taylor_elastic_bspline_c3;  // NOLINT
      if (i < 9) {
        qcpx_logit = taylor_elastic_bspline_c0 +
                    taylor_elastic_bspline_c1_prime * q_10y + taylor_elastic_bspline_c1 * std::pow(q_10y, 2) / 2;  // NOLINT
      } else if (i < 16) {
        qcpx_logit = taylor_elastic_bspline_c0 +
                    taylor_elastic_bspline_c1_prime * 0.9 + taylor_elastic_bspline_c1 * std::pow(0.9, 2) / 2 +   // NOLINT
                    taylor_elastic_bspline_c2_prime * (q_10y - 0.9) + taylor_elastic_bspline_c2 * std::pow((q_10y - 0.9), 2) / 2;  // NOLINT
      } else if (i < 23) {
        qcpx_logit = taylor_elastic_bspline_c0 +
                    taylor_elastic_bspline_c1_prime * 0.9 + taylor_elastic_bspline_c1 * std::pow(0.9, 2) / 2 +  // NOLINT
                    taylor_elastic_bspline_c2_prime * 0.7 + taylor_elastic_bspline_c2 * std::pow(0.7, 2) / 2 +  // NOLINT
                    taylor_elastic_bspline_c3_prime * (q_10y - 1.6) + taylor_elastic_bspline_c3 * std::pow((q_10y - 1.6), 2) / 2;  // NOLINT
      } else {
        qcpx_logit = taylor_elastic_bspline_c0 +
                    taylor_elastic_bspline_c1_prime * 0.9 + taylor_elastic_bspline_c1 * std::pow(0.9, 2) / 2 +  // NOLINT
                    taylor_elastic_bspline_c2_prime * 0.7 + taylor_elastic_bspline_c2 * std::pow(0.7, 2) / 2 +  // NOLINT
                    taylor_elastic_bspline_c3_prime * 0.7 + taylor_elastic_bspline_c3 * std::pow(0.7, 2) / 2 +  // NOLINT
                    taylor_elastic_bspline_c4_prime * (q_10y - 2.3) + taylor_elastic_bspline_c4 * std::pow((q_10y - 2.3), 2) / 2;  // NOLINT
      }
    } else if (params_.enable_qcpx_live_full_stage_model) {
      // 全链路模型优先级高于样条回归模型
      if (p_ad->get_inner_live_atom_paycnt_front() <= 0 || p_ad->get_inner_live_atom_paycnt_end() <= 0) {
        return;
      }
      if (i < 9) {
        qcpx_logit_stage1 = elastic_c0_stage1 + elastic_c1_stage1 * q_10y;
        qcpx_logit_stage2 = elastic_c0_stage2 + elastic_c1_stage2 * q_10y;
      } else if (i < 16) {
        qcpx_logit_stage1 = elastic_c0_stage1 + elastic_c1_stage1 * 0.9 + elastic_c2_stage1 * (q_10y - 0.9);  // NOLINT
        qcpx_logit_stage2 = elastic_c0_stage2 + elastic_c1_stage2 * 0.9 + elastic_c2_stage2 * (q_10y - 0.9);  // NOLINT
      } else if (i < 23) {
        qcpx_logit_stage1 = elastic_c0_stage1 + elastic_c1_stage1 * 0.9 + elastic_c2_stage1 * 0.7 + elastic_c3_stage1 * (q_10y - 1.6);  // NOLINT
        qcpx_logit_stage2 = elastic_c0_stage2 + elastic_c1_stage2 * 0.9 + elastic_c2_stage2 * 0.7 + elastic_c3_stage2 * (q_10y - 1.6);  // NOLINT
      } else {
        qcpx_logit_stage1 = elastic_c0_stage1 + elastic_c1_stage1 * 0.9 + elastic_c2_stage1 * 0.7 + elastic_c3_stage1 * 0.7 + elastic_c4_stage1 * (q_10y - 2.3);  // NOLINT
        qcpx_logit_stage2 = elastic_c0_stage2 + elastic_c1_stage2 * 0.9 + elastic_c2_stage2 * 0.7 + elastic_c3_stage2 * 0.7 + elastic_c4_stage2 * (q_10y - 2.3);  // NOLINT
      }
    } else if (params_.enable_qcpx_live_bspline_model) {
      if (i < 9) {
        qcpx_logit = elastic_bspline_c0 + elastic_bspline_c1 * q_10y;
      } else if (i < 16) {
        qcpx_logit = elastic_bspline_c0 + elastic_bspline_c1 * 0.9 + elastic_bspline_c2 * (q_10y - 0.9);  // NOLINT
      } else if (i < 23) {
        qcpx_logit = elastic_bspline_c0 + elastic_bspline_c1 * 0.9 + elastic_bspline_c2 * 0.7 + elastic_bspline_c3 * (q_10y - 1.6);  // NOLINT
      } else {
        qcpx_logit = elastic_bspline_c0 + elastic_bspline_c1 * 0.9 + elastic_bspline_c2 * 0.7 + elastic_bspline_c3 * 0.7 + elastic_bspline_c4 * (q_10y - 2.3);  // NOLINT
      }
    } else {
      qcpx_logit = elastic_c0 + elastic_c1 * q_10y;
    }
    if (params_.enable_qcpx_live_sigmoid_transform) {
      ratio = params_.value_qcpx_live_sigmoid_transform * std::exp(qcpx_logit) / (1 + std::exp(qcpx_logit));
      stage1_ratio = params_.value_qcpx_live_sigmoid_transform * std::exp(qcpx_logit_stage1) / (1 + std::exp(qcpx_logit_stage1));  // NOLINT
      stage2_ratio = params_.value_qcpx_live_sigmoid_transform * std::exp(qcpx_logit_stage2) / (1 + std::exp(qcpx_logit_stage2));  // NOLINT
    } else {
      ratio = std::exp(std::min(std::max(qcpx_logit, -3.0), 3.0));
      stage1_ratio = std::exp(std::min(std::max(qcpx_logit_stage1, -3.0), 3.0));
      stage2_ratio = std::exp(std::min(std::max(qcpx_logit_stage2, -3.0), 3.0));
    }
    if (params_.enable_qcpx_live_full_stage_model) {
      stage1_uplift_paycnt = stage1_ratio * p_ad->get_inner_live_atom_paycnt_front();
      stage2_uplift_paycnt = stage2_ratio * p_ad->get_inner_live_atom_paycnt_end();
      ratio = (stage1_uplift_paycnt + stage2_uplift_paycnt) / (p_ad->get_inner_live_atom_paycnt_front() + p_ad->get_inner_live_atom_paycnt_end());  // NOLINT
    }
    // Onemodel CVR bagging 优先级最高
    if (is_cvr_order_onemodel && params_.enable_qcpx_live_cvr_onemodel_bagging) {
      if ((paycnt_front_predict_base + paycnt_end_predict_base <= 0) || paycnt_front_predict_base < 0) return;
      // prob = sigmoid(logit)
      // pred = prob / (1 - prob) = exp(logit)
      // logit = ln(pred)
      double qcpx_front_logit = std::log(paycnt_front_predict_base) + amt_w * q_10y + amt_b;
      double qcpx_front_predict = std::exp(qcpx_front_logit);
      double onemodel_ratio = (qcpx_front_predict + paycnt_end_predict_base) / (paycnt_front_predict_base + paycnt_end_predict_base);  // NOLINT
      bagging_residual_cvr_ratio = ratio;
      bagging_onemodel_cvr_ratio = onemodel_ratio;
      // ratio = (onemodel_ratio + ratio) / 2.0;
      double bagging_coef = params_.inner_qcpx_order_cvr_bagging_ratio;  // 默认 0.5
      ratio = bagging_coef * onemodel_ratio + (1 - bagging_coef) * ratio;
    }
    // 搜后推
    if (params_.enable_qcpx_live_search_and_push) {
      ratio *= params_.inner_qcpx_search_and_push_ratio;
    }
    if (params_.enable_qcpx_live_clip_uplift && p_ad->Is(AdFlag::is_live_ad_inner)) {
      ratio = std::min(ratio, params_.value_qcpx_live_clip_uplift);
    }
    // CVR uplift ratio UB
    if (params_.enable_qcpx_live_cvr_uplift_bound) {
      ratio = std::min(ratio, params_.value_qcpx_live_cvr_uplift_bound);
    }
    // =================================== CalcReduceCouponCVRRatio End ===================================
    ctcvr_ratio = ctr_ratio * ratio;
    if (params_.enable_live_qcpx_p2l_ctr_uplift && original_ctr_is_valid && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.is_thanos_request ||  // NOLINT
        params_.enable_live_qcpx_live_ctr_one_model_uplift && original_ctr_is_valid && p_ad->Is(AdFlag::is_merchant_item_live) && params_.is_thanos_request) {  // NOLINT
      if (ctcvr_ratio <= 1.0) continue;
    } else {
      if (ratio <= 1.0) continue;
    }
    if (bid == 0.0) return;  // todo: 上移 不需要在循环里
    // =================================== GetROIBound start ===================================
    orderpay_value_live_roi_bound = params_.qcpx_live_coupon_adjust_roi_bound;
    if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
      orderpay_value_live_roi_bound = params_.qcpx_live_coupon_adjust_roi_bound_open;
    }
    // 直投 短引 系数拆分
    double unify_roi_pacing_coef = 1.0;
    if (params_.enable_qcpx_live_p2l_roi_bound_seperate && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.value_qcpx_live_roi_bound_p2l_open > 0) {  // NOLINT
      unify_roi_pacing_coef *= params_.value_qcpx_live_roi_bound_p2l_open;
    }
    // 商家白名单 调整系数
    unify_roi_pacing_coef *= unify_roi_pacing_coef_author;
    orderpay_value_live_roi_bound *= unify_roi_pacing_coef;
    // =================================== GetROIBound end ===================================
    new_ecpm_ratio = (1 - orderpay_value_live_roi_bound * q / bid) * ratio;
    if (params_.enable_live_qcpx_p2l_ctr_uplift && original_ctr_is_valid && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.is_thanos_request ||  // NOLINT
        params_.enable_live_qcpx_live_ctr_one_model_uplift && original_ctr_is_valid && p_ad->Is(AdFlag::is_merchant_item_live) && params_.is_thanos_request) {  // NOLINT
      new_ecpm_ratio = (1 - orderpay_value_live_roi_bound * q / bid) * ctcvr_ratio;
    }
    // 内部测试 修改请确认 @fandi
    if (params_.is_qcpx_test_user) {
      new_ecpm_ratio = ratio * params_.qcpx_test_user_boost_ratio;
    }
    if (new_ecpm_ratio > tmp_max_profit_ecpm_ratio) {
      tmp_max_profit_ecpm_ratio = new_ecpm_ratio;
      tmp_max_profit_i = i;
      tmp_max_profit_coupon_amount = q;
      pcvr_ratio = ratio;  // 本质上是 max_profit_cvr_ratio
      tmp_max_profit_ctr_ratio = ctr_ratio;
      tmp_max_profit_cvr = ratio * clip_ori_pcvr;
      tmp_max_profit_coupon_threshold = coupon_threshold;
      tmp_stage1_uplift_paycnt = stage1_uplift_paycnt;
      tmp_stage2_uplift_paycnt = stage2_uplift_paycnt;
      params_.bagging_residual_cvr_ratio = bagging_residual_cvr_ratio;
      params_.bagging_onemodel_cvr_ratio = bagging_onemodel_cvr_ratio;
    }
  }
  if (tmp_max_profit_i == 0) {
    p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::STRATEGY_MODEL);
    params_.inner_qcpx_cause = 31;
  } else {
    params_.inner_qcpx_cause = 32;
    pctr_ratio = 1.0;
    if (params_.enable_live_qcpx_p2l_ctr_uplift && original_ctr_is_valid && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.is_thanos_request ||  // NOLINT
        params_.enable_live_qcpx_live_ctr_one_model_uplift && original_ctr_is_valid && p_ad->Is(AdFlag::is_merchant_item_live) && params_.is_thanos_request) {  // NOLINT
      pctr_ratio = tmp_max_profit_ctr_ratio;
    }
    if (params_.enable_qcpx_live_amt_cpm_bid_update) {
      bid = p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : p_ad->get_cpa_bid();
      if (bid <= 0) return;
    }
    if (bid == 0) return;
    bid_ratio = 1.0 - tmp_max_profit_i * 1e3 / bid;
    cpm_ratio = bid_ratio * pctr_ratio * pcvr_ratio;
    if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_PEC) {
      cpm_ratio = tmp_max_profit_ecpm_ratio;
    }
    // 内部测试 修改请确认 @fandi
    if (params_.is_qcpx_test_user) {
      bid_ratio = 1.0;
      pcvr_ratio = pcvr_ratio * params_.qcpx_test_user_boost_ratio;
      cpm_ratio = pcvr_ratio;
    }
    if (cpm_ratio > 1.0) {  // NOLINT
      params_.coupon_threshold = tmp_max_profit_coupon_threshold - 200;
      params_.coupon_amount = tmp_max_profit_coupon_amount;
      params_.cpm_ratio = cpm_ratio;
      params_.bid_ratio = bid_ratio;
      params_.pctr_ratio = pctr_ratio;
      params_.pcvr_ratio = pcvr_ratio;
      p_ad->set_inner_qcpx_live_cvr(tmp_max_profit_cvr);
      if (params_.enable_qcpx_live_full_stage_model) {
        p_ad->Attr(ItemIdx::inner_qcpx_live_stage1_cvr).SetDoubleValue(p_ad->AttrIndex(), tmp_stage1_uplift_paycnt, false, false);  // NOLINT
        p_ad->Attr(ItemIdx::inner_qcpx_live_stage2_cvr).SetDoubleValue(p_ad->AttrIndex(), tmp_stage2_uplift_paycnt, false, false);  // NOLINT
      }
      p_ad->Attr(ItemIdx::inner_qcpx_live_ctr).SetDoubleValue(p_ad->AttrIndex(), origin_live_audience * params_.pctr_ratio, false, false);  // NOLINT
      if (params_.enable_qcpx_before_bagging_cvr_log) {
        // before bagging to log
        p_ad->Attr(ItemIdx::inner_qcpx_live_bagging_residual_cvr).SetDoubleValue(p_ad->AttrIndex(), params_.bagging_residual_cvr_ratio * clip_ori_pcvr, false, false);  // NOLINT
        p_ad->Attr(ItemIdx::inner_qcpx_live_bagging_onemodel_cvr).SetDoubleValue(p_ad->AttrIndex(), params_.bagging_onemodel_cvr_ratio * clip_ori_pcvr, false, false);  // NOLINT
      }
      if (params_.enable_qcpx_live_order_use_disc_coupon) {
        RANK_DOT_COUNT(session_data_, 1,
          "Order_reduce_delivery",
          absl::StrCat(p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0)),
          absl::StrCat(p_ad->get_item_type()),
          absl::StrCat(p_ad->get_ad_queue_type()),
          absl::StrCat(p_ad->get_ocpx_action_type()));
      }
    }
  }
}

void QcpxLiveStrategy::DecideOrderDiscCoupon(AdCommon* p_ad) {
  double ori_inner_qcpx_cause = params_.inner_qcpx_cause;
  if (p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) return;  // NOLINT
  auto& tmp_map = params_.rate_coupon_map;
  double origin_live_audience = GetLiveAudience(p_ad);
  double bid = CalcUnifyBid(p_ad);
  double tmp_ctr_ratio = 1.0;
  double tmp_pcvr_ratio = 1.0;
  double tmp_bid_ratio = 1.0;
  double tmp_decay_bid_ratio = 1.0;
  double tmp_cpm_ratio = 1.0;
  double orderpay_value_live_roi_bound = 1.0;
  double ori_pcvr = GetPcvr(p_ad);
  if (bid == 0.0) return;
  if (ori_pcvr <= 0) return;
  // 实时 ATV 数据兜底: 等于 0 (近期没卖出去东西) 则不发券
  if (params_.enable_qcpx_live_realtime_atv_3h_safety && std::fabs(params_.realtime_atv_3_h) < params_.epsilon) return;   // NOLINT
  // 订单发折扣券低 bid 屏蔽
  if (params_.enable_qcpx_live_order_disc_bid_shield && bid < params_.value_qcpx_live_order_disc_bid_threshold * 1000) return;  // NOLINT
  // =================================== GetROIBound start ===================================
  // 默认 ROI 约束策略
  orderpay_value_live_roi_bound = params_.qcpx_live_coupon_adjust_roi_bound;
  if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
    orderpay_value_live_roi_bound = params_.qcpx_live_coupon_adjust_roi_bound_open;
  }
  // 订单发折扣券用单独的系数
  if (params_.qcpx_live_order_discount_roi_bound > 0.0) {
    orderpay_value_live_roi_bound = params_.qcpx_live_order_discount_roi_bound;
    if (params_.qpon_type != kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
      orderpay_value_live_roi_bound *= 1.2;
    }
  }
  double unify_roi_pacing_coef = 1.0;
  if (params_.enable_qcpx_live_p2l_roi_bound_seperate && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.value_qcpx_live_roi_bound_p2l_open > 0) {  // NOLINT
    unify_roi_pacing_coef *= params_.value_qcpx_live_roi_bound_p2l_open;
  }
  // 商家白名单 调整系数
  if (params_.enable_qcpx_author_roi_pacing &&
      params_.author_roi_pacing_set != nullptr &&
      params_.author_roi_pacing_set->count(p_ad->get_author_id())) {
    unify_roi_pacing_coef *= params_.value_qcpx_author_roi_pacing;
  }
  orderpay_value_live_roi_bound *= unify_roi_pacing_coef;
  // =================================== GetROIBound end ===================================
  for (auto iter = tmp_map.begin(); iter != tmp_map.end(); iter++) {
    const auto& coupon_info = iter->second;
    if (params_.enable_qcpx_live_bound_rate_coupon_model) {
      if (coupon_info.coupon_rate < params_.value_qcpx_live_bound_rate_coupon_model_left
          || coupon_info.coupon_rate > params_.value_qcpx_live_bound_rate_coupon_model_right) {
        continue;
      }
    }
    tmp_pcvr_ratio = RateCouponGetPcvrRatio(p_ad, coupon_info.coupon_rate, coupon_info.capped_amount);
    tmp_decay_bid_ratio = OrderDiscGetBidRatio(p_ad, coupon_info.coupon_rate, coupon_info.capped_amount, orderpay_value_live_roi_bound);  // NOLINT
    tmp_ctr_ratio = CalcDiscountCouponLiveAudienceRatio(p_ad, coupon_info.coupon_rate, coupon_info.capped_amount);  // NOLINT
    if (params_.enable_qcpx_live_ctr_shield_depress) {
      tmp_ctr_ratio = std::max(tmp_ctr_ratio, 1.0);
    }
    if (params_.enable_live_qcpx_p2l_ctr_uplift && p_ad->Is(AdFlag::is_merchant_item_p2l) && params_.is_thanos_request ||  // NOLINT
        params_.enable_live_qcpx_live_ctr_one_model_uplift && p_ad->Is(AdFlag::is_merchant_item_live) && params_.is_thanos_request) {  // NOLINT
      tmp_cpm_ratio = tmp_ctr_ratio * tmp_pcvr_ratio * tmp_decay_bid_ratio;
    } else {
      tmp_cpm_ratio = tmp_pcvr_ratio * tmp_decay_bid_ratio;
    }
    tmp_bid_ratio = OrderDiscGetBidRatio(p_ad, coupon_info.coupon_rate, coupon_info.capped_amount, 1.0);  // NOLINT
    if (tmp_cpm_ratio > params_.cpm_ratio) {
      params_.cpm_ratio = tmp_cpm_ratio;
      params_.pcvr_ratio = tmp_pcvr_ratio;
      params_.bid_ratio = tmp_bid_ratio;
      params_.pctr_ratio = tmp_ctr_ratio;
      // 订单 折扣券覆盖满减券
      params_.coupon_type = 2;
      params_.rate_coupon_id = iter->first;
      params_.inner_qcpx_cause = 32;
      p_ad->Attr(ItemIdx::inner_qcpx_live_ctr).SetDoubleValue(p_ad->AttrIndex(), origin_live_audience * params_.pctr_ratio, false, false);  // NOLINT
      p_ad->set_inner_qcpx_live_cvr(ori_pcvr * tmp_pcvr_ratio);
      if (params_.enable_qcpx_before_bagging_cvr_log) {
        // before bagging to log
        p_ad->Attr(ItemIdx::inner_qcpx_live_bagging_residual_cvr).SetDoubleValue(p_ad->AttrIndex(), params_.bagging_residual_cvr_ratio * ori_pcvr, false, false);  // NOLINT
        p_ad->Attr(ItemIdx::inner_qcpx_live_bagging_onemodel_cvr).SetDoubleValue(p_ad->AttrIndex(), params_.bagging_onemodel_cvr_ratio * ori_pcvr, false, false);  // NOLINT
      }
    }
  }
  if (params_.coupon_type == 2 && params_.inner_qcpx_cause == 32) {
    RANK_DOT_COUNT(session_data_, 1,
      "Order_disc_delivery",
      absl::StrCat(p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0)),
      absl::StrCat(p_ad->get_item_type()),
      absl::StrCat(p_ad->get_ad_queue_type()),
      absl::StrCat(p_ad->get_ocpx_action_type()));
    if (ori_inner_qcpx_cause == 32) {
      RANK_DOT_COUNT(session_data_, 1,
        "Order_disc_replace",
        absl::StrCat(p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0)),
        absl::StrCat(p_ad->get_item_type()),
        absl::StrCat(p_ad->get_ad_queue_type()),
        absl::StrCat(p_ad->get_ocpx_action_type()));
    }
  }
}

double QcpxLiveStrategy::OrderDiscGetBidRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount, double roi_bound) {  // NOLINT
  double bid = CalcUnifyBid(p_ad);
  double discount_rate = 1.0 - coupon_rate / 1000.0;
  double gmv = params_.realtime_atv_3_h;
  double discount_amt = gmv * discount_rate;
  double live_gmv_price_ratio = GetLiveAuthorGmvPriceRatio(p_ad);
  double bid_ratio = 1.0 - roi_bound * discount_amt / bid;
  if (params_.enable_qcpx_live_order_disc_use_gmv_price_ratio && live_gmv_price_ratio > 0) {
    bid_ratio = 1.0 - roi_bound * discount_amt / live_gmv_price_ratio / bid;
  }
  if (params_.enable_qcpx_live_order_disc_antou_reuse_roas && params_.is_valid_antou_order && live_gmv_price_ratio > 0) {   // NOLINT
    bid_ratio = 1.0 - roi_bound * params_.project_roi_ratio * discount_rate / live_gmv_price_ratio;
  }
  return bid_ratio;
}

double QcpxLiveStrategy::CalcUnifyPcvr(AdCommon* p_ad) {
  double pcvr = p_ad->get_unify_cvr_info().value;
  if (p_ad->Is(AdFlag::is_inner_live_roas)) {
    pcvr = p_ad->get_order_paid() > 0 ? p_ad->get_order_paid() : p_ad->get_live_order_paid();
  } else if (p_ad->Is(AdFlag::is_inner_photo_roas)) {
    pcvr = p_ad->get_order_paid() > 0 ? p_ad->get_order_paid() : p_ad->get_c1_order_paied();
  }
  pcvr = (pcvr > 0.0 && pcvr < 1.0) ? pcvr : 0.0001;
  return pcvr;
}

double QcpxLiveStrategy::CalcUnifyBid(AdCommon* p_ad) {
  double pcvr = CalcUnifyPcvr(p_ad);
  double bid = 1.0;
  if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
    bid = p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : p_ad->get_cpa_bid();  // NOLINT
    if (params_.enable_qcpx_live_orderpay_bid_update) {
      bid = p_ad->get_cpa_bid() > 0 ? p_ad->get_cpa_bid() : p_ad->get_auto_cpa_bid();
    }
    // 全站暗投订单部分 使用 auto_cpa_bid
    if (params_.enable_qcpx_live_actual_storewide_bid_update && p_ad->get_scene_oriented_type() == 21) {
      bid = p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : p_ad->get_cpa_bid();
    }
    // NOBID 部分 使用 auto_cpa_bid
    if (params_.enable_qcpx_live_nobid_bid_update && p_ad->Is(AdFlag::IsEspUnifyNobidAdV2)) {
      bid = p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : p_ad->get_cpa_bid();
    }
    // 暗投订单支付 特殊处理
    if (params_.is_valid_antou_order && params_.project_roi_ratio > 0.0 && params_.realtime_atv_3_h > 0) {  // NOLINT
      double antou_cpa_bid = params_.realtime_atv_3_h / params_.project_roi_ratio;
      double cpa_bid = p_ad->get_cpa_bid();
      // 置信数据
      if (params_.order_num_author_3_h >= 10) {
        if (antou_cpa_bid > 0 && ((antou_cpa_bid - cpa_bid) / antou_cpa_bid >= 0.2) && params_.enable_qcpx_live_antou_order_confident_when_antou_ge_cpa) {  // NOLINT
          bid = antou_cpa_bid;
        } else if (antou_cpa_bid > 0 && ((cpa_bid - antou_cpa_bid) / antou_cpa_bid >= 0.2) && params_.enable_qcpx_live_antou_order_confident_when_antou_le_cpa) {  // NOLINT
          bid = antou_cpa_bid;
        }
      } else if (params_.order_num_author_3_h >= 3) {
        if (antou_cpa_bid > 0 && ((antou_cpa_bid - cpa_bid) / antou_cpa_bid >= 0.2) && params_.enable_qcpx_live_antou_order_unconfident_when_antou_ge_cpa) {  // NOLINT
          bid = antou_cpa_bid;
        } else if (antou_cpa_bid > 0 && ((cpa_bid - antou_cpa_bid) / antou_cpa_bid >= 0.2) && params_.enable_qcpx_live_antou_order_unconfident_when_antou_le_cpa) {  // NOLINT
          bid = antou_cpa_bid;
        }
      }
    }
  } else {
    double auto_roas = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();  // NOLINT
    if (params_.enable_qcpx_live_orderpay_roas_update && params_.ocpx_type == kuaishou::ad::AD_MERCHANT_ROAS) {  // NOLINT
      auto_roas = p_ad->get_roi_ratio() > 0.0 ? p_ad->get_roi_ratio() : p_ad->get_auto_roas();
    }
    if (params_.enable_qcpx_live_orderpay_storewide_update && params_.ocpx_type == kuaishou::ad::AD_STOREWIDE_ROAS) {  // NOLINT
      auto_roas = p_ad->get_roi_ratio() > 0.0 ? p_ad->get_roi_ratio() : p_ad->get_auto_roas();
    }
    double ecpm = p_ad->get_unify_ltv_info().value * 1e3 / std::max(auto_roas, 0.1) + 1e-8;  // NOLINT
    if (pcvr > 0.0) bid = ecpm / pcvr;
  }
  bid = bid > 100 ? bid : 100;
  return bid;
}

bool QcpxLiveStrategy::GetMinMaxCouponAmtBid(AdCommon* p_ad, int32_t* min, int32_t* max) {
  if (p_ad == nullptr || min == nullptr || max == nullptr) {
    return false;
  }
  int32_t min_coupon_amount_yuan = params_.qcpx_live_min_coupon_amount_yuan;
  int32_t max_coupon_amount_yuan = params_.qcpx_live_max_coupon_amount_yuan;
  double cpa_bid = 0.0;
  if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
    cpa_bid = p_ad->get_cpa_bid();
    double uni_bid = CalcUnifyBid(p_ad);
    if (params_.enable_qcpx_live_minmax_use_unify_bid && uni_bid > 0.0) {
      cpa_bid = uni_bid;
    }
  } else {
    double roi_ratio = p_ad->get_roi_ratio();
    double pltv = p_ad->get_inner_live_roas();
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
      pltv = p_ad->get_inner_live_roas_7days_0_2h();
    }
    double pcvr = p_ad->get_inner_live_atom_paycnt_front() + p_ad->get_inner_live_atom_paycnt_end();
    if (pcvr <= 0.0) {
      pcvr = p_ad->get_inner_live_roi_pay_front() + p_ad->get_inner_live_roi_pay_end();
    }
    if (pcvr <= 0.0) {
      pcvr = p_ad->get_order_paid() > 0.0 ? p_ad->get_order_paid() : p_ad->get_live_order_paid();
    }
    if (pltv <= 0.0 || pcvr <= 0.0 || roi_ratio <= 0.0) {
      return false;
    }
    cpa_bid = 1000 * (pltv / pcvr) / roi_ratio;
    if (params_.enable_qcpx_live_roas_use_realtime_atv_for_bid) {
      if (!(params_.enable_qcpx_live_realtime_atv_3h_safety && std::fabs(params_.realtime_atv_3_h) < params_.epsilon)) {  // NOLINT
        cpa_bid = params_.realtime_atv_3_h / roi_ratio;
      }
    }
  }

  double roi_bound = params_.qcpx_live_coupon_adjust_roi_bound;
  if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
    roi_bound = params_.qcpx_live_coupon_adjust_roi_bound_open;
  }
  if (params_.enable_qcpx_upper_bound_cpa_bid_coef) {
    roi_bound = params_.value_qcpx_upper_bound_cpa_bid_coef;
    if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
      roi_bound = params_.value_qcpx_upper_bound_cpa_bid_coef_open;
    }
  }
  if (roi_bound <= 0.0 || cpa_bid <= 0.0) {
    return false;
  }
  min_coupon_amount_yuan = std::max(min_coupon_amount_yuan,
    static_cast<int>(cpa_bid / roi_bound * params_.qcpx_live_cpa_bid_amt_thre_lower_bound / 1000));  // NOLINT
  max_coupon_amount_yuan = std::min(max_coupon_amount_yuan, static_cast<int>(cpa_bid / roi_bound / 1000));  // NOLINT

  *min = min_coupon_amount_yuan;
  *max = max_coupon_amount_yuan;
  // 限制券金占出价最低比例时 最低比例出价券金 > 券金上限 按最高券金发券
  // 先只生效订单支付
  if (max_coupon_amount_yuan < min_coupon_amount_yuan &&
      min_coupon_amount_yuan >= params_.qcpx_live_max_coupon_amount_yuan &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
    *min = params_.qcpx_live_max_coupon_amount_yuan;
    *max = params_.qcpx_live_max_coupon_amount_yuan;
  }
  return true;
}

double QcpxLiveStrategy::GetAtv(AdCommon* p_ad) {
  auto atv = 0.0;
  atv = p_ad->Attr(ItemIdx::fd_AUTHOR_gmv_mode).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0) * 1e3;  // NOLINT
  if (atv == 0.0) {
    atv = p_ad->Attr(ItemIdx::fd_AUTHOR_item_price_mode).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0) * 1e3;  // NOLINT
  }
  // 兜底
  if (atv == 0.0) {
    atv = p_ad->get_auto_atv();
  }
  return atv;
}

double QcpxLiveStrategy::GetLiveAuthorGmvPriceRatio(AdCommon* p_ad) {
  double live_gmv_price_ratio = 1.0;
  double live_gmv_price_ratio_7d = p_ad->Attr(ItemIdx::fd_AUTHOR_live_gmv_price_ratio_7d).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  double live_gmv_price_ratio_1d = p_ad->Attr(ItemIdx::fd_AUTHOR_live_gmv_price_ratio_1d).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  int32_t live_order_num_1d = p_ad->Attr(ItemIdx::fd_AUTHOR_live_order_num_1d).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  double weight_1d = params_.value_qcpx_discount_coupon_gmv_price_ratio_1d_weight;
  if (live_gmv_price_ratio_1d > 0 && live_order_num_1d >= 10.) {
    live_gmv_price_ratio = live_gmv_price_ratio_1d * weight_1d + live_gmv_price_ratio_7d * (1. - weight_1d);  // NOLINT
  } else {
    live_gmv_price_ratio = live_gmv_price_ratio_7d;
  }
  // realtime data
  if (params_.order_num_author_3_h > 0 &&
      params_.origin_price_consider_shop_promotion_author_3_h > 0 &&
      params_.enable_qcpx_use_realtime_author_gmv_price_ratio &&
      params_.author_gmv_price_ratio_3h >= 0 &&
      params_.author_gmv_price_ratio_3h <= 1.0
      ) {
    live_gmv_price_ratio = params_.author_gmv_price_ratio_3h;
  }
  live_gmv_price_ratio = std::min(std::max(live_gmv_price_ratio, 0.), 1.0);  // NOLINT
  return live_gmv_price_ratio;
}

double QcpxLiveStrategy::GetLiveAudience(AdCommon* p_ad) {
  // 获取主模型预估值
  double live_audience = 1.0;
  bool is_esp_mobile_live = p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE && p_ad->get_live_creative_type() == kuaishou::ad::AdEnum::LIVE_STREAM_CREATIVE_TYPE;  // NOLINT
  // 软广
  if (params_.ad_queue_type == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
    if (params_.is_thanos_request) {
      // 只考虑单列
      // 直投、短引
      live_audience = p_ad->get_live_audience();
    }
  } else if (params_.ad_queue_type == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
    if (params_.is_thanos_request) {
      // 只考虑单列
      if (p_ad->Is(AdFlag::is_merchant_item_live) || is_esp_mobile_live) {
        // 直投
        live_audience = p_ad->get_live_audience();
      } else if (p_ad->Is(AdFlag::is_merchant_item_p2l)) {
        // 短引
        live_audience = p_ad->get_cvr();
      }
    }
  }
  return live_audience;
}

double QcpxLiveStrategy::CalcReduceCouponLiveAudienceRatio(AdCommon* p_ad, double q10) {
  // 激励直播不走 ctr_uplift
  if (params_.enable_qcpx_rewarded &&
    p_ad->Is(AdFlag::is_live_ad_inner) &&
    (session_data_->get_is_rewarded() || session_data_->get_is_inspire_live_request())) {
      return 1.0;
  }

  double ctr_ratio = 1.0;
  double origin_live_audience = GetLiveAudience(p_ad);
  if (origin_live_audience >= 1.0 || origin_live_audience <= 0.0) return 1.0;
  double epsilon = 1e-8;
  if (params_.enable_p2l_multi_predict) {
    origin_live_audience = std::min(std::max(origin_live_audience, epsilon), 1 - epsilon);
  }
  double origin_logit_divisor = 1.0 - origin_live_audience;
  if (origin_logit_divisor == 0) return 1.0;
  double origin_logit = std::log(origin_live_audience / origin_logit_divisor);
  // 异常情况 直接返回 1.0
  double ctr_elastic_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_c0);  // NOLINT
  double ctr_elastic_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_c1);  // NOLINT
  // onemodel
  if (params_.enable_p2l_multi_predict) {
    ctr_elastic_c0 = p_ad->get_predict_score(PredictType::PredictType_p2l_qcpx_c0);  // NOLINT
    ctr_elastic_c1 = p_ad->get_predict_score(PredictType::PredictType_p2l_qcpx_c1);  // NOLINT
  }
  double qcpx_logit = origin_logit + ctr_elastic_c0 + ctr_elastic_c1 * q10;
  if (params_.enable_live_qcpx_live_ctr_one_model_uplift && p_ad->Is(AdFlag::is_merchant_item_live)) {
    double ctr_live_elastic_c1 = p_ad->get_predict_score(PredictType::PredictType_live_audience_qcpx_c1);  // NOLINT
    double ctr_live_elastic_c0 = p_ad->get_predict_score(PredictType::PredictType_live_audience_qcpx_c0);  // NOLINT
    qcpx_logit = origin_logit + ctr_live_elastic_c1 * q10;
    if (params_.enable_live_qcpx_live_ctr_one_model_piecewise_uplift) {
      qcpx_logit = origin_logit + ctr_live_elastic_c0 + ctr_live_elastic_c1 * q10;
    }
  }
  if (params_.enable_qcpx_ctr_bound) {
    qcpx_logit = std::min(std::max(qcpx_logit, -50.0), 15.0);
  } else {
    qcpx_logit = std::min(std::max(qcpx_logit, -15.0), 15.0);  //  -15, 15
  }
  double qcpx_ctr = std::exp(qcpx_logit) / (1 + std::exp(qcpx_logit));
  if (params_.enable_qcpx_live_ctr_adjust && p_ad->Is(AdFlag::is_merchant_item_live)) {
    qcpx_ctr *= params_.value_qcpx_live_ctr_reduce_adjust_coef;
  }
  ctr_ratio = qcpx_ctr / origin_live_audience;
  // p2l ctr cali 非 onemodel
  if (params_.enable_qcpx_p2l_ctr_cali && !params_.enable_p2l_multi_predict && p_ad->Is(AdFlag::is_p2l_ad_inner)) {  // NOLINT
    ctr_ratio *= params_.value_qcpx_p2l_ctr_cali_coef;
  }
  if (params_.enable_qcpx_ctr_bound) {
    ctr_ratio = std::min(ctr_ratio, params_.value_qcpx_live_ctr_ratio_bound);
  }
  return ctr_ratio;
}
}  // namespace ad_rank
}  // namespace ks
