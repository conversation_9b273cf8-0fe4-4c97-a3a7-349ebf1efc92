#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <set>
#include <vector>
#include <map>
#include <unordered_map>
#include <unordered_set>

#include "base/common/basic_types.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_proto/maven/ad/coupon/ad_coupon_cache.pb.h"
#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_0.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_1.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_2.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_3.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_4.pb.h"

namespace ks {
namespace ad_rank {

struct AdCommon;
struct ContextData;

struct QcpxPhotoCouponInfoV3 {
  int64_t coupon_rate = 0;
  int64_t capped_amount = 0;
};

struct QcpxPhotoParams {
  // *********** request 粒度 参数 & 变量 ***********
  // 广告属性
  kuaishou::ad::AdEnum_AdQueueType ad_queue_type;  // normal=1,native=2
  kuaishou::ad::AdEnum::ItemType item_type;  // photo=0,live=1,p2l=2
  kuaishou::ad::AdActionType ocpx_type;  // EVENT_ORDER_PAIED=395,AD_MERCHANT_ROAS=192,AD_STOREWIDE_ROAS=944
  int64_t user_id = 0;
  bool is_qcpx_test_user = false;  // 内部测试字段 高危 修改请确认 @fandi
  double qcpx_test_user_boost_ratio = 1.0;  // 内部测试字段 高危 修改请确认 @fandi
  // spdm
  bool enable_inner_pec_holdout = false;
  bool enable_inner_qcpx_open_holdout = false;
  bool enable_qcpx_photo_threshold_price_strategy = false;
  bool enable_qcpx_photo_threshold_max_price = false;
  bool enable_qcpx_photo_threshold_mode_item_price = false;
  bool enable_shelf_qcpx_photo_cvr_adjust = false;
  bool enable_rewarded_qcpx_photo_cvr_adjust = false;
  double rewarded_qcpx_photo_cvr_adjust_alpha = 1.0;
  double  shelf_homepage_qcpx_photo_cvr_adjust_alpha = 1.0;
  double  shelf_mall_qcpx_photo_cvr_adjust_alpha = 1.0;
  double  shelf_guesslike_qcpx_photo_cvr_adjust_alpha = 1.0;
  double shelf_qcpx_photo_cvr_adjust_alpha = 1.0;
  bool enable_qcpx_photo_paid_elastic_piecewise_model = false;
  bool enable_qcpx_photo_roas_elastic_piecewise_model = false;
  bool enable_qcpx_photo_specific_amount_coupon_id = false;
  double value_qcpx_photo_gmv_greater_min_price_ratio_thres = 0.0;
  double value_qcpx_photo_mode_gmv_ratio_thres = 0.0;
  double value_qcpx_photo_min_price_higher_avg_gmv_ratio_thres = 0.0;

  int32_t value_qcpx_photo_rct_flow_percent = 0;
  int32_t value_qcpx_photo_model_flow_percent = 0;
  int32_t value_qcpx_photo_price_coupon_ratio = 5;
  int32_t value_qcpx_photo_max_coupon_amount_yuan = 20;
  int32_t value_qcpx_photo_min_coupon_amount_yuan = 2;
  bool enable_qcpx_photo_no_min_5_thres = false;
  int32_t value_qcpx_photo_history_paid_count_thres = 0;
  int64_t specific_amount_coupon_id = 0;
  int32_t value_qcpx_photo_rct_amount_control_flow_percent = 0;
  int32_t value_qcpx_photo_rct_amount_treatment_flow_percent = 0;
  int32_t value_qcpx_photo_rct_rate_control_flow_percent = 0;
  int32_t value_qcpx_photo_rct_rate_treatment_flow_percent = 0;
  bool enable_qcpx_holdout_no_rct = false;
  double value_qcpx_photo_skip_low_roi_threshold = 1.0;

  // 分价格带控补贴率上限
  bool enable_qcpx_photo_price_range_coupon_ratio = false;
  int32_t value_qcpx_photo_price_range_coupon_ratio_10_50 = 5;
  int32_t value_qcpx_photo_price_range_coupon_ratio_50_100 = 5;

  // RCT 分流机制
  bool enable_qcpx_photo_allocate_flow_v2 = false;

  // *********** ROI 系数相关 ***********
  // 商家白名单 调整系数
  std::shared_ptr<absl::flat_hash_set<int64_t>> author_roi_pacing_set;
  bool enable_qcpx_author_roi_pacing = false;
  double value_qcpx_author_roi_pacing = 1.0;
  double value_qcpx_photo_G = 1.0;
  double value_qcpx_photo_whitebox_G = 1.0;
  double value_qcpx_photo_G_rate = 1.0;
  double value_qcpx_photo_whitebox_G_rate = 1.0;
  double shelf_merchant_photo_qcpx_roi_ratio = 1.0;
  double follow_photo_qcpx_roi_ratio = 1.0;
  bool enable_qcpx_photo_G_rate_ratio_roas = false;
  double value_qcpx_photo_G_rate_ratio_roas = 1.0;
  bool enable_qcpx_photo_storewide_order_ROI_pacing_strategy = false;
  double value_qcpx_photo_ROI_pacing_photo_storewide_order = 1.0;
  double value_qcpx_photo_G_lower_bound = 1.0;
  // ROI 实时调控
  double main_ecpm_auto_24h = 0.0;
  double main_ecpm_24h = 0.0;
  double qcpx_ecpm_auto_24h = 0.0;
  double qcpx_ecpm_24h = 0.0;
  double q_cost_24h = 0.0;
  double value_qcpx_roi_goal = 2.0;
  double value_qcpx_photo_order_err_factor = 0.1;
  double value_qcpx_photo_storewide_roas_err_factor = 0.1;
  double value_qcpx_photo_merchant_roas_err_factor = 0.1;
  double value_qcpx_photo_auto_roi_upper_bound = 1.2;
  double value_qcpx_photo_auto_roi_lower_bound = 0.8;
  double value_qcpx_photo_neg_err_factor = 0.1;
  bool enable_qcpx_photo_merge_roi_logic = false;
  bool enable_inner_qcpx_photo_roi_adjust = false;
  bool enable_qcpx_photo_neg_err_factor = false;
  // ROI-PID
  bool enable_inner_qcpx_roi_pid_strategy = false;
  bool enable_qcpx_photo_insert_pid_tag = false;
  int64_t inner_qcpx_roi_pid_tag = 0;

  bool enable_shelf_qcpx_photo_fixed_discount = false;
  bool enable_shelf_photo_shield_discount = false;
  int64_t shelf_qcpx_photo_fixed_discount = 800;
  bool enable_qcpx_photo_rate_rct_log_pcvr = false;
  bool enable_shelf_qcpx_photo_price_range_full_dis = false;
  std::string shelf_qcpx_photo_price_range_full_dis_tag = "base";
  bool enable_qcpx_photo_roas_unify_coupon_module = false;
  bool enable_qcpx_photo_cvr_rate_roas_multi_head_cemm_model_update = false;
  bool enable_qcpx_photo_cvr_rate_generative_mono_model = false;
  bool enable_qcpx_photo_cvr_rate_elastic_model = false;
  bool enable_qcpx_photo_rate_coupon_with_capped = false;
  bool enable_qcpx_photo_elastic_taylor_bspline_model = false;
  bool enable_qcpx_photo_amount_set_upper_bound = false;
  double value_qcpx_photo_uplift_ratio_upper_bound = 20.0;
  double value_qcpx_photo_rate_uplift_ratio_upper_bound = 50.0;
  bool enable_qcpx_photo_rate_set_upper_bound = false;
  bool enable_qcpx_photo_no_threshold_order = false;
  bool enable_photo_qcpx_cpa_bid_max_amount = false;
  double photo_qcpx_cpa_bid_max_amount_ratio = 1.0;
  bool enable_qcpx_photo_bid_update_roas_rate = false;
  bool enable_qcpx_photo_bid_update_storewide_rate = false;
  int64_t value_qcpx_photo_thre_ratio_when_delivery = 3;
  bool enable_random_coloring_uplift = false;
  int64_t random_coloring_flow_percent = 0;
  bool enable_qcpx_random_color_v2 = false;  // qcpx 支持反事实染色
  bool enable_qcpx_photo_rate_pltv_merge_seller_bear_amt = false;
  bool enable_qcpx_photo_rate_realtime_item_price = false;
  bool enable_qcpx_expire_minutes = false;
  bool enable_qcpx_photo_bound_rate_coupon_model = false;
  int64_t value_qcpx_photo_bound_rate_coupon_model_right = 1000;
  int64_t value_qcpx_photo_bound_rate_coupon_model_left = 1000;
  bool enable_qcpx_photo_actual_storewide_bid_update = false;
  bool enable_qcpx_unify_ROI_pacing_strategy = false;
  double value_qcpx_unify_ROI_pacing_photo_storewide = 1.0;
  double value_qcpx_unify_ROI_pacing_photo_order = 1.0;
  double qcpx_photo_order_antou_realtime_bid_ratio = 1.0;
  double qcpx_photo_order_antou_realtime_bid_up_ratio = 1.0;
  int64_t qcpx_photo_order_antou_realtime_bid_up_thre = 5;
  bool enable_qcpx_unify_ROI_pacing_strategy_s24 = false;
  double value_qcpx_unify_ROI_pacing_photo_order_s24 = 1.0;
  bool enable_qcpx_photo_roas_optim_decision_module = false;
  bool enable_qcpx_photo_optimal_style_disable_feed_card_rct = false;
  bool enable_qcpx_photo_search_and_push = false;
  int64_t value_qcpx_photo_optimal_style_disable_feed_card_rct_pp = 0;
  // 膨胀券相关
  bool enable_qcpx_amount_expand_coupon = false;
  int64_t qcpx_amount_expand_coupon_expand_ratio = 2;

  // 监控
  std::unordered_map<uint64_t, int64_t> coupon_delivery_cnt;
  std::string tag_threshold_price = "";
  // *********** ad 粒度 参数 & 变量 ***********
  int inner_qcpx_cause = 0;
  double inner_qcpx_search_and_push_ratio = 1.0;
  // 优惠券设置
  kuaishou::ad::AdEnum::QponType qpon_type = kuaishou::ad::AdEnum_QponType_UNKNOWN_QPON_TYPE;
  int64_t product_price = 0;
  int64_t coupon_threshold = 0;
  int64_t coupon_amount = 0;
  double roi_bound_final = 1.0;
  double bid_ratio = 1.0;
  double pcvr_ratio = 1.0;
  double cpm_ratio = 1.0;
  double tmp_max_profit_ecpm_amt = 1.0;
  // 优惠券设置 v3
  int32_t coupon_type = 0;
  int64_t amount_coupon_id = 0;
  int64_t rate_coupon_id = 0;
  std::vector<int64_t> rate_coupon_list;
  std::unordered_map<int64_t, QcpxPhotoCouponInfoV3> rate_coupon_map;
  const ks::platform::AttrTable* coupon_table = nullptr;
  ks::platform::ItemAttr* coupon_type_attr = nullptr;
  ks::platform::ItemAttr* coupon_status_attr = nullptr;
  ks::platform::ItemAttr* coupon_rule_attr = nullptr;
  // 货架商品卡定向发店铺折扣券 @wanpengcheng
  bool enable_qcpx_photo_shelf_target_store_disc = false;
  bool qcpx_photo_shelf_target_match = false;
};

class QcpxPhotoStrategy {
 public:
  explicit QcpxPhotoStrategy(ContextData* session_data) : session_data_(session_data) {}
  virtual const char* Name();
  void InitParams();
  bool Admit(AdCommon* p_ad);
  void Process(AdCommon* p_ad);
  void Monitor();

 protected:
  // init
  void InitAdParams(AdCommon* p_ad);
  void InitCouponParams(AdCommon* p_ad);
  void InitCouponParamsV3(AdCommon* p_ad);
  // admit
  bool AdmitV3(AdCommon* p_ad);
  bool IsHoldoutV3(AdCommon* p_ad);
  // common strategy
  void RunArchStrategyV3(AdCommon* p_ad);
  // fill coupon
  void FillCouponInfoV3(AdCommon* p_ad);
  void AddExpandCouponInfo(AdCommon* p_ad);
  // 变量
  ContextData* session_data_;
  QcpxPhotoParams params_;

 private:
  // calc
  double GetItemPriceForRate(AdCommon* p_ad);
  double CouponGetRoiBound(AdCommon* p_ad);
  double RateCouponGetPcvrRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount);
  double UnifyCouponGetRoasBidRatio(AdCommon* p_ad, bool is_rate, int32_t q, int64_t coupon_rate, int64_t capped_amount, double roi_bound);  // NOLINT
  // decide method
  void DecideByMaxRatio(AdCommon* p_ad);  // NOLINT
  void DecideByRandom(AdCommon* p_ad);
  void DecideByMaxRatioV3(AdCommon* p_ad);  // NOLINT
  void DecideByRandomV3(AdCommon* p_ad);
  void DecideByDiscountForShelf(AdCommon* p_ad);
  void DecideByMaxWithPriceForShelf(AdCommon* p_ad);
  void HoldOutPostProcess(AdCommon* p_ad);
  void AllocateFlow(AdCommon* p_ad);
  void AllocateFlowV2(AdCommon* p_ad);
  // threshold price optimization
  double GetThresholdPrice(AdCommon* p_ad);
  int64_t GetRealtimeCpaBid(AdCommon* p_ad);
  void ProcessThreshold(AdCommon* p_ad);
  // ROI
  double GetROIAdjustFactor(AdCommon* p_ad);
  double GetAutoROIFactor(AdCommon* p_ad, double ori_roi_factor);
  double GetAutoROIPidFactor(AdCommon* p_ad, double ori_roi_factor);
};

}  // namespace ad_rank
}  // namespace ks
