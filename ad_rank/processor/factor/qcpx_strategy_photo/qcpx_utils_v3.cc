#include "teams/ad/ad_rank/processor/factor/qcpx_strategy_photo/qcpx_strategy.h"

#include <algorithm>
#include <cstdint>
#include <memory>
#include <numeric>
#include <string>
#include <unordered_set>
#include <vector>
#include "json/json.h"
#include "json/value.h"

#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"

using kuaishou::ad::AdEnum;
namespace ks {
namespace ad_rank {

void QcpxPhotoStrategy::InitCouponParamsV3(AdCommon* p_ad) {
  // *********** ad 粒度 参数 & 变量 ***********
  InitCouponParams(p_ad);
  // 优惠券设置
  params_.qpon_type = kuaishou::ad::AdEnum_QponType_UNKNOWN_QPON_TYPE;
  params_.coupon_type = 0;
  params_.amount_coupon_id = 0;
  params_.rate_coupon_id = 0;
  params_.rate_coupon_map.clear();
  params_.rate_coupon_list.clear();
  auto coupon_id_list = p_ad->Attr(ItemIdx::coupon_config_id_list).GetIntListValue(p_ad->AttrIndex()).value_or(absl::Span<const int64_t>());  // NOLINT
  bool is_exist_specific_amount_coupon_id = false;
  for (auto coupon_id : coupon_id_list) {
    auto index = params_.coupon_table->GetItemAttrIndex(coupon_id);
    if (!index.has_value()) continue;
    int32_t coupon_template_status = params_.coupon_status_attr->GetIntValue(index.value()).value_or(0);
    if (coupon_template_status != 1) continue;
    int32_t coupon_type = params_.coupon_type_attr->GetIntValue(index.value()).value_or(0);
    const auto& coupon_rule = params_.coupon_rule_attr->GetStringValue(index.value()).value_or("");  // NOLINT
    if (coupon_type == 1) {
      // 满减券
      params_.amount_coupon_id = coupon_id;
      if (params_.enable_qcpx_photo_specific_amount_coupon_id) {
        if (coupon_id == params_.specific_amount_coupon_id) {
          is_exist_specific_amount_coupon_id = true;
        }
        if (is_exist_specific_amount_coupon_id) {
          params_.amount_coupon_id = params_.specific_amount_coupon_id;
        }
      }
    } else if (coupon_type == 2) {
      // 折扣券
      ::Json::Reader reader;
      ::Json::Value value;
      if (!reader.parse(std::string(coupon_rule), value)) continue;
      if (!value.isObject()) continue;
      uint64_t coupon_rate = value["reduceAmount"].asUInt64();
      uint64_t capped_amount = value["cappedAmount"].asUInt64();
      QcpxPhotoCouponInfoV3 tmp_info = {coupon_rate, capped_amount};
      params_.rate_coupon_map.insert({coupon_id, tmp_info});
      params_.rate_coupon_list.push_back(coupon_id);
    }
  }
}

bool QcpxPhotoStrategy::AdmitV3(AdCommon* p_ad) {
  auto coupon_id_list = p_ad->Attr(ItemIdx::coupon_config_id_list).GetIntListValue(p_ad->AttrIndex()).value_or(absl::Span<const int64_t>());  // NOLINT
  if (coupon_id_list.empty()) {
    p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_COUPON_INFO);
  }
  if (coupon_id_list.empty()) return false;
  if (params_.coupon_table == nullptr) return false;
  bool flag = true;
  flag &= Admit(p_ad);
  // 包内状态 1 正常 2 删除
  int32_t coupon_status = p_ad->Attr(ItemIdx::fd_PRODUCT_item_coupon_status).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  flag &= coupon_status == 1;
  // 投放类型 1 白盒 0 黑盒
  int32_t qcpx_put_type = p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  RANK_DOT_STATS(session_data_, static_cast<int>(flag),
    absl::StrCat(this->Name(), ".v3_admit_dot"),
    absl::StrCat(qcpx_put_type),
    absl::StrCat(p_ad->get_item_type()),
    absl::StrCat(p_ad->get_ad_queue_type()),
    absl::StrCat(p_ad->get_ocpx_action_type()));
  return flag;
}

void QcpxPhotoStrategy::RunArchStrategyV3(AdCommon* p_ad) {
  InitCouponParamsV3(p_ad);
  if (params_.amount_coupon_id == 0 && params_.rate_coupon_list.empty()) return;
  if (params_.enable_qcpx_photo_allocate_flow_v2) {
    AllocateFlowV2(p_ad);
  } else {
    AllocateFlow(p_ad);
  }
  if (IsHoldoutV3(p_ad)) {
    return;
  }

  // 货架商品卡定向发店铺折扣券 @wanpengcheng
  if (params_.enable_qcpx_photo_shelf_target_store_disc
      && session_data_->get_pos_manager_base().IsShelfMerchantTraffic()   // 货架
      && p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD) {   // 明投 // NOLINT
    int64_t author_id = p_ad->get_author_id();
    std::unordered_map<int64_t, std::unordered_set<int64_t>> coupon2authors;
    std::unordered_map<int64_t, std::unordered_set<int64_t>> pkg2coupons_shelf_target;
    if (RankKconfUtil::qcpxPhotoShelfTargetStoreDiscConfig() != nullptr) {
      coupon2authors = RankKconfUtil::qcpxPhotoShelfTargetStoreDiscConfig()->data().coupon2authors;  // NOLINT
    }
    if (RankKconfUtil::qcpxPackageConfigRelationShelfTarget() != nullptr) {
      pkg2coupons_shelf_target = RankKconfUtil::qcpxPackageConfigRelationShelfTarget()->data().pkg2coupons_shelf_target;  // NOLINT
    }
    int64_t shop_coupon_package_id = p_ad->Attr(ItemIdx::shop_coupon_package_id).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    std::vector<int64_t> coupon_id_list;
    if (!pkg2coupons_shelf_target.empty()) {
      auto shop_coupon_iter = pkg2coupons_shelf_target.find(shop_coupon_package_id);
      if (shop_coupon_iter != pkg2coupons_shelf_target.end()) {
        coupon_id_list.assign(shop_coupon_iter->second.begin(), shop_coupon_iter->second.end());
      }
    }
    // 遍历 定向券 id * 定向店铺 id
    params_.qcpx_photo_shelf_target_match = false;
    for (int64_t coupon_id : coupon_id_list) {
      if (params_.qcpx_photo_shelf_target_match) break;
      std::vector<int64_t> author_id_list;
      if (!coupon2authors.empty()) {
        auto author_iter = coupon2authors.find(coupon_id);
        if (author_iter != coupon2authors.end()) {
          author_id_list.assign(author_iter->second.begin(), author_iter->second.end());
        }
      }
      for (int64_t target_author_id : author_id_list) {
        if (target_author_id == author_id) {
          // 店铺命中
          // 验证券有效性
          auto coupon_index = params_.coupon_table->GetItemAttrIndex(coupon_id);
          if (!coupon_index.has_value()) continue;
          int32_t coupon_template_status = params_.coupon_status_attr->GetIntValue(coupon_index.value()).value_or(0); // NOLINT
          if (coupon_template_status != 1) continue;
          // 确定发券
          // 1. 重新配置券包信息
          auto shop_coupon_iter = pkg2coupons_shelf_target.find(shop_coupon_package_id);
          p_ad->Attr(ItemIdx::coupon_config_id_list).SetIntListValue(p_ad->AttrIndex(), {shop_coupon_iter->second.begin(), shop_coupon_iter->second.end()}, false, false); // NOLINT
          InitCouponParamsV3(p_ad);
          // 2. 配置发券信息
          params_.coupon_type = params_.coupon_type_attr->GetIntValue(coupon_index.value()).value_or(0);  // 2 折扣券 // NOLINT
          params_.rate_coupon_id = coupon_id;
          params_.qcpx_photo_shelf_target_match = true;
          params_.inner_qcpx_cause = 53;
          p_ad->set_coupon_display_type(2);
          p_ad->Attr(ItemIdx::coupon_package_id).SetIntValue(p_ad->AttrIndex(), shop_coupon_package_id, false, false); // NOLINT
          // 3. 打点
          RANK_DOT_COUNT(session_data_, 1,
            "ad.ad_rank.qcpx_photo_shelf_target_match",
            absl::StrCat(p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0)),  // NOLINT
            absl::StrCat(p_ad->get_item_type()),
            absl::StrCat(p_ad->get_ad_queue_type()),
            absl::StrCat(p_ad->get_ocpx_action_type()));
          break;
        }
      }
    }
  }

  int32_t qcpx_put_type = p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  params_.qpon_type = kuaishou::ad::AdEnum_QponType_INNER_QCPX_PEC;
  if (qcpx_put_type == 1) {
    params_.qpon_type = kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN;
  }

  if (params_.inner_qcpx_cause == 10 || params_.inner_qcpx_cause == 11) {
    params_.coupon_type = 0;
    HoldOutPostProcess(p_ad);
  } else if (params_.inner_qcpx_cause == 20) {
    params_.coupon_type = 1;
    DecideByRandom(p_ad);
  } else if (params_.inner_qcpx_cause == 21) {
    params_.coupon_type = 2;
    DecideByRandomV3(p_ad);
  } else if (params_.inner_qcpx_cause == 30) {
    params_.coupon_type = 1;

    if (params_.enable_shelf_qcpx_photo_price_range_full_dis
    && session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
      // 货架商品卡走普发满减券
        DecideByMaxWithPriceForShelf(p_ad);
    } else if (params_.enable_shelf_qcpx_photo_fixed_discount
      && session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
        DecideByDiscountForShelf(p_ad);
    } else if(params_.enable_shelf_photo_shield_discount && session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {   // NOLINT
        // 泛货架不走折扣券
        DecideByMaxRatio(p_ad);
    } else if (params_.enable_qcpx_photo_roas_unify_coupon_module &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)) {
      DecideByMaxRatioV3(p_ad);
    } else {
      DecideByMaxRatio(p_ad);
    }
  }
  // 填充 rct 字段
  p_ad->set_inner_qcpx_cause(params_.inner_qcpx_cause);
}

void QcpxPhotoStrategy::AllocateFlow(AdCommon* p_ad) {
  int32_t sum_flow = 0;
  // 10
  int32_t amount_control_flow_percent = params_.value_qcpx_photo_rct_amount_control_flow_percent;
  sum_flow += amount_control_flow_percent;
  int32_t sum_flow_10 = sum_flow;
  // 20
  int32_t amount_treatment_flow_percent = params_.value_qcpx_photo_rct_amount_treatment_flow_percent;
  sum_flow += amount_treatment_flow_percent;
  int32_t sum_flow_20 = sum_flow;
  // 11
  int32_t rate_control_flow_percent = params_.value_qcpx_photo_rct_rate_control_flow_percent;
  sum_flow += rate_control_flow_percent;
  int32_t sum_flow_11 = sum_flow;
  // 21
  int32_t rate_treatment_flow_percent = params_.value_qcpx_photo_rct_rate_treatment_flow_percent;
  sum_flow += rate_treatment_flow_percent;
  int32_t sum_flow_21 = sum_flow;
  // 30
  int32_t model_flow_percent = params_.value_qcpx_photo_model_flow_percent;
  sum_flow += model_flow_percent;
  int32_t sum_flow_30 = sum_flow;
  int32_t max_flow = 100;
  // 流量和检查
  if (sum_flow > max_flow) return;
  int32_t mod_num = ((session_data_->get_llsid() % 100511) + (p_ad->get_creative_id() % 100511)) % max_flow;
  // 因果
  params_.inner_qcpx_cause = 0;
  if (mod_num < sum_flow_10) {
    params_.inner_qcpx_cause = 10;  // 满减 holdout
  } else if (mod_num < sum_flow_20) {
    params_.inner_qcpx_cause = 20;  // 满减 exploration
  } else if (mod_num < sum_flow_11) {
    params_.inner_qcpx_cause = 11;  // 折扣 holdout
  } else if (mod_num < sum_flow_21) {
    params_.inner_qcpx_cause = 21;  // 折扣 exploration
  } else if (mod_num < sum_flow_30) {
    params_.inner_qcpx_cause = 30;
  }
}

void QcpxPhotoStrategy::AllocateFlowV2(AdCommon* p_ad) {
  // 30
  int32_t model_flow_percent = params_.value_qcpx_photo_model_flow_percent;
  int32_t max_flow = 100;
  // 流量和检查
  if (model_flow_percent > max_flow) return;
  int32_t mod_num = (session_data_->get_llsid() % 1000003) % max_flow;
  // 因果
  params_.inner_qcpx_cause = 0;
  if (mod_num < model_flow_percent) {
    params_.inner_qcpx_cause = 30;  // 策略流量
  } else {
    int32_t sum_flow = 0;
    // 10
    int32_t amount_control_flow_percent = params_.value_qcpx_photo_rct_amount_control_flow_percent;
    sum_flow += amount_control_flow_percent;
    int32_t sum_flow_10 = sum_flow;
    // 20
    int32_t amount_treatment_flow_percent = params_.value_qcpx_photo_rct_amount_treatment_flow_percent;
    sum_flow += amount_treatment_flow_percent;
    int32_t sum_flow_20 = sum_flow;
    // 11
    int32_t rate_control_flow_percent = params_.value_qcpx_photo_rct_rate_control_flow_percent;
    sum_flow += rate_control_flow_percent;
    int32_t sum_flow_11 = sum_flow;
    // 21
    int32_t rate_treatment_flow_percent = params_.value_qcpx_photo_rct_rate_treatment_flow_percent;
    sum_flow += rate_treatment_flow_percent;
    int32_t sum_flow_21 = sum_flow;
    if (sum_flow > max_flow) return;
    int32_t rct_mod_num = ((session_data_->get_llsid() % 1000003) + (p_ad->get_creative_id() % 1000003)) % max_flow; // NOLINT
    if (rct_mod_num < sum_flow_10) {
      params_.inner_qcpx_cause = 10;  // 满减 holdout
    } else if (rct_mod_num < sum_flow_20) {
      params_.inner_qcpx_cause = 20;  // 满减 exploration
    } else if (rct_mod_num < sum_flow_11) {
      params_.inner_qcpx_cause = 11;  // 折扣 holdout
    } else if (rct_mod_num < sum_flow_21) {
      params_.inner_qcpx_cause = 21;  // 折扣 exploration
    }
  }
}

void QcpxPhotoStrategy::DecideByRandomV3(AdCommon* p_ad) {
  double ori_pcvr = p_ad->get_unify_cvr_info().value;
  if (ori_pcvr <= 0.0) {
    return;
  }
  if (params_.rate_coupon_list.empty()) return;
  uint32_t select_idx = ks::ad_base::AdRandom::GetInt(0, params_.rate_coupon_list.size() - 1);  // NOLINT
  if (select_idx < params_.rate_coupon_list.size()) {
    params_.rate_coupon_id = params_.rate_coupon_list[select_idx];
  }
  if (params_.enable_qcpx_photo_rate_rct_log_pcvr) {
    int64_t coupon_rate = params_.rate_coupon_map.at(params_.rate_coupon_id).coupon_rate;
    int64_t capped_amount = params_.rate_coupon_map.at(params_.rate_coupon_id).capped_amount;
    double rate_new_pcvr_ratio = RateCouponGetPcvrRatio(p_ad, coupon_rate, capped_amount);
    p_ad->Attr(ItemIdx::inner_qcpx_photo_cvr).SetDoubleValue(p_ad->AttrIndex(), rate_new_pcvr_ratio * ori_pcvr, false, false);  // NOLINT
  }
}
// 货架普发折扣券
void QcpxPhotoStrategy::DecideByDiscountForShelf(AdCommon* p_ad) {
  double ori_pcvr = p_ad->get_unify_cvr_info().value;
  if (ori_pcvr <= 0.0) {
    return;
  }
  if (params_.rate_coupon_list.empty()) return;
  const auto& tmp_map = params_.rate_coupon_map;
  for (auto iter = tmp_map.begin(); iter != tmp_map.end(); iter++) {
    const auto& coupon_info = iter->second;
    if(coupon_info.coupon_rate != params_.shelf_qcpx_photo_fixed_discount) continue; // NOLINT
    params_.rate_coupon_id = iter->first;
    params_.inner_qcpx_cause = 51;
    params_.coupon_type = 2;
  }
}

// 货架根据价格带进行普发固定券
void QcpxPhotoStrategy::DecideByMaxWithPriceForShelf(AdCommon* p_ad) {
  // 计算命中的券门槛和券面额
  int64_t price = p_ad->get_product_min_price() * 10;
  p_ad->Attr(ItemIdx::threshold_price).SetIntValue(p_ad->AttrIndex(), price, false, false);
  auto price_range_coupon_config = RankKconfUtil::shelfQcpxPhotoPriceRangeFullConfig();
  auto price_range_coupon_param = params_.shelf_qcpx_photo_price_range_full_dis_tag;
  double coupon_amount = 0.0;
  double coupon_thresh = 0.0;
  if (price_range_coupon_config) {
    auto iter = price_range_coupon_config->data().exp_conf().find(price_range_coupon_param);
    if (iter != price_range_coupon_config->data().exp_conf().end()) {
      const auto& price_range = iter->second.price_range();
      const auto& coupon_thresh_conf = iter->second.coupon_thresh();
      const auto& coupon_amount_conf = iter->second.coupon_amount();
      if (price_range.size() + 1 == coupon_thresh_conf.size() && price_range.size() + 1 == coupon_amount_conf.size()  // NOLINT
        && price_range.size() > 0) {
        bool flag = false;
        for (size_t i = 0; i < price_range.size(); ++i) {
          if (price < price_range[i]) {
            coupon_thresh = coupon_thresh_conf[i];
            coupon_amount = coupon_amount_conf[i];
            flag = true;
            break;
          }
        }
        if (!flag) {
          coupon_thresh = coupon_thresh_conf[coupon_thresh_conf.size()-1];
          coupon_amount = coupon_amount_conf[coupon_amount_conf.size()-1];
        }
      }
    }
  } else {
    return;
  }
  if (price < coupon_thresh) {
    coupon_amount = 0;
    return;
  }
  RANK_DOT_STATS(session_data_, price, "DecideByMaxWithPriceForShelf",
              absl::StrCat(coupon_thresh),
              absl::StrCat(coupon_amount));

  // 泰勒样条回归模型
  double elastic_taylor_bspline_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c0);  // NOLINT
  double elastic_taylor_bspline_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1);  // NOLINT 
  double elastic_taylor_bspline_c2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c2);  // NOLINT
  double elastic_taylor_bspline_c3 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c3);  // NOLINT
  double elastic_taylor_bspline_c4 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c4);  // NOLINT
  double elastic_taylor_bspline_c5 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c5);  // NOLINT
  double elastic_taylor_bspline_c1_prime = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1_prime);  // NOLINT

  double q_yuan = coupon_amount / 1e3;
  double q_10y = coupon_amount / 1e4;  // 单位 十元
  double ori_pcvr = p_ad->get_unify_cvr_info().value;
  if (ori_pcvr <= 0.0) {
    return;
  }
  double clip_ori_pcvr = std::clamp(ori_pcvr, 0.000000305902227, 0.9999996941);  //  -15, 15
  double logit_divisor = 1.0 - clip_ori_pcvr;
  if (logit_divisor == 0) return;
  double ori_logit = std::log(clip_ori_pcvr / logit_divisor);
  double qcpx_logit = 0.0;
  if (params_.enable_qcpx_photo_elastic_taylor_bspline_model) {
      double elastic_taylor_bspline_c2_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1;  // NOLINT
      double elastic_taylor_bspline_c3_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1 + 0.5 * elastic_taylor_bspline_c2 ; // NOLINT
      double elastic_taylor_bspline_c4_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1 + 0.5 * elastic_taylor_bspline_c2  + 0.5 * elastic_taylor_bspline_c3;  // NOLINT
      double elastic_taylor_bspline_c5_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1 + 0.5 * elastic_taylor_bspline_c2  + 0.5 * elastic_taylor_bspline_c3  + 0.5 * elastic_taylor_bspline_c4;  // NOLINT
    if (q_yuan < 5) {
      qcpx_logit = ori_logit +
                    elastic_taylor_bspline_c0 +
                    elastic_taylor_bspline_c1_prime * q_10y + elastic_taylor_bspline_c1 * std::pow(q_10y, 2) / 2;  // NOLINT
    } else if (q_yuan < 10) {
      qcpx_logit = ori_logit +
                  elastic_taylor_bspline_c0 +
                  elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c2_prime * (q_10y - 0.5) + elastic_taylor_bspline_c2 * std::pow((q_10y - 0.5), 2) / 2;  // NOLINT
    } else if (q_yuan < 15) {
      qcpx_logit = ori_logit +
                  elastic_taylor_bspline_c0 +
                  elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c2_prime * 0.5 + elastic_taylor_bspline_c2 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c3_prime * (q_10y - 1.0) + elastic_taylor_bspline_c3 * std::pow((q_10y - 1.0), 2) / 2;  // NOLINT
    } else if (q_yuan < 20) {
      qcpx_logit = ori_logit +
                  elastic_taylor_bspline_c0 +
                  elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c2_prime * 0.5 + elastic_taylor_bspline_c2 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c3_prime * 0.5 + elastic_taylor_bspline_c3 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c4_prime * (q_10y - 1.5) + elastic_taylor_bspline_c4 * std::pow((q_10y - 1.5), 2) / 2;  // NOLINT
    } else {
      qcpx_logit = ori_logit +
                  elastic_taylor_bspline_c0 +
                  elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c2_prime * 0.5 + elastic_taylor_bspline_c2 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c3_prime * 0.5 + elastic_taylor_bspline_c3 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c4_prime * 0.5 + elastic_taylor_bspline_c4 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c5_prime * (q_10y - 2.0) + elastic_taylor_bspline_c5 * std::pow((q_10y - 2.0), 2) / 2;  // NOLINT
    }
  }
  qcpx_logit = std::min(std::max(qcpx_logit, -15.0), 15.0);  //  -15, 15
  double qcpx_cvr = std::exp(qcpx_logit) / (1 + std::exp(qcpx_logit));

  params_.coupon_threshold = coupon_thresh;
  params_.coupon_amount = coupon_amount;
  params_.cpm_ratio = 1.0;  // 不改竞价
  params_.inner_qcpx_cause = 52;
  params_.coupon_type = 1;

  // elastic 落日志
  p_ad->Attr(ItemIdx::inner_qcpx_photo_cvr).SetDoubleValue(p_ad->AttrIndex(), qcpx_cvr, false, false);  // NOLINT
}

void QcpxPhotoStrategy::DecideByMaxRatioV3(AdCommon* p_ad) {
  params_.inner_qcpx_cause = 31;
  // 短带满减券最优决策计算模块
  int32_t threshold_price = GetThresholdPrice(p_ad) + 200;
  p_ad->Attr(ItemIdx::threshold_price).SetIntValue(p_ad->AttrIndex(), threshold_price - 200, false, false);

  // 短带折扣券最优决策计算模块
  const auto& tmp_map = params_.rate_coupon_map;
  double rate_tmp_pcvr_ratio = 1.0;
  double rate_tmp_bid_ratio = 1.0;
  double rate_tmp_cpm_ratio = 1.0;
  int64_t rate_tmp_coupon_id = 0;
  double roi_bound_disc = CouponGetRoiBound(p_ad);
  for (auto iter = tmp_map.begin(); iter != tmp_map.end(); iter++) {
    const auto& coupon_info = iter->second;
    if (params_.enable_qcpx_photo_bound_rate_coupon_model) {
      if (coupon_info.coupon_rate < params_.value_qcpx_photo_bound_rate_coupon_model_left
          || coupon_info.coupon_rate > params_.value_qcpx_photo_bound_rate_coupon_model_right) {
        continue;
      }
    }
    double rate_new_pcvr_ratio = RateCouponGetPcvrRatio(p_ad, coupon_info.coupon_rate, coupon_info.capped_amount);  // NOLINT
    if (params_.enable_qcpx_photo_rate_set_upper_bound) {
      rate_new_pcvr_ratio = (rate_new_pcvr_ratio >= params_.value_qcpx_photo_rate_uplift_ratio_upper_bound)
            ? params_.value_qcpx_photo_rate_uplift_ratio_upper_bound: rate_new_pcvr_ratio;
    }
    double rate_new_decay_bid_ratio = UnifyCouponGetRoasBidRatio(p_ad, true, 0.0, coupon_info.coupon_rate, coupon_info.capped_amount, roi_bound_disc);  // NOLINT
    double rate_new_cpm_ratio = rate_new_pcvr_ratio * rate_new_decay_bid_ratio;
    double rate_new_bid_ratio = UnifyCouponGetRoasBidRatio(p_ad, true, 0.0, coupon_info.coupon_rate, coupon_info.capped_amount, 1.0);  // NOLINT
    if (rate_new_cpm_ratio > rate_tmp_cpm_ratio) {
      rate_tmp_cpm_ratio = rate_new_cpm_ratio;
      rate_tmp_pcvr_ratio = rate_new_pcvr_ratio;
      rate_tmp_bid_ratio = rate_new_bid_ratio;
      rate_tmp_coupon_id = iter->first;
    }
  }
  if (params_.enable_qcpx_photo_roas_optim_decision_module) {
    DecideByMaxRatio(p_ad);
    RANK_DOT_STATS(session_data_, rate_tmp_cpm_ratio * 1e3,
      absl::StrCat(this->Name(), ".rate_max_profit_ecpm"));
    RANK_DOT_STATS(session_data_, params_.tmp_max_profit_ecpm_amt * 1e3,
      absl::StrCat(this->Name(), ".amt_max_profit_ecpm"));
    if (rate_tmp_cpm_ratio <= 1.0 && params_.tmp_max_profit_ecpm_amt <= 1.0) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::STRATEGY_MODEL);
      return;
    }
    if (params_.tmp_max_profit_ecpm_amt < rate_tmp_cpm_ratio && rate_tmp_cpm_ratio > 1.0) {
      params_.coupon_type = 2;
      params_.cpm_ratio = rate_tmp_pcvr_ratio;
      params_.pcvr_ratio = rate_tmp_pcvr_ratio;
      params_.bid_ratio = rate_tmp_bid_ratio;
      params_.rate_coupon_id = rate_tmp_coupon_id;
      params_.roi_bound_final = roi_bound_disc;
      params_.inner_qcpx_cause = 32;
      params_.coupon_threshold = 0;
      params_.coupon_amount = 0;
      // 填充 qcpx cvr
      double tmp_max_profit_cvr = p_ad->get_unify_cvr_info().value * params_.pcvr_ratio;  // NOLINT
      p_ad->Attr(ItemIdx::inner_qcpx_photo_cvr).SetDoubleValue(p_ad->AttrIndex(), tmp_max_profit_cvr, false, false);  // NOLINT
      return;
    } else {
      return;
    }
  }
  if (rate_tmp_cpm_ratio <= 1.0) {
    p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::STRATEGY_MODEL);
    return;
  }

  params_.coupon_type = 2;
  params_.cpm_ratio = rate_tmp_pcvr_ratio;
  params_.pcvr_ratio = rate_tmp_pcvr_ratio;
  params_.bid_ratio = rate_tmp_bid_ratio;
  params_.rate_coupon_id = rate_tmp_coupon_id;
  params_.roi_bound_final = roi_bound_disc;

  params_.inner_qcpx_cause = 32;
  // 填充 qcpx cvr
  double tmp_max_profit_cvr = p_ad->get_unify_cvr_info().value * params_.pcvr_ratio;  // NOLINT
  p_ad->Attr(ItemIdx::inner_qcpx_photo_cvr).SetDoubleValue(p_ad->AttrIndex(), tmp_max_profit_cvr, false, false);  // NOLINT
}

double QcpxPhotoStrategy::RateCouponGetPcvrRatio(AdCommon* p_ad, int64_t coupon_rate, int64_t capped_amount) {  // NOLINT
  double ori_pcvr = p_ad->get_unify_cvr_info().value;
  if (ori_pcvr <= 0) return 0.0;
  double clip_ori_pcvr = std::clamp(ori_pcvr, 0.000000305902227, 0.9999996941);  //  -15, 15
  double logit_divisor = 1.0 - clip_ori_pcvr;
  double delta_logit = 0.0;
  if (logit_divisor == 0) return 0.0;
  double ori_logit = std::log(clip_ori_pcvr / logit_divisor);

  // qcpx photo independent rate model
  double photo_cvr_rate_roas_multi_head_900_logit_update = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_900_logit);  // NOLINT
  double photo_cvr_rate_roas_multi_head_850_logit_update = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_850_logit);  // NOLINT
  double photo_cvr_rate_roas_multi_head_800_logit_update = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_800_logit);  // NOLINT
  double photo_cvr_rate_roas_multi_head_750_logit_update = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_750_logit);  // NOLINT
  double photo_cvr_rate_roas_multi_head_700_logit_update = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_700_logit);  // NOLINT
  double photo_cvr_rate_roas_multi_head_650_logit_update = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_650_logit);  // NOLINT
  double photo_cvr_rate_roas_multi_head_600_logit_update = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_600_logit);  // NOLINT
  double photo_cvr_rate_roas_multi_head_550_logit_update = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_550_logit);  // NOLINT
  double photo_cvr_rate_roas_multi_head_500_logit_update = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_500_logit);  // NOLINT

  if (params_.enable_qcpx_photo_cvr_rate_roas_multi_head_cemm_model_update) {
    if (coupon_rate <= 900) delta_logit += photo_cvr_rate_roas_multi_head_900_logit_update;  // NOLINT
    if (coupon_rate <= 850) delta_logit += photo_cvr_rate_roas_multi_head_850_logit_update;  // NOLINT
    if (coupon_rate <= 800) delta_logit += photo_cvr_rate_roas_multi_head_800_logit_update;  // NOLINT
    if (coupon_rate <= 750) delta_logit += photo_cvr_rate_roas_multi_head_750_logit_update;  // NOLINT
    if (coupon_rate <= 700) delta_logit += photo_cvr_rate_roas_multi_head_700_logit_update;  // NOLINT
    if (coupon_rate <= 650) delta_logit += photo_cvr_rate_roas_multi_head_650_logit_update;  // NOLINT
    if (coupon_rate <= 600) delta_logit += photo_cvr_rate_roas_multi_head_600_logit_update;  // NOLINT
    if (coupon_rate <= 550) delta_logit += photo_cvr_rate_roas_multi_head_550_logit_update;  // NOLINT
    if (coupon_rate <= 500) delta_logit += photo_cvr_rate_roas_multi_head_500_logit_update;  // NOLINT
  }
  if (params_.enable_qcpx_photo_cvr_rate_generative_mono_model) {
    if (coupon_rate == 900) delta_logit = photo_cvr_rate_roas_multi_head_900_logit_update;  // NOLINT
    if (coupon_rate == 850) delta_logit = photo_cvr_rate_roas_multi_head_850_logit_update;  // NOLINT
    if (coupon_rate == 800) delta_logit = photo_cvr_rate_roas_multi_head_800_logit_update;  // NOLINT
    if (coupon_rate == 750) delta_logit = photo_cvr_rate_roas_multi_head_750_logit_update;  // NOLINT
    if (coupon_rate == 700) delta_logit = photo_cvr_rate_roas_multi_head_700_logit_update;  // NOLINT
    if (coupon_rate == 650) delta_logit = photo_cvr_rate_roas_multi_head_650_logit_update;  // NOLINT
    if (coupon_rate == 600) delta_logit = photo_cvr_rate_roas_multi_head_600_logit_update;  // NOLINT
    if (coupon_rate == 550) delta_logit = photo_cvr_rate_roas_multi_head_550_logit_update;  // NOLINT
    if (coupon_rate == 500) delta_logit = photo_cvr_rate_roas_multi_head_500_logit_update;  // NOLINT
  }
  if (params_.enable_qcpx_photo_cvr_rate_elastic_model) {
    double d0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_d0);  // NOLINT
    double d1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_rate_d1);  // NOLINT
    delta_logit = d0 + (1.0 - coupon_rate / 1000.0) * d1;
  }

  p_ad->Attr(ItemIdx::photo_cvr_rate_multi_head_900_logit).SetDoubleValue(p_ad->AttrIndex(), photo_cvr_rate_roas_multi_head_900_logit_update, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::photo_cvr_rate_multi_head_850_logit).SetDoubleValue(p_ad->AttrIndex(), photo_cvr_rate_roas_multi_head_850_logit_update, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::photo_cvr_rate_multi_head_800_logit).SetDoubleValue(p_ad->AttrIndex(), photo_cvr_rate_roas_multi_head_800_logit_update, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::photo_cvr_rate_multi_head_750_logit).SetDoubleValue(p_ad->AttrIndex(), photo_cvr_rate_roas_multi_head_750_logit_update, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::photo_cvr_rate_multi_head_700_logit).SetDoubleValue(p_ad->AttrIndex(), photo_cvr_rate_roas_multi_head_700_logit_update, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::photo_cvr_rate_multi_head_650_logit).SetDoubleValue(p_ad->AttrIndex(), photo_cvr_rate_roas_multi_head_650_logit_update, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::photo_cvr_rate_multi_head_600_logit).SetDoubleValue(p_ad->AttrIndex(), photo_cvr_rate_roas_multi_head_600_logit_update, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::photo_cvr_rate_multi_head_550_logit).SetDoubleValue(p_ad->AttrIndex(), photo_cvr_rate_roas_multi_head_550_logit_update, false, false);  // NOLINT
  p_ad->Attr(ItemIdx::photo_cvr_rate_multi_head_500_logit).SetDoubleValue(p_ad->AttrIndex(), photo_cvr_rate_roas_multi_head_500_logit_update, false, false);  // NOLINT

  double qcpx_logit = ori_logit + delta_logit;
  qcpx_logit = std::min(std::max(qcpx_logit, -15.0), 15.0);
  double qcpx_pcvr = std::exp(qcpx_logit) / (1 + std::exp(qcpx_logit));

  RANK_DOT_STATS(session_data_, qcpx_pcvr / ori_pcvr * 1e6,
    absl::StrCat(this->Name(), ".v3_rate_coupon_uplift_ratio_qcpx_photo_rate"),
    absl::StrCat(p_ad->get_item_type()),
    absl::StrCat(p_ad->get_ad_queue_type()),
    absl::StrCat(p_ad->get_ocpx_action_type()));
  return qcpx_pcvr / ori_pcvr;
}

double QcpxPhotoStrategy::GetItemPriceForRate(AdCommon* p_ad) {
  // 单位 元
  // 预估 GMV
  double item_price = p_ad->get_unify_ltv_info().value;
  // 预估 GMV + 离线商家券成本
  if (params_.enable_qcpx_photo_rate_pltv_merge_seller_bear_amt) {
    double seller_bear_amt = p_ad->Attr(
      ItemIdx::fd_PRODUCT_seller_bear_amt).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
    if (seller_bear_amt > 0.0) {
      item_price = p_ad->get_unify_ltv_info().value + seller_bear_amt;
    }
  }
  // 预估 GMV + 实时商家券成本
  if (params_.enable_qcpx_photo_rate_realtime_item_price) {
    double realtime_seller_bear_amt = 0.0;
    double order_num_product_3_h = p_ad->Attr(
      ItemIdx::fd_PRODUCT_order_num_product_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
    double shop_promotion_product_3_h = p_ad->Attr(
      ItemIdx::fd_PRODUCT_shop_promotion_product_3_h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
    if (order_num_product_3_h > 0.0 && shop_promotion_product_3_h > 0.0) {
      realtime_seller_bear_amt = shop_promotion_product_3_h / order_num_product_3_h;
    }
    if (realtime_seller_bear_amt > 0.0) {
      item_price = p_ad->get_unify_ltv_info().value + realtime_seller_bear_amt;
    }
  }
  return item_price;
}

double QcpxPhotoStrategy::UnifyCouponGetRoasBidRatio(AdCommon* p_ad, bool is_rate, int32_t q, int64_t coupon_rate, int64_t capped_amount, double roi_bound) {  // NOLINT
  double roi_ratio = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
  if (params_.enable_qcpx_photo_bid_update_roas_rate &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) {
    roi_ratio = p_ad->get_roi_ratio() > 0.0 ? p_ad->get_roi_ratio() : p_ad->get_auto_roas();
  }
  if (params_.enable_qcpx_photo_bid_update_storewide_rate &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) {
    roi_ratio = p_ad->get_roi_ratio() > 0.0 ? p_ad->get_roi_ratio() : p_ad->get_auto_roas();
  }
  double ori_pltv = p_ad->get_unify_ltv_info().value;
  if (roi_ratio <= 0.0 || ori_pltv <= 0.0) return 0.0;
  if (is_rate) {
    double discount_rate = 1.0 - coupon_rate / 1000.0;
    double discount_amt = GetItemPriceForRate(p_ad) * discount_rate;
    if (params_.enable_qcpx_photo_rate_coupon_with_capped) {
      discount_amt = std::min(discount_amt, capped_amount / 1000.0);
    }
    p_ad->Attr(ItemIdx::inner_qcpx_photo_rate_coupon_cost).SetDoubleValue(p_ad->AttrIndex(), discount_amt, false, false);  // NOLINT    
    return 1.0 - roi_bound * discount_amt * roi_ratio / ori_pltv;
  } else {
    return 1.0 - roi_bound * q * roi_ratio / ori_pltv;
  }
}

double QcpxPhotoStrategy::CouponGetRoiBound(AdCommon* p_ad) {
  double roi_bound = params_.value_qcpx_photo_G_rate;
  if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
    roi_bound = params_.value_qcpx_photo_whitebox_G_rate;
  }
  if (params_.enable_qcpx_photo_G_rate_ratio_roas &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) {
    roi_bound *= params_.value_qcpx_photo_G_rate_ratio_roas;
  }
  roi_bound = std::max(roi_bound, params_.value_qcpx_photo_G_lower_bound);
  return roi_bound;
}

void QcpxPhotoStrategy::FillCouponInfoV3(AdCommon* p_ad) {
  bool flag = true;
  flag &= params_.coupon_type > 0;
  if (!flag) return;
  int32_t qcpx_put_type = p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  int64_t dot_value = 0;
  if (params_.coupon_type == 1) {
    flag &= params_.amount_coupon_id > 0;
    flag &= params_.coupon_amount > 0;
    // 下发改门槛
    ProcessThreshold(p_ad);

    flag &= params_.coupon_threshold > params_.coupon_amount;
    if (!flag) return;
    p_ad->set_coupon_template_id(params_.amount_coupon_id);
    p_ad->set_coupon_type(params_.coupon_type);
    p_ad->set_threshold(params_.coupon_threshold);
    p_ad->set_coupon_discount_amount(params_.coupon_amount);
    p_ad->set_threshold_type(1);
    p_ad->set_threshold_upper(8000000);
    p_ad->set_discount_amount_upper(150000);
    p_ad->set_reduce_amount(0);
    p_ad->set_capped_amount(0);
    if (params_.enable_qcpx_expire_minutes) {
      p_ad->set_expire_minutes(30);
      // 若命中短有效期 ID kconf 修改为 15
      if (RankKconfUtil::qcpxTmpIdList() != nullptr) {
        if (RankKconfUtil::qcpxTmpIdList()->count(params_.amount_coupon_id) > 0) {
          p_ad->set_expire_minutes(15);
        }
      }
    }
    if (params_.enable_qcpx_amount_expand_coupon) {
      AddExpandCouponInfo(p_ad);
    }
    dot_value = p_ad->get_coupon_discount_amount();
  } else if (params_.coupon_type == 2) {
    flag &= params_.rate_coupon_id > 0;
    flag &= params_.rate_coupon_map.count(params_.rate_coupon_id) > 0;
    if (!flag) return;
    p_ad->set_coupon_template_id(params_.rate_coupon_id);
    p_ad->set_coupon_type(params_.coupon_type);
    p_ad->set_reduce_amount(params_.rate_coupon_map.at(params_.rate_coupon_id).coupon_rate);
    p_ad->set_capped_amount(params_.rate_coupon_map.at(params_.rate_coupon_id).capped_amount);
    p_ad->set_threshold(0);
    p_ad->set_coupon_discount_amount(0);
    p_ad->set_threshold_type(1);
    p_ad->set_threshold_upper(8000000);
    p_ad->set_discount_amount_upper(150000);
    if (params_.enable_qcpx_expire_minutes) {
      p_ad->set_expire_minutes(30);
      // 若命中短有效期 ID kconf 修改为 15
      if (RankKconfUtil::qcpxTmpIdList() != nullptr) {
        if (RankKconfUtil::qcpxTmpIdList()->count(params_.amount_coupon_id) > 0) {
          p_ad->set_expire_minutes(15);
        }
      }
    }
    dot_value = p_ad->get_reduce_amount();
  }

  // 命中染色逻辑策略，清空券信息
  bool is_need_rerank = true;
  if (params_.enable_random_coloring_uplift &&
      params_.random_coloring_flow_percent > 0 &&
      params_.random_coloring_flow_percent <= 50 &&
      params_.inner_qcpx_cause == 32) {
    double random_ratio = ks::ad_base::AdRandom::GetDouble() * 100;
    if (random_ratio < params_.random_coloring_flow_percent) {
      // 命中染色逻辑，不发券
      p_ad->Attr(ItemIdx::qcpx_cf_discount_amount).SetIntValue(p_ad->AttrIndex(), p_ad->get_coupon_discount_amount(), false, false);  // NOLINT
      p_ad->Attr(ItemIdx::qcpx_cf_reduce_amount).SetIntValue(p_ad->AttrIndex(), p_ad->get_reduce_amount(), false, false);  // NOLINT
      p_ad->set_coupon_template_id(0);
      p_ad->set_coupon_type(0);
      p_ad->set_reduce_amount(0);
      p_ad->set_capped_amount(0);
      p_ad->set_threshold(0);
      p_ad->set_coupon_discount_amount(0);
      p_ad->set_threshold_type(0);
      p_ad->set_threshold_upper(0);
      p_ad->set_discount_amount_upper(0);
      p_ad->set_coupon_display_type(0);
      p_ad->set_pre_boost_discount_amount(0);
      // set cause
      p_ad->set_inner_qcpx_cause(41);
    } else if (random_ratio < params_.random_coloring_flow_percent * 2) {
      // 命中染色逻辑，正常发券
      // set cause
      p_ad->set_inner_qcpx_cause(42);
    } else if (params_.enable_qcpx_random_color_v2 && random_ratio < params_.random_coloring_flow_percent * 3) {  // NOLINT
      // 命中染色逻辑，不发券
      p_ad->set_coupon_template_id(0);
      p_ad->set_coupon_type(0);
      p_ad->set_reduce_amount(0);
      p_ad->set_capped_amount(0);
      p_ad->set_threshold(0);
      p_ad->set_coupon_discount_amount(0);
      p_ad->set_threshold_type(0);
      p_ad->set_threshold_upper(0);
      p_ad->set_discount_amount_upper(0);
      p_ad->set_coupon_display_type(0);
      p_ad->set_pre_boost_discount_amount(0);
      // set cause
      p_ad->set_inner_qcpx_cause(43);
      is_need_rerank = false;
    }
  }
  // 券样式优选一期 RCT
  if (params_.enable_qcpx_photo_optimal_style_disable_feed_card_rct &&
      params_.inner_qcpx_cause == 32 &&
      params_.value_qcpx_photo_optimal_style_disable_feed_card_rct_pp > 0) {
    double random_ratio = ks::ad_base::AdRandom::GetDouble() * 100;
    if (random_ratio < params_.value_qcpx_photo_optimal_style_disable_feed_card_rct_pp) {
      // 正常发券 修改因果标签
      // todo: 样本过滤需要适配
      p_ad->set_inner_qcpx_cause(322);
    } else if (random_ratio < params_.value_qcpx_photo_optimal_style_disable_feed_card_rct_pp * 2) {
      // 无样式发券 修改因果标签
      p_ad->set_inner_qcpx_cause(321);
      p_ad->set_optimal_style_disable_feed_card(1);
    }
  }

  RANK_DOT_STATS(session_data_, dot_value,
    absl::StrCat(this->Name(), ".v3_delivery_dot"),
    absl::StrCat(params_.coupon_type),
    absl::StrCat(qcpx_put_type),
    absl::StrCat(p_ad->get_item_type()),
    absl::StrCat(p_ad->get_ad_queue_type()),
    absl::StrCat(p_ad->get_ocpx_action_type()));
  if (is_need_rerank) {
    p_ad->SetQponInfo(params_.qpon_type, 1.0, params_.pcvr_ratio, 1.0);  // NOLINT
    p_ad->set_coupon_scope(2);
    if (params_.enable_qcpx_photo_shelf_target_store_disc
        && session_data_->get_pos_manager_base().IsShelfMerchantTraffic()
        && p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD
        && params_.qcpx_photo_shelf_target_match
        && params_.inner_qcpx_cause == 53) {
      // 店铺命中, 定向发券
      p_ad->set_coupon_scope(1);
    }
    p_ad->Attr(ItemIdx::inner_qcpx_photo_cvr_uplift_ratio).SetDoubleValue(p_ad->AttrIndex(), params_.pcvr_ratio, false, false);  // NOLINT
  }
  p_ad->Attr(ItemIdx::inner_qcpx_roi_adjust_ratio).SetDoubleValue(p_ad->AttrIndex(), params_.roi_bound_final, false, false);  // NOLINT
}

void QcpxPhotoStrategy::AddExpandCouponInfo(AdCommon* p_ad) {
  if (p_ad->get_ad_queue_type() != kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
    return;
  }
  // 膨胀券的相关逻辑
  if (params_.qcpx_amount_expand_coupon_expand_ratio >= 2) {
    int64_t pre_boost_discount_amount = static_cast<int>(std::ceil(params_.coupon_amount / 1000.0 / params_.qcpx_amount_expand_coupon_expand_ratio) * 1000);  // NOLINT
    if (pre_boost_discount_amount >= 1000 && pre_boost_discount_amount < params_.coupon_amount) {  // NOLINT
      // 设置展示类型 1 代表膨胀券
      p_ad->set_coupon_display_type(1);
      // 设置膨胀前满减金额
      p_ad->set_pre_boost_discount_amount(pre_boost_discount_amount);
    }
  }
}

bool QcpxPhotoStrategy::IsHoldoutV3(AdCommon* p_ad) {
  if (params_.enable_qcpx_holdout_no_rct && params_.inner_qcpx_cause < 30) {
    return true;
  }
  int32_t qcpx_put_type = p_ad->Attr(ItemIdx::fd_UNIT_qcpx_put_type).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
  if (params_.enable_inner_pec_holdout && qcpx_put_type == 0) {
    return true;
  }
  if (params_.enable_inner_qcpx_open_holdout && qcpx_put_type == 1) {
    return true;
  }
  return false;
}

}  // namespace ad_rank
}  // namespace ks
