#include "teams/ad/ad_rank/processor/factor/qcpx_strategy_photo/qcpx_strategy.h"

#include <algorithm>
#include <memory>
#include <numeric>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <random>

#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_rank/data/p2p_data/inner_qcpx_coupon_online_p2p/inner_qcpx_coupon_online_p2p.h"

using kuaishou::ad::AdEnum;
namespace ks {
namespace ad_rank {

double QcpxPhotoStrategy::GetThresholdPrice(AdCommon* p_ad) {
  // 优先级 1.mode_item_price 2.unify_ltv 3.max_price 4.mode_gmv

  // item_price 众数作为
  int64_t mode_item_price = p_ad->Attr(
    ItemIdx::fd_PRODUCT_mode_item_price).GetIntValue(p_ad->AttrIndex()).value_or(0);
  if (params_.enable_qcpx_photo_threshold_mode_item_price && mode_item_price > 0) {
    return mode_item_price;
  }

  // max_price 作为门槛价
  if (params_.enable_qcpx_photo_threshold_max_price &&
      p_ad->get_product_max_price() > 0) {
    return p_ad->get_product_max_price() * 10;
  }

  // 门槛价优化总开关 (mode_gmv)
  if (params_.enable_qcpx_photo_threshold_price_strategy) {
    double mode_gmv = p_ad->Attr(
      ItemIdx::fd_PRODUCT_mode_gmv).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
    int order_paid_cnt = p_ad->Attr(
      ItemIdx::fd_PRODUCT_order_paid_cnt).GetIntValue(p_ad->AttrIndex()).value_or(0);
    int gmv_greater_min_price_cnt = p_ad->Attr(
      ItemIdx::fd_PRODUCT_gmv_greater_min_price_cnt).GetIntValue(p_ad->AttrIndex()).value_or(0);
    int mode_gmv_cnt = p_ad->Attr(
      ItemIdx::fd_PRODUCT_mode_gmv_cnt).GetIntValue(p_ad->AttrIndex()).value_or(0);
    double avg_gmv = p_ad->Attr(
      ItemIdx::fd_PRODUCT_avg_gmv).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
    double median_gmv = p_ad->Attr(
      ItemIdx::fd_PRODUCT_median_gmv).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);

    // 提升门槛价准入
    bool is_raise_threshold = true;
    is_raise_threshold &= mode_gmv > static_cast<double>(params_.product_price) / 1000;
    if (params_.value_qcpx_photo_history_paid_count_thres > 0) {
      is_raise_threshold &= order_paid_cnt > params_.value_qcpx_photo_history_paid_count_thres;
    } else {
      is_raise_threshold &= false;
    }
    if (params_.value_qcpx_photo_gmv_greater_min_price_ratio_thres > 0.0 && order_paid_cnt > 0) {
      is_raise_threshold &= static_cast<double>(gmv_greater_min_price_cnt) / order_paid_cnt >
        params_.value_qcpx_photo_gmv_greater_min_price_ratio_thres;
    } else {
      is_raise_threshold &= false;
    }
    if (params_.value_qcpx_photo_mode_gmv_ratio_thres > 0.0 && order_paid_cnt > 0) {
      is_raise_threshold &= static_cast<double>(mode_gmv_cnt) / order_paid_cnt >
        params_.value_qcpx_photo_mode_gmv_ratio_thres;
    }

    // 降低门槛价准入
    bool is_lower_threshold = true;
    if (params_.value_qcpx_photo_min_price_higher_avg_gmv_ratio_thres > 0.0 && avg_gmv > 0) {
      is_lower_threshold &= (static_cast<double>(params_.product_price) / 1000 - avg_gmv) / avg_gmv >
        params_.value_qcpx_photo_min_price_higher_avg_gmv_ratio_thres;
    } else {
      is_lower_threshold &= false;
    }

    RANK_DOT_COUNT(session_data_, 1, "ad.ad_rank.threshold_price_strategy");
    if (is_raise_threshold) {
      RANK_DOT_COUNT(session_data_, 1, "ad.ad_rank.raise_threshold_price");
      params_.tag_threshold_price = "raise";
      p_ad->Attr(ItemIdx::threshold_price_strategy_tag).SetIntValue(p_ad->AttrIndex(), 1, false, false);  // NOLINT
      return static_cast<int>(mode_gmv * 1000);
    } else if (is_lower_threshold) {
      RANK_DOT_COUNT(session_data_, 1, "ad.ad_rank.lower_threshold_price");
      params_.tag_threshold_price = "lower";
      p_ad->Attr(ItemIdx::threshold_price_strategy_tag).SetIntValue(p_ad->AttrIndex(), 2, false, false);  // NOLINT
      return static_cast<int>(median_gmv * 1000);
    } else {
      RANK_DOT_COUNT(session_data_, 1, "ad.ad_rank.no_change_threshold_price");
      params_.tag_threshold_price = "no_change";
      p_ad->Attr(ItemIdx::threshold_price_strategy_tag).SetIntValue(p_ad->AttrIndex(), 3, false, false);  // NOLINT
      return params_.product_price;
    }

  } else {
    return params_.product_price;
  }
}

void QcpxPhotoStrategy::HoldOutPostProcess(AdCommon* p_ad) {
  // Holdout 组的一些后处理操作，例如落表等
  int32_t threshold_price = GetThresholdPrice(p_ad) + 200;
  p_ad->Attr(ItemIdx::threshold_price).SetIntValue(p_ad->AttrIndex(), threshold_price - 200, false, false);
}

void QcpxPhotoStrategy::DecideByRandom(AdCommon* p_ad) {
  // 可发券筛选
  int32_t threshold_price = GetThresholdPrice(p_ad) + 200;
  p_ad->Attr(ItemIdx::threshold_price).SetIntValue(p_ad->AttrIndex(), threshold_price - 200, false, false);
  int32_t price_coupon_ratio = params_.value_qcpx_photo_price_coupon_ratio;
  // 分价格带控补贴率上限
  if (params_.enable_qcpx_photo_price_range_coupon_ratio) {
    int32_t ori_threshold_price = threshold_price - 200;
    if (ori_threshold_price >= 10 * 1e3 && ori_threshold_price < 50 * 1e3) {
      price_coupon_ratio = params_.value_qcpx_photo_price_range_coupon_ratio_10_50;
    } else if (ori_threshold_price >= 50 * 1e3 && ori_threshold_price < 100 * 1e3) {
      price_coupon_ratio = params_.value_qcpx_photo_price_range_coupon_ratio_50_100;
    }
  }
  if (price_coupon_ratio < 1) return;  // 满减系数 >= 1
  int32_t min_coupon_amount_yuan = params_.value_qcpx_photo_min_coupon_amount_yuan;
  int32_t max_coupon_amount_yuan = std::min(params_.value_qcpx_photo_max_coupon_amount_yuan,
                                static_cast<int>(threshold_price / 1e3 / price_coupon_ratio));  // NOLINT
  if (max_coupon_amount_yuan < min_coupon_amount_yuan) return;
  int32_t random_coupon_amount = ks::ad_base::AdRandom::GetInt(min_coupon_amount_yuan, max_coupon_amount_yuan) * 1e3;  // NOLINT
  int32_t random_coupon_threshold = random_coupon_amount * price_coupon_ratio - 200;  // NOLINT

  // 泰勒样条回归模型
  double elastic_taylor_bspline_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c0);  // NOLINT
  double elastic_taylor_bspline_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1);  // NOLINT 
  double elastic_taylor_bspline_c2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c2);  // NOLINT
  double elastic_taylor_bspline_c3 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c3);  // NOLINT
  double elastic_taylor_bspline_c4 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c4);  // NOLINT
  double elastic_taylor_bspline_c5 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c5);  // NOLINT
  double elastic_taylor_bspline_c1_prime = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1_prime);  // NOLINT
  double q_yuan = random_coupon_amount / 1e3;
  double q_10y = random_coupon_amount / 1e4;  // 单位 十元
  double ori_pcvr = p_ad->get_unify_cvr_info().value;
  if (ori_pcvr <= 0.0) {
    return;
  }
  double clip_ori_pcvr = std::clamp(ori_pcvr, 0.000000305902227, 0.9999996941);  //  -15, 15
  double logit_divisor = 1.0 - clip_ori_pcvr;
  if (logit_divisor == 0) return;
  double ori_logit = std::log(clip_ori_pcvr / logit_divisor);
  double qcpx_logit = 0.0;
  if (params_.enable_qcpx_photo_elastic_taylor_bspline_model) {
      double elastic_taylor_bspline_c2_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1;  // NOLINT
      double elastic_taylor_bspline_c3_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1 + 0.5 * elastic_taylor_bspline_c2 ; // NOLINT
      double elastic_taylor_bspline_c4_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1 + 0.5 * elastic_taylor_bspline_c2  + 0.5 * elastic_taylor_bspline_c3;  // NOLINT
      double elastic_taylor_bspline_c5_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1 + 0.5 * elastic_taylor_bspline_c2  + 0.5 * elastic_taylor_bspline_c3  + 0.5 * elastic_taylor_bspline_c4;  // NOLINT
    if (q_yuan < 5) {
      qcpx_logit = ori_logit +
                    elastic_taylor_bspline_c0 +
                    elastic_taylor_bspline_c1_prime * q_10y + elastic_taylor_bspline_c1 * std::pow(q_10y, 2) / 2;  // NOLINT
    } else if (q_yuan < 10) {
      qcpx_logit = ori_logit +
                  elastic_taylor_bspline_c0 +
                  elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c2_prime * (q_10y - 0.5) + elastic_taylor_bspline_c2 * std::pow((q_10y - 0.5), 2) / 2;  // NOLINT
    } else if (q_yuan < 15) {
      qcpx_logit = ori_logit +
                  elastic_taylor_bspline_c0 +
                  elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c2_prime * 0.5 + elastic_taylor_bspline_c2 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c3_prime * (q_10y - 1.0) + elastic_taylor_bspline_c3 * std::pow((q_10y - 1.0), 2) / 2;  // NOLINT
    } else if (q_yuan < 20) {
      qcpx_logit = ori_logit +
                  elastic_taylor_bspline_c0 +
                  elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c2_prime * 0.5 + elastic_taylor_bspline_c2 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c3_prime * 0.5 + elastic_taylor_bspline_c3 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c4_prime * (q_10y - 1.5) + elastic_taylor_bspline_c4 * std::pow((q_10y - 1.5), 2) / 2;  // NOLINT
    } else {
      qcpx_logit = ori_logit +
                  elastic_taylor_bspline_c0 +
                  elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c2_prime * 0.5 + elastic_taylor_bspline_c2 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c3_prime * 0.5 + elastic_taylor_bspline_c3 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c4_prime * 0.5 + elastic_taylor_bspline_c4 * std::pow(0.5, 2) / 2 +  // NOLINT
                  elastic_taylor_bspline_c5_prime * (q_10y - 2.0) + elastic_taylor_bspline_c5 * std::pow((q_10y - 2.0), 2) / 2;  // NOLINT
    }
  }
  qcpx_logit = std::min(std::max(qcpx_logit, -15.0), 15.0);  //  -15, 15
  double qcpx_cvr = std::exp(qcpx_logit) / (1 + std::exp(qcpx_logit));

  // 货架 cvr 校准
  if (params_.enable_shelf_qcpx_photo_cvr_adjust) {
    qcpx_cvr *= params_.shelf_qcpx_photo_cvr_adjust_alpha;
    RANK_DOT_COUNT(session_data_, params_.shelf_qcpx_photo_cvr_adjust_alpha,
        "ad.ad_rank.shelf_qcpx_photo_cvr_adjust_alpha");
  }

  if (params_.enable_rewarded_qcpx_photo_cvr_adjust) {
    qcpx_cvr *= params_.rewarded_qcpx_photo_cvr_adjust_alpha;
    RANK_DOT_COUNT(session_data_, params_.rewarded_qcpx_photo_cvr_adjust_alpha,
        "ad.ad_rank.rewarded_qcpx_photo_cvr_adjust_alpha");
  }

  params_.coupon_threshold = random_coupon_threshold;
  params_.coupon_amount = random_coupon_amount;
  params_.cpm_ratio = 1.0;  // 不改竞价

  // elastic 落日志
  p_ad->Attr(ItemIdx::inner_qcpx_photo_cvr).SetDoubleValue(p_ad->AttrIndex(), qcpx_cvr, false, false);  // NOLINT
}

void QcpxPhotoStrategy::DecideByMaxRatio(AdCommon* p_ad) {  // NOLINT
  // 可发券筛选
  int32_t threshold_price = GetThresholdPrice(p_ad) + 200;
  p_ad->Attr(ItemIdx::threshold_price).SetIntValue(p_ad->AttrIndex(), threshold_price - 200, false, false);
  int32_t price_coupon_ratio = params_.value_qcpx_photo_price_coupon_ratio;
  // 分价格带控补贴率上限
  if (params_.enable_qcpx_photo_price_range_coupon_ratio) {
    int32_t ori_threshold_price = threshold_price - 200;
    if (ori_threshold_price >= 10 * 1e3 && ori_threshold_price < 50 * 1e3) {
      price_coupon_ratio = params_.value_qcpx_photo_price_range_coupon_ratio_10_50;
    } else if (ori_threshold_price >= 50 * 1e3 && ori_threshold_price < 100 * 1e3) {
      price_coupon_ratio = params_.value_qcpx_photo_price_range_coupon_ratio_50_100;
    }
  }
  if (price_coupon_ratio < 1) return;  // 满减系数 >= 1
  int32_t min_coupon_amount_yuan = params_.value_qcpx_photo_min_coupon_amount_yuan;
  int32_t max_coupon_amount_yuan = std::min(params_.value_qcpx_photo_max_coupon_amount_yuan,
                                static_cast<int>(threshold_price / 1e3 / price_coupon_ratio));  // NOLINT
  if (params_.enable_photo_qcpx_cpa_bid_max_amount &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
    max_coupon_amount_yuan = std::min(params_.value_qcpx_photo_max_coupon_amount_yuan,
      static_cast<int>(GetRealtimeCpaBid(p_ad) * params_.photo_qcpx_cpa_bid_max_amount_ratio / 1000));
  }
  if (max_coupon_amount_yuan < min_coupon_amount_yuan) return;
  double bid = 1.0;
  double mock_cpa_bid = 1.0;
  double ori_pcvr = p_ad->get_unify_cvr_info().value;
  if (ori_pcvr <= 0.0) {
    return;
  }
  double ori_ltv = p_ad->get_unify_ltv_info().value;  // 原始单位 元
  double roas_coef = p_ad->get_auto_roas() > 0.0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();  // NOLINT
  double roi_ratio = std::max(p_ad->get_roi_ratio(), 0.1);
  roas_coef = std::max(roas_coef, 0.1);
  if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
    bid = GetRealtimeCpaBid(p_ad) > 0 ? GetRealtimeCpaBid(p_ad) : p_ad->get_auto_cpa_bid();
    // 全站暗投订单部分 使用 auto_cpa_bid
    if (params_.enable_qcpx_photo_actual_storewide_bid_update && p_ad->get_scene_oriented_type() == 24) {
      bid = p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : GetRealtimeCpaBid(p_ad);
    }
    // NOBID 部分 使用 auto_cpa_bid
    if (p_ad->Is(AdFlag::IsEspUnifyNobidAdV2)) {
      bid = p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : GetRealtimeCpaBid(p_ad);
    }
    mock_cpa_bid = GetRealtimeCpaBid(p_ad);
  } else {
    bid = ori_ltv * 1e3 / roas_coef;
    mock_cpa_bid = ori_ltv * 1e3 / roi_ratio;
  }
  bid = std::max(bid, 1.0);
  double pcvr_ratio = 1.0;
  double bid_ratio = 1.0;
  double cpm_ratio = 1.0;

  // 泰勒样条回归模型
  double elastic_taylor_bspline_c0 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c0);  // NOLINT
  double elastic_taylor_bspline_c1 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1);  // NOLINT 
  double elastic_taylor_bspline_c2 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c2);  // NOLINT
  double elastic_taylor_bspline_c3 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c3);  // NOLINT
  double elastic_taylor_bspline_c4 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c4);  // NOLINT
  double elastic_taylor_bspline_c5 = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c5);  // NOLINT
  double elastic_taylor_bspline_c1_prime = p_ad->get_predict_score(PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1_prime);  // NOLINT

  double clip_ori_pcvr = std::clamp(ori_pcvr, 0.000000305902227, 0.9999996941);  //  -15, 15
  double logit_divisor = 1.0 - clip_ori_pcvr;
  if (logit_divisor == 0) return;
  double ori_logit = std::log(clip_ori_pcvr / logit_divisor);
  double ori_ecpm = bid * clip_ori_pcvr;
  int32_t tmp_max_profit_i = 0;
  int32_t tmp_max_profit_coupon_amount = 0;
  int32_t tmp_max_profit_coupon_threshold = 0;
  double tmp_max_profit_ecpm = ori_ecpm;
  double tmp_max_profit_cvr = clip_ori_pcvr;
  double tmp_roi_bound_final = 1.0;
  double low_roi_threshold = 1.0;  // 不能发比 cpa_bid 更高的券
  low_roi_threshold = params_.value_qcpx_photo_skip_low_roi_threshold;
  // unify_ROI_pacing_strategy
  double unify_roi_pacing_coef = 1.0;
  double G = params_.value_qcpx_photo_G;
  if (!params_.enable_qcpx_photo_merge_roi_logic) {
    if (params_.enable_qcpx_unify_ROI_pacing_strategy) {
      // 全站
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS && params_.value_qcpx_unify_ROI_pacing_photo_storewide > 0) {  // NOLINT
        unify_roi_pacing_coef *= params_.value_qcpx_unify_ROI_pacing_photo_storewide;
      }
      // 订单
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
          p_ad->get_scene_oriented_type() == 24 &&
          params_.enable_qcpx_unify_ROI_pacing_strategy_s24 &&
          params_.value_qcpx_unify_ROI_pacing_photo_order_s24 > 0) {
        unify_roi_pacing_coef *= params_.value_qcpx_unify_ROI_pacing_photo_order_s24;
      } else if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED && params_.value_qcpx_unify_ROI_pacing_photo_order > 0) {  // NOLINT
        unify_roi_pacing_coef *= params_.value_qcpx_unify_ROI_pacing_photo_order;
      }
    }
    // 全站暗投订单 调整系数
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
        p_ad->get_scene_oriented_type() == 24 &&
        params_.enable_qcpx_photo_storewide_order_ROI_pacing_strategy) {
      unify_roi_pacing_coef *= params_.value_qcpx_photo_ROI_pacing_photo_storewide_order;
    }
    // 商家白名单 调整系数
    if (params_.enable_qcpx_author_roi_pacing &&
        params_.author_roi_pacing_set != nullptr &&
        params_.author_roi_pacing_set->count(p_ad->get_author_id())) {
      unify_roi_pacing_coef *= params_.value_qcpx_author_roi_pacing;
    }
    if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
      G = params_.value_qcpx_photo_whitebox_G;
      RANK_DOT_COUNT(session_data_, 1, "ad.ad_rank.photo_whitebox_roi_bound");
    }
    G *= unify_roi_pacing_coef;
    G = std::max(G, params_.value_qcpx_photo_G_lower_bound);
  } else {
    G = GetROIAdjustFactor(p_ad);
  }

  for (int32_t i = min_coupon_amount_yuan; i <= max_coupon_amount_yuan; ++i) {
    double q_10y = i / 10.0;  // 单位 十元
    double q = i * 1e3;
    // @fandi skip low roi
    if (mock_cpa_bid < low_roi_threshold * q) {
      continue;
    }
    int32_t coupon_threshold = q * price_coupon_ratio - 200;
    int q_yuan = i;
    double qcpx_logit = 0.0;
    if (params_.enable_qcpx_photo_elastic_taylor_bspline_model) {
      double elastic_taylor_bspline_c2_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1;  // NOLINT
      double elastic_taylor_bspline_c3_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1 + 0.5 * elastic_taylor_bspline_c2 ; // NOLINT
      double elastic_taylor_bspline_c4_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1 + 0.5 * elastic_taylor_bspline_c2  + 0.5 * elastic_taylor_bspline_c3;  // NOLINT
      double elastic_taylor_bspline_c5_prime = elastic_taylor_bspline_c1_prime + 0.5 * elastic_taylor_bspline_c1 + 0.5 * elastic_taylor_bspline_c2  + 0.5 * elastic_taylor_bspline_c3  + 0.5 * elastic_taylor_bspline_c4;  // NOLINT
      if (q_yuan < 5) {
        qcpx_logit = ori_logit +
                     elastic_taylor_bspline_c0 +
                     elastic_taylor_bspline_c1_prime * q_10y + elastic_taylor_bspline_c1 * std::pow(q_10y, 2) / 2;  // NOLINT
      } else if (q_yuan < 10) {
        qcpx_logit = ori_logit +
                    elastic_taylor_bspline_c0 +
                    elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c2_prime * (q_10y - 0.5) + elastic_taylor_bspline_c2 * std::pow((q_10y - 0.5), 2) / 2;  // NOLINT
      } else if (q_yuan < 15) {
        qcpx_logit = ori_logit +
                    elastic_taylor_bspline_c0 +
                    elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c2_prime * 0.5 + elastic_taylor_bspline_c2 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c3_prime * (q_10y - 1.0) + elastic_taylor_bspline_c3 * std::pow((q_10y - 1.0), 2) / 2;  // NOLINT
      } else if (q_yuan < 20) {
        qcpx_logit = ori_logit +
                    elastic_taylor_bspline_c0 +
                    elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c2_prime * 0.5 + elastic_taylor_bspline_c2 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c3_prime * 0.5 + elastic_taylor_bspline_c3 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c4_prime * (q_10y - 1.5) + elastic_taylor_bspline_c4 * std::pow((q_10y - 1.5), 2) / 2;  // NOLINT
      } else {
        qcpx_logit = ori_logit +
                    elastic_taylor_bspline_c0 +
                    elastic_taylor_bspline_c1_prime * 0.5 + elastic_taylor_bspline_c1 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c2_prime * 0.5 + elastic_taylor_bspline_c2 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c3_prime * 0.5 + elastic_taylor_bspline_c3 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c4_prime * 0.5 + elastic_taylor_bspline_c4 * std::pow(0.5, 2) / 2 +  // NOLINT
                    elastic_taylor_bspline_c5_prime * (q_10y - 2.0) + elastic_taylor_bspline_c5 * std::pow((q_10y - 2.0), 2) / 2;  // NOLINT
      }
    }
    qcpx_logit = std::min(std::max(qcpx_logit, -15.0), 15.0);  //  -15, 15
    double qcpx_cvr = std::exp(qcpx_logit) / (1 + std::exp(qcpx_logit));
    if (params_.enable_qcpx_photo_amount_set_upper_bound) {
      qcpx_cvr = (qcpx_cvr >= clip_ori_pcvr * params_.value_qcpx_photo_uplift_ratio_upper_bound)
            ? clip_ori_pcvr * params_.value_qcpx_photo_uplift_ratio_upper_bound: qcpx_cvr;
    }

    // 货架 cvr 校准
    if (params_.enable_shelf_qcpx_photo_cvr_adjust) {
      qcpx_cvr *= params_.shelf_qcpx_photo_cvr_adjust_alpha;
      RANK_DOT_STATS(session_data_, params_.shelf_qcpx_photo_cvr_adjust_alpha*1000,
          "ad.ad_rank.shelf_qcpx_photo_cvr_adjust_alpha");
    }
    // 搜后推
    if (params_.enable_qcpx_photo_search_and_push) {
      qcpx_cvr *= params_.inner_qcpx_search_and_push_ratio;
    }

    double new_ecpm = (bid - q) * qcpx_cvr;
    if (G > 0) {
      new_ecpm = (bid - q * G) * qcpx_cvr;
      tmp_roi_bound_final = G;
    }
    // 内部测试 高危 修改请确认 @fandi
    if (params_.is_qcpx_test_user) {
      new_ecpm = bid * qcpx_cvr * params_.qcpx_test_user_boost_ratio;
    }
    if (new_ecpm > tmp_max_profit_ecpm) {
      tmp_max_profit_ecpm = new_ecpm;
      tmp_max_profit_i = i;
      tmp_max_profit_coupon_amount = q;
      tmp_max_profit_cvr = qcpx_cvr;
      tmp_max_profit_coupon_threshold = coupon_threshold;
    }
  }
  if (tmp_max_profit_i == 0) {
    p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::STRATEGY_MODEL);
    params_.inner_qcpx_cause = 31;
  } else {
    params_.inner_qcpx_cause = 32;
    pcvr_ratio = tmp_max_profit_cvr / clip_ori_pcvr;
    bid_ratio = 1.0 - tmp_max_profit_i * 1e3 / bid;
    cpm_ratio = bid_ratio * pcvr_ratio;
    if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_PEC) {
      cpm_ratio = pcvr_ratio;
    }
    // 内部测试 高危 修改请确认 @fandi
    if (params_.is_qcpx_test_user) {
      bid_ratio = 1.0;
      cpm_ratio = pcvr_ratio * params_.qcpx_test_user_boost_ratio;
    }
    if (cpm_ratio > 1.0) {
      if (params_.enable_qcpx_photo_roas_optim_decision_module && ori_ecpm > 0.0) {
        params_.tmp_max_profit_ecpm_amt = tmp_max_profit_ecpm / ori_ecpm;
      }
      params_.coupon_threshold = tmp_max_profit_coupon_threshold;
      params_.coupon_amount = tmp_max_profit_coupon_amount;
      params_.roi_bound_final = tmp_roi_bound_final;
      params_.cpm_ratio = cpm_ratio;
      params_.bid_ratio = bid_ratio;
      params_.pcvr_ratio = pcvr_ratio;
      p_ad->Attr(ItemIdx::inner_qcpx_photo_cvr).SetDoubleValue(p_ad->AttrIndex(), tmp_max_profit_cvr, false, false);  // NOLINT
    }
  }
  // elastic 落日志
  // bidding 落日志
  p_ad->Attr(ItemIdx::inner_qcpx_photo_use_bid).SetDoubleValue(p_ad->AttrIndex(), bid, false, false);  // NOLINT
}

void QcpxPhotoStrategy::ProcessThreshold(AdCommon* p_ad) {
  params_.coupon_threshold = params_.coupon_amount * params_.value_qcpx_photo_thre_ratio_when_delivery - 200;  // NOLINT
  if (!params_.enable_qcpx_photo_no_min_5_thres) {
    params_.coupon_threshold = std::max(params_.coupon_threshold, static_cast<int64_t>(5000));
  }
  if (params_.enable_qcpx_photo_no_threshold_order &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
    params_.coupon_threshold = params_.coupon_amount + 100;
  }
}

int64_t QcpxPhotoStrategy::GetRealtimeCpaBid(AdCommon* p_ad) {
  int64_t cpa_bid = p_ad->get_cpa_bid();
  double rl_pay_amount_per_order =
    p_ad->Attr(ItemIdx::fd_PRODUCT_rl_pay_amount_per_order).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
  int64_t event_order_paid_cnt =
    p_ad->Attr(ItemIdx::fd_PRODUCT_event_order_paid_cnt).GetIntValue(p_ad->AttrIndex()).value_or(0);
  int64_t project_ocpx_action_type =
    p_ad->Attr(ItemIdx::fd_ecom_hosting_project_ocpx_action_type).GetIntValue(p_ad->AttrIndex()).value_or(0);
  double project_roi_ratio =
    p_ad->Attr(ItemIdx::fd_ecom_hosting_project_roi_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);

  if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
      (project_ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
       project_ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS) &&
      project_roi_ratio > 1e-6 &&
      rl_pay_amount_per_order > 0.0) {
    double antou_cpa_bid = (rl_pay_amount_per_order / project_roi_ratio) * 1000;
    if (p_ad->get_cpa_bid() > 2000 &&
        p_ad->get_cpa_bid() > antou_cpa_bid * params_.qcpx_photo_order_antou_realtime_bid_ratio) {
      cpa_bid = static_cast<int64_t>(antou_cpa_bid);
    }
    if (event_order_paid_cnt >= params_.qcpx_photo_order_antou_realtime_bid_up_thre &&
        p_ad->get_cpa_bid() < antou_cpa_bid * params_.qcpx_photo_order_antou_realtime_bid_up_ratio) {
      cpa_bid = static_cast<int64_t>(antou_cpa_bid);
    }
  }
  return cpa_bid;
}

double QcpxPhotoStrategy::GetROIAdjustFactor(AdCommon* p_ad) {
  double roi_factor = params_.value_qcpx_photo_G;
  if (params_.qpon_type == kuaishou::ad::AdEnum_QponType_INNER_QCPX_OPEN) {
    roi_factor = params_.value_qcpx_photo_whitebox_G;
    RANK_DOT_COUNT(session_data_, 1, "ad.ad_rank.photo_whitebox_roi_bound");
  }

  // 系数拆分
  double unify_roi_pacing_coef = 1.0;
  if (params_.enable_qcpx_unify_ROI_pacing_strategy) {
    // 全站
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS && params_.value_qcpx_unify_ROI_pacing_photo_storewide > 0) {  // NOLINT
      unify_roi_pacing_coef *= params_.value_qcpx_unify_ROI_pacing_photo_storewide;
    }
    // 订单
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
        p_ad->get_scene_oriented_type() == 24 &&
        params_.enable_qcpx_unify_ROI_pacing_strategy_s24 &&
        params_.value_qcpx_unify_ROI_pacing_photo_order_s24 > 0) {
      unify_roi_pacing_coef *= params_.value_qcpx_unify_ROI_pacing_photo_order_s24;
    } else if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED && params_.value_qcpx_unify_ROI_pacing_photo_order > 0) {  // NOLINT
      unify_roi_pacing_coef *= params_.value_qcpx_unify_ROI_pacing_photo_order;
    }
  }
  // 全站暗投订单 调整系数
  if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
      p_ad->get_scene_oriented_type() == 24 &&
      params_.enable_qcpx_photo_storewide_order_ROI_pacing_strategy) {
    unify_roi_pacing_coef *= params_.value_qcpx_photo_ROI_pacing_photo_storewide_order;
  }
  // 商家白名单 调整系数
  if (params_.enable_qcpx_author_roi_pacing &&
      params_.author_roi_pacing_set != nullptr &&
      params_.author_roi_pacing_set->count(p_ad->get_author_id())) {
    unify_roi_pacing_coef *= params_.value_qcpx_author_roi_pacing;
  }
  roi_factor *= unify_roi_pacing_coef;

  static const std::unordered_set<int64_t> valid_pid_tag = {
      kuaishou::ad::PidServer::PidTag::INNER_QCPX_PID_TAG,
      kuaishou::ad::PidServer::PidTag::INNER_QCPX_PID_BASE_TAG,
      kuaishou::ad::PidServer::PidTag::INNER_QCPX_PID_EXP_TAG,
      kuaishou::ad::PidServer::PidTag::INNER_QCPX_PID_EXP1_TAG,
      kuaishou::ad::PidServer::PidTag::INNER_QCPX_PID_EXP2_TAG};
  bool is_valid_pid_tag = valid_pid_tag.count(params_.inner_qcpx_roi_pid_tag) > 0;
  // ROI 实时调控
  if (params_.enable_inner_qcpx_photo_roi_adjust) {
    roi_factor = GetAutoROIFactor(p_ad, roi_factor);
  } else if (params_.enable_inner_qcpx_roi_pid_strategy && is_valid_pid_tag) {
    roi_factor = GetAutoROIPidFactor(p_ad, roi_factor);
  }
  roi_factor = std::max(roi_factor, params_.value_qcpx_photo_G_lower_bound);

  if (params_.enable_qcpx_photo_insert_pid_tag && is_valid_pid_tag) {
    p_ad->mutable_pid_tags()->insert(params_.inner_qcpx_roi_pid_tag);  // pid_tag，用于 AB 分流
    RANK_DOT_COUNT(session_data_, 1, "ad_rank_qcpx.photo_insert_pid_tag");
  }

  return roi_factor;
}

double QcpxPhotoStrategy::GetAutoROIFactor(AdCommon* p_ad, double ori_roi_factor) {
  double delta_ecpm = params_.qcpx_ecpm_24h - params_.main_ecpm_24h;  // 计算先验 delta_ecpm
  if (delta_ecpm == 0 || params_.q_cost_24h == 0) {
    return ori_roi_factor;
  }

  // 计算先验 ROI
  double pre_roi = delta_ecpm / params_.q_cost_24h;
  double err = params_.value_qcpx_roi_goal - pre_roi;
  double auto_pacing_coef = 1.0, auto_pacing_coef_clipped = 1.0;
  double auto_roi_factor = ori_roi_factor;
  // 区分不同优化目标调控函数系数
  if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
    auto_pacing_coef = std::exp(err * params_.value_qcpx_photo_order_err_factor);
  } else if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) {
    auto_pacing_coef = std::exp(err * params_.value_qcpx_photo_storewide_roas_err_factor);
  } else if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) {
    auto_pacing_coef = std::exp(err * params_.value_qcpx_photo_merchant_roas_err_factor);
  } else {
    return ori_roi_factor;
  }
  if (params_.enable_qcpx_photo_neg_err_factor && err < 0.0) {  // 高 ROI 分段调控
    auto_pacing_coef = std::exp(err * params_.value_qcpx_photo_neg_err_factor);
  }
  auto_pacing_coef_clipped = std::clamp(auto_pacing_coef, params_.value_qcpx_photo_auto_roi_lower_bound,
                                        params_.value_qcpx_photo_auto_roi_upper_bound);
  auto_roi_factor *= auto_pacing_coef_clipped;
  // 监控
  RANK_DOT_STATS(session_data_, auto_pacing_coef_clipped * 1000, "ad_rank_qcpx.qcpx_auto_pacing_coef_clipped",
                 absl::StrCat(p_ad->get_item_type()), absl::StrCat(p_ad->get_ad_queue_type()),
                 absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_STATS(session_data_, pre_roi * 1000, "ad_rank_qcpx.pre_roi",
                 absl::StrCat(p_ad->get_item_type()), absl::StrCat(p_ad->get_ad_queue_type()),
                 absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_STATS(session_data_, auto_roi_factor * 1000, "ad_rank_qcpx.qcpx_auto_roi_factor",
                 absl::StrCat(p_ad->get_item_type()), absl::StrCat(p_ad->get_ad_queue_type()),
                 absl::StrCat(p_ad->get_ocpx_action_type()));
  // 落日志
  p_ad->Attr(ItemIdx::inner_qcpx_auto_roi_pacing_coef).SetDoubleValue(p_ad->AttrIndex(), auto_pacing_coef, false, false);  // NOLINT

  return auto_roi_factor;
}

double QcpxPhotoStrategy::GetAutoROIPidFactor(AdCommon* p_ad, double ori_roi_factor) {
  double pid_coef = p_ad->get_inner_qcpx_roi_pid_value();
  if (std::abs(pid_coef) < 1e-6 || std::abs(pid_coef - 1.0) < 1e-6) {
    return ori_roi_factor;
  }
  double auto_roi_factor = ori_roi_factor * pid_coef;
  // 监控
  RANK_DOT_STATS(session_data_, pid_coef * 1000, "ad_rank_qcpx.qcpx_pid_coef",
                 absl::StrCat(p_ad->get_item_type()), absl::StrCat(p_ad->get_ad_queue_type()),
                 absl::StrCat(p_ad->get_ocpx_action_type()), absl::StrCat(params_.inner_qcpx_roi_pid_tag));
  RANK_DOT_STATS(session_data_, auto_roi_factor * 1000, "ad_rank_qcpx.qcpx_pid_auto_roi_factor",
                 absl::StrCat(p_ad->get_item_type()), absl::StrCat(p_ad->get_ad_queue_type()),
                 absl::StrCat(p_ad->get_ocpx_action_type()), absl::StrCat(params_.inner_qcpx_roi_pid_tag));
  RANK_DOT_COUNT(session_data_, 1, "ad_rank_qcpx.qcpx_pid_control_cnt",
                 absl::StrCat(p_ad->get_item_type()), absl::StrCat(p_ad->get_ad_queue_type()),
                 absl::StrCat(p_ad->get_ocpx_action_type()), absl::StrCat(params_.inner_qcpx_roi_pid_tag));

  return auto_roi_factor;
}

}  // namespace ad_rank
}  // namespace ks
