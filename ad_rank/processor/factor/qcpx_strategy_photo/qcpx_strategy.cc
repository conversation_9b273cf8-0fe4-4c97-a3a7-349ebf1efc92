#include "teams/ad/ad_rank/processor/factor/qcpx_strategy_photo/qcpx_strategy.h"

#include <algorithm>
#include <cstdint>
#include <memory>
#include <numeric>
#include <string>
#include <unordered_set>
#include <vector>

#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/ad_proto/maven/ad/coupon/ad_coupon_cache.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/engine_base/utils/utils.h"

using kuaishou::ad::AdEnum;
namespace ks {
namespace ad_rank {

const char* QcpxPhotoStrategy::Name() {
  return "qcpx_photo_strategy";
}

void QcpxPhotoStrategy::InitParams() {
  // *********** request 粒度 参数 & 变量 ***********
  params_.user_id = session_data_->get_user_id();  // 内部测试字段 高危 修改请确认 @fandi
  // spdm
  params_.enable_inner_pec_holdout =
    SPDM_enable_inner_pec_holdout(session_data_->get_spdm_ctx());
  params_.enable_inner_qcpx_open_holdout =
    SPDM_enable_inner_qcpx_open_holdout(session_data_->get_spdm_ctx());

  params_.value_qcpx_photo_rct_flow_percent =
    SPDM_value_qcpx_photo_rct_flow_percent(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_model_flow_percent =
    SPDM_value_qcpx_photo_model_flow_percent(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_price_coupon_ratio =
    SPDM_value_qcpx_photo_price_coupon_ratio(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_max_coupon_amount_yuan =
    SPDM_value_qcpx_photo_max_coupon_amount_yuan(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_min_coupon_amount_yuan =
    SPDM_value_qcpx_photo_min_coupon_amount_yuan(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_no_min_5_thres =
    SPDM_enable_qcpx_photo_no_min_5_thres(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_threshold_max_price =
    SPDM_enable_qcpx_photo_threshold_max_price(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_threshold_mode_item_price =
    SPDM_enable_qcpx_photo_threshold_mode_item_price(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_threshold_price_strategy =
    SPDM_enable_qcpx_photo_threshold_price_strategy(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_history_paid_count_thres =
    SPDM_value_qcpx_photo_history_paid_count_thres(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_gmv_greater_min_price_ratio_thres =
    SPDM_value_qcpx_photo_gmv_greater_min_price_ratio_thres(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_mode_gmv_ratio_thres =
    SPDM_value_qcpx_photo_mode_gmv_ratio_thres(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_min_price_higher_avg_gmv_ratio_thres =
    SPDM_value_qcpx_photo_min_price_higher_avg_gmv_ratio_thres(session_data_->get_spdm_ctx());
  params_.specific_amount_coupon_id =
    SPDM_value_qcpx_photo_specific_amount_coupon_id(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_rct_amount_control_flow_percent =
    SPDM_value_qcpx_photo_rct_amount_control_flow_percent(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_rct_amount_treatment_flow_percent =
    SPDM_value_qcpx_photo_rct_amount_treatment_flow_percent(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_rct_rate_control_flow_percent =
    SPDM_value_qcpx_photo_rct_rate_control_flow_percent(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_rct_rate_treatment_flow_percent =
    SPDM_value_qcpx_photo_rct_rate_treatment_flow_percent(session_data_->get_spdm_ctx());
  // 分价格带控补贴率上限
  params_.enable_qcpx_photo_price_range_coupon_ratio =
    SPDM_enable_qcpx_photo_price_range_coupon_ratio(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_price_range_coupon_ratio_10_50 =
    SPDM_value_qcpx_photo_price_range_coupon_ratio_10_50(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_price_range_coupon_ratio_50_100 =
    SPDM_value_qcpx_photo_price_range_coupon_ratio_50_100(session_data_->get_spdm_ctx());

  // 货架 cvr 校准
  params_.enable_shelf_qcpx_photo_cvr_adjust =
    (SPDM_enable_shelf_qcpx_photo_cvr_adjust(session_data_->get_spdm_ctx())
    && session_data_->get_pos_manager_base().IsShelfMerchantTraffic());
  params_.shelf_homepage_qcpx_photo_cvr_adjust_alpha =
    SPDM_shelf_homepage_qcpx_photo_cvr_adjust_alpha(session_data_->get_spdm_ctx());
  params_.shelf_mall_qcpx_photo_cvr_adjust_alpha =
    SPDM_shelf_mall_qcpx_photo_cvr_adjust_alpha(session_data_->get_spdm_ctx());
  params_.shelf_guesslike_qcpx_photo_cvr_adjust_alpha =
    SPDM_shelf_guesslike_qcpx_photo_cvr_adjust_alpha(session_data_->get_spdm_ctx());

  if (params_.enable_shelf_qcpx_photo_cvr_adjust) {
    if (session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
      params_.shelf_qcpx_photo_cvr_adjust_alpha = params_.shelf_homepage_qcpx_photo_cvr_adjust_alpha;
    } else if (session_data_->get_pos_manager_base().IsMallTraffic()) {
      params_.shelf_qcpx_photo_cvr_adjust_alpha = params_.shelf_mall_qcpx_photo_cvr_adjust_alpha;
    } else {
      params_.shelf_qcpx_photo_cvr_adjust_alpha = params_.shelf_guesslike_qcpx_photo_cvr_adjust_alpha;
    }
  }
  params_.enable_rewarded_qcpx_photo_cvr_adjust =
    (SPDM_enable_rewarded_qcpx_photo_cvr_adjust(session_data_->get_spdm_ctx())
    && (session_data_->get_is_rewarded() || session_data_->get_is_inspire_live_request()));
  params_.rewarded_qcpx_photo_cvr_adjust_alpha =
    SPDM_rewarded_qcpx_photo_cvr_adjust(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_specific_amount_coupon_id =
    SPDM_enable_qcpx_photo_specific_amount_coupon_id(session_data_->get_spdm_ctx());
  params_.enable_qcpx_holdout_no_rct =
    SPDM_enable_qcpx_holdout_no_rct(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_paid_elastic_piecewise_model =
    SPDM_enable_qcpx_photo_paid_elastic_piecewise_model(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_roas_elastic_piecewise_model =
    SPDM_enable_qcpx_photo_roas_elastic_piecewise_model(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_bid_update_roas_rate =
    SPDM_enable_qcpx_photo_bid_update_roas_rate(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_bid_update_storewide_rate =
    SPDM_enable_qcpx_photo_bid_update_storewide_rate(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_no_threshold_order =
    SPDM_enable_qcpx_photo_no_threshold_order(session_data_->get_spdm_ctx());
  params_.enable_photo_qcpx_cpa_bid_max_amount =
    SPDM_enable_photo_qcpx_cpa_bid_max_amount(session_data_->get_spdm_ctx());
  params_.photo_qcpx_cpa_bid_max_amount_ratio =
    SPDM_photo_qcpx_cpa_bid_max_amount_ratio(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_skip_low_roi_threshold =
    SPDM_value_qcpx_photo_skip_low_roi_threshold(session_data_->get_spdm_ctx());

  // RCT 分流机制
  params_.enable_qcpx_photo_allocate_flow_v2 =
    SPDM_enable_qcpx_photo_allocate_flow_v2(session_data_->get_spdm_ctx());

  // *********** ROI 系数相关 ***********
  // 商家白名单 调整系数
  params_.enable_qcpx_author_roi_pacing =
    SPDM_enable_qcpx_author_roi_pacing(session_data_->get_spdm_ctx());
  params_.value_qcpx_author_roi_pacing =
    SPDM_value_qcpx_author_roi_pacing(session_data_->get_spdm_ctx());
  params_.author_roi_pacing_set = RankKconfUtil::qcpxAuthorPacingSet();
  params_.value_qcpx_photo_G =
    SPDM_value_qcpx_photo_G(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_whitebox_G =
    SPDM_value_qcpx_photo_whitebox_roi_bound(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_G_rate =
    SPDM_value_qcpx_photo_G_rate(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_whitebox_G_rate =
    SPDM_value_qcpx_photo_whitebox_G_rate(session_data_->get_spdm_ctx());
  params_.shelf_merchant_photo_qcpx_roi_ratio =
    SPDM_shelf_merchant_photo_qcpx_roi_ratio(session_data_->get_spdm_ctx());
  params_.follow_photo_qcpx_roi_ratio =
    SPDM_follow_photo_qcpx_roi_ratio(session_data_->get_spdm_ctx());
  // 折扣券 ROAS 调整系数
  params_.enable_qcpx_photo_G_rate_ratio_roas =
    SPDM_enable_qcpx_photo_G_rate_ratio_roas(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_G_rate_ratio_roas =
    SPDM_value_qcpx_photo_G_rate_ratio_roas(session_data_->get_spdm_ctx());
  // 全站暗投订单 调整系数
  params_.enable_qcpx_photo_storewide_order_ROI_pacing_strategy =
    SPDM_enable_qcpx_photo_storewide_order_ROI_pacing_strategy(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_ROI_pacing_photo_storewide_order =
    SPDM_value_qcpx_photo_ROI_pacing_photo_storewide_order(session_data_->get_spdm_ctx());
  // ROI 兜底
  params_.value_qcpx_photo_G_lower_bound =
    SPDM_value_qcpx_photo_G_lower_bound(session_data_->get_spdm_ctx());
  // ROI 调控
  params_.enable_inner_qcpx_photo_roi_adjust =
    SPDM_enable_inner_qcpx_photo_roi_adjust(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_merge_roi_logic =
    SPDM_enable_qcpx_photo_merge_roi_logic(session_data_->get_spdm_ctx());
  params_.value_qcpx_roi_goal =
    SPDM_value_qcpx_roi_goal(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_order_err_factor =
    SPDM_value_qcpx_photo_order_err_factor(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_storewide_roas_err_factor =
    SPDM_value_qcpx_photo_storewide_roas_err_factor(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_merchant_roas_err_factor =
    SPDM_value_qcpx_photo_merchant_roas_err_factor(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_auto_roi_upper_bound =
    SPDM_value_qcpx_photo_auto_roi_upper_bound(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_auto_roi_lower_bound =
    SPDM_value_qcpx_photo_auto_roi_lower_bound(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_neg_err_factor =
    SPDM_enable_qcpx_photo_neg_err_factor(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_neg_err_factor =
    SPDM_value_qcpx_photo_neg_err_factor(session_data_->get_spdm_ctx());
  // ROI-PID
  params_.enable_inner_qcpx_roi_pid_strategy =
    SPDM_enable_inner_qcpx_roi_pid_strategy(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_insert_pid_tag =
    SPDM_enable_qcpx_photo_insert_pid_tag(session_data_->get_spdm_ctx());
  params_.inner_qcpx_roi_pid_tag = SPDM_inner_qcpx_roi_pid_tag(session_data_->get_spdm_ctx());

  params_.enable_qcpx_photo_rate_rct_log_pcvr =
    SPDM_enable_qcpx_photo_rate_rct_log_pcvr(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_roas_unify_coupon_module =
    SPDM_enable_qcpx_photo_roas_unify_coupon_module(session_data_->get_spdm_ctx());
  params_.enable_shelf_photo_shield_discount =
    SPDM_enable_shelf_photo_shield_discount(session_data_->get_spdm_ctx());
  params_.enable_shelf_qcpx_photo_fixed_discount =
    SPDM_enable_shelf_qcpx_photo_fixed_discount(session_data_->get_spdm_ctx());
  params_.shelf_qcpx_photo_fixed_discount =
    SPDM_shelf_qcpx_photo_fixed_discount(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_rate_coupon_with_capped =
    SPDM_enable_qcpx_photo_rate_coupon_with_capped(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_elastic_taylor_bspline_model =
    SPDM_enable_qcpx_photo_elastic_taylor_bspline_model(session_data_->get_spdm_ctx());
  params_.enable_shelf_qcpx_photo_price_range_full_dis =
    SPDM_enable_shelf_qcpx_photo_price_range_full_dis(session_data_->get_spdm_ctx());
  params_.shelf_qcpx_photo_price_range_full_dis_tag =
    SPDM_shelf_qcpx_photo_price_range_full_dis_tag(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_amount_set_upper_bound =
    SPDM_enable_qcpx_photo_amount_set_upper_bound(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_uplift_ratio_upper_bound =
    SPDM_value_qcpx_photo_uplift_ratio_upper_bound(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_rate_set_upper_bound =
    SPDM_enable_qcpx_photo_rate_set_upper_bound(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_rate_uplift_ratio_upper_bound =
    SPDM_value_qcpx_photo_rate_uplift_ratio_upper_bound(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_thre_ratio_when_delivery =
    SPDM_value_qcpx_photo_thre_ratio_when_delivery(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_cvr_rate_roas_multi_head_cemm_model_update =
    SPDM_enable_qcpx_photo_cvr_rate_roas_multi_head_cemm_model_update(session_data_->get_spdm_ctx());
  params_.enable_random_coloring_uplift =
    SPDM_enable_random_coloring_uplift(session_data_->get_spdm_ctx());
  params_.random_coloring_flow_percent =
    SPDM_random_coloring_flow_percent(session_data_->get_spdm_ctx());
  params_.enable_qcpx_random_color_v2 =
    SPDM_enable_qcpx_random_color_v2(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_bound_rate_coupon_model =
    SPDM_enable_qcpx_photo_bound_rate_coupon_model(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_bound_rate_coupon_model_right =
    SPDM_value_qcpx_photo_bound_rate_coupon_model_right(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_bound_rate_coupon_model_left =
    SPDM_value_qcpx_photo_bound_rate_coupon_model_left(session_data_->get_spdm_ctx());
  params_.enable_qcpx_expire_minutes = false;
  params_.enable_qcpx_photo_actual_storewide_bid_update =
    SPDM_enable_qcpx_photo_actual_storewide_bid_update(session_data_->get_spdm_ctx());
  params_.enable_qcpx_unify_ROI_pacing_strategy =
    SPDM_enable_qcpx_unify_ROI_pacing_strategy(session_data_->get_spdm_ctx());
  params_.value_qcpx_unify_ROI_pacing_photo_storewide =
    SPDM_value_qcpx_unify_ROI_pacing_photo_storewide(session_data_->get_spdm_ctx());
  params_.value_qcpx_unify_ROI_pacing_photo_order =
    SPDM_value_qcpx_unify_ROI_pacing_photo_order(session_data_->get_spdm_ctx());
  params_.qcpx_photo_order_antou_realtime_bid_ratio =
    SPDM_qcpx_photo_order_antou_realtime_bid_ratio(session_data_->get_spdm_ctx());
  params_.qcpx_photo_order_antou_realtime_bid_up_ratio =
    SPDM_qcpx_photo_order_antou_realtime_bid_up_ratio(session_data_->get_spdm_ctx());
  params_.qcpx_photo_order_antou_realtime_bid_up_thre =
    SPDM_qcpx_photo_order_antou_realtime_bid_up_thre(session_data_->get_spdm_ctx());
  params_.enable_qcpx_unify_ROI_pacing_strategy_s24 =
    SPDM_enable_qcpx_unify_ROI_pacing_strategy_s24(session_data_->get_spdm_ctx());
  params_.value_qcpx_unify_ROI_pacing_photo_order_s24 =
    SPDM_value_qcpx_unify_ROI_pacing_photo_order_s24(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_cvr_rate_generative_mono_model =
    SPDM_enable_qcpx_photo_cvr_rate_generative_mono_model(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_cvr_rate_elastic_model =
    SPDM_enable_qcpx_photo_cvr_rate_elastic_model(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_rate_pltv_merge_seller_bear_amt =
    SPDM_enable_qcpx_photo_rate_pltv_merge_seller_bear_amt(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_rate_realtime_item_price =
    SPDM_enable_qcpx_photo_rate_realtime_item_price(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_roas_optim_decision_module =
    SPDM_enable_qcpx_photo_roas_optim_decision_module(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_optimal_style_disable_feed_card_rct =
    SPDM_enable_qcpx_photo_optimal_style_disable_feed_card_rct(session_data_->get_spdm_ctx());
  params_.enable_qcpx_photo_search_and_push =
    SPDM_enable_qcpx_photo_search_and_push(session_data_->get_spdm_ctx());
  params_.value_qcpx_photo_optimal_style_disable_feed_card_rct_pp =
    SPDM_value_qcpx_photo_optimal_style_disable_feed_card_rct_pp(session_data_->get_spdm_ctx());
  // 膨胀券相关
  params_.enable_qcpx_amount_expand_coupon =
    SPDM_enable_qcpx_amount_expand_coupon(session_data_->get_spdm_ctx())
    && engine_base::CompareAppVersion(session_data_->get_app_version(), *RankKconfUtil::qcpxExpandVersionControlMinRequiredVersion()) >= 0  // NOLINT
    && (session_data_->get_sub_page_id() == 10011001 || session_data_->get_sub_page_id() == 100012194 ||
        session_data_->get_sub_page_id() == 100013100 || session_data_->get_sub_page_id() == 11001001 ||
        session_data_->get_sub_page_id() == 100016771 || session_data_->get_sub_page_id() == 10002001 ||
        session_data_->get_sub_page_id() == 10008001);  // NOLINT
  params_.qcpx_amount_expand_coupon_expand_ratio =
    SPDM_qcpx_amount_expand_coupon_expand_ratio(session_data_->get_spdm_ctx());

  if (SPDM_enable_shelf_merchant_photo_qcpx_roi(session_data_->get_spdm_ctx())
    && session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
      params_.value_qcpx_photo_G *= params_.shelf_merchant_photo_qcpx_roi_ratio;
      params_.value_qcpx_photo_whitebox_G *= params_.shelf_merchant_photo_qcpx_roi_ratio;
      params_.value_qcpx_photo_G_rate *= params_.shelf_merchant_photo_qcpx_roi_ratio;
      params_.value_qcpx_photo_whitebox_G_rate *= params_.shelf_merchant_photo_qcpx_roi_ratio;
  } else if (SPDM_enable_follow_photo_qcpx_roi(session_data_->get_spdm_ctx())
    && session_data_->get_pos_manager_base().IsFollow()) {
      params_.value_qcpx_photo_G *= params_.follow_photo_qcpx_roi_ratio;
      params_.value_qcpx_photo_whitebox_G *= params_.follow_photo_qcpx_roi_ratio;
      params_.value_qcpx_photo_G_rate *= params_.follow_photo_qcpx_roi_ratio;
      params_.value_qcpx_photo_whitebox_G_rate *= params_.follow_photo_qcpx_roi_ratio;
  }


  if (SPDM_enable_shelf_qcpx_photo_coupon(session_data_->get_spdm_ctx())
    && session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
      params_.value_qcpx_photo_max_coupon_amount_yuan =
        SPDM_shelf_qcpx_photo_max_coupon_amount_yuan(session_data_->get_spdm_ctx());
      params_.value_qcpx_photo_price_coupon_ratio =
        SPDM_shelf_qcpx_photo_price_coupon_ratio(session_data_->get_spdm_ctx());
  }

  // coupon_table
  params_.coupon_table = session_data_->common_r_->GetTable("ad_coupon_table");
  if (params_.coupon_table) {
    params_.coupon_type_attr = params_.coupon_table->GetAttr("fd_ad_coupon_template_coupon_type");
    params_.coupon_status_attr = params_.coupon_table->GetAttr("fd_ad_coupon_template_status");
    params_.coupon_rule_attr = params_.coupon_table->GetAttr("fd_ad_coupon_template_rule");
  }
  // 监控
  params_.coupon_delivery_cnt.clear();
  // 内部测试字段 高危 修改请确认 @fandi
  if (RankKconfUtil::enableQcpxTestUserList() && RankKconfUtil::qcpxTestUserList() != nullptr) {  // NOLINT
    params_.is_qcpx_test_user = RankKconfUtil::qcpxTestUserList()->count(params_.user_id) > 0;
    params_.qcpx_test_user_boost_ratio = RankKconfUtil::qcpxTestUserBoostRatio();
  }

  // 货架商品卡定向发店铺折扣券 @wanpengcheng
  params_.enable_qcpx_photo_shelf_target_store_disc =
    SPDM_enable_qcpx_photo_shelf_target_store_disc(session_data_->get_spdm_ctx());
}

void QcpxPhotoStrategy::InitAdParams(AdCommon* p_ad) {
  // *********** ad 粒度 参数 & 变量 ***********
  params_.inner_qcpx_cause = 0;
  // 搜后推
  params_.inner_qcpx_search_and_push_ratio = p_ad->get_inner_qcpx_search_and_push_ratio();  // NOLINT
  // ad 设置
  params_.ad_queue_type = p_ad->get_ad_queue_type();
  params_.item_type = p_ad->get_item_type();
  params_.ocpx_type = p_ad->get_ocpx_action_type();
  params_.product_price = p_ad->get_product_min_price() * 10;  // 单位厘
  // ROI 调控
  params_.main_ecpm_auto_24h = p_ad->Attr(ItemIdx::fd_CAMPAIGN_main_ecpm_auto_24h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.main_ecpm_24h = p_ad->Attr(ItemIdx::fd_CAMPAIGN_main_ecpm_24h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.qcpx_ecpm_auto_24h = p_ad->Attr(ItemIdx::fd_CAMPAIGN_qcpx_ecpm_auto_24h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.qcpx_ecpm_24h = p_ad->Attr(ItemIdx::fd_CAMPAIGN_qcpx_ecpm_24h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  params_.q_cost_24h = p_ad->Attr(ItemIdx::fd_CAMPAIGN_q_cost_24h).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);  // NOLINT
  // 优惠券设置
  InitCouponParams(p_ad);
}

void QcpxPhotoStrategy::InitCouponParams(AdCommon* p_ad) {
  // *********** ad 粒度 参数 & 变量 ***********
  params_.inner_qcpx_cause = 0;
  // 优惠券设置
  params_.coupon_threshold = 0;
  params_.coupon_amount = 0;
  params_.roi_bound_final = 1.0;
  params_.bid_ratio = 1.0;
  params_.pcvr_ratio = 1.0;
  params_.cpm_ratio = 1.0;
  params_.tmp_max_profit_ecpm_amt = 1.0;
}

bool QcpxPhotoStrategy::Admit(AdCommon* p_ad) {
  bool flag = true;
  // match item_type
  flag &= p_ad->Is(AdFlag::is_photo_ad_inner);  // NOLINT
  // match campaign_type
  flag &= p_ad->Is(AdFlag::is_merchant);
  // match ocpx_action_type
  flag &= p_ad->Is(AdFlag::is_merchant_product);
  flag &= params_.ocpx_type != kuaishou::ad::AD_MERCHANT_T7_ROI;
  // match account type
  flag &= p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP
          || p_ad->get_account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP_MOBILE;
  int32_t product_min_price = params_.product_price + 200;
  int32_t price_coupon_ratio = params_.value_qcpx_photo_price_coupon_ratio;
  flag &= price_coupon_ratio >= 2;  // 满减系数 >= 2
  flag &= product_min_price >= 2 * price_coupon_ratio * 1e3;  // 商品最低价需要大于 最低面额 * 商品券面额系数
  if (!(product_min_price >= 2 * price_coupon_ratio * 1e3)) p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::STRATEGY_MINPRICE);  // NOLINT
  return flag;
}

void QcpxPhotoStrategy::Process(AdCommon* p_ad) {
  InitAdParams(p_ad);
  if (AdmitV3(p_ad)) {
    RunArchStrategyV3(p_ad);
    FillCouponInfoV3(p_ad);
  }
  return;
}

void QcpxPhotoStrategy::Monitor() {
  for (auto it = params_.coupon_delivery_cnt.begin(); it != params_.coupon_delivery_cnt.end(); ++it) {  // NOLINT
    const std::string& item_type = absl::StrCat(it->first);
    const std::string& ad_queue_type = absl::StrCat(params_.ad_queue_type);
    RANK_DOT_COUNT(session_data_, it->second,
      absl::StrCat(this->Name(), ".coupon_delivery_cnt"),
      ad_queue_type, item_type);
  }
}

}  // namespace ad_rank
}  // namespace ks
