#include "teams/ad/ad_rank/processor/filter/filter.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/data/p2p_data/outer_loop_avg_cxr_data/outer_loop_avg_cxr_data.h"

namespace ks {
namespace ad_rank {

double CalCpmThresholdByAdloadV2(
    ContextData* session_data,
    const double target_adload,
    const std::string& dot_key) {

  auto adload_control = RankKconfUtil::adloadControlFollow();
  if (!adload_control) {
    // adload_control 空
    RANK_DOT_COUNT(session_data, 1, dot_key + ".error_adload_control");
    return 0.0;
  }
  const auto app_type = adload_control->data().app_type();

  const std::string& app_id = session_data->get_pos_manager_base().GetRequestAppId();
  const auto p_flow_type = app_type.find(app_id);
  // 只能处理 kconf 存在的 app_type
  if (p_flow_type == app_type.end()) {
    RANK_DOT_COUNT(session_data, 1, dot_key + ".error_app_type", app_id);
    return 0.0;
  }
  const auto flow_type = p_flow_type->second.flow_type();

  const auto p_param_list = flow_type.find(std::to_string(AdEnum::SOFT_AD_QUEUE));
  // 不存在的 SOFT_AD_QUEUE，设置有问题，终止
  if (p_param_list == flow_type.end()) {
    RANK_DOT_COUNT(session_data, 1, dot_key + ".error_flow_type");
    return 0.0;
  }
  const auto param_list = p_param_list->second.param();

  // 处理 kconf 中已经配置的结果，认为结果差 <1e-4 是同一个结果
  for (auto param : param_list) {
    double kconf_target_adload = param.target_adload();
    double kconf_cpm_threshold = param.cpm_threshold();
    if (abs(target_adload - kconf_target_adload) < 1e-4) {
      return kconf_cpm_threshold;
    }
  }
  RANK_DOT_COUNT(session_data, 1, dot_key + ".error_param_not_found");
  return 0.0;
}

std::unordered_set<AdCommon *> GetTopnCvrAd(ContextData* session_data, AdList* ad_list, int topn) {
  bool enable_high_pcvr_add_other_roas =
    SPDM_enable_high_pcvr_add_other_roas(session_data->get_spdm_ctx());
  bool enable_enhance_merchant_mac_user =
    SPDM_enable_enhance_merchant_mac_user(session_data->get_spdm_ctx());
  auto high_pcvr_merchant_mac_user_config =
    RankKconfUtil::highPcvrMerchantMacUserConfig();
  std::string dnc_exp_tag = SPDM_dnc_exp_tag(session_data->get_spdm_ctx());
  auto ctcvr_comparator =
    [&enable_high_pcvr_add_other_roas]
    (const AdCommon *left, const AdCommon *right) -> bool {
    if (left == nullptr || right == nullptr) {
      return false;
    }
    double left_ctcvr = left->get_unify_ctr_info().value * left->get_unify_cvr_info().value;
    double right_ctcvr = right->get_unify_ctr_info().value * right->get_unify_cvr_info().value;
    bool is_left_roas =
      left->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS;
    bool is_right_roas =
      right->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS;
    if (enable_high_pcvr_add_other_roas) {
      is_left_roas = left->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_FANS_TOP_ROI ||
                left->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
                left->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
                left->get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS ||
                left->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI;
      is_right_roas = right->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_FANS_TOP_ROI ||
                right->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
                right->get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS ||
                right->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
                right->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI;
    }
    if (is_left_roas) {
      double cvr_value =
        left->get_order_paid() > 0 ? left->get_order_paid() : left->get_live_order_paid();
      cvr_value =
        cvr_value > 0 ? cvr_value : left->get_c1_order_paied();
      left_ctcvr = left->get_unify_ctr_info().value * cvr_value;
    }
    if (is_right_roas) {
      double cvr_value =
        right->get_order_paid() > 0 ? right->get_order_paid() : right->get_live_order_paid();
      cvr_value =
        cvr_value > 0 ? cvr_value : right->get_c1_order_paied();
      right_ctcvr = right->get_unify_ctr_info().value * cvr_value;
    }
    int64_t left_creative_id = left->get_creative_id();
    int64_t right_creative_id = right->get_creative_id();
    return std::tie(left_ctcvr, left_creative_id) >
      std::tie(right_ctcvr, right_creative_id);
  };
  std::vector<AdCommon *> explore_ad_list;
  std::unordered_set<AdCommon *> explore_topn_creative_ids;
  double min_thd = SPDM_cvr_explore_cvr_min_thd(session_data->get_spdm_ctx());
  // 关注页单独阈值
  if (SPDM_enable_cvr_explore_follow(session_data->get_spdm_ctx()) && session_data->get_is_follow_request()) {
    min_thd = SPDM_cvr_explore_cvr_min_thd_soft_follow(session_data->get_spdm_ctx());
  }
  auto ocpc_white_set = session_data->get_ctcvr_explore_ocpc_white_set();
  auto ocpc_action_type_cvr_thr_map = RankKconfUtil::OcpcActionTypeCvrThrMap();
  if (SPDM_enable_cvr_explore_event(session_data->get_spdm_ctx())) {
    ocpc_white_set = session_data->get_ctcvr_explore_ocpc_white_event_set();
  }
  if (ocpc_white_set == nullptr) {
    return explore_topn_creative_ids;
  }
  int inner_topn = SPDM_high_pvr_skip_cpm_thr_inner_topn_num(session_data->get_spdm_ctx());
  int outer_topn = SPDM_high_pvr_skip_cpm_thr_outer_topn_num(session_data->get_spdm_ctx());
  for (auto ad : ad_list->Ads()) {
    if (ocpc_white_set->find(kuaishou::ad::AdActionType_Name(ad->get_ocpx_action_type()))
        == ocpc_white_set->end()) {
      continue;
    }
    double cvr_value = ad->get_unify_cvr_info().value;
    bool is_roas = ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS;
    if (enable_high_pcvr_add_other_roas) {
      is_roas = ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_FANS_TOP_ROI ||
                ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
                ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
                ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS ||
                ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI;
    }
    if (is_roas) {
      cvr_value =
        ad->get_order_paid() > 0 ? ad->get_order_paid() : ad->get_live_order_paid();
      cvr_value =
        cvr_value > 0 ? cvr_value : ad->get_c1_order_paied();
    }
    if ((cvr_value * ad->get_unify_ctr_info().value) < min_thd) {
      continue;
    }
    explore_ad_list.push_back(ad);
  }
  std::sort(explore_ad_list.begin(), explore_ad_list.end(), ctcvr_comparator);
  topn = static_cast<int>(explore_ad_list.size());
  // 电商 mac 交集人群提高内循环跳过个数
  auto buyer_effective_type =
    session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
  const std::string& merchant_mac_user_key =
    absl::Substitute("$0_$1_$2", dnc_exp_tag, buyer_effective_type, "topn");
  if (enable_enhance_merchant_mac_user) {
    if (high_pcvr_merchant_mac_user_config->data().enhance_pcvr_config().count(merchant_mac_user_key)
        > 0) {
      inner_topn = static_cast<int>(
            high_pcvr_merchant_mac_user_config->data().enhance_pcvr_config().at(merchant_mac_user_key));;
    }
  }
  for (int i = 0; i < topn; i++) {
    if (outer_topn < 1 && inner_topn < 1) {
      break;
    }
    if (explore_ad_list[i]->Is(AdFlag::is_outer_loop_ad)) {
      if (outer_topn > 0) {
        outer_topn -= 1;
      } else {
        continue;
      }
    } else {
      if (inner_topn > 0) {
        inner_topn -= 1;
      } else {
        continue;
      }
    }
    explore_topn_creative_ids.insert(explore_ad_list[i]);
  }
  return explore_topn_creative_ids;
}

bool GetLiveInnerCpmThr(ContextData* session_data, double* cpm_thr, const std::string& queue) {
  int64_t sub_page_id = session_data->get_pos_manager_base().GetSubPageId();
  if (!ad_base::IsLiveInnerExplore(sub_page_id)) {
    return false;
  }
  const std::string exp_tag = SPDM_live_inner_thr_exp_tag(session_data->get_spdm_ctx());
  const std::string u_level =
      session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
  const std::string key = absl::Substitute("$0_$1_$2_$3", exp_tag, sub_page_id, u_level, queue);
  const auto &config = RankKconfUtil::LiveInnerCpmThrMap();
  if (config != nullptr) {
    auto iter = config->find(key);
    if (iter != config->end()) {
      *cpm_thr = iter->second;
      return true;
    }
  }
  return false;
}

bool SetEcpmThresforOuterLoopDncOpt(ContextData* session_data,
                                    AdCommon* ad, const std::string& w_level,
                                    bool is_target_w_level,
                                    bool enable_strategy_for_potential_nc,
                                    bool enable_outer_loop_dynamic_cpm_thres,
                                    bool outer_loop_dnc_hc_new_key_prefix,
                                    const std::string& outer_loop_dnc_opt_dimension,
                                    double outer_loop_dynamic_cpm_thres_alpha,
                                    double outer_loop_dynamic_cpm_thres_min) {
  if (!ad->Is(AdFlag::is_outer_loop_ad)) {
    return false;
  }
  if ((!enable_outer_loop_dynamic_cpm_thres && !enable_strategy_for_potential_nc)
      || !is_target_w_level) {
    return false;
  }
  const std::string queue_type = kuaishou::ad::AdEnum::AdQueueType_Name(ad->get_ad_queue_type());
  const std::string industry_id_v3 = std::to_string(ad->get_industry_id_v3());
  const std::string industry_parent_id_v3 = std::to_string(ad->get_industry_parent_id_v3());
  const std::string product_name = ad->get_product_name();
  const std::string& ocpx_action_type =
    kuaishou::ad::AdActionType_Name(ad->get_ocpx_action_type());
  std::string key_prefix = absl::StrCat(ocpx_action_type, "_", w_level);
  if (outer_loop_dnc_opt_dimension == "industry") {
    if (outer_loop_dnc_hc_new_key_prefix) {
      key_prefix = absl::StrCat(key_prefix, "_", industry_id_v3);
    } else {
      key_prefix = absl::StrCat(key_prefix, "_", industry_parent_id_v3, "_", industry_id_v3);
    }
  } else if (outer_loop_dnc_opt_dimension == "product") {
    key_prefix = absl::StrCat(key_prefix, "_", product_name);
  } else {
    RANK_DOT_STATS(session_data, 1,
                  "outer_dnc_opt_cpm_thr_dynamic", queue_type, "invalid_dimension");
    return false;
  }
  double pctcvr = ad->get_unify_ctr_info().value * ad->get_unify_cvr_info().value;
  double base_cxr = 1.0;
  auto* p2p_avg_cxr_loader = OuterLoopAvgCxrData::GetInstance();
  if (p2p_avg_cxr_loader != nullptr && p2p_avg_cxr_loader->IsDataReady()) {
    bool ret = OuterLoopAvgCxrData::GetInstance()->GetAvgCxr(key_prefix, &base_cxr);
    if (!ret) {
      RANK_DOT_STATS(session_data, 1,
        "outer_dnc_opt_cpm_thr_dynamic", queue_type, "failed_to_get_avg_cxr", key_prefix);
      return false;
    }
  } else {
    RANK_DOT_STATS(session_data, 1,
                    "outer_dnc_opt_cpm_thr_dynamic", queue_type, "failed_to_load_p2p_data");
    return false;
  }
  double base_cpm_threshold = ad->get_cpm_thr();
  base_cpm_threshold /= kBenifitFactor;  // 单位对齐
  double cpm_thres_max = base_cpm_threshold;
  if (pctcvr <= 0 || pctcvr >= 1 || base_cxr <= 0 || base_cxr >=1) {
    return false;
  }
  RANK_DOT_STATS(session_data, base_cpm_threshold * 1000,
                  "outer_dnc_opt_cpm_thr_dynamic", queue_type, w_level, "ori_thres");
  base_cpm_threshold *=
   std::pow((2 - 2 / (1 + std::exp(0 - pctcvr / base_cxr))), outer_loop_dynamic_cpm_thres_alpha);
  base_cpm_threshold = std::max(base_cpm_threshold, outer_loop_dynamic_cpm_thres_min);
  base_cpm_threshold = std::min(base_cpm_threshold, cpm_thres_max);
  ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_DNC_OPT_CPM_THR, true);
  RANK_DOT_STATS(session_data, base_cpm_threshold * 1000,
                  "outer_dnc_opt_cpm_thr_dynamic", queue_type, w_level, "new_thres");
  return true;
}

bool SetEcpmThresforOuterLoopI2IEnhance(ContextData* session_data,
                                        AdCommon* ad,
                                        bool enable_wechat_mini_game_i2i_enhance,
                                        bool enable_outerloop_cross_ind_i2i_enhance,
                                        bool in_ocpx_white_set,
                                        bool in_ac_enhance_retrieve_set,
                                        double wechat_mini_game_cpm_thres,
                                        double outerloop_cross_ind_cpm_thres) {
  if (!ad->Is(AdFlag::is_outer_loop_ad) || !in_ocpx_white_set) {
    return false;
  }
  double base_cpm_threshold = ad->get_cpm_thr();
  if (enable_wechat_mini_game_i2i_enhance &&
      (((ad->get_landing_page_component() & 1) == 1 ||
        (ad->get_landing_page_component() & 2) == 2) &&
       ad->get_industry_parent_id_v3() == 1018)) {
    base_cpm_threshold = wechat_mini_game_cpm_thres;
  } else if (enable_outerloop_cross_ind_i2i_enhance || in_ac_enhance_retrieve_set) {
    base_cpm_threshold = outerloop_cross_ind_cpm_thres;
  } else {
    return false;
  }
  const std::string queue_type = kuaishou::ad::AdEnum::AdQueueType_Name(ad->get_ad_queue_type());
  RANK_DOT_STATS(session_data, base_cpm_threshold * 1000,
                 "outer_i2i_enhance_cpm_thres", queue_type, "new_thres");
  ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_DNC_OPT_CPM_THR, true);
  return true;
}

bool SetEcpmThresforOuterloopNcMax(ContextData* session_data, AdCommon* ad,
                                   bool enable_outerloop_nc_max_cpm_thres,
                                   double outerloop_nc_max_cpm_thres,
                                   bool enable_outerloop_low_active_skip_duanju,
                                   int is_outerloop_low_active_ac,
                                   bool enable_outerloop_low_active_ocpx_blackset,
                                   bool enable_outerloop_nc_max_user_limit) {
  if (!ad->Is(AdFlag::is_outer_loop_ad)) {
    return false;
  }
  if (enable_outerloop_low_active_skip_duanju && is_outerloop_low_active_ac > 0 &&
      ad->get_second_industry_id_v5() == 2012) {
    RANK_DOT_STATS(session_data, 1, "outerloop_ac_max_cpm_thres", "duanju_filter");
    return false;
  }
  auto ocpx_blackset = RankKconfUtil::outerloopLowActiveOcpxBlackSet();
  if (enable_outerloop_low_active_ocpx_blackset && is_outerloop_low_active_ac > 0 &&
      ocpx_blackset && ocpx_blackset->count(ad->get_ocpx_action_type()) > 0) {
    RANK_DOT_STATS(session_data, 1, "outerloop_ac_max_cpm_thres", "ocpx_filter");
    return false;
  }
  int64_t is_outerloop_top_ctcvr =
    ad->Attr(ItemIdx::is_outerloop_top_ctcvr).GetIntValue(ad->AttrIndex()).value_or(0);
  if (!enable_outerloop_nc_max_cpm_thres || is_outerloop_top_ctcvr <= 0) {
    return false;
  }
  bool is_potential_nc_user =
      session_data->get_rank_request()->is_outerloop_potential_nc_user();
  if (enable_outerloop_nc_max_user_limit && !is_potential_nc_user &&
      is_outerloop_low_active_ac <= 0) {
    RANK_DOT_STATS(session_data, 1, "outerloop_ac_max_cpm_thres", "not_goal_user");
    return false;
  }
  const std::string queue_type = kuaishou::ad::AdEnum::AdQueueType_Name(ad->get_ad_queue_type());
  RANK_DOT_STATS(session_data, ad->get_cpm_thr() * 1000,
                 "outerloop_nc_max_cpm_thres", queue_type, "ori_thres");
  RANK_DOT_STATS(session_data, outerloop_nc_max_cpm_thres * 1000,
                 "outerloop_nc_max_cpm_thres", queue_type, "new_thres");
  ad->SetCpmThr(outerloop_nc_max_cpm_thres, CpmThrFromTag::OUTER_DNC_OPT_CPM_THR, true);
  return true;
}

double GetWanheCpmThr(ContextData* session_data) {
  // 磁力万合软硬广门槛统一, 软广部分 cpm 门槛
  double wanhe_soft_cpm_thr = 0.0;
  if (session_data->get_pos_manager_base().IsWanhe()) {
    wanhe_soft_cpm_thr = SPDM_side_window_cpm_thr(session_data->get_spdm_ctx());
    if (session_data->get_pos_manager_base().IsMainSideWindow()) {
      if (SPDM_enable_main_side_window_cpm_thr(session_data->get_spdm_ctx())) {
        wanhe_soft_cpm_thr = SPDM_main_side_window_cpm_thr(session_data->get_spdm_ctx());
      }
    } else if (session_data->get_pos_manager_base().IsProfileSideWindow()) {
      if (SPDM_enable_profile_side_window_cpm_thr(session_data->get_spdm_ctx())) {
        wanhe_soft_cpm_thr = SPDM_profile_side_window_cpm_thr(session_data->get_spdm_ctx());
      }
    }
    const std::string& tag_key =
            SPDM_wanhe_charge_action_type_adjust_cpm_thr_tag(session_data->get_spdm_ctx());
    double cpm_th_ratio = 1.0;
    int64 sub_page_id = session_data->get_pos_manager_base().GetSubPageId();
    auto cpmThRatio = RankKconfUtil::wanheCpmThRatio();
    if (cpmThRatio != nullptr) {
      auto exp_ratio_conf = cpmThRatio->data().exp_subpageid_conf().find(tag_key);
      if (exp_ratio_conf != cpmThRatio->data().exp_subpageid_conf().end()) {
        auto sub_page_ratio_conf = exp_ratio_conf->second.subpageid_ratio().find(sub_page_id);
        if (sub_page_ratio_conf != exp_ratio_conf->second.subpageid_ratio().end()) {
          cpm_th_ratio = sub_page_ratio_conf->second;
        }
      }
    }
    wanhe_soft_cpm_thr = wanhe_soft_cpm_thr * cpm_th_ratio;
  }
  return wanhe_soft_cpm_thr;
}

void EcpmFilter(ContextData* session_data, AdList* ad_list, const FilterInfo& filter_info) {
  if (session_data->get_is_incentive() &&
      SPDM_enable_incentive_skip_ecpm_filter(session_data->get_spdm_ctx()) &&
      !(session_data->get_pos_manager_base().IsInspireMerchant() &&
        SPDM_enable_incentive_merchant_ecpm_filter(session_data->get_spdm_ctx()))) {
    return;
  }
  int64 explore_max_creative_id = 0;
  bool is_non_conv_user = false;
  bool enable_feed_explore_load_control_move_last =
    SPDM_enable_feed_explore_load_control_move_last(session_data->get_spdm_ctx());
  bool enable_cvr_explore_skip_soft_cpm_thr =
    SPDM_enable_cvr_explore_skip_soft_cpm_thr(session_data->get_spdm_ctx());
  bool enable_feed_explore_retain_load_control_thr =
    SPDM_enable_feed_explore_retain_load_control_thr(session_data->get_spdm_ctx());
  bool enable_feed_explore_rb_thr =
    SPDM_enable_feed_explore_rb_thr(session_data->get_spdm_ctx());
  bool enable_inner_explore_cpm_thr_new_score =
    SPDM_enable_inner_explore_cpm_thr_new_score(session_data->get_spdm_ctx());
  bool is_feed_explore = ks::ad_base::IsFeedExploreRequest(session_data->get_sub_page_id());
  bool is_inner_explore = ks::ad_base::IsInnerExploreRequest(session_data->get_sub_page_id());
  std::unordered_set<AdCommon *> explore_topn_creative_ids;
  bool enable_cvr_explore_inner = SPDM_enable_cvr_explore_inner(session_data->get_spdm_ctx());
  bool enable_cvr_explore_follow =
    SPDM_enable_cvr_explore_follow(session_data->get_spdm_ctx());
  if (enable_cvr_explore_skip_soft_cpm_thr &&
      !session_data->get_is_thanos_mix_request() &&
      !(enable_cvr_explore_inner && session_data->get_is_explore_feed_inner())) {
    enable_cvr_explore_skip_soft_cpm_thr = false;
  }
  // 关注页生效 pcvr 通道
  if (enable_cvr_explore_follow && session_data->get_is_follow_request()) {
    enable_cvr_explore_skip_soft_cpm_thr = true;
  }

  if (enable_cvr_explore_skip_soft_cpm_thr) {
    explore_topn_creative_ids = GetTopnCvrAd(
        session_data, ad_list, SPDM_explore_skip_cpm_thr_topn_num(session_data->get_spdm_ctx()));
    int64 non_conv_user_tag = SPDM_non_conv_user_cvr_skip_thr_tag(session_data->get_spdm_ctx());
    if ((session_data->get_rank_request()->ad_request().non_conv_user_tag() & non_conv_user_tag) == 0) {
      is_non_conv_user = true;
    }
  }
  bool enhance_force_reco = SPDM_enhance_force_reco(session_data->get_spdm_ctx());
  bool enable_cvr_explore_skip_cpm_thr_for_dac =
    SPDM_enable_cvr_explore_skip_cpm_thr_for_dac(session_data->get_spdm_ctx());
  std::string dnc_exp_tag = SPDM_dnc_exp_tag(session_data->get_spdm_ctx());
  // 调整 cpm 门槛值的系数
  double ad_rank_cpm_filter_threshold_factor = 1.0;
  double ad_rank_cpm_filter_threshold_factor_soft_inner_gamora =
      SPDM_ad_rank_cpm_filter_threshold_factor_soft_inner_gamora(session_data->get_spdm_ctx());
  double ad_rank_cpm_filter_threshold_factor_soft_inner_nebula =
      SPDM_ad_rank_cpm_filter_threshold_factor_soft_inner_nebula(session_data->get_spdm_ctx());
  double ad_rank_cpm_filter_threshold_factor_soft_outer_gamora =
      SPDM_ad_rank_cpm_filter_threshold_factor_soft_outer_gamora(session_data->get_spdm_ctx());
  double ad_rank_cpm_filter_threshold_factor_soft_outer_nebula =
      SPDM_ad_rank_cpm_filter_threshold_factor_soft_outer_nebula(session_data->get_spdm_ctx());
  int32 user_age = session_data->get_rank_request()->ad_request().ad_user_info().age();
  auto follow_inner_crowd_filter = RankKconfUtil::followInnerCrowdFilter();
  float user_phone_price = session_data->get_rank_request()->ad_request().ad_user_info().phone_price();
  int32 user_city = session_data->get_rank_request()->ad_request().ad_user_info().adcode().city();
  absl::flat_hash_set<std::string> user_app_package;
  for (const auto& device_info : session_data->get_rank_request()->ad_request().ad_user_info().device_info()) {   // NOLINT
    for (const std::string& app_package : device_info.app_package()) {
      user_app_package.insert(app_package);
    }
    for (const std::string& app_package : device_info.app_package_std()) {
      user_app_package.insert(app_package);
    }
  }
  bool is_follow_inner_crowd_install_spec_app = false;
  if (follow_inner_crowd_filter != nullptr) {
    for (auto& [app_package, useless] : follow_inner_crowd_filter->data().app_package()) {
      if (user_app_package.count(app_package) > 0) {
        is_follow_inner_crowd_install_spec_app = true;
        break;
      }
    }
  }
  double follow_cpm_threshold_yuan_ = SPDM_follow_cpm_threshold(session_data->get_spdm_ctx());
  std::string nebula_follow_live_inner_cpm_thr_exptag =
    SPDM_nebula_follow_live_inner_cpm_thr_exptag(session_data->get_spdm_ctx());
  bool enable_nebula_follow_live_inner_cpm_thr_u0potentialrisk =
    SPDM_enable_nebula_follow_live_inner_cpm_thr_u0potentialrisk(session_data->get_spdm_ctx());
  bool enable_nebula_follow_live_inner_cpm_thr_u0silent =
    SPDM_enable_nebula_follow_live_inner_cpm_thr_u0silent(session_data->get_spdm_ctx());
  bool enable_nebula_follow_live_inner_cpm_thr_u1 =
    SPDM_enable_nebula_follow_live_inner_cpm_thr_u1(session_data->get_spdm_ctx());
  bool enable_nebula_follow_live_inner_cpm_thr_u2 =
    SPDM_enable_nebula_follow_live_inner_cpm_thr_u2(session_data->get_spdm_ctx());
  bool enable_nebula_follow_live_inner_cpm_thr_u3 =
    SPDM_enable_nebula_follow_live_inner_cpm_thr_u3(session_data->get_spdm_ctx());
  bool enable_nebula_follow_live_inner_cpm_thr_u4 =
    SPDM_enable_nebula_follow_live_inner_cpm_thr_u4(session_data->get_spdm_ctx());
  bool enable_nebula_follow_live_inner_cpm_thr_u4plus =
    SPDM_enable_nebula_follow_live_inner_cpm_thr_u4plus(session_data->get_spdm_ctx());
  double nebula_follow_live_inner_cpm_thr_u0potentialrisk =
    SPDM_nebula_follow_live_inner_cpm_thr_u0potentialrisk(session_data->get_spdm_ctx());
  double nebula_follow_live_inner_cpm_thr_u0silent =
    SPDM_nebula_follow_live_inner_cpm_thr_u0silent(session_data->get_spdm_ctx());
  double nebula_follow_live_inner_cpm_thr_u1 =
    SPDM_nebula_follow_live_inner_cpm_thr_u1(session_data->get_spdm_ctx());
  double nebula_follow_live_inner_cpm_thr_u2 =
    SPDM_nebula_follow_live_inner_cpm_thr_u2(session_data->get_spdm_ctx());
  double nebula_follow_live_inner_cpm_thr_u3 =
    SPDM_nebula_follow_live_inner_cpm_thr_u3(session_data->get_spdm_ctx());
  double nebula_follow_live_inner_cpm_thr_u4 =
    SPDM_nebula_follow_live_inner_cpm_thr_u4(session_data->get_spdm_ctx());
  double nebula_follow_live_inner_cpm_thr_u4plus =
    SPDM_nebula_follow_live_inner_cpm_thr_u4plus(session_data->get_spdm_ctx());

  std::string follow_ecpm_threshold_init_tag = SPDM_follow_ecpm_threshold_init_tag(session_data->get_spdm_ctx());  // NOLINT
  bool enable_recover_follow_cpm_thr = SPDM_enable_recover_follow_cpm_thr(session_data->get_spdm_ctx());  // NOLINT
  double follow_ecpm_threshold_init = SPDM_follow_ecpm_threshold_init(session_data->get_spdm_ctx());  // NOLINT
  bool enable_recover_follow_gamoraouter_cpm_thr = SPDM_enable_recover_follow_gamoraouter_cpm_thr(session_data->get_spdm_ctx());  // NOLINT
  double follow_ecpm_threshold_init_gamoraouter = SPDM_follow_ecpm_threshold_init_gamoraouter(session_data->get_spdm_ctx());  // NOLINT
  std::string follow_ecpm_threshold_init_tag_2 = SPDM_follow_ecpm_threshold_init_tag_2(session_data->get_spdm_ctx());  // NOLINT
  bool enable_recover_follow_cpm_thr_2 = SPDM_enable_recover_follow_cpm_thr_2(session_data->get_spdm_ctx());  // NOLINT
  double follow_ecpm_threshold_init_2 = SPDM_follow_ecpm_threshold_init_2(session_data->get_spdm_ctx());  // NOLINT
  bool enable_recover_follow_gamoraouter_cpm_thr_2 = SPDM_enable_recover_follow_gamoraouter_cpm_thr_2(session_data->get_spdm_ctx());  // NOLINT
  double follow_ecpm_threshold_init_gamoraouter_2 = SPDM_follow_ecpm_threshold_init_gamoraouter_2(session_data->get_spdm_ctx());  // NOLINT

  bool enable_nearby_local_ecpm_switch = SPDM_enable_nearby_local_ecpm_switch(session_data->get_spdm_ctx());  // NOLINT
  bool enable_nearby_ecpm_open_switch = SPDM_enable_nearby_ecpm_open_switch(session_data->get_spdm_ctx());  // NOLINT
  bool enable_nearby_ecpm_config_strategy = SPDM_enable_nearby_ecpm_config_strategy(session_data->get_spdm_ctx());  // NOLINT
  std::string nearby_ecpm_config_strategy_tag = SPDM_nearby_ecpm_config_strategy_tag(session_data->get_spdm_ctx());  // NOLINT
  double nearby_local_soft_ecpm = SPDM_nearbyLocalSoftEcpmThreshold();
  bool enable_nearby_ecpm_out_thr = SPDM_enable_nearby_ecpm_out_thr(session_data->get_spdm_ctx());
  double nearby_local_ecpm_out = SPDM_nearby_local_ecpm_out(session_data->get_spdm_ctx());
  bool enable_nearby_user_group_cpm_strategy = SPDM_enable_nearby_user_group_cpm_strategy(session_data->get_spdm_ctx());  // NOLINT
  double nearby_user_group_cpm_thr = SPDM_nearby_user_group_cpm_thr(session_data->get_spdm_ctx());  // NOLINT

  auto set_follow_ecpm_threshold = [&] (AdCommon *ad) {
    double follow_cpm_threshold_yuan = follow_cpm_threshold_yuan_;

    if (session_data->get_sub_page_id() == ks::ad_base::AdSubPageId::kNebulaFollowLiveInner) {
      const std::string & user_utype =
        session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
      const std::string & expTag = nebula_follow_live_inner_cpm_thr_exptag;
      if (!session_data->get_rank_request()->ad_request().ad_user_info().has_buyer_effective_type()
        && enable_nebula_follow_live_inner_cpm_thr_u0potentialrisk) {
        ad->SetCpmThr(nebula_follow_live_inner_cpm_thr_u0potentialrisk,
          CpmThrFromTag::NEBULA_FOLLOW_LIVE_INNER_CPM_THR, true);
        RANK_DOT_STATS(session_data, 1, "kNebulaFollowLiveInnerCPMThr", expTag, user_utype);
        return;
      } else if (user_utype == "U0" && enable_nebula_follow_live_inner_cpm_thr_u0silent) {
        ad->SetCpmThr(nebula_follow_live_inner_cpm_thr_u0silent,
          CpmThrFromTag::NEBULA_FOLLOW_LIVE_INNER_CPM_THR, true);
        RANK_DOT_STATS(session_data, 1, "kNebulaFollowLiveInnerCPMThr", expTag, user_utype);
        return;
      } else if (user_utype == "U1" && enable_nebula_follow_live_inner_cpm_thr_u1) {
        ad->SetCpmThr(nebula_follow_live_inner_cpm_thr_u1,
          CpmThrFromTag::NEBULA_FOLLOW_LIVE_INNER_CPM_THR, true);
        RANK_DOT_STATS(session_data, 1, "kNebulaFollowLiveInnerCPMThr", expTag, user_utype);
        return;
      } else if (user_utype == "U2" && enable_nebula_follow_live_inner_cpm_thr_u2) {
        ad->SetCpmThr(nebula_follow_live_inner_cpm_thr_u2,
          CpmThrFromTag::NEBULA_FOLLOW_LIVE_INNER_CPM_THR, true);
        RANK_DOT_STATS(session_data, 1, "kNebulaFollowLiveInnerCPMThr", expTag, user_utype);
        return;
      } else if (user_utype == "U3" && enable_nebula_follow_live_inner_cpm_thr_u3) {
        ad->SetCpmThr(nebula_follow_live_inner_cpm_thr_u3,
          CpmThrFromTag::NEBULA_FOLLOW_LIVE_INNER_CPM_THR, true);
        RANK_DOT_STATS(session_data, 1, "kNebulaFollowLiveInnerCPMThr", expTag, user_utype);
        return;
      } else if (user_utype == "U4" && enable_nebula_follow_live_inner_cpm_thr_u4) {
        ad->SetCpmThr(nebula_follow_live_inner_cpm_thr_u4,
          CpmThrFromTag::NEBULA_FOLLOW_LIVE_INNER_CPM_THR, true);
        RANK_DOT_STATS(session_data, 1, "kNebulaFollowLiveInnerCPMThr", expTag, user_utype);
        return;
      } else if (user_utype == "U4+" && enable_nebula_follow_live_inner_cpm_thr_u4plus) {
        ad->SetCpmThr(nebula_follow_live_inner_cpm_thr_u4plus,
          CpmThrFromTag::NEBULA_FOLLOW_LIVE_INNER_CPM_THR, true);
        RANK_DOT_STATS(session_data, 1, "kNebulaFollowLiveInnerCPMThr", expTag, user_utype);
        return;
      }
    }

    if (enable_recover_follow_cpm_thr) {
      ad->SetCpmThr(follow_ecpm_threshold_init, CpmThrFromTag::FOLLOW_RECOVER_CPM_THR, true);
      RANK_DOT_STATS(session_data, 1, "recover_follow_cpm_thr", follow_ecpm_threshold_init_tag, "all");  // NOLINT
      return;
    }
    if (enable_recover_follow_cpm_thr_2) {
      ad->SetCpmThr(follow_ecpm_threshold_init_2, CpmThrFromTag::FOLLOW_RECOVER_CPM_THR, true);
      RANK_DOT_STATS(session_data, 1, "recover_follow_cpm_thr_2", follow_ecpm_threshold_init_tag_2, "all");  // NOLINT
      return;
    }
    if (session_data->get_sub_page_id() == ks::ad_base::AdSubPageId::kFollowFeedTop || session_data->get_sub_page_id() == ks::ad_base::AdSubPageId::kNebulaFollowOuter) {  // NOLINT
      if (enable_recover_follow_gamoraouter_cpm_thr) {
        ad->SetCpmThr(follow_ecpm_threshold_init_gamoraouter, CpmThrFromTag::FOLLOW_RECOVER_CPM_THR, true);
        RANK_DOT_STATS(session_data, 1, "recover_follow_cpm_thr", follow_ecpm_threshold_init_tag, "outer");
        return;
      }
      if (enable_recover_follow_gamoraouter_cpm_thr_2) {
        ad->SetCpmThr(follow_ecpm_threshold_init_gamoraouter_2, CpmThrFromTag::FOLLOW_RECOVER_CPM_THR, true);  // NOLINT
        RANK_DOT_STATS(session_data, 1, "recover_follow_cpm_thr_2", follow_ecpm_threshold_init_tag_2, "outer");  // NOLINT
        return;
      }
    }

    ad->SetCpmThr(follow_cpm_threshold_yuan, CpmThrFromTag::FOLLOW_TAB_CPM_THR, true);
  };
  bool enable_unify_explore_cpm_native_ad =
    SPDM_enable_unify_explore_cpm_native_ad(session_data->get_spdm_ctx());
  bool enable_unify_explore_cpm_native_ad_inner_explore =
    SPDM_enable_unify_explore_cpm_native_ad_inner_explore(session_data->get_spdm_ctx());
  double unify_explore_inner_cpm_native_ad =
    SPDM_unify_explore_inner_cpm_native_ad(session_data->get_spdm_ctx());
  double unify_explore_feed_cpm_native_ad =
    SPDM_unify_explore_feed_cpm_native_ad(session_data->get_spdm_ctx());
  bool enable_inner_explore_live_cpm_threshold =
      SPDM_enable_inner_explore_live_cpm_threshold(session_data->get_spdm_ctx());
  double inner_explore_live_cpm_threshold_soft =
      SPDM_inner_explore_live_cpm_threshold_soft(session_data->get_spdm_ctx());
  auto set_explore_unify_ecpm_threshold = [&] (AdCommon *ad) {
    if (!enable_unify_explore_cpm_native_ad) {
      if (!session_data->get_is_explore_feed_inner()) {
        return;
      }
      if (!enable_unify_explore_cpm_native_ad_inner_explore && session_data->get_is_explore_feed_inner()) {
        return;
      }
    }
    if (!(session_data->get_is_feed() && session_data->get_pos_manager_base().GetSubPageId() == 10002001)
            && !(session_data->get_is_explore_feed_inner())) {
      return;
    }
    // 发现页内外流只保留 固定门槛 +  人群 w 分层门槛调整
    double user_value_group_ratio = 1.0;
    double explore_cpm_thr = 1.0;  // 单位: 元
    if (session_data->get_is_explore_feed_inner()) {
      user_value_group_ratio = session_data->get_explore_inner_user_value_ratio();
      explore_cpm_thr = unify_explore_inner_cpm_native_ad;
      if (enable_inner_explore_live_cpm_threshold && inner_explore_live_cpm_threshold_soft > 0 &&
          ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
        explore_cpm_thr = inner_explore_live_cpm_threshold_soft;
      }
    } else {
      user_value_group_ratio = session_data->get_explore_feed_user_value_group_ratio();
      explore_cpm_thr = unify_explore_feed_cpm_native_ad;
    }
    if (enable_feed_explore_retain_load_control_thr && session_data->get_is_explore_feed_inner()) {
      explore_cpm_thr = std::max(ad->get_adload_fixed_cpm_thr(), explore_cpm_thr);
    }
    RANK_DOT_STATS(session_data, explore_cpm_thr * user_value_group_ratio * 1000,
            "explore_feed_inner_unify_native_cpm_thr");
    ad->SetCpmThr(explore_cpm_thr * user_value_group_ratio, CpmThrFromTag::EXPLORE_FEED_SOFT_CPM_THR, true);
    if (enable_feed_explore_load_control_move_last && session_data->get_is_explore_feed_inner()) {
      ad->SetCpmThr(ad->get_adload_fixed_cpm_thr() * user_value_group_ratio,
          CpmThrFromTag::EXPLORE_FEED_SOFT_CPM_THR, true);
    }
  };
  double sctr_into_cpm_discount_ratio_follow =
    SPDM_sctr_into_cpm_discount_ratio_follow(session_data->get_spdm_ctx());
  double feed_native_cpm_threshold_yuan = 4.0;
  double native_cpm_threshold_yuan = session_data->get_spdm_ctx().TryGetDouble(
      "native_cpm_threshold_yuan", 5.0);
  double native_nebula_cpm_threshold_yuan = session_data->get_spdm_ctx().TryGetDouble(
      "native_nebula_cpm_threshold_yuan", 5.0);
  double non_mix_thanos_native_cpm_threshold_yuan =
      SPDM_non_mix_thanos_native_cpm_threshold_yuan(session_data->get_spdm_ctx());
  double explore_feed_soft_cpm_threshold_yuan =
      SPDM_explore_feed_soft_cpm_threshold_yuan(session_data->get_spdm_ctx());
  double explore_feed_soft_live_cpm_thr =
      SPDM_explore_feed_soft_live_cpm_thr(session_data->get_spdm_ctx());
  double main_side_window_ratio = SPDM_main_side_window_soft_cpm_ratio(session_data->get_spdm_ctx());
  double profile_side_window_ratio = SPDM_profile_side_window_soft_cpm_ratio(session_data->get_spdm_ctx());
  double profile_skin_ratio = SPDM_profile_skin_soft_cpm_ratio(session_data->get_spdm_ctx());
  double wanhe_soft_cpm_thr = GetWanheCpmThr(session_data);
  double nobid_fast_cpm_threshold = SPDM_nobid_fast_cpm_threshold(session_data->get_spdm_ctx());
  double guess_you_like_cpm_threshold_yuan = SPDM_guess_you_like_cpm_threshold_yuan(session_data->get_spdm_ctx());    // NOLINT
  double buyer_homepage_soft_queue_cpm_thr = SPDM_buyer_homepage_soft_queue_cpm_thr(session_data->get_spdm_ctx());    // NOLINT
  bool enable_buyer_home_page_soft_queue_new_cpm_thr =
      SPDM_enable_buyer_home_page_soft_queue_new_cpm_thr(session_data->get_spdm_ctx());
  double buyer_home_page_soft_queue_new_cpm_thr =
      SPDM_buyer_home_page_soft_queue_new_cpm_thr(session_data->get_spdm_ctx());
  bool enable_mall_soft_queue_cpm_thr = SPDM_enable_mall_soft_queue_cpm_thr(session_data->get_spdm_ctx());
  double mall_soft_queue_cpm_thr = SPDM_mall_soft_queue_cpm_thr(session_data->get_spdm_ctx());
  bool enable_inner_explore_cpm_thr_ratio = SPDM_enable_inner_explore_cpm_thr_ratio(session_data->get_spdm_ctx());    // NOLINT
  double inner_explore_cpm_thr_ratio = SPDM_inner_explore_cpm_thr_ratio(session_data->get_spdm_ctx());
  double ecpm_threshold_explore_inner_native =
          SPDM_ecpm_threshold_explore_inner_native(session_data->get_spdm_ctx());
  // 发现页内流 uplift 门槛调优 且不跳过软广
  if ((!SPDM_enable_skip_soft_uplift_score(session_data->get_spdm_ctx()))
      && SPDM_enable_explore_inner_uplift_score(session_data->get_spdm_ctx())) {
    double user_uplift_score = session_data->get_rank_request()->ad_request().inner_explore_lift_score();
    double explore_inner_uplift_score_thr = SPDM_explore_inner_uplift_score_thr(session_data->get_spdm_ctx());
    if (user_uplift_score > explore_inner_uplift_score_thr) {
      double uplift_ratio = SPDM_explore_inner_uplift_score_ratio(session_data->get_spdm_ctx());
      ecpm_threshold_explore_inner_native *= uplift_ratio;
    }
  }
  // 内流软广人群系数调整
  if (SPDM_enable_explore_inner_user_value_soft_ad_adjust(session_data->get_spdm_ctx())) {
    ecpm_threshold_explore_inner_native *= session_data->get_explore_inner_user_value_ratio();
  }
  bool enable_fanstop_cpm_new_threshold_yuan =
      SPDM_enable_fanstop_cpm_new_threshold_yuan(session_data->get_spdm_ctx());
  std::shared_ptr<::ks::infra::TailNumberV2> fanstop_cpm_threshold_exp_tail_set_ =
      RankKconfUtil::fanstopCpmThresholdExpTailSet();
  std::shared_ptr<absl::flat_hash_set<int32_t>> nearby_local_industry_whitelist =
      RankKconfUtil::nearbyIndustryList();
  auto nearby_local_filter_ocpx_whitelist =  RankKconfUtil::nearbyFilterOcpxSet();
  auto nearby_local_filter_page_id_whitelist =  RankKconfUtil::nearbyFilterPageId();
  double fanstop_cpm_new_threshold_yuan = SPDM_fanstop_cpm_new_threshold_yuan(session_data->get_spdm_ctx());
  double feed_brand_fanstop_cpm_new_threshold_yuan =
      SPDM_feed_brand_fanstop_cpm_new_threshold_yuan(session_data->get_spdm_ctx());
  double brand_fanstop_cpm_new_threshold_yuan =
      SPDM_brand_fanstop_cpm_new_threshold_yuan(session_data->get_spdm_ctx());
  double feed_speed_fanstop_cpm_new_threshold_yuan =
      SPDM_feed_speed_fanstop_cpm_new_threshold_yuan(session_data->get_spdm_ctx());
  double speed_fanstop_cpm_new_threshold_yuan =
      SPDM_speed_fanstop_cpm_new_threshold_yuan(session_data->get_spdm_ctx());
  double account_id_cpm_thr_ratio_soft_v2 =
      SPDM_account_id_cpm_thr_ratio_soft_v2(session_data->get_spdm_ctx());
  bool enable_pay_mode_account_cpm_thr =
      SPDM_enable_pay_mode_account_cpm_thr(session_data->get_spdm_ctx());
  std::shared_ptr<absl::flat_hash_set<int64_t>> juxing_supplement_exp_account =
      RankKconfUtil::juxingSupplementExpAccount();
  int64_t pay_mode_tag = SPDM_pay_mode_tag(session_data->get_spdm_ctx());
  double pay_mode_account_cpm_thr = SPDM_pay_mode_account_cpm_thr(session_data->get_spdm_ctx());
  double unlogin_user_cpmthr_ratio =
      session_data->get_spdm_ctx().TryGetDouble("unlogin_user_cpmthr_ratio", 1.0);
  bool enable_native_jump_ratio_cpm_thre =
      SPDM_enable_native_jump_ratio_cpm_thre(session_data->get_spdm_ctx());
  double jump_decay_weight = SPDM_jump_decay_weight(session_data->get_spdm_ctx());
  int64_t jump_ratio_cvr_num_threshold = SPDM_jump_ratio_cvr_num_threshold(session_data->get_spdm_ctx());
  double jump_ratio_total_cvr = SPDM_jump_ratio_total_cvr(session_data->get_spdm_ctx());
  double jump_out_cpm_min_thre = SPDM_jump_out_cpm_min_thre(session_data->get_spdm_ctx());
  double jump_out_cpm_max_thre = SPDM_jump_out_cpm_max_thre(session_data->get_spdm_ctx());
  double fanstop_lowerdown_cpmthr_discount_ratio = RankKconfUtil::fanstopLowerEcpmThrRatio();
  bool enable_unify_adload_plugin = SPDM_enable_unify_adload_plugin(session_data->get_spdm_ctx());
  std::shared_ptr<::ks::infra::TailNumberV2> fanstop_lowerdown_cpmthr_whitelist_ =
      RankKconfUtil::fanstopLowerdownCpmthrWhitelist();
  bool enable_user_group_w_cpm_thr = false;
  std::string user_group_cpm_thr_key = "";
  double user_group_cpm_thr = 0.0;
  if (session_data->get_is_thanos_mix_request() &&
      !SPDM_enable_disable_soft_user_group_cpm_thr(session_data->get_spdm_ctx())) {
    enable_user_group_w_cpm_thr = SPDM_enable_user_group_w_cpm_thr(session_data->get_spdm_ctx());
    user_group_cpm_thr_key = SPDM_user_group_w_cpm_thr_exp_tag(session_data->get_spdm_ctx());
  } else if (session_data->get_is_follow_request()) {
    enable_user_group_w_cpm_thr = SPDM_enable_follow_user_group_w_cpm_thr(session_data->get_spdm_ctx());
    user_group_cpm_thr_key = SPDM_follow_user_group_w_cpm_thr_exp_tag(session_data->get_spdm_ctx());
  } else if (session_data->get_pos_manager_base().IsSideWindow()) {
    enable_user_group_w_cpm_thr = SPDM_enable_side_window_user_group_w_cpm_thr(session_data->get_spdm_ctx());
    user_group_cpm_thr_key = SPDM_side_window_user_group_w_cpm_thr_exp_tag(session_data->get_spdm_ctx());
  }
  user_group_cpm_thr_key =
      absl::Substitute("$0_$1_$2", user_group_cpm_thr_key, session_data->get_pos_manager_base().GetRequestAppId(),  // NOLINT
                       session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag());
  auto ptr = RankKconfUtil::userGroupThrExpConf();
  if (ptr->data().tag_2_thr_map().count(user_group_cpm_thr_key) > 0) {
    user_group_cpm_thr = ptr->data().tag_2_thr_map().at(user_group_cpm_thr_key);
  }
  std::shared_ptr<absl::flat_hash_set<int64_t>> industry_live_account_filter_set =
      RankKconfUtil::industryLiveNativeCpmThrdAccount();
  double outer_live_cpm_thr = SPDM_outer_live_cpm_thr(session_data->get_spdm_ctx());
  bool enable_explore_feed_soft_cpm_threshold =
      SPDM_enable_explore_feed_soft_cpm_threshold(session_data->get_spdm_ctx());
  bool enable_explore_feed_soft_live_cpm_thr =
      SPDM_enable_explore_feed_soft_live_cpm_thr(session_data->get_spdm_ctx());
  bool enable_account_id_cpm_thr_v2 =
      SPDM_enable_account_id_cpm_thr_v2(session_data->get_spdm_ctx());
  bool enable_adload_sample_collect =
      SPDM_enable_adload_sample_collect(session_data->get_spdm_ctx());
  bool enable_model_based_adload_control =
      SPDM_enable_model_based_adload_control(session_data->get_spdm_ctx());
  bool enable_big_promotion_support_by_author =
      SPDM_enable_big_promotion_support_by_author(session_data->get_spdm_ctx());
  bool enable_lt_experience =
      SPDM_enable_lt_experience(session_data->get_spdm_ctx());
  bool enable_cpm_thr_ratio_adjust_for_adload =
      SPDM_enable_cpm_thr_ratio_adjust_for_adload(session_data->get_spdm_ctx());
  bool enable_adload_adjust_cpm_thr_fixed =
      SPDM_enable_adload_adjust_cpm_thr_fixed(session_data->get_spdm_ctx());
  double adload_cpm_thr_ratio = SPDM_adload_cpm_thr_ratio(session_data->get_spdm_ctx());
  double adload_cpm_thr_ratio_nebula = SPDM_adload_cpm_thr_ratio_nebula(session_data->get_spdm_ctx());
  double big_promotion_support_cpm_ratio =
      SPDM_big_promotion_support_cpm_ratio(session_data->get_spdm_ctx());
  const std::string w_level =
    session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
  bool is_low_w_level_user = w_level == "W1" || w_level == "W2";
  bool enable_outer_loop_dynamic_cpm_thres =
    SPDM_enable_outer_loop_dynamic_cpm_thres(session_data->get_spdm_ctx());
  std::string outer_loop_dnc_opt_dimension =
    SPDM_outer_loop_dnc_opt_dimension(session_data->get_spdm_ctx());
  double outer_loop_dynamic_cpm_thres_alpha =
    SPDM_outer_loop_dynamic_cpm_thres_alpha(session_data->get_spdm_ctx());
  double outer_loop_dynamic_cpm_thres_min =
    SPDM_outer_loop_dynamic_cpm_thres_min_soft(session_data->get_spdm_ctx());
  bool outer_loop_dnc_opt_for_potential_nc =
    SPDM_outer_loop_dynamic_cpm_thres_for_potential_nc(session_data->get_spdm_ctx());
  bool outer_loop_potential_nc_bugfix = SPDM_outer_loop_potential_nc_bugfix(session_data->get_spdm_ctx());
  bool outer_loop_dnc_hc_new_key_prefix =
    SPDM_outer_loop_dnc_hc_new_key_prefix(session_data->get_spdm_ctx());
  std::string effect_w_level_set = SPDM_effect_w_level_set(session_data->get_spdm_ctx());
  std::vector<std::string> effect_w_level_set_split =
    absl::StrSplit(effect_w_level_set, ",", absl::SkipEmpty());
  bool is_target_w_level = true;
  if (effect_w_level_set != "all" &&
      std::find(effect_w_level_set_split.begin(), effect_w_level_set_split.end(), w_level) ==
      effect_w_level_set_split.end()) {
    is_target_w_level = false;
  }
  bool is_potential_nc_user =
      session_data->get_rank_request()->is_outerloop_potential_nc_user();
  double nc_user_cpm_ratio = SPDM_outer_loop_nc_user_cpm_soft_ratio(session_data->get_spdm_ctx());
  double nc_user_cpm_min = SPDM_outer_loop_nc_user_cpm_soft_min(session_data->get_spdm_ctx());
  bool enable_nc_model = SPDM_enable_nc_model_cpm_thres(session_data->get_spdm_ctx());
  bool enable_nc_retrieval = SPDM_enable_nc_retrieval_cpm_thres(session_data->get_spdm_ctx());
  bool enable_nc_reco_v2 = SPDM_enable_nc_reco_cpm_thres_v2(session_data->get_spdm_ctx());
  if (SPDM_enable_reco_only_w5_outer_nc_work(session_data->get_spdm_ctx()) &&
      w_level != "W5") {
    enable_nc_reco_v2 = false;
  }
  // 根据转化率 自适应调整 nc model 策略门槛
  bool enable_set_cpm_based_on_nc_score = SPDM_enable_set_cpm_based_on_nc_score(session_data->get_spdm_ctx());
  double nc_user_cpm_min_v2 = SPDM_outer_dnc_nc_user_cpm_min_v2_soft(session_data->get_spdm_ctx());
  double nc_model_cpm_adjust_ratio =
      SPDM_outer_dnc_nc_model_cpm_adjust_ratio_soft(session_data->get_spdm_ctx());
  double nc_model_cpm_lower_bound =
      SPDM_outer_dnc_nc_model_cpm_lower_bound_soft(session_data->get_spdm_ctx());
  double nc_model_cpm_upper_bound =
      SPDM_outer_dnc_nc_model_cpm_upper_bound_soft(session_data->get_spdm_ctx());
  const auto* nc_model_score_map_ptr = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_map<int64_t, double>>("nc_model_prod_score_map");
  bool enable_nc_llm = SPDM_enable_nc_llm_prod_cpm_thres(session_data->get_spdm_ctx());
  double vtr_thr = SPDM_nc_reco_vtr_cpm_thres(session_data->get_spdm_ctx());
  double lvtr_thr = SPDM_nc_reco_lvtr_cpm_thres(session_data->get_spdm_ctx());
  auto nc_product_set = RankKconfUtil::outerloopNcProductSet();
  auto nc_retrieval_set = RankKconfUtil::outerloopNcRetrievalSet();
  if (SPDM_enable_update_outerloop_nc_kconf(session_data->get_spdm_ctx())) {
    nc_product_set = RankKconfUtil::outerloopNcProductSetV2();
    nc_retrieval_set = RankKconfUtil::outerloopNcRetrievalSetV2();
  }
  const auto& nc_ocpx_set = RankKconfUtil::outerloopNcOcpxSet();
  bool enable_nc_limit_ocpx = SPDM_enable_outerloop_nc_limit_ocpx(session_data->get_spdm_ctx());
  // 根据长期价值 ltv 自适应调整 nc model 策略门槛
  bool enable_set_cpm_threshold_based_on_ltv =
    SPDM_enable_set_cpm_threshold_based_on_ltv(session_data->get_spdm_ctx());
  double alpha =
    SPDM_set_cpm_threshold_based_on_ltv_alpha_soft(session_data->get_spdm_ctx());
  double nc_model_cpm_threshold_upper_bound =
    SPDM_set_cpm_threshold_based_on_ltv_upperbound_soft(session_data->get_spdm_ctx());
  double nc_model_cpm_threshold_lower_bound =
    SPDM_set_cpm_threshold_based_on_ltv_lowerbound_soft(session_data->get_spdm_ctx());
  //  根据 dnc values 字段调整 nc model 策略门槛
  bool enable_set_cpm_threshold_based_on_dnc_values =
    SPDM_enable_set_cpm_threshold_based_on_dnc_values(session_data->get_spdm_ctx());
  bool enable_set_cpm_threshold_based_on_dnc_score =
    SPDM_enable_set_cpm_threshold_based_on_dnc_score(session_data->get_spdm_ctx());
  bool enable_set_cpm_threshold_based_on_dnc_ltv =
    SPDM_enable_set_cpm_threshold_based_on_dnc_ltv(session_data->get_spdm_ctx());
  double set_cpm_threshold_based_on_dnc_value_lowerbound =
    SPDM_set_cpm_threshold_based_on_dnc_value_lowerbound(session_data->get_spdm_ctx());
  const auto* interest_prod_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<std::string>>("outerloop_interest_product");
  const auto* llm_prod_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<std::string>>("llm_user_prod_interest_list");
  const auto* interest_industry_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<std::string>>("outerloop_interest_industry");
  const auto* interest_sec_ind_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<std::string>>("outerloop_interest_second_industry");
  const auto* nc_model_prod_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<int64_t>>("nc_model_prod_list");
  double user_group_ecpc_ratio = 1.0;  // 人群优化 ECPC 策略 CPM 门槛系数
  double user_group_ecpc_ratio_v2 = 1.0;
  if (SPDM_enable_user_group_ecpc_thr(session_data->get_spdm_ctx())) {
    std::string user_group_tag =
        session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
    std::string user_group_ecpc_key =
        absl::Substitute("$0_$1_$2", SPDM_user_value_group_thr_tag(session_data->get_spdm_ctx()),
                         session_data->get_is_thanos_mix_request() ? "thanos" : "feed", user_group_tag);
    auto ecpc_config = RankKconfUtil::userVauleGroupEcpcConf();
    if (ecpc_config && ecpc_config->data().cpm_thr_conf().count(user_group_ecpc_key) > 0) {
      user_group_ecpc_ratio = ecpc_config->data().cpm_thr_conf().at(user_group_ecpc_key);
    }
  }
  if (SPDM_enable_user_group_ecpc_thr_v2(session_data->get_spdm_ctx())) {
    std::string user_group_tag =
        session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
    std::string buyer_effective_type =
        session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
    std::string user_group_ecpc_key_v2 =
        absl::Substitute("$0_$1_$2", SPDM_user_value_group_thr_tag_v2(session_data->get_spdm_ctx()),
                         user_group_tag, buyer_effective_type);
    auto ecpc_config = RankKconfUtil::userVauleGroupEcpcConf();
    if (ecpc_config && ecpc_config->data().cpm_thr_conf().count(user_group_ecpc_key_v2) > 0) {
      user_group_ecpc_ratio_v2 = ecpc_config->data().cpm_thr_conf().at(user_group_ecpc_key_v2);
    }
  }
  if (SPDM_enable_thr_v2_skip_feed(session_data->get_spdm_ctx()) &&
      session_data->get_pos_manager_base().GetSubPageId() != 10011001 &&
      session_data->get_pos_manager_base().GetSubPageId() != 11001001) {
        user_group_ecpc_ratio_v2 = 1;
      }
  auto user_imp_opt_tag = session_data->common_r_->GetStringCommonAttr("ud_user_group_lv_dym").value_or("");
  if (SPDM_enable_user_imp_opt_thr_v2(session_data->get_spdm_ctx())) {
    user_imp_opt_tag = session_data->get_rank_request()->ad_request().ad_user_info().user_imp_opt_tag();
  }
  bool enable_live_inner_thr = false;
  double live_inner_thr = 0.0;
  if (SPDM_enable_live_inner_thr(session_data->get_spdm_ctx())) {
    enable_live_inner_thr = GetLiveInnerCpmThr(session_data, &live_inner_thr, "soft");
  }
  bool enable_user_imp_opt_thr = SPDM_enable_user_imp_opt_thr(session_data->get_spdm_ctx());
  double user_imp_opt_thr_ratio = SPDM_user_imp_opt_thr_ratio(session_data->get_spdm_ctx());
  double user_imp_opt_thr_ratio_nebula = SPDM_user_imp_opt_thr_ratio_nebula(session_data->get_spdm_ctx());
  bool enable_user_imp_opt_thr_all = SPDM_enable_user_imp_opt_thr_all(session_data->get_spdm_ctx());
  // 外循环 I2I 兴趣强化实验
  bool enable_mini_game_i2i_enhance = SPDM_enable_mini_game_i2i_enhance(session_data->get_spdm_ctx());
  bool enable_outerloop_cross_ind_i2i_enhance =
    SPDM_enable_outerloop_cross_ind_i2i_enhance(session_data->get_spdm_ctx());
  double mini_game_i2i_enhance_thres = SPDM_mini_game_i2i_enhance_thres_soft(session_data->get_spdm_ctx());
  double outerloop_cross_ind_thres = SPDM_outerloop_cross_ind_thres_soft(session_data->get_spdm_ctx());
  auto outerloop_i2i_enhance_ocpx_white_set = RankKconfUtil::outerloopNcGoalOcpxSet();
  bool enable_outerloop_ac_retrieval_enhance =
    SPDM_enable_outerloop_ac_retrieval_enhance(session_data->get_spdm_ctx());
  auto outerloop_ac_retrieval_set = RankKconfUtil::outerloopAcRetrievalSet();
  bool enable_w_level_limit_i2i_enhance = SPDM_enable_w_level_limit_i2i_enhance(session_data->get_spdm_ctx());
  std::string effect_w_level_set_i2i_enhance =
    SPDM_effect_w_level_set_i2i_enhance(session_data->get_spdm_ctx());
  std::vector<std::string> effect_w_level_i2i_enhance =
    absl::StrSplit(effect_w_level_set_i2i_enhance, ",", absl::SkipEmpty());
  if (enable_w_level_limit_i2i_enhance && effect_w_level_set_i2i_enhance != "all" &&
      std::find(effect_w_level_i2i_enhance.begin(), effect_w_level_i2i_enhance.end(), w_level) ==
      effect_w_level_i2i_enhance.end()) {
    enable_outerloop_cross_ind_i2i_enhance = false;
  }
  auto outerloop_ac_product_set = RankKconfUtil::outerloopAcProductSet();
  bool enable_outerloop_ac_product_set = SPDM_enable_outerloop_ac_product_set(session_data->get_spdm_ctx());
  auto ocpx_industry_map_config = RankKconfUtil::OuterLoopAcIndustry();
  bool enable_outerloop_ac_enhance_cpm_threshold =
    SPDM_enable_outerloop_ac_enhance_cpm_threshold(session_data->get_spdm_ctx());
  bool enable_outerloop_ac_converted_industry_limit =
    SPDM_enable_outerloop_ac_converted_industry_limit_rank(session_data->get_spdm_ctx());
  bool enable_outerloop_nc_max_cpm_thres =
    SPDM_enable_outerloop_nc_max_cpm_thres(session_data->get_spdm_ctx());
  double outerloop_nc_max_cpm_thres = SPDM_outerloop_nc_max_cpm_thres_soft(session_data->get_spdm_ctx());
  bool enable_outerloop_low_active_ac_max_cpm_thres =
    SPDM_enable_outerloop_low_active_ac_max_cpm_thres(session_data->get_spdm_ctx());
  double outerloop_ac_max_cpm_thres = SPDM_outerloop_ac_max_cpm_thres_soft(session_data->get_spdm_ctx());
  bool enable_outerloop_low_active_ac_max_cpm_ratio =
    SPDM_enable_outerloop_low_active_ac_max_cpm_ratio(session_data->get_spdm_ctx());
  double outerloop_ac_max_cpm_ratio = SPDM_outerloop_ac_max_cpm_ratio_soft(session_data->get_spdm_ctx());
  bool enable_outerloop_nc_max_user_limit =
    SPDM_enable_outerloop_nc_max_user_limit(session_data->get_spdm_ctx());
  int is_outerloop_low_active_ac = session_data->common_r_->
    GetIntCommonAttr("is_outerloop_low_active_cnt").value_or(0);
  double enable_wanhe_threshold_opt_v2 = SPDM_enable_wanhe_threshold_opt_v2(session_data->get_spdm_ctx());
  double enable_wanhe_thr_fix = SPDM_enable_wanhe_thr_fix(session_data->get_spdm_ctx());
  bool enable_outerloop_ac_retrieval_diversity =
    SPDM_enable_outerloop_ac_retrieval_diversity(session_data->get_spdm_ctx());
  int64_t outerloop_ac_retrieval_diversity_threshold =
    SPDM_outerloop_ac_retrieval_diversity_threshold(session_data->get_spdm_ctx());
  const auto* outerloop_impression_cnt = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_map<int64_t, int64_t>>("outerloop_impression_cnt");
  bool enable_outerloop_ac_enhance_cpm_ratio =
    SPDM_enable_outerloop_ac_enhance_cpm_ratio(session_data->get_spdm_ctx());
  double outerloop_ac_enhance_cpm_ratio =
    SPDM_outerloop_ac_enhance_cpm_ratio(session_data->get_spdm_ctx());
  bool enable_outerloop_ac_shallow_action =
    SPDM_enable_outerloop_ac_shallow_action(session_data->get_spdm_ctx());
  double outerloop_ac_enhance_cpm_ratio_comment =
    SPDM_outerloop_ac_enhance_cpm_ratio_comment(session_data->get_spdm_ctx());
  double outerloop_ac_enhance_cpm_ratio_play =
    SPDM_outerloop_ac_enhance_cpm_ratio_play(session_data->get_spdm_ctx());
  double outerloop_ac_enhance_cpm_ratio_like =
    SPDM_outerloop_ac_enhance_cpm_ratio_like(session_data->get_spdm_ctx());
  // 发现页外跳广告门槛优化
  auto explore_feed_jumpout_shield_subpageid = RankKconfUtil::exploreFeedJumpOutShieldSubPageId();
  auto explore_feed_jumpout_shield_set = RankKconfUtil::exploreFeedJumpOutShield();
  bool enable_shield_explore_feed_jumpout =
       SPDM_enable_shield_explore_feed_jumpout(session_data->get_spdm_ctx()) &&
       explore_feed_jumpout_shield_subpageid &&
       explore_feed_jumpout_shield_subpageid->count(session_data->get_sub_page_id());
  double shield_explore_feed_jumpout_ratio =
       SPDM_shield_explore_feed_jumpout_ratio(session_data->get_spdm_ctx());
  bool enable_shield_explore_feed_jumpout_upgrade =
       SPDM_enable_shield_explore_feed_jumpout_upgrade(session_data->get_spdm_ctx()) &&
       explore_feed_jumpout_shield_subpageid &&
       explore_feed_jumpout_shield_subpageid->count(session_data->get_sub_page_id());
  double new_shield_explore_feed_jumpout_ratio =
       SPDM_new_shield_explore_feed_jumpout_ratio(session_data->get_spdm_ctx());

  // 外循环联盟搜索重定向
  bool enable_outerloop_universe_retarget_cpm_thres =
    SPDM_enable_outerloop_universe_retarget_cpm_thres(session_data->get_spdm_ctx());
  double outerloop_universe_retarget_cpm_ratio =
    SPDM_outerloop_universe_retarget_cpm_ratio_native(session_data->get_spdm_ctx());
  const auto* universe_retarget_product_ids = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<int64_t>>("universe_retarget_product_ids");
  bool enable_outerloop_low_active_skip_duanju =
    SPDM_enable_outerloop_low_active_skip_duanju(session_data->get_spdm_ctx());
  bool enable_outerloop_low_active_ocpx_blackset =
    SPDM_enable_outerloop_low_active_ocpx_blackset(session_data->get_spdm_ctx());
  auto user_group_ecpc_thr_skip_sub_page_id = RankKconfUtil::userGroupEcpcThrSkipSubPageId();
  bool enable_user_group_ecpc_ratio_skip_feed =
       SPDM_enable_user_group_ecpc_ratio_skip_feed(session_data->get_spdm_ctx()) &&
       user_group_ecpc_thr_skip_sub_page_id &&
       user_group_ecpc_thr_skip_sub_page_id->count(session_data->get_pos_manager_base().GetSubPageId());
  bool enable_kwai_mini_game_rb_thr_adjust =
    SPDM_enable_kwai_mini_game_rb_thr_adjust(session_data->get_spdm_ctx());
  bool enable_big_game_rb_thr_adjust =
    SPDM_enable_big_game_rb_thr_adjust(session_data->get_spdm_ctx());
  bool enable_kwai_serial_rb_thr_adjust =
    SPDM_enable_kwai_serial_rb_thr_adjust(session_data->get_spdm_ctx());
  bool enable_kwai_fiction_rb_thr_adjust =
    SPDM_enable_kwai_fiction_rb_thr_adjust(session_data->get_spdm_ctx());
  const auto* outerloop_ac_campaign = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<int64_t>>("outerloop_ac_campaign");
  bool is_kwai_mini_game_potential_nc_user = false;
  bool is_kwai_serial_potential_nc_user = false;
  bool is_kwai_fiction_potential_nc_user = false;
  bool is_big_game_potential_nc_user = false;
  if (outerloop_ac_campaign) {
    is_kwai_mini_game_potential_nc_user =
      outerloop_ac_campaign->count(kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) == 0;
    is_kwai_serial_potential_nc_user =
      outerloop_ac_campaign->count(kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) == 0;
    is_kwai_fiction_potential_nc_user =
      outerloop_ac_campaign->count(kuaishou::ad::AdEnum::AD_KWAI_FICTION_PROMOTION) == 0;
    is_big_game_potential_nc_user =
      outerloop_ac_campaign->count(kuaishou::ad::AdEnum::APP) == 0;
  }
  double kwai_mini_game_rb_thr_ratio = is_kwai_mini_game_potential_nc_user?
    SPDM_kwai_mini_game_rb_thr_ratio_potential_nc(session_data->get_spdm_ctx()):
    SPDM_kwai_mini_game_rb_thr_ratio(session_data->get_spdm_ctx());
  double big_game_rb_thr_ratio = is_big_game_potential_nc_user?
    SPDM_big_game_rb_thr_ratio_potential_nc(session_data->get_spdm_ctx()):
    SPDM_big_game_rb_thr_ratio(session_data->get_spdm_ctx());
  double kwai_serial_rb_thr_ratio = is_kwai_serial_potential_nc_user?
    SPDM_kwai_serial_rb_thr_ratio_potential_nc(session_data->get_spdm_ctx()):
    SPDM_kwai_serial_rb_thr_ratio(session_data->get_spdm_ctx());
  double kwai_fiction_rb_thr_ratio = is_kwai_fiction_potential_nc_user?
    SPDM_kwai_fiction_rb_thr_ratio_potential_nc(session_data->get_spdm_ctx()):
    SPDM_kwai_fiction_rb_thr_ratio(session_data->get_spdm_ctx());
  bool enable_adjust_ecpmthr_innerloopbigcard_secondreq =
    SPDM_enable_adjust_ecpmthr_innerloopbigcard_secondreq(session_data->get_spdm_ctx());
  double adjust_ecpmthr_innerloopbigcard_secondreq_ratio =
    SPDM_adjust_ecpmthr_innerloopbigcard_secondreq_ratio(session_data->get_spdm_ctx());

  auto lsp_native_explore_thr_conf = RankKconfUtil::lspNativeUserLayeredExploreThrConf();
  const std::string& lsp_native_explore_thr_exp_name =
    SPDM_lsp_native_user_layered_explore_thr_exp_name(session_data->get_spdm_ctx());
  bool enable_lsp_native_user_layered_explore_thr =
       SPDM_enable_lsp_native_user_layered_explore_thr(session_data->get_spdm_ctx()) &&
       lsp_native_explore_thr_conf != nullptr &&
       lsp_native_explore_thr_conf->data().exp_list().contains(lsp_native_explore_thr_exp_name);
  auto set_ecpm_threshold = [&] (AdCommon* ad) {
    //----- 先设置门槛 -----//
    ///// 1. 按流量设置
    std::string app_id = session_data->get_pos_manager_base().GetRequestAppId();
    if (session_data->get_is_feed()) {  // 双列
      if (session_data->get_is_follow_request() && sctr_into_cpm_discount_ratio_follow) {
        ad->SetCpmThr(feed_native_cpm_threshold_yuan *
            sctr_into_cpm_discount_ratio_follow,
            CpmThrFromTag::EXPOLRE_FEED_CPM_THR, true);
        RANK_DOT_STATS(session_data, sctr_into_cpm_discount_ratio_follow*100,
              "sctr_into_cpm_discount_ratio_follow_feed");
      } else {
        ad->SetCpmThr(
            feed_native_cpm_threshold_yuan, CpmThrFromTag::EXPOLRE_FEED_CPM_THR, true);
      }
    } else {  // 单列
      if (session_data->get_is_thanos_mix_request()) {  // 单列混排流量
        ad->SetCpmThr(
            native_cpm_threshold_yuan, CpmThrFromTag::NATIVE_DEFAULT_CPM_THR, true);
        // 极速版
        if (app_id == "kuaishou_nebula") {
          ad->SetCpmThr(
            native_nebula_cpm_threshold_yuan, CpmThrFromTag::NATIVE_DEFAULT_CPM_THR, true);
        }
      } else {  // 单列非混排流量
         if (session_data->get_is_follow_request() && sctr_into_cpm_discount_ratio_follow) {
            ad->SetCpmThr(non_mix_thanos_native_cpm_threshold_yuan *
              sctr_into_cpm_discount_ratio_follow,
              CpmThrFromTag::NATIVE_DEFAULT_CPM_THR, true);
            RANK_DOT_STATS(session_data, sctr_into_cpm_discount_ratio_follow*100,
              "sctr_into_cpm_discount_ratio_follow_thanos");
         } else {
            ad->SetCpmThr(non_mix_thanos_native_cpm_threshold_yuan,
                CpmThrFromTag::NATIVE_DEFAULT_CPM_THR, true);
        }
      }
    }
    if (enable_explore_feed_soft_cpm_threshold) {
      if (session_data->get_is_feed() && session_data->get_pos_manager_base().GetSubPageId() == 10002001) {  //发现页双列  // NOLINT
        ad->SetCpmThr(explore_feed_soft_cpm_threshold_yuan,
            CpmThrFromTag::EXPLORE_FEED_SOFT_CPM_THR, true);
        //  设置软广直播门槛
        if (enable_explore_feed_soft_live_cpm_thr) {
          if (ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO_TO_LIVE ||
              ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
            ad->SetCpmThr(explore_feed_soft_live_cpm_thr,
              CpmThrFromTag::EXPLORE_FEED_SOFT_CPM_THR, true);
          }
        }
      }
    }
    // 万合流量增加 cpm 门槛系数
    if (session_data->get_pos_manager_base().IsWanhe()) {  // NOLINT
      if (SPDM_enable_wanhe_threshold_opt(session_data->get_spdm_ctx())) {
        // 新统一逻辑
        if (enable_wanhe_thr_fix) {
          double new_cpm_thr = wanhe_soft_cpm_thr;
          if (enable_wanhe_threshold_opt_v2 && ad->get_server_show_ctr() > 0) {
            new_cpm_thr = new_cpm_thr / ad->get_server_show_ctr();
          }
          RANK_DOT_STATS(session_data, new_cpm_thr*100, "wanhe_soft_cpm_thr");
          ad->SetCpmThr(new_cpm_thr, CpmThrFromTag::NATIVE_DEFAULT_CPM_THR, true);
        } else {
          if (enable_wanhe_threshold_opt_v2 && ad->get_server_show_ctr() > 0) {
            wanhe_soft_cpm_thr = wanhe_soft_cpm_thr / ad->get_server_show_ctr();
          }
          RANK_DOT_STATS(session_data, wanhe_soft_cpm_thr*100, "wanhe_soft_cpm_thr");
          ad->SetCpmThr(wanhe_soft_cpm_thr, CpmThrFromTag::NATIVE_DEFAULT_CPM_THR, true);
        }
      } else {
        // 老的逻辑
        double ratio = 1.0;
        if (session_data->get_pos_manager_base().IsMainSideWindow())  {
          ratio = main_side_window_ratio;
        } else if (session_data->get_pos_manager_base().IsProfileSideWindow()) {
          ratio = profile_side_window_ratio;
        } else if (session_data->get_pos_manager_base().IsProfileSkin()) {
          ratio = profile_skin_ratio;
        }
        RANK_DOT_STATS(session_data, ratio*100, "wanhe_soft_cpm_ratio");
        ad->SetCpmThr(non_mix_thanos_native_cpm_threshold_yuan*ratio,
              CpmThrFromTag::NATIVE_DEFAULT_CPM_THR, true);
      }
    }

    if (session_data->get_is_follow_request()) {  // 关注页
      set_follow_ecpm_threshold(ad);
    }
    if (session_data->get_is_search_request()) {  // 搜索
      double search_threshold = session_data->get_is_thanos_request() ?
          session_data->get_rank_search_cpm_thr().search_kuaishou_thano_native_ab_cpm_thr() :
          session_data->get_rank_search_cpm_thr().search_kuaishou_feed_native_ab_cpm_thr();
      search_threshold *= 1e-6;  // 搜索配置里的单位是 10^-6 元，转成元
      ad->SetCpmThr(search_threshold, CpmThrFromTag::SEARCH_DYNAMIC_CPM_THR, true);
    }
    if (session_data->get_is_inspire_live_request()) {  // 激励直播
      // 移动端极速进人 nobid 激励流量门槛
      if (ad->get_account_type() == AdEnum::ACCOUNT_ESP_MOBILE &&
          ad->get_bid_type() == AdEnum::OCPM_DSP &&
          ad->get_ocpx_action_type() == AdActionType::AD_AUDIENCE_FAST) {
        ad->SetCpmThr(
            nobid_fast_cpm_threshold, CpmThrFromTag::MOBILE_AUDIENCE_NOBID_CPM_THR, true);
      }
      if (ad->get_ocpx_action_type() == AdActionType::AD_LIVE_AUDIENCE_FAST) {
        ad->SetCpmThr(8, CpmThrFromTag::ESP_AUDIENCE_FAST_CPM_THR, true);
      }
    }

    if (session_data->get_pos_manager_base().IsGuessYouLike()) {  // 猜你喜欢
      ad->SetCpmThr(
          guess_you_like_cpm_threshold_yuan, CpmThrFromTag::GUESS_YOU_LIKE_CPM_THR, true);
    }
    if (session_data->get_pos_manager_base().IsBuyerHomePageTraffic()) {  // 买家首页
      if (enable_buyer_home_page_soft_queue_new_cpm_thr) {
        ad->SetCpmThr(
          buyer_home_page_soft_queue_new_cpm_thr, CpmThrFromTag::BUYER_HOME_PAGE_CPM_THR, true);
      } else {
        ad->SetCpmThr(
          buyer_homepage_soft_queue_cpm_thr, CpmThrFromTag::BUYER_HOME_PAGE_CPM_THR, true);
      }
    }
    if (session_data->get_pos_manager_base().IsMallTraffic() &&
        enable_mall_soft_queue_cpm_thr) {  // 商城
      ad->SetCpmThr(mall_soft_queue_cpm_thr, CpmThrFromTag::MALL_TABL_CPM_THR, true);
    }

    if (session_data->get_is_explore_feed_inner()) {
      double cpm_factor = 1.0;
      // 内循环 cpm 门槛系数
      if (enable_inner_explore_cpm_thr_ratio && ad->Is(AdFlag::is_inner_loop_ad)) {
        cpm_factor *= inner_explore_cpm_thr_ratio;
        RANK_DOT_STATS(session_data, inner_explore_cpm_thr_ratio * 100,
                       "kuaishou_explore_cpm_thr_ratio", "inner_flow.all_item.native_v2");
      }
      ad->SetCpmThr(ecpm_threshold_explore_inner_native * cpm_factor,
                            CpmThrFromTag::NATIVE_DEFAULT_CPM_THR, true);
      RANK_DOT_STATS(session_data, ecpm_threshold_explore_inner_native,
                    "explore_inner_native_cpm_thresh");
    }

    ///// 2. 按广告设置
    // 外粉 cpm 阈值调整实验
    if ((enable_fanstop_cpm_new_threshold_yuan ||
        (fanstop_cpm_threshold_exp_tail_set_ &&
         fanstop_cpm_threshold_exp_tail_set_->IsOnFor(ad->get_unit_id()))) &&
        ad->Is(AdFlag::is_fanstop) && ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
      if (session_data->get_is_feed()) {  // 双列
        ad->SetCpmThr(4.0, CpmThrFromTag::FEED_FANSTOP_NEW_CPM_THR, true);
      } else {  // 单列
        if (session_data->get_is_thanos_mix_request()) {  // 单列混排流量, 实验不影响单列非混排流量
          ad->SetCpmThr(
              fanstop_cpm_new_threshold_yuan, CpmThrFromTag::FANSTOP_NEW_CPM_THR, true);
        }
      }
    }
    // 聚星补量广告降低门槛
    if (session_data->get_is_explore_or_selected_request() && ad->get_is_lowest_cost() &&
        juxing_supplement_exp_account->count(ad->get_account_id())) {
      double juxing_threshold = session_data->get_is_feed() ? 1000 : 0.001;  // 双列屏蔽，单列 1 厘
      ad->SetCpmThr(juxing_threshold, CpmThrFromTag::JUXING_FANSTOP_CPM_THR, true);
    }
    // 新内粉单独设置门槛
    if (ad->Is(AdFlag::is_new_inner_fanstop)) {
      // 新内粉的 p0 和流量券两种订单单独设置门槛
      if (ad->get_priority_level() == 1 || ad->get_priority_level() == 5) {
        ad->SetCpmThr(1.2, CpmThrFromTag::NEW_INNER_PRIORITY_CPM_THR, true);
      } else {
        // 新内粉效果类门槛
        ad->SetCpmThr(1.0, CpmThrFromTag::NEW_INNER_EFFECT_CPM_THR, true);
      }
    }

    //----- 再调整门槛 -----//
    // 壮阳账户提高门槛
    if (enable_account_id_cpm_thr_v2) {
      if (ad->get_account_advertiser_risk_label() == "adRiskZYCorporations") {
        ad->MultiCpmThr(account_id_cpm_thr_ratio_soft_v2,
                                CpmThrFromTag::ACCOUNT_ID_DANGER_CPM_THR);
        RANK_DOT_STATS(session_data, account_id_cpm_thr_ratio_soft_v2,
                       "ACCOUNT_DANGER_CPM_THR_v2", "SOFT", app_id, std::to_string(ad->Is(AdFlag::is_inner_loop_ad)),  // NOLINT
                        ad->get_account_advertiser_risk_label());
      }
    }
    // 虚拟金账户调整门槛
    if (enable_pay_mode_account_cpm_thr && session_data->get_is_thanos_mix_request()) {
      if (ad->get_pay_mode() == pay_mode_tag) {
        ad->SetCpmThr(pay_mode_account_cpm_thr, CpmThrFromTag::PAY_MODE_ACCOUNT_CPM_THR);
        RANK_DOT_STATS(session_data, pay_mode_account_cpm_thr, "pay_mode_account_cpm_thr",
                 "soft", app_id, std::to_string(ad->Is(AdFlag::is_inner_loop_ad)), std::to_string(ad->Is(AdFlag::IsHardAd)));  // NOLINT
      }
    }
    // 未登录用户调整门槛
    if (session_data->get_is_unlogin_user() &&
        session_data->get_is_thanos_mix_request()) {
      ad->SetCpmThr(native_cpm_threshold_yuan * unlogin_user_cpmthr_ratio,
        CpmThrFromTag::UNLOGIN_USER_CPM_THR, true);
      RANK_DOT_STATS(session_data,
                    native_cpm_threshold_yuan * unlogin_user_cpmthr_ratio * 1000,
                    "unlogin_user_cpmthr_soft");
    }
    // 外跳率高的广告调整门槛
    if (enable_native_jump_ratio_cpm_thre && session_data->get_is_thanos_mix_request()) {
      std::string key = absl::StrCat(ad->get_campaign_type(), ",", ad->get_ocpx_action_type());
      double avg_conversion_cvr = ks::ad_rank::RankingData::GetInstance()->GetAdConversionHcMoving(key,
        ad->get_ad_conversion_cvr(), jump_decay_weight,
        jump_ratio_cvr_num_threshold);
      double avg_app_invoke_cvr = ks::ad_rank::RankingData::GetInstance()->GetAppInvokHcMoving(key,
        ad->get_app_invork_cvr(), jump_decay_weight,
        jump_ratio_cvr_num_threshold);
      double avg_cvr = std::max(avg_conversion_cvr, avg_app_invoke_cvr);
      double ratio = 1.0;
      if (jump_ratio_total_cvr) {
        ratio = avg_cvr / jump_ratio_total_cvr;
      }
      double min_value = jump_out_cpm_min_thre;
      double max_value = jump_out_cpm_max_thre;
      if (ratio <= min_value) {
        ratio = min_value;
      }
      if (ratio >= max_value) {
        ratio = max_value;
      }
      ad->MultiCpmThr(ratio, CpmThrFromTag::JUMP_OUT_CPM_THR);
      RANK_DOT_STATS(session_data, ratio * 1000, "jump_out_ratio_native_cpm_thr");
    }
    // 粉条特殊账户降低门槛 (活动需要)
    if (ad->Is(AdFlag::is_fanstop) &&
        ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 &&
        fanstop_lowerdown_cpmthr_whitelist_ &&
        fanstop_lowerdown_cpmthr_whitelist_->IsOnFor(ad->get_account_id())) {
      ad->MultiCpmThr(fanstop_lowerdown_cpmthr_discount_ratio,
                              CpmThrFromTag::FANSTOP_DYNAMIC_CPM_THR);
      RANK_DOT_STATS(session_data, ad->GetCpmThr(), "fanstop_custom_cpmthr");
    }
    // adload
    if (session_data->get_is_thanos_mix_request()) {
      if (!enable_adload_adjust_cpm_thr_fixed) {
        ad->MultiCpmThr(
          ad->get_adload_admit_thresh_ratio(), CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      }
      // unify adload control
      if (enable_unify_adload_plugin) {
        RANK_DOT_STATS(session_data, ad->get_adload_admit_thresh_ratio() * 1000, "unify_adload_admit",
                "SOFT", app_id, session_data->get_unify_adload_tag(), std::to_string(ad->Is(AdFlag::is_inner_loop_ad)));  // NOLINT
      }
      // adload sample
      if (enable_adload_sample_collect) {
        RANK_DOT_STATS(session_data, ad->get_adload_admit_thresh_ratio() * 1000, "sample_adload_admit",
                                                 "SOFT", app_id, std::to_string(ad->Is(AdFlag::is_inner_loop_ad)));  // NOLINT
      }
      // model based adload control
      if (enable_model_based_adload_control) {
        RANK_DOT_STATS(session_data, ad->get_adload_admit_thresh_ratio() * 1000, "model_adload_admit",
                       "SOFT", app_id, session_data->get_total_tag(), std::to_string(ad->Is(AdFlag::is_inner_loop_ad)));  // NOLINT
      }
    }
    // 分人群设置 cpm 门槛
    if (enable_user_group_w_cpm_thr) {
      ad->SetCpmThr(user_group_cpm_thr, CpmThrFromTag::USER_GROUP_CPM_THR);
      RANK_DOT_STATS(session_data, user_group_cpm_thr * 1000, "user_group_cpm_thr", user_group_cpm_thr_key,
                     kuaishou::ad::AdEnum::AdQueueType_Name(ad->get_ad_queue_type()),
                     kuaishou::ad::AdEnum::AdMonitorType_Name(ad->GetAdMonitorType()));
      const std::string& user_w_level =
        session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
      double user_w_group_cpm_thr_boost_ratio = 1.0;
      auto ueq_by_pxtr_w_level_exp_tag
        = SPDM_ueq_by_pxtr_w_level_exp_tag(session_data->get_spdm_ctx());
      RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "soft_user_group_boost_cpm_thr_before",
                    kuaishou::ad::AdEnum::AdQueueType_Name(ad->get_ad_queue_type()),
                    kuaishou::ad::AdEnum::AdMonitorType_Name(ad->GetAdMonitorType()),
                    user_w_level,
                    ueq_by_pxtr_w_level_exp_tag);
      if (user_w_level == "W1") {
        user_w_group_cpm_thr_boost_ratio =
          SPDM_soft_w1_group_cpm_thr_boost_ratio(session_data->get_spdm_ctx());
      } else if (user_w_level == "W2") {
        user_w_group_cpm_thr_boost_ratio =
          SPDM_soft_w2_group_cpm_thr_boost_ratio(session_data->get_spdm_ctx());
      } else if (user_w_level == "W3") {
        user_w_group_cpm_thr_boost_ratio =
          SPDM_soft_w3_group_cpm_thr_boost_ratio(session_data->get_spdm_ctx());
      } else if (user_w_level == "W4") {
        user_w_group_cpm_thr_boost_ratio =
          SPDM_soft_w4_group_cpm_thr_boost_ratio(session_data->get_spdm_ctx());
      } else if (user_w_level == "W5") {
        user_w_group_cpm_thr_boost_ratio =
          SPDM_soft_w5_group_cpm_thr_boost_ratio(session_data->get_spdm_ctx());
      } else {
        user_w_group_cpm_thr_boost_ratio =
          SPDM_soft_w_group_cpm_thr_boost_ratio(session_data->get_spdm_ctx());
      }
      ad->MultiCpmThr(user_w_group_cpm_thr_boost_ratio,
                                        CpmThrFromTag::USER_GROUP_BOOST_CPM_THR);
      RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "soft_user_group_boost_cpm_thr_after",
                    kuaishou::ad::AdEnum::AdQueueType_Name(ad->get_ad_queue_type()),
                    kuaishou::ad::AdEnum::AdMonitorType_Name(ad->GetAdMonitorType()),
                    user_w_level,
                    ueq_by_pxtr_w_level_exp_tag);
    }

    //// 本地原生链路人群探索
    if (enable_lsp_native_user_layered_explore_thr &&
        ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION) {
      const auto& exp_config =
        lsp_native_explore_thr_conf->data().exp_list().at(lsp_native_explore_thr_exp_name);
      bool enable_default = exp_config.enable_default();
      double default_ecpm_thr = exp_config.default_ecpm_thr();
      const auto& tag_thr_configs = exp_config.tag_thr_configs();

      const auto& user_layered_tag_scores = session_data->get_local_life_user_layered_tags();
      if (user_layered_tag_scores.empty() && enable_default) {
        RANK_DOT_COUNT(session_data, 1,
          "LSP_NATIVE_USER_LAYERED_EXPLORE_THR.empty_default_success_cnt");
        ad->MultiCpmThr(default_ecpm_thr, CpmThrFromTag::LSP_NATIVE_USER_LAYERED_EXPLORE_THR);
      } else {
        bool find_match = false;
        double min_ecpm_thr = 1.0;
        for (const auto& config : tag_thr_configs) {
          const auto& user_tags = config.user_tags();
          double tags_ecpm_thr = config.tags_ecpm_thr();
          for (const auto& tag_score : user_layered_tag_scores) {
            if (std::find(
                  user_tags.begin(),
                  user_tags.end(),
                  kuaishou::ad::AdEnum::LifeUserLayeredTags_Name(tag_score.first))
                != user_tags.end()) {
              find_match = true;
              if (tags_ecpm_thr > 0 && tags_ecpm_thr < min_ecpm_thr) {
                min_ecpm_thr = tags_ecpm_thr;
              }
            }
          }
        }

        if (find_match) {
          RANK_DOT_COUNT(session_data, 1,
            "LSP_NATIVE_USER_LAYERED_EXPLORE_THR.find_match_success_cnt");
          ad->MultiCpmThr(min_ecpm_thr, CpmThrFromTag::LSP_NATIVE_USER_LAYERED_EXPLORE_THR);
        } else if (enable_default) {
          RANK_DOT_COUNT(session_data, 1,
            "LSP_NATIVE_USER_LAYERED_EXPLORE_THR.default_success_cnt");
          ad->MultiCpmThr(default_ecpm_thr, CpmThrFromTag::LSP_NATIVE_USER_LAYERED_EXPLORE_THR);
        }
      }
    }
    // 行业直播
    auto is_industry_live_filter_account =
            industry_live_account_filter_set->find(ad->get_account_id());
    if (is_industry_live_filter_account != industry_live_account_filter_set->end() &&
        session_data->get_is_thanos_mix_request() &&
        ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE) {
      ad->SetCpmThr(outer_live_cpm_thr, CpmThrFromTag::NATIVE_UNIFY_CPM_THR, true);
      RANK_DOT_STATS(session_data,
                     outer_live_cpm_thr,
                     "outer_live_cpm_thr");
    }
    if (enable_set_cpm_threshold_based_on_dnc_values &&
        ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::AD_ROAS ||
        ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::AD_SEVEN_DAY_ROAS) {
      double dnc_score = ad->Attr(ItemIdx::dnc_score).GetDoubleValue((ad->AttrIndex())).value_or(0.0);
      double dnc_ltv = ad->Attr(ItemIdx::dnc_ltv).GetDoubleValue((ad->AttrIndex())).value_or(0.0);
      double dnc_ctcvr = ad->Attr(ItemIdx::dnc_ctcvr).GetDoubleValue((ad->AttrIndex())).value_or(0.0);
      double dnc_value = 0;
      if (enable_set_cpm_threshold_based_on_dnc_score) {
        dnc_value = dnc_score;
      } else if (enable_set_cpm_threshold_based_on_dnc_ltv) {
        dnc_value = dnc_ltv * dnc_ctcvr;
      }
      if (dnc_value >= set_cpm_threshold_based_on_dnc_value_lowerbound) {
          double base_cpm_threshold = ad->get_cpm_thr();
          base_cpm_threshold = 1.0;
          base_cpm_threshold = std::max(base_cpm_threshold, nc_user_cpm_min_v2);
          ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_NC_USER_CPM_THR, true);
      }
    }
    if (!enable_set_cpm_threshold_based_on_dnc_values &&
        ad->Is(AdFlag::is_outer_loop_ad) && kBenifitFactor > 0 &&
        (is_potential_nc_user) && (!enable_nc_limit_ocpx ||
        (nc_ocpx_set && nc_ocpx_set->count(ad->get_ocpx_action_type()) > 0))) {
      const auto& product_name = ad->get_product_name();
      auto product_id = base::CityHash64(product_name.c_str(), product_name.length());
      const auto& multi_retrieval_tag = ad->get_multi_retrieval_tag();
      if ((enable_nc_llm && llm_prod_set && llm_prod_set->count(product_name) > 0) ||
          (enable_nc_reco_v2 && (ad->get_reco_vtr() > vtr_thr || ad->get_reco_plvtr() > lvtr_thr)) ||
          (nc_product_set && nc_product_set->count(product_name) > 0) ||
          (enable_nc_model && nc_model_prod_set && nc_model_prod_set->count(product_id) > 0) ||
          (enable_nc_retrieval && nc_retrieval_set && nc_retrieval_set->count(multi_retrieval_tag) > 0)) {
        double base_cpm_threshold = ad->get_cpm_thr();
        base_cpm_threshold = base_cpm_threshold * nc_user_cpm_ratio / kBenifitFactor;
        base_cpm_threshold = std::max(base_cpm_threshold, nc_user_cpm_min);
        RANK_DOT_STATS(session_data, base_cpm_threshold * 100, "PotentialNcUser", "soft");
        ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_NC_USER_CPM_THR, true);
      }
      if (enable_set_cpm_based_on_nc_score && nc_model_score_map_ptr) {
        auto iter_nc_model = nc_model_score_map_ptr->find(product_id);
        if (iter_nc_model != nc_model_score_map_ptr->end()) {
          double cpm_ratio = iter_nc_model->second;
          cpm_ratio *= nc_model_cpm_adjust_ratio;
          cpm_ratio = std::max(nc_model_cpm_lower_bound,
                              std::min(cpm_ratio, nc_model_cpm_upper_bound));
          cpm_ratio += 1.0;
          if (cpm_ratio > 1.0) {
            double base_cpm_threshold = ad->get_cpm_thr();
            base_cpm_threshold = base_cpm_threshold / cpm_ratio / kBenifitFactor;
            base_cpm_threshold = std::max(base_cpm_threshold, nc_user_cpm_min_v2);
            RANK_DOT_STATS(session_data, base_cpm_threshold * 100,
              "PotentialNcUser", "soft", "nc_model_v2");
            ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_NC_USER_CPM_THR, true);
          }
        }
      }
      if (enable_set_cpm_threshold_based_on_ltv && nc_model_score_map_ptr) {
        auto iter_nc_model = nc_model_score_map_ptr->find(product_id);
        double cpm_ratio = 1.0;
        if (iter_nc_model != nc_model_score_map_ptr->end()) {
          // 计算长期价值 ltv cpm auto_cpa_bid
          auto outerloop_dnc_ltv = RankKconfUtil::outerloopDncLtv();
          auto ocpx_action_type =
            kuaishou::ad::AdActionType_Name(ad->get_ocpx_action_type());
          auto ocpx_action_type_enum = ad->get_ocpx_action_type();
          double ltv = 0;
          auto iter = outerloop_dnc_ltv->find(ocpx_action_type);
          if (iter != outerloop_dnc_ltv->end()) {
            ltv = iter->second;
          }
          std::string key = absl::StrCat(ocpx_action_type,
                              "_", ad->get_industry_parent_id_v3());
          iter = outerloop_dnc_ltv->find(key);
          if (iter != outerloop_dnc_ltv->end()) {
            ltv = iter->second;
          }
          double auto_cpa_bid = ad->get_auto_cpa_bid();
          auto ad_price_cpm = ad->get_cpm();
          if (ocpx_action_type_enum == kuaishou::ad::AdActionType::AD_ROAS ||
            ocpx_action_type_enum == kuaishou::ad::AdActionType::AD_SEVEN_DAY_ROAS) {
            // 对 ROI 目标进行特殊处理
            cpm_ratio = 0.0;
          } else if (auto_cpa_bid > 0 && ltv > 0) {
            cpm_ratio = ltv * ad_price_cpm / auto_cpa_bid;
          }
          // 放缩到 0 - 1 之间
          cpm_ratio = std::max(nc_model_cpm_threshold_lower_bound,
                          std::min(cpm_ratio, nc_model_cpm_threshold_upper_bound));
          cpm_ratio = (cpm_ratio - nc_model_cpm_threshold_lower_bound) /
            (nc_model_cpm_threshold_upper_bound - nc_model_cpm_threshold_lower_bound);
          cpm_ratio *= alpha;
          cpm_ratio += 1.0;
        }
        if (cpm_ratio > 1.0) {
          double base_cpm_threshold = ad->get_cpm_thr();
          base_cpm_threshold = base_cpm_threshold / cpm_ratio / kBenifitFactor;
          base_cpm_threshold = std::max(base_cpm_threshold, nc_user_cpm_min_v2);
          ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_NC_USER_CPM_THR, true);
        }
      }
    }
    if (!enable_set_cpm_threshold_based_on_dnc_values) {
      SetEcpmThresforOuterLoopDncOpt(session_data, ad, w_level, is_target_w_level,
                                is_potential_nc_user && outer_loop_dnc_opt_for_potential_nc &&
                                (!outer_loop_potential_nc_bugfix || ad->Is(AdFlag::is_outer_loop_ad)),
                                enable_outer_loop_dynamic_cpm_thres, outer_loop_dnc_hc_new_key_prefix,
                                outer_loop_dnc_opt_dimension,
                                outer_loop_dynamic_cpm_thres_alpha, outer_loop_dynamic_cpm_thres_min);
    }
    const std::string industry_parent_id_v3 = std::to_string(ad->get_industry_parent_id_v3());
    bool is_cross_industry = (interest_industry_set != nullptr) &&
      interest_industry_set->count(industry_parent_id_v3) <= 0;
    bool is_interest_product = (interest_prod_set != nullptr) &&
      interest_prod_set->count(ad->get_product_name()) > 0;
    enable_outerloop_cross_ind_i2i_enhance =
      enable_outerloop_cross_ind_i2i_enhance && is_cross_industry && is_interest_product;
    bool in_ocpx_white_set = outerloop_i2i_enhance_ocpx_white_set &&
      outerloop_i2i_enhance_ocpx_white_set->count(ad->get_ocpx_action_type()) > 0;
    bool in_ac_enhance_retrieve_set = enable_outerloop_ac_retrieval_enhance &&
      outerloop_ac_retrieval_set && outerloop_ac_retrieval_set->count(ad->get_multi_retrieval_tag()) > 0;
    if (enable_outerloop_ac_enhance_cpm_threshold) {
      if (enable_outerloop_ac_converted_industry_limit && (interest_industry_set != nullptr) &&
          interest_industry_set->count(industry_parent_id_v3) <= 0 && (!enable_outerloop_ac_shallow_action ||
            (interest_industry_set->count(absl::StrCat("CMT_", industry_parent_id_v3)) <= 0 &&
            interest_industry_set->count(absl::StrCat("PLAY_", industry_parent_id_v3)) <= 0 &&
            interest_industry_set->count(absl::StrCat("LIKE_", industry_parent_id_v3)) <= 0))) {
        enable_outerloop_cross_ind_i2i_enhance = false;
      } else if (ocpx_industry_map_config != nullptr) {
        auto ocpx_industry_map = ocpx_industry_map_config->data().config;
        auto iter = ocpx_industry_map.find(ad->get_ocpx_action_type());
        if (iter != ocpx_industry_map.end()) {
          enable_outerloop_cross_ind_i2i_enhance = iter->second.count(ad->get_industry_parent_id_v3()) > 0;
        }
      }
    }
    if (enable_outerloop_ac_product_set && outerloop_ac_product_set != nullptr &&
        outerloop_ac_product_set->count(ad->get_product_name()) <= 0) {
      enable_outerloop_cross_ind_i2i_enhance = false;
    }
    if (enable_outerloop_ac_retrieval_diversity && outerloop_impression_cnt) {
      const std::string& product_name = ad->get_product_name();
      int64_t city_product_id = base::CityHash64(product_name.c_str(), product_name.length());
      auto iter = outerloop_impression_cnt->find(city_product_id);
      if (iter != outerloop_impression_cnt->end() &&
          iter->second > outerloop_ac_retrieval_diversity_threshold) {
        RANK_DOT_STATS(session_data, 1,
                 "outer_i2i_enhance_cpm_thres", "product_overimpression_filter");
        enable_outerloop_cross_ind_i2i_enhance = false;
      }
    }
    if (enable_outerloop_ac_enhance_cpm_ratio && kBenifitFactor > 0) {
      if (enable_outerloop_ac_shallow_action && interest_industry_set) {
        if (interest_industry_set->count(absl::StrCat("CMT_", industry_parent_id_v3)) > 0) {
          outerloop_ac_enhance_cpm_ratio = outerloop_ac_enhance_cpm_ratio_comment;
        } else if (interest_industry_set->count(absl::StrCat("LIKE_", industry_parent_id_v3)) > 0) {
          outerloop_ac_enhance_cpm_ratio = outerloop_ac_enhance_cpm_ratio_like;
        } else if (interest_industry_set->count(absl::StrCat("PLAY_", industry_parent_id_v3)) > 0) {
          outerloop_ac_enhance_cpm_ratio = outerloop_ac_enhance_cpm_ratio_play;
        }
      }
      outerloop_cross_ind_thres = ad->GetCpmThr() / kBenifitFactor * outerloop_ac_enhance_cpm_ratio;
      RANK_DOT_STATS(session_data, outerloop_ac_enhance_cpm_ratio * 1000,
                 "outer_i2i_enhance_cpm_thres", "cpm_ratio");
    }
    SetEcpmThresforOuterLoopI2IEnhance(session_data, ad, enable_mini_game_i2i_enhance && is_interest_product,
                                       enable_outerloop_cross_ind_i2i_enhance,
                                       in_ocpx_white_set, in_ac_enhance_retrieve_set,
                                       mini_game_i2i_enhance_thres,
                                       outerloop_cross_ind_thres);
    if (enable_outerloop_universe_retarget_cpm_thres && universe_retarget_product_ids
        && ad->Is(AdFlag::is_outer_loop_ad)) {
      int64_t city_product_id = ad->get_city_product_id();
      if (universe_retarget_product_ids->count(city_product_id) > 0 && kBenifitFactor > 0) {
        double temp_cpm_threshold = ad->GetCpmThr() / kBenifitFactor * outerloop_universe_retarget_cpm_ratio;
        RANK_DOT_STATS(session_data, outerloop_universe_retarget_cpm_ratio * 1000,
                  "outerloop_universe_retarget_cpm_thres", "native");
        ad->SetCpmThr(temp_cpm_threshold, CpmThrFromTag::OUTERLOOP_UNIVERSE_RETARGET, true);
      }
    }
    if (enable_outerloop_low_active_ac_max_cpm_thres && is_outerloop_low_active_ac > 0) {
      if (enable_outerloop_low_active_ac_max_cpm_ratio && kBenifitFactor > 0) {
        outerloop_nc_max_cpm_thres = ad->GetCpmThr() / kBenifitFactor * outerloop_ac_max_cpm_ratio;
      } else {
        outerloop_nc_max_cpm_thres = outerloop_ac_max_cpm_thres;
      }
    }
    if (!(enable_set_cpm_threshold_based_on_dnc_values && is_potential_nc_user)) {
      SetEcpmThresforOuterloopNcMax(session_data, ad, enable_outerloop_nc_max_cpm_thres,
                                    outerloop_nc_max_cpm_thres, enable_outerloop_low_active_skip_duanju,
                                    is_outerloop_low_active_ac, enable_outerloop_low_active_ocpx_blackset,
                                    enable_outerloop_nc_max_user_limit);
    }
    // ------ 最后调整 cpm 门槛 ---------- //
    if (session_data->get_is_thanos_mix_request()) {
      ad->MultiCpmThr(ad->get_adload_admit_thresh_ratio_final(),
                                CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      ad->MultiCpmThr(SPDM_final_cpm_thr_ratio_soft(session_data->get_spdm_ctx()),
                              CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      if (enable_adload_adjust_cpm_thr_fixed) {
        ad->MultiCpmThr(
          ad->get_adload_admit_thresh_ratio(), CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      }
      // 大促扶持 author 白名单
      const auto& big_promotion_author_map = RankKconfUtil::BigPromotionSupportAdmit();
      if (enable_big_promotion_support_by_author &&
                big_promotion_author_map->find(ad->get_author_id()) != big_promotion_author_map->end()) {
        ad->MultiCpmThr(big_promotion_support_cpm_ratio, CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      }
      // LT 兑换实验
      if (enable_lt_experience) {
        ad->MultiCpmThr(
            session_data->get_lt_experience_cpm_ratio(), CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      }
      // adload 分端门槛系数
      if (enable_cpm_thr_ratio_adjust_for_adload) {
        if (app_id == "kuaishou") {
          ad->MultiCpmThr(adload_cpm_thr_ratio, CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
        } else if (app_id == "kuaishou_nebula") {
          ad->MultiCpmThr(adload_cpm_thr_ratio_nebula, CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
        }
      }
      // 人群曝光优化实验
      if (enable_user_imp_opt_thr && (!user_imp_opt_tag.empty() || enable_user_imp_opt_thr_all)) {
        if (app_id == "kuaishou") {
          ad->MultiCpmThr(user_imp_opt_thr_ratio, CpmThrFromTag::USER_IMP_OPT_THR);
        } else if (app_id == "kuaishou_nebula") {
          ad->MultiCpmThr(user_imp_opt_thr_ratio_nebula, CpmThrFromTag::USER_IMP_OPT_THR);
        }
      }
    }

    // 小游戏 load 实验
    if (SPDM_enable_adjust_mini_game_cpm_thr(session_data->get_spdm_ctx())
                                                && ad->Is(AdFlag::is_iaap_game_ad)) {
      // 通用打折
      if (SPDM_enable_mini_game_extra_load_strategy(session_data->get_spdm_ctx())) {
        if (app_id == "kuaishou") {
          ad->MultiCpmThr(SPDM_mini_game_extra_load_cpm_thr_gamora(session_data->get_spdm_ctx()),
                                                        CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
        } else if (app_id == "kuaishou_nebula") {
          ad->MultiCpmThr(SPDM_mini_game_extra_load_cpm_thr_nebula(session_data->get_spdm_ctx()),
                                                        CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
        }
      }
    }
    // 分产品/账户打折
    if (SPDM_enable_adjust_mini_game_cpm_thr(session_data->get_spdm_ctx())) {
      double mini_game_extra_load_cpm_thr = 1.0;
      const auto& mini_game_cpm_thr_list_conf = RankKconfUtil::miniGameCpmThrdList();
      if (mini_game_cpm_thr_list_conf != nullptr) {
        auto product_iter = mini_game_cpm_thr_list_conf->find(ad->get_product_name());
        if (product_iter != mini_game_cpm_thr_list_conf->end()) {
          mini_game_extra_load_cpm_thr = product_iter->second;
          RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "mini_game_cpm_thr_after_native_product", product_iter->first);   // NOLINT
        }
        auto account_iter = mini_game_cpm_thr_list_conf->find(absl::StrCat(ad->get_account_id()));
        if (account_iter != mini_game_cpm_thr_list_conf->end()) {
          mini_game_extra_load_cpm_thr = account_iter->second;
          RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "mini_game_cpm_thr_after_native_product", account_iter->first);   // NOLINT
        }
      }
      RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "mini_game_cpm_thr_before_native");
      if (SPDM_enable_mini_game_multi_cpm_thr(session_data->get_spdm_ctx())) {
        ad->MultiCpmThr(mini_game_extra_load_cpm_thr, CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      } else {
        ad->SetCpmThr(mini_game_extra_load_cpm_thr, CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      }
      RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "mini_game_cpm_thr_after_native");
    }

    // 人群优化 ECPC 策略 CPM 门槛系数
    if (session_data->get_is_thanos_mix_request() ||
        (session_data->get_is_feed() && !enable_user_group_ecpc_ratio_skip_feed)) {
      ad->MultiCpmThr(user_group_ecpc_ratio, CpmThrFromTag::USER_GROUP_ECPC_THR);
      ad->MultiCpmThr(user_group_ecpc_ratio_v2, CpmThrFromTag::USER_GROUP_ECPC_THR);
    }
    if (session_data->get_pos_manager_base().IsInnerExplore() ||
        session_data->get_is_follow_request() && session_data->get_is_feed()) {
      // 主站发现页内流，主站关注页双列，未命中相关策略 adload_fixed_cpm_thr = 0.0，没有效果
      ad->SetCpmThr(ad->get_adload_fixed_cpm_thr(), CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
    }
    // 发现页内流直播广告门槛
    if (enable_inner_explore_live_cpm_threshold && inner_explore_live_cpm_threshold_soft > 0 &&
        is_inner_explore && ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
      ad->SetCpmThr(inner_explore_live_cpm_threshold_soft, CpmThrFromTag::EXPOLRE_FEED_CPM_THR, true);
      RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "inner_explore_live_threshold",
          kuaishou::ad::AdEnum::AdQueueType_Name(ad->get_ad_queue_type()));
    }
    // 发现页内外流简化统一固定 unitfy cpm 门槛实验，只发现页生效，override 逻辑，先放最后
    if (!(enable_feed_explore_load_control_move_last && session_data->get_is_explore_feed_inner())) {
      set_explore_unify_ecpm_threshold(ad);
    }
    // 内循环大卡二次请求独立门槛
    if (enable_adjust_ecpmthr_innerloopbigcard_secondreq && session_data->get_rank_request()->ad_request().is_common_card_second_request() && session_data->get_rank_request()->ad_request().common_card_request_info().feed_card_ind() == kuaishou::ad::AdEnum::INNER_LOOP) {  // NOLINT
      ad->SetCpmThr(adjust_ecpmthr_innerloopbigcard_secondreq_ratio, CpmThrFromTag::INNER_BIGCARD_SECOND_REQ_THR, true);  // NOLINT
      RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "adjust_ecpmthr_innerloopbigcard_secondreq", kuaishou::ad::AdEnum::AdQueueType_Name(ad->get_ad_queue_type()));  // NOLINT
    }
  };
  // 调整 cpm 门槛值的系数
  auto product_cpm_threshold_map = RankKconfUtil::productCpmThresholdRatio();
  bool enable_product_adjust_cpm_threshold_ratio =
    SPDM_enable_product_adjust_cpm_threshold_ratio(session_data->get_spdm_ctx());
  bool enable_fill_user_value_group =
    SPDM_enable_fill_user_value_group(session_data->get_spdm_ctx());
  bool enable_set_explore_feed_high_quality_threshold =
    SPDM_enable_set_explore_feed_high_quality_threshold(session_data->get_spdm_ctx());
  // 返回 true 代表需要过滤
  auto set_explore_feed_high_quality_threshold = [&] (AdCommon *ad) {
    if (enable_set_explore_feed_high_quality_threshold) {
      if (session_data->get_is_feed() && session_data->get_pos_manager_base().GetSubPageId() == 10002001) {  //发现页双列  // NOLINT
        double final_ratio = 1.0;
        if (enable_fill_user_value_group) {
          final_ratio = session_data->get_explore_feed_user_value_group_ratio();
        }
        ad->SetCpmThr(ad->get_cpm_thr() * final_ratio * 1e-6,
                CpmThrFromTag::EXPOLRE_FEED_CPM_THR, true);
      RANK_DOT_STATS(session_data,
            ad->get_cpm_thr() * 1e-3, "explore_feed_soft_high_quality_thr");
      }
    }
  };
  bool enable_highue_skip_thr =
    SPDM_enable_highue_skip_thr(session_data->get_spdm_ctx());
  bool enable_fanstop_strong_style_skip_cpm_thres =
    SPDM_enable_fanstop_strong_style_skip_cpm_thres(session_data->get_spdm_ctx());
  bool enable_adx_rta_retarget_skip_cpm_thr =
    SPDM_enable_adx_rta_retarget_skip_cpm_thr(session_data->get_spdm_ctx());
  const std::string adx_rta_retarget_tags =
    SPDM_adx_rta_retarget_tags(session_data->get_spdm_ctx());
  bool enable_thanos_mix_unify_cpm_thr =
    SPDM_enable_thanos_mix_unify_cpm_thr_native(session_data->get_spdm_ctx());
  double rank_unify_cpm_thr_gamora =
    SPDM_rank_unify_cpm_thr_gamora_native(session_data->get_spdm_ctx());
  double rank_unify_cpm_thr_nebula =
    SPDM_rank_unify_cpm_thr_nebula_native(session_data->get_spdm_ctx());
  double rank_unify_cpm_thr_gamora_inner = rank_unify_cpm_thr_gamora;
  double rank_unify_cpm_thr_gamora_outer = rank_unify_cpm_thr_gamora;
  double rank_unify_cpm_thr_nebula_inner = rank_unify_cpm_thr_nebula;
  double rank_unify_cpm_thr_nebula_outer = rank_unify_cpm_thr_nebula;
  double rank_unify_cpm_thr_gamora_inner_v2 = rank_unify_cpm_thr_gamora;
  double rank_unify_cpm_thr_nebula_inner_v2 = rank_unify_cpm_thr_nebula;

  bool enable_new_cpm_thr_native =
    SPDM_enable_new_cpm_thr_native(session_data->get_spdm_ctx());
  double new_cpm_thr_gamora_native =
    SPDM_new_cpm_thr_gamora_native(session_data->get_spdm_ctx());
  double new_cpm_thr_nebula_native =
    SPDM_new_cpm_thr_nebula_native(session_data->get_spdm_ctx());

  bool enable_thanos_wlevel_cpm_thr_native =
    SPDM_enable_thanos_wlevel_cpm_thr_native(session_data->get_spdm_ctx());
  double w2_cpm_thr_gamora_native =
    SPDM_w2_cpm_thr_gamora_native(session_data->get_spdm_ctx());
  double w2_cpm_thr_nebula_native =
    SPDM_w2_cpm_thr_nebula_native(session_data->get_spdm_ctx());
  double w3_cpm_thr_gamora_native =
    SPDM_w3_cpm_thr_gamora_native(session_data->get_spdm_ctx());
  double w3_cpm_thr_nebula_native =
    SPDM_w3_cpm_thr_nebula_native(session_data->get_spdm_ctx());
  bool thanos_use_rb_thresh = SPDM_thanos_use_rb_thresh_native(session_data->get_spdm_ctx());
  if (SPDM_enable_thanos_mix_unify_cpm_thr_new(session_data->get_spdm_ctx())) {
    enable_thanos_mix_unify_cpm_thr =
        SPDM_enable_thanos_mix_unify_cpm_thr_native_v2(session_data->get_spdm_ctx());
    rank_unify_cpm_thr_gamora =
        SPDM_rank_unify_cpm_thr_gamora_native_v2(session_data->get_spdm_ctx());
    rank_unify_cpm_thr_nebula =
        SPDM_rank_unify_cpm_thr_nebula_native_v2(session_data->get_spdm_ctx());
    thanos_use_rb_thresh = SPDM_thanos_use_rb_thresh_native_v2(session_data->get_spdm_ctx());
  }
  const auto &unify_thr_map = RankKconfUtil::RankUnifyThrMap();
  if (SPDM_enable_rank_unify_thr_config(session_data->get_spdm_ctx()) && unify_thr_map != nullptr) {
    auto tag = SPDM_rank_unify_thr_config_tag(session_data->get_spdm_ctx());
    auto iter_gamora = unify_thr_map->find(absl::StrCat(tag, "_gamora_native"));
    if (iter_gamora != unify_thr_map->end()) {
    rank_unify_cpm_thr_gamora = iter_gamora->second;
    }
    auto iter_nebula = unify_thr_map->find(absl::StrCat(tag, "_nebula_native"));
    if (iter_nebula != unify_thr_map->end()) {
    rank_unify_cpm_thr_nebula = iter_nebula->second;
    }
    std::string user_level = session_data->get_is_unlogin_user() ? "unlogin" : w_level;
    iter_gamora = unify_thr_map->find(absl::StrCat(tag, "_gamora_native_", user_level));
    if (iter_gamora != unify_thr_map->end()) {
      rank_unify_cpm_thr_gamora = iter_gamora->second;
    }
    iter_nebula = unify_thr_map->find(absl::StrCat(tag, "_nebula_native_", user_level));
    if (iter_nebula != unify_thr_map->end()) {
      rank_unify_cpm_thr_nebula = iter_nebula->second;
    }
  }
  if (SPDM_enable_dlrank_sep_inner_outer(session_data->get_spdm_ctx()) && unify_thr_map != nullptr) {
    auto tag = SPDM_rank_unify_thr_config_tag_v2(session_data->get_spdm_ctx());
    auto iter_gamora_inner = unify_thr_map->find(absl::StrCat(tag, "_gamora_native_inner"));
    auto iter_gamora_outer = unify_thr_map->find(absl::StrCat(tag, "_gamora_native_outer"));
    auto iter_nebula_inner = unify_thr_map->find(absl::StrCat(tag, "_nebula_native_inner"));
    auto iter_nebula_outer = unify_thr_map->find(absl::StrCat(tag, "_nebula_native_outer"));
    if (iter_gamora_inner != unify_thr_map->end()) {
    rank_unify_cpm_thr_gamora_inner = iter_gamora_inner->second;
    }
    if (iter_gamora_outer != unify_thr_map->end()) {
    rank_unify_cpm_thr_gamora_outer = iter_gamora_outer->second;
    }
    if (iter_nebula_inner != unify_thr_map->end()) {
    rank_unify_cpm_thr_nebula_inner = iter_nebula_inner->second;
    }
    if (iter_nebula_outer != unify_thr_map->end()) {
    rank_unify_cpm_thr_nebula_outer = iter_nebula_outer->second;
    }
  }
  const auto &inner_unify_thr_map = RankKconfUtil::RankInnerUnifyThrMap();
  if (SPDM_enable_dlrank_sep_inner(session_data->get_spdm_ctx()) &&
      inner_unify_thr_map != nullptr) {
    auto tag = SPDM_rank_unify_thr_inner_config_tag_v2(session_data->get_spdm_ctx());
    auto iter_gamora_inner_v2 = inner_unify_thr_map->find(absl::StrCat(tag, "_gamora_native_inner"));
    auto iter_nebula_inner_v2 = inner_unify_thr_map->find(absl::StrCat(tag, "_nebula_native_inner"));
    if (iter_gamora_inner_v2 != inner_unify_thr_map->end()) {
      rank_unify_cpm_thr_gamora_inner_v2 = iter_gamora_inner_v2->second;
    } else {
      rank_unify_cpm_thr_gamora_inner_v2 = rank_unify_cpm_thr_gamora_inner;
    }
    if (iter_nebula_inner_v2 != inner_unify_thr_map->end()) {
      rank_unify_cpm_thr_nebula_inner_v2 = iter_nebula_inner_v2->second;
    } else {
      rank_unify_cpm_thr_nebula_inner_v2 = rank_unify_cpm_thr_nebula_inner;
    }
  }
  const std::vector<std::string> multi_retrieval_tags = absl::StrSplit(adx_rta_retarget_tags, ",");
  double native_unify_perf_ratio = RankKconfUtil::nativeUnifyCxrPerfRatio();
  auto user_group_level =
      session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
  double payskit_skip_ecpm_filter_ratio = SPDM_payskit_skip_ecpm_filter_ratio(session_data->get_spdm_ctx());
  bool enable_mini_game_big_r_force_skip_cpm_thrd =
                              SPDM_enable_mini_game_big_r_force_skip_cpm_thrd(session_data->get_spdm_ctx());
  bool xifan_skip_thr = SPDM_xifan_skip_thr(session_data->get_spdm_ctx());
  bool enable_refactor_filter_restore_cpm =
      SPDM_enable_refactor_filter_restore_cpm(session_data->get_spdm_ctx());
  std::string payskit_outer_bonus_to_inner_exptag =
    SPDM_payskit_outer_bonus_to_inner_exptag(session_data->get_spdm_ctx());
  bool enable_follow_use_rb_thresh = SPDM_enable_follow_use_rb_thresh(session_data->get_spdm_ctx());
  double follow_rb_thresh_value = SPDM_follow_rb_thresh_value(session_data->get_spdm_ctx());
  bool enable_inner_slide_gfp_thresh_soft =
    SPDM_enable_inner_slide_gfp_thresh_soft(session_data->get_spdm_ctx());
  std::string gfp_inner_score_thresh =
    SPDM_gfp_inner_score_thresh(session_data->get_spdm_ctx());
  const std::string& gfp_inner_score_exp_tag =
    SPDM_gfp_inner_score_exp_tag(session_data->get_spdm_ctx());
  auto inner_slide_page_conf = RankKconfUtil::innerSlideGfpThreshPageConf();
  auto filter = [&] (AdCommon* ad) -> bool {
    // 强出软广未跳过门槛, 这里暂时只对外循环内部业务生效
    if (enhance_force_reco && (ad->Is(AdFlag::is_iaa_game_ad) ||
        ad->Is(AdFlag::is_fiction_na_ad) || ad->Is(AdFlag::is_iaap_game_ad)) &&
        ad->get_ad_force_reco_tag() != kuaishou::ad::AdEnum_AdForceRecoTag_UNKNOWN_AD_FORCE_RECO_TAG) {
      return false;
    }
    // 强出跳过门槛
    if (enable_mini_game_big_r_force_skip_cpm_thrd &&
               ad->get_ad_force_reco_tag() == kuaishou::ad::AdEnum_AdForceRecoTag_RANK_GAME_PREMIERE) {
      return false;
    }
    if (session_data->get_pos_manager_base().GetRequestAppId() == "xifan" && xifan_skip_thr) {
      return false;
    }
    if (enable_highue_skip_thr &&
        ad->get_ad_force_reco_tag() == kuaishou::ad::AdEnum_AdForceRecoTag_RANK_HIGH_UE_MIXRANK) {
      return false;
    }

    // 搜索特殊逻辑，软硬广会放到同一个队列里，策略需要只对软广生效，而搜索的软广包含速推和粉条
    if (session_data->get_is_search_request()) {
      if (ad->get_promotion_type() != AdEnum::FLASH_PROMOTION && !ad->Is(AdFlag::is_fanstop)) {
        return false;
      }
      // 搜索广告干预
      if (session_data->get_enable_search_intervene() &&
          ad->get_multi_retrieval_tag() == ks::ad_target::multi_retr::RetrievalTag::SEARCHAD_INTERVENE) {
        return false;
      }
      // 搜索明投兜底广告保送
      if (ad->get_is_bidword_backup_ad()) {
        return false;
      }
      if (ad->get_multi_retrieval_tag() ==
        ks::ad_target::multi_retr::RetrievalTag::SEARCHAD_QUERY_CATEGORY_TO_ITEM_TYPE) {
        return false;
      }
      if (enable_fanstop_strong_style_skip_cpm_thres && (ad->get_allow_search_form_card() ||
          ad->get_is_search_celebrity() || ad->get_allow_search_app_card())) {
        return false;
      }
    }
    set_ecpm_threshold(ad);
    set_explore_feed_high_quality_threshold(ad);
    if (ad->get_cpm() <= 0) {  // ecpm 非正单独设一个过滤原因，所有广告都要满足
      return true;
    }
    if (session_data->get_is_inspire_live_request()) {  // 激励直播没有 cpm 门槛
      return false;
    }
    // 人群探索策略
    if (enable_cvr_explore_skip_soft_cpm_thr &&
        explore_max_creative_id > 0 &&
        ad->get_creative_id() == explore_max_creative_id &&
        (is_non_conv_user || enable_cvr_explore_skip_cpm_thr_for_dac)) {
          ad->set_is_max_ctcvr_skip_thr(true);
          return false;
    }
    // 人群探索策略 v2
    if (enable_cvr_explore_skip_soft_cpm_thr &&
        explore_topn_creative_ids.find(ad) != explore_topn_creative_ids.end() &&
        (is_non_conv_user || enable_cvr_explore_skip_cpm_thr_for_dac)) {
          ad->set_is_max_ctcvr_skip_thr(true);
          RANK_DOT_COUNT(session_data, 1,
          "high_pcvr_skip_cpm_thr_cnt", "soft", kuaishou::ad::AdEnum::ItemType_Name(ad->get_item_type()),
            kuaishou::ad::AdActionType_Name(ad->get_ocpx_action_type()), dnc_exp_tag);
          return false;
    }
    // adx rta 重定向跳过门槛探索
    if (enable_adx_rta_retarget_skip_cpm_thr && std::count(multi_retrieval_tags.begin(),
      multi_retrieval_tags.end(), absl::StrCat(ad->get_multi_retrieval_tag())) > 0) {
      RANK_DOT_COUNT(session_data, 1, "adx_rta_retarget_tag_skip_cpm_thr", "soft");
      return false;
    }
    if (enable_live_inner_thr) {
      ad->SetCpmThr(live_inner_thr, CpmThrFromTag::USER_GROUP_CPM_THR, true);
    }
    auto score = ad->get_cpm();
    if ((enable_inner_explore_cpm_thr_new_score && is_inner_explore) ||
        (enable_feed_explore_rb_thr && is_inner_explore && is_feed_explore)) {
      score = ad->get_rank_benifit();
    }
    auto thresh = ad->get_cpm_thr();
    std::string app_id = session_data->get_pos_manager_base().GetRequestAppId();
    if (ad->Is(AdFlag::is_outer_loop_ad)) {
      if (app_id == "kuaishou") {
        ad_rank_cpm_filter_threshold_factor = ad_rank_cpm_filter_threshold_factor_soft_outer_gamora;
      } else if (app_id == "kuaishou_nebula") {
        ad_rank_cpm_filter_threshold_factor = ad_rank_cpm_filter_threshold_factor_soft_outer_nebula;
      }
    } else if (ad->Is(AdFlag::is_inner_loop_ad)) {
      if (app_id == "kuaishou") {
        ad_rank_cpm_filter_threshold_factor = ad_rank_cpm_filter_threshold_factor_soft_inner_gamora;
      } else if (app_id == "kuaishou_nebula") {
        ad_rank_cpm_filter_threshold_factor = ad_rank_cpm_filter_threshold_factor_soft_inner_nebula;
      }
    }
    if (enable_product_adjust_cpm_threshold_ratio) {
      auto iter_prod = product_cpm_threshold_map->find(ad->get_product_name());
      if (iter_prod !=  product_cpm_threshold_map->end()) {
        double cpm_threshold_ratio = std::max(0.0, std::min(1.0, iter_prod->second));
        ad_rank_cpm_filter_threshold_factor *= cpm_threshold_ratio;
        RANK_DOT_STATS(session_data, cpm_threshold_ratio*1000, "product_cpm_threshold_ratio",
          ad->get_product_name(), "soft");
      }
    }
    if (session_data->get_is_thanos_mix_request()) {
      thresh *= ad_rank_cpm_filter_threshold_factor;
    }
    // 同城行业 cpm 过门槛
    auto is_valid_filter_ocpx = nearby_local_filter_ocpx_whitelist->count(ad->get_ocpx_action_type()) > 0;
    auto is_valid_page_id = nearby_local_filter_page_id_whitelist->count(session_data->get_page_id()) > 0;
    auto page_id = session_data->get_page_id();
    auto item_type = ad->get_item_type();
    auto first_industry = ad->get_first_industry_id_v5();
    auto is_inner = ad->Is(AdFlag::is_inner_loop_ad);
    auto campaigntype = ad->get_campaign_type();
    auto nearby_cpm_thr_config = RankKconfUtil::nearbyCpmThrConfig();
    auto nearby_user_group_filter_list = RankKconfUtil::nearbyUserGroupLevelConfig();
    auto nearby_page_id_filter_list = RankKconfUtil::nearbyCpmPageIdConfig();
    auto is_valid_user_group = nearby_user_group_filter_list->count(user_group_level) > 0;
    auto is_valid_cpm_page = nearby_page_id_filter_list->count(page_id) > 0;
    std::string form = kuaishou::ad::AdEnum_InteractiveForm_Name(session_data->get_rank_request()->ad_request().interactive_form());  // NOLINT
    if (is_valid_page_id &&
        enable_nearby_ecpm_config_strategy &&
        nearby_ecpm_config_strategy_tag != "") {
        std::string key;
        double cpm_thr = 0.0;

        // 根据策略类型生成对应的 key
        if (nearby_ecpm_config_strategy_tag == "nearby_pageid_ecpm") {
            key = std::to_string(page_id);
        } else if (nearby_ecpm_config_strategy_tag
                   == "nearby_pageid_itemtype_ecpm") {
            key = absl::StrCat(page_id, "|", item_type);
        } else if (nearby_ecpm_config_strategy_tag
                   == "nearby_pageid_interactive_usergroup_ecpm") {
            key = absl::StrCat(page_id, "|", form, "|", user_group_level);
        } else if (nearby_ecpm_config_strategy_tag
                  == "nearby_usergroup_ecpm") {
            key = user_group_level;
        } else if (nearby_ecpm_config_strategy_tag ==
        "nearby_pageid_interactive_itemtype_ecpm") {
            key = absl::StrCat(page_id, "|", form, "|", item_type);
        } else if (nearby_ecpm_config_strategy_tag ==
                   "nearby_pageid_interactive_ecpm") {
            key = absl::StrCat(page_id, "|", form);
        }

        // 通用的查找和设置逻辑
        auto config_iter = nearby_cpm_thr_config->datas.find(nearby_ecpm_config_strategy_tag);  // NOLINT
        if (config_iter != nearby_cpm_thr_config->datas.end()) {
            auto& config_map = config_iter->second;
            auto iter = config_map.find(key);
            if (iter != config_map.end()) {
                cpm_thr = iter->second;
            }
        }

        // 设置 CPM 门槛
        if (cpm_thr > 0) {
            ad->SetCpmThr(cpm_thr, CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_SOFT_CPM_TRH, true);
            thresh = ad->GetCpmThr();
            RANK_DOT_STATS(session_data, cpm_thr, "nearby_local_soft_map_config",
                          nearby_ecpm_config_strategy_tag, key);
        }
    } else if (is_valid_page_id && enable_nearby_ecpm_open_switch) {
      ad->SetCpmThr(nearby_local_soft_ecpm, CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_SOFT_CPM_TRH, true);
      thresh = ad->GetCpmThr();
      RANK_DOT_STATS(session_data, nearby_local_soft_ecpm, "nearby_local_filter_open_soft");
      if (enable_nearby_ecpm_out_thr && (form == "INTERACTIVE_FEED")) {
        ad->SetCpmThr(nearby_local_ecpm_out, CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_SOFT_CPM_TRH, true);
        thresh = ad->GetCpmThr();
        RANK_DOT_STATS(session_data, nearby_local_ecpm_out, "nearby_local_filter_open_soft-out");
      }
    } else if (is_valid_page_id && enable_nearby_local_ecpm_switch &&
        nearby_local_industry_whitelist != nullptr) {
            if (nearby_local_industry_whitelist->find(ad->get_industry_id_v3()) !=
                nearby_local_industry_whitelist->end()) {
                    if (is_valid_filter_ocpx) {
                              ad->SetCpmThr(nearby_local_soft_ecpm,
                               CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_SOFT_CPM_TRH, true);
                               RANK_DOT_STATS(session_data, nearby_local_soft_ecpm,
                               "nearby_local_filter_soft", std::to_string(ad->get_ocpx_action_type()));
                               thresh = ad->GetCpmThr();
                    }
            }
    }
    // 发现页体验优化 - 外跳广告门槛
    if (enable_shield_explore_feed_jumpout_upgrade) {
      if (explore_feed_jumpout_shield_set &&
          explore_feed_jumpout_shield_set->count(ad->get_ocpx_action_type())) {
        double new_cpm_thr = thresh * new_shield_explore_feed_jumpout_ratio / kBenifitFactor;
        ad->SetCpmThr(new_cpm_thr, CpmThrFromTag::EXPOLRE_FEED_CPM_THR, true);
          RANK_DOT_STATS(session_data, new_cpm_thr, "new_explore_feed_jumpout_ratio");
        thresh = ad->GetCpmThr();
      }
    } else if (enable_shield_explore_feed_jumpout) {
      if (explore_feed_jumpout_shield_set &&
          explore_feed_jumpout_shield_set->count(ad->get_ocpx_action_type())) {
        ad->SetCpmThr(thresh * shield_explore_feed_jumpout_ratio,
          CpmThrFromTag::EXPOLRE_FEED_CPM_THR, true);
          RANK_DOT_STATS(session_data, thresh * shield_explore_feed_jumpout_ratio,
          "explore_feed_jumpout_ratio");
        thresh = ad->GetCpmThr();
      }
    }
    // 公域单列 cpm 统一门槛实验（跳过其他门槛）
    if (session_data->get_is_thanos_mix_request() && enable_thanos_mix_unify_cpm_thr) {
      double unify_cpm_thr =
          app_id == "kuaishou" ? rank_unify_cpm_thr_gamora : rank_unify_cpm_thr_nebula;
      ad->SetCpmThr(unify_cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
      thresh = ad->GetCpmThr();
    }
    if (session_data->get_is_thanos_mix_request() &&
        SPDM_enable_dlrank_sep_inner_outer(session_data->get_spdm_ctx())) {
      double unify_cpm_thr = 0.0;
      if (ad->Is(AdFlag::is_inner_loop_ad)) {
        unify_cpm_thr =
          app_id == "kuaishou" ? rank_unify_cpm_thr_gamora_inner : rank_unify_cpm_thr_nebula_inner;
      } else {
        unify_cpm_thr =
          app_id == "kuaishou" ? rank_unify_cpm_thr_gamora_outer : rank_unify_cpm_thr_nebula_outer;
      }
      ad->SetCpmThr(unify_cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
      thresh = ad->GetCpmThr();
    }
    // 同城行业 cpm 分人群门槛设置
    if (enable_nearby_user_group_cpm_strategy && is_valid_user_group && is_valid_cpm_page) {
      ad->SetCpmThr(nearby_user_group_cpm_thr, CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_SOFT_CPM_TRH, true);
      thresh = ad->GetCpmThr();
      RANK_DOT_STATS(session_data, nearby_user_group_cpm_thr,
      "nearby_user_group_cpm_thr_soft", user_group_level);
    }
    if (session_data->get_is_thanos_mix_request() && enable_new_cpm_thr_native) {
      double cpm_thr =
          app_id == "kuaishou" ? new_cpm_thr_gamora_native : new_cpm_thr_nebula_native;
      ad->SetCpmThr(cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
      thresh = ad->GetCpmThr();
    }
    if (session_data->get_is_thanos_mix_request() && enable_thanos_wlevel_cpm_thr_native &&
        (w_level == "W2" || w_level == "W3")) {
      double cpm_thr = 0.0;
      if (w_level == "W2") {
        cpm_thr =
          app_id == "kuaishou" ? w2_cpm_thr_gamora_native : w2_cpm_thr_nebula_native;
      } else {
        cpm_thr =
          app_id == "kuaishou" ? w3_cpm_thr_gamora_native : w3_cpm_thr_nebula_native;
      }
      ad->SetCpmThr(cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
      thresh = ad->GetCpmThr();
    }
    if (enable_kwai_mini_game_rb_thr_adjust && ad->get_first_industry_id_v5() == 1018 &&
        ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) {
      ad->MultiCpmThr(kwai_mini_game_rb_thr_ratio, CpmThrFromTag::CONTENT_CONSUMPTION_INDUSTRY_CPM_THR);
      thresh = ad->GetCpmThr();
      RANK_DOT_STATS(session_data, kwai_mini_game_rb_thr_ratio, "outerloop_cc_ind_cpm_thr_minigame");
    }
    if (enable_big_game_rb_thr_adjust && ad->get_first_industry_id_v5() == 1018 &&
        ad->get_campaign_type() == kuaishou::ad::AdEnum::APP) {
      ad->MultiCpmThr(big_game_rb_thr_ratio, CpmThrFromTag::CONTENT_CONSUMPTION_INDUSTRY_CPM_THR);
      thresh = ad->GetCpmThr();
      RANK_DOT_STATS(session_data, big_game_rb_thr_ratio * 1000, "outerloop_cc_ind_cpm_thr_biggame");
    }
    if (enable_kwai_serial_rb_thr_adjust &&
        (ad->get_second_industry_id_v5() == 2012 || ad->get_second_industry_id_v5() == 2196) &&
        ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
      ad->MultiCpmThr(kwai_serial_rb_thr_ratio, CpmThrFromTag::CONTENT_CONSUMPTION_INDUSTRY_CPM_THR);
      thresh = ad->GetCpmThr();
      RANK_DOT_STATS(session_data, kwai_serial_rb_thr_ratio, "outerloop_cc_ind_cpm_thr_serial");
    }
    if (enable_kwai_fiction_rb_thr_adjust &&
         ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_FICTION_PROMOTION) {
      ad->MultiCpmThr(kwai_fiction_rb_thr_ratio, CpmThrFromTag::CONTENT_CONSUMPTION_INDUSTRY_CPM_THR);
      thresh = ad->GetCpmThr();
      RANK_DOT_STATS(session_data, kwai_fiction_rb_thr_ratio, "outerloop_cc_ind_cpm_thr_fiction");
    }
    // 小游戏分产品/账户打折
    if (SPDM_enable_adjust_mini_game_cpm_thr_v3(session_data->get_spdm_ctx())) {
      double mini_game_extra_load_cpm_thr = 0.0;
      const auto& mini_game_cpm_thr_list_conf = RankKconfUtil::miniGameCpmThrdList();
      if (mini_game_cpm_thr_list_conf != nullptr) {
        auto product_iter = mini_game_cpm_thr_list_conf->find(ad->get_product_name());
        if (product_iter != mini_game_cpm_thr_list_conf->end()) {
          mini_game_extra_load_cpm_thr = product_iter->second;
          RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "mini_game_cpm_thr_after_native_product_v3", product_iter->first);   // NOLINT
        }
        auto account_iter = mini_game_cpm_thr_list_conf->find(absl::StrCat(ad->get_account_id()));
        if (account_iter != mini_game_cpm_thr_list_conf->end()) {
          mini_game_extra_load_cpm_thr = account_iter->second;
          RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "mini_game_cpm_thr_after_native_account_v3", account_iter->first);   // NOLINT
        }
      }
      ad->SetCpmThr(mini_game_extra_load_cpm_thr, CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      thresh = ad->GetCpmThr();
    }
    if (session_data->get_is_thanos_mix_request() && thanos_use_rb_thresh) {
      score = ad->get_rank_benifit();
    }
    if (ks::ad_base::IsFollow(session_data->get_sub_page_id())) {
      if (enable_follow_use_rb_thresh) {
        score = ad->get_rank_benifit();
        ad->SetCpmThr(follow_rb_thresh_value, CpmThrFromTag::FOLLOW_PAGE_SOFT_AD_RB_THR, true);
        thresh = ad->GetCpmThr();
        RANK_DOT_STATS(session_data, score, "follow_rank_benifit_exp");
        RANK_DOT_STATS(session_data, thresh, "follow_rank_benifit_exp");
      }
      RANK_DOT_STATS(session_data, ad->get_rank_benifit(), "follow_rank_benifit");
    }
    if (session_data->get_is_thanos_mix_request() &&
        SPDM_enable_cali_in_dlrank(session_data->get_spdm_ctx())) {
      auto ori_cpm = ad->get_cpm();
      auto ori_bonus = ad->get_rank_benifit() - ori_cpm;
      auto cali_cpm = ori_cpm * SPDM_avg_cpm_cali_ratio_dlrank(session_data->get_spdm_ctx());
      auto cali_bonus = ori_bonus * SPDM_avg_bonus_cali_ratio_dlrank(session_data->get_spdm_ctx());
      score = cali_cpm + cali_bonus;
    }
     // 内循环特定广告门槛降低
    if (enable_inner_slide_gfp_thresh_soft && session_data->get_is_thanos_mix_request() &&
        ad->get_mix_gfp_score() > 0.0) {
      auto& ad_distribution_list =
        session_data->get_rank_request()->ad_request().ad_user_info().ad_ue_distribution_list();
      if (ad_distribution_list.size() >= 36) {
        double live_base_offsets_p10 = -1.0;
        double live_base_offsets_p25 = -1.0;
        double live_base_offsets_p50 = -1.0;
        double video_base_offsets_p10 = -1.0;
        double video_base_offsets_p25 = -1.0;
        double video_base_offsets_p50 = -1.0;
        if (session_data->get_page_id() == 10011) {
          video_base_offsets_p10 = ad_distribution_list.at(0);
          video_base_offsets_p25 = ad_distribution_list.at(1);
          video_base_offsets_p50 = ad_distribution_list.at(2);
          live_base_offsets_p10 = ad_distribution_list.at(3);
          live_base_offsets_p25 = ad_distribution_list.at(4);
          live_base_offsets_p50 = ad_distribution_list.at(5);
        }
        if (session_data->get_page_id() == 11001) {
          video_base_offsets_p10 = ad_distribution_list.at(6);
          video_base_offsets_p25 = ad_distribution_list.at(7);
          video_base_offsets_p50 = ad_distribution_list.at(8);
          live_base_offsets_p10 = ad_distribution_list.at(9);
          live_base_offsets_p25 = ad_distribution_list.at(10);
          live_base_offsets_p50 = ad_distribution_list.at(11);
        }
        double prev_inner_ad_score_threshold = -1.0;
        if (ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
          if (gfp_inner_score_thresh == "p10") {
            prev_inner_ad_score_threshold = live_base_offsets_p10;
          }
          if (gfp_inner_score_thresh == "p25") {
            prev_inner_ad_score_threshold = live_base_offsets_p25;
          }
          if (gfp_inner_score_thresh == "p50") {
            prev_inner_ad_score_threshold = live_base_offsets_p50;
          }
        } else {
          if (gfp_inner_score_thresh == "p10") {
            prev_inner_ad_score_threshold = video_base_offsets_p10;
          }
          if (gfp_inner_score_thresh == "p25") {
            prev_inner_ad_score_threshold = video_base_offsets_p25;
          }
          if (gfp_inner_score_thresh == "p50") {
            prev_inner_ad_score_threshold = video_base_offsets_p50;
          }
        }
        double thresh = app_id == "kuaishou" ? 6.2 : 4.0;
        if (prev_inner_ad_score_threshold > 0 &&
            ad->get_mix_gfp_score() > prev_inner_ad_score_threshold &&
            ad->get_rank_benifit() / 1e6 < thresh) {
          double new_thresh = 0.0;
          auto& conf = inner_slide_page_conf->data().confs();
          auto iter = conf.find(gfp_inner_score_exp_tag);
          if (iter != conf.end()) {
            auto& page_id_coef = ad->Is(AdFlag::is_photo_ad) ? iter->second.photo_coef() :
              (ad->Is(AdFlag::is_p2l_ad) ? iter->second.p2l_coef() : iter->second.live_coef());
            auto iter_coef = page_id_coef.find(absl::StrCat("$0", session_data->get_page_id()));
            if (iter_coef != page_id_coef.end()) {
              thresh = iter_coef->second;
            }
          }
          ad->SetCpmThr(new_thresh, CpmThrFromTag::GLOBAL_CPM_TRH, true);
          new_thresh = ad->GetCpmThr();
          RANK_DOT_STATS(session_data, ad->get_rank_benifit(), "INNER_GFP_LOWER_CPM_THRESH_SOFT",
                          absl::StrCat(ad->get_item_type()));
        }
      }
    }
    if (session_data->get_is_thanos_mix_request() &&
        SPDM_enable_dlrank_sep_inner(session_data->get_spdm_ctx())) {
      double unify_cpm_thr = 0.0;
      if (ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE ||
          ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP) {
        unify_cpm_thr = app_id == "kuaishou" ? rank_unify_cpm_thr_gamora_inner_v2 : rank_unify_cpm_thr_nebula_inner_v2;    // NOLINT
        ad->SetCpmThr(unify_cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
        thresh = ad->GetCpmThr();
      }
    }
    if (enable_refactor_filter_restore_cpm && ad->Is(AdFlag::IsHardAd)) {
      return false;
    }
    RANK_DOT_STATS(session_data, thresh, "final_cpm_thresh", "soft", user_group_level);
    // 发现页内流 pid load 调控门槛，移动到最后生效
    if (enable_feed_explore_load_control_move_last && session_data->get_is_explore_feed_inner()) {
      set_explore_unify_ecpm_threshold(ad);
      thresh = ad->GetCpmThr();
    }
    if (SPDM_enable_force_recall_by_ad_type(session_data->get_spdm_ctx())
        && ad->get_is_effect_recall_ad()) {
      RANK_DOT_COUNT(session_data, 1,
        "force_recall_by_ad_type",
        "recall_by_ad_type_tag_skip_filter",
        "ecpm");
      return false;
    }
    if ((SPDM_enable_force_effect_recall(session_data->get_spdm_ctx()) ||
        SPDM_enable_force_effect_recall_new(session_data->get_spdm_ctx()))
        && ad->get_is_effect_recall_ad()) {
      RANK_DOT_COUNT(session_data, 1, "force_effect_recall", "recall_tag_skip_filter", "ecpm");
      return false;
    }
    if (score <= thresh) {
      return true;
    }
    return false;
  };
  FINISH_FILTER()
  // ecpm 门槛监控
  if (ad_base::AdRandom::GetDouble() >= native_unify_perf_ratio) {  // 控制一下打点频率
    return;
  }
  std::string form = kuaishou::ad::AdEnum_InteractiveForm_Name(
      session_data->get_rank_request()->ad_request().interactive_form());
  std::string flow = kuaishou::ad::AdEnum_AdRequestFlowType_Name(
      session_data->get_rank_request()->ad_request().ad_request_flow_type());
  std::string user_tag = session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
  std::string soft_hard_union_exp_tag = SPDM_soft_hard_union_exp_tag(session_data->get_spdm_ctx());
  for (auto ad : ad_list->FullAds()) {
    std::string in_out = ad->Is(AdFlag::is_inner_loop_ad) ? "内循环" : "外循环";
    std::string item = ad->Is(AdFlag::is_live) ? "直播" : (ad->Is(AdFlag::is_p2l) ? "引流" : "作品");
    std::string source = ad->Is(AdFlag::is_fanstop) ? (ad->Is(AdFlag::is_new_inner_fanstop) ? "内粉" : "外粉") : "广告";  // NOLINT
    std::string cpm_tag = std::to_string(ad->get_cpm_tag());
    double cpm_thr = ad->get_cpm_thr();
    std::string cpm_base_tag =
        absl::StrCat(ad->Attr(ItemIdx::cpm_thr_base_tag).GetIntValue(ad->AttrIndex()).value_or(0));
    int64_t cpm_thr_base = ad->Attr(ItemIdx::cpm_thr_base).GetIntValue(ad->AttrIndex()).value_or(0);
    double cpm_thr_ratio =
        ad->Attr(ItemIdx::cpm_thr_ratio).GetDoubleValue((ad->AttrIndex())).value_or(1.0) * 1e+6;
    RANK_DOT_STATS(session_data, cpm_thr, "native_ecpm_threshold", cpm_tag, in_out, item, source);
    RANK_DOT_STATS(session_data, cpm_thr_base, "native_cpm_thr_base", cpm_base_tag, in_out, item, source);
    RANK_DOT_STATS(session_data, cpm_thr_ratio, "native_cpm_thr_ratio", cpm_tag, in_out, item, source);
    RANK_DOT_STATS(session_data, cpm_thr, "native_ecpm_threshold_pv", cpm_tag, flow, form, user_tag);
    RANK_DOT_STATS(session_data, cpm_thr_base, "native_cpm_thr_base_pv", cpm_base_tag, flow, form, user_tag);
    RANK_DOT_STATS(session_data, cpm_thr_ratio, "native_cpm_thr_ratio_pv", cpm_tag, flow, form, user_tag);

    RANK_DOT_STATS(session_data, cpm_thr, "native_ecpm_threshold_2", soft_hard_union_exp_tag, cpm_tag, in_out, item, source);  // NOLINT
  }
}  // NOLINT

void KminiGameCpmFilter(ContextData* session_data, AdList* ad_list, const FilterInfo& filter_info) {
  const auto& ocpc_action_type_kminigame_iaa = RankKconfUtil::ocpcActionTypeKminiGameIAA();
  double cpm_threshold_ratio = SPDM_cpm_threshold_ratio(session_data->get_spdm_ctx());
  bool enable_kminigame_cpm_thresh = SPDM_enable_kminigame_cpm_thresh(session_data->get_spdm_ctx());
  // 返回 true 代表需要过滤
  auto filter = [&] (AdCommon* p_ad) -> bool {
    bool admit = p_ad->Is(AdFlag::is_game_ad) &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE &&
        ocpc_action_type_kminigame_iaa &&
        ocpc_action_type_kminigame_iaa->count(kuaishou::ad::AdActionType_Name(
          p_ad->get_ocpx_action_type())) > 0;
    bool filt = false;
    if (admit) {
      double avg_cpm = ks::ad_rank::RankingData::GetInstance()->GetProductKeyLtv0AvgValue(
        absl::StrCat(p_ad->get_product_name(), "_cpm"), p_ad->get_cpm());
      if (enable_kminigame_cpm_thresh && p_ad->get_cpm() < avg_cpm * cpm_threshold_ratio) {
        filt = true;
      }
      RANK_DOT_STATS(session_data, avg_cpm, "kminigame_cpm_threshold", "avg_cpm",
        kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()),
                     p_ad->get_product_name());
      RANK_DOT_STATS(session_data, filt, "kminigame_cpm_threshold", "filt",
        kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()),
                     p_ad->get_product_name());
    }
    return filt;
  };
  FINISH_FILTER()
}

void RecoLiveCpmFilter(ContextData* session_data, AdList* ad_list, const FilterInfo& filter_info) {
  bool enable_reco_live_bigr_cpm_filter = SPDM_enable_reco_live_bigr_cpm_filter(session_data->get_spdm_ctx());
  double reco_live_bigr_cpm_thres = SPDM_reco_live_bigr_cpm_thres(session_data->get_spdm_ctx());
  const std::string live_user_tag = session_data->get_rank_request()->ad_request().ad_user_info().live_big_user_tag();  // NOLINT
  bool admit_filter = false;
  if ((!live_user_tag.empty() || live_user_tag.length() != 0) && enable_reco_live_bigr_cpm_filter) { // 只要字段不为空 说明是直播重点用户 NOLINT
    admit_filter = true;
  }
  // 返回 true 代表需要过滤
  auto filter = [&] (AdCommon* p_ad) -> bool {
    bool filt = false;
    if (admit_filter) {
      if (p_ad->get_cpm() < reco_live_bigr_cpm_thres) {
        filt = true;
      }
    }
    return filt;
  };
  FINISH_FILTER()
}

void RankBenefitFilter(ContextData* session_data, AdList* ad_list, const FilterInfo& filter_info) {
  if (!session_data->get_is_thanos_mix_request()) {
    return;
  }
  bool enable_rank_benefit_filter = SPDM_enable_rank_benefit_filter(session_data->get_spdm_ctx());
  if (!enable_rank_benefit_filter) {
    return;
  }
  double thresh = SPDM_default_rank_benefit_thresh(session_data->get_spdm_ctx()) * kBenifitFactor;
  // 返回 true 代表需要过滤
  auto filter = [&] (AdCommon* p_ad) -> bool {
    if (SPDM_enable_force_recall_by_ad_type(session_data->get_spdm_ctx())
        && p_ad->get_is_effect_recall_ad()) {
      RANK_DOT_COUNT(session_data, 1,
        "force_recall_by_ad_type",
        "recall_by_ad_type_tag_skip_filter",
        "rank_benifit");
      return false;
    }
    if ((SPDM_enable_force_effect_recall(session_data->get_spdm_ctx()) ||
        SPDM_enable_force_effect_recall_new(session_data->get_spdm_ctx())) &&
        p_ad->get_is_effect_recall_ad()) {
      RANK_DOT_COUNT(session_data, 1, "force_effect_recall", "recall_tag_skip_filter", "rank_benifit");
      return false;
    }
    if (p_ad->get_rank_benifit() < thresh) {
      return true;
    }
    return false;
  };
  FINISH_FILTER()
}

bool IsSkipCpmFilter(const AdCommon& ad) {
  // 圈定的广告跳过过滤
  if (ad.get_is_skip_ranking_cpm_filter() || ad.get_is_skip_ranking_filter()) {
    return true;
  }
  return false;
}


void NormalCpmFilter(ContextData* session_data,
               AdList* ad_list,
               const FilterInfo& filter_info) {
  auto cpm_filter_matrix_app_id = RankKconfUtil::cpmFilterMatrixAppId();
  bool enable_matrix_app_cpm_filter = SPDM_enable_matrix_app_cpm_filter(session_data->get_spdm_ctx()) &&
       cpm_filter_matrix_app_id &&
       cpm_filter_matrix_app_id->count(session_data->get_pos_manager_base().GetRequestAppId());
  double matrix_app_cpm_filter_threshold = SPDM_matrix_app_cpm_filter_threshold(session_data->get_spdm_ctx());
  bool enable_matrix_app_cpm_threshold_separate =
      SPDM_enable_matrix_app_cpm_threshold_separate(session_data->get_spdm_ctx());
  auto matrix_app_cpm_thre_map = RankKconfUtil::MatrixAppCpmThreMap();
  bool enable_follow_rank_refactor_skip_NormalCpmFilter =  SPDM_enable_follow_rank_refactor_skip_NormalCpmFilter(session_data->get_spdm_ctx());  // NOLINT
  auto follow_subpageid = RankKconfUtil::follow_subpageid();
  enable_follow_rank_refactor_skip_NormalCpmFilter = enable_follow_rank_refactor_skip_NormalCpmFilter && follow_subpageid != nullptr && follow_subpageid->count(session_data->get_sub_page_id()) > 0;  // NOLINT
  if (enable_follow_rank_refactor_skip_NormalCpmFilter) {
    return;
  }
  if (enable_matrix_app_cpm_threshold_separate && matrix_app_cpm_thre_map) {
    auto iter = matrix_app_cpm_thre_map->find(session_data->get_pos_manager_base().GetRequestAppId());
    if (iter != matrix_app_cpm_thre_map->end()) {
      matrix_app_cpm_filter_threshold = iter->second;
    }
  }
  // 矩阵流量跳过 CPM 过滤
  if (engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(
          session_data->get_pos_manager_base().GetRequestAppId()) &&
      !enable_matrix_app_cpm_filter) {
    return;
  }
  // 激励流量跳过 CPM 过滤
  if (session_data->get_is_incentive() &&
      !(session_data->get_pos_manager_base().IsInspireMerchant() &&
        SPDM_enable_incentive_merchant_normal_cpm_filter(session_data->get_spdm_ctx()))) {
    return;
  }
  // MONITOR_NEW_CREATIVE_V2("ad_rank.ranking_before_cpm_thr_ad_num");

  int32_t count_filter_by_zero_cpm = 0;
  // 调整 cpm 门槛值的系数
  bool enable_product_adjust_cpm_threshold_ratio =
    SPDM_enable_product_adjust_cpm_threshold_ratio(session_data->get_spdm_ctx());
  auto product_cpm_threshold_map = RankKconfUtil::productCpmThresholdRatio();
  double ad_rank_cpm_filter_threshold_factor = 1.0;
  double ad_rank_cpm_filter_threshold_factor_dsp_inner_gamora =
      SPDM_ad_rank_cpm_filter_threshold_factor_dsp_inner_gamora(session_data->get_spdm_ctx());
  double ad_rank_cpm_filter_threshold_factor_dsp_inner_nebula =
      SPDM_ad_rank_cpm_filter_threshold_factor_dsp_inner_nebula(session_data->get_spdm_ctx());
  double ad_rank_cpm_filter_threshold_factor_dsp_outer_gamora =
      SPDM_ad_rank_cpm_filter_threshold_factor_dsp_outer_gamora(session_data->get_spdm_ctx());
  double ad_rank_cpm_filter_threshold_factor_dsp_outer_nebula =
      SPDM_ad_rank_cpm_filter_threshold_factor_dsp_outer_nebula(session_data->get_spdm_ctx());
  bool enable_force_cpm_threshold = SPDM_enable_force_cpm_threshold(session_data->get_spdm_ctx());
  // 硬广 cpm 门槛过滤时去掉 bonus 因子
  bool enable_set_hard_origianl_cpm_thr =
      SPDM_enable_set_hard_origianl_cpm_thr(session_data->get_spdm_ctx());
  const std::string w_level =
    session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
  bool is_low_w_level_user = w_level == "W1" || w_level == "W2";
  bool enable_outer_loop_dynamic_cpm_thres =
    SPDM_enable_outer_loop_dynamic_cpm_thres(session_data->get_spdm_ctx());
  std::string outer_loop_dnc_opt_dimension =
    SPDM_outer_loop_dnc_opt_dimension(session_data->get_spdm_ctx());
  double outer_loop_dynamic_cpm_thres_alpha =
    SPDM_outer_loop_dynamic_cpm_thres_alpha(session_data->get_spdm_ctx());
  double outer_loop_dynamic_cpm_thres_min =
    SPDM_outer_loop_dynamic_cpm_thres_min_hard(session_data->get_spdm_ctx());
  bool outer_loop_dnc_opt_for_potential_nc =
    SPDM_outer_loop_dynamic_cpm_thres_for_potential_nc(session_data->get_spdm_ctx());
  bool outer_loop_potential_nc_bugfix = SPDM_outer_loop_potential_nc_bugfix(session_data->get_spdm_ctx());
  bool outer_loop_dnc_hc_new_key_prefix =
    SPDM_outer_loop_dnc_hc_new_key_prefix(session_data->get_spdm_ctx());
  std::string effect_w_level_set =
    SPDM_effect_w_level_set(session_data->get_spdm_ctx());
  std::vector<std::string> effect_w_level_set_split =
    absl::StrSplit(effect_w_level_set, ",", absl::SkipEmpty());
  bool is_target_w_level = true;
  if (effect_w_level_set != "all" &&
      std::find(effect_w_level_set_split.begin(), effect_w_level_set_split.end(), w_level) ==
      effect_w_level_set_split.end()) {
    is_target_w_level = false;
  }
  bool is_potential_nc_user =
      session_data->get_rank_request()->is_outerloop_potential_nc_user();
  double nc_user_cpm_ratio = SPDM_outer_loop_nc_user_cpm_hard_ratio(session_data->get_spdm_ctx());
  double nc_user_cpm_min = SPDM_outer_loop_nc_user_cpm_hard_min(session_data->get_spdm_ctx());
  bool enable_nc_model = SPDM_enable_nc_model_cpm_thres(session_data->get_spdm_ctx());
  bool enable_nc_retrieval = SPDM_enable_nc_retrieval_cpm_thres(session_data->get_spdm_ctx());
  auto nc_product_set = RankKconfUtil::outerloopNcProductSet();
  auto nc_retrieval_set = RankKconfUtil::outerloopNcRetrievalSet();
  bool enable_nc_reco_v2 = SPDM_enable_nc_reco_cpm_thres_v2(session_data->get_spdm_ctx());
  bool enable_outer_nc_reco_limit_wlevel =
      SPDM_enable_outer_nc_reco_limit_wlevel(session_data->get_spdm_ctx());
  const std::string& wlevel_limit =
    SPDM_outerloop_nc_reco_wlevel(session_data->get_spdm_ctx());
  std::vector<std::string> wlevel_limit_set =
    absl::StrSplit(wlevel_limit, ",", absl::SkipEmpty());
  if (enable_outer_nc_reco_limit_wlevel &&
      std::find(wlevel_limit_set.begin(),
        wlevel_limit_set.end(), w_level) == wlevel_limit_set.end()) {
    enable_nc_reco_v2 = false;
  }
  // 根据转化率 自适应调整 nc model 策略门槛
  bool enable_set_cpm_based_on_nc_score =
      SPDM_enable_set_cpm_based_on_nc_score(session_data->get_spdm_ctx());
  double nc_user_cpm_min_v2 = SPDM_outer_dnc_nc_user_cpm_min_v2_hard(session_data->get_spdm_ctx());
  double nc_model_cpm_adjust_ratio =
      SPDM_outer_dnc_nc_model_cpm_adjust_ratio_hard(session_data->get_spdm_ctx());
  double nc_model_cpm_lower_bound =
      SPDM_outer_dnc_nc_model_cpm_lower_bound_hard(session_data->get_spdm_ctx());
  double nc_model_cpm_upper_bound =
      SPDM_outer_dnc_nc_model_cpm_upper_bound_hard(session_data->get_spdm_ctx());
  const auto* nc_model_score_map_ptr = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_map<int64_t, double>>("nc_model_prod_score_map");
  bool enable_nc_llm = SPDM_enable_nc_llm_prod_cpm_thres(session_data->get_spdm_ctx());
  double vtr_thr = SPDM_nc_reco_vtr_cpm_thres(session_data->get_spdm_ctx());
  double lvtr_thr = SPDM_nc_reco_lvtr_cpm_thres(session_data->get_spdm_ctx());
  if (SPDM_enable_update_outerloop_nc_kconf(session_data->get_spdm_ctx())) {
    nc_product_set = RankKconfUtil::outerloopNcProductSetV2();
    nc_retrieval_set = RankKconfUtil::outerloopNcRetrievalSetV2();
  }
  const auto& nc_ocpx_set = RankKconfUtil::outerloopNcOcpxSet();
  // 根据长期价值 ltv 自适应调整 nc model 策略门槛
  bool enable_set_cpm_threshold_based_on_ltv =
    SPDM_enable_set_cpm_threshold_based_on_ltv(session_data->get_spdm_ctx());
  double alpha =
    SPDM_set_cpm_threshold_based_on_ltv_alpha_hard(session_data->get_spdm_ctx());
  double nc_model_cpm_threshold_upper_bound =
    SPDM_set_cpm_threshold_based_on_ltv_upperbound_hard(session_data->get_spdm_ctx());
  double nc_model_cpm_threshold_lower_bound =
    SPDM_set_cpm_threshold_based_on_ltv_lowerbound_hard(session_data->get_spdm_ctx());
  bool enable_nc_limit_ocpx = SPDM_enable_outerloop_nc_limit_ocpx(session_data->get_spdm_ctx());
  //  根据 dnc values 字段调整 nc model 策略门槛
  bool enable_set_cpm_threshold_based_on_dnc_values =
    SPDM_enable_set_cpm_threshold_based_on_dnc_values(session_data->get_spdm_ctx());
  bool enable_set_cpm_threshold_based_on_dnc_score =
    SPDM_enable_set_cpm_threshold_based_on_dnc_score(session_data->get_spdm_ctx());
  bool enable_set_cpm_threshold_based_on_dnc_ltv =
    SPDM_enable_set_cpm_threshold_based_on_dnc_ltv(session_data->get_spdm_ctx());
  double set_cpm_threshold_based_on_dnc_value_lowerbound =
    SPDM_set_cpm_threshold_based_on_dnc_value_lowerbound(session_data->get_spdm_ctx());
  const auto* interest_prod_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<std::string>>("outerloop_interest_product");
  const auto* llm_prod_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<std::string>>("llm_user_prod_interest_list");
  const auto* interest_industry_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<std::string>>("outerloop_interest_industry");
  const auto* interest_sec_ind_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<std::string>>("outerloop_interest_second_industry");
  const auto* nc_model_prod_set = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<int64_t>>("nc_model_prod_list");
  // 外循环 I2I 兴趣强化实验
  bool enable_mini_game_i2i_enhance = SPDM_enable_mini_game_i2i_enhance(session_data->get_spdm_ctx());
  bool enable_outerloop_cross_ind_i2i_enhance =
    SPDM_enable_outerloop_cross_ind_i2i_enhance(session_data->get_spdm_ctx());
  double mini_game_i2i_enhance_thres = SPDM_mini_game_i2i_enhance_thres_hard(session_data->get_spdm_ctx());
  double outerloop_cross_ind_thres = SPDM_outerloop_cross_ind_thres_hard(session_data->get_spdm_ctx());
  auto outerloop_i2i_enhance_ocpx_white_set = RankKconfUtil::outerloopNcGoalOcpxSet();
  bool enable_outerloop_ac_retrieval_enhance =
    SPDM_enable_outerloop_ac_retrieval_enhance(session_data->get_spdm_ctx());
  auto outerloop_ac_retrieval_set = RankKconfUtil::outerloopAcRetrievalSet();
  bool enable_w_level_limit_i2i_enhance = SPDM_enable_w_level_limit_i2i_enhance(session_data->get_spdm_ctx());
  std::string effect_w_level_set_i2i_enhance =
    SPDM_effect_w_level_set_i2i_enhance(session_data->get_spdm_ctx());
  std::vector<std::string> effect_w_level_i2i_enhance =
    absl::StrSplit(effect_w_level_set_i2i_enhance, ",", absl::SkipEmpty());
  if (enable_w_level_limit_i2i_enhance && effect_w_level_set_i2i_enhance != "all" &&
      std::find(effect_w_level_i2i_enhance.begin(), effect_w_level_i2i_enhance.end(), w_level) ==
      effect_w_level_i2i_enhance.end()) {
    enable_outerloop_cross_ind_i2i_enhance = false;
  }
  auto outerloop_ac_product_set = RankKconfUtil::outerloopAcProductSet();
  bool enable_outerloop_ac_product_set = SPDM_enable_outerloop_ac_product_set(session_data->get_spdm_ctx());
  auto ocpx_industry_map_config = RankKconfUtil::OuterLoopAcIndustry();
  bool enable_outerloop_ac_enhance_cpm_threshold =
    SPDM_enable_outerloop_ac_enhance_cpm_threshold(session_data->get_spdm_ctx());
  bool enable_outerloop_ac_converted_industry_limit =
    SPDM_enable_outerloop_ac_converted_industry_limit_rank(session_data->get_spdm_ctx());
  bool enable_outerloop_nc_max_cpm_thres =
    SPDM_enable_outerloop_nc_max_cpm_thres(session_data->get_spdm_ctx());
  double outerloop_nc_max_cpm_thres = SPDM_outerloop_nc_max_cpm_thres_hard(session_data->get_spdm_ctx());
  bool enable_outerloop_low_active_ac_max_cpm_thres =
    SPDM_enable_outerloop_low_active_ac_max_cpm_thres(session_data->get_spdm_ctx());
  double outerloop_ac_max_cpm_thres = SPDM_outerloop_ac_max_cpm_thres_hard(session_data->get_spdm_ctx());
  bool enable_outerloop_low_active_ac_max_cpm_ratio =
    SPDM_enable_outerloop_low_active_ac_max_cpm_ratio(session_data->get_spdm_ctx());
  double outerloop_ac_max_cpm_ratio = SPDM_outerloop_ac_max_cpm_ratio_hard(session_data->get_spdm_ctx());
  bool enable_outerloop_nc_max_user_limit =
    SPDM_enable_outerloop_nc_max_user_limit(session_data->get_spdm_ctx());
  int is_outerloop_low_active_ac = session_data->common_r_->
    GetIntCommonAttr("is_outerloop_low_active_cnt").value_or(0);
  auto user_group_level =
      session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
  bool enable_outerloop_ac_retrieval_diversity =
    SPDM_enable_outerloop_ac_retrieval_diversity(session_data->get_spdm_ctx());
  int64_t outerloop_ac_retrieval_diversity_threshold =
    SPDM_outerloop_ac_retrieval_diversity_threshold(session_data->get_spdm_ctx());
  const auto* outerloop_impression_cnt = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_map<int64_t, int64_t>>("outerloop_impression_cnt");
  bool enable_outerloop_ac_enhance_cpm_ratio =
    SPDM_enable_outerloop_ac_enhance_cpm_ratio(session_data->get_spdm_ctx());
  double outerloop_ac_enhance_cpm_ratio =
    SPDM_outerloop_ac_enhance_cpm_ratio(session_data->get_spdm_ctx());
  bool enable_buyer_home_page_modify_cpm_thres =
    SPDM_enable_buyer_home_page_modify_cpm_thres(session_data->get_spdm_ctx());
  double buyer_home_page_cpm_thres_new =
    SPDM_buyer_home_page_cpm_thres_new(session_data->get_spdm_ctx());
  bool enable_outerloop_nc_cpm_thres_plugin =
    SPDM_enable_outerloop_nc_cpm_thres_plugin(session_data->get_spdm_ctx());
  bool enable_outerloop_ac_shallow_action =
    SPDM_enable_outerloop_ac_shallow_action(session_data->get_spdm_ctx());
  double outerloop_ac_enhance_cpm_ratio_comment =
    SPDM_outerloop_ac_enhance_cpm_ratio_comment(session_data->get_spdm_ctx());
  double outerloop_ac_enhance_cpm_ratio_play =
    SPDM_outerloop_ac_enhance_cpm_ratio_play(session_data->get_spdm_ctx());
  double outerloop_ac_enhance_cpm_ratio_like =
    SPDM_outerloop_ac_enhance_cpm_ratio_like(session_data->get_spdm_ctx());
  bool enable_thanos_mix_unify_cpm_thr =
    SPDM_enable_thanos_mix_unify_cpm_thr(session_data->get_spdm_ctx());
  bool enable_dlrank_sep_inner_outer =
    SPDM_enable_dlrank_sep_inner_outer(session_data->get_spdm_ctx());
  bool enable_dlrank_sep_inner =
    SPDM_enable_dlrank_sep_inner(session_data->get_spdm_ctx());
  double rank_unify_cpm_thr_gamora =
    SPDM_rank_unify_cpm_thr_gamora(session_data->get_spdm_ctx());
  double rank_unify_cpm_thr_nebula =
    SPDM_rank_unify_cpm_thr_nebula(session_data->get_spdm_ctx());
  bool enable_inner_slide_gfp_thresh =
    SPDM_enable_inner_slide_gfp_thresh(session_data->get_spdm_ctx());
  std::string gfp_inner_score_thresh =
    SPDM_gfp_inner_score_thresh(session_data->get_spdm_ctx());
  std::string gfp_inner_score_exp_tag =
    SPDM_gfp_inner_score_exp_tag(session_data->get_spdm_ctx());
  auto inner_slide_page_conf = RankKconfUtil::innerSlideGfpThreshPageConf();
  double rank_unify_cpm_thr_gamora_inner = rank_unify_cpm_thr_gamora;
  double rank_unify_cpm_thr_gamora_outer = rank_unify_cpm_thr_gamora;
  double rank_unify_cpm_thr_nebula_inner = rank_unify_cpm_thr_nebula;
  double rank_unify_cpm_thr_nebula_outer = rank_unify_cpm_thr_nebula;
  double rank_unify_cpm_thr_gamora_inner_v2 = rank_unify_cpm_thr_gamora;
  double rank_unify_cpm_thr_nebula_inner_v2 = rank_unify_cpm_thr_nebula;
  bool enable_new_cpm_thr =
    SPDM_enable_new_cpm_thr(session_data->get_spdm_ctx());
  double new_cpm_thr_gamora =
    SPDM_new_cpm_thr_gamora(session_data->get_spdm_ctx());
  double new_cpm_thr_nebula =
    SPDM_new_cpm_thr_nebula(session_data->get_spdm_ctx());
  bool enable_thanos_wlevel_cpm_thr =
    SPDM_enable_thanos_wlevel_cpm_thr(session_data->get_spdm_ctx());
  double w2_cpm_thr_gamora =
    SPDM_w2_cpm_thr_gamora(session_data->get_spdm_ctx());
  double w2_cpm_thr_nebula =
    SPDM_w2_cpm_thr_nebula(session_data->get_spdm_ctx());
  double w3_cpm_thr_gamora =
    SPDM_w3_cpm_thr_gamora(session_data->get_spdm_ctx());
  double w3_cpm_thr_nebula =
    SPDM_w3_cpm_thr_nebula(session_data->get_spdm_ctx());
  bool thanos_use_rb_thresh = SPDM_thanos_use_rb_thresh(session_data->get_spdm_ctx());
  bool enable_cali_in_dlrank = SPDM_enable_cali_in_dlrank(session_data->get_spdm_ctx());
  if (SPDM_enable_thanos_mix_unify_cpm_thr_new(session_data->get_spdm_ctx())) {
    enable_thanos_mix_unify_cpm_thr =
        SPDM_enable_thanos_mix_unify_cpm_thr_v2(session_data->get_spdm_ctx());
    rank_unify_cpm_thr_gamora = SPDM_rank_unify_cpm_thr_gamora_v2(session_data->get_spdm_ctx());
    rank_unify_cpm_thr_nebula = SPDM_rank_unify_cpm_thr_nebula_v2(session_data->get_spdm_ctx());
    thanos_use_rb_thresh = SPDM_thanos_use_rb_thresh_v2(session_data->get_spdm_ctx());
  }
  const auto &unify_thr_map = RankKconfUtil::RankUnifyThrMap();
  if (SPDM_enable_rank_unify_thr_config(session_data->get_spdm_ctx()) && unify_thr_map != nullptr) {
    auto tag = SPDM_rank_unify_thr_config_tag(session_data->get_spdm_ctx());
    auto iter_gamora = unify_thr_map->find(absl::StrCat(tag, "_gamora"));
    if (iter_gamora != unify_thr_map->end()) {
    rank_unify_cpm_thr_gamora = iter_gamora->second;
    }
    auto iter_nebula = unify_thr_map->find(absl::StrCat(tag, "_nebula"));
    if (iter_nebula != unify_thr_map->end()) {
    rank_unify_cpm_thr_nebula = iter_nebula->second;
    }
    std::string user_level = session_data->get_is_unlogin_user() ? "unlogin" : w_level;
    iter_gamora = unify_thr_map->find(absl::StrCat(tag, "_gamora_", user_level));
    if (iter_gamora != unify_thr_map->end()) {
      rank_unify_cpm_thr_gamora = iter_gamora->second;
    }
    iter_nebula = unify_thr_map->find(absl::StrCat(tag, "_nebula_", user_level));
    if (iter_nebula != unify_thr_map->end()) {
      rank_unify_cpm_thr_nebula = iter_nebula->second;
    }
  }
  if (enable_dlrank_sep_inner_outer && unify_thr_map != nullptr) {
    auto tag = SPDM_rank_unify_thr_config_tag_v2(session_data->get_spdm_ctx());
    auto iter_gamora_inner = unify_thr_map->find(absl::StrCat(tag, "_gamora_inner"));
    auto iter_gamora_outer = unify_thr_map->find(absl::StrCat(tag, "_gamora_outer"));
    auto iter_nebula_inner = unify_thr_map->find(absl::StrCat(tag, "_nebula_inner"));
    auto iter_nebula_outer = unify_thr_map->find(absl::StrCat(tag, "_nebula_outer"));
    if (iter_gamora_inner != unify_thr_map->end()) {
    rank_unify_cpm_thr_gamora_inner = iter_gamora_inner->second;
    }
    if (iter_gamora_outer != unify_thr_map->end()) {
    rank_unify_cpm_thr_gamora_outer = iter_gamora_outer->second;
    }
    if (iter_nebula_inner != unify_thr_map->end()) {
    rank_unify_cpm_thr_nebula_inner = iter_nebula_inner->second;
    }
    if (iter_nebula_outer != unify_thr_map->end()) {
    rank_unify_cpm_thr_nebula_outer = iter_nebula_outer->second;
    }
  }
  bool enable_live_inner_thr = false;
  double live_inner_thr = 0.0;
  if (SPDM_enable_live_inner_thr(session_data->get_spdm_ctx())) {
    enable_live_inner_thr = GetLiveInnerCpmThr(session_data, &live_inner_thr, "hard");
  }
  const auto &inner_unify_thr_map = RankKconfUtil::RankInnerUnifyThrMap();
  if (enable_dlrank_sep_inner && inner_unify_thr_map != nullptr) {
    auto tag = SPDM_rank_unify_thr_inner_config_tag_v2(session_data->get_spdm_ctx());
    auto iter_gamora_inner_v2 = inner_unify_thr_map->find(absl::StrCat(tag, "_gamora_inner"));
    auto iter_nebula_inner_v2 = inner_unify_thr_map->find(absl::StrCat(tag, "_nebula_inner"));
    if (iter_gamora_inner_v2 != inner_unify_thr_map->end()) {
    rank_unify_cpm_thr_gamora_inner_v2 = iter_gamora_inner_v2->second;
    } else {
      rank_unify_cpm_thr_gamora_inner_v2 = rank_unify_cpm_thr_gamora_inner;
    }
    if (iter_nebula_inner_v2 != inner_unify_thr_map->end()) {
      rank_unify_cpm_thr_nebula_inner_v2 = iter_nebula_inner_v2->second;
    } else {
      rank_unify_cpm_thr_nebula_inner_v2 = rank_unify_cpm_thr_nebula_inner;
    }
  }
  bool enable_inner_explore_live_cpm_threshold =
      SPDM_enable_inner_explore_live_cpm_threshold(session_data->get_spdm_ctx());
  double inner_explore_live_cpm_threshold_hard =
      SPDM_inner_explore_live_cpm_threshold_hard(session_data->get_spdm_ctx());
  bool enable_unify_explore_cpm_normal_ad =
      SPDM_enable_unify_explore_cpm_normal_ad(session_data->get_spdm_ctx());
  auto explore_feed_jumpout_shield_subpageid = RankKconfUtil::exploreFeedJumpOutShieldSubPageId();
  auto explore_feed_jumpout_shield_set = RankKconfUtil::exploreFeedJumpOutShield();
  bool enable_shield_explore_feed_jumpout =
       SPDM_enable_shield_explore_feed_jumpout(session_data->get_spdm_ctx()) &&
       explore_feed_jumpout_shield_subpageid &&
       explore_feed_jumpout_shield_subpageid->count(session_data->get_sub_page_id());
  double shield_explore_feed_jumpout_ratio =
       SPDM_shield_explore_feed_jumpout_ratio(session_data->get_spdm_ctx());
  bool disable_skip_cpm_thr = SPDM_disable_skip_cpm_thr(session_data->get_spdm_ctx());
  bool enable_outerloop_low_active_skip_duanju =
    SPDM_enable_outerloop_low_active_skip_duanju(session_data->get_spdm_ctx());
  bool enable_outerloop_low_active_ocpx_blackset =
    SPDM_enable_outerloop_low_active_ocpx_blackset(session_data->get_spdm_ctx());
  bool enable_refactor_filter_restore_cpm =
    SPDM_enable_refactor_filter_restore_cpm(session_data->get_spdm_ctx());
  bool enable_feed_explore_rb_thr = SPDM_enable_feed_explore_rb_thr(session_data->get_spdm_ctx());
  bool is_feed_explore = ks::ad_base::IsFeedExploreRequest(session_data->get_sub_page_id());
  bool is_inner_explore = ks::ad_base::IsInnerExploreRequest(session_data->get_sub_page_id());
  bool enable_nearby_local_ecpm_switch = SPDM_enable_nearby_local_ecpm_switch(session_data->get_spdm_ctx());
  bool enable_nearby_ecpm_open_switch = SPDM_enable_nearby_ecpm_open_switch(session_data->get_spdm_ctx());
  double nearby_local_hard_ecpm = SPDM_nearbyLocalHardEcpmThreshold();
  std::shared_ptr<absl::flat_hash_set<int32_t>>
    nearby_local_industry_whitelist = RankKconfUtil::nearbyIndustryList();
  auto nearby_local_filter_ocpx_whitelist =  RankKconfUtil::nearbyFilterOcpxSet();
  auto nearby_local_filter_page_id_whitelist =  RankKconfUtil::nearbyFilterPageId();
  auto is_valid_page_id =
  nearby_local_filter_page_id_whitelist->count(session_data->get_page_id()) > 0;
  auto nearby_user_group_filter_list = RankKconfUtil::nearbyUserGroupLevelConfig();
  auto nearby_page_id_filter_list = RankKconfUtil::nearbyCpmPageIdConfig();
  auto enable_nearby_user_group_cpm_strategy =
  SPDM_enable_nearby_user_group_cpm_strategy(session_data->get_spdm_ctx());
  auto nearby_user_group_cpm_thr = SPDM_nearby_user_group_cpm_thr(session_data->get_spdm_ctx());
  bool enable_follow_hard_photo_ecpm_thr = SPDM_enable_follow_hard_photo_ecpm_thr(session_data->get_spdm_ctx()); // NOLINT
  double follow_hard_photo_ecpm_thr_ratio = SPDM_follow_hard_photo_ecpm_thr_ratio(session_data->get_spdm_ctx()); // NOLINT
  double follow_hard_photo_ecpm_thr_value = SPDM_follow_hard_photo_ecpm_thr_value(session_data->get_spdm_ctx()); // NOLINT
  auto lsp_native_explore_thr_conf = RankKconfUtil::lspNativeUserLayeredExploreThrConf();
  const std::string& lsp_native_explore_thr_exp_name =
    SPDM_lsp_native_user_layered_explore_thr_exp_name(session_data->get_spdm_ctx());
  bool enable_lsp_native_user_layered_explore_thr =
       SPDM_enable_lsp_native_user_layered_explore_thr(session_data->get_spdm_ctx()) &&
       lsp_native_explore_thr_conf != nullptr &&
       lsp_native_explore_thr_conf->data().exp_list().contains(lsp_native_explore_thr_exp_name);

  bool enable_adjust_ecpmthr_innerloopbigcard_secondreq =
    SPDM_enable_adjust_ecpmthr_innerloopbigcard_secondreq(session_data->get_spdm_ctx());
  double adjust_ecpmthr_innerloopbigcard_secondreq_ratio =
    SPDM_adjust_ecpmthr_innerloopbigcard_secondreq_ratio(session_data->get_spdm_ctx());
  bool enable_kwai_mini_game_rb_thr_adjust =
    SPDM_enable_kwai_mini_game_rb_thr_adjust(session_data->get_spdm_ctx());
  bool enable_big_game_rb_thr_adjust =
    SPDM_enable_big_game_rb_thr_adjust(session_data->get_spdm_ctx());
  bool enable_kwai_serial_rb_thr_adjust =
    SPDM_enable_kwai_serial_rb_thr_adjust(session_data->get_spdm_ctx());
  bool enable_kwai_fiction_rb_thr_adjust =
    SPDM_enable_kwai_fiction_rb_thr_adjust(session_data->get_spdm_ctx());
  const auto* outerloop_ac_campaign = session_data->common_r_->
      GetPtrCommonAttr<std::unordered_set<int64_t>>("outerloop_ac_campaign");
  bool is_kwai_mini_game_potential_nc_user = false;
  bool is_kwai_serial_potential_nc_user = false;
  bool is_kwai_fiction_potential_nc_user = false;
  bool is_big_game_potential_nc_user = false;
  if (outerloop_ac_campaign) {
    is_kwai_mini_game_potential_nc_user =
      outerloop_ac_campaign->count(kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) == 0;
    is_kwai_serial_potential_nc_user =
      outerloop_ac_campaign->count(kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) == 0;
    is_kwai_fiction_potential_nc_user =
      outerloop_ac_campaign->count(kuaishou::ad::AdEnum::AD_KWAI_FICTION_PROMOTION) == 0;
    is_big_game_potential_nc_user =
      outerloop_ac_campaign->count(kuaishou::ad::AdEnum::APP) == 0;
  }
  double kwai_mini_game_rb_thr_ratio = is_kwai_mini_game_potential_nc_user?
    SPDM_kwai_mini_game_rb_thr_ratio_potential_nc(session_data->get_spdm_ctx()):
    SPDM_kwai_mini_game_rb_thr_ratio(session_data->get_spdm_ctx());
  double big_game_rb_thr_ratio = is_big_game_potential_nc_user?
    SPDM_big_game_rb_thr_ratio_potential_nc(session_data->get_spdm_ctx()):
    SPDM_big_game_rb_thr_ratio(session_data->get_spdm_ctx());
  double kwai_serial_rb_thr_ratio = is_kwai_serial_potential_nc_user?
    SPDM_kwai_serial_rb_thr_ratio_potential_nc(session_data->get_spdm_ctx()):
    SPDM_kwai_serial_rb_thr_ratio(session_data->get_spdm_ctx());
  double kwai_fiction_rb_thr_ratio = is_kwai_fiction_potential_nc_user?
    SPDM_kwai_fiction_rb_thr_ratio_potential_nc(session_data->get_spdm_ctx()):
    SPDM_kwai_fiction_rb_thr_ratio(session_data->get_spdm_ctx());
  auto filter = [&](AdCommon* p_ad) -> bool {
    auto& ad = *p_ad;
    // 用于处理广告跳过 cpm 门槛过滤逻辑
    if (IsSkipCpmFilter(ad)) {
      // 跳过 cpm 门槛过滤
      // 在有强出门槛时，只生效强出门槛，后面的默认门槛逻辑仍旧跳过
      if (enable_force_cpm_threshold && ad.get_force_cpm_threshold() > 0
        && ad.get_cpm() < ad.get_force_cpm_threshold()) {
        return true;
      }
      if (!(session_data->get_is_thanos_mix_request() && disable_skip_cpm_thr)) {
        return false;
      }
    }
    // 矩阵流量 APP 最低 cpm 过滤
    if (enable_matrix_app_cpm_filter) {
      ad.SetCpmThr(matrix_app_cpm_filter_threshold, CpmThrFromTag::MATRIX_FLOW_CPM_THR, true);
      if (ad.get_cpm() < ad.GetCpmThr()) {
        RANK_DOT_STATS(session_data, ad.GetCpmThr(), "matrix_flow_cpm_threshold");
        return true;
      }
      return false;
    }
    if (ad.is_search_retarget_ad(session_data->get_guaranteed_tags())
          || ad.is_olp_retarget_ad(session_data->get_game_guaranteed_tags())) {
      if (!(session_data->get_is_thanos_mix_request() && disable_skip_cpm_thr)) {
        return false;
      }
    }
    // dnc 探索
    int64_t cpm = ad.get_cpm();
    if (cpm <= 0) {
      count_filter_by_zero_cpm++;
      return true;
    }
    if (enable_set_cpm_threshold_based_on_dnc_values &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::AD_ROAS ||
        p_ad->get_ocpx_action_type() != kuaishou::ad::AdActionType::AD_SEVEN_DAY_ROAS) {
      double dnc_score = p_ad->Attr(ItemIdx::dnc_score).GetDoubleValue((p_ad->AttrIndex())).value_or(0.0);
      double dnc_ltv = p_ad->Attr(ItemIdx::dnc_ltv).GetDoubleValue((p_ad->AttrIndex())).value_or(0.0);
      double dnc_ctcvr = p_ad->Attr(ItemIdx::dnc_ctcvr).GetDoubleValue((p_ad->AttrIndex())).value_or(0.0);
      double dnc_value = 0;
      if (enable_set_cpm_threshold_based_on_dnc_score) {
        dnc_value = dnc_score;
      } else if (enable_set_cpm_threshold_based_on_dnc_ltv) {
        dnc_value = dnc_ltv * dnc_ctcvr;
      }
      if (dnc_value >= set_cpm_threshold_based_on_dnc_value_lowerbound) {
          double base_cpm_threshold = p_ad->get_cpm_thr();
          base_cpm_threshold = 1.0;
          base_cpm_threshold = std::max(base_cpm_threshold, nc_user_cpm_min_v2);
          p_ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_NC_USER_CPM_THR, true);
      }
    }
    if (!enable_outerloop_nc_cpm_thres_plugin) {
      if (!enable_set_cpm_threshold_based_on_dnc_values &&
          ad.Is(AdFlag::is_outer_loop_ad) && kBenifitFactor > 0 &&
          (is_potential_nc_user) && (!enable_nc_limit_ocpx ||
          (nc_ocpx_set && nc_ocpx_set->count(p_ad->get_ocpx_action_type()) > 0))) {
        const auto& product_name = p_ad->get_product_name();
        auto product_id = base::CityHash64(product_name.c_str(), product_name.length());
        const auto& multi_retrieval_tag = p_ad->get_multi_retrieval_tag();
        if ((enable_nc_llm && llm_prod_set && llm_prod_set->count(product_name) > 0) ||
            (enable_nc_reco_v2 && (ad.get_reco_vtr() > vtr_thr || ad.get_reco_plvtr() > lvtr_thr)) ||
            (nc_product_set && nc_product_set->count(product_name) > 0) ||
            (enable_nc_model && nc_model_prod_set && nc_model_prod_set->count(product_id) > 0) ||
            (enable_nc_retrieval && nc_retrieval_set && nc_retrieval_set->count(multi_retrieval_tag) > 0)) {
          double base_cpm_threshold = p_ad->get_cpm_thr();
          base_cpm_threshold = base_cpm_threshold * nc_user_cpm_ratio / kBenifitFactor;
          base_cpm_threshold = std::max(base_cpm_threshold, nc_user_cpm_min);
          RANK_DOT_STATS(session_data, base_cpm_threshold * 100, "PotentialNcUser", "hard");
          p_ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_NC_USER_CPM_THR, true);
        }
        if (enable_set_cpm_based_on_nc_score && nc_model_score_map_ptr) {
          auto iter_nc_model = nc_model_score_map_ptr->find(product_id);
          if (iter_nc_model != nc_model_score_map_ptr->end()) {
            double cpm_ratio = iter_nc_model->second;
            cpm_ratio *= nc_model_cpm_adjust_ratio;
            cpm_ratio = std::max(nc_model_cpm_lower_bound,
                            std::min(cpm_ratio, nc_model_cpm_upper_bound));
            cpm_ratio += 1.0;
            if (cpm_ratio > 1.0) {
              double base_cpm_threshold = p_ad->get_cpm_thr();
              base_cpm_threshold = base_cpm_threshold / cpm_ratio / kBenifitFactor;
              base_cpm_threshold = std::max(base_cpm_threshold, nc_user_cpm_min_v2);
              RANK_DOT_STATS(session_data, base_cpm_threshold * 100, "PotentialNcUser",
                            "hard", "nc_model_v2");
              p_ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_NC_USER_CPM_THR, true);
            }
          }
        }
        if (enable_set_cpm_threshold_based_on_ltv && nc_model_score_map_ptr) {
          auto iter_nc_model = nc_model_score_map_ptr->find(product_id);
          if (iter_nc_model != nc_model_score_map_ptr->end()) {
            // 计算长期价值 ltv cpm auto_cpa_bid
            auto outerloop_dnc_ltv = RankKconfUtil::outerloopDncLtv();
            auto ocpx_action_type =
              kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
            auto ocpx_action_type_enum = p_ad->get_ocpx_action_type();
            double ltv = 0;
            auto iter = outerloop_dnc_ltv->find(ocpx_action_type);
            if (iter != outerloop_dnc_ltv->end()) {
              ltv = iter->second;
            }
            std::string key = absl::StrCat(ocpx_action_type,
                                "_", p_ad->get_industry_parent_id_v3());
            iter = outerloop_dnc_ltv->find(key);
            if (iter != outerloop_dnc_ltv->end()) {
              ltv = iter->second;
            }
            double auto_cpa_bid = p_ad->get_auto_cpa_bid();
            auto ad_price_cpm = p_ad->get_cpm();
            double cpm_ratio = 0;
            if (ocpx_action_type_enum == kuaishou::ad::AdActionType::AD_ROAS ||
              ocpx_action_type_enum == kuaishou::ad::AdActionType::AD_SEVEN_DAY_ROAS) {
              // 对 ROI 目标进行特殊处理
              cpm_ratio = 0.0;
            } else if (auto_cpa_bid > 0 && ltv > 0) {
              cpm_ratio = ltv * ad_price_cpm / auto_cpa_bid;
            }
            // 放缩到 0 - 1 之间
            cpm_ratio = std::max(nc_model_cpm_threshold_lower_bound,
                            std::min(cpm_ratio, nc_model_cpm_threshold_upper_bound));
            cpm_ratio = (cpm_ratio - nc_model_cpm_threshold_lower_bound) /
              (nc_model_cpm_threshold_upper_bound - nc_model_cpm_threshold_lower_bound);
            cpm_ratio *= alpha;
            cpm_ratio += 1.0;
            if (cpm_ratio > 1.0) {
              double base_cpm_threshold = p_ad->get_cpm_thr();
              base_cpm_threshold = base_cpm_threshold / cpm_ratio / kBenifitFactor;
              base_cpm_threshold = std::max(base_cpm_threshold, nc_user_cpm_min_v2);
              p_ad->SetCpmThr(base_cpm_threshold, CpmThrFromTag::OUTER_NC_USER_CPM_THR, true);
            }
          }
        }
      }
      if (!enable_set_cpm_threshold_based_on_dnc_values) {
        SetEcpmThresforOuterLoopDncOpt(session_data, p_ad, w_level, is_target_w_level,
                                      is_potential_nc_user && outer_loop_dnc_opt_for_potential_nc &&
                                      (!outer_loop_potential_nc_bugfix || ad.Is(AdFlag::is_outer_loop_ad)),
                                      enable_outer_loop_dynamic_cpm_thres, outer_loop_dnc_hc_new_key_prefix,
                                      outer_loop_dnc_opt_dimension,
                                      outer_loop_dynamic_cpm_thres_alpha, outer_loop_dynamic_cpm_thres_min);
      }
      const std::string industry_parent_id_v3 = std::to_string(ad.get_industry_parent_id_v3());
      bool is_cross_industry = (interest_industry_set != nullptr) &&
        interest_industry_set->count(industry_parent_id_v3) <= 0;
      bool is_interest_product = (interest_prod_set != nullptr) &&
        interest_prod_set->count(ad.get_product_name()) > 0;
      enable_outerloop_cross_ind_i2i_enhance =
        enable_outerloop_cross_ind_i2i_enhance && is_cross_industry && is_interest_product;
      bool in_ocpx_white_set = outerloop_i2i_enhance_ocpx_white_set &&
        outerloop_i2i_enhance_ocpx_white_set->count(p_ad->get_ocpx_action_type()) > 0;
      bool in_ac_enhance_retrieve_set = enable_outerloop_ac_retrieval_enhance &&
        outerloop_ac_retrieval_set && outerloop_ac_retrieval_set->count(p_ad->get_multi_retrieval_tag()) > 0;
      if (enable_outerloop_ac_enhance_cpm_threshold) {
        if (enable_outerloop_ac_converted_industry_limit && (interest_industry_set != nullptr) &&
            interest_industry_set->count(industry_parent_id_v3) <= 0 &&
            (!enable_outerloop_ac_shallow_action ||
              (interest_industry_set->count(absl::StrCat("CMT_", industry_parent_id_v3)) <= 0 &&
              interest_industry_set->count(absl::StrCat("PLAY_", industry_parent_id_v3)) <= 0 &&
              interest_industry_set->count(absl::StrCat("LIKE_", industry_parent_id_v3)) <= 0))) {
          enable_outerloop_cross_ind_i2i_enhance = false;
        } else if (ocpx_industry_map_config != nullptr) {
          auto ocpx_industry_map = ocpx_industry_map_config->data().config;
          auto iter = ocpx_industry_map.find(ad.get_ocpx_action_type());
          if (iter != ocpx_industry_map.end()) {
            enable_outerloop_cross_ind_i2i_enhance = iter->second.count(ad.get_industry_parent_id_v3()) > 0;
          }
        }
      }
      if (enable_outerloop_ac_product_set && outerloop_ac_product_set != nullptr &&
          outerloop_ac_product_set->count(ad.get_product_name()) <= 0) {
        enable_outerloop_cross_ind_i2i_enhance = false;
      }
      if (enable_outerloop_ac_retrieval_diversity && outerloop_impression_cnt) {
        const std::string& product_name = ad.get_product_name();
        int64_t city_product_id = base::CityHash64(product_name.c_str(), product_name.length());
        auto iter = outerloop_impression_cnt->find(city_product_id);
        if (iter != outerloop_impression_cnt->end() &&
            iter->second > outerloop_ac_retrieval_diversity_threshold) {
          RANK_DOT_STATS(session_data, 1,
                  "outer_i2i_enhance_cpm_thres", "product_overimpression_filter");
          enable_outerloop_cross_ind_i2i_enhance = false;
        }
      }
      if (enable_outerloop_ac_enhance_cpm_ratio && kBenifitFactor > 0) {
        if (enable_outerloop_ac_shallow_action && interest_industry_set) {
          if (interest_industry_set->count(absl::StrCat("CMT_", industry_parent_id_v3)) > 0) {
            outerloop_ac_enhance_cpm_ratio = outerloop_ac_enhance_cpm_ratio_comment;
          } else if (interest_industry_set->count(absl::StrCat("LIKE_", industry_parent_id_v3)) > 0) {
            outerloop_ac_enhance_cpm_ratio = outerloop_ac_enhance_cpm_ratio_like;
          } else if (interest_industry_set->count(absl::StrCat("PLAY_", industry_parent_id_v3)) > 0) {
            outerloop_ac_enhance_cpm_ratio = outerloop_ac_enhance_cpm_ratio_play;
          }
        }
        outerloop_cross_ind_thres = ad.GetCpmThr() / kBenifitFactor * outerloop_ac_enhance_cpm_ratio;
        RANK_DOT_STATS(session_data, outerloop_ac_enhance_cpm_ratio * 1000,
                  "outer_i2i_enhance_cpm_thres", "cpm_ratio");
      }
      SetEcpmThresforOuterLoopI2IEnhance(session_data, p_ad,
                                        enable_mini_game_i2i_enhance && is_interest_product,
                                        enable_outerloop_cross_ind_i2i_enhance,
                                        in_ocpx_white_set, in_ac_enhance_retrieve_set,
                                        mini_game_i2i_enhance_thres,
                                        outerloop_cross_ind_thres);
      if (enable_outerloop_low_active_ac_max_cpm_thres && is_outerloop_low_active_ac > 0) {
        if (enable_outerloop_low_active_ac_max_cpm_ratio && kBenifitFactor > 0) {
          outerloop_nc_max_cpm_thres = ad.GetCpmThr() / kBenifitFactor * outerloop_ac_max_cpm_ratio;
        } else {
          outerloop_nc_max_cpm_thres = outerloop_ac_max_cpm_thres;
        }
      }
      if (!(enable_set_cpm_threshold_based_on_dnc_values && is_potential_nc_user)) {
        SetEcpmThresforOuterloopNcMax(session_data, p_ad, enable_outerloop_nc_max_cpm_thres,
                                      outerloop_nc_max_cpm_thres, enable_outerloop_low_active_skip_duanju,
                                      is_outerloop_low_active_ac, enable_outerloop_low_active_ocpx_blackset,
                                      enable_outerloop_nc_max_user_limit);
      }
    }
    if (enable_live_inner_thr) {
      ad.SetCpmThr(live_inner_thr, CpmThrFromTag::USER_GROUP_CPM_THR, true);
    }
    auto thresh = ad.GetCpmThr();
    std::string app_id = session_data->get_pos_manager_base().GetRequestAppId();
    if (ad.Is(AdFlag::is_outer_loop_ad)) {
      if (app_id == "kuaishou") {
        ad_rank_cpm_filter_threshold_factor = ad_rank_cpm_filter_threshold_factor_dsp_outer_gamora;
      } else if (app_id == "kuaishou_nebula") {
        ad_rank_cpm_filter_threshold_factor = ad_rank_cpm_filter_threshold_factor_dsp_outer_nebula;
      }
    } else if (ad.Is(AdFlag::is_inner_loop_ad)) {
      if (app_id == "kuaishou") {
        ad_rank_cpm_filter_threshold_factor = ad_rank_cpm_filter_threshold_factor_dsp_inner_gamora;
      } else if (app_id == "kuaishou_nebula") {
        ad_rank_cpm_filter_threshold_factor = ad_rank_cpm_filter_threshold_factor_dsp_inner_nebula;
      }
    }
    if (enable_product_adjust_cpm_threshold_ratio) {
      auto iter_prod = product_cpm_threshold_map->find(ad.get_product_name());
      if (iter_prod !=  product_cpm_threshold_map->end()) {
        double cpm_threshold_ratio = std::max(0.0, std::min(1.0, iter_prod->second));
        ad_rank_cpm_filter_threshold_factor *= cpm_threshold_ratio;
        RANK_DOT_STATS(session_data, cpm_threshold_ratio*1000,
          "product_cpm_threshold_ratio", ad.get_product_name(), "hard");
      }
    }
    if (session_data->get_is_thanos_mix_request()) {
      thresh *= ad_rank_cpm_filter_threshold_factor;
    }
    if (session_data->get_pos_manager_base().IsBuyerHomePageTraffic() &&
        enable_buyer_home_page_modify_cpm_thres) {
      thresh = buyer_home_page_cpm_thres_new * kBenifitFactor;
    }
    // 同城页面本地行业跳过门槛实验
    auto is_valid_filter_ocpx = nearby_local_filter_ocpx_whitelist->count(ad.get_ocpx_action_type()) > 0;
    bool enable_nearby_ecpm_out_thr = SPDM_enable_nearby_ecpm_out_thr(session_data->get_spdm_ctx());
    double nearby_local_ecpm_out = SPDM_nearby_local_ecpm_out(session_data->get_spdm_ctx());
    bool enable_nearby_ecpm_config_strategy = SPDM_enable_nearby_ecpm_config_strategy(session_data->get_spdm_ctx());  // NOLINT
    std::string nearby_ecpm_config_strategy_tag = SPDM_nearby_ecpm_config_strategy_tag(session_data->get_spdm_ctx());  // NOLINT
    std::string form = kuaishou::ad::AdEnum_InteractiveForm_Name(session_data->get_rank_request()->ad_request().interactive_form());  // NOLINT
    auto nearby_cpm_thr_config = RankKconfUtil::nearbyCpmThrConfig();
    auto page_id = session_data->get_page_id();
    auto item_type = ad.get_item_type();
    auto first_industry = ad.get_first_industry_id_v5();
    auto is_inner = ad.Is(AdFlag::is_inner_loop_ad);
    auto campaigntype = ad.get_campaign_type();
    if (is_valid_page_id &&
        enable_nearby_ecpm_config_strategy &&
        nearby_ecpm_config_strategy_tag != "" &&
        nearby_cpm_thr_config != nullptr) {
        std::string key;
        double cpm_thr = 0.0;
        // 根据策略类型生成对应的 key
        if (nearby_ecpm_config_strategy_tag == "nearby_pageid_ecpm") {
            key = std::to_string(page_id);
        } else if (nearby_ecpm_config_strategy_tag
                   == "nearby_pageid_itemtype_ecpm") {
            key = absl::StrCat(page_id, "|", item_type);
        } else if (nearby_ecpm_config_strategy_tag
                   == "nearby_pageid_interactive_usergroup_ecpm") {
            key = absl::StrCat(page_id, "|", form, "|", user_group_level);
        } else if (nearby_ecpm_config_strategy_tag
                  == "nearby_usergroup_ecpm") {
            key = user_group_level;
        } else if (nearby_ecpm_config_strategy_tag ==
        "nearby_pageid_interactive_itemtype_ecpm") {
            key = absl::StrCat(page_id, "|", form, "|", item_type);
        } else if (nearby_ecpm_config_strategy_tag ==
                   "nearby_pageid_interactive_ecpm") {
            key = absl::StrCat(page_id, "|", form);
        }
        RANK_DOT_COUNT(session_data, 1, "nearby_cpm_config_cnt", nearby_ecpm_config_strategy_tag);
        // 通用的查找和设置逻辑
        auto config_iter = nearby_cpm_thr_config->datas.find(nearby_ecpm_config_strategy_tag);  // NOLINT
        if (config_iter != nearby_cpm_thr_config->datas.end()) {
            auto& config_map = config_iter->second;
            auto iter = config_map.find(key);
            if (iter != config_map.end()) {
                cpm_thr = iter->second;
            }
        }
        // 设置 CPM 门槛
        if (cpm_thr > 0) {
            ad.SetCpmThr(cpm_thr, CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_HARD_CPM_TRH, true);
            thresh = ad.GetCpmThr();
            RANK_DOT_STATS(session_data, cpm_thr, "nearby_local_hard_map_config",
                          nearby_ecpm_config_strategy_tag, key);
        }
    } else if (is_valid_page_id && enable_nearby_ecpm_open_switch) {
      ad.SetCpmThr(nearby_local_hard_ecpm, CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_HARD_CPM_TRH, true);
      thresh = ad.GetCpmThr();
      RANK_DOT_STATS(session_data, 1, "nearby_local_filter_open_hard");
      if (enable_nearby_ecpm_out_thr && (form == "INTERACTIVE_FEED")) {
        ad.SetCpmThr(nearby_local_ecpm_out, CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_HARD_CPM_TRH, true);
        thresh = ad.GetCpmThr();
        RANK_DOT_STATS(session_data, nearby_local_ecpm_out, "nearby_local_filter_open_hard-out");
      }
    } else if (is_valid_page_id && enable_nearby_local_ecpm_switch &&
        nearby_local_industry_whitelist != nullptr) {
            if (nearby_local_industry_whitelist->find(ad.get_industry_id_v3()) !=
                nearby_local_industry_whitelist->end()) {
                    if (is_valid_filter_ocpx) {
                              ad.SetCpmThr(nearby_local_hard_ecpm,
                               CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_HARD_CPM_TRH, true);
                              RANK_DOT_STATS(session_data, nearby_local_hard_ecpm,
                               "nearby_local_filter_hard", std::to_string(ad.get_ocpx_action_type()));
                              thresh = ad.GetCpmThr();
                    }
            }
    }
    if (!enable_unify_explore_cpm_normal_ad &&
        enable_inner_explore_live_cpm_threshold && inner_explore_live_cpm_threshold_hard > 0 &&
        ks::ad_base::IsInnerExploreRequest(session_data->get_sub_page_id()) &&
        ad.get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
      ad.SetCpmThr(inner_explore_live_cpm_threshold_hard, CpmThrFromTag::EXPOLRE_FEED_CPM_THR, true);
      thresh = ad.GetCpmThr();
      RANK_DOT_STATS(session_data, thresh, "inner_explore_live_threshold",
          kuaishou::ad::AdEnum::AdQueueType_Name(ad.get_ad_queue_type()));
    }
    // 发现页体验优化 - 外跳广告门槛
    if (enable_shield_explore_feed_jumpout) {
      if (explore_feed_jumpout_shield_set &&
          explore_feed_jumpout_shield_set->count(ad.get_ocpx_action_type())) {
        ad.SetCpmThr(thresh * shield_explore_feed_jumpout_ratio,
          CpmThrFromTag::EXPOLRE_FEED_CPM_THR, true);
          RANK_DOT_STATS(session_data, thresh * shield_explore_feed_jumpout_ratio,
          "explore_feed_jumpout_ratio");
        thresh = ad.GetCpmThr();
      }
    }
    if ((session_data->get_pos_manager_base().GetSubPageId() == 10008001 && ad.Is(AdFlag::is_inner_loop_ad) && (ad.Is(AdFlag::is_photo) || ad.Is(AdFlag::is_p2l))) && enable_follow_hard_photo_ecpm_thr) {  // NOLINT
      RANK_DOT_STATS(session_data, thresh, "follow_hard_photo_ecpm_thr", "before");  // NOLINT
      ad.SetCpmThr(thresh * follow_hard_photo_ecpm_thr_ratio, CpmThrFromTag::FOLLOW_HARD_PHOTO_ECPM_THR, true);  // NOLINT
      if (follow_hard_photo_ecpm_thr_value > 0.0) {
        ad.SetCpmThr(follow_hard_photo_ecpm_thr_value, CpmThrFromTag::FOLLOW_HARD_PHOTO_ECPM_THR, true);  // NOLINT
      }
      thresh = ad.GetCpmThr();
      RANK_DOT_STATS(session_data, thresh, "follow_hard_photo_ecpm_thr", "after");  // NOLINT
    }
    // 公域单列 cpm 统一门槛实验（跳过其他门槛）
    if (session_data->get_is_thanos_mix_request() && enable_thanos_mix_unify_cpm_thr) {
      double unify_cpm_thr =
          app_id == "kuaishou" ? rank_unify_cpm_thr_gamora : rank_unify_cpm_thr_nebula;
      ad.SetCpmThr(unify_cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
      thresh = ad.GetCpmThr();
    }
    if (session_data->get_is_thanos_mix_request() && enable_dlrank_sep_inner_outer) {
      double unify_cpm_thr = 0.0;
      if (ad.Is(AdFlag::is_inner_loop_ad)) {
        unify_cpm_thr = app_id == "kuaishou" ? rank_unify_cpm_thr_gamora_inner : rank_unify_cpm_thr_nebula_inner;    // NOLINT
      } else {
        unify_cpm_thr = app_id == "kuaishou" ? rank_unify_cpm_thr_gamora_outer : rank_unify_cpm_thr_nebula_outer;    // NOLINT
      }
      ad.SetCpmThr(unify_cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
      thresh = ad.GetCpmThr();
    }
    if (session_data->get_is_thanos_mix_request() && enable_new_cpm_thr) {
      double cpm_thr =
          app_id == "kuaishou" ? new_cpm_thr_gamora : new_cpm_thr_nebula;
      ad.SetCpmThr(cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
      thresh = ad.GetCpmThr();
    }
    // 同城行业 cpm 分人群门槛设置
    auto is_valid_user_group = nearby_user_group_filter_list->count(user_group_level) > 0;
    auto is_valid_cpm_page = nearby_page_id_filter_list->count(page_id) > 0;
    if (enable_nearby_user_group_cpm_strategy && is_valid_user_group && is_valid_cpm_page) {
      ad.SetCpmThr(nearby_user_group_cpm_thr,
      CpmThrFromTag::NEARBY_LOCAL_INDUSTRY_HARD_CPM_TRH, true);
      thresh = ad.GetCpmThr();
      RANK_DOT_STATS(session_data, nearby_user_group_cpm_thr, "nearby_user_group_cpm_thr_hard", user_group_level);  // NOLINT
    }
    if (session_data->get_is_thanos_mix_request() && enable_thanos_wlevel_cpm_thr &&
        (w_level == "W2" || w_level == "W3")) {
      double cpm_thr = 0.0;
      if (w_level == "W2") {
        cpm_thr =
          app_id == "kuaishou" ? w2_cpm_thr_gamora : w2_cpm_thr_nebula;
      } else {
        cpm_thr =
          app_id == "kuaishou" ? w3_cpm_thr_gamora : w3_cpm_thr_nebula;
      }
      ad.SetCpmThr(cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
      thresh = ad.GetCpmThr();
    }
    if (enable_kwai_mini_game_rb_thr_adjust && ad.get_first_industry_id_v5() == 1018 &&
        ad.get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) {
      ad.MultiCpmThr(kwai_mini_game_rb_thr_ratio, CpmThrFromTag::CONTENT_CONSUMPTION_INDUSTRY_CPM_THR);
      thresh = ad.GetCpmThr();
      RANK_DOT_STATS(session_data, kwai_mini_game_rb_thr_ratio, "outerloop_cc_ind_cpm_thr_minigame");
    }
    if (enable_big_game_rb_thr_adjust && ad.get_first_industry_id_v5() == 1018 &&
        ad.get_campaign_type() == kuaishou::ad::AdEnum::APP) {
      ad.MultiCpmThr(big_game_rb_thr_ratio, CpmThrFromTag::CONTENT_CONSUMPTION_INDUSTRY_CPM_THR);
      thresh = ad.GetCpmThr();
      RANK_DOT_STATS(session_data, big_game_rb_thr_ratio * 1000, "outerloop_cc_ind_cpm_thr_biggame");
    }
    if (enable_kwai_serial_rb_thr_adjust &&
        (ad.get_second_industry_id_v5() == 2012 || ad.get_second_industry_id_v5() == 2196) &&
        ad.get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
      ad.MultiCpmThr(kwai_serial_rb_thr_ratio, CpmThrFromTag::CONTENT_CONSUMPTION_INDUSTRY_CPM_THR);
      thresh = ad.GetCpmThr();
      RANK_DOT_STATS(session_data, kwai_serial_rb_thr_ratio, "outerloop_cc_ind_cpm_thr_serial");
    }
    if (enable_kwai_fiction_rb_thr_adjust &&
         ad.get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_FICTION_PROMOTION) {
      ad.MultiCpmThr(kwai_fiction_rb_thr_ratio, CpmThrFromTag::CONTENT_CONSUMPTION_INDUSTRY_CPM_THR);
      thresh = ad.GetCpmThr();
      RANK_DOT_STATS(session_data, kwai_fiction_rb_thr_ratio, "outerloop_cc_ind_cpm_thr_fiction");
    }
    auto score = ad.GetCpm();
    if (session_data->get_is_thanos_mix_request() && enable_set_hard_origianl_cpm_thr) {
      score = ad.get_cpm();
    }
    if ((session_data->get_is_thanos_mix_request() && thanos_use_rb_thresh) ||
        (is_inner_explore && is_feed_explore && enable_feed_explore_rb_thr)) {
      score = ad.get_rank_benifit();
    }
    if (session_data->get_is_thanos_mix_request() && enable_cali_in_dlrank) {
      auto ori_cpm = ad.get_cpm();
      auto ori_bonus = ad.get_rank_benifit() - ori_cpm;
      auto cali_cpm = ori_cpm * SPDM_avg_cpm_cali_ratio_dlrank(session_data->get_spdm_ctx());
      auto cali_bonus = ori_bonus * SPDM_avg_bonus_cali_ratio_dlrank(session_data->get_spdm_ctx());
      score = cali_cpm + cali_bonus;
    }
    if (enable_adjust_ecpmthr_innerloopbigcard_secondreq && session_data->get_rank_request()->ad_request().is_common_card_second_request() && session_data->get_rank_request()->ad_request().common_card_request_info().feed_card_ind() == kuaishou::ad::AdEnum::INNER_LOOP) {  // NOLINT
      ad.SetCpmThr(adjust_ecpmthr_innerloopbigcard_secondreq_ratio, CpmThrFromTag::INNER_BIGCARD_SECOND_REQ_THR, true);  // NOLINT
      RANK_DOT_STATS(session_data, ad.GetCpmThr(), "adjust_ecpmthr_innerloopbigcard_secondreq", kuaishou::ad::AdEnum::AdQueueType_Name(ad.get_ad_queue_type()));  // NOLINT
    }
    // 小游戏 load 实验
    if (SPDM_enable_adjust_mini_game_cpm_thr(session_data->get_spdm_ctx()) && ad.Is(AdFlag::is_iaap_game_ad)) {  // NOLINT
      // 通用打折
      if (SPDM_enable_mini_game_extra_load_strategy(session_data->get_spdm_ctx())) {
        if (app_id == "kuaishou") {
          ad.MultiCpmThr(SPDM_mini_game_extra_load_cpm_thr_gamora(session_data->get_spdm_ctx()), CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);  // NOLINT
        } else if (app_id == "kuaishou_nebula") {
          ad.MultiCpmThr(SPDM_mini_game_extra_load_cpm_thr_nebula(session_data->get_spdm_ctx()), CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);  // NOLINT
        }
      }
    }

    // 内循环特定广告门槛降低
    if (enable_inner_slide_gfp_thresh && session_data->get_is_thanos_mix_request() &&
        ad.get_mix_gfp_score() > 0.0) {
      auto& ad_distribution_list =
        session_data->get_rank_request()->ad_request().ad_user_info().ad_ue_distribution_list();
      if (ad_distribution_list.size() >= 36) {
        double live_base_offsets_p10 = -1.0;
        double live_base_offsets_p25 = -1.0;
        double live_base_offsets_p50 = -1.0;
        double video_base_offsets_p10 = -1.0;
        double video_base_offsets_p25 = -1.0;
        double video_base_offsets_p50 = -1.0;
        if (session_data->get_page_id() == 10011) {
          video_base_offsets_p10 = ad_distribution_list.at(0);
          video_base_offsets_p25 = ad_distribution_list.at(1);
          video_base_offsets_p50 = ad_distribution_list.at(2);
          live_base_offsets_p10 = ad_distribution_list.at(3);
          live_base_offsets_p25 = ad_distribution_list.at(4);
          live_base_offsets_p50 = ad_distribution_list.at(5);
        }
        if (session_data->get_page_id() == 11001) {
          video_base_offsets_p10 = ad_distribution_list.at(6);
          video_base_offsets_p25 = ad_distribution_list.at(7);
          video_base_offsets_p50 = ad_distribution_list.at(8);
          live_base_offsets_p10 = ad_distribution_list.at(9);
          live_base_offsets_p25 = ad_distribution_list.at(10);
          live_base_offsets_p50 = ad_distribution_list.at(11);
        }
        double prev_inner_ad_score_threshold = -1.0;
        if (ad.get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
          if (gfp_inner_score_thresh == "p10") {
            prev_inner_ad_score_threshold = live_base_offsets_p10;
          }
          if (gfp_inner_score_thresh == "p25") {
            prev_inner_ad_score_threshold = live_base_offsets_p25;
          }
          if (gfp_inner_score_thresh == "p50") {
            prev_inner_ad_score_threshold = live_base_offsets_p50;
          }
        } else {
          if (gfp_inner_score_thresh == "p10") {
            prev_inner_ad_score_threshold = video_base_offsets_p10;
          }
          if (gfp_inner_score_thresh == "p25") {
            prev_inner_ad_score_threshold = video_base_offsets_p25;
          }
          if (gfp_inner_score_thresh == "p50") {
            prev_inner_ad_score_threshold = video_base_offsets_p50;
          }
        }
        double thresh = app_id == "kuaishou" ? 6.2 : 4.0;
        double rb = ad.get_rank_benifit() / 1e6;
        if (prev_inner_ad_score_threshold > 0 &&
            ad.get_mix_gfp_score() > prev_inner_ad_score_threshold &&
            rb < thresh) {
          double new_thresh = 0.0;
          auto& conf = inner_slide_page_conf->data().confs();
          auto iter = conf.find(gfp_inner_score_exp_tag);
          if (iter != conf.end()) {
            auto& page_id_coef = p_ad->Is(AdFlag::is_photo_ad) ? iter->second.photo_coef() :
              (p_ad->Is(AdFlag::is_p2l_ad) ? iter->second.p2l_coef() : iter->second.live_coef());
            auto iter_coef = page_id_coef.find(absl::StrCat("$0", session_data->get_page_id()));
            if (iter_coef != page_id_coef.end()) {
              new_thresh = iter_coef->second;
            }
          }
          ad.SetCpmThr(new_thresh, CpmThrFromTag::GLOBAL_CPM_TRH, true);
          thresh = ad.GetCpmThr();
          RANK_DOT_STATS(session_data,  ad.get_rank_benifit(),
                        "INNER_GFP_LOWER_CPM_THRESH", absl::StrCat(ad.get_item_type()));
        }
      }
    }
    if (session_data->get_is_thanos_mix_request() && enable_dlrank_sep_inner) {
      double unify_cpm_thr = 0.0;
      if (ad.get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE ||
          ad.get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP) {
        unify_cpm_thr = app_id == "kuaishou" ? rank_unify_cpm_thr_gamora_inner_v2 : rank_unify_cpm_thr_nebula_inner_v2;    // NOLINT
        ad.SetCpmThr(unify_cpm_thr, CpmThrFromTag::GLOBAL_CPM_TRH, true);
        thresh = ad.GetCpmThr();
      }
    }
    //// 本地原生链路人群探索
    if (enable_lsp_native_user_layered_explore_thr &&
      ad.get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION) {
      const auto& exp_config =
        lsp_native_explore_thr_conf->data().exp_list().at(lsp_native_explore_thr_exp_name);
      bool enable_default = exp_config.enable_default();
      double default_ecpm_thr = exp_config.default_ecpm_thr();
      const auto& tag_thr_configs = exp_config.tag_thr_configs();

      const auto& user_layered_tag_scores = session_data->get_local_life_user_layered_tags();
      if (user_layered_tag_scores.empty() && enable_default) {
        RANK_DOT_COUNT(session_data, 1,
          "LSP_NATIVE_USER_LAYERED_EXPLORE_THR.empty_default_success_cnt");
        ad.MultiCpmThr(default_ecpm_thr, CpmThrFromTag::LSP_NATIVE_USER_LAYERED_EXPLORE_THR);
        thresh = ad.GetCpmThr();
      } else {
        bool find_match = false;
        double min_ecpm_thr = 1.0;
        for (const auto& config : tag_thr_configs) {
          const auto& user_tags = config.user_tags();
          double tags_ecpm_thr = config.tags_ecpm_thr();
          for (const auto& tag_score : user_layered_tag_scores) {
            if (std::find(
                  user_tags.begin(),
                  user_tags.end(),
                  kuaishou::ad::AdEnum::LifeUserLayeredTags_Name(tag_score.first))
                != user_tags.end()) {
              find_match = true;
              if (tags_ecpm_thr > 0 && tags_ecpm_thr < min_ecpm_thr) {
                min_ecpm_thr = tags_ecpm_thr;
              }
            }
          }
        }

        if (find_match) {
          RANK_DOT_COUNT(session_data, 1,
            "LSP_NATIVE_USER_LAYERED_EXPLORE_THR.find_match_success_cnt");
          ad.MultiCpmThr(min_ecpm_thr, CpmThrFromTag::LSP_NATIVE_USER_LAYERED_EXPLORE_THR);
          thresh = ad.GetCpmThr();
        } else if (enable_default) {
          RANK_DOT_COUNT(session_data, 1,
            "LSP_NATIVE_USER_LAYERED_EXPLORE_THR.default_success_cnt");
          ad.MultiCpmThr(default_ecpm_thr, CpmThrFromTag::LSP_NATIVE_USER_LAYERED_EXPLORE_THR);
          thresh = ad.GetCpmThr();
        }
      }
    }

    if (SPDM_enable_adjust_mini_game_cpm_thr(session_data->get_spdm_ctx())) {
      // 分产品/账户打折
      double mini_game_extra_load_cpm_thr = 1.0;
      const auto& mini_game_cpm_thr_list_conf = RankKconfUtil::miniGameCpmThrdList();
      if (mini_game_cpm_thr_list_conf != nullptr) {
        auto product_iter = mini_game_cpm_thr_list_conf->find(ad.get_product_name());
        if (product_iter != mini_game_cpm_thr_list_conf->end()) {
          mini_game_extra_load_cpm_thr = product_iter->second;
          RANK_DOT_STATS(session_data, ad.get_cpm_thr(), "mini_game_cpm_thr_before_normal_product", product_iter->first);   // NOLINT
        }
        auto account_iter = mini_game_cpm_thr_list_conf->find(absl::StrCat(ad.get_account_id()));
        if (account_iter != mini_game_cpm_thr_list_conf->end()) {
          mini_game_extra_load_cpm_thr = account_iter->second;
          RANK_DOT_STATS(session_data, ad.get_cpm_thr(), "mini_game_cpm_thr_before_normal_account", account_iter->first);   // NOLINT
        }
      }
      RANK_DOT_STATS(session_data, ad.get_cpm_thr(), "mini_game_cpm_thr_before_normal");
      if (SPDM_enable_mini_game_multi_cpm_thr(session_data->get_spdm_ctx())) {
        ad.MultiCpmThr(mini_game_extra_load_cpm_thr, CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      } else {
        ad.SetCpmThr(mini_game_extra_load_cpm_thr, CpmThrFromTag::UNIFY_ADLOAD_CONTROL_CPM_THR);
      }
      RANK_DOT_STATS(session_data, ad.get_cpm_thr(), "mini_game_cpm_thr_after_normal");
    }
    RANK_DOT_STATS(session_data, thresh, "final_cpm_thresh", "hard", user_group_level);
    if (enable_refactor_filter_restore_cpm && !ad.Is(AdFlag::IsHardAd)) {
      return false;
    }
    if (SPDM_enable_force_recall_by_ad_type(session_data->get_spdm_ctx())
        && p_ad->get_is_effect_recall_ad()) {
      RANK_DOT_COUNT(session_data, 1,
        "force_recall_by_ad_type",
        "recall_by_ad_type_tag_skip_filter",
        "normal_ecpm");
      return false;
    }
    if ((SPDM_enable_force_effect_recall(session_data->get_spdm_ctx()) ||
        SPDM_enable_force_effect_recall_new(session_data->get_spdm_ctx())) && ad.get_is_effect_recall_ad()) {
      RANK_DOT_COUNT(session_data, 1, "force_effect_recall", "recall_tag_skip_filter", "normal_ecpm");
      return false;
    }
    if (score < thresh && !session_data->get_pos_manager_base().IsMicroAppRequest()) {
      return true;
    }
    return false;
  };
  FINISH_FILTER()
}  // NOLINT

void UnifyFinalRbThresholdFilter(ContextData* session_data, AdList* ad_list, const FilterInfo& filter_info) {
  // 跳过激励，激励没有门槛限制
  if (session_data->get_is_rewarded() || session_data->get_is_inspire_live_request()) {
    return;
  }
  // 统一 rank benefit 门槛过滤，门槛统一策略，一旦开启，复写之前所有的策略的门槛
  int64 sub_page_id = session_data->get_pos_manager_base().GetSubPageId();
  std::string sub_page_id_str = absl::StrCat(sub_page_id);
  auto unifyFinalRbThresholdFilterConfig = RankKconfUtil::unifyFinalRbThresholdFilterConfig();
  if (unifyFinalRbThresholdFilterConfig == nullptr) {
    RANK_DOT_COUNT(session_data, 1, "UnifyFinalRbThresholdFilter.error", sub_page_id_str, "null_cfg");
    return;
  }
  std::string unify_final_rb_threshold_filter_tag =
      SPDM_unify_final_rb_threshold_filter_tag(session_data->get_spdm_ctx());
  auto p_rb_thr_map =
      unifyFinalRbThresholdFilterConfig->data().tag_map().find(unify_final_rb_threshold_filter_tag);
  if (p_rb_thr_map == unifyFinalRbThresholdFilterConfig->data().tag_map().end()) {
    RANK_DOT_COUNT(session_data, 1, "UnifyFinalRbThresholdFilter.error", sub_page_id_str,
                   absl::StrCat("no_tag=", unify_final_rb_threshold_filter_tag));
    return;
  }
  auto rb_thr_map = p_rb_thr_map->second;
  double rb_thr = rb_thr_map.rb_thr_otherwise();
  auto p_rb_thr = rb_thr_map.rb_thr_map().find(sub_page_id);
  if (p_rb_thr != rb_thr_map.rb_thr_map().end()) {
    rb_thr = p_rb_thr->second;
  }
  int64 rb_thr_ = static_cast<int64>(rb_thr * kBenifitFactor);
  auto filter = [&](AdCommon* p_ad) -> bool {
    bool mask = p_ad->get_rank_benifit() < rb_thr_;
    RANK_DOT_STATS(session_data, rb_thr * 1000, "UnifyFinalRbThresholdFilter", sub_page_id_str,
                   absl::StrCat("m:", mask));
    return mask;
  };
  FINISH_FILTER()
}

void BuyerHomeU4EcpmFilter(ContextData* session_data, AdList* ad_list,
                           const FilterInfo& filter_info) {
  if (!session_data->get_pos_manager_base().IsBuyerHomePageTraffic() ||
      !SPDM_enable_bh_u4_cpm_thr(session_data->get_spdm_ctx())) {
    return;
  }
  auto buyer_type = session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
  if (buyer_type != "U4" && buyer_type != "U4+") {
    return;
  }

  double live_cpm_thr = SPDM_bh_u4_cpm_thr_live(session_data->get_spdm_ctx());
  double item_cpm_thr = SPDM_bh_u4_cpm_thr_item(session_data->get_spdm_ctx());

  auto filter = [&] (AdCommon* ad) -> bool {
    if (ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO) {
      ad->SetCpmThr(item_cpm_thr, CpmThrFromTag::BUYERHOME_U4_CPM_THR, true);
    } else {
      ad->SetCpmThr(live_cpm_thr, CpmThrFromTag::BUYERHOME_U4_CPM_THR, true);
    }
    RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "bh_u4_cpm_thr",
                   kuaishou::ad::AdEnum::ItemType_Name(ad->get_item_type()));
    double cpm_thr = ad->get_cpm_thr();
    auto score = ad->get_cpm();
    if (score < cpm_thr) {
      return true;
    } else {
      return false;
    }
  };
  FINISH_FILTER()
}

void ShelfMerchantUnifyEcpmFilter(ContextData* session_data, AdList* ad_list,
                                  const FilterInfo& filter_info) {
  if (session_data->common_r_->GetRequestType() != "merchant_flow" &&
      !session_data->common_r_->GetIntCommonAttr("enable_split_shelf_merchant").value_or(0)) {
    return;
  }

  // 赚钱页跳过  ecpm  过滤逻辑
  if (SPDM_enable_skip_zhuanqian_ecpm_fliter(session_data->get_spdm_ctx()) &&
        session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    return;
  }

  double unify_cpm_thr = SPDM_shelf_merchant_unify_cpm_thr(session_data->get_spdm_ctx());
  double live_cpm_thr = unify_cpm_thr;
  double item_cpm_thr = unify_cpm_thr;
  double p2l_cpm_thr = unify_cpm_thr;
  bool enable_mingtou_cpm_thr = false;
  double mingtou_cpm_thr = unify_cpm_thr;

  double shelf_gyl_p2l_cpm_thr = SPDM_shelf_gyl_p2l_cpm_thr(session_data->get_spdm_ctx());
  double shelf_bh_p2l_cpm_thr = SPDM_shelf_bh_p2l_cpm_thr(session_data->get_spdm_ctx());
  double shelf_mall_p2l_cpm_thr = SPDM_shelf_mall_p2l_cpm_thr(session_data->get_spdm_ctx());
  double shelf_zq_p2l_cpm_thr = SPDM_shelf_zq_p2l_cpm_thr(session_data->get_spdm_ctx());

  // 商城
  bool enable_mall_cpm_thr = SPDM_enable_shelf_merchant_mall_cpm_thr(session_data->get_spdm_ctx());
  double mall_cpm_thr = SPDM_shelf_merchant_mall_cpm_thr(session_data->get_spdm_ctx());
  bool enable_mall_live_cpm_thr =
      SPDM_enable_shelf_merchant_mall_live_cpm_thr(session_data->get_spdm_ctx());
  double mall_live_cpm_thr = SPDM_shelf_merchant_mall_live_cpm_thr(session_data->get_spdm_ctx());

  // 买首
  bool enable_bh_cpm_thr = SPDM_enable_shelf_merchant_bh_cpm_thr(session_data->get_spdm_ctx());
  double bh_cpm_thr = SPDM_shelf_merchant_bh_cpm_thr(session_data->get_spdm_ctx());
  bool enable_bh_live_cpm_thr = SPDM_enable_shelf_merchant_bh_live_cpm_thr(session_data->get_spdm_ctx());
  double bh_live_cpm_thr = SPDM_shelf_merchant_bh_live_cpm_thr(session_data->get_spdm_ctx());

  // 猜喜
  bool enable_gyl_cpm_thr = SPDM_enable_shelf_merchant_gyl_cpm_thr(session_data->get_spdm_ctx());
  double gyl_cpm_thr = SPDM_shelf_merchant_gyl_cpm_thr(session_data->get_spdm_ctx());
  bool enable_gyl_live_cpm_thr = SPDM_enable_shelf_merchant_gyl_live_cpm_thr(session_data->get_spdm_ctx());
  double gyl_live_cpm_thr = SPDM_shelf_merchant_gyl_live_cpm_thr(session_data->get_spdm_ctx());
  bool enable_gyl_mingtou_cpm_thr =
      SPDM_enable_shelf_mingtou_gyl_cpm_thr(session_data->get_spdm_ctx());
  double gyl_mingtou_cpm_thr = SPDM_shelf_mingtou_gyl_cpm_thr(session_data->get_spdm_ctx());

  // 赚钱
  double zq_cpm_thr = SPDM_shelf_merchant_zq_cpm_thr(session_data->get_spdm_ctx());
  double zq_live_cpm_thr = SPDM_shelf_merchant_zq_live_cpm_thr(session_data->get_spdm_ctx());

  // 重定向广告门槛
  double retarget_item_cpm_thr = 0.01;
  double retarget_live_cpm_thr = 0.01;
  double retarget_gyl_cpm_thr = SPDM_shelf_retarget_gyl_cpm_thr(session_data->get_spdm_ctx());
  double retarget_gyl_live_cpm_thr = SPDM_shelf_retarget_gyl_live_cpm_thr(session_data->get_spdm_ctx());
  double retarget_bh_cpm_thr = SPDM_shelf_retarget_bh_cpm_thr(session_data->get_spdm_ctx());
  double retarget_bh_live_cpm_thr = SPDM_shelf_retarget_bh_live_cpm_thr(session_data->get_spdm_ctx());
  double retarget_mall_cpm_thr = SPDM_shelf_retarget_mall_cpm_thr(session_data->get_spdm_ctx());
  double retarget_mall_live_cpm_thr = SPDM_shelf_retarget_mall_live_cpm_thr(session_data->get_spdm_ctx());

  if (session_data->get_pos_manager_base().IsMallTraffic()) {
    if (enable_mall_cpm_thr) {
      item_cpm_thr = mall_cpm_thr;
    }
    if (enable_mall_live_cpm_thr) {
      live_cpm_thr = mall_live_cpm_thr;
    }
    p2l_cpm_thr = shelf_mall_p2l_cpm_thr;
  } else if (session_data->get_pos_manager_base().IsBuyerHomePageTraffic()) {
    if (enable_bh_cpm_thr) {
      item_cpm_thr = bh_cpm_thr;
    }
    if (enable_bh_live_cpm_thr) {
      live_cpm_thr = bh_live_cpm_thr;
    }
    p2l_cpm_thr = shelf_bh_p2l_cpm_thr;
  } else if (session_data->get_pos_manager_base().IsGuessYouLike()) {
    if (enable_gyl_cpm_thr) {
      item_cpm_thr = gyl_cpm_thr;
    }
    if (enable_gyl_mingtou_cpm_thr) {
      enable_mingtou_cpm_thr = enable_gyl_mingtou_cpm_thr;
      mingtou_cpm_thr = gyl_mingtou_cpm_thr;
    }
    if (enable_gyl_live_cpm_thr) {
      live_cpm_thr = gyl_live_cpm_thr;
    }
    p2l_cpm_thr = shelf_gyl_p2l_cpm_thr;
  } else if (session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    item_cpm_thr = zq_cpm_thr;
    live_cpm_thr = zq_live_cpm_thr;
    p2l_cpm_thr = shelf_zq_p2l_cpm_thr;
    //  其他过滤先跳过
  }
  auto filter = [&] (AdCommon* ad) -> bool {
    if (ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO) {
      if (enable_mingtou_cpm_thr
          && ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD) {
        ad->SetCpmThr(mingtou_cpm_thr, CpmThrFromTag::SHELF_MERCHANT_UNIFY_CPM_THR, true);
      } else {
        ad->SetCpmThr(item_cpm_thr, CpmThrFromTag::SHELF_MERCHANT_UNIFY_CPM_THR, true);
      }
    } else if (ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO_TO_LIVE) {
      ad->SetCpmThr(p2l_cpm_thr, CpmThrFromTag::SHELF_MERCHANT_UNIFY_CPM_THR, true);
    } else {
      ad->SetCpmThr(live_cpm_thr, CpmThrFromTag::SHELF_MERCHANT_UNIFY_CPM_THR, true);
    }
    RANK_DOT_STATS(session_data, ad->get_cpm_thr(), "shelf_merchant_unify_cpm_thr",
                  absl::StrCat(session_data->get_page_id()),
                  kuaishou::ad::AdEnum::ItemType_Name(ad->get_item_type()));
    double cpm_thr = ad->get_cpm_thr();
    auto score = ad->get_cpm();
    if (score < cpm_thr) {
      return true;
    } else {
      return false;
    }
  };
  FINISH_FILTER()
}

// 函数名与枚举名相同
REGISTER_FILTER(EcpmFilter)
REGISTER_FILTER(RankBenefitFilter)
REGISTER_FILTER(NormalCpmFilter)
REGISTER_FILTER(KminiGameCpmFilter)
REGISTER_FILTER(RecoLiveCpmFilter)
REGISTER_FILTER(UnifyFinalRbThresholdFilter)
REGISTER_FILTER(ShelfMerchantUnifyEcpmFilter)
REGISTER_FILTER(BuyerHomeU4EcpmFilter)

}  // namespace ad_rank
}  // namespace ks
