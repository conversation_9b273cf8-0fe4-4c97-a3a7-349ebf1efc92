#include "teams/ad/ad_rank/processor/model/common_predict_handler/inner_merge_hard_rocket_predict_handler.h"

namespace ks {
namespace ad_rank {

bool InnerMergeHardRocketPredictHandler::IsValid() {
  // 过滤非 精选+极速 页面
  if (context_data_->get_sub_page_id() != 10011001 &&
    context_data_->get_sub_page_id() != 11001001) {
    return false;
  }
  // 过滤没打开 ue kconf 请求开关的部分
  if (!SPDM_enable_reco_frrank_ue_kconf()) {
    return false;
  }
  // 过滤没打开 ue ab 请求开关的部分
  if (!SPDM_enable_inner_order_reco_frrank_ue(context_data_->get_spdm_ctx()) &&
      !SPDM_enable_inner_order_sv_reco_frrank_ue(context_data_->get_spdm_ctx())) {
    return false;
  }
  // 过滤二阶段开关没开的
  if (!SPDM_enable_inner_merge_second_stage_predict(context_data_->get_spdm_ctx())) {
    return false;
  }
  // 过滤没有请求硬广的
  if (!SPDM_enable_inner_merge_hard_rocket(context_data_->get_spdm_ctx())) {
    return false;
  }
  return true;
}

void InnerMergeHardRocketPredictHandler::FillItemContextItemCommonAttr(ItemContext* item_context,
                                                                    int64 name, float val) {
  if (item_context == nullptr) { return; }
  if (val > 0) {
    auto *item_common_attr = item_context->add_item_common_attr();
    item_common_attr->set_name_value(name);
    item_common_attr->set_type(CommonTypeEnum::FLOAT_ATTR);
    item_common_attr->set_float_value(val);
  }
}

void InnerMergeHardRocketPredictHandler::FillItemContexItemUescore(ItemContext* item_context,
                                                                const AdCommon* p_ad) {
  if (item_context == nullptr) { return; }
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCTR,
    p_ad->Attr(ItemIdx::reco_fr_pctr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PLVTR,
    p_ad->Attr(ItemIdx::reco_fr_plvtr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PSVTR,
    p_ad->Attr(ItemIdx::reco_fr_psvr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PVTR,
    p_ad->Attr(ItemIdx::reco_fr_pvtr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PWTD,
    p_ad->Attr(ItemIdx::reco_fr_pwtd).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCPR,
    p_ad->Attr(ItemIdx::reco_fr_pcpr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PLTR,
    p_ad->Attr(ItemIdx::reco_fr_pltr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PWTR,
    p_ad->Attr(ItemIdx::reco_fr_pwtr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PFTR,
    p_ad->Attr(ItemIdx::reco_fr_pftr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCMTR,
    p_ad->Attr(ItemIdx::reco_fr_pcmtr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PHTR,
    p_ad->Attr(ItemIdx::reco_fr_phtr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCLICK_LIVE,
    p_ad->Attr(ItemIdx::reco_fr_click_live).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context,
    ContextInfoCommonAttr::FRRANK_UESCORE_PEFFECTIVE_WATCH_LIVE_TIME,
    p_ad->Attr(ItemIdx::reco_fr_effective_watch_live_time).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PPTR,
    p_ad->Attr(ItemIdx::reco_fr_pptr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PEPSTR,
    p_ad->Attr(ItemIdx::reco_fr_pepstr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PLSTR,
    p_ad->Attr(ItemIdx::reco_fr_plstr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PETCM,
    p_ad->Attr(ItemIdx::reco_fr_petcm).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCMEF,
    p_ad->Attr(ItemIdx::reco_fr_pcmef).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0));
}

void InnerMergeHardRocketPredictHandler::FillItemContexItemRankIndex(ItemContext* item_context,
                                                                const AdCommon* p_ad,
                                                              const std::unordered_map<int64_t, std::unordered_map<int64_t, int>>& photo_rank_indices) {
  if (item_context == nullptr) { return; }
  
  auto get_rank_index = [](int64_t photo_id, int64_t attr_name, 
      const std::unordered_map<int64_t, std::unordered_map<int64_t, int>>& indices) -> int {
    auto rank_info_iter = indices.find(attr_name);
    if (rank_info_iter == indices.end()) {
      return 0;
    }
    const auto& photo_indices = rank_info_iter->second;
    auto iter = photo_indices.find(photo_id);
    return iter == photo_indices.end() ? 0 : iter->second;
  };
  auto pid = p_ad->get_photo_id();
  // 短带一阶段打分 序特征
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::ADRANK_INNER_MERGE_CVR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::ADRANK_INNER_MERGE_CVR_LIST, photo_rank_indices));
  // ue 打分 序特征
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PCTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PLVTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PLVTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PSVTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PSVTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PVTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PVTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PWTD_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PWTD_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCPR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PCPR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PLTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PLTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PWTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PWTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PFTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PFTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCMTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PCMTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PHTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PHTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCLICK_LIVE_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PCLICK_LIVE_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PEFFECTIVE_WATCH_LIVE_TIME_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PEFFECTIVE_WATCH_LIVE_TIME_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PPTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PPTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PEPSTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PEPSTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PLSTR_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PLSTR_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PETCM_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PETCM_LIST, photo_rank_indices));
  FillItemContextItemCommonAttr(item_context, ContextInfoCommonAttr::FRRANK_UESCORE_PCMEF_RANK_INDEX,
    get_rank_index(pid, ContextInfoCommonAttr::FRRANK_UESCORE_PCMEF_LIST, photo_rank_indices));
}

void InnerMergeHardRocketPredictHandler::FillItemContexItemTopLayer(ItemContext* item_context,
                                                                const AdCommon* p_ad) {
  auto* embedding_attr = const_cast<AdCommon*>(p_ad)->get_predict_embedding_attr(
    ks::engine_base::PredictEmbeddingType::PredictEmbeddingType_inner_merge_toplayer);
  if (embedding_attr == nullptr) {
    RANK_DOT_COUNT(context_data_, 1, "inner_merge_requset_failed", kess_name, "has_embedding", "ad");
    return;
  }
  auto* item_common_attr = item_context->add_item_common_attr();
  item_common_attr->CopyFrom(*embedding_attr);
  item_common_attr->set_name_value(kuaishou::ad::ContextInfoCommonAttr::INNER_MERGE_TOP_LAYER_EMB);
}

void InnerMergeHardRocketPredictHandler::FillItemPredictRequest(const std::vector<AdCommon*>& ad_list,
                                                              CmdItemMapping* cmd_item_mapping) {
  for (const auto& p_ad : ad_list) {
    // 标识 ad 的队列
    std::string source = "";
    if (p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD) {
      source = "normal_live_ad";
    } else if (p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) {
      source = "normal_photo_ad";
    } else if (p_ad->get_queue_type() == RankAdListType::NATIVE_AD) {
      source = "native_ad";
    } else if (p_ad->get_queue_type() == RankAdListType::FANSTOP) {
      source = "fanstop";
    }

    if (!p_ad || !p_ad->Is(AdFlag::GetValid)) {
      continue;
    }
    // 过滤非内循环订单合并场景
    if (!p_ad->Is(AdFlag::is_ue_merchant_order) && !p_ad->Is(AdFlag::is_ue_roas)) {
      continue;
    }
    // 过滤没有 top layer 表征的
    auto* embedding_attr = p_ad->get_predict_embedding_attr(
      ks::engine_base::PredictEmbeddingType::PredictEmbeddingType_inner_merge_toplayer);
    if (!embedding_attr) {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_requset_failed", kess_name, "has_embedding", source);
      continue;
    }
    // 过滤没有 ue 打分的: pctr pvtr
    double pctr_ue = p_ad->Attr(ItemIdx::reco_fr_pctr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
    double pvtr_ue = p_ad->Attr(ItemIdx::reco_fr_pvtr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
    if (pctr_ue <= 0 && pvtr_ue <= 0) {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_requset_failed", kess_name, "has_ue", source);
      continue;
    }
    // 请求成功打点
    RANK_DOT_COUNT(context_data_, 1, "inner_merge_requset_success", kess_name, source);
    // 标记 ad
    auto iter = itemid2ad.find(p_ad->get_creative_id());
    if (iter != itemid2ad.end()) {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_requset_failed", kess_name, "has_creative_id", source);
      continue;
    }
    itemid2ad[p_ad->get_creative_id()] = p_ad;
    // item
    if (cmd_item_mapping == nullptr) {
      continue;
    }
    cmd_item_mapping->add_item_id(p_ad->get_creative_id());
    predict_request_->add_item_id(p_ad->get_creative_id());
    predict_request_->add_item_skip_adlog_put(true);
    // callback_event
    auto callback_event = GetCallBackEvenet(p_ad->get_ocpx_action_type(), p_ad->get_deep_conversion_type());
    predict_request_->add_callback_event(callback_event);
    // item_id_info
    auto* item_id_info = predict_request_->add_item_id_info();
    item_id_info->set_cretive_id(p_ad->get_creative_id());
    item_id_info->set_photo_id(p_ad->get_photo_id());
    item_id_info->set_live_id(p_ad->get_live_id_for_ps());
    item_id_info->set_item_type(p_ad->get_item_type());
    if (SPDM_enableFillAuthorForPredict()) {
      item_id_info->set_unit_id(p_ad->get_unit_id());
      item_id_info->set_author_id(p_ad->get_author_id());
      item_id_info->set_campaign_id(p_ad->get_campaign_id());
    }
    // item_context
    auto* item_context = predict_request_->add_item_context();
    item_context->set_item_id(p_ad->get_creative_id());
    // top_layer embedding
    FillItemContexItemTopLayer(item_context, p_ad);
    // ue_score
    FillItemContexItemUescore(item_context, p_ad);
    // item_id info
    auto* item_context_item_id_info = item_context->mutable_item_id_info();
    item_context_item_id_info->CopyFrom(*item_id_info);
    // callback
    if (callback_event != kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN) {
      item_context->set_callback_event(callback_event);
    }
  }
}


void InnerMergeHardRocketPredictHandler::FillItemPredictRequestWithContext(const std::vector<AdCommon*>& ad_list,
                                                              CmdItemMapping* cmd_item_mapping,
              std::unordered_map<google::protobuf::int64, std::unordered_map<int64_t, float>>* rank_index_info) {
  // 对每个特征字段按 xtr 排序，保存 index
  std::unordered_map<int64_t, std::unordered_map<int64_t, int>> photo_rank_indices;
  
  for (const auto& rank_info : *rank_index_info) {
    auto attr_name = rank_info.first;
    const auto& photo_scores = rank_info.second;
    
    std::vector<std::pair<int64_t, float>> score_vec;
    score_vec.reserve(photo_scores.size());
    for (const auto& ps : photo_scores) {
      score_vec.emplace_back(ps.first, ps.second);
    }
    
    // 按 xtr 从大到小排序
    std::sort(score_vec.begin(), score_vec.end(),
      [](const auto& a, const auto& b) { return a.second > b.second; });
      
    // 记录每个 photo_id 的排序 index 从 1 开始
    for (size_t i = 0; i < score_vec.size(); i++) {
      photo_rank_indices[attr_name][score_vec[i].first] = i + 1;
    }
  }
  for (const auto& p_ad : ad_list) {
    // 标识 ad 的队列
    std::string source = "";
    if (p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD) {
      source = "normal_live_ad";
    } else if (p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) {
      source = "normal_photo_ad";
    } else if (p_ad->get_queue_type() == RankAdListType::NATIVE_AD) {
      source = "native_ad";
    } else if (p_ad->get_queue_type() == RankAdListType::FANSTOP) {
      source = "fanstop";
    }

    if (!p_ad || !p_ad->Is(AdFlag::GetValid)) {
      continue;
    }
    // 过滤非内循环订单合并场景
    if (!p_ad->Is(AdFlag::is_ue_merchant_order) && !p_ad->Is(AdFlag::is_ue_roas)) {
      continue;
    }
    // 过滤没有 top layer 表征的
    auto* embedding_attr = p_ad->get_predict_embedding_attr(
      ks::engine_base::PredictEmbeddingType::PredictEmbeddingType_inner_merge_toplayer);
    if (!embedding_attr) {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_requset_failed", kess_name, "has_embedding", source);
      continue;
    }
    // 过滤没有 ue 打分的: pctr pvtr
    double pctr_ue = p_ad->Attr(ItemIdx::reco_fr_pctr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
    double pvtr_ue = p_ad->Attr(ItemIdx::reco_fr_pvtr).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
    if (pctr_ue <= 0 && pvtr_ue <= 0) {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_requset_failed", kess_name, "has_ue", source);
      continue;
    }
    // 请求成功打点
    RANK_DOT_COUNT(context_data_, 1, "inner_merge_requset_success", kess_name, source);
    // 标记 ad
    auto iter = itemid2ad.find(p_ad->get_creative_id());
    if (iter != itemid2ad.end()) {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_requset_failed", kess_name, "has_creative_id", source);
      continue;
    }
    itemid2ad[p_ad->get_creative_id()] = p_ad;
    // item
    if (cmd_item_mapping == nullptr) {
      continue;
    }
    cmd_item_mapping->add_item_id(p_ad->get_creative_id());
    predict_request_->add_item_id(p_ad->get_creative_id());
    predict_request_->add_item_skip_adlog_put(true);
    // callback_event
    auto callback_event = GetCallBackEvenet(p_ad->get_ocpx_action_type(), p_ad->get_deep_conversion_type());
    predict_request_->add_callback_event(callback_event);
    // item_id_info
    auto* item_id_info = predict_request_->add_item_id_info();
    item_id_info->set_cretive_id(p_ad->get_creative_id());
    item_id_info->set_photo_id(p_ad->get_photo_id());
    item_id_info->set_live_id(p_ad->get_live_id_for_ps());
    item_id_info->set_item_type(p_ad->get_item_type());
    if (SPDM_enableFillAuthorForPredict()) {
      item_id_info->set_unit_id(p_ad->get_unit_id());
      item_id_info->set_author_id(p_ad->get_author_id());
      item_id_info->set_campaign_id(p_ad->get_campaign_id());
    }
    // item_context
    auto* item_context = predict_request_->add_item_context();
    item_context->set_item_id(p_ad->get_creative_id());
    // top_layer embedding
    FillItemContexItemTopLayer(item_context, p_ad);
    // ue_score
    FillItemContexItemUescore(item_context, p_ad);
    // rank index
    FillItemContexItemRankIndex(item_context, p_ad, &photo_rank_indices);
    // item_id info
    auto* item_context_item_id_info = item_context->mutable_item_id_info();
    item_context_item_id_info->CopyFrom(*item_id_info);
    // callback
    if (callback_event != kuaishou::ad::AdCallbackLog::EVENT_UNKNOWN) {
      item_context->set_callback_event(callback_event);
    }
  }
}

void InnerMergeHardRocketPredictHandler::FillRankIndexInfo(const std::vector<AdCommon*>& ad_list,
  std::unordered_map<google::protobuf::int64, std::unordered_map<int64_t, float>>* rank_index_info) {
    using kuaishou::ad::ContextInfoCommonAttr;
    const auto& add_photo_uescore = [&] (const AdCommon* p_ad, int64_t attr_name, const ItemIdx &pxtr_index) {
      float pxtr = p_ad->Attr(pxtr_index).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0);
      if (pxtr > 0.0) {
        (*rank_index_info)[attr_name][p_ad->get_photo_id()] = pxtr;
      }
    };
    const auto& add_inner_merge_rank_score = [&] (const AdCommon* p_ad, int64_t attr_name) {
      float inner_merge_ctcvr = 0.0;
      if (p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD
          || p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) {
          inner_merge_ctcvr = p_ad->get_predict_score(PredictType::PredictType_c1_order_paid);
      } else if (p_ad->get_queue_type() == RankAdListType::NATIVE_AD
          || p_ad->get_queue_type() == RankAdListType::FANSTOP) {
          inner_merge_ctcvr = p_ad->get_predict_score(PredictType::PredictType_order_paid);
      }
      if (inner_merge_ctcvr > 0.0 &&
        (p_ad->Is(AdFlag::is_ue_merchant_order) || p_ad->Is(AdFlag::is_ue_roas))) {
        (*rank_index_info)[attr_name][p_ad->get_photo_id()] = inner_merge_ctcvr;
      }
    };
    for (const auto& p_ad : ad_list) {
      // 标识 ad 的队列
      std::string source = "";
      if (p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD) {
        source = "normal_live_ad";
      } else if (p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) {
        source = "normal_photo_ad";
      } else if (p_ad->get_queue_type() == RankAdListType::NATIVE_AD) {
        source = "native_ad";
      } else if (p_ad->get_queue_type() == RankAdListType::FANSTOP) {
        source = "fanstop";
      }

      // 添加短视频 ue 分
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PCTR_LIST, ItemIdx::reco_fr_pctr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PLVTR_LIST, ItemIdx::reco_fr_plvtr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PSVTR_LIST, ItemIdx::reco_fr_psvr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PVTR_LIST, ItemIdx::reco_fr_pvtr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PWTD_LIST, ItemIdx::reco_fr_pwtd);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PCPR_LIST, ItemIdx::reco_fr_pcpr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PLTR_LIST, ItemIdx::reco_fr_pltr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PWTR_LIST, ItemIdx::reco_fr_pwtr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PFTR_LIST, ItemIdx::reco_fr_pftr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PCMTR_LIST, ItemIdx::reco_fr_pcmtr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PHTR_LIST, ItemIdx::reco_fr_phtr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PCLICK_LIVE_LIST, ItemIdx::reco_fr_click_live);  // NOLINT
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PEFFECTIVE_WATCH_LIVE_TIME_LIST,
          ItemIdx::reco_fr_effective_watch_live_time);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PPTR_LIST, ItemIdx::reco_fr_pptr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PEPSTR_LIST, ItemIdx::reco_fr_pepstr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PLSTR_LIST, ItemIdx::reco_fr_plstr);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PETCM_LIST, ItemIdx::reco_fr_petcm);
      add_photo_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_PCMEF_LIST, ItemIdx::reco_fr_pcmef);
      // 添加一阶段打分
      add_inner_merge_rank_score(p_ad, ContextInfoCommonAttr::ADRANK_INNER_MERGE_CVR_LIST);

      // 请求成功打点
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_rank_index_request_success", kess_name, source);
    }
}

void InnerMergeHardRocketPredictHandler::BuildRequest() {
  query_start_ts_ = base::GetTimestamp();
  std::unordered_map<google::protobuf::int64, std::unordered_map<int64_t, float>> rank_index_info;
  // 1、填充 cmd 基础数据
  predict_request_->set_llsid(context_data_->get_llsid());
  predict_request_->set_user_id(context_data_->get_user_id());
  predict_request_->add_cmd(cmd2);
  predict_request_->set_item_type(kuaishou::ad::algorithm::ItemType::AD_DSP);
  predict_request_->add_cmdkey(cmd_key2);
  predict_request_->set_skip_adlog_put(true);
  predict_request_->set_req_ver(3);
  predict_request_->set_request_scene(kuaishou::ad::algorithm::RequestScene::REQ_SCENE_INNER);

  auto* cmd_item_mapping = predict_request_->add_cmd_item_mapping();
  cmd_item_mapping->add_cmdkey(cmd_key2);
  cmd_item_mapping->add_cmd(cmd2);
  cmd_item_mapping->add_cmd_value_num(1);
  cmd_item_mapping->add_cmd_type(kuaishou::ad::algorithm::CmdType::CMD_TYPE_DEFAULT);

  // 2、添加 user 特征
  predict_request_->mutable_ad_user_info();

  // 3、填充 predict_request_ hard item 硬广相关信息
  if (SPDM_enable_rank_index_rocket(context_data_->get_spdm_ctx())) {
    // 添加 rank index context 特征
    FillRankIndexInfo(context_data_->get_ad_list().Ads(), &rank_index_info);
    FillRankIndexInfo(context_data_->get_native_ad_list().Ads(), &rank_index_info);
    FillRankIndexInfo(context_data_->get_fanstop_ad_list().Ads(),  &rank_index_info);
    FillItemPredictRequestWithContext(context_data_->get_ad_list().Ads(), cmd_item_mapping, &rank_index_info);
  } else {
    FillItemPredictRequest(context_data_->get_ad_list().Ads(), cmd_item_mapping);
  };

  auto item_size = GetItemSize();
  predict_request_->add_cmdkey_end_pos(item_size);

  // 4、添加 context 特征
  auto* predict_context = predict_request_->mutable_context();
  predict_context->set_sub_page_id(context_data_->get_sub_page_id());


  auto all_size = context_data_->get_ad_list().Ads().size();

  if (item_size > 0 && all_size > 0) {
    RANK_DOT_STATS(context_data_, base::GetTimestamp() - query_start_ts_,
      "inner_merge_hard_request", kess_name, "cost");
    RANK_DOT_STATS(context_data_, item_size * 1e6 / all_size,
                   "inner_merge_hard_request", kess_name, "item_rate");
    RANK_DOT_STATS(context_data_, item_size, "inner_merge_hard_request", kess_name, "item_size");
    RANK_DOT_STATS(context_data_, all_size, "inner_merge_hard_request", kess_name, "all_size");
    RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_request", kess_name, "success");
  } else {
    RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_request_failed", kess_name, "has_item");
  }
}

void InnerMergeHardRocketPredictHandler::ParseResponse() {
  RANK_DOT_STATS(context_data_, base::GetTimestamp() - query_start_ts_,
    "inner_merge_hard_response", kess_name, "cost");
  if (predict_response_->status() != kuaishou::ad::algorithm::PredictStatus::STATUS_OK) {
    RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_response_failed", kess_name, "response_status");
    return;
  }
  double rocket_ctr_sum = 0.0;
  double ctr_sum = 0.0;
  for (const auto& predict_result : predict_response_->predict_result()) {
    auto creative_id = predict_result.item_id();
    auto item_result_list = predict_result.value();
    auto iter = itemid2ad.find(creative_id);
    if (iter == itemid2ad.end()) {
      continue;
    }
    auto p_ad = iter->second;
    // 标识 ad 的队列
    std::string source = "";
    if (p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD) {
      source = "normal_live_ad";
    } else if (p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) {
      source = "normal_photo_ad";
    } else if (p_ad->get_queue_type() == RankAdListType::NATIVE_AD) {
      source = "native_ad";
    } else if (p_ad->get_queue_type() == RankAdListType::FANSTOP) {
      source = "fanstop";
    }
    // 预估值数量校验
    if (item_result_list.size() != 1) {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_response_item_failed",
                     kess_name, "result_size", source);
      continue;
    }
    // 二阶段预估失败，不替换
    double rocket_ctr = item_result_list[0];
    if (rocket_ctr == 0.0) {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_response_item_failed",
                      kess_name, "item_zero", source);
      continue;
    }

    // 监控二阶段 bound 情况
    double rocket_ctr_bound = p_ad->PredictBound(rocket_ctr, cmd_key, p_ad->get_product_name());
    if (std::fabs(rocket_ctr_bound - rocket_ctr) > DBL_EPSILON) {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_response_item_bound",
        kess_name, "rocket_ctr_has_bound", source);
    } else {
      RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_response_item_bound",
        kess_name, "rocket_ctr_no_bound", source);
    }
    RANK_DOT_STATS(context_data_, rocket_ctr_bound * 1e6, "inner_merge_hard_response_item",
        kess_name, "rocket_ctr", source);

    // 一阶段打分监控
    double bound_first_value = 0.0;
    if (p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD
        || p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) {
        bound_first_value = p_ad->get_predict_score(PredictType::PredictType_c1_order_paid);
        p_ad->set_predict_normal_score(engine_base::RType::CVR,
                PredictType::PredictType_c1_order_paid_first, bound_first_value, &bound_first_value,
                cmd_id, cmd_key, cmd_key_id);
    }
    RANK_DOT_STATS(context_data_, bound_first_value * 1e6, "inner_merge_hard_response_item",
      kess_name, "cvr", source);
    rocket_ctr_sum += rocket_ctr_bound;
    ctr_sum += bound_first_value;
  }
  // 一阶段或二阶段预估值全零，直接返回
  if (rocket_ctr_sum == 0 || ctr_sum == 0) {
    RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_response_failed", kess_name, "pcoc_nan");
    return;
  }
  // pcoc 监控
  RANK_DOT_STATS(context_data_, rocket_ctr_sum * 1e6 / ctr_sum,
                "inner_merge_hard_response", kess_name, "pcoc");
    // not use rocket ctr
  bool enable_inner_merge_use_rocket_ctr =
    SPDM_enable_inner_merge_use_rocket_ctr(context_data_->get_spdm_ctx());
  if (!enable_inner_merge_use_rocket_ctr) {
    RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_response_failed", kess_name, "use_rocket_ctr");
    return;
  }
  // 二阶段预估值替换
  for (const auto& predict_result : predict_response_->predict_result()) {
    auto creative_id = predict_result.item_id();
    auto item_result_list = predict_result.value();
    if (item_result_list.size() != 1) {
      continue;
    }
    double rocket_ctr = item_result_list[0];
    if (rocket_ctr == 0.0) {  // item 二阶段预估失败，不替换
      continue;
    }
    auto iter = itemid2ad.find(creative_id);
    if (iter == itemid2ad.end()) {
      continue;
    }
    auto p_ad = iter->second;

    double bound_value = rocket_ctr;
    if (p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD) {
      p_ad->set_predict_normal_score(engine_base::RType::CVR, PredictType::PredictType_c1_order_paid,
                                      rocket_ctr, &bound_value, cmd_id, cmd_key, cmd_key_id);
      p_ad->set_c1_order_paied(p_ad->get_predict_score(PredictType::PredictType_c1_order_paid));
      p_ad->set_c1_order_paied_cmd_id(p_ad->get_predict_cmd_id(PredictType::PredictType_c1_order_paid));
      p_ad->set_c1_order_paied_cmd(p_ad->get_predict_cmd_name(PredictType::PredictType_c1_order_paid));
    } else if (p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) {
      p_ad->set_predict_normal_score(engine_base::RType::CVR, PredictType::PredictType_c1_order_paid,
                                      rocket_ctr, &bound_value, cmd_id, cmd_key, cmd_key_id);
      p_ad->set_c1_order_paied(p_ad->get_predict_score(PredictType::PredictType_c1_order_paid));
      p_ad->set_c1_order_paied_cmd_id(p_ad->get_predict_cmd_id(PredictType::PredictType_c1_order_paid));
    } else if (p_ad->get_queue_type() == RankAdListType::NATIVE_AD) {
      p_ad->set_predict_native_score(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                      rocket_ctr, &bound_value, cmd_id, cmd_key, cmd_key_id);
      p_ad->set_order_paid(p_ad->get_predict_score(PredictType::PredictType_order_paid));
      p_ad->set_order_paid_cmd_id(p_ad->get_predict_cmd_id(PredictType::PredictType_order_paid));
    } else if (p_ad->get_queue_type() == RankAdListType::FANSTOP) {
      p_ad->set_predict_native_score(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                      rocket_ctr, &bound_value, cmd_id, cmd_key, cmd_key_id);
      p_ad->set_order_paid(p_ad->get_predict_score(PredictType::PredictType_order_paid));
    }
    PerfScore(rocket_ctr, rocket_ctr, bound_value,
      "CVR", "PredictType_c1_order_paid",
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
    RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_response_item_succ", kess_name);
  }
  RANK_DOT_COUNT(context_data_, 1, "inner_merge_hard_response_succ", kess_name);
}

}   // namespace ad_rank
}   // namespace ks
