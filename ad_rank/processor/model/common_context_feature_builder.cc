#include <vector>
#include <memory>
#include <unordered_map>
#include <unordered_set>

#include "teams/ad/ad_rank/processor/model/common_context_feature_builder.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "google/protobuf/repeated_field.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/processor/model/utility.h"

using kuaishou::ad::ContextInfoCommonAttr;
using kuaishou::ad::CommonTypeEnum;

namespace ks {
namespace ad_rank {

bool CommonContextFeatureBuilder::InitProcessor() {
  context_table_name_common_ = "rank_context_feature_common";
  return true;
}

bool CommonContextFeatureBuilder::PvInit(ks::platform::AddibleRecoContextInterface* context) {
  // todo: add switch
  auto ps_context_wrapper = context->GetMutablePtrCommonAttr<AdContextWrapper>("ad_context_wrapper");
  if (!ps_context_wrapper || !ps_context_wrapper->Get()) {
    return false;
  }
  auto ps_context = ps_context_wrapper->Get();
  ad_context_ = ps_context->GetMutableContextData<ContextData>();
  if (!ad_context_) {return false;}
  return true;
}

void CommonContextFeatureBuilder::Mix(ks::platform::AddibleRecoContextInterface* context) {
  if (!PvInit(context)) {
    LOG_EVERY_N(ERROR, 10000) << "failed to init CommonContextFeatureBuilder";
    return;
  }
  BuildCommonContextFeatureTable(context);
}

void CommonContextFeatureBuilder::BuildCommonContextFeatureTable(
    ::ks::platform::AddibleRecoContextInterface* context) {
  // 新建 table
  auto* context_feature_table = context->GetOrInsertDataTable(context_table_name_common_);
  if (!context_feature_table) {
    LOG_EVERY_N(ERROR, 1000) << "BuildCommonContextFeatureTable: can not create context feature table.";
    return;
  }
  // 新建 一行
  auto& item = context_feature_table->AddCommonRecoResult(0, 0, 0, 0);

  auto &ad_request = ad_context_->get_rank_request()->ad_request();
  // 新增 列

  if (ad_request.universe_ad_request_info().imp_info_size() > 0) {
    auto* app_id_attr = context_feature_table->GetOrInsertAttr("app_id");
    context->SetStringItemAttr(item, app_id_attr, ad_request.universe_ad_request_info().app_id());    // NOLINT

    auto* page_id_attr = context_feature_table->GetOrInsertAttr("page_id");
    context->SetIntItemAttr(item, page_id_attr, ad_request.universe_ad_request_info().imp_info().begin()->page_id());   // NOLINT

    auto* sub_page_id_attr = context_feature_table->GetOrInsertAttr("sub_page_id");
    context->SetIntItemAttr(item, sub_page_id_attr, ad_request.universe_ad_request_info().imp_info().begin()->sub_page_id());   // NOLINT

    auto* pos_id_attr = context_feature_table->GetOrInsertAttr("pos_id");
    context->SetIntItemAttr(item, pos_id_attr, ad_request.universe_ad_request_info().imp_info().begin()->position_id());  // NOLINT
  }

  if (ad_request.ad_user_session_info().has_ad_request_times()) {
    auto* ad_request_times_attr = context_feature_table->GetOrInsertAttr("ad_request_times");
    context->SetIntItemAttr(item, ad_request_times_attr, ad_request.ad_user_session_info().ad_request_times());   // NOLINT
  }

  FillContextInfo(context, context_feature_table, item);
}

void CommonContextFeatureBuilder::FillContextInfo(::ks::platform::MutableRecoContextInterface *context,
    ::ks::platform::DataFrame *context_feature_table, const ::ks::platform::CommonRecoResult &item) {

  auto &ad_request = ad_context_->get_rank_request()->ad_request();

  bool enable_context_info_completion = SPDM_enable_context_info_completion(ad_context_->get_spdm_ctx());
  if (enable_context_info_completion) {
    if (ad_request.ad_user_session_info().has_ad_session_request_times()) {
      ADD_CONTEXT_INT_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::AD_SESSION_REQUEST_TIMES),
          static_cast<int64_t>(ad_request.ad_user_session_info().ad_session_request_times()));
    }
    if (ad_request.ad_user_session_info().has_ad_inspire_style_pos_request_times()) {
      ADD_CONTEXT_INT_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::AD_INSPIRE_STYLE_POS_REQUEST_TIMES),
          static_cast<int64_t>(ad_request.ad_user_session_info().ad_inspire_style_pos_request_times()));
    }
    if (ad_request.reco_request_info().has_dark_mode()) {
      ADD_CONTEXT_INT_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::DARK_MODE),
          static_cast<int64_t>(ad_request.reco_request_info().dark_mode()));
    }
    if (ad_request.reco_request_info().has_client_volume()) {
      ADD_CONTEXT_FLOAT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::CLIENT_VOLUME),
        static_cast<float>(ad_request.reco_request_info().client_volume()));
    }
    if (ad_request.reco_request_info().has_teenage_age()) {
      ADD_CONTEXT_INT_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::TEENAGE_AGE),
          static_cast<int64_t>(ad_request.reco_request_info().teenage_age()));
    }
    if (ad_request.reco_request_info().has_width()) {
      ADD_CONTEXT_INT_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SCREEN_WIDTH),
          static_cast<int64_t>(ad_request.reco_request_info().width()));
    }
    if (ad_request.reco_request_info().has_height()) {
      ADD_CONTEXT_INT_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SCREEN_HEIGHT),
          static_cast<int64_t>(ad_request.reco_request_info().height()));
    }
    if (ad_request.reco_request_info().has_origin_channel()) {
      ADD_CONTEXT_STRING_COMMON_ATTR(
         static_cast<int32_t>(ContextInfoCommonAttr::ORIGIN_CHANNEL),
          static_cast<std::string>(ad_request.reco_request_info().origin_channel()));
    }
  }

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::BROWSE_TYPE),
      static_cast<int64_t>(ad_request.reco_request_info().browse_type()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::SOURCE),
      static_cast<int64_t>(ad_request.reco_request_info().source()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::SOURCE_PHOTO_ID),
      static_cast<int64_t>(ad_request.reco_request_info().source_photo_id()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_ATTRIBUTE),
      static_cast<int64_t>(ad_request.universe_ad_request_info().medium_attribute()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_UID),
      static_cast<int64_t>(ad_request.universe_ad_request_info().medium_uid()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_INDUSTRY_ID),
      static_cast<int64_t>(ad_request.universe_ad_request_info().medium_industry_id()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_INDUSTRY_ID_V2),
      static_cast<int64_t>(ad_request.universe_ad_request_info().medium_industry_id_v2()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_SUB_INDUSTRY_ID),
      static_cast<int64_t>(ad_request.universe_ad_request_info().medium_sub_industry_id()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_SUB_INDUSTRY_ID_V2),
      static_cast<int64_t>(ad_request.universe_ad_request_info().medium_sub_industry_id_v2()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_GAME_CATEGORY_ID),
      static_cast<int64_t>(ad_request.universe_ad_request_info().medium_game_category_id()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::DEVICE_STAT_BATTERY),
      static_cast<int64_t>(ad_request.reco_request_info().device_stats().device_stat_battery()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::DEVICE_STAT_MEMORY),
      static_cast<int64_t>(ad_request.reco_request_info().device_stats().device_stat_memory()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::DEVICE_STAT_DISKFREE),
      static_cast<int64_t>(ad_request.reco_request_info().device_stats().device_stat_diskfree()));

  // 将鸿蒙等同于安卓
  std::string platform = ad_request.ad_user_info().platform();
  if (platform == "harmony") {
    platform = "android";
  }
  ADD_CONTEXT_STRING_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::DEVICE_PLATFORM),
      platform);

  ADD_CONTEXT_STRING_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::DEVICE_NETWORK),
      std::string(ad_request.ad_user_info().network()));

  if (SPDM_enableNewProductCategoryForModel()) {
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::RANK_ENABLE_NEW_PRODUCT_CATEGORY_FOR_MODEL),
      static_cast<int64_t>(SPDM_enable_new_product_category_for_model(ad_context_->get_spdm_ctx()) ? 1 : 0));
  }

  if (SPDM_enable_model_explore(ad_context_->get_spdm_ctx())) {
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MODEL_EXPLORE_TAG),
      static_cast<int64_t>(ad_context_->get_is_model_explore()));
  }

  if (SPDM_enableSoftPhotoToLivePecTopBar(ad_context_->get_spdm_ctx())) {
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_P2L_SOFT_PEC_TOP_BAR_STATUS),
      static_cast<int64_t>(1));
  }

  if (RankKconfUtil::enableInnerQcpxLiveCvrOneModelTagFea()) {
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INNER_LIVE_QCPX_CVR_ONEMODEL_ORDER),
      static_cast<int64_t>(SPDM_enable_inner_live_qcpx_order(ad_context_->get_spdm_ctx()) ? 1 : 0));
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INNER_LIVE_QCPX_CVR_ONEMODEL_ROAS),
      static_cast<int64_t>(SPDM_enable_inner_live_qcpx_roas(ad_context_->get_spdm_ctx()) ? 1 : 0));
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INNER_LIVE_QCPX_CVR_ONEMODEL_T7ROAS),
      static_cast<int64_t>(SPDM_enable_inner_live_qcpx_t7roas(ad_context_->get_spdm_ctx()) ? 1 : 0));
  }

  if (RankKconfUtil::enableInnerQcpxLiveCvrOneModelBaggingTagFea()) {
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INNER_LIVE_QCPX_CVR_ONEMODEL_BAGGING),
      static_cast<int64_t>(false ? 1 : 0));
  }

  if (RankKconfUtil::enableInnerQcpxLiveCtrP2lOneModelTagFea()) {
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INNER_LIVE_QCPX_CTR_P2L_ONEMODEL_TAG),
      static_cast<int64_t>(SPDM_enable_p2l_multi_predict(ad_context_->get_spdm_ctx()) ? 1 : 0));
  }

  if (RankKconfUtil::enableInnerQcpxLivePecStyleFea()) {
    ADD_CONTEXT_UINT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_LIVE_PEC_STYLE_ABTEST_HASH_ID),
        static_cast<uint64_t>(ad_context_->get_qcpx_live_pec_style_abtest_hash_id()));
    ADD_CONTEXT_UINT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_P2L_SOFT_PEC_STYLE_ABTEST_HASH_ID),
        static_cast<uint64_t>(ad_context_->get_qcpx_p2l_soft_pec_style_abtest_hash_id()));
    ADD_CONTEXT_UINT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_P2L_HARD_PEC_STYLE_ABTEST_HASH_ID),
        static_cast<uint64_t>(ad_context_->get_qcpx_p2l_hard_pec_style_abtest_hash_id()));
    ADD_CONTEXT_INT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_LIVE_PEC_STYLE_ABTEST_HASH_ID_V2),
        std::abs(static_cast<int64_t>(ad_context_->get_qcpx_live_pec_style_abtest_hash_id())));
    ADD_CONTEXT_INT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_P2L_SOFT_PEC_STYLE_ABTEST_HASH_ID_V2),
        std::abs(static_cast<int64_t>(ad_context_->get_qcpx_p2l_soft_pec_style_abtest_hash_id())));
    ADD_CONTEXT_INT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_P2L_HARD_PEC_STYLE_ABTEST_HASH_ID_V2),
        std::abs(static_cast<int64_t>(ad_context_->get_qcpx_p2l_hard_pec_style_abtest_hash_id())));
  }

  if (RankKconfUtil::enableInnerQcpxAbtestParameterFea()) {
    ADD_CONTEXT_INT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_ABTEST_PARAM_HASH_ID),
        std::abs(static_cast<int64_t>(ad_context_->get_qcpx_abtest_param_hash_id())));
  }

  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_PHOTO_PAID_SELFTRAIN_INFO),
    static_cast<int64_t>(SPDM_enable_qcpx_photo_paid_elastic_piecewise_model(ad_context_->get_spdm_ctx()) ? 1 : 0));    // NOLINT
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INNER_LOOP_QCPX_PHOTO_ROAS_SELFTRAIN_INFO),
    static_cast<int64_t>(SPDM_enable_qcpx_photo_roas_elastic_piecewise_model(ad_context_->get_spdm_ctx()) ? 1 : 0));    // NOLINT
  /*
  bool is_inner_ps_request =
      (scene_type == "native" || scene_type == "inner_normal" || scene_type == "fanstop");
  这里同时计算inner和outer的候选广告数量并写入Context feature dataframe
  后续填充predict_request再特殊处理
  

  uint64_t inner_ad_list_size = 0;
  uint64_t outer_ad_list_size = 0;
  (*ad_context_->mutable_ad_list()).ForEach([&inner_ad_list_size, &outer_ad_list_size] (auto* p_ad) {
    if (p_ad->Is(AdFlag::is_inner_loop_ad)) ++inner_ad_list_size;
    if (p_ad->Is(AdFlag::is_outer_loop_ad)) ++outer_ad_list_size;
  });

  auto* inner_ad_list_size_attr = context_feature_table->GetOrInsertAttr("inner_ad_list_size");
  item.SetIntAttr(inner_ad_list_size_attr, inner_ad_list_size);

  auto* outer_ad_list_size_attr = context_feature_table->GetOrInsertAttr("outer_ad_list_size");
  item.SetIntAttr(outer_ad_list_size_attr, outer_ad_list_size);
  */

  if (ad_context_->get_is_kuaishou_traffic() || ad_context_->get_is_nebula_request()) {
    FillSmallDirectCallInfo(context, context_feature_table, item);
  }

  if ( SPDM_jixuMixRank() ) {
    GetValueMixRank(context, context_feature_table, item);
  }

  AddUniverseExtContextInfoCommonAttr(context_feature_table, item);

  ADD_CONTEXT_UINT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::AB_TEST_HASH_ID),
      static_cast<uint64_t>(ad_context_->get_ab_test_hash_id()));

  if (ad_request.universe_ad_request_info().imp_info_size() > 0) {
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::AD_STYLE),
      static_cast<int64_t>(ad_request.universe_ad_request_info().
                                                  imp_info().begin()->ad_style()));
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_RENDER_TYPE),
      static_cast<int64_t>(ad_request.universe_ad_request_info().
                                                  imp_info().begin()->render_type()));
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_TEMPLATE_ID),
      static_cast<int64_t>(ad_request.universe_ad_request_info().
                                                  imp_info().begin()->template_id()));
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_REWARDED_TYPE),
      static_cast<int64_t>(ad_request.universe_ad_request_info().
                                                  imp_info().begin()->rewarded_type()));
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::MEDIUM_REWARDED_NUM),
      static_cast<int64_t>(ad_request.universe_ad_request_info().
                                                  imp_info().begin()->rewarded_num()));

    int64_t ad_style =
        static_cast<int64_t>(ad_request.universe_ad_request_info().imp_info().begin()->ad_style());
    int64_t medium_industry_id_v2 =
        static_cast<int64_t>(ad_request.universe_ad_request_info().medium_industry_id_v2());
    int64_t medium_key = medium_industry_id_v2 * 1000 + ad_style;

    // 内循环媒体行业叉乘广告场景下 7 日 CTR 均值
    float ctr_7d = 0.0;
    ADD_CONTEXT_FLOAT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::UNIV_NEW_MEDIUM_INDUSTRY_ID_X_AD_STYLE_CTR_7D),
        static_cast<float>(ctr_7d));

    // 内循环媒体行业叉乘广告场景下 30 日 GMV 均值
    float gmv_30d = 0;
    ADD_CONTEXT_FLOAT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::UNIV_NEW_MEDIUM_INDUSTRY_ID_X_AD_STYLE_GMV_30D),
        static_cast<float>(gmv_30d));
    // 内循环媒体行业叉乘广告场景下 TOP GMV 作者列表
    const std::vector<int64_t> top_gmv_author_list;
    ADD_CONTEXT_INT_LIST_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::
          UNIV_NEW_MEDIUM_INDUSTRY_ID_X_AD_STYLE_TOP_GMV_AUTHOR_LIST_30D),
          top_gmv_author_list);
  }

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::COOPERATION_MODE),
      static_cast<int64_t>(ad_request.universe_ad_request_info().cooperation_mode()));

  if (ad_context_->get_is_splash_request() && RankKconfUtil::enableSplashModifyPs()) {
    ADD_CONTEXT_INT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::INTERACTIVE_FORM),
        static_cast<int64_t>(static_cast<int64_t>(kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS)));
  } else {
    ADD_CONTEXT_INT_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::INTERACTIVE_FORM),
        static_cast<int64_t>(ad_context_->get_pos_manager_base().GetInteractiveForm()));
  }

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::REFRESH_DIRECTION),
      static_cast<int64_t>(ad_context_->get_pos_manager_base().GetRefreshDirection()));

  // ContextInfoCommonAttr::IS_NATIVE_AD_REQUEST 特殊处理
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::LAST_PV_AD_POS),
      static_cast<int64_t>(ad_request.debug_message().req_pv_info().last_explore_pv_last_ad_pos()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::LAST_PV_TIMESTAMP),
      static_cast<int64_t>(ad_request.debug_message().req_pv_info().last_explore_pv_timestamp()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::LAST_FIRST_SCREEN_AD_TIMESTAMP),
      static_cast<int64_t>(ad_request.debug_message().req_pv_info().first_screen_ad_shw_timestamp()));

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::LAST_PV_PAGE_SIZE),
      static_cast<int64_t>(ad_request.debug_message().req_pv_info().last_explore_pv_page_size()));

  if (ad_request.has_page()) {
    ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::CURRENT_PAGE_NUMBER),
      static_cast<int64_t>(ad_request.page()));
  }
  if (SPDM_enable_fill_inner_trigger_hetu_emb(ad_context_->get_spdm_ctx())) {
    std::vector<float> inner_trigger_hetu_emb;
    for (const auto& score :
         ad_context_->get_rank_request()->ad_request().feed_triggle_hetu_info().score()) {
      inner_trigger_hetu_emb.emplace_back(score);
    }
    ADD_CONTEXT_FLOAT_LIST_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INNER_TRIGGER_HETU_EMB),
      inner_trigger_hetu_emb);
  }
  if (ad_context_->get_pos_manager_base().IsWanhe()) {
    AddWanheCreatorContextInfoCommonAttr(context_feature_table, item);
  }

  if ((ad_context_->get_sub_page_id() == 100012194 || ad_context_->get_sub_page_id() == 10002001) && SPDM_enable_add_chuangxin_model_feature(ad_context_->get_spdm_ctx())) { // NOLINT
    AddChuangXinModelFeatureContextInfoCommonAttr(context_feature_table, item);
  }

  if (ad_context_->get_is_rewarded() || ad_context_->get_is_inspire_live_request()) {
    AddIncentiveContextInfoCommonAttr(context_feature_table, item);
  }
  AddUser5RAuthorLstContextInfoCommonAttr(context_feature_table, item);
  AddBuyerEffectiveTypeContextInfoCommonAttr(context_feature_table, item);

  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::GESTURE_TYPE),
      static_cast<int64_t>(ad_request.gesture_type()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::CLIENT_VOLUME_V2),
      static_cast<int64_t>(ad_request.client_volume()));

  if (ad_request.ad_user_info().history_request_infos_size() > 0) {
    size_t l = ad_request.ad_user_info().history_request_infos_size();
    const auto& tmp = ad_request.ad_user_info().history_request_infos();

    std::vector<int64_t> sub_page_ids(l);
    std::vector<int64_t> request_timestamps(l);
    std::vector<int64_t> page_numbers(l);
    for (size_t i = 0; i < l; ++i) {
      sub_page_ids[i] = tmp[i].sub_page_id();
      request_timestamps[i] = static_cast<int64_t>(tmp[i].request_timestamp());
      // 没有刷次, 填 -2 默认值, 区分开其他值
      page_numbers[i] = tmp[i].has_page_number()? tmp[i].page_number(): -2;
    }

    ADD_CONTEXT_INT_LIST_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::HIST_REQ_SUB_PAGE_IDS),
          sub_page_ids);
    ADD_CONTEXT_INT_LIST_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::HIST_REQ_TIMESTAMPS),
          request_timestamps);
    ADD_CONTEXT_INT_LIST_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::HIST_REQ_PAGE_NUMS),
          page_numbers);
  }
}
void CommonContextFeatureBuilder::AddBuyerEffectiveTypeContextInfoCommonAttr(
    ::ks::platform::DataFrame *context_feature_table, const ::ks::platform::CommonRecoResult &item) {
  auto &ad_request = ad_context_->get_rank_request()->ad_request();
  std::string buyer_type_value;
  if (ad_request.ad_user_info().has_user_level_v2_risk()) {
    auto buyer_type_realtime
      = ad_request.ad_user_info().user_level_v2_risk();
    if (buyer_type_realtime == "u0-potential" || buyer_type_realtime == "risk") {
      buyer_type_value = "U0";
    } else if (buyer_type_realtime == "u0") {
      buyer_type_value = "U0+";
    } else if (buyer_type_realtime == "u1") {
      buyer_type_value = "U1";
    } else if (buyer_type_realtime == "u2") {
      buyer_type_value = "U2";
    } else if (buyer_type_realtime == "u3") {
      buyer_type_value = "U3";
    } else if (buyer_type_realtime == "u4") {
      buyer_type_value = "U4";
    } else if (buyer_type_realtime == "u4+") {
      buyer_type_value = "U4+";
    }
    ADD_CONTEXT_STRING_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::BUYER_EFFECTIVE_TYPE),
      buyer_type_value);
  } else {
    auto buyer_type
      = ad_request.ad_user_info().buyer_effective_type();
    if (!ad_request.ad_user_info().has_buyer_effective_type()) {
      buyer_type_value = "U0";
    } else if (buyer_type== "U0") {
      buyer_type_value = "U0+";
    } else if (buyer_type == "U1") {
      buyer_type_value = "U1";
    } else if (buyer_type == "U2") {
      buyer_type_value = "U2";
    } else if (buyer_type == "U3") {
      buyer_type_value = "U3";
    } else if (buyer_type == "U4") {
      buyer_type_value = "U4";
    } else if (buyer_type == "U4+") {
      buyer_type_value = "U4+";
    }
    ADD_CONTEXT_STRING_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::BUYER_EFFECTIVE_TYPE),
      buyer_type_value);
  }
}

void CommonContextFeatureBuilder::AddUser5RAuthorLstContextInfoCommonAttr(
    ::ks::platform::DataFrame *context_feature_table, const ::ks::platform::CommonRecoResult &item) {
  auto &ad_request = ad_context_->get_rank_request()->ad_request();
  const int32_t max_author_num = 100;
  int32_t r3_author_num = 0;
  int32_t r4_author_num = 0;
  int32_t r5_author_num = 0;

  std::vector<int64_t> r3_author_lst(max_author_num);
  std::vector<int64_t> r4_author_lst(max_author_num);
  std::vector<int64_t> r5_author_lst(max_author_num);

  const auto& strategy_crowd_info = ad_request.ad_user_info().strategy_crowd_info();
  for (const auto& crowd_info : strategy_crowd_info) {
    int64_t author_id = crowd_info.author_id();
    for (const auto& crowd_tag : crowd_info.tag()) {
      if (crowd_tag == kuaishou::ad::StrategyCrowdTag::BRAND_OFFLINE_R3 && r3_author_num <= max_author_num) {
        r3_author_lst.push_back(author_id);
        r3_author_num++;
      } else if (crowd_tag == kuaishou::ad::StrategyCrowdTag::BRAND_OFFLINE_R4
        && r4_author_num <= max_author_num) {
        r4_author_lst.push_back(author_id);
        r4_author_num++;
      } else if (crowd_tag == kuaishou::ad::StrategyCrowdTag::BRAND_OFFLINE_R5
        && r5_author_num <= max_author_num) {
        r5_author_lst.push_back(author_id);
        r5_author_num++;
      }
    }
  }
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::USER_5R_R3_AUTHOR_LST),
          r3_author_lst);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::USER_5R_R4_AUTHOR_LST),
          r4_author_lst);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
        static_cast<int32_t>(ContextInfoCommonAttr::USER_5R_R5_AUTHOR_LST),
          r5_author_lst);
}

void CommonContextFeatureBuilder::AddChuangXinModelFeatureContextInfoCommonAttr(::ks::platform::DataFrame *context_feature_table,  // NOLINT
  const ::ks::platform::CommonRecoResult &item) {
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_ABTEST_EXPECTED_CHARGED_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();   // NOLINT
  AUTHOR_ID_ABTEST_EXPECTED_CHARGED_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_ABTEST_EXPECTED_CHARGED_6);  // NOLINT
  AUTHOR_ID_ABTEST_EXPECTED_CHARGED_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_ABTEST_EXPECTED_CHARGED_6"), AUTHOR_ID_ABTEST_EXPECTED_CHARGED_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_AD_DELIVERY_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_AD_DELIVERY_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_AD_DELIVERY_CNT_6);  // NOLINT
  AUTHOR_ID_AD_DELIVERY_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_AD_DELIVERY_CNT_6"), AUTHOR_ID_AD_DELIVERY_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_AD_SHOW_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_AD_SHOW_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_AD_SHOW_CNT_6);  // NOLINT
  AUTHOR_ID_AD_SHOW_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_AD_SHOW_CNT_6"), AUTHOR_ID_AD_SHOW_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_CONVERSION_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_CONVERSION_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_CONVERSION_CNT_6);  // NOLINT
  AUTHOR_ID_CONVERSION_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_CONVERSION_CNT_6"), AUTHOR_ID_CONVERSION_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_COST_TOTAL_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_COST_TOTAL_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_COST_TOTAL_6);  // NOLINT
  AUTHOR_ID_COST_TOTAL_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_COST_TOTAL_6"), AUTHOR_ID_COST_TOTAL_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_COVER_SHOW_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_COVER_SHOW_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_COVER_SHOW_CNT_6);  // NOLINT
  AUTHOR_ID_COVER_SHOW_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_COVER_SHOW_CNT_6"), AUTHOR_ID_COVER_SHOW_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_ITEM_CLICK_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_ITEM_CLICK_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_ITEM_CLICK_CNT_6);  // NOLINT
  AUTHOR_ID_ITEM_CLICK_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_ITEM_CLICK_CNT_6"), AUTHOR_ID_ITEM_CLICK_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_PHOTO_PLAYED_3S_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_PHOTO_PLAYED_3S_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_PLAYED_3S_CNT_6);  // NOLINT
  AUTHOR_ID_PHOTO_PLAYED_3S_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_PLAYED_3S_CNT_6"), AUTHOR_ID_PHOTO_PLAYED_3S_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_PHOTO_PLAYED_5S_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_PHOTO_PLAYED_5S_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_PLAYED_5S_CNT_6);  // NOLINT
  AUTHOR_ID_PHOTO_PLAYED_5S_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_PLAYED_5S_CNT_6"), AUTHOR_ID_PHOTO_PLAYED_5S_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_PHOTO_PLAYED_DURATION_MS_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_PHOTO_PLAYED_DURATION_MS_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_PLAYED_DURATION_MS_6);  // NOLINT
  AUTHOR_ID_PHOTO_PLAYED_DURATION_MS_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_PLAYED_DURATION_MS_6"), AUTHOR_ID_PHOTO_PLAYED_DURATION_MS_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_PHOTO_PLAYED_END_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_PHOTO_PLAYED_END_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_PLAYED_END_CNT_6);  // NOLINT
  AUTHOR_ID_PHOTO_PLAYED_END_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_PLAYED_END_CNT_6"), AUTHOR_ID_PHOTO_PLAYED_END_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> AUTHOR_ID_PHOTO_REPLAYED_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  AUTHOR_ID_PHOTO_REPLAYED_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_REPLAYED_CNT_6);  // NOLINT
  AUTHOR_ID_PHOTO_REPLAYED_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_AUTHOR_ID_PHOTO_REPLAYED_CNT_6"), AUTHOR_ID_PHOTO_REPLAYED_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_ABTEST_EXPECTED_CHARGED_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_ABTEST_EXPECTED_CHARGED_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_ABTEST_EXPECTED_CHARGED_6);  // NOLINT
  INDUSTRY_ID_V3_ABTEST_EXPECTED_CHARGED_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_ABTEST_EXPECTED_CHARGED_6"), INDUSTRY_ID_V3_ABTEST_EXPECTED_CHARGED_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_AD_DELIVERY_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_AD_DELIVERY_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_AD_DELIVERY_CNT_6);  // NOLINT
  INDUSTRY_ID_V3_AD_DELIVERY_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_AD_DELIVERY_CNT_6"), INDUSTRY_ID_V3_AD_DELIVERY_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_AD_SHOW_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_AD_SHOW_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_AD_SHOW_CNT_6);  // NOLINT
  INDUSTRY_ID_V3_AD_SHOW_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_AD_SHOW_CNT_6"), INDUSTRY_ID_V3_AD_SHOW_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_CONVERSION_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_CONVERSION_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_CONVERSION_CNT_6);  // NOLINT
  INDUSTRY_ID_V3_CONVERSION_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_CONVERSION_CNT_6"), INDUSTRY_ID_V3_CONVERSION_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_COST_TOTAL_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_COST_TOTAL_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_COST_TOTAL_6);  // NOLINT
  INDUSTRY_ID_V3_COST_TOTAL_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_COST_TOTAL_6"), INDUSTRY_ID_V3_COST_TOTAL_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_COVER_SHOW_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_COVER_SHOW_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_COVER_SHOW_CNT_6);  // NOLINT
  INDUSTRY_ID_V3_COVER_SHOW_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_COVER_SHOW_CNT_6"), INDUSTRY_ID_V3_COVER_SHOW_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_ITEM_CLICK_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_ITEM_CLICK_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_ITEM_CLICK_CNT_6);  // NOLINT
  INDUSTRY_ID_V3_ITEM_CLICK_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_ITEM_CLICK_CNT_6"), INDUSTRY_ID_V3_ITEM_CLICK_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_PHOTO_PLAYED_3S_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_PHOTO_PLAYED_3S_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_PLAYED_3S_CNT_6);  // NOLINT
  INDUSTRY_ID_V3_PHOTO_PLAYED_3S_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_PLAYED_3S_CNT_6"), INDUSTRY_ID_V3_PHOTO_PLAYED_3S_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_PHOTO_PLAYED_5S_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_PHOTO_PLAYED_5S_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_PLAYED_5S_CNT_6);  // NOLINT
  INDUSTRY_ID_V3_PHOTO_PLAYED_5S_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_PLAYED_5S_CNT_6"), INDUSTRY_ID_V3_PHOTO_PLAYED_5S_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_PHOTO_PLAYED_DURATION_MS_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_PHOTO_PLAYED_DURATION_MS_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_PLAYED_DURATION_MS_6);  // NOLINT
  INDUSTRY_ID_V3_PHOTO_PLAYED_DURATION_MS_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_PLAYED_DURATION_MS_6"), INDUSTRY_ID_V3_PHOTO_PLAYED_DURATION_MS_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_PHOTO_PLAYED_END_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_PHOTO_PLAYED_END_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_PLAYED_END_CNT_6);  // NOLINT
  INDUSTRY_ID_V3_PHOTO_PLAYED_END_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_PLAYED_END_CNT_6"), INDUSTRY_ID_V3_PHOTO_PLAYED_END_CNT_6_ATTR);  // NOLINT;
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> INDUSTRY_ID_V3_PHOTO_REPLAYED_CNT_6_ATTR = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();  // NOLINT
  INDUSTRY_ID_V3_PHOTO_REPLAYED_CNT_6_ATTR->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_REPLAYED_CNT_6);  // NOLINT
  INDUSTRY_ID_V3_PHOTO_REPLAYED_CNT_6_ATTR->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);  // NOLINT
  item.SetExtraAttr(context_feature_table->GetOrInsertAttr("ContextInfoCommonAttr_Name_INDUSTRY_ID_V3_PHOTO_REPLAYED_CNT_6"), INDUSTRY_ID_V3_PHOTO_REPLAYED_CNT_6_ATTR);  // NOLINT
  auto traffic_cali_model_fea_combine = ad_context_->get_rank_request()->ad_request().traffic_cali_model_fea_combine();  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_ABTEST_EXPECTED_CHARGED_6_ATTR", AUTHOR_ID_ABTEST_EXPECTED_CHARGED_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_abtest_expected_charged_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_AD_DELIVERY_CNT_6_ATTR", AUTHOR_ID_AD_DELIVERY_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_ad_delivery_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_AD_SHOW_CNT_6_ATTR", AUTHOR_ID_AD_SHOW_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_ad_show_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_CONVERSION_CNT_6_ATTR", AUTHOR_ID_CONVERSION_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_conversion_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_COST_TOTAL_6_ATTR", AUTHOR_ID_COST_TOTAL_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_cost_total_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_COVER_SHOW_CNT_6_ATTR", AUTHOR_ID_COVER_SHOW_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_cover_show_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_ITEM_CLICK_CNT_6_ATTR", AUTHOR_ID_ITEM_CLICK_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_item_click_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_PHOTO_PLAYED_3S_CNT_6_ATTR", AUTHOR_ID_PHOTO_PLAYED_3S_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_photo_played_3s_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_PHOTO_PLAYED_5S_CNT_6_ATTR", AUTHOR_ID_PHOTO_PLAYED_5S_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_photo_played_5s_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_PHOTO_PLAYED_DURATION_MS_6_ATTR", AUTHOR_ID_PHOTO_PLAYED_DURATION_MS_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_photo_played_duration_ms_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_PHOTO_PLAYED_END_CNT_6_ATTR", AUTHOR_ID_PHOTO_PLAYED_END_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_photo_played_end_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("AUTHOR_ID_PHOTO_REPLAYED_CNT_6_ATTR", AUTHOR_ID_PHOTO_REPLAYED_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.author_id_photo_replayed_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_ABTEST_EXPECTED_CHARGED_6_ATTR", INDUSTRY_ID_V3_ABTEST_EXPECTED_CHARGED_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_abtest_expected_charged_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_AD_DELIVERY_CNT_6_ATTR", INDUSTRY_ID_V3_AD_DELIVERY_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_ad_delivery_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_AD_SHOW_CNT_6_ATTR", INDUSTRY_ID_V3_AD_SHOW_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_ad_show_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_CONVERSION_CNT_6_ATTR", INDUSTRY_ID_V3_CONVERSION_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_conversion_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_COST_TOTAL_6_ATTR", INDUSTRY_ID_V3_COST_TOTAL_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_cost_total_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_COVER_SHOW_CNT_6_ATTR", INDUSTRY_ID_V3_COVER_SHOW_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_cover_show_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_ITEM_CLICK_CNT_6_ATTR", INDUSTRY_ID_V3_ITEM_CLICK_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_item_click_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_PHOTO_PLAYED_3S_CNT_6_ATTR", INDUSTRY_ID_V3_PHOTO_PLAYED_3S_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_photo_played_3s_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_PHOTO_PLAYED_5S_CNT_6_ATTR", INDUSTRY_ID_V3_PHOTO_PLAYED_5S_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_photo_played_5s_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_PHOTO_PLAYED_DURATION_MS_6_ATTR", INDUSTRY_ID_V3_PHOTO_PLAYED_DURATION_MS_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_photo_played_duration_ms_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_PHOTO_PLAYED_END_CNT_6_ATTR", INDUSTRY_ID_V3_PHOTO_PLAYED_END_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_photo_played_end_cnt_6())));  // NOLINT
  FillJson2MapInt64Int64Attr("INDUSTRY_ID_V3_PHOTO_REPLAYED_CNT_6_ATTR", INDUSTRY_ID_V3_PHOTO_REPLAYED_CNT_6_ATTR->mutable_map_int64_int64_value(), base::Json(base::StringToJson(traffic_cali_model_fea_combine.industry_id_v3_photo_replayed_cnt_6())));  // NOLINT
}

template <typename T1>
void CommonContextFeatureBuilder::FillJson2MapInt64Int64Attr(const std::string tag, T1 * m, const base::Json & j) {  // NOLINT
  if (!j.IsObject()) {
    return;
  }
  bool is_dot = false;
  for (const auto& o : j.objects()) {
    std::string k_str = o.first;
    int64 k_int = -1;
    if (!absl::SimpleAtoi(k_str, &k_int)) {
      continue;
    }
    if (o.second == nullptr) {
      continue;
    }
    int64 v_int = o.second->IntValue(-1);
    m->insert({k_int, v_int});
    if (k_int != -1 && v_int != -1 && !is_dot) {
      ad_context_->dot_perf->Interval(v_int, "AddChuangXinModelFeatureContextInfoCommonAttr", tag, absl::StrCat("key_", k_int));  // NOLINT
      is_dot = true;
    }
  }
}

void CommonContextFeatureBuilder::AddWanheCreatorContextInfoCommonAttr(
    ::ks::platform::DataFrame *context_feature_table, const ::ks::platform::CommonRecoResult &item) {
  auto &ad_request = ad_context_->get_rank_request()->ad_request();
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_ID),
      static_cast<int64_t>(ad_request.creator_uid()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_CROSS_SECTION_FIRST_CLASSS_ID),
      static_cast<int64_t>(ad_request.wanhe_creator_info().final_cross_section_first_class_id()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_CROSS_SECTION_SECOND_CLASSS_ID),
      static_cast<int64_t>(ad_request.wanhe_creator_info().final_cross_section_second_class_id()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_FNAS_USER_NUM),
      static_cast<int64_t>(ad_request.wanhe_creator_info().fans_user_num()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_IS_OP_AUTHOR),
      static_cast<int64_t>(ad_request.wanhe_creator_info().is_op_author()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_PHOTO_ACTIVE_STATUS),
      static_cast<int64_t>(ad_request.wanhe_creator_info().photo_active_status()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_QUALITY_FIRST_KEY),
      static_cast<int64_t>(ad_request.wanhe_creator_info().quality_first_key()));
  ADD_CONTEXT_STRING_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_GENDER),
      ad_request.wanhe_creator_info().gender());
  ADD_CONTEXT_STRING_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_FRE_CITY_LEVEL),
      ad_request.wanhe_creator_info().fre_city_level());
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_IS_PHOTO_MCN_USER),
      static_cast<int64_t>(ad_request.wanhe_creator_info().is_photo_mcn_user()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_IS_BUSI_AUTHOR),
      static_cast<int64_t>(ad_request.wanhe_creator_info().is_busi_author()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_IS_COMMUNITY_GOOD_AUTHOR),
      static_cast<int64_t>(ad_request.wanhe_creator_info().is_community_good_author()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_FIRST_CATEGORY_ID),
      static_cast<int64_t>(ad_request.wanhe_creator_info().first_category_id()));
  ADD_CONTEXT_INT_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::WANHE_CREATOR_SECOND_CATEGORY_ID),
      static_cast<int64_t>(ad_request.wanhe_creator_info().second_category_id()));
}

void CommonContextFeatureBuilder::AddIncentiveContextInfoCommonAttr(
    ::ks::platform::DataFrame *context_feature_table, const ::ks::platform::CommonRecoResult &item) {
  auto *ad_request = ad_context_->mutable_rank_request()->mutable_ad_request();
  auto *inspire_req_info = ad_request->mutable_inspire_req_info();
  int64_t req_scene_type = inspire_req_info->request_scene_type();
  if (ad_context_->get_is_inspire_live_request() && !ad_context_->get_is_rewarded()) {
    base::Json json_extra = base::Json(base::StringToJson(ad_request->extra_request_ctx()));
    if (json_extra.IsObject()) {
      req_scene_type = json_extra.GetInt("requestSceneType", 0);
    }
  }
  auto &progress_extra_reward_info = inspire_req_info->progress_extra_reward_info();
  int64_t progress_extra_mission = progress_extra_reward_info.mission_per_day();
  int64_t progress_extra_reward = progress_extra_reward_info.reward_per_day();
  auto* task_progress_info = inspire_req_info->mutable_task_progress_info();
  if (ad_context_->get_is_inspire_live_request() && !ad_context_->get_is_rewarded()) {
    task_progress_info = ad_request->mutable_inspire_task_progress_info();
  }
  int64_t task_id = task_progress_info->task_id();
  int64_t daily_mission_limit = task_progress_info->daily_user_limit();
  auto &total_reward_sum_map = task_progress_info->total_reward_sum_map();
  int64_t same_day_common_view_count = 0;
  int64_t same_day_common_reward_count = 0;
  int64_t same_day_common_reward_amount = 0;
  int64_t same_day_again_view_count = 0;
  int64_t same_day_again_reward_count = 0;
  int64_t same_day_again_reward_amount = 0;
  int64_t same_day_deep_reward_count = 0;
  int64_t same_day_deep_reward_amount = 0;
  for (const auto& pair : total_reward_sum_map) {
    if (pair.first == "COMMON_BIZ") {
      same_day_common_view_count = pair.second.total_view_count();
      same_day_common_reward_count = pair.second.total_reward_count();
      same_day_common_reward_amount = pair.second.total_reward_amount();
    } else if (pair.first == "ONCE_AGAIN_BIZ") {
      same_day_again_view_count = pair.second.total_view_count();
      same_day_again_reward_count = pair.second.total_reward_count();
      same_day_again_reward_amount = pair.second.total_reward_amount();
    } else if (pair.first != "UNKNOWN_BIZ") {
      same_day_deep_reward_count = pair.second.total_reward_count();
      same_day_deep_reward_amount = pair.second.total_reward_amount();
    }
  }
  int64_t same_day_common_avg_reward = 0;
  if (same_day_common_reward_count > 0) {
    same_day_common_avg_reward = same_day_common_reward_amount / same_day_common_reward_count;
  }
  int64_t same_day_again_avg_reward = 0;
  if (same_day_again_reward_count > 0) {
    same_day_again_avg_reward = same_day_again_reward_amount / same_day_again_reward_count;
  }
  auto &task_reward_record_list = task_progress_info->task_reward_record_list();
  int64_t last_req_scene_type = 0;
  int64_t last_task_type = 0;
  int64_t last_req_seconds_interval = 0;
  int64_t last_three_avg_reward = 0;
  int64_t last_task_coin = 0;
  int64_t last_ad_price = 0;
  int64_t last_ad_cpm = 0;
  std::vector<int64_t> task_coin_list;
  std::vector<int64_t> task_type_list;
  std::vector<int64_t> task_seq_view_coin_list;      // action 序列， 长度 K-1
  std::vector<int64_t> task_seq_price_list;          // reward 序列， 长度 K-1
  std::vector<int64_t> task_seq_per5_sum_view_coin;  // state sum view coin，长度 K
  std::vector<int64_t> task_seq_pre5_avg_view_coin;  // state avg view coin，长度 K
  std::vector<int64_t> task_seq_pre5_avg_price;      // state price，长度 K
  std::vector<int64_t> task_seq_till_now_cnt;        // state till now 次数，长度 K
  std::vector<int64_t> task_seq_till_now_amt;        // state till now 总金币，长度 K
  // sar for state action reward
  int task_seq_len = SPDM_incntv_ad_sar_task_seq_len(ad_context_->get_spdm_ctx());
  int64_t total_award_cnt = same_day_common_reward_count + same_day_again_reward_count;
  int64_t total_award_amt = same_day_common_reward_amount + same_day_again_reward_amount;
  if (task_reward_record_list.size() > 0) {
    last_req_scene_type = task_reward_record_list[0].req_scene_type();
    last_task_type = task_reward_record_list[0].task_type();
    last_task_coin = task_reward_record_list[0].reward_amount();
    last_ad_price = task_reward_record_list[0].ad_price();
    last_ad_cpm = task_reward_record_list[0].ad_cpm();
    int64_t reward_time = task_reward_record_list[0].reward_time();
    last_req_seconds_interval = (ad_context_->get_current_timestamp_nodiff() / 1000 - reward_time) / 1000;
    int64_t last_three_reward_amount = 0;
    int64_t last_three_reward_count = 0;

    // 先统计 top 5 窗口
    int64_t pre5_view_coin_sum = 0;
    int64_t pre5_price_sum = 0;
    int64_t pre5_task_cnt = 0;
    for (int i = 0; i < task_reward_record_list.size() && pre5_task_cnt < 5; i++) {
      auto& task_info = task_reward_record_list[i];
      if ((task_info.task_type() == kuaishou::ad::InspireTaskProgressInfo_InspireTaskType_COMMON_BIZ ||
            task_info.task_type() == kuaishou::ad::InspireTaskProgressInfo_InspireTaskType_ONCE_AGAIN_BIZ)) {
        pre5_view_coin_sum += task_info.reward_amount();
        pre5_price_sum += task_info.ad_cpm() / 1000000L <= task_info.ad_price()
                              ? task_info.ad_cpm() / 1000000L
                              : task_info.ad_price();
        pre5_task_cnt += 1;
      }
    }
    if (pre5_task_cnt > 0) {
      task_seq_per5_sum_view_coin.push_back(pre5_view_coin_sum);
      task_seq_pre5_avg_view_coin.push_back(pre5_view_coin_sum / pre5_task_cnt);
      task_seq_pre5_avg_price.push_back(pre5_price_sum / pre5_task_cnt);
      task_seq_till_now_cnt.push_back(total_award_cnt);
      task_seq_till_now_amt.push_back(total_award_amt);
    }

    int task_seq_stat = 0;
    for (int i = 0; i < task_reward_record_list.size(); i++) {
      if (last_three_reward_count < 3) {
        last_three_reward_amount += task_reward_record_list[i].reward_amount();
        if (task_reward_record_list[i].task_type() < 3) {
          last_three_reward_count++;
        }
      }
      task_type_list.emplace_back(task_reward_record_list[i].task_type());
      task_coin_list.emplace_back(task_reward_record_list[i].reward_amount());

      if (task_seq_stat <= task_seq_len) {
        auto& task_info = task_reward_record_list[i];
        if ((task_info.task_type() == kuaishou::ad::InspireTaskProgressInfo_InspireTaskType_COMMON_BIZ ||
             task_info.task_type() == kuaishou::ad::InspireTaskProgressInfo_InspireTaskType_ONCE_AGAIN_BIZ)) {
          task_seq_stat += 1;
          // 减少 cpa / cpc 的 price 不可比影响
          int64_t price = task_info.ad_cpm() / 1000000L <= task_info.ad_price()
                              ? task_info.ad_cpm() / 1000000L
                              : task_info.ad_price();
          task_seq_price_list.insert(task_seq_price_list.begin(), price);
          task_seq_view_coin_list.insert(task_seq_view_coin_list.begin(), task_info.reward_amount());

          // 当前 state 的 pre5
          // 窗口 统计值 滑动
          pre5_view_coin_sum -= task_info.reward_amount();
          pre5_price_sum -= price;
          pre5_task_cnt -= 1;
          if (i + 5 < task_reward_record_list.size()) {
            auto task_info_gap5 = task_reward_record_list[i + 5];
            pre5_view_coin_sum += task_info_gap5.reward_amount();
            pre5_price_sum += task_info_gap5.ad_cpm() / 1000000L <= task_info_gap5.ad_price()
                                  ? task_info_gap5.ad_cpm() / 1000000L
                                  : task_info_gap5.ad_price();
            pre5_task_cnt += 1;
          }
          task_seq_per5_sum_view_coin.insert(task_seq_per5_sum_view_coin.begin(), pre5_view_coin_sum);
          task_seq_pre5_avg_view_coin.insert(task_seq_pre5_avg_view_coin.begin(),
                                             pre5_task_cnt > 0 ? pre5_view_coin_sum / pre5_task_cnt : 0);
          task_seq_pre5_avg_price.insert(task_seq_pre5_avg_price.begin(),
                                         pre5_task_cnt > 0 ? pre5_price_sum / pre5_task_cnt : 0);
          // 当前 state 的 till now 值
          total_award_cnt -= 1;
          task_seq_till_now_cnt.insert(task_seq_till_now_cnt.begin(), total_award_cnt);
          total_award_amt -= task_info.reward_amount();
          task_seq_till_now_amt.insert(task_seq_till_now_amt.begin(), total_award_amt);
        }
      }
    }
    if (last_three_reward_count > 0) {
      last_three_avg_reward = last_three_reward_amount / last_three_reward_count;
    }
  }
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_PROGRESS_EXTRA_MISSION),
    static_cast<int64_t>(progress_extra_mission));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_PROGRESS_EXTRA_REWARD),
    static_cast<int64_t>(progress_extra_reward));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_TASK_ID),
    static_cast<int64_t>(task_id));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_REQ_SCENE_TYPE),
    static_cast<int64_t>(req_scene_type));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_DAILY_MISSION_LIMIT),
    static_cast<int64_t>(daily_mission_limit));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_COMMON_VIEW_COUNT),
    static_cast<int64_t>(same_day_common_view_count));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_AGAIN_VIEW_COUNT),
    static_cast<int64_t>(same_day_again_view_count));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_COMMON_REWARD_COUNT),
    static_cast<int64_t>(same_day_common_reward_count));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_AGAIN_REWARD_COUNT),
    static_cast<int64_t>(same_day_again_reward_count));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_DEEP_REWARD_COUNT),
    static_cast<int64_t>(same_day_deep_reward_count));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_COMMON_REWARD_AMOUNT),
    static_cast<int64_t>(same_day_common_reward_amount));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_AGAIN_REWARD_AMOUNT),
    static_cast<int64_t>(same_day_again_reward_amount));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_DEEP_REWARD_AMOUNT),
    static_cast<int64_t>(same_day_deep_reward_amount));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_COMMON_AVG_REWARD),
    static_cast<int64_t>(same_day_common_avg_reward));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_AGAIN_AVG_REWARD),
    static_cast<int64_t>(same_day_again_avg_reward));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_LAST_REQ_SECONDS_INTERVAL),
    static_cast<int64_t>(last_req_seconds_interval));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_LAST_REQ_SCENE_TYPE),
    static_cast<int64_t>(last_req_scene_type));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_LAST_TASK_TYPE),
    static_cast<int64_t>(last_task_type));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_LAST_TASK_COIN),
    static_cast<int64_t>(last_task_coin));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_LAST_AD_PRICE),
    static_cast<int64_t>(last_ad_price));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_LAST_AD_CPM),
    static_cast<int64_t>(last_ad_cpm));
  ADD_CONTEXT_INT_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_LAST_THREE_AVG_REWARD),
    static_cast<int64_t>(last_three_avg_reward));
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_TASK_TYPE_LIST),
    task_type_list);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_SAME_DAY_TASK_COIN_LIST),
    task_coin_list);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INCENTIVE_TODAY_TASK_SEQ_VIEW_COIN_LIST),
      task_seq_view_coin_list);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INCNETIVE_TODAY_TASK_SEQ_NORMALIZED_PRICE_LIST),
      task_seq_price_list);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INCNETIVE_TODAY_TASK_SEQ_PRE5_SUM_VIEW_COIN_LIST),
      task_seq_per5_sum_view_coin);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INCNETIVE_TODAY_TASK_SEQ_PRE5_AVG_VIEW_COIN_LIST),
      task_seq_pre5_avg_view_coin);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INCNETIVE_TODAY_TASK_SEQ_PRE5_AVG_PRICE_LIST),
      task_seq_pre5_avg_price);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INCNETIVE_TODAY_TASK_SEQ_TILL_NOW_TASK_CNT_LIST),
      task_seq_till_now_cnt);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
      static_cast<int32_t>(ContextInfoCommonAttr::INCNETIVE_TODAY_TASK_SEQ_TILL_NOW_TASK_AMT_LIST),
      task_seq_till_now_amt);
}
void CommonContextFeatureBuilder::AddUniverseExtContextInfoCommonAttr(
    ::ks::platform::DataFrame *context_feature_table, const ::ks::platform::CommonRecoResult &item) {
  const std::string &ext_data =
      ad_context_->get_rank_request()->ad_request().universe_ad_request_info().ext_data();
  if (!ext_data.empty()) {
    base::Json ext_data_json(base::StringToJson(ext_data));
    if (ext_data_json.IsObject()) {
      base::Json* model_info_json = ext_data_json.Get("modeInfo");
      if (model_info_json != NULL && !model_info_json->IsObject()) {
        model_info_json = ext_data_json.Get("modelInfo");
      }
      if (model_info_json != NULL && model_info_json->IsObject()) {
        std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> map_int64_int64_common_attr =
            std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();
        map_int64_int64_common_attr->set_name_value(ContextInfoCommonAttr::MEDIUM_DEVICE_EXT_DATA);
        map_int64_int64_common_attr->set_type(CommonTypeEnum::MAP_INT64_INT64_ATTR);
        auto* map_int64_int64_attr = context_feature_table->GetOrInsertAttr(
          "ContextInfoCommonAttr_Name_MEDIUM_DEVICE_EXT_DATA");
        item.SetExtraAttr(map_int64_int64_attr, map_int64_int64_common_attr);

        auto map_val = map_int64_int64_common_attr->mutable_map_int64_int64_value();
        int cpu_count = model_info_json->GetInt("cpuCount", 0);
        (*map_val)[0] = cpu_count;
        int battery_percent = model_info_json->GetInt("batteryPercent", -1);
        (*map_val)[1] = battery_percent;
        int64 total_memory_size = model_info_json->GetInt("totalMemorySize", -1);
        (*map_val)[2] = total_memory_size;
        int64 available_memory_size = model_info_json->GetInt("availableMemorySize", -1);
        (*map_val)[3] = available_memory_size;
        int64 total_disk_size = model_info_json->GetInt("totalDiskSize", -1);
        (*map_val)[4] = total_disk_size;
        int64 available_disk_size = model_info_json->GetInt("availableDiskSize", -1);
        (*map_val)[5] = available_disk_size;
      }
    }
  }
}

void CommonContextFeatureBuilder::FillSmallDirectCallInfo(
    ::ks::platform::MutableRecoContextInterface *context,
    ::ks::platform::DataFrame *context_feature_table, const ::ks::platform::CommonRecoResult &item) {
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> direct_call_map_common_attr =
    std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();
  direct_call_map_common_attr->set_name_value(
    kuaishou::ad::ContextInfoCommonAttr_Name_SMALL_GAME_DIRECT_CALL_ITEM_LIST);
  direct_call_map_common_attr->set_type(
    kuaishou::ad::CommonTypeEnum_AttrType_MAP_UNIT64_BOOL_ATTR);
  auto* direct_call_map_attr = context_feature_table->GetOrInsertAttr(
    "ContextInfoCommonAttr_Name_SMALL_GAME_DIRECT_CALL_ITEM_LIST");
  item.SetExtraAttr(direct_call_map_attr, direct_call_map_common_attr);

  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> site_id_map_common_attr =
    std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();
  site_id_map_common_attr->set_name_value(
    kuaishou::ad::ContextInfoCommonAttr_Name_SMALL_GAME_DIRECT_CALL_SITE_ID_LIST);
  site_id_map_common_attr->set_type(
    kuaishou::ad::CommonTypeEnum_AttrType_MAP_UNIT64_UNIT64_ATTR);
  auto* site_id_map_attr = context_feature_table->GetOrInsertAttr(
    "ContextInfoCommonAttr_Name_SMALL_GAME_DIRECT_CALL_SITE_ID_LIST");
  item.SetExtraAttr(site_id_map_attr, site_id_map_common_attr);

  auto direct_call_map = direct_call_map_common_attr->mutable_map_unit64_bool_value();
  auto site_id_map = site_id_map_common_attr->mutable_map_unit64_unit64_value();

  std::string tmp_ab_key{""};
  for (const auto* ad : ad_context_->get_ad_list().Ads()) {
    // 判断是否启用了小游戏组件
    if (((ad->get_landing_page_component() & kuaishou::ad::AdEnum_PageComponentType_SMALL_GAME_NEW) >> 1) !=
        1) {
      continue;
    }
    if (ad->get_direct_call_type() == 1) {
      // 广告主开了直调
      direct_call_map->insert({ad->get_creative_id(), true});
    } else {
      // 广告主没开直调，判断是否能进行暗改
      if (!RankKconfUtil::smallGameForceDirectAbTestConf()->data().GetProductAbTestSwitch(
              ad->get_product_name(), &tmp_ab_key)) {
        // 这个广告不在暗改配置里面, 直接传 false
        direct_call_map->insert({ad->get_creative_id(), false});
      } else {
        auto iter = ad_context_->get_small_game_ab_test_values().find(tmp_ab_key);
        if (iter == ad_context_->get_small_game_ab_test_values().end()) {
          continue;
        }
        // 在暗改配置里面, 看看是否命中了实验
        direct_call_map->insert({ad->get_creative_id(), iter->second});
      }
    }
    site_id_map->insert({ad->get_creative_id(), ad->get_site_id()});
  }
}

void CommonContextFeatureBuilder::GetValueMixRank(
    ::ks::platform::MutableRecoContextInterface *context,
    ::ks::platform::DataFrame *context_feature_table, const ::ks::platform::CommonRecoResult &item) {
  std::shared_ptr<kuaishou::ad::ContextInfoCommonAttr> test_map_common_attr =
    std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();
  test_map_common_attr->set_name_value(
    kuaishou::ad::ContextInfoCommonAttr_Name_MIX_RANK_ALL_ADS);
  test_map_common_attr->set_type(
    kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_FLOAT_ATTR);
  auto* test_id_map_attr = context_feature_table->GetOrInsertAttr(
    "ContextInfoCommonAttr_Name_MIX_RANK_ALL_ADS");
  item.SetExtraAttr(test_id_map_attr, test_map_common_attr);



  auto test_id_map = test_map_common_attr->mutable_map_unit64_bool_value();
//   auto site_id_map = site_id_map_common_attr->mutable_map_unit64_unit64_value();

//   std::string tmp_ab_key{""};
  for (const auto* ad : ad_context_->get_ad_list().Ads()) {
    if (ad == nullptr) {
      break;
    }
    test_id_map->insert({ad->get_creative_id(), ad->get_hc_gpm()});
  }
  for (const auto* ad : ad_context_->get_native_ad_list().Ads()) {
    if (ad == nullptr) {
      break;
    }
    test_id_map->insert({ad->get_creative_id(), ad->get_hc_gpm()});
  }
  for (const auto* ad : ad_context_->get_fanstop_ad_list().Ads()) {
    if (ad == nullptr) {
      break;
    }
    test_id_map->insert({ad->get_creative_id(), ad->get_hc_gpm()});
  }
}

}   // namespace ad_rank
}   // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonContextFeatureBuilder, ::ks::ad_rank::CommonContextFeatureBuilder);
