#include "teams/ad/ad_rank/search/node/search_calc_benefit/plugin/calc_cpm.h"

#include <kenv/service_meta.h>

#include <algorithm>
#include <cmath>
#include <cstdint>
#include <map>
#include <memory>
#include <queue>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>

#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/time/time.h"
#include "google/protobuf/util/json_util.h"
#include "teams/ad/ad_rank/default/params/calc_benefit_param.h"
#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/ad_base/src/common/multi_retrieval_tag.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/redis/redis_util.h"
#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_charge.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/pid_tag_enum.pb.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/processor/factor/billing_separate.h"
#include "teams/ad/ad_rank/processor/factor/mcb_strategy/mcb_strategy.h"
#include "teams/ad/ad_rank/processor/factor/ranking_data.h"
#include "teams/ad/ad_rank/data/p2p_data/account_id_2_cash_rate/account_id_2_cash_rate_p2p.h"
#include "teams/ad/ad_rank/utils/utility/calc_benefit_util.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/engine_base/billing_separate/billing_separate_weight_manager.h"
#include "teams/ad/engine_base/kconf/auto_bid_config.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/engine_base/kconf/bid_type_transfer_conf.h"

constexpr int64_t kBenifitFactorInCalcBenefit = 1000000L;
constexpr double e_value_in_calc_cpm = 2.************;
constexpr double kKnewsMinChargeValue = 1.0;
constexpr double kRewardedMinChargeValue = 1.0;
constexpr int64_t compecomCalibrationScaleInCalcCpm = 1e9L;

DECLARE_bool(is_adrank_offline_diff_test);

using kuaishou::ad::AdEnum;
using ks::engine_base::PredictType;

#define FILTER_WITH_REASON(event_id, node_name, point_name, plugin_name) \
{ ad.SetAdInValid(event_id, node_name, point_name, plugin_name);         \
  session_data_->RecordLastCondition(event_id, ad.Is(AdFlag::IsHardAd)); }       \

namespace ks {
namespace ad_rank {

double GetRetentionBidCoefByTime(AdCommon* p_ad, ContextData* session_data) {
  auto retention_evening_product_bid_coef = RankKconfUtil::retentionEveningProductBidCoef();
  auto coef_map = retention_evening_product_bid_coef;
  auto iter = coef_map->find(p_ad->get_product_name());
  double bid_coef = 1.0;
  if (iter != coef_map->end()) {
    double value = iter->second;
    base::Time time = base::Time::FromDoubleT((double)base::GetTimestamp() / 1000000);
    base::Time::Exploded time_ex;
    time.LocalExplode(&time_ex);
    auto hour = time_ex.hour;
    if (hour >= 18) {
      double ratio = (23.0 - 1.0 * hour) / 6;
      bid_coef = ratio + (1 - ratio) * value;
    }
  }
  return bid_coef;
}

double DeepOptimizationRefactor(const DeepBidParams& params, CalcBenefitParams* c_params,
                          const kuaishou::ad::AdRequest& request, ContextData* session_data_,
                          const std::shared_ptr<absl::flat_hash_map<int64_t, int64_t>>& twin_bid_strategy,
                          double auction_bid_conv, double auction_bid_ecpc, double auction_bid_deep,
                          double deep_conv_rate, double conversion_rate, AdCommon* p_ad) {
  double auction_opt = auction_bid_conv;
  auto& ad = *p_ad;

  // 策略入口 - 授信双出价订单配置 deep_cpa 比例系数，只影响授信双出价订单
  if (ad.Is(AdFlag::is_deep_conv_credit_grant)) {
    double grant_cpa_ratio = 1.0;
    auction_bid_deep *= grant_cpa_ratio;
  }
  auto strategy = ad.get_twin_bid_strategy();
  if (ad.Is(AdFlag::is_deep_bid_twin)) {
    double pdcvr = ad.get_unify_deep_cvr_info().value;
    ASSERT_NOT_ZERO_OTHERWISE_RETURN_WITH(ad.get_deep_cpa_bid(), 0.0);
    double target_rate = 1.0 * ad.get_cpa_bid() / ad.get_deep_cpa_bid();
    switch (strategy) {
      case kuaishou::ad::AdEnum::DEEP_MIN_BID: {
        if (pdcvr <= 1e-5 || pdcvr >= 1.0) {
          auction_opt = 0.0;
          break;
        }
        if (ad.get_cpa_bid() <= 0 || ad.get_deep_cpa_bid() <= 0) {
          auction_opt = 0.0;
          break;
        }

        auto coef = ad.get_deep_min_bid_coef();
        coef = std::max(coef - session_data_->get_deep_coef_lower_bound(), 0.0);

        if (ad.get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY) {
          bool use_threshold = false;
          double threshold = 0.0;

          auto& threshold_tool = RankKconfUtil::mainRetentionTool()->data().threshold_tool();
          if (threshold_tool.size() > 0) {
            //  首先 产品名粒度 找门槛值
            for (auto& tool_iter : threshold_tool) {
              auto iter = tool_iter.second;
              if (std::count(iter.product_name().begin(), iter.product_name().end(),
                             ad.get_product_name())) {
                use_threshold = true;
                threshold = iter.threshold();
              }
            }

            // 再在 账户粒度 找门槛值 账户粒度门槛值 优先级更高
            for (auto& tool_iter : threshold_tool) {
              auto iter = tool_iter.second;
              if (std::count(iter.account_id().begin(), iter.account_id().end(), ad.get_account_id())) {
                use_threshold = true;
                threshold = iter.threshold();
              }
            }

            // 对新增的唤端次留在此跳过过滤
            if (ad.get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION && use_threshold) {
              RANK_DOT_STATS(session_data_, 1, "ad.ad_rank.threshold_tool", ad.get_product_name(),
                             std::to_string(ad.get_account_id()), "");
              if (threshold > pdcvr) {
                auction_opt = 0.0;
                RANK_DOT_STATS(session_data_, 1, "ad.ad_rank.threshold_tool_filter", ad.get_product_name(),
                               std::to_string(ad.get_account_id()), "");
                break;
              }
            }
          }

          double bid_coef = 1.0;
          bid_coef = GetRetentionBidCoefByTime(p_ad, session_data_);

          double twin_bid_coef = 1.0;
          double twin_bid_coef_max = RankKconfUtil::twinBidCoefMax();
          double twin_bid_coef_min = RankKconfUtil::twinBidCoefMin();
          if (ad.get_twin_bid_coef() <= twin_bid_coef_max &&
              ad.get_twin_bid_coef() >= twin_bid_coef_min) {
            twin_bid_coef = ad.get_twin_bid_coef();
          }

          double mcda_up_calibration_coef = McdaConvRetentionUpCalibration(session_data_, ad, params);
          ad.set_twin_bid_mcda_up_calibration_rate(mcda_up_calibration_coef);
          if (mcda_up_calibration_coef > 0 && std::fabs(mcda_up_calibration_coef - 1.0) > FLT_EPSILON &&
              std::fabs(mcda_up_calibration_coef - 1.0) <= 1.0) {
            pdcvr = pdcvr * mcda_up_calibration_coef;
          }
          if (ad.get_is_account_bidding()) {
            if (ad.get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
              SPDM_enableConversionNextdayStayEcpmLauch()) {
              auto conversion_nextday_stay_ecpm_black_list =
                RankKconfUtil::conversionNextdayStayEcpmBlackList();
              auto conversion_nextday_stay_ecpm_white_list =
                RankKconfUtil::conversionNextdayStayEcpmWhiteList();
              bool is_black = conversion_nextday_stay_ecpm_black_list && (
                conversion_nextday_stay_ecpm_black_list->count(std::to_string(ad.get_account_id())) ||
                conversion_nextday_stay_ecpm_black_list->count(ad.get_product_name()));
              bool is_white = conversion_nextday_stay_ecpm_white_list && (
                conversion_nextday_stay_ecpm_white_list->count(std::to_string(ad.get_account_id())) ||
                conversion_nextday_stay_ecpm_white_list->count(ad.get_product_name()));
              if ((SPDM_enable_conversion_nextday_ecpm_deep(session_data_->get_spdm_ctx()) &&
                !is_black) || is_white) {
                  auction_opt =
                    auction_bid_conv * (std::min(1.0, coef) * pdcvr /
                    target_rate + std::max(0.0, 1.0 - coef));
                  ks::ad_base::AdPerf::IntervalLogStash(auction_opt, "ad.ad_rank",
                    "conversion_nextday_stay_ecpm_before", std::to_string(ad.get_account_id()));
                  ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_rank", "conversion_nextday_stay_ecpm");
                  if (ad.get_cpa_bid() > 0) {
                    auction_opt = auction_bid_conv / ad.get_cpa_bid() * ad.get_deep_cpa_bid() * pdcvr;
                  }
                  ks::ad_base::AdPerf::IntervalLogStash(auction_opt, "ad.ad_rank",
                    "conversion_nextday_stay_ecpm_after", std::to_string(ad.get_account_id()));
              }
            } else {
              auction_opt =
                  auction_bid_conv * (std::min(1.0, coef) * pdcvr / target_rate + std::max(0.0, 1.0 - coef));
            }
          } else {
            auction_opt = bid_coef * twin_bid_coef * auction_bid_conv *
                          (1 + coef / target_rate * (pdcvr - target_rate));
          }
          auto seven_day_stay_twin_account_white_list = RankKconfUtil::sevenDayStayTwinAccountWhiteList();
          if (ad.get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY &&
              ad.get_deep_cpa_bid() > 0 && seven_day_stay_twin_account_white_list &&
              seven_day_stay_twin_account_white_list->count(ad.get_account_id()) ||
              ad.get_enhance_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_WEEK_STAY) {
            // 流量实验 + 白名单
            // 叠加 7 留 ecpc 实验
            double avg_7_day_retention_ratio = ks::ad_rank::RankingData::GetInstance()->GetUnitAvgRetention(
                ad.get_unit_id(), ad.get_predict_score(PredictType::PredictType_conv_7_day_stay));
            if (avg_7_day_retention_ratio <= 1e-5 || avg_7_day_retention_ratio >= 1.0) {
              // 啥也不干
            } else {
              auto conv_7_day_stay_ratio =
                ad.get_predict_score(PredictType::PredictType_conv_7_day_stay) / avg_7_day_retention_ratio;
              if (conv_7_day_stay_ratio >= 0.8 && conv_7_day_stay_ratio <= 1.2) {
                auction_opt = auction_opt * conv_7_day_stay_ratio;
              } else if (conv_7_day_stay_ratio > 1.2) {
                auction_opt = auction_opt * 1.2;
              } else {
                auction_opt = auction_opt * 0.8;
              }
              ad.set_conv_7_day_stay_ecpc(conv_7_day_stay_ratio);
            }
          }
          // 搜索双出价达成率控制工具
          // 2024/12/02 @wangbin24:  支持激活-次留双出价目标
          const auto search_twin_achieve_control = RankKconfUtil::SearchTwinAchieveRateControlWhiteList();
          if (search_twin_achieve_control != nullptr &&
              session_data_->get_is_search_request() &&
              ad.get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
              ad.get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY) {
              const auto product_conf_iter = \
                      search_twin_achieve_control->data().config().find(ad.get_product_name());
              if (product_conf_iter != search_twin_achieve_control->data().config().end()) {
                const auto product_conf = product_conf_iter->second;
                float threshold = product_conf.threshold();
                float boost     = product_conf.boost();
                auto predict_arcieve_rate = pdcvr  / target_rate;
                if (predict_arcieve_rate < threshold) {  // 预估深度转化率 < 设定值 弃量
                  auction_opt *= boost;
                }
              }
          }
        } else {
          if (ad.get_is_account_bidding()) {
            auction_opt =
                auction_bid_conv * (std::min(1.0, coef) * pdcvr / target_rate + std::max(0.0, 1.0 - coef));
          } else {
            auction_opt = auction_bid_conv * (1 + coef / target_rate * (pdcvr - target_rate));
          }
        }

        if (ad.get_is_account_bidding()) {
          auction_opt = std::max(auction_opt, 0.0);  // 去掉两倍限制
        } else {
          auction_opt = std::min(std::max(auction_opt, 0.0), 2.0 * auction_bid_conv);
        }
      }
      default:
        break;
    }
  } else if (ad.Is(AdFlag::is_deep_bid_ecpc)) {
    // 深度转化 -- ecpc 的方式
    switch (strategy) {
      case kuaishou::ad::AdEnum::NO_BID_FUSE: {
        if (ad.get_conv_nextstay() <= 1e-5 || ad.get_conv_nextstay() >= 1.0) {
          // 啥也不干
        } else {
          double avg_retention_ratio = ks::ad_rank::RankingData::GetInstance()->GetUnitAvgRetention(
              ad.get_unit_id(), ad.get_conv_nextstay());
          if (avg_retention_ratio <= 1e-5 || avg_retention_ratio >= 1.0) {
            // 啥也不干
          } else {
            auction_opt = auction_bid_conv * ad.get_conv_nextstay() / avg_retention_ratio;
            auction_opt = std::min(std::max(auction_opt, 0.0), 2.0 * auction_bid_conv);
          }
        }
        break;
      }
      default: {
        if (auction_bid_ecpc > 0) {
          auction_opt = auction_bid_ecpc;  //  深度转化 ecpc 类型的出价，且各个参数没问题
        }
        break;
      }
    }
  }

  ad.set_auction_bid_conv(auction_bid_conv);
  ad.set_auction_bid_ecpc(auction_bid_ecpc);
  ad.set_auction_bid_deep(auction_bid_deep);
  if (ad.Is(AdFlag::is_deep_conv_credit_jinjian)) {
    double adjust_ratio = 1.0;
    auto iter_product = params.credit_grant_product_name_map->find(ad.get_product_name());
    if (iter_product != params.credit_grant_product_name_map->end()) {
      if (iter_product->second > 0.0) {
        adjust_ratio =
            ad.get_predict_score(PredictType::PredictType_credit_conv_grant) / iter_product->second;
        adjust_ratio =
            adjust_ratio > params.jinjian_credit_opt_lower ? adjust_ratio : params.jinjian_credit_opt_lower;
        adjust_ratio =
            adjust_ratio < params.jinjian_credit_opt_upper ? adjust_ratio : params.jinjian_credit_opt_upper;
      }
    }
    auction_opt *= adjust_ratio;
  }
  return auction_opt;
}

const char* FilterCpmBidTypeMcbPlugin::Name() { return "FilterCpmBidTypeMcb"; }

void FilterCpmBidTypeMcbPlugin::Clear() {}

bool FilterCpmBidTypeMcbPlugin::IsRun(const ContextData* session_data, const Params* params,
                                      AdRankUnifyScene pos, const AdCommon* ad) {
  return true;
}

StraRetCode FilterCpmBidTypeMcbPlugin::Process(ContextData* session_data, Params* params,
                                               AdRankUnifyScene pos, AdCommon* p_ad) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(params_, StraRetCode::SUCC);
  // 直播短视频队列合并，去除直播广告
  if (p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner))
    return StraRetCode::ABORT;
  if (session_data->get_is_search_request() &&
      p_ad->get_queue_type() == RankAdListType::FANSTOP) {
    return StraRetCode::ABORT;
  }
  if ((p_ad->get_bid_type() == kuaishou::ad::AdEnum::MCB &&
       p_ad->get_ad_source_type() != kuaishou::ad::ADX) ||
      CalcBenefitUtility::IsAccountOcpmMcbOcpxDeep(*params_, *p_ad) ||
      CalcBenefitUtility::IsBidTypeTransfer(*params_, p_ad)) {
    return StraRetCode::SUCC;
  }
  return StraRetCode::ABORT;
}

const char* FilterCpmBidTypeOcpmPlugin::Name() { return "FilterCpmBidTypeOcpm"; }

void FilterCpmBidTypeOcpmPlugin::Clear() {}

bool FilterCpmBidTypeOcpmPlugin::IsRun(const ContextData* session_data, const Params* params,
                                      AdRankUnifyScene pos, const AdCommon* ad) {
  return true;
}

StraRetCode FilterCpmBidTypeOcpmPlugin::Process(ContextData* session_data, Params* params,
                                               AdRankUnifyScene pos, AdCommon* p_ad) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(params_, StraRetCode::SUCC);
  // 直播短视频队列合并，去除直播广告
  if (p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner)) return StraRetCode::ABORT;
  if (session_data->get_is_search_request() &&
      p_ad->get_queue_type() == RankAdListType::FANSTOP) {
    return StraRetCode::ABORT;
  }
  if (p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
      p_ad->get_ad_source_type() != kuaishou::ad::ADX &&
      !CalcBenefitUtility::IsAccountOcpmMcbOcpxDeep(*params_, *p_ad) &&
      !CalcBenefitUtility::IsBidTypeTransfer(*params_, p_ad)) {
    return StraRetCode::SUCC;
  }
  return StraRetCode::ABORT;
}

const char* FilterCpmBidTypeCpmPlugin::Name() { return "FilterCpmBidTypeCpm"; }

void FilterCpmBidTypeCpmPlugin::Clear() {}

bool FilterCpmBidTypeCpmPlugin::IsRun(const ContextData* session_data, const Params* params,
                                      AdRankUnifyScene pos, const AdCommon* ad) {
  return true;
}

StraRetCode FilterCpmBidTypeCpmPlugin::Process(ContextData* session_data, Params* params,
                                               AdRankUnifyScene pos, AdCommon* p_ad) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(params_, StraRetCode::SUCC);
  // 直播短视频队列合并，去除直播广告
  if (p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner)) return StraRetCode::ABORT;
  if (session_data->get_is_search_request() &&
      p_ad->get_queue_type() == RankAdListType::FANSTOP) {
    return StraRetCode::ABORT;
  }
  if (p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPM &&
      p_ad->get_ad_source_type() != kuaishou::ad::ADX) {
    return StraRetCode::SUCC;
  }
  return StraRetCode::ABORT;
}


const char* FilterCpmBidTypeCpcPlugin::Name() { return "FilterCpmBidTypeCpc"; }

void FilterCpmBidTypeCpcPlugin::Clear() {}

bool FilterCpmBidTypeCpcPlugin::IsRun(const ContextData* session_data, const Params* params,
                                      AdRankUnifyScene pos, const AdCommon* ad) {
  return true;
}

StraRetCode FilterCpmBidTypeCpcPlugin::Process(ContextData* session_data, Params* params,
                                               AdRankUnifyScene pos, AdCommon* p_ad) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(params_, StraRetCode::SUCC);
  // 直播短视频队列合并，去除直播广告
  if (p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner)) return StraRetCode::ABORT;
  if (session_data->get_is_search_request() &&
      p_ad->get_queue_type() == RankAdListType::FANSTOP) {
    return StraRetCode::ABORT;
  }
  if (p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC &&
      p_ad->get_ad_source_type() != kuaishou::ad::ADX) {
    return StraRetCode::SUCC;
  }
  return StraRetCode::ABORT;
}

const char* FilterCpmBidTypeCpaPlugin::Name() { return "FilterCpmBidTypeCpa"; }

void FilterCpmBidTypeCpaPlugin::Clear() {}

bool FilterCpmBidTypeCpaPlugin::IsRun(const ContextData* session_data, const Params* params,
                                      AdRankUnifyScene pos, const AdCommon* ad) {
  return true;
}

StraRetCode FilterCpmBidTypeCpaPlugin::Process(ContextData* session_data_, Params* params,
                                               AdRankUnifyScene pos, AdCommon* p_ad) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(params_, StraRetCode::SUCC);
  // 直播短视频队列合并，去除直播广告
  if (p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner)) return StraRetCode::ABORT;
  if (session_data_->get_is_search_request() &&
      p_ad->get_queue_type() == RankAdListType::FANSTOP) {
    return StraRetCode::ABORT;
  }
  if (p_ad->get_ad_source_type() == kuaishou::ad::ADX) {
    return StraRetCode::ABORT;
  }

  if (p_ad->get_bid_type() != kuaishou::ad::AdEnum::CPA) {
    return StraRetCode::ABORT;
  }

  bool enable_pop_recruit_for_cpa = p_ad->Is(AdFlag::is_pop_recruit_ad);

  if (!p_ad->Is(AdFlag::CheckValidOcpcActionType) && !enable_pop_recruit_for_cpa
       && !p_ad->Is(AdFlag::is_self_service_ad)) {
    auto& ad = *p_ad;
    (*session_data_->mutable_rank_filter_ads()).insert(std::make_pair(ad.item_mapping_key_id(),
                          kuaishou::log::ad::AdTraceFilterCondition::OCPC_ACTION_TYPE_UNKNOWN));
    FILTER_WITH_REASON_V2(kuaishou::log::ad::AdTraceFilterCondition::OCPC_ACTION_TYPE_UNKNOWN,
                            kuaishou::ad::AdRankNodeType::CALCULATE_BENEFIT_TYPE,
                            kuaishou::ad::AdRankPointType::RANK_POINT_DEFAULT_TYPE,
                            kuaishou::ad::AdRankPluginType::RANK_PLUGIN_DEFAULTTYPE);
    return StraRetCode::ABORT;
  }
  return StraRetCode::SUCC;
}


const char* CalcCpmBidTypeMcbPlugin::Name() { return "CalcCpmBidTypeMcb"; }

void CalcCpmBidTypeMcbPlugin::Clear() {}

bool CalcCpmBidTypeMcbPlugin::IsRun(const ContextData* session_data, const Params* params,
                                    AdRankUnifyScene pos, const AdCommon* ad) {
  if (pos == AdRankUnifyScene::UNIVERSE || pos == AdRankUnifyScene::NATIVE) {
    return false;
  }
  return true;
}

StraRetCode CalcCpmBidTypeMcbPlugin::Process(ContextData* session_data, Params* params, AdRankUnifyScene pos,
                                             AdCommon* p_ad) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (KS_UNLIKELY(params_ == nullptr)) {
    return StraRetCode::SUCC;
  }
  params_->mcb_strategy_->MCBBenifit(p_ad, session_data, params);
  return StraRetCode::SUCC;
}

const char* CalcCpmBidTypeCpmPlugin::Name() { return "CalcCpmBidTypeCpm"; }

void CalcCpmBidTypeCpmPlugin::Clear() {}

bool CalcCpmBidTypeCpmPlugin::IsRun(const ContextData* session_data, const Params* params,
                                    AdRankUnifyScene pos, const AdCommon* ad) {
  return true;
}

StraRetCode CalcCpmBidTypeCpmPlugin::Process(ContextData* session_data, Params* params, AdRankUnifyScene pos,
                                             AdCommon* p_ad) {
  AdCommon& ad = *p_ad;
  ad.set_cpm(ad.get_bid() * kBenifitFactorInCalcBenefit);
  ad.set_origin_cpm(ad.get_cpm());
  ad.SetAuctionBid(ad.get_bid(), AuctionBidModifyTag::kBenifitFactorInCalcBenefit);
  ad.set_precise_auction_bid(ad.get_bid());
  RANK_DOT_STATS(session_data, ad.get_cpm(), "cpm_monitor",
                            kuaishou::ad::AdSourceType_Name(p_ad->get_ad_source_type()),
                            kuaishou::ad::AdEnum::BidType_Name(ad.get_bid_type()),
                            kuaishou::ad::AdEnum_InteractiveForm_Name(
                              session_data->get_pos_manager_base().GetInteractiveForm()));
  return StraRetCode::SUCC;
}

const char* CalcCpmBidTypeCpcPlugin::Name() { return "CalcCpmBidTypeCpc"; }

void CalcCpmBidTypeCpcPlugin::Clear() {}

bool CalcCpmBidTypeCpcPlugin::IsRun(const ContextData* session_data, const Params* params,
                                    AdRankUnifyScene pos, const AdCommon* ad) {
  return true;
}

StraRetCode CalcCpmBidTypeCpcPlugin::Process(ContextData* session_data, Params* params, AdRankUnifyScene pos,
                                             AdCommon* p_ad) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(params_, StraRetCode::SUCC);
  AdCommon& ad = *p_ad;
  double origin_server_client_show_rate =
    ad.get_predict_score(PredictType::PredictType_server_client_show_rate);
  double server_client_show_rate_ocpm = 1.0;
  server_client_show_rate_ocpm = ad.get_server_client_show_rate_ocpm();
  std::string app_id = session_data->get_pos_manager_base().GetRequestAppId();
  if (session_data->get_is_thanos_mix_request() ||
      params_->enable_explore_inner_sctr_migrate && session_data->get_is_explore_feed_inner()) {
    ad.set_server_show_rate(server_client_show_rate_ocpm);
    if (app_id == "kuaishou") {
      server_client_show_rate_ocpm = session_data->get_unify_server_show_rate();
    } else if (app_id == "kuaishou_nebula") {
      server_client_show_rate_ocpm = session_data->get_nebula_unify_server_show_rate();
    }
  }
  double auction_bid = p_ad->get_bid();

  if (session_data->get_is_search_request() &&
      SPDM_enable_search_cpc_boost_align(session_data->get_spdm_ctx())) {
    if (session_data->get_is_search_inner_stream()) {
      // boost 逻辑对齐 - 内流：规整前 auction_bid & unify_ctr 都带 boost，规整后只有 auction_bid 带
      auction_bid *= p_ad->get_search_bid_boost();
      if (!SPDM_enable_search_regulate_boost_scope(session_data->get_spdm_ctx())) {
        ad.AdjustUnifyCtrValue(ad.get_unify_ctr_info().value * ad.get_search_bid_boost());
      }
    } else {
      // boost 逻辑对齐 - 外流：规整前 auction_bid & unify_ctr 都不带 boost，规整后 auction_bid 带
      if (SPDM_enable_search_regulate_boost_scope(session_data->get_spdm_ctx())) {
        auction_bid *= p_ad->get_search_bid_boost();
      }
    }
  }

  if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_PHOTO) {
    auction_bid = p_ad->get_bid() * params_->biz_click_2_bid_ratio_ * p_ad->get_cvr();
  }

  double cpm = static_cast<int64>(auction_bid * origin_server_client_show_rate * p_ad->get_ctr() *
                                  kBenefitFactorInCalcCpm);
  double origin_cpm = static_cast<int64>(auction_bid * p_ad->get_ctr() * kBenefitFactorInCalcCpm);

  if (session_data->get_is_thanos_request() &&
      p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC &&
      p_ad->get_ad_source_type() != kuaishou::ad::ADX) {
    cpm = static_cast<int64_t>(p_ad->get_bid() * ad.get_unify_ctr_info().value
          * ad.get_unify_cvr_info().value * kBenefitFactorInCalcCpm * server_client_show_rate_ocpm);
    origin_cpm = static_cast<int64_t>(p_ad->get_bid() * ad.get_unify_ctr_info().value
          * ad.get_unify_cvr_info().value * kBenefitFactorInCalcCpm);
    auction_bid = p_ad->get_bid();
    VLOG_EVERY_N(1, 1000) << "bid_type cpc ecpm, creative:" << p_ad->get_creative_id()
                          << ", account_id:" << p_ad->get_account_id()
                          << ", campaign_type:" << p_ad->get_campaign_type()
                          << ", cpm:" << cpm
                          << ", auction_bid:" << auction_bid
                          << ", bid_type:" << p_ad->get_bid_type()
                          << ", ctr:" << ad.get_unify_ctr_info().value
                          << ", cvr:" << ad.get_unify_cvr_info().value
                          << ", ctr_s:" << ad.get_unify_ctr_info().s_type
                          << ", ctr_e:" << ad.get_unify_ctr_info().e_type
                          << ", cvr_s:" << ad.get_unify_cvr_info().s_type
                          << ", cvr_e:" << ad.get_unify_cvr_info().e_type
                          << ", bid:" << p_ad->get_bid()
                          << ", server_client_show_rate_ocpm:" << server_client_show_rate_ocpm;
  }
  // 搜索双列
  if (session_data->get_is_search_request() &&
      p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC &&
      p_ad->Is(AdFlag::is_search_bidword) &&
      p_ad->get_quick_search() == 0) {
    auction_bid = p_ad->get_bid();
    if (SPDM_enable_search_cpc_boost_align(session_data->get_spdm_ctx())) {
      if (session_data->get_is_search_inner_stream()) {
        // boost 逻辑对齐 - 内流：规整前 auction_bid & unify_ctr 都带 boost，规整后只有 auction_bid 带
        auction_bid *= p_ad->get_search_bid_boost();
        // unify_ctr 前面乘过了
      } else {
        // boost 逻辑对齐 - 外流：规整前 auction_bid & unify_ctr 都不带 boost，规整后 auction_bid 带
        if (SPDM_enable_search_regulate_boost_scope(session_data->get_spdm_ctx())) {
          auction_bid *= p_ad->get_search_bid_boost();
        }
      }
    }
    cpm = static_cast<int64_t>(auction_bid
        * ad.get_unify_ctr_info().value * ad.get_unify_cvr_info().value
        * kBenefitFactorInCalcCpm);
    origin_cpm = cpm;
    if (0.0 < params_->search_cpc_price_ratio && params_->search_cpc_price_ratio < 1.0) {
      auction_bid *= params_->search_cpc_price_ratio;
    }
    LOG_EVERY_N(INFO, 500000) << "search_cpc_ad, creative:" << p_ad->get_creative_id()
      << ", campaign_type:" << p_ad->get_campaign_type()
      << ", cpm:" << cpm
      << ", auction_bid:" << auction_bid
      << ", bid_type:" << p_ad->get_bid_type();
  }

  // rta cpc 动态出价广告 单列
  if (p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC &&
      p_ad->get_real_time_type() == kuaishou::ad::AdEnum::RTA &&
      session_data->get_is_thanos_request()) {
    switch (p_ad->get_rta_sta_tag()) {
      // 一次出价 客户出价
      case kuaishou::ad::AdEnum::RATIO_AND_CPA_BID:
        if (p_ad->get_rta_ratio() > 0) {
          auction_bid *= p_ad->get_rta_ratio();
          cpm *= p_ad->get_rta_ratio();
          origin_cpm = cpm;
        }
        break;
      // 二次出价 客户出价
      case kuaishou::ad::AdEnum::RATIO_AND_CPA_BID_SECOND:
      case kuaishou::ad::AdEnum::RATIO_AND_CPA_BID_ONLINE:
        if (p_ad->get_is_second_rta_predict() && p_ad->get_rta_ratio_second_bid() > 0) {
          auction_bid *= p_ad->get_rta_ratio_second_bid();
          cpm *= p_ad->get_rta_ratio_second_bid();
          origin_cpm = cpm;
          p_ad->set_is_rta_bid_second(1);
        } else if (p_ad->get_rta_ratio() > 0) {
          // 二次请求失效 一次请求系数生效
          auction_bid *= p_ad->get_rta_ratio();
          cpm *= p_ad->get_rta_ratio();
          origin_cpm = cpm;
        }
        break;
      // 直接出价
      // 一次出价和二次出价都需要修改
      // 一次出价的 rta_bid 继承自第一次请求 单位厘
      // 二次请求的 rta_bid 继承自第二次请求 单位厘
      case kuaishou::ad::AdEnum::BID_AND_CPA_BID_DIR:
      case kuaishou::ad::AdEnum::BID_AND_CPA_BID_DIR_SECOND:
        if (p_ad->get_rta_bid() > 0) {
          auction_bid = p_ad->get_rta_bid();
          cpm = static_cast<int64_t>(p_ad->get_rta_bid() *
                ad.get_unify_ctr_info().value * ad.get_unify_cvr_info().value * kBenefitFactorInCalcCpm *
                server_client_show_rate_ocpm);
          origin_cpm = cpm;
          p_ad->set_is_rta_bid_second(1);
        }
        break;
      default:
        break;
    }
    VLOG_EVERY_N(1, 1000) << "CalcCpmBidTypeCpcPlugin rta_ad, account_id:" << p_ad->get_account_id()
                          << ",product_name:" << p_ad->get_product_name()
                          << ",rta_ratio:" << p_ad->get_rta_ratio()
                          << ",rta_sta_tag:" << p_ad->get_rta_sta_tag()
                          << ",cpm:" << cpm
                          << ",auction_bid:" << auction_bid
                          << ",rta_bid_second:" << p_ad->get_rta_ratio_second_bid()
                          << ",rta_bid:" << p_ad->get_rta_bid();
  }

  ad.set_cpm(cpm);
  ad.set_origin_cpm(origin_cpm);
  ad.SetAuctionBid(
      static_cast<int64>(auction_bid), AuctionBidModifyTag::CalcCpmBidTypeCpc);
  ad.set_precise_auction_bid(auction_bid);
  return StraRetCode::SUCC;
}

const char* CalcCpmBidTypeCpaPlugin::Name() { return "CalcCpmBidTypeCpa"; }

void CalcCpmBidTypeCpaPlugin::Clear() {}

bool CalcCpmBidTypeCpaPlugin::IsRun(const ContextData* session_data, const Params* params,
                                    AdRankUnifyScene pos, const AdCommon* ad) {
  return true;
}

StraRetCode CalcCpmBidTypeCpaPlugin::Process(ContextData* session_data, Params* params, AdRankUnifyScene pos,
                                             AdCommon* p_ad) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (KS_UNLIKELY(params_ == nullptr)) {
    return StraRetCode::SUCC;
  }
  AdCommon& ad = *p_ad;
  double conversion_rate = 0.0;
  double auction_bid = ad.get_cpa_bid();
  ad.SetAuctionBid(static_cast<int64>(auction_bid), AuctionBidModifyTag::CalcCpmBidTypeCpa);
  ad.set_precise_auction_bid(auction_bid);
  // 速推 CPA 只改排序，不改计费
  if (p_ad->Is(AdFlag::is_furious_cpa)) {
    auction_bid = ad.get_auto_cpa_bid();
  }
  double server_client_show_rate_ocpm_reconstruct = ad.get_server_client_show_rate_ocpm();

  std::string app_id = session_data->get_pos_manager_base().GetRequestAppId();
  if (session_data->get_is_thanos_mix_request() ||
      params_->enable_explore_inner_sctr_migrate && session_data->get_is_explore_feed_inner()) {
    ad.set_server_show_rate(server_client_show_rate_ocpm_reconstruct);
    if (app_id == "kuaishou") {
      server_client_show_rate_ocpm_reconstruct = session_data->get_unify_server_show_rate();
    } else if (app_id == "kuaishou_nebula") {
      server_client_show_rate_ocpm_reconstruct = session_data->get_nebula_unify_server_show_rate();
    }
  }

  if (session_data->get_is_thanos_request()) {
    conversion_rate = ad.get_unify_cvr_info().value;
    if (ad.get_cpa_ratio() > 0) {
      ad.set_cpm(auction_bid * conversion_rate * ad.get_unify_ctr_info().value *
                 server_client_show_rate_ocpm_reconstruct * kBenefitFactorInCalcCpm * ad.get_cpa_ratio());
    } else {
      ad.set_cpm(auction_bid * conversion_rate * ad.get_unify_ctr_info().value *
                 server_client_show_rate_ocpm_reconstruct * kBenefitFactorInCalcCpm);
    }
    ad.set_origin_cpm(auction_bid * conversion_rate * ad.get_unify_ctr_info().value * kBenefitFactorInCalcCpm);  // NOLINT
  } else {
    conversion_rate = ad.get_unify_cvr_info().value;
    ad.set_cpm(auction_bid * conversion_rate * ad.get_ctr() *
               ad.get_predict_score(engine_base::PredictType::PredictType_server_client_show_rate) *
               server_client_show_rate_ocpm_reconstruct * kBenefitFactorInCalcCpm);
    ad.set_origin_cpm(auction_bid * conversion_rate * ad.get_ctr() *
                      ad.get_predict_score(engine_base::PredictType::PredictType_server_client_show_rate) *
                      kBenefitFactorInCalcCpm);
  }
  ad.set_final_server_client_show_rate(server_client_show_rate_ocpm_reconstruct);
  return StraRetCode::SUCC;
}

const char* CalcCpmBidTypeOcpmDspPlugin::Name() { return "CalcCpmBidTypeOcpmDsp"; }

void CalcCpmBidTypeOcpmDspPlugin::Init(ContextData* session_data) {
}

void CalcCpmBidTypeOcpmDspPlugin::Clear() {
}

bool CalcCpmBidTypeOcpmDspPlugin::IsRun(const ContextData* session_data, const Params* params,
                                        AdRankUnifyScene pos, const AdCommon* ad) {
  return true;
}

StraRetCode CalcCpmBidTypeOcpmDspPlugin::Process(ContextData* session_data, Params* params,
                                                 AdRankUnifyScene pos, AdCommon* p_ad) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (KS_UNLIKELY(params_ == nullptr)) {
    return StraRetCode::SUCC;
  }
  AdCommon& ad = *p_ad;
  double conversion_rate = 0.0;
  double ctr = 0.0;
  double old_conversion_rate = 0.0;
  double old_ctr = 0.0;
  double auction_bid = 0.0;
  // cpm 拉升系数
  double auction_bid_pure = 0.0;
  // todo 上面调权迁移统一调权逻辑后，也进行迁移
  bool is_matrix_app_req = engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(
      session_data->get_pos_manager_base().GetRequestAppId());
  const auto &key_action_post_target_roi_product_map = RankKconfUtil::keyActionPostTargetRoi();

  old_conversion_rate = ad.get_unify_cvr_info().value;
  old_ctr = ad.get_unify_ctr_info().value;
  ctr = ad.get_unify_ctr_info().value;
  // 万合改曝光计费，去掉 sctr
  if (params_->enable_wanhe_charge_action_type_subpage_id &&
      session_data->get_pos_manager_base().IsWanhe()) {
    if (!ad.get_is_wanhe_charge_action_type() && ad.get_server_show_ctr() > 0) {
      ctr = ctr / ad.get_server_show_ctr();
      old_ctr = old_ctr / ad.get_server_show_ctr();
      ad.set_is_wanhe_charge_action_type(true);
      RANK_DOT_STATS(session_data, ctr * 1000000, "wanhe_ad_rank_charge_type", "ocpm_ctr");
      RANK_DOT_STATS(session_data, ad.get_server_show_ctr() * 1000000,
                     "wanhe_ad_rank_charge_type", "ocpm_server_ctr");
    }
  }

  conversion_rate = ad.get_unify_cvr_info().value;
  // 引流迁移短视频队列激励视频折扣系数
  auction_bid = ad.get_auto_cpa_bid() * ctr * conversion_rate;

  if (!session_data->get_disable_ecpm_strategy__6_billing_separate()) {
    // 排序计费分离，修改 auction_bid @tanweihan
    params_->billing_separate_strategy_->Process(session_data, p_ad, params, &auction_bid);
  }

  // 曝光系数移除
  utility::MulHardSctr(session_data, p_ad, params_->enable_explore_inner_sctr_migrate, &auction_bid,
                       "old.photo.ocpm", params_->fix_hard_unify_sctr_click);

  // 商品 Tab 或 商品 Kbox
  if (SPDM_enable_search_goods_and_kbox_remove_ctr(session_data->get_spdm_ctx()) &&
    (session_data->get_is_search_good_tab() || p_ad->get_is_search_kbox_item())) {
    auction_bid = ad.get_auto_cpa_bid() * ad.get_unify_cvr_info().value *
      params_->search_mingtou_goods_tab_photo_cpm_ratio;
  } else if (params_->search_goods_tab_remove_ctr &&
    session_data->get_is_search_good_tab()) {
    // 商品 tab 只有双列
    // 搜索广告购物 tab live 复用和 photo 相同开关, 代码走这里
    auction_bid = ad.get_auto_cpa_bid() * ad.get_unify_cvr_info().value  *
      params_->search_goods_tab_photo_cpm_ratio;
  }

  // ctr = ad.get_unify_ctr_info().value;
  if (ad.get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
      ad.get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY) {
    // 激活付费双出价 使用 click 作为 ctr 计算深度 ecpm 切分点
    ctr = ad.get_server_show_cvr();
  }

  if (session_data->get_pos_manager_base().IsGuessYouLike() &&
      params_->enable_guess_you_like_impression_charge) {
    auction_bid = ad.get_auto_cpa_bid() * ad.get_unify_cvr_info().value;
  }

  // 小程序广告更改计费方式
  if (session_data->get_pos_manager_base().IsMicroAppFeedRequest()) {
    auction_bid = ad.get_auto_cpa_bid() * ad.get_unify_cvr_info().value;
  }

  // 开屏内循环 bid boost 实验
  if (session_data->IsSplashTraffic()) {
    double splash_boost_coef = ad.get_splash_boost_coef();
    if (splash_boost_coef > 0 && splash_boost_coef != 1.0 &&
      !SPDM_enable_splash_boost_coef_immg(session_data->get_spdm_ctx())) {
      auction_bid *= splash_boost_coef;
    }
    if (p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ESP_EYE_MAX &&
        params_->splash_eyemax_discount_ratio_value > 0 &&
        params_->splash_eyemax_discount_ratio_value != 1.0 &&
        !SPDM_splash_eyemax_discount_immg(session_data->get_spdm_ctx())) {
      auction_bid *= params_->splash_eyemax_discount_ratio_value;
      RANK_DOT_COUNT(session_data, 1, "splash_discount_ratio", "dsp_ocpm_no_live");
    }
    double splash_innner_boost_ratio = 0.0;
    if (ad.get_spu_id_weigth() >1E-6) {
      splash_innner_boost_ratio =
        std::min((ad.get_spu_id_weigth() * session_data->get_splash_innner_boost_ratio() + 1.0), 2.0);
      auction_bid *= splash_innner_boost_ratio;
    }
    if (session_data->get_monitor_sample() && !ad.Is(AdFlag::is_outer_loop_ad)) {
        LOG_EVERY_N(INFO, 100000) << "cpm_debug1"
                         << ",llsid=" << session_data->get_llsid()
                         << ",IsRealtime="  << session_data->IsRealtimeSplashTraffic()
                         << ",user_id=" << session_data->get_user_id()
                         << ",account_id=" << ad.get_account_id()
                         << ",campaign_type=" << ad.get_campaign_type()
                         << ",promotion_type=" << ad.get_promotion_type()
                         << ",item_type=" << p_ad->get_item_type()
                         << ",live_creative_type=" << ad.get_live_creative_type()
                         << ",unit_id=" << ad.get_unit_id()
                         << ",speed=" << ad.get_speed()
                         << ",creative_id=" << ad.get_creative_id()
                         << ",ocpx=" << ad.get_ocpx_action_type()
                         << ",spu_id_weigth=" << ad.get_spu_id_weigth()
                         << ",splash_innner_boost_ratio=" << session_data->get_splash_innner_boost_ratio()
                         << ",final_boost_ratio=" << splash_innner_boost_ratio
                         << ",splash_boost_coef=" << p_ad->get_splash_boost_coef();
    }
  }

  // 搜索广告 auction_bid 兜底逻辑，以概率 manual_set_auction_bid_prob
  // 将 auction_bid 为 0 的设置为 manual_set_auction_bid_val
  if (session_data->get_pos_manager_base().IsSearchRequest()) {
    if (auction_bid < 1e-10) {
      RANK_DOT_COUNT(session_data, 1, "ad_rank.auction_bid_real_zero",
        session_data->get_search_rank_exp_name());
      if (ad_base::AdRandom::GetDouble() < params_->manual_set_auction_bid_prob) {
        auction_bid = params_->manual_set_auction_bid_val;
        RANK_DOT_COUNT(session_data, 1, "ad_rank.auction_bid_manual_set",
        session_data->get_search_rank_exp_name());
      }
    }
  }
  double auction_bid_init = auction_bid;
  if (!session_data->get_disable_ecpm_strategy__11_ecpc_ratio()) {
    ad.set_ecpc_ratio(1);
  }
  // 表单获客双出价冷启动策略
  if (ad.get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED &&
        ad.get_deep_conversion_type() ==
          kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION &&
            params_->enable_acquisition_twinbid_cold_start) {
    bool is_pass_cold_start = false;
    if (SPDM_enableFixIndexDefaultValue()) {
      // 当前策略取值，0 冷启动；1 冷启动成功
      // 其中 0 和默认值相同 无法区分 策略正排失败 or 0 因此，将默认值改成 2
      // 0 冷启动：单出价
      // 1 冷启动成功：双出价
      // 2 请求策略正排失败： 双出价
      is_pass_cold_start =
        p_ad->Attr(ItemIdx::fd_CAMPAIGN_is_pass_cold_start).GetIntValue(p_ad->AttrIndex()).value_or(2);
    } else {
      is_pass_cold_start =
        p_ad->Attr(ItemIdx::fd_CAMPAIGN_is_pass_cold_start).GetIntValue(p_ad->AttrIndex()).value_or(0);
    }
    if (!is_pass_cold_start) {
      ad.set_deep_bid_type(kuaishou::ad::AdEnum::UNKNOWN_OCPC_DEEP_BID_TYPE);
      ad.set_deep_conversion_type(kuaishou::ad::AdCallbackLog_EventType_EVENT_UNKNOWN);
      RANK_DOT_COUNT(session_data, 1, "ad_rank.lps_acquisition_coldstart_cnt",
          ad.get_product_name());
      LOG_EVERY_N(INFO, 100000) << "lpsAcquisition Twinbid ColdStart"
                          << ", account_id:" << p_ad->get_account_id()
                          << ", campaign_id:" << p_ad->get_campaign_id()
                          << ", is_pass_cold_start:" << is_pass_cold_start
                          << ", ad.get_deep_bid_type():" << ad.get_deep_bid_type()
                          << ", ad.get_deep_conversion_type():" << ad.get_deep_conversion_type();
    }
  }

  // 注意 ！！！ 策略迁移，最好完全按照当前顺序迁移，否则实验估计会不平, ecpc 直接操作 auction_bid
  if (ad.Is(AdFlag::is_deep_unified)) {
  } else if (ad.Is(AdFlag::need_deep_optimization)) {
    double deep_rate_from_click = 0.0;
    if (session_data->get_is_thanos_request()) {
      GetDeepConvRateFromClick2(session_data, ad, session_data->get_rank_request()->ad_request(),
                                params_->deep_bid_params, conversion_rate, 0.0, &deep_rate_from_click);
    } else {
      GetDeepConvRateFromClick1(session_data, ad, session_data->get_rank_request()->ad_request(),
                                params_->deep_bid_params, conversion_rate, 0.0, &deep_rate_from_click);
    }
    double auction_bid_deep = ad.get_deep_cpa_bid() * deep_rate_from_click * ctr;
    if (ad.get_auto_deep_cpa_bid() > 0) {
      auction_bid_deep = ad.get_auto_deep_cpa_bid() * deep_rate_from_click * ctr;
    }
    double auction_bid_ecpc = auction_bid;
    auto twin_bid_strategy = RankKconfUtil::twinBidStrategy();
    auction_bid =
        DeepOptimizationRefactor(params_->deep_bid_params, params_,
                                 session_data->get_rank_request()->ad_request(),
                                 session_data, twin_bid_strategy, auction_bid, auction_bid_ecpc,
                                 auction_bid_deep, deep_rate_from_click, conversion_rate, p_ad);
  }
  auction_bid_pure = auction_bid;

  ad.BoostUnifyEcpmRatio(ad.Attr(ItemIdx::ecpc_max_min_ratio).GetDoubleValue(ad.AttrIndex()).value_or(1.0)); // NOLINT
  auction_bid *= ad.get_unify_ecpm_ratio();

  // 有效获客单出价 bid * ctr * cvr * deep_cvr (非 ecpc)
  // 支持三条链路，表单/IM/企微
  auto deep_cvr = ad.get_unify_deep_cvr_info().value;
  if (SPDM_enable_fix_lps_acquisition_prediction_default(session_data->get_spdm_ctx())) {
    if (ad.get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION) {
      auction_bid = auction_bid * deep_cvr;
    }
  } else if (ad.get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION && deep_cvr >0) {
      auction_bid = auction_bid * deep_cvr;
  }

  // 关键行为出价公式 bid * ctr * cvr * deep_cvr (非 ecpc)
  if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION) {
    // 屏蔽快看点
    if (session_data->get_is_knews()) {
      auction_bid = 0;
    } else {
      double p_conv_nextstay = ad.get_conv_nextstay();
      double p_ltv0 = ad.get_predict_score(PredictType::PredictType_key_action_ltv0);
      double p_key_action_rate =
        ad.get_predict_score(PredictType::PredictType_conv_key_inapp_action_rate);
      double post_target_roi = 0;
      if (key_action_post_target_roi_product_map != nullptr &&
          key_action_post_target_roi_product_map->find(ad.get_product_name()) !=
            key_action_post_target_roi_product_map->end()) {
        post_target_roi = key_action_post_target_roi_product_map->find(ad.get_product_name())->second;
      }
      if (session_data->get_is_thanos_request() && params_->enable_deep_middle_model_exp &&
      params_->enable_deep_middle_model_kac_exp) {
        p_key_action_rate = ad.get_unify_deep_cvr_info().value;
      }
      auction_bid *= p_key_action_rate;
      double avg_retention = ks::ad_rank::RankingData::GetInstance()->GetKeyActionAvgRetention(
        ad.get_account_id(), p_conv_nextstay);
      double avg_key_action_rate = ks::ad_rank::RankingData::GetInstance()->GetAccountKeyActionAvgValue(
        ad.get_account_id(), p_key_action_rate);
      double avg_ltv0 = ks::ad_rank::RankingData::GetInstance()->GetAccountKeyLtv0AvgValue(
        ad.get_account_id(), p_ltv0);
      auto iter_reten = params_->deep_bid_params.key_action_retention_account_map->find(ad.get_account_id());
      auto iter_ltv0 = params_->deep_bid_params.key_action_account_map->find(ad.get_account_id());
      auto iter_key = params_->deep_bid_params.key_action_account_map2->find(ad.get_account_id());
      auto iter_key_extreme =
          params_->deep_bid_params.key_action_cold_start_extreme_account_map->find(ad.get_account_id());
      bool dsp_key_switch = false;
      bool dsp_reten_switch = false;
      bool dsp_ltv0_switch = false;
      if (params_->enable_key_action2_dsp) {
        for (const auto& action : ad.get_key_action_switch()) {
          if (action == 143) {
            dsp_key_switch = true;
          } else if (action == 7) {
            dsp_reten_switch = true;
          } else if (action == 228) {
            dsp_ltv0_switch = true;
          }
        }
      }
      // 关键行为 2.0 - 优化次留
      if (iter_reten != params_->deep_bid_params.key_action_retention_account_map->end() ||
          dsp_reten_switch) {
        double expect_retention = avg_retention;
        if (iter_reten != params_->deep_bid_params.key_action_retention_account_map->end()) {
          if (iter_reten->second > DBL_EPSILON && iter_reten->second < 0.99) {
            expect_retention = iter_reten->second;
          }
        }
        if (expect_retention > 0.0) {
          double ecpc_ratio = p_conv_nextstay / expect_retention;
          ecpc_ratio = std::min(std::max(ecpc_ratio, 0.0), 2.0);
          ecpc_ratio = 1 + (ecpc_ratio - 1) * params_->key_action_retention_gamma;
          auction_bid *= ecpc_ratio;
        }
      }
      // 关键行为 2.0 - 优化 ltv0
      if ((iter_ltv0 != params_->deep_bid_params.key_action_account_map->end() ||
           dsp_ltv0_switch) && avg_ltv0 > 0.0) {
        double ecpc_ratio = p_ltv0 / avg_ltv0;
        ecpc_ratio = std::min(std::max(ecpc_ratio, -10.0), 10.0);
        ecpc_ratio = std::max(1 + (ecpc_ratio - 1 - 0.4)
                                * params_->key_action_ltv0_gamma, 0.0);
        auction_bid *= ecpc_ratio;
      }
      // 关键行为 2.0 - 优化关键行为率
      bool optimize_key_action_rate = false;
      double expect_key_action_ratio = avg_key_action_rate;
      double gamma = params_->key_action_new_gamma;
      if (iter_key != params_->deep_bid_params.key_action_account_map2->end() || dsp_key_switch) {
        if (iter_key != params_->deep_bid_params.key_action_account_map2->end()) {
          if (iter_key->second > DBL_EPSILON && iter_key->second < 0.99) {
                    expect_key_action_ratio = iter_key->second;
          }
        }
        optimize_key_action_rate = true;
      }
      if (iter_key_extreme !=
            params_->deep_bid_params.key_action_cold_start_extreme_account_map->end()) {
        if (iter_key_extreme->second > DBL_EPSILON && iter_key_extreme->second < 0.99) {
          expect_key_action_ratio = iter_key_extreme->second;
        }
        optimize_key_action_rate = true;
        gamma = params_->key_action_cold_start_extreme_gamma;
      }
      if (optimize_key_action_rate && expect_key_action_ratio > 0.0) {
        double ecpc_ratio = p_key_action_rate / expect_key_action_ratio;
        ecpc_ratio = std::min(std::max(ecpc_ratio, 0.0), 10.0);
        ecpc_ratio = 1 + (ecpc_ratio - 1) * gamma;
        auction_bid *= ecpc_ratio;
      }
    }
  }

  // 阿里 RTA 外投白名单账户 auction_bid 处理
  if (ad.get_ali_outer_bid_type() == AliOuterBidType::ALI_UNION &&
      ad.get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT) {
    auction_bid = ad.get_ali_deep_cvr() * ad.get_unify_ctr_info().value;
    RANK_DOT_STATS(session_data, static_cast<int64_t>(ad.get_ali_deep_cvr()),
                   "ali_outer_deep_cvr_with_bid");
  }
  ad.set_is_server_show_ocpm(true);
  double server_client_show_rate = ad.get_server_client_show_rate_ocpm();
  double server_client_show_rate_account_ocpm_reconstruct =
            ad.get_server_client_show_rate_account_ocpm();
  double server_client_show_rate_account_ocpm_white_reconstruct =
            ad.get_server_client_show_rate_account_ocpm_white();

  if (!session_data->get_enable_server_show_rate_unify() &&
      CalcBenefitUtility::IsAccountBiddingSet(*params_, ad)) {
    server_client_show_rate = server_client_show_rate_account_ocpm_white_reconstruct;
  } else if (!session_data->get_enable_server_show_rate_unify() && ad.get_is_account_bidding()) {
      server_client_show_rate = server_client_show_rate_account_ocpm_reconstruct;
  }

  std::string app_id = session_data->get_pos_manager_base().GetRequestAppId();
  if (session_data->get_is_thanos_mix_request() ||
      params_->enable_explore_inner_sctr_migrate && session_data->get_is_explore_feed_inner()) {
    ad.set_server_show_rate(server_client_show_rate);
    if (app_id == "kuaishou") {
      server_client_show_rate = session_data->get_unify_server_show_rate();
    } else if (app_id == "kuaishou_nebula") {
      server_client_show_rate = session_data->get_nebula_unify_server_show_rate();
    }
  }
  // 曝光系数移除
  if (session_data->get_is_explore_feed_inner()) {
    server_client_show_rate *= utility::GetHardSctr1Factor(
        session_data, p_ad, params_->enable_explore_inner_sctr_migrate,
        params_->explore_inner_sctr_migrate_lower, params_->explore_inner_sctr_migrate_upper,
        "old.photo.ocpm");
  }

  // 搜索广告目前 conversion_rate * ctr 异常低，且模型方面还未优化，先强制 boost bid
  if (session_data->get_pos_manager_base().IsSearchRequest()) {
    if (SPDM_enable_search_regulate_boost_scope(session_data->get_spdm_ctx())) {
      auction_bid *= ad.get_search_bid_boost();
    } else if (!session_data->get_enable_search_pos_sctr2()) {
      auction_bid *= ad.get_search_bid_boost();
      ad.AdjustUnifyCtrValue(ad.get_unify_ctr_info().value * ad.get_search_bid_boost());
    }
    std::string ocpx_type_str = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    RANK_DOT_STATS(session_data, ad.get_search_bid_boost() * 1000000, "ad_rank.ad_rank_bid_boost",
                   session_data->get_search_rank_exp_name(), ocpx_type_str,
                   session_data->get_search_interactive_form());
  }
  if (session_data->get_pos_manager_base().IsGuessYouLike() &&
      params_->enable_gyl_server_show_rate) {
    server_client_show_rate = params_->gyl_server_show_rate;
  }
  // 磁力万合改计费之后, server_client_show_rate 置 1
  if (ad.get_is_wanhe_charge_action_type()) {
    server_client_show_rate =  1.0;
  }
  if (!session_data->get_disable_ecpm_strategy__9_same_product_penalty()) {
    int64 bid_penalty = GetSameMerchantProductPenalty(p_ad, params_, session_data, auction_bid);
    auction_bid -= bid_penalty;
  }
  // 移动端走硬广
  if (params_->enable_mobile_soft_to_hard_auction_bound &&
      p_ad->get_is_mobile_soft_to_hard() == true) {
      auction_bid = std::min(auction_bid, params_->mobile_soft2hard_max_auciton);
  }

  if (session_data->get_pos_manager_base().IsGuessYouLike() &&
      params_->enable_gyl_cpm_remove_ctr) {
      ad.set_cpm(static_cast<int64>(ad.get_auto_cpa_bid() * ad.get_unify_cvr_info().value *
                                    server_client_show_rate * kBenifitFactor));
  } else {
    ad.set_cpm(static_cast<int64>(auction_bid * server_client_show_rate * kBenifitFactor));
  }

  // 快看点低于 1 厘置为 1 厘
  if (is_matrix_app_req) {
    double knews_cpm = auction_bid * server_client_show_rate * kBenifitFactor;
    if (knews_cpm < kKnewsMinChargeValue) {
      ad.set_cpm(static_cast<int64>(kKnewsMinChargeValue));
    }
  }

  // 非电商直播推广 auction_bid
  const auto& non_merchant_params = params_->non_merchant_live_promote_params;
  if (non_merchant_params.enable_non_merchant_model) {
    if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
      if (ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
          ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION) {
        if (non_merchant_params.enable_fix_auction_bid_bug) {
          auction_bid *= ad.get_unify_deep_cvr_info().value;
        }
      }
      if (ad.get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
        auction_bid = std::min(auction_bid, non_merchant_params.direct_live_auction_bid_max_value);
      }
      ad.set_cpm(static_cast<int64>(auction_bid * server_client_show_rate * kBenifitFactor));
      RANK_DOT_STATS(session_data, auction_bid * 100000, "non_merchant_live_promotion_auction_bid",
                     kuaishou::ad::AdEnum_LiveCreativeType_Name(ad.get_live_creative_type()));
    }
  }

  double auction_bid_bak = auction_bid;
  auction_bid = std::round(auction_bid);
  ad.SetAuctionBid(
      static_cast<int64>(auction_bid), AuctionBidModifyTag::CalcCpmBidTypeOcpmDsp);
  if (params_->enable_reset_precise_auction_bid && session_data->get_is_thanos_request()) {
    ad.set_precise_auction_bid(auction_bid_bak);
  } else {
    ad.set_precise_auction_bid(auction_bid);
  }

  ad.set_final_server_client_show_rate(server_client_show_rate);

  if (session_data->get_pos_manager_base().IsInspireMerchant()) {
    ad.set_precise_auction_bid(auction_bid_bak);
  }

  if (session_data->get_pos_manager_base().IsSearchRequest()) {
    LOG_EVERY_N(INFO, 500000)
        << "price check"
        << "|ctr=" << ctr << "|ad.get_ctr()=" << ad.get_ctr() << "|str="
        << ad.get_predict_score(PredictType::PredictType_server_client_show_rate)
        << "|unify_ctr=" << ad.get_unify_ctr_info().value << "|unify_cvr=" << ad.get_unify_cvr_info().value
        << "|auction_bid=" << auction_bid << "|cpm=" << ad.get_cpm()
        << "|server_client_show_rate=" << server_client_show_rate
        << "|auto_cpa_bid=" << ad.get_auto_cpa_bid()
        << "|ocpc_action_type=" << ad.get_ocpx_action_type() << "|auction_bid_init=" << auction_bid_init
        << "|exp_name=" << session_data->get_search_rank_exp_name();
    if (!ks::infra::kenv::IsStressTestFlow()) {
      // 对 auction_bid_init 为 0 的情况进行监控.
      RANK_DOT_COUNT(session_data, 1, "ad_rank.auction_bid_init_cnt",
                     session_data->get_search_rank_exp_name(),
                     session_data->get_search_interactive_form(), "dsp_ad_list");
      // double 能保证 15 位的精度
      if (auction_bid_init < 1e-10) {
        RANK_DOT_COUNT(session_data, 1, "ad_rank.auction_bid_init_zero",
                       session_data->get_search_rank_exp_name(),
                       session_data->get_search_interactive_form(), "dsp_ad_list");
      }
    }
    if (ad.get_unify_cvr_info().value < 1e-10) {
      RANK_DOT_COUNT(session_data, 1, "ad_rank.unify_cvr_zero", session_data->get_search_rank_exp_name(),
                     session_data->get_search_interactive_form(), "dsp_ad_list");
      LOG_IF(INFO, session_data->get_is_search_debug())
          << "cvr_zero_check"
          << "|s_type=" << ad.get_unify_cvr_info().s_type << "|e_type=" << ad.get_unify_cvr_info().e_type
          << "|ocpx_action_type=" << ad.get_ocpx_action_type()
          << "|exp_name=" << session_data->get_search_rank_exp_name()
          << "|interactive_form=" << session_data->get_search_interactive_form();
    }
  }

  // cid roi 出价相关打点
  if (ad.get_ocpx_action_type() == kuaishou::ad::AD_CID_ROAS) {
  RANK_DOT_STATS(session_data, ad.get_auction_bid(), "ad_rank.cid_cpm_cal.auction_bid");
  RANK_DOT_STATS(session_data, ad.get_cpm(), "ad_rank.cid_cpm_cal.cpm");
  RANK_DOT_STATS(session_data, ad.get_auto_roas(), "ad_rank.cid_cpm_cal.auto_roas");
  VLOG_EVERY_N(1, 1000) << "calc cid ecpm, account_id:" << ad.get_account_id()
                        << ", campaign_type:" << ad.get_campaign_type()
                        << ", cpm:" << ad.get_cpm()
                        << ", AuctionBid:" << ad.get_auction_bid()
                        << ", AutoRoas:" << ad.get_auto_roas()
                        << ", roi_ratio:" << ad.get_roi_ratio()
                        << ", cvr:" << ad.get_unify_cvr_info().value
                        << ", ltv:" << ad.get_merchant_ltv();
  }

  // 激励电商计费打折
  if (session_data->get_is_rewarded_merchant()) {
    double coef = 1.0;
    if (ad.get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
        ad.get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
      coef = params_->reward_merchant_roas_coef;
    } else if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
               ad.get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) {
      coef = params_->reward_merchant_orderpay_coef;
    }
    ad.SetAuctionBid(static_cast<int64>(ad.get_auction_bid() * coef),
        AuctionBidModifyTag::CalcCpmBidTypeOcpmDsp);
    ad.set_precise_auction_bid(ad.get_precise_auction_bid() * coef);
  }
  ad.set_origin_cpm(static_cast<int64>(ad.get_precise_auction_bid() * kBenifitFactor));
  // 虚拟金广告调整 cpm, 设置顶价，只竞争低价尾量
  if (ad.get_internal_invest_plan_id() > 0) {
    ad.set_cpm(
        static_cast<int64>(std::min(ad.get_cpm(), std::max(0L, params_->internal_invest_up_threshold))));
  }
  // 全店的 roi 与原始出价的差距监控
  if (ad.get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
      ad.get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
      ad.get_ocpx_action_type() == kuaishou::ad::CID_ROAS ||
      ad.get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
    RANK_DOT_STATS(session_data, ad.get_auto_roas(), "ad_rank.diff_auto_roas_roi", "auto_roas",
                   kuaishou::ad::AdEnum::ItemType_Name(ad.get_item_type()),
                   kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type()));
    RANK_DOT_STATS(session_data, ad.get_roi_ratio(), "ad_rank.diff_auto_roas_roi", "roi_ratio",
                   kuaishou::ad::AdEnum::ItemType_Name(ad.get_item_type()),
                   kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type()));
  }
  if (ad.get_is_purchase_pay_test()) {
    RANK_DOT_STATS(session_data, ad.get_auction_bid() * 1000000, "ad_rank.purchase_pay_calc_cpm",
                   "auction_bid_ocpm", "true", kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
    RANK_DOT_STATS(session_data, ad.get_cpm(), "ad_rank.purchase_pay_calc_cpm", "cpm_ocpm", "true",
                   kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
  }
  return StraRetCode::SUCC;
}   // NOLINT

std::unordered_map<int64, double> CalcCpmBidTypeOcpmDspPlugin::CoeffLoadStr2Map(
    const std::string& config_str) {
  // ab 参数加载成 map ，ab string 为 : 和 ; 分割，格式为 13002001:2;13002003:2;13002004:2;13002006:2
  std::unordered_map<int64, double> config_map;
  if (config_str == "") {
    return config_map;
  }
  std::vector<absl::string_view> items = absl::StrSplit(config_str, ";");
  for (int i = 0; i < items.size(); i++) {
    const std::vector<std::string> key_val = absl::StrSplit(items[i], ":");
    if (key_val.size() != 2) {
      continue;
    }
    int64 key = 0;
    double value = 0.0;
    if (!absl::SimpleAtoi(key_val[0], &key) || !absl::SimpleAtod(key_val[1], &value)) {
      LOG(ERROR) << "coeff load error, item=" << key_val[0];
      continue;
    }
    config_map.emplace(key, value);
  }
  return config_map;
}

// 爆品跟品打压逻辑
int64 CalcCpmBidTypeOcpmDspPlugin::GetSameMerchantProductPenalty(AdCommon* p_ad, CalcBenefitParams* params_,
                                                                 ContextData* session_data,
                                                                 int64 auction_bid) {
  auto& ad = *p_ad;
  int64 score = 0;
  bool valid = false;
  // 判断是否是短视频电商
  if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      (ad.get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
       ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::CID_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) {
    valid = true;
  }
  if (p_ad->get_parsed_item_id() == 0) {
    valid = false;
  }
  if (!valid) {
    return score;
  }
  auto same_merchant_product_penalty = RankKconfUtil::sameMerchantProductPenalty();
  if (same_merchant_product_penalty->count(p_ad->get_parsed_item_id()) <= 0) {
    return score;
  }

  if (params_->enable_same_merchant_product_constant_penalty) {
    score = params_->ecom_same_merchant_product_constant_penalty;
    score = std::min(auction_bid, score);
  }

  score = auction_bid * params_->ecom_same_merchant_product_proportional_penalty;
  score = std::min(auction_bid, score);
  return score;
}

// credit 门槛试验
bool CheckCreditEvalThreshold(ContextData *session_data,
                                      const AdCommon& ad,
                                       const DeepBidParams& params,
                                       double deep_conv_rate_orig) {
    if (params.use_credit_eval_model) {
        // 获取门槛配置
        double credit_eval_score_threshold = 0.03;
        if (ad.get_credit_eval_rate() < credit_eval_score_threshold) {
            return false;
        }
    }
    return true;
}

void GetDeepConvRateFromClick1(ContextData *session_data,
                              const AdCommon& ad,
                              const kuaishou::ad::AdRequest &request,
                              const DeepBidParams& params,
                              double click1_conv_rate,
                              double default_value,
                              double *deep_conv_rate) {
  switch (ad.get_deep_conversion_type()) {
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY:
      *deep_conv_rate = std::min(ad.get_predict_score(PredictType::PredictType_click_purchase),
                                 params.max_deep_conversion_rate_over_conv * click1_conv_rate)
      * params.deep_predict_ratio_discount_params.pay_times;
      // 对于双出价使用深度统一模型，由于考虑的是付费设备数，需乘一个大数
      // 快享是用的 cmd 与主站不一致，这里不能给破坏掉
      if (session_data->get_pos_manager_base().GetAdRequestType()
          != kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_ITEM
          && params.twin_bid_use_unified_model_ratio != 1.0) {
        *deep_conv_rate *= params.twin_bid_use_unified_model_ratio;
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT: {
      if (params.jinjian_credit_twin_bid_from_click) {
        *deep_conv_rate = ad.get_click_credit_rate()
                          * params.deep_predict_ratio_discount_params.credit_grant;
      } else {
        *deep_conv_rate = click1_conv_rate *
                          std::min(ad.get_credit_rate(), params.max_deep_conversion_rate_over_conv)
                          * params.deep_predict_ratio_discount_params.credit_grant;
      }
      // 低于门槛就填个极低值
      if (!CheckCreditEvalThreshold(session_data, ad, params, *deep_conv_rate)) {
        *deep_conv_rate = 1E-8;
      }
    } break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN:
      if (params.jinjian_credit_twin_bid_from_click) {
        *deep_conv_rate = ad.get_click_credit_rate()
                          * params.deep_predict_ratio_discount_params.credit_grant;
      } else {
        *deep_conv_rate = click1_conv_rate *
                          std::min(ad.get_credit_rate(), params.max_deep_conversion_rate_over_conv)
                          * params.deep_predict_ratio_discount_params.credit_jinjian;
      }

      // 低于门槛就填个极低值
      if (!CheckCreditEvalThreshold(session_data, ad, params, *deep_conv_rate)) {
        *deep_conv_rate = 1E-8;
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY:
      // 不能高于设定的值，不能高于激活率
      *deep_conv_rate = std::min(ad.get_predict_score(PredictType::PredictType_click_retention),
                                 params.max_deep_conversion_rate_over_conv * click1_conv_rate)
          * params.deep_predict_ratio_discount_params.nextday_stay;
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_ADD_SHOPPINGCART:
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_ORDER_SUBMIT:
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_ORDER_PAIED:
      // 目前只有点击到电商深度行为的模型
      *deep_conv_rate = std::min(ad.get_predict_score(PredictType::PredictType_shop_action),
                                 params.max_deep_conversion_rate_over_conv * click1_conv_rate)
          * params.deep_predict_ratio_discount_params.add_shopping_cart;
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_VALID_CLUES:
      *deep_conv_rate = ad.get_predict_score(PredictType::PredictType_deep_rate);
      break;
    default:
      *deep_conv_rate = default_value;
  }
}

void GetDeepConvRateFromClick2(ContextData *session_data,
                              const AdCommon& ad,
                              const kuaishou::ad::AdRequest &request,
                              const DeepBidParams& params,
                              double click2_conv_rate,
                              double default_value,
                              double *deep_conv_rate) {
  switch (ad.get_deep_conversion_type()) {
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY:
      *deep_conv_rate = std::min(ad.get_click2_purchase(),
                                 params.max_deep_conversion_rate_over_conv * click2_conv_rate)
          * params.deep_predict_ratio_discount_params.pay_times;
      // 对于双出价使用深度统一模型，由于考虑的是付费设备数，需乘一个大数
      // 快享是用的 cmd 与主站不一致，这里不能给破坏掉
      if (session_data->get_pos_manager_base().GetAdRequestType()
          != kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_ITEM
          && params.twin_bid_use_unified_model_ratio != 1.0) {
        *deep_conv_rate *= params.twin_bid_use_unified_model_ratio;
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT: {
      if (params.jinjian_credit_twin_bid_from_click) {
        *deep_conv_rate = ad.get_click2_credit_rate()
                          * params.deep_predict_ratio_discount_params.credit_grant;
      } else {
        *deep_conv_rate = click2_conv_rate *
                          std::min(ad.get_credit_rate(), params.max_deep_conversion_rate_over_conv)
                          * params.deep_predict_ratio_discount_params.credit_grant;
      }

      // 低于门槛就填个极低值
      if (!CheckCreditEvalThreshold(session_data, ad, params, *deep_conv_rate)) {
        *deep_conv_rate = 1E-8;
      }
    } break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN:
      if (params.jinjian_credit_twin_bid_from_click) {
        *deep_conv_rate = ad.get_click2_credit_rate()
                          * params.deep_predict_ratio_discount_params.credit_grant;
      } else {
        *deep_conv_rate = click2_conv_rate *
                          std::min(ad.get_credit_rate(), params.max_deep_conversion_rate_over_conv)
                          * params.deep_predict_ratio_discount_params.credit_jinjian;
      }
      // 低于门槛就填个极低值
      if (!CheckCreditEvalThreshold(session_data, ad, params, *deep_conv_rate)) {
        *deep_conv_rate = 1E-8;
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY:
      // 不能高于设定的值，不能高于激活率
      *deep_conv_rate = std::min(ad.get_click2_nextstay(),
                                 params.max_deep_conversion_rate_over_conv * click2_conv_rate)
          * params.deep_predict_ratio_discount_params.nextday_stay;
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_ADD_SHOPPINGCART:
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_ORDER_SUBMIT:
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_ORDER_PAIED:
      // 目前只有点击到电商深度行为的模型
      *deep_conv_rate = std::min(ad.get_click2_shop_action(),
                                 params.max_deep_conversion_rate_over_conv * click2_conv_rate)
          * params.deep_predict_ratio_discount_params.add_shopping_cart;
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_VALID_CLUES:
      *deep_conv_rate = ad.get_predict_score(PredictType::PredictType_click2_deep_rate);
      break;
    default:
      *deep_conv_rate = default_value;
  }
}

}  // namespace ad_rank
}  // namespace ks
