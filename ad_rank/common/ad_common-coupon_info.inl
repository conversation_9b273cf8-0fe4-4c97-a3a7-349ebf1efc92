// auto gen public member 7 for CouponInfo base.coupon_info

inline void set_coupon_template_id(uint64_t v) {
  Attr(ItemIdx::coupon_template_id).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_coupon_template_id() const {
  return Attr(ItemIdx::coupon_template_id).GetIntValue(attr_row_).value_or(0);
}

inline void set_coupon_type(uint32_t v) {
  Attr(ItemIdx::coupon_type).SetIntValue(attr_row_, v, false, false);
}
inline uint32_t get_coupon_type() const {
  return Attr(ItemIdx::coupon_type).GetIntValue(attr_row_).value_or(0);
}

inline void set_threshold_type(uint32_t v) {
  Attr(ItemIdx::threshold_type).SetIntValue(attr_row_, v, false, false);
}
inline uint32_t get_threshold_type() const {
  return Attr(ItemIdx::threshold_type).GetIntValue(attr_row_).value_or(0);
}

inline void set_threshold(uint64_t v) {
  Attr(ItemIdx::threshold).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_threshold() const {
  return Attr(ItemIdx::threshold).GetIntValue(attr_row_).value_or(0);
}

inline void set_coupon_discount_amount(uint64_t v) {
  Attr(ItemIdx::coupon_discount_amount).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_coupon_discount_amount() const {
  return Attr(ItemIdx::coupon_discount_amount).GetIntValue(attr_row_).value_or(0);
}

inline void set_threshold_upper(uint64_t v) {
  Attr(ItemIdx::threshold_upper).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_threshold_upper() const {
  return Attr(ItemIdx::threshold_upper).GetIntValue(attr_row_).value_or(0);
}

inline void set_discount_amount_upper(uint64_t v) {
  Attr(ItemIdx::discount_amount_upper).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_discount_amount_upper() const {
  return Attr(ItemIdx::discount_amount_upper).GetIntValue(attr_row_).value_or(0);
}

inline void set_expire_minutes(uint64_t v) {
  Attr(ItemIdx::expire_minutes).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_expire_minutes() const {
  return Attr(ItemIdx::expire_minutes).GetIntValue(attr_row_).value_or(0);
}

inline void set_reduce_amount(uint64_t v) {
  Attr(ItemIdx::reduce_amount).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_reduce_amount() const {
  return Attr(ItemIdx::reduce_amount).GetIntValue(attr_row_).value_or(0);
}

inline void set_capped_amount(uint64_t v) {
  Attr(ItemIdx::capped_amount).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_capped_amount() const {
  return Attr(ItemIdx::capped_amount).GetIntValue(attr_row_).value_or(0);
}

inline void set_inner_qcpx_cause(uint64_t v) {
  Attr(ItemIdx::inner_qcpx_cause).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_inner_qcpx_cause() const {
  return Attr(ItemIdx::inner_qcpx_cause).GetIntValue(attr_row_).value_or(0);
}

inline void set_inner_qcpx_live_cvr_elastic_c0_front(double v) {
  Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c0_front).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_inner_qcpx_live_cvr_elastic_c0_front() const {
  return Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c0_front).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_inner_qcpx_live_cvr_elastic_c1_front(double v) {
  Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c1_front).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_inner_qcpx_live_cvr_elastic_c1_front() const {
  return Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c1_front).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_inner_qcpx_live_cvr_elastic_c0_end(double v) {
  Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c0_end).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_inner_qcpx_live_cvr_elastic_c0_end() const {
  return Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c0_end).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_inner_qcpx_live_cvr_elastic_c1_end(double v) {
  Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c1_end).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_inner_qcpx_live_cvr_elastic_c1_end() const {
  return Attr(ItemIdx::inner_qcpx_live_cvr_elastic_c1_end).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_inner_qcpx_live_cvr(double v) {
  Attr(ItemIdx::inner_qcpx_live_cvr).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_inner_qcpx_live_cvr() const {
  return Attr(ItemIdx::inner_qcpx_live_cvr).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_coupon_scope(uint64_t v) {
  Attr(ItemIdx::coupon_scope).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_coupon_scope() const {
  return Attr(ItemIdx::coupon_scope).GetIntValue(attr_row_).value_or(0);
}

inline void set_inner_qcpx_live_thre(double v) {
  Attr(ItemIdx::inner_qcpx_live_thre).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_inner_qcpx_live_thre() const {
  return Attr(ItemIdx::inner_qcpx_live_thre).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_playlet_unify_ltv(double v) {
  Attr(ItemIdx::playlet_unify_ltv).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_playlet_unify_ltv() const {
  return Attr(ItemIdx::playlet_unify_ltv).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_playlet_ctr(double v) {
  Attr(ItemIdx::playlet_ctr).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_playlet_ctr() const {
  return Attr(ItemIdx::playlet_ctr).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_playlet_cvr(double v) {
  Attr(ItemIdx::playlet_cvr).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_playlet_cvr() const {
  return Attr(ItemIdx::playlet_cvr).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_playlet_industry_ltv(double v) {
  Attr(ItemIdx::playlet_industry_ltv).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_playlet_industry_ltv() const {
  return Attr(ItemIdx::playlet_industry_ltv).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_playlet_industry_pay(double v) {
  Attr(ItemIdx::playlet_industry_pay).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_playlet_industry_pay() const {
  return Attr(ItemIdx::playlet_industry_pay).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_playlet_mid_ltv(double v) {
  Attr(ItemIdx::playlet_mid_ltv).SetDoubleValue(attr_row_, v, false, false);
}

inline double get_playlet_mid_ltv() const {
  return Attr(ItemIdx::playlet_mid_ltv).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_playlet_mid_real_ltv(double v) {
  Attr(ItemIdx::playlet_mid_real_ltv).SetDoubleValue(attr_row_, v, false, false);
}

inline double get_playlet_mid_real_ltv() const {
  return Attr(ItemIdx::playlet_mid_real_ltv).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_playlet_mid_iaa_calied_ltv(double v) {
  Attr(ItemIdx::playlet_mid_iaa_calied_ltv).SetDoubleValue(attr_row_, v, false, false);
}

inline double get_playlet_mid_iaa_calied_ltv() const {
  return Attr(ItemIdx::playlet_mid_iaa_calied_ltv).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_inner_qcpx_filter_reason(uint64_t v) {
  Attr(ItemIdx::inner_qcpx_filter_reason).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_inner_qcpx_filter_reason() const {
  return Attr(ItemIdx::inner_qcpx_filter_reason).GetIntValue(attr_row_).value_or(0);
}

inline void set_coupon_display_type(uint64_t v) {
  Attr(ItemIdx::coupon_display_type).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_coupon_display_type() const {
  return Attr(ItemIdx::coupon_display_type).GetIntValue(attr_row_).value_or(0);
}

inline void set_pre_boost_discount_amount(uint64_t v) {
  Attr(ItemIdx::pre_boost_discount_amount).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_pre_boost_discount_amount() const {
  return Attr(ItemIdx::pre_boost_discount_amount).GetIntValue(attr_row_).value_or(0);
}

inline void set_inner_qcpx_roi_pid_value(double v) {
  Attr(ItemIdx::inner_qcpx_roi_pid_value).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_inner_qcpx_roi_pid_value() const {
  return Attr(ItemIdx::inner_qcpx_roi_pid_value).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_optimal_style_disable_feed_card(uint64_t v) {
  Attr(ItemIdx::optimal_style_disable_feed_card).SetIntValue(attr_row_, v, false, false);
}
inline uint64_t get_optimal_style_disable_feed_card() const {
  return Attr(ItemIdx::optimal_style_disable_feed_card).GetIntValue(attr_row_).value_or(0);
}
inline void set_inner_qcpx_search_and_push_ratio(double v) {
  Attr(ItemIdx::inner_qcpx_search_and_push_ratio).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_inner_qcpx_search_and_push_ratio() const {
  return Attr(ItemIdx::inner_qcpx_search_and_push_ratio).GetDoubleValue(attr_row_).value_or(1.0);
}