#pragma once

/*
 * 使用说明:
 * 1. 一般打点使用 RANK_PERF_STATS & RANK_PERF_COUNT, 框架统一控制采样频率(10%)
 * 2. 自定义频率的使用 RANK_PERF_STATS_EVERY_N & RANK_PERF_COUNT_EVERY_N, CR 时提交注释，写明原因
 * 3. 每个接口最多支持 6 个 extra_tag
 * 4. 打点接口不满足需求的，可联系 RankSvr 负责人添加
 *
 */

#include <string>
#include <utility>
#include <vector>

#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"
#include "teams/ad/ad_base/src/perf/perf.h"

#define PERF_EVERY_N_VARNAME(base, line) PERF_EVERY_N_VARNAME_CONCAT(base, line)
#define PERF_EVERY_N_VARNAME_CONCAT(base, line) base##line

#define PERF_OCCURRENCES PERF_EVERY_N_VARNAME(perf_, __LINE__)

#define RANK_DOT_STATS(context, Value, Subtag, ...) \
  static int PERF_OCCURRENCES;                      \
  ++PERF_OCCURRENCES;                               \
  if (PERF_OCCURRENCES > 10)                        \
    PERF_OCCURRENCES -= 10;                         \
  if (PERF_OCCURRENCES == 1)                        \
    context->dot_perf->Interval(Value, Subtag, ##__VA_ARGS__);

#define RANK_DOT_COUNT(context, Value, Subtag, ...) \
  static int PERF_OCCURRENCES;                      \
  ++PERF_OCCURRENCES;                               \
  if (PERF_OCCURRENCES > 10)                        \
    PERF_OCCURRENCES -= 10;                         \
  if (PERF_OCCURRENCES == 1)                        \
    context->dot_perf->Count(Value, Subtag, ##__VA_ARGS__);

#define RANK_PERF_STATS(Value, NameSpace, Subtag, ...)          \
  static int PERF_OCCURRENCES;                                  \
  ++PERF_OCCURRENCES;                                           \
  if (PERF_OCCURRENCES > RankKconfUtil::perfUnifySampleRatio()) \
    PERF_OCCURRENCES -= RankKconfUtil::perfUnifySampleRatio();  \
  if (PERF_OCCURRENCES == 1)                                    \
    ks::ad_base::AdPerf::IntervalLogStash(Value, NameSpace, Subtag, ##__VA_ARGS__);

#define RANK_PERF_COUNT(Value, NameSpace, Subtag, ...)          \
  static int PERF_OCCURRENCES;                                  \
  ++PERF_OCCURRENCES;                                           \
  if (PERF_OCCURRENCES > RankKconfUtil::perfUnifySampleRatio()) \
    PERF_OCCURRENCES -= RankKconfUtil::perfUnifySampleRatio();  \
  if (PERF_OCCURRENCES == 1)                                    \
    ks::ad_base::AdPerf::CountLogStash(Value, NameSpace, Subtag, ##__VA_ARGS__);

#define RANK_PERF_STATS_EVERY_N(Mod, Value, NameSpace, Subtag, ...) \
  static_assert(Mod > 0);                                                      \
  static int PERF_OCCURRENCES;                                                 \
  ++PERF_OCCURRENCES;                                                          \
  if (PERF_OCCURRENCES > Mod)                                                  \
    PERF_OCCURRENCES -= Mod;                                                   \
  if (PERF_OCCURRENCES == 1)                                                   \
    ks::ad_base::AdPerf::IntervalLogStash(Value, NameSpace, Subtag, ##__VA_ARGS__);

#define RANK_PERF_COUNT_EVERY_N(Mod, Value, NameSpace, Subtag, ...) \
  static_assert(Mod > 0);                                                      \
  static int PERF_OCCURRENCES;                                                 \
  ++PERF_OCCURRENCES;                                                          \
  if (PERF_OCCURRENCES > Mod)                                                  \
    PERF_OCCURRENCES -= Mod;                                                   \
  if (PERF_OCCURRENCES == 1)                                                   \
    ks::ad_base::AdPerf::CountLogStash(Value, NameSpace, Subtag, ##__VA_ARGS__);

#define CONTINUE_WITH_REASON(event_id, node_name, point_name, plugin_name)                                  \
  {                                                                                                         \
    ad.SetAdInValid(event_id, node_name, point_name, plugin_name);                                        \
    if (ad.get_ad_source_type() == kuaishou::ad::ADX) {                                                   \
      PERF_ADX_DATA(absl::AsciiStrToLower(kuaishou::log::ad::AdTraceFilterCondition_Name(                 \
                          static_cast<kuaishou::log::ad::AdTraceFilterCondition>(event_id))),               \
                      ad.get_adx_source_type(), ad.get_tag_id(), 1);                                        \
    }                                                                                                     \
    if (ad.get_campaign_type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN) {                                   \
      PERF_DPA_DATA(kuaishou::log::ad::AdTraceFilterCondition_Name(event_id),                             \
                    kuaishou::ad::AdEnum::CampaignSubType_Name(ad.get_campaign_sub_type()), 1);           \
    }                                                                                                     \
    session_data->RecordLastCondition(event_id, ad.Is(AdFlag::IsHardAd));                                 \
    continue;                                                                                             \
  }

#define PERF_ADX_DATA(value, adx_id, ext2, count)                                               \
  do {                                                                                          \
    ks::ad_base::AdPerf::CountLogStash(count, "ad.rank_server", value,                          \
                                       kuaishou::ad::AdxSourceType_Name(adx_id).c_str(), ext2); \
  } while (0)

#define PERF_ADX_FILTER_DATA(event_id, adx_id, tag_id, count)                                     \
  do {                                                                                            \
    ks::ad_base::AdPerf::CountLogStash(count, "ad.ad_rank", "ad.rank_server.adx_filter",          \
                                       kuaishou::log::ad::AdTraceFilterCondition_Name(event_id),  \
                                       kuaishou::ad::AdxSourceType_Name(adx_id).c_str(), tag_id); \
  } while (0)

#define PERF_DPA_DATA(value, ext2, count)                                                              \
  do {                                                                                                 \
    ks::ad_base::AdPerf::CountLogStash(count, "ad.ad_rank", "ad.rank_server.dpa_filter", value, ext2); \
  } while (0)

#define FILTER_WITH_REASON_V2(event_id, node_name, point_name, plugin_name)                     \
  {                                                                                             \
    ad.SetAdInValid(event_id, node_name, point_name, plugin_name);                              \
    if (ad.get_ad_source_type() == kuaishou::ad::ADX) {                                         \
      PERF_ADX_DATA(absl::AsciiStrToLower(kuaishou::log::ad::AdTraceFilterCondition_Name(       \
                        static_cast<kuaishou::log::ad::AdTraceFilterCondition>(event_id))),     \
                    ad.get_adx_source_type(), ad.get_tag_id(), 1);                              \
      PERF_ADX_FILTER_DATA(event_id, ad.get_adx_source_type(), ad.get_tag_id(), 1);             \
    }                                                                                           \
    if (ad.get_campaign_type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN) {                         \
      PERF_DPA_DATA(kuaishou::log::ad::AdTraceFilterCondition_Name(event_id),                   \
                    kuaishou::ad::AdEnum::CampaignSubType_Name(ad.get_campaign_sub_type()), 1); \
    }                                                                                           \
    session_data_->RecordLastCondition(event_id, ad.Is(AdFlag::IsHardAd));                      \
    return StraRetCode::ABORT;                                                                  \
  }

#define CONTINUE_WITH_REASON_V2(event_id, node_name, point_name, plugin_name)                               \
  {                                                                                                         \
    ad.SetAdInValid(event_id, node_name, point_name, plugin_name);                                        \
    if (ad.get_ad_source_type() == kuaishou::ad::ADX) {                                                   \
      PERF_ADX_DATA(absl::AsciiStrToLower(kuaishou::log::ad::AdTraceFilterCondition_Name(                 \
                        static_cast<kuaishou::log::ad::AdTraceFilterCondition>(event_id))),               \
                    ad.get_adx_source_type(), ad.get_tag_id(), 1);                                        \
      PERF_ADX_FILTER_DATA(event_id, ad.get_adx_source_type(), ad.get_tag_id(), 1);                       \
    }                                                                                                     \
    if (ad.get_campaign_type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN) {                                   \
      PERF_DPA_DATA(kuaishou::log::ad::AdTraceFilterCondition_Name(event_id),                             \
                    kuaishou::ad::AdEnum::CampaignSubType_Name(ad.get_campaign_sub_type()), 1);           \
    }                                                                                                     \
    session_data_->RecordLastCondition(event_id, ad.Is(AdFlag::IsHardAd));                                \
    continue;                                                                                             \
  }

#define RETURN_WITH_REASON_V2(event_id, node_name, point_name, plugin_name)                     \
  {                                                                                             \
    ad.SetAdInValid(event_id, node_name, point_name, plugin_name);                              \
    if (ad.get_ad_source_type() == kuaishou::ad::ADX) {                                         \
      PERF_ADX_DATA(absl::AsciiStrToLower(kuaishou::log::ad::AdTraceFilterCondition_Name(       \
                        static_cast<kuaishou::log::ad::AdTraceFilterCondition>(event_id))),     \
                    ad.get_adx_source_type(), ad.get_tag_id(), 1);                              \
      PERF_ADX_FILTER_DATA(event_id, ad.get_adx_source_type(), ad.get_tag_id(), 1);             \
    }                                                                                           \
    if (ad.get_campaign_type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN) {                         \
      PERF_DPA_DATA(kuaishou::log::ad::AdTraceFilterCondition_Name(event_id),                   \
                    kuaishou::ad::AdEnum::CampaignSubType_Name(ad.get_campaign_sub_type()), 1); \
    }                                                                                           \
    session_data_->RecordLastCondition(event_id, ad.Is(AdFlag::IsHardAd));                      \
    return;                                                                                     \
  }

#define CHECK_WITH_REASON(event_id, node_name, point_name, plugin_name)                         \
  {                                                                                             \
    ad.SetAdInValid(event_id, node_name, point_name, plugin_name);                              \
    if (ad.get_ad_source_type() == kuaishou::ad::ADX) {                                         \
      PERF_ADX_DATA(absl::AsciiStrToLower(kuaishou::log::ad::AdTraceFilterCondition_Name(       \
                        static_cast<kuaishou::log::ad::AdTraceFilterCondition>(event_id))),     \
                    ad.get_adx_source_type(), ad.get_tag_id(), 1);                              \
      PERF_ADX_FILTER_DATA(event_id, ad.get_adx_source_type(), ad.get_tag_id(), 1);             \
    }                                                                                           \
    if (ad.get_campaign_type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN) {                         \
      PERF_DPA_DATA(kuaishou::log::ad::AdTraceFilterCondition_Name(event_id),                   \
                    kuaishou::ad::AdEnum::CampaignSubType_Name(ad.get_campaign_sub_type()), 1); \
    }                                                                                           \
    session_data_->RecordLastCondition(event_id, ad.Is(AdFlag::IsHardAd));                      \
  }

// NOTE(nanning): 内粉排序分分布统计
#define INNER_FANSTOP_CPM_AUTOBID_DISTRIBUTE(session_data, result, auto_bid)         \
  {                                                                                  \
    std::string auto_bid_dist = "default";                                           \
    if (auto_bid < 0) {                                                              \
      auto_bid_dist = "<0";                                                          \
    } else if (auto_bid == 0) {                                                      \
      auto_bid_dist = "=0";                                                          \
    } else if (auto_bid < 10) {                                                      \
      auto_bid_dist = "[0, 10)";                                                     \
    } else if (auto_bid < 100) {                                                     \
      auto_bid_dist = "[10, 100)";                                                   \
    } else if (auto_bid < 1000) {                                                    \
      auto_bid_dist = "[100, 1000)";                                                 \
    } else if (auto_bid < 10000) {                                                   \
      auto_bid_dist = "[1000, 10000)";                                               \
    } else if (auto_bid < 100000) {                                                  \
      auto_bid_dist = "[10000, 100000)";                                             \
    } else {                                                                         \
      auto_bid_dist = ">=100000";                                                    \
    }                                                                                \
    ks::ad_base::AdPerf::CountLogStash(1, "ad.fanstop_server", "com_autobid_dist",   \
                                       session_data->IsSelected()     ? "Selected"   \
                                       : session_data->IsNebula()     ? "Nebula"     \
                                       : session_data->IsNormalFeed() ? "NormalFeed" \
                                                                      : "Others",    \
                                       auto_bid_dist);                               \
  }

// 记录 ecpm 调价实验
#define INNER_FANSTOP_RANK_ECPM_ADJ(session_data, value, subtag, exp_flag)              \
  {                                                                                     \
    ks::ad_base::AdPerf::IntervalLogStash(value, "ad.fanstop_server", subtag, exp_flag, \
                                          session_data->IsSelected()     ? "Selected"   \
                                          : session_data->IsNebula()     ? "Nebula"     \
                                          : session_data->IsNormalFeed() ? "NormalFeed" \
                                                                         : "Others");   \
  }

#define INNER_FANSTOP_RANK_ECPM_ADJ_DTAIL(session_data, item_type, bid_type, priority_level, value, subtag, \
                                          exp_flag)                                                         \
  {                                                                                                         \
    ks::ad_base::AdPerf::IntervalLogStash(                                                                  \
        value, "ad.fanstop_server", subtag, exp_flag, kuaishou::ad::AdEnum::ItemType_Name(item_type),       \
        kuaishou::ad::AdEnum::BidType_Name(bid_type), priority_level > 0 ? "true" : "false",                \
        session_data->IsSelected()     ? "Selected"                                                         \
        : session_data->IsNebula()     ? "Nebula"                                                           \
        : session_data->IsNormalFeed() ? "NormalFeed"                                                       \
                                       : "Others");                                                         \
  }

#define CONTINUE_FOR_INVALID_AD(ad) \
  {                                 \
    if (!ad.Is(AdFlag::GetValid)) { \
      continue;                     \
    }                               \
  }

#define PERF_COUNT_EXT(counter, name, extra) \
  { ks::ad_base::AdPerf::CountLogStash(counter, "ad.fanstop_server", name, extra); }

#define PERF_INTERVAL(counter, name) \
  { ks::ad_base::AdPerf::IntervalLogStash(counter, "ad.fanstop_server", name); }

#define PERF_COUNT_APP_PAGE_VIEW_PLATFORM_EXT(counter, name, session_data, extra)                            \
  {                                                                                                          \
    ks::ad_base::AdPerf::CountLogStash(counter, "ad.fanstop_server.fanstop", name, session_data->app_id,     \
                                       kuaishou::fanstop::FansTopEnum_Page_Name(session_data->monitor_page), \
                                       std::to_string(static_cast<int>(session_data->fans_top_view_type)),   \
                                       session_data->platform, extra);                                       \
    ks::ad_base::AdPerf::CountLogStash(counter, "ad.fanstop_server", name, "0", session_data->app_id,        \
                                       std::to_string(session_data->page_id),                                \
                                       std::to_string(session_data->sub_page_id), extra);                    \
  }

#define CONTINUE_WITH_REASON_FANS(ad_filter_event, fans_ad, session_data)                            \
  {                                                                                                  \
    session_data->RecordLastCondition(ad_filter_event, fans_ad->Is(AdFlag::IsHardAd));             \
    (*session_data->mutable_rank_filter_ads())                                                     \
        .insert(std::make_pair(fans_ad->get_creative_id(), ad_filter_event));                      \
    fans_ad->SetAdInValid(ad_filter_event, kuaishou::ad::AdRankNodeType::FANSTOP_RANKING_TYPE,     \
                          kuaishou::ad::AdRankPointType::RANK_POINT_DEFAULT_TYPE,                  \
                          kuaishou::ad::AdRankPluginType::RANK_PLUGIN_DEFAULTTYPE);                \
    std::string event_name = kuaishou::log::ad::AdTraceFilterCondition_Name(ad_filter_event);      \
    session_data->mutable_fans_session_data()->perf_helper.FilterRecord(ad_filter_event, fans_ad); \
    continue;                                                                                      \
  }

#define MONITOR(monitor_key)                                                                      \
  for (auto p_ad : (*session_data_->mutable_ad_list()).Ads()) {                                   \
    if (ad_base::AdRandom::GetInt(1, 100) < RankKconfUtil::monitorRecordRatio()) {                \
      RANK_DOT_COUNT(session_data_, 1, monitor_key, std::to_string(p_ad->get_ocpx_action_type()), \
                     std::to_string(p_ad->get_industry_parent_id_v3()));                          \
    }                                                                                             \
  }

#define AD_LIST_SKIP_LIVE_AD(session_data, p_ad)                    \
  {                                                                 \
    if (p_ad == nullptr) {                                          \
      continue;                                                     \
    }                                                               \
    if (!session_data->get_is_search_request() &&                   \
        p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD) { \
      continue;                                                     \
    }                                                               \
  }

#define SEARCH_AD_LIST_SKIP_LIVE_AD(session_data, p_ad)                                                      \
  {                                                                                                          \
    if (p_ad == nullptr) {                                                                                   \
      continue;                                                                                              \
    }                                                                                                        \
    if (session_data->get_is_search_request() && p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD) { \
      continue;                                                                                              \
    }                                                                                                        \
  }

#define SEARCH_AD_LIST_SKIP_PHOTO_AD(session_data, p_ad)             \
  {                                                                  \
    if (p_ad == nullptr) {                                           \
      continue;                                                      \
    }                                                                \
    if (session_data->get_is_search_request() &&                     \
        p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) { \
      continue;                                                      \
    }                                                                \
  }

#define SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad)                                               \
  {                                                                                                   \
    if (p_ad == nullptr) {                                                                            \
      continue;                                                                                       \
    }                                                                                                 \
    if (session_data->get_is_search_request() && p_ad->get_queue_type() == RankAdListType::FANSTOP) { \
      continue;                                                                                       \
    }                                                                                                 \
  }

#define AD_LIST_SKIP_POP_RECRUIT_AD(session_data, p_ad) \
  {                                                     \
    if (p_ad == nullptr) {                              \
      continue;                                         \
    }                                                   \
    if (p_ad->Is(AdFlag::is_pop_recruit_ad)) {          \
      continue;                                         \
    }                                                   \
  }


#define ENUM_ITEM_ATTR(name, type) \
inline void set_##name(type v) { \
  Attr(ItemIdx::name).SetIntValue(attr_row_, static_cast<int64_t>(v), false, false); \
} \
inline type get_##name() const { \
  return static_cast<type>(Attr(ItemIdx::name).GetIntValue(attr_row_).value_or(0)); \
}

#define BOOL_ITEM_ATTR_SETTER(name) \
inline void set_##name(bool v) { \
  Attr(ItemIdx::name).SetIntValue(attr_row_, v ? 1 : 0, false, false); \
} \

#define BOOL_ITEM_ATTR_GETTER(name) \
inline bool get_##name() const { \
  return Attr(ItemIdx::name).GetIntValue(attr_row_).value_or(0) == 0 ? false : true; \
}

#define BOOL_ITEM_ATTR(name) \
BOOL_ITEM_ATTR_SETTER(name) \
BOOL_ITEM_ATTR_GETTER(name)

#define DOUBLE_ITEM_ATTR_SETTER(name) \
inline void set_##name(double v) { \
  Attr(ItemIdx::name).SetDoubleValue(attr_row_, v, false, false); \
} \

#define DOUBLE_ITEM_ATTR_GETTER(name, default_) \
inline double get_##name() const { \
  return Attr(ItemIdx::name).GetDoubleValue(attr_row_).value_or(default_); \
}

#define DOUBLE_ITEM_ATTR(name, default_) \
DOUBLE_ITEM_ATTR_SETTER(name) \
DOUBLE_ITEM_ATTR_GETTER(name, default_)

#define INT_ITEM_ATTR_GETTER(name, default_) \
inline int64_t get_##name() const { \
  return Attr(ItemIdx::name).GetIntValue(attr_row_).value_or(default_); \
}

#define INT_ITEM_ATTR_SETTER(name) \
inline void set_##name(int64_t v) { \
  Attr(ItemIdx::name).SetIntValue(attr_row_, v, false, false); \
} \

#define INT_ITEM_ATTR(name, default_) \
INT_ITEM_ATTR_SETTER(name) \
INT_ITEM_ATTR_GETTER(name, default_)

#define INT_LIST_ITEM_ATTR_GETTER(name) \
inline absl::Span<const int64> get_##name() const { \
  return Attr(ItemIdx::name).GetIntListValue(attr_row_).value_or(absl::Span<const int64_t>()); \
}

#define INT_LIST_ITEM_ATTR_APPENDER(name) \
inline bool append_##name(int64 v) { \
  return Attr(ItemIdx::name).AppendIntListValue(attr_row_, v); \
}

#define INT_LIST_ITEM_ATTR_SETTER(name) \
inline bool set_##name(std::vector<int64> &&val) { \
  return Attr(ItemIdx::name).SetIntListValue(attr_row_, std::move(val), false, false); \
} \

#define INT_LIST_ITEM_ATTR_RESETTER(name) \
inline bool reset_##name(int capacity = 0) { \
  return Attr(ItemIdx::name).ResetIntListValue(attr_row_, capacity, false, false); \
} \

#define INT_LIST_ITEM_ATTR_MODIFIERS(name) \
INT_LIST_ITEM_ATTR_SETTER(name) \
INT_LIST_ITEM_ATTR_RESETTER(name) \
INT_LIST_ITEM_ATTR_APPENDER(name)

#define INT_LIST_ITEM_ATTR(name) \
INT_LIST_ITEM_ATTR_GETTER(name) \
INT_LIST_ITEM_ATTR_MODIFIERS(name)
