#pragma once

#include <string.h>
#include <absl/strings/substitute.h>

#include <algorithm>
#include <functional>
#include <memory>
#include <ostream>
#include <set>
#include <map>
#include <string>
#include <type_traits>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_map.h"
#include "glog/logging.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "teams/ad/ad_base/src/common/enum.h"
#include "teams/ad/ad_base/src/common/pod_check.h"
#include "teams/ad/ad_base/src/container/stl_helper.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_trace_common.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/table_extend_fields.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.pb.h"
#include "teams/ad/ad_rank/common/ad_base_data.h"
#include "teams/ad/ad_rank/common/ad_common_constant.h"
#include "teams/ad/ad_rank/common/enum.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/engine_base/kconf/universe_ecpc_info_config.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/engine_base/cmd_curator/ad_base_data.h"
#include "teams/ad/engine_base/biz_common/resource_util.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_0.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_1.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_2.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_3.pb.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data_split_4.pb.h"
#include "teams/ad/ad_proto/maven/ad/coupon/ad_coupon_cache.pb.h"
#include "teams/ad/ad_feature_index/service/ad_feature_index_client_schemafree.h"
#include "teams/ad/ad_rank/common/base_data_def.h"
#include "teams/ad/ad_rank/common/ad_item_attr_accessor.h"
#include "teams/ad/ad_rank/common/ad_item_boolean_func.h"

#define ECOM_INDUSTRY_ID 20
#define TX_INDUSTRY_ID 38
#define TX_INDUSTRY_ID_V3 1003
#define COMP_ECOM_INDUSTRY_ID 29
#define ECOM_INDUSTRY_ID_V3 1022
#define COMP_ECOM_INDUSTRY_ID_V3 1032
#define MAX_FILTER_SIZE 1024
#define PAID_DUANJU_INDUSTRY_ID 2012
#define MOVIE_INDUSTRY_ID 2003

namespace kuaishou {
namespace ad {
namespace algorithm {
class CmdItemMapping;
}  // namespace algorithm
}  // namespace ad
}  // namespace kuaishou

namespace ks {
namespace ad_base {
enum AutoCpaBidModifyTagType;
}

enum RankAdListType {
  UNKNOWN = 0,
  NORMAL_PHOTO_AD = 1,
  NORMAL_LIVE_AD = 2,
  NATIVE_AD = 3,
  FANSTOP = 4,
  INNER_FANSTOP = 5
};

enum RankPredictQueueType {
  UNKNOWN_TYPE = 0,
  OUTER = 1,
  INNER_NORMAL = 2,
  INNER_NATIVE = 3
};

const double epsilon = 0.000001;
namespace ad_rank {
uint64_t UniqueDragonItemKey();
extern const char * kDefaultItemTableName;
extern const char * kDefaultItemTableAttrs;
class PsBoundChecker;
using ks::engine_base::PredictType;
using ks::ad_feature_index::meta_proto::FeatureSdkTable;
enum RUnifyType {
  CTR = 0,
  CVR = 1,
  DEEP_CVR = 2,
  LTV = 3,  // universe add
  SCTR = 4
};

// 注释格式: owner; 添加目的
// 不遵守同学请自己发红包 @cuixiaolong, @sunyuanshuai
enum RUnifyTag {
  UNKNOWN_TAG = 0,
  GAME_APPOINT = 1,  // zhoushuaiyin;
  AD_WATCH_TIMES = 2,  // tangweiqi;
  ISOTONIC_REGRESSION = 3,
  AD_MERCHANT_FOLLOW = 4,  // gaowei03;涨粉系数调整
  EVENT_GOODS_VIEW = 5,  // gaowei03;商品访问系数调整
  EVENT_ORDER_PAIED = 6,  // gaowei03;商品购买系数调整
  AD_LIVE_PLAYED_3S = 7,  // gaowei03;直播 p3s 系数调整
  JK_ORDER_SUBMIT = 8,  // sixianbo; 京快广告预测调整
  AD_WATCH_5_TIMES = 9,  // tangweiqi;观看 5 次
  AD_WATCH_10_TIMES = 10,  // tangweiqi;观看 10 次
  AD_WATCH_20_TIMES = 11,  // tangweiqi;观看 20 次
  AD_CONVERSION_COEFF = 12,   // lijie10; 激活率调整系数
  LPS_ECOM_COEFF = 13,   // zengdi; 直营表单率调整系数
  AD_SCTR_RESET = 14,  // lining; sctr 重调
  MERCHANT_ORDER_PAY = 15,   // fangyuan03; 小店短视频订单支付调整系数
  LPS_TX_COEFF = 16,    // dengjiaxing; 淘系表单率调整系数
  LPS_NOT_ECOM_COEFF = 17,           // dengjiaxing; 非直营表单率调整系数
  AD_REGISTER_COEFF = 18,  // zhaijianwei; 注册率调整系数
  AD_PURCHASE_ECOMM_COEFF = 19,  // xuyanyan; 综合电商付费率调整系数
  AD_COMP_CONVERSION_COEFF = 20,  // zhaijianwei; 电商激活率调整系数
  DPA_APP_INVOKED_COEFF = 21,  // cainingning; DPA 唤端调整系数
  LOWER_C1_REGISTER_CTR_THRESHOLD = 22,  // yudongjin 切一跳注册模型， 做了一个 ctr 门槛。
  LOWER_C1_MERCHANT_FOLLOW_ORDER_PAIED_THRESHOLD = 23,    // yudongjin 涨粉有订单付费门槛，roi 优化
  C1_MERCHANT_FOLLOW_ACCOUNT_RATIO = 24,  // yudongjin 涨粉 account 打折系数
  C1_ORDER_PAIED_ACCOUNT_RATIO = 25,  // yudongjin 订单支付 account 打折系数
  MANUAL_CALIBRATION = 26,  // 手动纠偏
  AD_PURCHASE_COEFF = 27,   // yuchengyuan  行业付费率调整系数
  AD_PURCHASE_APP_COEFF = 28,  // wangxin25; APP 曝光付费率调整系数
  AD_PURCHASE_CONV_COEFF = 29,  // lizhihe; 付费单出价激活付费率调整系数
  DPA_SCVR_COEFF = 30,  // linyuhao03; DPA scvr 模型调整系数
  AD_PURCHASE_APPINVOKED_COEFF = 31,  // lizhihe; 付费单出价唤端付费率调整系数
  MERCHANT_ROAS_COEFF = 32,  // chencongzheng; 小店短视频 roas 调整系数
  AD_24H_STAY_COEF = 33,  // zhangcong05; 激活 24 小时次留双出价 调整系数
  MULTI_RETRIEVAL_CXR_ADJ = 34,  // guojiangwei; 基于 ab 对某个召回通路 cvr 调整系数
  AD_PURCHASE_CONV_COEFF_DOUBLE_COL = 35,  // zhangzhaoyu; 付费单出价激活付费率（双列）调整系数
  LIVE_STREAM_PROMOTE_ORDER_PAIED_COEFF = 36,  // yechen05; 本地推直播直投 cvr 调整系数
  KWAI_PROMOTION_LOCAL_STORE_ORDER_PAIED_COEFF = 37,  // huangxin07; 本地推短视频订单 cvr 调整系数
  SMALL_SCENCE_CALIBRATE = 38,  // liubing05; 小流量场景校准
  PDD_HELP_ITEM_CONV_COEFF = 39,  // huangxin07; 拼多多流量助推 cvr 调整系数
  SPLASH_INNER_CALIBRATE = 40,  // liubing05; 开屏内循 cxr 校准
  NON_MERCHANT_LIVE_P2L_CONV_CVR_COEFF = 41,  // zhangxin29; 外循环行业直播-作品引流-激活类出价 cvr 调整系数
  NON_MERCHANT_LIVE_P2L_LPS_CVR_COEFF = 42,   // zhangxin29; 外循环行业直播-作品引流-表单类出价 cvr 调整系数
  GREDIT_GRANT_COEFF = 43,   // yuchengyuan; 外循环授信目标 cxr 校准
  EFFECTIVE_ACQUISITION_COEFF = 44,    // xuyanyan03; 外循环有效获客出价 cvr 校准
  AD_PURCHASE_IMP2PAY_COEFF = 45,  // zhangzhao06; 曝光付费模型调节系数
  AD_PURCHASE_CONV_ACCOUNT_COEFF = 46,  // lizhihe; 激活付费账户维度预估值调整系数
  AD_PURCHASE_APPINVOKED_ACCOUNT_COEFF = 47,  // lizhihe; 唤端付费账户维度预估值调整系数
  AD_ROAS_CONV_ACCOUNT_COEFF = 48,  // yangxinyong; ROI 单激活链路切分点账户维度预估值调整系数
  AD_ROAS_PURCHASE_ACCOUNT_COEFF = 49,  // yangxinyong; ROI 单付费链路切分点账户维度预估值调整系数
  AD_ROAS_CONV_PRODUCT_COEFF = 50,  // yangxinyong; ROI 单激活链路切分点产品维度预估值调整系数
  AD_ROAS_PURCHASE_PRODUCT_COEFF = 51,  // yangxinyong; ROI 单付费链路切分点产品维度预估值调整系数
  AD_PURCHASE_CLK2PAY_COEFF = 52,  // tiangeng; 短剧点击付费模型调节系数
  COMP_ECOM_CONVERSION_ACCOUNT_PRODUCT_COEFF = 53,  // zengdi; 电商激活产品 && account 粒度预估值调整系数
  COMP_ECOM_INVOKE_ACCOUNT_PRODUCT_COEFF = 54,  // zengdi; 唤端产品 && account 粒度预估值调整系数
  COMP_ECOM_CLICK2_PURCHASE_ACCOUNT_PRODUCT_COEFF = 55,  // zengdi; 综平付费产品 && account 粒度调整系数
  PRIVATE_MESSAGE_CONSULTATION_COEFF = 56,  // xuyanyan03; 私信咨询产品粒度预估值调整系数
  OUTER_HARD_CALI_CONFIG = 57,  // zhangmengxin; 外循环通用目标调节系数
  AD_EVERYDAY_STAY_COEFF = 58,  // xueerpeng; 每日留存账户维度预估值调整系数
  AD_WEEKDAY_STAY_COEFF = 59,  // xueerpeng; 七日留存账户维度预估值调整系数
  OUTER_U2U_CXR_ADJ = 60,  // wuzhibo; u2u 通路预估值调整系数
  WECHAT_GAME_USER_CALIB = 61,  // jiangnan07; 小游戏用户校准
  AD_DUANJU_PURCHASE_RATIO = 62,  // zhangzhaoyu; 短剧付费曝光打平
  GLOBAL_CXR_CALIBRATE = 63,  // yishijie; 通用模型纠偏工具
  REWARD_CVR_CALIBRATE = 64,   // songxu; 激励视频 cvr 校准
  PAIED_COURSE_ORDER_PAIED_COEFF = 65,   // wuwei03; 付费课堂订单支付 cvr 调整系数
  LEADS_SUBMIT_COEFF = 66,   // wengrunze; 私信留资 cvr 校准
  EVENT_PRIVATE_MESSAGE_SENT_COEFF = 67,   // wengrunze; 私信消息 cvr 校准
  AD_ROAS_CONV_STD_COEFF = 68,  // yangxinyong; ROI 浅待深激活切分点调整系数
  OUTER_SCENE_CALI_CONFIG = 69,  // dongyao03; 外循环分场景 ctr 校准
  AD_IAA_CONV_STD_COEFF = 70,  // wushanshan03; IAA 浅带深激活切分点调整系数
  AD_IAAP_LTV_CALIBRATE = 71,   // yangzhao07; 快小 IAAP ltv 校准
  OUTER_CTR_SOFT_QUEUE_COEFF = 72,  // zhouman; 外循环 ctr 软广队列校准
  LPS_NOT_ECOM_SOFT_COEFF = 73,  // tanyijia; 外循环 表单 软广队列校准
  OUTER_CTR_HARD_QUEUE_COEFF = 74,  // zhouman; 外循环 ctr 硬广队列校准
  AD_GAME_IAA_LTV_CALIBRATE = 75,  // gaoyuan21; iaa 游戏首 R ltv 校准
  AD_GAME_IAA_7R_LTV_CALIBRATE = 76,  // gaoyuan21; iaa 游戏 7R ltv 校准
  AD_GAME_IAP_7R_LTV_CALIBRATE = 77,   // yangzhao07; 快小 7R ltv 校准
  LPS_CLUE_NOT_ECOM_COEFF = 78,   // huwenkang03; 外循环本地表单校准
  AD_EFFECTIVE_CUSTOMER_ACQUISITION_COEFF = 79,   // litianfeng; 外循环获客单出价校准
  AD_LANDING_PAGE_FORM_SUBMITTED_COEFF = 80,   // litianfeng; 外循环获客双出价校准
  AD_GAME_IAP_7R_LTV_DEPRIORITIZE = 81,   // lichao22; 快小 7R ltv 低分段校准
  AD_GAME_IAP_1R_LTV_DEPRIORITIZE = 82,  // lichao22; 快小 首 R ltv 低分段校准
  AD_GAME_IAP_7R_LTV_ADJUST = 83,  // gaoyuan21; iap 7R ltv 打平曝光
  AD_GAME_SDK_7R_LTV_ADJUST = 84,  // gaoyuan21; sdk 7R ltv 打平曝光
  AD_BIG_GAME_1R_LTV_DEPRIORITIZE = 85,  // lichao22; 大游戏首 R ltv 低分段校准
  MULTIPLY_SCTR_INTO_CTR = 86  // jiangyuzhen03; 软硬广统一，将 sctr 乘入 ctr
};

template <class T> struct TypeChecker {};
template <> struct TypeChecker<int64_t> { typedef int64_t Type; };
template <> struct TypeChecker<float> { typedef float Type; };

struct RUnifyInfo {
  double value = 0.0;
  double original_value = 0.0;
  std::unordered_set<int32_t> cmd_id_list;
  kuaishou::ad::AdActionType s_type = kuaishou::ad::UNKNOWN_ACTION_TYPE;
  kuaishou::ad::AdActionType e_type = kuaishou::ad::UNKNOWN_ACTION_TYPE;
  RUnifyTag r_tag = UNKNOWN_TAG;
  bool has_set = false;
  void Clear();
};

struct PredictScoreMeta {
  double score = 0.0;
  int32 cmd_id;
  int32 use_counter;
  std::string cmd_name;
  bool is_bounded = false;
  int32 cmd_key_id;
  int32 r_type;
};

struct RScoreMeta {
  double score = 0.0;
  int32 use_counter = 0;
};

struct AdRankCmd {
  ~AdRankCmd() = default;
  std::string ctr_cmd = "";
  std::string cvr_cmd = "";
  std::string server_show_cvr_cmd = "";
  std::string server_show_ctr_cmd = "";
  std::string c2_order_paied_cmd = "";
  std::string live_goods_view_cmd = "";
  std::string live_order_paid_cmd = "";
  std::string live_play_3s_cmd = "";
  std::string live_server_show_play_3s_feed_cmd = "";
  std::string live_server_show_play_3s_slide_cmd = "";
  std::string live_p3s_wtr_cmd = "";
  std::string live_p3s_ltv_cmd = "";
  std::string live_audience_cmd = "";
  std::string photo2live_pay_rate_cmd = "";
  std::string c1_order_paied_cmd = "";
  std::string item_impression_wtr_cmd = "";
  std::string live_romm_stay_1m_cmd = "";
  std::string long_value_user_tag_cmd = "";

  void Clear();
};

// 调整排序 sort tag
// 如需添加联系 sunyuanshuai
enum SortTag {
  Brand = 0,
  NewGame = 1,
  FIRSTN = 2,
  KPerN = 3,  // 起量工具 N 保 K 策略；heqian, yeziqing
  Retarget = 4,  // 用户在次留等场景下强制 retarget
  MaxTag = 1024
};

enum RankSelectTag {
  RankSelectUnknown = 0,
  RankSelectInnerSoft = 1,
  RankSelectInnerHard = 2,
  RankSelectOuterLive = 3,
  RankSelectOuterSoftPhoto = 4,
  RankSelectOuterHardPhoto = 5,
  RankSelectUnify = 6
};

enum AdjustPriceTag {
  UnknownOtherPriceTag = -1,
  GspPrice = 0,
  GfpPrice = 1,
  BillingSeperate = 2,
  AdjustPriceRevenueOptimize = 3,
  AuthorDiscount = 4,
  Merchant = 5,
  MinCpm = 6,
  Minbid = 7,
  SiteDiy = 8,
  MaxPrice = 9,
  AdjustPricePriceDiscount = 10,
  ProbOne = 11,
  ProbZero = 12,
  Bottom = 13,
  RetargetPrice = 14,
  OldPriceSeparate = 15,
  Esp = 16,
  PriceSeparate = 17,
  UnifyBillingRatio = 18,
  BadPhotoTax = 19,
  RewardedRevenueOptimize = 20,
  BillingSeparateGfpPrice = 21,
  FollowPrice = 22,
  InspireLiveFeedPrice = 23,
  InspireMerchantPrice = 24,
  SmallGamePrice = 25,
  MinPriceProtection = 26,
  ADXPrice = 27,
  MinAuctionBid = 28,
  RetargetPriceDiscount = 29,
  RewardedPriceDiscount = 30,
  LowFilled = 31,
  OcpcMaxThr = 32,
  NativeMaxBound = 33,
  MinThrPrice = 34,
  MaxThrPrice = 35,
  AdjustPriceCpmProtection = 36,
  AdjustPriceProtectPrice = 37,
  OriginAuctionFinal = 38,
  AuctionFinal = 39,
  PostProcFinal = 40,
  AddReservePrice = 41,
  PecCoinEffect = 42,
  PriceWithTax = 43,
  RecruitPriceDiscount = 44,
  IndustryLivePrice = 45,
  MobileDiscount = 46,
  NobidProtect = 47,
  LiveSelectedPrice = 48,
  FtFairnessCorrectionV1 = 49,
  FtFairnessCorrectionV2 = 50,
  FanstopBillingSeperate = 51,
  CidPriceDiscount = 52,
  MiddlePagePriceDiscount = 53,
  NativeOuterPriceDiscount = 54,
  JuxingPrice = 55,
  FollowMaxPrice = 56,
  FanstopCostDiscountExp = 57,
  InnerPriceDiscount = 58,
  NewProductDiscount = 59,
  AdMerchantPriceDiscount = 60,
  LpsdeepPriceDiscount = 61,
  QudaoCluesPriceDiscount = 62,
  SmbPriceDiscount = 63,
  FanstopAuctionBidPrice = 64,
  WanheMaxPrice = 65,
  FanstopFollowPrice = 66,
  GuessYouLikePriceDiscount = 67,
  MaxBound = 68,
  MinBound = 69,
  ExploreInnerPrice = 70,
  ClientAiLiveAdRerankPrice = 71,
  ClientAiP0RerankPrice = 72,
  SelfServicePriceDiscount = 73,
  PoQuanPriceDiscount = 75,
  ReservePrice = 76,
  GameStabilityDiscount = 77,
  CidPriceDiscountV3 = 78,
  UnifyHardBillingSeparate = 79,
  UnifyNativeBS = 80,
  UnifyNativeFanstopV2NobidBS = 81,
  FanstopPrivateMessageLeadsDiscount = 82,
  AdjustPricePrivateMessage = 83,
  AdjustExplorePrice = 84,
  InnerProductEEPriceDiscount = 85,
  AdStorewideUplift = 86,
  AdjustPriceSimplePromotion = 87,
  InnerWhiteAccountDiscount = 88,
  InnerT7RoiDiscount = 89,
  AdStorewideMerchantUplift = 90,
  IAAPGameROIDiscount = 91,
  GameBigRExplore = 92,
  NewSpuPriceDiscount = 93,
  AdjustPricePrivateMessageGroup = 94,
  AdjustIncentivePrice = 95,
  UpItemsPrice = 96,
  InnerCrmEEPriceDiscount = 97,
  InnerSouthEEPriceDiscount = 98,
  NewCustomerHostingDiscount = 99,
  MatrixAppPriceDiscount = 101,
  StoreWideLiveWhiteListDiscount = 102,
  StoreWideMerchantWhiteListDiscount = 103,
  KuaiGameInspireDiscount = 104,
  InnerTieEEPriceDiscount = 105,
  FictionHotBookDiscount = 106,
  NewSpuCrowdDiscount = 107,
  ItemCardSelectedFlowDiscount = 108,
  ColdStartPrice = 109,
  LocalLifePriceDiscount = 110,
  InnerHighValuePrice = 111,
  OuterLivePriceDiscount = 112,
  InnerNewOrderDiscount = 113,
  DuanjuNativeOuterPriceDiscount = 114,
  DuanjuPriceDiscount = 115,
  InnerLivePrice = 116,
  LowTrIndustryDiscount = 117,
  InnerAdxEEOrientationDiscount = 118,
  ClientAiPhotoAdRerankPrice = 119,
  StorewideLowClevelDiscount = 120,
  InnerHighQualityDiscount = 121,
  InnerCtcvrCaliDiscount = 122,
  InnerNewGuardNDiscount = 123,
  NormalOriginCalcPriceDragon = 10000,
  NativeOriginCalcPriceDragon = 10001,
  UnifyOriginCalcPriceDragon = 10002
};

struct ExpEcpcInfo {
  double exp_ecpc_adjust_ratio = 1.0;
  EcpcAdjustTag exp_ecpc_adjust_tag = EcpcAdjustTag::Unknown;
};

struct PidBonusInfo {
  int64_t bonus_tag = 0;
  int64_t project_id = 0;
  double alpha = 0.0;
  double project_ratio = 1.0;
};

// ueq 版本 tag, 若需添加相关 tag 联系 wangguangshuai
// 添加 tag 记得同步修改 ad_server 下 ad_common 中对应枚举变量
enum UeqTag {
  InitUeq = 0,
  OldUeq = 1,
  NewUeq = 2,
  RefactorUeq = 3
};
// ueq 门槛策略 tag, 若需添加相关 tag 联系 wangguangshuai
// 添加 tag 记得同步修改 ad_server 下 ad_common 中对应枚举变量
enum SkipUeqThrTag {
  ThroughThr = 0,
  SelectAdSkipThr = 1,
  AllCreativeSkipThr = 2,
  NewCreativeSkipThr = 3,
  NewGameSkipThr = 4,
  FirstNSkipThr = 5,
  DmpCoreAudienceSkipThr = 6,
  DeepGameSkipThr = 7,
  LowNextDayUeqThr = 8,
  AutoAdmitUeqSkipThr = 9,
  AutoAdmitUeqThr = 10,
  CommonUeqSkipThr = 11,
  CommonUeqThr = 12,
  DpaSkipThr = 13,
  EcomSkipThr = 14
};

// 新产品开启准入绿色通道，通道类型
enum NewProductAdmitType {
  AdmitAllowClosed = 0,  // 无绿色通道
  DspUeqFilter = 1,
  CpmFilter = 2,
  DspMinbidFromCtr = 3,
  DspMinbidFromP3r = 4,
  DspMinbidFromCtrCvr = 5,
  DspMinbid = 6
};

enum PtdsChangeBidType {
  NoneType  = 0,
  kEcpcType = 1,
  kPerType  = 2,
  kFnType   = 3,
  kFrType   = 4,  // 外循环电商首刷出价提升
  kSrType   = 5,  // 外循环电商固定提价策略
  kMaxTag   = 100
};

enum class EcomSellerStatus {
  UNKNOWN = 0,
  NEED_PENALTY = 1,
  NEED_BONUS = 2,
  NORMAL = 3
};

struct FeatureTableData {
  std::shared_ptr<const ks::ad_feature_index::RowData> wt_photo;
  std::shared_ptr<const ks::ad_feature_index::RowData> wt_photo_dup;
  std::shared_ptr<const ks::ad_feature_index::RowData> wt_author;
  std::shared_ptr<const ks::ad_feature_index::RowData> wt_account;
  std::shared_ptr<const ks::ad_feature_index::RowData> wt_product;
  std::shared_ptr<const ks::ad_feature_index::RowData> wt_live;
  std::shared_ptr<const ks::ad_feature_index::RowData> wt_creative;
  std::shared_ptr<const ks::ad_feature_index::RowData> wt_unit;
  std::shared_ptr<const ks::ad_feature_index::RowData> wt_campaign;
  std::shared_ptr<const ks::ad_feature_index::RowData> ad_dsp_photo;
  std::shared_ptr<const ks::ad_feature_index::RowData> ad_dsp_campaign;
  std::shared_ptr<const ks::ad_feature_index::RowData> ad_dsp_unit;

  std::shared_ptr<const ks::ad_feature_index::RowData> creative;
  std::shared_ptr<const ks::ad_feature_index::RowData> unit;
  std::shared_ptr<const ks::ad_feature_index::RowData> campaign;
  std::shared_ptr<const ks::ad_feature_index::RowData> account;
  std::shared_ptr<const ks::ad_feature_index::RowData> photo;
  std::shared_ptr<const ks::ad_feature_index::RowData> live;
  std::shared_ptr<const ks::ad_feature_index::RowData> product;
  std::shared_ptr<const ks::ad_feature_index::RowData> author;
  std::shared_ptr<const ks::ad_feature_index::RowData> tube;

  std::shared_ptr<const ks::ad_feature_index::RowData> GetTableByName(const std::string& name) const;
  std::shared_ptr<const ks::ad_feature_index::RowData> GetTableByName(
      ks::ad_index_meta::proto::FeatureSdkTable table_enum) const;
  void Clear();
};

struct AdBaseNonPod {
  std::string product_name;  // account 里的 product_name
  std::string rewrite_query;  // 搜索广告改写 query
  std::string search_bidword;  // 搜索广告主买词
  std::unordered_set<int32_t> pid_tags;  // pid server 标记字段
  std::unordered_map<uint64_t, double> hc_record;
  std::vector<int32_t> crowd_tag;  // 广告命中的人群属性
  std::vector<int64_t> new_creative_tags_realtime;
  // 该广告可出的激励样式  <enum_string, enum_value>
  std::unordered_map<std::string, kuaishou::ad::RewardStyleType> reward_styles;
  std::string display_type;
  std::string reward_coin_treatment_str;
  std::string incntv_eprice_fb_ctl_key;
  std::string gear_position;
  std::string pec_coin_selec_key;
  std::string brand_level;
  std::vector<int64_t> photo_promotable_status;
  std::vector<int64_t> ad_items;
  std::vector<int64_t> kg_tag_ids;
  kuaishou::ad::AdRankTransparentInfo_SearchAppCard search_app_card_data;
  kuaishou::ad::AdCandidate_AdSearchTarget ad_search_age_gender_target;
  std::vector<int32_t> key_action_switch;
  std::string account_advertiser_risk_label;
  // 搜索点后推数据
  kuaishou::ad::SearchRecoQueryList search_reco_query_list;

  std::unordered_map<int32_t, double> suppress_hc_record;
  std::unordered_map<int32_t, double> traffic_hc_record;
  std::unordered_map<int32_t, double> industry_hc_record;
  std::unordered_map<int32_t, double> other_hc_record;
  // 券相关数据
  kuaishou::ad::coupon::X7CouponTemplate coupon_template_info;
  std::vector<double> pec_coupon_uplift_list;
  std::string playlet_name;

  // Flink ad_feature 特征生产相关复杂数据，用来替换 post-server 数据做实验，后续会删除
  std::vector<PcvrBucketItem> pcvr_bucket_vec;
  std::unordered_map<int32_t, double> other_hc_tag_score;

  void Clear();
};

struct AdxNonPod {
  std::string tag_id;
  std::string photo_uplift_tag;  //  电商素材扶持 tag
  std::string display_info;
  void Clear();
};

struct AdFanstopInfo {
  std::string item_ext_attr;  // 粉条扩展信息
  void Clear();
};

struct InnerLoopBonusInfo {
  InnerLoopBonusInfo(int64_t pro_id, int64_t bon_tag)
      : project_id(pro_id),
        bonus_tag(bon_tag) {}
  int64_t project_id = 0;
  int64_t bonus_tag = 0;
};

struct AdBonusDetail {
  AdBonusDetail(int64_t bon_tag, int64_t pro_id, double bon_0, double bon, double alf, double pro_ratio)
      : bonus_tag(bon_tag),
        bonus_project_id(pro_id),
        bonus(bon),
        alpha(alf),
        project_ratio(pro_ratio) {}
  int64_t bonus_tag = 0;
  int64_t bonus_project_id = 0;
  double bonus = 0.0;
  double alpha = 0.0;
  double project_ratio = 0.0;
};

// 多因子公式信息
struct MultiFactorInfo {
 public:
  MultiFactorInfo(std::string key, bool admit, double value)
      : key(key), admit(admit), value(value) {}
  std::string key = "";
  bool admit = false;
  double value = 0.0;
};

// 校准相关
struct AdCalibration {
 private:
  friend class AdCommon;
  std::string ad_cali_flow_tag = "x";  // 流量 tag
  std::string ad_cali_model_tag = "x";  // 模型 tag
  std::string ad_cali_auto_cpa_bid_tag = "x";  // 出价 tag
  std::string ad_calibration_tag = "";
  void Clear();
};

struct RecruitInfo {
  int64 job_id = -1;
  int64 org_id = -1;
  int32 job_category = -1;
  std::vector<int64> job_address;
};

struct UnifyUeqParams {
  double pxtr_weight = 0.0;
  double pxtr_bias = 0.0;
  double pxtr_exp = 0.0;
};

class FeatureIndexManager;
// 模块内部公用的 ad 结构
// 所有字段必须在 Clear 字段中清理
struct AdCommon {
  void Clear();
  std::string Debug();

#include "ad_common-ad_base_info.inl"  // NOLINT
#include "ad_common-ad_base_non_pod.inl"  // NOLINT
#include "ad_common-ad_bid_info.inl"  // NOLINT
#include "ad_common-ad_calibration.inl"  // NOLINT
#include "ad_common-ad_common.inl"  // NOLINT
#include "ad_common-ad_fanstop_info.inl"  // NOLINT
#include "ad_common-ad_filter_stage_info.inl"  // NOLINT
#include "ad_common-ad_heritage.inl"  // NOLINT
#include "ad_common-ad_material.inl"  // NOLINT
#include "ad_common-ad_pre_rank.inl"  // NOLINT
#include "ad_common-ad_price.inl"  // NOLINT
#include "ad_common-ad_rank_cmd.inl"  // NOLINT
#include "ad_common-ad_rank.inl"  // NOLINT
#include "ad_common-ad_reserve.inl"  // NOLINT
#include "ad_common-ad_server_show_ratio.inl"  // NOLINT
#include "ad_common-ad_statistics.inl"  // NOLINT
#include "ad_common-ad_ueq.inl"  // NOLINT
#include "ad_common-adx_non_pod.inl"  // NOLINT
#include "ad_common-coupon_info.inl"  // NOLINT
#include "ad_common-feature_table_data.inl"  // NOLINT

 private:
#include "ad_common-ad_common.private.inl"  // NOLINT

 public:
  // 搜索重定向广告
  bool is_search_retarget_ad(const std::unordered_set<int64_t>& tags) const;

  // 游戏行业重定向广告
  bool is_olp_retarget_ad(const std::unordered_set<int64_t>& tags) const;

  // 是否游戏多路重定向
  bool is_retarget_multi_tag(int64_t multi_retrieval_tag
        , uint64_t multi_overlay_tag_extend
        , uint64_t multi_overlay_tag
        , uint64_t tag_id) const;

  inline void SetAdInValid(kuaishou::log::ad::AdTraceFilterCondition condition,
                           const kuaishou::ad::AdRankNodeType ad_node_type,
                           kuaishou::ad::AdRankPointType point_stage_type,
                           kuaishou::ad::AdRankPluginType plugin_stage_type) {
    set_valid(false);
    set_filter_condition(static_cast<int64_t>(condition));
    set_node_stage_type(ad_node_type);
    set_point_stage_type(point_stage_type);
    set_plugin_stage_type(plugin_stage_type);
  }

  inline void ResetAdValid() {
    set_valid(true);
    set_filter_condition(static_cast<int64_t>(kuaishou::log::ad::UNKNOWN_AD_TRACE_FILTER_CONDITION));
    set_node_stage_type(kuaishou::ad::RANKING_TYPE);
    set_point_stage_type(kuaishou::ad::RANK_POINT_DEFAULT_TYPE);
    set_plugin_stage_type(kuaishou::ad::RANK_PLUGIN_DEFAULTTYPE);
  }

  inline void SetAdInValid(int32_t filrer_reason) {
    set_valid(false);
    set_filter_condition(filrer_reason);
  }

  inline void SetAdInValid(int32_t filrer_reason,
                           const kuaishou::ad::AdRankNodeType& ad_node_type) {
    set_valid(false);
    set_filter_condition(filrer_reason);
    set_node_stage_type(ad_node_type);
  }

  // NOTE: 从队列里过滤 ad 但不记录过滤原因，目前仅搜索场景调用
  inline void SetAdInvalidWithoutTrace() {
    set_valid(false);
    set_is_trace(false);
  }

  inline int64_t get_live_id_for_ps() const {
    return get_live_stream_id() > 0 ? get_live_stream_id() : get_live_stream_id_for_ps();
  }

  double get_predict_score(const PredictType& pt) const {
    if (Is(AdFlag::IsNativeAd)) {
      double res = 0.0;
      if (get_native_predict_score(pt, &res)) {
        return res;
      }
    }
    return get_default_predict_score(pt);
  }

  int get_predict_cmd_id(const int& pt) {
    if (!predict_score_list.contains(pt)) {
      return 0;
    }
    return predict_score_list[pt].cmd_id;
  }

  std::string get_predict_cmd_name(const int& pt) const {
    if (!predict_score_list.contains(pt)) {
      return "";
    }
    return predict_score_list[pt].cmd_name;
  }

  void set_predict_score(const int& pt, const double& score, const int32& cmd_id,
      const std::string& cmd_name) {
    auto& predict_score = predict_score_list[pt];
    // 有冲突, 打点 & 打日志
    if (predict_score.cmd_id != 0 && predict_score.cmd_id != cmd_id) {
      ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_rank", "predict_type_conflict", std::to_string(cmd_id),
                          std::to_string(predict_score.cmd_id), std::to_string(pt));
      return;
    }
    predict_score.score = PredictBound(score, cmd_name, get_product_name());
    predict_score.cmd_id = cmd_id;
    predict_score.cmd_name = cmd_name;
    predict_score.is_bounded = (std::fabs(predict_score.score - score) > DBL_EPSILON);
    // set 不改变计数
    // predict_score.use_counter = 0;
  }
  // 默认预估值填充
  void set_predict_score(int32_t rt, int32_t pt, double score, double *bound_score,
                        int32_t cmd_id, const std::string& cmdkey, int32_t cmd_key_id = 0) const;
  // 软广预估值填充
  void set_native_predict_score(int32_t rt, int32_t pt, double score, double *bound_score,
                                int32_t cmd_id, const std::string& cmdkey, int32_t cmd_key_id = 0) const;

  // 软广预估值填充, 硬广跳过
  bool set_predict_native_score(int32_t rt, int32_t pt, double score, double *bound_score,
                        int32_t cmd_id, const std::string& cmdkey, int32_t cmd_key_id = 0) const;

  // 硬广预估值填充, 软广跳过
  bool set_predict_normal_score(int32_t rt, int32_t pt, double score, double *bound_score,
                        int32_t cmd_id, const std::string& cmdkey, int32_t cmd_key_id = 0) const;

  void set_predict_embedding_attr(int32_t pet, const kuaishou::ad::ContextInfoCommonAttr *embedding_attr);
  const kuaishou::ad::ContextInfoCommonAttr* get_predict_embedding_attr(int32_t pet);
  void FillPredictScoreLog(kuaishou::ad::RankStageInfo::AdExtendInfo* ad_extend_info,
      bool keep_zero_counter = false) {
    for (auto iter = get_predict_score_list().begin(); iter != get_predict_score_list().end(); iter++) {
      if (iter->second.use_counter == 0 && !keep_zero_counter) {
        continue;
      }
      auto* pt_stat = ad_extend_info->add_predict_type_use_stat();
      pt_stat->set_predict_type(iter->first);
      pt_stat->set_use_counter(iter->second.use_counter);
      pt_stat->set_cmd_id(iter->second.cmd_id);
      pt_stat->set_score(iter->second.score);
      pt_stat->set_is_bounded(iter->second.is_bounded);
      pt_stat->set_r_type(iter->second.r_type);
      pt_stat->set_cmd_key_id(iter->second.cmd_key_id);
    }
  }

  uint64_t fill_item_mapping(kuaishou::ad::algorithm::CmdItemMapping *cmd_item_ptr) const {
    cmd_item_ptr->add_item_id(item_mapping_key_id());
    cmd_item_ptr->add_callback_event(get_cmd_item_callback_event());
    return item_mapping_key_id();
  }

  void fill_ps_item_context(kuaishou::ad::algorithm::ItemContext* item_context) const;

  void fill_ps_item_id_info(kuaishou::ad::algorithm::ItemComponentInfo* item_id_info) const;

  // engine_base/cmd_curator/cmd_curator_v2.h 使用
  bool is_skip_cmd_request() const {
    return get_enable_skip_cmd_request();
  }

  void add_sub_predict_ad(AdCommon* ad) {
    mutable_sub_predict_ad_vec()->push_back(ad);
  }

  bool SkipFilter(int32_t group_enum, int32_t rule_enum);

  // 不重复的一个 id
  uint64_t item_mapping_key_id() const;
  int64_t predict_id_for_ps() const;
  uint64_t creative_id() const;
  uint64_t unique_id() const;
  const std::string item_type() const;
  void SetAdForceRecoTag(const kuaishou::ad::AdEnum_AdForceRecoTag ad_force_tag,
                         const ks::ad_rank::kconf::AdForceRecoTagConfig::TagConf* tag_conf,
                         const google::protobuf::Map<std::string, uint32_t>& priority_conf,
                         bool is_fill_flag = true);
  void SetAdForceRecoTag(const kuaishou::ad::AdEnum_AdForceRecoTag ad_force_tag,
                         bool is_fill_flag = true);
  bool SetCpmThr(double cpm, CpmThrFromTag cpm_thr_from_tag, bool is_overwrite = false);
  int64 GetCpmThr() const;
  void MultiCpmThr(double ratio, CpmThrFromTag cpm_thr_from_tag);
  int64 GetMinbid();
  void SetMinbid(int64 param_minbid, MinbidFromTag minbid_from_tag);
  int64_t GetSecondIndustryId();
  int64_t GetFirstIndustryId();
  bool SetCpaBid(int64 new_bid, ad_base::AutoCpaBidModifyTagType modify_tag);
  bool SetAutoCpaBid(int64 new_bid, ad_base::AutoCpaBidModifyTagType modify_tag);
  bool SetAuctionBid(int64_t new_auction_bid, AuctionBidModifyTag modify_tag);
  bool SetAutoRoas(double new_auto_roas, ad_base::AutoRoasModifyTagType modify_tag);
  bool SetAutoRoasIntTag(double new_auto_roas, int64_t modify_tag);
  bool SetAutoCpaBidIntTag(int64 target_auto_cpa_bid, int64_t modify_tag);
  void set_skip_all_filter();
  void set_skip_filter(size_t filter);
  ks::platform::ItemAttr& Attr(const ItemIdx &idx) const {
    assert(attrs_ != nullptr); return attrs_->Accessor(idx);
  }
  int AttrIndex() const { assert(attr_row_ >= 0); return attr_row_; }
  bool Is(const AdFlag &flag) const { return bool_funcs.Is(flag); }

  /**
   * @brief 设置智能优惠券信息
   * @details - 引擎会自动完成排序、计费的调整和相关日志的落盘
   *          - 日志信息落于 online_join_params.ad_rank_trans_info.qpon_info
   *          - 需要在 calc_cpm_for_qpon 算子前调用
   *          - 有问题联系 @jiangyuzhen03
   *
   * @param qpon_type 智能优惠券类型
   * @param bid_qpon_ratio  出价打折系数
   *                        - 客户承担成本时 = (bid - qpon) / bid
   *                        - 平台承担成本时 = 1（否则会有 ERROR 日志）
   * @param uplift_cvr_ratio  cvr 提升比例
   * @param uplift_ctr_ratio  ctr 提升比例
   * @param cost_sharing_type  成本承担模式，默认客户承担成本
   */
  bool SetQponInfo(
    kuaishou::ad::AdEnum::QponType qpon_type,
    double bid_qpon_ratio,
    double uplift_cvr_ratio,
    double uplift_ctr_ratio,
    kuaishou::ad::AdEnum::QponCostSharingType cost_sharing_type = kuaishou::ad::AdEnum::CUSTOMER_ONLY);
  // 重置
  bool ResetQponInfo();

 private:
  friend class AdList;
  friend class FeatureIndexManager;  // chenchen13 临时添加，用来能够使用 GetValue
  friend class RankRequestParser;       // 支持直接传输 attr 时候把字符串赋值过来
  friend class ContextAndItemMockerBase;  // 支持 Unit Test
  void CopyFrom(const AdCommon *ad);
  // 构造函数对基本类型初始化为默认值
  explicit AdCommon(const AdListAttr *attrs);
  AdCommon(const AdListAttr *attrs, int64_t item_key);
  AdCommon(const AdListAttr *attrs, int64_t item_key, bool without_new_reco_result);

  AdCommon &operator=(const AdCommon &) = default;  // 仅允许内部使用
  AdCommon(const AdCommon &) = delete;              // 禁止使用
  AdCommon(AdCommon &&) noexcept = delete;          // 禁止使用
  // ====== 1. 以下为广告的基本属性字段 ======
  AdBaseNonPod base_np;  // np 字段
  ks::engine_base::ResourceIdType resource_type;
  FeatureTableData feature_table_data;
  ///// --- 图化私有化变量
  AdCalibration ad_calibration;  // 校准相关
  AdFanstopInfo ad_fanstop;   // 粉条相关字段
  AdRankCmd rank_cmd;  // 精排 cmd
  AdxNonPod adx_np;    // adx np 字段
  kuaishou::ad::AdKboxInfo ad_kbox_info;
  std::bitset<MAX_FILTER_SIZE> filter_skip_status;
  std::string industry_hc_control_key;
  std::string dnc_hc_control_key;
  std::string transfer_pair = "";
  std::unordered_map<int64_t, AdBonusDetail> bonus_detail_map;
  std::unordered_map<int64_t, double> bcb_bonus_project;          // 外循环 bcb 补贴 bonus0
  std::vector<InnerLoopBonusInfo> innerloop_bonus_info;  // 内循环补贴数据
  std::unordered_map<int64_t, double> traffic_hc_map;
  std::unordered_map<int64_t, double> suppress_hc_map;
  std::unordered_map<int64_t, double> coin_quit_rate_map;
  std::unordered_map<int64_t, double> deep_coin_quit_rate_map;
  std::unordered_map<int64_t, double> incentive_deep_reward_ratio;
  std::unordered_map<int64_t, double> incentive_deep_uplift_ctr;
  std::vector<int64_t> reward_coin_treatment_list;
  std::unordered_map<int64_t, double> incentive_invoked_uplift_cvr;
  std::unordered_map<int64_t, double> incentive_deep_uplift_cvr;
  std::vector<MultiFactorInfo> factor_info;  // 多因子信息 [ 仅命中白盒时填充 ]
  std::vector<PidBonusInfo> pid_bonus_info;
  std::vector<RecruitInfo> recruit_info_list;
  std::vector<ks::ad_base::LogicQueueType> logic_queue_type;
  ///// --- 图化私有化变量  END
  const AdListAttr *attrs_ = nullptr;
  int  attr_row_ = -1;
  AdItemBooleanFunc bool_funcs;
  std::vector<AdCommon*> sub_predict_ad_vec;  // 复用当前 ad 预估的 ad_vec
  RUnifyInfo unify_ctr_info;
  RUnifyInfo unify_cvr_info;
  RUnifyInfo unify_deep_cvr_info;
  RUnifyInfo unify_ltv_info;
  RUnifyInfo unify_sctr_info;
  absl::flat_hash_map<std::string, double> calibrate_value;
  std::string offcali_cvr_modelstart;
  std::string offcali_cvr_modelend;
  std::unordered_set<int32_t> req_cmd_ids;  // 请求的 cmd_id 集合
  std::unordered_map<int32_t, int32_t> req_cmd_key_ids;  // 请求的 cmd_key_id -> cmd_id 集合
  mutable absl::flat_hash_map<int, PredictScoreMeta> predict_score_list;
  mutable absl::flat_hash_map<int, PredictScoreMeta> native_predict_score_list;
  mutable absl::flat_hash_map<int32_t, RScoreMeta> r_score_list;
  mutable absl::flat_hash_map<int32_t, RScoreMeta> native_r_score_list;

  int64_t GetIntValue(ks::ad_index_meta::proto::FeatureSdkTable table, const std::string& field) const;
  bool GetBoolValue(ks::ad_index_meta::proto::FeatureSdkTable table, const std::string& field) const;
  double GetDoubleValue(ks::ad_index_meta::proto::FeatureSdkTable table, const std::string& field) const;
  float GetFloatValue(ks::ad_index_meta::proto::FeatureSdkTable table, const std::string& field) const;
  std::string GetStringValue(ks::ad_index_meta::proto::FeatureSdkTable table, const std::string& field) const;
  std::vector<int64_t> GetIntListValue(ks::ad_index_meta::proto::FeatureSdkTable table,
                                       const std::string& field) const;
  std::vector<float> GetFloatListValue(ks::ad_index_meta::proto::FeatureSdkTable table,
                                       const std::string& field) const;
  std::vector<double> GetDoubleListValue(ks::ad_index_meta::proto::FeatureSdkTable table,
                                         const std::string& field) const;
  std::vector<std::string> GetStringListValue(ks::ad_index_meta::proto::FeatureSdkTable table,
                                              const std::string& field) const;
  template <typename T>
  T GetValue(ks::ad_index_meta::proto::FeatureSdkTable table_enum, const std::string& field) const;

  bool get_native_predict_score(const PredictType& pt, double* res) const {
    if (!native_predict_score_list.contains(pt)) {
      return false;
    }
    auto& native_predict_score = native_predict_score_list[static_cast<int>(pt)];
    native_predict_score.use_counter++;
    *res = native_predict_score.score;
    return true;
  }

 public:
  double get_default_predict_score(const PredictType& pt) const {
    if (!predict_score_list.contains(pt)) {
      return 0.0;
    }
    auto& predict_score = predict_score_list[static_cast<int>(pt)];
    predict_score.use_counter++;
    return predict_score.score;
  }

  void SetUnifySctr(double r,
                    kuaishou::ad::AdActionType start,
                    kuaishou::ad::AdActionType end,
                    int32_t cmd_id_1 = 0) {
    auto *sctr = mutable_unify_sctr_info();
    sctr->value = r;
    sctr->original_value = r;
    sctr->s_type = start;
    sctr->e_type = end;
    if (cmd_id_1 != 0) {
      sctr->cmd_id_list.insert(cmd_id_1);
    }
  }
  void AdjustUnifyCtrValue(double r) {
    mutable_unify_ctr_info()->value = r;
  }
  void SetUnifyCtr(double r,
              kuaishou::ad::AdActionType start,
              kuaishou::ad::AdActionType end,
              int32_t cmd_id_1 = 0,
              int32_t cmd_id_2 = 0) {
    auto *ctr = mutable_unify_ctr_info();
    ctr->value = r;
    ctr->original_value = r;
    ctr->s_type = start;
    ctr->e_type = end;
    if (cmd_id_1 != 0) {
      ctr->cmd_id_list.insert(cmd_id_1);
    }
    if (cmd_id_2 != 0) {
      ctr->cmd_id_list.insert(cmd_id_2);
    }
  }
  void AdjustUnifyCvrValue(double r) {
    mutable_unify_cvr_info()->value = r;
  }
  void SetUnifyCvr(double r,
              kuaishou::ad::AdActionType start,
              kuaishou::ad::AdActionType end,
              int32_t cmd_id_1 = 0,
              int32_t cmd_id_2 = 0) {
    auto *cvr = mutable_unify_cvr_info();
    cvr->value = r;
    cvr->original_value = r;
    cvr->s_type = start;
    cvr->e_type = end;
    if (cmd_id_1 != 0) {
      cvr->cmd_id_list.insert(cmd_id_1);
    }
    if (cmd_id_2 != 0) {
      cvr->cmd_id_list.insert(cmd_id_2);
    }
  }

  void SetUnifyDeepCvr(double r,
              kuaishou::ad::AdActionType start,
              kuaishou::ad::AdActionType end,
              int32_t cmd_id = 0) {
    auto *deep_cvr = mutable_unify_deep_cvr_info();
    deep_cvr->value = r;
    deep_cvr->original_value = r;
    deep_cvr->s_type = start;
    deep_cvr->e_type = end;
    deep_cvr->has_set = true;
    if (cmd_id != 0) {
      deep_cvr->cmd_id_list.insert(cmd_id);
    }
  }

  inline void SetEcpmUpperBound(int64_t value, int64_t tag) {
    set_ecpm_upper_bound(value);
    set_ecpm_upper_bound_tag(tag);
  }

  inline void SetEcpmLowerBound(int64_t value, int64_t tag) {
    set_ecpm_lower_bound(value);
    set_ecpm_lower_bound_tag(tag);
  }

  inline bool AddEcpmAdjust(int64_t value, int64_t tag) {
    return append_ecpm_adjust_list(value) &&
           append_ecpm_adjust_tag_list(tag);
  }

  inline bool ResetEcpmAdjust(int64_t capacity = 0) {
    return reset_ecpm_adjust_list(capacity) &&
           reset_ecpm_adjust_tag_list(capacity);
  }

  void SetConstraintInfo(double r, kuaishou::ad::AdActionType action_type) {
    set_constraint_action_type(action_type);
    set_constraint_r(r);
  }

  void BoostUnifyEcpmRatio(double ratio) {
    set_unify_ecpm_ratio(get_unify_ecpm_ratio() * ratio);
  }

  double PredictBound(const double& ori_score, const std::string& cmd_key,
                      const std::string& product_name) const;

  inline void SetUnifyLtv(double r, kuaishou::ad::AdActionType start,
              kuaishou::ad::AdActionType end,
              int32_t cmd_id = 0) {
    auto *ltv = mutable_unify_ltv_info();
    ltv->value = r;
    ltv->original_value = r;
    ltv->s_type = start;
    ltv->e_type = end;
    if (cmd_id != 0) {
      ltv->cmd_id_list.insert(cmd_id);
    }
  }

  void ResetUnifyCxr() {
    mutable_unify_cvr_info()->Clear();
    mutable_unify_ctr_info()->Clear();
  }

  double GetInnerLiveRoas7days() const;
  void SetSearchSuggestPos(int32_t pos, SearchPosTag pos_tag);

  void AppendCaliFlowTag(const std::string& tag);
  void AppendCaliModelTag(const std::string& tag);
  void AppendCaliAutoCpaBidTag(const std::string& tag);

  void SetHcTagScore(HiddenCostType hc_type, const std::vector<int64_t>& hc_tag,
                     const std::vector<double>& hc_tag_score);
  void SetHcScore(HiddenCostType hc_type, int32 hc_tag, double hc_score);
  void SetHcScore(double new_hc_score);
  int64 GetBonusCpm() const;
  int64 GetCpm() const;
  void SetBonusCpm(int64 new_cpm, BonusCpmTag cpm_thr_from_tag);
  void SetBonusCpm(int64 new_cpm, BonusCpmTag cpm_thr_from_tag, int64 b_project_id);
  // NOTE(guojiangwei) 存在反复修改 rank_benefit 但不修改 bonus_cpm
  // 的情况，后面的逻辑会覆盖前面
  int64 GetRankBenefit() const;
  // 策略正排相关接口
  void SetRowData(std::shared_ptr<const ks::ad_feature_index::RowData> row, const std::string& table);
  void SetRowData(std::shared_ptr<const ks::ad_feature_index::RowData> row,
                  ks::ad_index_meta::proto::FeatureSdkTable table_enum);
  int64_t GetFeaKeyByTable(const std::string& name) const;
  int64_t GetFeaKeyByTable(ks::ad_index_meta::proto::FeatureSdkTable table_enum) const;
  // 是否统一计算 ecpm
  inline bool IsUnifiedEcpm() const {
    if (get_ad_source_type() == kuaishou::ad::ADX) {
      return false;
    }
    return get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP ||
           get_bid_type() == kuaishou::ad::AdEnum::MCB;
  }

  // 曝光系数填充接口
  inline void SetUnifyServerClientShowRate(double value, int tag) {
    set_unify_server_client_show_rate(value);
    set_unify_server_client_show_rate_tag(tag);
  }

  // deep_cvr 准入标接口
  inline void SetHasDeepCvr(bool value, int tag) {
    set_has_deep_cvr(value);
    set_has_deep_cvr_tag(tag);
  }

  bool CheckType(RUnifyType type,
                  kuaishou::ad::AdActionType start,
                  kuaishou::ad::AdActionType end) {
    switch (type) {
      case CTR:
        return (get_unify_ctr_info().s_type == start && get_unify_ctr_info().e_type == end);
      break;
      case CVR:
        return (get_unify_cvr_info().s_type == start && get_unify_cvr_info().e_type == end);
        break;
      case DEEP_CVR:
        return (get_unify_deep_cvr_info().s_type == start && get_unify_deep_cvr_info().e_type == end);
        break;
      case LTV:
        return (get_unify_ltv_info().s_type == start && get_unify_ltv_info().e_type == end);
        break;
      default:
        return false;
    }
  }
  // 此接口为开屏 RTB 使用，如需要联系 wangjiabin05 lining
  void ModifySplashRtbUnifyCtrPv(RUnifyTag r_tag,
                                 double server_show_client_ratio,
                                 double upper = 1.0,
                                 double lower = 0.0);

  void ModifyUnifyInfoLinear(RUnifyType r_type,
                        kuaishou::ad::AdActionType start,
                        kuaishou::ad::AdActionType end,
                        RUnifyTag r_tag,
                        double k = 1.0, double b = 0.0, double upper = 1.0, double lower = 0.0);

  // 此接口用于浅带深系数调权
  void ModifyUnifyInfoUnLinear(RUnifyType r_type,
                        kuaishou::ad::AdActionType start,
                        kuaishou::ad::AdActionType end,
                        RUnifyTag r_tag,
                        double k = 1.0, double b = 0.0, double e = 1.0,
                        double upper = 1.0, double lower = 0.0);

  void ModifyUnifyInfoLinear(RUnifyType r_type,
                        kuaishou::ad::AdActionType start,
                        kuaishou::ad::AdActionType end,
                        UniverseRUnifyTag r_tag,
                        double k = 1.0, double b = 0.0, double upper = 1.0, double lower = 0.0);


  const absl::flat_hash_map<int, PredictScoreMeta>&  GetPredictScoreList() {
    return get_predict_score_list();
  }

  void CopyFromPredictScore(AdCommon* ad, bool enable_copy_embedding_attr) {
    *mutable_req_cmd_ids() = ad->get_req_cmd_ids();
    *mutable_req_cmd_key_ids() = ad->get_req_cmd_key_ids();
    *mutable_predict_score_list() = ad->get_predict_score_list();
    *mutable_r_score_list() = ad->get_r_score_list();
    *mutable_native_predict_score_list() = ad->get_native_predict_score_list();
    *mutable_native_r_score_list() = ad->get_native_r_score_list();
    if (enable_copy_embedding_attr) {
      *mutable_predict_embedding_attr_list() = ad->get_predict_embedding_attr_list();
    }
  }

  // 替换 creative 与 queue type 前需要调用此方法
  void SetToBeReplaced() {
    set_is_ad_replaced(true);
    set_replaced_creative_id(get_creative_id());
    set_replaced_ad_queue_type(get_ad_queue_type());
  }

  void addReqCmdIds(int32_t cmd_id) {
    mutable_req_cmd_ids()->insert(cmd_id);
  }

  void add_cmd_ids(int32_t cmd_id, int32_t cmd_key_id = 0) {
    mutable_req_cmd_ids()->insert(cmd_id);
    mutable_req_cmd_key_ids()->insert({cmd_key_id, cmd_id});
  }

  bool IsNeedCalibration(RUnifyType r_type, int32_t cmd_id);

  void ExcuteCalibration(RUnifyType r_type, double cali_rate);
  void ExcuteCalibration(RUnifyType r_type, double p_val, double p_coef);
  void ShelfMerchantExcuteCalibration(RUnifyType r_type, double cali_rate);
  void ShelfMerchantExcuteCalibrationNew(RUnifyType r_type, double cali_rate);
  void ShelfLiveExcuteCalibration(RUnifyType r_type, double cali_rate);

  std::string GetOnlineCalibrationKey(int64_t page_id, int32_t cmd_id, int key_part_num);
  std::string GetOfflineCalibrationKey(int64_t page_id, std::string modelstart, std::string modelend);
  void SetCalibrateValue(const absl::flat_hash_map<std::string, double>& score_map) {
    calibrate_value = std::move(score_map);
    return;
  }
  double GetCalibrateValue(const std::string& key) {
    if (get_calibrate_value().find(key) == get_calibrate_value().end()) {
      return 0;
    } else {
      return calibrate_value[key];
    }
  }
  double GetOfflineCalibrateValue(const std::string& key) {
    if (get_calibrate_value().find(key) == get_calibrate_value().end()) {
      return 1.0;
    } else {
      return calibrate_value[key];
    }
  }

  int GetAdMonitorType() const {
    if (Is(AdFlag::is_inner_fanstop)) {
      return kuaishou::ad::AdEnum_AdMonitorType_FANSTOP_INNER;
    } else if (Is(AdFlag::is_new_inner_fanstop)) {
      return kuaishou::ad::AdEnum_AdMonitorType_FANSTOP_INNER_V2;
    }
    if (Is(AdFlag::is_inner_loop_ad)) {
      return kuaishou::ad::AdEnum_AdMonitorType_INNER_LOOP_DEFAULT;
    }
    return kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT;
  }
};

// 广告比较器
typedef std::function<bool(const AdCommon*, const AdCommon*)> AdComparator;
typedef std::function<bool(const AdCommon*)> AdSelector;
// 广告队列结构；不直接使用 std::vector，原因是需要提供快速的删除和排序功能
// 删除广告的话将 AdCommon 的 valid 置为 false，然后调用 Compact
class AdList {
 public:
  AdList();
  explicit AdList(int64_t list_size);
  ~AdList();

  // 获取一个 AdCommon 变量；广告数超过 list_size 时 返回 nullptr
  AdCommon* Append();
  AdCommon* Append(int64_t item_key);
  AdCommon* AppendWithOutNew(int64_t item_key);
  // 删除 valid == false 的广告
  void Compact();
  std::string DebugString();

  // 对广告队列排序
  void Sort(const AdComparator& comparator);
  void StableSort(const AdComparator& comparator);

  // 对广告队列随机 shuffle
  void RandomShuffle();
  void RandomShuffleFromIndex(size_t index);

  // 广告队列取 topN；删掉多余的广告；保证调用完成后一定有序
  void TopN(const AdComparator& comparator, size_t n);

  // 移除 [position, size) 的数据
  void Erase(int position,
             kuaishou::log::ad::AdTraceFilterCondition a = kuaishou::log::ad::AdTraceFilterCondition::DROP_DOWN_DSP_AD_LIST_FILTER,  // NOLINT
             kuaishou::ad::AdRankNodeType b = kuaishou::ad::AdRankNodeType::RANK_SERVER_PREPARE_TYPE,
             kuaishou::ad::AdRankPointType c = kuaishou::ad::AdRankPointType::RANK_POINT_DEFAULT_TYPE,
             kuaishou::ad::AdRankPluginType d = kuaishou::ad::AdRankPluginType::RANK_PLUGIN_DEFAULTTYPE);

  // 直播队列删除引流和直投 quota 以外的元素
  void LiveSeparateErase(int p2l_quota, int direct_live_quota,
      int esp_mobile_live_quota, int mobile_hard_live_roi_quota,
      int* p2l_num, int* direct_live_num, int* esp_mobile_live_num, int* non_esp_mobile_live_num,
      int* drop_cnt, bool p2l_remain_to_live, bool remain_to_p2l, bool enable_adjust_mobile_hard_live_quota,
      kuaishou::log::ad::AdTraceFilterCondition *last_filter_condition,
      kuaishou::log::ad::AdTraceFilterCondition a = kuaishou::log::ad::AdTraceFilterCondition::DROP_DOWN_DSP_AD_LIST_FILTER,  // NOLINT
      kuaishou::ad::AdRankNodeType b = kuaishou::ad::AdRankNodeType::RANK_SERVER_PREPARE_TYPE,
      kuaishou::ad::AdRankPointType c = kuaishou::ad::AdRankPointType::RANK_POINT_DEFAULT_TYPE,
      kuaishou::ad::AdRankPluginType d = kuaishou::ad::AdRankPluginType::RANK_PLUGIN_DEFAULTTYPE);

  // 清空所有广告数据
  void Clear();

  // 元素置换
  void Swap(int a, int b);

  template <typename T>
  void ForEach(T&& handler) {
    std::for_each(ad_list_.begin(), ad_list_.end(), std::forward<T>(handler));
  }

  // 获取所有广告信息
  std::vector<AdCommon*>& Ads() { return ad_list_; }
  const std::vector<AdCommon*>& Ads() const { return ad_list_; }

  int32_t GetInnerLoopAdSize();
  int32_t GetOuterLoopAdSize();

  // 获取没有删除过的广告 list
  std::vector<AdCommon*>& FullAds() { return ad_full_list_; }
  const std::vector<AdCommon*>& FullAds() const { return ad_full_list_; }

  // 获取第 N 条广告
  // 需要保证访问前，队列做过 Compact；这样队列中不存在 null 的广告
  // i 必须在 [0, Size) 之间，否则返回 nullptr
  AdCommon* At(int i);
  const AdCommon* At(int i) const;

  // 广告队列大小
  size_t Size() const { return ad_list_.size(); }
  const AdCommon* GetFirstAdCommonByItemId(uint64_t item_id);
  size_t capacity() const { return capacity_; }
  void InitAdTable(const AdListAttr *attrs) {
    attrs_ = attrs;
  }
  void Add(AdCommon *ptr);
  // ele 为 nullptr 时全部迁移有效的 ad, 否则只迁移 cond == ture 的
  void MoveIf(AdList *src, const AdSelector &cond);
  AdCommon* DeepCopy(const AdCommon *src);
  AdCommon* DeepCopy(const AdCommon *src, int64_t item_key);

 private:
  const AdListAttr *attrs_ = nullptr;       //
  size_t capacity_ = 0;                     // 这个队列的最大容量
  size_t available_index_ = 0;              // 可以被使用的广告数组下标
  std::vector<AdCommon *> ad_list_;         // 保存广告数据指针，便于排序和取 topN
  std::vector<AdCommon *> ad_full_list_;    // 保存广告数据指针，用于保存全量的数据，不进行删除
  bool item_id_2_ad_common_init_ = false;
  std::unordered_map<uint64_t, std::vector<const AdCommon *>> item_id_2_ad_common_;
  uint64_t ad_cnt_;
  int32_t inner_ad_full_size_ = -1;
};

}  // namespace ad_rank
}  // namespace ks
