#include "teams/ad/ad_rank/common/context_data.h"

#include <algorithm>
#include <unordered_set>
#include <functional>
#include <random>

#include "absl/strings/substitute.h"
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "base/time/timestamp.h"
#include "gflags/gflags_declare.h"
#include "google/protobuf/repeated_field.h"
#include "ks/base/abtest/single_file_dynamic_config.h"
#include "ks/serving_util/dynamic_config.h"
#include "ks/util/json.h"
#include "ks/base/abtest/abtest_instance.h"
#include "nlohmann/json.hpp"
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_base/src/attrs/fanstop_common_attr.h"
#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_base/src/common/deploy_utils.h"
#include "teams/ad/ad_rank/default/framework/bonus/calc_cpm_bonus.h"
#include "teams/ad/ad_base/src/common/os_version.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "teams/ad/ad_base/src/util/check_reco_request_type.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_rank/utils/fanstop_util/fans_request_util.h"
#include "teams/ad/ad_rank/utils/utility/ksn_util.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_base/src/common/multi_retrieval_tag.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/engine_base/universe/rank/utils/universe_media_cpm_bound_realtime.h"
#include "teams/ad/ad_base/src/container/singleton.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/engine_base/data_stability/depend_data_level.h"
#include "teams/ad/ad_rank/common/fanstop_context/fanstop_session_data.h"
#include "teams/ad/ad_rank/common/reco_manager/reco_manager.h"
#include "teams/ad/ad_rank/utils/fanstop_util/data_flow/candidate_util/outer_loop_live_candidate_util.h"
#include "teams/ad/ad_base/src/kess/client_helper.h"
#include "teams/ad/engine_base/search/util/card_style/card_style_utiils.h"
#include "teams/ad/ad_rank/default/params/auction_param.h"
#include "teams/ad/ad_rank/default/params/native_unify_filter_params.h"
#include "teams/ad/ad_rank/default/params/native_unify_auction_params.h"
#include "teams/ad/ad_rank/default/params/native_unify_calc_params.h"

DECLARE_int32(ksp_group_deploy_type);
DECLARE_bool(is_universe);
DEFINE_bool(is_adrank_offline_diff_test, false, "is ad rank server offline diff test service");
DEFINE_bool(open_chain_log, false, "open chain log");
using ks::ad_target::multi_retr::RetrievalTag;

namespace ks {
namespace ad_rank {
#define _Attr_(v) #v
const char * ContextData::attr_names_[Common::Attr::MAX_ATTR_NUM] = { ALL_COMMON_ATTRS };
#undef _Attr_

ContextData::ContextData() {
  attrs_.fill(nullptr);
}

void ContextData::InitGlobalAdTable() {
  common_w_->SetPtrCommonAttr(
      kDefaultItemTableAttrs,
      std::make_shared<AdListAttr>(common_w_->GetOrInsertDataTable(kDefaultItemTableName)));
  const AdListAttr* table_attrs = common_w_->GetPtrCommonAttr<AdListAttr>(kDefaultItemTableAttrs);
  ad_list.InitAdTable(table_attrs);
  fanstop_ad_list.InitAdTable(table_attrs);
  live_ad_list.InitAdTable(table_attrs);
  native_ad_list.InitAdTable(table_attrs);
}

void ContextData::SetBonusCpmTagMonitor(const std::string& name,
                                        int64 bonus_cpm,
                                        int64 bonus_cpm_tag,
                                        int64 bonus_cpm_project_id) {
  // 采样 20%
  if (get_llsid() % 100 < 20) {
    // 记录 bonus_cpm 的绝对值
    dot_perf->Interval(bonus_cpm,
                       absl::Substitute("ad.ad_rank.benefit.bonus_cpm_$0", name),
                       absl::StrCat(bonus_cpm_tag),
                       pos_manager_base.GetRequestAppId(),
                       absl::StrCat(pos_manager_base.GetInteractiveForm()),
                       absl::StrCat(bonus_cpm_project_id));
  }
}

void ContextData::ExceptionBonusMonitor(int64 industry_id, int64 bonus_cpm, int64 bonus_cpm_tag) {
  if (get_llsid() % 100 < RankKconfUtil::bonusExceptionMonitorRatio()) {
    dot_perf->Interval(bonus_cpm,
            "ad.ad_rank.benefit.bonus_cpm_exception",
            absl::Substitute("$0_$1", industry_id, bonus_cpm_tag),
            pos_manager_base.GetRequestAppId(),
            absl::StrCat(pos_manager_base.GetInteractiveForm()));
  }
}

static const std::unordered_set<kuaishou::log::ad::AdTraceFilterCondition> invalid_filter_set{
    kuaishou::log::ad::AdTraceFilterCondition::ADX_RETRIEVAL_RESULTS,
    kuaishou::log::ad::AdTraceFilterCondition::LUCKY_ONE,
    kuaishou::log::ad::AdTraceFilterCondition::FRONT_LUCKY_ONE};

void ContextData::RecordLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition, bool is_hard) {
  if (invalid_filter_set.count(condition)) {
    return;
  }
  if (is_hard) {
    *mutable_last_filter_condition() = condition;
  } else {
    *mutable_soft_last_filter_condition() = condition;
  }
}

void ContextData::RecordSoftLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition) {
  if (!invalid_filter_set.count(condition)) {
    *mutable_soft_last_filter_condition() = condition;
  }
}

void ContextData::LogInfo(int64_t total_cost_us,
                          const std::unordered_map<std::string, int64>& nodes_cost) {
  int32_t universe_log_freq = SPDM_universeLogInfoFreq();
  int64_t pos_id = 0;
  const auto& ad_request = get_rank_request()->ad_request();
  if (ad_request.universe_ad_request_info().imp_info().size() > 0) {
    pos_id = ad_request.universe_ad_request_info().imp_info(0).position_id();
  }
  std::string nodes_cost_str;
  for (auto& kv : nodes_cost) {
    absl::SubstituteAndAppend(&nodes_cost_str, " $0_cost:$1us", kv.first, kv.second);
  }

  LOG_EVERY_N(INFO, universe_log_freq) << "AdRank user_id:" << get_user_id()
                                       << "|llsid:" << get_llsid()
                                       << "|device_id:" << ad_request.ad_user_info().device_id()
                                       << "|app_id:" << ad_request.universe_ad_request_info().app_id()
                                       << "|pos_id:" << pos_id
                                       << "|req_ad_num:" << get_rank_request()->ad_candidate_size()
                                       << "|res_ad_num:" << get_rank_response()->ad_rank_result_size()
                                       << "|time_cost:" << total_cost_us / 1000
                                       << "|node_cost:" << nodes_cost_str;
}
// 增加自动生成的 dragon 声明、实现拆分的函数
#include "context_data-context_data.cc.inl"    // NOLINT

void ContextData::PrepareInitAttr(const kuaishou::ad::AdRequest& ad_request,
    ks::platform::MutableRecoContextInterface* context) {
  attrs_.fill(nullptr);
  common_w_ = context;
  common_r_ = context;
  Json extra_ctx(StringToJson(ad_request.extra_request_ctx()));
  int64_t tmp_llsid = extra_ctx.GetInt("llsid", -1);
  uint64_t tmp_user_id = ad_request.ad_user_info().id();
  bool dsl_clear_unlogin_uid = SPDM_enableDSLClearUnloginUserId();
  if (!dsl_clear_unlogin_uid) {
    common_w_->SetIntCommonAttr("user_id_for_ab_param", tmp_user_id);
  }
  if (ad_request.ad_user_info().is_unlogin_user()) {
    spdm_ctx.Reset(
        tmp_llsid, 0, ad_request.ad_user_info().device_id(), ks::AbtestBiz::AD_DSP,
        ad_request.ad_user_info().abtest_mapping_id());
    if (dsl_clear_unlogin_uid) {
      common_w_->SetIntCommonAttr("user_id_for_ab_param", 0);
    }
  } else {
    spdm_ctx.Reset(
        tmp_llsid, tmp_user_id, ad_request.ad_user_info().device_id(), ks::AbtestBiz::AD_DSP,
        ad_request.ad_user_info().abtest_mapping_id());
    if (dsl_clear_unlogin_uid) {
      common_w_->SetIntCommonAttr("user_id_for_ab_param", tmp_user_id);
    }
  }
  // 统一注册以前的成员变量
  #include "context_data-context_data.init.extra"  // NOLINT
  // 复杂 kconf 的 shared_ptr 注册到 dragon context 中，即可通过 get 函数获取
  Accessor(Common::Attr::ctcvr_explore_ocpc_white_set)
      .SetPtrValue(0, RankKconfUtil::ctcvrSkipCpmThdOcpcActionType());
  Accessor(Common::Attr::ctcvr_explore_ocpc_white_event_set)  // NOLINT
      .SetPtrValue(0, RankKconfUtil::ctcvrSkipCpmThdOcpcActionTypeEvent());
  Accessor(Common::Attr::ocpc_action_type_ltv_map)
      .SetPtrValue(0, RankKconfUtil::OcpcActionTypeLtvMap());
  Accessor(Common::Attr::bonus_split_test_project)
      .SetPtrValue(0, RankKconfUtil::bonusSplitTestProjectWhiteList());
  Accessor(Common::Attr::fanstop_cvr_threshold_whitelist)
      .SetPtrValue(0, RankKconfUtil::fanstopCvrThresholdWhitelist());
  Accessor(Common::Attr::fanstop_brand_base_profit_rate)
      .SetPtrValue(0, RankKconfUtil::fanstopBrandBaseProfitRate());
  set_llsid(tmp_llsid);
  set_user_id(tmp_user_id);
  set_start_ts(base::GetTimestamp());
  set_current_timestamp_nodiff(utility::GetTimestampNoDiff());
  ExtractAttrFromAdRequest(ad_request);
}

void ContextData::PrepareInitAttrForUT(ks::platform::MutableRecoContextInterface* context) {
  attrs_.fill(nullptr);
  common_w_ = context;
  common_r_ = context;
  spdm_ctx.Reset(111000, 2743261703, "ad_request.ad_user_info().device_id()",
    ks::AbtestBiz::AD_DSP);
  // 统一注册以前的成员变量
  #include "context_data-context_data.init.extra"  // NOLINT

  dot_perf = new ks::ad_base::Dot("test_ut", 1001);
  set_start_ts(base::GetTimestamp());
}

void ContextData::ExtractAttrFromAdRequest(const kuaishou::ad::AdRequest& ad_request) {
  std::string app_id = ad_request.universe_ad_request_info().app_id();
  Accessor(Common::Attr::app_id).SetStringValue(0, std::move(app_id));
  Accessor(Common::Attr::browse_type).SetIntValue(0,
      ad_request.reco_request_info().browse_type(), false, false);
  Accessor(Common::Attr::page_size).SetIntValue(0,
      ad_request.page_size(), false, false);
  Accessor(Common::Attr::ad_request_times).SetIntValue(0,
      ad_request.ad_user_session_info().ad_request_times(), false, false);
}

bool ContextData::Initialize(const kuaishou::ad::AdRequest& ad_request) {
  set_for_test(ks::ad_base::AdKessClient::Instance().IsTestEnv());
  if (SPDM_switchPosManagerInit() < 2) {
    pos_manager_base.Initialize(ad_request);
  } else {
    pos_manager_base.Initialize(common_r_);
  }
  dot_perf = new ks::ad_base::Dot(
    ad_request.product(), pos_manager_base.GetAdRequestType(),
    pos_manager_base.GetInteractiveForm(), pos_manager_base.GetSubPageId());
  if (dot_perf->Enable() && SPDM_switchPosManagerInit() == 1) {
    pos_manager_base.DiffAttr(common_r_, dot_perf);
  }
  set_page_id(pos_manager_base.GetMediumPageId());
  set_sub_page_id(pos_manager_base.GetSubPageId());
  mutable_ps_wait_once()->clear();
  set_native_rank_candidate(google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::SampleSpec>(
          mutable_rpc_arena()));
  set_context(google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::Context>(
          mutable_rpc_arena()));
  set_inner_native_context(google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::Context>(
          mutable_rpc_arena()));
  set_inner_normal_context(google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::Context>(
          mutable_rpc_arena()));
  set_outer_normal_context(google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::Context>(
          mutable_rpc_arena()));

  (*mutable_ad_list_map()).insert({"native_ad", mutable_native_ad_list()});
  (*mutable_ad_list_map()).insert({"normal_fanstop", mutable_fanstop_ad_list()});
  InitGlobalAdTable();
  set_predict_process_start_tm(0);
  set_user_group_tag(ad_request.ad_user_info().user_group_tag());
  // 计算 llsid
  if (get_llsid() % 100 < RankKconfUtil::monitorRecordRatio()) {
    set_monitor_sample(true);
  }
  auto ab_init_st = base::GetTimestamp();
  set_is_search_request(IsSearchTraffic());
  set_is_search_good_card(IsSearchGoodCard());
  auto ab_init_ed = base::GetTimestamp();

  set_serialized_reco_user_info(ad_request.reco_user_info().SerializeAsString());
  if (SPDM_enable_rank_industry_explore(spdm_ctx) || SPDM_enable_industry_user_explore_jiaotong(spdm_ctx)) {
    set_industry_explore_flow(get_rank_request()->industry_explore_flow());
  }
  set_is_default_deploy(IsDefaultDeploy());

  set_enable_new_bonus_diff_compare(get_is_default_deploy() &&
      SPDM_enable_new_bonus_diff_compare(spdm_ctx) &&
      ad_base::AdRandom::GetInt(1, 10000) <= RankKconfUtil::rankBonusDiffCompareSample());

  if (get_is_default_deploy()) {
    Accessor(Common::Attr::reco_manager)
        .SetPtrValue(0, std::make_shared<RecoManager>(SPDM_enable_opt_reco_handler_order(spdm_ctx)));
  }

  std::string app_id = pos_manager_base.GetRequestAppId();
  set_lt_experience_cpm_ratio(SPDM_lt_experience_cpm_ratio(spdm_ctx));
  if (app_id == "kuaishou_nebula") {
    set_lt_experience_cpm_ratio(SPDM_lt_experience_cpm_ratio_nebula(spdm_ctx));
  }

  if (get_is_default_deploy()) {
    if (get_fans_session_data() == nullptr) {
      Accessor(Common::Attr::fans_session_data)
          .SetPtrValue(0, std::make_shared<FansTopSessionData>());
    }
    auto *fans = mutable_fans_session_data();
    if (fans == nullptr)
      return false;
    bool is_valid_fanstop_request = false;
    if (SPDM_switchCommonAttrOfFanstopSessionData()) {
      is_valid_fanstop_request = fans->Initialize(common_r_, nullptr);
    } else {
      is_valid_fanstop_request = fans->Initialize(
        ad_request, get_rank_request()->request_fanstop_ext(), &spdm_ctx);
      if (dot_perf->Enable()) {
        // 检查 fanstop session data 的一致性 , 为下线 fanstopSessionData 做准备
        fans->Initialize(common_r_, dot_perf);
      }
    }
    if (!is_valid_fanstop_request) {
      return false;
    }
    set_enable_innerloop_bonus_update(SPDM_enable_innerloop_bonus_update(spdm_ctx));
    mutable_innerloop_bonus_data()->Initialize();
    mutable_inner_loop_ad_rank_infos()->Initialize(this);
    mutable_outer_loop_ad_rank_infos()->Initialize(this);
    auto outer_loop_live_candidate_builder = std::make_shared<OuterloopLiveCandidateBuilder>();
    if (outer_loop_live_candidate_builder && common_w_) {
      outer_loop_live_candidate_builder->Initialize(this);
      common_w_->SetPtrCommonAttr("outer_loop_live_candidate_builder", outer_loop_live_candidate_builder);
    }
    // !! 注意，这个地方修改了几个字段, 以后挪到 front 修改
    FansRequestUtil::RewriteFanstopInfoWithAdditional(get_rank_request()->request_fanstop_ext(),
                                                      mutable_fans_session_data());
    set_native_candidate_pv_sample_ratio(ad_base::AdRandom::GetDouble());
  }
  set_is_ios_platform(ad_request.ad_user_info().platform() == "ios");
  set_platform(ad_request.ad_user_info().platform());
  set_app_version(ad_request.ad_user_info().platform_version());
  set_is_follow_request(IsFollowTraffic());
  set_is_follow_inner_request(IsFollowInnerTraffic());
  set_is_follow_feed_request(IsFollowFeedTraffic());
  set_is_nearby_request(IsNearbyTraffic());
  set_is_explore_or_selected_request(IsExploreOrSelectedTraffic());
  set_is_thanos_mix_request(IsThanosMixTraffic());
  set_is_explore_feed_inner(IsExploreFeedInnerTraffic());
  set_is_explore_feed_mix(IsExploreFeedMixTraffic());
  set_is_inspire_live_request(pos_manager_base.IsInspireLive());
  set_is_inspire_mix(ad_request.is_inspire_mix());
  if (pos_manager_base.IsWanhe() && RankKconfUtil::wanheChargeActionTypeSubPageId() &&
      RankKconfUtil::wanheChargeActionTypeSubPageId()->count(pos_manager_base.GetSubPageId()) > 0) {
    set_is_wanhe_drop_sctr(true);
  }
  set_is_splash_request(IsSplashTraffic());
  if (get_is_splash_request()) {
    set_enable_innerloop_bonus_update(SPDM_enable_innerloop_bonus_update(spdm_ctx));
    // 开屏广告请求参数级初始化
    set_is_splash_debug(RankKconfUtil::splashDebugUserList()->count(get_user_id()) > 0);
  }
  FillItemRequestInfo(ad_request);

  // 刷次
  set_ad_request_times(ad_request.ad_user_session_info().ad_request_times());
  // 设置是否为上下滑或滑滑板请求
  set_is_thanos_request(ad_base::util::IsThanosRequest(ad_request));
  if (get_is_splash_request() && RankKconfUtil::enableSplashToThanos()) {
    set_is_thanos_request(true);
  }
  // 兼容搜索广告的双列模式
  set_is_follow_tab(pos_manager_base.GetAdRequestType() ==
                  kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_FOLLOW);

  set_is_featured_selected(ad_base::util::IsFeaturedSelectedRequest(ad_request) ||
                         ad_base::util::IsInnerExploreRequest(ad_request));
  // 双列流量
  set_is_feed(ad_base::util::IsFeedRequest(ad_request));
  // 极速版请求
  set_is_nebula_request(ad_request.universe_ad_request_info().app_id() == "kuaishou_nebula");
  // 主站滑滑场景流量
  set_is_kuaishou_thanos(
      get_is_thanos_request() && ad_request.universe_ad_request_info().app_id() == "kuaishou");
  // 激励视频
  set_is_rewarded(pos_manager_base.IsRewardedRequestBase(ad_request));
  set_is_knews(engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(
      ad_request.universe_ad_request_info().app_id()));
  // 激励视频，不包括快看点和小游戏
  set_is_rewarded_not_smallgame(pos_manager_base.IsRewardedNotSmallGameBase(ad_request));
  // 激励视频 电商 tab
  set_is_rewarded_merchant(pos_manager_base.IsInspireMerchant());
  // 激励流量
  set_is_incentive(get_is_rewarded() || get_is_inspire_live_request());
  // 请求粒度判断是否触发激励随机实验，用 llsid 保证软硬广队列处理时结果相同
  bool is_incentive_explore = ad_base::AdRandom::GetDouble(get_llsid()) <=
      SPDM_unify_calc_coin_explore_traffic_ratio(spdm_ctx);
  set_is_incentive_explore(is_incentive_explore);

  if (engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(
          pos_manager_base.GetRequestAppId()) &&
      !get_is_thanos_request()) {
    int pos_id = 0;
    if (ad_request.universe_ad_request_info().imp_info().size() > 0) {
      pos_id = ad_request.universe_ad_request_info().imp_info(0).position_id();
    }
    set_is_thanos_request(true);
  }

  set_is_live_rerank_req(ad_request.is_live_rerank_req());
  // 小程序流量打点
  if (pos_manager_base.IsMicroAppRequest()) {
    int pos_id = 0;
    if (ad_request.universe_ad_request_info().imp_info().size() > 0) {
      pos_id = ad_request.universe_ad_request_info().imp_info(0).position_id();
    }
  }

  set_is_kuaishou_traffic(pos_manager_base.GetRequestAppId() == "kuaishou");
  set_is_live_audience_exclude_sub_page_id(
          RankKconfUtil::liveAudienceExcludeSubPageIdList()->count(
          pos_manager_base.GetSubPageId()));
  // 目前仅主 app 发现页未登录用户出广告
  set_is_unlogin_user(ad_request.ad_user_info().is_unlogin_user());
  set_is_lahuo_user(ad_request.is_lahuo_user());
  // 搜索广告单双列打点 tag
  set_search_interactive_form(
      kuaishou::ad::AdEnum_InteractiveForm_Name(pos_manager_base.GetInteractiveForm()));

  for (const auto& ab_key : RankKconfUtil::smallGameForceDirectAbTestConf()->data().GetAllAbTestSwitch()) {
    bool is_force_direct = get_spdm_ctx().TryGetBoolean(ab_key, false);
    mutable_small_game_ab_test_values()->insert({ab_key, is_force_direct});
  }

  // 大促 gpm 参数
  set_big_promotion_gpm_hc_exp_tag(SPDM_big_promotion_gpm_hc_exp_tag(spdm_ctx));
  const auto big_promotion_gpm_hc_conf_ptr = RankKconfUtil::bigPromotionGpmHcConf();
  if (big_promotion_gpm_hc_conf_ptr != nullptr) {
    const auto& big_promotion_exp_list = big_promotion_gpm_hc_conf_ptr->data().exp_list();
    auto it = big_promotion_exp_list.find(get_big_promotion_gpm_hc_exp_tag());
    if (it != big_promotion_exp_list.end()) {
      *mutable_big_promotion_gpm_hc_exp_conf() = it->second;
    }
  }

  // 填充电商 merchant product clicked map
  FillFreqInfo(ad_request);

  // 获取 abtest 平台的 广告形态数据
  FillAbTestData();

  // 获取 qcpx 直播券样式 abtest 参数
  if (RankKconfUtil::enableInnerQcpxLivePecStyleFea()) {
    FillQcpxLiveAndP2lPecStyleAbTestData();
  }

  // 获取 qcpx abtest 参数
  if (RankKconfUtil::enableInnerQcpxAbtestParameterFea()) {
    FillQcpxAbtestParameterData();
  }
  // 是否打印多因子信息
  // 条件 1 : 测试环境 & 开关打开
  // 条件 2 : 命中 trace
  // 条件 3 : ab 开关
  // 满足 (条件 1 || 条件 2) && 条件 3
  set_enable_print_factor_info(
    ((RankKconfUtil::enableDryrunLog() && ks::ad_base::AdKessClient::Instance().IsTestEnv()) ||
      (get_rank_request()->ad_request().trace_log_sampling_flag() == kuaishou::ad::FORCE_SAMPLING ||
       get_rank_request()->ad_request().trace_log_sampling_flag() == kuaishou::ad::SELECTIVE_SAMPLING)) &&
    SPDM_enable_print_factor_info(spdm_ctx));
  set_print_factor_info_random_num(ad_base::AdRandom::GetInt(0, 15));
  // 填充多路 cmd
  FillMultiRetrievalCmd();
  InitMultiColor(ad_request);
  set_work_flow_type(get_rank_request()->work_flow_type());
  set_need_predict_user_next_stay_time(get_rank_request()->need_predict_user_next_stay_time());
  // 垂直行业人群分层标签
  set_industry_explore_user_tag(get_rank_request()->industry_explore_user_tag());

  // NOTE: !!!! 下面不得再从 request、deployment 获取信息给 context 的变量
  // NOTE: !!!! 下面不得再从 request、deployment 获取信息给 context 的变量
  // NOTE: !!!! 下面不得再从 request、deployment 获取信息给 context 的变量
  // 目前粉条相关的 abtest 参数都通过此处进行传递 (ToDo: wangning14)
  if (get_is_search_request()) {
    InitializeSearchSpecificParams();
  }
  if (get_fans_session_data() != nullptr) {
    // 把 fanstop session data 这坨 ps 样本迁移到 attr
    auto *arena = mutable_rpc_arena();
    Accessor(Common::Attr::fanstop_ps_context).SetPtrValue(0,
      google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::Context>(arena));
    Accessor(Common::Attr::fanstop_candidate_photo).SetPtrValue(0,
      google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::UsedItem>(arena));
    Accessor(Common::Attr::fanstop_candidate_live).SetPtrValue(0,
      google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::UsedItem>(arena));
    Accessor(Common::Attr::archimedes_candidate_photo).SetPtrValue(0,
      google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::UsedItem>(arena));
    Accessor(Common::Attr::archimedes_candidate_live).SetPtrValue(0,
      google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::UsedItem>(arena));
  }
  set_enable_product_ltr_write_samples(engine_base::AdKconfUtil::enableProductLtrWriteSamples());
  set_enable_ocpx_ltr_write_samples(engine_base::AdKconfUtil::enableOcpxLtrWriteSamples());

  GetAllStrategyParams();
  GetIndustryExpParams();
  GetModelScoreParams();
  GetProductModelLabScore();
  // 预估值兜底初始化
  mutable_ps_bound_checker()->Init(&spdm_ctx, dot_perf, get_page_id());

  // 行业人群
  std::unordered_set<int64> industry_orientation_ids;
  for (auto orientation_id : get_rank_request()->selected_orientation_for_industry()) {
    industry_orientation_ids.insert(orientation_id);
  }
  for (auto product : ad_request.ad_user_info().game_white_products()) {
    mutable_game_white_products()->insert(product);
  }
  for (auto product : ad_request.ad_user_info().game_sequential_interest_product()) {
    mutable_game_interest_retarget_product()->insert(product);
  }
  auto game_reco_interest_product_ptr = std::make_unique<std::unordered_set<std::string>>();
  for (auto product : ad_request.ad_user_info().game_reco_interest_product()) {
    game_reco_interest_product_ptr->insert(product);
  }
  common_w_->SetPtrCommonAttr("game_reco_photo_interest_product",
        std::move(game_reco_interest_product_ptr));
  if (SPDM_enable_rank_load_nc_model_prod_list(spdm_ctx)) {
    auto prod_nc_model_set_ptr = std::make_unique<std::unordered_set<int64_t>>();
    auto nc_model_prod_score_map_ptr = std::make_unique<std::unordered_map<int64_t, double>>();
    for (const auto &value :
          ad_request.ad_user_info().llm_user_prod_interest_list()) {
      // 数据格式 product_name@23
      std::vector<absl::string_view> tokens = absl::StrSplit(value, "@", absl::SkipEmpty());
      if (tokens.size() == 2) {
        std::string product_name(tokens[0].data(), tokens[0].length());
        int64_t city_product_id = base::CityHash64(product_name.c_str(), product_name.length());
        prod_nc_model_set_ptr->insert(city_product_id);
        double ecpc_ratio = 0;
        if (!absl::SimpleAtod(tokens[1], &ecpc_ratio)) {
          continue;
        }
        (*nc_model_prod_score_map_ptr)[city_product_id] = ecpc_ratio / 100 + 1.0;;
      }
    }
    common_w_->SetPtrCommonAttr("nc_model_prod_list",
          std::move(prod_nc_model_set_ptr));
    common_w_->SetPtrCommonAttr("nc_model_prod_score_map",
        std::move(nc_model_prod_score_map_ptr));
  }

  if (SPDM_enable_rank_load_outerloop_interest_industry(spdm_ctx)) {
    auto outerloop_interest_product_ptr = std::make_unique<std::unordered_set<std::string>>();
    auto outerloop_interest_industry_ptr = std::make_unique<std::unordered_set<std::string>>();
    auto outerloop_interest_second_industry_ptr = std::make_unique<std::unordered_set<std::string>>();
    auto outerloop_shallow_interest_industry_ptr = std::make_unique<std::unordered_set<std::string>>();
    auto outerloop_ac_industry_ptr = std::make_unique<std::unordered_set<int64_t>>();
    auto outerloop_ac_campaign_ptr = std::make_unique<std::unordered_set<int64_t>>();
    for (const auto& product : ad_request.ad_user_info().product_interest_tags()) {
      // 外循环已转化行业
      if (SPDM_enable_rank_load_outerloop_ac_industry(spdm_ctx) &&
          product.length() > 8 && product.substr(0, 8) == "DAC_IND_") {
        int64_t dac_industry = 0;
        if (absl::SimpleAtoi(product.substr(8), &dac_industry)) {
          outerloop_ac_industry_ptr->insert(dac_industry);
        }
      }
      if (product.length() > 8 && product.substr(0, 8) == "DAC_CAM_") {
        int64_t dac_campaign = 0;
        if (absl::SimpleAtoi(product.substr(8), &dac_campaign)) {
          outerloop_ac_campaign_ptr->insert(dac_campaign);
        }
      }
      // 外循环转化频率
      if (SPDM_enable_rank_load_outerloop_ac_frequence(spdm_ctx) &&
          product.length() > 8 && product.substr(0, 8) == "DAC_FRE_") {
        int ac_frequency = 0;
        if (absl::SimpleAtoi(product.substr(8), &ac_frequency)) {
          common_w_->SetIntCommonAttr("outerloop_ac_frequence", ac_frequency);
        }
      }
      if (product.length() > 4 && product.substr(0, 4) == "IND_") {
        outerloop_interest_industry_ptr->insert(product.substr(4));
        if (product.length() > 8 && product.substr(0, 4) == "IND_") {
          outerloop_shallow_interest_industry_ptr->insert(product.substr(4));
        }
      } else {
        outerloop_interest_product_ptr->insert(product);
      }
    }
    common_w_->SetPtrCommonAttr("outerloop_interest_product",
          std::move(outerloop_interest_product_ptr));
    common_w_->SetPtrCommonAttr("outerloop_interest_industry",
          std::move(outerloop_interest_industry_ptr));
    common_w_->SetPtrCommonAttr("outerloop_interest_second_industry",
          std::move(outerloop_interest_second_industry_ptr));
    common_w_->SetPtrCommonAttr("outerloop_shallow_interest_industry",
          std::move(outerloop_shallow_interest_industry_ptr));
    common_w_->SetPtrCommonAttr("outerloop_ac_industry",
          std::move(outerloop_ac_industry_ptr));
    common_w_->SetPtrCommonAttr("outerloop_ac_campaign",
          std::move(outerloop_ac_campaign_ptr));
  } else {
    auto outerloop_interest_product_ptr = std::make_unique<std::unordered_set<std::string>>();
    for (const auto& product : ad_request.ad_user_info().product_interest_tags()) {
      outerloop_interest_product_ptr->insert(product);
    }
    common_w_->SetPtrCommonAttr("outerloop_interest_product",
          std::move(outerloop_interest_product_ptr));
  }

  if (SPDM_enable_outerloop_low_active_ac_mark(spdm_ctx)) {
    bool is_outerloop_potential_nc_user = false;
    if (get_rank_request()) {
      is_outerloop_potential_nc_user = get_rank_request()->is_outerloop_potential_nc_user();
    }
    if (SPDM_enable_outerloop_low_active_ac_mark_update(spdm_ctx)) {
      int outerloop_ac_frequence = common_r_->
        GetIntCommonAttr("outerloop_ac_frequence").value_or(-1);
      int outerloop_low_active_ac_thresh = SPDM_outerloop_low_active_ac_thresh(spdm_ctx);
      if (outerloop_ac_frequence >= outerloop_low_active_ac_thresh) {
        common_w_->SetIntCommonAttr("is_outerloop_low_active_cnt", 1);
      } else {
        common_w_->SetIntCommonAttr("is_outerloop_low_active_cnt", 0);
      }
    } else {
      const auto* interest_industry_set = common_w_->
        GetPtrCommonAttr<std::unordered_set<std::string>>("outerloop_interest_industry");
      const auto* shallow_interest_industry_set = common_w_->
        GetPtrCommonAttr<std::unordered_set<std::string>>("outerloop_shallow_interest_industry");
      int interest_industry_cnt = interest_industry_set != nullptr ? interest_industry_set->size() : 0;
      if (shallow_interest_industry_set != nullptr) {
        interest_industry_cnt -= shallow_interest_industry_set->size();
      }
      if (!is_outerloop_potential_nc_user &&
          (interest_industry_set != nullptr && interest_industry_cnt <= 0)) {
        common_w_->SetIntCommonAttr("is_outerloop_low_active_cnt", 1);
      } else {
        common_w_->SetIntCommonAttr("is_outerloop_low_active_cnt", 0);
      }
    }
  }
  SetSwitches();

  if (get_is_default_deploy()) {
    set_enable_req_index_using_new_config(SPDM_enable_req_index_using_new_config(spdm_ctx) ||
      SPDM_enableReqIndexUsingNewConfig());

    if (SPDM_enableReqIndexByTail()) {
      const char* p_instance_id = getenv("INSTANCE_ID");
      std::string instance_id_str = "-1";
      int instance_id = -1;
      if (p_instance_id != nullptr) {
        instance_id_str = p_instance_id;
      }
      bool ret = absl::SimpleAtoi(instance_id_str, &instance_id);
      auto req_index_by_instance_id_tail = RankKconfUtil::ReqIndexByInstanceIdTail();
      if (req_index_by_instance_id_tail->IsOnFor(instance_id)) {
        set_enable_req_index_using_new_config(true);
        dot_perf->Count(1, "req_index_by_instance_id_tail", "on");
      } else {
        set_enable_req_index_using_new_config(false);
        dot_perf->Count(1, "req_index_by_instance_id_tail", "close");
      }
      dot_perf->Interval(instance_id, "instance_id");
    }
    set_enable_fill_item_attr_by_index(SPDM_enable_fill_item_attr_by_index(spdm_ctx));
    set_enable_fill_inner_loop_item_attr_by_index(SPDM_enable_fill_inner_loop_item_attr_by_index(spdm_ctx));
    // set_enable_not_fill_item_attr_from_target(false);
    set_enable_not_fill_item_attr_from_target(SPDM_enable_not_fill_item_attr_from_target(spdm_ctx));
  }

  // QCPX 随机分流标签 @fandi
  int64_t inner_qcpx_cause_rand_num = ad_base::AdRandom::GetInt(0, 99);
  common_w_->SetIntCommonAttr("inner_qcpx_cause_rand_num", inner_qcpx_cause_rand_num);
  // 货架电商独立精排
  bool enable_split_shelf_merchant = (pos_manager_base.IsShelfMerchantTraffic() &&
                                      SPDM_enable_split_shelf_merchant(spdm_ctx)) ||
                                     (pos_manager_base.IsMallTraffic() &&
                                      SPDM_enable_split_shelf_merchant_mall(spdm_ctx)) ||
                                     (pos_manager_base.IsBuyerHomePageTraffic() &&
                                      SPDM_enable_split_shelf_merchant_buyer(spdm_ctx)) ||
                                     (pos_manager_base.IsGuessYouLike() &&
                                      SPDM_enable_split_shelf_merchant_guess(spdm_ctx)) ||
                                     (pos_manager_base.IsZhuanQianTraffic() &&
                                      SPDM_enable_split_shelf_merchant_zhuanqian(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_split_shelf_merchant", enable_split_shelf_merchant);

  common_w_->SetIntCommonAttr("enable_second_stage_predict", SPDM_enableSecondStagePredictV2() || SPDM_enable_second_stage_predict(spdm_ctx)); // NOLINT

  common_w_->SetIntCommonAttr("rank_direct_predict_flag", 0);
  common_w_->SetIntCommonAttr("inner_normal_predict_enum", 0b0011);
  common_w_->SetIntCommonAttr("inner_native_predict_enum", 0b0101);
  common_w_->SetIntCommonAttr("outer_predict_enum", 0b1001);
  common_w_->SetIntCommonAttr("rpc_parallel", SPDM_rpcParallel() || SPDM_rpc_parallel(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_remote_table_timeout", SPDM_enable_remote_table_timeout(spdm_ctx));
  if ((SPDM_rank_direct_predict_flag(spdm_ctx) > 0 ||
       SPDM_rankDirectPredictFlag() > 0) &&
      common_r_->GetIntCommonAttr("enable_get_router_user_info").value_or(0) == 1) {
    if (SPDM_rankDirectPredictFlag() > 0) {
      common_w_->SetIntCommonAttr("rank_direct_predict_flag", SPDM_rankDirectPredictFlag());
    } else {
      common_w_->SetIntCommonAttr("rank_direct_predict_flag", SPDM_rank_direct_predict_flag(spdm_ctx));
    }
  }
  common_w_->SetIntCommonAttr("is_adrank_offline_diff_test", FLAGS_is_adrank_offline_diff_test);
  dot_perf->Count(1, "rank_direct_predict_flag",
      absl::StrCat(common_r_->GetIntCommonAttr("rank_direct_predict_flag").value_or(0)));
  auto& rank_direct_predict_cmdkey_group = SPDM_rank_direct_predict_cmdkey_group(spdm_ctx);
  common_w_->SetStringCommonAttr("rank_direct_predict_cmdkey_group",
      rank_direct_predict_cmdkey_group.empty() ? "base" : rank_direct_predict_cmdkey_group);
  common_w_->SetStringCommonAttr("rank_exp_cost_time_tag", SPDM_rank_exp_cost_time_tag(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_clean_inefficient_cmdkey",
      SPDM_enable_clean_inefficient_cmdkey(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_rank_skip_invalid_item",
      SPDM_enable_rank_skip_invalid_item(spdm_ctx) || SPDM_enableRankSkipInvalidItem());

  common_w_->SetStringCommonAttr("serialized_reco_user_info", get_serialized_reco_user_info());

  if (SPDM_enable_random_stop_playlet_smart_offer_by_rank(spdm_ctx)) {
    uint32_t user_tag =
          get_rank_request()->ad_request().ad_user_info().smart_offer_user_tag();
    dot_perf->Count(1, "random_stop_playlet_smart_offer",
                    user_tag == 0 ? "DNC" : "DOC", "total_pv");  // NOLINT
    if ((ad_base::AdRandom::GetInt(1, 100) <=
          SPDM_random_stop_playlet_smart_offer_ratio_doc(spdm_ctx) && user_tag != 0)  // NOLINT
      || (ad_base::AdRandom::GetInt(1, 100) <=
          SPDM_random_stop_playlet_smart_offer_ratio_dnc(spdm_ctx) && user_tag == 0)) { // NOLINT
      dot_perf->Count(1, "random_stop_playlet_smart_offer", user_tag == 0 ? "DNC" : "DOC", "1");  // NOLINT
      common_w_->SetIntCommonAttr("random_stop_playlet_smart_offer", 1); // NOLINT
    }
  }

  if (SPDM_enable_random_playlet_smart_offer_by_rank(spdm_ctx)) {
    uint32_t user_tag =
          get_rank_request()->ad_request().ad_user_info().smart_offer_user_tag();
    dot_perf->Count(1, "random_playlet_multi_smart_offer_index", user_tag == 0 ? "DNC" : "DOC", "total_pv");
    if ((ks::ad_base::AdRandom::GetInt(0, 99) <
          SPDM_random_playlet_smart_offer_ratio_doc(spdm_ctx) && user_tag != 0)  // NOLINT
        || (ks::ad_base::AdRandom::GetInt(0, 99) <
          SPDM_random_playlet_smart_offer_ratio_dnc(spdm_ctx) && user_tag == 0)) {
      std::string multi_smart_offer_gear_info =
          SPDM_multi_smart_offer_gear_info(spdm_ctx);
      if (SPDM_enable_playlet_uplift_gear_info_by_user_type(spdm_ctx)) {
        if (user_tag == 0) {
          multi_smart_offer_gear_info =
            SPDM_multi_smart_offer_gear_info_dnc(spdm_ctx);
          if (SPDM_enable_normal_smart_offer_multi_uplift_model_rank(spdm_ctx)) {
            multi_smart_offer_gear_info =
              SPDM_normal_multi_smart_offer_gear_info_dnc(spdm_ctx);
          }
        } else {
          multi_smart_offer_gear_info =
            SPDM_multi_smart_offer_gear_info_doc(spdm_ctx);
          if (SPDM_enable_normal_smart_offer_multi_uplift_model_rank(spdm_ctx)) {
            multi_smart_offer_gear_info =
              SPDM_normal_multi_smart_offer_gear_info_doc(spdm_ctx);
          }
        }
      }
      std::vector<std::vector<double>> all_smart_offer;
      if (!multi_smart_offer_gear_info.empty()) {
        for (auto& token_info : absl::StrSplit(multi_smart_offer_gear_info, ";", absl::SkipEmpty())) {
          std::vector<absl::string_view> one_smart_offer = absl::StrSplit(token_info, ",", absl::SkipEmpty());
          if (one_smart_offer.size() < 3) {
            continue;
          }
          std::vector<double> tmp_one;
          for (int i = 0; i < one_smart_offer.size(); ++i) {
            float num;
            if (absl::SimpleAtof(one_smart_offer[i], &num)) {
              tmp_one.push_back(num);
            } else {
              continue;
            }
          }
          if (tmp_one.size() >= 3) {
            all_smart_offer.push_back(tmp_one);
          }
        }
      }
      if (all_smart_offer.size() >= 1) {
        int32_t random_index = ad_base::AdRandom::GetInt(0, all_smart_offer.size() - 1);
        common_w_->SetIntCommonAttr("random_playlet_multi_smart_offer_index", random_index); // NOLINT
        dot_perf->Count(1, "random_playlet_multi_smart_offer_index",
                user_tag == 0 ? "DNC" : "DOC", absl::StrCat(random_index));
      }
    }
  }

  set_deep_coef_lower_bound(RankKconfUtil::deepCoefLowerBound());
  if (SPDM_enable_unify_adload_plugin(spdm_ctx) ||
      SPDM_enable_industry_orientation_adload_control(spdm_ctx)) {
    UnifyAdloadParamInit(industry_orientation_ids);
  }

  PolarisParamInit();

  InitLocalLifeUserLayeredTagsNew();

  // adload allocate
  if (SPDM_enable_adload_sample_collect(spdm_ctx)) {
    AdloadPredictParamInit();
  }
  set_unify_server_show_rate(SPDM_unify_server_show_rate(spdm_ctx));
  set_nebula_unify_server_show_rate(SPDM_nebula_unify_server_show_rate(spdm_ctx));
  if (SPDM_enable_model_based_adload_control(spdm_ctx)) {
    ModelBasedAdloadParamInit();
  }

  std::string force_impression_multi_tagid_list =
    spdm_ctx.TryGetString("force_impression_multi_tagid_list", "66");
  mutable_guaranteed_tags()->clear();
  for (auto &tagstring : absl::StrSplit(force_impression_multi_tagid_list, ",", absl::SkipEmpty())) {
    int64_t tagid;
    if (absl::SimpleAtoi(tagstring, &tagid)) {
      mutable_guaranteed_tags()->insert(tagid);
    }
  }
  // 重定向修复
  if (SPDM_enable_game_retarget_fix(spdm_ctx) ||  SPDM_enable_game_retarget_switch(spdm_ctx)) {
    mutable_game_guaranteed_tags()->clear();
    const auto& tag_id_conf = RankKconfUtil::gameForceMultiTagidConf();
    if (tag_id_conf != nullptr && tag_id_conf->size() > 0) {
      for (auto& tag_id : *tag_id_conf) {
        mutable_game_guaranteed_tags()->insert(tag_id);
      }
    }
  }
  // 大 R EE 探索白名单产品
  if (SPDM_enable_mini_game_big_r_ee_params_parse_rank(spdm_ctx)) {
    mutable_mini_game_ee_product_ids_map()->clear();
    const auto& user_info = get_rank_request()->ad_request().ad_user_info();
    // 24h 曝光产品列表
    std::unordered_map<int64_t, int64_t> user_imp_product_hash_map;
    const auto& user_action_info =
        get_rank_request()->ad_request().ad_session_response_pack().response().user_action_info();
    for (int64_t i = 0; i < user_action_info.ad_action_info_size(); i++) {
      auto ad_action_info = user_action_info.ad_action_info(i);
      if (ad_action_info.action_type() != kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION) {
        continue;
      }
      for (int64_t j = 0; j < ad_action_info.ad_detail_info_size(); j++) {
        auto& ad_detail_info = ad_action_info.ad_detail_info(j);
        user_imp_product_hash_map[ad_detail_info.product_hash()]++;
      }
    }
    // 历史 30d 曝光产品列表
    std::unordered_map<int64_t, int64_t> user_30d_imp_product_hash_map;
    const auto& product_id_list = user_info.touke_user_impression_city_product_id_list();
    const auto& touke_user_impression_cnt = user_info.touke_user_impression_cnt();
    for (int64_t i = 0; i < product_id_list.size() && i < touke_user_impression_cnt.size(); i++) {
      user_30d_imp_product_hash_map[product_id_list[i]] = touke_user_impression_cnt[i];
    }
    bool imp_product_30d_admit = true;
    bool imp_product_24h_admit = true;
    static ks::ad_base::TargetKeyConvertor str_2_int64_;
    const auto& mini_game_big_r_strategy_conf = engine_base::AdKconfUtil::miniGameBigRStrategy();
    const auto product_explore_conf = mini_game_big_r_strategy_conf->data().product_explore_map;
    auto product_explore_map_iter = product_explore_conf.find(
                                        SPDM_enable_mini_game_big_r_strategy_tag(get_spdm_ctx()));
    if (product_explore_map_iter != product_explore_conf.end()) {
      auto& product_explore_map = product_explore_map_iter->second;
      for (auto& [product_name, explore_ratio] : product_explore_map) {
        auto product_id = str_2_int64_(product_name);
        auto imp_30d_iter = user_30d_imp_product_hash_map.find(product_id);
        if (imp_30d_iter != user_30d_imp_product_hash_map.end() &&
              imp_30d_iter->second > SPDM_mini_game_big_r_ee_strategy_30d_thrd(get_spdm_ctx())) {
          imp_product_30d_admit = false;
        }
        auto imp_24h_iter = user_imp_product_hash_map.find(product_id);
        if (imp_24h_iter != user_imp_product_hash_map.end() &&
              imp_24h_iter->second > SPDM_mini_game_big_r_ee_strategy_24h_thrd(get_spdm_ctx())) {
          imp_product_24h_admit = false;
        }
        if ((SPDM_enable_mini_game_big_r_ee_strategy_by_30d(get_spdm_ctx()) && imp_product_30d_admit) ||
            (SPDM_enable_mini_game_big_r_ee_strategy_by_24h(get_spdm_ctx()) && imp_product_24h_admit &&
            (imp_product_30d_admit || !SPDM_enable_mini_game_big_r_explore_end(get_spdm_ctx())))) {
            (*mutable_mini_game_ee_product_ids_map()).emplace(product_id, explore_ratio);
        }
      }
    }

    // 大 R 字段填充
    set_is_mini_game_big_r_user(false);
    double iap_payment_24h = 0.0;
    double iap_payment_30d = 0.0;
    double iap_payment_total = 0.0;
    std::string game_history_pay_amount_str = user_info.game_history_pay_amount();
    base::Json game_history_pay_amount_json = base::Json(base::StringToJson(game_history_pay_amount_str));
    if (game_history_pay_amount_json.IsObject()) {
      iap_payment_24h = game_history_pay_amount_json.GetFloat("iap_payment_24h", 0.0);
      iap_payment_30d = game_history_pay_amount_json.GetFloat("iap_payment_30d", 0.0);
      iap_payment_total = game_history_pay_amount_json.GetFloat("iap_payment_total", 0.0);
    }
    double default_iap_payment_constraint = SPDM_default_iap_payment_constraint(get_spdm_ctx());
    if (SPDM_enable_big_r_update_rank_constraint(get_spdm_ctx())) {
      default_iap_payment_constraint = SPDM_default_iap_payment_constraint_rank(get_spdm_ctx());
    }
    bool game_big_r_status_v2 = (SPDM_enable_iap_payment_window_30d(get_spdm_ctx()) ? iap_payment_30d :
                              iap_payment_total) > default_iap_payment_constraint;
    if (SPDM_enable_big_r_use_long_value(get_spdm_ctx())) {
      game_big_r_status_v2 |= (iap_payment_30d / (iap_payment_24h <= 0.000001 ? 180.0 :
                            iap_payment_24h) > SPDM_default_iap_long_value_constraint(get_spdm_ctx()));
    }
    set_is_mini_game_big_r_user(game_big_r_status_v2);

    // 大 R 监控
    if (SPDM_enable_mini_game_big_r_uv_monitor(get_spdm_ctx())) {
      dot_perf->Count(1, "is_mini_game_big_r_user_rank",
                              absl::StrCat(get_is_mini_game_big_r_user()), absl::StrCat(get_page_id()));
      if (product_explore_map_iter != product_explore_conf.end()) {
        auto& product_explore_map = product_explore_map_iter->second;
        for (auto& [product_name, explore_ratio] : product_explore_map) {
          auto product_id = str_2_int64_(product_name);
          auto mini_game_ee_product_ids_map_iter = mutable_mini_game_ee_product_ids_map()->find(product_id);
          if (mini_game_ee_product_ids_map_iter != mutable_mini_game_ee_product_ids_map()->end()) {
            dot_perf->Count(1, "is_mini_game_big_r_user_need_explore_rank", product_name,
                              absl::StrCat(get_is_mini_game_big_r_user()), absl::StrCat(get_page_id()));
          }
        }
      }
    }

    dot_perf->Count(1, "game_explore_product_size_rank",
                            absl::StrCat(mutable_mini_game_ee_product_ids_map()->size()),
                                 SPDM_enable_mini_game_big_r_strategy_tag(get_spdm_ctx()),
                                              absl::StrCat(get_is_mini_game_big_r_user()));
  }
  // target kv 透传
  if (SPDM_enable_mini_game_big_r_user_update(get_spdm_ctx())) {
    auto big_r_explore_record_trans =
                common_r_->GetStringListCommonAttr("default.big_r_explore_record_trans").
                                              value_or(std::vector<absl::string_view>());
    for (auto it : big_r_explore_record_trans) {
      kuaishou::ad::BigRExploreRecord big_r_explore_record;
      if (big_r_explore_record.ParseFromString(std::string(it))) {
        mutable_big_r_explore_record_list()->push_back(big_r_explore_record);
      }
    }
  }
  bool enable_model_explore = SPDM_enable_model_explore(spdm_ctx);
  double model_explore_thr = SPDM_model_explore_thr(spdm_ctx);
  if (enable_model_explore && ad_base::AdRandom::GetDouble() < model_explore_thr) {
    set_is_model_explore(true);
  }
  MixRankBidExpParamInit();
  set_disable_account_post_server(true);
  set_rank_ps_adjust_exp_tag(spdm_ctx.TryGetString("rank_ps_adjust_exp_tag", ""));
  auto buyer_effective_type = get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
  if (get_rank_request()->ad_request().ad_user_info().has_user_level_v2_risk()) {
    buyer_effective_type = get_rank_request()->ad_request().ad_user_info().user_level_v2_risk();
  }
  if (buyer_effective_type == "U4" || buyer_effective_type == "U4+" ||
      buyer_effective_type == "u4" || buyer_effective_type == "u4+") {
    set_buyer_tier("U4");
  } else if (buyer_effective_type == "U3" || buyer_effective_type == "U2" ||
              buyer_effective_type == "u3" || buyer_effective_type == "u2") {
    set_buyer_tier("U2&U3");
  } else if (buyer_effective_type == "U0" || buyer_effective_type == "u0" ||
              buyer_effective_type == "risk" || buyer_effective_type == "u0-potential") {
    set_buyer_tier("U0&U1");
  }

  // 开屏 spuid 重定向加权
  if (get_is_splash_request()) {
    std::string exp_tag = SPDM_spu_weight_exp_tag_for_splash_inner_ad(spdm_ctx);
    if (exp_tag != "" && !exp_tag.empty()) {
      auto ratio_conf = RankKconfUtil::splashSpuIdRatioExpConf();
      if (ratio_conf != nullptr) {
        auto exp_iter = ratio_conf->data().ab_list().find(exp_tag);
        if (exp_iter != ratio_conf->data().ab_list().end()) {
          // 暂时统一调整，不区分更细纬度
          auto iter1 = exp_iter->second.config().find("default");
          if (iter1 != exp_iter->second.config().end()) {
            auto conf = iter1->second;
            set_splash_innner_boost_ratio(conf.boost_ratio());
          }
        }
      }
    }
  }
  dot_perf->Interval(ab_init_st - ab_init_ed, "init_ad_cost_us");
  // 用于统计 rank 收到了多少请求
  dot_perf->Count(1, "rank_request_num");
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_rank", "rank_abtest_debug_flag",
      SPDM_rank_abtest_debug_flag(spdm_ctx) ? "1" : "0");
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_rank", "rank_kconf_debug_flag",
      SPDM_rankKconfDebugFlag() ? "1" : "0");

  if (common_r_->GetIntCommonAttr("enable_get_router_user_info").value_or(0) == 1) {
    const std::vector<std::string> user_info_attr_names = {
      "ad_router_ad_fantop_action",
      "ad_router_ad_user_realtime_action_attr_map_pb",
      "ad_router_ad_user_realtime_action_attr_map_bs",
      "ad_router_ad_user_realtime_action_pb",
      "ad_router_eb_ad_user_realtime_action_pb",
      "ad_router_ad_user_realtime_action_bs",
      "ad_router_ad_user_profile",
      "ad_router_req_sub_context",
      "ad_router_reco_sub_context",
      "ad_router_ad_context",
      "ad_router_user_info_bin"
    };
    for (const auto& attr_name : user_info_attr_names) {
       const auto &user_info_str = common_r_->GetStringCommonAttr(attr_name).value_or("");
       ks::ad_base::AdPerf::IntervalLogStash(user_info_str.size(), "ad.ad_rank", "router_user_info_size", attr_name);   // NOLINT
    }
  }
  return true;
}

void ContextData::ParseModelBasedAdloadControlDetail(const std::shared_ptr<
  ks::ad_base::kconf::ProtoKconf<ks::ad_rank::kconf::ModelBasedAdloadControl>> modelBasedAdloadControl,
                                                const std::string& exp_tag,
                                                const std::string& app_id,
                                                const ModelBasedAdloadDimensionTag dimension_tag) {
  const auto& app_list = modelBasedAdloadControl->data().app_list();
  for (auto it = app_list.begin(); it != app_list.end(); it++) {
    if (it->first != app_id) {
      continue;
    }

    const auto& exp_list = it->second.exp_list();
    for (auto exp_list_iter = exp_list.begin(); exp_list_iter != exp_list.end(); exp_list_iter++) {
      if (exp_list_iter->first != exp_tag) {
        continue;
      }
      if (dimension_tag == ModelBasedAdloadDimensionTag::Naive) {
        (*mutable_model_based_adload_map())[ModelBasedAdloadDimensionTag::Naive] =
             exp_list_iter->second.naive_name();
      } else if (dimension_tag == ModelBasedAdloadDimensionTag::Industry) {
        for (auto& kv : exp_list_iter->second.industry_name()) {
          int64 industry_id = kv.first;
          if (industry_id > ModelBasedAdloadDimensionTag::MinFirstIndustry &&
                industry_id < ModelBasedAdloadDimensionTag::MaxFirstIndustry) {
            (*mutable_model_based_adload_map())[kv.first] = kv.second;
          }
        }
      } else if (dimension_tag == ModelBasedAdloadDimensionTag::User) {
        for (auto& kv : exp_list_iter->second.user_group_name()) {
          int64 user_level = kv.first;
          if (user_level > ModelBasedAdloadDimensionTag::MinUserLevel &&
                user_level < ModelBasedAdloadDimensionTag::MaxUserLevel) {
            (*mutable_model_based_adload_map())[kv.first] = kv.second;
          }
        }
      }
    }
  }
}

void ContextData::ModelBasedAdloadParamInit() {
  auto naive_ptr = RankKconfUtil::modelBasedAdloadControl();
  auto industry_ptr = RankKconfUtil::modelBasedIndustryAdloadControl();
  auto user_ptr = RankKconfUtil::modelBasedUserAdloadControl();
  std::string naive_tag = SPDM_model_based_adload_naive_tag(spdm_ctx);
  std::string industry_tag = SPDM_model_based_adload_industry_tag(spdm_ctx);
  set_total_tag(absl::StrCat(naive_tag, "_", industry_tag, "_", SPDM_model_based_adload_user_tag(spdm_ctx)));
  std::string app_id = pos_manager_base.GetRequestAppId();

  ParseModelBasedAdloadControlDetail(naive_ptr, naive_tag,
                          app_id, ModelBasedAdloadDimensionTag::Naive);
  ParseModelBasedAdloadControlDetail(industry_ptr, industry_tag,
                          app_id, ModelBasedAdloadDimensionTag::Industry);
  ParseModelBasedAdloadControlDetail(user_ptr, SPDM_model_based_adload_user_tag(spdm_ctx),
                          app_id, ModelBasedAdloadDimensionTag::User);
}


void ContextData::AdloadPredictParamInit() {
  mutable_adload_predict_sample_map()->clear();
  if (get_llsid() > 0) {
    int64_t adload_sample_pv_bucket = SPDM_adload_sample_pv_bucket(spdm_ctx);
    if (adload_sample_pv_bucket <= 0) {
      return;
    }
    int64_t llsid_bucket = get_llsid() % adload_sample_pv_bucket;
    const auto& adload_predict_arm_map = RankKconfUtil::adloadpredictarm()->data().group_name();
    auto adload_group_map = adload_predict_arm_map.find(SPDM_adload_sample_group_tag(spdm_ctx));
    if (adload_group_map != adload_predict_arm_map.end()) {
      const auto& arms_set = adload_group_map->second.arms();
      auto param_list = arms_set.find(llsid_bucket);
      if (param_list != arms_set.end()) {
        const auto& control_dim_map = param_list->second.control_dim();
        for (auto& it : control_dim_map) {
         (*mutable_adload_predict_sample_map())[it.first] = &(it.second);
        }
      }
    }
  }
}

void ContextData::ParseUnifyAdloadControlDetail(
    const std::shared_ptr<
      ks::ad_base::kconf::ProtoKconf<ks::ad_rank::kconf::UnifyAdloadControlData2> > UnifyAdloadControlData2,
    const std::string& exp_tag,
    const std::string& app_id,
    const UnifyAdloadDimensionTag dimension_tag,
    const std::unordered_set<int64>& industry_orientation_ids) {
  const auto& app_list = UnifyAdloadControlData2->data().app_list();
  auto default_industry_ori_strategy = RankKconfUtil::defaultIndustryOrientationStrategyList();
  auto default_industry_ori_list = RankKconfUtil::DefaultIndustryOrientationList();
  for (auto it = app_list.begin(); it != app_list.end(); it++) {
    if (it->first != app_id) {
      continue;
    }
    const auto& exp_list = it->second.exp_list();
    for (auto exp_list_it = exp_list.begin(); exp_list_it != exp_list.end(); exp_list_it++) {
      if (exp_list_it->first != exp_tag) {
        continue;
      }
      if (dimension_tag == UnifyAdloadDimensionTag::ALL) {
        (*mutable_unify_adload_control_map())[UnifyAdloadDetailTag::Total] = &(exp_list_it->second.total());
      } else if (dimension_tag == UnifyAdloadDimensionTag::INNER_FANSTOP) {
        (*mutable_unify_adload_control_map())[UnifyAdloadDetailTag::InnerFanstop] = &(exp_list_it->second.inner_fanstop());   // NOLINT
      } else if (dimension_tag == UnifyAdloadDimensionTag::INDUSTRY) {
        for (const auto& kv : exp_list_it->second.industry()) {
          int64 industry_id = kv.first;
          if (industry_id > UnifyAdloadDetailTag::MinIndustryId &&
                industry_id < UnifyAdloadDetailTag::MaxIndustryId) {
            (*mutable_unify_adload_control_map())[industry_id] = &(kv.second);
          }
        }
      } else if (dimension_tag == UnifyAdloadDimensionTag::INDUSTRY_ORIENTATION) {
        // 解析行业人群配置
        auto& unify_adload_industry_ori_control_map_ = *mutable_unify_adload_industry_ori_control_map();
        for (const auto& kv : exp_list_it->second.industry_orientation()) {
          int64 industry_orientation_id = kv.first;
          const auto& conf = kv.second;
          AdloadControlConfig unify_adload_industry_ori_control_config;
          if (industry_orientation_ids.find(industry_orientation_id) != industry_orientation_ids.end() ||  // 如果该人群命中配置 // NOLINT
              (default_industry_ori_list->count(industry_orientation_id) > 0 &&
                  conf.enable_all_industry_orientation()) ||
              (default_industry_ori_strategy->count(industry_orientation_id) > 0 &&
                  industry_orientation_id < 64 &&
                  (get_industry_explore_user_tag() & (1L << industry_orientation_id)) > 0)) {  // 如果不命中
            unify_adload_industry_ori_control_config.show_ratio_output = conf.show_ratio_output();
            for (const auto& industry_product : conf.industry_product_set()) {
              unify_adload_industry_ori_control_config.industry_product_set.insert(
                  base::CityHash64(industry_product.c_str(), industry_product.length()));
            }
            for (const auto& industry_photo : conf.industry_photo_set()) {
              unify_adload_industry_ori_control_config.industry_photo_set.insert(industry_photo);
            }
            for (const auto& industry_id : conf.industry_id_set()) {
              unify_adload_industry_ori_control_config.industry_id_set.insert(industry_id);
            }
            for (const auto& ocpx : conf.ocpx_set()) {
              unify_adload_industry_ori_control_config.ocpx_set.insert(ocpx);
            }
            for (const auto& account_id : conf.account_id_set()) {
              unify_adload_industry_ori_control_config.account_id_set.insert(account_id);
            }
            unify_adload_industry_ori_control_config.cpm_thresh =
                conf.cpm_thresh();
            unify_adload_industry_ori_control_config.force_reco_avoid_same_second_industry_topn =
                conf.force_reco_avoid_same_second_industry_topn();
            unify_adload_industry_ori_control_config.force_reco_cpm_ratio_threshold =
                conf.force_reco_cpm_ratio_threshold();
            unify_adload_industry_ori_control_config.industry_ori_show_ratio_strategy =
                conf.industry_ori_show_ratio_strategy();
            unify_adload_industry_ori_control_map_[industry_orientation_id] =
                unify_adload_industry_ori_control_config;
          }
        }
      } else if (dimension_tag == UnifyAdloadDimensionTag::GAME_SHOUFA) {
        // 解析游戏首发人群配置
        for (const auto& kv : exp_list_it->second.industry_orientation()) {
          int64 industry_orientation_id = kv.first;
          const auto& conf = kv.second;
          AdloadControlConfig game_shoufa_config;
          if (industry_orientation_ids.find(industry_orientation_id) != industry_orientation_ids.end() ||  // 如果该人群命中配置 // NOLINT
              conf.enable_all_industry_orientation() ||
              (industry_orientation_id < 64 &&
               (get_industry_explore_user_tag() & (1L << industry_orientation_id)) > 0)) {
            game_shoufa_config.show_ratio_output = conf.show_ratio_output();
            for (const auto& industry_product : conf.industry_product_set()) {
              game_shoufa_config.industry_product_set.insert(
                  base::CityHash64(industry_product.c_str(), industry_product.length()));
            }
            for (const auto& industry_photo : conf.industry_photo_set()) {
              game_shoufa_config.industry_photo_set.insert(industry_photo);
            }
            for (const auto& industry_id : conf.industry_id_set()) {
              game_shoufa_config.industry_id_set.insert(industry_id);
            }
            for (const auto& account_id : conf.account_id_set()) {
              game_shoufa_config.account_id_set.insert(account_id);
            }
            game_shoufa_config.cpm_thresh = conf.cpm_thresh();
            game_shoufa_config.force_reco_avoid_same_second_industry_topn =
                conf.force_reco_avoid_same_second_industry_topn();
            game_shoufa_config.force_reco_cpm_ratio_threshold = conf.force_reco_cpm_ratio_threshold();
            game_shoufa_config.is_only_wechat_game = conf.is_only_wechat_game();
            (*mutable_game_shoufa_force_control_map())[industry_orientation_id] = game_shoufa_config;
          }
        }
      }
    }
  }
}

void ContextData::PolarisParamInit() {
  auto ptr = RankKconfUtil::industryPolarisStrategy();
  if (!ptr) {
    return;
  }
  const auto& polaris_config = ptr->data();
  auto industry_list = polaris_config.industry_list();
  const std::string& polaris_exp_params_exp_tag = SPDM_polaris_exp_params_exp_tag(spdm_ctx);
  auto& exp_list = polaris_config.exp_list();
  auto it_exp_tag = exp_list.find(polaris_exp_params_exp_tag);
  if (it_exp_tag != exp_list.end()) {
    const auto& exp_detail = it_exp_tag->second;
    industry_list = exp_detail.industry_list();
  }
  for (auto it = industry_list.begin(); it != industry_list.end(); it++) {
    int64 industry_id = it->first;
    const auto& detail = it->second;
    PolarisParam polaris_param;
    polaris_param.params.CopyFrom(detail.params());
    (*mutable_industry_polaris_param_map())[industry_id] = polaris_param;
  }
  const auto& user_tag_score = get_rank_request()->ad_request().ad_user_info().polaris_tag_score();
  auto user_ptr = RankKconfUtil::IndustryPolarisTagScoreConfig();
  if (!user_ptr) {
    return;
  }
  auto str_list = RankKconfUtil::polarisUserOrientation();
  const auto& user_config = user_ptr->data();
  auto& user_exp_list = user_config.exp_list();
  const std::string& user_exp_tag = SPDM_polaris_user_tag_score_hc_exp_tag(spdm_ctx);
  auto it_user_exp_tag = user_exp_list.find(user_exp_tag);
  if (it_user_exp_tag != user_exp_list.end()) {
    const auto& params = it_user_exp_tag->second;
    const auto& admit_tag_map = params.admit_tag_map();
    dot_perf->Count(1, "ad_rank.industry_polaris_hc_user_ratio", "user_tag_count", "all",
                    user_exp_tag);
    for (auto& config : user_tag_score) {
      dot_perf->Count(1, "ad_rank.industry_polaris_hc_user_ratio", "user_tag_count", config.tag(),
                      user_exp_tag);
      for (auto& admit_detail : admit_tag_map) {
        bool config_valid = false;
        if (str_list && admit_detail.enable_auto_tag_list()) {
          config_valid = (str_list->count(config.tag()) > 0);
        } else {
          auto itr = std::find(admit_detail.tag().begin(), admit_detail.tag().end(), config.tag());
          config_valid = (itr != admit_detail.tag().end());
        }
        if (config_valid && config.score() >= admit_detail.score_thr()) {
          (*mutable_user_tag_params()).insert({config.tag(),
          UserTagParam(config.score(), admit_detail.min_score(), admit_detail.max_score(),
          admit_detail.user_tag_ratio(), admit_detail.value_tag(), admit_detail.industry_strategy_tag())});
          dot_perf->Count(1, "ad_rank.industry_polaris_hc_user_ratio", "admit_tag_count", config.tag(),
                          user_exp_tag);
          break;
        }
      }
    }
    set_cpm_thr_boost_ratio(params.cpm_thr_boost_ratio());
    set_min_user_hc_ratio(params.min_user_hc_ratio());
    set_max_user_hc_ratio(params.max_user_hc_ratio());
    set_user_hc_strategy(params.user_hc_strategy());
  }
}

void ContextData::InitLocalLifeUserLayeredTags() {
  // 把用户行为标签解析出来: "1:1.0;2:2.0" --> [(1, 1.0), (2, 2.0), ...]
  mutable_local_life_user_layered_tags()->clear();
  const std::string& user_layered_tags =
    get_rank_request()->ad_request().ad_user_info().user_layered_tags();
  std::vector<absl::string_view> user_layered_tags_vec =
    absl::StrSplit(user_layered_tags, ';', absl::SkipEmpty());
  for (const auto& item : user_layered_tags_vec) {
    std::vector<absl::string_view> tag_and_score = absl::StrSplit(item, ':', absl::SkipEmpty());
    if (tag_and_score.size() == 2) {
      int tag = 0;
      float score = 0.0;
      if (absl::SimpleAtoi(tag_and_score[0], &tag) && absl::SimpleAtof(tag_and_score[1], &score)) {
        mutable_local_life_user_layered_tags()->emplace_back(tag, score);
      }
    }
  }
}

void ContextData::InitLocalLifeUserLayeredTagsNew() {
  // 把用户行为标签解析出来: "1:1.0;2:2.0" --> [(1, 1.0), (2, 2.0), ...]
  mutable_user_layered_tags_hit_map()->clear();
  mutable_local_life_user_layered_tags()->clear();
  const std::string& user_layered_tags =
      get_rank_request()->ad_request().ad_user_info().user_layered_tags();
  std::vector<absl::string_view> user_layered_tags_vec =
      absl::StrSplit(user_layered_tags, ';', absl::SkipEmpty());
  std::vector<absl::string_view> user_tag_source_set_str =
      absl::StrSplit(SPDM_user_tag_source_list_str(spdm_ctx), ",", absl::SkipEmpty());
  absl::flat_hash_set<int> user_tag_source_set;
  std::for_each(user_tag_source_set_str.begin(),
                user_tag_source_set_str.end(), [&] (const absl::string_view& view) {
    int source = 0;
    if (absl::SimpleAtoi(view, &source)) {
      user_tag_source_set.emplace(source);
    }
  });
  dot_perf->Count(1, "ad_rank.InitLocalLifeUserLayeredTags", "user_source",
                  "user_cnt_0");
  std::unordered_map<int, float> temp_user_layered_tags_map;
  for (const auto& item : user_layered_tags_vec) {
    std::vector<absl::string_view> tag_and_score = absl::StrSplit(item, ':', absl::SkipEmpty());
    // tag : score
    if (tag_and_score.size() == 2) {
      int tag = 0;
      float score = 0.0;
      if (absl::SimpleAtoi(tag_and_score[0], &tag) && absl::SimpleAtof(tag_and_score[1], &score)) {
        auto iter_temp = temp_user_layered_tags_map.find(tag);
        if (iter_temp != temp_user_layered_tags_map.end()) {
          if (iter_temp->second > 0 && score > iter_temp->second) {
            temp_user_layered_tags_map[tag] = score;
          } else if (iter_temp->second < 0 && score < iter_temp->second) {
            temp_user_layered_tags_map[tag] = score;
          }
        } else {
          temp_user_layered_tags_map[tag] = score;
        }
      }
    }
    // source : tag : score
    if (tag_and_score.size() == 3) {
      int source = 0;
      int tag = 0;
      float score = 0.0;
      if (absl::SimpleAtoi(tag_and_score[0], &source) &&
          absl::SimpleAtoi(tag_and_score[1], &tag) &&
          absl::SimpleAtof(tag_and_score[2], &score)) {
        const std::string& source_name = kuaishou::ad::AdEnum::UserLayeredSources_Name(source);
        dot_perf->Count(1, "ad_rank.InitLocalLifeUserLayeredTags", "user_source",
                        "user_cnt_1");
        dot_perf->Count(1, "ad_rank.InitLocalLifeUserLayeredTags", "user_source",
                        source_name, absl::StrCat(tag));
        if (user_tag_source_set.count(source) > 0) {  // source 准入
          dot_perf->Count(1, "ad_rank.InitLocalLifeUserLayeredTags", "user_admit_source",
                          source_name, absl::StrCat(tag));
          auto iter_temp = temp_user_layered_tags_map.find(tag);
          if (iter_temp != temp_user_layered_tags_map.end()) {
            if (iter_temp->second > 0 && score > iter_temp->second) {
              temp_user_layered_tags_map[tag] = score;
            } else if (iter_temp->second < 0 && score < iter_temp->second) {
              temp_user_layered_tags_map[tag] = score;
            }
          } else {
            temp_user_layered_tags_map[tag] = score;
          }
        }
      }
    }
    dot_perf->Count(1, "ad_rank.InitLocalLifeUserLayeredTags",
                    "size", absl::StrCat(tag_and_score.size()));
  }
  mutable_user_layered_tags_hit_map()->insert({"pv_init_total", 1});
  if (!temp_user_layered_tags_map.empty()) {
    mutable_user_layered_tags_hit_map()->insert({"pv_init_has_user_tag", 1});
    dot_perf->Count(1, "ad_rank.InitLocalLifeUserLayeredTags", "user_tag",
                    "user_cnt_1");
    for (const auto& [tag, score] : temp_user_layered_tags_map) {
      mutable_user_layered_tags_hit_map()->insert({absl::StrCat("pv_init_usertag_", tag), 1});
      mutable_local_life_user_layered_tags()->emplace_back(tag, score);
      dot_perf->Count(1, "ad_rank.InitLocalLifeUserLayeredTags",
                      "user_tag", absl::StrCat(tag));
    }
  }
}

void ContextData::ParseUnifyAdloadConf(const std::unordered_set<int64>& industry_orientation_ids) {
  auto total_ptr = RankKconfUtil::totalUnifyAdloadControl();
  auto inner_fanstop_ptr = RankKconfUtil::InnerFanstopAdloadControl();
  auto industry_ptr = RankKconfUtil::IndustryAdloadControl();

  std::string total_tag = SPDM_unify_adload_total_tag(spdm_ctx);
  std::string ft_tag = SPDM_unify_adload_ft_tag(spdm_ctx);
  std::string ad_tag = SPDM_unify_adload_ad_tag(spdm_ctx);
  std::string inner_fanstop_tag = "exp3";
  std::string app_id = pos_manager_base.GetRequestAppId();
  set_unify_adload_tag(absl::StrCat(total_tag, "_", ft_tag));

  // 整体
  ParseUnifyAdloadControlDetail(total_ptr, total_tag, app_id, UnifyAdloadDimensionTag::ALL,
      industry_orientation_ids);
  // 内粉
  ParseUnifyAdloadControlDetail(inner_fanstop_ptr,
                  inner_fanstop_tag, app_id, UnifyAdloadDimensionTag::INNER_FANSTOP,
                  industry_orientation_ids);
  // 行业人群 短剧
  if (SPDM_enable_industry_orientation_adload_control(spdm_ctx)) {
    dot_perf->Interval(industry_orientation_ids.size(), "ad_rank.industry_orientation_ids");
    auto industry_ori_ptr = RankKconfUtil::industryOrientationAdloadControl();
    std::string industry_ori_tag = SPDM_unify_adload_industry_ori_tag(spdm_ctx);
    if (app_id == "kuaishou" || app_id == "kuaishou_nebula") {
      ParseUnifyAdloadControlDetail(industry_ori_ptr, industry_ori_tag,
                                    "default", UnifyAdloadDimensionTag::INDUSTRY_ORIENTATION,
                                    industry_orientation_ids);
    } else {
      ParseUnifyAdloadControlDetail(industry_ori_ptr,
                                    industry_ori_tag, app_id, UnifyAdloadDimensionTag::INDUSTRY_ORIENTATION,
                                    industry_orientation_ids);
    }
  }
  // 游戏行业首发独立配置
  if (SPDM_enable_game_shoufa_force_adload_control(spdm_ctx)) {
    auto industry_ori_ptr = RankKconfUtil::gameShoufaForceAdloadControl();
    std::string industry_ori_tag = SPDM_game_shoufa_force_exp_tag(spdm_ctx);
    if (app_id == "kuaishou" || app_id == "kuaishou_nebula") {
      ParseUnifyAdloadControlDetail(industry_ori_ptr, industry_ori_tag,
                                    "default", UnifyAdloadDimensionTag::GAME_SHOUFA,
                                    industry_orientation_ids);
    } else {
      ParseUnifyAdloadControlDetail(industry_ori_ptr,
                                    industry_ori_tag, app_id, UnifyAdloadDimensionTag::GAME_SHOUFA,
                                    industry_orientation_ids);
    }
  }
}

void ContextData::UnifyAdloadParamInit(const std::unordered_set<int64>& industry_orientation_ids) {
  ParseUnifyAdloadConf(industry_orientation_ids);
  set_enable_industry_orientation_adload_control(SPDM_enable_industry_orientation_adload_control(spdm_ctx));
}

void ContextData::InitializeSearchSpecificParams() {
  int64_t search_now_ms = base::GetTimestamp();
  const auto& ad_request = get_rank_request()->ad_request();
  if (ad_request.search_info().search_source() == kuaishou::ad::SearchInfo::LIVE_STREAM_SEARCH) {
    set_is_search_live_tab(true);
  } else if (ad_request.search_info().search_source() == kuaishou::ad::SearchInfo::GOODS_SEARCH) {
    set_is_search_good_tab(true);
  } else if (ad_request.search_info().search_source() == kuaishou::ad::SearchInfo::FEED_SEARCH) {
    set_is_search_photo_tab(true);
  } else if (ad_request.search_info().search_source() ==
                 kuaishou::ad::SearchInfo::INNER_STREAM_COMBO_SEARCH) {
    set_is_search_inner_stream(true);
  } else if (ad_request.search_info().search_source() == kuaishou::ad::SearchInfo::ALADDIN_COMBO_SEARCH) {
    set_aladdin_combo_search(true);
  } else if (ad_request.search_info().search_source() ==
                 kuaishou::ad::SearchInfo::ACTIVITY_COMMODITY_SEARCH ||
             ad_request.search_info().request_source() == 1) {
    // 凑单页主题中间页复用购物 tab 策略
    set_is_search_good_tab(true);
  }
  // 这个参数其实是调节 cpm 和 bid 关系的一个系数。可能是由于历史原因叫了这个名字
  if (RankKconfUtil::searchCheckQuery()->count(ad_request.search_info().query())) {
    set_is_hot_query(true);
  }
  if (RankKconfUtil::searchSkipFilterQuery()->count(ad_request.search_info().query())) {
    set_is_skip_filter_query(true);
  }
  // 后续搜索 native 的代码都需要删除
  // TODO(search) 后续再删除这个逻辑
  set_enable_search_pos_sctr2(SPDM_enable_search_pos_sctr2(spdm_ctx) && !get_is_search_inner_stream());

  std::string rank_search_cpm_ab = spdm_ctx.TryGetString("rank_search_cpm_group_name", "base");
  auto shared = RankKconfUtil::rankSearchCpmThrConfig();
  const auto &kconf = shared->data().abtest();
  const auto iter = kconf.find(rank_search_cpm_ab);
  if (iter != kconf.end()) {
    mutable_rank_search_cpm_thr()->CopyFrom(iter->second);
  } else {
    LOG(WARNING) << "rankSearchCpmThrConfig not find.";
  }

  std::string rank_search_multilist_cpm_ab =
    spdm_ctx.TryGetString("rank_search_multilist_cpm_group_name", "base");
  auto shared_multilist = RankKconfUtil::rankSearchMultilistCpmThrConfig();
  const auto &kconf_multilist = shared_multilist->data().abtest();
  const auto iter_multilist = kconf_multilist.find(rank_search_multilist_cpm_ab);
  if (iter_multilist != kconf_multilist.end()) {
    mutable_rank_search_multilist_cpm_thr()->CopyFrom(iter_multilist->second);
  } else {
    LOG(WARNING) << "rankSearchMultilistCpmThrConfig not find.";
  }

  std::string rank_search_cpm_adjust = spdm_ctx.TryGetString("rank_search_cpm_adjust", "base");
  auto shared_adjust = RankKconfUtil::rankSearchCpmThrAdjustConfig();
  const auto &kconf_adjust = shared_adjust->data().abtest();
  const auto iter_adjust = kconf_adjust.find(rank_search_cpm_adjust);
  if (iter_adjust != kconf_adjust.end()) {
    mutable_rank_search_cpm_thr_adjust()->CopyFrom(iter_adjust->second);
  } else {
    LOG(WARNING) << "rankSearchCpmThrAdjustConfig not find.";
  }

  if (get_fans_session_data() == nullptr) {
    Accessor(Common::Attr::fans_session_data)
        .SetPtrValue(0, std::make_shared<FansTopSessionData>());
  }
  auto *fans = mutable_fans_session_data();
  if (fans == nullptr)
    return;
  bool is_valid_fanstop_request = false;
  if (SPDM_switchCommonAttrOfFanstopSessionData()) {
    is_valid_fanstop_request = fans->Initialize(common_r_, nullptr);
  } else {
    is_valid_fanstop_request = fans->Initialize(
      ad_request, get_rank_request()->request_fanstop_ext(), &spdm_ctx);
    if (dot_perf->Enable()) {
      // 检查 fanstop session data 的一致性 , 为下线 fanstopSessionData 做准备
      fans->Initialize(common_r_, dot_perf);
    }
  }
  // 这个地方粉条对搜索单独处理
  fans->rank_page_strategy = FansPageStrategy::kExploreStrategy;
  if (!is_valid_fanstop_request) {
    LOG_EVERY_N(WARNING, 10000) << "init fans_session_data failed, user_id:"
                                << ad_request.ad_user_info().id()
                                << ", llsid:" << get_llsid();
  }
  if (ad_request.search_info().new_double_style_val() > 0) {
    set_is_search_new_feed(true);
  }
  set_is_search_debug(engine_base::AdKconfUtil::searchDebugUserList()->count(get_user_id()) > 0);
  dot_perf->Interval(base::GetTimestamp() - search_now_ms, "ad_rank_search_fanstop");
  // 搜索广告请求级参数初始化
  set_search_log_freq(RankKconfUtil::searchLogFreq());
  std::string search_rank_exp_name = *RankKconfUtil::searchRankExpName();
  set_search_rank_exp_name(spdm_ctx.TryGetString(search_rank_exp_name, "base"));
  set_search_rank_bidword_model_exp_name(
      spdm_ctx.TryGetString("search_rank_bidword_model_exp_name", "base"));
  const auto& ab_names = RankKconfUtil::abSearchTestName()->data().ab_names();
  int32_t cnt = 0;
  for (const auto& ab_name : ab_names) {
    std::string group_name = spdm_ctx.TryGetString(ab_name, "default");
    set_search_ab_group_name(get_search_ab_group_name() + group_name + "|");
    // 加上实验数量的限制，防止组合爆炸
    if (++cnt >= 3) {
      break;
    }
  }
  // 明投开关
  // 搜索广告干预功能开关
  set_enable_search_intervene(!get_rank_request()->search_intervene_skip_rank());

  set_search_dot_query(ks::ad_target_search::GetQueryNeedDot(ad_request.search_info().query()));
}

void ContextData::GetModelScoreParams() {
  const auto& score_map =
      get_rank_request()->ad_request().ad_user_info().user_model_score().model_score_map();
  for (auto iter = score_map.begin(); iter != score_map.end(); iter++) {
    (*mutable_ind_model_score_map())[iter->first] = iter->second;
  }
}

void ContextData::GetIndustryExpParams() {
  if (!SPDM_enable_ind_deep_optimize(spdm_ctx)) {
    return;
  }
  auto ind_deep_product_map = RankKconfUtil::indDeepOptProductMap();
  auto& ind_product_deepmodel_map_ = *mutable_ind_product_deepmodel_map();

  for (auto supplier_data : get_rank_request()->ad_request().ad_user_info().supplier_data()) {
    auto& product_name = supplier_data.product_name();
    if (product_name.empty()) {
      continue;
    }
    auto product_map_iter = ind_deep_product_map->find(product_name);
    if (product_map_iter != ind_deep_product_map->end()) {
      std::vector<std::string> products = absl::StrSplit(product_map_iter->second, ',', absl::SkipEmpty());
      for (const auto& product : products) {
        IndustryDeepModelInfo ind_deep_model_info;
        ind_deep_model_info.pcvr = supplier_data.pcvr();
        ind_product_deepmodel_map_[product] = ind_deep_model_info;
      }
    } else {
      IndustryDeepModelInfo ind_deep_model_info;
      ind_deep_model_info.pcvr = supplier_data.pcvr();
      ind_product_deepmodel_map_[product_name] = ind_deep_model_info;
    }
  }

  if (SPDM_enable_use_supplier_v2((spdm_ctx))) {
    // 使用新的 PB 结构
    for (auto supplier_data : get_rank_request()->ad_request().ad_user_info().supplier_data_v2()) {
      if (supplier_data.supplier() != "model_lab" || supplier_data.industry_data().size() <= 0) {
        continue;
      }
      for (auto industry_data : supplier_data.industry_data()) {
        for (auto product_data : industry_data.product_data()) {
          auto& product_name = product_data.product_name();
          if (product_name.empty()) {
            continue;
          }
          auto product_map_iter = ind_deep_product_map->find(product_name);
          if (product_map_iter != ind_deep_product_map->end()) {
            std::vector<std::string> products =
                      absl::StrSplit(product_map_iter->second, ',', absl::SkipEmpty());
            for (const auto& product : products) {
              IndustryDeepModelInfo ind_deep_model_info;
              ind_deep_model_info.pcvr = static_cast<double>(product_data.pcvr() / 100);
              ind_product_deepmodel_map_[product] = ind_deep_model_info;
            }
          } else {
            IndustryDeepModelInfo ind_deep_model_info;
            ind_deep_model_info.pcvr = static_cast<double>(product_data.pcvr() / 100);
            ind_product_deepmodel_map_[product_name] = ind_deep_model_info;
          }
        }
      }
    }
  }
}

void ContextData::GetProductModelLabScore() {
  if (!SPDM_enable_product_model_lab_score(spdm_ctx)) {
    return;
  }
  for (auto supplier_data : get_rank_request()->ad_request().ad_user_info().supplier_data()) {
    if (supplier_data.supplier() != "model_lab" || supplier_data.product_name() != "合家和") {
      continue;
    }
    common_w_->SetDoubleCommonAttr("product_model_lab_score", supplier_data.pcvr());
    auto pcvr = common_r_->GetDoubleCommonAttr("product_model_lab_score").value_or(0.0);
  }
}

void ContextData::GetAllStrategyParams() {
  // 外循环 mcda pre-exp 白名单
  set_pre_mt_product_account(&RankKconfUtil::preMtProductAccount()->data());
  GetCtcvrParams();
  GetServerClientShowParams();
  GetProjectId();
  GetClientCpmRatio();
}

void ContextData::GetCtcvrParams() {
  // roas cvr 模型重置开关
  set_neixunhuan_photo_roi_cvr_reset_value(
      spdm_ctx.TryGetDouble("neixunhuan_photo_roi_cvr_reset_value", 0.001));
}

void ContextData::GetServerClientShowParams() {
  // 保证 ab 参数在 pv-wise 和 ad-wise 一致
  set_enable_server_show_rate_unify(false);
}

void ContextData::GetProjectId() {
  // 获取 起量扶持工具 id
  set_support_project_exp_id(get_rank_request()->ad_request().support_project_info().support_project_exp_id());  // NOLINT
  set_support_project_id(get_rank_request()->ad_request().support_project_info().support_project_id());
}

void ContextData::GetClientCpmRatio() {
  auto gamora_enable_mix_list_rerank_switch =
      spdm_ctx.TryGetBoolean("gamora_enable_mix_list_rerank_switch", false);
  auto enable_commercial_universe_mix_gamora = spdm_ctx.TryGetBoolean(
      "enable_commercial_universe_mix_gamora", false);
  auto nebula_enable_mix_rank_list =
      spdm_ctx.TryGetBoolean("nebula_enable_mix_rank_list", false);
  auto enable_commercial_universe_mix_nebula = spdm_ctx.TryGetBoolean(
      "enable_commercial_universe_mix_nebula", false);

  // 底导版精选页
  if (ks::ad_base::IsSelectedRequest(pos_manager_base.GetSubPageId())) {
    // 不走混排的流量
    if ((gamora_enable_mix_list_rerank_switch == 1 &&
         enable_commercial_universe_mix_gamora == 0) ||
        (gamora_enable_mix_list_rerank_switch == 0)) {
    } else {
      set_client_cpm_enable_switch(true);
    }
  } else if (ks::ad_base::IsNebulaExploreRequest(
                 pos_manager_base.GetSubPageId())) {  // 极速版发现页
    if ((nebula_enable_mix_rank_list == 1 &&
         enable_commercial_universe_mix_nebula == 0) ||
        (nebula_enable_mix_rank_list == 0)) {
    } else {
      set_client_cpm_enable_switch(true);
    }
  }
}

void ContextData::FillMultiRetrievalCmd() {
  // 先填充 multi_retrieval_cmd
  for (auto iter : get_rank_request()->multi_retrieval_info()) {
    (*mutable_multi_retrieval_cmd()).emplace(iter.tag(), iter.index_name());
  }
}

void ContextData::DotMultiTag(const std::string& metric_key, const std::string& color_name,
                              const std::unordered_map<int32_t, int32_t>& retr_tag_map) {
  for (const auto& pair : retr_tag_map) {
    auto cmd_iter = get_multi_retrieval_cmd().find(pair.first);
    if (cmd_iter != get_multi_retrieval_cmd().end()) {
      const std::string& cmd = cmd_iter->second;
      // 打点监控
      if (pair.second > 0) {  // NOLINT
        auto maybe_tag = ks::ad_target::multi_retr::RetrievalTag::_from_integral_nothrow(pair.first);  // NOLINT
        if (!maybe_tag) {
          continue;
        }
        const std::string& tag_string = maybe_tag->_to_string();  // NOLINT
        dot_perf->Interval(pair.second, metric_key, tag_string, cmd);
        (*mutable_multi_color_data()).Color(pair.first, color_name, pair.second);
      }
    }
  }

  return;
}

void ContextData::InitMultiColor(const kuaishou::ad::AdRequest& ad_request) {
  if (ad_request.debug_param().debug_mode() && ad_request.debug_param().multi_tag_id() > 0) {
    (*mutable_multi_color_data()).tag_id = ad_request.debug_param().multi_tag_id();
  }
}

// 获取 abtest 平台的 广告形态数据
void ContextData::FillAbTestData() {
  auto ab_test_for_ad_forms = RankKconfUtil::abtestForAdForms();
  if (ab_test_for_ad_forms != nullptr) {
    JsonObject json;
    for (auto ab_test_for_ad_form : *ab_test_for_ad_forms) {
      switch (ab_test_for_ad_form.second) {
        case 1:
          json.set(ab_test_for_ad_form.first,
                    spdm_ctx.TryGetBoolean(ab_test_for_ad_form.first, false));
          break;
        case 2:
          json.set(ab_test_for_ad_form.first,
                    spdm_ctx.TryGetInteger(ab_test_for_ad_form.first, 0));
          break;
        case 3:
          json.set(ab_test_for_ad_form.first,
                    spdm_ctx.TryGetString(ab_test_for_ad_form.first, ""));
          break;
        default:
          break;
      }
    }
    std::string ab_test_for_ad_forms_{};  //  广告形态的 abtest 数据的 jsonstring
    if (json.get()) {
      ab_test_for_ad_forms_ = base::JsonToString(json.get());
    }
    set_ab_test_hash_id(std::hash<std::string>()(ab_test_for_ad_forms_));
  }
}

void ContextData::FillPecStyleAbTestData(const std::shared_ptr<std::map<std::string, long>>& ab_test_for_ad_forms,  // NOLINT
                            const std::string& item_type_str) {
  if (ab_test_for_ad_forms != nullptr) {
    JsonObject json;
    for (auto ab_test_for_ad_form : *ab_test_for_ad_forms) {
      switch (ab_test_for_ad_form.second) {
        case 1:
          json.set(ab_test_for_ad_form.first,
                    spdm_ctx.TryGetBoolean(ab_test_for_ad_form.first, false));
          break;
        case 2:
          json.set(ab_test_for_ad_form.first,
                    spdm_ctx.TryGetInteger(ab_test_for_ad_form.first, 0));
          break;
        case 3:
          json.set(ab_test_for_ad_form.first,
                    spdm_ctx.TryGetString(ab_test_for_ad_form.first, ""));
          break;
        default:
          break;
      }
    }
    std::string ab_test_for_ad_forms_str{};
    if (json.get()) {
      ab_test_for_ad_forms_str = base::JsonToString(json.get());
    }
    if (item_type_str == "live") {
      set_qcpx_live_pec_style_abtest_hash_id(std::hash<std::string>()(ab_test_for_ad_forms_str));  // NOLINT
    }
    if (item_type_str == "p2l_soft") {
      set_qcpx_p2l_soft_pec_style_abtest_hash_id(std::hash<std::string>()(ab_test_for_ad_forms_str));  // NOLINT
    }
    if (item_type_str == "p2l_hard") {
      set_qcpx_p2l_hard_pec_style_abtest_hash_id(std::hash<std::string>()(ab_test_for_ad_forms_str));  // NOLINT
    }
  }
}

// 直播 - 获取 QCPX 券样式 abtest 参数
void ContextData::FillQcpxLiveAndP2lPecStyleAbTestData() {
  auto live_ab_test_for_ad_forms = RankKconfUtil::qcpxLivePecStyleAbtestForms();
  auto p2l_soft_ab_test_for_ad_forms = RankKconfUtil::qcpxP2lSoftPecStyleAbtestForms();
  auto p2l_hard_ab_test_for_ad_forms = RankKconfUtil::qcpxP2lHardPecStyleAbtestForms();
  FillPecStyleAbTestData(live_ab_test_for_ad_forms, "live");
  FillPecStyleAbTestData(p2l_soft_ab_test_for_ad_forms, "p2l_soft");
  FillPecStyleAbTestData(p2l_hard_ab_test_for_ad_forms, "p2l_hard");
}

// 获取 QCPX abtest 参数
void ContextData::FillQcpxAbtestParameterData() {
  auto qcpx_ab_test_for_ad_forms = RankKconfUtil::qcpxAbtestParameterForms();
  if (qcpx_ab_test_for_ad_forms != nullptr) {
    JsonObject json;
    for (auto qcpx_ab_test_for_ad_form : *qcpx_ab_test_for_ad_forms) {
      switch (qcpx_ab_test_for_ad_form.second) {
        case 1:
          json.set(qcpx_ab_test_for_ad_form.first,
                    spdm_ctx.TryGetBoolean(qcpx_ab_test_for_ad_form.first, false));
          break;
        case 2:
          json.set(qcpx_ab_test_for_ad_form.first,
                    spdm_ctx.TryGetInteger(qcpx_ab_test_for_ad_form.first, 0));
          break;
        case 3:
          json.set(qcpx_ab_test_for_ad_form.first,
                    spdm_ctx.TryGetString(qcpx_ab_test_for_ad_form.first, ""));
          break;
        default:
          break;
      }
    }
    std::string qcpx_ab_test_for_ad_forms_str{};
    if (json.get()) {
      qcpx_ab_test_for_ad_forms_str = base::JsonToString(json.get());
    }
    set_qcpx_abtest_param_hash_id(std::hash<std::string>()(qcpx_ab_test_for_ad_forms_str));
  }
}

void ContextData::FillItemRequestInfo(const kuaishou::ad::AdRequest& ad_request) {
  do {
    // Athena 统一请求样式, 100*, 200* 同时传入的话在 data_postproc 会有问题
    if (ad_request.universe_ad_request_info().imp_info_size() != 0) {
      for (int i = ad_request.universe_ad_request_info().imp_info_size() -1; i >= 0; --i) {
        const auto& imp = ad_request.universe_ad_request_info().imp_info(i);
        if (imp.ad_num() == 0) {
          continue;
        }
        for (int pos_index = 0; pos_index < imp.ad_num(); ++pos_index) {
          for (int32_t  cmt : imp.creative_material_types()) {
            this->mutable_creative_material_types()->insert(cmt);
          }
        }
      }
      break;
    }
  } while (false);
}

ContextData::~ContextData() {
  if (dot_perf) {
    delete dot_perf;
    dot_perf = nullptr;
  }
  spdm_ctx.Clear();
}

AdRankUnifyScene ContextData::GetUnifyScene() {
  if (get_is_feed()) {
    return AdRankUnifyScene::FEED;
  }
  return AdRankUnifyScene::FEED;
}

void ContextData::FillFreqInfo(const kuaishou::ad::AdRequest& ad_request) {
  if (!ad_request.ad_session_response_pack().response().has_user_action_info()) {
    return;
  }
  std::unordered_map<int64, std::set<int64,
                    std::greater<int64>>> merchant_product_clicked_map;  // 电商商品频控用于精排的过滤 map
  const auto &user_action_info =
      ad_request.ad_session_response_pack().response().user_action_info();
  int64 now_ms =  get_current_timestamp_nodiff() / 1000;
  int64_t freq_check_range = 3600;
  int64 check_start_ts_ms = now_ms - freq_check_range * 1000;
  std::vector<std::pair<int64, std::vector<int64>>> candidates;
  int64 seq_start_ts_ms = now_ms;
  int seq_check_len = 0;
  if (SPDM_enable_multi_imp_adjust(spdm_ctx)) {
    std::string seq_exp_tag = SPDM_seq_exp_tag(spdm_ctx);
    auto seq_adjust_conf = RankKconfUtil::seqAdjustConfV2();
    auto seq_itr = seq_adjust_conf->data().exp_config().find(seq_exp_tag);
    if (seq_itr != seq_adjust_conf->data().exp_config().end()) {
      set_seq_tag_config(&(seq_itr->second));
      seq_start_ts_ms -= seq_itr->second.check_second() * 1000;
      seq_check_len = seq_itr->second.check_len();
    }
  }
  int counter = 0;
  const auto second_industry_id_map = ks::engine_base::AdKconfUtil::secondIndustryIdMap();
  for (int64_t i = 0; i < user_action_info.ad_action_info_size(); i++) {
    auto ad_action_info = user_action_info.ad_action_info(i);
    for (int64_t j = 0; j < ad_action_info.ad_detail_info_size(); j++) {
      auto ad_detail_info = ad_action_info.ad_detail_info(j);
      if (ad_action_info.action_type() == kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION) {  // 曝光
        auto merchant_product_id = ad_detail_info.merchant_product_id();
        auto timestamp = ad_detail_info.timestamp();
        auto iter = merchant_product_clicked_map.find(merchant_product_id);
        if (merchant_product_id > 0 && iter != merchant_product_clicked_map.end() &&
          timestamp >= check_start_ts_ms) {
          iter->second.insert(timestamp);
        } else if (merchant_product_id > 0 && timestamp >= check_start_ts_ms) {
          std::set<int64, std::greater<int64>> tempset;
          tempset.insert(timestamp);
          merchant_product_clicked_map.emplace(merchant_product_id, tempset);
        }
        if (SPDM_enable_multi_imp_adjust(get_spdm_ctx()) && timestamp >= seq_start_ts_ms &&
            get_seq_tag_config() != nullptr &&
            (ad_detail_info.pos_id() == 27 || ad_detail_info.pos_id() == 108)) {
          std::vector<int64> ids;
          for (int i = 0; i < get_seq_tag_config()->tags_size(); ++i) {
            counter++;
            const std::string& tag = get_seq_tag_config()->tags(i);
            if (tag == "product") {
              ids.push_back(merchant_product_id);
            } else if (tag == "author") {
              ids.push_back(ad_detail_info.author_id());
            } else if (tag == "industry") {
              ids.push_back(ad_detail_info.industry_id());
            } else if (tag == "product_name") {
              ids.push_back(ad_detail_info.product_hash());
            } else if (tag == "first_industry" && second_industry_id_map != nullptr) {
              auto iter = second_industry_id_map->find(ad_detail_info.industry_id());
              if (iter != second_industry_id_map->end()) {
                ids.push_back(iter->second);
              }
            }
          }
          if (ids.size() == get_seq_tag_config()->tags_size()) {
            candidates.push_back(std::make_pair(timestamp, std::move(ids)));
          }
        }
      }
    }
  }

  for (auto it = merchant_product_clicked_map.begin(); it != merchant_product_clicked_map.end(); ++it) {
    auto product_id = it->first;
    double count = 0.0;
    for (auto timestamp : it->second) {
      counter++;
      count += 1.0;
    }
    (*mutable_merchant_product_clicked_count()).emplace(product_id, count);
  }
  if (SPDM_enable_multi_imp_adjust(get_spdm_ctx()) && candidates.size() > 0) {
    std::sort(candidates.rbegin(), candidates.rend());
    mutable_seq_check_ads_list()->resize(get_seq_tag_config()->tags_size());
    auto& seq_check_ads_list_ = *mutable_seq_check_ads_list();
    for (int i = 0; i < candidates.size() && i < seq_check_len; ++i) {
      for (int j = 0; j < candidates[i].second.size(); ++j) {
        counter++;
        int64 id = candidates[i].second[j];
        seq_check_ads_list_[j][id] += 1;
      }
    }
  }
  return;
}

size_t ContextData::GetAmdLiveAdTotalInAdList() {
  if (!get_is_rewarded()) {
    return 0;
  }

  size_t amd_live_total = 0;
  for (auto *p_ad : (*mutable_ad_list()).Ads()) {
    auto &ad = *p_ad;
    if (ad.get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {
      ++amd_live_total;
    }
  }
  return amd_live_total;
}


size_t ContextData::GetAmdLiveAdTotalInLiveAdList() {
  if (!get_is_rewarded()) {
    return 0;
  }

  size_t amd_live_total = 0;
  for (auto *p_ad : (*mutable_live_ad_list()).Ads()) {
    auto &ad = *p_ad;
    if (ad.get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {
      ++amd_live_total;
    }
  }
  return amd_live_total;
}

void ContextData::MixRankBidExpParamInit() {
  if (!get_is_thanos_mix_request()) {
    return;
  }
  set_is_dpp_crowd(0);
  set_treatment_tag("");
  if (get_rank_request() == nullptr) {
    return;
  }
  base::Json imp_json(base::StringToJson(get_rank_request()->ad_request().diversity_sensitive_user_info()));
  if (imp_json.IsObject()) {
    set_is_dpp_crowd(imp_json.GetInt("is_dpp_crowd", 0));
  }

  // 解析离线计算的 uplift 预估值
  bool enable_bid_uplift_strategy_new = SPDM_enable_bid_uplift_strategy_new(spdm_ctx);
  auto new_mix_rank_bid_exp_tag = SPDM_new_mix_rank_bid_exp_tag(spdm_ctx);
  bool enable_new_uplift_score_calc = SPDM_enable_new_uplift_score_calc(spdm_ctx);
  const std::string& treatment_uplift_user_info =
      get_rank_request()->ad_request().ad_user_info().treatment_uplift_user_info();
  auto mix_rank_bid_control_params_ptr = RankKconfUtil::mixRankBidControlParams();
  auto unify_adload_output_ptr = RankKconfUtil::unifyAdloadControlOutput();
  const auto& kv_map = unify_adload_output_ptr->data().kv_map;
  if (!treatment_uplift_user_info.empty() && mix_rank_bid_control_params_ptr != nullptr) {
    std::vector<std::string> treatment_info_vec = absl::StrSplit(treatment_uplift_user_info, ';');
    const auto& params_map = mix_rank_bid_control_params_ptr->data().params_map();
    double max_uplift_value = SPDM_mixBidUpliftThr();
    for (const auto& treatmemt_info : treatment_info_vec) {
      std::vector<std::string> uplift_value_vec = absl::StrSplit(treatmemt_info, ':');
      if (uplift_value_vec.size() == 5) {
        double delta_cost = 0.0;
        double delta_gmv = 0.0;
        double delta_gift = 0.0;
        double delta_time = 0.0;
        const std::string& treatment = uplift_value_vec[0];
        double temp_uplift_value = 0.0;
        double temp_uplift_effect = 0.0;
        std::string key = absl::Substitute("$0_$1", new_mix_rank_bid_exp_tag, treatment);
        if (params_map.count(key) == 0) continue;
        const auto& params = params_map.at(key);
        if (!absl::SimpleAtod(uplift_value_vec[1], &delta_cost)) {
          delta_cost = 0.0;
        }
        if (!absl::SimpleAtod(uplift_value_vec[2], &delta_gmv)) {
          delta_gmv = 0.0;
        }
        if (!absl::SimpleAtod(uplift_value_vec[3], &delta_gift)) {
          delta_gift = 0.0;
        }
        if (!absl::SimpleAtod(uplift_value_vec[4], &delta_time)) {
          delta_time = 0.0;
        }

        // uplift 策略调整
        if (!enable_bid_uplift_strategy_new) {
          temp_uplift_value = delta_cost + params.gmv_ratio() * delta_gmv + params.gift_ratio() * delta_gift +
                              params.time_ratio() * delta_time;
          if (temp_uplift_value >= params.uplift_thr() && temp_uplift_value > max_uplift_value) {
            max_uplift_value = temp_uplift_value;
            set_treatment_tag(treatment);
          }
        } else {
          if (kv_map.count(key) == 0) continue;
          double bid_uplift_value =  kv_map.at(key).server_show_output();
          if (bid_uplift_value > 0.001) {
            if (!enable_new_uplift_score_calc) {
              temp_uplift_value = delta_cost + params.gmv_ratio() + delta_gmv +
                                  params.gift_ratio() * delta_gift + params.time_ratio() * delta_time;
            } else {
              temp_uplift_value = delta_cost;
            }
            temp_uplift_effect = temp_uplift_value / bid_uplift_value;
            if (temp_uplift_value >= params.uplift_thr() && temp_uplift_effect > max_uplift_value) {
                max_uplift_value = temp_uplift_effect;
                set_treatment_tag(treatment);
            }
          }
        }
      }
    }
  }

  dot_perf->Count(1, "mix_rank_bid_exp_opt_treatment", get_treatment_tag(),
                  new_mix_rank_bid_exp_tag);
}

void ContextData::Clear() {
  // 受 dragon 析构影响， 此处按照如下顺序书写
  if (common_w_ == nullptr) {
    return;  // 未初始化, 不需要 Clear
  }
  // 1. 资源释放  ************************************************************
  ks::ad_base::ClientTagHelper::Instance().Clear();
  ad_list.Clear();
  live_ad_list.Clear();
  native_ad_list.Clear();
  fanstop_ad_list.Clear();
  // 强制释放 attr, 解决 attr 依赖的问题
  // 下面这几个变量必须早于 arena 释放
  auto *ad_rank_infos = mutable_ad_rank_infos();
  if (ad_rank_infos) ad_rank_infos->clear();
  auto *outer_loop_ad_rank = mutable_outer_loop_ad_rank_infos();
  if (outer_loop_ad_rank) outer_loop_ad_rank->Clear();
  auto *inner_loop_ad_rank = mutable_inner_loop_ad_rank_infos();
  if (inner_loop_ad_rank) inner_loop_ad_rank->Clear();
  auto* outer_candidate_builder = common_w_->
      GetMutablePtrCommonAttr<OuterloopLiveCandidateBuilder>("outer_loop_live_candidate_builder");
  if (outer_candidate_builder) outer_candidate_builder->Clear();

  // 2. 依托于本地变量的释放和重置  *********************************************
  common_w_ = nullptr;
  common_r_ = nullptr;
  if (dot_perf) {
    delete dot_perf;
    dot_perf = nullptr;
  }
  // 字符串类, 和头文件对齐
  app_version = "";  // 快手版本号
  auto_param_exp_group_ecpm_v2 = "";
  big_promotion_gpm_hc_exp_tag = "base";
  buyer_tier = "";
  chosen_gender_ = "";
  exp_group_name = "";
  platform = "";
  rank_ps_adjust_exp_tag = "";
  search_ab_group_name = "";
  search_dot_query = "";
  search_interactive_form = "";
  search_rank_bidword_model_exp_name = "";
  search_rank_exp_name = "";
  serialized_reco_user_info = "";
  total_tag = "base";
  unify_adload_tag = "base";
  treatment_tag = "";

  // 本地对象  grep "Access"  common/context_data-context_data.init.extra |awk -F'[&)]' '{print $(NF-1)}' |grep -v "ad_list$"|wc -l // NOLINT
  spdm_ctx.Clear();
  pos_manager_base.Clear();
  small_game_ab_test_values.clear();
  big_promotion_gpm_hc_exp_conf.Clear();
}

absl::flat_hash_map<std::string, kuaishou::ad::RankStageInfo::AdPredictStat>*
  ContextData::ad_predict_stat_ptr() {
  return mutable_ad_predict_stats_();
}

void ContextData::SetSwitches() {
  const auto& ad_request = get_rank_request()->ad_request();
  common_w_->SetIntCommonAttr("enable_qcpx_shelf_auction_operator",
    SPDM_enable_qcpx_shelf_auction_operator(spdm_ctx) && !SPDM_enable_qcpx_shelf_auction_operator_hold(spdm_ctx));  // NOLINT
  common_w_->SetIntCommonAttr("enable_tube_to_inner_loop_exp", SPDM_enable_tube_to_inner_loop_exp(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_shelf_req_ad_style_server", 1);
  // front 下移开关
  common_w_->SetIntCommonAttr("enable_front_auction_move_rank",
  ad_request.enable_front_auction_move_rank());
  common_w_->SetIntCommonAttr("enable_hard_soft_union",
  ad_request.enable_hard_soft_union());
  common_w_->SetIntCommonAttr("enable_ad_history_filter_id_unify",
  ad_request.enable_ad_history_filter_id_unify());
  common_w_->SetIntCommonAttr("enable_qcpx_p2p_switch",
  SPDM_enable_qcpx_p2p_switch(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_rank_quota_logic_split",
  SPDM_enable_rank_quota_logic_split(spdm_ctx) &&
  ks::ad_base::IsMixRankSupportType(get_sub_page_id()));
  common_w_->SetIntCommonAttr("enable_force_reco_mark_unify",
  SPDM_enable_force_reco_mark_unify(spdm_ctx));
  set_enable_unify_ecpm_bound(SPDM_enable_unify_ecpm_bound(spdm_ctx));
  set_ad_request_type(pos_manager_base.GetAdRequestType());
  set_enable_search_rank_build_predict_req_new(SPDM_enableSearchRankBuildPredictReqNew() &&
                                        SPDM_enable_search_rank_build_predict_req_new(spdm_ctx));
  set_enable_splash_rank_build_predict_req_new(SPDM_enableSearchRankBuildPredictReqNew() ||
                                        SPDM_enable_splash_rank_build_predict_req_new(spdm_ctx));
  set_enable_splash_rank_build_predict_resp_new(get_is_splash_request() &&
                                                get_enable_splash_rank_build_predict_req_new() &&
                                                SPDM_enable_splash_rank_build_predict_resp_new(spdm_ctx));

  set_enable_refactor_splash_boost_coef_prepare(SPDM_enable_refactor_splash_boost_coef_prepare(spdm_ctx));

  // 缓存内软开关
  common_w_->SetIntCommonAttr("enable_adlist_cache_inner_soft",
    SPDM_enable_adlist_cache_inner_soft(spdm_ctx));

  bool is_inner_explore = IsExploreFeedInnerTraffic();
  bool enable_feed_explore = ks::ad_base::IsFeedExploreRequest(pos_manager_base.GetSubPageId()) &&
                             SPDM_enable_feed_explore_skip_filter_opt(spdm_ctx);
  common_w_->SetIntCommonAttr("enable_explore_page_skip_filter_diversity",
      SPDM_enable_explore_page_skip_filter_diversity(spdm_ctx) &&
      (is_inner_explore || enable_feed_explore));
  common_w_->SetIntCommonAttr("enable_explore_page_skip_filter_other",
      SPDM_enable_explore_page_skip_filter_other(spdm_ctx) &&
      (is_inner_explore || enable_feed_explore));

  // fill_bid 逻辑后移开关
  common_w_->SetIntCommonAttr("enable_fill_bid_move_back", SPDM_enable_fill_bid_move_back(spdm_ctx));
  common_w_->SetIntCommonAttr("use_data_converter_v2",
    (SPDM_use_data_converter_v2_adrank(spdm_ctx) || SPDM_useDataConverterV2()));
  common_w_->SetIntCommonAttr("ad_router_send_request_timeout",
      SPDM_ad_router_send_request_timeout(spdm_ctx));

  set_disable_ecpm_strategy__1_next_stay_filter(
    SPDM_disable_ecpm_strategy__1_next_stay_filter(spdm_ctx));
  set_disable_ecpm_strategy__2_zhutui_thres(
    SPDM_disable_ecpm_strategy__2_zhutui_thres(spdm_ctx));
  set_disable_ecpm_strategy__3_juxing_thres(
    SPDM_disable_ecpm_strategy__3_juxing_thres(spdm_ctx));
  set_disable_ecpm_strategy__4_native_ecpm_upper_bound(
    SPDM_disable_ecpm_strategy__4_native_ecpm_upper_bound(spdm_ctx));
  set_disable_ecpm_strategy__5_follow_u4_zoom(
    SPDM_disable_ecpm_strategy__5_follow_u4_zoom(spdm_ctx));
  set_disable_ecpm_strategy__6_billing_separate(
    SPDM_disable_ecpm_strategy__6_billing_separate(spdm_ctx));
  set_enable_search_rank_predict_replace(SPDM_enableSearchRankPredictReplace());
  set_enable_search_rank_build_predict_resp_new(get_is_search_request() &&
                                                SPDM_enable_search_rank_build_predict_resp_new(spdm_ctx));
  set_disable_ecpm_strategy__7_old_ecpm_ratio(
    SPDM_disable_ecpm_strategy__7_old_ecpm_ratio(spdm_ctx));
  set_disable_ecpm_strategy__8_cpm_origin(
    SPDM_disable_ecpm_strategy__8_cpm_origin(spdm_ctx));
  set_disable_ecpm_strategy__9_same_product_penalty(
    SPDM_disable_ecpm_strategy__9_same_product_penalty(spdm_ctx));
  set_disable_ecpm_strategy__10_follow_charge_bonus(
    SPDM_disable_ecpm_strategy__10_follow_charge_bonus(spdm_ctx));
  set_enable_search_rank_predict_direct(SPDM_enable_search_rank_predict_direct(spdm_ctx));
  set_disable_ecpm_strategy__11_ecpc_ratio(
    SPDM_disable_ecpm_strategy__11_ecpc_ratio(spdm_ctx));
  set_disable_ecpm_strategy__12_ecpc_adjust_ratio(
    SPDM_disable_ecpm_strategy__12_ecpc_adjust_ratio(spdm_ctx));
  set_disable_ecpm_strategy__13_unify_ecpc(
    SPDM_disable_ecpm_strategy__13_unify_ecpc(spdm_ctx));
  set_enable_splash_rank_predict_direct(get_enable_splash_rank_build_predict_req_new()
    && get_enable_splash_rank_build_predict_resp_new()
    && SPDM_enable_splash_rank_predict_direct(spdm_ctx)
    && SPDM_enableSplashRankPredictDirect());
  set_disable_ecpm_strategy__14_calc_prepare(
    SPDM_disable_ecpm_strategy__14_calc_prepare(spdm_ctx));
  set_enable_ecpm_refactor(
    SPDM_enable_ecpm_refactor(spdm_ctx));
}  // ContextData::SetSwitches

}  // namespace ad_rank
}  // namespace ks
