#include "teams/ad/ad_rank/common/macro.h"
// auto gen private member 70 for AdCommon 

inline ks::engine_base::ResourceIdType* mutable_resource_type() {
  return Attr(ItemIdx::resource_type).GetMutablePtrValue<ks::engine_base::ResourceIdType>(attr_row_);
}
inline const ks::engine_base::ResourceIdType& get_resource_type() const {
  return *Attr(ItemIdx::resource_type).GetPtrValue<ks::engine_base::ResourceIdType>(attr_row_);
}

inline void set_cmd_item_callback_event(const kuaishou::ad::AdCallbackLog_EventType v) {
  Attr(ItemIdx::cmd_item_callback_event).SetIntValue(attr_row_, static_cast<int64_t>(v), false, false);
}
inline const kuaishou::ad::AdCallbackLog_EventType get_cmd_item_callback_event() const {
  return static_cast<kuaishou::ad::AdCallbackLog_EventType>(Attr(ItemIdx::cmd_item_callback_event).GetIntValue(attr_row_).value_or(0));
}

inline void set_enable_unify_rvalue(bool v) {
  Attr(ItemIdx::enable_unify_rvalue).SetIntValue(attr_row_, v, false, false);
}
inline bool get_enable_unify_rvalue() const {
  return Attr(ItemIdx::enable_unify_rvalue).GetIntValue(attr_row_).value_or(false);
}

inline void set_industry_hc_control(bool v) {
  Attr(ItemIdx::industry_hc_control).SetIntValue(attr_row_, v, false, false);
}
inline bool get_industry_hc_control() const {
  return Attr(ItemIdx::industry_hc_control).GetIntValue(attr_row_).value_or(false);
}

inline void set_is_account_ocpm_mcb_ocpx_deep(bool v) {
  Attr(ItemIdx::is_account_ocpm_mcb_ocpx_deep).SetIntValue(attr_row_, v, false, false);
}
inline bool get_is_account_ocpm_mcb_ocpx_deep() const {
  return Attr(ItemIdx::is_account_ocpm_mcb_ocpx_deep).GetIntValue(attr_row_).value_or(false);
}

inline void set_is_blocked_ad(bool v) {
  Attr(ItemIdx::is_blocked_ad).SetIntValue(attr_row_, v, false, false);
}
inline bool get_is_blocked_ad() const {
  return Attr(ItemIdx::is_blocked_ad).GetIntValue(attr_row_).value_or(false);
}

inline void set_is_wanhe_charge_action_type(bool v) {
  Attr(ItemIdx::is_wanhe_charge_action_type).SetIntValue(attr_row_, v, false, false);
}
inline bool get_is_wanhe_charge_action_type() const {
  return Attr(ItemIdx::is_wanhe_charge_action_type).GetIntValue(attr_row_).value_or(false);
}

inline void set_pid_service_key_id(int64_t v) {
  Attr(ItemIdx::pid_service_key_id).SetIntValue(attr_row_, v, false, false);
}
inline int64_t get_pid_service_key_id() const {
  return Attr(ItemIdx::pid_service_key_id).GetIntValue(attr_row_).value_or(0);
}

inline void set_esp_author_fans_calibration_rate(double v) {
  Attr(ItemIdx::esp_author_fans_calibration_rate).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_esp_author_fans_calibration_rate() const {
  return Attr(ItemIdx::esp_author_fans_calibration_rate).GetDoubleValue(attr_row_).value_or(1.0);
}

inline void set_ss_calibration_rate(double v) {
  Attr(ItemIdx::ss_calibration_rate).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_ss_calibration_rate() {
  return Attr(ItemIdx::ss_calibration_rate).GetDoubleValue(attr_row_).value_or(1.0);
}

inline void set_industry_hc_control_ratio(double v) {
  Attr(ItemIdx::industry_hc_control_ratio).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_industry_hc_control_ratio() const {
  return Attr(ItemIdx::industry_hc_control_ratio).GetDoubleValue(attr_row_).value_or(0.0);
}

inline void set_dnc_hc_control_ratio(double v) {
  Attr(ItemIdx::dnc_hc_control_ratio).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_dnc_hc_control_ratio() const {
  return Attr(ItemIdx::dnc_hc_control_ratio).GetDoubleValue(attr_row_).value_or(0.0);
}
inline void set_dnc_hc_control_key(const std::string& v) {
  dnc_hc_control_key = v;
  auto val = v; Attr(ItemIdx::dnc_hc_control_key).SetStringValue(attr_row_, std::move(val), false, false);
}
inline const std::string& get_dnc_hc_control_key() const {
  return dnc_hc_control_key;
}
inline void set_dnc_hc_control(bool v) {
  Attr(ItemIdx::dnc_hc_control).SetIntValue(attr_row_, v, false, false);
}
inline bool get_dnc_hc_control() const {
  return Attr(ItemIdx::dnc_hc_control).GetIntValue(attr_row_).value_or(false);
}

inline void set_inner_account_bs_control_ratio(double v) {
  Attr(ItemIdx::inner_account_bs_control_ratio).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_inner_account_bs_control_ratio() const {
  return Attr(ItemIdx::inner_account_bs_control_ratio).GetDoubleValue(attr_row_).value_or(1.0);
}

inline void set_side_window_cross_section_pid_ratio(double v) {
  Attr(ItemIdx::side_window_cross_section_pid_ratio).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_side_window_cross_section_pid_ratio() const {
  return Attr(ItemIdx::side_window_cross_section_pid_ratio).GetDoubleValue(attr_row_).value_or(1.0);
}

inline void set_smb_billing_control_ratio(double v) {
  Attr(ItemIdx::smb_billing_control_ratio).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_smb_billing_control_ratio() const {
  return Attr(ItemIdx::smb_billing_control_ratio).GetDoubleValue(attr_row_).value_or(1.0);
}

// predict_bound_checker just declare funcs, impl in cc.inl
void set_predict_bound_checker(PsBoundChecker * v);
const PsBoundChecker * get_predict_bound_checker() const;

inline void set_queue_type(const RankAdListType v) {
  Attr(ItemIdx::queue_type).SetIntValue(attr_row_, static_cast<int64_t>(v), false, false);
}
inline const RankAdListType get_queue_type() const {
  return static_cast<RankAdListType>(Attr(ItemIdx::queue_type).GetIntValue(attr_row_).value_or(static_cast<int64_t>(RankAdListType::UNKNOWN)));
}

inline void set_ad_priority(const kuaishou::ad::AdEnum::AdPriority v) {
  Attr(ItemIdx::ad_priority).SetIntValue(attr_row_, static_cast<int64_t>(v), false, false);
}
inline const kuaishou::ad::AdEnum::AdPriority get_ad_priority() const {
  return static_cast<kuaishou::ad::AdEnum::AdPriority>(Attr(ItemIdx::ad_priority).GetIntValue(attr_row_).value_or(static_cast<int64_t>(kuaishou::ad::AdEnum::NORMAL_PRIORITY)));
}

inline kuaishou::ad::AdKboxInfo* mutable_ad_kbox_info() {
  return Attr(ItemIdx::ad_kbox_info).GetMutablePtrValue<kuaishou::ad::AdKboxInfo>(attr_row_);
}
inline const kuaishou::ad::AdKboxInfo& get_ad_kbox_info() const {
  return *Attr(ItemIdx::ad_kbox_info).GetPtrValue<kuaishou::ad::AdKboxInfo>(attr_row_);
}

inline std::bitset<MAX_FILTER_SIZE >* mutable_filter_skip_status() {
  return Attr(ItemIdx::filter_skip_status).GetMutablePtrValue<std::bitset<MAX_FILTER_SIZE >>(attr_row_);
}

inline void set_industry_hc_control_key(const std::string& v) {
  industry_hc_control_key = v;
  auto val = v; Attr(ItemIdx::industry_hc_control_key).SetStringValue(attr_row_, std::move(val), false, false);
}
inline const std::string& get_industry_hc_control_key() const {
  return industry_hc_control_key;
}

inline void set_transfer_pair(const std::string& v) {
  transfer_pair = v;
  auto val = v; Attr(ItemIdx::transfer_pair).SetStringValue(attr_row_, std::move(val), false, false);
}
inline const std::string& get_transfer_pair() const {
  return transfer_pair;
}

inline std::unordered_map<int64_t, AdBonusDetail >* mutable_bonus_detail_map() {
  return Attr(ItemIdx::bonus_detail_map).GetMutablePtrValue<std::unordered_map<int64_t, AdBonusDetail >>(attr_row_);
}
inline const std::unordered_map<int64_t, AdBonusDetail >& get_bonus_detail_map() const {
  return *Attr(ItemIdx::bonus_detail_map).GetPtrValue<std::unordered_map<int64_t, AdBonusDetail >>(attr_row_);
}

inline std::unordered_map<int64_t, double >* mutable_bcb_bonus_project() {
  return Attr(ItemIdx::bcb_bonus_project).GetMutablePtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}
inline const std::unordered_map<int64_t, double >& get_bcb_bonus_project() const {
  return *Attr(ItemIdx::bcb_bonus_project).GetPtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}

inline std::unordered_map<int64_t, double >* mutable_coin_quit_rate_map() {
  return Attr(ItemIdx::coin_quit_rate_map).GetMutablePtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}
inline const std::unordered_map<int64_t, double >& get_coin_quit_rate_map() const {
  return *Attr(ItemIdx::coin_quit_rate_map).GetPtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}

inline std::unordered_map<int64_t, double >* mutable_deep_coin_quit_rate_map() {
  return Attr(ItemIdx::deep_coin_quit_rate_map).GetMutablePtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}
inline const std::unordered_map<int64_t, double >& get_deep_coin_quit_rate_map() const {
  return *Attr(ItemIdx::deep_coin_quit_rate_map).GetPtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}

inline std::vector<int64_t>* mutable_reward_coin_treatment_list() {
  return Attr(ItemIdx::reward_coin_treatment_list).GetMutablePtrValue<std::vector<int64_t>>(attr_row_);
}
inline const std::vector<int64_t>& get_reward_coin_treatment_list() const {
  return *Attr(ItemIdx::reward_coin_treatment_list).GetPtrValue<std::vector<int64_t>>(attr_row_);
}

inline std::unordered_map<int64_t, double >* mutable_incentive_invoked_uplift_cvr() {
  return Attr(ItemIdx::incentive_invoked_uplift_cvr).GetMutablePtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}
inline const std::unordered_map<int64_t, double >& get_incentive_invoked_uplift_cvr() const {
  return *Attr(ItemIdx::incentive_invoked_uplift_cvr).GetPtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}

inline std::unordered_map<int64_t, double >* mutable_incentive_deep_reward_ratio() {
  return Attr(ItemIdx::incentive_deep_reward_ratio).GetMutablePtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}
inline const std::unordered_map<int64_t, double >& get_incentive_deep_reward_ratio() const {
  return *Attr(ItemIdx::incentive_deep_reward_ratio).GetPtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}

inline std::unordered_map<int64_t, double >* mutable_incentive_deep_uplift_ctr() {
  return Attr(ItemIdx::incentive_deep_uplift_ctr).GetMutablePtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}
inline const std::unordered_map<int64_t, double >& get_incentive_deep_uplift_ctr() const {
  return *Attr(ItemIdx::incentive_deep_uplift_ctr).GetPtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}

inline std::unordered_map<int64_t, double >* mutable_incentive_deep_uplift_cvr() {
  return Attr(ItemIdx::incentive_deep_uplift_cvr).GetMutablePtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}
inline const std::unordered_map<int64_t, double >& get_incentive_deep_uplift_cvr() const {
  return *Attr(ItemIdx::incentive_deep_uplift_cvr).GetPtrValue<std::unordered_map<int64_t, double >>(attr_row_);
}

inline std::vector<InnerLoopBonusInfo >* mutable_innerloop_bonus_info() {
  return Attr(ItemIdx::innerloop_bonus_info).GetMutablePtrValue<std::vector<InnerLoopBonusInfo >>(attr_row_);
}
inline const std::vector<InnerLoopBonusInfo >& get_innerloop_bonus_info() const {
  return *Attr(ItemIdx::innerloop_bonus_info).GetPtrValue<std::vector<InnerLoopBonusInfo >>(attr_row_);
}

inline std::vector<MultiFactorInfo >* mutable_factor_info() {
  return Attr(ItemIdx::factor_info).GetMutablePtrValue<std::vector<MultiFactorInfo >>(attr_row_);
}
inline const std::vector<MultiFactorInfo >& get_factor_info() const {
  return *Attr(ItemIdx::factor_info).GetPtrValue<std::vector<MultiFactorInfo >>(attr_row_);
}

inline std::vector<PidBonusInfo >* mutable_pid_bonus_info() {
  return Attr(ItemIdx::pid_bonus_info).GetMutablePtrValue<std::vector<PidBonusInfo >>(attr_row_);
}
inline const std::vector<PidBonusInfo >& get_pid_bonus_info() const {
  return *Attr(ItemIdx::pid_bonus_info).GetPtrValue<std::vector<PidBonusInfo >>(attr_row_);
}

inline std::vector<RecruitInfo >* mutable_recruit_info_list() {
  return Attr(ItemIdx::recruit_info_list).GetMutablePtrValue<std::vector<RecruitInfo >>(attr_row_);
}
inline const std::vector<RecruitInfo >& get_recruit_info_list() const {
  return *Attr(ItemIdx::recruit_info_list).GetPtrValue<std::vector<RecruitInfo >>(attr_row_);
}

inline std::vector<double>* mutable_uplift_subsidy_list() {
  return Attr(ItemIdx::uplift_subsidy_list).GetMutablePtrValue<std::vector<double>>(attr_row_);
}
inline const std::vector<double>& get_uplift_subsidy_list() const {
  return *Attr(ItemIdx::uplift_subsidy_list).GetPtrValue<std::vector<double>>(attr_row_);
}

inline std::vector<double>* mutable_uplift_conv_pay_list() {
  return Attr(ItemIdx::uplift_conv_pay_list).GetMutablePtrValue<std::vector<double>>(attr_row_);
}
inline const std::vector<double>& get_uplift_conv_pay_list() const {
  return *Attr(ItemIdx::uplift_conv_pay_list).GetPtrValue<std::vector<double>>(attr_row_);
}

inline std::vector<double>* mutable_uplift_industry_conv_ltv_list() {
  return Attr(ItemIdx::uplift_industry_conv_ltv_list).GetMutablePtrValue<std::vector<double>>(attr_row_);
}
inline const std::vector<double>& get_uplift_industry_conv_ltv_list() const {
  return *Attr(ItemIdx::uplift_industry_conv_ltv_list).GetPtrValue<std::vector<double>>(attr_row_);
}

inline std::vector<ks::ad_base::LogicQueueType >* mutable_logic_queue_type() {
  return Attr(ItemIdx::logic_queue_type).GetMutablePtrValue<std::vector<ks::ad_base::LogicQueueType >>(attr_row_);
}
inline const std::vector<ks::ad_base::LogicQueueType >& get_logic_queue_type() const {
  return *Attr(ItemIdx::logic_queue_type).GetPtrValue<std::vector<ks::ad_base::LogicQueueType >>(attr_row_);
}

inline void set_valid(bool v) {
  Attr(ItemIdx::valid).SetIntValue(attr_row_, v, false, false);
}
inline bool get_valid() const {
  return Attr(ItemIdx::valid).GetIntValue(attr_row_).value_or(true);
}

inline void set_is_trace(bool v) {
  Attr(ItemIdx::is_trace).SetIntValue(attr_row_, v, false, false);
}
inline bool get_is_trace() const {
  return Attr(ItemIdx::is_trace).GetIntValue(attr_row_).value_or(true);
}

inline void set_enable_skip_cmd_request(bool v) {
  Attr(ItemIdx::enable_skip_cmd_request).SetIntValue(attr_row_, v, false, false);
}
inline bool get_enable_skip_cmd_request() const {
  return Attr(ItemIdx::enable_skip_cmd_request).GetIntValue(attr_row_).value_or(false);
}

inline std::vector<AdCommon * >* mutable_sub_predict_ad_vec() {
  return Attr(ItemIdx::sub_predict_ad_vec).GetMutablePtrValue<std::vector<AdCommon * >>(attr_row_);
}
inline const std::vector<AdCommon * >& get_sub_predict_ad_vec() const {
  return *Attr(ItemIdx::sub_predict_ad_vec).GetPtrValue<std::vector<AdCommon * >>(attr_row_);
}

inline RUnifyInfo* mutable_unify_ctr_info() {
  return Attr(ItemIdx::unify_ctr_info).GetMutablePtrValue<RUnifyInfo>(attr_row_);
}
inline const RUnifyInfo& get_unify_ctr_info() const {
  return *Attr(ItemIdx::unify_ctr_info).GetPtrValue<RUnifyInfo>(attr_row_);
}

inline RUnifyInfo* mutable_unify_cvr_info() {
  return Attr(ItemIdx::unify_cvr_info).GetMutablePtrValue<RUnifyInfo>(attr_row_);
}
inline const RUnifyInfo& get_unify_cvr_info() const {
  return *Attr(ItemIdx::unify_cvr_info).GetPtrValue<RUnifyInfo>(attr_row_);
}

inline RUnifyInfo* mutable_unify_deep_cvr_info() {
  return Attr(ItemIdx::unify_deep_cvr_info).GetMutablePtrValue<RUnifyInfo>(attr_row_);
}
inline const RUnifyInfo& get_unify_deep_cvr_info() const {
  return *Attr(ItemIdx::unify_deep_cvr_info).GetPtrValue<RUnifyInfo>(attr_row_);
}

inline RUnifyInfo* mutable_unify_ltv_info() {
  return Attr(ItemIdx::unify_ltv_info).GetMutablePtrValue<RUnifyInfo>(attr_row_);
}
inline const RUnifyInfo& get_unify_ltv_info() const {
  return *Attr(ItemIdx::unify_ltv_info).GetPtrValue<RUnifyInfo>(attr_row_);
}
inline const RUnifyInfo& get_unify_ltv7_info() const {
  return *Attr(ItemIdx::unify_ltv7_info).GetPtrValue<RUnifyInfo>(attr_row_);
}
inline RUnifyInfo* mutable_unify_sctr_info() {
  return Attr(ItemIdx::unify_sctr_info).GetMutablePtrValue<RUnifyInfo>(attr_row_);
}
inline const RUnifyInfo& get_unify_sctr_info() const {
  return *Attr(ItemIdx::unify_sctr_info).GetPtrValue<RUnifyInfo>(attr_row_);
}

inline const absl::flat_hash_map<std::string, double >& get_calibrate_value() const {
  return *Attr(ItemIdx::calibrate_value).GetPtrValue<absl::flat_hash_map<std::string, double >>(attr_row_);
}

inline void set_offcali_cvr_modelstart(const std::string& v) {
  offcali_cvr_modelstart = v;
  auto val = v; Attr(ItemIdx::offcali_cvr_modelstart).SetStringValue(attr_row_, std::move(val), false, false);
}
inline const std::string& get_offcali_cvr_modelstart() const {
  return offcali_cvr_modelstart;
}

inline void set_offcali_cvr_modelend(const std::string& v) {
  offcali_cvr_modelend = v;
  auto val = v; Attr(ItemIdx::offcali_cvr_modelend).SetStringValue(attr_row_, std::move(val), false, false);
}
inline const std::string& get_offcali_cvr_modelend() const {
  return offcali_cvr_modelend;
}

inline void set_unify_ecpm_ratio(double v) {
  Attr(ItemIdx::unify_ecpm_ratio).SetDoubleValue(attr_row_, v, false, false);
}
inline double get_unify_ecpm_ratio() const {
  return Attr(ItemIdx::unify_ecpm_ratio).GetDoubleValue(attr_row_).value_or(1.0);
}

inline std::unordered_set<int32_t >* mutable_req_cmd_ids() {
  return Attr(ItemIdx::req_cmd_ids).GetMutablePtrValue<std::unordered_set<int32_t >>(attr_row_);
}
inline const std::unordered_set<int32_t >& get_req_cmd_ids() const {
  return *Attr(ItemIdx::req_cmd_ids).GetPtrValue<std::unordered_set<int32_t >>(attr_row_);
}

inline std::unordered_map<int32_t, int32_t >* mutable_req_cmd_key_ids() {
  return Attr(ItemIdx::req_cmd_key_ids).GetMutablePtrValue<std::unordered_map<int32_t, int32_t >>(attr_row_);
}
inline const std::unordered_map<int32_t, int32_t >& get_req_cmd_key_ids() const {
  return *Attr(ItemIdx::req_cmd_key_ids).GetPtrValue<std::unordered_map<int32_t, int32_t >>(attr_row_);
}

inline absl::flat_hash_map<int, PredictScoreMeta >* mutable_predict_score_list() {
  return Attr(ItemIdx::predict_score_list).GetMutablePtrValue<absl::flat_hash_map<int, PredictScoreMeta >>(attr_row_);
}
inline const absl::flat_hash_map<int, PredictScoreMeta >& get_predict_score_list() const {
  return *Attr(ItemIdx::predict_score_list).GetPtrValue<absl::flat_hash_map<int, PredictScoreMeta >>(attr_row_);
}

inline absl::flat_hash_map<int, PredictScoreMeta >* mutable_native_predict_score_list() {
  return Attr(ItemIdx::native_predict_score_list).GetMutablePtrValue<absl::flat_hash_map<int, PredictScoreMeta >>(attr_row_);
}
inline const absl::flat_hash_map<int, PredictScoreMeta >& get_native_predict_score_list() const {
  return *Attr(ItemIdx::native_predict_score_list).GetPtrValue<absl::flat_hash_map<int, PredictScoreMeta >>(attr_row_);
}

inline absl::flat_hash_map<int32_t, RScoreMeta >* mutable_r_score_list() {
  return Attr(ItemIdx::r_score_list).GetMutablePtrValue<absl::flat_hash_map<int32_t, RScoreMeta >>(attr_row_);
}
inline const absl::flat_hash_map<int32_t, RScoreMeta >& get_r_score_list() const {
  return *Attr(ItemIdx::r_score_list).GetPtrValue<absl::flat_hash_map<int32_t, RScoreMeta >>(attr_row_);
}

inline absl::flat_hash_map<int, const kuaishou::ad::ContextInfoCommonAttr*>* mutable_predict_embedding_attr_list() {
  return Attr(ItemIdx::predict_embedding_attr_list).GetMutablePtrValue<absl::flat_hash_map<int, const kuaishou::ad::ContextInfoCommonAttr*>>(attr_row_);
}

inline const absl::flat_hash_map<int, const kuaishou::ad::ContextInfoCommonAttr*>& get_predict_embedding_attr_list() {
  return *Attr(ItemIdx::predict_embedding_attr_list).GetPtrValue<absl::flat_hash_map<int, const kuaishou::ad::ContextInfoCommonAttr*>>(attr_row_);
}

inline void set_sdpa_third_category_id(int64_t v) {
  Attr(ItemIdx::sdpa_third_category_id).SetIntValue(attr_row_, v, false, false);
}
inline int64_t get_sdpa_third_category_id() const {
  return Attr(ItemIdx::sdpa_third_category_id).GetIntValue(attr_row_).value_or(0);
}

inline void set_sdpa_category_id(int64_t v) {
  Attr(ItemIdx::sdpa_category_id).SetIntValue(attr_row_, v, false, false);
}
inline int64_t get_sdpa_category_id() const {
  return Attr(ItemIdx::sdpa_category_id).GetIntValue(attr_row_).value_or(0);
}

inline void set_clue_sdpa_category_id(int64_t v) {
  Attr(ItemIdx::clue_sdpa_category_id).SetIntValue(attr_row_, v, false, false);
}
inline int64_t get_clue_sdpa_category_id() const {
  return Attr(ItemIdx::clue_sdpa_category_id).GetIntValue(attr_row_).value_or(0);
}

inline void set_stable_category_third_id(int64_t v) {
  Attr(ItemIdx::stable_category_third_id).SetIntValue(attr_row_, v, false, false);
}
inline int64_t get_stable_category_third_id() const {
  return Attr(ItemIdx::stable_category_third_id).GetIntValue(attr_row_).value_or(0);
}

inline void set_spu_id_v3(int64_t v) {
  Attr(ItemIdx::spu_id_v3).SetIntValue(attr_row_, v, false, false);
}
inline int64_t get_spu_id_v3() const {
  return Attr(ItemIdx::spu_id_v3).GetIntValue(attr_row_).value_or(0);
}

inline void set_global_app_id(int64_t v) {
  Attr(ItemIdx::global_app_id).SetIntValue(attr_row_, v, false, false);
}
inline int64_t get_global_app_id() const {
  return Attr(ItemIdx::global_app_id).GetIntValue(attr_row_).value_or(0);
}

inline void set_is_aggr_replace(bool v) {
  Attr(ItemIdx::is_aggr_replace).SetIntValue(attr_row_, v, false, false);
}
inline bool get_is_aggr_replace() const {
  return Attr(ItemIdx::is_aggr_replace).GetIntValue(attr_row_).value_or(false);
}

inline absl::flat_hash_map<int32_t, RScoreMeta >* mutable_native_r_score_list() {
  return Attr(ItemIdx::native_r_score_list).GetMutablePtrValue<absl::flat_hash_map<int32_t, RScoreMeta >>(attr_row_);
}
inline const absl::flat_hash_map<int32_t, RScoreMeta >& get_native_r_score_list() const {
  return *Attr(ItemIdx::native_r_score_list).GetPtrValue<absl::flat_hash_map<int32_t, RScoreMeta >>(attr_row_);
}

inline std::vector<double>* mutable_shelf_order_pay_cvr_pos() {
  return Attr(ItemIdx::shelf_order_pay_cvr_pos).GetMutablePtrValue<std::vector<double>>(attr_row_);
}
inline const std::vector<double>& get_shelf_order_pay_cvr_pos() const {
  return *Attr(ItemIdx::shelf_order_pay_cvr_pos).GetPtrValue<std::vector<double>>(attr_row_);
}

inline std::vector<double>* mutable_shelf_merchant_cpm_pos() {
  return Attr(ItemIdx::shelf_merchant_cpm_pos).GetMutablePtrValue<std::vector<double>>(attr_row_);
}
inline const std::vector<double>& get_shelf_merchant_cpm_pos() const {
  return *Attr(ItemIdx::shelf_merchant_cpm_pos).GetPtrValue<std::vector<double>>(attr_row_);
}

inline void set_is_up_items(bool v) {
  Attr(ItemIdx::is_up_items_test).SetIntValue(attr_row_, v, false, false);
}
inline bool get_is_up_items() const {
  return Attr(ItemIdx::is_up_items_test).GetIntValue(attr_row_).value_or(false);
}

inline std::vector<double>* mutable_inner_title_select_ctr_style() {
  return Attr(ItemIdx::inner_title_select_ctr_style).GetMutablePtrValue<std::vector<double>>(attr_row_);
}
inline const std::vector<double>& get_inner_title_select_ctr_style() const {
  return *Attr(ItemIdx::inner_title_select_ctr_style).GetPtrValue<std::vector<double>>(attr_row_);
}

ENUM_ITEM_ATTR(qpon_type, kuaishou::ad::AdEnum_QponType)
ENUM_ITEM_ATTR(qpon_cost_sharing_type, kuaishou::ad::AdEnum::QponCostSharingType)
ENUM_ITEM_ATTR(replaced_ad_queue_type, kuaishou::ad::AdEnum::AdQueueType)
ENUM_ITEM_ATTR(aggregate_mode, kuaishou::ad::AdEnum::AccountAggregateMode)

BOOL_ITEM_ATTR(has_qpon)
BOOL_ITEM_ATTR(is_ad_replaced)
BOOL_ITEM_ATTR(aggr_bidding_has_x_info)
BOOL_ITEM_ATTR_GETTER(has_deep_cvr)

INT_ITEM_ATTR(qpon_auction_bid_org, 0)
INT_ITEM_ATTR(qpon_cpm_org, 0)
INT_ITEM_ATTR(qpon_price_calibration, 0)
INT_ITEM_ATTR(replaced_creative_id, 0)
INT_ITEM_ATTR(aggr_bidding_x_account_id, 0)
INT_ITEM_ATTR(aggr_bidding_x_creative_id, 0)
INT_ITEM_ATTR(aggr_bidding_x_photo_id, 0)
INT_ITEM_ATTR(aggr_bidding_x_site_id, 0)
INT_ITEM_ATTR(create_source_type, 0)
INT_ITEM_ATTR(brand_name_hash, 0)
INT_ITEM_ATTR(unit_put_type, 0)
INT_ITEM_ATTR_GETTER(unify_server_client_show_rate_tag, 0)
INT_ITEM_ATTR_GETTER(has_deep_cvr_tag, 0)
INT_ITEM_ATTR_GETTER(ecpm_upper_bound, INT64_MAX)
INT_ITEM_ATTR_GETTER(ecpm_lower_bound, 0)
INT_ITEM_ATTR_GETTER(ecpm_upper_bound_tag, 0)
INT_ITEM_ATTR_GETTER(ecpm_lower_bound_tag, 0)

DOUBLE_ITEM_ATTR(uplift_cpm_ratio, 0.)
DOUBLE_ITEM_ATTR(bid_qpon_ratio, 0.)
DOUBLE_ITEM_ATTR(uplift_ctr_ratio, 0.)
DOUBLE_ITEM_ATTR(uplift_cvr_ratio, 0.)
DOUBLE_ITEM_ATTR(outerloop_ee_boost_ratio, 0.)
DOUBLE_ITEM_ATTR(ad_direct_merchant_price_ratio, 0.)
DOUBLE_ITEM_ATTR(ecpc_max_min_ratio, 0.)
DOUBLE_ITEM_ATTR_GETTER(unify_server_client_show_rate, 1.)

INT_LIST_ITEM_ATTR_GETTER(ecpm_adjust_list)
INT_LIST_ITEM_ATTR_GETTER(ecpm_adjust_tag_list)
