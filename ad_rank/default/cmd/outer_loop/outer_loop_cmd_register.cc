#include <vector>
#include <unordered_set>
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_rank/default/cmd/outer_loop/outer_loop_cmd_register.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"


namespace ks {
namespace ad_rank {
using kuaishou::ad::AdEnum;
using kuaishou::ad::AdActionType;
using ks::engine_base::PredictType;

// cmd  新范式
OuterCmdRegister::OuterCmdRegister(CmdCuratorContext* context) : cmd_curator_ctx_(context) {
}

CuratorTypeV2 *OuterCmdRegister::CreateCmdCurator() const {
  auto p_cmd_curator = new CuratorTypeV2(cmd_curator_ctx_);

  RegisterCtrCmd(p_cmd_curator);
  RegisterCvrCmd(p_cmd_curator);
  RegisterDeepCvrCmd(p_cmd_curator);
  RegisterLtvCmd(p_cmd_curator);
  RegisterAucCmd(p_cmd_curator);
  RegisterOtherCmd(p_cmd_curator);
  return p_cmd_curator;
}

void OuterCmdRegister::RegisterCtrCmd(CuratorTypeV2 *p_cmd_curator) const {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_server_show_ctr,
                       "ad_dsp_server_show_item_imp_ocpm_rewarded",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->session_data_->get_is_rewarded() &&
            !p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() &&
            !RankKconfUtil::incentiveSctrPredSupPages()->count(p_ctx->session_data_->get_sub_page_id()) &&
            p_ctx->session_data_->get_is_thanos_request()) {
          return false;
        }
        if (((p_ctx->session_data_->get_is_rewarded())) &&
            ((p_ctx->IsOcpc2Unit(p_ad) || p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM) &&
             !(p_ad->get_ad_source_type() == kuaishou::ad::ADX) &&
             (p_ad->get_creative_circulation_type() == kuaishou::ad::tables::CreativeExtendScore_CreativeCirculationType_EXTERNAL_CIRCULATION_TYPE))) {  // NOLINT
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_server_show_ctr,
                       "ad_dsp_server_show_item_imp_ocpm",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->session_data_->get_is_thanos_mix_request() &&
            SPDM_close_ad_dsp_server_show_item_imp_ocpm(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }

        if (((!(p_ctx->session_data_->get_is_rewarded()) &&
            !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            ((p_ctx->IsOcpc2Unit(p_ad) || p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM) &&
              !(p_ad->get_ad_source_type() == kuaishou::ad::ADX) &&
              !(p_ad->Is(AdFlag::is_whole_ecom_ad)) &&
              !(p_ad->Is(AdFlag::is_ad_merchant_order)) &&
              !(p_ad->Is(AdFlag::is_reco_roas))) &&
              !p_ctx->session_data_->get_client_cpm_enable_switch() &&
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
              (p_ctx->fix_hard_unify_sctr_ && (p_ad->get_ocpx_action_type() ==
                kuaishou::ad::AD_PURCHASE_CONVERSION ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CID_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT)) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) ||
              (p_ad->Is(AdFlag::is_reco_roas)) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) ||
              (p_ad->get_ad_source_type() == kuaishou::ad::ADX &&
               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
              (p_ad->Is(AdFlag::is_ad_watch_times)) ||
              (p_ad->Is(AdFlag::is_ad_watch_5_times) || p_ad->Is(AdFlag::is_ad_watch_10_times) || p_ad->Is(AdFlag::is_ad_watch_20_times)) ||  // NOLINT
              (p_ad->Is(AdFlag::is_multi_conv)) ||
              (p_ad->Is(AdFlag::is_7_day_pay_times)) ||
              (p_ad->Is(AdFlag::is_game_roas)) ||
              (p_ad->Is(AdFlag::is_pop_recruit_ad)) ||
              (p_ad->Is(AdFlag::is_self_service_ad)) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK) ||
              ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) &&
               p_ctx->fix_hard_unify_sctr_click_) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) ||
              (p_ad->get_deep_conversion_type()
                  == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
                 p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
              (p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
               p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                 p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) ||
              (p_ctx->session_data_->get_pos_manager_base().IsMicroAppRequest())) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_server_show_ctr,
                       "dpa_ad_dsp_server_show_item_imp_ecom",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
          (p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_dpa))) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_server_show_ctr,
                       "ad_dsp_server_show_item_imp_ecom",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->session_data_->get_is_thanos_mix_request() &&
            SPDM_close_ad_dsp_server_show_item_imp_ecom(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
          ((p_ctx->IsOcpc2Unit(p_ad) || p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM) &&
          !(p_ad->get_ad_source_type() == kuaishou::ad::ADX) &&
          !(p_ad->Is(AdFlag::is_dpa)) &&
          !(p_ctx->session_data_->get_is_rewarded())) &&
          ((!p_ad->Is(AdFlag::is_direct_ecom) && p_ad->Is(AdFlag::is_whole_ecom_ad)) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT))) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_server_client_show_rate,
                       "ad_dsp_server_client_show",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
          (p_ctx->session_data_->get_pos_manager_base().IsMicroAppRequest() ||
          p_ctx->session_data_->get_is_feed())) &&
          (!(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
          p_ad->Is(AdFlag::is_direct_ecom)) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_non_merchant_live_sctr,
                       "ad_dsp_live_photo_sctr",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->session_data_->get_is_rewarded() &&
            !p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() &&
            !RankKconfUtil::incentiveSctrPredSupPages()->count(p_ctx->session_data_->get_sub_page_id()) &&
            p_ctx->session_data_->get_is_thanos_request()) {
          return false;
        }
        if (((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() ==
              kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT))) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            p_ad->Is(AdFlag::is_self_service_ad)) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_server_show_ctr,
                       "ad_adx_thanos_sctr",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->session_data_->get_is_thanos_request() &&
          p_ad->get_ad_source_type() == kuaishou::ad::ADX
        ) {
          return true;
        }
        return false;
  });

    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_non_merchant_live_ctr,
                       "ad_dsp_item_imp_click_photo",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->Is(AdFlag::is_self_service_ad)) {
          return true;
        }
        return false;
  });
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_auc_model_base,
                       "ad_dsp_item_imp_click_photo_auc_base",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_dsplive_p2l_ctr_bypass_auc) {
          if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_p2l) &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_p2l) && p_ad->Is(AdFlag::is_self_service_ad)) {
            return true;
          }
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "ad_dsp_item_imp_click_photo_auc_exp",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_dsplive_p2l_ctr_bypass_auc) {
          if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_p2l) &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_p2l) && p_ad->Is(AdFlag::is_self_service_ad)) {
            return true;
          }
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_non_merchant_live_sctr,
                       "ad_dsp_live_sctr",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->session_data_->get_is_thanos_mix_request() &&
            SPDM_close_ad_dsp_live_sctr(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }

        if ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() ==
                kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live_ad) &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live_ad) &&
            p_ad->Is(AdFlag::is_self_service_ad)) {
          return true;
        }
        return false;
  });
  if (!SPDM_enable_clear_useless_live_cmd(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_non_merchant_live_ctr,
                       "ad_live_ctr_pop_test",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ad->Is(AdFlag::is_live_ad) &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live_ad) &&
            p_ad->Is(AdFlag::is_self_service_ad)) {
          return true;
        }
        return false;
    });
  }
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_credit_click2,
                       "ad_dsp_click2_credit_jinjian",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((p_ctx->IsOcpc2Unit(p_ad)  &&  p_ad->Is(AdFlag::is_deep_conv_credit_jinjian))) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_ctr,
                       "app_ctr_cmd",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->session_data_->get_is_thanos_request() &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) {
        if (!p_ctx->IsOcpc2Unit(p_ad) && !p_ctx->IsAdxLargeCreatives(p_ad)) {
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
            return true;
          }
          if (!(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
                p_ad->Is(AdFlag::is_direct_ecom))) {
            return true;
          }
        }
      }
      return false;
    });
    // 下面的是 for outer iaa item-emb
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
                        ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                        {engine_base::RType::CVR},
                        {engine_base::PredictType::PredictType_outer_iaa_emb_first_dim},
                        {engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_ctr_iaa_item_emb},
                        "ad_dsp_iaa_item_emb",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->enable_iaa_item_emb_) {
        return false;  // 开关不打开的丢弃
      }
      if (p_ad->Is(AdFlag::is_outer_loop_ad) &&  // 是外循环广告
           p_ctx->session_data_->get_is_rewarded() &&  // 是激励广告
        // page_id 或 pos_id 在指定的 kconf 中
      ((p_ctx->iaa_rl_page_id_ && !p_ctx->iaa_rl_page_id_->empty() &&
      p_ctx->iaa_rl_page_id_->count(p_ctx->session_data_->get_page_id()) > 0) ||
      (p_ctx->session_data_->get_pos_manager_base().request_imp_infos.size() > 0 &&
      p_ctx->iaa_rl_pos_id_ && !p_ctx->iaa_rl_pos_id_->empty() &&
        p_ctx->iaa_rl_pos_id_->count(
          p_ctx->session_data_->get_pos_manager_base().request_imp_infos[0].pos_id) > 0))) {
        return true;
      }
      return false;
    });
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
                        ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                        {engine_base::RType::CVR},
                        {engine_base::PredictType::PredictType_outer_live_emb_first_dim},
                        {engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_ctr_live_item_emb},
                        "ad_dsp_live_item_emb",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->enable_live_item_emb_) {
        return false;  // 开关不打开的放弃
      }
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
        return true;
      }
      return false;
    });
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
                        ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                        {engine_base::RType::CVR},
                        {engine_base::PredictType::PredictType_outer_live_sid_first_dim},
                        {engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_live_sid_vec},
                        "ad_dsp_outer_live_sid_vec",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->enable_outer_live_sid_) {
        return false;  // 开关不打开的放弃
      }
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
        return true;
      }
      return false;
    });
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
                        ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                        {engine_base::RType::CVR},
                        {engine_base::PredictType::Predict_outer_live_recall_emb_first_dim},
                        {engine_base::PredictEmbeddingType::Predict_outer_live_recall_emb_vec},
                        "ad_dsp_outer_live_recall_emb",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->enable_outer_live_recall_emb_) {
        return false;  // 开关不打开的放弃
      }
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
        return true;
      }
      return false;
    });
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
                        ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                        {engine_base::RType::CVR},
                        {engine_base::PredictType::PredictType_outer_live_rl_output_first_dim},
                        {engine_base::PredictEmbeddingType::PredictType_outer_live_rl_output},
                        "model_live_rl_cmd",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->enable_live_rl_ecpc_) {
        return false;  // 开关不打开的放弃
      }
      if (p_ctx->enable_live_rl_onemodel_) {
        return false;
      }
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
        return true;
      }
      return false;
    });
    p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                  engine_base::PredictType::PredictType_live_rl_onemodel,
                                  "cmd_live_rl_onemodel",
                                  CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->enable_live_rl_ecpc_) {
        return false;  // 开关不打开的放弃
      }
      if (!p_ctx->enable_live_rl_onemodel_) {
        return false;
      }
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
        return true;
      }
      return false;
      });
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
                        ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                        {engine_base::RType::CVR},
                        {engine_base::PredictType::PredictType_outer_iaa_rl_output_first_dim},
                        {engine_base::PredictEmbeddingType::PredictType_outer_iaa_rl_output},
                        "model_iaa_rl_cmd",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->enable_iaa_rl_emb_) {
        return false;  // 开关不打开的丢弃
      }
      // 外循环直播的直接去掉，不要影响
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
        return false;
      }
      if (p_ad->Is(AdFlag::is_outer_loop_ad) &&  p_ctx->enable_iaa_rl_page_pos_) {
        return true;
      }
      return false;
    });
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_non_merchant_live_ctr,
                       "ad_dsp_live_played_click_live",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_live_ad) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_live_ad) && p_ad->Is(AdFlag::is_self_service_ad)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_auc_model_base,
                       "ad_dsp_live_played_click_live_auc_base",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_dsplive_ctr_bypass_auc) {
        if (p_ad->get_campaign_type() ==
              kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live_ad) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live_ad) && p_ad->Is(AdFlag::is_self_service_ad)) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "ad_dsp_live_played_click_live_auc_exp",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_dsplive_ctr_bypass_auc) {
        if (p_ad->get_campaign_type() ==
              kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live_ad) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live_ad) && p_ad->Is(AdFlag::is_self_service_ad)) {
          return true;
        }
      }
      return false;
    });
}

void OuterCmdRegister::RegisterCvrCmd(CuratorTypeV2 *p_cmd_curator) const {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_purchase,
                                    "playlet_online_invoked2_purchase",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_search_duanju_independent &&
          p_ctx->session_data_->get_is_search_request()) {
          return false;
        }
        if (p_ctx->enable_conv_invoked_pay_online &&
             ((p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) ||
             (p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id()) > 0) ||
             (p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0))) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_purchase,
                                    "ad_rank_app_invoked2_purchase",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_search_duanju_independent &&
          p_ctx->session_data_->get_is_search_request()) {
          return false;
        }
        if (!(SPDM_enableYanhangProABtest() &&
              (p_ctx->yanhang_pro_product_map->count(p_ad->get_product_name()) > 0 ||
               p_ad->get_is_yanhang())) &&
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE &&
              (!p_ad->Is(AdFlag::is_sdpa_ad) || p_ad->Is(AdFlag::is_sdpa_ad) && SPDM_enable_sdpa_purchase_to_zhongtai(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
              !(p_ctx->enable_car_purchase_single_cmd_ && p_ad->Is(AdFlag::is_jiaotong_ad))) ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM) ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
             (p_ad->Is(AdFlag::is_invoke_ecpc_account_white_list_ad)) ||
             (!p_ctx->enable_conv_invoked_pay_online &&
              ((p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) ||
              (p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id()) > 0) ||
              (p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0))) ||
             (p_ctx->IsMerchantPlatformInvokedAd(p_ad)))) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_purchase,
                                    "ad_rank_app_invoked2_purchase_coldstart",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((SPDM_enableYanhangProABtest() &&
             (p_ctx->yanhang_pro_product_map->count(p_ad->get_product_name()) > 0 ||
              p_ad->get_is_yanhang())) &&
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE &&
              (!p_ad->Is(AdFlag::is_sdpa_ad) || p_ad->Is(AdFlag::is_sdpa_ad) && SPDM_enable_sdpa_purchase_to_zhongtai(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
              !(p_ctx->enable_car_purchase_single_cmd_ && p_ad->Is(AdFlag::is_jiaotong_ad))) ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM) ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
             (p_ad->Is(AdFlag::is_invoke_ecpc_account_white_list_ad)) ||
             (p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) ||
             (p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id()) > 0) ||
             (p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0) ||
             (p_ctx->IsMerchantPlatformInvokedAd(p_ad)))) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_purchase,
                                    "ad_rank_app_car_purchase",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) &&
            p_ctx->enable_car_purchase_single_cmd_ &&
            p_ad->Is(AdFlag::is_jiaotong_ad)) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_car_purchase,
                                    "ad_rank_app_car_purchase_ensemble",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) &&
            p_ctx->enable_car_purchase_single_ensemble_ &&
            p_ad->Is(AdFlag::is_jiaotong_ad)) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_wangfu_purchase,
                                    "ad_rank_app_wangfu_purchase",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) &&
              p_ctx->enable_wangfu_purchase_cmd_ &&
              (!p_ctx->enable_use_x18_purchase_model_ ||
              !(RankKconfUtil::meituanX18accounts() &&
              RankKconfUtil::meituanX18accounts()->count(p_ad->get_account_id()) != 0)) &&
              RankKconfUtil::WangfuIndustryList() &&
              RankKconfUtil::WangfuIndustryList()->find(absl::StrCat(p_ad->get_industry_id_v3())) !=
              RankKconfUtil::WangfuIndustryList()->end()))) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_wangfu_purchase,
                                    "model_purchase_ecom",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) &&
              p_ctx->enable_purchase_ecom_cmdkey_ &&
              p_ad->Is(AdFlag::is_comp_ecom_ad) &&
              !(p_ctx->enable_use_purchase_promotion_model_ &&
                RankKconfUtil::EcomPurchasePromotionProductNames() &&
                RankKconfUtil::EcomPurchasePromotionProductNames()->find(
                  absl::StrCat(p_ad->get_product_name())) !=
                RankKconfUtil::EcomPurchasePromotionProductNames()->end())))) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_wangfu_purchase,
                                    "model_purchase_ecom_promotion",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) &&
              p_ctx->enable_purchase_ecom_cmdkey_ &&
              p_ad->Is(AdFlag::is_comp_ecom_ad) &&
              p_ctx->enable_use_purchase_promotion_model_ &&
              RankKconfUtil::EcomPurchasePromotionProductNames() &&
              RankKconfUtil::EcomPurchasePromotionProductNames()->find(
                absl::StrCat(p_ad->get_product_name())) !=
              RankKconfUtil::EcomPurchasePromotionProductNames()->end()))) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_zongping_purchase_exp,
                                    "model_purchase_ecom_exp",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) &&
              p_ctx->enable_purchase_ecom_cmdkey_ &&
              p_ad->Is(AdFlag::is_comp_ecom_ad) &&
              p_ctx->enable_use_purchase_exp_model_))) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_ee_purchase,
                                    "model_purchase_ee",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
              p_ctx->enable_purchase_ee_cmdkey_ &&
              p_ad->Is(AdFlag::is_media_game_ad)))) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_cid_uplift_cvr,
                                    "cid_uplift_cvr",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->cid_customize != nullptr && p_ctx->enable_cid_qcpx_cmd) {
          if (p_ctx->cid_customize->account_conf().find(p_ad->get_account_id())
            != p_ctx->cid_customize->account_conf().end() &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CID_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT)) {
            return true;
          }
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_wangfu_purchase,
                                    "ad_rank_app_wangfu_purchase_x18",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) &&
              p_ctx->enable_wangfu_purchase_cmd_ &&
              (p_ctx->enable_use_x18_purchase_model_ &&
              (RankKconfUtil::meituanX18accounts() &&
              RankKconfUtil::meituanX18accounts()->count(p_ad->get_account_id()) != 0)) &&
              RankKconfUtil::WangfuIndustryList() &&
              RankKconfUtil::WangfuIndustryList()->find(absl::StrCat(p_ad->get_industry_id_v3())) !=
              RankKconfUtil::WangfuIndustryList()->end()))) {
          return true;
        }
        return false;
      });
  // [huwenkang03]
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_purchase,
                                    "ad_rank_car_deep_conv_purchase",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ad->Is(AdFlag::is_deep_conv_purchase) &&
            p_ctx->enable_car_conv_purchase_cmd_ && p_ad->Is(AdFlag::is_jiaotong_ad)) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_car_purchase,
                                    "ad_rank_car_deep_conv_purchase_ensemble",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
            p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY) &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) &&
            p_ctx->enable_car_conv_purchase_ensemble_ &&
            p_ad->Is(AdFlag::is_jiaotong_ad)) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_clue_ensemble_imp_lps,
                                    "ad_dsp_clue_ensemble_imp_lps",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        // 是否屏蔽行业直播
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
          return false;
        }
        // 本地线索准入
        bool clue_flag = (p_ad->Is(AdFlag::is_clue_ad) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED));
        // 教育准入
        bool edu_flag = (p_ad->Is(AdFlag::is_education_ad) &&
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        !p_ad->Is(AdFlag::is_lps_tx) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
        (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION));
        // 商品速推准入
        bool product_simple_promotion_flag = (p_ad->get_campaign_type() ==
        kuaishou::ad::AdEnum_CampaignType_AD_PRODUCT_SIMPLE_PROMOTION);
        // 流量准入
        if (p_ctx->IsOcpc2Unit(p_ad) &&
            (((p_ctx->enable_clue_ensemble_imp_lps_ || p_ctx->enable_merge_clue_and_edu_) && clue_flag) ||
            (p_ctx->enable_merge_clue_and_edu_ && edu_flag) ||
            (p_ctx->enable_product_simple_promotion_ensemble_ && product_simple_promotion_flag))) {
          return true;
        }
        return false;
      });
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_outer_llm_generative_sim_score,
                                    "outer_llm_generative_sim_score",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_sdpa_llm_generative_ecpc_ &&
            p_ad->Is(AdFlag::is_sdpa)) {
          return true;
        }
        if (p_ctx->enable_clue_llm_generative_ecpc_ &&
            p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->Is(AdFlag::is_all_clue_ad)) {
          return true;
        }
        return false;
      });
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_outer_llm_fwh_sim_score,
                                    "outer_llm_fwh_sim_score",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_clue_llm_fwh_ecpc_ &&
            p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->Is(AdFlag::is_prm_ad)) {
          return true;
        }
        return false;
      });
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_credit_conv_grant,
                                    "jinjian_to_credit_grant", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        auto ad_ret =
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT ||
            p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN ||
            p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT;
        return ad_ret;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_purchase,
                       "rank_ad_dsp_fiction_purchase_ratio",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_search_duanju_independent &&
        p_ctx->session_data_->get_is_search_request()) {
        return false;
      }
      if (((p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
            p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE)) {
          return true;
      }
      return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_cvr,
                       "ad_dsp_cvr_cmd",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) {
        return false;
      }
      if (!p_ctx->IsOcpc2Unit(p_ad) &&
          !(p_ad->get_ad_source_type() == kuaishou::ad::ADX &&
            p_ctx->session_data_->get_is_thanos_request()) &&
          (p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE &&
           !p_ctx->IsAdxLargeCreatives(p_ad)) &&
          !(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
            p_ad->Is(AdFlag::is_direct_ecom))) {
        return true;
      }
      return false;
    });

    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_cvr,
                       "ad_dsp_live_click_conv_live",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {  // NOLINT
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
            p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||  // NOLINT
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
            return true;
          }
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_auc_model_base,
                       "ad_dsp_live_click_conv_live_auc_base",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_dsplive_direct_conv_bypass_auc) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {  // NOLINT
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
              p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||  // NOLINT
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
            return true;
          }
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "ad_dsp_live_click_conv_live_auc_exp",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_dsplive_direct_conv_bypass_auc) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {  // NOLINT
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
              p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||  // NOLINT
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
            return true;
          }
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_click2_deep_rate,
                       "rank_click2_deep_unified",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          p_ad->get_industry_parent_id_v3() == 1003) {
        return false;
      }

      auto kIsAliOuterDeliveryAccountList =
        (RankKconfUtil::aliOuterDeliveryAccountList()->find(p_ad->get_account_id()) !=
         RankKconfUtil::aliOuterDeliveryAccountList()->end() ||
         p_ad->get_campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE);

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          kIsAliOuterDeliveryAccountList) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          p_ad->Is(AdFlag::is_dpa)) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_REGISTER) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM) {
        return false;
      }

      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_deep_unified) && !p_ad->Is(AdFlag::is_single_bid_purchase_v2) &&  // NOLINT
          !p_ad->Is(AdFlag::is_ad_watch_times) && !p_ad->Is(AdFlag::is_ad_watch_5_times) && !p_ad->Is(AdFlag::is_ad_watch_10_times) &&  // NOLINT
          !p_ad->Is(AdFlag::is_ad_watch_20_times) && !p_ad->Is(AdFlag::is_single_jinjian) && !p_ad->Is(AdFlag::is_single_credit) &&  // NOLINT
          !p_ad->Is(AdFlag::is_single_valid_clues) && !p_ad->Is(AdFlag::is_add_wechat) && !p_ad->Is(AdFlag::is_multi_conv) &&  // NOLINT
          p_ad->get_unit_type() != kuaishou::ad::AdEnum_UnitType_JK_UNIT && !p_ad->Is(AdFlag::is_deeper_conversion) &&  // NOLINT
          p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_KEY_INAPP_ACTION) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_landingpage_submit,
                       "landingpage_submit_rate_cmd",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) {
        return false;
      }
      if (p_ctx->enable_rewarded_cmd_lps && p_ctx->session_data_->get_is_rewarded()) {
        return false;
      }

      if (!p_ctx->IsOcpc2Unit(p_ad) &&
          !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
          p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION)) &&
          p_ctx->special_lps_unit_set_->count(p_ad->get_unit_id()) == 0) {
        return true;
      }
      // 快聘作品表单，请求统一表单模型
      if (!p_ctx->IsOcpc2Unit(p_ad) &&
          !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
          p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                     engine_base::PredictType::PredictType_click_purchase_rate_single_bid,
                     "click_purchase_single_bid",
                     CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) {
        return false;
      }
      if (!p_ctx->IsOcpc2Unit(p_ad) &&
          p_ad->Is(AdFlag::is_single_bid_purchase) &&
          p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE &&
          p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE &&
          p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_TAOBAO &&
          p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_LANDING_PAGE) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_c2_game_appoint_rate,
                       "ad_dsp_game_appointment_c2",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_landingpage_submit,
                       "ad_dsp_item_imp_lps_jinjian_credit_grant",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) &&
          (p_ad->Is(AdFlag::is_single_jinjian) || p_ad->Is(AdFlag::is_single_credit)) &&
           (!SPDM_enable_jinjian_credit_grant_routing(p_ctx->session_data_->get_spdm_ctx()))) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_cvr,
                       "ad_dsp_item_click_lps_photo",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->enable_ad_dsp_live_lps_photo_to_live &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
        return true;
      }
      return false;
    });

    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_non_merchant_live_cvr,
                      "ad_dsp_item_click_lps_photo_to_live",
                      CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_ad_dsp_live_lps_photo_to_live &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
        return true;
      }
      return false;
    });
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_auc_model_base,
                       "ad_dsp_item_click_lps_photo_auc_base",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_dsplive_p2l_lps_bypass_auc) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "ad_dsp_item_click_lps_photo_auc_exp",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_dsplive_p2l_lps_bypass_auc) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_click2_deep_rate,
                       "ad_dsp_click2_valid_clues_bid",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) &&
          (p_ad->Is(AdFlag::is_single_valid_clues) || p_ad->Is(AdFlag::is_deep_conv_valid_clues))) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_click_app_invoked,
                       "dpa_click_app_invoked",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_dpa) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_cvr,
                       "ad_adx_thanos_cvr",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->session_data_->get_is_thanos_request() &&
          !p_ctx->enable_adx_thanos_cvr_multihead) {
        if (p_ad->get_ad_source_type() == kuaishou::ad::ADX) {
          return true;
        }
      }
      return false;
    });

    p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_cvr,
                        "ad_adx_thanos_cvr_multihead",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->session_data_->get_is_thanos_request() &&
          p_ctx->enable_adx_thanos_cvr_multihead) {
        if (p_ad->get_ad_source_type() == kuaishou::ad::ADX) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_click2_deep_rate,
                       "ad_dsp_click2_jinjian_single_bid",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_jinjian)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_purchase,
                       "rank_ad_dsp_purchase_ratio",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      auto kIsPenicillinPurchaseWhitelist =
        RankKconfUtil::PenicillinConvPurchaseWhitelist()->find(p_ad->get_account_id()) !=
        RankKconfUtil::PenicillinConvPurchaseWhitelist()->end();
      if (p_ctx->enable_search_duanju_independent &&
        p_ctx->session_data_->get_is_search_request()) {
        return false;
      }
      if (!(SPDM_enableYanhangProABtest() &&
            (p_ctx->yanhang_pro_product_map->count(p_ad->get_product_name()) > 0 ||
             p_ad->get_is_yanhang())) &&
          (!((p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
              p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE))) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
          return true;
        }

        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
            kIsPenicillinPurchaseWhitelist) {
          return true;
        }

        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
          return true;
        }

        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
          return true;
        }

        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) {
          return true;
        }

        if ((p_ad->Is(AdFlag::is_deep_conv_purchase) &&
             !(p_ctx->enable_car_conv_purchase_cmd_ && p_ad->Is(AdFlag::is_jiaotong_ad))) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) &&
             !(p_ctx->enable_car_purchase_single_cmd_ && p_ad->Is(AdFlag::is_jiaotong_ad))) ||
            p_ad->Is(AdFlag::is_roi_low_purchase_or_retention_ecpc_ad) ||
            p_ad->Is(AdFlag::is_wechat_small_game_low_purchase_rate_drop_ad)) {
          return true;
        }

        auto kecpcProductAndUnitConfig_product_name =
          RankKconfUtil::ecpcProductAndUnitConfig()->data().product_infos.find(p_ad->get_product_name()) !=
          RankKconfUtil::ecpcProductAndUnitConfig()->data().product_infos.end();

        auto kecpcProductAndUnitConfig_account_id =
          RankKconfUtil::ecpcProductAndUnitConfig()->data().total_accountids_set.find(p_ad->get_account_id()) !=  // NOLINT
          RankKconfUtil::ecpcProductAndUnitConfig()->data().total_accountids_set.end();

        if (kecpcProductAndUnitConfig_product_name &&
            kecpcProductAndUnitConfig_account_id &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
          return true;
        }
        // 首发激活白名单产品预估付费率
        if ( p_ctx->enable_game_shoufa_pay_ecpc_boost
            && p_ctx->game_shoufa_product_name_set_ != nullptr
            && p_ctx->game_shoufa_product_name_set_->count(p_ad->get_product_name()) > 0
            && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION
              || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ) {
          return true;
        }
        if (p_ctx->ad_roas_drop_admit_ != nullptr && p_ctx->enable_ecpc_conv_predict_more_sta) {
          if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
              != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
              p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
              != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
             return true;
          }
        }
        // 综合平台激活付费预估
        if (p_ctx->IsMerchantPlatformConversiondAd(p_ad)) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_purchase,
                       "rank_ad_dsp_purchase_ratio_coldstart",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      auto kIsPenicillinPurchaseWhitelist =
        RankKconfUtil::PenicillinConvPurchaseWhitelist()->find(p_ad->get_account_id()) !=
        RankKconfUtil::PenicillinConvPurchaseWhitelist()->end();

      if ((SPDM_enableYanhangProABtest() &&
           (p_ctx->yanhang_pro_product_map->count(p_ad->get_product_name()) > 0 ||
            p_ad->get_is_yanhang())) &&
          (!((p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
              p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE))) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
          return true;
        }

        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
            kIsPenicillinPurchaseWhitelist) {
          return true;
        }

        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
          return true;
        }

        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
          return true;
        }

        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) {
          return true;
        }

        if (p_ad->Is(AdFlag::is_deep_conv_purchase) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
            p_ad->Is(AdFlag::is_roi_low_purchase_or_retention_ecpc_ad) ||
            p_ad->Is(AdFlag::is_wechat_small_game_low_purchase_rate_drop_ad)) {
          return true;
        }

        auto kecpcProductAndUnitConfig_product_name =
          RankKconfUtil::ecpcProductAndUnitConfig()->data().product_infos.find(p_ad->get_product_name()) !=
          RankKconfUtil::ecpcProductAndUnitConfig()->data().product_infos.end();

        auto kecpcProductAndUnitConfig_account_id =
          RankKconfUtil::ecpcProductAndUnitConfig()->data().total_accountids_set.find(p_ad->get_account_id()) !=  // NOLINT
          RankKconfUtil::ecpcProductAndUnitConfig()->data().total_accountids_set.end();

        if (kecpcProductAndUnitConfig_product_name &&
            kecpcProductAndUnitConfig_account_id &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
          return true;
        }
        if (p_ctx->ad_roas_drop_admit_ != nullptr && p_ctx->enable_ecpc_conv_predict_more_sta) {
          if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
              != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
              p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
              != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
             return true;
          }
        }
      }
      return false;
    });

    if (!SPDM_enable_outer_ctr_second_stage_predict(cmd_curator_ctx_->session_data_->get_spdm_ctx()) || (
        cmd_curator_ctx_->session_data_->get_sub_page_id() != ******** &&
        cmd_curator_ctx_->session_data_->get_sub_page_id() != ********)) {
      p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_server_show_cvr,
                                    "ad_dsp_server_show_cvr_ocpm",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((SPDM_disable_adx_for_ctr(p_ctx->session_data_->get_spdm_ctx()))
            && (p_ad->get_ad_source_type() == kuaishou::ad::ADX)) {
          return false;
        }
        if ((p_ctx->IsOcpc2Unit(p_ad) || (p_ctx->session_data_->get_is_thanos_request() &&
                                          p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC)) &&
            !p_ctx->IsAdxLargeCreatives(p_ad)) {
          if (p_ad->Is(AdFlag::is_dpa)) {
            return false;
          }

          if (p_ad->Is(AdFlag::is_direct_ecom) ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
            return false;
          }

          if (p_ctx->IsOcpc2Unit(p_ad) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
              (p_ctx->aliOuterDeliveryAccountList->count(p_ad->get_account_id()) > 0
                || p_ad->get_campaign_sub_type() ==
                kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE)) {
            return true;
          }

          if (RankKconfUtil::storyAdPurchaseWhitelist()->find(p_ad->get_product_name()) !=
              RankKconfUtil::storyAdPurchaseWhitelist()->end()) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_single_bid_purchase_v2) ||
              p_ad->get_deep_conversion_type() ==
              kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT ||
              (!p_ctx->enable_c2_lps_ownctr_ && p_ctx->enable_c2_lps_ &&
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) ||
              p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ADD_WECHAT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_REGISTER) {
            return true;
          }
          if (p_ctx->session_data_->get_is_thanos_request() &&
              p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC) {
            return true;
           }
        }
        return false;
      });
    } else {
      p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
                {engine_base::RType::CVR}, {engine_base::PredictType::PredictType_server_show_cvr},
                {engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_ctr_showocpm_toplayer},
                 "ad_dsp_server_show_cvr_ocpm", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((SPDM_disable_adx_for_ctr(p_ctx->session_data_->get_spdm_ctx()))
            && (p_ad->get_ad_source_type() == kuaishou::ad::ADX)) {
          return false;
        }
        if ((p_ctx->IsOcpc2Unit(p_ad) || (p_ctx->session_data_->get_is_thanos_request() &&
                                          p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC)) &&
            !p_ctx->IsAdxLargeCreatives(p_ad)) {
          if (p_ad->Is(AdFlag::is_dpa)) {
            return false;
          }

          if (p_ad->Is(AdFlag::is_direct_ecom) ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
            return false;
          }

          if (p_ctx->IsOcpc2Unit(p_ad) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
              (p_ctx->aliOuterDeliveryAccountList->count(p_ad->get_account_id()) > 0
                || p_ad->get_campaign_sub_type() ==
                kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE)) {
            return true;
          }

          if (RankKconfUtil::storyAdPurchaseWhitelist()->find(p_ad->get_product_name()) !=
              RankKconfUtil::storyAdPurchaseWhitelist()->end()) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_single_bid_purchase_v2) ||
              p_ad->get_deep_conversion_type() ==
              kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT ||
              (!p_ctx->enable_c2_lps_ownctr_ && p_ctx->enable_c2_lps_ &&
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) ||
              p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ADD_WECHAT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_REGISTER) {
            return true;
          }
          if (p_ctx->session_data_->get_is_thanos_request() &&
              p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC) {
            return true;
           }
        }
        return false;
      });
    }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                     engine_base::PredictType::PredictType_server_show_cvr,
                     "ad_dpa_server_show_cvr_ocpm",
                     CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) {
        return false;
      }

      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_dpa)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_click2_purchase_rate_single_bid,
                       "rank_click2_purchase_single_bid",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) {
        return false;
      }
      if (p_ctx->session_data_->get_is_splash_request()) {
        return false;
      }
      if (p_ctx->IsOcpc2Unit(p_ad) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE) {
        return true;
      } else if (p_ctx->enable_self_service_request_purchase &&
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                  p_ad->Is(AdFlag::is_self_service_ad)) {
        return true;
      }
      if (p_ctx->ad_roas_drop_admit_ != nullptr && p_ctx->enable_ecpc_click_2_purchase_exp) {
        if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
            != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
            p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
            != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_cvr,
                       "ad_dsp_live_click_new_action_p2l",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_ad_dsp_live_wx) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
          && p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {  // NOLINT
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT) {
            return true;
          }
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_non_merchant_live_cvr,
                      "ad_dsp_live_click_new_action_dl",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_ad_dsp_live_wx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
          && p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {  // NOLINT
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT) {
          return true;
        }
      }
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_cvr,
                       "ad_dsp_live_click_wx_p2l",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_ad_dsp_live_wx2 && !p_ctx->enable_ad_dsp_live_wx) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
          && p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {  // NOLINT
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) {
            return true;
          }
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_non_merchant_live_cvr,
                      "ad_dsp_live_click_wx_dl",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_ad_dsp_live_wx2 && !p_ctx->enable_ad_dsp_live_wx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
          && p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {  // NOLINT
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) {
          return true;
        }
      }
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_cvr,
                       "ad_dsp_live_button_click",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_ad_dsp_live_button_click) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::LIVE_STREAM_COMPONENT_CLICK &&
              (p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT ||  // NOLINT
                p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT)) {  // NOLINT
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_button_click,
                       "ad_dsp_live_button_click_test",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
              (p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT ||  // NOLINT
                p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT)) {  // NOLINT
        if (p_ctx->enable_ad_dsp_live_button_click_test) {
          return true;
        }
        if (p_ctx->enable_outer_live_twin_bid_flow ||
              p_ctx->outer_live_twin_bid_account_white->count(p_ad->get_account_id()) > 0) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_playtime,
                       "ad_dsp_live_playtime",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
              (p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT ||  // NOLINT
                p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT)) {  // NOLINT
        // 拆分表单
        if (p_ctx->enable_ad_dsp_live_lps_playtime &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
          return false;
        }
        if (p_ctx->enable_ad_dsp_live_playtime) {
          return true;
        }
        // 外循环直播时长策略开关
        if (p_ctx->enable_outer_live_playtime_strategy) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_playtime,
                       "ad_dsp_live_lps_playtime",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          (p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT ||    // NOLINT
           p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) &&  // NOLINT
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
        if (p_ctx->enable_ad_dsp_live_lps_playtime) {
          return true;
        }
        // 外循环直播时长策略开关
        if (p_ctx->enable_outer_live_playtime_strategy) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_lps_realtime_cvr,
                       "ad_dsp_live_click_lps_live_realtime",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_live_ad) && p_ad->Is(AdFlag::is_self_service_ad) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
        return true;
      }
      return false;
    });

  if (cmd_curator_ctx_->session_data_->common_r_
        ->GetIntCommonAttr("enable_clean_inefficient_cmdkey").value_or(0) == 0) {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_cvr,
                       "ad_dsp_live_click_lps_live",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->enable_ad_dsp_live_lps_direct_live &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
        return true;
      }
      if (!p_ctx->enable_ad_dsp_live_lps_direct_live &&
          p_ad->Is(AdFlag::is_live_ad) && p_ad->Is(AdFlag::is_self_service_ad) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
        return true;
      }
      return false;
    });
    }

    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_cvr,
                       "ad_dsp_live_click_lps_direct_live",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_ad_dsp_live_lps_direct_live &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
        return true;
      }
      if (p_ctx->enable_ad_dsp_live_lps_direct_live &&
          p_ad->Is(AdFlag::is_live_ad) && p_ad->Is(AdFlag::is_self_service_ad) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_auc_model_base,
                       "ad_dsp_live_click_lps_live_auc_base",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_dsplive_direct_lps_bypass_auc) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "ad_dsp_live_click_lps_live_auc_exp",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_dsplive_direct_lps_bypass_auc) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_click2_deep_rate,
                       "click2_app_register",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) {
        return false;
      }
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_REGISTER) {
        return true;
      }
      if (p_ctx->isEcpcRegisterAd(p_ad)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_deep_rate,
                       "ad_dsp_click_valid_clues_bid",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->IsOcpc2Unit(p_ad) && (p_ad->Is(AdFlag::is_single_valid_clues) || p_ad->Is(AdFlag::is_deep_conv_valid_clues))) {  // NOLINT
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_deep_rate,
                       "ad_dsp_click_jinjian_single_bid",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_jinjian)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_deep_rate,
                       "click_deep_unified",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad)) {
        return false;
      }

      if (p_ad->get_unit_type() == kuaishou::ad::AdEnum_UnitType_JK_UNIT) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          p_ad->get_industry_parent_id_v3() == 1003) {
        return false;
      }
      bool IsAliOuterDeliveryAccountList =
        (RankKconfUtil::aliOuterDeliveryAccountList()->find(p_ad->get_account_id()) !=
          RankKconfUtil::aliOuterDeliveryAccountList()->end() ||
        p_ad->get_campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE);

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          p_ad->get_industry_parent_id_v3() == 1003) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          IsAliOuterDeliveryAccountList) {
        return false;
      }

      if (p_ad->Is(AdFlag::is_deep_unified) &&
          !p_ad->Is(AdFlag::is_single_bid_purchase) &&
          !p_ad->Is(AdFlag::is_single_jinjian) &&
          !p_ad->Is(AdFlag::is_single_credit) &&
          !p_ad->Is(AdFlag::is_single_valid_clues) &&
          !p_ad->Is(AdFlag::is_add_wechat) &&
          !p_ad->Is(AdFlag::is_multi_conv) &&
          !p_ad->Is(AdFlag::is_ad_watch_times) &&
          !p_ad->Is(AdFlag::is_ad_watch_5_times) &&
          !p_ad->Is(AdFlag::is_ad_watch_10_times) &&
          !p_ad->Is(AdFlag::is_ad_watch_20_times)) {
        return true;
      }
      return false;
    });

  if (SPDM_enable_cid_roi_use_new_cmd(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {  // NOLINT
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_merchant_ltv,
                       "cid_roi_imp_gmv_cmd_fix",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad)) {
         if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CID_ROAS) ||
            (p_ctx->enable_order_submit_gpm_use_ltv_model &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
            && !(p_ctx->enable_order_submit_cold_split_cmd &&
            p_ctx->ad_order_submit_cold_start_account_product_map_->count(p_ad->get_account_id()) > 0))) {
          return true;
        }
      }
      if (p_ctx->enable_cid_search_boost_exp && p_ctx->IsCidSearchAd(p_ad)) {
        return true;
      }
      return false;
    });
  }

    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "cid_roi_imp_gmv_cmd_fix_auc_exp",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad)) {
         if (p_ctx->enable_cid_roi_imp_gmv_cmd_fix_auc_exp &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CID_ROAS) {
          return true;
        }
      }
      if (p_ctx->enable_cid_search_boost_exp && p_ctx->IsCidSearchAd(p_ad)) {
        return true;
      }
      return false;
    });

  if (SPDM_enable_cid_cvr_ecom_multi_cmd_exp(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    p_cmd_curator->RegisterMultiPredictCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "click_order_submit_cmd_muti_exp",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
              && !(p_ctx->enable_order_submit_cold_split_cmd &&
            p_ctx->ad_order_submit_cold_start_account_product_map_->count(p_ad->get_account_id()) > 0)
              && !(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
                  && (p_ad->get_live_creative_type() ==
                           kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT
                    || p_ad->get_live_creative_type() ==
                           kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT))) {
        return true;
      }
      return false;
    });
  }

  if (SPDM_enable_cid_cvr_ecom_multi_cmd(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    p_cmd_curator->RegisterMultiPredictCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_deep_rate,
                       "click_order_submit_cmd_muti",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
              && !(p_ctx->enable_order_submit_cold_split_cmd &&
            p_ctx->ad_order_submit_cold_start_account_product_map_->count(p_ad->get_account_id()) > 0)
              && !(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
                  && (p_ad->get_live_creative_type() ==
                           kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT
                    || p_ad->get_live_creative_type() ==
                           kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT))) {
        return true;
      }
      if (p_ctx->cid_customize != nullptr && p_ctx->enable_cid_qcpx_cmd) {
        if (p_ctx->cid_customize->account_conf().find(p_ad->get_account_id())
          != p_ctx->cid_customize->account_conf().end()) {
          return true;
        }
      }
      return false;
    });
  } else {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_deep_rate,
                       "click_order_submit_cmd",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
              && !(p_ctx->enable_order_submit_cold_split_cmd &&
            p_ctx->ad_order_submit_cold_start_account_product_map_->count(p_ad->get_account_id()) > 0)
              && !(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
                  && (p_ad->get_live_creative_type() ==
                           kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT
                    || p_ad->get_live_creative_type() ==
                           kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT))) {
        return true;
      }
      if (p_ctx->cid_customize != nullptr && p_ctx->enable_cid_qcpx_cmd) {
        if (p_ctx->cid_customize->account_conf().find(p_ad->get_account_id())
          != p_ctx->cid_customize->account_conf().end()) {
          return true;
        }
      }
      return false;
    });
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "click_order_submit_cmd_auc_exp",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_cid_order_cross_auc &&
        p_ctx->IsOcpc2Unit(p_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
              && !(p_ctx->enable_order_submit_cold_split_cmd &&
            p_ctx->ad_order_submit_cold_start_account_product_map_->count(p_ad->get_account_id()) > 0)
            && !(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
               && (p_ad->get_live_creative_type() ==
                           kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT
                  || p_ad->get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT))) {
        return true;
      }
      return false;
    });

  if (SPDM_enable_cid_order_live_cmdkey_v2(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_deep_rate,
                       "click_order_submit_live_cmd_live_v2",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
              && !(p_ctx->enable_order_submit_cold_split_cmd &&
            p_ctx->ad_order_submit_cold_start_account_product_map_->count(p_ad->get_account_id()) > 0)
            && (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
            && p_ad->get_live_creative_type() ==
                     kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT)) {
        return true;
      }
      return false;
    });

    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_deep_rate,
                       "click_order_submit_live_cmd_photo_v2",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT
              && !(p_ctx->enable_order_submit_cold_split_cmd &&
            p_ctx->ad_order_submit_cold_start_account_product_map_->count(p_ad->get_account_id()) > 0)
            && (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
            && p_ad->get_live_creative_type() ==
                     kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT)) {
        return true;
      }
      return false;
    });
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_click2_purchase_rate_single_bid,
                       "lps_rank_click2_purchase_single_bid",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
         (p_ctx->enable_ad_rank_clk2purchase_predict ||
         p_ctx->short_play_clk2pay_account_list_->count(p_ad->get_account_id()) > 0)) ||
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
         p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
         (p_ctx->enable_ad_rank_clk2purchase_roas_predict ||
         p_ctx->short_play_clk2pay_account_roas_list_->count(p_ad->get_account_id()) > 0))) {
      return true;
    } else if (!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->enable_lps_rank_click2_purchase_single_bid_search) &&
        (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_TAOBAO ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LANDING_PAGE ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE) &&
        (p_ctx->IsOcpc2Unit(p_ad)) &&
        (RankKconfUtil::storyAdPurchaseWhitelist()->find(p_ad->get_product_name()) ==
         RankKconfUtil::storyAdPurchaseWhitelist()->end())) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) {
        return true;
      }
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_game_appoint_rate,
                       "ad_dsp_game_appointment_cmd",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!(p_ctx->IsOcpc2Unit(p_ad)) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK)) {
      return true;
    }
    return false;
  });

  if (SPDM_enable_multipredict_click_app_invoked(cmd_curator_ctx_->session_data_->get_spdm_ctx())) { // NOLINT
    p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_click_app_invoked,
                        "click_app_invoked_multi",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      bool enable_mini_game_invoke_link = false;
      if (p_ctx->enable_outer_game_invoke_link) {
        bool is_mini_game = (((p_ad->get_landing_page_component() & 1) == 1 ||
                            (p_ad->get_landing_page_component() & 2) == 2) &&
                            p_ad->get_industry_parent_id_v3() == 1018);
        if (p_ctx->game_invoke_link_config != nullptr) {
          auto iter_product = p_ctx->game_invoke_link_config->find(p_ad->get_product_name());
          bool is_game_invoke_link_product = (
              iter_product != p_ctx->game_invoke_link_config->end());
          enable_mini_game_invoke_link = (is_game_invoke_link_product && is_mini_game &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS);
        }
      }
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !p_ad->Is(AdFlag::is_dpa) &&
          ((p_ctx->IsOcpc2Unit(p_ad) &&
            p_ctx->ocpm_cut_by_item_imp_app_invoked &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
          (!p_ctx->IsOcpc2Unit(p_ad) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
            (p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) ||
            (p_ad->Is(AdFlag::is_paid_duanju_ad_v3) && p_ctx->enable_playlet_click_app_invoked &&
             p_ctx->is_playlet_invoked_tail_ != nullptr &&
             p_ctx->is_playlet_invoked_tail_->IsOnFor(p_ad->get_account_id())) ||
            (p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) ||
          (p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0) ||
          (enable_mini_game_invoke_link) ||
          (p_ctx->enable_ad_rank_playlet_pay_panel_purchase))) {
        return true;
      }
      return false;
    });
  }

  p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_click_app_invoked_ecom,
                        "model_click_app_invoked_ecom_multi",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_invoked_ecom_cmd_ &&
          p_ad->Is(AdFlag::is_comp_ecom_ad) &&
          !(p_ctx->enable_use_invoke_promotion_model_ &&
            RankKconfUtil::EcomInvokePromotionProductNames() &&
            RankKconfUtil::EcomInvokePromotionProductNames()->find(
              absl::StrCat(p_ad->get_product_name())) !=
            RankKconfUtil::EcomInvokePromotionProductNames()->end()) &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          ((p_ctx->IsOcpc2Unit(p_ad) &&
            p_ctx->ocpm_cut_by_item_imp_app_invoked &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
           (!p_ctx->IsOcpc2Unit(p_ad) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
            (p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) ||
           (p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0))) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_click_app_invoked_ecom,
                        "model_click_app_invoked_ecom_multi_promotion",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_invoked_ecom_cmd_ &&
          p_ad->Is(AdFlag::is_comp_ecom_ad) &&
          p_ctx->enable_use_invoke_promotion_model_ &&
          RankKconfUtil::EcomInvokePromotionProductNames() &&
          RankKconfUtil::EcomInvokePromotionProductNames()->find(
            absl::StrCat(p_ad->get_product_name())) !=
          RankKconfUtil::EcomInvokePromotionProductNames()->end() &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          ((p_ctx->IsOcpc2Unit(p_ad) &&
            p_ctx->ocpm_cut_by_item_imp_app_invoked &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
           (!p_ctx->IsOcpc2Unit(p_ad) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
            (p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) ||
           (p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0))) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_click_app_invoked_ecom_exp,
                        "model_click_app_invoked_ecom_exp",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_invoked_ecom_exp_cmd_ &&
          p_ad->Is(AdFlag::is_comp_ecom_ad) &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          ((p_ctx->IsOcpc2Unit(p_ad) &&
            p_ctx->ocpm_cut_by_item_imp_app_invoked &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
           (!p_ctx->IsOcpc2Unit(p_ad) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
            (p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) ||
           (p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0))) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_click2_purchase_rate_single_bid,
                       "novel_purchase_model_new",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    bool is_novel_conv_purchase_white_list = false;
    auto novel_product_conf = RankKconfUtil::novelConvPurchaseWhiteConf();
    auto& novel_product_conf_map = novel_product_conf->data().novel_purchase();
    auto novel_purchase = novel_product_conf_map.find(1);
    if (novel_purchase != novel_product_conf_map.end()) {
      auto& product_novel_purchase_map = novel_purchase->second.product_name();
      auto& account_novel_purchase_map = novel_purchase->second.account_id();
      const auto p_novel_purchase_iter = product_novel_purchase_map.find(p_ad->get_product_name());
      const auto a_novel_purchase_iter = account_novel_purchase_map.find(p_ad->get_account_id());
      if (p_novel_purchase_iter != product_novel_purchase_map.end() ||
          a_novel_purchase_iter != account_novel_purchase_map.end()) {
        is_novel_conv_purchase_white_list = true;
      } else {
        is_novel_conv_purchase_white_list = false;
      }
    } else {
      is_novel_conv_purchase_white_list = false;
    }

    if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_TAOBAO ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LANDING_PAGE ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE) &&
        p_ctx->IsOcpc2Unit(p_ad) &&
        RankKconfUtil::storyAdPurchaseWhitelist()->find(p_ad->get_product_name()) !=
        RankKconfUtil::storyAdPurchaseWhitelist()->end() &&
        !(p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
          p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0) &&
        !is_novel_conv_purchase_white_list) {
      return true;
    }
    return false;
  });


  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_click2_deep_rate,
                      "ad_dsp_click2_credit_single_bid_v2",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if ((p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_credit)) ||
        (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN) ||
        (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT)) {
      return true;
    }
    return false;
  });

  if (cmd_curator_ctx_->session_data_->common_r_
        ->GetIntCommonAttr("enable_clean_inefficient_cmdkey").value_or(0) == 0) {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_click_purchase_rate_single_bid,
                       "lps_click_purchase_single_bid",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        RankKconfUtil::storyAdPurchaseWhitelist()->find(p_ad->get_product_name()) ==
        RankKconfUtil::storyAdPurchaseWhitelist()->end() &&
        !p_ctx->IsOcpc2Unit(p_ad) &&
        p_ad->Is(AdFlag::is_single_bid_purchase) &&
        (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_TAOBAO ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LANDING_PAGE)) {
      return true;
    }
    return false;
  });
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_app_conversion_rate_sdpa_ensemble,
                       "ad_dsp_sdpa_imp_conv",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        p_ctx->IsOcpc2Unit(p_ad) &&
        (p_ad->Is(AdFlag::is_sdpa_ecom_ad) && !p_ctx->enable_sdpa_ecom_conv_main_pred_ ||
         p_ctx->enable_sdpa_ensemble_conversion_ && p_ad->Is(AdFlag::is_sdpa_ad)) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
         (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
           p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
         p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
         p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0 ||
         (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY)) {
       return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_app_conversion_rate,
                       "ad_dsp_item_imp_conv",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx()) &&
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        p_ctx->IsOcpc2Unit(p_ad) &&
        !p_ad->Is(AdFlag::is_comp_ecom_ad) &&
        !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
          RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddHelpProductWhitelist()->end()) &&
        !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
          RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddNativeProductWhitelist()->end()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
         (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
           p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
         p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
         p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0 ||
         (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
         (SPDM_enable_game_request_imp_conv(p_ctx->session_data_->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
         (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) {
      return true;
    }

    if (!SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx()) &&
        p_ctx->IsOcpc2Unit(p_ad) &&
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
          RankKconfUtil::pddHelpProductWhitelist() &&
          RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddHelpProductWhitelist()->end()) &&
        !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
          RankKconfUtil::pddNativeProductWhitelist() &&
          RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddNativeProductWhitelist()->end()) &&
         (p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
           && p_ctx->enable_ecom_conv_ensemble_) &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
         !p_ad->Is(AdFlag::is_dpa)) ||
         (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
           p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
      return true;
    }

    return false;
  });

  if (SPDM_enable_outer_u_cvr(cmd_curator_ctx_->session_data_->get_spdm_ctx()) &&
      !SPDM_enable_outer_u_ctcvr(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    if (!SPDM_enable_outer_ctr_second_stage_predict(cmd_curator_ctx_->session_data_->get_spdm_ctx()) || (
        cmd_curator_ctx_->session_data_->get_sub_page_id() != ******** &&
        cmd_curator_ctx_->session_data_->get_sub_page_id() != ********)) {
          p_cmd_curator->RegisterCmd(
            new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                engine_base::PredictType::PredictType_outer_u_ctr,
                "ad_dsp_outer_u_ctr",
                CMD_SOURCE_AD_DSP),
            [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
              if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                  (p_ctx->IsOcpc2Unit(p_ad) ||
                      (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
                      (p_ad->Is(AdFlag::is_deep_unified) &&
                      p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE)))) &&
                  !p_ad->Is(AdFlag::is_comp_ecom_ad) &&
                  !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
                    RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
                    RankKconfUtil::pddHelpProductWhitelist()->end()) &&
                  !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
                    RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
                    RankKconfUtil::pddNativeProductWhitelist()->end()) &&
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                    (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
                  p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
                  p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0 ||
                  (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&   // NOLINT
                    p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
                  (SPDM_enable_game_request_imp_conv(p_ctx->session_data_->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
                    p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) {
                return true;
              }
              if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
                !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                (!p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_comp_ecom_ad)) &&
                ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
                (p_ad->Is(AdFlag::is_deep_unified) &&
                p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE))) {
                return true;
              }
              if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
                  !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                  !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
                    RankKconfUtil::pddHelpProductWhitelist() &&
                    RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
                    RankKconfUtil::pddHelpProductWhitelist()->end()) &&
                  !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
                    RankKconfUtil::pddNativeProductWhitelist() &&
                    RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
                    RankKconfUtil::pddNativeProductWhitelist()->end()) &&
                  (p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
                    && p_ctx->enable_ecom_conv_ensemble_) &&
                  ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
                  !p_ad->Is(AdFlag::is_dpa)) ||
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
                    (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
                    (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
                    (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
                return true;
              }
              return false;
            });
        } else {
          p_cmd_curator->RegisterCmd(
            new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
                {engine_base::RType::CVR}, {engine_base::PredictType::PredictType_outer_u_ctr},
                {engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_ctr_conv_toplayer},
                 "ad_dsp_outer_u_ctr", CMD_SOURCE_AD_DSP),
                 [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
              if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                  (p_ctx->IsOcpc2Unit(p_ad) ||
                      (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
                      (p_ad->Is(AdFlag::is_deep_unified) &&
                      p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE)))) &&
                  !p_ad->Is(AdFlag::is_comp_ecom_ad) &&
                  !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
                    RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
                    RankKconfUtil::pddHelpProductWhitelist()->end()) &&
                  !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
                    RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
                    RankKconfUtil::pddNativeProductWhitelist()->end()) &&
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                    (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
                  p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
                  p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0 ||
                  (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&  // NOLINT
                    p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
                  (SPDM_enable_game_request_imp_conv(p_ctx->session_data_->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
                    p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) {
                return true;
              }
              if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
                !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                (!p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_comp_ecom_ad)) &&
                ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
                (p_ad->Is(AdFlag::is_deep_unified) &&
                p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE))) {
                return true;
              }
              if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
                  !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                  !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
                    RankKconfUtil::pddHelpProductWhitelist() &&
                    RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
                    RankKconfUtil::pddHelpProductWhitelist()->end()) &&
                  !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
                    RankKconfUtil::pddNativeProductWhitelist() &&
                    RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
                    RankKconfUtil::pddNativeProductWhitelist()->end()) &&
                  (p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
                    && p_ctx->enable_ecom_conv_ensemble_) &&
                  ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
                  !p_ad->Is(AdFlag::is_dpa)) ||
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
                    (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
                    (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
                    (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
                return true;
              }
              return false;
            });
        }

    static const std::vector<engine_base::PredictType> pts = {
      engine_base::PredictType::PredictType_outer_u_cvr,
      engine_base::PredictType::PredictType_outer_u_noctcvr
    };
    static const std::vector<engine_base::RType> rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    static const std::unordered_set<int64_t> incentive_page_ids = {100012065, 100011344, 100011251, 11101, 13001, 100011347, 100012068}; // NOLINT
    auto is_outer_conv_traffic =
        [&](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          (p_ctx->IsOcpc2Unit(p_ad) ||
              (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
              (p_ad->Is(AdFlag::is_deep_unified) &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE)))) &&
          !p_ad->Is(AdFlag::is_comp_ecom_ad) &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
           p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
           p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0 ||
           (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
           (SPDM_enable_game_request_imp_conv(p_ctx->session_data_->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) {
        return true;
      }
      if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        (!p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_comp_ecom_ad)) &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
        (p_ad->Is(AdFlag::is_deep_unified) &&
        p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE))) {
        return true;
      }
      if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist() &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist() &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
           (p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
             && p_ctx->enable_ecom_conv_ensemble_) &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
           !p_ad->Is(AdFlag::is_dpa)) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
        return true;
      }
      return false;
    };
    if (SPDM_enable_outer_conv_incentive_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())
            && incentive_page_ids.count(cmd_curator_ctx_->session_data_->get_page_id()) > 0) {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(rts, pts, "ad_dsp_outer_incentive_u_cvr_multi", CMD_SOURCE_AD_DSP),
        is_outer_conv_traffic);
    } else {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(rts, pts, "ad_dsp_outer_u_cvr_multi", CMD_SOURCE_AD_DSP),
        is_outer_conv_traffic);
    }
  }

  if (SPDM_enable_outer_u_ctcvr(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> pts = {
      engine_base::PredictType::PredictType_outer_u_ctr,
      engine_base::PredictType::PredictType_outer_u_cvr,
      engine_base::PredictType::PredictType_outer_u_noctcvr
    };
    static const std::vector<engine_base::RType> rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    auto is_outer_conv_traffic =
        [&](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          (p_ctx->IsOcpc2Unit(p_ad) ||
              (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
              (p_ad->Is(AdFlag::is_deep_unified) &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE)))) &&
          !p_ad->Is(AdFlag::is_comp_ecom_ad) &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
           p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
           p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0 ||
           (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
           (SPDM_enable_game_request_imp_conv(p_ctx->session_data_->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) {
        return true;
      }
      if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        (!p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_comp_ecom_ad)) &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
        (p_ad->Is(AdFlag::is_deep_unified) &&
        p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE))) {
        return true;
      }
      if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist() &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist() &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
           (p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
             && p_ctx->enable_ecom_conv_ensemble_) &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
           !p_ad->Is(AdFlag::is_dpa)) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
        return true;
      }
      return false;
    };
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(rts, pts, "ad_dsp_outer_u_ctcvr", CMD_SOURCE_AD_DSP),
      is_outer_conv_traffic);
  }

  // 外循环电商激活模型拆分软硬广
    p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_app_conversion_rate,
                        "ad_dsp_ecom_item_imp_conv_multipredict_new",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
          ((p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
            && !p_ctx->enable_ecom_conv_ensemble_) ||
            p_ad->Is(AdFlag::is_sdpa_ecom_ad)) &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
          !p_ad->Is(AdFlag::is_dpa)) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
        return true;
      }
      return false;
    });

    p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_app_conversion_rate,
                        "ad_dsp_ecom_item_imp_conv_multipredict",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (false && !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
          ((p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
            && !p_ctx->enable_ecom_conv_ensemble_) ||
            p_ad->Is(AdFlag::is_sdpa_ecom_ad)) &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
          !p_ad->Is(AdFlag::is_dpa)) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
        return true;
      }
      return false;
    });
      p_cmd_curator->RegisterMultiPredictCmd(
          new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                          engine_base::PredictType::PredictType_ecom_app_conversion_rate,
                          "ad_dsp_ecom_item_imp_conv_multipredict_ensemble",
                          CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
              RankKconfUtil::pddHelpProductWhitelist() != nullptr &&
              RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
              RankKconfUtil::pddHelpProductWhitelist()->end()) &&
            !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
              RankKconfUtil::pddNativeProductWhitelist() != nullptr &&
              RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
              RankKconfUtil::pddNativeProductWhitelist()->end()) &&
            (p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
              && p_ctx->enable_ecom_conv_ensemble_) &&
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
            !p_ad->Is(AdFlag::is_dpa)) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
              (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
          return true;
        }
        return false;
      });

    // 用信率 完件->用信
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_fin_use_credit_rate,
                       "ad_dsp_fin_use_credit_rate",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((p_ctx->enable_use_credit_cvr_
            && p_ctx->fin_use_credit_product_->count(p_ad->get_product_name()) > 0)
            && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT) {
          return true;
        }
        return false;
      });

  // 小贷 ROI
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_fin_credit_roi_rate,
                       "ad_dsp_fin_credit_roi_model",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_fin_credit_roi_ecpc_ &&
        p_ctx->fin_credit_roi_white_product_ != nullptr &&
        p_ctx->fin_credit_roi_white_product_->count(p_ad->get_product_name()) > 0 &&
        p_ad->Is(AdFlag::is_finance_ad) && p_ctx->IsOcpc2Unit(p_ad)) {
        return true;
      }
    return false;
  });

  // 完件->授信 预估  cmd
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_fin_jinjian_credit_rate,
                       "ad_dsp_fin_jinjian_credit_rate",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((p_ctx->enable_use_jinjian_credit_cvr_
            && p_ctx->fin_jinjian_credit_product_ != nullptr
            && p_ctx->fin_jinjian_credit_product_->count(p_ad->get_product_name()) > 0)
            && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT) {
          return true;
        }
        return false;
      });

  // 表单->获客
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_lps_acquisition,
                       "ad_dsp_lps_acquisition",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      // 获客单出价, 表单 ecpc
        if ((p_ctx->enable_finedu_obtain_cvr_ecpc_
            && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED
            && p_ctx->fin_edu_obtain_account_->count(p_ad->get_account_id()) > 0) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION) {
          return true;
        }
     // 通用 ecpc 工具
      if (p_ctx->ad_roas_drop_admit_ != nullptr && p_ctx->enable_ecpc_add_acquisition_model) {
        if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
            != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
            p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
            != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
            return true;
        }
      }
     // 企微/IM 双出价
      if (p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_deep_conversion_type() ==
           kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION && (
        p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED
        )
      ) {
        return true;
      }
     // 获客双出价/双出价暗测
      return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED &&
             p_ad->get_deep_conversion_type() ==
             kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION) ||
             p_ad->Is(AdFlag::is_tongxin_ad);
  });

  // 无效线索
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_lps_acquisition_clk_lps,
                       "ad_dsp_lps_acquisition_clk_lps",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad == nullptr || p_ctx == nullptr) {
        return false;
      }
      if (p_ctx->enable_customer_acquisition_cvr_request_
         && RankKconfUtil::InvalidCluesEcpcIndustry()->find(p_ad->get_first_industry_id_v5())
         != RankKconfUtil::InvalidCluesEcpcIndustry()->end() &&
         (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED
        || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION
        || p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT
        || p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT
        || p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED)) {
        return true;
      }
      return false;
  });

  // 有效获客泛化新模型
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_lps_acquisition_generalization,
                       "ad_dsp_lps_acquisition_generalization",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad == nullptr || p_ctx == nullptr) {
        return false;
      }
      if (p_ctx->enable_lps_acquisition_generalization_
         && p_ad->Is(AdFlag::is_lps_deep_generalization)) {
        bool admit_b = (p_ctx->acquisition_generalization_whiteproduct_list_ != nullptr
            && p_ctx->acquisition_generalization_whiteproduct_list_->find(p_ad->get_product_name())
            != p_ctx->acquisition_generalization_whiteproduct_list_->end());
        bool admit_c = (p_ctx->acquisition_generalization_whiteproduct_list_forC_ != nullptr
            && p_ctx->acquisition_generalization_whiteproduct_list_forC_->find(p_ad->get_product_name())
            != p_ctx->acquisition_generalization_whiteproduct_list_forC_->end());
        if (SPDM_enable_clue_optimize_switch(p_ctx->session_data_->get_spdm_ctx())) {
          return admit_b || admit_c || p_ad->Is(AdFlag::is_clue_optimize_switch);
        }
        return admit_b || admit_c;
      }
      if (p_ctx->enable_iaa_acquisition_generalization_ && p_ctx->iaa_acq_gen_page_flag_) {
        bool admit_iaa = ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE
          || p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION)
          && p_ctx->iaa_acquisition_generalization_whiteocpx_set_ != nullptr
          && p_ctx->iaa_acquisition_generalization_whiteocpx_set_->find(p_ad->get_ocpx_action_type())
          != p_ctx->iaa_acquisition_generalization_whiteocpx_set_->end());
        if (admit_iaa) {
          auto iaa_acq_gen_account_white_json =
            RankKconfUtil::iaaAcqGenAccountWhiteListAndEcpcParams();
          const auto& iaa_tag_2_conf_map =
            iaa_acq_gen_account_white_json->data().tag_2_params_map;
          if (p_ctx->enable_skip_sheild_budget_by_version_whitejson_) {
            if (!p_ctx->iaa_acq_gen_version_list_.empty() && !iaa_tag_2_conf_map.empty()) {
              for (const auto& version : p_ctx->iaa_acq_gen_version_list_) {
                auto tag = absl::StrCat(version, "_", p_ad->get_account_id());
                if (iaa_tag_2_conf_map.find(tag) != iaa_tag_2_conf_map.end()) {
                  p_ad->Attr(ItemIdx::iaa_acq_gen_tag).
                    SetIntValue(p_ad->AttrIndex(), true, false, false);
                  RANK_DOT_STATS(p_ctx->session_data_, 1, "outerloopcmdregister.iaaacquisition",
                    "white_json_budget", version,
                    kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
                  return true;
                }
              }
              return false;
            } else {
              return false;
            }
          } else if (p_ctx->enable_iaa_acq_gen_ecpc_on_skip_sheild_budget_) {
            // 如果只在放开屏蔽账户生效策略，必须在放开屏蔽白名单中
            if (p_ctx->skip_sheild_budget_whiteset_ != nullptr
              && p_ctx->skip_sheild_budget_whiteset_->find(p_ad->get_account_id())
              != p_ctx->skip_sheild_budget_whiteset_->end()) {
              p_ad->Attr(ItemIdx::iaa_acq_gen_tag).
                SetIntValue(p_ad->AttrIndex(), true, false, false);
              RANK_DOT_STATS(p_ctx->session_data_, 1, "outerloopcmdregister.iaaacquisition",
                "white_budget", kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
              return true;
            } else {
              return false;
            }
          }
          bool iaa_acq_gen_tag_value = true;
          p_ad->Attr(ItemIdx::iaa_acq_gen_tag).
            SetIntValue(p_ad->AttrIndex(), iaa_acq_gen_tag_value, false, false);
          RANK_DOT_STATS(p_ctx->session_data_, 1, "outerloopcmdregister.iaaacquisition",
            "white_ocpx", kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
          return true;
        }
      }
      return false;
  });

  // c2 表单, ctr 部分[ 软硬广拆头]
     if (!SPDM_enable_outer_ctr_second_stage_predict(cmd_curator_ctx_->session_data_->get_spdm_ctx()) || (
        cmd_curator_ctx_->session_data_->get_sub_page_id() != ******** &&
        cmd_curator_ctx_->session_data_->get_sub_page_id() != ********)) {
           // c2 表单, ctr 部分
          p_cmd_curator->RegisterCmd(
              new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                              engine_base::PredictType::PredictType_server_show_cvr,
                              "ad_dsp_item_imp_clk",
                              CMD_SOURCE_AD_DSP),
          [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
            if (p_ctx->enable_c2_lps_ownctr_ &&
                !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
                !p_ad->Is(AdFlag::is_direct_ecom) &&
                (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
                (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
              return true;
            }
            // 快聘作品表单，请求统一表单模型
            if (p_ctx->enable_c2_lps_ownctr_ &&
                !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
                !p_ad->Is(AdFlag::is_direct_ecom) &&
                (p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
                p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
              return true;
            }
            // 线索统一 ctr: 引入私信留资
            if (SPDM_enable_leads_unify_cvr(p_ctx->session_data_->get_spdm_ctx())) {
              if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                  p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
                  !p_ad->Is(AdFlag::is_direct_ecom) &&
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
                return true;
              }
              if (p_ad->Is(AdFlag::is_self_service_ad) &&
                  !p_ad->Is(AdFlag::is_live_ad) &&
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
                return true;
              }
            }
            return false;
          });
        } else {
           // c2 表单, ctr 部分
          p_cmd_curator->RegisterCmd(
              new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
                {engine_base::RType::CVR}, {engine_base::PredictType::PredictType_server_show_cvr},
                {engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_ctr_itemclick_toplayer},
                 "ad_dsp_item_imp_clk", CMD_SOURCE_AD_DSP),
          [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
            if (p_ctx->enable_c2_lps_ownctr_ &&
                !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
                !p_ad->Is(AdFlag::is_direct_ecom) &&
                (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
                (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
              return true;
            }
            // 快聘作品表单，请求统一表单模型
            if (p_ctx->enable_c2_lps_ownctr_ &&
                !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
                !p_ad->Is(AdFlag::is_direct_ecom) &&
                (p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
                p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
              return true;
            }
            // 线索统一 ctr: 引入私信留资
            if (SPDM_enable_leads_unify_cvr(p_ctx->session_data_->get_spdm_ctx())) {
              if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
                  p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
                  !p_ad->Is(AdFlag::is_direct_ecom) &&
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
                return true;
              }
              if (p_ad->Is(AdFlag::is_self_service_ad) &&
                  !p_ad->Is(AdFlag::is_live_ad) &&
                  (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
                return true;
              }
            }
            return false;
          });
        }

  // c2 表单
  // 无点击转化
  if (SPDM_enable_fill_lps_add_noctcvr(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> pts = {
      engine_base::PredictType::PredictType_landingpage_submit_rate_c2,
      engine_base::PredictType::PredictType_landingpage_submit_rate_c2_noctcvr
    };
    static const std::vector<engine_base::RType> rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(rts, pts, "ad_dsp_item_clk_lps_noctcvr", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_c2_lps_ &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
          (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
        return true;
      }
      // 快聘作品表单，请求统一表单模型
      if (p_ctx->enable_c2_lps_ &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
          p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
        return true;
      }
      // 线索统一 cvr: 引入私信留资
      if (SPDM_enable_leads_unify_cvr(p_ctx->session_data_->get_spdm_ctx())) {
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
            !p_ad->Is(AdFlag::is_direct_ecom) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_self_service_ad) &&
            !p_ad->Is(AdFlag::is_live_ad) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
          return true;
        }
      }
      return false;
    });
  } else {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_landingpage_submit_rate_c2,
                      "ad_dsp_item_clk_lps",
                      CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_c2_lps_ &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
          (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
        return true;
      }
      // 快聘作品表单，请求统一表单模型
      if (p_ctx->enable_c2_lps_ &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
          p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
        return true;
      }
      // 线索统一 cvr: 引入私信留资
      if (SPDM_enable_leads_unify_cvr(p_ctx->session_data_->get_spdm_ctx())) {
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
            !p_ad->Is(AdFlag::is_direct_ecom) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_self_service_ad) &&
            !p_ad->Is(AdFlag::is_live_ad) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
          return true;
        }
      }
      return false;
    });
  }

  // c1 表单硬软广拆分
  if (SPDM_enable_ad_dsp_multi_imp_lps(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_landingpage_submit,
                        "ad_dsp_item_multi_imp_lps",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      // c2 表单开关, 不走 c1 表单
      if (p_ctx->enable_c2_lps_) {
          return false;
      }
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
          (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
          p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION))) {
        return true;
      }
      // 快聘作品表单，请求统一表单模型
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
          p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
        return true;
      }
      return false;
    });
  } else {
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                          engine_base::PredictType::PredictType_landingpage_submit,
                          "ad_dsp_item_imp_lps",
                          CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        // c2 表单开关, 不走 c1 表单
        if (p_ctx->enable_c2_lps_) {
            return false;
        }
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
            !p_ad->Is(AdFlag::is_direct_ecom) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
            (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
            p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION))) {
          return true;
        }
        // 快聘作品表单，请求统一表单模型
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
            !p_ad->Is(AdFlag::is_direct_ecom) &&
            (p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
            p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
          return true;
        }
        return false;
      });
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_landingpage_submit,
                      "ad_dsp_item_imp_lps_wechat",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
        !p_ad->Is(AdFlag::is_direct_ecom) &&
        (SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED)) {
      return true;
    }
    if (p_ad->Is(AdFlag::is_self_service_ad) &&
        !p_ad->Is(AdFlag::is_live_ad) &&
        (SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED)) {
      return true;
    }
    return false;
  });

    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_cvr,
                       "ad_dsp_item_click_conv_photo",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
         p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) {
        return true;
      }
      return false;
    });
  p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_finance_ensemble_imp_lps,
                        "ad_dsp_finance_ensemble_imp_lps",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_finance_ad)
          && (p_ctx->fin_finance_lps_ensemble_industry_->count(p_ad->get_industry_id_v3()) > 0)
          && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED)) {
        return true;
      }
      return false;
  });

  p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_edu_ensemble_imp_lps,
                        "ad_dsp_edu_ensemble_imp_lps",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      // 是否屏蔽行业直播
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
        return false;
      }
      if (p_ctx->enable_edu_ensemble_imp_lps_ &&
          !p_ctx->enable_merge_clue_and_edu_ &&
          p_ad->Is(AdFlag::is_education_ad) &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
          (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
        return true;
      }
      return false;
    });

  // 教育行业表单到深度 ecpc 模型 正价课目标
  p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        engine_base::PredictType::PredictType_edu_lps_deep_rate,
                        "ad_dsp_edu_lps_deep_model",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      // 屏蔽行业直播和搜索流量
      if (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE) {
        return false;
      }
      if (p_ctx->enable_edu_lps_deep_ecpc_ &&
          (p_ctx->edu_lps_deep_ecpc_white_product_ != nullptr &&
           p_ctx->edu_lps_deep_ecpc_white_product_->count(p_ad->get_product_name()) > 0 ||
           p_ad->get_deep_conversion_type() ==
               kuaishou::ad::AdCallbackLog_EventType_EVENT_HIGH_PRICE_CLASS_PAY) &&
          (p_ad->Is(AdFlag::is_education_ad) || (p_ctx->enable_edu_lps_deep_ecpc_other_ind_ && !p_ad->Is(AdFlag::is_education_ad))) &&  // NOLINT
           p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_auc_model_base,
                       "ad_dsp_item_click_conv_photo_auc_base",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_dsplive_p2l_conv_bypass_auc) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
          p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) {
        return true;
      }
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "ad_dsp_item_click_conv_photo_auc_exp",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_dsplive_p2l_conv_bypass_auc) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
          p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) {
        return true;
      }
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_c1_order_paid,
                       "c1_order_paied_local_store_cmd",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_LOCAL_STORE_ORDER &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED)  {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_c1_order_paid,
                       "c1_order_paied_lead_retention_cmd",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_KWAI_CLASS_PROMOTION ||
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_KWAI_LEAD_RETENTION) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED)  {
      return true;
    }
    return false;
  });

  if (cmd_curator_ctx_->session_data_->common_r_
        ->GetIntCommonAttr("enable_clean_inefficient_cmdkey").value_or(0) == 0) {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_non_merchant_live_cvr,
                      "ad_dsp_live_cvr_order_paied",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED)  {
      return true;
    }
    return false;
  });
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                      engine_base::PredictType::PredictType_non_merchant_live_cvr,
  "ad_dsp_live_cvr_order_paied_photo", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED)  {
      return true;
    }
    return false;
  });

  if (!SPDM_enable_outer_prm_second_stage_predict(cmd_curator_ctx_->session_data_->get_spdm_ctx()) || (
    cmd_curator_ctx_->session_data_->get_sub_page_id() != ******** &&
    cmd_curator_ctx_->session_data_->get_sub_page_id() != ********)) {
    if (SPDM_enable_ad_dsp_multi_imp_prm(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
      p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                          PredictType::PredictType_landingpage_submit,
                          "ad_dsp_item_multi_imp_lps_message", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
            !p_ad->Is(AdFlag::is_direct_ecom) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
            !(p_ctx->enable_ad_dsp_item_imp_lps_message_local &&
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_self_service_ad) &&
            !p_ad->Is(AdFlag::is_live_ad) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
            !(p_ctx->enable_ad_dsp_item_imp_lps_message_local &&
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
          return true;
        }
        return false;
    });
    } else {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                        PredictType::PredictType_landingpage_submit,
                        "ad_dsp_item_imp_lps_message", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
          !(p_ctx->enable_ad_dsp_item_imp_lps_message_local &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_self_service_ad) &&
          !p_ad->Is(AdFlag::is_live_ad) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
          !(p_ctx->enable_ad_dsp_item_imp_lps_message_local &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
        return true;
      }
      return false;
    });
  }
  } else {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
        {engine_base::RType::CVR}, {engine_base::PredictType::PredictType_landingpage_submit},
        {engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_prm_ctr_toplayer, engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_prm_pcvr}, // NOLINT
         "ad_dsp_item_imp_lps_message", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
        !p_ad->Is(AdFlag::is_direct_ecom) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
        !(p_ctx->enable_ad_dsp_item_imp_lps_message_local &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
      return true;
    }
    if (p_ad->Is(AdFlag::is_self_service_ad) &&
        !p_ad->Is(AdFlag::is_live_ad) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
        !(p_ctx->enable_ad_dsp_item_imp_lps_message_local &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
      return true;
    }
    return false;
  });
} // NOLINT


  // 本地原生链路广告 私信消息数 cmd
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       PredictType::PredictType_landingpage_submit,
                       "ad_dsp_item_imp_lps_message_local", CMD_SOURCE_AD_DSP),
   [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
     if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
         p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
         !p_ad->Is(AdFlag::is_direct_ecom) &&
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
         (p_ctx->enable_ad_dsp_item_imp_lps_message_local &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
       return true;
     }
     if (p_ad->Is(AdFlag::is_self_service_ad) &&
         !p_ad->Is(AdFlag::is_live_ad) &&
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
         (p_ctx->enable_ad_dsp_item_imp_lps_message_local &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
       return true;
     }
     return false;
  });
  if (!SPDM_enable_outer_leads_second_stage_predict(cmd_curator_ctx_->session_data_->get_spdm_ctx()) || (
    cmd_curator_ctx_->session_data_->get_sub_page_id() != ******** &&
    cmd_curator_ctx_->session_data_->get_sub_page_id() != ********)) {
      if (SPDM_enable_ad_dsp_multi_imp_leads(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
        p_cmd_curator->RegisterMultiPredictCmd(
          new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                           PredictType::PredictType_landingpage_submit,
                           "ad_dsp_item_multi_imp_lps_leadssubmit", CMD_SOURCE_AD_DSP),
       [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
         if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
             p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
             !p_ad->Is(AdFlag::is_direct_ecom) &&
             (p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION)) &&
             !(p_ctx->enable_ad_dsp_item_imp_lps_leadssubmit_local &&
               p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
           return true;
         }
         if (p_ad->Is(AdFlag::is_self_service_ad) &&
             !p_ad->Is(AdFlag::is_live_ad) &&
             p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
             !(p_ctx->enable_ad_dsp_item_imp_lps_leadssubmit_local &&
               p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
           return true;
         }
         return false;
      });
      } else {
        p_cmd_curator->RegisterCmd(
            new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                            PredictType::PredictType_landingpage_submit,
                            "ad_dsp_item_imp_lps_leadssubmit", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          // 是否开启了 ab 开关，且是门店私信智能优选账户
          bool is_locallife_prmn_selected = false;
          if (SPDM_request_local_life_promotion_selected_way(p_ctx->session_data_->get_spdm_ctx()) > 0 &&
              p_ad->Is(AdFlag::is_locallife_prmn_selected_set)) {
              is_locallife_prmn_selected = true;
          }
          if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION &&
               p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
               is_locallife_prmn_selected) {  // 门店私信智能优选额外请求模型
            return true;
          }
          if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
              p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
              !p_ad->Is(AdFlag::is_direct_ecom) &&
              (p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION)) &&
              !(p_ctx->enable_ad_dsp_item_imp_lps_leadssubmit_local &&
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_self_service_ad) &&
              !p_ad->Is(AdFlag::is_live_ad) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
              !(p_ctx->enable_ad_dsp_item_imp_lps_leadssubmit_local &&
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
            return true;
          }
          return false;
        });
     }
  } else {
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
          {engine_base::RType::CVR}, {engine_base::PredictType::PredictType_landingpage_submit},
          {engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_prm_ctr_toplayer, engine_base::PredictEmbeddingType::PredictEmbeddingType_outer_leads_pcvr}, // NOLINT
           "ad_dsp_item_imp_lps_leadssubmit", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      // 是否开启了 ab 开关，且是门店私信智能优选账户
      bool is_locallife_prmn_selected = false;
      if (SPDM_request_local_life_promotion_selected_way(p_ctx->session_data_->get_spdm_ctx()) > 0 &&
          p_ad->Is(AdFlag::is_locallife_prmn_selected_set)) {
          is_locallife_prmn_selected = true;
      }
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
          is_locallife_prmn_selected) {  // 门店私信智能优选额外请求模型
        return true;
      }
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION)) &&
          !(p_ctx->enable_ad_dsp_item_imp_lps_leadssubmit_local &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_self_service_ad) &&
          !p_ad->Is(AdFlag::is_live_ad) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
          !(p_ctx->enable_ad_dsp_item_imp_lps_leadssubmit_local &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION)) {
        return true;
      }
      return false;
    });
  }
  // 本地原生链路广告 私信留资 cmd
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       PredictType::PredictType_landingpage_submit,
                       "ad_dsp_item_imp_lps_leadssubmit_local", CMD_SOURCE_AD_DSP),
   [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      // 是否开启了 ab 开关，且是门店私信智能优选账户
      bool is_locallife_prmn_selected = false;
      if (SPDM_request_local_life_promotion_selected_way(p_ctx->session_data_->get_spdm_ctx()) > 0 &&
          p_ad->Is(AdFlag::is_locallife_prmn_selected_set)) {
          is_locallife_prmn_selected = true;
      }
     if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
         p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
         !p_ad->Is(AdFlag::is_direct_ecom) &&
         (p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
         (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION)) &&
         (p_ctx->enable_ad_dsp_item_imp_lps_leadssubmit_local &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION &&
          !is_locallife_prmn_selected &&
          !SPDM_enable_request_local_life_promotion_nobagging(p_ctx->session_data_->get_spdm_ctx()))) {
          // 门店私信智能优选与 nobagging 请求新 base 模型
       return true;
     }
     if (p_ad->Is(AdFlag::is_self_service_ad) &&
         !p_ad->Is(AdFlag::is_live_ad) &&
         p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
         (p_ctx->enable_ad_dsp_item_imp_lps_leadssubmit_local &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION &&
          !is_locallife_prmn_selected &&
          !SPDM_enable_request_local_life_promotion_nobagging(p_ctx->session_data_->get_spdm_ctx()))) {
          // 门店私信智能优选与 nobagging 请求新 base 模型
       return true;
     }
     return false;
  });

  // 本地原生链路广告 私信留资 新 cmd output 为 list
  static const std::vector<engine_base::PredictType> locallife_prmn_pts = {
    engine_base::PredictType::PredictType_locallife_prmn_leadssubmit_local,
    engine_base::PredictType::PredictType_locallife_prmn_leadssubmit_other
  };
  static const std::vector<engine_base::RType> locallife_prmn_rts = {
    engine_base::RType::CVR,
    engine_base::RType::CVR
  };
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(locallife_prmn_rts, locallife_prmn_pts,
                       "ad_dsp_item_imp_lps_leadssubmit_local_multi", CMD_SOURCE_AD_DSP),
   [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      // 是否开启了 ab 开关，且是门店私信智能优选账户
      bool is_locallife_prmn_selected = false;
      if (SPDM_request_local_life_promotion_selected_way(p_ctx->session_data_->get_spdm_ctx()) > 0 &&
          p_ad->Is(AdFlag::is_locallife_prmn_selected_set)) {
          is_locallife_prmn_selected = true;
      }
     if (p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_LOCAL_LIFE_PROMOTION &&
        (is_locallife_prmn_selected ||
        SPDM_enable_request_local_life_promotion_nobagging(p_ctx->session_data_->get_spdm_ctx()))) {
       return true;
     }
     return false;
  });
  if (SPDM_enable_leads_message_queue_predict(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    p_cmd_curator->RegisterMultiPredictCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_leads_message_industry,
                       "ad_dsp_item_imp_msg_queue_multi", CMD_SOURCE_AD_DSP),
     [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
       if (!SPDM_enable_private_leads_model_ensemble(p_ctx->session_data_->get_spdm_ctx())) {
         return false;
       }
       if (SPDM_enable_healthcare_single_tower(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
       }
       if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
           p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
           !p_ad->Is(AdFlag::is_direct_ecom) &&
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT) {
         return true;
       }
       if (p_ad->Is(AdFlag::is_self_service_ad) &&
           !p_ad->Is(AdFlag::is_live_ad) &&
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT) {
         return true;
       }
       return false;
    });

    p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                         engine_base::PredictType::PredictType_leads_message_industry,
                         "ad_dsp_item_imp_lds_queue_multi", CMD_SOURCE_AD_DSP),
     [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
       if (!SPDM_enable_private_leads_model_ensemble(p_ctx->session_data_->get_spdm_ctx())) {
         return false;
       }
       if (SPDM_enable_healthcare_single_tower(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
       }
       if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
           p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
           !p_ad->Is(AdFlag::is_direct_ecom) &&
           (p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
           p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION))) {
         return true;
       }
       if (p_ad->Is(AdFlag::is_self_service_ad) &&
           !p_ad->Is(AdFlag::is_live_ad) &&
           p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
         return true;
       }
       return false;
    });
  } else {
  // 模型 ensemble 新建 cmd key
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                     engine_base::PredictType::PredictType_leads_message_industry,
                     "ad_dsp_item_imp_sent_message", CMD_SOURCE_AD_DSP),
   [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
     if (!SPDM_enable_private_leads_model_ensemble(p_ctx->session_data_->get_spdm_ctx())) {
       return false;
     }
     if (SPDM_enable_healthcare_single_tower(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
     }
     if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
         p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
         !p_ad->Is(AdFlag::is_direct_ecom) &&
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT) {
       return true;
     }
     if (p_ad->Is(AdFlag::is_self_service_ad) &&
         !p_ad->Is(AdFlag::is_live_ad) &&
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT) {
       return true;
     }
     return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_leads_message_industry,
                       "ad_dsp_item_imp_leads_submit", CMD_SOURCE_AD_DSP),
   [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
     if (!SPDM_enable_private_leads_model_ensemble(p_ctx->session_data_->get_spdm_ctx())) {
       return false;
     }
     if (SPDM_enable_healthcare_single_tower(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
     }
     if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
         p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
         !p_ad->Is(AdFlag::is_direct_ecom) &&
         (p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
         (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION))) {
       return true;
     }
     if (p_ad->Is(AdFlag::is_self_service_ad) &&
         !p_ad->Is(AdFlag::is_live_ad) &&
         p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
       return true;
     }
     return false;
  });
  }

  static const std::vector<engine_base::PredictType> healthcare_multi_predicts = {
    engine_base::PredictType::PredictType_leads_message_industry,
    engine_base::PredictType::PredictType_leads_message_healthcare};

  static const std::vector<engine_base::RType> healthcare_multi_rtypes(
    healthcare_multi_predicts.size(), engine_base::RType::CVR);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(healthcare_multi_rtypes,
                      healthcare_multi_predicts,
                      "ad_dsp_item_imp_sent_message_multi", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!SPDM_enable_private_leads_model_ensemble(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
          SPDM_enable_healthcare_single_tower(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_self_service_ad) &&
          !p_ad->Is(AdFlag::is_live_ad) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
          SPDM_enable_healthcare_single_tower(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      return false;
    });

    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(healthcare_multi_rtypes,
                        healthcare_multi_predicts,
                        "ad_dsp_item_imp_leads_submit_multi", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!SPDM_enable_private_leads_model_ensemble(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
          !p_ad->Is(AdFlag::is_direct_ecom) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION)) &&
          SPDM_enable_healthcare_single_tower(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_self_service_ad) &&
          !p_ad->Is(AdFlag::is_live_ad) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
          SPDM_enable_healthcare_single_tower(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      return false;
    });


  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                       engine_base::PredictType::PredictType_prm_leads_submit,
                       "ad_dsp_prm_leads_submit",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad == nullptr || p_ctx == nullptr) {
        return false;
      }
      if (SPDM_enable_prm_2_leads_set_dcvr(p_ctx->session_data_->get_spdm_ctx())
         && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT
         && p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_LEADS_SUBMIT) {
        return true;
      }
      return false;
  });

  static const std::vector<engine_base::PredictType> lps_leads_integration_pts = {
    engine_base::PredictType::PredictType_lps_leads_integration_leads_prob,
    engine_base::PredictType::PredictType_lps_leads_integration_lps_prob
  };
  static const std::vector<engine_base::RType> lps_leads_integration_rts = {
    engine_base::RType::CVR,
    engine_base::RType::CVR
  };
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(lps_leads_integration_rts, lps_leads_integration_pts,
                      "ad_dsp_lps_leads_landingpage_integration_cmd", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!SPDM_enable_new_form_pm_integration(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    auto auto_manage_open = p_ad->get_auto_manage();
    const kuaishou::ad::AdEnum::CampaignType campaign_type = p_ad->get_campaign_type();
    int64_t sub_conversion_path = p_ad->Attr(
      ItemIdx::fd_ad_magicsite_page_das_sub_conversion_path).GetIntValue(p_ad->AttrIndex()).value_or(0);
    bool admit_qiwei_narrow = ((sub_conversion_path & 4) != 4);
    bool admit_sixin_narrow = ((sub_conversion_path & 1) != 1);

    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED &&
              auto_manage_open == kuaishou::ad::AdEnum::AUTO_MANAGE_OPEN &&
              campaign_type == kuaishou::ad::AdEnum::SITE_PAGE && admit_qiwei_narrow && admit_sixin_narrow) {
      if (p_ad->Is(AdFlag::is_white_integration_ual_list)) {
          return true;
      }
    }

    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED &&
              auto_manage_open != kuaishou::ad::AdEnum::AUTO_MANAGE_OPEN &&
              campaign_type == kuaishou::ad::AdEnum::SITE_PAGE && admit_qiwei_narrow && admit_sixin_narrow) {
      if (p_ad->Is(AdFlag::is_white_integration_list)) {
          return true;
      }
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_pm_integration_single_treat_prob,
                                    "ad_dsp_landingpage_integration_single_cmd", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!SPDM_enable_form_pm_integration_single_model(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    auto auto_manage_open = p_ad->get_auto_manage();
    const kuaishou::ad::AdEnum::CampaignType campaign_type = p_ad->get_campaign_type();
    int64_t sub_conversion_path = p_ad->Attr(
      ItemIdx::fd_ad_magicsite_page_das_sub_conversion_path).GetIntValue(p_ad->AttrIndex()).value_or(0);
    bool admit_qiwei_narrow = ((sub_conversion_path & 4) != 4);
    bool admit_sixin_narrow = ((sub_conversion_path & 1) != 1);

    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED &&
              auto_manage_open == kuaishou::ad::AdEnum::AUTO_MANAGE_OPEN &&
              campaign_type == kuaishou::ad::AdEnum::SITE_PAGE && admit_qiwei_narrow && admit_sixin_narrow) {
      if (p_ad->Is(AdFlag::is_white_integration_ual_list)) {
          return true;
      }
    }

    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED &&
              auto_manage_open != kuaishou::ad::AdEnum::AUTO_MANAGE_OPEN &&
              campaign_type == kuaishou::ad::AdEnum::SITE_PAGE && admit_qiwei_narrow && admit_sixin_narrow) {
      if (p_ad->Is(AdFlag::is_white_integration_list)) {
          return true;
      }
    }
    return false;
  });

  return;
}  // NOLINT

void OuterCmdRegister::RegisterDeepCvrCmd(CuratorTypeV2 *p_cmd_curator) const {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_lps_valid_clues,
                       "la_lps_valid_clues",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
          (!p_ctx->enable_real_deep_wechat_connected_lps_cvr_set &&
               p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE)) &&
          ((RankKconfUtil::laBidEnhancementDefaultWhiteList()->find(p_ad->get_product_name()) !=
                RankKconfUtil::laBidEnhancementDefaultWhiteList()->end() ||
            RankKconfUtil::laBidEnhancementProductWhiteList() ->find(p_ad->get_product_name()) !=
                RankKconfUtil::laBidEnhancementProductWhiteList()->end() ||
            (p_ad->Is(AdFlag::is_deep_conv_deep_valid_clues) ||
            (!p_ctx->enable_real_deep_wechat_connected_lps_cvr_set &&
      p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_WECHAT_CONNECTED)) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
            (!p_ctx->enable_real_deep_wechat_connected_lps_cvr_set &&
                 p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE))) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED &&
            p_ad->Is(AdFlag::is_tongxin_ad))) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_conv_key_inapp_action_rate,
                       "dsp_rank_key_action",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION) ||
            (p_ctx->key_action_account_map_->count(p_ad->get_account_id()) > 0))) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_conv_24h_stay,
      "ad_dsp_conv_24h_stay", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
          (p_ad->get_deep_conversion_type() ==
              kuaishou::ad::AdCallbackLog_EventType_EVENT_24H_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY)) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_conv_7_day_stay,
                       "ad_dsp_conv_week_stay",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((p_ad->Is(AdFlag::is_deep_conv_retention) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY) &&
            (RankKconfUtil::sevenDayStayTwinAccountWhiteList()->find(p_ad->get_account_id()) !=
                RankKconfUtil::sevenDayStayTwinAccountWhiteList()->end() ||
            p_ad->get_enhance_conversion_type() ==
                kuaishou::ad::AdCallbackLog_EventType_EVENT_WEEK_STAY)) ||
                (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
                  p_ad->get_deep_conversion_type() ==
                  kuaishou::ad::AdCallbackLog_EventType_EVENT_WEEK_STAY)) {
          return true;
        }
        if (p_ctx->ad_roas_drop_admit_ != nullptr && p_ctx->enable_ecpc_add_weekstay_model) {
          if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
              != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
              p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
              != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
             return true;
          }
        }
        return false;
  });

  if (SPDM_enable_everyday_retention_new_cmd(cmd_curator_ctx_->session_data_->get_spdm_ctx())) { // NOLINT
    static const std::vector<engine_base::PredictType> pt_all = {
      engine_base::PredictType::PredictType_everyday_stay,
      engine_base::PredictType::PredictType_1day_stay,
      engine_base::PredictType::PredictType_2day_stay,
      engine_base::PredictType::PredictType_3day_stay,
      engine_base::PredictType::PredictType_4day_stay,
      engine_base::PredictType::PredictType_5day_stay,
      engine_base::PredictType::PredictType_6day_stay,
      engine_base::PredictType::PredictType_7day_stay};
     static const std::vector<engine_base::RType> rt_all = {
      engine_base::RType::DEEP_CVR,
      engine_base::RType::UNCATEGORIZED,
      engine_base::RType::UNCATEGORIZED,
      engine_base::RType::UNCATEGORIZED,
      engine_base::RType::UNCATEGORIZED,
      engine_base::RType::UNCATEGORIZED,
      engine_base::RType::UNCATEGORIZED,
      engine_base::RType::UNCATEGORIZED};
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
                       rt_all,
                       pt_all,
                       "ad_dsp_conv_everyday_stay1",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
                    p_ad->get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_RETENTION_DAYS) ||
               ((SPDM_enable_everyday_retention_single(p_ctx->session_data_->get_spdm_ctx())) &&  //NOLINT
               (p_ad->get_ocpx_action_type() ==
                    kuaishou::ad::EVENT_RETENTION_DAYS))) {
              return true;
            }
          return false;
    });
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_deep_cvr,
                       "ad_dsp_live_click_conv_pay",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_search_duanju_independent &&
        p_ctx->session_data_->get_is_search_request()) {
        return false;
      }
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION &&
          !(SPDM_enableYanhangProABtest() &&
            (p_ctx->yanhang_pro_product_map->count(p_ad->get_product_name()) > 0 ||
             p_ad->get_is_yanhang()))) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_non_merchant_live_deep_cvr,
                       "ad_dsp_live_click_conv_pay_coldstart",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION &&
          (SPDM_enableYanhangProABtest() &&
           (p_ctx->yanhang_pro_product_map->count(p_ad->get_product_name()) > 0 ||
            p_ad->get_is_yanhang()))) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_click_purchase,
                       "rank_ad_dsp_click_purchase",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_deep_conv_purchase) &&
        (p_ad->Is(AdFlag::is_insurance_ad) ||
         !(p_ad->get_twin_bid_strategy() == kuaishou::ad::AdEnum::ONLY_DEEP_DEVICE ||
           p_ad->get_twin_bid_strategy() == kuaishou::ad::AdEnum::MIN_OCPC_DEEP_DEVICE))) {
      return true;
    }
    return false;
  });

  // SDPA 拉活付费模型单拆
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::DEEP_CVR,
                       engine_base::PredictType::PredictType_purchase,
                       "ad_rank_sdpa_app_invoked2_purchase", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!SPDM_enable_sdpa_purchase_to_zhongtai(p_ctx->session_data_->get_spdm_ctx()) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE &&
        p_ad->Is(AdFlag::is_sdpa_ad)) {
      return true;
    }
    return false;
  });

  return;
}

void OuterCmdRegister::RegisterLtvCmd(CuratorTypeV2 *p_cmd_curator) const {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_game_purchase_ltv,
                                    "ad_dsp_game_purchase_ltv_twin_bid",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!p_ctx->enable_deep_cvr_shelve_ &&
              p_ad->get_industry_parent_id_v3() == 1018 &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_purchase_ltv,
                                    "ad_dsp_purchase_ltv",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_is_purchase_pay_test()) {
        return true;
      }
      return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_game_conv_ltv,
                                    "ad_dsp_game_purchase_ltv_new",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!p_ctx->enable_ad_dsp_playlet_purchase_ltv) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
          p_ad->get_second_industry_id_v5() == 2012 &&
          (p_ctx->enable_playlet_conversion_predict ||
          p_ctx->playlet_model_conv_tail_->count(p_ad->get_account_id() % 100) > 0)) {
            return false;
      }
      if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS
          && ((p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)
          || (p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())))) ||
          (p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM)) {
        return true;
      }
      if (p_ad->get_admit_industry_purchase_ltv()) {
        return true;
      }
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_game_conv_ltv,
                                    "ad_dsp_playlet_purchase_ltv",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_ad_dsp_playlet_purchase_ltv && !p_ctx->enable_only_playlet_invoked_model_predict_ltv_old) {  // NOLINT
      if (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS || (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)))  // NOLINT
          && ((p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)
          || (p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())))) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM)) {
        return true;
      }
      if (p_ad->get_admit_industry_purchase_ltv()) {
        return true;
      }
    }
    if (p_ctx->enable_only_playlet_invoked_model_predict_ltv_old &&
      p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
      ((p_ctx->mini_app_roas_invoked_product_set_ != nullptr &&
      p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0) ||
      (p_ctx->mini_app_roas_invoked_account_set_ != nullptr &&
      p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())))) {
        return true;
    }
    if (p_ctx->enable_only_playlet_invoked_model_predict_account &&
        p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
        p_ctx->is_playlet_invoked_tail_ != nullptr &&
        p_ctx->is_playlet_invoked_tail_->IsOnFor(p_ad->get_account_id())) {
        return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_industry_pay_ltv,
                                    "ad_dsp_industry_pay_ltv",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_request_industry_pay_ltv &&
        !p_ctx->enable_request_industry_pay_ltv_new &&
        p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_industry_pay_ltv,
                                    "ad_dsp_industry_pay_ltv_new",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_request_industry_pay_ltv_new &&
        !p_ctx->enable_only_playlet_invoked_model_predict_ltv_new_normal &&
        p_ad->Is(AdFlag::is_paid_duanju_ad_v3)  &&  // NOLINT
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS) {
      return true;
    }
    if (p_ctx->enable_only_playlet_invoked_model_predict_ltv_new_normal &&
      p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
      ((p_ctx->mini_app_roas_invoked_product_set_ != nullptr &&
      p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0) ||
      (p_ctx->mini_app_roas_invoked_account_set_ != nullptr &&
      p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())))) {
        return true;
    }
    if (p_ctx->enable_only_playlet_invoked_model_predict_account &&
       p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
       p_ctx->is_playlet_invoked_tail_ != nullptr &&
       p_ctx->is_playlet_invoked_tail_->IsOnFor(p_ad->get_account_id())) {
        return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                  engine_base::PredictType::PredictType_game_industry_pay_ltv,
                                  "ad_dsp_game_industry_pay_ltv",
                                  CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx){
      if ((p_ctx->enable_request_game_industry_pay_ltv ||
        p_ctx->enable_request_minigame_industry_pay_ltv ||
        p_ctx->enable_request_game_industry_pay_ltv_fliter) &&
        p_ad->Is(AdFlag::is_game_ad) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS) {
        return true;
      }
      return false;
  });

  // if (SPDM_enable_playlet_uplift_ltv_cmd(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
  //   static const std::vector<engine_base::PredictType> d_ltv_pts = {
  //     engine_base::PredictType::PredictType_smart_offer_two_head_origin_ltv0,
  //     engine_base::PredictType::PredictType_smart_offer_two_head_uplift_ltv1
  //   };
  //   static const std::vector<engine_base::RType> d_ltv_rts = {
  //     engine_base::RType::CVR,
  //     engine_base::RType::CVR
  //   };
  //   p_cmd_curator->RegisterCmd(
  //       new engine_base::CmdWrapperV2(d_ltv_rts, d_ltv_pts,
  //                       "ad_dsp_playlet_uplift_ltv_cmd", CMD_SOURCE_AD_DSP),
  //   [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
  //     if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
  //       return true;
  //     }
  //     return false;
  //   });
  // }

  if (SPDM_enable_playlet_uplift_cvr_cmd(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> d_cvr_pts = {
      engine_base::PredictType::PredictType_smart_offer_two_head_origin_cvr0,
      engine_base::PredictType::PredictType_smart_offer_two_head_uplift_cvr1
    };
    static const std::vector<engine_base::RType> d_cvr_rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(d_cvr_rts, d_cvr_pts,
                        "ad_dsp_playlet_uplift_cvr_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->disable_playlet_uplift_cvr_cmd && p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
        return true;
      }
      return false;
    });
  }
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_playlet_click_pay_purchase,
                                    "ad_dsp_playlet_click_pay_purchase",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_ad_rank_playlet_click_pay_purchase
            && p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
      return true;
    }
    return false;
  });

  if (SPDM_enable_normal_smart_offer_multi_uplift_ltv_cmd_h6(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {  // NOLINT
    static const std::vector<engine_base::PredictType> m_ltv_pts = {
      engine_base::PredictType::PredictType_smart_offer_multi_origin_ltv0,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv1,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv2,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv3,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv4,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv5,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv6,
    };
    static const std::vector<engine_base::RType> m_ltv_rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(m_ltv_rts, m_ltv_pts,
                        "normal_ad_dsp_smart_offer_multi_uplift_ltv_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
        return true;
      }
      return false;
    });
  } else if (SPDM_enable_smart_offer_multi_uplift_ltv_cmd_h6(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {  // NOLINT
    static const std::vector<engine_base::PredictType> m_ltv_pts = {
      engine_base::PredictType::PredictType_smart_offer_multi_origin_ltv0,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv1,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv2,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv3,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv4,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv5,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv6,
    };
    static const std::vector<engine_base::RType> m_ltv_rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(m_ltv_rts, m_ltv_pts,
                        "ad_dsp_smart_offer_multi_uplift_ltv_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
        return true;
      }
      return false;
    });
  } else if (SPDM_enable_smart_offer_multi_uplift_ltv_cmd_h5(cmd_curator_ctx_->session_data_->get_spdm_ctx())) { // NOLINT
    static const std::vector<engine_base::PredictType> m_ltv_pts = {
      engine_base::PredictType::PredictType_smart_offer_multi_origin_ltv0,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv1,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv2,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv3,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv4,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv5
    };
    static const std::vector<engine_base::RType> m_ltv_rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(m_ltv_rts, m_ltv_pts,
                        "ad_dsp_smart_offer_multi_uplift_ltv_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
        return true;
      }
      return false;
    });
  } else if (SPDM_enable_smart_offer_multi_uplift_ltv_cmd_h4(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {  // NOLINT
    static const std::vector<engine_base::PredictType> m_ltv_pts = {
      engine_base::PredictType::PredictType_smart_offer_multi_origin_ltv0,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv1,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv2,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv3,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_ltv4
    };
    static const std::vector<engine_base::RType> m_ltv_rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(m_ltv_rts, m_ltv_pts,
                        "ad_dsp_smart_offer_multi_uplift_ltv_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
        return true;
      }
      return false;
    });
  }

  if (SPDM_enable_normal_smart_offer_multi_uplift_cvr_cmd_h6(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {  // NOLINT
    static const std::vector<engine_base::PredictType> c_cvr_pts = {
      engine_base::PredictType::PredictType_smart_offer_multi_origin_cvr0,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr1,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr2,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr3,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr4,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr5,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr6
    };
    static const std::vector<engine_base::RType> c_cvr_rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(c_cvr_rts, c_cvr_pts,
                        "normal_ad_dsp_smart_offer_multi_uplift_cvr_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
        return true;
      }
      return false;
    });
  } else if (SPDM_enable_smart_offer_multi_uplift_cvr_cmd_h6(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {  // NOLINT
    static const std::vector<engine_base::PredictType> c_cvr_pts = {
      engine_base::PredictType::PredictType_smart_offer_multi_origin_cvr0,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr1,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr2,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr3,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr4,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr5,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr6
    };
    static const std::vector<engine_base::RType> c_cvr_rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(c_cvr_rts, c_cvr_pts,
                        "ad_dsp_smart_offer_multi_uplift_cvr_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
        return true;
      }
      return false;
    });
  } else if (SPDM_enable_smart_offer_multi_uplift_cvr_cmd_h5(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {  // NOLINT
    static const std::vector<engine_base::PredictType> c_cvr_pts = {
      engine_base::PredictType::PredictType_smart_offer_multi_origin_cvr0,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr1,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr2,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr3,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr4,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr5
    };
    static const std::vector<engine_base::RType> c_cvr_rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(c_cvr_rts, c_cvr_pts,
                        "ad_dsp_smart_offer_multi_uplift_cvr_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
        return true;
      }
      return false;
    });
  } else if (SPDM_enable_smart_offer_multi_uplift_cvr_cmd_h4(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {  // NOLINT
    static const std::vector<engine_base::PredictType> c_cvr_pts = {
      engine_base::PredictType::PredictType_smart_offer_multi_origin_cvr0,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr1,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr2,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr3,
      engine_base::PredictType::PredictType_smart_offer_multi_uplift_cvr4
    };
    static const std::vector<engine_base::RType> c_cvr_rts = {
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR,
      engine_base::RType::CVR
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(c_cvr_rts, c_cvr_pts,
                        "ad_dsp_smart_offer_multi_uplift_cvr_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
        return true;
      }
      return false;
    });
  }
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_industry_invoked_pay,
                                    "ad_dsp_industry_invoked_pay",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if ((p_ctx->enable_ad_rank_industry_purchase_predict ||
         p_ctx->enable_ad_rank_industry_invoked_purchase_ltv_predict)  &&
         !p_ctx->enable_ad_rank_industry_purchase_predict_new &&
         p_ad->get_second_industry_id_v5() == 2012) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_industry_invoked_pay,
                                    "ad_dsp_industry_invoked_pay_new",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if ((((p_ctx->enable_ad_rank_industry_purchase_predict_new ||
         p_ctx->enable_ad_rank_industry_invoked_purchase_ltv_predict_new) &&
         p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
         !p_ctx->enable_only_playlet_invoked_model_predict) ||
         (p_ctx->enable_only_playlet_invoked_model_predict &&
         p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
         ((p_ctx->mini_app_roas_invoked_product_set_ != nullptr &&
         p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0) ||
         (p_ctx->mini_app_roas_invoked_account_set_ != nullptr &&
         p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id()))))) ||
         (p_ctx->enable_only_playlet_invoked_model_predict_account &&
          p_ad->Is(AdFlag::is_paid_duanju_ad_v3) &&
          p_ctx->is_playlet_invoked_tail_ != nullptr &&
          p_ctx->is_playlet_invoked_tail_->IsOnFor(p_ad->get_account_id()))) {  // NOLINT
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_playlet_pay_panel,
                                    "ad_dsp_playlet_pay_panel",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (((p_ctx->enable_ad_rank_playlet_pay_panel_purchase ||
        p_ctx->playlet_model_panel_tail_->count(p_ad->get_account_id() % 100))
        || p_ctx->enable_baokuanju_dynamic_coef_adjustment)
        && p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) { // NOLINT
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_playlet_pay_panel_purchase,
                                    "ad_dsp_playlet_pay_panel_purchase",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if ((p_ctx->enable_ad_rank_playlet_pay_panel_purchase ||
        p_ctx->playlet_model_panel_tail_->count(p_ad->get_account_id() % 100)) &&
        p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_industry_conv_pay,
                                    "ad_dsp_industry_conv_pay",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_ad_rank_industry_conv_purchase_ltv_predict &&
        p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_7_day_game_conv_ltv,
                                    "ad_dsp_game_conv_ltv_seven_days_new",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_seven_day_ltv &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS && p_ad->Is(AdFlag::is_roi_7d_roi_mcb_ecpc_ad)) ||  // NOLINT
           ((p_ctx->ad_roas_drop_admit_ != nullptr) &&
            (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
              != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
            p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
              != p_ctx->ad_roas_drop_admit_->admit().account_id().end())))) {
          return true;
        }
        if (p_ad->get_admit_7r_conv_ltv()) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_7_day_purchase_ltv,
                                    "ad_dsp_purchase_ltv_7d",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (SPDM_enable_polaris_request_7r_purchase_ltv(p_ctx->session_data_->get_spdm_ctx()) &&
            (p_ad->get_admit_industry_purchase_ltv() || p_ad->get_is_purchase_pay_test())) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_30_day_game_conv_ltv,
                                    "ad_dsp_game_conv_ltv_30d",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        return false;
  });

  if (cmd_curator_ctx_->session_data_->common_r_
        ->GetIntCommonAttr("enable_clean_inefficient_cmdkey").value_or(0) == 0) {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_game_conv_ltv,
                                    "ad_dsp_game_conv_ltv_kpo_base",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        const auto &roas_kpo_white_product_name_map = RankKconfUtil::roasKpoWhiteProductNameType();
        bool is_kpo_roi_type_one = false;
        bool is_kpo_roi_type_two = false;
        bool is_kpo_roi_type_three = false;
        if (roas_kpo_white_product_name_map != nullptr &&
          roas_kpo_white_product_name_map->find(p_ad->get_product_name()) !=
          roas_kpo_white_product_name_map->end()) {
          int32_t ad_roi_kpo_type = roas_kpo_white_product_name_map->find(p_ad->get_product_name())->second;
          if (ad_roi_kpo_type == 1) {
            is_kpo_roi_type_one = true;
          } else if (ad_roi_kpo_type == 2) {
            is_kpo_roi_type_two = true;
          } else {
            is_kpo_roi_type_three = true;
          }
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
          (!(p_ctx->enable_roi_purchase_cold &&  ((p_ad->get_new_creative_tag() & 4) == 4))) &&
          (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS)) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE  &&
            p_ad->get_deep_conversion_type()
                == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION  &&
             p_ad->get_deep_conversion_type()
                == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY)) &&
          ((is_kpo_roi_type_one && !p_ctx->kpo_roi_gray) ||
            (is_kpo_roi_type_two && !p_ctx->kpo_roi_test) ||
            (is_kpo_roi_type_three && !p_ctx->kpo_roi_launch))) {
          return true;
        }
        return false;
  });
  }

  if (cmd_curator_ctx_->session_data_->common_r_
        ->GetIntCommonAttr("enable_clean_inefficient_cmdkey").value_or(0) == 0) {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_game_conv_ltv,
                                    "ad_dsp_game_conv_ltv_kpo",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        const auto &roas_kpo_white_product_name_map = RankKconfUtil::roasKpoWhiteProductNameType();
        bool is_kpo_roi_type_one = false;
        bool is_kpo_roi_type_two = false;
        bool is_kpo_roi_type_three = false;
        if (roas_kpo_white_product_name_map != nullptr &&
          roas_kpo_white_product_name_map->find(p_ad->get_product_name()) !=
          roas_kpo_white_product_name_map->end()) {
          int32_t ad_roi_kpo_type = roas_kpo_white_product_name_map->find(p_ad->get_product_name())->second;
          if (ad_roi_kpo_type == 1) {
            is_kpo_roi_type_one = true;
          } else if (ad_roi_kpo_type == 2) {
            is_kpo_roi_type_two = true;
          } else {
            is_kpo_roi_type_three = true;
          }
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
          (!(p_ctx->enable_roi_purchase_cold && ((p_ad->get_new_creative_tag() & 4) == 4))) &&
          (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS)) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
               p_ad->get_deep_conversion_type()
                  == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
               p_ad->get_deep_conversion_type()
                  == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY)) &&
              ((is_kpo_roi_type_one && p_ctx->kpo_roi_gray) ||
              (is_kpo_roi_type_two && p_ctx->kpo_roi_test) ||
              (is_kpo_roi_type_three && p_ctx->kpo_roi_launch))) {
          return true;
        }
        return false;
  });
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                  engine_base::PredictType::PredictType_key_action_ltv0,
                                  "dsp_rank_key_action_ltv0_new",
                                  CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        bool enable_dsp_rank_key_action_ltv0_new =
            SPDM_enable_dsp_rank_key_action_ltv0_new(p_ctx->session_data_->get_spdm_ctx());  // NOLINT
        bool enable_kminigame_key_action_ltv0 =
            SPDM_enable_kminigame_key_action_ltv0(p_ctx->session_data_->get_spdm_ctx());  // NOLINT
        bool enable_non_closed_loop_iaa_key_action_use_ltv =
            SPDM_enable_non_closed_loop_iaa_key_action_use_ltv(p_ctx->session_data_->get_spdm_ctx());  // NOLINT
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())
        && enable_dsp_rank_key_action_ltv0_new) {
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP
          ) {
            return true;
          }
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION) {
            bool dsp_ltv0_switch = false;
            for (const auto& action : p_ad->get_key_action_switch()) {
              if (action == 228) {
                dsp_ltv0_switch = true;
                break;
              }
            }
            if (dsp_ltv0_switch || enable_kminigame_key_action_ltv0 ||
              (p_ctx->key_action_account_map_->count(p_ad->get_account_id()) > 0)) {
                return true;
            }
          }
          if (enable_non_closed_loop_iaa_key_action_use_ltv) {
            if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
              if (p_ctx->key_action_use_ltv_product_set_ != nullptr &&
                  p_ctx->key_action_use_ltv_product_set_->count(p_ad->get_product_name()) > 0) {
                return true;
              }
            }
          }
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_industry_playlet_iaa_ltv,
                                    "ad_dsp_playlet_iaa_ltv",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->Is(AdFlag::is_duanju_ad)) {
      auto ocpx_action_type = p_ad->get_ocpx_action_type();
      if (ocpx_action_type == kuaishou::ad::AD_IAA_ROAS
        || ocpx_action_type == kuaishou::ad::AD_SERIAL_IAA_ROAS
        || ocpx_action_type == kuaishou::ad::AD_ROAS_IAAP) {
        return true;
      }
    }
    return false;
  });

  if (SPDM_enable_iaa_unify_duanju_multi_head_model_v2(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_mid_playlet_iaa_ltv,
                                    "ad_dsp_mid_playlet_iaa_ltv",
                                    CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_duanju_ad)) {
        auto ocpx_action_type = p_ad->get_ocpx_action_type();
        if (ocpx_action_type == kuaishou::ad::AD_IAA_ROAS
          || ocpx_action_type == kuaishou::ad::AD_SERIAL_IAA_ROAS) {
          return true;
        }
      }
      return false;
    });
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_playlet_click_pay_purchase,
                                    "ad_dsp_playlet_iaa_ltv_transform",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    // 付费短剧 IAA ltv 预估实时变现 到 全归因 的折算变换
    if (p_ctx->enable_ad_dsp_playlet_iaa_ltv_transform && p_ad->Is(AdFlag::is_duanju_iaa_ltv_ad)) {
      return true;
    }
    return false;
  });

  if (SPDM_enable_request_industry_game_iaa_ltv_ipu_multi_head(
    cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    // 快小 iaa 多头预估, ltv, ipu 0/1 prob, ipu cascade 值
    static const std::vector<engine_base::PredictType> multi_head_ltv_pts = {
      engine_base::PredictType::PredictType_industry_game_iaa_ltv,
      engine_base::PredictType::PredictType_game_iaa_ipu_binary_prob,
      engine_base::PredictType::PredictType_game_iaa_ipu_cascade
    };
    static const std::vector<engine_base::RType> multi_head_ltv_rts = {
      engine_base::RType::LTV,
      engine_base::RType::LTV,
      engine_base::RType::LTV
    };
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(multi_head_ltv_rts, multi_head_ltv_pts,
      "ad_dsp_game_iaa_multi_head_ltv_ipu", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (SPDM_enable_request_industry_game_iaa_ltv_ipu_multi_head(
          p_ctx->session_data_->get_spdm_ctx())
        && p_ad->Is(AdFlag::is_iaa_game_ltv)
        || (p_ctx->enable_iaap_request_iaa_ltv
        && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP)) {
          return true;  // 对 iaa & iaap iaa 部分生效
        }
        return false;
      });
  } else {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_industry_game_iaa_ltv,
                                    "ad_dsp_game_iaa_ltv",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_request_industry_game_iaa_ltv && p_ad->Is(AdFlag::is_iaa_game_ltv)
      || (p_ctx->enable_iaap_request_iaa_ltv && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP)) {
        return true;  // 只对 game IAA ltv 样本进行打分
    }
    return false;
  });
  }

  if (SPDM_enable_fiction_iaa_ltv_multi_head(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    // 小说 iaa 多头预估, ltv, ipu 0/1 prob, ipu cascade 值
    static const std::vector<engine_base::PredictType> multi_head_ltv_pts = {
      engine_base::PredictType::PredictType_industry_fiction_iaa_ltv,
      engine_base::PredictType::PredictType_fiction_iaa_ipu_binary_prob,
      engine_base::PredictType::PredictType_fiction_iaa_ipu_cascade
    };
    static const std::vector<engine_base::RType> multi_head_ltv_rts = {
      engine_base::RType::LTV,
      engine_base::RType::LTV,
      engine_base::RType::LTV
    };
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(multi_head_ltv_rts, multi_head_ltv_pts,
        "ad_dsp_fiction_iaa_multi_head_ltv_ipu", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (SPDM_enable_fiction_iaa_ltv_multi_head(p_ctx->session_data_->get_spdm_ctx())
          && p_ad->Is(AdFlag::is_fiction_iaa_ad)) {
          return true;
        }
        return false;
      });
  } else {
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                      engine_base::PredictType::PredictType_industry_fiction_iaa_ltv,
                                      "ad_dsp_fiction_iaa_ltv",
                                      CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      // 小说 iaa ltv 模型拆分
      if (p_ctx->enable_industry_fiction_iaa_ltv_ensemble && p_ad->Is(AdFlag::is_fiction_iaa_ad)) {
          return true;
      }
      return false;
    });
  }

  if (SPDM_enable_request_game_industry_unified_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> mini_game_iap_conv_ltv_pts = {
        engine_base::PredictType::PredictType_mini_game_iap_pay,
        engine_base::PredictType::PredictType_mini_game_iap_ltv};
    static const std::vector<engine_base::RType> mini_game_iap_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(mini_game_iap_conv_ltv_rts, mini_game_iap_conv_ltv_pts,
                                      "ad_dsp_game_r_ltv", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          // 快小开关: SPDM_enable_request_game_r_ltv
          // 微小开关: SPDM_enable_request_wx_minigame_ltv 首 R、首日付费
          // 大游开关: SPDM_enable_request_hardcore_game_ltv 首 R、付费、激活付费
          if ((SPDM_enable_request_kx_minigame_ltv(p_ctx->session_data_->get_spdm_ctx()) &&
               p_ad->Is(AdFlag::is_iaap_game_ad))  // 快小
              || (SPDM_enable_request_wx_minigame_ltv(p_ctx->session_data_->get_spdm_ctx()) &&
                  p_ad->Is(AdFlag::is_wechat_game_ad_v2))  // 微小
              || (SPDM_enable_request_big_game_ltv(p_ctx->session_data_->get_spdm_ctx()) &&
                  p_ad->Is(AdFlag::is_iap_game_ad_v3))) {
            return true;
          }
          return false;
        });
  } else if (SPDM_enable_request_game_r_ltv(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> mini_game_iap_conv_ltv_pts = {
        engine_base::PredictType::PredictType_mini_game_iap_ltv,
        engine_base::PredictType::PredictType_mini_game_iap_ltv_c_subsidy};
    static const std::vector<engine_base::RType> mini_game_iap_conv_ltv_rts = {engine_base::RType::LTV,
                                                                engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(mini_game_iap_conv_ltv_rts, mini_game_iap_conv_ltv_pts,
                                      "ad_dsp_game_r_ltv", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          // 大 R 开关: SPDM_enable_game_r_limit
          // 快小开关: SPDM_enable_request_game_r_ltv
          // 微小开关: SPDM_enable_request_wx_minigame_ltv
          // 大游开关: SPDM_enable_request_hardcore_game_ltv
          if (((SPDM_enable_request_game_r_ltv(p_ctx->session_data_->get_spdm_ctx()) &&
               p_ad->Is(AdFlag::is_iaap_game_ad))  // 快小
              || (SPDM_enable_request_wx_minigame_ltv(p_ctx->session_data_->get_spdm_ctx()) &&
                  p_ad->Is(AdFlag::is_wechat_game_ad))  // 微小
              || (SPDM_enable_request_hardcore_game_ltv(p_ctx->session_data_->get_spdm_ctx()) &&
                  p_ad->Is(AdFlag::is_iap_game_ad_v2)))
              && (!SPDM_enable_game_r_limit(p_ctx->session_data_->get_spdm_ctx()) ||
                  p_ctx->session_data_->get_rank_request()->ad_request().ad_user_info().game_big_r_status()
                  > 0)) {
              return true;
            }
          return false;
        });
  }


  // 行业模型 - 7r 模型 - 社交
  if (SPDM_enable_social_7r_request_game_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> mini_conv_ltv_pts = {
        engine_base::PredictType::PredictType_outer_game_conv_pay_7,  // 7r 行业模型 - 付费率
        engine_base::PredictType::PredictType_outer_game_conv_pay_cnt_7,  // 7r 行业模型 - 付费次数
        engine_base::PredictType::PredictType_outer_game_conv_ltv_7,  // 7r 行业模型 - ltv
    };
    static const std::vector<engine_base::RType> mini_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(mini_conv_ltv_rts, mini_conv_ltv_pts,
                                            "ad_dsp_social_7r_game_model", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          auto social_7r_white_conf = RankKconfUtil::social7RWhiteConf();
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS &&
              social_7r_white_conf && social_7r_white_conf->count(p_ad->get_product_name())) {
            return true;
          }
          return false;
        });
  }

  // 行业模型 - 7r 模型
  if (SPDM_enable_request_game_industry_7r_unified_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> mini_conv_ltv_pts = {
        engine_base::PredictType::PredictType_outer_game_conv_pay_7,  // 7r 行业模型 - 付费率
        engine_base::PredictType::PredictType_outer_game_conv_pay_cnt_7,  // 7r 行业模型 - 付费次数
        engine_base::PredictType::PredictType_outer_game_conv_ltv_7,  // 7r 行业模型 - ltv
    };
    static const std::vector<engine_base::RType> mini_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(mini_conv_ltv_rts, mini_conv_ltv_pts,
                                      "ad_dsp_mini_game_7r_industry_model", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          bool is_wechat_game = false;
          is_wechat_game =
              (((p_ad->get_landing_page_component() & 1) == 1 ||
                (p_ad->get_landing_page_component() & 2) == 2 ||
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
               p_ad->get_industry_parent_id_v3() == 1018);

          if (SPDM_enable_game_industry_7r_model(p_ctx->session_data_->get_spdm_ctx())) {
            if ((is_wechat_game) &&
                ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
                  p_ad->get_deep_conversion_type() ==
                      kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
                !(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
                !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
                !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) {
              return true;
            }
            if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
                p_ad->Is(AdFlag::is_iaap_game_ad)) {
              return true;
            }
          }
          // for 快小整体 和 微小 7R
          if (SPDM_enable_game_industry_7r_model_kx(p_ctx->session_data_->get_spdm_ctx())) {
            if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS &&
                 (p_ad->Is(AdFlag::is_wechat_game_ad) || p_ad->Is(AdFlag::is_iap_7r_game_ad))) ||
                (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP &&
                 p_ad->Is(AdFlag::is_iap_7r_game_ad)) ||
                (SPDM_enable_iaap_request_industry_7r_model(p_ctx->session_data_->get_spdm_ctx()) &&
                 p_ad->Is(AdFlag::is_iaap_game_ad))) {
              return true;
            }
          }
          // for 大游戏 sdk 回传 ad_seven_day_roas # update is_game_sdk
          if (SPDM_enable_big_game_sdk_model(p_ctx->session_data_->get_spdm_ctx())) {
            if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
                (SPDM_enable_big_game_pay_times_admit(p_ctx->session_data_->get_spdm_ctx()) &&
                 p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
                        p_ad->get_first_industry_id_v5() == 1018 &&
                p_ad->get_is_game_sdk()) {
              return true;
            }
          } else {
            if (SPDM_enable_big_game_request_industry_7r_model(p_ctx->session_data_->get_spdm_ctx())) {
              if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
                   (SPDM_enable_big_game_pay_times_admit(p_ctx->session_data_->get_spdm_ctx()) &&
                    p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
                  p_ad->get_first_industry_id_v5() == 1018 &&
                  (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP ||
                   (SPDM_enable_big_game_add_live_admit(p_ctx->session_data_->get_spdm_ctx()) &&
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE))) {
                if (SPDM_enable_big_game_add_sdk_admit(p_ctx->session_data_->get_spdm_ctx())) {
                  auto big_game_sdk_white_conf = RankKconfUtil::bigGameSdkWhiteConf();
                  if (big_game_sdk_white_conf && big_game_sdk_white_conf->count(p_ad->get_product_name())) {
                    return true;
                  }
                } else {
                  return true;
                }
              }
            }
          }
          if (SPDM_enable_big_game_request_industry_7r_model_new(p_ctx->session_data_->get_spdm_ctx()) &&
              p_ad->get_is_fill_7r_ad()) {
            return true;
          }
          return false;
        });
  } else if (SPDM_enable_game_industry_7r_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())
     || SPDM_enable_game_industry_7r_model_kx(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> mini_conv_ltv_pts = {
        engine_base::PredictType::PredictType_outer_game_conv_pay_7,  // 7r 行业模型 - 付费率
        engine_base::PredictType::PredictType_outer_game_conv_ltv_7,  // 7r 行业模型 - ltv
    };
    static const std::vector<engine_base::RType> mini_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(mini_conv_ltv_rts, mini_conv_ltv_pts,
                                      "ad_dsp_mini_game_7r_industry_model", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          bool is_wechat_game = false;
          is_wechat_game =
              (((p_ad->get_landing_page_component() & 1) == 1 ||
                (p_ad->get_landing_page_component() & 2) == 2 ||
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
               p_ad->get_industry_parent_id_v3() == 1018);

          if (SPDM_enable_game_industry_7r_model(p_ctx->session_data_->get_spdm_ctx())) {
            if ((is_wechat_game) &&
                ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
                  p_ad->get_deep_conversion_type() ==
                      kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
                !(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
                !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
                !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) {
              return true;
            }
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
              p_ad->Is(AdFlag::is_iaap_game_ad)) {
            return true;
          }
          }
          // for 快小整体 和 微小 7R
          if (SPDM_enable_game_industry_7r_model_kx(p_ctx->session_data_->get_spdm_ctx())) {
            if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS &&
                 (p_ad->Is(AdFlag::is_wechat_game_ad) || p_ad->Is(AdFlag::is_iap_7r_game_ad))) ||
                (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP &&
                 p_ad->Is(AdFlag::is_iap_7r_game_ad)) ||
                 (SPDM_enable_iaap_request_industry_7r_model(p_ctx->session_data_->get_spdm_ctx()) &&
                 p_ad->Is(AdFlag::is_iaap_game_ad))) {
              return true;
            }
          }
          // for 大游戏 sdk 回传 ad_seven_day_roas # update is_game_sdk
          if (SPDM_enable_big_game_sdk_model(p_ctx->session_data_->get_spdm_ctx())) {
            if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS &&
                p_ad->get_first_industry_id_v5() == 1018 && p_ad->get_is_game_sdk()) {
              return true;
            }
          } else {
            if (SPDM_enable_big_game_request_industry_7r_model(p_ctx->session_data_->get_spdm_ctx())) {
              if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS &&
                  p_ad->get_first_industry_id_v5() == 1018 &&
                  p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP) {
                if (SPDM_enable_big_game_add_sdk_admit(p_ctx->session_data_->get_spdm_ctx())) {
                  auto big_game_sdk_white_conf = RankKconfUtil::bigGameSdkWhiteConf();
                  if (big_game_sdk_white_conf && big_game_sdk_white_conf->count(p_ad->get_product_name())) {
                    return true;
                  }
                } else {
                  return true;
                }
              }
            }
          }
          if (SPDM_enable_big_game_request_industry_7r_model_new(p_ctx->session_data_->get_spdm_ctx()) &&
                  p_ad->get_is_fill_7r_ad()) {
            return true;
          }
          return false;
        });
  }

  if (SPDM_enable_mini_game_subsidy_industry_model_v3(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> mini_conv_ltv_pts = {
        engine_base::PredictType::PredictType_outer_discount_ratio,  // 行业模型 - 折扣率
        engine_base::PredictType::PredictType_outer_uplift_conv_pay_base,  // 行业模型 - 付费率
        engine_base::PredictType::PredictType_outer_uplift_ltv_base,       // 行业模型 - 真实付费头 ltv
        engine_base::PredictType::PredictType_outer_uplift_ltv_uplift0     // 行业模型 - 补贴头 ltv
    };
    static const std::vector<engine_base::RType> mini_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(mini_conv_ltv_rts, mini_conv_ltv_pts,
                                      "ad_dsp_mini_game_subsidy_industry_model", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          bool is_wechat_game = false;
          is_wechat_game =
              (((p_ad->get_landing_page_component() & 1) == 1 ||
                (p_ad->get_landing_page_component() & 2) == 2 ||
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
               p_ad->get_industry_parent_id_v3() == 1018);

          if (!(SPDM_enable_subsidy_model_skip_wechat_game(p_ctx->session_data_->get_spdm_ctx()) &&
                p_ad->Is(AdFlag::is_wechat_game_ad)) &&
              (is_wechat_game) &&
              ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
                p_ad->get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
              !(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
              !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
              !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) {
            return true;
          }
          return false;
        });
  } else if (SPDM_enable_mini_game_subsidy_industry_model_v2(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {  //NOLINT
    static const std::vector<engine_base::PredictType> mini_conv_ltv_pts = {
        engine_base::PredictType::PredictType_outer_uplift_conv_pay_base,  // 行业模型 - 付费率
        engine_base::PredictType::PredictType_outer_uplift_ltv_base,       // 行业模型 - 真实付费头 ltv
        engine_base::PredictType::PredictType_outer_uplift_ltv_uplift0,    // 行业模型 - 补贴头 ltv
    };
    static const std::vector<engine_base::RType> mini_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(mini_conv_ltv_rts, mini_conv_ltv_pts,
                                      "ad_dsp_mini_game_subsidy_industry_model", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          bool is_wechat_game = false;
          is_wechat_game =
              (((p_ad->get_landing_page_component() & 1) == 1 ||
                (p_ad->get_landing_page_component() & 2) == 2 ||
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
               p_ad->get_industry_parent_id_v3() == 1018);

          if (!(SPDM_enable_subsidy_model_skip_wechat_game(p_ctx->session_data_->get_spdm_ctx()) &&
                p_ad->Is(AdFlag::is_wechat_game_ad)) &&
              (is_wechat_game) &&
              ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
                p_ad->get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
              !(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
              !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
              !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) {
            return true;
          }
          return false;
        });
  } else if (SPDM_enable_mini_game_subsidy_industry_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> mini_conv_ltv_pts = {
        engine_base::PredictType::PredictType_outer_uplift_conv_pay_base,     // 行业模型 - 无补贴头付费率
        engine_base::PredictType::PredictType_outer_uplift_conv_pay_uplift0,  // 行业模型 - 有补贴头付费率
        engine_base::PredictType::PredictType_outer_uplift_ltv_base,          // 行业模型 - 无补贴头实付 ltv
        engine_base::PredictType::PredictType_outer_uplift_ltv_uplift0,       // 行业模型 - 有补贴头实付 ltv
    };
    static const std::vector<engine_base::RType> mini_conv_ltv_rts = {
      engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(mini_conv_ltv_rts, mini_conv_ltv_pts,
                                      "ad_dsp_mini_game_subsidy_industry_model", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          bool is_wechat_game = false;
          is_wechat_game =
              (((p_ad->get_landing_page_component() & 1) == 1 ||
                (p_ad->get_landing_page_component() & 2) == 2 ||
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
               p_ad->get_industry_parent_id_v3() == 1018);

          if (!(SPDM_enable_subsidy_model_skip_wechat_game(p_ctx->session_data_->get_spdm_ctx()) &&
                p_ad->Is(AdFlag::is_wechat_game_ad)) &&
              (is_wechat_game) &&
              ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
                p_ad->get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
              !(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
              !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
              !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) {
            return true;
          }
          return false;
        });
  }

  if (SPDM_enable_c_subsidy_game_model_v4(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> mini_conv_ltv_pts = {
        engine_base::PredictType::PredictType_game_conv_pay,            // 无补贴新字段 付费率
        engine_base::PredictType::PredictType_game_conv_pay_c_subsidy,  // 补贴新字段 付费率
        engine_base::PredictType::PredictType_game_conv_ltv,            // 复用 base ltv 字段
        engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy,  // 复用 base 补贴预估
    };
    static const std::vector<engine_base::RType> mini_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(mini_conv_ltv_rts, mini_conv_ltv_pts,
                                      "ad_dsp_mini_game_conv_ltv_multi_head_csubsidy_v4", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          bool is_wechat_game = false;
          is_wechat_game =
              (((p_ad->get_landing_page_component() & 1) == 1 ||
                (p_ad->get_landing_page_component() & 2) == 2 ||
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
               p_ad->get_industry_parent_id_v3() == 1018);

          if (!(SPDM_enable_subsidy_model_skip_wechat_game(p_ctx->session_data_->get_spdm_ctx()) &&
                p_ad->Is(AdFlag::is_wechat_game_ad)) &&
              (is_wechat_game) &&
              ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
                p_ad->get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
              !(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
              !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
              !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) {
            return true;
          }
          return false;
        });
  } else {
      if (SPDM_enable_c_subsidy_game_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())
          || SPDM_enable_c_subsidy_game_model_v2(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
      static const std::vector<engine_base::PredictType> mini_conv_ltv_pts = {
        engine_base::PredictType::PredictType_game_conv_ltv,   // 复用 base ltv 字段
        engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy
      };
      static const std::vector<engine_base::RType> mini_conv_ltv_rts = {
        engine_base::RType::LTV,
        engine_base::RType::LTV
      };
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(mini_conv_ltv_rts, mini_conv_ltv_pts,
                              "ad_dsp_mini_game_conv_ltv_multi_head_csubsidy_v2", CMD_SOURCE_AD_DSP),
                          // "ad_dsp_mini_game_multi_conv_ltv", CMD_SOURCE_AD_DSP),
                          // "ad_dsp_mini_game_conv_ltv_multi_head_csubsidy", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        bool is_wechat_game = false;
            is_wechat_game = (((p_ad->get_landing_page_component() & 1) == 1 ||
                              (p_ad->get_landing_page_component() & 2) == 2 ||
                              (p_ad->get_campaign_type() ==
                              kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
                              p_ad->get_industry_parent_id_v3() == 1018);

            if (!(SPDM_enable_subsidy_model_skip_wechat_game(p_ctx->session_data_->get_spdm_ctx())
            && p_ad->Is(AdFlag::is_wechat_game_ad)) && (is_wechat_game) && (
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
                p_ad->get_deep_conversion_type()
                    == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
                    p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
              !(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
              !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
              !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) {
                  return true;
              }
            return false;
      });
      } else {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                        engine_base::PredictType::PredictType_game_conv_ltv,
                        "ad_dsp_mini_game_conv_ltv",
                        CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        bool is_wechat_game = false;
          is_wechat_game = (((p_ad->get_landing_page_component() & 1) == 1 ||
                            (p_ad->get_landing_page_component() & 2) == 2 ||
                            (p_ad->get_campaign_type() ==
                            kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
                            p_ad->get_industry_parent_id_v3() == 1018);

          if ((is_wechat_game) && (
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
              p_ad->get_deep_conversion_type()
                  == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
            !(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
            !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
            !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) {
                return true;
            }
          return false;
    });
    }
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                       engine_base::PredictType::PredictType_game_conv_ltv,
                       "ad_dsp_mini_game_conv_ltv",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      bool is_wechat_game = false;
        is_wechat_game = (((p_ad->get_landing_page_component() & 1) == 1 ||
                          (p_ad->get_landing_page_component() & 2) == 2 ||
                          (p_ad->get_campaign_type() ==
                          kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
                          p_ad->get_industry_parent_id_v3() == 1018);

        if ((!(SPDM_enable_game_conv_model_skip_kuaixiao_game(p_ctx->session_data_->get_spdm_ctx())
          && p_ad->Is(AdFlag::is_iaap_game_ad))) && ((is_wechat_game) &&
           ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
             p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) &&
           !(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
           !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
           !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0))) {
            return true;
          }
        return false;
  });
  if (SPDM_enable_fiction_iap_pay_ltv_model_decide_price(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> fiction_conv_ltv_pts = {
        engine_base::PredictType::PredictType_fiction_iap_surprise_conv_pay_ltv,
        engine_base::PredictType::PredictType_fiction_iap_surprise_pay_amount,
        engine_base::PredictType::PredictType_fiction_iap_appear_conv_pay_ltv,
        engine_base::PredictType::PredictType_fiction_iap_appear_pay_amount,
        engine_base::PredictType::PredictType_fiction_iap_no_subsidy_conv_pay_ltv,
        engine_base::PredictType::PredictType_fiction_iap_no_subsidy_pay_amount};
    static const std::vector<engine_base::RType> fiction_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV,
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(fiction_conv_ltv_rts, fiction_conv_ltv_pts,
                                      "ad_dsp_fiction_iap_two_style_head_pay_ltv", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          if (p_ad->Is(AdFlag::is_fiction_iap_ad)) {
            return true;
          }
          return false;
        });
  } else if (SPDM_enable_fiction_iap_pay_ltv_multi_style_head(
                 cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> fiction_conv_ltv_pts = {
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_k,           // uplift_k
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_pay_rate_0,  // 无补贴激活付费率
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_pay_amount_0,  // pay_ltv0
        engine_base::PredictType::PredictType_fiction_iap_multi_retain_uplift_k,      // 挽留 uplift_k
        engine_base::PredictType::
            PredictType_fiction_iap_multi_uplift_retain_pay_rate_0,  // 挽留无补贴激活付费率
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_retain_pay_amount_0};  // 挽留 pay_ltv0
    static const std::vector<engine_base::RType> fiction_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV,
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(fiction_conv_ltv_rts, fiction_conv_ltv_pts,
                                      "ad_dsp_fiction_multi_style_discount_uplift_ltv", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          if (p_ad->Is(AdFlag::is_fiction_iap_ad)) {
            return true;
          }
          return false;
        });
  } else if (SPDM_enable_fiction_iap_price_multi_head_v3(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> fiction_conv_ltv_pts = {
      engine_base::PredictType::PredictType_fiction_iap_multi_uplift_k,           // 立出 uplift_k
      engine_base::PredictType::PredictType_fiction_iap_multi_retain_uplift_k,    // 挽留 uplift_k
      engine_base::PredictType::PredictType_fiction_iap_multi_uplift_0,           // 立出 uplift_0
      engine_base::PredictType::PredictType_fiction_iap_multi_retain_uplift_0,    // 挽留 uplift_0
      engine_base::PredictType::PredictType_fiction_iap_multi_uplift_pay_rate_0,  // 立出 无补贴付费率
      engine_base::PredictType::PredictType_fiction_iap_multi_uplift_retain_pay_rate_0,  // 挽留 无补贴付费率
  };
    static const std::vector<engine_base::RType> fiction_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV,
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(fiction_conv_ltv_rts, fiction_conv_ltv_pts,
                                      "ad_dsp_fiction_multi_price_sigmoid_uplift_k_ltv", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          if (p_ad->Is(AdFlag::is_fiction_iap_ad)) {
            return true;
          }
          return false;
        });
  } else if (SPDM_enable_fiction_iap_price_multi_head_v2(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> fiction_conv_ltv_pts = {
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_k,           // 立出 uplift_k
        engine_base::PredictType::PredictType_fiction_iap_multi_retain_uplift_k,    // 挽留 uplift_k
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_pay_rate_0,  // 立出 无补贴付费率
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_retain_pay_rate_0,  // 挽留
    };
    static const std::vector<engine_base::RType> fiction_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(fiction_conv_ltv_rts, fiction_conv_ltv_pts,
                                      "ad_dsp_fiction_multi_price_uplift_k_ltv", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          if (p_ad->Is(AdFlag::is_fiction_iap_ad)) {
            return true;
          }
          return false;
        });
  } else if (SPDM_enable_fiction_iap_pay_ltv_multi_head(cmd_curator_ctx_->session_data_->get_spdm_ctx()) &&
             (SPDM_useNovelSubsidy(cmd_curator_ctx_->session_data_->get_spdm_ctx()) ||
              SPDM_enable_offline_fiction_subsidy_uplift_model(
                  cmd_curator_ctx_->session_data_->get_spdm_ctx()))) {
    static const std::vector<engine_base::PredictType> fiction_conv_ltv_pts = {
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_k,           // uplift_k
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_pay_rate_0,  // 无补贴激活付费率
        engine_base::PredictType::PredictType_fiction_iap_multi_uplift_pay_amount_0,  // pay_ltv
    };
    static const std::vector<engine_base::RType> fiction_conv_ltv_rts = {
        engine_base::RType::LTV, engine_base::RType::LTV, engine_base::RType::LTV};
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(fiction_conv_ltv_rts, fiction_conv_ltv_pts,
                                      "ad_dsp_fiction_subsidy_uplift_k_ltv", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          if (p_ad->Is(AdFlag::is_fiction_iap_ad)) {
            return true;
          }
          return false;
        });
  } else {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR,
                                    engine_base::PredictType::PredictType_fiction_pay_ltv,
                                    "ad_dsp_fiction_pay_ltv",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_fiction_pay_ltv_model &&
        p_ad->Is(AdFlag::is_fiction_na_ad) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS) {
      return true;
    }
    return false;
  });
  }

  if (SPDM_enable_fiction_industry_uplift_model(cmd_curator_ctx_->session_data_->get_spdm_ctx()) &&
      !SPDM_enable_offline_base_fiction_iap_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> na_conv_ltv_subsidy_pts = {
        engine_base::PredictType::PredictType_fiction_conv_ltv,
        engine_base::PredictType::PredictType_fiction_conv_ltv_c_subsidy};
    static const std::vector<engine_base::RType> na_conv_ltv_subsidy_rts = {
      engine_base::RType::LTV,
      engine_base::RType::LTV
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(na_conv_ltv_subsidy_rts, na_conv_ltv_subsidy_pts,
                        "ad_dsp_fiction_conv_ltv_multi_head_csubsidy", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_fiction_na_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS) {
        return true;
      }
      return false;
    });
  }

  // 付费小说 C 补模型 cmd
  if (SPDM_enable_c_subsidy_novel_model_v2(cmd_curator_ctx_->session_data_->get_spdm_ctx()) &&
      !SPDM_enable_offline_base_fiction_iap_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> na_conv_ltv_pts = {
      engine_base::PredictType::PredictType_game_conv_ltv,
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy
    };
    static const std::vector<engine_base::RType> na_conv_ltv_rts = {
      engine_base::RType::LTV,
      engine_base::RType::LTV
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(na_conv_ltv_rts, na_conv_ltv_pts,
                        "ad_dsp_conv_ltv_multi_head_csubsidy_novel", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_fiction_na_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS) {
        return true;
      }
      return false;
    });
  }
  if (SPDM_enable_c_subsidy_duanju_multi_head_model(cmd_curator_ctx_->session_data_->get_spdm_ctx()) ||
    SPDM_enable_c_subsidy_duanju_multi_head_model_hy_rank(cmd_curator_ctx_->session_data_->get_spdm_ctx()) ||
    SPDM_enable_c_subsidy_duanju_multi_head_model_mid_rank(cmd_curator_ctx_->session_data_->get_spdm_ctx())
    || SPDM_enable_c_no_subsidy_duanju_multi_head_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> conv_ltv_pts = {
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy0,
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy1,
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy2,
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy3,
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy4,
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy5,
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy6
    };
    static const std::vector<engine_base::RType> conv_ltv_rts = {
      engine_base::RType::LTV,
      engine_base::RType::LTV,
      engine_base::RType::LTV,
      engine_base::RType::LTV,
      engine_base::RType::LTV,
      engine_base::RType::LTV,
      engine_base::RType::LTV
    };
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(conv_ltv_rts, conv_ltv_pts,
                      "ad_dsp_game_conv_ltv_multi_head_csubsidy", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
      return true;
    }
    return false;
    });
  }

  if (SPDM_enable_c_subsidy_duanju_model_backup(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> conv_ltv_pts = {
      engine_base::PredictType::PredictType_game_conv_ltv,
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy
    };
    static const std::vector<engine_base::RType> conv_ltv_rts = {
      engine_base::RType::LTV,
      engine_base::RType::LTV
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(conv_ltv_rts, conv_ltv_pts,
                        "ad_dsp_playlet_conv_ltv_multi_head_csubsidy", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      bool is_wechat_game = false;
      is_wechat_game = (((p_ad->get_landing_page_component() & 1) == 1 ||
                      (p_ad->get_landing_page_component() & 2) == 2 ||
                      (p_ad->get_campaign_type() ==
                      kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
                      p_ad->get_industry_parent_id_v3() == 1018);
      bool is_novel = false;
      is_novel = SPDM_enable_c_subsidy_novel_model_v2(p_ctx->session_data_->get_spdm_ctx()) &&
              p_ad->Is(AdFlag::is_fiction_na_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS;
      const auto &roas_kpo_white_product_name_map = RankKconfUtil::roasKpoWhiteProductNameType();
      bool is_kpo_roi_type_one = false;
      bool is_kpo_roi_type_two = false;
      bool is_kpo_roi_type_three = false;
      if (roas_kpo_white_product_name_map != nullptr &&
        roas_kpo_white_product_name_map->find(p_ad->get_product_name()) !=
        roas_kpo_white_product_name_map->end()) {
      int32_t ad_roi_kpo_type = roas_kpo_white_product_name_map->find(p_ad->get_product_name())->second;
        if (ad_roi_kpo_type == 1) {
          is_kpo_roi_type_one = true;
        } else if (ad_roi_kpo_type == 2) {
          is_kpo_roi_type_two = true;
        } else {
          is_kpo_roi_type_three = true;
        }
      }
      if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
        (!(is_wechat_game) && !(is_novel) &&
        (!(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
        !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
        !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) ||
        (p_ad->get_second_industry_id_v5() == 2012 && (p_ctx->enable_playlet_conversion_predict ||
        p_ctx->playlet_model_conv_tail_->count(p_ad->get_account_id() % 100) > 0))) &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
          !is_kpo_roi_type_one &&
          !is_kpo_roi_type_two &&
          !is_kpo_roi_type_three) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) ||
          (p_ad->get_ocpx_action_type()
              == kuaishou::ad::AD_PURCHASE  &&  p_ad->get_industry_parent_id_v3() == 1018) ||
          (p_ad->get_deep_conversion_type()
              == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type()
              == kuaishou::ad::AD_CONVERSION &&
          !is_kpo_roi_type_one &&
          !is_kpo_roi_type_two &&
          !is_kpo_roi_type_three) ||
          (p_ad->get_deep_conversion_type()
              == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          !is_kpo_roi_type_one &&
          !is_kpo_roi_type_two &&
          !is_kpo_roi_type_three) ||
          (p_ad->get_deep_conversion_type()
              == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type()
              == kuaishou::ad::AD_PURCHASE_CONVERSION) ||
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) &&
          p_ad->get_first_industry_id_v5() == 1018)) && (p_ad->Is(AdFlag::is_paid_duanju_ad_v3))) {
        return true;
      }
      if (p_ctx->ad_roas_drop_admit_ != nullptr && p_ctx->enable_ecpc_conv_predict_more_sta) {
        if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
          != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
          p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
          != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
          return true;
        }
      }
      return false;
    });
  }
  if (SPDM_enable_c_subsidy_duanju_model(cmd_curator_ctx_->session_data_->get_spdm_ctx()) ||
    SPDM_enable_c_subsidy_playlet_mid_model(cmd_curator_ctx_->session_data_->get_spdm_ctx()) ||
    SPDM_enable_c_subsidy_novel_model(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    static const std::vector<engine_base::PredictType> conv_ltv_pts = {
      engine_base::PredictType::PredictType_game_conv_ltv,
      engine_base::PredictType::PredictType_game_conv_ltv_c_subsidy
    };
    static const std::vector<engine_base::RType> conv_ltv_rts = {
      engine_base::RType::LTV,
      engine_base::RType::LTV
    };
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(conv_ltv_rts, conv_ltv_pts,
                        "ad_dsp_game_conv_ltv_multi_head_csubsidy", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      bool is_wechat_game = false;
      is_wechat_game = (((p_ad->get_landing_page_component() & 1) == 1 ||
                      (p_ad->get_landing_page_component() & 2) == 2 ||
                      (p_ad->get_campaign_type() ==
                      kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
                      p_ad->get_industry_parent_id_v3() == 1018);
      bool is_novel = false;
      is_novel = SPDM_enable_c_subsidy_novel_model_v2(p_ctx->session_data_->get_spdm_ctx()) &&
              p_ad->Is(AdFlag::is_fiction_na_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS;
      const auto &roas_kpo_white_product_name_map = RankKconfUtil::roasKpoWhiteProductNameType();
      bool is_kpo_roi_type_one = false;
      bool is_kpo_roi_type_two = false;
      bool is_kpo_roi_type_three = false;
      if (roas_kpo_white_product_name_map != nullptr &&
        roas_kpo_white_product_name_map->find(p_ad->get_product_name()) !=
        roas_kpo_white_product_name_map->end()) {
      int32_t ad_roi_kpo_type = roas_kpo_white_product_name_map->find(p_ad->get_product_name())->second;
        if (ad_roi_kpo_type == 1) {
          is_kpo_roi_type_one = true;
        } else if (ad_roi_kpo_type == 2) {
          is_kpo_roi_type_two = true;
        } else {
          is_kpo_roi_type_three = true;
        }
      }
      // exclusive with playlet cmdkey backup
      if (p_ad->Is(AdFlag::is_paid_duanju_ad_v3)
        && SPDM_enable_c_subsidy_duanju_model_backup(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
        (!(is_wechat_game) && !(is_novel) &&
        (!(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
        !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
        !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) ||
        (p_ad->get_second_industry_id_v5() == 2012 && (p_ctx->enable_playlet_conversion_predict ||
        p_ctx->playlet_model_conv_tail_->count(p_ad->get_account_id() % 100) > 0))) &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
          !is_kpo_roi_type_one &&
          !is_kpo_roi_type_two &&
          !is_kpo_roi_type_three) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) ||
          (p_ad->get_ocpx_action_type()
              == kuaishou::ad::AD_PURCHASE  &&  p_ad->get_industry_parent_id_v3() == 1018) ||
          (p_ad->get_deep_conversion_type()
              == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type()
              == kuaishou::ad::AD_CONVERSION &&
          !is_kpo_roi_type_one &&
          !is_kpo_roi_type_two &&
          !is_kpo_roi_type_three) ||
          (p_ad->get_deep_conversion_type()
              == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          !is_kpo_roi_type_one &&
          !is_kpo_roi_type_two &&
          !is_kpo_roi_type_three) ||
          (p_ad->get_deep_conversion_type()
              == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type()
              == kuaishou::ad::AD_PURCHASE_CONVERSION) ||
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) &&
          p_ad->get_first_industry_id_v5() == 1018))) {
        return true;
      }
      if (p_ctx->ad_roas_drop_admit_ != nullptr && p_ctx->enable_ecpc_conv_predict_more_sta) {
        if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
          != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
          p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
          != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
          return true;
        }
      }
      return false;
    });
  } else {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                      engine_base::PredictType::PredictType_game_conv_ltv,
                      "ad_dsp_game_conv_ltv",
                      CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        bool is_wechat_game = false;
        is_wechat_game = (((p_ad->get_landing_page_component() & 1) == 1 ||
                        (p_ad->get_landing_page_component() & 2) == 2 ||
                        (p_ad->get_campaign_type() ==
                        kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)) &&
                        p_ad->get_industry_parent_id_v3() == 1018);
        bool is_novel = false;
        is_novel = SPDM_enable_c_subsidy_novel_model_v2(p_ctx->session_data_->get_spdm_ctx()) &&
              p_ad->Is(AdFlag::is_fiction_na_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS;
        const auto &roas_kpo_white_product_name_map = RankKconfUtil::roasKpoWhiteProductNameType();
        bool is_kpo_roi_type_one = false;
        bool is_kpo_roi_type_two = false;
        bool is_kpo_roi_type_three = false;
        if (roas_kpo_white_product_name_map != nullptr &&
          roas_kpo_white_product_name_map->find(p_ad->get_product_name()) !=
          roas_kpo_white_product_name_map->end()) {
        int32_t ad_roi_kpo_type = roas_kpo_white_product_name_map->find(p_ad->get_product_name())->second;
          if (ad_roi_kpo_type == 1) {
            is_kpo_roi_type_one = true;
          } else if (ad_roi_kpo_type == 2) {
            is_kpo_roi_type_two = true;
          } else {
            is_kpo_roi_type_three = true;
          }
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
          (!(is_wechat_game) && !(is_novel) &&
          (!(p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) &&
          !(p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) &&
          !(p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0)) ||
          (p_ad->get_second_industry_id_v5() == 2012 && (p_ctx->enable_playlet_conversion_predict ||
          p_ctx->playlet_model_conv_tail_->count(p_ad->get_account_id() % 100) > 0))) &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
            !is_kpo_roi_type_one &&
            !is_kpo_roi_type_two &&
            !is_kpo_roi_type_three) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) ||
            (p_ad->get_ocpx_action_type()
                == kuaishou::ad::AD_PURCHASE  &&  p_ad->get_industry_parent_id_v3() == 1018) ||
            (p_ad->get_deep_conversion_type()
                == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_ocpx_action_type()
                == kuaishou::ad::AD_CONVERSION &&
            !is_kpo_roi_type_one &&
            !is_kpo_roi_type_two &&
            !is_kpo_roi_type_three) ||
            (p_ad->get_deep_conversion_type()
                == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            !is_kpo_roi_type_one &&
            !is_kpo_roi_type_two &&
            !is_kpo_roi_type_three) ||
            (p_ad->get_deep_conversion_type()
                == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_ocpx_action_type()
                == kuaishou::ad::AD_PURCHASE_CONVERSION) ||
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) &&
            p_ad->get_first_industry_id_v5() == 1018))) {
          return true;
        }
        if (p_ctx->ad_roas_drop_admit_ != nullptr && p_ctx->enable_ecpc_conv_predict_more_sta) {
          if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
            != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
            p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
            != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
            return true;
          }
        }
        return false;
    });
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                       engine_base::PredictType::PredictType_7_day_pay_times,
                       "ad_dsp_7_day_multi_pay_times",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES &&
          !(p_ctx->seven_days_pay_times_product_map_->count(p_ad->get_product_name()) > 0)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                       engine_base::PredictType::PredictType_purchase_7d_pay_times,
                       "ad_dsp_7_day_purchase_pay_times",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          (p_ctx->seven_days_purchase_pay_times_product_set_->count(p_ad->get_product_name()) > 0)) {
        return true;
      }
      if (p_ctx->IsMerchantPlatformPaytimesAd(p_ad)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                      engine_base::PredictType::PredictType_1_day_pay_times,
                      "ad_dsp_1_day_pay_times",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                      engine_base::PredictType::PredictType_2_7_day_pay_times,
                      "ad_dsp_2_7_day_pay_times",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
      return true;
    }
    if (p_ad->get_admit_2_7d_pay_times()) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                      engine_base::PredictType::PredictType_1_day_pay_amount,
                      "ad_dsp_1_day_pay_amount",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                      engine_base::PredictType::PredictType_2_7_day_pay_amount,
                      "ad_dsp_2_7_day_pay_amount",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
        p_ad->Is(AdFlag::is_iaap_game_ad)) {
      return true;
    }
    if (p_ad->Is(AdFlag::is_game_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
      p_ctx->enable_long_ratio_ad_roas_) {
      return true;
    }
    if (p_ctx->enable_long_ratio_roas_twin_game_ad_ && p_ad->Is(AdFlag::is_roas_twin_game_ad)) {
      return true;
    }
    if (p_ad->get_admit_2_7d_pay_ltv()) return true;
    return false;
  });
  return;
}  // NOLINT

void OuterCmdRegister::RegisterAucCmd(CuratorTypeV2 *p_cmd_curator) const {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE,
                       engine_base::PredictType::PredictType_unified_ctr_auc_exp,
                       "ad_dsp_outer_ctr_cross_auc_exp",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!SPDM_enable_outer_unify_ctr_cross_auc(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    // 表单
    if (p_ctx->enable_c2_lps_ownctr_ &&
            !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
            !p_ad->Is(AdFlag::is_direct_ecom) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
            (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
          return true;
        }
        // 快聘作品表单，请求统一表单模型
        if (p_ctx->enable_c2_lps_ownctr_ &&
            !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
            !p_ad->Is(AdFlag::is_direct_ecom) &&
            (p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
            p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
          return true;
        }
        // 线索统一 ctr: 引入私信留资
        if (SPDM_enable_leads_unify_cvr(p_ctx->session_data_->get_spdm_ctx())) {
           if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
               p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
               !p_ad->Is(AdFlag::is_direct_ecom) &&
               (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
               p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
             return true;
           }
           if (p_ad->Is(AdFlag::is_self_service_ad) &&
               !p_ad->Is(AdFlag::is_live_ad) &&
               (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
               p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
             return true;
           }
        }
    // 激活
    if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          (p_ctx->IsOcpc2Unit(p_ad) ||
              (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
              (p_ad->Is(AdFlag::is_deep_unified) &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE)))) &&
          !p_ad->Is(AdFlag::is_comp_ecom_ad) &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist() != nullptr &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist() != nullptr &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
           (p_ctx->fiction_app_conversion_purchase_product_map_ != nullptr &&
           p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0) ||
           (p_ctx->fiction_app_conversion_purchase_account_map_ != nullptr &&
           p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0) ||
           (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
           (SPDM_enable_game_request_imp_conv(p_ctx->session_data_->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) {
        return true;
      }
      if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) && //NOLINT
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        (!p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_comp_ecom_ad)) &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
        (p_ad->Is(AdFlag::is_deep_unified) &&
        p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE))) {
        return true;
      }
      if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  //NOLINT
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist() &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist() &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
           (p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
             && p_ctx->enable_ecom_conv_ensemble_) &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
           !p_ad->Is(AdFlag::is_dpa)) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
        return true;
      }
    // 点击
        if ((p_ctx->IsOcpc2Unit(p_ad) || (p_ctx->session_data_->get_is_thanos_request() &&
                                          p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC)) &&
            !p_ctx->IsAdxLargeCreatives(p_ad)) {
          if (p_ad->Is(AdFlag::is_dpa)) {
            return false;
          }

          if (p_ad->Is(AdFlag::is_direct_ecom) ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
            return false;
          }

          if (p_ctx->IsOcpc2Unit(p_ad) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
              (p_ctx->aliOuterDeliveryAccountList != nullptr &&
                p_ctx->aliOuterDeliveryAccountList->count(p_ad->get_account_id()) > 0
                || p_ad->get_campaign_sub_type() ==
                kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE)) {
            return true;
          }

          if (RankKconfUtil::storyAdPurchaseWhitelist() != nullptr &&
              RankKconfUtil::storyAdPurchaseWhitelist()->find(p_ad->get_product_name()) !=
              RankKconfUtil::storyAdPurchaseWhitelist()->end()) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_single_bid_purchase_v2) ||
              p_ad->get_deep_conversion_type() ==
              kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT ||
              (!p_ctx->enable_c2_lps_ownctr_ && p_ctx->enable_c2_lps_ &&
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) ||
              p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ADD_WECHAT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_REGISTER) {
            return true;
          }
          if (p_ctx->session_data_->get_is_thanos_request() &&
              p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC) {
            return true;
           }
        }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE,
                       engine_base::PredictType::PredictType_unified_ctr_auc_base,
                       "ad_dsp_outer_ctr_cross_auc_base",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!SPDM_enable_outer_unify_ctr_cross_auc(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    // 表单
    if (p_ctx->enable_c2_lps_ownctr_ &&
            !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
            !p_ad->Is(AdFlag::is_direct_ecom) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_GET_THROUGH ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
            (!SPDM_enable_wechat_connected_imp_lps(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_MEASUREMENT_HOUSE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION)) {
          return true;
        }
        // 快聘作品表单，请求统一表单模型
        if (p_ctx->enable_c2_lps_ownctr_ &&
            !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
            !p_ad->Is(AdFlag::is_direct_ecom) &&
            (p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
            p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
          return true;
        }
        // 线索统一 ctr: 引入私信留资
        if (SPDM_enable_leads_unify_cvr(p_ctx->session_data_->get_spdm_ctx())) {
           if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
               p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
               !p_ad->Is(AdFlag::is_direct_ecom) &&
               (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
               p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
             return true;
           }
           if (p_ad->Is(AdFlag::is_self_service_ad) &&
               !p_ad->Is(AdFlag::is_live_ad) &&
               (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
               p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
             return true;
           }
        }
    // 激活
    if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          (p_ctx->IsOcpc2Unit(p_ad) ||
              (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
              (p_ad->Is(AdFlag::is_deep_unified) &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE)))) &&
          !p_ad->Is(AdFlag::is_comp_ecom_ad) &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist() != nullptr &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist() != nullptr &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
           (p_ctx->fiction_app_conversion_purchase_product_map_ != nullptr &&
           p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0) ||
           (p_ctx->fiction_app_conversion_purchase_account_map_ != nullptr &&
           p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0) ||
           (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
           (SPDM_enable_game_request_imp_conv(p_ctx->session_data_->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) {
        return true;
      }
      if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        (!p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_comp_ecom_ad)) &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
        (p_ad->Is(AdFlag::is_deep_unified) &&
        p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE))) {
        return true;
      }
      if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
            RankKconfUtil::pddHelpProductWhitelist() &&
            RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddHelpProductWhitelist()->end()) &&
          !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
            RankKconfUtil::pddNativeProductWhitelist() &&
            RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::pddNativeProductWhitelist()->end()) &&
           (p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
             && p_ctx->enable_ecom_conv_ensemble_) &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
           !p_ad->Is(AdFlag::is_dpa)) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
        return true;
      }
    // 点击
        if ((p_ctx->IsOcpc2Unit(p_ad) || (p_ctx->session_data_->get_is_thanos_request() &&
                                          p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC)) &&
            !p_ctx->IsAdxLargeCreatives(p_ad)) {
          if (p_ad->Is(AdFlag::is_dpa)) {
            return false;
          }

          if (p_ad->Is(AdFlag::is_direct_ecom) ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
            return false;
          }

          if (p_ctx->IsOcpc2Unit(p_ad) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
              (p_ctx->aliOuterDeliveryAccountList->count(p_ad->get_account_id()) > 0
                || p_ad->get_campaign_sub_type() ==
                kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE)) {
            return true;
          }

          if (RankKconfUtil::storyAdPurchaseWhitelist() != nullptr &&
              RankKconfUtil::storyAdPurchaseWhitelist()->find(p_ad->get_product_name()) !=
              RankKconfUtil::storyAdPurchaseWhitelist()->end()) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_single_bid_purchase_v2) ||
              p_ad->get_deep_conversion_type() ==
              kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT ||
              (!p_ctx->enable_c2_lps_ownctr_ && p_ctx->enable_c2_lps_ &&
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) ||
              p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ADD_WECHAT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_REGISTER) {
            return true;
          }
          if (p_ctx->session_data_->get_is_thanos_request() &&
              p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC) {
            return true;
           }
        }
    return false;
  });

  // 激活多头旁路 auc
  p_cmd_curator->RegisterCmd(
       new engine_base::CmdWrapperV2({engine_base::RType::AUC_EXP, engine_base::RType::AUC_EXP},
                     {engine_base::PredictType::PredictType_outer_u_cvr_cross_auc,
                     engine_base::PredictType::PredictType_outer_u_noctcvr_cross_auc},
                     "ad_dsp_outer_conv_cross_auc_new", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!SPDM_enable_outerconv_cross_auc(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
    }
    if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        (p_ctx->IsOcpc2Unit(p_ad) ||
            (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
            (p_ad->Is(AdFlag::is_deep_unified) &&
            p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE)))) &&
        !p_ad->Is(AdFlag::is_comp_ecom_ad) &&
        !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
          RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddHelpProductWhitelist()->end()) &&
        !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
          RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddNativeProductWhitelist()->end()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
          p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
          p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0 ||
          (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
          (SPDM_enable_game_request_imp_conv(p_ctx->session_data_->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) {
      return true;
    }
    if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
      !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
      (!p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_comp_ecom_ad)) &&
      ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
      (p_ad->Is(AdFlag::is_deep_unified) &&
      p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_SITE_PAGE))) {
      return true;
    }
    if ((SPDM_enable_outer_u_cvr_old_request_close(p_ctx->session_data_->get_spdm_ctx())) &&  // NOLINT
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
          RankKconfUtil::pddHelpProductWhitelist() &&
          RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddHelpProductWhitelist()->end()) &&
        !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
          RankKconfUtil::pddNativeProductWhitelist() &&
          RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddNativeProductWhitelist()->end()) &&
          (p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)
            && p_ctx->enable_ecom_conv_ensemble_) &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
          !p_ad->Is(AdFlag::is_dpa)) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP,
                     engine_base::PredictType::PredictType_auc_model_exp,
                     "ad_dsp_item_imp_conv_auc_exp",
                     CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_10b_1009_auc &&
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        p_ctx->IsOcpc2Unit(p_ad) &&
        !p_ad->Is(AdFlag::is_comp_ecom_ad) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
         (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
         p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->get_product_name()) > 0 ||
         p_ctx->fiction_app_conversion_purchase_account_map_->count(p_ad->get_account_id()) > 0 ||
         (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY)) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "ad_dsp_item_imp_lps_auc_cross",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_cross_auc_lps &&
        !(p_ctx->session_data_->get_is_rewarded() && p_ctx->enable_rewarded_cmd_lps) &&
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_lps_tx) &&
        !p_ad->Is(AdFlag::is_direct_ecom) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED &&
        p_ad->get_industry_id_v3() != 1111 &&
        p_ctx->car_lps_whitelis_product_map_ != nullptr &&
        p_ctx->car_lps_whitelis_product_map_->count(p_ad->get_product_name()) == 0) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP,
                       engine_base::PredictType::PredictType_auc_model_exp,
                       "ad_dsp_ecom_item_imp_conv_auc_exp",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_ecom_conv_cross_auc &&
        !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
        !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_SOCIAL &&
          RankKconfUtil::pddHelpProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddHelpProductWhitelist()->end()) &&
        !(p_ctx->enable_pdd_native_cmd && p_ad->get_is_dsp_outer_loop_native() &&
          RankKconfUtil::pddNativeProductWhitelist()->find(p_ad->get_product_name()) !=
          RankKconfUtil::pddNativeProductWhitelist()->end()) &&
         ((p_ad->Is(AdFlag::is_comp_ecom_ad) && !p_ad->Is(AdFlag::is_sdpa_ecom_ad)) ||
          p_ad->Is(AdFlag::is_sdpa_ecom_ad)) &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
         !p_ad->Is(AdFlag::is_dpa)) ||
         (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP))) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterMultiPredictCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP,
                       engine_base::PredictType::PredictType_auc_model_exp,
                        "click_app_invoked_multi_auc_exp",
                        CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_invoke_cross_auc &&
          !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !p_ad->Is(AdFlag::is_dpa) &&
          ((p_ctx->IsOcpc2Unit(p_ad) &&
            p_ctx->ocpm_cut_by_item_imp_app_invoked &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
          (!p_ctx->IsOcpc2Unit(p_ad) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) ||
            (p_ad->get_second_industry_id_v5() == 2012 && p_ctx->enable_playlet_inovked_predict) ||
            (p_ctx->mini_app_roas_invoked_account_set_ != nullptr &&
            p_ctx->mini_app_roas_invoked_account_set_->count(p_ad->get_account_id())) ||
          (p_ctx->mini_app_roas_invoked_product_set_ != nullptr &&
          p_ctx->mini_app_roas_invoked_product_set_->count(p_ad->get_product_name()) > 0))) {
        return true;
      }
      return false;
  });
  return;
}

void OuterCmdRegister::RegisterOtherCmd(CuratorTypeV2 *p_cmd_curator) const {
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                       engine_base::PredictType::PredictType_up_model_conv_retention_rate,
                       "ad_dsp_conv_retention_up_model",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!SPDM_enable_mcda_qps_save(p_ctx->session_data_->get_spdm_ctx())
            && (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            p_ad->Is(AdFlag::is_deep_conv_retention)) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                      engine_base::PredictType::PredictType_conv_nextstay,
                      "ad_dsp_server_conv_rention",
                      CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ad->Is(AdFlag::is_deep_conv_retention) ||
            p_ad->Is(AdFlag::is_roi_low_purchase_or_retention_ecpc_ad) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY)) {
          return true;
        }
        if (p_ctx->ad_roas_drop_admit_ != nullptr && p_ctx->enable_ecpc_conv_predict_more_sta) {
          if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
              != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
              p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
              != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
             return true;
          }
        }
        if (RankKconfUtil::WeekRetentionEcpcAccountList()->find(p_ad->get_account_id()) !=
                RankKconfUtil::WeekRetentionEcpcAccountList()->end()  &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
            p_ad->get_deep_conversion_type() ==
                kuaishou::ad::AdCallbackLog_EventType_EVENT_WEEK_STAY) {
          return true;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS) {
          return true;
        }
        if (p_ctx->enable_conv_nextstay_ecpc &&
            (RankKconfUtil::adConvNextstayEcpcWhiteProduct()->find(p_ad->get_product_name()) !=
            RankKconfUtil::adConvNextstayEcpcWhiteProduct()->end() ||
            RankKconfUtil::adConvNextstayEcpcWhiteAccount()->find(absl::StrCat(p_ad->get_account_id())) !=
            RankKconfUtil::adConvNextstayEcpcWhiteAccount()->end())) {
          return true;
        }
        return false;
  });


  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    engine_base::PredictType::PredictType_invo_traffic_score,
                                    "ad_dsp_invo_traffic_score",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_invo_traffic_predict) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
                      ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                      {engine_base::RType::UNCATEGORIZED},
                      {engine_base::PredictType::PredictType_invo_traffic_cali_top_layer_score},
      {engine_base::PredictEmbeddingType::PredictEmbeddingType_invo_traffic_cali_top_layer_emb},
                      "ad_dsp_invo_traffic_cali_top_layer_emb",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_invo_traffic_cali_top_layer_emb_predict) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
                      ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                      {engine_base::RType::UNCATEGORIZED},
                      {engine_base::PredictType::PredictType_invo_traffic_cali_top_layer_score},
      {engine_base::PredictEmbeddingType::PredictEmbeddingType_invo_traffic_cali_top_layer_emb},
                      "ad_dsp_invo_traffic_cali_ad_conversion",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_invo_traffic_cali_ad_conversion &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
                      ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                      {engine_base::RType::UNCATEGORIZED},
                      {engine_base::PredictType::PredictType_invo_traffic_cali_top_layer_score},
      {engine_base::PredictEmbeddingType::PredictEmbeddingType_invo_traffic_cali_top_layer_emb},
                      "ad_dsp_invo_traffic_cali_event_order_paied",
                      CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->enable_invo_traffic_cali_event_order_paied &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                       engine_base::PredictType::PredictType_conv_nextstay,
                       "ad_dsp_server_key_action_retention",
                       CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION)) {
          bool dsp_reten_switch = false;
          for (const auto& action : p_ad->get_key_action_switch()) {
            if (action == 7) {
              dsp_reten_switch = true;
              break;
            }
          }
          if (dsp_reten_switch ||
            (p_ctx->key_action_retention_account_map_->count(p_ad->get_account_id()) > 0)) {
              return true;
          }
        }
        return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                       engine_base::PredictType::PredictType_click_novel_score,
                       "novel_lps_click2_purchase_single_bid",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) {
        return false;
      }
      const auto& novelEcpcWhiteList = RankKconfUtil::novelEcpcWhiteList();
      if (novelEcpcWhiteList->find(p_ad->get_product_name()) != novelEcpcWhiteList->end() &&
          p_ad->Is(AdFlag::is_single_bid_purchase_v2)) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LANDING_PAGE ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_TAOBAO) {
          return true;
        }
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                       engine_base::PredictType::PredictType_conv_quality_score,
                       "rank_conv_quality_score",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_conv_quality_ecpc &&
          p_ad->Is(AdFlag::is_conv_quality_product_account_white_list_ad)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                       engine_base::PredictType::PredictType_appinvoke_nextstay,
                       "ad_dsp_server_app_invoke_nextstay",
                       CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED &&
          p_ad->Is(AdFlag::is_deep_conv_retention)) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    engine_base::PredictType::PredictType_cid_mcda_score,
                                    "ad_dsp_server_cid_mcda_boost_model",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->session_data_->get_is_thanos_request() &&
            p_ctx->IsCidMcdaAd(p_ad)) {
          return true;
        }
        return false;
      });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                       engine_base::PredictType::PredictType_click2_purchase_mt,
                       "rank_click2_jinjian_mt",
                       CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    bool is_mt_jinjian_white_list = false;
    {
      auto mt_product_account_exp = RankKconfUtil::mtProductAccountExp();
      auto& mt_product_account_jinjian_map = mt_product_account_exp->data().mt_jinjian();
      auto tag_mt_jinjian = mt_product_account_jinjian_map.find(1);
      if (tag_mt_jinjian != mt_product_account_jinjian_map.end()) {
          auto& product_mt_jinjian_map = tag_mt_jinjian->second.product_name();
          auto& account_mt_jinjian_map = tag_mt_jinjian->second.account_id();
          const auto product_mt_jinjian_iter =
          product_mt_jinjian_map.find(p_ad->get_product_name());
          const auto account_mt_jinjian_iter =
          account_mt_jinjian_map.find(p_ad->get_account_id());
        if ((product_mt_jinjian_iter != product_mt_jinjian_map.end() ||
            account_mt_jinjian_iter != account_mt_jinjian_map.end())) {
          is_mt_jinjian_white_list = true;
        } else {
          is_mt_jinjian_white_list = false;
        }
      } else {
        is_mt_jinjian_white_list = false;
      }
    }
    {
      auto mt_product_account_launch = RankKconfUtil::mtProductAccountLaunch();
      auto& mt_product_account_jinjian_map = mt_product_account_launch->data().mt_jinjian();
      auto tag_mt_jinjian = mt_product_account_jinjian_map.find(1);
      if (tag_mt_jinjian != mt_product_account_jinjian_map.end()) {
        auto& product_mt_jinjian_map = tag_mt_jinjian->second.product_name();
        auto& account_mt_jinjian_map = tag_mt_jinjian->second.account_id();
        const auto product_mt_jinjian_iter = product_mt_jinjian_map.find(p_ad->get_product_name());
        const auto account_mt_jinjian_iter = account_mt_jinjian_map.find(p_ad->get_account_id());
        if ((product_mt_jinjian_iter != product_mt_jinjian_map.end() ||
            account_mt_jinjian_iter != account_mt_jinjian_map.end())) {
          is_mt_jinjian_white_list = true;
        }
      }
    }
    if (p_ad->Is(AdFlag::is_mcda_extra_jinjian_product_white_list_ad)) {
      is_mt_jinjian_white_list = true;
    }
    if ((p_ctx->enable_mt_shouxin_launch || p_ctx->enable_mt_cov_exp_tmp) &&
        (is_mt_jinjian_white_list &&
        p_ctx->IsOcpc2Unit(p_ad) &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT)) {
      return true;
    }
    return false;
  });

  if (SPDM_enable_rnd_cold_start_cmd_split(cmd_curator_ctx_->session_data_->get_spdm_ctx()) &&
      !SPDM_enable_rnd_cold_start_cmd_split_new_photo(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                      engine_base::PredictType::PredictType_rnd_explore_score,
                      "ad_dsp_rnd_cold_start_outer",
                      CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->session_data_->get_is_search_request()) {
        return true;
      }
      return false;
    });
  } else {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                      engine_base::PredictType::PredictType_rnd_explore_score,
                      "ad_dsp_imp_conv_diversity_rnd_explore_outer",
                      CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->session_data_->get_is_search_request()) {
        return true;
      }
      return false;
    });
    if (SPDM_enable_rnd_cold_start_cmd_split_new_photo(
      cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                        engine_base::PredictType::PredictType_rnd_explore_score,
                        "ad_dsp_rnd_cold_start_outer",
                        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!p_ctx->session_data_->get_is_search_request() && p_ad->Is(AdFlag::is_new_model_creative)) {
          return true;
        }
        return false;
      });
    }
  }
  // 游戏首充党预测模型
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                     engine_base::PredictType::PredictType_game_palyer_first_charge_rate,
                     "dsp_roi_ykb_shouchong_model",
                     CMD_SOURCE_AD_DSP),
  [] (const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->enable_request_game_player_only_first_charge_ecpc_ &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) &&
        p_ad->get_first_industry_id_v5() == 1018) {
      return true;
    }
    return false;
  });

  static const std::vector<PredictType> user_experience_playtime_predicts = {
      PredictType::PredictType_user_experience_score1,
      PredictType::PredictType_user_experience_score2,
      PredictType::PredictType_user_experience_score3,
      PredictType::PredictType_user_experience_score4,
      PredictType::PredictType_user_experience_score5,
      PredictType::PredictType_user_experience_score6,
      PredictType::PredictType_user_experience_score7,
      PredictType::PredictType_user_experience_score8};
  static const std::vector<engine_base::RType> predict_types(user_experience_playtime_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(predict_types, user_experience_playtime_predicts,
                                        "outer_ad_user_experience_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->session_data_->get_is_search_request() &&
              !p_ctx->session_data_->get_is_rewarded()) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                     PredictType::PredictType_negative_ratio,
                     "outer_ad_negative_ratio_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_close_ad_ntr_model(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
      }
      if (SPDM_enable_close_ad_ntr_model_thanos_mix(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
      }
      if (!p_ctx->session_data_->get_is_search_request()
          && !p_ctx->session_data_->get_is_rewarded()
          && SPDM_enable_ntr_ratio(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                     PredictType::PredictType_jump_out_rate,
                     "outer_ad_jump_out_rate_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->session_data_->get_is_search_request()
          && !p_ctx->session_data_->get_is_rewarded()
          && SPDM_enable_jump_out_rate_model_outer(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      return false;
    });

  static const std::vector<engine_base::PredictType> incentive_quit_rate_pts = {
      PredictType::PredictType_incentive_quit_rate_0,
      PredictType::PredictType_incentive_quit_rate_1,
      PredictType::PredictType_incentive_quit_rate_2,
      PredictType::PredictType_incentive_quit_rate_3,
      PredictType::PredictType_incentive_quit_rate_4,
      PredictType::PredictType_incentive_quit_rate_5,
      PredictType::PredictType_incentive_quit_rate_6,
      PredictType::PredictType_incentive_quit_rate_7,
      PredictType::PredictType_incentive_quit_rate_8,
      PredictType::PredictType_incentive_quit_rate_9,
      PredictType::PredictType_incentive_quit_rate_10,
      PredictType::PredictType_incentive_quit_rate_11,
      PredictType::PredictType_incentive_quit_rate_12,
      PredictType::PredictType_incentive_quit_rate_13,
      PredictType::PredictType_incentive_quit_rate_14};
  static const std::vector<engine_base::RType> incentive_quit_rate_rts(
      incentive_quit_rate_pts.size(), engine_base::RType::UNCATEGORIZED);
  auto incentive_quit_rate_cmd_condition = [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->session_data_->get_is_incentive_explore()) {
      return false;
    }
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (p_ctx->session_data_->get_is_incentive() &&
        (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
          admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
      return true;
    }
    return false;
  };
  auto incentive_quit_rate_rct_cmd_condition = [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    // incentive_quit_rate_cmd 中的条件取反
    if (!p_ctx->session_data_->get_is_incentive_explore()) {
      return false;
    }
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (p_ctx->session_data_->get_is_incentive() &&
        (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
          admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
      return true;
    }
    return false;
  };
  bool enable_incentive_adx_quit_rate_cmd =
      SPDM_enable_incentive_adx_quit_rate_cmd(cmd_curator_ctx_->session_data_->get_spdm_ctx());
  if (cmd_curator_ctx_->session_data_->get_is_incentive_explore()) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                    "incentive_quit_rate_rct_adx", CMD_SOURCE_AD_DSP),
      [&](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ad->get_ad_source_type() == kuaishou::ad::ADX && enable_incentive_adx_quit_rate_cmd) &&
          incentive_quit_rate_rct_cmd_condition(p_ad, p_ctx);
      });
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                    "incentive_quit_rate_rct_outer", CMD_SOURCE_AD_DSP),
      [&](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return !(p_ad->get_ad_source_type() == kuaishou::ad::ADX && enable_incentive_adx_quit_rate_cmd) &&
          incentive_quit_rate_rct_cmd_condition(p_ad, p_ctx);
      });
  } else {
    auto incentive_costly_sub_page_ids = RankKconfUtil::incentiveCostlySubPageIds();
    if (incentive_costly_sub_page_ids->count(cmd_curator_ctx_->session_data_->get_sub_page_id()) == 0 &&
        SPDM_enable_incentive_iaa_quit_rate_cmd(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                      "incentive_quit_rate_adx_iaa", CMD_SOURCE_AD_DSP),
        [&](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          return (p_ad->get_ad_source_type() == kuaishou::ad::ADX && enable_incentive_adx_quit_rate_cmd) &&
            incentive_quit_rate_cmd_condition(p_ad, p_ctx);
        });
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                      "incentive_quit_rate_outer_iaa", CMD_SOURCE_AD_DSP),
        [&](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          return !(p_ad->get_ad_source_type() == kuaishou::ad::ADX && enable_incentive_adx_quit_rate_cmd) &&
            incentive_quit_rate_cmd_condition(p_ad, p_ctx);
        });
    } else {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                      "incentive_quit_rate_adx", CMD_SOURCE_AD_DSP),
        [&](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
            return (p_ad->get_ad_source_type() == kuaishou::ad::ADX && enable_incentive_adx_quit_rate_cmd) &&
              incentive_quit_rate_cmd_condition(p_ad, p_ctx);
        });
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                      "incentive_quit_rate_outer", CMD_SOURCE_AD_DSP),
        [&](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
            return !(p_ad->get_ad_source_type() == kuaishou::ad::ADX && enable_incentive_adx_quit_rate_cmd) &&
              incentive_quit_rate_cmd_condition(p_ad, p_ctx);
        });
    }
  }

  static const std::vector<engine_base::PredictType> incentive_deep_quit_rate_pts = {
      PredictType::PredictType_incentive_deep_quit_rate_0,
      PredictType::PredictType_incentive_deep_quit_rate_1,
      PredictType::PredictType_incentive_deep_quit_rate_2,
      PredictType::PredictType_incentive_deep_quit_rate_3,
      PredictType::PredictType_incentive_deep_quit_rate_4,
      PredictType::PredictType_incentive_deep_quit_rate_5};
  static const std::vector<engine_base::RType> incentive_deep_quit_rate_rts(
      incentive_deep_quit_rate_pts.size(), engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(incentive_deep_quit_rate_rts, incentive_deep_quit_rate_pts,
                                    "incentive_deep_quit_rate_outer", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        auto admit_ocpxs = RankKconfUtil::incentiveDeepCoinOcpxs();
        auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
        auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
        if (p_ctx->session_data_->get_is_incentive() &&
            (SPDM_enable_incentive_deep_quit_rate_pred(p_ctx->session_data_->get_spdm_ctx()) ||
            SPDM_enable_incentive_coin_joint_decision(p_ctx->session_data_->get_spdm_ctx())) &&
            (admit_ocpxs && admit_ocpxs->count(p_ad->get_ocpx_action_type())) &&
            (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
             admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
          return true;
        }
        return false;
      });

  // 深度激励唤端优化目标 uplift cvr
  static const std::vector<engine_base::PredictType> incentive_invoked_uplift_cvr_pts = {
      PredictType::PredictType_incentive_invoked_uplift_cvr_0,
      PredictType::PredictType_incentive_invoked_uplift_cvr_1,
      PredictType::PredictType_incentive_invoked_uplift_cvr_2,
      PredictType::PredictType_incentive_invoked_uplift_cvr_3};
  static const std::vector<engine_base::RType> incentive_invoked_uplift_cvr_rts(
    incentive_invoked_uplift_cvr_pts.size(), engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(incentive_invoked_uplift_cvr_rts, incentive_invoked_uplift_cvr_pts,
                     "incentive_invoked_uplift_cvr", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_deep_incentive_model_add_sub_page_admit(p_ctx->session_data_->get_spdm_ctx())) {
        const auto& invoked_admit_sub_page = RankKconfUtil::invokedAdmitSubPageIdSet();  // 主场景
        const auto& incentive_dish_sub_page_id = RankKconfUtil::incentiveDishSubPageId();  // 饭补
        const auto& short_play_pos_ids = RankKconfUtil::shortPlayDeepIncentivePosIds();  // 短剧 IAA
        bool main_scene_admit = invoked_admit_sub_page &&
          invoked_admit_sub_page->count(p_ctx->session_data_->get_sub_page_id());
        bool dish_admit = SPDM_enable_deep_incentive_add_dish(p_ctx->session_data_->get_spdm_ctx()) &&
          incentive_dish_sub_page_id &&
          incentive_dish_sub_page_id->count(p_ctx->session_data_->get_sub_page_id());
        bool short_play_admit =
          SPDM_enable_invoked_d_i_iaa_use_uplift_model(p_ctx->session_data_->get_spdm_ctx()) &&
          short_play_pos_ids &&
          short_play_pos_ids->count(p_ctx->session_data_->get_pos_manager_base().GetMediumPosId());
        if (!(main_scene_admit || dish_admit || short_play_admit)) {
          return false;
        }
      }
      if (p_ctx->session_data_->get_is_rewarded() &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
        return true;
      }
      return false;
    });

  auto incntv_ltv_outer_cmd_condition = [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (SPDM_enable_ucc_ltv_model_subpage_admit(p_ctx->session_data_->get_spdm_ctx())) {
      // 是否激励
      if (!p_ctx->session_data_->get_is_incentive() ||
          !SPDM_enable_incntv_ltv_cmd(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      // ltv 单独的 subpage 准入
      auto admit_sub_page_ids_ltv = RankKconfUtil::incentiveLtvAdmitSubPageIds();
      if (!admit_sub_page_ids_ltv ||
          admit_sub_page_ids_ltv->count(p_ctx->session_data_->get_sub_page_id()) == 0) {
        return false;
      }
      return true;
    }
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (p_ctx->session_data_->get_is_incentive() &&
        SPDM_enable_incntv_ltv_cmd(p_ctx->session_data_->get_spdm_ctx()) &&
        (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
         admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
      return true;
    }
    return false;
  };
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    engine_base::PredictType::PredictType_incntv_ltv, "incntv_ltv_outer_cmd",
                                    CMD_SOURCE_AD_DSP),
      [&incntv_ltv_outer_cmd_condition](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        return incntv_ltv_outer_cmd_condition(p_ad, p_ctx);
      });

  // 深度激励激活优化目标 uplift cvr
  static const std::vector<engine_base::PredictType> incentive_conv_uplift_cvr_pts = {
      PredictType::PredictType_incentive_deep_task_uplift_cvr_0,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_1,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_2,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_3};
  static const std::vector<engine_base::RType> incentive_conv_uplift_cvr_rts(
    incentive_conv_uplift_cvr_pts.size(), engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(incentive_conv_uplift_cvr_rts, incentive_conv_uplift_cvr_pts,
                     "incentive_conv_uplift_cvr", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_deep_incentive_model_add_sub_page_admit(p_ctx->session_data_->get_spdm_ctx())) {
        if (p_ctx->session_data_->get_is_ios_platform()) {
          return false;
        }
        const auto& conv_admit_sub_page = RankKconfUtil::convAdmitSubPageIdSet();  // 主场景
        const auto& incentive_dish_sub_page_id = RankKconfUtil::incentiveDishSubPageId();  // 饭补
        const auto& short_play_pos_ids = RankKconfUtil::shortPlayDeepIncentivePosIds();  // 短剧 IAA
        bool main_scene_admit = conv_admit_sub_page &&
          conv_admit_sub_page->count(p_ctx->session_data_->get_sub_page_id());
        bool dish_admit = SPDM_enable_deep_incentive_add_dish(p_ctx->session_data_->get_spdm_ctx()) &&
          incentive_dish_sub_page_id &&
          incentive_dish_sub_page_id->count(p_ctx->session_data_->get_sub_page_id());
        bool short_play_admit =
          SPDM_enable_invoked_d_i_iaa_use_uplift_model(p_ctx->session_data_->get_spdm_ctx()) &&
          short_play_pos_ids &&
          short_play_pos_ids->count(p_ctx->session_data_->get_pos_manager_base().GetMediumPosId());
        if (!(main_scene_admit || dish_admit || short_play_admit)) {
          return false;
        }
      }
      if (p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_incentive_conv_uplift_model(p_ctx->session_data_->get_spdm_ctx()) ||
          SPDM_enable_incentive_coin_joint_decision(p_ctx->session_data_->get_spdm_ctx())) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
        return true;
      }
      // 激活激励 uplift 决策接入收集销售线索营销目标
      if (p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_conv_d_i_uplift_model_add_site_page_cmd_admit(p_ctx->session_data_->get_spdm_ctx()) ||
          SPDM_enable_incentive_coin_joint_decision(p_ctx->session_data_->get_spdm_ctx())) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
        return true;
      }
      return false;
    });


  // 深度激励外循环优化目标领奖率
  static const std::vector<engine_base::PredictType> incentive_deep_task_rewarded_ratio_pts = {
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_3};
  static const std::vector<engine_base::RType> incentive_deep_task_rewarded_ratio_rts(
    incentive_deep_task_rewarded_ratio_pts.size(), engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(incentive_deep_task_rewarded_ratio_rts, incentive_deep_task_rewarded_ratio_pts,  // NOLINT
                     "incentive_deep_task_rewarded_ratio", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_deep_incentive_model_add_sub_page_admit(p_ctx->session_data_->get_spdm_ctx())) {
        const auto& invoked_admit_sub_page = RankKconfUtil::invokedAdmitSubPageIdSet();  // 主场景
        const auto& incentive_dish_sub_page_id = RankKconfUtil::incentiveDishSubPageId();  // 饭补
        const auto& short_play_pos_ids = RankKconfUtil::shortPlayDeepIncentivePosIds();  // 短剧 IAA
        bool main_scene_admit = invoked_admit_sub_page &&
          invoked_admit_sub_page->count(p_ctx->session_data_->get_sub_page_id());
        bool dish_admit = SPDM_enable_deep_incentive_add_dish(p_ctx->session_data_->get_spdm_ctx()) &&
          incentive_dish_sub_page_id &&
          incentive_dish_sub_page_id->count(p_ctx->session_data_->get_sub_page_id());
        bool short_play_admit =
          SPDM_enable_invoked_d_i_iaa_use_uplift_model(p_ctx->session_data_->get_spdm_ctx()) &&
          short_play_pos_ids &&
          short_play_pos_ids->count(p_ctx->session_data_->get_pos_manager_base().GetMediumPosId());
        if (!(main_scene_admit || dish_admit || short_play_admit)) {
          return false;
        }
      }
      // 激活
      if (p_ctx->session_data_->get_is_rewarded() &&
          !SPDM_enable_conv_rewarded_ratio_model(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
        return true;
      }
      // 拉活
      if (p_ctx->session_data_->get_is_rewarded() &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
        return true;
      }
      return false;
    });

  // 激活激励领奖率，从上面逻辑中拆分，使用不一样的 cmdkey
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(incentive_deep_task_rewarded_ratio_rts, incentive_deep_task_rewarded_ratio_pts,  // NOLINT
                     "incentive_conv_rewarded_ratio", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_deep_incentive_model_add_sub_page_admit(p_ctx->session_data_->get_spdm_ctx())) {
        if (p_ctx->session_data_->get_is_ios_platform()) {
          return false;
        }
        const auto& conv_admit_sub_page = RankKconfUtil::convAdmitSubPageIdSet();  // 主场景
        const auto& incentive_dish_sub_page_id = RankKconfUtil::incentiveDishSubPageId();  // 饭补
        const auto& short_play_pos_ids = RankKconfUtil::shortPlayDeepIncentivePosIds();  // 短剧 IAA
        bool main_scene_admit = conv_admit_sub_page &&
          conv_admit_sub_page->count(p_ctx->session_data_->get_sub_page_id());
        bool dish_admit = SPDM_enable_deep_incentive_add_dish(p_ctx->session_data_->get_spdm_ctx()) &&
          incentive_dish_sub_page_id &&
          incentive_dish_sub_page_id->count(p_ctx->session_data_->get_sub_page_id());
        bool short_play_admit =
          SPDM_enable_invoked_d_i_iaa_use_uplift_model(p_ctx->session_data_->get_spdm_ctx()) &&
          short_play_pos_ids &&
          short_play_pos_ids->count(p_ctx->session_data_->get_pos_manager_base().GetMediumPosId());
        if (!(main_scene_admit || dish_admit || short_play_admit)) {
          return false;
        }
      }
      if (p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_conv_rewarded_ratio_model(p_ctx->session_data_->get_spdm_ctx()) ||
          SPDM_enable_incentive_coin_joint_decision(p_ctx->session_data_->get_spdm_ctx())) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
        return true;
      }
      return false;
    });

  // 外循环深度激励 logits 模型
  static const std::vector<engine_base::PredictType> incentive_deep_task_logits_model_pts = {
      PredictType::PredictType_incentive_deep_task_uplift_logits_0,
      PredictType::PredictType_incentive_deep_task_uplift_logits_1,
      PredictType::PredictType_incentive_deep_task_uplift_logits_2,
      PredictType::PredictType_incentive_deep_task_uplift_logits_3,
      PredictType::PredictType_incentive_deep_task_uplift_logits_4,
      PredictType::PredictType_incentive_deep_task_uplift_logits_5};
  static const std::vector<engine_base::RType> incentive_deep_task_logits_model_rts(
    incentive_deep_task_logits_model_pts.size(), engine_base::RType::UNCATEGORIZED);

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(incentive_deep_task_logits_model_rts, incentive_deep_task_logits_model_pts,  // NOLINT
                     "incentive_invoked_uplift_logits_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_deep_incentive_model_add_sub_page_admit(p_ctx->session_data_->get_spdm_ctx())) {
        const auto& invoked_admit_sub_page = RankKconfUtil::invokedAdmitSubPageIdSet();
        if (!(invoked_admit_sub_page &&
            invoked_admit_sub_page->count(p_ctx->session_data_->get_sub_page_id()))) {
          return false;
        }
      }
      if (p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_invoked_use_logits_model(p_ctx->session_data_->get_spdm_ctx()) ||
          SPDM_enable_invoked_d_i_base_use_logits_model(p_ctx->session_data_->get_spdm_ctx())) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(incentive_deep_task_logits_model_rts, incentive_deep_task_logits_model_pts,  // NOLINT
                     "incentive_conv_uplift_logits_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_deep_incentive_model_add_sub_page_admit(p_ctx->session_data_->get_spdm_ctx())) {
        if (p_ctx->session_data_->get_is_ios_platform()) {
          return false;
        }
        const auto& conv_admit_sub_page = RankKconfUtil::convAdmitSubPageIdSet();
        if (!(conv_admit_sub_page &&
            conv_admit_sub_page->count(p_ctx->session_data_->get_sub_page_id()))) {
          return false;
        }
      }
      if (p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_conv_use_logits_model(p_ctx->session_data_->get_spdm_ctx()) ||
          SPDM_enable_conv_d_i_base_use_logits_model(p_ctx->session_data_->get_spdm_ctx())) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
        return true;
      }
      if (p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_conv_site_page_use_logits_model(p_ctx->session_data_->get_spdm_ctx()) ||
          SPDM_enable_conv_d_i_base_use_logits_model(p_ctx->session_data_->get_spdm_ctx())) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
        return true;
      }
      return false;
    });

  // 外循环深度激励领奖率模型
  static const std::vector<engine_base::PredictType> incentive_deep_task_rewarded_ratio_new_pts = {
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_3,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_4,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_5};
  static const std::vector<engine_base::RType> incentive_deep_task_rewarded_ratio_new_rts(
    incentive_deep_task_rewarded_ratio_new_pts.size(), engine_base::RType::UNCATEGORIZED);

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(incentive_deep_task_rewarded_ratio_new_rts, incentive_deep_task_rewarded_ratio_new_pts,  // NOLINT
                     "incentive_invoked_reward_ratio_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_deep_incentive_model_add_sub_page_admit(p_ctx->session_data_->get_spdm_ctx())) {
        const auto& invoked_admit_sub_page = RankKconfUtil::invokedAdmitSubPageIdSet();
        if (!(invoked_admit_sub_page &&
            invoked_admit_sub_page->count(p_ctx->session_data_->get_sub_page_id()))) {
          return false;
        }
      }
      if (p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_invoked_use_logits_model(p_ctx->session_data_->get_spdm_ctx()) ||
          SPDM_enable_invoked_d_i_base_use_logits_model(p_ctx->session_data_->get_spdm_ctx())) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
        return true;
      }
      return false;
    });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(incentive_deep_task_rewarded_ratio_new_rts, incentive_deep_task_rewarded_ratio_new_pts,  // NOLINT
                     "incentive_conv_reward_ratio_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_deep_incentive_model_add_sub_page_admit(p_ctx->session_data_->get_spdm_ctx())) {
        if (p_ctx->session_data_->get_is_ios_platform()) {
          return false;
        }
        const auto& conv_admit_sub_page = RankKconfUtil::convAdmitSubPageIdSet();
        if (!(conv_admit_sub_page &&
            conv_admit_sub_page->count(p_ctx->session_data_->get_sub_page_id()))) {
          return false;
        }
      }
      if (p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_invoked_use_logits_model(p_ctx->session_data_->get_spdm_ctx()) ||
          SPDM_enable_conv_d_i_base_use_logits_model(p_ctx->session_data_->get_spdm_ctx())) &&
          (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
        return true;
      }
      return false;
    });


  // 作品涨粉
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                       engine_base::PredictType::PredictType_wtr,
                       "outer_photo_follow_cmd", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->session_data_->get_pos_manager_base().IsFollow()) {
      return false;
    }
    if ((p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
        p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW) {
      return true;
    }
    if (!p_ad->Is(AdFlag::is_live_ad) &&
        p_ad->Is(AdFlag::is_self_service_ad) &&
        p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW) {
      return true;
    }
    return false;
  });

  // 直播涨粉
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                     engine_base::PredictType::PredictType_wtr,
                     "outer_live_follow_cmd", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->session_data_->get_pos_manager_base().IsFollow()) {
      return false;
    }
    if (p_ad->Is(AdFlag::is_live_ad) &&
        p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW) {
      return true;
    }
    if (p_ad->Is(AdFlag::is_live_ad) &&
        p_ad->Is(AdFlag::is_self_service_ad) &&
        p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW) {
      return true;
    }
    return false;
  });

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    engine_base::PredictType::PredictType_inner_explore_sim,
                                    "ad_dsp_inner_explore_sim_cmd_outer",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        // 直播不请求
        if (p_ad->Is(AdFlag::is_live)) {
          return false;
        }
        if (p_ctx->enable_inner_explore_sim_rank_) {
          return true;
        }
        return false;
  });


  // 磁力快招 B 端: 作品线索点击
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                       engine_base::PredictType::PredictType_consult_ctr,
                       "pop_photo_click_cmd", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if ((p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
        (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
         p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
        p_ad->get_ocpx_action_type() == AdActionType::AD_BUTTON_CLICK_CONSULT) {
      return true;
    }
    if (!p_ad->Is(AdFlag::is_live_ad) &&
        p_ad->Is(AdFlag::is_self_service_ad) &&
        p_ad->get_ocpx_action_type() == AdActionType::AD_ITEM_CLICK) {
      return true;
    }
    return false;
  });

  // 磁力快招 B 端: 作品线索提交
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                       engine_base::PredictType::PredictType_leads_submit,
                       "pop_photo_submit_cmd", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((p_ad->Is(AdFlag::is_photo) || p_ad->Is(AdFlag::is_p2l)) &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) &&
            p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
          return true;
        }
        return false;
      });

  // 磁力快招 B 端: 直播线索提交
  if (cmd_curator_ctx_->session_data_->common_r_
        ->GetIntCommonAttr("enable_clean_inefficient_cmdkey").value_or(0) == 0) {
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                     engine_base::PredictType::PredictType_leads_submit,
                     "pop_live_submit_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if ((p_ad->Is(AdFlag::is_live_ad)) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE &&
          p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
        return true;
      }
      return false;
    });
  }

  // 自助平台: 长播 & 点赞
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                     engine_base::PredictType::PredictType_ltr,
                     "self_service_play_like_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_self_service_ad) &&
          p_ad->get_ocpx_action_type() == AdActionType::AD_FANS_TOP_PLAY) {
        return true;
      }
      return false;
    });

  // game iaa ltv7
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
                                    engine_base::PredictType::PredictType_industry_game_iaa_ltv7,
                                    "ad_dsp_game_iaa_ltv7",
                                    CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS) {
      return true;
    }
    bool is_iaa_roi7_target_account = utility::IsGameIaaRoi7TargetAccount(p_ctx->session_data_, *p_ad);
    if ((p_ctx->enable_iaap_request_iaa_ltv7 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) ||
        (p_ctx->enable_iaap_7r_request_iaa_ltv7 &&
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP)) {
      return true;
    }
    if (p_ctx->enable_white_acount_request_industry_game_iaa_ltv7) {
      if (p_ctx->enable_request_industry_game_iaa_ltv7
          && (is_iaa_roi7_target_account || p_ctx->enable_request_industry_game_iaa_ltv7_all_product)
          && p_ad->Is(AdFlag::is_iaa_game_ltv)) {
          return true;  // 只对 game IAA ltv 样本进行 7r 打分
      }
    } else if (p_ctx->enable_request_industry_game_iaa_ltv7
        && p_ad->Is(AdFlag::is_iaa_game_ltv)) {
        return true;  // 只对 game IAA ltv 样本进行 7r 打分
    }
    return false;
  });
}  // NOLINT

}  // namespace ad_rank
}  // namespace ks
