#include "teams/ad/ad_rank/default/cmd/inner_loop/hard_ad_cmd_condition.h"
#include <memory>
#include <string>
#include <unordered_set>

namespace ks {
namespace ad_rank {
namespace inner_hard_cmd {

#define IS_AMD_PHOTO(p_ad)                                     \
  {                                                            \
    if (p_ad->get_queue_type() != RankAdListType::NORMAL_PHOTO_AD) { \
      return false;                                            \
    }                                                          \
  }

#define IS_AMD_LIVE(p_ad)                                     \
  {                                                           \
    if (p_ad->get_queue_type() != RankAdListType::NORMAL_LIVE_AD) { \
      return false;                                           \
    }                                                         \
  }

#define IS_PHOTO(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_photo)) {   \
    return false;            \
  }                          \
}

#define IS_P2L(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_p2l)) {   \
    return false;          \
  }                        \
}

#define IS_LIVE(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_live)) {   \
    return false;           \
  }                         \
}

#define IS_AMD_PHOTO_OR_SHELF_P2L(p_ad) {     \
  if (p_ad->get_queue_type() != RankAdListType::NORMAL_PHOTO_AD   \
      && !(p_ad->Is(AdFlag::is_p2l_ad_inner)  \
           && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic())) {   \
    return false;                \
  }                             \
}

using ks::engine_base::PredictType;
using kuaishou::ad::AdActionType;
using kuaishou::ad::AdEnum;


std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_server_client_show_inner() {
  // ad_dsp_server_client_show_inner 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
             (p_ctx->session_data_->get_pos_manager_base().IsMicroAppRequest() || p_ctx->session_data_->get_is_feed())) &&  // NOLINT
            ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_photo_campaign) ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign) ||
              p_ad->Is(AdFlag::is_direct_ecom)) &&
             p_ad->get_ocpx_action_type() != kuaishou::ad::AD_CONVERSION)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_server_show_item_imp_inner_ecom() {
  // ad_dsp_server_show_item_imp_inner_ecom 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if ((!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            (p_ctx->IsOcpc2Unit(p_ad) && !(p_ad->get_ad_source_type() == kuaishou::ad::ADX) &&
             !(p_ad->Is(AdFlag::is_dpa)) &&
             !(p_ctx->session_data_->get_is_rewarded())) &&
             !p_ctx->session_data_->get_client_cpm_enable_switch() &&
            ((p_ad->Is(AdFlag::is_direct_ecom) || p_ad->Is(AdFlag::is_ad_merchant_order) || p_ad->Is(AdFlag::is_ad_merchant_follow) ||  // NOLINT
              p_ad->Is(AdFlag::is_reco_roas) || p_ad->Is(AdFlag::is_photo_ad_merchant_follow_quality)||
              p_ad->Is(AdFlag::is_photo_ad_merchant_follow_fast) ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_photo_campaign) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) &&
                  p_ctx->fix_hard_unify_sctr_click_))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_invo_traffic_score_inner() {
  // ad_dsp_invo_traffic_score_inner 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (p_ctx->enable_invo_traffic_predict) {
          return true;
        }
        return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_server_show_item_imp_inner_ecom_rewarded() {  // NOLINT
  // ad_dsp_server_show_item_imp_inner_ecom_rewarded 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (p_ctx->session_data_->get_is_rewarded() &&
            !p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() &&
            !RankKconfUtil::incentiveSctrPredSupPages()->count(p_ctx->session_data_->get_sub_page_id()) &&
            p_ctx->session_data_->get_is_thanos_request()) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (((p_ctx->session_data_->get_is_rewarded())) &&
            (p_ctx->IsOcpc2Unit(p_ad) && !(p_ad->get_ad_source_type() == kuaishou::ad::ADX) &&
             !(p_ad->Is(AdFlag::is_dpa)) &&
             (p_ad->get_creative_circulation_type() ==
              kuaishou::ad::tables::CreativeExtendScore_CreativeCirculationType_INTERNAL_CIRCULATION_TYPE))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> app_ctr_cmd_ecom() {
  // app_ctr_cmd_ecom 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (p_ctx->enable_inner_photo_shelf_ctr_cmd &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if ((!p_ctx->session_data_->get_is_thanos_request() &&
             (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            ((!p_ctx->IsOcpc2Unit(p_ad)) && (!p_ctx->IsAdxLargeCreatives(p_ad))) &&
            ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_photo_campaign) ||
              p_ad->Is(AdFlag::is_direct_ecom)) &&
             (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_CONVERSION))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_photo_shelf_ctr_cmd() {
  // inner_photo_shelf_ctr_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (p_ctx->enable_inner_photo_shelf_ctr_cmd &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic() &&
            !p_ctx->session_data_->get_is_thanos_request()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_photo_ctr() {
  // live_photo_ctr 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
            && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // feed_ctr 硬广金牛移动版、粉条实验
        if (p_ctx->enable_feed_ctr_fanstop_ad) {
          if (((!p_ctx->session_data_->get_is_thanos_request()) &&
              !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
              (p_ad->Is(AdFlag::is_mobile_live_campaign) &&
               p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE)) {
            return true;
          }
        }
        if (((!p_ctx->session_data_->get_is_thanos_request()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            (p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE &&
             p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_server_show_item_imp() {
  // live_server_show_item_imp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (((p_ctx->session_data_->get_is_follow_tab()) ||
             (!p_ctx->session_data_->get_is_follow_tab() &&
              !p_ctx->session_data_->get_is_thanos_request()) ||
             (!p_ctx->session_data_->get_is_follow_tab() &&
              !p_ctx->session_data_->get_is_thanos_request())) &&
            !((p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) && p_ad->Is(AdFlag::is_amd_direct_live)) {  // NOLINT
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_feed() {
  // server_show_live_started_feed 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_item_live_audience_feed(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_shelf_live_card_ctr_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantNoZhuanQianTraffic()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_is_thanos_request()) &&
             !(p_ctx->enable_server_show_live_started_feed_search &&
               p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            (p_ad->Is(AdFlag::is_amd_live_campaign) &&
             p_ad->get_live_creative_type() == AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_feed_auc_base() {
  // server_show_live_started_feed_auc_base 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_live_audience_feed_use_auc) {
          if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
               !(p_ctx->session_data_->get_is_thanos_request()) &&
              !(p_ctx->enable_server_show_live_started_feed_search &&
                p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
              (p_ad->Is(AdFlag::is_amd_live_campaign) &&
              p_ad->get_live_creative_type() == AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_feed_auc_exp() {
  // server_show_live_started_feed_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_live_audience_feed_use_auc) {
          if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
               !(p_ctx->session_data_->get_is_thanos_request()) &&
              !(p_ctx->enable_server_show_live_started_feed_search &&
                p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
              (p_ad->Is(AdFlag::is_amd_live_campaign) &&
              p_ad->get_live_creative_type() == AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> follow_tab_live_photo_ctr() {
  // follow_tab_live_photo_ctr 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
            && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE &&
             p_ad->get_live_creative_type() != AdEnum::LIVE_STREAM_CREATIVE_TYPE &&
             p_ctx->session_data_->get_is_follow_tab())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_merchant_server_show_item_imp_ocpm() {
  // ad_merchant_server_show_item_imp_ocpm 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (p_ctx->session_data_->get_is_rewarded() &&
            !p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() &&
            !RankKconfUtil::incentiveSctrPredSupPages()->count(p_ctx->session_data_->get_sub_page_id()) &&
            p_ctx->session_data_->get_is_thanos_request()) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (((!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
             !p_ctx->session_data_->get_client_cpm_enable_switch() &&
             (p_ctx->IsOcpc2Unit(p_ad) && !(p_ad->Is(AdFlag::is_ad_merchant_follow))) &&
             ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW) ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)) ||
            (p_ctx->IsOcpc2Unit(p_ad) &&
             p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_slide() {
  // server_show_live_started_slide 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_item_live_audience_slide(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_shelf_live_card_ctr_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantNoZhuanQianTraffic()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (((p_ctx->session_data_->get_is_thanos_request()) &&
              !(p_ctx->session_data_->get_is_follow_tab())) &&
            !((p_ctx->session_data_->get_is_live_audience_exclude_sub_page_id())) &&
            !((p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
              (p_ctx->enable_server_show_live_started_slide_search)) &&
            !(p_ctx->session_data_->get_is_explore_or_selected_request() &&
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_FAST ||
               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_AUDIENCE_FAST)) &&
            p_ad->Is(AdFlag::is_amd_live_campaign) && p_ad->Is(AdFlag::is_amd_direct_live)) {
          return true;
        }
        if (!SPDM_enable_item_live_audience_hard_without_p2l(p_ctx->session_data_->get_spdm_ctx())) {
          if (p_ctx->session_data_->get_is_thanos_request() &&
                !p_ctx->session_data_->get_is_follow_tab() &&
                !p_ctx->session_data_->get_is_search_request() &&
                p_ad->Is(AdFlag::is_amd_photo_to_live)) {
            return true;
          }
        }
        if (p_ctx->session_data_->get_is_thanos_request() &&
            p_ctx->session_data_->get_is_inspire_live_request() &&
            (p_ctx->session_data_->get_pos_manager_base().GetAdRequestType() ==
             kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_SMALL_GAME ||
             SPDM_enable_incentive_simple_live(p_ctx->session_data_->get_spdm_ctx()) &&
             RankKconfUtil::incentiveSimpleLivSupPages()->count(p_ctx->session_data_->get_sub_page_id()))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_slide_p5s() {
  // server_show_live_started_slide_p5s 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->session_data_->get_is_thanos_request() &&
            p_ctx->session_data_->get_is_explore_or_selected_request() &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_FAST ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_AUDIENCE_FAST) &&
            p_ad->Is(AdFlag::is_amd_live_campaign) && p_ad->Is(AdFlag::is_amd_direct_live)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_slide_auc_base() {  // NOLINT
  // server_show_live_started_slide_auc_base 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_live_audience_single_use_auc) {
          if (((p_ctx->session_data_->get_is_thanos_request()) &&
                !(p_ctx->session_data_->get_is_follow_tab())) &&
              !((p_ctx->session_data_->get_is_live_audience_exclude_sub_page_id())) &&
              !((p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
                (p_ctx->enable_server_show_live_started_slide_search)) &&
              p_ad->Is(AdFlag::is_amd_live_campaign) && p_ad->Is(AdFlag::is_amd_direct_live)) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_slide_auc_exp() {
  // server_show_live_started_slide_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_live_audience_single_use_auc) {
          if (((p_ctx->session_data_->get_is_thanos_request()) &&
                !(p_ctx->session_data_->get_is_follow_tab())) &&
              !((p_ctx->session_data_->get_is_live_audience_exclude_sub_page_id())) &&
              !((p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
                (p_ctx->enable_server_show_live_started_slide_search)) &&
              p_ad->Is(AdFlag::is_amd_live_campaign) && p_ad->Is(AdFlag::is_amd_direct_live)) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> slide_live_server_show_item_imp() {
  // slide_live_server_show_item_imp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (p_ctx->session_data_->get_is_rewarded() &&
            !p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() &&
            !RankKconfUtil::incentiveSctrPredSupPages()->count(p_ctx->session_data_->get_sub_page_id()) &&
            p_ctx->session_data_->get_is_thanos_request()) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (((!p_ctx->session_data_->get_is_follow_tab() &&
             p_ctx->session_data_->get_is_thanos_request()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
             !p_ctx->session_data_->get_client_cpm_enable_switch() &&
            p_ad->Is(AdFlag::is_amd_live_campaign) && p_ad->Is(AdFlag::is_amd_direct_live)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_nebula_order_paied_cmd() {
  // c1_nebula_order_paied_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_filter_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
          return false;
        }
        if (SPDM_enable_inner_orderpaied_merge_queue(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_inner_merge_all_cvr_ctcvr_multi(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (p_ctx->enable_model_explore) {
            return false;
        }
        if (p_ctx->enable_mobile_photo_soft_model &&
            (!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            p_ad->Is(AdFlag::is_mobile_photo_campaign) &&
            p_ad->get_bid_type() == AdEnum::OCPM_DSP &&
            p_ad->get_is_mobile_soft_to_hard() == true &&
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
            (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)))) {
          return false;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike()) {
          // 暗投
          if (p_ad->get_creative_material_type() != kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
             p_ctx->enable_use_new_order_pay_cmd_for_guess_you_like) {
            return false;
          }
          // 明投
          if (p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
             p_ctx->enable_item_card) {
             return false;
           }
        }
        if (p_ctx->enable_shelf_photo_cmd_fix) {
          if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
            if (p_ctx->enable_use_new_order_paied_cmd_for_mall) {
              return false;
            }
          }
          if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic()) {
            if (p_ctx->enable_use_new_order_paied_cmd_for_zhuanqian) {
              return false;
            }
          }
          if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
            if (p_ctx->enable_use_new_order_paied_cmd_for_buyer_home) {
              return false;
            }
          }
        }
        if ((!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            (!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant()) && ((p_ctx->IsOcpc2Unit(p_ad))) &&  // NOLINT
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
            (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) ||
            (p_ctx->enable_conv_boost_roas_request_order_paid && p_ad->Is(AdFlag::is_inner_photo_roas))) &&
            (!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_nebula_order_paied_explore_cmd() {
  // c1_nebula_order_paied_explore_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_filter_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (!p_ctx->enable_model_explore) {
            return false;
        }
        if ((!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            (!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant()) && ((p_ctx->IsOcpc2Unit(p_ad))) &&  // NOLINT
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) ||
            (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) &&
            (!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_guess_you_like_cmd() {
  // order_paied_for_guess_you_like_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike() &&
            p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return true;
        }
        // 单列商品卡
        if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike()) {
          // 暗投
          if (p_ad->get_creative_material_type() != kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
             p_ctx->enable_use_new_order_pay_cmd_for_guess_you_like) {
            return true;
          }
          // 明投
          if (p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
             p_ctx->enable_item_card) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_mall_cmd() {
  // order_paied_for_mall_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic() &&
            p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_mall) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_zhuanqian_cmd() {
  // order_paied_for_zhuanqian_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic() &&
            p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_zhuanqian) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_buyer_home_cmd() {
  // order_paied_for_buyer_home_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic() &&
            p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_buyer_home) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_gyl_pos_cmd() {
  // order_paied_for_gyl_pos_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (!p_ctx->enable_order_paied_cvr_pos_cmd_for_gyl) {
          return false;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_mall_pos_cmd() {
  // order_paied_for_mall_pos_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (!p_ctx->enable_order_paied_cvr_pos_cmd_for_mall) {
          return false;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_bh_pos_cmd() {
  // order_paied_for_bh_pos_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (!p_ctx->enable_order_paied_cvr_pos_cmd_for_bh) {
          return false;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_order_paid_rate() {
  // c1_order_paid_rate 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_filter_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
          return false;
        }
        if (SPDM_enable_inner_orderpaied_merge_queue(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_inner_merge_all_cvr_ctcvr_multi(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_mall) {
            return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_zhuanqian) {
            return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_buyer_home) {
            return false;
          }
        }
        if (p_ctx->enable_shelf_photo_cmd_fix) {
          if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike()) {
            // 暗投
            if (p_ad->get_creative_material_type() != kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
              p_ctx->enable_use_new_order_pay_cmd_for_guess_you_like) {
              return false;
            }
            // 明投
            if (p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
              p_ctx->enable_item_card) {
              return false;
            }
          }
        }
        if (p_ctx->enable_mobile_photo_soft_model &&
            (!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            p_ad->Is(AdFlag::is_mobile_photo_campaign) &&
            p_ad->get_bid_type() == AdEnum::OCPM_DSP &&
            p_ad->get_is_mobile_soft_to_hard() == true &&
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
            (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)))) {
          return false;
        }
        if (p_ctx->enable_model_explore) {
            return false;
        }
        if ((!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) && ((!p_ctx->IsOcpc2Unit(p_ad))) &&  // NOLINT
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
            (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_pay() {
  // native_photo_pay 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_filter_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
          return false;
        }
        if (SPDM_enable_inner_orderpaied_merge_queue(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_inner_merge_all_cvr_ctcvr_multi(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (p_ctx->enable_shelf_photo_cmd_fix) {
          if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike()) {
            // 暗投
            if (p_ad->get_creative_material_type() != kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
              p_ctx->enable_use_new_order_pay_cmd_for_guess_you_like) {
              return false;
            }
            // 明投
            if (p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
              p_ctx->enable_item_card) {
              return false;
            }
          }
          if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
            if (p_ctx->enable_use_new_order_paied_cmd_for_mall) {
              return false;
            }
          }
          if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic()) {
            if (p_ctx->enable_use_new_order_paied_cmd_for_zhuanqian) {
              return false;
            }
          }
          if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
            if (p_ctx->enable_use_new_order_paied_cmd_for_buyer_home) {
              return false;
            }
          }
        }
        if (p_ctx->enable_mobile_photo_soft_model &&
            (!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            p_ad->Is(AdFlag::is_mobile_photo_campaign) &&
            p_ad->get_bid_type() == AdEnum::OCPM_DSP &&
            p_ad->get_is_mobile_soft_to_hard() == true &&
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
            (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_order_paid_rate_explore() {
  // c1_order_paid_rate_explore 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_filter_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (!p_ctx->enable_model_explore) {
            return false;
        }
        if ((!(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) && ((!p_ctx->IsOcpc2Unit(p_ad))) &&  // NOLINT
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
              (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> effective_play() {
  // effective_play 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_ss_effective_play_drop_request(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (((!p_ctx->session_data_->get_is_splash_request())) &&
            ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) &&
                 (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_EFFECTIVE_PLAY))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_cvr_cmd_ecom() {
  // ad_dsp_cvr_cmd_ecom 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad);
        if (((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) &&
             (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_photo_item_click() {
  // lsp_photo_item_click 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad);
        if (p_ad->Is(AdFlag::is_lsp_photo_item_click_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> item_impression_wtr() {
  // item_impression_wtr 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE &&
             p_ad->get_live_creative_type() != AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_p3s_wtr() {
  // live_p3s_wtr 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if ((p_ad->Is(AdFlag::is_amd_live_campaign) &&
             p_ad->get_live_creative_type() == AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
          if (p_ctx->enable_inner_live_follow_rm_invalid_request_2) {
            if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_FANS_TOP_FOLLOW) {
              return true;
            }
          } else {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_to_live_rate() {
  // photo_to_live_rate 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_p2l_live_audience(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (SPDM_enable_live_p2l_thanos_split(p_ctx->session_data_->get_spdm_ctx())) {
          if (p_ctx->session_data_->get_is_thanos_mix_request()) {
            return false;
          }
        }
        IS_AMD_LIVE(p_ad)
        if (((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             !p_ctx->session_data_->get_is_search_request() &&
             p_ad->get_live_creative_type() != AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_to_live_rate_auc_base() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
    if (!SPDM_enable_p2l_use_auc(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    if (SPDM_enable_p2l_live_audience(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    if (!p_ad->Is(AdFlag::IsHardAd)) {
      return false;
    }
    IS_AMD_LIVE(p_ad)
    if (((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
          p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
         !p_ctx->session_data_->get_is_search_request() &&
         p_ad->get_live_creative_type() != AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
      return true;
    }
    return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_to_live_rate_auc_exp() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
    if (!SPDM_enable_p2l_use_auc(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    if (SPDM_enable_p2l_live_audience(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    if (!p_ad->Is(AdFlag::IsHardAd)) {
      return false;
    }
    IS_AMD_LIVE(p_ad)
    if (((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
          p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
         !p_ctx->session_data_->get_is_search_request() &&
         p_ad->get_live_creative_type() != AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
      return true;
    }
    return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_to_live_rate_thanos() {
  // photo_to_live_rate 精选发现页的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (!SPDM_enable_live_p2l_thanos_split(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             !p_ctx->session_data_->get_is_search_request() &&
             p_ad->get_live_creative_type() != AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> nebula_ntr_model_cmd() {
  // nebula_ntr_model_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (SPDM_enable_close_ntr_pred(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
        }
        IS_AMD_LIVE(p_ad)
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_dpa))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_playtime_cmd() {
  // live_playtime_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        // 粉条+直播+进人目标
        IS_AMD_LIVE(p_ad)
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_merchant_reco_ltv() {
  // ad_dsp_merchant_reco_ltv 的匹配条件
  auto ad_dsp_merchant_reco_ltv_condition = [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) -> bool {
        if (SPDM_enable_filter_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
          return false;
        }
        if (SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
            return false;
        }
        if (SPDM_enable_inner_merge_all_ltv_multi(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_inner_photo_shelf_ltv_multi(p_ctx->session_data_->get_spdm_ctx()) &&
            ((p_ad->Is(AdFlag::is_photo) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) ||
            (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
            p_ad->Is(AdFlag::is_merchant_live_roas) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()))) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO_OR_SHELF_P2L(p_ad)
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
            && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()
            && p_ad->Is(AdFlag::is_merchant_live_roas)) {
          return true;
        }

        if (((p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant()) ||
             (!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() &&
              !p_ctx->enable_neixunhuan_photo_roi_cvr_reset)) &&
            (p_ad->Is(AdFlag::is_reco_roas))) {
          return true;
        }

        if (p_ctx->enable_neixunhuan_photo_roi_cvr_reset &&
              !p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() &&
              !(p_ctx->session_data_->get_is_search_request() && p_ctx->enable_search_jinniu_pc_photo_roi_model) &&  // NOLINT
              p_ctx->enable_inner_hard_gpm_score) {
          if (p_ctx->enable_hc_gpm_order_pay_independent &&
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
               (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
               p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID))) {
            return true;
          }
        }

        if (!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() &&
              !(p_ctx->session_data_->get_is_search_request() && p_ctx->enable_search_jinniu_pc_photo_roi_model) &&  // NOLINT
              p_ctx->enable_inner_hard_gpm_score) {
          if (p_ctx->enable_hc_gpm_order_pay_independent &&
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
               (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
               p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID))) {
            return true;
          }
        }
        return false;
      };
  auto inner_net_transaction_value_ltv_condition_2 =
  [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) -> bool {
    // 净成交 roi 广告判断条件，用于全流量引流测试
    if (SPDM_enable_net_transaction_value_gmv_v2(p_ctx->session_data_->get_spdm_ctx()) &&
        p_ad != nullptr && p_ad->get_bid_assist_type() == 1 &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
        p_ad->get_ocpx_action_type() == AdActionType::AD_STOREWIDE_ROAS) {
        return true;
    }
    return false;
  };
  return [&ad_dsp_merchant_reco_ltv_condition, &inner_net_transaction_value_ltv_condition_2]
  (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
    return
      ad_dsp_merchant_reco_ltv_condition(p_ad, p_ctx) &&
      !inner_net_transaction_value_ltv_condition_2(p_ad, p_ctx);
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> neixunhuan_photo_roi_two_stage_cvr() {
  // neixunhuan_photo_roi_two_stage_cvr 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
            return false;
        }
        if (SPDM_enable_inner_merge_all_cvr_ctcvr_multi(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_mall) {
             return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_zhuanqian) {
             return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_buyer_home) {
            return false;
          }
        }
        if (p_ctx->enable_shelf_photo_cmd_fix) {
          if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike()) {
            if (p_ad->get_creative_material_type() != kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
              p_ctx->enable_use_new_order_pay_cmd_for_guess_you_like) {
              return false;
            }
            if (p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
              p_ctx->enable_item_card) {
              return false;
            }
          }
        }
        if (((!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant())) &&
          !(p_ctx->session_data_->get_is_search_request() &&
            p_ctx->enable_search_jinniu_pc_photo_roi_model) &&
          ((p_ad->Is(AdFlag::is_jinniu_roas) || p_ad->Is(AdFlag::is_reco_roas) ||
            p_ad->Is(AdFlag::is_inner_photo_roas)))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> neixunhuan_photo_roi_gpm() {
  // neixunhuan_photo_roi_gpm 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (SPDM_disable_mix_gmv_in_rank(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (p_ctx->enable_sv_roas_gpm_cmd) {
          if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
            return false;
          }
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_live_p3s_ltv() {
  // ad_dsp_live_p3s_ltv 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        bool enable_only_keep_roas = p_ctx->enable_only_keep_roas;
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            !((p_ctx->enable_hard_roas_live_pay_rate) &&
               p_ad->Is(AdFlag::is_inner_live_roas)) &&
            (!enable_only_keep_roas ||
             enable_only_keep_roas && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                                       p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
                                       p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS ||
                                       p_ad->get_ocpx_action_type() == kuaishou::ad::AD_FANS_TOP_ROI))) {
          return false;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas() {
  // inner_hard_live_roas 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->enable_roas_multi_cmd) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_multi() {
  // inner_hard_live_roas_multi 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (!p_ctx->enable_roas_multi_cmd) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_multi_thanos() {
  // inner_hard_live_roas_multi_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (!p_ctx->enable_roas_multi_cmd) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gmv_atom_qcpx() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_roas) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gmv_atom_multi() {
  // inner_hard_live_gmv_atom_multi 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ctx->enable_inner_live_atomization_net_roas && p_ad->get_bid_assist_type()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_roi_paycnt_atom_multi_item_live() {
  // live_roi_paycnt_atom_multi_item_live 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_LIVE(p_ad)
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_roi_paycnt_atom_multi_item_p2l() {
  // live_roi_pay_cnt_atom_multi_item_p2l 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_P2L(p_ad)
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_net_gmv_atom_multi() {
  // inner_hard_live_net_gmv_atom_multi 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_atomization_net_roas) {
          return false;
        }
        if (!p_ad->get_bid_assist_type()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gmv_atom_qcpx_thanos() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_roas) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv) {
          return true;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gmv_atom_multi_thanos() {
  // inner_hard_live_gmv_atom_multi_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ctx->enable_inner_live_atomization_net_roas && p_ad->get_bid_assist_type()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv) {
          return true;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
live_roi_paycnt_atom_multi_item_live_thanos() {
  // live_roi_paycnt_atom_multi_item_live_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_LIVE(p_ad)
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv) {
          return true;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
live_roi_paycnt_atom_multi_item_p2l_thanos() {
  // live_roi_paycnt_atom_multi_item_p2l_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_P2L(p_ad)
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv) {
          return true;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
inner_hard_live_net_gmv_atom_multi_thanos() {
  // inner_hard_live_net_gmv_atom_multi_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_atomization_net_roas) {
          return false;
        }
        if (!p_ad->get_bid_assist_type()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv) {
          return true;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gpm() {
  // inner_hard_live_gpm 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (SPDM_disable_mix_gmv_in_rank(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_roas_gpm_cmd) {
          if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
            return false;
          }
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_0_2h() {
  // inner_hard_live_roas_7days_0_2h 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() &&
            p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ctx->enable_roas_7days_0_2h_cmd &&
             p_ad->get_ocpx_action_type() ==
                 kuaishou::ad::AD_MERCHANT_T7_ROI)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_0_2h_gmv_atom_qcpx() {   // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_t7roas) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() &&
            p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ctx->enable_roas_7days_0_2h_cmd &&
             p_ad->get_ocpx_action_type() ==
                 kuaishou::ad::AD_MERCHANT_T7_ROI)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_0_2h_gmv_atom_multi() {   // NOLINT
  // inner_hard_live_roas_7days_0_2h_gmv_atom_multi 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() &&
            p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ctx->enable_roas_7days_0_2h_cmd &&
             p_ad->get_ocpx_action_type() ==
                 kuaishou::ad::AD_MERCHANT_T7_ROI)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_t7roi_0_2h_paycnt_atom_multi_item_live() {   // NOLINT
  // live_t7roi_0_2h_paycnt_atom_multi_item_live 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_LIVE(p_ad)
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() &&
            p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ctx->enable_roas_7days_0_2h_cmd &&
             p_ad->get_ocpx_action_type() ==
                 kuaishou::ad::AD_MERCHANT_T7_ROI)) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_t7roi_0_2h_paycnt_atom_multi_item_p2l() {   // NOLINT
  // live_t7roi_0_2h_paycnt_atom_multi_item_p2l 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_P2L(p_ad)
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_follow_tab() &&
            p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ctx->enable_roas_7days_0_2h_cmd &&
             p_ad->get_ocpx_action_type() ==
                 kuaishou::ad::AD_MERCHANT_T7_ROI)) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_2h_3d() {
  // inner_hard_live_roas_7days_2h_3d 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->session_data_->get_is_follow_tab() &&
            p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ctx->enable_roas_7days_2h_3d_cmd &&
             p_ad->get_ocpx_action_type() ==
                 kuaishou::ad::AD_MERCHANT_T7_ROI)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_3d_7d() {
  // inner_hard_live_roas_7days_3d_7d 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->session_data_->get_is_follow_tab() &&
            p_ad->Is(AdFlag::is_amd_live_campaign) &&
            !(p_ctx->session_data_->get_is_search_request() &&
              p_ctx->enable_search_roi_model) &&
            (p_ctx->enable_roas_7days_3d_7d_cmd &&
             p_ad->get_ocpx_action_type() ==
                 kuaishou::ad::AD_MERCHANT_T7_ROI)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> merchant_video_cvr_auc_base() {
  // merchant_video_cvr_auc_base 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant()) &&
            (p_ad->Is(AdFlag::is_ad_merchant_order)) &&
            p_ctx->enable_merchant_video_auc_cross_test) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_merchant_follow_ratio_auc_exp() {  // NOLINT
  // server_show_merchant_follow_ratio_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (p_ctx->IsOcpc2Unit(p_ad) &&
            p_ctx->enable_reco_follow_use_auc_exp &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE)) {
        return true;
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> merchant_video_cvr_auc_exp() {
  // merchant_video_cvr_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant()) &&
            (p_ad->Is(AdFlag::is_ad_merchant_order)) &&
            p_ctx->enable_merchant_video_auc_cross_test) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_live_p3s_ltv_auc_base() {
  // ad_dsp_live_p3s_ltv_auc_base 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_live_ltv_use_auc) {
          bool enable_only_keep_roas = p_ctx->enable_only_keep_roas;
          if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
              (!enable_only_keep_roas ||
               enable_only_keep_roas && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
                                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
                                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_FANS_TOP_ROI))) {
            return true;
          }
          return false;
        }

        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_live_p3s_ltv_auc_exp() {
  // ad_dsp_live_p3s_ltv_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_live_ltv_use_auc) {
          bool enable_only_keep_roas = p_ctx->enable_only_keep_roas;
          if (!p_ctx->session_data_->get_is_follow_tab() && p_ad->Is(AdFlag::is_amd_live_campaign) &&
              (!enable_only_keep_roas ||
               enable_only_keep_roas && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
                                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
                                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_FANS_TOP_ROI))) {
            return true;
          }
          return false;
        }

        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> merchant_photo_roas_cvr_auc_base() {
  // merchant_photo_roas_cvr_auc_base 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_mall) {
             return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_zhuanqian) {
             return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_buyer_home) {
            return false;
          }
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant()) &&
            (!(p_ctx->session_data_->get_is_search_request() &&
                p_ctx->enable_search_jinniu_pc_photo_roi_model)) &&
            ((p_ad->Is(AdFlag::is_jinniu_roas) || p_ad->Is(AdFlag::is_reco_roas) ||
                p_ad->Is(AdFlag::is_inner_photo_roas))) &&
            p_ctx->enable_merchant_photo_roas_cvr_auc_cross_test) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> merchant_photo_roas_cvr_auc_exp() {
  // merchant_photo_roas_cvr_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_mall) {
             return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_buyer_home) {
            return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_zhuanqian) {
            return false;
          }
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant()) &&
            (!(p_ctx->session_data_->get_is_search_request() &&
                p_ctx->enable_search_jinniu_pc_photo_roi_model)) &&
            ((p_ad->Is(AdFlag::is_jinniu_roas) || p_ad->Is(AdFlag::is_reco_roas) ||
                p_ad->Is(AdFlag::is_inner_photo_roas))) &&
            p_ctx->enable_merchant_photo_roas_cvr_auc_cross_test) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_merchant_follow_rate() {
  // c1_merchant_follow_rate 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (p_ctx->session_data_->get_is_feed() &&
            ((!p_ctx->IsOcpc2Unit(p_ad)) &&
             (p_ad->Is(AdFlag::is_merchant_follow_roi) || p_ad->Is(AdFlag::is_photo_ad_merchant_follow_quality) ||  // NOLINT
              p_ad->Is(AdFlag::is_photo_ad_merchant_follow_fast) ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
              p_ad->Is(AdFlag::is_photo_ad_merchant_follow_mobile)))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> rank_imp2_conv_inner_loop_mcda() {
  // rank_imp2_conv_inner_loop_mcda 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
              (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED &&
              p_ad->Is(AdFlag::is_direct_ecom)) ||
             (p_ad->Is(AdFlag::is_jinniu_roas)) || (p_ad->Is(AdFlag::is_reco_roas))) &&
            (p_ad->Is(AdFlag::is_mcda_cov_white_list_ad))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_merchant_follow_ratio() {
  // server_show_merchant_follow_ratio 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad)
        if (((p_ctx->IsOcpc2Unit(p_ad)) &&
             (p_ad->Is(AdFlag::is_merchant_follow_roi) || p_ad->Is(AdFlag::is_photo_ad_merchant_follow_quality) ||  // NOLINT
              p_ad->Is(AdFlag::is_photo_ad_merchant_follow_fast) ||
             (p_ad->Is(AdFlag::is_mobile_photo_campaign)
                && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_FANS_TOP_FOLLOW) ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_ad_user_experience_cmd() {
  // inner_ad_user_experience_cmd 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
      }
      if (p_ad->get_queue_type() != RankAdListType::NORMAL_LIVE_AD && p_ad->get_queue_type() != RankAdListType::NORMAL_PHOTO_AD){   // NOLINT
        return false;
      }
      if (!p_ctx->session_data_->get_is_search_request() &&
          !p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_hard_ad_user_experience_cmd(p_ctx->session_data_->get_spdm_ctx()) ||
           SPDM_enable_cvr_protect(p_ctx->session_data_->get_spdm_ctx()) ||
           SPDM_enable_hc_experience_score(p_ctx->session_data_->get_spdm_ctx()) ||
           (p_ctx->session_data_->get_pos_manager_base().IsSideWindow() &&
            SPDM_enable_side_window_filter_pxr(p_ctx->session_data_->get_spdm_ctx())))) {
        return true;
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_ad_negative_ratio_cmd() {
  // inner_ad_negative_ratio_cmd 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsHardAd)) {
        return false;
      }
      if (SPDM_enable_close_ad_ntr_model(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (SPDM_enable_close_ad_ntr_model_thanos_mix(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
      }
      if (p_ad->get_queue_type() != RankAdListType::NORMAL_LIVE_AD && p_ad->get_queue_type() != RankAdListType::NORMAL_PHOTO_AD){   // NOLINT
        return false;
      }
      if (!p_ctx->session_data_->get_is_search_request()
          && !p_ctx->session_data_->get_is_rewarded()
          && SPDM_enable_ntr_ratio(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_ad_jump_out_rate_cmd() {
  // inner_ad_jump_out_rate_cmd 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsHardAd)) {
        return false;
      }
      if (p_ad->get_queue_type() != RankAdListType::NORMAL_LIVE_AD && p_ad->get_queue_type() != RankAdListType::NORMAL_PHOTO_AD){   // NOLINT
        return false;
      }
      if (!p_ctx->session_data_->get_is_search_request()
          && !p_ctx->session_data_->get_is_rewarded()
          && SPDM_enable_jump_out_rate_model_inner(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_pay_rate() {
  // live_pay_rate 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        // 主站精选 & 极速发现 27&108 流量拆分
        if (p_ctx->session_data_->get_is_thanos_mix_request() && p_ctx->enable_live_pay_single_tab) {
          return false;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
             (p_ad->Is(AdFlag::is_inner_live_roas))) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_conv_boost_roas_request_order_paid && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_pay_rate_atom_qcpx() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_order) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_switch && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_t7_switch &&
         (p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI)) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
             (p_ad->Is(AdFlag::is_inner_live_roas))) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_conv_boost_roas_request_order_paid && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_pay_rate_atom_multi2() {
  // live_pay_rate_atom_multi2 的匹配条件
  // 新增筛选条件注意限制 p_ad->Is(AdFlag::is_live)
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ctx->enable_inner_live_qcpx_order) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_switch && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_t7_switch &&
         (p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI)) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
             (p_ad->Is(AdFlag::is_inner_live_roas))) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_conv_boost_roas_request_order_paid && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_pay_rate_single_tab() {
  // live_pay_rate_single_tab 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request() || !p_ctx->enable_live_pay_single_tab) {
          return false;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
             (p_ad->Is(AdFlag::is_inner_live_roas))) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_conv_boost_roas_request_order_paid && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_live_pay_rate() {
  // lsp_live_pay_rate 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)

        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }

        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspStorewideFlowGMVModelEnsemble(p_ad)) {
          return true;
        }

        // 本地推直播订单拆分
        if (!p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest())) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if ((!(p_ctx->session_data_->get_is_follow_tab()) &&
             !(p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
             (p_ad->Is(AdFlag::is_inner_live_roas))) &&
            p_ad->Is(AdFlag::is_amd_live_campaign)) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_hard_live_roas() {
  // lsp_hard_live_roas 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)

        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l)) &&
            !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ad->Is(AdFlag::is_amd_live_campaign) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_and_p2l_pay_rate_auc_base() {
  // live_and_p2l_pay_rate_auc_base 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsHardAd)) {
        return false;
      }
      IS_AMD_LIVE(p_ad)
      bool enable_live_pay_use_auc = p_ctx->enable_live_pay_use_auc;
      bool enable_merchant_roas_use_auc = p_ctx->enable_merchant_roas_use_auc;
      bool enable_merchant_t7_roi_use_auc = p_ctx->enable_merchant_t7_roi_use_auc;
      // not search && not inspire && (live or p2l)
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !p_ctx->session_data_->get_is_rewarded() &&
          !p_ctx->session_data_->get_is_inspire_live_request() &&
          (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l))) {
          if (enable_live_pay_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::EVENT_ORDER_PAIED) {
            return true;
          }
          if (enable_merchant_roas_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_ROAS) {
            return true;
          }
          if (enable_merchant_t7_roi_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI) {
            return true;
          }
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_and_p2l_pay_rate_auc_exp() {
  // live_and_p2l_pay_rate_auc_exp 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsHardAd)) {
        return false;
      }
      IS_AMD_LIVE(p_ad)
      bool enable_live_pay_use_auc = p_ctx->enable_live_pay_use_auc;
      bool enable_merchant_roas_use_auc = p_ctx->enable_merchant_roas_use_auc;
      bool enable_merchant_t7_roi_use_auc = p_ctx->enable_merchant_t7_roi_use_auc;
      // not search && not inspire && (live or p2l)
      if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
          !p_ctx->session_data_->get_is_rewarded() &&
          !p_ctx->session_data_->get_is_inspire_live_request() &&
          (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l))) {
          if (enable_live_pay_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::EVENT_ORDER_PAIED) {
            return true;
          }
          if (enable_merchant_roas_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_ROAS) {
            return true;
          }
          if (enable_merchant_t7_roi_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI) {
            return true;
          }
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_room_stay_1m_cmd() {
  // live_room_stay_1m_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsHardAd)) {
        return false;
      }
      IS_AMD_LIVE(p_ad)
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_PLAYED_1M &&
          p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE) {
        return true;
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo2live_pay_rate() {
  // photo2live_pay_rate 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request() && p_ctx->enable_live_pay_p2l_single_tab) {
          return false;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE)) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE &&
            p_ad->Is(AdFlag::is_inner_live_t7_roi)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo2live_pay_rate_atom_qcpx() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_order) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_switch && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_t7_switch &&
             (p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI)) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE)) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE &&
            p_ad->Is(AdFlag::is_inner_live_t7_roi)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo2live_pay_rate_atom_multi2() {
  // photo2live_pay_rate_atom_multi2 的匹配条件
  // 新增筛选条件注意限制 p_ad->Is(AdFlag::is_p2l)
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ctx->enable_inner_live_qcpx_order) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (!p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_switch && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_t7_switch &&
             (p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI)) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE)) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE &&
            p_ad->Is(AdFlag::is_inner_live_t7_roi)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo2live_pay_rate_single_tab() {
  // photo2live_pay_rate_single_tab 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)
        if (p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request() || !p_ctx->enable_live_pay_p2l_single_tab) {
          return false;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE)) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE &&
            p_ad->Is(AdFlag::is_inner_live_t7_roi)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_photo2live_pay_rate() {
  // lsp_photo2live_pay_rate 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_LIVE(p_ad)

        // locallife roas use order pay model for roas
        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }

        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspStorewideFlowGMVModelEnsemble(p_ad)) {
          return true;
        }

        // 本地推直播订单拆分
        if (!p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE)) &&
            (p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if ((!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest()) &&
            (p_ad->get_live_creative_type() == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_goods_view() {
  // live_goods_view 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsHardAd)) {
        return false;
      }
      IS_AMD_LIVE(p_ad)
      if ((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_GOODS_VIEW)) {
        return true;
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_private_message_sent_cmd() {
  // photo_private_message_sent_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_PHOTO(p_ad)
        if (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 &&
            p_ad->Is(AdFlag::is_photo) &&
            p_ad->get_ocpx_action_type() == AdActionType::EVENT_PRIVATE_MESSAGE_SENT) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_leads_submit_cmd() {
  // photo_leads_submit_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_PHOTO(p_ad)
        if (p_ad->Is(AdFlag::is_photo)
            && p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2
            && p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_form_submit_lps_cmd() {
  // photo_form_submit_lps_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_PHOTO(p_ad)
        if (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 &&
            p_ad->Is(AdFlag::is_photo) &&
            p_ad->get_ocpx_action_type() == AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> fanstop_live_clue_clk_cmd() {
  // live_clue_clk_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_LIVE(p_ad)
        if (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 &&
            (p_ad->get_ocpx_action_type() == AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED
             || p_ad->get_ocpx_action_type() == AdActionType::LEADS_SUBMIT)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> fanstop_photo_phone_call() {
  // fanstop_photo_phone_call 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_PHOTO(p_ad)
        if (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 &&
            p_ad->Is(AdFlag::is_photo) &&
            p_ad->get_ocpx_action_type() == AdActionType::EVENT_PHONE_CALL) {
          return true;
        }
        return false;
      };
}


bool QcpxCommonCondition(const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
  bool flag = true;
  // hold
  flag &= !SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx());
  if (SPDM_enable_qcpx_live_u0_filter(p_ctx->session_data_->get_spdm_ctx()) && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {  // NOLINT
    auto& buyer_effective_type = p_ctx->session_data_->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();  // NOLINT
    flag &= buyer_effective_type.compare("") != 0;
    flag &= buyer_effective_type.compare("U0") != 0;
  }
  if (SPDM_enable_qcpx_live_u0_filter_potential(p_ctx->session_data_->get_spdm_ctx()) && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {  // NOLINT
    auto& buyer_effective_type = p_ctx->session_data_->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();  // NOLINT
    flag &= buyer_effective_type.compare("") != 0;
  }
  if (!flag) return false;
  // page
  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_subpage_set = RankKconfUtil::qcpxSubpageConfig();  // NOLINT
  if (qcpx_subpage_set == nullptr) return false;
  int64_t subpage_id = p_ctx->session_data_->get_sub_page_id();
  flag &= qcpx_subpage_set->count(subpage_id) > 0;
  // 泛货架
  bool is_hit_shelf_photo = RankKconfUtil::qcpxShelfPhotoAdmitSubpageid()->count(subpage_id)>0;   // NOLINT
  bool is_hit_shelf_live = RankKconfUtil::qcpxShelfLiveAdmitSubpageid()->count(subpage_id)>0;   // NOLINT 
  bool is_hit_rewarded = RankKconfUtil::qcpxRewardAdmitSubpageid()->count(subpage_id)>0; // NOLINT 
  bool enable_qcpx_rewarded = SPDM_enable_qcpx_rewarded(p_ctx->session_data_->get_spdm_ctx()); // NOLINT
  bool enable_qcpx_shelf_run_v2 = (SPDM_enable_qcpx_shelf_run_v2(p_ctx->session_data_->get_spdm_ctx())
                                    && !SPDM_enable_qcpx_shelf_run_v2_hold(p_ctx->session_data_->get_spdm_ctx())); // NOLINT
  bool enable_qcpx_live_shelf_run_v2 = (SPDM_enable_qcpx_live_shelf_run_v2(p_ctx->session_data_->get_spdm_ctx())  // NOLINT
                                        && !SPDM_enable_qcpx_live_shelf_run_v2_hold(p_ctx->session_data_->get_spdm_ctx()));   // NOLINT
  // 关注页
  if (subpage_id == 100013100 || subpage_id == 100016771 || subpage_id == 10008001) {
    flag = true;
  }

  // 泛货架商品卡
  if (enable_qcpx_shelf_run_v2 && is_hit_shelf_photo) {
    if (p_ad->Is(AdFlag::is_photo_ad_inner)) {  // NOLINT
      flag = true;
      // 猜喜不生效
      if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike() && SPDM_disable_qcpx_shelf_guess_run(p_ctx->session_data_->get_spdm_ctx())) {  // NOLINT
        flag = false;
      }
    }
  }

  // 泛货架直播卡
  if (enable_qcpx_live_shelf_run_v2 && is_hit_shelf_live) {
    if ((p_ad->Is(AdFlag::is_p2l_ad_inner) || p_ad->Is(AdFlag::is_live_ad_inner))) {  // NOLINT
      flag = true;
    }
  }

  // 激励
  if (enable_qcpx_rewarded && is_hit_rewarded) {
    flag = true;
  }

  return flag;
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= SPDM_tag_qcpx_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0;
        flag &= p_ad->Is(AdFlag::is_live_ad_inner);
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        if (SPDM_enable_qcpx_model_only_orderpay(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
        }
        return flag;
      };
}

bool QcpxPhotoCommonCondition(const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
  if (p_ad->get_campaign_type() != kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
    return false;
  }
  if (p_ad->get_account_type() != AdEnum::ACCOUNT_ESP &&
      p_ad->get_account_type() != AdEnum::ACCOUNT_ESP_MOBILE) {
    return false;
  }
  if (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
      p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
      p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
    return false;
  }
  if (SPDM_enable_qcpx_photo_filter_u0(p_ctx->session_data_->get_spdm_ctx())) {
    auto& buyer_effective_type = p_ctx->session_data_->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();  // NOLINT
    if (buyer_effective_type.compare("U0") == 0) {
      return false;
    }
  }
  bool flag = QcpxCommonCondition(p_ad, p_ctx);
  flag &= SPDM_enable_qcpx_photo_strategy_run(p_ctx->session_data_->get_spdm_ctx());
  flag &= p_ad->Is(AdFlag::is_photo_ad_inner);
  flag &= p_ad->Is(AdFlag::is_merchant_product);
  flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
  if (flag && SPDM_tag_qcpx_photo_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("arch") == 0) return true;  // NOLINT
  return false;
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_bspline_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // embq
        if (SPDM_enable_qcpx_live_cvr_merge_embq_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // merge cmd
        if (SPDM_enable_qcpx_live_cvr_merge_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        // holdout
        if (SPDM_enable_qcpx_live_orderpay_request_reduce(p_ctx->session_data_->get_spdm_ctx()) &&
            (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
             SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()))) {
          return false;
        }
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= SPDM_enable_qcpx_live_bspline_model(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ad->Is(AdFlag::is_merchant_live);
        flag &= p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner);
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (!SPDM_enable_qcpx_live_roas_use_reduce_coupon(p_ctx->session_data_->get_spdm_ctx())) {  // 发券优选 - 允许 ROAS 发满减券  // NOLINT
          if (SPDM_enable_qcpx_live_only_orderpay(p_ctx->session_data_->get_spdm_ctx())) {
            flag &= p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
          }
        } else {
          flag &= (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
                   p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                   p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
                   p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI);
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
         }
        return false;
      };
}

// @wanpengcheng QCPX 直播订单 CVR Taylor 硬广
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_taylor_bspline_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        // holdout
        if (SPDM_enable_qcpx_live_orderpay_request_reduce(p_ctx->session_data_->get_spdm_ctx()) &&
            (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
             SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()))) {
          return false;
        }
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= SPDM_enable_qcpx_live_taylor_bspline_model(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ad->Is(AdFlag::is_merchant_live);
        flag &= p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner);
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (SPDM_enable_qcpx_live_only_orderpay(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
         }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_live_cvr_full_stage_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // holdout
        if (SPDM_enable_qcpx_live_orderpay_request_reduce(p_ctx->session_data_->get_spdm_ctx()) &&
            (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
             SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()))) {
          return false;
        }
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= SPDM_enable_qcpx_live_full_stage_model(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ad->Is(AdFlag::is_merchant_live);
        flag &= p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner);
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (SPDM_enable_qcpx_live_only_orderpay(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
         }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_p2l_hard_ctr_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // one model
        if (SPDM_enable_p2l_multi_predict(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // holdout
        if (SPDM_enable_qcpx_live_orderpay_request_reduce(p_ctx->session_data_->get_spdm_ctx()) &&
            (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
             SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()))) {
          return false;
        }
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= SPDM_enable_qcpx_p2l_ctr_model(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ctx->session_data_->get_is_thanos_request();
        // roas 流量下仅发折扣券流量预估
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
          flag &= SPDM_enable_qcpx_live_roas_use_rate_coupon(p_ctx->session_data_->get_spdm_ctx());
        }
        flag &= p_ad->Is(AdFlag::IsHardAd);
        flag &= p_ad->Is(AdFlag::is_merchant_live);
        flag &= p_ad->Is(AdFlag::is_p2l_ad_inner);
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        IS_AMD_LIVE(p_ad);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (!((p_ad->get_campaign_type() == AdEnum::LIVE_STREAM_PROMOTE ||
              p_ad->Is(AdFlag::is_mobile_live_campaign)) &&
             !p_ctx->session_data_->get_is_search_request() &&
             p_ad->get_live_creative_type() != AdEnum::LIVE_STREAM_CREATIVE_TYPE)) {
          flag = false;
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
         }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_photo_taylor_piecewise_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // 泛货架流量不请求模型
        if (SPDM_enable_shelf_qcpx_photo_elastic_taylor_bspline_model(p_ctx->session_data_->get_spdm_ctx())
           && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // 软硬广分拆 cmd key
        if (SPDM_enable_qcpx_photo_split_cmd_key(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // 泰勒展开 - 分段线性模型
        if (!SPDM_enable_qcpx_photo_elastic_taylor_bspline_model(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_qcpx_photo_roas_unify_coupon_module(p_ctx->session_data_->get_spdm_ctx()) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)) {
          return false;
        }
        bool flag = true;
        flag &= QcpxPhotoCommonCondition(p_ad, p_ctx);
        return flag;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_photo_tata_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // 泛货架流量不请求模型
        if (SPDM_enable_shelf_qcpx_photo_elastic_taylor_bspline_model(p_ctx->session_data_->get_spdm_ctx())
           && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // 泰勒展开 - 分段线性模型
        if (!SPDM_enable_qcpx_photo_elastic_taylor_bspline_model(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // 软硬广分拆 cmd key
        if (!SPDM_enable_qcpx_photo_split_cmd_key(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        if (SPDM_enable_qcpx_photo_roas_unify_coupon_module(p_ctx->session_data_->get_spdm_ctx()) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)) {
          return false;
        }
        bool flag = true;
        flag &= QcpxPhotoCommonCondition(p_ad, p_ctx);
        return flag;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> shelf_qcpx_photo_taylor_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // 泛货架单独模型
        if (!SPDM_enable_shelf_qcpx_photo_elastic_taylor_bspline_model(p_ctx->session_data_->get_spdm_ctx())
          || !p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        bool flag = true;
        flag &= QcpxPhotoCommonCondition(p_ad, p_ctx);
        return flag;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_photo_cvr_rate_roas_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // 短带折扣券 roas 开关
        if (!SPDM_enable_qcpx_photo_roas_unify_coupon_module(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        bool flag = true;
        flag &= QcpxPhotoCommonCondition(p_ad, p_ctx);
        flag &= ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) || (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS));  // NOLINT
        return flag;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_photo_cvr_unify_roas_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // 短带折扣券 roas 开关
        if (!SPDM_enable_qcpx_photo_roas_unify_coupon_module(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        bool flag = true;
        flag &= QcpxPhotoCommonCondition(p_ad, p_ctx);
        flag &= ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) || (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS));  // NOLINT
        return flag;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_cvr_disc_roas_multi_head_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // embq
        if (SPDM_enable_qcpx_live_cvr_merge_embq_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // merge cmd
        if (SPDM_enable_qcpx_live_cvr_merge_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        // 折扣券 roas 模型开关
        if (!SPDM_enable_qcpx_live_cvr_disc_roas_multi_head_model(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!QcpxCommonCondition(p_ad, p_ctx)) {
          return false;
        }
        // holdout
        if (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
            SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_merchant_live)) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_live_ad_inner) && !p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return false;
        }
        if (p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP &&
            p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP_MOBILE) {
          return false;
        }
        if (!SPDM_enable_qcpx_live_order_use_disc_coupon(p_ctx->session_data_->get_spdm_ctx())) {  // 发券优选 - 允许订单发折扣券  // NOLINT
          // roas
          if (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI) {
            return false;
          }
        } else {
          // roas order
          if (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
            return false;
          }
        }
        // 仅生效直投开关打开，仅预估直投
        if (SPDM_enable_qcpx_live_rate_coupon_only_live(p_ctx->session_data_->get_spdm_ctx()) &&
            !p_ad->Is(AdFlag::is_live_ad_inner)) {
          return false;
        }
        return true;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_cvr_merge_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // embq
        if (SPDM_enable_qcpx_live_cvr_merge_embq_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // merge cmd
        if (!SPDM_enable_qcpx_live_cvr_merge_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        // holdout
        if (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
            SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_merchant_live)) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_live_ad_inner) && !p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return false;
        }
        if (p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP &&
            p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP_MOBILE) {
          return false;
        }
        // 仅生效直投开关打开，仅预估直投
        if (SPDM_enable_qcpx_live_rate_coupon_only_live(p_ctx->session_data_->get_spdm_ctx()) &&
            !p_ad->Is(AdFlag::is_live_ad_inner)) {
          return false;
        }
        // return true;
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
        }
        return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_cvr_merge_embq_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // merge embq cmd
        if (!SPDM_enable_qcpx_live_cvr_merge_embq_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        // holdout
        if (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
            SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_merchant_live)) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_live_ad_inner) && !p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return false;
        }
        if (p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP &&
            p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP_MOBILE) {
          return false;
        }
        // 仅生效直投开关打开，仅预估直投
        if (SPDM_enable_qcpx_live_rate_coupon_only_live(p_ctx->session_data_->get_spdm_ctx()) &&
            !p_ad->Is(AdFlag::is_live_ad_inner)) {
          return false;
        }
        // return true;
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
        }
        return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_inner_explore_sim_condition() {
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        // 直播不请求
        if (!p_ad->Is(AdFlag::IsHardAd) || p_ad->Is(AdFlag::is_live)) {
          return false;
        }
        if (p_ctx->enable_inner_explore_sim_rank_) {
          return true;
        }
        return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> storewide_live_uplift_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      std::shared_ptr<::ks::infra::TailNumberV2> storewide_uplift_tail = RankKconfUtil::storewideUpliftTail();
      std::string exp_tag = SPDM_storewide_live_uplift_exp_tag(p_ctx->session_data_->get_spdm_ctx());
      int64_t page_id = p_ctx->session_data_->get_page_id();
      // 判断是否为直播和短引物料
      if (!(p_ad->Is(AdFlag::is_p2l) || p_ad->Is(AdFlag::is_live))) {
        return false;
      }
      // 低 u 请求过滤
      if (SPDM_enable_storewide_live_uplift_model_block_lower_u(p_ctx->session_data_->get_spdm_ctx())) {
        auto& buyer_effective_type = p_ctx->session_data_->get_rank_request()
            ->ad_request().ad_user_info().buyer_effective_type();
        if (!buyer_effective_type.compare(""))
          return false;
        if (!buyer_effective_type.compare("U0"))
          return false;
        if (!buyer_effective_type.compare("U1"))
          return false;
      }
      // 判断是否为直播全站
      if ((SPDM_enable_storewide_live_uplift_model_cmd(p_ctx->session_data_->get_spdm_ctx()) ||
           (storewide_uplift_tail && storewide_uplift_tail->IsOnFor(p_ad->get_author_id()))) &&
          p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE) {
        return true;
      }
      return false;
  };
}

std::function<bool(const AdCommon* p_ad, CmdCuratorContext* p_ctx)> incentive_quit_rate_cmd() {
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ctx->session_data_->get_is_incentive_explore()) {
      return false;
    }
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (p_ctx->session_data_->get_is_incentive() &&
        (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
         admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
      return true;
    }
    return false;
  };
}

std::function<bool(const AdCommon* p_ad, CmdCuratorContext* p_ctx)> incentive_quit_rate_rct_cmd() {
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    // incentive_quit_rate_cmd 中的条件取反
    if (!p_ctx->session_data_->get_is_incentive_explore()) {
      return false;
    }
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (p_ctx->session_data_->get_is_incentive() &&
        (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
         admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
      return true;
    }
    return false;
  };
}

std::function<bool(const AdCommon* p_ad, CmdCuratorContext* p_ctx)> incentive_deep_quit_rate_cmd() {
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    auto admit_ocpxs = RankKconfUtil::incentiveDeepCoinOcpxs();
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (p_ctx->session_data_->get_is_incentive() &&
        (SPDM_enable_incentive_deep_quit_rate_pred(p_ctx->session_data_->get_spdm_ctx()) ||
        SPDM_enable_incentive_coin_joint_decision(p_ctx->session_data_->get_spdm_ctx())) &&
        (admit_ocpxs && admit_ocpxs->count(p_ad->get_ocpx_action_type())) &&
        (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
         admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
      return true;
    }
    return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> storewide_merchant_uplift_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      std::shared_ptr<::ks::infra::TailNumberV2> storewide_merchant_uplift_tail
          = RankKconfUtil::storewideMerchantUpliftTail();
      // 判断是否为短视频
      if (!(p_ad->Is(AdFlag::is_photo))) {
        return false;
      }
      if (storewide_merchant_uplift_tail == nullptr) {
        return false;
      }
      if (SPDM_enable_storewide_merchant_uplift_model_block_u0_u1(
        p_ctx->session_data_->get_spdm_ctx())) {
        auto& buyer_effective_type = p_ctx->session_data_->get_rank_request()
            ->ad_request().ad_user_info().buyer_effective_type();
        if (!buyer_effective_type.compare(""))
          return false;
        if (!buyer_effective_type.compare("U0"))
          return false;
        if (!buyer_effective_type.compare("U1"))
          return false;
      }
      if (RankKconfUtil::enableStorewideRequestByFlowTail()) {
        // 判断是否为短带, 是否开启对应尾号及流量
        if ((storewide_merchant_uplift_tail->IsOnFor(p_ad->get_campaign_id()) ||
              SPDM_enable_storewide_merchant_uplift_flow_exp(p_ctx->session_data_->get_spdm_ctx()))
            && SPDM_enable_storewide_merchant_uplift_model_cmd(p_ctx->session_data_->get_spdm_ctx())
            && p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_PRODUCT) {
          return true;
        }
      } else {
         // 判断是否为短带
        if (SPDM_enable_storewide_merchant_uplift_model_cmd(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_PRODUCT) {
          return true;
        }
      }
      return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> incentive_deep_task_uplift_cvr_cmd() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (SPDM_enable_deep_incentive_model_add_sub_page_admit(p_ctx->session_data_->get_spdm_ctx())) {
        if (p_ctx->session_data_->get_is_ios_platform()) {
          return false;
        }
        const auto& order_admit_sub_page = RankKconfUtil::orderAdmitSubPageIdSet();  // 主场景
        const auto& incentive_dish_sub_page_id = RankKconfUtil::incentiveDishSubPageId();  // 饭补
        bool main_scene_admit = order_admit_sub_page &&
          order_admit_sub_page->count(p_ctx->session_data_->get_sub_page_id());
        bool dish_admit = SPDM_enable_deep_incentive_add_dish(p_ctx->session_data_->get_spdm_ctx()) &&
          incentive_dish_sub_page_id &&
          incentive_dish_sub_page_id->count(p_ctx->session_data_->get_sub_page_id());
        if (!(main_scene_admit || dish_admit)) {
          return false;
        }
      }
      if (p_ctx->session_data_->get_is_rewarded() &&
          (SPDM_enable_incentive_order_uplift_model(p_ctx->session_data_->get_spdm_ctx()) ||
          SPDM_enable_incentive_coin_joint_decision(p_ctx->session_data_->get_spdm_ctx())) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) {
        return true;
      }
      return false;
  };
}

}  // namespace inner_hard_cmd
}  // namespace ad_rank
}  // namespace ks
