#pragma once

#include "teams/ad/ad_rank/model/cmd_curator.h"
#include "teams/ad/engine_base/cmd_curator/cmd_curator_v2.h"

namespace ks {
namespace ad_rank {
namespace inner_native_cmd {

using ks::engine_base::PredictType;


std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_sctr_feed();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_sctr_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_ctr();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_photo_shelf_ctr_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_lvtr_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_audience();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_audience_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_audience_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_audience_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> effective_play_flash();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_inner_explore_sim_condition();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_fast_p5s();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inspire_live_p5s_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_feed();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_invo_traffic_score_inner();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_wtr();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_pay();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_guess_you_like_cmd();  // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_mall_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_zhuanqian_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_buyer_home_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_gyl_pos_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_mall_pos_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_bh_pos_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay_atom_qcpx();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay_atom_multi2();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay_single_tab();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_native_p2l_pay();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay_follow_tab_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_booking();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_photo_item_click();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_wtr();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay_atom_qcpx();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay_atom_multi2();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay_single_tab();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_native_live_pay();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_native_live_roas();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay_follow_tab_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_and_p2l_pay_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_and_p2l_pay_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_playtime_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_shop_jump();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_cmtr_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> neixunhuan_photo_native_roi();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> neixunhuan_photo_native_roi_gpm();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_ltv();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_gmv();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_gmv();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_multi();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_multi_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gmv_atom_qcpx();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gmv_atom_multi();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_roi_paycnt_atom_multi_item_live();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
live_roi_paycnt_atom_multi_item_live_thanos();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_roi_paycnt_atom_multi_item_p2l();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
live_roi_paycnt_atom_multi_item_p2l_thanos();


std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_net_gmv_atom_multi();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gmv_atom_qcpx_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gmv_atom_multi_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
inner_soft_live_net_gmv_atom_multi_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gpm();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_0_2h();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_0_2h_gmv_atom_qcpx();   // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_0_2h_gmv_atom_multi();   // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_t7roi_0_2h_paycnt_atom_multi_item_live();   // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_t7roi_0_2h_paycnt_atom_multi_item_p2l();   // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_2h_3d();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_3d_7d();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_ad_user_experience_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_gmv_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_gmv_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_feed_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_feed_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_negative_ratio_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_ad_jump_out_rate_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_photo_leads_submit_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_p2l_clue_clk_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_live_clue_clk_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> incentive_quit_rate_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> incentive_quit_rate_rct_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> incentive_deep_quit_rate_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_p2l_soft_ctr_condition();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_bspline_condition();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_taylor_bspline_condition();  // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_cvr_disc_roas_multi_head_condition();  // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_cvr_merge_condition();  // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_cvr_merge_embq_condition();  // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_photo_tata_condition();  // NOLINT
}  // namespace inner_native_cmd
}  // namespace ad_rank
}  // namespace ks
