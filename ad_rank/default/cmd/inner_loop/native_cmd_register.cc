#include "teams/ad/ad_rank/default/cmd/inner_loop/native_cmd_register.h"

#include <vector>
#include <string>
#include "teams/ad/ad_rank/default/cmd/inner_loop/native_cmd_condition.h"
#include "teams/ad/ad_rank/common/macro.h"

namespace ks {
namespace ad_rank {
using kuaishou::ad::AdEnum;
using ks::engine_base::PredictType;
using kuaishou::ad::AdActionType;

#define IS_NATIVE_AD(p_ad) {                            \
  if (p_ad->get_queue_type() != RankAdListType::NATIVE_AD) {  \
    return false;                                       \
  }                                                     \
}                                                       \

#define IS_FANSTOP(p_ad) {                            \
  if (p_ad->get_queue_type() != RankAdListType::FANSTOP) {  \
    return false;                                     \
  }                                                   \
}

#define IS_PHOTO_OR_P2L(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_photo_or_p2l)) {   \
    return false;                   \
  }                                 \
}

#define IS_PHOTO(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_photo)) {   \
    return false;            \
  }                          \
}

#define IS_P2L(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_p2l)) {   \
    return false;          \
  }                        \
}

#define IS_LIVE(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_live)) {   \
    return false;           \
  }                         \
}

void RegisterFanstopCmdV2(CuratorTypeV2* cmd_curator, ContextData* session_data);
void RegisterNativeCommonCmd(CuratorTypeV2* cmd_curator, ContextData* session_data);

void RegisterNativeCtrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data);
void RegisterNativeCvrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data);
void RegisterNativeDeepCvrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data);
void RegisterNativeLtvCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data);
void RegisterNativeCpmLtrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data);
void RegisterNativeAucCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data);
void RegisterNativeOtherCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data);

void RegisterNativeCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) {
  // 原 native cmd
  RegisterNativeCtrCmd(p_cmd_curator, session_data);
  RegisterNativeCvrCmd(p_cmd_curator, session_data);
  RegisterNativeDeepCvrCmd(p_cmd_curator, session_data);
  RegisterNativeLtvCmd(p_cmd_curator, session_data);
  RegisterNativeCpmLtrCmd(p_cmd_curator, session_data);
  RegisterNativeAucCmd(p_cmd_curator, session_data);
  RegisterNativeOtherCmd(p_cmd_curator, session_data);

  // 软广通用
  RegisterNativeCommonCmd(p_cmd_curator, session_data);

  // 原 粉条 cmd
  RegisterFanstopCmdV2(p_cmd_curator, session_data);
}

// cmd  新范式
NativeCmdRegister::NativeCmdRegister(CmdCuratorContext *context) : cmd_curator_ctx_(context) {}

CuratorTypeV2 *NativeCmdRegister::CreateCmdCurator() const {
  auto p_cmd_curator = new CuratorTypeV2(cmd_curator_ctx_);

  RegisterNativeCmd(p_cmd_curator, cmd_curator_ctx_->session_data_);

  return p_cmd_curator;
}

void RegisterNativeCtrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) {
  if (p_cmd_curator == nullptr) return;
  // 磁力金牛双列 sctr（作品 + 直播）
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_server_show_ctr,
                                    "native_photo_sctr_feed", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_photo_sctr_feed());
  // 磁力金牛单列 sctr（作品 + 直播）
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_server_show_ctr,
                                    "native_photo_sctr_thanos", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_photo_sctr_thanos());
  // 作品双列 photo impression -> item impression
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_ctr,
                                    "native_photo_ctr", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_photo_ctr());
  // 货架 photo 双列 photo_impression -> item_impression
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_ctr,
                                    "inner_photo_shelf_ctr_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_photo_shelf_ctr_cmd());
  // 作品单列长播 item impression -> long view
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_ctr,
                                    "photo_lvtr_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::photo_lvtr_cmd());
  // 作品引流 item impression -> audience
  static const std::vector<PredictType> native_p2l_audience_multi_predicts = {
      PredictType::PredictType_live_audience,
      PredictType::PredictType_p2l_qcpx_c0,
      PredictType::PredictType_p2l_qcpx_c1,
      PredictType::PredictType_p2l_qcpx_d0,
      PredictType::PredictType_p2l_qcpx_d1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak2};
  static const std::vector<engine_base::RType> native_p2l_audience_multi_rtypes(
      native_p2l_audience_multi_predicts.size(),
      engine_base::RType::CTR);
  static const std::vector<PredictType> native_p2l_audience_multi_predicts_auc_base = {
      PredictType::PredictType_auc_model_base,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1};
  static const std::vector<engine_base::RType> native_p2l_audience_multi_rtypes_auc_base(
      native_p2l_audience_multi_predicts_auc_base.size(),
      engine_base::RType::AUC_BASE);
  static const std::vector<PredictType> native_p2l_audience_multi_predicts_auc_exp = {
      PredictType::PredictType_auc_model_exp,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1};
  static const std::vector<engine_base::RType> native_p2l_audience_multi_rtypes_auc_exp(
      native_p2l_audience_multi_predicts_auc_exp.size(),
      engine_base::RType::AUC_EXP);
  if (SPDM_enable_p2l_multi_predict(session_data->get_spdm_ctx())) {
    if (!SPDM_enable_second_stage_predict(session_data->get_spdm_ctx()) ||
        (!SPDM_enable_skip_out_emb(session_data->get_spdm_ctx()) &&
         session_data->get_sub_page_id() != 10011001 && session_data->get_sub_page_id() != 11001001)) {
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(native_p2l_audience_multi_rtypes, native_p2l_audience_multi_predicts,
                                        "native_p2l_audience", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(native_p2l_audience_multi_rtypes_auc_base,
                                        native_p2l_audience_multi_predicts_auc_base,
                                        "native_p2l_audience_auc_base", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(native_p2l_audience_multi_rtypes_auc_exp,
                                        native_p2l_audience_multi_predicts_auc_exp,
                                        "native_p2l_audience_auc_exp", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience_auc_exp());
    } else if ((session_data->get_sub_page_id() != 10011001 && session_data->get_sub_page_id() != 11001001)) {
      // 一阶段优化开关打开，并且不属于单列的页面，need_predict_embedding = 0
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        native_p2l_audience_multi_rtypes, native_p2l_audience_multi_predicts,
                                        {}, "native_p2l_audience", CMD_SOURCE_AD_DSP, 0),
          inner_native_cmd::native_p2l_audience());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        native_p2l_audience_multi_rtypes_auc_base,
                                        native_p2l_audience_multi_predicts_auc_base, {},
                                        "native_p2l_audience_auc_base", CMD_SOURCE_AD_DSP, 0),
          inner_native_cmd::native_p2l_audience_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        native_p2l_audience_multi_rtypes_auc_exp,
                                        native_p2l_audience_multi_predicts_auc_exp, {},
                                        "native_p2l_audience_auc_exp", CMD_SOURCE_AD_DSP, 0),
          inner_native_cmd::native_p2l_audience_auc_exp());
    } else {
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
              native_p2l_audience_multi_rtypes, native_p2l_audience_multi_predicts,
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer},
              "native_p2l_audience", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
              native_p2l_audience_multi_rtypes_auc_base, native_p2l_audience_multi_predicts_auc_exp,
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer_auc_base},
              "native_p2l_audience_auc_base", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
              native_p2l_audience_multi_rtypes_auc_exp, native_p2l_audience_multi_predicts_auc_exp,
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer_auc_exp},
              "native_p2l_audience_auc_exp", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience_auc_exp());
    }
  } else {
    if (!SPDM_enable_second_stage_predict(session_data->get_spdm_ctx()) ||
        (!SPDM_enable_skip_out_emb(session_data->get_spdm_ctx()) &&
         session_data->get_sub_page_id() != 10011001 && session_data->get_sub_page_id() != 11001001)) {
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                        "native_p2l_audience", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                        "native_p2l_audience_auc_base", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                        "native_p2l_audience_auc_exp", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience_auc_exp());
    } else if ((session_data->get_sub_page_id() != 10011001 && session_data->get_sub_page_id() != 11001001)) {
      // 一阶段优化开关打开，并且不属于单列的页面，need_predict_embedding = 0
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        {engine_base::RType::CTR}, {PredictType::PredictType_live_audience},
                                        {}, "native_p2l_audience", CMD_SOURCE_AD_DSP, 0),
          inner_native_cmd::native_p2l_audience());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        {engine_base::RType::AUC_BASE},
                                        {PredictType::PredictType_auc_model_base}, {},
                                        "native_p2l_audience_auc_base", CMD_SOURCE_AD_DSP, 0),
          inner_native_cmd::native_p2l_audience_auc_base());
      p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                                     ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                     {engine_base::RType::AUC_EXP}, {PredictType::PredictType_auc_model_exp},
                                     {}, "native_p2l_audience_auc_exp", CMD_SOURCE_AD_DSP, 0),
                                 inner_native_cmd::native_p2l_audience_auc_exp());
    } else {
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG, {engine_base::RType::CTR},
              {PredictType::PredictType_live_audience},
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer},
              "native_p2l_audience", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG, {engine_base::RType::AUC_BASE},
              {PredictType::PredictType_auc_model_base},
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer_auc_base},
              "native_p2l_audience_auc_base", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG, {engine_base::RType::AUC_EXP},
              {PredictType::PredictType_auc_model_exp},
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer_auc_exp},
              "native_p2l_audience_auc_exp", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_audience_auc_exp());
    }
  }
  // 精选发现页作品引流 item impression -> audience
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                    "native_p2l_audience_thanos", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_p2l_audience_thanos());
  // 作品有效播放 item impression -> effective play
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                    "effective_play_flash", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::effective_play_flash());
  // 直播单列 feed impression -> audience
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                    "native_live_audience", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_audience());
  // 直播单列 feed impression -> audience(p5s)
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                    "native_live_audience_fast_p5s", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_audience_fast_p5s());

  // 直播激励单列 p15s（理论上没什么量了，新版激励直播都是双列；只影响 CPC/oCPC）
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                    "inspire_live_p5s_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inspire_live_p5s_cmd());

  if (!SPDM_enable_item_live_audience_feed(session_data->get_spdm_ctx())) {
    // 直播双列 feed impression -> audience
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                      "native_live_audience_feed", CMD_SOURCE_AD_DSP),
                              inner_native_cmd::native_live_audience_feed());
  }
}

void RegisterNativeCvrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) {
  if (p_cmd_curator == nullptr) return;
  // 作品 item impression -> follow
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_wtr, "native_photo_wtr",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_photo_wtr());
  // 作品 item impression -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_photo_pay", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_photo_pay());
  // 猜喜 item impression -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_order_paied_for_guess_you_like_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_order_paied_for_guess_you_like_cmd());
  // 商城 item impression -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_order_paied_for_mall_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_order_paied_for_mall_cmd());
  // 赚钱 item impression -> order paid
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                  "native_order_paied_for_zhuanqian_cmd", CMD_SOURCE_AD_DSP),
                          inner_native_cmd::native_order_paied_for_zhuanqian_cmd());
  // 买首 item impression -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_order_paied_for_buyer_home_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_order_paied_for_buyer_home_cmd());
  // 货架 item impression -> order paid，分位次
  static const std::vector<engine_base::PredictType> inner_shelf_order_pay_cvr_pos_pts = {
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos1,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos2,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos3,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos4,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos5,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos6,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos7,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos8,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos9,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos10};
  static const std::vector<engine_base::RType> inner_shelf_order_pay_cvr_pos_rts(
      inner_shelf_order_pay_cvr_pos_pts.size(),
      engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(inner_shelf_order_pay_cvr_pos_rts, inner_shelf_order_pay_cvr_pos_pts,
                                    "native_order_paied_for_gyl_pos_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_order_paied_for_gyl_pos_cmd());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(inner_shelf_order_pay_cvr_pos_rts, inner_shelf_order_pay_cvr_pos_pts,
                                    "native_order_paied_for_mall_pos_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_order_paied_for_mall_pos_cmd());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(inner_shelf_order_pay_cvr_pos_rts, inner_shelf_order_pay_cvr_pos_pts,
                                    "native_order_paied_for_bh_pos_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_order_paied_for_bh_pos_cmd());

  // 作品引流 audience -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_p2l_pay", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_p2l_pay());

  static const std::vector<PredictType> inner_live_atom_order_multi_predicts2 = {
      PredictType::PredictType_order_paid,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3,
      PredictType::PredictType_inner_live_atom_paycnt_bak4,
      PredictType::PredictType_inner_live_atom_paycnt_bak5,
      PredictType::PredictType_inner_live_atom_paycnt_bak6,
      PredictType::PredictType_inner_live_atom_paycnt_bak7,
      PredictType::PredictType_inner_live_atom_paycnt_bak8,
      PredictType::PredictType_inner_live_atom_paycnt_bak9,
      PredictType::PredictType_inner_live_atom_paycnt_bak10,
      PredictType::PredictType_inner_live_atom_paycnt_bak11,
      PredictType::PredictType_inner_live_atom_paycnt_bak12,
      PredictType::PredictType_inner_live_atom_paycnt_bak13,
      PredictType::PredictType_inner_live_atom_paycnt_bak14,
      PredictType::PredictType_inner_live_atom_paycnt_bak15,
      PredictType::PredictType_inner_live_atom_paycnt_bak16};
  static const std::vector<engine_base::RType> inner_live_atom_order_multi_rtypes2(
                       inner_live_atom_order_multi_predicts2.size(),
                       engine_base::RType::CVR);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_order_multi_rtypes2,
          inner_live_atom_order_multi_predicts2,
          "native_live_pay_atom_qcpx", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_live_pay_atom_qcpx());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_order_multi_rtypes2,
          inner_live_atom_order_multi_predicts2,
          "native_p2l_pay_atom_qcpx", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_pay_atom_qcpx());

  static const std::vector<PredictType> inner_live_atom_order_multi_predicts = {
      PredictType::PredictType_order_paid,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3};
  static const std::vector<engine_base::RType> inner_live_atom_order_multi_rtypes(
                       inner_live_atom_order_multi_predicts.size(),
                       engine_base::RType::CVR);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_order_multi_rtypes,
          inner_live_atom_order_multi_predicts,
          "native_p2l_pay_atom_multi2", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_p2l_pay_atom_multi2());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_p2l_pay_single_tab", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_p2l_pay_single_tab());
  // 作品引流 audience -> order paid 关注页分流实验
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_p2l_pay_follow_tab_exp", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_p2l_pay_follow_tab_exp());
  // 直播预约 item impression -> item click
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_live_booking_rate,
                                    "native_live_booking", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_booking());
  // 本地推短视频点击 item impression -> item click
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_lsp_photo_item_click,
                                    "lsp_photo_item_click", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::lsp_photo_item_click());
  // 直播 feed impression -> follow
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_wtr, "native_live_wtr",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_wtr());
  // 直播 play started -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_live_pay", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_pay());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_order_multi_rtypes,
          inner_live_atom_order_multi_predicts,
          "native_live_pay_atom_multi2", CMD_SOURCE_AD_DSP),
          inner_native_cmd::native_live_pay_atom_multi2());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_live_pay_single_tab", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_pay_single_tab());

  // locallife live order_paid for ROAS
  if (SPDM_enable_locallife_live_roas_use_locallife_model(session_data->get_spdm_ctx())) {
    // paycnt infer
    static const std::vector<PredictType> locallife_roas_order_paid_predicts = {
      PredictType::PredictType_order_paid,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end};
    static const std::vector<engine_base::RType> locallife_roas_order_paid_multi_rtypes(
      locallife_roas_order_paid_predicts.size(), engine_base::RType::CVR);
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(locallife_roas_order_paid_multi_rtypes,
        locallife_roas_order_paid_predicts,
        "lsp_native_live_pay_roas", CMD_SOURCE_AD_DSP),
        inner_native_cmd::lsp_native_live_roas());
  }

  // locallife order paid multi predict
  if (SPDM_enable_locallife_live_order_paid_add_label(session_data->get_spdm_ctx())) {
    // direct live
    static const std::vector<PredictType> locallife_order_order_paid_predicts = {
      PredictType::PredictType_order_paid,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end};
    static const std::vector<engine_base::RType> locallife_order_order_paid_multi_rtypes(
      locallife_order_order_paid_predicts.size(), engine_base::RType::CVR);
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(locallife_order_order_paid_multi_rtypes,
                                    locallife_order_order_paid_predicts,
                                    "lsp_native_live_pay", CMD_SOURCE_AD_DSP),
                                    inner_native_cmd::lsp_native_live_pay());
    // p2l
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(locallife_order_order_paid_multi_rtypes,
                                    locallife_order_order_paid_predicts,
                                    "lsp_native_p2l_pay", CMD_SOURCE_AD_DSP),
                                    inner_native_cmd::lsp_native_p2l_pay());
  } else {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
        "lsp_native_live_pay", CMD_SOURCE_AD_DSP),
        inner_native_cmd::lsp_native_live_pay());
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
        "lsp_native_p2l_pay", CMD_SOURCE_AD_DSP),
        inner_native_cmd::lsp_native_p2l_pay());
  }

  // 直播 play started -> order paid 关注页分流实验
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_order_paid,
                                    "native_live_pay_follow_tab_exp", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_pay_follow_tab_exp());
  // 直播订单 [旁路 AUC]
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "native_live_and_p2l_pay_auc_base", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_and_p2l_pay_auc_base());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "native_live_and_p2l_pay_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_and_p2l_pay_auc_exp());
  // 直播 feed impression -> p60s
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_playtime,
                                    "live_playtime_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::live_playtime_cmd());
  // 直播 feed impression -> shop link jump
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_shop_jump,
                                    "native_live_shop_jump", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_shop_jump());
  // 直播 audience -> cmt
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_cmtr, "live_cmtr_cmd",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::live_cmtr_cmd());
}

void RegisterNativeDeepCvrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) {
  if (p_cmd_curator == nullptr) return;
}

void RegisterNativeLtvCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) {
  if (p_cmd_curator == nullptr) return;
  // 短视频软广 order paid -> gmv
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_native_gmv,
          "neixunhuan_photo_native_roi", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::neixunhuan_photo_native_roi());
  // 短视频软广 GPM imp -> gmv
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_mix_unify_gpm,
          "neixunhuan_photo_native_roi_gpm", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::neixunhuan_photo_native_roi_gpm());
  // 作品 order paid -> gmv
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_gmv, "native_photo_ltv",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_photo_ltv());
  // 作品引流 audience -> gmv
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_gmv, "native_p2l_gmv",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_p2l_gmv());
  // 直播 audience -> gmv
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_gmv, "native_live_gmv",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_gmv());
}

void RegisterNativeCpmLtrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) {
  if (p_cmd_curator == nullptr) return;
}

void RegisterNativeCommonCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) {
  if (p_cmd_curator == nullptr) return;

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
      PredictType::PredictType_inner_live_roas, "inner_soft_live_roas",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_roas());

  static const std::vector<PredictType> inner_live_roas_multi_predicts = {
      PredictType::PredictType_inner_live_roas,
      PredictType::PredictType_inner_live_roas_hard,
      PredictType::PredictType_inner_live_roas_front,
      PredictType::PredictType_inner_live_roas_end,
      PredictType::PredictType_inner_live_roi_gmv_front,
      PredictType::PredictType_inner_live_roi_gmv_end,
      PredictType::PredictType_inner_live_roi_pay_front,
      PredictType::PredictType_inner_live_roi_pay_end,
      PredictType::PredictType_inner_live_roi_ext};
  static const std::vector<engine_base::RType> live_roas_predict_types(inner_live_roas_multi_predicts.size(),
                 engine_base::RType::LTV);
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(live_roas_predict_types,
                     inner_live_roas_multi_predicts, "inner_soft_live_roas_multi",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_roas_multi());
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(live_roas_predict_types,
                     inner_live_roas_multi_predicts, "inner_soft_live_roas_multi_thanos",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_roas_multi_thanos());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV,
      PredictType::PredictType_mix_unify_gpm, "inner_soft_live_gpm",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_gpm());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          engine_base::RType::LTV,
          PredictType::PredictType_inner_live_roas_7days_0_2h,
          "inner_soft_live_roas_7days_0_2h", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_roas_7days_0_2h());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          engine_base::RType::LTV,
          PredictType::PredictType_inner_live_roas_7days_2h_3d,
          "inner_soft_live_roas_7days_2h_3d", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_roas_7days_2h_3d());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          engine_base::RType::LTV,
          PredictType::PredictType_inner_live_roas_7days_3d_7d,
          "inner_soft_live_roas_7days_3d_7d", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_roas_7days_3d_7d());

  static const std::vector<PredictType> inner_live_atom_roi_paycnt_multi_predicts2 = {
      PredictType::PredictType_order_paid,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3,
      PredictType::PredictType_inner_live_atom_paycnt_bak4,
      PredictType::PredictType_inner_live_atom_paycnt_bak5,
      PredictType::PredictType_inner_live_atom_paycnt_bak6,
      PredictType::PredictType_inner_live_atom_paycnt_bak7,
      PredictType::PredictType_inner_live_atom_paycnt_bak8,
      PredictType::PredictType_inner_live_atom_paycnt_bak9,
      PredictType::PredictType_inner_live_atom_paycnt_bak10,
      PredictType::PredictType_inner_live_atom_paycnt_bak11,
      PredictType::PredictType_inner_live_atom_paycnt_bak12,
      PredictType::PredictType_inner_live_atom_paycnt_bak13,
      PredictType::PredictType_inner_live_atom_paycnt_bak14,
      PredictType::PredictType_inner_live_atom_paycnt_bak15,
      PredictType::PredictType_inner_live_atom_paycnt_bak16};
  static const std::vector<engine_base::RType> inner_live_atom_roi_paycnt_multi_rtypes2(
                       inner_live_atom_roi_paycnt_multi_predicts2.size(),
                       engine_base::RType::CVR);
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(inner_live_atom_roi_paycnt_multi_rtypes2,
                     inner_live_atom_roi_paycnt_multi_predicts2, "inner_soft_live_roi_paycnt_atom_qcpx",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_gmv_atom_qcpx());
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(inner_live_atom_roi_paycnt_multi_rtypes2,
                     inner_live_atom_roi_paycnt_multi_predicts2, "inner_soft_live_roi_paycnt_atom_qcpx_thanos",   // NOLINT
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_gmv_atom_qcpx_thanos());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_roi_paycnt_multi_rtypes2,
          inner_live_atom_roi_paycnt_multi_predicts2,
          "inner_soft_live_roas_7days_0_2h_roi_paycnt_atom_qcpx", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_roas_7days_0_2h_gmv_atom_qcpx());

  static const std::vector<PredictType> inner_live_atom_roi_paycnt_multi_predicts = {
      PredictType::PredictType_order_paid,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3};
  static const std::vector<engine_base::RType> inner_live_atom_roi_paycnt_multi_rtypes(
                       inner_live_atom_roi_paycnt_multi_predicts.size(),
                       engine_base::RType::CVR);

  static const std::vector<PredictType> inner_live_atom_roi_gmv_multi_predicts = {
      PredictType::PredictType_inner_live_atom_gmv_all,
      PredictType::PredictType_inner_live_atom_gmv_front,
      PredictType::PredictType_inner_live_atom_gmv_end,
      PredictType::PredictType_inner_live_atom_gmv_indirect};
  static const std::vector<engine_base::RType> inner_live_atom_roi_gmv_multi_rtypes(
                       inner_live_atom_roi_gmv_multi_predicts.size(),
                       engine_base::RType::LTV);

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
        inner_live_atom_roi_paycnt_multi_rtypes,
        inner_live_atom_roi_paycnt_multi_predicts,
        "inner_soft_live_roi_paycnt_atom_multi",
        CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          if (p_ctx->enable_inner_live_qcpx_roas) {
            return false;
          } else if (p_ad->get_bid_assist_type() && p_ctx->enable_inner_live_atomization_net_roas) {
            return inner_native_cmd::inner_soft_live_net_gmv_atom_multi()(p_ad, p_ctx);
          } else if (p_ctx->enable_live_order_item_live_split) {
            return inner_native_cmd::live_roi_paycnt_atom_multi_item_p2l()(p_ad, p_ctx);
          } else {
            return inner_native_cmd::inner_soft_live_gmv_atom_multi()(p_ad, p_ctx);
          }
        });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
        inner_live_atom_roi_paycnt_multi_rtypes,
        inner_live_atom_roi_paycnt_multi_predicts,
        "inner_soft_live_roi_paycnt_atom_multi_thanos",  // NOLINT
        CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          if (p_ctx->enable_inner_live_qcpx_roas) {
            return false;
          } else if (p_ad->get_bid_assist_type() && p_ctx->enable_inner_live_atomization_net_roas) {
            return inner_native_cmd::inner_soft_live_net_gmv_atom_multi_thanos()(p_ad, p_ctx);
          } else if (p_ctx->enable_live_order_item_live_split) {
            return inner_native_cmd::live_roi_paycnt_atom_multi_item_p2l_thanos()(p_ad, p_ctx);
          } else {
            return inner_native_cmd::inner_soft_live_gmv_atom_multi_thanos()(p_ad, p_ctx);
          }
        });
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
        inner_live_atom_roi_paycnt_multi_rtypes,
        inner_live_atom_roi_paycnt_multi_predicts,
        "inner_soft_live_roi_paycnt_atom_multi_item_live",
        CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          return inner_native_cmd::live_roi_paycnt_atom_multi_item_live()(p_ad, p_ctx);
        });

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
        inner_live_atom_roi_paycnt_multi_rtypes,
        inner_live_atom_roi_paycnt_multi_predicts,
        "inner_soft_live_roi_paycnt_atom_multi_item_live_thanos",  // NOLINT
        CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
         return inner_native_cmd::live_roi_paycnt_atom_multi_item_live_thanos()(p_ad, p_ctx);
        });
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(inner_live_atom_roi_gmv_multi_rtypes,
                     inner_live_atom_roi_gmv_multi_predicts, "inner_soft_live_gmv_atom_multi",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_gmv_atom_multi());
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(inner_live_atom_roi_gmv_multi_rtypes,
                     inner_live_atom_roi_gmv_multi_predicts, "inner_soft_live_gmv_atom_multi_thanos",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_gmv_atom_multi_thanos());
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(inner_live_atom_roi_gmv_multi_rtypes,
                     inner_live_atom_roi_gmv_multi_predicts, "inner_soft_live_net_gmv_atom_multi",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_net_gmv_atom_multi());
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(inner_live_atom_roi_gmv_multi_rtypes,
                     inner_live_atom_roi_gmv_multi_predicts, "inner_soft_live_net_gmv_atom_multi_thanos",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_net_gmv_atom_multi_thanos());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_roi_paycnt_multi_rtypes,
          inner_live_atom_roi_paycnt_multi_predicts,
          "inner_soft_live_roas_7days_0_2h_roi_paycnt_atom_multi", CMD_SOURCE_AD_DSP),
          [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
            if (p_ctx->enable_inner_live_qcpx_t7roas) {
              return false;
            } else if (p_ctx->enable_live_order_item_live_split) {
              return inner_native_cmd::live_t7roi_0_2h_paycnt_atom_multi_item_p2l()(p_ad, p_ctx);
            } else {
              return inner_native_cmd::inner_soft_live_roas_7days_0_2h_gmv_atom_multi()(p_ad, p_ctx);
            }
          });
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_roi_paycnt_multi_rtypes,
          inner_live_atom_roi_paycnt_multi_predicts,
          "inner_soft_t7roi_0_2h_paycnt_atom_multi_item_live", CMD_SOURCE_AD_DSP),
          [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
            if (p_ctx->enable_inner_live_qcpx_t7roas) {
              return false;
            } else {
              return inner_native_cmd::live_t7roi_0_2h_paycnt_atom_multi_item_live()(p_ad, p_ctx);
            }
          });
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_roi_gmv_multi_rtypes,
          inner_live_atom_roi_gmv_multi_predicts,
          "inner_soft_live_roas_7days_0_2h_gmv_atom_multi", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::inner_soft_live_roas_7days_0_2h_gmv_atom_multi());

  // ctr 预估
  static const std::vector<PredictType> user_experience_playtime_predicts = {
    PredictType::PredictType_user_experience_score1,
    PredictType::PredictType_user_experience_score2,
    PredictType::PredictType_user_experience_score3,
    PredictType::PredictType_user_experience_score4,
    PredictType::PredictType_user_experience_score5,
    PredictType::PredictType_user_experience_score6,
    PredictType::PredictType_user_experience_score7,
    PredictType::PredictType_user_experience_score8};
  static const std::vector<engine_base::RType> pre_type(user_experience_playtime_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(pre_type, user_experience_playtime_predicts,
                                  "inner_ad_user_experience_cmd", CMD_SOURCE_AD_DSP),
                          inner_native_cmd::inner_ad_user_experience_cmd());
}

void RegisterNativeAucCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) {
  if (p_cmd_curator == nullptr) return;
  // 作品引流 audience -> order paid 旁路 auc
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "native_live_gmv_auc_base", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_gmv_auc_base());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "native_live_gmv_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_gmv_auc_exp());

  // 直播单列 feed impression -> audience  [旁路 auc]
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "native_live_audience_auc_base", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_audience_auc_base());
  // 直播单列 feed impression -> audience  [旁路 auc]
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "native_live_audience_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_audience_auc_exp());
  // 直播双列 feed impression -> audience [旁路 auc]
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "native_live_audience_feed_auc_base", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_audience_feed_auc_base());
  // 直播双列 feed impression -> audience [旁路 auc]
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "native_live_audience_feed_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_live_audience_feed_auc_exp());
}

void RegisterNativeOtherCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) {
  if (p_cmd_curator == nullptr) return;
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED, PredictType::PredictType_negative_ratio,
                                  "native_negative_ratio_cmd", CMD_SOURCE_AD_DSP),
                          inner_native_cmd::native_negative_ratio_cmd());

  if (!SPDM_enable_jump_out_rate_model_inner_combine(session_data->get_spdm_ctx())) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED, PredictType::PredictType_jump_out_rate,
                                    "native_ad_jump_out_rate_cmd", CMD_SOURCE_AD_DSP),
                            inner_native_cmd::native_ad_jump_out_rate_cmd());
  }

p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    engine_base::PredictType::PredictType_invo_traffic_score,
                                    "ad_dsp_invo_traffic_score_inner",
                                    CMD_SOURCE_AD_DSP),
                            inner_native_cmd::ad_dsp_invo_traffic_score_inner());
  // @wanpengcheng inner_qcpx_p2l_soft_ctr_model
  static const std::vector<PredictType> inner_qcpx_p2l_soft_ctr_model_predicts = {
    PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_c0,
    PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_c1,
    PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_d0,
    PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_d1
  };
  static const std::vector<engine_base::RType> qcpx_p2l_soft_ctr_model_predict_types(inner_qcpx_p2l_soft_ctr_model_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_p2l_soft_ctr_model_predict_types, inner_qcpx_p2l_soft_ctr_model_predicts,  // NOLINT
      "inner_qcpx_p2l_soft_ctr_model_cmd", CMD_SOURCE_AD_DSP),
      inner_native_cmd::qcpx_p2l_soft_ctr_condition());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    engine_base::PredictType::PredictType_inner_explore_sim,
                                    "ad_dsp_inner_explore_sim_cmd_inner",
                                    CMD_SOURCE_AD_DSP),
      inner_native_cmd::ad_dsp_inner_explore_sim_condition());

  // @fandi QCPX 订单 CVR 拆软硬广
  static const std::vector<PredictType> inner_qcpx_live_cvr_elastic_bspline_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c0,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c1,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c2,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c3,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c4
  };
  static const std::vector<engine_base::RType> qcpx_live_elastic_bspline_predict_types(inner_qcpx_live_cvr_elastic_bspline_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_elastic_bspline_predict_types, inner_qcpx_live_cvr_elastic_bspline_predicts,
      "native_inner_qcpx_live_cvr_bspline_cmd", CMD_SOURCE_AD_DSP),
      inner_native_cmd::native_qcpx_live_bspline_condition());
  // @wanpengcheng QCPX 直播订单 CVR Taylor 软广
  static const std::vector<PredictType> inner_qcpx_live_cvr_taylor_elastic_bspline_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c0,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c1,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c2,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c3,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c4,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c1_prime
  };
  static const std::vector<engine_base::RType> qcpx_live_taylor_elastic_bspline_predict_types(inner_qcpx_live_cvr_taylor_elastic_bspline_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_taylor_elastic_bspline_predict_types, inner_qcpx_live_cvr_taylor_elastic_bspline_predicts,
      "native_inner_qcpx_live_cvr_taylor_bspline_cmd", CMD_SOURCE_AD_DSP),
      inner_native_cmd::native_qcpx_live_taylor_bspline_condition());
  // @fandi 折扣券多头 CVR 拆软硬广
  static const std::vector<PredictType> inner_qcpx_live_cvr_disc_multi_head_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_950_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_900_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_850_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_800_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_750_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_700_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_650_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_600_logit
  };
  static const std::vector<engine_base::RType> qcpx_live_cvr_disc_multi_head_predict_types(inner_qcpx_live_cvr_disc_multi_head_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_cvr_disc_multi_head_predict_types, inner_qcpx_live_cvr_disc_multi_head_predicts,  // NOLINT
      "native_inner_qcpx_live_cvr_disc_multi_head_cmd", CMD_SOURCE_AD_DSP),
      inner_native_cmd::native_qcpx_live_cvr_disc_roas_multi_head_condition());

  // @wanpengcheng inner_qcpx_live_cvr_merge_cmd
  static const std::vector<PredictType> inner_qcpx_live_cvr_merge_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c0,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c1,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c2,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c3,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c4,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_950_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_900_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_850_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_800_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_750_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_700_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_650_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_600_logit
  };
  static const std::vector<engine_base::RType> qcpx_live_cvr_merge_predict_types(inner_qcpx_live_cvr_merge_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_cvr_merge_predict_types, inner_qcpx_live_cvr_merge_predicts,  // NOLINT
      "native_inner_qcpx_live_cvr_merge_cmd", CMD_SOURCE_AD_DSP),
      inner_native_cmd::native_qcpx_live_cvr_merge_condition());

  // @fandi inner_qcpx_live_cvr_merge_embq_cmd
  static const std::vector<PredictType> inner_qcpx_live_cvr_merge_embq_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e0,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e1,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e2,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e3,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e4,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e5,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e6,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e7,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e8,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e9,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e10,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e11,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e12,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e13,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e14,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e15,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_negbias,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_950_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_900_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_850_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_800_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_750_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_700_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_650_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_600_logit
  };
  static const std::vector<engine_base::RType> qcpx_live_cvr_merge_embq_predict_types(inner_qcpx_live_cvr_merge_embq_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_cvr_merge_embq_predict_types, inner_qcpx_live_cvr_merge_embq_predicts,  // NOLINT
      "native_inner_qcpx_live_cvr_merge_embq_cmd", CMD_SOURCE_AD_DSP),
      inner_native_cmd::native_qcpx_live_cvr_merge_embq_condition());

  // @jiangjiaxin inner_qcpx_photo_cvr_tata_native
  static const std::vector<PredictType> inner_qcpx_photo_cvr_elastic_tata_native_predicts = {
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c0,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c2,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c3,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c4,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c5,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1_prime
  };
  static const std::vector<engine_base::RType> qcpx_photo_elastic_tata_native_predict_types(inner_qcpx_photo_cvr_elastic_tata_native_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_photo_elastic_tata_native_predict_types, inner_qcpx_photo_cvr_elastic_tata_native_predicts,  // NOLINT
      "native_inner_qcpx_photo_cvr_elastic_tata_cmd", CMD_SOURCE_AD_DSP),
      inner_native_cmd::native_qcpx_photo_tata_condition());
  static const std::vector<engine_base::PredictType> incentive_quit_rate_pts = {
      PredictType::PredictType_incentive_quit_rate_0,
      PredictType::PredictType_incentive_quit_rate_1,
      PredictType::PredictType_incentive_quit_rate_2,
      PredictType::PredictType_incentive_quit_rate_3,
      PredictType::PredictType_incentive_quit_rate_4,
      PredictType::PredictType_incentive_quit_rate_5,
      PredictType::PredictType_incentive_quit_rate_6,
      PredictType::PredictType_incentive_quit_rate_7,
      PredictType::PredictType_incentive_quit_rate_8,
      PredictType::PredictType_incentive_quit_rate_9,
      PredictType::PredictType_incentive_quit_rate_10,
      PredictType::PredictType_incentive_quit_rate_11,
      PredictType::PredictType_incentive_quit_rate_12,
      PredictType::PredictType_incentive_quit_rate_13,
      PredictType::PredictType_incentive_quit_rate_14};
  static const std::vector<engine_base::RType> incentive_quit_rate_rts(
      incentive_quit_rate_pts.size(), engine_base::RType::UNCATEGORIZED);
  if (session_data->get_is_incentive_explore()) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                    "incentive_quit_rate_rct", CMD_SOURCE_AD_DSP),
      inner_native_cmd::incentive_quit_rate_rct_cmd());
  } else {
    auto incentive_costly_sub_page_ids = RankKconfUtil::incentiveCostlySubPageIds();
    if (incentive_costly_sub_page_ids->count(session_data->get_sub_page_id()) == 0 &&
        SPDM_enable_incentive_iaa_quit_rate_cmd(session_data->get_spdm_ctx())) {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                      "incentive_quit_rate_iaa", CMD_SOURCE_AD_DSP),
        inner_native_cmd::incentive_quit_rate_cmd());
    } else {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                      "incentive_quit_rate", CMD_SOURCE_AD_DSP),
        inner_native_cmd::incentive_quit_rate_cmd());
    }
  }

  static const std::vector<engine_base::PredictType> incentive_deep_quit_rate_pts = {
      PredictType::PredictType_incentive_deep_quit_rate_0,
      PredictType::PredictType_incentive_deep_quit_rate_1,
      PredictType::PredictType_incentive_deep_quit_rate_2,
      PredictType::PredictType_incentive_deep_quit_rate_3,
      PredictType::PredictType_incentive_deep_quit_rate_4,
      PredictType::PredictType_incentive_deep_quit_rate_5};
  static const std::vector<engine_base::RType> incentive_deep_quit_rate_rts(
      incentive_deep_quit_rate_pts.size(), engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(incentive_deep_quit_rate_rts, incentive_deep_quit_rate_pts,
                                    "incentive_deep_quit_rate", CMD_SOURCE_AD_DSP),
      inner_native_cmd::incentive_deep_quit_rate_cmd());

  if (SPDM_enable_rnd_cold_start_cmd_split(session_data->get_spdm_ctx()) &&
      !SPDM_enable_rnd_cold_start_cmd_split_new_photo(session_data->get_spdm_ctx())) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_rnd_explore_score,
                                    "ad_dsp_rnd_cold_start_inner",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (!p_ctx->session_data_->get_is_search_request()) {
          return true;
        }
        return false;
      });
  } else {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_rnd_explore_score,
                                    "ad_dsp_imp_conv_diversity_rnd_explore_inner",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (!p_ctx->session_data_->get_is_search_request() &&
            SPDM_enable_adx_rta_retarget_rnd_hc_boost(p_ctx->session_data_->get_spdm_ctx())) {
          return true;
        }
        return false;
      });
    if (SPDM_enable_rnd_cold_start_cmd_split_new_photo(session_data->get_spdm_ctx())) {
      // 旧素材还是用老 cmd 新素材用新 cmd
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                      PredictType::PredictType_rnd_explore_score,
                                      "ad_dsp_rnd_cold_start_inner",
                                      CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
          if (!p_ad->Is(AdFlag::IsNativeAd) || !p_ad->Is(AdFlag::is_new_model_creative)) {
            return false;
          }
          IS_NATIVE_AD(p_ad);
          if (!p_ctx->session_data_->get_is_search_request()) {
            return true;
          }
          return false;
        });
    }
  }
}

}  // namespace ad_rank
}  // namespace ks
