#pragma once

#include "teams/ad/ad_rank/model/cmd_curator.h"
#include "teams/ad/engine_base/cmd_curator/cmd_curator_v2.h"

namespace ks {
namespace ad_rank {
namespace inner_hard_cmd {

using ks::engine_base::PredictType;


std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_server_client_show_inner();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_invo_traffic_score_inner();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_server_show_item_imp_inner_ecom();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_server_show_item_imp_inner_ecom_rewarded();  // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_inner_explore_sim_condition();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> app_ctr_cmd_ecom();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_photo_shelf_ctr_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_photo_ctr();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_server_show_item_imp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_feed();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_feed_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_feed_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> follow_tab_live_photo_ctr();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_merchant_server_show_item_imp_ocpm();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_slide();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_slide_p5s();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_slide_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_live_started_slide_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> slide_live_server_show_item_imp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_nebula_order_paied_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_nebula_order_paied_explore_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_guess_you_like_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_mall_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_zhuanqian_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_buyer_home_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_gyl_pos_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_mall_pos_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> order_paied_for_bh_pos_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_order_paid_rate();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_pay();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_order_paid_rate_explore();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> effective_play();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_cvr_cmd_ecom();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_photo_item_click();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> item_impression_wtr();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_p3s_wtr();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_to_live_rate();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_to_live_rate_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_to_live_rate_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_to_live_rate_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> nebula_ntr_model_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_playtime_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_merchant_reco_ltv();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> neixunhuan_photo_roi_two_stage_cvr();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> neixunhuan_photo_roi_gpm();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_live_p3s_ltv();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_multi();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_multi_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gmv_atom_qcpx();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gmv_atom_multi();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_roi_paycnt_atom_multi_item_live();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_roi_paycnt_atom_multi_item_p2l();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
live_roi_paycnt_atom_multi_item_live_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
live_roi_paycnt_atom_multi_item_p2l_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_net_gmv_atom_multi();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gmv_atom_qcpx_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gmv_atom_multi_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
inner_hard_live_net_gmv_atom_multi_thanos();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_gpm();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_0_2h();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_0_2h_gmv_atom_qcpx();   // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_0_2h_gmv_atom_multi();   // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_t7roi_0_2h_paycnt_atom_multi_item_live();   // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_t7roi_0_2h_paycnt_atom_multi_item_p2l();   // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_2h_3d();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_hard_live_roas_7days_3d_7d();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> merchant_video_cvr_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_merchant_follow_ratio_auc_exp();  // NOLINT

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> merchant_video_cvr_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_live_p3s_ltv_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_live_p3s_ltv_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> merchant_photo_roas_cvr_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> merchant_photo_roas_cvr_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> c1_merchant_follow_rate();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> rank_imp2_conv_inner_loop_mcda();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> server_show_merchant_follow_ratio();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_ad_user_experience_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_ad_negative_ratio_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_ad_jump_out_rate_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_pay_rate();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_pay_rate_atom_qcpx();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_pay_rate_atom_multi2();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_pay_rate_single_tab();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_live_pay_rate();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_hard_live_roas();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_and_p2l_pay_rate_auc_base();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_and_p2l_pay_rate_auc_exp();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_room_stay_1m_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo2live_pay_rate();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo2live_pay_rate_atom_qcpx();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo2live_pay_rate_atom_multi2();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo2live_pay_rate_single_tab();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_photo2live_pay_rate();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_goods_view();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_photo_leads_submit_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_p2l_clue_clk_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_live_clue_clk_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_private_message_sent_cmd();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_leads_submit_cmd();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_form_submit_lps_cmd();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> fanstop_photo_phone_call();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> fanstop_live_clue_clk_cmd();

// qcpx
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_condition();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_live_condition();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_live_cvr_disc_roas_multi_head_condition();  // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_live_cvr_full_stage_condition();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_photo_taylor_piecewise_condition();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> shelf_qcpx_photo_taylor_condition();  // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_p2l_hard_ctr_condition();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_bspline_condition();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_taylor_bspline_condition();  // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_cvr_disc_roas_multi_head_condition();  // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_cvr_merge_condition();  // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_live_cvr_merge_embq_condition();  // NOLINT
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_photo_cvr_rate_roas_condition();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> normal_qcpx_photo_tata_condition();
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_photo_cvr_unify_roas_condition();

// storewide uplift
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> storewide_live_uplift_condition();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> storewide_merchant_uplift_condition();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> incentive_quit_rate_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> incentive_quit_rate_rct_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> incentive_deep_quit_rate_cmd();

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> incentive_deep_task_uplift_cvr_cmd();

}  // namespace inner_hard_cmd
}  // namespace ad_rank
}  // namespace ks
