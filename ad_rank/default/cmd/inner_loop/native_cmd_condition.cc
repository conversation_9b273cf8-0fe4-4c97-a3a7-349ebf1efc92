#include "teams/ad/ad_rank/default/cmd/inner_loop/native_cmd_condition.h"

namespace ks {
namespace ad_rank {
namespace inner_native_cmd {

#define IS_NATIVE_AD(p_ad) {                            \
  if (p_ad->get_queue_type() != RankAdListType::NATIVE_AD) {  \
    return false;                                       \
  }                                                     \
}                                                       \

#define IS_FANSTOP(p_ad) {                            \
  if (p_ad->get_queue_type() != RankAdListType::FANSTOP) {  \
    return false;                                     \
  }                                                   \
}

#define IS_PHOTO_OR_P2L(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_photo_or_p2l)) {   \
    return false;                   \
  }                                 \
}

#define IS_PHOTO(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_photo)) {   \
    return false;            \
  }                          \
}

#define IS_P2L(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_p2l)) {   \
    return false;          \
  }                        \
}

#define IS_LIVE(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_live)) {   \
    return false;           \
  }                         \
}

#define IS_PHOTO_OR_SHELF_P2L(p_ad) {     \
  if (!p_ad->Is(AdFlag::is_photo)           \
      && !(p_ad->Is(AdFlag::is_p2l_ad_inner)  \
           && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic())) { \
    return false;                 \
  }                               \
}

using ks::engine_base::PredictType;
using kuaishou::ad::AdActionType;
using kuaishou::ad::AdEnum;



std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_sctr_feed() {
  // native_photo_sctr_feed 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ctx->session_data_->get_is_feed()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_sctr_thanos() {
  // native_photo_sctr_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ctx->session_data_->get_is_rewarded() &&
            !p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() &&
            !RankKconfUtil::incentiveSctrPredSupPages()->count(p_ctx->session_data_->get_sub_page_id()) &&
            p_ctx->session_data_->get_is_thanos_request()) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ctx->session_data_->get_is_thanos_request() &&
            !p_ctx->session_data_->get_client_cpm_enable_switch() &&
            !p_ctx->session_data_->get_is_inspire_mix()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_ctr() {
  // native_photo_ctr 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_PHOTO_OR_P2L(p_ad);
        if (p_ctx->enable_inner_photo_shelf_ctr_cmd &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ctx->session_data_->get_is_feed()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_photo_shelf_ctr_cmd() {
  // inner_photo_shelf_ctr_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_PHOTO_OR_P2L(p_ad);
        if (p_ctx->enable_inner_photo_shelf_ctr_cmd &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic() &&
            p_ctx->session_data_->get_is_feed()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> photo_lvtr_cmd() {
  // photo_lvtr_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_PHOTO(p_ad);
        if (p_ctx->session_data_->get_is_thanos_request() &&
            p_ad->get_ocpx_action_type() == AdActionType::AD_FANS_TOP_PLAY) {
          return true;
        }
        // 针对新内粉的效果类订单 请求长播模型
        if (p_ctx->session_data_->get_is_thanos_request() &&
          p_ad->Is(AdFlag::is_new_inner_fanstop) &&
          p_ad->get_priority_level() == 0) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_fanstop) && p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {  // NOLINT
          return false;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_audience() {
  // native_p2l_audience 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_p2l_live_audience(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (SPDM_enable_live_p2l_thanos_split(p_ctx->session_data_->get_spdm_ctx())) {
          if (p_ctx->session_data_->get_is_thanos_mix_request()) {
            return false;
          }
        }
        IS_P2L(p_ad);
        return true;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_audience_auc_base() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
    if (!SPDM_enable_p2l_use_auc(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    if (SPDM_enable_p2l_live_audience(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    if (!p_ad->Is(AdFlag::IsNativeAd)) {
      return false;
    }
    IS_P2L(p_ad);
    return true;
  };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_audience_auc_exp() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
    if (!SPDM_enable_p2l_use_auc(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    if (SPDM_enable_p2l_live_audience(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    if (!p_ad->Is(AdFlag::IsNativeAd)) {
      return false;
    }
    IS_P2L(p_ad);
    return true;
  };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_audience_thanos() {
  // native_p2l_audience 精选发现的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!SPDM_enable_live_p2l_thanos_split(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_P2L(p_ad);
        return true;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> effective_play_flash() {
  // effective_play_flash 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_ss_effective_play_drop_request(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_PHOTO(p_ad);
        if (p_ad->get_ocpx_action_type() == AdActionType::AD_LIVE_EFFECTIVE_PLAY) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_invo_traffic_score_inner() {
  // ad_dsp_invo_traffic_score_inner 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ctx->enable_invo_traffic_predict) {
          return true;
        }
        return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience() {
  // native_live_audience 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_item_live_audience_slide(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_shelf_live_card_ctr_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantNoZhuanQianTraffic()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_LIVE(p_ad);
        if (p_ctx->session_data_->get_is_thanos_request() &&
            !p_ctx->session_data_->get_is_inspire_live_request() &&
            !(p_ctx->session_data_->get_is_explore_or_selected_request() &&
             (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_FAST ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_AUDIENCE_FAST))) {
          return true;
        }
        if (p_ctx->session_data_->get_is_thanos_request() &&
            p_ctx->session_data_->get_is_inspire_live_request() &&
            (p_ctx->session_data_->get_pos_manager_base().GetAdRequestType() ==
             kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_SMALL_GAME ||
             SPDM_enable_incentive_simple_live(p_ctx->session_data_->get_spdm_ctx()) &&
             RankKconfUtil::incentiveSimpleLivSupPages()->count(p_ctx->session_data_->get_sub_page_id()))) {
          return true;
        }
        // 激励看视频广告位请求直播进人模型
        auto watch_video_sub_page_set = RankKconfUtil::watchVideoSubpageSet();
        auto watch_video_page_set = RankKconfUtil::watchVideoPageSet();
        if (p_ctx->session_data_->get_is_rewarded() &&
            (watch_video_sub_page_set->count(p_ctx->session_data_->get_sub_page_id()) ||
            watch_video_page_set->count(p_ctx->session_data_->get_page_id()))) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_fast_p5s() {
  // native_live_audience_fast_p5s 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_LIVE(p_ad);
        if (p_ctx->session_data_->get_is_thanos_request() &&
            p_ctx->session_data_->get_is_explore_or_selected_request() &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_FAST ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_AUDIENCE_FAST)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inspire_live_p5s_cmd() {
  // inspire_live_p5s_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_LIVE(p_ad);
        if (!SPDM_enable_item_live_audience_slide_inspire_without_p5s(p_ctx->session_data_->get_spdm_ctx())) {
          if (p_ctx->session_data_->get_is_thanos_request() &&
              p_ctx->session_data_->get_is_inspire_live_request()) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> ad_dsp_inner_explore_sim_condition() {
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        // 直播不请求
        if (!p_ad->Is(AdFlag::IsNativeAd) || p_ad->Is(AdFlag::is_live)) {
          return false;
        }
        if (p_ctx->enable_inner_explore_sim_rank_) {
          return true;
        }
        return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_feed() {
  // native_live_audience_feed 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_item_live_audience_feed(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_shelf_live_card_ctr_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantNoZhuanQianTraffic()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_LIVE(p_ad);
        if (p_ctx->session_data_->get_is_feed()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_wtr() {
  // native_photo_wtr 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_ROI)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_pay() {
  // native_photo_pay 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_filter_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
          return false;
        }
        if (SPDM_enable_inner_orderpaied_merge_queue(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_inner_merge_all_cvr_ctcvr_multi(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_mall) {
            return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_zhuanqian) {
            return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          if (p_ctx->enable_use_new_order_paied_cmd_for_buyer_home) {
            return false;
          }
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike() &&
            p_ctx->enable_use_new_order_pay_cmd_for_guess_you_like) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_photo) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
            ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
              (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
              (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID))))) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_photo) &&
          p_ctx->session_data_->get_is_thanos_mix_request()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_guess_you_like_cmd() {  // NOLINT
  // native_order_paied_for_guess_you_like_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        bool is_shelf_p2l_ad = p_ad->Is(AdFlag::is_p2l_ad_inner) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic();
        if (!is_shelf_p2l_ad) {
          IS_NATIVE_AD(p_ad)
          IS_PHOTO(p_ad)
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike() &&
            is_shelf_p2l_ad) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike() &&
            p_ctx->enable_use_new_order_pay_cmd_for_guess_you_like) {
          return true;
         }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_mall_cmd() {
  // native_order_paied_for_mall_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        bool is_shelf_p2l_ad = p_ad->Is(AdFlag::is_p2l_ad_inner) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic();
        if (!is_shelf_p2l_ad) {
          IS_NATIVE_AD(p_ad)
          IS_PHOTO(p_ad)
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic() &&
            is_shelf_p2l_ad) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic() &&
            p_ctx->enable_use_new_order_paied_cmd_for_mall) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_zhuanqian_cmd() {
  // native_order_paied_for_zhuanqian_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        bool is_shelf_p2l_ad = p_ad->Is(AdFlag::is_p2l_ad_inner) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic();
        if (!is_shelf_p2l_ad) {
          IS_NATIVE_AD(p_ad)
          IS_PHOTO(p_ad)
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic() &&
            is_shelf_p2l_ad) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsZhuanQianTraffic() &&
            p_ctx->enable_use_new_order_paied_cmd_for_zhuanqian) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_buyer_home_cmd() {
  // native_order_paied_for_buyer_home_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        bool is_shelf_p2l_ad = p_ad->Is(AdFlag::is_p2l_ad_inner) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic();
        if (!is_shelf_p2l_ad) {
          IS_NATIVE_AD(p_ad)
          IS_PHOTO(p_ad)
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic() &&
            is_shelf_p2l_ad) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic() &&
            p_ctx->enable_use_new_order_paied_cmd_for_buyer_home) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_gyl_pos_cmd() {
  // native_order_paied_for_gyl_pos_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        bool is_shelf_p2l_ad = p_ad->Is(AdFlag::is_p2l_ad_inner) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic();
        if (!is_shelf_p2l_ad) {
          IS_NATIVE_AD(p_ad)
          IS_PHOTO(p_ad)
        }
        if (!p_ctx->enable_order_paied_cvr_pos_cmd_for_gyl) {
          return false;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike()) {
          return true;
         }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_mall_pos_cmd() {
  // native_order_paied_for_mall_pos_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        bool is_shelf_p2l_ad = p_ad->Is(AdFlag::is_p2l_ad_inner) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic();
        if (!is_shelf_p2l_ad) {
          IS_NATIVE_AD(p_ad)
          IS_PHOTO(p_ad)
        }
        if (!p_ctx->enable_order_paied_cvr_pos_cmd_for_mall) {
          return false;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsMallTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_order_paied_for_bh_pos_cmd() {
  // native_order_paied_for_bh_pos_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        bool is_shelf_p2l_ad = p_ad->Is(AdFlag::is_p2l_ad_inner) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic();
        if (!is_shelf_p2l_ad) {
          IS_NATIVE_AD(p_ad)
          IS_PHOTO(p_ad)
        }
        if (!p_ctx->enable_order_paied_cvr_pos_cmd_for_bh) {
          return false;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay() {
  // native_p2l_pay 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
            && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_p2l_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request() && p_ctx->enable_live_pay_p2l_single_tab) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->enable_conv_boost_roas_request_order_paid &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->Is(AdFlag::is_inner_live_t7_roi)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay_atom_qcpx() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_order) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
            && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_switch && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_t7_switch &&
         (p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI)) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_p2l_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->enable_conv_boost_roas_request_order_paid &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->Is(AdFlag::is_inner_live_t7_roi)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay_atom_multi2() {
  // native_p2l_pay_atom_multi2 的匹配条件
  // 新增筛选条件注意限制 p_ad->Is(AdFlag::is_p2l)
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ctx->enable_inner_live_qcpx_order) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
            && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_switch && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_t7_switch &&
         (p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI)) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_p2l_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->enable_conv_boost_roas_request_order_paid &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->Is(AdFlag::is_inner_live_t7_roi)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay_single_tab() {
  // native_p2l_pay 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
            && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_p2l_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request() || !p_ctx->enable_live_pay_p2l_single_tab) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->enable_conv_boost_roas_request_order_paid &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->Is(AdFlag::is_inner_live_t7_roi)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_native_p2l_pay() {
  // lsp_native_p2l_pay 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);

        // locallife roas use order pay model for roas
        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }

        if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspStorewideFlowGMVModelEnsemble(p_ad)) {
          return true;
        }

        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
            && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // 本地推直播订单拆分
        if (!p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_p2l_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_pay_follow_tab_exp() {
  // native_p2l_pay_follow_tab_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
            && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ctx->enable_p2l_pay_follow_tab_exp && p_ctx->session_data_->get_is_follow_tab()) {
          if (p_ad->Is(AdFlag::is_p2l) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {  // NOLINT
            return true;
          }
          if (p_ad->Is(AdFlag::is_p2l) && (p_ctx->enable_soft_roas_live_pay_rate ||
              p_ctx->IsEspLiveAtvTail(p_ad)) &&
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_FANS_TOP_ROI)) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_p2l) && p_ctx->IsLspLiveAtv(p_ad)) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_p2l) && p_ctx->enable_leverage_score_use_post_gmv) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_p2l) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_p2l) &&
              (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
              p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_booking() {
  // native_live_booking 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_photo) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_photo_item_click() {
  // lsp_photo_item_click 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_lsp_photo_item_click_ad)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_wtr() {
  // native_live_wtr 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay() {
  // native_live_pay 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_live_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request() && p_ctx->enable_live_pay_single_tab) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
          return true;
        }
        if (p_ctx->enable_conv_boost_roas_request_order_paid &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay_atom_qcpx() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_order) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (!p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_switch && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_t7_switch &&
         (p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI)) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_live_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
          return true;
        }
        if (p_ctx->enable_conv_boost_roas_request_order_paid &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay_atom_multi2() {
  // native_live_pay_atom_multi2 的匹配条件
  // 新增筛选条件注意限制 p_ad->Is(AdFlag::is_live)
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ctx->enable_inner_live_qcpx_order) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (!p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_switch && p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_t7_switch &&
         (p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI)) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_live_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
          return true;
        }
        if (p_ctx->enable_conv_boost_roas_request_order_paid &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay_single_tab() {
  // native_live_pay_single_tab 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ctx->enable_inner_live_atomization_order) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // 本地推直播订单拆分
        if (p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // Note(lixu05): gpm 计算不再请求订单模型
        if (p_ad->Is(AdFlag::is_esp_ad) &&
            p_ad->Is(AdFlag::is_inner_loop_ad)) {
          // 浅度干掉
          if (!p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
            return false;
          }
        }
        // Note(tengwei): 粉条浅度(非深度)目标不请求模型
        if ((p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU) &&
            !p_ad->Is(AdFlag::is_inner_loop_deep_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_live_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request() || !p_ctx->enable_live_pay_single_tab) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspLiveAtv(p_ad)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
          return true;
        }
        if (p_ctx->enable_conv_boost_roas_request_order_paid &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_native_live_pay() {
  // lsp_native_live_pay 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);

        // locallife roas use order pay model for roas
        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return false;
        }

        if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspStorewideFlowGMVModelEnsemble(p_ad)) {
          return true;
        }

        // 本地推直播订单拆分
        if (!p_ad->Is(AdFlag::is_lsp_live_order_ad)) {
          return false;
        }
        // 关注页分流实验
        if (p_ctx->enable_live_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_live) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) && (p_ctx->enable_soft_roas_live_pay_rate ||
            p_ctx->IsEspLiveAtvTail(p_ad)) &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_leverage_score_use_post_gmv ||
             p_ctx->session_data_->get_is_thanos_mix_request())) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
            p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> lsp_native_live_roas() {
  // lsp_native_live_roas 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);

        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l)) &&
            !p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            p_ad->Is(AdFlag::is_inner_live_roas)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_pay_follow_tab_exp() {
  // native_live_pay_follow_tab_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ctx->enable_live_pay_follow_tab_exp &&
            p_ctx->session_data_->get_is_follow_tab()) {
          if (p_ad->Is(AdFlag::is_live) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {  // NOLINT
            return true;
          }
          if (p_ad->Is(AdFlag::is_live) && (p_ctx->enable_soft_roas_live_pay_rate ||
              p_ctx->IsEspLiveAtvTail(p_ad)) &&
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_FANS_TOP_ROI)) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_live) && p_ctx->IsLspLiveAtv(p_ad)) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_live) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_live) &&
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_live) && p_ctx->enable_leverage_score_use_post_gmv) {
            return true;
          }
          if (p_ad->Is(AdFlag::is_live) &&
              (p_ctx->enable_roas_live_high_atv && p_ad->Is(AdFlag::is_inner_live_roas) &&
              p_ad->get_auto_atv() > p_ctx->high_atv_thre)) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_and_p2l_pay_auc_base() {
  // native_live_and_p2l_pay_auc_base 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad)
        bool enable_live_pay_use_auc = p_ctx->enable_live_pay_use_auc;
        bool enable_merchant_roas_use_auc = p_ctx->enable_merchant_roas_use_auc;
        bool enable_merchant_t7_roi_use_auc = p_ctx->enable_merchant_t7_roi_use_auc;
        // not search && not inspire && (live or p2l)
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            !p_ctx->session_data_->get_is_rewarded() &&
            !p_ctx->session_data_->get_is_inspire_live_request() &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l))) {
          if (enable_live_pay_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::EVENT_ORDER_PAIED) {
            return true;
          }
          if (enable_merchant_roas_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_ROAS) {
            return true;
          }
          if (enable_merchant_t7_roi_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_and_p2l_pay_auc_exp() {
  // native_live_and_p2l_pay_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad)
        bool enable_live_pay_use_auc = p_ctx->enable_live_pay_use_auc;
        bool enable_merchant_roas_use_auc = p_ctx->enable_merchant_roas_use_auc;
        bool enable_merchant_t7_roi_use_auc = p_ctx->enable_merchant_t7_roi_use_auc;
        // not search && not inspire && (live or p2l)
        if (!p_ctx->session_data_->get_pos_manager_base().IsSearchRequest() &&
            !p_ctx->session_data_->get_is_rewarded() &&
            !p_ctx->session_data_->get_is_inspire_live_request() &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l))) {
          if (enable_live_pay_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::EVENT_ORDER_PAIED) {
            return true;
          }
          if (enable_merchant_roas_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_ROAS) {
            return true;
          }
          if (enable_merchant_t7_roi_use_auc &&
              p_ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_playtime_cmd() {
  // live_playtime_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        // 软广 ps 融合实验, 这里同时适配 native 和 fanstop
        if (p_ad->Is(AdFlag::is_live) && p_ctx->session_data_->get_is_thanos_mix_request() &&
            p_ctx->enable_live_calc_vtr_ueq) {
          return true;
        }
        // 粉条+直播+进人目标
        if (p_ad->Is(AdFlag::is_live) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_shop_jump() {
  // native_live_shop_jump 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_live) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_SHOP_LINK_JUMP) {  // NOLINT
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_cmtr_cmd() {
  // live_cmtr_cmd 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ad->get_queue_type() == RankAdListType::NATIVE_AD) {
          if (p_ad->Is(AdFlag::is_live) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_COMMENT) {
            return true;
          }
        } else if (p_ad->get_queue_type() == RankAdListType::FANSTOP) {
          if (p_ad->Is(AdFlag::is_live) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_COMMENT) {
            return true;
          }
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> neixunhuan_photo_native_roi() {
  // neixunhuan_photo_native_roi 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (SPDM_enable_filter_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_photo) && p_ctx->enable_roas_native_use_imp_ltv_switch_new
              && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
              (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) ||
                                 p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
                                 p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) &&
            !(p_ctx->session_data_->get_is_search_request() && p_ctx->enable_search_jinniu_moble_roi_model)) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> neixunhuan_photo_native_roi_gpm() {
  // neixunhuan_photo_native_roi_gpm 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (SPDM_disable_mix_gmv_in_rank(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ad->Is(AdFlag::is_photo) && p_ctx->enable_sv_roas_gpm_cmd) {
          if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
            return false;
          }
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_photo_ltv() {
  // native_photo_ltv 的匹配条件
  auto native_photo_ltv_condition = [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) -> bool {
        if (SPDM_enable_filter_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
          return false;
        }
        if (SPDM_enable_inner_merge_all_ltv_multi(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (SPDM_enable_inner_photo_shelf_ltv_multi(p_ctx->session_data_->get_spdm_ctx()) &&
            ((p_ad->Is(AdFlag::is_photo) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) ||
            (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
            p_ad->Is(AdFlag::is_merchant_live_roas) &&
            p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()))) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ad->get_queue_type() != RankAdListType::NATIVE_AD &&
            !(p_ad->Is(AdFlag::is_p2l_ad_inner) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic())) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_photo) && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||  // NOLINT
                                 p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
                                 (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
                                 p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) ||
                                 p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) &&
            !(p_ctx->session_data_->get_is_search_request() && p_ctx->enable_search_jinniu_moble_roi_model)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_photo) && !p_ctx->enable_leverage_score_use_post_gmv) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
              && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()
              && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||  // NOLINT
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
                  (!SPDM_enable_inner_cid_model(p_ctx->session_data_->get_spdm_ctx()) &&
                  p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) &&
              !(p_ctx->session_data_->get_is_search_request() &&
                p_ctx->enable_search_jinniu_moble_roi_model)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner)
             && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()
             && !p_ctx->enable_leverage_score_use_post_gmv) {
          return true;
        }
        return false;
      };
  auto inner_net_transaction_value_ltv_condition_2 =
  [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) -> bool {
    // 净成交 roi 广告判断条件，用于全流量引流测试
    if (SPDM_enable_net_transaction_value_gmv_v2(p_ctx->session_data_->get_spdm_ctx()) &&
        p_ad != nullptr && p_ad->get_bid_assist_type() == 1 &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
        p_ad->get_ocpx_action_type() == AdActionType::AD_STOREWIDE_ROAS) {
        return true;
    }
    return false;
  };
  return [&native_photo_ltv_condition, &inner_net_transaction_value_ltv_condition_2]
  (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
    return
      native_photo_ltv_condition(p_ad, p_ctx) &&
      !inner_net_transaction_value_ltv_condition_2(p_ad, p_ctx);
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_p2l_gmv() {
  // native_p2l_gmv 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        return false;
        if (p_ad->Is(AdFlag::is_p2l) && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
                               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) &&
            !(p_ctx->session_data_->get_is_search_request() && p_ctx->enable_search_roi_model) &&
            !((p_ctx->enable_soft_roas_live_pay_rate) &&
               p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
             p_ctx->session_data_->get_is_rewarded())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_gmv() {
  // native_live_gmv 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        return false;
        if (p_ad->Is(AdFlag::is_live) && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
                                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) &&
            !(p_ctx->session_data_->get_is_search_request() && p_ctx->enable_search_roi_model) &&
            !((p_ctx->enable_soft_roas_live_pay_rate) &&
              p_ad->Is(AdFlag::is_inner_live_roas))) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live) &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
             p_ctx->session_data_->get_is_rewarded())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas() {
  // inner_soft_live_roas 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->enable_roas_multi_cmd) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_multi() {
  // inner_soft_live_roas_multi 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (!p_ctx->enable_roas_multi_cmd) {
           return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_multi_thanos() {
  // inner_soft_live_roas_multi_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (!p_ctx->enable_roas_multi_cmd) {
           return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gmv_atom_qcpx() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_roas) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // locallife use local model
        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gmv_atom_multi() {
  // inner_soft_live_gmv_atom_multi 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ctx->enable_inner_live_atomization_net_roas && p_ad->get_bid_assist_type()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // locallife use local model
        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_roi_paycnt_atom_multi_item_live() {
  // live_roi_paycnt_atom_multi_item_live 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
       if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_LIVE(p_ad)
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // locallife use local model
        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_roi_paycnt_atom_multi_item_p2l() {
  // live_roi_paycnt_atom_multi_item_p2l 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_P2L(p_ad)
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // locallife use local model
        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_net_gmv_atom_multi() {
  // inner_soft_live_net_gmv_atom_multi 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_atomization_net_roas) {
          return false;
        }
        if (!p_ad->get_bid_assist_type()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // locallife use local model
        if (SPDM_enable_locallife_live_roas_use_locallife_model(p_ctx->session_data_->get_spdm_ctx()) &&
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gmv_atom_qcpx_thanos() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_roas) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l)) &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gmv_atom_multi_thanos() {
  // inner_soft_live_gmv_atom_multi_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ctx->enable_inner_live_atomization_net_roas && p_ad->get_bid_assist_type()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l)) &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
live_roi_paycnt_atom_multi_item_live_thanos() {
  // live_roi_paycnt_atom_multi_item_live_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_LIVE(p_ad)
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l)) &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
live_roi_paycnt_atom_multi_item_p2l_thanos() {
  // live_roi_paycnt_atom_multi_item_p2l_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_P2L(p_ad)
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l)) &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (!p_ctx->enable_inner_live_atomization_roas) {
          return false;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)>
inner_soft_live_net_gmv_atom_multi_thanos() {
  // inner_soft_live_net_gmv_atom_multi_thanos 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_atomization_net_roas) {
          return false;
        }
        if (!p_ad->get_bid_assist_type()) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
            (p_ctx->session_data_->get_is_inspire_live_request() ||
            p_ctx->session_data_->get_pos_manager_base().IsInspireMerchant() ||
            p_ctx->session_data_->get_is_rewarded()) &&
            p_ctx->enable_esp_live_inspire_gmv &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l)) &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
           return false;
        }
        if (p_ad->Is(AdFlag::is_photo_or_p2l) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_inner_live_roas) &&
                !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        if (p_ctx->session_data_->get_pos_manager_base().IsBuyerHomePageTraffic()) {
          return true;
        }
        if (p_ctx->enable_shelf_live_order_pltv_model_exp &&
          p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_gpm() {
  // inner_soft_live_gpm 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (SPDM_disable_mix_gmv_in_rank(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (p_ctx->enable_roas_gpm_cmd &&
            (p_ad->Is(AdFlag::is_live) || p_ad->Is(AdFlag::is_p2l))) {
          if (!p_ctx->session_data_->get_is_thanos_mix_request()) {
            return false;
          }
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_0_2h() {
  // inner_soft_live_roas_7days_0_2h 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (p_ctx->enable_roas_7days_0_2h_cmd && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            p_ad->get_ocpx_action_type() ==
                kuaishou::ad::AD_MERCHANT_T7_ROI &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_0_2h_gmv_atom_qcpx() {   // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_inner_live_qcpx_t7roas) {
          return false;
        }
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ctx->enable_roas_7days_0_2h_cmd && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            p_ad->get_ocpx_action_type() ==
                kuaishou::ad::AD_MERCHANT_T7_ROI &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_0_2h_gmv_atom_multi() {   // NOLINT
  // inner_soft_live_roas_7days_0_2h_gmv_atom_multi 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ctx->enable_roas_7days_0_2h_cmd && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            p_ad->get_ocpx_action_type() ==
                kuaishou::ad::AD_MERCHANT_T7_ROI &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_t7roi_0_2h_paycnt_atom_multi_item_live() {   // NOLINT
  // live_t7roi_0_2h_paycnt_atom_multi_item_live 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_LIVE(p_ad)
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ctx->enable_roas_7days_0_2h_cmd && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            p_ad->get_ocpx_action_type() ==
                kuaishou::ad::AD_MERCHANT_T7_ROI &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        return false;
      };
}
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> live_t7roi_0_2h_paycnt_atom_multi_item_p2l() {   // NOLINT
  // live_t7roi_0_2h_paycnt_atom_multi_item_p2l 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ctx->enable_live_order_item_live_split) {
          return false;
        }
        IS_P2L(p_ad)
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (!p_ctx->enable_inner_live_atomization_t7roas) {
          return false;
        }
        if (p_ad->Is(AdFlag::is_p2l_ad_inner) &&
              p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        if (p_ctx->enable_roas_7days_0_2h_cmd && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            p_ad->get_ocpx_action_type() ==
                kuaishou::ad::AD_MERCHANT_T7_ROI &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_2h_3d() {
  // inner_soft_live_roas_7days_2h_3d 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ctx->enable_roas_7days_2h_3d_cmd && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            p_ad->get_ocpx_action_type() ==
                kuaishou::ad::AD_MERCHANT_T7_ROI &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_soft_live_roas_7days_3d_7d() {
  // inner_soft_live_roas_7days_3d_7d 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        if (p_ctx->enable_roas_7days_3d_7d_cmd && p_ad->Is(AdFlag::is_amd_live_campaign) &&
            p_ad->get_ocpx_action_type() ==
                kuaishou::ad::AD_MERCHANT_T7_ROI &&
            !(p_ctx->session_data_->get_is_search_request())) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> inner_ad_user_experience_cmd() {
  // inner_ad_user_experience_cmd 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsNativeAd)) {
        return false;
      }
      if ((p_ctx->session_data_->get_pos_manager_base().IsSideWindow() &&
           SPDM_enable_side_window_filter_pxr(p_ctx->session_data_->get_spdm_ctx())) ||
           SPDM_enable_inner_native_pxr_cmd(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_gmv_auc_base() {
  // native_live_gmv_auc_base 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ctx->enable_live_ltv_use_auc) {
          if (p_ad->Is(AdFlag::is_live) && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||  // NOLINT
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)) {
            return true;
          }
          return false;
        }

        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_gmv_auc_exp() {
  // native_live_gmv_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (p_ctx->enable_live_ltv_use_auc) {
          if (p_ad->Is(AdFlag::is_live) && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||  // NOLINT
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)) {
            return true;
          }
          return false;
        }

        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_auc_base() {
  // native_live_audience_auc_base 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_LIVE(p_ad);
        if (!p_ctx->enable_live_audience_single_use_auc) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_request() &&
            !p_ctx->session_data_->get_is_inspire_live_request()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_auc_exp() {
  // native_live_audience_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_LIVE(p_ad);
        if (!p_ctx->enable_live_audience_single_use_auc) {
          return false;
        }
        if (p_ctx->session_data_->get_is_thanos_request() &&
            !p_ctx->session_data_->get_is_inspire_live_request()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_feed_auc_base() {
  // native_live_audience_feed_auc_base 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_LIVE(p_ad);
        if (!p_ctx->enable_live_audience_feed_use_auc) {
          return false;
        }
        if (p_ctx->session_data_->get_is_feed()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_live_audience_feed_auc_exp() {
  // native_live_audience_feed_auc_exp 的匹配条件
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
        }
        IS_LIVE(p_ad);
        if (!p_ctx->enable_live_audience_feed_use_auc) {
          return false;
        }
        if (p_ctx->session_data_->get_is_feed()) {
          return true;
        }
        return false;
      };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_negative_ratio_cmd() {
  // native_negative_ratio_cmd 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
      }
      IS_NATIVE_AD(p_ad);
      if (SPDM_enable_close_ad_ntr_model(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
      }
      if (SPDM_enable_close_ad_ntr_model_thanos_mix(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ctx->session_data_->get_is_thanos_mix_request()) {
          return false;
      }
      if (!p_ctx->session_data_->get_is_search_request()
          && !p_ctx->session_data_->get_is_rewarded() &&
          !p_ctx->session_data_->get_is_inspire_live_request()) {
        return true;
      }
      return false;
    };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_ad_jump_out_rate_cmd() {
  // native_ad_jump_out_rate_cmd 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext *p_ctx) {
      if (!p_ad->Is(AdFlag::IsNativeAd)) {
          return false;
      }
      IS_NATIVE_AD(p_ad);
      if (!p_ctx->session_data_->get_is_search_request()
          && !p_ctx->session_data_->get_is_rewarded() &&
          !p_ctx->session_data_->get_is_inspire_live_request()
          && SPDM_enable_jump_out_rate_model_inner(p_ctx->session_data_->get_spdm_ctx())) {
        return true;
      }
      return false;
    };
}

std::function<bool(const AdCommon* p_ad, CmdCuratorContext* p_ctx)> incentive_quit_rate_cmd() {
  // incentive_quit_rate_cmd 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    IS_NATIVE_AD(p_ad);
    if (p_ctx->session_data_->get_is_incentive_explore()) {
      return false;
    }
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (p_ctx->session_data_->get_is_incentive() &&
        (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
         admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
      return true;
    }
    return false;
  };
}

std::function<bool(const AdCommon* p_ad, CmdCuratorContext* p_ctx)> incentive_quit_rate_rct_cmd() {
  // incentive_quit_rate_cmd 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    IS_NATIVE_AD(p_ad);
    // incentive_quit_rate_cmd 中的条件取反
    if (!p_ctx->session_data_->get_is_incentive_explore()) {
      return false;
    }
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (p_ctx->session_data_->get_is_incentive() &&
        (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
         admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
      return true;
    }
    return false;
  };
}

std::function<bool(const AdCommon* p_ad, CmdCuratorContext* p_ctx)> incentive_deep_quit_rate_cmd() {
  // incentive_quit_rate_cmd 的匹配条件
  return [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    IS_NATIVE_AD(p_ad);
    auto admit_ocpxs = RankKconfUtil::incentiveDeepCoinOcpxs();
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (p_ctx->session_data_->get_is_incentive() &&
        SPDM_enable_incentive_deep_quit_rate_pred(p_ctx->session_data_->get_spdm_ctx()) &&
        (admit_ocpxs && admit_ocpxs->count(p_ad->get_ocpx_action_type())) &&
        (admit_page_ids && admit_page_ids->count(p_ctx->session_data_->get_page_id()) ||
         admit_sub_page_ids && admit_sub_page_ids->count(p_ctx->session_data_->get_sub_page_id()))) {
      return true;
    }
    return false;
  };
}

bool QcpxCommonCondition(const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
  bool flag = true;
  if (SPDM_enable_qcpx_live_u0_filter(p_ctx->session_data_->get_spdm_ctx()) && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {  // NOLINT
    auto& buyer_effective_type = p_ctx->session_data_->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();  // NOLINT
    flag &= buyer_effective_type.compare("") != 0;
    flag &= buyer_effective_type.compare("U0") != 0;
  }
  if (SPDM_enable_qcpx_live_u0_filter_potential(p_ctx->session_data_->get_spdm_ctx()) && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {  // NOLINT
    auto& buyer_effective_type = p_ctx->session_data_->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();  // NOLINT
    flag &= buyer_effective_type.compare("") != 0;
  }
  if (!flag) return false;
  // page
  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_subpage_set = RankKconfUtil::qcpxSubpageConfig();  // NOLINT
  if (qcpx_subpage_set == nullptr) return false;
  int64_t subpage_id = p_ctx->session_data_->get_sub_page_id();
  flag &= qcpx_subpage_set->count(subpage_id) > 0;

  bool is_hit_shelf_photo = RankKconfUtil::qcpxShelfPhotoAdmitSubpageid()->count(subpage_id)>0;   // NOLINT
  bool is_hit_shelf_live = RankKconfUtil::qcpxShelfLiveAdmitSubpageid()->count(subpage_id)>0;   // NOLINT 
  bool is_hit_rewarded = RankKconfUtil::qcpxRewardAdmitSubpageid()->count(subpage_id)>0; // NOLINT 
  bool enable_qcpx_rewarded = SPDM_enable_qcpx_rewarded(p_ctx->session_data_->get_spdm_ctx()); // NOLINT
  bool enable_qcpx_shelf_run_v2 = (SPDM_enable_qcpx_shelf_run_v2(p_ctx->session_data_->get_spdm_ctx())
                                  && !SPDM_enable_qcpx_shelf_run_v2_hold(p_ctx->session_data_->get_spdm_ctx()));  // NOLINT
  bool enable_qcpx_live_shelf_run_v2 = (SPDM_enable_qcpx_live_shelf_run_v2(p_ctx->session_data_->get_spdm_ctx())   // NOLINT
                                  && !SPDM_enable_qcpx_live_shelf_run_v2_hold(p_ctx->session_data_->get_spdm_ctx()));   // NOLINT


  // 关注页
  if (subpage_id == 100013100 || subpage_id == 100016771 || subpage_id == 10008001) {
    flag = true;
  }

    // 泛货架商品卡
  if (enable_qcpx_shelf_run_v2 && is_hit_shelf_photo) {
    if (p_ad->Is(AdFlag::is_photo_ad_inner)) {  // NOLINT
      flag = true;
      // 猜喜不生效
      if (p_ctx->session_data_->get_pos_manager_base().IsGuessYouLike() && SPDM_disable_qcpx_shelf_guess_run(p_ctx->session_data_->get_spdm_ctx())) {  // NOLINT
        flag = false;
      }
    }
  }
  // 泛货架直播卡
  if (enable_qcpx_live_shelf_run_v2 && is_hit_shelf_live) {
    if ((p_ad->Is(AdFlag::is_p2l_ad_inner) || p_ad->Is(AdFlag::is_live_ad_inner))) {  // NOLINT
      flag = true;
    }
  }

  // 激励
  if (enable_qcpx_rewarded && is_hit_rewarded) {
    flag = true;
  }

  return flag;
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> qcpx_p2l_soft_ctr_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // one model
        if (SPDM_enable_p2l_multi_predict(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // holdout
        if (SPDM_enable_qcpx_live_orderpay_request_reduce(p_ctx->session_data_->get_spdm_ctx()) &&
            (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
             SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()))) {
          return false;
        }
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= SPDM_enable_qcpx_p2l_ctr_model(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ctx->session_data_->get_is_thanos_request();
        // roas 流量下仅发折扣券流量预估
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) {
          flag &= SPDM_enable_qcpx_live_roas_use_rate_coupon(p_ctx->session_data_->get_spdm_ctx());
        }
        flag &= p_ad->Is(AdFlag::IsNativeAd);
        flag &= p_ad->Is(AdFlag::is_merchant_live);
        flag &= p_ad->Is(AdFlag::is_p2l_ad_inner);
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        IS_P2L(p_ad);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
         }
        return false;
      };
}

// @fandi QCPX 订单 CVR 拆软硬广
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_bspline_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // embq
        if (SPDM_enable_qcpx_live_cvr_merge_embq_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // merge cmd
        if (SPDM_enable_qcpx_live_cvr_merge_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        // holdout
        if (SPDM_enable_qcpx_live_orderpay_request_reduce(p_ctx->session_data_->get_spdm_ctx()) &&
            (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
             SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()))) {
          return false;
        }
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= SPDM_enable_qcpx_live_bspline_model(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ad->Is(AdFlag::is_merchant_live);
        flag &= p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner);
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (!SPDM_enable_qcpx_live_roas_use_reduce_coupon(p_ctx->session_data_->get_spdm_ctx())) {  // 发券优选 - 允许 ROAS 发满减券  // NOLINT
          if (SPDM_enable_qcpx_live_only_orderpay(p_ctx->session_data_->get_spdm_ctx())) {
            flag &= p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
          }
        } else {
          flag &= (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
                   p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                   p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
                   p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI);
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
         }
        return false;
      };
}

// @wanpengcheng QCPX 直播订单 CVR Taylor 软广
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_taylor_bspline_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        IS_NATIVE_AD(p_ad);
        // holdout
        if (SPDM_enable_qcpx_live_orderpay_request_reduce(p_ctx->session_data_->get_spdm_ctx()) &&
            (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
             SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()))) {
          return false;
        }
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= SPDM_enable_qcpx_live_taylor_bspline_model(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ad->Is(AdFlag::is_merchant_live);
        flag &= p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner);
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (SPDM_enable_qcpx_live_only_orderpay(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
         }
        return false;
      };
}

// @fandi 折扣券多头 CVR 拆软硬广
std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_cvr_disc_roas_multi_head_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // embq
        if (SPDM_enable_qcpx_live_cvr_merge_embq_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // merge cmd
        if (SPDM_enable_qcpx_live_cvr_merge_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        // 折扣券 roas 模型开关
        if (!SPDM_enable_qcpx_live_cvr_disc_roas_multi_head_model(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!QcpxCommonCondition(p_ad, p_ctx)) {
          return false;
        }
        // holdout
        if (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
            SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_merchant_live)) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_live_ad_inner) && !p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return false;
        }
        if (p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP &&
            p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP_MOBILE) {
          return false;
        }
        if (!SPDM_enable_qcpx_live_order_use_disc_coupon(p_ctx->session_data_->get_spdm_ctx())) {  // 发券优选 - 允许订单发折扣券  // NOLINT
          // roas
          if (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI) {
            return false;
          }
        } else {
          // roas order
          if (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI &&
              p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
            return false;
          }
        }
        // 仅生效直投开关打开，仅预估直投
        if (SPDM_enable_qcpx_live_rate_coupon_only_live(p_ctx->session_data_->get_spdm_ctx()) &&
            !p_ad->Is(AdFlag::is_live_ad_inner)) {
          return false;
        }
        return true;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_cvr_merge_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // embq
        if (SPDM_enable_qcpx_live_cvr_merge_embq_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // merge cmd
        if (!SPDM_enable_qcpx_live_cvr_merge_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        // holdout
        if (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
            SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_merchant_live)) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_live_ad_inner) && !p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return false;
        }
        if (p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP &&
            p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP_MOBILE) {
          return false;
        }
        // 仅生效直投开关打开，仅预估直投
        if (SPDM_enable_qcpx_live_rate_coupon_only_live(p_ctx->session_data_->get_spdm_ctx()) &&
            !p_ad->Is(AdFlag::is_live_ad_inner)) {
          return false;
        }
        // return true;
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
        }
        return false;
  };
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_live_cvr_merge_embq_condition() {  // NOLINT
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // merge cmd
        if (!SPDM_enable_qcpx_live_cvr_merge_embq_model_cmd(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        // holdout
        if (SPDM_enable_inner_coupon_holdout(p_ctx->session_data_->get_spdm_ctx()) ||
            SPDM_enable_inner_live_coupon_holdout(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_merchant_live)) {
          return false;
        }
        if (!p_ad->Is(AdFlag::is_live_ad_inner) && !p_ad->Is(AdFlag::is_p2l_ad_inner)) {
          return false;
        }
        if (p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP &&
            p_ad->get_account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP_MOBILE) {
          return false;
        }
        // 仅生效直投开关打开，仅预估直投
        if (SPDM_enable_qcpx_live_rate_coupon_only_live(p_ctx->session_data_->get_spdm_ctx()) &&
            !p_ad->Is(AdFlag::is_live_ad_inner)) {
          return false;
        }
        // return true;
        bool flag = QcpxCommonCondition(p_ad, p_ctx);
        flag &= SPDM_enable_qcpx_live_strategy_run(p_ctx->session_data_->get_spdm_ctx());
        flag &= p_ad->Is(AdFlag::is_merchant_product);
        if (!SPDM_enable_qcpx_live_model_use_t7_roi(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
        }
        if (!SPDM_enable_qcpx_live_model_use_storewide_roas(p_ctx->session_data_->get_spdm_ctx())) {
          flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS;
        }
        if (flag &&
        (SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("model") == 0 ||
         SPDM_tag_qcpx_live_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("rct") == 0)) {
          return true;  // NOLINT
        }
        return false;
  };
}

bool QcpxPhotoCommonCondition(const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
  if (p_ad->get_campaign_type() != kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
    return false;
  }
  if (p_ad->get_account_type() != AdEnum::ACCOUNT_ESP &&
      p_ad->get_account_type() != AdEnum::ACCOUNT_ESP_MOBILE) {
    return false;
  }
  if (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
      p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
      p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
    return false;
  }

  if (SPDM_enable_qcpx_photo_filter_u0(p_ctx->session_data_->get_spdm_ctx())) {
    auto& buyer_effective_type = p_ctx->session_data_->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();  // NOLINT
    if (buyer_effective_type.compare("U0") == 0) {
      return false;
    }
  }
  bool flag = QcpxCommonCondition(p_ad, p_ctx);
  flag &= SPDM_enable_qcpx_photo_strategy_run(p_ctx->session_data_->get_spdm_ctx());
  flag &= p_ad->Is(AdFlag::is_photo_ad_inner);
  flag &= p_ad->Is(AdFlag::is_merchant_product);
  flag &= p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI;
  if (flag && SPDM_tag_qcpx_photo_strategy(p_ctx->session_data_->get_spdm_ctx()).compare("arch") == 0) return true;  // NOLINT
  return false;
}

std::function<bool(const AdCommon *p_ad, CmdCuratorContext *p_ctx)> native_qcpx_photo_tata_condition() {
  return [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // 泛货架流量不请求模型
        if (SPDM_enable_shelf_qcpx_photo_elastic_taylor_bspline_model(p_ctx->session_data_->get_spdm_ctx())
           && p_ctx->session_data_->get_pos_manager_base().IsShelfMerchantTraffic()) {
          return false;
        }
        // 泰勒展开 - 分段线性模型
        if (!SPDM_enable_qcpx_photo_elastic_taylor_bspline_model(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        // 软硬广分拆 cmd key
        if (!SPDM_enable_qcpx_photo_split_cmd_key(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        IS_NATIVE_AD(p_ad);
        if (SPDM_enable_qcpx_photo_roas_unify_coupon_module(p_ctx->session_data_->get_spdm_ctx()) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)) {
          return false;
        }
        bool flag = true;
        flag &= QcpxPhotoCommonCondition(p_ad, p_ctx);
        return flag;
      };
}
}  // namespace inner_native_cmd
}  // namespace ad_rank
}  // namespace ks
