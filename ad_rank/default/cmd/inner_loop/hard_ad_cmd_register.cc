#include <memory>
#include <set>
#include <string>
#include <vector>

#include "teams/ad/ad_rank/default/cmd/inner_loop/hard_ad_cmd_register.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/default/cmd/inner_loop/hard_ad_cmd_condition.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"

namespace ks {
namespace ad_rank {
using ks::engine_base::PredictType;
using kuaishou::ad::AdActionType;
using kuaishou::ad::AdEnum;

#define IS_AMD_PHOTO(p_ad)                                     \
  {                                                            \
    if (p_ad->get_queue_type() != RankAdListType::NORMAL_PHOTO_AD) { \
      return false;                                            \
    }                                                          \
  }

#define IS_AMD_LIVE(p_ad)                                     \
  {                                                           \
    if (p_ad->get_queue_type() != RankAdListType::NORMAL_LIVE_AD) { \
      return false;                                           \
    }                                                         \
  }

void RegisterInnerUnionCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data);

AmdCmdRegister::AmdCmdRegister(CmdCuratorContext *context) : cmd_curator_ctx_(context) {}

CuratorTypeV2 *AmdCmdRegister::CreateCmdCurator() const {
  auto p_cmd_curator = new CuratorTypeV2(cmd_curator_ctx_);

  // 软硬融合 cmdKey
  RegisterInnerUnionCmd(p_cmd_curator, cmd_curator_ctx_->session_data_);

  // 硬广 cmdkey
  RegisterCtrCmd(p_cmd_curator, cmd_curator_ctx_->session_data_);
  RegisterCvrCmd(p_cmd_curator, cmd_curator_ctx_->session_data_);
  RegisterDeepCvrCmd(p_cmd_curator, cmd_curator_ctx_->session_data_);
  RegisterLtvCmd(p_cmd_curator, cmd_curator_ctx_->session_data_);
  RegisterCpmLtrCmd(p_cmd_curator, cmd_curator_ctx_->session_data_);
  RegisterAucCmd(p_cmd_curator, cmd_curator_ctx_->session_data_);
  RegisterOtherCmd(p_cmd_curator, cmd_curator_ctx_->session_data_);
  return p_cmd_curator;
}

void AmdCmdRegister::RegisterCtrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) const {
  if (p_cmd_curator == nullptr) return;
  // 短视频和直播公用的 cmd_key
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_server_client_show_rate,
                                    "ad_dsp_server_client_show_inner", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::ad_dsp_server_client_show_inner());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_server_show_ctr,
                                    "ad_dsp_server_show_item_imp_inner_ecom", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::ad_dsp_server_show_item_imp_inner_ecom());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_server_show_ctr,
                                    "ad_dsp_server_show_item_imp_inner_ecom_rewarded",
                                    CMD_SOURCE_AD_DSP),  // NOLINT
                            inner_hard_cmd::ad_dsp_server_show_item_imp_inner_ecom_rewarded());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_ctr, "app_ctr_cmd_ecom",
                                    CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::app_ctr_cmd_ecom());
  // 货架 photo 双列 photo_impression -> item_impression
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_ctr,
                                    "inner_photo_shelf_ctr_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_photo_shelf_ctr_cmd());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_ctr, "live_photo_ctr",
                                    CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::live_photo_ctr());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_server_show_ctr,
                                    "live_server_show_item_imp", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::live_server_show_item_imp());

  if (!SPDM_enable_item_live_audience_feed(session_data->get_spdm_ctx())) {
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                      "server_show_live_started_feed", CMD_SOURCE_AD_DSP),
                              inner_hard_cmd::server_show_live_started_feed());
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "server_show_live_started_feed_auc_base", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::server_show_live_started_feed_auc_base());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "server_show_live_started_feed_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::server_show_live_started_feed_auc_exp());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_ctr,
                                    "follow_tab_live_photo_ctr", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::follow_tab_live_photo_ctr());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_server_show_ctr,
                                    "ad_merchant_server_show_item_imp_ocpm", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::ad_merchant_server_show_item_imp_ocpm());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                    "server_show_live_started_slide", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::server_show_live_started_slide());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_live_audience,
                                    "server_show_live_started_slide_p5s", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::server_show_live_started_slide_p5s());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "server_show_live_started_slide_auc_base", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::server_show_live_started_slide_auc_base());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "server_show_live_started_slide_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::server_show_live_started_slide_auc_exp());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CTR, PredictType::PredictType_server_show_ctr,
                                    "slide_live_server_show_item_imp", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::slide_live_server_show_item_imp());
  if (SPDM_enable_aigc_label(session_data->get_spdm_ctx()) && SPDM_closeStyleRank()
    && !ks::ad_base::IsCplRequest(session_data->get_sub_page_id())) {
    static const std::vector<PredictType> inner_title_select_ctr_style_predicts = {
      PredictType::Predict_inner_title_select_ctr_style0,
      PredictType::Predict_inner_title_select_ctr_style1,
      PredictType::Predict_inner_title_select_ctr_style2,
      PredictType::Predict_inner_title_select_ctr_style3,
      PredictType::Predict_inner_title_select_ctr_style4,
      PredictType::Predict_inner_title_select_ctr_style5,
      PredictType::Predict_inner_title_select_ctr_style6,
      PredictType::Predict_inner_title_select_ctr_style7,
      PredictType::Predict_inner_title_select_ctr_style8,
      PredictType::Predict_inner_title_select_ctr_style9,
      PredictType::Predict_inner_title_select_ctr_style10,
      PredictType::Predict_inner_title_select_ctr_style11,
      PredictType::Predict_inner_title_select_ctr_style12,
      PredictType::Predict_inner_title_select_ctr_style13};
    static const std::vector<engine_base::RType> inner_title_select_ctr_style_rtypes(
        inner_title_select_ctr_style_predicts.size(),
        engine_base::RType::CTR);
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(inner_title_select_ctr_style_rtypes,
          inner_title_select_ctr_style_predicts,
          "ad_yellow_car_title_select", CMD_SOURCE_AD_DSP),
        [](const AdCommon* p_ad, CmdCuratorContext* p_ctx){
          return p_ad->Is(AdFlag::is_style_request_ad)
            && p_ad->Is(AdFlag::is_inner_loop_ad) && p_ad->get_merchant_product_id() != 0;
        });
  }
}

void AmdCmdRegister::RegisterCvrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) const {
  if (p_cmd_curator == nullptr) return;
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_c1_order_paid,
                                    "c1_nebula_order_paied_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::c1_nebula_order_paied_cmd());
  //订单模型主动探索
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          {engine_base::RType::UNCATEGORIZED, engine_base::RType::UNCATEGORIZED,
          engine_base::RType::UNCATEGORIZED, engine_base::RType::UNCATEGORIZED},
          {PredictType::PredictType_c1_order_paid, PredictType::PredictType_order_paid_explore,
          PredictType::PredictType_order_paid_item_avg, PredictType::PredictType_order_paid_model_score},
          "c1_nebula_order_paied_explore_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::c1_nebula_order_paied_explore_cmd());

  // 猜喜 item impression -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_c1_order_paid,
                                    "order_paied_for_guess_you_like_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::order_paied_for_guess_you_like_cmd());

  // 商城 item impression -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_c1_order_paid,
                                    "order_paied_for_mall_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::order_paied_for_mall_cmd());

  // 赚钱 item impression -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_c1_order_paid,
                                  "order_paied_for_zhuanqian_cmd", CMD_SOURCE_AD_DSP),
                          inner_hard_cmd::order_paied_for_zhuanqian_cmd());

  // 买首 item impression -> order paid
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_c1_order_paid,
                                    "order_paied_for_buyer_home_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::order_paied_for_buyer_home_cmd());

  // 货架 item impression -> order paid，分位次
  static const std::vector<engine_base::PredictType> inner_shelf_order_pay_cvr_pos_pts = {
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos1,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos2,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos3,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos4,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos5,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos6,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos7,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos8,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos9,
      PredictType::PredictType_inner_shelf_order_pay_cvr_pos10};
  static const std::vector<engine_base::RType> inner_shelf_order_pay_cvr_pos_rts(
      inner_shelf_order_pay_cvr_pos_pts.size(),
      engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(inner_shelf_order_pay_cvr_pos_rts, inner_shelf_order_pay_cvr_pos_pts,
                                    "order_paied_for_gyl_pos_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::order_paied_for_gyl_pos_cmd());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(inner_shelf_order_pay_cvr_pos_rts, inner_shelf_order_pay_cvr_pos_pts,
                                    "order_paied_for_mall_pos_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::order_paied_for_mall_pos_cmd());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(inner_shelf_order_pay_cvr_pos_rts, inner_shelf_order_pay_cvr_pos_pts,
                                    "order_paied_for_bh_pos_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::order_paied_for_bh_pos_cmd());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_c1_order_paid,
                                    "c1_order_paid_rate", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::c1_order_paid_rate());

  // 移动版进硬广订单
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_c1_order_paid,
                                    "native_photo_pay", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::native_photo_pay());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          {engine_base::RType::UNCATEGORIZED, engine_base::RType::UNCATEGORIZED,
          engine_base::RType::UNCATEGORIZED, engine_base::RType::UNCATEGORIZED},
          {PredictType::PredictType_c1_order_paid, PredictType::PredictType_order_paid_explore,
          PredictType::PredictType_order_paid_item_avg, PredictType::PredictType_order_paid_model_score},
          "c1_order_paid_rate_explore", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::c1_order_paid_rate_explore());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_cvr,
                                    "effective_play_flash",
                                    CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::effective_play());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_cvr,
                                    "ad_dsp_cvr_cmd_ecom", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::ad_dsp_cvr_cmd_ecom());
  // 本地推短视频组件点击 item impression -> item click
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_lsp_photo_item_click,
                                    "lsp_photo_item_click", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::lsp_photo_item_click());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_item_impression_wtr,
                                    "item_impression_wtr", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::item_impression_wtr());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_live_p3s_wtr,
                                    "live_p3s_wtr", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::live_p3s_wtr());
  static const std::vector<PredictType> photo_to_live_rate_multi_predicts = {
      PredictType::PredictType_cvr,
      PredictType::PredictType_p2l_qcpx_c0,
      PredictType::PredictType_p2l_qcpx_c1,
      PredictType::PredictType_p2l_qcpx_d0,
      PredictType::PredictType_p2l_qcpx_d1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak2};
  static const std::vector<engine_base::RType> photo_to_live_rate_multi_rtypes(
      photo_to_live_rate_multi_predicts.size(),
      engine_base::RType::CVR);
  static const std::vector<PredictType> photo_to_live_rate_multi_predicts_auc_base = {
      PredictType::PredictType_auc_model_base,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1};
  static const std::vector<engine_base::RType> photo_to_live_rate_multi_rtypes_auc_base(
      photo_to_live_rate_multi_predicts_auc_base.size(),
      engine_base::RType::AUC_BASE);
  static const std::vector<PredictType> photo_to_live_rate_multi_predicts_auc_exp = {
      PredictType::PredictType_auc_model_exp,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1,
      PredictType::PredictType_p2l_bak1};
  static const std::vector<engine_base::RType> photo_to_live_rate_multi_rtypes_auc_exp(
      photo_to_live_rate_multi_predicts_auc_exp.size(),
      engine_base::RType::AUC_EXP);
  if (SPDM_enable_p2l_multi_predict(session_data->get_spdm_ctx())) {
    if (!SPDM_enable_second_stage_predict(session_data->get_spdm_ctx()) ||
        (!SPDM_enable_skip_out_emb(session_data->get_spdm_ctx()) &&
         session_data->get_sub_page_id() != 10011001 && session_data->get_sub_page_id() != 11001001)) {
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(photo_to_live_rate_multi_rtypes, photo_to_live_rate_multi_predicts,
                                        "photo_to_live_rate", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(photo_to_live_rate_multi_rtypes_auc_base,
                                        photo_to_live_rate_multi_predicts_auc_base,
                                        "photo_to_live_rate_auc_base", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(photo_to_live_rate_multi_rtypes_auc_exp,
                                        photo_to_live_rate_multi_predicts_auc_exp,
                                        "photo_to_live_rate_auc_exp", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate_auc_exp());
    } else if ((session_data->get_sub_page_id() != 10011001 && session_data->get_sub_page_id() != 11001001)) {
      // 一阶段优化开关打开，并且不属于单列的页面，need_predict_embedding = 0
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        photo_to_live_rate_multi_rtypes, photo_to_live_rate_multi_predicts,
                                        {}, "photo_to_live_rate", CMD_SOURCE_AD_DSP, 0),
          inner_hard_cmd::photo_to_live_rate());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        photo_to_live_rate_multi_rtypes_auc_base,
                                        photo_to_live_rate_multi_predicts_auc_base, {},
                                        "photo_to_live_rate_auc_base", CMD_SOURCE_AD_DSP, 0),
          inner_hard_cmd::photo_to_live_rate_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        photo_to_live_rate_multi_rtypes_auc_exp,
                                        photo_to_live_rate_multi_predicts_auc_exp, {},
                                        "photo_to_live_rate_auc_exp", CMD_SOURCE_AD_DSP, 0),
          inner_hard_cmd::photo_to_live_rate_auc_exp());
    } else {
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
              photo_to_live_rate_multi_rtypes, photo_to_live_rate_multi_predicts,
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer},
              "photo_to_live_rate", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
              photo_to_live_rate_multi_rtypes_auc_base, photo_to_live_rate_multi_predicts_auc_base,
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer_auc_base},
              "photo_to_live_rate_auc_base", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG,
              photo_to_live_rate_multi_rtypes_auc_exp, photo_to_live_rate_multi_predicts_auc_exp,
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer_auc_exp},
              "photo_to_live_rate_auc_exp", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate_auc_exp());
    }
  } else {
    if (!SPDM_enable_second_stage_predict(session_data->get_spdm_ctx()) ||
        (!SPDM_enable_skip_out_emb(session_data->get_spdm_ctx()) &&
         session_data->get_sub_page_id() != 10011001 && session_data->get_sub_page_id() != 11001001)) {
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_cvr,
                                        "photo_to_live_rate", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                        "photo_to_live_rate_auc_base", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                        "photo_to_live_rate_auc_exp", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate_auc_exp());
    } else if ((session_data->get_sub_page_id() != 10011001 && session_data->get_sub_page_id() != 11001001)) {
      // 一阶段优化开关打开，并且不属于单列的页面，need_predict_embedding = 0
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        {engine_base::RType::CVR}, {PredictType::PredictType_cvr}, {},
                                        "photo_to_live_rate", CMD_SOURCE_AD_DSP, 0),
          inner_hard_cmd::photo_to_live_rate());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                        {engine_base::RType::AUC_BASE},
                                        {PredictType::PredictType_auc_model_base}, {},
                                        "photo_to_live_rate_auc_base", CMD_SOURCE_AD_DSP, 0),
          inner_hard_cmd::photo_to_live_rate_auc_base());
      p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                                     ks::engine_base::CmdStrategyTag::DEFAULT_TAG,
                                     {engine_base::RType::AUC_EXP}, {PredictType::PredictType_auc_model_exp},
                                     {}, "photo_to_live_rate_auc_exp", CMD_SOURCE_AD_DSP, 0),
                                 inner_hard_cmd::photo_to_live_rate_auc_exp());
    } else {
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG, {engine_base::RType::CVR},
              {PredictType::PredictType_cvr},
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer},
              "photo_to_live_rate", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG, {engine_base::RType::AUC_BASE},
              {PredictType::PredictType_auc_model_base},
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer_auc_base},
              "photo_to_live_rate_auc_base", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate_auc_base());
      p_cmd_curator->RegisterCmd(
          new engine_base::CmdWrapperV2(
              ks::engine_base::CmdStrategyTag::FIRST_STAGE_PREDICT_TAG, {engine_base::RType::AUC_EXP},
              {PredictType::PredictType_auc_model_exp},
              {engine_base::PredictEmbeddingType::PredictEmbeddingType_live_audience_toplayer_auc_exp},
              "photo_to_live_rate_auc_exp", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo_to_live_rate_auc_exp());
    }
  }
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_cvr,
                                    "photo_to_live_rate_thanos", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::photo_to_live_rate_thanos());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_ntr,
                                    "nebula_ntr_model_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::nebula_ntr_model_cmd());
  // 直播 feed impression -> p60s
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_playtime,
                                    "live_playtime_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::live_playtime_cmd());

  // --- 粉条进硬广的几个模型请求兼容
  // 私信消息发送: AD_ITEM_IMPRESSION -> EVENT_PRIVATE_MESSAGE_SENT
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_leads_submit,
                                    "photo_private_message_sent_cmd", CMD_SOURCE_AD_DSP),
                                    inner_hard_cmd::photo_private_message_sent_cmd());
  // 作品线索提交: AD_ITEM_IMPRESSION -> LEADS_SUBMIT
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_leads_submit,
                                    "photo_leads_submit_cmd", CMD_SOURCE_AD_DSP),
                                    inner_hard_cmd::photo_leads_submit_cmd());
  // 作品表单: AD_ITEM_IMPRESSION -> AD_LANDING_PAGE_FORM_SUBMITTED (EVENT_FORM_SUBMIT)
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_leads_submit,
                                    "photo_form_submit_lps_cmd", CMD_SOURCE_AD_DSP),
                                    inner_hard_cmd::photo_form_submit_lps_cmd());
  // 400 电话拨打: AD_ITEM_IMPRESSION -> EVENT_PHONE_CALL
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_leads_submit,
                                    "fanstop_photo_phone_call_cmd", CMD_SOURCE_AD_DSP),
                                    inner_hard_cmd::fanstop_photo_phone_call());
  // 粉条-直播线索提交 LEADS_SUBMIT / AD_LANDING_PAGE_FORM_SUBMITTED
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::CVR, PredictType::PredictType_leads_submit,
                                  "live_clue_clk_cmd", CMD_SOURCE_AD_DSP),
                                  inner_hard_cmd::fanstop_live_clue_clk_cmd());
}

void AmdCmdRegister::RegisterDeepCvrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) const {
  if (p_cmd_curator == nullptr) return;
}

void AmdCmdRegister::RegisterLtvCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) const {
  if (p_cmd_curator == nullptr) return;
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_merchant_ltv,
                                    "ad_dsp_merchant_reco_ltv", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::ad_dsp_merchant_reco_ltv());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_c1_order_paid,
                                    "neixunhuan_photo_roi_two_stage_cvr", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::neixunhuan_photo_roi_two_stage_cvr());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_mix_unify_gpm,
                                    "neixunhuan_photo_roi_gpm", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::neixunhuan_photo_roi_gpm());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_live_p3s_ltv,
                                    "ad_dsp_live_p3s_ltv", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::ad_dsp_live_p3s_ltv());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_inner_live_roas,
                                    "inner_hard_live_roas", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_roas());

  static const std::vector<PredictType> inner_live_roas_multi_predicts = {
      PredictType::PredictType_inner_live_roas,
      PredictType::PredictType_inner_live_roas_hard,
      PredictType::PredictType_inner_live_roas_front,
      PredictType::PredictType_inner_live_roas_end,
      PredictType::PredictType_inner_live_roi_gmv_front,
      PredictType::PredictType_inner_live_roi_gmv_end,
      PredictType::PredictType_inner_live_roi_pay_front,
      PredictType::PredictType_inner_live_roi_pay_end,
      PredictType::PredictType_inner_live_roi_ext};
  static const std::vector<engine_base::RType> live_roas_hard_predict_types(
                       inner_live_roas_multi_predicts.size(),
                       engine_base::RType::LTV);
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                      live_roas_hard_predict_types, inner_live_roas_multi_predicts,
                                    "inner_hard_live_roas_multi", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_roas_multi());
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                      live_roas_hard_predict_types, inner_live_roas_multi_predicts,
                                    "inner_hard_live_roas_multi_thanos", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_roas_multi_thanos());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::LTV, PredictType::PredictType_mix_unify_gpm,
                                    "inner_hard_live_gpm", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_gpm());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          engine_base::RType::LTV,
          PredictType::PredictType_inner_live_roas_7days_0_2h,
          "inner_hard_live_roas_7days_0_2h", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_roas_7days_0_2h());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          engine_base::RType::LTV,
          PredictType::PredictType_inner_live_roas_7days_2h_3d,
          "inner_hard_live_roas_7days_2h_3d", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_roas_7days_2h_3d());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          engine_base::RType::LTV,
          PredictType::PredictType_inner_live_roas_7days_3d_7d,
          "inner_hard_live_roas_7days_3d_7d", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_roas_7days_3d_7d());

  static const std::vector<PredictType> inner_live_atom_roi_paycnt_multi_predicts2 = {
      PredictType::PredictType_order_paid,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3,
      PredictType::PredictType_inner_live_atom_paycnt_bak4,
      PredictType::PredictType_inner_live_atom_paycnt_bak5,
      PredictType::PredictType_inner_live_atom_paycnt_bak6,
      PredictType::PredictType_inner_live_atom_paycnt_bak7,
      PredictType::PredictType_inner_live_atom_paycnt_bak8,
      PredictType::PredictType_inner_live_atom_paycnt_bak9,
      PredictType::PredictType_inner_live_atom_paycnt_bak10,
      PredictType::PredictType_inner_live_atom_paycnt_bak11,
      PredictType::PredictType_inner_live_atom_paycnt_bak12,
      PredictType::PredictType_inner_live_atom_paycnt_bak13,
      PredictType::PredictType_inner_live_atom_paycnt_bak14,
      PredictType::PredictType_inner_live_atom_paycnt_bak15,
      PredictType::PredictType_inner_live_atom_paycnt_bak16};
  static const std::vector<engine_base::RType> inner_live_atom_roi_paycnt_multi_rtypes2(
                       inner_live_atom_roi_paycnt_multi_predicts2.size(),
                       engine_base::RType::CVR);
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                      inner_live_atom_roi_paycnt_multi_rtypes2, inner_live_atom_roi_paycnt_multi_predicts2,
                                    "inner_hard_live_roi_paycnt_atom_qcpx", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_gmv_atom_qcpx());
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                      inner_live_atom_roi_paycnt_multi_rtypes2, inner_live_atom_roi_paycnt_multi_predicts2,
                                    "inner_hard_live_roi_paycnt_atom_qcpx_thanos", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_gmv_atom_qcpx_thanos());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_roi_paycnt_multi_rtypes2,
          inner_live_atom_roi_paycnt_multi_predicts2,
          "inner_hard_live_roas_7days_0_2h_roi_paycnt_atom_qcpx", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_roas_7days_0_2h_gmv_atom_qcpx());

  static const std::vector<PredictType> inner_live_atom_roi_paycnt_multi_predicts = {
      PredictType::PredictType_order_paid,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3};
  static const std::vector<engine_base::RType> inner_live_atom_roi_paycnt_multi_rtypes(
                       inner_live_atom_roi_paycnt_multi_predicts.size(),
                       engine_base::RType::CVR);

  static const std::vector<PredictType> inner_live_atom_roi_gmv_multi_predicts = {
      PredictType::PredictType_inner_live_atom_gmv_all,
      PredictType::PredictType_inner_live_atom_gmv_front,
      PredictType::PredictType_inner_live_atom_gmv_end,
      PredictType::PredictType_inner_live_atom_gmv_indirect};
  static const std::vector<engine_base::RType> inner_live_atom_roi_gmv_multi_rtypes(
                       inner_live_atom_roi_gmv_multi_predicts.size(),
                       engine_base::RType::LTV);
  p_cmd_curator->RegisterCmd( new engine_base::CmdWrapperV2(
        inner_live_atom_roi_paycnt_multi_rtypes, inner_live_atom_roi_paycnt_multi_predicts,
        "inner_hard_live_roi_paycnt_atom_multi", CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          if (p_ctx->enable_inner_live_qcpx_roas) {
            return false;
          } else if (p_ad->get_bid_assist_type() && p_ctx->enable_inner_live_atomization_net_roas) {
            return inner_hard_cmd::inner_hard_live_net_gmv_atom_multi()(p_ad, p_ctx);
          } else if (p_ctx->enable_live_order_item_live_split) {
            return inner_hard_cmd::live_roi_paycnt_atom_multi_item_p2l()(p_ad, p_ctx);
          } else {
            return inner_hard_cmd::inner_hard_live_gmv_atom_multi()(p_ad, p_ctx);
          }
        });
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
        inner_live_atom_roi_paycnt_multi_rtypes,
        inner_live_atom_roi_paycnt_multi_predicts,
        "inner_hard_live_roi_paycnt_atom_multi_thanos",
        CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          if (p_ctx->enable_inner_live_qcpx_roas) {
            return false;
          } else if (p_ad->get_bid_assist_type() && p_ctx->enable_inner_live_atomization_net_roas) {
            return inner_hard_cmd::inner_hard_live_net_gmv_atom_multi_thanos()(p_ad, p_ctx);
          } else if (p_ctx->enable_live_order_item_live_split) {
            return inner_hard_cmd::live_roi_paycnt_atom_multi_item_p2l_thanos()(p_ad, p_ctx);
          } else {
            return inner_hard_cmd::inner_hard_live_gmv_atom_multi_thanos()(p_ad, p_ctx);
          }
        });
  p_cmd_curator->RegisterCmd( new engine_base::CmdWrapperV2(
        inner_live_atom_roi_paycnt_multi_rtypes, inner_live_atom_roi_paycnt_multi_predicts,
        "inner_hard_live_roi_paycnt_atom_multi_item_live", CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
            return inner_hard_cmd::live_roi_paycnt_atom_multi_item_live()(p_ad, p_ctx);
        });
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
        inner_live_atom_roi_paycnt_multi_rtypes,
        inner_live_atom_roi_paycnt_multi_predicts,
        "inner_hard_live_roi_paycnt_atom_multi_item_live_thanos",
        CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
           return inner_hard_cmd::live_roi_paycnt_atom_multi_item_live_thanos()(p_ad, p_ctx);
        });

  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                      inner_live_atom_roi_gmv_multi_rtypes, inner_live_atom_roi_gmv_multi_predicts,
                                    "inner_hard_live_gmv_atom_multi", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_gmv_atom_multi());

  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                      inner_live_atom_roi_gmv_multi_rtypes, inner_live_atom_roi_gmv_multi_predicts,
                                    "inner_hard_live_gmv_atom_multi_thanos", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_gmv_atom_multi_thanos());

  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                      inner_live_atom_roi_gmv_multi_rtypes, inner_live_atom_roi_gmv_multi_predicts,
                                    "inner_hard_live_net_gmv_atom_multi", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_net_gmv_atom_multi());

  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(
                      inner_live_atom_roi_gmv_multi_rtypes, inner_live_atom_roi_gmv_multi_predicts,
                                    "inner_hard_live_net_gmv_atom_multi_thanos", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_net_gmv_atom_multi_thanos());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_roi_paycnt_multi_rtypes,
          inner_live_atom_roi_paycnt_multi_predicts,
          "inner_hard_live_roas_7days_0_2h_roi_paycnt_atom_multi", CMD_SOURCE_AD_DSP),
          [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
            if (p_ctx->enable_inner_live_qcpx_t7roas) {
              return false;
            } else if (p_ctx->enable_live_order_item_live_split) {
              return inner_hard_cmd::live_t7roi_0_2h_paycnt_atom_multi_item_p2l()(p_ad, p_ctx);
            } else {
              return inner_hard_cmd::inner_hard_live_roas_7days_0_2h_gmv_atom_multi()(p_ad, p_ctx);
            }
          });
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_roi_paycnt_multi_rtypes,
          inner_live_atom_roi_paycnt_multi_predicts,
          "inner_hard_t7roi_0_2h_paycnt_atom_multi_item_live", CMD_SOURCE_AD_DSP),
          [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
            if (p_ctx->enable_inner_live_qcpx_t7roas) {
              return false;
            } else {
              return inner_hard_cmd::live_t7roi_0_2h_paycnt_atom_multi_item_live()(p_ad, p_ctx);
            }
          });
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_roi_gmv_multi_rtypes,
          inner_live_atom_roi_gmv_multi_predicts,
          "inner_hard_live_roas_7days_0_2h_gmv_atom_multi", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_hard_live_roas_7days_0_2h_gmv_atom_multi());
}

void AmdCmdRegister::RegisterCpmLtrCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) const {
  if (p_cmd_curator == nullptr) return;
}

void AmdCmdRegister::RegisterAucCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) const {
  if (p_cmd_curator == nullptr) return;
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "merchant_video_cvr_auc_base", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::merchant_video_cvr_auc_base());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "server_show_merchant_follow_ratio_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::server_show_merchant_follow_ratio_auc_exp());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "merchant_video_cvr_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::merchant_video_cvr_auc_exp());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "ad_dsp_live_p3s_ltv_auc_base", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::ad_dsp_live_p3s_ltv_auc_base());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "ad_dsp_live_p3s_ltv_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::ad_dsp_live_p3s_ltv_auc_exp());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "merchant_photo_roas_cvr_auc_base", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::merchant_photo_roas_cvr_auc_base());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "merchant_photo_roas_cvr_auc_exp", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::merchant_photo_roas_cvr_auc_exp());
}

void AmdCmdRegister::RegisterOtherCmd(CuratorTypeV2 *p_cmd_curator, ContextData* session_data) const {
  if (p_cmd_curator == nullptr) return;
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_c1_merchant_follow, "c1_merchant_follow_rate",
                                    CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::c1_merchant_follow_rate());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_inner_loop_ecpc_imp_conv_rate,
                                    "rank_imp2_conv_inner_loop_mcda",
                                    CMD_SOURCE_AD_DSP),  // NOLINT
                            inner_hard_cmd::rank_imp2_conv_inner_loop_mcda());
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_merchant_follow_ocpm,
                                    "server_show_merchant_follow_ratio", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::server_show_merchant_follow_ratio());

  static const std::vector<engine_base::PredictType> incentive_quit_rate_pts = {
      PredictType::PredictType_incentive_quit_rate_0,
      PredictType::PredictType_incentive_quit_rate_1,
      PredictType::PredictType_incentive_quit_rate_2,
      PredictType::PredictType_incentive_quit_rate_3,
      PredictType::PredictType_incentive_quit_rate_4,
      PredictType::PredictType_incentive_quit_rate_5,
      PredictType::PredictType_incentive_quit_rate_6,
      PredictType::PredictType_incentive_quit_rate_7,
      PredictType::PredictType_incentive_quit_rate_8,
      PredictType::PredictType_incentive_quit_rate_9,
      PredictType::PredictType_incentive_quit_rate_10,
      PredictType::PredictType_incentive_quit_rate_11,
      PredictType::PredictType_incentive_quit_rate_12,
      PredictType::PredictType_incentive_quit_rate_13,
      PredictType::PredictType_incentive_quit_rate_14};
  static const std::vector<engine_base::RType> incentive_quit_rate_rts(
      incentive_quit_rate_pts.size(), engine_base::RType::UNCATEGORIZED);
  if (session_data->get_is_incentive_explore()) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                    "incentive_quit_rate_rct", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::incentive_quit_rate_rct_cmd());
  } else {
    auto incentive_costly_sub_page_ids = RankKconfUtil::incentiveCostlySubPageIds();
    if (incentive_costly_sub_page_ids->count(session_data->get_sub_page_id()) == 0 &&
        SPDM_enable_incentive_iaa_quit_rate_cmd(session_data->get_spdm_ctx())) {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                      "incentive_quit_rate_iaa", CMD_SOURCE_AD_DSP),
        inner_hard_cmd::incentive_quit_rate_cmd());
    } else {
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(incentive_quit_rate_rts, incentive_quit_rate_pts,
                                      "incentive_quit_rate", CMD_SOURCE_AD_DSP),
        inner_hard_cmd::incentive_quit_rate_cmd());
    }
  }

  static const std::vector<engine_base::PredictType> incentive_deep_quit_rate_pts = {
      PredictType::PredictType_incentive_deep_quit_rate_0,
      PredictType::PredictType_incentive_deep_quit_rate_1,
      PredictType::PredictType_incentive_deep_quit_rate_2,
      PredictType::PredictType_incentive_deep_quit_rate_3,
      PredictType::PredictType_incentive_deep_quit_rate_4,
      PredictType::PredictType_incentive_deep_quit_rate_5};
  static const std::vector<engine_base::RType> incentive_deep_quit_rate_rts(
      incentive_deep_quit_rate_pts.size(), engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(incentive_deep_quit_rate_rts, incentive_deep_quit_rate_pts,
                                    "incentive_deep_quit_rate", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::incentive_deep_quit_rate_cmd());

  static const std::vector<engine_base::PredictType> incentive_deep_task_uplift_pts = {
      PredictType::PredictType_incentive_deep_task_uplift_cvr_0,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_1,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_2,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_3};
  static const std::vector<engine_base::RType> incentive_deep_task_uplift_rts(
                                                  incentive_deep_task_uplift_pts.size(),
                                                  engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(incentive_deep_task_uplift_rts, incentive_deep_task_uplift_pts,
                                    "incentive_deep_task_uplift_cvr", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::incentive_deep_task_uplift_cvr_cmd());

  if (SPDM_enable_rnd_cold_start_cmd_split(cmd_curator_ctx_->session_data_->get_spdm_ctx()) &&
      !SPDM_enable_rnd_cold_start_cmd_split_new_photo(cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_rnd_explore_score,
                                    "ad_dsp_rnd_cold_start_inner", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad);
        if (!p_ctx->session_data_->get_is_search_request()) {
          return true;
        }
        return false;
      });
  } else {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_rnd_explore_score,
                                    "ad_dsp_imp_conv_diversity_rnd_explore_inner", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (!p_ad->Is(AdFlag::IsHardAd)) {
          return false;
        }
        IS_AMD_PHOTO(p_ad);
        if (!p_ctx->session_data_->get_is_search_request()) {
          return true;
        }
        return false;
      });
    if (SPDM_enable_rnd_cold_start_cmd_split_new_photo(
        cmd_curator_ctx_->session_data_->get_spdm_ctx())) {
      // 旧素材还是用老 cmd 新素材用新 cmd
      p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                      PredictType::PredictType_rnd_explore_score,
                                      "ad_dsp_rnd_cold_start_inner", CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          if (!p_ad->Is(AdFlag::IsHardAd) || !p_ad->Is(AdFlag::is_new_model_creative)) {
            return false;
          }
          IS_AMD_PHOTO(p_ad);
          if (!p_ctx->session_data_->get_is_search_request()) {
            return true;
          }
          return false;
        });
    }
  }

  static const std::vector<PredictType> user_experience_playtime_predicts = {
      PredictType::PredictType_user_experience_score1,
      PredictType::PredictType_user_experience_score2,
      PredictType::PredictType_user_experience_score3,
      PredictType::PredictType_user_experience_score4,
      PredictType::PredictType_user_experience_score5,
      PredictType::PredictType_user_experience_score6,
      PredictType::PredictType_user_experience_score7,
      PredictType::PredictType_user_experience_score8};
  static const std::vector<engine_base::RType> predict_types(user_experience_playtime_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(predict_types, user_experience_playtime_predicts,
                                        "inner_ad_user_experience_cmd", CMD_SOURCE_AD_DSP),
                          inner_hard_cmd::inner_ad_user_experience_cmd());

  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED, PredictType::PredictType_negative_ratio,
                                  "inner_ad_negative_ratio_cmd", CMD_SOURCE_AD_DSP),
                          inner_hard_cmd::inner_ad_negative_ratio_cmd());

  if (!SPDM_enable_jump_out_rate_model_inner_combine(session_data->get_spdm_ctx())) {
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED, PredictType::PredictType_jump_out_rate,
                                    "inner_ad_jump_out_rate_cmd", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::inner_ad_jump_out_rate_cmd());
  }

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    engine_base::PredictType::PredictType_invo_traffic_score,
                                    "ad_dsp_invo_traffic_score_inner",
                                    CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::ad_dsp_invo_traffic_score_inner());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED, PredictType::PredictType_live_pay_rate,
                                    "live_pay_rate", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::live_pay_rate());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    engine_base::PredictType::PredictType_inner_explore_sim,
                                    "ad_dsp_inner_explore_sim_cmd_inner",
                                    CMD_SOURCE_AD_DSP),
      inner_hard_cmd::ad_dsp_inner_explore_sim_condition());

  static const std::vector<PredictType> inner_live_atom_order_multi_predicts2_live = {
      PredictType::PredictType_live_pay_rate,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3,
      PredictType::PredictType_inner_live_atom_paycnt_bak4,
      PredictType::PredictType_inner_live_atom_paycnt_bak5,
      PredictType::PredictType_inner_live_atom_paycnt_bak6,
      PredictType::PredictType_inner_live_atom_paycnt_bak7,
      PredictType::PredictType_inner_live_atom_paycnt_bak8,
      PredictType::PredictType_inner_live_atom_paycnt_bak9,
      PredictType::PredictType_inner_live_atom_paycnt_bak10,
      PredictType::PredictType_inner_live_atom_paycnt_bak11,
      PredictType::PredictType_inner_live_atom_paycnt_bak12,
      PredictType::PredictType_inner_live_atom_paycnt_bak13,
      PredictType::PredictType_inner_live_atom_paycnt_bak14,
      PredictType::PredictType_inner_live_atom_paycnt_bak15,
      PredictType::PredictType_inner_live_atom_paycnt_bak16};
  static const std::vector<engine_base::RType> inner_live_atom_order_multi_rtypes2_live(
                       inner_live_atom_order_multi_predicts2_live.size(),
                       engine_base::RType::CVR);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_order_multi_rtypes2_live,
          inner_live_atom_order_multi_predicts2_live,
          "live_pay_rate_atom_qcpx", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::live_pay_rate_atom_qcpx());

  static const std::vector<PredictType> inner_live_atom_order_multi_predicts2_photo2live = {
      PredictType::PredictType_photo2live_pay_rate,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3,
      PredictType::PredictType_inner_live_atom_paycnt_bak4,
      PredictType::PredictType_inner_live_atom_paycnt_bak5,
      PredictType::PredictType_inner_live_atom_paycnt_bak6,
      PredictType::PredictType_inner_live_atom_paycnt_bak7,
      PredictType::PredictType_inner_live_atom_paycnt_bak8,
      PredictType::PredictType_inner_live_atom_paycnt_bak9,
      PredictType::PredictType_inner_live_atom_paycnt_bak10,
      PredictType::PredictType_inner_live_atom_paycnt_bak11,
      PredictType::PredictType_inner_live_atom_paycnt_bak12,
      PredictType::PredictType_inner_live_atom_paycnt_bak13,
      PredictType::PredictType_inner_live_atom_paycnt_bak14,
      PredictType::PredictType_inner_live_atom_paycnt_bak15,
      PredictType::PredictType_inner_live_atom_paycnt_bak16};
  static const std::vector<engine_base::RType> inner_live_atom_order_multi_rtypes2_photo2live(
                       inner_live_atom_order_multi_predicts2_live.size(),
                       engine_base::RType::CVR);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_order_multi_rtypes2_photo2live,
          inner_live_atom_order_multi_predicts2_photo2live,
          "photo2live_pay_rate_atom_qcpx", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo2live_pay_rate_atom_qcpx());

  static const std::vector<PredictType> inner_live_atom_order_multi_predicts_live = {
      PredictType::PredictType_live_pay_rate,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3};
  static const std::vector<engine_base::RType> inner_live_atom_order_multi_rtypes_live(
                       inner_live_atom_order_multi_predicts_live.size(),
                       engine_base::RType::UNCATEGORIZED);
  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_order_multi_rtypes_live,
          inner_live_atom_order_multi_predicts_live,
          "live_pay_rate_atom_multi2", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::live_pay_rate_atom_multi2());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED, PredictType::PredictType_live_pay_rate,
                                    "live_pay_rate_single_tab", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::live_pay_rate_single_tab());

  // locallife live order_paid for ROAS
  if (SPDM_enable_locallife_live_roas_use_locallife_model(session_data->get_spdm_ctx())) {
    // paycnt infer
    static const std::vector<PredictType> locallife_roas_order_paid_predicts = {
      PredictType::PredictType_order_paid,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end};
    static const std::vector<engine_base::RType> locallife_roas_order_paid_multi_rtypes(
      locallife_roas_order_paid_predicts.size(), engine_base::RType::UNCATEGORIZED);
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(locallife_roas_order_paid_multi_rtypes,
                                    locallife_roas_order_paid_predicts,
                                    "lsp_live_pay_rate_roas", CMD_SOURCE_AD_DSP),
                                    inner_hard_cmd::lsp_hard_live_roas());
  }

  // locallife order paid multi predict
  if (SPDM_enable_locallife_live_order_paid_add_label(session_data->get_spdm_ctx())) {
    // direct live
    static const std::vector<PredictType> locallife_order_order_paid_predicts_live = {
      PredictType::PredictType_live_pay_rate,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end};
    static const std::vector<engine_base::RType> locallife_order_order_paid_multi_rtypes_live(
      locallife_order_order_paid_predicts_live.size(), engine_base::RType::UNCATEGORIZED);
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(locallife_order_order_paid_multi_rtypes_live,
                                    locallife_order_order_paid_predicts_live,
                                    "lsp_live_pay_rate", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::lsp_live_pay_rate());
    // p2l
    static const std::vector<PredictType> locallife_order_order_paid_predicts_p2l = {
      PredictType::PredictType_photo2live_pay_rate,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end};
    static const std::vector<engine_base::RType> locallife_order_order_paid_multi_rtypes_p2l(
      locallife_order_order_paid_predicts_p2l.size(), engine_base::RType::UNCATEGORIZED);
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(locallife_order_order_paid_multi_rtypes_p2l,
                                    locallife_order_order_paid_predicts_p2l,
                                    "lsp_photo2live_pay_rate", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::lsp_photo2live_pay_rate());
  } else {
    p_cmd_curator->RegisterCmd(
        new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                      PredictType::PredictType_live_pay_rate,
                                      "lsp_live_pay_rate", CMD_SOURCE_AD_DSP),
                                      inner_hard_cmd::lsp_live_pay_rate());
    p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_photo2live_pay_rate,
                                    "lsp_photo2live_pay_rate", CMD_SOURCE_AD_DSP),
                                    inner_hard_cmd::lsp_photo2live_pay_rate());
  }

  // 直播订单 [旁路 AUC]
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::AUC_BASE, PredictType::PredictType_auc_model_base,
                                    "live_and_p2l_pay_rate_auc_base", CMD_SOURCE_AD_DSP),
                          inner_hard_cmd::live_and_p2l_pay_rate_auc_base());
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(engine_base::RType::AUC_EXP, PredictType::PredictType_auc_model_exp,
                                    "live_and_p2l_pay_rate_auc_exp", CMD_SOURCE_AD_DSP),
                          inner_hard_cmd::live_and_p2l_pay_rate_auc_exp());
  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                            PredictType::PredictType_live_room_stay_1m,
                            "live_room_stay_1m_cmd", CMD_SOURCE_AD_DSP),
                          inner_hard_cmd::live_room_stay_1m_cmd());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_photo2live_pay_rate, "photo2live_pay_rate",
                                    CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::photo2live_pay_rate());

  static const std::vector<PredictType> inner_live_atom_order_multi_predicts_photo2live = {
      PredictType::PredictType_photo2live_pay_rate,
      PredictType::PredictType_inner_live_atom_paycnt_front,
      PredictType::PredictType_inner_live_atom_paycnt_end,
      PredictType::PredictType_inner_live_atom_paycnt_indirect,
      PredictType::PredictType_inner_live_atom_paycnt_bak1,
      PredictType::PredictType_inner_live_atom_paycnt_bak2,
      PredictType::PredictType_inner_live_atom_paycnt_bak3};
  static const std::vector<engine_base::RType> inner_live_atom_order_multi_rtypes_photo2live(
                       inner_live_atom_order_multi_predicts_live.size(),
                       engine_base::RType::UNCATEGORIZED);

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(
          inner_live_atom_order_multi_rtypes_photo2live,
          inner_live_atom_order_multi_predicts_photo2live,
          "photo2live_pay_rate_atom_multi2", CMD_SOURCE_AD_DSP),
          inner_hard_cmd::photo2live_pay_rate_atom_multi2());

  p_cmd_curator->RegisterCmd(
      new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                                    PredictType::PredictType_photo2live_pay_rate,
                                    "photo2live_pay_rate_single_tab",
                                    CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::photo2live_pay_rate_single_tab());

  p_cmd_curator->RegisterCmd(new engine_base::CmdWrapperV2(engine_base::RType::UNCATEGORIZED,
                            PredictType::PredictType_live_goods_view,
                            "live_goods_view", CMD_SOURCE_AD_DSP),
                            inner_hard_cmd::live_goods_view());
  // @jiangjiaxin inner_qcpx_photo_cvr_unify_roas_cmd
  static const std::vector<PredictType> inner_qcpx_photo_cvr_unify_predicts = {
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_900_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_850_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_800_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_750_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_700_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_650_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_600_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_550_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_500_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c0,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c2,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c3,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c4,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c5,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1_prime
  };
  static const std::vector<engine_base::RType> inner_qcpx_photo_cvr_unify_predict_types(inner_qcpx_photo_cvr_unify_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      inner_qcpx_photo_cvr_unify_predict_types, inner_qcpx_photo_cvr_unify_predicts,  // NOLINT
      "inner_qcpx_photo_cvr_unify_roas_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::qcpx_photo_cvr_unify_roas_condition());

  // @cuihongyi inner_qcpx_photo_cvr_unify_elastic_cmd
  static const std::vector<PredictType> inner_qcpx_photo_cvr_unify_elastic_predicts = {
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_d0,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_d1,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c0,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c2,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c3,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c4,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c5,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1_prime
  };
  static const std::vector<engine_base::RType> inner_qcpx_photo_cvr_unify_elastic_predict_types(inner_qcpx_photo_cvr_unify_elastic_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      inner_qcpx_photo_cvr_unify_elastic_predict_types, inner_qcpx_photo_cvr_unify_elastic_predicts,  // NOLINT
      "inner_qcpx_photo_cvr_unify_elastic_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::qcpx_photo_cvr_unify_roas_condition());

  // @jiangjiaxin inner_qcpx_photo_cvr_rate_roas
  static const std::vector<PredictType> inner_qcpx_photo_cvr_rate_predicts = {
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_900_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_850_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_800_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_750_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_700_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_650_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_600_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_550_logit,
    PredictType::PredictType_inner_qcpx_photo_cvr_rate_multi_head_500_logit
  };
  static const std::vector<engine_base::RType> qcpx_photo_cvr_rate_predict_types(inner_qcpx_photo_cvr_rate_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_photo_cvr_rate_predict_types, inner_qcpx_photo_cvr_rate_predicts,  // NOLINT
      "inner_qcpx_photo_cvr_rate_roas_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::qcpx_photo_cvr_rate_roas_condition());

// @jiangjiaxin inner_qcpx_photo_cvr_taylor_bspline
  static const std::vector<PredictType> inner_qcpx_photo_cvr_elastic_taylor_piecewise_predicts = {
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c0,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c2,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c3,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c4,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c5,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_taylor_bspline_c1_prime
  };
  static const std::vector<engine_base::RType> qcpx_photo_elastic_taylor_piecewise_predict_types(inner_qcpx_photo_cvr_elastic_taylor_piecewise_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_photo_elastic_taylor_piecewise_predict_types, inner_qcpx_photo_cvr_elastic_taylor_piecewise_predicts,  // NOLINT
      "inner_qcpx_photo_cvr_elastic_taylor_piecewise_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::qcpx_photo_taylor_piecewise_condition());
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_photo_elastic_taylor_piecewise_predict_types, inner_qcpx_photo_cvr_elastic_taylor_piecewise_predicts,  // NOLINT
      "normal_inner_qcpx_photo_cvr_elastic_tata_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::normal_qcpx_photo_tata_condition());

  // @yuanli03 inner_shelf_qcpx_photo_cvr_taylor_bspline
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_photo_elastic_taylor_piecewise_predict_types, inner_qcpx_photo_cvr_elastic_taylor_piecewise_predicts,  // NOLINT
      "inner_shelf_qcpx_photo_cvr_elastic_taylor_piecewise_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::shelf_qcpx_photo_taylor_condition());

  // @wanpengcheng inner_qcpx_live_cvr_disc_multi_head
  static const std::vector<PredictType> inner_qcpx_live_cvr_disc_multi_head_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_950_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_900_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_850_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_800_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_750_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_700_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_650_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_600_logit
  };
  static const std::vector<engine_base::RType> qcpx_live_cvr_disc_multi_head_predict_types(inner_qcpx_live_cvr_disc_multi_head_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_cvr_disc_multi_head_predict_types, inner_qcpx_live_cvr_disc_multi_head_predicts,  // NOLINT
      "normal_inner_qcpx_live_cvr_disc_multi_head_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::normal_qcpx_live_cvr_disc_roas_multi_head_condition());

  // @wanpengcheng inner_qcpx_live_cvr_merge_cmd
  static const std::vector<PredictType> inner_qcpx_live_cvr_merge_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c0,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c1,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c2,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c3,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c4,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_950_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_900_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_850_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_800_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_750_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_700_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_650_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_600_logit
  };
  static const std::vector<engine_base::RType> qcpx_live_cvr_merge_predict_types(inner_qcpx_live_cvr_merge_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_cvr_merge_predict_types, inner_qcpx_live_cvr_merge_predicts,  // NOLINT
      "normal_inner_qcpx_live_cvr_merge_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::normal_qcpx_live_cvr_merge_condition());

  // @fandi inner_qcpx_live_cvr_merge_embq_cmd
  static const std::vector<PredictType> inner_qcpx_live_cvr_merge_embq_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e0,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e1,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e2,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e3,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e4,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e5,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e6,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e7,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e8,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e9,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e10,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e11,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e12,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e13,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e14,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_e15,
    PredictType::PredictType_inner_qcpx_live_cvr_embq_negbias,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_950_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_900_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_850_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_800_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_750_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_700_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_650_logit,
    PredictType::PredictType_inner_qcpx_live_cvr_disc_roas_multi_head_600_logit
  };
  static const std::vector<engine_base::RType> qcpx_live_cvr_merge_embq_predict_types(inner_qcpx_live_cvr_merge_embq_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_cvr_merge_embq_predict_types, inner_qcpx_live_cvr_merge_embq_predicts,  // NOLINT
      "normal_inner_qcpx_live_cvr_merge_embq_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::normal_qcpx_live_cvr_merge_embq_condition());

  // @wanpengcheng inner_qcpx_p2l_hard_ctr_model
  static const std::vector<PredictType> inner_qcpx_p2l_hard_ctr_model_predicts = {
    PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_c0,
    PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_c1,
    PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_d0,
    PredictType::PredictType_inner_qcpx_p2l_live_audience_elastic_d1
  };
  static const std::vector<engine_base::RType> qcpx_p2l_hard_ctr_model_predict_types(inner_qcpx_p2l_hard_ctr_model_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_p2l_hard_ctr_model_predict_types, inner_qcpx_p2l_hard_ctr_model_predicts,  // NOLINT
      "inner_qcpx_p2l_hard_ctr_model_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::qcpx_p2l_hard_ctr_condition());

  // @wanpengcheng inner_qcpx_live_cvr_full_stage
  static const std::vector<PredictType> inner_qcpx_live_cvr_full_stage_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c0_stage1,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c0_stage2,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c1_stage1,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c1_stage2,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c2_stage1,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c2_stage2,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c3_stage1,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c3_stage2,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c4_stage1,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_c4_stage2
  };
  static const std::vector<engine_base::RType> qcpx_live_cvr_full_stage_predict_types(inner_qcpx_live_cvr_full_stage_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_cvr_full_stage_predict_types, inner_qcpx_live_cvr_full_stage_predicts,  // NOLINT
      "inner_qcpx_live_cvr_full_stage_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::qcpx_live_cvr_full_stage_condition());

// @jiangjiaxin inner_qcpx_live_bspline
  static const std::vector<PredictType> inner_qcpx_live_cvr_elastic_bspline_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c0,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c1,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c2,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c3,
    PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c4
  };
  static const std::vector<engine_base::RType> qcpx_live_elastic_bspline_predict_types(inner_qcpx_live_cvr_elastic_bspline_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_elastic_bspline_predict_types, inner_qcpx_live_cvr_elastic_bspline_predicts,
      "normal_inner_qcpx_live_cvr_bspline_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::normal_qcpx_live_bspline_condition());
  // @wanpengcheng QCPX 直播订单 CVR Taylor 硬广
  static const std::vector<PredictType> inner_qcpx_live_cvr_taylor_elastic_bspline_predicts = {
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c0,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c1,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c2,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c3,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c4,
    PredictType::PredictType_inner_qcpx_live_cvr_taylor_elastic_bspline_c1_prime
  };
  static const std::vector<engine_base::RType> qcpx_live_taylor_elastic_bspline_predict_types(inner_qcpx_live_cvr_taylor_elastic_bspline_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      qcpx_live_taylor_elastic_bspline_predict_types, inner_qcpx_live_cvr_taylor_elastic_bspline_predicts,
      "normal_inner_qcpx_live_cvr_taylor_bspline_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::normal_qcpx_live_taylor_bspline_condition());
  // inner storewide live uplift
  static const std::vector<PredictType> inner_storewide_live_uplift_predicts = {
    PredictType::PredictType_inner_storewide_live_uplift_prob1,
    PredictType::PredictType_inner_storewide_live_uplift_prob2
  };
  static const std::vector<engine_base::RType> storewide_live_uplift_predict_types(inner_storewide_live_uplift_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      storewide_live_uplift_predict_types, inner_storewide_live_uplift_predicts,
      "inner_storewide_live_uplift_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::storewide_live_uplift_condition());

  // inner storewide photo uplift
  static const std::vector<PredictType> inner_storewide_merchant_uplift_predicts = {
    PredictType::PredictType_inner_storewide_merchant_uplift_prob1,
    PredictType::PredictType_inner_storewide_merchant_uplift_prob2
  };
  static const std::vector<engine_base::RType> storewide_photo_uplift_predict_types(inner_storewide_merchant_uplift_predicts.size(), engine_base::RType::UNCATEGORIZED);   // NOLINT
  p_cmd_curator->RegisterCmd(
    new engine_base::CmdWrapperV2(
      storewide_photo_uplift_predict_types, inner_storewide_merchant_uplift_predicts,
      "inner_storewide_merchant_uplift_cmd", CMD_SOURCE_AD_DSP),
      inner_hard_cmd::storewide_merchant_uplift_condition());
}

}  // namespace ad_rank
}  // namespace ks
