#include "teams/ad/ad_rank/default/strategy/calc_benefit/plugin/qcpx_strategy_plugins/qcpx_strategy_plugin.h"
#include <string>
#include <algorithm>
#include <memory>
#include <set>
#include <unordered_set>

#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"

#include "teams/ad/ad_rank/processor/factor/qcpx_strategy_live/qcpx_strategy.h"
#include "teams/ad/ad_rank/processor/factor/qcpx_strategy_photo/qcpx_strategy.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "absl/strings/str_cat.h"

namespace ks {
namespace ad_rank {

const char* QcpxStrategyPlugin::Name() { return "QcpxStrategyPlugin"; }

void QcpxStrategyPlugin::Clear() {}

bool QcpxStrategyPlugin::IsRun(const ContextData* session_data, const AdList* adlist) {
  // holdout spdm
  if (SPDM_enable_inner_coupon_holdout(session_data->get_spdm_ctx())) {
    session_data->common_w_->SetIntCommonAttr("inner_qcpx_filter_reason", kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_IS_HOLDOUT);  // NOLINT
    return false;
  }
  bool flag = true;
  // 总开关
  flag &= adlist->Size() > 0;
  if (!flag) return false;

  bool enable_skip_peccoupon_innerloopbigcard_secondreq = SPDM_enable_skip_peccoupon_innerloopbigcard_secondreq(session_data->get_spdm_ctx());  // NOLINT
  if (enable_skip_peccoupon_innerloopbigcard_secondreq && session_data->get_rank_request()->ad_request().is_common_card_second_request() && session_data->get_rank_request()->ad_request().common_card_request_info().feed_card_ind() == kuaishou::ad::AdEnum::INNER_LOOP) {  // NOLINT
    RANK_DOT_COUNT(session_data, 1, "skip_peccoupon_innerloopbigcard_secondreq");
    return false;
  }
  return flag;
}

StraRetCode QcpxStrategyPlugin::Process(ContextData* session_data, AdList* adlist) {
  // 优先级从上到下依次递增, 直播与短带并列
  if (SPDM_enable_qcpx_live_strategy_run(session_data->get_spdm_ctx())) {
    LiveRunner(session_data, adlist);
  } else {
    session_data->common_w_->SetIntCommonAttr("inner_qcpx_filter_reason", kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_IS_HOLDOUT); // NOLINT
  }
  if (SPDM_enable_qcpx_photo_strategy_run(session_data->get_spdm_ctx())) {
    PhotoRunner(session_data, adlist);
  } else {
    session_data->common_w_->SetIntCommonAttr("inner_qcpx_filter_reason", kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_IS_HOLDOUT); // NOLINT
  }
  return StraRetCode::SUCC;
}

void QcpxStrategyPlugin::LiveRunner(ContextData* session_data, AdList* adlist) {
  if (SPDM_enable_qcpx_live_u0_filter_potential(session_data->get_spdm_ctx())) {
    if (!(SPDM_enable_qcpx_u0_nofilter_rewarded(session_data->get_spdm_ctx()) &&
    (session_data->get_is_rewarded() || session_data->get_is_inspire_live_request()))) {
        auto& buyer_effective_type = session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();  // NOLINT
        if (buyer_effective_type.compare("") == 0) {
          session_data->common_w_->SetIntCommonAttr("inner_qcpx_filter_reason", kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_BLACKLIST_U0);  // NOLINT
          RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.live_u0_potential_filter");
          return;
        }
    }
  }
  if (SPDM_enable_qcpx_live_u0_filter(session_data->get_spdm_ctx())) {
    if (!(SPDM_enable_qcpx_u0_nofilter_rewarded(session_data->get_spdm_ctx()) &&
    (session_data->get_is_rewarded() || session_data->get_is_inspire_live_request()))) {
      auto& buyer_effective_type = session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();  // NOLINT
      if (buyer_effective_type.compare("") == 0 || buyer_effective_type.compare("U0") == 0) {
        session_data->common_w_->SetIntCommonAttr("inner_qcpx_filter_reason", kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_BLACKLIST_U0);  // NOLINT
        RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.live_u0_filter");
        return;
      }
    }
  }
  QcpxLiveStrategy qcpx_runner(session_data);
  bool enable_inner_live_coupon_holdout = SPDM_enable_inner_live_coupon_holdout(session_data->get_spdm_ctx());  // NOLINT
  bool enable_inner_live_coupon_holdout_live = SPDM_enable_inner_live_coupon_holdout_live(session_data->get_spdm_ctx());  // NOLINT
  bool enable_inner_live_coupon_holdout_p2l = SPDM_enable_inner_live_coupon_holdout_p2l(session_data->get_spdm_ctx());  // NOLINT
  bool enable_qcpx_live_shelf_run_v2 = (SPDM_enable_qcpx_live_shelf_run_v2(session_data->get_spdm_ctx()) &&
                                        !SPDM_enable_qcpx_live_shelf_run_v2_hold(session_data->get_spdm_ctx()));   // NOLINT
  bool enable_qcpx_rewarded = SPDM_enable_qcpx_rewarded(session_data->get_spdm_ctx());  // NOLINT
  qcpx_runner.InitParams();
  // 客户实验
  const std::unordered_set<int32_t> hosting_types{
        kuaishou::ad::AdEnum::ORIENTED_SMART_GOODS,  // 8 海投（智能选品）
        kuaishou::ad::AdEnum::ORIENTED_SMART_OPTIONAL_GOODS,  // 13 海投（自选商品）
        kuaishou::ad::AdEnum::ORIENTED_SMART_SINGLE_GOODS,  // 20 海投（单商品）
        kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE,  // 21 全店直播推广
        kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT,  // 23 直播托管
        kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_PRODUCT,  // 24 全站商品推广
        kuaishou::ad::AdEnum::ORIENTED_SEARCH_WORD_PROJECT,  // 25 搜索托管
        kuaishou::ad::AdEnum::STORE_NEW_CUSTOMER_HOSTING,  // 26 店铺新客托管
        kuaishou::ad::AdEnum::ORIENTED_SEARCH_WORD_LIVE_PROJECT,  // 27 搜索直播托管
        kuaishou::ad::AdEnum::ORIENTED_ESP_MOBILE_HOSTING,  // 29 移动端托管
        kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE  // 30 本地推全店直播推广
  };
  bool enable_qcpx_customer_exp = false;
  int64_t seed = SPDM_value_qcpx_customer_exp_seed(session_data->get_spdm_ctx());
  int64_t percent = SPDM_value_qcpx_customer_exp_percent(session_data->get_spdm_ctx());
  // 直播 已有券不发券 临时实验 数据准备 @fandi
  bool enable_live_skip_q_when_has_live_q = SPDM_enable_live_skip_q_when_has_live_q(session_data->get_spdm_ctx());  // NOLINT
  bool enable_p2l_hard_skip_q_when_has_live_q = SPDM_enable_p2l_hard_skip_q_when_has_live_q(session_data->get_spdm_ctx());  // NOLINT
  auto& ad_request = session_data->get_rank_request()->ad_request();
  bool user_has_qcpx_live_coupon = ad_request.ad_user_info().user_has_qcpx_live_coupon();
  // 流量准入
  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_subpage_set = RankKconfUtil::qcpxSubpageConfig();
  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_shelf_live_subpageid_set = RankKconfUtil::qcpxShelfLiveAdmitSubpageid();   // NOLINT
  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_rewarded_subpageid_set = RankKconfUtil::qcpxRewardAdmitSubpageid();   // NOLINT
  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_follow_subpageid_set = RankKconfUtil::qcpxFollowAdmitSubpageid();  // NOLINT
  int64_t subpage_id = session_data->get_sub_page_id();
  bool is_hit_shelf_live = qcpx_shelf_live_subpageid_set != nullptr && qcpx_shelf_live_subpageid_set->count(subpage_id)>0;   // NOLINT
  bool is_hit_rewarded = qcpx_rewarded_subpageid_set != nullptr && qcpx_rewarded_subpageid_set->count(subpage_id)>0;   // NOLINT
  bool is_hit_follow = qcpx_follow_subpageid_set != nullptr && qcpx_follow_subpageid_set->count(subpage_id)>0;   // NOLINT
  bool is_follow_freq = is_hit_follow && SPDM_enable_qcpx_freq_control_for_follow(session_data->get_spdm_ctx()); // NOLINT
  bool hit_common_subpage = qcpx_subpage_set != nullptr && qcpx_subpage_set->count(subpage_id) > 0;
  bool enable_qcpx_live_add_admit = SPDM_enable_qcpx_live_add_admit(session_data->get_spdm_ctx());
  // 电商券数据
  bool enable_qcpx_live_no_when_ecom_coupon = SPDM_enable_qcpx_live_no_when_ecom_coupon(session_data->get_spdm_ctx());   // NOLINT
  const auto& author_ecom_coupon_list = session_data->get_author_ecom_coupon_list();
  // 频控: 数据准备
  bool enable_live_qcpx_freq_control = SPDM_enable_live_qcpx_freq_control(session_data->get_spdm_ctx());
  int value_qcpx_freq_control_pay_nd = is_follow_freq ?
    SPDM_value_live_qcpx_freq_control_pay_nd_follow(session_data->get_spdm_ctx()) :
    SPDM_value_live_qcpx_freq_control_pay_nd(session_data->get_spdm_ctx());
  const auto& order_info = session_data->get_rank_request()->ad_request().ad_user_info().colossus_order_data_map();  // NOLINT
  auto order_seller_iter = order_info.find("seller_id");
  auto order_real_seller_iter = order_info.find("real_seller_id");
  auto order_time_iter = order_info.find("pay_order_time");
  int64 current_time_stamp = session_data->get_current_timestamp_nodiff() / 1000000;
  absl::flat_hash_set<int64_t> recent_sellerid_set;
  absl::flat_hash_set<int64_t> recent_real_sellerid_set;
  std::string p2l_min_soft_app_ver = "12.4.10";
  std::string p2l_min_hard_app_ver = "10.5.40";
  std::string live_min_soft_app_ver = "12.1.10";
  std::string live_min_hard_app_ver = "12.1.10";
  std::string app_ver = session_data->get_app_version();
  std::string qcpx_shelf_version_control_latest_version = SPDM_qcpx_shelf_version_control_latest_version(session_data->get_spdm_ctx());  // NOLINT
  // 货架版控
  if (session_data->get_pos_manager_base().IsShelfMerchantTraffic() &&
      engine_base::CompareAppVersion(app_ver, qcpx_shelf_version_control_latest_version) < 0) {
    session_data->common_w_->SetIntCommonAttr("inner_qcpx_filter_reason", kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);  // NOLINT
    RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_live_req_is_shelf_version_control");
    return;
  }
  // 频控: 商家粒度 seller
  if (order_seller_iter != order_info.end() && order_time_iter != order_info.end()) {
    const auto& order_seller_list = order_seller_iter->second.int_list_value();
    const auto& order_time_list = order_time_iter->second.int_list_value();
    if (order_seller_list.size() != order_time_list.size()) {
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_live_freq_control_seller_data_badcase");
    } else {
      for (int i = 0; i < order_seller_list.size(); i++) {
        if (current_time_stamp - order_time_list[i] <= 60 * 60 * 24 * value_qcpx_freq_control_pay_nd) {
          if (order_seller_list[i] == 0) continue;
          recent_sellerid_set.insert(order_seller_list[i]);
        }
      }
    }
  }
  // 频控: 商家粒度 real_seller
  if (order_real_seller_iter != order_info.end() && order_time_iter != order_info.end()) {
    const auto& order_real_seller_list = order_real_seller_iter->second.int_list_value();
    const auto& order_time_list = order_time_iter->second.int_list_value();
    if (order_real_seller_list.size() != order_time_list.size()) {
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_stat_freq_control_real_seller_data_badcase");
    } else {
      for (int i = 0; i < order_real_seller_list.size(); i++) {
        if (current_time_stamp - order_time_list[i] <= 60 * 60 * 24 * value_qcpx_freq_control_pay_nd) {
          if (order_real_seller_list[i] == 0) continue;
          recent_real_sellerid_set.insert(order_real_seller_list[i]);
        }
      }
    }
  }
  // 电商屏蔽名单
  bool enable_ecom_qcpx_filter_list = SPDM_enable_ecom_qcpx_filter_list(session_data->get_spdm_ctx());
  // 搜后推
  bool enable_qcpx_live_search_and_push = SPDM_enable_qcpx_live_search_and_push(session_data->get_spdm_ctx());  // NOLINT
  int value_qcpx_live_search_and_push_hours = SPDM_value_qcpx_live_search_and_push_hours(session_data->get_spdm_ctx());  // NOLINT
  double value_qcpx_live_search_and_push_ratio = SPDM_value_qcpx_live_search_and_push_ratio(session_data->get_spdm_ctx());  // NOLINT
  const auto& feasury_data_map = session_data->get_rank_request()->ad_request().ad_user_info().feasury_data_map();  // NOLINT
  auto search_show_time_iter = feasury_data_map.find("uStandardMerchantGoodsSearchShowTimeList");
  auto search_show_author_iter = feasury_data_map.find("uStandardMerchantGoodsSearchShowAuthorList");
  bool has_search_show_time = search_show_time_iter != feasury_data_map.end();
  bool has_search_show_author = search_show_author_iter != feasury_data_map.end();
  absl::flat_hash_set<int64_t> recent_search_show_author_set;
  if (has_search_show_time && has_search_show_author) {
    const auto& search_show_time_vec = search_show_time_iter->second.int_list_value();
    const auto& search_show_author_vec = search_show_author_iter->second.int_list_value();
    if (search_show_time_vec.size() == search_show_author_vec.size()) {
      for (int i = 0; i < search_show_time_vec.size(); i++) {
        if (current_time_stamp - search_show_time_vec[i] / 1e3 <= 60 * 60 * value_qcpx_live_search_and_push_hours) {  // NOLINT
          recent_search_show_author_set.insert(search_show_author_vec[i]);
        }
      }
    }
  }
  for (const auto& p_ad : adlist->Ads()) {
    if (!(p_ad->Is(AdFlag::is_esp_ad) &&
          p_ad->Is(AdFlag::is_inner_loop_deep_ad) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE)) {
      continue;
    }
    if (enable_qcpx_live_add_admit &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_T7_ROI &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
      continue;
    }
    // 流量准入
    if (p_ad->Attr(ItemIdx::fd_UNIT_auto_deliver_type).GetIntValue(p_ad->AttrIndex()).value_or(0) == 11) {
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_auto_deliver_admit");
      continue;
    }
    // 客户实验屏蔽
    int64_t ad_id = hosting_types.count(p_ad->get_scene_oriented_type()) > 0 ? p_ad->get_campaign_id() : p_ad->get_unit_id();  // NOLINT
    if (enable_qcpx_customer_exp && seed > 100) {
      int64_t mod = ad_id % seed;
      double bound = 0.01 * percent * seed;
      if (mod < bound) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_IS_HOLDOUT);
        continue;
      }
    }
    bool page_admit = false;
    if (hit_common_subpage) {
      page_admit = true;
    }
    // 流量准入: 双列关注页
    if (subpage_id == 10008001) {
      page_admit = true;
    }
    // 流量准入: 单列关注页
    if (subpage_id == 100013100 || subpage_id == 100016771) {
      page_admit = true;
    }

    // 流量准入: 泛货架
    if (enable_qcpx_live_shelf_run_v2  && is_hit_shelf_live) {   // NOLINT
      page_admit = true;
    }

    // 流量准入: 激励
    if (enable_qcpx_rewarded && is_hit_rewarded) {   // NOLINT
      page_admit = true;
    }

    if (!page_admit) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_PAGEID);
      continue;
    }
    RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_page_admit",
      absl::StrCat(subpage_id), absl::StrCat(p_ad->get_item_type()));

    // 破价过滤
    // fandi 临时豁免黑名单逻辑 未来删除
    // bool is_tmp_skip = false;
    // int64_t author_id = p_ad->get_author_id();
    // if (RankKconfUtil::qcpxTmpIdList() != nullptr) {
    //   if (RankKconfUtil::qcpxTmpIdList()->count(author_id) > 0) {
    //     is_tmp_skip = true;
    //   }
    // }
    if (IsFilterAd(p_ad, enable_ecom_qcpx_filter_list)) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_BLACKLIST_AUTHOR);
      continue;
    }
    // 已有券不发券 临时实验 执行 @fandi
    // 直投
    if (enable_live_skip_q_when_has_live_q &&
      p_ad->Is(AdFlag::is_live) &&
      user_has_qcpx_live_coupon) {
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_live_skip_q_when_has_live_q");
      continue;
    }
    // 短引硬广
    if (enable_p2l_hard_skip_q_when_has_live_q &&
      p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE &&
      p_ad->Is(AdFlag::is_p2l) &&
      user_has_qcpx_live_coupon) {
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_p2l_hard_skip_q_when_has_live_q");
      continue;
    }
    // 版控: 执行
    if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE && p_ad->Is(AdFlag::is_p2l)) {
      // 短引软广
      if (engine_base::CompareAppVersion(app_ver, p2l_min_soft_app_ver) < 0) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
        continue;
      }
    } else if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE && p_ad->Is(AdFlag::is_p2l)) {  // NOLINT
      // 短引硬广
      if (engine_base::CompareAppVersion(app_ver, p2l_min_hard_app_ver) < 0) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
        continue;
      }
    } else if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE && p_ad->Is(AdFlag::is_live)) {  // NOLINT
      // 直投软广
      if (engine_base::CompareAppVersion(app_ver, live_min_soft_app_ver) < 0) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
        continue;
      }
    } else {
      // 直投硬广
      if (engine_base::CompareAppVersion(app_ver, live_min_hard_app_ver) < 0) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
        continue;
      }
    }
    // 版控: 双列关注页
    if (session_data->get_sub_page_id() == 10008001 &&
        engine_base::CompareAppVersion(app_ver, "12.11.10") < 0) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
      continue;
    }
    // 版控: 双列发现 仅生效直投
    if (session_data->get_sub_page_id() == 10002001 &&
        p_ad->Is(AdFlag::is_live) &&
        engine_base::CompareAppVersion(app_ver, "12.11.10") < 0) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
      continue;
    }
    // 版控: 单列关注页
    if ((subpage_id == 100013100 || subpage_id == 100016771) &&
        engine_base::CompareAppVersion(app_ver, "12.11.10") < 0) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
      continue;
    }

    //版控：激励
    if (enable_qcpx_rewarded && is_hit_rewarded &&
      engine_base::CompareAppVersion(app_ver, "13.7.20") < 0) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
      continue;
    }

    // 频控: 执行
    if (enable_live_qcpx_freq_control) {
      int64_t cand_sellerid = p_ad->get_author_id();
      // 直播: seller_id 粒度
      if (!p_ad->Is(AdFlag::is_photo_ad_inner) && recent_sellerid_set.contains(cand_sellerid)) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::FREQCONTROL_BUY);
        RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_live_freq_control_effective");
        continue;
      }
      // 直播: real_seller_id 粒度
      if (!p_ad->Is(AdFlag::is_photo_ad_inner) && recent_real_sellerid_set.contains(cand_sellerid)) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::FREQCONTROL_BUY);
        RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_live_freq_control_live_real_seller_effective");
        continue;
      }
    }
    if (enable_inner_live_coupon_holdout_live && p_ad->Is(AdFlag::is_live)) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_IS_NORMAL_HOLDOUT);
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.live_runner_hold_for_live_live");
      continue;
    }
    if (enable_inner_live_coupon_holdout_p2l && p_ad->Is(AdFlag::is_p2l)) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_IS_NORMAL_HOLDOUT);
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.live_runner_hold_for_live_p2l");
      continue;
    }
    if (enable_inner_live_coupon_holdout) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_IS_NORMAL_HOLDOUT);
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.live_runner_hold_for_live");
      continue;
    }

    // 电商券过滤
    if (enable_qcpx_live_no_when_ecom_coupon) {
      const auto& author_ecom_coupon_list_iter = author_ecom_coupon_list.find(p_ad->get_author_id());
      if (author_ecom_coupon_list_iter != author_ecom_coupon_list.end()) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_ECOM);
        RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.live_runner_hold_for_ecom_coupon");
        continue;
      }
    }
    // 搜后推
    if (enable_qcpx_live_search_and_push) {
      p_ad->set_inner_qcpx_search_and_push_ratio(1.0);
      if (recent_search_show_author_set.contains(p_ad->get_author_id())) {
        p_ad->set_inner_qcpx_search_and_push_ratio(value_qcpx_live_search_and_push_ratio);
      }
    }

    qcpx_runner.Process(p_ad);
  }
  qcpx_runner.Monitor();
}

void QcpxStrategyPlugin::PhotoRunner(ContextData* session_data, AdList* adlist) {
  if (SPDM_enable_qcpx_photo_filter_u0(session_data->get_spdm_ctx())) {
    auto& buyer_effective_type = session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();  // NOLINT
    if (buyer_effective_type.compare("U0") == 0) {
      session_data->common_w_->SetIntCommonAttr("inner_qcpx_filter_reason", kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_BLACKLIST_U0);  // NOLINT
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.photo_u0_filter");
      return;
    }
  }
  // 短带低价格带不发券
  bool enable_qcpx_photo_no_le_10 = SPDM_enable_qcpx_photo_no_le_10(session_data->get_spdm_ctx());
  // 短带比价高价率不发券
  bool enable_qcpx_photo_no_bijia_high = SPDM_enable_qcpx_photo_no_bijia_high(session_data->get_spdm_ctx());
  double value_qcpx_photo_no_bijia_high = SPDM_value_qcpx_photo_no_bijia_high(session_data->get_spdm_ctx());
  // 流量准入
  bool enable_qcpx_shelf_run_v2 = (SPDM_enable_qcpx_shelf_run_v2(session_data->get_spdm_ctx())
                                    && !SPDM_enable_qcpx_shelf_run_v2_hold(session_data->get_spdm_ctx()));
  bool enable_qcpx_shelf_shield_U0 = SPDM_enable_qcpx_shelf_shield_U0(session_data->get_spdm_ctx())
                                      && session_data->get_pos_manager_base().IsShelfMerchantTraffic();
  bool enable_qcpx_shelf_shield_U0_risk = SPDM_enable_qcpx_shelf_shield_U0_risk(session_data->get_spdm_ctx())
                                      && session_data->get_pos_manager_base().IsShelfMerchantTraffic();
  bool enable_qcpx_shelf_shield_U0_potential =
                                      SPDM_enable_qcpx_shelf_shield_U0_potential(session_data->get_spdm_ctx())
                                      && session_data->get_pos_manager_base().IsShelfMerchantTraffic();
  bool disable_qcpx_shelf_guess_run = SPDM_disable_qcpx_shelf_guess_run(session_data->get_spdm_ctx()) && session_data->get_pos_manager_base().IsGuessYouLike();  // NOLINT
  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_subpage_set = RankKconfUtil::qcpxSubpageConfig();
  int64_t subpage_id = session_data->get_sub_page_id();
  bool hit_common_subpage = qcpx_subpage_set != nullptr && qcpx_subpage_set->count(subpage_id) > 0;

  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_shelf_photo_subpageid_set = RankKconfUtil::qcpxShelfPhotoAdmitSubpageid();  // NOLINT
  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_rewarded_subpageid_set = RankKconfUtil::qcpxRewardAdmitSubpageid();  // NOLINT
  std::shared_ptr<absl::flat_hash_set<int64_t>> qcpx_follow_subpageid_set = RankKconfUtil::qcpxFollowAdmitSubpageid();  // NOLINT
  bool is_hit_rewarded = qcpx_rewarded_subpageid_set != nullptr && qcpx_rewarded_subpageid_set->count(subpage_id)>0;   // NOLINT
  bool is_hit_shelf_photo = qcpx_shelf_photo_subpageid_set != nullptr && qcpx_shelf_photo_subpageid_set->count(subpage_id)>0;   // NOLINT
  bool is_hit_follow = qcpx_follow_subpageid_set != nullptr && qcpx_follow_subpageid_set->count(subpage_id)>0;   // NOLINT
  bool enable_qcpx_rewarded = SPDM_enable_qcpx_rewarded(session_data->get_spdm_ctx());  // NOLINT

  QcpxPhotoStrategy qcpx_runner(session_data);
  bool enable_inner_photo_coupon_holdout = SPDM_enable_inner_photo_coupon_holdout(session_data->get_spdm_ctx());  // NOLINT
  qcpx_runner.InitParams();

  // 客户实验
  const std::unordered_set<int32_t> hosting_types{
        kuaishou::ad::AdEnum::ORIENTED_SMART_GOODS,  // 8 海投（智能选品）
        kuaishou::ad::AdEnum::ORIENTED_SMART_OPTIONAL_GOODS,  // 13 海投（自选商品）
        kuaishou::ad::AdEnum::ORIENTED_SMART_SINGLE_GOODS,  // 20 海投（单商品）
        kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE,  // 21 全店直播推广
        kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT,  // 23 直播托管
        kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_PRODUCT,  // 24 全站商品推广
        kuaishou::ad::AdEnum::ORIENTED_SEARCH_WORD_PROJECT,  // 25 搜索托管
        kuaishou::ad::AdEnum::STORE_NEW_CUSTOMER_HOSTING,  // 26 店铺新客托管
        kuaishou::ad::AdEnum::ORIENTED_SEARCH_WORD_LIVE_PROJECT,  // 27 搜索直播托管
        kuaishou::ad::AdEnum::ORIENTED_ESP_MOBILE_HOSTING,  // 29 移动端托管
        kuaishou::ad::AdEnum::ORIENTED_LSP_STORE_WIDE_LIVE  // 30 本地推全店直播推广
  };
  bool enable_qcpx_customer_exp = false;
  int64_t seed = SPDM_value_qcpx_customer_exp_seed(session_data->get_spdm_ctx());
  int64_t percent = SPDM_value_qcpx_customer_exp_percent(session_data->get_spdm_ctx());
  // 版控: 数据准备
  std::string min_soft_app_ver = "12.2.20";
  std::string min_hard_app_ver = "10.5.40";
  std::string app_ver = session_data->get_app_version();
  std::string qcpx_shelf_version_control_latest_version = SPDM_qcpx_shelf_version_control_latest_version(session_data->get_spdm_ctx());  // NOLINT
  // 货架版控
  if (session_data->get_pos_manager_base().IsShelfMerchantTraffic() &&
      engine_base::CompareAppVersion(app_ver, qcpx_shelf_version_control_latest_version) < 0) {  // NOLINT
    RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_photo_req_is_shelf_version_control");
    session_data->common_w_->SetIntCommonAttr("inner_qcpx_filter_reason", kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);  // NOLINT
    return;
  }
  // 频控: 数据准备
  bool is_follow_freq = is_hit_follow && SPDM_enable_qcpx_freq_control_for_follow(session_data->get_spdm_ctx()); // NOLINT
  int value_qcpx_freq_control_pay_nd = is_follow_freq ?
      SPDM_value_photo_qcpx_freq_control_pay_nd_follow(session_data->get_spdm_ctx()) :
      SPDM_value_photo_qcpx_freq_control_pay_nd(session_data->get_spdm_ctx()); // NOLINT
  const auto& order_info = session_data->get_rank_request()->ad_request().ad_user_info().colossus_order_data_map();  // NOLINT

  int value_qcpx_freq_control_pay_nd_author_spu = is_follow_freq ? // NOLINT
    SPDM_value_qcpx_freq_control_pay_nd_author_spu_follow(session_data->get_spdm_ctx()) :
    SPDM_value_qcpx_freq_control_pay_nd_author_spu(session_data->get_spdm_ctx());

  auto order_spuid_iter = order_info.find("spuid");
  auto order_time_iter = order_info.find("pay_order_time");
  auto order_uniform_spu_id_iter = order_info.find("uniform_spu_id");
  auto order_seller_iter = order_info.find("seller_id");
  auto order_real_seller_iter = order_info.find("real_seller_id");

  int64 current_time_stamp = session_data->get_current_timestamp_nodiff() / 1000000;
  absl::flat_hash_set<int64_t> recent_spuid_set;
  absl::flat_hash_set<int64_t> recent_uniform_spu_id_set;
  absl::flat_hash_set<int64_t> recent_itemid_set;
  absl::flat_hash_set<std::string> recent_author_spu_set;

  // 频控: author X spu 粒度
  if (order_time_iter != order_info.end() &&
      order_seller_iter != order_info.end() && order_real_seller_iter != order_info.end() &&
      order_uniform_spu_id_iter != order_info.end()) {
    const auto& order_time_list = order_time_iter->second.int_list_value();
    const auto& order_seller_list = order_seller_iter->second.int_list_value();
    const auto& order_real_seller_list = order_real_seller_iter->second.int_list_value();
    const auto& order_uniform_spu_id_list = order_uniform_spu_id_iter->second.int_list_value();
    if (order_time_list.size() == order_seller_list.size() &&
        order_time_list.size() == order_real_seller_list.size() &&
        order_time_list.size() == order_uniform_spu_id_list.size()) {
      for (int i = 0; i < order_time_list.size(); i++) {
        if (current_time_stamp - order_time_list[i] <= 60 * 60 * 24 * value_qcpx_freq_control_pay_nd_author_spu) {  // NOLINT
          int64_t seller_id = order_seller_list[i];
          int64_t real_seller_id = order_real_seller_list[i];
          int64_t uniform_spu_id = order_uniform_spu_id_list[i];
          recent_author_spu_set.insert(absl::StrCat(seller_id, "_", uniform_spu_id));
          recent_author_spu_set.insert(absl::StrCat(real_seller_id, "_", uniform_spu_id));
        }
      }
    }
    RANK_DOT_STATS(session_data, recent_author_spu_set.size(),
      "ad.ad_rank.qcpx_photo_freq_control_author_spu_size")
  }

  // 频控: item 粒度
  auto order_itemid_iter = order_info.find("item_id");
  if (order_itemid_iter != order_info.end() && order_time_iter != order_info.end()) {
    const auto& order_itemid_list = order_itemid_iter->second.int_list_value();
    const auto& order_time_list = order_time_iter->second.int_list_value();
    if (order_itemid_list.size() != order_time_list.size()) {
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_photo_freq_control_itemid_data_badcase");
    } else {
      for (int i = 0; i < order_itemid_list.size(); i++) {
        if (current_time_stamp - order_time_list[i] <= 60 * 60 * 24 * value_qcpx_freq_control_pay_nd) {
          if (order_itemid_list[i] == 0) continue;
          recent_itemid_set.insert(order_itemid_list[i]);
        }
      }
    }
  }

  // 频控兼容新 spu_id
  if (order_uniform_spu_id_iter != order_info.end() && order_time_iter != order_info.end()) {  // NOLINT
    const auto& order_uniform_spu_id_list = order_uniform_spu_id_iter->second.int_list_value();
    const auto& order_time_list = order_time_iter->second.int_list_value();
    if (order_uniform_spu_id_list.size() != order_time_list.size()) {
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_photo_freq_control_new_spu_data_badcase");
    } else {
      for (int i = 0; i < order_uniform_spu_id_list.size(); i++) {
        if (current_time_stamp - order_time_list[i] <= 60 * 60 * 24 * value_qcpx_freq_control_pay_nd) {
          if (order_uniform_spu_id_list[i] == 0) continue;
          recent_uniform_spu_id_set.insert(order_uniform_spu_id_list[i]);
        }
      }
    }
  }
  // 电商屏蔽名单
  bool enable_ecom_qcpx_filter_list = SPDM_enable_ecom_qcpx_filter_list(session_data->get_spdm_ctx());
  // 搜后推
  bool enable_qcpx_photo_search_and_push = SPDM_enable_qcpx_photo_search_and_push(session_data->get_spdm_ctx());  // NOLINT
  int value_qcpx_photo_search_and_push_hours = SPDM_value_qcpx_photo_search_and_push_hours(session_data->get_spdm_ctx());  // NOLINT
  double value_qcpx_photo_search_and_push_ratio = SPDM_value_qcpx_photo_search_and_push_ratio(session_data->get_spdm_ctx());  // NOLINT
  const auto& feasury_data_map = session_data->get_rank_request()->ad_request().ad_user_info().feasury_data_map();  // NOLINT
  auto search_show_time_iter = feasury_data_map.find("uStandardMerchantGoodsSearchShowTimeList");
  auto search_show_item_iter = feasury_data_map.find("uStandardMerchantGoodsSearchShowItemList");
  bool has_search_show_time = search_show_time_iter != feasury_data_map.end();
  bool has_search_show_item = search_show_item_iter != feasury_data_map.end();
  absl::flat_hash_set<int64_t> recent_search_show_item_set;
  if (has_search_show_time && has_search_show_item) {
    const auto& search_show_time_vec = search_show_time_iter->second.int_list_value();
    const auto& search_show_item_vec = search_show_item_iter->second.int_list_value();
    if (search_show_time_vec.size() == search_show_item_vec.size()) {
      for (int i = 0; i < search_show_time_vec.size(); i++) {
        if (current_time_stamp - search_show_time_vec[i] / 1e3 <= 60 * 60 * value_qcpx_photo_search_and_push_hours) {  // NOLINT
          recent_search_show_item_set.insert(search_show_item_vec[i]);
        }
      }
    }
  }
  for (const auto& p_ad : adlist->Ads()) {
    if (!(p_ad->Is(AdFlag::is_esp_ad) &&
          p_ad->Is(AdFlag::is_inner_loop_deep_ad) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE)) {
      continue;
    }
    if (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS &&
        p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
      continue;
    }
    // 客户实验屏蔽
    int64_t ad_id = hosting_types.count(p_ad->get_scene_oriented_type()) > 0 ? p_ad->get_campaign_id() : p_ad->get_unit_id();  // NOLINT
    if (enable_qcpx_customer_exp && seed > 100) {
      int64_t mod = ad_id % seed;
      double bound = 0.01 * percent * seed;
      if (mod < bound) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_IS_HOLDOUT);
        continue;
      }
    }
    // 短带低价格带不发券
    if (enable_qcpx_photo_no_le_10 &&
        p_ad->get_product_min_price() * 10 < 10000) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::STRATEGY_MINPRICE);
      continue;
    }
    // 短带比价高价率不发券
    if (enable_qcpx_photo_no_bijia_high &&
       p_ad->Attr(ItemIdx::fd_PRODUCT_sku_price_high_rate).GetDoubleValue(p_ad->AttrIndex()).value_or(0.0) > value_qcpx_photo_no_bijia_high) {  // NOLINT
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_BIJIA);
      continue;
    }
    // 流量准入
    bool page_admit = false;
    if (hit_common_subpage) {
      page_admit = true;
    }
    // 流量准入: 双列关注页
    if (subpage_id == 10008001) {
      page_admit = true;
    }
    // 流量准入: 单列关注页
    if (subpage_id == 100013100 || subpage_id == 100016771) {
      page_admit = true;
    }

    // 流量准入: 泛货架 photo
    if (enable_qcpx_shelf_run_v2 && is_hit_shelf_photo) {
      page_admit = true;
      if (disable_qcpx_shelf_guess_run) {
        page_admit = false;
      }
    }

    // 流量准入: 激励
    if (enable_qcpx_rewarded && is_hit_rewarded) {   // NOLINT
      page_admit = true;
    }

    if (!page_admit) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_PAGEID);
      continue;
    }
    RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_page_admit",
      absl::StrCat(subpage_id), absl::StrCat(p_ad->get_item_type()));
    // 破价过滤
    if (IsFilterAd(p_ad, enable_ecom_qcpx_filter_list)) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_BLACKLIST_ITEM);
      continue;
    }
    // 版控: 执行
    if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
      // 软广
      if (engine_base::CompareAppVersion(app_ver, min_soft_app_ver) < 0) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
        continue;
      }
    } else {
      // 硬广
      if (engine_base::CompareAppVersion(app_ver, min_hard_app_ver) < 0) {
        p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
        continue;
      }
    }
    // 版控: 双列关注页
    if (session_data->get_sub_page_id() == 10008001 &&
        engine_base::CompareAppVersion(app_ver, "12.11.10") < 0) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
      continue;
    }
    // 版控: 单列关注页
    if ((subpage_id == 100013100 || subpage_id == 100016771) &&
        engine_base::CompareAppVersion(app_ver, "12.11.10") < 0) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
      continue;
    }

    //版控：激励
    if (enable_qcpx_rewarded && is_hit_rewarded &&
      engine_base::CompareAppVersion(app_ver, "13.7.20") < 0) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_INVALID_VERSION);
      continue;
    }

    // 频控: 执行
    int64_t author_id = p_ad->get_author_id();
    int64_t cand_itemid = p_ad->get_merchant_product_id();
    int64_t ecom_spu_id = p_ad->Attr(ItemIdx::fd_PRODUCT_ecom_spu_id).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    int64_t photo_ecom_spu_id = p_ad->Attr(ItemIdx::fd_PHOTO_ecom_spu_id).GetIntValue(p_ad->AttrIndex()).value_or(0);  // NOLINT
    int64_t cand_spuid = ecom_spu_id > 0 ? ecom_spu_id : photo_ecom_spu_id;

    // 频控: item 粒度
    if (p_ad->Is(AdFlag::is_photo_ad_inner) && recent_itemid_set.contains(cand_itemid)) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::FREQCONTROL_BUY);
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_photo_item_freq_control_effective");
      continue;
    }
    // 频控: 新 spu_id 粒度
    if (p_ad->Is(AdFlag::is_photo_ad_inner) && recent_uniform_spu_id_set.contains(cand_spuid)) {  // NOLINT
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::FREQCONTROL_BUY);
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_photo_freq_control_effective_new_spu");
      continue;
    }
    // 频控: author X spu 粒度
    if (author_id > 0 && ecom_spu_id > 0 &&
        recent_author_spu_set.contains(absl::StrCat(author_id, "_", ecom_spu_id))) {
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::FREQCONTROL_BUY);
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.qcpx_photo_freq_control_effective_author_spu");
      continue;
    }

    if (enable_inner_photo_coupon_holdout) {
      RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.photo_runner_hold_for_photo");
      p_ad->set_inner_qcpx_filter_reason(kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_IS_NORMAL_HOLDOUT);
      continue;
    }

    if (enable_qcpx_shelf_shield_U0) {
        auto& buyer_effective_type =
            session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
        if ((buyer_effective_type == "U0" && enable_qcpx_shelf_shield_U0_risk)
            || (buyer_effective_type == "" && enable_qcpx_shelf_shield_U0_potential)) {
          session_data->common_w_->SetIntCommonAttr("inner_qcpx_filter_reason",
            kuaishou::ad::InnerQcpxFilterReason::ADMITFAIL_BLACKLIST_U0);  // NOLINT
          continue;
      }
    }
    // 搜后推
    int64_t item_id = p_ad->get_merchant_product_id();
    if (enable_qcpx_photo_search_and_push) {
      p_ad->set_inner_qcpx_search_and_push_ratio(1.0);
      if (recent_search_show_item_set.contains(item_id)) {
        p_ad->set_inner_qcpx_search_and_push_ratio(value_qcpx_photo_search_and_push_ratio);
      }
    }
    qcpx_runner.Process(p_ad);
  }
  qcpx_runner.Monitor();
}

bool QcpxStrategyPlugin::IsFilterAd(const AdCommon* p_ad, bool enable_ecom_qcpx_filter_list) {
  int64_t author_id = p_ad->get_author_id();
  if (RankKconfUtil::qcpxFilterAuthorList() != nullptr) {
    if (RankKconfUtil::qcpxFilterAuthorList()->count(author_id) > 0) {
      return true;
    }
  }
  int64_t item_id = p_ad->get_merchant_product_id();
  if (RankKconfUtil::qcpxFilterItemList() != nullptr && p_ad->Is(AdFlag::is_photo_ad_inner)) {
    if (RankKconfUtil::qcpxFilterItemList()->count(item_id) > 0) {
      return true;
    }
  }
  if (enable_ecom_qcpx_filter_list) {
    std::string author_id_str = absl::StrCat(author_id);
    if (RankKconfUtil::canNotUsePlatformCouponShopIdSet() != nullptr) {
      if (RankKconfUtil::canNotUsePlatformCouponShopIdSet()->count(author_id_str) > 0) {
        return true;
      }
    }
    std::string item_id_str = absl::StrCat(item_id);
    if (RankKconfUtil::canNotUsePlatformCouponItemIdSet() != nullptr && p_ad->Is(AdFlag::is_photo_ad_inner)) {
      if (RankKconfUtil::canNotUsePlatformCouponItemIdSet()->count(item_id_str) > 0) {
        return true;
      }
    }
  }

  return false;
}

}  // namespace ad_rank
}  // namespace ks
