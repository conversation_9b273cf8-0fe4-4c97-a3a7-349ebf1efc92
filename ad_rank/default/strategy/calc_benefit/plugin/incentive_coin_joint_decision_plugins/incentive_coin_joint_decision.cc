#include <algorithm>
#include <cstdint>
#include <memory>
#include <numeric>
#include <string>
#include <valarray>
#include <vector>
#include <unordered_set>

#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/default/strategy/calc_benefit/plugin/incentive_coin_joint_decision_plugins/incentive_coin_joint_decision.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/default/params/calc_benefit_param.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_base/src/common/app_version_compare.h"

using ks::engine_base::PredictType;
namespace ks {
namespace ad_rank {

void IncentiveCoinJointDecision::Clear() {
  uplift_cvr_pts_.clear();
  rewarded_ratio_pts_.clear();
  uplift_cvr_list_.clear();
  rewarded_ratio_list_.clear();
  cvr_adjust_list_.clear();
  deep_quit_rate_list_.clear();
  user_treatment_list_.clear();
  coin_percent_trt_list_.clear();
  treatment_coin_list_.clear();
  treatment_discount_list_.clear();
  rct_treatment_coin_list_.clear();
  rct_treatment_discount_list_.clear();
  product_id_list_.clear();
  cvr_adjust_map_.clear();
  coin_ratio_map_.clear();
  deep_coin_coef_map_.clear();
  discount_coef_map_.clear();
  alpha_map_.clear();
  clip_ratio_map_.clear();
  rct_ratio_map_.clear();
}

bool IncentiveCoinJointDecision::Initialize(ContextData* session_data) {
  plugin_name_ = Name();
  // 一些 holdout or skip 实验
  if (SPDM_enable_live_order_joint_incentive_holdout(session_data->get_spdm_ctx()) &&
      plugin_name_ == "LiveOrderCoinJointDecision") {
    return false;
  }
  if (SPDM_enable_joint_decision_skip_invoked(session_data->get_spdm_ctx()) &&
      plugin_name_ == "InvokedCoinJointDecision") {
    return false;
  }
  // 全局变量初始化
  exp_tag_ = SPDM_incentive_coin_joint_decision_exp_tag(session_data->get_spdm_ctx());
  enable_incentive_coin_joint_decision_ =
    SPDM_enable_incentive_coin_joint_decision(session_data->get_spdm_ctx());
  enable_incentive_joint_decision_skip_twin_bid_ =
    SPDM_enable_incentive_joint_decision_skip_twin_bid(session_data->get_spdm_ctx());
  balance_ratio_ = SPDM_incentive_coin_joint_decision_balance_ratio(session_data->get_spdm_ctx());
  enable_invoked_use_logits_model_ = false;
  enable_conv_use_logits_model_ = false;
  enable_order_use_logits_model_ = false;
  enable_live_order_use_logits_model_ = false;
  enable_live_order_use_ctr_uplift_model_ =
    SPDM_enable_live_order_use_ctr_uplift_model(session_data->get_spdm_ctx());
  use_logits_model_ = false;
  enable_joint_decision_use_uplift_model_output_ =
    SPDM_enable_joint_decision_use_uplift_model_output(session_data->get_spdm_ctx());
  disable_joint_decision_use_ecpc_ratio_ =
    SPDM_disable_joint_decision_use_ecpc_ratio(session_data->get_spdm_ctx());
  disable_live_order_joint_decision_rank_ctr_ =
    SPDM_disable_live_order_joint_decision_rank_ctr(session_data->get_spdm_ctx());
  enable_joint_decision_ecpm_origin_use_rank_cvr_ =
    SPDM_enable_joint_decision_ecpm_origin_use_rank_cvr(session_data->get_spdm_ctx());
  enable_joint_decision_unify_budget_range_ =
    SPDM_enable_joint_decision_unify_budget_range(session_data->get_spdm_ctx());
  enable_joint_decision_link_check_ =
    SPDM_enable_joint_decision_link_check(session_data->get_spdm_ctx());
  incentive_joint_decision_view_coin_roi_bias_ =
    SPDM_incentive_joint_decision_view_coin_roi_bias(session_data->get_spdm_ctx());
  enable_deep_incentive_continuous_decision_ =
    SPDM_enable_deep_incentive_continuous_decision(session_data->get_spdm_ctx());

  const auto& deep_incentive_rct_config = RankKconfUtil::deepIncentiveRctConfig();
  if (!deep_incentive_rct_config) {
    return false;
  }
  /* 通用初始化 */
  if (!HyperParametersInitialize(session_data)) {
    return false;
  }
  // 浅度金币相关初始化
  ViewCoinInitialize(session_data);
  /* 拆分插件类型初始化 */
  if (plugin_name_ == "InvokedCoinJointDecision") {
    enable_invoked_use_logits_model_ = SPDM_enable_invoked_use_logits_model(session_data->get_spdm_ctx());
    treatment_coin_list_ = {0, 100, 200, 600};
    const auto& invoked_treatment_coin_list =
      deep_incentive_rct_config->data().rct_config_v1().invoked_treatment_coin_list();
    rct_treatment_coin_list_.assign(invoked_treatment_coin_list.begin(),
      invoked_treatment_coin_list.end());
    uplift_cvr_pts_ = {
      PredictType::PredictType_incentive_invoked_uplift_cvr_0,
      PredictType::PredictType_incentive_invoked_uplift_cvr_1,
      PredictType::PredictType_incentive_invoked_uplift_cvr_2,
      PredictType::PredictType_incentive_invoked_uplift_cvr_3
    };
    rewarded_ratio_pts_ = {
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_3
    };
    if (enable_invoked_use_logits_model_) {
      treatment_coin_list_ = {0, 100, 200, 300, 400, 500};
      uplift_cvr_pts_ = {
        PredictType::PredictType_incentive_deep_task_uplift_logits_0,
        PredictType::PredictType_incentive_deep_task_uplift_logits_1,
        PredictType::PredictType_incentive_deep_task_uplift_logits_2,
        PredictType::PredictType_incentive_deep_task_uplift_logits_3,
        PredictType::PredictType_incentive_deep_task_uplift_logits_4,
        PredictType::PredictType_incentive_deep_task_uplift_logits_5
      };
      rewarded_ratio_pts_ = {
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_3,
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_4,
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_5
      };
      use_logits_model_ = true;
    }
    return true;
  } else if (plugin_name_ == "ConvCoinJointDecision") {
    enable_conv_use_logits_model_ = SPDM_enable_conv_use_logits_model(session_data->get_spdm_ctx());
    treatment_coin_list_ = {0, 100, 200, 600};
    const auto& conv_treatment_coin_list =
      deep_incentive_rct_config->data().rct_config_v1().conv_treatment_coin_list();
    rct_treatment_coin_list_.assign(conv_treatment_coin_list.begin(),
      conv_treatment_coin_list.end());
    uplift_cvr_pts_ = {
      PredictType::PredictType_incentive_deep_task_uplift_cvr_0,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_1,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_2,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_3
    };
    rewarded_ratio_pts_ = {
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_3
    };
    if (enable_conv_use_logits_model_) {
      treatment_coin_list_ = {0, 100, 300, 500, 700, 1000};
      uplift_cvr_pts_ = {
        PredictType::PredictType_incentive_deep_task_uplift_logits_0,
        PredictType::PredictType_incentive_deep_task_uplift_logits_1,
        PredictType::PredictType_incentive_deep_task_uplift_logits_2,
        PredictType::PredictType_incentive_deep_task_uplift_logits_3,
        PredictType::PredictType_incentive_deep_task_uplift_logits_4,
        PredictType::PredictType_incentive_deep_task_uplift_logits_5
      };
      rewarded_ratio_pts_ = {
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_3,
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_4,
        PredictType::PredictType_incentive_deep_task_rewarded_ratio_5
      };
      use_logits_model_ = true;
    }
    return true;
  } else if (plugin_name_ == "OrderCoinJointDecision") {
    enable_order_use_logits_model_ = SPDM_enable_order_use_logits_model(session_data->get_spdm_ctx());
    treatment_coin_list_ = {0, 1000, 26666, 50000};
    const auto& order_treatment_coin_list =
      deep_incentive_rct_config->data().rct_config_v1().order_treatment_coin_list();
    rct_treatment_coin_list_.assign(order_treatment_coin_list.begin(),
      order_treatment_coin_list.end());
    uplift_cvr_pts_ = {
      PredictType::PredictType_incentive_deep_task_uplift_cvr_0,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_1,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_2,
      PredictType::PredictType_incentive_deep_task_uplift_cvr_3
    };
    if (enable_order_use_logits_model_) {
      treatment_coin_list_ = {0, 10000, 26666, 50000, 80000, 99999};
      uplift_cvr_pts_ = {
        PredictType::PredictType_incentive_deep_task_uplift_logits_0,
        PredictType::PredictType_incentive_deep_task_uplift_logits_1,
        PredictType::PredictType_incentive_deep_task_uplift_logits_2,
        PredictType::PredictType_incentive_deep_task_uplift_logits_3,
        PredictType::PredictType_incentive_deep_task_uplift_logits_4,
        PredictType::PredictType_incentive_deep_task_uplift_logits_5
      };
      use_logits_model_ = true;
    }
    return true;
  } else if (plugin_name_ == "LiveOrderCoinJointDecision") {
    enable_live_order_use_logits_model_ =
      SPDM_enable_live_order_use_logits_model(session_data->get_spdm_ctx());
    auto live_order_treatment_map = RankKconfUtil::liveOrderDeepIncentiveTreatmentMap();
    if (!live_order_treatment_map) {
      return false;
    }
    treatment_discount_list_.clear();
    treatment_coin_list_.clear();
    for (const auto& iter : *live_order_treatment_map) {
      std::string discount_str = iter.first;
      double discount = 0;
      int coin_upper = iter.second;
      if (absl::SimpleAtod(discount_str, &discount)) {
        treatment_discount_list_.push_back(discount);
        treatment_coin_list_.push_back(coin_upper);
      }
    }
    if (enable_live_order_use_logits_model_) {
      treatment_discount_list_ = {0, 0.1, 0.15, 0.2, 0.25, 0.3};
      treatment_coin_list_ = {0, 99999, 1999999, 2999999, 3999999, 4999999};
      uplift_cvr_pts_ = {
        PredictType::PredictType_incentive_deep_task_uplift_logits_0,
        PredictType::PredictType_incentive_deep_task_uplift_logits_1,
        PredictType::PredictType_incentive_deep_task_uplift_logits_2,
        PredictType::PredictType_incentive_deep_task_uplift_logits_3,
        PredictType::PredictType_incentive_deep_task_uplift_logits_4,
        PredictType::PredictType_incentive_deep_task_uplift_logits_5
      };
      uplift_ctr_pts_ = {
        PredictType::PredictType_incentive_deep_task_ctr_uplift_0,
        PredictType::PredictType_incentive_deep_task_ctr_uplift_1,
        PredictType::PredictType_incentive_deep_task_ctr_uplift_2,
        PredictType::PredictType_incentive_deep_task_ctr_uplift_3,
        PredictType::PredictType_incentive_deep_task_ctr_uplift_4,
        PredictType::PredictType_incentive_deep_task_ctr_uplift_5
      };
      use_logits_model_ = true;
    }
    rct_treatment_discount_list_.assign(treatment_discount_list_.begin(), treatment_discount_list_.end());
    rct_treatment_coin_list_.assign(treatment_coin_list_.begin(), treatment_coin_list_.end());
    // 新 rct 在样式修改 ready 后再开启
    if (SPDM_enable_live_order_d_i_use_new_treatment(session_data->get_spdm_ctx())) {
      rct_treatment_discount_list_.clear();
      rct_treatment_coin_list_.clear();
      const auto& live_order_treatment_discount_list =
        deep_incentive_rct_config->data().rct_config_v1().live_order_treatment_discount_list();
      const auto& live_order_treatment_coin_upper_list =
        deep_incentive_rct_config->data().rct_config_v1().live_order_treatment_coin_upper_list();
      rct_treatment_discount_list_.assign(live_order_treatment_discount_list.begin(),
        live_order_treatment_discount_list.end());
      rct_treatment_coin_list_.assign(live_order_treatment_coin_upper_list.begin(),
        live_order_treatment_coin_upper_list.end());
    }
    std::sort(treatment_discount_list_.begin(), treatment_discount_list_.end());
    std::sort(treatment_coin_list_.begin(), treatment_coin_list_.end());
    uplift_cvr_pts_ = {
      PredictType::PredictType_incentive_live_order_ctcvr_uplift_0,
      PredictType::PredictType_incentive_live_order_ctcvr_uplift_1,
      PredictType::PredictType_incentive_live_order_ctcvr_uplift_2
    };
    return true;
  } else {
    return false;
  }
}

bool IncentiveCoinJointDecision::HyperParametersInitialize(ContextData* session_data) {
  const auto& ocpc_action_type_list_ = RankKconfUtil::deepIncentiveAllOcpc();
  if (!ocpc_action_type_list_) {
    return false;
  }
  // cvr 校准
  const std::vector<std::string>& cvr_adjust_ratio_str_list =
      absl::StrSplit(session_data->get_spdm_ctx().TryGetString(absl::StrCat(plugin_name_,
      "_deep_incentive_adjust_cvr_ratio_str"), "1,1"), ",");
  for (auto& cvr_adjust_ratio_str : cvr_adjust_ratio_str_list) {
    double cvr_adjust_raito = 1;
    if (absl::SimpleAtod(cvr_adjust_ratio_str, &cvr_adjust_raito)) {
      cvr_adjust_list_.push_back(cvr_adjust_raito);
    } else {
      cvr_adjust_list_.push_back(1.0);
    }
  }
  coin_ratio_ = session_data->get_spdm_ctx().
    TryGetDouble(absl::StrCat(plugin_name_, "_deep_incentive_coin_ratio"), 1.0);
  view_coin_ratio_ = session_data->get_spdm_ctx().
    TryGetDouble(absl::StrCat(plugin_name_, "_view_coin_ratio"), 1.0);
  deep_coin_coef_ = session_data->get_spdm_ctx().
    TryGetDouble(absl::StrCat(plugin_name_, "_deep_incentive_deep_coin_coef"), 1.0);
  discount_coef_ = session_data->get_spdm_ctx().
    TryGetDouble(absl::StrCat(plugin_name_, "_deep_incentive_discount_coef"), 1.0);
  alpha_ = session_data->get_spdm_ctx().
    TryGetDouble(absl::StrCat(plugin_name_, "_deep_incentive_quit_rate_alpha"), 1.0);
  clip_ratio_ = session_data->get_spdm_ctx().
    TryGetDouble(absl::StrCat(plugin_name_, "_deep_incentive_clip_ratio"), 0.5);
  rct_ratio_ = session_data->get_spdm_ctx().
    TryGetDouble(absl::StrCat(plugin_name_, "_deep_incentive_rct_ratio"), 0.0);
  deep_coin_lower_ = session_data->get_spdm_ctx().
    TryGetInteger(absl::StrCat(plugin_name_, "_deep_incentive_coin_lower"), 50);
  deep_coin_upper_ = session_data->get_spdm_ctx().
    TryGetInteger(absl::StrCat(plugin_name_, "_deep_incentive_rct_upper"), 1000);
  deep_roi_bound_ = session_data->get_spdm_ctx().
    TryGetDouble(absl::StrCat(plugin_name_, "_deep_incentive_roi_bound"), 2.5);
  for (const auto& ocpc_action_type : *ocpc_action_type_list_) {
    std::string key = absl::StrCat(plugin_name_, "_", ocpc_action_type);
    // cvr adjust map
    std::vector<double> tmp_cvr_adjust_list;
    const std::vector<std::string>& tmp_cvr_adjust_ratio_str_list =
      absl::StrSplit(session_data->get_spdm_ctx().TryGetString(absl::StrCat(key,
      "_deep_incentive_adjust_cvr_ratio_str"), "1,1"), ",");
    for (auto& cvr_adjust_ratio_str : tmp_cvr_adjust_ratio_str_list) {
      double cvr_adjust_raito = 1;
      if (absl::SimpleAtod(cvr_adjust_ratio_str, &cvr_adjust_raito)) {
        tmp_cvr_adjust_list.push_back(cvr_adjust_raito);
      } else {
        tmp_cvr_adjust_list.push_back(1.0);
      }
    }
    cvr_adjust_map_.emplace(key, tmp_cvr_adjust_list);
    // 其他超参 map
    double coin_ratio_by_ocpc = session_data->get_spdm_ctx().
      TryGetDouble(absl::StrCat(key, "_deep_incentive_coin_ratio"), 1.0);
    double view_coin_ratio_by_ocpc = session_data->get_spdm_ctx().
      TryGetDouble(absl::StrCat(key, "_view_coin_ratio"), 1.0);
    double deep_coin_coef_by_ocpc = session_data->get_spdm_ctx().
      TryGetDouble(absl::StrCat(key, "_deep_incentive_deep_coin_coef"), 1.0);
    double discount_coef_by_ocpc = session_data->get_spdm_ctx().
      TryGetDouble(absl::StrCat(key, "_deep_incentive_discount_coef"), 1.0);
    double alpha_by_ocpc = session_data->get_spdm_ctx().
      TryGetDouble(absl::StrCat(key, "_deep_incentive_quit_rate_alpha"), 1.0);
    double clip_ratio_by_ocpc = session_data->get_spdm_ctx().
      TryGetDouble(absl::StrCat(key, "_deep_incentive_clip_ratio"), 0.5);
    double rct_ratio_map_by_ocpc = session_data->get_spdm_ctx().
      TryGetDouble(absl::StrCat(key, "_deep_incentive_rct_ratio"), 0);
    int deep_coin_lower_by_ocpc = session_data->get_spdm_ctx().
      TryGetInteger(absl::StrCat(key, "_deep_incentive_coin_lower"), 50);
    int deep_coin_upper_by_ocpc = session_data->get_spdm_ctx().
      TryGetInteger(absl::StrCat(key, "_deep_incentive_coin_upper"), 1000);
    double deep_roi_bound_by_ocpc = session_data->get_spdm_ctx().
      TryGetDouble(absl::StrCat(key, "_deep_incentive_roi_bound"), 2.0);
    if (coin_ratio_by_ocpc != 1) {
      coin_ratio_map_.emplace(key, coin_ratio_by_ocpc);
    }
    if (view_coin_ratio_by_ocpc != 1) {
      view_coin_ratio_map_.emplace(key, view_coin_ratio_by_ocpc);
    }
    if (deep_coin_coef_by_ocpc != 1) {
      deep_coin_coef_map_.emplace(key, deep_coin_coef_by_ocpc);
    }
    if (discount_coef_by_ocpc != 1) {
      discount_coef_map_.emplace(key, discount_coef_by_ocpc);
    }
    if (alpha_by_ocpc != 1) {
      alpha_map_.emplace(key, alpha_by_ocpc);
    }
    if (clip_ratio_by_ocpc != 0.5) {
      clip_ratio_map_.emplace(key, clip_ratio_by_ocpc);
    }
    if (rct_ratio_map_by_ocpc != 0) {
      rct_ratio_map_.emplace(key, rct_ratio_map_by_ocpc);
    }
    if (deep_coin_lower_by_ocpc != 50) {
      deep_coin_lower_map_.emplace(key, deep_coin_lower_by_ocpc);
    }
    if (deep_coin_upper_by_ocpc != 1000) {
      deep_coin_upper_map_.emplace(key, deep_coin_upper_by_ocpc);
    }
    if (deep_roi_bound_by_ocpc != 2.0) {
      deep_roi_bound_map_.emplace(key, deep_roi_bound_by_ocpc);
    }
  }
  return true;
}

void IncentiveCoinJointDecision::ViewCoinInitialize(ContextData* session_data) {
  session_data->set_incentive_rct_flag(0);
  spdm_reward_rate_ = session_data->get_spdm_ctx().TryGetDouble("reward_rate", 1.0);
  spdm_coin_upper_ = SPDM_unify_coin_upper(session_data->get_spdm_ctx());
  spdm_coin_lower_ = SPDM_unify_coin_lower(session_data->get_spdm_ctx());
  unify_calc_coin_default_roi_ = SPDM_unify_calc_coin_default_roi(session_data->get_spdm_ctx());
  treatment_max_num_ = SPDM_incentive_ad_shallow_treatment_max_num(session_data->get_spdm_ctx());
  spdm_refactor_ad_trt_ = SPDM_enable_refactor_ucc_ad_trt(session_data->get_spdm_ctx());
  spdm_rank_has_more_ = SPDM_enable_rank_has_more(session_data->get_spdm_ctx());
  spdm_rank_has_more_thres_ = SPDM_rank_has_more_thres(session_data->get_spdm_ctx());
  fix_ucc_next_value_log_ = SPDM_fix_ucc_next_value_log(session_data->get_spdm_ctx());
  task_cnt_control_coef_ = SPDM_unify_calc_coin_task_cnt_control_coef(session_data->get_spdm_ctx());
  spdm_enable_low_value_req_def_stg_ = SPDM_enable_ucc_low_value_req_def_stg(session_data->get_spdm_ctx());
  spdm_low_value_req_thres_ = SPDM_ucc_low_value_req_thres(session_data->get_spdm_ctx());
  spdm_trt_use_percent_ = SPDM_unify_calc_coin_trt_use_percent(session_data->get_spdm_ctx());
  fix_ucc_get_quit_rate_ = SPDM_fix_ucc_get_quit_rate(session_data->get_spdm_ctx());
  const auto& gross_max_sub_page_id_blacklist = RankKconfUtil::grossMaxSubPageIdBlackList();
  const auto& watch_video_sub_page_set = RankKconfUtil::watchVideoSubpageSet();
  const auto& watch_video_page_set = RankKconfUtil::watchVideoPageSet();

  // 部分 sub page id 跳过毛利排序
  if (gross_max_sub_page_id_blacklist) {
    auto iter =
          gross_max_sub_page_id_blacklist->find(std::to_string(session_data->get_sub_page_id()));
    if (iter != gross_max_sub_page_id_blacklist->end() &&
        session_data->get_spdm_ctx().TryGetBoolean(iter->second, false)) {
      spdm_reward_rate_ = 0;
    }
  }

  std::string sub_page_id_coef_key =
      absl::StrCat("unify_calc_coin_def_stg_roi_bias_subpage_", session_data->get_sub_page_id());
  unify_calc_coin_default_roi_ += session_data->get_spdm_ctx().TryGetDouble(sub_page_id_coef_key, 0.0);

  alpha_bias_ = 0.0;
  roi_control_coef_ = SPDM_incntv_coin_roi_control_coef(session_data->get_spdm_ctx());
  auto& exp_tags = *(RankKconfUtil::UnifyCalcCoinRoiCoefBiasExpTag());
  if (!exp_tags.empty()) {
    for (const auto& tag : exp_tags) {
      std::string exp_roi_coef = absl::StrCat("unify_calc_coin_def_stg_roi_bias_exp_tag_", tag);
      unify_calc_coin_default_roi_ += session_data->get_spdm_ctx().TryGetDouble(exp_roi_coef, 0.0);

      std::string exp_alpha_coef = absl::StrCat("unify_calc_coin_exploit_alpha_bias_exp_tag_", tag);
      alpha_bias_ += session_data->get_spdm_ctx().TryGetDouble(exp_alpha_coef, 0.0);
      roi_control_coef_ += session_data->get_spdm_ctx().TryGetDouble(exp_alpha_coef, 0.0);
    }
  }

  if (SPDM_enable_budget_allocation_user_roi_alpha_bias(session_data->get_spdm_ctx())) {
    unify_calc_coin_default_roi_ +=
        session_data->get_rank_request()->ad_request().ad_user_info().incntv_ad_roi_bias();
    roi_control_coef_ += session_data->get_rank_request()->ad_request().ad_user_info().incntv_ad_alpha_bias();
  }

  unify_calc_coin_default_roi_ += incentive_joint_decision_view_coin_roi_bias_;

  if (unify_calc_coin_default_roi_ <= 0) {
    unify_calc_coin_default_roi_ = 0.1;
  }
  if (roi_control_coef_ <= 0) {
    roi_control_coef_ = 0.000001;
  }

  const auto& coin_upper_json_kconf = RankKconfUtil::incntvAdUnifyCalcCoinSubPageIdToCoinUpperV2();
  std::string exp_tag = SPDM_unify_calc_coin_upper_exp_tag(session_data->get_spdm_ctx());
  for (const auto& rule : coin_upper_json_kconf->data().coin_upper_conf) {
    if (rule.exp_tag == exp_tag) {
      std::string subpage_key = absl::StrCat(session_data->get_sub_page_id());
      if (rule.subpage_to_upper.count(subpage_key) > 0) {
        spdm_coin_upper_ = rule.subpage_to_upper.find(subpage_key)->second;
      } else {
        spdm_coin_upper_ = rule.default_value;
      }
      break;
    }
  }

  FillContextInfo(session_data);
}

void IncentiveCoinJointDecision::FillContextInfo(ContextData* session_data) {
  ctx_pre1_award_price_ = 0.0;
  ctx_pre5_avg_price_ = 0.0;
  ctx_pre10_avg_price_ = 0.0;
  ctx_pre20_avg_price_ = 0.0;
  ctx_pre1_award_view_coin_ = 0.0;
  ctx_pre20_avg_view_coin_ = 0.0;
  ctx_day_task_idx_ = 0.0;
  ctx_view_coin_amt_ = 0.0;
  ctx_view_coin_cnt_ = 0.0;

  auto& incentive_task_info =
      session_data->get_rank_request()->ad_request().inspire_req_info().task_progress_info();
  if (incentive_task_info.task_id() == 0) {
    return;
  }
  auto& task_reward_record_list = incentive_task_info.task_reward_record_list();
  double seq_total_view_cnt = 0.0;
  for (int i = 0; i < task_reward_record_list.size(); i++) {
    auto& task_info = task_reward_record_list[i];
    if (task_info.task_type() == kuaishou::ad::InspireTaskProgressInfo_InspireTaskType_COMMON_BIZ ||
        task_info.task_type() == kuaishou::ad::InspireTaskProgressInfo_InspireTaskType_ONCE_AGAIN_BIZ) {
      // 减少 cpa / cpc 的 price 不可比影响
      double price = task_info.ad_cpm() > 0 && kBenifitFactor > 0
                          ? std::min(1.0 * task_info.ad_cpm() / kBenifitFactor, 1.0 * task_info.ad_price())
                          : 1.0 * task_info.ad_price();
      if (ctx_pre1_award_view_coin_ == 0) {
        ctx_pre1_award_view_coin_ = task_info.reward_amount();
        ctx_pre1_award_price_ = price;
      }
      if (seq_total_view_cnt < 5) {
        ctx_pre5_avg_price_ += price;
      }
      if (seq_total_view_cnt < 10) {
        ctx_pre10_avg_price_ += price;
      }
      ctx_pre20_avg_price_ += price;
      ctx_pre20_avg_view_coin_ += task_info.reward_amount();
      seq_total_view_cnt += 1.0;
    }
  }
  if (seq_total_view_cnt > 0) {
    ctx_pre5_avg_price_ = ctx_pre5_avg_price_ / std::min(seq_total_view_cnt, 5.0);
    ctx_pre10_avg_price_ = ctx_pre10_avg_price_ / std::min(seq_total_view_cnt, 10.0);
    ctx_pre20_avg_price_ = ctx_pre20_avg_price_ / seq_total_view_cnt;
    ctx_pre20_avg_view_coin_ = ctx_pre20_avg_view_coin_ / seq_total_view_cnt;
  }

  const auto& cum_info_map = incentive_task_info.total_reward_sum_map();
  // 取值可枚举，只有 4 个，InspireTaskType:
  // UNKNOWN_BIZ/COMMON_BIZ/ONCE_AGAIN_BIZ/ACTIVATE_APP_BIZ/INVOKED_APP_BIZ
  for (auto& iter : cum_info_map) {
    std::string key = iter.first;
    if (key == "COMMON_BIZ" || key == "ONCE_AGAIN_BIZ") {
      ctx_view_coin_cnt_ += iter.second.total_reward_count();
      ctx_view_coin_amt_ += iter.second.total_reward_amount();
      if (key == "COMMON_BIZ") {
        ctx_day_task_idx_ += iter.second.total_reward_count();
      }
    }
  }

  struct tm tm_info;
  time_t current_time = session_data->get_current_timestamp_nodiff() / 1000000;
  localtime_r(&current_time, &tm_info);
  cur_hour_ = tm_info.tm_hour;
}

bool IncentiveCoinJointDecision::PvAdmit(ContextData* session_data) {
  rct_valid_ = false;
  decision_valid_ = false;
  const auto& deep_incentive_rct_config = RankKconfUtil::deepIncentiveRctConfig();
  if (!deep_incentive_rct_config) {
    return false;
  }
  // 风控判断
  if (SPDM_enable_risk_user_skip_deep_incentive(session_data->get_spdm_ctx())) {
    int reward_antispam_code = session_data->get_rank_request()->ad_request().front_antispam_code();
    base::Json json_extra = base::Json(base::StringToJson(
        session_data->get_rank_request()->ad_request().extra_request_ctx()));
    int live_antispam_code = 0;
    if (json_extra.IsObject()) {
      live_antispam_code = json_extra.GetInt("liveAntispamCode", 0);
    }
    if (reward_antispam_code > 0 || live_antispam_code == 1001) {
      return false;
    }
  }

  if (plugin_name_ == "InvokedCoinJointDecision") {
    // 填充 product_id_list_
    std::string reward_type = "invoke";
    int task_complete_num =
      GetIncentiveStyleTaskCompleteCount(session_data, &reward_type, &product_id_list_);
    // sub_page_id 准入
    const auto& decision_admit_sub_page_id = RankKconfUtil::invokedAdmitSubPageIdSet();
    const auto& rct_admit_sub_page_id =
      deep_incentive_rct_config->data().rct_sub_page_id().invoked_sub_page_id();
    bool sub_page_id_admit_1 = session_data->get_spdm_ctx().TryGetBoolean(
      absl::StrCat(plugin_name_, "_invoked_d_i_admit_v1_", session_data->get_sub_page_id()), false);
    bool sub_page_id_admit_2 = session_data->get_spdm_ctx().TryGetBoolean(
      absl::StrCat(plugin_name_, "_invoked_d_i_admit_v2_", session_data->get_sub_page_id()), false);
    if (decision_admit_sub_page_id->count(session_data->get_sub_page_id()) || sub_page_id_admit_1 ||
        sub_page_id_admit_2) {
      decision_valid_ = true;
    }
    if (std::find(rct_admit_sub_page_id.begin(), rct_admit_sub_page_id.end(),
        session_data->get_sub_page_id()) != rct_admit_sub_page_id.end()) {
      rct_valid_ = true;
    }
  } else if (plugin_name_ == "ConvCoinJointDecision") {
    // ios 不出
    if (session_data->get_is_ios_platform()) {
      return false;
    }
    // sub_page_id 准入
    const auto& decision_admit_sub_page_id = RankKconfUtil::convAdmitSubPageIdSet();
    const auto& rct_admit_sub_page_id =
      deep_incentive_rct_config->data().rct_sub_page_id().conv_sub_page_id();
    bool sub_page_id_admit_1 = session_data->get_spdm_ctx().TryGetBoolean(
      absl::StrCat(plugin_name_, "_conv_d_i_admit_v1_", session_data->get_sub_page_id()), false);
    bool sub_page_id_admit_2 = session_data->get_spdm_ctx().TryGetBoolean(
      absl::StrCat(plugin_name_, "_conv_d_i_admit_v2_", session_data->get_sub_page_id()), false);
    if (decision_admit_sub_page_id->count(session_data->get_sub_page_id()) || sub_page_id_admit_1 ||
        sub_page_id_admit_2) {
      decision_valid_ = true;
    }
    if (std::find(rct_admit_sub_page_id.begin(), rct_admit_sub_page_id.end(),
        session_data->get_sub_page_id()) != rct_admit_sub_page_id.end()) {
      rct_valid_ = true;
    }
  } else if (plugin_name_ == "OrderCoinJointDecision") {
    // ios 不出
    if (session_data->get_is_ios_platform()) {
      return false;
    }
    if (SPDM_enable_order_d_i_holdout_for_qcpx(session_data->get_spdm_ctx())) {
      return false;
    }
    // sub_page_id 准入
    const auto& decision_admit_sub_page_id = RankKconfUtil::orderAdmitSubPageIdSet();
    const auto& rct_admit_sub_page_id =
      deep_incentive_rct_config->data().rct_sub_page_id().order_sub_page_id();
    bool sub_page_id_admit_1 = session_data->get_spdm_ctx().TryGetBoolean(
      absl::StrCat(plugin_name_, "_order_d_i_admit_v1_", session_data->get_sub_page_id()), false);
    bool sub_page_id_admit_2 = session_data->get_spdm_ctx().TryGetBoolean(
      absl::StrCat(plugin_name_, "_order_d_i_admit_v2_", session_data->get_sub_page_id()), false);
    if (decision_admit_sub_page_id->count(session_data->get_sub_page_id()) || sub_page_id_admit_1 ||
        sub_page_id_admit_2) {
      decision_valid_ = true;
    }
    if (std::find(rct_admit_sub_page_id.begin(), rct_admit_sub_page_id.end(),
        session_data->get_sub_page_id()) != rct_admit_sub_page_id.end()) {
      rct_valid_ = true;
    }
  } else if (plugin_name_ == "LiveOrderCoinJointDecision") {
    // ios 不出
    if (session_data->get_is_ios_platform()) {
      return false;
    }
    // 版控
    if (ks::ad_base::AppVersionCompare::Compare(
        session_data->get_rank_request()->ad_request().ad_user_info().platform_version(), "13.3.30") < 0) {
      return false;
    }
    if (SPDM_enable_live_order_d_i_holdout_for_qcpx(session_data->get_spdm_ctx()) ||
        SPDM_enable_live_roas_d_i_holdout_for_qcpx(session_data->get_spdm_ctx())) {
      return false;
    }
    // sub_page_id 准入
    const auto& decision_admit_sub_page_id = RankKconfUtil::liveOrderPayAdmitSubPageIds();
    const auto& rct_admit_sub_page_id =
      deep_incentive_rct_config->data().rct_sub_page_id().live_order_sub_page_id();
    bool sub_page_id_admit_1 = session_data->get_spdm_ctx().TryGetBoolean(
      absl::StrCat(plugin_name_, "_live_order_d_i_admit_v1_", session_data->get_sub_page_id()), false);
    bool sub_page_id_admit_2 = session_data->get_spdm_ctx().TryGetBoolean(
      absl::StrCat(plugin_name_, "_live_order_d_i_admit_v2_", session_data->get_sub_page_id()), false);
    if (decision_admit_sub_page_id->count(session_data->get_sub_page_id()) || sub_page_id_admit_1 ||
        sub_page_id_admit_2) {
      decision_valid_ = true;
    }
    if (std::find(rct_admit_sub_page_id.begin(), rct_admit_sub_page_id.end(),
        session_data->get_sub_page_id()) != rct_admit_sub_page_id.end()) {
      rct_valid_ = true;
    }
  }

  return rct_valid_ || decision_valid_;
}

void IncentiveCoinJointDecision::AdAdmit(ContextData* session_data, AdList* adlist) {
  const auto& invoked_admit_config = RankKconfUtil::incentiveInvokedDeepIncentiveRatioConfig();
  const auto& conv_admit_config = RankKconfUtil::incentiveConvDeepIncentiveRatioConfig();
  const auto& d_i_site_page_product_name_config =
    RankKconfUtil::incentiveUpliftDecisionSitePageProductNameWhiteList();
  const auto& coin_order_paied_black_author_list = RankKconfUtil::coinOrderPaiedBlackAuthorList();
  const auto& live_order_black_author_list = RankKconfUtil::LiveOrderBlackAuthorList();

  for (auto* p_ad : adlist->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    p_ad->set_incentive_coin_joint_decision_valid(false);
    if (plugin_name_ == "InvokedCoinJointDecision" && invoked_admit_config) {
      if (!(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED)) {
        continue;
      }
      if (enable_joint_decision_unify_budget_range_ && p_ad->Is(AdFlag::IsNativeAd)) {
        continue;
      }
      if (p_ad->get_item_type() != kuaishou::ad::AdEnum::ITEM_PHOTO) {
        continue;
      }
      // 单产品 id 频控
      int64_t product_id = conv_(p_ad->get_product_name());
      if (product_id_list_.count(product_id) > 0) {
        continue;
      }
      // 控比逻辑
      const auto& account_config = invoked_admit_config->data().account_id_config();
      const auto& product_name_config = invoked_admit_config->data().product_name_config();
      double random_number = ad_base::AdRandom::GetDouble();
      double invoked_style_ratio = -1;
      if (account_config.find(absl::StrCat(p_ad->get_account_id())) != account_config.end()) {
        auto iter = account_config.find(absl::StrCat(p_ad->get_account_id()));
        invoked_style_ratio = iter->second;
      } else if (product_name_config.find(p_ad->get_product_name()) != product_name_config.end()) {
        auto iter = product_name_config.find(p_ad->get_product_name());
        invoked_style_ratio = iter->second;
      }
      if (invoked_style_ratio != -1 && random_number > invoked_style_ratio) {
        continue;
      }
      p_ad->set_incentive_coin_joint_decision_valid(true);
    } else if (plugin_name_ == "ConvCoinJointDecision" && conv_admit_config &&
        d_i_site_page_product_name_config) {
      if (!((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION)) {
        continue;
      }
      if (p_ad->get_item_type() != kuaishou::ad::AdEnum::ITEM_PHOTO) {
        continue;
      }
      if (enable_joint_decision_unify_budget_range_ && p_ad->Is(AdFlag::IsNativeAd)) {
        continue;
      }
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) {
        // 控比逻辑
        const auto& account_config = conv_admit_config->data().account_id_config();
        const auto& product_name_config = conv_admit_config->data().product_name_config();
        double random_number = ad_base::AdRandom::GetDouble();
        double conv_style_ratio = -1;
        // 双出价直接跳过
        if (enable_incentive_joint_decision_skip_twin_bid_ && p_ad->get_deep_conversion_type() !=
            kuaishou::ad::AdCallbackLog_EventType_EVENT_UNKNOWN) {
          continue;
        }
        // 账户 id 控比优先级更高
        if (account_config.count(absl::StrCat(p_ad->get_account_id()))) {
          auto iter = account_config.find(absl::StrCat(p_ad->get_account_id()));
          conv_style_ratio = iter->second;
        } else if (product_name_config.count(p_ad->get_product_name())) {
          auto iter = product_name_config.find(p_ad->get_product_name());
          conv_style_ratio = iter->second;
        }
        if (conv_style_ratio == -1 && p_ad->get_deep_conversion_type() !=
            kuaishou::ad::AdCallbackLog_EventType_EVENT_UNKNOWN) {
          continue;
        } else if (conv_style_ratio != -1 && random_number > conv_style_ratio) {
          continue;
        }
      } else if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE) {
        if (!d_i_site_page_product_name_config->count(p_ad->get_product_name())) {
          continue;
        }
      }
      p_ad->set_incentive_coin_joint_decision_valid(true);
    } else if (plugin_name_ == "OrderCoinJointDecision" && coin_order_paied_black_author_list) {
      if (enable_joint_decision_unify_budget_range_ &&
          p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
        continue;
      }
      if (!(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI))) {
        continue;
      }
      if (p_ad->get_item_type() != kuaishou::ad::AdEnum::ITEM_PHOTO) {
        continue;
      }
      if (coin_order_paied_black_author_list->count(p_ad->get_author_id())) {
        continue;
      }
      p_ad->set_incentive_coin_joint_decision_valid(true);
    } else if (plugin_name_ == "LiveOrderCoinJointDecision" && live_order_black_author_list) {
      if (!(p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI))) {
        continue;
      }
      if (live_order_black_author_list->count(p_ad->get_author_id())) {
        continue;
      }
      p_ad->set_incentive_coin_joint_decision_valid(true);
    }
    RANK_DOT_COUNT(session_data, 1, plugin_name_, absl::StrCat(session_data->get_sub_page_id()),
      exp_tag_);
  }
}

void IncentiveCoinJointDecision::CalcExpectedPriceFromCpm(ContextData* session_data, AdList* adlist) {
  auto& ads = adlist->Ads();
  for (auto* p_ad : ads) {
    if (p_ad == nullptr) {
      continue;
    }
    if (!p_ad->get_incentive_coin_joint_decision_valid() || p_ad->get_incentive_deep_coin_rct_tag() > 0) {
      continue;
    }
    double ad_e_price = p_ad->get_auto_cpa_bid() *
      p_ad->get_unify_ctr_info().value * p_ad->get_unify_cvr_info().value;
    double ecpc_ratio =
      p_ad->Attr(ItemIdx::ecpc_max_min_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
    if (ecpc_ratio > 0) {
      ad_e_price *= ecpc_ratio;
    }
    p_ad->set_incntv_ad_e_price(ad_e_price);
    RANK_DOT_STATS(session_data, ad_e_price * 1000, absl::StrCat(plugin_name_ + "_e_price"),
      absl::StrCat(session_data->get_sub_page_id()), absl::StrCat(p_ad->get_ocpx_action_type()), exp_tag_);
  }
}

void IncentiveCoinJointDecision::GenerateCandidateTreatmentByAd(ContextData* session_data, AdList* adlist) {
  // 离散固定档位的金币，计算好的金币写入 user_treatment_list_
  auto& coin_treatment_list = *(RankKconfUtil::adShallowIncentiveCoinTreatmentList());
  std::sort(coin_treatment_list.begin(), coin_treatment_list.end());
  for (int i = 0; i < coin_treatment_list.size(); i++) {
    int coin = coin_treatment_list[i];
    if (coin >= spdm_coin_lower_ && coin <= spdm_coin_upper_) {
      user_treatment_list_.push_back(coin);
    }
  }

  // DefStg * 百分比的金币，计算好的金币写入 coin_percent_trt_list_
  bool enable_spdm_percent_trt = SPDM_enable_unify_calc_coin_percent_trt(session_data->get_spdm_ctx());
  if (enable_spdm_percent_trt) {
    const std::vector<std::string> spdm_percent_trt_str =
        absl::StrSplit(SPDM_unify_calc_coin_percent_trt_str(session_data->get_spdm_ctx()), ",");
    std::vector<double> percent_trt_list;
    for (auto& trt_str : spdm_percent_trt_str) {
      int trt_int = 0;
      if (absl::SimpleAtoi(trt_str, &trt_int)) {
        percent_trt_list.push_back(trt_int / 100.0);
      }
    }
    for (double v : percent_trt_list) { coin_percent_trt_list_.push_back(v); }
  }
  // 关闭 或者 兜底
  if (!enable_spdm_percent_trt || coin_percent_trt_list_.empty()) {
    auto& kconf_percent_trt_list = *(RankKconfUtil::adShallowIncentiveCoinPercentTreatmentList());
    for (double v : kconf_percent_trt_list) { coin_percent_trt_list_.push_back(v); }
  }
  std::sort(coin_percent_trt_list_.begin(), coin_percent_trt_list_.end());
  std::vector<int64_t> def_lower_coin_percent_trt_list;
  std::vector<int64_t> def_upper_coin_percent_trt_list;
  int percent_trt_size = coin_percent_trt_list_.size();
  if (!coin_percent_trt_list_.empty()) {
    double base_percent = 1 + coin_percent_trt_list_[0];
    if (base_percent > 0) {
      for (auto percent : coin_percent_trt_list_) {
        int64_t lower_coin_mul_percent = (1 + percent) / base_percent * spdm_coin_lower_;
        def_lower_coin_percent_trt_list.push_back(lower_coin_mul_percent);
      }
    }
    base_percent = 1 + coin_percent_trt_list_[percent_trt_size - 1];
    if (base_percent > 0) {
      for (auto percent : coin_percent_trt_list_) {
        int64_t upper_coin_mul_percent = (1 + percent) / base_percent * spdm_coin_upper_;
        def_upper_coin_percent_trt_list.push_back(upper_coin_mul_percent);
      }
    }
  }

  if (spdm_refactor_ad_trt_) {
    // 把 trt 分为 决策使用的 trt 和 模型使用的 trt。
    // 1. 模型 trt 跟 RCT 和 模型 cmd 绑定，放到 reward_coin_treatment_str 里
    // 2. 决策 trt 通过 GetQuitRate 方法 将 模型 trt 的预估值 转化为 决策 trt 的预估值 放到
    //    reward_coin_treatment_list 里
    auto& ads = adlist->Ads();
    for (auto* p_ad : ads) {
      if (!p_ad) {
        continue;
      }
      if (!p_ad->get_incentive_coin_joint_decision_valid() || p_ad->get_incentive_deep_coin_rct_tag() > 0) {
        continue;
      }
      int64_t ad_coin = std::round(p_ad->get_incntv_ad_e_price() * 10 / unify_calc_coin_default_roi_);
      std::vector<int64_t> decision_trt;
      std::vector<int64_t> model_trt;
      // 默认方式，离散档位的金币值 做 RCT
      if (treatment_max_num_ >= user_treatment_list_.size()) {
        model_trt = user_treatment_list_;
        decision_trt = user_treatment_list_;
      } else {
        int right = 0;
        for (; right < user_treatment_list_.size(); right++) {
          if (ad_coin < user_treatment_list_[right]) {
            break;
          }
        }
        int left = right - 1;
        for (int i = treatment_max_num_; i > 0; i--) {
          if (left < 0) {
            right++;
          } else if (right >= user_treatment_list_.size() ||
                     ad_coin - user_treatment_list_[left] <= user_treatment_list_[right] - ad_coin) {
            left--;
          } else {
            right++;
          }
        }
        for (int i = left + 1; i < right; i++) {
          model_trt.push_back(user_treatment_list_[i]);
          decision_trt.push_back(user_treatment_list_[i]);
        }
      }

      // 如果 决策 trt 使用 百分比金币值
      if (spdm_trt_use_percent_) {
        decision_trt.clear();
        // coin_percent_trt_list_ 中放的是 百分比小数， [-0.3,-0.2,-0.1,0.1,0.2,0.3]
        if (coin_percent_trt_list_.size() > 0 && coin_percent_trt_list_[0] > -1.0) {
          // 先判断 第一个有没有突破下限 和 最后一个有没有突破上限，保证每次 都是相同的 trt 数量
          int last_index = coin_percent_trt_list_.size() - 1;
          if (ad_coin * (1 + coin_percent_trt_list_[0]) < spdm_coin_lower_) {
            decision_trt = def_lower_coin_percent_trt_list;
          } else if (ad_coin * (1 + coin_percent_trt_list_[last_index]) > spdm_coin_upper_) {
            decision_trt = def_upper_coin_percent_trt_list;
          } else {
            for (auto percent : coin_percent_trt_list_) {
              int64_t one_plus_percent_coin = static_cast<int64_t>(ad_coin * (1 + percent));
              decision_trt.push_back(one_plus_percent_coin);
            }
          }
        }
      }

      // 埋点
      for (int64_t trt_coin : decision_trt) {
        p_ad->mutable_reward_coin_treatment_list()->push_back(trt_coin);
      }
      std::string model_trt_str = absl::StrJoin(model_trt, ",");
      p_ad->set_reward_coin_treatment_str(model_trt_str);
    }
  } else {
    auto& ads = adlist->Ads();
    for (auto* p_ad : ads) {
      if (p_ad == nullptr) {
        continue;
      }
      if (!p_ad->get_incentive_coin_joint_decision_valid() || p_ad->get_incentive_deep_coin_rct_tag() > 0) {
        continue;
      }
      int64_t ad_coin = std::round(p_ad->get_incntv_ad_e_price() * 10 / unify_calc_coin_default_roi_);
      std::vector<int64_t> ad_trt;     // ad 自身的 rct 候选 trt list
      if (spdm_trt_use_percent_) {
        // coin_percent_trt_list_ 中放的是 百分比小数， [-0.3,-0.2,-0.1,0.1,0.2,0.3]
        if (coin_percent_trt_list_.size() > 0 && coin_percent_trt_list_[0] > -1.0) {
          // 先判断 第一个有没有突破下限 和 最后一个有没有突破上限，保证每次 都是相同的 trt 数量
          int last_index = coin_percent_trt_list_.size() - 1;
          if (ad_coin * (1 + coin_percent_trt_list_[0]) < spdm_coin_lower_) {
            ad_trt = def_lower_coin_percent_trt_list;
          } else if (ad_coin * (1 + coin_percent_trt_list_[last_index]) > spdm_coin_upper_) {
            ad_trt = def_upper_coin_percent_trt_list;
          } else {
            for (auto percent : coin_percent_trt_list_) {
              int64_t one_plus_percent_coin = static_cast<int64_t>(ad_coin * (1 + percent));
              ad_trt.push_back(one_plus_percent_coin);
            }
          }
        }
      } else if (treatment_max_num_ >= user_treatment_list_.size()) {
        ad_trt = user_treatment_list_;
      } else {
        int right = 0;
        for (; right < user_treatment_list_.size(); right++) {
          if (ad_coin < user_treatment_list_[right]) {
            break;
          }
        }
        int left = right - 1;
        for (int i = treatment_max_num_; i > 0; i--) {
          if (left < 0) {
            right++;
          } else if (right >= user_treatment_list_.size() ||
                     ad_coin - user_treatment_list_[left] <= user_treatment_list_[right] - ad_coin) {
            left--;
          } else {
            right++;
          }
        }
        for (int i = left + 1; i < right; i++) { ad_trt.push_back(user_treatment_list_[i]); }
      }
      for (int64_t trt_coin : ad_trt) { p_ad->mutable_reward_coin_treatment_list()->push_back(trt_coin); }
      std::string ad_trt_str = absl::StrJoin(ad_trt, ",");
      p_ad->set_reward_coin_treatment_str(ad_trt_str);
    }
  }
}

void IncentiveCoinJointDecision::GetExpectedValue(ContextData* session_data, AdList* adlist) {
  // 长期价值系数
  lt_value_coef_ = 1.0;
  auto& task_progress_info =
      session_data->get_rank_request()->ad_request().inspire_req_info().task_progress_info();
  spdm_cpm2value_coef_ = SPDM_incntv_cpm2value_coef(session_data->get_spdm_ctx());

  bool use_mdp_ltv = SPDM_enable_ucc_mdp_ltv(session_data->get_spdm_ctx());
  double mdp_ltv_coef = SPDM_ucc_mdp_ltv_coef(session_data->get_spdm_ctx());
  double mdp_ltv_upper_coef = SPDM_ucc_mdp_ltv_upper_coef(session_data->get_spdm_ctx());
  std::string mdp_ltv_exp_key = SPDM_ucc_mdp_ltv_exp_key(session_data->get_spdm_ctx());
  const auto& entry_map = *(RankKconfUtil::incntvAdSubPageIdToEntry());
  std::string entry_key = "";
  auto iter = entry_map.find(session_data->get_sub_page_id());
  if (iter != entry_map.end()) {
    entry_key = iter->second;
  }
  bool use_model_ltv = SPDM_enable_ucc_mdp_ltv_model(session_data->get_spdm_ctx());
  double model_ltv_coef_weight = SPDM_ucc_mdp_ltv_model_coef_weight(session_data->get_spdm_ctx());
  bool ltv_model_predict_expected_value =
      SPDM_enable_ucc_mdp_ltv_model_predict_expected_value(session_data->get_spdm_ctx());
  bool enable_roll_back_evalue = SPDM_enable_ucc_mdp_roll_back_evalue(session_data->get_spdm_ctx());
  bool rool_back_evalue_thres = SPDM_ucc_mdp_roll_back_evalue_thres(session_data->get_spdm_ctx());
  bool enable_normalize_evalue = SPDM_enable_ucc_mdp_normalize_evalue(session_data->get_spdm_ctx());
  double session_expected_value = 0.0;
  for (auto* p_ad : adlist->Ads()) {
    if (!p_ad) {
      continue;
    }
    if (!p_ad->get_incentive_coin_joint_decision_valid() || p_ad->get_incentive_deep_coin_rct_tag() > 0) {
      continue;
    }
    double expected_value = p_ad->get_incntv_ad_e_price();  // 默认值 cpm
    // use_mdp_ltv 是否 额外计算 长期价值， false 时候 直接用 cpm
    // use_model_ltv 用 NN 模型打分，false 时候用树模型
    if (use_mdp_ltv && use_model_ltv) {
      if (ltv_model_predict_expected_value) {   // 模型预估价值
        // 如果不满足 thres 则沿用 原来的 evalue 当次 cpm
        // for 有转化行为后，用户短期和长期价值快速增长的情况
        if (!enable_roll_back_evalue ||
            p_ad->get_incntv_ltv() > p_ad->get_incntv_ad_e_price() * rool_back_evalue_thres) {
          expected_value = p_ad->get_incntv_ltv();
        }
      } else {    // 模型预估 价值 加 cpm
        // 避免 后续 rb 中 使用的 长期价值 失去物理含义
        expected_value += p_ad->get_incntv_ltv() * model_ltv_coef_weight;
        if (enable_normalize_evalue && 1 + model_ltv_coef_weight != 0) {
          expected_value = expected_value / (1 + model_ltv_coef_weight);
        }
      }
    } else if (use_mdp_ltv && !mdp_ltv_exp_key.empty() && !entry_key.empty()) {
      double mdp_ltv = 0.0;
      mdp_ltv = GetMdpLtvByKconfV2(p_ad, session_data, absl::StrCat(mdp_ltv_exp_key, "-", entry_key));
      mdp_ltv = std::min(mdp_ltv * mdp_ltv_coef, expected_value * mdp_ltv_upper_coef);
      expected_value += mdp_ltv;
    }
    p_ad->set_incentive_expected_value(expected_value);
    if (expected_value > session_expected_value) {
      session_expected_value = expected_value;
    }
  }
  session_data->set_incentive_expected_value(session_expected_value);
}

double IncentiveCoinJointDecision::GetMdpLtvByKconfV2(AdCommon* p_ad, ContextData* session_data,
                                               std::string exp_key) {
  const auto& ltv_kconf = RankKconfUtil::incntvAdUnifyCalcCoinMdpLtvV2();
  auto iter = ltv_kconf->data().exp_tag_to_kv_list.find(exp_key);
  if (iter == ltv_kconf->data().exp_tag_to_kv_list.end()) {
    return 0.0;
  }
  auto spec_ltv_kconf = iter->second;
  auto key_name_list = spec_ltv_kconf.key_name_list;
  auto key_value_list = spec_ltv_kconf.key_value_list;
  std::vector<double> p_ad_key_value;
  for (const auto& key_name : key_name_list) {
    if (key_name == "cpm") {
      p_ad_key_value.push_back(p_ad->get_incntv_ad_e_price());
    } else if (key_name == "p_next") {
      p_ad_key_value.push_back(1 - GetQuitRate(p_ad, p_ad->get_reward_coin()));
    } else if (key_name == "pre5_avg_price") {
      p_ad_key_value.push_back(ctx_pre5_avg_price_);
    } else if (key_name == "pre10_avg_price") {
      p_ad_key_value.push_back(ctx_pre10_avg_price_);
    } else if (key_name == "pre20_avg_price") {
      p_ad_key_value.push_back(ctx_pre20_avg_price_);
    } else if (key_name == "pre1_view_coin") {
      p_ad_key_value.push_back(ctx_pre1_award_view_coin_);
    } else if (key_name == "pre20_avg_coin") {
      p_ad_key_value.push_back(ctx_pre20_avg_view_coin_);
    } else if (key_name == "pre1_price") {
      p_ad_key_value.push_back(ctx_pre1_award_price_);
    } else if (key_name == "task_idx") {
      p_ad_key_value.push_back(ctx_day_task_idx_);
    } else if (key_name == "view_amt") {
      p_ad_key_value.push_back(ctx_view_coin_amt_);
    } else if (key_name == "view_cnt") {
      p_ad_key_value.push_back(ctx_view_coin_cnt_);
    } else if (key_name == "ucpm") {
      p_ad_key_value.push_back(
          session_data->get_rank_request()->ad_request().ad_user_info().incntv_ad_cpm_7d());
    } else if (key_name == "u_ltv") {
      p_ad_key_value.push_back(
          session_data->get_rank_request()->ad_request().ad_user_info().incntv_ad_user_ltv());
    } else if (key_name == "cur_hour") {
      p_ad_key_value.push_back(cur_hour_);
    } else {
      return 0.0;  // 存在匹配不上的 key 直接返回 0.0
    }
  }
  for (const auto& one_kv : key_value_list) {
    bool is_hit = true;
    for (int i = 0; i < key_name_list.size(); i++) {
      double lower = one_kv.key_lower_list[i];
      double upper = one_kv.key_upper_list[i];
      if (p_ad_key_value[i] < lower || p_ad_key_value[i] >= upper) {
        is_hit = false;  // 其中某个维度 匹配不上，就 false
        break;
      }
    }
    if (is_hit) {
      return one_kv.key_to_value;
    }
  }

  return 0.0;
}

bool IncentiveCoinJointDecision::IsRun(const ContextData* session_data, const Params* params,
                                          AdRankUnifyScene pos, const AdList* adlist) {
  // 只对激励流量生效
  if (!(session_data->get_is_rewarded() || session_data->get_is_inspire_live_request()) ||
      adlist->Size() <= 0) {
    return false;
  }

  // 深浅联合决策 holdout 实验
  if (SPDM_enable_incentive_coin_joint_decision_holdout(session_data->get_spdm_ctx())) {
    return false;
  }

  if (SPDM_enable_incentive_coin_joint_decision(session_data->get_spdm_ctx())) {
    return true;
  }

  if (SPDM_enable_deep_incentive_rct_migrate(session_data->get_spdm_ctx())) {
    return true;
  }

  return false;
}

StraRetCode IncentiveCoinJointDecision::Process(ContextData* session_data, Params* params,
                                                  AdRankUnifyScene pos, AdList* adlist) {
  if (!Initialize(session_data)) {
    return StraRetCode::SUCC;
  }

  // pv 级判断， RCT 逻辑和 decision 逻辑判断条件不一致
  if (!PvAdmit(session_data)) {
    return StraRetCode::SUCC;
  }

  // ad 级判断，主要为控比逻辑和黑白名单，RCT 和 decision 判断条件一致
  AdAdmit(session_data, adlist);
  // set rct tag
  SetIsRct(session_data, adlist);

  if (enable_incentive_coin_joint_decision_) {
    // 根据计算出预期计费 price
    CalcExpectedPriceFromCpm(session_data, adlist);
    // 填充浅度金币候选 treatment
    GenerateCandidateTreatmentByAd(session_data, adlist);
    // 填充退出率预估值
    FillCandidateTreatmentScore(session_data, adlist);
    FillCandidateDeepTreatmentScore(session_data, adlist);
    GetExpectedValue(session_data, adlist);
  }

  for (auto* p_ad : adlist->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    if (!p_ad->get_incentive_coin_joint_decision_valid()) {
      continue;
    }

    if (decision_valid_ && p_ad->get_incentive_deep_coin_rct_tag() <= 0 &&
        enable_incentive_coin_joint_decision_) {
      SetJointDecisionTag(session_data, p_ad);  // set decision tag
    }

    if (p_ad->get_incentive_deep_coin_rct_tag() > 0) {
      ClearBeforeDecision(session_data, p_ad);
      RandomDeepCoinStrategy(session_data, p_ad);
      FillDeepPredictValue(session_data, p_ad);
    } else if (enable_incentive_coin_joint_decision_ &&
      p_ad->get_incentive_coin_joint_decision_tag() > 0) {
      ClearBeforeDecision(session_data, p_ad);
      // 深度激励预估值填充
      FillDeepPredictValue(session_data, p_ad);
      // 联合决策
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI)) {
        RoasJointDecision(session_data, p_ad);
      } else {
        CommonJointDecision(session_data, p_ad);
        ContinuousDecision(session_data, p_ad);
      }
    }
    ProcessAfterDecision(session_data, p_ad);
    FillStyle(session_data, p_ad);
  }
  return StraRetCode::SUCC;
}

void IncentiveCoinJointDecision::SetIsRct(ContextData* session_data, AdCommon* p_ad) {
  double random_number = ad_base::AdRandom::GetDouble();
  // 短带下单激励需要额外处理
  if (plugin_name_ == "OrderCoinJointDecision" && p_ad->get_product_min_price() < 200) {
    return;
  }
  double rct_ratio_cur = rct_ratio_;
  std::string key = absl::StrCat(plugin_name_, "_", p_ad->get_ocpx_action_type());
  if (rct_ratio_map_.find(key) != rct_ratio_map_.end()) {
    rct_ratio_cur = rct_ratio_map_.find(key)->second;
  }
  if (random_number < rct_ratio_cur) {
    p_ad->set_incentive_deep_coin_rct_tag(2);
  }
}

void IncentiveCoinJointDecision::SetIsRct(ContextData* session_data, AdList* adlist) {
  if (!rct_valid_) {
    return;
  }

  bool enable_joint_decision_use_pv_rct =
    SPDM_enable_joint_decision_use_pv_rct(session_data->get_spdm_ctx());
  bool pv_is_rct = false;
  if (enable_joint_decision_use_pv_rct) {
    double pv_random_number = ad_base::AdRandom::GetDouble();
    if (pv_random_number < rct_ratio_) {
      pv_is_rct = true;
    }
  }

  auto& ads = adlist->Ads();
  for (auto* p_ad : ads) {
    if (p_ad == nullptr) {
      continue;
    }
    if (!p_ad->get_incentive_coin_joint_decision_valid()) {
      continue;
    }
    double random_number = ad_base::AdRandom::GetDouble();
    // 短带下单激励需要额外处理
    if (plugin_name_ == "OrderCoinJointDecision" && p_ad->get_product_min_price() < 200) {
      return;
    }
    double rct_ratio_cur = rct_ratio_;
    std::string key = absl::StrCat(plugin_name_, "_", p_ad->get_ocpx_action_type());
    if (rct_ratio_map_.find(key) != rct_ratio_map_.end()) {
      rct_ratio_cur = rct_ratio_map_.find(key)->second;
    }
    if (!enable_joint_decision_use_pv_rct && random_number < rct_ratio_cur) {
      p_ad->set_incentive_deep_coin_rct_tag(2);
    }
    if (pv_is_rct) {
      p_ad->set_incentive_deep_coin_rct_tag(2);
    }
  }
}

void IncentiveCoinJointDecision::SetJointDecisionTag(ContextData* session_data, AdCommon* p_ad) {
  // 短带下单激励需要额外处理
  if (plugin_name_ == "OrderCoinJointDecision" && p_ad->get_product_min_price() < 1000 &&
      !use_logits_model_) {
    return;
  }
  if (use_logits_model_ && plugin_name_ == "OrderCoinJointDecision" && p_ad->get_product_min_price() < 200) {
    return;
  }
  p_ad->set_incentive_coin_joint_decision_tag(1);
}

void IncentiveCoinJointDecision::RandomDeepCoinStrategy(ContextData* session_data, AdCommon* p_ad) {
  int max_global_treatment_index = rct_treatment_coin_list_.size() - 1;
  // 短带下单激励需要额外处理
  if (plugin_name_ == "OrderCoinJointDecision") {
    double max_treatment_coin = p_ad->get_product_min_price() / 0.5 * 100;
    for (int i = 1 ; i < rct_treatment_coin_list_.size(); i++) {
      if (rct_treatment_coin_list_[i] > max_treatment_coin) {
        max_global_treatment_index = i - 1;
        break;
      }
    }
  }
  int index = ad_base::AdRandom::GetInt(0, max_global_treatment_index);
  if (index >= 0 && index < rct_treatment_coin_list_.size()) {
    p_ad->set_deep_rewarded_coin(rct_treatment_coin_list_[index]);
    if (plugin_name_ == "LiveOrderCoinJointDecision" &&
      rct_treatment_coin_list_.size() == rct_treatment_discount_list_.size()) {
      p_ad->set_coin_discount_ratio(rct_treatment_discount_list_[index]);
    }
  }
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(plugin_name_, "_rct"), absl::StrCat("rct_treatment_", index),
    absl::StrCat(p_ad->get_ocpx_action_type()), exp_tag_);
}

void IncentiveCoinJointDecision::FillDeepPredictValue(ContextData* session_data, AdCommon* p_ad) {
  uplift_cvr_list_.clear();
  rewarded_ratio_list_.clear();
  deep_quit_rate_list_.clear();
  bool is_live_order = (plugin_name_ == "LiveOrderCoinJointDecision") &&
    enable_live_order_use_logits_model_;
  double base_cvr = 0;
  double uplift_cvr = 0;
  double predict_cvr = 0;
  double rank_cvr = std::clamp(p_ad->get_unify_cvr_info().value, 1e-18, 0.9999996941);
  double rank_logits = std::log(rank_cvr / (1 - rank_cvr + 1e-18));
  double total_logits = rank_logits;
  if (is_live_order) {
    double rank_pay_cnt = p_ad->get_inner_live_atom_paycnt_front() + p_ad->get_inner_live_atom_paycnt_end();
    if (rank_pay_cnt <= 0) {
      RANK_DOT_COUNT(session_data, 1, "deep_incentive_error", absl::StrCat(plugin_name_, "_paycnt_is_zero"));
      rank_pay_cnt = 1e-6;
    }
    rank_cvr = rank_pay_cnt;
    total_logits = std::log(rank_cvr);
  }
  // uplift cvr
  for (int i = 0; i < uplift_cvr_pts_.size(); i++) {
    if (i == 0) {
      base_cvr = p_ad->get_predict_score(uplift_cvr_pts_[i]);
      if (use_logits_model_) {
        base_cvr = rank_cvr;
      }
    }
    predict_cvr = p_ad->get_predict_score(uplift_cvr_pts_[i]);
    if (use_logits_model_) {
      total_logits += predict_cvr;
      total_logits = std::min(std::max(total_logits, -15.0), 15.0);
      predict_cvr = std::exp(total_logits) / (1 + std::exp(total_logits));
      if (is_live_order && predict_cvr != 1) {
        predict_cvr = predict_cvr / (1 - predict_cvr);
      }
    }
    if (i >= 0 && i < rct_treatment_coin_list_.size()) {
      p_ad->mutable_incentive_deep_uplift_cvr()->insert({rct_treatment_coin_list_[i], predict_cvr});
    }
    uplift_cvr = predict_cvr - base_cvr;
    uplift_cvr_list_.push_back(uplift_cvr);
    RANK_DOT_STATS(session_data, predict_cvr * 1e6, absl::StrCat(plugin_name_ + "_pcvr"), absl::StrCat(i),
      absl::StrCat(p_ad->get_ocpx_action_type()), exp_tag_);
  }
  // 领奖率
  if (plugin_name_ == "InvokedCoinJointDecision" || plugin_name_ == "ConvCoinJointDecision" &&
      rewarded_ratio_pts_.size() > 0) {
    rewarded_ratio_list_.push_back(0);  // treatment 0 领奖率为 0
    for (int i = 0; i < rewarded_ratio_pts_.size(); i++) {
      double reward_ratio = p_ad->get_predict_score(rewarded_ratio_pts_[i]);
      rewarded_ratio_list_.push_back(reward_ratio);
      RANK_DOT_STATS(session_data, reward_ratio * 1e6, absl::StrCat(plugin_name_ + "_reward_ratio"),
        absl::StrCat(i), absl::StrCat(p_ad->get_ocpx_action_type()), exp_tag_);
    }
  } else {
    rewarded_ratio_list_.assign(uplift_cvr_pts_.size(), 1);
  }
  // for 打点
  if (rewarded_ratio_list_.size() == treatment_coin_list_.size()) {
    for (int i = 0; i < rewarded_ratio_list_.size(); i++) {
      p_ad->mutable_incentive_deep_reward_ratio()->
          insert({treatment_coin_list_[i], rewarded_ratio_list_[i]});
    }
  }
  // 退出率
  for (int i = 0; i < treatment_coin_list_.size(); i++) {
    if (p_ad->get_deep_coin_quit_rate_map().find(treatment_coin_list_[i]) !=
        p_ad->get_deep_coin_quit_rate_map().end()) {
      double quit_rate = p_ad->get_deep_coin_quit_rate_map().find(treatment_coin_list_[i])->second;
      deep_quit_rate_list_.push_back(quit_rate);
    }
  }
  if (deep_quit_rate_list_.size() != treatment_coin_list_.size()) {
    deep_quit_rate_list_.assign(treatment_coin_list_.size(), 0);
  }
  // ctr
  if (is_live_order && uplift_cvr_list_.size() == uplift_ctr_pts_.size() &&
      p_ad->get_item_type() != kuaishou::ad::AdEnum::ITEM_PHOTO_TO_LIVE) {
    for (int i = 0; i < uplift_ctr_pts_.size(); i++) {
      double uplift_ctr = p_ad->get_predict_score(uplift_ctr_pts_[i]);
      if (!enable_live_order_use_ctr_uplift_model_ || uplift_ctr <= 0) {
        uplift_ctr = p_ad->get_unify_ctr_info().value;
      }
      uplift_cvr_list_[i] *= uplift_ctr;
      if (i >= 0 && i < treatment_coin_list_.size()) {
        p_ad->mutable_incentive_deep_uplift_ctr()->
          insert({treatment_coin_list_[i], uplift_ctr});
      }
      RANK_DOT_STATS(session_data, uplift_ctr * 1e6, absl::StrCat(plugin_name_ + "_pctr"),
        absl::StrCat(i), absl::StrCat(p_ad->get_ocpx_action_type()), exp_tag_);
    }
  }
}

void IncentiveCoinJointDecision::FillCandidateTreatmentScore(ContextData* session_data, AdList* adlist) {
  auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
  auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
  if (!(admit_page_ids && admit_page_ids->count(session_data->get_page_id()) ||
        admit_sub_page_ids && admit_sub_page_ids->count(session_data->get_sub_page_id()))) {
    return;
  }
  auto& coin_treatment_list = *(RankKconfUtil::adShallowIncentiveCoinTreatmentList());
  std::sort(coin_treatment_list.begin(), coin_treatment_list.end());
  static const std::vector<engine_base::PredictType> quit_rate_pts = {
      PredictType::PredictType_incentive_quit_rate_0,
      PredictType::PredictType_incentive_quit_rate_1,
      PredictType::PredictType_incentive_quit_rate_2,
      PredictType::PredictType_incentive_quit_rate_3,
      PredictType::PredictType_incentive_quit_rate_4,
      PredictType::PredictType_incentive_quit_rate_5,
      PredictType::PredictType_incentive_quit_rate_6,
      PredictType::PredictType_incentive_quit_rate_7,
      PredictType::PredictType_incentive_quit_rate_8,
      PredictType::PredictType_incentive_quit_rate_9,
      PredictType::PredictType_incentive_quit_rate_10,
      PredictType::PredictType_incentive_quit_rate_11,
      PredictType::PredictType_incentive_quit_rate_12,
      PredictType::PredictType_incentive_quit_rate_13,
      PredictType::PredictType_incentive_quit_rate_14};
  // todo, 暂时先不支持 percent trt 模型
  double quit_rate = 0.0;
  bool is_treat_list_valid = coin_treatment_list.size() >= quit_rate_pts.size();
  auto& ads = adlist->Ads();
  for (auto* p_ad : ads) {
    if (!p_ad->get_incentive_coin_joint_decision_valid()) {
      continue;
    }
    if (p_ad && p_ad->get_predict_score(quit_rate_pts[0]) > 1e-6) {
      for (int i = 0; i < quit_rate_pts.size(); i++) {
        quit_rate = p_ad->get_predict_score(quit_rate_pts[i]);
        if (is_treat_list_valid) {
          p_ad->mutable_coin_quit_rate_map()->insert({coin_treatment_list[i], quit_rate});
        }
      }
    }
  }
}

void IncentiveCoinJointDecision::FillCandidateDeepTreatmentScore(ContextData* session_data, AdList* adlist) {
  if (!SPDM_enable_incentive_deep_quit_rate_pred(session_data->get_spdm_ctx())) {
    return;
  }
  auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
  auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
  if (!(admit_page_ids && admit_page_ids->count(session_data->get_page_id()) ||
        admit_sub_page_ids && admit_sub_page_ids->count(session_data->get_sub_page_id()))) {
    return;
  }
  auto& coin_treatment_list = *(RankKconfUtil::adDeepIncentiveCoinTreatmentList());
  std::sort(coin_treatment_list.begin(), coin_treatment_list.end());
  static const std::vector<engine_base::PredictType> quit_rate_pts = {
      PredictType::PredictType_incentive_deep_quit_rate_0,
      PredictType::PredictType_incentive_deep_quit_rate_1,
      PredictType::PredictType_incentive_deep_quit_rate_2,
      PredictType::PredictType_incentive_deep_quit_rate_3,
      PredictType::PredictType_incentive_deep_quit_rate_4,
      PredictType::PredictType_incentive_deep_quit_rate_5};
  // todo, 暂时先不支持 percent trt 模型
  double quit_rate = 0.0;
  bool is_treat_list_valid = coin_treatment_list.size() >= quit_rate_pts.size();
  auto& ads = adlist->Ads();
  for (auto* p_ad : ads) {
    if (!p_ad) {
      continue;
    }
    if (!p_ad->get_incentive_coin_joint_decision_valid()) {
      continue;
    }
    for (int i = 0; i < quit_rate_pts.size(); i++) {
      quit_rate = p_ad->get_predict_score(quit_rate_pts[i]);
      if (is_treat_list_valid) {
        p_ad->mutable_deep_coin_quit_rate_map()->insert({coin_treatment_list[i], quit_rate});
        RANK_DOT_STATS(session_data, quit_rate * 1e6, absl::StrCat(plugin_name_ + "_quit_rate"),
          absl::StrCat(i), absl::StrCat(p_ad->get_ocpx_action_type()), exp_tag_);
      }
    }
    p_ad->mutable_deep_coin_quit_rate_map()->insert({1000, 0});  // 兼容短带下单多出来的一个 tower
  }
}

void IncentiveCoinJointDecision::FillIncntvLTVScore(ContextData* session_data, AdList* adlist) {
  if (!SPDM_enable_incntv_ltv_cmd(session_data->get_spdm_ctx())) {
    return;
  }
  if (SPDM_enable_ucc_ltv_model_subpage_admit(session_data->get_spdm_ctx())) {
    // ltv 单独的 subpage 准入
    auto admit_sub_page_ids_ltv = RankKconfUtil::incentiveLtvAdmitSubPageIds();
    if (!admit_sub_page_ids_ltv || admit_sub_page_ids_ltv->count(session_data->get_page_id()) == 0) {
      return;
    }
  } else {
    auto admit_page_ids = RankKconfUtil::incentiveQuitRateAdmitPageIds();
    auto admit_sub_page_ids = RankKconfUtil::incentiveQuitRateAdmitSubPageIds();
    if (!(admit_page_ids && admit_page_ids->count(session_data->get_page_id()) ||
          admit_sub_page_ids && admit_sub_page_ids->count(session_data->get_sub_page_id()))) {
      return;
    }
  }
  for (auto* p_ad : adlist->Ads()) {
    if (!p_ad) {
      continue;
    }
    double ltv_value = p_ad->get_predict_score(PredictType::PredictType_incntv_ltv);
    if (ltv_value > 0) {
      p_ad->set_incntv_ltv(ltv_value);
    }
  }
}

void IncentiveCoinJointDecision::RoasJointDecision(ContextData* session_data, AdCommon* p_ad) {
  if (uplift_cvr_pts_.size() == 0 || uplift_cvr_pts_.size() != uplift_cvr_list_.size() ||
      uplift_cvr_list_.size() != rewarded_ratio_list_.size() ||
      rewarded_ratio_list_.size() != treatment_coin_list_.size() ||
      treatment_coin_list_.size() != treatment_discount_list_.size() ||
      treatment_discount_list_.size() != deep_quit_rate_list_.size()) {
    RANK_DOT_COUNT(session_data, 1, "deep_incentive_error",
      absl::StrCat(plugin_name_, "_roas_vector_size_unequal"));
    return;
  }

  int64_t auto_cpa_bid = p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : p_ad->get_cpa_bid();
  double auto_roas =  p_ad->get_auto_roas() > 0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
  double rank_ctr = p_ad->get_unify_ctr_info().value;
  double rank_cvr = p_ad->get_unify_cvr_info().value;
  double ecpc_ratio =
    p_ad->Attr(ItemIdx::ecpc_max_min_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
  if (disable_joint_decision_use_ecpc_ratio_) {
    ecpc_ratio = 1;
  }
  double ecpm_origin = auto_cpa_bid * rank_ctr * rank_cvr * ecpc_ratio;
  double base_cvr = p_ad->get_predict_score(uplift_cvr_pts_[0]);
  if (enable_live_order_use_logits_model_ && plugin_name_ == "LiveOrderCoinJointDecision") {
    base_cvr = p_ad->get_inner_live_atom_paycnt_front() + p_ad->get_inner_live_atom_paycnt_end();
  }
  if (disable_live_order_joint_decision_rank_ctr_ &&
      plugin_name_ == "LiveOrderCoinJointDecision") {
    ecpm_origin = auto_cpa_bid * (base_cvr + uplift_cvr_list_[0]) * ecpc_ratio;
  }
  double profit = 0;
  double optimal_profit = 0;
  double optimal_ecpm_ratio = 1.0;
  // 浅度金币相关
  int index_view = 0;
  double expected_value = p_ad->get_incentive_expected_value();
  const std::vector<int64_t>& ad_trt = p_ad->get_reward_coin_treatment_list();
  double opt_quit_rate = 0;
  double next_value = expected_value + task_cnt_control_coef_ * 1.0;
  int64_t optimal_view_coin = 10;
  // 深度金币相关
  int index_deep = 0;
  double opt_deep_quit_rate = 0;
  std::string key = absl::StrCat(plugin_name_, "_", p_ad->get_ocpx_action_type());
  double coin_ratio_cur = coin_ratio_;
  if (coin_ratio_map_.find(key) != coin_ratio_map_.end()) {
    coin_ratio_cur = coin_ratio_map_.find(key)->second;
  }
  double view_coin_ratio_cur = view_coin_ratio_;
  if (view_coin_ratio_map_.find(key) != view_coin_ratio_map_.end()) {
    view_coin_ratio_cur = view_coin_ratio_map_.find(key)->second;
  }
  double deep_coin_coef_cur = deep_coin_coef_;
  if (deep_coin_coef_map_.find(key) != deep_coin_coef_map_.end()) {
    deep_coin_coef_cur = deep_coin_coef_map_.find(key)->second;
  }
  double discount_coef_cur = discount_coef_;
  if (discount_coef_map_.find(key) != discount_coef_map_.end()) {
    discount_coef_cur = discount_coef_map_.find(key)->second;
  }
  double alpha_cur = alpha_;
  if (alpha_map_.find(key) != alpha_map_.end()) {
    alpha_cur = alpha_map_.find(key)->second;
  }
  double clip_ratio_cur = clip_ratio_;
  if (clip_ratio_map_.find(key) != clip_ratio_map_.end()) {
    clip_ratio_cur = clip_ratio_map_.find(key)->second;
  }
  // 决策前判断
  if (base_cvr <= 0 || auto_roas <= 0 || ecpm_origin <= 0 || ad_trt.size() == 0) {
    return;
  }
  // 金币决策
  for (int i = 0; i < uplift_cvr_list_.size(); i++) {
    for (int j = 0; j < ad_trt.size(); j++) {
      double ecpm_ratio = (uplift_cvr_list_[i] + base_cvr) / base_cvr;
      int64_t percent_coin =
        static_cast<int64_t>(std::max(std::min(1.0 * ad_trt[j], spdm_coin_upper_), spdm_coin_lower_));
      // 当前广告收益
      double ecpm_cur = uplift_cvr_list_[0] * ecpm_ratio / auto_roas;
      // 后续广告价值
      double next_value_cur = uplift_cvr_list_[0] * next_value / auto_roas / ecpm_origin;
      // 退出率
      double view_coin_click_rate = 1 - GetQuitRate(p_ad, percent_coin);
      double deep_coin_click_rate = 1 - deep_quit_rate_list_[i];
      double click_next_rate = std::min(1.0,
        (1 - alpha_cur) * view_coin_click_rate + alpha_cur * deep_coin_click_rate);
      if (deep_coin_click_rate >= 1) {
        click_next_rate = std::min(1.0, view_coin_click_rate);
      }
      // 金币成本，单位厘
      if (rewarded_ratio_list_[i] == 1) {
        rewarded_ratio_list_[i] = uplift_cvr_list_[i] + base_cvr;
      }
      double deep_coin = treatment_discount_list_[i] * rewarded_ratio_list_[i];
      double view_coin = percent_coin * uplift_cvr_list_[0] / auto_roas / ecpm_origin / 10;
      double unify_coin = deep_coin + view_coin;
      if (enable_joint_decision_use_uplift_model_output_) {
        ecpm_cur = base_cvr * ecpm_ratio / auto_roas;
        next_value_cur = base_cvr * next_value / auto_roas / ecpm_origin;
        view_coin = percent_coin * base_cvr / auto_roas / ecpm_origin / 10;
      }
      // 决策
      profit = ecpm_cur + balance_ratio_ * click_next_rate * next_value_cur -
        (1 + coin_ratio_cur) * deep_coin - (1 + view_coin_ratio_cur) * view_coin;
      if (i == 0 && j == 0) {
        optimal_profit = profit - 1;  // 确保 i == 0 且 j == 0 时 optimal_profit 一定能 update 一次
      }
      if (profit > optimal_profit) {
        optimal_view_coin = percent_coin;
        optimal_profit = profit;
        index_deep = i;
        index_view = j;
        optimal_ecpm_ratio = ecpm_ratio;
        opt_quit_rate = 1 - view_coin_click_rate;
        opt_deep_quit_rate = 1 - deep_coin_click_rate;
      }
    }
  }
  // 深度字段填充
  p_ad->set_incentive_deep_quit_rate(opt_deep_quit_rate);
  p_ad->set_coin_discount_ratio(treatment_discount_list_[index_deep] * discount_coef_cur);
  p_ad->set_deep_rewarded_coin(floor(treatment_coin_list_[index_deep] * deep_coin_coef_cur));
  optimal_ecpm_ratio = std::min(optimal_ecpm_ratio, clip_ratio_cur + 1);
  std::vector<double> cvr_adjust_list_cur = cvr_adjust_list_;
  if (cvr_adjust_map_.find(key) != cvr_adjust_map_.end()) {
    cvr_adjust_list_cur = cvr_adjust_map_.find(key)->second;
  }
  optimal_ecpm_ratio *= SafeGetDoubleVectorValue(cvr_adjust_list_cur, index_deep,
    1.0, session_data, absl::StrCat(plugin_name_ + "_cvr_adjust_list_"));
  optimal_ecpm_ratio = std::max(std::min(optimal_ecpm_ratio, 2.0), 0.2);
  p_ad->set_incentive_uplift_ecpm_ratio(optimal_ecpm_ratio);
  if (plugin_name_ != "LiveOrderCoinJointDecision") {
    // 直播下单激励先不 set
    p_ad->set_deep_rewarded_rate(rewarded_ratio_list_[index_deep]);
  }
  // 浅度字段填充
  double opt_next_value = fix_ucc_next_value_log_ ? (1 - opt_quit_rate) * expected_value : next_value;
  p_ad->set_incentive_quit_rate(opt_quit_rate);
  p_ad->set_incentive_next_value(opt_next_value);
  p_ad->set_reward_rate(spdm_reward_rate_);
  p_ad->set_reward_coin(optimal_view_coin);
  double opt_has_more_score = opt_next_value * 10.0 - roi_control_coef_ * optimal_view_coin;
  p_ad->set_rank_has_more(opt_has_more_score >= spdm_rank_has_more_thres_
                            ? kuaishou::ad::AdEnum_IncentiveAdHasMoreStatus_HAS_MORE
                            : kuaishou::ad::AdEnum_IncentiveAdHasMoreStatus_NO_MORE);
  p_ad->set_incntv_ad_view_coin_coef(roi_control_coef_);
  p_ad->set_incentive_coin_upper(spdm_coin_upper_);
  // 打点监控
  std::string view_coin_index = absl::StrCat(std::floor(p_ad->get_reward_coin() / 100));
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(plugin_name_, "_roas_deep_coin_index"),
    absl::StrCat(index_deep), exp_tag_, absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(plugin_name_, "_roas_view_coin_index"),
    view_coin_index, exp_tag_, absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_STATS(session_data, p_ad->get_deep_rewarded_coin(), absl::StrCat(plugin_name_, "_roas_deep_coin"),
    exp_tag_,  absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_STATS(session_data, optimal_ecpm_ratio * 100,
    absl::StrCat(plugin_name_, "_roas_optimal_ecpm_ratio"), exp_tag_,
    absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_STATS(session_data, rank_cvr * 1e6, absl::StrCat(plugin_name_, "_roas_rank_cvr"),
    exp_tag_, absl::StrCat(p_ad->get_ocpx_action_type()));
}

void IncentiveCoinJointDecision::CommonJointDecision(ContextData* session_data, AdCommon* p_ad) {
  if (uplift_cvr_pts_.size() == 0 || uplift_cvr_pts_.size() != uplift_cvr_list_.size() ||
      uplift_cvr_list_.size() != rewarded_ratio_list_.size() ||
      rewarded_ratio_list_.size() != treatment_coin_list_.size() ||
      treatment_coin_list_.size() != deep_quit_rate_list_.size()) {
    RANK_DOT_COUNT(session_data, 1, "deep_incentive_error",
      absl::StrCat(plugin_name_, "_common_vector_size_unequal"));
    return;
  }

  int64_t auto_cpa_bid = p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : p_ad->get_cpa_bid();
  double rank_ctr = p_ad->get_unify_ctr_info().value;
  double rank_cvr = p_ad->get_unify_cvr_info().value;
  double ecpc_ratio =
    p_ad->Attr(ItemIdx::ecpc_max_min_ratio).GetDoubleValue(p_ad->AttrIndex()).value_or(1.0);
  if (disable_joint_decision_use_ecpc_ratio_) {
    ecpc_ratio = 1;
  }
  double ecpm_origin = auto_cpa_bid * rank_ctr * rank_cvr * ecpc_ratio;
  double base_cvr = p_ad->get_predict_score(uplift_cvr_pts_[0]);
  if (use_logits_model_) {
    base_cvr = p_ad->get_unify_cvr_info().value;
    if (enable_live_order_use_logits_model_ && plugin_name_ == "LiveOrderCoinJointDecision") {
      base_cvr = p_ad->get_inner_live_atom_paycnt_front() + p_ad->get_inner_live_atom_paycnt_end();
    }
  }
  if (enable_joint_decision_use_uplift_model_output_ &&
      !enable_joint_decision_ecpm_origin_use_rank_cvr_) {
    ecpm_origin = auto_cpa_bid * rank_ctr * (base_cvr + uplift_cvr_list_[0]) * ecpc_ratio;
  }
  if (disable_live_order_joint_decision_rank_ctr_ &&
      plugin_name_ == "LiveOrderCoinJointDecision") {
    ecpm_origin = auto_cpa_bid * (base_cvr + uplift_cvr_list_[0]) * ecpc_ratio;
  }
  double profit = 0;
  double optimal_profit = 0;
  double optimal_ecpm_ratio = 1.0;
  // 浅度金币相关
  int index_view = 0;
  double expected_value = p_ad->get_incentive_expected_value();
  double next_value = expected_value + task_cnt_control_coef_ * 1.0;
  std::vector<int64_t> ad_trt = p_ad->get_reward_coin_treatment_list();
  int64_t optimal_view_coin = 10;
  double opt_quit_rate = 0;
  // 深度金币相关
  int index_deep = 0;
  double opt_deep_quit_rate = 0;
  std::string key = absl::StrCat(plugin_name_, "_", p_ad->get_ocpx_action_type());
  double coin_ratio_cur = coin_ratio_;
  if (coin_ratio_map_.find(key) != coin_ratio_map_.end()) {
    coin_ratio_cur = coin_ratio_map_.find(key)->second;
  }
  double view_coin_ratio_cur = view_coin_ratio_;
  if (view_coin_ratio_map_.find(key) != view_coin_ratio_map_.end()) {
    view_coin_ratio_cur = view_coin_ratio_map_.find(key)->second;
  }
  double deep_coin_coef_cur = deep_coin_coef_;
  if (deep_coin_coef_map_.find(key) != deep_coin_coef_map_.end()) {
    deep_coin_coef_cur = deep_coin_coef_map_.find(key)->second;
  }
  double discount_coef_cur = discount_coef_;
  if (discount_coef_map_.find(key) != discount_coef_map_.end()) {
    discount_coef_cur = discount_coef_map_.find(key)->second;
  }
  double alpha_cur = alpha_;
  if (alpha_map_.find(key) != alpha_map_.end()) {
    alpha_cur = alpha_map_.find(key)->second;
  }
  double clip_ratio_cur = clip_ratio_;
  if (clip_ratio_map_.find(key) != clip_ratio_map_.end()) {
    clip_ratio_cur = clip_ratio_map_.find(key)->second;
  }
  // 决策前判断
  if (base_cvr <= 0 || ecpm_origin <= 0 || ad_trt.size() == 0) {
    return;
  }
  // 金币决策
  for (int i = 0; i < uplift_cvr_list_.size(); i++) {
    for (int j = 0; j < ad_trt.size(); j++) {
      double ecpm_ratio = (uplift_cvr_list_[i] + base_cvr) / base_cvr;
      int64_t percent_coin =
        static_cast<int64_t>(std::max(std::min(1.0 * ad_trt[j], spdm_coin_upper_), spdm_coin_lower_));
      // 当前广告收益
      double ecpm_cur = ecpm_origin * ecpm_ratio;
      // 退出率
      double view_coin_click_rate = 1 - GetQuitRate(p_ad, percent_coin);
      double deep_coin_click_rate = 1 - deep_quit_rate_list_[i];
      double click_next_rate = std::min(1.0,
        (1 - alpha_cur) * view_coin_click_rate + alpha_cur * deep_coin_click_rate);
      if (deep_coin_click_rate >= 1) {
        click_next_rate = std::min(1.0, view_coin_click_rate);
      }
      // 金币成本，单位厘
      if (rewarded_ratio_list_[i] == 1) {
        rewarded_ratio_list_[i] = uplift_cvr_list_[i] + base_cvr;
      }
      double unify_coin = (treatment_coin_list_[i] * rewarded_ratio_list_[i] + percent_coin) / 10;
      // 决策
      double deep_coin = treatment_coin_list_[i] * rewarded_ratio_list_[i] / 10;
      double view_coin = percent_coin / 10;
      profit = ecpm_cur + balance_ratio_ * click_next_rate * next_value -
        (1 + coin_ratio_cur) * deep_coin - (1 + view_coin_ratio_cur) * view_coin;
      if (i == 0 && j == 0) {
        optimal_profit = profit - 1;  // 确保 i == 0 且 j == 0 时 optimal_profit 一定能 update 一次
      }
      if (profit > optimal_profit) {
        optimal_view_coin = percent_coin;
        optimal_profit = profit;
        index_deep = i;
        index_view = j;
        optimal_ecpm_ratio = ecpm_ratio;
        opt_quit_rate = 1 - view_coin_click_rate;
        opt_deep_quit_rate = 1 - deep_coin_click_rate;
      }
    }
  }
  // 深度字段填充
  p_ad->set_incentive_deep_quit_rate(opt_deep_quit_rate);
  p_ad->set_deep_rewarded_coin(floor(treatment_coin_list_[index_deep] * deep_coin_coef_cur));
  if (plugin_name_ == "LiveOrderCoinJointDecision") {
    p_ad->set_coin_discount_ratio(treatment_discount_list_[index_deep] * discount_coef_cur);
  }
  optimal_ecpm_ratio = std::min(optimal_ecpm_ratio, clip_ratio_cur + 1);
  std::vector<double> cvr_adjust_list_cur = cvr_adjust_list_;
  if (cvr_adjust_map_.find(key) != cvr_adjust_map_.end()) {
    cvr_adjust_list_cur = cvr_adjust_map_.find(key)->second;
  }
  optimal_ecpm_ratio *= SafeGetDoubleVectorValue(cvr_adjust_list_cur, index_deep,
    1.0, session_data, absl::StrCat(plugin_name_ + "_cvr_adjust_list_"));
  optimal_ecpm_ratio = std::max(std::min(optimal_ecpm_ratio, 2.0), 0.2);
  p_ad->set_incentive_uplift_ecpm_ratio(optimal_ecpm_ratio);
  if (plugin_name_ != "LiveOrderCoinJointDecision" && plugin_name_ != "OrderCoinJointDecision") {
    // 直播下单激励先不 set
    p_ad->set_deep_rewarded_rate(rewarded_ratio_list_[index_deep]);
  }
  // 浅度字段填充
  double opt_next_value = fix_ucc_next_value_log_ ? (1 - opt_quit_rate) * expected_value : next_value;
  p_ad->set_incentive_quit_rate(opt_quit_rate);
  p_ad->set_incentive_next_value(opt_next_value);
  p_ad->set_reward_rate(spdm_reward_rate_);
  p_ad->set_reward_coin(optimal_view_coin);
  double opt_has_more_score = opt_next_value * 10.0 - roi_control_coef_ * optimal_view_coin;
  p_ad->set_rank_has_more(opt_has_more_score >= spdm_rank_has_more_thres_
                            ? kuaishou::ad::AdEnum_IncentiveAdHasMoreStatus_HAS_MORE
                            : kuaishou::ad::AdEnum_IncentiveAdHasMoreStatus_NO_MORE);
  p_ad->set_incntv_ad_view_coin_coef(roi_control_coef_);
  p_ad->set_incentive_coin_upper(spdm_coin_upper_);

  // 打点监控
  std::string view_coin_index = absl::StrCat(std::floor(p_ad->get_reward_coin() / 100));
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(plugin_name_, "_deep_coin_index"),
    absl::StrCat(index_deep), exp_tag_, absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(plugin_name_, "_view_coin_index"),
    view_coin_index, exp_tag_, absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_STATS(session_data, p_ad->get_deep_rewarded_coin(), absl::StrCat(plugin_name_, "_deep_coin"),
    exp_tag_,  absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_STATS(session_data, optimal_ecpm_ratio * 100,
    absl::StrCat(plugin_name_, "_optimal_ecpm_ratio"), exp_tag_,
    absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_STATS(session_data, rank_cvr * 1e6, absl::StrCat(plugin_name_, "_rank_cvr"),
    exp_tag_, absl::StrCat(p_ad->get_ocpx_action_type()));
}

void IncentiveCoinJointDecision::ContinuousDecision(ContextData* session_data, AdCommon* p_ad) {
  if (!enable_deep_incentive_continuous_decision_) return;
  if (plugin_name_ == "LiveOrderCoinJointDecision") return;
  if (uplift_cvr_pts_.size() < 1 || uplift_cvr_pts_.size() != uplift_cvr_list_.size() ||
      uplift_cvr_list_.size() != rewarded_ratio_list_.size() ||
      rewarded_ratio_list_.size() != treatment_coin_list_.size() ||
      treatment_coin_list_.size() != deep_quit_rate_list_.size()) {
    RANK_DOT_COUNT(session_data, 1, "deep_incentive_error",
      absl::StrCat(plugin_name_, "_continuous_vector_size_unequal"));
    return;
  }

  p_ad->set_deep_rewarded_coin(0);
  int64_t bid = p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : p_ad->get_cpa_bid();
  double base_cvr = p_ad->get_unify_cvr_info().value;
  double b_0 = base_cvr + uplift_cvr_list_[0];
  double deep_rewarded_rate = 0;
  double optimal_profit = bid * b_0;
  double optimal_ecpm_ratio = 1.0;
  int optimal_deep_coin = 0;
  std::string key = absl::StrCat(plugin_name_, "_", p_ad->get_ocpx_action_type());
  // 金币下界
  double deep_coin_lower_cur = deep_coin_lower_;
  const auto& deep_coin_lower_iter = deep_coin_lower_map_.find(key);
  if (deep_coin_lower_iter != deep_coin_lower_map_.end()) {
    deep_coin_lower_cur = deep_coin_lower_iter->second;
  }
  // 金币上界
  double deep_coin_upper_cur = deep_coin_upper_;
  const auto& deep_coin_upper_iter = deep_coin_upper_map_.find(key);
  if (deep_coin_upper_iter != deep_coin_upper_map_.end()) {
    deep_coin_upper_cur = deep_coin_upper_iter->second;
  }
  // 边际 roi 约束
  double deep_roi_bound_cur = deep_roi_bound_;
  const auto& deep_roi_bound_iter = deep_roi_bound_map_.find(key);
  if (deep_roi_bound_iter != deep_roi_bound_map_.end()) {
    deep_roi_bound_cur = deep_roi_bound_iter->second;
  }
  // clip ratio
  double clip_ratio_cur = clip_ratio_;
  const auto& clip_ratio_iter = clip_ratio_map_.find(key);
  if (clip_ratio_iter != clip_ratio_map_.end()) {
    clip_ratio_cur = clip_ratio_iter->second;
  }
  if (plugin_name_ != "InvokedCoinJointDecision" && plugin_name_ != "ConvCoinJointDecision") {
    rewarded_ratio_list_.assign(uplift_cvr_list_.begin(), uplift_cvr_list_.end());
  }

  // 决策前判断
  if (base_cvr <= 0 || deep_coin_lower_cur >= deep_coin_upper_cur || deep_roi_bound_cur <= 0) {
    return;
  }
  // 金币决策
  for (int i = 0; i < uplift_cvr_list_.size() - 1; i++) {
    // 计算 k_i, b_i, r_k_i, r_b_i
    double left_coin = treatment_coin_list_[i] / 10.0, right_coin = treatment_coin_list_[i+1] / 10.0;
    double left_cvr = base_cvr + uplift_cvr_list_[i], right_cvr = base_cvr + uplift_cvr_list_[i+1];
    double left_reward_ratio = rewarded_ratio_list_[i], right_reward_ratio = rewarded_ratio_list_[i+1];
    if (left_coin >= right_coin || left_cvr >= right_cvr || right_reward_ratio == left_reward_ratio) {
      continue;
    }
    double k_i = (right_cvr - left_cvr) / (right_coin - left_coin);
    double b_i = left_cvr - k_i * left_coin;
    double r_k_i = (right_reward_ratio - left_reward_ratio) / (right_coin - left_coin);
    double r_b_i = left_reward_ratio - r_k_i * left_coin;
    double peak_coin = (bid * k_i - r_b_i) / 2.0 / r_k_i;
    // 根据边际 roi 约束和金币上下界，计算左右金币边界
    // 边际 roi 约束
    double sqrt_result = std::pow(deep_roi_bound_cur * r_b_i - bid * k_i, 2) -
      4 * deep_roi_bound_cur * r_k_i * (bid * b_0 - bid * b_i);
    if (sqrt_result < 0) continue;
    double x1 = (bid * k_i - deep_roi_bound_cur * r_b_i -
      std::sqrt(sqrt_result)) / 2 / deep_roi_bound_cur / r_k_i;
    double x2 = (bid * k_i - deep_roi_bound_cur * r_b_i +
      std::sqrt(sqrt_result)) / 2 / deep_roi_bound_cur / r_k_i;
    left_coin = std::max(left_coin, x1);
    right_coin = std::min(right_coin, x2);
    // 金币上下界
    left_coin = std::max(left_coin, deep_coin_lower_cur / 10.0);
    right_coin = std::min(right_coin, deep_coin_upper_cur / 10.0);
    if (left_coin > right_coin) continue;
    // 计算 profit
    double left_profit =
      (bid * k_i - r_b_i) * left_coin + bid * b_i - r_k_i * std::pow(left_coin, 2);
    double right_profit =
      (bid * k_i - r_b_i) * right_coin + bid * b_i - r_k_i * std::pow(right_coin, 2);
    double peak_profit =
      (bid * k_i - r_b_i) * peak_coin + bid * b_i - r_k_i * std::pow(peak_coin, 2);
    // 区间内最优金币只会在极值点或者左右边界产出
    if (peak_coin < left_coin || peak_coin > right_coin) {
      if (left_profit >= right_profit) {
        peak_coin = left_coin, peak_profit = left_profit;
      } else {
        peak_coin = right_coin, peak_profit = right_profit;
      }
    }
    if (peak_profit > optimal_profit) {
      optimal_deep_coin = peak_coin;
      optimal_profit = peak_profit;
      optimal_ecpm_ratio = (k_i * peak_coin + b_i) / base_cvr;
      deep_rewarded_rate = r_k_i * peak_coin + r_b_i;
    }
  }

  // 深度字段填充
  p_ad->set_deep_rewarded_coin(floor(optimal_deep_coin * 10));
  optimal_ecpm_ratio = std::min(optimal_ecpm_ratio, clip_ratio_cur + 1);
  optimal_ecpm_ratio = std::max(std::min(optimal_ecpm_ratio, 2.0), 0.2);
  p_ad->set_incentive_uplift_ecpm_ratio(optimal_ecpm_ratio);
  if (plugin_name_ != "LiveOrderCoinJointDecision" && plugin_name_ != "OrderCoinJointDecision") {
    p_ad->set_deep_rewarded_rate(deep_rewarded_rate);
  }
  // 打点监控
  RANK_DOT_STATS(session_data, p_ad->get_deep_rewarded_coin(), absl::StrCat(plugin_name_, "_deep_coin"),
    exp_tag_,  absl::StrCat(p_ad->get_ocpx_action_type()));
  RANK_DOT_STATS(session_data, optimal_ecpm_ratio * 100,
    absl::StrCat(plugin_name_, "_optimal_ecpm_ratio"), exp_tag_,
    absl::StrCat(p_ad->get_ocpx_action_type()));
}

void IncentiveCoinJointDecision::ProcessAfterDecision(ContextData* session_data, AdCommon* p_ad) {
  if (enable_joint_decision_link_check_) {
    p_ad->set_incentive_coin_joint_decision_tag(0);
  }
  if (p_ad->get_reward_coin() <= 0) {
    p_ad->set_incentive_coin_joint_decision_tag(0);
  }
  if (spdm_enable_low_value_req_def_stg_ &&
      p_ad->get_incntv_ad_e_price() < spdm_low_value_req_thres_ && p_ad->get_deep_rewarded_coin() <= 0) {
    p_ad->set_incentive_coin_joint_decision_tag(0);
  }
}

void IncentiveCoinJointDecision::FillStyle(ContextData* session_data, AdCommon* p_ad) {
  if (p_ad->get_deep_rewarded_coin() == 0) {
    return;
  }
  if (plugin_name_ == "InvokedCoinJointDecision") {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED),
      kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED});
  } else if (plugin_name_ == "ConvCoinJointDecision") {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP),
      kuaishou::ad::RewardStyleType::ACTIVE_APP});
  } else if (plugin_name_ == "OrderCoinJointDecision") {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER),
      kuaishou::ad::RewardStyleType::PLAY_AND_ORDER});
  } else if (plugin_name_ == "LiveOrderCoinJointDecision") {
    p_ad->set_order_deep_incentive_type(kuaishou::ad::OrderDeepIncentiveType::ORDER_DISCOUNT_COIN);
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER),
      kuaishou::ad::RewardStyleType::PLAY_AND_ORDER});
  }
}

/* 辅助函数统一放在下面 */
void IncentiveCoinJointDecision::ClearBeforeDecision(ContextData* session_data, AdCommon* p_ad) {
  if (p_ad->get_reward_styles().count(
        kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED)) &&
      plugin_name_ == "InvokedCoinJointDecision") {
    p_ad->mutable_reward_styles()->erase(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED));
  }
  if (p_ad->get_reward_styles().count(
        kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP)) &&
      plugin_name_ == "ConvCoinJointDecision") {
    p_ad->mutable_reward_styles()->erase(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP));
  }
  if (p_ad->get_reward_styles().count(
        kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER)) &&
      plugin_name_ == "OrderCoinJointDecision" &&
      p_ad->get_campaign_type() != kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {
    p_ad->mutable_reward_styles()->erase(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER));
  }
  p_ad->set_pec_uplift_cvr(0);
  p_ad->set_incentive_uplift_ecpm_ratio(1);
  p_ad->set_deep_rewarded_rate(0);
  p_ad->set_deep_incentive_cvr_adjust_ratio(1);
}

double IncentiveCoinJointDecision::GetQuitRate(AdCommon* p_ad, int64_t target_coin) {
  const std::unordered_map<int64_t, double>& ad_coin_quit_rate_map = p_ad->get_coin_quit_rate_map();
  std::vector<std::int64_t> ad_coin_quit_rate_map_keys;
  for (const auto& pair : ad_coin_quit_rate_map) { ad_coin_quit_rate_map_keys.push_back(pair.first); }
  int max_size = ad_coin_quit_rate_map_keys.size();
  if (max_size == 0) {
    return 0.0;
  }
  std::sort(ad_coin_quit_rate_map_keys.begin(), ad_coin_quit_rate_map_keys.end());
  double target_coin_quit_rate = 0;
  if (ad_coin_quit_rate_map.count(target_coin) == 0) {
    int right = 0;
    for (; right < max_size; right++) {
      if (target_coin < ad_coin_quit_rate_map_keys[right]) {
        break;
      }
    }
    // 处理边界条件
    if (right <= 0 || right >= max_size) {
      right = right <= 0 ? 1 : max_size - 1;
    }
    int left = right - 1;
    if (fix_ucc_get_quit_rate_) {
      if (left < 0 || left >= max_size) {
        return 0.0;
      }
    } else {
      if (left <= 0 || left >= max_size) {
        return 0.0;
      }
    }
    int64_t left_coin = ad_coin_quit_rate_map_keys[left];
    int64_t right_coin = ad_coin_quit_rate_map_keys[right];
    if (right_coin - left_coin > 0 &&
        ad_coin_quit_rate_map.find(left_coin) != ad_coin_quit_rate_map.end() &&
        ad_coin_quit_rate_map.find(right_coin) != ad_coin_quit_rate_map.end()) {
      double left_coin_quit_rate = ad_coin_quit_rate_map.find(left_coin)->second;
      double right_coin_quit_rate = ad_coin_quit_rate_map.find(right_coin)->second;
      target_coin_quit_rate = left_coin_quit_rate + (target_coin - left_coin) *
                                                        (right_coin_quit_rate - left_coin_quit_rate) /
                                                        (right_coin - left_coin);
      target_coin_quit_rate = std::min(std::max(target_coin_quit_rate, 0.0), 1.0);
    }
    p_ad->mutable_coin_quit_rate_map()->insert({target_coin, target_coin_quit_rate});
    return target_coin_quit_rate;
  } else if (ad_coin_quit_rate_map.find(target_coin) != ad_coin_quit_rate_map.end()) {
    return ad_coin_quit_rate_map.find(target_coin)->second;
  }
  return 0;
}

// 获取下发时间间隔
int64_t IncentiveCoinJointDecision::GetIncentiveStyleDeliverTimeGap(
  const ContextData* session_data, std::string* reward_type) {
  const auto now = session_data->get_current_timestamp_nodiff();
  int64_t latest_deliver_timestamp = 0;
  kuaishou::ad::RewardStyleType target_style;
  if (*reward_type == "invoke") {
    target_style = kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED;
  } else if (*reward_type == "active") {
    target_style = kuaishou::ad::RewardStyleType::ACTIVE_APP;
  } else if (*reward_type == "order") {
    target_style = kuaishou::ad::RewardStyleType::PLAY_AND_ORDER;
  } else {
    return now / 1000 / 1000;
  }
  // 遍历服务端 browseSet, 获取最近一次下发样式的时间
  const auto& ad_browsed_infos = session_data->get_rank_request()->ad_request().ad_user_info().ad_browsed_info();  // NOLINT
  for (const auto& browsed : ad_browsed_infos) {
    for (const auto& detail_info : browsed.ad_detail_info()) {
      if (detail_info.award_inspire_style() == target_style &&
          browsed.timestamp() > latest_deliver_timestamp) {
        latest_deliver_timestamp = browsed.timestamp();
      }
    }
  }
  return (now - latest_deliver_timestamp) / 1000 / 1000;
}

// 获取任务完成次数
int64_t IncentiveCoinJointDecision::GetIncentiveStyleTaskCompleteCount(
  const ContextData* session_data, std::string* reward_type, std::unordered_set<int64_t>* product_id_list) {
  const auto& ad_action_infos = session_data->get_rank_request()->ad_request()
                                    .ad_session_response_pack()
                                    .response()
                                    .user_action_info()
                                    .ad_action_info();

  int64_t now_s = session_data->get_current_timestamp_nodiff() / 1000000;
  auto bd = absl::FromUnixSeconds(now_s).In(absl::LocalTimeZone());
  int64_t start_of_day = now_s - bd.hour * 3600 - bd.minute * 60 - bd.second;   // 单位：秒

  int task_compelete_count = 0;
  for (const auto& action_info : ad_action_infos) {
    for (const auto& detail_info : action_info.ad_detail_info()) {
      if (((detail_info.award_play_and_invoked() && *reward_type == "invoke") ||
          (detail_info.award_active_app() && *reward_type == "active")) &&
          detail_info.timestamp() / 1000 > start_of_day) {
        // 保存该用户当天完成的激励拉活任务的 product_id 列表
        product_id_list->insert(detail_info.product_hash());
        // 统计当天完成拉活激励任务的次数
        ++task_compelete_count;
      }
    }
  }
  return task_compelete_count;
}

double IncentiveCoinJointDecision::SafeGetDoubleVectorValue(const std::vector<double>& target_vector,
  const int& index, double defalut_value, ContextData* session_data, const std::string& vector_tag) {
  if (index >= 0 && index < target_vector.size()) {
    return target_vector[index];
  }
  RANK_DOT_COUNT(session_data, 1, "deep_incentive_vector_error", vector_tag);
  return defalut_value;
}

}  // namespace ad_rank
}  // namespace ks
