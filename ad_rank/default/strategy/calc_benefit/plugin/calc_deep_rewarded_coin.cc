#include <algorithm>
#include <cstdint>
#include <memory>
#include <numeric>
#include <string>
#include <valarray>
#include <vector>
#include <unordered_set>

#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/default/strategy/calc_benefit/plugin/calc_deep_rewarded_coin.h"
#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/default/params/calc_benefit_param.h"
#include "teams/ad/ad_rank/utils/kconf/kconf_data.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/ad_rank/utils/utility/utility.h"
#include "teams/ad/ad_base/src/common/app_version_compare.h"

using kuaishou::ad::AdEnum;
using ks::engine_base::PredictType;
namespace ks {
namespace ad_rank {

const char* CalcDeepRewardedCoinPlugin::Name() { return "CalcDeepRewardedCoinPlugin"; }

void CalcDeepRewardedCoinPlugin::Clear() {
  uplift_cvr_list_.clear();
}

bool CalcDeepRewardedCoinPlugin::IsRun(const ContextData* session_data, const Params* params,
                                         AdRankUnifyScene pos, const AdList* adlist) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(const_cast<Params*>(params));
  if (params_ == nullptr) {
    return false;
  }
  if (SPDM_enable_order_deep_incentive_total_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  DeepRewardedParams* deep_rewarded_params = &params_->deep_rewarded_params;
  if (SPDM_enable_incentive_coin_joint_decision(session_data->get_spdm_ctx())) {
    return false;
  }
  if (SPDM_enable_order_d_i_holdout_for_qcpx(session_data->get_spdm_ctx())) {
    return false;
  }
  // 只对激励流量生效
  if (!session_data->get_is_rewarded()) {
    return false;
  }
  if (SPDM_enable_deep_incentive_total_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  if (deep_rewarded_params->enable_unify_calc_deep_rewarded_coin) {
    return true;
  }
  return false;
}

bool CalcDeepRewardedCoinPlugin::Admit(AdCommon* p_ad, DeepRewardedParams* params_,
                                         ContextData* session_data) {
  // 有下单样式才生效。先只对下单激励生效，后续放开到拉活，激活
  if (!p_ad->get_reward_styles().count(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    return false;
  }

  return true;
}

StraRetCode CalcDeepRewardedCoinPlugin::Process(ContextData* session_data, Params* params,
                                                  AdRankUnifyScene pos, AdList* adlist) {
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (params_ == nullptr) {
    return StraRetCode::ABORT;
  }
  DeepRewardedParams* deep_rewarded_params = &params_->deep_rewarded_params;
  if (!deep_rewarded_params->deep_rewarded_coin_list.empty()) {
    uplift_cvr_list_.resize(deep_rewarded_params->deep_rewarded_coin_list.size(), 0.0);
  }
  // 风控判断
  if (SPDM_enable_risk_user_skip_deep_incentive(session_data->get_spdm_ctx())) {
    int reward_antispam_code = session_data->get_rank_request()->ad_request().front_antispam_code();
    base::Json json_extra = base::Json(base::StringToJson(
        session_data->get_rank_request()->ad_request().extra_request_ctx()));
    int live_antispam_code = 0;
    if (json_extra.IsObject()) {
      live_antispam_code = json_extra.GetInt("liveAntispamCode", 0);
    }
    if (reward_antispam_code > 0 || live_antispam_code == 1001) {
      return StraRetCode::SUCC;
    }
  }
  for (auto* p_ad : adlist->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    if (Admit(p_ad, deep_rewarded_params, session_data)) {
      uplift_cvr_list_.assign(deep_rewarded_params->deep_rewarded_coin_list.size(), 0.0);
      GetDeepRewardedUpliftCvr(p_ad, deep_rewarded_params, session_data);
      CalcOptimalDeepRewardedCoin(p_ad, deep_rewarded_params, session_data);
    }
  }
  return StraRetCode::SUCC;
}

void CalcDeepRewardedCoinPlugin::GetDeepRewardedUpliftCvr(AdCommon* p_ad, DeepRewardedParams* params,
                                                      ContextData* session_data) {
  if (uplift_cvr_list_.size() > 0) {
    utility::GetCouponDiscountRatio(p_ad, session_data);
    uplift_cvr_list_[0] = 0.0;
    if (uplift_cvr_list_.size() != p_ad->get_pec_coupon_uplift_list().size() + 1) {
      return;
    }
    for (int i = 1; i < uplift_cvr_list_.size(); i++) {
      uplift_cvr_list_[i] = p_ad->get_pec_coupon_uplift_list()[i-1];
    }
  }
}

void CalcDeepRewardedCoinPlugin::CalcOptimalDeepRewardedCoin(AdCommon* p_ad, DeepRewardedParams* params,
                                                             ContextData* session_data) {
  uint64_t product_price = p_ad->get_payment_per_order();
  if (params->enable_deep_rewarded_use_min_price) {
    product_price = p_ad->get_product_min_price() * 10;
  }
  double lambda = params->lambda;
  const auto& app_id = session_data->get_pos_manager_base().GetRequestAppId();
  if (app_id == "kuaishou") {
    lambda = params->lambda_main;
  }
  p_ad->set_deep_rewarded_coin(0);
  // 策略 tag
  if (params->enable_deep_rewarded_platform_profit_maximum) {
  auto CalcPlatformProfit = [&](AdCommon* p_ad, double discount_amount, double uplift_cvr) {
      double origin_cpm = 0.0;
      double profit = 0.0;
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) {
        int64_t cpa_bid =
            p_ad->get_cpa_bid() > 0 ? p_ad->get_cpa_bid() : p_ad->get_auto_cpa_bid();
        origin_cpm = cpa_bid * p_ad->get_unify_cvr_info().value * p_ad->get_unify_ctr_info().value;
        profit = (cpa_bid - lambda * discount_amount) *
                 (p_ad->get_unify_cvr_info().value + uplift_cvr) * p_ad->get_unify_ctr_info().value;
      } else if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                 p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
                 p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS ||
                 p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) {
        if (p_ad->get_roi_ratio() > 0) {
          origin_cpm = product_price / p_ad->get_roi_ratio() * p_ad->get_unify_cvr_info().value *
             p_ad->get_unify_ctr_info().value;
          profit = (product_price / p_ad->get_roi_ratio() - lambda * discount_amount) *
                   (p_ad->get_unify_cvr_info().value + uplift_cvr) * p_ad->get_unify_ctr_info().value;
          if (params->enable_deep_rewarded_use_min_price) {
            origin_cpm =
             p_ad->get_unify_ltv_info().value / p_ad->get_roi_ratio() * p_ad->get_unify_cvr_info().value *
             p_ad->get_unify_ctr_info().value;
             profit = (p_ad->get_unify_ltv_info().value / p_ad->get_roi_ratio() - lambda * discount_amount) *
                   (p_ad->get_unify_cvr_info().value + uplift_cvr) * p_ad->get_unify_ctr_info().value;
          }
        }
      }
      p_ad->set_pec_coupon_origin_cpm(origin_cpm);
      return profit;
    };
    ProfitMaximumStrategy(p_ad, params, session_data, CalcPlatformProfit);
  } else if (params->enable_random_deep_rewarded_coin) {
    RandomIssueCoinStrategy(p_ad, params, session_data);
  } else {
    // 下单激励下按照商品价格折扣发金币
    if (product_price > 0 &&
       p_ad->get_reward_styles().count(
       kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
      p_ad->set_deep_rewarded_coin(params->inspire_order_deep_rewarded_fix_discount_ratio * product_price);
      p_ad->set_pec_final_price(product_price);
      p_ad->set_pec_final_ratio(params->inspire_order_deep_rewarded_fix_discount_ratio);
    }
    if (params->enable_inspire_order_deep_rewarded_fix_coin) {
      // 固定值发金币
      p_ad->set_deep_rewarded_coin(params->inspire_order_deep_rewarded_fix_coin);
    }
  }
  // 对激励部分流量单独处理
  if (params->enable_specific_deep_rewarded_coin
      && p_ad->Is(AdFlag::is_inner_loop_order_ad) && p_ad->Is(AdFlag::is_merchant_live)) {
    p_ad->set_deep_rewarded_coin(params->inspire_order_deep_rewarded_fix_coin);
  }

  // 如果判断深度金币为 0，则移除下单激励样式
  if (p_ad->get_deep_rewarded_coin() <= 0 && p_ad->get_reward_styles().count(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->erase(
          kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER));
  }
  RANK_DOT_STATS(session_data,  p_ad->get_deep_rewarded_coin(),
                               "ad_rank.deep_rewarded_coin_delivery", params->exp_tag);
}

void CalcDeepRewardedCoinPlugin::ProfitMaximumStrategy(
    AdCommon* p_ad, DeepRewardedParams* params, ContextData* session_data,
    std::function<double(AdCommon*, double, double)> proft_func) {
  double profit = 0;
  double max_profit = 0;
  int index = 0;
  double max_uplift_cvr = 0.0;
  const auto& deep_rewarded_coin_list = params->deep_rewarded_coin_list;
  for (int i = 0; i < uplift_cvr_list_.size(); i++) {
    // 金币单位要除 10 换算成厘
    double discount_amount_temp = deep_rewarded_coin_list[i] / 10.0;
    double uplift_cvr = uplift_cvr_list_[i] - uplift_cvr_list_[0];
    profit = proft_func(p_ad, discount_amount_temp, uplift_cvr);
    if (profit > max_profit) {
      max_profit = profit;
      p_ad->set_deep_rewarded_coin(floor(deep_rewarded_coin_list[i]));
      index = i;
      max_uplift_cvr = uplift_cvr;
    }
  }
  // 设置最终 profit
  p_ad->set_pec_coupon_uplift_profit(max_profit);
  p_ad->set_pec_uplift_cvr(max_uplift_cvr);
}

void CalcDeepRewardedCoinPlugin::RandomIssueCoinStrategy(AdCommon* p_ad, DeepRewardedParams* params,
                                                             ContextData* session_data) {
  const auto& rewarded_coin_list = params->deep_rewarded_coin_list;
  int index = ad_base::AdRandom::GetInt(0, rewarded_coin_list.size() - 1);
  p_ad->set_deep_rewarded_coin(rewarded_coin_list[index]);
}

/* -----拉活激励----- */
const char* InvokedDeepCoinDecisionPlugin::Name() { return "InvokedDeepCoinDecisionPlugin"; }

void InvokedDeepCoinDecisionPlugin::Clear() {
  invoked_uplift_cvr_list_.clear();
  invoked_treatment_coin_list_.clear();
  cvr_ratio_list_.clear();
  invoked_product_ids_.clear();
  invoked_global_treatment_coin_list_.clear();
}

bool InvokedDeepCoinDecisionPlugin::IsRun(const ContextData* session_data, const Params* params,
                                          AdRankUnifyScene pos, const AdList* adlist) {
  // 只对激励流量生效
  if (!session_data->get_is_rewarded() || adlist->Size() <= 0) {
    return false;
  }
  if (SPDM_enable_incentive_coin_joint_decision(session_data->get_spdm_ctx()) &&
      !SPDM_enable_joint_decision_skip_invoked(session_data->get_spdm_ctx())) {
    return false;
  }
  if (SPDM_enable_deep_incentive_total_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  // 拉活 holdout 实验
  if (SPDM_enable_invoked_deep_incentive_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  // holdout v2 此开关控制是否生效
  if (SPDM_enable_invoked_deep_incentive_holdout_v2(session_data->get_spdm_ctx())) {
    return false;
  }
  // 全局 rct 开关
  if (SPDM_enable_invoked_d_i_global_rct(session_data->get_spdm_ctx())) {
    return true;
  }
  return true;
}

StraRetCode InvokedDeepCoinDecisionPlugin::Process(ContextData* session_data, Params* params,
                                                  AdRankUnifyScene pos, AdList* adlist) {
  Initialize(session_data);

  if (!PvAdmit(session_data)) {
    return StraRetCode::SUCC;
  }

  auto invoked_product_name_blacklist = RankKconfUtil::invokedProductNameBlacklist();
  bool enable_iaa_deep_coin_coef = SPDM_enable_iaa_deep_coin_coef(session_data->get_spdm_ctx());
  double invoked_iaa_deep_coin_coef = SPDM_invoked_iaa_deep_coin_coef(session_data->get_spdm_ctx());
  bool enable_deep_incentive_rct_add_native =
    SPDM_enable_deep_incentive_rct_add_native(session_data->get_spdm_ctx());
  const auto& invoked_admit_config = RankKconfUtil::incentiveInvokedDeepIncentiveRatioConfig();
  const auto& d_i_specific_config = RankKconfUtil::deepIncentiveSpecificConfig();
  const auto& specific_deep_coin_coef_config = RankKconfUtil::specificDeepCoinCoefConfig();
  const auto now = session_data->get_current_timestamp_nodiff();

  for (auto* p_ad : adlist->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    if (!AdAdmit(session_data, p_ad)) {
      continue;
    }

    // 一些不太适合放到 AdAdmit 中的判断条件在这里单拎出来
    if (p_ad->get_reward_styles().count(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED))) {
      p_ad->mutable_reward_styles()->erase(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED));
    }

    if (invoked_admit_config) {
      // 拉活激励新策略控比逻辑
      // 控比优先级 account_id > product_name
      const auto& account_config = invoked_admit_config->data().account_id_config();
      const auto& product_name_config = invoked_admit_config->data().product_name_config();
      double random_number = ad_base::AdRandom::GetDouble();
      double invoked_style_ratio = -1;
      // 账户 id 控比优先级更高
      if (account_config.find(absl::StrCat(p_ad->get_account_id())) != account_config.end()) {
        auto iter = account_config.find(absl::StrCat(p_ad->get_account_id()));
        invoked_style_ratio = iter->second;
      } else if (product_name_config.find(p_ad->get_product_name()) != product_name_config.end()) {
        auto iter = product_name_config.find(p_ad->get_product_name());
        invoked_style_ratio = iter->second;
      }
      if (invoked_style_ratio != -1 && random_number > invoked_style_ratio) {
        // 未配置控比默认生效，已配置控比按控比比例进行准入
        continue;
      }
    } else {
      if (invoked_product_name_blacklist &&
        invoked_product_name_blacklist->count(p_ad->get_product_name()) > 0) {
        continue;
      }
    }

    // 针对特定产品名修改 coin_ratio 和 coin_coef
    specific_uplift_coin_ratio_ = -1;
    specific_coin_coef_ = -1;

    if (enable_iaa_deep_coin_coef && is_iaa_pos_) {
      if (specific_coin_coef_ == -1) {
        specific_coin_coef_ = invoked_iaa_deep_coin_coef;
      } else {
        specific_coin_coef_ *= invoked_iaa_deep_coin_coef;
      }
    }

    // 策略逻辑
    // 获取 predict uplift cvr
    GetPredictUpliftCvr(session_data, p_ad);
    if (is_rct_ || is_global_rct_) {
      // rct 实验
      RandomDeepCoinStrategy(session_data, p_ad);
    } else if (!enable_deep_incentive_rct_add_native || p_ad->Is(AdFlag::IsHardAd) ||
        enable_invoked_d_i_base_use_logits_model_) {
      // 金币决策
      DeepCoinDecision(session_data, p_ad);
    }
  }
  return StraRetCode::SUCC;
}

void InvokedDeepCoinDecisionPlugin::Initialize(ContextData* session_data) {
  enable_invoked_d_i_base_use_logits_model_ =
    SPDM_enable_invoked_d_i_base_use_logits_model(session_data->get_spdm_ctx());
  // treatment coin 初始化
  invoked_treatment_coin_list_ = {0, 100, 200 , 600};
  // uplift cvr list 初始化
  if (!invoked_treatment_coin_list_.empty()) {
    // size 保持一致
    invoked_uplift_cvr_list_.resize(invoked_treatment_coin_list_.size(), 0.0);
  }
  // model pts 初始化
  incentive_uplift_cvr_pts_ = {
    PredictType::PredictType_incentive_invoked_uplift_cvr_0,
    PredictType::PredictType_incentive_invoked_uplift_cvr_1,
    PredictType::PredictType_incentive_invoked_uplift_cvr_2,
    PredictType::PredictType_incentive_invoked_uplift_cvr_3
  };
  // 领奖率模型 pts 初始化
  incentive_rewarded_ratio_pts_ = {
    PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
    PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
    PredictType::PredictType_incentive_deep_task_rewarded_ratio_3
  };
  if (enable_invoked_d_i_base_use_logits_model_) {
    invoked_treatment_coin_list_ = {0, 100, 200, 300, 400, 500};
    incentive_uplift_cvr_pts_ = {
      PredictType::PredictType_incentive_deep_task_uplift_logits_0,
      PredictType::PredictType_incentive_deep_task_uplift_logits_1,
      PredictType::PredictType_incentive_deep_task_uplift_logits_2,
      PredictType::PredictType_incentive_deep_task_uplift_logits_3,
      PredictType::PredictType_incentive_deep_task_uplift_logits_4,
      PredictType::PredictType_incentive_deep_task_uplift_logits_5
    };
    incentive_rewarded_ratio_pts_ = {
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_3,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_4,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_5
    };
    invoked_uplift_cvr_list_.assign(6, 0.0);
  }
  invoked_rewarded_ratio_list_.resize(incentive_rewarded_ratio_pts_.size(), 0.0);
  // uplift cvr ratio 初始化
  uplift_cvr_ratio_ = SPDM_invoked_uplift_cvr_ratio(session_data->get_spdm_ctx());
  // uplift cvr 上界，防止模型预估值起飞
  uplift_cvr_upper_ = 1.0;
  // treatment coin ratio 初始化，领奖率系数
  invoked_treatment_coin_ratio_ = SPDM_invoked_treatment_coin_ratio(session_data->get_spdm_ctx());
  if (enable_invoked_d_i_base_use_logits_model_) {
    invoked_treatment_coin_ratio_ =
      SPDM_invoked_d_i_coin_ratio_for_logits_model(session_data->get_spdm_ctx());
  }
  // 实验 exp tag 初始化
  exp_tag_ = "invoked_base";
  // 打点前缀初始化
  prefix_ = "ad_rank.invoked_uplift_model.";
  // rct tag 初始化
  is_rct_ = false;
  // 全局 RCT tag 初始化
  is_global_rct_ = false;
  double random_number = ad_base::AdRandom::GetDouble();
  if (random_number < SPDM_invoked_d_i_global_rct_ratio(session_data->get_spdm_ctx()) &&
      SPDM_enable_invoked_d_i_global_rct(session_data->get_spdm_ctx())) {
    is_global_rct_ = true;
  }
  const auto& deep_incentive_rct_config = RankKconfUtil::deepIncentiveRctConfig();
  if (SPDM_enable_invoked_d_i_global_rct(session_data->get_spdm_ctx()) &&
      deep_incentive_rct_config) {
    const auto& invoked_treatment_coin_list =
      deep_incentive_rct_config->data().rct_config_v1().invoked_treatment_coin_list();
    const auto& invoked_sub_page_id =
      deep_incentive_rct_config->data().rct_sub_page_id().invoked_sub_page_id();
    invoked_global_treatment_coin_list_.assign(invoked_treatment_coin_list.begin(),
      invoked_treatment_coin_list.end());
    if (invoked_global_treatment_coin_list_.empty() ||
        (SPDM_enable_deep_incentive_rct_add_native(session_data->get_spdm_ctx()) &&
        std::find(invoked_sub_page_id.begin(), invoked_sub_page_id.end(),
        session_data->get_sub_page_id()) == invoked_sub_page_id.end())) {
      is_global_rct_ = false;
    }
  }
  if (SPDM_enable_deep_incentive_rct_migrate(session_data->get_spdm_ctx())) {
    is_rct_ = false;
    is_global_rct_ = false;
  }
  // cvr 系数
  const std::vector<std::string>& cvr_ratio_str_list =
      absl::StrSplit(SPDM_invoked_uplift_cvr_ratio_str(session_data->get_spdm_ctx()), ",");
  for (auto& cvr_ratio_str : cvr_ratio_str_list) {
    double cvr_raito = 1;
    if (absl::SimpleAtod(cvr_ratio_str, &cvr_raito)) {
      cvr_ratio_list_.push_back(cvr_raito);
    } else {
      cvr_ratio_list_.push_back(1.0);
    }
  }
  if (cvr_ratio_list_.size() != invoked_uplift_cvr_list_.size()) {
    // 兜底，防止 size 不一致后续运行出错
    cvr_ratio_list_.assign(invoked_uplift_cvr_list_.size(), 1.0);
  }
  // 最优 uplift cvr ratio
  optimal_uplift_cvr_ratio_ = SPDM_invoked_optimal_uplift_cvr_ratio(session_data->get_spdm_ctx());
  if (enable_invoked_d_i_base_use_logits_model_) {
    optimal_uplift_cvr_ratio_ = SPDM_invoked_d_i_clip_ratio_for_logits_model(session_data->get_spdm_ctx());
  }
  // 拉活激励使用领奖率系数计算
  enable_invoked_deep_incentive_use_rewarded_ratio_ =
    SPDM_enable_invoked_deep_incentive_use_rewarded_ratio(session_data->get_spdm_ctx());
  // 记录领奖率用于后续毛利最大化
  enable_deep_incentive_gross_max_by_model_ =
    SPDM_enable_deep_incentive_gross_max_by_model(session_data->get_spdm_ctx());
  deep_incentive_gross_max_coef_ = SPDM_deep_incentive_gross_max_coef(session_data->get_spdm_ctx());
  // 金币打折系数
  invoked_deep_coin_coef_ = SPDM_invoked_deep_incentive_coin_coef(session_data->get_spdm_ctx());
  // 整体 cvr 校准系数
  const std::vector<std::string>& cvr_adjust_ratio_str_list =
      absl::StrSplit(SPDM_invoked_deep_incentive_adjust_cvr_ratio_str(session_data->get_spdm_ctx()), ",");
  invoked_adjust_cvr_list_.clear();
  for (auto& cvr_adjust_ratio_str : cvr_adjust_ratio_str_list) {
    double cvr_adjust_raito = 1;
    if (absl::SimpleAtod(cvr_adjust_ratio_str, &cvr_adjust_raito)) {
      invoked_adjust_cvr_list_.push_back(cvr_adjust_raito);
    } else {
      invoked_adjust_cvr_list_.push_back(1.0);
    }
  }
  if (enable_invoked_d_i_base_use_logits_model_) {
    invoked_adjust_cvr_list_.clear();
    const std::vector<std::string>& cvr_adjust_ratio_str_for_logits_model =
      absl::StrSplit(SPDM_invoked_d_i_cvr_adjust_str_for_logits_model(session_data->get_spdm_ctx()), ",");
    for (auto& cvr_adjust_ratio_str : cvr_adjust_ratio_str_for_logits_model) {
      double cvr_adjust_raito = 1;
      if (absl::SimpleAtod(cvr_adjust_ratio_str, &cvr_adjust_raito)) {
        invoked_adjust_cvr_list_.push_back(cvr_adjust_raito);
      } else {
        invoked_adjust_cvr_list_.push_back(1.0);
      }
    }
    if (invoked_adjust_cvr_list_.size() != invoked_uplift_cvr_list_.size()) {
      invoked_adjust_cvr_list_.assign(invoked_uplift_cvr_list_.size(), 1);
    }
  }
  // 记录领奖率
  enable_deep_incentive_record_rewarded_ratio_ =
    SPDM_enable_deep_incentive_record_rewarded_ratio(session_data->get_spdm_ctx());
  // iaa 广告位判断
  is_iaa_pos_ = false;
  const auto& short_play_pos_ids = RankKconfUtil::shortPlayDeepIncentivePosIds();
  is_iaa_pos_ = short_play_pos_ids &&
    short_play_pos_ids->count(session_data->get_pos_manager_base().GetMediumPosId());
  // 记录观测数据
  invoked_d_i_ob_data_record_ratio_ =
    SPDM_invoked_d_i_ob_data_record_ratio(session_data->get_spdm_ctx());
  disable_deep_incentive_logits_model_uplift_cvr_limitation_ =
    SPDM_disable_deep_incentive_logits_model_uplift_cvr_limitation(session_data->get_spdm_ctx());
}

bool InvokedDeepCoinDecisionPlugin::PvAdmit(ContextData* session_data) {
  // size 保持一致
  if (invoked_treatment_coin_list_.size() == 0 ||
      invoked_treatment_coin_list_.size() != incentive_uplift_cvr_pts_.size() ||
      cvr_ratio_list_.size() != invoked_treatment_coin_list_.size()) {
    RANK_DOT_COUNT(session_data, 1, "deep_incentive_error", "invoked_vector_size_unequal");
    return false;
  }
  // 风控判断
  if (SPDM_enable_risk_user_skip_deep_incentive(session_data->get_spdm_ctx())) {
    int reward_antispam_code = session_data->get_rank_request()->ad_request().front_antispam_code();
    base::Json json_extra = base::Json(base::StringToJson(
        session_data->get_rank_request()->ad_request().extra_request_ctx()));
    int live_antispam_code = 0;
    if (json_extra.IsObject()) {
      live_antispam_code = json_extra.GetInt("liveAntispamCode", 0);
    }
    if (reward_antispam_code > 0 || live_antispam_code == 1001) {
      return false;
    }
  }
  // 饭补增加深度激励
  const auto& incentive_dish_sub_page_id = RankKconfUtil::incentiveDishSubPageId();
  bool enable_deep_incentive_add_dish = SPDM_enable_deep_incentive_add_dish(session_data->get_spdm_ctx()) &&
    incentive_dish_sub_page_id && incentive_dish_sub_page_id->count(session_data->get_sub_page_id());
  // 广告位限制
  auto invoked_admit_sup_page_id = RankKconfUtil::invokedAdmitSubPageIdSet();
  if (!(is_global_rct_ && SPDM_enable_deep_incentive_rct_add_native(session_data->get_spdm_ctx())) &&
      invoked_admit_sup_page_id && !invoked_admit_sup_page_id->count(session_data->get_sub_page_id()) &&
      !(SPDM_enable_invoked_d_i_iaa_use_uplift_model(session_data->get_spdm_ctx()) &&
      is_iaa_pos_) && !(enable_deep_incentive_add_dish &&
      ks::ad_base::AppVersionCompare::Compare(session_data->get_rank_request()->
      ad_request().ad_user_info().platform_version(), "13.4.10") >= 0)) {
    return false;
  }
  // 版本号限制
  if (ks::ad_base::AppVersionCompare::Compare(session_data->get_rank_request()
      ->ad_request().ad_user_info().platform_version(), "11.7.10") < 0) {
    return false;
  }
  // 当任务次数频控开启或 product id 频控开启时，调用该函数
  std::string reward_type = "invoke";
  int task_complete_num = 0;
  if (SPDM_incentive_invoked_max_mission_num(session_data->get_spdm_ctx()) > 0) {
    task_complete_num =
      GetIncentiveStyleTaskCompleteCount(session_data, &reward_type, &invoked_product_ids_);
  }
  // 最大任务次数限制
  if (SPDM_incentive_invoked_max_mission_num(session_data->get_spdm_ctx()) > 0 &&
      task_complete_num >= SPDM_incentive_invoked_max_mission_num(session_data->get_spdm_ctx())) {
    return false;
  }
  // 下发时间间隔限制
  if (SPDM_incentive_invoked_delivery_time_gap(session_data->get_spdm_ctx()) > 0 &&
      GetIncentiveStyleDeliverTimeGap(session_data, &reward_type) <
      SPDM_incentive_invoked_delivery_time_gap(session_data->get_spdm_ctx())) {
    return false;
  }
  return true;
}

bool InvokedDeepCoinDecisionPlugin::AdAdmit(ContextData* session_data, AdCommon* p_ad) {
  // 判断营销目标和优化目标，目前只对拉活唤端生效，对拉活其他优化目标不生效
  if (p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
      p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_APP_INVOKED) {
    return false;
  }
  // producet id 频控
  int64_t product_id = conv_(p_ad->get_product_name());
  if (invoked_product_ids_.count(product_id) > 0) {
    return false;
  }
  return true;
}

void InvokedDeepCoinDecisionPlugin::GetPredictUpliftCvr(ContextData* session_data, AdCommon* p_ad) {
  invoked_uplift_cvr_list_.assign(invoked_treatment_coin_list_.size(), 0.0);
  double base_cvr = 0;
  double invoked_uplift_cvr = 0;
  double predict_cvr = 0;
  double max_uplift_cvr = 0;
  double sum_uplift_cvr = 0;
  double sum_rewarded_ratio = 0;
  double rank_cvr = std::clamp(p_ad->get_unify_cvr_info().value, 1e-18, 0.9999996941);
  double rank_logits = std::log(rank_cvr / (1 - rank_cvr + 1e-18));
  double total_logits = rank_logits;
  for (int i = 0; i < invoked_uplift_cvr_list_.size(); i++) {
    if (i == 0) {
      base_cvr = p_ad->get_predict_score(incentive_uplift_cvr_pts_[i]) * cvr_ratio_list_[i];
      if (enable_invoked_d_i_base_use_logits_model_) {
        base_cvr = rank_cvr;
      }
    }
    // 记录校准前预估的绝对 cvr
    predict_cvr = p_ad->get_predict_score(incentive_uplift_cvr_pts_[i]);
    if (enable_invoked_d_i_base_use_logits_model_) {
      total_logits += predict_cvr;
      total_logits = std::min(std::max(total_logits, -15.0), 15.0);
      predict_cvr = std::exp(total_logits) / (1 + std::exp(total_logits));
    }
    p_ad->mutable_incentive_invoked_uplift_cvr()->insert({invoked_treatment_coin_list_[i], predict_cvr});
    predict_cvr *= cvr_ratio_list_[i];
    // uplift cvr
    invoked_uplift_cvr = predict_cvr - base_cvr;
    // 记录 max && sum 用于打点
    max_uplift_cvr = std::max(max_uplift_cvr, invoked_uplift_cvr);
    sum_uplift_cvr += invoked_uplift_cvr;
    // 预估值上下界限制
    if (!disable_deep_incentive_logits_model_uplift_cvr_limitation_) {
      invoked_uplift_cvr =
        std::max(std::min(invoked_uplift_cvr * uplift_cvr_ratio_, uplift_cvr_upper_), 0.0);
    }
    invoked_uplift_cvr_list_[i] = invoked_uplift_cvr;
  }
  for (int i = 0; i < invoked_rewarded_ratio_list_.size(); i++) {
    invoked_rewarded_ratio_list_[i] = p_ad->get_predict_score(incentive_rewarded_ratio_pts_[i]);
    if (i < invoked_treatment_coin_list_.size() - 1) {
      p_ad->mutable_incentive_deep_reward_ratio()->
        insert({invoked_treatment_coin_list_[i+1], invoked_rewarded_ratio_list_[i]});
    }
    sum_rewarded_ratio += invoked_rewarded_ratio_list_[i];
  }
  // uplift model 相关打点监控
  double mean_uplift_cvr = sum_uplift_cvr / invoked_uplift_cvr_list_.size();
  double mean_rewarded_ratio = sum_rewarded_ratio / invoked_rewarded_ratio_list_.size();
  RANK_DOT_STATS(session_data, max_uplift_cvr * 1e6, absl::StrCat(prefix_, "max_uplift_cvr"), exp_tag_);
  RANK_DOT_STATS(session_data, mean_uplift_cvr * 1e6, absl::StrCat(prefix_, "mean_uplift_cvr"), exp_tag_);
  RANK_DOT_STATS(session_data, mean_rewarded_ratio * 1e6, absl::StrCat(prefix_, "mean_rewarded_ratio"), exp_tag_);  // NOLINT
}

void InvokedDeepCoinDecisionPlugin::DeepCoinDecision(ContextData* session_data, AdCommon* p_ad) {
  double profit = 0;
  double optimal_profit = 0;
  int index = 0;
  double optimal_uplift_cvr = 0.0;
  double coin_ratio = invoked_treatment_coin_ratio_;
  double coin_coef = invoked_deep_coin_coef_;
  int64_t cpa_bid = p_ad->get_cpa_bid() > 0 ? p_ad->get_cpa_bid() : p_ad->get_auto_cpa_bid();
  // 开关控制是否使用精排预估 cvr 作为 base cvr
  double base_cvr = p_ad->get_predict_score(incentive_uplift_cvr_pts_[0]) * cvr_ratio_list_[0];
  if (enable_invoked_d_i_base_use_logits_model_) {
    base_cvr = p_ad->get_unify_cvr_info().value;
  }
  // 金币决策
  for (int i = 0; i < invoked_uplift_cvr_list_.size(); i++) {
    bool is_roi_constrain_satisfied = true;
    // 金币单位要除 10 换算成厘
    double unify_treatment_coin = invoked_treatment_coin_list_[i] / 10.0;
    double uplift_cvr = invoked_uplift_cvr_list_[i];
    profit = (cpa_bid - coin_ratio * unify_treatment_coin) *
                (base_cvr + uplift_cvr) * p_ad->get_unify_ctr_info().value;
    double unify_reward_ratio = base_cvr + uplift_cvr;
    // 使用领奖率计算金币成本
    if (enable_invoked_deep_incentive_use_rewarded_ratio_ && i >= 1) {
      double rewarded_ratio = invoked_rewarded_ratio_list_[i-1];
      profit = (cpa_bid * (base_cvr + uplift_cvr) -
                rewarded_ratio * coin_ratio * unify_treatment_coin) *
                p_ad->get_unify_ctr_info().value;
      unify_reward_ratio = rewarded_ratio;
    }
    // 本次决策 roi
    double roi_this_decision = (cpa_bid * (base_cvr + uplift_cvr)) /
                                (unify_reward_ratio * unify_treatment_coin + 1e-10);
    double marginal_roi_this_decision = (cpa_bid * uplift_cvr) /
                                        (unify_reward_ratio * unify_treatment_coin + 1e-10);
    if (i == 0) {
      p_ad->set_pec_coupon_origin_cpm(profit);
    }
    if (profit > optimal_profit && is_roi_constrain_satisfied) {
      optimal_profit = profit;
      index = i;
      optimal_uplift_cvr = uplift_cvr;
    }
  }

  optimal_uplift_cvr =
    std::min(optimal_uplift_cvr, p_ad->get_unify_cvr_info().value * optimal_uplift_cvr_ratio_);
  p_ad->set_incentive_deep_base_cvr(base_cvr);
  p_ad->set_deep_rewarded_coin(floor(invoked_treatment_coin_list_[index] * coin_coef));
  p_ad->set_pec_coupon_uplift_profit(optimal_profit);
  p_ad->set_pec_uplift_cvr(optimal_uplift_cvr);
  // 记录观测数据
  double ob_data_random_number = ad_base::AdRandom::GetDouble();
  if (invoked_d_i_ob_data_record_ratio_ > 0 && ob_data_random_number < invoked_d_i_ob_data_record_ratio_) {
    p_ad->set_incentive_deep_coin_rct_tag(10);
  }
  if (index >= 0 && index < invoked_adjust_cvr_list_.size()) {
    p_ad->set_deep_incentive_cvr_adjust_ratio(invoked_adjust_cvr_list_[index]);
  }
  if ((enable_deep_incentive_gross_max_by_model_ || enable_deep_incentive_record_rewarded_ratio_) &&
      index >= 1) {
    p_ad->set_deep_rewarded_rate(invoked_rewarded_ratio_list_[index-1] * deep_incentive_gross_max_coef_);
  }
  // 最终决策金币为 0 则去掉样式
  if (p_ad->get_deep_rewarded_coin() <= 0 && p_ad->get_reward_styles().count(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED))) {
    p_ad->mutable_reward_styles()->erase(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED));
  }
  // 模型决策金币大于 0 则补充上样式
  if (p_ad->get_deep_rewarded_coin() > 0 && !p_ad->get_reward_styles().count(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED))) {
    p_ad->mutable_reward_styles()->insert(
    {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED),
    kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED});
  }
  // 打点监控
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "req_cnt"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_deep_rewarded_coin(), absl::StrCat(prefix_, "invoked_deep_coin"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, optimal_uplift_cvr * 1e6, absl::StrCat(prefix_, "optimal_uplift_cvr"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, optimal_profit, absl::StrCat(prefix_, "optimal_profit"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_pec_coupon_origin_cpm(), absl::StrCat(prefix_, "origin_cpm"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "treatment_", index), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, base_cvr * 1e6, absl::StrCat(prefix_, "base_cvr"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_unify_cvr_info().value * 1e6, absl::StrCat(prefix_, "rank_cvr"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
}

void InvokedDeepCoinDecisionPlugin::RandomDeepCoinStrategy(ContextData* session_data, AdCommon* p_ad) {
  int index = ad_base::AdRandom::GetInt(0, invoked_treatment_coin_list_.size() - 1);
  if (is_rct_ && index < invoked_treatment_coin_list_.size() &&
      index >= 0) {  // 防止数组越界
    p_ad->set_deep_rewarded_coin(invoked_treatment_coin_list_[index]);
    p_ad->set_incentive_deep_coin_rct_tag(1);
    // rct 实验不影响排序和计费
    p_ad->set_pec_uplift_cvr(0.0);
    RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "rct_treatment_", index), exp_tag_);
  }
  index = ad_base::AdRandom::GetInt(0, invoked_global_treatment_coin_list_.size() - 1);
  if (is_global_rct_ && index >= 0 && index < invoked_global_treatment_coin_list_.size()) {
    p_ad->set_deep_rewarded_coin(invoked_global_treatment_coin_list_[index]);
    p_ad->set_incentive_deep_coin_rct_tag(2);
    p_ad->set_pec_uplift_cvr(0.0);
    RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "global_rct_treatment_", index), exp_tag_);
  }
  bool style_admit = is_rct_ || is_global_rct_;
  // 若随机到的深度金币为 0 或本次 pv 没有命中 rct 则移除深度样式
  if ((p_ad->get_deep_rewarded_coin() <= 0 || !style_admit) && p_ad->get_reward_styles().count(
  kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED))) {
    p_ad->mutable_reward_styles()->erase(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED));
  }
  // 若本次 pv 命中 rct 且随机到的深度金币不为 0 但没有样式，则加上样式
  if (p_ad->get_deep_rewarded_coin() > 0 && style_admit && !p_ad->get_reward_styles().count(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED))) {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED),
      kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED});
  }
}

/* -----下单激励----- */
const char* OrderDeepCoinDecisionPlugin::Name() { return "OrderDeepCoinDecisionPlugin"; }

void OrderDeepCoinDecisionPlugin::Clear() {
  order_treatment_coin_list_.clear();
  order_uplift_cvr_list_.clear();
  cvr_ratio_list_.clear();
  order_adjust_cvr_list_.clear();
  order_price_adjust_ratio_list_.clear();
  order_uplift_logits_list_.clear();
  order_global_treatment_coin_list_.clear();
}

bool OrderDeepCoinDecisionPlugin::IsRun(const ContextData* session_data, const Params* params,
                                          AdRankUnifyScene pos, const AdList* adlist) {
  // 只对激励流量生效
  if (!session_data->get_is_rewarded() || adlist->Size() <= 0) {
    return false;
  }
  if (SPDM_enable_incentive_coin_joint_decision(session_data->get_spdm_ctx())) {
    return false;
  }
  if (SPDM_enable_deep_incentive_total_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  if (SPDM_enable_order_d_i_holdout_for_qcpx(session_data->get_spdm_ctx())) {
    return false;
  }
  // 下单 holdout 实验
  if (SPDM_enable_order_deep_incentive_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  // rct 开关
  if (SPDM_enable_order_deep_incentive_rct_switch_split(session_data->get_spdm_ctx())) {
    return true;
  }
  // ab 开关，所有返回 fasle 的逻辑都要在此之前
  if (SPDM_enable_order_uplift_deep_coin_decision(session_data->get_spdm_ctx())) {
    return true;
  }
  // 全局 rct 开关
  if (SPDM_enable_order_d_i_global_rct(session_data->get_spdm_ctx())) {
    return true;
  }
  return false;
}

StraRetCode OrderDeepCoinDecisionPlugin::Process(ContextData* session_data, Params* params,
                                                  AdRankUnifyScene pos, AdList* adlist) {
  Initialize(session_data);
  // 店铺黑名单
  auto coin_order_paied_black_author_list = RankKconfUtil::coinOrderPaiedBlackAuthorList();
  bool enable_incentive_order_uplift_model =
        SPDM_enable_incentive_order_uplift_model(session_data->get_spdm_ctx());
  bool enable_order_rct_request_uplift_model =
        SPDM_enable_order_rct_request_uplift_model(session_data->get_spdm_ctx());
  bool enable_deep_incentive_rct_add_native =
    SPDM_enable_deep_incentive_rct_add_native(session_data->get_spdm_ctx());
  bool enable_order_uplift_deep_coin_decision =
    SPDM_enable_order_uplift_deep_coin_decision(session_data->get_spdm_ctx());

  if (!PvAdmit(session_data)) {
    return StraRetCode::SUCC;
  }

  for (auto* p_ad : adlist->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    if (!AdAdmit(session_data, p_ad)) {
      continue;
    }
    if (coin_order_paied_black_author_list->count(p_ad->get_author_id()) > 0) {
      continue;
    }
    // 获取 uplift cvr
    if (enable_incentive_order_uplift_model || (enable_order_rct_request_uplift_model && is_rct_)) {
      GetPredictUpliftCvr(session_data, p_ad);
    }

    p_ad->set_pec_uplift_cvr(0);
    if ((!enable_model_decision_skip_rct_ && is_rct_) || is_global_rct_) {
      // rct 实验
      RandomDeepCoinStrategy(session_data, p_ad);
    } else if (!enable_deep_incentive_rct_add_native || (!is_global_rct_ &&
      enable_order_uplift_deep_coin_decision) || enable_order_d_i_base_use_logits_model_) {
      // 清空样式
      if (p_ad->get_reward_styles().count(
          kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
        p_ad->mutable_reward_styles()->erase(
          kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER));
      }
      // 金币决策
      DeepCoinDecision(session_data, p_ad);
    }
  }
  return StraRetCode::SUCC;
}

void OrderDeepCoinDecisionPlugin::Initialize(ContextData* session_data) {
  enable_order_d_i_base_use_logits_model_ =
    SPDM_enable_order_d_i_base_use_logits_model(session_data->get_spdm_ctx());
  // treatment coin 初始化
  order_treatment_coin_list_ = {0, 1000, 26666, 50000};
  // 实验 exp tag 初始化
  exp_tag_ = "order_base";
  // 打点前缀初始化
  prefix_ = "ad_rank.order_uplift_model.";
  // rct tag 初始化
  is_rct_ = false;
  double random_number = ad_base::AdRandom::GetDouble();
  if (random_number < SPDM_order_fix_deep_style_ratio(session_data->get_spdm_ctx()) &&
      SPDM_enable_order_rct_fix_coin(session_data->get_spdm_ctx())) {
    is_rct_ = true;
  }
  // 全局 RCT tag 初始化
  is_global_rct_ = false;
  random_number = ad_base::AdRandom::GetDouble();
  if (random_number < SPDM_order_d_i_global_rct_ratio(session_data->get_spdm_ctx()) &&
      SPDM_enable_order_d_i_global_rct(session_data->get_spdm_ctx())) {
    is_global_rct_ = true;
  }
  const auto& deep_incentive_rct_config = RankKconfUtil::deepIncentiveRctConfig();
  if (SPDM_enable_order_d_i_global_rct(session_data->get_spdm_ctx()) &&
      deep_incentive_rct_config) {
    const auto& order_treatment_coin_list =
      deep_incentive_rct_config->data().rct_config_v1().order_treatment_coin_list();
    const auto& order_sub_page_id =
      deep_incentive_rct_config->data().rct_sub_page_id().order_sub_page_id();
    order_global_treatment_coin_list_.assign(order_treatment_coin_list.begin(),
      order_treatment_coin_list.end());
    std::sort(order_global_treatment_coin_list_.begin(), order_global_treatment_coin_list_.end());
    if (order_global_treatment_coin_list_.empty() ||
        (SPDM_enable_deep_incentive_rct_add_native(session_data->get_spdm_ctx()) &&
        std::find(order_sub_page_id.begin(), order_sub_page_id.end(),
        session_data->get_sub_page_id()) == order_sub_page_id.end())) {
      is_global_rct_ = false;
    }
  }
  if (SPDM_enable_deep_incentive_rct_migrate(session_data->get_spdm_ctx())) {
    is_rct_ = false;
    is_global_rct_ = false;
  }
  // sub page id 准入
  enable_incentive_order_admit_sub_page_id_ =
    SPDM_enable_incentive_order_admit_sub_page_id(session_data->get_spdm_ctx());
  // 跳过 roas
  enable_incentive_order_skip_roas_ = SPDM_enable_incentive_order_skip_roas(session_data->get_spdm_ctx());
  // uplift cvr list 初始化
  if (!order_treatment_coin_list_.empty()) {
    // size 保持一致
    order_uplift_cvr_list_.resize(order_treatment_coin_list_.size(), 0.0);
  }
  // 模型决策跳过 rct
  enable_model_decision_skip_rct_ = SPDM_enable_model_decision_skip_rct(session_data->get_spdm_ctx());
  // 模型 pts 初始化
  incentive_uplift_cvr_pts_ = {
    PredictType::PredictType_incentive_deep_task_uplift_cvr_0,
    PredictType::PredictType_incentive_deep_task_uplift_cvr_1,
    PredictType::PredictType_incentive_deep_task_uplift_cvr_2,
    PredictType::PredictType_incentive_deep_task_uplift_cvr_3
  };
  // logits 模型 pts 初始化
  incentive_uplift_logits_pts_ = {
    PredictType::PredictType_order_deep_incentive_uplift_logits_0,
    PredictType::PredictType_order_deep_incentive_uplift_logits_1,
    PredictType::PredictType_order_deep_incentive_uplift_logits_2,
    PredictType::PredictType_order_deep_incentive_uplift_logits_3
  };
  if (enable_order_d_i_base_use_logits_model_) {
    order_treatment_coin_list_ = {0, 10000, 26666, 50000, 80000, 99999};
    incentive_uplift_cvr_pts_ = {
      PredictType::PredictType_incentive_deep_task_uplift_logits_0,
      PredictType::PredictType_incentive_deep_task_uplift_logits_1,
      PredictType::PredictType_incentive_deep_task_uplift_logits_2,
      PredictType::PredictType_incentive_deep_task_uplift_logits_3,
      PredictType::PredictType_incentive_deep_task_uplift_logits_4,
      PredictType::PredictType_incentive_deep_task_uplift_logits_5
    };
  }
  // 一些 vector 初始化
  order_uplift_cvr_list_.assign(incentive_uplift_cvr_pts_.size(), 0);
  order_uplift_logits_list_.assign(incentive_uplift_logits_pts_.size(), 0);
  // 这里先清空，使得后面的 push back 逻辑从 index = 0 的位置开始
  cvr_ratio_list_.clear();
  order_adjust_cvr_list_.clear();
  // cvr 系数
  const std::vector<std::string>& cvr_ratio_str_list =
      absl::StrSplit(SPDM_order_uplift_cvr_ratio_str(session_data->get_spdm_ctx()), ",");
  bool enable_order_uplift_cvr_adjust =
        SPDM_enable_order_uplift_cvr_adjust(session_data->get_spdm_ctx());
  for (auto& cvr_ratio_str : cvr_ratio_str_list) {
    double cvr_raito = 1;
    if (absl::SimpleAtod(cvr_ratio_str, &cvr_raito) && enable_order_uplift_cvr_adjust) {
      cvr_ratio_list_.push_back(cvr_raito);
    } else {
      cvr_ratio_list_.push_back(1.0);
    }
  }
  if (cvr_ratio_list_.size() != order_uplift_cvr_list_.size()) {
    // 兜底，防止 size 不一致后续运行出错
    cvr_ratio_list_.assign(order_uplift_cvr_list_.size(), 1.0);
  }
  // 预估值上界
  uplift_cvr_upper_ = 1.0;
  // coin ratio
  order_treatment_coin_ratio_ = SPDM_order_treatment_coin_ratio(session_data->get_spdm_ctx());
  // clip ratio
  optimal_uplift_cvr_ratio_ = SPDM_order_optimal_uplift_cvr_ratio(session_data->get_spdm_ctx());
  // 金币打折系数
  order_deep_coin_coef_ = SPDM_order_deep_incentive_coin_coef(session_data->get_spdm_ctx());
  // 整体 cvr 校准系数
  const std::vector<std::string>& cvr_adjust_ratio_str_list =
      absl::StrSplit(SPDM_order_deep_incentive_adjust_cvr_ratio_str(session_data->get_spdm_ctx()), ",");
  bool enable_order_deep_incentive_adjust_cvr =
        SPDM_enable_order_deep_incentive_adjust_cvr(session_data->get_spdm_ctx());
  for (auto& cvr_adjust_ratio_str : cvr_adjust_ratio_str_list) {
    double cvr_adjust_raito = 1;
    if (absl::SimpleAtod(cvr_adjust_ratio_str, &cvr_adjust_raito) &&
        enable_order_deep_incentive_adjust_cvr) {
      order_adjust_cvr_list_.push_back(cvr_adjust_raito);
    } else {
      order_adjust_cvr_list_.push_back(1.0);
    }
  }
  if (enable_order_d_i_base_use_logits_model_) {
    order_treatment_coin_ratio_ = SPDM_order_d_i_coin_ratio_for_logits_model(session_data->get_spdm_ctx());
    optimal_uplift_cvr_ratio_ = SPDM_order_d_i_clip_ratio_for_logits_model(session_data->get_spdm_ctx());
    order_adjust_cvr_list_.clear();
    const std::vector<std::string>& cvr_adjust_ratio_str_for_logits_model =
      absl::StrSplit(SPDM_order_d_i_cvr_adjust_str_for_logits_model(session_data->get_spdm_ctx()), ",");
    for (auto& cvr_adjust_ratio_str : cvr_adjust_ratio_str_for_logits_model) {
      double cvr_adjust_raito = 1;
      if (absl::SimpleAtod(cvr_adjust_ratio_str, &cvr_adjust_raito)) {
        order_adjust_cvr_list_.push_back(cvr_adjust_raito);
      } else {
        order_adjust_cvr_list_.push_back(1.0);
      }
    }
    if (order_adjust_cvr_list_.size() != order_uplift_cvr_list_.size()) {
      order_adjust_cvr_list_.assign(order_uplift_cvr_list_.size(), 1);
    }
  }
  // 计费打折系数
  const std::vector<std::string>& price_adjust_ratio_str_list =
      absl::StrSplit(SPDM_deep_incentive_price_adjust_str_for_sv_order(session_data->get_spdm_ctx()), ",");
  for (auto& price_adjust_ratio_str : price_adjust_ratio_str_list) {
    order_price_adjust_ratio_list_.push_back(1.0);
  }
  order_deep_incentive_roi_constrain_coef_ =
    SPDM_order_deep_incentive_roi_constrain_coef(session_data->get_spdm_ctx());
  // 记录领奖率
  enable_deep_incentive_record_rewarded_ratio_ =
    SPDM_enable_deep_incentive_record_rewarded_ratio(session_data->get_spdm_ctx());
  order_deep_incentive_marginal_roi_constrain_coef_ =
    SPDM_order_deep_incentive_marginal_roi_constrain_coef(session_data->get_spdm_ctx());
  order_d_i_global_rct_coin_ratio_upper_ =
    SPDM_order_d_i_global_rct_coin_ratio_upper(session_data->get_spdm_ctx());
  // 记录观测数据
  order_d_i_ob_data_record_ratio_ =
    SPDM_order_d_i_ob_data_record_ratio(session_data->get_spdm_ctx());
  disable_deep_incentive_logits_model_uplift_cvr_limitation_ =
    SPDM_disable_deep_incentive_logits_model_uplift_cvr_limitation(session_data->get_spdm_ctx());
  // profit 阈值
  enable_order_d_i_use_profit_threshold_ =
    SPDM_enable_order_d_i_use_profit_threshold(session_data->get_spdm_ctx());
  order_d_i_use_profit_threshold_ =
    SPDM_order_d_i_use_profit_threshold(session_data->get_spdm_ctx());
  const auto& specific_industry_ids = RankKconfUtil::orderDeepIncentiveSpecificIndustryIds();
  if (specific_industry_ids) {
    specific_industry_ids_.clear();
    specific_industry_ids_.assign(specific_industry_ids->begin(), specific_industry_ids->end());
  }
}

bool OrderDeepCoinDecisionPlugin::PvAdmit(ContextData* session_data) {
  // 预留，暂时先使用线上频控策略
  // size 保持一致
  if (order_treatment_coin_list_.size() == 0) {
    return false;
  }
  if (SPDM_enable_incentive_order_uplift_model(session_data->get_spdm_ctx()) &&
      (order_treatment_coin_list_.size() != incentive_uplift_cvr_pts_.size() ||
       cvr_ratio_list_.size() != order_treatment_coin_list_.size())) {
    RANK_DOT_COUNT(session_data, 1, "deep_incentive_error", "order_roas_vector_size_unequal");
    return false;
  }
  // 风控判断
  if (SPDM_enable_risk_user_skip_deep_incentive(session_data->get_spdm_ctx())) {
    int reward_antispam_code = session_data->get_rank_request()->ad_request().front_antispam_code();
    base::Json json_extra = base::Json(base::StringToJson(
        session_data->get_rank_request()->ad_request().extra_request_ctx()));
    int live_antispam_code = 0;
    if (json_extra.IsObject()) {
      live_antispam_code = json_extra.GetInt("liveAntispamCode", 0);
    }
    if (reward_antispam_code > 0 || live_antispam_code == 1001) {
      return false;
    }
  }
  // ios 不出
  if (session_data->get_is_ios_platform()) {
    return false;
  }
  // 11.7.10 版本以下不出
  if (ks::ad_base::AppVersionCompare::Compare(
      session_data->get_rank_request()->ad_request().ad_user_info().platform_version(), "11.1.10") < 0) {
    return false;
  }
  // 饭补增加深度激励
  const auto& incentive_dish_sub_page_id = RankKconfUtil::incentiveDishSubPageId();
  bool enable_deep_incentive_add_dish = SPDM_enable_deep_incentive_add_dish(session_data->get_spdm_ctx()) &&
    incentive_dish_sub_page_id && incentive_dish_sub_page_id->count(session_data->get_sub_page_id());
  // sub page id 准入
  if (enable_incentive_order_admit_sub_page_id_) {
    auto order_admit_sub_page_id_set = RankKconfUtil::orderAdmitSubPageIdSet();
    if (order_admit_sub_page_id_set &&
        !order_admit_sub_page_id_set->count(session_data->get_sub_page_id()) &&
        !(enable_deep_incentive_add_dish &&
        ks::ad_base::AppVersionCompare::Compare(session_data->get_rank_request()->
        ad_request().ad_user_info().platform_version(), "13.4.10") >= 0)) {
      return false;
    }
  }
  // 下发时间间隔限制
  std::string reward_type = "order";
  if (SPDM_incentive_order_delivery_time_gap(session_data->get_spdm_ctx()) > 0 &&
      GetIncentiveStyleDeliverTimeGap(session_data, &reward_type) <
      SPDM_incentive_order_delivery_time_gap(session_data->get_spdm_ctx())) {
    return false;
  }
  // 任务完成次数限制
  const auto& inspire_req_info = session_data->get_rank_request()->ad_request().inspire_req_info();
  int task_finish_count = inspire_req_info.today_paid_order_num();
  if (SPDM_incentive_order_max_mission_num(session_data->get_spdm_ctx()) > 0 &&
      task_finish_count >= SPDM_incentive_order_max_mission_num(session_data->get_spdm_ctx())) {
    return false;
  }

  return true;
}

bool OrderDeepCoinDecisionPlugin::AdAdmit(ContextData* session_data, AdCommon* p_ad) {
  // 预留，暂时先使用线上频控策略
  // 判断营销目标和优化目标
  if (!(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
       p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
       p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS ||
       p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) &&
       !is_global_rct_ && !enable_order_d_i_base_use_logits_model_) {
    return false;
  }
  // 跳过 roas
  if (is_global_rct_ &&
      (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
      p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) &&
      enable_incentive_order_skip_roas_ && !enable_order_d_i_base_use_logits_model_) {
    return false;
  }
  // logits 模型准入转化目标
  if (enable_order_d_i_base_use_logits_model_ &&
      !(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
      p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
      p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
      p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI))) {
    return false;
  }
  // RCT 准入转化目标
  if (is_global_rct_ &&
      !(p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
      p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
      p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
      p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI))) {
    return false;
  }
  // 商品价格限制
  if (p_ad->get_product_min_price() * 10 < 10000 &&
      is_global_rct_ && !enable_order_d_i_base_use_logits_model_) {
    return false;
  }
  if ((is_global_rct_ || enable_order_d_i_base_use_logits_model_) &&
      p_ad->get_product_min_price() < 200) {
    return false;
  }
  // 物料类型限制
  if (p_ad->get_item_type() != kuaishou::ad::AdEnum::ITEM_PHOTO) {
    return false;
  }
  return true;
}

void OrderDeepCoinDecisionPlugin::GetPredictUpliftCvr(ContextData* session_data, AdCommon* p_ad) {
  order_uplift_cvr_list_.assign(order_treatment_coin_list_.size(), 0.0);
  double base_cvr = 0;
  double order_uplift_cvr = 0;
  double predict_cvr = 0;
  double max_uplift_cvr = 0;
  double sum_uplift_cvr = 0;
  double predict_logits = 0;
  double rank_cvr = std::clamp(p_ad->get_unify_cvr_info().value, 1e-18, 0.9999996941);
  double rank_logits = std::log(rank_cvr / (1 - rank_cvr + 1e-18));
  double total_logits = rank_logits;
  for (int i = 0; i < order_uplift_cvr_list_.size(); i++) {
    if (i == 0) {
      base_cvr = p_ad->get_predict_score(incentive_uplift_cvr_pts_[i]) * cvr_ratio_list_[i];
      if (enable_order_d_i_base_use_logits_model_) {
        base_cvr = p_ad->get_unify_cvr_info().value;
      }
    }
    // 记录校准前预估的绝对 cvr
    predict_cvr = p_ad->get_predict_score(incentive_uplift_cvr_pts_[i]);
    if (enable_order_d_i_base_use_logits_model_) {
      total_logits += predict_cvr;
      total_logits = std::min(std::max(total_logits, -15.0), 15.0);
      predict_cvr = std::exp(total_logits) / (1 + std::exp(total_logits));
    }
    p_ad->mutable_incentive_deep_uplift_cvr()->insert({order_treatment_coin_list_[i], predict_cvr});
    predict_cvr *= cvr_ratio_list_[i];
    // uplift cvr
    order_uplift_cvr = predict_cvr - base_cvr;
    // 记录 max && sum 用于打点
    max_uplift_cvr = std::max(max_uplift_cvr, order_uplift_cvr);
    sum_uplift_cvr += order_uplift_cvr;
    // 预估值上下界限制
    if (!disable_deep_incentive_logits_model_uplift_cvr_limitation_) {
      order_uplift_cvr = std::max(std::min(order_uplift_cvr, uplift_cvr_upper_), 0.0);
    }
    order_uplift_cvr_list_[i] = order_uplift_cvr;
  }
  // uplift model 相关打点监控
  double mean_uplift_cvr = sum_uplift_cvr / order_uplift_cvr_list_.size();
  RANK_DOT_STATS(session_data, max_uplift_cvr * 1e6, absl::StrCat(prefix_, "max_uplift_cvr"), exp_tag_);
  RANK_DOT_STATS(session_data, mean_uplift_cvr * 1e6, absl::StrCat(prefix_, "mean_uplift_cvr"), exp_tag_);
}

void OrderDeepCoinDecisionPlugin::DeepCoinDecision(ContextData* session_data, AdCommon* p_ad) {
  double profit = 0;
  double optimal_profit = 0;
  int index = 0;
  double optimal_uplift_cvr = 0.0;
  int64_t cpa_bid = p_ad->get_cpa_bid() > 0 ? p_ad->get_cpa_bid() : p_ad->get_auto_cpa_bid();
  double profit_threshold = 0;
  if (enable_order_d_i_use_profit_threshold_ && !specific_industry_ids_.empty() &&
      std::find(specific_industry_ids_.begin(), specific_industry_ids_.end(),
      p_ad->get_first_industry_id_v5()) != specific_industry_ids_.end()) {
    profit_threshold = order_d_i_use_profit_threshold_;
  }
  if (p_ad->get_auto_cpa_bid() > 0) {
    cpa_bid = p_ad->get_auto_cpa_bid();
  }
  // 开关控制是否使用精排预估 cvr 作为 base cvr
  double base_cvr = p_ad->get_predict_score(incentive_uplift_cvr_pts_[0]) * cvr_ratio_list_[0];
  if (enable_order_d_i_base_use_logits_model_) {
    base_cvr = p_ad->get_unify_cvr_info().value;
  }
  // 金币决策
  for (int i = 0; i < order_uplift_cvr_list_.size(); i++) {
    bool is_roi_constrain_satisfied = true;
    // 金币单位要除 10 换算成厘
    double unify_treatment_coin = order_treatment_coin_list_[i] / 10.0;
    double uplift_cvr = order_uplift_cvr_list_[i];
    profit = (cpa_bid - order_treatment_coin_ratio_ * unify_treatment_coin) *
                (base_cvr + uplift_cvr) * p_ad->get_unify_ctr_info().value;
    if (i == 0) {
      p_ad->set_pec_coupon_origin_cpm(profit);
    }
    // 本次决策 roi
    double roi_this_decision = cpa_bid / (unify_treatment_coin + 1e-10);
    double marginal_roi_this_decision = (cpa_bid * uplift_cvr) /
                                        ((base_cvr + uplift_cvr) * unify_treatment_coin + 1e-10);
    if ((profit - optimal_profit > profit_threshold) && is_roi_constrain_satisfied) {
      optimal_profit = profit;
      index = i;
      optimal_uplift_cvr = uplift_cvr;
    }
  }
  if (optimal_uplift_cvr_ratio_ > 0) {
    optimal_uplift_cvr =
      std::min(optimal_uplift_cvr, p_ad->get_unify_cvr_info().value * optimal_uplift_cvr_ratio_);
  }
  p_ad->set_deep_rewarded_coin(floor(order_treatment_coin_list_[index] * order_deep_coin_coef_));
  p_ad->set_pec_coupon_uplift_profit(optimal_profit);
  p_ad->set_pec_uplift_cvr(optimal_uplift_cvr);
  // 记录观测数据
  double ob_data_random_number = ad_base::AdRandom::GetDouble();
  if (order_d_i_ob_data_record_ratio_ > 0 && ob_data_random_number < order_d_i_ob_data_record_ratio_) {
    p_ad->set_incentive_deep_coin_rct_tag(10);
  }
  if (enable_deep_incentive_record_rewarded_ratio_) {
    p_ad->set_deep_rewarded_rate(optimal_uplift_cvr + p_ad->get_unify_cvr_info().value);
  }
  if (index >= 0 && index < order_adjust_cvr_list_.size()) {
    p_ad->set_deep_incentive_cvr_adjust_ratio(order_adjust_cvr_list_[index]);
  }
  if (p_ad->get_deep_rewarded_coin() > 0 && !p_ad->get_reward_styles().count(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER),
      kuaishou::ad::RewardStyleType::PLAY_AND_ORDER});
  }
  // 打点监控
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "req_cnt"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_deep_rewarded_coin(), absl::StrCat(prefix_, "order_deep_coin"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, optimal_uplift_cvr * 1e6, absl::StrCat(prefix_, "optimal_uplift_cvr"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, optimal_profit, absl::StrCat(prefix_, "optimal_profit"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_pec_coupon_origin_cpm(), absl::StrCat(prefix_, "origin_cpm"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "treatment_", index), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, base_cvr * 1e6, absl::StrCat(prefix_, "base_cvr"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_unify_cvr_info().value * 1e6, absl::StrCat(prefix_, "rank_cvr"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
}

void OrderDeepCoinDecisionPlugin::RandomDeepCoinStrategy(ContextData* session_data, AdCommon* p_ad) {
  int index = ad_base::AdRandom::GetInt(0, order_treatment_coin_list_.size() - 1);
  if (is_rct_ && index < order_treatment_coin_list_.size() &&
      index >= 0) {  // 防止数组越界
    p_ad->set_deep_rewarded_coin(order_treatment_coin_list_[index]);
    p_ad->set_incentive_deep_coin_rct_tag(1);
    // rct 实验不影响排序和计费
    p_ad->set_pec_uplift_cvr(0.0);
    RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "rct_treatment_", index), exp_tag_);
  }
  int max_global_treatment_index = order_global_treatment_coin_list_.size() - 1;
  if (order_d_i_global_rct_coin_ratio_upper_ > 0 && is_global_rct_) {
    double max_treatment_coin = p_ad->get_product_min_price() / order_d_i_global_rct_coin_ratio_upper_
      * 100;
    for (int i = 1 ; i < order_global_treatment_coin_list_.size(); i++) {
      if (order_global_treatment_coin_list_[i] > max_treatment_coin) {
        max_global_treatment_index = i - 1;
        break;
      }
    }
  }
  index = ad_base::AdRandom::GetInt(0, max_global_treatment_index);
  if (is_global_rct_ && index >= 0 && index < order_global_treatment_coin_list_.size()) {
    p_ad->set_deep_rewarded_coin(order_global_treatment_coin_list_[index]);
    p_ad->set_incentive_deep_coin_rct_tag(2);
    p_ad->set_pec_uplift_cvr(0.0);
    RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "global_rct_treatment_", index), exp_tag_);
  }
  bool  style_admit = is_rct_ || is_global_rct_;
  // 若随机到的深度金币为 0 或本次 pv 没有命中 rct 则移除深度样式
  if ((p_ad->get_deep_rewarded_coin() <= 0 || !style_admit) && p_ad->get_reward_styles().count(
  kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->erase(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER));
  }
  // 若本次 pv 命中 rct 且随机到的深度金币不为 0 但没有样式，则加上样式
  if (p_ad->get_deep_rewarded_coin() > 0 && style_admit && !p_ad->get_reward_styles().count(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER),
      kuaishou::ad::RewardStyleType::PLAY_AND_ORDER});
  }
}

/* -----激活激励----- */
const char* ConvDeepCoinDecisionPlugin::Name() { return "ConvDeepCoinDecisionPlugin"; }

void ConvDeepCoinDecisionPlugin::Clear() {
  conv_treatment_coin_list_.clear();
  conv_product_ids_.clear();
  conv_adjust_cvr_list_.clear();
  cvr_ratio_list_.clear();
  conv_uplift_cvr_list_.clear();
  conv_global_treatment_coin_list_.clear();
}

bool ConvDeepCoinDecisionPlugin::IsRun(const ContextData* session_data, const Params* params,
                                          AdRankUnifyScene pos, const AdList* adlist) {
  // 只对激励流量生效
  if (!session_data->get_is_rewarded() || adlist->Size() <= 0) {
    return false;
  }
  if (SPDM_enable_incentive_coin_joint_decision(session_data->get_spdm_ctx())) {
    return false;
  }
  if (SPDM_enable_deep_incentive_total_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  // 激活 holdout 实验
  if (SPDM_enable_conv_deep_incentive_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  // ab 开关，所有返回 fasle 的逻辑都要在此之前
  if (SPDM_enable_conv_uplift_deep_coin_decision(session_data->get_spdm_ctx())) {
    return true;
  }
  // 全局 rct 开关
  if (SPDM_enable_conv_d_i_global_rct(session_data->get_spdm_ctx())) {
    return true;
  }
  return false;
}

StraRetCode ConvDeepCoinDecisionPlugin::Process(ContextData* session_data, Params* params,
                                                  AdRankUnifyScene pos, AdList* adlist) {
  Initialize(session_data);

  bool enable_incentive_conv_uplift_model =
        SPDM_enable_incentive_conv_uplift_model(session_data->get_spdm_ctx());
  bool enable_conv_skip_deep_bid_twin =
        SPDM_enable_conv_skip_deep_bid_twin(session_data->get_spdm_ctx());
  bool enable_conv_d_i_coin_coef_for_specific_customer =
    SPDM_enable_conv_d_i_coin_coef_for_specific_customer(session_data->get_spdm_ctx());
  double conv_d_i_specific_customer_coin_roi_coef =
    SPDM_conv_d_i_specific_customer_coin_roi_coef(session_data->get_spdm_ctx());
  bool enable_conv_d_i_uplift_site_page_product_name_whitelist =
    SPDM_enable_conv_d_i_uplift_site_page_product_name_whitelist(session_data->get_spdm_ctx());
  bool enable_iaa_deep_coin_coef = SPDM_enable_iaa_deep_coin_coef(session_data->get_spdm_ctx());
  bool enable_deep_incentive_rct_add_native =
    SPDM_enable_deep_incentive_rct_add_native(session_data->get_spdm_ctx());
  double conv_iaa_deep_coin_coef = SPDM_conv_iaa_deep_coin_coef(session_data->get_spdm_ctx());
  const auto& coin_coef_config = RankKconfUtil::deepIncentiveConvCoinCoefConfig();
  const auto& white_list_conf = RankKconfUtil::activateAdvertiserWhiteConfig();
  const auto& black_list_conf = RankKconfUtil::activateAdvertiserBlackConfig();
  const auto& conv_admit_config = RankKconfUtil::incentiveConvDeepIncentiveRatioConfig();
  const auto& d_i_specific_config = RankKconfUtil::deepIncentiveSpecificConfig();
  const auto& d_i_site_page_product_name_config =
    RankKconfUtil::incentiveUpliftDecisionSitePageProductNameWhiteList();
  const auto& specific_deep_coin_coef_config = RankKconfUtil::specificDeepCoinCoefConfig();

  if (!PvAdmit(session_data)) {
    return StraRetCode::SUCC;
  }

  for (auto* p_ad : adlist->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    if (!AdAdmit(session_data, p_ad)) {
      continue;
    }
    if (enable_conv_d_i_uplift_site_page_product_name_whitelist &&
        d_i_site_page_product_name_config &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE &&
        !d_i_site_page_product_name_config->count(p_ad->get_product_name())) {
      continue;
    }
    if (!conv_admit_config) {
      // 黑白名单准入
      int64_t industry_id_v3 = p_ad->get_industry_id_v3();
      const auto& product_id = conv_(p_ad->get_product_name());
      if (black_list_conf->data().IsInBlackList(p_ad->get_account_id(),
          industry_id_v3, product_id)) {
        continue;
      }
      // 跳过激活双出价
      if (enable_conv_skip_deep_bid_twin &&
          (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY ||
          p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY ||
          p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_WEEK_STAY)) {
        continue;
      }
    } else {
      // 激活激励新策略控比逻辑
      // 控比优先级 account_id > product_name
      // 双出价默认不生效，单出价且未加黑 account_id or product_name 默认生效
      const auto& account_config = conv_admit_config->data().account_id_config();
      const auto& product_name_config = conv_admit_config->data().product_name_config();
      double random_number = ad_base::AdRandom::GetDouble();
      double conv_style_ratio = -1;
      // 账户 id 控比优先级更高
      if (account_config.count(absl::StrCat(p_ad->get_account_id()))) {
        auto iter = account_config.find(absl::StrCat(p_ad->get_account_id()));
        conv_style_ratio = iter->second;
      } else if (product_name_config.count(p_ad->get_product_name())) {
        auto iter = product_name_config.find(p_ad->get_product_name());
        conv_style_ratio = iter->second;
      }
      if (conv_style_ratio == -1 && p_ad->get_deep_conversion_type() !=
          kuaishou::ad::AdCallbackLog_EventType_EVENT_UNKNOWN) {
        // 未配置控比，为双出价时跳过
        continue;
      } else if (conv_style_ratio != -1 &&
        random_number > conv_style_ratio) {
        continue;
      }
    }

    // 针对特定产品名修改 coin_ratio 和 coin_coef
    specific_uplift_coin_ratio_ = -1;
    specific_coin_coef_ = -1;

    if (enable_iaa_deep_coin_coef && is_iaa_pos_) {
      if (specific_coin_coef_ == -1) {
        specific_coin_coef_ = conv_iaa_deep_coin_coef;
      } else {
        specific_coin_coef_ *= conv_iaa_deep_coin_coef;
      }
    }

    // 获取 uplift cvr
    if (enable_incentive_conv_uplift_model) {
      GetPredictUpliftCvr(session_data, p_ad);
    }
    if (is_global_rct_) {
      // rct 实验
      RandomDeepCoinStrategy(session_data, p_ad);
    } else if (!enable_deep_incentive_rct_add_native || p_ad->Is(AdFlag::IsHardAd) ||
        enable_conv_d_i_base_use_logits_model_) {
      // 清空样式
      if (p_ad->get_reward_styles().count(
          kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP))) {
        p_ad->mutable_reward_styles()->erase(
          kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP));
      }
      // 金币决策
      DeepCoinDecision(session_data, p_ad);
      // 客户出资
      if (enable_conv_d_i_coin_coef_for_specific_customer && coin_coef_config) {
        double coin_coef = 1;
        const auto& account_coin_config = coin_coef_config->data().account_id_config();
        const auto& product_name_coin_config = coin_coef_config->data().product_name_config();
        const auto& account_iter = account_coin_config.find(absl::StrCat(p_ad->get_account_id()));
        const auto& product_name_iter = product_name_coin_config.find(p_ad->get_product_name());
        if (account_iter != account_coin_config.end()) {
          coin_coef = account_iter->second;
        } else if (product_name_iter != product_name_coin_config.end()) {
          coin_coef = product_name_iter->second;
        }
        if (coin_coef > 1) {
          int before_deep_coin = p_ad->get_deep_rewarded_coin();
          int after_deep_coin = p_ad->get_deep_rewarded_coin() * coin_coef;
          double additional_price = (after_deep_coin - before_deep_coin) * p_ad->get_deep_rewarded_rate() *
            conv_d_i_specific_customer_coin_roi_coef / 10.0;
          p_ad->set_incentive_additional_price(additional_price);
          p_ad->set_deep_rewarded_coin(after_deep_coin);
        }
      }
    }
  }
  return StraRetCode::SUCC;
}

void ConvDeepCoinDecisionPlugin::Initialize(ContextData* session_data) {
  enable_conv_d_i_base_use_logits_model_ =
    SPDM_enable_order_d_i_base_use_logits_model(session_data->get_spdm_ctx());
  // treatment coin 初始化
  conv_treatment_coin_list_ = {0, 100, 200, 600};
  // 实验 exp tag 初始化
  exp_tag_ = SPDM_conv_uplift_model_exp_tag(session_data->get_spdm_ctx());
  // 打点前缀初始化
  prefix_ = "ad_rank.conv_uplift_model.";
  // 全局 RCT tag 初始化
  is_global_rct_ = false;
  double random_number = ad_base::AdRandom::GetDouble();
  if (random_number < SPDM_conv_d_i_global_rct_ratio(session_data->get_spdm_ctx()) &&
      SPDM_enable_conv_d_i_global_rct(session_data->get_spdm_ctx())) {
    is_global_rct_ = true;
  }
  const auto& deep_incentive_rct_config = RankKconfUtil::deepIncentiveRctConfig();
  if (SPDM_enable_conv_d_i_global_rct(session_data->get_spdm_ctx()) &&
      deep_incentive_rct_config) {
    const auto& conv_treatment_coin_list =
      deep_incentive_rct_config->data().rct_config_v1().conv_treatment_coin_list();
    const auto& conv_sub_page_id =
      deep_incentive_rct_config->data().rct_sub_page_id().conv_sub_page_id();
    conv_global_treatment_coin_list_.assign(conv_treatment_coin_list.begin(),
      conv_treatment_coin_list.end());
    if (conv_global_treatment_coin_list_.empty() ||
        (SPDM_enable_deep_incentive_rct_add_native(session_data->get_spdm_ctx()) &&
        std::find(conv_sub_page_id.begin(), conv_sub_page_id.end(),
        session_data->get_sub_page_id()) == conv_sub_page_id.end())) {
      is_global_rct_ = false;
    }
  }
  if (SPDM_enable_deep_incentive_rct_migrate(session_data->get_spdm_ctx())) {
    is_global_rct_ = false;
  }
  // uplift cvr list 初始化
  if (!conv_treatment_coin_list_.empty()) {
    // size 保持一致
    conv_uplift_cvr_list_.resize(conv_treatment_coin_list_.size(), 0.0);
  }
  // 模型决策跳过 rct
  enable_model_decision_skip_rct_ = SPDM_enable_conv_model_decision_skip_rct(session_data->get_spdm_ctx());
  // 模型 pts 初始化
  incentive_uplift_cvr_pts_ = {
    PredictType::PredictType_incentive_deep_task_uplift_cvr_0,
    PredictType::PredictType_incentive_deep_task_uplift_cvr_1,
    PredictType::PredictType_incentive_deep_task_uplift_cvr_2,
    PredictType::PredictType_incentive_deep_task_uplift_cvr_3
  };
  // 领奖率模型 pts 初始化
  incentive_rewarded_ratio_pts_ = {
    PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
    PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
    PredictType::PredictType_incentive_deep_task_rewarded_ratio_3
  };
  if (enable_conv_d_i_base_use_logits_model_) {
    conv_treatment_coin_list_ = {0, 100, 300, 500, 700, 1000};
    incentive_uplift_cvr_pts_ = {
      PredictType::PredictType_incentive_deep_task_uplift_logits_0,
      PredictType::PredictType_incentive_deep_task_uplift_logits_1,
      PredictType::PredictType_incentive_deep_task_uplift_logits_2,
      PredictType::PredictType_incentive_deep_task_uplift_logits_3,
      PredictType::PredictType_incentive_deep_task_uplift_logits_4,
      PredictType::PredictType_incentive_deep_task_uplift_logits_5
    };
    incentive_rewarded_ratio_pts_ = {
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_1,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_2,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_3,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_4,
      PredictType::PredictType_incentive_deep_task_rewarded_ratio_5
    };
  }
  conv_rewarded_ratio_list_.resize(incentive_rewarded_ratio_pts_.size(), 0.0);
  // 一些 vector 初始化
  conv_uplift_cvr_list_.assign(incentive_uplift_cvr_pts_.size(), 0);
  // 这里先清空，使得后面的 push back 逻辑从 index = 0 的位置开始
  cvr_ratio_list_.clear();
  conv_adjust_cvr_list_.clear();
  // cvr 系数
  const std::vector<std::string>& cvr_ratio_str_list =
      absl::StrSplit(SPDM_conv_uplift_cvr_ratio_str(session_data->get_spdm_ctx()), ",");
  bool enable_conv_uplift_cvr_adjust =
        SPDM_enable_conv_uplift_cvr_adjust(session_data->get_spdm_ctx());
  for (auto& cvr_ratio_str : cvr_ratio_str_list) {
    double cvr_raito = 1;
    if (absl::SimpleAtod(cvr_ratio_str, &cvr_raito) && enable_conv_uplift_cvr_adjust) {
      cvr_ratio_list_.push_back(cvr_raito);
    } else {
      cvr_ratio_list_.push_back(1.0);
    }
  }
  if (cvr_ratio_list_.size() != conv_uplift_cvr_list_.size()) {
    // 兜底，防止 size 不一致后续运行出错
    cvr_ratio_list_.assign(conv_uplift_cvr_list_.size(), 1.0);
  }
  // 预估值上界
  uplift_cvr_upper_ = 1.0;
  // coin ratio
  conv_treatment_coin_ratio_ = SPDM_conv_treatment_coin_ratio(session_data->get_spdm_ctx());
  // clip ratio
  optimal_uplift_cvr_ratio_ = SPDM_conv_optimal_uplift_cvr_ratio(session_data->get_spdm_ctx());
  // 激活激励使用领奖率系数计算
  enable_conv_deep_incentive_use_rewarded_ratio_ =
    SPDM_enable_conv_deep_incentive_use_rewarded_ratio(session_data->get_spdm_ctx());
  // 记录领奖率用于后续毛利最大化
  enable_deep_incentive_gross_max_by_model_ =
    SPDM_enable_deep_incentive_gross_max_by_model(session_data->get_spdm_ctx());
  deep_incentive_gross_max_coef_ = SPDM_deep_incentive_gross_max_coef(session_data->get_spdm_ctx());
  // 金币打折系数
  conv_deep_coin_coef_ = SPDM_conv_deep_incentive_coin_coef(session_data->get_spdm_ctx());
  // 整体 cvr 校准系数
  const std::vector<std::string>& cvr_adjust_ratio_str_list =
      absl::StrSplit(SPDM_conv_deep_incentive_adjust_cvr_ratio_str(session_data->get_spdm_ctx()), ",");
  for (auto& cvr_adjust_ratio_str : cvr_adjust_ratio_str_list) {
    double cvr_adjust_raito = 1;
    if (absl::SimpleAtod(cvr_adjust_ratio_str, &cvr_adjust_raito)) {
      conv_adjust_cvr_list_.push_back(cvr_adjust_raito);
    } else {
      conv_adjust_cvr_list_.push_back(1.0);
    }
  }
  if (enable_conv_d_i_base_use_logits_model_) {
    conv_treatment_coin_ratio_ = SPDM_conv_d_i_coin_ratio_for_logits_model(session_data->get_spdm_ctx());
    optimal_uplift_cvr_ratio_ = SPDM_conv_d_i_clip_ratio_for_logits_model(session_data->get_spdm_ctx());
    const std::vector<std::string>& cvr_adjust_ratio_str_for_logits_model =
      absl::StrSplit(SPDM_conv_d_i_cvr_adjust_str_for_logits_model(session_data->get_spdm_ctx()), ",");
    for (auto& cvr_adjust_ratio_str : cvr_adjust_ratio_str_for_logits_model) {
      double cvr_adjust_raito = 1;
      if (absl::SimpleAtod(cvr_adjust_ratio_str, &cvr_adjust_raito)) {
        conv_adjust_cvr_list_.push_back(cvr_adjust_raito);
      } else {
        conv_adjust_cvr_list_.push_back(1.0);
      }
    }
    if (conv_adjust_cvr_list_.size() != conv_uplift_cvr_list_.size()) {
      conv_adjust_cvr_list_.assign(conv_uplift_cvr_list_.size(), 1);
    }
  }
  // uplift cvr 前置
  enable_conv_uplift_cvr_clip_ = !enable_conv_d_i_base_use_logits_model_;
  conv_uplift_cvr_clip_ratio_ = SPDM_conv_uplift_cvr_clip_ratio(session_data->get_spdm_ctx());
  // 记录领奖率
  enable_deep_incentive_record_rewarded_ratio_ =
    SPDM_enable_deep_incentive_record_rewarded_ratio(session_data->get_spdm_ctx());
  // 收集销售线索接入
  enable_conv_d_i_uplift_model_add_site_page_ =
    SPDM_enable_conv_d_i_uplift_model_add_site_page(session_data->get_spdm_ctx());
  // iaa 广告位判断
  is_iaa_pos_ = false;
  const auto& short_play_pos_ids = RankKconfUtil::shortPlayDeepIncentivePosIds();
  is_iaa_pos_ = short_play_pos_ids &&
    short_play_pos_ids->count(session_data->get_pos_manager_base().GetMediumPosId());
  // 记录观测数据
  conv_d_i_ob_data_record_ratio_ =
    SPDM_conv_d_i_ob_data_record_ratio(session_data->get_spdm_ctx());
  disable_conv_site_page_deep_incentive_ =
    SPDM_disable_conv_site_page_deep_incentive(session_data->get_spdm_ctx());
  disable_deep_incentive_logits_model_uplift_cvr_limitation_ =
    SPDM_disable_deep_incentive_logits_model_uplift_cvr_limitation(session_data->get_spdm_ctx());
}

bool ConvDeepCoinDecisionPlugin::PvAdmit(ContextData* session_data) {
  // 预留，暂时先使用线上频控策略
  // size 保持一致
  if (conv_treatment_coin_list_.size() == 0) {
    return false;
  }
  if (SPDM_enable_incentive_conv_uplift_model(session_data->get_spdm_ctx()) &&
      (conv_treatment_coin_list_.size() != incentive_uplift_cvr_pts_.size() ||
       cvr_ratio_list_.size() != conv_treatment_coin_list_.size())) {
    RANK_DOT_COUNT(session_data, 1, "deep_incentive_error", "conv_vector_size_unequal");
    return false;
  }
  // 风控判断
  if (SPDM_enable_risk_user_skip_deep_incentive(session_data->get_spdm_ctx())) {
    int reward_antispam_code = session_data->get_rank_request()->ad_request().front_antispam_code();
    base::Json json_extra = base::Json(base::StringToJson(
        session_data->get_rank_request()->ad_request().extra_request_ctx()));
    int live_antispam_code = 0;
    if (json_extra.IsObject()) {
      live_antispam_code = json_extra.GetInt("liveAntispamCode", 0);
    }
    if (reward_antispam_code > 0 || live_antispam_code == 1001) {
      return false;
    }
  }
  // ios 不出
  if (session_data->get_is_ios_platform()) {
    return false;
  }
  // 11.7.10 版本以下不出
  if (ks::ad_base::AppVersionCompare::Compare(
      session_data->get_rank_request()->ad_request().ad_user_info().platform_version(), "11.1.10") < 0) {
    return false;
  }
  const auto& incentive_dish_sub_page_id = RankKconfUtil::incentiveDishSubPageId();
  bool enable_deep_incentive_add_dish = SPDM_enable_deep_incentive_add_dish(session_data->get_spdm_ctx()) &&
    incentive_dish_sub_page_id && incentive_dish_sub_page_id->count(session_data->get_sub_page_id());
  // sub page id 准入
  auto conv_admit_sub_page_id_set = RankKconfUtil::convAdmitSubPageIdSet();
  if (!(is_global_rct_ && SPDM_enable_deep_incentive_rct_add_native(session_data->get_spdm_ctx())) &&
      conv_admit_sub_page_id_set &&
      !conv_admit_sub_page_id_set->count(session_data->get_sub_page_id()) &&
      !(SPDM_enable_conv_d_i_iaa_use_uplift_model(session_data->get_spdm_ctx()) &&
      is_iaa_pos_) && !(enable_deep_incentive_add_dish &&
      ks::ad_base::AppVersionCompare::Compare(session_data->get_rank_request()->
      ad_request().ad_user_info().platform_version(), "13.4.10") >= 0)) {
    return false;
  }
  // 当任务次数频控开启或 product id 频控开启时，调用该函数
  std::string reward_type = "active";
  int task_complete_num = 0;
  if (SPDM_incentive_conv_max_mission_num(session_data->get_spdm_ctx()) > 0) {
    task_complete_num =
      GetIncentiveStyleTaskCompleteCount(session_data, &reward_type, &conv_product_ids_);
  }
  // 最大任务次数限制
  if (SPDM_incentive_conv_max_mission_num(session_data->get_spdm_ctx()) > 0 &&
      task_complete_num >= SPDM_incentive_conv_max_mission_num(session_data->get_spdm_ctx())) {
    return false;
  }
  // 下发时间间隔限制
  if (SPDM_incentive_conv_delivery_time_gap(session_data->get_spdm_ctx()) > 0 &&
      GetIncentiveStyleDeliverTimeGap(session_data, &reward_type) <
      SPDM_incentive_conv_delivery_time_gap(session_data->get_spdm_ctx())) {
    return false;
  }

  return true;
}

bool ConvDeepCoinDecisionPlugin::AdAdmit(ContextData* session_data, AdCommon* p_ad) {
  // 判断营销目标和优化目标
  if (!((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
      (enable_conv_d_i_uplift_model_add_site_page_ &&
      p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE &&
      !disable_conv_site_page_deep_incentive_)) &&
      p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION)) {
    return false;
  }
  // 物料类型限制
  if (p_ad->get_item_type() != kuaishou::ad::AdEnum::ITEM_PHOTO) {
    return false;
  }
  // producet id 频控
  int64_t product_id = conv_(p_ad->get_product_name());
  // 已安装则不出
  if (JudgeIsInstalled(p_ad, session_data)) {
    return false;
  }

  return true;
}

void ConvDeepCoinDecisionPlugin::GetPredictUpliftCvr(ContextData* session_data, AdCommon* p_ad) {
  conv_uplift_cvr_list_.assign(conv_treatment_coin_list_.size(), 0.0);
  double base_cvr = 0;
  double conv_uplift_cvr = 0;
  double predict_cvr = 0;
  double max_uplift_cvr = 0;
  double sum_uplift_cvr = 0;
  double sum_rewarded_ratio = 0;
  double uplift_cvr_upper = p_ad->get_unify_cvr_info().value * conv_uplift_cvr_clip_ratio_;
  double rank_cvr = std::clamp(p_ad->get_unify_cvr_info().value, 1e-18, 0.9999996941);
  double rank_logits = std::log(rank_cvr / (1 - rank_cvr + 1e-18));
  double total_logits = rank_logits;
  for (int i = 0; i < conv_uplift_cvr_list_.size(); i++) {
    if (i == 0) {
      base_cvr = p_ad->get_predict_score(incentive_uplift_cvr_pts_[i]) * cvr_ratio_list_[i];
      if (enable_conv_d_i_base_use_logits_model_) {
        base_cvr = rank_cvr;
      }
    }
    // 记录校准前预估的绝对 cvr
    predict_cvr = p_ad->get_predict_score(incentive_uplift_cvr_pts_[i]);
    if (enable_conv_d_i_base_use_logits_model_) {
      total_logits += predict_cvr;
      total_logits = std::min(std::max(total_logits, -15.0), 15.0);
      predict_cvr = std::exp(total_logits) / (1 + std::exp(total_logits));
    }
    p_ad->mutable_incentive_deep_uplift_cvr()->insert({conv_treatment_coin_list_[i], predict_cvr});
    predict_cvr *= cvr_ratio_list_[i];
    // uplift cvr
    conv_uplift_cvr = predict_cvr - base_cvr;
    // clip 前置
    if (enable_conv_uplift_cvr_clip_) {
      conv_uplift_cvr = std::min(conv_uplift_cvr, uplift_cvr_upper);
    }
    // 记录 max && sum 用于打点
    max_uplift_cvr = std::max(max_uplift_cvr, conv_uplift_cvr);
    sum_uplift_cvr += conv_uplift_cvr;
    // 预估值上下界限制
    if (!disable_deep_incentive_logits_model_uplift_cvr_limitation_) {
      conv_uplift_cvr = std::max(std::min(conv_uplift_cvr, uplift_cvr_upper_), 0.0);
    }
    conv_uplift_cvr_list_[i] = conv_uplift_cvr;
  }
  for (int i = 0; i < conv_rewarded_ratio_list_.size(); i++) {
    conv_rewarded_ratio_list_[i] = p_ad->get_predict_score(incentive_rewarded_ratio_pts_[i]);
    if (i < conv_treatment_coin_list_.size() - 1) {
      p_ad->mutable_incentive_deep_reward_ratio()->
        insert({conv_treatment_coin_list_[i+1], conv_rewarded_ratio_list_[i]});
    }
    sum_rewarded_ratio += conv_rewarded_ratio_list_[i];
  }
  // uplift model 相关打点监控
  double mean_uplift_cvr = sum_uplift_cvr / conv_uplift_cvr_list_.size();
  double mean_rewarded_ratio = sum_rewarded_ratio / conv_rewarded_ratio_list_.size();
  RANK_DOT_STATS(session_data, max_uplift_cvr * 1e6, absl::StrCat(prefix_, "max_uplift_cvr"), exp_tag_);
  RANK_DOT_STATS(session_data, mean_uplift_cvr * 1e6, absl::StrCat(prefix_, "mean_uplift_cvr"), exp_tag_);
  RANK_DOT_STATS(session_data, mean_rewarded_ratio * 1e6, absl::StrCat(prefix_, "mean_rewarded_ratio"), exp_tag_);  // NOLINT
}

void ConvDeepCoinDecisionPlugin::DeepCoinDecision(ContextData* session_data, AdCommon* p_ad) {
  double profit = 0;
  double optimal_profit = 0;
  int index = 0;
  double optimal_uplift_cvr = 0.0;
  double coin_ratio = conv_treatment_coin_ratio_;
  double coin_coef = conv_deep_coin_coef_;
  int64_t cpa_bid = p_ad->get_cpa_bid() > 0 ? p_ad->get_cpa_bid() : p_ad->get_auto_cpa_bid();
  if (p_ad->get_auto_cpa_bid() > 0) {
    cpa_bid = p_ad->get_auto_cpa_bid();
  }
  // 开关控制是否使用精排预估 cvr 作为 base cvr
  double base_cvr = p_ad->get_predict_score(incentive_uplift_cvr_pts_[0]) * cvr_ratio_list_[0];
  if (enable_conv_d_i_base_use_logits_model_) {
    base_cvr = p_ad->get_unify_cvr_info().value;
  }
  // 金币决策
  for (int i = 0; i < conv_uplift_cvr_list_.size(); i++) {
    bool is_roi_constrain_satisfied = true;
    // 金币单位要除 10 换算成厘
    double unify_treatment_coin = conv_treatment_coin_list_[i] / 10.0;
    double uplift_cvr = conv_uplift_cvr_list_[i];
    profit = (cpa_bid - coin_ratio * unify_treatment_coin) *
                (base_cvr + uplift_cvr) * p_ad->get_unify_ctr_info().value;
    double unify_reward_ratio = base_cvr + uplift_cvr;
    // 使用领奖率计算金币成本
    if (enable_conv_deep_incentive_use_rewarded_ratio_ && i >= 1) {
      double rewarded_ratio = conv_rewarded_ratio_list_[i-1];
      profit = (cpa_bid * (base_cvr + uplift_cvr) -
                rewarded_ratio * coin_ratio * unify_treatment_coin) *
                p_ad->get_unify_ctr_info().value;
      unify_reward_ratio = rewarded_ratio;
    }
    // 本次决策 roi
    double roi_this_decision = (cpa_bid * (base_cvr + uplift_cvr)) /
                                (unify_reward_ratio * unify_treatment_coin + 1e-10);
    double marginal_roi_this_decision = (cpa_bid * uplift_cvr) /
                                        (unify_reward_ratio * unify_treatment_coin + 1e-10);
    if (i == 0) {
      p_ad->set_pec_coupon_origin_cpm(profit);
    }
    if (profit > optimal_profit && is_roi_constrain_satisfied) {
      optimal_profit = profit;
      index = i;
      optimal_uplift_cvr = uplift_cvr;
    }
  }
  if (optimal_uplift_cvr_ratio_ > 0 && !enable_conv_uplift_cvr_clip_) {
    optimal_uplift_cvr =
      std::min(optimal_uplift_cvr, p_ad->get_unify_cvr_info().value * optimal_uplift_cvr_ratio_);
  }
  p_ad->set_deep_rewarded_coin(floor(conv_treatment_coin_list_[index] * coin_coef));
  p_ad->set_pec_coupon_uplift_profit(optimal_profit);
  p_ad->set_pec_uplift_cvr(optimal_uplift_cvr);
  // 记录观测数据
  double ob_data_random_number = ad_base::AdRandom::GetDouble();
  if (conv_d_i_ob_data_record_ratio_ > 0 && ob_data_random_number < conv_d_i_ob_data_record_ratio_) {
    p_ad->set_incentive_deep_coin_rct_tag(10);
  }
  if (index >= 0 && index < conv_adjust_cvr_list_.size()) {
    p_ad->set_deep_incentive_cvr_adjust_ratio(conv_adjust_cvr_list_[index]);
  }
  if ((enable_deep_incentive_gross_max_by_model_ || enable_deep_incentive_record_rewarded_ratio_) &&
      index >= 1) {
    p_ad->set_deep_rewarded_rate(conv_rewarded_ratio_list_[index-1] * deep_incentive_gross_max_coef_);
  }
  if (p_ad->get_deep_rewarded_coin() > 0 && !p_ad->get_reward_styles().count(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP))) {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP),
      kuaishou::ad::RewardStyleType::ACTIVE_APP});
  }
  // 打点监控
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "req_cnt"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_deep_rewarded_coin(), absl::StrCat(prefix_, "active_deep_coin"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, optimal_uplift_cvr * 1e6, absl::StrCat(prefix_, "optimal_uplift_cvr"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, optimal_profit, absl::StrCat(prefix_, "optimal_profit"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_pec_coupon_origin_cpm(), absl::StrCat(prefix_, "origin_cpm"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "treatment_", index), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, base_cvr * 1e6, absl::StrCat(prefix_, "base_cvr"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_unify_cvr_info().value * 1e6, absl::StrCat(prefix_, "rank_cvr"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
}


void ConvDeepCoinDecisionPlugin::RandomDeepCoinStrategy(ContextData* session_data, AdCommon* p_ad) {
  int index = ad_base::AdRandom::GetInt(0, conv_global_treatment_coin_list_.size() - 1);
  if (is_global_rct_ && index >= 0 && index < conv_global_treatment_coin_list_.size()) {
    p_ad->set_deep_rewarded_coin(conv_global_treatment_coin_list_[index]);
    p_ad->set_incentive_deep_coin_rct_tag(2);
    p_ad->set_pec_uplift_cvr(0.0);
    RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "global_rct_treatment_", index), exp_tag_);
  }
  bool  style_admit = is_global_rct_;
  // 若随机到的深度金币为 0 或本次 pv 没有命中 rct 则移除深度样式
  if ((p_ad->get_deep_rewarded_coin() <= 0 || !style_admit) && p_ad->get_reward_styles().count(
  kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP))) {
    p_ad->mutable_reward_styles()->erase(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP));
  }
  // 若本次 pv 命中 rct 且随机到的深度金币不为 0 但没有样式，则加上样式
  if (p_ad->get_deep_rewarded_coin() > 0 && style_admit && !p_ad->get_reward_styles().count(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP))) {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::ACTIVE_APP),
      kuaishou::ad::RewardStyleType::ACTIVE_APP});
  }
}

/* -----直播订单激励----- */
const char* LiveOrderPayDeepCoinDecisionPlugin::Name() { return "LiveOrderPayDeepCoinDecisionPlugin"; }

void LiveOrderPayDeepCoinDecisionPlugin::Clear() {
  live_order_discount_list_.clear();
  live_order_coin_upper_list_.clear();
  live_order_uplift_ctcvr_list_.clear();
  live_order_global_treatment_discount_list_.clear();
  live_order_global_treatment_coin_upper_list_.clear();
  live_order_cvr_adjust_ratio_list_.clear();
}

bool LiveOrderPayDeepCoinDecisionPlugin::IsRun(const ContextData* session_data, const Params* params,
                                          AdRankUnifyScene pos, const AdList* adlist) {
  // 只对激励流量生效
  if (!(session_data->get_is_rewarded() || session_data->get_is_inspire_live_request()) ||
      adlist->Size() <= 0) {
    return false;
  }
  if (SPDM_enable_order_deep_incentive_total_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  if (SPDM_enable_incentive_coin_joint_decision(session_data->get_spdm_ctx())) {
    return false;
  }
  if (SPDM_enable_live_order_d_i_holdout_for_qcpx(session_data->get_spdm_ctx())) {
    return false;
  }
  // 直播订单激励 holdout 实验
  if (SPDM_enable_live_order_pay_deep_incentive_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  // 直播订单激励 holdout 实验 v2
  if (SPDM_enable_live_order_pay_deep_incentive_holdout_v2(session_data->get_spdm_ctx())) {
    return false;
  }
  // 直播订单激励 rct 开关
  if (SPDM_enable_live_order_pay_deep_incentive_rct(session_data->get_spdm_ctx())) {
    return true;
  }

  if (SPDM_enable_live_order_pay_uplift_decision(session_data->get_spdm_ctx())) {
    return true;
  }

  return false;
}

StraRetCode LiveOrderPayDeepCoinDecisionPlugin::Process(ContextData* session_data, Params* params,
                                                  AdRankUnifyScene pos, AdList* adlist) {
  Initialize(session_data);

  if (!PvAdmit(session_data)) {
    return StraRetCode::SUCC;
  }

  for (auto* p_ad : adlist->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    if (!AdAdmit(session_data, p_ad)) {
      continue;
    }
    // rct 实验
    if (is_rct_) {
      RandomDeepCoinStrategy(session_data, p_ad);
    }
    // uplift 决策
    if (!is_rct_ && SPDM_enable_live_order_pay_uplift_decision(session_data->get_spdm_ctx())) {
      GetPredictUpliftCvr(session_data, p_ad);
      DeepCoinDecision(session_data, p_ad);
    }
  }
  return StraRetCode::SUCC;
}

void LiveOrderPayDeepCoinDecisionPlugin::Initialize(ContextData* session_data) {
  // treatment 初始化，key 为折扣 value 为金币上限
  auto* live_order_treatment_map = RankKconfUtil::liveOrderDeepIncentiveTreatmentMap().get();
  if (live_order_treatment_map) {
    live_order_treatment_map_.assign(live_order_treatment_map->begin(), live_order_treatment_map->end());
  }
  if (SPDM_enable_live_order_vector_clear_before_push_back(session_data->get_spdm_ctx())) {
    live_order_discount_list_.clear();
    live_order_coin_upper_list_.clear();
  }
  // discount list 和 upper list 初始化
  for (const auto& iter : live_order_treatment_map_) {
    std::string discount_str = iter.first;
    double discount = 0;
    int coin_upper = iter.second;
    if (absl::SimpleAtod(discount_str, &discount)) {
      live_order_discount_list_.push_back(discount);
      live_order_coin_upper_list_.push_back(coin_upper);
    }
  }
  // 排序，确保元素一一对应
  std::sort(live_order_discount_list_.begin(), live_order_discount_list_.end());
  std::sort(live_order_coin_upper_list_.begin(), live_order_coin_upper_list_.end());
  // pts 初始化
  live_order_uplift_ctcvr_pts_ = {
    PredictType::PredictType_incentive_live_order_ctcvr_uplift_0,
    PredictType::PredictType_incentive_live_order_ctcvr_uplift_1,
    PredictType::PredictType_incentive_live_order_ctcvr_uplift_2
  };
  // coinratio 初始化
  live_order_treatment_coin_ratio_ =
    SPDM_live_order_treatment_coin_ratio(session_data->get_spdm_ctx());
  // clip raito 初始化
  live_order_optimal_uplift_cvr_ratio_ =
    SPDM_live_order_optimal_uplift_cvr_ratio(session_data->get_spdm_ctx());
  // rct tag 初始化
  is_rct_ = false;
  double random_number = ad_base::AdRandom::GetDouble();
  if (random_number < SPDM_live_order_fix_deep_style_ratio(session_data->get_spdm_ctx()) &&
      SPDM_enable_live_order_rct_fix_coin(session_data->get_spdm_ctx())) {
    is_rct_ = true;
  }
  // 使用新 treatment
  enable_live_order_d_i_use_new_treatment_ =
    SPDM_enable_live_order_d_i_use_new_treatment(session_data->get_spdm_ctx());
  const auto& deep_incentive_rct_config = RankKconfUtil::deepIncentiveRctConfig();
  if (enable_live_order_d_i_use_new_treatment_ && deep_incentive_rct_config) {
    const auto& live_order_treatment_discount_list =
      deep_incentive_rct_config->data().rct_config_v1().live_order_treatment_discount_list();
    const auto& live_order_treatment_coin_upper_list =
      deep_incentive_rct_config->data().rct_config_v1().live_order_treatment_coin_upper_list();
    live_order_global_treatment_discount_list_.assign(live_order_treatment_discount_list.begin(),
      live_order_treatment_discount_list.end());
    live_order_global_treatment_coin_upper_list_.assign(live_order_treatment_coin_upper_list.begin(),
      live_order_treatment_coin_upper_list.end());
    if (live_order_global_treatment_discount_list_.empty() ||
        live_order_global_treatment_coin_upper_list_.empty() ||
        live_order_global_treatment_discount_list_.size() !=
        live_order_global_treatment_coin_upper_list_.size()) {
      is_rct_ = false;
    }
    std::sort(live_order_global_treatment_discount_list_.begin(),
      live_order_global_treatment_discount_list_.end());
    std::sort(live_order_global_treatment_coin_upper_list_.begin(),
      live_order_global_treatment_coin_upper_list_.end());
  }
  if (SPDM_enable_deep_incentive_rct_migrate(session_data->get_spdm_ctx())) {
    is_rct_ = false;
  }
  // 打点前缀初始化
  prefix_ = "ad_rank.live_order_uplift_model.";
  // exp tag 初始化
  exp_tag_ = SPDM_live_order_uplift_model_exp_tag(session_data->get_spdm_ctx());
  // cvr 校准系数
  const std::vector<std::string>& cvr_ratio_str_list =
    absl::StrSplit(SPDM_live_order_d_i_cvr_adjust_str(session_data->get_spdm_ctx()), ",");
  for (auto& cvr_ratio_str : cvr_ratio_str_list) {
    double cvr_ratio = 1;
    if (absl::SimpleAtod(cvr_ratio_str, &cvr_ratio)) {
      live_order_cvr_adjust_ratio_list_.push_back(cvr_ratio);
    } else {
      live_order_cvr_adjust_ratio_list_.push_back(1.0);
    }
  }
  if (live_order_cvr_adjust_ratio_list_.size() != live_order_discount_list_.size()) {
    // 兜底，防止 size 不一致后续运行出错
    live_order_cvr_adjust_ratio_list_.assign(live_order_discount_list_.size(), 1.0);
  }
  // 金币上界与折扣系数
  live_order_d_i_discount_coef_ = SPDM_live_order_d_i_discount_coef(session_data->get_spdm_ctx());
  live_order_d_i_coin_upper_coef_ = SPDM_live_order_d_i_coin_upper_coef(session_data->get_spdm_ctx());
  // 记录观测数据
  live_order_d_i_ob_data_record_ratio_ =
    SPDM_live_order_d_i_ob_data_record_ratio(session_data->get_spdm_ctx());
  // profit 阈值
  enable_order_d_i_use_profit_threshold_ =
    SPDM_enable_order_d_i_use_profit_threshold(session_data->get_spdm_ctx());
  order_d_i_use_profit_threshold_ =
    SPDM_order_d_i_use_profit_threshold(session_data->get_spdm_ctx());
  const auto& specific_industry_ids = RankKconfUtil::orderDeepIncentiveSpecificIndustryIds();
  if (specific_industry_ids) {
    specific_industry_ids_.clear();
    specific_industry_ids_.assign(specific_industry_ids->begin(), specific_industry_ids->end());
  }
}

bool LiveOrderPayDeepCoinDecisionPlugin::PvAdmit(ContextData* session_data) {
  auto live_order_pay_admit_sub_page_ids = RankKconfUtil::liveOrderPayAdmitSubPageIds();

  // 生效页面准入
  if (live_order_pay_admit_sub_page_ids &&
      !live_order_pay_admit_sub_page_ids->count(session_data->get_sub_page_id())) {
    return false;
  }
  // 风控判断
  if (SPDM_enable_risk_user_skip_deep_incentive(session_data->get_spdm_ctx())) {
    int reward_antispam_code = session_data->get_rank_request()->ad_request().front_antispam_code();
    base::Json json_extra = base::Json(base::StringToJson(
        session_data->get_rank_request()->ad_request().extra_request_ctx()));
    int live_antispam_code = 0;
    if (json_extra.IsObject()) {
      live_antispam_code = json_extra.GetInt("liveAntispamCode", 0);
    }
    if (reward_antispam_code > 0 || live_antispam_code == 1001) {
      return false;
    }
  }
  // ios 不出
  if (session_data->get_is_ios_platform()) {
    return false;
  }
  // 版控
  if (ks::ad_base::AppVersionCompare::Compare(
      session_data->get_rank_request()->ad_request().ad_user_info().platform_version(), "13.3.30") < 0) {
    return false;
  }

  return true;
}

bool LiveOrderPayDeepCoinDecisionPlugin::AdAdmit(ContextData* session_data, AdCommon* p_ad) {
  if (!(p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED)) {
    return false;
  }

  return true;
}

void LiveOrderPayDeepCoinDecisionPlugin::GetPredictUpliftCvr(ContextData* session_data, AdCommon* p_ad) {
  live_order_uplift_ctcvr_list_.clear();
  double base_cvr = 0;
  double live_order_uplift_cvr = 0;
  double predict_cvr = 0;
  for (int i = 0; i < live_order_uplift_ctcvr_pts_.size(); i++) {
    if (i == 0) {
      base_cvr = p_ad->get_predict_score(live_order_uplift_ctcvr_pts_[i]);
    }
    predict_cvr = p_ad->get_predict_score(live_order_uplift_ctcvr_pts_[i]);
    if (i >= 0 && i < live_order_coin_upper_list_.size()) {
      p_ad->mutable_incentive_deep_uplift_cvr()->insert({live_order_coin_upper_list_[i], predict_cvr});
    }
    live_order_uplift_cvr = predict_cvr - base_cvr;
    live_order_uplift_ctcvr_list_.push_back(live_order_uplift_cvr);
  }
}

void LiveOrderPayDeepCoinDecisionPlugin::DeepCoinDecision(ContextData* session_data, AdCommon* p_ad) {
  if (!(live_order_uplift_ctcvr_pts_.size() == live_order_discount_list_.size() &&
      live_order_discount_list_.size() == live_order_coin_upper_list_.size() &&
      live_order_coin_upper_list_.size() == live_order_uplift_ctcvr_list_.size())) {
    RANK_DOT_COUNT(session_data, 1, "deep_incentive_error", "live_order_vector_size_unequal");
    return;
  }
  double profit = 0;
  double optimal_profit = 0;
  int index = 0;
  double optimal_uplift_cvr = 0.0;
  double ecpm_ratio = 0;
  int64_t auto_cpa_bid =  p_ad->get_auto_cpa_bid() > 0 ? p_ad->get_auto_cpa_bid() : p_ad->get_cpa_bid();
  double base_cvr = p_ad->get_predict_score(live_order_uplift_ctcvr_pts_[0]);
  double profit_threshold = 0;
  if (enable_order_d_i_use_profit_threshold_ && !specific_industry_ids_.empty() &&
      std::find(specific_industry_ids_.begin(), specific_industry_ids_.end(),
      p_ad->get_first_industry_id_v5()) != specific_industry_ids_.end()) {
    profit_threshold = order_d_i_use_profit_threshold_;
  }
  if (base_cvr == 0) {
    return;
  }
  // 金币决策
  for (int i = 0; i < live_order_uplift_ctcvr_list_.size(); i++) {
    double unify_treatment_coin = live_order_coin_upper_list_[i] / 10.0;
    double uplift_cvr = live_order_uplift_ctcvr_list_[i];
    profit = (auto_cpa_bid - live_order_treatment_coin_ratio_ * unify_treatment_coin) *
      (base_cvr + uplift_cvr);
    if (i == 0) {
      p_ad->set_pec_coupon_origin_cpm(profit);
    }
    if (profit - optimal_profit > profit_threshold) {
      optimal_profit = profit;
      index = i;
      optimal_uplift_cvr = uplift_cvr;
    }
  }
  p_ad->set_order_deep_incentive_type(kuaishou::ad::OrderDeepIncentiveType::ORDER_DISCOUNT_COIN);
  p_ad->set_coin_discount_ratio(live_order_discount_list_[index] * live_order_d_i_discount_coef_);
  p_ad->set_deep_rewarded_coin(floor(live_order_coin_upper_list_[index] * live_order_d_i_coin_upper_coef_));
  p_ad->set_pec_coupon_uplift_profit(optimal_profit);
  // 记录观测数据
  double ob_data_random_number = ad_base::AdRandom::GetDouble();
  if (live_order_d_i_ob_data_record_ratio_ > 0 &&
      ob_data_random_number < live_order_d_i_ob_data_record_ratio_) {
    p_ad->set_incentive_deep_coin_rct_tag(10);
  }
  ecpm_ratio = (optimal_uplift_cvr + base_cvr) / base_cvr;
  ecpm_ratio = std::min(ecpm_ratio, live_order_optimal_uplift_cvr_ratio_ + 1);
  ecpm_ratio *= SafeGetDoubleVectorValue(live_order_cvr_adjust_ratio_list_, index,
    1.0, session_data, "live_order_cvr_adjust_ratio_list_");
  ecpm_ratio = std::max(std::min(ecpm_ratio, 1.5), 0.5);
  p_ad->set_incentive_uplift_ecpm_ratio(ecpm_ratio);
  if (p_ad->get_deep_rewarded_coin() > 0 && !p_ad->get_reward_styles().count(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER),
      kuaishou::ad::RewardStyleType::PLAY_AND_ORDER});
  }
  // 打点监控
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "req_cnt"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_deep_rewarded_coin(), absl::StrCat(prefix_, "order_deep_coin"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, optimal_uplift_cvr * 1e6, absl::StrCat(prefix_, "optimal_uplift_cvr"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "treatment_", index), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, base_cvr * 1e6, absl::StrCat(prefix_, "base_cvr"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, ecpm_ratio * 1e6, absl::StrCat(prefix_, "ecpm_ratio"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
}

void LiveOrderPayDeepCoinDecisionPlugin::RandomDeepCoinStrategy(ContextData* session_data, AdCommon* p_ad) {
  int index = ad_base::AdRandom::GetInt(0, live_order_treatment_map_.size() - 1);
  if (is_rct_ && index < live_order_treatment_map_.size() && index >= 0) {  // 防止数组越界
    auto& kv = live_order_treatment_map_[index];
    double coin_discount_ratio = 0;
    int deep_coin_upper = 0;
    if (absl::SimpleAtod(kv.first, &coin_discount_ratio)) {
      deep_coin_upper = kv.second;
    }
    p_ad->set_order_deep_incentive_type(kuaishou::ad::OrderDeepIncentiveType::ORDER_DISCOUNT_COIN);
    p_ad->set_coin_discount_ratio(coin_discount_ratio);
    p_ad->set_deep_rewarded_coin(deep_coin_upper);  // 对于折扣金币，此处填充值为金币上界
    p_ad->set_incentive_deep_coin_rct_tag(1);
    // rct 实验不影响排序和计费
    p_ad->set_pec_uplift_cvr(0.0);
    RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "rct_treatment_", index), exp_tag_);
  }
  index = ad_base::AdRandom::GetInt(0, live_order_global_treatment_discount_list_.size() - 1);
  if (enable_live_order_d_i_use_new_treatment_ && is_rct_ && index >= 0 &&
      index < live_order_global_treatment_discount_list_.size()) {
    p_ad->set_order_deep_incentive_type(kuaishou::ad::OrderDeepIncentiveType::ORDER_DISCOUNT_COIN);
    p_ad->set_coin_discount_ratio(live_order_global_treatment_discount_list_[index]);
    p_ad->set_deep_rewarded_coin(live_order_global_treatment_coin_upper_list_[index]);
    p_ad->set_incentive_deep_coin_rct_tag(2);
    p_ad->set_pec_uplift_cvr(0.0);
    p_ad->set_incentive_uplift_ecpm_ratio(1.0);
    RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "global_rct_treatment_", index), exp_tag_);
  }
  // 若随机到的深度金币为 0 或本次 pv 没有命中 rct 则移除深度样式
  if ((p_ad->get_deep_rewarded_coin() <= 0 || !is_rct_) && p_ad->get_reward_styles().count(
  kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->erase(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER));
  }
  // 若本次 pv 命中 rct 且随机到的深度金币不为 0 但没有样式，则加上样式
  if (p_ad->get_deep_rewarded_coin() > 0 && is_rct_ && !p_ad->get_reward_styles().count(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER),
      kuaishou::ad::RewardStyleType::PLAY_AND_ORDER});
  }
}


/* -----直播 ROAS 激励----- */
const char* LiveRoasDeepCoinDecisionPlugin::Name() { return "LiveRoasDeepCoinDecisionPlugin"; }

void LiveRoasDeepCoinDecisionPlugin::Clear() {
  live_roas_discount_list_.clear();
  live_roas_coin_upper_list_.clear();
  live_roas_uplift_ctcvr_list_.clear();
  live_roas_global_treatment_discount_list_.clear();
  live_roas_global_treatment_coin_upper_list_.clear();
  live_roas_cvr_adjust_ratio_list_.clear();
}

bool LiveRoasDeepCoinDecisionPlugin::IsRun(const ContextData* session_data, const Params* params,
                                          AdRankUnifyScene pos, const AdList* adlist) {
  // 只对激励流量生效
  if (!(session_data->get_is_rewarded() || session_data->get_is_inspire_live_request()) ||
      adlist->Size() <= 0) {
    return false;
  }
  if (SPDM_enable_order_deep_incentive_total_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  if (SPDM_enable_incentive_coin_joint_decision(session_data->get_spdm_ctx())) {
    return false;
  }
  if (SPDM_enable_live_roas_d_i_holdout_for_qcpx(session_data->get_spdm_ctx())) {
    return false;
  }
  // 直播 ROAS 激励 holdout 实验
  if (SPDM_enable_live_roas_deep_incentive_holdout(session_data->get_spdm_ctx())) {
    return false;
  }
  // 直播 ROAS 激励 holdout 实验 v2
  if (SPDM_enable_live_roas_deep_incentive_holdout_v2(session_data->get_spdm_ctx())) {
    return false;
  }
  // 直播 ROAS 激励 rct 开关
  if (SPDM_enable_live_roas_deep_incentive_rct(session_data->get_spdm_ctx())) {
    return true;
  }

  if (SPDM_enable_live_roas_uplift_decision(session_data->get_spdm_ctx())) {
      return true;
  }

  return false;
}

StraRetCode LiveRoasDeepCoinDecisionPlugin::Process(ContextData* session_data, Params* params,
                                                  AdRankUnifyScene pos, AdList* adlist) {
  Initialize(session_data);

  if (!PvAdmit(session_data)) {
    return StraRetCode::SUCC;
  }

  for (auto* p_ad : adlist->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    if (!AdAdmit(session_data, p_ad)) {
      continue;
    }
    // rct 实验
    if (is_rct_) {
      RandomDeepCoinStrategy(session_data, p_ad);
    }
    // uplift 决策
    if (!is_rct_ && SPDM_enable_live_roas_uplift_decision(session_data->get_spdm_ctx())) {
      GetPredictUpliftCvr(session_data, p_ad);
      DeepCoinDecision(session_data, p_ad);
    }
  }
  return StraRetCode::SUCC;
}

void LiveRoasDeepCoinDecisionPlugin::Initialize(ContextData* session_data) {
  // treatment 初始化，key 为折扣 value 为金币上限
  auto* live_roas_treatment_map = RankKconfUtil::liveRoasDeepIncentiveTreatmentMap().get();
  if (live_roas_treatment_map) {
    live_roas_treatment_map_.assign(live_roas_treatment_map->begin(), live_roas_treatment_map->end());
  }
  if (SPDM_enable_live_order_vector_clear_before_push_back(session_data->get_spdm_ctx())) {
    live_roas_discount_list_.clear();
    live_roas_coin_upper_list_.clear();
  }
  // discount list 和 upper list 初始化
  for (const auto& iter : live_roas_treatment_map_) {
    std::string discount_str = iter.first;
    double discount = 0;
    int coin_upper = iter.second;
    if (absl::SimpleAtod(discount_str, &discount)) {
      live_roas_discount_list_.push_back(discount);
      live_roas_coin_upper_list_.push_back(coin_upper);
    }
  }
  // 排序，确保元素一一对应
  std::sort(live_roas_discount_list_.begin(), live_roas_discount_list_.end());
  std::sort(live_roas_coin_upper_list_.begin(), live_roas_coin_upper_list_.end());
  // pts 初始化
  live_roas_uplift_ctcvr_pts_ = {
    PredictType::PredictType_incentive_live_order_ctcvr_uplift_0,
    PredictType::PredictType_incentive_live_order_ctcvr_uplift_1,
    PredictType::PredictType_incentive_live_order_ctcvr_uplift_2
  };
  // coinratio 初始化
  live_roas_treatment_coin_ratio_ =
    SPDM_live_roas_treatment_coin_ratio(session_data->get_spdm_ctx());
  // clip raito 初始化
  live_roas_optimal_uplift_cvr_ratio_ =
    SPDM_live_roas_optimal_uplift_cvr_ratio(session_data->get_spdm_ctx());
  // rct tag 初始化
  is_rct_ = false;
  double random_number = ad_base::AdRandom::GetDouble();
  if (random_number < SPDM_live_roas_fix_deep_style_ratio(session_data->get_spdm_ctx()) &&
      SPDM_enable_live_roas_rct_fix_coin(session_data->get_spdm_ctx())) {
    is_rct_ = true;
  }
  // 使用新 treatment
  enable_live_roas_d_i_use_new_treatment_ =
    SPDM_enable_live_roas_d_i_use_new_treatment(session_data->get_spdm_ctx());
  const auto& deep_incentive_rct_config = RankKconfUtil::deepIncentiveRctConfig();
  if (enable_live_roas_d_i_use_new_treatment_ && deep_incentive_rct_config) {
    const auto& live_roas_treatment_discount_list =
      deep_incentive_rct_config->data().rct_config_v1().live_roas_treatment_discount_list();
    const auto& live_roas_treatment_coin_upper_list =
      deep_incentive_rct_config->data().rct_config_v1().live_roas_treatment_coin_upper_list();
    live_roas_global_treatment_discount_list_.assign(live_roas_treatment_discount_list.begin(),
      live_roas_treatment_discount_list.end());
    live_roas_global_treatment_coin_upper_list_.assign(live_roas_treatment_coin_upper_list.begin(),
      live_roas_treatment_coin_upper_list.end());
    if (live_roas_global_treatment_discount_list_.empty() ||
        live_roas_global_treatment_coin_upper_list_.empty() ||
        live_roas_global_treatment_discount_list_.size() !=
        live_roas_global_treatment_coin_upper_list_.size()) {
      is_rct_ = false;
    }
    std::sort(live_roas_global_treatment_discount_list_.begin(),
      live_roas_global_treatment_discount_list_.end());
    std::sort(live_roas_global_treatment_coin_upper_list_.begin(),
      live_roas_global_treatment_coin_upper_list_.end());
  }
  if (SPDM_enable_deep_incentive_rct_migrate(session_data->get_spdm_ctx())) {
    is_rct_ = false;
  }
  // 打点前缀初始化
  prefix_ = "ad_rank.live_roas_uplift_model.";
  // exp tag 初始化
  exp_tag_ = SPDM_live_roas_uplift_model_exp_tag(session_data->get_spdm_ctx());
  const std::vector<std::string>& cvr_ratio_str_list =
    absl::StrSplit(SPDM_live_roas_d_i_cvr_adjust_str(session_data->get_spdm_ctx()), ",");
  for (auto& cvr_ratio_str : cvr_ratio_str_list) {
    double cvr_ratio = 1;
    if (absl::SimpleAtod(cvr_ratio_str, &cvr_ratio)) {
      live_roas_cvr_adjust_ratio_list_.push_back(cvr_ratio);
    } else {
      live_roas_cvr_adjust_ratio_list_.push_back(1.0);
    }
  }
  if (live_roas_cvr_adjust_ratio_list_.size() != live_roas_discount_list_.size()) {
    // 兜底，防止 size 不一致后续运行出错
    live_roas_cvr_adjust_ratio_list_.assign(live_roas_discount_list_.size(), 1.0);
  }
  // discount 平滑系数
  live_roas_discount_ratio_ = SPDM_live_roas_discount_ratio(session_data->get_spdm_ctx());
  // 金币上界与折扣系数
  live_roas_d_i_discount_coef_ = SPDM_live_roas_d_i_discount_coef(session_data->get_spdm_ctx());
  live_roas_d_i_coin_upper_coef_ = SPDM_live_roas_d_i_coin_upper_coef(session_data->get_spdm_ctx());
  // 记录观测数据
  live_order_d_i_ob_data_record_ratio_ =
    SPDM_live_order_d_i_ob_data_record_ratio(session_data->get_spdm_ctx());
  // profit 阈值
  enable_order_d_i_use_profit_threshold_ =
    SPDM_enable_order_d_i_use_profit_threshold(session_data->get_spdm_ctx());
  order_d_i_use_profit_threshold_ =
    SPDM_order_roas_d_i_use_profit_threshold(session_data->get_spdm_ctx());
  const auto& specific_industry_ids = RankKconfUtil::orderDeepIncentiveSpecificIndustryIds();
  if (specific_industry_ids) {
    specific_industry_ids_.clear();
    specific_industry_ids_.assign(specific_industry_ids->begin(), specific_industry_ids->end());
  }
}

bool LiveRoasDeepCoinDecisionPlugin::PvAdmit(ContextData* session_data) {
  auto live_roas_admit_sub_page_ids = RankKconfUtil::liveRoasAdmitSubPageIds();

  // 生效页面准入
  if (live_roas_admit_sub_page_ids &&
      !live_roas_admit_sub_page_ids->count(session_data->get_sub_page_id())) {
    return false;
  }
  // 风控判断
  if (SPDM_enable_risk_user_skip_deep_incentive(session_data->get_spdm_ctx())) {
    int reward_antispam_code = session_data->get_rank_request()->ad_request().front_antispam_code();
    base::Json json_extra = base::Json(base::StringToJson(
        session_data->get_rank_request()->ad_request().extra_request_ctx()));
    int live_antispam_code = 0;
    if (json_extra.IsObject()) {
      live_antispam_code = json_extra.GetInt("liveAntispamCode", 0);
    }
    if (reward_antispam_code > 0 || live_antispam_code == 1001) {
      return false;
    }
  }
  // ios 不出
  if (session_data->get_is_ios_platform()) {
    return false;
  }
  // 版控
  if (ks::ad_base::AppVersionCompare::Compare(
      session_data->get_rank_request()->ad_request().ad_user_info().platform_version(), "13.3.30") < 0) {
    return false;
  }

  return true;
}

bool LiveRoasDeepCoinDecisionPlugin::AdAdmit(ContextData* session_data, AdCommon* p_ad) {
  if (!(p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS))) {
    return false;
  }

  return true;
}

void LiveRoasDeepCoinDecisionPlugin::GetPredictUpliftCvr(ContextData* session_data, AdCommon* p_ad) {
  live_roas_uplift_ctcvr_list_.clear();
  double base_cvr = 0;
  double live_roas_uplift_cvr = 0;
  double predict_cvr = 0;
  for (int i = 0; i < live_roas_uplift_ctcvr_pts_.size(); i++) {
    if (i == 0) {
      base_cvr = p_ad->get_predict_score(live_roas_uplift_ctcvr_pts_[i]);
    }
    predict_cvr = p_ad->get_predict_score(live_roas_uplift_ctcvr_pts_[i]);
    if (i >= 0 && i < live_roas_coin_upper_list_.size()) {
      p_ad->mutable_incentive_deep_uplift_cvr()->insert({live_roas_coin_upper_list_[i], predict_cvr});
    }
    live_roas_uplift_cvr = predict_cvr - base_cvr;
    live_roas_uplift_ctcvr_list_.push_back(live_roas_uplift_cvr);
  }
}

void LiveRoasDeepCoinDecisionPlugin::DeepCoinDecision(ContextData* session_data, AdCommon* p_ad) {
  if (!(live_roas_uplift_ctcvr_pts_.size() == live_roas_discount_list_.size() &&
      live_roas_discount_list_.size() == live_roas_coin_upper_list_.size() &&
      live_roas_coin_upper_list_.size() == live_roas_uplift_ctcvr_list_.size())) {
    RANK_DOT_COUNT(session_data, 1, "deep_incentive_error", "live_roas_vector_size_unequal");
    return;
  }
  double profit = 0;
  double optimal_profit = 0;
  int index = 0;
  double optimal_uplift_cvr = 0.0;
  double ecpm_ratio = 0;
  double auto_roas =  p_ad->get_auto_roas() > 0 ? p_ad->get_auto_roas() : p_ad->get_roi_ratio();
  double base_cvr = p_ad->get_predict_score(live_roas_uplift_ctcvr_pts_[0]);
  double discount_ratio = 1;
  double profit_threshold = 0;
  if (enable_order_d_i_use_profit_threshold_ && !specific_industry_ids_.empty() &&
      std::find(specific_industry_ids_.begin(), specific_industry_ids_.end(),
      p_ad->get_first_industry_id_v5()) != specific_industry_ids_.end()) {
    profit_threshold = order_d_i_use_profit_threshold_;
  }
  if (auto_roas == 0 || base_cvr == 0) {
     return;
  }
  // 金币决策
  for (int i = 0; i < live_roas_uplift_ctcvr_list_.size(); i++) {
    double discount = live_roas_discount_list_[i];
    double uplift_cvr = live_roas_uplift_ctcvr_list_[i];
    profit = (1 / auto_roas - discount_ratio * discount) * (base_cvr + uplift_cvr);
    if (i == 0) {
      p_ad->set_pec_coupon_origin_cpm(profit);
    }
    if (profit - optimal_profit > profit_threshold) {
      optimal_profit = profit;
      index = i;
      optimal_uplift_cvr = uplift_cvr;
    }
  }
  optimal_uplift_cvr =
      std::min(optimal_uplift_cvr,p_ad->get_unify_ctr_info().value * p_ad->get_unify_cvr_info().value * live_roas_optimal_uplift_cvr_ratio_);  //NOLINT
  p_ad->set_order_deep_incentive_type(kuaishou::ad::OrderDeepIncentiveType::ORDER_DISCOUNT_COIN);
  p_ad->set_coin_discount_ratio(live_roas_discount_list_[index] * live_roas_d_i_discount_coef_);
  p_ad->set_deep_rewarded_coin(floor(live_roas_coin_upper_list_[index] * live_roas_d_i_coin_upper_coef_));
  p_ad->set_pec_coupon_uplift_profit(optimal_profit);
  // 记录观测数据
  double ob_data_random_number = ad_base::AdRandom::GetDouble();
  if (live_order_d_i_ob_data_record_ratio_ > 0 &&
      ob_data_random_number < live_order_d_i_ob_data_record_ratio_) {
    p_ad->set_incentive_deep_coin_rct_tag(10);
  }
  ecpm_ratio = (optimal_uplift_cvr + base_cvr) / base_cvr;
  ecpm_ratio = std::min(ecpm_ratio, live_roas_optimal_uplift_cvr_ratio_ + 1);
  ecpm_ratio *= SafeGetDoubleVectorValue(live_roas_cvr_adjust_ratio_list_, index,
    1.0, session_data, "live_roas_cvr_adjust_ratio_list_");
  ecpm_ratio = std::max(std::min(ecpm_ratio, 1.5), 0.5);
  p_ad->set_incentive_uplift_ecpm_ratio(ecpm_ratio);
  if (p_ad->get_deep_rewarded_coin() > 0 && !p_ad->get_reward_styles().count(
      kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER),
      kuaishou::ad::RewardStyleType::PLAY_AND_ORDER});
  }
  // 打点监控
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "req_cnt"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, p_ad->get_deep_rewarded_coin(), absl::StrCat(prefix_, "order_deep_coin"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, optimal_uplift_cvr * 1e6, absl::StrCat(prefix_, "optimal_uplift_cvr"),
    exp_tag_, absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "treatment_", index), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, base_cvr * 1e6, absl::StrCat(prefix_, "base_cvr"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
  RANK_DOT_STATS(session_data, ecpm_ratio * 1e6, absl::StrCat(prefix_, "ecpm_ratio"), exp_tag_,
    absl::StrCat(session_data->get_sub_page_id()), p_ad->get_product_name());
}

void LiveRoasDeepCoinDecisionPlugin::RandomDeepCoinStrategy(ContextData* session_data, AdCommon* p_ad) {
  int index = ad_base::AdRandom::GetInt(0, live_roas_treatment_map_.size() - 1);
  if (is_rct_ && index < live_roas_treatment_map_.size() && index >= 0) {  // 防止数组越界
    auto& kv = live_roas_treatment_map_[index];
    double coin_discount_ratio = 0;
    int deep_coin_upper = 0;
    if (absl::SimpleAtod(kv.first, &coin_discount_ratio)) {
      deep_coin_upper = kv.second;
    }
    p_ad->set_order_deep_incentive_type(kuaishou::ad::OrderDeepIncentiveType::ORDER_DISCOUNT_COIN);
    p_ad->set_coin_discount_ratio(coin_discount_ratio);
    p_ad->set_deep_rewarded_coin(deep_coin_upper);  // 对于折扣金币，此处填充值为金币上界
    p_ad->set_incentive_deep_coin_rct_tag(1);
    // rct 实验不影响排序和计费
    p_ad->set_pec_uplift_cvr(0.0);
    RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "rct_treatment_", index), exp_tag_);
  }
  index = ad_base::AdRandom::GetInt(0, live_roas_global_treatment_discount_list_.size() - 1);
  if (enable_live_roas_d_i_use_new_treatment_ && is_rct_ && index >= 0 &&
      index < live_roas_global_treatment_discount_list_.size()) {
    p_ad->set_order_deep_incentive_type(kuaishou::ad::OrderDeepIncentiveType::ORDER_DISCOUNT_COIN);
    p_ad->set_coin_discount_ratio(live_roas_global_treatment_discount_list_[index]);
    p_ad->set_deep_rewarded_coin(live_roas_global_treatment_coin_upper_list_[index]);
    p_ad->set_incentive_deep_coin_rct_tag(2);
    p_ad->set_pec_uplift_cvr(0.0);
    p_ad->set_incentive_uplift_ecpm_ratio(1.0);
    RANK_DOT_COUNT(session_data, 1, absl::StrCat(prefix_, "global_rct_treatment_", index), exp_tag_);
  }
  // 若随机到的深度金币为 0 或本次 pv 没有命中 rct 则移除深度样式
  if ((p_ad->get_deep_rewarded_coin() <= 0 || !is_rct_) && p_ad->get_reward_styles().count(
  kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->erase(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER));
  }
  // 若本次 pv 命中 rct 且随机到的深度金币不为 0 但没有样式，则加上样式
  if (p_ad->get_deep_rewarded_coin() > 0 && is_rct_ && !p_ad->get_reward_styles().count(
    kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER))) {
    p_ad->mutable_reward_styles()->insert(
      {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER),
      kuaishou::ad::RewardStyleType::PLAY_AND_ORDER});
  }
}

/* -----使用 qcpx 券替代深度激励金币----- */
const char* IncentiveReplaceCoinByCoupon::Name() { return "ReplaceCoinByCoupon"; }

void IncentiveReplaceCoinByCoupon::Clear() {}

bool IncentiveReplaceCoinByCoupon::IsRun(const ContextData* session_data, const Params* params,
                                          AdRankUnifyScene pos, const AdList* adlist) {
  // 只对激励流量生效
  if (!(session_data->get_is_rewarded() || session_data->get_is_inspire_live_request()) ||
      adlist->Size() <= 0) {
    return false;
  }
  if (SPDM_enable_order_d_i_use_qcpx_coupon(session_data->get_spdm_ctx()) ||
      SPDM_enable_live_order_d_i_use_qcpx_coupon(session_data->get_spdm_ctx()) ||
      SPDM_enable_live_roas_d_i_use_qcpx_coupon(session_data->get_spdm_ctx())) {
    return true;
  }

  return false;
}

StraRetCode IncentiveReplaceCoinByCoupon::Process(ContextData* session_data, Params* params,
                                                  AdRankUnifyScene pos, AdList* adlist) {
  bool enable_incentive_fill_coupon_scope =
    SPDM_enable_incentive_fill_coupon_scope(session_data->get_spdm_ctx());
  // u0 过滤 先不加
  // 版控
  std::string app_ver = session_data->get_app_version();
  if (ks::ad_base::AppVersionCompare::Compare(app_ver, "13.7.20") < 0) {
    return StraRetCode::SUCC;
  }
  // 页面准入
  if (SPDM_enable_qcpx_coupon_add_sub_page_id_admit(session_data->get_spdm_ctx())) {
    const auto& admit_sub_page_ids = RankKconfUtil::qcpxCouponSubPageIdSet();
    if (admit_sub_page_ids && !admit_sub_page_ids->count(session_data->get_sub_page_id())) {
      return StraRetCode::SUCC;
    }
  }
  // 黑名单
  const auto& qcpx_filter_item_list = RankKconfUtil::qcpxFilterItemList();
  const auto& qcpx_filter_author_list = RankKconfUtil::qcpxFilterAuthorList();
  // 券信息读取
  const ks::platform::AttrTable* coupon_table = session_data->common_r_->GetTable("ad_coupon_table");
  ks::platform::ItemAttr* coupon_type_attr = nullptr;
  ks::platform::ItemAttr* coupon_status_attr = nullptr;
  ks::platform::ItemAttr* coupon_rule_attr = nullptr;
  if (coupon_table) {
    coupon_type_attr = coupon_table->GetAttr("fd_ad_coupon_template_coupon_type");
    coupon_status_attr = coupon_table->GetAttr("fd_ad_coupon_template_status");
    coupon_rule_attr = coupon_table->GetAttr("fd_ad_coupon_template_rule");
  }
  if (!coupon_type_attr || !coupon_status_attr || !coupon_rule_attr) {
    return StraRetCode::SUCC;
  }

  // ad 级别过滤
  for (auto* p_ad : adlist->Ads()) {
    bool need_coupon_scope = false;
    if (p_ad == nullptr) {
      continue;
    }
    if (qcpx_filter_item_list && qcpx_filter_item_list->count(p_ad->get_merchant_product_id()) > 0) {
      continue;
    }
    if (qcpx_filter_author_list && qcpx_filter_author_list->count(p_ad->get_author_id()) > 0) {
      continue;
    }
    if (!(p_ad->get_reward_styles().count(
        kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER)) &&
        p_ad->get_deep_rewarded_coin() > 0)) {
      continue;
    }
    const auto& coupon_id_list =
      p_ad->Attr(ItemIdx::coupon_config_id_list).
      GetIntListValue(p_ad->AttrIndex()).value_or(absl::Span<const int64_t>());
    for (const auto& coupon_id : coupon_id_list) {
      if (coupon_id <= 0) continue;
      auto index = coupon_table->GetItemAttrIndex(coupon_id);
      if (!index.has_value()) continue;
      int32_t coupon_template_status = coupon_status_attr->GetIntValue(index.value()).value_or(0);
      if (coupon_template_status != 1) continue;
      int32_t coupon_type = coupon_type_attr->GetIntValue(index.value()).value_or(0);
      const auto& coupon_rule = coupon_rule_attr->GetStringValue(index.value()).value_or("");
      if (coupon_type == 1 && p_ad->get_order_deep_incentive_type() !=
          kuaishou::ad::OrderDeepIncentiveType::ORDER_DISCOUNT_COIN) {
        int64_t deep_coin = p_ad->get_deep_rewarded_coin();
        // 满减券
        p_ad->set_coupon_template_id(coupon_id);
        p_ad->set_coupon_type(coupon_type);
        p_ad->set_threshold(10000);
        p_ad->set_coupon_discount_amount(static_cast<int64_t>(deep_coin / 10000) * 1000);
        p_ad->set_threshold_type(1);
        p_ad->set_threshold_upper(8000000);
        p_ad->set_discount_amount_upper(150000);
        p_ad->set_reduce_amount(0);
        p_ad->set_capped_amount(0);
        p_ad->set_expire_minutes(30);
        p_ad->mutable_reward_styles()->erase(
          kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER));
        need_coupon_scope = true;
      } else if (coupon_type == 2 && p_ad->get_order_deep_incentive_type() ==
          kuaishou::ad::OrderDeepIncentiveType::ORDER_DISCOUNT_COIN &&
          p_ad->get_coin_discount_ratio() > 0) {
        double coin_discount = p_ad->get_coin_discount_ratio();
        ::Json::Reader reader;
        ::Json::Value value;
        if (!reader.parse(std::string(coupon_rule), value)) continue;
        if (!value.isObject()) continue;
        uint64_t coupon_rate = value["reduceAmount"].asUInt64();
        uint64_t capped_amount = value["cappedAmount"].asUInt64();
        if ((1000 * (1 - coin_discount)) != coupon_rate) continue;
        p_ad->set_coupon_template_id(coupon_id);
        p_ad->set_coupon_type(coupon_type);
        p_ad->set_reduce_amount(coupon_rate);
        p_ad->set_capped_amount(capped_amount);
        p_ad->set_threshold(0);
        p_ad->set_coupon_discount_amount(0);
        p_ad->set_threshold_upper(8000000);
        p_ad->set_discount_amount_upper(150000);
        p_ad->mutable_reward_styles()->erase(
          kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_ORDER));
        need_coupon_scope = true;
      }
    }
    if (enable_incentive_fill_coupon_scope && need_coupon_scope) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
        p_ad->set_coupon_scope(1);
      } else if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
        p_ad->set_coupon_scope(2);
      }
    }
  }
  return StraRetCode::SUCC;
}

/* -----付费激励使用 uplift cvr 决策金币----- */
const char* PurchaseDeepCoinDecisionPlugin::Name() { return "PurchaseDeepCoinDecisionPlugin"; }

void PurchaseDeepCoinDecisionPlugin::Clear() {}

bool PurchaseDeepCoinDecisionPlugin::IsRun(const ContextData* session_data, const Params* params,
                                          AdRankUnifyScene pos, const AdList* adlist) {
  // 只对激励流量生效
  if (!(session_data->get_is_rewarded() || session_data->get_is_inspire_live_request()) ||
      adlist->Size() <= 0) {
    return false;
  }
  if (SPDM_enable_purchase_deep_incentive_rct(session_data->get_spdm_ctx())) {
    return true;
  }

  return false;
}

StraRetCode PurchaseDeepCoinDecisionPlugin::Process(ContextData* session_data, Params* params,
                                                  AdRankUnifyScene pos, AdList* adlist) {
  // 版控
  std::string app_ver = session_data->get_app_version();
  if (ks::ad_base::AppVersionCompare::Compare(app_ver, "13.7.30") < 0) {
    return StraRetCode::SUCC;
  }
  // 操作系统
  if (!SPDM_enable_purchase_deep_incentive_rct_add_ios(session_data->get_spdm_ctx()) &&
      session_data->get_is_ios_platform()) {
    return StraRetCode::SUCC;
  }
  // 黑白名单
  const auto& black_list = RankKconfUtil::purchaseIncentiveBlackList();
  const auto& white_list = RankKconfUtil::purchaseIncentiveWhiteList();
  // 生效页面
  const auto& deep_incentive_rct_config = RankKconfUtil::deepIncentiveRctConfig();
  const auto& rct_admit_sub_page_id =
      deep_incentive_rct_config->data().rct_sub_page_id().purchase_sub_page_id();
  if (std::find(rct_admit_sub_page_id.begin(), rct_admit_sub_page_id.end(),
      session_data->get_sub_page_id()) == rct_admit_sub_page_id.end()) {
    return StraRetCode::SUCC;
  }
  const auto& treatment_coin_list =
    deep_incentive_rct_config->data().rct_config_v1().purchase_treatment_coin_list();

  double rct_ratio = SPDM_purchase_deep_incentive_rct_ratio(session_data->get_spdm_ctx());

  // ad 级别过滤
  for (auto* p_ad : adlist->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    const auto& account_id = absl::StrCat(p_ad->get_account_id());
    const std::string& action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    const std::string& campaign_type = kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type());;
    std::string ocpc_key_1 = absl::StrCat(action_type, "_", account_id);
    std::string ocpc_key_2 = absl::StrCat(action_type, "_", p_ad->get_product_name());
    std::string campaign_key_1 = absl::StrCat(campaign_type, "_", account_id);
    std::string campaign_key_2 = absl::StrCat(campaign_type, "_", p_ad->get_product_name());
    if (black_list && (black_list->count(p_ad->get_product_name()) || black_list->count(account_id)
        || black_list->count(ocpc_key_1) || black_list->count(ocpc_key_2) ||
        black_list->count(campaign_key_1) || black_list->count(campaign_key_2))) {
      continue;
    }
    if (white_list && (white_list->count(p_ad->get_product_name()) || white_list->count(account_id)
        || white_list->count(ocpc_key_1) || white_list->count(ocpc_key_2) ||
        white_list->count(campaign_key_1) || white_list->count(campaign_key_2))) {
      double random_number = ad_base::AdRandom::GetDouble();
      if (random_number > rct_ratio) {
        continue;
      }
      int index = ad_base::AdRandom::GetInt(0, treatment_coin_list.size() - 1);
      int deep_coin = 0;
      if (index >= 0 && index < treatment_coin_list.size()) {
        deep_coin = treatment_coin_list[index];
      }
      p_ad->mutable_reward_styles()->clear();
      p_ad->set_deep_rewarded_coin(deep_coin);
      p_ad->set_incentive_deep_coin_rct_tag(2);
      p_ad->set_pec_uplift_cvr(0.0);
      p_ad->set_incentive_uplift_ecpm_ratio(1);
      if (deep_coin > 0) {
        p_ad->mutable_reward_styles()->insert(
          {kuaishou::ad::RewardStyleType_Name(kuaishou::ad::RewardStyleType::PLAY_AND_PURCHASE),
          kuaishou::ad::RewardStyleType::PLAY_AND_PURCHASE});
      }
      RANK_DOT_COUNT(session_data, 1, absl::StrCat("ad_rank.purchase_incentive.", "rct_treatment_", index));
    }
  }
  return StraRetCode::SUCC;
}

/* 辅助函数统一放在下面 */

// 判断是否安装
bool JudgeIsInstalled(const AdCommon* ad, const ContextData* session_data) {
  auto product_to_package_maps = RankKconfUtil::ProductActivePackageName();
  const std::string& product_name = ad->get_product_name();
  std::string package_name = "";
  if (product_to_package_maps != nullptr) {
    auto product_map_iter = product_to_package_maps->find(product_name);
    if (product_map_iter != product_to_package_maps->end()) {
      package_name = product_map_iter->second;
    }
  }
  if (package_name == "") {
    return false;
  }

  if (session_data->get_rank_request()->ad_request().ad_user_info().device_info_size() > 0) {
    const auto& app_packages = session_data->get_rank_request()->ad_request().
                                      ad_user_info().device_info(0).app_package();
    for (const auto& app_package : app_packages) {
      if (package_name == app_package) {
        return true;
      }
    }
  }
  return false;
}

// 获取下发时间间隔
int64_t GetIncentiveStyleDeliverTimeGap(
    const ContextData* session_data, std::string* reward_type) {
  const auto now = session_data->get_current_timestamp_nodiff();
  int64_t latest_deliver_timestamp = 0;
  kuaishou::ad::RewardStyleType target_style;
  if (*reward_type == "invoke") {
    target_style = kuaishou::ad::RewardStyleType::PLAY_AND_INVOKED;
  } else if (*reward_type == "active") {
    target_style = kuaishou::ad::RewardStyleType::ACTIVE_APP;
  } else if (*reward_type == "order") {
    target_style = kuaishou::ad::RewardStyleType::PLAY_AND_ORDER;
  } else {
    return now / 1000 / 1000;
  }
  // 遍历服务端 browseSet, 获取最近一次下发样式的时间
  const auto& ad_browsed_infos = session_data->get_rank_request()->ad_request().ad_user_info().ad_browsed_info();  // NOLINT
  for (const auto& browsed : ad_browsed_infos) {
    for (const auto& detail_info : browsed.ad_detail_info()) {
      if (detail_info.award_inspire_style() == target_style &&
          browsed.timestamp() > latest_deliver_timestamp) {
        latest_deliver_timestamp = browsed.timestamp();
      }
    }
  }
  return (now - latest_deliver_timestamp) / 1000 / 1000;
}

// 获取任务完成次数
int64_t GetIncentiveStyleTaskCompleteCount(
    const ContextData* session_data, std::string* reward_type, std::unordered_set<int64_t>* product_id_list) {
  const auto& ad_action_infos = session_data->get_rank_request()->ad_request()
                                    .ad_session_response_pack()
                                    .response()
                                    .user_action_info()
                                    .ad_action_info();

  int64_t now_s = session_data->get_current_timestamp_nodiff() / 1000000;
  auto bd = absl::FromUnixSeconds(now_s).In(absl::LocalTimeZone());
  int64_t start_of_day = now_s - bd.hour * 3600 - bd.minute * 60 - bd.second;   // 单位：秒

  int task_compelete_count = 0;
  for (const auto& action_info : ad_action_infos) {
    for (const auto& detail_info : action_info.ad_detail_info()) {
      if (((detail_info.award_play_and_invoked() && *reward_type == "invoke") ||
          (detail_info.award_active_app() && *reward_type == "active")) &&
          detail_info.timestamp() / 1000 > start_of_day) {
        // 保存该用户当天完成的激励拉活任务的 product_id 列表
        product_id_list->insert(detail_info.product_hash());
        // 统计当天完成拉活激励任务的次数
        ++task_compelete_count;
      }
    }
  }
  return task_compelete_count;
}

double SafeGetDoubleVectorValue(const std::vector<double>& target_vector, const int& index,
  double defalut_value, ContextData* session_data, const std::string& vector_tag) {
  if (index >= 0 && index < target_vector.size()) {
    return target_vector[index];
  }
  RANK_DOT_COUNT(session_data, 1, "deep_incentive_vector_error", vector_tag);
  return defalut_value;
}
}  // namespace ad_rank
}  // namespace ks
