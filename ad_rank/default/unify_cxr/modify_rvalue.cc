#include <string>
#include <algorithm>
#include <vector>
#include <tuple>
#include <cmath>
#include <unordered_map>
#include <unordered_set>
#include "base/time/time.h"
#include "base/strings/string_split.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_rank/default/unify_cxr/modify_rvalue.h"
#include "teams/ad/ad_rank/utils/kconf/kconf.h"
#include "teams/ad/ad_rank/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank/default/params/calc_benefit_param.h"
#include "teams/ad/ad_rank/default/params/native_unify_calc_params.h"
#include "teams/ad/ad_rank/processor/factor/ranking_data.h"

namespace ks {
namespace ad_rank {
using ks::engine_base::PredictType;

const char* ModifyUnifyCxrInfoNormal::Name() {
  return "ModifyUnifyCxrInfoNormal";
}
void ModifyUnifyCxrInfoNormal::Clear() {}
bool ModifyUnifyCxrInfoNormal::IsRun(
    const ContextData* session_data, const Params* params,
    AdRankUnifyScene pos, const AdList* ad_list) {
  return ad_list->Size() > 0;
}

StraRetCode ModifyUnifyCxrInfoNormal::Process(
    ContextData* session_data_, Params* params, AdRankUnifyScene pos, AdList* ad_list) {
  CalcBenefitParams* calc_benefit_params = dynamic_cast<CalcBenefitParams*>(params);
  if (calc_benefit_params == nullptr) {
    return StraRetCode::SUCC;
  }
  auto& cali_params = calc_benefit_params->search_calibrate_params;
  auto& merchant_ranking_params = calc_benefit_params->merchant_ranking_params;

  for (auto p_ad : ad_list->Ads()) {
    // 24 策略重构 由于 ad_dsp_server_client_show_inner 只覆盖硬广，本策略维持现状：仅对硬广生效
    if (p_ad->get_ad_list_type() != 1) {continue;}
    // 目前仅作实验用，跳过老校准
    if (p_ad->get_is_skip_cali_before() == 1) {continue;}
    // 硬广融合，内循环直播生效
    if (!p_ad->Is(AdFlag::is_live_ad_inner) && !p_ad->Is(AdFlag::is_p2l_ad_inner)) continue;
    auto &ad = *p_ad;
    if (!p_ad->Is(AdFlag::is_amd_live_campaign)) {
      continue;
    }
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data_, p_ad)
    bool direct_live = p_ad->Is(AdFlag::is_amd_direct_live);
    // ab for online calibration
    if (p_ad->get_enable_unify_rvalue()) {
      // 校准 CTR 值，系数为模型预估的 server_client_show_rate, 暂不下线
      if (!direct_live) {  // 直投直播, 作品引流
        if (!session_data_->get_is_thanos_request()) {  // 作品引流双列
          double k_factor = 1.0, b = 0.0;
          k_factor *= p_ad->get_predict_score(engine_base::PredictType::PredictType_server_client_show_rate);  // NOLINT
          if (merchant_ranking_params.ctr_discount_ratio_photo2live > 0) {
            k_factor *= merchant_ranking_params.ctr_discount_ratio_photo2live;
          }
          p_ad->ModifyUnifyInfoLinear(RUnifyType::CTR,
                                   kuaishou::ad::AD_DELIVERY,
                                   kuaishou::ad::AD_PHOTO_IMPRESSION,
                                   RUnifyTag::AD_LIVE_PLAYED_3S,
                                   k_factor, b);
        }
      }
      switch (p_ad->get_ocpx_action_type()) {
        if (!SPDM_disable_useless_calibration_strategy(session_data_->get_spdm_ctx())) {
        case kuaishou::ad::EVENT_GOODS_VIEW: {
          if (session_data_->get_is_thanos_request()) {
            double upper_bound = merchant_ranking_params.live_c2_goods_view_upper_bound;
            p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                   kuaishou::ad::AD_LIVE_PLAYED_3S,
                                   p_ad->get_ocpx_action_type(),
                                   RUnifyTag::EVENT_GOODS_VIEW,
                                   1.0, 0.0,
                                   upper_bound, 0.0);
          } else {
            double upper_bound = merchant_ranking_params.live_c1_goods_view_upper_bound;
            if (direct_live) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                   kuaishou::ad::AD_LIVE_PLAYED_3S,
                                   p_ad->get_ocpx_action_type(),
                                   RUnifyTag::EVENT_GOODS_VIEW,
                                   1.0, 0.0,
                                   upper_bound, 0.0);
             } else {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                   kuaishou::ad::AD_ITEM_IMPRESSION,
                                   p_ad->get_ocpx_action_type(),
                                   RUnifyTag::EVENT_GOODS_VIEW,
                                   1.0, 0.0,
                                   upper_bound * p_ad->get_cvr(), 0.0);
             }
          }
          break;
        }
        case kuaishou::ad::CID_EVENT_ORDER_PAID:
        case kuaishou::ad::EVENT_ORDER_PAIED: {
          if (session_data_->get_is_thanos_request()) {  // 单列
            double upper_bound = merchant_ranking_params.live_c2_order_paied_upper_bound;
            p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                   kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                                   p_ad->get_ocpx_action_type(),
                                   RUnifyTag::EVENT_ORDER_PAIED,
                                   1.0, 0.0,
                                   upper_bound, 0.0);
          } else {  // 双列
            double upper_bound =
                merchant_ranking_params.live_c1_order_paied_upper_bound;
            if (direct_live) {
              p_ad->ModifyUnifyInfoLinear(
                  RUnifyType::CVR,
                  kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                  p_ad->get_ocpx_action_type(), RUnifyTag::EVENT_ORDER_PAIED,
                  1.0, 0.0, upper_bound, 0.0);
            } else {
              if (merchant_ranking_params.live_c1_p2l_paid_ratio > 0.0) {
                double k_factor =
                    merchant_ranking_params.live_c1_p2l_paid_ratio;
                p_ad->ModifyUnifyInfoLinear(
                    RUnifyType::CVR,
                    kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                    p_ad->get_ocpx_action_type(),
                    RUnifyTag::EVENT_ORDER_PAIED, k_factor, 0.0,
                    upper_bound, 0.0);
              }
            }
          }
          break;
        }
        }  // AB test 下线无用校准策略
        default:
          break;
        }
      }
    }

  return StraRetCode::SUCC;
}

const char* ManualCalibrationPlugin::Name() {
  return "ManualCalibrationPlugin";
}

void ManualCalibrationPlugin::Clear() {}

bool ManualCalibrationPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode ManualCalibrationPlugin::Process(ContextData* session_data, Params* params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  if (ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (KS_UNLIKELY(params_ == nullptr)) {
    return StraRetCode::SUCC;
  }
  enable_ctr_manual_cali = SPDM_enable_ctr_manual_cali(session_data->get_spdm_ctx());
  enable_cvr_manual_cali = SPDM_enable_cvr_manual_cali(session_data->get_spdm_ctx());
  enable_dcvr_manual_cali = SPDM_enable_dcvr_manual_cali(session_data->get_spdm_ctx());
  enable_ltv_manual_cali = SPDM_enable_ltv_manual_cali(session_data->get_spdm_ctx());
  enable_manual_cali_add_creativetype =
    SPDM_enable_manual_cali_add_creativetype(session_data->get_spdm_ctx());
  enable_manual_cali_itemtype =
    SPDM_enable_manual_cali_itemtype(session_data->get_spdm_ctx());
  enable_manual_cali_remove_unvalid_tag =
    SPDM_enable_manual_cali_remove_unvalid_tag(session_data->get_spdm_ctx());
  enable_adxcpc_miss_ctr_manual_calibrate =
    SPDM_enable_adxcpc_miss_ctr_manual_calibrate(session_data->get_spdm_ctx());
  enable_adxcpc_model_cali = SPDM_enable_adxcpc_model_cali(session_data->get_spdm_ctx());
  enable_mini_game_iaap_calibration_strategy_v2 =
      SPDM_enable_mini_game_iaap_calibration_strategy_v2(session_data->get_spdm_ctx());
  enable_mini_game_seven_day_iaap_calibration_strategy =
      SPDM_enable_mini_game_seven_day_iaap_calibration_strategy(session_data->get_spdm_ctx());
  enable_mini_game_seven_day_iaap_deprioritize_strategy =
      SPDM_enable_mini_game_seven_day_iaap_deprioritize_strategy(session_data->get_spdm_ctx());
  enable_game_seven_day_deprioritize_strategy =
      SPDM_enable_game_seven_day_deprioritize_strategy(session_data->get_spdm_ctx());
  enable_game_first_r_calibration_strategy =
      SPDM_enable_game_first_r_calibration_strategy(session_data->get_spdm_ctx());
  enable_big_game_first_r_calibration_strategy =
      SPDM_enable_big_game_first_r_calibration_strategy(session_data->get_spdm_ctx());
  enable_game_iap_7r_ltv_adjust =
      SPDM_enable_game_iap_7r_ltv_adjust(session_data->get_spdm_ctx());
  game_iap_7r_ltv_coef =
      SPDM_game_iap_7r_ltv_coef(session_data->get_spdm_ctx());
  game_iap_7r_ltv_adjust_tag =
      SPDM_game_iap_7r_ltv_adjust_tag(session_data->get_spdm_ctx());

  enable_game_sdk_7r_ltv_adjust =
      SPDM_enable_game_sdk_7r_ltv_adjust(session_data->get_spdm_ctx());
  game_sdk_7r_ltv_coef =
      SPDM_game_sdk_7r_ltv_coef(session_data->get_spdm_ctx());
  game_sdk_7r_ltv_adjust_tag =
      SPDM_game_sdk_7r_ltv_adjust_tag(session_data->get_spdm_ctx());

  enable_credit_grant_soft_cali =
      SPDM_enable_credit_grant_soft_cali(session_data->get_spdm_ctx());
  weight_credit_grant_soft_cali =
      SPDM_weight_credit_grant_soft_cali(session_data->get_spdm_ctx());

  if (RankKconfUtil::adxCpcModelCaliRate()) {
    auto adxCpcModelCaliRate = RankKconfUtil::adxCpcModelCaliRate();
    auto itr_hard = adxCpcModelCaliRate->find("HARD_AD_QUEUE");
    adxcpc_hard_cali_weight = (itr_hard != adxCpcModelCaliRate->end()) ? itr_hard->second : 1.0;
    auto itr_soft = adxCpcModelCaliRate->find("SOFT_AD_QUEUE");
    adxcpc_soft_cali_weight = (itr_soft != adxCpcModelCaliRate->end()) ? itr_soft->second : 1.0;
  }

  const OnlineUnifyCtrCaliManuallyConfig& ctr_cali_manual_config
    = RankKconfUtil::onlineUnifyCtrCaliManuallyConfig()->data();
  const OnlineUnifyCvrCaliManuallyConfig& cvr_cali_manual_config
    = RankKconfUtil::onlineUnifyCvrCaliManuallyConfig()->data();
  const OnlineUnifyDcvrCaliManuallyConfig& dcvr_cali_manual_config
    = RankKconfUtil::onlineUnifyDcvrCaliManuallyConfig()->data();
  const OnlineUnifyLtvCaliManuallyConfig& ltv_cali_manual_config
    = RankKconfUtil::onlineUnifyLtvCaliManuallyConfig()->data();

  if (RankKconfUtil::onlineUnifyCxrCaliManuallyBound()) {
    auto onlineUnifyCxrCaliManuallyBound = RankKconfUtil::onlineUnifyCxrCaliManuallyBound();
    auto itr_min = onlineUnifyCxrCaliManuallyBound->find("manual_cali_lower");
    manual_cali_lower = (itr_min != onlineUnifyCxrCaliManuallyBound->end()) ? itr_min->second : 0.0;
    auto itr_max = onlineUnifyCxrCaliManuallyBound->find("manual_cali_upper");
    manual_cali_upper = (itr_max != onlineUnifyCxrCaliManuallyBound->end()) ? itr_max->second : 1000.0;
  }
  auto &ads = ad_list->Ads();
  for (auto *p_ad : ads) {
    if (p_ad == nullptr) continue;
    // 目前仅作实验用，跳过老校准
    if (p_ad->get_is_skip_cali_before() == 1) {continue;}
    if (!p_ad->Is(AdFlag::is_outer_loop_ad) &&
        p_ad->get_account_type() != kuaishou::ad::AdEnum::ACCOUNT_LSP) {
      continue;
    }
    std::string key_prefix = "";
    // LSP add prefix
    if (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
      key_prefix = "lsp_";
    }
    double cali_ratio = 1.0;
    std::string account_campaign = absl::StrCat(key_prefix, p_ad->get_account_id(),
                                  "_", p_ad->get_campaign_type());
    std::string account_campaign_page = absl::StrCat(key_prefix, p_ad->get_account_id(),
                                  "_", p_ad->get_campaign_type(),
                                  "_", session_data->get_page_id());
    std::string account_ocpx = absl::StrCat(key_prefix, p_ad->get_account_id(),
                                  "_", p_ad->get_ocpx_action_type());
    std::string account_ocpx_page = absl::StrCat(key_prefix, p_ad->get_account_id(),
                                  "_", p_ad->get_ocpx_action_type(),
                                  "_", session_data->get_page_id());
    std::string account_campaign_ocpc = absl::StrCat(key_prefix, p_ad->get_account_id(),
                                  "_", p_ad->get_campaign_type(),
                                  "_", p_ad->get_ocpx_action_type());
    std::string account_campaign_ocpc_page = absl::StrCat(key_prefix, p_ad->get_account_id(),
                                  "_", p_ad->get_campaign_type(),
                                  "_", p_ad->get_ocpx_action_type(),
                                  "_", session_data->get_page_id());
    std::string account_str = absl::StrCat(key_prefix, p_ad->get_account_id());
    int64 account = p_ad->get_account_id();
    // 本地不走外循环配置
    if (key_prefix == "LSP_") {
      account = -1;
    }
    std::string product = absl::StrCat(key_prefix, p_ad->get_product_name());
    std::string ocpx_account = absl::StrCat(key_prefix, p_ad->get_ocpx_action_type(), "_",
                                            p_ad->get_account_id());
    std::string ocpx_product = absl::StrCat(key_prefix, p_ad->get_ocpx_action_type(), "_",
                                            p_ad->get_product_name());
    std::string page_account =
        absl::StrCat(key_prefix, session_data->get_page_id(), "_", p_ad->get_account_id());
    std::string page_product =
        absl::StrCat(key_prefix, session_data->get_page_id(), "_", p_ad->get_product_name());
    std::string subpage_account =
        absl::StrCat(key_prefix, session_data->get_sub_page_id(), "_", p_ad->get_account_id());
    std::string subpage_product = absl::StrCat(key_prefix, session_data->get_sub_page_id(),
      "_", p_ad->get_product_name());

    std::string queue_account = absl::StrCat(key_prefix, p_ad->get_queue_type(), "_", p_ad->get_account_id());
    std::string queue_product =
        absl::StrCat(key_prefix, p_ad->get_queue_type(), "_", p_ad->get_product_name());
    std::string campaign_account =
        absl::StrCat(key_prefix, p_ad->get_campaign_type(), "_", p_ad->get_account_id());
    std::string campaign_product =
        absl::StrCat(key_prefix, p_ad->get_campaign_type(), "_", p_ad->get_product_name());

    std::string page_account_ocpx_deepconv = absl::StrCat(key_prefix, session_data->get_page_id(),
                                  "_", p_ad->get_account_id(),
                                  "_", p_ad->get_ocpx_action_type(),
                                  "_", p_ad->get_deep_conversion_type());
    std::string page_product_ocpx_deepconv = absl::StrCat(key_prefix, session_data->get_page_id(),
                                  "_", p_ad->get_product_name(),
                                  "_", p_ad->get_ocpx_action_type(),
                                  "_", p_ad->get_deep_conversion_type());

    std::string creativetype_bidtype_ocpx_product = absl::StrCat(key_prefix, p_ad->get_creative_type(),
                                  "_", p_ad->get_bid_type(),
                                  "_", p_ad->get_ocpx_action_type(),
                                  "_", p_ad->get_product_name());
    std::string creativetype_bidtype_ocpx_account = absl::StrCat(key_prefix, p_ad->get_creative_type(),
                                  "_", p_ad->get_bid_type(),
                                  "_", p_ad->get_ocpx_action_type(),
                                  "_", p_ad->get_account_id());
    std::string creativetype_bidtype_ocpx_deepconv_product =
        absl::StrCat(key_prefix, p_ad->get_creative_type(),
                                  "_", p_ad->get_bid_type(),
                                  "_", p_ad->get_ocpx_action_type(),
                                  "_", p_ad->get_deep_conversion_type(),
                                  "_", p_ad->get_product_name());
    std::string creativetype_bidtype_ocpx_deepconv_account =
        absl::StrCat(key_prefix, p_ad->get_creative_type(),
                                  "_", p_ad->get_bid_type(),
                                  "_", p_ad->get_ocpx_action_type(),
                                  "_", p_ad->get_deep_conversion_type(),
                                  "_", p_ad->get_account_id());

    std::string itemtype = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
    std::string itemtype_product = absl::StrCat(key_prefix, itemtype, "_", p_ad->get_product_name());
    std::string itemtype_account = absl::StrCat(key_prefix, itemtype, "_", p_ad->get_account_id());

    auto cal_cali_rate = [&] (
       const ::google::protobuf::Map<std::string, double>& creativetype_bidtype_ocpx_product_ratio,
       const ::google::protobuf::Map<std::string, double>& creativetype_bidtype_ocpx_account_ratio,
       const ::google::protobuf::Map<std::string, double>&
        creativetype_bidtype_ocpx_deepconv_product_ratio,
       const ::google::protobuf::Map<std::string, double>&
        creativetype_bidtype_ocpx_deepconv_account_ratio,
        const ::google::protobuf::Map<std::string, double>& itemtype_product_ratio,
        const ::google::protobuf::Map<std::string, double>& itemtype_account_ratio) -> double {
      double res_cali_rate = 1.0;
      auto iter_creativetype_bidtype_ocpx_product =
            creativetype_bidtype_ocpx_product_ratio.find(
              creativetype_bidtype_ocpx_product);
      if (iter_creativetype_bidtype_ocpx_product !=
            creativetype_bidtype_ocpx_product_ratio.end() &&
              0 < iter_creativetype_bidtype_ocpx_product->second) {
          res_cali_rate *= iter_creativetype_bidtype_ocpx_product->second;
      }
      auto iter_creativetype_bidtype_ocpx_account =
            creativetype_bidtype_ocpx_account_ratio.find(
              creativetype_bidtype_ocpx_account);
      if (iter_creativetype_bidtype_ocpx_account !=
            creativetype_bidtype_ocpx_account_ratio.end() &&
              0 < iter_creativetype_bidtype_ocpx_account->second) {
          res_cali_rate *= iter_creativetype_bidtype_ocpx_account->second;
      }
      auto iter_creativetype_bidtype_ocpx_deepconv_product =
            creativetype_bidtype_ocpx_deepconv_product_ratio.find(
              creativetype_bidtype_ocpx_deepconv_product);
      if (iter_creativetype_bidtype_ocpx_deepconv_product !=
            creativetype_bidtype_ocpx_deepconv_product_ratio.end() &&
              0 < iter_creativetype_bidtype_ocpx_deepconv_product->second) {
          res_cali_rate *= iter_creativetype_bidtype_ocpx_deepconv_product->second;
      }
      auto iter_creativetype_bidtype_ocpx_deepconv_account =
            creativetype_bidtype_ocpx_deepconv_account_ratio.find(
              creativetype_bidtype_ocpx_deepconv_account);
      if (iter_creativetype_bidtype_ocpx_deepconv_account !=
            creativetype_bidtype_ocpx_deepconv_account_ratio.end() &&
              0 < iter_creativetype_bidtype_ocpx_deepconv_account->second) {
          res_cali_rate *= iter_creativetype_bidtype_ocpx_deepconv_account->second;
      }
      if (enable_manual_cali_itemtype) {
        auto iter_itemtype_product = itemtype_product_ratio.find(itemtype_product);
        if (iter_itemtype_product != itemtype_product_ratio.end() &&
              0 < iter_itemtype_product->second) {
            res_cali_rate *= iter_itemtype_product->second;
        }
        auto iter_itemtype_account = itemtype_account_ratio.find(itemtype_account);
        if (iter_itemtype_account != itemtype_account_ratio.end() &&
              0 < iter_itemtype_account->second) {
            res_cali_rate *= iter_itemtype_account->second;
        }
      }
      return res_cali_rate;
    };

    if (enable_ctr_manual_cali &&
        !(p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC &&
          p_ad->get_ad_source_type() == kuaishou::ad::ADX &&
          enable_adxcpc_miss_ctr_manual_calibrate)) {
        auto iter_account = ctr_cali_manual_config.account_ratio().find(account);
        if (iter_account != ctr_cali_manual_config.account_ratio().end() && 0 < iter_account->second) {
            cali_ratio *= iter_account->second;
        }
        auto iter_product = ctr_cali_manual_config.product_ratio().find(product);
        if (iter_product != ctr_cali_manual_config.product_ratio().end() && 0 < iter_product->second) {
            cali_ratio *= iter_product->second;
        }
        auto iter_ocpx_account = ctr_cali_manual_config.ocpx_account_ratio().find(ocpx_account);
        if (iter_ocpx_account != ctr_cali_manual_config.ocpx_account_ratio().end() &&
                0 < iter_ocpx_account->second) {
            cali_ratio *= iter_ocpx_account->second;
        }
        auto iter_ocpx_product = ctr_cali_manual_config.ocpx_product_ratio().find(ocpx_product);
        if (iter_ocpx_product != ctr_cali_manual_config.ocpx_product_ratio().end() &&
                0 < iter_ocpx_product->second) {
            cali_ratio *= iter_ocpx_product->second;
        }
        auto iter_page_account = ctr_cali_manual_config.page_account_ratio().find(page_account);
        if (iter_page_account != ctr_cali_manual_config.page_account_ratio().end() &&
                0 < iter_page_account->second) {
            cali_ratio *= iter_page_account->second;
        }
        auto iter_page_product = ctr_cali_manual_config.page_product_ratio().find(page_product);
        if (iter_page_product != ctr_cali_manual_config.page_product_ratio().end() &&
                0 < iter_page_product->second) {
            cali_ratio *= iter_page_product->second;
        }
        auto iter_subpage_account = ctr_cali_manual_config.subpage_account_ratio().find(subpage_account);
        if (iter_subpage_account != ctr_cali_manual_config.subpage_account_ratio().end() &&
                0 < iter_subpage_account->second) {
            cali_ratio *= iter_subpage_account->second;
        }
        auto iter_subpage_product = ctr_cali_manual_config.subpage_product_ratio().find(subpage_product);
        if (iter_subpage_product != ctr_cali_manual_config.subpage_product_ratio().end() &&
                0 < iter_subpage_product->second) {
            cali_ratio *= iter_subpage_product->second;
        }
        auto iter_queue_account = ctr_cali_manual_config.queue_account_ratio().find(queue_account);
        if (iter_queue_account != ctr_cali_manual_config.queue_account_ratio().end() &&
                0 < iter_queue_account->second) {
            cali_ratio *= iter_queue_account->second;
        }
        auto iter_queue_product = ctr_cali_manual_config.queue_product_ratio().find(queue_product);
        if (iter_queue_product != ctr_cali_manual_config.queue_product_ratio().end() &&
                0 < iter_queue_product->second) {
            cali_ratio *= iter_queue_product->second;
        }
        auto iter_campaign_account = ctr_cali_manual_config.campaign_account_ratio().find(campaign_account);
        if (iter_campaign_account != ctr_cali_manual_config.campaign_account_ratio().end() &&
                0 < iter_campaign_account->second) {
            cali_ratio *= iter_campaign_account->second;
        }
        auto iter_campaign_product = ctr_cali_manual_config.campaign_product_ratio().find(campaign_product);
        if (iter_campaign_product != ctr_cali_manual_config.campaign_product_ratio().end() &&
                0 < iter_campaign_product->second) {
            cali_ratio *= iter_campaign_product->second;
        }
        if (enable_manual_cali_add_creativetype) {
          cali_ratio *= cal_cali_rate(
            ctr_cali_manual_config.creativetype_bidtype_ocpx_product_ratio(),
            ctr_cali_manual_config.creativetype_bidtype_ocpx_account_ratio(),
            ctr_cali_manual_config.creativetype_bidtype_ocpx_deepconv_product_ratio(),
            ctr_cali_manual_config.creativetype_bidtype_ocpx_deepconv_account_ratio(),
            ctr_cali_manual_config.itemtype_product_ratio(),
            ctr_cali_manual_config.itemtype_account_ratio());
        }
        auto iter_account_campaign =
            ctr_cali_manual_config.account_campaign_ratio().find(account_campaign);
        if (iter_account_campaign != ctr_cali_manual_config.account_campaign_ratio().end() &&
                0 < iter_account_campaign->second) {
            cali_ratio *= iter_account_campaign->second;
        }
        auto iter_account_campaign_page =
            ctr_cali_manual_config.account_campaign_page_ratio().find(account_campaign_page);
        if (iter_account_campaign_page != ctr_cali_manual_config.account_campaign_page_ratio().end() &&
                0 < iter_account_campaign_page->second) {
            cali_ratio *= iter_account_campaign_page->second;
        }
        auto iter_account_ocpx =
            ctr_cali_manual_config.account_ocpx_ratio().find(account_ocpx);
        if (iter_account_ocpx != ctr_cali_manual_config.account_ocpx_ratio().end() &&
                0 < iter_account_ocpx->second) {
            cali_ratio *= iter_account_ocpx->second;
        }
        auto iter_account_ocpx_page =
            ctr_cali_manual_config.account_ocpx_page_ratio().find(account_ocpx_page);
        if (iter_account_ocpx_page != ctr_cali_manual_config.account_ocpx_page_ratio().end() &&
                0 < iter_account_ocpx_page->second) {
            cali_ratio *= iter_account_ocpx_page->second;
        }
        auto iter_account_campaign_ocpc =
            ctr_cali_manual_config.account_campaign_ocpc_ratio().find(account_campaign_ocpc);
        if (iter_account_campaign_ocpc != ctr_cali_manual_config.account_campaign_ocpc_ratio().end() &&
                0 < iter_account_campaign_ocpc->second) {
            cali_ratio *= iter_account_campaign_ocpc->second;
        }
        auto iter_account_campaign_ocpc_page =
            ctr_cali_manual_config.account_campaign_ocpc_page_ratio().find(account_campaign_ocpc_page);
        if (iter_account_campaign_ocpc_page !=
                ctr_cali_manual_config.account_campaign_ocpc_page_ratio().end() &&
                0 < iter_account_campaign_ocpc_page->second) {
            cali_ratio *= iter_account_campaign_ocpc_page->second;
        }
        auto iter_account_str = ctr_cali_manual_config.account_str_ratio().find(account_str);
        if (iter_account_str !=
                ctr_cali_manual_config.account_str_ratio().end() &&
                0 < iter_account_str->second) {
            cali_ratio *= iter_account_str->second;
        }

        if (cali_ratio <= manual_cali_lower) {
          cali_ratio = manual_cali_lower;
        }
        if (cali_ratio >= manual_cali_upper) {
          cali_ratio = manual_cali_upper;
        }
        if ((!enable_manual_cali_remove_unvalid_tag) ||
            (cali_ratio != 1.0 && cali_ratio > 0)) {
          p_ad->ModifyUnifyInfoLinear(RUnifyType::CTR,
                              p_ad->get_unify_ctr_info().s_type,
                              p_ad->get_unify_ctr_info().e_type,
                              RUnifyTag::GLOBAL_CXR_CALIBRATE,
                              cali_ratio);
        }
        if (cali_ratio != 1.0) {
          RANK_DOT_COUNT(session_data, 1,
            "ad_rank.manualPlugin_cali_ctr_count",
            ocpx_product,
            page_account);
          if (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
            RANK_DOT_COUNT(session_data, 1, "ad_rank.manualPlugin_cali_ctr_count_lsp");
          }
        }
    }
    if (p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC &&
        p_ad->get_ad_source_type() == kuaishou::ad::ADX &&
        enable_adxcpc_model_cali) {
      double adxcpc_cali_weight = 1.0;
      if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
        adxcpc_cali_weight = adxcpc_hard_cali_weight;
      } else if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
        adxcpc_cali_weight = adxcpc_soft_cali_weight;
      }
      p_ad->ModifyUnifyInfoLinear(RUnifyType::CTR,
                          p_ad->get_unify_ctr_info().s_type,
                          p_ad->get_unify_ctr_info().e_type,
                          RUnifyTag::GLOBAL_CXR_CALIBRATE,
                          adxcpc_cali_weight);
    }
    cali_ratio = 1.0;
    if (enable_cvr_manual_cali) {
        auto iter_account = cvr_cali_manual_config.account_ratio().find(account);
        if (iter_account != cvr_cali_manual_config.account_ratio().end() && 0 < iter_account->second) {
            cali_ratio *= iter_account->second;
        }
        auto iter_product = cvr_cali_manual_config.product_ratio().find(product);
        if (iter_product != cvr_cali_manual_config.product_ratio().end() && 0 < iter_product->second) {
            cali_ratio *= iter_product->second;
        }
        auto iter_ocpx_account = cvr_cali_manual_config.ocpx_account_ratio().find(ocpx_account);
        if (iter_ocpx_account != cvr_cali_manual_config.ocpx_account_ratio().end() &&
                0 < iter_ocpx_account->second) {
            cali_ratio *= iter_ocpx_account->second;
        }
        auto iter_ocpx_product = cvr_cali_manual_config.ocpx_product_ratio().find(ocpx_product);
        if (iter_ocpx_product != cvr_cali_manual_config.ocpx_product_ratio().end() &&
                0 < iter_ocpx_product->second) {
            cali_ratio *= iter_ocpx_product->second;
        }
        auto iter_page_account = cvr_cali_manual_config.page_account_ratio().find(page_account);
        if (iter_page_account != cvr_cali_manual_config.page_account_ratio().end() &&
                0 < iter_page_account->second) {
            cali_ratio *= iter_page_account->second;
        }
        auto iter_page_product = cvr_cali_manual_config.page_product_ratio().find(page_product);
        if (iter_page_product != cvr_cali_manual_config.page_product_ratio().end() &&
                0 < iter_page_product->second) {
            cali_ratio *= iter_page_product->second;
        }
        auto iter_subpage_account = cvr_cali_manual_config.subpage_account_ratio().find(subpage_account);
        if (iter_subpage_account != cvr_cali_manual_config.subpage_account_ratio().end() &&
                0 < iter_subpage_account->second) {
            cali_ratio *= iter_subpage_account->second;
        }
        auto iter_subpage_product = cvr_cali_manual_config.subpage_product_ratio().find(subpage_product);
        if (iter_subpage_product != cvr_cali_manual_config.subpage_product_ratio().end() &&
                0 < iter_subpage_product->second) {
            cali_ratio *= iter_subpage_product->second;
        }
        auto iter_queue_account = cvr_cali_manual_config.queue_account_ratio().find(queue_account);
        if (iter_queue_account != cvr_cali_manual_config.queue_account_ratio().end() &&
                0 < iter_queue_account->second) {
            cali_ratio *= iter_queue_account->second;
        }
        auto iter_queue_product = cvr_cali_manual_config.queue_product_ratio().find(queue_product);
        if (iter_queue_product != cvr_cali_manual_config.queue_product_ratio().end() &&
                0 < iter_queue_product->second) {
            cali_ratio *= iter_queue_product->second;
        }
        auto iter_campaign_account = cvr_cali_manual_config.campaign_account_ratio().find(campaign_account);
        if (iter_campaign_account != cvr_cali_manual_config.campaign_account_ratio().end() &&
                0 < iter_campaign_account->second) {
            cali_ratio *= iter_campaign_account->second;
        }
        auto iter_campaign_product = cvr_cali_manual_config.campaign_product_ratio().find(campaign_product);
        if (iter_campaign_product != cvr_cali_manual_config.campaign_product_ratio().end() &&
                0 < iter_campaign_product->second) {
            cali_ratio *= iter_campaign_product->second;
        }
        if (enable_manual_cali_add_creativetype) {
          cali_ratio *= cal_cali_rate(
            cvr_cali_manual_config.creativetype_bidtype_ocpx_product_ratio(),
            cvr_cali_manual_config.creativetype_bidtype_ocpx_account_ratio(),
            cvr_cali_manual_config.creativetype_bidtype_ocpx_deepconv_product_ratio(),
            cvr_cali_manual_config.creativetype_bidtype_ocpx_deepconv_account_ratio(),
            cvr_cali_manual_config.itemtype_product_ratio(),
            cvr_cali_manual_config.itemtype_account_ratio());
        }
        auto iter_account_campaign =
            cvr_cali_manual_config.account_campaign_ratio().find(account_campaign);
        if (iter_account_campaign != cvr_cali_manual_config.account_campaign_ratio().end() &&
                0 < iter_account_campaign->second) {
            cali_ratio *= iter_account_campaign->second;
        }
        auto iter_account_campaign_page =
            cvr_cali_manual_config.account_campaign_page_ratio().find(account_campaign_page);
        if (iter_account_campaign_page != cvr_cali_manual_config.account_campaign_page_ratio().end() &&
                0 < iter_account_campaign_page->second) {
            cali_ratio *= iter_account_campaign_page->second;
        }
        auto iter_account_ocpx =
            cvr_cali_manual_config.account_ocpx_ratio().find(account_ocpx);
        if (iter_account_ocpx != cvr_cali_manual_config.account_ocpx_ratio().end() &&
                0 < iter_account_ocpx->second) {
            cali_ratio *= iter_account_ocpx->second;
        }
        auto iter_account_ocpx_page =
            cvr_cali_manual_config.account_ocpx_page_ratio().find(account_ocpx_page);
        if (iter_account_ocpx_page != cvr_cali_manual_config.account_ocpx_page_ratio().end() &&
                0 < iter_account_ocpx_page->second) {
            cali_ratio *= iter_account_ocpx_page->second;
        }
        auto iter_account_campaign_ocpc =
            cvr_cali_manual_config.account_campaign_ocpc_ratio().find(account_campaign_ocpc);
        if (iter_account_campaign_ocpc != cvr_cali_manual_config.account_campaign_ocpc_ratio().end() &&
                0 < iter_account_campaign_ocpc->second) {
            cali_ratio *= iter_account_campaign_ocpc->second;
        }
        auto iter_account_campaign_ocpc_page =
            cvr_cali_manual_config.account_campaign_ocpc_page_ratio().find(account_campaign_ocpc_page);
        if (iter_account_campaign_ocpc_page !=
                cvr_cali_manual_config.account_campaign_ocpc_page_ratio().end() &&
                0 < iter_account_campaign_ocpc_page->second) {
            cali_ratio *= iter_account_campaign_ocpc_page->second;
        }
        auto iter_account_str = cvr_cali_manual_config.account_str_ratio().find(account_str);
        if (iter_account_str !=
                cvr_cali_manual_config.account_str_ratio().end() &&
                0 < iter_account_str->second) {
            cali_ratio *= iter_account_str->second;
        }
        if (cali_ratio <= manual_cali_lower) {
          cali_ratio = manual_cali_lower;
        }
        if (cali_ratio >= manual_cali_upper) {
          cali_ratio = manual_cali_upper;
        }
        if ((!enable_manual_cali_remove_unvalid_tag) ||
            (cali_ratio != 1.0 && cali_ratio > 0)) {
          p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                              p_ad->get_unify_cvr_info().s_type,
                              p_ad->get_unify_cvr_info().e_type,
                              RUnifyTag::GLOBAL_CXR_CALIBRATE,
                              cali_ratio);
        }
        if (cali_ratio != 1.0) {
          RANK_DOT_COUNT(session_data, 1,
            "ad_rank.manualPlugin_cali_cvr_count",
            ocpx_product,
            page_account);
          if (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
            RANK_DOT_COUNT(session_data, 1, "ad_rank.manualPlugin_cali_cvr_count_lsp");
          }
        }
    }
    cali_ratio = 1.0;
    if (enable_dcvr_manual_cali) {
        auto iter_account = dcvr_cali_manual_config.account_ratio().find(account);
        if (iter_account != dcvr_cali_manual_config.account_ratio().end() && 0 < iter_account->second) {
            cali_ratio *= iter_account->second;
        }
        auto iter_product = dcvr_cali_manual_config.product_ratio().find(product);
        if (iter_product != dcvr_cali_manual_config.product_ratio().end() && 0 < iter_product->second) {
            cali_ratio *= iter_product->second;
        }
        auto iter_ocpx_account = dcvr_cali_manual_config.ocpx_account_ratio().find(ocpx_account);
        if (iter_ocpx_account != dcvr_cali_manual_config.ocpx_account_ratio().end() &&
                0 < iter_ocpx_account->second) {
            cali_ratio *= iter_ocpx_account->second;
        }
        auto iter_ocpx_product = dcvr_cali_manual_config.ocpx_product_ratio().find(ocpx_product);
        if (iter_ocpx_product != dcvr_cali_manual_config.ocpx_product_ratio().end() &&
                0 < iter_ocpx_product->second) {
            cali_ratio *= iter_ocpx_product->second;
        }
        auto iter_page_account = dcvr_cali_manual_config.page_account_ratio().find(page_account);
        if (iter_page_account != dcvr_cali_manual_config.page_account_ratio().end() &&
                0 < iter_page_account->second) {
            cali_ratio *= iter_page_account->second;
        }
        auto iter_page_product = dcvr_cali_manual_config.page_product_ratio().find(page_product);
        if (iter_page_product != dcvr_cali_manual_config.page_product_ratio().end() &&
                0 < iter_page_product->second) {
            cali_ratio *= iter_page_product->second;
        }
        auto iter_subpage_account = dcvr_cali_manual_config.subpage_account_ratio().find(subpage_account);
        if (iter_subpage_account != dcvr_cali_manual_config.subpage_account_ratio().end() &&
                0 < iter_subpage_account->second) {
            cali_ratio *= iter_subpage_account->second;
        }
        auto iter_subpage_product = dcvr_cali_manual_config.subpage_product_ratio().find(subpage_product);
        if (iter_subpage_product != dcvr_cali_manual_config.subpage_product_ratio().end() &&
                0 < iter_subpage_product->second) {
            cali_ratio *= iter_subpage_product->second;
        }
        auto iter_page_account_ocpx_deepconv =
              dcvr_cali_manual_config.page_account_ocpx_deepconv_ratio().find(page_account_ocpx_deepconv);
        if (iter_page_account_ocpx_deepconv !=
              dcvr_cali_manual_config.page_account_ocpx_deepconv_ratio().end() &&
                0 < iter_page_account_ocpx_deepconv->second) {
            cali_ratio *= iter_page_account_ocpx_deepconv->second;
        }
        auto iter_page_product_ocpx_deepconv =
              dcvr_cali_manual_config.page_product_ocpx_deepconv_ratio().find(page_product_ocpx_deepconv);
        if (iter_page_product_ocpx_deepconv !=
              dcvr_cali_manual_config.page_product_ocpx_deepconv_ratio().end() &&
                0 < iter_page_product_ocpx_deepconv->second) {
            cali_ratio *= iter_page_product_ocpx_deepconv->second;
        }
        auto iter_queue_account = dcvr_cali_manual_config.queue_account_ratio().find(queue_account);
        if (iter_queue_account != dcvr_cali_manual_config.queue_account_ratio().end() &&
                0 < iter_queue_account->second) {
            cali_ratio *= iter_queue_account->second;
        }
        auto iter_queue_product = dcvr_cali_manual_config.queue_product_ratio().find(queue_product);
        if (iter_queue_product != dcvr_cali_manual_config.queue_product_ratio().end() &&
                0 < iter_queue_product->second) {
            cali_ratio *= iter_queue_product->second;
        }
        auto iter_campaign_account = dcvr_cali_manual_config.campaign_account_ratio().find(campaign_account);
        if (iter_campaign_account != dcvr_cali_manual_config.campaign_account_ratio().end() &&
                0 < iter_campaign_account->second) {
            cali_ratio *= iter_campaign_account->second;
        }
        auto iter_campaign_product = dcvr_cali_manual_config.campaign_product_ratio().find(campaign_product);
        if (iter_campaign_product != dcvr_cali_manual_config.campaign_product_ratio().end() &&
                0 < iter_campaign_product->second) {
            cali_ratio *= iter_campaign_product->second;
        }
        if (enable_manual_cali_add_creativetype) {
          cali_ratio *= cal_cali_rate(
            dcvr_cali_manual_config.creativetype_bidtype_ocpx_product_ratio(),
            dcvr_cali_manual_config.creativetype_bidtype_ocpx_account_ratio(),
            dcvr_cali_manual_config.creativetype_bidtype_ocpx_deepconv_product_ratio(),
            dcvr_cali_manual_config.creativetype_bidtype_ocpx_deepconv_account_ratio(),
            dcvr_cali_manual_config.itemtype_product_ratio(),
            dcvr_cali_manual_config.itemtype_account_ratio());
        }
        if (cali_ratio <= manual_cali_lower) {
          cali_ratio = manual_cali_lower;
        }
        if (cali_ratio >= manual_cali_upper) {
          cali_ratio = manual_cali_upper;
        }
        if ((!enable_manual_cali_remove_unvalid_tag) ||
            (cali_ratio != 1.0 && cali_ratio > 0)) {
          p_ad->ModifyUnifyInfoLinear(RUnifyType::DEEP_CVR,
                              p_ad->get_unify_deep_cvr_info().s_type,
                              p_ad->get_unify_deep_cvr_info().e_type,
                              RUnifyTag::GLOBAL_CXR_CALIBRATE,
                              cali_ratio,
                              0.0, 2000.0, 0.0);
        }
        if (cali_ratio != 1.0) {
          RANK_DOT_COUNT(session_data, 1,
            "ad_rank.manualPlugin_cali_dcvr_count",
            ocpx_product,
            page_account);
          if (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
            RANK_DOT_COUNT(session_data, 1, "ad_rank.manualPlugin_cali_dcvr_count_lsp");
          }
        }
    }
    cali_ratio = 1.0;
    if (enable_ltv_manual_cali) {
        auto iter_account = ltv_cali_manual_config.account_ratio().find(account);
        if (iter_account != ltv_cali_manual_config.account_ratio().end() && 0 < iter_account->second) {
            cali_ratio *= iter_account->second;
        }
        auto iter_product = ltv_cali_manual_config.product_ratio().find(product);
        if (iter_product != ltv_cali_manual_config.product_ratio().end() && 0 < iter_product->second) {
            cali_ratio *= iter_product->second;
        }
        auto iter_ocpx_account = ltv_cali_manual_config.ocpx_account_ratio().find(ocpx_account);
        if (iter_ocpx_account != ltv_cali_manual_config.ocpx_account_ratio().end() &&
                0 < iter_ocpx_account->second) {
            cali_ratio *= iter_ocpx_account->second;
        }
        auto iter_ocpx_product = ltv_cali_manual_config.ocpx_product_ratio().find(ocpx_product);
        if (iter_ocpx_product != ltv_cali_manual_config.ocpx_product_ratio().end() &&
                0 < iter_ocpx_product->second) {
            cali_ratio *= iter_ocpx_product->second;
        }
        auto iter_page_account = ltv_cali_manual_config.page_account_ratio().find(page_account);
        if (iter_page_account != ltv_cali_manual_config.page_account_ratio().end() &&
                0 < iter_page_account->second) {
            cali_ratio *= iter_page_account->second;
        }
        auto iter_page_product = ltv_cali_manual_config.page_product_ratio().find(page_product);
        if (iter_page_product != ltv_cali_manual_config.page_product_ratio().end() &&
                0 < iter_page_product->second) {
            cali_ratio *= iter_page_product->second;
        }
        auto iter_subpage_account = ltv_cali_manual_config.subpage_account_ratio().find(subpage_account);
        if (iter_subpage_account != ltv_cali_manual_config.subpage_account_ratio().end() &&
                0 < iter_subpage_account->second) {
            cali_ratio *= iter_subpage_account->second;
        }
        auto iter_subpage_product = ltv_cali_manual_config.subpage_product_ratio().find(subpage_product);
        if (iter_subpage_product != ltv_cali_manual_config.subpage_product_ratio().end() &&
                0 < iter_subpage_product->second) {
            cali_ratio *= iter_subpage_product->second;
        }
        auto iter_page_account_ocpx_deepconv =
              ltv_cali_manual_config.page_account_ocpx_deepconv_ratio().find(page_account_ocpx_deepconv);
        if (iter_page_account_ocpx_deepconv !=
              ltv_cali_manual_config.page_account_ocpx_deepconv_ratio().end() &&
                0 < iter_page_account_ocpx_deepconv->second) {
            cali_ratio *= iter_page_account_ocpx_deepconv->second;
        }
        auto iter_page_product_ocpx_deepconv =
              ltv_cali_manual_config.page_product_ocpx_deepconv_ratio().find(page_product_ocpx_deepconv);
        if (iter_page_product_ocpx_deepconv !=
              ltv_cali_manual_config.page_product_ocpx_deepconv_ratio().end() &&
                0 < iter_page_product_ocpx_deepconv->second) {
            cali_ratio *= iter_page_product_ocpx_deepconv->second;
        }
        auto iter_queue_account = ltv_cali_manual_config.queue_account_ratio().find(queue_account);
        if (iter_queue_account != ltv_cali_manual_config.queue_account_ratio().end() &&
                0 < iter_queue_account->second) {
            cali_ratio *= iter_queue_account->second;
        }
        auto iter_queue_product = ltv_cali_manual_config.queue_product_ratio().find(queue_product);
        if (iter_queue_product != ltv_cali_manual_config.queue_product_ratio().end() &&
                0 < iter_queue_product->second) {
            cali_ratio *= iter_queue_product->second;
        }
        auto iter_campaign_account = ltv_cali_manual_config.campaign_account_ratio().find(campaign_account);
        if (iter_campaign_account != ltv_cali_manual_config.campaign_account_ratio().end() &&
                0 < iter_campaign_account->second) {
            cali_ratio *= iter_campaign_account->second;
        }
        auto iter_campaign_product = ltv_cali_manual_config.campaign_product_ratio().find(campaign_product);
        if (iter_campaign_product != ltv_cali_manual_config.campaign_product_ratio().end() &&
                0 < iter_campaign_product->second) {
            cali_ratio *= iter_campaign_product->second;
        }
        if (enable_manual_cali_add_creativetype) {
          cali_ratio *= cal_cali_rate(
            ltv_cali_manual_config.creativetype_bidtype_ocpx_product_ratio(),
            ltv_cali_manual_config.creativetype_bidtype_ocpx_account_ratio(),
            ltv_cali_manual_config.creativetype_bidtype_ocpx_deepconv_product_ratio(),
            ltv_cali_manual_config.creativetype_bidtype_ocpx_deepconv_account_ratio(),
            ltv_cali_manual_config.itemtype_product_ratio(),
            ltv_cali_manual_config.itemtype_account_ratio());
        }
        auto iter_account_campaign =
            ltv_cali_manual_config.account_campaign_ratio().find(account_campaign);
        if (iter_account_campaign != ltv_cali_manual_config.account_campaign_ratio().end() &&
                0 < iter_account_campaign->second) {
            cali_ratio *= iter_account_campaign->second;
        }
        auto iter_account_campaign_page =
            ltv_cali_manual_config.account_campaign_page_ratio().find(account_campaign_page);
        if (iter_account_campaign_page != ltv_cali_manual_config.account_campaign_page_ratio().end() &&
                0 < iter_account_campaign_page->second) {
            cali_ratio *= iter_account_campaign_page->second;
        }
        auto iter_account_ocpx =
            ltv_cali_manual_config.account_ocpx_ratio().find(account_ocpx);
        if (iter_account_ocpx != ltv_cali_manual_config.account_ocpx_ratio().end() &&
                0 < iter_account_ocpx->second) {
            cali_ratio *= iter_account_ocpx->second;
        }
        auto iter_account_ocpx_page =
            ltv_cali_manual_config.account_ocpx_page_ratio().find(account_ocpx_page);
        if (iter_account_ocpx_page != ltv_cali_manual_config.account_ocpx_page_ratio().end() &&
                0 < iter_account_ocpx_page->second) {
            cali_ratio *= iter_account_ocpx_page->second;
        }
        auto iter_account_campaign_ocpc =
            ltv_cali_manual_config.account_campaign_ocpc_ratio().find(account_campaign_ocpc);
        if (iter_account_campaign_ocpc != ltv_cali_manual_config.account_campaign_ocpc_ratio().end() &&
                0 < iter_account_campaign_ocpc->second) {
            cali_ratio *= iter_account_campaign_ocpc->second;
        }
        auto iter_account_campaign_ocpc_page =
            ltv_cali_manual_config.account_campaign_ocpc_page_ratio().find(account_campaign_ocpc_page);
        if (iter_account_campaign_ocpc_page !=
                ltv_cali_manual_config.account_campaign_ocpc_page_ratio().end() &&
                0 < iter_account_campaign_ocpc_page->second) {
            cali_ratio *= iter_account_campaign_ocpc_page->second;
        }
        auto iter_account_str = ltv_cali_manual_config.account_str_ratio().find(account_str);
        if (iter_account_str !=
                ltv_cali_manual_config.account_str_ratio().end() &&
                0 < iter_account_str->second) {
            cali_ratio *= iter_account_str->second;
        }
        if (cali_ratio <= manual_cali_lower) {
          cali_ratio = manual_cali_lower;
        }
        if (cali_ratio >= manual_cali_upper) {
          cali_ratio = manual_cali_upper;
        }
        if ((!enable_manual_cali_remove_unvalid_tag) ||
            (cali_ratio != 1.0 && cali_ratio > 0)) {
          p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                              p_ad->get_unify_ltv_info().s_type,
                              p_ad->get_unify_ltv_info().e_type,
                              RUnifyTag::GLOBAL_CXR_CALIBRATE,
                              cali_ratio,
                              0.0, 100000.0, 0.0);
        }
        if (cali_ratio != 1.0) {
          RANK_DOT_COUNT(session_data, 1,
            "ad_rank.manualPlugin_cali_ltv_count",
            ocpx_product,
            page_account);
          if (p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_LSP) {
            RANK_DOT_COUNT(session_data, 1, "ad_rank.manualPlugin_cali_ltv_count_lsp");
          }
        }
    }

    if (enable_mini_game_iaap_calibration_strategy_v2 && p_ad->Is(AdFlag::is_iaap_game_ad)) {
      // 校准
      double upper = 2000.0;
      double lower = 0.0;
      double ltv_w = 1.0;
      double ltv_bias = 0.0;
      if (p_ad->get_optimal_subsidy_treatment() == 1) {
        ltv_w = params_->subsidy_calibrate_factor_w;
        ltv_bias = params_->subsidy_calibrate_factor_b;
      } else {
        ltv_w = params_->no_subsidy_calibrate_factor_w;
        ltv_bias = params_->no_subsidy_calibrate_factor_b;
      }
      p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, p_ad->get_unify_ltv_info().s_type,
                                  p_ad->get_unify_ltv_info().e_type, RUnifyTag::AD_IAAP_LTV_CALIBRATE,
                                  ltv_w, ltv_bias, upper, lower);
    }
  // 大游戏 首 R 低分段校准
  auto big_game_sdk_white_conf_first_r = RankKconfUtil::bigGameSdkWhiteConf();
  if (enable_big_game_first_r_calibration_strategy &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) &&
            p_ad->get_first_industry_id_v5() == 1018 &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP &&
            big_game_sdk_white_conf_first_r &&
            big_game_sdk_white_conf_first_r->count(p_ad->get_product_name())) {
    double upper = 2000.0;
    double lower = 0.0;
    double is_pay_w = 1.0;
    double is_pay_bias = 0.0;
    const auto& game_iap_ltv1_deprioritize_config  = RankKconfUtil::gameIapLtv7DeprioritizeConfig();
    std::string game_ltv1_deprioritize_tag =
      SPDM_game_iap_ltv1_deprioritize_exp_tag(session_data->get_spdm_ctx());
    int64_t game_product_is_pay_1_ema_req_threshold = 500;  // 预热期样本数
    double game_product_is_pay_1_ema_decay_weight = 0.99;  // ema 衰减系数
    double game_deep_avg_pacing_upper_bound = 1.0;  // 上界比例
    double game_deep_avg_pacing_lower_bound = 0.001;  // 下界比例
    double game_is_pay_1_std_scale_ratio = 1.0;  // 方差缩放系数
    // 无补贴头
    double pred_big_game_is_pay_1 = p_ad->get_predict_score(PredictType::PredictType_mini_game_iap_pay);
    // 动态调整
    if (game_iap_ltv1_deprioritize_config != nullptr) {
      auto match_exp_list =
          game_iap_ltv1_deprioritize_config->data().exp_list().find(game_ltv1_deprioritize_tag);
      if (match_exp_list != game_iap_ltv1_deprioritize_config->data().exp_list().end()) {
        game_product_is_pay_1_ema_req_threshold = match_exp_list->second.req_threshold();
        game_product_is_pay_1_ema_decay_weight = match_exp_list->second.decay_weight();
        game_deep_avg_pacing_upper_bound = match_exp_list->second.upper_bound();
        game_deep_avg_pacing_lower_bound = match_exp_list->second.lower_bound();
        game_is_pay_1_std_scale_ratio = match_exp_list->second.scale_ratio();
      }
    }
    double pred_is_pay_1_moving_avg = pred_big_game_is_pay_1;
    double pred_is_pay_1_std_moving_std = 1e-6;
    pred_is_pay_1_moving_avg = ks::ad_rank::RankingData::GetInstance()->GetGameIapProuctLtv1AvgMoving(
        p_ad->get_product_name(),
        pred_big_game_is_pay_1,
        game_product_is_pay_1_ema_decay_weight,
        game_product_is_pay_1_ema_req_threshold);
    pred_is_pay_1_std_moving_std = ks::ad_rank::RankingData::GetInstance()->GetGameIapProuctLtv1StdMoving(
        p_ad->get_product_name(),
        pred_big_game_is_pay_1,
        game_product_is_pay_1_ema_req_threshold);
    double ratio = 1.0;
    if (pred_is_pay_1_std_moving_std > 0 && game_is_pay_1_std_scale_ratio > 0) {
      // 只对均值下扰动
      if (pred_big_game_is_pay_1 < pred_is_pay_1_moving_avg) {
        ratio = (pred_big_game_is_pay_1 - pred_is_pay_1_moving_avg) /
                            (3 * pred_is_pay_1_std_moving_std * game_is_pay_1_std_scale_ratio) + 1.0;
        is_pay_w = std::min(std::max(ratio, game_deep_avg_pacing_lower_bound),
                            game_deep_avg_pacing_upper_bound);
      }
    }
    double ltv_before = p_ad->get_unify_ltv_info().value;
    RANK_DOT_STATS(session_data, pred_big_game_is_pay_1 * 1000, "pred_big_game_is_pay_1_tag");
    RANK_DOT_STATS(session_data, pred_is_pay_1_moving_avg * 1000, "pred_big_game_is_pay_1_moving_avg_tag");
    RANK_DOT_STATS(session_data, pred_is_pay_1_std_moving_std * 1000, "pred_big_game_is_pay_1_moving_std");
    RANK_DOT_STATS(session_data, ratio * 1000, "big_game_1r_ltv_deprioritize_ratio");
    RANK_DOT_STATS(session_data, is_pay_w * 1000, "big_game_1r_ltv_deprioritize_ratio_w");
    RANK_DOT_STATS(session_data, is_pay_bias * 1000, "big_game_1r_ltv_deprioritize_ratio_bias");
    RANK_DOT_STATS(session_data, game_product_is_pay_1_ema_req_threshold, "big_game_product_req_threshold");
    RANK_DOT_STATS(session_data, ltv_before * 1000, "big_game_1r_ltv_deprioritize_ltv_before");
    p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, p_ad->get_unify_ltv_info().s_type,
                      p_ad->get_unify_ltv_info().e_type, RUnifyTag::AD_BIG_GAME_1R_LTV_DEPRIORITIZE,
                      is_pay_w, is_pay_bias, upper, lower);
    double ltv_after = p_ad->get_unify_ltv_info().value;
    RANK_DOT_STATS(session_data, ltv_after * 1000, "big_game_1r_ltv_deprioritize_ltv_after");
  }
  // iap 首 R 低分段校准
  if (enable_game_first_r_calibration_strategy && p_ad->Is(AdFlag::is_iaap_game_ad)) {
    double upper = 2000.0;
    double lower = 0.0;
    double is_pay_w = 1.0;
    double is_pay_bias = 0.0;
    const auto& game_iap_ltv1_deprioritize_config  = RankKconfUtil::gameIapLtv7DeprioritizeConfig();
    std::string game_ltv1_deprioritize_tag =
      SPDM_game_iap_ltv1_deprioritize_exp_tag(session_data->get_spdm_ctx());
    int64_t game_iap_product_is_pay_1_ema_req_threshold = 500;  // 预热期样本数
    double game_iap_product_is_pay_1_ema_decay_weight = 0.99;  // ema 衰减系数
    double game_iap_deep_avg_pacing_upper_bound = 1.0;  // 上界比例
    double game_iap_deep_avg_pacing_lower_bound = 0.001;  // 下界比例
    double game_iap_is_pay_1_std_scale_ratio = 1.0;  // 方差缩放系数
    // 无补贴头
    double pred_is_pay_1 = p_ad->get_predict_score(PredictType::PredictType_outer_uplift_conv_pay_base);
    // 动态调整
    if (game_iap_ltv1_deprioritize_config != nullptr) {
      auto match_exp_list =
          game_iap_ltv1_deprioritize_config->data().exp_list().find(game_ltv1_deprioritize_tag);
      if (match_exp_list != game_iap_ltv1_deprioritize_config->data().exp_list().end()) {
        game_iap_product_is_pay_1_ema_req_threshold = match_exp_list->second.req_threshold();
        game_iap_product_is_pay_1_ema_decay_weight = match_exp_list->second.decay_weight();
        game_iap_deep_avg_pacing_upper_bound = match_exp_list->second.upper_bound();
        game_iap_deep_avg_pacing_lower_bound = match_exp_list->second.lower_bound();
        game_iap_is_pay_1_std_scale_ratio = match_exp_list->second.scale_ratio();
      }
    }
    double pred_is_pay_1_moving_avg = pred_is_pay_1;
    double pred_is_pay_1_std_moving_std = 1e-6;
    pred_is_pay_1_moving_avg = ks::ad_rank::RankingData::GetInstance()->GetGameIapProuctLtv1AvgMoving(
        p_ad->get_product_name(),
        pred_is_pay_1,
        game_iap_product_is_pay_1_ema_decay_weight,
        game_iap_product_is_pay_1_ema_req_threshold);
    pred_is_pay_1_std_moving_std = ks::ad_rank::RankingData::GetInstance()->GetGameIapProuctLtv1StdMoving(
        p_ad->get_product_name(),
        pred_is_pay_1,
        game_iap_product_is_pay_1_ema_req_threshold);
    double ratio = 1.0;
    if (pred_is_pay_1_std_moving_std > 0 && game_iap_is_pay_1_std_scale_ratio > 0) {
      // 只对均值下扰动
      if (pred_is_pay_1 < pred_is_pay_1_moving_avg) {
        ratio = (pred_is_pay_1 - pred_is_pay_1_moving_avg) /
                            (3 * pred_is_pay_1_std_moving_std * game_iap_is_pay_1_std_scale_ratio) + 1.0;
        is_pay_w = std::min(std::max(ratio, game_iap_deep_avg_pacing_lower_bound),
                                                game_iap_deep_avg_pacing_upper_bound);
      }
    }
    double ltv_before = p_ad->get_unify_ltv_info().value;
    RANK_DOT_STATS(session_data, pred_is_pay_1 * 1000, "pred_is_pay_1_tag");
    RANK_DOT_STATS(session_data, pred_is_pay_1_moving_avg * 1000, "pred_is_pay_1_moving_avg_tag");
    RANK_DOT_STATS(session_data, pred_is_pay_1_std_moving_std * 1000, "pred_is_pay_1_std_moving_std_tag");
    RANK_DOT_STATS(session_data, ratio * 1000, "game_iap_1r_ltv_deprioritize_ratio");
    RANK_DOT_STATS(session_data, is_pay_w * 1000, "game_iap_1r_ltv_deprioritize_ratio_w");
    RANK_DOT_STATS(session_data, is_pay_bias * 1000, "game_iap_1r_ltv_deprioritize_ratio_bias");
    RANK_DOT_STATS(session_data, game_iap_product_is_pay_1_ema_req_threshold, "game_product_req_threshold");
    RANK_DOT_STATS(session_data, ltv_before * 1000, "game_iap_1r_ltv_deprioritize_ltv_before");
    p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, p_ad->get_unify_ltv_info().s_type,
                      p_ad->get_unify_ltv_info().e_type, RUnifyTag::AD_GAME_IAP_1R_LTV_DEPRIORITIZE,
                      is_pay_w, is_pay_bias, upper, lower);
    double ltv_after = p_ad->get_unify_ltv_info().value;
    RANK_DOT_STATS(session_data, ltv_after * 1000, "game_iap_1r_ltv_deprioritize_ltv_after");
  }
    // iap && 大游戏 7R 低分段校准
    auto big_game_sdk_white_conf = RankKconfUtil::bigGameSdkWhiteConf();
    if (enable_mini_game_seven_day_iaap_deprioritize_strategy && p_ad->Is(AdFlag::is_iap_7r_game_ad)||
        (enable_game_seven_day_deprioritize_strategy &&
                                   (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS &&
                                    p_ad->get_first_industry_id_v5() == 1018 &&
                                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP &&
                                    big_game_sdk_white_conf &&
                                    big_game_sdk_white_conf->count(p_ad->get_product_name())))) {
      double upper = 2000.0;
      double lower = 0.0;
      double is_pay_w = 1.0;
      double is_pay_bias = 0.0;
      double alpha = 1.0;
      double beta = 0.0;
      const auto& game_iap_ltv7_deprioritize_config  = RankKconfUtil::gameIapLtv7DeprioritizeConfig();
      std::string game_ltv7_deprioritize_tag =
        SPDM_game_iap_ltv7_deprioritize_exp_tag(session_data->get_spdm_ctx());
      int64_t game_iap_product_is_pay_7_ema_req_threshold = 500;  // 预热期样本数
      double game_iap_product_is_pay_7_ema_decay_weight = 0.99;  // ema 衰减系数
      double game_iap_deep_avg_pacing_upper_bound = 1.0;  // 上界比例
      double game_iap_deep_avg_pacing_lower_bound = 0.001;  // 下界比例
      double game_iap_is_pay_7_std_scale_ratio = 1.0;  // 方差缩放系数
      double pred_is_pay_7 = p_ad->get_predict_score(PredictType::PredictType_outer_game_conv_pay_7);
      // 动态调整
      if (game_iap_ltv7_deprioritize_config != nullptr) {
        auto match_exp_list =
            game_iap_ltv7_deprioritize_config->data().exp_list().find(game_ltv7_deprioritize_tag);
        if (match_exp_list != game_iap_ltv7_deprioritize_config->data().exp_list().end()) {
          game_iap_product_is_pay_7_ema_req_threshold = match_exp_list->second.req_threshold();
          game_iap_product_is_pay_7_ema_decay_weight = match_exp_list->second.decay_weight();
          game_iap_deep_avg_pacing_upper_bound = match_exp_list->second.upper_bound();
          game_iap_deep_avg_pacing_lower_bound = match_exp_list->second.lower_bound();
          game_iap_is_pay_7_std_scale_ratio = match_exp_list->second.scale_ratio();
        }
      }
      alpha = params_->iap_game_7r_is_pay_calibrate_factor_w;
      beta = params_->iap_game_7r_is_pay_calibrate_factor_b;
      double pred_is_pay_7_moving_avg = pred_is_pay_7;
      double pred_is_pay_7_std_moving_std = 1e-6;
      pred_is_pay_7_moving_avg = ks::ad_rank::RankingData::GetInstance()->GetGameIapProuctLtv7AvgMoving(
          p_ad->get_product_name(),
          pred_is_pay_7,
          game_iap_product_is_pay_7_ema_decay_weight,
          game_iap_product_is_pay_7_ema_req_threshold);
      pred_is_pay_7_std_moving_std = ks::ad_rank::RankingData::GetInstance()->GetGameIapProuctLtv7StdMoving(
          p_ad->get_product_name(),
          pred_is_pay_7,
          game_iap_product_is_pay_7_ema_req_threshold);
      double ratio = 1.0;
      if (pred_is_pay_7_std_moving_std > 0 && game_iap_is_pay_7_std_scale_ratio > 0) {
        // 只对均值下扰动
        if (pred_is_pay_7 < pred_is_pay_7_moving_avg) {
          ratio = (pred_is_pay_7 - pred_is_pay_7_moving_avg) /
                              (3 * pred_is_pay_7_std_moving_std * game_iap_is_pay_7_std_scale_ratio) + 1.0;
          is_pay_w = std::min(std::max(ratio * alpha + beta, game_iap_deep_avg_pacing_lower_bound),
                                                  game_iap_deep_avg_pacing_upper_bound);
          is_pay_bias = beta;
        }
      }
      RANK_DOT_STATS(session_data, pred_is_pay_7 * 1000, "pred_is_pay_7_tag");
      RANK_DOT_STATS(session_data, pred_is_pay_7_moving_avg * 1000, "pred_is_pay_7_moving_avg_tag");
      RANK_DOT_STATS(session_data, pred_is_pay_7_std_moving_std * 1000, "pred_is_pay_7_std_moving_std_tag");
      RANK_DOT_STATS(session_data, ratio * 1000, "game_iap_7r_ltv_deprioritize_ratio");
      RANK_DOT_STATS(session_data, is_pay_w * 1000, "game_iap_7r_ltv_deprioritize_ratio_w");
      RANK_DOT_STATS(session_data, is_pay_bias * 1000, "game_iap_7r_ltv_deprioritize_ratio_bias");
      RANK_DOT_STATS(session_data, game_iap_product_is_pay_7_ema_req_threshold, "game_product_req_threshold");
      p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, p_ad->get_unify_ltv_info().s_type,
                        p_ad->get_unify_ltv_info().e_type, RUnifyTag::AD_GAME_IAP_7R_LTV_DEPRIORITIZE,
                        is_pay_w, is_pay_bias, upper, lower);
    }

    if (enable_mini_game_seven_day_iaap_calibration_strategy && p_ad->Is(AdFlag::is_iap_7r_game_ad)) {
      // 校准
      double upper = 2000.0;
      double lower = 0.0;
      double ltv_w = 1.0;
      double ltv_bias = 0.0;
      ltv_w = params_->iap_game_7r_ltv_calibrate_factor_w;
      ltv_bias = params_->iap_game_7r_ltv_calibrate_factor_b;
      p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, p_ad->get_unify_ltv_info().s_type,
                                  p_ad->get_unify_ltv_info().e_type, RUnifyTag::AD_GAME_IAP_7R_LTV_CALIBRATE,
                                  ltv_w, ltv_bias, upper, lower);
    }



    if (enable_game_iap_7r_ltv_adjust && p_ad->Is(AdFlag::is_iap_7r_game_ad)||
        (enable_game_sdk_7r_ltv_adjust &&
                                   (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS &&
                                    p_ad->get_first_industry_id_v5() == 1018 &&
                                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP &&
                                    big_game_sdk_white_conf &&
                                    big_game_sdk_white_conf->count(p_ad->get_product_name())))) {
     double bias = 0.0;
     double upper = 2000.0;
     double lower = 0.0;
     if (enable_game_iap_7r_ltv_adjust && p_ad->Is(AdFlag::is_iap_7r_game_ad)) {
      double w = game_iap_7r_ltv_coef;
      auto ltv_before = p_ad->get_unify_ltv_info().value;

      p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, p_ad->get_unify_ltv_info().s_type,
      p_ad->get_unify_ltv_info().e_type, RUnifyTag::AD_GAME_IAP_7R_LTV_ADJUST,
      w, bias, upper, lower);

      auto ltv_after = p_ad->get_unify_ltv_info().value;
      std::string iap_7r_adjust_tag = absl::StrCat("iap_7r_adjust_", game_iap_7r_ltv_adjust_tag);
      RANK_DOT_STATS(session_data, ltv_before * 1000, iap_7r_adjust_tag, "ltv_ori");
      RANK_DOT_STATS(session_data, ltv_after * 1000, iap_7r_adjust_tag, "ltv_adjust");
    }
    if (enable_game_sdk_7r_ltv_adjust &&
      (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS &&
       p_ad->get_first_industry_id_v5() == 1018 &&
       p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP &&
       big_game_sdk_white_conf &&
       big_game_sdk_white_conf->count(p_ad->get_product_name()))) {
          double w = game_sdk_7r_ltv_coef;
          auto ltv_before = p_ad->get_unify_ltv_info().value;

          p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, p_ad->get_unify_ltv_info().s_type,
          p_ad->get_unify_ltv_info().e_type, RUnifyTag::AD_GAME_SDK_7R_LTV_ADJUST,
          w, bias, upper, lower);
          auto ltv_after = p_ad->get_unify_ltv_info().value;
          std::string sdk_7r_adjust_tag = absl::StrCat("sdk_7r_adjust_", game_sdk_7r_ltv_adjust_tag);
          RANK_DOT_STATS(session_data, ltv_before * 1000, sdk_7r_adjust_tag, "ltv_ori");
          RANK_DOT_STATS(session_data, ltv_after * 1000, sdk_7r_adjust_tag, "ltv_adjust");
       }
    }

    if (enable_credit_grant_soft_cali &&
        weight_credit_grant_soft_cali > 0 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT &&
        p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
          p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                              p_ad->get_unify_cvr_info().s_type,
                              p_ad->get_unify_cvr_info().e_type,
                              RUnifyTag::GLOBAL_CXR_CALIBRATE,
                              weight_credit_grant_soft_cali);
    }
  }
  return StraRetCode::SUCC;
}  // NOLINT


const char* ModifyRValuePlugin::Name() {
  return "ModifyRValuePlugin";
}

void ModifyRValuePlugin::Clear() {}

bool ModifyRValuePlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode ModifyRValuePlugin::Process(ContextData* session_data, Params* params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  if (ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (KS_UNLIKELY(params_ == nullptr)) {
    return StraRetCode::SUCC;
  }
  bool disable_useless_calibration_strategy =
            SPDM_disable_useless_calibration_strategy(session_data->get_spdm_ctx());
  const auto &ad_roas_account_modify_rate_map = RankKconfUtil::adRoasAccountModifyRateMap();

  const auto &ad_roas_playlet_calibrate_realtime = *(RankKconfUtil::adRoasDuanjuCalibrateRealtime());
  const auto &ad_playlet_roas_list_realtime = GetCalibrationList(ad_roas_playlet_calibrate_realtime, 2, 4);
  const auto &ad_conv_roas_calibrate = *(RankKconfUtil::adConvRoasCalibrate());
  const auto &ad_conv_roas_list = GetCalibrationList(ad_conv_roas_calibrate, 2, 4);
  const auto &ad_conv_roas_calibrate_mini_app = *(RankKconfUtil::adConvRoasCalibrateMiniApp());
  const auto &ad_conv_roas_list_mini_app = GetCalibrationList(ad_conv_roas_calibrate_mini_app, 2, 4);
  const auto &ad_roas_product_modify_rate_map = RankKconfUtil::adRoasProductModifyRateMap();
  double ad_duanju_purchase_ratio = RankKconfUtil::adDuanjuPurchaseRatio();
  const auto &ad_roas_std_coef = RankKconfUtil::adRoasStdCoef();
  const auto &ad_iaa_std_coef = RankKconfUtil::adIaaStdCoef();
  const auto &ad_roas_calibration_game_upper =
                        SPDM_ad_roas_calibration_game_upper(session_data->get_spdm_ctx());  // NOLINT
  const auto &ad_roas_calibration_game_lower =
                        SPDM_ad_roas_calibration_game_lower(session_data->get_spdm_ctx());  // NOLINT
  const auto &ad_roas_calibration_mini_game_upper =
                        SPDM_ad_roas_calibration_mini_game_upper(session_data->get_spdm_ctx());  // NOLINT
  const auto &ad_roas_calibration_mini_game_lower =
                        SPDM_ad_roas_calibration_mini_game_lower(session_data->get_spdm_ctx());  // NOLINT
  const auto &ad_roas_calibration_upper =
                        SPDM_ad_roas_calibration_upper(session_data->get_spdm_ctx());  // NOLINT
  const auto &ad_roas_calibration_lower =
                        SPDM_ad_roas_calibration_lower(session_data->get_spdm_ctx());  // NOLINT
  bool enable_std_cvr_ltv_coef = SPDM_enable_std_cvr_ltv_coef(session_data->get_spdm_ctx());  // NOLINT
  bool enable_std_cvr_ltv_coef_duanju = SPDM_enable_std_cvr_ltv_coef_duanju(session_data->get_spdm_ctx());  // NOLINT
  bool enable_std_cvr_ltv_coef_iaa = SPDM_enable_std_cvr_ltv_coef_iaa(session_data->get_spdm_ctx());  // NOLINT
  const auto &std_ad_roas_exp_tag = SPDM_std_ad_roas_exp_tag(session_data->get_spdm_ctx());  // NOLINT
  const auto &std_ad_iaa_exp_tag = SPDM_std_ad_iaa_exp_tag(session_data->get_spdm_ctx());  // NOLINT
  bool enable_roi_auto_calibrate = SPDM_enable_roi_auto_calibrate(session_data->get_spdm_ctx());
  bool enable_duanju_purchase_ratio = SPDM_enable_duanju_purchase_ratio(session_data->get_spdm_ctx());
  bool enable_roi_auto_calibrate_realtime = SPDM_enable_roi_auto_calibrate_realtime(session_data->get_spdm_ctx());  // NOLINT
  bool enable_conv_roi_auto_calibrate = SPDM_enable_conv_roi_auto_calibrate(session_data->get_spdm_ctx());
  bool enable_conv_roi_auto_calibrate_mini_app =
                        SPDM_enable_conv_roi_auto_calibrate_mini_app(session_data->get_spdm_ctx());
  bool enable_mini_game_iap_calibration_strategy =
      SPDM_enable_mini_game_iap_calibration_strategy(session_data->get_spdm_ctx());
  bool enable_mini_game_iaap_calibration_strategy =
      SPDM_enable_mini_game_iaap_calibration_strategy(session_data->get_spdm_ctx());
  bool enable_game_iaa_ltv_calibrate =
      SPDM_enable_game_iaa_ltv_calibrate(session_data->get_spdm_ctx());
  bool enable_game_iaa_7r_ltv_calibrate =
      SPDM_enable_game_iaa_7r_ltv_calibrate(session_data->get_spdm_ctx());
  bool enable_zp_reward_cvr_revise_exp_ = SPDM_enable_zp_reward_cvr_revise_exp(session_data->get_spdm_ctx());
  bool enable_duanju_outer_ctr_industry_cali =
                        SPDM_enable_duanju_outer_ctr_industry_cali(session_data->get_spdm_ctx());
  bool enable_revert_std_cvr_ltv_coef = SPDM_enable_revert_std_cvr_ltv_coef(session_data->get_spdm_ctx());  // NOLINT
  reward_cvr_revise_conf_ = RankKconfUtil::rewardCvrReviseConf();
  reward_cvr_revise_bound_up_ = SPDM_reward_cvr_revise_bound_up(session_data->get_spdm_ctx());
  reward_cvr_revise_bound_down_ = SPDM_reward_cvr_revise_bound_down(session_data->get_spdm_ctx());
  bool enable_outer_u_ctr_hard_queue_cali = SPDM_enable_outer_u_ctr_hard_queue_cali(session_data->get_spdm_ctx());  // NOLINT
  const auto &outer_u_ctr_hard_queue_cali_ratio = SPDM_outer_u_ctr_hard_queue_cali_ratio(session_data->get_spdm_ctx());  // NOLINT
  bool enable_outer_u_cvr = SPDM_enable_outer_u_cvr(session_data->get_spdm_ctx());
  bool enable_c2_lps_ = SPDM_enable_c2_lps(session_data->get_spdm_ctx());
  // 针对某个多路的精排 cvr 进行校准，只对单列 外循环 信息流 MCB 和 ocpm_dsp 生效 [guojw]
  bool enable_multi_retrieval_cxr_adj_exp = SPDM_enable_multi_retrieval_cxr_adj_exp(session_data->get_spdm_ctx());  // NOLINT

  bool enable_lps_clue_not_ecom_coef = SPDM_enable_lps_clue_not_ecom_coef(session_data->get_spdm_ctx());  // NOLINT
  bool enable_ad_huoke_coeff = SPDM_enable_ad_huoke_coeff(session_data->get_spdm_ctx());  // NOLINT

  // 初始化多路 adjust_cxr 参数
  std::unordered_map<int64_t, std::unordered_map<int64_t, double> > multi_retrieval_cxr_adj;
  std::string multi_retrieval_cxr_adj_str =
      session_data->get_spdm_ctx().TryGetString("multi_retrieval_cxr_adj_str", "");
  if (enable_multi_retrieval_cxr_adj_exp && session_data->get_is_explore_or_selected_request()
        && !multi_retrieval_cxr_adj_str.empty()) {
    for (auto& token_chl : absl::StrSplit(multi_retrieval_cxr_adj_str, "|", absl::SkipEmpty())) {
      std::vector<absl::string_view> chl_kv = absl::StrSplit(token_chl, "_", absl::SkipEmpty());
      int64_t chl_id;
      if (chl_kv.size() != 2 || !absl::SimpleAtoi(chl_kv[0], &chl_id)) {
        break;
      }
      auto inner_ocpc2rate = &(multi_retrieval_cxr_adj[chl_id]);
      for (auto& ocpc_token : absl::StrSplit(chl_kv[1], ",", absl::SkipEmpty())) {
        std::vector<absl::string_view> ocpc_kv = absl::StrSplit(ocpc_token, ":", absl::SkipEmpty());
        if (ocpc_kv.size() == 2) {
          int64 t;
          double s;
          if (absl::SimpleAtoi(ocpc_kv[0], &t) && absl::SimpleAtod(ocpc_kv[1], &s)) {
            if (chl_id == 66 || chl_id == 88 || chl_id == 90) {
              inner_ocpc2rate->insert({t, s});
            } else if (session_data->get_guaranteed_tags().count(chl_id)) {
              inner_ocpc2rate->insert({t, s});
            } else {
              if (s >= 0.7 && s <= 1.5) {
                inner_ocpc2rate->insert({t, s});
              }
            }
          }
        }
      }
    }
  }
  const auto &outer_hard_dsp_cali_config = RankKconfUtil::outerHardDspCaliConfig();
  const auto &outer_ctr_industry_cali_config = RankKconfUtil::outerCtrIndustryCaliConfig();

  auto &ads = ad_list->Ads();
  for (auto *p_ad : ads) {
      if (p_ad == nullptr) continue;
      // 目前仅作实验用，跳过老校准
      if (p_ad->get_is_skip_cali_before() == 1) {continue;}
      // 24 策略重构 先对齐基线：仅对硬广队列生效，后续考虑做策略合并
      if (p_ad->get_ad_list_type() != 1) {continue;}
      // 直播短视频队列合并，去除直播广告
      if (p_ad->Is(AdFlag::is_live_ad_inner) || p_ad->Is(AdFlag::is_p2l_ad_inner)) continue;
      SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad)
      bool is_perf_ocpx_action_type = true;

      // dingxiangkun, 逻辑保留, 不下线
      // 外循环广告配置预估值校准逻辑
      if (p_ad->Is(AdFlag::is_outer_loop_ad)) {
        std::string product_key = absl::StrCat(p_ad->get_product_name(), "|",
          kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type()), "|",
          kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
        auto iter_product = outer_hard_dsp_cali_config->find(product_key);
        if (iter_product != outer_hard_dsp_cali_config->end()) {
          p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                      p_ad->get_unify_cvr_info().s_type, p_ad->get_unify_cvr_info().e_type,
                                      RUnifyTag::OUTER_HARD_CALI_CONFIG, iter_product->second);
        }
      }
      // 外循环短剧行业预估值校准逻辑
      if (enable_duanju_outer_ctr_industry_cali && p_ad->Is(AdFlag::is_outer_loop_ad)) {
        std::string industry_key = absl::StrCat(p_ad->get_industry_id_v3());
        auto iter_industry_product = outer_ctr_industry_cali_config->find(industry_key);
        if (iter_industry_product != outer_ctr_industry_cali_config->end()) {
          p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                      p_ad->get_unify_cvr_info().s_type,
                                      p_ad->get_unify_cvr_info().e_type,
                                      RUnifyTag::OUTER_SCENE_CALI_CONFIG,
                                      iter_industry_product->second);
        }
      }
      // 外循环统一 ctr 硬广校准逻辑
      if (p_ad != nullptr &&
          p_ad->Is(AdFlag::is_outer_loop_ad) &&
          ((enable_outer_u_cvr &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
           (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
           (SPDM_enable_game_request_imp_conv(session_data->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) ||
        (enable_c2_lps_ && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION))) &&
          p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE &&
          enable_outer_u_ctr_hard_queue_cali &&
          outer_u_ctr_hard_queue_cali_ratio > 0) {
        p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                    p_ad->get_unify_cvr_info().s_type,
                                    p_ad->get_unify_cvr_info().e_type,
                                    RUnifyTag::OUTER_CTR_HARD_QUEUE_COEFF,
                                    outer_u_ctr_hard_queue_cali_ratio);
      }

      switch (p_ad->get_ocpx_action_type()) {
        // dingxiangkun, 逻辑保留, 暂不下线
      case kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED:
        {
          if (!p_ad->Is(AdFlag::is_direct_ecom) && !p_ad->Is(AdFlag::is_lps_tx) &&
              !enable_lps_clue_not_ecom_coef) {
            if (session_data->get_is_thanos_request()) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                    kuaishou::ad::AD_ITEM_IMPRESSION, p_ad->get_ocpx_action_type(),
                                    RUnifyTag::LPS_NOT_ECOM_COEFF, params_->lps_not_ecom_coeff_single_col);
            } else {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                    kuaishou::ad::AD_ITEM_IMPRESSION, p_ad->get_ocpx_action_type(),
                                    RUnifyTag::LPS_NOT_ECOM_COEFF, params_->lps_not_ecom_coeff_double_col);
            }
          }
          if (!p_ad->Is(AdFlag::is_direct_ecom) && !p_ad->Is(AdFlag::is_lps_tx) &&
              p_ad->Is(AdFlag::is_clue_ad) && enable_lps_clue_not_ecom_coef) {
            if (session_data->get_is_thanos_request()) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                    kuaishou::ad::AD_ITEM_IMPRESSION,
                                    p_ad->get_ocpx_action_type(),
                                    RUnifyTag::LPS_CLUE_NOT_ECOM_COEFF,
                                    params_->lps_clue_not_ecom_coeff_single_col);
            } else {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                    kuaishou::ad::AD_ITEM_IMPRESSION,
                                    p_ad->get_ocpx_action_type(),
                                    RUnifyTag::LPS_CLUE_NOT_ECOM_COEFF,
                                    params_->lps_clue_not_ecom_coeff_double_col);
            }
          }

          if (enable_ad_huoke_coeff && p_ad != nullptr && p_ad->get_deep_conversion_type() ==
          kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION) {
            if (session_data->get_is_thanos_request()) {
                p_ad->ModifyUnifyInfoLinear(RUnifyType::DEEP_CVR,
                                            kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                                            p_ad->get_ocpx_action_type(),
                                            RUnifyTag::AD_LANDING_PAGE_FORM_SUBMITTED_COEFF,
                                            params_->ad_landing_page_form_submitted_coeff_single_col);
              } else {
                p_ad->ModifyUnifyInfoLinear(RUnifyType::DEEP_CVR,
                                            kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                                            p_ad->get_ocpx_action_type(),
                                            RUnifyTag::AD_LANDING_PAGE_FORM_SUBMITTED_COEFF,
                                            params_->ad_landing_page_form_submitted_coeff_double_col);
              }
          }
        }
        break;
      case kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT:
        {
          if (session_data->get_is_thanos_request()) {
            p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                        kuaishou::ad::AD_ITEM_IMPRESSION, p_ad->get_ocpx_action_type(),
                                        RUnifyTag::EVENT_PRIVATE_MESSAGE_SENT_COEFF,
          params_->event_private_message_coeff_single_col);
          } else {
            p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                        kuaishou::ad::AD_ITEM_IMPRESSION, p_ad->get_ocpx_action_type(),
                                        RUnifyTag::EVENT_PRIVATE_MESSAGE_SENT_COEFF,
          params_->event_private_message_coeff_double_col);
          }
        }
        break;
      case kuaishou::ad::LEADS_SUBMIT:
        {
          if (session_data->get_is_thanos_request()) {
            p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                        kuaishou::ad::AD_ITEM_IMPRESSION, p_ad->get_ocpx_action_type(),
                                        RUnifyTag::LEADS_SUBMIT_COEFF,
        params_->leads_submit_coeff_single_col);
          } else {
            p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                        kuaishou::ad::AD_ITEM_IMPRESSION, p_ad->get_ocpx_action_type(),
                                        RUnifyTag::LEADS_SUBMIT_COEFF,
        params_->leads_submit_coeff_double_col);
          }
        }
        break;
      case kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION:
        {
          if (enable_ad_huoke_coeff && p_ad != nullptr) {
            if (session_data->get_is_thanos_request()) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::DEEP_CVR,
                                          kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                                          p_ad->get_ocpx_action_type(),
                                          RUnifyTag::AD_EFFECTIVE_CUSTOMER_ACQUISITION_COEFF,
                                          params_->ad_effective_customer_acquisition_coeff_single_col);
            } else {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::DEEP_CVR,
                                          kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                                          p_ad->get_ocpx_action_type(),
                                          RUnifyTag::AD_EFFECTIVE_CUSTOMER_ACQUISITION_COEFF,
                                          params_->ad_effective_customer_acquisition_coeff_double_col);
            }
          }
        }
        break;
      case kuaishou::ad::AD_CONVERSION:
        {
          if (!p_ad->Is(AdFlag::is_comp_ecom_ad)) {
            if (session_data->get_is_thanos_request()) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                  kuaishou::ad::AD_ITEM_IMPRESSION, p_ad->get_ocpx_action_type(),
                  RUnifyTag::AD_CONVERSION_COEFF, params_->conv_coeff_single_col);
            } else {
                p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                  kuaishou::ad::AD_ITEM_IMPRESSION, p_ad->get_ocpx_action_type(),
                  RUnifyTag::AD_CONVERSION_COEFF, params_->conv_coeff_double_col);
            }
          }
        }
        break;
      case kuaishou::ad::AD_PURCHASE:
        {
          // dingxiangkun, 逻辑保留, 不下线
          if (enable_duanju_purchase_ratio && p_ad->Is(AdFlag::is_paid_duanju_ad_v3)) {
            if (p_ad->CheckType(RUnifyType::CVR, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_PURCHASE)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR, kuaishou::ad::AD_CONVERSION,
              kuaishou::ad::AD_PURCHASE, RUnifyTag::AD_DUANJU_PURCHASE_RATIO, ad_duanju_purchase_ratio);
            } else if (p_ad->CheckType(RUnifyType::CVR, kuaishou::ad::EVENT_APP_INVOKED,
                                        kuaishou::ad::AD_PURCHASE)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR, kuaishou::ad::EVENT_APP_INVOKED,
              kuaishou::ad::AD_PURCHASE, RUnifyTag::AD_DUANJU_PURCHASE_RATIO, ad_duanju_purchase_ratio);
            }
          }
        }
        break;
      case kuaishou::ad::AD_ROAS:
      case kuaishou::ad::AD_SEVEN_DAY_ROAS:
        // 增加 std 的调权实验
        // dingxiangkun, 逻辑保留, 不下线
        if (!(enable_revert_std_cvr_ltv_coef && p_ad->Is(AdFlag::is_game_ad_v2)) && enable_std_cvr_ltv_coef &&
            ad_roas_std_coef != nullptr &&
            !(p_ad->Is(AdFlag::is_paid_duanju_ad_v3) && enable_std_cvr_ltv_coef_duanju)) {
          // 根据人群分层对 cvr，ltv 进行处理
          double upper = 2000.0;
          double lower = 0.0;
          std::string user_group_level =
              session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
          auto match_exp_list = ad_roas_std_coef->data().exp_list().find(std_ad_roas_exp_tag);
          if (match_exp_list != ad_roas_std_coef->data().exp_list().end()) {
             auto &process_config = match_exp_list->second.progress_config();
             auto match_user_group = process_config.find(user_group_level);
             if (match_user_group != process_config.end()) {
                double cvr_w = match_user_group->second.cvr_w();
                double cvr_bias = match_user_group->second.cvr_bias();
                double cvr_exp = match_user_group->second.cvr_exp();
                double ltv_w = match_user_group->second.ltv_w();
                double ltv_bias = match_user_group->second.ltv_bias();
                double ltv_exp = match_user_group->second.ltv_exp();
                if (p_ad->CheckType(RUnifyType::CVR, kuaishou::ad::AD_ITEM_IMPRESSION,
                kuaishou::ad::AD_CONVERSION)) {
                  p_ad->ModifyUnifyInfoUnLinear(RUnifyType::CVR,
                                              kuaishou::ad::AD_ITEM_IMPRESSION,
                                              kuaishou::ad::AD_CONVERSION,
                                              RUnifyTag::AD_ROAS_CONV_STD_COEFF,
                                              cvr_w, cvr_bias, cvr_exp);
                }
                if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS)) {
                  p_ad->ModifyUnifyInfoUnLinear(RUnifyType::LTV,
                                              kuaishou::ad::AD_CONVERSION,
                                              kuaishou::ad::AD_ROAS,
                                              RUnifyTag::AD_ROAS_CONV_STD_COEFF,
                                              ltv_w, ltv_bias, ltv_exp, upper, lower);
                }
             }
          }
        }
        {
          double b = 0.0;
          double upper = 2000.0;
          double lower = 0.0;
        // dingxiangkun, 近期有变更，先做 AB 实验
        if (!disable_useless_calibration_strategy) {
          auto ad_roas_account_modify_rate_map_iter =
            ad_roas_account_modify_rate_map->find(p_ad->get_account_id());
          if (ad_roas_account_modify_rate_map_iter != ad_roas_account_modify_rate_map->end()) {
            double ad_roas_account_modify_rate = ad_roas_account_modify_rate_map_iter->second;
            if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                                          kuaishou::ad::AD_CONVERSION,
                                          kuaishou::ad::AD_ROAS,
                                          RUnifyTag::AD_ROAS_CONV_ACCOUNT_COEFF,
                                          ad_roas_account_modify_rate,
                                          b,
                                          upper,
                                          lower);
            } else if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_PURCHASE,
                                        kuaishou::ad::AD_ROAS)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                                          kuaishou::ad::AD_PURCHASE,
                                          kuaishou::ad::AD_ROAS,
                                          RUnifyTag::AD_ROAS_PURCHASE_ACCOUNT_COEFF,
                                          ad_roas_account_modify_rate,
                                          b,
                                          upper,
                                          lower);
            }
          }
          // dingxiangkun, 近期有变更，先做 AB 实验
          // 先账户粒度校准，再产品粒度的校准
          auto ad_roas_product_modify_rate_map_iter =
            ad_roas_product_modify_rate_map->find(p_ad->get_product_name());
          if (ad_roas_product_modify_rate_map_iter != ad_roas_product_modify_rate_map->end()) {
            double ad_roas_product_modify_rate = ad_roas_product_modify_rate_map_iter->second;
            if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                                          kuaishou::ad::AD_CONVERSION,
                                          kuaishou::ad::AD_ROAS,
                                          RUnifyTag::AD_ROAS_CONV_PRODUCT_COEFF,
                                          ad_roas_product_modify_rate,
                                          b,
                                          upper,
                                          lower);
            } else if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_PURCHASE,
                                        kuaishou::ad::AD_ROAS)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                                          kuaishou::ad::AD_PURCHASE,
                                          kuaishou::ad::AD_ROAS,
                                          RUnifyTag::AD_ROAS_PURCHASE_PRODUCT_COEFF,
                                          ad_roas_product_modify_rate,
                                          b,
                                          upper,
                                          lower);
            }
          }
          // dingxiangkun, AB 实验后再下线， Kconf:adRoasDuanjuCalibrateRealtime, coder:zhangzhao06
          if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_PURCHASE,
                                            kuaishou::ad::AD_ROAS)) {
            if (enable_roi_auto_calibrate_realtime) {
              auto raw_ltv_value = p_ad->get_unify_ltv_info().value;
              auto calibrate_value = CalKconfCalibrationRes(raw_ltv_value, ad_playlet_roas_list_realtime,
                                        ad_roas_calibration_upper, ad_roas_calibration_lower);
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                    kuaishou::ad::AD_PURCHASE,
                    kuaishou::ad::AD_ROAS,
                    RUnifyTag::AD_ROAS_PURCHASE_PRODUCT_COEFF,
                    calibrate_value,
                    b,
                    upper,
                    lower);
            }
          }
        }
          // 小游戏校准, 不下线
          if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS)) {
              if (enable_conv_roi_auto_calibrate_mini_app && p_ad->Is(AdFlag::is_wechat_game_ad)) {
                auto raw_ltv_value = p_ad->get_unify_ltv_info().value;
                auto calibrate_value = CalKconfCalibrationRes(raw_ltv_value, ad_conv_roas_list_mini_app,
                                  ad_roas_calibration_mini_game_upper, ad_roas_calibration_mini_game_lower);
                p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                      kuaishou::ad::AD_CONVERSION,
                      kuaishou::ad::AD_ROAS,
                      RUnifyTag::AD_ROAS_PURCHASE_PRODUCT_COEFF,
                      calibrate_value,
                      b,
                      upper,
                      lower);
              // 游戏校准, 不下线
              } else if (enable_conv_roi_auto_calibrate && p_ad->get_industry_parent_id_v3() == 1018
              && !p_ad->Is(AdFlag::is_wechat_game_ad)) {
                auto raw_ltv_value = p_ad->get_unify_ltv_info().value;
                auto calibrate_value = CalKconfCalibrationRes(raw_ltv_value, ad_conv_roas_list,
                                            ad_roas_calibration_game_upper, ad_roas_calibration_game_lower);
                p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                      kuaishou::ad::AD_CONVERSION,
                      kuaishou::ad::AD_ROAS,
                      RUnifyTag::AD_ROAS_PURCHASE_PRODUCT_COEFF,
                      calibrate_value,
                      b,
                      upper,
                      lower);
              }
          }

          if (enable_mini_game_iap_calibration_strategy && p_ad->Is(AdFlag::is_iaap_game_ad)) {
            // 校准
            double upper = 2000.0;
            double lower = 0.0;
            double ltv_w = 1.0;
            double ltv_bias = 0.0;
            if (p_ad->get_optimal_subsidy_treatment() == 1) {
              ltv_w = params_->subsidy_calibrate_factor_w;
              ltv_bias = params_->subsidy_calibrate_factor_b;
            } else {
              ltv_w = params_->no_subsidy_calibrate_factor_w;
              ltv_bias = params_->no_subsidy_calibrate_factor_b;
            }
            if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS,
                                          RUnifyTag::AD_ROAS_CONV_PRODUCT_COEFF, ltv_w, ltv_bias, upper,
                                          lower);
            }
          }
        }
        break;

      case kuaishou::ad::AD_ROAS_IAAP:
        if (enable_mini_game_iaap_calibration_strategy && p_ad->Is(AdFlag::is_iaap_game_ad)) {
          // 校准
          double upper = 2000.0;
          double lower = 0.0;
          double ltv_w = 1.0;
          double ltv_bias = 0.0;
          if (p_ad->get_optimal_subsidy_treatment() == 1) {
            ltv_w = params_->subsidy_calibrate_factor_w;
            ltv_bias = params_->subsidy_calibrate_factor_b;
          } else {
            ltv_w = params_->no_subsidy_calibrate_factor_w;
            ltv_bias = params_->no_subsidy_calibrate_factor_b;
          }
          if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS_IAAP)) {
            p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION,
                                        kuaishou::ad::AD_ROAS_IAAP, RUnifyTag::AD_IAAP_LTV_CALIBRATE, ltv_w,
                                        ltv_bias, upper, lower);
          }
        }
      break;

      case kuaishou::ad::AD_IAA_ROAS:
        if (!(enable_revert_std_cvr_ltv_coef && p_ad->Is(AdFlag::is_game_ad_v2)) &&
            enable_std_cvr_ltv_coef_iaa && ad_iaa_std_coef != nullptr) {
          // 根据人群分层对 cvr，ltv 进行处理
          double upper = 2000.0;
          double lower = 0.0;
          std::string user_group_level =
              session_data->get_rank_request()->ad_request().ad_user_info().user_value_group_tag();
          auto match_exp_list = ad_iaa_std_coef->data().exp_list().find(std_ad_iaa_exp_tag);
          if (match_exp_list != ad_iaa_std_coef->data().exp_list().end()) {
             auto &process_config = match_exp_list->second.progress_config();
             auto match_user_group = process_config.find(user_group_level);
             if (match_user_group != process_config.end()) {
                double cvr_w = match_user_group->second.cvr_w();
                double cvr_bias = match_user_group->second.cvr_bias();
                double cvr_exp = match_user_group->second.cvr_exp();
                double ltv_w = match_user_group->second.ltv_w();
                double ltv_bias = match_user_group->second.ltv_bias();
                double ltv_exp = match_user_group->second.ltv_exp();
                if (p_ad->CheckType(RUnifyType::CVR, kuaishou::ad::AD_ITEM_IMPRESSION,
                kuaishou::ad::AD_CONVERSION)) {
                  p_ad->ModifyUnifyInfoUnLinear(RUnifyType::CVR,
                                              kuaishou::ad::AD_ITEM_IMPRESSION,
                                              kuaishou::ad::AD_CONVERSION,
                                              RUnifyTag::AD_IAA_CONV_STD_COEFF,
                                              cvr_w, cvr_bias, cvr_exp);
                }
                if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION,
                kuaishou::ad::AD_IAA_ROAS)) {
                  p_ad->ModifyUnifyInfoUnLinear(RUnifyType::LTV,
                                              kuaishou::ad::AD_CONVERSION,
                                              kuaishou::ad::AD_IAA_ROAS,
                                              RUnifyTag::AD_IAA_CONV_STD_COEFF,
                                              ltv_w, ltv_bias, ltv_exp, upper, lower);
                }
             }
          }
        }
        if (enable_game_iaa_ltv_calibrate && p_ad->Is(AdFlag::is_iaa_game_ltv)) {
            double upper = 2000.0;
            double lower = 0.0;
            double ltv_w = 1.0;
            double ltv_bias = 0.0;
            ltv_w = params_->iaa_game_ltv_calibrate_factor_w;
            ltv_bias = params_->iaa_game_ltv_calibrate_factor_b;
            p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION,
                                        kuaishou::ad::AD_IAA_ROAS, RUnifyTag::AD_GAME_IAA_LTV_CALIBRATE,
                                        ltv_w, ltv_bias, upper, lower);
        }

        break;
      case kuaishou::ad::AD_IAA_7DAY_ROAS:
        if (enable_game_iaa_7r_ltv_calibrate && p_ad->get_campaign_type()==
        kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) {
          double upper = 2000.0;
          double lower = 0.0;
          double ltv_w = 1.0;
          double ltv_bias = 0.0;
          ltv_w = params_->iaa_game_7r_ltv_calibrate_factor_w;
          ltv_bias = params_->iaa_game_7r_ltv_calibrate_factor_b;
          p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION,
                                      kuaishou::ad::AD_IAA_7DAY_ROAS, RUnifyTag::AD_GAME_IAA_7R_LTV_CALIBRATE,
                                      ltv_w, ltv_bias, upper, lower);
        }
        break;
      // dingxiangkun, AB 实验后下线
      case kuaishou::ad::EVENT_APP_INVOKED:
        {
        if (!disable_useless_calibration_strategy) {
          if (p_ad->Is(AdFlag::is_dpa)) {
            // dpa_app_invoke_coeff_col 推全，参数固化为 1.2
            p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                        kuaishou::ad::AD_ITEM_IMPRESSION,
                                        p_ad->get_ocpx_action_type(),
                                        RUnifyTag::DPA_APP_INVOKED_COEFF,
                                        1.2);
          }
        }
        }
      break;
      // 不下线, 模型迭代长期需要使用
      case kuaishou::ad::AD_CREDIT_GRANT:
        {
          if (session_data->get_is_thanos_request()
              && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT) {
              if (!SPDM_enable_new_coeff_credit(session_data->get_spdm_ctx())) {
                double ind_adjust_ratio = params_->credit_grant_single_double_coeff_col;
                p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                          kuaishou::ad::AD_ITEM_CLICK,
                                          p_ad->get_ocpx_action_type(),
                                          RUnifyTag::GREDIT_GRANT_COEFF,
                                          ind_adjust_ratio);
              } else {
                double ind_adjust_ratio = params_->credit_grant_single_double_coeff_col_v2;
                p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                          kuaishou::ad::AD_ITEM_CLICK,
                                          p_ad->get_ocpx_action_type(),
                                          RUnifyTag::GREDIT_GRANT_COEFF,
                                          ind_adjust_ratio);
              }
          }
        }
      break;
      default:
        is_perf_ocpx_action_type = false;   // 走 default 的不用打点
        break;
      }
      if (is_perf_ocpx_action_type) {
        session_data->dot_perf->Count(1, "modify_r_value_ocpx_action_type_test",
                                      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
      }
      bool is_perf_deep_conversion_type = true;
      switch (p_ad->get_deep_conversion_type()) {
        // 暂时保留不下线, 是次留的策略，不是校准策略
        case kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY: {
          double pdcvr = p_ad->get_conv_nextstay();
          p_ad->ModifyUnifyInfoLinear(RUnifyType::DEEP_CVR, kuaishou::ad::AD_CONVERSION,
                                      kuaishou::ad::AdActionType::EVENT_NEXTDAY_STAY,
                                      RUnifyTag::ISOTONIC_REGRESSION, 0, pdcvr);
          break;
        }
        case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY: {
          if (!disable_useless_calibration_strategy) {
          // dingxiangkun, 近期有变更，先 AB 实验
          double b = 0.0;
          double upper = 2000.0;
          double lower = 0.0;
          auto ad_roas_account_modify_rate_map_iter =
            ad_roas_account_modify_rate_map->find(p_ad->get_account_id());
          if (ad_roas_account_modify_rate_map_iter != ad_roas_account_modify_rate_map->end()) {
            double ad_roas_account_modify_rate = ad_roas_account_modify_rate_map_iter->second;
            if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                                          kuaishou::ad::AD_CONVERSION,
                                          kuaishou::ad::AD_ROAS,
                                          RUnifyTag::AD_ROAS_CONV_ACCOUNT_COEFF,
                                          ad_roas_account_modify_rate,
                                          b,
                                          upper,
                                          lower);
            } else if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_PURCHASE,
                                        kuaishou::ad::AD_ROAS)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                                          kuaishou::ad::AD_PURCHASE,
                                          kuaishou::ad::AD_ROAS,
                                          RUnifyTag::AD_ROAS_PURCHASE_ACCOUNT_COEFF,
                                          ad_roas_account_modify_rate,
                                          b,
                                          upper,
                                          lower);
            }
          }
          // dingxiangkun, 近期有变更，先 AB 实
          // 先账户粒度校准，再产品粒度的校准
          auto ad_roas_product_modify_rate_map_iter =
            ad_roas_product_modify_rate_map->find(p_ad->get_product_name());
          if (ad_roas_product_modify_rate_map_iter != ad_roas_product_modify_rate_map->end()) {
            double ad_roas_product_modify_rate = ad_roas_product_modify_rate_map_iter->second;
            if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                                          kuaishou::ad::AD_CONVERSION,
                                          kuaishou::ad::AD_ROAS,
                                          RUnifyTag::AD_ROAS_CONV_PRODUCT_COEFF,
                                          ad_roas_product_modify_rate,
                                          b,
                                          upper,
                                          lower);
            } else if (p_ad->CheckType(RUnifyType::LTV, kuaishou::ad::AD_PURCHASE,
                                        kuaishou::ad::AD_ROAS)) {
              p_ad->ModifyUnifyInfoLinear(RUnifyType::LTV,
                                          kuaishou::ad::AD_PURCHASE,
                                          kuaishou::ad::AD_ROAS,
                                          RUnifyTag::AD_ROAS_PURCHASE_PRODUCT_COEFF,
                                          ad_roas_product_modify_rate,
                                          b,
                                          upper,
                                          lower);
            }
          }
          }
          break;
        }
        default:
          is_perf_deep_conversion_type = false;
          break;
      }
      // dingxiangkun, ab 参数复杂, 暂时不下线
      // 更新某个召回通路的 punify_cvr
      auto ocpc_type = static_cast<int>(p_ad->get_ocpx_action_type());
      auto retrieval_tag = p_ad->get_multi_retrieval_tag();
      if (enable_multi_retrieval_cxr_adj_exp && session_data->get_is_explore_or_selected_request()
            && !multi_retrieval_cxr_adj.empty()
            && (multi_retrieval_cxr_adj.find(retrieval_tag) != multi_retrieval_cxr_adj.end())
            && (p_ad->get_bid_type() == kuaishou::ad::AdEnum::MCB ||
              p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP)) {
        auto ocpc2rate = multi_retrieval_cxr_adj.find(retrieval_tag)->second;
        auto rate_iter = ocpc2rate.find(ocpc_type);
        if (rate_iter != ocpc2rate.end()) {
          auto adjust_ratio = rate_iter->second;
          p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                             p_ad->get_unify_cvr_info().s_type,
                             p_ad->get_unify_cvr_info().e_type,
                             RUnifyTag::MULTI_RETRIEVAL_CXR_ADJ,
                             adjust_ratio);
        }
      }
      // 激励流量校准, 暂不下线
      if (enable_zp_reward_cvr_revise_exp_ && session_data->get_is_rewarded() &&
          session_data->get_is_thanos_request()) {
        ModifyRewardCvr(session_data, p_ad);
      }
  }
  return StraRetCode::SUCC;
} // NOLINT

void ModifyRValuePlugin::ModifyRewardCvr(ContextData* session_data, AdCommon* p_ad) {
  if (p_ad->Attr(ItemIdx::is_cvr_common_calibrated).GetIntValue(p_ad->AttrIndex()).value_or(0) == 1) {
    return;
  }
  if (reward_cvr_revise_conf_ == nullptr) {
    return;
  }
  auto key_str = absl::StrCat(kuaishou::ad::AdActionType_Name(
                               p_ad->get_ocpx_action_type()), "_",
                               p_ad->get_product_name());
  auto iter = reward_cvr_revise_conf_->data().admit().find(key_str);
  if (iter == reward_cvr_revise_conf_->data().admit().end()) {
    key_str = absl::StrCat(kuaishou::ad::AdActionType_Name(
                                     p_ad->get_ocpx_action_type()),
                                 "_other");
    iter = reward_cvr_revise_conf_->data().admit().find(key_str);
  }
  if (iter != reward_cvr_revise_conf_->data().admit().end()) {
    p_ad->set_reward_cvr_r(1.0);
    for (auto& revise_node : iter->second.revise_node()) {
        if (p_ad->get_reward_styles().find(revise_node.reward_style())
          != p_ad->get_reward_styles().end()) {
          if (revise_node.reward_r() > 0) {
            p_ad->set_reward_cvr_r(p_ad->get_reward_cvr_r() * revise_node.reward_r());
            RANK_DOT_STATS(session_data, revise_node.reward_r() * 1000,
                           "ad_rank.ModifyRewardCvr.reward_style", key_str, revise_node.reward_style());
          }
        } else {
          if (revise_node.normal_r() > 0) {
            p_ad->set_reward_cvr_r(p_ad->get_reward_cvr_r() * revise_node.normal_r());
            RANK_DOT_STATS(session_data, revise_node.normal_r() * 1000,
                           "ad_rank.ModifyRewardCvr.normal_style", key_str, revise_node.reward_style());
          }
        }
    }
    auto ratio = std::min(reward_cvr_revise_bound_up_, p_ad->get_reward_cvr_r());
    ratio = std::max(ratio, reward_cvr_revise_bound_down_);
    p_ad->set_reward_cvr_r(ratio);
    p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                p_ad->get_unify_cvr_info().s_type,
                                p_ad->get_unify_cvr_info().e_type,
                                RUnifyTag::REWARD_CVR_CALIBRATE,
                                p_ad->get_reward_cvr_r());
    RANK_DOT_STATS(session_data, p_ad->get_reward_cvr_r() * 1000,
                   "ad_rank.ModifyRewardCvr.reward_cvr_r", key_str);
  }
}

std::vector<std::vector<double>> ModifyRValuePlugin::GetCalibrationList(
             const std::vector<std::string> &calibrate_raw, uint32_t bins_n, uint32_t cal_n) {
  std::vector<double> bins_left_list;
  std::vector<double> calibrate_list;
  for (const std::string& str : calibrate_raw) {
    std::vector<std::string> vals;
    base::SplitString(str, "@", &vals);
    if (vals.size() > bins_n && vals.size() > cal_n) {
      double bins_left;
      double calibrate_values;
      if (!absl::SimpleAtod(vals[bins_n], &bins_left)) {continue;}
      if (!absl::SimpleAtod(vals[cal_n], &calibrate_values)) {continue;}
      bins_left_list.push_back(bins_left);
      calibrate_list.push_back(calibrate_values);
    }
  }
  std::vector<std::vector<double>> res{bins_left_list, calibrate_list};
  return res;
}
double ModifyRValuePlugin::CalKconfCalibrationRes(double score,
        const std::vector<std::vector<double>> &calibrate_list, double upper, double lower) {
  double calibrate_value = 1.0;
  if (calibrate_list.size() < 2 || calibrate_list[0].size() != calibrate_list[1].size()) {
    return calibrate_value;
  }
  const auto &bins_left_list = calibrate_list[0];
  const auto &calibrate_value_list = calibrate_list[1];
  for (int i = 0; i+1 < bins_left_list.size(); i++) {
    if (score >= bins_left_list[i] && score < bins_left_list[i+1]) {
      calibrate_value = calibrate_value_list[i];
      if (calibrate_value_list[i] < lower) {
        calibrate_value = lower;
      } else if (calibrate_value_list[i] > upper) {
        calibrate_value = upper;
      }
      break;
    }
  }
  return calibrate_value;
}

void CalibrationWithCmdCorePlugin::OnlineCalibrationWithCmd(
    ContextData* session_data, AdCommon* p_ad, CalcBenefitParams* params) {
  int32_t cmd_id = 0;
  if (p_ad->get_landingpage_submit_rate() > 0) {
    cmd_id = p_ad->get_landingpage_submit_rate_cmd_id();
  }
  if (p_ad->get_app_conversion_rate() > 0) {
    cmd_id = p_ad->get_app_conversion_rate_cmd_id();
  }
  int64_t page_id = session_data->get_page_id();
  std::string query_key = p_ad->GetOnlineCalibrationKey(page_id, cmd_id, 4);
  double cali_rate = p_ad->GetCalibrateValue(query_key);
  // if (cali_rate > 0) {
  //  LOG_EVERY_N(INFO, 100) << "OnlineCalibrationWithCmd,key:"<<query_key<<",value:"<<cali_rate;
  // }
  // else {
  //  LOG_EVERY_N(INFO, 100) << "OnlineCalibrationWithCmd no cali_rate,key:"<<query_key<<",value:"<<cali_rate;
  // }

  //校准服务返回分数
  // double cali_rate = p_ad->get_unify_cvr_info().calibrate_value;
  double smart_bidding_min_rate =
           session_data->get_spdm_ctx().TryGetDouble("calibration_with_cmd_min_rate", 1.0);
  double smart_bidding_max_rate =
           session_data->get_spdm_ctx().TryGetDouble("calibration_with_cmd_max_rate", 1.0);
  // 表单独立校准参数限定
  if (params != nullptr && p_ad->get_landingpage_submit_rate() > 0) {
    auto& cali_params = params->lps_calibrate_params;
    bool use_lps_cali_params = cali_params.use_lps_cali_params;
    if (use_lps_cali_params) {
       smart_bidding_min_rate = cali_params.smart_bidding_min_rate;
       smart_bidding_max_rate = cali_params.smart_bidding_max_rate;
       exception_upper_bound = cali_params.exception_upper_bound;
       exception_lower_bound = cali_params.exception_lower_bound;
    }
  }

  if (cali_rate == 1.0) {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_cali_rate_one");
  } else if (cali_rate == 0) {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_cali_rate_zero");
  } else {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_cali_rate_other");
  }

  if (exception_upper_bound > 1.0 && cali_rate >= exception_upper_bound) {
    cali_rate = 1.0;
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_exception_upper_bound_work");
  } else {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_exception_upper_bound_no_work");
  }
  if (exception_lower_bound < 1.0 && cali_rate <= exception_lower_bound) {
    cali_rate = 1.0;
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_exception_lower_bound_work");
  } else {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_exception_lower_bound_no_work");
  }

  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (p_ad->get_landingpage_submit_rate() > 0) {
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (smart_bidding_min_rate < 1.0 && smart_bidding_min_rate > cali_rate) {
        cali_rate = smart_bidding_min_rate;
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_landingpage_lower_bound_count",
            std::to_string(p_ad->get_landingpage_submit_rate_cmd_id()),
            action_type);
      }
      if (smart_bidding_max_rate > 1.0 && smart_bidding_max_rate < cali_rate) {
        cali_rate = smart_bidding_max_rate;
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_landingpage_upper_bound_count",
            std::to_string(p_ad->get_landingpage_submit_rate_cmd_id()),
            action_type);
      }
      if (p_ad->IsNeedCalibration(RUnifyType::CTR,
                                  p_ad->get_landingpage_submit_rate_cmd_id())) {
        p_ad->ExcuteCalibration(RUnifyType::CTR, cali_rate);
      } else if (p_ad->IsNeedCalibration(
                     RUnifyType::CVR,
                     p_ad->get_landingpage_submit_rate_cmd_id())) {
        p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
      } else {
      }
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                     "ad_rank.online_calibration_landingpage_cali_rate",
                     std::to_string(p_ad->get_landingpage_submit_rate_cmd_id()),
                     action_type);
      RANK_DOT_COUNT(session_data, 1,
                     "ad_rank.online_calibration_landingpage_cali_rate_count",
                     std::to_string(p_ad->get_landingpage_submit_rate_cmd_id()),
                     action_type);
      if (cali_rate > 1.0) {
        RANK_DOT_STATS(
            session_data, static_cast<int64_t>(100 * cali_rate),
            "ad_rank.online_calibration_landingpage_cali_rate_up",
            std::to_string(p_ad->get_landingpage_submit_rate_cmd_id()),
            action_type);
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_landingpage_cali_rate_count_up",
            std::to_string(p_ad->get_landingpage_submit_rate_cmd_id()),
            action_type);
      } else {
        RANK_DOT_STATS(
            session_data, static_cast<int64_t>(100 * cali_rate),
            "ad_rank.online_calibration_landingpage_cali_rate_down",
            std::to_string(p_ad->get_landingpage_submit_rate_cmd_id()),
            action_type);
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_landingpage_cali_rate_count_down",
            std::to_string(p_ad->get_landingpage_submit_rate_cmd_id()),
            action_type);
      }
    }
  }
  if (p_ad->get_app_conversion_rate() > 0) {
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (smart_bidding_min_rate < 1.0 && smart_bidding_min_rate > cali_rate) {
        cali_rate = smart_bidding_min_rate;
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_conv_lower_bound_count",
            std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
            action_type);
      }
      if (smart_bidding_max_rate > 1.0 && smart_bidding_max_rate < cali_rate) {
        cali_rate = smart_bidding_max_rate;
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_conv_upper_bound_count",
            std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
            action_type);
      }
      if (p_ad->IsNeedCalibration(RUnifyType::CTR,
                                  p_ad->get_app_conversion_rate_cmd_id())) {
        p_ad->ExcuteCalibration(RUnifyType::CTR, cali_rate);
      } else if (p_ad->IsNeedCalibration(
                     RUnifyType::CVR, p_ad->get_app_conversion_rate_cmd_id())) {
        p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
      } else {
      }
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                     "ad_rank.online_calibration_app_conv_cali_rate",
                     std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
                     action_type);
      RANK_DOT_COUNT(session_data, 1,
                     "ad_rank.online_calibration_app_conv_cali_rate_count",
                     std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
                     action_type);
      if (cali_rate > 1.0) {
        RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                       "ad_rank.online_calibration_app_conv_cali_rate_up",
                       std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
                       action_type);
        RANK_DOT_COUNT(session_data, 1,
                       "ad_rank.online_calibration_app_conv_cali_rate_count_up",
                       std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
                       action_type);
      } else {
        RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                       "ad_rank.online_calibration_app_conv_cali_rate_down",
                       std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
                       action_type);
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_app_conv_cali_rate_count_down",
            std::to_string(p_ad->get_app_conversion_rate_cmd_id()), action_type);
      }
    }
  }
}

void CalibrationWithCmdCorePlugin::OnlineCalibrationWithCmdOrderPaied(
    ContextData* session_data, AdCommon* p_ad, CalcBenefitParams* params) {
  auto& cali_params = params->order_paied_calibrate_params;
  int32_t cmd_id = 0;
  if (p_ad->get_c1_order_paied() > 0) {
    cmd_id = p_ad->get_c1_order_paied_cmd_id();
  }
  int64_t page_id = session_data->get_page_id();
  int32_t query_type = enable_inner_order_pay_cali_ad_queue_type_hard ?  5 : 4;
  if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) {
    query_type = SPDM_enable_inner_cid_calibrate_control_adqueue(session_data->get_spdm_ctx()) ? 6 : 4;
  }
  std::string query_key = p_ad->GetOnlineCalibrationKey(page_id, cmd_id, query_type);
  p_ad->Attr(ItemIdx::adcalibrate_response_key).SetStringValue(p_ad->AttrIndex(), std::string(query_key), false, false);  // NOLINT
  double cali_rate = p_ad->GetCalibrateValue(query_key);
  if (cali_rate == 1.0) {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_order_paied_cali_rate_one");
  } else if (cali_rate == 0) {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_order_paied_cali_rate_zero");
  } else {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.online_calibration_order_paied_cali_rate_other");
  }

  if (cali_params.exception_upper_bound > 1.0 && cali_rate >= cali_params.exception_upper_bound) {
    cali_rate = 1.0;
  }
  if (cali_params.exception_lower_bound < 1.0 && cali_rate <= cali_params.exception_lower_bound) {
    cali_rate = 1.0;
  }

  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (p_ad->get_c1_order_paied() > 0) {
    std::string cmd_id_str = absl::StrCat(p_ad->get_c1_order_paied_cmd_id());
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (cali_params.smart_bidding_min_rate < 1.0 && cali_params.smart_bidding_min_rate > cali_rate) {
        cali_rate = cali_params.smart_bidding_min_rate;
      }
      if (cali_params.smart_bidding_max_rate > 1.0 && cali_params.smart_bidding_max_rate < cali_rate) {
        cali_rate = cali_params.smart_bidding_max_rate;
      }
      if (p_ad->IsNeedCalibration(
                    RUnifyType::CVR,
                    p_ad->get_c1_order_paied_cmd_id())) {
        p_ad->Attr(ItemIdx::ad_calibrate_cali_rate).SetDoubleValue(p_ad->AttrIndex(), cali_rate, false, false);  // NOLINT cali_rate_turn 不准确
        p_ad->Attr(ItemIdx::ad_calibrate_origin_cvr).SetDoubleValue(p_ad->AttrIndex(), p_ad->get_unify_cvr_info().value, false, false);  // NOLINT
        p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
      }
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                    "ad_rank.online_calibration_order_paied_cali_rate",
                    cmd_id_str,
                    action_type);
      RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.online_calibration_order_paied_cali_rate_count",
                    cmd_id_str,
                    action_type);
    }
  }
}

void CalibrationWithCmdCorePlugin::OnlineCalibrationWithCmdRoas(
    ContextData* session_data, AdCommon* p_ad, CalcBenefitParams* params) {
  auto& cali_params = params->roas_calibrate_params;
  int32_t cmd_id = 0;
  if (p_ad->get_merchant_ltv() > 0) {
    cmd_id = (enable_roas_online_calibration_by_stage_one_cmd ?
        p_ad->get_c1_order_paied_cmd_id() : p_ad->get_merchant_ltv_cmd_id());
  }
  int64_t page_id = session_data->get_page_id();
  int32_t query_type = enable_inner_roas_cali_ad_queue_type_hard ?  5 : 4;
  if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
    query_type = SPDM_enable_inner_cid_calibrate_control_adqueue(session_data->get_spdm_ctx()) ? 6 : 4;
  }
  std::string query_key = p_ad->GetOnlineCalibrationKey(page_id, cmd_id, query_type);
  p_ad->Attr(ItemIdx::adcalibrate_response_key).SetStringValue(p_ad->AttrIndex(), std::string(query_key), false, false);  // NOLINT
  double cali_rate = p_ad->GetCalibrateValue(query_key);

  if (cali_params.exception_upper_bound > 1.0 && cali_rate >= cali_params.exception_upper_bound) {
    cali_rate = 1.0;
  }
  if (cali_params.exception_lower_bound < 1.0 && cali_rate <= cali_params.exception_lower_bound) {
    cali_rate = 1.0;
  }

  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (p_ad->get_merchant_ltv() > 0) {
    std::string cmd_id_str = absl::StrCat(cmd_id);
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (cali_params.smart_bidding_min_rate < 1.0 && cali_params.smart_bidding_min_rate > cali_rate) {
        cali_rate = cali_params.smart_bidding_min_rate;
      }
      if (cali_params.smart_bidding_max_rate > 1.0 && cali_params.smart_bidding_max_rate < cali_rate) {
        cali_rate = cali_params.smart_bidding_max_rate;
      }
      if ((!enable_roas_online_calibration_by_stage_one_cmd &&
          p_ad->IsNeedCalibration(RUnifyType::LTV, cmd_id)) ||
          (enable_roas_online_calibration_by_stage_one_cmd &&
          p_ad->IsNeedCalibration(RUnifyType::CVR, cmd_id))) {
        p_ad->Attr(ItemIdx::ad_calibrate_cali_rate).SetDoubleValue(p_ad->AttrIndex(), cali_rate, false, false);  // NOLINT cali_rate_turn 不准确
        p_ad->Attr(ItemIdx::ad_calibrate_origin_cvr).SetDoubleValue(p_ad->AttrIndex(), p_ad->get_unify_ltv_info().value, false, false);  // NOLINT
        p_ad->ExcuteCalibration(RUnifyType::LTV, cali_rate);
      }
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                    "ad_rank.online_calibration_roas_cali_rate",
                    cmd_id_str,
                    action_type);
    }
  }
}

void CalibrationWithCmdCorePlugin::OnlineCalibrationWithCmdStorewide(
    ContextData* session_data, AdCommon* p_ad, CalcBenefitParams* params) {
  auto& cali_params = params->storewide_calibrate_params;
  int32_t cmd_id = 0;
  if (p_ad->get_merchant_ltv() > 0) {
    cmd_id = (enable_roas_online_calibration_by_stage_one_cmd ?
        p_ad->get_c1_order_paied_cmd_id() : p_ad->get_merchant_ltv_cmd_id());
  }
  int64_t page_id = session_data->get_page_id();
  int32_t query_type = enable_inner_storewide_cali_ad_queue_type ?  5 : 4;
  std::string query_key = p_ad->GetOnlineCalibrationKey(page_id, cmd_id, query_type);
  double cali_rate = p_ad->GetCalibrateValue(query_key);
  if (cali_params.exception_upper_bound > 1.0 && cali_rate >= cali_params.exception_upper_bound) {
    cali_rate = 1.0;
  }
  if (cali_params.exception_lower_bound < 1.0 && cali_rate <= cali_params.exception_lower_bound) {
    cali_rate = 1.0;
  }
  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (p_ad->get_merchant_ltv() > 0) {
    std::string cmd_id_str = absl::StrCat(cmd_id);
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (cali_params.smart_bidding_min_rate < 1.0 && cali_params.smart_bidding_min_rate > cali_rate) {
        cali_rate = cali_params.smart_bidding_min_rate;
      }
      if (cali_params.smart_bidding_max_rate > 1.0 && cali_params.smart_bidding_max_rate < cali_rate) {
        cali_rate = cali_params.smart_bidding_max_rate;
      }
      if ((!enable_roas_online_calibration_by_stage_one_cmd &&
          p_ad->IsNeedCalibration(RUnifyType::LTV, cmd_id)) ||
          (enable_roas_online_calibration_by_stage_one_cmd &&
          p_ad->IsNeedCalibration(RUnifyType::CVR, cmd_id))) {
        p_ad->ExcuteCalibration(RUnifyType::LTV, cali_rate);
      }
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                    "ad_rank.online_calibration_storewide_cali_rate",
                    cmd_id_str,
                    action_type);
    }
  }
}

const char* CalibrationWithCmdCorePlugin::Name() {
  return "CalibrationWithCmdCorePlugin";
}

void CalibrationWithCmdCorePlugin::Clear() {}

bool CalibrationWithCmdCorePlugin::IsRun(const ContextData* session_data, const Params* params,
                                         AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CalibrationWithCmdCorePlugin::Process(ContextData* session_data,
                                                  Params* params,
                                                  AdRankUnifyScene pos,
                                                  AdList* ad_list) {
  auto enable_shelf_express_cali_ratio =
  SPDM_enable_shelf_express_cali_ratio(session_data->get_spdm_ctx());
  auto shelf_express_cali_ratio =
  SPDM_shelf_express_cali_ratio(session_data->get_spdm_ctx());
  if (!session_data->get_is_search_request() &&
      (!SPDM_enbale_gyl_calibration(session_data->get_spdm_ctx()) ||
          !session_data->get_pos_manager_base().IsShelfMerchantTraffic())) {
    if (!SPDM_enbale_online_calibration_with_cmd(session_data->get_spdm_ctx()) &&
      !SPDM_enbale_online_calibration_with_cmd_order_paied(session_data->get_spdm_ctx()) &&
      !SPDM_enbale_online_calibration_with_cmd_roas(session_data->get_spdm_ctx()) &&
      !SPDM_enable_new_online_calibration_with_cmd_order_paied(session_data->get_spdm_ctx()) &&
      SPDM_disable_new_online_calibration_with_cmd_roas(session_data->get_spdm_ctx()) &&
      !SPDM_enable_inner_normal_storewide_calibration(session_data->get_spdm_ctx())) {
      return StraRetCode::SUCC;
    }
  }

  if (ad_list == nullptr || ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  auto& ads = ad_list->Ads();
  auto jiChenAccountList = RankKconfUtil::jiChenAccountList();
  auto exploreMobileSoftCaliMap = RankKconfUtil::exploreMobileSoftCaliMap();
  bool disable_useless_calibration_strategy =
       SPDM_disable_useless_calibration_strategy(session_data->get_spdm_ctx());
  bool enable_jicheng_account_only_for_cold =
       SPDM_enable_jicheng_account_only_for_cold(session_data->get_spdm_ctx());
  bool enable_mobile_s2h_explore_cali =
       SPDM_enable_mobile_s2h_explore_cali(session_data->get_spdm_ctx());
  bool enable_esp_aigc_order_paid_cali =
       SPDM_enable_esp_aigc_order_paid_cali(session_data->get_spdm_ctx());
  double esp_inner_aigc_order_paid_cali_rate =
       SPDM_esp_inner_aigc_order_paid_cali_rate(session_data->get_spdm_ctx());
  bool enable_esp_aigc_roas_cali =
       SPDM_enable_esp_aigc_roas_cali(session_data->get_spdm_ctx());
  double esp_inner_aigc_roas_cali_rate =
       SPDM_esp_inner_aigc_roas_cali_rate(session_data->get_spdm_ctx());
  bool enable_mobile_s2h_non_explore_cali =
       SPDM_enable_mobile_s2h_non_explore_cali(session_data->get_spdm_ctx());
  bool enable_jicheng_account_event_order_paied =
       SPDM_enable_jicheng_account_event_order_paied(session_data->get_spdm_ctx());
  bool enbale_online_calibration_with_cmd_roas =
       SPDM_enbale_online_calibration_with_cmd_roas(session_data->get_spdm_ctx());
  bool enbale_online_calibration_with_cmd_order_paied =
       SPDM_enbale_online_calibration_with_cmd_order_paied(session_data->get_spdm_ctx());
  bool disable_new_online_calibration_with_cmd_roas =
       SPDM_disable_new_online_calibration_with_cmd_roas(session_data->get_spdm_ctx());
  bool enable_new_online_calibration_with_cmd_order_paied =
       SPDM_enable_new_online_calibration_with_cmd_order_paied(session_data->get_spdm_ctx());
  bool enable_inner_normal_storewide_calibration =
       SPDM_enable_inner_normal_storewide_calibration(session_data->get_spdm_ctx());
  bool enbale_online_calibration_with_cmd =
       SPDM_enbale_online_calibration_with_cmd(session_data->get_spdm_ctx());
  bool enable_item_card_jichen =
        SPDM_enable_item_card_jichen(session_data->get_spdm_ctx());
  bool enable_item_card_jichen_add_ocpc_action_type =
        SPDM_enable_item_card_jichen_add_ocpc_action_type(session_data->get_spdm_ctx());
  bool enable_item_card_soft_queue_jichen =
        SPDM_enable_item_card_soft_queue_jichen(session_data->get_spdm_ctx());
  bool enable_item_card_spu_jichen =
        SPDM_enable_item_card_spu_jichen(session_data->get_spdm_ctx());
  bool enable_item_card_author_spu_jichen =
        SPDM_enable_item_card_author_spu_jichen(session_data->get_spdm_ctx());
  double item_card_jichen_rate_delta =
        SPDM_item_card_jichen_rate_delta(session_data->get_spdm_ctx());
  bool enable_item_card_not_jichen_cali =
        SPDM_enable_item_card_not_jichen_cali(session_data->get_spdm_ctx());
  bool enable_item_card_not_jichen_cali2 =
        SPDM_enable_item_card_not_jichen_cali2(session_data->get_spdm_ctx());
  double item_card_not_jichen_cali_rate =
        SPDM_item_card_not_jichen_cali_rate(session_data->get_spdm_ctx());
  bool disable_item_card_jichen_roas =
        SPDM_disable_item_card_jichen_roas(session_data->get_spdm_ctx());
  bool enable_item_card_merchant_product_id_hard_jichen =
        SPDM_enable_item_card_merchant_product_id_hard_jichen(session_data->get_spdm_ctx());
  bool enable_item_card_roas_item_id_hard_jichen =
        SPDM_enable_item_card_roas_item_id_hard_jichen(session_data->get_spdm_ctx());
  bool enable_item_card_roas_jichen_fix =
        SPDM_enable_item_card_roas_jichen_fix(session_data->get_spdm_ctx());
  bool enable_item_card_roas_item_id_soft_jichen =
        SPDM_enable_item_card_roas_item_id_soft_jichen(session_data->get_spdm_ctx());
  bool disable_spu_0_jichen = SPDM_disable_spu_0_jichen(session_data->get_spdm_ctx());
  bool disable_merchant_product_id_0_jichen =
     SPDM_disable_merchant_product_id_0_jichen(session_data->get_spdm_ctx());
  bool enable_jichen_cvr_upper_bound =
     SPDM_enable_jichen_cvr_upper_bound(session_data->get_spdm_ctx());
  double item_card_jichen_cvr_upper_bound =
        SPDM_item_card_jichen_cvr_upper_bound(session_data->get_spdm_ctx());
  bool enableInnerRoasCaliCloseTailExp = RankKconfUtil::enableInnerRoasCaliCloseTailExp();
  auto innerRoasCaliCloseTailSet = RankKconfUtil::innerRoasCaliCloseTailSet();
  exception_upper_bound = session_data->get_spdm_ctx().
                                               TryGetDouble("online_calibration_exception_upper_bound", 1.0);
  exception_lower_bound = session_data->get_spdm_ctx().
                                               TryGetDouble("online_calibration_exception_lower_bound", 1.0);
  enable_inner_order_pay_cali_ad_queue_type_hard =
                  SPDM_enable_inner_order_pay_cali_ad_queue_type(session_data->get_spdm_ctx());
  enable_inner_roas_cali_ad_queue_type_hard =
                  SPDM_enable_inner_roas_cali_ad_queue_type(session_data->get_spdm_ctx());
  enable_roas_online_calibration_by_stage_one_cmd =
      SPDM_enable_roas_online_calibration_by_stage_one_cmd(session_data->get_spdm_ctx());
  enable_inner_storewide_cali_ad_queue_type =
      SPDM_enable_inner_storewide_cali_ad_queue_type(session_data->get_spdm_ctx());

  bool enable_inner_cid_model_calibration_flow =
      SPDM_enable_inner_cid_model_calibration_flow(session_data->get_spdm_ctx());
  auto is_hosting = [&](auto& p_ad) -> bool {
    return (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_SINGLE_GOODS ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_CID_SUPER_PRODUCT ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_GOODS ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_OPTIONAL_GOODS);
  };
  auto cid_calibrate_tail_number = RankKconfUtil::innerCIDModelCalibrateTailNumber();
  auto bid_id_is_on = [&](auto& p_ad) -> bool {
    auto bid_id = is_hosting(p_ad)? p_ad->get_campaign_id(): p_ad->get_unit_id();
    return cid_calibrate_tail_number && cid_calibrate_tail_number->IsOnFor(bid_id);
  };
  std::unordered_map<std::string, double> gyl_predict_result_map;
  std::unordered_set<std::string> only_soft_set;
  std::unordered_map<std::string, std::vector<double>> gyl_roas_predict_map;

  bool enable_item_card_order_paied_cali =
       SPDM_enable_item_card_order_paied_cali(session_data->get_spdm_ctx());
  auto itemCardCaliMap = RankKconfUtil::itemCardCaliMap();

  if ((session_data->get_pos_manager_base().IsGuessYouLike() && enable_item_card_jichen) ||
      (session_data->get_pos_manager_base().IsShelfMerchantTraffic() &&
      enable_item_card_jichen)) {
    for (auto* p_ad : ads) {
      if (p_ad == nullptr) continue;
      // 目前仅作实验用，跳过老校准
      if (p_ad->get_is_skip_cali_before() == 1) {continue;}
      if (p_ad->get_creative_material_type() != kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD) {
        if (enable_jichen_cvr_upper_bound &&
           p_ad->get_unify_cvr_info().value > item_card_jichen_cvr_upper_bound) {
          continue;
        }
        std::string spu_cvr_key =
               absl::Substitute("cvr_spu_id_$0", absl::StrCat(p_ad->get_spu_id_v2()));
        if (enable_item_card_jichen_add_ocpc_action_type) {
          spu_cvr_key =
               absl::Substitute("cvr_spu_id_$0_ocpc_$1", absl::StrCat(p_ad->get_spu_id_v2()),
               p_ad->get_ocpx_action_type());
        }
        auto spu_cvr_iter = gyl_predict_result_map.find(spu_cvr_key);
        if (spu_cvr_iter != gyl_predict_result_map.end()) {
          gyl_predict_result_map[spu_cvr_key] = std::max(spu_cvr_iter->second, p_ad->get_unify_cvr_info().value);  // NOLINT
        } else {
          gyl_predict_result_map[spu_cvr_key] = p_ad->get_unify_cvr_info().value;
        }
        std::string author_spu_cvr_key = absl::Substitute("author_$0_cvr_spu_id_$1",
               absl::StrCat(p_ad->get_author_id()), absl::StrCat(p_ad->get_spu_id_v2()));
        if (enable_item_card_jichen_add_ocpc_action_type) {
          author_spu_cvr_key = absl::Substitute("author_$0_cvr_spu_id_$1_ocpc_$2",
               absl::StrCat(p_ad->get_author_id()), absl::StrCat(p_ad->get_spu_id_v2()),
               p_ad->get_ocpx_action_type());
        }
        if (enable_item_card_merchant_product_id_hard_jichen) {
          const std::string& merchant_product_id_cvr_key =
                  absl::Substitute("cvr_merchant_product_id_$0_ocpc_$1",
                  absl::StrCat(p_ad->get_merchant_product_id()),
                  p_ad->get_ocpx_action_type());
          auto merchant_product_id_cvr_iter = gyl_predict_result_map.find(merchant_product_id_cvr_key);
          if (merchant_product_id_cvr_iter != gyl_predict_result_map.end()) {
            gyl_predict_result_map[merchant_product_id_cvr_key] =
            std::max(merchant_product_id_cvr_iter->second, p_ad->get_unify_cvr_info().value);
          } else {
            gyl_predict_result_map[merchant_product_id_cvr_key] = p_ad->get_unify_cvr_info().value;
          }
        }
        auto author_spu_cvr_iter = gyl_predict_result_map.find(author_spu_cvr_key);
        if (author_spu_cvr_iter != gyl_predict_result_map.end()) {
          gyl_predict_result_map[author_spu_cvr_key] =
             std::max(author_spu_cvr_iter->second, p_ad->get_unify_cvr_info().value);
        } else {
          gyl_predict_result_map[author_spu_cvr_key] = p_ad->get_unify_cvr_info().value;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) {
          const std::string& roas_predict_key =
              absl::Substitute("roas_item_id_$0", absl::StrCat(p_ad->get_merchant_product_id()));
          auto roas_predict_iter = gyl_roas_predict_map.find(roas_predict_key);
          double current_cvltv = p_ad->get_unify_cvr_info().value * p_ad->get_unify_ltv_info().value;
          double cvr = p_ad->get_unify_cvr_info().value;
          double ltv = p_ad->get_unify_ltv_info().value;
          if (roas_predict_iter != gyl_roas_predict_map.end() &&
              roas_predict_iter->second.size() == 3) {
            double pred_cvltv = roas_predict_iter->second[0];
            if (current_cvltv > pred_cvltv) {
              roas_predict_iter->second[0] = current_cvltv;
              roas_predict_iter->second[1] = cvr;
              roas_predict_iter->second[2] = ltv;
            }
          } else {
            std::vector<double> tmp{current_cvltv, cvr, ltv};
            gyl_roas_predict_map[roas_predict_key] = tmp;
          }
        }
      }
    }
    if (enable_item_card_soft_queue_jichen) {
      for (auto& kv : session_data->get_gyl_soft_queue_predict_result_map()) {
        auto soft_iter = gyl_predict_result_map.find(kv.first);
        if (soft_iter != gyl_predict_result_map.end()) {
          gyl_predict_result_map[kv.first] = std::max(soft_iter->second, kv.second);
        } else {
          gyl_predict_result_map[kv.first] = kv.second;
          only_soft_set.insert(kv.first);
        }
      }
    }
    if (enable_item_card_roas_item_id_soft_jichen) {
      for (auto& kv : session_data->get_gyl_roas_soft_queue_predict_map()) {
        if (kv.second.size() != 3) {
          continue;
        }
        auto hard_iter = gyl_roas_predict_map.find(kv.first);
        if (hard_iter != gyl_roas_predict_map.end()) {
          if (kv.second[0] > hard_iter->second[0]) {
            gyl_roas_predict_map[kv.first] = kv.second;
          }
        } else {
          gyl_roas_predict_map[kv.first] = kv.second;
        }
      }
    }
  }
  for (auto* p_ad : ads) {
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad)
    // 硬广融合，过滤内循环硬广
    if (!session_data->get_is_search_request()) {
      // 24 策略重构 先对齐基线：仅对硬广队列生效，后续考虑做策略合并
      if (p_ad->get_ad_list_type() != 1) {continue;}
      if (p_ad->Is(AdFlag::is_inner_loop_ad)) {
        // cid 直接校准
        if (SPDM_enable_inner_cid_model_calibrate_control()
           && (enable_inner_cid_model_calibration_flow
              || bid_id_is_on(p_ad))) {
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)
            OnlineCalibrationWithCmdOrderPaied(session_data, p_ad, params_);
          if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)
            OnlineCalibrationWithCmdRoas(session_data, p_ad, params_);
        }
        if ((enbale_online_calibration_with_cmd_order_paied ||
             enable_new_online_calibration_with_cmd_order_paied) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
        OnlineCalibrationWithCmdOrderPaied(session_data, p_ad, params_);
        }
        if ((enbale_online_calibration_with_cmd_roas &&
            !disable_new_online_calibration_with_cmd_roas) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
          if (enableInnerRoasCaliCloseTailExp) {
            auto roas_ad_id = p_ad->get_unit_id();
            if (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_SINGLE_GOODS ||
                p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_CID_SUPER_PRODUCT ||
                p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::STORE_NEW_CUSTOMER_HOSTING ||
                p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_PRODUCT) {
                roas_ad_id = p_ad->get_campaign_id();
            }
            if (innerRoasCaliCloseTailSet->count(roas_ad_id % 100) > 0) {
                  continue;
            }
          }
          OnlineCalibrationWithCmdRoas(session_data, p_ad, params_);
        }
        if (enable_inner_normal_storewide_calibration &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
          OnlineCalibrationWithCmdStorewide(session_data, p_ad, params_);
        }
        // 固定坑位发现页校准
        // dingxiangkun AB test 下线无用校准策略
        if (!disable_useless_calibration_strategy) {
        if (enable_mobile_s2h_explore_cali &&
            exploreMobileSoftCaliMap != nullptr &&
            p_ad->get_is_mobile_soft_to_hard() == true &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) {
          double cali_rate = 1.0;
          std::string key = "default";
          if (session_data->get_page_id() == 10002) {
            key = p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO ? "PHOTO" : "P2L";
          } else if (enable_mobile_s2h_non_explore_cali) {
            key = p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO ? "PHOTO_NE" : "P2L_NE";
          }
          auto iter = exploreMobileSoftCaliMap->find(key);
          if (iter != exploreMobileSoftCaliMap->end()) {
            cali_rate = iter->second;
          }
          p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
        }
        }
        // 策略未生效状态，后续还可能会用
        if (enable_esp_aigc_order_paid_cali &&
            esp_inner_aigc_order_paid_cali_rate != 1.0 &&
            p_ad->get_creative_photo_source() == 1 &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) &&
            p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
          double cali_rate = esp_inner_aigc_order_paid_cali_rate;
          p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
        }
        // 策略未生效状态，后续还可能会用
        if (enable_esp_aigc_roas_cali &&
            esp_inner_aigc_roas_cali_rate != 1.0 &&
            p_ad->get_creative_photo_source() == 1 &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS &&
            p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
          double cali_rate = esp_inner_aigc_roas_cali_rate;
          p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
        }
        bool enable_cvr_cali = false;
        if (p_ad != nullptr) {
          p_ad->set_is_shelf_express_heritage(0);
        }
        if ((session_data->get_pos_manager_base().IsGuessYouLike() && enable_item_card_jichen) ||
            (session_data->get_pos_manager_base().IsShelfMerchantTraffic() &&
            enable_item_card_jichen)) {
          if (p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
              (p_ad->get_ocpx_action_type() != kuaishou::ad::AD_MERCHANT_ROAS
                   || !disable_item_card_jichen_roas) &&
                    (p_ad->get_spu_id_v2() != 0 || !disable_spu_0_jichen) &&
                    (p_ad->get_merchant_product_id() != 0 || !disable_merchant_product_id_0_jichen)) {
            RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.item_card_creative_count",
                     absl::StrCat(p_ad->get_ocpx_action_type()));
            if (enable_item_card_merchant_product_id_hard_jichen) {
              if (!enable_item_card_roas_item_id_hard_jichen ||
                   (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
                    p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) {
                const std::string& merchant_product_id_cvr_key =
                        absl::Substitute("cvr_merchant_product_id_$0_ocpc_$1",
                        absl::StrCat(p_ad->get_merchant_product_id()),
                        p_ad->get_ocpx_action_type());
                auto merchant_product_id_cvr_iter = gyl_predict_result_map.find(merchant_product_id_cvr_key);
                if (merchant_product_id_cvr_iter != gyl_predict_result_map.end()
                    && p_ad->get_unify_cvr_info().value > 0) {
                  double cali_rate = merchant_product_id_cvr_iter->second / p_ad->get_unify_cvr_info().value
                          + item_card_jichen_rate_delta;
                  p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
                  if (enable_shelf_express_cali_ratio) {
                    cali_rate *= shelf_express_cali_ratio;
                  }
                  p_ad->set_gyl_cvr_cali(cali_rate);
                  if (p_ad != nullptr) {
                    p_ad->set_is_shelf_express_heritage(1);
                  }
                  enable_cvr_cali = true;
                  RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.item_card_cvr_product_id_jichen_count",
                      absl::StrCat(p_ad->get_ocpx_action_type()));
                  auto soft_iter = only_soft_set.find(merchant_product_id_cvr_key);
                  if (soft_iter != only_soft_set.end()) {
                    RANK_DOT_COUNT(session_data, 1,
                    "ad.ad_rank.item_card_cvr_product_id_only_soft_jichen_count",
                      absl::StrCat(p_ad->get_ocpx_action_type()));
                  }
                }
              }
              if (enable_item_card_roas_item_id_hard_jichen &&
               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) {
                const std::string& roas_predict_key =
                  absl::Substitute("roas_item_id_$0", absl::StrCat(p_ad->get_merchant_product_id()));
                auto roas_iter = gyl_roas_predict_map.find(roas_predict_key);
                if (roas_iter != gyl_roas_predict_map.end() && p_ad->get_unify_ltv_info().value > 0
                    && p_ad->get_unify_cvr_info().value > 0) {
                  if (roas_iter->second.size() == 3) {
                    double jichen_cvr = roas_iter->second[1];
                    double jichen_ltv = roas_iter->second[2];
                    if (jichen_cvr > 0 && jichen_ltv > 0) {
                      double cali_ltv_rate = jichen_ltv / p_ad->get_unify_ltv_info().value
                          + item_card_jichen_rate_delta;
                      p_ad->ExcuteCalibration(RUnifyType::LTV, cali_ltv_rate);
                      double cali_cvr_rate = jichen_cvr / p_ad->get_unify_cvr_info().value
                          + item_card_jichen_rate_delta;
                      p_ad->ExcuteCalibration(RUnifyType::CVR, cali_cvr_rate);
                      if (enable_item_card_roas_jichen_fix) {
                        if (enable_shelf_express_cali_ratio) {
                          cali_cvr_rate *= shelf_express_cali_ratio;
                        }
                        p_ad->set_gyl_ltv_cali(cali_ltv_rate);
                        p_ad->set_gyl_cvr_cali(cali_cvr_rate);
                        if (p_ad != nullptr) {
                          p_ad->set_is_shelf_express_heritage(1);
                        }
                        enable_cvr_cali = true;
                      }
                    }
                    RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.item_card_roas_jichen_count");
                  }
                }
              }
            } else if (enable_item_card_spu_jichen && !disable_useless_calibration_strategy) {
              std::string spu_cvr_key =
                 absl::Substitute("cvr_spu_id_$0", absl::StrCat(p_ad->get_spu_id_v2()));
              if (enable_item_card_jichen_add_ocpc_action_type) {
                spu_cvr_key =
                    absl::Substitute("cvr_spu_id_$0_ocpc_$1", absl::StrCat(p_ad->get_spu_id_v2()),
                    p_ad->get_ocpx_action_type());
              }
              auto spu_cvr_iter = gyl_predict_result_map.find(spu_cvr_key);
              if (spu_cvr_iter != gyl_predict_result_map.end() && p_ad->get_unify_cvr_info().value > 0) {
                double cali_rate = spu_cvr_iter->second / p_ad->get_unify_cvr_info().value
                        + item_card_jichen_rate_delta;
                p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
                p_ad->set_gyl_cvr_cali(cali_rate);
                enable_cvr_cali = true;
                RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.item_card_cvr_spu_jichen_count",
                     absl::StrCat(p_ad->get_ocpx_action_type()));
                auto soft_iter = only_soft_set.find(spu_cvr_key);
                if (soft_iter != only_soft_set.end()) {
                  RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.item_card_cvr_spu_only_soft_jichen_count",
                     absl::StrCat(p_ad->get_ocpx_action_type()));
                }
              }
              if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) {
                const std::string& spu_ltv_key =
                 absl::Substitute("ltv_spu_id_$0", absl::StrCat(p_ad->get_spu_id_v2()));
                auto spu_ltv_iter = gyl_predict_result_map.find(spu_ltv_key);
                if (spu_ltv_iter != gyl_predict_result_map.end() && p_ad->get_unify_ltv_info().value > 0) {
                  double cali_rate = spu_ltv_iter->second / p_ad->get_unify_ltv_info().value
                          + item_card_jichen_rate_delta;
                  p_ad->ExcuteCalibration(RUnifyType::LTV, cali_rate);
                  p_ad->set_gyl_ltv_cali(cali_rate);
                  RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.item_card_ltv_spu_jichen_count");
                  auto soft_iter = only_soft_set.find(spu_ltv_key);
                  if (soft_iter != only_soft_set.end()) {
                    RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.item_card_ltv_spu_only_soft_jichen_count",
                      absl::StrCat(p_ad->get_ocpx_action_type()));
                  }
                }
              }
            } else if (enable_item_card_author_spu_jichen && !disable_useless_calibration_strategy) {
              std::string author_spu_cvr_key =  absl::Substitute("author_$0_cvr_spu_id_$1",
                    absl::StrCat(p_ad->get_author_id()), absl::StrCat(p_ad->get_spu_id_v2()));
              if (enable_item_card_jichen_add_ocpc_action_type) {
                author_spu_cvr_key = absl::Substitute("author_$0_cvr_spu_id_$1_ocpc_$2",
                    absl::StrCat(p_ad->get_author_id()), absl::StrCat(p_ad->get_spu_id_v2()),
                    p_ad->get_ocpx_action_type());
              }
              auto author_spu_cvr_iter = gyl_predict_result_map.find(author_spu_cvr_key);
              if (author_spu_cvr_iter != gyl_predict_result_map.end() && p_ad->get_unify_cvr_info().value > 0) {  // NOLINT
                double cali_rate = author_spu_cvr_iter->second / p_ad->get_unify_cvr_info().value
                        + item_card_jichen_rate_delta;
                p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
                p_ad->set_gyl_cvr_cali(cali_rate);
                enable_cvr_cali = true;
                RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.item_card_cvr_spu_author_jichen_count",
                     absl::StrCat(p_ad->get_ocpx_action_type()));
                auto soft_iter = only_soft_set.find(author_spu_cvr_key);
                if (soft_iter != only_soft_set.end()) {
                  RANK_DOT_COUNT(session_data, 1,
                   "ad.ad_rank.item_card_cvr_spu_author_only_soft_jichen_count",
                    absl::StrCat(p_ad->get_ocpx_action_type()));
                }
              }
              if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) {
                const std::string& author_spu_ltv_key =  absl::Substitute("author_$0_ltv_spu_id_$1",
                      absl::StrCat(p_ad->get_author_id()), absl::StrCat(p_ad->get_spu_id_v2()));
                auto author_spu_ltv_iter = gyl_predict_result_map.find(author_spu_ltv_key);
                if (author_spu_ltv_iter != gyl_predict_result_map.end()
                       && p_ad->get_unify_ltv_info().value > 0) {
                  double cali_rate = author_spu_ltv_iter->second / p_ad->get_unify_ltv_info().value
                           + item_card_jichen_rate_delta;
                  p_ad->ExcuteCalibration(RUnifyType::LTV, cali_rate);
                  p_ad->set_gyl_ltv_cali(cali_rate);
                  RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.item_card_ltv_spu_author_jichen_count");
                  auto soft_iter = only_soft_set.find(author_spu_ltv_key);
                  if (soft_iter != only_soft_set.end()) {
                    RANK_DOT_COUNT(session_data, 1,
                       "ad.ad_rank.item_card_ltv_spu_author_only_soft_jichen_count",
                      absl::StrCat(p_ad->get_ocpx_action_type()));
                  }
                }
              }
            }
            if (!enable_item_card_not_jichen_cali2 &&
                enable_item_card_not_jichen_cali && item_card_not_jichen_cali_rate > 0
              && !enable_cvr_cali) {
              p_ad->ExcuteCalibration(RUnifyType::CVR, item_card_not_jichen_cali_rate);
              p_ad->set_gyl_cvr_cali(-1 * item_card_not_jichen_cali_rate);
            }
          }
        }
        if (session_data->get_pos_manager_base().IsShelfMerchantTraffic() &&
          p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
          enable_item_card_not_jichen_cali2 && item_card_not_jichen_cali_rate > 0 && !enable_cvr_cali) {
          p_ad->ExcuteCalibration(RUnifyType::CVR, item_card_not_jichen_cali_rate);
          p_ad->set_gyl_cvr_cali(-1 * item_card_not_jichen_cali_rate);
        }
        bool is_new_creative = p_ad->Is(AdFlag::esp_new_creative);
        if (enable_jicheng_account_event_order_paied &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE
           && (is_new_creative || !enable_jicheng_account_only_for_cold)) {
          auto iter = jiChenAccountList->find(p_ad->get_account_id());
          if (iter != jiChenAccountList->end()) {
            auto new_account = iter->second;
            std::string jicheng_key = new_account + "_" +
                 kuaishou::ad::AdEnum_CampaignType_Name(p_ad->get_campaign_type())
                 + "_" + kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
            auto jicheng_iter = session_data->get_jicheng_account_pred_history_value().find(jicheng_key);
            if (jicheng_iter != session_data->get_jicheng_account_pred_history_value().end()
                && jicheng_iter->second > 0 && p_ad->get_unify_cvr_info().value > 0) {
              double cali_rate = jicheng_iter->second / p_ad->get_unify_cvr_info().value;
              p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
              RANK_DOT_COUNT(session_data, 1, "ad.ad_rank.order_paied_jicheng_count");
            }
          }
        }
      } else {
        if (enbale_online_calibration_with_cmd) {
          OnlineCalibrationWithCmd(session_data, p_ad, params_);
        }
      }
    }
}
  return StraRetCode::SUCC;
}

const char* ShelfMerchantOfflineCalibrationPlugin::Name() {
  return "ShelfMerchantOfflineCalibration";
}

void ShelfMerchantOfflineCalibrationPlugin::Clear() {}

bool ShelfMerchantOfflineCalibrationPlugin::IsRun(const ContextData* session_data, const Params* params,
                                         AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode ShelfMerchantOfflineCalibrationPlugin::Process(ContextData* session_data,
                                                  Params* params,
                                                  AdRankUnifyScene pos,
                                                  AdList* ad_list) {
  if (ad_list == nullptr || ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }
  if (!session_data->get_pos_manager_base().IsShelfMerchantTraffic()) {
    return StraRetCode::SUCC;
  }

  bool enable_shelf_merchant_offline_cvr_cali =
       SPDM_enable_shelf_merchant_offline_cvr_cali(session_data->get_spdm_ctx());
  bool enable_shelf_p2l_offline_cali =
       SPDM_enable_shelf_p2l_offline_cali(session_data->get_spdm_ctx());
  bool enable_shelf_photo_offline_cali =
       SPDM_enable_shelf_photo_offline_cali(session_data->get_spdm_ctx());
  bool enable_gyl_feed_offline_cali =
       SPDM_enable_gyl_feed_offline_cali(session_data->get_spdm_ctx());
  auto& ads = ad_list->Ads();
  for (auto* p_ad : ads) {
    if (p_ad == nullptr) {
      continue;
    }
    // 目前仅作实验用，跳过老校准
    if (p_ad->get_is_skip_cali_before() == 1) {continue;}
    if (p_ad->Is(AdFlag::is_outer_loop_ad)) {
      continue;
    }
    if (p_ad->Is(AdFlag::is_photo) && enable_shelf_merchant_offline_cvr_cali) {
      ShelfMerchantOfflineCvrCali(session_data, p_ad);
    }
    if (p_ad->Is(AdFlag::is_p2l_ad_inner) && enable_shelf_p2l_offline_cali) {
      ShelfP2lOfflineCali(session_data, p_ad);
    }
    if (p_ad->Is(AdFlag::is_photo) && enable_shelf_photo_offline_cali) {
      ShelfPhotoOfflineCali(session_data, p_ad);
    }
    if (session_data->get_pos_manager_base().IsGuessYouLike() &&
         session_data->get_is_feed() &&
         enable_gyl_feed_offline_cali) {
      ShelfGylFeedOfflineCali(session_data, p_ad);
    }
  }
  return StraRetCode::SUCC;
}

void ShelfMerchantOfflineCalibrationPlugin::ShelfPhotoOfflineCali(
    ContextData* session_data, AdCommon* p_ad) {
  std::string page_tag = "";
  if (session_data->get_pos_manager_base().IsGuessYouLike()) {
    page_tag = "gyl";
  } else if (session_data->get_pos_manager_base().IsMallTraffic()) {
    page_tag = "mall";
  } else if (session_data->get_pos_manager_base().IsBuyerHomePageTraffic()) {
    page_tag = "bh";
  } else if (session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    page_tag = "zq";
  }
  auto ocpx_action_type = p_ad->get_ocpx_action_type();
  auto& ocpx_action_type_name = kuaishou::ad::AdActionType_Name(ocpx_action_type);
  std::string ctr_page_type_key = absl::StrCat("ctr-", page_tag,  "-", ocpx_action_type_name);
  std::string ctr_page_default_key = absl::StrCat("ctr-", page_tag,  "-", "all");
  std::string cvr_page_type_key = absl::StrCat("cvr-", page_tag,  "-", ocpx_action_type_name);
  std::string cvr_page_default_key = absl::StrCat("cvr-", page_tag,  "-", "all");
  std::string ltv_page_type_key = absl::StrCat("ltv-", page_tag,  "-", ocpx_action_type_name);
  std::string ltv_page_default_key = absl::StrCat("ltv-", page_tag,  "-", "all");
  auto shelfPhotoMingtouOfflineCaliMap = RankKconfUtil::shelfPhotoMingtouOfflineCaliMap();
  auto shelfPhotoOfflineCaliMap = RankKconfUtil::shelfPhotoAntouOfflineCaliMap();
  if (p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD) {
    shelfPhotoOfflineCaliMap = shelfPhotoMingtouOfflineCaliMap;
  }
  if (shelfPhotoOfflineCaliMap == nullptr) {
    return;
  }
  double ctr_cali_rate = 1.0;
  double cvr_cali_rate = 1.0;
  double ltv_cali_rate = 1.0;
  // ctr
  if (session_data->get_is_feed()) {
    if (auto iter_res = shelfPhotoOfflineCaliMap->find(ctr_page_type_key);
             iter_res != shelfPhotoOfflineCaliMap->end()) {
      ctr_cali_rate = iter_res->second;
    } else if (auto iter_res = shelfPhotoOfflineCaliMap->find(ctr_page_default_key);
             iter_res != shelfPhotoOfflineCaliMap->end()) {
      ctr_cali_rate = iter_res->second;
    }
  }
  if (ctr_cali_rate != 1.0 && ctr_cali_rate > 0) {
    p_ad->ShelfMerchantExcuteCalibrationNew(RUnifyType::CTR, ctr_cali_rate);
    p_ad->set_shelf_offline_ctr_cali_rate(ctr_cali_rate);
  }
  // cvr
  if (auto iter_res = shelfPhotoOfflineCaliMap->find(cvr_page_type_key);
           iter_res != shelfPhotoOfflineCaliMap->end()) {
    cvr_cali_rate = iter_res->second;
  } else if (auto iter_res = shelfPhotoOfflineCaliMap->find(cvr_page_default_key);
           iter_res != shelfPhotoOfflineCaliMap->end()) {
    cvr_cali_rate = iter_res->second;
  }
  if (cvr_cali_rate != 1.0 && cvr_cali_rate > 0) {
    p_ad->ShelfMerchantExcuteCalibration(RUnifyType::CVR, cvr_cali_rate);
    p_ad->set_shelf_offline_cvr_cali_rate(cvr_cali_rate);
  }
  // ltv
  if (p_ad->Is(AdFlag::is_reco_roas)) {
    if (auto iter_res = shelfPhotoOfflineCaliMap->find(ltv_page_type_key);
             iter_res != shelfPhotoOfflineCaliMap->end()) {
      ltv_cali_rate = iter_res->second;
    } else if (auto iter_res = shelfPhotoOfflineCaliMap->find(ltv_page_default_key);
             iter_res != shelfPhotoOfflineCaliMap->end()) {
      ltv_cali_rate = iter_res->second;
    }
  }
  if (ltv_cali_rate != 1.0 && ltv_cali_rate > 0) {
    p_ad->ShelfMerchantExcuteCalibration(RUnifyType::LTV, ltv_cali_rate);
    p_ad->set_shelf_offline_ltv_cali_rate(ltv_cali_rate);
  }
  return;
}

void ShelfMerchantOfflineCalibrationPlugin::ShelfGylFeedOfflineCali(
    ContextData* session_data, AdCommon* p_ad) {
  std::string item_type_tag = "";
  if (p_ad->Is(AdFlag::is_photo)) {
    item_type_tag = "photo";
  } else if (p_ad->Is(AdFlag::is_p2l)) {
    item_type_tag = "p2l";
  } else if (p_ad->Is(AdFlag::is_live)) {
    item_type_tag = "live";
  }
  std::string ctr_type_key = absl::StrCat("ctr-", item_type_tag);
  std::string cvr_type_key = absl::StrCat("cvr-", item_type_tag);
  std::string ltv_type_key = absl::StrCat("ltv-", item_type_tag);
  auto shelfGylFeedOfflineCaliMap = RankKconfUtil::shelfGylFeedOfflineCaliMap();
  if (shelfGylFeedOfflineCaliMap == nullptr) {
    return;
  }
  double ctr_cali_rate = 1.0;
  double cvr_cali_rate = 1.0;
  double ltv_cali_rate = 1.0;
  // ctr
  if (auto iter_res = shelfGylFeedOfflineCaliMap->find(ctr_type_key);
           iter_res != shelfGylFeedOfflineCaliMap->end()) {
    ctr_cali_rate = iter_res->second;
  }
  // cvr
  if (p_ad->Is(AdFlag::is_photo_or_p2l) ||
      p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ORDER_PAIED) {
    if (auto iter_res = shelfGylFeedOfflineCaliMap->find(cvr_type_key);
             iter_res != shelfGylFeedOfflineCaliMap->end()) {
      cvr_cali_rate = iter_res->second;
    }
  }
  // ltv
  if (p_ad->Is(AdFlag::is_reco_roas) || p_ad->Is(AdFlag::is_merchant_live_roas)) {
    if (auto iter_res = shelfGylFeedOfflineCaliMap->find(ltv_type_key);
             iter_res != shelfGylFeedOfflineCaliMap->end()) {
      ltv_cali_rate = iter_res->second;
    }
  }
  if (ctr_cali_rate != 1.0 && ctr_cali_rate > 0) {
    if (p_ad->Is(AdFlag::is_live)) {
      p_ad->ShelfLiveExcuteCalibration(RUnifyType::CTR, ctr_cali_rate);
    } else {
      p_ad->ShelfMerchantExcuteCalibrationNew(RUnifyType::CTR, ctr_cali_rate);
    }
    p_ad->set_gyl_feed_ctr_cali_rate(ctr_cali_rate);
  }
  if (cvr_cali_rate != 1.0 && cvr_cali_rate > 0) {
    if (p_ad->Is(AdFlag::is_live)) {
      p_ad->ShelfLiveExcuteCalibration(RUnifyType::CVR, cvr_cali_rate);
    } else {
      p_ad->ShelfMerchantExcuteCalibration(RUnifyType::CVR, cvr_cali_rate);
    }
    p_ad->set_gyl_feed_cvr_cali_rate(cvr_cali_rate);
  }
  if (ltv_cali_rate != 1.0 && ltv_cali_rate > 0) {
    if (p_ad->Is(AdFlag::is_live)) {
      p_ad->ShelfLiveExcuteCalibration(RUnifyType::LTV, ltv_cali_rate);
    } else {
      p_ad->ShelfMerchantExcuteCalibration(RUnifyType::LTV, ltv_cali_rate);
    }
    p_ad->set_gyl_feed_ltv_cali_rate(ltv_cali_rate);
  }
  return;
}

void ShelfMerchantOfflineCalibrationPlugin::ShelfP2lOfflineCali(
    ContextData* session_data, AdCommon* p_ad) {
  std::string page_tag = "";
  if (session_data->get_pos_manager_base().IsGuessYouLike()) {
    page_tag = "gyl";
  } else if (session_data->get_pos_manager_base().IsMallTraffic()) {
    page_tag = "mall";
  } else if (session_data->get_pos_manager_base().IsBuyerHomePageTraffic()) {
    page_tag = "bh";
  } else if (session_data->get_pos_manager_base().IsZhuanQianTraffic()) {
    page_tag = "zq";
  }
  auto ocpx_action_type = p_ad->get_ocpx_action_type();
  auto& ocpx_action_type_name = kuaishou::ad::AdActionType_Name(ocpx_action_type);
  std::string page_type_key = absl::StrCat(page_tag,  "-", ocpx_action_type_name);
  std::string page_default_key = absl::StrCat(page_tag,  "-", "all");
  auto shelfP2lCvrCaliMap = RankKconfUtil::shelfP2lOfflineCvrCaliMap();
  auto shelfP2lLtvCaliMap = RankKconfUtil::shelfP2lOfflineLtvCaliMap();
  if (shelfP2lCvrCaliMap == nullptr && shelfP2lLtvCaliMap == nullptr) {
    return;
  }
  double cvr_cali_rate = 1.0;
  double ltv_cali_rate = 1.0;
  if (shelfP2lCvrCaliMap != nullptr) {
    if (auto iter_res = shelfP2lCvrCaliMap->find(page_type_key);
             iter_res != shelfP2lCvrCaliMap->end()) {
      cvr_cali_rate = iter_res->second;
    } else if (auto iter_res = shelfP2lCvrCaliMap->find(page_default_key);
             iter_res != shelfP2lCvrCaliMap->end()) {
      cvr_cali_rate = iter_res->second;
    }
  }
  if (cvr_cali_rate != 1.0 && cvr_cali_rate > 0) {
    p_ad->ShelfMerchantExcuteCalibration(RUnifyType::CVR, cvr_cali_rate);
  }
  if (shelfP2lLtvCaliMap != nullptr && p_ad->Is(AdFlag::is_merchant_live_roas)) {
    if (auto iter_res = shelfP2lLtvCaliMap->find(page_type_key);
             iter_res != shelfP2lLtvCaliMap->end()) {
      ltv_cali_rate = iter_res->second;
    } else if (auto iter_res = shelfP2lLtvCaliMap->find(page_default_key);
             iter_res != shelfP2lLtvCaliMap->end()) {
      ltv_cali_rate = iter_res->second;
    }
  }
  if (ltv_cali_rate != 1.0 && ltv_cali_rate > 0) {
    p_ad->ShelfMerchantExcuteCalibration(RUnifyType::LTV, ltv_cali_rate);
  }
  p_ad->set_cali_rate_turn(cvr_cali_rate * ltv_cali_rate);
  return;
}

void ShelfMerchantOfflineCalibrationPlugin::ShelfMerchantOfflineCvrCali(
    ContextData* session_data, AdCommon* p_ad) {
  auto itemCardCmdPageCaliMap = RankKconfUtil::itemCardCmdPageCaliMap();
  auto itemCardCmdPageCaliMapV1 = RankKconfUtil::itemCardCmdPageCaliMapV1();
  const auto &shelf_cmd_page_cali_exp_tag =
              SPDM_shelf_cmd_page_cali_exp_tag(session_data->get_spdm_ctx());
  if (shelf_cmd_page_cali_exp_tag == "v1") {
    itemCardCmdPageCaliMap = itemCardCmdPageCaliMapV1;
  }
  if (itemCardCmdPageCaliMap == nullptr) {
    return;
  }
  int32_t cmd_id = 0;
  int64_t page_id = session_data->get_page_id();
  if (p_ad->get_queue_type() == RankAdListType::NATIVE_AD ||
      p_ad->get_queue_type() == RankAdListType::FANSTOP) {
    if (p_ad->get_order_paid() > 0) {
      cmd_id = p_ad->get_order_paid_cmd_id();
    } else {
      return;
    }
  } else if (p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) {
    if (p_ad->get_c1_order_paied() > 0) {
      cmd_id = p_ad->get_c1_order_paied_cmd_id();
    } else {
      return;
    }
  } else {
    return;
  }
  std::string cmd_page_key = absl::StrCat(cmd_id, "-", page_id);
  auto cmd_page_res = itemCardCmdPageCaliMap->find(cmd_page_key);
  if (cmd_page_res != itemCardCmdPageCaliMap->end()) {
    double cali_rate = cmd_page_res->second;
    if (cali_rate != 1.0 && cali_rate > 0) {
      p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                    "ad_rank.shele_merchant_offline_cvr_calibration",
                    cmd_page_key);
    }
  }
  return;
}

const char* CvrOfflineCalibratePlugin::Name() {
  return "CvrOfflineCalibratePlugin";
}

void CvrOfflineCalibratePlugin::Clear() {}

bool CvrOfflineCalibratePlugin::IsRun(const ContextData* session_data, const Params* params,
                                         AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CvrOfflineCalibratePlugin::Process(ContextData* session_data,
                                                  Params* params,
                                                  AdRankUnifyScene pos,
                                                  AdList* ad_list) {
  if (session_data->get_is_search_request()) {
    return StraRetCode::SUCC;
  }
  if (ad_list == nullptr || ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }

  auto globalRateThreshold = RankKconfUtil::GlobalRateThreshold();
  offline_rate_intervention = RankKconfUtil::OfflineRateIntervention();
  auto itr_min = globalRateThreshold->find("global_min");
  global_lower_bound = (itr_min != globalRateThreshold->end()) ? itr_min->second : 0.0;
  auto itr_max = globalRateThreshold->find("global_max");
  global_upper_bound = (itr_max != globalRateThreshold->end()) ? itr_max->second : 1000.0;

  auto& ads = ad_list->Ads();
  for (auto* p_ad : ads) {
    if (p_ad == nullptr) continue;
    // 目前仅作实验用，跳过老校准
    if (p_ad->get_is_skip_cali_before() == 1) {continue;}
    if (p_ad->Is(AdFlag::is_outer_loop_ad)) {
      CvrOfflineCalibrate(session_data, p_ad);
    }
}
  return StraRetCode::SUCC;
}

void CvrOfflineCalibratePlugin::CvrOfflineCalibrate(
    ContextData* session_data, AdCommon* p_ad) {

  int64_t page_id = session_data->get_page_id();

  std::string modelstart = p_ad->get_offcali_cvr_modelstart();
  std::string modelend = p_ad->get_offcali_cvr_modelend();
  std::string query_key = p_ad->GetOfflineCalibrationKey(page_id, modelstart, modelend);
  double cali_rate = p_ad->GetOfflineCalibrateValue(query_key);

  if (cali_rate >= global_upper_bound) {
    cali_rate = global_upper_bound;
  }
  if (cali_rate <= global_lower_bound) {
    cali_rate = global_lower_bound;
  }

  std::string pre = absl::StrCat(session_data->get_page_id(), "#",
                                 p_ad->get_ocpx_action_type(), "#",
                                 p_ad->get_unify_cvr_info().s_type, "#",
                                 p_ad->get_unify_cvr_info().e_type);
  std::vector<std::string> cali_intervention_keys =
                                 {absl::StrCat(pre, "_account_", p_ad->get_account_id()),
                                  absl::StrCat(pre, "_product_", p_ad->get_product_name()),
                                  absl::StrCat(pre, "_industry_", p_ad->get_industry_id_v3()),
                                  absl::StrCat(pre, "_default")};

  for (auto key_tmp : cali_intervention_keys) {
      auto iter = offline_rate_intervention->find(key_tmp);
      if (iter != offline_rate_intervention->end()) {
        cali_rate = iter->second;
        break;
      }
  }

  if (cali_rate == 1.0) {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.offline_calibration_cali_rate_one");
  } else if (cali_rate == 0) {
    RANK_DOT_COUNT(session_data, 1,
                   "ad_rank.offline_calibration_cali_rate_zero");
  }

  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (cali_rate != 1.0 && cali_rate > 0) {
    p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
    p_ad->set_cali_rate_turn(cali_rate);
    RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                    "ad_rank.offline_calibration_cali_rate",
                    modelstart + "_" + modelend,
                    action_type);
    RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.offline_calibration_cali_rate_count",
                    modelstart + "_" + modelend,
                    action_type);
    if (cali_rate > 1.0) {
      RANK_DOT_COUNT(
          session_data, 1,
          "ad_rank.offline_calibration_cali_rate_count_up",
          modelstart + "_" + modelend,
          action_type);
    } else {
      RANK_DOT_COUNT(
          session_data, 1,
          "ad_rank.offline_calibration_cali_rate_count_down",
          modelstart + "_" + modelend,
          action_type);
    }
  }
}

const char* CalibrationFeedSctrPlugin::Name() {
  return "CalibrationFeedSctrPlugin";
}

void CalibrationFeedSctrPlugin::Clear() {}

bool CalibrationFeedSctrPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CalibrationFeedSctrPlugin::Process(ContextData* session_data, Params* params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  if (!(SPDM_enable_feed_explore_sctr_calibrate(session_data->get_spdm_ctx()) &&
        ks::ad_base::IsFeedExploreRequest(session_data->get_sub_page_id()))) {
    return StraRetCode::SUCC;
  }
  if (ad_list == nullptr || ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }
  auto cali_conf = RankKconfUtil::feedSctrCalibrationConf();
  if (cali_conf == nullptr) {
    return StraRetCode::SUCC;
  }
  const std::string& exp_tag = SPDM_feed_explore_sctr_calibrate_tag(session_data->get_spdm_ctx());
  double feed_sctr_cali_upper_bound = SPDM_feed_sctr_cali_upper_bound(session_data->get_spdm_ctx());
  double feed_sctr_cali_lower_bound = SPDM_feed_sctr_cali_lower_bound(session_data->get_spdm_ctx());
  auto sub_page_id = session_data->get_sub_page_id();
  for (auto* p_ad : ad_list->Ads()) {
    if (p_ad == nullptr) continue;
    // 目前仅作实验用，跳过老校准
    if (p_ad->get_is_skip_cali_before() == 1) {continue;}
    std::string campaign_type = kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type());
    std::string ad_queue_type = kuaishou::ad::AdEnum::AdQueueType_Name(p_ad->get_ad_queue_type());
    std::string item_type = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
    std::string key = absl::StrCat(exp_tag, "|", sub_page_id, "|",
                                   item_type, "|", ad_queue_type, "|", campaign_type);
    double cali_rate = 1.0;
    const auto& iter = cali_conf->find(key);
    if (iter != cali_conf->end()) {
      cali_rate = iter->second;
    }
    cali_rate = std::max(std::min(cali_rate, feed_sctr_cali_upper_bound), feed_sctr_cali_lower_bound);
    if (cali_rate != 1.0 && cali_rate > 0) {
      p_ad->ExcuteCalibration(RUnifyType::CTR, cali_rate);
      p_ad->set_cali_rate_turn(cali_rate);
      std::string loop_type = p_ad->Is(AdFlag::is_inner_loop_ad) ? "inner" : "outer";
      RANK_DOT_STATS(session_data, static_cast<int64_t>(1000 * cali_rate),
                     "ad_rank.feed_sctr_cali_rate",
                     exp_tag, item_type, loop_type);
    }
  }
  return StraRetCode::SUCC;
}

const char* CalibrationModelCvrPlugin::Name() {
  return "CalibrationModelCvrPlugin";
}

void CalibrationModelCvrPlugin::Clear() {}

bool CalibrationModelCvrPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return  true;
}


double CalibrationModelCvrPlugin::ComputeCalibCvrByTopLayerEmb(ContextData* session_data,
                                                              AdCommon* p_ad,
                                                              double cvr_input,
                                                              const std::string& campaign_type,
                                                              const std::string& ocpc_action_type,
                                                              const std::string& invo_traffic_cali_exp_tag) {
    // ====== 获取模型输出 embedding_attr ======
    auto* embedding_attr = p_ad->get_predict_embedding_attr(
        ks::engine_base::PredictEmbeddingType::PredictEmbeddingType_invo_traffic_cali_top_layer_emb);

    if (!embedding_attr) {
        RANK_DOT_COUNT(session_data, 1,
                        "invo_traffic_cali_top_layer_emb_predict",
                        "embedding_attr_null",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
        return -1.0;
    }
    int32_t model_embedding_size =
      SPDM_invo_traffic_cali_model_emb_size(session_data->get_spdm_ctx());
    if (embedding_attr->float_list_value_size() != model_embedding_size) {
        RANK_DOT_COUNT(session_data, 1,
                        "invo_traffic_cali_top_layer_emb_predict",
                        "embedding_attr_size_error",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
        LOG(ERROR) << "[ComputeCalibCvrByTopLayerEmb] embedding_attr size error, "
                   << "expected size: 790, "
                   << "actual size: "
                   << embedding_attr->float_list_value_size()
                   << ", campaign_type: "
                   << campaign_type
                   << ", ocpc_action_type: "
                   << ocpc_action_type
                   << ", invo_traffic_cali_exp_tag: "
                   << invo_traffic_cali_exp_tag;
        return -1.0;
    }

    // ====== embedding_attr 解析 ======
    int32_t sparse_output_dim = 16;
    int32_t w0_in = 1, w0_out = 32;
    int32_t b0_dim = 32;
    int32_t w1_in = 32, w1_out = 16;
    int32_t b1_dim = 16;
    int32_t fc_w_in = 16, fc_w_out = 5;
    int32_t fc_b_dim = 5;
    int32_t emb_row = 5, emb_col = 16;
    int32_t final_w_in = 16, final_w_out = 1;
    int32_t final_b_dim = 1;

    int32_t offset = 0;
    std::vector<double> sparse_output;
    std::vector<std::vector<double>> cvr_sparse_dnn_w_0;
    std::vector<double> cvr_sparse_dnn_b_0;
    std::vector<std::vector<double>> cvr_sparse_dnn_w_1;
    std::vector<double> cvr_sparse_dnn_b_1;
    std::vector<std::vector<double>> fc_cvr_sparse_w;
    std::vector<double> fc_cvr_sparse_b;
    std::vector<std::vector<double>> cvr_sparse_embeddings;
    std::vector<std::vector<double>> fc_final_layer_w;
    std::vector<double> fc_final_layer_b;

    auto get_1d = [&](int32_t len) {
        std::vector<double> v;
        for (int32_t i = 0; i < len; ++i) {
            v.push_back(embedding_attr->float_list_value(offset + i));
        }
        offset += len;
        return v;
    };
    auto get_2d = [&](int32_t row, int32_t col) {
        std::vector<std::vector<double>> m(row, std::vector<double>(col));
        for (int32_t i = 0; i < row; ++i)
            for (int32_t j = 0; j < col; ++j) {
                m[i][j] = embedding_attr->float_list_value(offset++);
            }
        return m;
    };

    // 切分参数
    sparse_output = get_1d(sparse_output_dim);
    cvr_sparse_dnn_w_0 = get_2d(w0_in, w0_out);
    cvr_sparse_dnn_b_0 = get_1d(b0_dim);
    cvr_sparse_dnn_w_1 = get_2d(w1_in, w1_out);
    cvr_sparse_dnn_b_1 = get_1d(b1_dim);
    fc_cvr_sparse_w = get_2d(fc_w_in, fc_w_out);
    fc_cvr_sparse_b = get_1d(fc_b_dim);
    cvr_sparse_embeddings = get_2d(emb_row, emb_col);
    fc_final_layer_w = get_2d(final_w_in, final_w_out);
    fc_final_layer_b = get_1d(final_b_dim);

    if (offset != embedding_attr->float_list_value_size()) {
        RANK_DOT_COUNT(session_data, 1,
                        "invo_traffic_cali_top_layer_emb_predict",
                        "embedding_attr_size_error_offset",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
        LOG(ERROR) << "[ComputeCalibCvrByTopLayerEmb] embedding_attr size error, "
                   << "offset: " << offset
                   << ", embedding_attr size: "
                   << embedding_attr->float_list_value_size()
                   << ", campaign_type: "
                   << campaign_type
                   << ", ocpc_action_type: "
                   << ocpc_action_type
                   << ", invo_traffic_cali_exp_tag: "
                   << invo_traffic_cali_exp_tag;
        return -1.0;
    }

    // ====== 工具函数实现 ======
    auto sigmoid = [](double x) {
        return 1.0 / (1.0 + std::exp(-x));
    };
    auto activation = [](const std::vector<double>& x, const std::string& act) {
        // x: [dim] -> result: [dim]
        if (x.empty()) {
            return std::vector<double>();
        }
        std::vector<double> result(x.size());
        if (act == "tanh") {
            for (size_t i = 0; i < x.size(); ++i) {
                result[i] = std::tanh(x[i]);
            }
        } else if (act == "relu") {
            for (size_t i = 0; i < x.size(); ++i) {
                result[i] = std::max(0.0, x[i]);
            }
        } else if (act == "softmax") {
            double max_elem = *std::max_element(x.begin(), x.end());
            double sum = 0.0;
            for (size_t i = 0; i < x.size(); ++i) {
                result[i] = std::exp(x[i] - max_elem);
                sum += result[i];
            }
            for (size_t i = 0; i < x.size(); ++i) {
                result[i] /= sum;
            }
        } else {
            for (size_t i = 0; i < x.size(); ++i) {
                result[i] = x[i];
            }
        }
        return result;
    };
    auto matmul = [](const std::vector<double>& input, const std::vector<std::vector<double>>& w) {
        // input: [in_dim], w: [in_dim, out_dim] -> result: [out_dim]
        if (input.empty() || w.empty() || w[0].empty()) {
            return std::vector<double>();
        }
        std::vector<double> result(w[0].size(), 0.0);
        for (size_t j = 0; j < w[0].size(); ++j) {
            for (size_t i = 0; i < input.size(); ++i) {
                result[j] += input[i] * w[i][j];
            }
        }
        return result;
    };
    auto add = [](const std::vector<double>& a, const std::vector<double>& b) {
        // a: [dim], b: [dim] -> result: [dim]
        if (a.empty() || b.empty() || a.size() != b.size()) {
            return std::vector<double>();
        }
        std::vector<double> result(a.size());
        for (size_t i = 0; i < a.size(); ++i) {
            result[i] = a[i] + b[i];
        }
        return result;
    };
    auto fc = [&](const std::vector<double>& input, const std::vector<std::vector<double>>& w,
                    const std::vector<double>& b, const std::string& act) {
        // input: [in_dim], w: [in_dim, out_dim], b: [out_dim] -> result: [out_dim]
        if (input.empty() || w.empty() || b.empty()) {
            return std::vector<double>();
        }
        auto o = add(matmul(input, w), b);
        if (act == "none" || act == "null") {
            return o;
        }
        return activation(o, act);
    };
    auto clip = [](double x, double min_v, double max_v) {
        return std::max(min_v, std::min(max_v, x));
    };
    // cvr_sparse_input
    auto cvr_sparse_input = [&](double cvr_input_val) {
        // 输入为单个 double，先扩展为 1 维
        // input: [1]
        std::vector<double> input = {static_cast<double>(cvr_input_val)};
        // 第一层: input [1], w0 [1,32], b0 [32] -> o1 [32]
        auto o1 = fc(input, cvr_sparse_dnn_w_0, cvr_sparse_dnn_b_0, "tanh");
        // 第二层: o1 [32], w1 [32,16], b1 [16] -> o2 [16]
        auto o2 = fc(o1, cvr_sparse_dnn_w_1, cvr_sparse_dnn_b_1, "tanh");
        // fc softmax: o2 [16], fc_w [16,5], fc_b [5] -> o3 [5]
        auto o3 = fc(o2, fc_cvr_sparse_w, fc_cvr_sparse_b, "softmax");
        // matmul with embedding: o3 [5], emb [5,16] -> out [16]
        auto out = matmul(o3, cvr_sparse_embeddings);
        // out: [16]
        return out;
    };

    // ====== 主推理流程 ======
    // sparse_output: [16]
    // cvr_input 需要 clip
    double cvr_input_clipped = clip(cvr_input, std::exp(-15.0), std::exp(15.0));
    // 检查除零问题
    double denominator = 1.0 - cvr_input_clipped;
    if (denominator == 0.0) {
        RANK_DOT_COUNT(session_data, 1,
                        "invo_traffic_cali_top_layer_emb_predict",
                        "1_cvr_input_clipped_zero_denominator",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
        return -1.0;
    }
    double main_logit = clip(std::log(cvr_input_clipped / denominator), -15.0, 15.0);
    // cvr_sparse_input_vec: [16]
    auto cvr_sparse_input_vec = cvr_sparse_input(cvr_input_clipped);
    // dnn_input = sparse_output * cvr_sparse_input_vec: [16] * [16] -> [16]
    if (sparse_output.empty()) {
        RANK_DOT_COUNT(session_data, 1,
                        "invo_traffic_cali_top_layer_emb_predict",
                        "sparse_output_empty",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
        return -1.0;
    }
    if (cvr_sparse_input_vec.empty()) {
        RANK_DOT_COUNT(session_data, 1,
                        "invo_traffic_cali_top_layer_emb_predict",
                        "cvr_sparse_input_vec_empty",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
        return -1.0;
    }
    if (sparse_output.size() != cvr_sparse_input_vec.size()) {
        RANK_DOT_COUNT(session_data, 1,
                        "invo_traffic_cali_top_layer_emb_predict",
                        "sparse_output_cvr_sparse_input_vec_size_mismatch",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
        return -1.0;
    }
    std::vector<double> dnn_input(sparse_output.size());
    for (size_t i = 0; i < sparse_output.size(); ++i) {
        dnn_input[i] = sparse_output[i] * cvr_sparse_input_vec[i];
    }
    // final layer: dnn_input [16], fc_final_layer_w [16,1], fc_final_layer_b [1] -> logits_vec [1]
    auto logits_vec = fc(dnn_input, fc_final_layer_w, fc_final_layer_b, "none");
    if (logits_vec.empty()) {
        RANK_DOT_COUNT(session_data, 1,
                        "invo_traffic_cali_top_layer_emb_predict",
                        "logits_vec_empty",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
        return -1.0;
    }
    double logits = clip(main_logit + logits_vec[0], -15.0, 15.0);
    double prob = sigmoid(logits);
    return prob;
}
StraRetCode CalibrationModelCvrPlugin::Process(ContextData* session_data, Params* params,
                                         AdRankUnifyScene pos, AdList* ad_list) {
  if (!(SPDM_enable_feed_explore_cvr_calibrate(session_data->get_spdm_ctx()) &&
        (ks::ad_base::IsFeedExploreRequest(session_data->get_sub_page_id()) ||
        ks::ad_base::IsInnerExploreRequest(session_data->get_sub_page_id())))) {
    return StraRetCode::SUCC;
  }

  if (ad_list == nullptr || ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }

  RANK_DOT_COUNT(session_data, 1,
              "ad_rank.begin_calc_feed_explore_cvr_cali_rate",
              std::to_string(session_data->get_sub_page_id()));

  double alpha = SPDM_feed_explore_cvr_cali_alpha(session_data->get_spdm_ctx());
  double beta = SPDM_feed_explore_cvr_cali_beta(session_data->get_spdm_ctx());
  double upper_bound = SPDM_feed_explore_cvr_cali_upper_bound(session_data->get_spdm_ctx());
  double lower_bound = SPDM_feed_explore_cvr_cali_lower_bound(session_data->get_spdm_ctx());
  auto cali_conf = RankKconfUtil::cvrCaliModelConfigKey();
  if (cali_conf == nullptr) {
    return StraRetCode::SUCC;
  }

  auto valid_cali_method = SPDM_enable_cvr_sigmoid_calibrate_method(session_data->get_spdm_ctx());
  bool enable_invo_traffic_cali_top_layer_emb_predict =
    SPDM_enable_invo_traffic_cali_top_layer_emb_predict(session_data->get_spdm_ctx());
  bool enable_invo_traffic_cali_ad_conversion =
    SPDM_enable_invo_traffic_cali_ad_conversion(session_data->get_spdm_ctx());
  bool enable_invo_traffic_cali_event_order_paied =
    SPDM_enable_invo_traffic_cali_event_order_paied(session_data->get_spdm_ctx());
  double model_cvr_max_diff_percent =
    SPDM_invo_traffic_cali_model_cvr_max_diff_percent(session_data->get_spdm_ctx());
  std::string invo_traffic_cali_exp_tag =
    SPDM_invo_traffic_cali_exp_tag(session_data->get_spdm_ctx());

  for (auto* p_ad : ad_list->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }

    // 多校准目标准入判断
    bool invo_traffic_cali_admit = false;
    if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
        enable_invo_traffic_cali_ad_conversion) {
      invo_traffic_cali_admit = true;
    } else if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
               p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED &&
               enable_invo_traffic_cali_event_order_paied) {
      invo_traffic_cali_admit = true;
    }

    double ori_cvr = p_ad->get_unify_cvr_info().value;
    double cali_cvr = p_ad->get_invo_traffic_score();
    std::string campaign_type = kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type());
    std::string ocpc_action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    auto exp_tag = SPDM_feed_explore_cvr_cali_exp_tag(session_data->get_spdm_ctx());
    std::string key = absl::StrCat(exp_tag, "|",
                                  campaign_type, "|", ocpc_action_type);
    if (!invo_traffic_cali_admit) {
      if (cali_conf->count(key) == 0) {
        continue;
      }
    }

    double cvr_input_clipped = 0.0;
    double main_logit = 0.0;
    double combined_value = 0.0;
    double new_cvr = 0.0;
    double cali_rate = 1.0;

    if (enable_invo_traffic_cali_top_layer_emb_predict || invo_traffic_cali_admit) {
      RANK_DOT_COUNT(session_data, 1,
                    "invo_traffic_cali_top_layer_emb_predict",
                    "cvr_total_count",
                    campaign_type,
                    ocpc_action_type,
                    invo_traffic_cali_exp_tag);
      p_ad->set_feedtraffic_cvr_cali(ori_cvr);
      cvr_input_clipped = std::max(std::min(ori_cvr, 0.9999999), 0.0000001);
      double model_cali_cvr = ComputeCalibCvrByTopLayerEmb(session_data, p_ad, ori_cvr,
                                campaign_type, ocpc_action_type, invo_traffic_cali_exp_tag);

      if (model_cali_cvr <= 0) {
        RANK_DOT_COUNT(session_data, 1,
                        "invo_traffic_cali_top_layer_emb_predict",
                        "cvr_invalid",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
        continue;
      }
      double diff_percent = 0.0;
      if (std::abs(ori_cvr) > 1e-8) {
          diff_percent = std::abs(model_cali_cvr - ori_cvr) / std::abs(ori_cvr);
          RANK_DOT_STATS(session_data, static_cast<int64_t>(diff_percent * 1000),
                        "invo_traffic_cali_top_layer_emb_predict",
                        "cali_cvr_diff_percent",
                        campaign_type,
                        ocpc_action_type,
                        invo_traffic_cali_exp_tag);
          if (diff_percent > model_cvr_max_diff_percent) {
            RANK_DOT_COUNT(session_data, 1,
                            "invo_traffic_cali_top_layer_emb_predict",
                            "cvr_diff_too_large",
                            campaign_type,
                            ocpc_action_type,
                            invo_traffic_cali_exp_tag);
            continue;
          }
      }
      combined_value = alpha * cvr_input_clipped + beta * model_cali_cvr;
      cali_rate = combined_value / cvr_input_clipped;
      p_ad->set_feedtraffic_cvr_after_cali(combined_value);
    } else {
      if (cali_cvr <= 0) {
        RANK_DOT_COUNT(session_data, 1,
                       "ad_rank.invalid_feed_explore_cali_cvr");
        continue;
      }
      RANK_DOT_STATS(session_data, static_cast<int64_t>(cali_cvr),
                "ad_rank.ori_cvr_cali_cvr_not_valid",
                std::to_string(session_data->get_sub_page_id()));
      if (valid_cali_method) {
        p_ad->set_feedtraffic_cvr_cali(ori_cvr);
        cvr_input_clipped = std::max(std::min(ori_cvr, 0.9999999), 0.0000001);
        main_logit = std::max(std::min(
            log(cvr_input_clipped / (1 - cvr_input_clipped)),
            15.0),
            -15.0);
        combined_value = alpha * main_logit + beta * cali_cvr;
        new_cvr = 1.0 / (1.0 + exp(-combined_value));
        cali_rate = new_cvr / cvr_input_clipped;
        p_ad->set_feedtraffic_cvr_after_cali(new_cvr);
      } else {
        p_ad->set_feedtraffic_cvr_cali(ori_cvr);
        cvr_input_clipped = std::max(std::min(ori_cvr, 0.9999999), 0.0000001);
        combined_value = alpha * cvr_input_clipped + beta * cali_cvr;
        cali_rate = combined_value / cvr_input_clipped;
        p_ad->set_feedtraffic_cvr_after_cali(combined_value);
      }
    }

    if (cali_rate <= 0) {
      continue;
    }
    cali_rate = std::max(std::min(cali_rate, upper_bound), lower_bound);
    if (cali_rate != 1.0 && cali_rate > 0) {
      p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
      p_ad->set_cali_rate_turn(cali_rate);
      p_ad->Attr(ItemIdx::unify_cvr_calibration_model_rate).SetDoubleValue(
        p_ad->AttrIndex(), cali_rate, false, false);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(1000 * cali_rate),
                     "ad_rank.feed_explore_cvr_cali_rate",
                     std::to_string(session_data->get_sub_page_id()));
    }
  }
  return StraRetCode::SUCC;
}

const char* CommonOfflineCalibratePlugin::Name() {
  return "CommonOfflineCalibratePlugin";
}

void CommonOfflineCalibratePlugin::Clear() {}

bool CommonOfflineCalibratePlugin::IsRun(const ContextData* session_data, const Params* params,
                                         AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CommonOfflineCalibratePlugin::Process(ContextData* session_data,
                                                  Params* params,
                                                  AdRankUnifyScene pos,
                                                  AdList* ad_list) {
  if (!SPDM_enable_common_offline_calibrate(session_data->get_spdm_ctx())) {
    return StraRetCode::SUCC;
  }
  if (ad_list == nullptr || ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }

  auto onlineUnifyCxrCaliCommonBound = RankKconfUtil::onlineUnifyCxrCaliCommonBound();
  auto itr_lower = onlineUnifyCxrCaliCommonBound->find("common_cali_lower");
  common_cali_lower = (itr_lower != onlineUnifyCxrCaliCommonBound->end()) ? itr_lower->second : 0.0;
  auto itr_upper = onlineUnifyCxrCaliCommonBound->find("common_cali_upper");
  common_cali_upper = (itr_upper != onlineUnifyCxrCaliCommonBound->end()) ? itr_upper->second : 1000.0;
  auto& ads = ad_list->Ads();

  for (auto* p_ad : ads) {
    if (p_ad == nullptr) continue;
    // 目前仅作实验用，跳过老校准
    if (p_ad->get_is_skip_cali_before() == 1) {continue;}
    if (p_ad->Is(AdFlag::is_outer_loop_ad)) {
      CommonOfflineCalibrate(session_data, p_ad);
    }
}
  return StraRetCode::SUCC;
}

void CommonOfflineCalibratePlugin::CommonOfflineCalibrate(
    ContextData* session_data, AdCommon* p_ad) {
  const std::string& ocpc_action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  const std::string& deep_action_type = absl::StrCat(ocpc_action_type, "#",
          kuaishou::ad::AdCallbackLog_EventType_Name(p_ad->get_deep_conversion_type()));
  const std::string& ad_queue_type = kuaishou::ad::AdEnum_AdQueueType_Name(p_ad->get_ad_queue_type());

  // ctr common offline calibrate
  RUnifyInfo const* unify_ctr_info = &p_ad->get_unify_ctr_info();
  p_ad->set_unify_ctr_before_commoncali(unify_ctr_info->value);
  std::string ctr_modelstart = kuaishou::ad::AdActionType_Name(unify_ctr_info->s_type);
  std::string ctr_modelend = kuaishou::ad::AdActionType_Name(unify_ctr_info->e_type);
  std::string ctr_query_key = absl::StrCat("common_offline-ctr-",
                      kuaishou::ad::AdEnum_AdQueueType_Name(p_ad->get_ad_queue_type()),
                      "-", p_ad->get_photo_id());
  double ctr_cali_rate = p_ad->GetOfflineCalibrateValue(ctr_query_key);
  if (ctr_cali_rate >= common_cali_upper) {
    ctr_cali_rate = common_cali_upper;
  }
  if (ctr_cali_rate <= common_cali_lower) {
    ctr_cali_rate = common_cali_lower;
  }
  if (ctr_cali_rate != 1.0 && ctr_cali_rate > 0) {
    p_ad->ExcuteCalibration(RUnifyType::CTR, ctr_cali_rate);
    RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * ctr_cali_rate),
                    "ad_rank.common_offline_calibration_mean_ctr",
                    ocpc_action_type + "_" + ctr_modelstart + "_" + ctr_modelend,
                    ad_queue_type);
    RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.common_offline_calibration_count_ctr",
                    ocpc_action_type + "_" + ctr_modelstart + "_" + ctr_modelend,
                    ad_queue_type);
  }
  p_ad->Attr(ItemIdx::common_ctr_cali_rate).SetDoubleValue(
    p_ad->AttrIndex(), ctr_cali_rate, false, false);
  p_ad->set_unify_ctr_after_commoncali(unify_ctr_info->value);

  // cvr common offline calibrate
  RUnifyInfo const* unify_cvr_info = &p_ad->get_unify_cvr_info();
  p_ad->set_unify_cvr_before_commoncali(unify_cvr_info->value);
  std::string cvr_modelstart = kuaishou::ad::AdActionType_Name(unify_cvr_info->s_type);
  std::string cvr_modelend = kuaishou::ad::AdActionType_Name(unify_cvr_info->e_type);
  std::string cvr_query_key = absl::StrCat("common_offline-cvr-",
                      kuaishou::ad::AdEnum_AdQueueType_Name(p_ad->get_ad_queue_type()),
                      "-", p_ad->get_photo_id());
  double cvr_cali_rate = p_ad->GetOfflineCalibrateValue(cvr_query_key);
  if (cvr_cali_rate >= common_cali_upper) {
    cvr_cali_rate = common_cali_upper;
  }
  if (cvr_cali_rate <= common_cali_lower) {
    cvr_cali_rate = common_cali_lower;
  }
  if (cvr_cali_rate != 1.0 && cvr_cali_rate > 0) {
    p_ad->ExcuteCalibration(RUnifyType::CVR, cvr_cali_rate);
    RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cvr_cali_rate),
                    "ad_rank.common_offline_calibration_mean_cvr",
                    ocpc_action_type + "_" + cvr_modelstart + "_" + cvr_modelend,
                    ad_queue_type);
    RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.common_offline_calibration_count_cvr",
                    ocpc_action_type + "_" + cvr_modelstart + "_" + cvr_modelend,
                    ad_queue_type);
  }
  p_ad->Attr(ItemIdx::common_cvr_cali_rate).SetDoubleValue(
    p_ad->AttrIndex(), cvr_cali_rate, false, false);
  p_ad->set_unify_cvr_after_commoncali(unify_cvr_info->value);

  // dcvr common offline calibrate
  RUnifyInfo const* unify_dcvr_info = &p_ad->get_unify_deep_cvr_info();
  p_ad->set_unify_dcvr_before_commoncali(unify_dcvr_info->value);
  std::string dcvr_modelstart = kuaishou::ad::AdActionType_Name(unify_dcvr_info->s_type);
  std::string dcvr_modelend = kuaishou::ad::AdActionType_Name(unify_dcvr_info->e_type);
  std::string dcvr_query_key = absl::StrCat("common_offline-dcvr-",
                      kuaishou::ad::AdEnum_AdQueueType_Name(p_ad->get_ad_queue_type()),
                      "-", p_ad->get_photo_id());
  double dcvr_cali_rate = p_ad->GetOfflineCalibrateValue(dcvr_query_key);
  if (dcvr_cali_rate >= common_cali_upper) {
    dcvr_cali_rate = common_cali_upper;
  }
  if (dcvr_cali_rate <= common_cali_lower) {
    dcvr_cali_rate = common_cali_lower;
  }
  if (dcvr_cali_rate != 1.0 && dcvr_cali_rate > 0) {
    p_ad->ExcuteCalibration(RUnifyType::DEEP_CVR, dcvr_cali_rate);
    RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * dcvr_cali_rate),
                    "ad_rank.common_offline_calibration_mean_dcvr",
                    deep_action_type + "_" + dcvr_modelstart + "_" + dcvr_modelend,
                    ad_queue_type);
    RANK_DOT_COUNT(session_data, 1,
                    "ad_rank.common_offline_calibration_count_dcvr",
                    deep_action_type + "_" + dcvr_modelstart + "_" + dcvr_modelend,
                    ad_queue_type);
  }
  p_ad->Attr(ItemIdx::common_dcvr_cali_rate).SetDoubleValue(
    p_ad->AttrIndex(), dcvr_cali_rate, false, false);
  p_ad->set_unify_dcvr_after_commoncali(unify_dcvr_info->value);
}

// CalibrationWithCmdNative
const char* CalibrationWithCmdNativePlugin::Name() {
  return "CalibrationWithCmdNativePlugin";
}

void CalibrationWithCmdNativePlugin::Clear() {}

bool CalibrationWithCmdNativePlugin::IsRun(const ContextData* session_data, const Params* params,
                                         AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}
StraRetCode CalibrationWithCmdNativePlugin::Process(ContextData* session_data,
                                                  Params* params,
                                                  AdRankUnifyScene pos,
                                                  AdList* ad_list) {
  bool enable_calibration_click_native =
                  SPDM_enbale_online_calibration_cmd_native(session_data->get_spdm_ctx());
  bool enable_calibration_conv_native =
                  SPDM_enbale_online_calibration_conv_native(session_data->get_spdm_ctx());
  bool enable_inner_native_roas_calibration =
                  SPDM_enable_inner_native_roas_calibration(session_data->get_spdm_ctx());
  bool enable_inner_native_order_pay_calibration =
                  SPDM_enable_inner_native_order_pay_calibration(session_data->get_spdm_ctx());
  bool enable_new_inner_native_roas_calibration =
      SPDM_enable_new_inner_native_roas_calibration(session_data->get_spdm_ctx());
  bool enable_new_inner_native_order_pay_calibration =
      SPDM_enable_new_inner_native_order_pay_calibration(session_data->get_spdm_ctx());
  bool disable_inner_native_roas_calibration =
      SPDM_disable_inner_native_roas_calibration(session_data->get_spdm_ctx());
  bool enable_inner_native_storewide_calibration =
      SPDM_enable_inner_native_storewide_calibration(session_data->get_spdm_ctx());
  bool enable_lps_cvr_soft_queue_cali = SPDM_enable_lps_cvr_soft_queue_cali(session_data->get_spdm_ctx());  // NOLINT
  const auto &lps_not_ecom_coeff_soft_col = SPDM_lps_not_ecom_coeff_soft_col(session_data->get_spdm_ctx());  // NOLINT
  enable_inner_order_pay_cali_ad_queue_type_native =
                  SPDM_enable_inner_order_pay_cali_ad_queue_type(session_data->get_spdm_ctx());
  enable_inner_roas_cali_ad_queue_type_native =
                  SPDM_enable_inner_roas_cali_ad_queue_type(session_data->get_spdm_ctx());
  enable_inner_storewide_cali_ad_queue_type =
      SPDM_enable_inner_storewide_cali_ad_queue_type(session_data->get_spdm_ctx());
  bool enable_inspire_soft_calibration_roas =
       SPDM_enable_new_inspire_soft_calibration_roas(session_data->get_spdm_ctx());
  bool enable_inspire_soft_calibration_order_paied =
       SPDM_enable_new_inspire_soft_calibration_order_paied(session_data->get_spdm_ctx());
  bool enable_inspire_soft_calibration_storewide =
       SPDM_enable_new_inspire_soft_calibration_storewide(session_data->get_spdm_ctx());
  bool enable_outer_u_ctr_soft_queue_cali = SPDM_enable_outer_u_ctr_soft_queue_cali(session_data->get_spdm_ctx());  // NOLINT
  bool enable_credit_grant_u_ctr_soft_queue_cali = SPDM_enable_credit_grant_u_ctr_soft_queue_cali(session_data->get_spdm_ctx());  // NOLINT
  const auto &outer_u_ctr_soft_queue_cali_ratio = SPDM_outer_u_ctr_soft_queue_cali_ratio(session_data->get_spdm_ctx());  // NOLINT
  const auto &credit_grant_u_ctr_soft_queue_cali_ratio = SPDM_credit_grant_u_ctr_soft_queue_cali_ratio(session_data->get_spdm_ctx());  // NOLINT
  bool enable_outer_u_cvr = SPDM_enable_outer_u_cvr(session_data->get_spdm_ctx());
  bool enable_c2_lps_ = SPDM_enable_c2_lps(session_data->get_spdm_ctx());
  auto inspireSoftOrderPaiedCaliMap = RankKconfUtil::inspireSoftOrderPaiedCaliMap();
  auto inspireSoftRoasCaliMap = RankKconfUtil::inspireSoftRoasCaliMap();
  auto inspireSoftStorewideCaliMap = RankKconfUtil::inspireSoftStorewideCaliMap();
  auto inspireSoftCaliPageIdSet = RankKconfUtil::inspireSoftCaliPageIdSet();
  bool enableInnerRoasCaliCloseTailExp = RankKconfUtil::enableInnerRoasCaliCloseTailExp();
  auto innerRoasCaliCloseTailSet = RankKconfUtil::innerRoasCaliCloseTailSet();
  enable_native_roas_online_calibration_by_stage_one_cmd =
      SPDM_enable_native_roas_online_calibration_by_stage_one_cmd(session_data->get_spdm_ctx());
  if (ad_list == nullptr || ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }
  bool enable_inner_cid_model_calibration_flow =
      SPDM_enable_inner_cid_model_calibration_flow(session_data->get_spdm_ctx());
  auto is_hosting = [&](auto& p_ad) -> bool {
    return (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_SINGLE_GOODS ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_CID_SUPER_PRODUCT ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_GOODS ||
        p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_OPTIONAL_GOODS);
  };
  auto cid_calibrate_tail_number = RankKconfUtil::innerCIDModelCalibrateTailNumber();
  auto bid_id_is_on = [&](auto& p_ad) -> bool {
    auto bid_id = is_hosting(p_ad)? p_ad->get_campaign_id(): p_ad->get_unit_id();
    return cid_calibrate_tail_number && cid_calibrate_tail_number->IsOnFor(bid_id);
  };
  auto& ads = ad_list->Ads();
  auto* params_ = dynamic_cast<NativeUnifyCalcParams*>(params);
  ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(params_, StraRetCode::SUCC);
  for (auto* p_ad : ads) {
    if (p_ad == nullptr) continue;
    // 目前仅作实验用，跳过老校准
    if (p_ad->get_is_skip_cali_before() == 1) {continue;}
    // 24 策略重构 先对齐基线：仅对软广队列生效，后续考虑做策略合并
    if (p_ad->get_ad_list_type() != 2) {continue;}
    // cid 软广追加直接校准
    if ((enable_inner_cid_model_calibration_flow
          || bid_id_is_on(p_ad))) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)
        OnlineCalibrationWithCmdOrderPaiedNative(session_data, p_ad, params_);
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)
        OnlineCalibrationWithCmdRoasNative(session_data, p_ad, params_);
    }
    if (p_ad->Is(AdFlag::is_outer_loop_ad) &&
        ((p_ad != nullptr &&
          enable_outer_u_cvr &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) ||
           (p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY ||
           (SPDM_enable_game_request_imp_conv(session_data->get_spdm_ctx()) && p_ad->Is(AdFlag::is_game_ad) &&  // NOLINT
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS))) ||
        (enable_c2_lps_ && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION))) &&
        p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE &&
        enable_outer_u_ctr_soft_queue_cali &&
        outer_u_ctr_soft_queue_cali_ratio > 0) {
      p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                  p_ad->get_unify_cvr_info().s_type,
                                  p_ad->get_unify_cvr_info().e_type,
                                  RUnifyTag::OUTER_CTR_SOFT_QUEUE_COEFF,
                                  outer_u_ctr_soft_queue_cali_ratio);
    }
    if (p_ad->Is(AdFlag::is_outer_loop_ad) &&
        (p_ad != nullptr &&
         enable_outer_u_cvr &&
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT) &&
        p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE &&
        enable_credit_grant_u_ctr_soft_queue_cali &&
        credit_grant_u_ctr_soft_queue_cali_ratio > 0) {
      p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
                                  p_ad->get_unify_cvr_info().s_type,
                                  p_ad->get_unify_cvr_info().e_type,
                                  RUnifyTag::OUTER_CTR_SOFT_QUEUE_COEFF,
                                  credit_grant_u_ctr_soft_queue_cali_ratio);
    }
    if (enable_calibration_click_native && !p_ad->Is(AdFlag::is_inner_loop_ad)) {
      OnlineCalibrationCmdNative(session_data, p_ad, params_);
    }
    if (enable_calibration_conv_native && !p_ad->Is(AdFlag::is_inner_loop_ad)) {
      OnlineCalibrationCmdConvNative(session_data, p_ad, params_);
    }
    if ((enable_inner_native_roas_calibration ||
         enable_new_inner_native_roas_calibration) &&
        !disable_inner_native_roas_calibration &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI) &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
      if (enableInnerRoasCaliCloseTailExp) {
        auto roas_ad_id = p_ad->get_unit_id();
        if (p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_SMART_SINGLE_GOODS ||
            p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_CID_SUPER_PRODUCT ||
            p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::STORE_NEW_CUSTOMER_HOSTING ||
            p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_PRODUCT) {
            roas_ad_id = p_ad->get_campaign_id();
        }
        if (innerRoasCaliCloseTailSet->count(roas_ad_id % 100) > 0) {
              continue;
        }
      }
      OnlineCalibrationWithCmdRoasNative(session_data, p_ad, params_);
    }
    if ((enable_inner_native_order_pay_calibration ||
         enable_new_inner_native_order_pay_calibration) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
      OnlineCalibrationWithCmdOrderPaiedNative(session_data, p_ad, params_);
    }
    if (enable_inner_native_storewide_calibration &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
      OnlineCalibrationWithCmdStorewideNative(session_data, p_ad, params_);
    }

    if (enable_lps_cvr_soft_queue_cali &&
        lps_not_ecom_coeff_soft_col > 0.0 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
        if (!p_ad->Is(AdFlag::is_direct_ecom) && !p_ad->Is(AdFlag::is_lps_tx)) {
          if (session_data->get_is_thanos_request()) {
            p_ad->ModifyUnifyInfoLinear(RUnifyType::CVR,
            kuaishou::ad::AD_ITEM_IMPRESSION, p_ad->get_ocpx_action_type(),
            RUnifyTag::LPS_NOT_ECOM_SOFT_COEFF, lps_not_ecom_coeff_soft_col);
          }
        }
    }
    // 激励流量软广校准
    if ((enable_inspire_soft_calibration_roas || enable_inspire_soft_calibration_order_paied ||
         enable_inspire_soft_calibration_storewide) &&
        (inspireSoftCaliPageIdSet->count(session_data->get_page_id()) > 0) &&
        p_ad->Is(AdFlag::IsNativeAd) &&
        p_ad->get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
      double cali_rate = 1.0;
      int32_t cmd_id = 0;
      if (p_ad->get_order_paid() > 0) {
        cmd_id = p_ad->get_order_paid_cmd_id();
      }
      std::string key = absl::StrCat(cmd_id, "-", session_data->get_page_id());
      if (enable_inspire_soft_calibration_roas &&
          inspireSoftRoasCaliMap != nullptr &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)) {
        auto iter = inspireSoftRoasCaliMap->find(key);
        if (iter != inspireSoftRoasCaliMap->end()) {
          cali_rate = iter->second;
        }
        p_ad->ExcuteCalibration(RUnifyType::LTV, cali_rate);
        p_ad->set_cali_rate_turn(cali_rate);
      }
      if (enable_inspire_soft_calibration_order_paied &&
          inspireSoftOrderPaiedCaliMap != nullptr &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) {
        auto iter = inspireSoftOrderPaiedCaliMap->find(key);
        if (iter != inspireSoftOrderPaiedCaliMap->end()) {
          cali_rate = iter->second;
        }
        p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
        p_ad->set_cali_rate_turn(cali_rate);
      }
      if (enable_inspire_soft_calibration_storewide &&
          inspireSoftStorewideCaliMap != nullptr &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) {
        auto iter = inspireSoftStorewideCaliMap->find(key);
        if (iter != inspireSoftStorewideCaliMap->end()) {
          cali_rate = iter->second;
        }
        p_ad->ExcuteCalibration(RUnifyType::LTV, cali_rate);
        p_ad->set_cali_rate_turn(cali_rate);
      }
      if (cali_rate != 1.0 && cali_rate > 0) {
        const std::string& action_type =
            kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
        std::string cmd_id_str = absl::StrCat(cmd_id);
        RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
            "ad_rank.calibration_inspire_soft_cali_rate",
            cmd_id_str,
            action_type);
        RANK_DOT_COUNT(session_data, 1,
            "ad_rank.calibration_inspire_soft_cali_rate_count",
            cmd_id_str,
            action_type);
      }
    }
  }
  return StraRetCode::SUCC;
}

void CalibrationWithCmdNativePlugin::OnlineCalibrationWithCmdOrderPaiedNative(
    ContextData* session_data, AdCommon* p_ad, NativeUnifyCalcParams* params) {
  int32_t cmd_id = 0;
  if (p_ad->get_order_paid() > 0) {
    cmd_id = p_ad->get_order_paid_cmd_id();
  }
  int64_t page_id = session_data->get_page_id();
  int32_t query_type = enable_inner_order_pay_cali_ad_queue_type_native ?  5 : 4;
  if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) {
    query_type = SPDM_enable_inner_cid_calibrate_control_adqueue(session_data->get_spdm_ctx()) ? 6 : 4;
  }
  std::string query_key = p_ad->GetOnlineCalibrationKey(page_id, cmd_id, query_type);
  p_ad->Attr(ItemIdx::adcalibrate_response_key).SetStringValue(p_ad->AttrIndex(), std::string(query_key), false, false);  // NOLINT
  double cali_rate = p_ad->GetCalibrateValue(query_key);

  if (params->native_order_exception_upper_bound > 1.0 &&
      cali_rate >= params->native_order_exception_upper_bound) {
    cali_rate = 1.0;
  }
  if (params->native_order_exception_lower_bound < 1.0 &&
      cali_rate <= params->native_order_exception_lower_bound) {
    cali_rate = 1.0;
  }

  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (p_ad->get_order_paid() > 0) {
    std::string cmd_id_str = absl::StrCat(p_ad->get_order_paid_cmd_id());
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (params->native_order_smart_bidding_min_rate < 1.0 &&
          params->native_order_smart_bidding_min_rate > cali_rate) {
        cali_rate = params->native_order_smart_bidding_min_rate;
      }
      if (params->native_order_smart_bidding_max_rate > 1.0 &&
          params->native_order_smart_bidding_max_rate < cali_rate) {
        cali_rate = params->native_order_smart_bidding_max_rate;
      }
      if (p_ad->IsNeedCalibration(
                    RUnifyType::CVR,
                    p_ad->get_order_paid_cmd_id())) {
        p_ad->Attr(ItemIdx::ad_calibrate_cali_rate).SetDoubleValue(p_ad->AttrIndex(), cali_rate, false, false);  // NOLINT cali_rate_turn 不准确
        p_ad->Attr(ItemIdx::ad_calibrate_origin_cvr).SetDoubleValue(p_ad->AttrIndex(), p_ad->get_unify_cvr_info().value, false, false);  // NOLINT
        p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
      }
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                    "ad_rank.online_calibration_native_order_paied_cali_rate",
                    cmd_id_str,
                    action_type);
    }
  }
}

void CalibrationWithCmdNativePlugin::OnlineCalibrationWithCmdRoasNative(
    ContextData* session_data, AdCommon* p_ad, NativeUnifyCalcParams* params) {
  int32_t cmd_id = 0;
  if (p_ad->get_gmv() > 0) {
    cmd_id = (enable_native_roas_online_calibration_by_stage_one_cmd ?
        p_ad->get_order_paid_cmd_id() : p_ad->get_gmv_cmd_id());
  }
  int64_t page_id = session_data->get_page_id();
  int32_t query_type = enable_inner_roas_cali_ad_queue_type_native ?  5 : 4;
  if (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
    query_type = SPDM_enable_inner_cid_calibrate_control_adqueue(session_data->get_spdm_ctx()) ? 6 : 4;
  }
  std::string query_key = p_ad->GetOnlineCalibrationKey(page_id, cmd_id, query_type);
  p_ad->Attr(ItemIdx::adcalibrate_response_key).SetStringValue(p_ad->AttrIndex(), std::string(query_key), false, false);  // NOLINT
  double cali_rate = p_ad->GetCalibrateValue(query_key);

  if (params->native_roas_exception_upper_bound > 1.0 &&
      cali_rate >= params->native_roas_exception_upper_bound) {
    cali_rate = 1.0;
  }
  if (params->native_roas_exception_lower_bound < 1.0 &&
      cali_rate <= params->native_roas_exception_lower_bound) {
    cali_rate = 1.0;
  }

  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (p_ad->get_gmv() > 0) {
    std::string cmd_id_str = absl::StrCat(cmd_id);
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (params->native_roas_smart_bidding_min_rate < 1.0 &&
          params->native_roas_smart_bidding_min_rate > cali_rate) {
        cali_rate = params->native_roas_smart_bidding_min_rate;
      }
      if (params->native_roas_smart_bidding_max_rate > 1.0 &&
          params->native_roas_smart_bidding_max_rate < cali_rate) {
        cali_rate = params->native_roas_smart_bidding_max_rate;
      }
      if ((!enable_native_roas_online_calibration_by_stage_one_cmd &&
          p_ad->IsNeedCalibration(RUnifyType::LTV, cmd_id)) ||
          (enable_native_roas_online_calibration_by_stage_one_cmd &&
          p_ad->IsNeedCalibration(RUnifyType::CVR, cmd_id))) {
        p_ad->Attr(ItemIdx::ad_calibrate_cali_rate).SetDoubleValue(p_ad->AttrIndex(), cali_rate, false, false);  // NOLINT cali_rate_turn 不准确
        p_ad->Attr(ItemIdx::ad_calibrate_origin_cvr).SetDoubleValue(p_ad->AttrIndex(), p_ad->get_unify_ltv_info().value, false, false);  // NOLINT
        p_ad->ExcuteCalibration(RUnifyType::LTV, cali_rate);
      }
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                    "ad_rank.online_calibration_native_roas_cali_rate",
                    cmd_id_str,
                    action_type);
    }
  }
}

void CalibrationWithCmdNativePlugin::OnlineCalibrationWithCmdStorewideNative(
    ContextData* session_data, AdCommon* p_ad, NativeUnifyCalcParams* params) {
  int32_t cmd_id = 0;
  if (p_ad->get_gmv() > 0) {
    cmd_id = (enable_native_roas_online_calibration_by_stage_one_cmd ?
        p_ad->get_order_paid_cmd_id() : p_ad->get_gmv_cmd_id());
  }
  int64_t page_id = session_data->get_page_id();
  int32_t query_type = enable_inner_storewide_cali_ad_queue_type ?  5 : 4;
  std::string query_key = p_ad->GetOnlineCalibrationKey(page_id, cmd_id, query_type);
  double cali_rate = p_ad->GetCalibrateValue(query_key);
  if (params->native_storewide_exception_upper_bound > 1.0 &&
      cali_rate >= params->native_storewide_exception_upper_bound) {
    cali_rate = 1.0;
  }
  if (params->native_storewide_exception_lower_bound < 1.0 &&
      cali_rate <= params->native_storewide_exception_lower_bound) {
    cali_rate = 1.0;
  }
  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (p_ad->get_gmv() > 0) {
    std::string cmd_id_str = absl::StrCat(cmd_id);
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (params->native_storewide_smart_bidding_min_rate < 1.0 &&
          params->native_storewide_smart_bidding_min_rate > cali_rate) {
        cali_rate = params->native_storewide_smart_bidding_min_rate;
      }
      if (params->native_storewide_smart_bidding_max_rate > 1.0 &&
          params->native_storewide_smart_bidding_max_rate < cali_rate) {
        cali_rate = params->native_storewide_smart_bidding_max_rate;
      }
      if ((!enable_native_roas_online_calibration_by_stage_one_cmd &&
          p_ad->IsNeedCalibration(RUnifyType::LTV, cmd_id)) ||
          (enable_native_roas_online_calibration_by_stage_one_cmd &&
          p_ad->IsNeedCalibration(RUnifyType::CVR, cmd_id))) {
        p_ad->ExcuteCalibration(RUnifyType::LTV, cali_rate);
      }
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                    "ad_rank.online_calibration_native_storewide_cali_rate",
                    cmd_id_str,
                    action_type);
    }
  }
}

void CalibrationWithCmdNativePlugin::OnlineCalibrationCmdNative(
    ContextData* session_data, AdCommon* p_ad, NativeUnifyCalcParams* params) {
  //校准服务返回分数
  // double cali_rate = p_ad->get_unify_cvr_info().calibrate_value;
  int32_t cmd_id = p_ad->get_server_show_cvr_cmd_id();
  if (cmd_id <= 0) {
     return;
  }
  if (!p_ad->IsNeedCalibration(RUnifyType::CTR,
                                  p_ad->get_server_show_cvr_cmd_id())) {
    return;
  }
  int64_t page_id = session_data->get_page_id();
  std::string query_key = p_ad->GetOnlineCalibrationKey(page_id, cmd_id, 5);
  double cali_rate = p_ad->GetCalibrateValue(query_key);
  if (cali_rate > 0) {
    LOG_EVERY_N(INFO, 10000) << "OnlineCalibrationCmdNative,key:" << query_key << ",value:" << cali_rate;
  } else {
    LOG_EVERY_N(INFO, 10000) << "OnlineCalibrationCmdNative not found,key:"
                             << query_key << ",value:" << cali_rate;
  }
  double smart_bidding_min_rate = params->smart_bidding_min_rate;
  double smart_bidding_max_rate = params->smart_bidding_max_rate;
  double exception_upper_bound = params->exception_upper_bound;
  double exception_lower_bound = params->exception_lower_bound;
  if (exception_upper_bound > 1.0 && cali_rate >= exception_upper_bound) {
    cali_rate = 1.0;
  }
  if (exception_lower_bound < 1.0 && cali_rate <= exception_lower_bound) {
    cali_rate = 1.0;
  }
  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (p_ad->get_server_show_cvr() > 0 &&
      (p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_JINJIAN ||
       p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_CREDIT_GRANT ||
       p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_ITEM_CLICK ||
       p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::EVENT_ADD_WECHAT ||
       p_ad->get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_PURCHASE)
     ) {
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (smart_bidding_min_rate < 1.0 && smart_bidding_min_rate > cali_rate) {
        cali_rate = smart_bidding_min_rate;
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_native_ctr_lower_bound_count",
            std::to_string(p_ad->get_server_show_cvr_cmd_id()),
            action_type);
      }
      if (smart_bidding_max_rate > 1.0 && smart_bidding_max_rate < cali_rate) {
        cali_rate = smart_bidding_max_rate;
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_native_ctr_upper_bound_count",
            std::to_string(p_ad->get_server_show_cvr_cmd_id()),
            action_type);
      }
      p_ad->ExcuteCalibration(RUnifyType::CTR, cali_rate);
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                     "ad_rank.online_calibration_native_ctr_cali_rate",
                     std::to_string(p_ad->get_server_show_cvr_cmd_id()),
                     action_type);
      RANK_DOT_COUNT(session_data, 1,
                     "ad_rank.online_calibration_native_ctr_cali_rate_count",
                     std::to_string(p_ad->get_server_show_cvr_cmd_id()),
                     action_type);
    }
  }
}

void CalibrationWithCmdNativePlugin::OnlineCalibrationCmdConvNative(
    ContextData* session_data, AdCommon* p_ad, NativeUnifyCalcParams* params) {
  //校准服务返回分数
  int32_t cmd_id = p_ad->get_app_conversion_rate_cmd_id();
  if (cmd_id <= 0) {
     return;
  }
  if (!p_ad->IsNeedCalibration(RUnifyType::CTR, p_ad->get_app_conversion_rate_cmd_id()) &&
      !p_ad->IsNeedCalibration(RUnifyType::CVR, p_ad->get_app_conversion_rate_cmd_id())) {
    return;
  }
  int64_t page_id = session_data->get_page_id();
  std::string query_key = p_ad->GetOnlineCalibrationKey(page_id, cmd_id, 5);
  double cali_rate = p_ad->GetCalibrateValue(query_key);
  if (cali_rate > 0) {
    LOG_EVERY_N(INFO, 10000) << "OnlineCalibrationCmdConvNative,key:" << query_key << ",value:" << cali_rate;
  } else {
    LOG_EVERY_N(INFO, 10000) << "OnlineCalibrationCmdConvNative not found,key:"
                             << query_key << ",value:" << cali_rate;
  }
  double smart_bidding_min_rate = params->calibration_native_conv_min_rate;
  double smart_bidding_max_rate = params->calibration_native_conv_max_rate;
  double exception_upper_bound = params->native_calibration_conv_exception_upper_bound;
  double exception_lower_bound = params->native_calibration_conv_exception_lower_bound;
  const std::string& action_type =
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  if (exception_upper_bound > 1.0 && cali_rate >= exception_upper_bound) {
    RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_native_conv_exception_upper_count",
            std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
            action_type);
    cali_rate = 1.0;
  }
  if (exception_lower_bound < 1.0 && cali_rate <= exception_lower_bound) {
    RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_native_conv_exception_lower_count",
            std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
            action_type);
    cali_rate = 1.0;
  }
  if (cali_rate > 0 && cali_rate != 1.0) {
    RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_native_conv_valid_cnt",
            std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
            action_type);
  } else {
    RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_native_conv_invalid_cnt",
            std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
            action_type);
  }
  if (p_ad->get_app_conversion_rate() > 0) {
    if (cali_rate != 1.0 && cali_rate > 0) {
      if (smart_bidding_min_rate < 1.0 && smart_bidding_min_rate > cali_rate) {
        cali_rate = smart_bidding_min_rate;
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_native_conv_lower_bound_count",
            std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
            action_type);
      }
      if (smart_bidding_max_rate > 1.0 && smart_bidding_max_rate < cali_rate) {
        cali_rate = smart_bidding_max_rate;
        RANK_DOT_COUNT(
            session_data, 1,
            "ad_rank.online_calibration_native_conv_upper_bound_count",
            std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
            action_type);
      }
      if (p_ad->IsNeedCalibration(RUnifyType::CTR,
                                  p_ad->get_app_conversion_rate_cmd_id())) {
        p_ad->ExcuteCalibration(RUnifyType::CTR, cali_rate);
      } else if (p_ad->IsNeedCalibration(
                     RUnifyType::CVR, p_ad->get_app_conversion_rate_cmd_id())) {
        p_ad->ExcuteCalibration(RUnifyType::CVR, cali_rate);
      }
      p_ad->set_cali_rate_turn(cali_rate);
      RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * cali_rate),
                     "ad_rank.online_calibration_native_conv_cali_rate",
                     std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
                     action_type);
      RANK_DOT_COUNT(session_data, 1,
                     "ad_rank.online_calibration_native_conv_cali_rate_count",
                     std::to_string(p_ad->get_app_conversion_rate_cmd_id()),
                     action_type);
    }
  }
}

const char* CalibrationSplashInnerAdPlugin::Name() {
  return "CalibrationSplashInnerAdPlugin";
}

void CalibrationSplashInnerAdPlugin::Clear() {}

bool CalibrationSplashInnerAdPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CalibrationSplashInnerAdPlugin::Process(ContextData* session_data, Params* params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  // 仅对开屏内循环广告生效
  if (!session_data->get_is_splash_request()) {
    return StraRetCode::SUCC;
  }

  if (ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }

  bool enable_splash_inner_ad_calibration = true;
  if (enable_splash_inner_ad_calibration == false) {
    return StraRetCode::SUCC;
  }

  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (params_ == nullptr) {
    return StraRetCode::SUCC;
  }
  int cnt = 0;
  auto &ads = ad_list->Ads();
  for (auto *p_ad : ads) {
    if (!enable_splash_inner_ad_calibration) {
      continue;
    }
    switch (p_ad->get_ocpx_action_type()) {
      case kuaishou::ad::AD_MERCHANT_ROAS:
      case kuaishou::ad::AD_MERCHANT_T7_ROI:
      case kuaishou::ad::CID_ROAS:
        {
          if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
            if (params_->splash_inner_13_roas_ctr_coef > 0.0
              && params_->splash_inner_13_roas_ctr_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::CTR, params_->splash_inner_13_roas_ctr_coef);
            }
            if (params_->splash_inner_13_roas_cvr_coef > 0.0
              && params_->splash_inner_13_roas_cvr_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::CVR, params_->splash_inner_13_roas_cvr_coef);
            }
            if (params_->splash_inner_13_roas_ltv_coef > 0.0
              && params_->splash_inner_13_roas_ltv_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::LTV, params_->splash_inner_13_roas_ltv_coef);
            }
          } else if (p_ad->get_campaign_type()
                      == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
            if (params_->splash_inner_14_roas_ctr_coef > 0.0
              && params_->splash_inner_14_roas_ctr_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::CTR, params_->splash_inner_14_roas_ctr_coef);
            }
            if (params_->splash_inner_14_roas_cvr_coef > 0.0
              && params_->splash_inner_14_roas_cvr_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::CVR, params_->splash_inner_14_roas_cvr_coef);
            }
            if (params_->splash_inner_14_roas_ltv_coef > 0.0
              && params_->splash_inner_14_roas_ltv_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::LTV, params_->splash_inner_14_roas_ltv_coef);
            }
            cnt++;
          }
        }
        break;
      case kuaishou::ad::EVENT_ORDER_PAIED:
      case kuaishou::ad::CID_EVENT_ORDER_PAID:
        {
          if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
            if (params_->splash_inner_13_pay_ctr_coef > 0.0
              && params_->splash_inner_13_pay_ctr_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::CTR, params_->splash_inner_13_pay_ctr_coef);
            }
            if (params_->splash_inner_13_pay_cvr_coef > 0.0
              && params_->splash_inner_13_pay_cvr_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::CVR, params_->splash_inner_13_pay_cvr_coef);
            }
          } else if (p_ad->get_campaign_type()
                      == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
            if (params_->splash_inner_14_pay_ctr_coef > 0.0
              && params_->splash_inner_14_pay_ctr_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::CTR, params_->splash_inner_14_pay_ctr_coef);
            }
            if (params_->splash_inner_14_pay_cvr_coef > 0.0
                && params_->splash_inner_14_pay_cvr_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::CVR, params_->splash_inner_14_pay_cvr_coef);
            }
            cnt++;
          }
        }
        break;
      case kuaishou::ad::AD_LIVE_AUDIENCE:
        {
          if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
            if (params_->splash_inner_14_aud_ctr_coef > 0.0
              &&  params_->splash_inner_14_aud_ctr_coef != 1.0) {
              p_ad->ExcuteCalibration(RUnifyType::CTR, params_->splash_inner_14_aud_ctr_coef);
            }
            cnt++;
          }
        }
        break;
      default:
        break;
    }
  }
  LOG_EVERY_N(INFO, 10000) << "CalibrationSplashInnerAdPlugin,debug live =" << cnt
      << ",cc=" << params_->splash_inner_14_roas_cvr_coef;
  return StraRetCode::SUCC;
}



const char* CalibrationInnerSelfServAdPlugin::Name() {
  return "CalibrationInnerSelfServAdPlugin";
}

void CalibrationInnerSelfServAdPlugin::Clear() {}

bool CalibrationInnerSelfServAdPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CalibrationInnerSelfServAdPlugin::Process(ContextData* session_data, Params* params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  if (!SPDM_enable_inner_ss_calibration(session_data->get_spdm_ctx())) {
    return StraRetCode::SUCC;
  }
  bool enable_ss_population_cali = SPDM_enable_ss_population_cali(session_data->get_spdm_ctx());
  bool enable_ss_population_cali_refine = SPDM_enable_ss_population_cali_refine(session_data->get_spdm_ctx());
  bool enable_ss_population_cali_wpage = SPDM_enable_ss_population_cali_wpage(session_data->get_spdm_ctx());
  bool enable_ss_population_cali_two = SPDM_enable_ss_population_cali_two(session_data->get_spdm_ctx());
  bool enable_ss_population_cali_three = SPDM_enable_ss_population_cali_three(session_data->get_spdm_ctx());
  bool enable_ss_remove_follow_page = SPDM_enable_ss_remove_follow_page(session_data->get_spdm_ctx());
  bool enable_ss_remove_page_two = SPDM_enable_ss_remove_page_two(session_data->get_spdm_ctx());
  bool enable_ss_multi_bound = SPDM_enable_ss_multi_bound(session_data->get_spdm_ctx());
  bool enable_ss_multi_bound_refine = SPDM_enable_ss_multi_bound_refine(session_data->get_spdm_ctx());
  bool enable_ss_multi_bound_wpage = SPDM_enable_ss_multi_bound_refine(session_data->get_spdm_ctx());

  const auto& inner_cali_daily_conf = RankKconfUtil::innerCaliDailyKconf();
  if (inner_cali_daily_conf == nullptr) {
    return StraRetCode::SUCC;
  }

  const auto& inner_cali_daily_conf_refine = RankKconfUtil::innerCaliDailyKconfRefine();
  if (inner_cali_daily_conf_refine == nullptr) {
    return StraRetCode::SUCC;
  }

  const auto& inner_cali_daily_conf_wpage = RankKconfUtil::innerCaliDailyKconfWpage();
  if (inner_cali_daily_conf_wpage == nullptr) {
    return StraRetCode::SUCC;
  }

  if (ad_list == nullptr || ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }

  // 关注页分流实验
  if (enable_ss_remove_follow_page &&
      session_data->get_is_follow_tab()) {
    return StraRetCode::SUCC;
  }

  // 去掉激励开屏
  if (enable_ss_remove_page_two) {
    if (session_data->get_is_rewarded() ||
        session_data->get_is_inspire_live_request() ||
        session_data->get_is_splash_request()) {
          return StraRetCode::SUCC;
        }
  }

  auto self_service_cali_tail = RankKconfUtil::selfServiceCaliTail();
  auto ui_type = session_data->get_rank_request()->ad_request().ad_user_info().inner_buyer_ui_type();
  auto gender = session_data->get_rank_request()->ad_request().ad_user_info().gender();
  auto age_segment = session_data->get_rank_request()->ad_request().ad_user_info().age_segment();
  if (enable_ss_population_cali_two && age_segment == "50+") {
    age_segment = "50";
  }
  int cnt = 0;
  auto &ads = ad_list->Ads();
  for (auto *p_ad : ads) {
    if (p_ad) {
      if (enable_ss_population_cali) {
        if (!p_ad->Is(AdFlag::is_inner_loop_ad)) {  // 内循环
          continue;
        }
      } else {
        if (!p_ad->Is(AdFlag::is_amd_live_campaign)) {  // 内循环直播
          continue;
        }
      }

      auto account_create_source = p_ad->Attr(ItemIdx::fd_ACCOUNT_create_source)
        .GetIntValue(p_ad->AttrIndex()).value_or(0);
      const std::string& action_type =
          kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());

      // inner population cali strategy
      if (enable_ss_population_cali &&
          p_ad->Is(AdFlag::is_inner_loop_ad)) {
        auto item_type = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
        auto ocpx_action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
        auto main_industry =
          p_ad->Attr(ItemIdx::fd_AUTHOR_industry)
          .GetIntValue(p_ad->AttrIndex()).value_or(-1);
        double cali_ratio = 1.0;
        auto it_cali_ratio = inner_cali_daily_conf->find(absl::StrCat(item_type,
                                                              "_", ocpx_action_type,
                                                              "_", main_industry,
                                                              "_", ui_type,
                                                              "_", gender,
                                                              "_", age_segment));

        if (it_cali_ratio != inner_cali_daily_conf->end()) {
          cali_ratio = it_cali_ratio->second;
          RANK_DOT_COUNT(session_data, 1,
                            "ad_rank.population_matched_count",
                            absl::StrCat(item_type,
                            "_", ocpx_action_type,
                            "_", main_industry,
                            "_", ui_type,
                            "_", gender,
                            "_", age_segment));
        }


        // 保险设置上下限
        double lower_bound = 0.9;
        double upper_bound = 1.1;
        if (enable_ss_multi_bound) {
          auto it_upper = inner_cali_daily_conf->find(absl::StrCat(item_type,
                            "_", ocpx_action_type,
                            "_", "upper_bound"));
          if (it_upper != inner_cali_daily_conf->end()) {
            upper_bound = it_upper->second;
          }
          auto it_lower = inner_cali_daily_conf->find(absl::StrCat(item_type,
                            "_", ocpx_action_type,
                            "_", "lower_bound"));
          if (it_lower != inner_cali_daily_conf->end()) {
            lower_bound = it_lower->second;
          }
        } else {
          auto it_upper = inner_cali_daily_conf->find("upper_bound");
          if (it_upper != inner_cali_daily_conf->end()) {
            upper_bound = it_upper->second;
          }
          auto it_lower = inner_cali_daily_conf->find("lower_bound");
          if (it_lower != inner_cali_daily_conf->end()) {
            lower_bound = it_lower->second;
          }
        }


        if (cali_ratio != 1.0 && cali_ratio > 0.01) {
          if (enable_ss_population_cali_three) {
            cali_ratio = std::max(cali_ratio, lower_bound);
            cali_ratio = std::min(cali_ratio, upper_bound);
          }
          p_ad->ExcuteCalibration(RUnifyType::CVR, cali_ratio);
          p_ad->set_cali_rate_turn(cali_ratio);
          RANK_DOT_STATS(session_data, static_cast<int64_t>(cali_ratio * 100),
                   "ad.rank.inner_population_cali_ratio");
        }
      }

      // inner population cali strategy
      if (enable_ss_population_cali_refine &&
          p_ad->Is(AdFlag::is_inner_loop_ad)) {
        // 内循环自助客户准入
        if (enable_ss_multi_bound_refine &&
            !(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE &&
            p_ad->Attr(ItemIdx::fd_ACCOUNT_create_source)
              .GetIntValue(p_ad->AttrIndex()).value_or(0) == 20)) {
          continue;
        }


        auto item_type = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
        auto ocpx_action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
        auto main_industry =
          p_ad->Attr(ItemIdx::fd_AUTHOR_industry)
          .GetIntValue(p_ad->AttrIndex()).value_or(-1);
        double cali_ratio = 1.0;
        auto it_cali_ratio = inner_cali_daily_conf_refine->find(absl::StrCat(item_type,
                                                              "_", ocpx_action_type,
                                                              "_", main_industry,
                                                              "_", ui_type,
                                                              "_", gender,
                                                              "_", age_segment));

        if (it_cali_ratio != inner_cali_daily_conf_refine->end()) {
          cali_ratio = it_cali_ratio->second;
          RANK_DOT_COUNT(session_data, 1,
                            "ad_rank.population_matched_count",
                            absl::StrCat(item_type,
                            "_", ocpx_action_type,
                            "_", main_industry,
                            "_", ui_type,
                            "_", gender,
                            "_", age_segment));
        }

        // 保险设置上下限
        double lower_bound = 0.9;
        double upper_bound = 1.1;
        if (enable_ss_population_cali_wpage) {
          if (enable_ss_multi_bound_refine && enable_ss_multi_bound) {
            auto it_upper = inner_cali_daily_conf->find(absl::StrCat(item_type,
                              "_", ocpx_action_type,
                              "_", "upper_bound"));
            if (it_upper != inner_cali_daily_conf->end()) {
              upper_bound = it_upper->second;
            }
            auto it_lower = inner_cali_daily_conf->find(absl::StrCat(item_type,
                              "_", ocpx_action_type,
                              "_", "lower_bound"));
            if (it_lower != inner_cali_daily_conf->end()) {
              lower_bound = it_lower->second;
            }
          } else {
            auto it_upper = inner_cali_daily_conf->find("upper_bound");
            if (it_upper != inner_cali_daily_conf->end()) {
              upper_bound = it_upper->second;
            }
            auto it_lower = inner_cali_daily_conf->find("lower_bound");
            if (it_lower != inner_cali_daily_conf->end()) {
              lower_bound = it_lower->second;
            }
          }
        } else {
          if (enable_ss_multi_bound_refine && enable_ss_multi_bound) {
            auto it_upper = inner_cali_daily_conf_refine->find(absl::StrCat(item_type,
                              "_", ocpx_action_type,
                              "_", "upper_bound"));
            if (it_upper != inner_cali_daily_conf_refine->end()) {
              upper_bound = it_upper->second;
            }
            auto it_lower = inner_cali_daily_conf_refine->find(absl::StrCat(item_type,
                              "_", ocpx_action_type,
                              "_", "lower_bound"));
            if (it_lower != inner_cali_daily_conf_refine->end()) {
              lower_bound = it_lower->second;
            }
          } else {
            auto it_upper = inner_cali_daily_conf_refine->find("upper_bound");
            if (it_upper != inner_cali_daily_conf_refine->end()) {
              upper_bound = it_upper->second;
            }
            auto it_lower = inner_cali_daily_conf_refine->find("lower_bound");
            if (it_lower != inner_cali_daily_conf_refine->end()) {
              lower_bound = it_lower->second;
            }
          }
        }


        if (cali_ratio != 1.0 && cali_ratio > 0.01) {
          if (enable_ss_population_cali_three) {
            cali_ratio = std::max(cali_ratio, lower_bound);
            cali_ratio = std::min(cali_ratio, upper_bound);
          }
          p_ad->ExcuteCalibration(RUnifyType::CVR, cali_ratio);
          p_ad->set_cali_rate_turn(cali_ratio);
          RANK_DOT_STATS(session_data, static_cast<int64_t>(cali_ratio * 100),
                   "ad.rank.inner_population_cali_ratio");
        }
      }

      // inner population cali strategy
      if (enable_ss_population_cali_wpage &&
          p_ad->Is(AdFlag::is_inner_loop_ad)) {
        auto page_id = session_data->get_page_id();
        auto item_type = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
        auto ocpx_action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
        auto main_industry =
          p_ad->Attr(ItemIdx::fd_AUTHOR_industry)
          .GetIntValue(p_ad->AttrIndex()).value_or(-1);
        double cali_ratio = 1.0;
        auto it_cali_ratio = inner_cali_daily_conf_wpage->find(absl::StrCat(page_id,
                                                              "_", item_type,
                                                              "_", ocpx_action_type,
                                                              "_", main_industry,
                                                              "_", ui_type,
                                                              "_", gender,
                                                              "_", age_segment));

        if (it_cali_ratio != inner_cali_daily_conf_wpage->end()) {
          cali_ratio = it_cali_ratio->second;
          RANK_DOT_COUNT(session_data, 1,
                            "ad_rank.population_wpage_matched_count",
                            absl::StrCat(page_id,
                            "_", item_type,
                            "_", ocpx_action_type,
                            "_", main_industry,
                            "_", ui_type,
                            "_", gender,
                            "_", age_segment));
        }

        // 保险设置上下限
        double lower_bound = 0.9;
        double upper_bound = 1.1;
        if (enable_ss_multi_bound_wpage && enable_ss_multi_bound) {
          auto it_upper = inner_cali_daily_conf_wpage->find(absl::StrCat(item_type,
                            "_", ocpx_action_type,
                            "_", "upper_bound"));
          if (it_upper != inner_cali_daily_conf_wpage->end()) {
            upper_bound = it_upper->second;
          }
          auto it_lower = inner_cali_daily_conf_wpage->find(absl::StrCat(item_type,
                            "_", ocpx_action_type,
                            "_", "lower_bound"));
          if (it_lower != inner_cali_daily_conf_wpage->end()) {
            lower_bound = it_lower->second;
          }
        } else {
          auto it_upper = inner_cali_daily_conf_wpage->find("upper_bound");
          if (it_upper != inner_cali_daily_conf_wpage->end()) {
            upper_bound = it_upper->second;
          }
          auto it_lower = inner_cali_daily_conf_wpage->find("lower_bound");
          if (it_lower != inner_cali_daily_conf_wpage->end()) {
            lower_bound = it_lower->second;
          }
        }

        if (cali_ratio != 1.0 && cali_ratio > 0.01) {
          if (enable_ss_population_cali_three) {
            cali_ratio = std::max(cali_ratio, lower_bound);
            cali_ratio = std::min(cali_ratio, upper_bound);
          }
          p_ad->ExcuteCalibration(RUnifyType::CVR, cali_ratio);
          p_ad->set_cali_rate_turn(cali_ratio);
          RANK_DOT_STATS(session_data, static_cast<int64_t>(cali_ratio * 100),
                   "ad.rank.inner_population_cali_ratio");
        }
      }
    }
  }

  return StraRetCode::SUCC;
}

// CalibrationInnerAd
const char* CalibrationInnerAdPlugin::Name() {
  return "CalibrationInnerAdPlugin";
}

void CalibrationInnerAdPlugin::Clear() {}

bool CalibrationInnerAdPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CalibrationInnerAdPlugin::Process(ContextData* session_data, Params* params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  // 内循环广告校准
  if (ad_list == nullptr || ad_list->Ads().size() <= 0) {
    return StraRetCode::SUCC;
  }

  auto& ads = ad_list->Ads();

  for (auto* p_ad : ads) {
    if (p_ad) {
      // 直播全站增量建模校准
      if (p_ad->Is(AdFlag::is_inner_loop_ad) &&
          p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE) {
        InnerStorewideLiveUpliftCalibration(session_data, p_ad);
      }
      // 直播全站分 u 校准
      if (p_ad->Is(AdFlag::is_inner_loop_ad) &&
          p_ad->get_scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE) {
        InnerStorewideLiveULevelCalibration(session_data, p_ad);
      }
    }
  }
  return StraRetCode::SUCC;
}

void CalibrationInnerAdPlugin::InnerStorewideLiveUpliftCalibration(
    ContextData* session_data, AdCommon* p_ad) {
  auto storewide_live_uplift_map = RankKconfUtil::upliftBoundMap();
  auto storewide_live_uplift_tail = RankKconfUtil::storewideUpliftTail();
  auto storewide_live_uplift_r_tail = RankKconfUtil::storewideLiveUpliftRTail();
  auto storewide_live_uplift_q_tail = RankKconfUtil::storewideLiveUpliftQTail();
  const int32_t& nature_ad_tag = p_ad->Attr(ItemIdx::fd_AUTHOR_nature_ad_tag)
    .GetIntValue(p_ad->AttrIndex()).value_or(0);
  const int64_t& page_id = session_data->get_page_id();
  const std::string& buyer_effective_type = session_data->get_rank_request()
    ->ad_request().ad_user_info().buyer_effective_type();
  std::string exp_tag = SPDM_storewide_live_uplift_exp_tag(session_data->get_spdm_ctx());

  // 客户实验打 exp_tag （客户侧实验优先级高于流量侧实验）
  if (SPDM_enable_storewide_live_uplift_author_exp_tag(session_data->get_spdm_ctx())) {
    const auto& author_conf_ptr = RankKconfUtil::storewideLiveUpliftAuthorExpTag();
    if (!author_conf_ptr) {
      return;
    }
    int32_t div_num = author_conf_ptr->data().div_num();
    const auto& exp_config = author_conf_ptr->data().exp_config();
    if (div_num > 0) {
      std::string author_tail_num = std::to_string(p_ad->get_author_id() % div_num);
      const auto& iter = exp_config.find(author_tail_num);
      if (iter != exp_config.end()) {
        exp_tag = iter->second;
      }
    }
  }

  // 全互斥准入
  if (p_ad->get_storewide_incompatible_type() != 1) {
    return;
  }
  // 模型预估值请求准入
  if (!(SPDM_enable_storewide_live_uplift_model_cmd(session_data->get_spdm_ctx()))) {
    return;
  }
  // 低 u 策略准入
  if (SPDM_enable_storewide_live_uplift_block_lower_u(session_data->get_spdm_ctx())) {
    if (buyer_effective_type == "" || buyer_effective_type == "U0" || buyer_effective_type == "U1") {
      return;
    }
  }
  // 分页面准入（非屏蔽逻辑）
  if (SPDM_enable_storewide_live_uplift_page_limit(session_data->get_spdm_ctx())) {
    const auto& kconf = RankKconfUtil::storewideLiveUpliftPageLimit();
    if (!kconf) {
      return;
    }
    const auto& exp_config = kconf->data().exp_config();
    if (!exp_config.empty()) {
      const auto& iter = exp_config.find(exp_tag);
      if (iter != exp_config.end()) {
        const auto& page_ids = iter->second.page_ids();
        if (std::find(page_ids.begin(), page_ids.end(), page_id) == page_ids.end()) {
          return;
        }
      }
    }
  }

  if (SPDM_enable_storewide_live_uplift_calibration(session_data->get_spdm_ctx()) ||
      (storewide_live_uplift_tail && storewide_live_uplift_tail->IsOnFor(p_ad->get_author_id()))) {
    double p1 = p_ad->get_predict_score(PredictType::PredictType_inner_storewide_live_uplift_prob1);
    double p2 = p_ad->get_predict_score(PredictType::PredictType_inner_storewide_live_uplift_prob2);
    double p2_weight = SPDM_storewide_live_uplift_p2_weight(session_data->get_spdm_ctx());
    double uplift_ratio = 1.0;

    if (SPDM_enable_storewide_live_uplift_r(session_data->get_spdm_ctx()) || storewide_live_uplift_r_tail->IsOnFor(p_ad->get_author_id())) {  // NOLINT
      // R 模型
      uplift_ratio = 1.0 + p1;
    } else if (SPDM_enable_storewide_live_uplift_q(session_data->get_spdm_ctx()) || storewide_live_uplift_q_tail->IsOnFor(p_ad->get_author_id())) {  // NOLINT
      // Q 模型
      uplift_ratio = 1.0 - p2 * p2_weight;
    } else {
      // R - Q 模型
      uplift_ratio = 1.0 + p1 - p2 * p2_weight;
    }
    if (SPDM_enable_storewide_live_uplift_fix_cali_v2(session_data->get_spdm_ctx())) {
      uplift_ratio = 1.0;
    }

    // 分页面校准
    if (SPDM_enable_storewide_live_uplift_page_calibration(session_data->get_spdm_ctx())) {
      const auto& kconf = RankKconfUtil::storewideLiveUpliftPageConf();
      if (!kconf) {
        return;
      }
      const auto& exp_config = kconf->data().exp_config();
      if (!exp_config.empty()) {
        const auto& iter = exp_config.find(exp_tag);
        if (iter != exp_config.end()) {
          const auto& coef_map = iter->second.page_coef();
          const auto& iter_coef = coef_map.find(page_id);
          if (iter_coef != coef_map.end()) {
            uplift_ratio = uplift_ratio * iter_coef->second;
          }
        }
      }
    }
    // 分客户类型校准
    if (SPDM_enable_storewide_live_uplift_esp_calibration(session_data->get_spdm_ctx())) {
      const auto& esp_kconf = RankKconfUtil::storewideLiveUpliftEspConf();
      if (!esp_kconf) {
        return;
      }
      const auto& exp_config = esp_kconf->data().exp_config();
      if (!exp_config.empty()) {
        const auto& iter = exp_config.find(exp_tag);
        if (iter != exp_config.end()) {
          const auto& coef_map = iter->second.esp_coef();
          const auto& iter_coef = coef_map.find(nature_ad_tag);
          if (iter_coef != coef_map.end()) {
            uplift_ratio = uplift_ratio * iter_coef->second;
          }
        }
      }
    }

    // 上下界限制
    if (SPDM_enable_storewide_live_uplift_bound(session_data->get_spdm_ctx())) {
      const auto& bound_kconf = RankKconfUtil::storewideLiveUpliftBoundConf();
      if (!bound_kconf) {
        return;
      }
      const auto& exp_config = bound_kconf->data().exp_config();
      if (!exp_config.empty()) {
        const auto& iter = exp_config.find(exp_tag);
        if (iter != exp_config.end()) {
          const auto& bound_map = iter->second.bound();
          const auto& iter_ratio_upper = bound_map.find("upper_bound");
          if (iter_ratio_upper != bound_map.end()) {
            uplift_ratio = std::min(iter_ratio_upper->second, uplift_ratio);
          }
          auto iter_ratio_lower = bound_map.find("lower_bound");
          if (iter_ratio_lower != bound_map.end()) {
            uplift_ratio = std::max(iter_ratio_lower->second, uplift_ratio);
          }
        }
      }
    }

    if (uplift_ratio != 1.0 && uplift_ratio > 0) {
      if (SPDM_enable_storewide_live_uplift_cali_ltv(session_data->get_spdm_ctx()) &&
          p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED) {
        p_ad->ExcuteCalibration(RUnifyType::LTV, uplift_ratio);
      } else {
        p_ad->ExcuteCalibration(RUnifyType::CVR, uplift_ratio);
      }
    }

    p_ad->Attr(ItemIdx::inner_storewide_live_uplift_cali_cvr).SetDoubleValue(p_ad->AttrIndex(), p_ad->get_unify_cvr_info().value, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_storewide_live_uplift_prob1).SetDoubleValue(p_ad->AttrIndex(), p1, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_storewide_live_uplift_prob2).SetDoubleValue(p_ad->AttrIndex(), p2, false, false);  // NOLINT
    p_ad->Attr(ItemIdx::inner_storewide_live_uplift_ratio).SetDoubleValue(p_ad->AttrIndex(), uplift_ratio, false, false);  // NOLINT

    RANK_DOT_STATS(session_data, uplift_ratio * 1e3,
                  "ad_rank.InnerStorewideLiveUpliftCalibration",
                  "uplift_ratio",
                  absl::StrCat(exp_tag),
                  absl::StrCat(buyer_effective_type),
                  absl::StrCat(nature_ad_tag));
    RANK_DOT_STATS(session_data, p1 * 1e3,
                  "ad_rank.InnerStorewideLiveUpliftCalibration",
                  "p1",
                  absl::StrCat(exp_tag),
                  absl::StrCat(buyer_effective_type),
                  absl::StrCat(nature_ad_tag));
    RANK_DOT_STATS(session_data, p2 * 1e3,
                  "ad_rank.InnerStorewideLiveUpliftCalibration",
                  "p2",
                  absl::StrCat(exp_tag),
                  absl::StrCat(buyer_effective_type),
                  absl::StrCat(nature_ad_tag));
  }
}

void CalibrationInnerAdPlugin::InnerStorewideLiveULevelCalibration(
  ContextData* session_data, AdCommon* p_ad) {
  // 全互斥
  if (p_ad->get_storewide_incompatible_type() != 1) {
    return;
  }

  const std::string& exp_tag = SPDM_storewide_live_uplift_exp_tag(session_data->get_spdm_ctx());
  const std::string& buyer_effective_type =
    session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
  if (SPDM_enable_storewide_live_u_level_calibration(session_data->get_spdm_ctx())) {
    const auto& kconf = RankKconfUtil::storewideLiveULevelConf();
    if (!kconf) {
      return;
    }
    auto& exp_config = kconf->data().exp_config();
    if (!exp_config.empty()) {
      auto iter = exp_config.find(exp_tag);
      if (iter != exp_config.end()) {
        auto& coef_map = iter->second.u_level_coef();
        auto iter_coef = coef_map.find(buyer_effective_type);
        if (iter_coef != coef_map.end()) {
          p_ad->ExcuteCalibration(RUnifyType::CVR, iter_coef->second);
        }
      }
    }
  }
}

const char* CalibrationInnerNobidPlugin::Name() {
  return "CalibrationInnerNobidPlugin";
}
void CalibrationInnerNobidPlugin::Clear() {}
bool CalibrationInnerNobidPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CalibrationInnerNobidPlugin::Process(ContextData* session_data, Params* params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  if (!SPDM_enable_inner_nobid_calibration(session_data->get_spdm_ctx())) {
    return StraRetCode::SUCC;
  }
  int cnt = 0;
  auto &ads = ad_list->Ads();
  bool enable_nobid_age_gender_cali =
      SPDM_enable_nobid_age_gender_cali(session_data->get_spdm_ctx());
  bool enable_nobid_age_gender_cali_v2 =
    SPDM_enable_nobid_age_gender_cali_v2(session_data->get_spdm_ctx());
  bool enable_only_boost = SPDM_enable_only_boost(session_data->get_spdm_ctx());
  bool enable_nobid_pcvr_cali =
      SPDM_enable_nobid_pcvr_cali(session_data->get_spdm_ctx());
  bool enable_nobid_pcvr_cali_v2 =
    SPDM_enable_nobid_pcvr_cali_v2(session_data->get_spdm_ctx());
  bool enable_smb_industry_cali =
    SPDM_enable_smb_industry_cali(session_data->get_spdm_ctx());
  auto enable_smb_industry_cvr_exp_name =
    SPDM_enable_smb_industry_cvr_exp_name(session_data->get_spdm_ctx());
  auto enable_smb_industry_cvr_exp =
    SPDM_enable_smb_industry_cvr_exp(session_data->get_spdm_ctx());
  auto enable_smb_industry_cvr_exp_patch =
    SPDM_enable_smb_industry_cvr_exp_patch(session_data->get_spdm_ctx());
  auto nobid_cali_exp_config = RankKconfUtil::nobidCaliExpConfig();
  auto gender = session_data->get_rank_request()->ad_request().ad_user_info().gender();
  auto age_segment = session_data->get_rank_request()->ad_request().ad_user_info().age_segment();
  const auto& gender_age_key_conf = RankKconfUtil::innerNobidGenderAgeKeyConf();
  const auto& inner_smb_cali_daily_conf = RankKconfUtil::innerSmbCaliDailyKconf();
  auto buyer_effective_type = session_data->get_rank_request()
            ->ad_request().ad_user_info().buyer_effective_type();
  if (!gender_age_key_conf) {
    return StraRetCode::SUCC;
  }
  for (auto *p_ad : ads) {
    if (p_ad) {
      if (!p_ad->Is(AdFlag::is_inner_loop_ad)) {  // 内循环
        continue;
      }
      if (!p_ad->Is(AdFlag::IsEspUnifyNobidAdV2)) {
        continue;
      }
      if (!p_ad->Is(AdFlag::is_amd_live_campaign)) {  // 内循环直播
        continue;
      }
      if (!(p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP ||
            p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE)) {  // 磁力金牛
        continue;
      }
      if (p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_ORDER_PAIED &&
          p_ad->get_ocpx_action_type() != kuaishou::ad::CID_EVENT_ORDER_PAID) {
        continue;
      }
      auto pcoc_by_gender_age = p_ad->Attr(ItemIdx::fd_AUTHOR_pcoc_by_gender_age)
        .GetDoubleListValue(p_ad->AttrIndex()).value_or(absl::Span<const double>());
      std::vector<double> pcoc_gender_age(pcoc_by_gender_age.begin(), pcoc_by_gender_age.end());
      auto pcoc_by_pred_cvr = p_ad->Attr(ItemIdx::fd_AUTHOR_pcoc_by_pred_cvr)
        .GetDoubleListValue(p_ad->AttrIndex()).value_or(absl::Span<const double>());
      std::vector<double> pcoc_pred_cvr(pcoc_by_pred_cvr.begin(), pcoc_by_pred_cvr.end());
      double cali_ratio = 1.0;
      std::string bucket_tag = "bucket";
      if (enable_nobid_age_gender_cali) {
        if (pcoc_gender_age.size() != 6) {
          continue;
        }
        auto it_cali_ratio = gender_age_key_conf->find(absl::StrCat(gender,
                                                              "_", age_segment));
        if (it_cali_ratio != gender_age_key_conf->end()) {
          // 固定六位
          int cali_position = std::max(std::min(it_cali_ratio->second, 5), 0);
          cali_ratio = pcoc_gender_age[cali_position];
          RANK_DOT_STATS(session_data, static_cast<int64_t>(cali_ratio * 100), absl::StrCat(cali_position),
                    "ad.rank.inner_nobid_age_gender_cali_ratio");
          bucket_tag = absl::StrCat(bucket_tag, "_age_gender_cali_", std::to_string(cali_position));
        }
      }
      if (enable_nobid_age_gender_cali_v2
          && (p_ad->get_item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE
          || p_ad->get_item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE)) {
        auto it_cali_ratio = gender_age_key_conf->find(absl::StrCat(gender,
                                                              "_", age_segment));
        int seg = 0;
        if (p_ad->get_item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
          seg = 0;
        } else {
          seg = 6;
        }
        if (it_cali_ratio != gender_age_key_conf->end()) {
          // 固定六位
          int cali_position = std::max(std::min(it_cali_ratio->second, 5), 0) + seg;
          if (cali_position < pcoc_gender_age.size()) {
            cali_ratio = pcoc_gender_age[cali_position];
            if (enable_only_boost) {
              cali_ratio = std::max(1.0, cali_ratio);
            }
            bucket_tag = absl::StrCat(bucket_tag, "_age_gender_cali_",
            kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type()),
            cali_position);
          }
        }
      }
      if (enable_nobid_pcvr_cali) {
        if (pcoc_pred_cvr.size() != 15) {
          continue;
        }
        int cali_position = 0;
        bool found = false;
        for (int i = 0; i < 5; i++) {
          if (p_ad->get_unify_cvr_info().value <= pcoc_pred_cvr[i]) {
            cali_position = i + 5;
            found = true;
            break;
          }
        }
        if (found) {
          cali_ratio = pcoc_by_pred_cvr[cali_position];
          RANK_DOT_STATS(session_data, static_cast<int64_t>(cali_ratio * 100), absl::StrCat(cali_position),
                    "ad.rank.inner_nobid_pcvr_cali_ratio");
          bucket_tag = absl::StrCat(bucket_tag, "_pcvr_cali_", std::to_string(cali_position));
        }
      }
      int global_cali_position = -1;
      if (enable_nobid_pcvr_cali_v2 &&
          (p_ad->get_item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE
          || p_ad->get_item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE)) {
        int cali_position = 0;
        int seg = 0;
        if (p_ad->get_item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
          seg = 0;
        } else {
          seg = 10;
        }
        bool found = false;
        for (int i = 0; i < 5; i++) {
          if (i+seg < pcoc_pred_cvr.size() &&
            p_ad->get_unify_cvr_info().value <= pcoc_pred_cvr[i+seg]) {
            cali_position = seg + i + 5;
            found = true;
            break;
          }
        }
        if (found && cali_position < pcoc_by_pred_cvr.size()) {
          cali_ratio = pcoc_by_pred_cvr[cali_position];
          if (enable_only_boost) {
              cali_ratio = std::max(1.0, cali_ratio);
          }
          auto first_industry_id = p_ad->get_first_industry_id_v5();
          auto second_industry_id = p_ad->get_second_industry_id_v5();
          bucket_tag = absl::StrCat(bucket_tag, "_pcvr_cali_",
          "first_", first_industry_id,
          "second_", second_industry_id,
          kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type()),
            cali_position);
        }
        global_cali_position = cali_position;
      }
      if (enable_smb_industry_cali &&
          p_ad->Is(AdFlag::is_inner_loop_ad)) {
        int64 author_cost_level_92d_int = p_ad->Attr(ItemIdx::fd_AUTHOR_cost_level_92d_int)
                                    .GetIntValue(p_ad->AttrIndex()).value_or(-1);
        auto item_type = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
        auto first_industry_id = p_ad->get_first_industry_id_v5();
        cali_ratio = 1.0;
        auto it_cali_ratio = inner_smb_cali_daily_conf->find(absl::StrCat(item_type,
                                                              "_", ocpx_action_type,
                                                              "_", first_industry_id,
                                                              "_", buyer_effective_type,
                                                              "_", author_cost_level_92d_int));

        if (it_cali_ratio != inner_smb_cali_daily_conf->end()) {
          cali_ratio = it_cali_ratio->second;
          RANK_DOT_COUNT(session_data, 1,
                            "ad_rank.lower_level_population_matched_count",
                            absl::StrCat(item_type,
                                    "_", ocpx_action_type,
                                    "_", first_industry_id,
                                    "_", buyer_effective_type));
        }
      }
      if (enable_smb_industry_cvr_exp) {
        auto item_type = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
        auto first_industry_id = p_ad->get_first_industry_id_v5();
        auto second_industry_id = p_ad->get_second_industry_id_v5();
        int64 author_cost_level_92d_int = p_ad->Attr(ItemIdx::fd_AUTHOR_cost_level_92d_int)
                                    .GetIntValue(p_ad->AttrIndex()).value_or(-1);
        std::string lower_tag1 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_lower_", item_type);
        std::string lower_tag2 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_lower_", global_cali_position);
        std::string lower_tag3 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_lower_", first_industry_id, "_", second_industry_id);
        std::string lower_tag4 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_lower_", global_cali_position, "_", item_type, "_",
                first_industry_id, "_", second_industry_id);
        std::string lower_tag5 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_lower_", item_type, "_", first_industry_id);
        std::string lower_tag6 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_lower_", item_type, "_", buyer_effective_type);
        std::string lower_tag7 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_lower_", item_type, "_", author_cost_level_92d_int);
        std::string lower_tag8 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_lower_", item_type, "_", first_industry_id, "_",
                buyer_effective_type, "_", author_cost_level_92d_int);

        std::string upper_tag1 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_upper_", item_type);
        std::string upper_tag2 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_upper_", global_cali_position);
        std::string upper_tag3 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_upper_", first_industry_id, "_", second_industry_id);
        std::string upper_tag4 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_upper_", global_cali_position, "_", item_type, "_",
                first_industry_id, "_", second_industry_id);
        std::string upper_tag5 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_upper_", item_type, "_", first_industry_id);
        std::string upper_tag6 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_upper_", item_type, "_", buyer_effective_type);
        std::string upper_tag7 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_upper_", item_type, "_", author_cost_level_92d_int);
        std::string upper_tag8 = absl::StrCat(enable_smb_industry_cvr_exp_name,
                "_upper_", item_type, "_", first_industry_id, "_",
                buyer_effective_type, "_", author_cost_level_92d_int);

        std::vector<std::string> lower_tags = {lower_tag1, lower_tag2, lower_tag3, lower_tag4};
        std::vector<std::string> upper_tags = {upper_tag1, upper_tag2, upper_tag3, upper_tag4};
        if (enable_smb_industry_cvr_exp_patch) {
          std::vector<std::string> tmp_lower_tags = {lower_tag5, lower_tag6, lower_tag7, lower_tag8};
          std::vector<std::string> tmp_upper_tags = {upper_tag5, upper_tag6, upper_tag7, upper_tag8};
          lower_tags.insert(lower_tags.end(), tmp_lower_tags.begin(), tmp_lower_tags.end());
          upper_tags.insert(upper_tags.end(), tmp_upper_tags.begin(), tmp_upper_tags.end());
        }
        double lower_bound = 0.5;
        double upper_bound = 2.0;
        for (auto x : lower_tags) {
          auto iter = nobid_cali_exp_config->find(x);
          if (iter != nobid_cali_exp_config->end()) {
            lower_bound = iter->second;
          }
        }
        for (auto x : upper_tags) {
          auto iter = nobid_cali_exp_config->find(x);
          if (iter != nobid_cali_exp_config->end()) {
            upper_bound = iter->second;
          }
        }
        if (cali_ratio != 1.0 && cali_ratio > 0.01) {
          cali_ratio = std::min(std::max(cali_ratio, lower_bound),
                          upper_bound);
          RANK_DOT_COUNT(session_data, 1,
                            "ad_rank.exp_name");
        }
      }
      // 保险设置上下限
      double lower_bound = 0.5;
      double upper_bound = 2.0;
      if (cali_ratio != 1.0 && cali_ratio > 0.01) {
        cali_ratio = std::max(cali_ratio, lower_bound);
        cali_ratio = std::min(cali_ratio, upper_bound);
        p_ad->ExcuteCalibration(RUnifyType::CVR, cali_ratio);
        p_ad->set_cali_rate_turn(cali_ratio);
        RANK_DOT_STATS(session_data, static_cast<int64_t>(cali_ratio * 100),
                  "ad.rank.inner_nobid_cali_ratio");
        RANK_DOT_STATS(session_data, static_cast<int64_t>(cali_ratio * 100),
          "ad.rank.inner_nobid_cali_ratio_bucket", bucket_tag);
      }
  }
  }
  return StraRetCode::SUCC;
}

const char* CalibrationInnerCtcvrPlugin::Name() {
  return "CalibrationInnerCtcvrPlugin";
}
void CalibrationInnerCtcvrPlugin::Clear() {}
bool CalibrationInnerCtcvrPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CalibrationInnerCtcvrPlugin::Process(ContextData* session_data, Params* params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  if (!SPDM_enable_inner_ctcvr_calibration(session_data->get_spdm_ctx())) {
    return StraRetCode::SUCC;
  }

  bool enable_ctcvr_not_follow_cali =
      SPDM_enable_ctcvr_not_follow_cali(session_data->get_spdm_ctx());
  double inner_ctcvr_cali_lower_bound =
      SPDM_inner_ctcvr_cali_lower_bound(session_data->get_spdm_ctx());
  bool disable_inner_ctcvr_cali_u2 =
      SPDM_disable_inner_ctcvr_cali_u2(session_data->get_spdm_ctx());
  bool disable_inner_ctcvr_cali_u3 =
      SPDM_disable_inner_ctcvr_cali_u3(session_data->get_spdm_ctx());
  auto &ads = ad_list->Ads();
  const auto& conf = RankKconfUtil::innerCtcvrCaliFollowItemBuyerConf();
  auto buyer_effective_type = session_data->get_rank_request()
            ->ad_request().ad_user_info().buyer_effective_type();
  std::string Follow_tag[] = {"Not_Follow", "Follow"};
  std::string item_type[] = {"ITEM_PHOTO", "ITEM_LIVE", "ITEM_PHOTO_TO_LIVE"};

  if (disable_inner_ctcvr_cali_u2 && buyer_effective_type == "U2") {
    return StraRetCode::SUCC;
  }
  if (disable_inner_ctcvr_cali_u3 && buyer_effective_type == "U3") {
    return StraRetCode::SUCC;
  }

  for (auto *p_ad : ads) {
    if (p_ad) {
      if (!p_ad->Is(AdFlag::is_inner_loop_ad)) {
        continue;
      }
      auto ocpx_action_type = p_ad->get_ocpx_action_type();
      if (!(ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
          ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
          ocpx_action_type == kuaishou::ad::AD_MERCHANT_T7_ROI ||
          ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS)) {
        continue;
      }
      bool follow_flag = p_ad->get_is_fan_follow();
      auto ad_type = p_ad->get_item_type();

      double cali_ratio = 1.0;

      if (buyer_effective_type == "U4+") {
        buyer_effective_type = "U4PLUS";
      }
      std::string key = absl::StrCat(Follow_tag[follow_flag], "_"
      , buyer_effective_type, "_", item_type[ad_type]);
      if (enable_ctcvr_not_follow_cali) {
        if (!follow_flag) {
          key = "NULL";
        }
      }
      auto itr = conf->find(key);
      if (itr != conf->end()) {
        cali_ratio = itr->second;
      }

      // 保险设置上下限
      double lower_bound = inner_ctcvr_cali_lower_bound;
      double upper_bound = 1.15;
      if (cali_ratio != 1.0 && cali_ratio > 0.01) {
        cali_ratio = std::max(cali_ratio, lower_bound);
        cali_ratio = std::min(cali_ratio, upper_bound);
        p_ad->ExcuteCalibration(RUnifyType::CVR, cali_ratio);
        p_ad->set_cali_rate_turn(cali_ratio);
        RANK_DOT_STATS(session_data, static_cast<int64_t>(cali_ratio * 100),
                  "ad.rank.inner_ctcvr_cali_ratio");
      }
    }
  }
  return StraRetCode::SUCC;
}


const char* CalibrationInnerCpmPlugin::Name() {
  return "CalibrationInnerCpmPlugin";
}
void CalibrationInnerCpmPlugin::Clear() {}
bool CalibrationInnerCpmPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode CalibrationInnerCpmPlugin::Process(ContextData* session_data, Params* params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  if (!SPDM_enable_inner_cpm_calibration(session_data->get_spdm_ctx())) {
    return StraRetCode::SUCC;
  }
  double pcoc_weight_lower_bound = 0.0;
  double pcoc_weight_upper_bound = 0.0;
  pcoc_weight_lower_bound = SPDM_pcoc_weight_lower_bound(session_data->get_spdm_ctx());
  pcoc_weight_upper_bound = SPDM_pcoc_weight_upper_bound(session_data->get_spdm_ctx());
  const auto& pcoc_conf = RankKconfUtil::innerIndustryCPMCaliCombineHiveConf();
  auto iter_pcoc = pcoc_conf->find("exp7");
  double exp7_ratio = 1.0;
  if (iter_pcoc != pcoc_conf->end() && pcoc_weight_lower_bound <= pcoc_weight_upper_bound) {
    exp7_ratio = std::clamp(iter_pcoc->second, pcoc_weight_lower_bound, pcoc_weight_upper_bound);
  }
  auto &ads = ad_list->Ads();
  auto buyer_effective_type = session_data->get_rank_request()
            ->ad_request().ad_user_info().buyer_effective_type();

  for (auto *p_ad : ads) {
    if (p_ad) {
      if (!p_ad->Is(AdFlag::is_inner_loop_ad)) {
        continue;
      }
      std::string item_type = kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type());
      if ((buyer_effective_type == "U4" || buyer_effective_type == "U4+") && (item_type == "ITEM_LIVE")) {
        p_ad->ExcuteCalibration(RUnifyType::CVR, exp7_ratio);
        p_ad->set_cali_rate_turn(exp7_ratio);
        RANK_DOT_STATS(session_data, static_cast<int64_t>(exp7_ratio * 100),
                  "ad.rank.inner_ctcvr_cali_ratio");
      }
    }
  }
  return StraRetCode::SUCC;
}

}  // namespace ad_rank
}  // namespace ks
