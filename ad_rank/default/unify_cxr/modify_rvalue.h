#pragma once

#include <string>
#include <vector>
#include <list>
#include <tuple>
#include <map>
#include <memory>

#include "teams/ad/ad_rank/common/context_data.h"
#include "teams/ad/ad_rank/common/enum.h"
#include "teams/ad/ad_rank/common/plugin/plugin.h"
#include "teams/ad/ad_rank/common/ad_common.h"
#include "teams/ad/ad_rank/default/params/calc_benefit_param.h"
#include "teams/ad/ad_rank/default/params/native_unify_calc_params.h"


namespace ks {
namespace ad_rank {
class ModifyUnifyCxrInfoNormal : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
};
class ModifyRValuePlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
  std::vector<std::vector<double>> GetCalibrationList(
              const std::vector<std::string> &calibrate_raw, uint32_t bins_n, uint32_t cal_n);
  double CalKconfCalibrationRes(double score, const std::vector<std::vector<double>> &calibrate_list,
                                    double upper, double lower);
  void ModifyRewardCvr(ContextData* session_data, AdCommon* p_ad);
  bool enable_zp_reward_cvr_revise_exp_ = false;
  double reward_cvr_revise_bound_up_ = 0.0;
  double reward_cvr_revise_bound_down_ = 0.0;
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<ks::ad_rank::kconf::RewardCvrReviseConf>>
      reward_cvr_revise_conf_;
};

class ManualCalibrationPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
  bool enable_ctr_manual_cali = false;
  bool enable_cvr_manual_cali = false;
  bool enable_dcvr_manual_cali = false;
  bool enable_ltv_manual_cali = false;
  bool enable_manual_cali_add_creativetype = false;
  bool enable_manual_cali_itemtype = false;
  bool enable_manual_cali_remove_unvalid_tag = false;
  bool enable_ecom_conv_ensemble = false;
  bool enable_adxcpc_miss_ctr_manual_calibrate = false;
  bool enable_adxcpc_model_cali = false;
  double manual_cali_lower = 0.0;
  double manual_cali_upper = 1000.0;
  double adxcpc_hard_cali_weight = 1.0;
  double adxcpc_soft_cali_weight = 1.0;
  bool enable_mini_game_iaap_calibration_strategy_v2 = false;
  bool enable_mini_game_seven_day_iaap_calibration_strategy = false;
  bool enable_mini_game_seven_day_iaap_deprioritize_strategy = false;
  bool enable_game_seven_day_deprioritize_strategy = false;
  bool enable_game_first_r_calibration_strategy = false;
  bool enable_big_game_first_r_calibration_strategy = false;
  bool enable_credit_grant_soft_cali = false;
  double weight_credit_grant_soft_cali = 1.0;

  bool enable_game_iap_7r_ltv_adjust = false;
  double game_iap_7r_ltv_coef = 1.0;
  std::string game_iap_7r_ltv_adjust_tag = "default";

  bool enable_game_sdk_7r_ltv_adjust = false;
  double game_sdk_7r_ltv_coef = 1.0;
  std::string game_sdk_7r_ltv_adjust_tag = "default";
};

class CalibrationWithCmdCorePlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
  void OnlineCalibrationWithCmd(ContextData *session_data, AdCommon* p_ad, CalcBenefitParams* params);
  void OnlineCalibrationWithCmdOrderPaied(ContextData *session_data,
      AdCommon* p_ad, CalcBenefitParams* params);
  void OnlineCalibrationWithCmdRoas(ContextData *session_data,
      AdCommon* p_ad, CalcBenefitParams* params);
  void OnlineCalibrationWithCmdStorewide(ContextData *session_data,
      AdCommon* p_ad, CalcBenefitParams* params);
  double exception_upper_bound;
  double exception_lower_bound;
  bool enable_inner_order_pay_cali_ad_queue_type_hard = false;
  bool enable_inner_roas_cali_ad_queue_type_hard = false;
  bool enable_inner_storewide_cali_ad_queue_type = false;
  bool enable_roas_online_calibration_by_stage_one_cmd = false;
};

class CvrOfflineCalibratePlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
  void CvrOfflineCalibrate(ContextData *session_data, AdCommon* p_ad);
  double global_upper_bound;
  double global_lower_bound;
  std::shared_ptr<absl::flat_hash_map<std::string, double>> offline_rate_intervention{nullptr};
};

class CommonOfflineCalibratePlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
  void CommonOfflineCalibrate(ContextData *session_data, AdCommon* p_ad);
  double common_cali_lower = 0.0;
  double common_cali_upper = 1000.0;
};

class CalibrationWithCmdNativePlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
  void OnlineCalibrationCmdNative(ContextData *session_data, AdCommon* p_ad, NativeUnifyCalcParams* params);
  void OnlineCalibrationCmdConvNative(ContextData *session_data,
                                      AdCommon* p_ad, NativeUnifyCalcParams* params);
  void OnlineCalibrationWithCmdRoasNative(ContextData *session_data,
                                      AdCommon* p_ad, NativeUnifyCalcParams* params);
  void OnlineCalibrationWithCmdOrderPaiedNative(ContextData *session_data,
                                      AdCommon* p_ad, NativeUnifyCalcParams* params);
  void OnlineCalibrationWithCmdStorewideNative(ContextData *session_data,
                                      AdCommon* p_ad, NativeUnifyCalcParams* params);
  bool enable_inner_order_pay_cali_ad_queue_type_native = false;
  bool enable_inner_roas_cali_ad_queue_type_native = false;
  bool enable_inner_storewide_cali_ad_queue_type = false;
  bool enable_native_roas_online_calibration_by_stage_one_cmd = false;
};

class CalibrationSplashInnerAdPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
};

class CalibrationInnerSelfServAdPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
};

class ShelfMerchantOfflineCalibrationPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
  void ShelfMerchantOfflineCvrCali(ContextData *session_data, AdCommon* p_ad);
  void ShelfP2lOfflineCali(ContextData *session_data, AdCommon* p_ad);
  void ShelfPhotoOfflineCali(ContextData *session_data, AdCommon* p_ad);
  void ShelfGylFeedOfflineCali(ContextData *session_data, AdCommon* p_ad);
};

class CalibrationInnerAdPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
  void InnerStorewideLiveUpliftCalibration(ContextData *session_data, AdCommon* p_ad);
  void InnerStorewideLiveULevelCalibration(ContextData *session_data, AdCommon* p_ad);
};

class CalibrationInnerNobidPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
};

class CalibrationInnerCtcvrPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
};

class CalibrationInnerCpmPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
};

class CalibrationFeedSctrPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
};

class CalibrationModelCvrPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
  double ComputeCalibCvrByTopLayerEmb(ContextData* session_data, AdCommon* p_ad, double cvr_input,
                            const std::string& campaign_type, const std::string& ocpc_action_type,
                            const std::string& invo_traffic_cali_exp_tag);
};

class CalibrationInnerRoasCvrPlugin : public AdlistPluginBase {
  DECLARE_ADLIST_PLUGIN_METHODS;
};

}  // namespace ad_rank
}  // namespace ks
