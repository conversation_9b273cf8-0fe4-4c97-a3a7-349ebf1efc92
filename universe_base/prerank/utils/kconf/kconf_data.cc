#include "teams/ad/universe_base/prerank/utils/kconf/kconf_data.h"

#include <map>
#include <set>
#include <string>
#include <memory>
#include <algorithm>
#include <utility>
#include "teams/ad/universe_base/prerank/utils/kconf/kconf.h"
#include "teams/ad/engine_base/kconf/kconf.h"

namespace ks {
namespace universe_base {
namespace prerank {

bool UniverseDcafTimeoutExtraTime::Init() {
  prerank.clear();
  rank.clear();
  auto process = [&] (auto& pb_data, auto& map_data) {
    for (const auto& [exp_name, value_map] : pb_data) {
      std::vector<TimeoutRangeInfo> cur_exp_conf;
      for (const auto& [range_key, extra_time] : value_map.value_map()) {
        std::vector<int32_t> nums;
        for (absl::string_view sv : absl::StrSplit(range_key, ',')) {
            int32_t val;
            if (absl::SimpleAtoi(sv, &val)) {
                nums.push_back(val);
            }
        }
        if (nums.size() == 2) {
          cur_exp_conf.push_back(std::make_tuple(nums[0], nums[1], extra_time));
        } else {
          LOG_EVERY_N(INFO, 100) << "UniverseDcafTimeoutExtraTime parse failed for "
            << range_key;
        }
      }
      map_data.emplace(exp_name, std::move(cur_exp_conf));
    }
  };
  process(pb().prerank(), prerank);
  process(pb().rank(), rank);
  return true;
}

const double UniverseDcafTimeoutExtraTime::GetPrerankExtratime(
    const std::string& exp_name, int32_t timeout) const {
  if (prerank.find(exp_name) != prerank.end()) {
    const auto& conf = prerank.at(exp_name);
    for (const auto [left, right, extra_time] : conf) {
      if (timeout > left && timeout <= right) {
        return extra_time;
      }
    }
  }
  return 0;
}

const double UniverseDcafTimeoutExtraTime::GetRankExtratime(
    const std::string& exp_name, int32_t timeout) const {
  if (rank.find(exp_name) != rank.end()) {
    const auto& conf = rank.at(exp_name);
    for (const auto [left, right, extra_time] : conf) {
      if (timeout > left && timeout <= right) {
        return extra_time;
      }
    }
  }
  return 0;
}

bool UniverseDcafTrafficRecord::Init() {
  target_timeout_map.clear();
  rank_timeout_map.clear();
  auto process = [&] (auto& data, auto& timeout_map) {
    for (const auto& [tp_ratio, value_map] : data) {
      std::map<int32_t, int32_t> cur_timeout_map;
      for (const auto& [quota, timeout] : value_map.value_map()) {
        int32_t quota_val;
        if (absl::SimpleAtoi(quota, &quota_val)) {
          cur_timeout_map[quota_val] = timeout;
        }
      }
      timeout_map[tp_ratio] = cur_timeout_map;
    }
  };
  process(pb().target_timeout_map(), target_timeout_map);
  process(pb().rank_timeout_map(), rank_timeout_map);
  return true;
}

int32_t UniverseDcafTrafficRecord::GetTimeCostByRankQuota(const std::string& tp_ratio, int32_t quota) const {
  if (rank_timeout_map.count(tp_ratio) > 0) {
    auto it = rank_timeout_map.at(tp_ratio);
    if (it.count(quota) > 0) {
      return it.at(quota);
    }
  }
  return 0;
}

int32_t UniverseDcafTrafficRecord::GetTimeCostByPrerankQuota(
    const std::string& tp_ratio, int32_t quota) const {
  if (target_timeout_map.count(tp_ratio) > 0) {
    auto it = target_timeout_map.at(tp_ratio);
    if (it.count(quota) > 0) {
      return it.at(quota);
    }
  }
  return 0;
}

int32_t UniverseDcafTrafficRecord::FindInf(const std::map<int32_t, int32_t>& timeout_map,
    int32_t timeout, int32_t quota, int32_t extra_time,
    std::shared_ptr<std::set<int32_t>> enable_quota) const {
  auto it = timeout_map.find(quota);
  auto new_quota = quota;
  if (it != timeout_map.end()) {
    for (auto rit = std::make_reverse_iterator(std::next(it)); rit != timeout_map.rend(); ++rit) {
      if (enable_quota->find(rit->first) != enable_quota->end()) {
        new_quota = rit->first;
        if (rit->second + extra_time <= timeout) {
          break;
        }
      }
    }
  }
  return new_quota;
}

int32_t UniverseDcafTrafficRecord::AdjustRankQuotaByTimeout(
    const std::string& tp_ratio, int32_t timeout, int32_t quota, int32_t extra_time) const {
  auto it = rank_timeout_map.find(tp_ratio);
  auto enable_quota = engine_base::AdKconfUtil::universeDcafRankEnableTimeoutStat();
  if (it == rank_timeout_map.end()) {
    return quota;
  } else {
    return FindInf(it->second, timeout, quota, extra_time, enable_quota);
  }
}

int32_t UniverseDcafTrafficRecord::AdjustPrerankQuotaByTimeout(
    const std::string& tp_ratio, int32_t timeout, int32_t quota, int32_t extra_time) const {
  auto it = target_timeout_map.find(tp_ratio);
  auto enable_quota = engine_base::AdKconfUtil::universeDcafPrerankEnableTimeoutStat();
  if (it == target_timeout_map.end()) {
    return quota;
  } else {
    return FindInf(it->second, timeout, quota, extra_time, enable_quota);
  }
}

bool ManufacturerGreenChannelConfig::Init() {
  all_account_ids.clear();
  pos2_account_black_list.clear();
  recall_cut_quota = pb().recall_cut_quota();
  prerank_quota = pb().prerank_quota();
  search_num = pb().search_num();
  std::stringstream ss;
  ss << "recall_cut_quota: " << recall_cut_quota << ", prerank_quota: " << prerank_quota
     << ", search_num: " << search_num << ", account_ids: [ ";
  static const int64_t max_config_num = 3;
  static const int64_t max_account_num_per_config = 20;
  int32_t i = 0;
  for (const auto& [product_name, config] : pb().config()) {
    int64_t num = std::min(max_account_num_per_config, static_cast<int64_t>(config.account_id_list_size()));
    all_account_ids.insert(config.account_id_list().begin(), config.account_id_list().begin() + num);
    product2_account_ids[product_name] = {config.account_id_list().begin(),
                                          config.account_id_list().begin() + num};
    for (int j = 0; j < num; j++) {
      ss << config.account_id_list(i) << "-";
    }
    for (int64_t pos_id : config.black_pos_id_list()) {
      auto& black_account_list = pos2_account_black_list[pos_id];
      black_account_list.insert(config.account_id_list().begin(), config.account_id_list().end());
    }
    if (++i >= max_config_num) {
      break;
    }
  }
  ss << "], black_account_list: [";
  for (auto& [pos_id, account_ids] : pos2_account_black_list) {
    ss << pos_id << ": [" << absl::StrJoin(account_ids, "-") << "], ";
  }
  ss << "]";

  ss << ", product2_account_ids: [ ";
  for (auto& [product_name, account_ids] : product2_account_ids) {
    ss << product_name << ": [" << absl::StrJoin(account_ids, "-") << "], ";
  }
  ss << "]";
  LOG(INFO) << "ManufacturerGreenChannelConfig debug str: " << ss.str();
  return true;
}
}  // namespace prerank
}  // namespace universe_base
}  // namespace ks
