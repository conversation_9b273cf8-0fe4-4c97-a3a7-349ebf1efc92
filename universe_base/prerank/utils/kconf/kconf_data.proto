
syntax = "proto3";
package ks.universe_base.prerank.kconf;
import "teams/ad/ad_proto/kuaishou/ad/common/enums.proto";
import "teams/ad/ad_proto/kuaishou/ad/ad_trace_filter_condition.proto";
option cc_enable_arenas = true;

message RtaDyBidConfig {
  message RtaValue {
     map<int64, int32> account_id = 1;
     map<string, int32> product_name = 2;
  }
  map<int64, RtaValue> rta_exp = 1;
}

message UniverseDcafResult {
  message Detail {
    double lambda = 1;
    int64 quota = 2;  // use qps_quota when user_value is not avaiable
    repeated int64 quota_grades = 3;    // 算力档位 quota
    int64 update_ts_sec = 4;
    string hourly_default_quota = 5;  // use hourly_default_quota when not updated for specified time
    string hour_quota_min = 6;  // format 3=2,5=2
    string hour_quota_max = 7;  // 同上
    bool   use_linear_strategy = 8;  // 是否使用线性的策略
    double linear_k = 9;
    double linear_a = 10;
    double linear_b = 11;
    bool   use_smart_compute_model = 12;
  }
  map<string, Detail> rank_map = 1;
  map<string, Detail> prerank_map = 2;
  int64 max_minute_not_updated = 3;  // use default quota if kconf not updated longer than this
}

message UniverseDcafTimeoutExtraTimePb {
  message Stage {
    map<string, double> value_map = 1;
  }
  map<string, Stage> prerank = 1;
  map<string, Stage> rank = 2;
}

message UniverseDcafTrafficRecordPb {
  message ValueMap{
    map<string, string> value_map = 1;
  }
  message TimeoutMap {
    map<string, int32> value_map = 1;
  }
  map<string, ValueMap> param_map = 1;
  int64 record_sec = 2;
  int64 interval_sec = 3;
  double max_miss_percent = 4;
  int64 restart_sec = 5;
  map<string, TimeoutMap> target_timeout_map = 6;
  map<string, TimeoutMap> rank_timeout_map = 7;
}

message universeTinyPrerankDiverseLC {
  map<int64, float> base = 1;
  map<int64, float> exp = 2;
}

message UniverseAppStorePosInfo {
  message PosInfo {
    int32 ad_style = 1;
    string distribute_type = 2;
  }
  map<int64, PosInfo> pos_infos = 1;
}

message ManufacturerGreenChannelConfigPb {
  message SimpleGreenChannelConfigPb {
    repeated int64 black_pos_id_list = 1;
    repeated int64 account_id_list = 2;
  }
  map<string, SimpleGreenChannelConfigPb> config = 1;
  int64 recall_cut_quota = 2;
  int64 prerank_quota = 3;
  int64 search_num = 4;
}

message CrowdTagRecallConfig {
  message TagConf {
    repeated int32 tag = 1;
  }
  map<string, TagConf> conf_map = 1;
}