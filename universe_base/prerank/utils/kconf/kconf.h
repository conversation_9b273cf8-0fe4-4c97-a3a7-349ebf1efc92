#pragma once
#ifndef IMPL_BLOCK_BEGIN        /* auto generated by reset_kconf.py, you can ignore. */
  #define IMPL_BLOCK_BEGIN 0    /* auto generated by reset_kconf.py, you can ignore. */
#endif                          /* auto generated by reset_kconf.py, you can ignore. */
#ifndef IMPL_BLOCK_END          /* auto generated by reset_kconf.py, you can ignore. */
  #define IMPL_BLOCK_END 10000  /* auto generated by reset_kconf.py, you can ignore. */
#endif                          /* auto generated by reset_kconf.py, you can ignore. */

#include <string>

#include "teams/ad/ad_base/src/common/manual_config.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/universe_base/prerank/utils/kconf/kconf_data.h"
#include "teams/ad/universe_base/prerank/utils/kconf/kconf_data.pb.h"
// 实现 cc 中带实现， header 中只带声明
/***************** NOTICE *****************/
/****** 自定义数据类型需要前向声明       *****/
/*****************************************/
#ifndef KCONF_CC_WITH_IMPL
#undef DEFINE_KCONF_NODE
#define DEFINE_KCONF_NODE(type, config_path, config_key, default_value) \
  API_KCONF_NODE(type, config_path, config_key, default_value)

#undef DEFINE_KCONF_NODE_LOAD
#define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key) \
  API_KCONF_NODE_LOAD(type, config_path, config_key)

#undef DEFINE_SET_NODE_KCONF
#define DEFINE_SET_NODE_KCONF(type, config_path, config_key) API_SET_NODE_KCONF(type, config_path, config_key)

#undef DEFINE_KCONF_MAP_KCONF
#define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
  API_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key)
#else
#undef DEFINE_KCONF_NODE_ATTR
#define DEFINE_KCONF_NODE_ATTR __attribute__((used))
#endif

namespace ks {
namespace universe_base {
namespace prerank {
using namespace ks::universe_base::prerank::kconf;  // NOLINT

class AdKconfUtil {
 public:
  DEFINE_PROTOBUF_NODE_KCONF(RtaDyBidConfig, ad.adtarget2, rtaDyBidConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_rank1)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_rank2)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_rank3)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_rank4)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_rank5)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_rank6)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_rank7)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_rank8)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_rank9)

  // 联盟粗排 dcaf 相关
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_exp_prerank1)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_exp_prerank2)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_exp_prerank3)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_exp_prerank4)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_exp_prerank5)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_exp_prerank6)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_exp_prerank7)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_exp_prerank8)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseDcafResult, ad.universeAdAutoParam, universe_dcaf_lambda_exp_prerank9)

  // 联盟弹性队列降级系数
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, universeElasticDegrade)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableUniverseRankDynamicQuotaByTimeout, true);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universePrerankOcpxActionTypeSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeInnerRoiActionTypeSet)
  DEFINE_TAILNUMBERV2_KCONF(ad.adtarget3, universeMcbUseAcbAccountTail, "100;;;")
  DEFINE_PROTOBUF_NODE_KCONF(
    UniverseDcafTrafficRecord, ad.universeAdAutoParam, universeDcafTrafficRecordPrerank)  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(
    UniverseDcafTrafficRecord, ad.universeAdAutoParam, universeDcafTrafficRecord)  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(
    UniverseDcafTimeoutExtraTime, ad.adtarget3, universeDcafTimeoutExtraTime)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adserver2, adUniverseInnerLiveAudienceNotFansRatioDimAuthor);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, universeTinyQueryRecallTag)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, universeTinyGetTopNOcpxActionTypeSet)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, universeTinyGetTopNDeepConversionTypeSet)
  DEFINE_PROTOBUF_NODE_KCONF(universeTinyPrerankDiverseLC, ad.adtarget2,
                             universeTinyPrerankDiverseLCDistribute);
  DEFINE_PROTOBUF_NODE_KCONF(universeTinyPrerankDiverseLC, ad.adtarget2,
                             universePrerankDiverseLCDistribute);
  DEFINE_PROTOBUF_NODE_KCONF(universeTinyPrerankDiverseLC, ad.adtarget2,
                             universeTinyPrerankDiverseLCSearch);
  DEFINE_PROTOBUF_NODE_KCONF(universeTinyPrerankDiverseLC, ad.adtarget2,
                             universeTinySearchPrerankDiverseByIndex);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeTinySearchPos)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeTinyDistributePos)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableTinyRankRecord, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, universeQueryRewritePosWhiteList);
  DEFINE_PROTOBUF_NODE_KCONF(UniverseAppStorePosInfoStruct, ad.frontserver, universeAppStorePosInfo)
  DEFINE_PROTOBUF_NODE_KCONF(ManufacturerGreenChannelConfig, ad.adtarget2, manufacturerGreenChannelConfig)
  DEFINE_PROTOBUF_NODE_KCONF(CrowdTagRecallConfig, ad.adtarget, UniverseInnerLoopHighValueTagList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget, universeLcDisTopProductRecallList);
  DEFINE_INT32_KCONF_NODE(ad.adtarget, unionLtrPrerankExploreRatio, 15)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, unionPrerankExploreNum, 3)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableUniversePrerankDynamicQuotaByTimeout, true);
  // 联盟弹性队列降级系数
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget3, universeCommunicationGreenChannelSet);
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableGreeanChannelOrientation, false);
};

}  // namespace prerank
}  // namespace universe_base
}  // namespace ks

#ifndef KCONF_CC_WITH_IMPL
#undef DEFINE_KCONF_NODE
#define DEFINE_KCONF_NODE(type, config_path, config_key, default_value) \
  DEFINE_KCONF_NODE_BODY(type, config_path, config_key, default_value)

#undef DEFINE_KCONF_NODE_LOAD
#define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key) \
  DEFINE_KCONF_NODE_LOAD_BODY(type, config_path, config_key)

#undef DEFINE_SET_NODE_KCONF
#define DEFINE_SET_NODE_KCONF(type, config_path, config_key) \
  DEFINE_SET_NODE_KCONF_BODY(type, config_path, config_key)

#undef DEFINE_KCONF_MAP_KCONF
#define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
  DEFINE_KCONF_MAP_KCONF_BODY(key_type, value_type, config_path, config_key)
#else
#undef DEFINE_KCONF_NODE_ATTR
#define DEFINE_KCONF_NODE_ATTR
#endif
