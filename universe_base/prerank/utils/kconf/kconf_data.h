#pragma once
#include "teams/ad/universe_base/prerank/utils/kconf/kconf_data.pb.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include <map>
#include <set>
#include <string>
#include <vector>
#include <memory>
#include <algorithm>
#include <unordered_map>
#include <tuple>

namespace ks {
namespace universe_base {
namespace prerank {
struct UniverseDcafTrafficRecord
  : public ks::ad_base::kconf::KconfInitProto<kconf::UniverseDcafTrafficRecordPb> {
 public:
  int32_t GetTimeCostByPrerankQuota(const std::string& tp_ratio, int32_t quota) const;
  int32_t GetTimeCostByRankQuota(const std::string& tp_ratio, int32_t quota) const;
  int32_t AdjustRankQuotaByTimeout(const std::string& tp_ratio, int32_t timeout, int32_t quota,
    int32_t extra_time) const;
  int32_t AdjustPrerankQuotaByTimeout(const std::string& tp_ratio, int32_t timeout, int32_t quota,
    int32_t extra_time) const;
  bool Init() override;
 private:
  absl::flat_hash_map<std::string, std::map<int32_t, int32_t>> target_timeout_map;  // NOLINT
  absl::flat_hash_map<std::string, std::map<int32_t, int32_t>> rank_timeout_map;  // NOLINT
  int32_t FindInf(const std::map<int32_t, int32_t>& timeout_map,
    int32_t timeout, int32_t quota, int32_t extra_time,
    std::shared_ptr<std::set<int32_t>> enable_quota) const;
};

struct UniverseDcafTimeoutExtraTime
  : public ks::ad_base::kconf::KconfInitProto<kconf::UniverseDcafTimeoutExtraTimePb> {
 public:
  using TimeoutRangeInfo = std::tuple<int32_t, int32_t, double>;
  bool Init() override;
  const double GetPrerankExtratime(const std::string& exp_name, int32_t timeout) const;
  const double GetRankExtratime(const std::string& exp_name, int32_t timeout) const;
 private:
  std::unordered_map<std::string, std::vector<TimeoutRangeInfo>> prerank;
  std::unordered_map<std::string, std::vector<TimeoutRangeInfo>> rank;
};

struct UniverseAppStorePosInfoStruct :
  public ks::ad_base::kconf::KconfInitProto<kconf::UniverseAppStorePosInfo> {
  bool Init() override {
    LOG(INFO) << "kconf update pos_infos:" << pb().ShortDebugString();
    return true;
  }
  bool IsAppStoreFlow(int64_t pos_id) const {
    return pb().pos_infos().count(pos_id) > 0;
  }
  int64_t GetAdStyle(int64_t pos_id) const {
    auto iter = pb().pos_infos().find(pos_id);
    if (iter != pb().pos_infos().end()) {
      return iter->second.ad_style();
    }
    return 0;
  }
  std::string GetDistributeType(int64_t pos_id) const {
    auto iter = pb().pos_infos().find(pos_id);
    if (iter != pb().pos_infos().end()) {
      return iter->second.distribute_type();
    }
    return "";
  }
};

struct ManufacturerGreenChannelConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::ManufacturerGreenChannelConfigPb> {
  std::vector<std::vector<int64_t>> GetAllAccountIds(const int64_t pos_id) const {
    std::vector<int64_t> ids;
    std::vector<std::vector<int64_t>> valid_account_ids;
    auto iter = pos2_account_black_list.find(pos_id);
    for (auto& [product_name, account_ids] : product2_account_ids) {
      ids.clear();
      for (int64_t id : account_ids) {
        if (pos2_account_black_list.count(pos_id) > 0 &&
            pos2_account_black_list.at(pos_id).count(id) > 0) {
          continue;
        }
        ids.push_back(id);
      }
      valid_account_ids.emplace_back(ids);
    }
    return valid_account_ids;
  }

  bool HitProduct(const std::string& product_name) const {
    return product2_account_ids.count(product_name) > 0;
  }

  bool HitAccountId(int64_t pos_id, const int64_t account_id) const {
    if (all_account_ids.count(account_id) == 0) {
      return false;
    }
    // 如果 pos 有黑名单，判断是否命中
    if (pos2_account_black_list.count(pos_id) > 0) {
      return pos2_account_black_list.at(pos_id).count(account_id) == 0;
    }

    return true;
  }
  const int64_t GetRecallCutQuota() const {
    return recall_cut_quota;
  }
  const int64_t GetPrerankQuota() const {
    return prerank_quota;
  }
  const int64_t GetSearchNum() const {
    return search_num;
  }
  bool Init() override;
 private:
  absl::flat_hash_set<int64_t> all_account_ids;
  absl::flat_hash_map<std::string, std::vector<int64_t>> product2_account_ids;
  absl::flat_hash_map<int64_t, absl::flat_hash_set<int64_t>> pos2_account_black_list;
  int64_t recall_cut_quota = 0;
  int64_t prerank_quota = 0;
  int64_t search_num = 0;
};
}  // namespace prerank
}  // namespace universe_base
}  // namespace ks
