#pragma once

#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace universe_base {
namespace prerank {

DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_nobid_roas_discount);
//  [gaowei03]  联盟内循环 nobid roas 折扣
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_nobid_cpa_discount);
DECLARE_SPDM_ABTEST_BOOL(enable_univ_prerank_context_id_list_feature);  // [fengyajuan] 联盟粗排增加上下文特征  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_deep_select);  //  [chendongdong] 联盟内循环深度广告筛选
DECLARE_SPDM_KCONF_BOOL(enableDetailCpmSampleSkipDcaf);  // [xiaowentao] 价值分样本跳过 DCAF
DECLARE_SPDM_KCONF_BOOL(enableDisableDcafWhenDegrade);  // [xiaowentao] 在降级的时候跳过动态算力
DECLARE_SPDM_ABTEST_BOOL(enable_dcaf_rank_extra_time_v2);  // [xiaowentao] DCAF 精排降档预估其他阶段耗时
DECLARE_SPDM_ABTEST_BOOL(enable_dcaf_prerank_extra_time_v2);  // [xiaowentao] DCAF 粗排降档预估其他阶段耗时
DECLARE_SPDM_ABTEST_BOOL(enable_universe_remove_prerank_ltv);  //  [chendongdong]  下线粗排 ltv 模型
DECLARE_SPDM_KCONF_INT64(universeDcafRankTimeoutExtraTime);
DECLARE_SPDM_ABTEST_BOOL(enable_univ_prerank_es_score_new);
DECLARE_SPDM_ABTEST_BOOL(universe_skip_prearank_quota_fix);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_skip_prearank);
DECLARE_SPDM_ABTEST_BOOL(enable_univ_prerank_real_action_model);
DECLARE_SPDM_ABTEST_BOOL(enable_univ_onestep_req_preprank_model);  // [chendongdong] 联盟一段式广告请求粗排模型  // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableSearchQueryNormP2p);  // [qianyangchao] 搜索词归一化
DECLARE_SPDM_ABTEST_BOOL(enable_app_store_white_pkg_opt_pos_algin);    // [yingyuanxiang] 白名单和完全匹配强绑定限制广告位生效 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enableUniverseTinyLLMFixedQueryType);  // [jiangyifan] 大模型纠错召回使用固定相关性开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_package_distribute_top_prerank);  // [linglong] 厂商分发 大系统粗排多样性 // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(univ_prerank_hyper_score_gamma);
DECLARE_SPDM_ABTEST_DOUBLE(univ_inner_skip_prearank_quota);
DECLARE_SPDM_ABTEST_DOUBLE(univ_live_deep_skip_prearank_quota);
DECLARE_SPDM_KCONF_BOOL(enableUniverseInnerGreenChannelMediaConfig);  // [tanghaihong] 联盟内循环绿通配置支持媒体维度 // NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_prerank_inner_supported_group_tag);  // [chendongdong] 内循环深度人群扶持标签 // NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_package_distribute_prerank_app_uniq_num);  // [linglong] 厂商分发 大系统粗排多样性广告个数 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_close_guard_auto_bid);   // [liuyibo05] 关闭系统出价兜底
DECLARE_SPDM_ABTEST_BOOL(enable_close_guard_auto_bid_with_filter_ad);  // [liuyibo05] 系统出价获取失败过滤
DECLARE_SPDM_ABTEST_INT64(universe_app_search_prerank_diversity_uniq_quota); // [niejinlong] 厂商搜索粗排多样性 quota  // NOLINT
// huangwenbin
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_search_package_flow);  // [huangwenbin] 联盟厂商搜索位次信息利用召粗精总开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_search_prerank_diverse_by_index);  // [huangwenbin] 厂商搜索多样性开关 基于位次信息 // NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_tiny_search_prerank_diverse_by_index_abtag);  // [huangwenbin] 厂商搜索多样性 kconf 配置 基于位次信息 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_prerank_diverse_layer_control_distribute);  // [huangwenbin] 厂商分发多样性开关 // NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_tiny_prerank_diverse_lc_search_tag);  // [huangwenbin] 厂商搜索多样性 kconf 配置 // NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_tiny_prerank_diverse_lc_distribute_tag);  // [huangwenbin] 厂商分发多样性 kconf 配置 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_dcaf_rank);  // [xiaowentao] 联盟精排 DCAF
DECLARE_SPDM_ABTEST_BOOL(skip_universe_dcaf_rank);  // [xiaowentao] 跳过联盟精排 DCAF
DECLARE_SPDM_ABTEST_BOOL(enable_inner_deep_new_ocpx_prerank);  // [wuhan12] 联盟内循环深度粗排扶持新预算兼容
DECLARE_SPDM_ABTEST_STRING(universe_inner_high_value_crowd_target_tag_key);
DECLARE_SPDM_ABTEST_BOOL(enable_audience_diverse_prerank);  // [xiemiao] 直播进人明投爬坡多样性开关
DECLARE_SPDM_ABTEST_BOOL(enable_author_ecpm_prerank_sort);  // [xiemiao] 直播爬坡排序开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_fans_prerank_video);  // [linglong] 短视频 粉丝爬坡开关
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_explore_prerank_papo);  // [chenwu03] 小店探索召回爬坡
DECLARE_SPDM_ABTEST_BOOL(enable_union_shortvideo_dark_prerank_papo);  // [chenwu03] 联盟短视频暗投策略
DECLARE_SPDM_ABTEST_BOOL(enable_universe_llm_u2p_recall);  // [huoyan03] 联盟 llm u2p 粗排爬坡
DECLARE_SPDM_ABTEST_BOOL(enable_universe_llm_u2p_v2_recall);  // [huoyan03] 联盟 llm u2p 粗排爬坡
DECLARE_SPDM_KCONF_INT64(universeDcafPrerankTimeoutExtraTime);
// 联盟 DCAF 动态算力通过超时时间来调控档位
DECLARE_SPDM_ABTEST_STRING(universe_dcaf_rank_timeout_tp_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_smart_compute_dynamic_quota);  //  [liangli] 联盟智能算力动态档位开关
DECLARE_SPDM_ABTEST_BOOL(enable_sug_query_type_fix);     //  [yinliang] sug 打标 bug fix
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_ctcvr_auto_cpa_bid);   //  [fengyajuan] 联盟 ctcvr
DECLARE_SPDM_ABTEST_STRING(universe_dcaf_prerank_version_id);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_communication_green_channel);  // [xuxuejian] 联盟通信粗排爬坡
DECLARE_SPDM_KCONF_BOOL(enableIsolateUniversePrerank);  // [yangzhanwu] 分离联盟粗排开关
DECLARE_SPDM_ABTEST_STRING(universe_prerank_diverse_lc_distribute_tag);  // [linglong] 厂商分发 大系统粗排多样性 kconf 配置 // NOLINT
DECLARE_SPDM_KCONF_BOOL(universeSkipPrerankFillRtaBidInfo);  // [jiangyifan] FillBidRtaInfo 前置到 ranking // NOLINT

}  // namespace prerank
}  // namespace universe_base
}  // namespace ks
