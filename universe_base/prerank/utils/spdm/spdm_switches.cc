#include "teams/ad/universe_base/prerank/utils/spdm/spdm_switches.h"

namespace ks {
namespace universe_base {
namespace prerank {
SPDM_ABTEST_DOUBLE(universe_inner_nobid_roas_discount, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_inner_nobid_cpa_discount, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_universe_inner_deep_select, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_prerank_context_id_list_feature, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adserver2, enableDetailCpmSampleSkipDcaf);
SPDM_KCONF_BOOL(ad.adserver2, enableDisableDcafWhenDegrade);
SPDM_ABTEST_BOOL(enable_dcaf_prerank_extra_time_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dcaf_rank_extra_time_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_remove_prerank_ltv, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_INT64(ad.adtarget3, universeDcafRankTimeoutExtraTime, 20);
SPDM_ABTEST_BOOL(enable_univ_prerank_es_score_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_skip_prearank_quota_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_skip_prearank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_prerank_real_action_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_onestep_req_preprank_model, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adtarget2, enableSearchQueryNormP2p);
SPDM_ABTEST_BOOL(enable_app_store_white_pkg_opt_pos_algin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enableUniverseTinyLLMFixedQueryType, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_guard_auto_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_guard_auto_bid_with_filter_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_package_distribute_top_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(univ_prerank_hyper_score_gamma, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(univ_inner_skip_prearank_quota, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(univ_live_deep_skip_prearank_quota, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseInnerGreenChannelMediaConfig);
SPDM_ABTEST_INT64(universe_prerank_inner_supported_group_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(universe_package_distribute_prerank_app_uniq_num, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(universe_app_search_prerank_diversity_uniq_quota, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_universe_tiny_prerank_diverse_layer_control_distribute, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_tiny_prerank_diverse_lc_search_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_tiny_prerank_diverse_lc_distribute_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_universe_tiny_search_prerank_diverse_by_index, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_tiny_search_package_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_tiny_search_prerank_diverse_by_index_abtag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_universe_dcaf_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(skip_universe_dcaf_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_deep_new_ocpx_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_inner_high_value_crowd_target_tag_key, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_audience_diverse_prerank, ks::AbtestBiz::AD_DSP);  // [xiemiao] 直播多样性开关
SPDM_ABTEST_BOOL(enable_author_ecpm_prerank_sort, ks::AbtestBiz::AD_DSP);  // [xiemiao] 直播爬坡排序开关
SPDM_ABTEST_BOOL(enable_universe_fans_prerank_video, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_explore_prerank_papo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_llm_u2p_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_llm_u2p_v2_recall, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_INT64(ad.adtarget3, universeDcafPrerankTimeoutExtraTime, 20);
SPDM_ABTEST_STRING(universe_dcaf_rank_timeout_tp_ratio, ks::AbtestBiz::AD_DSP, "99");
SPDM_ABTEST_BOOL(enable_universe_smart_compute_dynamic_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sug_query_type_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prerank_ctcvr_auto_cpa_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_dcaf_prerank_version_id, ks::AbtestBiz::AD_DSP, "prerank_base");
SPDM_ABTEST_BOOL(enable_union_shortvideo_dark_prerank_papo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_communication_green_channel, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adtarget3, enableIsolateUniversePrerank);
SPDM_ABTEST_STRING(universe_prerank_diverse_lc_distribute_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_KCONF_BOOL(ad.adtarget3, universeSkipPrerankFillRtaBidInfo);

}  // namespace prerank
}  // namespace universe_base
}  // namespace ks
