#pragma once
#include <absl/container/flat_hash_map.h>
#include <absl/container/flat_hash_set.h>

#include <utility>
#include <string>
#include <vector>

#include "teams/ad/engine_base/utils/reco_user_info_wrapper.h"
#include "teams/ad/engine_base/cmd_curator/ad_base_data.h"
#include "teams/ad/ad_base/src/common/pod_check.h"
#include "teams/ad/ad_base/src/common/enum.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/universe_base/prerank/common/enum.h"

namespace ks {
namespace universe_base {
namespace prerank {
struct CarmPrerankInfo {
  std::vector<int64_t> author_id_list;
  std::vector<int64_t> account_id_list;
  std::vector<int64_t> photo_id_list;
  std::vector<int64_t> industry_id_v3_list;
  std::vector<int64_t> city_product_id_list;
  std::vector<int64_t> ocpx_action_type_list;
  void Clear() {
    author_id_list.clear();
    account_id_list.clear();
    photo_id_list.clear();
    industry_id_v3_list.clear();
    city_product_id_list.clear();
    ocpx_action_type_list.clear();
  }
};

// 用来上报 tracelog 辅助
class TraceFilterHelper {
 public:
  TraceFilterHelper(std::function<void()> &&init_handler, std::function<void()> &&destruct_handler)
      : destruct_handler_(std::move(destruct_handler)) {
    init_handler();
  }

  ~TraceFilterHelper() {
    destruct_handler_();
  }

 private:
  std::function<void()> destruct_handler_;
};

struct UniversePrerankPrepareParams {
  int32_t max_prerank_num = 300;
  bool is_enable_prerank = true;
  bool fake_flag = false;
  uint64_t ab_test_hash_id = 0;
  ks::engine_base::RecoUserInfoWrapper reco_user_info_wrapper;

  std::string dcaf_rank_version_id;
  std::string dcaf_prerank_version_id;
  int32_t max_prerank_num_before_dcaf = 0;
  int32_t max_retrieval_creatives_before_dcaf = 0;
  int32_t max_retrieval_creatives = 0;
  int32_t dcaf_pred_time_prerank = 0;
  int32_t dcaf_pred_time_rank = 0;

  void Clear();
};


struct AdPreRankNonPod {
  struct PredictScoreMeta {
    double score = 0.0;
    int32 cmd_id;
    int32 use_counter;
  };
  struct RScoreMeta {
    double score = 0.0;
    int32 use_counter;
  };
  std::vector<int> prerank_strategy_tags;  // 记录粗排修改预估值的策略 tag
  mutable absl::flat_hash_set<int64_t> prerank_cmd_ids;
  mutable absl::flat_hash_set<int64_t> cmd_key_ids;
  mutable absl::flat_hash_map<int32_t, PredictScoreMeta> predict_score_list;
  mutable absl::flat_hash_map<int32_t, RScoreMeta> r_score_list;

  mutable std::vector<int64_t> req_cmd_ids_list;
  mutable std::vector<int64_t> req_cmd_key_ids_list;

  // predict score 读接口
  double get_predict_score(const engine_base::PredictType& pt) const;
  double get_r_score(const engine_base::RType& rt) const;
  void set_predict_score(int32_t rt, int32_t pt, double score, int32_t cmd_id) const;
  // cmd id 接口
  void add_prerank_cmd_ids(int64_t cmd_id, int64_t cmd_key_id = 0) const {
    prerank_cmd_ids.insert(cmd_id);
    req_cmd_ids_list.push_back(cmd_id);
    req_cmd_key_ids_list.push_back(cmd_key_id);
  }

  const absl::flat_hash_set<int64_t>& get_prerank_cmd_ids() const {
    return prerank_cmd_ids;
  }

  const std::vector<int64_t>& getReqCmdIdsList() const { return req_cmd_ids_list; }
  const std::vector<int64_t>& getReqCmdKeyIdsList() const { return req_cmd_key_ids_list; }

  void Clear() {
    prerank_strategy_tags.clear();
    predict_score_list.clear();
    r_score_list.clear();
    prerank_cmd_ids.clear();
    cmd_key_ids.clear();

    req_cmd_ids_list.clear();
    req_cmd_key_ids_list.clear();
  }
};

struct AdPreRank : public ad_base::PodDataBase<AdPreRank> {
  double ctr = 0.0;
  double delivery_rate = 0.0;
  double new_creative_delivery_rate = 0.0;
  double conv_nextstay = 0.0;
  double conversion_rate = 0.0;
  double purchase_ltv = 0.0;
  double purchase_ltv7 = 0.0;
  double order_submit = 0.0;
  double landingpage_submit_rate = 0.0;
  double prerank_universe_deep = 0.0;
  double conv2_purchase = 0.0;
  double lps2_purchase = 0.0;
  double prerank_universe_invoke = 0.0;
  double prerank_universe_ctcvr = 0.0;
  double prerank_universe_ltr = 0.0;
  double prerank_ltr_sort_score = 0.0;
  double prerank_ltr_sort_score_norm = 0.0;
  double universe_prerank_item_click = 0.0;
  double c1_event_order = 0.0;
  double universe_jiuniu_merchant_roas = 0.0;
  double c1_merchant_follow = 0.0;
  double prerank_imp_key_inapp_rate = 0.0;
  double univ_xdt_audience_rate = 0.0;
  double univ_xdt_pay_rate = 0.0;
  double univ_xdt_roas_rate = 0.0;
  // preranking
  double score = 0.0;
  int32_t pos_in_prerank = 0;
  // 联盟无值，设置默认值
  double cvr = 0.0;
  double ecpm_unify_ctcvr = 0.0;
  double ecpm_unify_ctcvr_norm = 0.0;
  // 探索分
  double ee_score = 0.0;
  // 信息流粗排
  double ueq = 0.0;
  double bonus_cpm = 0.0;
  double bonus_rate = 0.0;  // 粉条直播粗排 bonus 分
  double click2_lps = 0.0;
  double prerank_thanos_xdt_order_paied = 0.0;
  double click2_deep_rate = 0.0;
  double click2_conv = 0.0;
  double nebula_prerank_merchant_follow = 0.0;
  double nebula_prerank_purchase = 0.0;
  double click2_app_invoked = 0.0;
  double server_show_cvr = 0.0;
  double unify_ctr = 0.0;  // 统一 ctr
  kuaishou::ad::AdActionType unify_ctr_e_type;
  double unify_cvr = 0.0;  // 统一 cvr
  kuaishou::ad::AdActionType unify_cvr_e_type;
  double unify_deep_cvr = 0.0;  // 统一 deep_cvr
  kuaishou::ad::AdActionType unify_deep_cvr_e_type;
  double prerank_cpm_ltr = 0.0;
  double prerank_ctcvr_score = 0.0;
  double prerank_real_action_score = 0.0;
  double prerank_real_action_score_norm = 0.0;
  double prerank_seven_day_pay_times = 0.0;
  int64_t real_action_idx = 0;
  int64_t ue_idx = 0;
  int ue_freq = 0;
  int64_t ecpm_idx = 0;  // ecpm 排序 idx
  int64_t e2e_idx = 0;   // e2e 排序 idx
  int64_t ctr_idx = 0;   // ctr 排序 idx
  int64_t e2e_pc_live_idx = 0;
  int64_t ecpm_pc_live_idx = 0;
  int64_t e2e_mobile_live_idx = 0;
  int64_t ecpm_mobile_live_idx = 0;
  int64_t recall_idx = 0;
  int64_t cvr_bid_idx = 0;
  int64_t cpm_ltr_idx = 0;
  int64_t ctcvr_idx = 0;   //  联盟 ctcvr idx
  int64_t ueq_idx = 0;
  double sp_score0 = 0.0;
  int64_t sp_idx0 = 0;
  double epsp_degree = 0.0;   // epsp_degree
  double epsp_preference = 0.0;   // epsp_preference
  int epsp_i = 0;  // epsp original index.
  double ensemble_score = 0.0;   // ensemble sort
  int32_t ensemble_score_idx = 0;
  double two_way_ensemble_score = 0.0;   // 2-way ensemble sort
  double three_way_ensemble_score = 0.0;   // 3-way ensemble sort
  bool need_ecpm_secondary_sort = false;   // ECPM 二次排序标记位
  double prod_apr = 0.0;         // 淘系电商产品化 apr
  double click2_prod_apr = 0.0;  // click2 --> prod_apr
  double button_click = 0.0;
  double play3s = 0.0;
  double ntr = 0.0;
  double multitask_play3s = 0.0;
  double multitask_ltr = 0.0;
  double multitask_wtr = 0.0;
  double deep_rate = 0.0;
  double c1_prerank_unified = 0.0;
  double prerank_ecpm_ctr = 0.0;  // 粗排 ctr
  double prerank_ecpm_cvr = 0.0;  // 粗排 cvr
  double click_credit_rate = 0.0;
  double conv_key_action_rate = 0.0;  // 激活到关键行为率
  double prerank_new_creative_lps = 0.0;
  double prerank_new_creative_conv = 0.0;
  double prerank_new_creative_deep = 0.0;
  double new_creative_cvr = 0.0;
  double jinniu_prerank_score = 0.0;  // 金牛排序 score
  double feed_prerank_merchant_follow = 0.0;
  double feed_prerank_purchase = 0.0;
  double click_app_invoked = 0.0;                  // 一跳唤端
  double pred_ad_time = 0.0;                       // 预估广告时长
  double merchant_ltv = 0.0;                       // 电商 ltv
  double prerank_follow_ltv = 0.0;                // merchant_follow_roi ltv
  double prerank_wanjian_shouxin_rate_ecpc = 0.0;  // 小额贷款粗排 ecpc 策略
  double prerank_biaodan_shouxin_rate = 0.0;       // 小额贷款粗排授信单出价表单切分
  double c2_prerank_unified = 0.0;
  double click2_credit_rate = 0.0;
  double goods_view = 0.0;
  double order_paid = 0.0;
  double prod_merchant_follow = 0.0;
  int64_t dynamic_from = 0;
  int64_t return_idx = 0;
  int64_t dynamic_idx = 0;
  double dynamic_score = 0.0;
  int64_t dynamic_exp_tag = 0;
  // 补充
  double photo_to_live_prerank_p3s = 0.0;
  double shop_live_prerank_p3s = 0.0;
  double photo_to_live_item_imp_wtr = 0.0;
  double photo_to_live_server_show_ctr = 0.0;
  double live_prerank_p3s_wtr = 0.0;
  double prerank_p3s_goods_view = 0.0;
  double prerank_p3s_pay_rate = 0.0;
  double prerank_ecpm_live_ctr = 0.0;
  double live_prerank_inroom_nebula = 0.0;
  double live_prerank_inroom_feed = 0.0;
  double photo_to_live_item_imp_p3s = 0.0;
  double photo_to_live_item_pay_rate = 0.0;
  double live_prerank_p3s_ltv = 0.0;
  double live_prerank_stay_time = 0.0;
  // auxiliary strategy
  uint64_t merchant_auxiliary_deli_rate_key = 0;
  uint64_t merchant_auxiliary_cvr_key = 0;

  double ocpm_auxiliary_cvr = 0.0;
  double auxiliary_delivery_rate = 0.0;

  double merchant_prerank_deli_rate_auxiliary_ratio = 0.0;
  double merchant_prerank_cvr_auxiliary_ratio = 0.0;
  // 保存某个通路召回的创意在召回通路的 index 关系。
  int64_t one_tag_index = 10000;

  // 粗排多路权重
  double prerank_ecpm_weight = 1.0;
  double prerank_ecpm_score_weight = 0.0;
  double prerank_ecpm_score_idx_weight = 0.0;
  double prerank_e2e_ensemble_weight = 1.0;
  double prerank_e2e_score_weight = 0.0;
  double prerank_e2e_score_idx_weight = 0.0;
  double prerank_cpm_ltr_ensemble_weight = 0.0;
  double prerank_cpm_ltr_score_weight = 0.0;
  double prerank_cpm_ltr_score_idx_weight = 0.0;
  // check 添加兴趣通路 idx 权重
  double prerank_real_action_weight = 0.0;

  double click2_unified = 0.0;

  // 内循环短视频粗排 ctcvr 预估值
  double prerank_ctcvr = 0.0;

  int64_t dragon_auto_cpa_bid = 0;
  int32_t dragon_auto_cpa_bid_modify_tag = 0;
  double dragon_ecpm = 0.0;
  int64_t dragon_ecpm_idx = 0;
  double dragon_cpm_ltr = 0.0;
  int64_t dragon_cpm_ltr_idx = 0;
  double dragon_delivery_rate = 0.0;
  int64_t dragon_e2e_idx = 0;
  double dragon_new_creative_delivery_rate = 0.0;
  double dragon_ensemble_score = 0.0;
  int64_t dragon_ensemble_score_idx = 0;
  int32_t dragon_pos_in_prerank = 10000;
  int32_t dragon_layer_select_type = 0;
  int32_t dragon_ad_select_type = 0;
  int32_t dragon_reason = 0;
  int32_t dragon_idx = 0;
  bool dragon_is_hit_bonus = 0;
  bool dragon_is_inner_green_channel = 0;
  double dragon_prerank_score = 0.0;
  int64_t dragon_prerank_type = 0;
  // 高光时刻标记
  bool hot_live_flag = false;

  // 粗排排序因子
  double gpm = 0.0;
  double leverage_score = 0.0;
  int64_t gpm_live_idx = 0;
  int64_t gpm_mobile_live_idx = 0;
  int64_t gpm_pc_live_idx = 0;
  int32_t universe_tiny_query_type = 0;

  void SetUnifyCtr(double r, kuaishou::ad::AdActionType end) {
    unify_ctr = r;
    unify_ctr_e_type = end;
  }
  void SetUnifyCvr(double r, kuaishou::ad::AdActionType end) {
    unify_cvr = r;
    unify_cvr_e_type = end;
  }
  void SetUnifyDeepCvr(double r, kuaishou::ad::AdActionType end) {
    unify_deep_cvr = r;
    unify_deep_cvr_e_type = end;
  }

  double GetUnifyCtr() const {
    return unify_ctr;
  }

  double GetUnifyCvr() const {
    return unify_cvr;
  }

  double GetUnifyDeepCvr() const {
    return unify_deep_cvr;
  }

  double GetUnifyEcpmRatio() const {
    return unify_ecpm_ratio;
  }

  void ModifyUnifyCxr(RUnifyType r_type, RUnifyTag r_tag, double r) {
    switch (r_type) {
    case RUnifyType::CTR:
      unify_ctr = r;
      unify_ctr_r_tag = r_tag;
      break;
    case RUnifyType::CVR:
      unify_cvr = r;
      unify_cvr_r_tag = r_tag;
      break;
    case RUnifyType::DEEP_CVR:
      unify_deep_cvr = r;
      unify_deep_cvr_r_tag = r_tag;
      break;
    case RUnifyType::ECPM_RATIO:
      unify_ecpm_ratio = r;
      unify_ecpm_ratio_r_tag = r_tag;
    default:
      break;
    }
  }

 private:
  RUnifyTag unify_ctr_r_tag = RUnifyTag::UNKNOWN_TAG,
            unify_cvr_r_tag = RUnifyTag::UNKNOWN_TAG,
            unify_deep_cvr_r_tag = RUnifyTag::UNKNOWN_TAG,
            unify_ecpm_ratio_r_tag = RUnifyTag::UNKNOWN_TAG;
  double unify_ecpm_ratio = 1.0;
};

struct AppendStrategyInfo : public ad_base::PodDataBase<AppendStrategyInfo> {
  int32_t append_group{0};
  int32_t append_tag{0};
  int32_t sort_tag{0};
  int32_t index{-1};
  float score{0};
  int64_t universe_green_channel_stra_id{0};
  int64_t universe_green_channel_ad_id{0};
  int64_t universe_green_channel_media_id{0};
  bool universe_green_channel_white_green{false};
};



struct AdBidInfo : public ad_base::PodDataBase<AdBidInfo> {
 protected:
  int64 auto_cpa_bid = 0;  // 系统调整后的 ocpc 第二阶段出价
  ad_base::AutoCpaBidModifyTagType auto_cpa_bid_modify_tag =
      ad_base::AutoCpaBidModifyTagType::kDefault;  // 修改 auto_cpa_bid 时需要指定的 code 标记
  double auto_roas = 0.0;
  ad_base::AutoRoasModifyTagType auto_roas_modify_tag = ad_base::AutoRoasModifyTagType::kRoasDefault;

  kuaishou::ad::AdEnum::RtaDyRatioType rta_sta_tag = kuaishou::ad::AdEnum::AUTO_CPA_BID_ONLY;
  double rta_ratio = 0.0;
  int64_t rta_bid = 0;

 public:
  int64_t group_idx = 0;
  int64 raw_auto_cpa_bid = 0;
  int64_t exp_start_ts = 0;  // add
  int64_t ad_bid_server_group_tag = 0;
  int32_t bid_explore_group_idx = 0;
  int64_t deep_group_index = 0;

  double deep_flow_control_rate = 0.1;
  double deep_min_coef = 0;
  double deep_min_bid_coef = 0;
  float backflow_cv = 0;        //
  int64 auto_deep_cpa_bid = 0;  // 系统调整后的 ocpc 深度出价
  bool is_calibration = false;
  double calibration_ratio = 1.0;
  double acc_cold_start_coef = 0.0;  // ROI 广告的加速探索系数 add
  double twin_bid_coef = 1.0;
  double price_separate_ratio = 1.0;
  kuaishou::ad::BidServerType bid_server_type = kuaishou::ad::BID_SERVER_MAIN;
  int32_t bid_ad_type = 1;
  kuaishou::ad::AdEnum::DeepTwinBidStrategys twin_bid_strategy = kuaishou::ad::AdEnum::MIN_OCPC_DEEP;
  float first_coef = 1.0;
  float second_coef = 1.0;
  double allocated_budget_unify = 0.0;  // 最大转化单位时间分配预算，用于召回排序
  double price_ratio = 1.0;
  float target_factor = 1.0;
  float budget_coef = 1.0;
  float cpa_coef = 1.0;
  // MCB 的投放策略控制
  kuaishou::ad::AdEnum::BidStrategyGroup bid_strategy_group = kuaishou::ad::AdEnum::DEFAULT_BID_STRATEGY;
  kuaishou::ad::AdEnum::CampaignAutoManage auto_manage = kuaishou::ad::AdEnum::AUTO_MANAGE_CLOSE;
  kuaishou::ad::AdEnum::CampaignAutoAdjust auto_adjust = kuaishou::ad::AdEnum::AUTO_ADJUST_CLOSE;
  float mcb_cpa_bid = 0.0;  // mcb 成效预估， ab 平台用来计算 target_cost
  float mcb_roi_ratio = 0.0;  // mcb 成效预估， ab 平台用来计算 target_cost
  float performance_fix_ratio = 0.0;  // mcb 成效预估 fix_ratio
  float product_cpa_bid = 0.0;  // mcb 后验，作为竞价基准
  float product_roi_ratio = 0.0;  // mcb 后验，作为竞价基准
  bool is_account_bidding = false;
  bool is_inner_ad = false;
  double auto_bid_weight = 1.0;
  float bid_coef = 1.0;
  int64_t aggre_key = 0;
  int64 sort_bid = 0;
  double cost_ratio = 0.0;
  double fanstop_cvr_threshold_ratio = 1.0;
  // rta 增长数据
  float lt_7{0.0};
  float next_stay_rate{0.0};
  float lt7over1{0.0};
  float roi{0.0};
  int32 product{0};

  bool SetAutoRoas(double new_auto_roas, ad_base::AutoRoasModifyTagType modify_tag) {
    auto_roas = new_auto_roas;
    auto_roas_modify_tag = modify_tag;
    return true;
  }

  double GetAutoRoas() const {
    return auto_roas;
  }

  ad_base::AutoRoasModifyTagType GetAutoRoasModifyTag() const {
    return auto_roas_modify_tag;
  }

  bool SetAutoCpaBid(int64 target_auto_cpa_bid, ad_base::AutoCpaBidModifyTagType modify_tag) {
    if (auto_cpa_bid != target_auto_cpa_bid) {
      auto_cpa_bid_modify_tag = modify_tag;
      auto_cpa_bid = target_auto_cpa_bid;
    }
    return true;
  }

  int64 GetAutoCpaBid() const {
    return auto_cpa_bid;
  }

  int64 GetSortBid() const {
    return sort_bid > 0 ? sort_bid : auto_cpa_bid;
  }

  ad_base::AutoCpaBidModifyTagType GetAutoCpaBidModifyTag() const {
    return auto_cpa_bid_modify_tag;
  }

  void SetRtaStaTag(kuaishou::ad::AdEnum::RtaDyRatioType type) {
    rta_sta_tag = type;
  }
  void SetRtaRatio(double ratio) {
    rta_ratio = ratio;
  }
  void SetRtaBid(int64_t bid) {
    rta_bid = bid;
  }
  kuaishou::ad::AdEnum::RtaDyRatioType GetRtaStaTag() const {
    return rta_sta_tag;
  }
  double GetRtaRatio() const {
    return rta_ratio;
  }
  int64_t GetRtaBid() const {
    return rta_bid;
  }
} __attribute__((aligned(8)));

struct MainStrategyInfo : public ad_base::PodDataBase<MainStrategyInfo> {
  int64_t tag{0};
  int32_t index{-1};
  float score{0};
} __attribute__((aligned(8)));

}  // namespace prerank
}  // namespace universe_base
}  // namespace ks
