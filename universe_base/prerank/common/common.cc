#include "teams/ad/universe_base/prerank/common/common.h"
namespace ks {
namespace universe_base {
namespace prerank {
void UniversePrerankPrepareParams::Clear() {
  max_prerank_num = 300;
  is_enable_prerank = true;
  fake_flag = false;
  ab_test_hash_id = 0;
  reco_user_info_wrapper.Clear();
  dcaf_rank_version_id.clear();
  dcaf_prerank_version_id.clear();
  max_prerank_num_before_dcaf = 0;
  max_retrieval_creatives_before_dcaf = 0;
  max_retrieval_creatives = 0;
  dcaf_pred_time_prerank = 0;
  dcaf_pred_time_rank = 0;
}

// predict score 读接口
double AdPreRankNonPod::get_predict_score(const engine_base::PredictType& pt) const {
  if (auto it = predict_score_list.find(static_cast<int32_t>(pt)); it != predict_score_list.end()) {
    it->second.use_counter++;
    return it->second.score;
  }
  return 0.0;
}

double AdPreRankNonPod::get_r_score(const engine_base::RType& rt) const {
  if (auto it = r_score_list.find(rt); it != r_score_list.end()) {
    it->second.use_counter++;
    return it->second.score;
  }
  return 0.0;
}

void AdPreRankNonPod::set_predict_score(int32_t rt, int32_t pt, double score, int32_t cmd_id) const {
  auto& predict_score = predict_score_list[pt];
  // 有冲突, 打点 & 打日志
  if (predict_score.cmd_id != 0 && predict_score.cmd_id != cmd_id) {
    LOG_EVERY_N(INFO, 10000) << "predict_type_conflict: cmd id: " << cmd_id << ", pt: " << pt
                              << ", predict_score.cmd_id: " << predict_score.cmd_id;
    return;
  }

  auto& r_score = r_score_list[rt];
  predict_score.score = r_score.score = score;
  predict_score.cmd_id = cmd_id;
}
}  // namespace prerank
}  // namespace universe_base
}  // namespace ks
