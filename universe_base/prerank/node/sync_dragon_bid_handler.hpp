#include "teams/ad/universe_base/prerank/node/sync_dragon_bid_handler.h"
#include "teams/ad/universe_base/prerank/utils/kconf/kconf.h"
#include "teams/ad/universe_base/prerank/utils/spdm/spdm_switches.h"

#include <memory>
#include <string>
#include <utility>

#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_trace_common.pb.h"
#include "teams/ad/engine_base/universe/universe_deep_target_config.h"
#include "teams/ad/ad_base/src/kconf_flags/kconf_flags.h"
#ifdef TARGET_UNIVERSE_SCHEMA_CUT
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Campaign.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Account.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Unit.h"
#else
#include "teams/ad/ad_table/code_generator/ad_target_universe/Campaign.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Account.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Unit.h"
#endif

namespace ks {
namespace universe_base {
namespace prerank {
template <typename ConfigClass, typename PrerankAdList>
void SyncDragonBidHandler<ConfigClass, PrerankAdList>::IssueRpc() {
  // Build Request
  BuildRequest();
  // 非阻塞请求
  bid_handler_wrapper_->AsyncSendRequest(!session_data_->lane_id.empty(),
                                         session_data_->kenv);
}

template <typename ConfigClass, typename PrerankAdList>
bool SyncDragonBidHandler<ConfigClass, PrerankAdList>::ProcessInner() {
  session_data_ = AdSyncNode::context_->template GetMutableContextData<ContextData>();
  bid_handler_wrapper_ = std::make_unique<engine_base::DragonBidHandlerWrapper>();
  if (session_data_ == nullptr) {
    LOG(ERROR) << "context_data_ init failed.";
    return false;
  }

  ad_list_ = session_data_->prerank_ad_list.get();
  auto flow_type = ad_base::BidServiceFlowType::UNIVERSE;

  bid_handler_wrapper_->Init(session_data_->session_context,
                        &session_data_->spdm_ctx, flow_type);
  // 设置标志
  bid_handler_wrapper_->SetGetIndustryFromAccount();

  IssueRpc();
  bid_handler_wrapper_->WaitInner();
  ParseResponseImpl();

  return true;
}

template <typename ConfigClass, typename PrerankAdList>
void SyncDragonBidHandler<ConfigClass, PrerankAdList>::ParseResponseImpl() {
  bid_handler_wrapper_->OuterFillBidInfo(ad_base::AutoCpaBidModifyTagType::kFillBidMsg, ad_list_->Ads());
  FillRtaBidInfoV2(ad_list_);
  UniverseInnerNoBidProcessor();
  if (!SPDM_enable_close_guard_auto_bid(session_data_->spdm_ctx)) {
    GuardAutoBid();
  }
  if (SPDM_enable_close_guard_auto_bid_with_filter_ad(session_data_->spdm_ctx)) {
    for (auto p_ad : ad_list_->Ads()) {
      if (!IS_OCPX(p_ad->bid_type()) && !p_ad->is_furious_cpa_ads()) {
        continue;
      }
      // ROI 预算
      if (p_ad->roi_ratio() > 1e-6) {
        if (p_ad->bid_info.GetAutoRoas() <= 1e-6) {
          p_ad->SetAdInValid(kuaishou::log::ad::AdTraceFilterCondition::GET_AUTO_BID_FAILED_FILTER,
            kuaishou::ad::AdServerNodeType::PRERANKING_PREPARE_TYPE);
          session_data_->dot->Count(1, "get_auto_bid_failed_filter_count", "auto_roas_zero");
        }
      } else {
        if (p_ad->bid_info.GetAutoCpaBid() <= 0) {
          p_ad->SetAdInValid(kuaishou::log::ad::AdTraceFilterCondition::GET_AUTO_BID_FAILED_FILTER,
            kuaishou::ad::AdServerNodeType::PRERANKING_PREPARE_TYPE);
          session_data_->dot->Count(1, "get_auto_bid_failed_filter_count", "auto_cpa_bid_zero");
        }
      }
    }
  }
}

template <typename ConfigClass, typename PrerankAdList>
void SyncDragonBidHandler<ConfigClass, PrerankAdList>::UniverseInnerNoBidProcessor() {
  for (auto p_ad : ad_list_->Ads()) {
    if (p_ad->campaign_type() != kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE &&
        p_ad->campaign_type() != kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
      continue;
    }
    auto& bid_info = p_ad->bid_info;
    if (p_ad->speed() != kuaishou::ad::AdEnum::SPEED_NO_BID) {
      continue;
    }
    if (p_ad->ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
        p_ad->ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI ||
        p_ad->ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS) {
      auto universe_inner_nobid_roas_discount =
        SPDM_universe_inner_nobid_roas_discount(session_data_->spdm_ctx);
      if (universe_inner_nobid_roas_discount > 0) {
        bid_info.SetAutoRoas(bid_info.GetAutoRoas() /
                             universe_inner_nobid_roas_discount,
                             ad_base::AutoRoasModifyTagType::kRoasUniverseNobidModify);
        p_ad->set_roi_ratio(bid_info.GetAutoRoas());
      }
    } else {
      bid_info.SetAutoCpaBid(bid_info.GetAutoCpaBid() *
                             SPDM_universe_inner_nobid_cpa_discount(session_data_->spdm_ctx),
                             ad_base::AutoCpaBidModifyTagType::kMCB);
      p_ad->set_cpa_bid(bid_info.GetAutoCpaBid());  //  nobid cpa_bid 为空，需要填上该字段
    }
  }
}

template <typename ConfigClass, typename PrerankAdList>
void SyncDragonBidHandler<ConfigClass, PrerankAdList>::GuardAutoBid() {
  auto guadrd_func = [&](auto* p_ad) {
  };
  bid_handler_wrapper_->OuterGuardAutoBid(ad_list_->Ads(),
    ad_base::AutoCpaBidModifyTagType::kGuardFillBidMsg,
    guadrd_func);
}

template <typename ConfigClass, typename PrerankAdList>
void SyncDragonBidHandler<ConfigClass, PrerankAdList>::BuildRequest() {
  bid_handler_wrapper_->param_.page_id = session_data_->page_id;
  bid_handler_wrapper_->param_.sub_page_id = session_data_->sub_page_id;
  bid_handler_wrapper_->param_.is_unlogin_user =
    session_data_->ad_request->ad_user_info().is_unlogin_user();
  bid_handler_wrapper_->param_.request_flow_type = session_data_->ad_request->ad_request_flow_type();
  bid_handler_wrapper_->param_.media_app_id = session_data_->pos_manager_base.GetRequestAppId();
  bid_handler_wrapper_->param_.CalcDeepAutoBidTag(session_data_->pos_manager_base);
  bid_handler_wrapper_->BuildRequestHeader(session_data_->llsid,
      session_data_->user_id, session_data_->ad_request->ad_user_info().device_id(),
      session_data_->ad_request->ad_user_info().abtest_mapping_id());
  bid_handler_wrapper_->UniverseBuildRequest(ad_list_->Ads());
}


template <typename ConfigClass, typename PrerankAdList>
void SyncDragonBidHandler<ConfigClass, PrerankAdList>::FillRtaBidInfoV2(PrerankAdList *ad_list) {
  if constexpr(!ContextData::skip_prerank_fill_rta_bid_info) { // 这段代码前置到 target 的 ranking, ad server 无需调用
    if (!SPDM_universeSkipPrerankFillRtaBidInfo()) {
      const auto& rta_config = AdKconfUtil::rtaDyBidConfig();
      int64_t rta_ratio_exp_sta_tag =
          session_data_->session_context->TryGetInteger("rta_ratio_exp_sta_tag", 0);
      static absl::flat_hash_set<kuaishou::ad::AdEnum_RtaDyRatioType> rta_sta_tags{
          kuaishou::ad::AdEnum::RATIO_AND_AUTO_CPA_BID,
          kuaishou::ad::AdEnum::RATIO_AND_CPA_BID,
          kuaishou::ad::AdEnum::RATIO_AND_AUTO_CPA_BID_ONLINE,
          kuaishou::ad::AdEnum::RATIO_AND_AUTO_CPA_BID_SECOND,
          kuaishou::ad::AdEnum::RATIO_AND_CPA_BID_ONLINE,
          kuaishou::ad::AdEnum::RATIO_AND_CPA_BID_SECOND,
          kuaishou::ad::AdEnum::BID_AND_CPA_BID_DIR,
          kuaishou::ad::AdEnum::BID_AND_CPA_BID_DIR_SECOND};
      auto rta_dy_pro = [&](auto* p_ad, int64_t account_id, std::string& product_name) -> bool {
        int32_t rta_type = 0;
        auto iter = rta_config->data().rta_exp().find(rta_ratio_exp_sta_tag);
        if (iter != rta_config->data().rta_exp().end()) {
          auto account_iter = iter->second.account_id().find(account_id);
          if (account_iter != iter->second.account_id().end()) {
            rta_type = account_iter->second;
          } else {
            auto product_iter = iter->second.product_name().find(product_name);
            if (product_iter != iter->second.product_name().end()) {
              rta_type = product_iter->second;
            }
          }
        }
        if (rta_sta_tags.count(static_cast<kuaishou::ad::AdEnum_RtaDyRatioType>(rta_type)) > 0) {
          p_ad->bid_info.SetRtaStaTag(static_cast<kuaishou::ad::AdEnum_RtaDyRatioType>(rta_type));
        }
        return true;
      };
      for (auto p_ad : ad_list->Ads()) {
        // rta 广告填充 rta ratio
        auto account_id = p_ad->account_id();
        auto product_name = p_ad->product_name();
        auto unit_id = p_ad->unit_id();
        bool is_rta = false;
        auto rta_account_info = session_data_->rta_ad_data.GetAccountInfo(account_id);
        if (rta_account_info != nullptr) {
          if (rta_account_info->all_unit) {
            p_ad->bid_info.SetRtaRatio(rta_account_info->all_bid_ratio);
            p_ad->bid_info.SetRtaBid(rta_account_info->all_rta_bid);
          } else {
            auto response_ratio = rta_account_info->unit_rta_bid_ratio_map.find(unit_id);
            if (response_ratio != rta_account_info->unit_rta_bid_ratio_map.end()) {
              p_ad->bid_info.SetRtaRatio(response_ratio->second);
            }
            auto response_bid = rta_account_info->unit_rta_bid_map.find(unit_id);
            if (response_bid != rta_account_info->unit_rta_bid_map.end()) {
              p_ad->bid_info.SetRtaBid(response_bid->second);
            }
          }
          p_ad->bid_info.lt_7 = rta_account_info->lt_7;
          p_ad->bid_info.next_stay_rate = rta_account_info->next_stay_rate;
          p_ad->bid_info.lt7over1 = rta_account_info->lt7over1;
          p_ad->bid_info.roi = rta_account_info->roi;
          p_ad->bid_info.product = rta_account_info->product;
          is_rta = true;
        }
        // RTA 动态出价标记
        if (is_rta) {
          rta_dy_pro(p_ad, account_id, product_name);
        }
      }
    }
  }
}
}  // namespace prerank
}  // namespace ad_target
}  // namespace ks