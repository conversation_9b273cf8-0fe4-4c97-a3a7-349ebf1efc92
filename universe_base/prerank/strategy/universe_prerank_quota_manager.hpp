#include "teams/ad/universe_base/prerank/strategy/universe_prerank_quota_manager.h"

#include <algorithm>
#include <map>
#include <utility>
#include <vector>

#include "absl/strings/str_cat.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"

#include "rapidjson/document.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/spdm_lib/src/context.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/prerank_base/params.h"
#include "teams/ad/ad_dynamic_compute/dcaf_solver/sdk/universe_dcaf_quality_info_entity.h"
#include "teams/ad/universe_base/prerank/utils/kconf/kconf.h"
#include "teams/ad/universe_base/prerank/utils/spdm/spdm_switches.h"

namespace ks {
namespace universe_base {
namespace prerank {

#define REGISTER_DCAF_EXP_BY_VERSION(version_id)                                       \
  do {                                                                                 \
    auto kconf_cb = []() { return AdKconfUtil::universe_dcaf_lambda_##version_id(); }; \
    init_exp_groups_[#version_id] = kconf_cb;                                          \
  } while (0)

// 注册一个新的实验版本需要两步：
// step1 在 teams/ad/ad_target_universe/utils/kconf/kconf.h 增加一个 kconf 定义，
//  格式 universe_dcaf_lambda_{version_id}
// step2 在 RegisterDcafExp 中添加新增的 kconf 定义： REGISTER_DCAF_EXP_BY_VERSION({version_id})
static std::unordered_map<std::string, ParamCb> RegisterDcafExp() {
  std::unordered_map<std::string, ParamCb> init_exp_groups_;
  REGISTER_DCAF_EXP_BY_VERSION(rank1);
  REGISTER_DCAF_EXP_BY_VERSION(rank2);
  REGISTER_DCAF_EXP_BY_VERSION(rank3);
  REGISTER_DCAF_EXP_BY_VERSION(rank4);
  REGISTER_DCAF_EXP_BY_VERSION(rank5);
  REGISTER_DCAF_EXP_BY_VERSION(rank6);
  REGISTER_DCAF_EXP_BY_VERSION(rank7);
  REGISTER_DCAF_EXP_BY_VERSION(rank8);
  REGISTER_DCAF_EXP_BY_VERSION(rank9);

  return init_exp_groups_;
}

#define REGISTER_DCAF_PRERANK_EXP_BY_VERSION(version_id)                                   \
  do {                                                                                     \
    auto kconf_cb = []() { return AdKconfUtil::universe_dcaf_lambda_exp_##version_id(); }; \
    init_exp_groups_prerank_[#version_id] = kconf_cb;                                      \
  } while (0)

// 注册一个新的实验版本需要两步：
// step1 在 teams/ad/ad_target_universe/utils/kconf/kconf.h 增加一个 kconf 定义，
//  格式 dcaf_lambda_exp_{version_id}
// step2 在 RegisterDcafExp 中添加新增的 kconf 定义： REGISTER_DCAF_PRERANK_EXP_BY_VERSION({version_id})
static std::unordered_map<std::string, ParamCb> RegisterPrerankDcafExp() {
  std::unordered_map<std::string, ParamCb> init_exp_groups_prerank_;
  REGISTER_DCAF_PRERANK_EXP_BY_VERSION(prerank1);
  REGISTER_DCAF_PRERANK_EXP_BY_VERSION(prerank2);
  REGISTER_DCAF_PRERANK_EXP_BY_VERSION(prerank3);
  REGISTER_DCAF_PRERANK_EXP_BY_VERSION(prerank4);
  REGISTER_DCAF_PRERANK_EXP_BY_VERSION(prerank5);
  REGISTER_DCAF_PRERANK_EXP_BY_VERSION(prerank6);
  REGISTER_DCAF_PRERANK_EXP_BY_VERSION(prerank7);
  REGISTER_DCAF_PRERANK_EXP_BY_VERSION(prerank8);
  REGISTER_DCAF_PRERANK_EXP_BY_VERSION(prerank9);

  return init_exp_groups_prerank_;
}

template <typename ContextData>
std::unordered_map<std::string, ParamCb> UniversePrerankQuotaManagerStrategy<ContextData>::exp_groups_(RegisterDcafExp());
template <typename ContextData>
std::unordered_map<std::string, ParamCb> UniversePrerankQuotaManagerStrategy<ContextData>::exp_groups_prerank_(
  RegisterPrerankDcafExp());

template <typename ContextData>
void UniversePrerankQuotaManagerStrategy<ContextData>::ProcessInner(ContextData *session_data) {
  if (session_data == nullptr) {
    return;
  }
  session_data_ = session_data;
  if (SPDM_enableDetailCpmSampleSkipDcaf()
      && session_data_->ad_request->target_request().enable_detail_cpm_kafka()) {
    // 如果是价值分样本流（包括老的价值分模型和新的 uplift 模型），就跳过动态算力
    session_data->dot->Count(1, "universe_detail_cpm_skip_dcaf_rank");
    return;
  }
  if (SPDM_enableDisableDcafWhenDegrade()) {
    auto degrade_conf = AdKconfUtil::universeElasticDegrade();
    auto iter = degrade_conf->find("max_prerank_num_ratio");
    if (iter != degrade_conf->end() && iter->second != 1.0) {
      // 当前处于降级状态，跳过动态算力
      session_data->dot->Count(1, "universe_degrade_skip_dcaf_rank");
      return;
    }
  }

  session_data_->universe_prerank_prepare_params.max_prerank_num_before_dcaf =
    session_data_->universe_prerank_prepare_params.max_prerank_num;
  auto rank_version_id = session_data_->universe_prerank_prepare_params.dcaf_rank_version_id;
  session_data_->dot->Interval(session_data_->universe_prerank_prepare_params.max_prerank_num_before_dcaf,
    "dcaf_max_prerank_num_original", rank_version_id);
  FillRankBaseLineNum(session_data_->universe_prerank_prepare_params.max_prerank_num);
  auto max_prerank_num = session_data_->universe_prerank_prepare_params.max_prerank_num;
  if (AdKconfUtil::enableUniverseRankDynamicQuotaByTimeout()) {
    session_data_->dot->Interval(max_prerank_num, "dcaf_max_prerank_num_before_timeout", rank_version_id);
    auto rpc_inner_time = (base::GetTimestamp() - session_data_->rpc_start_ts) / 1000.0;
    auto timeout = session_data_->ad_request->ad_user_info().universe_timeout();
    if (timeout > 0) {
      auto tp_ratio = SPDM_universe_dcaf_rank_timeout_tp_ratio(session_data_->spdm_ctx);
      // 当前阶段（主要是召回阶段）已花费的耗时 + 预计的 ad front，ad style 和 ad server 等的耗时
      auto other_extra_time = SPDM_universeDcafRankTimeoutExtraTime();
      if (SPDM_enable_dcaf_rank_extra_time_v2(session_data_->spdm_ctx) &&
          AdKconfUtil::universeDcafTimeoutExtraTime() != nullptr) {
        auto tmp_other_extra_time =
          AdKconfUtil::universeDcafTimeoutExtraTime()->data().GetRankExtratime(
            rank_version_id, timeout);
        if (tmp_other_extra_time > 0) {
          other_extra_time = tmp_other_extra_time;
        }
      }
      auto extra_time = static_cast<int32_t>(rpc_inner_time + other_extra_time);
      // 基于超时下调 quota
      auto new_prerank_num = AdKconfUtil::universeDcafTrafficRecord()->data().AdjustRankQuotaByTimeout(
        tp_ratio, timeout, max_prerank_num, extra_time);
      // 一些打点：主要是超时下调前后的超时预估和耗时预估
      auto dcaf_rank_time_old = AdKconfUtil::universeDcafTrafficRecord()->data().GetTimeCostByRankQuota(
        tp_ratio, max_prerank_num);
      auto is_timeout_old = timeout < extra_time + dcaf_rank_time_old ? 1 : 0;
      session_data_->dot->Interval(dcaf_rank_time_old,
        "dcaf_rank_time_old",
        rank_version_id, tp_ratio);
      session_data_->dot->Interval(is_timeout_old,
        "dcaf_rank_is_timeout_old",
        rank_version_id, tp_ratio);
      auto dcaf_rank_time_new = AdKconfUtil::universeDcafTrafficRecord()->data().GetTimeCostByRankQuota(
        tp_ratio, new_prerank_num);
      auto is_timeout_new = timeout < extra_time + dcaf_rank_time_new ? 1 : 0;
      session_data_->universe_prerank_prepare_params.dcaf_pred_time_rank = extra_time + dcaf_rank_time_new;
      session_data_->dot->Interval(dcaf_rank_time_new,
        "dcaf_rank_time_new",
        rank_version_id, tp_ratio);
      session_data_->dot->Interval(is_timeout_new,
        "dcaf_rank_is_timeout_new",
        rank_version_id, tp_ratio);
      LOG_EVERY_N(INFO, 10000) << "universe rank dynamic quota, prerank_num: " << max_prerank_num
        << ", timeout: " << timeout
        << ", tp_ratio: " << tp_ratio
        << ", rank_version_id: " << rank_version_id
        << ", extra_time: " << extra_time
        << ", rpc_inner_time: " << rpc_inner_time
        << ", dcaf_rank_time_old: " << dcaf_rank_time_old
        << ", dcaf_rank_time_new: " << dcaf_rank_time_new
        << ", is_timeout_old: " << is_timeout_old
        << ", is_timeout_new: " << is_timeout_new
        << ", new_prerank_num: " << new_prerank_num;
      session_data_->universe_prerank_prepare_params.max_prerank_num = new_prerank_num;
    }
    session_data_->dot->Interval(session_data_->universe_prerank_prepare_params.max_prerank_num,
      "dcaf_max_prerank_num_after_timeout",
      rank_version_id);
  }

  int32 union_ad_style =
          session_data_->pos_manager_base.request_imp_infos.empty() ? 0 :
                                     session_data_->pos_manager_base.request_imp_infos[0].ad_style;
  session_data_->dot->Interval(session_data_->universe_prerank_prepare_params.max_prerank_num,
                               "dcaf_max_prerank_num",
                               absl::StrCat(union_ad_style),
                               session_data_->universe_prerank_prepare_params.dcaf_rank_version_id);
}

template <typename ContextData>
int UniversePrerankQuotaManagerStrategy<ContextData>::GetMaxPrerankNum(ContextData *session_data) {
  const auto &request = *(session_data_->ad_request);
  int max_prerank_num{};
  engine_base::PreRankPrepareParams prerank_params(session_data_->IsUniverseRtbTraffic(),
                                                   session_data_->session_context);
  // 联盟弹性广告队列
  const auto &elastic_info = session_data->ad_request->universe_ad_request_info().elastic_info();
  if (elastic_info.max_prerank_num() <= 0) {  // 填充默认值
    max_prerank_num = prerank_params.target_select_cnt;
  } else {
    max_prerank_num = elastic_info.max_prerank_num();
  }
  return max_prerank_num;
}

// prerank 上移临时使用模版兼容，模版的实现通过编译 universe_preranking.cc 导出；
// 但该方法仅在 target 其他 node 中引用， universe_preranking.cc 无实际引用，会被优化掉
// 需要用 __attribute__((used)) 强制导出
template <typename ContextData>
__attribute__((used)) void UniversePrerankQuotaManagerStrategy<ContextData>::ProcessInnerPrerank(
    ContextData *session_data, int *max_retrieval_creatives) {
  // [xiaowentao] DCAF for prerank
  if (session_data == nullptr) {
    return;
  }
  session_data_ = session_data;
  if (SPDM_enableDetailCpmSampleSkipDcaf()
      && session_data_->ad_request->target_request().enable_detail_cpm_kafka()) {
    // 如果是价值分样本流（包括老的价值分模型和新的 uplift 模型），就跳过动态算力
    session_data->dot->Count(1, "universe_detail_cpm_skip_dcaf_prerank");
    return;
  }
  if (SPDM_enableDisableDcafWhenDegrade()) {
    auto degrade_conf = AdKconfUtil::universeElasticDegrade();
    auto iter = degrade_conf->find("max_retrieval_creatives_ratio");
    if (iter != degrade_conf->end() && iter->second != 1.0) {
      // 当前处于降级状态，跳过动态算力
      session_data->dot->Count(1, "universe_degrade_skip_dcaf_prerank");
      return;
    }
  }
  auto prerank_version_id =
    SPDM_universe_dcaf_prerank_version_id(session_data_->spdm_ctx);
  session_data_->dot->Interval(*max_retrieval_creatives,
    "dcaf_max_max_retrieval_creatives_before_dcaf",
    prerank_version_id);
  auto iter = exp_groups_prerank_.find(prerank_version_id);
  if (iter == exp_groups_prerank_.end()) {
    return;
  }
  auto max_prerank_num = session_data_->universe_prerank_prepare_params.max_prerank_num;
  session_data_->universe_prerank_prepare_params.max_retrieval_creatives_before_dcaf =
    *max_retrieval_creatives;
  ks::dynamic_compute::dcaf_solver::UniverseDcafQualityInfoEntity udqie(
    session_data_->ad_request->universe_quality_info(),
    max_prerank_num, *max_retrieval_creatives);
  if (!udqie.IsValid()) {
    return;
  }
  auto param = iter->second();
  auto it =
      param->data().rank_map().find(prerank_version_id);
  if (it == param->data().rank_map().end()) {
    return;
  }
  int64_t time_no_update = base::GetTimestamp() / 1000 / 1000 - it->second.update_ts_sec();
  if (time_no_update > param->data().max_minute_not_updated() * 60) {  // 长时间没有更新
    return;
  }
  if (it->second.lambda() <= 0) {
    return;
  }
  google::protobuf::RepeatedField<int64_t> quotas = it->second.quota_grades();
  *max_retrieval_creatives = udqie.CalcQuotaPrerank(it->second.lambda(), quotas);

  if (AdKconfUtil::enableUniversePrerankDynamicQuotaByTimeout()) {
    session_data_->dot->Interval(*max_retrieval_creatives,
      "dcaf_max_max_retrieval_creatives_before_timeout",
      prerank_version_id);
    auto rpc_inner_time = (base::GetTimestamp() - session_data_->rpc_start_ts) / 1000.0;
    auto timeout = session_data_->ad_request->ad_user_info().universe_timeout();
    if (timeout > 0) {
      auto tp_ratio = SPDM_universe_dcaf_rank_timeout_tp_ratio(session_data_->spdm_ctx);
      // 先调用一下精排 DCAF 获得 rank quota
      int32_t rank_quota = GetMaxPrerankNum(session_data);
      FillRankBaseLineNum(rank_quota);
      if (rank_quota <= 0) {
        session_data->dot->Count(1, "universe_invalid_rank_quota_when_dcaf_prerank_timeout");
        LOG_EVERY_N(INFO, 1000) << "universe_invalid_rank_quota_when_dcaf_prerank_timeout";
      }
      auto rank_time = AdKconfUtil::universeDcafTrafficRecord()->data().GetTimeCostByRankQuota(
        tp_ratio, rank_quota);
      if (rank_time > 0) {
        // 当前阶段（主要是在 target 中召回前的一些处理）已花费的耗时
        // + 预计的 ad front，ad style 和 ad server 等的耗时
        // + 预估的 rank 阶段耗时
        auto other_extra_time = SPDM_universeDcafPrerankTimeoutExtraTime();
        if (SPDM_enable_dcaf_prerank_extra_time_v2(session_data_->spdm_ctx) &&
            AdKconfUtil::universeDcafTimeoutExtraTime() != nullptr) {
          auto tmp_other_extra_time =
            AdKconfUtil::universeDcafTimeoutExtraTime()->data().GetPrerankExtratime(
              prerank_version_id, timeout);
          if (tmp_other_extra_time > 0) {
            other_extra_time = tmp_other_extra_time;
          }
        }
        auto extra_time = static_cast<int32_t>(
          rpc_inner_time + other_extra_time + rank_time);
        auto new_max_retrieval_creatives =
          AdKconfUtil::universeDcafTrafficRecordPrerank()->data().AdjustPrerankQuotaByTimeout(
            tp_ratio, timeout, *max_retrieval_creatives, extra_time);
        auto dcaf_retrieval_time_old =
          AdKconfUtil::universeDcafTrafficRecordPrerank()->data().GetTimeCostByPrerankQuota(
            tp_ratio, *max_retrieval_creatives);
        auto is_timeout_old = timeout < extra_time + dcaf_retrieval_time_old ? 1 : 0;
        session_data_->dot->Interval(dcaf_retrieval_time_old,
          "dcaf_retrieval_time_old",
          prerank_version_id, tp_ratio);
        session_data_->dot->Interval(is_timeout_old,
          "dcaf_retrieval_is_timeout_old",
          prerank_version_id, tp_ratio);
        auto dcaf_retrieval_time_new =
          AdKconfUtil::universeDcafTrafficRecordPrerank()->data().GetTimeCostByPrerankQuota(
            tp_ratio, new_max_retrieval_creatives);
        auto is_timeout_new = timeout < extra_time + dcaf_retrieval_time_new ? 1 : 0;
        session_data_->universe_prerank_prepare_params.dcaf_pred_time_prerank = extra_time + dcaf_retrieval_time_new;
        session_data_->dot->Interval(dcaf_retrieval_time_new,
          "dcaf_retrieval_time_new",
          prerank_version_id, tp_ratio);
        session_data_->dot->Interval(is_timeout_new,
          "dcaf_retrieval_is_timeout_new",
          prerank_version_id, tp_ratio);
        LOG_EVERY_N(INFO, 10000)
          << "universe prerank dynamic quota, max_retrieval_creatives: "
          << *max_retrieval_creatives
          << ", timeout: " << timeout
          << ", tp_ratio: " << tp_ratio
          << ", rank_quota: " << rank_quota
          << ", prerank_version_id: " << prerank_version_id
          << ", extra_time: " << extra_time
          << ", rpc_inner_time: " << rpc_inner_time
          << ", dcaf_retrieval_time_old: " << dcaf_retrieval_time_old
          << ", dcaf_retrieval_time_new: " << dcaf_retrieval_time_new
          << ", is_timeout_old: " << is_timeout_old
          << ", is_timeout_new: " << is_timeout_new
          << ", new_max_retrieval_creatives: " << new_max_retrieval_creatives;
        *max_retrieval_creatives = new_max_retrieval_creatives;
      } else {
        session_data->dot->Count(1, "universe_invalid_rank_time_when_dcaf_prerank_timeout");
        LOG_EVERY_N(INFO, 1000) << "universe_invalid_rank_time_when_dcaf_prerank_timeout";
      }
    }
    session_data_->dot->Interval(*max_retrieval_creatives,
      "dcaf_max_max_retrieval_creatives_after_timeout",
      prerank_version_id);
  }
  session_data_->universe_prerank_prepare_params.max_retrieval_creatives = *max_retrieval_creatives;

  LOG_EVERY_N(INFO, 10000) << "UniversePrerankQuotaManagerStrategy::ProcessInnerPrerank"
    << " prerank_version_id " << prerank_version_id
    << " lambda " << it->second.lambda()
    << " quotas.size " << quotas.size()
    << " old_max_retrieval_creatives "
    << session_data_->universe_prerank_prepare_params.max_retrieval_creatives_before_dcaf
    << " max_retrieval_creatives " << *max_retrieval_creatives;
  session_data_->dot->Interval(*max_retrieval_creatives,
    "dcaf_max_max_retrieval_creatives",
    prerank_version_id);
}

template <typename ContextData>
void UniversePrerankQuotaManagerStrategy<ContextData>::FillRankBaseLineNum(int32_t &max_prerank_num) {
  // 主路 quota 计算
  auto iter = exp_groups_.find(session_data_->universe_prerank_prepare_params.dcaf_rank_version_id);
  if (iter == exp_groups_.end()) {
    return;
  }

  ks::dynamic_compute::dcaf_solver::UniverseDcafQualityInfoEntity udqie(
    session_data_->ad_request->universe_quality_info(),
    max_prerank_num);

  if (!udqie.IsValid()) {
    return;
  }
  auto param = iter->second();
  auto it =
      param->data().rank_map().find(session_data_->universe_prerank_prepare_params.dcaf_rank_version_id);
  if (it == param->data().rank_map().end()) {
    return;
  }
  int64_t time_no_update = base::GetTimestamp() / 1000 / 1000 - it->second.update_ts_sec();
  if (time_no_update > param->data().max_minute_not_updated() * 60) {  // 长时间没有更新
    return;
  }
  if (it->second.lambda() <= 0) {
    return;
  }
  if (SPDM_enable_universe_smart_compute_dynamic_quota(session_data_->spdm_ctx)) {
    google::protobuf::RepeatedField<int64_t> quotas = it->second.quota_grades();
    max_prerank_num = udqie.CalcQuota(it->second.lambda(), quotas);
  }
}

}  // namespace prerank
}  // namespace universe_base
}  // namespace ks
