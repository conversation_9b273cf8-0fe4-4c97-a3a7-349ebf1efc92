#include "teams/ad/ad_rank_universe/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"
namespace ks {
namespace ad_rank {

// 定义 kconf 开关:  SPDM_KCONF_BOOL(path, key);
// 定义 abtest 开关: SPDM_ABTEST_BOOL(key, ks::AbtestBiz);

// 以下为 kconf 开关定义.
SPDM_KCONF_BOOL(ad.adRank, enableRewardedConversionCalibrate);
SPDM_KCONF_BOOL(ad.adRank, disableDiversityExploreHc);
SPDM_KCONF_BOOL(ad.adRank, enableNodeTimeCost);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableCmdKeyRecord);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUniverseAdRouterExp);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillCxrOpSplit);
SPDM_KCONF_BOOL(ad.adRank, enablePostSeparateDataProcCb);
SPDM_KCONF_BOOL(ad.adRank, enableUniverseEcpcUnifyFillInfos);
SPDM_KCONF_BOOL(ad.adRank, enableFansFollow);
SPDM_KCONF_BOOL(ad.adRank, enableUniverseBillingPidService);
SPDM_KCONF_BOOL(ad.adRank, enableUniverseEcpcTargetConfig);
SPDM_KCONF_BOOL(ad.adRank, enableUniverseColdStartOvercostLimit);
SPDM_KCONF_BOOL(ad.adRank, enablePredictScoreCheck);
SPDM_KCONF_BOOL(ad.adRank, enablePredictScoreStandby);
SPDM_KCONF_BOOL(ad.adRank, enableUniverseInnerEcpc);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseEcpcAdjustDim);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseEcpcActiveTimeReverse);
SPDM_KCONF_BOOL(ad.adRank, universeInnerRecordNew);
SPDM_KCONF_BOOL(ad.adRank2, enableParseDeliveredQponInfo);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseQcpxVersionControl);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseTransparentQcpxRank);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseInnerQcpxFilterReason);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseColdStartField);
SPDM_KCONF_BOOL(ad.adRank2, enablePhotoFirstIndexTimeUniverse);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseRankMediumUidFix);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseAdCouponTemplate);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseSetQcpxFeature);
SPDM_KCONF_BOOL(ad.adRank, enableFixUniverseLtvEndType);
SPDM_KCONF_BOOL(ad.adRank2, enableUniverseUCxr);
SPDM_KCONF_BOOL(ad.adRank2, enableIaaLtvRatio);
SPDM_KCONF_BOOL(ad.adRank2, enableInvokePurchaseRatio);
SPDM_KCONF_BOOL(ad.adRank2, enableConversionPurchaseRatio);
SPDM_KCONF_BOOL(ad.adRank3, enableUniverseQcpxPerf);
SPDM_KCONF_BOOL(ad.adRank2, printHighPrecisionAuctionBid);
SPDM_KCONF_BOOL(ad.adRank3, enableCorporationNameUniverse);
SPDM_KCONF_BOOL(ad.adRank2, disableEcpcKeyActionNextStayPidRatio);
SPDM_KCONF_BOOL(ad.adRank2, enableBoostRatioSuppress);

SPDM_ABTEST_BOOL(enable_universe_rank_fix_medium_uid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_direct_live_audience_split, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_direct_live_audience_split_additional, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_closure_support_mode, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_rank_cmds_feature, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_dcvr_common_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_ltv_common_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tiny_close_inner_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_offline_historical_cali, ks::AbtestBiz::AD_DSP);
// ------------------------------ kconf int32 参数定义 ---------------------------------
SPDM_KCONF_INT32(ad.adRank, universeLogInfoFreq, 100);
SPDM_KCONF_INT32(ad.adRank, perfNodeCostLatency, 1000);
SPDM_KCONF_INT32(ad.adRank, universeUserFirstExposureUpperCpm, 100);
SPDM_KCONF_INT32(ad.adRank, universeOuterExploreUpperBound, 3);

// ------------------------------ kconf int64 参数定义 ---------------------------------

// ------------------------------ kconf double 参数定义 ---------------------------------
// ------------------------------ kconf string 参数定义 ---------------------------------

// 以下为 abtest 开关定义.
SPDM_ABTEST_BOOL(enable_ad_bid_server_group_tag_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_bid_server_group_tag_fix2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univere_interest_socre_opt_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_conversion_week_nextstay, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enbale_universe_ecpc_crod_package_interest_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ios_invoke_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_tiny_skip_model_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_shortvideo_cvrcorrect_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_live_cali_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_explore_strategy_offline, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_rank_ecom_spu_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_spu_explore_center, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_small_app_direct_jump, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_esp_mobile_live_to_hard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(esp_mobile_live_to_hard_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(inner_normal_live_size, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(inner_native_live_pc_quota, ks::AbtestBiz::AD_DSP, 25);
SPDM_ABTEST_STRING(rank_dragon_update_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_universe_ecpc_raw_cpm_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_skip_deep_drop, ks::AbtestBiz::AD_DSP);
// [yangjinhui]
SPDM_ABTEST_BOOL(enable_universe_adx_bid_double_v2, ks::AbtestBiz::AD_DSP);  // universe adx bid double
SPDM_ABTEST_BOOL(enable_universe_quality_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_industry_crowd_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_out_retarget_ratio_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_out_retarget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ks_search_ecom_crowd_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_leads_submit_ecpc_threshold, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(universe_private_message_ecpc_prefix, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(universe_private_message_ecpc_threshold, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_universe_main_pcvr_perturb_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(enable_universe_main_pcvr_perturb_ecpc_expkey, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_universe_main_pcvr_perturb_ecpc_ocpx, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_main_pcvr_perturb_ecpc_industry_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(enable_universe_main_pcvr_perturb_ecpc_industry_switch_key, ks::AbtestBiz::AD_DSP, 0);

// [liuxiaofan05]
SPDM_ABTEST_BOOL(enable_context_info_completion, ks::AbtestBiz::AD_DSP);

// [huangwenbin]
SPDM_ABTEST_BOOL(enable_universe_tiny_search_package_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_tiny_search_rank_explore_by_index, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_jump_product_name, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_search_loss_data_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_manufacturer_high_ctr_pos_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outer_explore_cost_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_lc_request_cpm_explore_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_lc_request_cpm_more_explore_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outer_universe_high_cpm_explore_ratio_up_bound, ks::AbtestBiz::AD_DSP, 50.0);
SPDM_ABTEST_DOUBLE(universe_inner_explore_avg_ratio_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_center_ocpx_cost_ratio_target_value, ks::AbtestBiz::AD_DSP, 1.5);
SPDM_ABTEST_DOUBLE(explore_center_ocpx_coef_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_center_ocpx_coef_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_search_loss_data_cali_upper_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_search_loss_data_cali_boost_threshold, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(outer_universe_high_cpm_explore_ocpx, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_inner_explore_center_ab_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_inner_boost_kconf_ab_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_search_loss_data_algorithm, ks::AbtestBiz::AD_DSP, "AVG");
SPDM_ABTEST_STRING(enable_search_skip_deep_drop_query_type_enmu, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(enable_search_loss_data_cali_query_type_enmu, ks::AbtestBiz::AD_DSP, "10");
SPDM_ABTEST_STRING(manufacturer_high_ctr_pos_explore_ab_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_tiny_search_rank_explore_by_index_abtag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_INT64(ks_user_group_effect_switch, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(outer_universe_high_cpm_explore_phone_price_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(universe_search_loss_data_cali_upper_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(universe_search_loss_data_cali_quantile, ks::AbtestBiz::AD_DSP, 0);

// [rentianci]
SPDM_ABTEST_BOOL(enable_universe_search_relevance_tool_for_mcb, ks::AbtestBiz::AD_DSP);

// [wuhan12]
SPDM_ABTEST_BOOL(enable_universe_inner_high_value_crowd_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_inner_high_value_crowd_explore_algorithm, ks::AbtestBiz::AD_DSP, "UCBV1");
SPDM_ABTEST_STRING(universe_inner_high_value_crowd_rank_tag, ks::AbtestBiz::AD_DSP, "exp");
SPDM_ABTEST_BOOL(enable_universe_inner_high_value_crowd_top_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_inner_high_value_crowd_top_explore_algorithm, ks::AbtestBiz::AD_DSP, "UCBV1");
SPDM_ABTEST_STRING(universe_inner_high_value_crowd_top_explore_tag, ks::AbtestBiz::AD_DSP, "exp");

// QCPX
SPDM_ABTEST_BOOL(enable_universe_qcpx_coupon_holdout, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_qcpx_freq_control_delivery_max_cnt_1d, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(universe_qcpx_rct_amount_control_flow_percent, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(universe_qcpx_rct_amount_treatment_flow_percent, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(universe_qcpx_model_flow_percent, ks::AbtestBiz::AD_DSP, 70);
SPDM_ABTEST_DOUBLE(universe_qcpx_roi_thres_ratio, ks::AbtestBiz::AD_DSP, 5.5);
SPDM_ABTEST_BOOL(enable_universe_qcpx_admit_u0_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_qcpx_skip_delivery_freq_control, ks::AbtestBiz::AD_DSP);
// 直播
SPDM_ABTEST_BOOL(enable_universe_qcpx_live_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_qcpx_live_specific_amount_coupon_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_qcpx_live_specific_amount_coupon_id, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(universe_qcpx_live_price_coupon_ratio, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_INT64(universe_qcpx_live_min_coupon_amount_yuan, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(universe_qcpx_live_max_coupon_amount_yuan, ks::AbtestBiz::AD_DSP, 30);
SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_cvr_ratio_bound, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_INT64(universe_qcpx_live_half_discount_max_amount, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 20.0);
SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(universe_qcpx_live_order_uplift_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(universe_qcpx_live_cvr_ratio_new_ocpx, ks::AbtestBiz::AD_DSP, 1.3);
SPDM_ABTEST_BOOL(enable_universe_qcpx_live_cvr_spline_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_spline_upper_bound, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_spline_new_upper_bound, ks::AbtestBiz::AD_DSP, 5.0);
// 短视频
SPDM_ABTEST_BOOL(enable_universe_qcpx_photo_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_qcpx_photo_specific_amount_coupon_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_qcpx_photo_specific_amount_coupon_id, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(universe_qcpx_photo_price_coupon_ratio, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(universe_qcpx_photo_min_coupon_amount_yuan, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(universe_qcpx_photo_max_coupon_amount_yuan, ks::AbtestBiz::AD_DSP, 30);
SPDM_ABTEST_INT64(universe_qcpx_photo_history_paid_count_thres, ks::AbtestBiz::AD_DSP, 150);
SPDM_ABTEST_DOUBLE(universe_qcpx_photo_gmv_greater_min_price_ratio_thres, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(universe_qcpx_photo_mode_gmv_ratio_thres, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(universe_qcpx_photo_min_price_higher_avg_gmv_ratio_thres, ks::AbtestBiz::AD_DSP, 0.25);
SPDM_ABTEST_DOUBLE(universe_qcpx_photo_uplift_cvr_ratio_bound, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_INT64(universe_qcpx_photo_half_discount_max_amount, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_DOUBLE(universe_qcpx_photo_uplift_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 20.0);
SPDM_ABTEST_DOUBLE(universe_qcpx_photo_uplift_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(universe_qcpx_photo_cvr_ratio_new_ocpx, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(universe_qcpx_photo_roi_thres_ratio, ks::AbtestBiz::AD_DSP, 4.0);

// RankingUniverseIaaProductBonus 插件参数
// RankingUniverseUpModelStrategy 插件参数
SPDM_ABTEST_BOOL(enable_calibration_merchant_order_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_mcda_conve_double, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_small_scale_mcda_roi_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_up_ecpc_pos_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_ecommerce_category_tag_rank_bandit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_bonuscpm_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_bonuscpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_pid_service, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_unverse_billing_service, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_unverse_ecpc_pid_service, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_roas_low_roi_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_tc_cem_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_tc_cem_live_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_new_tc_condition, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_app_author_CEM, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_all_deep_explore_pos_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_all_deep_v2_cb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_all_deep_v2_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_all_deep_v2_inner_first_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_fix_mcb_random_charge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_all_deep_explore_pos_out, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_audience_tag_rank_bandit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_app_author_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_fans_app_author_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_video_fans_app_author_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_lc_pos_account_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_lc_dis_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_lc_uni_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_lc_request_cpm_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_lc_mab_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_lc_mab_request_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_lc_mab_only_cpm_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_sink_indstyle_account_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_sink_indstyle_account_spu_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_live_play_time_explore_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_inner_retrieval_rank_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_innerdeep_offline_boost_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_innerdeep_offline_ecpc_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_innerdeep_offline_explore_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_offline_boost_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_offline_ecpc_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_offline_explore_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_universe_sink_explore_nobid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_center_ocpx_dynamic_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_cost_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_explore_center_media_fuse, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_center_dynamic_weight_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_redirect_indstyle_category_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_game_roas_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_inner_explore_center_sigma_threshold, ks::AbtestBiz::AD_DSP, 0.01);
SPDM_ABTEST_DOUBLE(universe_inner_explore_center_cpm_threshold, ks::AbtestBiz::AD_DSP, 1000.0);
SPDM_ABTEST_DOUBLE(universe_inner_explore_center_mu_threshold, ks::AbtestBiz::AD_DSP, 1000.0);
SPDM_ABTEST_DOUBLE(universe_inner_explore_center_ratio_upbound, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_BOOL(disable_universe_redirect_single_explore_nobid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_rank_app_dup_package, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_distribution_pos_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_distribution_strategy_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_distribution_pos_leaderboard_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_distribution_pos_leaderboard_in_explore_state, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_lc_distribution_cost_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_distribution_pos_message_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_search_ecpc_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_universe_search_ecpc_explore_on_purchase, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_search_ecpc_explore_cost_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_appstyle_product_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_indstyle_explore_cem_user, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_indusytle_cem_alg, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_indusytle_cem_random_once, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_crow_keywords_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_crow_active_time, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_crow_active_time_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_crow_app_active_time, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_active_time_app_list_upbound, ks::AbtestBiz::AD_DSP, 300);
SPDM_ABTEST_DOUBLE(universe_active_time_beta, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_active_time_alpha, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_active_time_gamma, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_active_time_upper, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(universe_active_time_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_universe_crow_keyword_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_intersert_score_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_calc_rewarded_richenss_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_play_time_explore_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_bid_tool_skip_compensation, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_cost_boost_explore_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_billing_separate_rewarded_video, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_high_quality_crowd_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_conv_7stay, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_merchant_video_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_fake_user_explore_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_fake_user_explore_strategy_expand, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_loop_multi_purchase_user_reco, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_loop_multi_purchase_user_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_noboost_unit_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_lc_dis_drop_ratio_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_live_ads_bid_adjust_ratio_hard_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_live_reserve_bid_adjust_ratio_hard_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_live_follow_bid_adjust_ratio_hard_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_manufacturer_search_hq_traffic_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_dark_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_dark_strategy_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_shortvideo_dark_unify_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_shortvideo_dark_explore_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_dark_explore_aim_target_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_retrieval_rank_boost_one_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_inner_video_pay_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_inner_p2l_roas_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_inner_live_roas_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_inner_p2l_roas_cali_formula, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_inner_live_roas_cali_formula, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_shortvideo_roi_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_purchase_dcvr_beta, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_calibrate_inner_account_admit_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_calibrate_inner_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_roi_calibration_ir, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_ad_roas_calibration_ir, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(hybrid_auction_billing_ratio_max_native, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(hybrid_auction_billing_ratio_min_native, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(hybrid_auction_billing_ratio_max, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(hybrid_auction_billing_ratio_min, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_BOOL(enable_universe_shortvideo_roi_cvrmodel_bottom, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_calibrate_account, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_seven_days_roi_app, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_spu_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_quality_strategy_exp, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(disable_universe_shortvideo_order_posexplore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_xdt_roas_posexplore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cpm_protect, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cpm_protect_indstyle, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cpm_protect_product, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_account_indstyle, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_shortvideo_industry_sytle_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_sytle_boost_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_video_multi_retr_tag_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enbale_universe_ecpc_crod_package_active_app, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enbale_universe_ecpc_crod_package_fl_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_ecpc_crowd_package_up_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_ecommerce_interest_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_increase_bid_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_increase_bid_ad_outer_only, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bs_cover_all_industry_live_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_white_product_skip_account_bidding, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_second_predict_use_new_paid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pos_control_poquan_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_poquan_top_limit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_flow_poquan_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_game_ios_cheap_flow_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_game_ios_cheap_flow_explore_filter_mini, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_compete_fail_info_llm_explore, ks::AbtestBiz::AD_DSP);
// ------------------------------ abtest integer 参数定义 ---------------------------------
SPDM_ABTEST_INT64(universe_ocpx_sample_size, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(t0_fans_cnt, ks::AbtestBiz::AD_DSP, 10000);
SPDM_ABTEST_INT64(t1_fans_cnt, ks::AbtestBiz::AD_DSP, 100000);
SPDM_ABTEST_INT64(inner_explore_cost_control_u_level, ks::AbtestBiz::AD_DSP, 0);
// ------------------------------ abtest double 参数定义 ----------------------------------
SPDM_ABTEST_DOUBLE(live_follow_fast_model_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_follow_model_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(live_follow_model_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(live_follow_model_upper, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(no_bid_live_universe_ltv_threld, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(no_bid_merchant_universe_ltv_threld, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(no_bid_live_universe_paied_threld, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(no_bid_merchant_universe_paied_threld, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(universe_inner_boost_uppper_bound_v2, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(enable_universe_game_shoufa_ecpc, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(universe_common_cali_upper_bound, ks::AbtestBiz::AD_DSP, 1.5);
SPDM_ABTEST_DOUBLE(universe_common_cali_lower_bound, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(universe_common_cali_ltv_upper_bound, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_DOUBLE(universe_common_cali_ltv_lower_bound, ks::AbtestBiz::AD_DSP, 0.2);
SPDM_ABTEST_DOUBLE(inner_explore_cost_control_u_level_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_explore_cost_control_mobile_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_compete_fail_info_llm_explore_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_item_card_boost_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tiny_request_pid_service, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(no_bid_merchant_bid_rate, ks::AbtestBiz::AD_DSP, 0.2);

SPDM_ABTEST_BOOL(enable_long_ratio_seven_day_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(upper_origin_pay_amount_7d, ks::AbtestBiz::AD_DSP, 2000.0);
// 长线纠偏系数
SPDM_ABTEST_BOOL(enable_seven_day_longratio_cali_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(seven_day_longratio_cali_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_ad_roas_longratio_cali_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ad_roas_longratio_cali_ratio, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_DOUBLE(upper_r_long_ratio_7d_repurchase_param, ks::AbtestBiz::AD_DSP, 30.0);

// ------------------------------ abtest string 参数定义 ----------------------------------
SPDM_ABTEST_STRING(fanstop_inner_region_org_exp_types, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(universe_retrieval_rank_boost_config, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_video_multi_retr_tag_first_up_config, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_multi_retr_tag_escape_cvr_filter_config, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(uax_explore_strategy_names, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_game_score_crowd_id_list, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(poquan_explore_range_raito, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_flow_poquan_explore_exp_name, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_calibrate_key, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(inner_explore_cost_control_media_ab_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_lc_dis_drop_kconf_ratio_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(enable_universe_game_ios_cheap_flow_explore_exp_key,
                    ks::AbtestBiz::AD_DSP, "");

// ---------------------- 内粉专用 参数声明区域（防 git 冲突） -------------------
// ---------------------- abtest bool 参数声明 -------------------
// [nanning]
SPDM_ABTEST_BOOL(enable_universe_nobid_merchant, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_rank_high_priority_quota, ks::AbtestBiz::AD_DSP);
// ---------------------- abtest int64 参数声明 -------------------
SPDM_ABTEST_INT64(fanstop_rank_high_priority_photo_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(universe_flow_poquan_explore_exp_ver, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(universe_imp_poquan_explore_exp_ver, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(fanstop_rank_high_priority_live_quota, ks::AbtestBiz::AD_DSP, -1);
// ---------------------- abtest double 参数声明 -------------------
// ---------------------- abtest string 参数声明 -------------------

// ---------------------- kconf bool 参数声明 -------------------

// ---------------------- kconf int 参数声明 -------------------
// ---------------------- kconf double 参数声明 -------------------

// ---------------------- kconf string 参数声明 -------------------

SPDM_ABTEST_BOOL(enable_sctr_into_cpm_follow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sctr_into_cpm_guess_you_like, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_outer_u_ctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_new_ad_rank_infos, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_outer_u_ctcvr_split_20min_in_out, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_outer_u_ctcvr_split_20min_in_out_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_photo_ue_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(photo_cpm_ue_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(universe_outer_ucvr_bound_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_outer_active_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_initial_release_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_retarget_force_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(initial_release_base_prob, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(initial_release_prob_max, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(initial_release_prob_min, ks::AbtestBiz::AD_DSP, 0.5);

SPDM_ABTEST_BOOL(enable_corporation_ceiling_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_corporation_ceiling_rank_force_tag, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_item_card_merchant_product_id_hard_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_merchant_product_id_soft_jichen, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_item_card_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_jichen_add_ocpc_action_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_soft_queue_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_spu_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_author_spu_jichen, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_BOOL(enable_nextstay_white_list_tiny, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_ltv_expand_upper, ks::AbtestBiz::AD_DSP, 200.0);

SPDM_ABTEST_BOOL(enable_fiction_conv2_purchase_exp_new, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_user_scene_req_freq_boost, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_req_freq_boost_op_config, ks::AbtestBiz::AD_DSP);

// zhangsongyu
SPDM_ABTEST_BOOL(enable_universe_ban_low_cpm_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_high_cpm_model_fusion, ks::AbtestBiz::AD_DSP);
// zhangtie
SPDM_ABTEST_BOOL(enable_universe_pos_action_feas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_leads_submit_conflict, ks::AbtestBiz::AD_DSP);

// lijingtao03
SPDM_ABTEST_BOOL(enable_universe_inner_cid_cvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_cid_gmv, ks::AbtestBiz::AD_DSP);

// suzhentao
SPDM_ABTEST_BOOL(enable_universe_playlet_iaa_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_playlet_iaa_cvr, ks::AbtestBiz::AD_DSP);
}  // namespace ad_rank
}  // namespace ks
