#pragma once

#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace ad_rank {
// SPDM 使用文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIDGfeGPH2sQAxiBwuiaJ4
// 声明 kconf 开关:  DECLARE_SPDM_KCONF_BOOL(key);  // [owner] 开关用途/作用
// 声明 abtest 开关: DECLARE_SPDM_ABTEST_BOOL(key);  // [owner] 开关用途/作用

// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.


// 以下为 kconf 开关声明.
DECLARE_SPDM_KCONF_BOOL(enableFansFollow);  // [zhoushuaiyin] 使用上游透传的关注数据
DECLARE_SPDM_KCONF_BOOL(enableNodeTimeCost);
DECLARE_SPDM_KCONF_BOOL(enableCmdKeyRecord);
DECLARE_SPDM_KCONF_BOOL(enablePostSeparateDataProcCb);  // [weiyilong] Post 分离 DataProc 回调
DECLARE_SPDM_KCONF_BOOL(enableUniverseEcpcUnifyFillInfos);  // [zhoumingsong] 联盟 ecpc 填充落数据流相应字段
DECLARE_SPDM_KCONF_BOOL(enableUniverseEcpcTargetConfig);  // [zhoumingsong] 联盟 ecpc 策略配置匹配开关
DECLARE_SPDM_KCONF_BOOL(enableUniverseColdStartOvercostLimit);  // [hepan] 联盟 不顶价 开关
DECLARE_SPDM_KCONF_BOOL(enablePredictScoreCheck);  // [zhoushuaiyin] 预估值兜底检查开关
DECLARE_SPDM_KCONF_BOOL(enablePredictScoreStandby);  // [zhoushuaiyin] 预估值兜底替换开关
DECLARE_SPDM_KCONF_BOOL(enableUniverseBillingPidService);
DECLARE_SPDM_KCONF_BOOL(enableFillCxrOpSplit);  // FillCxr 算子拆分
DECLARE_SPDM_KCONF_BOOL(enableUniverseEcpcAdjustDim);  // [zengzhengda] 联盟 ecpc 调整维度
DECLARE_SPDM_KCONF_BOOL(enableUniverseEcpcActiveTimeReverse);  // [liangshuang] 联盟 ecpc 活跃时长逆转 ratio
DECLARE_SPDM_KCONF_BOOL(universeInnerRecordNew);  // [wanglei10] 联盟内循环新打点
DECLARE_SPDM_KCONF_BOOL(enableParseDeliveredQponInfo);  // [tanghaihong] 联盟解析 qcpx 历史下发信息
DECLARE_SPDM_KCONF_BOOL(enableUniverseQcpxVersionControl);  // [tanghaihong] 联盟 qcpx 版控
DECLARE_SPDM_KCONF_BOOL(enableUniverseTransparentQcpxRank);  // [tanghaihong] 联盟 rank 透传 qcpx 字段
DECLARE_SPDM_KCONF_BOOL(enableUniverseInnerQcpxFilterReason);  // [tanghaihong] 联盟 rank 透传 qcpx 过滤原因  // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseAdRouterExp);     // [liuyibo05] 精排模型直联实验
DECLARE_SPDM_KCONF_BOOL(enableUniverseColdStartField);  // [tanghaihong] 联盟下发新创意冷启字段
DECLARE_SPDM_KCONF_BOOL(enablePhotoFirstIndexTimeUniverse);  // [tanghaihong] photo 首次进联盟索引时间
DECLARE_SPDM_KCONF_BOOL(enableUniverseRankMediumUidFix);  // [tanghaihong] 修复顶价策略 medium_uid 字段
DECLARE_SPDM_ABTEST_BOOL(enable_universe_rank_fix_medium_uid);  // [tanghaihong] 修复 context medium_uid 字段
DECLARE_SPDM_ABTEST_BOOL(enable_closure_support_mode);   // [liuyibo05] 联盟支持闭环电商转化链路
DECLARE_SPDM_KCONF_BOOL(enableUniverseAdCouponTemplate);  // [tanghaihong] 联盟接入 ad_coupon_template 策略正排  // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseSetQcpxFeature);  // [tanghaihong] 联盟设置 qcpx 策略正排相关字段
DECLARE_SPDM_KCONF_BOOL(enableFixUniverseLtvEndType);  // [tanghaihong] 联盟修复 tracelog ltv end_type 落表问题  // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseUCxr);  // [zhangcong05] 联盟下发 cxr 字段
DECLARE_SPDM_KCONF_BOOL(enableIaaLtvRatio);  // [dengtiancong] 联盟 Iaa Ltv 开关
DECLARE_SPDM_KCONF_BOOL(enableInvokePurchaseRatio);  // [dengtiancong] 联盟唤端付费开关
DECLARE_SPDM_KCONF_BOOL(enableConversionPurchaseRatio);  // [dengtiancong] 联盟激活付费开关
DECLARE_SPDM_KCONF_BOOL(enableUniverseQcpxPerf);  // [yangzhanwu] 联盟 qcpx 打点开关
DECLARE_SPDM_KCONF_BOOL(printHighPrecisionAuctionBid);   // [huoyan03] 高精度 auction_bid 打点
DECLARE_SPDM_KCONF_BOOL(enableCorporationNameUniverse);   // [xiaowentao] 透传营业执照到 front
DECLARE_SPDM_KCONF_BOOL(disableEcpcKeyActionNextStayPidRatio);  // [xuxuejian] 联盟关闭关键行为 ecpc pid_ratio  // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableBoostRatioSuppress);  // [dingjinren] 用户 APP 活跃时长打压系数开关  // NOLINT
// ---------------------- kconf bool 参数声明 -------------------
// ---------------------- kconf int32 参数声明 -------------------
DECLARE_SPDM_KCONF_INT32(universeLogInfoFreq);  // [zhangyunhao03] 联盟日志打印频率
DECLARE_SPDM_KCONF_INT32(perfNodeCostLatency);  //  [zhangguanglei] perf 打点记录长尾 node 耗时
DECLARE_SPDM_KCONF_INT32(universeUserFirstExposureUpperCpm);  // [xiaowentao] 联盟首曝策略 cpm 上界
DECLARE_SPDM_KCONF_INT32(universeOuterExploreUpperBound)  // [dingjinren] 探索中心化外循环上限

// ---------------------- kconf int64 参数声明 -------------------
// ---------------------- kconf double 参数声明 -------------------
// ---------------------- kconf string 参数声明 -------------------

// 以下为 abtest 开关声明.
DECLARE_SPDM_ABTEST_BOOL(enable_universe_skip_deep_drop);  //  [liangli]  小系统跳过次留 drop

DECLARE_SPDM_ABTEST_BOOL(enable_close_rank_cmds_feature);   // [liuyibo05] 关闭 RANK_CMDS 特征实验
DECLARE_SPDM_ABTEST_BOOL(enable_tiny_close_inner_cmd);  // [liuyibo] 小系统关闭内循环 cmd
DECLARE_SPDM_ABTEST_BOOL(enable_universe_direct_live_audience_split);   // [liuyibo05]
DECLARE_SPDM_ABTEST_BOOL(enable_universe_direct_live_audience_split_additional);   // [lijingtao03]

DECLARE_SPDM_KCONF_BOOL(enableUniverseInnerEcpc);  //  [zhoumingsong] 联盟内循环 ECPC 开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_dcvr_common_cali);  // [haozhitong] 联盟统一深度预估值校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ltv_common_cali);  // [haozhitong] 联盟统一 LTV 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_offline_historical_cali);  // [haozhitong] 下线联盟历史校准逻辑实验

// [zengzhengda] 联盟人群标签精排 bandit 探索开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_audience_tag_rank_bandit);
// [zengzhengda] 联盟意向人群 ecpc 实验开关
DECLARE_SPDM_ABTEST_BOOL(enbale_universe_ecpc_crod_package_interest_score);
// [zengzhengda] 联盟排序计费分离打平系数接入 V3
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_skip_model_opt);  //  [zhangyunfei03] 联盟小系统跳过模型
DECLARE_SPDM_ABTEST_BOOL(enable_universe_pid_service);    //  [weizihyong03] rank 请求 pid_service
DECLARE_SPDM_ABTEST_BOOL(enable_unverse_billing_service);    //  [weizihyong03] 联盟计费分离迁移
DECLARE_SPDM_ABTEST_BOOL(enable_unverse_ecpc_pid_service);    //  [weizihyong03] 联盟 ecpc 迁移
//  [zengzhengda] 联盟七留 ecpc 策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_conversion_week_nextstay);
// [zengzhengda] 联盟活跃数据精排 ecpc 实验开关
DECLARE_SPDM_ABTEST_BOOL(enbale_universe_ecpc_crod_package_active_app);
// [zengzhengda] 联盟联邦建模 ecpc 应用实验开关
DECLARE_SPDM_ABTEST_BOOL(enbale_universe_ecpc_crod_package_fl_model);
// [zhoumingsong] 联盟内循环人群包 ecpc 实验流量开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_ecpc_crowd_package_up_price);
DECLARE_SPDM_ABTEST_STRING(outer_universe_high_cpm_explore_ocpx);  // [huangwenbin] 外联盟 CPM 高段位探索 ocpx //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(outer_universe_high_cpm_explore_ratio_up_bound);  // [huangwenbin] 外联盟 CPM 高段位探索 探索系数上界控制 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(outer_explore_cost_ratio); // [xiemiao] 外联盟媒体 CPM 高段位控成本开关 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_lc_request_cpm_explore_ratio); // [linglong] 厂商竞胜探索 CPM 门槛系数 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_lc_request_cpm_more_explore_ratio); // [linglong] 厂商竞胜探索 CPM 门槛进一步探索系数 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_jump_product_name);  // [xiemiao] 外联盟媒体 CPM 高段位探索跳过产品名实验开关 //NOLINT
DECLARE_SPDM_ABTEST_INT64(ks_user_group_effect_switch);  // [huangwenbin] 外联盟媒体 CPM 高段位探索 快手或非快人群切换开关 //NOLINT
DECLARE_SPDM_ABTEST_INT64(outer_universe_high_cpm_explore_phone_price_threshold);  // [huangwenbin] 外联盟媒体 CPM 高段位探索 手机价格门槛 //NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_inner_explore_center_ab_tag);  // [huangwenbin] 探索中心化实验 AB 标识 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_explore_avg_ratio_v2); // [huangwenbin] 内循环探索系数均值控制系数 V2 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(explore_center_ocpx_cost_ratio_target_value); // [huangwenbin] 内循环成本率业务基准线 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(explore_center_ocpx_coef_lower_bound); // [huangwenbin] 内循环探索中心化 动态调控下限 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(explore_center_ocpx_coef_upper_bound); // [huangwenbin] 内循环探索中心化 动态调控上限 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_live_play_time_explore_v2);  // [huangwenbin] 内循环探索直播开播时间控制开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_inner_retrieval_rank_boost);  // [huangwenbin] 内循环召回通路扶持控制开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_innerdeep_offline_boost_ratio);  // [huangwenbin] 内循环深度 boost 框架系数控制开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_innerdeep_offline_ecpc_ratio);  // [huangwenbin] 内循环深度 ecpc 框架系数控制开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_innerdeep_offline_explore_ratio);  // [huangwenbin] 内循环深度 explore 框架系数控制开关 //NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_inner_boost_kconf_ab_tag);  // [huangwenbin] 联盟内循环深度 boost 框架生效白名单 AB 标识 //NOLINT

// [huangwenbin] 联盟厂商相关开关
DECLARE_SPDM_ABTEST_STRING(enable_search_skip_deep_drop_query_type_enmu);  // [huangwenbin] 联盟厂商搜索多样性跳过深度预估 枚举值 //NOLINT
DECLARE_SPDM_ABTEST_STRING(enable_search_loss_data_cali_query_type_enmu);  // [huangwenbin] 联盟厂商搜索竞败数据利用 枚举值 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_search_loss_data_cali);  // [huangwenbin] 联盟厂商搜索竞败数据利用控制开关 //NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_search_loss_data_cali_upper_threshold);  // [huangwenbin] 联盟厂商搜索竞败数据利用 CPM 上界 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_search_loss_data_cali_upper_ratio);  // [huangwenbin] 联盟厂商搜索竞败数据利用 ratio 上界 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_search_loss_data_cali_boost_threshold);  // [huangwenbin] 联盟厂商搜索竞败数据利用 boost_score 门槛 //NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_search_loss_data_cali_quantile);  // [huangwenbin] 联盟厂商搜索竞败数据利用 CPM 百分位控制 //NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_search_loss_data_algorithm);  // [huangwenbin] 联盟厂商搜索竞败数据利用 模型版本控制 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_manufacturer_high_ctr_pos_explore);  // [huangwenbin] 联盟厂商自有联盟高 CTR POS 扶持控制开关 //NOLINT
DECLARE_SPDM_ABTEST_STRING(manufacturer_high_ctr_pos_explore_ab_tag);  // [huangwenbin] 联盟厂商自有联盟高 CTR POS 扶持 AB 版本标识 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_search_package_flow);  // [huangwenbin] 联盟厂商搜索位次信息利用召粗精总开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_search_rank_explore_by_index);  // [huangwenbin] 联盟厂商搜索位次探索策略开关 //NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_tiny_search_rank_explore_by_index_abtag);  // [huangwenbin] 联盟厂商搜索位次探索策略开关 //NOLINT

// [rentianci]
// 联盟厂商搜索相关性工具对 mcb 是否生效开关，默认生效
DECLARE_SPDM_ABTEST_BOOL(enable_universe_search_relevance_tool_for_mcb);

// [wuhan12]
// 联盟内循环高价值人群探索开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_high_value_crowd_explore);
// 联盟内循环高价值人群探索算法
DECLARE_SPDM_ABTEST_STRING(universe_inner_high_value_crowd_explore_algorithm);
// 联盟内循环高价值人群 Tag AB 标识
DECLARE_SPDM_ABTEST_STRING(universe_inner_high_value_crowd_rank_tag);
// 联盟内循环人群首位探索开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_high_value_crowd_top_explore);
// 联盟内循环人群首位探索算法
DECLARE_SPDM_ABTEST_STRING(universe_inner_high_value_crowd_top_explore_algorithm);
// 联盟内循环人群首位探索 Tag AB 标识
DECLARE_SPDM_ABTEST_STRING(universe_inner_high_value_crowd_top_explore_tag);

// QCPX
// [wuhan12]
DECLARE_SPDM_ABTEST_BOOL(enable_universe_qcpx_coupon_holdout);  // 联盟 qcpx 发券 holdout 开关
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_freq_control_delivery_max_cnt_1d);  // 限制当日券下发最大次数
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_rct_amount_control_flow_percent);  // 满减券 RCT holdout 百分占比
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_rct_amount_treatment_flow_percent);  // 满减 exploration 百分占比
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_model_flow_percent);  // 满减券 model 模型策略发券百分占比
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_roi_thres_ratio);  // 满减券 ROI 系数阈值
DECLARE_SPDM_ABTEST_BOOL(enable_universe_qcpx_admit_u0_rank);  // 联盟 qcpx u0 人群准入开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_qcpx_skip_delivery_freq_control);  // 联盟 qcpx 跳过下发频控开关
// 直播
DECLARE_SPDM_ABTEST_BOOL(enable_universe_qcpx_live_strategy);  // 直播发券策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_qcpx_live_specific_amount_coupon_id);  // 直播限定券模版开关
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_live_specific_amount_coupon_id);  // 直播限定券模版 id
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_live_price_coupon_ratio);  // 直播满减券打折系数 K，即满减为 K*N-N
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_live_min_coupon_amount_yuan);  // 直播满减券最小金额，元
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_live_max_coupon_amount_yuan);  // 直播满减券最大金额，元
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_cvr_ratio_bound);  // 直播满减券 uplift cvr ratio 下界
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_live_half_discount_max_amount);  // 直播满减券五折力度对应的最大券面额
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_ratio_upper_bound);  // 直播 uplift 值盖帽系数
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_ratio_lower_bound);  // 直播 uplift 值兜底系数
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_live_order_uplift_ratio_lower_bound);  // 直播订单 uplift 值兜底系数
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_live_cvr_ratio_new_ocpx);  // 直播满减券兼容新预算 cvr ratio
DECLARE_SPDM_ABTEST_BOOL(enable_universe_qcpx_live_cvr_spline_model);  // 直播 cvr spline model 建模开关
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_spline_upper_bound);  // 直播 spline uplift 值盖帽系数
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_live_uplift_spline_new_upper_bound);  // 新预算 spline 盖帽系数
// 短视频
DECLARE_SPDM_ABTEST_BOOL(enable_universe_qcpx_photo_strategy);  // 短视频发券策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_qcpx_photo_specific_amount_coupon_id);  // 短视频限定券模版开关
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_photo_specific_amount_coupon_id);  // 短视频限定券模版 id
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_photo_price_coupon_ratio);  // 短视频满减券打折系数 K，即满减为 K*N-N
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_photo_min_coupon_amount_yuan);  // 短视频满减券最小金额，元
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_photo_max_coupon_amount_yuan);  // 短视频满减券最大金额，元
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_photo_history_paid_count_thres);  // 短视频商品历史成交单数阈值
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_photo_gmv_greater_min_price_ratio_thres);  // GMV > min_price
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_photo_mode_gmv_ratio_thres);  // 短视频 GMV 众数占比阈值
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_photo_min_price_higher_avg_gmv_ratio_thres);  // min_price > avg_GMV
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_photo_uplift_cvr_ratio_bound);  // 短视频满减券 uplift cvr ratio 下界
DECLARE_SPDM_ABTEST_INT64(universe_qcpx_photo_half_discount_max_amount);  // 短视频满减券五折力度对应的最大券面额  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_photo_uplift_ratio_upper_bound);  // 短视频 uplift 值盖帽系数
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_photo_uplift_ratio_lower_bound);  // 短视频 uplift 值兜底系数
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_photo_cvr_ratio_new_ocpx);  // 短视频满减券兼容新预算 cvr ratio
DECLARE_SPDM_ABTEST_DOUBLE(universe_qcpx_photo_roi_thres_ratio);  // 短视频满减券 ROI 系数阈值

// [wangning14] 搜索广告快投放 bonus 打折
// [liangshuang08]
DECLARE_SPDM_ABTEST_BOOL(enable_calibration_merchant_order_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_calibrate_account);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_seven_days_roi_app);
// [yangjinhui] 联盟电商兴趣分
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ecommerce_interest_score);
// [yangjinhui] 联盟电商行业品类精排 bandit 探索开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ecommerce_category_tag_rank_bandit);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_out_retarget);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_quality_score);  // [yangjinhui] 联盟质量分实验开关
// [yangjinhui] 联盟行业人群策略
DECLARE_SPDM_ABTEST_BOOL(enable_universe_industry_crowd_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_out_retarget_ratio_strategy);
// [huoyan03] 联盟使用主站搜索人群探索
DECLARE_SPDM_ABTEST_BOOL(enable_ks_search_ecom_crowd_strategy);
// [xuxuejian] 联盟有效获客 ECPC 策略
DECLARE_SPDM_ABTEST_DOUBLE(universe_leads_submit_ecpc_threshold);
DECLARE_SPDM_ABTEST_STRING(universe_private_message_ecpc_prefix);
DECLARE_SPDM_ABTEST_DOUBLE(universe_private_message_ecpc_threshold);
// [zhangwenjie08] 联盟主站预估分扰动 ECPC 策略
DECLARE_SPDM_ABTEST_BOOL(enable_universe_main_pcvr_perturb_ecpc);
DECLARE_SPDM_ABTEST_STRING(enable_universe_main_pcvr_perturb_ecpc_expkey);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_game_ios_cheap_flow_explore);  // ios 游戏低价流量探索
DECLARE_SPDM_ABTEST_STRING(enable_universe_game_ios_cheap_flow_explore_exp_key);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_game_ios_cheap_flow_explore_filter_mini);
// [huoyan03] 联盟竞败回传信息大模型推理扰动策略
DECLARE_SPDM_ABTEST_BOOL(enable_universe_compete_fail_info_llm_explore);
DECLARE_SPDM_ABTEST_DOUBLE(universe_compete_fail_info_llm_explore_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_main_pcvr_perturb_ecpc_ocpx);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_main_pcvr_perturb_ecpc_industry_switch);
DECLARE_SPDM_ABTEST_INT64(enable_universe_main_pcvr_perturb_ecpc_industry_switch_key);
// [zengzhengda] 联盟出价工具跳过赔付开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_bid_tool_skip_compensation);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_cost_boost_explore_strategy);  // [zhoumingsong] 联盟欠成本提收策略开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_bid_server_group_tag_fix);  // [zengzhengda] 字段 ad_bid_server_group_tag 修复开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_bid_server_group_tag_fix2);  // [zengzhengda] 字段 ad_bid_server_group_tag 修复开关 2  // NOLINT
// [chendongdong] 联盟 ios 唤端系数
DECLARE_SPDM_ABTEST_BOOL(enable_ios_invoke_boost);
// [liyilin06] 联盟 mcda ecpc 助攻小流量实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_small_scale_mcda_roi_ecpc);  // [liyilin06] roi 小流量实验助攻开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_up_ecpc_pos_adjust);  // [liyilin06] 助攻门槛实验开关
// [fengyajuan] mcda 激活支持双出价
DECLARE_SPDM_ABTEST_BOOL(enable_universe_mcda_conve_double);
// [liyilin06] 联盟 mcda 统一助攻迁出策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_shortvideo_roi_cvrmodel_bottom);  // [fengyajuan] 短视频 roi cvr 准入
DECLARE_SPDM_ABTEST_BOOL(enable_universe_shortvideo_roi_cvrmodel_bottom);  // [fengyajuan] 短视频 roi cvr 准入
DECLARE_SPDM_ABTEST_BOOL(enable_univere_interest_socre_opt_v1);  // [zengzhengda] 联盟意向分实验优化 v1   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_industry_sytle_boost_roas); // [xiemiao] 联盟photo roas 探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_cpm_protect); // [xiemiao] 联盟 pos photo 短视频上界控制探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_cpm_protect_indstyle); // [xiemiao] 联盟 indstyle 短视频上界控制探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_cpm_protect_product); // [xiemiao] 联盟 product 短视频上界控制探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_account_indstyle); // [xiemiao] 联盟 indstyle 短视频切换账户探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_crow_keywords_ecpc); // [zengzhengda] 联盟关键词人群 ecpc   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_crow_active_time); // [zengzhengda] 联盟活跃 app 时长人群 ecpc   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_crow_active_time_log); // [liangshuang] 联盟活跃 app 时长人群 ecpc   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_crow_keyword_log); // [liangshuang] 联盟活跃关键词人群 ecpc   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_intersert_score_opt); // [liangshuang] 联盟活跃兴趣分人群 ecpc   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_app_author_explore);  // [xiemiao] 联盟app author探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_fans_app_author_explore);  // [huangwenbin] 联盟粉丝app author探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_video_fans_app_author_explore);  // [linglong] 联盟短视频粉丝app author探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_lc_pos_account_explore);  // [linglong] 联盟厂商流量探索 account探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_lc_dis_explore);  // [linglong] 联盟厂商分发流量探索 account 探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_lc_uni_explore);  // [linglong] 联盟厂商联盟自有流量探索 account 探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_lc_request_cpm_explore);  // [linglong] 联盟厂商竞胜探索 顶价至 cpm 门槛开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_lc_mab_explore);  // [linglong] 联盟厂商填充探索 mab 探索开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_lc_mab_request_explore);  // [linglong] 联盟厂商填充探索 cpm 门槛探索开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_lc_mab_only_cpm_explore);  // [linglong] 联盟厂商填充探索 mab 只探索没达到cpm门槛的广告 探索开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_sink_indstyle_account_explore);  // [huangwenbin] 联盟下沉市场indstyle account探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_sink_indstyle_account_spu_explore); // [linglong] 联盟下沉市场indstyle accountXspu探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_universe_sink_explore_nobid);  // [huangwenbin] 联盟下沉市场indstyle account探索 nobid 控制 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_explore_center_ocpx_dynamic_control);  // [huangwenbin] 联盟内循环探索中心化动态调控 控制 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_explore_center_media_fuse);  // [huangwenbin] 联盟内循环探索中心化媒体侧融合方案 控制 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_explore_center_dynamic_weight_control);  // [wuhan12] 联盟内循环探索中心化动态权重 控制 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_redirect_indstyle_category_explore);  // [huangwenbin] 联盟重定向单行为探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_game_roas_explore);  // [huangwenbin] 联盟重游人群探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_universe_redirect_single_explore_nobid);  // [huangwenbin] 联盟重定向单行为探索 nobid 控制 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_rank_app_dup_package);  // [huangwenbin] 联盟厂商商店流量精排多样性开关 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_explore_center_sigma_threshold);  // [huangwenbin] 联盟内循环探索中心化方案终止条件 sigma 门槛 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_explore_center_cpm_threshold);  // [huangwenbin] 联盟内循环探索中心化方案终止条件 cpm 门槛 单位厘 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_explore_center_mu_threshold);  // [huangwenbin] 联盟内循环探索中心化方案终止条件 mu 门槛 单位厘 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_explore_center_ratio_upbound);  // [huangwenbin] 联盟内循环探索中心化方案终止条件 ratio 上界控制 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_cost_control);  // [huangwenbin]  内循环探索中心化成本优化 开关 //NOLINT
DECLARE_SPDM_ABTEST_INT64(inner_explore_cost_control_u_level);  // [huangwenbin] 内循环探索中心化成本优化 U 分层控制 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_cost_control_u_level_ratio);  // [huangwenbin]  内循环探索中心化成本优化 U 分层系数控制  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_cost_control_mobile_ratio);  // [huangwenbin] 内循环探索中心化成本优化 移动端系数控制  //NOLINT
DECLARE_SPDM_ABTEST_STRING(inner_explore_cost_control_media_ab_tag);  // [huangwenbin] 内循环探索中心化成本优化 媒体侧 AB 标识控制  //NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_lc_dis_drop_kconf_ratio_tag);  // [linglong] 厂商分发次留调整 kconf AB 标识控制  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_lc_dis_drop_ratio_v2);  // [linglong] 联盟内循环厂商商店 drop 策略 v2 开关  //NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_universe_appstyle_product_explore);  // [xiemiao] 联盟appstyle product 探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_indusytle_cem_alg);  //  [xiemiao] 联盟媒体行业 CEM 探索开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_indusytle_cem_random_once);  //  [xiemiao] 联盟媒体行业 CEM 探索随机开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_indstyle_explore_cem_user);  //  [xiemiao] 联盟媒体行业 CEM 人群开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_bonuscpm_live);  // [xiemiao] 联盟小店直播 POS 下探 bonus 实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_bonuscpm);  // [xiemiao] 联盟小店短视频 POS 下探 bonus 实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tc_cem_live); // [xiemiao] 联盟小店直播订单填充率 cem 实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_new_tc_condition); // [xiemiao] 联盟小店直播填充率 cem 实验新条件开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_app_author_CEM); // [xiemiao] 联盟小店直播填充率 cem 实验重点媒体功能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_all_deep_explore_pos_inner); // [xiemiao] 联盟整体填充实验内循环开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_all_deep_v2_cb); // [xiemiao] 联盟整体填充实验控成本一位开关 V2 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_all_deep_v2_inner_first_ad); // [xiemiao] 联盟整体填充实验开启全深度一位 V2 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_all_deep_v2_inner); // [xiemiao] 联盟整体填充实验内循环开关 V2 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_all_deep_explore_pos_out); // [xiemiao] 联盟整体填充实验外循环开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tc_cem_live_roas); // [xiemiao] 联盟小店直播 roas填充率 cem 实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_fix_mcb_random_charge);  // [xiecong03] 修复 mcb
DECLARE_SPDM_ABTEST_BOOL(enable_calc_rewarded_richenss_info);
DECLARE_SPDM_ABTEST_BOOL(enable_billing_separate_rewarded_video);
DECLARE_SPDM_ABTEST_BOOL(enable_live_play_time_explore_fix); // [yingyuanxiang] 直播开播时间优化修复 //NOLINT

// [wangyang10]
DECLARE_SPDM_ABTEST_BOOL(enable_esp_mobile_live_to_hard);  // 移动版直播投硬广开关
DECLARE_SPDM_ABTEST_INT64(esp_mobile_live_to_hard_quota);  // 移动版直播投硬广 quota

DECLARE_SPDM_ABTEST_BOOL(enable_universe_merchant_video_strategy);  // [chenwu03] 联盟内循环小店短视频潜在用户触达 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_fake_user_explore_strategy);  // [chenwu03] 联盟内循环小店 fake user 探索 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_fake_user_explore_strategy_expand);  // [chenwu03] 联盟内循环小店 fake user 探索 expand //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_loop_multi_purchase_user_reco);  // [yingyuanxiang] 联盟内循环小店重复购买人群 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_loop_multi_purchase_user_live);  // [yingyuanxiang] 联盟内循环直播重复购买人群 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_noboost_unit_bid);  // [xiemiao] 联盟内循环兜底 plugin 中生效 unit粒度实时 开关  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_univ_live_ads_bid_adjust_ratio_hard_bound);    // [yingyuanxiang] 联盟内循环调价幅度控制策略  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_univ_live_reserve_bid_adjust_ratio_hard_bound);    // [yingyuanxiang] 联盟内循环直播预约调价幅度控制策略  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_univ_live_follow_bid_adjust_ratio_hard_bound);    // [yingyuanxiang] 联盟内循环直播涨粉调价幅度控制策略  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_univ_inner_video_pay_cali);  // [yingyuanxiang] 联盟内循环短视频订单校准
DECLARE_SPDM_ABTEST_BOOL(enable_univ_inner_p2l_roas_cali);  // [yingyuanxiang] 联盟内循环引流直播 ROAS 校准
DECLARE_SPDM_ABTEST_BOOL(enable_univ_inner_live_roas_cali);  // [yingyuanxiang] 联盟内循环直播直投 ROAS 校准
DECLARE_SPDM_ABTEST_BOOL(enable_univ_inner_p2l_roas_cali_formula);  // [yingyuanxiang] 联盟内循环引流直播 ROAS 校准 ATV 公式  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_univ_inner_live_roas_cali_formula);  // [yingyuanxiang] 联盟内循环直播直投 ROAS 校准 ATV 公式  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_manufacturer_search_hq_traffic_explore);  // [yingyuanxiang] 厂商搜索优质流量探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_high_quality_crowd_explore);  // [yingyuanxiang]  联盟内循环高质量人群探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_distribution_pos_explore);   // [zhoumingsong] 联盟厂商分发场景探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_distribution_strategy_explore);   // [zhoumingsong] 联盟厂商分发场景探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_distribution_pos_leaderboard_explore);   // [zhoumingsong] 联盟厂商分发场景探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_distribution_pos_leaderboard_in_explore_state);  // [zhoumingsong] 联盟厂商分发场景探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_lc_distribution_cost_ratio);  // [linglong] 联盟厂商分发场景探索 成本率保护开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_distribution_pos_message_explore);   // [zhoumingsong] 联盟厂商分发场景探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_search_ecpc_explore);   // [niejinlong] 联盟厂商搜索场景探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_universe_search_ecpc_explore_on_purchase);   // [niejinlong] 联盟厂商搜索场景探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_search_ecpc_explore_cost_ratio);   // [niejinlong] 联盟厂商搜索场景探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_live_cali_fix);  // [yingyuanxiang] 联盟内循环校准 id 切换   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_rank_ecom_spu_switch);  //  [yingyuanxiang] 联盟 SPU 探索切换 ECOM_SPU  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_spu_explore_center);     // [yingyuanxiang] 联盟 SPU 探索中心化
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_explore_strategy_offline);  // [huangwenbin] 联盟内循环探索策略下线开关   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_roas_low_roi_filter);  // [gongxiaopeng03] 联盟直播 roas 低 roi 打压
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_dark_strategy);  // [chenwu03] 联盟内循环首位暗投策略
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_dark_strategy_v2);  // [chenwu03] 联盟内循环首位暗投策略 V2
DECLARE_SPDM_ABTEST_BOOL(enable_universe_shortvideo_dark_unify_boost);  // [chenwu03] 联盟内循环首位暗投统一提价 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(universe_shortvideo_dark_explore_v1);  // [chenwu03] 联盟内循环暗投探索开关 v1 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_dark_explore_aim_target_cpm);  // [chenwu03] 联盟内循环首位暗投 target cpm 限制 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_retrieval_rank_boost_one_tag);  // [chenwu03] 联盟内循环单召回分支提权 // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(hybrid_auction_billing_ratio_max);
DECLARE_SPDM_ABTEST_DOUBLE(hybrid_auction_billing_ratio_min);
DECLARE_SPDM_ABTEST_DOUBLE(universe_outer_ucvr_bound_ratio);  // [chendongdong] 联盟 cvr 模型 bound 值
DECLARE_SPDM_ABTEST_BOOL(enable_universe_outer_u_ctr);  //  [chendongdong] 联盟外循环统一 ctr
DECLARE_SPDM_ABTEST_BOOL(enable_use_new_ad_rank_infos);  //  [chendongdong] 联盟下发率采样方案提前
//  [liangshuang08] 联盟外循环统一 是否区分 20 分钟内外
DECLARE_SPDM_ABTEST_BOOL(enable_universe_outer_u_ctcvr_split_20min_in_out);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_outer_u_ctcvr_split_20min_in_out_ensemble);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ecpc_raw_cpm_fix);  //  [chengbobo] 联盟 ecpc 字段修复
DECLARE_SPDM_ABTEST_DOUBLE(universe_outer_active_adjust_ratio);  //  [dengtiancong] 联盟激活切分系数
DECLARE_SPDM_ABTEST_BOOL(enable_pos_control_poquan_explore);  // [dingjinren] 联盟破圈探索分 pos 实验开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_poquan_top_limit);  // [dingjinren] 联盟破圈探索限制选定范围 top 开关 //NOLINT
DECLARE_SPDM_ABTEST_STRING(poquan_explore_range_raito);  // [dingjinren] 联盟破圈探索顶价参数 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_flow_poquan_explore);  // [dingjinren] 联盟流量框架探索实验开关 //NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_flow_poquan_explore_exp_name);  // [dingjinren] 联盟流量框架探索实验命中策略 //NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_flow_poquan_explore_exp_ver);  // [dingjinren] 联盟流量框架探索实验策略版本号 //NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_imp_poquan_explore_exp_ver);  // [dingjinren] 联盟流量框架探索实验策略版本号 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_outer_offline_boost_ratio);  // [dingjinren] 外循环 boost 框架系数控制开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_outer_offline_ecpc_ratio);  // [dingjinren] 外循环 ecpc 框架系数控制开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_outer_offline_explore_ratio);  // [dingjinren] 外循环 explore 框架系数控制开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_crow_app_active_time);  // [dingjinren] 用户 APP 活跃时长应用策略 //NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_active_time_app_list_upbound);  // [dingjinren] 活跃 APP 限制 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_active_time_beta);  // [dingjinren] 用户 APP 活跃时长扰动参数 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_active_time_alpha);  // [dingjinren] 用户 APP 活跃时长缩放系数参数 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_active_time_gamma);  // [dingjinren] 用户 APP 活跃时长形状参数 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_active_time_upper);  // [dingjinren] 用户 APP 活跃时长上界 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_active_time_lower);  // [dingjinren] 用户 APP 活跃时长下界 //NOLINT

DECLARE_SPDM_ABTEST_INT64(inner_normal_live_size)
DECLARE_SPDM_ABTEST_INT64(inner_native_live_pc_quota);
DECLARE_SPDM_ABTEST_STRING(rank_dragon_update_exp_tag);

DECLARE_SPDM_ABTEST_BOOL(enable_universe_adx_bid_double_v2);  // [yangjinhui] universe adx bid double

DECLARE_SPDM_ABTEST_BOOL(enable_sctr_into_cpm_guess_you_like);    //  [luwei] cpm 打开猜喜 ctr
//  [gaowei03]  联盟短视频 cvr 门槛
DECLARE_SPDM_ABTEST_DOUBLE(no_bid_merchant_bid_rate);
//  [gaowei03] 联盟内循环 nobid bid 折扣
DECLARE_SPDM_ABTEST_BOOL(enable_universe_shortvideo_roi_cali); // [fengyajuan] 短视频 roi 校准开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_purchase_dcvr_beta);  // [gongxiaopeng03] 联盟付费 dcvr beta
DECLARE_SPDM_ABTEST_BOOL(enable_universe_calibrate_inner_account_admit_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_calibrate_inner_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_roi_calibration_ir);  // [zhoumingsong] 联盟 roi 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ad_roas_calibration_ir);  // [zhoumingsong] 联盟 roi 单校准开关

// [fengyajuan] 探索策略失效开关
DECLARE_SPDM_ABTEST_BOOL(disable_universe_shortvideo_order_posexplore);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_xdt_roas_posexplore);
DECLARE_SPDM_ABTEST_BOOL(disable_shortvideo_industry_sytle_boost);

DECLARE_SPDM_ABTEST_BOOL(enable_universe_video_multi_retr_tag_strategy);  // [chenwu03] 联盟短视频单召回分支在精排首位提价 //NOLINT
// [yaokangping] 微信小游戏 drop 策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_shortvideo_cvrcorrect_exp);  // [fengyajuan] 短视频订单模型调整开关
DECLARE_SPDM_ABTEST_BOOL(enable_photo_ue_thr);  // [lizemin] 风管 cpm 低质量 photo 治理总开关
DECLARE_SPDM_ABTEST_INT64(photo_cpm_ue_tag);  // [lizemin] 风管 cpm 低质量 photo 治理 tag

// [liuxiaofan05]
DECLARE_SPDM_ABTEST_BOOL(enable_context_info_completion);

// [liuyi]
DECLARE_SPDM_ABTEST_BOOL(enable_skip_increase_bid_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_skip_increase_bid_ad_outer_only);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_spu_strategy_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_quality_strategy_exp);

DECLARE_SPDM_ABTEST_BOOL(enable_bs_cover_all_industry_live_type);
DECLARE_SPDM_ABTEST_BOOL(enable_white_product_skip_account_bidding);
DECLARE_SPDM_ABTEST_BOOL(enable_rta_second_predict_use_new_paid);  // [shanminghui] 使用拼多多新版本 paid
DECLARE_SPDM_ABTEST_BOOL(enable_universe_conv_7stay);  // [zhangcong05] 联盟转化率 7 天留存

// ---------------------- abtest integer 参数声明 -------------------
DECLARE_SPDM_ABTEST_BOOL(enable_sctr_into_cpm_follow);

DECLARE_SPDM_ABTEST_INT64(t0_fans_cnt);
DECLARE_SPDM_ABTEST_INT64(t1_fans_cnt);

// ---------------------- abtest double 参数声明 -------------------
DECLARE_SPDM_ABTEST_DOUBLE(live_follow_fast_model_ratio);  // [yingyuanxiang]  直播快速涨粉系数
DECLARE_SPDM_ABTEST_DOUBLE(merchant_follow_model_ratio);  // [yingyuanxiang]  短视频涨粉系数
DECLARE_SPDM_ABTEST_DOUBLE(live_follow_model_ratio);  // [gaowei03]  直播涨粉系数
DECLARE_SPDM_ABTEST_DOUBLE(live_follow_model_upper);  // [gaowei03]  直播涨粉上限
DECLARE_SPDM_ABTEST_DOUBLE(no_bid_live_universe_ltv_threld);
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_boost_uppper_bound_v2);  // [huangwenbin] 联盟内循环精排 boost 上限 V2 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(enable_universe_game_shoufa_ecpc);  // [xuxuejian] 联盟游戏首发实验开关 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_common_cali_upper_bound);  // [haozhitong] 联盟统一校准上限
DECLARE_SPDM_ABTEST_DOUBLE(universe_common_cali_lower_bound);  // [haozhitong] 联盟统一校准下限
DECLARE_SPDM_ABTEST_DOUBLE(universe_common_cali_ltv_upper_bound);  // [haozhitong] 联盟统一校准 ltv 上限
DECLARE_SPDM_ABTEST_DOUBLE(universe_common_cali_ltv_lower_bound);  // [haozhitong] 联盟统一校准 ltv 下限

//  [gaowei03]  联盟直播 nobid ltv 门槛
DECLARE_SPDM_ABTEST_DOUBLE(no_bid_merchant_universe_ltv_threld);
//  [gaowei03]  联盟短视频 nobid ltv 门槛
DECLARE_SPDM_ABTEST_DOUBLE(no_bid_live_universe_paied_threld);
//  [gaowei03]  联盟直播 cvr ltv 门槛
DECLARE_SPDM_ABTEST_DOUBLE(no_bid_merchant_universe_paied_threld)

// ---------------------- abtest string 参数声明 -------------------
DECLARE_SPDM_ABTEST_STRING(fanstop_inner_region_org_exp_types);  // [luoqiang] 跳过内粉 ecpm 过滤的组织 id
DECLARE_SPDM_ABTEST_STRING(universe_retrieval_rank_boost_config);  // [gaoweo03] 联盟内循环召回精排 boost
DECLARE_SPDM_ABTEST_STRING(universe_video_multi_retr_tag_first_up_config);  // [chenwu03] 联盟短视频多路分支首位 boost  //NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_multi_retr_tag_escape_cvr_filter_config);  // [chenwu03] 联盟短视频多路分支获免 cvr 准人过滤  //NOLINT
DECLARE_SPDM_ABTEST_STRING(uax_explore_strategy_names);  // [dingjinren] 联盟 uax 探索实验策略名  //NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_game_score_crowd_id_list);  // [dingjinren] 联盟微小游生效人群包  //NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_calibrate_key);  // [haozhitong] 联盟纠偏 key

// ---------------------- 内粉专用 参数声明区域（防 git 冲突） -------------------
// ---------------------- abtest bool 参数声明 -------------------
DECLARE_SPDM_ABTEST_BOOL(enable_universe_nobid_merchant);   //  [gaowei03] 联盟短视频 nobid 开关
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_rank_high_priority_quota);  // 粉条精排保量 quota 开关
// ---------------------- abtest int64 参数声明 -------------------
DECLARE_SPDM_ABTEST_INT64(fanstop_rank_high_priority_photo_quota);  // 粉条精排 photo 保量 quota
DECLARE_SPDM_ABTEST_INT64(fanstop_rank_high_priority_live_quota);  // 粉条精排 live 保量 quota
DECLARE_SPDM_ABTEST_INT64(universe_ocpx_sample_size);   // [chendongdong] 联盟 ocpx 采样个数

DECLARE_SPDM_ABTEST_BOOL(enable_initial_release_tag);
DECLARE_SPDM_ABTEST_DOUBLE(initial_release_base_prob);
DECLARE_SPDM_ABTEST_DOUBLE(initial_release_prob_max);
DECLARE_SPDM_ABTEST_DOUBLE(initial_release_prob_min);
DECLARE_SPDM_ABTEST_BOOL(enable_retarget_force_tag);

DECLARE_SPDM_ABTEST_BOOL(enable_corporation_ceiling_rank);  // [guoqi03] 天花板精排
// [wuyonghong] 天花板精排换到新的强出 tag
DECLARE_SPDM_ABTEST_BOOL(enable_corporation_ceiling_rank_force_tag);
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower);  // [dingyiming05] 计费比下界
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper);  // [dingyiming05] 计费比上界
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower);  // [dingyiming05] 软广计费比下界
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper);  // [dingyiming05] 软广计费比上界
DECLARE_SPDM_ABTEST_BOOL(enable_small_app_direct_jump);  // [wanglei10] 小程序广告请求精排模型增加特征
DECLARE_SPDM_ABTEST_BOOL(enable_nextstay_white_list_tiny);  // [zhangcong05] 联盟白名单模型支持小系统
DECLARE_SPDM_ABTEST_BOOL(enable_tiny_request_pid_service);  // [wanglei10] 小系统请求 pid service
DECLARE_SPDM_ABTEST_DOUBLE(universe_ltv_expand_upper);  // [xuxuejian] 游戏 ltv 值扩大上界

DECLARE_SPDM_ABTEST_BOOL(enable_fiction_conv2_purchase_exp_new);  // [zhangtie] 联盟激活到付费实验 小说推广

// [xiaowentao] 用户 x 场景 x app_id 首曝顶价策略
DECLARE_SPDM_ABTEST_BOOL(enable_user_scene_req_freq_boost);

// [xiaowentao] 首曝顶价运营工具
DECLARE_SPDM_ABTEST_BOOL(enable_req_freq_boost_op_config);

// [zhangsongyu] 联盟过滤低 cpm 策略
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ban_low_cpm_explore);
// [zhangsongyu] 联盟高价值模型融合策略
DECLARE_SPDM_ABTEST_BOOL(enable_universe_high_cpm_model_fusion);
// [zhangtie] 联盟 pos 位行为相关特征
DECLARE_SPDM_ABTEST_BOOL(enable_universe_pos_action_feas);

DECLARE_SPDM_ABTEST_BOOL(enable_fix_leads_submit_conflict); // [zhangcong05] 修复私信模型前链路冲突 //NOLINT

// [lijingtao03] 联盟内循环 cid 模型接入开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_cid_cvr);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_cid_gmv);

// [suzhentao] 联盟短剧模型接入开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_playlet_iaa_ltv);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_playlet_iaa_cvr);
}  // namespace ad_rank
}  // namespace ks
