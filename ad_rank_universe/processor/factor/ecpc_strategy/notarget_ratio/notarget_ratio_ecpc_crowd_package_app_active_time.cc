#include "teams/ad/ad_rank_universe/processor/factor/ecpc_strategy/notarget_ratio/notarget_ratio_ecpc_crowd_package_app_active_time.h"

#include "teams/ad/ad_rank_universe/processor/framework/udf_manager.h"
#include "teams/ad/ad_rank_universe/common/context_data.h"
#include "teams/ad/ad_rank_universe/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank_universe/strategy/ranking_data.h"
#include "teams/ad/ad_rank_universe/data/p2p/universe_industry_active_app_p2p.h"

namespace ks {
namespace ad_rank {

std::vector<UniverseEcpcInfo>& NoTargetRatioEcpcCrowdPackageAppActiveTime::GetEcpcInfos(AdCommon* ad) {
  auto object = engine_base::UniverseEcpcOptimizationType::ECPC_CROW_PACKAGE_APP_ACTIVE_TIME;
  return ad->universe_ecpc_infos_map[ecpc_strategy_type][object];
}

void NoTargetRatioEcpcCrowdPackageAppActiveTime::ProcessEcpcInfo(
    AdCommon* ad, UniverseEcpcInfo* ecpc_info, double pdcvr, double target) {
  ecpc_info->ecpc_unify_input = pdcvr;
  ecpc_info->target = target;
}

bool NoTargetRatioEcpcCrowdPackageAppActiveTime::GetPdcvrAndTarget(
    AdCommon* ad, UniverseEcpcInfo* ecpc_info, double* pdcvr, double* target) {
  RANK_DOT_COUNT(session_data_, 1, "ecpc_crow_package_app_active_time");
  double alpha = SPDM_universe_active_time_alpha(session_data_->spdm_ctx);
  double gamma = SPDM_universe_active_time_gamma(session_data_->spdm_ctx);
  double upper = SPDM_universe_active_time_upper(session_data_->spdm_ctx);
  double lower = SPDM_universe_active_time_lower(session_data_->spdm_ctx);
  double ratio = 1.0;
  auto iter =
    session_data_->universe_data.universe_active_time_ind2ratio_map.find(ad->get_first_industry_id_v5());
  if (SPDM_enableBoostRatioSuppress()) {
    if (iter != session_data_->universe_data.universe_active_time_ind2ratio_map.end()) {
      ratio = iter->second;
      // 只在 ecpc 系数大于一的时候进行顶价
      if (ratio > 1.0) {
        auto active_app_industry_data = ad_rank::UniverseIndustryActiveAppStat::GetInstance();
        if (active_app_industry_data != nullptr) {
          std::string ocpx_action_type = kuaishou::ad::AdActionType_Name(ad->get_ocpx_action_type());
          const auto& pm = session_data_->pos_manager_base;
          // account_id X p_type X media_app_id X ad_style;
          std::string key = absl::StrCat(ad->get_account_id(), "_", ocpx_action_type, "_",
              pm.GetRequestAppId(), "_", session_data_->universe_data.ad_style);
          std::string score_str;
          if (active_app_industry_data->GetActiveAppStatData(key, &score_str)) {
            double score;
            if (absl::SimpleAtod(score_str, &score)) {
              double bandit_ratio = 2.0 / (1 + exp(-score/gamma)) - 0.5;
              ratio = std::min(std::max(lower, alpha * bandit_ratio * ratio), upper);
            }
          }
        }
      }
    }
    if (ratio <= 1.0) {
      RANK_DOT_COUNT(session_data_, 1, "ecpc_crow_package_app_active_time_invalid");
      return false;
    }
  } else {
    if (iter != session_data_->universe_data.universe_active_time_ind2ratio_map.end()) {
      ratio = iter->second;
      // 只在 ecpc 系数大于一的时候进行顶价
      if (ratio > 1.0) {
        auto active_app_industry_data = ad_rank::UniverseIndustryActiveAppStat::GetInstance();
        if (active_app_industry_data != nullptr) {
          std::string ocpx_action_type = kuaishou::ad::AdActionType_Name(ad->get_ocpx_action_type());
          const auto& pm = session_data_->pos_manager_base;
          // account_id X p_type X media_app_id X ad_style;
          std::string key = absl::StrCat(ad->get_account_id(), "_", ocpx_action_type, "_",
              pm.GetRequestAppId(), "_", session_data_->universe_data.ad_style);
          std::string score_str;
          if (active_app_industry_data->GetActiveAppStatData(key, &score_str)) {
            double score;
            if (absl::SimpleAtod(score_str, &score)) {
              double bandit_ratio = 2.0 / (1 + exp(-score/gamma)) - 0.5;
              ratio = std::min(std::max(lower, alpha * bandit_ratio * ratio), upper);
            }
          }
        }
      }
    } else {
      RANK_DOT_COUNT(session_data_, 1, "ecpc_crow_package_app_active_time_invalid");
      return false;
    }
  }
  *pdcvr = ratio;
  *target = 1;
  return true;
}
RegisterClass(UdfFactor, NoTargetRatioEcpcCrowdPackageAppActiveTime);

}  // namespace ad_rank
}  // namespace ks
