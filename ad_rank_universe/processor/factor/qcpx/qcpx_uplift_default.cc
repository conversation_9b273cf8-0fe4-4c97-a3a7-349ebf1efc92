#include "teams/ad/ad_rank_universe/processor/factor/qcpx/qcpx_uplift_default.h"

#include <algorithm>
#include <cstdint>
#include <string>
#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"
#include "absl/strings/string_view.h"

#include "teams/ad/ad_rank_universe/processor/framework/udf_manager.h"
#include "teams/ad/ad_rank_universe/common/context_data.h"
#include "teams/ad/ad_rank_universe/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank_universe/utils/kconf/kconf.h"
#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/engine_base/utils/utils.h"

#include "teams/ad/ad_rank_universe/processor/factor/qcpx/qcpx_strategy_live/qcpx_strategy.h"
#include "teams/ad/ad_rank_universe/processor/factor/qcpx/qcpx_strategy_photo/qcpx_strategy.h"

namespace ks {
namespace ad_rank {

void QcpxUpliftDefault::Init(ContextData *session_data) {
  // 流量准入
  if (SPDM_enable_universe_qcpx_coupon_holdout(session_data->spdm_ctx)) {
    is_run_qcpx_flag_ = false;
    session_data->universe_inner_qcpx_pv_filter_reason =
      kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_ADMITFAIL_IS_HOLDOUT;
    return;
  }
  qcpx_media_black_list_config_ = RankKconfUtil::universeQcpxMediaBlackListConfig();
  is_run_qcpx_flag_ = VersionControl(session_data) && CheckPvAdmit(session_data);
}

bool QcpxUpliftDefault::Admit(ContextData* session_data, AdCommon* p_ad) {
  if (!is_run_qcpx_flag_) {
    return false;
  }
  // 预算准入
  // ocpm mcb
  auto bid_type = p_ad->get_bid_type();
  if (bid_type != kuaishou::ad::AdEnum::OCPM_DSP &&
      bid_type != kuaishou::ad::AdEnum::MCB) {
    p_ad->set_universe_inner_qcpx_filter_reason(kuaishou::ad::AdEnum_UniverseInnerQcpxAdFilterReason_ADMITFAIL_INVALID_INNER_DEEP);  // NOLINT
    return false;
  }
  // 内循环深度预算
  bool is_universe_inner_deep_ad = false;
  is_universe_inner_deep_ad = (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
    || p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE)
    && (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED
    || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
    || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS
    || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI);
  if (!is_universe_inner_deep_ad) {
    p_ad->set_universe_inner_qcpx_filter_reason(kuaishou::ad::AdEnum_UniverseInnerQcpxAdFilterReason_ADMITFAIL_INVALID_INNER_DEEP);  // NOLINT
    return false;
  }
  // 直播圈选 author_id
  if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
    && IsFilterAuthorAd(p_ad)) {
    p_ad->set_universe_inner_qcpx_filter_reason(kuaishou::ad::AdEnum_UniverseInnerQcpxAdFilterReason_ADMITFAIL_BLACKLIST_AUTHOR);  // NOLINT
    return false;
  }
  // 短视频圈选 item_id
  if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE
    && IsFilterItemAd(p_ad)) {
    p_ad->set_universe_inner_qcpx_filter_reason(kuaishou::ad::AdEnum_UniverseInnerQcpxAdFilterReason_ADMITFAIL_BLACKLIST_ITEM);  // NOLINT
    return false;
  }
  // 营销目标 x 广告场景 x 合作模式 黑名单
  if (qcpx_media_black_list_config_->data().IsHit(
        p_ad->get_campaign_type(),
        session_data->universe_data.ad_style,
        session_data->universe_data.cooperation_mode,
        session_data->universe_data.pos_id)) {
    p_ad->set_universe_inner_qcpx_filter_reason(kuaishou::ad::AdEnum_UniverseInnerQcpxAdFilterReason_CAMP_MEDIA_BLACKLIST);  // NOLINT
    return false;
  }
  if (SPDM_enableUniverseQcpxPerf()) {
    session_data->dot_perf->Count(1, "qcpx", "inner_deep_request_number");
    session_data->dot_perf->Interval(0, "qcpx", "coupon_request_number");
  }
  return true;
}

double QcpxUpliftDefault::Process(ContextData* session_data, AdCommon* p_ad) {
  if (SPDM_enable_universe_qcpx_live_strategy(session_data->spdm_ctx)
    && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
    && !IsDeliveredAuthorAd(session_data, p_ad)) {
    QcpxLiveStrategy qcpx_live_runner(session_data);
    qcpx_live_runner.InitParams();
    qcpx_live_runner.Process(p_ad);
  }
  bool is_skip_qcpx_mode = session_data->is_skip_qcpx_mode && p_ad->is_closure_ad();
  if (!is_skip_qcpx_mode && SPDM_enable_universe_qcpx_photo_strategy(session_data->spdm_ctx)
    && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE
    && !IsDeliveredItemAd(session_data, p_ad)) {
    QcpxPhotoStrategy qcpx_photo_runner(session_data);
    qcpx_photo_runner.InitParams();
    qcpx_photo_runner.Process(p_ad);
  }
  double ratio = p_ad->get_uplift_cpm_ratio();
  if (ratio < 1.0) {
    ratio = 1.0;
  }
  RANK_DOT_STATS(session_data, ratio * 10000, "qcpx_uplift_default_ratio");
  return ratio;
}

bool QcpxUpliftDefault::CheckPvAdmit(ContextData* session_data) {
  // 媒体圈选
  std::string media_app_id = session_data->pos_manager_base.GetRequestAppId();
  auto media_app_id_backlist = RankKconfUtil::UniverseQcpxMediaAppBackList();
  if (media_app_id_backlist) {
    if (media_app_id_backlist->count(media_app_id) > 0) {
      session_data->universe_inner_qcpx_pv_filter_reason =
        kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_ADMITFAIL_BLACKLIST_MEDIA;
      return false;
    }
  }
  // 广告场景圈选
  int32_t ad_style = session_data->universe_data.ad_style;
  auto ad_style_backlist = RankKconfUtil::UniverseQcpxAdStyleBackList();
  if (ad_style_backlist) {
    if (ad_style_backlist->count(ad_style) > 0) {
      session_data->universe_inner_qcpx_pv_filter_reason =
        kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_ADMITFAIL_BLACKLIST_STYLE;
      return false;
    }
  }
  // 人群圈选
  bool is_high_value_user = false;
  auto user_tag_set_ptr = RankKconfUtil::UniverseQcpxUserTagList();
  if (SPDM_enable_universe_qcpx_admit_u0_rank(session_data->spdm_ctx)) {
    user_tag_set_ptr = RankKconfUtil::UniverseQcpxUserTagListV2();
  }
  if (user_tag_set_ptr) {
    for (int64_t bit_pos : *user_tag_set_ptr) {
      uint64_t filter_user_tag = 1ULL << bit_pos;
      is_high_value_user = (((session_data->universe_data.universe_inner_loop_user_tag) &
        filter_user_tag) > 0);
      if (is_high_value_user) {
        break;
      }
    }
  }
  if (!is_high_value_user) {
    session_data->universe_inner_qcpx_pv_filter_reason =
      kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_ADMITFAIL_INVALID_CROWD;
    return false;
  }
  // 券频控
  if (!SPDM_enable_universe_qcpx_skip_delivery_freq_control(session_data->spdm_ctx)) {
    // 如果不跳过券下发频控，则限制当日券下发次数
    int32_t universe_qcpx_freq_control_delivery_max_cnt_1d =
      SPDM_universe_qcpx_freq_control_delivery_max_cnt_1d(session_data->spdm_ctx);
    if (session_data->qpon_delivery_daily_cnt > universe_qcpx_freq_control_delivery_max_cnt_1d) {
      session_data->universe_inner_qcpx_pv_filter_reason =
        kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_FREQCONTROL_DEL;
      return false;
    }
  }
  return true;
}

bool QcpxUpliftDefault::VersionControl(ContextData* session_data) {
  if (!SPDM_enableUniverseQcpxVersionControl()) {
    return true;
  }
  std::string version =
      !session_data->ks_version.empty() ? session_data->ks_version : session_data->nebula_version;
  if (version.empty()) {
    session_data->universe_inner_qcpx_pv_filter_reason =
      kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_ADMITFAIL_INVALID_VERSION;
    return false;
  }
  if (engine_base::CompareAppVersion(version, "13.1.20") < 0) {
    session_data->universe_inner_qcpx_pv_filter_reason =
      kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_ADMITFAIL_INVALID_VERSION;
    return false;
  }
  return true;
}

bool QcpxUpliftDefault::IsFilterAuthorAd(AdCommon* p_ad) {
  // 沿用主站 + 联盟额外单独设置 author_id 黑名单
  int64_t author_id = p_ad->get_author_id();
  if (RankKconfUtil::qcpxFilterAuthorList() != nullptr) {
    if (RankKconfUtil::qcpxFilterAuthorList()->count(author_id) > 0) {
      return true;
    }
  }
  if (RankKconfUtil::UniverseQcpxFilterAuthorList() != nullptr) {
    if (RankKconfUtil::UniverseQcpxFilterAuthorList()->count(author_id) > 0) {
      return true;
    }
  }
  return false;
}

bool QcpxUpliftDefault::IsFilterItemAd(AdCommon* p_ad) {
  // 沿用主站 + 联盟额外单独设置 item_id 黑名单
  int64_t item_id = p_ad->get_merchant_product_id();
  if (RankKconfUtil::qcpxFilterItemList() != nullptr) {
    if (RankKconfUtil::qcpxFilterItemList()->count(item_id) > 0) {
      return true;
    }
  }
  if (RankKconfUtil::UniverseQcpxFilterItemList() != nullptr) {
    if (RankKconfUtil::UniverseQcpxFilterItemList()->count(item_id) > 0) {
      return true;
    }
  }
  return false;
}

bool QcpxUpliftDefault::IsDeliveredAuthorAd(ContextData* session_data, AdCommon* p_ad) {
  // user_id * author_id 券下发一致性
  int64_t author_id = p_ad->get_author_id();
  auto author_2_delivered_qpon_map = session_data->author_2_delivered_qpon_map;
  auto it = author_2_delivered_qpon_map.find(author_id);
  if (it == author_2_delivered_qpon_map.end()) {
    return false;
  }
  DeliveredQponInfo delivered_qpon_info = it->second;
  int32_t universe_inner_qcpx_cause = delivered_qpon_info.universe_inner_qcpx_cause;
  int32_t coupon_type = delivered_qpon_info.coupon_type;
  int64_t amount_coupon_id = delivered_qpon_info.coupon_template_id;
  int64_t coupon_threshold = delivered_qpon_info.threshold;
  int64_t coupon_amount = delivered_qpon_info.coupon_amount;
  double bid_qpon_ratio = delivered_qpon_info.bid_qpon_ratio;
  double uplift_ctr_ratio = delivered_qpon_info.uplift_ctr_ratio;
  double uplift_cvr_ratio = delivered_qpon_info.uplift_cvr_ratio;
  // 填充相同 author_id 下发的券信息
  bool flag = true;
  flag &= coupon_type > 0;
  if (!flag) return false;
  // 满减券信息填充
  if (coupon_type == 1) {
    flag &= amount_coupon_id > 0;
    flag &= coupon_amount > 0;
    flag &= coupon_threshold > coupon_amount;
    flag &= bid_qpon_ratio > 0;
    flag &= uplift_ctr_ratio > 0;
    flag &= uplift_cvr_ratio > 0;
    if (!flag) return false;
    p_ad->set_universe_inner_qcpx_cause(universe_inner_qcpx_cause);
    p_ad->set_coupon_template_id(amount_coupon_id);
    p_ad->set_coupon_type(coupon_type);
    p_ad->set_threshold(coupon_threshold);
    p_ad->set_coupon_discount_amount(coupon_amount);
    p_ad->set_threshold_type(1);
    p_ad->set_threshold_upper(8000000);
    p_ad->set_discount_amount_upper(150000);
    p_ad->set_reduce_amount(0);
    p_ad->set_capped_amount(0);
  }
  kuaishou::ad::AdEnum::QponType qpon_type = kuaishou::ad::AdEnum_QponType_INNER_UNION_QCPX_OPEN;  // NOLINT
  p_ad->SetQponInfo(qpon_type,
    bid_qpon_ratio,
    uplift_cvr_ratio,
    uplift_ctr_ratio,
    kuaishou::ad::AdEnum::PLATFORM_ONLY);
  p_ad->set_coupon_scope(1);
  return true;
}

bool QcpxUpliftDefault::IsDeliveredItemAd(ContextData* session_data, AdCommon* p_ad) {
  // user_id * item_id 券下发一致性
  int64_t item_id = p_ad->get_merchant_product_id();
  auto item_2_delivered_qpon_map = session_data->item_2_delivered_qpon_map;
  auto it = item_2_delivered_qpon_map.find(item_id);
  if (it == item_2_delivered_qpon_map.end()) {
    return false;
  }
  DeliveredQponInfo delivered_qpon_info = it->second;
  int32_t universe_inner_qcpx_cause = delivered_qpon_info.universe_inner_qcpx_cause;
  int32_t coupon_type = delivered_qpon_info.coupon_type;
  int64_t amount_coupon_id = delivered_qpon_info.coupon_template_id;
  int64_t coupon_threshold = delivered_qpon_info.threshold;
  int64_t coupon_amount = delivered_qpon_info.coupon_amount;
  double bid_qpon_ratio = delivered_qpon_info.bid_qpon_ratio;
  double uplift_ctr_ratio = delivered_qpon_info.uplift_ctr_ratio;
  double uplift_cvr_ratio = delivered_qpon_info.uplift_cvr_ratio;
  // 填充相同 item_id 下发的券信息
  bool flag = true;
  flag &= coupon_type > 0;
  if (!flag) return false;
  // 满减券信息填充
  if (coupon_type == 1) {
    flag &= amount_coupon_id > 0;
    flag &= coupon_amount > 0;
    flag &= coupon_threshold > coupon_amount;
    flag &= bid_qpon_ratio > 0;
    flag &= uplift_ctr_ratio > 0;
    flag &= uplift_cvr_ratio > 0;
    if (!flag) return false;
    p_ad->set_universe_inner_qcpx_cause(universe_inner_qcpx_cause);
    p_ad->set_coupon_template_id(amount_coupon_id);
    p_ad->set_coupon_type(coupon_type);
    p_ad->set_threshold(coupon_threshold);
    p_ad->set_coupon_discount_amount(coupon_amount);
    p_ad->set_threshold_type(1);
    p_ad->set_threshold_upper(8000000);
    p_ad->set_discount_amount_upper(150000);
    p_ad->set_reduce_amount(0);
    p_ad->set_capped_amount(0);
  }
  kuaishou::ad::AdEnum::QponType qpon_type = kuaishou::ad::AdEnum_QponType_INNER_UNION_QCPX_OPEN;  // NOLINT
  p_ad->SetQponInfo(qpon_type,
    bid_qpon_ratio,
    uplift_cvr_ratio,
    uplift_ctr_ratio,
    kuaishou::ad::AdEnum::PLATFORM_ONLY);
  p_ad->set_coupon_scope(2);
  return true;
}

RegisterClass(UdfFactor, QcpxUpliftDefault);
}  // namespace ad_rank
}  // namespace ks
