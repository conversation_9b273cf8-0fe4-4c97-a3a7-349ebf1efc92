#include "teams/ad/ad_rank_universe/common/context_data.h"

#include <algorithm>
#include <unordered_set>
#include <functional>

#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "absl/strings/substitute.h"
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "base/time/timestamp.h"
#include "gflags/gflags_declare.h"
#include "google/protobuf/repeated_field.h"
#include "ks/base/abtest/single_file_dynamic_config.h"
#include "ks/serving_util/dynamic_config.h"
#include "ks/util/json.h"
#include "ks/base/abtest/abtest_instance.h"
#include "nlohmann/json.hpp"
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_base/src/common/deploy_utils.h"
#include "teams/ad/ad_base/src/common/os_version.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "teams/ad/ad_base/src/util/check_reco_request_type.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_rank_universe/utils/utility/ksn_util.h"
#include "teams/ad/ad_rank_universe/utils/utility/utility.h"
#include "teams/ad/ad_base/src/common/multi_retrieval_tag.h"
#include "teams/ad/engine_base/cache_loader/support_project_p2p_cache.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/engine_base/universe/rank/utils/universe_media_cpm_bound_realtime.h"
#include "teams/ad/ad_rank_universe/data/cache/clock_cache_adapter.h"
#include "teams/ad/ad_base/src/container/singleton.h"
#include "teams/ad/ad_rank_universe/utils/spdm/spdm_switches.h"
#include "teams/ad/engine_base/data_stability/depend_data_level.h"
#include "teams/ad/ad_base/src/kess/client_helper.h"
#include "teams/ad/engine_base/search/util/card_style/card_style_utiils.h"

DECLARE_int32(ksp_group_deploy_type);
DECLARE_bool(is_universe);
DEFINE_bool(is_adrank_offline_diff_test, false, "is ad rank server offline diff test service");
DEFINE_bool(open_chain_log, false, "open chain log");

using ks::ad_target::multi_retr::RetrievalTag;

namespace ks {
namespace ad_rank {
#define _Attr_(v) #v
const char * ContextData::attr_names_[CommonIdx::MAX_COMMON_ATTR_NUM] = { ALL_COMMON_ATTRS };
#undef _Attr_

static const auto kLocalTimeZone = absl::LocalTimeZone();

ContextData::ContextData(): hostname(ad_base::GetHostName()),
                            for_test(ks::ad_base::AdKessClient::Instance().IsTestEnv()) {
}

void ContextData::SetBidCpmMonitorUniverse(const std::string& name,
                                           int64 auction_bid,
                                           int64 cpm,
                                           kuaishou::ad::AdEnum::BidType bid_type) {
  // 采样 20%
  if (llsid % 100 < 20) {
    dot_perf->Interval(auction_bid,
            absl::Substitute("ad_rank.rank_benefit.auction_bid_$0", name),
            kuaishou::ad::AdEnum::BidType_Name(bid_type));
    dot_perf->Interval(cpm,
            absl::Substitute("ad_rank.rank_benefit.cpm_$0", name),
            kuaishou::ad::AdEnum::BidType_Name(bid_type));
  }
}

static const std::unordered_set<kuaishou::log::ad::AdTraceFilterCondition> invalid_filter_set{
    kuaishou::log::ad::AdTraceFilterCondition::ADX_RETRIEVAL_RESULTS,
    kuaishou::log::ad::AdTraceFilterCondition::LUCKY_ONE,
    kuaishou::log::ad::AdTraceFilterCondition::FRONT_LUCKY_ONE};

void ContextData::RecordLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition, bool is_hard) {
  if (invalid_filter_set.count(condition)) {
    return;
  }
  if (is_hard) {
    last_filter_condition_ = condition;
  } else {
    soft_last_filter_condition_ = condition;
  }
}

void ContextData::LogInfo(int64_t total_cost_us,
                          const std::unordered_map<std::string, int64>& nodes_cost) {
  int32_t universe_log_freq = SPDM_universeLogInfoFreq();
  int64_t pos_id = 0;
  const auto& ad_request = rank_request->ad_request();
  if (ad_request.universe_ad_request_info().imp_info().size() > 0) {
    pos_id = ad_request.universe_ad_request_info().imp_info(0).position_id();
  }
  std::string nodes_cost_str;
  for (auto& kv : nodes_cost) {
    absl::SubstituteAndAppend(&nodes_cost_str, " $0_cost:$1us", kv.first, kv.second);
  }

  LOG_EVERY_N(INFO, universe_log_freq) << "AdRank user_id:" << user_id
                                       << "|llsid:" << llsid
                                       << "|device_id:" << ad_request.ad_user_info().device_id()
                                       << "|gid:" << gid
                                       << "|app_id:" << ad_request.universe_ad_request_info().app_id()
                                       << "|pos_id:" << pos_id
                                       << "|req_ad_num:" << rank_request->ad_candidate_size()
                                       << "|res_ad_num:" << rank_response->ad_rank_result_size()
                                       << "|time_cost:" << total_cost_us / 1000
                                       << "|node_cost:" << nodes_cost_str;
}

void ContextData::UniverseInitialize(const kuaishou::ad::AdRequest& ad_request) {
  // 共用逻辑
  predict_score_thresh_config =  &RankKconfUtil::predictScoreThresholdConfigV2()->data();
  is_ios_platform = (ad_request.ad_user_info().platform() == "ios");
  app_version = ad_request.ad_user_info().platform_version();
  // 目前仅主 app 发现页未登录用户出广告
  is_unlogin_user = ad_request.ad_user_info().is_unlogin_user();
  is_skip_qcpx_mode = SPDM_enable_closure_support_mode(spdm_ctx) &&
      ad_request.universe_ad_request_info().closure_support_mode();
  dot_perf = new ks::ad_base::Dot(ad_request.product(), pos_manager_base.GetAdRequestType(),
      pos_manager_base.GetInteractiveForm(), pos_manager_base.GetSubPageId());
  page_id = pos_manager_base.GetMediumPageId();
  sub_page_id = pos_manager_base.GetSubPageId();
  if (ad_request.universe_ad_request_info().imp_info_size() > 0) {
    page_id = ad_request.universe_ad_request_info().imp_info(0).page_id();
    sub_page_id = ad_request.universe_ad_request_info().imp_info(0).sub_page_id();
  }
  set_sub_page_id(sub_page_id);
  if (SPDM_enable_universe_rank_fix_medium_uid(spdm_ctx)) {
    medium_uid = ad_request.universe_ad_request_info().medium_uid();
  }

  // 获取 abtest 平台的 广告形态数据
  FillAbTestData();
  // 填充多路 cmd
  FillMultiRetrievalCmd();
  InitMultiColor(ad_request);
  group_tag = rank_request->group_tag();
  deep_group_tag = rank_request->deep_group_tag();
  work_flow_type = rank_request->work_flow_type();
  // 联盟 mcda ab 分组
  universe_ecpc_united_mcda_exp_id =
      kconf_session_context->TryGetInteger("universe_ecpc_united_mcda_exp_id", 0);

  // 联盟单独逻辑
  if (rank_request->ad_request().universe_ad_request_info().imp_info_size() > 0) {
    universe_data.rtb_bid_coff =
        rank_request->ad_request().universe_ad_request_info().imp_info(0).rtb_cpm_bid_coff();
  }
  if (ad_request.ad_user_info().has_universe_inner_loop_user_tag()) {
    universe_data.universe_inner_loop_user_tag = ad_request.ad_user_info().universe_inner_loop_user_tag();
  }
  universe_data.Initialize(this);
  for (auto& ad_style_info : rank_request->ad_style_response().ad_style_infos()) {
    if (ad_style_info.style_groups_size() > 0) {
      ad_style_res.emplace(std::piecewise_construct,
        std::forward_as_tuple(ad_style_info.creative_id()),
        std::forward_as_tuple(&ad_style_info));
    }
  }
  ks_version = ad_request.universe_ks_version();
  nebula_version = ad_request.universe_nebula_version();

  // 用于统计 rank 收到了多少请求
  dot_perf->Count(1, "rank_request_num");
}

void ContextData::GetUserDefineBoundConfig(AdCommon* ad,
    const std::string &optimization_object,
    double *lowerbound,
    double *upperbound,
    double *gamma) {
  if (ecpc_user_define_bound_map == nullptr) {
    return;
  }
  int64_t account_id = ad->get_account_id();
  std::string product_name = ad->base_np.product_name;
  int64_t industry_id_v3 = ad->get_industry_id_v3();
  int64_t ocpx_action_type = static_cast<int64> (ad->get_ocpx_action_type());
  // 上下界优先级 optimization_object > account > product > industry > ocpx
  if (ecpc_user_define_bound_map->optimization_object_upperbound().count(optimization_object) > 0) {
    *upperbound = ecpc_user_define_bound_map->optimization_object_upperbound().at(optimization_object);
  } else if (ecpc_user_define_bound_map->account_upperbound().count(account_id) > 0) {
    *upperbound = ecpc_user_define_bound_map->account_upperbound().at(account_id);
  } else if (ecpc_user_define_bound_map->product_upperbound().count(product_name) > 0) {
    *upperbound = ecpc_user_define_bound_map->product_upperbound().at(product_name);
  } else if (ecpc_user_define_bound_map->second_industry_upperbound().count(industry_id_v3) > 0) {
    *upperbound = ecpc_user_define_bound_map->second_industry_upperbound().at(industry_id_v3);
  } else if (ecpc_user_define_bound_map->ocpx_action_upperbound().count(ocpx_action_type) > 0) {
    *upperbound = ecpc_user_define_bound_map->ocpx_action_upperbound().at(ocpx_action_type);
  }

  if (ecpc_user_define_bound_map->optimization_object_lowerbound().count(optimization_object) > 0) {
    *lowerbound = ecpc_user_define_bound_map->optimization_object_lowerbound().at(optimization_object);
  } else if (ecpc_user_define_bound_map->account_lowerbound().count(account_id) > 0) {
    *lowerbound = ecpc_user_define_bound_map->account_lowerbound().at(account_id);
  } else if (ecpc_user_define_bound_map->product_lowerbound().count(product_name) > 0) {
    *lowerbound = ecpc_user_define_bound_map->product_lowerbound().at(product_name);
  } else if (ecpc_user_define_bound_map->second_industry_lowerbound().count(industry_id_v3) > 0) {
    *lowerbound = ecpc_user_define_bound_map->second_industry_lowerbound().at(industry_id_v3);
  } else if (ecpc_user_define_bound_map->ocpx_action_lowerbound().count(ocpx_action_type) > 0) {
    *lowerbound = ecpc_user_define_bound_map->ocpx_action_lowerbound().at(ocpx_action_type);
  }

  if (ecpc_user_define_bound_map->optimization_object_gamma().count(optimization_object)) {
    *gamma = ecpc_user_define_bound_map->optimization_object_gamma().at(optimization_object);
  }
}

void ContextData::InitGlobalAdTable() {
  common_w_->SetPtrCommonAttr(
      kDefaultItemTableAttrs,
      std::make_shared<AdListAttr>(common_w_->GetOrInsertDataTable(kDefaultItemTableName)));
  const AdListAttr* table_attrs = common_w_->GetPtrCommonAttr<AdListAttr>(kDefaultItemTableAttrs);
  ad_list.InitAdTable(table_attrs);
}

bool ContextData::Initialize(const kuaishou::ad::AdRequest& ad_request) {
  start_ts = base::GetTimestamp();
  pos_manager_base.Initialize(ad_request);
  rpc_arena = std::make_shared<google::protobuf::Arena>();
  union_ltr_used_item = google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::UsedItem>(
          rpc_arena.get());
  context = google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::Context>(
          rpc_arena.get());

  ad_list.SetRequestInfo(pos_manager_base.GetAdRequestType());
  InitGlobalAdTable();

  // 时间相关
  time_now = absl::Now();
  bd = time_now.In(kLocalTimeZone);

  user_id = ad_request.ad_user_info().id();
  gid = ad_request.gid();
  user_level_v2_risk = ad_request.ad_user_info().user_level_v2_risk();

  // 竞胜率模型参数
  if (ad_request.ad_user_info().rtb_win_rate_model_params_size() == 2) {
    rtb_win_rate_model_params.push_back(ad_request.ad_user_info().rtb_win_rate_model_params()[0]);
    rtb_win_rate_model_params.push_back(ad_request.ad_user_info().rtb_win_rate_model_params()[1]);
  }

  // 计算 llsid
  Json extra_ctx(StringToJson(ad_request.extra_request_ctx()));
  llsid = extra_ctx.GetInt("llsid", -1);
  perf_sample_ratio = RankKconfUtil::perfUnifySampleRatio();

  auto ab_init_st = base::GetTimestamp();
  is_universe_flow =
      (pos_manager_base.GetAdRequestType() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNIVERSE);

  auto ab_init_ed = base::GetTimestamp();

  UniverseInitialize(ad_request);

  auto* pv_item_table = common_w_->GetOrInsertDataTable("pv_item_table");
  auto item = pv_item_table->AddCommonRecoResult(llsid, 0, 0, 0);
  auto position_id_attr = pv_item_table->GetOrInsertAttr("position_id");
  item.SetIntAttr(position_id_attr, universe_data.pos_id);
  ecpc_user_define_bound_map = RankKconfUtil::universeEcpcUserDefineBoundConfig()->ptr_data();
  universe_ecommerce_interest_score_industry_list =
      engine_base::AdKconfUtil::universeEcommerceInterestScoreIndustryList();
  universe_ecommerce_interest_score_product_list =
      engine_base::AdKconfUtil::universeEcommerceInterestScoreProductList();
  universe_ecommerce_interest_score_account_list =
      engine_base::AdKconfUtil::universeEcommerceInterestScoreAccountList();
  universe_crowd_ratio = RankKconfUtil::universeCrowdRatio();
  universe_game_score_crowd_ratio = RankKconfUtil::universeGameScoreCrowdRatio();
  ks_search_ecom_crowd_ratio = RankKconfUtil::ksSearchEcomCrowdRatio();
  universe_product_opration = RankKconfUtil::universeProductOpration();
  universe_ug_proi2_maitaining_cost_ratio_account_map =
      engine_base::AdKconfUtil::universeUgProi2MaitainingCostRatioAccountMap();
  universe_ug_proi2_operation_cost_ratio_account_map =
      engine_base::AdKconfUtil::universeUgProi2OperationCostRatioAccountMap();
  enable_universe_ecpc_strategy_info_ratio_lowerbound =
    kconf_session_context->TryGetDouble("enable_universe_ecpc_strategy_info_ratio_lowerbound", 0.1);
  enable_universe_ecpc_strategy_info_ratio_upperbound =
    kconf_session_context->TryGetDouble("enable_universe_ecpc_strategy_info_ratio_upperbound", 2.0);
  universe_ecpc_movingavg_decay_weight =
      kconf_session_context->TryGetDouble("universe_ecpc_movingavg_decay_weight", 0.99);
  universe_ecpc_pow_gamma = kconf_session_context->TryGetDouble("universe_ecpc_pow_gamma", 1.0);
  optimazation_method = kconf_session_context->TryGetString("optimazation_method", "pltv0");
  universe_pcxr_start_end = kconf_session_context->TryGetString("universe_pcxr_start_end", "purchase_ltv");
  seven_roi_optimazation_method =
      kconf_session_context->TryGetString("universe_ecpc_seven_roi_optimazation_method", "null");
  roi_optimazation_method =
      kconf_session_context->TryGetString("universe_ecpc_roi_optimazation_method", "null");
  enable_universe_industry_crowd_strategy =
      SPDM_enable_universe_industry_crowd_strategy(spdm_ctx);
  enable_ks_search_ecom_crowd_strategy =
      SPDM_enable_ks_search_ecom_crowd_strategy(spdm_ctx);
  universe_leads_submit_ecpc_threshold =
      SPDM_universe_leads_submit_ecpc_threshold(spdm_ctx);
  universe_private_message_ecpc_threshold =
      SPDM_universe_private_message_ecpc_threshold(spdm_ctx);
  enable_universe_main_pcvr_perturb_ecpc =
      SPDM_enable_universe_main_pcvr_perturb_ecpc(spdm_ctx);
  enable_universe_out_retarget = SPDM_enable_universe_out_retarget(spdm_ctx);
  factor_active_time_v2 = RankKconfUtil::adUniverseEcpcActiveTimeFactor();
  enable_key_action_ecpc_adjusted_by_pid =
    kconf_session_context->TryGetBoolean("enable_key_action_ecpc_adjusted_by_pid", false);
  universe_key_action_ecpc_adjusted_by_pid_lowerbound =
    kconf_session_context->TryGetDouble("universe_key_action_ecpc_adjusted_by_pid_lowerbound", -1.0);
  universe_key_action_ecpc_adjusted_by_pid_upperbound =
    kconf_session_context->TryGetDouble("universe_key_action_ecpc_adjusted_by_pid_upperbound", 1.0);
  keyaction_retention_target_map = RankKconfUtil::universeKeyActionRetentionTargetConfig()->ptr_data();
  poquan_explore_strategy_kconf = RankKconfUtil::universePoquanExploreStrategyKconf()->ptr_data();
  flow_explore_exp_strategy_kconf = RankKconfUtil::universeFlowExploreExpConf()->ptr_data();
  flow_explore_param_conf = RankKconfUtil::universeFlowExploreParamConf();
  poquan_explore_param_conf = RankKconfUtil::universePoquanExploreParamConf();

  enable_universe_intersert_score_opt =
    SPDM_enable_universe_intersert_score_opt(spdm_ctx);
  enable_universe_crow_keyword_log =
    SPDM_enable_universe_crow_keyword_log(spdm_ctx);
  enable_universe_crow_active_time_log =
    SPDM_enable_universe_crow_active_time_log(spdm_ctx);
  enable_universe_crow_app_active_time =
    SPDM_enable_universe_crow_app_active_time(spdm_ctx);
  enable_ecpc_active_time_reverse = SPDM_enableUniverseEcpcActiveTimeReverse();
  enable_ecpc_adjust_dim = SPDM_enableUniverseEcpcAdjustDim();
  enable_ecommerce_interest_score = SPDM_enable_universe_ecommerce_interest_score(spdm_ctx);
  is_universe_tiny_flow = (sub_page_id == 19000002);

  int64_t pos_id = 0;
  if (pos_manager_base.request_imp_infos.size() > 0) {
    pos_id = pos_manager_base.request_imp_infos[0].pos_id;
  }
  if (RankKconfUtil::universeTinySearchPos()->count(pos_id) > 0 &&
    ad_request.universe_ad_request_info().search_query().empty()) {
    is_pkg_name_flow = true;  // 搜索广告位但没有搜索词，走包名 only 逻辑
    if (SPDM_enable_universe_tiny_search_package_flow(spdm_ctx))
      request_pkg_name_relevance = ad_request.universe_ad_request_info().imp_info(0).suggest_pkg_relevance();
  }

  req_is_allow_user_jump_wx =
      ad_request.ad_user_info().universe_inner_admit_info().is_allow_non_fake_user_jump_wx();
  print_factor_info_random_num = ad_base::AdRandom::GetInt(0, 15);
  universe_ks_main_cvr_map = std::make_shared<std::unordered_map<int64_t, double>>();
  universe_ks_main_pcvr_perturb_map = std::make_shared<std::unordered_map<std::string, int64_t>>();
  return true;
}

double ContextData::GetRtbBidCoff() const {
  double cpm_bid_coff = RankKconfUtil::universeCpmBidCoff();
  // cooperation_mode = 4 七猫不请求 bid_universe_adx 服务
  // 七猫实际结算方式同 adn 无需调价保证实际分成比例
  if (IsUniverseRtbTraffic()
      && pos_manager_base.GetCooperationMode() != 4) {
    if (RankKconfUtil::enablePidCpmBid()) {
      int64_t pos_id = 0;
      auto request_imp_infos = pos_manager_base.request_imp_infos;
      if (request_imp_infos.size() > 0) {
        pos_id = request_imp_infos[0].pos_id;
      }
      ks::engine_base::UniverseMediaCpmBoundRealtime::GetInstance()
        ->GetCpmBidCoff(pos_manager_base.GetRequestAppId(),
            pos_id, &cpm_bid_coff);
    }
  }
  return cpm_bid_coff;
}

void ContextData::FillMultiRetrievalCmd() {
  // 先填充 multi_retrieval_cmd
  for (auto iter : rank_request->multi_retrieval_info()) {
    multi_retrieval_cmd.emplace(iter.tag(), iter.index_name());
  }
}

void ContextData::DotMultiTag(const std::string& metric_key, const std::string& color_name,
                              const std::unordered_map<int32_t, int32_t>& retr_tag_map) {
  for (const auto& pair : retr_tag_map) {
    auto cmd_iter = multi_retrieval_cmd.find(pair.first);
    if (cmd_iter != multi_retrieval_cmd.end()) {
      const std::string& cmd = cmd_iter->second;
      // 打点监控
      if (pair.second > 0) {  // NOLINT
        auto maybe_tag = ks::ad_target::multi_retr::RetrievalTag::_from_integral_nothrow(pair.first);  // NOLINT
        if (!maybe_tag) {
          continue;
        }
        const std::string& tag_string = maybe_tag->_to_string();  // NOLINT
        dot_perf->Interval(pair.second, metric_key, tag_string, cmd);
        multi_color_data.Color(pair.first, color_name, pair.second);
      }
    }
  }

  return;
}

bool ContextData::IsUgMonopolizeFlow() {
  auto ug_monopolize_flow = engine_base::AdKconfUtil::ugMonopolizeFlowCpm();
  if (pos_manager_base.request_imp_infos.empty()) {
    return false;
  }
  int64_t pos_id = pos_manager_base.request_imp_infos[0].pos_id;
  if (ug_monopolize_flow->count(pos_id) != 0) {
    return true;
  }
  return false;
}

void ContextData::InitMultiColor(const kuaishou::ad::AdRequest& ad_request) {
  if (ad_request.debug_param().debug_mode() && ad_request.debug_param().multi_tag_id() > 0) {
    multi_color_data.tag_id = ad_request.debug_param().multi_tag_id();
  }
}

// 获取 abtest 平台的 广告形态数据
void ContextData::FillAbTestData() {
  auto ab_test_for_ad_forms = RankKconfUtil::abtestForAdForms();
  if (ab_test_for_ad_forms != nullptr) {
    JsonObject json;
    for (auto ab_test_for_ad_form : *ab_test_for_ad_forms) {
      switch (ab_test_for_ad_form.second) {
        case 1:
          json.set(ab_test_for_ad_form.first,
                    kconf_session_context->TryGetBoolean(ab_test_for_ad_form.first, false));
          break;
        case 2:
          json.set(ab_test_for_ad_form.first,
                    kconf_session_context->TryGetInteger(ab_test_for_ad_form.first, 0));
          break;
        case 3:
          json.set(ab_test_for_ad_form.first,
                    kconf_session_context->TryGetString(ab_test_for_ad_form.first, ""));
          break;
        default:
          break;
      }
    }
    std::string ab_test_for_ad_forms_{};  //  广告形态的 abtest 数据的 jsonstring
    if (json.get()) {
      ab_test_for_ad_forms_ = base::JsonToString(json.get());
    }
    ab_test_hash_id = std::hash<std::string>()(ab_test_for_ad_forms_);
  }
}

ContextData::~ContextData() {
  if (dot_perf) {
    delete dot_perf;
    dot_perf = nullptr;
  }
  spdm_ctx.Clear();
}

AdRankUnifyScene ContextData::GetUnifyScene() {
  return AdRankUnifyScene::UNIVERSE;
}

void ContextData::PrepareInitAttr(const kuaishou::ad::AdRequest& ad_request,
    ks::platform::MutableRecoContextInterface* context) {
  attrs_.fill(nullptr);
  common_w_ = context;
  common_r_ = context;
  Json extra_ctx(StringToJson(ad_request.extra_request_ctx()));
  int64_t tmp_llsid = extra_ctx.GetInt("llsid", -1);
  uint64_t tmp_user_id = ad_request.ad_user_info().id();

  spdm_ctx.Reset(
      tmp_llsid, tmp_user_id, ad_request.ad_user_info().device_id(), ks::AbtestBiz::AD_DSP,
      ad_request.ad_user_info().abtest_mapping_id());
  common_w_->SetIntCommonAttr("user_id_for_ab_param", tmp_user_id);

  // // 统一注册以前的成员变量
  // #include "context_data-context_data.init.extra"  // NOLINT
  // set_llsid(tmp_llsid);
  // set_user_id(tmp_user_id);
  // set_start_ts(base::GetTimestamp());
}

void ContextData::Clear() {
  common_w_ = nullptr;
  common_r_ = nullptr;
  ks::ad_base::ClientTagHelper::Instance().Clear();
  rpc_arena.reset();
  node_time_cost.clear();

  spdm_ctx.Clear();
  downstream_time_cost.clear();
  ad_style_res.clear();

  ad_list.Clear(dot_perf);
  if (dot_perf) {
    delete dot_perf;
    dot_perf = nullptr;
  }
  ad_predict_stats.clear();
  ad_predict_stats_.clear();
  tab_type = kuaishou::ad::algorithm::TabType::UNKNOWN_TAB;
  page_id = 0;
  sub_page_id = 0;
  print_factor_info_random_num = 0;
  cpm_percentile = 0.0;
  high_cvr_origin_cvr_ratio = 1.0;
  use_high_cvr_origin_cvr_ratio = false;
  conv_product_name_cnt = 0;
  conv_total_cnt_bucket = -1;
  conv_product_names = "";
  conv_second_industry_names = "";
  conv_product_ratios_buckets = "";
  click_product_name_cnt = 0;
  click_total_cnt_bucket = -1;
  click_product_names = "";
  click_second_industry_names = "";
  click_product_ratios_buckets = "";


  llsid = -1;  // 唯一标示一次请求的 llsid
  user_id = 0;
  gid = "";
  medium_uid = 0;
  // 综合电商付费 cut_by_imp
  ps_is_timeout = false;
  enable_photo_ue_thr = false;
  is_universe_tiny_flow = false;
  photo_cpm_ue_tag = 0;
  union_ltr_used_item = nullptr;
  context = nullptr;

  last_filter_condition_ = kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION;
  soft_last_filter_condition_ = kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION;

  multi_retrieval_cmd.clear();
  multi_color_data.Clear();
  ad_rank_infos.clear();
  new_ad_rank_infos.clear();
  group_tag.clear();
  deep_group_tag.clear();

  rank_filter_ads.clear();
  creative_2_rank_infos_map.clear();

  is_universe_flow = false;
  rank_cmds.clear();
  is_ios_platform = false;
  ad_request_times = 0;
  query_ps_times = 0;

  work_flow_type = 0;
  ranking_ps_cost_ms = 0;

  start_ts = 0;
  pos_manager_base.Clear();
  app_version.clear();

  predict_score_thresh_config = nullptr;
  pre_mt_product_account = nullptr;

  is_unlogin_user = false;
  is_skip_qcpx_mode = false;
  ab_test_hash_id = 0;
  perf_sample_ratio = 10;
  inner_loop_creative_set.clear();

  // ad_cxr_map.Clear();
  // 联盟参数清理
  if (ks::ad_rank::IsUniverseKsn()) {
    universe_data.Clear();
  }
  universe_rank_sample_cid_set.clear();

  guaranteed_tags.clear();

  coupon_ids_set.clear();
  ad_coupon_template_map.Clear();
  universe_inner_qcpx_pv_filter_reason =
    kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_QCPX_UNKNOWN_TYPE_PV;

  enable_print_factor_info = false;
  req_is_allow_user_jump_wx = false;
  print_factor_info_random_num = 0;
  formula_info.clear();
  downstream_waiter.clear();
  rtb_win_rate_model_params.clear();
  universe_ks_main_cvr_map->clear();
  universe_ks_main_pcvr_perturb_map->clear();
}

int ContextData::GetWeekday() const {
  return bd.weekday;
}
}  // namespace ad_rank
}  // namespace ks
