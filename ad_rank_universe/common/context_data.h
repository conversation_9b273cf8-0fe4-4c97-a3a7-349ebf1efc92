#pragma once

#include <cstdint>
#include <functional>
#include <map>
#include <memory>
#include <ostream>
#include <set>
#include <sstream>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/common/basic_types.h"
#include "glog/logging.h"
#include "google/protobuf/arena.h"
#include "ks/base/perfutil/perfutil.h"
#include "absl/time/time.h"
#include "teams/ad/ad_base/src/common/multi_color_data.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/ad_base/src/klog/ad_engine_chain_trace_log.h"
#include "teams/ad/ad_base/src/klog/ad_ylog.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_base.h"
#include "teams/ad/ad_base/src/spdm_lib/src/context.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_hosting.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_model_calibrate.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_style_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_style_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adx/cxr/adx_cxr.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adx/track/adx_track.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/budget_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/counter/ad_counter_service.kess.grpc.pb.h"
#include "teams/ad/ad_rank_universe/common/ad_common.h"
#include "teams/ad/ad_rank_universe/common/base_data_def.h"
#include "teams/ad/ad_rank_universe/common/enum.h"
#include "teams/ad/ad_rank_universe/common/macro.h"
#include "teams/ad/ad_rank_universe/common/universe_data.h"
#include "teams/ad/ad_rank_universe/utils/kconf/kconf.h"
#include "teams/ad/ad_rank_universe/utils/utility/ksn_util.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "teams/ad/ad_rank_universe/node/universe_calc_benefit/plugin/universe_calc_benefit_params.h"
#include "teams/ad/ad_rank_universe/common/row_data_map.h"
#include "teams/ad/ad_rank_universe/common/common_attrs.h"

namespace base {
class Json;
}

namespace kuaishou {
namespace ad {
class AdRequest;
}
}

using kuaishou::ad::algorithm::UniversePredictRequest;
using kuaishou::ad::algorithm::UniversePredictResponse;

namespace ks {
namespace ad_rank {

namespace kconf {
class PtdsFirstClickConf_Values;
class PredictScoreThresholdConfig;
class PreMtProductAccount;
}

struct DeliveredQponInfo {
  int32_t universe_inner_qcpx_cause = 0;
  int32_t coupon_type = 0;
  int64_t coupon_template_id = 0;
  int64_t coupon_amount = 0;
  int64_t threshold = 0;
  int64_t reduce_amount = 0;
  int64_t capped_amount = 0;
  double bid_qpon_ratio = 0.0;
  double uplift_cvr_ratio = 0.0;
  double uplift_ctr_ratio = 0.0;
};

#define _Attr_(v) v
enum CommonIdx : int {
  ALL_COMMON_ATTRS,  // 这个是上面的宏展开, 不要在这里加变量
  MAX_COMMON_ATTR_NUM
};
#undef _Attr_

// 请求的 context，记录请求级的参数
// NOTE(baixiaohang)：暂时不做持久化，每个请求新生成一个
struct ContextData final {
  // dragon commonattr, 读写分离, 建立 ContextData 和 dragon attr 之间的关系,
  //   1. 如果上下文中有 dragon, 请直接使用 dragon 的接口
  //   2. 如果上下文中没有 dragon, 可使用 context 获取合适的 common_r_, common_w_ 进行读写
  ks::platform::MutableRecoContextInterface *common_w_ = nullptr;
  ks::platform::ReadableRecoContextInterface *common_r_ = nullptr;
  // dragon commonattr 结束, ContextData 的 Clear 、 析构函数中不要使用上面连个变量
  ks::spdm::Context spdm_ctx;
  ks::spdm::Context* kconf_session_context = &spdm_ctx;

  std::shared_ptr<google::protobuf::Arena> rpc_arena;

  absl::Time time_now;       // 当前时刻 absl::Time 值
  absl::Time::Breakdown bd;  // 当前时刻 absl::Time::Breakdown 值

  void InitGlobalAdTable();
  AdList ad_list;  // 广告队列
  int64 page_id;
  int64 sub_page_id;
  // 记录 ranksvr 过滤广告，用于监控
  std::unordered_map<int64, kuaishou::log::ad::AdTraceFilterCondition> rank_filter_ads;
  mutable std::unordered_map<std::string, kuaishou::ad::SessionRankInfo *> creative_2_rank_infos_map;

  std::vector<kuaishou::ad::PerfInfo::NodeTimeCost> node_time_cost;

  // 从请求中解析得到的信息
  int64 llsid = -1;  // 唯一标示一次请求的 llsid
  uint64 user_id = 0;  // 用户 id
  std::string gid = "";
  std::string user_level_v2_risk;  //  电商用户分级 u0～u4

  // universe
  int64 medium_uid = 0;  // 本次请求媒体的 uid, 主要针对中台联盟流量
  bool for_test = false;                       // 是否测试环境
  bool ps_is_timeout = false;   // 精排请求的返回码是否为超时
  int64_t start_ts = 0;

  // 风管 cpm 治理
  bool enable_photo_ue_thr = false;
  int64_t photo_cpm_ue_tag = 0;
  // predict cmds
  std::vector<std::string> rank_cmds;

  // 多路数据
  std::unordered_map<int64_t, std::string> multi_retrieval_cmd;
  // 多路 debug 统计信息
  ks::ad_base::MultiColorData multi_color_data;

  // predict server ack 结果
  kuaishou::ad::algorithm::UsedItem *union_ltr_used_item = nullptr;
  kuaishou::ad::algorithm::TabType tab_type;
  kuaishou::ad::algorithm::Context *context = nullptr;
  std::vector<AdRankInfo> ad_rank_infos;  // 用于填充 UsedItem 中的 RankInfo
  std::vector<AdRankInfo> new_ad_rank_infos;  // 新方案中用于填充 UsedItem 中的 RankInfo
  PsBoundChecker ps_bound_checker;  // 精排预估值兜底

  bool is_universe_flow = false;                // 是否为联盟流量
  UniverseCalcBenefitParams universe_calc_benefit_params;  // 联盟 calc_benefit 参数

  bool is_ios_platform = false;   // 是否为 ios 平台

  kuaishou::ad::AdRankRequest* rank_request{nullptr};
  kuaishou::ad::AdRankResponse* rank_response{nullptr};

  // 标示流量类型，0 代表正常流量，1 代表随机探索，2 代表独占流量, 3 表示 retarget 独占
  int64_t work_flow_type = 0;
  int64_t ranking_ps_cost_ms = 0;
  // 新版广告位
  ks::ad_base::PosManagerBase pos_manager_base;

  // thread data 不随 pv 的变化而变化
  std::string hostname{};
  std::string app_version{};  // 快手版本号

  int64_t query_ps_times = 0;  // 请求 ps 的次数
  int64_t ad_request_times = 0;

  bool is_unlogin_user = false;  // 是否为未登录用户
  bool is_skip_qcpx_mode = false;  // 闭环电商跳过 qcpx 逻辑

  std::string group_tag{};  // bid_server 调价 group_tag
  std::string deep_group_tag{};  //  bid_server 深度调价 group_tag
  uint64_t ab_test_hash_id = 0;   // ab_test_for_ad_forms_ 的 hash 结果

  // 联盟 mcda ab 分组
  int32_t universe_ecpc_united_mcda_exp_id = 0;

  // 外循环 mcda pre-exp 白名单
  const ks::ad_rank::kconf::PreMtProductAccount* pre_mt_product_account = nullptr;

  std::map<std::string, AdPredictStatWrapper> ad_predict_stats;
  std::map<std::string, kuaishou::ad::RankStageInfo::AdPredictStat> ad_predict_stats_;
  const ks::ad_rank::kconf::PredictScoreThresholdConfig* predict_score_thresh_config = nullptr;
  // ecpc 参数
  std::shared_ptr<UniverseEcpcUserDefineBoundConfig> ecpc_user_define_bound_map;
  std::shared_ptr<std::set<int64_t>> universe_ecommerce_interest_score_industry_list;
  std::shared_ptr<std::set<std::string>> universe_ecommerce_interest_score_product_list;
  std::shared_ptr<std::set<int64_t>> universe_ecommerce_interest_score_account_list;
  std::shared_ptr<std::map<int64_t, double>> universe_crowd_ratio;
  std::shared_ptr<std::map<int64_t, double>> universe_game_score_crowd_ratio;
  std::shared_ptr<std::map<std::string, double>> ks_search_ecom_crowd_ratio;
  std::shared_ptr<std::map<std::string, int64_t>> universe_product_opration;
  std::shared_ptr<std::map<int64_t, double>> universe_ug_proi2_maitaining_cost_ratio_account_map;
  std::shared_ptr<std::map<int64_t, double>> universe_ug_proi2_operation_cost_ratio_account_map;
  std::shared_ptr<std::unordered_map<int64_t, double>> universe_ks_main_cvr_map;
  std::shared_ptr<std::unordered_map<std::string, int64_t>> universe_ks_main_pcvr_perturb_map;
  bool enable_universe_intersert_score_opt{false};
  bool enable_universe_crow_keyword_log{false};
  bool enable_universe_crow_active_time_log{false};
  bool enable_universe_crow_app_active_time{false};
  bool enable_universe_ecpc_active_time_reverse{false};
  bool enable_ecpc_active_time_reverse{false};
  bool enable_ecpc_adjust_dim{false};
  bool enable_ecommerce_interest_score{false};
  bool enable_universe_industry_crowd_strategy{false};
  bool enable_ks_search_ecom_crowd_strategy{false};
  double universe_leads_submit_ecpc_threshold = 0.0;
  double universe_private_message_ecpc_threshold = 0.0;
  bool enable_universe_main_pcvr_perturb_ecpc{false};
  bool enable_universe_out_retarget{false};
  bool enable_universe_not_impression_crowd_strategy{false};
  int32 factor_active_time_v2{50};
  double enable_universe_ecpc_strategy_info_ratio_lowerbound = 0.1;
  double enable_universe_ecpc_strategy_info_ratio_upperbound = 2.0;
  double universe_ecpc_movingavg_decay_weight = 0.99;
  double universe_ecpc_pow_gamma = 1.0;
  std::string optimazation_method;
  std::string universe_pcxr_start_end;
  std::string seven_roi_optimazation_method;
  std::string roi_optimazation_method;
  std::string universe_out_retarget_rank_ratio;
  bool enable_key_action_ecpc_adjusted_by_pid = false;
  double universe_key_action_ecpc_adjusted_by_pid_lowerbound = -1.0;
  double universe_key_action_ecpc_adjusted_by_pid_upperbound = 1.0;
  std::shared_ptr<UniverseKeyActionRetentionTargetConfig> keyaction_retention_target_map;
  std::shared_ptr<UniversePoquanExploreStrategyKconf> poquan_explore_strategy_kconf;
  std::shared_ptr<UniverseFlowExploreExpConf> flow_explore_exp_strategy_kconf;
  std::shared_ptr<std::map<std::string, double>> flow_explore_param_conf;
  std::shared_ptr<std::map<std::string, double>> poquan_explore_param_conf;

  // 广告位特征
  double high_cvr_origin_cvr_ratio = 1.0;
  bool use_high_cvr_origin_cvr_ratio = false;  // 是否用高价值模型
  double cpm_percentile = 0.0;
  int64_t conv_product_name_cnt = 0;
  int64_t conv_total_cnt_bucket = -1;
  std::string conv_product_names = "";
  std::string conv_second_industry_names = "";
  std::string conv_product_ratios_buckets = "";
  int64_t click_product_name_cnt = 0;
  int64_t click_total_cnt_bucket = -1;
  std::string click_product_names = "";
  std::string click_second_industry_names = "";
  std::string click_product_ratios_buckets = "";

  // qcpx 相关参数
  int32_t qpon_delivery_daily_cnt = 0;  // 用户当日下发 qcpx 券次数
  absl::flat_hash_map<int64_t, DeliveredQponInfo> author_2_delivered_qpon_map;  // author_id 粒度当日下发券信息，用于券一致性 // NOLINT
  absl::flat_hash_map<int64_t, DeliveredQponInfo> item_2_delivered_qpon_map;  // item_id 粒度当日下发券信息，用于券一致性 // NOLINT
  absl::flat_hash_set<int64_t> coupon_ids_set;  // 优惠券 id 候选集
  RowDataMap ad_coupon_template_map;  // 策略正排访问 ad_coupon_template 表的结果 // NOLINT
  kuaishou::ad::AdEnum::UniverseInnerQcpxPvFilterReason universe_inner_qcpx_pv_filter_reason;  // 内循环 qcpx pv 级别过滤原因 // NOLINT

  // 竞胜率模型参数
  std::vector<double> rtb_win_rate_model_params;

  bool is_live_audience_exclude_sub_page_id = false;
  // debug 相关数据

  ks::ad_base::Dot* dot_perf = nullptr;
  int64_t perf_sample_ratio = 10;

  std::set<uint64_t> inner_loop_creative_set;
  UniverseData universe_data;

  // 记录最后一个广告过滤原因, 用于记录请求被过滤的最终原因
  kuaishou::log::ad::AdTraceFilterCondition last_filter_condition_;
  // 增加软广最后过滤原因
  kuaishou::log::ad::AdTraceFilterCondition soft_last_filter_condition_;
  // ad_style_server 返回数据
  std::unordered_map<int64_t, const kuaishou::ad::AdStyleSelectionResponse_AdStyleInfo*> ad_style_res;

  std::unordered_set<uint64_t> universe_rank_sample_cid_set;

  std::unordered_set<int64_t> guaranteed_tags;  // 强出的召回 tag
  std::unordered_set<int64_t> game_guaranteed_tags;  // 游戏行业强出的召回 tag

  absl::flat_hash_map<std::string, std::vector<std::function<void()>>> downstream_waiter;
  // 暂时保留
  std::vector<kuaishou::ad::PerfInfo::DownStreamServiceStatus> downstream_time_cost;
  bool enable_print_factor_info = false;
  bool req_is_allow_user_jump_wx = false;
  absl::flat_hash_map<std::string, std::string> formula_info;
  std::shared_ptr<std::map<std::string, std::string>> formula_describe_mapping;
  int32_t print_factor_info_random_num = 0;

  std::string ks_version;   // 快手版本
  std::string nebula_version;   // 快手极速版版本

  bool is_universe_tiny_flow{false};

  bool is_pkg_name_flow = false;  // 是否厂商包名流量
  int32_t request_pkg_name_relevance = 0;  // 请求级包名价值（厂商定义）

 private:
  // 使用此数组加速 dragon 数据的获取, 用于单次 pv 多次访问的场景
  // 1. 类型检查 auto_gen_context_data.py, 2. 枚举 3. 设置值 4. 写 get_xxx 函数
  mutable std::array<ks::platform::CommonAttr*, CommonIdx::MAX_COMMON_ATTR_NUM> attrs_;
  static const char * attr_names_[CommonIdx::MAX_COMMON_ATTR_NUM];
  inline ks::platform::CommonAttr& Accessor(const CommonIdx &idx) const {
    if (attrs_[idx] == nullptr) {
      // 暂不考虑线程安全, 认为这是在单线程使用的
      attrs_[idx] = common_w_->GetCommonAttrAccessor(attr_names_[idx]);
    }
    return *attrs_[idx];
  }

 public:
 #include "context_data-context_data.inl"  // NOLINT

  // 接口域
 public:
  ContextData();
  ~ContextData();
  ContextData(const ContextData& other) = delete;
  ContextData& operator = (const ContextData& other) = delete;
  void PrepareInitAttr(const kuaishou::ad::AdRequest& ad_request,
                       ks::platform::MutableRecoContextInterface* common);  // 准备 spdm 和 context

  bool Initialize(const kuaishou::ad::AdRequest& ad_request);
  void UniverseInitialize(const kuaishou::ad::AdRequest& ad_request);
  void LogInfo(int64_t total_cost_us, const std::unordered_map<std::string, int64>& nodes_cost);
  int  GetWeekday() const;
  void SetBidCpmMonitorUniverse(const std::string& name,
                                int64 auction_bid,
                                int64 cpm,
                                kuaishou::ad::AdEnum::BidType bid_type);

  AdRankUnifyScene GetUnifyScene();

  double GetRtbBidCoff() const;

  void GetUserDefineBoundConfig(AdCommon* ad,
    const std::string &optimization_object,
    double *lowerbound,
    double *upperbound,
    double *gamma);

  bool IsUniverseTraffic() const {
    return (pos_manager_base.GetAdRequestType() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNIVERSE);
  }

  bool IsUniverseRtbTraffic() const {
    auto cooperation_mode = pos_manager_base.GetCooperationMode();
    return IsUniverseTraffic() && (cooperation_mode == 4 || cooperation_mode == 5 || cooperation_mode == 6 ||
                                   cooperation_mode == 7 || cooperation_mode == 8 || cooperation_mode == 9);
  }

  bool IsRtbProfitTrafic() const {
    auto cooperation_mode = pos_manager_base.GetCooperationMode();
    return IsUniverseTraffic() && (cooperation_mode == 6 ||
                                   cooperation_mode == 7);
  }

  void FillAbTestData();

  // 填充多路 cmd
  void FillMultiRetrievalCmd();

  // 多路打点
  void DotMultiTag(const std::string& metric_key, const std::string& tag_name,
                   const std::unordered_map<int32_t, int32_t>& tag_retr);

  void InitMultiColor(const kuaishou::ad::AdRequest& ad_request);

  bool IsUgMonopolizeFlow();

  void RecordLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition, bool is_native);

  // !!!! 所有的成员变量都需要重新 Init
  void Clear();
};

struct TimeRecorder {
  TimeRecorder(ContextData* session_data, int32 node_type) {
    enter_time_ = base::GetTimestamp();
    session_data_ = session_data;
    node_time_cost.set_node_type(node_type);
  }

  ~TimeRecorder() {
    if (session_data_) {
      node_time_cost.set_time_cost_ms(base::GetTimestamp() - enter_time_);
      node_time_cost.set_time_enter_ms(enter_time_ - session_data_->start_ts);
      session_data_->node_time_cost.push_back(node_time_cost);
      node_time_cost.Clear();
    }
    session_data_ = nullptr;
  }
  int64_t enter_time_ = 0;
  ContextData* session_data_ = nullptr;
  kuaishou::ad::PerfInfo::NodeTimeCost node_time_cost;
};

}  // namespace ad_rank
}  // namespace ks
