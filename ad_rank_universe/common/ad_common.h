#pragma once

#include <string.h>
#include <absl/strings/substitute.h>

#include <algorithm>
#include <cstdint>
#include <functional>
#include <memory>
#include <ostream>
#include <set>
#include <map>
#include <string>
#include <string_view>
#include <type_traits>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/common/basic_types.h"
#include "absl/container/flat_hash_map.h"
#include "glog/logging.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "teams/ad/ad_base/src/common/enum.h"
#include "teams/ad/ad_base/src/common/pod_check.h"
#include "teams/ad/ad_base/src/container/stl_helper.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_trace_common.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/table_extend_fields.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.pb.h"
#include "teams/ad/ad_rank_universe/common/ad_base_data.h"
#include "teams/ad/ad_rank_universe/common/ad_common_constant.h"
#include "teams/ad/ad_rank_universe/common/enum.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/engine_base/kconf/universe_ecpc_info_config.h"
#include "teams/ad/ad_rank_universe/utils/kconf/kconf.h"
#include "teams/ad/engine_base/cmd_curator/ad_base_data.h"
#include "teams/ad/engine_base/biz_common/resource_util.h"
#include "teams/ad/ad_rank_universe/utils/kconf/kconf_data.pb.h"
#include "teams/ad/ad_proto/maven/ad/coupon/ad_coupon_cache.pb.h"
#include "teams/ad/ad_feature_index/service/ad_feature_index_client_schemafree.h"
#include "teams/ad/ad_rank_universe/common/ad_item_attr_accessor.h"

#define ECOM_INDUSTRY_ID 20
#define TX_INDUSTRY_ID 38
#define TX_INDUSTRY_ID_V3 1003
#define COMP_ECOM_INDUSTRY_ID 29
#define ECOM_INDUSTRY_ID_V3 1022
#define COMP_ECOM_INDUSTRY_ID_V3 1032
#define MAX_FILTER_SIZE 1024

namespace kuaishou {
namespace ad {
namespace algorithm {
class CmdItemMapping;
}  // namespace algorithm
}  // namespace ad
}  // namespace kuaishou

namespace ks {
namespace ad_base {
enum AutoCpaBidModifyTagType;
}

const double epsilon = 0.000001;
namespace ad_rank {
extern const char * kDefaultItemTableName;
extern const char * kDefaultItemTableAttrs;
class PsBoundChecker;
using ks::engine_base::PredictType;
using kuaishou::ad::FlowExploreExpInfo;
using kuaishou::ad::ExploreFeature;
//
// checklist:
// schema_uri: Unit 属性
//
// display_info: Creative 属性
// cover_url: Creative 属性
// screenshots: Creative 属性
//
// photo_url: adx 专属
//
// app_name: 优先 p_ad_app, 没有的时候从 p_unit 里面取
// package_name: 优先 p_ad_app, 没有的时候从 p_unit 里面取
// app_icon_url: 优先 p_ad_app, 没有的时候从 p_unit 里面取
// url: 优先 p_ad_app, 没有的时候从 p_unit 里面取
//
// url, secure_url： app, unit

enum RUnifyType {
  CTR = 0,
  CVR = 1,
  DEEP_CVR = 2,
  LTV = 3  // universe add
};

// 注释格式: owner; 添加目的
// 不遵守同学请自己发红包 @cuixiaolong, @sunyuanshuai
enum RUnifyTag {
  UNKNOWN_TAG = 0,
  GAME_APPOINT = 1,  // zhoushuaiyin;
  AD_WATCH_TIMES = 2,  // tangweiqi;
  ISOTONIC_REGRESSION = 3,
  AD_MERCHANT_FOLLOW = 4,  // gaowei03;涨粉系数调整
  EVENT_GOODS_VIEW = 5,  // gaowei03;商品访问系数调整
  EVENT_ORDER_PAIED = 6,  // gaowei03;商品购买系数调整
  AD_LIVE_PLAYED_3S = 7,  // gaowei03;直播 p3s 系数调整
  JK_ORDER_SUBMIT = 8,  // sixianbo; 京快广告预测调整
  AD_WATCH_5_TIMES = 9,  // tangweiqi;观看 5 次
  AD_WATCH_10_TIMES = 10,  // tangweiqi;观看 10 次
  AD_WATCH_20_TIMES = 11,  // tangweiqi;观看 20 次
  AD_CONVERSION_COEFF = 12,   // lijie10; 激活率调整系数
  LPS_ECOM_COEFF = 13,   // zengdi; 直营表单率调整系数
  AD_SCTR_RESET = 14,  // lining; sctr 重调
  MERCHANT_ORDER_PAY = 15,   // fangyuan03; 小店短视频订单支付调整系数
  LPS_TX_COEFF = 16,    // dengjiaxing; 淘系表单率调整系数
  LPS_NOT_ECOM_COEFF = 17,           // dengjiaxing; 非直营表单率调整系数
  AD_REGISTER_COEFF = 18,  // zhaijianwei; 注册率调整系数
  AD_PURCHASE_ECOMM_COEFF = 19,  // xuyanyan; 综合电商付费率调整系数
  AD_COMP_CONVERSION_COEFF = 20,  // zhaijianwei; 电商激活率调整系数
  DPA_APP_INVOKED_COEFF = 21,  // cainingning; DPA 唤端调整系数
  LOWER_C1_REGISTER_CTR_THRESHOLD = 22,  // yudongjin 切一跳注册模型， 做了一个 ctr 门槛。
  LOWER_C1_MERCHANT_FOLLOW_ORDER_PAIED_THRESHOLD = 23,    // yudongjin 涨粉有订单付费门槛，roi 优化
  C1_MERCHANT_FOLLOW_ACCOUNT_RATIO = 24,  // yudongjin 涨粉 account 打折系数
  C1_ORDER_PAIED_ACCOUNT_RATIO = 25,  // yudongjin 订单支付 account 打折系数
  MANUAL_CALIBRATION = 26,  // 手动纠偏
  AD_PURCHASE_COEFF = 27,   // yuchengyuan  行业付费率调整系数
  AD_PURCHASE_APP_COEFF = 28,  // wangxin25; APP 曝光付费率调整系数
  AD_PURCHASE_CONV_COEFF = 29,  // lizhihe; 付费单出价激活付费率调整系数
  DPA_SCVR_COEFF = 30,  // linyuhao03; DPA scvr 模型调整系数
  AD_PURCHASE_APPINVOKED_COEFF = 31,  // lizhihe; 付费单出价唤端付费率调整系数
  MERCHANT_ROAS_COEFF = 32,  // chencongzheng; 小店短视频 roas 调整系数
  AD_24H_STAY_COEF = 33,  // zhangcong05; 激活 24 小时次留双出价 调整系数
  MULTI_RETRIEVAL_CXR_ADJ = 34,  // guojiangwei; 基于 ab 对某个召回通路 cvr 调整系数
  AD_PURCHASE_CONV_COEFF_DOUBLE_COL = 35,  // zhangzhaoyu; 付费单出价激活付费率（双列）调整系数
  LIVE_STREAM_PROMOTE_ORDER_PAIED_COEFF = 36,  // yechen05; 本地推直播直投 cvr 调整系数
  KWAI_PROMOTION_LOCAL_STORE_ORDER_PAIED_COEFF = 37,  // huangxin07; 本地推短视频订单 cvr 调整系数
  SMALL_SCENCE_CALIBRATE = 38,  // liubing05; 小流量场景校准
  PDD_HELP_ITEM_CONV_COEFF = 39,  // huangxin07; 拼多多流量助推 cvr 调整系数
  SPLASH_INNER_CALIBRATE = 40,  // liubing05; 开屏内循 cxr 校准
  NON_MERCHANT_LIVE_P2L_CONV_CVR_COEFF = 41,  // zhangxin29; 外循环行业直播-作品引流-激活类出价 cvr 调整系数
  NON_MERCHANT_LIVE_P2L_LPS_CVR_COEFF = 42,   // zhangxin29; 外循环行业直播-作品引流-表单类出价 cvr 调整系数
  GREDIT_GRANT_COEFF = 43,   // yuchengyuan; 外循环授信目标 cxr 校准
  EFFECTIVE_ACQUISITION_COEFF = 44,    // xuyanyan03; 外循环有效获客出价 cvr 校准
  AD_PURCHASE_IMP2PAY_COEFF = 45,  // zhangzhao06; 曝光付费模型调节系数
  AD_PURCHASE_CONV_ACCOUNT_COEFF = 46,  // lizhihe; 激活付费账户维度预估值调整系数
  AD_PURCHASE_APPINVOKED_ACCOUNT_COEFF = 47,  // lizhihe; 唤端付费账户维度预估值调整系数
  AD_ROAS_CONV_ACCOUNT_COEFF = 48,  // yangxinyong; ROI 单激活链路切分点账户维度预估值调整系数
  AD_ROAS_PURCHASE_ACCOUNT_COEFF = 49,  // yangxinyong; ROI 单付费链路切分点账户维度预估值调整系数
  AD_ROAS_CONV_PRODUCT_COEFF = 50,  // yangxinyong; ROI 单激活链路切分点产品维度预估值调整系数
  AD_ROAS_PURCHASE_PRODUCT_COEFF = 51,  // yangxinyong; ROI 单付费链路切分点产品维度预估值调整系数
  AD_PURCHASE_CLK2PAY_COEFF = 52,  // tiangeng; 短剧点击付费模型调节系数
  COMP_ECOM_CONVERSION_ACCOUNT_PRODUCT_COEFF = 53,  // zengdi; 电商激活产品 && account 粒度预估值调整系数
  COMP_ECOM_INVOKE_ACCOUNT_PRODUCT_COEFF = 54,  // zengdi; 唤端产品 && account 粒度预估值调整系数
  COMP_ECOM_CLICK2_PURCHASE_ACCOUNT_PRODUCT_COEFF = 55,  // zengdi; 综平付费产品 && account 粒度调整系数
  PRIVATE_MESSAGE_CONSULTATION_COEFF = 56,  // xuyanyan03; 私信咨询产品粒度预估值调整系数
  OUTER_HARD_CALI_CONFIG = 57,  // zhangmengxin; 外循环通用目标调节系数
  AD_EVERYDAY_STAY_COEFF = 58,  // xueerpeng; 每日留存账户维度预估值调整系数
  AD_WEEKDAY_STAY_COEFF = 59,  // xueerpeng; 七日留存账户维度预估值调整系数
  OUTER_U2U_CXR_ADJ = 60,  // wuzhibo; u2u 通路预估值调整系数
  WECHAT_GAME_USER_CALIB = 61,  // jiangnan07; 小游戏用户校准
  AD_DUANJU_PURCHASE_RATIO = 62,  // zhangzhaoyu; 短剧付费曝光打平
  GLOBAL_CXR_CALIBRATE = 63  // yishijie; 通用模型纠偏工具
};

template <class T> struct TypeChecker {};
template <> struct TypeChecker<int64_t> { typedef int64_t Type; };
template <> struct TypeChecker<float> { typedef float Type; };

// 用于记录数字变量的变化链.
template <typename T>
class ChangeWatcher {
 public:
  typedef typename TypeChecker<T>::Type Type;

  explicit ChangeWatcher(const char* name):
    head_("[ChangeWatcher] "), is_watch_(false), alert_cnt_(0), name_(name) {
    head_.append(name);
    dedup_set_.clear();
  }
  ChangeWatcher<T>& operator = (const ChangeWatcher& another) {
    min_ = another.min_;
    max_ = another.max_;
    is_watch_ = another.is_watch_;
    is_limit_ = another.is_limit_;
    alert_cnt_ = another.alert_cnt_;
    data_ = another.data_;
    flow_str_ = another.flow_str_;
    return *this;
  }

  struct Records {
    Records(int i, T b, T a): id(i), before(b), after(a) {}
    int id;
    T before;
    T after;
  };

  void Watch() { is_watch_ = true; }

  void AddChange(int id, T before, T after) {
    if (!is_watch_) {
      return;
    }
    if (IsAlert(id, before, after)) {
      ++alert_cnt_;
      data_.emplace_back(id, before, after);
    }
  }
  void PrintAndClear(ks::ad_base::Dot* dot_perf) {
    if (!is_watch_) {
      return;
    }

    std::map<int, int> m;
    if (alert_cnt_ > 0) {
      // 标识本次请求中可能存在调价问题.
      for (auto& record : data_) {
        ++m[record.id];
      }
      if (dot_perf != nullptr) {
        for (auto& pr : m) {
          dot_perf->Count(pr.second, "value_limit", name_, absl::StrCat(pr.first));
        }
      }
      if (is_limit_) {
        // 有实际兜底修改的写 log
        std::string log("!#");
        for (auto& record : data_) {
          // 每个部分的格式: id:before-after|
          log.append(absl::StrCat(record.id, ":", record.before, "~", record.after, "|"));
        }
        LOG(INFO) << head_ << "-" << flow_str_ << " thr[" << min_ << "," << max_ << "] " << log;
      }
    }
    is_watch_ = false;
    alert_cnt_ = 0;
    data_.clear();
    flow_str_.clear();
    dedup_set_.clear();
  }

  void UpdateThr(kuaishou::ad::AdEnum::AdRequestFlowType request_flow_type) {
    is_limit_ = false;
    auto ptr = RankKconfUtil::ChangeWatcherConf();
    if (ptr == nullptr) {
      return;
    }

    auto& conf_pb = ptr->data();
    const auto& group_iter = conf_pb.groups().find(name_);
    if (group_iter == conf_pb.groups().end()) {
      return;
    }
    min_ = group_iter->second.min_thr();
    max_ = group_iter->second.max_thr();
    if (!(group_iter->second.enable())) {
      return;
    }
    is_limit_ = true;

    flow_str_ = std::move(kuaishou::ad::AdEnum_AdRequestFlowType_Name(request_flow_type));
    const auto& flow_iter = group_iter->second.flows().find(flow_str_);
    if (flow_iter != group_iter->second.flows().end()) {
      min_ = flow_iter->second.min_thr();
      max_ = flow_iter->second.max_thr();
    }
  }

  T GetLimitedValue(T value) {
    T ret = is_limit_ ?
      ((value >= min_ && value <= max_) ? value : (value < min_ ? min_ : max_)) : value;
    return ret;
  }

 private:
  bool IsAlert(int id, T before, T after) {
    if (dedup_set_.count(id) == 0) {
      dedup_set_.insert(id);
    } else {
      ks::infra::PerfUtil::CountLogStash(
          1, "ad.ad_rank", "repeated_change_id", name_, absl::StrCat(id));
    }
    ks::infra::PerfUtil::IntervalLogStash(after * 1000, "ad.ad_rank",
        "rank_change_monitor", "after_value", name_, absl::StrCat(id));
    if (after > max_) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_rank",
          "rank_change_monitor", "hit_static_max_limit", name_, absl::StrCat(id));
    }
    if (after < min_) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_rank",
          "rank_change_monitor", "hit_static_min_limit", name_, absl::StrCat(id));
    }
    if (after > max_ || after < min_) {
      // 值超过界限.
      return true;
    }
    return false;
  }

  // TypeChecker 限制了 T 可以比较大小.
  T min_;  // 最小值兜底.
  T max_;  // 最大值兜底.

  std::string head_;
  std::string flow_str_;
  bool is_watch_;
  bool is_limit_;
  uint32_t alert_cnt_;
  std::vector<Records> data_;
  std::string name_;
  std::set<int> dedup_set_;
};

struct RUnifyInfo {
  double value = 0.0;
  double calibrate_value = 0.0;
  std::unordered_set<int32_t> cmd_id_list;
  kuaishou::ad::AdActionType s_type;
  kuaishou::ad::AdActionType e_type;
  RUnifyTag r_tag;
  void Clear();
};
struct UniverseOperationData {
  // 1:顶价, 2:打折
  int32_t operation_type = 0;
  int64_t operation_stra_id = 0;
  int64_t operation_ad_cluster_id = 0;
  int64_t operation_media_cluster_id = 0;

  UniverseOperationData(int32_t type, int64_t stra_id,
                        int64_t ad_cluster_id, int64_t media_cluster_id)
    : operation_type(type), operation_stra_id(stra_id),
      operation_ad_cluster_id(ad_cluster_id),
      operation_media_cluster_id(media_cluster_id) {}

  void clear() {
    memset(this, 0, sizeof(*this));
  }
};

struct PredictScoreMeta {
  double score = 0.0;
  int32 cmd_id;
  int32 use_counter;
  std::string cmd_name;
  bool is_bounded = false;
};

// 调整排序 sort tag
// 如需添加联系 sunyuanshuai
enum SortTag {
  Brand = 0,
  NewGame = 1,
  FIRSTN = 2,
  KPerN = 3,  // 起量工具 N 保 K 策略；heqian, yeziqing
  Retarget = 4,  // 用户在次留等场景下强制 retarget
  MaxTag = 1024
};

// 排序计费相关字段
struct AdPrice : public ad_base::PodDataBase<AdPrice> {
 public:
  int64 rank_benifit = 0;          // 排序 score
  int64 cpm;                       // 每千次曝光收费
  int64 origin_cpm;                // 最初的 ecpm = bid * ctr * cvr [guoyuan03]
  SortTag sort_tag = SortTag::MaxTag;  // 竞价排序阶段的 sort tag
  int64 real_cpm = 0;              // ctr * cvr * auto_cpa_bid
  int64 raw_cpm = 0;               // raw 的 cpm, 考虑 ctr, cvr 校准 但不考虑各种补贴策略的 cpm
  // 联盟是否使用二价计费原则
  bool universe_use_second_price = false;

  int64 GetCpm() const {
    return cpm;
  }

  int64 GetCpmWithoutBonus() const {
    return cpm;
  }

  int64 GetRankBenefit() const {
    return cpm;
  }
};

struct AdBaseNonPod {
  std::string product_name;  // account 里的 product_name
  std::unordered_set<int32_t> pid_tags;  // pid server 标记字段
  std::vector<UniverseOperationData> universe_operation_datas;  // 联盟运营平台数据
  std::vector<int32_t> crowd_tag;  // 广告命中的人群属性
  void Clear();
};

// 广告被过滤数据
struct AdFilterStageInfo {
  int64_t filter_condition;  // 真实的过滤原因
  kuaishou::ad::AdRankNodeType node_stage_type;
  kuaishou::ad::AdRankPointType point_stage_type;
  kuaishou::ad::AdRankPluginType plugin_stage_type;
  void Clear();
};

struct AdxNonPod {
  std::string tag_id;
  bool large_amount_creative;  // 是否是海量创意类型

  void Clear();
};

// 多因子公式信息
struct MultiFactorInfo {
 public:
  MultiFactorInfo(std::string key, bool admit, double value)
      : key(key), admit(admit), value(value) {}
  std::string key{""};
  bool admit{false};
  double value{0.0};
};

struct AdFactorInfo {
  int32_t formula_type = 0;
  int32_t factor_type = 0;
  bool is_hit = false;
  double value = 0.0;
};

// 联盟 ecpc 策略
struct UniverseEcpcInfo {
  int32_t pid_tag = 0;  // 和 ecpc_strategy_type 对应，或者一致
  engine_base::UniverseEcpcStrategyType ecpc_strategy_type =
      engine_base::UniverseEcpcStrategyType::ECPC_STRATEGY_UNKNOWN;
  engine_base::UniverseEcpcOptimizationType optimization_object =
      engine_base::UniverseEcpcOptimizationType::ECPC_OPTIMIZATION_UNKNOWN;
  double target = 0.0;
  double ecpc_unify_input = 0.0;  // 匹配策略包时，这个输入值应该无法确定
  double ratio = 1.0;
  engine_base::UniverseEcpcExpState exp_state =
      engine_base::UniverseEcpcExpState::ECPC_STATE_UNKNOWN;  // 单策略准入中使用
  std::vector<int64_t> crowd_package_list;
  bool is_crowd_package_excluded = false;
  bool is_valid = false;
  int64_t crowd_package_id = 0;
  std::string config_package_id;
  bool has_pdcvr = false;
  bool do_strategy{false};
  void Clear();
};

// 模块内部公用的 ad 结构
// 所有字段必须在 Clear 字段中清理
struct AdCommon {
  // 构造函数对基本类型初始化为默认值
  AdCommon();

  void Clear(ks::ad_base::Dot* dot_perf = nullptr);

#include "ad_common-ad_base_info.inl"  // NOLINT
#include "ad_common-ad_bid_info.inl"  // NOLINT
#include "ad_common-ad_pre_rank.inl"  // NOLINT
#include "ad_common-ad_rank.inl"  // NOLINT
#include "ad_common-ad_statistics.inl"  // NOLINT

  void SetWatcher(kuaishou::ad::AdEnum::AdRequestFlowType request_flow_type);
  void WatchChange();

  bool SetAuctionBid(int64_t new_auction_bid, AuctionBidModifyTag modify_tag);
  bool SetAutoCpaBid(int64_t new_bid, ad_base::AutoCpaBidModifyTagType modify_tag);

  inline bool IsEcomAd() const {
    return is_lingdian() || is_sanfang() || is_yidian();
  }

  // 随心推（ 金牛移动 ）
  inline bool IsMobileMerchant() const {
    return get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE;
  }

  inline bool IsHardAd() const {
    return get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE;
  }

  inline bool is_esp_merchant_t7_roi() const {
    static absl::flat_hash_set<int64_t> account_types{
      kuaishou::ad::AdEnum::ACCOUNT_ESP,
      kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE,
    };
    return get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI &&
           account_types.count(get_account_type()) > 0;
  }

  inline bool IsNativeAd() const {
    return get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE;
  }

  inline bool is_new_model_creative() const {
    if (is_fanstop()) {
      return get_new_creative_tag() > 0;
    }
    return (get_new_creative_tag() &
            static_cast<int>(NewCreative::NewModel)) > 0;
  }

  inline bool is_deep_conv_purchase() const {
    return get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY;
  }

  inline bool is_deep_conv_retention() const {
    return get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY ||
           get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_24H_STAY;
  }

  inline bool esp_new_creative() const {
    return (get_new_creative_tag() & static_cast<int>(NewCreative::NewModel)) > 0 &&
      (IsMobileMerchant() || is_merchant());
  }

  // 零店过滤判断
  inline bool is_lingdian() const {
    return get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
           get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE;
  }
  // 一店过滤判断
  inline bool is_yidian() const {
    return get_industry_parent_id_v3() == 1003 &&
      get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE;
  }
  // 三方过滤判断
  inline bool is_sanfang() const {
    return get_industry_parent_id_v3() == 1002 &&
      get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE;
  }
  // dpa 判断
  inline bool is_dpa() const {
    return get_campaign_type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN &&
        get_campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_DPA ||
        get_dpa_type() == 1;
  }
  // sdpa 判断
  inline bool is_sdpa() const {
    return get_campaign_type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN &&
        get_campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_SDPA ||
        (get_dpa_type() == 2 && get_dpa_industry_id() > 0);
  }

  // 联盟内循环广告判断
  inline bool is_universe_inner_ad() const {
    return is_merchant();
  }

  inline bool is_universe_inner_deep_ad() const {
    return (get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
            get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) &&
        (get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
         get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
         get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS ||
         get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED);
  }

  inline bool is_merchant() const {
    return get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
      get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE;
  }

  inline bool is_ad_merchant_order() const {
    return get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
           get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
  }

  inline bool is_merchant_live() const {
    return get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE;
  }

  inline bool is_reco_roas() const {
    return get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
        (get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
         get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
         get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS);
  }

  // 联盟直投广告
  inline bool is_universe_ads() const {
    return resource_type.is_universe;
  }

  // universe
  inline bool is_universe_opt_ads() const {
    return resource_type.is_universe_opt;
  }

  // 电商直播素材
  inline bool is_merchant_item_live() const {
    return get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE &&
      get_live_creative_type() == kuaishou::ad::AdEnum::LIVE_STREAM_CREATIVE_TYPE;
  }

  // 电商作品引流素材
  inline bool is_merchant_item_p2l() const {
    return get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE &&
      get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE;
  }

  // 电商作品素材
  inline bool is_merchant_item_photo() const {
    return get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE;
  }

  inline bool is_fanstop() const {
    return get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_FANS
        || get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_SHOW
        || get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_ALL
        || get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS
        || get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_TO_SHOW
        || get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL;
  }

  // 素材是否为纯作品形式
  inline bool is_photo() const {
    return get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO ||
           is_merchant_item_photo();
  }

  // 素材是否为作品引流形式, 图片引流开屏专用
  inline bool is_p2l() const {
    return get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO_TO_LIVE ||
           is_merchant_item_p2l() ||
           (get_item_type() == kuaishou::ad::AdEnum::ITEM_PICTURE_TO_LIVE ||
               get_live_creative_type() ==
               kuaishou::ad::AdEnum_LiveCreativeType_PIC_TO_LIVE_STREAM_CREATIVE_TYPE);
  }

  // 素材是否为纯作品或作品引流形式
  inline bool is_photo_or_p2l() const {
    return is_photo() || is_p2l();
  }

  // 素材是否为直投直播形式
  inline bool is_live() const {
    return get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE ||
           is_merchant_item_live();
  }

  // 素材类型是否直投直播
  inline bool is_live_ad() const {
    return get_item_type() == kuaishou::ad::AdEnum::ITEM_LIVE;
  }

  // 素材类型是否为视频
  inline bool is_photo_ad() const {
    return get_item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO;
  }

  // 素材是否为内循环广告
  inline bool is_inner_loop_ad() const {
    return get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE ||
           get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_FANS ||
           get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_SHOW ||
           get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_ALL ||
           get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE ||
           get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS ||
           get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_TO_SHOW ||
           get_campaign_type() == kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL;
  }

  // 素材类型是否为外循环广告
  inline bool is_outer_loop_ad() const {
    return !is_inner_loop_ad();
  }

  // 内循环硬广引流直播
  bool is_amd_photo_to_live() const;

  // 内循环硬广直投直播
  bool is_amd_direct_live() const;

  // 微信小游戏直跳
  bool is_small_game_direct_jump() const;

  // universe
  // 联盟投放类型
  std::string GetUniverseAdPlacementType() const {
    if (is_universe_ads()) {
      return "pt_union";
    } else if (is_universe_opt_ads()) {
      return "pt_opt";
    } else {
      return "pt_kwai";
    }
  }

  inline bool is_deep_bid_twin() const {
    return get_deep_bid_type() == kuaishou::ad::AdEnum::DEEP_TWIN;
  }

  inline bool need_deep_optimization() const {
    return get_deep_bid_type() == kuaishou::ad::AdEnum::DEEP_TWIN ||
        get_deep_bid_type() == kuaishou::ad::AdEnum::DEEP_ECPC;
  }
  inline void SetAdInValid(kuaishou::log::ad::AdTraceFilterCondition condition,
                           const kuaishou::ad::AdRankNodeType ad_node_type,
                           kuaishou::ad::AdRankPointType point_stage_type,
                           kuaishou::ad::AdRankPluginType plugin_stage_type) {
    valid = false;
    ad_filter_stage_info.filter_condition = static_cast<int64_t>(condition);
    ad_filter_stage_info.node_stage_type = ad_node_type;
    ad_filter_stage_info.point_stage_type = point_stage_type;
    ad_filter_stage_info.plugin_stage_type = plugin_stage_type;
  }

  inline void SetAdInValid(int32_t filrer_reason) {
    valid = false;
    ad_filter_stage_info.filter_condition = filrer_reason;
  }

  inline void SetAdInvalid() {
    valid = false;
  }
  // 想要捞回广告
  inline void SetAdValid() {
    valid = true;
  }
  inline bool GetValid() const {
    return valid;
  }

  inline bool is_closure_ad() const {
    static absl::flat_hash_set<int32_t> campaign_types{
      kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE
    };
    static absl::flat_hash_set<int32_t> ocpx_action_types{
      kuaishou::ad::EVENT_ORDER_PAIED,
      kuaishou::ad::AD_MERCHANT_ROAS,
      kuaishou::ad::AD_MERCHANT_T7_ROI,
      kuaishou::ad::AD_STOREWIDE_ROAS
    };
    return campaign_types.count(get_campaign_type()) > 0 &&
           ocpx_action_types.count(get_ocpx_action_type()) > 0;
  }

  inline bool IsTrace() const {
    return is_trace;
  }

  // 搜索广告--是否明投召回
  inline bool is_search_bidword() const {
    return get_is_search_bidword_target();
  }

  inline int64_t get_live_id_for_ps() const {
    return get_live_stream_id() > 0 ? get_live_stream_id() : get_live_stream_id_for_ps();
  }

  double get_ecpc_pid_ratio(const int32_t& pid_tag, double default_pid_ratio = 0.0) {
    if (ecpc_pid_ratio_map.count(pid_tag) <= 0) {
      return default_pid_ratio;
    }
    return ecpc_pid_ratio_map[pid_tag];
  }

  void set_ecpc_pid_ratio(const int32_t& pid_tag, const double& pid_ratio) {
    ecpc_pid_ratio_map[pid_tag] = pid_ratio;
  }

  double get_predict_score(const PredictType& pt) const {
    if (enable_dataframe_field) {
      return PtAttr(pt).GetDoubleValue(attr_row_).value_or(0.0);
    }
    if (!predict_score_list.contains(pt)) {
      return 0.0;
    }
    auto& predict_score = predict_score_list[static_cast<int>(pt)];
    predict_score.use_counter++;
    return predict_score.score;
  }

  int get_predict_cmd_id(const int& pt) {
    if (!predict_score_list.contains(pt)) {
      return 0;
    }
    return predict_score_list[pt].cmd_id;
  }

  void set_predict_score(const int& pt, const double& score, const int32& cmd_id,
      const std::string& cmd_name) {
    auto& predict_score = predict_score_list[pt];
    // 有冲突, 打点 & 打日志
    if (predict_score.cmd_id != 0 && predict_score.cmd_id != cmd_id) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_rank", "predict_type_conflict", std::to_string(cmd_id),
                          std::to_string(predict_score.cmd_id), std::to_string(pt));
      return;
    }
    predict_score.score = score;
    predict_score.cmd_id = cmd_id;
    predict_score.cmd_name = cmd_name;
    // set 不改变计数
    // predict_score.use_counter = 0;
  }

  void FillPredictScoreLog(kuaishou::ad::RankStageInfo::AdExtendInfo* ad_extend_info) {
    for (auto iter = predict_score_list.begin(); iter != predict_score_list.end(); iter++) {
      if (iter->second.use_counter == 0) {
        continue;
      }
      auto* pt_stat = ad_extend_info->add_predict_type_use_stat();
      pt_stat->set_predict_type(iter->first);
      pt_stat->set_use_counter(iter->second.use_counter);
      pt_stat->set_cmd_id(iter->second.cmd_id);
      pt_stat->set_score(iter->second.score);
      pt_stat->set_is_bounded(iter->second.is_bounded);
    }
  }

  bool SkipFilter(int32_t group_enum, int32_t rule_enum);

  // 不重复的一个 id
  uint64_t creative_id() const;
  const std::string item_type() const;
  const int item_type_int() const;
  // ====== 1. 以下为广告的基本属性字段 ======
  bool enable_dataframe_field{false};
  AdBaseNonPod base_np;  // np 字段
  AdxNonPod adx_np;    // adx np 字段
  AdPrice ad_price;    // 排序计费相关字段
  AdFilterStageInfo ad_filter_stage_info;  // 广告过滤原因数据
  int32_t ads_source;  // 0: 表示通过正常请求得到的广告；1: 表示从兜底数据得到的广告
  std::string ad_data_v2{};  // 广告样式数据
  bool enable_unify_rvalue = false;
  kuaishou::ad::AdEnum::AdPriority ad_priority = kuaishou::ad::AdEnum::NORMAL_PRIORITY;  // 混排优先级
  ks::engine_base::ResourceIdType resource_type;
  std::vector<UniverseEcpcInfo> universe_ecpc_infos;
  std::map<int32_t, std::map<int32_t, std::vector<UniverseEcpcInfo>>> universe_ecpc_infos_map;
  std::vector<int64_t> rule_id_list;  // 联盟精排 format model 升级候选样式列表
  std::vector<int64_t> audience_cluster_arr;
  std::vector<int64_t> coupon_config_id_list;  // 券模板 id 候选集
  std::map<int64_t, std::string> universe_explore_multi_records;
  std::map<int64_t, std::string> universe_explore_weight_records;
  std::string universe_explore_ab_tag;
  std::string universe_mcda_hit_tag;
  double universe_mcda_hit_ratio;
  PsBoundChecker* predict_bound_checker;  // context_data 中对象
  int64_t pid_service_key_id = 0;  // pid key
  FlowExploreExpInfo flow_explore_exp_info;  // 流量探索框架落表字段
  ExploreFeature flow_explore_feature_info;  // 流量探索框架特征字段

  std::vector<MultiFactorInfo> factor_info;  // 多因子信息 [ 仅命中白盒时填充 ]
  std::unordered_map<int64_t, std::unordered_map<int64_t, AdFactorInfo>> ad_factor_info;  // 广告策略因子信息
  std::unordered_map<int64_t, double> ad_formula_info;  // 广告策略公式信息

  std::bitset<MAX_FILTER_SIZE> filter_skip_status;

 private:
  bool valid;          // valid 为 false，会被删除
  bool is_trace = true;  // 默认需要记录 trace
  bool enable_skip_cmd_request = false;  // 不请求精排模型
  std::vector<AdCommon*> sub_predict_ad_vec;  // 复用当前 ad 预估的 ad_vec
  RUnifyInfo unify_ctr_info;
  RUnifyInfo unify_cvr_info;
  RUnifyInfo unify_deep_cvr_info;
  RUnifyInfo unify_ltv_info;
  std::map<std::string, double> calibrate_value;
  std::string offcali_cvr_modelstart;
  std::string offcali_cvr_modelend;
  double unify_ecpc = 1.0;  // 统一 ecpc 系数
  std::unordered_set<int32_t> req_cmd_ids;  // 请求的 cmd_id 集合
  std::unordered_set<int32_t> req_cmd_key_ids;  // 请求的 cmd_key_id 集合
  std::vector<int32_t> req_cmd_ids_list;
  std::vector<int32_t> req_cmd_key_ids_list;
  mutable absl::flat_hash_map<int, PredictScoreMeta> predict_score_list;
  mutable absl::flat_hash_map<int, PredictScoreMeta> native_predict_score_list;
  absl::flat_hash_map<int32_t, double> ecpc_pid_ratio_map;

  const AdListAttr *attrs_ = nullptr;
  int  attr_row_ = -1;

  ChangeWatcher<int64_t> auto_cpa_bid_watcher;

 public:
  void SetUnifyCtr(double r,
              kuaishou::ad::AdActionType start,
              kuaishou::ad::AdActionType end,
              int32_t cmd_id_1 = 0,
              int32_t cmd_id_2 = 0) {
    unify_ctr_info.value = r;
    unify_ctr_info.s_type = start;
    unify_ctr_info.e_type = end;
    if (cmd_id_1 != 0) {
      unify_ctr_info.cmd_id_list.insert(cmd_id_1);
    }
    if (cmd_id_2 != 0) {
      unify_ctr_info.cmd_id_list.insert(cmd_id_2);
    }
  }

  void SetUnifyCvr(double r,
              kuaishou::ad::AdActionType start,
              kuaishou::ad::AdActionType end,
              int32_t cmd_id_1 = 0,
              int32_t cmd_id_2 = 0) {
    unify_cvr_info.value = r;
    unify_cvr_info.s_type = start;
    unify_cvr_info.e_type = end;
    if (cmd_id_1 != 0) {
      unify_cvr_info.cmd_id_list.insert(cmd_id_1);
    }
    if (cmd_id_2 != 0) {
      unify_cvr_info.cmd_id_list.insert(cmd_id_2);
    }
  }

  void SetUnifyDeepCvr(double r,
              kuaishou::ad::AdActionType start,
              kuaishou::ad::AdActionType end,
              int32_t cmd_id = 0) {
    unify_deep_cvr_info.value = r;
    unify_deep_cvr_info.s_type = start;
    unify_deep_cvr_info.e_type = end;
    if (cmd_id != 0) {
      unify_deep_cvr_info.cmd_id_list.insert(cmd_id);
    }
  }

  void SetUnifyEcpc(double ratio) {
    unify_ecpc = ratio;
  }

  double PredictBound(const double& ori_score, const std::string& cmd_key,
                      const std::string& product_name) const;

  // universe
  inline void SetUnifyLtv(double r, kuaishou::ad::AdActionType start,
              kuaishou::ad::AdActionType end,
              int32_t cmd_id = 0) {
    unify_ltv_info.value = r;
    unify_ltv_info.s_type = start;
    unify_ltv_info.e_type = end;
    if (cmd_id != 0) {
      unify_ltv_info.cmd_id_list.insert(cmd_id);
    }
  }

  const RUnifyInfo& GetUnifyCtrInfo() const {
    return unify_ctr_info;
  }

  const RUnifyInfo&  GetUnifyCvrInfo() const {
    return unify_cvr_info;
  }

  const RUnifyInfo&  GetUnifyDeepCvrInfo() const {
    return unify_deep_cvr_info;
  }

  const RUnifyInfo &GetUnifyLtvInfo() const {
    return unify_ltv_info;
  }

  const double &GetUnifyEcpc() const {
    return unify_ecpc;
  }

  uint64_t UniqueDragonItemKey() {
    static thread_local uint64_t item_key = 0;
    return ++item_key;
  }

  // 设置 qcpx 优惠券信息
  bool SetQponInfo(
    kuaishou::ad::AdEnum::QponType qpon_type,
    double bid_qpon_ratio,
    double uplift_cvr_ratio,
    double uplift_ctr_ratio,
    kuaishou::ad::AdEnum::QponCostSharingType cost_sharing_type = kuaishou::ad::AdEnum::CUSTOMER_ONLY);

  // 数据 kv 化相关
  ks::platform::ItemAttr& Attr(const ItemIdx &idx) const {
    assert(attrs_ != nullptr); return attrs_->Accessor(idx);
  }
  // 数据 kv 化相关
  ks::platform::ItemAttr& PtAttr(const PredictType& pt) const {
    assert(attrs_ != nullptr); return attrs_->PtAccessor(pt);
  }
  int AttrIndex() const { assert(attr_row_ >= 0); return attr_row_; }
  void SetAttr(const AdListAttr *attrs, bool enable_dataframe = false) {
    attrs_ = attrs;
    attr_row_ = attrs_->AddRow(UniqueDragonItemKey());
    enable_dataframe_field = true;
    // // 1. 必设项目, 兼容提前图化的算子
    // Attr(ItemIdx::ad_common_ptr).SetPtrValue(attr_row_, std::shared_ptr<AdCommon>(this));
  }

  void SetAttr(const AdListAttr *attrs, int64_t item_key, bool enable_dataframe = false) {
    attrs_ = attrs;
    attr_row_ = attrs_->AddRow(item_key);
    enable_dataframe_field = enable_dataframe;
    // // 1. 必设项目, 兼容提前图化的算子
    // Attr(ItemIdx::ad_common_ptr).SetPtrValue(attr_row_, std::shared_ptr<AdCommon>(this));
  }

  bool CheckType(RUnifyType type,
                  kuaishou::ad::AdActionType start,
                  kuaishou::ad::AdActionType end) {
    switch (type) {
      case CTR:
        return (unify_ctr_info.s_type == start && unify_ctr_info.e_type == end);
      break;
      case CVR:
        return (unify_cvr_info.s_type == start && unify_cvr_info.e_type == end);
        break;
      case DEEP_CVR:
        return (unify_deep_cvr_info.s_type == start && unify_deep_cvr_info.e_type == end);
        break;
      case LTV:
        return (unify_ltv_info.s_type == start && unify_ltv_info.e_type == end);
        break;
      default:
        return false;
    }
  }

  void ModifyUnifyInfoLinear(RUnifyType r_type,
                        kuaishou::ad::AdActionType start,
                        kuaishou::ad::AdActionType end,
                        UniverseRUnifyTag r_tag,
                        double k = 1.0, double b = 0.0, double upper = 1.0, double lower = 0.0);

  void addReqCmdIds(int32_t cmd_id) {
    req_cmd_ids.insert(cmd_id);
  }

  void add_cmd_ids(int32_t cmd_id, int32_t cmd_key_id = 0) {
    req_cmd_ids.insert(cmd_id);
    req_cmd_key_ids.insert(cmd_key_id);
    req_cmd_ids_list.push_back(cmd_id);
    req_cmd_key_ids_list.push_back(cmd_key_id);
  }

  const std::vector<int32_t>& getReqCmdIdsList() const {
    return req_cmd_ids_list;
  }
  const std::vector<int32_t>& getReqCmdKeyIdsList() const {
    return req_cmd_key_ids_list;
  }

  const std::unordered_set<int32_t>& getReqCmdIds() const {
    return req_cmd_ids;
  }

  const std::unordered_set<int32_t>& getReqCmdKeyIds() const {
    return req_cmd_key_ids;
  }

  void SetCalibrateValue(const std::map<std::string, double>& score_map) {
    calibrate_value = std::move(score_map);
    return;
  }
  double GetCalibrateValue(const std::string& key) {
    if (calibrate_value.find(key) == calibrate_value.end()) {
      return 0;
    } else {
      return calibrate_value[key];
    }
  }
};

class DegradeManager {
 public:
  // 按 target 和广告来源类型获取截断阈值;
  bool IsOutOfThreshold(
      kuaishou::ad::AdEnum_TargetServerShardType target_type,
      kuaishou::ad::AdSourceType ad_source_type) {
    return data_[MakeKey(target_type, ad_source_type)].IsOutOfThreshold();
  }
  void Add(
      kuaishou::ad::AdEnum_TargetServerShardType target_type,
      kuaishou::ad::AdSourceType ad_source_type) {
    return data_[MakeKey(target_type, ad_source_type)].Add();
  }
  void Clear(ks::ad_base::Dot* dot_perf);

  // 数据记录
  class DegradeCounter {
   public:
    bool IsOutOfThreshold() {
      bool ret = curr_count_ + 1 > threshold_ ? true : false;
      forbid_count_ += ret;
      return ret;
    }
    void Add() { ++curr_count_; }
    void Clear(uint32_t key, ks::ad_base::Dot* dot_perf);

   private:
    void UpdateThreshold();
    uint32_t CalculateThreshold(uint32_t old);
    uint32_t GetTsMinutes() { return ::base::GetTimestamp() / 1000; }
    void BuildNameStrings(uint32_t key);

    static const int TIME_WINDOW_ = 10;  // 时间窗口: 10 分钟.
    uint32_t threshold_ = 2500;
    uint32_t avg_threshold_ = 2500;
    uint32_t forbid_count_ = 0;  // 被降级掉了多少广告.
    uint32_t curr_count_ = 0;  // 留存下来了多少广告.
    uint32_t index_ = 0;  // 指向当前位置的 index
    uint32_t timestamp_[TIME_WINDOW_] = {0};  // 分钟级时间戳,用于确定当前 index;
    uint32_t count_[TIME_WINDOW_] = {0};  // 每分钟的广告总数;
    uint32_t times_[TIME_WINDOW_] = {0};  // 每分钟的请求次数;
    std::string type_;
  };

 private:
  uint32_t MakeKey(
      kuaishou::ad::AdEnum_TargetServerShardType target_type,
      kuaishou::ad::AdSourceType ad_source_type) {
    uint32_t key = static_cast<uint32_t>(target_type);
    return (key << 16) | static_cast<uint16_t>(ad_source_type);
  }

 private:
  std::map<uint32_t, DegradeCounter> data_;
};

// 广告比较器
typedef std::function<bool(const AdCommon*, const AdCommon*)> AdComparator;

// 广告队列结构；不直接使用 std::vector，原因是需要提供快速的删除和排序功能
// 删除广告的话将 AdCommon 的 valid 置为 false，然后调用 Compact
class AdList {
 public:
  using AdComparator = std::function<bool(const AdCommon*, const AdCommon*)>;
  using MemberType = AdCommon*;
  AdList();
  explicit AdList(int64_t list_size);
  ~AdList();

  // 请求级传递流量类型进来.后面如果参数多了就改成传递同个参数结构体的引用.
  void SetRequestInfo(kuaishou::ad::AdEnum::AdRequestFlowType request_flow_type) {
    request_flow_type_ = request_flow_type;
  }

  // 获取一个 AdCommon 变量；广告数超过 kMaxAdCount 时 返回 nullptr
  AdCommon* Append();
  AdCommon* Append(
      kuaishou::ad::AdEnum_TargetServerShardType target_type,
      kuaishou::ad::AdSourceType ad_source_type);
  AdCommon* Append(int64_t item_key, bool enable_dataframe);

  // 删除 valid == false 的广告
  void Compact();

  // 对广告队列排序
  void Sort(const AdComparator& comparator);
  void StableSort(const AdComparator& comparator);

  // 对广告队列随机 shuffle
  void RandomShuffle();
  void RandomShuffleFromIndex(size_t index);

  // 广告队列取 topN；删掉多余的广告；保证调用完成后一定有序
  void TopN(const AdComparator& comparator, size_t n);

  // 清空所有广告数据
  void Clear(ks::ad_base::Dot* dot_perf = nullptr);

  // 元素置换
  void Swap(int a, int b);

  template <typename T>
  void ForEach(T&& handler) {
    std::for_each(ad_list_.begin(), ad_list_.end(), std::forward<T>(handler));
  }

  // 获取所有广告信息
  std::vector<AdCommon*>& Ads() { return ad_list_; }
  const std::vector<AdCommon*>& Ads() const { return ad_list_; }
  int VerifyLosingAd() const;

  int32_t GetInnerLoopAdSize();
  int32_t GetOuterLoopAdSize();

  // 获取没有删除过的广告 list
  std::vector<AdCommon*>& FullAds() { return ad_full_list_; }
  const std::vector<AdCommon*>& FullAds() const { return ad_full_list_; }
  void GetCreativeIds(std::vector<int64_t > *creative_ids) const;

  // 获取第 N 条广告
  // 需要保证访问前，队列做过 Compact；这样队列中不存在 null 的广告
  // i 必须在 [0, Size) 之间，否则返回 nullptr
  AdCommon* At(int i);
  const AdCommon* At(int i) const;

  // 广告队列大小
  size_t Size() const { return ad_list_.size(); }
  void InitAdTable(const AdListAttr *attrs) {
    attrs_ = attrs;
  }

  size_t AdCommonSize() const {
    return ads_.size();
  }

  void PsKeyMapping();

  const std::vector<const AdCommon*>& GetAdCommonByItemId(uint64_t item_id) const;
  const AdCommon* GetFirstAdCommonByItemId(uint64_t item_id) const;

  // 序列化版本, 更改序列化字段时字段值+1
  inline std::string GetAdCommonVersion() const {
    return "5";
  }

  // 为粉条保留，迁移新 cmd 后删除
  uint64_t GetPsKeyByItemId(int64_t item_id) const {
    return item_id;
  }

  // 为粉条保留，迁移新 cmd 后删除
  const AdCommon* GetAdCommonByPsKey(uint64_t ps_key_id) const {
    return GetFirstAdCommonByItemId(ps_key_id);
  }

  void Clone(AdList* other);
  void Swap(AdList* other);

 private:
  const AdListAttr *attrs_ = nullptr;       //
  std::vector<AdCommon> ads_;               // 广告数组
  size_t available_index_ = 0;              // 可以被使用的广告数组下标
  std::vector<AdCommon *> ad_list_;         // 保存广告数据指针，便于排序和取 topN
  std::vector<AdCommon *> ad_full_list_;    // 保存广告数据指针，用于保存全量的数据，不进行删除
  // std::vector<AdCommon *> ad_filter_list_;  // 保存广告数据指针，用于保存被过滤且没有打标的广告
  std::unordered_map<uint64_t, std::vector<const AdCommon *>> item_id_2_ad_common_;
  uint64_t ad_cnt_;
  kuaishou::ad::AdEnum::AdRequestFlowType request_flow_type_;

  DegradeManager down_grade_;
  int32_t inner_ad_full_size_ = -1;
};

}  // namespace ad_rank
}  // namespace ks
