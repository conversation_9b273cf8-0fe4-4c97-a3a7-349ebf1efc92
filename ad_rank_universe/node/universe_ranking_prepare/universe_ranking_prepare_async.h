#pragma once

#include <string>
#include <map>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <utility>

#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.kess.grpc.pb.h"
#include "ks/base/abtest/session_context.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_rank_universe/common/ad_common.h"
#include "teams/ad/ad_rank_universe/common/enum.h"
#include "teams/ad/ad_rank_universe/processor/framework/skydome_node.h"
#include "teams/ad/ad_rank_universe/model/cmd_curator.h"

namespace ks {
namespace ad_rank {
typedef std::shared_ptr<std::map<int64_t, int64_t>> Int64Int64MapSharedPtr;

class UniverseRankingPrepareAsync final
    : public SkydomeAsyncNode
    , public AdNodeItemBase<UniverseRankingPrepareAsync> {
 public:
  UniverseRankingPrepareAsync() : SkydomeAsyncNode(nullptr, kUniverseRankingPrepareAsyncNode) {}
  explicit UniverseRankingPrepareAsync(AdContext &context): UniverseRankingPrepareAsync() {
    SetContext(&context);
  }

  ~UniverseRankingPrepareAsync() {}
  static constexpr AdNodeType class_name() {
    return AdNodeType::UniverseRankingPrepareAsyncType;
  }
  static const char kPredictClientOther[];
  static const char kPredictClientUniverseTiny[];

 protected:
  bool ProcessInner() override;
  bool WaitInner() override;

 private:
  typedef std::unordered_map<int64, PredictResult> ResultMap;

  void Init(ContextData *context);
  CmdCurator* CreateCmdCurator() const;

  // 填充 cxr
  void FillCxrV2(const kuaishou::ad::AdRequest &request);

  // 设定 cxr 阈值上下线
  void CxrProtect(const kuaishou::ad::AdRequest &request);

  // 调用 Universe Predict 服务, 不重试
  bool PredictRpcCall(const kuaishou::ad::algorithm::UniversePredictRequest &universe_predict_request,
                      kuaishou::ad::algorithm::UniversePredictResponse *response);

  void PredictRpcCallAsync(const UniversePredictRequest& universe_predict_request);

  void IssuePredictRequest(const kuaishou::ad::AdRequest& request);

  void AdjustAdxCtrRatio(AdCommon *ad);

  void UniverseInnerCmdCurator(CmdCurator* p_cmd_curator) const;

  void UniverseOuterCmdCurator(CmdCurator* p_cmd_curator) const;

  bool IsAdxLargeCreatives(const AdCommon *ad);

  void TracePredictResultMapSize(int64 predict_result_map_size);

  bool InnerCmdCalibrationStrategy(Int64Int64MapSharedPtr tag_map, AdCommon *ad);

  void PrepareRealTimeItem(
      kuaishou::ad::algorithm::UniversePredictRequest* request);

 private:
  AdList *ad_list_;
  ContextData *session_data_;
  bool enable_universe_outer_u_ctr_ = false;
  bool enable_universe_outer_u_ctcvr_split_20min_in_out_ = false;
  bool enable_universe_outer_u_ctcvr_split_20min_in_out_ensemble_ = false;
  bool enable_universe_high_cpm_model_fusion_ = false;
  double universe_outer_ucvr_bound_ratio_ = 1.0;
  double universe_outer_active_adjust_ratio_ = 1.0;
  ResultMap predict_result_map_;
  int64_t ranking_ps_cost_start_ts_;
  const std::string async_call_tag_ = "universe_ranking_prepare_async_tag";
  std::unique_ptr<CmdCurator> p_cmd_curator_;
  bool is_issue_rpc_;
  kuaishou::ad::algorithm::UniversePredictResponse* ps_response_;
  int64_t query_ps_start_ts_;
  int kess_status_;
  std::atomic_flag wait_once_;
};

}   // namespace ad_rank
}   // namespace ks
