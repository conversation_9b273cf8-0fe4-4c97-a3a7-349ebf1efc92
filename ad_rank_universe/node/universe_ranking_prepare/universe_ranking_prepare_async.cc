#include "teams/ad/ad_rank_universe/node/universe_ranking_prepare/universe_ranking_prepare_async.h"

#include <algorithm>
#include <functional>
#include <memory>
#include <set>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_rank_universe/common/enum.h"
#include "teams/ad/ad_rank_universe/common/ad_common.h"
#include "infra/redis_proxy_client/src/redis_proxy_client/redis_client.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/trace_filter/trace_filter.h"
#include "teams/ad/ad_base/src/util/fill_realtime_user_info.h"
#include "teams/ad/ad_rank_universe/common/context_data.h"
#include "teams/ad/ad_rank_universe/utils/kconf/kconf.h"
#include "teams/ad/ad_rank_universe/utils/utility/utility.h"
#include "teams/ad/ad_base/src/common/multi_retrieval_tag.h"
#include "teams/ad/engine_base/cmd_curator/cmd_config_updater.h"
#include "teams/ad/engine_base/cmd_curator/cmd_register.h"
#include "teams/ad/engine_base/utils/tag_extra_timeout_parser.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/engine_base/spdm/spdm_switches.h"
#include "teams/ad/ad_rank_universe/utils/utility/ksn_util.h"

using ks::ad_base::OptionsFromMilli;
using ks::ad_base::TraceFilterName;
using kuaishou::ad::algorithm::kess::UniversePredictService;
using kuaishou::ad::algorithm::UniversePredictRequest;
using kuaishou::ad::algorithm::UniversePredictResponse;
using kuaishou::ad::AdRequest;
using kuaishou::ad::AdResponse;
using google::protobuf::Arena;
using ks::engine_base::PredictType;

DECLARE_bool(is_universe);

namespace ks {
namespace ad_rank {

using FilterType = kuaishou::log::ad::AdTraceFilterCondition;
const char UniverseRankingPrepareAsync::kPredictClientOther[] = "ranking_prepare_predict_client_other";
const char UniverseRankingPrepareAsync::kPredictClientUniverseTiny[] = "ad-predict-router-universe-tiny";

void UniverseRankingPrepareAsync::Init(ContextData* context) {
  ad_list_ = &(context->ad_list);
  session_data_ = context;
  predict_result_map_.clear();
  enable_universe_outer_u_ctr_ = SPDM_enable_universe_outer_u_ctr(session_data_->spdm_ctx);
  enable_universe_outer_u_ctcvr_split_20min_in_out_ =
             SPDM_enable_universe_outer_u_ctcvr_split_20min_in_out(session_data_->spdm_ctx);
  enable_universe_outer_u_ctcvr_split_20min_in_out_ensemble_ =
             SPDM_enable_universe_outer_u_ctcvr_split_20min_in_out_ensemble(session_data_->spdm_ctx);
  enable_universe_high_cpm_model_fusion_ =
             SPDM_enable_universe_high_cpm_model_fusion(session_data_->spdm_ctx);
  universe_outer_ucvr_bound_ratio_ = SPDM_universe_outer_ucvr_bound_ratio(session_data_->spdm_ctx);
  universe_outer_active_adjust_ratio_ = SPDM_universe_outer_active_adjust_ratio(session_data_->spdm_ctx);
  ranking_ps_cost_start_ts_ = 0;
  p_cmd_curator_.reset(nullptr);
  is_issue_rpc_ = false;
  ps_response_ = nullptr;
  query_ps_start_ts_ = 0;
  kess_status_ = KESS_STATUS_OK;
}

bool UniverseRankingPrepareAsync::ProcessInner() {
  TimeRecorder timer(context_->GetMutableContextData<ContextData>(),
      kuaishou::ad::UNIVERSE_RANKING_PREPARE_ASYNC);

  ContextData *context = context_->GetMutableContextData<ContextData>();
  const AdRequest& request = *(context_->GetRPCRequest<AdRequest>());
  if (KS_UNLIKELY(context == nullptr)) {
    return true;
  }
  auto start_ts = base::GetTimestamp();
  RANK_DOT_COUNT(context, context->ad_list.Size(),
                 "ad_num.ranking_prepare_count", "before");
  // 1. 初始化数据
  Init(context);

  // 2. 异步发起精排请求
  IssuePredictRequest(request);


  RANK_DOT_STATS(session_data_, base::GetTimestamp() - start_ts,
                 "ad_rank.stage_cost_time", AdAsyncNode::GetName(), "ProcessInner");
  wait_once_.clear();
  return true;
}

bool UniverseRankingPrepareAsync::WaitInner() {
  TimeRecorder timer(context_->GetMutableContextData<ContextData>(),
      kuaishou::ad::UNIVERSE_RANKING_PREPARE_ASYNC);
  if (!is_issue_rpc_) {
    return true;
  }
  // 不可重入, 重入就是直接退出
  if (wait_once_.test_and_set(std::memory_order_acquire)) {
    return true;
  }
  // 1.获取 rpc 请求结果
  WAIT_ASYNC_RESULT_WITH_TAG(async_call_tag_);
  auto* p_request = Arena::CreateMessage<UniversePredictRequest>(session_data_->rpc_arena.get());
  UniversePredictRequest& universe_predict_request = *p_request;
  if (kess_status_ != KESS_STATUS_OK || ps_response_->predict_result_v2_size() <= 0) {
    LOG(WARNING) << "llsid: " << session_data_->llsid
                 << " async call rank predict server first try failed: " << kess_status_;
    bool enable_cache_miss_retry = RankKconfUtil::enablePsRankCacheMissRetry();
    if (ps_response_->is_cached() && ps_response_->predict_result_v2_size() <= 0 &&
        enable_cache_miss_retry) {
        return PredictRpcCall(universe_predict_request, ps_response_);
    }
    return false;
  }
  auto start_ts = base::GetTimestamp();
  p_cmd_curator_->FillPredictResultV2(*ps_response_);
  p_cmd_curator_->CmdRequestStatMonitor();
  p_cmd_curator_->CmdResultStatMonitor(*ps_response_);
  session_data_->ranking_ps_cost_ms = (base::GetTimestamp() - ranking_ps_cost_start_ts_) / 1000;
  TracePredictResultMapSize(predict_result_map_.size());

  if (!SPDM_enableFillCxrOpSplit()) {
    // 2.填充广告预估结果
    const AdRequest& request = *(context_->GetRPCRequest<AdRequest>());
    FillCxrV2(request);

    // 3.设置 ctr 阈值上限
    CxrProtect(request);

    session_data_->dot_perf->Count(1, "universe_ranking_fill_cxr", "old");
  }

  RANK_DOT_COUNT(session_data_, session_data_->ad_list.Size(),
                 "ad_num.ranking_prepare_count", "after");
  RANK_DOT_STATS(session_data_, base::GetTimestamp() - start_ts,
                 "ad_rank.stage_cost_time", AdAsyncNode::GetName(), "WaitInner");
  return true;
}

void UniverseRankingPrepareAsync::FillCxrV2(const kuaishou::ad::AdRequest& request) {
  Int64Int64MapSharedPtr universe_cxr_cali_tag_map = RankKconfUtil::universeCxrCaliTagMap();
  // 多路统计
  std::unordered_map<int32_t, int32_t> retr_tag_map;
  int universe_inner_cmd_cxr_cali_count = 0;
  for (auto* p_ad : ad_list_->Ads()) {
    auto& ad = *p_ad;
    const int64 unit_id = ad.get_unit_id();
    const int64 creative_id = ad.get_creative_id();
    retr_tag_map[ad.get_multi_retrieval_tag()]++;
    double high_cvr_origin_cvr_ratio = 1.0;

    ad.set_ctr(ad.get_predict_score(PredictType::PredictType_ctr));
    ad.set_ctr_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_ctr));
    ad.set_cvr(ad.get_predict_score(PredictType::PredictType_cvr));
    ad.set_cvr_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_cvr));
    ad.addReqCmdIds(ad.get_cvr_cmd_id());  //  加入 cmd ID list
    ad.set_app_conversion_rate(ad.get_predict_score(PredictType::PredictType_cvr));
    ad.set_ug_conv_pltv7(ad.get_predict_score(PredictType::PredictType_ug_conv_pltv7));
    ad.set_miniapp_iaa_roas_ltv(ad.get_predict_score(PredictType::PredictType_miniapp_iaa_roas_ltv)); // 小程序 && 首日变现 roi NOLINT
    ad.set_landingpage_submit_rate(ad.get_predict_score(PredictType::PredictType_landingpage_submit));
    ad.set_landingpage_submit_rate_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_landingpage_submit));
    ad.set_retention_rate(ad.get_predict_score(PredictType::PredictType_conv_nextstay));
    ad.set_click_credit_rate(ad.get_predict_score(PredictType::PredictType_click_credit));
    ad.set_deep_credit_rate(ad.get_predict_score(PredictType::PredictType_deep_credit));
    ad.set_click2_lps(ad.get_predict_score(PredictType::PredictType_click2_lps));
    ad.set_click2_lps_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_click2_lps));
    ad.set_purchase_7_day_ltv(ad.get_predict_score(PredictType::PredictType_game_purchase_7_day_ltv));
    ad.set_click_wanjian_rate(ad.get_predict_score(PredictType::PredictType_click_wanjian));
    ad.set_live_audience(ad.get_predict_score(PredictType::PredictType_live_audience));
    ad.set_live_audience_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_live_audience));
    ad.set_conv2_purchase(ad.get_predict_score(PredictType::PredictType_conv2_purchase));
    ad.set_c1_order_paied(ad.get_predict_score(PredictType::PredictType_c1_order_paid));
    ad.set_c2_order_paied(ad.get_predict_score(PredictType::PredictType_c2_order_paid));
    ad.set_c1_order_paied_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_c1_order_paid));
    ad.set_c2_order_paied_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_c2_order_paid));
    ad.set_c1_merchant_follow(ad.get_predict_score(PredictType::PredictType_c1_merchant_follow));
    ad.set_c1_merchant_follow_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_c1_merchant_follow));
    ad.set_conv_nextstay(ad.get_predict_score(PredictType::PredictType_conv_nextstay));
    ad.set_conv_nextstay_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_conv_nextstay));
    ad.set_live_order_paid(ad.get_predict_score(PredictType::PredictType_live_pay_rate));
    ad.set_live_order_paid_cmd_id(ad.get_predict_cmd_id(PredictType::PredictType_live_pay_rate));
    ad.set_ad_merchant_roas_rate(ad.get_predict_score(PredictType::PredictType_merchant_model_ltv_rate));
    ad.set_lps_acquisition(ad.get_predict_score(PredictType::PredictType_lps_acquisition));
    ad.set_key_action_ltv0(ad.get_predict_score(PredictType::PredictType_key_action_ltv0));
    ad.set_reward_origin_landingpage_submit_rate(ad.get_landingpage_submit_rate());
    ad.set_universe_order_submit_rate(ad.get_predict_score(PredictType::PredictType_universe_order_submit_rate));  // NOLINT
    ad.set_universe_appinvoke_nextstay_rate(ad.get_predict_score(PredictType::PredictType_universe_appinvoke_nextstay_rate));   // NOLINT
    ad.set_universe_ecommerce_interest_score(ad.get_predict_score(PredictType::PredictType_universe_ecom_interest_score));  // NOLINT
    ad.set_universe_quality_score(ad.get_predict_score(PredictType::PredictType_universe_quality_score));
    ad.set_univ_non_merchant_live_cvr(ad.get_predict_score(PredictType::PredictType_univ_non_merchant_live_cvr));   // NOLINT
    ad.set_univ_non_merchant_live_deep_cvr(ad.get_predict_score(PredictType::PredictType_univ_non_merchant_live_deep_cvr));   // NOLINT
    ad.set_merchant_ltv(ad.get_predict_score(PredictType::PredictType_merchant_ltv));
    ad.set_conv2_roas_ltv(ad.get_predict_score(PredictType::PredictType_conv2_roas_ltv));
    ad.set_game_conv_ltv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv));
    ad.set_universe_inner_cid_cvr(ad.get_predict_score(PredictType::PredictType_universe_inner_cid_cvr));
    ad.set_universe_inner_cid_gmv(ad.get_predict_score(PredictType::PredictType_universe_inner_cid_gmv));
    ad.set_universe_playlet_iaa_ltv(ad.get_predict_score(PredictType::PredictType_universe_playlet_iaa_ltv));
    ad.set_universe_playlet_iaa_cvr(ad.get_predict_score(PredictType::PredictType_universe_playlet_iaa_cvr));
    // orderpay 两阶段预估值赋值到一阶段，为了兼容策略阈值过滤
    bool is_merchant_reco_promote =
        ad.get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE;
    bool is_c1_order_paid =
        ad.get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
    bool is_merchant_roas =
        ad.get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS;
    if (is_merchant_reco_promote && (is_c1_order_paid || is_merchant_roas)) {
      ad.set_c1_order_paied(ad.get_c2_order_paied() * ad.get_ctr());
    }
    if (enable_universe_outer_u_ctr_ && !enable_universe_outer_u_ctcvr_split_20min_in_out_) {
      // 联盟外循环统一 ctr 激活切分点复写 ctr cvr 值
      double u_ctr = ad.get_predict_score(PredictType::PredictType_outer_u_ctr);
      double u_cvr = ad.get_predict_score(PredictType::PredictType_outer_u_cvr);
      double no_ctcvr = ad.get_predict_score(PredictType::PredictType_outer_u_noctcvr);
      double final_val = u_ctr * u_cvr + no_ctcvr;
      final_val = std::min(std::max(final_val, 0.0), universe_outer_ucvr_bound_ratio_);
      final_val = final_val * universe_outer_active_adjust_ratio_;
      if (u_ctr > 1e-8 && u_cvr > 1e-8) {
        ad.set_ctr(u_ctr);
        ad.set_cvr(final_val);
      }
    }
    if (enable_universe_outer_u_ctcvr_split_20min_in_out_ && !enable_universe_high_cpm_model_fusion_) {
      double ctcvr_p_20_in = ad.get_predict_score(PredictType::PredictType_outer_u_ctcvr_p_in_20min);
      double ctcvr_p_20_out = ad.get_predict_score(PredictType::PredictType_outer_u_ctcvr_p_out_20min);
      double no_ctcvr = ad.get_predict_score(PredictType::PredictType_outer_u_noctcvr);
      double ctcvr_all = ad.get_predict_score(PredictType::PredictType_outer_u_ctcvr_all);
      double final_val = ctcvr_p_20_in + ctcvr_p_20_out + no_ctcvr;
      final_val = std::min(std::max(final_val, 0.0), 1.5 * ctcvr_all);
      final_val = final_val * universe_outer_active_adjust_ratio_;
      if (final_val > 1e-8) {
        ad.set_cvr(final_val);
      }
    }

    if (enable_universe_outer_u_ctcvr_split_20min_in_out_ && enable_universe_high_cpm_model_fusion_) {
      double ctcvr_p_20_in = ad.get_predict_score(PredictType::PredictType_outer_u_ctcvr_p_in_20min);
      double ctcvr_p_20_out = ad.get_predict_score(PredictType::PredictType_outer_u_ctcvr_p_out_20min);
      double no_ctcvr = ad.get_predict_score(PredictType::PredictType_outer_u_noctcvr);
      double ctcvr_all = ad.get_predict_score(PredictType::PredictType_outer_u_ctcvr_all);
      double final_val = ctcvr_p_20_in + ctcvr_p_20_out + no_ctcvr;
      final_val = std::min(std::max(final_val, 0.0), 1.5 * ctcvr_all);
      final_val = final_val * universe_outer_active_adjust_ratio_;
      // 高价值预估率
      double ctcvr_p_20_in_highcpm =
              ad.get_predict_score(PredictType::PredictType_outer_u_ctcvr_p_in_20min_highcpm);
      double ctcvr_p_20_out_highcpm =
              ad.get_predict_score(PredictType::PredictType_outer_u_ctcvr_p_out_20min_highcpm);
      double no_ctcvr_highcpm =
              ad.get_predict_score(PredictType::PredictType_outer_u_noctcvr_highcpm);
      double ctcvr_all_highcpm =
              ad.get_predict_score(PredictType::PredictType_outer_u_ctcvr_all_highcpm);
      double final_val_highcpm =
              ctcvr_p_20_in_highcpm + ctcvr_p_20_out_highcpm + no_ctcvr_highcpm;
      final_val_highcpm = std::min(std::max(final_val_highcpm, 0.0), 1.5 * ctcvr_all_highcpm);
      final_val_highcpm = final_val_highcpm * universe_outer_active_adjust_ratio_;
      if (final_val > 1e-8) {
        high_cvr_origin_cvr_ratio = final_val_highcpm / final_val;
        session_data_->set_high_cvr_origin_cvr_ratio(high_cvr_origin_cvr_ratio);
        ad.set_cvr(final_val);
      }
    }

    if (ad.get_ad_source_type() == kuaishou::ad::ADX) {
      AdjustAdxCtrRatio(&ad);
    }
    if (InnerCmdCalibrationStrategy(universe_cxr_cali_tag_map, &ad)) {
      ++universe_inner_cmd_cxr_cali_count;
    }
  }

  // 内循环校准标签获取监控
  RANK_DOT_COUNT(session_data_, universe_inner_cmd_cxr_cali_count,
    "ad_rank.universe_ranking_prepare_async.universe_inner_cmd_cxr_cali_count");

  // 多路打点监控
  session_data_->DotMultiTag("multi_retr_num_in_rank_prepare", "rank", retr_tag_map);

  // 有过滤广告，需要 compact
  ad_list_->Compact();
}  // NOLINT

bool UniverseRankingPrepareAsync::InnerCmdCalibrationStrategy(Int64Int64MapSharedPtr tag_map, AdCommon *ad) {
  if (tag_map == nullptr) {
    return false;
  }
  bool ret = false;
  if (ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
    if (SPDM_enable_universe_inner_live_cali_fix(session_data_->spdm_ctx)) {
      ad->set_universe_live_audience_cali_tag(ad->get_live_audience_cmd_id());
      ad->set_universe_live_pay_rate_cali_tag(ad->get_live_order_paid_cmd_id());
    } else {
      auto it1 = tag_map->find(ad->get_live_audience_cmd_id());
      if (it1 != tag_map->end()) {
        ad->set_universe_live_audience_cali_tag(it1->second);
        ret = true;
      }
      auto it2 = tag_map->find(ad->get_live_order_paid_cmd_id());
      if (it2 != tag_map->end()) {
        ad->set_universe_live_pay_rate_cali_tag(it2->second);
        ret = true;
      }
    }
  }

  if (ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
    if (ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED
            || ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) {
      ad->set_universe_video_pay_rate_cali_tag(ad->get_c2_order_paied_cmd_id());
    }
  }

  return ret;
}

void UniverseRankingPrepareAsync::TracePredictResultMapSize(int64 predict_result_map_size) {
  ContextData *context =
      context_->GetMutableContextData<ContextData>();
  if (context != nullptr && context->rank_response != nullptr) {
    context->rank_response->set_predict_result_map_size(predict_result_map_size);
  }
  return;
}

bool UniverseRankingPrepareAsync::IsAdxLargeCreatives(const AdCommon *ad) {
  if (ad->get_ad_source_type() == kuaishou::ad::ADX
    && ad->adx_np.large_amount_creative) {
    bool enable_adx_large_creatives_model =
      session_data_->kconf_session_context->TryGetBoolean("enable_adx_large_creatives_model", true);
    if (enable_adx_large_creatives_model) {
      return true;
    }
  }
  return false;
}

void UniverseRankingPrepareAsync::AdjustAdxCtrRatio(AdCommon *ad) {
  if (IsAdxLargeCreatives(ad)) {
    // 打印一下 adx 海量创意的 ctr/cvr 的值，方便监控
    ks::infra::PerfUtil::IntervalLogStash(ad->get_ctr()*10000,
                   "ad.ad_server",
                   "large_adx_ctr",
                   kuaishou::ad::AdxSourceType_Name(ad->get_adx_source_type()));
    ks::infra::PerfUtil::IntervalLogStash(ad->get_cvr()*10000,
                   "ad.ad_server",
                   "large_adx_cvr",
                   kuaishou::ad::AdxSourceType_Name(ad->get_adx_source_type()));
    const auto large_creatives_ctr_ratio =
      session_data_->kconf_session_context->TryGetDouble("large_creatives_ctr_ratio", 0.0);
    const auto large_creatives_cvr_ratio =
      session_data_->kconf_session_context->TryGetDouble("large_creatives_cvr_ratio", 0.0);
    if (large_creatives_ctr_ratio > 0.0) {
      LOG_EVERY_N(INFO, 10000)
          << ",pctr:" << ad->get_ctr()
          << ",large_creatives_ctr_ratio:" << large_creatives_ctr_ratio;
      ad->set_ctr(ad->get_ctr() * large_creatives_ctr_ratio);
    }
    if (large_creatives_cvr_ratio > 0.0) {
      LOG_EVERY_N(INFO, 10000)
        << ",pcvr:" << ad->get_cvr()
        << ",large_creatives_cvr_ratio:" << large_creatives_cvr_ratio;
      ad->set_cvr(ad->get_cvr() * large_creatives_cvr_ratio);
    }
    // 记录精排模型预测结果命中的 ADX 广告数
    RANK_DOT_COUNT(session_data_, 1, "ad_rank.ranking_prepare.after_rank_predict_hit_adx_ad_count",
                   kuaishou::ad::AdEnum_BidType_Name(ad->get_bid_type()),
                   kuaishou::ad::AdxSourceType_Name(ad->get_adx_source_type()));
  }
}

void UniverseRankingPrepareAsync::CxrProtect(const kuaishou::ad::AdRequest& request) {
  static const double default_ctr_max = 1.0;
  static const double default_cvr_max = 1.0;
  static const double default_app_conversion_rate_max = 1.0;
  static const double default_landingpage_submit_rate_max = 1.0;

  for (auto *p_ad : ad_list_->Ads()) {
    auto &ad = *p_ad;
    // 加上限门槛
    ad.set_ctr(std::min(ad.get_ctr(), default_ctr_max));
    ad.set_cvr(std::min(ad.get_cvr(), default_cvr_max));
    ad.set_app_conversion_rate(std::min(ad.get_app_conversion_rate(),
                                           default_app_conversion_rate_max));
    ad.set_landingpage_submit_rate(
        std::min(ad.get_landingpage_submit_rate(),
                 default_landingpage_submit_rate_max));
  }
  ad_list_->Compact();
}


CmdCurator* UniverseRankingPrepareAsync::CreateCmdCurator() const {
  auto *p_cmd_curator = new CmdCurator(AdAsyncNode::GetName());
  if (!p_cmd_curator) {
    return nullptr;
  }

  auto p_cmd_curator_context(std::make_unique<CmdCuratorContext>());
  p_cmd_curator_context->Init(session_data_);
  p_cmd_curator->Reset(std::move(p_cmd_curator_context));

  // 内循环
  if (session_data_->sub_page_id == 19000001) {
    UniverseInnerCmdCurator(p_cmd_curator);
  } else if (!SPDM_enable_tiny_close_inner_cmd(session_data_->spdm_ctx)) {
    UniverseInnerCmdCurator(p_cmd_curator);
  }

  // 外循环
  UniverseOuterCmdCurator(p_cmd_curator);

  return p_cmd_curator;
}

void UniverseRankingPrepareAsync::UniverseInnerCmdCurator(CmdCurator* p_cmd_curator) const {
  // 小店通短视频涨粉
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_c1_merchant_follow,
                     "universe_c1_merchant_follow", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW
        || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST
        || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)
        && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE;
      });
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_ctr, "universe_effective_play_ctr", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_EFFECTIVE_PLAY &&
               p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE;
      });
  // 直播涨粉
  p_cmd_curator->RegisterCmd(
          new CmdWrapper(PredictType::PredictType_c1_merchant_follow,
                         "universe_live_c1_merchant_follow", CMD_SOURCE_AD_DSP),
          [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW
                   || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST
                   || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY)
                   && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE;
          });
  // 小店通短视频 roas
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_merchant_model_ltv_rate,
                     "universe_c1_merchant_roas", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
        || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS)
        && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE;
      });

  // 短视频预估拆分为二段
  p_cmd_curator->RegisterCmd(
          new CmdWrapper(PredictType::PredictType_ctr, "universe_merchant_ctr",
                         CMD_SOURCE_AD_DSP),
          [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
            bool is_c1_order_paid =
                    p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
            bool is_merchant_roas =
                    p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS;
            bool is_storewide_roas =
                    p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS;
            bool is_merchant_reco_promote = p_ad->get_campaign_type() ==
                                            kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE;
            return (is_c1_order_paid || is_merchant_roas || is_storewide_roas)
                   && is_merchant_reco_promote;
          });
  // 短视频 QCPX CVR 建模
  auto compare = [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
    bool is_c1_order_paid =
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
    bool is_merchant_roas =
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS;
    bool is_merchant_reco_promote = p_ad->get_campaign_type() ==
                                    kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE;
    return (is_c1_order_paid || is_merchant_roas) && is_merchant_reco_promote;
  };
  static const std::vector<PredictType> inner_pts = {
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_c1,
    PredictType::PredictType_inner_qcpx_photo_cvr_elastic_c0
  };
  p_cmd_curator->RegisterCmd(new CmdWrapper(inner_pts,
        "universe_merchant_qcpx_cvr_one_model", CMD_SOURCE_AD_DSP), compare);

  p_cmd_curator->RegisterCmd(
          new CmdWrapper(PredictType::PredictType_c2_order_paid, "universe_merchant_c2_paied",
                        CMD_SOURCE_AD_DSP),
          [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
            bool is_c1_order_paid =
                    p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED;
            bool is_merchant_roas =
                    p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS;
            bool is_storewide_roas =
                    p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS;
            bool is_merchant_reco_promote = p_ad->get_campaign_type() ==
                                            kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE;
            return (is_c1_order_paid || is_merchant_roas || is_storewide_roas)
                  && is_merchant_reco_promote;
          });
  // 小店通短视频直播预约
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_ctr, "universe_live_reserve_rate",
                    CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        bool is_live_reserve =
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK;
        bool is_merchant_reco_promote = p_ad->get_campaign_type() ==
                                        kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE;
        return is_live_reserve && is_merchant_reco_promote;
      });
  // 小店短视频引流直播间进人
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_live_audience, "universe_photo2live_live_audience",
                     CMD_SOURCE_AD_DSP),
    [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        bool enable_switch = ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_FAST ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_QUALITY))
          || p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY
          || (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI &&
            (p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP
            || p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE));
        return enable_switch && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
            && p_ad->get_live_creative_type() ==
                kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE;
    });
  // 小店直投直播直播间进人
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_live_audience, "universe_direct_live_audience",
                     CMD_SOURCE_AD_DSP),
    [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        bool enable_switch = ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_FAST ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_QUALITY))
          || p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY
          || (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI &&
            (p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP
            || p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE));
        return enable_switch && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
            && p_ad->get_live_creative_type() ==
                kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE;
    });
  // 小店直投直播直播间进人单独拆分
  if (SPDM_enable_universe_direct_live_audience_split(session_data_->spdm_ctx) &&
      SPDM_enable_universe_direct_live_audience_split_additional(session_data_->spdm_ctx)) {
    p_cmd_curator->RegisterCmd(
        new CmdWrapper(PredictType::PredictType_live_audience, "universe_direct_live_audience_split",
                      CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          bool enable_switch = ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_FAST ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_AUDIENCE_QUALITY));
          return enable_switch && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
              && p_ad->get_live_creative_type() ==
                  kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE
              && p_ad->get_rule_id() == 1;;
      });
  }

  // 小店直播间订单支付
  // 直播 QCPX CVR Uplift 建模
  if (SPDM_enable_universe_qcpx_live_cvr_spline_model(session_data_->spdm_ctx)) {
    static const std::vector<PredictType> live_spline_pts = {
      PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c1,
      PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c2,
      PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c3,
      PredictType::PredictType_inner_qcpx_live_cvr_elastic_bspline_c4
    };
    p_cmd_curator->RegisterCmd(
        new CmdWrapper(live_spline_pts, "universe_live_qcpx_cvr_spline_model",
                        CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          return p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
              && (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED
              || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
              || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS
              || (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI
                && (p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP
                || p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE)));
      });
  } else {
    static const std::vector<PredictType> live_linear_pts = {
      PredictType::PredictType_inner_qcpx_live_cvr_elastic_c1,
      PredictType::PredictType_inner_qcpx_live_cvr_elastic_c0
    };
    p_cmd_curator->RegisterCmd(
        new CmdWrapper(live_linear_pts, "universe_live_qcpx_cvr_one_model",
                        CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
          return p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
              && (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED
              || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS);
      });
  }
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_live_pay_rate, "universe_live_pay_rate",
                      CMD_SOURCE_AD_DSP),
    [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
          && (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
          || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS
          || (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI
            && (p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP
            || p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE)));
    });

  // 小店直播 roas formula
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_merchant_model_ltv_rate,
                     "universe_live_roas_formula_rate", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
            && (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
            || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS
            || (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI
                && (p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP
                || p_ad->get_account_type() ==  kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE)));
    });

  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_universe_inner_cid_cvr,
                     "universe_inner_cid_pay_rate", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE
            && (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS)
            && p_ctx->enable_universe_inner_cid_cvr_;
    });

  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_universe_inner_cid_gmv,
                     "universe_inner_cid_ltv_rate", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE
            && p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS
            && p_ctx->enable_universe_inner_cid_gmv_;
    });

  // 短剧 ltv
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_universe_playlet_iaa_ltv,
                     "universe_ad_dsp_playlet_iaa_ltv", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION
            && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS
            && p_ctx->enable_universe_playlet_iaa_ltv_;
    });

  // 短剧 cvr
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_universe_playlet_iaa_cvr,
                     "universe_ad_dsp_playlet_iaa_cvr", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION
            && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS
            && p_ctx->enable_universe_playlet_iaa_cvr_;
    });
}

void UniverseRankingPrepareAsync::UniverseOuterCmdCurator(CmdCurator* p_cmd_curator) const {
  const std::vector<PredictType> up_model_predict_types = {
      PredictType::PredictType_up_model_no_pay_rate,
      PredictType::PredictType_up_model_low_pay_rate,
      PredictType::PredictType_up_model_medium_pay_rate,
      PredictType::PredictType_up_model_high_pay_rate};
  auto ad_style = session_data_->pos_manager_base.request_imp_infos.size() > 0 ?
                  session_data_->pos_manager_base.request_imp_infos[0].ad_style : 0;

  // ctr 模型
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_ctr, "rank_universe_ctr",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        //  激活已经切一跳 不使用 ctr
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
          return false;
        }
        // 涨粉不使用 ctr
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW
            || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST
            || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY) {
          return false;
        }
        // 订单不使用 ctr
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
          return false;
        }
        // AD_MERCHANT_ROAS
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
            || p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) {
          return false;
        }
        // AD_LIVE_EFFECTIVE_PLAY
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LIVE_EFFECTIVE_PLAY) {
          return false;
        }
        // 唤端不使用 ctr
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
          return false;
        }
        // adx cpm 不使用 ctr
        if (p_ad->get_ad_source_type() == kuaishou::ad::ADX &&
            p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPM) {
          return false;
        }

        // 直播预约不使用 ctr
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK) {
          return false;
        }

        // adx-cpc 广告走独立的 ctr 模型
        if (p_ad->get_ad_source_type() == kuaishou::ad::ADX &&
            p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC) {
          return false;
        }

        return true;
      });
    }

  // adx-cpc ctr 模型
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_ctr, "adx_rank_universe_ctr",
                                         CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      // adx-cpc 广告生效
      if (p_ad->get_ad_source_type() == kuaishou::ad::ADX &&
          p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC) {
        return true;
      }
      // 默认不生效
      return false;
    });

  // 小程序 roi && iaa 首日 roi ltv 模型
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_miniapp_iaa_roas_ltv,
                                        "miniapp_iaa_roas_ltv",
                                        CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP &&
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE);
  });

  // 小程序 iap 激活到内购付费 首日 roi ltv 模型
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_game_conv_ltv,
                                        "miniapp_iap_roas_ltv_new",
                                        CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP &&
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE);
  });

  /* 激活模型 */
  // 一跳到激活模型
  if (!(session_data_->is_universe_tiny_flow || enable_universe_outer_u_ctr_ ||
        enable_universe_outer_u_ctcvr_split_20min_in_out_)) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_cvr,
                                          "rank_universe_imp_conv",
                                          CMD_SOURCE_AD_DSP),
      [ad_style] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // 表单和唤端不使用激活
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
          return false;
        }
        // 电商单拆
        if (p_ad->get_industry_id_v3() == 1032) {
          return false;
        }
        // 内循环广告不使用激活
        if (p_ad->is_merchant()) {
          return false;
        }

        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
          return false;
        }
        return true;
      });
  }

  // 联盟外循环统一 ctr
  if (SPDM_enable_universe_outer_u_ctr(session_data_->spdm_ctx) &&
      !SPDM_enable_universe_outer_u_ctcvr_split_20min_in_out(session_data_->spdm_ctx) &&
      !session_data_->is_universe_tiny_flow) {
    auto compare = [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      // 表单和唤端不使用激活
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
        return false;
      }
      // 电商单拆
      if (p_ad->get_industry_id_v3() == 1032) {
        return false;
      }
      // 内循环广告不使用激活
      if (p_ad->is_merchant()) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
        return false;
      }
      return true;
    };
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_outer_u_ctr,
                                  "universe_outer_u_ctr", CMD_SOURCE_AD_DSP), compare);
    static const std::vector<PredictType> pts = {
      PredictType::PredictType_outer_u_cvr,
      PredictType::PredictType_outer_u_noctcvr};
    p_cmd_curator->RegisterCmd(new CmdWrapper(pts, "universe_conv_cvr_multi", CMD_SOURCE_AD_DSP), compare);
  }
  // 区分 20 分钟内外
  if (SPDM_enable_universe_outer_u_ctcvr_split_20min_in_out(session_data_->spdm_ctx) &&
      !session_data_->is_universe_tiny_flow) {
    auto compare = [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      // 表单和唤端不使用激活
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
        return false;
      }
      // 电商单拆
      if (p_ad->get_industry_id_v3() == 1032) {
        return false;
      }
      // 内循环广告不使用激活
      if (p_ad->is_merchant()) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
        return false;
      }
      return true;
    };
    p_cmd_curator->RegisterCmd(new CmdWrapper(
                    PredictType::PredictType_outer_u_ctcvr_p_in_20min,
                                  "outer_u_ctcvr_p_in_20min", CMD_SOURCE_AD_DSP), compare);
    static const std::vector<PredictType> pts = {
      PredictType::PredictType_outer_u_ctcvr_p_out_20min,
      PredictType::PredictType_outer_u_noctcvr,
      PredictType::PredictType_outer_u_ctcvr_all};
    p_cmd_curator->RegisterCmd(new CmdWrapper(pts,
         "outer_u_ctcvr_p_out_20min_and_noclick_and_all", CMD_SOURCE_AD_DSP), compare);
  }

  if (SPDM_enable_universe_outer_u_ctcvr_split_20min_in_out(session_data_->spdm_ctx) &&
      SPDM_enable_universe_high_cpm_model_fusion(session_data_->spdm_ctx) &&
      !session_data_->is_universe_tiny_flow) {
    auto compare = [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      // 表单和唤端不使用激活
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
        return false;
      }
      // 电商单拆
      if (p_ad->get_industry_id_v3() == 1032) {
        return false;
      }
      // 内循环广告不使用激活
      if (p_ad->is_merchant()) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
        return false;
      }
      return true;
    };

    // 高价值模型参数
    p_cmd_curator->RegisterCmd(new CmdWrapper(
                    PredictType::PredictType_outer_u_ctcvr_p_in_20min_highcpm,
                                  "outer_u_ctcvr_p_in_20min_highcpm", CMD_SOURCE_AD_DSP), compare);
    static const std::vector<PredictType> pts_highcpm = {
      PredictType::PredictType_outer_u_ctcvr_p_out_20min_highcpm,
      PredictType::PredictType_outer_u_noctcvr_highcpm,
      PredictType::PredictType_outer_u_ctcvr_all_highcpm};
    p_cmd_curator->RegisterCmd(new CmdWrapper(pts_highcpm,
         "outer_u_ctcvr_p_out_20min_and_noclick_and_all_highcpm", CMD_SOURCE_AD_DSP), compare);
  }

  // 区分 20 分钟内外
  if (SPDM_enable_universe_outer_u_ctcvr_split_20min_in_out_ensemble(session_data_->spdm_ctx) &&
      !session_data_->is_universe_tiny_flow) {
    auto compare = [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return true;
    };
    static const std::vector<PredictType> pts = {
      PredictType::PredictType_outer_u_ctcvr_p_out_20min_ensemble,
      PredictType::PredictType_outer_u_noctcvr_ensemble,
      PredictType::PredictType_outer_u_ctcvr_all_ensemble};
    p_cmd_curator->RegisterCmd(new CmdWrapper(pts,
         "outer_u_ctcvr_p_out_20min_and_noclick_and_all_ensemble", CMD_SOURCE_AD_DSP), compare);
  }

  // 小系统单拆
  /* 激活模型R*/
  // 一跳到激活模型
  if (session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_cvr,
                                          "rank_universe_imp_conv_tiny",
                                          CMD_SOURCE_AD_DSP),
      [ad_style] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // 表单和唤端不使用激活
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
          return false;
        }
        // 内循环广告不使用激活
        if (p_ad->is_merchant()) {
          return false;
        }
        return true;
      });
  }

  // 一跳到电商激活模型
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_cvr,
                                          "rank_universe_imp_conv_ecom",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        // 表单和唤端不使用激活
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
          return false;
        }
        // ADX CPM CPC 广告不请求激活模型
        if (p_ad->get_ad_source_type() == kuaishou::ad::ADX &&
            (p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPM ||
            p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC)) {
          return false;
        }
        // 电商单拆
        if (p_ad->get_industry_id_v3() == 1032) {
          return true;
        }
        return false;
      });
  }

  // 一跳到表单提交 format model 升级前
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_landingpage_submit,
                                         "universe_imp_lps",
                                         CMD_SOURCE_AD_DSP),
    [this] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION &&
          !p_ctx->enable_fix_leads_submit_conflict_ &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
          p_ad->get_deep_conversion_type() ==
            kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION) {
         return true;
      }
      if (((p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION &&
          !p_ctx->enable_fix_leads_submit_conflict_) ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION) {
            return true;
      }
      return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED);
    });

  // 表单链路增加有效获客
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_lps_acquisition,
                                        "universe_ad_dsp_lps_acquisition",
                                        CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED &&
          p_ad->get_deep_conversion_type() ==
            kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION) {
        return true;
      }
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
          (p_ad->get_deep_conversion_type() ==
            kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION ||
           p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_UNKNOWN)) {
         return true;
      }
      if ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION) {
            return true;
      }
      // 私信目标增加有效获客预估
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
          p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_UNKNOWN) {
            return true;
      }
      return false;
  });

  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_landingpage_submit,
                                        "universe_message_cmd",
                                        CMD_SOURCE_AD_DSP),
    [this] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      // 私信消息
      return p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT &&
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION;
    });

  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_landingpage_submit,
                                        "universe_private_lz_cmd",
                                        CMD_SOURCE_AD_DSP),
    [this] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ctx->enable_fix_leads_submit_conflict_) {
        return p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION &&
               p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION;
      }
      // 私信留资
      return p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT &&
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION;
    });

  // 激活到付费模型:优化激活(开关、账户、产品名配置)、优化付费
  // 同时请求模型，但是用的时候单独用，为了分析
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv2_purchase,
                                          "universe_conv2_purchase",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
              p_ctx->ad_purchase_cold_start_account_product_map->count(p_ad->get_account_id()) > 0) {
          return false;
        }
        return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
                  (p_ad->is_deep_conv_purchase() ||
                  p_ctx->universe_purchase_product_list_->count(p_ad->base_np.product_name))) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
                  p_ad->get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION &&
                  p_ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE &&
                  (p_ad->get_live_creative_type() == kuaishou::ad::AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT ||   // NOLINT
                  p_ad->get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT)) ||  // NOLINT
              (p_ctx->universe_ecpc_adconversion_to_purchase_product_list_->count(p_ad->base_np.product_name) &&   // NOLINT
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
              (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                  (p_ad->get_deep_conversion_type() !=
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) &&
                    (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP ||
                    p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION ||
                    (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_FICTION_PROMOTION &&
                    p_ctx->enable_fiction_conv2_purchase_exp_new_) ||
                    p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->base_np.product_name))) ||       // NOLINT
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES));
      });
  }
  // 付费 ROI 双出价
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv2_purchase,
                                         "universe_conv2_purchase_24h",
                                         CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
       return (true &&
       p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
        p_ad->get_deep_conversion_type() ==
        kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY);
    });
  // 小系统单拆
  // 激活到付费模型:优化激活(开关、账户、产品名配置)、优化付费
  // 同时请求模型，但是用的时候单独用，为了分析
  if (session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv2_purchase,
                                          "universe_conv2_purchase_tiny",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
                (p_ad->is_deep_conv_purchase() ||
                p_ctx->universe_purchase_product_list_->count(p_ad->base_np.product_name))) ||
                (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
                p_ad->get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) ||
                (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION &&
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE &&
              p_ad->get_live_creative_type() == kuaishou::ad::AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) ||
                (
              p_ctx->universe_ecpc_adconversion_to_purchase_product_list_->count(p_ad->base_np.product_name) &&     // NOLINT
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
                (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                  (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP ||
                    p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->base_np.product_name))) ||     // NOLINT
                  p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
                  p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) &&
                !(false && p_ad->is_new_model_creative()));
      });
  }

  // 唤端请求付费模型，用于 ecpc 中唤端优化付费
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv2_purchase,
                                          "universe_appinvoked2_purchase",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ctx->universe_ecpc_appinvoked_to_purchase_product_list_->count(p_ad->base_np.product_name) &&     // NOLINT
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
                p_ctx->universe_mini_app_roas_invoked_account_map_->count(p_ad->get_account_id()) ||
                p_ctx->universe_mini_app_roas_invoked_product_map_->count(p_ad->base_np.product_name) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) &&
                (p_ctx->universe_app_invoked_purchase_account_white_list_->count(p_ad->get_account_id()) ||
                p_ctx->universe_app_invoked_purchase_product_white_list_->count(p_ad->base_np.product_name)));      // NOLINT
      });
  }
  // 小系统单拆
  // 唤端请求付费模型，用于 ecpc 中唤端优化付费
  if (session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv2_purchase,
                                          "universe_appinvoked2_purchase_tiny",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ctx->universe_ecpc_appinvoked_to_purchase_product_list_->count(p_ad->base_np.product_name) &&     // NOLINT
                p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) ||
                p_ctx->universe_mini_app_roas_invoked_account_map_->count(p_ad->get_account_id()) ||
                p_ctx->universe_mini_app_roas_invoked_product_map_->count(p_ad->base_np.product_name) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) &&
                (p_ctx->universe_app_invoked_purchase_account_white_list_->count(p_ad->get_account_id()) ||
                p_ctx->universe_app_invoked_purchase_product_white_list_->count(p_ad->base_np.product_name)));
      });
  }

  // 注册
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click2_deep_rate,
                                          "universe_event_register",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ad->get_ocpx_action_type() ==
                kuaishou::ad::EVENT_REGISTER ||
                p_ctx->universe_register_product_list_->count(p_ad->base_np.product_name));
      });
  }

  // 一跳到换端
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click_app_invoked,
                                          "universe_click_app_invoked",
                                          CMD_SOURCE_AD_DSP),
      [this] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED ||
                (p_ctx->universe_mini_app_roas_invoked_account_map_->count(p_ad->get_account_id()) ||
                  p_ctx->universe_mini_app_roas_invoked_product_map_->count(p_ad->base_np.product_name)) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) &&
                (p_ctx->universe_app_invoked_purchase_account_white_list_->count(p_ad->get_account_id()) ||
                p_ctx->universe_app_invoked_purchase_product_white_list_->count(p_ad->base_np.product_name))));     // NOLINT
      });
  }
  // 小系统单拆
  // 一跳到换端
  if (session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click_app_invoked,
                                          "universe_click_app_invoked_tiny",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED ||
                (p_ctx->universe_mini_app_roas_invoked_account_map_->count(p_ad->get_account_id()) ||
                  p_ctx->universe_mini_app_roas_invoked_product_map_->count(p_ad->base_np.product_name)) ||
              (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) &&
                (p_ctx->universe_app_invoked_purchase_account_white_list_->count(p_ad->get_account_id()) ||
                p_ctx->universe_app_invoked_purchase_product_white_list_->count(p_ad->base_np.product_name)));
      });
  }
  // 激活次留
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_nextstay,
                                          "universe_conv_retention",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
              (p_ad->is_deep_conv_retention() ||
                p_ctx->retention_account_list_->count(p_ad->get_account_id()) ||
                p_ctx->universe_retention_product_list_->count(p_ad->base_np.product_name)) &&
                (!p_ctx->universe_retention_product_model_white_list_->count(p_ad->base_np.product_name)))
              || p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY;
      });
  }
  // 小系统单拆
  // 激活次留
  if (session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_nextstay,
                                          "universe_conv_retention_tiny",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
              (p_ad->is_deep_conv_retention() ||
                p_ctx->retention_account_list_->count(p_ad->get_account_id()) ||
                p_ctx->universe_retention_product_list_->count(p_ad->base_np.product_name)) &&
                (!p_ctx->universe_retention_product_model_white_list_->count(p_ad->base_np.product_name)))
              || p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY;
      });
  }
  // 激活次留_白名单产品
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_nextstay,
                                          "universe_conv_retention_white_product",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
              (p_ad->is_deep_conv_retention() ||
                p_ctx->retention_account_list_->count(p_ad->get_account_id()) ||
                p_ctx->universe_retention_product_list_->count(p_ad->base_np.product_name)) &&
                p_ctx->universe_retention_product_model_white_list_->count(p_ad->base_np.product_name);
      });
  }

  // 激活次留_关键行为出价
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_nextstay,
                                          "universe_conv_retention_keyaction",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        return (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) &&
              p_ctx->universe_keyaction_retention_product_list_->count(p_ad->base_np.product_name);
      });
  }

  // ecpc 激活次留优化七留请求七留模型
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_7_day_stay,
                                          "universe_conv2_week_stay",
                                          CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ctx->enable_universe_conv_7stay_ &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
          p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_WEEK_STAY) {
          LOG_EVERY_N(INFO, 10000) << "[zhangcong05] universe_conv2_week_stay cmd is enabled";
          return true;
      }
      return  p_ctx->universe_ecpc_nextstay_to_weekstay_product_list_->count(p_ad->base_np.product_name) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
              p_ad->get_deep_conversion_type() ==  kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY;
    });
  }
  // ecpc 激活次留优化七留请求七留模型小系统
  if (session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_7_day_stay,
                                          "universe_conv2_week_stay_tiny",
                                          CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return  p_ctx->universe_ecpc_nextstay_to_weekstay_product_list_->count(p_ad->base_np.product_name) &&
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION &&
              p_ad->get_deep_conversion_type() ==  kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY;
    });
  }
  // 表单付费模型
  p_cmd_curator->RegisterCmd(new CmdWrapper(
          PredictType::PredictType_click2_purchase_rate_single_bid,
          "universe_lps_click2_purchase_single_bid",
          CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_CID ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LANDING_PAGE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) &&
              !(p_ctx->fiction_app_conversion_purchase_product_map_->count(p_ad->base_np.product_name));
  });

  // ROI 双出价的 ltv 模型
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_game_purchase_ltv,
                                          "ad_universe_twin_purchase_ltv",
                                          CMD_SOURCE_AD_DSP),
      [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY
            || (p_ctx->universe_ecpc_purchase_to_ltv_product_list_->count(p_ad->base_np.product_name) &&
                p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE);
    });
  }

  // ROI 单出价 ltv 模型
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_game_purchase_ltv,
                                         "ad_universe_game_purchase_ltv",
                                         CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS;
    });

  // 7 日 ROI 单出价 ltv 模型 by zengdi 2021/11/28
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_game_purchase_7_day_ltv,
                                         "ad_universe_game_purchase_7_day_ltv",
                                         CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) ||
      (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
          p_ctx->universe_ecpc_ltv_to_weekltv_product_list_->count(p_ad->base_np.product_name));
    });

  // 7 日付费次数模型
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_7_day_pay_times,
                                         "ad_universe_pay_times",
                                         CMD_SOURCE_AD_DSP),
                          [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
                            return p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES;
                          });

  // 联盟激活关键行为模型
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_key_inapp_action_rate,
                                         "universe_conv_key_inapp_action",
                                         CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      return p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION;
    });

  // 联盟订单提交模型
  p_cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_universe_order_submit_rate,
                    "universe_click_order_submit_v2_cmd",
                    CMD_SOURCE_AD_DSP),
  [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT) {
      return true;
    } else {
      return false;
    }
  });

  // 联盟唤端次留模型
  if (!session_data_->is_universe_tiny_flow) {
    p_cmd_curator->RegisterCmd(
        new CmdWrapper(PredictType::PredictType_universe_appinvoke_nextstay_rate,
                      "universe_appinvoke_nextstay_rate", CMD_SOURCE_AD_DSP),
        [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED &&
            ((p_ad->get_deep_conversion_type()
                == kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY) ||
              (p_ctx->universe_ecpc_app_invoke_to_next_stay_product_list_ != nullptr &&
              p_ctx->universe_ecpc_app_invoke_to_next_stay_product_list_->find(
              p_ad->base_np.product_name) !=
                p_ctx->universe_ecpc_app_invoke_to_next_stay_product_list_->end()))) {
          return true;
        } else {
          return false;
        }
      });
  }
  // 小系统单拆
  // 联盟唤端次留模型
  if (session_data_->is_universe_tiny_flow) {
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_universe_appinvoke_nextstay_rate,
                     "universe_appinvoke_nextstay_rate_tiny", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED &&
          ((p_ad->get_deep_conversion_type()
              == kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY) ||
            (p_ctx->universe_ecpc_app_invoke_to_next_stay_product_list_ != nullptr &&
             p_ctx->universe_ecpc_app_invoke_to_next_stay_product_list_->find(
             p_ad->base_np.product_name) !=
              p_ctx->universe_ecpc_app_invoke_to_next_stay_product_list_->end()))) {
        return true;
      } else {
        return false;
      }
     });
  }

  // 联盟外循环直播流模型
  // 联盟外循环直播流进人模型
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_live_audience,
                     "universe_outer_audience_rate", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() == kuaishou::ad::AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE)) {
        return true;
      } else {
        return false;
      }
     });
  // 联盟外循环直播流激活类 cvr 模型
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_univ_non_merchant_live_cvr,
                     "ad_univ_live_click_conv_live", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() == kuaishou::ad::AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS)) {
        return true;
      } else {
        return false;
      }
     });
  // 联盟外循环短视频流模型
  // 联盟外循环短视频引流进人模型
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_live_audience,
                     "universe_ad_dsp_item_imp_click_photo", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE)) {
        return true;
      } else {
        return false;
      }
     });
  // 联盟外循环短视频引流直播流激活类 cvr 模型
  p_cmd_curator->RegisterCmd(
      new CmdWrapper(PredictType::PredictType_univ_non_merchant_live_cvr,
                     "uni_ad_dsp_item_click_conv_photo", CMD_SOURCE_AD_DSP),
      [](const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS)) {
        return true;
      } else {
        return false;
      }
     });
  // 联盟订单提交 ROI-电商下单推广
  p_cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_merchant_ltv,
                                         "universe_cid_roi_imp_gmv_cmd_fix",
                                         CMD_SOURCE_AD_DSP),
    [] (const AdCommon *p_ad, CmdCuratorContext *p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_CID &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CID_ROAS) {
        return true;
      } else {
        return false;
      }
    });
}  // NOLINT

void UniverseRankingPrepareAsync::IssuePredictRequest(const kuaishou::ad::AdRequest& request) {
  // 没有广告，不预估
  if (ad_list_->Size() <= 0) return;

  ranking_ps_cost_start_ts_ = base::GetTimestamp();

  // 构造 Predict request, 填充基本参数
  auto* p_request = Arena::CreateMessage<UniversePredictRequest>(session_data_->rpc_arena.get());
  UniversePredictRequest& universe_predict_request = *p_request;

  universe_predict_request.set_llsid(session_data_->llsid);
  universe_predict_request.set_user_id(request.ad_user_info().id());
  universe_predict_request.mutable_ad_user_info()->set_device_id(request.ad_user_info().device_id());
  universe_predict_request.set_item_type(kuaishou::ad::algorithm::ItemType::AD_DSP);
  universe_predict_request.set_app_id(session_data_->pos_manager_base.GetRequestAppId());
  universe_predict_request.set_qa_tag(ks::ad_rank::NoDiffPsQaTag::UNIVERSE_PS_TAG);
  ad_base::util::FillRealtimeUserInfo(request.ad_user_info(),
                                      universe_predict_request.mutable_ad_user_info());

  if (session_data_->kconf_session_context->TryGetBoolean("enable_reco_user_info_cmp", false)) {
    request.reco_user_info().SerializeToString(
        universe_predict_request.mutable_serialized_reco_user_info());
    session_data_->dot_perf->Count(1, "ad_rank.reco_user_info_check",
          universe_predict_request.mutable_serialized_reco_user_info()->empty() ? "empty" : "not_empty",
          kuaishou::ad::AdEnum_AdRequestFlowType_Name(session_data_->pos_manager_base.GetAdRequestType()));
  }
  // 未登陆标志 & ps fake user info 开关
  universe_predict_request.mutable_ad_user_info()->set_is_unlogin_user(session_data_->is_unlogin_user);
  // 珍珠港打开 ps user info fake 开关
  universe_predict_request.set_enable_fake_user_info(true);

  // 联盟为 adx-cpc 广告开启 ctr 预估
  PrepareRealTimeItem(&universe_predict_request);

  // 构造命令字
  session_data_->query_ps_times = 1;
  p_cmd_curator_ = std::unique_ptr<CmdCurator>(CreateCmdCurator());
  if (p_cmd_curator_.get() == nullptr) {
    return;
  }
  if (p_cmd_curator_->PrepareCmds(session_data_, AdAsyncNode::GetName()) < 0) {
    LOG(ERROR) << "cmd curator ranking prepare cmd error!";
    return;
  }

  int merchant_count = 0;
  int64_t accept_start_ts = base::GetTimestamp();
  for (size_t i = 0; i < session_data_->ad_list.Size(); ++i) {
    AdCommon* p_ad = session_data_->ad_list.At(i);
    if (p_ad == nullptr) {
      continue;
    }
    if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
      merchant_count++;
    }
    p_cmd_curator_->Accept(p_ad, p_ad->creative_id());
  }

  RANK_DOT_STATS(session_data_, base::GetTimestamp() - accept_start_ts,
                 "cmd", AdAsyncNode::GetName(), "accept_all_creative_use_ts");
  RANK_DOT_COUNT(session_data_, merchant_count, "ad_server.ranking_prepare_merchant_count");
  if (p_cmd_curator_->FillPredictRequest(AdAsyncNode::GetName(), &universe_predict_request,
                                        kuaishou::ad::algorithm::RequestScene::REQ_SCENE_UNIVERSE,
                                        true) < 0) {
    LOG(WARNING) << "FillPredictRequest failed";
    return;
  }

  utility::FillRequestContextInfo(*session_data_, request, &universe_predict_request);
  if (!SPDM_enable_close_rank_cmds_feature(session_data_->spdm_ctx)) {
    utility::AddCmdContextInfoCommonAttr(&universe_predict_request);
  }
  utility::CopyDataFromPsRequest(*session_data_, universe_predict_request);

  for (const auto& cmd : universe_predict_request.cmd()) {
    session_data_->rank_cmds.push_back(cmd);
  }
  PredictRpcCallAsync(universe_predict_request);
}

void UniverseRankingPrepareAsync::PredictRpcCallAsync(
    const UniversePredictRequest& universe_predict_request) {
  ps_response_ = Arena::CreateMessage<UniversePredictResponse>(session_data_->rpc_arena.get());
  std::string kess_name = kPredictClientOther;
  if (session_data_->is_universe_tiny_flow) {
    kess_name = kPredictClientUniverseTiny;
  }
  int status = KESS_STATUS_OK;
  auto client = ks::ad_base::AdKessClient::ClientOfKey<UniversePredictService>(kess_name);
  if (!client.first || !client.second) {
    status = NOT_FOUND;
  } else {
    is_issue_rpc_ = true;
    ps_response_->clear_is_cached();
    int32_t predict_server_time_out_ms;
    if (session_data_->is_universe_tiny_flow) {
      predict_server_time_out_ms = RankKconfUtil::predictServerTinyTimeoutMills();
    } else {
      predict_server_time_out_ms = RankKconfUtil::predictServerTimeoutMills();
    }

    std::string hash_str;
    bool select_one{false};
    auto key_id = session_data_->rank_request->ad_request().request_hash().key_id();
    if (key_id != 0) {
        hash_str = absl::StrCat(key_id, "PsUserHash");
    } else {
        select_one = true;
    }
    auto hash = base::CityHash64(hash_str.c_str(), hash_str.length());
    auto stub = select_one ? client.second->SelectOne() : client.second->SelectByHash(hash);
    std::function<void(const UniversePredictResponse& resp, int status)> callback =
        [this](const UniversePredictResponse& resp, int status){
        *(this->ps_response_) = resp;
        this->kess_status_ = status;
        RANK_DOT_STATS(session_data_, base::GetTimestamp() - query_ps_start_ts_,
                        "cmd", AdAsyncNode::GetName(), "query_ps_use_ts");
    };
    query_ps_start_ts_ = base::GetTimestamp();
    AD_HALF_ASYNC_KESS_CALL_WITH_STUB(UniversePredictService,
                                      Predict,
                                      UniversePredictResponse,
                                      universe_predict_request,
                                      callback,
                                      predict_server_time_out_ms,
                                      stub,
                                      async_call_tag_);
  }
  kess_status_ = status;
}

bool UniverseRankingPrepareAsync::PredictRpcCall(
    const UniversePredictRequest& universe_predict_request,
    UniversePredictResponse* response) {
  std::string kess_name = kPredictClientOther;
  if (session_data_->is_universe_tiny_flow) {
    kess_name = kPredictClientUniverseTiny;
  }
  auto client = ks::ad_base::AdKessClient::ClientOfKey<UniversePredictService>(kess_name);
  response->clear_is_cached();
  auto &universe_predict_response = *response;
  int32_t predict_server_time_out_ms = RankKconfUtil::predictServerTimeoutMills();

  std::string hash_str;
  bool select_one{false};
  auto key_id = session_data_->rank_request->ad_request().request_hash().key_id();
  if (key_id != 0) {
    hash_str = absl::StrCat(key_id, "PsUserHash");
  } else {
    select_one = true;
  }
  auto hash = base::CityHash64(hash_str.c_str(), hash_str.length());
  auto start_ts = base::GetTimestamp();
  auto status = (select_one ? client.second->SelectOne() : client.second->SelectByHash(hash))
                ->Predict(OptionsFromMilli(predict_server_time_out_ms),
                                           universe_predict_request,
                                           &universe_predict_response);
  auto end_ts = base::GetTimestamp();

  auto predict_cost = (end_ts - start_ts) / 1000;

  utility::RecordNodeProcessTime("ranking_ps_cost",
                                  predict_cost,
                                  session_data_->ps_is_timeout,
                                  session_data_->rank_response);

  LOG_EVERY_N(INFO, 10000) << "llsid:" << session_data_->llsid
                         << " predict_cost:" << predict_cost
                         << " req_count:" << ad_list_->Size()
                         << " res_count:"
                         << universe_predict_response.predict_result_size()
                         << " universe_predict_response.status:"
                         << universe_predict_response.status()
                         << " user_id:" << universe_predict_request.user_id()
                         << " status:" << status.error_message();

  if (!status.ok()
      || universe_predict_response.status() != kuaishou::ad::algorithm::STATUS_OK) {
    return false;
  }

  return true;
}

void UniverseRankingPrepareAsync::PrepareRealTimeItem(UniversePredictRequest* request) {
  // 未开启，返回
  if (!RankKconfUtil::enableRealItemPs()) {
    return;
  }
  // 针对所有的海量创意
  auto* real_time_item = request->add_real_time_item();
  auto black_real_creative_id = RankKconfUtil::blackRealItemIdSet();
  for (auto& p_ad : ad_list_->Ads()) {
    if (p_ad->adx_np.large_amount_creative && p_ad->get_creative_id() > 0) {
      if (black_real_creative_id->find(p_ad->get_creative_id()) == black_real_creative_id->end()) {
        request->add_real_time_item_id(p_ad->get_creative_id());
      }
    }

    if (p_ad->get_ad_source_type() == kuaishou::ad::ADX &&
       (!p_ad->adx_np.large_amount_creative) && p_ad->get_creative_id() > 0) {
      real_time_item->set_source_identifier("adx");
      real_time_item->add_item_id(p_ad->get_creative_id());
    }
  }
}

}  // namespace ad_rank
}  // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, UniverseRankingPrepareAsync, ::ks::ad_rank::UniverseRankingPrepareAsync);
