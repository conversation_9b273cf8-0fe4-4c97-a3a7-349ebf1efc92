#!/usr/bin/env python3
# coding=utf-8
import os
import sys

dragon_path = os.path.abspath('../../../../dragon/')
ad_dragon_path = os.path.abspath('../../ad_dragon/')
dragon_exts_path = os.path.abspath('../../dragon_exts/')
sys.path.append(dragon_path)
sys.path.append(ad_dragon_path)
sys.path.append(dragon_exts_path)
sys.path.append('../../../../')
os.environ["CHECK_TALBE_DEPENDENCY"] = "true"

from ad_dragonfly.base.formula1.formula_mixin import AdFormulaApiMix
import config.adrank_config as adrank_config
import schema_manager.common_attr_schema as common_attr_schema
import schema_manager.item_attr_schema as item_attr_schema
from schema_manager.feature_item_attr_schema import *
from ad_rank_dragonfly.ad_api_mixin import *
from dragonfly.modular.module import module
from dragonfly.visualization.dag import *
from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource, current_flow
from dragonfly.modular.data_manager import data_manager, ab_param as ab, kconf_param as kconf
from dragonfly.ext.ad_model.ad_model_mixin import AdModelApiMixin
from dragonfly.ext.ad_router.ad_router_mixin import AdRouterApiMixin
from teams.ad.grid.remote_table.remote_table_mixin import RemoteGridMixin
from module.enum.formula_type_enum import get_enum, Formula
from module.utils.proto_utils import ProtoGuard
from module.formula.rb_formula import RBFormula
# Request type enum
RequestType_default = 1
RequestType_universe_flow = 5

# pb 文件相对路径
PROCESSOR_PB_PATH = "../processor/proto/filter_enum.proto"
FILTER_REASON_PB_PATH = "../../ad_proto/kuaishou/ad/ad_trace_filter_condition.proto"


class AdRankServerFlow(LeafFlow, AdFormulaApiMix, RBFormula, AdRankMixin, RemoteGridMixin, AdModelApiMixin, AdRouterApiMixin):
  def __init__(self, name, item_table):
    LeafFlow.__init__(self, name=name, item_table=item_table)

  def prepare(self):
    """
    pslab 处理前
    """
    self.ad_rank_process_prepare()
    return self

  @module()
  def prepare_request(self, name):
    with data_manager:
      current_flow() \
    .common_switch_mock_mixer() \
    .parse_request()  \
    .universe_build_df()
    return self

  def resp_mock(self):
    """
    请求后处理部分
    """
    self.ad_rank_observe_mixer()
    return self

  @module()
  def post_proc(self, name):
    self.rank_server_post_proc() \
    .resp_mock()
    return self


  @module()
  def ad_rank_extdata_process(self, name):
    self.if_("sub_page_id ~= 19000002") \
      .request_remote_table_opt(
        **feature_index_table,
        no_check=True
      ).item_attr_copy_to_common_attr(
        item_table = "pv_item_table",
        copy_schema = feature_index_table["request_tables"]["pv_item_table"]["remote_table_relation"]["wt_universe_position"]["local_field_alias"]
      ) \
    .end_if_() \
    .get_kconf_params(   # 取kconf配置数据
      kconf_configs=[
        dict(kconf_key="ad.adFrontDiffSwitches.enableUniverseAdRouterExp",
           value_type="bool",
           export_common_attr = "enableUniverseAdRouterExp"
        )
      ]
    ) \
    .if_("enableUniverseAdRouterExp == 1 and enable_universe_get_router_user_info == 1") \
      .universe_rank_ad_router_request_prepare(
        item_table_name = "ad_list_item_table",
        arena_attr_name = 'ad_router_predict_arena',
        predict_request_attr_name = "universe_origin_router_request"
      ).ad_router_bs_trans_enricher(
        no_check=True,
        save_async_status_to = "bs_trans_async_status"
      ).universe_rank_cmd_manager_mixer() \
      .model_register(
        register_table = [
          dict(
            item_table_name = "ad_list_item_table",
            cmd_table_name = "universe_rank_cmd_table"
          )
        ]
      ).universe_rank_user_feature_build_enricher() \
      .universe_rank_context_feature_build_enricher() \
      .universe_rank_item_feature_build_enricher(
        register_table = [
          dict(
            item_table_name = "ad_list_item_table",
            item_feature_table_name = "item_feature_table_name"
          )
        ]
      ).ad_router_send_request_mixer(
        no_check=True,
        arena_attr_name = 'ad_router_predict_arena',
        predict_request_attr_name = "universe_origin_router_request",
        user_feature_table_name = "universe_rank_user_feature",
        context_feature_table_name = "universe_rank_context_feature",
        item_feature_tables = ["item_feature_table_name"],
        cmd_item_tables = [
          {
            "cmd_table" : "universe_rank_cmd_table",
            "item_table" : "global_ad_table"
          }
        ],
        merge_user_info_name = "merge_user_info",
        predict_response_attr_name = "universe_predict_response",
        save_async_status_to = "universe_predict_async_status",
        output_debug_sub_reqeust_name = "sub_request_map_exp",
        async_status_attr = "bs_trans_async_status",
        predict_scene = "universe"
      ) \
    .else_() \
      .universe_ranking_prepare_async(
        pslab_downstream_processor = "fill_cxr_enricher",
      ) \
    .end_if_() \
    .model_calibrate_async(pslab_downstream_processor = "universe_unify_cxr") \
    .ali_predict(pslab_downstream_processor = "universe_unify_cxr") \
    .rta_second_predict(pslab_downstream_processor = "universe_unify_cxr") \
    .rank_server_prepare() \
    .if_("enableUniverseAdRouterExp == 1 and enable_universe_get_router_user_info == 1") \
      .ad_router_response_proc(
        no_check=True,
        cmd_item_tables = [
          {
            "cmd_table" : "universe_rank_cmd_table",
            "item_table" : "global_ad_table"
          }
        ],
        predict_response_attr_name = "universe_predict_response",
        async_status_attr = "universe_predict_async_status"
      ).fill_cxr_enricher() \
    .else_() \
      .fill_cxr_enricher(
        pslab_async_out_processor = "fill_cxr_enricher"
      ) \
    .end_if_() \
    .universe_unify_cxr(pslab_async_out_processor = "universe_unify_cxr") \
    .init_params()
    return self

  @module()
  def calc_benefit(self, name):
    self.calc_cpm(name="calc_cpm") \
    .calc_rank_benefit(name="calc_rank_benefit")
    return self

  @module()
  def ad_rank_admit(self, name):
    with ProtoGuard() as proto_guard:
      FilterGroup = proto_guard.get_module(PROCESSOR_PB_PATH).FilterGroupType
      FilterRule = proto_guard.get_module(PROCESSOR_PB_PATH).FilterRuleType
      FilterReason = proto_guard.get_module(FILTER_REASON_PB_PATH).AdTraceFilterCondition
      self.run_udf_filter(
        name = "NobidPsFilter",
        item_table = "ad_list_item_table",
        filter_group = FilterGroup.RankAdmitGroup,
        filter_rule = FilterRule.NobidPsFilter,
        filter_reason = FilterReason.RANKING_CVR_MIN_THR_FILTER
      ).run_udf_filter(
        name = "InnerDeepPsFilter",
        item_table = "ad_list_item_table",
        filter_group = FilterGroup.RankAdmitGroup,
        filter_rule = FilterRule.InnerDeepPsFilter,
        filter_reason = FilterReason.CVR_MODEL_FILTER
      ).run_udf_filter(
        name = "EspMobileDeepPsFilter",
        item_table = "ad_list_item_table",
        filter_group = FilterGroup.RankAdmitGroup,
        filter_rule = FilterRule.EspMobileDeepPsFilter,
        filter_reason = FilterReason.RANKING_CVR_MIN_THR_FILTER
      ).run_udf_filter(
        name = "AntouRandomDropFilter",
        item_table = "ad_list_item_table",
        filter_group = FilterGroup.RankAdmitGroup,
        filter_rule = FilterRule.AntouRandomDropFilter,
        filter_reason = FilterReason.RANKING_ANTOU_RANDOM_DROP_FILTER
      )
    return self

  @module()
  def ad_rank_universe_flow(self, name):
    with data_manager:
      current_flow() \
    .prepare_request(name="request_parse")  \
    .ad_rank_extdata_process(name="extdata_proc")  \
    .ad_rank_admit(name="ad_admit")  \
    .calc_benefit(name="calc_benefit")  \
    .universe_auction() \
    .post_proc(name="post_proc")
    return self

default_flow = AdRankServerFlow(name="default", item_table="item_table")

#ab 参数 dome  enable_ab = '{{' + ab("enable_ab", False) + '}}'

with default_flow, data_manager:
    '''
    默认处理流程
    '''
    req_type = RequestType_default
    default_flow.prepare() \
    .if_("rank_process_status == 1")\
      .switch_("rank_graph_name") \
        .case_("AdUniverseRank")\
          .ad_rank_universe_flow(name="ad_rank_universe_flow") \
        .default_() \
          .do_nothing() \
        .end_switch_() \
    .end_if_() \

universe_flow = AdRankServerFlow(name="universe_flow", item_table="item_table")
with universe_flow, data_manager:
    '''
    默认处理流程
    '''
    req_type = RequestType_universe_flow
    universe_flow.prepare() \
    .if_("rank_process_status == 1") \
      .ad_rank_universe_flow(name="universe") \
    .end_if_() \

# Service definition
# 定义service维度的一些配置, add_leaf_flows 方法将各个 flow 绑定到对应的 request_type 下
#default_flow = AdRankServerFlow(name="default", item_table="item_table").default_flow()

def CookService(service):
  service.return_common_attrs([
  ])
  service.return_item_attrs([
  ])
  service.CHECK_UNUSED_ATTR = False
  service.AUTO_INJECT_META_DATA = False
  service.item_attr_types = item_attr_schema.get_item_table_schema()
  service.common_attr_types = common_attr_schema.get_rank_common_schema()
  service.check_attr_type_for_tables = item_attr_schema.get_item_table_list()
  service.IGNORE_NO_SOURCE_ATTR=['ad_list_item_table::ad_price.gpm',
                                 'ad_list_item_table::ad_price.cpm',
                                 'ad_list_item_table::ad_price.soft_freq_emb_hc_ratio',
                                 'ad_list_item_table::is_photo_or_p2l']
  #item_attr_schema = rank_schema_config.get_item_table_schema()
  #common_attr_schema = rank_schema_config.get_rank_common_schema()
  #service.item_attr_types = item_attr_schema
  #service.common_attr_types = common_attr_schema
  current_folder = os.path.dirname(os.path.abspath(__file__))
  service.add_leaf_flows(leaf_flows=[default_flow], request_type="default", as_default=True)  \
         .add_leaf_flows(leaf_flows=[universe_flow], request_type="universe_flow") \
         .draw(to_dragonfly_viz = True, mode = "remote")

service = LeafService(kess_name="ad-rank-server")
CookService(service)


current_folder = os.path.dirname(os.path.abspath(__file__))
service.build(output_file=os.path.join(current_folder, "../pub/ad_rank_server/config/config.json"),
              extra_fields=adrank_config.config)
