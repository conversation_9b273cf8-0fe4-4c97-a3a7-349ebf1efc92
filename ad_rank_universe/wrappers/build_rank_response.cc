#include "teams/ad/ad_rank_universe/wrappers/build_rank_response.h"

#include <algorithm>
#include <cstdint>
#include <functional>
#include <map>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "base/encoding/base64.h"
#include "base/strings/string_printf.h"
#include "serving_base/utility/system_util.h"
#include "ks/base/abtest/abtest_instance.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/pid_tag_enum.pb.h"
#include "teams/ad/ad_rank_universe/common/context_data.h"
#include "teams/ad/ad_rank_universe/utils/kconf/kconf.h"
#include "teams/ad/ad_rank_universe/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank_universe/utils/utility/ksn_util.h"
#include "teams/ad/ad_rank_universe/utils/utility/utility.h"

using kuaishou::ad::AdRankResponse;
using ks::engine_base::PredictType;

constexpr int64_t kExValue = 1e6;
namespace ks {
namespace ad_rank {

bool RankResponseBuilder::BuildRankResp(const ContextData& context_data,
                                        AdRankResponse* rank_resp) {
  rank_resp->set_last_filter_condition(context_data.last_filter_condition_);
  rank_resp->set_soft_last_filter_condition(context_data.soft_last_filter_condition_);
  bool found_hard = false;
  bool found_soft = false;
  for (const auto* p_ad : context_data.ad_list.Ads()) {
    if (p_ad->IsHardAd()) {
      rank_resp->set_last_filter_condition(
        kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION);
      found_hard = true;
    } else if (p_ad->IsNativeAd()) {
      rank_resp->set_soft_last_filter_condition(
        kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION);
      found_soft = true;
    }
    if (found_hard && found_soft) break;
  }
  rank_resp->set_server_host(context_data.hostname);
  rank_resp->set_ranking_ps_cost_ms(context_data.ranking_ps_cost_ms);
  rank_resp->set_ranking_ps_timeout_judge(context_data.ps_is_timeout);

  *rank_resp->mutable_rank_cmd() = {context_data.rank_cmds.begin(), context_data.rank_cmds.end()};
  // debug info
  FillDebugInfo(context_data, rank_resp);
  FillPsRequestContextOuterLoop(context_data, rank_resp);

  // 设置 ad_list
  FillCalcBenifitAdInfo(context_data, rank_resp);
  FillRankAdInfo(context_data, rank_resp);
  FillFilterAdInfo(context_data, rank_resp);
  if (IsUniverseKsn() && context_data.IsUniverseTraffic()) {
    FillUniverseAckData(context_data, rank_resp);
  }

  return true;
}

void RankResponseBuilder::FillPsRequestContextOuterLoop(const ContextData& context_data,
                                                        kuaishou::ad::AdRankResponse* rank_resp) {
  rank_resp->mutable_ps_context()->CopyFrom(*context_data.context);
  FillStyleServerData(context_data, rank_resp);
}

void RankResponseBuilder::FillStyleServerData(const ContextData& context_data,
                                              kuaishou::ad::AdRankResponse* rank_resp) {
  using kuaishou::ad::ContextInfoCommonAttr;
  if (context_data.ad_style_res.empty()) {
    return;
  }
  // 主站落地页样式特征
  auto* lp_style_server_id = ks::ad_rank::utility::AddStyleFeature(
      rank_resp->mutable_ps_context(), ContextInfoCommonAttr::STYLE_SERVER_LP_STYLE_MATERIAL_ID_V2);
  auto* lp_style_type = ks::ad_rank::utility::AddStyleFeature(
      rank_resp->mutable_ps_context(), ContextInfoCommonAttr::STYLE_SERVER_LP_STYLE_TYPE_V2);
  auto* lp_resource_type = ks::ad_rank::utility::AddStyleFeature(
      rank_resp->mutable_ps_context(), ContextInfoCommonAttr::STYLE_SERVER_LP_RESOURCE_TYPE_V2);
  auto* lp_impr_data = ks::ad_rank::utility::AddStyleFeature(
      rank_resp->mutable_ps_context(), ContextInfoCommonAttr::STYLE_SERVER_LP_IMPR_DATA_V2);
  auto* lp_conv_data = ks::ad_rank::utility::AddStyleFeature(
      rank_resp->mutable_ps_context(), ContextInfoCommonAttr::STYLE_SERVER_LP_CONV_DATA_V2);
  // 如果遍历第一个队列已经填充了数据，第二个队列就不用再遍历了
  FillPsContextStyleServerInfo(lp_style_server_id, lp_style_type, lp_resource_type, lp_impr_data,
                                   lp_conv_data, context_data, context_data.ad_list);
  // 主站卡片样式特征
  auto* card_matrix_style_material_id = ks::ad_rank::utility::AddStyleFeature(
      rank_resp->mutable_ps_context(), ContextInfoCommonAttr::STYLE_SERVER_CARD_MATRIX_STYLE_MATERIAL_ID);
  auto* card_matrix_style_type = ks::ad_rank::utility::AddStyleFeature(
      rank_resp->mutable_ps_context(), ContextInfoCommonAttr::STYLE_SERVER_CARD_MATRIX_STYLE_TYPE);
  auto* card_matrix_style_sub_type = ks::ad_rank::utility::AddStyleFeature(
      rank_resp->mutable_ps_context(), ContextInfoCommonAttr::STYLE_SERVER_CARD_MATRIX_STYLE_SUB_TYPE);
  FillPsContextStyleServerCardInfo(card_matrix_style_material_id, card_matrix_style_type,
                                        card_matrix_style_sub_type, context_data, context_data.ad_list);
}

bool RankResponseBuilder::FillPsContextStyleServerInfo(
    kuaishou::ad::ContextInfoCommonAttr* lp_style_server_id,
    kuaishou::ad::ContextInfoCommonAttr* lp_style_type, kuaishou::ad::ContextInfoCommonAttr* lp_resource_type,
    kuaishou::ad::ContextInfoCommonAttr* lp_impr_data, kuaishou::ad::ContextInfoCommonAttr* lp_conv_data,
    const ContextData& context_data, const AdList& ad_list) {
  bool is_fill = false;
  std::set<int32_t> style_type_list{202, 210};
  for (const auto* p_ad : ad_list.Ads()) {
    if (p_ad->is_inner_loop_ad()) {
      continue;
    }
    auto iter = context_data.ad_style_res.find(p_ad->get_creative_id());
    if (iter == context_data.ad_style_res.end()) {
      continue;
    }
    for (auto& style_group : iter->second->style_groups()) {
      auto& main_style = style_group.main_style();
      if (style_type_list.count(main_style.style_type())) {
        (*(lp_style_server_id->mutable_map_int64_string_value()))[p_ad->get_creative_id()] =
            std::to_string(main_style.style_material_id());
        (*(lp_style_type->mutable_map_int64_string_value()))[p_ad->get_creative_id()] =
            std::to_string(main_style.style_type());
        (*(lp_resource_type->mutable_map_int64_string_value()))[p_ad->get_creative_id()] =
            std::to_string(main_style.resource_type());
        (*(lp_impr_data->mutable_map_int64_string_value()))[p_ad->get_creative_id()] =
            std::to_string(main_style.style_dsp_lp_impr_data());
        (*(lp_conv_data->mutable_map_int64_string_value()))[p_ad->get_creative_id()] =
            std::to_string(main_style.style_dsp_lp_conv_data());
        is_fill = true;
        break;
      }
    }
  }
  return is_fill;
}

bool RankResponseBuilder::FillPsContextStyleServerCardInfo(
    kuaishou::ad::ContextInfoCommonAttr* card_matrix_style_material_id,
    kuaishou::ad::ContextInfoCommonAttr* card_matrix_style_type,
    kuaishou::ad::ContextInfoCommonAttr* card_matrix_style_sub_type,
    const ContextData& context_data, const AdList& ad_list) {
  bool is_fill = false;
  std::set<int32_t> matrix_style_type_list{12};
  for (const auto* p_ad : ad_list.Ads()) {
    if (p_ad->is_inner_loop_ad()) {
      continue;
    }
    auto iter = context_data.ad_style_res.find(p_ad->get_creative_id());
    if (iter == context_data.ad_style_res.end()) {
      continue;
    }
    for (auto& style_group : iter->second->style_groups()) {
      auto& main_style = style_group.main_style();
      if (matrix_style_type_list.count(main_style.matrix_style_type())) {
        (*(card_matrix_style_material_id->mutable_map_int64_string_value()))[p_ad->get_creative_id()] =
            std::to_string(main_style.matrix_style_material_id());
        (*(card_matrix_style_type->mutable_map_int64_string_value()))[p_ad->get_creative_id()] =
            std::to_string(main_style.matrix_style_type());
        (*(card_matrix_style_sub_type->mutable_map_int64_string_value()))[p_ad->get_creative_id()] =
            std::to_string(main_style.matrix_style_sub_type());
        is_fill = true;
        break;
      }
    }
  }
  return is_fill;
}

void PerfAdFeatureDiffValue(int64 val1, int64 val2, const std::string& field) {
  int64 diff_value = std::abs(val1 - val2);
  int64 diff_ratio = (diff_value * 1.0 / (val1 == 0 ? val1 + 1 : val1)) * 100;
  ks::infra::PerfUtil::IntervalLogStash(diff_value, "ad.ad_rank.ad_feature_diff", "diff_value", field);
  ks::infra::PerfUtil::IntervalLogStash(diff_ratio, "ad.ad_rank.ad_feature_diff", "diff_ratio", field);
  ks::infra::PerfUtil::IntervalLogStash(val1, "ad.ad_rank.ad_feature_diff", "post_server_value", field);
  ks::infra::PerfUtil::IntervalLogStash(val2, "ad.ad_rank.ad_feature_diff", "ad_feature_value", field);
}

bool RankResponseBuilder::FillRankAdInfo(const ContextData& context_data,
                                        AdRankResponse* rank_resp) {
  // 避免每个 ad 都获取一次，这里提前获取
  auto* priority_map = RankKconfUtil::inspireStylePriorityConf().get();
  if (priority_map == nullptr) {
    return true;
  }
  std::vector<std::pair<std::string, int32_t>> priority_conf(priority_map->begin(), priority_map->end());
  // 按优先级从高到低进行一次排序
  std::sort(priority_conf.begin(), priority_conf.end(),
            [](const std::pair<std::string, int32_t>& left, const std::pair<std::string, int32_t>& right) {
              return left.second > right.second;
            });
  const auto p_conf = RankKconfUtil::inspireStyleMultiPriorityConf();
  const auto &multi_priority_map = p_conf->data().style_priority_map();
  const auto &account_priority_map = p_conf->data().account_map();

  int maxRankSize = 50;
  if (context_data.IsUniverseTraffic()) {
    maxRankSize = context_data.kconf_session_context->TryGetInteger("max_ad_rank_result_size_universe", 50);
  }
  bool enable_cid_spu_strategy_exp = SPDM_enable_cid_spu_strategy_exp(context_data.spdm_ctx);
  bool enable_cid_quality_strategy_exp = SPDM_enable_cid_quality_strategy_exp(context_data.spdm_ctx);

  size_t hard_ad_res_size = 0;
  size_t soft_ad_res_size = 0;
  size_t inner_fans_res_size = 0;
  size_t outer_live_res_size = 0;
  size_t inner_normal_res_size = 0;
  size_t inner_native_res_size = 0;
  size_t outer_normal_res_size = 0;
  size_t outer_native_res_size = 0;

  int32_t universe_ecpc_fill_info_max_size = RankKconfUtil::universeEcpcUnifyFillInfoMaxSize();
  // 激励视频相关字段
  int32_t rewarded_current_view_cnt = 0;        // 激励视频用户当天第几刷
  int32_t rewarded_last_inspire_amount = 0;     // 激励视频用户上一次金币数量
  bool rewarded_show_activate_inspire = false;  // 激励视频激活激励 pv tag
  bool rewarded_show_order_inspire = false;     // 激励视频下单激励 pv tag
  // 搜索广告下载强样式标记
  bool search_ad_app_card_flag = false;
  bool search_ad_form_card_flag = false;
  int32_t search_ad_app_card_satisfy_cnt = 0;
  int32_t search_ad_form_card_satisfy_cnt = 0;
  int32_t search_ad_list_app_card_satisfy_cnt = 0;
  int32_t search_ad_list_form_card_satisfy_cnt = 0;
  std::set<int64_t> list_app_product_id_set;
  std::set<int64_t> list_form_product_id_set;
  int32_t search_ad_app_card_target_trans_cnt = 0;
  int32_t search_ad_form_card_target_trans_cnt = 0;
  // N 刷出 K 次人群包命中
  int64_t support_project_orientation_id = context_data.rank_request->rank_control_params()
      .support_project_orientation_id();
  // 粉条保量 quota
  bool enable_fanstop_quota = SPDM_enable_fanstop_rank_high_priority_quota(context_data.spdm_ctx);
  int64_t fanstop_photo_quota = SPDM_fanstop_rank_high_priority_photo_quota(context_data.spdm_ctx);
  int64_t fanstop_live_quota = SPDM_fanstop_rank_high_priority_live_quota(context_data.spdm_ctx);
  std::string bonus_exp_tag =
      context_data.kconf_session_context->TryGetString("rank_dragon_update_exp_tag", "default");
  int64_t fanstop_photo_count = 0;
  int64_t fanstop_live_count = 0;
  std::unordered_map<int64_t, int32_t> ad_force_reco_tag_count;
  auto auc_hit_tag = context_data.kconf_session_context->TryGetString("auc_hit_tag", "");
  bool enableAdFeatureDiff = (ad_base::AdRandom::GetInt(1, 10000) <= 1);
  for (auto pos = 0; pos < context_data.ad_list.Size(); ++pos) {
    const AdCommon* p_ad = context_data.ad_list.At(pos);
    if (p_ad == nullptr || !p_ad->GetValid()) {
      continue;
    }
    bool exceed_res_quota = false;
    if (rank_resp->ad_rank_result_size() >= maxRankSize) {
      exceed_res_quota = true;
    }
    if (enable_fanstop_quota && p_ad->is_fanstop() &&
        p_ad->get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
      // 对 fanstop, count < quota 时，按照 quota 进行保量
      if ((fanstop_photo_count < fanstop_photo_quota && p_ad->is_photo()) ||
          (fanstop_live_count < fanstop_live_quota && p_ad->is_live())) {
        exceed_res_quota = false;
      }
      if (p_ad->is_photo()) {
        fanstop_photo_count += 1;
      } else if (p_ad->is_live()) {
        fanstop_live_count += 1;
      }
    }
    if (exceed_res_quota) {
      AdCommon *ad = const_cast<AdCommon *> (context_data.ad_list.At(pos));
      if (ad != nullptr) {
        ad->SetAdInValid(kuaishou::log::ad::AdTraceFilterCondition::RANKING_SORT_FILTER,
                       kuaishou::ad::AdRankNodeType::RANK_SERVER_POST_PROC_TYPE,
                       kuaishou::ad::AdRankPointType::RANK_POINT_DEFAULT_TYPE,
                       kuaishou::ad::AdRankPluginType::RANK_PLUGIN_DEFAULTTYPE);
        std::unordered_map<int64, kuaishou::log::ad::AdTraceFilterCondition>* rank_filter_ads
          = const_cast<std::unordered_map<int64, kuaishou::log::ad::AdTraceFilterCondition> *>
            (&context_data.rank_filter_ads);
        if (rank_filter_ads != nullptr) {
          rank_filter_ads->insert(std::make_pair(p_ad->creative_id(),
              kuaishou::log::ad::AdTraceFilterCondition::RANKING_SORT_FILTER));
        }
        continue;
      }
    }

    bool is_skip_qcpx_mode = context_data.is_skip_qcpx_mode && p_ad->is_closure_ad();
    kuaishou::ad::RankResult* p_resp_rank_result = rank_resp->add_ad_rank_result();
    for (const auto cmd_id : p_ad->getReqCmdIds()) {
      if (p_resp_rank_result->cmd_id_size() >= RankKconfUtil::maxRecordCmdNum()) {
        break;
      }
      p_resp_rank_result->add_cmd_id(cmd_id);
    }

    if (SPDM_enableCmdKeyRecord()) {
      for (const auto cmd_key_id : p_ad->getReqCmdKeyIds()) {
        if (p_resp_rank_result->cmd_key_id_size() >= RankKconfUtil::maxRecordCmdNum()) {
          break;
        }
        p_resp_rank_result->add_cmd_key_id(cmd_key_id);
      }
    }
    LOG_EVERY_N(INFO, 100000) << "cmd_id_size:" << p_resp_rank_result->cmd_id_size();
    p_resp_rank_result->set_ad_priority(p_ad->ad_priority);
    p_resp_rank_result->set_creative_id(p_ad->get_creative_id());
    p_resp_rank_result->set_unique_id(p_ad->creative_id());
    {
      auto p_resp_rank_base = p_resp_rank_result->mutable_rank_base_info();

      p_resp_rank_base->set_ad_strategy_tag(p_ad->get_ad_strategy_tag());
      p_resp_rank_base->set_is_server_show_ocpm(p_ad->get_is_server_show_ocpm());
      p_resp_rank_base->set_is_client_show_ocpm(p_ad->get_is_client_show_ocpm());
      p_resp_rank_base->set_is_skip_bid_server(
        p_resp_rank_base->is_skip_bid_server() || p_ad->get_is_skip_bid_server());
      p_resp_rank_base->set_is_hit_keyword_target(p_ad->get_is_hit_keyword_target());
      p_resp_rank_base->set_creative_type(p_ad->get_creative_type());
      p_resp_rank_base->set_is_programmatic_creative(p_ad->get_is_programmatic_creative());
      p_resp_rank_base->set_topic_type(p_ad->get_topic_type());
      p_resp_rank_base->set_ad_source_type(p_ad->get_ad_source_type());
      p_resp_rank_base->set_adx_source_type(p_ad->get_adx_source_type());
      p_resp_rank_base->set_adx_tag_id(p_ad->adx_np.tag_id);
      p_resp_rank_base->set_campaign_type(p_ad->get_campaign_type());
      p_resp_rank_base->set_account_id(p_ad->get_account_id());
      p_resp_rank_base->set_campaign_id(p_ad->get_campaign_id());
      p_resp_rank_base->set_unit_id(p_ad->get_unit_id());
      p_resp_rank_base->set_product_name(p_ad->base_np.product_name);
      p_resp_rank_base->set_convert_type(p_ad->get_convert_type());
      p_resp_rank_base->set_is_retarget_ad(p_ad->get_is_retarget_ad());
      p_resp_rank_base->set_retarget_tool_tag(p_ad->get_retarget_tool_tag());
      p_resp_rank_base->set_product_min_price(p_ad->get_product_min_price());
      p_resp_rank_base->set_is_unit_bonus(p_ad->get_is_unit_bonus());
      bool is_account_style_priority = false;
      auto iter = account_priority_map.find(p_ad->get_account_id());
      if (iter != account_priority_map.end()) {
        auto iter2 = multi_priority_map.find(iter->second);
        if (iter2 != multi_priority_map.end()) {
          std::vector<std::pair<std::string, int32_t>> account_priority_conf(
            iter2->second.style_priority().begin(), iter2->second.style_priority().end());
          // 按优先级从高到低进行一次排序
          std::sort(account_priority_conf.begin(), account_priority_conf.end(),
                    [](const std::pair<std::string, int32_t>& left,
                        const std::pair<std::string, int32_t>& right) {
                      return left.second > right.second;
                    });
          is_account_style_priority = true;
        }
      }
      p_resp_rank_base->mutable_feature_index_info()->set_general_review(p_ad->get_general_review());
      p_resp_rank_base->mutable_feature_index_info()->set_hot_review(p_ad->get_hot_review());
      p_resp_rank_base->mutable_feature_index_info()->set_topk_review(p_ad->get_topk_review());
      p_resp_rank_base->set_campaign_budget(p_ad->get_campaign_budget());
      p_resp_rank_base->set_landing_page_component(p_ad->get_landing_page_component());
    }
    {
      auto p_resp_bid = p_resp_rank_result->mutable_rank_bid_info();
      p_resp_bid->set_bid_type(p_ad->get_bid_type());
      p_resp_bid->set_auction_bid(p_ad->get_auction_bid());
      p_resp_bid->set_auction_bid_deep(p_ad->get_auction_bid_deep());
      p_resp_bid->set_auction_bid_ecpc(p_ad->get_auction_bid_ecpc());
      p_resp_bid->set_ranking_auto_cpa_bid(p_ad->get_auto_cpa_bid());
      p_resp_bid->set_auto_cpa_bid_modify_tag(p_ad->get_auto_cpa_bid_modify_tag());
      p_resp_bid->set_auction_bid_modify_tag(static_cast<int64_t>(p_ad->get_auction_modify_tag()));
      p_resp_bid->set_auto_roas_modify_tag(p_ad->get_auto_roas_modify_tag());
      p_resp_bid->set_raw_auction_bid(p_ad->get_raw_auction_bid());
      p_resp_bid->set_cpa_bid(p_ad->get_cpa_bid());
      p_resp_bid->set_rta_bid(p_ad->get_rta_bid());
      p_resp_bid->set_twin_bid_strategy(p_ad->get_twin_bid_strategy());
      if (SPDM_enable_ad_bid_server_group_tag_fix2(context_data.spdm_ctx)) {
        p_resp_bid->set_ad_bid_server_group_tag(p_ad->get_ad_bid_server_group_tag());
      }
      // 联盟 ecpc 专用字段
      if (IsUniverseKsn()) {
        p_resp_bid->set_ecpc_unify_input(
            p_ad->get_predict_score(PredictType::PredictType_conv_nextstay));
        if (SPDM_enableUniverseEcpcUnifyFillInfos()) {
          int32_t universe_ecpc_info_capacity = 0;
          for (const auto& strategy_info : p_ad->universe_ecpc_infos_map) {
            for (const auto& factor_info : strategy_info.second) {
              for (const auto& ecpc_info : factor_info.second) {
                if ((ecpc_info.pid_tag ==
                      kuaishou::ad::PidServer::PidTag::UNIVERSE_ECPC_HAS_TARGET_RATIO_PRIORI ||
                    ecpc_info.pid_tag ==
                      kuaishou::ad::PidServer::PidTag::UNIVERSE_ECPC_HAS_TARGET_COST_POST) &&
                    universe_ecpc_info_capacity < universe_ecpc_fill_info_max_size) {
                  p_resp_bid->add_ecpc_unify_inputs(ecpc_info.ecpc_unify_input);
                  p_resp_bid->add_ecpc_unify_targets(ecpc_info.target);
                  universe_ecpc_info_capacity++;
                }
              }
            }
          }
          universe_ecpc_info_capacity += p_ad->base_np.pid_tags.size();
          context_data.dot_perf->Interval(universe_ecpc_info_capacity,
                                          "ad_rank.universe_ecpc_fill_info_capacity_input_size");
        }
        for (const auto& pid_tag : p_ad->base_np.pid_tags) {
          p_resp_bid->add_ecpc_unify_inputs(0);
          p_resp_bid->add_ecpc_unify_targets(0);
        }
      }
    }
    {
      auto p_resp_rank = p_resp_rank_result->mutable_rank_rank_info();
      p_resp_rank->set_ctr(p_ad->get_ctr());
      p_resp_rank->set_pos_in_rank(p_ad->get_pos_in_rank());
      p_resp_rank->set_unify_ctr(p_ad->GetUnifyCtrInfo().value);
      p_resp_rank->set_universe_cvr_calibrate_origin_rate(p_ad->get_universe_cvr_calibrate_origin_value());
      p_resp_rank->set_unify_cvr(p_ad->GetUnifyCvrInfo().value);
      p_resp_rank->set_ctr_start(p_ad->GetUnifyCtrInfo().s_type);
      p_resp_rank->set_ctr_end(p_ad->GetUnifyCtrInfo().e_type);
      p_resp_rank->set_ctr_value(p_ad->GetUnifyCtrInfo().value);
      p_resp_rank->set_ctr_tag((int32_t)p_ad->GetUnifyCtrInfo().r_tag);

      p_resp_rank->set_cvr_start(p_ad->GetUnifyCvrInfo().s_type);
      p_resp_rank->set_cvr_end(p_ad->GetUnifyCvrInfo().e_type);
      p_resp_rank->set_cvr_value(p_ad->GetUnifyCvrInfo().value);
      p_resp_rank->set_cvr_tag((int32_t)p_ad->GetUnifyCvrInfo().r_tag);
      p_resp_rank->set_origin_c1_lps_rate(p_ad->get_reward_origin_landingpage_submit_rate());
      p_resp_rank->set_deep_cvr_start(p_ad->GetUnifyDeepCvrInfo().s_type);
      p_resp_rank->set_deep_cvr_end(p_ad->GetUnifyDeepCvrInfo().e_type);
      p_resp_rank->set_deep_cvr_value(p_ad->GetUnifyDeepCvrInfo().value);
      p_resp_rank->set_deep_cvr_tag((int32_t)p_ad->GetUnifyDeepCvrInfo().r_tag);
      // 交叉 AUC 的指标数据先使用这个
      {
          AdCommon *ad = const_cast<AdCommon *> (p_ad);
          p_resp_rank->set_predict_auc_score_exp(
              ad->get_predict_score(PredictType::PredictType_auc_model_exp));
          p_resp_rank->set_predict_auc_score_base(
              ad->get_predict_score(PredictType::PredictType_auc_model_base));
          p_resp_rank->set_auc_cmd_id_base(
              ad->get_predict_cmd_id(PredictType::PredictType_auc_model_base));
          p_resp_rank->set_auc_cmd_id_exp(
              ad->get_predict_cmd_id(PredictType::PredictType_auc_model_exp));
          p_resp_rank->set_predict_auc_score_exp_fusion(
              ad->get_predict_score(PredictType::PredictType_auc_model_exp_fusion));
          p_resp_rank->set_predict_auc_score_base_fusion(
              ad->get_predict_score(PredictType::PredictType_auc_model_base_fusion));
          p_resp_rank->set_auc_cmd_id_base_fusion(
              ad->get_predict_cmd_id(PredictType::PredictType_auc_model_base_fusion));
          p_resp_rank->set_auc_cmd_id_exp_fusion(
              ad->get_predict_cmd_id(PredictType::PredictType_auc_model_exp_fusion));
      }
      p_resp_rank->set_auc_hit_tag(auc_hit_tag);
      p_resp_rank->set_universe_bid_boost_coef(p_ad->get_universe_bid_boost_coef());
      p_resp_rank->set_ug_pltv7(p_ad->get_ug_pltv7());
      p_resp_rank->set_ug_roi_goal(p_ad->get_ug_roi_goal());
      p_resp_rank->set_ug_roi_bid_coef(p_ad->get_ug_roi_bid_coef());
      p_resp_rank->set_ug_roi_discount_coef(p_ad->get_ug_roi_discount_coef());
      p_resp_rank->set_ug_roi_ecpm_truth(p_ad->get_ug_roi_ecpm_truth());
      p_resp_rank->set_ug_roi_deep_cvr_truth(p_ad->get_ug_roi_deep_cvr_truth());
      for (const auto& data : p_ad->base_np.universe_operation_datas) {
        auto* operation_data = p_resp_rank->add_universe_operation_data();
        operation_data->set_universe_operation_type(
            kuaishou::ad::AdEnum_UniverseOperationType(data.operation_type));
        operation_data->set_universe_operation_stra_id(data.operation_stra_id);
        operation_data->set_universe_operation_ad_cluster_id(data.operation_ad_cluster_id);
        operation_data->set_universe_operation_media_cluster_id(data.operation_media_cluster_id);
      }
    }
    {
      auto p_resp_rank_price = p_resp_rank_result->mutable_rank_price_info();
      p_resp_rank_price->set_rank_benifit(p_ad->ad_price.rank_benifit);
      if (context_data.IsUniverseTraffic()) {
        // 联盟 rank_benefit 和 real_benefit 没有区别
        p_resp_rank_price->set_rank_benifit(p_ad->ad_price.GetRankBenefit());
        p_resp_rank_price->set_real_benifit(p_ad->ad_price.GetRankBenefit());
        p_resp_rank_price->set_universe_use_second_price(p_ad->ad_price.universe_use_second_price);
      }
      p_resp_rank_price->set_cpm(p_ad->ad_price.cpm);
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
        context_data.dot_perf->Interval(p_ad->ad_price.cpm, "ad_rank.lps_ecpm", "normal");
      }
      p_resp_rank_price->set_sort_tag(p_ad->ad_price.sort_tag);
      p_resp_rank_price->set_real_cpm(p_ad->ad_price.raw_cpm);
      p_resp_rank_price->set_universe_rtb_ecpm(p_ad->get_rtb_ecpm());
      p_resp_rank_price->set_origin_cpm(p_ad->ad_price.origin_cpm);
      double cpm_ratio = p_ad->ad_price.origin_cpm > 0 ?
                        1.0 * p_ad->ad_price.cpm / p_ad->ad_price.origin_cpm : 1.0;
      p_resp_rank_price->set_cpm_ratio(cpm_ratio);
      // 联盟 ecpc pid_tag 填充
      if (IsUniverseKsn() && SPDM_enableUniverseEcpcUnifyFillInfos()) {
        int32_t universe_ecpc_info_capacity = 0;

        for (const auto& strategy_info : p_ad->universe_ecpc_infos_map) {
          for (const auto& factor_info : strategy_info.second) {
            for (const auto& ecpc_info : factor_info.second) {
              if ((ecpc_info.pid_tag ==
                    kuaishou::ad::PidServer::PidTag::UNIVERSE_ECPC_HAS_TARGET_RATIO_PRIORI ||
                  ecpc_info.pid_tag ==
                    kuaishou::ad::PidServer::PidTag::UNIVERSE_ECPC_HAS_TARGET_COST_POST) &&
                  universe_ecpc_info_capacity < universe_ecpc_fill_info_max_size) {
                p_resp_rank_price->add_pid_tag(ecpc_info.pid_tag);
                universe_ecpc_info_capacity++;
              }
            }
          }
        }
        universe_ecpc_info_capacity += p_ad->base_np.pid_tags.size();
        context_data.dot_perf->Interval(universe_ecpc_info_capacity,
                                        "ad_rank.universe_ecpc_fill_info_capacity_pid_tag_size");
      }
      for (const auto& pid_tag : p_ad->base_np.pid_tags) {
        p_resp_rank_price->add_pid_tag(pid_tag);
      }
    }
    // 外循环电商机制预估分集合回传
    {
      auto p_resp_made = p_resp_rank_result->mutable_rank_made_info();
      // 复用 rta 一次出价系数
      p_resp_made->set_first_click_score(
        static_cast<int64_t>(p_ad->get_rta_ratio() * kExValue));
      // 复用 rta 二次出价系数
      p_resp_made->set_multi_touch_score(
        static_cast<int64_t>(p_ad->get_rta_ratio_second_bid() * kExValue));
      if (p_ad->get_is_rta_bid_second()) {
        p_resp_made->set_rta_second_direct_bid(p_ad->get_rta_bid());
      }
      p_resp_made->set_rta_sta_tag(p_ad->get_rta_sta_tag());
    }
    // 回传 predict params , 用于打印 adlogfull
    {
      auto p_resp_predict_origin_info = p_resp_rank_result->mutable_predict_origin_info();
      p_resp_predict_origin_info->set_ctr(p_ad->get_ctr());
      p_resp_predict_origin_info->set_cvr(p_ad->get_cvr());
      p_resp_predict_origin_info->set_cvr_cmd_id(p_ad->get_cvr_cmd_id());
      p_resp_predict_origin_info->set_ltr(p_ad->get_predict_score(PredictType::PredictType_ltr));
      p_resp_predict_origin_info->set_photo2live_rate(p_ad->get_live_audience());
      p_resp_predict_origin_info->set_fan_longterm_active_rate(
          p_ad->get_predict_score(PredictType::PredictType_fan_longterm_active_rate));
      p_resp_predict_origin_info->set_app_conversion_rate(p_ad->get_app_conversion_rate());  // 和 cvr 一模一样 NOLINT
      p_resp_predict_origin_info->set_landingpage_submit_rate(p_ad->get_landingpage_submit_rate());
      p_resp_predict_origin_info->set_click2_lps(p_ad->get_click2_lps());
      p_resp_predict_origin_info->set_purchase_rate(
          p_ad->get_predict_score(PredictType::PredictType_purchase));
      p_resp_predict_origin_info->set_click_purchase_rate(
          p_ad->get_predict_score(PredictType::PredictType_click_purchase));
      p_resp_predict_origin_info->set_retention_rate(p_ad->get_retention_rate());
      p_resp_predict_origin_info->set_click_retention_rate(
          p_ad->get_predict_score(PredictType::PredictType_click_retention));
      p_resp_predict_origin_info->set_click_credit_rate(p_ad->get_click_credit_rate());
      p_resp_predict_origin_info->set_shop_action_rate(
          p_ad->get_predict_score(PredictType::PredictType_shop_action));
      p_resp_predict_origin_info->set_credit_conv_grant_rate(
          p_ad->get_predict_score(PredictType::PredictType_credit_conv_grant));
      p_resp_predict_origin_info->set_server_client_show_rate(
          p_ad->get_predict_score(PredictType::PredictType_server_client_show_rate));
      p_resp_predict_origin_info->set_conv_nextstay(p_ad->get_conv_nextstay());
      p_resp_predict_origin_info->set_click_purchase_rate_single_bid(
          p_ad->get_predict_score(PredictType::PredictType_click_purchase_rate_single_bid));
      p_resp_predict_origin_info->set_click2_purchase_rate_single_bid(
          p_ad->get_predict_score(PredictType::PredictType_click2_purchase_rate_single_bid));
      p_resp_predict_origin_info->set_deep_rate(
          p_ad->get_predict_score(PredictType::PredictType_deep_rate));
      p_resp_predict_origin_info->set_click2_deep_rate(
          p_ad->get_predict_score(PredictType::PredictType_click2_deep_rate));
      p_resp_predict_origin_info->set_conv_ltv(
          p_ad->get_predict_score(PredictType::PredictType_game_conv_ltv));
      p_resp_predict_origin_info->set_delivered_rule_id(p_ad->get_rule_id());
      p_resp_predict_origin_info->set_c1_order_paied(p_ad->get_c1_order_paied());
      p_resp_predict_origin_info->set_c2_order_paied(p_ad->get_c2_order_paied());
      p_resp_predict_origin_info->set_c1_merchant_follow(p_ad->get_c1_merchant_follow());
      p_resp_predict_origin_info->set_click_purchase_rate_single_bid(
          p_ad->get_predict_score(PredictType::PredictType_click_purchase_rate_single_bid));
      p_resp_predict_origin_info->set_click2_purchase_rate_single_bid(
          p_ad->get_predict_score(PredictType::PredictType_click2_purchase_rate_single_bid));
      p_resp_predict_origin_info->set_server_client_show_rate(
          p_ad->get_predict_score(PredictType::PredictType_server_client_show_rate));
      p_resp_predict_origin_info->set_live_order_paid_rate(p_ad->get_live_order_paid());
      p_resp_predict_origin_info->set_game_appoint_rate(
          p_ad->get_predict_score(PredictType::PredictType_game_appoint_rate));
      p_resp_predict_origin_info->set_c2_game_appoint_rate(
          p_ad->get_predict_score(PredictType::PredictType_c2_game_appoint_rate));
      if (SPDM_enableUniverseUCxr()) {
          p_resp_predict_origin_info->set_outer_u_ctr(
         p_ad->get_predict_score(PredictType::PredictType_outer_u_ctr));
          p_resp_predict_origin_info->set_outer_u_cvr(
             p_ad->get_predict_score(PredictType::PredictType_outer_u_cvr));
          p_resp_predict_origin_info->set_outer_u_noctcvr(
             p_ad->get_predict_score(PredictType::PredictType_outer_u_noctcvr));
      }
      p_resp_predict_origin_info->set_merchant_ltv(p_ad->get_ad_merchant_roas_rate());
      p_resp_predict_origin_info->set_conv2_purchase(p_ad->get_conv2_purchase());
      p_resp_predict_origin_info->set_click_app_invoked(
          p_ad->get_predict_score(PredictType::PredictType_click_app_invoked));
      p_resp_predict_origin_info->set_p2l_server_show_ctr(p_ad->get_ctr());
      p_resp_predict_origin_info->set_p2l_p3s(p_ad->get_cvr());
      p_resp_predict_origin_info->set_ad_live_audience(p_ad->get_live_audience());
      p_resp_predict_origin_info->set_event_7_day_pay_times(
          p_ad->get_predict_score(PredictType::PredictType_7_day_pay_times));
      p_resp_predict_origin_info->set_reward_cascading_landingpage_submit_rate(
          p_ad->get_reward_origin_landingpage_submit_rate());
      p_resp_predict_origin_info->set_conv_7_day_stay(
          p_ad->get_predict_score(PredictType::PredictType_conv_7_day_stay));
      {
        AdCommon *ad = const_cast<AdCommon *> (p_ad);
        p_resp_predict_origin_info->set_apr(ad->get_predict_score(PredictType::PredictType_apr));
        p_resp_predict_origin_info->set_c1_conv_fix(
            ad->get_predict_score(PredictType::PredictType_c1_conv_fix));
        p_resp_predict_origin_info->set_special_lps(
            ad->get_predict_score(PredictType::PredictType_special_lps));
        p_resp_predict_origin_info->set_purchase_ltv(
            ad->get_predict_score(PredictType::PredictType_game_purchase_ltv));
      }
    }
    {
      auto* p_resp_ad_rank_trans_info = p_resp_rank_result->mutable_ad_rank_trans_info();
      {
        AdCommon *ad = const_cast<AdCommon *> (p_ad);
        p_resp_ad_rank_trans_info->set_up_model_no_pay_rate(
            ad->get_predict_score(PredictType::PredictType_up_model_no_pay_rate));
        p_resp_ad_rank_trans_info->set_up_model_low_pay_rate(
            ad->get_predict_score(PredictType::PredictType_up_model_low_pay_rate));
        p_resp_ad_rank_trans_info->set_up_model_medium_pay_rate(
            ad->get_predict_score(PredictType::PredictType_up_model_medium_pay_rate));
        p_resp_ad_rank_trans_info->set_up_model_high_pay_rate(
            ad->get_predict_score(PredictType::PredictType_up_model_high_pay_rate));
        if (SPDM_printHighPrecisionAuctionBid()) {
          p_resp_ad_rank_trans_info->set_high_precision_auction_bid(
              p_ad->get_high_precision_auction_bid());
        }
        if (SPDM_enableCorporationNameUniverse()) {
          p_resp_ad_rank_trans_info->set_corporation_name_universe(
            std::string(p_ad->get_corporation_name()));
        }
      }
      auto *ad_strategy_info = p_resp_ad_rank_trans_info->mutable_ad_strategy_info();
      for (const auto& formula_info : p_ad->ad_formula_info) {
        if (formula_info.second != 1.0) {
          auto* ad_formula_info = ad_strategy_info->add_ad_formula_info();
          ad_formula_info->set_formula_type(formula_info.first);
          ad_formula_info->set_value(formula_info.second);
        }
      }
      for (const auto& formula_factor_info : p_ad->ad_factor_info) {
        for (const auto& factor_info : formula_factor_info.second) {
          auto& info = factor_info.second;
          if (info.is_hit) {
            auto* ad_factor_info = ad_strategy_info->add_ad_factor_info();
            ad_factor_info->set_formula_type(info.formula_type);
            ad_factor_info->set_factor_type(info.factor_type);
            ad_factor_info->set_is_hit(info.is_hit);
            ad_factor_info->set_value(info.value);
          }
        }
      }
      // 加速探索字段
      {
        if (p_ad->get_is_explore_ad()) {
          p_resp_ad_rank_trans_info->set_is_explore_ad(p_ad->get_is_explore_ad());
          p_resp_ad_rank_trans_info->mutable_auction_trans_to_front()->set_is_increment_explore(
            p_ad->get_is_explore_ad());
          p_resp_ad_rank_trans_info->set_auto_bid_explore(
            p_ad->get_auto_bid_explore());
        }
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE)) {
        p_resp_ad_rank_trans_info->set_miniapp_iaa_roas_ltv(
          p_ad->get_miniapp_iaa_roas_ltv());
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) {
        p_resp_ad_rank_trans_info->set_game_conv_ltv(p_ad->get_game_conv_ltv());
      }
      p_resp_ad_rank_trans_info->set_second_industry_id(p_ad->get_second_industry_id_v5());
      p_resp_ad_rank_trans_info->set_crm_center(p_ad->get_crm_center());
      p_resp_ad_rank_trans_info->set_universe_cost_boost_ratio(p_ad->get_universe_cost_boost_ratio());
      p_resp_ad_rank_trans_info->set_conv_7_day_ltv(p_ad->get_purchase_7_day_ltv());
      p_resp_ad_rank_trans_info->set_conv_1_day_ltv(
          p_ad->get_predict_score(PredictType::PredictType_game_conv_ltv));
      p_resp_ad_rank_trans_info->set_event_7_day_pay_times(
          p_ad->get_predict_score(PredictType::PredictType_7_day_pay_times));
      p_resp_ad_rank_trans_info->set_conv_24h_stay(
          p_ad->get_predict_score(PredictType::PredictType_conv_24h_stay));
      p_resp_ad_rank_trans_info->set_ug_rta_lt_7(p_ad->get_ug_rta_lt7());
      p_resp_ad_rank_trans_info->set_ug_rta_next_stay_rate(p_ad->get_ug_rta_next_stay_rate());
      p_resp_ad_rank_trans_info->set_ug_rta_lt7over1(p_ad->get_ug_rta_lt7over1());
      p_resp_ad_rank_trans_info->set_conv_key_inapp_action_rate(
          p_ad->get_predict_score(PredictType::PredictType_conv_key_inapp_action_rate));
      p_resp_ad_rank_trans_info->set_budget_coef(p_ad->get_budget_coef());
      p_resp_ad_rank_trans_info->set_cpa_coef(p_ad->get_cpa_coef());
      p_resp_ad_rank_trans_info->set_universe_quality_score(
          p_ad->get_universe_quality_score());
      p_resp_ad_rank_trans_info->set_ecpc_adjust_ratio(1.0);
      p_resp_ad_rank_trans_info->set_ecpc_adjust_tag(EcpcAdjustTag::Unknown);
      p_resp_ad_rank_trans_info->set_ali_exclude_flag(p_ad->get_ali_outer_bid_type());
      p_resp_ad_rank_trans_info->set_auto_roas(p_ad->get_auto_roas());
      p_resp_ad_rank_trans_info->set_calibration_ratio(p_ad->get_calibration_ratio());
      p_resp_ad_rank_trans_info->set_fl_product_dcvr(p_ad->get_fl_product_dcvr());
      p_resp_ad_rank_trans_info->set_universe_cvr_calibrate_origin_rate(
                                p_ad->get_universe_cvr_calibrate_origin_value());
      p_resp_ad_rank_trans_info->set_universe_explore_ab_tag(p_ad->universe_explore_ab_tag);
      p_resp_ad_rank_trans_info->set_universe_mcda_hit_tag(p_ad->universe_mcda_hit_tag);
      p_resp_ad_rank_trans_info->set_universe_mcda_hit_ratio(p_ad->universe_mcda_hit_ratio);
      for (const auto& pair : p_ad->universe_explore_multi_records) {
        p_resp_ad_rank_trans_info->mutable_universe_explore_multi_records()->insert(
            {static_cast<int32_t>(pair.first), static_cast<std::string>(pair.second)});
      }
      for (const auto& pair : p_ad->universe_explore_weight_records) {
        p_resp_ad_rank_trans_info->mutable_universe_explore_weight_records()->insert(
            {static_cast<int32_t>(pair.first), static_cast<std::string>(pair.second)});
      }
      double media_share_ratio =
        context_data.rank_request->ad_request().universe_ad_request_info().share_ratio() * 1.0 / 10000;
      p_resp_ad_rank_trans_info->set_media_share_ratio(media_share_ratio);
      p_resp_ad_rank_trans_info->set_unify_ltv(p_ad->GetUnifyLtvInfo().value);
      p_resp_ad_rank_trans_info->set_target_factor(p_ad->get_target_factor());
      p_resp_ad_rank_trans_info->set_universe_cpm_boost_tag(p_ad->get_universe_cpm_boost_tag());
      p_resp_ad_rank_trans_info->set_universe_cpa_boost_tag(p_ad->get_universe_cpa_boost_tag());
      p_resp_ad_rank_trans_info->set_universe_pos_org_target_cpm(
          context_data.universe_data.universe_pos_org_target_cpm);
      p_resp_ad_rank_trans_info->set_universe_pos_target_cpm(
          context_data.universe_data.universe_pos_target_cpm);
      p_resp_ad_rank_trans_info->set_universe_pos_cpm_bound(
          context_data.universe_data.universe_pos_cpm_bound);
      for (int i = 0; i < p_ad->universe_ecpc_infos.size(); i++) {
        if (p_ad->universe_ecpc_infos[i].ecpc_strategy_type ==
              engine_base::UniverseEcpcStrategyType::NO_TARGET_RARIO_PRIORI ||
            p_ad->universe_ecpc_infos[i].ecpc_strategy_type ==
              engine_base::UniverseEcpcStrategyType::HAS_TARGET_RATIO_PRIORI) {
          continue;
        }
        auto *universe_ecpc_info = p_resp_ad_rank_trans_info->add_universe_ecpc_info();
        universe_ecpc_info->set_ecpc_strategy_type(p_ad->universe_ecpc_infos[i].ecpc_strategy_type);
        universe_ecpc_info->set_input(p_ad->universe_ecpc_infos[i].ecpc_unify_input);
        universe_ecpc_info->set_target(p_ad->universe_ecpc_infos[i].target);
        universe_ecpc_info->set_ratio(p_ad->universe_ecpc_infos[i].ratio);
        universe_ecpc_info->set_is_valid(p_ad->universe_ecpc_infos[i].is_valid);
        universe_ecpc_info->set_crowd_package_id(p_ad->universe_ecpc_infos[i].crowd_package_id);
        universe_ecpc_info->set_optimization_object(p_ad->universe_ecpc_infos[i].optimization_object);
        universe_ecpc_info->set_exp_state(p_ad->universe_ecpc_infos[i].exp_state);
        universe_ecpc_info->set_has_pdcvr(p_ad->universe_ecpc_infos[i].has_pdcvr);
      }
      for (const auto& strategy_info : p_ad->universe_ecpc_infos_map) {
        for (const auto& factor_info : strategy_info.second) {
          for (const auto& ecpc_info : factor_info.second) {
            auto *universe_ecpc_info = p_resp_ad_rank_trans_info->add_universe_ecpc_info();
            universe_ecpc_info->set_ecpc_strategy_type(ecpc_info.ecpc_strategy_type);
            universe_ecpc_info->set_input(ecpc_info.ecpc_unify_input);
            universe_ecpc_info->set_target(ecpc_info.target);
            universe_ecpc_info->set_ratio(ecpc_info.ratio);
            universe_ecpc_info->set_is_valid(ecpc_info.is_valid);
            universe_ecpc_info->set_crowd_package_id(ecpc_info.crowd_package_id);
            universe_ecpc_info->set_optimization_object(ecpc_info.optimization_object);
            universe_ecpc_info->set_exp_state(ecpc_info.exp_state);
            universe_ecpc_info->set_has_pdcvr(ecpc_info.has_pdcvr);
          }
        }
      }
      p_resp_ad_rank_trans_info->set_mcb_roi_ratio(p_ad->get_mcb_roi_ratio());
      p_resp_ad_rank_trans_info->set_mcb_cpa_bid(p_ad->get_mcb_cpa_bid());
      p_resp_ad_rank_trans_info->set_performance_fix_ratio(p_ad->get_performance_fix_ratio());
      p_resp_ad_rank_trans_info->set_product_cpa_bid(p_ad->get_product_cpa_bid());
      p_resp_ad_rank_trans_info->set_unify_sample_rank_bid(p_ad->get_unify_sample_rank_bid());
      p_resp_ad_rank_trans_info->set_universe_live_audience_cali_tag(
                                    p_ad->get_universe_live_audience_cali_tag());
      p_resp_ad_rank_trans_info->set_universe_live_pay_rate_cali_tag(
                                    p_ad->get_universe_live_pay_rate_cali_tag());
      p_resp_ad_rank_trans_info->set_universe_video_pay_rate_cali_tag(
                                    p_ad->get_universe_video_pay_rate_cali_tag());
      p_resp_ad_rank_trans_info->set_universe_inner_strategy_tag(
                                      p_ad->get_universe_inner_real_boost_tag());
      p_resp_ad_rank_trans_info->set_universe_inner_strategy_ratio(
                                      p_ad->get_universe_inner_real_boost_ratio());
      if (context_data.is_universe_tiny_flow) {
        p_resp_ad_rank_trans_info->set_universe_tiny_query_type(
                                      p_ad->get_universe_tiny_query_type());
        p_resp_ad_rank_trans_info->set_app_package_name_id(
                                      p_ad->get_app_package_name_id());
      }
      int64 unit_ad_counter_item_impression = p_ad->get_unit_ad_counter_item_impression_ad_feature();
      p_resp_ad_rank_trans_info->set_unit_ad_counter_item_impression(unit_ad_counter_item_impression);  // NOLINT
      p_resp_ad_rank_trans_info->set_constraint_cpa(p_ad->get_constraint_cpa());
      p_resp_ad_rank_trans_info->set_constraint_roi(p_ad->get_constraint_roi());
      p_resp_ad_rank_trans_info->set_ori_photo_quality_score(p_ad->get_ori_photo_quality_score());
      {
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT) {
          auto score = p_ad->get_predict_score(PredictType::PredictType_cid_mcda_score);
          if (score > 0) {
            p_resp_ad_rank_trans_info->set_ecpc_predict_type(kuaishou::ad::AdEnum::P_CID_MCDA);
            p_resp_ad_rank_trans_info->set_ecpc_predict_score(score);
          }
        }
        if (enable_cid_spu_strategy_exp) {
          p_resp_ad_rank_trans_info->set_cid_spu_type(p_ad->get_cid_spu_type());
          p_resp_ad_rank_trans_info->set_cid_spu_price_ratio(p_ad->get_cid_spu_price_ratio());
        }
        if (enable_cid_quality_strategy_exp) {
          p_resp_ad_rank_trans_info->set_cid_quality_level(p_ad->get_cid_quality_level());
        }
        p_resp_ad_rank_trans_info->set_rta_quality_score(p_ad->get_rta_quality_score());
      }
      p_resp_ad_rank_trans_info->set_is_account_bidding(p_ad->get_is_account_bidding());
      p_resp_ad_rank_trans_info->set_bid_coef(p_ad->get_bid_coef());
      p_resp_ad_rank_trans_info->set_aggre_key(p_ad->get_aggre_key());
      // 统一 adload 曝光系数， cpm 门槛系数
      p_resp_ad_rank_trans_info->set_native_strict_status(p_ad->get_native_strict_status());
      p_resp_ad_rank_trans_info->set_unify_ctr_before_commoncali(
        p_ad->get_unify_ctr_before_commoncali());
      p_resp_ad_rank_trans_info->set_unify_cvr_before_commoncali(
        p_ad->get_unify_cvr_before_commoncali());
      p_resp_ad_rank_trans_info->set_unify_dcvr_before_commoncali(
        p_ad->get_unify_dcvr_before_commoncali());
      p_resp_ad_rank_trans_info->set_unify_ltv_before_commoncali(
        p_ad->get_unify_ltv_before_commoncali());
      p_resp_ad_rank_trans_info->set_unify_ctr_after_commoncali(
        p_ad->get_unify_ctr_after_commoncali());
      p_resp_ad_rank_trans_info->set_unify_cvr_after_commoncali(
        p_ad->get_unify_cvr_after_commoncali());
      p_resp_ad_rank_trans_info->set_unify_dcvr_after_commoncali(
        p_ad->get_unify_dcvr_after_commoncali());
      p_resp_ad_rank_trans_info->set_unify_ltv_after_commoncali(
        p_ad->get_unify_ltv_after_commoncali());
      if (SPDM_enableUniverseUCxr()) {
        p_resp_ad_rank_trans_info->set_outer_u_ctcvr_p_in_20min(
            p_ad->get_predict_score(PredictType::PredictType_outer_u_ctcvr_p_in_20min));
        p_resp_ad_rank_trans_info->set_outer_u_ctcvr_p_out_20min(
            p_ad->get_predict_score(PredictType::PredictType_outer_u_ctcvr_p_out_20min));
        p_resp_ad_rank_trans_info->set_outer_u_ctcvr_all(
            p_ad->get_predict_score(PredictType::PredictType_outer_u_ctcvr_all));
        p_resp_ad_rank_trans_info->set_outer_u_noctcvr(
            p_ad->get_predict_score(PredictType::PredictType_outer_u_noctcvr));
      }

      if (SPDM_enable_universe_high_cpm_model_fusion(context_data.spdm_ctx)) {
        p_resp_ad_rank_trans_info->set_high_cvr_origin_cvr_ratio(
            context_data.get_high_cvr_origin_cvr_ratio());
        p_resp_ad_rank_trans_info->set_use_high_cvr_origin_cvr_ratio(
            context_data.get_use_high_cvr_origin_cvr_ratio());
      }

      if (SPDM_enableUniverseTransparentQcpxRank() && !is_skip_qcpx_mode) {
        auto* qpon_info = p_resp_ad_rank_trans_info->mutable_qpon_info();
        qpon_info->set_has_qpon(p_ad->get_has_qpon());
        qpon_info->set_qpon_type(p_ad->get_qpon_type());
        qpon_info->set_bid_qpon_ratio(p_ad->get_bid_qpon_ratio());
        qpon_info->set_uplift_ratio(p_ad->get_uplift_cpm_ratio());
        qpon_info->set_uplift_ctr_ratio(p_ad->get_uplift_ctr_ratio());
        qpon_info->set_uplift_cvr_ratio(p_ad->get_uplift_cvr_ratio());
        qpon_info->set_cost_sharing_type(p_ad->get_qpon_cost_sharing_type());
        // qcpx coupon info
        p_resp_ad_rank_trans_info->set_coupon_scope(p_ad->get_coupon_scope());
        p_resp_ad_rank_trans_info->set_coupon_strategy_type(p_ad->get_qpon_type());
        p_resp_ad_rank_trans_info->set_qcpx_put_type(p_ad->get_unit_qcpx_put_type());
        if (SPDM_enableUniverseQcpxPerf()) {
          context_data.dot_perf->Count(1, "qcpx", "ad_filter_reason",
          kuaishou::ad::AdEnum::UniverseInnerQcpxAdFilterReason_Name(
            p_ad->get_universe_inner_qcpx_filter_reason()));
          context_data.dot_perf->Interval(p_ad->get_uplift_cvr_ratio() * 1e6, "qcpx", "qpon_info",
            "uplift_cvr_ratio");
          context_data.dot_perf->Interval(p_ad->get_uplift_ctr_ratio() * 1e6, "qcpx", "qpon_info",
            "uplift_ctr_ratio");
          context_data.dot_perf->Interval(p_ad->get_uplift_cpm_ratio() * 1e6, "qcpx", "qpon_info",
            "uplift_ratio");
          context_data.dot_perf->Interval(p_ad->get_bid_qpon_ratio() * 1e6, "qcpx", "qpon_info",
            "bid_qpon_ratio");
          context_data.dot_perf->Interval(p_ad->get_uplift_cvr_ratio() * p_ad->get_uplift_ctr_ratio() *
                                          p_ad->get_bid_qpon_ratio() * 1e9, "qcpx", "qpon_info",
            "uplift_cpm_ratio");
          context_data.dot_perf->Count(1, "qcpx", "universe_inner_qcpx_cause",
            absl::StrCat(p_ad->get_universe_inner_qcpx_cause()));
          context_data.dot_perf->Count(1, "qcpx", "qpon_type",
            kuaishou::ad::AdEnum_QponType_Name(p_ad->get_qpon_type()));
          if (context_data.universe_inner_qcpx_pv_filter_reason ==
              kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_QCPX_UNKNOWN_TYPE_PV &&
              p_ad->get_universe_inner_qcpx_filter_reason() ==
              kuaishou::ad::AdEnum_UniverseInnerQcpxAdFilterReason_QCPX_UNKNOWN_TYPE_AD) {
            context_data.dot_perf->Interval(1, "qcpx", "coupon_request_number");
          }
        }
        if (SPDM_enableUniverseInnerQcpxFilterReason()) {
          p_resp_ad_rank_trans_info->set_universe_inner_qcpx_filter_reason(
            kuaishou::ad::AdEnum::UniverseInnerQcpxAdFilterReason_Name(p_ad->get_universe_inner_qcpx_filter_reason()));  // NOLINT
        } else {
          p_resp_ad_rank_trans_info->set_universe_inner_qcpx_filter_reason(
            kuaishou::ad::InnerQcpxFilterReason_Name(p_ad->get_universe_inner_qcpx_filter_reason()));
        }
        p_resp_ad_rank_trans_info->set_universe_inner_qcpx_cause(p_ad->get_universe_inner_qcpx_cause());
      }
      if (RankKconfUtil::enableUniverseFlowExplore()) {
        auto* flow_explore_exp_info = p_resp_ad_rank_trans_info->mutable_flow_explore_exp_info();
        flow_explore_exp_info->CopyFrom(p_ad->get_flow_explore_exp_info());
      }
    }
    if (SPDM_enableUniverseTransparentQcpxRank() && !is_skip_qcpx_mode) {
      if (p_ad->get_coupon_template_id() > 0) {
        auto p_coupon_info = p_resp_rank_result->mutable_coupon_info();
        p_coupon_info->set_coupon_template_id(p_ad->get_coupon_template_id());
        p_coupon_info->set_coupon_type(p_ad->get_coupon_type());
        p_coupon_info->set_threshold_type(p_ad->get_threshold_type());
        p_coupon_info->set_threshold(p_ad->get_threshold());
        p_coupon_info->set_discount_amount(p_ad->get_coupon_discount_amount());
        p_coupon_info->set_threshold_upper(p_ad->get_threshold_upper());
        p_coupon_info->set_discount_amount_upper(p_ad->get_discount_amount_upper());
        p_coupon_info->set_reduce_amount(p_ad->get_reduce_amount());
        p_coupon_info->set_capped_amount(p_ad->get_capped_amount());
        if (SPDM_enableUniverseQcpxPerf()) {
          context_data.dot_perf->Interval(p_ad->get_coupon_discount_amount(), "qcpx", "coupon_info",
            "discount_amount");
          context_data.dot_perf->Interval(p_ad->get_threshold(), "qcpx", "coupon_info",
            "coupon_threshold");
          context_data.dot_perf->Interval(p_ad->get_reduce_amount(), "qcpx", "coupon_info",
            "reduce_amount");
          context_data.dot_perf->Interval(p_ad->get_capped_amount(), "qcpx", "coupon_info",
            "capped_amount");
          context_data.dot_perf->Count(1, "qcpx", "coupon_type",
            absl::StrCat(p_ad->get_coupon_type()));
        }
      }
    }
    {
      auto p_resp_rank_assemble = p_resp_rank_result->mutable_assemble_info();
      p_resp_rank_assemble->set_industry(p_ad->get_industry_id_v3());
    }
    {
      auto p_rank_stat_info = p_resp_rank_result->mutable_rank_stat_info();
      p_rank_stat_info->set_photo_release_time(
          p_ad->get_photo_first_index_time());
      p_rank_stat_info->set_live_release_time(p_ad->get_live_release_time());
      if (SPDM_enableUniverseColdStartField()) {
        p_rank_stat_info->set_unit_release_time(p_ad->get_unit_release_time());
      }
    }

    if (p_ad->IsNativeAd()) {
      soft_ad_res_size++;
    } else {
      hard_ad_res_size++;
    }

    // 内外循环维度计数
    if (p_ad->is_inner_loop_ad()) {
      if (p_ad->IsNativeAd()) {
        inner_native_res_size++;
      } else {
        inner_normal_res_size++;
      }
    } else {
      if (p_ad->IsNativeAd()) {
        outer_native_res_size++;
      } else {
        outer_normal_res_size++;
      }
    }

    // 透传到 front 的数据
    {
      auto rank_info_iter = context_data.creative_2_rank_infos_map.find(
          absl::Substitute("$0-$1", p_ad->get_ad_queue_type(), p_resp_rank_result->creative_id()));
      if (rank_info_iter != context_data.creative_2_rank_infos_map.end()) {
        rank_info_iter->second->SerializeToString(
            p_resp_rank_result->mutable_ad_rank_to_front()->mutable_rank_info());
      } else {
        context_data.dot_perf->Count(1, "ad_rank.creative_2_rank_info", "get_failed");
      }
    }
  }

  if (rank_resp->ad_rank_result_size() > 0 && RankKconfUtil::enableAdRankResultPass()) {
    kuaishou::ad::AdRankPassThrough temp;
    temp.set_group_tag(context_data.group_tag);
    temp.set_deep_group_tag(context_data.deep_group_tag);
    temp.set_cpm_bound(context_data.universe_data.cpm_bound);
    temp.set_target_cpm(context_data.universe_data.target_cpm);
    temp.set_org_target_cpm(context_data.universe_data.org_target_cpm);
    if (SPDM_enableUniverseInnerQcpxFilterReason()) {
      temp.set_universe_inner_qcpx_pv_filter_reason(context_data.universe_inner_qcpx_pv_filter_reason);
    }
    if (SPDM_enableUniverseQcpxPerf()) {
      context_data.dot_perf->Count(1, "qcpx", "pv_filter_reason",
        kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_Name(
          context_data.universe_inner_qcpx_pv_filter_reason));
    }
    if (context_data.IsUniverseTraffic()) {
      int maxSize = std::min(static_cast<int>(context_data.kconf_session_context->TryGetInteger(
                                 "max_ad_rank_result_pass_size_universe", 50)),
                             rank_resp->ad_rank_result_size());
      *temp.mutable_ad_rank_result() = {rank_resp->ad_rank_result().begin(),
                                        rank_resp->ad_rank_result().begin() + maxSize};
    }

    std::map<int64_t, kuaishou::ad::SessionRankInfo*> rank_info_map;
    std::map<int64_t, std::vector<kuaishou::ad::SessionRankInfo *>> rank_infos_map;
    for (int i = 0; i < rank_resp->rank_info_size(); i++) {
      uint64_t unique_id = rank_resp->rank_info(i).unique_id();
      rank_info_map[rank_resp->rank_info(i).unique_id()] = rank_resp->mutable_rank_info(i);
      rank_infos_map[unique_id].push_back(rank_resp->mutable_rank_info(i));
    }

    auto get_proper_rank_info = [&rank_infos_map](
                                    const std::vector<kuaishou::ad::SessionRankInfo *> &rank_infos,
                                    bool is_inner_loop_hard_ad) -> kuaishou::ad::SessionRankInfo * {
      if (rank_infos.empty()) return 0;
      for (int i = 0; i < rank_infos.size(); ++i) {
        bool is_amd_rank_info = !(rank_infos[i]->is_native() || rank_infos[i]->is_fanstop());
        if (is_inner_loop_hard_ad == is_amd_rank_info) {
          return rank_infos[i];
        }
      }
      return rank_infos.back();
    };

    for (int i = 0; i < temp.ad_rank_result_size(); i++) {
      if (context_data.inner_loop_creative_set.count(temp.ad_rank_result(i).unique_id())) {
        auto &rank_result = temp.ad_rank_result(i);
        auto t = temp.add_rank_info();
        auto iter = rank_infos_map.find(rank_result.unique_id());
        if (iter != rank_infos_map.end() && iter->second.size() > 0) {
          auto *proper =
             get_proper_rank_info(iter->second, rank_result.rank_base_info().is_inner_loop_hard_ad());
          if (proper) {
            t->CopyFrom(*proper);
          }
        }
      } else {
        auto t = temp.add_rank_info();
        auto iter = rank_info_map.find(temp.ad_rank_result(i).unique_id());
        if (iter != rank_info_map.end() && iter->second != nullptr) {
          t->CopyFrom(*(iter->second));
        }
      }
    }

    std::string pass_through;
    temp.SerializeToString(&pass_through);
    std::string base64_pass_through;
    base::Base64Encode(pass_through, &base64_pass_through);
    rank_resp->set_ad_rank_result_pass_through(base64_pass_through);
  }


  // MCB 浅度约束打点
  std::unordered_map<std::string, std::unordered_map<std::string, int32_t>> constraint_r_cnt;

  for (auto pos = 0; pos < context_data.ad_list.Size(); ++pos) {
    const AdCommon* p_ad = context_data.ad_list.At(pos);
    if (p_ad == nullptr || !p_ad->GetValid() || p_ad->get_bid_type() != kuaishou::ad::AdEnum::MCB) {
      continue;
    }

    const std::string& ocpx_name = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    const std::string& constraint_name = kuaishou::ad::AdActionType_Name(0);

    constraint_r_cnt[ocpx_name][constraint_name]++;
  }

  for (const auto& i : constraint_r_cnt) {
    for (const auto& j : i.second) {
      context_data.dot_perf->Interval(j.second, "ad_rank.constraint", i.first, j.first);
    }
  }

  return true;
}  // NOLINT

bool RankResponseBuilder::FillCalcBenifitAdInfo(const ContextData& context_data,
                                                AdRankResponse* rank_resp) {
  std::vector<std::pair<uint64_t, uint64_t>> unique_id_2_cpm;
  std::map<uint64_t, int> unique_id_2_pos_in_cpm_rank;
  int64_t max_real_rank_benifit = INT_MIN;
  int top0_real_rank_benifit = -1;
  uint32 ps_pv_tag = 0;  // 最后一位代表 ps_pv_tag, 倒数第二位代表 sort_by_benifit_flag
  int64_t top0_photo_id = 0;
  const auto& ad_rank_infos = context_data.ad_rank_infos;
  for (size_t i = 0; i < ad_rank_infos.size(); ++i) {
    if (ad_rank_infos[i].rank_info.ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE &&
      !(context_data.inner_loop_creative_set.count(ad_rank_infos[i].rank_info.item_id()) > 0)) {
      continue;
    }
    uint64_t item_id = ad_rank_infos[i].rank_info.item_id();
    uint64_t cpm = ad_rank_infos[i].rank_info.cpm();
    unique_id_2_cpm.push_back(std::make_pair(item_id, cpm));
    if (ad_rank_infos[i].rank_info.real_rank_benifit() > max_real_rank_benifit) {
      max_real_rank_benifit = ad_rank_infos[i].rank_info.real_rank_benifit();
    }
    uint32_t rank_index = 1000;   // 异常情况，默认 1000
    if (rank_index == 0) {
      top0_real_rank_benifit = ad_rank_infos[i].rank_info.real_rank_benifit();
      top0_photo_id = ad_rank_infos[i].photo_id;
      if (ad_rank_infos[i].bonus_cpm > 0) {
        ps_pv_tag = (ps_pv_tag | 1);  // 将最后一位置为 1
      }
    }
  }
  std::sort(unique_id_2_cpm.begin(), unique_id_2_cpm.end(), [](auto &lhs, auto &rhs) {
    return lhs.second > rhs.second;
  });

  int inner_loop_cpm_rank_index = 0;
  int outer_loop_cpm_rank_index = 0;
  for (size_t i = 0; i < unique_id_2_cpm.size(); ++i) {
    unique_id_2_pos_in_cpm_rank[unique_id_2_cpm[i].first] = i;
  }
  // rank 排名第一的创意是否按照 benifit 排序，sort_tag = 1024
  bool sort_by_benifit_flag = (top0_real_rank_benifit == max_real_rank_benifit);
  if (!sort_by_benifit_flag) {
    ps_pv_tag = (ps_pv_tag | 2);  // 将倒数第二位置 1
  }
  for (size_t i = 0; i < ad_rank_infos.size(); ++i) {
    auto p_resp_rank_info = rank_resp->add_rank_info();
    p_resp_rank_info->set_item_id(ad_rank_infos[i].rank_info.item_id());
    p_resp_rank_info->set_creative_id(ad_rank_infos[i].rank_info.item_id());
    p_resp_rank_info->set_ad_queue_type(ad_rank_infos[i].rank_info.ad_queue_type());
    p_resp_rank_info->mutable_virtual_item()->CopyFrom(
        ad_rank_infos[i].rank_info.virtual_item());
    p_resp_rank_info->set_rank(ad_rank_infos[i].rank_info.rank());
    p_resp_rank_info->set_cpm(ad_rank_infos[i].rank_info.cpm());
    p_resp_rank_info->set_bid(ad_rank_infos[i].rank_info.bid());
    p_resp_rank_info->set_delivery_rate(ad_rank_infos[i].rank_info.delivery_rate());
    p_resp_rank_info->set_unique_id(ad_rank_infos[i].unique_id);

    p_resp_rank_info->set_unique_id(ad_rank_infos[i].rank_info.virtual_item().extra().data_index());
    p_resp_rank_info->set_ad_strategy_tag(ad_rank_infos[i].ad_strategy_tag);

    uint32_t rank_index = 1000;   // 异常情况，默认 1000
    auto index_iter  = unique_id_2_pos_in_cpm_rank.find(ad_rank_infos[i].rank_info.item_id());
    int cpm_rank_index = 1000;
    if (index_iter != unique_id_2_pos_in_cpm_rank.end()) {
      cpm_rank_index = index_iter->second;
    }
    p_resp_rank_info->mutable_label_info_attr()->CopyFrom(
        ad_rank_infos[i].rank_info.label_info_attr());
    auto* label_info_attr = p_resp_rank_info->add_label_info_attr();
    label_info_attr->set_int_value(cpm_rank_index);
    label_info_attr->set_name_value(
      ::kuaishou::ad::LabelInfoCommonAttr_Name::LabelInfoCommonAttr_Name_CPM_INDEX);
    p_resp_rank_info->set_rank_index(rank_index);
    p_resp_rank_info->set_real_rank_benifit(ad_rank_infos[i].rank_info.real_rank_benifit());
    p_resp_rank_info->set_real_cpm(ad_rank_infos[i].rank_info.real_cpm());
    p_resp_rank_info->set_unify_ctr(ad_rank_infos[i].rank_info.unify_ctr());
    p_resp_rank_info->set_unify_cvr(ad_rank_infos[i].rank_info.unify_cvr());
    p_resp_rank_info->set_unify_deep_cvr(ad_rank_infos[i].rank_info.unify_deep_cvr());
    p_resp_rank_info->set_ctr_value(ad_rank_infos[i].ctr_value);
    p_resp_rank_info->set_cvr_value(ad_rank_infos[i].cvr_value);
    p_resp_rank_info->set_bonus_cpm(ad_rank_infos[i].bonus_cpm);
    p_resp_rank_info->set_bonus_cpm_tag(ad_rank_infos[i].bonus_cpm_tag);
    p_resp_rank_info->set_auction_bid(ad_rank_infos[i].auction_bid);
    p_resp_rank_info->set_is_native(ad_rank_infos[i].rank_info.is_native());
    if (ad_rank_infos[i].source_type == kuaishou::ad::FANS_TOP_V2) {
      p_resp_rank_info->set_is_fanstop(true);
    }
    // rank_weight / 10000 为是否按照 rank benigit 排序
    uint64_t rank_weight = kSortByBenifitBaseWeight;
    if (!sort_by_benifit_flag) {
      rank_weight = 0;
    }
    if (rank_index == 0) {
      rank_weight += 1000;
    } else if (max_real_rank_benifit > 0 && ad_rank_infos[i].rank_info.real_rank_benifit() > 0) {
      rank_weight += static_cast<uint64_t>(
          ad_rank_infos[i].rank_info.real_rank_benifit() * 1000.0 / max_real_rank_benifit);
    }
    p_resp_rank_info->set_rank_weight(rank_weight);
    if (ad_rank_infos[i].photo_id == top0_photo_id && rank_index > 0) {
      ps_pv_tag = (ps_pv_tag | 4);  // 将倒数第三位置 1
    }
    p_resp_rank_info->set_ps_pv_tag(ps_pv_tag);
    ps_pv_tag = (ps_pv_tag & 3);  // 将倒数第三位恢复为 0

    if (!context_data.creative_2_rank_infos_map
             .emplace(absl::Substitute("$0-$1", p_resp_rank_info->ad_queue_type(),
                                       p_resp_rank_info->creative_id()),
                      p_resp_rank_info)
             .second) {
      // 理论不应该有这个打点，否则同一个 queue_type 也有重复 creative
      context_data.dot_perf->Count(1, "ad_rank.creative_2_rank_info", "emplace_failed");
    }
  }
  return true;
}

bool RankResponseBuilder::FillFilterAdInfo(const ContextData& context_data,
                                           AdRankResponse* rank_resp) {
  for (auto &item : context_data.rank_filter_ads) {
    auto p_resp_filter_ad = rank_resp->add_filter_ad();
    p_resp_filter_ad->set_unique_id(item.first);
    p_resp_filter_ad->set_filter_condition(item.second);
  }
  return true;
}

void RankResponseBuilder::FillDebugInfo(const ContextData& context, AdRankResponse* rank_resp) {
  if (context.multi_color_data.IsDebug()) {
    auto* stat_info = rank_resp->mutable_debug_message()->mutable_multi_stat_info();
    stat_info->insert(context.multi_color_data.infos.begin(),
                      context.multi_color_data.infos.end());
    LOG(INFO) << context.multi_color_data;
  }
}

void RankResponseBuilder::FillUniverseAckData(const ContextData& context, AdRankResponse* rank_resp) {
  rank_resp->mutable_ps_universe_ack_data()->mutable_union_ltr_used_item()->CopyFrom(
      *context.union_ltr_used_item);
  auto* pid_info = rank_resp->mutable_ps_universe_ack_data()->mutable_universe_bonus_control_pid_value();
  pid_info->insert(context.universe_data.universe_bonus_control_pid_value.begin(),
                   context.universe_data.universe_bonus_control_pid_value.end());
}

}  // namespace ad_rank
}  // namespace ks
