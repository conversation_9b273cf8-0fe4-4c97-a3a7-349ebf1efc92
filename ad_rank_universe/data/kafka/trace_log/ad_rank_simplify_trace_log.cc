#include "teams/ad/ad_rank_universe/data/kafka/trace_log/ad_rank_simplify_trace_log.h"

#include <string>
#include <utility>
#include <map>
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_rank_universe/common/ad_common.h"
#include "teams/ad/ad_base/src/common/deploy_utils.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_base/src/pb_helper/pb_helper.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/engine_base/fanstop_common/pv_debug/trace_info_io.h"
#include "teams/ad/engine_base/creative_debug_util/creative_debug_util.h"
#include "teams/ad/ad_rank_universe/utils/kconf/kconf.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/ad_rank_universe/utils/spdm/spdm_switches.h"


DECLARE_int32(ksp_group_deploy_type);

namespace ks {
namespace ad_rank {
DEFINE_string(ad_rank_simplify_trace_log_topic, "ad_rank_simplify_trace_log",
              "ad rank simplify trace log topic");
DEFINE_string(ad_rank_simplify_trace_log_v2_topic, "ad_rank_simplify_trace_log_v2",
              "ad rank simplify trace log v2 topic");
DEFINE_string(ad_rank_simplify_trace_log_topic_test, "ad_rank_simplify_trace_log_test",
              "ad rank simplify trace log topic test");
kuaishou::ad::AdTraceDspType GetAdDspType(const AdCommon &ad) {
  kuaishou::ad::AdTraceDspType ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::NORMAL_DSP_TYPE;
  if (ad.get_ad_source_type() == kuaishou::ad::ADX) {
    // adx
    ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::ADX_DSP_TYPE;
  } else if (ad.get_account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE) {
    // 磁力金牛移动版
    if (ad.IsHardAd()) {
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::ESP_MOBILE_HARD_DSP_TYPE;
    } else {
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::ESP_MOBILE_SOFT_DSP_TYPE;
    }
  } else if (ad.get_ad_source_type() == kuaishou::ad::FANS_TOP_V2) {
      // 外粉
    if (ad.IsHardAd()) {              // 硬广
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::HARD_FANSTOP_DSP_TYPE;
    } else if (ad.IsNativeAd()) {     // 软广
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::NORMAL_FANSTOP_DSP_TYPE;
    } else {                          // 未匹配到软硬广则为未知
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::UNKNOW_AD_QUEUE_TYPE;
    }
  } else if (ad.is_dpa() || ad.is_sdpa()) {
    // dpa
    ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::DPA_DSP_TYPE;
  } else if (ad.get_promotion_type() == kuaishou::ad::AdEnum::SPECIALTY_PROMOTION) {
    // 专推
    if (ad.IsHardAd()) {
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::SPECIALTY_PROMOTION_HARD_DSP_TYPE;
    } else {
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::SPECIALTY_PROMOTION_SOFT_DSP_TYPE;
    }
  } else if (ad.get_promotion_type() == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
    // 速推
    if (ad.IsHardAd()) {
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::FLASH_PROMOTION_HARD_DSP_TYPE;
    } else {
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::FLASH_PROMOTION_SOFT_DSP_TYPE;
    }
  } else if (ad.is_search_bidword()) {
    ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::MINGTOU_DSP_TYPE;
  } else if (ad.get_is_outer_loop_native()) {
    if (ad.IsHardAd()) {
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::NATIVE_AD_DSP_TYPE;
    } else {
      ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::SOFT_AD_DSP_TYPE;
    }
  } else if (ad.is_fanstop() && ad.get_ad_source_type() == kuaishou::ad::DSP) {
    ad_trace_dsp_type = kuaishou::ad::AdTraceDspType::B_CLIENT_FANSTOP;
  }
  return ad_trace_dsp_type;
}
AdRankSimplifyTraceLogManager::AdRankSimplifyTraceLogManager() {
  const std::string user_params = "compression.codec=lz4";
  // 没有初始化成功需要打出来 error 日志
  std::string simplify_trace_log_topic{""};
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv()) {
    simplify_trace_log_topic = FLAGS_ad_rank_simplify_trace_log_topic_test;
  } else {
    simplify_trace_log_topic = FLAGS_ad_rank_simplify_trace_log_topic;
  }
  if (0 == producer_.Init(simplify_trace_log_topic, user_params, 0,
                          ks::engine_base::DependDataLevel::WEAK_DEPEND)) {
    LOG(INFO) << "AdRankSimplifyTraceLog producer init success, topic: " << simplify_trace_log_topic;
    producer_init_suc_ = true;
  } else {
    LOG(ERROR) << "AdRankSimplifyTraceLog producer init failed, topic: " << simplify_trace_log_topic;
  }
  if (0 == v2_table_producer_.Init(FLAGS_ad_rank_simplify_trace_log_v2_topic, user_params, 0,
                                   ks::engine_base::DependDataLevel::WEAK_DEPEND)) {
    LOG(INFO) << "AdRankSimplifyTraceLogV2 producer init success, topic: "
              << FLAGS_ad_rank_simplify_trace_log_v2_topic;
    v2_table_producer_init_suc_ = true;
  } else {
    LOG(ERROR) << "AdRankSimplifyTraceLogV2 producer init failed, topic: "
               << FLAGS_ad_rank_simplify_trace_log_v2_topic;
  }
}
AdRankSimplifyTraceLogManager &AdRankSimplifyTraceLogManager::GetInstance() {
  static AdRankSimplifyTraceLogManager recorder;
  return recorder;
}
void AdRankSimplifyTraceLogManager::CleanExcludedField(
    kuaishou::ad::AdRankSimplifyTraceLog *ad_rank_simplify_trace_log) {
  auto filter_field = [&](const std::string &name) -> bool {
    if (RankKconfUtil::rankTraceLogAdExtendInfoWhiteList()->count(name) > 0) {
      // 找到了不需要过滤
      return false;
    }
    return true;
  };
  auto *rank_stage_info = ad_rank_simplify_trace_log->mutable_rank_stage_info();
  for (int i = 0; i < rank_stage_info->ad_item_info_size(); i++) {
    ks::ad_base::Proto::CleanFiledByFun(rank_stage_info->mutable_ad_item_info(i)->mutable_ad_extend_info(),
                                        filter_field);
  }
}
void AdRankSimplifyTraceLogManager::SendTraceLog(const kuaishou::ad::AdRequest &ad_request,
                                                 ContextData *context_data) {
  SendCreativeDebugFilterColor(context_data);
  const auto& trace_type = GetTraceType(*context_data);
  const bool is_send_white_box_data = (trace_type != AdEngineTraceType::SKIP);
  const bool is_send_v2_data =
      v2_table_producer_init_suc_ &&
      (ad_request.trace_log_sampling_flag_v2_table() != kuaishou::ad::TraceLogSamplingFlag::SKIP_SAMPLING);
  if (!is_send_white_box_data && !is_send_v2_data) {
    return;
  }

  kuaishou::ad::AdRankSimplifyTraceLog ad_rank_simplify_trace_log;
  auto *ad_request_base_info = ad_rank_simplify_trace_log.mutable_ad_request_base_info();
  // 1. 构造 AdRequestBaseInfo
  BuildRequestBaseInfo(ad_request, trace_type, context_data, ad_request_base_info);
  // 2. 写入 ad_lis_num
  auto *rank_stage_info = ad_rank_simplify_trace_log.mutable_rank_stage_info();

  rank_stage_info->set_ad_list_num(
      context_data->ad_list.FullAds().size());

  VLOG(2) << "[debug]llsid:"  << context_data->llsid
          << " trace size:" << rank_stage_info->ad_list_num()
          << " adlist size:" << context_data->ad_list.Size()
          << "/" << context_data->ad_list.FullAds().size();

  bool is_ad_data_filled = false;
  if (trace_type == AdEngineTraceType::ALL_DATA) {
    // 3. 构造 RankStageInfo
    is_print_factor_info_ = context_data->enable_print_factor_info;
    BuildRankStageInfo(context_data, rank_stage_info);
    is_print_factor_info_ = false;  // 仅白盒打印多因子公式
    // 4. 发送 kafka 之前先清除没有审批的字段
    CleanExcludedField(&ad_rank_simplify_trace_log);
    is_ad_data_filled = true;
  }
  // 5. 发送 kafka
  std::string trace_str = ad_rank_simplify_trace_log.SerializeAsString();
  if (producer_init_suc_ && is_send_white_box_data) {
    auto ret = producer_.Produce(trace_str);
    if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
      context_data->dot_perf->Count(1, "ad_rank_simplify_trace_log_error");
    } else {
      context_data->dot_perf->Count(1, "ad_rank_simplify_trace_log_suc");
    }
  }

  // 多因子框架部分信息 不落 V2
  if (is_ad_data_filled && context_data->enable_print_factor_info) {
     // LOG(INFO) << "debug tracelog: " << ad_rank_simplify_trace_log.ShortDebugString();
     // pv
     int num = ad_rank_simplify_trace_log.ad_request_base_info().ad_formula_info().size();
     ad_rank_simplify_trace_log.mutable_ad_request_base_info()
                              ->mutable_ad_formula_info()->DeleteSubrange(0, num);
     // adv
     auto *stage_info = ad_rank_simplify_trace_log.mutable_rank_stage_info();
     for (int i = 0; i < stage_info->ad_item_info_size(); i++) {
       int num = stage_info->mutable_ad_item_info(i)->mutable_ad_factor_info()->size();
       stage_info->mutable_ad_item_info(i)->mutable_ad_factor_info()->DeleteSubrange(0, num);
     }
     trace_str = std::move(ad_rank_simplify_trace_log.SerializeAsString());
     // LOG(INFO) << "debug tracelog v2: " << ad_rank_simplify_trace_log.ShortDebugString();
  }
  // 发 v2 表必须要有 ad 数据，如果前面没填，这里补上
  if (!is_ad_data_filled && (ad_request.trace_log_sampling_flag_v2_table() !=
                                 kuaishou::ad::TraceLogSamplingFlag::SKIP_SAMPLING ||
                             ad_request_base_info->ad_request_extend().is_flink_task_drop())) {
    BuildRankStageInfo(context_data, rank_stage_info);
    CleanExcludedField(&ad_rank_simplify_trace_log);
    trace_str = std::move(ad_rank_simplify_trace_log.SerializeAsString());
    is_ad_data_filled = true;
  }
  if (is_send_v2_data) {
    auto ret = v2_table_producer_.Produce(trace_str);
    if (ret != ks::ad_base::AdKafkaStatus::SUCCESS) {
      context_data->dot_perf->Count(1, "ad_rank_simplify_trace_log_v2_error");
    } else {
      context_data->dot_perf->Count(1, "ad_rank_simplify_trace_log_v2_suc");
    }
  }
}
void AdRankSimplifyTraceLogManager::BuildRequestBaseInfo(
                      const kuaishou::ad::AdRequest &ad_request,
                      const AdEngineTraceType& trace_type,
                      ContextData *context_data,
                      kuaishou::ad::AdRequestBaseInfo *ad_request_base_info) {
  int64_t current_time_stamp = base::GetTimestamp();
  ad_request_base_info->set_user_id(ad_request.ad_user_info().id());
  ad_request_base_info->set_llsid(ks::ad_base::GetLlsid(ad_request));
  ad_request_base_info->set_page_id(ks::ad_base::GetPageId(ad_request));
  auto sub_page_id = is_universe_tiny_flow_? 19000002 : ks::ad_base::GetSubPageId(ad_request);
  ad_request_base_info->set_sub_page_id(sub_page_id);
  ad_request_base_info->set_pos_id(ks::ad_base::GetMediumPosId(ad_request));
  ad_request_base_info->set_app_id(ks::ad_base::GetRequestAppId(ad_request));
  ad_request_base_info->set_timestamp(current_time_stamp / 1000);
  ad_request_base_info->set_ad_request_flow_type(ad_request.ad_request_flow_type());
  ad_request_base_info->mutable_host_info()->set_service_stage(getenv("KWS_SERVICE_STAGE"));
  ad_request_base_info->mutable_host_info()->set_service_region(getenv("KWS_SERVICE_REGION"));
  ad_request_base_info->mutable_host_info()->set_host_name(serving_base::GetHostName());
  ad_request_base_info->set_platform(ad_request.ad_user_info().platform());
  ad_request_base_info->set_interactive_form(ad_request.interactive_form());
  ad_request_base_info->set_reco_client_browse_type(ad_request.reco_request_info().browse_type());
  ad_request_base_info->set_medium_attribute(ks::ad_base::GetMediumAttribute(ad_request));
  ad_request_base_info->set_medium_uid(ad_request.universe_ad_request_info().medium_uid());
  ad_request_base_info->set_device_id(ad_request.ad_user_info().device_id());
  ad_request_base_info->set_age(ad_request.ad_user_info().age());
  ad_request_base_info->set_gender(ks::ad_base::GenderConvert(ad_request.ad_user_info().gender()));
  if (RankKconfUtil::enableArchimedesStandaloneTrace() &&
        ad_utility::any_of(FLAGS_ksp_group_deploy_type, +ad_base::DeployType::archimedes)) {
    ad_request_base_info->set_service_deploy_name(kuaishou::ad::ServiceDeployName::AD_RANK_ARCHIMEDES);
  } else {
    ad_request_base_info->set_service_deploy_name(kuaishou::ad::ServiceDeployName::AD_RANK_UNIVERSE);
  }
  ad_request_base_info->set_trace_log_sampling_flag(ad_request.trace_log_sampling_flag());
  if (trace_type == AdEngineTraceType::PV_DATA_ONLY) {
    ad_request_base_info->set_is_only_write_request_data(true);
  }
  if (context_data->work_flow_type == static_cast<int64_t>(ks::ad_base::WorkFlowType::RandomWork)) {
    ad_request_base_info->set_explore_strategy(static_cast<int>(kuaishou::ad::RANDOM_EXPLORE_STRATEGY));
  } else {
    ad_request_base_info->set_explore_strategy(static_cast<int>(kuaishou::ad::DEFAULT_EXPLORE_STRATEGY));
  }
  auto& request_imp_infos = context_data->pos_manager_base.request_imp_infos;
  for (auto& request_imp : request_imp_infos) {
    int32 ad_style = request_imp.ad_style;
    if (context_data->IsUniverseTraffic()) {
      ad_request_base_info->set_union_ad_style(ad_style);
      break;
    }
  }
  // 写入耗时信息
  ad_request_base_info->mutable_perf_info()->set_server_time_cost_ms(
      (current_time_stamp - context_data->start_ts) / 1000);

  // 写入 node 耗时信息，最大写入 50 个，做个简单的兜底
  if (SPDM_enableNodeTimeCost()) {
    for (int i = 0; i < context_data->node_time_cost.size() && i < 50; i++) {
      auto* node = ad_request_base_info->mutable_perf_info()->add_node_cost();
      node->CopyFrom(context_data->node_time_cost[i]);
    }

    for (int i = 0; i < context_data->downstream_time_cost.size() && i < 5; i++) {
      auto* downstream = ad_request_base_info->mutable_perf_info()->add_down_stream_service_status();
      downstream->CopyFrom(context_data->downstream_time_cost[i]);
    }
    if (ad_request_base_info->perf_info().server_time_cost_ms() >= SPDM_perfNodeCostLatency()) {
      // 聚合相同 type 的耗时
      auto name_mapping = RankKconfUtil::perfNodeCostLatencyMergeName();
      std::map<std::string, kuaishou::ad::PerfInfo::NodeTimeCost> merged_ns_cost;
      for (const auto &ns : context_data->node_time_cost) {
        std::string name = "AdRankNodeType_" + std::to_string(ns.node_type());
        if (kuaishou::ad::AdRankNodeType_IsValid(ns.node_type())) {
          name = kuaishou::ad::AdRankNodeType_Name(ns.node_type());
        }
        auto it = merged_ns_cost.find(name);
        if (it == merged_ns_cost.end()) {
          it = merged_ns_cost.insert(std::make_pair(name,
              kuaishou::ad::PerfInfo::NodeTimeCost())).first;
        }
        it->second.set_time_cost_ms(it->second.time_cost_ms() + ns.time_cost_ms());

        // 聚合到配置归一的名字上
        auto tmp = name_mapping->find(name);
        if (tmp != name_mapping->end()) {
          std::string mapping_name = tmp->second + ".merged";
          it = merged_ns_cost.find(mapping_name);
          if (it == merged_ns_cost.end()) {
            it = merged_ns_cost.insert(std::make_pair(mapping_name,
                kuaishou::ad::PerfInfo::NodeTimeCost())).first;
          }
          it->second.set_time_cost_ms(it->second.time_cost_ms() + ns.time_cost_ms());
        }
      }
      context_data->dot_perf->Interval(ad_request_base_info->perf_info().server_time_cost_ms(),
                              "bad_latency", "total_cost");
      for (const auto &ns : merged_ns_cost) {
        context_data->dot_perf->Interval(ns.second.time_cost_ms(), "bad_latency", ns.first);
      }
    }
  }

  {
    // ad request base extend
    auto* ad_request_extend = ad_request_base_info->mutable_ad_request_extend();
    if (context_data->is_universe_tiny_flow &&
        ks::engine_base::AdKconfUtil::enableUniverseTinyAddSearchQueryToTraceLog()) {
      is_universe_tiny_flow_ = true;
      ad_request_extend->set_search_query(ad_request.universe_ad_request_info().search_query());
    } else {
      is_universe_tiny_flow_ = false;
    }
    ad_request_extend->set_is_flink_task_drop(ks::ad_base::IsEngineSendTraceLogV2(
        context_data->user_id, ad_request.ad_request_flow_type(), trace_type,
        ks::ad_base::AdEngineTraceServiceType::AdRankServer));
  }

  if (context_data->enable_print_factor_info) {
    // ad_formula_info
    for (auto& formula : context_data->formula_info) {
      auto *formula_info = ad_request_base_info->add_ad_formula_info();
      formula_info->set_key(formula.first);
      formula_info->set_belonged(formula.second);
    }
  }
}

void AdRankSimplifyTraceLogManager::SendCreativeDebugFilterColor(ContextData *context_data) {
  if (context_data->rank_request->ad_request().debug_param().creative_id_size() <= 0) {
    return;
  }
  auto build_ad_list_item = [&](AdList &ad_list, bool filter_valid_ad) {
    for (auto *ad : ad_list.FullAds()) {
      SendCreativeDebugFilterColor(context_data, *ad);
    }
  };
  build_ad_list_item(context_data->ad_list, false);
}

void AdRankSimplifyTraceLogManager::SendCreativeDebugFilterColor(
      ContextData *context_data, const AdCommon &ad) {
  if (ad.GetValid()) {
    return;
  }
  if (context_data->rank_request->ad_request().debug_param().creative_id(0) !=
      ad.get_creative_id()) {
    return;
  }
  if (static_cast<kuaishou::log::ad::AdTraceFilterCondition>(ad.ad_filter_stage_info.filter_condition)
       == kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION) {
    return;
  }
  ks::engine_base::SendCreativeDebugFilterColor(
      context_data->rank_request->ad_request(),
      context_data->rank_request->ad_request().debug_param().creative_id(0),
      static_cast<kuaishou::log::ad::AdTraceFilterCondition>(ad.ad_filter_stage_info.filter_condition));
}

void AdRankSimplifyTraceLogManager::BuildRankStageInfo(ContextData *context_data,
                                                       kuaishou::ad::RankStageInfo *rank_stage_info) {
  std::unordered_map<kuaishou::ad::AdTraceDspType, int64> dsp_type_all_list;
  std::unordered_map<kuaishou::ad::AdTraceDspType, int64> dsp_type_alive_list;
  std::unordered_map<int64, int64> first_industry_alive_list;
  std::unordered_map<kuaishou::ad::AdActionType, int64> ocpc_action_type_alive_list;
  int64_t alive_result_num = 0;
  auto statistics_num = [&](AdCommon *ad) {
    kuaishou::ad::AdTraceDspType dsp_type = GetAdDspType(*ad);
    dsp_type_all_list[dsp_type] += 1;
    if (ad->GetValid()) {
      alive_result_num += 1;
      dsp_type_alive_list[dsp_type] += 1;
      first_industry_alive_list[ad->get_industry_parent_id_v3()] += 1;
      ocpc_action_type_alive_list[ad->get_ocpx_action_type()] += 1;
      context_data->dot_perf->Interval(ad->ad_price.rank_benifit, "rank_dryrun_alive_rank_benifit");
      if (dsp_type == kuaishou::ad::AdTraceDspType::NORMAL_FANSTOP_DSP_TYPE ||
          dsp_type == kuaishou::ad::AdTraceDspType::INNER_FANSTOP_DSP_TYPE ||
          dsp_type == kuaishou::ad::AdTraceDspType::INNER_FANSTOP_V2_DSP_TYPE ||
          dsp_type == kuaishou::ad::AdTraceDspType::ESP_MOBILE_SOFT_DSP_TYPE ||
          dsp_type == kuaishou::ad::AdTraceDspType::FLASH_PROMOTION_SOFT_DSP_TYPE ||
          dsp_type == kuaishou::ad::AdTraceDspType::SPECIALTY_PROMOTION_SOFT_DSP_TYPE) {
        context_data->dot_perf->Interval(ad->ad_price.rank_benifit, "soft_rank_dryrun_alive_rank_benifit");
      } else {
        context_data->dot_perf->Interval(ad->ad_price.rank_benifit, "hard__rank_dryrun_alive_rank_benifit");
      }
    }
  };
  auto build_ad_list_item = [&](AdList &ad_list, bool filter_valid_ad) {
    for (auto *ad : ad_list.FullAds()) {
      if (filter_valid_ad && ad->GetValid()) {
        // 针对 live 队列，只需要取里面被过滤的数据
        continue;
      }
      // 划分队列导致的 invalid 不记录 trace
      if (!ad->IsTrace()) {
        continue;
      }
      statistics_num(ad);
      BuildRankItemInfo(context_data, *ad, rank_stage_info->add_ad_item_info());
    }
  };
  build_ad_list_item(context_data->ad_list, false);

  // 记录预估值相关信息
  if (RankKconfUtil::enableAdPredictStatLog()) {
    for (auto iter = context_data->ad_predict_stats.begin();
                              iter != context_data->ad_predict_stats.end(); iter++) {
      auto* ad_predict_stat = rank_stage_info->add_ad_predict_stat();
      ad_predict_stat->CopyFrom(iter->second.stat);
      ad_predict_stat->set_predict_count(iter->second.total_predict_item_count);
      if (iter->second.total_predict_item_count > 0) {
        ad_predict_stat->set_avg_score(iter->second.total_predict_score / iter->second.total_predict_item_count);  // NOLINT
      }
    }

    for (auto iter = context_data->ad_predict_stats_.begin();
         iter != context_data->ad_predict_stats_.end(); iter++) {
      auto* ad_predict_stat = rank_stage_info->add_ad_predict_stat();
      ad_predict_stat->CopyFrom(iter->second);
      if (ad_predict_stat->predict_count() > 0) {
        ad_predict_stat->set_avg_score(ad_predict_stat->avg_score() /
                                       ad_predict_stat->predict_count());
      }
    }
  }

  {
    // 进行一些打点
    for (auto item = dsp_type_all_list.begin(); item != dsp_type_all_list.end(); ++item) {
      context_data->dot_perf->Interval(item->second, "ad_list_all_num",
                                       kuaishou::ad::AdTraceDspType_Name(item->first),
                                       "0");
      if (item->first == kuaishou::ad::AdTraceDspType::NORMAL_FANSTOP_DSP_TYPE ||
          item->first == kuaishou::ad::AdTraceDspType::INNER_FANSTOP_DSP_TYPE ||
          item->first == kuaishou::ad::AdTraceDspType::INNER_FANSTOP_V2_DSP_TYPE ||
          item->first == kuaishou::ad::AdTraceDspType::ESP_MOBILE_SOFT_DSP_TYPE ||
          item->first == kuaishou::ad::AdTraceDspType::FLASH_PROMOTION_SOFT_DSP_TYPE ||
          item->first == kuaishou::ad::AdTraceDspType::SPECIALTY_PROMOTION_SOFT_DSP_TYPE) {
        context_data->dot_perf->Interval(item->second, "soft_ad_list_all_num", "0");
      } else {
        context_data->dot_perf->Interval(item->second, "hard_ad_list_all_num", "0");
      }
    }
    for (auto item = dsp_type_alive_list.begin(); item != dsp_type_alive_list.end(); ++item) {
      context_data->dot_perf->Interval(item->second, "ad_list_alive_num",
                                       kuaishou::ad::AdTraceDspType_Name(item->first),
                                       "0");
    }
    for (auto item = first_industry_alive_list.begin(); item != first_industry_alive_list.end(); ++item) {
      context_data->dot_perf->Interval(item->second * 1000000, "rank_dryrun_first_industry_size",
                                       base::IntToString(item->first));
    }
    for (auto item = ocpc_action_type_alive_list.begin(); item != ocpc_action_type_alive_list.end(); ++item) {
      context_data->dot_perf->Interval(item->second * 1000000, "rank_dryrun_ocpc_action_type_size",
                                       kuaishou::ad::AdActionType_Name(item->first));
    }
    context_data->dot_perf->Interval(alive_result_num * 1000000, "rank_dryrun_ad_list_alive_num");
    context_data->dot_perf->Count(1, "rank_dryrun_ad_rank_result_num",
                                  base::IntToString(alive_result_num > 0));
  }
}
void AdRankSimplifyTraceLogManager::BuildRankItemInfo(
    ContextData *context_data, const AdCommon &ad,
    kuaishou::ad::RankStageInfo::AdTraceItemInfo *ad_item_info) {
  if (ad.GetValid()) {
    ad_item_info->set_is_alive(true);
    context_data->dot_perf->Count(
        1, "ad_rank_valid_ad_num",
        kuaishou::log::ad::AdTraceFilterCondition_Name(static_cast<kuaishou::log::ad::AdTraceFilterCondition>(
            ad.ad_filter_stage_info.filter_condition)));
  } else {
    context_data->dot_perf->Count(
        1, "ad_rank_invalid_ad_num",
        kuaishou::ad::AdRankNodeType_Name(ad.ad_filter_stage_info.node_stage_type),
        kuaishou::ad::AdRankPointType_Name(ad.ad_filter_stage_info.point_stage_type),
        kuaishou::ad::AdRankPluginType_Name(ad.ad_filter_stage_info.plugin_stage_type),
        kuaishou::log::ad::AdTraceFilterCondition_Name(static_cast<kuaishou::log::ad::AdTraceFilterCondition>(
            ad.ad_filter_stage_info.filter_condition)));
    ad_item_info->set_is_alive(false);
    auto *ad_filter_info = ad_item_info->mutable_ad_filter_info();
    ad_filter_info->set_filter_reason(
        static_cast<kuaishou::log::ad::AdTraceFilterCondition>(ad.ad_filter_stage_info.filter_condition));
    ad_filter_info->set_node_stage_type(static_cast<kuaishou::ad::AdRankNodeType>
                                        (ad.ad_filter_stage_info.node_stage_type));
    ad_filter_info->set_point_stage_type(ad.ad_filter_stage_info.point_stage_type);
    ad_filter_info->set_plugin_stage_type(ad.ad_filter_stage_info.plugin_stage_type);
  }
  // ad_base_info
  {
    auto *ad_base_info = ad_item_info->mutable_ad_base_info();
    ad_base_info->set_account_id(ad.get_account_id());
    ad_base_info->set_campaign_id(ad.get_campaign_id());
    ad_base_info->set_unit_id(ad.get_unit_id());
    ad_base_info->set_creative_id(ad.get_creative_id());
    ad_base_info->set_photo_id(ad.get_photo_id());
    ad_base_info->set_live_stream_id(ad.get_live_stream_id());
    ad_base_info->set_item_type(ad.get_item_type());
    ad_base_info->set_industry_level_1_id(ad.get_first_industry_id_v5());
    ad_base_info->set_industry_level_2_id(ad.get_second_industry_id_v5());
    if (ad.get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE ||
        ad.get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
      ad_base_info->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_LIVE);
    } else if (ad.get_live_creative_type() ==
                   kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE ||
               ad.get_live_creative_type() ==
                   kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
      ad_base_info->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE);
    }
    ad_base_info->set_ad_dsp_type(GetAdDspType(ad));
    ad_base_info->set_first_industry_id(ad.get_industry_parent_id_v3() >= 0 ?
                                        ad.get_industry_parent_id_v3() : 0);
    ad_base_info->set_multi_retrieval_tag(ad.get_multi_retrieval_tag());
    ad_base_info->set_new_creative_tag(ad.get_new_creative_tag());
    ad_base_info->set_campaign_type(ad.get_campaign_type());
    ad_base_info->set_target_id(ad.get_target_id());
    ad_base_info->set_cover_id(ad.get_cover_id());
    ad_base_info->set_account_type(ad.get_account_type());
    ad_base_info->set_campaign_promotion_type(ad.get_promotion_type());
    ad_base_info->set_ad_source_type(ad.get_ad_source_type());
    ad_base_info->set_adx_source_type(ad.get_adx_source_type());
    ad_base_info->set_agent_id(ad.get_agent_id());
    ad_base_info->set_ad_queue_type(GetAdQueueType(ad, *context_data));
    ad_base_info->set_merchant_product_id(std::to_string(ad.get_parsed_item_id()));
    ad_base_info->set_category_level_1_id(ad.get_category_level_1_id());
    ad_base_info->set_category_level_2_id(ad.get_category_level_2_id());
    ad_base_info->set_category_level_3_id(ad.get_category_level_3_id());
    ad_base_info->set_product_name(ad.base_np.product_name);
    // ad_base_info->set_is_pt_union();
    ad_base_info->set_author_id(ad.get_author_id());
    if (ad.is_inner_loop_ad()) {
      for (int32_t tag : ad.base_np.crowd_tag) {
        ad_base_info->add_crowd_tag(tag);
      }
    }
    ad_base_info->set_kol_user_type(ad.get_kol_user_type());
    ad_base_info->set_is_outer_loop_native(ad.get_is_outer_loop_native());
    ad_base_info->set_native_strict_status(ad.get_native_strict_status());
  }
  // ad_bid_info
  {
    auto *ad_bid_info = ad_item_info->mutable_ad_bid_info();
    ad_bid_info->set_auto_cpa_bid(ad.get_auto_cpa_bid());
    ad_bid_info->set_cpa_bid(ad.get_cpa_bid());
    ad_bid_info->set_deep_cpa_bid(ad.get_deep_cpa_bid());
    ad_bid_info->set_auto_deep_cpa_bid(ad.get_auto_deep_cpa_bid());
    ad_bid_info->set_bid_type(ad.get_bid_type());
    ad_bid_info->set_ocpc_action_type(ad.get_ocpx_action_type());
    ad_bid_info->set_ocpc_deep_bid_type(ad.get_deep_bid_type());
    ad_bid_info->set_deep_conversion_type(ad.get_deep_conversion_type());
    ad_bid_info->set_ocpc_bid_modify_tag(ad.get_auto_cpa_bid_modify_tag());
    ad_bid_info->set_auction_bid(ad.get_auction_bid());
    ad_bid_info->set_bid(ad.get_bid());
    ad_bid_info->set_roi_ratio(ad.get_roi_ratio());
    ad_bid_info->set_auto_roas(ad.get_auto_roas());
    ad_bid_info->set_ecpc_adjust_ratio(1.0);
    ad_bid_info->set_ecpc_adjust_tag(0);
    ad_bid_info->set_speed_type(ad.get_speed());
  }
  // ad_cmp_info
  {
    auto* ad_benifit_info = ad_item_info->mutable_ad_benifit_info();
    ad_benifit_info->set_cpm(ad.ad_price.cpm);
    ad_benifit_info->set_rank_benifit(ad.ad_price.rank_benifit);
    ad_benifit_info->set_origin_cpm(ad.ad_price.origin_cpm);  // 自动调参前的 cpm
  }
  // ad_cxr_info
  {
    static int64_t cxr_ratio = 1000000;
    auto *ad_cxr_info = ad_item_info->mutable_ad_cxr_info();
    ad_cxr_info->mutable_ctr_info()->set_value(ad.GetUnifyCtrInfo().value * cxr_ratio);
    ad_cxr_info->mutable_ctr_info()->set_start_type(ad.GetUnifyCtrInfo().s_type);
    ad_cxr_info->mutable_ctr_info()->set_end_type(ad.GetUnifyCtrInfo().e_type);
    ad_cxr_info->mutable_ctr_info()->set_unify_tag(static_cast<int32_t>(ad.GetUnifyCtrInfo().r_tag));
    for (auto& cmd_id : ad.GetUnifyCtrInfo().cmd_id_list) {
      ad_cxr_info->mutable_ctr_info()->add_cmd_ids(cmd_id);
    }

    ad_cxr_info->mutable_cvr_info()->set_value(ad.GetUnifyCvrInfo().value * cxr_ratio);
    ad_cxr_info->mutable_cvr_info()->set_start_type(ad.GetUnifyCvrInfo().s_type);
    ad_cxr_info->mutable_cvr_info()->set_end_type(ad.GetUnifyCvrInfo().e_type);
    ad_cxr_info->mutable_cvr_info()->set_unify_tag(static_cast<int32_t>(ad.GetUnifyCvrInfo().r_tag));
    for (auto& cmd_id : ad.GetUnifyCvrInfo().cmd_id_list) {
      ad_cxr_info->mutable_cvr_info()->add_cmd_ids(cmd_id);
    }

    ad_cxr_info->mutable_deep_cvr_info()->set_value(ad.GetUnifyDeepCvrInfo().value * cxr_ratio);
    ad_cxr_info->mutable_deep_cvr_info()->set_start_type(ad.GetUnifyDeepCvrInfo().s_type);
    ad_cxr_info->mutable_deep_cvr_info()->set_end_type(ad.GetUnifyDeepCvrInfo().e_type);
    ad_cxr_info->mutable_deep_cvr_info()->set_unify_tag(static_cast<int32_t>(ad.GetUnifyDeepCvrInfo().r_tag));
    for (auto& cmd_id : ad.GetUnifyDeepCvrInfo().cmd_id_list) {
      ad_cxr_info->mutable_deep_cvr_info()->add_cmd_ids(cmd_id);
    }

    ad_cxr_info->mutable_ltv_info()->set_value(ad.GetUnifyLtvInfo().value * cxr_ratio);
    ad_cxr_info->mutable_ltv_info()->set_start_type(ad.GetUnifyLtvInfo().s_type);
    if (SPDM_enableFixUniverseLtvEndType()) {
      ad_cxr_info->mutable_ltv_info()->set_end_type(ad.GetUnifyLtvInfo().e_type);
    } else {
      ad_cxr_info->mutable_ltv_info()->set_start_type(ad.GetUnifyLtvInfo().e_type);
    }
    ad_cxr_info->set_event_7day_pay_times(ad.get_predict_score(engine_base::PredictType::PredictType_7_day_pay_times));  // NOLINT
  }

  // ad_extend_info
  {
    auto *ad_extend_info = ad_item_info->mutable_ad_extend_info();
    for (const auto cmd_id : ad.getReqCmdIds()) {
      ad_extend_info->add_cmd_id(cmd_id);
    }

    auto* p_ad = const_cast<AdCommon*>(&ad);
    auto& cmd_key_id_list = p_ad->getReqCmdKeyIdsList();
    auto& cmd_id_list = p_ad->getReqCmdIdsList();
    if (cmd_key_id_list.size() == cmd_id_list.size()) {
      for (int i = 0; i < cmd_key_id_list.size() && i < cmd_id_list.size(); i++) {
        auto* model_meta = ad_extend_info->add_model_meta_list();
        model_meta->set_cmd_key_id(cmd_key_id_list[i]);
        model_meta->set_cmd_id(cmd_id_list[i]);
      }
    }

    if (RankKconfUtil::enableNewPredictFill()) {
      auto* p_ad = const_cast<AdCommon*>(&ad);
      if (p_ad != nullptr) {
        p_ad->FillPredictScoreLog(ad_extend_info);
      }
    }
    ad_extend_info->set_is_fan_follow(ad.get_is_fan_follow());
    ad_extend_info->set_multi_overlay_tag(ad.get_multi_overlay_tag());
    ad_extend_info->set_multi_overlay_tag_extend(ad.get_multi_overlay_tag_extend());
    ad_extend_info->set_delivery_rate(ad.get_prerank_delivery_rate());
    ad_extend_info->set_retrieval_post_score(ad.get_retrieval_post_score());
    ad_extend_info->set_prerank_score(ad.get_prerank_score());
    ad_extend_info->set_rta_source_type(ad.get_rta_source_type());
    ad_extend_info->set_rta_ratio(ad.get_rta_ratio());  // rta 一次出价系数
    ad_extend_info->set_rta_bid_ratio(ad.get_rta_ratio());  // rta 一次出价系数
    ad_extend_info->set_rta_ratio_second_bid(ad.get_rta_ratio_second_bid());  // rta 二次出价系数
    ad_extend_info->set_rta_bid_ratio_second_bid(ad.get_rta_ratio_second_bid());  // rta 二次出价系数
    ad_extend_info->set_rta_sta_tag(ad.get_rta_sta_tag());
    ad_extend_info->set_rta_bid(ad.get_rta_bid_prerank());  // rta 一次出价
    ad_extend_info->set_rta_bid_second_bid(ad.get_rta_bid());  // rta 二次出价
    ad_extend_info->set_rta_trace_id(ad.get_rta_trace_req_id());  // rta trace id
    ad_extend_info->set_ad_strategy_tag(ad.get_ad_strategy_tag());
    ad_extend_info->set_live_start_gap(base::GetTimestamp() / 1000 - ad.get_live_start_ts());
    ad_extend_info->set_photo_quality_score(ad.get_ori_photo_quality_score());  //设置视频质量分字段
    ad_extend_info->set_ad_monitor_type(GetAdMonitorType(ad));
    ad_extend_info->set_prerank_append_tag(ad.get_prerank_append_tag());  // 粗排爬坡策略标记
    ad_extend_info->set_prerank_append_group(ad.get_prerank_append_group());  // 粗排爬坡组标记
    if (is_universe_tiny_flow_) {
      ad_extend_info->set_universe_tiny_query_type(ad.get_universe_tiny_query_type());
    }
    if ((ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
                ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_T7_ROI ||
                ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS ||
                ad.get_ocpx_action_type() == kuaishou::ad::AdActionType::AD_MERCHANT_FOLLOW_ROI)) {
      ad_extend_info->set_rank_ctcvr(ad.GetUnifyCtrInfo().value * ad.GetUnifyCvrInfo().value *
                                     ad.GetUnifyLtvInfo().value / 10);
    } else {
      ad_extend_info->set_rank_ctcvr(ad.GetUnifyCtrInfo().value * ad.GetUnifyCvrInfo().value);
    }
    ad_extend_info->set_rta_ratio(ad.get_rta_ratio());
    ad_extend_info->set_rta_ratio_second_bid(ad.get_rta_ratio_second_bid());
  }

  // ad_strategy_info
  auto *ad_strategy_info = ad_item_info->mutable_ad_strategy_info();
  for (const auto& formula_info : ad.ad_formula_info) {
    auto* ad_formula_info = ad_strategy_info->add_ad_formula_info();
    ad_formula_info->set_formula_type(formula_info.first);
    ad_formula_info->set_value(formula_info.second);
  }
  for (const auto& formula_factor_info : ad.ad_factor_info) {
    for (const auto& factor_info : formula_factor_info.second) {
      auto& info = factor_info.second;
      auto* ad_factor_info = ad_strategy_info->add_ad_factor_info();
      ad_factor_info->set_formula_type(info.formula_type);
      ad_factor_info->set_factor_type(info.factor_type);
      ad_factor_info->set_is_hit(info.is_hit);
      ad_factor_info->set_value(info.value);
    }
  }

  // if (context_data->enable_print_factor_info) {
  if (is_print_factor_info_) {
    // ad_factor_info
    for (auto& factor : ad.factor_info) {
      auto *factor_info = ad_item_info->add_ad_factor_info();
      factor_info->set_key(factor.key);
      factor_info->set_admit(factor.admit);
      factor_info->set_value(factor.value);
    }
  }
}

int AdRankSimplifyTraceLogManager::GetAdMonitorType(const AdCommon &ad) {
  if (ad.is_inner_loop_ad()) {
    return kuaishou::ad::AdEnum_AdMonitorType_INNER_LOOP_DEFAULT;
  }
  return kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT;
}

AdEngineTraceType AdRankSimplifyTraceLogManager::GetTraceType(const ContextData& context_data) const {
  // 测试环境 & 开关打开
  if (RankKconfUtil::enableDryrunLog() && ks::ad_base::AdKessClient::Instance().IsTestEnv()) {
      return AdEngineTraceType::ALL_DATA;
  }

  if (context_data.rank_request == nullptr) {
    return AdEngineTraceType::SKIP;
  }
  const auto& ad_request = context_data.rank_request->ad_request();

  if (ad_request.trace_log_sampling_flag() == kuaishou::ad::FORCE_SAMPLING) {
    return AdEngineTraceType::ALL_DATA;
  }
  if (ad_request.trace_log_sampling_flag() == kuaishou::ad::SELECTIVE_SAMPLING) {
    return AdEngineTraceType::ALL_DATA;
  }
  if (RankKconfUtil::disablePvDataTraceLog()) {
    return AdEngineTraceType::SKIP;
  } else {
    return AdEngineTraceType::PV_DATA_ONLY;
  }
}

kuaishou::ad::AdEnum::AdQueueType AdRankSimplifyTraceLogManager::GetAdQueueType(
    const AdCommon &ad, const ContextData& context_data) const {
  return ad.get_ad_queue_type();
}

}  // namespace ad_rank
}  // namespace ks
