#pragma once

#include "teams/ad/ad_base/src/better_enums/better_enums.h"

constexpr int32_t KMAXTAGID = 448;

namespace ks {
namespace ad_target {
namespace multi_retr {

BETTER_ENUM(
    RetrievalTag,
    int32_t,
    UNKNOWN = 0,
    RETRIEVAL_COLD_START_POST = 1,
    RETRIEVAL_XY_LPS = 2,  // 直营迁移小店暂用
    RETRIEVAL_DELIVERY = 3,
    RETRIEVAL_ECPM = 4,
    RETRIEVAL_TARGET_EXPAND = 5,
    UNIVERSE_ADVERTISER_INDUSTRY_LIUX = 6,
    YC_U2S22I = 7,  // author: yechen05, 功能：s2 召回
    RETRIEVAL_ZXM_ITEM_CLICK = 8,
    HZK_MOBILE_ASSISTED_RETRIEVAL = 9,  // author: huangzhaokai  功能：软广参竞助攻召回  // NOLINT
    SC_RETRIEVAL_SMART_MIX = 10,  // author: shenchong03, 功能：智能 mix 召回
    NEW_CREATIVE_POST = 11,
    SC_RETRIEVAL_SMART_ALL = 12,  // author: shench<PERSON>03, 功能: 智能定向召回技术延伸
    SC_SMART_TOOL_PAY = 13,       // author: shenchong03, 功能：智能定向付费单出价
    RETRIEVAL_DELIVERY_COLD_START = 14,
    XDT_DELIVERY_RETR = 15,
    MEDIUM_POST_CPM = 16,
    RETRIEVAL_DELIVERY_INDUSTRY = 17,
    RETRIEVAL_NM_LIVE_STREAM_HQ = 18,
    RETRIEVAL_XY_CONV = 19,
    JK_ALIOUTER_MMU_TAG_RETRIEVAL = 20,  // author: huangxin07, 功能：商家外投 MMU 类目召回
    UNIVERSE_GREEN_CHANNEL = 21,
    GAME_RETARGET_AD = 22,  // author: rongyu03, 功能：游戏广告重定向召回
    GAME_RETARGET_RECO = 23,  // author: rongyu03, 功能：游戏推荐重定向召回
    RETRIEVAL_UNIVERSE_POST_ECPM = 24,
    U2U2I_RETR = 25,
    U2I2I_RETR = 26,
    POST_AUCTION_RETR_LN = 27,
    MATERIAL_EMBEDDING_RETR = 28,
    SC_RETRIEVAL_CONV = 29,  // author: shenchong03, 功能：大盘表单召回
    CHY_RECO_RETARGET = 30,  // author: cuihongyi  功能: 电商重定向
    ADVANCED_PCREATIVE_OFFLINE = 31,
    PRODUCT_TAG = 32,  // author: xiedong, 功能：游戏头客追击召回独立通路(复用 PRODUCT_TAG)
    XDT_MATERIAL_EMBEDDING_RETR = 33,
    ECOM_KWD_RETR = 34,
    XDT_LIVE_GREEN_OFFLINE = 35,
    JK_RETR_ADS = 36,
    RETRIEVAL_UNIVERSE_COLD_START_POST = 37,
    ECOM_DELIVERY_RETR = 38,
    OUTER_NATIVE_FICTION_RETR = 39,  // author: zhangmengxin  功能: 外循环原生小说白盒召回
    RETRIEVAL_USER_FOLLOW_AUTHOR_EMBED = 40,
    PRODUCT_LOCAL_TAG = 41,
    RETRIEVAL_USER_CLUSTER = 42,
    RETRIVAL_USER_BY_LABEL = 43,  // author: chenwancheng03, 功能: 内循环软广 tdm 召回 粉条纯作品通路
    MERCHANT_RETARGET_OFFLINE = 44,
    RETRIEVAL_REPEATE_CLK_CREATIVE = 45,
    SMART_TOOL_ADS = 46,
    YC_LBS = 47,  // author: yechen05, 功能：LBS 召回
    AGGREGATE_SECOND_REQUEST = 48,  // author: liuzichen05, 功能: 聚合竞价二次请求强出召回
    RETRIEVAL_LJJ_CREDIT_GRANT = 49,
    XDT_FEATURE_DELIVERY = 50,
    RETRIEVAL_UNIVERSE_MEDIUM_USER_POST = 51,
    XDT_DELIVERY_RETR_E2E_WSY = 52,
    RETRIEVAL_SINGLE_INDUSTRY_CXA = 53,
    RETRIEVAL_SEARCH_CONTENT_CXA = 54,
    RETRIEVAL_CLICK2_CXA = 55,
    UNIVERSE_MONOPOLIZE_CHANNEL_GJW = 56,
    MERCHANT_RETRIEVAL_DELIVERY_DLM = 57,
    MERCHANT_RETRIEVAL_ECPM_DLM = 58,
    MERCHANT_RETRIEVAL_TARGET_EXPAND_DLM = 59,
    MERCHANT_RETRIEVAL_DELIVERY_COLD_START_DLM = 60,
    MERCHANT_RETRIEVAL_DELIVERY_INDUSTRY_DLM = 61,
    CHY_REALTIME_AUTHOR = 62,   // author: cuihongyi  功能: 实时追投召回
    RETRIEVAL_PURE_NEW_CREATIVE_TWH = 63,
    ECOM_DELIVERY_RETR_E2E_WSY = 64,
    CHY_KEYWORD_RETARGET = 65,  // author: cuihongyi  功能: 关键词重定向召回
    RETRIEVAL_P3S_WJW = 66,
    RETRIEVAL_P5S_WJW = 67,
    RETRIEVAL_PEND_WJW = 68,
    CHY_SIYU_ORIENT = 69,   // author: cuihongyi  功能: 私域定向找回
    MERCHANT_RETRIEVAL_FANS_RETRIEVAL_GW = 70,
    RETRIEVAL_UNIVERSE_PURCHASE_POST_ECPM_WL = 71,
    ZYH_USER_INSTALLED_APP = 72,
    ZYH_USER_INDUSTRY_POST = 73,
    TWH_COLD_START_EMBEDDING_REPLACE = 74,     // author: tanweihan, 功能: 新创意 emb 替换召回
    YYX_UNIVERSE_INNER_SHALLOW_MEDIA = 75,     // author: yingyuanxiang, 功能: 联盟内循环浅度指定优化目标随机召回   // NOLINT
    CHY_COLOR_RETRIEVAL = 76,                  // author: cuihongyi, 功能: 种草召回
    SC_RETRIEVAL_PAY = 77,                     // author: shenchong03, 功能：大盘付费召回
    LIUX_UNIVERSE_GENERAL_POST_CPM_TABLE_V2 = 78,  // author: liuxuan05, 功能：联盟统一后验 table search
    SC_RETRIEVAL_STAY = 79,                        // author: shenchong03, 功能：大盘次留召回
    LX_REALTIME_KEYWORD = 80,                 // author: lixu05,  功能： 实时关键词召回
    LX_BRAND_CROWD = 81,                   // author: lixu05, 功能： 品牌人群召回
    LS_RETRIEVAL_UNIVERSE_POST_DATA = 82,  // author: lusuo, 功能: 联盟通用后验数据召回通路
    WLF_LIVE_USER_CLICK_AUTHOR_EMBED = 83,  // author: wanglongfu，功能：小店直播点击 author embed 召回通路
    LK_ECOM_RETR_ECPM = 84,                 // author: like07, 功能：电商 ecpm 召回
    TG_SMART_TOOL_ADS_DEEP = 85,  // author: tiangeng, 功能：智能定向深度召回队列
    SF_I2I_INFO_SEQ = 86,         // author: bieshifu, u2i2i,  用户序列信号召回
    SF_I2I_INFO_ACTION = 87,  // author: bieshifu, u2i2i,  用户行为信号召回优化，包括展开优化
    ZSY_SHUFFLE_N_ADS = 88,   // author: zhaoshuyan 功能： 随机召回 N 个定向广告
    CHY_TARGET_EXPAND = 89,           // author: cuihongyi 功能: 扩量广告召回
    LJJ_I2I_DEEP_ACTION = 90,         // author: lijunjie, 功能：基于用户后链路行为进行召回
    CXA_QUERY_PHOTO = 91,             // author: chenxian, 功能：基于 query 的召回
    CXA_MATERIAL_DECAY_RETR = 92,     // author: chenxian, 功能：素材衰减召回
    LDC_W2V_I2I = 93,                 // author: lidecong, 功能：w2v i2i 召回
    RETRIEVAL_SMART_TOOL = 94,        // author: shenchong03, 功能：智能定向召回通路
    ZMC_KNEWS_INDUSTRY_SUPPORT = 95,  // author: zhoumancang, 功能：快看点行业扶持
    WL_GENERAL_INDUSTRY_USER_TAG_RETR = 96,  // author: wanglei10, 功能：通用行业人群标签召回通路
    GJW_RETRIEVAL_PLUMMET_UNIT = 97,         // author: guojiangwei, 功能: 掉量暴力算召回
    HQ_SUPPORT_PROJECT = 98,                 // author: heqian, 功能: 起量扶持通路
    WQ_KEYWORD_COLD_START = 99,              // author: wangqi12, 功能: 关键词冷启动召回
    LJJ_RETARGET = 100,                      // author: lijunjie, 功能: 已转化用户 retarget
    PLAYLET_RETARGET_STRATEGY = 101,         // author: yangxuan06 功能：短剧重定向策略
    YXY_ROAS_ECPM_RETR = 102,                // author: yangxinyong, 功能: ROI 出价 ECPM 召回通路
    U2TAG2I_RETR_YF = 103,                   // author: yufei, 功能: 行为意向标签召回支路
    POST_AUCTION_RETR_KNEWS = 104,       // author: lijupan, 功能: 快看点历史参竞广告召回通路
    SC_SMART_TOOL_NEXTDAY_STAY = 105,    // author: shenchong03, 功能：智能定向次留召回队列
    WY_LIVE_MATERIAL = 106,              // author: wangyuan11, 功能：直播素材召回
    WQ_KEYWORD_CROWD_DETECT = 107,       // author: wangqi12, 功能：关键词人群探索
    SC_SMART_TOOL_ROAS = 108,            // author: shenchong03, 功能：智能定向 roas 召回队列
    U2U2I_GRAPH = 109,                   // author: hanwenying, U2U2I 召回通路
    LJJ_HIGH_QUALITY_RETR = 110,         // author: lijunjie, 功能: 优质素材单独召回通路
    KUAISHOU_TOP_COST_UNIT = 111,        // author: yudongjin, 功能：主站头部消耗 unit 召回通路
    UNIVERSE_LIVE_CJ_CID_CHANNEL = 112,  // author: xiemiao, 功能：联盟内循环对齐精排召回链
    CHY_LOCAL_PHOTO = 113,               // author: cuihongyi, 功能：本地短视频召回
    WY_AMD_HISTORY_AUCTION = 114,        // author: wangyuan11, 功能：小店直播历史参竞召回
    RETRIEVAL_CVR_BID_CXA = 115,         // author: chenxian, 功能：基于后链路转化召回通路
    YZ_AD_HOSTING_ECPM = 116,            // author: yuanzhun03, 功能：托管广告 ecpm 召回通路
    YZ_AD_HOSTING_WIN = 117,             // author: yuanzhun03, 功能：历史参竞托管广告召回通路
    HIGH_QUALITY_PHOTO = 118,            // author: caining, 功能：素材扶优专用召回通路
    UNIVERSE_INNER_LOOP_LIVE_FANS = 119,  // author: huangwenbin 功能: 联盟内循环直播粉丝召回
    UNIVERSE_INNER_LIVE_DP = 120,  // author: xiemiao 功能: 联盟内循环浅度直播召回链
    HNL_LOOKALIKE = 121,     // author: lihaonan03, 功能：lookalike 拓展移换到召回侧使用
    ECOM_EXPLORE_ADS = 122,  // author: lihantong, 功能：电商探索召回通路
    XTT_U2REGION2I = 123,  // author: xutaotao03, 功能：白盒反向召回, u2region2unit
    RETRIEVAL_ECOMU_COLD_START = 124,      // xiemiao，功能：联盟内循环冷启召回
    PHOTO_RECALL_U2I = 125,                // wangpeng16, 功能：photo 粒度召回 u2i
    PHOTO_RECALL_I2I = 126,                // wangpeng16, 功能：photo 粒度召回 u2i2i
    UNIVERSE_HIGH_QUALITY_LIVE_ADS = 127,  // author: yingyuanxiang, 功能：联盟优质直播广告召回
    UNIVERSE_INNER_LOOP_lIVE_CHANNEL = 128,  // author: xiemiao, 功能：联盟内循环直播召回
    LIVE_RECALL_U2A2I = 129,               // author: wangshengyu, 功能：直播 author 维度召回
    CHY_LSP = 130,                         // author: cuihongyi, 功能：磁力本地推召回
    XYY_U2REGION2I = 131,                  // author: xuyanyan, 功能: 白盒反向召回, u2region2unit
    BOYLE_COLD_START_RETRIEVAL = 132,      // author: zhangbiying, 功能：波义耳项目广告召回
    LYH_SDPA_RETARGET = 133,               // author: linyuhao03, 功能：SDPA 重定向召回
    OUTER_SOFT_NATIVE_FICTION_RETRIEVAL = 134,  // author: jiangjinling, 功能：外循环 na 小说软广扶持
    OUTER_NATIVE_FICTION_RETARGET = 135,  // author: jiangjinling, 功能：外循环 na 小说重定向召回
    WYX_SDPA_TAG_RETR = 136,            // author: wangyuxiang03, 功能：SDPA 实时标签召回
    YYX_UNIVERSE_AD4RECO_RETRIEVAL = 137,       // author: yingyuanxiang, 功能：联盟召回主站召回物料
    NON_AMD_LIVE_DIRECT_LIVE_RETRIEVAL = 138,  // author: zhangbiying, 功能：非电商直播行业直播直投召回
    UNIVERSE_DIRECT_HIGH_CVR_ADS = 139,  // author: wangye06 功能：联盟内循环优质直营广告
    UNIVERSE_AUCTION_LIST_FROM_KUAISHOU = 140,  // author: yingyuanxiang, 功能：联盟使用主站竞价队列召回
    UNIVERSE_INNER_LOOP_DIRECTlIVE_CHANNEL = 141,  // author: xiemiao, 功能：联盟内循环直播直投召回
    SDPA_ECOMM_TAG_RETR = 142,                // author: wangyuxiang03, 功能: SDPA 电商召回
    GAME_MCDA_YIFANG_TAG_RETRIEVAL = 143,  // author: zhangmengxin, 功能: 游戏一方 mcda 数据白盒召回
    GAME_MCDA_SANFANG_TAG_RETRIEVAL = 144,  // author: zhangmengxin, 功能: 游戏三方 mcda 数据白盒召回
    GAME_MMU_TAG_RETRIEVAL = 145,           // author: zhangmengxin, 功能: 游戏 mmu 数据白盒召回
    POPULATION_REVERSE_RETRIEVAL = 146,     // author: zhangmengxin, 功能: 人群包白盒反向召回
    OUTER_CROSS_IND_ENHANCE = 147,   // author: jiayalong, 功能: 外循环跨行业兴趣加强
    DH_OLP_RETARGET = 148,                  // author:dinghe03,功能：外循环行业优化方向重定向
    MESSAGE_TAG_RETRIEVAL = 149,  // author: xutaotao03, 功能：线索私信目标 本地召回
    XYY_U2POPULATION2I = 150,  // author: xuyanyan, 功能: 白盒人群包反向召回二级行业, u2population2unit

    UNIVERSE_INNER_LOOP_GREEN_CHANNEL_NEW = 151,  // author: wanglei10, 功能：联盟内循环绿通 new
    UNIVERSE_DIRECT_ECOM_MINGTOU = 152,           // author: yingyuanxiang, 功能: 联盟直营明投召回
    SDPA_INDUSTRIAL_RETRIEVAL = 153,              // author: wangyuxiang03, 功能：SDPA 行业融合召回
    WSY_INNER_OCPX_TAG_RETRIEVAL = 154,           // author:wangshengyu, 功能: LiveTarget live-ocpx 召回
    INNER_UNIVERSE_BATCH_HIGH_COST_CREATIVE = 155,  // author: wangye06 功能：联盟内循环 batch 高消耗
    INNER_UNIVERSE_BATCH_HIGH_COST_CREATIVE_RANK = 156,  // author: wangye06 功能：联盟内循环 batch 高消耗, 排序之后 // NOLINT
    XYY_U2C2C2I = 157,  // author: xuyanyan, 功能: 白盒召回-相似客户 u2corporation2corporation2i
    UNIVERSE_LIVE_HIGH_LIGHT_CHANNEL = 158,   // author: gongxiaopeng03 功能：联盟直播高光时刻倒排
    WSY_INNER_PAY_OCPX_TAG_RETRIEVAL = 159,  // author: wangshengyu 功能：LiveTarget live-ocpx pay 召回
    WSY_INNER_ROAS_OCPX_TAG_RETRIEVAL = 160,  // author: wangshengyu 功能：LiveTarget live-ocpx ROAS 召回
    YSK_MCB_RETRIEVAL = 161,      // author: yushengkai, 功能：MCB 广告召回
    DH_ECOM_RETARGET = 162,    // authou:dinghe ,功能：  电商重定向召回
    WSY_INNER_FOLLOW_OCPX_TAG_RETRIEVAL = 163,  // author: wangshengyu 功能：LiveTarget live-ocpx follow 召回
    CM_SPU_RECO_ACTION_RETRIEVAL = 164,  // author: caomeng 功能： 用户历史商品相关行为召回
    MLY_HIGH_QUALITY_FANS_TAG_RETRIEVAL = 165,  // author: maileyi 功能：高价值粉丝标签召回
    GW_UNIVERSE_PAT_TAG_RETRIEVAL = 166,      // author: gaowei03 功能：联盟 pay cvr 召回
    LX_PURCHASE_INTENTION_LIVE = 167,     // author: lixu05  功能：购物意图定向直播召回
    ZXY_BID_ADJUST_RETRIVAL = 168,      // author: zhouxinyu 功能：提价短视频召回
    YCH_HIGH_VALUE_FANS_RETRIEVAL = 169,    // author: yangchuanhua 功能：高价值粉丝 user->author id 召回
    CL_SPU_ACTION_RETRIEVAL = 170,  // author: caolin 功能： 用户历史行为 spu 召回
    CL_CATE_ACTION_RETRIEVAL = 171,  // author: caolin 功能： 用户历史行为 cate 召回
    WQ_SIMILAR_AUTHOR_RETRIEVAL = 172,  // author: wangqi12 功能： 相似达人召回
    GW_UNIVERSE_INNER_FANS_RETRIEVAL = 173,   // author: gaowei03 功能： 联盟内循环粉丝召回
    YSQ_LOW_PRICE_PRODUCT_OFFLINE = 174,  // author: yesiqi 功能： 电商低价格商品召回
    MYB_EDG_GRAPH_AUTHOR_EMB_LIVE = 175,  // author: mayanbin 功能： 基于 EDG 图学习的 emb 直播
    MYB_EDG_GRAPH_AUTHOR_EMB_PHOTO = 176,  // author: mayanbin 功能： 基于 EDG 图学习的 emb 作品
    RETRIEVAL_UNIVERSE_SHORTVIDEO_POSTERIORI_REVALL = 177,  // author: fengyajuan 功能: 联盟内循环短视频召回
    WL_UNIVERSE_TRAFFIC_WHITE_RETRIEVAL = 178,  // author: wanglei10 功能: 联盟先审后投独占召回
    HXJ_SPU_SWING_RETRIEVAL_LIVE = 179,  // author: hexijian 功能： 基于 swing 算法的商品到直播召回
    WECHAT_GAME = 180,    // author: guoqi03  功能: 微信小游戏召回
    GW_UNIVERSE_INNER_PHOTO_RETRIVAL = 181,  // author: gaowei03  功能: 联盟内循环 photo 召回
    CHY_U2U2I_PHOTO = 182,  // author: cuihongyi  功能: 内循环 U2U photo 召回
    LSP_PHOTO_AGENT_FOLLOW_RETRIEVAL = 183,  // author: tengwei  功能: 本地 photo 代投关注页召回
    CW_UNIVERSE_INNER_FANS_RETRIEVAL = 184,  // author: chenwu03  功能: 联盟内循环 fans 召回
    UNIVERSE_INNER_LIVE_RESERVE_RETR = 185,  // author: yingyuanxiang 功能: 联盟内循环直播预约广告召回
    UNIVERSE_SHORTVIDEO_PHOTO_POSTER_RETR = 186,  // author: chenwu03 功能: 联盟内循环短视频 photo 粒度召回
    HXJ_MERCHANT_FACE_RETR = 187,  // author: hexijian 功能: 内循环短视频人脸数据召回
    LSZ_AD_MERCHANT_FANS_REACH_RETR = 188,  // author: lishaozhe 功能: 商业化涨粉粉丝重触达
    CW_UNIVERSE_INNER_DIRECT_ECOM_DELIVERY_RETR = 189,  // author: chenwu03 功能: 联盟商选下发率通路
    LX_KEYWORD_LIVE = 190,  // author: lixu05  功能: 内循环关键词定向直播召回
    LX_KEYWORD_PHOTO = 191,  // author: lixu05  功能: 内循环关键词定向作品召回
    MINI_GAME_I2I_ENHANCE = 192,  // author: jiayalong 功能： 外循环联盟数据重定向
    LX_HISTORY_LIVE_TOUCH = 193,  // author: lixu05  功能: 历史直播触达涨粉再召回
    FKY_AD_APPOINTMENT_REACH_RETR = 194,  // author: fukunyang 功能: 直播预约重召回
    CHY_SHOP_ORIENT = 195,  // author: cuihongyi 功能: 私域定向召回
    CW_UNIVERSE_INNER_U2P2P_VIDEO_RETR = 196,  // author: chenwu03 功能: 联盟内循环短视频 u2p2p 召回
    CORPORATION_CEILING = 197,   // author:  guoqi03  功能： 客户天花板摸高一条龙
    CW_UNIVERSE_VIDEO_KWAI_MAIN_SUPPLEMENT = 198,  // author: chenwu03 功能: 联盟内循环短视频主战补充召回
    CW_UNIVERSE_INNER_U2TAG2I_VIDEO_RETR = 199,  // author:  yangfukang03  功能： applist 信号接入
    HZK_MOBILE_PHOTO_RETRIEVAL = 200,   // author:  huangzhaokai  功能：移动端短视频 creative 粒度召回
    MYH_POP_RECRUIT_LIVE_RETR = 201,   // author:  mayanbin  功能：外循环招聘直播间直投 creative 粒度召回
    HQ_OUTER_PURE_SOFT_RETR = 202,  // author: heqian 功能：外循环纯软广本地 unit 召回
    CW_UNIVERSE_INNER_U2U2I_VIDEO_RETR = 203,  // author: chenwu03 能: 联盟内循环短视频 u2u2i 召回
    HZK_MOBILE_HISTORY_AUCTION_RETRIEVAL = 204,  // author: yangfukang03  功能：llm 用户兴趣预测  // NOLINT
    CHY_COLORKEY = 205,  // author: cuihongyi 功能: 主站 colorkey 人群包召回
    AUTHOR_RECREATE_RETRIEVAL = 206,  // author: yuanwei09 功能: 达人素材二创召回
    INNER_CEILING = 207,   // author:  yuanwei09  功能： 内循环客户天花板摸高一条龙
    CW_UNIVERSE_KWAI_NEARLINE_RETRIEVAL = 208,  // author: chenwu03 功能: 联盟内循环短视频主 app nearline 召回  // NOLINT
    PHOTO_CEILING = 209,  // author : guoqi03 功能: 素材天花板
    CBO_RETRIEVAL = 210,  // author: tangweiqi 功能: cbo 产品
    SESSION_CACHE = 211,  // author : sunkang 功能：粗排缓存召回
    TG_CAMPAIGN_AUTO_RETR = 212,   // author:  tiangeng  功能：智投模式召回
    YYX_UNIVERSE_SPU2LIVE_RETR = 213,  // author : yingyuanxiang 功能: 联盟直播基于 SPU 的商品召回
    YYX_UNIVERSE_SPU2PHOTO_RETR = 214,  // author : yingyuanxiang 功能: 联盟短视频基于 SPU 的商品召回
    INDUSTRY_DEBUT_RETR = 215,  // author : liqinglong 功能: 行业首发本地召回
    YYX_UNIVERSE_SPU2LIVE_REALTIME = 216,   // author: yingyuanxiang 功能: 联盟直播 SPU 实时商品召回
    YYX_UNIVERSE_SPU2PHOTO_REALTIME = 217,  // author: yingyuanxiang 功能: 联盟短视频 SPU 实时商品召回
    WHF_MATURE_ORIGINAL_PHOTO = 218,  // author: wanghongfei 功能: 短视频高消耗原创素材召回
    YYX_UNIVERSE_HOT_SPU = 219,  // author: yingyuanxiang 功能: 联盟短视频热门 SPU 召回
    YYX_UNIVERSE_HOT_AUTHOR = 220,  // author: yingyuanxiang 功能: 联盟直播热门作者召回
    UNIVERSE_OUTER_INDUSTRY_STRATEGY = 221,  // author: pengzhengbo03 功能: 联盟外循环行业策略召回
    DACHANG_LIVE_FANS_STRATEGY = 222,  // author: qipeng 功能: 大场白名单粉丝召回
    SPUID_RETARGET_STRATEGY = 223,  // author: liubing05 功能: SPUID 重定向召回
    RECO_RECALL_STRATEGY = 224,  // author: zhangyeyao 功能: reco 召回结果补齐
    UNIVERSE_CID_STRATEGY = 225,  // author: yangjinhui 功能：联盟 CID 策略召回
    UNIVERSE_CID_STRATEGY_UPDATE = 226,  // author: yangjinhui 功能：联盟 CID 策略召回优化
    MERCHANT_RECALL_STRATEGY = 227,  // author: rentingyu 功能: 电商兴趣召回
    WANHE_CREATOR_STRATEGY = 228,  // author: zhaokun03 功能: 磁力万合创作者相关召回
    OUTER_FOLLOW_PAGE_NATIVE_RETR = 229,  // author: limiaochen 功能: 外循环关注页原生软广短视频召回
    OUTER_FOLLOW_PAGE_NATIVE_RETR_LIVE = 230,  // author: limiaochen 功能: 外循环关注页原生软广直播召回
    RETENTION_DAYS_RETARGET_STRATEGY = 231,  // author: libingjie 功能: 每日留存重定向策略
    OUTER_FOLLOW_PAGE_KOL_RETR = 232,  // author: limiaochen 功能: 外循环关注页 kol_user_id 短视频召回
    PARTICULAR_PRODUCTS = 233,  // author: liuzichen 功能: 特定产品召回
    XIFAN_RETRIEVAL = 234,  // author: liuzichen05 功能: 喜番短剧 APP 召回
    PRODUCT_RETARGET_AD = 235,  // author: yangfukang03 功能: 外循环行业兴趣重定向
    FRESH_OUTER_PRODUCT_EXPLORE = 236,  // author: zhangyeyao 功能: 外循环新品召回
    INDUSTRY_BOOST_RETR = 237,  // author: zhaoqilong 功能：外循环行业 boost 召回支路
    UNIVERSE_RECALL_CACHE_RETRIEVAL = 238,    // author: wanglei10, 功能: 联盟召回缓存通路
    ADX_RETARGET = 239,  // author: zengjiangwei03 功能：adx 召回
    // 开屏召回 tag 240 - 255 开屏专用, 非开屏业务请勿使用
    SPLASH_DELIVERY_PICTURE_PHOTO = 240,  // author: huangwei06,  功能: 开屏广告下发率召回
    SPLASH_HISTORY_ECPM_PICTURE_PHOTO = 241,  // author: huangwei06,  功能: 开屏广告历史参竞召回
    SPLASH_LTR_PICTURE_PHOTO = 242,  // author: huangwei06,  功能: 开屏广告 ltr 召回
    SPLASH_FANS_RETR = 243,  // author: liubing05,  功能: 开屏粉丝召回
    KAI_GAME_IAP_RETR = 244,  // author: nizhihao, 功能: 快小游 IAP 召回
    XSM_PHOTO_COST_DROP_RETRIEVAL = 245,  // author: xusimin, 功能: 掉量素材召回
    PLAYLET_AGGR_CARD_RETR = 246,  // author: liyongchang, 功能：短剧聚合大卡二次召回
    CPL_INDUSTRY_AUTHOR_RETR = 247,  // author: limiaochen, 功能：CPL 召回
    MINI_GAME_BIG_R_RETR = 248,   // author: nizhihao, 功能: 快小游 大 R 白名单召回
    XIFAN_IAA_RETRIEVAL = 249,  // author: liuzichen05, 喜番 IAA 召回
    FICTION_NOT_NA_USER_RETR = 250,   // author: zhaoyi13, 功能: NA 小说自然流量召回
    /*
      256 - 319 为 fanstop 专用 tag，非 fanstop 业务请勿使用
      具体细节请联系 @houjian/@cuiyanliang
    */
    HQ_FOLLOW_LIST = 258,                   // author: heqian, 功能: fanstop 粉丝定向离线召回
    HJ_U2I2I_MMU_143_CLASS_CLICK = 259,     // author: houjian, 功能: fanstop u2i2i mmu tag
    HJ_HUNGRY_DEGREE_UNIT_RETRIEVAL = 260,  // author: houjian, 功能: fanstop 饥渴度召回
    YFK_HUNGRY_DEGREE_RETRIEVAL = 261,  // author: yangfukang03, 功能: 新内粉保量订单召回
    JJG_BRUTE_PHOTO_RETRIEVAL = 262,        // author: jinjiaguag, 功能: 作品暴力算召回
    WY_BRUTE_LIVE_RETRIEVAL = 263,          // author: wangyang10, 功能: 直播暴力算召回
    YSW_PHOTO_RANDOM_RETRIEVAL = 264,       // author: yangshaowei03, 功能: 作品随机召回
    YSW_LIVE_RANDOM_RETRIEVAL = 265,        // author: yangshaowei03, 功能: 直播随机召回
    ZH_U2I2I_MMU_143_CLASS_LIKE = 266,      // author: zhaohao03, 功能: fanstop u2i2i mmu 143 like tag
    ZH_U2I2I_MMU_623_CLASS_CLICK = 267,     // author: zhaohao03, 功能: fanstop u2i2i mmu 623 click tag
    LTS_COMMON_LEAF_P2L_RETRIEVAL = 268,           // author: litianshi03, 功能: 内粉 P2L commonleaf 召回
    NN_INNER_RANDOM_PHOTO_RETRIVEAL = 269,           // author: nanning, 功能: 内粉作品随机召回
    LTS_COMMON_LEAF_LIVE_RETRIEVAL = 270,              // author: litianshi03, 功能: 内粉直播 commonleaf 召回

    ZH_PHOTO_VIDEO_FEATURE_EMB = 271,  // author: zhaohao03, 功能: fanstop ann embedding photo video feature
    ZH_PHOTO_AUTHOR_EMB = 272,      // author: zhaohao03, 功能: fanstop ann embedding photo author embedding
    ZH_LIVE_AUTHOR_EMB = 273,       // author: zhaohao03, 功能: fanstop ann embedding live author embedding
    GJW_TAG_BASED_RETRIEVAL = 274,  // author: guojiangwei, 功能：联盟标签召回
    HJ_FOLLOW_RETRIEVAL_SKIP_TARGET = 275,  // author: houjian, 功能：粉丝召回，跳过定向
    YCH_CATEGORY_LIVE_RETRIEVAL = 278,  // author: chenwancheng03, 功能: 内循环软广 tdm 召回 粉条作品引流通路
    YCH_CATEGORY_P2L_RETRIEVAL = 279,       // author: yangchuanhua, 功能： 商品类目引流作品召回
    YCH_MMU_ENTITY_LIVE_RETRIEVAL = 280,    // author: yangchuanhua, 功能： mmu 商品实体直播召回
    YCH_MMU_ENTITY_P2L_RETRIEVAL = 281,  // author: yangchuanhua, 功能： mmu 商品实体引流作品召回
    YCH_SEARCH_ENTITY_LIVE_RETRIEVAL = 282,  // author: yangchuanhua, 功能： search query 实体直播召回
    YCH_SEARCH_ENTITY_P2L_RETRIEVAL = 283,  // author: yangchuanhua, 功能： search query 实体引流作品召回
    LX_BRUTE_LIVE_RETRIEVAL_CL = 284,   // author: lixu05, 功能: 直播暴力算点击进入直播间召回
    LX_BRUTE_LIVE_RETRIEVAL_CVR = 285,  // author: lixu05, 功能: 直播暴力算深度转化召回
    BFF_BRUTE_CLJN_LIVE_RETRIEVAL_E2E = 286,  // author: bianfeifei, 功能: 磁力金牛直播暴力算 E2E 召回
    ZH_P2L_AUTHOR_EMB = 287,  // author: zhaohao03, 功能: fanstop ann embedding p2l author embedding
    ZCL_ECPM_PS_PHOTO_RETRIEVAL = 288,         // author: zhangchenlu03, 功能: fanstop photo ecpm 召回
    ZYM_FANSTOP_TAG_RECALL = 289,              // author: zhangyumeng05, 功能: fanstop 标签召回
    LX_RETARGET_LIVE_RETRIEVAL = 290,          // author: lixu05, 功能: fanstop 重定向直播召回
    LX_RETARGET_PHOTO_RETRIEVAL = 291,         // author: lixu05, 功能: fanstop 重定向作品召回
    LX_CLUSTER_LIVE_RETRIEVAL = 292,           // author: lixu05, 功能: fanstop 聚类直播召回
    LX_CLUSTER_PHOTO_RETRIEVAL = 293,          // author: lixu05, 功能: fanstop 聚类作品召回
    ZH_QUERY_PHOTO_RETRIEVAL = 294,            // author: zhaohao03, 功能: fanstop 搜索 query 作品召回
    ZH_QUERY_LIVE_RETRIEVAL = 295,             // author: zhaohao03, 功能: fanstop 搜索 query 直播召回
    MYB_REALTIME_QUERY_PHOTO_RETRIEVAL = 296,  // author: mayanbin, 功能: fanstop 实时搜索 query 作品召回
    MYB_REALTME_QUERY_LIVE_RETRIEVAL = 297,  // author: mayanbin, 功能: fanstop 实时搜索 query 直播召回
    JJG_PHOTO_NEARLINE_RETRIEVAL = 298,      // author: jinjiaguang, 功能: fanstop photo 近线召回
    CWC_ECPM_PS_LIVE_RETRIEVAL = 299,  // author: chenwancheng03, 功能：fanstop live ecpm( 无 OCPC) 召回
    CWC_ECPM_ROI_PS_LIVE_RETRIEVAL = 300,  // author: chenwancheng03, 功能：fanstop live ecpm( 有 OCPC) 召回
    YLY_LTR_PHOTO_RETRIEVAL = 301,         // author: yanglingyun, 功能: fanstop photo ltr 作品召回
    YLY_LTR_LIVE_RETRIEVAL = 302,       // author: chenwancheng03, 功能: 内循环软广 tdm 召回 粉条直投直播通路
    MYB_BRUTE_LIVE_RETRIEVAL_MERCHANT = 303,  // author: mayanbin, 功能: fanstop 直播 商品召回
    ZH_HOT_LIVE_RETRIEVAL = 304,              // author: zhaohao03, 功能：fanstop live 热门召回
    ZH_HOT_P2L_RETRIEVAL = 305,               // author: zhaohao03, 功能：fanstop p2l 热门召回
    YSQ_BRUTE_CLJN_VIDEO_RETRIEVAL_E2E = 306,  // author: yesiqi, 功能: 磁力金牛短视频暴力算 E2E 召回
    ZCL_ECPM_PS_ROI_PHOTO_RETRIEVAL = 307,       // author: zhangchenlu03, 功能: fanstop ecpm roi 召回
    LX_PURCHASE_INTENTION_PHOTO = 308,         // author: lixu05 功能：购物意图定向作品召回
    WQ_MMU_ENTITY_EXPAND_PHOTO_RETRIEVAL = 309,  // author: wangqi12 功能：fanstop 实体扩展召回
    WQ_GRAPH_EMB_LIVE_RETRIEVAL = 310,           // author: wangqi12 功能：fanstop 商品图 emb 召回
    WQ_GRAPH_EMB_PHOTO_RETRIEVAL = 311,          // author: wangqi12 功能：fanstop 商品图 emb 召回
    WQ_MMU_KEYWORD_LIVE_RETRIEVAL = 312,         // author: wangqi12 功能：fanstop photo 实体召回
    WQ_MMU_KEYWORD_PHOTO_RETRIEVAL = 313,        // author: wangqi12 功能：fanstop photo 实体扩展召回
    YF_MI_PHOTO_RETRIEVAL = 314,                 // author: yufei, 功能： fanstop photo 多向量召回
    MYB_REALTIME_TAG_EXPAND_PHOTO_RETRIEVAL = 315,  // author : mayanbin, 功能: photo 实时召回 tag 扩展
    MYB_REALTIME_TAG_EXPAND_LIVE_RETRIEVAL = 316,   // author : mayanbin, 功能: live 实时召回 tag 扩展
    YFK_HUNGRY_DEGREE_PHOTO_RETRIEVAL = 317,  // author: yangfukang03, 功能: 新内粉保量订单召回
    LTS_COMMON_LEAF_RANDOM_RETRIEVAL = 318,  // author: litianshi03, 功能：内粉请求 reco common leaf 随机召回
    ZH_CL_PHOTO_RETRIEVAL = 319,  // author: zhangheng08, 功能: fanstop photo 对比学习召回
    /*
      320 - 447 为 搜索广告 专用tag，非 搜索广告 业务请勿使用
    */
    SEARCHAD_QUERY_TO_CREATIVE = 320,       // author: chencheng,  功能：搜索广告 query2creative 方式召回
    SEARCHAD_QUERY_TO_PHOTO = 321,          // author: chencheng,  功能：搜索广告 query2photo 方式召回
    SEARCHAD_QUERY_TO_RETR = 322,           // author: chencheng,  功能：搜索广告 query2retr 方式召回
    SEARCHAD_QUERY_REWRITE_CREATIVE = 323,  // author: chencheng,  功能：搜索广告 query2creative 方式召回
    SEARCHAD_QUERY_REWRITE_PHOTO = 324,     // author: chencheng,  功能：搜索广告 query2photo 方式召回
    SEARCHAD_QUERY_REMEMBER_ME = 325,       // author: chencheng,  功能：搜索广告 remember me 方式召回
    SEARCHAD_QUERY_BIDWORD = 326,     // author: wangning14,  功能：搜索广告暗投 bidword 方式召回
    SEARCHAD_QUERY_BIDWORD_AUTO = 327,  // author: wangning14,  功能：搜索广告暗投挖掘 bidword 方式召回
    SEARCHAD_PHOTO_EXTEND = 328,        // author: houxue,  功能：搜索广告 photo_extend 方式召回
    SEARCHAD_QUERY_TO_RETR_LIVE = 329,  // author: weiyilong,  功能：搜索广告 query2retr_live 方式召回
    SEARCHAD_RECOMMENDER = 330,  // author: gaokaiming,  功能：搜索广告 query_to_recommender 方式召回
    SEARCHAD_QUERY_TO_USER_EMB = 331,  // author: weiyilong,  功能：搜索广告 query_to_user emb 方式召回
    SEARCHAD_REMEMBER_LIVE = 332,  // author: zhaocuncheng,  功能：搜索广告 query_to_remember_live 方式召回
    SEARCHAD_QUERY_TO_RETR_LIVE_EMB = 333,  // author: niejinlong,  功能：搜索广告 query_to_live emb 方式召回
    SEARCHAD_MINGTOU = 334,                 // author: wangjiabin05,  功能：搜索广告明投召回
    SEARCHAD_QUERY_TO_AUTHOR_LIVE = 335,  // author: zhangchaoyi03,  功能：搜索广告 query_author_live 召回
    SEARCHAD_QUERY_TO_RETR_FANSTOP = 336,  // author: zhouxuan06,  功能：搜索广告粉条 photo adretr 召回
    SEARCHAD_BIDWORD_TO_USER = 337,    // authoe: duantao, 功能:搜索明投召回内循环广告主
    SEARCHAD_RELEVANCE_RECALL = 338,   // author: weiyilong, 功能: 搜索广告相关性模型召回
    SEARCHAD_QUERY_TO_SKU_PHOTO = 339,   // author: zhangchaoyi03, 功能: 搜索广告小店商品召回 photo
    SEARCHAD_QUERY_TO_SKU_LIVE = 340,   // author: zhangchaoyi03, 功能: 搜索广告小店商品召回 live
    SEARCHAD_USER_ECPM_RECO = 341,   // author: weiyilong,  功能：搜索广告用户 ecpm 模型召回
    SEARCHAD_QUERY_TO_GRAPH_RETR = 342,   // author: niejinlong,  功能：搜索广告图模型召回
    SEARCHAD_QUERY_TO_QUICK_PHOTO = 343,  // author: zhangchaoyi03, 功能: 搜索广告快投放召回
    SEARCHAD_REFER_PHOTO_TO_PHOTO = 344,  // author: zhaocuncheng,  功能: refer photo 召回 photo
    SEARCHAD_USER_HIGHVALUE_PHOTO = 345,  // author: caomeng,  功能: refer 召回用户高价值 photo
    SEARCHAD_APP_CARD_ACCOUNT = 346,  // author: weiyilong,  功能: 搜索广告下载类强样式 account
    SEARCHAD_E2E_TWIN_TOWER = 347,  // author: weiyilong,  功能: 搜索广告 e2e 双塔召回
    SEARCHAD_SEARCH_PHOTO_TO_AD = 348,  // author: niejinlong,  功能: 搜索自然作品召回
    SEARCHAD_SEARCH_KEYWORD_TO_LIVE = 349,  // author: zhangheng08,  功能: 搜索广告定向关键词直播召回
    SEARCHAD_INTERVENE = 350,  // author: zhaodi,  功能: 搜索干预召回
    SEARCHAD_QUERY_TO_SPU_PHOTO = 351,  // author: caomeng, 功能: spu 搜索视频召回
    SEARCHAD_QUERY_TO_SPU_LIVE = 352,  // author: caomeng, 功能: spu 搜索直播召回
    SEARCHAD_QUERY_TO_FACE_LIVE = 353,  // author: caomeng, 功能: face 搜索直播召回
    SEARCHAD_SEARCH_KEYWORD_TO_PHOTO = 354,  // author: zhangheng08,  功能: 搜索广告定向关键词 photo 召回
    SEARCHAD_INNER_REFER_PHOTO_RETR = 355,  // author: niejinlong,  功能: 内流 refer photo 召回
    SEARCHAD_FORM_SUBMIT_ACCOUNT = 356,  // author: zhaodi,  功能: 搜索广告表单提交强样式 account
    SEARCHAD_DSI = 357,  // author: gaokaiming,  功能: 搜索广告 DSI
    SEARCHAD_BIDWORD_RETR = 358,  // author: weiyilong,  功能: 搜索广告 bidwordRetr 在线召回
    SEARCHAD_BIDWORD_OFFLINE_EXTEND = 359,  // author: weiyilong,  功能: 搜索广告 bidwordExtend 离线召回
    SEARCHAD_FOLLOW_USER_RECALL = 360,  // author: duantao,  功能: 关注关系召回
    SEARCHAD_APP_CARD_ACCOUNT_NOT_EXACT = 361,  // author: weiyilong,  功能: 搜索广告下载类强样式 account
    SEARCHAD_FORM_SUBMIT_ACCOUNT_NOT_EXACT = 362,  // author: zhaodi,  功能: 搜索广告表单提交强样式 account
    SEARCHAD_AUTHORNAME_TO_LIVE = 363,  // author: zhaocuncheng,  功能：搜索广告 authorname2live 方式召回
    SEARCHAD_INNER_REFER_PHOTO_TO_PHOTO_RETR = 364,  // author: zhaoyilin05,  功能: 内流 referp2p2ad 召回
    SEARCHAD_QUERY_TO_SKU_PHOTO_TYPE = 365,  // author: zhangchaoyi03, 功能：搜索广告 query2sku photo 离线召回
    SEARCHAD_QUERY_TO_SKU_LIVE_TYPE = 366,  // author: zhangchaoyi03, 功能：搜索广告 query2sku live 离线召回
    SEARCHAD_RECOMMEND_QUERY_TO_AD_TYPE = 367,  // author: niejinlong, 功能：搜索广告导流 authorid2live 召回
    SEARCHAD_QUERY_TO_AD_RETR_REALTIME = 368,  // author: duantao, 功能：搜索广告 adretr-realtime

    UNIVERSE_TINY_PACKAGE_DISTRIBUTE = 369,  // author: yinliang, 功能：联盟小系统厂商分发
    UNIVERSE_TINY_PACKAGE_QUERY_EXACT = 370,  // author: yinliang, 功能：联盟小系统厂商 query ，精准
    UNIVERSE_TINY_PACKAGE_QUERY_CATEGORY = 371,  // author: yinliang, 功能：联盟小系统厂商 query ，类目
    UNIVERSE_TINY_PACKAGE_QUERY_TEXT = 372,  // author: yinliang, 功能：联盟小系统厂商 query ，文本
    UNIVERSE_TINY_PACKAGE_QUERY_PACKAGE = 373,  // author: yinliang, 功能：联盟小系统厂商 query ，包名白名单
    UNIVERSE_TINY_PACKAGE_DISTRIBUTE_NEW = 374,  // author: wanglei10, 功能：联盟小系统厂商分发 倒排
    UNIVERSE_TINY_PACKAGE_DISTRIBUTE_APP_ADVANCE  = 375,  // author: yinliang, 功能：联盟小系统厂商分发 拉活
    UNIVERSE_TINY_PACKAGE_SUG_HANZI = 376,  // author: yinliang, 功能：联盟小系统厂商 suggest-汉字
    UNIVERSE_TINY_PACKAGE_SUG_PINYING = 377,  // author: yinliang, 功能：联盟小系统厂商 suggest-拼音
    UNIVERSE_TINY_PACKAGE_SUG_PINYING_F_C = 378,  // author: yinliang, 功能：联盟小系统厂商 suggest-拼音首字母


    SEARCHAD_INNER_AUTHOR_TO_AUTHOR = 376,  // author: zhaoyilin05, 功能: 内流 referauthor2author 召回
    SEARCHAD_APP_CARD_ACCOUNT_LIST = 377,  // author: zahodi,  功能: 搜索广告下载类列表强样式 account
    SEARCHAD_FORM_SUBMIT_ACCOUNT_LIST = 378,  // author: zhaodi,  功能: 搜索广告表单提交列表强样式 account
    SEARCHAD_INNER_SERIES_RETR = 379,  // author: zhaoyilin05,  功能: 搜索广告自建链路短剧表单强样式召回
    SEARCHAD_QUERY_CATEGORY_TO_ITEM_TYPE = 380,  // author:zhangzhicong, 功能: 搜索广告中间页根据类目召回 item
    SEARCHAD_QUERY_TO_SKU_EMB = 381,  // author: niejinlong, 功能: 搜索广告 query2sku embedding 召回
    SEARCHAD_QUERY_TO_LOCAL_LIFE_GOODS = 382,  // author: zhaodi, 功能: 搜索广告 query2goods 文本召回
    SEARCHAD_QUERY_TO_RETR_LIVE_REALTIME = 383,  // author: zhaodi, 功能: 搜索广告直播实时化文本召回
    SEARCHAD_FORM_SUBMIT_DPA_ID_RETR = 384,  // author: zhaoyilin05, 功能: 搜索广告表单强样式 dpa_id 召回
    SEARCHAD_QUERY_TO_AUTHOR_LIVE_REWRITE = 385,  // author: zhangchaoyi03, 功能:搜索改写 query_author_live
    SEARCHAD_QUERY_TO_AUTHOR_GRAPH = 386,   // author: zhaoyilin05, 功能：图模型 query2author 召回
    SEARCHAD_INNER_PHOTO_TO_AUTHOR_GRAPH = 387,   // author: zhaoyilin05, 功能：图模型 referphoto2author 召回
    SEARCHAD_QUERY_TO_BOOK = 388,  // author: zhangchaoyi03, 功能：query 召回 book_id
    SEARCHAD_QUERY_TO_VIDEO_CONTENT = 389,   // author: wangsiyuan11, 功能：搜索广告视频内容理解召回
    SEARCHAD_QUERY_TO_INDUSTRY = 390,  // author: zhaorongsheng, 功能: 搜索激励广告盒子单列行业召回
    SEARCHAD_QUERY_QUANTIZE_ID_TO_PHOTO = 391,  // author: zhangchaoyi03, 功能：query 量化 id 召回
    SEARCHAD_ADBOX_TWIN_TOWER = 392,  // author: lifeiyang03, 功能：广告盒子双塔模型召回
    SEARCHAD_QUERY_TO_RETR_P2L_REALTIME = 393,  // author: zhaodi, 功能：短引实时化文本倒排召回
    SEARCHAD_ADBOX_TWIN_TOWER_LIVE = 394,  // author: lifeiyang03, 功能：广告盒子直播双塔模型召回
    SEARCHAD_QUERY_TO_SKU_SID_RETR = 395,  // author: luyi05, 功能：搜索广告 query2sku 语义 ID 召回
    SEARCHAD_SKU_TWIN_TOWER = 396,  // author: lifeiyang03, 功能：sku 双塔模型召回

    UNIVERSE_TINY_USER_QUERY_HISTORY = 421,  // author: shanminghui, 功能：联盟小系统用户检索历史召回
    UNIVERSE_TINY_USER_QUERY_HISTORY_LONGER = 422,  // author: zhangyunhao03, 功能：联盟小系统 7 日历史召回
    UNIVERSE_TINY_PACKAGE_SUG_A_2_B  = 423,  // author: yinliang, 功能：联盟小系统搜 A 出 B
    UNIVERSE_MANUFACTURER_GREEN_CHANNEL  = 424,  // author: wanglei10, 功能：联盟厂商自有 & 三方流量绿色通道
    UNIVERSE_TINY_PACKAGE_INTERVENE  = 425,  // author: yinliang, 功能：联盟小系统人工干预召回
    UNIVERSE_RTA_MONOPOLIZE_RANDOM_RETRIEVAL  = 426,  // author: wanglei10, 功能：联盟 rta local 召回
    UNIVERSE_OUT_RETARGET_RETRIEVAL = 427,  // author: yangjinhui, 功能： 联盟外循环重定向召回
    UNIVERSE_INNER_DEEP_HIGH_VALUE_CROWD = 428,  // author: wuhan12, 功能： 联盟内循环高价值人群深度召回
    UNIVERSE_COLD_START_PHOTO_RECALL = 429,  // author: yangjinhui, 功能：联盟冷启动 photo 召回
    UNIVERSE_LLM_U2P_RECALL = 430,  // author: huoyan03, 功能：联盟 LLM 做 u2p 召回
    UNIVERSE_TINY_PACKAGE_QUERY_TEXT_LLM = 431,  // author: jiangyifan05, 功能：联盟小系统大模型纠错召回
    CLIENT_AI_RERANK_REQ_RETR = 432,  // author: wangwenguang, 功能：端智能二次请求召回老广告
    INCENTIVE_LIVE_RESERVATION_AUTHOR = 433,  // author: gaozepeng, 功能：激励直播预约大客召回
    UNIVERSE_SPLASH_CACHE_PHOTO_RETRIEVAL = 434,  // author: chendongdong, 功能：联盟开屏视频缓存重召回
    INCENTIVE_LIVE_ONLY_TASK = 435,  // author: zhangxingyu03, 功能：激励赚钱页直播任务召回
    INCENTIVE_SILENT_FANS_RECALL_TASK = 436,  // author: zhangxingyu03, 功能：激励沉默粉丝召回任务
    UNIVERSE_PACKAGE_DISTRIBUTE = 437,  // author: linglong, 功能：联盟厂商分发场景召回
    INCENTIVE_INVOKED_PRODUCT = 438,  // author: gaozepeng, 功能：激励唤端产品名召回
    UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_A = 439,  // author: rentianci, 功能: 搜索广告包名召回 A
    UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_B = 440,  // author: rentianci, 功能: 搜索广告包名召回 B
    UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_C = 441,  // author: rentianci, 功能: 搜索广告包名召回 C
    UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_D = 442,  // author: rentianci, 功能: 搜索广告包名召回 D
    UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_E = 443,  // author: rentianci, 功能: 搜索广告包名召回 E
    UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_F = 444,  // author: rentianci, 功能: 搜索广告包名召回 F
    MAX_TAG = KMAXTAGID
    /*  tag id 不–要超过 KMAXTAGID;
        多路命名规范: 名字缩写_功能 = 70,  //  名字缩写解释;
        多路命名实例: HQ_TEST = 70,  // author: heqian, 功能: test;
        修改多路代码请 @heqian/@chengyuxuan/@yanfeng;
        tag 240-255 为开屏多路
        tag 256-319 为 fantop 多路
        tag 320-420 为 搜索广告 多路
        tag 421-447 为 联盟多路
        违反上述限制，请自觉修正并发红包
    */
)

}  // namespace multi_retr
}  // namespace ad_target
}  // namespace ks
