#pragma once

namespace ks {
namespace ad_base {

enum WorkFlowType {
  NormalWork = 0,  // 走正常流程流量
  RandomWork = 1,  // 走随机探索流程
  ExclusiveWork = 2,  // 独占流量
  RetargetWork = 3,  // retarget 独占流量
  UniverseExclusiveWork = 4,  // 联盟独占 v2.0
};

enum ServerType {
  UnkonwnServer = 0,
  FrontServer = 1,
  BudgetStatusServer = 2,
  TargetServer = 3,
};

enum AdEngineTraceType {
  SKIP = 0,
  PV_DATA_ONLY = 1,
  ALL_DATA = 2,
};

enum AdEngineTraceServiceType {
  Unknown = 0,
  AdFrontServer = 1,
  AdServer = 2,
  AdTargetServer = 3,
  AdRankServer = 4,
  AdMixRankServer = 5,
};

// 添加新枚举值, cr 请添加 qijiangtao/yushengkai/zhouxinyu
enum AutoCpaBidModifyTagType {
  kDefault = 0,
  kFormExp = 1,
  kConvExp = 2,
  kPaySingleBidExp = 3,
  kUniverseROIExp = 4,
  kPrepareBenifitOcpcDspRefactor = 5,
  kCalcBenefits = 6,
  kParseRankRequest = 7,
  kFillAdList = 8,
  kSSTHFillAdList = 9,
  kMergeFansTop = 10,
  kSetColdStartBonus = 11,
  kMergeAdxAds = 12,
  kMergeBrandAds = 13,
  kFillDynamicBidInfo = 14,
  kParseRankRequest1 = 15,
  kGetOriginBid = 16,
  kFillBidMsg = 17,
  kDegradeAutoBidV2 = 18,
  kSpecifyAdCpaExp = 19,
  kGameHighValueBidModify = 20,
  kGamePayROI = 21,
  kMerchantAdACBid = 22,
  kCpaBid = 23,
  kJingkuaiExp = 24,
  KOptimizeCtCvrExp = 25,
  kLowestCostDefault = 26,
  kRtaRatioModify = 27,
  kUnionROAS = 28,
  kMerchantROAS = 29,
  kAppInvokedBidExp = 30,
  kDpaExp = 31,
  kFristnBid = 32,
  kSoftnBid = 33,
  kMerchantROASDefault = 34,
  kMerchantFollowROIUpdate = 35,
  kInsurancePayROI = 36,
  kAdxOcpmBid = 37,
  kBsBidExp = 38,
  kDjkBidExp = 39,
  kMerchantDiscountBid = 40,
  kLaBidExp = 41,
  kMerchantOrderROIUpdate = 42,
  kMerchantOrderEcpcUpdate = 43,
  kPayTimes = 44,
  kLiveAutoCpaBid = 45,
  kWechat = 46,
  kMerchantPrerankRoas = 47,
  kMergeDpaAds = 48,
  kNativeMerchantROAS = 49,
  kGuardFillBidMsg = 50,
  kGuardMerchantAdACBid = 51,
  kGuardDefaultCpaBid = 52,
  kSkipDspAutoBid = 53,
  kSkipMercAutoBid = 54,
  kNativeMerchantRoasDefault = 55,
  kMerchantAutoCpaBid = 56,
  kRevElasticityCpaBidExp = 57,
  kGamePayROI2 = 58,
  kGamePayROI3 = 59,
  kGamePayROI4 = 60,
  kGamePayROI5 = 61,
  kGamePayROI6 = 62,
  kGamePayROI7 = 63,
  kGamePayROI8 = 64,
  kGamePayROI9 = 65,
  kGamePayROI10 = 65,
  kGamePayROI11 = 66,
  kGamePayROI12 = 67,
  kSevenDayROI = 68,
  kSpecialtyNoBid = 69,
  kCalibration = 70,
  kPrerankCpaExp = 71,
  kGamePayROI13 = 72,
  kGamePayROI14 = 73,
  kNobid = 74,
  kMroas = 75,
  kDefaultExpCpaBid = 76,
  kCpaBidConvRatioFill = 77,
  kPayTimesPrerank = 78,
  kGamePayROI15 = 79,
  kLiveOcpmColdStartExp = 80,
  kMerchantMcb = 81,
  kAccountBidding = 82,
  kFansTopROI = 83,
  kNativeUnifyDefault = 84,
  KNativeUnifyUnitAlign = 85,
  kCalcCostCapAutoBid = 86,
  kCalcRoiAutoBid = 87,
  kMerchantROASV2 = 88,
  kOneAdCalcBenefit = 89,
  kRoiRatioGt0 = 90,
  kNobidRoasBidGt0 = 91,
  kMerchantROASRoiRatioGt0 = 92,
  kMroasChangeVideoRoasCpaBid = 93,
  kMroasChangeVideoRoasCpaBidElse = 94,
  kMroasUniverseLiveRoasUseNewBid = 95,
  kMroasUniverseLiveRoasUseNewBidElse = 96,
  kMhtCPLModifyBid = 97,
  kFansTopHighValue = 98,
  kMCB = 99,
  kMobileLiveAutoBid = 100,
  kIndustryLive = 101,
  kSearchFlowSkipAutoCpaBidExp = 102,
  kRtaBidModify = 103,
  kPageModify = 104,
  kRtaNativeBidModify = 105,
  kFanstopBidExploreBidModify = 106,
  kRtaAccountBidModify = 107,
  kAccountUnionTagBidding = 108,
  kCidRoasBidModify = 109,
  kSocialPayROIBidModify = 110,
  kUniverseInnerHardBoundModify = 111,
  kCidPrerankRoasBidModify = 112,
  kCpaLiveHostingOverdue = 113,
  kColdStartCpaBid = 114,
  kCloseAutoBid = 115,
  kAutoBidBidServiceEcpc = 116,
  kSpecialUnitBoost = 117,
  kCidRoasAutoCpaBid = 118,
  kMerchantSplashLiveT7Roi = 119,
  kMerchantUpDimBidding = 120,
  kRtaManualBidModify = 121,
  kMcbDiffDateReset = 122,
  kCidOrderModify = 123,
  kMaxBound = 199,  // 新增 tag 在这一行之上
};

enum AutoRoasModifyTagType {
  kRoasDefault = 0,
  kRoasMerchantPrerankRoas = 1,
  kRoasMergeFansTop = 2,
  kRoasNativeMerchantRoasDefault = 3,
  kRoasAmdCalcBenefit = 4,
  kRoasAmdRoasModifyAutoBidStrategy = 5,
  kRoasDeepRoasFlowControl = 6,
  kRoasZeroFillA = 7,
  kRoasDefaultStart = 8,
  kRoasFillBidInfo = 9,
  kRoasDefaultExpStart = 10,
  kRoasConvRatioFill = 11,
  kRoasNativeRankBound = 12,
  kRoasLiveOcpmColdStartExp = 13,
  kRoasAccountBidding = 14,
  kRoasMobileLiveAutoBid = 15,
  kRoasUniverseNobidModify = 16,
  kSearchFlowSkipAutoRoasExp = 17,
  kRoasPageModify = 18,
  kRoasAccountUnionTagBidding = 19,
  kRoasLiveHostingOverdue = 20,
  kRoasColdStart = 21,
  kRoasCloseAutoBid = 22,
  kRoasBidServiceEcpc = 23,
  kRoasSpecialUnitBoost = 24,
  kRoasUpDimBidding = 25,
  kRoasMcbDiffDateReset = 26,
  kCidRoasModify = 27,
  kRoasMaxBound = 99,  // 新增 tag 在这一行之上
  kMhtCPLModifyRoas = 100
};

enum class BudgetBitType {
  DSP_TYPE = 0x1,
  VIRTUAL_CHARGE_HIT_LINE_TYPE = 0x2,
  ESP_TYPE = 0x4,
};

// dark_control_code 部分 bit type
enum class DarkControlCodeBitType {
  INSPIRE_LIVE_DARK_AUTO_CONTROL_TYPE,    // 激励直播自动控比
  INSPIRE_DARK_AUTO_CONTROL_TYPE,   // 激励视频自动控比
  INC_EXPLORE_HAVE_LEFT_BUDGET_TYPE,  // 外循环增量探索是否有剩余预算
};

enum class BidServiceBitType {
  IN_ACC_EXPLORE_STATUS = 0x1,
  EFFECT_FIRST = 0x2,
  DEEP_BID_TWIN = 0x4,
  DEEP_CONV_RETENTION = 0x8,
  DEEP_CONV_PURCHASE = 0x10,
  UNIVERSE_AD = 0x20,
  UNIVERSE_OPT_AD = 0x40,
  FANSTOP_AD = 0x80,
  FANSTOP_BID_AD = 0x100,
  LOWEST_COST = 0x200,
  INNER_DELIVERY = 0x400,
  INNER_CPM = 0x800,
  INNER_AD = 0x1000,
  TWINBID_PRIOR_SHALLOW = 0x2000
};

enum SimplifyTraceLogSimlpeType {
  NO_SIMPLE = 0,  // 都不采样
  SIMPLE_WHITE_BOX_DATA = 1,    //  只白盒数据采样
  SIMPLE_DATA_DEPOSITORY_DATA = 2,   //  只数仓数据采样
  SIMPLE_WHITE_BOX_AND_DATA_DEPOSITORY_DATA = 3,    // 白盒和数仓数据都采样
};

enum class BidServiceFlowType {
  DEFAULT = 0,
  UNIVERSE = 1,
  SEARCH = 2,
  BIDWORD_SEARCH = 3,
  REWARDED = 4,
  SPLASH = 5
};

enum class BidServiceRequestFlowType {
  DEFAULT = 0,
  REQUEST_FLOW_TYPE_NEBULA = 1,
  REQUEST_FLOW_TYPE_SELECTED = 2
};

enum AdPageId {
  kDetail = 10001,
  kExplore = 10002,
  kNearby = 10003,
  kFollow = 10008,
  kAggregate = 10013,
  kInspire = 100011291,
  kNebulaExplore = 11001,  // 包括 Toast 补签任务
  kNebulaAppStart = 11100,
  kNebulaNearby = 11002,
  kNebulaFollow = 11008,
  kNebulaInspire = 11101,
  kNewNebulaInspire = 100011502,
  kNebulaAggregate = 11015,
  kFeatured = 10011,                             // 底导版
  kWechatApp = 100011099,                        // 快手微信小程序
  kQQApp = 100012458,                            // 快手 QQ 小程序
  kLiveSquare = 10007,                           // 直播广场
  kNewNebulaNormalFeedInspire = 100012060,       //极速版双列激励直播
  kNebulaRelationVideoInnerPageId = 100013364,   // 极速版相关视频内流
  kNormalFeedInspire = 100014023,                // 主版双列激励直播
  kMerchantInspirePageId = 100012269,            // 主版激励电商
  kNebulaMerchantInspirePageId = 100012268,      // 极速版激励电商
  kMerchantInspireFeedPageId = 100018921,        // 主版激励电商双列
  kNebulaMerchantInspireFeedPageId = 100018923,  // 极速版激励电商双列
  kNebulaFollowSlide = 100016770,                // 极速版关注页单列
  kBuyerHomePagePageId = 100011029,              // 电商买家首页
};

enum AdSubPageId {
  kUnknowPageId = 0,
  kFollowFeedTop = 10008001,        // 快手-关注页置顶位，主站双列
  kNebulaFollowFeedTop = 11008001,  // 粉条关注页置顶
  kPearlSmallVideo = 20001003,
  kPearlNewsFeed = 20001001,
  kPearlPageSet = 20002001,
  kPullDownRefresh = 10002002,
  kExploreFeed = 10002001,
  kNearbyFeed = 10003001,
  kUpDownFeed = 10002003,
  kUpDownSlide = 10002004,
  kThanosSlide = 10002005,
  kDianWanScreen1 = 30004001,  // 电丸插屏场景 1
  kDianWanScreen2 = 30004002,
  kNebulaExploreFeed = 11001001,
  kNebulaToastScene = 100011286,  // Toast 补签粉条（发现页）
  kNebulaAppStartScene = 11100001,
  kNebulaNearbyFeed = 11002001,
  kSmallStore = 10006001,                       // 小店
  kNebulaInspireScene = 11101001,               // 激励广告
  kSelected = 10011001,                         // 底导版精选页
  kSelectedBackFresh = 100011343,               // 底导版精选页 BackFresh 流量
  kNearbyInternalFlow = 100011085,              // 主 App 设置版内外不同流, 此标记为内部流量
  kWechatAppSub = 100011100,                    // 快手微信小程序
  kQQAppSub = 100012459,                        // 快手 QQ 小程序
  kInspireLive = 100011292,                     // 主版任务中心激励直播
  kNewNebulaInspireLive = 100011503,            // 极速版任务中心激励直播
  kMerchantInspire = 100012270,                 // 电商激励
  kNebulaMerchantInspire = 100012271,           // 电商激励-极速版
  kNewNebulaNormalFeedInspireLive = 100012061,  //极速版任务中心双列激励直播
  kExploreInner = 100012194,                    // 双列内流流量
  kSubLiveSquare = 10007001,                    // 直播广场
  kFollowInner = 100013100,                     // 主站关注页内流
  kNebulaFollowThanos = 100016771,              // 极速版关注页单列
  kNebulaRelationVideoInner = 100013365,        // 极速版相关视频内流
  kInspireAdditionalCheckIn = 100011430,        // 激励视频补签
  kInspireCheckIn = 100011586,                  // 激励视频签到
  kInspireGoldenBox = 11101003,                 // 激励视频宝箱
  kNebulaDrawingGameCnt = 100013630,            // 极速版游戏化抽奖获取次数
  kNebulaDrawingGameTime = 100013629,           // 极速版游戏化抽奖时段奖励
  kNebulaDrawingGameCoin = 100013628,           // 极速版游戏化抽奖双倍金币
  kNebulaReadingPage = 100017305,               // 极速版视频阅读页插屏
  kNebulaInspireGame = 100018128,               // 激励游戏互动场景
  kM2uPhotoModifySaveBanner = 100018568,        // 一甜照片修图保存页 banner 广告位
  kM2uVideoModifySaveBanner = 100018569,        // 一甜视频修图保存页 banner 广告位
  kKmovieExportPageBanner = 100014094,          // 快影剪辑完成导出页 banner 广告位
  kSearchInnerStream = 100018489,               // 搜索内流主板
  kSearchNebulaInnerStream = 100018490,         // 搜索极速版内流
  kNebulaNearbyFeedDiff = 100025977,            // 内外不同流外流
  kFollowLiveInner = 100026189,                 // 关注页直播内流：多物料类型
  kFollowLiveRoomInner = 100026503,             // 关注页直播内流：仅直播间
  kNebulaFollowLiveInner = 100026191,           // 极速版关注页直播内流
  kNebulaFollowOuter = 100032287,               // 极速版关注页外流
  kNebulaFollowLiveInnerCardEntry = 100032288,       //关注页直播内流-直播卡入口
};

enum LogicQueueType {
  kUnknowLogicQueueType = 0,
  kHardInner = 1,        // 内循环硬广
  kSoftInner = 2,        // 内循环软广
  kHardOuter = 3,        // 外循环硬广
  kSoftOuter = 4,        // 外循环软广
  kSoft = 5,              // 软广
  kHard = 6,              // 硬广
  kInner = 7,             // 内循环
  kOuter = 8,             // 外循环
  kHighQualitySoft = 9,    // 优质软广
  kOtherQualitySoft = 10,  // 普通软广
  kNebulaExploreHardInner = 11,  // 极速版发现页 && 内循环硬广
  kNebulaExploreSoftInner = 12,  // 极速版发现页 && 内循环软广
  kNebulaExploreHardOuter = 13,  // 极速版发现页 && 外循环硬广
  kNebulaExploreSoftOuter = 14,  // 极速版发现页 && 外循环软广
  kSelectedHardInner = 15,       // 底导版精选页 && 内循环硬广
  kSelectedSoftInner = 16,       // 底导版精选页 && 内循环软广
  kSelectedHardOuter = 17,       // 底导版精选页 && 外循环硬广
  kSelectedSoftOuter = 18,       // 底导版精选页 && 外循环软广
};
}  // namespace ad_base
}  // namespace ks
