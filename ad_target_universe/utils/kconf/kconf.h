#pragma once
#ifndef IMPL_BLOCK_BEGIN        /* auto generated by reset_kconf.py, you can ignore. */
  #define IMPL_BLOCK_BEGIN 0    /* auto generated by reset_kconf.py, you can ignore. */
#endif                          /* auto generated by reset_kconf.py, you can ignore. */
#ifndef IMPL_BLOCK_END          /* auto generated by reset_kconf.py, you can ignore. */
  #define IMPL_BLOCK_END 10000  /* auto generated by reset_kconf.py, you can ignore. */
#endif                          /* auto generated by reset_kconf.py, you can ignore. */

#include <string>

#include "teams/ad/ad_base/src/common/manual_config.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_target_universe/utils/kconf/kconf_data.h"
#include "teams/ad/universe_base/prerank/utils/kconf/kconf.h"
#include "teams/ad/ad_target_universe/utils/kconf/kconf_data.pb.h"
#include "teams/ad/ad_target_universe/utils/kconf/trace_config.h"
// 实现 cc 中带实现， header 中只带声明
/***************** NOTICE *****************/
/****** 自定义数据类型需要前向声明       *****/
/*****************************************/
#ifndef KCONF_CC_WITH_IMPL
#undef DEFINE_KCONF_NODE
#define DEFINE_KCONF_NODE(type, config_path, config_key, default_value) \
  API_KCONF_NODE(type, config_path, config_key, default_value)

#undef DEFINE_KCONF_NODE_LOAD
#define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key) \
  API_KCONF_NODE_LOAD(type, config_path, config_key)

#undef DEFINE_SET_NODE_KCONF
#define DEFINE_SET_NODE_KCONF(type, config_path, config_key) API_SET_NODE_KCONF(type, config_path, config_key)

#undef DEFINE_KCONF_MAP_KCONF
#define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
  API_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key)
#else
#undef DEFINE_KCONF_NODE_ATTR
#define DEFINE_KCONF_NODE_ATTR __attribute__((used))
#endif

namespace ks {
namespace ad_target {
struct AppBlackListConfig;
struct GameProductTopicMap;
struct TableSearchOptions;
struct TraceConfig;
struct NearbyLocalIndustryEntry;
struct UeRiskOrderLimit;
struct ProgCreativePostRetrievalConfig;
struct ProductFilterRatioConfig;
struct RetrDeliveryIndustryMap;
struct PostEcpmFlowParam;
struct PtdsCorpDiversityWhiteList;
struct UniverseDcafDetect;
struct PrivacyPolicyFilterConfig;
struct PermissionInfoFilterConfig;
struct RealAppVersionFilterConfig;
struct UniverseOptPosAccountFilter;
struct UniverseOptPosProductFilter;
struct UniverseOptAppAccountFilter;
struct UniverseOptAppProductFilter;


struct UselessPathConfig;



struct UniverseInnerShallowMediaRecallConfig;

using namespace ks::ad_base::kconf;    // NOLINT
using namespace ks::ad_target::kconf;  // NOLINT
using ks::universe_base::prerank::kconf::CrowdTagRecallConfig;

class AdKconfUtil {
 public:
  // int64

#if 0 >= IMPL_BLOCK_BEGIN and 0 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableEspAgent, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableUniverseLiveStream, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableUniverseTinyPrintProductNames, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableUniverseTinyUseAppNamePrefixEnginge, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableLiveStreamFilter, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, brpcUsercodeBthread, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableRoaring, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableMultiCoverCreative, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableRiskFilter, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableCreativeTableSearchSplit, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, loadIndexOnly, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableUniverseSiteDiyFilter, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableSupportSubquerySQL, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableUnivereseNewFeedPos, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, skipRtaService, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableSessionCache, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, universePlayableFilter, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, universeOpPlatformDot, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableFirstIndexTimeUniverse, false)
  DEFINE_TAILNUMBERV2_KCONF(ad.adtarget2, universeOpPlatformDotTailAdmit, "10000;0;;")
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, universeSimplifyBuildInvertMap, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableLiveAgent, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget4, enableCloseDarkAdConversionThreshold, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableOrientDeliveryMingtouShowHashMap, false)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseCategoryToRiskLabels, ad.adtarget2, universeCategoryToRiskLabels)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseHarmonySiteAssemblyFilterConfig, ad.adtarget3,
                             universeHarmonySiteAssemblyFilterConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseCCreativeRiskConfig, ad.adtarget4, universeCFilterConfigs)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseCCreativeRiskConfig, ad.adtarget4, universeCFilterWhiteConfigs)
  DEFINE_PROTOBUF_NODE_KCONF(engine_base::UniverseStoreSearchDarkAdBlackConfig, ad.adFrontDiffSwitches,
      universeStoreDistributDarkAdBlackConfig);
  DEFINE_PROTOBUF_NODE_KCONF(engine_base::UniverseStoreSearchDarkAdBlackConfig, ad.adFrontDiffSwitches,
      universeSkipAllDarkAdBlackConfig);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adtarget3, universeHarmonyPublicSdkVersion, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adtarget3, firstIndexTimeRedisUniverse,
                                        "adUniverseFirstIndexTimeCache")
  DEFINE_PROTOBUF_NODE_KCONF(UniverseAuditBlockConfig, ad.adtarget3, universeAuditBlockConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseNewCreativeBlackList, ad.adtarget3, universeNewCreativeBlackList)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseAccountListFreqConfigs, ad.adtarget3, universeAccountListFreqConfigs)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseFreTimeConfig, ad.adtarget3, universeFreTimeConfig)
  DEFINE_PROTOBUF_NODE_KCONF(OperationConfigFromKconfBase, ad.universeOperationToolEngine, meidaOrientRule);  //NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(engine_base::UniverseDarkAdDynamicItemConfig, ad.adtarget4, universeDarkAdConversionThresholdConf);  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(ConsultationAdConfig, ad.adtarget4, consultationAdWhiteConfig);
  DEFINE_SET_NODE_KCONF(int32, ad.adtarget2, universeMigrateTsmSet)
  DEFINE_SET_NODE_KCONF(int64, ad.adtarget2, universeTinySearchRequestInventoryPosId)
  DEFINE_SET_NODE_KCONF(int64, ad.adtarget2, universeTinySearchFilterPos)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget4, apiFlowDarkBlackAccount)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget4, apiFlowDarkBlackProduct)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adFrontDiffSwitches, universeDarkCSkipDarkControlConf)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget4, sdkFlowDarkBlackAccount)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget4, sdkFlowDarkBlackProduct)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeInnerShallowScenePosSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeTxScenePosSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeTxScenePosAutoSet)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, universeEffectPathSet)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseTinySearchRelevanceMergeConfig, ad.adtarget2,
                             universeTinySearchRelevanceMergeConfig);
  DEFINE_PROTOBUF_NODE_KCONF(UniverseSugQueryTypeConfig, ad.adtarget2,
                             universeSugQueryTypeConfig);
  DEFINE_PROTOBUF_NODE_KCONF(UniverseInterveneConfig, ad.adtarget2, universeInterveneConfig)

  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget2, universeTinyInstalledFilterAppIdList)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, aggrePageInspireCampaignTypeSet)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, universeTinyUserQueryHistroyStyleWhitelist)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, universeTinyUserQueryRecordTag)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget4, rtaAdInstalledAppFilterUidSet)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableFreqCappingNew, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, disableH5Ads, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableAnnFilter, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableFilterInvalidDetailCreative, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableOptionalField, false)
  DEFINE_BOOL_KCONF_NODE(ad.engine, isUniverseTinyDeploy, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableBussinessInterestTag, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, disableFlowControll, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableSetMultiParamMonitor, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableStrictLimitTarget, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, openPlayable_3_0, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enablePerfNodesCost, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableSQLApi, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableInnerLoopUserData, true);

  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableBudgetControlStatusConsume, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableLahuoUnloginAdTag, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableJinniuBlackUser, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableFansTopBrandPerf, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, isTargetOffline, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableOfflineTargetAnalysis, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableTargetLiveOfflineFilter, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableUniverseAutoParamTrans, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableUniverseDarkSkipCelebrityLabel, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableUniversePostEcpmRandom, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableUniversePostEcpmPartialRandom, true)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableUniverseLocalRetrievalRandom, false)
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, universeLocalRetrievalRandomNum, 5000)
  DEFINE_DOUBLE_KCONF_NODE(ad.adtarget3, universePostEcpmPartialRandomRatio, 0.8)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, checkTargetInfo, false)

  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enablePecFlow, false);
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, universeTargetWebServiceUpdate, false);
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, universeObjectJsonHandlerUpdate, false);
  // int32
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 1 >= IMPL_BLOCK_BEGIN and 1 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT32_KCONF_NODE(ad.adtarget, cacheAdTtl, 3 * 60 * 1000 * 1000)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, cacheAdCap, 0)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, rbUniverseWorkNum, 0)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, adtargetLogInfoFreq, 1000)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, behaviorLimitNum, 400)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, ruleFilterStrategyWorkers, 1)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, ruleFilterWorkers, 1)
  DEFINE_INT32_KCONF_NODE(ad.adserver, exitSleepSeconds, 5)
  DEFINE_INT32_KCONF_NODE(ad.adserver, adTargetServerReqNormalProcessRatio, 100)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, maxFalconCounter, 600000)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, embeddingRetrRequestProcessRatio, 100)  // embedding_retr 服务降级开关
  DEFINE_INT32_KCONF_NODE(ad.adtarget, universeAdSelectedUserFeedsRatio, 50)
  DEFINE_INT32_KCONF_NODE(ad.adserver, adRetrievalMaxCreativeLimits, 18000)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, brpcLowerLimitMs, 10)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, cacheUpperLimit, 0)
  DEFINE_INT32_KCONF_NODE(ad.adtarget3, universeTinyTaskGroupThreadNum, 128)
  DEFINE_INT32_KCONF_NODE(ad.adtarget3, universeTaskGroupThreadNum, 24)
  DEFINE_INT32_KCONF_NODE(ad.adtarget3, universeGraphDriverThreadNum, 24)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, grpcLowerLimitMs, 10)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, brpcServerQueueWaitUpper, 50 * 1000)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, brpcServerQueueUpperLimit, 250)
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, tableSearchRtaReidsTimeout, 2)
  // 使用 adcode 的流量比例，按照用户尾号后三位来划分
  // ranking 并行计算 parts
  DEFINE_INT32_KCONF_NODE(ad.adtarget, RedisOpTimeout, 1)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, redisRecallLimit, 1000)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, filterConversionDays, 30)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, unitPostEcpmExpandSize, 20)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, darkBudgetControlDefaultBtr, 100)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, fastQuitRemainMs, -1)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, targetingBthreadType, 1);
  DEFINE_INT32_KCONF_NODE(ad.adtarget, newCreativeConfFlag, 4)
  // 召回定向分析
  DEFINE_INT32_KCONF_NODE(ad.adtarget, targetAnalysisTagId, -1)

  // 头部商家召回
  DEFINE_INT64_KCONF_NODE(ad.adtarget2, universeInnerLastCostIndexKey, 20240731)

  // 低质限流跳过上限
  DEFINE_INT32_KCONF_NODE(ad.adtarget, reseviorLimitLevel, 70)

  DEFINE_INT32_KCONF_NODE(ad.adtarget, sessionCacheAdCap, 0)
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, espAppealListTruncSize, 10000);
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, e2eSampleRate, 10)
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, e2eSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, randomSampleSize, 0)
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, sampleStartIndex, 1000)
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, bthreadConcurrencyNums, 300)
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, universeCreativeCycleThreshold, 500)
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, universeRecallCacheTag, 320)

  DEFINE_STRING_STRING_MAP_KCONF(ad.adtarget3, TempUserXBudgetMap);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adtarget4, universePostCpmKey);

  // 联盟全库随机采样个数
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, UniverseAllDomainSampleSize, 0)
  DEFINE_INT64_KCONF_NODE(ad.adtarget4, UniverseAfterRtaFilterSampleSize, 0)

  DEFINE_INT64_KCONF_NODE(ad.adtarget, truncateCreativeSize, 20000)
  DEFINE_INT64_KCONF_NODE(ad.adtarget, brpcTargetTimeoutMs, 270)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget2, tableSearchOfflineSet);
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableUniverseAggregateToContextOpt, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableUniverseCreativeTaskMergeAsync, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.universeAdAutoParam, targetDebugUnitSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.universeAdAutoParam, targetDebugCreativeSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeStarAdvertiserWhiteSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget4, universeInnerDeepOcpcTypeSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget4, universeStarAdvertiserProductWhiteSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget, autoTestBizNameSet);
  DEFINE_SET_NODE_KCONF(int64_t, ad.adtarget2, tinySearchRecallOcpxTypeSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, universeMediaBlackUidSet)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PecStyleConfSceneMap, ad.adtarget2,
                             pecStyleConfSceneMap);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, pecOnlyFlowList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, skipHightlightFilterUnitTailSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, vivoAppStoreFlowSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget, multiRedisList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.brand, mmaAdvertiserList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adserver, nebulaOnlyAdsProductNameSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adStyleServer, InspireScreenPhotoDurationLimitedMediaId);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, accountIpadWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, universeLpsFilterUid)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget2, enableRuleCheckerTable)
  DEFINE_HASH_SET_NODE_KCONF(std::string, adData.realtime, targetTraceLogAdExtendInfoWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, universeLpsFliterConvertType)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, universeInternalAccountWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, universeLandingpagePosIdBlacklist)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.engine, universeEnableCostMediumList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, hongmengAccountFilterList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget, playableInterstitialBlackSet)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget, universeDarkBudgetControlAccountTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeTinySkipEcpmModelRecallPosSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, UniverseNonFakeUserIndustryList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, UniverseNonFakeUserActionTypeList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget, UniverseNonFakeUserProductList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, microAppCampaignWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, ecomFestivalAccountList)
  // rta 突破白名单配置
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, rtaBreakAccountList)
  // 联盟推全 photo 粒度新创意打标行业名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, universeNewCreativeTagIndustrySet)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.adtarget2, disableRTBRealTimeRecallHours);
  DEFINE_PROTOBUF_NODE_KCONF(UniverseShieldFastApplicationConfig, ad.adtarget2,
                             universeShieldFastApplicationConfig);
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 2 >= IMPL_BLOCK_BEGIN and 2 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeAppStoreDistributeTags)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget3, installedFilterOnlyUseAppUidSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeAppStoreDistributeStyles)
  DEFINE_INT64_KCONF_NODE(ad.adtarget2, universeTinyRankLogTag, 374)
  DEFINE_TAILNUMBERV2_KCONF(ad.adtarget2, universeDarkAutoControlUnitTail, "")

  DEFINE_STRING_STRING_MAP_KCONF(ad.adtarget2, universeTinyPosIdCampaignTypeFilterMap);
  DEFINE_STRING_STRING_MAP_KCONF(ad.adtarget2, universeTinyAdStyleCampaignTypeFilterMap);
  // json
  DEFINE_KCONF_NODE_LOAD(ProductFilterRatioConfig, ad.adtarget, productFilterRatioConfig)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 3 >= IMPL_BLOCK_BEGIN and 3 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_PROTOBUF_NODE_KCONF(ConvertedDataConf, ad.adtarget, convertedDataConf);
  DEFINE_PROTOBUF_NODE_KCONF(UniverseWakeupFilterConfig, ad.adtarget3, universeWakeupFilterConfig);
  DEFINE_PROTOBUF_NODE_KCONF(UniverseOcpxFilterByCrowdConfig, ad.adtarget3, universeOcpxFilterByCrowdConfig);
  DEFINE_PROTOBUF_NODE_KCONF(rankTruncateMapConfig, ad.adtarget, rankTruncateMap)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseOcpcToPackages, ad.adtarget2, universeOcpcToPackages);
  DEFINE_PROTOBUF_NODE_KCONF(UniverseOcpcToPackages, ad.adtarget2, universeOcpcToPackagesForAppAdvance);
  DEFINE_PROTOBUF_NODE_KCONF(TargetGroupMapInfo, ad.adtarget2, targetGroupMapInfo);
  DEFINE_PROTOBUF_NODE_KCONF(UselessPathConfig, ad.adtarget2, uselessPathConfig);
  DEFINE_PROTOBUF_NODE_KCONF(UnionMidPageWhiteConfig, ad.universeApi,
                                              aggregatePageProductNameAccountWhiteList)
  DEFINE_PROTOBUF_NODE_KCONF(UniversePaidProductListConf, ad.adtarget2, universePaidProductListConf)
  DEFINE_PROTOBUF_NODE_KCONF(UniversePaidProductListConf, ad.adtarget2, universeTaidProductListConf)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseAliBrowsedInfoFreqFilterConf,
                             ad.adtarget2, universeAliBrowsedInfoFreqFilterConf)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseBrowsedInfoFreqFilterConf,
                             ad.adtarget3, universeBrowsedInfoFreqFilterConf)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseProductListFreqConfig,
                             ad.adtarget2, universeProductListFreqConfig)

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, adStyleBlackSet);
  // 直营电商 id V3
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, DirectEcomIndustryIdsV3)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adEcDistribution, livingAuthorWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.materialGenerationServer, liveStreamVipAuthors)
  DEFINE_INT64_KCONF_NODE(ad.adserver, universeMaxAdPrerankCommonCount, 10000)  // 联盟粗排队列最大容量
  // user define data with load:  (type, namespace, name)
  // 前 n 刷必保策略 复用绿色通道配置文件类型
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 4 >= IMPL_BLOCK_BEGIN and 4 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_PROTOBUF_NODE_KCONF(ProductImprFreqConfig, ad.adtarget, productImprFreqConfig);
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 5 >= IMPL_BLOCK_BEGIN and 5 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_KCONF_NODE_LOAD(TableSearchOptions, ad.adtarget, tableSearchOptions)
  DEFINE_KCONF_NODE_LOAD(TraceConfig, ad.adtarget, universeTraceConfig)
  // string map
  DEFINE_STRING_STRING_MAP_KCONF(ad.adtarget, perfTimeCostOfAbtestKeyInTarget)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adtarget, SearchUdf2Table)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adtarget, SearchUdf2TruncateNum)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 6 >= IMPL_BLOCK_BEGIN and 6 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_INT64_MAP_KCONF(ad.adtarget, universeAccountThrowRate)
  // 联盟固定系数流控
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.adtarget2, universeAccountHardBtrMap);
  // map<string, double>
  // 联盟 app 分档点击比例控制
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.adtarget, productFilterRatio)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.adserver, adDspNegAppFilteRatioMap)
  // 联盟粗排精排队列长度降级预案
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.adtarget, UniverseRankLimiter)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adserver, adUniverseFakeUserExpandTarget)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adserver, UniverseDisableTargetSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget2, adExpandTargetSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget2, replayLaneIdSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget, universeInnerFansDisableTargetSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, universeInnerLoopTargetIgnoreLiveOcpxSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, universeInnerLoopTargetIgnoreVideoOcpxSet)
  DEFINE_SET_NODE_KCONF(int64, ad.adtarget, universeInnerLoopFansRetrVideoOcpxSet)
  DEFINE_SET_NODE_KCONF(int64, ad.adtarget, universeInnerLoopFansRetrBlackList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, nativeHiddenNobidTailWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, nativeHiddenNobidUnitWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adFrontDiffSwitches, universeTinySkipDarkPosId)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adFrontDiffSwitches, universeStoreDistributSkipDarkPosId)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adFrontDiffSwitches, universeSkipAllDarkAdPosId)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.engine, virtualGoldWhiteResourceId)
  // 阿里外投 posid 黑名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, aliOuterBlackListPosid)
  DEFINE_PROTOBUF_NODE_KCONF(PsDispatchPackConfig, ad.adtarget, multiRecallDispatchPackConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseMerchantKSAppInfoConfPb, ad.frontserver, UniverseMerchantKSAppInfo)
  // 联盟图文新素材打标
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, universeNewImageCreativeTagIndustryIdSet);
  // 多路分层配置
  DEFINE_PROTOBUF_NODE_KCONF(MultiParamConfig, ad.adtarget, multiParamsConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, merchantLocalLifeFilterCityRegion)
  DEFINE_PROTOBUF_NODE_KCONF(MerchantUserEmbeddingConf, ad.adtarget, merchantUserEmbeddingConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseRewardFilterInfo, ad.adtarget, universeRewardFilterInfoLessThan10s)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseRewardFilterInfo, ad.adtarget, universeRewardFilterInfoMoreThan60s)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseAggSceneBlackList, ad.adtarget2, universeAggSceneBlackList)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseSkipPackageConfig, ad.adtarget, universeSkipPackageConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseInnerMultiTags, ad.adtarget, universeInnerMultiTags)
  DEFINE_PROTOBUF_NODE_KCONF(PrivacyPolicyFilterConfig, ad.adtarget, privacyPolicyFilterConfig)
  DEFINE_PROTOBUF_NODE_KCONF(PermissionInfoFilterConfig, ad.adtarget, permissionInfoFilterConfig)
  DEFINE_PROTOBUF_NODE_KCONF(RealAppVersionFilterConfig, ad.adtarget, realAppVersionFilterConfig)
  DEFINE_PROTOBUF_NODE_KCONF(RealAppVersionFilterConfig, ad.adtarget3, honorFlowTargetConfig)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, universePhoneMaterialAllFillUids);
  DEFINE_PROTOBUF_NODE_KCONF(ManufacturerGreenChannelTrafficAdmitConfig, ad.adtarget2, manufacturerGreenChannelTrafficAdmitConfig)  // NOLINT
  DEFINE_INT64_KCONF_NODE(ad.adtarget2, universeManufacturerNonTinyWhiteTag, 424)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseSiteAssemblyConfig, ad.adtarget, universeSiteAssemblyConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseSiteAssemblyConfigV2, ad.adtarget, universeSiteAssemblyConfigV2)
  DEFINE_PROTOBUF_NODE_KCONF(NewUniverseAntispamConf, ad.adtarget2, newUniverseAntispamConf)
  DEFINE_PROTOBUF_NODE_KCONF(NewUniverseAntispamConf, ad.adtarget2, newUniverseAntispamConfTest)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseMediumFixedInvestToolConfig,
                             ad.adtarget,
                             universeMediumFixedInvestToolConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, freqTraceUserWLV2)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, stayRetargetTagSet)
  // 针对白名单用户放开风控
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, riskControlWhiteUserList)
  DEFINE_PROTOBUF_NODE_KCONF(DotExpConfig,
                             ad.adRank,
                             dotExpConfig)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 7 >= IMPL_BLOCK_BEGIN and 7 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_KCONF_NODE_LOAD(UniverseOptPosAccountFilter, ad.adtarget, universeOptimizationPosAccountFilter)
  DEFINE_KCONF_NODE_LOAD(UniverseOptPosProductFilter, ad.adtarget, universeOptimizationPosProductFilter)
  DEFINE_KCONF_NODE_LOAD(UniverseOptAppAccountFilter, ad.adtarget, universeOptimizationAppAccountFilter)
  DEFINE_KCONF_NODE_LOAD(UniverseOptAppProductFilter, ad.adtarget, universeOptimizationAppProductFilter)
  DEFINE_DOUBLE_KCONF_NODE(ad.adtarget2, universeInnerVideoRoasRoiThresh, 0.0)  // 联盟短视频 roas 目标 roi 过滤阈值  // NOLINT
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 8 >= IMPL_BLOCK_BEGIN and 8 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_JSON_NODE_KCONF(ad.adtarget, a1Config)
  DEFINE_PROTOBUF_NODE_KCONF(MultiDegradeConfig, ad.adtarget, multiRetrievalDegrade)
  //  https://kconf.corp.kuaishou.com/#/ad/adserver/antiExplodeParams
  DEFINE_JSON_NODE_KCONF(ad.adtarget, brpcSeriveConfig)
  DEFINE_JSON_NODE_KCONF(ad.adtarget, targetJsonConfig)
  // manual_config
  DEFINE_KCONF_NODE_LOAD(ks::ad_base::AppBlackListConfig, ad.adserver2, appBlackListConfig)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 9 >= IMPL_BLOCK_BEGIN and 9 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_PROTOBUF_NODE_KCONF(ProductFreqConfig, ad.adtarget, productFreqConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseRetargetSetConfig, ad.adtarget, universeRetargetSetConfig)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 10 >= IMPL_BLOCK_BEGIN and 10 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_KCONF_NODE_LOAD(DebugConfig, ad.adtarget, debugConfig)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adtarget, DeviceBrandMap)
  // 短视频涨粉跳过高层级人群过滤的白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, universeMerchantFollowSkipUserFilterAuthorList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, universeMerchantFollowSkipUserFilterAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, universeMerchantFollowSkipRoiAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, universeMerchantFollowSkipRoiAuthorList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, universeInnerShallowOcpcSkipEcpcAccountSet)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 11 >= IMPL_BLOCK_BEGIN and 11 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  // 双列限流的低观感二级行业
  DEFINE_PROTOBUF_NODE_KCONF(UeUnifyFilterABConf, ad.adtarget, ueUnifyFilterABConf);
  DEFINE_PROTOBUF_NODE_KCONF(UePassGroupTags, ad.adtarget, uePassGroupTags);  // [liaibao]
  DEFINE_PROTOBUF_NODE_KCONF(UeXfhsGroupTags, ad.adtarget, ueXfhsGroupTags);  // [liaibao]
  // 分级分发实验
  DEFINE_PROTOBUF_NODE_KCONF(RiskLabelInfo, crc.dispose, riskLabelInfo);  // [liaibao]
  // ue 规则引擎过滤
  DEFINE_PROTOBUF_NODE_KCONF(UeRuleEngineTagToOrder, ad.adtarget, ueRuleEngineTagToOrder);  // [liaibao]
  DEFINE_PROTOBUF_NODE_KCONF(UeRuleEngineOrderConf, ad.adtarget, ueRuleEngineOrderConf);  // [liaibao]
  DEFINE_PROTOBUF_NODE_KCONF(UeRuleEngineTagToExp, ad.adtarget, ueRuleEngineTagToExp);  // [liaibao]
  // 高举报账户治理策略
  DEFINE_PROTOBUF_NODE_KCONF(UeRiskAccountTagToExp, ad.adtarget, ueRiskAccountTagToExp);
  DEFINE_PROTOBUF_NODE_KCONF(UeRiskAccountTagToOrder, ad.adtarget, ueRiskAccountTagToOrder);
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 12 >= IMPL_BLOCK_BEGIN and 12 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_KCONF_NODE(ad.adtarget, adTargetSimplifyTracePercent, 0)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, universeInnerTencentRtaMultiTags)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget4, universeTinySearchRecallSuggestPackageMultiTags)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, merchantExpandUserBlackList)
  DEFINE_PROTOBUF_NODE_KCONF(PhotoRecallConfig, ad.adtarget, photoRecallConfig)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseBehaviorInterestKeywordTargetProtect, ad.adtarget,
                            universeBehaviorInterestKeywordTargetProtect)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseRegionTargetProtect, ad.adtarget, universeRegionTargetProtect)
  DEFINE_PROTOBUF_NODE_KCONF(PlayAbleSdkMinVersionConfig, ad.adtarget, playAbleSdkMinVersionConfig)
  DEFINE_PROTOBUF_NODE_KCONF(AuditAdAppGrade, audit.adCreative, auditAdAppGrade)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseInnerShallowMediaRecallConfig,
                              ad.adtarget2, universeInnerShallowMediaRecallConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SutuiFilterConfig, ad.adtarget, sutuiFilterConfig)
  DEFINE_INT32_KCONF_NODE(ad.adtarget, offTracePercent, 0)
  // budget 相关
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.adFlowControl, chargeTagNoControl)
  DEFINE_INT32_KCONF_NODE(ad.adFlowControl, defaultIntervalNumber, 1000000)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 13 >= IMPL_BLOCK_BEGIN and 13 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_SET_NODE_KCONF(int32_t, ad.adFlowControl, newDarkControlAccountTail)

  // dpa
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.dpaServer, dpaWhiteUsers)
  DEFINE_KCONF_NODE_LOAD(ks::ad_base::FlowBlackListConfig, ad.dpaServer, flowBlackListConfig)
  DEFINE_STRING_INT32_MAP_KCONF(ad.predict, model_cmd);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, universeSkipDiversitySecondIndustrySet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget, universeSkipDiversityProductNameSet)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableProgCreativeExp, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableProgCreativeExpOpt, false)

  // 联盟 dcaf 相关
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adtarget2, universeDcafRankBaseVersion, "lmd01")

  // 粗排缓存 相关
  DEFINE_PROTOBUF_NODE_KCONF(PrerankCacheConf, ad.adAutoParam, prerank_cache_window)

  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableNearlineInvertAnalysis, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableIntersectOpt, false)

  DEFINE_INT32_KCONF_NODE(ad.adserver2, amdAnalyseMod, 25)
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, enableAmdAnalyse, false)

  DEFINE_PROTOBUF_NODE_KCONF(CrowdTagRecallConfig, ad.adtarget, UniverseCrowdTagRecallConf);
  DEFINE_SET_NODE_KCONF(int64, ad.adtarget, UniverseInnerHighValueCrowdAuthorList);
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableRiskControlV2, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableTargetRequestPid, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableTargetingDistance, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableTargetingOperators, false);
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableMergeInnerIndex, false)

  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, universeEnableSetChannelData, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableUniverseTsmStat, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableUniverseTsmStatPerf, false)

  // dcaf 相关策略总开关
  DEFINE_BOOL_KCONF_NODE(ad.adAutoParam, disableDcafGlobal, false)
  // dcaf unify version
  DEFINE_PROTOBUF_NODE_KCONF(QuotaExploreConfig, ad.adAutoParam, quotaExploreConfig)
  DEFINE_STRING_STRING_MAP_KCONF(ad.adAutoParam, dcaf_biz_sub_page_id)  // sub_page_id : biz_list
  DEFINE_STRING_STRING_MAP_KCONF(ad.adAutoParam, dcaf_base_versions)  // biz : value
  DEFINE_STRING_STRING_MAP_KCONF(ad.adAutoParam, dcaf_rank_default_quota)  // biz : value
  DEFINE_INT32_KCONF_NODE(ad.adAutoParam, dcafOfflineRedisTimeOut, 10);
  DEFINE_INT32_KCONF_NODE(ad.adAutoParam, dcafOfflineRedisLifeTime, 3600);
  DEFINE_INT32_KCONF_NODE(ad.adAutoParam, dcafOfflineRedisKeyRandomMax, 100);

  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableUniverseTinyRecallDataDebug, false);
  DEFINE_PROTOBUF_NODE_KCONF(ActionTargetConfig, ad.adtarget2, actionTargetConfig)
  DEFINE_PROTOBUF_NODE_KCONF(NegIndustryFilterSet, ad.adtarget2, negIndustryFilterSet);
  DEFINE_INT64_INT64_MAP_KCONF(ad.adserver2, universeTinyQueryType2TagMap);
  DEFINE_TAILNUMBERV2_KCONF(
      ad.adtarget, enableDirectConnectPredictSvr,
      "100;;;");  // 对于海选模型 UserEmbedding 的访问是否直连
  DEFINE_TAILNUMBERV2_KCONF(
    ad.adtarget, PreselectDegrade2,
    "100;;;");  // 海选降级
  // 需要从规则引擎回滚的原 TableSearch 函数列表
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adtarget2, rollbackRuleFunctions)
  // AZ2.0 - Ann 一键降级
  DEFINE_INT32_KCONF_NODE(ad.adtarget2, annTriggerDegradeRate, 0);
  // ps 请求 reco user info 字段白名单
  DEFINE_JSON_NODE_KCONF(ad.adserver2, recoUserInfoWhiteList);
  // context data guard ad index 开关
  DEFINE_BOOL_KCONF_NODE(ad.adtarget2, contextDataIndexGuard, false);
  // 在线检索切换 ad table 和 ad index 配置
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adtarget2, searchIndexOption, "AD_INDEX");
  DEFINE_PROTOBUF_NODE_KCONF(UeRiskOrderLimit, ad.adtarget3, ueRiskFilterOrder);  // [liaibao]
  // 生体负向治理框架白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget3, shieldEquityCardSecIndV63Set)
  // [xiaowentao] 联盟动态算力 uplift 模型 - 召回 quota 到 ES 截断 quota（粗排 quota）的系数
  // 用于打平粗排模型算力 quota
  DEFINE_DOUBLE_KCONF_NODE(ad.adtarget2, universeUpliftSmartComputePrerankQuota, 0.8);
  // [huoyan03] 获取流量探索特征 redis 开关
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableUniverseFlowExplore, false);
  DEFINE_PROTOBUF_NODE_KCONF(
    UniverseSearchQueryAccountProductBlacklist, ad.adtarget3, universeSearchQueryAccountProductBlacklist)  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(UniverseTinySearchSuggestPackageListSplitPb,
                              ad.adtarget4, UniverseTinySearchSuggestPackageListSplit)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
};

}  // namespace ad_target
}  // namespace ks

#ifndef KCONF_CC_WITH_IMPL
#undef DEFINE_KCONF_NODE
#define DEFINE_KCONF_NODE(type, config_path, config_key, default_value) \
  DEFINE_KCONF_NODE_BODY(type, config_path, config_key, default_value)

#undef DEFINE_KCONF_NODE_LOAD
#define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key) \
  DEFINE_KCONF_NODE_LOAD_BODY(type, config_path, config_key)

#undef DEFINE_SET_NODE_KCONF
#define DEFINE_SET_NODE_KCONF(type, config_path, config_key) \
  DEFINE_SET_NODE_KCONF_BODY(type, config_path, config_key)

#undef DEFINE_KCONF_MAP_KCONF
#define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
  DEFINE_KCONF_MAP_KCONF_BODY(key_type, value_type, config_path, config_key)
#else
#undef DEFINE_KCONF_NODE_ATTR
#define DEFINE_KCONF_NODE_ATTR
#endif
