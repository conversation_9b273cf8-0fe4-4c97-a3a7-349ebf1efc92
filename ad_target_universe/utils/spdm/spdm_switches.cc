#include "teams/ad/ad_target_universe/utils/spdm/spdm_switches.h"

namespace ks {
namespace ad_target {

// 定义 kconf 开关:  SPDM_KCONF_BOOL(path, key);
// 定义 abtest 开关: SPDM_ABTEST_BOOL(key, AbtestBiz);

// 以下为 kconf 开关定义.
SPDM_KCONF_BOOL(ad.adtarget, enableUniverseRewardFilter);
SPDM_KCONF_BOOL(ad.adtarget2, enableUniverseInterveneConfig);
SPDM_KCONF_BOOL(ad.adtarget, enablePlayableInterstitialBlackSet);
SPDM_KCONF_BOOL(ad.adtarget3, diableWideAudienceExplore);  // [周帅印] 全店过滤

SPDM_KCONF_BOOL(ad.adFlowControl, enableVirtualGoldFilter);
SPDM_KCONF_BOOL(ad.adFlowControl, enableEspBudgetPartition);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseNewCreativeTagRecord);
SPDM_ABTEST_BOOL(enable_honor_support_market_app, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dark_ad_filter_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_brand_id_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_c_creative_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_media_only_app_installed_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_new_account_dark_day_num_v2, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_BOOL(enable_mini_app_ad_in_front, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adtarget2, enableTableSearchSimpleRecord);
SPDM_KCONF_BOOL(ad.adtarget2, universeFillMerchantItemId);
SPDM_KCONF_BOOL(ad.adtarget2, universeFillLiveStartTimeTarget);
SPDM_KCONF_BOOL(ad.adtarget2, universeInnerSupportOriginLive);
SPDM_KCONF_BOOL(ad.adtarget4, disableUniverseInspireDarkFilter);
SPDM_KCONF_BOOL(ad.adtarget4, disableUniverseSplashDarkFilter);
SPDM_KCONF_BOOL(ad.adtarget2, enableRtaUndertakeFilter);
SPDM_KCONF_BOOL(ad.adtarget2, universeSearchDataWriter);
SPDM_KCONF_BOOL(ad.adtarget2, universeUserShopDataDegrade);
SPDM_KCONF_BOOL(ad.adtarget2, universeSheildFilterWithFastapplicationv2);
SPDM_KCONF_BOOL(ad.adtarget2, enableUniverseTinySearchFilterPos);
SPDM_KCONF_BOOL(ad.adtarget2, enableUniverseOutSearcherParallelOpt);
SPDM_KCONF_BOOL(ad.adtarget3, enableSkipSiteAssemblyFilter);
SPDM_KCONF_BOOL(ad.adtarget3, enableUseUniverseChainGrayDataOffline);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseSearchQueryAccountProductFilter);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseAbSwitchOpt);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseBrowsedInfoFreqFilterNew);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseDarkCCreativeFilter);
SPDM_KCONF_BOOL(ad.adtarget3, universeDarkFilterOpt);
SPDM_KCONF_BOOL(ad.adtarget3, enableFixWhiteBoxFilterReason);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseAdStyleFilterRefactor);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseMingtouFilterCrowdId);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniversePacingControlFixHotkey);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniversePacingControlFixCompact);
SPDM_KCONF_INT32(ad.frontserver2, universeSendCostCopyNum, 10);
SPDM_KCONF_BOOL(ad.adtarget3, universeSkipUselessRiskControl);
SPDM_KCONF_BOOL(ad.adtarget3, enableOperationOrientUpgrade);
SPDM_ABTEST_INT64(new_account_filter_day_opt, ks::AbtestBiz::AD_DSP, 14);
SPDM_ABTEST_BOOL(enable_pv_campaign_type_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_store_search_skip_dark, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_store_distribut_skip_dark, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_distribut_flow_skip_search_ad_dark, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_filter_in_table_search, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target2rank_second_industry_5, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(skip_universe_tiny_search_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(skip_universe_tiny_search_filter_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_ecom_spu_switch_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_wechat_rta_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_rta_filter_sample, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_wt_unit_exit_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_photo_exit_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_wt_photo_exit_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_account_exit_exit_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_wt_account_exit_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_campaign_exit_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target_universe_targeting_operators, ks::AbtestBiz::AD_DSP);

SPDM_KCONF_INT32(ad.adtarget2, onlyPecSceneId, 3);
// 以下为 kconf int64 参数声明
SPDM_KCONF_INT64(ad.adtarget, immTriggerTokenMaxQuota, 5000);
SPDM_KCONF_INT64(ad.adtarget, immPerTokenExpandMaxQuota, 200);
SPDM_KCONF_INT64(ad.adtarget, immTotalCreativeMaxQuota, 50000);
SPDM_KCONF_INT64(ad.adtarget3, universeFollowListTruncateNum, 1000);


SPDM_KCONF_BOOL(ad.adtarget2, universeTinyPrerankOpt);

// 以下为 kconf double 参数声明

// 以下为 abtest 开关声明.
SPDM_ABTEST_BOOL(enable_prerank_industry_live_diversity, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mobile_hard_p2l_ocpm_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_del_dup_account, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_age_expand, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_product_impr_freq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_ad_installed_app_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merge_creative_opt_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_high_level_user_support_ep, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_low_level_user_support_ep, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_low_user_data_only_cache, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_residence_adcode_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_aigc_holdout, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(week_stay_retarget_tag, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_universe_product_skip_diversity, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_wide_material_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_age_merge_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_degrade_by_medium, ks::AbtestBiz::AD_DSP);

// [liuchangjin] 粗排完全排序计费分离开关
SPDM_ABTEST_BOOL(enable_live_stream_author_filter, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_universe_sug_api_async, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_sug_search_a_2_b, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_sug_search_a_2_b_floor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sug_query_type_optimize, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_expandbyrelevance_remove_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_huge_migrate_tsm_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_tx_scene_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_tx_scene_auto, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_adjust_brecall_quato_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_adjust_recall_quato_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_at_skip_rta_table_universe_tiny, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_live_fans_max_num, ks::AbtestBiz::AD_DSP, 300);
SPDM_ABTEST_BOOL(enable_universe_top_app_cost_unit_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_post_cpm_prefix_key, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_universe_all_post_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_unit_app_cost_threshold, ks::AbtestBiz::AD_DSP, 10000);
SPDM_ABTEST_STRING(universe_141_ocpx_config, ks::AbtestBiz::AD_DSP, "395;");
SPDM_ABTEST_INT64(universe_unit_recall_limit_num, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(universe_lc_dis_top_recall_level, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_BOOL(enable_universe_inner_fans_short_video, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_ocpx_recall_fans, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_redirect_recall_append, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_u2author_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_live_and_merchant_dp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_photo_recall_append, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_universe_live_mt_only, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_action_app_fill_conv_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_inner_loop_fans_target_expand, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dark_c_skip_dark_control, ks::AbtestBiz::AD_DSP);

// [luochao, chenxian] 搜索重定向专项，query 频控次数
SPDM_ABTEST_INT64(search_retarget_frequency_control, ks::AbtestBiz::AD_DSP, 1);

SPDM_ABTEST_STRING(universe_realtime_recall_tag_key,  ks::AbtestBiz::AD_DSP, "base");

SPDM_ABTEST_BOOL(universe_site_assembly_filter_opt_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_support_video_sdk, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_esp_mobile_fill_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_account_type_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(short_play_time_interval, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(short_play_photo_check_seconds, ks::AbtestBiz::AD_DSP, 14400);
SPDM_ABTEST_INT64(short_play_photo_industry_check_seconds, ks::AbtestBiz::AD_DSP, 3600);
SPDM_ABTEST_INT64(short_play_photo_first_industry_check_seconds, ks::AbtestBiz::AD_DSP, 3600);
SPDM_ABTEST_INT64(universe_retarget_retrieval_creative_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(universe_retarget_retrieval_author_num, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_BOOL(enable_universe_wt_live_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_live_pay_yellow_chart_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_live_roas_yellow_chart_filter, ks::AbtestBiz::AD_DSP);

SPDM_KCONF_BOOL(ad.adtarget2, enable_add_model_meta_to_tracelog);

SPDM_ABTEST_BOOL(enable_universe_merchant_follow_fast, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_site_assembly_filter_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_filter_creative_type_not_match, ks::AbtestBiz::AD_DSP);
// [songxu] 用户目标行为过滤开关 新
SPDM_ABTEST_BOOL(enable_action_target_product_filter_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_action_target_product_filter_group, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_universe_query_retarget_merge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_recall_es_selected_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_effect_layer_zigzag, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_fix_unit_index, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_tiny_search_package_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_tiny_search_recall_diverse_by_index, ks::AbtestBiz::AD_DSP);

// ------------------------------abtest int64  参数定义  ----------------------------------


SPDM_ABTEST_INT64(splash_inner_prerank_quota, ks::AbtestBiz::AD_DSP, 70);
SPDM_ABTEST_INT64(session_cache_ttl, ks::AbtestBiz::AD_DSP, 180);
SPDM_ABTEST_INT64(session_cache_quota, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(cache_admit_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(cache_exit_quota, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(cache_outer_extra_ranking_quota, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(session_cache_target_type, ks::AbtestBiz::AD_DSP, 0);
// ------------------------------abtest double 参数定义 ----------------------------------
SPDM_ABTEST_DOUBLE(universe_ranking_main_quota_ratio, ks::AbtestBiz::AD_DSP, 0.9);
SPDM_ABTEST_DOUBLE(univ_multi_retr_sn_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(univ_main_layer_recall_quota_ratio, ks::AbtestBiz::AD_DSP, 0.6);
SPDM_ABTEST_DOUBLE(univ_inner_deep_layer_recall_quota_ratio, ks::AbtestBiz::AD_DSP, 0.2);
SPDM_ABTEST_BOOL(enable_universe_dark_skip_celebrity_label, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_esp_appeal_target_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_thanos_switch_risk, ks::AbtestBiz::AD_DSP);

// ---------------------- abtest int64 参数声明 -------------------
// [zhangruyuan]
SPDM_ABTEST_INT64(download_ad_disk_free_threshold, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_INT64(ue_feed_explore_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(ue_feed_inner_explore_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(ue_limit_free_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(ue_pass_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_ue_specialpass_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_recall_inner_supported_group_tag, ks::AbtestBiz::AD_DSP, 0);
// [liaibao] 分级分发评估实验 1.0
SPDM_ABTEST_INT64(adue_risklevel_first_industryid, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adue_risklevel_filter_levelthr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adue_risklevel_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
// [liaibao] 分级分发评估实验 3.0
SPDM_ABTEST_BOOL(enable_adue_risklevel_evaluate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adue_risklevel_eval_filter_skip, ks::AbtestBiz::AD_DSP);
// [liaibao] 规则引擎过滤
SPDM_ABTEST_BOOL(enable_ue_ruleengine_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(adue_ruleengine_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
// 单账户过滤策略开关
SPDM_ABTEST_BOOL(enable_ue_single_account_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(single_account_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
// [dengchijun] 高举报账户过滤
SPDM_ABTEST_BOOL(enable_ue_risk_account_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(flow_grading_report_filter_exp_group, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_ue_sensitive_user_filter, ks::AbtestBiz::AD_DSP);
// [dengchijun] 分级分发举报敏感用户过滤
SPDM_ABTEST_BOOL(enable_ue_grading_report_user_filter, ks::AbtestBiz::AD_DSP);
// [dengchijun] 举报敏感用户 redis
SPDM_ABTEST_STRING(user_report_prob_prefix, ks::AbtestBiz::AD_DSP, "user_tag_predict_report_submit:");

// ------------------------------abtest string 参数定义 ------------
SPDM_ABTEST_STRING(useless_path_ab_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(universe_ocpx_filter_by_crowd, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(dcaf_rank_version_cache, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(skip_media_ssp_sync_creative_blacklist, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_effect_path, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(univ_skip_prerank_retrival_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(univ_inner_deep_effect_layer_path, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_new_pacing_control_key_prefix, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_new_pacing_control_ad_keys, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(universe_pacing_control_filter_type, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(universe_tiny_search_recall_diverse_by_index_abtag, ks::AbtestBiz::AD_DSP, "base");

// ---------------------- kconf int64 参数声明 -------------------

SPDM_ABTEST_BOOL(enable_universe_esp_mobile_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_live_no_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_multi_retr_quota_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_prerank_user_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_recall_industry_user_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_ad4reco_recall_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univers_out_retarget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_llm_u2p_recall_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_llm_u2p_v2_recall_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_ks_search_ecom_data, ks::AbtestBiz::AD_DSP);

// [qianyangchao] 联盟小系统跳过已转化过滤
SPDM_ABTEST_BOOL(enable_universe_tiny_skip_converted_filter, ks::AbtestBiz::AD_DSP);

// [liuzichen05] ps 实验名称
SPDM_ABTEST_STRING(target_ps_adjust_exp_tag, ks::AbtestBiz::AD_DSP, "");
// [rongyu03] 游戏行业重定向开关
SPDM_ABTEST_BOOL(enable_game_olp_retarget_pv, ks::AbtestBiz::AD_DSP);
// [rongyu03] 游戏行业重定向新增通路开关
SPDM_ABTEST_BOOL(enable_game_olp_retarget_new_path, ks::AbtestBiz::AD_DSP);
// [lizemin] ps 请求清理 reco user info 细粒度裁剪
SPDM_ABTEST_BOOL(enable_reco_user_info_trim_for_ps, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_universe_request_ordered_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_ep_follow_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_api_consultation, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_quick_search_skip_budget_control_universe, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_direct_search_skip_budget_control_universe, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_INT32(ad.adtarget2, universeTraceTsmRatio, 0);

SPDM_KCONF_BOOL(ad.adtarget2, enableEsQuotaOpt)

SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseTransparentQcpxTarget)

SPDM_KCONF_BOOL(ad.adtarget2, enableSearchDarkFix);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseFillPostEcpmFix);

SPDM_KCONF_BOOL(ad.adtarget3, skipWxShortAppAdFilterHarmonyFilter);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseHarmonySiteAssemblyFilter);

SPDM_ABTEST_INT64(effect_layer_max_traverse_num_universe, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(effect_layer_truncate_quota_by_elastic_index, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(enable_universe_fre_time_filter, ks::AbtestBiz::AD_DSP, "base");
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseOpLatencyDotV2);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseSearchQueryBidwordInfo);
SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseTempUserXBudgetFilter);
SPDM_ABTEST_BOOL(universe_dark_budget_free_quick_search, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_x_account_blacklist, ks::AbtestBiz::AD_DSP);

SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseWechatIdConvFilter);

SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseAdTypeFix);

SPDM_KCONF_BOOL(ad.adtarget3, enableUniverseKwaiBookFilter);

SPDM_ABTEST_BOOL(enable_universe_dcaf_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(skip_universe_dcaf_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dcaf_timeout_log, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_universe_antispam_filter_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_click_freq_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_elastic_ab_conf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_elastic_ab_conf, ks::AbtestBiz::AD_DSP, "");
SPDM_KCONF_BOOL(ad.adtarget3, enableRetLLMU2pProducts);
}  // namespace ad_target
}  // namespace ks
