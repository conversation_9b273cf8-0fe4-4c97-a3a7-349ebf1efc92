#pragma once

#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"
#include "teams/ad/universe_base/prerank/utils/spdm/spdm_switches.h"
namespace ks {
namespace ad_target {

// SPDM 使用文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIDGfeGPH2sQAxiBwuiaJ4

// 声明 kconf 开关:  DECLARE_SPDM_KCONF_BOOL(key);  // [owner] 开关用途/作用
// 声明 abtest 开关: DECLARE_SPDM_ABTEST_BOOL(key);  // [owner] 开关用途/作用

// 所有 spdm 开关在 spdm_switches.h 中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在 spdm_switches.h 中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在 spdm_switches.h 中声明, 在 spdm_switches.cc 中定义.

// 以下为 kconf 开关声明.
DECLARE_SPDM_KCONF_BOOL(enableUniverseNewCreativeTagRecord)  // [wanglei10] 联盟新创意打点
DECLARE_SPDM_KCONF_BOOL(enableUniverseRewardFilter);  // [尹亮] 联盟开启对部分媒体的激励视频时长过滤
DECLARE_SPDM_KCONF_BOOL(diableWideAudienceExplore);  // [周帅印] 全店过滤
DECLARE_SPDM_KCONF_BOOL(enableUniverseInterveneConfig);  // [yinliang] 是否开启联盟搜索人工干预配置
DECLARE_SPDM_KCONF_BOOL(enablePlayableInterstitialBlackSet);  // [尹亮] 联盟插屏广告媒体黑名单开关
DECLARE_SPDM_KCONF_BOOL(enableEspBudgetPartition);  // [liming11] 磁力金牛是否切换成新的预算隔离 tag
DECLARE_SPDM_ABTEST_INT64(universe_new_account_dark_day_num_v2)    // [liuyibo05] 联盟新客保护期天数实验，默认 3 天 NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_honor_support_market_app)  // [liuyibo05] 荣耀流量拉新预算只投放应用商店上架应用 NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_media_only_app_installed_filter)   // [liuyibo05] 厂商媒体只用安装列表做已安装过滤，不考虑转化数据 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_mini_app_ad_in_front);     // [liuyibo05] 支持快小游广告流量准入逻辑优化
DECLARE_SPDM_KCONF_BOOL(enableVirtualGoldFilter);  // [liming11] 虚拟金过滤
DECLARE_SPDM_ABTEST_INT64(new_account_filter_day_opt);  // [liuyibo05] 暗投新客保护期天数实验
DECLARE_SPDM_ABTEST_BOOL(skip_universe_tiny_search_filter);  // [tanghaihong] 联盟是否过滤搜索暗投小系统广告
DECLARE_SPDM_ABTEST_BOOL(skip_universe_tiny_search_filter_v2);  // [tanghaihong] 联盟是否过滤搜索暗投小系统广告 v2 // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseTinySearchFilterPos);  // [tanghaihong] 联盟小系统按广告位过滤搜索直投广告 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_target_universe_targeting_operators);  // [tanghaihong] 联盟运营商定向 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_dark_ad_filter_exp);  // [liuyibo05] 暗投准入控制实验
DECLARE_SPDM_KCONF_BOOL(enableUniverseOutSearcherParallelOpt);  // [jiangyifan] 联盟tsm out_searcher查倒排与等定向结果并行化 // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseBrowsedInfoFreqFilterNew);  // [jiangyifan] 联盟新频控开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_antispam_filter_test);  // [jiangyifan] 联盟使用新反作弊过滤规则
DECLARE_SPDM_ABTEST_BOOL(enable_universe_click_freq_fix);  // [jiangyifan] 点击频控修复开关
DECLARE_SPDM_KCONF_BOOL(enableUniverseAdStyleFilterRefactor);  // [jiangyifan] 场景定投使用重构后代码开关
DECLARE_SPDM_KCONF_BOOL(enableUniverseMingtouFilterCrowdId);  // [jiangyifan] 联盟明暗投定向支持人群包 id 以及场景  // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseSearchQueryAccountProductFilter);  // [jiangyifan] 联盟搜索屏蔽词过滤开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_store_search_skip_dark);     // [liuyibo05] 联盟小系统商店搜索流量跳过暗投控比 NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_store_distribut_skip_dark);     // [liuyibo05] 联盟商店分发流量跳过暗投控比 NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_distribut_flow_skip_search_ad_dark);     // [liuyibo05] 支持搜索预算联盟商店分发流量跳过暗投控比 NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_wechat_rta_filter);    // [liuyibo05] 联盟微信小游戏 rta 过滤
DECLARE_SPDM_ABTEST_BOOL(enable_universe_rta_filter_sample);    // [dengtiancong] 联盟 rta 过滤采样
DECLARE_SPDM_ABTEST_BOOL(enable_filter_in_table_search);    // [liuyibo05] vivo 创意频控过滤前置到 table_search  // NOLINT
DECLARE_SPDM_KCONF_BOOL(universeTinyPrerankOpt);   // [wanglei10] 联盟 tiny 部署 粗排优化
DECLARE_SPDM_KCONF_BOOL(universeUserShopDataDegrade);  // [wanglei10] 联盟用户购买数据降级
DECLARE_SPDM_KCONF_BOOL(universeSheildFilterWithFastapplicationv2);  // [zhongziwei] 快应用标签屏蔽
DECLARE_SPDM_KCONF_BOOL(enableTableSearchSimpleRecord);  // [wanglei10] 联盟打印 table_search 过滤原因
DECLARE_SPDM_KCONF_BOOL(enableSkipSiteAssemblyFilter);  // [wanglei10] 联盟建站过滤支持 ab 开关
DECLARE_SPDM_KCONF_BOOL(enableUniverseDarkCCreativeFilter);  // [wanglei10] 联盟 dark c 分级过滤
DECLARE_SPDM_KCONF_BOOL(universeDarkFilterOpt);  // [wanglei10] 联盟暗投过滤修复
DECLARE_SPDM_ABTEST_BOOL(enable_pv_campaign_type_filter);

DECLARE_SPDM_ABTEST_BOOL(universe_degrade_by_medium);  //  [liangli] 队列降级开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_dark_c_skip_dark_control);  // [liuyibo05] 暗投 C 类创意跳过暗投控比逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_universe_c_creative_filter);    // [liuyibo05] 联盟 C 类创意风控下探分级过滤

DECLARE_SPDM_KCONF_BOOL(enable_add_model_meta_to_tracelog);   // [houjian] enable_add_model_meta_to_tracelog

DECLARE_SPDM_KCONF_BOOL(universeFillMerchantItemId);  //  [wanglei10] 联盟下发商品 id
DECLARE_SPDM_ABTEST_INT64(search_retarget_frequency_control);  // [luochao, chenxian] 搜索重定向专项
DECLARE_SPDM_KCONF_BOOL(universeFillLiveStartTimeTarget);  //  [wanglei10] 联盟下发直播开播时间
DECLARE_SPDM_KCONF_BOOL(universeInnerSupportOriginLive);  // [wanglei10] 联盟内循环支持原生直播
DECLARE_SPDM_KCONF_BOOL(disableUniverseInspireDarkFilter);  // [libaolin03] 下线联盟激励暗投白名单
DECLARE_SPDM_KCONF_BOOL(disableUniverseSplashDarkFilter);  // [libaolin03] 下线联盟开屏暗投白名单
DECLARE_SPDM_KCONF_BOOL(enableRtaUndertakeFilter);  // [wanglei10] rta unit 兜底过滤
DECLARE_SPDM_KCONF_BOOL(universeSearchDataWriter);  // [liangli] 联盟应用商店搜索数据写入
DECLARE_SPDM_KCONF_BOOL(enableUseUniverseChainGrayDataOffline);  // [wanglei10] 联盟预算灰度放量过滤迁移到索引构建 // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseAbSwitchOpt);  // [wanglei10] ab 开关优化 // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableFixWhiteBoxFilterReason);  // [shanminghui] 白盒过滤原因修复 // NOLINT
DECLARE_SPDM_KCONF_BOOL(universeSkipUselessRiskControl);  // [wanglei10] 跳过无效风控
DECLARE_SPDM_KCONF_BOOL(enableOperationOrientUpgrade);  // [shanminghui] 联盟明暗投定向升级  // NOLINT

// 以下为 kconf int32 参数声明
DECLARE_SPDM_KCONF_INT32(onlyPecSceneId);  // [zhangruyuan] 只出互动场景的场景 ID

// 以下为 kconf int64 参数声明
DECLARE_SPDM_KCONF_INT64(immTriggerTokenMaxQuota);  //  [limingxi] 召回专用 quota
DECLARE_SPDM_KCONF_INT64(immPerTokenExpandMaxQuota);  //  [limingxi] 召回专用 quota
DECLARE_SPDM_KCONF_INT64(immTotalCreativeMaxQuota);  //  [limingxi] 召回专用 quota
DECLARE_SPDM_KCONF_INT64(universeFollowListTruncateNum);  //  [wanglei10] 关注列表截断数

// 以下为 kconf double 参数声明

// 以下为 abtest 开关声明.
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_industry_live_diversity);
DECLARE_SPDM_ABTEST_BOOL(enable_merge_creative_opt_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_hard_p2l_ocpm_admit);  // [huangzhaokai] 是否放开移动端硬广引流 ocpm // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_product_impr_freq);  // [zhangyunfei03] 联盟产品粒度频控 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_product_skip_diversity);  // [yangjinhui] 联盟账户跳过多样性过滤
DECLARE_SPDM_ABTEST_BOOL(enable_universe_brand_id_target)  // [liuyibo05] 联盟品牌 id 定向开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ecom_spu_switch_prerank);  // [yingyuanxiang] 联盟粗排切换电商 SPU 开关    // NOLINT
DECLARE_SPDM_ABTEST_BOOL(universe_site_assembly_filter_opt_v2);  // [wanglei10] 组件屏蔽优化
DECLARE_SPDM_ABTEST_BOOL(enable_universe_residence_adcode_target);  // [wanglei10] 联盟居住地定向
DECLARE_SPDM_ABTEST_BOOL(enable_universe_aigc_holdout);  // [zhangyunhao03] 联盟 aigc holdout 实验

DECLARE_SPDM_ABTEST_BOOL(enable_thanos_switch_risk);  // [zhangruyuan] 信息流风控切换
DECLARE_SPDM_ABTEST_BOOL(enable_close_wide_material_filter);  // [zhangyunhao03] 关闭素材类型过滤逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_ad_prerank_user_tag);   // [shenchong03] 粗排用户分层开关
DECLARE_SPDM_ABTEST_BOOL(enable_ad_recall_industry_user_tag);  // [ruanjing] 召回行业高价值人群标签
DECLARE_SPDM_ABTEST_BOOL(enable_universe_filter_creative_type_not_match);  // [tanghaihong] 联盟下线 CREATIVE_TYPE_NOT_MATCH 过滤 // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_live_stream_author_filter);  // [liuwenlong03] live_stream.user_id 频控过滤开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_del_dup_account);  //  [gongxiaopeng03] 联盟多样性过滤开关

DECLARE_SPDM_ABTEST_BOOL(enable_universe_sug_api_async);     //  [yinliang] 联盟异步调用 sug
DECLARE_SPDM_ABTEST_BOOL(enable_universe_sug_search_a_2_b);     //  [yinliang] 联盟 sug 搜 a 出 b 优化
DECLARE_SPDM_ABTEST_BOOL(enable_universe_sug_search_a_2_b_floor);  //  [yinliang] 联盟 sug 搜 a 出 b 优化, 兜底  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_sug_query_type_optimize);     //  [yinliang] 联盟 sug 打分优化
DECLARE_SPDM_ABTEST_BOOL(enable_expandbyrelevance_remove_ratio);     //  [yinliang] 联盟搜索多样性优化
DECLARE_SPDM_ABTEST_BOOL(enable_universe_huge_migrate_tsm_fix);  // [gongxiaopeng03] 联盟大系统迁移 TSM fix
DECLARE_SPDM_ABTEST_DOUBLE(universe_adjust_brecall_quato_ratio);  // [yinliang] 调整召回通路的 quato ratio
DECLARE_SPDM_ABTEST_DOUBLE(universe_adjust_recall_quato_ratio);  // [yinliang] 联盟引流进人非粉粗排打压
DECLARE_SPDM_ABTEST_BOOL(enable_at_skip_rta_table_universe_tiny);   // [qianyangchao] 联盟小系统绕过 rta_table
DECLARE_SPDM_ABTEST_BOOL(enable_universe_merchant_follow_fast);  // [wanglei10] 联盟涨粉速度开关
DECLARE_SPDM_ABTEST_BOOL(universe_site_assembly_filter_opt);  // [wanglei10] 联盟建站组件屏蔽优化

DECLARE_SPDM_ABTEST_BOOL(enable_universe_esp_appeal_target_opt);    // [wanglei10] engine_population 定向优化
DECLARE_SPDM_ABTEST_BOOL(enable_universe_high_level_user_support_ep);    // [wanglei10] 联盟内循环高质量人群 支持 ep 定向  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_low_level_user_support_ep);    // [wanglei10] 联盟内循环非高质量人群 支持 ep 定向  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_low_user_data_only_cache);    // [wanglei10] 联盟内循环非高质量人群获取内循环行为数据只走缓存  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(universe_inner_loop_fans_target_expand);  // [yingyuanxiang] 联盟内循环粉丝定向拓展开关  // NOLINT

DECLARE_SPDM_ABTEST_INT64(week_stay_retarget_tag);

DECLARE_SPDM_ABTEST_BOOL(enable_esp_mobile_fill_fix);   // [wanghongfei] 召回多样性过滤门槛
DECLARE_SPDM_ABTEST_BOOL(enable_splash_support_video_sdk);
// [zengzhengda] 联盟活跃 app 补充定向已激活 v2
DECLARE_SPDM_ABTEST_BOOL(enable_universe_action_app_fill_conv_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_account_type_filter);   // [liuxiaoyan]  聚星频控 1.0
DECLARE_SPDM_ABTEST_DOUBLE(short_play_time_interval);   // [liuxiaoyan]  新快滑广告播放时长
DECLARE_SPDM_ABTEST_INT64(short_play_photo_check_seconds);  // [liuxiaoyan]  快滑广告多长时间不出
DECLARE_SPDM_ABTEST_INT64(short_play_photo_industry_check_seconds);  // [liuxiaoyan]  快滑广告多长时间不出
DECLARE_SPDM_ABTEST_INT64(short_play_photo_first_industry_check_seconds);  // [rentingyu]  一级行业快滑广告多长时间不出 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_esp_mobile_live);  // [wanglei10] 联盟支持磁力金牛移动端 ab 开关
DECLARE_SPDM_ABTEST_STRING(universe_effect_path);  // [dengtiancong] 效果类通路可配置 ab 参数
DECLARE_SPDM_ABTEST_STRING(univ_skip_prerank_retrival_tag);  // [chendongdong] 跳过粗排的召回通路 tag
DECLARE_SPDM_ABTEST_STRING(univ_inner_deep_effect_layer_path);  // [chendongdong] 内循环效果通路集合
DECLARE_SPDM_ABTEST_BOOL(universe_multi_retr_quota_opt);
// [bianfeifei]
DECLARE_SPDM_ABTEST_BOOL(enable_universe_age_merge_filter);     // [liuyibo05] 联盟年龄合并白名单实验
DECLARE_SPDM_ABTEST_BOOL(enable_universe_age_expand);   // [liuyibo05] 联盟年龄定向扩展实验
DECLARE_SPDM_ABTEST_BOOL(enable_rta_ad_installed_app_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_action_target_product_filter_2);  //  [songxu] 用户目标行为过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_action_target_product_filter_group);  //  [songxu] 用户目标行为过滤开关

DECLARE_SPDM_ABTEST_BOOL(enable_target2rank_second_industry_5);  // [zhangruyuan] 透传行业 5.0 二级行业 // NOLINT

DECLARE_SPDM_ABTEST_STRING(universe_post_cpm_prefix_key);  // [haozhitong] 联盟 post cpm 前缀 key
DECLARE_SPDM_ABTEST_BOOL(enable_universe_all_post_cpm);  // [haozhitong] 联盟 post cpm 全局召回开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_top_app_cost_unit_recall);  // [linglong] 头部商家召回开关
DECLARE_SPDM_ABTEST_INT64(universe_unit_app_cost_threshold);  // [linglong] 头部商家召回 主站创意消耗阈值
DECLARE_SPDM_ABTEST_STRING(universe_141_ocpx_config);  // [linglong] 头部商家召回 ocpx 配置
DECLARE_SPDM_ABTEST_INT64(universe_unit_recall_limit_num);  // [linglong] 头部商家召回 单 unit 召回数量限制
DECLARE_SPDM_ABTEST_INT64(universe_lc_dis_top_recall_level);  // [linglong] 厂商分发头部商家召回 level 限制
// [wuhan12] 联盟内循环高价值人群召回 & 粗排爬坡 AB tag key
DECLARE_SPDM_ABTEST_BOOL(disable_universe_live_mt_only);  // [xiemiao] 联盟内循环直播浅度召回明投开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_wt_live_target);   // [yingyuanxiang] 联盟接入直播宽表  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_live_pay_yellow_chart_filter);     // [yingyuanxiang] 联盟直播订单未挂车过滤  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_live_roas_yellow_chart_filter);    // [yingyuanxiang] 联盟直播 ROAS 未挂车过滤 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ad4reco_recall_data);    // [yingyuanxiang] 联盟接入 ad4reco 召回数据 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_univers_out_retarget);    // [yangjinhui] 联盟重定向召回数据 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_llm_u2p_recall_data);    // [huoyan03] 联盟LLM重定向召回数据 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_llm_u2p_v2_recall_data);    // [huoyan03] 联盟LLM重定向召回数据 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ks_search_ecom_data);  // [huoyan03] 联盟使用主站搜索电商数据  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_photo_recall_append);    // [yingyuanxiang] 联盟内循环 photo 召回补充 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tx_scene_v2);    // [gongxiaopeng03] 生效联盟通信召回场景开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tx_scene_auto);    // [songchuanjiang] 生效联盟通信召回场景开关 // NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_new_pacing_control_key_prefix);   // [xuxuejian] 联盟实时流控数据前缀 // NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_new_pacing_control_ad_keys);   // [xuxuejian] 联盟实时流控单元标签 // NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_pacing_control_filter_type);   // [xuxuejian] 联盟实时流控过滤类型 // NOLINT
// [huangwenbin] 厂商
// [huangwenbin] 内循环
DECLARE_SPDM_ABTEST_STRING(universe_realtime_recall_tag_key);  // [huangwenbin] 实时召回实验组别
DECLARE_SPDM_ABTEST_INT64(universe_live_fans_max_num);  // [huangwenbin] 联盟内循环直播粉丝量最大值截断
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_fans_short_video);  // [huangwenbin] 联盟内循环粉丝短视频开关
DECLARE_SPDM_ABTEST_BOOL(enable_new_ocpx_recall_fans);  // [huangwenbin] 联盟内循环新预算兼容 粉丝召回
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_redirect_recall_append);  // [huangwenbin] 联盟内循环重定向召回兜底开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_live_and_merchant_dp);  // [huangwenbin] 联盟内循环浅度广告召回
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_u2author_recall);  // [huangwenbin] 联盟内循环 u2author 数据利用召回策略开关  // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_fix_unit_index);  // [shanminghui] 联盟 GetUnitIndex 修复  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_search_package_flow);  // [libaolin03] 联盟厂商搜索位次信息利用召粗精总开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_search_recall_diverse_by_index);  // [libaolin03] 厂商搜索多样性开关 基于位次信息 // NOLINT

// ---------------------- abtest int64 参数声明 -------------------
DECLARE_SPDM_ABTEST_INT64(splash_inner_prerank_quota);  // [liubing05] 开屏内循环 quota
DECLARE_SPDM_ABTEST_INT64(session_cache_ttl);  // [sunkang] 粗排缓存过期时间，单位：秒
DECLARE_SPDM_ABTEST_INT64(session_cache_quota);  // [sunkang] 缓存广告 quota
DECLARE_SPDM_ABTEST_INT64(cache_admit_quota);  // [sunkang] 缓存准入 quota 控制
DECLARE_SPDM_ABTEST_INT64(cache_exit_quota);  // [sunkang] 缓存退场 quota 控制
DECLARE_SPDM_ABTEST_INT64(cache_outer_extra_ranking_quota);  // [sunkang] 命中缓存流量 外循环额外粗排入口 quota  // NOLINT
DECLARE_SPDM_ABTEST_INT64(session_cache_target_type);  // [sunkang] 粗排缓存 服务生效控制
// ---------------------- abtest double 参数声明 -------------------
DECLARE_SPDM_ABTEST_INT64(download_ad_disk_free_threshold);  // [zhangruyuan] 下载类广告要求内存大小阈值

DECLARE_SPDM_ABTEST_DOUBLE(universe_ranking_main_quota_ratio);    // [wanglei10] 联盟召回截断主路比例

DECLARE_SPDM_ABTEST_STRING(universe_ocpx_filter_by_crowd);  // [yangzhanwu] 联盟 ocpx 人群过滤
DECLARE_SPDM_ABTEST_STRING(dcaf_rank_version_cache);  // [sunkang] 动态缓存版本控制

// ---------------------- 内粉专用 参数声明区域（防 git 冲突） -------------------
// ---------------------- abtest bool 参数声明 -------------------

// [qiyifan] 粉条 item&bid_type&ocpx 重复过滤 迁移到召回
DECLARE_SPDM_ABTEST_BOOL(enable_universe_dark_skip_celebrity_label);  // [wanglei10] 联盟跳过暗投网红定向
DECLARE_SPDM_ABTEST_BOOL(enable_universe_query_retarget_merge);   // [liangli]  联盟小系统搜索场景合并策略：相关性优先  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_effect_layer_zigzag);  // [chendongdong] 联盟效果类通路 zigzag 合并方式  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_univ_recall_es_selected_fix);  // [chendongdong] 联盟 es 合并方式修复
// ---------------------- abtest int64 参数声明 -------------------
// [dengchijun] 主站发现页双列低观感治理
DECLARE_SPDM_ABTEST_INT64(ue_feed_explore_tag);
DECLARE_SPDM_ABTEST_INT64(ue_feed_inner_explore_tag);
// [liaibao] 特批素材放行评估实验
DECLARE_SPDM_ABTEST_INT64(ue_pass_filter_exp_group);
DECLARE_SPDM_ABTEST_BOOL(enable_ue_specialpass_filter);
// [liaibao] 分级分发评估实验 1.0
DECLARE_SPDM_ABTEST_INT64(adue_risklevel_first_industryid);
DECLARE_SPDM_ABTEST_INT64(adue_risklevel_filter_levelthr);
DECLARE_SPDM_ABTEST_INT64(adue_risklevel_filter_exp_group);
// [liaibao] 分级分发风险评估实验 3.0
DECLARE_SPDM_ABTEST_BOOL(enable_adue_risklevel_evaluate);
DECLARE_SPDM_ABTEST_BOOL(enable_adue_risklevel_eval_filter_skip);
// [liaibao] 规则引擎过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ue_ruleengine_filter);
DECLARE_SPDM_ABTEST_INT64(adue_ruleengine_filter_exp_group);
// 单账户过滤策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_ue_single_account_filter);
DECLARE_SPDM_ABTEST_INT64(single_account_filter_exp_group);
// [dengchijun] 高举报账户过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ue_risk_account_filter);
DECLARE_SPDM_ABTEST_INT64(flow_grading_report_filter_exp_group);
DECLARE_SPDM_ABTEST_BOOL(enable_ue_sensitive_user_filter);
// 分级分发举报敏感用户过滤
DECLARE_SPDM_ABTEST_BOOL(enable_ue_grading_report_user_filter);
// 举报敏感用户 redis
DECLARE_SPDM_ABTEST_STRING(user_report_prob_prefix);
DECLARE_SPDM_ABTEST_INT64(universe_recall_inner_supported_group_tag);
// [dengchijun] 低质过滤豁免
DECLARE_SPDM_ABTEST_INT64(ue_limit_free_tag);
DECLARE_SPDM_ABTEST_INT64(universe_retarget_retrieval_author_num);  //  [gaowei03]  联盟重定向选取的作者数
DECLARE_SPDM_ABTEST_INT64(universe_retarget_retrieval_creative_num);   //  [gaowei03]  联盟重定向选取的创意数

// ---------------------- abtest double 参数声明 -------------------

//  [gaowei03]  联盟内循环 nobid cpa 折扣
DECLARE_SPDM_ABTEST_DOUBLE(univ_multi_retr_sn_adjust_ratio);  // [chendongdong] 联盟多路召回 sn 调整系数
DECLARE_SPDM_ABTEST_DOUBLE(univ_main_layer_recall_quota_ratio);  //  [chendongdong] 联盟主模型通路 Quota
DECLARE_SPDM_ABTEST_DOUBLE(univ_inner_deep_layer_recall_quota_ratio);  // [chendongdong] 联盟内循环深度召回 Quota // NOLINT

// ---------------------- abtest string 参数声明 -------------------

DECLARE_SPDM_ABTEST_STRING(useless_path_ab_tag);  // [heqian] 无用通路清理实验 tag
DECLARE_SPDM_ABTEST_STRING(skip_media_ssp_sync_creative_blacklist);  // [shanminghui] 跳过 ssp 同步的创意黑名单过滤的 uid 名单 // NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_tiny_search_recall_diverse_by_index_abtag);  // [libaolin03] 厂商搜索多样性 kconf 配置 基于位次信息 // NOLINT
// ---------------------- kconf int64 参数声明 -------------------

DECLARE_SPDM_ABTEST_BOOL(enable_universe_live_no_bid);  // [gaowei03] 联盟直播 nobid 开关

// [qianyangchao] 联盟小系统跳过已转化过滤
DECLARE_SPDM_ABTEST_BOOL(enable_universe_tiny_skip_converted_filter);

// [liuzichen05] ps 实验名称
DECLARE_SPDM_ABTEST_STRING(target_ps_adjust_exp_tag);
// [rongyu03] 游戏行业重定向开关
DECLARE_SPDM_ABTEST_BOOL(enable_game_olp_retarget_pv);
// [rongyu03] 游戏行业重定向新增通路开关
DECLARE_SPDM_ABTEST_BOOL(enable_game_olp_retarget_new_path);
// [lizemin] ps 请求清理 reco user info 细粒度裁剪
DECLARE_SPDM_ABTEST_BOOL(enable_reco_user_info_trim_for_ps);

DECLARE_SPDM_ABTEST_BOOL(enable_universe_request_ordered_data);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ep_follow_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_api_consultation);  // [shanminghui] api 流量上放开私信预算
DECLARE_SPDM_KCONF_BOOL(enableUniverseTransparentQcpxTarget);  // [tanghaihong] target 透传 qcpx 相关字段开关

DECLARE_SPDM_ABTEST_BOOL(enable_quick_search_skip_budget_control_universe);  // [tanghaihong] 联盟小系统快投广告跳过暗投控比 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_direct_search_skip_budget_control_universe);  // [tanghaihong] 联盟小系统直投广告跳过暗投控比 // NOLINT
DECLARE_SPDM_KCONF_INT32(universeTraceTsmRatio);  //  [tanghaihong] 联盟 trace Tsm 放量开关, 防止下发数据过多

DECLARE_SPDM_KCONF_BOOL(enableEsQuotaOpt);  // [tanghaihong] ensemble sort quota 逻辑优化

DECLARE_SPDM_KCONF_BOOL(enableSearchDarkFix);  // [tanghaihong] 搜索暗投联盟大系统 fix
DECLARE_SPDM_KCONF_BOOL(enableUniverseFillPostEcpmFix);  // [tanghaihong] FillPostEcpm 偶发 core fix

DECLARE_SPDM_KCONF_BOOL(skipWxShortAppAdFilterHarmonyFilter);  // [tanghaihong] UniverseSiteSmallGameFilter 和 UniverseWXSmallAppFilter 对鸿蒙过滤 // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseHarmonySiteAssemblyFilter);  // [tanghaihong] 鸿蒙系统组件过滤

DECLARE_SPDM_ABTEST_INT64(effect_layer_max_traverse_num_universe);  // [tanghaihong] 效果类通路计算 ensemble score 每一路的最大遍历数 // NOLINT
DECLARE_SPDM_ABTEST_STRING(effect_layer_truncate_quota_by_elastic_index);  // [tanghaihong] 效果类通路截断后 quota by 弹性队列分组 // NOLINT

DECLARE_SPDM_KCONF_BOOL(enableUniverseOpLatencyDotV2);  // [tanghaihong] 联盟 target 算子耗时打点支持弹性队列分组 // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseSearchQueryBidwordInfo);  // [tanghaihong] 联盟支持搜索 mock query 前置
DECLARE_SPDM_KCONF_BOOL(enableUniverseTempUserXBudgetFilter);  // [gongxiaopeng03] 联盟 target temp 用户超成本预算过滤 // NOLINT
DECLARE_SPDM_ABTEST_STRING(enable_universe_fre_time_filter);  // [zhognziwei] 频控分时过滤分流量控制不同频控
DECLARE_SPDM_ABTEST_BOOL(universe_dark_budget_free_quick_search);  // [zhongziwei] 快投非厂商白名单优化目标 绕过暗投控比开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_outer_x_account_blacklist);  // [zhaijianwei] 外循环 X 账户黑名单过滤 //NOLINT

DECLARE_SPDM_KCONF_BOOL(enableUniverseWechatIdConvFilter);  // [tanghaihong] 联盟支持企微号已转化过滤

DECLARE_SPDM_KCONF_BOOL(enableUniverseAdTypeFix);  // [tanghaihong] 联盟修复 ad_type 倒排的兜底过滤

DECLARE_SPDM_KCONF_BOOL(enableUniverseKwaiBookFilter);  // [tanghaihong] 快小说预算过滤开关

DECLARE_SPDM_ABTEST_BOOL(enable_universe_dcaf_prerank);  // [xiaowentao] 联盟粗排 DCAF
DECLARE_SPDM_ABTEST_BOOL(skip_universe_dcaf_prerank);  // [xiaowentao] 跳过联盟粗排 DCAF
DECLARE_SPDM_ABTEST_BOOL(enable_dcaf_timeout_log);  // [xiaowentao] 记录 DCAF 超时降档的数据
// [xiaowentao] extra_time 是指 ad front, ad style, rpc 通信等固有耗时
DECLARE_SPDM_KCONF_BOOL(enableUniversePacingControlFixHotkey);  // [xuxuejian] 联盟实时流控修复热 key 开关
DECLARE_SPDM_KCONF_INT32(universeSendCostCopyNum);  // [xuxuejian] 联盟实时消耗数据份数缓解热 key
DECLARE_SPDM_KCONF_BOOL(enableUniversePacingControlFixCompact);  // [xuxuejian] 联盟实时流控修复 compact 开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_elastic_ab_conf);
DECLARE_SPDM_ABTEST_STRING(universe_elastic_ab_conf);
DECLARE_SPDM_KCONF_BOOL(enableRetLLMU2pProducts);  // [jiangyifan] llm u2p 查询结果返回到 adserver
}  // namespace ad_target
}  // namespace ks
