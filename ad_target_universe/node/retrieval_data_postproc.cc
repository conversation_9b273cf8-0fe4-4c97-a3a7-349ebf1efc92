#include "teams/ad/ad_target_universe/node/retrieval_data_postproc.h"

#include <sys/types.h>

#include <algorithm>
#include <cstddef>
#include <cstdint>
#include <memory>
#include <sstream>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>

#include "absl/strings/str_cat.h"
#include "glog/logging.h"
#include "glog/stl_logging.h"
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_index/index/utils/ad_index_type_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_target_universe/client/kafka/trace_manager.h"
#include "teams/ad/ad_target_universe/common/ad_table_migrage.h"
#include "teams/ad/ad_target_universe/common/prerank_ad_list.h"
#include "teams/ad/ad_target_universe/utils/common/ad_utils.h"
#include "teams/ad/ad_target_universe/utils/kconf/kconf.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/ad_target_universe/utils/spdm/spdm_switches.h"
#include "teams/ad/engine_base/auto_bid/bid_trans_utils.h"
#include "teams/ad/engine_base/fanstop_common/pv_debug/trace_info_io.h"
#include "teams/ad/prefix_engine/service/app_name_prefix_engine.h"
#include "teams/ad/ad_target_universe/utils/table_extension/table_wrapper.h"
#include "teams/ad/ad_target_universe/utils/common/ad_table_util.h"
#ifdef TARGET_UNIVERSE_SCHEMA_CUT
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/WTAccount.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/WTAuthor.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/WTPhoto.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/WTProduct.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/AdDspTargetMedia.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/AdAppRelease.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/AdMagicSitePageDas.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Agent.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Material.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/AgentAccount.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/TraceUtil.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Campaign.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Account.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Creative.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/IndustryV3.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/LiveStreamUserInfo.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Target.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Unit.h"
#else
#include "teams/ad/ad_table/code_generator/ad_target_universe/WTAccount.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/WTAuthor.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/WTPhoto.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/WTProduct.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/AdDspTargetMedia.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/AdAppRelease.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/AdMagicSitePageDas.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Agent.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Material.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/AgentAccount.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/TraceUtil.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Campaign.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Account.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Creative.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/IndustryV3.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/LiveStreamUserInfo.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Target.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Unit.h"
#endif

DECLARE_bool(target_no_diff_switch);
DEFINE_INF_KCONF_int32("ad.adtarget.projectBonusNumLimit",
                       projectBonusNumLimit,
                       10,
                       "project bonus num limit");
DEFINE_INF_KCONF_bool("ad.adtarget.enableBonusSupportInfo",
                      enableBonusSupportInfo,
                      false,
                      "enableBonusSupportInfo");

using kuaishou::ad::AdEnum;
using kuaishou::ad::AdTargetResponse;
using kuaishou::fanstop::FansTopEnum;
using kuaishou::log::ad::AdTraceFilterCondition;
using kuaishou::log::ad::AdTraceFilterCondition_Name;

namespace ks {
namespace ad_target {
using ::operator<<;

bool RetrievalDataPostproc::Prepare() {
  session_data_ = context_->GetMutableContextData<ContextData>();
  inner_data_ = &(session_data_->targeting_data);
  break_map_.clear();
  check_target_ = AdKconfUtil::checkTargetInfo();
  enable_esp_mobile_fill_fix =
      SPDM_enable_esp_mobile_fill_fix(session_data_->spdm_ctx);
  response_account_set_.clear();
  enable_target2rank_second_industry_5 = SPDM_enable_target2rank_second_industry_5(session_data_->spdm_ctx);
  auto dragon_context = MixContext();
  target_server_skip_prerank_ = dragon_context->GetIntCommonAttr("target_server_skip_prerank").value_or(0);
  return true;
}

void RetrievalDataPostproc::FillTargetData(kuaishou::ad::AdTargetResponse *response) const {
  // set multi_retrieval_info
  for (const auto &kv : session_data_->multi_path_context.tag_2_path_context) {
    auto multi_retrieval_info = response->mutable_request_tag()->add_multi_retrieval_info();
    multi_retrieval_info->set_tag(kv.first);
    multi_retrieval_info->set_index_name(kv.second->GetPathConfig().cmd());
  }
  // auto_param
  auto *auto_param_info = response->mutable_request_tag()->mutable_auto_param_info();
  auto_param_info->add_exp_id(session_data_->multi_param_manager.auto_param_expid);
  auto_param_info->add_exp_id(session_data_->multi_param_manager.diversity_auto_expid);
  // counter
  auto counter = response->mutable_counter();
  counter->set_retrieval_target_unit_num(session_data_->target_counter.target_cnt);
  counter->set_retrieval_selected_unit_num(session_data_->target_counter.expand);
  counter->set_retrieval_candidate_size(session_data_->target_counter.rule);
  counter->set_server_host(session_data_->hostname);
  response->set_work_flow_type(session_data_->work_flow_type);
  response->set_dynamic_retrieval_queue_size(session_data_->dynamic_retrieval_queue_size);
  response->set_group_tag(session_data_->group_tag);
  response->set_deep_group_tag(session_data_->deep_group_tag);
  response->set_universe_ad_selected_budget_type(session_data_->universe_ad_selected_budget_type);
  response->set_last_filter_condition(session_data_->trace_log->SimplifyTrace().GetLastFilterCondition());

  auto fill_retrieval_cnt =
      [&](const absl::flat_hash_map<int32, int32> &cnt,
          google::protobuf::RepeatedPtrField<kuaishou::ad::AdTargetResponse_RetrievalCount> *rc) {
        for (auto &&kv : cnt) {
          auto node = rc->Add();
          node->set_tag(kv.first);
          node->set_count(kv.second);
        }
      };

  fill_retrieval_cnt(inner_data_->target_retrieval_cnt, counter->mutable_targeting());
  fill_retrieval_cnt(inner_data_->ranking_retrieval_cnt, counter->mutable_ranking());
  // set prerank pv data
  if (AdKconfUtil::enableUniverseAutoParamTrans()) {
    // 联盟没有 preranking info
    auto *prerank_pv_info = response->mutable_request_tag()->mutable_prerank_pv_info();
    prerank_pv_info->mutable_auto_param_info()->CopyFrom(session_data_->universe_auto_param_info);
  }
  auto *prerank_pv_info = response->mutable_request_tag()->mutable_prerank_pv_info();
  prerank_pv_info->set_prerank_ps_quota(session_data_->prerank_ps_accepted_item_size);
  std::string product_freq_data;
  auto& product_response = const_cast<ks::infra::RedisResponse<std::string>&>(
      session_data_->redis_result.impr_freq_filter_product);
  product_response.Get(&product_freq_data);
  response->mutable_request_tag()->set_universe_product_freq_data(product_freq_data);
  response->mutable_request_tag()->set_kwai_did_tag(session_data_->rta_ad_data.GetKwaiDidTag());
  response->mutable_request_tag()->set_nebula_did_tag(session_data_->rta_ad_data.GetNebulaDidTag());
  // carm
  if (universe_base::prerank::SPDM_enable_univ_prerank_context_id_list_feature(session_data_->spdm_ctx)) {
    auto* carm_info_list = response->mutable_outer_request_feature_message();
    if (carm_info_list) {
      for (int i = 0; i < session_data_->carm_prerank_info.author_id_list.size(); i++) {
        carm_info_list->add_outer_hard_candidate_author_ids(
                        session_data_->carm_prerank_info.author_id_list[i]);
      }
      for (int i = 0; i < session_data_->carm_prerank_info.account_id_list.size(); i++) {
        carm_info_list->add_outer_hard_candidate_account_ids(
                      session_data_->carm_prerank_info.account_id_list[i]);
      }
      for (int i = 0; i < session_data_->carm_prerank_info.photo_id_list.size(); i++) {
        carm_info_list->add_outer_hard_candidate_photo_ids(
                      session_data_->carm_prerank_info.photo_id_list[i]);
      }
      for (int i = 0; i < session_data_->carm_prerank_info.industry_id_v3_list.size(); i++) {
        carm_info_list->add_outer_hard_candidate_industry_id_v3_ids(
                      session_data_->carm_prerank_info.industry_id_v3_list[i]);
      }
      for (int i = 0; i < session_data_->carm_prerank_info.city_product_id_list.size(); i++) {
        carm_info_list->add_outer_hard_candidate_city_product_id_ids(
                      session_data_->carm_prerank_info.city_product_id_list[i]);
      }
      for (int i = 0; i < session_data_->carm_prerank_info.ocpx_action_type_list.size(); i++) {
        carm_info_list->add_outer_hard_candidate_ocpx_action_type_ids(
                      session_data_->carm_prerank_info.ocpx_action_type_list[i]);
      }
    }
  }
  // 联盟厂商 sug 结果落 server_show
  // 灰度的时候需要落一些日志，检查两者是否一致
  for (const auto &item : session_data_->universe_tiny_query_recall_data.GetAppNamePrefixItems()) {
    auto app_name_prefix_item = response->add_app_name_prefix_item_list();
    app_name_prefix_item->set_app_name(item.app_name);
    app_name_prefix_item->set_package_name(item.package_name);
    for (const auto &category : item.categorys) {
      app_name_prefix_item->add_categorys(category);
    }
    for (const auto &match_type : item.match_types) {
      app_name_prefix_item->add_match_types(match_type);
    }
    app_name_prefix_item->set_score(item.score);
  }
}
void RetrievalDataPostproc::Report(const kuaishou::ad::AdTargetResponse &ad_response) const {
  // [tanghaihong] 这里保留 subtag = live_exp，以免影响现有监控
  session_data_->dot->Interval(ad_response.ad_list_size(), "live_exp", "target_response_size");

  kuaishou::ad::AdEnum_AdRequestFlowType ad_request_type = session_data_->pos_manager_base.GetAdRequestType();
  session_data_->dot->Count(1, "retrieval_request_count");

  auto byte_size_long = ad_response.ByteSizeLong();
  session_data_->dot->Interval(byte_size_long, "response_size");

  int total_cnt = 0;
  for (auto &&stat : break_map_) {
    total_cnt += stat.second;
    session_data_->dot->Interval(stat.second, "target_break", "tag_id", std::to_string(stat.first));
  }
  if (total_cnt) {
    session_data_->dot->Interval(total_cnt, "target_break", "total_cnt");
  }
}

bool RetrievalDataPostproc::ProcessInner() {
  Prepare();
  auto response = context_->GetRPCResponse<AdTargetResponse>();

  FillAdTargetResponse(response);
  FillTargetData(response);
  FillDebugInfo(response);
  if (!target_server_skip_prerank_) {
    FillUniversePrerankParams(response);
  }
  FillNewCreativeResponse(response);
  FillUnionLtrUsedItem(response);
  FillE2eUsedItem(response);
  Report(*response);

  session_data_->is_target_time_out = false;  // 本次 pv 未超时
  session_data_->dot->Interval(base::GetTimestamp() - session_data_->start_ts,
      "live_exp.total.latency");
  return true;
}

void RetrievalDataPostproc::FillUniversePrerankParams(kuaishou::ad::AdTargetResponse *ad_response) {
  auto universe_prerank_info = ad_response->mutable_universe_prerank_info();
  universe_prerank_info->set_is_enable_prerank(
      session_data_->universe_prerank_prepare_params.is_enable_prerank);
  universe_prerank_info->set_max_prerank_num(session_data_->universe_prerank_prepare_params.max_prerank_num);
  if (ks::ad_server::AdIndexKconf::isUniverseTinyDeploy() == false
      && ks::universe_base::prerank::SPDM_enable_universe_dcaf_rank(session_data_->spdm_ctx)) {
    universe_prerank_info->set_max_prerank_num_before_dcaf(
      session_data_->universe_prerank_prepare_params.max_prerank_num_before_dcaf);
    if (SPDM_enable_dcaf_timeout_log(session_data_->spdm_ctx)) {
      universe_prerank_info->set_dcaf_pred_time_rank(
        session_data_->universe_prerank_prepare_params.dcaf_pred_time_rank);
    }
  }
  if (ks::ad_server::AdIndexKconf::isUniverseTinyDeploy() == false
      && SPDM_enable_universe_dcaf_prerank(session_data_->spdm_ctx)) {
    universe_prerank_info->set_max_retrieval_creatives_before_dcaf(
      session_data_->universe_prerank_prepare_params.max_retrieval_creatives_before_dcaf);
  }
}

void RetrievalDataPostproc::FillNewCreativeResponse(kuaishou::ad::AdTargetResponse *ad_response) {
  // universe prerank info
  int64_t max_retrieval_rank_info = 30;
  ad_base::AdRandomShuffle::PartialShuffle(session_data_->retrieval_new_creatives.begin(),
                                           session_data_->retrieval_new_creatives.end(),
                                           max_retrieval_rank_info);
  ad_base::AdRandomShuffle::PartialShuffle(session_data_->prerank_new_creatives.begin(),
                                           session_data_->prerank_new_creatives.end(),
                                           max_retrieval_rank_info);

  auto universe_prerank_info = ad_response->mutable_universe_prerank_info();
  if (ks::ad_server::AdIndexKconf::isUniverseTinyDeploy() == false
      && SPDM_enable_universe_dcaf_prerank(session_data_->spdm_ctx)) {
    universe_prerank_info->set_max_retrieval_creatives(
      session_data_->universe_prerank_prepare_params.max_retrieval_creatives);
    if (SPDM_enable_dcaf_timeout_log(session_data_->spdm_ctx)) {
      universe_prerank_info->set_dcaf_pred_time_prerank(
        session_data_->universe_prerank_prepare_params.dcaf_pred_time_prerank);
    }
  }
  // 召回
  int32_t i = 0;
  for (const auto &creative_id : session_data_->retrieval_new_creatives) {
    universe_prerank_info->add_retrieval_new_creative(creative_id);
    if (++i > max_retrieval_rank_info) {
      break;
    }
  }
  // 粗排
  i = 0;
  for (const auto &creative_id : session_data_->prerank_new_creatives) {
    universe_prerank_info->add_prerank_new_creative(creative_id);
    if (++i > max_retrieval_rank_info) {
      break;
    }
  }
  if (SPDM_enableRetLLMU2pProducts()) {
    for (auto llm_u2p_product : session_data_->llm_u2p_products) {
      universe_prerank_info->add_llm_u2p_products(llm_u2p_product);
    }
  }
}

void RetrievalDataPostproc::FillUnionLtrUsedItem(kuaishou::ad::AdTargetResponse *ad_response) {
  if (session_data_->union_ltr_used_item) {
    ad_response->mutable_used_item_message()->mutable_union_ltr_used_item()->CopyFrom(
        *session_data_->union_ltr_used_item);
  }
}

void RetrievalDataPostproc::FillE2eUsedItem(kuaishou::ad::AdTargetResponse *ad_response) {
  if (session_data_->e2e_used_item) {
    int negative_sample_size = session_data_->e2e_used_item->ad_rank_infos_size();
    LOG_EVERY_N(INFO, 1000) << "e2e_used_item negative sample size:" << negative_sample_size;
    ad_response->mutable_used_item_message()->mutable_e2e_used_item()->CopyFrom(
        *session_data_->e2e_used_item);
  }
}

void RetrievalDataPostproc::FillDebugInfo(kuaishou::ad::AdTargetResponse *ad_response) const {
  // creative 级别 debug 结果返回
  const auto &debug_creative_data = inner_data_->trace_stat.color;
  if (debug_creative_data.creative_id > 0) {
    auto creative_debug = ad_response->mutable_debug_str()->add_creative_debug();

    if (auto it = inner_data_->ad_map.find(debug_creative_data.creative_id);
        it != inner_data_->ad_map.end() && it->second) {
      debug_creative_data.reason = it->second->strategy_base.filter_cond;
    }

    creative_debug->set_creative_id(debug_creative_data.creative_id);
    creative_debug->set_reason(debug_creative_data.reason);
  }
}

bool RetrievalDataPostproc::IsLiveCampaign(const RetrievalAdCommon *ad) const {
  return ad->IsEspLivePromotion() ||
         (session_data_->enable_fanstop_live_in_hard_queue && ad->is_fanstop_hard_queue_live_campaign());
}

bool RetrievalDataPostproc::FillAdTargetResponse(kuaishou::ad::AdTargetResponse *response) {
  const bool is_festival_req =
      (session_data_->ad_request->ecom_festival_req() ==
           kuaishou::ad::AdEnum_EcomFestivalReq_ECOM_GREEN_REQ ||
       session_data_->ad_request->ecom_festival_req() == kuaishou::ad::AdEnum_EcomFestivalReq_ECOM_ONLY_REQ);
  int64_t new_creative_retrieval_num = 0;  // 触发召回阶段新创意召回量
  int ocpm_ad_count = 0;
  int ocpc_ad_count = 0;
  int cpa_ad_count = 0;
  int jk_ad_count = 0;
  int ali_outer_count = 0;
  int merchant_live_roas_count = 0;

  auto fill_creative_extra = [&](const ks::Creative &creative,
                                 kuaishou::ad::AdTargetResponse_Ad &ad) {
    auto creative_extra = ad.mutable_creative_extra();
    creative_extra->set_photo_id(creative.photo_id());
    creative_extra->set_pic_id(creative.pic_id());
    creative_extra->set_dup_photo_id(creative.dup_photo_id());
    creative_extra->set_dup_cover_id(creative.dup_cover_id());
    creative_extra->set_cover_id(creative.cover_id());
    creative_extra->set_account_id(creative.account_id());
    this->response_account_set_.emplace(creative.account_id());
    creative_extra->set_create_time(CreativeGetCreateTime(&creative, CreativeTimeTag::kAuditTime));
    creative_extra->set_live_creative_type(creative.live_creative_type());
    creative_extra->set_creative_material_type(creative.creative_material_type());
    creative_extra->set_circulation_type(
      kuaishou::ad::tables::CreativeExtendScore_CreativeCirculationType(creative.circulation_type()));
    // 返回优质素材起量策略相关字段
    if (creative.has_creative_support_info()) {
      ad.mutable_photo_model_scores()->set_pcvr(creative.pcvr());
      ad.mutable_photo_model_scores()->set_content_predict_value(creative.content_predict_value());
      ad.mutable_photo_model_scores()->set_ocpx_action_type(creative.ocpx_action_type());
      ad.mutable_photo_model_scores()->set_pcvr_rank(creative.pcvr_rank());
      ad.mutable_photo_model_scores()->set_content_rating(creative.content_rating());
    }
    creative_extra->set_duration(creative.duration());
    // 填充素材宽高, 复用一下 pic_width 和 pic_height
    creative_extra->set_pic_width(creative.cover_width());
    creative_extra->set_pic_height(creative.cover_height());
    if (creative.cover_width() == 0 || creative.cover_height() == 0) {
      const auto* material = TableWrapper::GetMaterial(creative.item_material1());
      if (material) {
        creative_extra->set_pic_width(material->cover_width());
        creative_extra->set_pic_height(material->cover_height());
      }
    }
  };
  bool enable_live_agent = AdKconfUtil::enableLiveAgent();
  auto ecom_festival_account_whitelist = AdKconfUtil::ecomFestivalAccountList();

  auto fill_ad_common = [&](const RetrievalAdCommon* ad, kuaishou::ad::AdTargetResponse_Ad* node) {
    node->set_creative_id(ad->creative_id());
    if (target_server_skip_prerank_) {
      node->set_scene_oriented_type(ad->scene_oriented_type());
      node->set_budget_smart_allocation(ad->budget_smart_allocation());
      node->set_fans_count(ad->fans_count());
      node->set_is_inner_delivery(ad->is_inner_delivery());
      if (ad->base.p_account) {
        node->set_second_industry_id_v6(ad->base.p_account->second_industry_id_v6());
      }
    }
    if (ad->material_id_v2()) {
      node->set_material_id(ad->material_id_v2());
    }
    if (ad->base.p_unit->has_unit_support_info_optional()) {
      node->set_bid_strategy(ad->base.p_unit->bid_strategy());
      node->set_mcb_value_type(ad->base.p_unit->mcb_value_type());
    }
    node->set_target_id(ad->base.p_unit->target_id());
    const int64_t site_id = ad->base.p_unit->site_id();
    const AdMagicSitePageDas *p_ad_magicsite_page_das =
                      TableWrapper::GetAdMagicSitePageDas(site_id);
    if (p_ad_magicsite_page_das != nullptr) {
      auto *base_info = node->mutable_ad_target_to_rank()->mutable_ad_base();
      base_info->set_landing_page_component(p_ad_magicsite_page_das->landing_page_component());
      node->set_direct_call_type(p_ad_magicsite_page_das->direct_call_type());
    }
    auto *explore_fea = node->mutable_ad_target_to_rank()->mutable_explore_feature();
    if (AdKconfUtil::enableUniverseFlowExplore()) {
      // 探索策略 v1 特征
      std::string explore_fea_v1 = "";
      auto ret_v1 = session_data_->redis_result.universe_explore_fea_data_v1.Get(&explore_fea_v1);
      if (ret_v1 == ks::infra::KS_INF_REDIS_NO_ERROR && !explore_fea_v1.empty()) {
        std::vector<std::string> fea_arr = absl::StrSplit(explore_fea_v1, ",");
        if (fea_arr.size() >= 4) {
          float req_num = std::stof(fea_arr[0]);
          float union_avg_ecpm = std::stof(fea_arr[1]);
          float outer_win_ecpm = std::stof(fea_arr[2]);
          float win_rate = std::stof(fea_arr[3]);
          explore_fea->set_req_num(req_num);
          explore_fea->set_union_avg_ecpm(union_avg_ecpm);
          explore_fea->set_outer_win_ecpm(outer_win_ecpm);
          explore_fea->set_win_rate(win_rate);
        }
      }
      // 探索策略 v2 特征
      std::string explore_fea_v2 = "";
      auto ret_v2 = session_data_->redis_result.universe_explore_fea_data_v2.Get(&explore_fea_v2);
      if (ret_v2 == ks::infra::KS_INF_REDIS_NO_ERROR && !explore_fea_v2.empty()) {
        std::vector<std::string> fea_arr_v2 = absl::StrSplit(explore_fea_v2, "_");
        if (fea_arr_v2.size() >= 2) {
          float union_ten_percent_ecpm = std::stof(fea_arr_v2[0]);
          float potential_cost = std::stof(fea_arr_v2[1]);
          explore_fea->set_union_ten_percent_ecpm(union_ten_percent_ecpm);
          explore_fea->set_potential_cost(potential_cost);
        }
      }
    }
    auto *base_info = node->mutable_ad_target_to_rank()->mutable_ad_base();
    if (ad->base.p_ad_app_release) {
      base_info->set_app_package_name_id(ad->base.p_ad_app_release->package_name_id());
    }
    if (SPDM_enable_universe_ks_search_ecom_data(session_data_->spdm_ctx)) {
      std::string ks_search_ecom_crowd = "";
      auto ret = session_data_->redis_result.universe_ks_search_ecom_data.Get(&ks_search_ecom_crowd);
      if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !ks_search_ecom_crowd.empty()) {
        base_info->set_ks_search_ecom_crowd(ks_search_ecom_crowd);
      }
    }
    if (SPDM_enableUniverseTransparentQcpxTarget()) {
      const WTProduct* p_wt_product = nullptr;
      const WTAuthor* p_wt_author = nullptr;
      if (session_data_->ad_table_forward_opt) {
        p_wt_product = ad->base.p_unit->item_id_ref_WTProduct();
      } else {
        p_wt_product = TableWrapper::GetWTProduct(ad->base.p_unit->item_id());
      }
      const auto author_id = ad->base.p_unit->has_unit_support_info_optional()
                         ? ad->base.p_unit->live_user_id()
                         : ad->base.p_account->user_id();
      if (session_data_->wt_author_df) {
        p_wt_author = session_data_->wt_author_df->Find(author_id).GetStruct<WTAuthor>();
      } else {
        p_wt_author = TableWrapper::GetWTAuthor(author_id);
      }
      if (p_wt_author != nullptr) {
        base_info->set_shop_coupon_package_id(p_wt_author->shop_coupon_package_id());
      }
      if (p_wt_product != nullptr) {
        base_info->set_item_coupon_package_id(p_wt_product->item_coupon_package_id());
      }
    }
    if (enable_target2rank_second_industry_5 && ad->base.p_account != nullptr) {
      base_info->set_second_industry_id_v5(ad->base.p_account->second_industry_id_v5());
    }
    if (ad->base.p_wt_account != nullptr) {
      base_info->set_crm_center(ad->base.p_wt_account->crm_center());
    }
    base_info->set_mmu_posterior_score(ad->others.mmu_posterior_score);
    base_info->set_bad_photo_tax_coef(ad->others.bad_photo_tax_coef);
    base_info->set_package_name_pos(ad->others.package_name_pos);
    if (ad->base.p_account) {
      base_info->set_first_industry_id_v5(ad->base.p_account->first_industry_id_v5());
    }

    node->mutable_creative_extra()->set_unit_id(ad->unit_id());

    if (ad->base.p_creative) {
      fill_creative_extra(*(ad->base.p_creative), *node);
    }

    using BonusSupportCacheLoaderValue =
        std::unordered_map<int64_t, std::vector<std::pair<int64_t, int64_t>>>;
    auto set_innerloop_bonus_support_info = [&](
        const BonusSupportCacheLoaderValue& data_id, int64_t id) {
      auto iter = data_id.find(id);
      if (iter != data_id.end()) {
        for (auto&& [project_id, bonus_tag] : iter->second) {
          auto *innerloop_bonus_info = node->add_innerloop_bonus_info();
          innerloop_bonus_info->set_project_id(project_id);
          innerloop_bonus_info->set_bonus_tag(bonus_tag);
        }
      }
    };

    // 设置网赚标签
    node->set_wangzhuan_tag(false);

    // nobid 广告填充 cpa_bid
    if (ad->base.p_unit->speed() == kuaishou::ad::AdEnum::SPEED_NO_BID) {
      node->mutable_bid()->set_cpa_bid(ad->bid_info.cpa_bid);
      node->mutable_bid()->set_roi_ratio(ad->bid_info.roi_ratio);
    }

    // 临时解决 ad server 索引丢失 account_support_info 数据问题
    // 完全在 target 服务做电商直播数据过滤与信息填充回传 ad server
    if (enable_live_agent) {
      if (ad->base.p_live_stream_user_info && ad->base.p_live_stream_user_info->is_live()) {
        node->set_live_stream_id(ad->base.p_live_stream_user_info->live_stream_id());
        node->set_user_id(ad->base.p_live_stream_user_info->user_id());
      }
    } else if (ad->base.p_live_stream_user_info && ad->base.p_live_stream_user_info->is_live()) {
      node->set_live_stream_id(ad->base.p_live_stream_user_info->live_stream_id());
      node->set_live_stream_id_encrypted(ad->base.p_live_stream_user_info->live_stream_id_encrypted());
      node->set_user_id(ad->base.p_live_stream_user_info->user_id());
    }
    if ((!node->has_user_id() || node->user_id() <= 0) && ad->base.p_unit->has_unit_support_info_optional()) {
      node->set_user_id(ad->base.p_unit->live_user_id());
    }
    // 联盟内循环填开播时间和商品 id
    if (ad->base.p_live_stream_user_info && ad->base.p_live_stream_user_info->is_live()) {
      node->set_live_event_time(ad->base.p_live_stream_user_info->event_time());
    }
    if (ad->base.p_unit->has_merchant_small_shop_support_info_optional()) {
      node->set_universe_merchant_item_id(ad->base.p_unit->item_id());
    }

    node->set_is_expansion(static_cast<kuaishou::ad::IsExpansion>(ad->others.expansion_type));

    if (fabs(ad->others.pred_score) > FLT_EPSILON) {
      node->set_pred_score(ad->others.pred_score);
    }
    if (ad->others.sort_type > 0) {
      node->set_recall_sort_type(static_cast<kuaishou::ad::AdEnum_OneStepSortType>(ad->others.sort_type));
    }
    node->set_photo_source(ad->others.photo_source);
    node->set_photo_quality_score(ad->others.photo_quality_score);

    if (ad->strategy_base.ad_strategy_tag != 0) {
      node->set_ad_strategy_tag(ad->strategy_base.ad_strategy_tag);
    }
    if (ad->strategy_base.ad_policy_tag != kuaishou::ad::AdTargetResponse::UNKNOWN_POLICY_TYPE) {
      node->set_ad_policy_type(
          static_cast<::kuaishou::ad::AdTargetResponse_PolicyType>(ad->strategy_base.ad_policy_tag));
    }
    if (ad->strategy_base.ad_ecology_tag != kuaishou::ad::AdTargetResponse::UNKNOWN_ECOLOGY_TYPE) {
      node->set_ad_ecology_type(
          static_cast<kuaishou::ad::AdTargetResponse_EcologyType>(ad->strategy_base.ad_ecology_tag));
    }
    node->set_ad_multi_retr_type(
        static_cast<kuaishou::ad::AdTargetResponse_MultiRetrType>(ad->strategy_base.ad_multi_retr_type));

    // 设置内粉北极星订单字段
    if (ad->base.p_campaign->has_fanstop_support()) {
      node->set_polaris_delivery(ad->base.p_campaign->is_archimedis_polaris_delivery());
    }

    // 设置内粉召回 reason 字段
    node->set_inner_fanstop_reason(ad->others.reason);

    // 设置召回源头字段
    if (ad->strategy_base.retrieval_tag_new != 0) {
      node->set_retrieval_tag_new(ad->strategy_base.retrieval_tag_new);
    }

    if (ad->strategy_base.multi_retrieval_tag != 0) {
      node->set_multi_retrieval_tag(ad->strategy_base.multi_retrieval_tag);
    }

    if (ad->strategy_base.multi_overlay_tag > 0) {
      node->mutable_algo_strategy()->set_multi_overlay_tag(ad->strategy_base.multi_overlay_tag);
    }

    if (ad->strategy_base.multi_overlay_tag_extend > 0) {
      node->mutable_algo_strategy()->set_multi_overlay_tag_extend(ad->strategy_base.multi_overlay_tag_extend);
    }

    node->set_retrieval_rank_score(ad->strategy_base.retrieval_rank_score);
    node->set_retrieval_post_score(0);
    node->set_account_mark(kuaishou::ad::AdEnum::AccountMark(ad->base.p_account->account_mark()));
    node->set_is_ai_hosting_ad(ad->is_ai_hosting_ads());
    node->set_is_playable(ad->is_playable_v2());

    if (ad->strategy_base.new_creative_tag != 0) {
      node->set_new_creative_tag(ad->strategy_base.new_creative_tag);
    }
    // 存储广告是否要强制召回
    if (ad->strategy_base.is_retarget_ad) {
      node->set_is_retarget_ad(true);
    }
    node->set_retarget_tool_tag(ad->strategy_base.retarget_tag);

    // rta 信息透传
    if (ad->others.real_time_type == kuaishou::ad::AdEnum::RTA) {
      node->mutable_rta_info()->set_real_time_type(kuaishou::ad::AdEnum::RTA);
      node->mutable_rta_info()->set_rta_source_type(ad->others.rta_source_type);
      node->mutable_rta_info()->set_trace_req_id(ad->others.rta_trace_request_id);
      node->mutable_rta_info()->set_feature_id(ad->others.rta_feature_id);
      session_data_->rta_ad_data.GetAccountCompressKey(ad->account_id(),
                                                       node->mutable_rta_info()->mutable_compress_key());
      // rta strategy_id transpass
      uint64 strategy_id = 0;
      double quality_score = 0.0;
      auto rta_account_info = session_data_->rta_ad_data.GetAccountInfo(ad->account_id());
      if (rta_account_info != nullptr) {
        strategy_id = rta_account_info->strategy_id;
        quality_score = rta_account_info->quality_score;
      }
      node->mutable_ad_target_to_rank()->mutable_ad_base()->set_real_time_type(kuaishou::ad::AdEnum::RTA);
      node->mutable_ad_target_to_rank()->mutable_ad_base()->set_rta_source_type(ad->others.rta_source_type);
      node->mutable_ad_target_to_rank()->mutable_ad_base()->set_strategy_id(strategy_id);
      node->mutable_ad_target_to_rank()->mutable_ad_base()->set_rta_quality_score(quality_score);
      // rta feature_id transpass
      node->mutable_ad_target_to_rank()->mutable_ad_base()->set_rta_feature_id(ad->others.rta_feature_id);
      // rta trace_req_id transpass
      node->mutable_ad_target_to_rank()->mutable_ad_base()
        ->set_rta_trace_req_id(ad->others.rta_trace_request_id);
    }
    if (ad->base.p_campaign->internal_invest_plan_id() > 0) {
      node->mutable_ad_target_to_rank()->mutable_ad_base()
        ->set_internal_invest_plan_id(ad->base.p_campaign->internal_invest_plan_id());
    }
    // 品牌序列化投放标识
    node->set_is_brand_boost_cljn(ad->others.is_brand_boost_cljn);

    ++inner_data_->ranking_retrieval_cnt[ad->strategy_base.multi_retrieval_tag];
    new_creative_retrieval_num += ad->is_new_creative();

    if (ad->bid_type() == kuaishou::ad::AdEnum::OCPC) {
      ocpc_ad_count++;
    } else if (ad->bid_type() == kuaishou::ad::AdEnum::OCPM_DSP) {
      ocpm_ad_count++;
    } else if (ad->bid_type() == kuaishou::ad::AdEnum::CPA) {
      cpa_ad_count++;
    }

    jk_ad_count += (ad->base.p_unit->unit_type() == kuaishou::ad::AdEnum_UnitType_JK_UNIT);
    ali_outer_count +=
        (ad->base.p_campaign->sub_type() == kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE);

    if (IsLiveCampaign(ad) && ad->bid_info.ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS) {
      merchant_live_roas_count += 1;
    }

    session_data_->multi_retr_prerank_num += (ad->strategy_base.multi_retrieval_tag > 0);

    FillMerchantData(ad, node);

    FillInnerLoopAdQueueType(*ad, node);
    FillAdTargetTransInfo(ad, node);

    // [tanghaihong] 代码治理 dcaf_prerank_queue_type 在 diff 卡点中，后续看是否删掉卡点
    node->set_dcaf_prerank_queue_type(kuaishou::ad::AdEnum::UNKNOWN_PRERANK_QUEUE_TYPE);
    auto* p_unit = ad->base.p_unit;
    if (p_unit != nullptr) {
      node->set_campaign_id(p_unit->campaign_id());
      node->set_resource_id(p_unit->resource_id());
      node->set_unit_source_type(p_unit->unit_source_type());
      node->set_ad_app_id(p_unit->app_id());
      node->set_study_status(p_unit->study_status());
      node->set_web_uri_type(static_cast<kuaishou::ad::AdEnum_UnitWebUriType>(p_unit->web_uri_type()));
      // TODO(wuyonghong): check trace_util
      if (p_unit->convert_id()> 0) {
        auto p_trace_util = TableWrapper::GetTraceUtil(p_unit->convert_id());
        if (p_trace_util != nullptr && p_trace_util->id() > 0) {
          node->set_convert_type(static_cast<kuaishou::ad::AdCallbackLog_ConvertType>(p_trace_util->type()));
        }
      } else if (p_unit->convert_id() == 0) {
        if (static_cast<int32>(p_unit->taobao_url_type()) == 4) {  // 金牛
          node->set_convert_type(kuaishou::ad::AdCallbackLog_ConvertType::AdCallbackLog_ConvertType_ECOM);
        } else if (static_cast<int32>(p_unit->web_uri_type()) == 2) {  // 自建站
          node->set_convert_type(
              kuaishou::ad::AdCallbackLog_ConvertType::AdCallbackLog_ConvertType_SITE_DIY);
        }
      }
      int64_t ids_mask = 0;
      int64_t mask = 1;
      for (auto rid : p_unit->resource_ids()) {
        ids_mask |= mask << (rid - 1);
      }
      node->set_resource_ids_mask(ids_mask);
      node->set_site_id(p_unit->site_id());
      node->set_convert_id(p_unit->convert_id());
      node->set_package_id(p_unit->package_id());
      // fill dpa info
      node->mutable_dpa_info()->set_dpa_type(p_unit->dpa_type());
      node->mutable_dpa_info()->set_library_id(p_unit->library_id());
      node->set_resource_ids_mask(p_unit->resource_ids_mask());
    }
    if (ad->base.p_unit->has_unit_support_info_optional()) {
      node->set_target_type(ad->base.p_unit->target_type());
      node->set_fiction_id(ad->base.p_unit->fiction_id());
      node->set_support_type(
          static_cast<kuaishou::ad::AdEnum_AdDspUnitSupportType>(ad->base.p_unit->support_type()));
      node->mutable_dpa_info()->set_dpa_industry_id(ad->base.p_unit->dpa_industry_id());
    }
    node->set_unit_type(ad->unit_type());
    node->set_is_in_acc_explore_status(ad->is_in_acc_explore_status());
    node->set_explore_bid_type(ad->explore_bid_type());
    if (ad->base.p_campaign != nullptr) {
      node->set_campaign_type(ad->campaign_type());
      node->set_campaign_sub_type(ad->campaign_sub_type());
      node->set_campaign_promotion_type(ad->promotion_type());
    }
    if (ad->base.p_account != nullptr) {
      node->set_account_type(ad->account_type());
      node->set_industry_id_v3(ad->industry_id_v3());
      node->set_city_product_id(ad->city_product_id());
      node->set_new_industry_id(ad->base.p_account->new_industry_id());
      node->set_industry_id(ad->base.p_account->industry_id());
      node->set_account_user_id(ad->base.p_account->user_id());
      node->set_licence_id_num(ad->base.p_account->licence_id_num());
      node->set_product_name(ad->base.p_account->product_name());
    }
    if (ad->base.p_wt_account != nullptr) {
      node->set_is_fast_app(ad->base.p_wt_account->is_fast_app());
    }
    int64_t agent_id = 0;
    int64_t agent_type = 0;
    if (ad->base.p_agent) {
      agent_id = ad->base.p_agent->agent_id();
      agent_type = ad->base.p_agent->agent_type();
    } else {  // 从索引补齐字段
      if (session_data_->ad_index && ad->base.p_account) {
        auto* p_agent_account = TableWrapper::GetAgentAccount(ad->base.p_account->id());
        if (p_agent_account) {
          auto* p_agent = TableWrapper::GetAgent(p_agent_account->agent_id());
          if (p_agent) {
            agent_id = p_agent->agent_id();
            agent_type = p_agent->agent_type();
          }
        }
      }
    }
    node->set_agent_id(agent_id > 0 ? agent_id : -1);
    node->set_agent_type(agent_type > 0 ? agent_type : -1);

    if (ad->base.p_live_stream_user_info) {
      node->set_is_live(ad->base.p_live_stream_user_info->is_live());
      node->set_index_live_stream_id(ad->base.p_live_stream_user_info->live_stream_id());
    } else {  // 从索引补齐
      if (session_data_->ad_index && ad->base.p_unit->has_unit_support_info_optional()) {
        auto p_live = TableWrapper::GetLiveStreamUserInfo(ad->base.p_unit->live_user_id());
        if (p_live) {
          node->set_is_live(p_live->is_live());
          node->set_index_live_stream_id(p_live->live_stream_id());
        }
      }
    }
    if (ad->base.p_live_stream_user_info != nullptr) {
      node->set_live_user_id(ad->base.p_live_stream_user_info->user_id());
      node->set_is_recruiting_live(ad->base.p_live_stream_user_info->is_recruiting_live());
      if (ad->base.p_live_stream_user_info->is_live()) {
        node->set_live_event_time(ad->base.p_live_stream_user_info->event_time());
      }
    }

    if (ad->base.p_industry_v3) {
      node->set_industry_parent_id_v3(ad->base.p_industry_v3->parent_id());
    } else {  // 从索引补齐字段
      if (ad->base.p_account) {
        auto p_industry_v3 = TableWrapper::GetIndustryV3(ad->base.p_account->industry_id_v3());
        if (p_industry_v3 && p_industry_v3->id() > 0) {
          node->set_industry_parent_id_v3(p_industry_v3->parent_id());
        }
      }
    }

    if (session_data_->ad_index && ad->base.p_account) {
      auto p_industry = TableWrapper::GetIndustryV3(ad->base.p_account->new_industry_id());
      if (p_industry && p_industry->id() > 0) {
        node->set_new_industry_parent_id(p_industry->parent_id());
      }
    }
    auto bid = node->mutable_bid();
    if (ad->base.p_unit != nullptr) {
      bid->set_bid(ad->base.p_unit->bid());
      bid->set_deep_conversion_bid(ad->base.p_unit->deep_conversion_bid());
      bid->set_index_cpa_bid(ad->base.p_unit->cpa_bid());
      bid->set_index_roi_ratio(ad->base.p_unit->roi_ratio());
      bid->set_speed_type(static_cast<kuaishou::ad::AdEnum::SpeedType>(ad->base.p_unit->speed()));
      bid->set_deep_conversion_type(static_cast<kuaishou::ad::AdCallbackLog_EventType>
                                      (ad->base.p_unit->deep_conversion_type()));
    }
    if (ad->base.p_unit->has_unit_support_info_optional()) {
      bid->set_enhance_conversion_type(
          ad->base.p_unit->enhance_conversion_type());
    }
    bid->set_bid_type(static_cast<kuaishou::ad::AdEnum_BidType>(ad->bid_info.bid_type));
    bid->set_ocpx_action_type(ad->ocpx_action_type());
  };

  auto fill_rta_sta_tag = [](const PrerankAdCommon *ad, kuaishou::ad::AdTargetResponse_Ad *node) {
    if (ad->p_retrieval_ad->others.real_time_type == kuaishou::ad::AdEnum::RTA) {
      node->mutable_rta_info()->set_rta_sta_tag(ad->bid_info.GetRtaStaTag());
      node->mutable_rta_info()->set_rta_ratio(ad->rta_ratio());
      node->mutable_rta_info()->set_rta_bid(ad->rta_bid());
    }
  };
  auto fill_bid_trans_info = [&](const PrerankAdCommon *ad, kuaishou::ad::AdTargetResponse_Ad *node) {
    auto *mutable_trans_info = node->mutable_ad_target_trans_info();
    mutable_trans_info->mutable_bid_trans_info()->CopyFrom(ad->bid_trans_info);
    mutable_trans_info->mutable_ad_bid_trans_info()->CopyFrom(ad->ad_bid_trans_info);
    node->mutable_bid()->set_fanstop_guaranteed_ratio(
      engine_base::GetBidTransInfoValueWithDefault<double>(ad->ad_bid_trans_info,
                                                            "fanstop_guaranteed_ratio", 0.0));
    node->mutable_bid()->set_auto_bid_explore(
      engine_base::GetBidTransInfoValueWithDefault<double>(ad->ad_bid_trans_info, "auto_bid_explore", 0.0));
  };
  auto fill_prerank_append_info = [](const PrerankAdCommon *ad, kuaishou::ad::AdTargetResponse_Ad *node) {
        auto *base_info = node->mutable_ad_target_to_rank()->mutable_ad_base();
        base_info->set_prerank_append_tag(ad->append_strategy_info.append_tag);
        base_info->set_prerank_append_group(ad->append_strategy_info.append_group);
  };
  auto fill_universe_tiny_info = [&](const PrerankAdCommon *ad, kuaishou::ad::AdTargetResponse_Ad *node) {
    if (!session_data_->is_universe_tiny_prerank_opt) {
      return;
    }
    auto *base_info = node->mutable_ad_target_to_rank()->mutable_ad_base();
    base_info->set_universe_tiny_query_type(ad->prerank.universe_tiny_query_type);
    base_info->set_app_package_name_id(ad->app_package_name_id());
  };
  auto fill_universe_rta_ug_info = [&](const PrerankAdCommon *ad, kuaishou::ad::AdTargetResponse_Ad *node) {
    auto *ug_rta_info = node->mutable_ad_target_to_rank()->mutable_ug_rta_info();
    ug_rta_info->set_lt_7(ad->bid_info.lt_7);
    ug_rta_info->set_next_stay_rate(ad->bid_info.next_stay_rate);
    ug_rta_info->set_lt7over1(ad->bid_info.lt7over1);
    ug_rta_info->set_roi(ad->bid_info.roi);
    ug_rta_info->set_product(ad->bid_info.product);
  };
  auto fill_prerank_common = [&](const PrerankAdCommon *ad, kuaishou::ad::AdTargetResponse_Ad *node) {
    fill_ad_common(ad->p_retrieval_ad, node);
    if (!target_server_skip_prerank_) {
      fill_bid_trans_info(ad, node);
      FillPrerankDataV1(ad, node);
      FillUniversePrerankData(ad, node);
      fill_prerank_append_info(ad, node);
    }
    fill_rta_sta_tag(ad, node);
    fill_universe_tiny_info(ad, node);
    fill_universe_rta_ug_info(ad, node);
  };
  auto target_check = [&](const RetrievalAdCommon *ad) -> bool {
    if (!check_target_) {
      return false;
    }

    if (ad->others.unit_index && !inner_data_->unit_roaring_bitmap.contains(ad->others.unit_index)) {
      session_data_->break_multi_tag = ad->strategy_base.multi_retrieval_tag;
      session_data_->break_unit_id = ad->unit_id();
      break_map_[ad->strategy_base.multi_retrieval_tag]++;
      return true;
    }
    return false;
  };
  // 直播召回融合，是否透出外循环直播结果 直播集群下，根据 kconf 开关，决定是否过滤
  size_t outer_live_ad_num = 0;

  // 联盟部署 允许出外循环直播广告
  auto outer_live_admit = [&] (const PrerankAdCommon *p_ad) ->bool {
    if (p_ad->live_creative_type() != AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
      return true;
    }
    outer_live_ad_num++;
    return true;
  };
  size_t outer_live_ad_filter_num = 0;
  std::for_each(session_data_->prerank_ad_list->Ads().begin(),
                  session_data_->prerank_ad_list->Ads().end(),
                  [&](const PrerankAdCommon *p_ad) {
                    if (target_check(p_ad->p_retrieval_ad)) {
                      return;
                    }
                    if (!outer_live_admit(p_ad)) {
                      outer_live_ad_filter_num++;
                      return;
                    }
                    auto node = response->add_ad_list();
                    fill_prerank_common(p_ad, node);
                  });
  session_data_->dot->Interval(outer_live_ad_filter_num, "outer_live_exp", "retrieval_postproc",
      "outer_ad_filtered_num", session_data_->outer_live_exp_name);
  session_data_->dot->Interval(outer_live_ad_num, "outer_live_exp", "retrieval_postproc",
                               "outer_ad_num", session_data_->outer_live_exp_name,
                               absl::StrCat(session_data_->ad_request->need_inner_retrieval(),
                                            session_data_->ad_request->need_outer_retrieval()));
  session_data_->dot->Interval(session_data_->prerank_ad_list->Size(),
                                "fill_response_size", "prerank_ad_list");

  for (const auto &kv : inner_data_->ranking_retrieval_cnt) {
    auto maybe_tag = ::ks::ad_target::multi_retr::RetrievalTag::_from_integral_nothrow(kv.first);  // NOLINT
    if (maybe_tag) {
      session_data_->dot->Interval(
          kv.second, "ad_target.multi_retr_num_in_postproc", maybe_tag->_to_string());  // NOLINT
    }
  }

  if (jk_ad_count > 0) {
    session_data_->dot->Interval(jk_ad_count, "ad_target.data_postproc.jk_retrieval");
  }
  if (ali_outer_count > 0) {
    session_data_->dot->Interval(ali_outer_count, "ad_target.data_postproc.ali_retrieval");
  }

  return true;
}

void RetrievalDataPostproc::FillInnerLoopAdQueueType(const RetrievalAdCommon &ad,
                                                     kuaishou::ad::AdTargetResponse_Ad *node) const {
  if (ad.strategy_base.queue_type == 2) {
    node->set_ad_queue_type(kuaishou::ad::AdEnum::SOFT_AD_QUEUE);
  } else {
    node->set_ad_queue_type(kuaishou::ad::AdEnum::HARD_AD_QUEUE);
  }
}

void RetrievalDataPostproc::FillPrerankDataV1(const PrerankAdCommon *ad, AdTargetResponse::Ad *node) const {
  if (ad == nullptr || node == nullptr) {
    return;
  }
  // adbase
  auto base_info = node->mutable_base_info();
  base_info->set_is_skip_bid_server(ad->base.is_skip_bid_server);
  base_info->set_prerank_type(ad->base.prerank_type);
  // bid
  auto bid = node->mutable_bid();
  bid->set_cost_ratio(ad->bid_info.cost_ratio);
  bid->set_auto_cpa_bid(ad->bid_info.GetAutoCpaBid());
  if (ad->p_retrieval_ad->base.p_unit->speed() == kuaishou::ad::AdEnum::SPEED_NO_BID) {
    bid->set_cpa_bid(ad->p_retrieval_ad->bid_info.cpa_bid);
    bid->set_roi_ratio(ad->p_retrieval_ad->bid_info.roi_ratio);
  }
  if (ad->p_retrieval_ad->IsFansNobid()) {
    bid->set_cpa_bid(ad->p_retrieval_ad->bid_info.cpa_bid);
  }
  // mcb
  if (ad->bid_info.bid_strategy_group == kuaishou::ad::AdEnum::MAX_CONV_STRATEGY) {
    bid->set_cpa_bid(ad->p_retrieval_ad->bid_info.cpa_bid);
    bid->set_roi_ratio(ad->p_retrieval_ad->bid_info.roi_ratio);
  }
  // ddl 紧张，复用下废弃字段 TODO(wangtao15 2023.01.01): 添加正式字段
  bid->set_constraint_roi(static_cast<int32_t>(ad->p_retrieval_ad->bid_info.cap_bid_type));
  bid->set_group_idx(ad->bid_info.group_idx);
  bid->set_auto_roas(ad->auto_roas());
  bid->set_deep_flow_control_rate(0.1);
  bid->set_deep_min_coef(ad->bid_info.deep_min_coef);
  bid->set_deep_min_bid_coef(ad->bid_info.deep_min_bid_coef);
  bid->set_calibration_ratio(ad->bid_info.calibration_ratio);
  bid->set_price_separate_ratio(ad->bid_info.price_separate_ratio);
  bid->set_auto_deep_cpa_bid(ad->bid_info.auto_deep_cpa_bid);
  bid->set_calibration_ratio(1.0);
  bid->set_price_separate_ratio(1.0);
  bid->set_twin_bid_strategy(ad->bid_info.twin_bid_strategy);
  bid->set_acc_cold_start_coef(ad->bid_info.acc_cold_start_coef);
  bid->set_raw_auto_cpa_bid(ad->bid_info.raw_auto_cpa_bid);
  bid->set_auto_cpa_bid_modify_tag(ad->bid_info.GetAutoCpaBidModifyTag());
  bid->set_auto_roas_modify_tag(ad->bid_info.GetAutoRoasModifyTag());
  bid->set_first_coef(ad->bid_info.first_coef);
  bid->set_second_coef(ad->bid_info.second_coef);
  bid->set_price_ratio(ad->bid_info.price_ratio);
  bid->set_target_factor(ad->bid_info.target_factor);
  bid->set_budget_coef(ad->bid_info.budget_coef);
  bid->set_cpa_coef(ad->bid_info.cpa_coef);
  bid->set_bid_strategy_group(ad->bid_info.bid_strategy_group);
  if (target_server_skip_prerank_) {
    bid->set_auto_manage(ad->bid_info.auto_manage);
    bid->set_auto_adjust(ad->bid_info.auto_adjust);
  }
  bid->set_constraint_action_type(ad->constraint_action_type());
  bid->set_constraint_cpa(ad->constraint_cpa());
  bid->set_mcb_cpa_bid(ad->bid_info.mcb_cpa_bid);
  bid->set_mcb_roi_ratio(ad->bid_info.mcb_roi_ratio);
  bid->set_performance_fix_ratio(ad->bid_info.performance_fix_ratio);
  bid->set_product_cpa_bid(ad->bid_info.product_cpa_bid);
  bid->set_product_roi_ratio(ad->bid_info.product_roi_ratio);
  bid->set_bid_server_id(ad->bid_info.bid_server_type);
  bid->set_high_quality_photo_adjust_ratio(1.0);
  if (session_data_->admit_hard_ad_delivery &&
      ad->account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE) {
    node->set_payer_id(ad->p_retrieval_ad->payer_id());
  }
  bid->set_is_account_bidding(ad->bid_info.is_account_bidding);
  bid->set_bid_coef(ad->bid_info.bid_coef);
  bid->set_aggre_key(ad->bid_info.aggre_key);
  // prerank
  auto prerank = node->mutable_prerank();
  prerank->set_ctr(ad->prerank.universe_prerank_item_click);
  prerank->set_delivery_rate(ad->prerank.delivery_rate);

  prerank->set_new_creative_delivery_rate(ad->prerank.new_creative_delivery_rate);
  prerank->set_conv_nextstay(ad->prerank.conv_nextstay);
  prerank->set_conversion_rate(ad->prerank.conversion_rate);
  prerank->set_purchase_ltv(ad->prerank.purchase_ltv);
  prerank->set_landingpage_submit_rate(ad->prerank.landingpage_submit_rate);
  prerank->set_prerank_universe_deep(ad->prerank.prerank_universe_deep);
  prerank->set_conv2_purchase(ad->prerank.conv2_purchase);
  prerank->set_lps2_purchase(ad->prerank.lps2_purchase);
  prerank->set_prerank_universe_invoke(ad->prerank.prerank_universe_invoke);
  prerank->set_prerank_universe_ctcvr(ad->prerank.prerank_universe_ctcvr);
  prerank->set_prerank_universe_ecpm(ad->prerank.ecpm_unify_ctcvr);
  prerank->set_score(ad->prerank.score);
  prerank->set_pos_in_prerank(ad->prerank.pos_in_prerank);
  prerank->set_prerank_universe_c1_event_order(ad->prerank.c1_event_order);
  prerank->set_univ_xdt_audience_rate(ad->prerank.univ_xdt_audience_rate);
  prerank->set_univ_xdt_pay_rate(ad->prerank.univ_xdt_pay_rate);
  prerank->set_univ_xdt_roas_rate(ad->prerank.univ_xdt_roas_rate);
  if (ad->campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
    prerank->set_univ_xdt_roas_rate(ad->prerank.universe_jiuniu_merchant_roas);
  }
  // prerank_ctcvr_score 通路已下线，改用 prerank_real_action_score
  prerank->set_prerank_ctcvr_score(ad->prerank.prerank_ctcvr_score);
}

void RetrievalDataPostproc::FillUniversePrerankData(const PrerankAdCommon *ad,
                                            kuaishou::ad::AdTargetResponse::Ad *node) const {
  // 填充 prerank 排序信息 复用接口
  auto prerank = node->mutable_prerank();
  if (ad->prerank.ensemble_score > 0.0) {
    prerank->set_ensemble_score(ad->prerank.ensemble_score);
  }
  if (ad->prerank.cpm_ltr_idx > 0) {
    prerank->set_cpm_ltr_idx(ad->prerank.cpm_ltr_idx);
  }
  if (ad->prerank.ctcvr_idx > 0) {
    prerank->set_ecpm_idx(ad->prerank.ctcvr_idx);
  }
}

void RetrievalDataPostproc::FillAdTargetTransInfo(const RetrievalAdCommon *ad,
                                                  kuaishou::ad::AdTargetResponse_Ad *node) {
  auto* mutable_trans_info = node->mutable_ad_target_trans_info();
  if (ad->base.p_wt_product) {
    mutable_trans_info->set_ks_brand_name_hash(ad->base.p_wt_product->ks_brand_name_hash());
  }
  if (ad->others.reported_trigger != 0) {  // 0 作为无意义值
    mutable_trans_info->set_reported_trigger(ad->others.reported_trigger);
  }

  for (auto &user_diver_index : session_data_->freq_context_data.ad_user_diversity_index) {
    if (user_diver_index.first >= kuaishou::ad::AdUserDiversityIndex::DiversityType_MIN &&
        user_diver_index.first <= kuaishou::ad::AdUserDiversityIndex::DiversityType_MAX) {
      auto *index = mutable_trans_info->add_ad_user_diversity_index();
      index->set_diversity_type(
          static_cast<kuaishou::ad::AdUserDiversityIndex::DiversityType>(user_diver_index.first));
      index->set_diversity_value(user_diver_index.second);
    }
  }
  // photo 只填一个 spuid
  if (ad->base.p_wt_photo != nullptr && ad->base.p_wt_photo->ad_spu_ids().size() != 0) {
    mutable_trans_info->add_ad_spu_ids(ad->base.p_wt_photo->ad_spu_ids()[0]);
  }
  if (ad->base.p_wt_product != nullptr) {
    mutable_trans_info->set_spu_id_v2(ad->base.p_wt_product->spu_id_v1());
    mutable_trans_info->set_spu_entity_cluster_id(ad->base.p_wt_product->spu_entity_cluster_id());
    mutable_trans_info->set_x7_entity_id(ad->base.p_wt_product->x7_entity_id());
  }
  if (ad->base.p_wt_photo != nullptr) {
    mutable_trans_info->set_product_cluster_id_v1(
        ad->base.p_wt_photo->product_cluster_id_v1());
    mutable_trans_info->set_product_cluster_id_v2(
        ad->base.p_wt_photo->product_cluster_id_v2());
  }
  if (ad->base.p_wt_photo != nullptr) {
    mutable_trans_info->set_is_youzhi(ad->base.p_wt_photo->is_youzhi());
  }
  auto unit = ad->base.p_unit;
  if (ad->base.p_wt_product != nullptr) {
    if (unit && unit->has_merchant_small_shop_support_info_optional()) {
      mutable_trans_info->set_merchant_product_id(unit->product_id());
    }
    mutable_trans_info->set_category_level_1_id(ad->base.p_wt_product->category_level_1_id());
    mutable_trans_info->set_category_level_2_id(ad->base.p_wt_product->category_level_2_id());
    mutable_trans_info->set_category_level_3_id(ad->base.p_wt_product->category_level_3_id());
    mutable_trans_info->set_category_level_4_id(ad->base.p_wt_product->category_level_4_id());
  }
  mutable_trans_info->set_is_outer_loop_native(ad->strategy_base.is_outer_loop_native);
  mutable_trans_info->set_is_outer_loop_native_photo(ad->strategy_base.is_outer_loop_native_photo);
  mutable_trans_info->set_replace_photo_id(0);
  mutable_trans_info->set_target_audience_score(ad->others.universe_ranking_score);

  mutable_trans_info->set_kol_user_type(ad->base.p_creative->kol_user_type());
  mutable_trans_info->set_is_dsp_outer_loop_native(ad->base.p_creative->outer_loop_native());
  if (node->ad_target_to_rank().ad_base().pec_style_ad_info().right_id() > 0) {
    mutable_trans_info->set_is_right_pec_ad(true);
  }
  if (unit && unit->has_merchant_small_shop_support_info_optional()) {
    mutable_trans_info->mutable_unit_small_shop_merchant_support_info()->set_item_id(unit->item_id());
    mutable_trans_info->mutable_unit_small_shop_merchant_support_info()->set_product_id(unit->product_id());
  }

  if (session_data_->enable_search_query_bidword_info &&
      !ad->search_query_bidword_info.empty()) {
    mutable_trans_info->mutable_search_query_info()->set_search_query_bidword_info(
        ad->search_query_bidword_info);
  }

  if (ad->others.pacing_flag >= 0) {
    mutable_trans_info->mutable_universe_pacing_control_info()->set_flag(
      ad->others.pacing_flag);
    mutable_trans_info->mutable_universe_pacing_control_info()->set_ratio(
      ad->others.pacing_ratio);
    mutable_trans_info->mutable_universe_pacing_control_info()->set_minute_cost(
      ad->others.pacing_minute_cost);
    mutable_trans_info->mutable_universe_pacing_control_info()->set_minute_budget(
      ad->others.pacing_minute_budget);
    mutable_trans_info->mutable_universe_pacing_control_info()->set_hour_cost(
      ad->others.pacing_hour_cost);
    mutable_trans_info->mutable_universe_pacing_control_info()->set_hour_budget(
      ad->others.pacing_hour_budget);
    mutable_trans_info->mutable_universe_pacing_control_info()->set_pre_minute_cost(
      ad->others.pacing_pre_minute_cost);
    mutable_trans_info->mutable_universe_pacing_control_info()->set_pre_minute_budget(
      ad->others.pacing_pre_minute_budget);
    mutable_trans_info->mutable_universe_pacing_control_info()->set_left_budget(
      ad->others.pacing_left_budget);
  }
}

}  // namespace ad_target
}  // namespace ks
