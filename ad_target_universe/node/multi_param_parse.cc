#include "teams/ad/ad_target_universe/node/multi_param_parse.h"

#include <algorithm>
#include <map>
#include <memory>
#include <set>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include "base/time/timestamp.h"
#include "glog/stl_logging.h"

#include "teams/ad/ad_target_universe/common/context_data.h"
#include "teams/ad/ad_target_universe/common/retrieval_base_data.h"

#include "teams/ad/ad_base/src/auto_param/auto_param.h"
#include "teams/ad/ad_target_universe/retrieval/layer_resource.h"
#include "teams/ad/ad_target_universe/client/kafka/trace_manager.h"
#include "teams/ad/ad_target_universe/utils/kconf/kconf.h"
#include "teams/ad/ad_index/index/utils/kconf/kconf_util.h"
#include "teams/ad/engine_base/cache_loader/support_project_p2p_cache.h"
#include "teams/ad/engine_base/kconf/kconf.h"

#include "teams/ad/ad_target_universe/common/multi_param_manager.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_target_universe/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_target_universe/framework/tsm/config/kconf_loader.h"
#include "teams/ad/ad_target_universe/framework/tsm/config/ab2pb/ab2pb.h"
#include "teams/ad/ad_target_universe/retrieval/base_data.h"

namespace ks {
namespace ad_target {

namespace detail {
using ks::ad_target::multi_retr::RetrievalTag;

const absl::flat_hash_map<int32_t, std::string> kUdfName = {
    {+RetrievalTag::GW_UNIVERSE_PAT_TAG_RETRIEVAL, "UniverseLive2Creative"},
    {+RetrievalTag::GW_UNIVERSE_INNER_FANS_RETRIEVAL, "UniverseLiveInnerAuthor2Creative"},
    {+RetrievalTag::YYX_UNIVERSE_SPU2LIVE_RETR, "universe_spu_2creative_index"},
    {+RetrievalTag::YYX_UNIVERSE_SPU2PHOTO_RETR, "universe_spu_2creative_by_photo_index"},
    {+RetrievalTag::YYX_UNIVERSE_SPU2LIVE_REALTIME, "universe_spu_2creative_index"},
    {+RetrievalTag::YYX_UNIVERSE_SPU2PHOTO_REALTIME, "universe_spu_2creative_by_photo_index"},
    {+RetrievalTag::YYX_UNIVERSE_HOT_SPU, "universe_spu_2creative_by_photo_index"},
    {+RetrievalTag::YYX_UNIVERSE_HOT_AUTHOR, "UniverseLiveInnerAuthor2Creative"},
    {+RetrievalTag::UNIVERSE_OUTER_INDUSTRY_STRATEGY, "unit_id_index"},
    {+RetrievalTag::UNIVERSE_CID_STRATEGY, "unit_id_index"},
    {+RetrievalTag::UNIVERSE_CID_STRATEGY_UPDATE, "unit_id_index"},

    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_DISTRIBUTE, "unit_id_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_EXACT, "universe_package_id_ocpx_index_v2"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_INTERVENE, "universe_package_id_ocpx_index_v2"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_CATEGORY, "universe_package_id_ocpx_index_v2"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_TEXT, "universe_package_name_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_PACKAGE, "universe_package_name_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_DISTRIBUTE_NEW, "universe_package_id_ocpx_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_DISTRIBUTE_APP_ADVANCE,
     "universe_package_id_ocpx_app_advance_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_TEXT_LLM, "universe_package_name_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_A, "universe_package_name_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_B, "universe_package_name_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_C, "universe_package_name_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_D, "universe_package_name_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_E, "universe_package_name_index"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_F, "universe_package_name_index"},

    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_HANZI, "universe_package_id_ocpx_index_sug"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PINYING, "universe_package_id_ocpx_index_sug"},
    {+RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PINYING_F_C, "universe_package_id_ocpx_index_sug"},
    {+RetrievalTag::UNIVERSE_TINY_USER_QUERY_HISTORY, "universe_product_id_index"},
    {+RetrievalTag::UNIVERSE_TINY_USER_QUERY_HISTORY_LONGER, "universe_package_name_index"},
    {+RetrievalTag::UNIVERSE_RTA_MONOPOLIZE_RANDOM_RETRIEVAL, "unit_id_index"},
    {+RetrievalTag::YYX_UNIVERSE_AD4RECO_RETRIEVAL, "Photo2Creative"},
    {+RetrievalTag::UNIVERSE_LIVE_CJ_CID_CHANNEL, "universe_author_deep_index"},
    {+RetrievalTag::UNIVERSE_INNER_DEEP_HIGH_VALUE_CROWD, "universe_author_deep_index"},
    {+RetrievalTag::UNIVERSE_INNER_LOOP_lIVE_CHANNEL, "universe_product_id_index"},
    {+RetrievalTag::UNIVERSE_OUT_RETARGET_RETRIEVAL, "universe_out_retarget_index"},
    {+RetrievalTag::UNIVERSE_COLD_START_PHOTO_RECALL, "Photo2Creative"},
    {+RetrievalTag::UNIVERSE_LLM_U2P_RECALL, "universe_product2creative_index"},
    {+RetrievalTag::UNIVERSE_SPLASH_CACHE_PHOTO_RETRIEVAL, "photo_id_index"},
    {+RetrievalTag::UNIVERSE_PACKAGE_DISTRIBUTE, "universe_product2cid"},
};

std::string GetUdfName(int32_t tag) {
  if (auto it = kUdfName.find(tag); it != kUdfName.end()) {
    return it->second;
  }

  return "creative_id_index";
}
}  // namespace detail
using namespace ks::ad_target::multi_retr;  // NOLINT

void MultiRetrSwitch::InitDisableMultiRetrTagSet(base::Json *json) {
  if (!json) {
    return;
  }

  const base::Json *disable_multi_retr = json->Get("disable_multi_retr");

  if (!disable_multi_retr || !disable_multi_retr->GetBoolean("enable", false)) {
    return;
  }

  base::Json *disable_multi_retr_tag = disable_multi_retr->Get("disable_multi_retr_tag");
  if (disable_multi_retr_tag && disable_multi_retr_tag->IsArray() && disable_multi_retr_tag->size() > 0) {
    for (int i = 0; i < disable_multi_retr_tag->size(); i++) {
      int32_t value = disable_multi_retr_tag->GetInt(i, 0);
      disable.emplace(value);
    }
  }
}

void MultiRetrSwitch::Initialize(base::Json *json) {
  if (!json) {
    return;
  }

  const base::Json *multi_retr_json = json->Get("multi_retr_json");

  if (!multi_retr_json) {
    return;
  }

  p2p_switch = multi_retr_json->GetBoolean("p2p_switch", false);
  picasso_switch = multi_retr_json->GetBoolean("picasso_switch", false);
  redis_switch = multi_retr_json->GetBoolean("redis_switch", false);
  local_switch = multi_retr_json->GetBoolean("local_switch", false);
  ann_switch = multi_retr_json->GetBoolean("ann_switch", false);
  brute_switch = multi_retr_json->GetBoolean("brute_switch", false);
  common_leaf_switch = multi_retr_json->GetBoolean("common_leaf_switch", false);

  InitDisableMultiRetrTagSet(json);
}

bool MultiParamParse::Prepare() {
  session_data_ = context_->GetMutableContextData<ContextData>();
  // 这里验证 context 非 null ，后续各个 private 函数无须再判断
  if (!session_data_) {
    LOG(ERROR) << "MultiParamParse session_data failed, context is null";
    return false;
  }

  // multi_params  init
  for (const auto &enum_value : MultiRetrRequestType::_values()) {  // NOLINT
    int enum_id = enum_value._to_integral();                        // NOLINT
    session_data_->multi_param_manager.multi_params[enum_id];
  }

  // TODO(chengxuyuan):  保存 flow_type 不要用 str，然后特殊流量重置 flow type
  session_data_->multi_param_manager.flow_type_str =
      session_data_->pos_manager_base.GetFlowType()._to_string();  // NOLINT

  // clear
  multi_retr_switch_.Clear();
  multi_tag_opt_.clear();
  // 降级开关
  auto a1_config = AdKconfUtil::a1Config();
  multi_retr_switch_.Initialize(a1_config->data.get());  // 异构源开关

  // 生态类标签 + 海选参数
  std::string preselect_ab_key =
      absl::StrCat("preselect_params_", session_data_->multi_param_manager.flow_type_str);
  // const std::string preselect_ab_key = "cmd=amazing_model,dim=64,search_num=10000";
  session_data_->multi_param_manager.preselect_params.Init(
      session_data_->session_context->TryGetString(preselect_ab_key, ""));
  // 效果类标签 + 海选参数
  std::string preselect4eff_ab_key =
      absl::StrCat("preselect4effect_params_", session_data_->multi_param_manager.flow_type_str);
  session_data_->multi_param_manager.preselect4effect_params.Init(
      session_data_->session_context->TryGetString(preselect4eff_ab_key, ""));
  // 效果类 photo 召回 标签 + 海选
  std::string preselect4eff_photo_ab_key =
      absl::StrCat("preselect4effectphoto_params_", session_data_->multi_param_manager.flow_type_str);
  session_data_->multi_param_manager.preselect4effect_photo_params.Init(
      session_data_->session_context->TryGetString(preselect4eff_photo_ab_key, ""));

  std::string product_tag_key =
       absl::StrCat("enable_preselect_product_off_recall_", session_data_->multi_param_manager.flow_type_str);
    session_data_->preselect_use_product =
       session_data_->session_context->TryGetBoolean(product_tag_key, false);

  // 初始化分层
  session_data_->multi_path_config.GetAllLayerConfig().clear();

  is_universe_tiny_deploy_ = ks::ad_server::AdIndexKconf::isUniverseTinyDeploy();
  universe_migrate_tsm_set_ = AdKconfUtil::universeMigrateTsmSet();
  universe_inner_shallow_scene_pos_set_ = AdKconfUtil::universeInnerShallowScenePosSet();
  universe_tx_scene_pos_set_ = AdKconfUtil::universeTxScenePosSet();
  if (SPDM_enable_universe_tx_scene_auto(session_data_->spdm_ctx)) {
    universe_tx_scene_pos_set_ = AdKconfUtil::universeTxScenePosAutoSet();
  }
  return true;
}

bool MultiParamParse::SkipRecall() const {
  // universe elatic exp
  const auto univse_elatic_exp_enum = session_data_->universe_elastic_info.univse_elatic_exp_enum;
  if (univse_elatic_exp_enum == +UniverseElaticExpTag::skip_multi_retrieval ||
      univse_elatic_exp_enum == +UniverseElaticExpTag::skip_multi_retrieval_with_adjust_queue) {
    return true;
  }
  return false;
}


void MultiParamParse::PrepareUdf() {
  // 清理 layer
  ErasePath();
  absl::flat_hash_map<std::string, int> udf_count;
  for (auto &[tag, pv] : multi_tag_opt_) {
    auto& v = *pv;
    if (v.SearchTableName().empty()) {
      v.set_table_name(detail::GetUdfName(tag));
    }

    udf_count[v.SearchTableName()] += 1;
    v.Prepare(session_data_);
  }
  // analysis prepare
  auto iter = multi_tag_opt_.find(AdKconfUtil::targetAnalysisTagId());
  if (iter != multi_tag_opt_.end()) {
    iter->second->InitAnalysisCell();
  }

  // offline
  if (AdKconfUtil::isTargetOffline()) {
    for (auto &&[tag_id, opt] : multi_tag_opt_) {
      opt->InitAnalysisCell();
    }
  }

  for (auto &[name, cnt] : udf_count) {
    session_data_->lite_dot->Interval(cnt, "ad_target.outer.udf", name);
  }
}


bool MultiParamParse::ProcessInner() {
  // 1.0 初始化
  Prepare();
  // 保证 graph driver 执行 init
  ad_base::ScopeGuard guard([&]() {
    session_data_->tsm_state = session_data_->graph_driver.Init(session_data_) &&
                              session_data_->graph_driver.Run();
  });

  if (SkipRecall()) {
    return true;
  }

  // 2.0 abtest 参数解析、准入
  auto is_universe_tiny_deploy_use_tsm = is_universe_tiny_deploy_ && false;
  auto is_universe_huge_deploy_migrate_tsm = !is_universe_tiny_deploy_;
  if (is_universe_tiny_deploy_use_tsm) {  // 联盟小系统是否走 TSM
    ParseTsmParam();
  } else if (is_universe_huge_deploy_migrate_tsm) {  // 联盟大系统迁移 TSM
    ParseMultiRetrParam();
    ParseTsmParam();
  } else {
    ParseMultiRetrParam();
  }

  // 3.0 资源约束
  RestrictResource();

  // 4.0 参数维护与校验
  CheckAndAdjustParam();

  PrepareUdf();

  return true;
}

bool MultiParamParse::EnableChannel(const int32_t tag_id) const {
  // 联盟召回通路加一个流量控制参数 默认打开
  int32_t ad_style = (session_data_->pos_manager_base.request_imp_infos.size() > 0)
                          ? session_data_->pos_manager_base.request_imp_infos[0].ad_style
                          : -1;
  bool enable_ad_style = session_data_->session_context->TryGetBoolean(
      absl::Substitute("enable_multi_search_union_adstyle_$0_$1", tag_id, ad_style), true);
  if (!enable_ad_style) {
    return false;
  }
  // 联盟厂商分发流量独占召回 分发独占 tag_id 只出在分发 style
  if (AdKconfUtil::universeAppStoreDistributeTags()->count(tag_id) &&
      AdKconfUtil::universeAppStoreDistributeStyles()->count(ad_style) == 0) {
    return false;
  }
  return session_data_->session_context->TryGetBoolean(
         absl::Substitute("enable_multi_search_$0", tag_id), false);
}

std::string MultiParamParse::GetConfigAbKey(const std::string& tag_name) const {
  std::string ab_name = absl::StrCat("multi_search_", absl::AsciiStrToLower(tag_name),
                                     "_", session_data_->multi_param_manager.flow_type_str);
  return ab_name;
}

void MultiParamParse::AdjustMultiRetrParam() {
  bool enalbe_multi_retr_quota_opt = SPDM_universe_multi_retr_quota_opt(session_data_->spdm_ctx);
  if (!enalbe_multi_retr_quota_opt) {
    return;
  }
  double adjust_ratio = SPDM_univ_multi_retr_sn_adjust_ratio(session_data_->spdm_ctx);
  if (adjust_ratio < 0.0) {
    return;
  }
  // 根据媒体耗时情况 对召回通路参数进行降级操作
  uint32_t timeout = session_data_->ad_request->ad_user_info().universe_timeout();
  if (timeout > 0) {
    adjust_ratio = adjust_ratio * timeout / KMaxTimeOut;
    adjust_ratio = adjust_ratio > 1.0 ? 1.0 : adjust_ratio;
  }
  uint32_t effect_layer_max_traverse_num =
        SPDM_effect_layer_max_traverse_num_universe(session_data_->spdm_ctx);
  std::set<absl::string_view> universe_effect_path_list =
        absl::StrSplit(SPDM_universe_effect_path(session_data_->spdm_ctx), ',');
  for (auto &[tag, path_config] : multi_tag_opt_) {
    auto& tag_opt = *path_config;
    // 对所有召回通路 search num 调整
    tag_opt.set_trigger_token_max_quota(adjust_ratio * tag_opt.TriggerTokenMaxQuota());
    // 对所有召回通路 merge quota 调整
    tag_opt.set_merge_creative_max_quota(adjust_ratio * tag_opt.MergeCreativeMaxQuota());
    // 限制通路的最大展开 cid 个数
    size_t total_creative_max_quota = adjust_ratio * tag_opt.UnivSearchTotalCreativeMaxQuota();
    if (total_creative_max_quota <= 0) {
      total_creative_max_quota = tag_opt.MergeCreativeMaxQuota();
      if (universe_effect_path_list.count(std::to_string(tag)) > 0) {
        total_creative_max_quota = adjust_ratio * effect_layer_max_traverse_num;
      }
    }
    tag_opt.set_search_total_creative_max_quota(total_creative_max_quota);
  }
  session_data_->lite_dot->Interval(1000.0 * adjust_ratio, "ad_target.multi_param_adjust_ratio");
}

void MultiParamParse::ParseMultiRetrParam() {
  auto &multi_params = session_data_->multi_param_manager.multi_params;
  const auto degrade_kconf = AdKconfUtil::multiRetrievalDegrade();
  const auto &degrade_config = degrade_kconf->data();

  auto& layers = session_data_->multi_path_config.GetAllLayerConfig();
  layers.emplace_back();
  auto& layer = layers.back();

  for (const auto &adjust_channels_type : multi_retr::RetrievalTag::_values()) {
    int tag_id = adjust_channels_type._to_integral();

    // 非法准入判断
    if (!Admit(tag_id)) {
      continue;
    }

    // 联盟大系统迁移 TSM
    // 命中实验标签，并且 kconf 里面有，则 continue
    if (!is_universe_tiny_deploy_ && universe_migrate_tsm_set_->count(tag_id) > 0) {
      continue;
    }

    // ab 开关逻辑
    if (!EnableChannel(tag_id)) {
      continue;
    }

    std::string ab_key = GetConfigAbKey(adjust_channels_type._to_string());
    std::string ab_config_str = session_data_->session_context->TryGetString(ab_key, "");
    if (ab_config_str.empty() || ab_config_str == "None" || ab_config_str == "none") {
      continue;
    }

    int request_type = 0;

    shiba::ExpPathConf path_conf;
    if (!tsm::Ab2pb::String2Pb(ab_config_str, request_type, path_conf)) {
      session_data_->lite_dot->Count(1, "retrieval_parse_error", absl::StrCat(request_type), ab_key);
      LOG_EVERY_N(WARNING, 1000) << "Fail to parse multi param " << ab_config_str << " ### " << ab_key;
      continue;
    }

    if (degrade_config.HitReqType(request_type)) {
      session_data_->lite_dot->Count(1, "ad_target.degrade_req_type",
                                absl::StrCat(request_type), absl::StrCat(tag_id));
      continue;
    }

    auto path_config = std::make_unique<tsm::PathConfig>(path_conf, ab_key, "", tag_id);

    auto &tag_param = *path_config;

    // 检查 cmd 是否合法
    if (tag_param.RankerType() == +MultiRetrRequestType::kAnn ||
        tag_param.RankerType() == +MultiRetrRequestType::kCommonLeaf ||
        tag_param.RankerType() == +MultiRetrRequestType::kPs ||
        tag_param.RankerType() == +MultiRetrRequestType::kPreSelect ||
        tag_param.RankerType() == +MultiRetrRequestType::kTdm) {
      if (tag_param.cmd().empty()) {
        LOG_EVERY_N(ERROR, 1000) << "TTTT tag_parse_status: " << tag_param.RankerType()
                                 << " " << *path_config;
        continue;
      }
    }

    if (tag_param.MergeCreativeMaxQuota() <= 0 || tag_param.TriggerTokenMaxQuota() <= 0) {
      session_data_->lite_dot->Count(
          1, "ad_target.invalid_tsm_config", std::to_string(tag_id), std::to_string(tag_param.RankerType()));
      LOG_EVERY_N(INFO, 100) << "TTTT invalid " << tag_id << "#" << tag_param.RankerType();
      continue;
    }
    layer.GetPathConfigs().push_back(std::move(path_config));

    session_data_->lite_dot->Count(1, "retrieval_abtest_key", ab_key, std::to_string(tag_param.RankerType()),
                              std::to_string(tag_id));
  }

  for (auto& layer_it : layers) {
    for (auto& path_config : layer_it.GetPathConfigs()) {
      // 回写到 multi_tag_opt
      multi_tag_opt_.emplace(path_config->PathId(), path_config.get());
      multi_params[path_config->RankerType()].insert(path_config->PathId());
    }
  }
}

bool MultiParamParse::ParseTsmParam() {
  auto* kconf_loader = tsm::KconfLoader::GetInstance();

  auto biz_config = kconf_loader->GetBizConfig();
  if (!biz_config) {
    return false;
  }

  auto& config = session_data_->multi_path_config;
  auto flow_type = session_data_->pos_manager_base.GetFlowType();
  std::set<int32_t> scene_id_set;
  scene_id_set.insert(flow_type);

  UniverseSetSceneIdSet(scene_id_set);

  for (const auto& biz : biz_config->biz_configs()) {
    std::string biz_name = BizType_Name(biz.biz_type());

    config.set_biz_quota(biz_name, biz);

    std::set<int32_t> default_paths;
    for (auto scene_id : scene_id_set) {
      std::vector<int32_t> layer_ids = kconf_loader->GetLayerIds(biz_name, scene_id);
      for (int32_t layer_id : layer_ids) {
        auto pb_config = kconf_loader->GetConfig(biz_name, scene_id, layer_id);
        if (!pb_config) {
          continue;
        }
        std::string layer_exp = absl::Substitute("SHIBA_AB_$0_S$1L$2_layer_exp",
                                                 biz_name, scene_id, layer_id);
        std::string layer_exp_name = session_data_->session_context->TryGetString(layer_exp, "default");
        auto* pb_layer_conf = kconf_loader->GetLayerConfig(pb_config, layer_exp_name);
        if (!pb_layer_conf) {
          continue;
        }
        config.layer_configs_.emplace_back(biz_name, scene_id, layer_id, *pb_layer_conf);
        auto& layer_config = config.layer_configs_.back();

        std::vector<int32_t> path_ids = kconf_loader->GetPathIds(pb_config);
        for (int32_t path_id : path_ids) {
          if (scene_id == 0) {
            default_paths.insert(path_id);
          }
          if (scene_id != 0 && default_paths.count(path_id) > 0) {
            continue;
          }
          // 联盟大系统迁移 TSM
          // 1. 没有命中实验标签要 continue
          // 2. 命中了实验标签，但是 kconf 里面没有， 则 continue
          if (!is_universe_tiny_deploy_ && universe_migrate_tsm_set_->count(path_id) == 0) {
            continue;
          }
          // 联盟弹性队列多路黑白名单控制
          if (SPDM_enable_universe_elastic_ab_conf(session_data_->spdm_ctx) &&
              ks::engine_base::AdKconfUtil::universeElasticConfigAbExp() != nullptr) {
            auto exp_id = SPDM_universe_elastic_ab_conf(session_data_->spdm_ctx);
            auto conf =
              ks::engine_base::AdKconfUtil::universeElasticConfigAbExp()->data().Get(exp_id);
            if (!conf.value_or(ks::engine_base::AdKconfUtil::universeElasticConfig())->Check(
                session_data_->universe_elastic_config_index, path_id)) {
              continue;
            }
          } else if (!ks::engine_base::AdKconfUtil::universeElasticConfig()->Check(
              session_data_->universe_elastic_config_index, path_id)) {
            continue;
          }
          if (session_data_->session_context->TryGetBoolean(
              absl::Substitute("disable_shiba_ab_outer_$0", path_id), false)) {
            continue;
          }
          std::string path_exp = absl::Substitute("SHIBA_AB_$0_S$1L$2_p$3_exp",
                                                  biz_name, scene_id, layer_id, path_id);
          std::string path_exp_name = session_data_->session_context->TryGetString(path_exp, "");
          auto* pb_exp_path_conf = kconf_loader->GetExpPathConfig(pb_config, path_id, path_exp_name);
          if (!pb_exp_path_conf) {
            continue;
          }
          layer_config.AddExpPathConf(*pb_exp_path_conf, path_exp, path_exp_name, path_id);
        }

        std::stable_sort(layer_config.path_configs_.begin(),
                         layer_config.path_configs_.end(),
                         [](const auto& left, const auto& right) {
                           return left->Priority() < right->Priority();
                         });
      }
    }
  }

  auto& layers = session_data_->multi_path_config.GetAllLayerConfig();
  auto& multi_params = session_data_->multi_param_manager.multi_params;
  for (auto& layer_it : layers) {
    for (auto& path_config : layer_it.GetPathConfigs()) {
      // 回写到 multi_tag_opt
      multi_tag_opt_.emplace(path_config->PathId(), path_config.get());
      multi_params[path_config->RankerType()].insert(path_config->PathId());
    }
  }
  return true;
}

bool MultiParamParse::Admit(int32_t tag_id) {
  // 是否命中 valid tags
  if (!AdKconfUtil::multiParamsConfig()->data().valid_tags.count(tag_id)) {
    return false;
  }
  // 联盟场景根据 is_request_model 判断是否走内循环模型召回
  if (!session_data_->universe_ks_install_info.is_request_model &&
      AdKconfUtil::universeInnerMultiTags()->data().online_tags.count(tag_id)) {
    return false;
  }
  int32_t ad_style = (session_data_->pos_manager_base.request_imp_infos.size() > 0)
                  ? session_data_->pos_manager_base.request_imp_infos[0].ad_style
                  : 0;
  // 联盟厂商大系统自有 & 三方流量 绿色通道
  if (!session_data_->is_universe_non_tiny_manufacturer_pv &&
      AdKconfUtil::universeManufacturerNonTinyWhiteTag() == tag_id) {
    return false;
  }
  // 联盟厂商 sug 优化
  if (is_universe_tiny_deploy_) {
    // 白名单内 pos 不走 369 通路
    if (AdKconfUtil::universeTinySkipEcpmModelRecallPosSet()->count(
          session_data_->pos_manager_base.GetMediumPosId())) {
      if (tag_id == multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_DISTRIBUTE) {
        return false;
      }
    }

    const auto& perf_conf = engine_base::AdKconfUtil::universeTinyPerfConf()->data();
    if (perf_conf.Contains("skip_recall_274", session_data_->pos_manager_base.GetMediumPosId())) {
      if (tag_id == multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_DISTRIBUTE_NEW) {
        return false;
      }
    }
    // 包名召回准入逻辑，369 通路作为包名召回的兜底逻辑不返回 false
    if (tag_id != multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_DISTRIBUTE
    && AdKconfUtil::universeTinySearchRecallSuggestPackageMultiTags() != nullptr) {
      // 判断开关是否生效
      auto is_sug_package_recall_valid =
      SPDM_enable_universe_tiny_search_package_flow(session_data_->spdm_ctx)
      && SPDM_enable_universe_tiny_search_recall_diverse_by_index(session_data_->spdm_ctx);
      if (session_data_->is_pkg_name_flow && is_sug_package_recall_valid) {
        if (!AdKconfUtil::universeTinySearchRecallSuggestPackageMultiTags()->count(tag_id)) {
          //（ 包名流量 && 开关打开）&& 旧通路
          return false;
        } else if ((session_data_ != nullptr && !session_data_->path_id_2_pkg_and_pos_list.contains(tag_id))
                   || session_data_->path_id_2_pkg_and_pos_list[tag_id].empty()) {
          //（ 包名流量 && 开关打开）&& 新通路 && 子列表为空
          return false;
        }
      }
      // （ 非包名流量 || 非开关打开）&& 新通路
      if ((!session_data_->is_pkg_name_flow || !is_sug_package_recall_valid)
          && AdKconfUtil::universeTinySearchRecallSuggestPackageMultiTags()->count(tag_id)) {
        return false;
      }
    }
    // 分发场景走用户检索历史召回
    if (tag_id == multi_retr::RetrievalTag::UNIVERSE_TINY_USER_QUERY_HISTORY ||
        tag_id == multi_retr::RetrievalTag::UNIVERSE_TINY_USER_QUERY_HISTORY_LONGER) {
      return AdKconfUtil::universeTinyUserQueryHistroyStyleWhitelist()->count(ad_style);
    }
  }
  // 开屏场景视频缓存重召回
  if (SPDM_enable_splash_support_video_sdk(session_data_->spdm_ctx) &&
        tag_id == multi_retr::RetrievalTag::UNIVERSE_SPLASH_CACHE_PHOTO_RETRIEVAL) {
    if (ad_style != 4 || session_data_->ad_request->orient_info_v2().photo_info_size() <= 0) {
      return false;
    }
  }
  // 联盟场景 腾快通 RTA 只走特定召回通路
  if (session_data_->is_universe_tkt_rta_flow &&
      !AdKconfUtil::universeInnerTencentRtaMultiTags()->count(tag_id)) {
    return false;
  }
  // 弹性计算
  const auto univse_elatic_exp_enum = session_data_->universe_elastic_info.univse_elatic_exp_enum;
  if (univse_elatic_exp_enum == +UniverseElaticExpTag::only_e2e ||
             univse_elatic_exp_enum == +UniverseElaticExpTag::only_e2e_with_adjust_queue) {
    if (tag_id != +multi_retr::RetrievalTag::RETRIEVAL_DELIVERY) {
      return false;
    }
  }
  // 弹性队列通路黑白名单
  if (SPDM_enable_universe_elastic_ab_conf(session_data_->spdm_ctx) &&
      ks::engine_base::AdKconfUtil::universeElasticConfigAbExp() != nullptr) {
    auto exp_id = SPDM_universe_elastic_ab_conf(session_data_->spdm_ctx);
    auto conf =
      ks::engine_base::AdKconfUtil::universeElasticConfigAbExp()->data().Get(exp_id);
    if (!conf.value_or(ks::engine_base::AdKconfUtil::universeElasticConfig())->Check(
        session_data_->universe_elastic_config_index, tag_id)) {
      return false;
    }
  } else if (!ks::engine_base::AdKconfUtil::universeElasticConfig()->Check(
      session_data_->universe_elastic_config_index, tag_id)) {
    return false;
  }
  if (multi_retr_switch_.disable.contains(tag_id)) {
    LOG_EVERY_N(INFO, 1000) << "Disable Tag : " << tag_id;
    return false;
  }

  return true;
}

template <class ParamType>
void GenPriority(const ParamType &kconf_params,
                 const absl::flat_hash_map<int32_t, tsm::PathConfig*> &ab_params,
                 std::map<int, std::set<int>> &prio_to_tags) {  // NOLINT
  // ab_params 已经是准入过的参数
  int32_t tag_id = 0;
  int32_t priority = 0;
  for (auto &param : kconf_params) {
    tag_id = param.channel_id();
    auto it = ab_params.find(tag_id);
    if (it == ab_params.end()) {
      continue;
    }

    // kconf 都是推全的优先级 优先 kconf 取
    priority = (param.priority() != 0) ? param.priority() : it->second->Priority();
    prio_to_tags[priority].insert(tag_id);
  }
}

void MultiParamParse::RestrictResource() {
  const auto config = AdKconfUtil::multiParamsConfig();
  auto &multi_params = session_data_->multi_param_manager.multi_params;
  auto &multi_tag_opt = multi_tag_opt_;
  // 对所有通路的召回参数进行调整
  AdjustMultiRetrParam();
  // 计算每一层的资源
  std::map<int32_t, std::set<int32_t>> prio_to_tags;
  int32_t disable_count = 0, total_tags = 0;
  LayerResource resource(session_data_);
  for (auto &pair : config->data().pb().layers()) {
    auto &layer = pair.second;
    // 生成该层优先级配置
    prio_to_tags.clear();
    GenPriority(layer.base(), multi_tag_opt, prio_to_tags);
    GenPriority(layer.exp(), multi_tag_opt, prio_to_tags);

    // 计算该层资源
    auto ratio = SPDM_universe_adjust_brecall_quato_ratio(session_data_->spdm_ctx);
    resource.SetResource(pair.first, layer, ratio);

    disable_count = 0;
    total_tags = 0;
    for (auto &prio_tag_pair : prio_to_tags) {
      auto &tags = prio_tag_pair.second;
      for (auto &tag_id : tags) {
        if (auto it = multi_tag_opt.find(tag_id); it != multi_tag_opt.end()) {
          total_tags++;
          auto &param = *it->second;
          param.level = pair.first;
          if (resource.InvalidSearchNum(param) || resource.InvalidQuota(param)) {
            // 没资源了
            multi_tag_opt.erase(tag_id);
            disable_count++;
          }
        }
      }
    }
    // 总资源打点
    if (disable_count > 0) {
      session_data_->lite_dot->Interval(disable_count, "ad_target.multi_param_disable_tags", layer.desc());
      session_data_->lite_dot->Interval(total_tags, "ad_target.multi_param_total_tags", layer.desc());
    }
    // 分层资源细节打点
    resource.DotLayerResource();
  }
  // 回写参数
  // 联盟弹性队列调整实验
  double adjust_ratio = 1.0;
  const auto univse_elatic_exp_enum = session_data_->universe_elastic_info.univse_elatic_exp_enum;
  if (univse_elatic_exp_enum == +UniverseElaticExpTag::scaling_ratio ||
      univse_elatic_exp_enum == +UniverseElaticExpTag::scaling_ratio_with_adjust_queue) {
    adjust_ratio = session_data_->universe_elastic_info.adjust_ratio;
  }
  for (auto &pair : multi_params) {
    auto &op_param = pair.second;
    for (auto iter = op_param.begin(); iter != op_param.end();) {
      // 未找到说明资源不足被干掉了
      auto tag_id = *iter;
      if (0 == multi_tag_opt.count(tag_id)) {
        // 删除前打点具体模型
        session_data_->lite_dot->Count(1, "ad_target.multi_param_disable_tag", std::to_string(tag_id));
        iter = op_param.erase(iter);
      } else {
        if (auto it = multi_tag_opt.find(tag_id); it != multi_tag_opt.end()) {
          auto &param = *it->second;
          param.set_trigger_token_max_quota(param.TriggerTokenMaxQuota() * adjust_ratio);
          param.set_merge_creative_max_quota(param.MergeCreativeMaxQuota() * adjust_ratio);
          session_data_->multi_param_manager.total_search_num += param.TriggerTokenMaxQuota();
        }
        iter++;
      }
    }
  }

  return;
}

void MultiParamParse::ErasePath() {
    auto &cfg = session_data_->multi_path_config;
    auto &layers = cfg.GetAllLayerConfig();
    if (!layers.empty()) {
      auto &path_configs = layers[0].GetPathConfigs();
      auto it = std::remove_if(path_configs.begin(), path_configs.end(), [&](auto &rhs) {
        return !multi_tag_opt_.contains(rhs->PathId()) && rhs->PathId() != 9999;
      });
      path_configs.erase(it, path_configs.end());
    }
}

void MultiParamParse::CheckAndAdjustParam() {
  SetMultiParamMonitor();
  StatMachine();
  // Apply new config
  auto& cfg = session_data_->multi_path_config;
  for (auto &layer : cfg.GetAllLayerConfig()) {
    for (auto &path : layer.GetPathConfigs()) {
      ks::infra::PerfUtil::IntervalLogStash(
          1, "ad.shiba_sdk", "recall_conf_dump", path->TriggerName(), std::to_string(path->PathId()));
    }
  }
}

void MultiParamParse::StatMachine() {
  auto &multi_tag_opt = multi_tag_opt_;
  for (auto &&[tag_id, param] : multi_tag_opt) {
    session_data_->machine_stat.Stat(MachineStats::RECALL, tag_id, param->TriggerTokenMaxQuota(),
                                     session_data_->multi_param_manager.total_search_num);
  }
}

void MultiParamParse::SetMultiParamMonitor() {
  if (!AdKconfUtil::enableSetMultiParamMonitor()) {
    return;
  }
  const auto &multi_params = session_data_->multi_param_manager.multi_params;
  const auto &multi_tag_opt = multi_tag_opt_;
  // 打点统计
  // 每次 pv 请求多少路, 总 quota, 总 search_num
  int32_t total_multi_retr_count = 0, total_search_num = 0, total_quota = 0;
  for (auto &type_kv : multi_params) {
    int32_t total_search_num_by_type = 0, total_quota_type = 0;  // 分数据源
    for (auto &tag_id : type_kv.second) {
      auto iter = multi_tag_opt.find(tag_id);
      if (iter == multi_tag_opt.end()) {
        continue;
      }

      auto& path_config = *iter->second;

      session_data_->lite_dot->Interval(path_config.TriggerTokenMaxQuota(),
                                   "multi_retr_search_num", std::to_string(tag_id),
                                   path_config.cmd(),
                                   std::to_string(type_kv.first));
      session_data_->lite_dot->Interval(path_config.MergeCreativeMaxQuota(),
                                   "multi_retr_quota", std::to_string(tag_id),
                                   path_config.cmd(),
                                   std::to_string(type_kv.first));
      total_quota_type += path_config.MergeCreativeMaxQuota();
      total_search_num_by_type += path_config.TriggerTokenMaxQuota();
    }

    // 每个数据源 有 多少路 ,search_num ,quota 打点
    session_data_->lite_dot->Interval(type_kv.second.size(), "pv_multi_retr_count",
                                      std::to_string(type_kv.first));
    session_data_->lite_dot->Interval(total_search_num_by_type, "pv_multi_retr_search_num",
                                      std::to_string(type_kv.first));
    session_data_->lite_dot->Interval(total_quota_type, "pv_multi_retr_quota", std::to_string(type_kv.first));
    total_multi_retr_count += type_kv.second.size();
    total_search_num += total_search_num_by_type;
    total_quota += total_quota_type;
  }
  // 总路数，总 search_num, 总 quota
  session_data_->lite_dot->Interval(total_multi_retr_count, "pv_total_multi_retr_count");
  session_data_->lite_dot->Interval(total_search_num, "pv_total_multi_retr_search_num");
  session_data_->lite_dot->Interval(total_quota, "pv_total_multi_retr_quota");
}

// 异常处理 wait
void MultiParamParse::QuitRelease() {
  if (!session_data_->wait_once.test_and_set(std::memory_order_acquire)) {
    session_data_->invert_ready_promise.set_value(true);
    if (session_data_->tsm_state) {
      session_data_->graph_driver.Wait();
    }
  }
  return;
}

void MultiParamParse::UniverseSetSceneIdSet(std::set<int32_t>& scene_id_set) const {
  scene_id_set.clear();

  int32_t ad_style = (session_data_->pos_manager_base.request_imp_infos.size() > 0)
                         ? session_data_->pos_manager_base.request_imp_infos[0].ad_style
                         : 0;

  if (is_universe_tiny_deploy_) {
    if (ad_style == 18) {
      scene_id_set.insert(2002);
    } else {
      scene_id_set.insert(2001);
    }
    return;
  }
  if (session_data_->sub_page_id == 19000003) {
    scene_id_set.insert(1005);
    return;
  }
  if (SPDM_enable_universe_huge_migrate_tsm_fix(session_data_->spdm_ctx)) {
    if (SPDM_enable_universe_tx_scene_v2(session_data_->spdm_ctx) &&
      universe_tx_scene_pos_set_->count(session_data_->pos_manager_base.GetMediumPosId()) > 0) {
        scene_id_set.insert(1004);
    } else {
      if (!session_data_->universe_ks_install_info.is_request_model) {
      scene_id_set.insert(1001);
      } else {
        if (universe_inner_shallow_scene_pos_set_->count(
              session_data_->pos_manager_base.GetMediumPosId()) > 0) {
          scene_id_set.insert(1003);
        } else {
          scene_id_set.insert(1002);
        }
      }
    }
  } else {
    if (session_data_->ad_request->ad_user_info().is_universe_fake_user()) {
      scene_id_set.insert(1001);
    } else {
      if (universe_inner_shallow_scene_pos_set_->count(
            session_data_->pos_manager_base.GetMediumPosId()) > 0) {
        scene_id_set.insert(1003);
      } else {
        scene_id_set.insert(1002);
      }
    }
  }
}

}  // namespace ad_target
}  // namespace ks
