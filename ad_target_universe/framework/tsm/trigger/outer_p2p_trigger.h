#pragma once

#include <string>
#include <vector>
#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"

#include "teams/ad/ad_target_universe/strategy/strategy.h"
#include "teams/ad/ad_target_universe/framework/tsm/op_base.h"

namespace ks {

namespace ad_target {
class OuterP2pTrigger : public ks::ad_target::tsm::OpBase {
 public:
  OuterP2pTrigger();

  void ResultToTokens();

 public:
  // 统一架构融合
  // 适配 op_base.h
  bool Init(ContextData *, const std::vector<tsm::PathContext *> *);
  bool Run();
  bool Wait();

 private:
  void UniverseInit();

  absl::flat_hash_map<int32_t, std::function<void(tsm::PathContext*)>> func_map_;

  void AnalyzeP2pDataByMediumPostCpm(tsm::PathContext* path);
  void AnalyzeP2pDataByInnerUniverseShorvideoPosteriori(tsm::PathContext* path);
  void AnalyzeP2pDataByInnerUniverseShortvideoPhoto(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseLive(tsm::PathContext* path);

  void AnalyzeP2pDataByUniverseQueryExact(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseQueryCategory(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseQueryText(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseQueryTextLLM(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseSuggestPackage(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseHistoryQuery(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseQueryPackage(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseSugHanzi(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseSugPinying(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseSug_PinyingFC(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseSug_A_2_B(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseQueryIntervene(tsm::PathContext* path);
  void AnalyzeP2pDataByInnerUniverseLiveReserveRetr(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseOuterIndustryStrategy(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseCidStrategyNew(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseCidStrategyUpdate(tsm::PathContext* path);
  void AnalyzeIndexDataByTinyConfPackage(tsm::PathContext* path);
  void AnalyzeP2pDataByUniverseColdStartPhotoRecall(tsm::PathContext* path);
};
}  // namespace ad_target
}  // namespace ks
