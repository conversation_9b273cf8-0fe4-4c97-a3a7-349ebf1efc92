#include "teams/ad/ad_target_universe/framework/tsm/trigger/outer_p2p_trigger.h"

#include <algorithm>
#include <cmath>
#include <functional>
#include <unordered_map>
#include <vector>
#include <set>

#include "abseil/absl/strings/ascii.h"

#include "teams/ad/engine_base/search/util/string/query_string_normalize.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "teams/ad/ad_base/src/pb_helper/pb_helper.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_base.pb.h"
#include "teams/ad/ad_target_universe/universe/bg_task/ad_universe_medium_post_cpm.h"
#include "teams/ad/ad_target_universe/utils/kconf/kconf.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/ad_base/src/redis/redis_util.h"
#include "teams/ad/ad_target_universe/universe/bg_task/universe_direct_live_ads_v2.h"
#include "teams/ad/ad_target_universe/universe/bg_task/universe_outer_industry_strategy_p2p.h"
#include "teams/ad/ad_target_universe/universe/bg_task/universe_query_recall.h"
#include "teams/ad/universe_base/prerank/utils/p2p/universe_query_recall_new.h"
#include "teams/ad/ad_target_universe/universe/bg_task/universe_cid_strategy_new_p2p.h"
#include "teams/ad/ad_target_universe/universe/bg_task/universe_cid_strategy_update_p2p.h"
#include "teams/ad/ad_target_universe/universe/bg_task/universe_cold_start_photo_recall_p2p.h"
#include "teams/ad/universe_base/prerank/utils/p2p/universe_query_llm_recall.h"
#include "teams/ad/prefix_engine/service/app_name_prefix_engine.h"
#include "teams/ad/ad_target_universe/common/ad_table_migrage.h"

using kuaishou::ad::algorithm::UserRetrievalTagInfo;

#define UNIVERSE_TINY_RECALL_TEMPLATE(tag_id)                                                            \
  {                                                                                                      \
    if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {                                             \
      return;                                                                                            \
    }                                                                                                    \
                                                                                                         \
    auto& token_list = path->token_list;                                                                 \
    token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());                                    \
                                                                                                         \
    const auto tiny_search_recall_ocpx_type_set = AdKconfUtil::tinySearchRecallOcpxTypeSet();            \
    if (tiny_search_recall_ocpx_type_set == nullptr) {                                                   \
      return;                                                                                            \
    }                                                                                                    \
                                                                                                         \
    if (session_data_->universe_tiny_query_recall_data_worker.valid()) {                                 \
      auto latency = ad_base::TaskGroup::Wait(session_data_->universe_tiny_query_recall_data_worker);    \
      session_data_->dot->Interval(latency, "universe_tiny_query_recall_data_worker_cost",               \
                                   absl::StrCat(tag_id));                                                \
    }                                                                                                    \
                                                                                                         \
    for (const auto& package_id :                                                                        \
         session_data_->universe_tiny_query_recall_data.GetRecallData(path->GetPathConfig().PathId())) { \
      auto query_type = session_data_->universe_tiny_query_recall_data.GetQueryType(package_id);         \
      if (query_type == 0) {                                                                             \
        session_data_->dot->Interval(1, "universe_tiny_diversity_optimize", "recall_query_type_miss",    \
                                     absl::StrCat(path->GetPathConfig().PathId()));                      \
      }                                                                                                  \
      for (const auto& ocpx_action_type : *tiny_search_recall_ocpx_type_set) {                           \
        token_list.emplace_back(package_id, query_type);                                                 \
        auto& token = token_list.back();                                                                 \
        token.token.subtoken_2 = ocpx_action_type;                                                       \
        session_data_->universe_query_stat.Stat(engine_base::UniverseQueryStatCommon::RECALL,            \
                                                path->GetPathConfig().PathId(), query_type, package_id); \
        if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {                         \
          break;                                                                                         \
        }                                                                                                \
      }                                                                                                  \
    }                                                                                                    \
  }

namespace ks {
namespace ad_target {

void OuterP2pTrigger::UniverseInit() {
  func_map_.insert(
      {+multi_retr::RetrievalTag::MEDIUM_POST_CPM,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByMediumPostCpm, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_INNER_LOOP_DIRECTlIVE_CHANNEL,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseLive, this, std::placeholders::_1)});

  func_map_.insert({+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_DISTRIBUTE_NEW,
      std::bind(&OuterP2pTrigger::AnalyzeIndexDataByTinyConfPackage,
          this,
          std::placeholders::_1)});

  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_EXACT,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryExact, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_CATEGORY,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryCategory, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_TEXT,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryText, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_USER_QUERY_HISTORY_LONGER,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseHistoryQuery, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_PACKAGE,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryPackage, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_QUERY_TEXT_LLM,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryTextLLM, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_A,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSuggestPackage, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_B,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSuggestPackage, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_C,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSuggestPackage, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_D,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSuggestPackage, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_E,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSuggestPackage, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PACKAGE_F,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSuggestPackage, this, std::placeholders::_1)});

  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_HANZI,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSugHanzi, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PINYING,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSugPinying, this, std::placeholders::_1)});
  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_PINYING_F_C,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSug_PinyingFC, this, std::placeholders::_1)});

  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_SUG_A_2_B,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseSug_A_2_B, this, std::placeholders::_1)});

  func_map_.insert(
      {+multi_retr::RetrievalTag::UNIVERSE_TINY_PACKAGE_INTERVENE,
       std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryIntervene, this, std::placeholders::_1)});
  func_map_.insert({+multi_retr::RetrievalTag::RETRIEVAL_UNIVERSE_SHORTVIDEO_POSTERIORI_REVALL,
                    std::bind(&OuterP2pTrigger::AnalyzeP2pDataByInnerUniverseShorvideoPosteriori,
                              this,
                              std::placeholders::_1)});
  func_map_.insert({+multi_retr::RetrievalTag::UNIVERSE_INNER_LIVE_RESERVE_RETR,
                    std::bind(&OuterP2pTrigger::AnalyzeP2pDataByInnerUniverseLiveReserveRetr,
                              this,
                              std::placeholders::_1)});
  func_map_.insert({+multi_retr::RetrievalTag::UNIVERSE_OUTER_INDUSTRY_STRATEGY,
                    std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseOuterIndustryStrategy,
                              this,
                              std::placeholders::_1)});
  func_map_.insert({+multi_retr::RetrievalTag::UNIVERSE_CID_STRATEGY,
                    std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseCidStrategyNew,
                              this,
                              std::placeholders::_1)});
  func_map_.insert({+multi_retr::RetrievalTag::UNIVERSE_CID_STRATEGY_UPDATE,
                    std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseCidStrategyUpdate,
                              this,
                              std::placeholders::_1)});
  func_map_.insert({+multi_retr::RetrievalTag::UNIVERSE_SHORTVIDEO_PHOTO_POSTER_RETR,
                    std::bind(&OuterP2pTrigger::AnalyzeP2pDataByInnerUniverseShortvideoPhoto,
                              this,
                              std::placeholders::_1)});
  func_map_.insert({+multi_retr::RetrievalTag::UNIVERSE_COLD_START_PHOTO_RECALL,
                    std::bind(&OuterP2pTrigger::AnalyzeP2pDataByUniverseColdStartPhotoRecall,
                              this,
                              std::placeholders::_1)});
}

void OuterP2pTrigger::AnalyzeP2pDataByMediumPostCpm(tsm::PathContext* path) {
  std::string universe_post_cpm_prefix_key = SPDM_universe_post_cpm_prefix_key(session_data_->spdm_ctx);
  std::string prefix = universe_post_cpm_prefix_key.empty() ? "v0" : universe_post_cpm_prefix_key;
  std::vector<AdUniverseMediumPostCpmItem> retr_items;
  if (prefix == "v0" || prefix == "v1") {
    int32_t ad_style = (session_data_->pos_manager_base.request_imp_infos.size() > 0)
                          ? session_data_->pos_manager_base.request_imp_infos[0].ad_style
                          : -1;
    int64 medium_uid = session_data_->medium_uid;
    int64 second_industry_id = session_data_->pos_manager_base.GetMediumSubIndustryId();

    std::string medium_key = absl::StrCat(prefix, "_", ad_style, "_", medium_uid);
    std::string industry_key = absl::StrCat(prefix, "_", ad_style, "_", second_industry_id);

    if (AdUniverseMediumPostCpmDataCache::GetInstance()
            ->GetAdUniverseMediumPostCpmData(medium_key, &retr_items)) {
      LOG_EVERY_N(INFO, 10000) << "AdUniverseMediumPostCpmDataCache get cache success, medium_key: "
                              << medium_key << ", retr_items_size: " << retr_items.size();
    }
    if (AdUniverseMediumPostCpmDataCache::GetInstance()
            ->GetAdUniverseMediumPostCpmData(industry_key, &retr_items)) {
      LOG_EVERY_N(INFO, 10000) << "AdUniverseMediumPostCpmDataCache get cache success, industry_key: "
                              << industry_key << ", retr_items_size: " << retr_items.size();
    }
  } else {
    auto universe_post_cpm_key_map = AdKconfUtil::universePostCpmKey();
    if (universe_post_cpm_key_map == nullptr) {
      return;
    }
    for (const auto& dim : absl::StrSplit(prefix, '|')) {
      auto iter = universe_post_cpm_key_map->find(dim);
      if (iter == universe_post_cpm_key_map->end()) {
        continue;
      }
      if (retr_items.empty()) {
        std::string value;
        if (iter->second == "media_app_id") {
          value = session_data_->pos_manager_base.GetRequestAppId();
        } else if (iter->second == "pos_id") {
          value = session_data_->pos_manager_base.GetMediumPosId();
        } else {
          continue;
        }
        std::string query_key = absl::StrCat(dim, "_", value);
        VLOG(2) << "query_key: " << query_key;
        if (AdUniverseMediumPostCpmDataCache::GetInstance()
              ->GetAdUniverseMediumPostCpmData(query_key, &retr_items)) {
          LOG_EVERY_N(INFO, 10000) << "AdUniverseMediumPostCpmDataCache get cache success, query_key: "
                                  << query_key << ", retr_items_size: " << retr_items.size();
        }
      }
    }
    if (retr_items.empty() && SPDM_enable_universe_all_post_cpm(session_data_->spdm_ctx)
          && AdUniverseMediumPostCpmDataCache::GetInstance()
              ->GetAdUniverseMediumPostCpmData("all_post_cpm", &retr_items)) {
      LOG_EVERY_N(INFO, 10000) << "AdUniverseMediumPostCpmDataCache get cache success, query_key: "
                              << "all_post_cpm" << ", retr_items_size: " << retr_items.size();
    }
  }

  if (retr_items.empty()) {
    return;
  }

  int64_t i = 0;
  auto &token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());
  for (const auto &item : retr_items) {
    token_list.emplace_back(item.creative_id, item.score);
    if (++i >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseSugHanzi(tsm::PathContext* path) {
  UNIVERSE_TINY_RECALL_TEMPLATE(path->GetPathConfig().PathId());
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseSugPinying(tsm::PathContext* path) {
  UNIVERSE_TINY_RECALL_TEMPLATE(path->GetPathConfig().PathId());
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseSug_PinyingFC(tsm::PathContext* path) {
  UNIVERSE_TINY_RECALL_TEMPLATE(path->GetPathConfig().PathId());
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseSug_A_2_B(tsm::PathContext* path) {
  UNIVERSE_TINY_RECALL_TEMPLATE(path->GetPathConfig().PathId());
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryIntervene(tsm::PathContext* path) {
  UNIVERSE_TINY_RECALL_TEMPLATE(path->GetPathConfig().PathId());
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryExact(tsm::PathContext* path) {
  UNIVERSE_TINY_RECALL_TEMPLATE(path->GetPathConfig().PathId());
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryCategory(tsm::PathContext* path) {
  UNIVERSE_TINY_RECALL_TEMPLATE(path->GetPathConfig().PathId());
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseHistoryQuery(tsm::PathContext* path) {
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }

  auto recall_data = UniverseQueryRecall::GetInstance()->GetData();
  if (!recall_data) return;
  const auto& user_info = session_data_->ad_request->ad_user_info();
  auto& token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());

  static const int32_t PRIORITY = 5;
  static ad_base::TargetKeyConvertor conv;
  auto priority_iter = recall_data->find(PRIORITY);
  if (priority_iter == recall_data->end()) {
    return;
  }

  for (const auto& query : user_info.universe_user_query_recalled_product_ids_longer()) {
    auto query_iter = priority_iter->second.find(query);
    if (query_iter == priority_iter->second.end()) {
      return;
    }

    for (const auto& item : query_iter->second) {
      int64_t package_id = conv(item);
      token_list.emplace_back(package_id, PRIORITY);
      session_data_->universe_query_stat.Stat(engine_base::UniverseQueryStatCommon::RECALL,
                                              path->GetPathConfig().PathId(), PRIORITY, package_id);
      if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
        break;
      }
    }
  }
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryText(tsm::PathContext* path) {
  auto search_auery = session_data_->ad_request->universe_ad_request_info().search_query();
  ignore_result(absl::StripAsciiWhitespace(search_auery));
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }

  auto recall_data = UniverseQueryRecall::GetInstance()->GetData();
  if (!recall_data) return;
  static const int32_t PRIORITY = 1;
  static ad_base::TargetKeyConvertor conv;
  auto priority_iter = recall_data->find(PRIORITY);
  if (priority_iter == recall_data->end()) {
    return;
  }
  auto query_iter = priority_iter->second.find(conv(search_auery));
  if (query_iter == priority_iter->second.end()) {
    return;
  }

  auto& token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());
  for (const auto& item : query_iter->second) {
    token_list.emplace_back(conv(item), PRIORITY);
    if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryTextLLM(tsm::PathContext* path) {
  auto search_query = session_data_->ad_request->universe_ad_request_info().search_query();
  ignore_result(absl::StripAsciiWhitespace(search_query));
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }

  auto recall_data = ks::universe_base::prerank::UniverseQueryLLMRecall::GetInstance()->GetData();
  if (!recall_data) return;
  static ad_base::TargetKeyConvertor conv;
  auto recall_exp_group = session_data_->session_context->TryGetString("universe_llm_recall_group",
    "base");
  std::string query_key = absl::StrCat(recall_exp_group, "@", search_query);
  auto query_iter = recall_data->find(conv(query_key));
  if (query_iter == recall_data->end()) {
    return;
  }

  auto &invert_list = query_iter->second;
  auto& token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());
  for (auto &[query_type, tokens] : invert_list) {
    for (const auto& item : tokens) {
      token_list.emplace_back(item, query_type);
      if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
        break;
      }
    }
  }
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseSuggestPackage(tsm::PathContext* path) {
  if (path == nullptr || session_data_ == nullptr
  || path->GetPathConfig().TriggerTokenMaxQuota() <= 0
  || !SPDM_enable_universe_tiny_search_package_flow(session_data_->spdm_ctx)
  || !SPDM_enable_universe_tiny_search_recall_diverse_by_index(session_data_->spdm_ctx)) {
        return;
  }
  int32_t path_id = path->GetPathConfig().PathId();
  auto it = session_data_->path_id_2_pkg_and_pos_list.find(path_id);
  if (it == session_data_->path_id_2_pkg_and_pos_list.end()) {
    return;
  }
  static ad_base::TargetKeyConvertor conv;
  auto& token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());
  for (const auto& [pkg_name, pos] : it->second) {
    token_list.emplace_back(conv(pkg_name), pos);
    if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseLive(tsm::PathContext* path) {
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }

  auto &token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());
  // 头部商家召回开关
  if (SPDM_enable_universe_top_app_cost_unit_recall(session_data_->spdm_ctx)) {
    GetCreativeByLastCostIndex(session_data_,
        &path->token_list, path->GetPathConfig().TriggerTokenMaxQuota());
  } else {
    if (UniverseDirectLiveAdsV2::GetInstance() == nullptr) {
      LOG_EVERY_N(WARNING, 1000) << "UniverseDirectLiveAdsV2 GetInstance error";
      return;
    }
    auto direct_live_ads_data = UniverseDirectLiveAdsV2::GetInstance()->GetData();
    if (direct_live_ads_data.get() == nullptr) {
      LOG_EVERY_N(WARNING, 1000) << "UniverseDirectLiveAdsV2 get data error";
      return;
    }
    // 利用，使用后验数据顺序加入广告
    const auto &retr_items = *(direct_live_ads_data.get());
    for (const auto &item : retr_items) {
      token_list.emplace_back(item.first, item.second);
      if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
        break;
      }
    }
  }
  return;
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseOuterIndustryStrategy(tsm::PathContext* path) {
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }
  auto industry_ad_data = UniverseOuterIndustryStrategyP2P::GetInstance()->GetData();
  if (!industry_ad_data.get()) {
    LOG_EVERY_N(WARNING, 1000) << "UniverseOuterIndustryStrategyP2P get data error";
    return;
  }
  auto &token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());

  // 利用，使用后验数据顺序加入广告
  const auto &retr_items = *(industry_ad_data.get());
  for (const auto &item : retr_items) {
    token_list.emplace_back(item.first, item.second);
    if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
  return;
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseCidStrategyNew(tsm::PathContext* path) {
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }
  auto industry_ad_data = UniverseCidStrategyNewP2P::GetInstance()->GetData();
  if (!industry_ad_data.get()) {
    LOG_EVERY_N(WARNING, 1000) << "UniverseCidStrategyNewP2P get data error";
    return;
  }
  auto &token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());

  // 利用，使用后验数据顺序加入广告
  const auto &retr_items = *(industry_ad_data.get());
  for (const auto &item : retr_items) {
    token_list.emplace_back(item.first, item.second);
    if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
  return;
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseCidStrategyUpdate(tsm::PathContext* path) {
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }
  auto industry_ad_data = UniverseCidStrategyUpdateP2P::GetInstance()->GetData();
  if (!industry_ad_data.get()) {
    LOG_EVERY_N(WARNING, 1000) << "UniverseCidStrategyUpdateP2P get data error";
    return;
  }
  auto &token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());

  // 利用，使用后验数据顺序加入广告
  const auto &retr_items = *(industry_ad_data.get());
  for (const auto &item : retr_items) {
    token_list.emplace_back(item.first, item.second);
    if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
  return;
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseColdStartPhotoRecall(tsm::PathContext* path) {
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }
  auto industry_ad_data = UniverseColdStartPhotoRecallP2P::GetInstance()->GetData();
  if (!industry_ad_data.get()) {
    LOG_EVERY_N(WARNING, 1000) << "UniverseColdeStartRecallP2P get data error";
    return;
  }
  auto &token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());
  const auto &retr_items = *(industry_ad_data.get());
  for (const auto &item : retr_items) {
    token_list.emplace_back(item.first, item.second);
    if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
  return;
}

void OuterP2pTrigger::AnalyzeP2pDataByUniverseQueryPackage(tsm::PathContext* path) {
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }
  if (session_data_->ad_request->universe_ad_request_info().imp_info_size() == 0) {
    return;
  }

  const auto& imp_info = session_data_->ad_request->universe_ad_request_info().imp_info(0);

  static ad_base::TargetKeyConvertor conv;
  auto& token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());

  for (const auto& item : imp_info.pkg_white_list()) {
    token_list.emplace_back(conv(item), 4);
    if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
  for (const auto& item : imp_info.suggest_pkg_white_list()) {
    token_list.emplace_back(conv(item), 4);
    if (token_list.size() >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
}

void OuterP2pTrigger::AnalyzeIndexDataByTinyConfPackage(tsm::PathContext* path) {
  if (path->GetPathConfig().TriggerTokenMaxQuota() <= 0) {
    return;
  }
  int32_t ad_style = session_data_->pos_manager_base.request_imp_infos.empty() ? 0
                     : session_data_->pos_manager_base.request_imp_infos[0].ad_style;
  // 遍历 kconf 展开创意到候选集
  static ad_base::TargetKeyConvertor conv;
  const std::string& search_query = session_data_->ad_request->universe_ad_request_info().search_query();
  ignore_result(absl::StripAsciiWhitespace(search_query));
  auto recall_data = ks::universe_base::prerank::UniverseQueryRecallNew::GetInstance()->GetData();
  if (!recall_data) return;
  auto package_id2_priorty = recall_data->find(conv(search_query));

  auto& token_list = path->token_list;
  token_list.reserve(path->GetPathConfig().TriggerTokenMaxQuota());
  int64_t i = 0;
  auto& hash_keys = AdKconfUtil::universeOcpcToPackages()->data().GetHashKeyList();
  // package_id list * ocpx_action_type
  for (auto & [ pkg_id, ocpx_action_type ] : hash_keys) {
    int32_t query_type = 0;
    if (package_id2_priorty != recall_data->end()) {
      auto iter = package_id2_priorty->second.find(pkg_id);
      if (iter != package_id2_priorty->second.end()) {
        query_type = iter->second;
      }
    }

    token_list.emplace_back(pkg_id, query_type);
    auto& token = token_list.back();
    token.token.subtoken_2 = ocpx_action_type;
    if (ad_style == 18 || ad_style == 22) {
      session_data_->universe_query_stat.Stat(engine_base::UniverseQueryStatCommon::RECALL,
                                              path->GetPathConfig().PathId(), query_type, pkg_id);
    }

    if (++i >= path->GetPathConfig().TriggerTokenMaxQuota()) {
      break;
    }
  }
}

}  // namespace ad_target
}  // namespace ks

#undef UNIVERSE_TINY_RECALL_TEMPLATE
