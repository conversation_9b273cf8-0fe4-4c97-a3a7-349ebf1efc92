#include "teams/ad/ad_target_universe/outer/strategy/rule/filter_universe_outer_loop.h"

#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/ad_index/index/forward/ad_index.h"
#ifdef TARGET_UNIVERSE_SCHEMA_CUT
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/AdMagicSitePageDas.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Campaign.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Account.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Creative.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Unit.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/PhotoStatus.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/WTPhoto.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/WTUnit.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/WTAccount.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/AdAppRelease.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe_cut/Target.h"
#else
#include "teams/ad/ad_table/code_generator/ad_target_universe/AdMagicSitePageDas.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Campaign.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Account.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Creative.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Unit.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/PhotoStatus.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/WTPhoto.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/WTUnit.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/WTAccount.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/AdAppRelease.h"
#include "teams/ad/ad_table/code_generator/ad_target_universe/Target.h"
#endif
#include "teams/ad/ad_target_universe/universe/bg_task/universe_direct_picture_author_white_list.h"
#include "teams/ad/ad_target_universe/universe/bg_task/universe_fast_application_v2_data_for_block.h"
#include "teams/ad/ad_target_universe/common/context_data.h"
#include "teams/ad/ad_target_universe/utils/kconf/kconf.h"
#include "teams/ad/ad_target_universe/utils/perf/perf_util.h"
#include "teams/ad/ad_target_universe/utils/spdm/spdm_switches.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/ad_base/src/common/scoped_guard.h"
#include "teams/ad/ad_proto/kuaishou/ad/universe/universe_product_freq_data.pb.h"
#include "teams/ad/ad_index/index/utils/ad_table_compatible.h"
#include "teams/ad/ad_index/index/utils/kconf/kconf_util.h"
#include "teams/ad/ad_target_universe/utils/table_extension/table_wrapper.h"
#include "teams/ad/ad_target_universe/utils/common/ad_table_util.h"

DECLARE_bool(is_universe_tiny_deploy);

namespace ks {
namespace ad_target {

BETTER_ENUM(AdType, int32_t, dsp = 0, search)

// UniverseOrientLabelSheildFilter
UniverseOrientLabelSheildFilter::UniverseOrientLabelSheildFilter(ContextData* context, size_t worker_nums)
    : counter_(worker_nums) {
  Initialize(context, worker_nums);
  if (session_data_->operation_orient_label_sheild_config == nullptr) {
    return;
  }

  auto iter = session_data_->operation_orient_label_sheild_config->find(0);
  if (iter == session_data_->operation_orient_label_sheild_config->end()) {
    return;
  }
  int64_t app_id;
  if (!absl::SimpleAtoi(session_data_->pos_manager_base.GetRequestAppId(), &app_id)) {
    app_id = 0;
  }
  const auto pos_id = session_data_->pos_manager_base.GetMediumPosId();
  iter->second.GetOrientLabel(session_data_->medium_uid, session_data_->pos_manager_base.GetRequestAppId(),
                              pos_id, ols_block_item_);

  universe_shield_fast_application_config_ = AdKconfUtil::universeShieldFastApplicationConfig();
}

bool UniverseOrientLabelSheildFilter::PickUp() const {
  if (ols_block_item_.shield_equity_card == false && ols_block_item_.shield_sex == false &&
      ols_block_item_.shield_fast_application == false &&
      (!SPDM_universeSheildFilterWithFastapplicationv2() ||
      ols_block_item_.shield_fast_application_v2 == false)) {
    return false;
  }
  return true;
}

RuleStatus UniverseOrientLabelSheildFilter::DoRule(RetrievalAdCommon& ad, size_t task_no) const {
  if (ad.base.p_account == nullptr) {
    return RuleStatus::OK;
  }

  //  白名单逻辑
  auto* account = ad.base.p_account;
  if (ols_block_item_.ad_account_ids.count(account->id()) > 0 ||
      ols_block_item_.city_product_ids.count(account->city_product_id()) > 0) {
    ++counter_[task_no % counter_.size()].hit_white_list_count;
    return RuleStatus::OK;
  }
  int64_t sec_ind = ad.base.p_account->second_industry_id_v5_1();
  int64_t sec_ind_v63 = ad.base.p_account->second_industry_id_v6_3();
  // 屏蔽权益卡
  // cid 跳过权益卡屏蔽
  bool cid_skip_shield_equity_card = ad.ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT;
  if (AdKconfUtil::shieldEquityCardSecIndV63Set()) {
    const auto& shield_equity_card_sec_ind_v63_set = AdKconfUtil::shieldEquityCardSecIndV63Set();
    if (ols_block_item_.shield_equity_card &&
        (ad.campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE ||
        ad.campaign_type() == kuaishou::ad::AdEnum::AD_CID ||
        ad.campaign_type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) &&
        shield_equity_card_sec_ind_v63_set->count(sec_ind_v63) == 0 &&
        !cid_skip_shield_equity_card) {
      ++counter_[task_no % counter_.size()].filter_count;
      RULE_FILTER_RETURN_WITH_REASON(
          kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_OPERATION_ORIENT_LABEL_SHEILD_FILTER);
    }
  }

  // 屏蔽涉黄
  if (ols_block_item_.shield_sex && sec_ind == 2002 && ad.ocpx_action_type() == kuaishou::ad::AD_PURCHASE) {
    ++counter_[task_no % counter_.size()].filter_count;
    RULE_FILTER_RETURN_WITH_REASON(
        kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_OPERATION_ORIENT_LABEL_SHEILD_FILTER);
  }

  // 屏蔽快应用
  if (universe_shield_fast_application_config_ != nullptr && ols_block_item_.shield_fast_application) {
    const AdMagicSitePageDas* p_ad_magicsite_page_das = ad.base.p_magic_site_page;
    if (p_ad_magicsite_page_das != nullptr &&
        !p_ad_magicsite_page_das->type_list().empty() &&
        !p_ad_magicsite_page_das->sub_type_list().empty() &&
        p_ad_magicsite_page_das->type_list().size() == p_ad_magicsite_page_das->sub_type_list().size()) {
      for (int i = 0; i < p_ad_magicsite_page_das->type_list().size() && i < 100; ++i) {
        if (universe_shield_fast_application_config_->data().IsHit(
                p_ad_magicsite_page_das->type_list().at(i), p_ad_magicsite_page_das->sub_type_list().at(i))) {
          ++counter_[task_no % counter_.size()].filter_count;
          RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::
                                             UNIVERSE_OPERATION_ORIENT_LABEL_SHEILD_FAST_APPLICATION_FILTER);
        }
      }
    }
  }

  // 屏蔽快应用（新） 和上边那个快应用不一样；
  const auto& fast_app_white_product_ids = ad_server::AdIndexKconf::universeFastAppProductWhiteList()
                                              ->data().GetProductIds();
  if (SPDM_universeSheildFilterWithFastapplicationv2()) {
    auto* p2p_config = GetUniverserfastapplicationv2dataforblockContainer();
    auto fast_application_v2_data_for_block = p2p_config ? p2p_config->GetData() : nullptr;
    // 有数据 且 屏蔽标签 且  是需要过滤的 cityproduct
    if (fast_application_v2_data_for_block && ols_block_item_.shield_fast_application_v2
        && fast_application_v2_data_for_block->find(ad.city_product_id())
            != fast_application_v2_data_for_block->end()
        && fast_app_white_product_ids.count(ad.city_product_id()) == 0) {
      ++counter_[task_no % counter_.size()].filter_count;
      ++counter_[task_no % counter_.size()].fast_appv2_conut;
      RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::
      UNIVERSE_OPERATION_ORIENT_LABEL_SHEILD_FAST_APPLICATION_V2_FILTER);
    }
  }
  return RuleStatus::OK;
}

void UniverseOrientLabelSheildFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_orient_label_sheild_filter", s_counter.filter_count);
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_orient_label_sheild_hit_white_list_count",
                     s_counter.hit_white_list_count);
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_orient_label_sheild_filter_hit_fastAppv2",
                      s_counter.fast_appv2_conut);
}

// UniverseInternalNonAdDarkUnitFilter
UniverseInternalNonAdDarkUnitFilter::UniverseInternalNonAdDarkUnitFilter(ContextData *context,
                                                                         size_t worker_nums)
    : counter_(worker_nums) {
  Initialize(context, worker_nums);
}
bool UniverseInternalNonAdDarkUnitFilter::PickUp() const {
  return true;
}
RuleStatus UniverseInternalNonAdDarkUnitFilter::DoRule(RetrievalAdCommon &ad, size_t task_no) const {
  if (!ad.is_universe_ads() && ad.agent_type() == 2) {
    ++counter_[task_no % counter_.size()].filter_count;
    RULE_FILTER_RETURN_WITH_REASON(
        kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_DARK_NON_AD_INTERNAL_AGENT_FILTER);
  }

  return RuleStatus::OK;
}
void UniverseInternalNonAdDarkUnitFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter &c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_dark_internal_non_ad_filter", s_counter.filter_count);
}

// UniverseAggregatePageFilter
UniverseAggregatePageFilter::UniverseAggregatePageFilter(ContextData *context, size_t worker_nums)
    : counter_(worker_nums) {
  Initialize(context, worker_nums);
  aggregate_page_config = std::make_shared<UnionMidPageWhiteConfig>(
      AdKconfUtil::aggregatePageProductNameAccountWhiteList()->data());
}

bool UniverseAggregatePageFilter::PickUp() const {
  return session_data_->ad_request->universe_ad_request_info().universe_task_type() == 3;
}

RuleStatus UniverseAggregatePageFilter::DoRule(RetrievalAdCommon &ad, size_t task_no) const {
  auto& product_white_set = aggregate_page_config->product_white_set_;
  auto& account_white_set = aggregate_page_config->account_white_set_;
  // 聚合中间页二次请求非配置预算都过滤掉
  if (product_white_set.count(ad.city_product_id()) <= 0 && account_white_set.count(ad.account_id()) <= 0) {
    RULE_FILTER_RETURN_WITH_REASON(
        kuaishou::log::ad::AdTraceFilterCondition::AGGREGATE_PAGE_WHITE_FILTER);
  }

  return RuleStatus::OK;
}

void UniverseAggregatePageFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter &c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_aggregate_page_filter", s_counter.filter_count);
}

// UniverseFansLiveFilter
UniverseFansLiveFilter::UniverseFansLiveFilter(ContextData *context, size_t worker_nums)
    : counter_(worker_nums) {
  Initialize(context, worker_nums);
  const std::string& sdk_version = context->ad_request->universe_ad_request_info().sdk_version();
  int32_t sdk_type = context->ad_request->universe_ad_request_info().sdk_type();
  bool sdk_ver_admit =
      ((sdk_type == 1 && engine_base::CompareAppVersion(sdk_version, "3.3.34") >= 0) ||
       (sdk_type == 2 && engine_base::CompareAppVersion(sdk_version, "3.3.33") >= 0));
  int32_t ad_style = context->pos_manager_base.request_imp_infos.empty()
                 ? 0 : session_data_->pos_manager_base.request_imp_infos[0].ad_style;
  // 版本控制 && 直播开关 && 激励全屏
  enable_universe_outer_origin_live = context->ad_request->universe_live_support_mode() == 1 &&
                                      sdk_ver_admit && (ad_style == 2 || ad_style == 3);
  origin_live_white_authors = AdKconfUtil::livingAuthorWhiteList();
  int64_t pos_id = context->pos_manager_base.request_imp_infos.empty()
               ? 0
               : session_data_->pos_manager_base.request_imp_infos[0].pos_id;
  // 获取渲染类型
  int32_t render_type = context->ad_request->universe_ad_request_info().imp_info_size() > 0
                 ? context->ad_request->universe_ad_request_info().imp_info(0).render_type() : 0;
  // app_id
  const std::string& app_id = context->pos_manager_base.GetRequestAppId();
  // 信息流准入
  // 1 版本判断
  bool inner_live_feed_sdk_admit =
        (sdk_type == 1 && engine_base::CompareAppVersion(sdk_version, "3.3.36") >= 0) ||
        (sdk_type == 2 && engine_base::CompareAppVersion(sdk_version, "3.3.33") >= 0);
  // 2 获取模板列表
  std::vector<int32_t> pos_template_ids;
  if (context->ad_request->universe_ad_request_info().imp_info_size() > 0) {
    const auto& imp_info = context->ad_request->universe_ad_request_info().imp_info(0);
    for (const auto& id : imp_info.template_ids()) {
      pos_template_ids.emplace_back(id);
    }
    if (imp_info.template_ids().empty()) {
      pos_template_ids.emplace_back(imp_info.template_id());
    }
  }
  // 3 自渲染限定素材类型 1 自渲染 2 模板渲染
  bool material_type_check = render_type == 2 || (render_type == 1 &&
                             (session_data_->IsPositionSupportVerticalPhoto() ||
                              session_data_->IsPositionSupportWidePhoto()));
  is_feed_support_outer_origin_live = ad_style == 1;
  // 插屏场景准入
  // 版本判断
  bool inner_live_interstitial_sdk_admit =
        (sdk_type == 1 && engine_base::CompareAppVersion(sdk_version, "3.3.37") >= 0) ||
        (sdk_type == 2 && engine_base::CompareAppVersion(sdk_version, "3.3.33") >= 0);
}

bool UniverseFansLiveFilter::PickUp() const {
  return true;
}
RuleStatus UniverseFansLiveFilter::DoRule(RetrievalAdCommon &ad, size_t task_no) const {
  if (ad.campaign_type() == 16 &&
      ad.base.p_creative->live_creative_type() == 3) {
    int64_t author_id = ad.base.p_unit->has_unit_support_info_optional() ?
                        ad.base.p_unit->live_user_id() : -1;
    bool ad_admit = (enable_universe_outer_origin_live ||
                     is_feed_support_outer_origin_live) &&
                    origin_live_white_authors && origin_live_white_authors->count(author_id);
    // 粉丝直播推广直播直投 白名单命中可出
    if (!ad_admit) {
      ++counter_[task_no % counter_.size()].filter_count;
      RULE_FILTER_RETURN_WITH_REASON(
          kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_FANS_LIVE_STREAM_FILTER);
    }
  }

  return RuleStatus::OK;
}
void UniverseFansLiveFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter &c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_fans_live_ad_filter", s_counter.filter_count);
}

// UniverseTinyInstalledAppFloorFilter
UniverseTinyInstalledAppFloorFilter::UniverseTinyInstalledAppFloorFilter(ContextData* context,
                                                                         size_t worker_nums)
    : counter_(worker_nums) {
  Initialize(context, worker_nums);

  int32_t ad_style = context->pos_manager_base.request_imp_infos.empty()
                         ? 0
                         : session_data_->pos_manager_base.request_imp_infos[0].ad_style;
  const std::string& app_id = context->pos_manager_base.GetRequestAppId();

  is_tiny_disable_logic_tf_ = (ad_style == 18 || ad_style == 19 || ad_style == 20 || ad_style == 21 ||
                               AdKconfUtil::universeTinyInstalledFilterAppIdList()->count(app_id));

  if (!is_tiny_disable_logic_tf_) {
    return;
  }
  static ad_base::TargetKeyConvertor conv_;
  for (const auto& item : context->app_set) { app_hash_set_.insert(conv_(item)); }
}
bool UniverseTinyInstalledAppFloorFilter::PickUp() const {
  return is_tiny_disable_logic_tf_ && !app_hash_set_.empty();
}
RuleStatus UniverseTinyInstalledAppFloorFilter::DoRule(RetrievalAdCommon& ad, size_t task_no) const {
  if (!is_tiny_disable_logic_tf_) {
    return RuleStatus::OK;
  }
  if (ad.campaign_type() != kuaishou::ad::AdEnum::APP) {  // 仅对拉新生效
    return RuleStatus::OK;
  }
  const auto package_name_id = ad.base.p_ad_app_release ? ad.base.p_ad_app_release->package_name_id()
                                                  : ad.base.p_unit->city_app_package_name_id();
  if (app_hash_set_.count(package_name_id)) {
    ++counter_[task_no % counter_.size()].filter_count;
    RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::APP_PACKAGE_ALREADY_INSTALLED);
  }

  return RuleStatus::OK;
}
void UniverseTinyInstalledAppFloorFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter &c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_tiny_installed_app_floor_filter", s_counter.filter_count);
}

UniverseNonAppIntroductionFilter::UniverseNonAppIntroductionFilter(ContextData *context,
                                                                 size_t worker_nums)
    : counter_(worker_nums) {
  Initialize(context, worker_nums);
}

bool UniverseNonAppIntroductionFilter::PickUp() const {
  int64_t pos_id = session_data_->pos_manager_base.request_imp_infos.empty()
               ? 0 : session_data_->pos_manager_base.request_imp_infos[0].pos_id;
  const std::string& app_id = session_data_->pos_manager_base.GetRequestAppId();
  const auto& media_uid = session_data_->medium_uid;
  return engine_base::AdKconfUtil::universeAppIntroductionConfig()->data()
           .IsHitBlackList(media_uid, app_id, pos_id);
}

RuleStatus UniverseNonAppIntroductionFilter::DoRule(RetrievalAdCommon &ad, size_t task_no) const {
  int64_t package_name_id = ad.base.p_ad_app_release ?
                            ad.base.p_ad_app_release->package_name_id() :
                            ad.base.p_unit->city_app_package_name_id();
  bool empty_in_index = true;
  if (ad.campaign_type() == kuaishou::ad::AdEnum::APP &&
      ad.base.p_ad_app_release && ad.base.p_ad_app_release->has_function_introduction()) {
    empty_in_index = false;
  }

  if ((ad.campaign_type() == kuaishou::ad::AdEnum::APP &&
      !engine_base::AdKconfUtil::universeAppIntroductionConfig()->data()
      .HaskconfIntroduction(package_name_id)) && empty_in_index) {
    ++counter_[task_no % counter_.size()].filter_count;
    RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::APP_PACKAGE_ALREADY_INSTALLED);
  }
  return RuleStatus::OK;
}

void UniverseNonAppIntroductionFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter &c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_non_app_introduction_filter", s_counter.filter_count);
}

UniverseAccountListFreqFilter::UniverseAccountListFreqFilter(ContextData *context,
                                                                 size_t worker_nums)
    : counter_(worker_nums) {
  Initialize(context, worker_nums);
  filter_stra_ids_.clear();
  // 获取用户账户列表频控数据
  std::string redis_freq_raw_str;
  std::string redis_freq_decoded_str;
  auto ret = session_data_->redis_result.freq_filter_account_list.Get(&redis_freq_raw_str);
  if (ret != ks::infra::KS_INF_REDIS_NO_ERROR && ret != ks::infra::KS_INF_REDIS_ERR_UNKNOWN) {
    LOG_EVERY_N(INFO, 1000) << "Failed to get user pb from redis, reason: " << ret;
    return;
  }
  if (redis_freq_raw_str.empty()) return;
  if (!base::Base64Decode(redis_freq_raw_str, &redis_freq_decoded_str)) {
    LOG_EVERY_N(INFO, 1000) << "Failed to decode string from redis adUniverseFreqData";
    return;
  }
  kuaishou::ad::universe::AccountListFreqData redis_freq_pb;
  if (!redis_freq_pb.ParseFromString(redis_freq_decoded_str)) {
    LOG_EVERY_N(INFO, 1000) << "Failed to parse decoded string to pb";
    return;
  }
  // // debug
  // auto* click_data = redis_freq_pb.mutable_click_data();
  // (*click_data)[1] = 2;
  // 当前 user 各策略点击数 如果比配置的高 说明需要过滤
  for (auto& [stra_id, click_num] : redis_freq_pb.click_data()) {
    int64_t click_thres = AdKconfUtil::universeAccountListFreqConfigs()->data()
                          .GetClickThreshold(stra_id);
    if (click_thres != -1 && click_num >= click_thres) {
      filter_stra_ids_.insert(stra_id);
    }
  }
}

// 判断当前 pv 是否有需要过滤的 accounts
bool UniverseAccountListFreqFilter::PickUp() const {
  return !filter_stra_ids_.empty();
}

RuleStatus UniverseAccountListFreqFilter::DoRule(RetrievalAdCommon &ad, size_t task_no) const {
  if (AdKconfUtil::universeAccountListFreqConfigs()->data().
                CheckAccountInvalid(ad.account_id(), filter_stra_ids_)) {
    ++counter_[task_no % counter_.size()].filter_count;
    RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_IMPR_FREQ_FILTER);
  }
  return RuleStatus::OK;
}

void UniverseAccountListFreqFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter &c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_account_list_freq_filter", s_counter.filter_count);
}

// UniverseOrientDeliveryMingtouFilter
UniverseOrientDeliveryMingtouFilter::UniverseOrientDeliveryMingtouFilter(ContextData* context,
                                                                         size_t worker_nums)
    : counter_(worker_nums) {
  Initialize(context, worker_nums);
  static ad_base::TargetKeyConvertor conv;
  pv_pos_id = session_data_->pos_manager_base.GetMediumPosId();
  pv_app_id_int64 = conv(session_data_->pos_manager_base.GetRequestAppId());
  static const absl::flat_hash_map<std::string, int64_t> platform_map = {
    {"android", 1},
    {"ios", 2},
    {"harmony", 4}
  };
  os_type = 0;
  const auto iter = platform_map.find(context->ad_request->ad_user_info().platform());
  if (iter != platform_map.end()) {
    os_type = iter->second;
  }
}

bool UniverseOrientDeliveryMingtouFilter::PickUp() const {
  return true;
}

RuleStatus UniverseOrientDeliveryMingtouFilter::DoRule(RetrievalAdCommon& ad, size_t task_no) const {
  if (ad.base.p_account == nullptr || ad.base.p_campaign == nullptr || ad.base.p_unit == nullptr) {
    return RuleStatus::OK;
  }

  if (session_data_->orient_delivery_mingtou_config == nullptr) {
    return RuleStatus::OK;
  }
  auto config_iter = session_data_->orient_delivery_mingtou_config->find(0);
  if (config_iter == session_data_->orient_delivery_mingtou_config->end()) {
    return RuleStatus::OK;
  }

  // 平台界面左侧字段
  const auto& account_id = ad.base.p_account->id();
  const auto& product_id = ad.base.p_account->city_product_id();
  const auto& second_industry_id = ad.base.p_account->industry_id_v3();
  const auto& first_industry_id_v6_3 = ad.base.p_account->first_industry_id_v6_3();
  const auto& second_industry_id_v6_3 = ad.base.p_account->second_industry_id_v6_3();

  // 平台界面右侧字段
  const auto& ocpx_action_type = ad.base.p_unit->ocpx_action_type();
  const auto& deep_conversion_type = ad.base.p_unit->deep_conversion_type();
  const auto& bid_type = ad.base.p_unit->bid_type();
  const auto& campaign_type = ad.base.p_campaign->type();

  bool result;
  int64_t ad_placement;
  ad_base::TargetKeyConvertor str_2_int64;
  const auto& user_orientation_set = session_data_->target_profiler.user_orientation_set;
  const int32_t ad_style = session_data_->pos_manager_base.request_imp_infos.empty()
                           ? 0
                           : session_data_->pos_manager_base.request_imp_infos[0].ad_style;
  static const auto dark = str_2_int64("pt_kwai");
  static const auto light = str_2_int64("pt_union");
  static const auto light_opt = str_2_int64("pt_opt");
  if (!UnitHasResourceId(ad.base.p_unit, 5)) {
    ad_placement = dark;
  } else if (UnitHasResourceId(ad.base.p_unit, 10)) {
    ad_placement = light_opt;
  } else {
    ad_placement = light;
  }
  result = config_iter->second.DoFilter(pv_app_id_int64,
                                        pv_pos_id,
                                        os_type,
                                        ad_style,
                                        user_orientation_set,
                                        account_id,
                                        product_id,
                                        second_industry_id,
                                        first_industry_id_v6_3,
                                        second_industry_id_v6_3,
                                        ocpx_action_type,
                                        deep_conversion_type,
                                        campaign_type,
                                        bid_type,
                                        ad_placement);
  if (result) {
    if (counter_.size() > 0) {
      ++counter_[task_no % counter_.size()].filter_count;
    }
    RULE_FILTER_RETURN_WITH_REASON(
        kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_DIRECT_AD_MEDIA_FILTER);
  }

  return RuleStatus::OK;
}

void UniverseOrientDeliveryMingtouFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_orient_delivery_mingtou_filter", s_counter.filter_count);
}

// UniverseRiskLabelFilter
UniverseRiskLabelFilter::UniverseRiskLabelFilter(ContextData* context,
                                                 size_t worker_nums) : counter_(worker_nums) {
  Initialize(context, worker_nums);

  blocked_categories_.clear();
  auto app_id = session_data_->pos_manager_base.GetRequestAppId();
  auto uid = session_data_->medium_uid;
  const auto& blocked_categories_config = AdKconfUtil::universeAuditBlockConfig()->data();
  blocked_categories_config.GetCategoriesByAppidUid(app_id, uid, blocked_categories_);

  risk_label_config_ =
    std::make_shared<UniverseCategoryToRiskLabels>(AdKconfUtil::universeCategoryToRiskLabels()->data());
}

bool UniverseRiskLabelFilter::PickUp() const {
  return !blocked_categories_.empty();
}

RuleStatus UniverseRiskLabelFilter::DoRule(RetrievalAdCommon& ad, size_t task_no) const {
  if (ad.base.p_creative == nullptr) {
    return RuleStatus::OK;
  }
  for (const auto risk_label : ad.base.p_creative->risk_labels()) {
    const auto& categories = risk_label_config_->GetCategoriesByRiskLabel(risk_label);
    for (const auto& category : categories) {
      if (blocked_categories_.count(category) > 0) {
        if (counter_.size() > 0) {
          ++counter_[task_no % counter_.size()].filter_count;
        }
        RULE_FILTER_RETURN_WITH_REASON(
            kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_RISK_LABEL_FILTER);
      }
    }
  }

  return RuleStatus::OK;
}

void UniverseRiskLabelFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_risk_label_filter", s_counter.filter_count);
}

// UniverseConvertedDataFilter
UniverseConvertedDataFilter::UniverseConvertedDataFilter(ContextData* context,
                                                 size_t worker_nums) : counter_(worker_nums) {
  Initialize(context, worker_nums);
  static ad_base::TargetKeyConvertor conv;
  auto start_us = base::GetTimestamp();
  int64_t cur_ms_ = start_us / 1000;
  const auto& ad_cov_event = session_data_->ad_request->ad_user_info().ad_cov_event();

  auto& UNIT_DATA_ = conv_data_[UNIT_DATA];
  auto& ACCOUNT_DATA_ = conv_data_[ACCOUNT_DATA];
  auto& CAMPAIGN_DATA_ = conv_data_[CAMPAIGN_DATA];
  auto& APP_DATA_ = conv_data_[APP_DATA];
  auto& CORPORATION_DATA_ = conv_data_[CORPORATION_DATA];
  auto& DEFINE_PRODUCT_DATA_ = conv_data_[DEFINE_PRODUCT_DATA];
  auto& PACKAGE_DATA_ = conv_data_[PACKAGE_DATA];

  auto fill = [&](const auto& item, bool new_action) {
    kuaishou::ad::AdActionType ocpx_type;
    if (new_action && (!kuaishou::ad::AdActionType_Parse(item.event_type(),
                                                         &ocpx_type))) {  // 只有新版数据考虑 action type
      LOG(ERROR) << "UniverseConvertedDataFilter parse redis EventType error:" << item.event_type();
      return;
    }

    int64_t unit_id = item.unit_id();
    const Unit* unit;
    // 修改
    if (context->unit_df) {
      unit = session_data_->unit_df->Find(unit_id).GetStruct<Unit>();   // NOLINT
    } else {
      unit = TableWrapper::GetUnit(unit_id);
    }
    // 先使用 item 里面的 account_id 和 campaign_id, 如果没有则索引里查
    int64_t account_id = item.account_id() > 0 ? item.account_id() : unit ? unit->account_id() : 0;
    const Account* account = nullptr;
    if (context->account_df) {
      account = session_data_->account_df->Find(account_id).GetStruct<Account>();
    } else {
      account = TableWrapper::GetAccount(account_id);
    }
    int32_t range_type = ConvertedTimeRange(cur_ms_ - item.timestamp());
    // 超过 90 天或重定向账户，不做过滤
    if (range_type < 0 ||
        session_data_->retarget_account_set.find(account_id) != session_data_->retarget_account_set.end()) {
      return;
    }

    if (!item.product_name().empty()) {
      APP_DATA_[conv_(item.product_name())][new_action].Merge(
          new_action, {ocpx_type, range_type});
    }
    if (!item.package_name().empty()) {
      APP_DATA_[conv_(item.package_name())][new_action].Merge(
          new_action, {ocpx_type, range_type});
    }
    if (!item.define_product().empty()) {
      DEFINE_PRODUCT_DATA_[conv_(item.define_product())][new_action]
          .Merge(new_action, {ocpx_type, range_type});
    }
    if (!item.corporation_name().empty()) {
      CORPORATION_DATA_[conv_(item.corporation_name())][new_action]
          .Merge(new_action, {ocpx_type, range_type});
    }
    if (unit != nullptr) {
      UNIT_DATA_[unit->id()][new_action].Merge(new_action, {ocpx_type, range_type});
    }
    // campaign level
    int64_t campaign_id = item.campaign_id() > 0 ? item.campaign_id() : unit ? unit->campaign_id() : 0;
    if (campaign_id != 0) {
      CAMPAIGN_DATA_[campaign_id][new_action].Merge(new_action, {ocpx_type, range_type});
    }
    // account level
    if (account_id != 0) {
      ACCOUNT_DATA_[account_id][new_action].Merge(new_action, {ocpx_type, range_type});
    }
    if (account) {
      // corporation level
      if (account->city_corporation_id() != 0) {
        CORPORATION_DATA_[account->city_corporation_id()][new_action].Merge(
            new_action, {ocpx_type, range_type});
      }
      // app level
      if (account->city_product_id()) {
        APP_DATA_[account->city_product_id()][new_action].Merge(new_action, {ocpx_type, range_type});
      }
      if (unit && unit->package_id() > 0) {
        // 修改
        const AdAppRelease* p_app = nullptr;
        if (context->unit_df) {
          p_app = unit->package_id_ref_AdAppRelease();
        } else {
          p_app = TableWrapper::GetAdAppRelease(unit->package_id());
        }
        if (p_app) {
          PACKAGE_DATA_[p_app->package_name_id()][new_action].Merge(new_action, {ocpx_type, range_type});
        }
      }
      // 用户定义产品名
      if (account->define_product_hash() != 0) {
        DEFINE_PRODUCT_DATA_[account->define_product_hash()][new_action].Merge(
            new_action, {ocpx_type, range_type});
      }
    }
  };
  // [tanghaihong] 这里不做截断应该也还好
  for (const auto& item : ad_cov_event.event_infos()) {
    fill(item, false);
  }
  for (const auto& item : ad_cov_event.action_infos()) {
    fill(item, true);
  }
  wechat_id_to_last_convert_time_.clear();
  enable_wechat_id_conv_filter_ = SPDM_enableUniverseWechatIdConvFilter();
  if (enable_wechat_id_conv_filter_) {
    for (const auto& item : ad_cov_event.corp_action_infos()) {
      int64_t wechat_id = conv(item.corp_id());
      auto iter = wechat_id_to_last_convert_time_.find(wechat_id);
      if (iter == wechat_id_to_last_convert_time_.end()) {
        wechat_id_to_last_convert_time_[wechat_id] = item.timestamp();
      } else {
        iter->second = std::max<int64_t>(item.timestamp(), iter->second);
      }
    }
  }
  session_data_->dot->Interval(ad_cov_event.event_infos_size() + ad_cov_event.action_infos_size(),
                               "ad_cov_event_total_size");

  auto app_list_upbound = 500;
  auto third_action_app_info = session_data_->universe_audience_tag_infos.find(18);
  auto second_action_app_info = session_data_->universe_audience_tag_infos.find(10);
  if (third_action_app_info !=  session_data_->universe_audience_tag_infos.end() &&
      third_action_app_info->second.string_set.size() <= app_list_upbound &&
      third_action_app_info->second.string_set.size() > 0) {
    for (const auto &app_package_name : third_action_app_info->second.string_set) {
      second_action_app_info_set_.emplace(conv_(app_package_name));
    }
    session_data_->dot->Count(1, "universe_action_app_exp_third_v4_cnt");
    session_data_->dot->Interval(third_action_app_info->second.string_set.size(),
                          "universe_action_app_exp_third_app_size_v4_cnt");
  }
  if (second_action_app_info != session_data_->universe_audience_tag_infos.end() &&
      second_action_app_info->second.string_set.size() <= app_list_upbound &&
      second_action_app_info->second.string_set.size() > 0) {
    for (const auto &app_package_name : second_action_app_info->second.string_set) {
      third_action_app_info_set_.emplace(conv_(app_package_name));
    }
    session_data_->dot->Count(1, "universe_action_app_exp_second_v4_cnt");
    session_data_->dot->Interval(second_action_app_info->second.string_set.size(),
                          "universe_action_app_exp_second_app_size_v4_cnt");
  }

  session_data_->dot->Interval(base::GetTimestamp() - start_us, "converted_data_prepare_cost_us");
}

bool UniverseConvertedDataFilter::PickUp() const {
  return true;
}

RuleStatus UniverseConvertedDataFilter::DoRule(RetrievalAdCommon& ad, size_t task_no) const {
  if (ad.base.p_account == nullptr ||
      ad.base.p_campaign == nullptr ||
      ad.base.p_unit == nullptr ||
      ad.base.p_target == nullptr) {
    return RuleStatus::OK;
  }

  // 小系统 && 拉新 && 开关，不走这个过滤
  if (FLAGS_is_universe_tiny_deploy &&
      SPDM_enable_universe_tiny_skip_converted_filter(session_data_->spdm_ctx) &&
      !session_data_->app_set.empty() &&
      ad.campaign_type() == kuaishou::ad::AdEnum::APP) {
    return RuleStatus::OK;
  }

  int32_t level = ad.base.p_target->filter_converted_level();
  int32_t filter_time_range = ad.base.p_target->filter_time_range();
  auto ocpx_action_type = ad.base.p_unit->ocpx_action_type();

  auto judge = [&](int64_t id, int64_t data_level, int ocpx_action_type) {
    auto iter = conv_data_.find(data_level);
    if (iter == conv_data_.end()) {
      return false;
    }
    auto it = iter->second.find(id);
    if (it == iter->second.end()) {
      return false;
    }
    // old
    if (it->second[0].status && it->second[0].Hit(false, ocpx_action_type, filter_time_range)) {
      return true;
    }
    // new
    if (it->second[1].status && it->second[1].Hit(true, ocpx_action_type, filter_time_range)) {
      return true;
    }
    return false;
  };

  bool filter = false;

  switch (level) {
    case kuaishou::ad::AdEnum::UNIT_LEVEL:
      filter = judge(ad.unit_id(), UNIT_DATA, ocpx_action_type);
      break;
    case kuaishou::ad::AdEnum::CAMPAIGN_LEVEL:
      filter = judge(ad.campaign_id(), CAMPAIGN_DATA, ocpx_action_type);
      break;
    case kuaishou::ad::AdEnum::ACCOUNT_LEVEL:
      filter = judge(ad.account_id(), ACCOUNT_DATA, ocpx_action_type);
      break;
    case kuaishou::ad::AdEnum::CORPORATION_LEVEL:
      filter = judge(ad.base.p_account->city_corporation_id(), CORPORATION_DATA, ocpx_action_type);
      break;
    case kuaishou::ad::AdEnum::APP_LEVEL: {
      filter = judge(ad.base.p_account->city_product_id(), APP_DATA, ocpx_action_type);
      if (filter) {
        break;
      }
      int64_t package_name_id = ad.base.p_ad_app_release ?
        ad.base.p_ad_app_release->package_name_id() : ad.base.p_unit->city_app_package_name_id();
      filter = judge(package_name_id, PACKAGE_DATA, ocpx_action_type);
      if (filter) {
        break;
      }
      if (second_action_app_info_set_.count(package_name_id) > 0 ||
          third_action_app_info_set_.count(package_name_id) > 0) {
        filter = true;
      }
      break;
    }
    case kuaishou::ad::AdEnum::DEFINE_PRODUCT_LEVEL:
      filter = judge(ad.base.p_account->define_product_hash(), DEFINE_PRODUCT_DATA, ocpx_action_type);
      break;
    case kuaishou::ad::AdEnum::WECHAT_ID_LEVEL:
      if (enable_wechat_id_conv_filter_ && !ad.base.p_target->filter_converted_wechat_id().empty()) {
        const auto& wechat_ids = ad.base.p_target->filter_converted_wechat_id();
        for (const auto& id : wechat_ids) {
          auto iter = wechat_id_to_last_convert_time_.find(id);
          if (iter == wechat_id_to_last_convert_time_.end()) {
            continue;
          } else {
            auto convert_time_range = ConvertedTimeRange(iter->second);
            if (convert_time_range == -1 || filter_time_range < convert_time_range) {
              continue;
            }
            filter = true;
            break;
          }
        }
      }
      break;
    default:
      filter = false;
  }

  if (filter) {
    if (counter_.size() > 0) {
      ++counter_[task_no % counter_.size()].filter_count;
    }
    RULE_FILTER_RETURN_WITH_REASON(
        kuaishou::log::ad::AdTraceFilterCondition::CONVERTED_USER_FILTER);
  }

  return RuleStatus::OK;
}

void UniverseConvertedDataFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_converted_data_filter", s_counter.filter_count);
}

UniverseSiteAssemblyFilter::UniverseSiteAssemblyFilter(ContextData *context,
                                                                      size_t worker_nums)
    : counter_(worker_nums) {
  Initialize(context, worker_nums);
  universe_site_assembly_filter_opt_ = SPDM_universe_site_assembly_filter_opt_v2(context->spdm_ctx);
  sdk_version_ = context->ad_request->universe_ad_request_info().sdk_version();
  if (SPDM_enableSkipSiteAssemblyFilter() && universe_site_assembly_filter_opt_) {
    auto& ab_keys = AdKconfUtil::universeSiteAssemblyConfigV2()->data().GetAbKeys();
    for (auto& [key, ab_key] : ab_keys) {
      bool ab_val = session_data_->session_context->TryGetBoolean(ab_key, false);
      if (ab_val) skip_keys_.emplace(key);
    }
  }
}

bool UniverseSiteAssemblyFilter::PickUp() const {
  return universe_site_assembly_filter_opt_;
}

RuleStatus UniverseSiteAssemblyFilter::DoRule(RetrievalAdCommon &ad, size_t task_no) const {
  if (ad.base.p_magic_site_page) {
    auto* p_magicsite = ad.base.p_magic_site_page;
    if (!p_magicsite->type_list().empty() && !p_magicsite->sub_type_list().empty()) {
      auto& type_list = p_magicsite->type_list();
      auto& sub_type_list = p_magicsite->sub_type_list();
      // 兜底过滤
      if (type_list.size() != sub_type_list.size()) {
        ++counter_[task_no % counter_.size()].special_filter_count;
          RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::
                                             UNIVERSE_SITE_ASSEMBLY_FILTER);
      } else if (SPDM_enableSkipSiteAssemblyFilter()) {
        for (int i = 0; i < type_list.size() && i < 10; i++) {
          // 跳过过滤
          auto key = absl::Hash<std::pair<int64_t, int64_t>>()(
            std::make_pair(type_list.at(i), sub_type_list.at(i)));
          if (!skip_keys_.count(key) &&
              AdKconfUtil::universeSiteAssemblyConfigV2()->data().CheckInvalid(
                            type_list.at(i), sub_type_list.at(i), sdk_version_)) {
            ++counter_[task_no % counter_.size()].normal_filter_count;
            RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::
                                              UNIVERSE_SITE_ASSEMBLY_FILTER);
          }
        }
      } else {  // 正常过滤
        for (int i = 0; i < type_list.size() && i < 10; i++) {
          if (AdKconfUtil::universeSiteAssemblyConfigV2()->data().CheckInvalid(
                            type_list.at(i), sub_type_list.at(i), sdk_version_)) {
            ++counter_[task_no % counter_.size()].normal_filter_count;
            RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::
                                              UNIVERSE_SITE_ASSEMBLY_FILTER);
          }
        }
      }
    }
  }
  return RuleStatus::OK;
}

void UniverseSiteAssemblyFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_site_type_normal_filter", s_counter.normal_filter_count);
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_site_type_special_filter", s_counter.special_filter_count);
}

// UniverseHarmonySiteAssemblyFilter
UniverseHarmonySiteAssemblyFilter::UniverseHarmonySiteAssemblyFilter(ContextData* context,
                                                 size_t worker_nums) : counter_(worker_nums) {
  Initialize(context, worker_nums);
}

bool UniverseHarmonySiteAssemblyFilter::PickUp() const {
  return SPDM_enableUniverseHarmonySiteAssemblyFilter() &&
         session_data_->ad_request->ad_user_info().platform() == "harmony";
}

RuleStatus UniverseHarmonySiteAssemblyFilter::DoRule(RetrievalAdCommon& ad, size_t task_no) const {
  if (ad.base.p_magic_site_page == nullptr) {
    return RuleStatus::OK;
  }
  const auto& type_list_vec = ad.base.p_magic_site_page->type_list();
  const auto& sub_type_list_vec = ad.base.p_magic_site_page->sub_type_list();
  if (type_list_vec.size() != sub_type_list_vec.size()) {
    RULE_FILTER_RETURN_WITH_REASON(
        kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_HARMONY_SITE_ASSEMBLY_FILTER);
  }
  const auto& config = AdKconfUtil::universeHarmonySiteAssemblyFilterConfig()->data();
  for (int32_t i = 0; i < type_list_vec.size() && i < sub_type_list_vec.size(); ++i) {
    if (config.IsBlockedPageComponent(type_list_vec[i], sub_type_list_vec[i])) {
      if (counter_.size() > 0) {
        ++counter_[task_no % counter_.size()].filter_count;
      }
      RULE_FILTER_RETURN_WITH_REASON(
        kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_HARMONY_SITE_ASSEMBLY_FILTER);
    }
    if (i >= 10) {
      // 截断，防止性能问题
      if (counter_.size() > 0) {
        ++counter_[task_no % counter_.size()].filter_count;
      }
      RULE_FILTER_RETURN_WITH_REASON(
        kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_HARMONY_SITE_ASSEMBLY_FILTER);
    }
  }

  return RuleStatus::OK;
}

void UniverseHarmonySiteAssemblyFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_harmony_site_assembly_filter", s_counter.filter_count);
}

UniverseProductFreandTimeFilter::UniverseProductFreandTimeFilter
                  (ContextData *context, size_t worker_nums) : counter_(worker_nums) {
  Initialize(context, worker_nums);
  filteredproduct.clear();
  // 获取用户产品分时点击频控数据
  std::string redis_freq_raw_str;
  std::string redis_freq_decoded_str;
  auto ret = session_data_->redis_result.freq_filter_product_time_list.Get(&redis_freq_raw_str);
  if (ret != ks::infra::KS_INF_REDIS_NO_ERROR && ret != ks::infra::KS_INF_REDIS_ERR_UNKNOWN) {
    LOG_EVERY_N(INFO, 1000) << "Failed to get user pb from redis, reason: " << ret;
    return;
  }
  if (redis_freq_raw_str.empty()) return;
  if (!base::Base64Decode(redis_freq_raw_str, &redis_freq_decoded_str)) {
    LOG_EVERY_N(INFO, 1000) << "Failed to decode string from redis adUniverseFreqData";
    return;
  }
  kuaishou::ad::universe::ProductUnionFreqData redis_freq_pb;
  if (!redis_freq_pb.ParseFromString(redis_freq_decoded_str)) {
    LOG_EVERY_N(INFO, 1000) << "Failed to parse decoded string to pb";
    return;
  }
  if (!AdKconfUtil::universeFreTimeConfig()) {
    LOG(WARNING) << "Kconf ptr nullptr";
    return;
  }
  auto data_config = AdKconfUtil::universeFreTimeConfig()->data();
  int64_t hour = session_data_->bd.hour;
  auto flag = SPDM_enable_universe_fre_time_filter(session_data_->spdm_ctx);
  if (!data_config.filterproducts(flag, &filteredproduct, hour, redis_freq_pb)) {
    LOG(WARNING) << "UniverseProductFreandTimeFilter get Prodcut config error,  type error";
  }
}

bool UniverseProductFreandTimeFilter::PickUp() const {
  return !filteredproduct.empty();
}
RuleStatus UniverseProductFreandTimeFilter::DoRule(RetrievalAdCommon &ad, size_t task_no) const {
  if (filteredproduct.count(ad.city_product_id())) {
    counter_[task_no % counter_.size()].filter_count++;
    RULE_FILTER_RETURN_WITH_REASON(
        kuaishou::log::ad::AdTraceFilterCondition::
              UNIVERSE_PRODUCT_FRE_AND_TIME_FILTER);
  }
  return RuleStatus::OK;
}
void UniverseProductFreandTimeFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.UniverseProductFreandTimeFilter", s_counter.filter_count);
}

// UniverseTinySearchFilterCover
UniverseTinySearchFilterCover::UniverseTinySearchFilterCover(ContextData* context,
                                                 size_t worker_nums) : counter_(worker_nums) {
  Initialize(context, worker_nums);
}

bool UniverseTinySearchFilterCover::PickUp() const {
  if (!SPDM_enableUniverseAdTypeFix()) {
    return false;
  }
  if (!FLAGS_is_universe_tiny_deploy) {
    return false;
  }
  bool skip_filter = SPDM_skip_universe_tiny_search_filter(session_data_->spdm_ctx) ||
                     SPDM_skip_universe_tiny_search_filter_v2(session_data_->spdm_ctx);
  const auto pos_id = session_data_->pos_manager_base.GetMediumPosId();
  const int32_t ad_style = session_data_->pos_manager_base.request_imp_infos.empty()
                           ? 0
                           : session_data_->pos_manager_base.request_imp_infos[0].ad_style;
  const auto support_pos_id_set = AdKconfUtil::universeTinySearchFilterPos();
  if (SPDM_enableUniverseTinySearchFilterPos() &&
      support_pos_id_set != nullptr && support_pos_id_set->count(pos_id) == 0) {
    // 搜索直投广告限制在指定广告位出，不在这些广告位上的必须过滤，不能跳过
    skip_filter = false;
  }
  if ((ad_style != 19 && ad_style != 20) ||
      session_data_->ad_request->universe_ad_request_info().search_query().empty()) {
    skip_filter = false;
  }
  return !skip_filter;
}

RuleStatus UniverseTinySearchFilterCover::DoRule(RetrievalAdCommon& ad, size_t task_no) const {
  if (ad.base.p_unit == nullptr || ad.base.p_unit->ad_type() == AdType::search) {
    if (counter_.size() > 0) {
        ++counter_[task_no % counter_.size()].filter_count;
    }
    RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_TINY_SEARCH_FILTER);
  }
  return RuleStatus::OK;
}

void UniverseTinySearchFilterCover::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_tiny_search_filter_cover", s_counter.filter_count);
}

UniverseCCreativeFilter::UniverseCCreativeFilter(ContextData* context,
    size_t worker_nums) : counter_(worker_nums) {
  Initialize(context, worker_nums);
  enable_universe_c_creative_filter_ = SPDM_enable_universe_c_creative_filter(context->spdm_ctx);
  if (enable_universe_c_creative_filter_) {
    const std::string& app_id = session_data_->pos_manager_base.GetRequestAppId();
    auto pos_id = session_data_->pos_manager_base.GetMediumPosId();
    risk_item_ =
        AdKconfUtil::universeCFilterConfigs()->data().GetMediaTags(
            session_data_->medium_uid, app_id, pos_id);
    white_risk_item_ =
        AdKconfUtil::universeCFilterWhiteConfigs()->data().GetMediaTags(
            session_data_->medium_uid, app_id, pos_id);
  }
}

bool UniverseCCreativeFilter::PickUp() const {
  return enable_universe_c_creative_filter_;
}
// dark c 素材分级过滤
RuleStatus UniverseCCreativeFilter::DoRule(RetrievalAdCommon& ad, size_t task_no) const {
  if (ad.base.p_creative != nullptr &&
      ad.base.p_creative->app_grade() == 3 &&
      ad.base.p_creative->risk_labels().size() != 0) {
    for (auto& label : ad.base.p_creative->risk_labels()) {
      if (risk_item_ && risk_item_->black_risk_labels.count(label) > 0) {
        ++counter_[task_no % counter_.size()].filter_count;
        RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_C_CREATIVE_BLACK_CONF_FILTER);  // NOLINT
      }
      if (AdKconfUtil::universeCFilterWhiteConfigs()->data().full_white_risk_labels.count(label) > 0) {
        if (white_risk_item_ && white_risk_item_->white_risk_labels.count(label) > 0) {
          return RuleStatus::OK;
        }
        ++counter_[task_no % counter_.size()].filter_count;
        RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_C_CREATIVE_WHITE_CONF_FILTER);  // NOLINT
      }
    }
  }
  return RuleStatus::OK;
}

void UniverseCCreativeFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_c_creative_filter", s_counter.filter_count);
}

// UniverseDarkCCreativeFilter
UniverseDarkCCreativeFilter::UniverseDarkCCreativeFilter(ContextData* context,
                  size_t worker_nums) : counter_(worker_nums) {
  Initialize(context, worker_nums);
  enable_universe_dark_c_creative_filter_ = SPDM_enableUniverseDarkCCreativeFilter();
  if (enable_universe_dark_c_creative_filter_) {
    const std::string& app_id = session_data_->pos_manager_base.GetRequestAppId();
    risk_tags_ = ks::engine_base::AdKconfUtil::universeDarkCFilterConfigs()->data().GetMediaTags(app_id);
  }
}

bool UniverseDarkCCreativeFilter::PickUp() const {
  if (!enable_universe_dark_c_creative_filter_) return false;
  return !risk_tags_.empty();
}
// dark c 素材分级过滤
RuleStatus UniverseDarkCCreativeFilter::DoRule(RetrievalAdCommon& ad, size_t task_no) const {
  if (!ad.is_universe_ads() &&
      ad.base.p_creative != nullptr &&
      ad.base.p_creative->app_grade() == 3 &&
      ad.base.p_creative->risk_labels().size() != 0) {
    if (ks::engine_base::AdKconfUtil::universeDarkCFilterConfigs()->data().AdHit(
          ad.base.p_creative->risk_labels(), risk_tags_)) {
      ++counter_[task_no % counter_.size()].filter_count;
      RULE_FILTER_RETURN_WITH_REASON(kuaishou::log::ad::AdTraceFilterCondition::UNIVERSE_DARK_C_FILTER);
    }
  }
  return RuleStatus::OK;
}

void UniverseDarkCCreativeFilter::Report() const {
  Counter s_counter;
  std::for_each(counter_.begin(), counter_.end(), [&](const Counter& c) { s_counter.merge(c); });
  PERF_DATA_INTERVAL("ad_target.ad_filter.universe_dark_c_creative_filter", s_counter.filter_count);
}


}  // namespace ad_target
}  // namespace ks
