#pragma once

#include <memory>
#include <set>
#include <vector>
#include <string>
#include <limits>
#include <algorithm>
#include <utility>
#include "absl/container/flat_hash_set.h"
#include "teams/ad/ad_target_universe/utils/kconf/kconf_data.h"
#include "teams/ad/ad_target_universe/strategy/rule/rule_base.h"
#include "teams/ad/ad_target_universe/universe/bg_task/universe_operation_orient_label_sheild.h"

namespace ks {
namespace engine_base {
struct OperationConfigFlowControl;
}

namespace ad_target {

class UniverseOrientLabelSheildFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    int32_t hit_white_list_count = 0;
    int32_t fast_appv2_conut = 0;
    void merge(const Counter& c) {
      this->filter_count += c.filter_count;
      this->hit_white_list_count += c.hit_white_list_count;
      this->fast_appv2_conut += c.fast_appv2_conut;
    }
  };

 public:
  UniverseOrientLabelSheildFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 private:
  mutable std::vector<Counter> counter_;

  OLSBlockItem ols_block_item_;  // 标签过滤相关

  // 快应用配置
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<ks::ad_target::UniverseShieldFastApplicationConfig>>
      universe_shield_fast_application_config_{nullptr};
};


class UniverseInternalNonAdDarkUnitFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseInternalNonAdDarkUnitFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 private:
  mutable std::vector<Counter> counter_;
};

class UniverseAggregatePageFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseAggregatePageFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 public:
  std::shared_ptr<UnionMidPageWhiteConfig> aggregate_page_config;

 private:
  mutable std::vector<Counter> counter_;
};

class UniverseFansLiveFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseFansLiveFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 public:
  std::shared_ptr<absl::flat_hash_set<int64_t>> origin_live_white_authors{nullptr};
  bool enable_universe_outer_origin_live{false};
  bool is_feed_support_outer_origin_live {false};
 private:
  mutable std::vector<Counter> counter_;
};

class UniverseTinyInstalledAppFloorFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseTinyInstalledAppFloorFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 private:
  mutable std::vector<Counter> counter_;
  bool is_tiny_disable_logic_tf_;
  absl::flat_hash_set<int64_t> app_hash_set_;
};

class UniverseNonAppIntroductionFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseNonAppIntroductionFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;
 private:
  mutable std::vector<Counter> counter_;
};

class UniverseAccountListFreqFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseAccountListFreqFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;
 private:
  mutable std::vector<Counter> counter_;
  absl::flat_hash_set<int64_t> filter_stra_ids_;
};

class UniverseSiteAssemblyFilter : public RuleBase {
 private:
  struct Counter {
    int32_t normal_filter_count = 0;
    int32_t special_filter_count = 0;
    void merge(const Counter &c) {
      this->normal_filter_count += c.normal_filter_count;
      this->special_filter_count += c.special_filter_count;
    }
  };
 public:
  UniverseSiteAssemblyFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;
 private:
  mutable std::vector<Counter> counter_;
  bool universe_site_assembly_filter_opt_{false};
  std::string sdk_version_;
  absl::flat_hash_set<int64_t> skip_keys_;
};

class UniverseOrientDeliveryMingtouFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseOrientDeliveryMingtouFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 private:
  mutable std::vector<Counter> counter_;
  int64_t pv_pos_id;
  int64_t pv_app_id_int64;
  int64_t os_type;
};

class UniverseRiskLabelFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseRiskLabelFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 private:
  mutable std::vector<Counter> counter_;
  absl::flat_hash_set<int64_t> blocked_categories_;
  std::shared_ptr<UniverseCategoryToRiskLabels> risk_label_config_;
};

// 迁移 CampaignTable::ConvertedFilterV2ForTruncateAdCovEvent 已转化过滤
class UniverseConvertedDataFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseConvertedDataFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

  enum ConvertType {
    UNIT_DATA = 0,
    ACCOUNT_DATA,
    CAMPAIGN_DATA,
    APP_DATA,
    CORPORATION_DATA,
    DEFINE_PRODUCT_DATA,
    PACKAGE_DATA,
  };

  struct ConvertedItem {
    int ocpx_action_type = 0;
    int range_type = 0;
  };

  struct ConvertedInfo {
    // new action == true 使用
    absl::flat_hash_map<int, int> ocpx_2_range_type;
    // new action == false 使用
    int range_type = std::numeric_limits<int>::max();
    // 标识状态
    bool status = false;

    void Merge(bool new_action, const ConvertedItem& r) {
      status = true;
      if (new_action) {
        auto it = ocpx_2_range_type.find(r.ocpx_action_type);
        if (it == ocpx_2_range_type.end()) {
          ocpx_2_range_type[r.ocpx_action_type] = r.range_type;
        } else {
          it->second = std::min(r.range_type, it->second);
        }
      } else {
        range_type = r.range_type;
      }
    }

    bool Hit(bool new_action, int m_ocpx_action_type, int m_range_type) const {
      if (new_action) {
        auto it = ocpx_2_range_type.find(m_ocpx_action_type);
        if (it == ocpx_2_range_type.end()) {
          return false;
        }
        return m_range_type >= it->second;
      }
      return m_range_type >= range_type;
    }
  };

  int ConvertedTimeRange(int64_t time) const {
    static int64_t kNinetyDay = (int64_t)90 * 24 * 3600 * 1000;
    static int64_t kSixtyDay = (int64_t)60 * 24 * 3600 * 1000;
    static int64_t kThirtyDay = (int64_t)30 * 24 * 3600 * 1000;
    static int64_t days_180 = (int64_t)180 * 24 * 3600 * 1000;
    static int64_t days_365 = (int64_t)365 * 24 * 3600 * 1000;
    if (time > days_365) {
      return -1;
    } else if (time > days_180) {
      return 4;
    } else if (time > kNinetyDay) {
      return 3;
    } else if (time > kSixtyDay) {
      return 2;
    } else if (time > kThirtyDay) {
      return 1;
    } else {
      return 0;
    }
    return -1;
  }

 private:
  mutable std::vector<Counter> counter_;

  static ad_base::TargetKeyConvertor conv_;

  // ConvertType x id -> [ConvertedInfo_old, ConvertedInfo_new]
  absl::flat_hash_map<int, absl::flat_hash_map<int64_t, std::array<ConvertedInfo, 2>>> conv_data_;
  // 企微 id 已转化过滤使用
  absl::flat_hash_map<int64_t, int64_t> wechat_id_to_last_convert_time_;
  bool enable_wechat_id_conv_filter_;

  absl::flat_hash_set<int64_t> second_action_app_info_set_;
  absl::flat_hash_set<int64_t> third_action_app_info_set_;
};

class UniverseHarmonySiteAssemblyFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseHarmonySiteAssemblyFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 private:
  mutable std::vector<Counter> counter_;
};

// zzw UniverseProductFreandTimeFilter
class UniverseProductFreandTimeFilter : public RuleBase {
 public:
  UniverseProductFreandTimeFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;
 private:
  absl::flat_hash_set<int64_t> filteredproduct;  //被过滤产品
  struct Counter {
    int32_t filter_count{0};
    void merge(const Counter& C) {
      this->filter_count += C.filter_count;
    }
  };
  mutable std::vector<Counter> counter_;  //打点记录,每个子任务都有一个
};

// RbUniverse::UniverseTinySearchFilter 的兜底逻辑，解 ad_type 倒排问题
class UniverseTinySearchFilterCover : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseTinySearchFilterCover(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 private:
  mutable std::vector<Counter> counter_;
};

// c 类创意分级过滤
class UniverseCCreativeFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseCCreativeFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 private:
  mutable std::vector<Counter> counter_;
  bool enable_universe_c_creative_filter_;
  std::shared_ptr<UniverseCCreativeRiskConfig::Item> risk_item_;
  std::shared_ptr<UniverseCCreativeRiskConfig::Item> white_risk_item_;
};

// dark c 素材分级过滤
class UniverseDarkCCreativeFilter : public RuleBase {
 private:
  struct Counter {
    int32_t filter_count = 0;
    void merge(const Counter &c) { this->filter_count += c.filter_count; }
  };

 public:
  UniverseDarkCCreativeFilter(ContextData *context, size_t worker_nums);
  bool PickUp() const override;
  RuleStatus DoRule(RetrievalAdCommon &ad, size_t task_no) const override;
  void Report() const override;

 private:
  mutable std::vector<Counter> counter_;
  bool enable_universe_dark_c_creative_filter_;
  absl::flat_hash_set<int64_t> risk_tags_;
};

}  // namespace ad_target
}  // namespace ks
