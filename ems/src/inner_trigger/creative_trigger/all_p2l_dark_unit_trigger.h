#pragma once
#include <memory>
#include <string>
#include <set>
#include "teams/ad/ems/src/core/timer_trigger.h"
#include "teams/ad/ems/src/util/async_redis_helper.h"
#include "teams/ad/ems/src/util/async_redis_params.h"
#include "teams/ad/index_builder/tables/data_gate/creative.h"
#include "teams/ad/ems/src/invert/ad_table/common.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/index_manager.h"

namespace ks {
namespace ad_ems {

class AllP2lDarkUnitTrigger : public TimerTrigger {
 public:
  AllP2lDarkUnitTrigger() : TimerTrigger() {
  }
  virtual ~AllP2lDarkUnitTrigger() {
  }
  bool InitImpl(const std::string& conf_path) override;
  int64_t IntervalMs() override;

 protected:
  void RunImpl() override;
 private:
  std::shared_ptr<AdTableForwardIndexWrapper> index_wrapper_;
};

DECLARE_TRIGGER(AllP2lDarkUnitTrigger);

}  // namespace ad_ems
}  // namespace ks
