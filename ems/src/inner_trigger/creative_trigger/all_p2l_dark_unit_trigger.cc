#include "teams/ad/ems/src/inner_trigger/creative_trigger/all_p2l_dark_unit_trigger.h"

#include <string>
#include <vector>
#include <unordered_set>

#include "base/common/logging.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/table_listener.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/index_manager.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ems/src/core/inner_task.h"
#include "teams/ad/ems/src/util/util.h"
#include "teams/ad/ems/src/util/kconf.h"
#include "teams/ad/index_builder/tables/data_gate/unit.h"
#include "teams/ad/index_builder/tables/data_gate/campaign.h"
#include "teams/ad/index_builder/tables/data_gate/creative.h"
#include "teams/ad/ems/src/invert/invert_data_manager.h"
#include "teams/ad/ems/src/core/workflow.h"
#include "teams/ad/engine_base/budget_common/budget_status_receiver.h"
#include "teams/ad/ems/src/common_toolkit/rate_limiter_manager.h"

namespace ks {
namespace ad_ems {

using namespace ::ks::ad_table_lite::StructNS;  // NOLINT
using tables::data_gate::Unit;
using tables::data_gate::Account;
using tables::data_gate::Campaign;
using tables::data_gate::Creative;
using tables::data_gate::LiveStreamUserInfo;
namespace td = ::tables::data_gate;

static const char kAllP2lDarkTriggerAction[] = "all_p2l_dark_trigger_action";
bool AllP2lDarkUnitTrigger::InitImpl(const std::string& kconf_path) {
  LOG(WARNING) << "AllP2lDarkUnitTrigger InitImpl[" << kconf_path << "].";
  index_wrapper_ = std::make_shared<AdTableForwardIndexWrapper>(Name());
  return true;
}

void AllP2lDarkUnitTrigger::RunImpl() {
  LOG(INFO) << Name() << " Start RunImpl";
  auto index = index_wrapper_;
  // TODO(zhaizhiqiang): 实现逻辑
  if (FLAGS_enable_bid_no_diff_switch) {
    LOG(INFO) << Name() << ", nodiff";
    thread_local bool is_run = false;
    if (is_run) {
      LOG(INFO) << Name() << ", nodiff skip";
      return;
    }
    is_run = true;
    return;
  }
  std::vector<uint64_t> unit_ids;
  ks::ad_ems::InvertDataManager::Instance().GetAllInnerLoopUnit(&unit_ids);
  ks::infra::PerfUtil::CountLogStash(unit_ids.size(), kCommonPerfNameSpace,
      kAllP2lDarkTriggerAction, "unit_size");
  LOG(INFO) << Name() << ", unit_ids size : " << unit_ids.size();
  for (auto unit_id : unit_ids) {
    td::Unit unit = index->Get<Unit>(unit_id);
    // unit 无效
    if (!unit.IsValid()) {
      LOG(INFO) << Name() << ", Get Unit not valid, unit_id:" << unit_id;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "unit_not_value");
      continue;
    }
    if (!ShardMgr::Instance().AccountInCurrentShard(unit.account_id)) {
      LOG_EVERY_N(INFO, 1000) << Name() << ", AccountInCurrentShard Filter account_id:" << unit.account_id;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "account_not_in_shard");
      continue;
    }
    // 不在直播
    kuaishou::ad::tables::LiveStreamUserInfo live_stream_info;
    if (unit.live_user_id == 0
        || !index->Get<LiveStreamUserInfo>(unit.live_user_id, &live_stream_info)) {
      LOG_EVERY_N(INFO, 1000) << Name() << ", LiveStreamUserInfo not valid, unit_id:" << unit_id
      << ", live_user_id:" << unit.live_user_id;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "live_stream_info_not_ok");
      continue;
    }
    std::shared_ptr<std::set<int64_t>> author_exp_set_ = EmsKconfUtil::p2lDarkExpAuthorSet();
    if (unit.live_user_id % 100 >= EmsKconfUtil::photoDarkAuthorTail()
        && (author_exp_set_ == nullptr
        || !author_exp_set_->count(unit.live_user_id))) {
      LOG_EVERY_N(INFO, 1000) << Name() << ", AB filter unit_id:" << unit_id
      << ", live_user_id:" << unit.live_user_id;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "ab_filter");
      continue;
    }
    // unit 是高光明投 unit
    if (unit.high_light_flash == 1) {
      LOG_EVERY_N(INFO, 1000) << Name() << ", high_light_flash, unit_id:" << unit_id
        << ", high_light_flash:" << unit.high_light_flash;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "unit_is_high_light_flash");
      continue;
    }
    // 直播关闭
    if (!live_stream_info.is_live()) {
      LOG_EVERY_N(INFO, 1000) << Name()
      << ", LiveStreamUserInfo is not live, unit_id:" << unit_id
      << ", live_user_id:" << unit.live_user_id;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "is_not_living");
      continue;
    }
    // 直播计划
    kuaishou::ad::tables::Campaign campaign;
    if (!index->Get<Campaign>(unit.campaign_id, &campaign) || campaign.id() == 0 ||
        campaign.type() != kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {
      LOG_EVERY_N(INFO, 1000) << Name() << ", campaign is not value, unit_id:" << unit_id
      << ", live_user_id:" << unit.live_user_id
      << ", campaign_id:" << unit.campaign_id;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "campaign_is_not_live");
      continue;
    }
    // 只有直播创意
    std::unordered_set<uint64_t> creative_ids;
    InvertDataManager::Instance().GetCreativeListByUnit(unit_id, &creative_ids);
    if (creative_ids.size() >= 26 || creative_ids.size() == 0) {
      LOG_EVERY_N(INFO, 1000) << Name() << ", creative_ids size: " << creative_ids.size()
      << " , unit_id:" << unit_id
      << " , live_user_id:" << unit.live_user_id;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "unit_has_no_creative");
      continue;
    }
    // 存在创意类型不是直播
    bool has_not_live_creative = false;
    uint64_t live_creative_id = 0;
    for (auto creative_id : creative_ids) {
      td::Creative creative = index->Get<Creative>(creative_id);
      if (has_not_live_creative || !creative.IsValid()) {
        continue;
      }
      if (creative.live_creative_type !=
          ::kuaishou::ad::AdEnum_LiveCreativeType::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE) {
        has_not_live_creative = true;
      }
      if (creative_id > 0) {
        live_creative_id = creative_id;
      }
    }
    if (has_not_live_creative || live_creative_id == 0) {
      LOG_EVERY_N(INFO, 1000) << Name() << " creative_ids size: " << creative_ids.size()
      << ", unit_id: " << unit_id
      << ", live_user_id:" << unit.live_user_id
      << ", live_creative_id:" << live_creative_id;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "has_p2l_creative");
      continue;
    }
    ks::engine_base::UnitChargeInfo charge_info;
    if (engine_base::BudgetStatusReceiver::GetInstance()->GetUnitChargeInfo(
            unit_id, util::GetStartOfDaySeconds() * 1000 * 1000, &charge_info)) {
      // 过滤掉没有预算
      if (charge_info.left_budget <= 0) {
        ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, kAllP2lDarkTriggerAction,
                                           "unit_no_budget");
        continue;
      }
      // 过滤掉当天没有消耗的老单元,判断标准是单元无消耗，创建时间大于七天
      if (charge_info.unit_charge <= EmsKconfUtil::p2lDarkCostLowBond() &&
          (base::GetTimestamp() / 1000 - unit.create_time > 7 * 24 * 3600000)) {
        ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, kAllP2lDarkTriggerAction, "unit_no_cost");
        LOG_EVERY_N(INFO, 1000) << Name() << "unit_low_cost_filter  unit_id: " << unit_id
          << ", charge: " << charge_info.unit_charge
          << ", live_user_id:" << unit.live_user_id
          << ", live_creative_id:" << live_creative_id;
        continue;
      }
    }

    td::Account account = index->Get<Account>(unit.account_id);
    if (account.user_id != unit.live_user_id) {
      // 过滤掉代投场景
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
       kAllP2lDarkTriggerAction, "unit_user_not_equal_live_user");
      continue;
    }

    // 判断是否已经处理过
    std::string redis_key = std::to_string(unit_id) + std::to_string(live_stream_info.live_stream_id());
    RedisOption redis_option;
    redis_option.set_table(*EmsKconfUtil::fanstopP2LAdDataRedis());
    redis_option.set_timeout(10);
    redis_option.set_key(redis_key);
    FanstopP2lParams fanstop_p2l_params;
    fanstop_p2l_params.Init(redis_option);
    AsyncRedisHelper async_redis_helper;
    bool result = false;
    // redis 查询失败不做处理
    if (!async_redis_helper.Exist(&fanstop_p2l_params, &result)) {
      LOG_EVERY_N(INFO, 1000) << Name() << ", has already real unit_id:" << unit_id
      << ", redis_key:" << redis_key;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "already_deal");
      continue;
    }
    // 已经处理过了
    if (result) {
      LOG_EVERY_N(INFO, 1000) << Name() << ", has already real unit_id:" << unit_id
      << ", redis_key:" << redis_key;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
        kAllP2lDarkTriggerAction, "already_deal");
      continue;
    }

    LOG(INFO) << Name() << ", process unit_id:" << unit_id;
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
      kAllP2lDarkTriggerAction, "process_unit_num");
    std::string task_request_id = util::GenerateHex(16);
    std::shared_ptr<P2lDarkTask> task(new P2lDarkTask(Name(), task_request_id));
    task->SetEnableSharding(true);
    task->account_id = unit.account_id;
    task->unit_id = unit_id;
    task->user_id = unit.live_user_id;
    task->live_id = live_stream_info.live_stream_id();
    task->creative_id = live_creative_id;
    task->campaign_id = unit.campaign_id;
    task->SetTaskAllowId(task->unit_id);
    if (!EmsKconfUtil::disableTriggerControlPendingTaskNum()) {
      while (WorkflowManager::Instance().ShardThreadPoolPendingNum() >
             EmsKconfUtil::maxShardPoolPendingNum()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, kAllP2lDarkTriggerAction, "sleep_action");
        ks::infra::PerfUtil::CountLogStash(
            1, kCommonPerfNameSpace, "sleep_action_cnt", task->Type(), task->TriggerName(),
            absl::StrCat(WorkflowManager::Instance().ShardThreadPoolPendingNum()));
      }
    } else {
      RateLimiterManager::Instance().RateLimiterAquire(
          ::kuaishou::ad::ems::RateLimiterType::INNER_ALL_P2l_DARK_UNIT_TRIGGER_TYPE);
    }
    EmitTask(task);
  }
  LOG(INFO) << Name() << " Emd RunImpl";
}

int64_t AllP2lDarkUnitTrigger::IntervalMs() {
  return EmsKconfUtil::p2lDarkInterval();
}

}  // namespace ad_ems
}  // namespace ks
