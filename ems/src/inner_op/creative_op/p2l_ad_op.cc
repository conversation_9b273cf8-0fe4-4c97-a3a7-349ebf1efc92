#include "teams/ad/ems/src/inner_op/creative_op/p2l_ad_op.h"

#include <memory>
#include <unordered_set>
#include <algorithm>
#include "base/common/logging.h"
#include "teams/ad/ems/src/core/bg_context.h"
#include "teams/ad/ems/src/core/inner_task.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/index_manager.h"
#include "teams/ad/ems/src/inner_toolkit/platform_service.h"
#include "teams/ad/ems/src/invert/invert_data_manager.h"
#include "teams/ad/ems/src/util/kconf.h"
#include "teams/ad/ems/src/common/common.h"
#include "teams/ad/ems/src/inner_toolkit/cache_data_manager.h"
#include "teams/ad/ems/src/util/util.h"
#include "teams/ad/ems/src/core/env_manager.h"
#include "teams/ad/ems/src/proto/redis_helper.pb.h"
#include "teams/ad/ems/src/util/async_redis_params.h"
#include "teams/ad/ad_base/src/common/scoped_guard.h"
#include "teams/ad/ems/src/util/async_redis_helper.h"
#include "teams/ad/ems/src/proto/unit_info.pb.h"
#include "teams/ad/ems/src/common_toolkit/get_grid_table.h"
#include "teams/ad/ems/src/inner_cache_loader/mobile_hosting_author_level_data.h"
#include "teams/ad/ems/src/common_toolkit/trace_log_service.h"
#include "teams/ad/ems/src/inner_cache_loader/hosting_live_quota_data.h"
#include "teams/ad/ems/src/util/spdm_switches.h"
#include "teams/ad/ems/src/inner_util/util.h"

DECLARE_bool(enable_bid_no_diff_switch);

namespace ks {
namespace ad_ems {

using namespace ::ks::ad_table_lite::StructNS;  // NOLINT
namespace td = ::tables::data_gate;
using tables::data_gate::Unit;
using tables::data_gate::Creative;
using ks::ad_ems::redis_helper::RedisOption;
static const char kP2lAdOpAction[] = "p2l_ad_op_action";

DEFINE_OP(P2lAdOp);

bool P2lAdOp::InitImpl(const std::string& kconf_path) {
  LOG(WARNING) << "P2lAdOp[" << Name() << "] InitImpl " << kconf_path;
  return true;
}

int P2lAdOp::Run(Context* context) {
  if (!context) {
    LOG(ERROR) << Name() << "context is NULL.";
    return -1;
  }
  std::string task_type = context->GetTask()->Type();
  if (task_type == "P2lUnitTask") {
    std::shared_ptr<P2lUnitTask> task =
              std::dynamic_pointer_cast<P2lUnitTask>(context->GetTask());
    if (!task || !task->index_wrapper) {
      LOG_EVERY_N(ERROR, 100000) << Name() << "task is NULL.";
      return -1;
    }
    auto self_context = task->p2l_ad_op_ctx;
    if (!self_context) {
      LOG(ERROR) << Name() << "self_context is NULL.";
      return -1;
    }
    self_context->reason = task->reason;
    ks::infra::PerfUtil::IntervalLogStash(1, kCommonPerfNameSpace,
        kP2lAdOpAction, "begin_p2l_unit_id_size", "0");
    BgContext* bg_context = BgContext::Instance();
    if (!bg_context) {
      return 0;
    }
    // TODO(yizhou03): 打点验证, 验证完后删除
    ks::infra::PerfUtil::IntervalLogStash(task->ad_infos.size(), kCommonPerfNameSpace, kP2lAdOpAction,
                                          "ad_infos.size");
    if (task->ad_infos.size() > 1) {
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, kP2lAdOpAction, "ad_infos.size > 1");
    }
    LOG(INFO) << "ad_infos.size: " << task->ad_infos.size();

    {
      std::vector<int64_t> unit_id_vec;
      unit_id_vec.reserve(task->ad_infos.size());
      for (int i = 0; i < task->ad_infos.size(); i++) {
        unit_id_vec.push_back(task->ad_infos[i].unit_id);
      }
      GetForwardIndexData("wt_unit", unit_id_vec, task->index_wrapper->forward_index_context);
      GetForwardIndexData("ad_dsp_unit", unit_id_vec, task->index_wrapper->forward_index_context);
    }

    for (int i = 0; i < task->ad_infos.size(); i++) {
      if (util::IsDebugUnit(task->ad_infos[i].unit_id)) {
        self_context->debug_unit = task->ad_infos[i].unit_id;
        LOG(INFO) << Name() << "  debug_unit:" << task->ad_infos[i].unit_id;
      }
      if (task->trigger_type == ks::ad_ems::TriggerType::Index) {
        LOG(INFO) << Name() << "  index trigger unit:" << task->ad_infos[i].unit_id;
      }
      // 设置已经在投的创意素材信息
      SetAllIndexInfo(task, task->ad_infos[i].unit_id);
      int ret = FillOneAd(context, task, task->ad_infos[i].unit_id, task->trigger_type);
      if (ret == -1 && task->trigger_type == ks::ad_ems::TriggerType::Index) {
        ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
                                      kP2lAdOpAction, "photo_live_produce_number");
        AdContext msg;
        msg.unit_id = task->ad_infos[i].unit_id;
        msg.times = task->ad_infos[i].times;
        bg_context->Put(msg, true);
      }
    }
    ks::infra::PerfUtil::IntervalLogStash(self_context->p2l_ads_.size(), kCommonPerfNameSpace,
        kP2lAdOpAction, "end_p2l_unit_id_size", "0");
  } else {
      return -1;
  }

  return 0;
}

void P2lAdOp::SetAllIndexInfo(std::shared_ptr<P2lUnitTask> tasks,
                     const int64_t unit_id) {
  if (tasks == nullptr) {
    return;
  }
  auto index = tasks->index_wrapper;
  td::Unit unit_base  = index->Get<Unit>(unit_id);
  if (!unit_base.IsValid()) {
    LOG_IF(INFO, 1000) << Name() << " unit_id:" << unit_id
      << " get Unit failed";
    return;
  }
  auto enable_set_index_info = EmsKconfUtil::enableSetIndexInfo();
  if (enable_set_index_info && !enable_set_index_info->IsOnFor(unit_base.campaign_id)) {
    return;
  }
  // 获取计划下在投创意
  InvertDataManager::Instance().GetGridPhotoCreativeMapByCampaign(unit_base.campaign_id,
                                &tasks->campaign_photo_creative_num_map);
  InvertDataManager::Instance().GetGridPhotoValidCreativeMapByCampaign(unit_base.campaign_id,
                             &tasks->campaign_photo_valid_creative_num_map);
  InvertDataManager::Instance().GetGridPhotoCreativeMapByUnit(unit_id,
                             &tasks->unit_photo_creative_num_map);
  InvertDataManager::Instance().GetGridCreativeListByUnitId(unit_id,
                             &tasks->unit_creative_list);
  LOG_IF(INFO, 100) << Name() << " unit_id:" << unit_id
                     << "campaign_id: " << unit_base.campaign_id
                     << "campaign_photo_size: "
                     << tasks->campaign_photo_creative_num_map.size()
                     << "unit_photo_size: " << tasks->unit_photo_creative_num_map.size()
                     << "unit_creative_list: "
                     << tasks->unit_creative_list.size();
}

int P2lAdOp::FillOneAd(Context* context, std::shared_ptr<P2lUnitTask> task,
    int64_t unit_id, int64_t trigger_type) {
  if (!task) {
    return -2;
  }
  auto index = task->index_wrapper;
  auto self_context = task->p2l_ad_op_ctx;
  if (!self_context) {
    LOG(ERROR) << Name() << "self_context is NULL.";
    return -2;
  }
  bool is_debug = false;
  if (util::IsDebugUnit(unit_id)) {
    is_debug = true;
  }
  kuaishou::ad::ems::EmsTraceLog trace_log;
  trace_log.set_log_timestamp_ms(base::GetTimestamp());
  auto* trigger_task_info = trace_log.mutable_trigger_task_info();
  trigger_task_info->set_trigger_name(task->TriggerName());
  trace_log.set_tag("p2l_ad_op_default");
  ks::ad_base::ScopeGuard scope_guard;
  auto start_ts = base::GetTimestamp();
  scope_guard += [&] () {
    int64 tt = base::GetTimestamp() - start_ts;
    if (trace_log.tag() != "") {
      TraceLogService::Instance().AsyncSend(trace_log);
    }
    ks::infra::PerfUtil::IntervalLogStash(tt, kCommonPerfNameSpace,
                          "op_run_p2l_ad_op", Name(), ShardMgr::Instance().ShardName());
  };
  self_context->trigger_type = trigger_type;
  td::Unit unit_base  = index->Get<Unit>(unit_id);
  trace_log.set_can_put_unit_id(unit_id);
  if (!unit_base.IsValid()) {
    LOG_IF(INFO, is_debug) << Name() << " unit_id:" << unit_id
      << " get Unit failed";
    trace_log.set_tag("p2l_ad_op_unit_invalid");
    return -1;
  }
  if (unit_base.unit_type == kuaishou::ad::AdEnum_UnitType_ADVANCED_PROGRAMMED_UNIT) {
    LOG_IF(INFO, is_debug) << Name() << " unit_id:" << unit_id
      << " unit_type is AdEnum_UnitType_ADVANCED_PROGRAMMED_UNIT";
    trace_log.set_tag("p2l_ad_op_error_unit_type");
    return -2;
  }
  kuaishou::ad::tables::Account account;
  if (!index->Get<Account>(unit_base.account_id, &account) || account.id() == 0) {
    LOG_IF(INFO, is_debug) << Name() << " unit_id:" << unit_id
      << " get Account failed: " << unit_base.account_id;
    trace_log.set_tag("p2l_ad_op_no_account");
    return -2;
  }
  auto account_id = account.id();
  if (!ShardMgr::Instance().AccountInCurrentShard(account_id)) {
    LOG_IF(INFO, is_debug) << Name() << " unit_id:" << unit_id
      << ", account is not in current shard!, account_id:" << account_id;
    LOG_EVERY_N(INFO, 1000000) << Name()  << " account is not in current shard!, account_id:" << account_id
      << " unit_id:" << unit_id;
    trace_log.set_tag("");
    return -2;
  }
  kuaishou::ad::tables::Campaign campaign;
  if (!index->Get<Campaign>(unit_base.campaign_id, &campaign) || campaign.id() == 0) {
    LOG_IF(INFO, is_debug) << Name() << " unit_id:" << unit_id
      << " get campaign failed: " << unit_base.campaign_id;
    trace_log.set_tag("p2l_ad_op_no_campaign");
    return -1;
  }
  // 全站推广, 素材追投的单元，处于加速探索的过程中，不需要补充创意
  if (util::IsValidStoreSceneOrientedType(campaign.scene_oriented_type())) {
    kuaishou::ad::tables::Unit unit_pb;
    if (index->Get<Unit>(unit_id, &unit_pb)) {
      if (unit_pb.unit_support_info().explore_ext_type() == kuaishou::ad::AdEnum::MATERIAL_EXPLORE_TYPE) {
        int64_t now_ms = base::GetTimestamp() / 1000;
        int32_t explore_bid_type = 0;
        // 先判断状态
        if (unit_base.IsInAccExploreStatus(now_ms, &explore_bid_type)) {
          LOG(INFO) << "unit_id:" << unit_id
            << " is IsValidStoreSceneOrientedType && MATERIAL_EXPLORE_TYPE && IsInAccExploreStatus, Skip";
          trace_log.set_tag("p2l_ad_op_in_explore");
          return -2;
        }
      }
    }
  }
  // 非直播推广过滤
  if (!util::AdmitInnerP2LTask(campaign.type())) {
    LOG_IF(INFO, is_debug) << Name() << " unit_id:" << unit_id << "campaign is not live";
    LOG_EVERY_N(INFO, 1000) << Name() << " unit_id:" << unit_id << "campaign is not live";
    trace_log.set_tag("p2l_ad_op_error_p2l");
    return -2;
  }
  // 不是系统优选或者系统补位广告
  if (!util::IsValidCreativeBuildType(unit_base.creative_build_type)) {
    LOG_IF(INFO, is_debug) << Name()  << " unit_id:" << unit_id
          << " creative_build_type is error: " << unit_base.creative_build_type;
    trace_log.set_tag("p2l_ad_op_error_creative_build_type");
    return -2;
  }

  if (util::IsEspMobile(account.account_type(), campaign.type())) {
    if (campaign.scene_oriented_type() != kuaishou::ad::AdEnum::ORIENTED_ESP_MOBILE_HOSTING) {
      if (unit_base.fanstop_item_type == kuaishou::ad::AdEnum::ITEM_LIVE) {
        LOG_IF(ERROR, is_debug) << Name() << " unit_id:" << unit_id
          << ", photo2live admit.";
        trace_log.set_tag("p2l_ad_op_error_mobile");
        return -2;
      }
    }
  }
  int64_t live_stream_id = 0;
  kuaishou::ad::AdActionType ocpx_action_type = kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
  ocpx_action_type = unit_base.ocpx_action_type;
  if (trigger_type == ks::ad_ems::TriggerType::Time) {
    kuaishou::ad::tables::LiveStreamUserInfo live_stream_info;
    if (!index->Get<LiveStreamUserInfo>(unit_base.live_user_id, &live_stream_info)) {
      LOG_IF(INFO, is_debug) << Name() << " get LiveStreamUserInfo failed: " << unit_id;
      trace_log.set_tag("p2l_ad_op_not_living");
      return -2;
    }
    live_stream_id = live_stream_info.live_stream_id();
    auto emsLiveAccountList = EmsKconfUtil::emsLiveAccountList();
    if (emsLiveAccountList->find(unit_base.account_id) !=
        emsLiveAccountList->end()) {
      LOG_IF(INFO, is_debug) << Name() << " account in emsLiveAccountList:" << unit_base.account_id;
      LOG_EVERY_N(INFO, 1000) << Name() << " account in emsLiveAccountList:" << unit_base.account_id;
      return -2;
    }
    int64 now_tm = base::GetTimestamp() / 1000;
    int64 event_time = live_stream_info.event_time();
    // 没有直播
    if (!live_stream_info.is_live()) {
      LOG_IF(INFO, is_debug) << Name() << " no in live :" << unit_id;
      trace_log.set_tag("p2l_ad_op_not_living");
      return -2;
    }
    // 如果直播没有达到要求时间
    if (now_tm - event_time < 0) {
      LOG_IF(INFO, is_debug) << Name() << " live time less";
      LOG_EVERY_N(WARNING, 1000) << Name() << " live time less";
      trace_log.set_tag("p2l_ad_op_not_living_time");
      return -2;
    }
  } else {
    kuaishou::ad::tables::LiveStreamUserInfo live_stream_info;
    if (!index->Get<LiveStreamUserInfo>(unit_base.live_user_id, &live_stream_info)) {
      LOG_IF(INFO, is_debug) << Name() << " get LiveStreamUserInfo failed: " << unit_id;
    }
    live_stream_id = live_stream_info.live_stream_id();

    if (unit_base.creative_build_type == kuaishou::ad::AdEnum::HIGHLIGHT_FLASH_BUILD_TYPE) {
      LOG_IF(INFO, is_debug) << Name() << " unit_id:" << unit_id << " HIGHLIGHT_FLASH_BUILD_TYPE Skip";
      trace_log.set_tag("p2l_ad_op_highlight");
      return -2;
    }
  }
  if (!FLAGS_enable_bid_no_diff_switch && !EnvManager::Instance().IsCandidateDefault()) {
    if (CacheDataManager::Instance().HitEspAddCreativeError(unit_id)) {
      LOG_IF(INFO, is_debug) << Name() << " unit_id:" << unit_id << " HitEspAddCreativeError.";
      trace_log.set_tag("p2l_ad_op_hit_esp_add");
      return -3;
    }
  }
  bool hosting_live_multi_material_author = IsHostingLiveMultiMaterialAuthor(account.user_id() % 100);
  // 冷启动素材单元单独给 quota
  bool is_cold_unit = IsColdUnit(campaign, unit_id, index);
  bool is_multi_material = task->is_multi_material_unit;
  bool is_relation_item_unit = IsRelationItemUnit(campaign, unit_id, index);
  std::vector<td::Creative> creative_list;
  InvertDataManager::Instance().GetCreativeListByUnitWrapper(unit_id, &creative_list);
  int64_t sys_num = GetSysNum(task, creative_list, unit_base);
  int64_t live_item_card_shadow_num =  GetLiveItemCardShadowNum(task, creative_list, unit_base);
  int64_t other_num = GetOtherNum(task, creative_list, unit_base);
  int64_t live_num = GetLiveNum(task, creative_list, unit_base);
  int64_t photo_limit_num = 15;
  int64_t live_limit_num = 0;
  kuaishou::ad::AdEnum_AutoDeliverType auto_deliver_type = GetAutoDeliverType(campaign, unit_base);
  auto scene_oriented_type = campaign.scene_oriented_type();
  int64_t log_freq = 1000;
  if (FLAGS_enable_bid_no_diff_switch) {
    log_freq = 1;
  }
  // 从 redis 拿 unit 下已经创建的 creative 数, 与索引中数区别为审核状态 & 创意裁剪产生的 diff
  std::unordered_set<int64_t> valid_creative_set;
  if (util::GetValidUnitCidInfo(unit_id, &valid_creative_set) &&
      valid_creative_set.size() > sys_num) {
    sys_num = valid_creative_set.size();
  }
  if (task->unit_creative_list.size() > sys_num) {
    sys_num = task->unit_creative_list.size();
  }
  LOG_EVERY_N(INFO, log_freq) << "account_id:" << account_id << ", campaign_id:" << campaign.id()
    << ", request_id:" << task->TaskRequestId()
    << ", unit_id:" << unit_id << ", campaign_type:" << campaign.type()
    << ", ocpx_action_type:" << unit_base.ocpx_action_type
    << ", sys_num:" << sys_num << ", other_num:" << other_num
    << ", auto_deliver_type:" << auto_deliver_type
    << ", sys_num: " << sys_num
    << ", unit_creative_list: " << task->unit_creative_list.size()
    << ", valid_creative_set: " << valid_creative_set.size();
  if (auto_deliver_type == kuaishou::ad::AdEnum::PROGRAMMATIC_PHOTO) {
    photo_limit_num = EmsKconfUtil::maxSysCreativeNum() - sys_num;
    // 移动端创意 QUOTA
    if (scene_oriented_type == kuaishou::ad::AdEnum::ORIENTED_ESP_MOBILE_HOSTING) {
      photo_limit_num = EmsKconfUtil::maxMobileHostingLiveCreativeLimitNum() - sys_num;
      if (EmsKconfUtil::mobileHostingLevelQuotaTail() &&
          EmsKconfUtil::mobileHostingLevelQuotaTail()->IsOnFor(campaign.id())) {
        int64_t mobile_quota = GetMobileLiveHostingCreativeQuota(task, campaign.id(), account.user_id());
        photo_limit_num = mobile_quota - sys_num;
        LOG_EVERY_N(INFO, 1) << " Mobile lvl quota author " << account.user_id()
                                << " unit_id " << unit_id
                                << " live limit_num " << mobile_quota
                                << " sys_num " << sys_num
                                << " photo_limit_num" << photo_limit_num;
      }
      auto hosting_config = util::GetInnerHostingStrategyConfigs("MOBILE", campaign.id());
      if (hosting_config.enable_unit_diversity_control() &&
          hosting_config.mobile_unit_creative_quota() > 0) {
        photo_limit_num = hosting_config.mobile_unit_creative_quota() - sys_num;
      }
    }
    // 直播托管与直播全站
    if (scene_oriented_type == kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT
        || scene_oriented_type == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE) {
      photo_limit_num = EmsKconfUtil::maxHostingLiveCreativeV2Num() - sys_num;
      if (scene_oriented_type == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE) {
        photo_limit_num = EmsKconfUtil::maxStoreWideLiveCreativeNum() - sys_num;
      }
      if (is_cold_unit) {
        photo_limit_num = EmsKconfUtil::maxHostingLiveColdCreativeNum() - sys_num;
      }
      if (hosting_live_multi_material_author && is_multi_material) {
        photo_limit_num = EmsKconfUtil::maxHostingLiveMultiMaterialCreativeNum() - sys_num;
      }
      if (is_relation_item_unit) {
        photo_limit_num = EmsKconfUtil::maxHostingLiveRelationCreativeNum() - sys_num;
      }
      int64_t freq = 1000;
      if (EmsKconfUtil::enableInnerHostingLiveLogFreq()) {
        freq = 1;
      }
      LOG_EVERY_N(INFO, freq) << " LiveHosting quota author: " << account.user_id()
                                << " unit_id: " << unit_id
                                << " scene_oriented_type: " << scene_oriented_type
                                << " sys_num: " << sys_num
                                << " photo_limit_num: " << photo_limit_num
                                << " is_relation_item_unit: " << is_relation_item_unit
                                << " is_cold_unit: " << is_cold_unit
                                << " is_multi_material_unit: " << is_multi_material;
    }
  } else if (auto_deliver_type == kuaishou::ad::AdEnum::LIVE_ITEM_CARD_SHADOW) {
    auto max_live_item_card_shadow_num = EmsKconfUtil::maxLiveItemCardShadowCreativeNum();
    if (EmsKconfUtil::maxLiveItemCardShadowCreativeNumExpTail()->IsOnFor(account_id)) {
      max_live_item_card_shadow_num = EmsKconfUtil::maxLiveItemCardShadowCreativeNumExp();
    }
    photo_limit_num = max_live_item_card_shadow_num - live_item_card_shadow_num;
  } else {
    if (unit_base.creative_build_type == kuaishou::ad::AdEnum::SYSTEM_OPTIMIZATION_BUILD_TYPE ||
      unit_base.creative_build_type == kuaishou::ad::AdEnum::HIGHLIGHT_FLASH_BUILD_TYPE) {
      photo_limit_num = EmsKconfUtil::maxOtherSysCreativeNum() - sys_num;
    } else if (unit_base.creative_build_type == kuaishou::ad::AdEnum::SYSTEM_FILL_BUILD_TYPE) {
      photo_limit_num = 5 - other_num;
    } else {
      photo_limit_num = EmsKconfUtil::maxOtherSysCreativeNum() - sys_num;
    }
  }
  // 搜索托管
  auto search_live_hosting_creative_num_conf = EmsKconfUtil::searchLiveHostingCreativeNumConf();
  if (util::EnableSearchLiveHostingCreativeLimit(scene_oriented_type)) {
    photo_limit_num = search_live_hosting_creative_num_conf->data().GetP2lLimit(account_id) - sys_num;
    live_limit_num = search_live_hosting_creative_num_conf->data().GetLiveLimit(account_id) - live_num;
    LOG_EVERY_N(INFO, 100) << Name() << ", search live hosting"
                           << ", account:" << account_id << ", campaign_id:" << campaign.id()
                           << ", unit_id:" << unit_id << ", photo_limit_num:" << photo_limit_num
                           << ", live_limit_num:" << live_limit_num;
    ks::infra::PerfUtil::CountLogStash(live_limit_num, kCommonPerfNameSpace, kP2lAdOpAction,
                                       "search_live_hosting_live_num");
    if (photo_limit_num <= 0 && live_limit_num <= 0) {
      LOG_IF(INFO, is_debug) << Name() << unit_id << " creative size is full.";
      LOG_EVERY_N(INFO, 1000) << Name() << unit_id << " creative size is full.";
      trace_log.set_tag("p2l_ad_op_photo_limit_num_zero");
      return -2;
    }
  } else if (photo_limit_num <= 0) {
    LOG_IF(INFO, is_debug) << Name() << unit_id << " creative size is full.";
    LOG_EVERY_N(INFO, 1000) << Name() << unit_id << " creative size is full.";
    trace_log.set_tag("p2l_ad_op_photo_limit_num_zero");
    return -2;
  }
  std::string special_unit_type = "";
  if (scene_oriented_type == kuaishou::ad::AdEnum::ORIENTED_ESP_MOBILE_HOSTING) {
    auto hosting_config = util::GetInnerHostingStrategyConfigs("MOBILE", campaign.id());
    if (hosting_config.enable_cold_unit()) {
      special_unit_type = util::GetMobileSpecialUnitType(unit_id);
    }
  }
  // author user 对不上就是代投
  bool is_daitou = campaign.campaign_fanstop_support_info().payer_id() !=
                  campaign.campaign_fanstop_support_info().author_id();
  // 本地有代投授权账户的场景，同一只用 unit 索引中的 live_user_id 字段
  int64_t user_id = account.user_id();
  if (util::IsLspHostingUnit(unit_base.unit_feature, unit_base.creative_build_type)) {
    user_id = unit_base.live_user_id;
  }
  kuaishou::ad::tables::AdDspEcomHostingProject project;
  if (!index->Get<AdDspEcomHostingProject>(unit_base.campaign_id, &project) ||
      project.id() == 0) {
    return -2;
  }
  if (util::IsValidLiveHostingSceneOrientedType(scene_oriented_type) &&
      unit_base.live_user_id > 0 &&
      (unit_base.live_user_id != user_id || project.put_type() == 1) &&
      EmsKconfUtil::helpPayUserTails()->IsOnFor(user_id)) {
    is_daitou = true;
    user_id = unit_base.live_user_id;
  }
  trace_log.set_tag("p2l_ad_op_photo_limit_num_" + std::to_string(photo_limit_num));
  self_context->p2l_ads_.push_back({task->TaskRequestId(), special_unit_type, unit_id,
                                    unit_base.account_id, user_id,
                                    unit_base.campaign_id, campaign.combo_order_id(),
                                    live_stream_id,
                                    campaign.scene_oriented_type(), photo_limit_num, live_limit_num,
                                    campaign.promotion_type(), campaign.type(),
                                    account.account_type(), ocpx_action_type,
                                    unit_base.creative_build_type,
                                    auto_deliver_type, unit_base.fanstop_item_type,
                                    is_daitou, unit_base.unit_feature});
  // 打日志
  if ((scene_oriented_type == kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT
      || scene_oriented_type == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE)) {
    LOG_EVERY_N(INFO, 100) << ", user_id:" << user_id << ", campaign_id:" << campaign.id()
        << ", unit_id:" << unit_id << ", is_cold_unit:" << is_cold_unit
        << ", photo_limit_num:" << photo_limit_num;
    if (is_cold_unit) {
      LOG_EVERY_N(INFO, 100) << ", user_id:" << user_id << ", campaign_id:" << campaign.id()
          << ", unit_id:" << unit_id << ", is_cold_unit:" << is_cold_unit
          << ", photo_limit_num:" << photo_limit_num;
    }
    if (is_multi_material) {
      LOG_EVERY_N(INFO, 100) << "hosting_live_multi_material_author:" << hosting_live_multi_material_author
      << ", user_id:" << user_id << ", campaign_id:" << campaign.id()
      << ", unit_id:" << unit_id << ", is_multi_material:" << is_multi_material
      << ", photo_limit_num:" << photo_limit_num;
  }
  }
  return 0;
}

bool P2lAdOp::IsHostingLiveMultiMaterialAuthor(int64_t user_id) {
  bool hosting_live_multi_material_author = false;
  auto hosting_live_multi_material_author_tail =
                  EmsKconfUtil::hostingLiveMultiMaterialCreativeQuotaAuthorTail();
  if (hosting_live_multi_material_author_tail->find(user_id % 100) !=
                  hosting_live_multi_material_author_tail->end()) {
    hosting_live_multi_material_author = true;
  }
  return hosting_live_multi_material_author;
}

bool P2lAdOp::IsColdUnit(const kuaishou::ad::tables::Campaign& campaign, int64_t unit_id,
                         std::shared_ptr<ForwardIndexWrapper> index) const {
  bool is_cold_unit = false;
  if (!FLAGS_enable_bid_no_diff_switch && (
      campaign.scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT ||
      campaign.scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE)) {
    std::string key =
        absl::Substitute("hosting_live_unit_is_cold_$0_$1", campaign.id(), unit_id);
    RedisOption basic_option;
    basic_option.set_table(*EmsKconfUtil::hostingAdDataRedis());
    basic_option.set_timeout(10);
    BasicParams basic_params;
    AsyncRedisHelper async_redis_helper;
    basic_option.set_key(key);
    basic_params.Init(basic_option);
    async_redis_helper.AsyncGet(&basic_params);
    std::string value;
    async_redis_helper.Wait(&basic_params, &value);
    if (value == "1") {
      is_cold_unit = true;
    }
  }
  if (is_cold_unit ||
      util::GetHostingAlgoTagInnerWorker(unit_id, index) == kuaishou::ad::AdEnum::NEW_PHOTO_TAG) {
    is_cold_unit = true;
    LOG(INFO) << Name() << " NEW_PHOTO_TAG unit_id:" << unit_id
                        << "old tag:" << is_cold_unit;
  }
  return is_cold_unit;
}

bool P2lAdOp::IsRelationItemUnit(const kuaishou::ad::tables::Campaign& campaign, int64_t unit_id,
                                 std::shared_ptr<ForwardIndexWrapper> index) const {
  bool is_relation_item_unit = false;
  if (!FLAGS_enable_bid_no_diff_switch && (
      campaign.scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT ||
      campaign.scene_oriented_type() == kuaishou::ad::AdEnum::ORIENTED_STORE_WIDE_LIVE)) {
    RedisOption basic_option;
    basic_option.set_table(*EmsKconfUtil::hostingAdDataRedis());
    basic_option.set_timeout(10);
    BasicParams basic_params;
    AsyncRedisHelper async_redis_helper;
    // 判断是否是关联物料素材
    std::string item_key =
        absl::Substitute("hosting_live_unit_relation_item_$0_$1", campaign.id(), unit_id);
    basic_option.set_key(item_key);
    basic_params.Init(basic_option);
    async_redis_helper.AsyncGet(&basic_params);
    std::string value;
    async_redis_helper.Wait(&basic_params, &value);
    if (value == "1") {
      is_relation_item_unit = true;
    }
  }
  if (is_relation_item_unit ||
      util::GetHostingAlgoTagInnerWorker(unit_id, index) == kuaishou::ad::AdEnum::RELATE_PHOTO_TAG) {
    is_relation_item_unit = true;
    LOG(INFO) << Name() << " RELATE_PHOTO_TAG unit_id:" << unit_id
                        << "old tag:" << is_relation_item_unit;
  }
  return is_relation_item_unit;
}

int64_t P2lAdOp::GetSysNum(
    std::shared_ptr<P2lUnitTask> task,
    const std::vector<td::Creative>& creative_list,
    const tables::data_gate::Unit& unit_base) const {
  auto index = task->index_wrapper;;
  int64_t sys_num = 0;
  for (const auto& creative : creative_list) {
    if (!creative.IsValid()) {
      continue;
    }
    if (creative.creative_type == kuaishou::ad::AdEnum::ESP_SYSTEM_CREATE &&
        (unit_base.creative_build_type == kuaishou::ad::AdEnum::SYSTEM_OPTIMIZATION_BUILD_TYPE ||
         unit_base.creative_build_type == kuaishou::ad::AdEnum::SMART_GOODS_CUSTOM_MATERIALS_TYPE ||
         unit_base.creative_build_type == kuaishou::ad::AdEnum::HIGHLIGHT_FLASH_BUILD_TYPE ||
         unit_base.creative_build_type == kuaishou::ad::AdEnum::ESP_PROGRAMMED_BUILD_TYPE)) {
      sys_num++;
    } else if (creative.creative_type == kuaishou::ad::AdEnum::LSP_SYSTEM_CREATE &&
      (unit_base.creative_build_type == kuaishou::ad::AdEnum::SYSTEM_OPTIMIZATION_BUILD_TYPE ||
       unit_base.creative_build_type == kuaishou::ad::AdEnum::SMART_GOODS_CUSTOM_MATERIALS_TYPE ||
       unit_base.creative_build_type == kuaishou::ad::AdEnum::SYSTEM_FILL_BUILD_TYPE ||
       unit_base.creative_build_type == kuaishou::ad::AdEnum::HIGHLIGHT_FLASH_BUILD_TYPE ||
       unit_base.creative_build_type == kuaishou::ad::AdEnum::LSP_PROGRAMMED_BUILD_TYPE)) {
      sys_num++;
    }
  }
  return sys_num;
}

int64_t P2lAdOp::GetLiveItemCardShadowNum(
    std::shared_ptr<P2lUnitTask> task,
    const std::vector<td::Creative>& creative_list,
    const tables::data_gate::Unit& unit_base) const {
  auto index = task->index_wrapper;
  int64_t sys_num = 0;
  for (const auto& creative : creative_list) {
    if (!creative.IsValid()) {
      continue;
    }
    sys_num++;
  }
  return sys_num;
}

int64_t P2lAdOp::GetOtherNum(
    std::shared_ptr<P2lUnitTask> task,
    const std::vector<td::Creative>& creative_list,
    const tables::data_gate::Unit& unit_base) const {
  int64_t other_num = 0;
  for (const auto& creative : creative_list) {
    if (!creative.IsValid()) {
      continue;
    }
    if (creative.creative_type == kuaishou::ad::AdEnum::ESP_SYSTEM_CREATE &&
        unit_base.creative_build_type == kuaishou::ad::AdEnum::SYSTEM_FILL_BUILD_TYPE) {
      other_num++;
    }
  }
  return other_num;
}

int64_t P2lAdOp::GetLiveNum(
    std::shared_ptr<P2lUnitTask> task,
    const std::vector<td::Creative>& creative_list,
    const tables::data_gate::Unit& unit_base) const {
  int64_t live_num = 0;
  for (const auto& creative : creative_list) {
    if (!creative.IsValid()) {
      continue;
    }
    if (creative.live_creative_type  == kuaishou::ad::AdEnum::LIVE_STREAM_CREATIVE_TYPE &&
        (unit_base.creative_build_type == kuaishou::ad::AdEnum::SYSTEM_OPTIMIZATION_BUILD_TYPE ||
         unit_base.creative_build_type == kuaishou::ad::AdEnum::SMART_GOODS_CUSTOM_MATERIALS_TYPE)) {
      live_num++;
    }
  }
  return live_num;
}

int64_t P2lAdOp::GetMobileLiveHostingCreativeQuota(
    std::shared_ptr<P2lUnitTask> task,
    int64_t campaign_id, int64_t user_id) {
  int64_t default_num = EmsKconfUtil::maxMobileHostingLiveCreativeLimitNum();
  auto index = task->index_wrapper;
  kuaishou::ad::tables::AdDspEcomHostingProject project;
  if (!index->Get<AdDspEcomHostingProject>(campaign_id, &project) || project.id() == 0) {
    return default_num;
  }
  auto mobile_hosting_lvl_quota_config = EmsKconfUtil::mobileHostingLevelQutaConfig();
  if (!mobile_hosting_lvl_quota_config) {
    return default_num;
  }
  std::unordered_set<int64_t> photo_list;
  util::ParseJsonIntList(project.photo_list(), &photo_list);
  int64_t min_thresh = 10;
  auto campaign_tail = campaign_id % 10;
  std::string thresh_key = absl::Substitute("reduce_$0", campaign_tail);
  auto iter = mobile_hosting_lvl_quota_config->find(thresh_key);
  if (iter != mobile_hosting_lvl_quota_config->end()) {
    min_thresh = iter->second;
  }
  if (photo_list.size() <= min_thresh) {
    default_num = std::min(default_num, static_cast<int64_t>(photo_list.size()));
  } else  {
    int64_t level = ks::ems::HostingMobileAuthorLevelData::GetInstance()->GetMobileLiveAuthorLevel(user_id);
    std::string lvl_key = absl::Substitute("lvl_$0_tail_$1", level, campaign_tail);
    int64_t quota_bias = 0;
    auto quota_iter = mobile_hosting_lvl_quota_config->find(lvl_key);
    if (quota_iter != mobile_hosting_lvl_quota_config->end()) {
      quota_bias = quota_iter->second;
    }
    default_num += quota_bias;
    ks::infra::PerfUtil::IntervalLogStash(quota_bias, kCommonPerfNameSpace,
                                          "quota_bias_diff_live", std::string(lvl_key));
  }
  return default_num;
}

kuaishou::ad::AdEnum_AutoDeliverType P2lAdOp::GetAutoDeliverType(
    const kuaishou::ad::tables::Campaign& campaign,
    const td::Unit& unit_base) {
  auto scene_oriented_type = campaign.scene_oriented_type();
  kuaishou::ad::AdEnum_AutoDeliverType auto_deliver_type = unit_base.auto_deliver_type;
  if (unit_base.creative_build_type == kuaishou::ad::AdEnum::SYSTEM_OPTIMIZATION_BUILD_TYPE ||
        unit_base.creative_build_type == kuaishou::ad::AdEnum::ESP_PROGRAMMED_BUILD_TYPE ||
        unit_base.creative_build_type == kuaishou::ad::AdEnum::SYSTEM_FILL_BUILD_TYPE) {
    auto_deliver_type = kuaishou::ad::AdEnum::PROGRAMMATIC_PHOTO;
  }
  if (scene_oriented_type  == kuaishou::ad::AdEnum::ORIENTED_LIVE_HOSTING_PROJECT ||
      scene_oriented_type == kuaishou::ad::AdEnum::ORIENTED_SEARCH_WORD_LIVE_PROJECT) {
    auto_deliver_type = kuaishou::ad::AdEnum::PROGRAMMATIC_PHOTO;
  }
  if (unit_base.auto_deliver_type == kuaishou::ad::AdEnum::LIVE_ITEM_CARD_SHADOW) {
    auto_deliver_type = kuaishou::ad::AdEnum::LIVE_ITEM_CARD_SHADOW;
  }
  // 本地推
  if (util::IsLspHostingUnit(unit_base.unit_feature, unit_base.creative_build_type)) {
    auto_deliver_type = kuaishou::ad::AdEnum::PROGRAMMATIC_PHOTO;
  }
  return auto_deliver_type;
}

}  // namespace ad_ems
}  // namespace ks
