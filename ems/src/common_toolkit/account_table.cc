#include <optional>
#include <vector>
#include <utility>
#include <string>
#include <unordered_map>
#include "teams/ad/grid/sdk/grid_client.h"
#include "teams/ad/ems/src/common_toolkit/account_table.h"
#include "teams/ad/ems/src/util/kconf.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"  // AdEnum
#include "teams/ad/index_builder/tables/data_gate/account.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/index_manager.h"
#include "perfutil/perfutil.h"

namespace ks {
namespace ad_ems {

DECLARE_bool(enable_grid_sdk_cache);
namespace td = ::tables::data_gate;
using tables::data_gate::Account;

void GetAccountTable::PadFromPb(kuaishou::ad::tables::Account& account, EmsAccount& result) {
  result.id = account.id();
  result.user_id = account.user_id();
  result.agent_id = account.agent_id();
  result.city_product_id = account.city_product_id();
  result.create_time = account.create_time();
  result.day_budget = account.day_budget();
  result.create_source = account.create_source();
  result.hard_auth_status = account.hard_auth_status();
  result.hard_review_status = account.hard_review_status();
  result.put_status = account.put_status();
  result.review_status = account.review_status();
  result.frozen_status = account.frozen_status();
  result.corporation_name = account.corporation_name();
  result.license_no  = account.license_no();
  result.product_name = account.product_name();
  result.constraint_info_ocpx_action_type.clear();
  result.constraint_info_value.clear();
  if (account.has_account_support_info()) {
    const auto& account_support_info = account.account_support_info();
    result.account_auto_manage = account_support_info.account_auto_manage();
    result.auto_manage_type = account_support_info.auto_manage_type();
    if (account_support_info.has_extend_fields()) {
      const auto& extend_fields = account_support_info.extend_fields();
      for (int i = 0; i < extend_fields.constraint_info_ocpx_action_type_size(); ++i) {
        result.constraint_info_ocpx_action_type.push_back(extend_fields.constraint_info_ocpx_action_type(i));
      }
      for (int i = 0; i < extend_fields.constraint_info_value_size(); ++i) {
        result.constraint_info_value.push_back(extend_fields.constraint_info_value(i));
      }
    }
  }
}

void GetAccountTable::PadFromStruct(const td::Account& account, EmsAccount& result) {
  result.id = account.id;
  result.user_id = account.user_id;
  // result.agent_id             // td::Account 没有这个数据
  result.city_product_id = account.city_product_id;
  // result.create_time          // td::Account 没有这个数据
  result.day_budget = account.day_budget;
  // result.create_source        // td::Account 没有这个数据
  // result.hard_auth_status     // td::Account 没有这个数据
  // result.hard_review_status  // td::Account 没有这个数据
  result.account_auto_manage = account.account_auto_manage;
  result.auto_manage_type = account.auto_manage_type;
  result.put_status = account.put_status;
  result.review_status = account.review_status;
  result.frozen_status = account.frozen_status;
  // result.corporation_name;  // td::Account 没有这个数据
  // result.license_no;       // td::Account 没有这个数据
  // result.product_name;    // td::Account 没有这个数据
  // LOG(INFO) << "PadFromStruct:" << result.Desc();
}

bool GetAccountTable::GetAccountData(const int64_t query_id, EmsAccount& result,
  const bool is_struct, const int special_condition) {
  Querykey query_key;
  query_key.i64 = query_id;
  std::vector<Querykey> querykey_vec{query_key};
  std::vector<GridData> resp;
  // 调用父类的接口, 进行正排数据获取
  int ret = BatchGetTable(querykey_vec, resp);
  if (!ret) {
    return false;
  }
  if (resp.size() != 1) {
    return false;
  }
  auto& single_table = resp[0];
  // 对一张表进行解析
  TransformData(query_key.i64, single_table, result);
  return true;
}

void GetAccountTable::TransformData(const std::vector<Querykey>& req_batch, const GridData& input,
  std::unordered_map<int64_t, std::optional<td::Account>>& data_map,
  std::unordered_map<int64_t, std::optional<kuaishou::ad::tables::Account>>& pb_data_map) {
  if (input.RowSize() != req_batch.size()) {
    return;
  }
  // 这里遍历两次, 分别写 data_map 和 pb_data_map
  Transform2Td(req_batch, input, data_map);
  Transform2Pb(req_batch, input, pb_data_map);
}

void GetAccountTable::Transform2Td(const std::vector<Querykey>& req_batch, const GridData& input,
  std::unordered_map<int64_t, std::optional<td::Account>>& data_map) {
  for (auto i = 0; i < req_batch.size(); i++) {
    auto& req_item = req_batch[i];
    if (!input.IsValid(i)) {
      data_map.emplace(req_item.i64, std::nullopt);
      LOG(INFO) << "Transform2Td, i " << i << ", not Valid. account_id=" << req_item.i64;
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "Account_td_diff", "input_invalid");
      continue;
    }
    td::Account result;
    // 解析字段
    GRID_DATA_DIFF(Account)
    SET_TD_GRID_FIELD(Int64, id);  // 0
    SET_TD_GRID_FIELD(Int64, user_id);  // 1
    SET_TD_GRID_FIELD(Int64, city_product_id);  // 3
    SET_TD_GRID_FIELD(Int64, day_budget);  // 5
    SET_TD_GRID_FIELD_STATIC_CAST(Int32, account_auto_manage, kuaishou::ad::AdEnum::AccountAutoManage)  // 9
    SET_TD_GRID_FIELD_STATIC_CAST(Int32, auto_manage_type, kuaishou::ad::AdEnum::AutoManageType);  // 10
    SET_TD_GRID_FIELD_STATIC_CAST(Int32, put_status, kuaishou::ad::AdEnum::PutStatus);  // 11
    SET_TD_GRID_FIELD_STATIC_CAST(Int32, review_status, kuaishou::ad::AdEnum::ReviewStatus);  // 12
    SET_TD_GRID_FIELD_STATIC_CAST(Int32, frozen_status, kuaishou::ad::AdEnum::AdDspAccountFrozenStatus);  // NOLINT 13
    DiffAccountTd(result);
    data_map.emplace(req_item.i64, std::move(result));
  }
}

void GetAccountTable::Transform2Pb(const std::vector<Querykey>& req_batch, const GridData& input,
  std::unordered_map<int64_t, std::optional<kuaishou::ad::tables::Account>>& data_map) {
  for (auto i = 0; i < req_batch.size(); i++) {
    auto& req_item = req_batch[i];
    if (!input.IsValid(i)) {
      data_map.emplace(req_item.i64, std::nullopt);
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "Account_pb_diff", "input_invalid");
      continue;
    }
    kuaishou::ad::tables::Account result;
    // 解析字段
    GRID_DATA_DIFF_PB(Account);
    SET_PB_GRID_FIELD(Int64, id);  // 0
    SET_PB_GRID_FIELD(Int64, user_id);  // 1
    // 针对 join_agent_id 做特殊处理, 请求使用 join_agent_id, 存储使用 agent_id
    // GET_GRID_FIELD(Int64, join_agent_id);  // 2
    if (input.IsInt64(i, _field_info_map["join_agent_id"])) {
      result.set_agent_id(input.GetInt64(i, "join_agent_id"));
    } else {
      LOG(INFO) << "table " << _table_name << " key_id " << req_item.i64 << " has_no join_agent_id";
    }
    SET_PB_GRID_FIELD(Int64, city_product_id);  // 3
    SET_PB_GRID_FIELD(Int64, create_time);  // 4
    SET_PB_GRID_FIELD(Int64, day_budget);  // 5
    SET_PB_GRID_FIELD(Int32, create_source);  // 6
    SET_PB_GRID_FIELD_STATIC_CAST(Int32, hard_auth_status, kuaishou::ad::AdEnum::AuthenticationStatus);  // 7
    SET_PB_GRID_FIELD_STATIC_CAST(Int32, hard_review_status, kuaishou::ad::AdEnum::ReviewStatus);  // 8
    // GET_PB_GRID_FIELD_STATIC_CAST(Int32, account_auto_manage, kuaishou::ad::AdEnum::AccountAutoManage);  // NOLINT 9
    // GET_PB_GRID_FIELD_STATIC_CAST(Int32, auto_manage_type, kuaishou::ad::AdEnum::AutoManageType);  // 10
    auto* account_support_info = result.mutable_account_support_info();
    if (input.IsInt32(i, _field_info_map["account_auto_manage"])) {
      account_support_info->set_account_auto_manage(
        static_cast<kuaishou::ad::AdEnum::AccountAutoManage>(input.GetInt32(i, "account_auto_manage")));
    }
    if (input.IsInt32(i, _field_info_map["auto_manage_type"])) {
      account_support_info->set_auto_manage_type(
        static_cast<kuaishou::ad::AdEnum::AutoManageType>(input.GetInt32(i, "auto_manage_type")));
    }
    SET_PB_GRID_FIELD_STATIC_CAST(Int32, put_status, kuaishou::ad::AdEnum::PutStatus);  // 11
    SET_PB_GRID_FIELD_STATIC_CAST(Int32, review_status, kuaishou::ad::AdEnum::ReviewStatus);  // 12
    SET_PB_GRID_FIELD_STATIC_CAST(Int32, frozen_status, kuaishou::ad::AdEnum::AdDspAccountFrozenStatus);  // NOLINT 13

    SET_PB_GRID_STRING_FIELD(String, corporation_name);  // 14
    SET_PB_GRID_STRING_FIELD(String, license_no);  // 15
    SET_PB_GRID_STRING_FIELD(String, product_name);  // 16

    std::vector<int64_t> constraint_info_ocpx_action_type;
    std::vector<float> constraint_info_value;
    SET_PB_GRID_LIST_FIELD(Int64List, constraint_info_ocpx_action_type)  // 17
    SET_PB_GRID_LIST_FIELD(FloatList, constraint_info_value)  // 18
    auto* extend_fields = result.mutable_account_support_info()->mutable_extend_fields();
    // 先清空
    extend_fields->clear_constraint_info_ocpx_action_type();
    extend_fields->clear_constraint_info_value();
    // 塞 constraint_info_ocpx_action_type
    for (const auto& val : constraint_info_ocpx_action_type) {
      extend_fields->add_constraint_info_ocpx_action_type(val);
    }
    // 塞 constraint_info_value
    for (const auto& val : constraint_info_value) {
      extend_fields->add_constraint_info_value(val);
    }
    DiffAccountPb(result);
    data_map.emplace(req_item.i64, std::move(result));
  }
}

#define DIFF_ACCOUNT_TD_FIELD(field) \
  DiffField("Account", #field, td_struct.field, td_data.field, td_struct.id, td_diff_str, "td"); \

#define DIFF_ACCOUNT_PB_FIELD(field) \
  DiffField("Account", #field, pb_table.field(), pb_data.field(), pb_data.id(), pb_diff_str, "pb");

void GetAccountTable::DiffAccountTd(const td::Account& td_struct) {
  int64_t id = td_struct.id;
  // 1. get origin index
  auto* index = ks::ad_table_lite::IndexManager::GetInstance();
  if (index == nullptr || !index->WaitForReady()) {
    LOG(WARNING) << "DiffAccount index is not Ready.";
    return;
  }
  td::Account td_data = index->Get<td::Account>(id);
  // 2. diff td
  std::string td_diff_str;
  DIFF_ACCOUNT_TD_FIELD(id)
  DIFF_ACCOUNT_TD_FIELD(user_id)
  DIFF_ACCOUNT_TD_FIELD(city_product_id)
  DIFF_ACCOUNT_TD_FIELD(day_budget)
  DIFF_ACCOUNT_TD_FIELD(account_auto_manage)
  DIFF_ACCOUNT_TD_FIELD(auto_manage_type)
  DIFF_ACCOUNT_TD_FIELD(put_status)
  DIFF_ACCOUNT_TD_FIELD(review_status)
  DIFF_ACCOUNT_TD_FIELD(frozen_status)
  // 3. print diff and dot
  if (!td_diff_str.empty()) {
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "Account_td_diff", "total_diff",
                                       std::to_string(td_struct.id));
    LOG(WARNING) << "Account_td_has_diff:"
                 << ", id:" << td_struct.id << ", diff_str:" << td_diff_str;
  }
  ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "Account_td_diff", "diff_ratio",
    td_diff_str.empty() ? "no_diff" : "has_diff");
}

void GetAccountTable::DiffAccountPb(const kuaishou::ad::tables::Account& pb_table) {
  int64_t id = pb_table.id();
  // 1. get origin index
  auto* index = ks::ad_table_lite::IndexManager::GetInstance();
  if (index == nullptr || !index->WaitForReady()) {
    LOG(WARNING) << "DiffAccount index is not Ready.";
    return;
  }
  kuaishou::ad::tables::Account pb_data;
  if (!index->Get<Account>(id, &pb_data) || pb_data.id() == 0) {
    return;
  }
  // 2 diff pb
  // 2.1 diff 外层字段
  std::string pb_diff_str;
  DIFF_ACCOUNT_PB_FIELD(id)
  DIFF_ACCOUNT_PB_FIELD(user_id)
  DIFF_ACCOUNT_PB_FIELD(agent_id)
  DIFF_ACCOUNT_PB_FIELD(city_product_id)
  DIFF_ACCOUNT_PB_FIELD(create_time)
  DIFF_ACCOUNT_PB_FIELD(day_budget)
  DIFF_ACCOUNT_PB_FIELD(create_source)
  DIFF_ACCOUNT_PB_FIELD(hard_auth_status)
  DIFF_ACCOUNT_PB_FIELD(hard_review_status)
  DIFF_ACCOUNT_PB_FIELD(put_status)
  DIFF_ACCOUNT_PB_FIELD(review_status)
  DIFF_ACCOUNT_PB_FIELD(frozen_status)
  DIFF_ACCOUNT_PB_FIELD(corporation_name)
  DIFF_ACCOUNT_PB_FIELD(license_no)
  DIFF_ACCOUNT_PB_FIELD(product_name)
  // 2.2 diff 嵌套字段
  if (pb_table.has_account_support_info() && pb_data.has_account_support_info()) {
    DiffField("Account", "account_support_info.auto_manage_type",
              pb_table.account_support_info().auto_manage_type(),
              pb_data.account_support_info().auto_manage_type(), pb_data.id(), pb_diff_str, "pb");
    DiffField("Account", "account_support_info.account_auto_manage",
              pb_table.account_support_info().account_auto_manage(),
              pb_data.account_support_info().account_auto_manage(), pb_data.id(), pb_diff_str, "pb");
    if (pb_table.account_support_info().has_extend_fields() &&
        pb_data.account_support_info().has_extend_fields()) {
      DiffField("Account", "account_support_info.extend_fields.constraint_info_ocpx_action_type",
                pb_table.account_support_info().extend_fields().constraint_info_ocpx_action_type(),
                pb_data.account_support_info().extend_fields().constraint_info_ocpx_action_type(),
                pb_data.id(), pb_diff_str, "pb");
    }
  } else if (pb_table.has_account_support_info() || pb_data.has_account_support_info()) {
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, absl::StrCat("Account_pb_diff"),
                                       "account_support_info", std::to_string(pb_table.id()));
    pb_diff_str.append(" |Account-account_support_info:pb_one_has_no_value| ");
  }

  // 3. print diff and dot
  if (!pb_diff_str.empty()) {
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "Account_pb_diff", "total_diff",
                                       std::to_string(pb_table.id()));
    LOG(WARNING) << "Account_pb_has_diff:"
                 << ", id:" << pb_table.id() << ", diff_str:" << pb_diff_str;
  }
  ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "Account_pb_diff", "diff_ratio",
                                     pb_diff_str.empty() ? "no_diff" : "has_diff");
}

void GetAccountTable::TransformData(int64_t key, const GridData& input, EmsAccount& result) {
  if (!input.IsValid()) return;
  if (input.RowSize() != 1) return;
  if (!input.IsValid(0))  return;  // 第 i 行是否存在
  // 解析字段
  GET_GRID_FIELD(Int64, id);  // 0
  GET_GRID_FIELD(Int64, user_id);  // 1
  // 针对 join_agent_id 做特殊处理, 请求使用 join_agent_id, 存储使用 agent_id
  // GET_GRID_FIELD(Int64, join_agent_id);  // 2
  if (input.IsInt64(ROW_INDEX, _field_info_map["join_agent_id"])) {
    result.agent_id = input.GetInt64(ROW_INDEX, "join_agent_id");
  } else {
    LOG_EVERY_N(INFO, 1000) << "table " << _table_name << " key_id " << key << " has_no join_agent_id";
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
      "TransformData_error", _table_name, "join_agent_id");
  }
  GET_GRID_FIELD(Int64, city_product_id);  // 3
  GET_GRID_FIELD(Int64, create_time);  // 4
  GET_GRID_FIELD(Int64, day_budget);  // 5

  GET_GRID_FIELD(Int32, create_source);  // 6
  GET_GRID_FIELD(Int32, hard_auth_status);  // 7
  GET_GRID_FIELD(Int32, hard_review_status);  // 8
  GET_GRID_FIELD(Int32, account_auto_manage);  // 9
  GET_GRID_FIELD(Int32, auto_manage_type);  // 10
  GET_GRID_FIELD(Int32, put_status);  // 11
  GET_GRID_FIELD(Int32, review_status);  // 12
  GET_GRID_FIELD(Int32, frozen_status);  // 13

  GET_GRID_PTR_FIELD(String, corporation_name);  // 14
  GET_GRID_PTR_FIELD(String, license_no);  // 15
  GET_GRID_PTR_FIELD(String, product_name);  // 16
  GET_GRID_PTR_FIELD(Int64List, constraint_info_ocpx_action_type)  // 17
  GET_GRID_PTR_FIELD(FloatList, constraint_info_value)  // 18
}

}  // namespace ad_ems
}  // namespace ks
