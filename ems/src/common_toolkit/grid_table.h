#ifndef TEAMS_AD_EMS_SRC_COMMON_TOOLKIT_GRID_TABLE_H_
#define TEAMS_AD_EMS_SRC_COMMON_TOOLKIT_GRID_TABLE_H_

#include <utility>
#include <string>
#include <vector>
#include <iostream>
#include <sstream>
#include <unordered_map>
#include "gflags/gflags.h"
#include "teams/ad/grid/sdk/grid_client.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "base/common/base.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ems/src/common/common.h"
#include "teams/ad/ems/src/util/table_helper.h"  // 通用 Table 辅助函数
#include "teams/ad/ems/src/util/kconf.h"

namespace ks {
namespace ad_ems {
const int ROW_INDEX = 0;  // 定义常量

// input: 输入; result: struct 输出对象; ROW_INDEX: 写入第几行,目前只有 1 行(值 为 0)
// _field_info_map: 将 #field_name 映射为其在 input 中的顺序
#define GET_GRID_FIELD(Type, field_name)  \
  if (input.Is##Type(ROW_INDEX, _field_info_map[#field_name])) { \
      result.field_name = input.Get##Type(ROW_INDEX, #field_name); \
  } else { \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,  \
      "TransformData_error", _table_name, #field_name); \
  }


#define GET_GRID_PTR_FIELD(Type, field_name)  \
  if (input.Is##Type(ROW_INDEX, _field_info_map[#field_name])) { \
      input.Get##Type(ROW_INDEX, #field_name, &(result.field_name)); \
  } else { \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,  \
      "TransformData_error", #field_name); \
  }

#define GRID_DATA_DIFF(Type) \
  auto td_id = req_item.i64; \
  auto* index = ks::ad_table_lite::IndexManager::GetInstance(); \
  td::Type td_data = index->Get<td::Type>(td_id); \
  bool enable_diff = EmsKconfUtil::forwardTableDiff()->count(#Type); \
  std::string td_type_str = #Type;

#define GRID_DATA_DIFF_PB(Type) \
  auto td_id = req_item.i64; \
  auto* index = ks::ad_table_lite::IndexManager::GetInstance(); \
  kuaishou::ad::tables::Type pb_data; \
  index->Get<td::Type>(td_id, &pb_data); \
  bool enable_diff = EmsKconfUtil::forwardTableDiff()->count(#Type); \
  std::string pb_type_str = #Type;


// 将 Grid 数据塞进 tables::data_gate 结构体中
#define SET_TD_GRID_FIELD(Type, field_name)  \
  if (input.Is##Type(i, _field_info_map[#field_name])) { \
      result.field_name = input.Get##Type(i, #field_name); \
  } else { \
  } \
  if (enable_diff && td_data.field_name != result.field_name && \
      EmsKconfUtil::emsDiffWhilteList()->count(absl::StrCat(td_type_str, "#", #field_name)) <= 0) { \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, \
        "data_gate_data_diff", absl::StrCat(td_type_str, "#", #field_name), "diff"); \
    LOG_EVERY_N(INFO, 100) << td_type_str << "#" << #field_name << "_id=" << td_id  \
              << ": local=" << td_data.field_name << ": remote=" << result.field_name; \
  } else if (enable_diff) { \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, \
        "data_gate_data_diff", absl::StrCat(td_type_str, "#", #field_name), "nodiff"); \
  }

// 将 Grid 数据塞进 tables::data_gate 结构体中, 支持强制类型转换
#define SET_TD_GRID_FIELD_STATIC_CAST(Type, field_name, TargetType)        \
  if (input.Is##Type(i, _field_info_map[#field_name])) {                   \
      result.field_name = static_cast<TargetType>(input.Get##Type(i, #field_name)); \
  }

// 将 Grid 数据塞进 tables::data_gate 结构体中
#define SET_TD_GRID_PTR_FIELD(Type, field_name)  \
  if (input.Is##Type(i, _field_info_map[#field_name])) { \
      input.Get##Type(i, #field_name, &(result.field_name)); \
  } else { \
  }

// 将 Grid 数据塞进 pb Message 中
#define SET_PB_GRID_FIELD(Type, field_name)                                                             \
  if (input.Is##Type(i, _field_info_map[#field_name])) {                                                \
    result.set_##field_name(input.Get##Type(i, #field_name));                                           \
  } else {                                                                                              \
  }                                                                                                     \
  if (enable_diff && pb_data.field_name() != result.field_name() &&                                     \
      EmsKconfUtil::emsDiffWhilteList()->count(absl::StrCat(pb_type_str, "#", #field_name)) <= 0) {     \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "data_gate_data_diff_pb",               \
                                       absl::StrCat(pb_type_str, "#", #field_name), "diff");            \
    LOG_EVERY_N(INFO, 100) << pb_type_str << "#" << #field_name << "_id=" << td_id                      \
                           << ": local=" << pb_data.field_name() << ": remote=" << result.field_name(); \
  } else if (enable_diff) {                                                                             \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "data_gate_data_diff_pb",               \
                                       absl::StrCat(pb_type_str, "#", #field_name), "nodiff");          \
  }

// 将 Grid 数据塞进指定 pb Message 中
#define SET_SUB_PB_GRID_FIELD(Type, sub_message, field_name)  \
  if (input.Is##Type(i, _field_info_map[#field_name])) { \
      sub_message->set_##field_name(input.Get##Type(i, #field_name)); \
  } else { \
  }

// 将 Grid 数据塞进指定 pb Message 中, 支持强制类型转换
#define SET_SUB_PB_GRID_FIELD_STATIC_CAST(Type, sub_message, field_name, TargetType)  \
  if (input.Is##Type(i, _field_info_map[#field_name])) { \
      sub_message->set_##field_name(static_cast<TargetType>(input.Get##Type(i, #field_name))); \
  } else { \
  }

// 将 Grid 数据塞进 pb Message 中, 支持强制类型转换
#define SET_PB_GRID_FIELD_STATIC_CAST(Type, field_name, TargetType)                                     \
  if (input.Is##Type(i, _field_info_map[#field_name])) {                                                \
    result.set_##field_name(static_cast<TargetType>(input.Get##Type(i, #field_name)));                  \
  } else {                                                                                              \
  }                                                                                                     \
  if (enable_diff && pb_data.field_name() != result.field_name() &&                                     \
      EmsKconfUtil::emsDiffWhilteList()->count(absl::StrCat(pb_type_str, "#", #field_name)) <= 0) {     \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "data_gate_data_diff_pb",               \
                                       absl::StrCat(pb_type_str, "#", #field_name), "diff");            \
    LOG_EVERY_N(INFO, 100) << pb_type_str << "#" << #field_name << "_id=" << td_id                      \
                           << ": local=" << pb_data.field_name() << ": remote=" << result.field_name(); \
  } else if (enable_diff) {                                                                             \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "data_gate_data_diff_pb",               \
                                       absl::StrCat(pb_type_str, "#", #field_name), "nodiff");          \
  }

// 将 Grid String 数据塞进 pb Message 中
#define SET_PB_GRID_STRING_FIELD(Type, field_name)  \
  {                                                  \
    std::string temp;                                  \
    if (input.Is##Type(i, _field_info_map[#field_name])) { \
      input.Get##Type(i, #field_name, &temp); \
      result.set_##field_name(temp);          \
    } else {                                   \
    }                                                              \
  }

// 将 Grid String 数据塞进指定 pb Message 中
#define SET_SUB_PB_GRID_STRING_FIELD(Type, sub_message, field_name)  \
  {                                                  \
    std::string temp;                                  \
    if (input.Is##Type(i, _field_info_map[#field_name])) { \
      input.Get##Type(i, #field_name, &temp); \
      sub_message->set_##field_name(temp);          \
    } else {                                   \
    }                                                              \
  }


//  将 Grid 数据塞进在外部定义的结构体
#define SET_PB_GRID_LIST_FIELD(Type, field_name)  \
  if (input.Is##Type(i, _field_info_map[#field_name])) { \
      input.Get##Type(i, #field_name, &field_name); \
  } else { \
  }

/***************************** 倒排接口 ***********************************/
// input: 输入; item: struct 输出对象; row_index: 写入第几行
// _field_info_map: 将 #field_name 映射为其在 input 中的顺序
#define GET_GRID_FIELD_BY_ROW_INDEX(Type, field_name, row_index)  \
  if (input.Is##Type(row_index, _field_info_map[#field_name])) { \
      item.field_name = input.Get##Type(row_index, #field_name); \
  } else { \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,  \
      "TransformData_error", _index_name, #field_name); \
  }

#define GET_GRID_PTR_FIELD_BY_ROW_INDEX(Type, field_name, row_index)  \
  if (input.Is##Type(row_index, _field_info_map[#field_name])) { \
      input.Get##Type(row_index, #field_name, &(result.field_name)); \
  } else { \
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,  \
      "TransformData_error", #field_name); \
  }

class BatchGridTable {
 public:
  BatchGridTable() = default;

  // 虚析构函数, 确保派生类正确析构
  virtual ~BatchGridTable() = default;

  // 纯虚函数, 各个派生子类(用来真正请求各个表)必须实现, 初始化自己的字段 _field_info_vec
  virtual void InitTableField() = 0;

  // 子类使用 InitGridQuery 进行请求的部分构造
  void InitGridQuery();

  bool GetNewTail(const int64_t key_id);

  int32_t GetStage();

  bool BatchGetTable(const std::vector<ks::grid::Querykey>& querykey_vec, std::vector<ks::grid::GridData>& resp); //NOLINT
  // 将 query 字段单拎出来
  ks::grid::GridQuery GeGridQuery() {
    return _grid_query;
  }

 protected:
  ks::grid::GridQuery _grid_query;
  std::string _table_name;
  // map 记录 field index, 方便查询
  std::unordered_map<std::string, uint32_t> _field_info_map;
  // 子类的新增字段在这里定义
  std::vector<std::string> _field_info_vec;
  // 倒排名称
  std::string _index_name;
  // 倒排结果队列限制
  int32_t _limit;
  // 单 key 倒排最大长度，超出则截断
  int32_t _posting_list_length_limit;
};

template <typename TVal1, typename TVal2>
void DiffField(const std::string& table_name, const std::string& field_name,
               const TVal1& origin_val, const TVal2& expected_val,
               const int64_t table_id,
               std::string& diff_str_accumulator,     // NOLINT
               const std::string& diff_type) {        // diff_type 分为 td 和 pb
  auto key = absl::StrCat(table_name, "#", field_name);
  if (diff_type == "td" && EmsKconfUtil::emsDiffWhilteList()->count(key)) {
    return;
  }
  if (origin_val != expected_val) {
    ks::infra::PerfUtil::CountLogStash(
        1, kCommonPerfNameSpace,
        absl::StrCat(table_name, "_", diff_type, "_diff"),
        field_name, std::to_string(table_id));

    std::string diff_str = absl::StrCat(
        " |", table_name, "-", field_name,
        ":", diff_type, "_origin_val=", origin_val,
        ",expected_val=", expected_val, "| ");

    diff_str_accumulator.append(diff_str);
  }
}

template <typename TVal1, typename TVal2>
void DiffField(const std::string& table_name, const std::string& field_name,
               const google::protobuf::RepeatedField<TVal1>& origin_val,
               const google::protobuf::RepeatedField<TVal2>& expected_val, const int64_t table_id,
               std::string& diff_str_accumulator,  // NOLINT
               const std::string& diff_type) {     // diff_type 分为 td 和 pb
  bool is_diff = (origin_val.size() != expected_val.size());
  auto origin_val_str = absl::StrJoin(origin_val, ",");
  auto expected_val_str = absl::StrJoin(expected_val, ",");
  if (origin_val_str != expected_val_str) {
    ks::infra::PerfUtil::CountLogStash(
        1, kCommonPerfNameSpace,
        absl::StrCat(table_name, "_", diff_type, "_diff"),
        field_name, std::to_string(table_id));

    std::string diff_str = absl::StrCat(
        " |", table_name, "-", field_name,
        ":", diff_type, "_origin_val=", origin_val_str,
        ",expected_val=", expected_val_str, "| ");

    diff_str_accumulator.append(diff_str);
  }
}

}  // namespace ad_ems
}  // namespace ks

#endif  // TEAMS_AD_EMS_SRC_COMMON_TOOLKIT_GRID_TABLE_H_
