#include <vector>
#include <utility>
#include <string>
#include <set>
#include "teams/ad/grid/sdk/grid_client.h"
#include "teams/ad/ems/src/common_toolkit/creative_table.h"
#include "teams/ad/ems/src/util/kconf.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"  // AdEnum
#include "teams/ad/ad_table_lite/include/ad_table_lite/index_manager.h"
#include "perfutil/perfutil.h"

namespace ks {
namespace ad_ems {

DECLARE_bool(enable_grid_sdk_cache);
namespace td = ::tables::data_gate;
using tables::data_gate::Creative;

void GetCreativeTable::PadFromStruct(const td::Creative& creative, EmsCreative& result) {
  result.id = creative.id;
  result.photo_id = creative.photo_id;
  result.unit_id = creative.unit_id;
  result.create_time = creative.create_time;
  result.account_id = creative.account_id;
  result.campaign_id = creative.campaign_id;
  result.live_stream_id = creative.live_stream_id;
  result.creative_type = creative.creative_type;
  result.live_creative_type = creative.live_creative_type;
  result.creative_material_type = creative.creative_material_type;
  result.review_status = creative.review_status;
  result.put_status = creative.put_status;
  result.auto_deliver_type = creative.auto_deliver_type;
  result.creative_photo_source = creative.creative_photo_source;
  result.community_review_status = creative.community_review_status;
  LOG(INFO) << "PadFromStruct:" << result.Desc();
}

bool GetCreativeTable::GetCreativeData(const int64_t query_id, EmsCreative& result,
  const bool is_struct, const int special_condition) {
  // 每次调用接口, 都获取当前的 Kconf 最新状态
  auto stage = GetStage();
  bool is_use_gird = GetNewTail(query_id);
  auto* index = ks::ad_table_lite::IndexManager::GetInstance();
  // 请求原始数据, 填充 origin_data
  EmsCreative origin_data;
  td::Creative ems_td;
  ems_td = index->Get<Creative>(query_id);
  ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "GetCreativeTable_is_struct",
  std::to_string(query_id));
  LOG(INFO) << "GetCreativeTable is_struct, id:" << query_id;
  PadFromStruct(ems_td, origin_data);
  // Grid 数据填充
  EmsCreative grid_data;
  // 判断是否请求 Grid
  if ((stage == 1 && is_use_gird == true) ||  // 部分尾号 Diff 阶段
      (stage == 2 && is_use_gird == true) ||  // 部分尾号切 Grid 阶段
      stage == 3) {                           // 完全切 Grid 阶段
    Querykey query_key;
    query_key.i64 = query_id;
    // 请求 Grid 数据
    GetCreativeData(query_key, grid_data);
    LOG(INFO) << "GetCreativeData_grid: " << grid_data.Desc();
  }

  // 判断使用新数据还是老数据
  if ((stage == 2 && is_use_gird) || stage == 3) {  // 使用新数据
    result = std::move(grid_data);
  } else {
    result = std::move(origin_data);
  }
  LOG(INFO) << "GetCreativeData_final: " << result.Desc();
  return true;
}

void GetCreativeTable::GetCreativeData(const Querykey& query_key, EmsCreative& result) {
  std::vector<Querykey> querykey_vec{query_key};
  std::vector<GridData> resp;

  // 调用父类的接口, 进行正排数据获取
  int ret = BatchGetTable(querykey_vec, resp);

  if (!ret) {
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "GetCreativeTable_error",
      std::to_string(ret), std::to_string(query_key.i64));
    return;
  }

  if (resp.size() != 1) {
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "GetCreativeTable_error_size",
      std::to_string(resp.size()), std::to_string(query_key.i64));
    return;
  }

  ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "GetCreativeTable_success",
    std::to_string(query_key.i64));

  auto& single_table = resp[0];
  // 对一张表进行解析
  TransformData(query_key.i64, single_table, result);
}

void GetCreativeTable::TransformData(int64_t key, const GridData& input, EmsCreative& result) {
  if (!input.IsValid()) return;
  if (input.RowSize() != 1) return;
  if (!input.IsValid(0))  return;  // 第 i 行是否存在
  LOG(INFO) << "begin to EmsCreative TransformData, key_id:" << key;
  ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
    "GetCreativeTable_TransformData", std::to_string(key));

  // 解析字段
  GET_GRID_FIELD(Int64, id);
  GET_GRID_FIELD(Int64, photo_id);
  GET_GRID_FIELD(Int64, unit_id);
  GET_GRID_FIELD(Int64, create_time);
  GET_GRID_FIELD(Int64, account_id);
  GET_GRID_FIELD(Int64, campaign_id);
  // 针对 live_stream_id 做特殊处理, 请求使用 parent_live_stream_id, 存储使用 live_stream_id
  if (input.IsInt64(ROW_INDEX, _field_info_map["parent_live_stream_id"])) {
    result.live_stream_id = input.GetInt64(ROW_INDEX, "parent_live_stream_id");
  } else {
    LOG(INFO) << "table " << _table_name << " key_id " << key << " has_no join_agent_id";
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
      "TransformData_error", _table_name, "parent_live_stream_id");
  }
  GET_GRID_FIELD(Int32, creative_type);
  GET_GRID_FIELD(Int32, live_creative_type);
  GET_GRID_FIELD(Int32, creative_material_type);
  GET_GRID_FIELD(Int32, review_status);
  GET_GRID_FIELD(Int32, put_status);
  GET_GRID_FIELD(Int32, auto_deliver_type);
  GET_GRID_FIELD(Int32, creative_photo_source);
  GET_GRID_FIELD(Int32, community_review_status);
}
void GetCreativeTable::TransformData(const std::vector<Querykey>& req_batch, const GridData& input,
    std::unordered_map<int64_t, std::optional<td::Creative>>& data_map,    //NOLINT
    std::unordered_map<int64_t, std::optional<kuaishou::ad::tables::Creative>>& pb_data_map) {}
void GetCreativeTable::Transform2Td(const std::vector<Querykey>& req_batch, const GridData& input,
    std::unordered_map<int64_t, std::optional<td::Creative>>& data_map) {}
void GetCreativeTable::Transform2Pb(const std::vector<Querykey>& req_batch, const GridData& input,
    std::unordered_map<int64_t, std::optional<kuaishou::ad::tables::Creative>>& data_map) {}

#define DIFF_CREATIVE_TD_FIELD(field) \
  DiffField("Creative", #field, td_struct.field, td_data.field, td_struct.id, td_diff_str, "td");

#define DIFF_CREATIVE_PB_FIELD(field) \
  DiffField("Creative", #field, pb_table.field(), pb_data.field(), pb_data.id(), pb_diff_str, "pb");
void GetCreativeTable::DiffCreativeTd(const td::Creative& td_struct) {
  int64_t id = td_struct.id;
  // 1. get origin index
  auto* index = ks::ad_table_lite::IndexManager::GetInstance();
  if (index == nullptr || !index->WaitForReady()) {
    LOG(WARNING) << "DiffCreative index is not Ready.";
    return;
  }
  td::Creative td_data = index->Get<td::Creative>(id);
  // 2. diff td
  std::string td_diff_str;
  DIFF_CREATIVE_TD_FIELD(id);
  DIFF_CREATIVE_TD_FIELD(photo_id);
  DIFF_CREATIVE_TD_FIELD(unit_id);
  DIFF_CREATIVE_TD_FIELD(create_time);
  DIFF_CREATIVE_TD_FIELD(account_id);
  DIFF_CREATIVE_TD_FIELD(campaign_id);
  DIFF_CREATIVE_TD_FIELD(live_stream_id);
  DIFF_CREATIVE_TD_FIELD(creative_type);
  DIFF_CREATIVE_TD_FIELD(live_creative_type);
  DIFF_CREATIVE_TD_FIELD(creative_material_type);
  DIFF_CREATIVE_TD_FIELD(review_status);
  DIFF_CREATIVE_TD_FIELD(put_status);
  DIFF_CREATIVE_TD_FIELD(auto_deliver_type);
  DIFF_CREATIVE_TD_FIELD(creative_photo_source);
  DIFF_CREATIVE_TD_FIELD(community_review_status);

  // 3. print diff and dot
  if (!td_diff_str.empty()) {
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "Creative_td_diff", "total_diff",
                                       std::to_string(td_struct.id));
    LOG(WARNING) << "Creative_td_has_diff:"
                 << ", id:" << td_struct.id << ", diff_str:" << td_diff_str;
  }
  ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "Creative_td_diff", "diff_ratio",
                                     td_diff_str.empty() ? "no_diff" : "has_diff");
}
}  // namespace ad_ems
}  // namespace ks
