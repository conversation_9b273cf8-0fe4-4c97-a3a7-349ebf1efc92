#pragma once

#include <string>
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/bid_server/base/kconf/kconf_data.pb.h"
#include "teams/ad/bid_server/base/kconf/kconf_data.h"

namespace ks {
namespace bid_server {

using namespace ks::ad_base::kconf;  // NOLINT
using namespace ks::bid_server::kconf;  // NOLINT

// 配置链接
// https://kconf.corp.kuaishou.com/#/ad/bidServer/migrationMode

class AdBaseKconfUtil {
 public:
  // 拟合配置
  DEFINE_PROTOBUF_NODE_KCONF(ModelFixConfPB, ad.bidServer, modelFixConf);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, logSampleFrequency, 100);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, logSampleFrequencyHigh, 10);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, logSampleFrequencySuprme, 1);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantEffectivePlayTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, orderPaiedNonSingleCommodityTail);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, NonSingleCommodityGroupTag, "");
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, universeInnerClickActionTyeFixTail);
  // costcap 迁移相关
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAllOcpmCostCap, false)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmCostCapUnitIdSet)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableSpecialtyOcpmCostCap, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFixEndTime, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCboNobid, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableSkipOcpmCostCap, false)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, storewideAddT7Tail)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, cboNobidCampaignWhiteList)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCboCostCap, true)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, cboStoreWideCampaignWhiteList)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, espLiveOcpmBudgetInvalidUserList)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, ocpmLiveRoas2CostCapUnitTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, ocpmPhotoRoas2CostCapUnitTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, liveHosting2UnitAutoBidTail, "")
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCboStoreWide, false)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, fanstopMerchantFollowTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, mobileHostingCboTail, "");

  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer, storeWideInitBidWhiteList);

  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableHaitouTraceLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAllPvTraceLog, false)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMapNew, ad.bidServer, ocpmCostCapOcpxAccountIdMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMapNew, ad.bidServer, ocpmCostCapOcpxAccountTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMapNew, ad.bidServer, ocpmCostCapOcpxUnitTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(NativeNobidExpConfigNew, ad.bidServer, nativeNobidExpConfig);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFixedPeriodBeginTime, true)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativePvMinuteCalcProportion)  // 流量分布比例
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeLivePvMinuteCalcProportion)  // 专推直播流量分布比例
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeFlashPvMinuteCalcProportion)  // 速推直播流量分布比例
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativePvROICalcMixProportion)  // 短视频 ROI PV 混合分布比例
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, accExploreShadowBudgetRatio)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, accExploreExpTail, "");
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, NativePvROIDynamicCostWeight, 1.0)  // 短视频 ROI PV 权重
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, nativeProportionNewWhiteList)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, accNobidMaxBudget, 20000000)  // 加速探索最大预算
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmCostCapLogUnitIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, nativeNobidLogUnitIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, nativeNobidLogUnitTailSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, nobidPvPropTransUnitTailSet)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFakeBudget, false);
  DEFINE_SET_NODE_KCONF(int32, ad.bidServer, nobid_invalid_author_industry)

  // campaign 最低预算 元
  DEFINE_INT32_KCONF_NODE(ad.bidServer, mcbCampaignLowestBudget, 500)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, recordMsgFoot, false);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, slowMsgThresholdMs, 3000);

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableAdLiveAudienceTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableAdButtonClickConsultTail);

  // 内循环 116 大促工具白名单
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, innerLive116ToolWhiteAuthor);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, innerPhoto116ToolWhiteAuthor);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, innerBoostBidToolWhite);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, innerBoostBidToolWhiteCostcap);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableInnerBoostBidKconf, true);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableInnerBoostBidPlatform, false);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, innerLive116ToolWhiteSellerRange);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, innerPhoto116ToolWhiteSellerRange);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, innerBoostBidToolBound);
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMapNew, ad.bidServer, ocpxCostCapOcpmUnitTailMap);
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMapNew, ad.bidServer, ocpxCostCapOcpmCampaignTailMap);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableFixStorewideRoasAccTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableFixStorewideRoasAccV3Tail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, budgetChangeOrderAccountTail, "");

  DEFINE_PROTOBUF_NODE_KCONF(BidServerOuterHotMsgDropConf, ad.bidServer, bidServerOuterHotMsgDropConf)

  // 最大转化流量分配曲线最长使用过去的天数
  DEFINE_INT32_KCONF_NODE(ad.bidServer, noBidCostRatioOptBeforeDays, 1);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, pastWindowInfoMinutes, 5);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, noBidCostRatioOptGmvTypeAll, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, noBidCostRatioOptBudgetGrpAll, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enablePayRoi24hSwitch, true);

  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableLSPItemClickConvSwitch, false);

  // 直播预约数统计口径切换开关
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableReservationCountSwitch, false);
  // 外循环直播点击数口径切换开关
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableLiveClickConversionSwitch, false);
  // nobid 预算分配曲线修复开关
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enablePvRatioAscendingCheck, false);
  // 短视频全站迁移实验
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, PhotoStoreWideTransTail, "");
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMapNew, ad.bidServer, nobidMobileInitOcpxCampaignTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMapNew, ad.bidServer, nobidPcInitOcpxUnitTailMap)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, disableShard, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableCheckTTL, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableTTL, false);
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, cacheTTL, 60);
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, maxTTL, 360);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableRemoveExpire, false);

  // nobid
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer2, pastWindowNobidOcpxSet);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableFixOrderEndTime, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableLockBudgetDeliveryEndTime, false)
  // acc increment
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, PhotoAccIncrementTransTail, "");
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, storeWideNobidCboSceneSet);
  // 稳投
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, fixWentouReset, false);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, GetIndustryPvRatioRetryCountTail, "");
};

}  // namespace bid_server
}  // namespace ks
