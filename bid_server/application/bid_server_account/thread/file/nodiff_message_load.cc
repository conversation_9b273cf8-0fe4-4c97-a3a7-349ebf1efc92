#include "teams/ad/bid_server/application/bid_server_account/thread/file/nodiff_message_load.h"

#include <algorithm>
#include <string>
#include <utility>
#include <memory>

#include "absl/strings/str_cat.h"
#include "google/protobuf/text_format.h"
#include "teams/ad/bid_server/application/bid_server_account/kconf/kconf.h"
#include "teams/ad/bid_server/framework/utils/perf.h"
#include "teams/ad/bid_server/application/bid_server_account/p2p/bid_server_nodiff_data.h"

DECLARE_bool(enable_bid_no_diff_switch);

namespace ks {
namespace bid_server {

void NodiffMessageLoad::Start() {
  if (started_.load()) {
    LOG(WARNING) << "NodiffMessageLoad has been already started";
    return;
  }

  LOG(INFO) << "NodiffMessageLoad start";
  started_.store(true);
  work_thread_ = new std::thread{[this]() {
    while (!stopped_.load()) {
      if (is_finish_) {
        LOG_EVERY_N(INFO, 100) << "Fetch Msg From BidServerLogDataP2P, Failed";
        base::SleepForMilliseconds(1000);
        continue;
      }
      TRIGGER_SKIP_CONTINUE()
      LogData log_data;
      auto op_action_type = BidServerLogDataP2P::GetInstance()->GetLogData(log_index_, &log_data);
      if (op_action_type == SUCCESS) {
        LOG_EVERY_N(INFO, 1) << "Fetch Msg From BidServerLogDataP2P, index:" << log_index_;
        ++log_index_;
        int64_t msg_count = TimeUtil::RecordMsgCount();
        if (log_data.log_type == AD_LOG_OCPM) {
          std::shared_ptr<AdLogForAlgo> ad_log_pb_msg(new AdLogForAlgo());
          if (ad_log_pb_msg->ParseFromString(log_data.log_data)) {
            if (ks::bid_server::AdKconfUtil::enableAdlogFieldControl()) {
              auto config = AdKconfUtil::bidServerAdLogWhiteList()->data;
              ks::ad_base::Proto::CleanFiledByName(ad_log_pb_msg.get(), config.get(), true);
            }
            ProcessMsg(*ad_log_pb_msg);
            LOG_EVERY_N(INFO, 1) << "ParseBase64PB Sucess, msg:" << ad_log_pb_msg->ShortDebugString()
              << ", log_type:" << log_data.log_type
              << ", msg_seq: " << msg_count
              << ", msg_type:0";
          }
        } else {
          LOG_EVERY_N(INFO, 1) << " log_type no find. ";
        }
      } else if (op_action_type == FINISH) {
        is_finish_ = true;
      }
    }
  }};
}

void NodiffMessageLoad::Stop() {
  if (stopped_.load()) {
    LOG(WARNING) << "NodiffMessageLoad has been already stopped";
    return;
  }

  stopped_.store(true);

  if (work_thread_) {
    work_thread_->join();
    delete work_thread_;
  }

  LOG(INFO) << "NodiffMessageLoad stop";
}

REGISTER_CLASS(ThreadBase, NodiffMessageLoad);

}  // namespace bid_server
}  // namespace ks
