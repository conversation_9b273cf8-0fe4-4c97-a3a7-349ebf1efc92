#pragma once

#include <atomic>

#include "teams/ad/ad_proto/kuaishou/ad/budget_status.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/bid_server/bid_server_backflow_predict.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/dw/ad_log_for_algo.pb.h"
#include "teams/ad/bid_server/application/bid_server_account/util/msg_process.h"
#include "teams/ad/bid_server/framework/api/thread_base.h"
#include "teams/ad/bid_server/framework/api/trigger_base.h"

namespace ks {
namespace bid_server {

using kuaishou::ad::dw::AdLogForAlgo;

class NodiffMessageLoad: public ThreadBase, public MsgProcess, public TriggerController {
 public:
  NodiffMessageLoad() = default;
  ~NodiffMessageLoad() = default;

  void WarmUp() {}
  void Start() final;
  void Stop() final;
  DEFINE_CLASS_NAME(NodiffMessageLoad)

 private:
  std::atomic<bool> started_{false};
  std::atomic<bool> stopped_{false};
  std::thread* work_thread_{nullptr};
  bool is_finish_{false};
  int64_t log_index_{0};
};
}  // namespace bid_server
}  // namespace ks
