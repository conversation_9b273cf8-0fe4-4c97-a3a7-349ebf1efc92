#pragma once

#include <memory>

#include "teams/ad/ad_proto/kuaishou/ad/budget_status.pb.h"
#include "teams/ad/bid_server/framework/api/partition_api.h"
#include "teams/ad/ad_proto/kuaishou/ad/bid_server/bid_server_backflow_predict.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/dw/ad_log_for_algo.pb.h"
#include "teams/ad/bid_server/application/bid_server_account/trigger/log_inner_data.h"

namespace ks {
namespace bid_server {

using kuaishou::ad::BackflowUnitResultMap;
using kuaishou::ad::dw::AdLogForAlgo;

class MsgProcess : public PartitionApi{
 public:
  MsgProcess() = default;
  ~MsgProcess() = default;

  // ad_log 消息处理
  void ProcessMsg(const AdLogForAlgo& adlog);

  void PerfRawMsg(RawMsgType type, const std::shared_ptr<rttr::variant>& value) override;

 private:
  // ad_log 消息处理方法
  bool ShouldFilter(const LogInnerData &inner_data,
                    const AdLogForAlgo &adlog);
  void MonitorAdLog(const LogInnerData &inner_data);
  void MonitorAggrAdLog(const LogInnerData &inner_data);
  bool DispatchMsg(std::shared_ptr<LogInnerData> inner_data);
};

}  // namespace bid_server
}  // namespace ks
