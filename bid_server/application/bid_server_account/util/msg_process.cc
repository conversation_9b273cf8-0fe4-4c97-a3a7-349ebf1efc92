#include "teams/ad/bid_server/application/bid_server_account/util/msg_process.h"

#include <memory>
#include <string>
#include <utility>
#include <algorithm>
#include "base/common/sleep.h"
#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/engine_base/kconf/account_ocpm_bidding_config.h"
#include "teams/ad/engine_base/kconf/soft_hard_isolate.h"
#include "teams/ad/ad_base/src/common/latency_record.h"
#include "teams/ad/bid_server/framework/manager/unit_shard_mgr.h"
#include "teams/ad/bid_server/framework/utils/perf.h"
#include "teams/ad/bid_server/base/kafka_helper.h"
#include "teams/ad/bid_server/application/bid_server_account/kconf/kconf.h"
#include "teams/ad/bid_server/application/bid_server_account/data/msg.h"
#include "teams/ad/bid_server/base/util/index_util.h"
#include "teams/ad/bid_server/framework/utils/kafka_util.h"
#include "teams/ad/engine_base/bid_common/utils.h"
#include "teams/ad/bid_server/framework/input/adlogforalgo_consumer.h"
#include "teams/ad/bid_server/base/common.h"
#include "teams/ad/bid_server/application/bid_server_account/util/tool.h"
#include "teams/ad/bid_server/application/bid_server_account/util/spdm_switches.h"
#include "teams/ad/bid_server/framework/index/default/campaign_manager.h"
#include "teams/ad/bid_server/framework/index/default/account_manager.h"

DECLARE_bool(enable_single_mode);
DECLARE_bool(enable_bid_no_diff_switch);
DECLARE_bool(is_test_env);

namespace ks {
namespace bid_server {

void MsgProcess::ProcessMsg(const AdLogForAlgo& adlog) {
  std::shared_ptr<LogInnerData> inner_data = std::make_shared<LogInnerData>();
  inner_data->ParseMsg(adlog);
  if (ShouldFilter(*inner_data, adlog)) {
    return;
  }
  if (SPDM_enableAggrOuterOcpmMsg()) {    // 打开, 使用新消息
    if (!adlog.aggr_outer_ocpm_msg().is_aggr_msg()) {    // 过滤旧消息
      perf::Inc("aggr_outer_ocpm_msg.filter_old_msg");
      return;
    }
    perf::Inc("aggr_outer_ocpm_msg.enable_new_msg");
  } else {    // 关闭, 使用旧消息
    if (adlog.aggr_outer_ocpm_msg().is_aggr_msg()) {     // 过滤新消息
      perf::Inc("aggr_outer_ocpm_msg.filter_new_msg");
      return;
    }
  }
  if (FLAGS_enable_bid_no_diff_switch) {
    inner_data->msg_seq = TimeUtil::GetMsgCount();
  }
  perf::Inc("adlogforalgo.dispatch.cnt");
  // 日志量打点
  if (adlog.action_type() == "AD_DELIVERY" &&
      adlog.ad_log_aggr_message_value().is_aggr_msg() &&
      adlog.is_from_flink()) {
    perf::Count(adlog.ad_log_aggr_message_value().aggr_msg_cnt(),
                "bid_server_account_adlog_cnt",
                "AD_DELIVERY",
                absl::StrCat(adlog.medium_attribute()),
                adlog.ocpc_action_type());
  } else {
    perf::Count(1,
                "bid_server_account_adlog_cnt",
                adlog.action_type(),
                absl::StrCat(adlog.medium_attribute()),
                adlog.ocpc_action_type());
  }
  bool enable_monitor = DispatchMsg(inner_data);
  if (!enable_monitor) {
    return;
  }
  MonitorAdLog(*inner_data);
  if (SPDM_enablePerfOuterOcpmAggrMsgFileds()) {
    MonitorAggrAdLog(*inner_data);
  }
}


bool MsgProcess::ShouldFilter(const LogInnerData& inner_data,
                                       const AdLogForAlgo& adlog) {
  // 人工停止调价, true 跳过
  if (AccountKconfUtil::accountSkipConsumeLog()) {
    perf::Inc("adlogforalgo.skip_consume_log", absl::StrCat(inner_data.is_native_nature));
    return true;
  }
  auto& degrade_conf = AccountKconfUtil::DegradeConfManage()->data();
  bool enable_degrade = degrade_conf.pb().enable_degrade();
  // 人工停止调价, true 跳过
  if (enable_degrade && degrade_conf.InDegrade("outer_ocpm_bid_ad_log",
                                                adlog.account_id(),
                                                adlog.campaign_id(),
                                                adlog.ocpc_action_type(),
                                                adlog.campaign_type())) {
    perf::Inc("adlogforalgo.skip_consume_log", absl::StrCat(inner_data.is_native_nature));
    perf::Count(1, "outer_ocpm_bid_ad_log", "degrade_msg_tail",
              absl::StrCat(adlog.campaign_id() % 100), absl::StrCat(adlog.account_id() % 100),
              adlog.ocpc_action_type(), adlog.campaign_type());
    return true;
  }
  if (!FLAGS_is_test_env || !AdKconfUtil::enableTestEnvOnlyShard0UpdateRedis()) {
    if ((inner_data.source_type == kuaishou::ad::AdSourceType::BRAND || adlog.source_type() == "BRAND")
        && AccountKconfUtil::enableBrandOneShard()) {
      LOG_EVERY_N(INFO, 1000) << "source_type:" << inner_data.source_type
        << ", source_type_str:" << adlog.source_type()
        << ", brandShardName:" << *(AccountKconfUtil::brandShardName())
        << ", shard_name:" << UnitShardMgr::Instance().get_shard_name();
      if (*(AccountKconfUtil::brandShardName()) != UnitShardMgr::Instance().get_shard_name()) {
        return true;
      }
    } else {
      // cancidate 环境不过滤非本 shard 的消息
      // 1. 过滤非本 shard 日志
      if (!UnitShardMgr::Instance().is_valid_shard_unit(inner_data.account_id)) {
        perf::Inc("adlogforalgo.shard_filter", absl::StrCat(inner_data.is_native_nature));
        return true;
      }
    }
  }

  // 3. base check
  if (!MsgBaseChecker::IsValidAccountMsg(adlog)) {
    perf::Inc("adlogforalgo.valid_filter", absl::StrCat(inner_data.is_native_nature));
    return true;
  }

  // 4. 检查 account 和 campaign 粒度是否降级
  auto rollback_conf = AccountKconfUtil::accountRollbackManagerConf();
  const AccountRollbackManagerConf& conf = rollback_conf->data();
  if (conf.CampaignInRollbackScope(inner_data.campaign_id) ||
      conf.AccountInRollbackScope(inner_data.account_id)) {
    perf::Inc("adlogforalgo.rollback_filter", absl::StrCat(inner_data.is_native_nature));
    LOG_EVERY_N(INFO, 100) << "degradation, inner_data:" << inner_data.ToString();
    return true;
  }

  // 联盟白名单账户下非账户调价单元不进账户调价
  auto universe_account_bidding_set = engine_base::AdKconfUtil::universeAccountBiddingAccountSet();
  auto universe_exp_unit_tail = engine_base::AdKconfUtil::universeAccountBiddingExpUnitTail();
  if (universe_account_bidding_set->count(inner_data.account_id) > 0 && IsUniverse(inner_data.tag)) {
    if (!(universe_exp_unit_tail->count(inner_data.unit_id % 100) > 0)) {
      LOG_EVERY_N(INFO, 100) << "universe_account_bidding"
                             << " account_id=" << inner_data.account_id
                             << " unit_id=" << inner_data.unit_id;
      return true;
    }
  }

  // 联盟跑量工具过滤：联盟 & 账户白名单 & 生效时间范围内 & 单元尾号命中
  if (inner_data.is_universe_opt && AccountKconfUtil::shadowOcpxToolWhiteListConf()->data().InWhiteList(
        inner_data.account_id, inner_data.ocpc_action_type,
        inner_data.delivery_timestamp / 1000, inner_data.unit_id % 100)) {
    LOG_EVERY_N(INFO, 10000) << "bid_server_account_shadow_ocpx_tool_ratio"
                          << " unit_id=" << adlog.unit_id()
                          << " account_id=" << adlog.account_id()
                          << " product_name=" << adlog.product_name()
                          << " ocpx_action_type=" << adlog.ocpc_action_type()
                          << " delivery_timestamp=" << adlog.delivery_timestamp();
    return true;
  }

  // 全域推广异常 plc 过滤
  auto plc_biz_type_white_list = AccountKconfUtil::natureNativePlcBizTypeWhiteList();
  if (AccountKconfUtil::enableNatureNativePlcFilter()) {
    if (inner_data.is_native_nature && plc_biz_type_white_list->count(inner_data.plc_biz_type) == 0) {
      perf::Inc("adlogforalgo.nature_ad_plc_filter");
      return true;
    }
    perf::Inc("adlogforalgo.nature_ad_enter_plc_filter");
  }

  return false;
}

void MsgProcess::MonitorAdLog(const LogInnerData &inner_data) {
  // TODO(策略同学) 填充监控逻辑
  perf::Interval(inner_data.lag_sec, "ad_log_with_price_lag_sec",
                  kuaishou::ad::AdActionType_Name(inner_data.log_action_type));
  uint64_t price = inner_data.price_sum;
  price = inner_data.price_sum + inner_data.increment_explore_price_sum;
  uint64_t target_cost = inner_data.target_cost;
  target_cost = inner_data.target_cost + inner_data.increment_explore_target_cost;
  int64_t gimbal_cost = inner_data.gimbal_cost_sum;
  int64_t receivable_price = inner_data.receivable_price_sum;
  int64_t customer_hc_cost = inner_data.customer_hc_cost_sum;
  uint64_t price_before_billing_separate = inner_data.price_before_billing_separate_sum;
  uint64_t price_after_billing_separate = inner_data.price_after_billing_separate_sum;
  uint64_t gimbal_price_before = inner_data.gimbal_price_before_sum;
  std::string charge_tag = absl::StrCat(inner_data.charge_tag);
  if (price > 0) {
    if (inner_data.bid_type == kuaishou::ad::AdEnum::MCB) {
      std::string adv_type = "nobid";
      if (static_cast<int>(inner_data.constraint_roi) == 6) {
        adv_type = "cost_cap";
      } else if (static_cast<int>(inner_data.constraint_roi) == 5) {
        adv_type = "abo_cap";
      } else if (::ks::engine_base::IsAccountStrategy(inner_data.account_id)) {
        adv_type = "abo_noncap";
      }
      perf::Count(price,
                  "mcb_campaign_cost",
                  inner_data.campaign_type,
                  kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                  absl::StrCat(inner_data.campaign_id % 100),
                  absl::StrCat(inner_data.account_id % 100),
                  charge_tag,
                  adv_type);
    }
    if (inner_data.auto_adjust == "AUTO_ADJUST_OPEN") {
      if (!AccountKconfUtil::account_enablePerfOpt()) {  //下线维度爆炸的 perf 但不破坏原有监控
        perf::Count(price,
                          "auto_adjust_price",
                          inner_data.product_name,
                          kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                          "",
                          kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                          inner_data.tag,
                          inner_data.campaign_type);
      }
    }
    if (IsDuanju(inner_data.campaign_type_enum, inner_data.second_industry_name, inner_data.account_id,
        inner_data.deep_conversion_type)) {
      perf::Count(price,
                    "duanju_price",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    inner_data.is_increment_explore ? "increment" : "not_increment",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    "");
      perf::Gauge(gimbal_cost,
                    "duanju_gimbal_cost",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    "",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    "");
    }
    if (IsDuanju(inner_data.campaign_type_enum, inner_data.second_industry_name, inner_data.account_id,
        inner_data.deep_conversion_type)) {
      perf::Count(gimbal_cost,
                    "duanju_gimbal_cost_v2",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    inner_data.is_increment_explore ? "increment" : "not_increment",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    "");
    }
    if (IsFiction(inner_data.campaign_type_enum, inner_data.account_id)) {
      perf::Count(price,
                    "fiction_price",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    inner_data.is_increment_explore ? "increment" : "not_increment",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    "");
      perf::Gauge(gimbal_cost,
                    "fiction_gimbal_cost",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    "",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    "");
    }
    if (inner_data.is_account_bidding) {
      if (!AccountKconfUtil::account_enablePerfOpt()) {  //下线维度爆炸的 perf 但不破坏原有监控
        perf::Count(price,
                    "ocpm_account_price",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    "",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    inner_data.campaign_type);
        perf::Count(price_before_billing_separate,
                    "ocpm_account_origin_price",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    "",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    inner_data.campaign_type);
        perf::Count(price_after_billing_separate,
                    "ocpm_account_after_price",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    "",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    inner_data.campaign_type);
        perf::Count(customer_hc_cost,
                    "ocpm_account_customer_hc_cost",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    "",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    inner_data.campaign_type);
        perf::Count(receivable_price,
                    "ocpm_account_receivable_price",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    "",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    inner_data.campaign_type);
        perf::Count(gimbal_price_before,
                    "ocpm_account_gimbal_price_before",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    "",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    inner_data.campaign_type);
      }
    }
    }
    if (target_cost > 0 && IsDuanju(inner_data.campaign_type_enum, inner_data.second_industry_name,
      inner_data.account_id, inner_data.deep_conversion_type)) {
    perf::Count(target_cost,
                    "duanju_target_cost",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    inner_data.is_increment_explore ? "increment" : "not_increment",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    "");
    perf::Count(inner_data.bonus_amount,
                    "duanju_bonus_amount",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    inner_data.is_increment_explore ? "increment" : "not_increment",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    "");
    perf::Count(inner_data.bonus_amount_target_cost,
                    "duanju_bonus_amount_target_cost",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    inner_data.is_increment_explore ? "increment" : "not_increment",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    "");
  }
  if (target_cost > 0 &&
    IsFiction(inner_data.campaign_type_enum, inner_data.account_id)) {
    perf::Count(target_cost,
                    "fiction_target_cost",
                    inner_data.product_name,
                    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                    inner_data.is_increment_explore ? "increment" : "not_increment",
                    kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                    inner_data.tag,
                    "");
  }
  perf::Count(1,
        "normal_msg_data",
        absl::StrCat(inner_data.account_id % 100),
        absl::StrCat(inner_data.is_from_flink),
        inner_data.action_type,
        absl::StrCat(inner_data.source_type));
  if (target_cost > 0 && inner_data.is_account_bidding) {
    perf::Count(target_cost,
                  "ocpm_account_target_cost",
                  inner_data.product_name,
                  kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                  "",
                  kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                  inner_data.tag,
                  inner_data.campaign_type);
    if (!SPDM_enableFixDeepTargetCostPerf()) {
      if (inner_data.deep_conversion_type > 0) {
        perf::Count(inner_data.deep_target_cost,
          "ocpm_account_deep_target_cost",
          inner_data.product_name,
          kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
          kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
          inner_data.tag,
          inner_data.campaign_type);
      }
    }
  }
  if (SPDM_enableFixDeepTargetCostPerf()) {
    if (inner_data.deep_target_cost > 0 && inner_data.is_account_bidding &&
      inner_data.deep_conversion_type > 0) {
      perf::Count(inner_data.deep_target_cost,
        "ocpm_account_deep_target_cost",
        inner_data.product_name,
        kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
        kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
        inner_data.tag,
        inner_data.campaign_type);
    }
  }

  if (inner_data.is_native_nature) {
    perf::Count(1,
                "native_nature_cnt",
                inner_data.product_name,
                kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                absl::StrCat(inner_data.account_id),
                kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                inner_data.tag,
                inner_data.action_type);
    perf::Count(inner_data.target_cost,
                "native_nature_target_cost",
                inner_data.product_name,
                kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                absl::StrCat(inner_data.account_id),
                kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                inner_data.tag,
                inner_data.action_type);
  }

  if (inner_data.source_type == kuaishou::ad::AdSourceType::BRAND) {
    // 监控品牌曝光数据
    auto action_type = inner_data.log_action_type;
    if (action_type == kuaishou::ad::AdActionType::AD_SPLASH_IMPRESSION
        || action_type == kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION) {
      perf::Count(1,
                  "brand_msg_impres",
                  inner_data.tag,
                  absl::StrCat(inner_data.pos_id),
                  inner_data.action_type);
    }
  }
}

#define PERF_INNER_DATA_MSG(field)                                                    \
  perf::Interval(inner_data.field,                                                    \
    "inner_data_msg_diff",                                                            \
    #field,                                                                           \
    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),                     \
    kuaishou::ad::AdActionType_Name(inner_data.log_action_type),                      \
    kuaishou::ad::AdSourceType_Name(inner_data.source_type),                          \
    inner_data.campaign_type,                                                         \
    kuaishou::ad::AdEnum::BidType_Name(inner_data.bid_type));


#define PERF_INNER_DATA_MSG_ZOOM(field, num)                                          \
  perf::Interval(inner_data.field * num,                                              \
    "inner_data_msg_diff",                                                            \
    #field,                                                                           \
    kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),                     \
    kuaishou::ad::AdActionType_Name(inner_data.log_action_type),                      \
    kuaishou::ad::AdSourceType_Name(inner_data.source_type),                          \
    inner_data.campaign_type,                                                         \
    kuaishou::ad::AdEnum::BidType_Name(inner_data.bid_type));

void MsgProcess::MonitorAggrAdLog(const LogInnerData &inner_data) {
  // 转化数
  PERF_INNER_DATA_MSG(action_count)
  PERF_INNER_DATA_MSG(constraint_action_count)
  PERF_INNER_DATA_MSG(today_deli_action_count)
  PERF_INNER_DATA_MSG(deep_action_count)
  // 消耗相关
  PERF_INNER_DATA_MSG(inner_expect_cv_sum)
  PERF_INNER_DATA_MSG(inner_today_deli_expect_cv_sum)
  PERF_INNER_DATA_MSG(inner_deep_expect_cv_sum)
  PERF_INNER_DATA_MSG(expect_target_cost_sum)
  PERF_INNER_DATA_MSG(price_sum)
  PERF_INNER_DATA_MSG(cpa_bid_sum)
  PERF_INNER_DATA_MSG(roi_ratio_sum)
  PERF_INNER_DATA_MSG(auto_roas_cost_sum)
  PERF_INNER_DATA_MSG(constraint_r_sum)
  PERF_INNER_DATA_MSG(price_before_billing_separate_sum)
  PERF_INNER_DATA_MSG(price_after_billing_separate_sum)
  PERF_INNER_DATA_MSG(receivable_price_sum)
  PERF_INNER_DATA_MSG(customer_hc_cost_sum)
  PERF_INNER_DATA_MSG(gimbal_price_before_sum)
  PERF_INNER_DATA_MSG(gimbal_cost_sum)
  PERF_INNER_DATA_MSG(target_cpa_sum)
  PERF_INNER_DATA_MSG(cpa_coef_cost_sum)
  PERF_INNER_DATA_MSG(auto_cpa_bid_cost_sum)
  PERF_INNER_DATA_MSG(cost_total_sum)
  PERF_INNER_DATA_MSG(coupon_cost_sum)
  PERF_INNER_DATA_MSG(aggr_msg_cnt)
  PERF_INNER_DATA_MSG(increment_explore_price_sum)
  PERF_INNER_DATA_MSG(increment_gimbal_before_sum)
  PERF_INNER_DATA_MSG(wentou_split_price_sum)
  PERF_INNER_DATA_MSG(wentou_gimbal_price_before_sum)
  // 预期消耗相关
  PERF_INNER_DATA_MSG(target_cost)
  PERF_INNER_DATA_MSG(auto_tcpa_target_cost)
  PERF_INNER_DATA_MSG(increment_explore_target_cost)
  PERF_INNER_DATA_MSG(wentou_split_target_cost)
  PERF_INNER_DATA_MSG(target_cost_attr)
  PERF_INNER_DATA_MSG(constraint_target_cost)
  PERF_INNER_DATA_MSG(deep_target_cost)
  PERF_INNER_DATA_MSG(roas_24h_all_target_cost)
  PERF_INNER_DATA_MSG(increment_explore_roas_24h_all_target_cost)
  PERF_INNER_DATA_MSG(roas_24h_all_target_cost_attr)
  PERF_INNER_DATA_MSG(predict_target_cost)
  PERF_INNER_DATA_MSG(raw_payment)
  PERF_INNER_DATA_MSG(purchase_amount_first_day_sum)
  PERF_INNER_DATA_MSG(bonus_amount)
  PERF_INNER_DATA_MSG(bonus_amount_target_cost)
  PERF_INNER_DATA_MSG(real_pay_target_cost)
  PERF_INNER_DATA_MSG_ZOOM(sub_bid_coef, 1000)
  PERF_INNER_DATA_MSG_ZOOM(aigc_bid_coef, 1000)
  PERF_INNER_DATA_MSG_ZOOM(wentou_campaign_calibration, 1000)
  PERF_INNER_DATA_MSG_ZOOM(subsidy_amount_yuan, 1000)
}

bool MsgProcess::DispatchMsg(std::shared_ptr<LogInnerData> inner_data_ptr) {
  const LogInnerData& inner_data = *inner_data_ptr;
  bool enable_ocpm_detail_perf = AccountKconfUtil::enableOcpmDetailPerf();
  uint64_t campaign_id  = inner_data.campaign_id;
  uint64_t account_id = inner_data.account_id;
  uint64_t unit_id = inner_data.unit_id;
  std::string group_tag = inner_data.tag;
  bool  not_cold_start_bid = inner_data.charge_type != "1" && inner_data.charge_type != "2" &&
                              inner_data.charge_type != "3";
  bool is_fanstop_campaign =
      (inner_data.campaign_type == "AD_FANSTOP_LIVE_TO_FANS" ||
       inner_data.campaign_type == "AD_FANSTOP_LIVE_TO_SHOW" ||
       inner_data.campaign_type == "AD_FANSTOP_LIVE_TO_ALL" ||
       inner_data.campaign_type == "AD_FANSTOP_TO_FANS" ||
       inner_data.campaign_type == "AD_FANSTOP_TO_SHOW" ||
       inner_data.campaign_type == "AD_FANSTOP_TO_ALL");
  std::string ocpx_str = kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type);

  // 修改 group_tag 为 union_tag 合并调价
  std::string union_tag;
  bool union_tag_flag = false;
  if (group_tag == "adlogfull_ocpm" ||
      group_tag == "adlogfull_ocpm_exp1" ||
      group_tag == "adlogfull_ocpm_exp2" ||
      group_tag == "adlogfull_ocpm_exp3") {
    union_tag = base::StringReplace(group_tag, "adlogfull_ocpm", "union_tag", true);
    union_tag_flag = true;
  } else if (group_tag == "search" ||
             group_tag == "search_exp1" ||
             group_tag == "search_exp2" ||
             group_tag == "search_exp3") {
    union_tag = base::StringReplace(group_tag, "search", "union_tag", true);
    union_tag_flag = true;
  } else if (group_tag == "inspire" ||
             group_tag == "inspire_exp1" ||
             group_tag == "inspire_exp2" ||
             group_tag == "inspire_exp3") {
    union_tag = base::StringReplace(group_tag, "inspire", "union_tag", true);
    union_tag_flag = true;
  } else if (group_tag == "feed2slide" ||
             group_tag == "feed2slide_exp1" ||
             group_tag == "feed2slide_exp2" ||
             group_tag == "feed2slide_exp3") {
    union_tag = base::StringReplace(group_tag, "feed2slide", "union_tag", true);
    union_tag_flag = true;
  } else if (group_tag == "slidewindow" ||
             group_tag == "slidewindow_exp1" ||
             group_tag == "slidewindow_exp2" ||
             group_tag == "slidewindow_exp3") {
    union_tag = base::StringReplace(group_tag, "slidewindow", "union_tag", true);
    union_tag_flag = true;
  }
  if (SPDM_enableGetAllUnitIdInTrigger()) {
    auto p_all_unit_ids = CampaignManager::Instance().GetAllUnitIdByCampaignId(campaign_id);
    perf::Interval(p_all_unit_ids->size(),
                   "all_unit_ids_size_in_trigger",
                   kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                   kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                   inner_data.tag,
                   inner_data.campaign_type);
  }
  auto now_time_us = TimeUtil::Instance().GetTimestamp();
  // campaign_id -> unit_ids
  CampaignId2UnitIdsPtr all_wentou_campaign_unit_ids = std::make_shared<CampaignId2UnitIds>();
  int64_t max_unit_size = 0;
  int32_t wentou_campaign_cnt = 0;
  if (SPDM_enableWentouRemoteIndex()) {
    if (SPDM_enableUseAsyncInvertWentouIndex()) {
      auto p_wentou_campaign_ids = AccountManager::Instance().GetAllWentouCampaignIdByAccountId(account_id);
      wentou_campaign_cnt = p_wentou_campaign_ids->size();
      for (auto& campaign_id : *p_wentou_campaign_ids) {
        auto p_all_unit_ids = CampaignManager::Instance().GetAllUnitIdByCampaignId(campaign_id);
        max_unit_size = std::max(max_unit_size, static_cast<int64_t>(p_all_unit_ids->size()));
        // 插入所有稳投 campaign 的 unit_id
        all_wentou_campaign_unit_ids->insert_or_assign(campaign_id, p_all_unit_ids);
      }
    } else {
      // 获取当前 account 所有的 campaign
      auto p_all_campaign_ids = AccountManager::Instance().GetAllCampaignIdByAccountId(account_id);
      for (auto& campaign_id : *p_all_campaign_ids) {
        if (!IsWentouCampaignIndex(campaign_id))
          continue;
        auto p_all_unit_ids = CampaignManager::Instance().GetAllUnitIdByCampaignId(campaign_id);
        wentou_campaign_cnt++;
        max_unit_size = std::max(max_unit_size, static_cast<int64_t>(p_all_unit_ids->size()));
        // 插入所有稳投 campaign 的 unit_id
        all_wentou_campaign_unit_ids->insert_or_assign(campaign_id, p_all_unit_ids);
      }
    }
    perf::Interval(wentou_campaign_cnt,
                  "wentou_campaign_cnt_in_trigger",
                  kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                  kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                  inner_data.tag,
                  inner_data.campaign_type);
    perf::Interval(all_wentou_campaign_unit_ids->size(),
                  "all_campaign_unit_ids_size_in_trigger",
                  kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                  kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                  inner_data.tag,
                  inner_data.campaign_type);
    perf::Interval(max_unit_size,
                  "max_unit_size_in_trigger",
                  kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                  kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                  inner_data.tag,
                  inner_data.campaign_type);
    auto last_time_us = TimeUtil::Instance().GetTimestamp();
    perf::Interval(last_time_us - now_time_us, "trigger_timestamp", "all_campaign_unit_ids");
  }
  bool is_auto_manage_open = (inner_data.bid_type == kuaishou::ad::AdEnum::OCPM_DSP
                              && IsAutoManageOpen(campaign_id));
  bool enable_fix_cold_start_tag = AccountKconfUtil::enableFixColdStartTag();
  if (inner_data.bid_type == kuaishou::ad::AdEnum::OCPM_DSP
      && inner_data.campaign_type != "MERCHANT_RECO_PROMOTE"
      && inner_data.campaign_type != "LIVE_STREAM_PROMOTE"
      && !is_fanstop_campaign
      && inner_data.account_type != "ACCOUNT_FANSTOP_TEMU"
      && (not_cold_start_bid || enable_fix_cold_start_tag)) {
    if (inner_data.is_universe_opt &&
        inner_data.log_action_type == kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION) {
      // 过滤非聚合消息
      bool is_aggr_msg = inner_data.is_aggr_msg;
      if (SPDM_enableAggrOuterOcpmMsg()) {
        is_aggr_msg = inner_data.aggr_outer_ocpm_msg.is_aggr_msg;
      }
      if (!is_aggr_msg) {
        perf::Inc("adlogforalgo.universe_impression_aggr_fliter", "skip_no_aggr_log");
        return false;
      }
    }
    if (enable_ocpm_detail_perf) {
      perf::Count(inner_data.price_sum,
                  "ocpm_account_price1",
                  kuaishou::ad::AdActionType_Name(inner_data.ocpc_action_type),
                  "",
                  kuaishou::ad::AdCallbackLog_EventType_Name(inner_data.deep_conversion_type),
                  inner_data.tag,
                  inner_data.campaign_type);
    }
    // 过滤非 flink 的下发消息
    if (inner_data.log_action_type == kuaishou::ad::AdActionType::AD_DELIVERY &&
        !inner_data.is_from_flink) {
      perf::Inc("adlogforalgo.aggr_fliter", "skip_no_flink_delivery_log");
      return false;
    }
    auto price = inner_data.price;
    auto record_gsp_price = inner_data.record_gsp_price;
    if (SPDM_enableAggrOuterOcpmMsg() && inner_data.aggr_outer_ocpm_msg.is_aggr_msg) {
      price = inner_data.price_sum;
      record_gsp_price = inner_data.aggr_outer_ocpm_msg.record_gsp_price_sum;
    }
    AccountCpaMsg account_cpa_msg(account_id,
                                inner_data.llsid,
                                inner_data.charge_tag,
                                group_tag,
                                price,
                                record_gsp_price,
                                inner_data.cpa_bid,
                                inner_data.action_count,
                                inner_data.delivery_timestamp,
                                inner_data.log_process_timestamp,
                                kuaishou::ad::AdEnum::UNKNOWN_AD_QUEUE_TYPE,
                                inner_data_ptr,
                                not_cold_start_bid,
                                all_wentou_campaign_unit_ids);
    PushToStrategy(account_id,
                  RawMsgType::OCPMAccountCpaMsg,
                  std::make_shared<rttr::variant>(account_cpa_msg));
    if (union_tag_flag) {
      // union_tag 整体，不区分流量位和软硬广的消息
      auto price = inner_data.price;
      auto record_gsp_price = inner_data.record_gsp_price;
      if (SPDM_enableAggrOuterOcpmMsg() && inner_data.aggr_outer_ocpm_msg.is_aggr_msg) {
        price = inner_data.price_sum;
        record_gsp_price = inner_data.aggr_outer_ocpm_msg.record_gsp_price_sum;
      }
      AccountCpaMsg union_cpa_msg(account_id,
                                  inner_data.llsid,
                                  inner_data.charge_tag,
                                  union_tag,
                                  price,
                                  record_gsp_price,
                                  inner_data.cpa_bid,
                                  inner_data.action_count,
                                  inner_data.delivery_timestamp,
                                  inner_data.log_process_timestamp,
                                  kuaishou::ad::AdEnum::UNKNOWN_AD_QUEUE_TYPE,
                                  inner_data_ptr,
                                  not_cold_start_bid,
                                  all_wentou_campaign_unit_ids);
      PushToStrategy(account_id,
                     RawMsgType::OCPMAccountCpaMsg,
                     std::make_shared<rttr::variant>(union_cpa_msg));
    }
    std::string default_tag = GetDefaultGroupTagBySubTag(group_tag);
    if (default_tag != group_tag) {
      LOG_EVERY_N(INFO, 10000) << "default_tag != group_tag default_tag=" << default_tag
                              << " group_tag=" << group_tag;
      // 主 tag 不论如何都会发消息
      auto price = inner_data.price;
      auto record_gsp_price = inner_data.record_gsp_price;
      if (SPDM_enableAggrOuterOcpmMsg() && inner_data.aggr_outer_ocpm_msg.is_aggr_msg) {
        price = inner_data.price_sum;
        record_gsp_price = inner_data.aggr_outer_ocpm_msg.record_gsp_price_sum;
      }
      AccountCpaMsg account_cpa_msg(account_id,
                                  inner_data.llsid,
                                  inner_data.charge_tag,
                                  default_tag,
                                  price,
                                  record_gsp_price,
                                  inner_data.cpa_bid,
                                  inner_data.action_count,
                                  inner_data.delivery_timestamp,
                                  inner_data.log_process_timestamp,
                                  kuaishou::ad::AdEnum::UNKNOWN_AD_QUEUE_TYPE,
                                  inner_data_ptr,
                                  not_cold_start_bid,
                                  all_wentou_campaign_unit_ids);
      PushToStrategy(account_id,
                    RawMsgType::OCPMAccountCpaMsg,
                    std::make_shared<rttr::variant>(account_cpa_msg));
    }

    // 无论如何都发送主 union_tag
    std::string default_union_tag = GetDefaultGroupTagBySubTag(union_tag);
    if (union_tag_flag &&
        default_union_tag != union_tag) {
      // 保证主 tag 发送，区分流量位+软硬广的消息，用作计算 流量位_软硬 成本率
      auto price = inner_data.price;
      auto record_gsp_price = inner_data.record_gsp_price;
      if (SPDM_enableAggrOuterOcpmMsg() && inner_data.aggr_outer_ocpm_msg.is_aggr_msg) {
        price = inner_data.price_sum;
        record_gsp_price = inner_data.aggr_outer_ocpm_msg.record_gsp_price_sum;
      }
      AccountCpaMsg union_main_cpa_msg(account_id,
                                      inner_data.llsid,
                                      inner_data.charge_tag,
                                      default_union_tag,
                                      price,
                                      record_gsp_price,
                                      inner_data.cpa_bid,
                                      inner_data.action_count,
                                      inner_data.delivery_timestamp,
                                      inner_data.log_process_timestamp,
                                      kuaishou::ad::AdEnum::UNKNOWN_AD_QUEUE_TYPE,
                                      inner_data_ptr,
                                      not_cold_start_bid,
                                      all_wentou_campaign_unit_ids);
      PushToStrategy(account_id,
                     RawMsgType::OCPMAccountCpaMsg,
                     std::make_shared<rttr::variant>(union_main_cpa_msg));
    }

    perf::Inc("send_ocpm_account_msg");
  }

  bool is_new_inner_fanstop_adlog
    = (!inner_data.customer_type)
      && inner_data.is_new_inner;
  bool is_c_fanstop =
        is_fanstop_campaign &&
        (inner_data.account_type == "ACCOUNT_FANSTOP_V2") &&
        (inner_data.customer_type || is_new_inner_fanstop_adlog);
  bool is_biz_fanstop =
        is_fanstop_campaign &&
        (inner_data.account_type == "ACCOUNT_CPC");
  bool is_social_fanstop =
        is_fanstop_campaign &&
        (inner_data.account_type == "ACCOUNT_SOCIAL") &&
        SPDM_enableSocailFanstopSupportBid();
  bool is_temu_fanstop =
        (inner_data.account_type == "ACCOUNT_FANSTOP_TEMU");
  if (is_c_fanstop || is_biz_fanstop || is_temu_fanstop || is_social_fanstop) {
    if (inner_data.log_action_type == kuaishou::ad::AdActionType::AD_DELIVERY && !inner_data.is_from_flink) {
      // 在放量白名单  ->  过滤 不是来自 flink 的 AD_DELIVERY 消息
      perf::Inc("bid_server_account_adlogforalgo_fanstop_aggr_filter", "filter_proxy_white_log");
      return false;
    }

    std::string default_tag = GetDefaultGroupTagBySubTag(group_tag);  // 这里是粉条的主副 tag
    FanstopUnitCpaMsg fanstop_unit_cpa_msg(account_id,
                                           unit_id,
                                           inner_data.charge_tag,
                                           default_tag,  // 主 tag 一直发
                                           inner_data_ptr);
    PushToStrategy(account_id,
                   RawMsgType::FanstopUnitCpaMsg,
                   std::make_shared<rttr::variant>(fanstop_unit_cpa_msg));
    perf::Inc("send_fanstop_unit_msg", default_tag);

    if (default_tag != group_tag) {
      // 说明现在的 group_tag 是副 tag，上面发了 default_tag 的数据, 这里就发一份副 tag 的数据
      FanstopUnitCpaMsg fanstop_unit_cpa_msg(account_id,
                                            unit_id,
                                            inner_data.charge_tag,
                                            group_tag,  // 副 tag
                                            inner_data_ptr);
      PushToStrategy(account_id,
                    RawMsgType::FanstopUnitCpaMsg,
                    std::make_shared<rttr::variant>(fanstop_unit_cpa_msg));
      perf::Inc("send_fanstop_unit_msg", group_tag);
    }
  }

  if (inner_data.source_type == kuaishou::ad::AdSourceType::BRAND) {  // 品牌数据流分发
    BrandUnitCpaMsg cpa_msg(account_id,
                            unit_id,
                            inner_data.charge_tag,
                            inner_data_ptr);
    PushToStrategy(account_id,
                   RawMsgType::BrandUnitCpaMsg,
                   std::make_shared<rttr::variant>(cpa_msg));
    perf::Inc("send_brand_unit_msg");
  }
  return true;
}

void MsgProcess::PerfRawMsg(RawMsgType type, const std::shared_ptr<rttr::variant>& value) {
  if (!SPDM_enablePerfRawMsgFields()) {
    return;
  }
  PerfAccountMsgField(type, value);
}

}  // namespace bid_server
}  // namespace ks
