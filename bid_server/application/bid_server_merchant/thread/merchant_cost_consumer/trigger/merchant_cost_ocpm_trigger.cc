// Authors: <AUTHORS>
#include "teams/ad/bid_server/application/bid_server_merchant/thread/merchant_cost_consumer/trigger/merchant_cost_ocpm_trigger.h"

#include <memory>
#include <utility>
#include <string>
#include "absl/strings/substitute.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/strings/string_number_conversions.h"
#include "teams/ad/bid_server/application/bid_server_merchant/thread/merchant_cost_consumer/trigger/trigger_mgr.h"
#include "teams/ad/ad_proto/kuaishou/ad/dw/ad_log_for_algo.pb.h"
#include "teams/ad/ad_base/src/common/latency_record.h"
#include "teams/ad/bid_server/framework/manager/unit_shard_mgr.h"
#include "teams/ad/bid_server/application/bid_server_merchant/kconf/kconf.h"
#include "teams/ad/ad_base/src/common/latency_record_perfutil.h"
#include "teams/ad/bid_server/framework/common/common.h"
#include "teams/ad/bid_server/framework/index/bid_index_manager.h"
#include "falcon/counter.h"
#include "teams/ad/bid_server/application/bid_server_merchant/util/util.h"
#include "teams/ad/bid_server/bid_server_main/trigger/adlogfull_inner_data.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_main/merchant_main_bid_msg.h"
#include "teams/ad/bid_server/application/bid_server_merchant/data_mgr/merchant_data_mgr.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/merchant_ocpm_msg.h"
#include "teams/ad/bid_server/base/strategy/lowest_cost/cost_cap_util.h"
#include "teams/ad/bid_server/framework/utils/common_util.h"
#include "teams/ad/bid_server/framework/utils/spdm_switches.h"

DECLARE_bool(enable_bid_no_diff_switch);

namespace ks {
namespace bid_server {

using kuaishou::ad::dw::AdLogForAlgo;
using ::ks::ad_base::LatencyRecordPrefUtil;

static const char kMerchantCostOcpmTriggerPerfutilNamespace[] =
  "bid_server_merchant.merchant_cost_ocpm_trigger";

MerchantCostOcpmTrigger::MerchantCostOcpmTrigger() {
  kNamespace_ = kMerchantCostOcpmTriggerPerfutilNamespace;
  if (MerchantDataMgr::Instance().IsTestEnv()) {
    kNamespace_ = absl::Substitute("$0_$1", kNamespace_, "test");
  }
}

bool MerchantCostOcpmTrigger::IsInvalidMsg(const AdLogForAlgo &msg) {
  // 丢弃无用数据
  bool enable_play_seconds = false;
  if (MerchantKconfUtil::enablePlaySecondsAction() &&
      (msg.ocpc_action_type()  == "AD_LIVE_AUDIENCE_FAST" ||
      msg.ocpc_action_type()  == "AD_AUDIENCE_FAST") && msg.action_type() == "AD_LIVE_PLAYED_SECONDS") {
    falcon::Inc("bid_server_merchant.enable_play_seconds");
    enable_play_seconds = true;
  }
  if (MerchantKconfUtil::enablePcAudiencePlaySecondsAction() &&
      (msg.ocpc_action_type()  == "AD_LIVE_AUDIENCE" ||
      msg.ocpc_action_type()  == "AD_LIVE_AUDIENCE_QUALITY") &&
      msg.action_type() == "AD_LIVE_PLAYED_SECONDS") {
    falcon::Inc("bid_server_merchant.enable_pc_audience_play_seconds");
    enable_play_seconds = true;
  }
  if (MerchantKconfUtil::merchantLiveDisableActionType()->count(msg.action_type()) == 1 &&
      !enable_play_seconds) {
    if (msg.price() <= FLT_EPSILON) {
      // 这里有过滤
      if (!(msg.action_type() == "AD_LIVE_IMPRESSION" &&
          (msg.ocpc_action_type()  == "AD_MERCHANT_ROAS"
             || msg.ocpc_action_type()  == "AD_MERCHANT_T7_ROI"
             || msg.ocpc_action_type()  == "EVENT_ORDER_PAIED"))) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.bid_server", "disable_action_type_log",
                                           "MerchantCostOcpmTrigger", "disable_action_type",
                                           msg.action_type());
        return true;
      }
    } else {
      LOG_EVERY_N(INFO, 1000) << "skip disable action_type:" << msg.action_type()
        << ", campaign_type:" << msg.campaign_type()
        << ", group_tag:" << msg.bid_server_group_tag()
        << ", price:" << msg.price();
    }
  }
  // 实验使用
  if (MerchantKconfUtil::merchantLiveExpDisableActionTypeUnitTail()->count(msg.unit_id() % 100) == 1) {
    if (MerchantKconfUtil::merchantLiveExpDisableActionType()->count(msg.action_type()) == 1) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.bid_server", "disable_action_type_log",
        "MerchantCostOcpmTrigger", "exp_disable_action_type",
        msg.action_type());
      return true;
    }
  }
  // NO_BID 跳过
  auto speed_type = static_cast<kuaishou::ad::AdEnum::SpeedType>(msg.speed_type());
  if (speed_type == kuaishou::ad::AdEnum::SPEED_NO_BID) {
    return true;
  }
  auto bid_state_info_ptr = BID_STATE.GetBidStateInfo(msg.unit_id());
  if (bid_state_info_ptr &&
      bid_state_info_ptr->speed_type == kuaishou::ad::AdEnum::SPEED_NO_BID) {
    return true;
  }
  // 短视频全站迁移走短视频 roas trigger
  if (IsPhotoStoreWideTrans(msg)) {
    return true;
  }
  // 短视频加速探索 2.0 走 bid_server_main
  if (IsPhotoAccIncrementTrans(msg)) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.bid_server",
    "invalid_msg_acc_increment", msg.bid_server_group_tag(),
    msg.ocpc_action_type(), msg.action_type());
    return true;
  }
  return false;
}

int32_t MerchantCostOcpmTrigger::OnMsg(const AdLogForAlgo& log) {
  int32_t res = 0;
  TRIGGER_SKIP_CHECK_ZERO
  if (!MerchantBidConfMgr::Instance().GetServerConfig().enable_ocpm_trigger()) {
    return res;
  }
  if (log.log_source_type() == "client_callback") {
    ks::infra::PerfUtil::CountLogStash(1, "ad.bid_server",
                                       "bid_server_merchant_skip_merchant_roi_order_paid_data",
                                       "MerchantCostOcpmTrigger", log.action_type());
    return res;
  }
  // 全店 roi 不走 IsTrans 逻辑
  bool is_storewide_switch = (log.scene_oriented_type() == 21 || log.scene_oriented_type() == 30);
  if (log.ocpc_action_type() != "AD_STOREWIDE_ROAS" && !is_storewide_switch &&
      !IsTrans(log.campaign_type(), log.unit_id(), log.bid_server_group_tag())) {
    LOG_EVERY_N(INFO, 1000) << "ProcessOcpmLog::IsTrans " << log.unit_id();
    return res;
  }

  if (IsInvalidMsg(log)) {
    LOG_EVERY_N(INFO, 100000) << "invalid_message.";
    ks::infra::PerfUtil::CountLogStash(1, kNamespace_, "invalid_msg");
    return res;
  }

  if (log.speed_type() == 3 &&
      MerchantKconfUtil::enableLowestCostTrigger()) {
    return res;
  }
  ++res;
  ProcessOcpmLog(log);
  if (log.price() > 0) {
    ks::infra::PerfUtil::CountLogStash(log.price(), kNamespace_, "msg_cnt",
      base::Uint64ToString(log.unit_id()));
  }
  auto speed_type = kuaishou::ad::AdEnum::SpeedType_Name(
    static_cast<kuaishou::ad::AdEnum::SpeedType>(log.speed_type()));
  ks::infra::PerfUtil::CountLogStash(1, "ad.bid_server", "process_unit_info", "MerchantCostOcpmTrigger",
    log.campaign_type(), log.promotion_type(), log.ocpc_action_type(), speed_type);
  return res;
}

void MerchantCostOcpmTrigger::ProcessOcpmLog(const AdLogForAlgo& log) {
  LOG_EVERY_N(INFO, 100000) << "ProcessOcpmLog: unit_id: " << log.unit_id();
  std::string bid_group_tag = GetMerchantBidGroupTag(log.bid_server_group_tag());
  MerchantUnitInfoData ctx = TransforToMerchantUnitInfoData(log);
  ctx.group_tag = bid_group_tag;
  auto& degrade_conf = MerchantKconfUtil::DegradeConfManage()->data();
  bool enable_degrade = degrade_conf.pb().enable_degrade();
  bool is_in_degrade_scope = enable_degrade &&
                              degrade_conf.InDegrade("inner_bid_ad_log",
                                        log.account_id(), log.campaign_id(),
                                        log.ocpc_action_type(), log.campaign_type());
  // 应收流的 price 即实际的 cost
  MerchantOcpmMsg bid_msg(log);
  if (FLAGS_enable_bid_no_diff_switch) {
    bid_msg.is_hot_unit = false;
    bid_msg.is_degrade = false;
  } else {
    bid_msg.is_hot_unit = IsHotUnit(bid_msg.unit_id);
    bid_msg.is_degrade = is_in_degrade_scope;
  }
  if (FLAGS_enable_bid_no_diff_switch) {
    bid_msg.msg_seq = TimeUtil::GetMsgCount();
  }
  // 热点消息降级
  if (bid_msg.is_hot_unit && !FLAGS_enable_bid_no_diff_switch) {
    if (MerchantKconfUtil::merchantLiveHotDisableActionType()->count(log.action_type()) == 1) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.bid_server", "disable_action_type_log",
        "MerchantCostOcpmTrigger", "hot_disable_action_type",
        log.action_type());
      return;
    }
    BidStateInfoPtr bid_info_ptr =
      BID_STATE.GetBidStateInfo(bid_msg.unit_id);
    if (bid_info_ptr) {
      if (bid_info_ptr->left_budget <= 1e-6) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.bid_server", "disable_action_type_log",
            "MerchantCostOcpmTrigger", "no_left_budget", log.action_type());
        return;
      }
    } else {
      ks::infra::PerfUtil::CountLogStash(1, "ad.bid_server", "disable_action_type_log",
        "MerchantCostOcpmTrigger", "bid_index_nullptr", log.action_type());
      return;
    }
  }
  LOG_EVERY_N(INFO, 100000) << "ProcessOcpmLog::log:"
            << " uint_id: " << log.unit_id()
            << " cost: " << log.price()
            << " author_id: " << log.author_id()
            << " account_id: " << log.account_id()
            << " group_tag: " << log.bid_server_group_tag()
            << " cost_total: " << bid_msg.cost;
  if (FLAGS_enable_bid_no_diff_switch) {
    LOG_EVERY_N(INFO, 1) << "no_diff_test,merchant_ocpm, MerchantOcpmMsg, ad_log_seq:"
                         << TimeUtil::GetMsgCount()
                         << ",  ad_log:" << log.ShortDebugString()
                         << ",  event_server_timestamp:" << log.event_server_timestamp() * 1000;
  }
  bool is_filter_ocpm = false;
  if (SPDM_enableAddMerchantNewTopic()) {
    if (SPDM_enableAggrMerchantOcpmMsg()) {   // 消费新 topic, 过滤老 topic
      is_filter_ocpm = !log.aggr_merchant_ocpm_msg().is_aggr_merchant_ocpm_msg();
    } else {                                  // 消费老 topic, 过滤新 topic
      is_filter_ocpm = log.aggr_merchant_ocpm_msg().is_aggr_merchant_ocpm_msg();
    }
  }
  if (!is_filter_ocpm) {
    if (SPDM_enableMerchantTriggerFillIds()) {
      bid_msg.FillIndexIds(log);
    }
    MerchantPartitionApi::PushToStrategy(log.account_id(), log.unit_id(),
        RawMsgType::MerchantOcpmMsg,
        std::make_shared<rttr::variant>(bid_msg));
    const auto abopt_send_uniontag_tail = AdKconfUtil::MerchantABOptimizeSendUnionTagTail();
    if (abopt_send_uniontag_tail &&
      abopt_send_uniontag_tail->IsOnFor(log.account_id())) {
        std::string union_tag = "union_tag";
        std::string origin_group_tag = "union_tag";
        std::swap(bid_msg.group_tag, union_tag);
         std::swap(bid_msg.origin_group_tag, origin_group_tag);
        MerchantPartitionApi::PushToStrategy(log.account_id(), log.unit_id(),
                RawMsgType::MerchantOcpmMsg,
                std::make_shared<rttr::variant>(std::move(bid_msg)));
        std::swap(bid_msg.group_tag, union_tag);
         std::swap(bid_msg.origin_group_tag, origin_group_tag);
    }
  }
  falcon::Inc(absl::Substitute("$0.$1", kNamespace_, "msg_cnt").c_str());
  // costcap 实验订单同时推送 NativeLowestCost 消息更新 nobid context
  BidStateInfoPtr bid_info_ptr =
    BID_STATE.GetBidStateInfo(log.unit_id());
  if (IsOcpmCostCap(log.unit_id(), bid_info_ptr)) {
    std::shared_ptr<InnerData> inner_data = std::make_shared<InnerData>();
    inner_data->ParseMsg(log);
    NativeLowestCostMsg msg(inner_data,
                            bid_info_ptr,
                            inner_data->unit_id,
                            inner_data->campaign_id,
                            inner_data->delivery_timestamp,
                            IsHotUnit(inner_data->unit_id));
    if (FLAGS_enable_bid_no_diff_switch) {
      msg.msg_seq = TimeUtil::GetMsgCount();
    }
    if (FLAGS_enable_bid_no_diff_switch) {
      LOG_EVERY_N(INFO, 1) << "no_diff_test,merchant_ocpm, MerchantOcpmCostCapMsg, ad_log_seq:"
                           << TimeUtil::GetMsgCount()
                           << ",  ad_log:" << log.ShortDebugString()
                           << ",  event_server_timestamp:" << log.event_server_timestamp() * 1000;
    }
    if (!SPDM_enableAddMerchantNewTopic() ||
        SPDM_enableAggrNativeLowestCostMsg() == log.aggr_merchant_ocpm_msg().is_aggr_merchant_ocpm_msg()) {
      if (SPDM_enableMerchantTriggerFillIds()) {
        bid_msg.FillIndexIds(log);
      }
      MerchantPartitionApi::PushToStrategy(log.account_id(), log.unit_id(),
          RawMsgType::MerchantOcpmCostCapMsg,
          std::make_shared<rttr::variant>(msg));

      const auto abopt_send_uniontag_tail = AdKconfUtil::MerchantABOptimizeSendUnionTagTail();
      if (abopt_send_uniontag_tail &&
        abopt_send_uniontag_tail->IsOnFor(log.account_id()) && msg.inner_data) {
        int32_t union_tag = ad_base::AutoBidGroupTags::union_tag;
        std::swap(msg.inner_data->ad_bid_server_group_tag, union_tag);
        MerchantPartitionApi::PushToStrategy(log.account_id(), log.unit_id(),
                                             RawMsgType::MerchantOcpmCostCapMsg,
                                             std::make_shared<rttr::variant>(msg));
        std::swap(msg.inner_data->ad_bid_server_group_tag, union_tag);
      }
      ks::infra::PerfUtil::CountLogStash(1, kNamespace_, "process_ocpm_costcap_log",
                                        base::Uint64ToString(log.unit_id()));
    }
  }
  const auto acc_explore_campaign_tail = MerchantKconfUtil::storewideAccExploreCampaignTail();
  bool is_acc_explore_campaign = false;
  if (SPDM_enableStorewideAccCampaign() && acc_explore_campaign_tail && bid_info_ptr &&
      acc_explore_campaign_tail->IsOnFor(bid_info_ptr->campaign_id) && bid_info_ptr->explore_put_type == 3) {
    is_acc_explore_campaign = true;
  }
  bool is_not_storewide_inc = bid_info_ptr && (bid_info_ptr->explore_put_type == 2 || is_acc_explore_campaign) &&  // NOLINT
        log.scene_oriented_type() == 21 && !log.is_increment_explore();
  if ((IsNobidAcc(bid_info_ptr) || IsLiveAccIncrement(bid_info_ptr, log.is_increment_explore()))
      && !is_not_storewide_inc) {
    if (!SPDM_enableAddMerchantNewTopic() ||
        SPDM_enableAggrNativeLowestCostMsg() == log.aggr_merchant_ocpm_msg().is_aggr_merchant_ocpm_msg()) {
      std::shared_ptr<InnerData> inner_data = std::make_shared<InnerData>();
      inner_data->ParseMsg(log);
      NativeLowestCostMsg msg(inner_data,
                              bid_info_ptr,
                              inner_data->unit_id,
                              inner_data->campaign_id,
                              inner_data->delivery_timestamp,
                              IsHotUnit(inner_data->unit_id));
      if (FLAGS_enable_bid_no_diff_switch) {
        msg.msg_seq = TimeUtil::GetMsgCount();
      }
      if (FLAGS_enable_bid_no_diff_switch) {
        LOG_EVERY_N(INFO, 1) << "no_diff_test,merchant_ocpm, MerchantOcpmNobidAccMsg, ad_log_seq:"
                             << TimeUtil::GetMsgCount()
                             << ",  ad_log:" << log.ShortDebugString()
                             << ",  event_server_timestamp:" << log.event_server_timestamp() * 1000;
      }
      if (SPDM_enableMerchantTriggerFillIds()) {
        msg.FillIndexIds(log);
      }
      MerchantPartitionApi::PushToStrategy(log.account_id(), log.unit_id(),
          RawMsgType::MerchantOcpmNobidAccMsg,
          std::make_shared<rttr::variant>(msg));
      const auto abopt_send_uniontag_tail = AdKconfUtil::MerchantABOptimizeSendUnionTagTail();
      if (abopt_send_uniontag_tail &&
        abopt_send_uniontag_tail->IsOnFor(log.account_id()) && msg.inner_data) {
        int32_t union_tag = ad_base::AutoBidGroupTags::union_tag;
        std::swap(msg.inner_data->ad_bid_server_group_tag, union_tag);
        MerchantPartitionApi::PushToStrategy(log.account_id(), log.unit_id(),
                                             RawMsgType::MerchantOcpmNobidAccMsg,
                                             std::make_shared<rttr::variant>(msg));
        std::swap(msg.inner_data->ad_bid_server_group_tag, union_tag);
      }
      ks::infra::PerfUtil::CountLogStash(1, kNamespace_, "process_ocpm_nobid_acc_log",
                                        base::Uint64ToString(log.unit_id()));
    }
  }
  if (bid_msg.cost > 0) {
    ks::infra::PerfUtil::CountLogStash(bid_msg.cost, kNamespace_, "process_log_cost_total",
                                       base::Uint64ToString(bid_msg.unit_id));
  }
  if (bid_msg.scene_oriented_type == 21 && bid_msg.gmv > 0) {
    ks::infra::PerfUtil::CountLogStash(bid_msg.gmv, kNamespace_, "process_log_gmv_total",
                                       base::Uint64ToString(bid_msg.unit_id));
  }
  // debug
  if (MerchantKconfUtil::enableStoreWideRoiDebugLog() &&
      bid_msg.scene_oriented_type == 21 &&
      bid_msg.gmv > 0) {
    LOG(INFO) << "StoreWideRoiDebugLog_conversion:"
              << " from_ad "
              << " uint_id: " << log.unit_id()
              << " cost: " << log.price()
              << " author_id: " << log.author_id()
              << " account_id: " << log.account_id()
              << " group_tag: " << log.bid_server_group_tag()
              << " cost_total: " << bid_msg.cost
              << " msg: " << bid_msg;
  }

  auto debug_id_set = MerchantKconfUtil::adMerchantDebugUnitIdSet();
  if (debug_id_set->count(bid_msg.unit_id) > 0) {
    LOG(INFO) << "Debug::ProcessOrderPaidLog::"
              << " unit_id: " << bid_msg.unit_id
              << " campaign_type: " << bid_msg.campaign_type
              << " ocpc_action_type: " << bid_msg.ocpc_action_type
              << " promotion_type: " << bid_msg.promotion_type
              << " item_type: " << bid_msg.item_type
              << " author_id: " << bid_msg.author_id
              << " group_tag: " << bid_msg.group_tag
              << " cost: " << bid_msg.cost
              << " target_cost: " << bid_msg.target_cost;
  }
}


REGISTER_CLASS(MerchantTriggerBase, MerchantCostOcpmTrigger);

}  // namespace bid_server
}  // namespace ks
