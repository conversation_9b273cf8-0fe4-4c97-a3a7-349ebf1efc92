#pragma once

#include <string>
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/bid_server/framework/utils/kconf_data/grouping_config.h"
#include "teams/ad/bid_server/framework/utils/kconf_data/kconf_data.pb.h"
#include "teams/ad/bid_server/application/bid_server_merchant/kconf/kconf_data.h"
#include "teams/ad/bid_server/application/bid_server_merchant/proto/merchant_bid.pb.h"
#include "teams/ad/bid_server/framework/utils/kconf_data/pv_distribution.h"
#include "teams/ad/ad_proto/kuaishou/ad/bid_service/request_unit_sender.pb.h"
#include "teams/ad/bid_server/base/kconf/kconf_data.pb.h"
#include "teams/ad/engine_base/kconf/degrade_conf_manage_conf.h"
#include "teams/ad/bid_server/application/bid_server_merchant/proto/merchant_fanstop_cost_cap.pb.h"

namespace ks {
namespace bid_server {
using namespace ks::ad_base::kconf;  // NOLINT
using kuaishou::ad::MerchantBidTimerMonitorConfig;
using kuaishou::ad::MerchantBidExpConfigMap;
using kuaishou::ad::B2xConfig;
using kuaishou::ad::MerchantROASBidConfig;
using kuaishou::ad::MerchantOcpmBidConfig;
using kuaishou::ad::OperationConfigAutoROI;
using kuaishou::ad::OperationConfigAutoCpaBid;
using kuaishou::ad::MerchantROASExpConfigMap;
using CostCapOcpxUnitTailMap = ks::bid_server::kconf::CostCapOcpxUnitTailMapNew;
using kuaishou::ad::MerchantPhotoROASBidConfig;
using kuaishou::ad::MerchantPhotoROASBidGroupConfig;
using kuaishou::ad::MerchantPhotoROASBidExpConfig;
using kuaishou::ad::BidServerMerchantServerConfig;
using kuaishou::ad::bidService::RequestUnitTaskConfig;
using ks::bid_server::kconf::BidServerFanstopMultiGroupConfig;
using ks::bid_server::kconf::DarkAccExploreConfig;
using ks::bid_server_merchant::kconf::ChargeTagBudgetRateInfoList;
using ks::bid_server_merchant::kconf::FansTopCostCapPidConfig;
using ks::bid_server_merchant::kconf::FansTopCostCapOcpcConfig;
using ks::bid_server_merchant::kconf::FansTopOcpxActionType2ActionType;
using ks::bid_server_merchant::kconf::StepConf;
using NativeNobidExpConfig = ks::bid_server::kconf::NativeNobidExpConfigNew;
using kuaishou::ad::InnerMerchantConvTimeDelayConfig;
using kuaishou::ad::MerchantOcpmLinearPacingGridConfigMap;
using kuaishou::ad::MerchantFanstopCostcapExpConfig;
using kuaishou::ad::MerchantStorewideConfig;
using kuaishou::ad::PhotoPageCaliExpMap;
using kuaishou::ad::UpDimensionCaliExpConfMap;
using kuaishou::ad::RoiPidAjustStrategy;
using ks::bid_server_merchant::kconf::MerchantReinforcementLearningPredictModelConfig;
using ks::bid_server_merchant::kconf::NobidExtendExpConfig;
using ks::bid_server_merchant::kconf::PricePidConfig;
using ks::bid_server_merchant::kconf::AdAutoMLParam;
using ks::bid_server_merchant::kconf::MerchantNobidDTDataConfig;

class MerchantKconfUtil {
 public:
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, clearAllDelayMapInfo, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableParseMerchantOrderPaidLog, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableParseMerchantCostLog, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableBidContextNew, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMerchantBidBoostStart, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMerchantPhotoRoasClearContext, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, useMerchantConsumerTrigger, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMerchantFanstopCampaignTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableEspNobidBound, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCostCapSkipDynamicBound, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableLowestCostTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enablePlaySecondsAction, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enablePcAudiencePlaySecondsAction, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMobileTimerTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCampaignMobileTimerTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCampaignPcTimerTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, isUseIsConversion, true);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, isMerchantOcpmSkipAB, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAuthorUnionExp, true);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFixedPeriodBeginTime, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCostCapSendTraceLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAccNobidSendTraceLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableOptBidGetBound, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopNobidAll, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAccIncrementIndex, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableSkipSourceType, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAudienceFastTPerf, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCboCostCapToOcpm, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFlowControlRedis, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopCostCapFix, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableStorewideMonitor, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableLiveHostingMonitor, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableDarkNobid, false);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, cboStoreWideDefaultStartBid, 0.6)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, costHourTimes, 1.0)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableBidServerSmbFill, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableStorewideSmooth, false)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, storeWideColdstartSmoothCostLi, 100000.0)
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, storeWideColdstartMinCount, 60)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, storeWideColdstartSmoothTargetCostLi, 100000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, roasColdstartSmoothCostLi, 100000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, roasColdstartSmoothTargetCostLi, 100000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, roasPerrorBound, 10.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, account_price_billing_control_ratio, 0.8)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, account_price_billing_control_amount, 200000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, price_billing_dynamic_control, 0.25)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, account_price_billing_control_ratio_roas, 0.9)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, account_price_billing_control_amount_roas, 1000000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, account_price_billing_rate_gt, 2.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, account_price_billing_rate_gt_roas, 1.6)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, account_price_billing_rate_le, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, account_price_billing_rate_le_roas, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, ocpm_pre_convert_upper, 1.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, ocpm_pre_convert_loan_author_upper, 2.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, eop_stable_bid_bound, 2.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, roas_stable_bid_bound, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, no_conv_pre_convert_upper, 500)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, have_conv_pre_convert_upper_ratio, 1.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, roas_no_convert_lower_bound, 0.85)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, hack_author_price_lower, 1.0)
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, slide_window_minute, 10)
  DEFINE_INT64_KCONF_NODE(ad.bidServer3, hostingMpcTimeCostThresh, 120);
  DEFINE_INT64_KCONF_NODE(ad.bidServer3, hostingMpcStepNumControl, 61);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, mpc_t7_control_upper_bound, 1.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, mpc_t7_control_lower_bound, 0.9)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, storewide_cold_start_avgcost_ratio, 0.05)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, hosting_b2x_p_upper_bound, 0.95)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, storewide_rtg_target, 1.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, storewide_low_rtg_target, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, step_ratio_lower_bound, 0.97)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, step_ratio_upper_bound, 1.03)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, high_roi_upper_bound, 1.0)
  DEFINE_PROTOBUF_NODE_KCONF(PricePidConfig, ad.bidServer, priceBillingPidConfig)
  DEFINE_PROTOBUF_NODE_KCONF(PricePidConfig, ad.bidServer, priceBillingPidConfigT7)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, EspLiveDefaultStableBid)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, EspLiveHostingStableBid)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, authorEspLiveDefaultStableBid)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, authorEspLiveHostingStableBid)
  DEFINE_SET_NODE_KCONF(int64, ad.adRank2, newCustomerBsTails);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, storewideHardBoundTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, storewideColdBoundTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, coldStartTimeTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, storewideAddT7Tail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer3, liveHackAuthorList);
  DEFINE_PROTOBUF_NODE_KCONF(AdAutoMLParam, ad.bidServer2, EspLiveEopPIDAutoParams);
  DEFINE_PROTOBUF_NODE_KCONF(AdAutoMLParam, ad.bidServer2, espLiveAutoParmasRoasColdStartLvlThre);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, FanstopCostCapBoostV2RemoveTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, liveHostingItemCailTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, blackHardBoundDynamic, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enhanceBoundDynamic, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, crossDayConverAttributeTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, orderSkipCrossDayResetTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewidePageTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideALLPageTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideItemTypeCaliTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideFollowSelectNatureCaliTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideNatureCaliTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideAccExploreCampaignTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideAccExploreResetTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideAccExploreCampaignReset, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideAccExploreStartBidTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideAccExploreLogTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewidePageBoundProTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, accountControlPriceTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, accountPriceRateTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, roiRatioFromAdLog, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, targetCostControlPriceTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, priceBillingPidTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, priceBillingPidT7Tail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, ltvFixInPreConvertTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, timeIndexFixTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveDefaultStableBidTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveHostingStableBidTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, hostingTargetCostProTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideTargetCostProTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, innerPreConvertModelTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, innerPreConvertModelT7Tail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, roasNoConvertLowerBoundTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, b2xHostingLiveTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, b2xHostingLiveT7Tail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, b2xHostingLiveOrderTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, gimbalPriceControlBillingTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, hostingGimbalPriceControlBillingTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, hostingMpcModelDataTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, moveHackAuthorPriceTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, hostingMpcModelTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, storewideMpcModelTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, storewideColdstartAvgcostTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, hostingB2xFixBugTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, fixBidConvertBugTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, dtWriteTrueValueTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, mpcTimeControlTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, hostingB2xMaxBoundTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, singleStepRatioTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, highRoiFixBoundTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, fixStT7RoiBoundTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, fixStBoundSmoothTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, zhongxiaoRlTail, "");
  // 全站不调价
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, StorewideNoPid, "");
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, noPidLower, 0.95);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, noPidUpper, 1.05);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, storewideSmoothTail);
  DEFINE_SET_NODE_KCONF(int64, ad.adRank2, pureNewCustomerBSTail);
  DEFINE_SET_NODE_KCONF(int64, ad.adRank2, pureNewCustomerGMVBSTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, campaign2UnitTailId);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, whiteHardBoundDynamic);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, byAuthorTypeAdjustSwitch, "");
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fixStorewideIncompatibleTypeCampaignTail);
  DEFINE_SET_NODE_KCONF(int64, ad.adEsp, storewideAllIncompatibleSwitch);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableStorewideDebug, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableUnitAdjustRateDebug, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableUnitAdjustRate, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableStorewidePageBoundPro, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableRoasHierarchicalHardBound, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAccIncrement, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableBidTracelogLiveHosting, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAllPvTraceLogV2, true)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, roasHierarchicalHardBoundConf);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, roasHierarchicalStepRatioConf);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, merchantRoasSendPriceConfigs);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, hostingRoasSendPriceConfigs);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, merchantHostingLivePriceRatioConfigs);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, espT7RoiDefalutGmv0, 10.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, espT7RoiDefalutGmv7, 6.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, espT7RoiDefalutIndirectGmv7, 6.0)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableT7roiRoasCampaignTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, t7roiRoasCampaignTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableMerchantClickStatTailSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableGmvLevelLiveHostingPriceRatioTail);
  DEFINE_STRING_STRING_MAP_KCONF(ad.bidServer, merchantTableCache);
  // 数据回滚检查周期
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchant_rollbackCheckIntervalSeconds, 1);
  // 回滚配置
  DEFINE_PROTOBUF_NODE_KCONF(MerchantRollbackManagerConf, ad.bidServer, merchant_rollbackManagerConf);
  // 重置检查周期
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchant_resetCheckIntervalSeconds, 1);
  // 重置配置
  DEFINE_PROTOBUF_NODE_KCONF(MerchantResetManagerConf, ad.bidServer, merchant_resetManagerConf);
  /// merchant_order_paid_log
  // kafka topic 参数
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchangOrderPaidLogBidServerKafkaTopic, "merchant_order_paid_log_bidserver");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantOrderPaidLogBidServerKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  // ad_merchant_reco_order
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRecoOrderBidServerKafkaTopic, "ad_merchant_reco_order");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRecoOrderBidServerKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  // roi cost
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRoiCostLogKafkaTopic, "bid_server_merchant_roi_cost");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRoiCostLogKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  // roi order
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRoiOrderPaidLogKafkaTopic, "client_log_for_bid_server");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRoiOrderPaidLogKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  // kafka topic 参数
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchangCostLogBidServerKafkaTopic, "merchant_cost_ad_log_full_bidserver");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchangCostLogBidServerNoDiffKafkaTopic, "ad_log_for_algo_bidserver_graph_diff_test");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantCostLogBidServerKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  //  message delay
  DEFINE_INT64_KCONF_NODE(ad.bidServer, maxMerchantKafkaMessageDelaySec, 60*60*12);  // 12 hours
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchantRecoOrderConsumerNum, 10)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchantOrderPaidLogConsumerNum, 16)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchantCostLogConsumerNum, 16)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchantFanstopLogConsumerNum, 16)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, accExploreBaseMs, 600*1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitActivateDoIntervalMs, 30*1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, requestUnitActivateDoIntervalMs, 1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, sjMerchantTestUnitId, 0)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, merchantHotUnitFreqThreshold, 100000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nobidHotUpdateBatchSize, 10000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nobidHotUpdateTimeInterval, 300)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, merchantHotUnitReportFreqThreshold, 1000)

  // nobid
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativePvROICalcMixProportion)  // 短视频 ROI PV 混合分布比例
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, NativePvROIDynamicCostWeight, 1.0)  // 短视频 ROI PV 权重
  // merchant trans
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantLiveTransTailSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantPhotoTransTailSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, unitRequestActiveOffTailSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, innerLiveHighAtvAuthor)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, coldStartAuthorExpSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, coldStartAuthorExpTailSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, MobileFastAudienceUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, MobileFastAudienceUnitTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, mobileAutoBidLiUnitSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, mobileAutoBidLiCampaignSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, dragonInnerLoopDebugUnitIdTail)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileAutoBidLiOcpxUnitTailMap)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, espLiveGspPriceConfigV2)

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, innerRemoveSpamOrderTailSet);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, innerRemoveSpamOrderGroupTag, "");

  // 分行业设置低客单价系数以及折扣系数
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, industryThresholdPrice);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, industryDiscountRatio);

  // config
  DEFINE_PROTOBUF_NODE_KCONF(
    BidServerMerchantServerConfig, ad.bidServer, bidServerMerchantServerConfig);
  DEFINE_PROTOBUF_NODE_KCONF(
      MerchantBidTimerMonitorConfig, ad.bidServer, merchantBidTimerMonitorConfig);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantROASBidConfig, ad.bidServer, merchantROASBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantROASExpConfigMap, ad.bidServer, merchantROASExpConfigMap);

  // unit request activate
  DEFINE_PROTOBUF_NODE_KCONF(RequestUnitTaskConfig, ad.bidServer, requestUnitTaskConfig);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, unitRequestActivateConsumerNum, 16)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, requestUnitActivateGroupTagSet);

  // operation config
  DEFINE_PROTOBUF_NODE_KCONF(OperationConfigAutoROI, ad.bidServer, operationConfigAutoROI);
  DEFINE_PROTOBUF_NODE_KCONF(OperationConfigAutoCpaBid, ad.bidServer, operationConfigAutoCpaBid)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantMonitorAuthorIdSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantMonitorAccountIdSet);

  // ocpm
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmPhotoBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmLiveAudienceBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmLiveFollowBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmLiveOrderBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer2, merchantOcpmPhotoRoasAccBidConf);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, innerPreConvertTimeCostThresh, 10);
  DEFINE_PROTOBUF_NODE_KCONF(B2xConfig, ad.bidServer2, b2xConfigConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantBidExpConfigMap, ad.bidServer, merchantOcpmExpConfigMap);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantBidExpConfigMap, ad.bidServer, merchantUniverseOcpmExpConfigMap);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, isOcpmLiveAd, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, isFanstopOcpmAd, true)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, merchantRUADropRate, 0);
  // 分页面成本
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer2, storewideTrafficSource);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, pageBidCaliBound);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, espLiveAutoParamsBound);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, followSelectPageBidCaliBound);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, pageBidStorewideBound);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, scenceAuthorTypeRatio);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, pageNatureBidStorewideBound);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, storewideClibCostRatio);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, itemBidCaliBound);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer, pageIdToNameDict);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer, pageIdToKeyDict);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer2, followSelectUnionDict);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, toudaWhiteList);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, toudaPriceUnitTailList, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveEopColdStartTransTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveRoasColdStartTransTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveMultiCreativeBidUpdateTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveAccExploreResetContextTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, photoStorewideAccExploreResetContextTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveT7ColdStartTransTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveHostingColdStartTransTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveRoasHostingColdStartTransTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveT7HostingColdStartTransTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLivePageCaliT7RoiSplitTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveLoanAuthorSkipLimitTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveT7RoiPoolTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveStorewideRoasPoolTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, followSelectNewParamsUnionTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, biddingByAuthorTypeTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, followSelectUnionTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, playletPageCaliTail, "");
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer2, espLivePageKeyDict);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer2, storewidePosKeyDict);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer2, storewidePageKeyDict);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer2, storewidePageKeyDictV2);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer2, storewidePageKeyABDict);
  DEFINE_STRING_STRING_MAP_KCONF(ad.bidServer2, storewideFollowSelectDict);
  DEFINE_STRING_STRING_MAP_KCONF(ad.bidServer2, storewideNatureDict);
  DEFINE_STRING_STRING_MAP_KCONF(ad.bidServer2, storewideAllKeyDict);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, skipBidPageCaliAuthorList);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, inspirePageId);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, selectPageId);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, unitConvNumsCostThreshold);
  // 直播托管冷启动不压价
  DEFINE_INT64_KCONF_NODE(ad.bidServer, hostingLiveColdBoostCostThresh, 50000);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveHostingRoasUnionBidTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableFixLiveHostingWriteBidTail, "");
  // 修复托管 T7 调价问题
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveHostingFixT7Tail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveHostingFixT7TailV2, "");

  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, b2xExpTail, "");
  // 托管冷启动素材 boost
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableHostingLiveColdPhotoAuthorV2List);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableHostingLiveColdPhotoAuthorV2Tail);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, hostingLiveColdPhotoBoostRatio, 1.0)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enablehostingLiveWideColdPhotoBoost, false);

  // 直播控成本拆分物料
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, espLiveUnitAutoSplitItemTypeWhite, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, espLiveUnitAutoSplitItemTypeWhiteExp, "");
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, espLiveUnitSplitItemTypeOcpxWhite);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, espLiveUnitSplitItemTypeOcpxWhiteExp);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableUniverseSplitP2l, false);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, skipResetTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, historyRoiTail);
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, notResetTime, 10 * 60 * 1000);
  // 7 日 ROI 拆分物料算转化系数
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, t7RoiCombineItemAuthorRatio, "");

  DEFINE_PROTOBUF_NODE_KCONF(NoDiffLevelConfStruct, ad.bidServer, noDiffLevelConf)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, NoDiffRedisTtl, 3600*2);

  // [xusimin]
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingLiveRLCampaignTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingLiveDTCampaignTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingDTRoasSafeTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingDTOrderSafeTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingLiveDTRoasNewSafeTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingLiveDTOrderNewSafeTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingLiveDTT7NewSafeTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingLiveDTCampaignRoasTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingLiveDTCampaignOrderTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingLiveDTCampaignT7Tail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingLiveRLCampaignCoverTail);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, hostingDtT7roiconfig, "V1");
  DEFINE_INT64_KCONF_NODE(ad.bidServer, hostingLiveRLTimeCostThresh, 10);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, hostingLiveRLBadCountThresh, 10);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, hostingDtLowerbound, 0.7);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, hostingDtUpperbound, 1.3);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, SwDtRatioLower, 1.2);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, SwDtCostLower, 100);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, SwPhotoDtRatioLower, 1.2);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, SwPhotoDtCostLower, 100);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, SwPhotoMpcUpperBound, ********);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, storewideShadowNobidUpperBound, 1.5);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, storewideShadowNobidLowerBound, 0.1);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, SwRlbidRatioLower, 0.99);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, SwRlbidRatioUpper, 1.01);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, t7OptMpcRatio);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, InnerLivePErrorParams);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, InnerLiveDiffErrorParams);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, T7RoiConvertRatioRedisTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, T7RoiConvertRatioPriceRedisTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableT7RoiMpcLiveTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableT7RoiMpcP2lTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, storewideNewBsTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, storewideNewBsPriceTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, storewideDynamicBoundTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, FixHostingT7Tail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, PhotoNewbsTail, "");
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, hosting_t7_smooth_upper_bound, 200000.0);
  DEFINE_PROTOBUF_NODE_KCONF(PricePidConfig, ad.bidServer3, priceBillingPidConfigSw)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, hosting_price_control_cost_lower_bound, 400000.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, hosting_price_control_discount_amount_lower_bound, 400000.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, hosting_discount_amount_lower_bound_eop, 400000.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, hosting_discount_amount_lower_bound_t7, 400000.0);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, SdT7AdjustUpdatetimeIntervalTail, "");
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, sd_t7_adjust_update_time_interval_cost, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, hosting_price_control_ratio_lower_bound, 0.95);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, high_atv_lower_bound, 300000.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, high_atv_price_upper, 4.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, high_atv_bid_rate_upper, 20.0);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, SwDtExpTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, PhotoSwDtExpTail, "");
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enablePhotoSwDt, true);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enableFixLastCostTrace, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enableFixQiandu, false);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer3, SwLiveDtAuthorTailConfigMap);
  DEFINE_STRING_STRING_MAP_KCONF(ad.bidServer3, SwLiveDtGroupTagConfigMap);
  DEFINE_INT64_KCONF_NODE(ad.bidServer3, SwDtAuthorDivisor, 100);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer3, StorewideDtTargetRtg, 300000.0);
  DEFINE_STRING_BOOL_MAP_KCONF(ad.bidServer3, SwBidBoundGroupTagConfigMap);

  // RL 配置
  DEFINE_PROTOBUF_NODE_KCONF(MerchantReinforcementLearningPredictModelConfig,
        ad.bidServer, InnerLoopRlPredictConfig)

  // lowest cost
  DEFINE_KCONF_NODE_LOAD(PVDistribution, ad.bidServer, merchantPhotoFlowDistribution)

  DEFINE_SET_NODE_KCONF(int32, ad.bidServer, MerchantUniverseConfSplitSet);
  // merchant costcap
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, CostCapUnitMaxCostDiscount, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, CostCapUnitCpaBidDiscount, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, CostCapUnitRoiDiscount, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, nobidInitBaseCpaBidDiscount, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FanstopRoasCutoffGuardbidRoiMin, 1.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FanstopRoasCutoffGuardbidRoiValue, 1.5)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, costCapOcpxUnitTailMap)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMerchantCostCap, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableSpecialtyOcpmCostCap, false)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmCostCapUnitIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmCostCapLogUnitIdSet)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, ocpmCostCapOcpxUnitTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, ocpmCostCapOcpxAccountIdMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, ocpmCostCapOcpxAccountTailMap)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, costCapRedisName, "adMerchantROASBid")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, bid2ValueRedisName, "adBidSimulator")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, innerUnitCreativeNumRedisName, "adEmsHostingData")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, nobidAccTransAccountTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, accIncrementTransAccountTail, "")
  DEFINE_TAILNUMBERV2_KCONF(adData.whiteBox, diffLogInnerAccountTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, costCapSkipAdjustValueUnitTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, costCapSkipAdjustValueCampaignTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, optYesterdayCostTail, "")
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enablelspLiveMinCost, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enablelspLiveOp, false)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, lspLiveMinCostTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, lspLiveOpResetTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, lspLiveOpReWriteTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, lspMonitorTail, "");
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, lspLiveMinCostConfig);
  // [lixu05]
  DEFINE_INT64_KCONF_NODE(ad.ems, itemHostingOriginOcpxConvThresMax, 10);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, itemDefaultPriceForMultiOcpx, 5000);  // 兜底客单价，厘
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, minCpaBidForMultiOcpx, 2000);  // 兜底出价，厘
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, maxCpaBidForMultiOcpx, 5000000);  // 兜底出价，厘
  DEFINE_SET_NODE_KCONF(int64, ad.adRank2, newSpuBoostTagList);

  DEFINE_PROTOBUF_NODE_KCONF(ks::engine_base::DegradeConfManageConf, ad.bidServer, DegradeConfManage);

  // 关注页 boost auto_roi
  DEFINE_STRING_STRING_MAP_KCONF(ad.bidServer, merchantRoiAuthorWhiteMap);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, belowZeroPacingRate, 0.5)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, boostAuthorIds)

  // fanstop skip costcap
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, fanstopRoiUpperThre, 1.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, fanstopRoiLowerThre, 0.3)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopSkipCostcapCampaignTailSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopSkipCostcapPayerTailSet)

  // merchant fanstop costcap
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopOcpmCostCapCampaignIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopOcpmCostCapPayerIdSet)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, fanstopOcpmCostCapOcpxUnitTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, fanstopOcpmCostCapOcpxCampaignTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, fanstopOcpmCostCapOcpxPayerIdMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, fanstopOcpmCostCapOcpxPayerTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, nobidPcInitOcpxUnitTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, nobidMobileInitOcpxCampaignTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, ocpmNobidAccOcpxUnitTailMap)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmNobidAccAccountIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmAccIncrementAccountIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmAccIncrementUnitIdSet)
  // fanstop campaign
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopCampaign, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopCampaignLaunchType, false)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopCampaignWhiteList)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopHostingCampaignTail)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, hostingOrderCampaignTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, nobidPriceRatioByLevelTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, mobileHostingCboTail, "");

  // mobile costcap
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, mobileUnitWhiteList)  // 移动端 costcap 暗投白名单
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileCostcapOcpxUnitTailMap)

  // mobile nobid
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileOcpxUnitTailMap)

  // 移动端调价策略架构合并
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, mobileUnit2PcOcpmWhiteList)  // 移动端直播迁移至 PC 端
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileLiveTransPcOcpmOcpxUnitTail)

  // 放贷逻辑作者白名单
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, loansAuthorWhiteList);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, loansAuthorRatioList);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer3, SkipPreConvertAccountList);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer3, SkipMPCAccountList);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer3, EspLiveAccExploreInitBid);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, newOweAuthorLiveList);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer3, newOweAuthorLiveTargetRatioList);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, newOweAuthorLiveUnitTail, "");
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, newOweLoansParams);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, smoothRateMap);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, costRateToBoundMap);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, accExploreShadowBudgetRatio);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, skipRoasMpcTail, "");

  // photo roas
  DEFINE_PROTOBUF_NODE_KCONF(MerchantPhotoROASBidConfig, ad.bidServer, merchantPhotoROASBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantBidExpConfigMap, ad.bidServer, merchantPhotoROASExpConfigMap);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantPhotoROASBidExpConfig, ad.bidServer, merchantPhotoROASBidExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(NativeNobidExpConfig, ad.bidServer, nativeNobidExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantPhotoROASBidGroupConfig, ad.bidServer, merchantPhotoROASBidGroupConf);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer, merchantPhotoROASColdUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, agentAccountWhiteListROAS);

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantLiveBidABTailNums);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantPhotoBidABTailNums);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, adMerchantValidGroupTagSet);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, smoothPacingOcpxSet);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, adMerchantValidOcpmGroupTagSet);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, adMerchantValidUniverseGroupTagSet);

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, dropUnitSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopOcpmTargetCostTestAuthorSet);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, dropUnitRatio, 0);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, dropInnerUnitSet);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, dropInnerUnitRatio, 0);
  DEFINE_INT64_INT64_MAP_KCONF(ad.bidServer, hotDataGuardMap);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, storewideQianduRatioMap);

  // [qiulizhou]
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, photoMpcAdjustOptBidPrefix, "win_bid_cost_photo");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, RoasBid2xWhitelist, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, MpcUseBidWhitelist, "");
  DEFINE_SET_NODE_KCONF(int32, ad.bidServer3, StorewidePhotoNatureAdTagList);
  DEFINE_INT32_KCONF_NODE(ad.bidServer3, StorewidePhotoCacheCostLimit, 1000000);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, PhotoStorewideAccIncrementWhitelist, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, PhotoCidCampaignidWhitelist, "");
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, InnerMerchantPErrorParams);

  // [fukunyang]
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, PhotoMpcFixExpTail, "");
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer3, multiCreativeExplorePriceMapPhoto);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, multiCreativeExplorePricePhotoTail, "");
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer3, multiCreativeExplorePriceMap);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, accExploreAuthorBoundMap);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, multiCreativeExplorePriceTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, PhotoStorewideBidStepLimitTail, "");
  // debug
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, adMerchantDebugUnitIdSet);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableStoreWideRoiDebugLog, false);

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, roasOnlyDirectOrderUnitTail);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, merchantServerTraceKafkaTopic,
      "bid_server_merchant_server_tracelog")
  // 内循环迁移
  DEFINE_SET_NODE_KCONF(int32_t, ad.bidServer, merchantRecoUnitTailSet);
  DEFINE_SET_NODE_KCONF(int32_t, ad.bidServer, merchantFanstopNewNobidTriggerUnitTailSet);
  // 大促计费分离 author 白名单
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, merchantOcpmAuthorWhiteSet);

  DEFINE_INT64_KCONF_NODE(ad.bidServer, bid2valueFetchThreadNum, 2);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitEnterIndexFetchThreadNum, 2);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitIsColdFetchThreadNum, 2);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, bid2valueFetchMaxPendingNum, 1000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitEnterIndexFetchMaxPendingNum, 1000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitIsColdFetchMaxPendingNum, 1000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, bid2valueValidExpireTime, 300);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, bid2valueInValidExpireTime, 120);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitEnterIndexValidExpireTime, 300);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitEnterIndexInValidExpireTime, 120);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitIsColdValidExpireTime, 300);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitIsColdInValidExpireTime, 120);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableBid2valueCache, true);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapFetchThreadNum, 2);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, liveHostingOrderPayAuthorTail, 0);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, liveHostingAllOcpxAuthorTail, 0);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapFetchMaxPendingNum, 1000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapValidExpireTime, 300);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapInValidExpireTime, 120);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapRedisExpireTime, 86400);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, newCustBSAcccountTailConfigs);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, selfServiceAccountBSConfig);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, nobidTempHardBoundConfigs);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, selfServiceBSTail, "");
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer2, selfServiceCaliTail)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, nobidRecallKafka, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, selfServiceNobidPriceRatioROASTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, accExploreExpTail, "");
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, nobidRecallConfig);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, tempHardBoundUnitTail, "");
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, pureNewCustBsAccountConfigs);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, newCustBSFixTails, "");
  // TODO(wangtao21): 测试代码
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, fanstopCostCapV2TransTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, fanstopMerchantFollowTail, "")
  // 内粉业务使用
  DEFINE_STRING_INT64_MAP_KCONF(reco.inner_fanstop, archimedesLowestCostInitBidMap);
  DEFINE_STRING_DOUBLE_MAP_KCONF(reco.inner_fanstop, archimedesLowestParamMap);  // 公式超参控制
  // fanstop 业务使用
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, consumeCheckUnits)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, consumeDebugUnits)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, maxKafkaMessageDelaySec, 120);  // 2 分钟
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopCostCapBoostThreshold, 180000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, currentShowUnitFlushWindowUs, 1000000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestBoostThreshold, 600000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestBoostThreshold, 240000)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, OcpmDspZeroBoostUnitSet)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, LowestCostLiveDefaultDuration, 7200000)
  DEFINE_PROTOBUF_NODE_KCONF(BidServerFanstopMultiGroupConfig, ad.fanstopServer, fanstopMultiGroupConfig)
  DEFINE_PROTOBUF_NODE_KCONF(ChargeTagBudgetRateInfoList, ad.adFlowControl,
      fanstopChargeTagBudgetRateInfoList)
  DEFINE_PROTOBUF_NODE_KCONF(BidServerFanstopMultiGroupConfig, ad.fanstopServer, fanstopGroupExpConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, FansTopPvMinuteCalcProportion)  // 流量分布比例
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestUpdateIntervalMs, 180000)  // 粉条 LowestCost 更新间隔
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestStoreRemoteSize, 20)  // 粉条  LowestCost 更新窗口
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestUpdateBatchSize, 20)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansLowestCostEndingBoostTail, 0)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestUpdateIntervalMs, 120000)  // 内粉 lc 更新间隔
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestStoreRemoteSize, 20)  // 粉条  LowestCost 更新窗口
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestUpdateBatchSize, 100000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerLiveFansTopLowestUpdateIntervalMs, 30000)  // 内粉 lc 更新间隔
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerLiveFansTopLowestStoreRemoteSize, 10)  // 粉条 LC 更新窗口
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerLiveFansTopLowestUpdateBatchSize, 10000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestOcpmInitBidFen, 300)  // 粉条  LowestCost 涨粉初始出价
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestOcpmInitBidFen, 600)  // 粉条  LowestCost 初始出价
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansLiveLowestOcpmInitBidFen, 10)   // 直播 CPM LC 初始出价
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansLiveLowestOcpmInitBidFen, 10)   // 直播 CPM LC 初始出价
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCouponCost, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, GroupBudgetEnableDefault, true)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidLnWeight, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidLnWeight, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidRatioWeight, 0.2)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidRatioWeight, 0.05)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidParamP, 0.45)  // 粉条 LowestCost 调价参数 P
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidParamI, 0.0001)  // 粉条 LowestCost 调价参数 I
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidParamD, 0.15)  // 粉条 LowestCost 调价参数 D
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidParamP, 1.0)  // 粉条 LowestCost 调价参数 P
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidParamI, 0.0)  // 粉条 LowestCost 调价参数 I
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidParamD, 3.0)  // 粉条 LowestCost 调价参数 D
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidRatio, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidRatioMul, 0.2)
    DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidRatio, 0.75)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidRatioMul, 0.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FanstopHotMsgSkipRatio, 0.95)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFanstopHotMsgSkipRatio, 0.95)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, LowestCostOuterBidBound, 100000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, LowestCostOuterBidLowerBound, 1)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, LowestCostInnerBidBound, 100000)
  DEFINE_STRING_INT64_MAP_KCONF(ad.fanstopServer, FansTopLowestCostInitBidConfig);
  DEFINE_STRING_INT64_MAP_KCONF(ad.bidServer, EspLiveNobidUpperBound);
  DEFINE_STRING_INT64_MAP_KCONF(ad.bidServer, EspLiveNobidLowerBound);
  DEFINE_STRING_INT64_MAP_KCONF(ad.fanstopServer, InnerFansTopLowestCostInitBidConfig);
  DEFINE_STRING_INT64_MAP_KCONF(ad.fanstopServer, FansTopLowestCostMaxBidConfig);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopTraceLog, true);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, KakfaLogPidRatio, 0.1);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopCostCapLogInterval, 100)  // 粉条 Cost Cap Log
  // fanstop costcap
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopCostCapV2CostAchieveThreFen, 10000)  // 显示达成率消耗阈值
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, fansTopCostCapV2UnitTailSet)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, fansTopCostCapV3UnitTailSet)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, fansTopHighValueMcbUnitTailSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopLowestCostPayerSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopLowestCostOcpxPayerSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopLcPayerPassSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopLcPayerOcpxPassSet);
  DEFINE_PROTOBUF_NODE_KCONF(PhotoPageCaliExpMap, ad.bidServer2, nobidPageCaliExpMap);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, nobidPageAllocationByLevel);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, nobidPageAllocationBudgetLevel);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantFanstopCostcapExpConfig, ad.bidServer, merchantFanstopCostcapExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(FansTopCostCapPidConfig, ad.bidServer, fansTopCostCapPidConfig);
  DEFINE_PROTOBUF_NODE_KCONF(FansTopCostCapOcpcConfig, ad.bidServer, fansTopCostCapOcpcConfig);
  DEFINE_PROTOBUF_NODE_KCONF(FansTopOcpxActionType2ActionType, ad.bidServer,
                            fansTopOcpxActionType2ActionType);
  DEFINE_PROTOBUF_NODE_KCONF(InnerMerchantConvTimeDelayConfig, ad.bidServer,
                             MerchantConvTimeDelayConfig);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmLinearPacingGridConfigMap, ad.bidServer,
                             MerchantOcpmLinearPacingGridMap);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantStorewideConfig, ad.bidServer2,
                             MerchantStorewide);
  DEFINE_PROTOBUF_NODE_KCONF(kuaishou::ad::UpDimensionCaliExpConfMap,
                             ad.bidServer2, UpDimensionCaliExpConfMap);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopCostCapInitPacingRate, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopCostCapInitBidPacingRate, 1.0);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, currentShowUnitMaxDirty, 20);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
    fanstopCurrentShowTopic, "bid_server_fanstop_current_show");
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, hostingNobidFixRoasLowerBound, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, hostingNobidFixOrderUpperBound, 100000.0)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingNobidFixCampaignTail)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, nativeNobidLogInterval, 10)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativePvMinuteCalcProportion)  // 流量分布比例
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeLivePvMinuteCalcProportion)  // 专推直播流量分布比例
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeFlashPvMinuteCalcProportion)  // 速推直播流量分布比例
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nativeNobidDelaySeconds, 1800)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeLowestCostPidParamMap)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, nativeProportionNewWhiteList)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, nativeInitCpaBidDiscount, 0.8)
  DEFINE_STRING_INT64_MAP_KCONF(ad.bidServer, NativeLowestCostInitBidConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, nativeLowestCostPidResultCoef)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, nativeLowestCostStepRatio, 0.1)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeLowestCostDefaultLowerConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeLowestCostDefaultUpperConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, activityNobidUpperConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeOptBidAccountStepMap)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, merchantPriceRatioMap)
  // 丢弃 action_type
  // 直播配置
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantLiveDisableActionType)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantLiveExpDisableActionType)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, merchantLiveExpDisableActionTypeUnitTail)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantLiveHotDisableActionType)
  // 直播成本保护参竞实验行业白名单
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, innerOcpmMpcIndustryExp);
  // 联盟直播监控日志白名单
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, universeMerchantLiveMonitorLogWhiteConfig)

  // 短视频配置
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantRecoDisableActionType)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantRecoExpDisableActionType)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, merchantRecoExpDisableActionTypeUnitTail)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantRecoHotDisableActionType)

  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, stepInterfaceStragtegySet)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nativeNobidUpdateInterval, 10 * 1000 * 1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nativeNobidBudgetCheckInterval, 10 * 1000 * 1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, bidStepTwoInterval, 1 * 1000 * 1000)
  // 策略接口切换
  DEFINE_PROTOBUF_NODE_KCONF(StepConf, ad.bidServer, stepStrategyConf);
  // 无 diff 测试
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantNoDiffRedis, "adEngineFlowProcess");
  DEFINE_PROTOBUF_NODE_KCONF(ks::bid_server_merchant::kconf::MerchantNoDiffConfPB,
                             ad.bidServer, merchantNoDiffConf);
  DEFINE_TAILNUMBERV2_KCONF(adData.whiteBox, diffLogAccountTail, "");
  DEFINE_INT64_KCONF_NODE(ad.bidServer, noDiffAdLogKafkaStartTimestamp, -1)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, innerAggTail, "");

  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, roi7TargetCostRatioUpper, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableT7RoiMpcTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, enableFanstopV2SkipCostcapFixTail, "");
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, merchantAccFixKconf, false);  // 加速探索打折修复
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableNobidAccResetAccountTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, storewideAccHistory, "");
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableRedisKvDebugLog, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableReloadRedis, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, disableShard, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableCheckTTL, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableTTL, false);
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, cacheTTL, 60);
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, maxTTL, 360);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableRemoveExpire, false);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer2, merchantLiveCampaignTypes);
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, pcLiveUnitCostTimeGap, 600);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, targetCostProIncludeAccountMobile, false);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, targetCostProLiveAccountWhitelist);
  // [liuxianyi]
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, selfServiceAccountTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, PhotoOrderInitCpaAccountTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, BoundReversedTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, SkipReportEngineCampaignTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, SkipLlsidEq0CampaignTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, StoreWideSkipBidTypeCampaignTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, SkipClientCallbackAccountTail, "");
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, bidRatioUpperLine, 1.3);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, bidRatioLowerLine, 0.8);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, pastWindowTotalCostThr, 1000);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer2, NobidDTTimeCostThresh, 10);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, budgetCustomizedBoundMap)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer2, ratioDiffStepRatioMap)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableRlBid, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enablePidBound, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableLowBudget, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableInitBidOrder, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableWritePidBid, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableUnifyBound, false);
  DEFINE_PROTOBUF_NODE_KCONF(NobidExtendExpConfig, ad.bidServer2, nobidExtendExpConfig);
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, maxLowestCostContextLagLength, 10);
  // 套餐包
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, combolOrderAccountTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, budgetChangeOrderAccountTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, nobidAccResetAcc, "");
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableRemoveNatureGMV, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableCalTargetCost, false);
  // 短视频加速探索 2.0
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, aAccStashUnitID, 0);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmAccIncrementPhotoAccountIdSet)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, ocpmAccIncrementPhotoAccountIdTail, "");
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, PhotoAutoBidExploreGuardConfig)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableAccPhotoRoasIsApplyAdjust, false);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::KafkaConsumerConfPB, ad.bidServer, competeCampaignKafkaConsumerConf);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableMerchantCompeteCampaignMgr, false);
  // 参竞 campaign 最大有效时间 单位 秒
  DEFINE_INT32_KCONF_NODE(ad.bidServer, maxCompeteTimeThr, 300);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableNobidCompeteCampaignMgrUnitTail, "");
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableLOGCompeteCampaignMgr, false);
  // 出价监控是否增加 campaing_type 维度
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, addPerCampaignType, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableAddPerfBidInfo, false);
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, merchantKmlManagerThreadNum, 4)
  DEFINE_INT32_KCONF_NODE(ad.bidServer3, merchantKmlManagerHostingLiveMpcQueueSize, 2)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enableUseIndexRoiRatio, false);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, enableUseIndexRoiRatioAccountTail, "");
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enablePerfDetailPidCacheGetError, false);
  // Hosting Live MPC 配置
  DEFINE_PROTOBUF_NODE_KCONF(MerchantReinforcementLearningPredictModelConfig,
        ad.bidServer2, innerMpcModelConfig);
  // Lowest Cost RL 配置
  DEFINE_PROTOBUF_NODE_KCONF(MerchantReinforcementLearningPredictModelConfig,
        ad.bidServer2, InnerLoopLowestCostRlPredictConfig);
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, maxLowestCostRlFeatresLength, 200);
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, maxLowestCostRlTimeSteps, 4095);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantNobidDTDataConfig, ad.bidServer2, NobidDTDataConfig);
  DEFINE_PROTOBUF_NODE_KCONF(DarkAccExploreConfig, ad.bidServer2, tCDarkAccExploreConfig);
  DEFINE_PROTOBUF_NODE_KCONF(RoiPidAjustStrategy, ad.bidServer2, roiPidAjustStrategy);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantReinforcementLearningPredictModelConfig,
        ad.bidServer2, innerPreConvertModelConfig);
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, merchantInferRequestTimeout, 300);
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, merchantOcpmMsgPerfRate, 1000);
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, aigbRequestIntervalms, 300000);
  // PhotoROAS AIGB 配置
  DEFINE_PROTOBUF_NODE_KCONF(MerchantReinforcementLearningPredictModelConfig,
        ad.bidServer2, InnerLoopStorewideAIGBPredictConfig);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, closeFanstopLowestCostUnitTail, "");
  //热点信息旁路线程
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer2, transferMsgHelperAccountIds)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, transferMsgHelperMaxQueueLength, ********)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, transferMsgHelperEnableQueueLengthLimit, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, transferMsgHelperEnableDropMsg, true)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, photoROASGetRoiRatioFromAdLogTail, "");
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, MerchantByteSizeRate, 1000);
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, TransferQueueKeyIdNum, 10);
  DEFINE_INT32_KCONF_NODE(ad.bidServer2, autoTransferHotUnitAfterStartMinute, 10);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableAutoTransferHotUnit, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableResetMap, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enablePerfReset, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableResetLargeLog, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableNewResetLogic, false);
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer3, enablePerfNobidCasePriceCampaign);
  // 内循环 cid 顶价打折单独配置
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enableInnerCIDBillingSeperateDispatch, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enableAccountCIDBillingSeperate, false);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, AccountCIDBillingSeperateMap);
  // 净成交投放
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer3, authorPureGmvRatio);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer3, pureRoiPerf, "");
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer3, photoAuthorPureGmvRatio);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, merchant_optTriggerQPS, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, merchant_enableSkipFillTraceLog, false);
};

}  // namespace bid_server
}  // namespace ks
