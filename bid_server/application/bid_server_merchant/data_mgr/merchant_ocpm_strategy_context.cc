// Authors: <AUTHORS>
#include "teams/ad/bid_server/application/bid_server_merchant/data_mgr/merchant_ocpm_strategy_context.h"

#include <memory>
#include <algorithm>
#include "base/thread/thread_util.h"
#include "teams/ad/bid_server/application/bid_server_merchant/data_mgr/thread_common_data.h"
#include "teams/ad/bid_server/application/bid_server_merchant/data_mgr/merchant_data_mgr.h"
#include "teams/ad/bid_server/application/bid_server_merchant/kconf/kconf.h"
#include "teams/ad/bid_server/framework/utils/common_util.h"
#include "teams/ad/bid_server/framework/utils/json_to_stable_str.h"
#include "teams/ad/bid_server/application/bid_server_merchant/util/util.h"
#include "teams/ad/bid_server/base/strategy/lowest_cost/cost_cap_util.h"
#include "teams/ad/bid_server/framework/utils/spdm_switches.h"

DECLARE_bool(enable_bid_no_diff_switch);
namespace ks {
namespace bid_server {

MerchantOcpmStrategyContext::MerchantOcpmStrategyContext() {
  bid_context_store_ptr_ =
    std::make_shared<RedisKvPbStore<std::string, OcpmBidContext>>(
      "merchant_ocpm_bid_context");
  bid_context_store_ptr_->SetRedisBatchSender(MerchantDataMgr::Instance().GetRedisBatchSender());
  if (FLAGS_enable_bid_no_diff_switch) {
    bid_context_store_ptr_->DisableLoadRedis();
  }
  if (MerchantKconfUtil::enableReloadRedis()) {
    bid_context_store_ptr_->EnableReLoadRedis();
  }
  if (MerchantKconfUtil::enableRedisKvDebugLog()) {
    bid_context_store_ptr_->EnableDebugLog();
  }
}

std::shared_ptr<MerchantOcpmStrategyContext> MerchantDataMgr::GetMerchantOcpmThreadContext() {
  thread_local std::shared_ptr<MerchantOcpmStrategyContext> p_context;
  if (p_context == nullptr) {
    int tid = thread::GetThreadID();
    std::lock_guard<std::mutex> lck(ocpm_mtx_);
    if (ocpm_strategy_context_map_.find(tid) == ocpm_strategy_context_map_.end()) {
      LOG(INFO) << "build MerchantOcpmStrategyContext context for thread:" << tid;
      ocpm_strategy_context_map_[tid] = std::make_shared<MerchantOcpmStrategyContext>();
      ocpm_strategy_context_map_[tid]->Start();
    }
    p_context = ocpm_strategy_context_map_[tid];
  }
  return p_context;
}

OcpmBidContext* MerchantDataMgr::GetOcpmBidContext(const std::string& bid_context_key) {
  if (ocpm_bid_) {
    uint64_t key = std::hash<std::string>()(bid_context_key);
    str_hash_param_[key] = &bid_context_key;
    auto ret = ocpm_bid_->get(bid_context_key);
    str_hash_param_.erase(key);
    return ret;
  }
  return GetMerchantOcpmThreadContext()->GetOcpmBidContext(bid_context_key);
}

void MerchantDataMgr::RedisSave(const std::string& ctx_key, const OcpmBidContext& context, int64_t ttl) {
  if (FLAGS_is_test_env && AdKconfUtil::enableTestEnvOnlyShard0UpdateRedis() &&
    DeploymentUtil::CloudShardId() != 0) {
    // 测试环境 只有 0 号分片更新缓存
    return;
  }
  if (ocpm_bid_) {
    ocpm_bid_->AsyncToRedis(ctx_key);
    return;
  }
  GetMerchantOcpmThreadContext()->bid_context_store_ptr_->RedisSave(ctx_key, context, ttl);
}

BidDataLevel MerchantDataMgr::GetBidDataLevel(const OcpmBidContext* context_ptr) {
  if (!context_ptr) {
    return +BidDataLevel::UNIT_BID_DATA_LEVEL;
  }
  if (context_ptr->is_cbo()) {
    return +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL;
  } else {
    if (context_ptr->enable_p2l_single()) {
      return +BidDataLevel::UNIT_P2L_BID_DATA_LEVEL;
    }
  }
  return +BidDataLevel::UNIT_BID_DATA_LEVEL;
}

OcpmBidContext* MerchantOcpmStrategyContext::GetOcpmBidContext(const std::string& bid_context_key) {
  if (FLAGS_enable_bid_no_diff_switch) {
    auto iter = loaded_from_p2p.find(bid_context_key);
    if (iter != loaded_from_p2p.end() && iter->second) {
      return bid_context_store_ptr_->MutableValue(bid_context_key);
    }
    std::string value_str;
    BidServerBidDataMapP2P::GetInstance()->GetBidData(
            bid_context_store_ptr_->GenRedisKey(bid_context_key), &value_str);
    OcpmBidContext value;
    if (!value.ParseFromString(value_str)) {
      loaded_from_p2p[bid_context_key] = true;
      return nullptr;
    }
    bid_context_store_ptr_->InsertKeyValue(bid_context_key, value);
    loaded_from_p2p[bid_context_key] = true;
  }
  // 开启 check ttl
  if (ControllerDataMgr::Instance().EnableCheckTTL()) {
    if (ControllerDataMgr::Instance().IsCtxExpire(context_map_last_use_ts[bid_context_key])) {
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "cache_expire", "ocpm_ctx");
      if (ControllerDataMgr::Instance().EnableTTL()) {
        bid_context_store_ptr_->RemoveCache(bid_context_key);
        LOG(INFO) << "cache_expire, ocpm_ctx, key:" << bid_context_key;
      }
    } else {
      ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "cache_ok", "ocpm_ctx");
    }
  }
  // 控制频率
  // 开启淘汰
  if (ControllerDataMgr::Instance().EnableRemoveExpire()) {
    std::vector<std::string> remove_ids = MerchantDataMgr::Instance().GetRemoveIds(context_map_last_use_ts);
    MerchantDataMgr::Instance().RemoveIds(&context_map_last_use_ts, remove_ids);
    // 删除数据
    for (auto remove_id : remove_ids) {
      LOG(INFO) << "remove expire key:" << remove_id;
      bid_context_store_ptr_->RemoveCache(remove_id);
    }
  }
  context_map_last_use_ts[bid_context_key] = ControllerDataMgr::Instance().GetCtxTimeTs();
  return bid_context_store_ptr_->MutableValue(bid_context_key);
}

std::string MerchantDataMgr::GetUnitPriceRatioStr(const OcpmBidContext* context_ptr) {
  if (!context_ptr) {
    return "";
  }

  base::JsonObject unit_price_ratio;
  for (auto& kv : context_ptr->unit_price_ratio_info_map()) {
    if (kv.second.is_valid() && kv.second.price_ratio_valid()) {
      unit_price_ratio.set(absl::StrCat(kv.first), kv.second.price_ratio());
    }
  }

  return JsonToStableString(unit_price_ratio);
}

void MerchantDataMgr::SendMerchantOcpmResultToRemote(
  OcpmBidContext* context_ptr, const std::string& group_tag,
  const kuaishou::ad::BidServerMerchantTraceLog& c_trace_log, const std::string& dragon_type,
  BidStateInfoPtr bid_info_ptr) {
  BidDataLevel bid_data_level = GetBidDataLevel(context_ptr);
  uint64_t unit_id = context_ptr->unit_id();
  uint64_t campaign_id = context_ptr->campaign_id();
  const NativeLowestCostContext& lowest_cost_context = context_ptr->native_lowest_cost_context();
  uint64_t key_id = unit_id;
  if (bid_data_level == +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL) {
    key_id = campaign_id;
  }
  // get AdMultiPidCache
  auto pid_cache_ptr = ThreadCommonDataMgr::Instance()->GetAndInsertAdMultiPidCache(bid_data_level, key_id);
  if (!pid_cache_ptr) {
    std::string key_id_str = "";
    if (MerchantKconfUtil::enablePerfDetailPidCacheGetError()) {
      key_id_str = absl::StrCat(key_id);
    }
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "pid_cache_get_error",
                bid_data_level == +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL ? "campaign_level" : "unit_level",
                key_id_str);

    context_ptr->set_send_result_status(1);
    return;
  }
  auto multi_pid_cache_ptr = pid_cache_ptr->Get();

  multi_pid_cache_ptr->set_unit_id(unit_id);
  multi_pid_cache_ptr->set_campaign_id(context_ptr->campaign_id());
  if (FLAGS_enable_bid_no_diff_switch) {
    multi_pid_cache_ptr->mutable_pid_cache()->clear();
  }
  auto& pid_cache = *multi_pid_cache_ptr->mutable_pid_cache();
  if (pid_cache.find(group_tag) == pid_cache.end()) {
    InitMultiPidCache(multi_pid_cache_ptr, group_tag);
  }
  pid_cache[group_tag].set_roi_ratio(context_ptr->roi_ratio());
  pid_cache[group_tag].set_target_cpa(context_ptr->cpa_bid());
  pid_cache[group_tag].set_current_cpa(context_ptr->auto_cpa_bid());
  pid_cache[group_tag].set_auto_roi(context_ptr->auto_roi_ratio());
  pid_cache[group_tag].set_auto_bid_explore(context_ptr->auto_bid_explore());
  pid_cache[group_tag].set_adjust_rate_acc_increment(context_ptr->adjust_rate_acc_increment());
  pid_cache[group_tag].set_is_live_acc_increment(context_ptr->is_live_acc_increment());
  pid_cache[group_tag].set_id(key_id);
  pid_cache[group_tag].set_achieve_ratio(
    context_ptr->cost_ratio_adjust_context().auto_cost_ratio());
  if (context_ptr->softad_diff_ratio() > 0.0) {
    pid_cache[group_tag].set_achieve_ratio(context_ptr->softad_diff_ratio());
  }
  pid_cache[group_tag].set_r_calibration(
            context_ptr->conv_ratio_adjust_context().auto_conv_ratio());
  pid_cache[group_tag].set_ocpm_inner_strategy_tag(context_ptr->ocpm_inner_strategy_tag());
  pid_cache[group_tag].set_total_cost(context_ptr->cost());
  pid_cache[group_tag].set_today_target_cost(context_ptr->target_cost());
  pid_cache[group_tag].set_is_author_fans_cost(context_ptr->is_author_fans_cost());
  pid_cache[group_tag].set_is_author_fans_target_cost(context_ptr->is_author_fans_target_cost());
  pid_cache[group_tag].set_is_cold_unit_photo(context_ptr->is_cold_unit_photo());
  pid_cache[group_tag].set_auto_atv(context_ptr->auto_atv());
  pid_cache[group_tag].set_bid_coef(context_ptr->adjust_auto_value_rate());
  pid_cache[group_tag].set_is_inner_boost_bid(context_ptr->is_inner_boost_bid());
  bool is_campaing_acc_explore = IsStorewideAccFixExpV2(bid_info_ptr);
  bool is_storewide_switch =
      (!IsStorewideAccFixExp(bid_info_ptr) || is_campaing_acc_explore) &&
      (context_ptr->scene_oriented_type() == 21 || context_ptr->scene_oriented_type() == 30);
  if (is_storewide_switch && context_ptr->speed_type() != 5) {
    double storewide_auto_cpa = context_ptr->cpa_bid() * context_ptr->roi_ratio();
    if (context_ptr->auto_roi_ratio()> 0.0) {
      storewide_auto_cpa /= context_ptr->auto_roi_ratio();
    } else {
      storewide_auto_cpa = context_ptr->cpa_bid();  // 兜底策略
      perf::Count(1, "storewide_auto_roas_error", std::to_string(context_ptr->campaign_id()));
    }
    if (MerchantKconfUtil::enableFixQiandu() &&
        (context_ptr->ocpc_action_type() == "AD_LIVE_AUDIENCE" ||
        context_ptr->ocpc_action_type() == "AD_MERCHANT_FOLLOW")) {
      storewide_auto_cpa = context_ptr->auto_cpa_bid();
    }
    double new_storewide_auto_cpa = storewide_auto_cpa;
    pid_cache[group_tag].set_current_cpa(new_storewide_auto_cpa);
    if (MerchantKconfUtil::enableStorewideMonitor()) {
      LOG_EVERY_N(INFO, 10000) << "debug storewide event order paied."
                            << ", author id: " << context_ptr->author_id()
                            << ", campaign_id: " << context_ptr->campaign_id()
                            << ", unit_id: " << context_ptr->unit_id()
                            << ", roi_ratio: " << context_ptr->roi_ratio()
                            << ", group_tag: " << group_tag
                            << ", storewide_auto_cpa: " << storewide_auto_cpa
                            << ", cpa_bid: " << context_ptr->cpa_bid()
                            << ", auto_roi_ratio: " << context_ptr->auto_roi_ratio()
                            << ", ocpx: " << context_ptr->ocpc_action_type();
    }
  }
  if (context_ptr->scene_oriented_type() == 21 &&
    context_ptr->ocpc_action_type() == "AD_LIVE_AUDIENCE" && bid_info_ptr && bid_info_ptr->explore_put_type == 2) {  // NOLINT
    const auto storewide_qiandu_ratio_map = MerchantKconfUtil::storewideQianduRatioMap();
    double ratio = 1.0;
    if (storewide_qiandu_ratio_map &&
        storewide_qiandu_ratio_map->find(0) != storewide_qiandu_ratio_map->end()) {
      ratio = storewide_qiandu_ratio_map->find(0)->second;
    }
    if (storewide_qiandu_ratio_map &&
        storewide_qiandu_ratio_map->find(context_ptr->author_id()) != storewide_qiandu_ratio_map->end()) {
      ratio = storewide_qiandu_ratio_map->find(context_ptr->author_id())->second;
    }
    perf::Interval(ratio, "storewide_qiandu_ratio", std::to_string(context_ptr->unit_id() % 1000));
    pid_cache[group_tag].set_current_cpa(context_ptr->auto_cpa_bid() * ratio);
  }
  // 对全站加速探索 2.0 内容型客户提价
  if (context_ptr->scene_oriented_type() == 21 &&
    context_ptr->ocpc_action_type() != "AD_LIVE_AUDIENCE" && bid_info_ptr && bid_info_ptr->explore_put_type == 2  // NOLINT
    && bid_info_ptr->storewide_gmv_ratio > 0.01 && bid_info_ptr->storewide_gmv_ratio < 0.99) {
    double boost_ratio = 1.0 / (1 - bid_info_ptr->storewide_gmv_ratio);
    boost_ratio = std::min(boost_ratio, 5.0);
    boost_ratio = std::max(boost_ratio, 1.0);
    if (bid_info_ptr->ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS) {
      pid_cache[group_tag].set_auto_bid_explore(context_ptr->auto_bid_explore() / boost_ratio);
    } else {
      pid_cache[group_tag].set_auto_bid_explore(context_ptr->auto_bid_explore() * boost_ratio);
    }
    pid_cache[group_tag].set_adjust_rate_acc_increment(context_ptr->adjust_rate_acc_increment() * boost_ratio);  // NOLINT
  }
  // native_lowest_cost_context for costcap
  if (MerchantKconfUtil::enableCostCapSendTraceLog()) {
    auto& lc_context = *pid_cache[group_tag].mutable_native_lowest_cost_context();
    lc_context.set_current_budget(lowest_cost_context.current_budget());
    lc_context.set_past_pv_ratio(lowest_cost_context.past_pv_ratio());
    lc_context.set_past_cost_ratio(lowest_cost_context.past_cost_ratio());
    lc_context.set_is_explore_msg(lowest_cost_context.is_explore_msg());
    lc_context.set_is_campaign_bid(lowest_cost_context.is_campaign_bid());
    lc_context.set_first_log_timestamp(lowest_cost_context.first_log_timestamp());
    lc_context.set_schedule_time_type(lowest_cost_context.schedule_time_type());
    lc_context.set_is_same_date(lowest_cost_context.is_same_date());
    lc_context.set_total_cv(lowest_cost_context.total_cv());
    lc_context.set_target_cpa(lowest_cost_context.target_cpa());
    lc_context.set_expected_cost(lowest_cost_context.expected_cost());
    lc_context.set_is_init(lowest_cost_context.is_init());
    lc_context.set_allocated_budget_unify(lowest_cost_context.allocated_budget_unify());
    lc_context.set_is_predict_live_duration(lowest_cost_context.is_predict_live_duration());
    lc_context.set_unit_start_time(lowest_cost_context.unit_start_time());
    lc_context.set_unit_end_time(lowest_cost_context.unit_end_time());
    lc_context.set_is_haitou(lowest_cost_context.is_haitou());
    lc_context.set_second_industry_name(lowest_cost_context.second_industry_name());
    lc_context.set_is_ctcvr_high(lowest_cost_context.is_ctcvr_high());
    lc_context.set_upper_bound(lowest_cost_context.upper_bound());
    lc_context.set_lower_bound(lowest_cost_context.lower_bound());
    if (MerchantKconfUtil::enableAllPvTraceLogV2()) {
      lc_context.clear_unit_hour_schedule();
      lc_context.clear_pv_proportion();
      for (auto iter = lowest_cost_context.unit_hour_schedule().begin();
           iter != lowest_cost_context.unit_hour_schedule().end(); ++iter) {
        lc_context.add_unit_hour_schedule(*iter);
      }
      for (auto iter = lowest_cost_context.pv_proportion().begin();
           iter != lowest_cost_context.pv_proportion().end(); ++iter) {
        lc_context.add_pv_proportion(*iter);
      }
    }
  }
  // 单元成本率
  float cpa_ratio = context_ptr->cost() / (context_ptr->target_cost() + 1e-3);
  pid_cache[group_tag].set_cpa_ratio(cpa_ratio);
  pid_cache[group_tag].set_price_ratio(context_ptr->price_ratio());
  multi_pid_cache_ptr->set_ts(TimeUtil::Instance().GetTimestamp());
  // 分页面成本信息
  pid_cache[group_tag].set_page_achieve_info(context_ptr->page_achieve_info());
  // 计划层级调价下单元计费信息
  pid_cache[group_tag].set_unit_price_ratio(GetUnitPriceRatioStr(context_ptr));
  kuaishou::ad::AdEnum::CampaignType campaign_type;
  kuaishou::ad::AdEnum::CampaignType_Parse(context_ptr->campaign_type(), &campaign_type);
  // cbo 实验组发送 unit 维度调价
  if (lowest_cost_context.is_cbo_unit_data()) {
    const auto& unit_map = lowest_cost_context.unit_map();
    for (auto iter = unit_map.begin(); iter != unit_map.end(); ++iter) {
      if (iter->second.auto_bid() <= 0.01) {
        if (MerchantKconfUtil::enableLiveHostingMonitor()) {
          LOG_EVERY_N(INFO, 100) << "debug live_hosting auto_bid_is_zero"
                            << ", author id: " << context_ptr->author_id()
                            << ", campaign_id: " << context_ptr->campaign_id()
                            << ", unit_id: " << context_ptr->unit_id()
                            << ", roi_ratio: " << context_ptr->roi_ratio()
                            << ", auto_bid: " << iter->second.auto_bid()
                            << ", cpa_bid: " << context_ptr->cpa_bid()
                            << ", auto_roi_ratio: " << context_ptr->auto_roi_ratio()
                            << ", ocpx: " << context_ptr->ocpc_action_type();
        }
        continue;
      }
      // 修改 unit 维度出价
      bid_data_level = +BidDataLevel::UNIT_BID_DATA_LEVEL;
      key_id = iter->first;
      kuaishou::ad::AdActionType ocpx_action_type = iter->second.ocpx_action_type();
      auto unit_pid_cache_ptr =
          ThreadCommonDataMgr::Instance()->GetAndInsertAdMultiPidCache(bid_data_level, key_id);
      if (!unit_pid_cache_ptr) {
        std::string key_id_str = "";
        if (MerchantKconfUtil::enablePerfDetailPidCacheGetError()) {
          key_id_str = absl::StrCat(key_id);
        }
        ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "unit_pid_cache_get_error",
                    "unit_level", key_id_str);
        continue;
      }
      auto unit_multi_pid_cache_ptr = unit_pid_cache_ptr->Get();
      auto& unit_pid_cache = *unit_multi_pid_cache_ptr->mutable_pid_cache();
      if (unit_pid_cache.find(group_tag) == unit_pid_cache.end()) {
        InitMultiPidCache(unit_multi_pid_cache_ptr, group_tag);
      }
      unit_multi_pid_cache_ptr->set_unit_id(iter->first);
      unit_multi_pid_cache_ptr->set_campaign_id(campaign_id);
      unit_pid_cache[group_tag].set_id(iter->first);
      if (ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
          ocpx_action_type == kuaishou::ad::AD_MERCHANT_T7_ROI) {
        unit_pid_cache[group_tag].set_auto_roi(iter->second.auto_bid());
      } else {
        unit_pid_cache[group_tag].set_current_cpa(iter->second.auto_bid());
      }
      unit_pid_cache[group_tag].set_price_ratio(iter->second.price_ratio());
      unit_pid_cache[group_tag].set_page_achieve_info(context_ptr->page_achieve_info());
      if (MerchantKconfUtil::enableBidTracelogLiveHosting()) {
        unit_pid_cache[group_tag].set_total_cost(context_ptr->cost());
        unit_pid_cache[group_tag].set_today_target_cost(context_ptr->target_cost());
      }
      // store and send to kafka
      ThreadCommonDataMgr::Instance()->SendAdMultiPidCache(
          bid_data_level, key_id, ocpx_action_type, campaign_type, *unit_multi_pid_cache_ptr, c_trace_log,
          kMerchantOcpmStrategy, group_tag, bid_info_ptr);
    }
  } else if ("AD_STOREWIDE_ROAS" == context_ptr->ocpc_action_type() &&
      context_ptr->campaign_type() == "LIVE_STREAM_PROMOTE" &&
      context_ptr->speed_type() != 5 &&
      (MerchantKconfUtil::campaign2UnitTailId()->count(context_ptr->account_id() % 100) ||
      MerchantKconfUtil::campaign2UnitTailId()->count(context_ptr->account_id()))) {
      bid_data_level = +BidDataLevel::UNIT_BID_DATA_LEVEL;
      kuaishou::ad::AdActionType ocpx_action_type;
      kuaishou::ad::AdActionType_Parse(context_ptr->ocpc_action_type(), &ocpx_action_type);
      const auto& campaign_units = context_ptr->campaign_units();
      for (auto iter = campaign_units.begin(); iter != campaign_units.end(); ++iter) {
        pid_cache[group_tag].set_id(iter->first);  // 这会修改 multi_pid_cache_ptr 里的内容
        pid_cache[group_tag].set_campaign_id(context_ptr->campaign_id());
        pid_cache[group_tag].set_account_id(context_ptr->account_id());
        multi_pid_cache_ptr->set_unit_id(iter->first);
        ThreadCommonDataMgr::Instance()->SendAdMultiPidCache(bid_data_level, key_id, ocpx_action_type,
          campaign_type, *multi_pid_cache_ptr, c_trace_log,
          kMerchantOcpmStrategy, group_tag, bid_info_ptr);
      }
    } else {
    kuaishou::ad::AdActionType ocpx_action_type;
    kuaishou::ad::AdActionType_Parse(context_ptr->ocpc_action_type(), &ocpx_action_type);
    // store and send to kafka
    ThreadCommonDataMgr::Instance()->SendAdMultiPidCache(bid_data_level, key_id, ocpx_action_type,
      campaign_type, *multi_pid_cache_ptr, c_trace_log,
      kMerchantOcpmStrategy, group_tag, bid_info_ptr);
  }
}

}  // namespace bid_server
}  // namespace ks
