#include "teams/ad/bid_server/application/bid_server_merchant/util/monitor_helper.h"
#include "teams/ad/bid_server/application/bid_server_merchant/kconf/kconf.h"
namespace ks {
namespace bid_server {

using kuaishou::ad::AdActionType;

static void TranslateCallbackType(
    std::string* log_action_type_str, const std::string& log_js_action_type_str) {
  // callback 上报事件和原来的 ad_log_decoded 是有差别的，有些字段不一致，这里要翻译一下
  // 参照 http://git.corp.kuaishou.com/ks-ad/kuaishou-ad-datacenter/blob/master/
  // datacenter-biz/src/main/java/com/kuaishou/ad/datacenter/biz/algo/consumer/AdCallBackLogConsumer.java
  if (*log_action_type_str == "EVENT_JS" && log_js_action_type_str == "JS_CLICK3") {
    *log_action_type_str = "AD_LANDING_PAGE_FORM_SUBMITTED";
  } else if (*log_action_type_str == "EVENT_CONVERSION") {
    *log_action_type_str = "AD_CONVERSION";
  } else if (*log_action_type_str == "EVENT_PAY") {
    *log_action_type_str = "AD_PURCHASE";
  } else if (*log_action_type_str == "EVENT_CREDIT_GRANT") {
    *log_action_type_str = "AD_CREDIT_GRANT";
  }
}

kuaishou::ad::AdSourceType MonitorHelper::GetAdSourceType(const kuaishou::ad::dw::AdLogForAlgo &ad_log) {
  kuaishou::ad::AdSourceType source_type = kuaishou::ad::AdSourceType::UNKNOWN_SOURCE_TYPE;
  kuaishou::ad::AdSourceType_Parse(ad_log.source_type(), &source_type);
  return source_type;
}

/// 转换 action type
kuaishou::ad::AdActionType MonitorHelper::GetAdjustedActionType(
    const kuaishou::ad::dw::AdLogForAlgo &ad_log) {
  std::string log_action_type_str = ad_log.action_type();
  const std::string& log_js_action_type_str = ad_log.callback_js_event_type();
  TranslateCallbackType(&log_action_type_str, log_js_action_type_str);

  kuaishou::ad::AdActionType log_action_type = kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
  kuaishou::ad::AdActionType_Parse(log_action_type_str, &log_action_type);
  return log_action_type;
}
/// 转换计数
int MonitorHelper::GetActionCount(const kuaishou::ad::dw::AdLogForAlgo &ad_log) {
    kuaishou::ad::AdSourceType source_type = GetAdSourceType(ad_log);

  kuaishou::ad::AdActionType ocpc_action_type = kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
  kuaishou::ad::AdActionType_Parse(ad_log.ocpc_action_type(), &ocpc_action_type);
  int action_count = 0;
  // 判断是否是 DPA
  if (!ad_log.is_for_report_engine()) return action_count;
  uint64_t delivery_time =
      (ad_log.delivery_timestamp() + 28800000) / 86400000;
  uint64_t log_process_time =
      (ad_log.log_process_timestamp() + 28800000) / 86400000;
  if (source_type == kuaishou::ad::AdSourceType::ADX) {
    kuaishou::ad::AdActionType log_action_type = GetAdjustedActionType(ad_log);
    if (ocpc_action_type == kuaishou::ad::AD_ITEM_CLICK
        && (log_action_type == kuaishou::ad::AD_ITEM_CLICK
            || log_action_type == kuaishou::ad::AD_LANDING_PAGE_CLICK
            || log_action_type == kuaishou::ad::AD_NEW_DETAIL_PAGE_CLICK)) {
      action_count = 1;
    }
  } else {
    action_count = GetDspActionCount(ad_log, ocpc_action_type);
  }
  return action_count;
}

int MonitorHelper::GetDspActionCount(const kuaishou::ad::dw::AdLogForAlgo &ad_log,
    kuaishou::ad::AdActionType action_type) {
  int action_count = 0;
  if (!ad_log.is_for_report_engine()) return action_count;
  kuaishou::ad::AdActionType log_action_type = GetAdjustedActionType(ad_log);
  bool is_conversion_7d = ad_log.conversion_time() > 0
          && (ad_log.log_process_timestamp() - ad_log.conversion_time() < 86400 * 7 * 1000);
  switch (action_type) {
    case kuaishou::ad::AD_ITEM_CLICK:
      action_count = ad_log.e_ad_item_click();
      break;
    case kuaishou::ad::AD_CONVERSION:
      action_count = ad_log.e_event_conversion();
      break;
    case kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED:
      action_count = ad_log.e_event_form_submit();
      break;
    case kuaishou::ad::AD_APPROXIMATE_PURCHASE:
      action_count = ad_log.e_ad_item_approx_pay();
      break;
    case kuaishou::ad::AD_PURCHASE:
      action_count = ad_log.e_event_new_user_pay();
      break;
    case kuaishou::ad::AD_LIVE_AUDIENCE:
      action_count = ad_log.is_conversion() == true ? 1 : 0;
      break;
    case kuaishou::ad::EVENT_ORDER_PAIED:
      if (ad_log.campaign_type() == "MERCHANT_RECO_PROMOTE") {
        action_count = (log_action_type == action_type) ? 1 : 0;
      } else {
        action_count = log_action_type == action_type ? 1 : 0;
      }
      break;
    case kuaishou::ad::EVENT_7_DAY_PAY_TIMES:
      if (is_conversion_7d && log_action_type == kuaishou::ad::EVENT_PAY) {
        action_count = 1;
      } else {
        action_count = 0;
      }
      break;
    default:
      action_count = (log_action_type == action_type);
      break;
  }
  return action_count;
}

/// 计费金额
int MonitorHelper::GetPrice(const kuaishou::ad::dw::AdLogForAlgo &ad_log) {
  AdActionType charge_action_type = kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
  kuaishou::ad::AdActionType_Parse(ad_log.charge_action_type(), &charge_action_type);

  AdActionType log_action_type = GetAdjustedActionType(ad_log);
  if (charge_action_type == log_action_type) {
    if (ad_log.aggr_merchant_ocpm_msg().is_aggr_merchant_ocpm_msg()) {
      return ad_log.aggr_merchant_ocpm_msg().price_sum();
    }
    if (ad_log.ad_log_aggr_message_value().is_aggr_msg()) {
      return ad_log.ad_log_aggr_message_value().price_sum();
    }
    return ad_log.price();
  }
  return 0;
}

/// 计费金额 (打折前)
int MonitorHelper::GetPriceOrigin(const kuaishou::ad::dw::AdLogForAlgo &ad_log) {
  AdActionType charge_action_type = kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
  kuaishou::ad::AdActionType_Parse(ad_log.charge_action_type(), &charge_action_type);

  AdActionType log_action_type = GetAdjustedActionType(ad_log);
  if (charge_action_type == log_action_type) {
    return ad_log.record_gsp_price();
  }
  return 0;
}

/// 打折后的计费金额，非实际计费金额 混排服务可能会修改
int MonitorHelper::GetSeparatePrice(const kuaishou::ad::dw::AdLogForAlgo &ad_log) {
  AdActionType charge_action_type = kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
  kuaishou::ad::AdActionType_Parse(ad_log.charge_action_type(), &charge_action_type);
  AdActionType log_action_type = GetAdjustedActionType(ad_log);
  if (charge_action_type == log_action_type) {
    return ad_log.separate_gsp_price();
  }
  return 0;
}

uint64_t MonitorHelper::GetCost(const kuaishou::ad::dw::AdLogForAlgo& log) {
  if (IsFanstop(log)) {
    if (log.aggr_merchant_ocpm_msg().is_aggr_merchant_ocpm_msg()) {
      return log.aggr_merchant_ocpm_msg().cost_total_sum();
    }
    if (log.ad_log_aggr_message_value().is_aggr_msg()) {
      return log.ad_log_aggr_message_value().cost_total_sum();
    }
    return log.cost_total();
  }
  return GetPrice(log);
}

double MonitorHelper::GetTargetCost(const kuaishou::ad::dw::AdLogForAlgo& log) {
  bool is_conv = GetIsConv(log);
  double target_cost = 0;
  bool is_storewide_switch = (log.scene_oriented_type() == 21 || log.scene_oriented_type() == 30);
  if (is_conv) {
    if (IsFanstop(log)
        && (log.ocpc_action_type() == "AD_FANS_TOP_ROI"
            || log.ocpc_action_type() == "AD_MERCHANT_ROAS")) {
      if (log.cpa_bid() > 0) {
        double gmv = GetGmv(log);
        // 移动端 roas 类型 roi_ratio 填在 cpa_bid 字段中， 放大 1000 倍
        target_cost = gmv / log.cpa_bid() * 1000;
      }
    } else if (log.ocpc_action_type() == "AD_MERCHANT_ROAS" || is_storewide_switch ||
        log.ocpc_action_type() == "AD_STOREWIDE_ROAS") {
      if (log.roi_ratio() > 0) {
        double gmv = GetGmv(log);
        target_cost = gmv / log.roi_ratio();
      }
    } else {
      if (log.aggr_merchant_ocpm_msg().is_aggr_merchant_ocpm_msg()) {
        target_cost = log.cpa_bid() * log.aggr_merchant_ocpm_msg().msg_cnt();
      } else {
      target_cost = log.cpa_bid();
      }
    }
  }
  return target_cost;
}

double MonitorHelper::GetOriginGmv(const kuaishou::ad::dw::AdLogForAlgo& log) {
  bool is_storewide_switch = (log.scene_oriented_type() == 21 || log.scene_oriented_type() == 30);
  double gmv = log.callback_purchase_amount() * 1000;
  if (log.campaign_type() == "LIVE_STREAM_PROMOTE") {
    if (log.ocpc_action_type() == "EVENT_ORDER_PAIED") {
      if (log.action_type() == "EVENT_ORDER_PAIED") {
        gmv = log.callback_purchase_amount() * 1000;
      } else {
        gmv = 0.0;
      }
    } else if (log.ocpc_action_type() == "AD_MERCHANT_ROAS" ||
        log.ocpc_action_type() == "AD_STOREWIDE_ROAS" || is_storewide_switch) {
      if (log.action_type() == "EVENT_ORDER_PAIED") {
        gmv = log.callback_purchase_amount() * 1000;
      } else {
        gmv = 0.0;
      }
    }
  } else if (log.campaign_type() == "MERCHANT_RECO_PROMOTE") {
    if (log.ocpc_action_type() == "EVENT_ORDER_PAIED") {
      if (log.action_type() == "EVENT_ORDER_PAIED") {
        gmv = log.callback_purchase_amount() * 1000;
      } else {
        gmv = 0.0;
      }
    } else if (log.ocpc_action_type() == "AD_MERCHANT_ROAS" ||
        log.ocpc_action_type() == "AD_STOREWIDE_ROAS") {
      if (log.action_type() == "EVENT_ORDER_PAIED") {
        gmv = log.callback_purchase_amount() * 1000;
      } else {
        gmv = 0.0;
      }
    }
  }
  return gmv;
}


double MonitorHelper::GetGmv(const kuaishou::ad::dw::AdLogForAlgo& log) {
  bool is_conv = GetIsConv(log);
  auto callback_purchase_amount = log.callback_purchase_amount();
  if (log.aggr_merchant_ocpm_msg().is_aggr_merchant_ocpm_msg()) {
    callback_purchase_amount = log.aggr_merchant_photo_roas_msg().callback_purchase_amount_sum();
  }
  double gmv = callback_purchase_amount * 1000;
  if (log.ocpc_action_type() == "EVENT_ORDER_PAIED" ||
      log.ocpc_action_type() == "AD_MERCHANT_ROAS" ||
      log.ocpc_action_type() == "AD_STOREWIDE_ROAS" ||
      log.ocpc_action_type() == "AD_FANS_TOP_ROI") {
    if (is_conv) {
      gmv = callback_purchase_amount * 1000;
    } else {
      gmv = 0.0;
    }
  } else {
    if (log.action_type() == "EVENT_ORDER_PAIED") {
      gmv = callback_purchase_amount * 1000;
    } else {
      gmv = 0.0;
    }
  }
  return gmv;
}

bool MonitorHelper::IsFanstop(const kuaishou::ad::dw::AdLogForAlgo& log) {
  if (log.campaign_type() == "AD_FANSTOP_TO_FANS"
      || log.campaign_type() == "AD_FANSTOP_TO_SHOW"
      || log.campaign_type() == "AD_FANSTOP_TO_ALL"
      || log.campaign_type() == "AD_FANSTOP_LIVE_TO_FANS"
      || log.campaign_type() == "AD_FANSTOP_LIVE_TO_SHOW"
      || log.campaign_type() == "AD_FANSTOP_LIVE_TO_ALL") {
    return true;
  }
  return false;
}

bool MonitorHelper::GetIsConv(const kuaishou::ad::dw::AdLogForAlgo& log) {
  bool is_conv = log.is_conversion();
  bool is_storewide_switch = (log.scene_oriented_type() == 21 || log.scene_oriented_type() == 30);
  if (log.campaign_type() == "LIVE_STREAM_PROMOTE") {
    if (log.ocpc_action_type() == "AD_MERCHANT_ROAS" ||
        log.ocpc_action_type() == "AD_STOREWIDE_ROAS" || is_storewide_switch) {
      if (log.is_conversion() && log.callback_purchase_amount() > 0) {
        is_conv = true;
      } else {
        is_conv = false;
      }
    } else {
      is_conv = log.is_conversion();
    }
  } else if (IsFanstop(log)) {
    if (log.ocpc_action_type() == "AD_MERCHANT_ROAS" ||
        log.ocpc_action_type() == "AD_FANS_TOP_ROI" ||
        log.ocpc_action_type() == "AD_STOREWIDE_ROAS") {
      if (log.is_conversion() && log.callback_purchase_amount() > 0) {
        is_conv = true;
      } else {
        is_conv = false;
      }
    } else {
      is_conv = log.is_conversion();
    }
  } else if (log.campaign_type() == "MERCHANT_RECO_PROMOTE") {
    if (log.ocpc_action_type() == "EVENT_ORDER_PAIED") {
      if (log.is_conversion() &&
          log.action_type() == "EVENT_ORDER_PAIED") {
        is_conv = true;
      } else {
        is_conv = false;
      }
    } else if (log.ocpc_action_type() == "AD_MERCHANT_ROAS" ||
        log.ocpc_action_type() == "AD_STOREWIDE_ROAS") {
      if (log.is_conversion() &&
          log.action_type() == "EVENT_ORDER_PAIED") {
        is_conv = true;
      } else {
        is_conv = false;
      }
    } else {
      is_conv = (log.is_conversion() || log.ocpc_action_type() == log.action_type());
    }
  }
  return is_conv;
}

}  // namespace bid_server
}  // namespace ks
