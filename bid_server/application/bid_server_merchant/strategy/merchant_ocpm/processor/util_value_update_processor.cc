// Authors: <AUTHORS>
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/util_value_update_processor.h"
#include <algorithm>
#include <cmath>
#include <string>
#include <tuple>
#include <vector>
#include <map>
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/lowest_cost/common_util.h"
#include "teams/ad/bid_server/base/kconf/kconf.h"
#include "teams/ad/bid_server/base/strategy/lowest_cost/cost_cap_util.h"
#include "teams/ad/bid_server/application/bid_server_merchant/kconf/kconf.h"
#include "teams/ad/bid_server/framework/utils/time.h"
#include "teams/ad/bid_server/framework/index/default/bid_state_info_ad_table_helper.h"
#include "teams/ad/bid_server/framework/utils/kconf.h"

DECLARE_bool(enable_bid_no_diff_switch);
namespace ks {
namespace bid_server {

void UtilValueUpdateProcessor::Process(StrategySession* p_session) {
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  LOG_EVERY_N(INFO, 100000) << "UtilValueUpdateProcessor::Process: unit_id: " << msg.unit_id;
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;
  if (p_util_vals->is_debug) {LOG(WARNING)
    << "UtilValueUpdateProcessor::Process:tid:" << p_util_vals->thread_id;}
  SetLinearPacingExpConfig(msg, strategy_config, exp_config, bid_info_ptr, p_bid_context, p_util_vals);
  SetAdShortType(p_bid_context, p_util_vals);
  // pacing method
  SetPacingMethod(msg, p_util_vals);
  // set flow control status
  SetFlowControlStatus(bid_info_ptr, p_bid_context, p_util_vals, exp_config);
  // set pid
  SetPidParams(p_session);
  // costcap
  SetCostCap(strategy_config, bid_info_ptr, p_bid_context, p_util_vals, msg);
  // 全站暗投 nobid
  SetStoreWideNobid(strategy_config, bid_info_ptr, p_bid_context, p_util_vals, msg);
  // hard bound
  SetHardBound(msg, strategy_config, bid_info_ptr, exp_config, p_bid_context, p_util_vals);
  if (SetAccExplore(strategy_config, bid_info_ptr, p_bid_context, p_util_vals)) {
    // acc_explore
  } else if (SetColdStart(msg, strategy_config, bid_info_ptr, exp_config, p_bid_context, p_util_vals)) {
    // cold start
  }
  // 加速探索重构增量消息仍然走冷启，保持状态一致
  if (p_util_vals->is_acc_increment) {
    SetColdStart(msg, strategy_config, bid_info_ptr, exp_config, p_bid_context, p_util_vals);
  }

  SetBudgetBound(p_session);
  SetLiveHosting(p_session);

  //  is_update_adjust
  bool is_update = IsUpdate(bid_info_ptr, msg, p_bid_context, strategy_config, exp_config, p_util_vals);
  bool is_apply_adjust = IsApplyAdjust(strategy_config, p_bid_context, p_util_vals, exp_config, msg);
  if (FLAGS_enable_bid_no_diff_switch || p_util_vals->is_debug) {
    LOG(INFO) << "no diff debug is update, " << is_update << " is_apply_adjust, " << is_apply_adjust
      << " msg seq, " << msg.event_server_timestamp << " unit id, " << msg.unit_id;
  }
  p_util_vals->is_ad_open = is_update;
  p_util_vals->is_apply_adjust = is_apply_adjust;
  p_util_vals->is_update_adjust = is_update && is_apply_adjust;
  SetAdStatusTag(p_bid_context, p_util_vals);

  AdjustRateBound(exp_config, p_util_vals, p_bid_context, msg);
  if (!strategy_config.enable_conv_update() && msg.msg_type == MerchantMsgType::kConvMsg) {
    p_util_vals->is_update_adjust = false;
  }

  if (p_util_vals->is_debug) {
    LOG(WARNING)
    << "Debug:tid:" << p_util_vals->thread_id
    << " is_update: " << is_update
    << " is_apply_adjust: " << is_apply_adjust
    << " is_update_adjust: " << p_util_vals->is_update_adjust;
  }

  if (msg.IsActivate() && !p_util_vals->is_update_adjust) {
    p_util_vals->is_monitor = false;
  }

  // 联盟监控日志
  UniverseMonitorLog(p_session);
}

void UtilValueUpdateProcessor::SetAdStatusTag(OcpmBidContext* p_bid_context,
  ProcessUtilValues* p_util_vals) {
    /*
    ad_status_tag = 0; unit_id 一直开启
    ad_status_tag = 1; unit_id 一直关停
    ad_status_tag = 2; unit_id 由开启到关停
    ad_status_tag = 3; unit_id 由关停到重新开启
  */
  const bool last_ad_valid = p_bid_context->last_ad_valid();
  const uint64 last_ad_status_tag = p_bid_context->ad_status_tag();
  uint64 now_ad_status_tag = 0;
  if (last_ad_valid == true && p_util_vals->is_ad_open == true) {
    // 一直开启 : 上次开启 这次也开启
    p_util_vals->ad_status_tag = 0;
    now_ad_status_tag = 0;
  } else if (last_ad_valid == false && p_util_vals->is_ad_open == false) {
    // 一直关停 ：上次关停， 这次也关停
    p_util_vals->ad_status_tag = 1;
    now_ad_status_tag = 1;
  } else if (last_ad_valid == true && p_util_vals->is_ad_open == false) {
    // 由开启到关停 ： 上次开启 这次关停
    p_util_vals->ad_status_tag = 2;
    now_ad_status_tag = 2;
  } else if (last_ad_valid == false && p_util_vals->is_ad_open == true) {
    // 由关停到重新开启 ： 上次关停 这次开启
    p_util_vals->ad_status_tag = 3;
    now_ad_status_tag = 3;
  }
  // 修改价格 ： 打标记 4
  if (p_util_vals->is_target_modify && p_util_vals->is_ad_open == true) {
    p_util_vals->ad_status_tag = 4;
    now_ad_status_tag = 4;
  }
  if (FLAGS_enable_bid_no_diff_switch || p_util_vals->is_debug) {
    LOG(WARNING) << "no diff debug last_ad_valid, " << last_ad_valid
      << " util_vals_is_ad_open, " << p_util_vals->is_ad_open
      << " util_vals_is_target_modify, " << p_util_vals->is_target_modify
      << " util_vals_ad_status_tag, " << p_util_vals->ad_status_tag
      << " now_ad_status_tag, " << now_ad_status_tag
      << " msg seq, " << TimeUtil::Instance().GetTimestamp()
      << " unit id, " << p_bid_context->unit_id();
  }
  p_bid_context->set_last_ad_valid(p_util_vals->is_ad_open);
  p_bid_context->set_ad_status_tag(now_ad_status_tag);
  if (last_ad_status_tag != now_ad_status_tag) {
    p_util_vals->ad_status_tag_change = true;
  }
  if (p_util_vals->is_ad_open) {
    int64 now_time_min = TimeUtil::Instance().GetTimestamp() / 1000000 / 60;
    int64 last_ad_open_min = p_bid_context->last_ad_open_timestamp() / 1000000 / 60;
    if (now_time_min != last_ad_open_min) {
      p_bid_context->set_ad_open_count(p_bid_context->ad_open_count() + 1);  // 记录投流的分钟数
    }
    p_bid_context->set_last_ad_open_timestamp(TimeUtil::Instance().GetTimestamp());
  }
}

void UtilValueUpdateProcessor::Process2(StrategySession* p_session) {
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  LOG_EVERY_N(INFO, 100000) << "UtilValueUpdateProcessor::Process: unit_id: " << msg.unit_id;
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;

  p_util_vals->is_sync_to_dsp = IsSyncResultRemote(p_bid_context, strategy_config, p_util_vals, exp_config);
}

void UtilValueUpdateProcessor::SetLinearPacingExpConfig(const MerchantOcpmMsg& msg,
  const MerchantOcpmBidConfig& strategy_config,
  const MerchantOcpmExpConfig& exp_config,
  BidStateInfoPtr bid_info_ptr,
  OcpmBidContext* p_bid_context,
  ProcessUtilValues* util_vals) {
  if (!p_bid_context || !util_vals) {
    return;
  }
  // 移动端先跳过该实验配置
  if (util_vals->is_fanstop) {
    if (msg.ocpc_action_type == "EVENT_ORDER_PAIED") {
      util_vals->pacing_type = static_cast<PacingType>(exp_config.pacing_type_num);
    }
    if (util_vals->is_unit_ocpm_roas_fanstop && exp_config.roas_enable_single_step_pacing_fanstop) {
      util_vals->pacing_type = PacingType::ROAS_SINGLE_STEP;
    }
    return;
  }

  auto esp_live_hosting_roas_union_bid_tail = MerchantKconfUtil::espLiveHostingRoasUnionBidTail();
  if (msg.is_roas_hosting_live && esp_live_hosting_roas_union_bid_tail &&
      esp_live_hosting_roas_union_bid_tail->IsOnFor(msg.campaign_id)) {
    util_vals->pacing_type = PacingType::ROAS_SINGLE_STEP;
    return;
  }

  std::string pro_ocpx = absl::Substitute("$0#$1", msg.promotion_type, msg.ocpc_action_type);
  std::string ocpx_action_type = msg.ocpc_action_type;
  if (p_bid_context->promotion_type().size() > 0 &&
      p_bid_context->ocpc_action_type().size() > 0) {
    pro_ocpx = absl::Substitute("$0#$1", p_bid_context->promotion_type(), p_bid_context->ocpc_action_type());
    ocpx_action_type = p_bid_context->ocpc_action_type();
  }
  // 满足线性策略配置调价才设置 promotion_type * ocpx
  const auto pro_ocpx_set = MerchantBidConfMgr::Instance().GetSmoothPacingOcpxSetPtr();
  if (pro_ocpx_set->find(pro_ocpx) != pro_ocpx_set->end()
      && exp_config.group_tag_str.find(msg.group_tag) != std::string::npos
      && msg.group_tag != "merchant_acc_explore"
      && exp_config.enable_cost_prior_ocpx_str.find(ocpx_action_type) != std::string::npos
      || p_bid_context->user_cost_prior_algo()) {
    util_vals->enable_cost_prior_algo = exp_config.enable_cost_prior_algo > 0 ? true : false;
    util_vals->pacing_type = static_cast<PacingType>(exp_config.pacing_type_num);
    util_vals->enable_linear_after_cold_start_no_conv_drop =
      exp_config.enable_linear_after_cold_start_no_conv_drop > 0 ? true : false;
    util_vals->enable_linear_adaptive_update_time =
      exp_config.enable_linear_adaptive_update_time > 0 ? true : false;
    util_vals->enable_linear_pacing_cold_start_low_bound = true;
  } else {
    util_vals->enable_cost_prior_algo = false;
    util_vals->pacing_type = PacingType::DEFAULT;
    util_vals->enable_linear_after_cold_start_no_conv_drop = false;
    util_vals->enable_linear_adaptive_update_time = false;
    util_vals->enable_linear_pacing_cold_start_low_bound = false;
  }

  if (util_vals->enable_cost_prior_algo && p_bid_context->user_cost_prior_algo() == false
      && exp_config.group_tag_str == msg.group_tag
      && exp_config.enable_cost_prior_ocpx_str.find(ocpx_action_type) != std::string::npos) {
    util_vals->reset_pacing = true;
    p_bid_context->set_user_cost_prior_algo(true);
  }
  if (!util_vals->enable_cost_prior_algo) {
    p_bid_context->set_user_cost_prior_algo(false);
  }
  // 全站使用单独的 pacing_type 实验
  if (util_vals->is_storewide_roas &&
      exp_config.storewide_enable_single_step_pacing) {
    util_vals->pacing_type = PacingType::ROAS_SINGLE_STEP;
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_bid_server", "STOREWIDE_SINGLE_STEP",
                                       std::to_string(p_bid_context->campaign_id()));
  }
  // ROAS 使用单独的 pacing_type 实验
  if ((util_vals->is_unit_ocpm_roas_pc && exp_config.roas_enable_single_step_pacing) ||
      (msg.speed_type == 6 && util_vals->is_roas
       && exp_config.roas_enable_single_step_pacing_costcap)) {
    util_vals->pacing_type = PacingType::ROAS_SINGLE_STEP;
  }
  if (msg.is_hosting_live && util_vals->is_roas) {
    util_vals->pacing_type = PacingType::ROAS_SINGLE_STEP;
  }
  // t7_roi 使用单独的 pacing_type 实验
  if (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI") {
    util_vals->pacing_type = PacingType::ROAS_SINGLE_STEP;
  }
}

inline void UtilValueUpdateProcessor::SetAdShortType(
  OcpmBidContext* p_bid_context, ProcessUtilValues* p_util_vals) {
  std::string t1 = p_bid_context->campaign_type();
  std::string t2 = p_bid_context->ocpc_action_type();
  if (p_bid_context->campaign_type() == "LIVE_STREAM_PROMOTE" || p_util_vals->is_fanstop) {
    t1 = "LIVE";
  } else if (p_bid_context->campaign_type() == "MERCHANT_RECO_PROMOTE") {
    t1 = "PHOTO";
  } else if (p_bid_context->campaign_type() == "PHOTO") {
    t1 = "CPHOTO";
  } else if (p_bid_context->campaign_type() == "SITE_PAGE") {
    t1 = "SITE";
  } else if (p_bid_context->campaign_type() == "TAOBAO") {
    t1 = "TAOBAO";
  }

  if (p_bid_context->ocpc_action_type() == "AD_LIVE_AUDIENCE") {
    t2 = "AUDIENCE";
  } else if (p_bid_context->ocpc_action_type() == "AD_MERCHANT_FOLLOW") {
    t2 = "FOLLOW";
  } else if (p_bid_context->ocpc_action_type() == "AD_MERCHANT_ROAS"
    || p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI"
    || p_bid_context->ocpc_action_type() == "AD_FANS_TOP_ROI"
    || p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS") {
    t2 = "ROAS";
  } else if (p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED") {
    t2 = "ORDER";
  } else if (p_bid_context->ocpc_action_type() == "AD_LIVE_PLAYED_1M") {
    t2 = "1M";
  } else if (p_bid_context->ocpc_action_type() == "AD_LIVE_PLAYED_3S") {
    t2 = "3S";
  } else if (p_bid_context->ocpc_action_type() == "AD_LIVE_SHOP_LINK_JUMP") {
    t2 = "JUMP";
  } else if (p_bid_context->ocpc_action_type() == "EVENT_GOODS_VIEW") {
    t2 = "VIEW";
  }

  p_util_vals->ad_short_type = static_cast<std::string>(absl::Substitute("$0_$1", t1, t2));
}

inline bool UtilValueUpdateProcessor::IsSyncResultRemote(OcpmBidContext* p_bid_context,
                                                         const MerchantOcpmBidConfig& strategy_config,
                                                         ProcessUtilValues* p_util_vals,
                                                         const MerchantOcpmExpConfig& exp_config) {
  if (p_util_vals->is_no_bid) {
    return false;
  }
  if (p_util_vals->is_sync_to_dsp) {
    return true;
  }
  if (p_util_vals->is_update_adjust) {
    return true;
  }
  // when ad's status change sync to remote
  if (p_util_vals->ad_status_tag_change) {
    return true;
  }
  if (p_util_vals->ad_off_target_cost > 0) {
    return true;
  }
  // 间隔超过阈值同步一次
  uint64_t now_ms = TimeUtil::Instance().GetTimestamp() / 1000;
  uint64_t last_sync_ms = p_bid_context->last_sync_result_timestamp() / 1000;
  uint64_t sync_interval_ms = strategy_config.result_sync_time_interval_ms();
  if (sync_interval_ms > 0 && now_ms - last_sync_ms >= sync_interval_ms) {
    return true;
  }
  return false;
}

inline uint64_t UtilValueUpdateProcessor::GetAdaptiveUpdateTime(const MerchantOcpmBidConfig& strategy_config,
    OcpmBidContext* p_bid_context, uint64_t update_interval_ms) {
  const auto& cold_start_config = strategy_config.cold_start_config();
  double prior_cost = static_cast<double>(cold_start_config.cold_start_cost_li());
  if (p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED" &&
      p_bid_context->cpa_bid() > 0 && cold_start_config.cold_start_cost_conv_num() > 0) {
    prior_cost = p_bid_context->cpa_bid() * cold_start_config.cold_start_cost_conv_num();
  }
  double cost = std::max(1.0, p_bid_context->cost()) + prior_cost;
  double target_cost = std::max(1.0, p_bid_context->target_cost()) + prior_cost;
  double adaptive_rate = target_cost / cost;
  return static_cast<uint64_t>(update_interval_ms / std::max(1.0, adaptive_rate));
}

inline bool UtilValueUpdateProcessor::IsApplyAdjust(const MerchantOcpmBidConfig& strategy_config,
                                                    OcpmBidContext* p_bid_context,
                                                    ProcessUtilValues* p_util_vals,
                                                    const MerchantOcpmExpConfig& exp_config,
                                                    const MerchantOcpmMsg& msg) {
  uint64_t update_interval_ms = strategy_config.adjust_update_time_interval_ms();
  if (!IsLEZero(exp_config.adjust_update_time_interval_ms_exp)) {
    update_interval_ms = exp_config.adjust_update_time_interval_ms_exp;
  }
  // roas 单独设置
  if (p_util_vals->is_roas) {
    if (!IsLEZero(exp_config.roas_adjust_update_time_interval_ms_exp)) {
      update_interval_ms = exp_config.roas_adjust_update_time_interval_ms_exp;
    }
    // 全站推广单独设置
    if (p_util_vals->is_storewide_roas) {
      update_interval_ms = strategy_config.storewide_adjust_update_time_interval_ms();
      if (!IsLEZero(exp_config.storewide_adjust_update_time_interval_ms_exp)) {
        update_interval_ms = exp_config.storewide_adjust_update_time_interval_ms_exp;
      }
      if (p_util_vals->is_storewide_acc2_kcb &&
          !IsLEZero(exp_config.storewide_acc2_kcb_time_interval_ms_exp)) {
        update_interval_ms = exp_config.storewide_acc2_kcb_time_interval_ms_exp;
      }
    }
  }
  if (p_util_vals->is_cold_start) {
    update_interval_ms = strategy_config.cold_start_config().adjust_update_time_interval_ms();
    // roas 冷启动调价间隔
    if (p_util_vals->is_roas) {
      if (!IsLEZero(exp_config.roas_coldstart_adjust_update_time_interval_ms_exp)) {
        update_interval_ms = exp_config.roas_coldstart_adjust_update_time_interval_ms_exp;
      }
      // 全站推广单独设置
      if (p_util_vals->is_storewide_roas) {
        if (!IsLEZero(exp_config.storewide_coldstart_adjust_update_time_interval_ms_exp)) {
          update_interval_ms = exp_config.storewide_coldstart_adjust_update_time_interval_ms_exp;
        }
        // 全互斥单独设置
        if (p_bid_context->reject_type() == 1 &&
            !IsLEZero(exp_config.storewide_allin_coldstart_adjust_update_time_interval_ms)) {
          update_interval_ms = exp_config.storewide_allin_coldstart_adjust_update_time_interval_ms;
        }
      }
    }
  } else if (p_util_vals->is_explore) {
    update_interval_ms = strategy_config.explore_config().adjust_update_time_interval_ms();
  } else if (p_util_vals->is_acc_explore_bid) {
    update_interval_ms = strategy_config.cold_start_config().adjust_update_time_interval_ms();
  }
  if (IsLEZero(p_bid_context->cost())) {
    if (update_interval_ms > 1000 && !p_util_vals->is_storewide_roas) {
      update_interval_ms = 1000;
    }
  }
  if (p_util_vals->enable_cost_prior_algo) {
    bool get_suc;
    auto ocpx_unit_tail_pid_params = MerchantBidConfMgr::Instance().GetOcpxUnitTailPidParams(
      p_bid_context->ocpc_action_type(), p_bid_context->unit_id(), &get_suc);
    if (get_suc == true && !IsLEZero(ocpx_unit_tail_pid_params.init_update_interval)) {
      update_interval_ms = ocpx_unit_tail_pid_params.init_update_interval;
    }
    if (IsLEZero(ocpx_unit_tail_pid_params.init_update_interval)) {
      LOG_EVERY_N(INFO, 100000) << "get ocpx_unit_tail_pid_params error unit_id=" << p_bid_context->unit_id()
        << " ocpx=" << p_bid_context->ocpc_action_type()
        << " pro=" << p_bid_context->promotion_type();
    }
  }
  if (p_util_vals->enable_linear_adaptive_update_time) {
    double tmp_update_interval_ms = GetAdaptiveUpdateTime(strategy_config, p_bid_context, update_interval_ms);
    if (!IsLEZero(tmp_update_interval_ms)) {
      update_interval_ms = tmp_update_interval_ms;
    }
  }
  if (p_util_vals->is_fanstop) {
    update_interval_ms = !IsLEZero(exp_config.update_interval_ms)
                          ? exp_config.update_interval_ms : 60000;
    if (p_util_vals->is_roas) {
      // [fanstopOcpmConfig]
      if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_roas_update_interval_ms)) {
        update_interval_ms = exp_config.fanstop_ocpm_roas_update_interval_ms;
      }
    } else {
      // [fanstopOcpmConfig]
      if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_cpa_update_interval_ms)) {
        update_interval_ms = exp_config.fanstop_ocpm_cpa_update_interval_ms;
      }
    }
  }
  // 已经同步过到  bid_service  并且命中实验, 则不考虑非消耗和转化消息触发调价
  bool skip_drop = false;
  skip_drop = msg.account_type == "ACCOUNT_ESP" &&
              (p_util_vals->is_unit_ocpm_roas_pc || p_util_vals->is_unit_ocpm_eop_pc) &&
              exp_config.skip_cost_drop_msg == 1;
  if (!skip_drop) {
    if (!(msg.cost > 0 || msg.target_cost > 0)) {
      p_util_vals->is_cost_target_msg = false;
      if ((p_util_vals->enable_cost_prior_algo)
          && p_bid_context->last_sync_result_timestamp() > 0
          && (p_bid_context->auto_cpa_bid() > 0 || p_bid_context->auto_roi_ratio() > 0)) {
        return false;
      }
    }
  }
  // roas 欠成本时，调价频率降低
  if ((p_util_vals->is_unit_ocpm_roas_pc || p_util_vals->is_unit_ocpm_roas_fanstop) &&
      (p_bid_context->cost() + 100000.0) / (p_bid_context->target_cost() + 100000.0) < 0.85) {
    update_interval_ms = update_interval_ms / 2;
  }
  update_interval_ms = !IsLEZero(update_interval_ms) ? update_interval_ms : 10000;
  // 时间间隔未超过阈值不调整
  p_util_vals->update_interval_ms = update_interval_ms;
  uint64_t now_ms = TimeUtil::Instance().GetTimestamp() / 1000;
  uint64_t last_update_ms = p_bid_context->last_update_adjust_timestamp() / 1000;
  if (update_interval_ms <= 0 || now_ms - last_update_ms < update_interval_ms) {
    p_util_vals->is_update_interval_ms = true;
    return false;
  } else {
    p_util_vals->is_update_interval_ms = false;
  }
  // 直播 PC 端
  if (p_bid_context->campaign_type() == "LIVE_STREAM_PROMOTE" &&
      p_bid_context->speed_type() == 1 &&
      (p_bid_context->ocpc_action_type() == "AD_MERCHANT_ROAS" ||
       p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED" ||
       (p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS" && msg.is_storewide_incompatible) ||
       (exp_config.enable_T7_Add_Update_adjust == 1 &&
        p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI" && msg.is_hosting_live) ||
       (p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI"
        && !msg.is_hosting_live && MerchantKconfUtil::SdT7AdjustUpdatetimeIntervalTail()->IsOnFor(
          msg.unit_id)))) {
    double interval_cost = p_bid_context->cost() - p_bid_context->last_update_cost();
    double interval_cost_exp = exp_config.adjust_update_time_interval_cost_exp * 1000;  // yuan
    if (!SPDM_enableDeleteSwExp() && p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS") {
      interval_cost_exp = exp_config.sw_adjust_update_time_interval_cost_exp * 1000;  // yuan
    }
    if (p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI") {
      if (msg.is_hosting_live) {
        interval_cost_exp = exp_config.t7_adjust_update_time_interval_cost_exp * 1000;  // yuan
      } else if (MerchantKconfUtil::SdT7AdjustUpdatetimeIntervalTail()->IsOnFor(msg.unit_id)) {
        interval_cost_exp = MerchantKconfUtil::sd_t7_adjust_update_time_interval_cost() * 1000;  // yuan
      }
    }
    // 持续超成本且预期花费不变时，满足消耗间隔才压价
    if (!IsLEZero(interval_cost_exp) &&
        p_bid_context->last_update_cost() > p_bid_context->last_update_target_cost() &&
        p_bid_context->cost() > p_bid_context->target_cost() &&
        p_bid_context->target_cost() <= p_bid_context->last_update_target_cost() &&
        interval_cost <= interval_cost_exp) {
      LOG_EVERY_N(INFO, 100) << "adjust_update_time_interval_cost_exp:"
         << ", ocpc_action_type: " << p_bid_context->ocpc_action_type()
         << ", speed_type: " << p_bid_context->speed_type()
         << ", campaign_id: " << p_bid_context->campaign_id()
         << ", unit_id: " << p_bid_context->unit_id()
         << ", last_update_cost: " << p_bid_context->last_update_cost()
         << ", last_update_target_cost: " << p_bid_context->last_update_target_cost()
         << ", cost: " << p_bid_context->cost()
         << ", target_cost: " << p_bid_context->target_cost()
         << ", interval_cost_exp: " << interval_cost_exp
         << ", update_interval_ms: " << update_interval_ms;
      return false;
    }
    // 持续欠成本时，满足消耗间隔才提价
    double interval_cost_exp_owe = exp_config.adjust_update_time_interval_cost_exp_owe * 1000;  // yuan
    if (!IsLEZero(interval_cost_exp_owe) &&
        p_bid_context->last_update_cost() < p_bid_context->last_update_target_cost() &&
        p_bid_context->cost() < p_bid_context->target_cost() &&
        interval_cost <= interval_cost_exp_owe) {
      LOG_EVERY_N(INFO, 100) << "adjust_update_time_interval_cost_exp:"
                             << ", ocpc_action_type: " << p_bid_context->ocpc_action_type()
                             << ", speed_type: " << p_bid_context->speed_type()
                             << ", campaign_id: " << p_bid_context->campaign_id()
                             << ", unit_id: " << p_bid_context->unit_id()
                             << ", last_update_cost: " << p_bid_context->last_update_cost()
                             << ", last_update_target_cost: " << p_bid_context->last_update_target_cost()
                             << ", cost: " << p_bid_context->cost()
                             << ", target_cost: " << p_bid_context->target_cost()
                             << ", interval_cost_exp: " << interval_cost_exp
                             << ", update_interval_ms: " << update_interval_ms;
      return false;
    }
  }
  return true;
}

inline bool UtilValueUpdateProcessor::IsUpdate(
  const BidStateInfoPtr& bid_info_ptr, const MerchantOcpmMsg& msg,
  OcpmBidContext* p_bid_context, const MerchantOcpmBidConfig& strategy_config,
  const MerchantOcpmExpConfig& exp_config, ProcessUtilValues* p_util_vals) {
  if (msg.is_degrade) {
    return false;
  }
  if (msg.is_hot_unit) {
    return false;
  }
  if (p_util_vals->is_no_bid) {
    return false;
  }
  if (FLAGS_enable_bid_no_diff_switch || p_util_vals->is_debug) {
    LOG(WARNING) << "no diff debug has_bid_state_info_ptr, " << bid_info_ptr
      << " msg seq, " << msg.event_server_timestamp
      << " unit id, " << msg.unit_id;
  }

  auto is_live_hosting_effect = msg.is_hosting_live;
  if (bid_info_ptr) {
    if (FLAGS_enable_bid_no_diff_switch || p_util_vals->is_debug) {
      LOG(WARNING) << "no diff debug has_bid_state_info_ptr, " << bid_info_ptr
        << " bid_state_info_online, " << bid_info_ptr->online
        << " bid_state_info_advertisable, " << bid_info_ptr->advertisable
        << " bid_state_info_is_live, " << bid_info_ptr->is_live
        << " bid_state_info_is_status_open, " << bid_info_ptr->is_status_open
        << " bid_state_info_ad_status, " << bid_info_ptr->ad_status
        << " bid_state_info_left_budget, " << bid_info_ptr->left_budget
        << " msg seq, " << msg.event_server_timestamp
        << " unit id, " << msg.unit_id;
    }

    if (p_util_vals->is_debug) {
      LOG(WARNING)
      << "Debug:tid:" << p_util_vals->thread_id
      << " is_on_live: " << bid_info_ptr->is_live
      << " ad_status: " << bid_info_ptr->ad_status
      << " advertisable: " << bid_info_ptr->advertisable
      << " is_status_open: " << bid_info_ptr->is_status_open
      << " is_online: " << bid_info_ptr->online;
    }
    p_util_vals->is_bid_info = true;
    p_util_vals->is_on_live = bid_info_ptr->is_live;
    p_util_vals->ad_status = static_cast<int>(bid_info_ptr->ad_status);
    p_util_vals->advertisable = bid_info_ptr->advertisable;
    p_util_vals->is_status_open = bid_info_ptr->is_status_open;
    p_util_vals->is_online = bid_info_ptr->online;
    p_util_vals->old_is_out_of_budget = p_bid_context->old_is_out_of_budget();
    if (bid_info_ptr->left_budget <= exp_config.unit_budget_hit_threshold) {
      p_util_vals->is_out_of_budget = true;
    }
    if ((p_bid_context->old_is_out_of_budget() && msg.conv_num > 0) ||
        !p_bid_context->old_is_out_of_budget()) {
      p_bid_context->set_old_is_out_of_budget(p_util_vals->is_out_of_budget);
    }
    if (!bid_info_ptr->online ||
        !bid_info_ptr->advertisable ||
        !bid_info_ptr->is_status_open) {
      return false;
    }
    if (msg.campaign_type == "LIVE_STREAM_PROMOTE"
        || MerchantKconfUtil::merchantLiveCampaignTypes()->count(msg.campaign_type)) {
      if (!bid_info_ptr->is_live) {
        return false;
      }
    }
    return true;
  } else if (is_live_hosting_effect ||
             msg.is_storewide_with_order) {
    // msg 对应单元关停导致取不到单元层级索引就取计划层级索引
    const BidStateInfoPtr& campaign_info_ptr = msg.campaign_bid_info_ptr;
    if (campaign_info_ptr) {
      bool is_campaign_status_open = campaign_info_ptr->is_campaign_status_open;
      bool is_live = campaign_info_ptr->is_live;
      LOG(INFO) << "storewide_unit_ptr_null: "
          << " unit_id:" << msg.unit_id
          << " campaign_id:" << msg.campaign_id
          << " roi_ratio: " << campaign_info_ptr->roi_ratio
          << " is_campaign_status_open: " << is_campaign_status_open
          << " is_live: " << is_live
          << " msg: " << msg;
      if (is_campaign_status_open &&
          (msg.campaign_type != "LIVE_STREAM_PROMOTE" || is_live)) {
        p_util_vals->is_bid_info = true;
        return true;
      }
    }
    p_util_vals->is_bid_info = false;
    return false;
  } else {
    p_util_vals->is_bid_info = false;
  }
  return false;
}

inline void UtilValueUpdateProcessor::SetFlowControlStatus(const BidStateInfoPtr& bid_info_ptr,
                                                           OcpmBidContext* p_bid_context,
                                                           ProcessUtilValues* p_util_vals,
                                                           const MerchantOcpmExpConfig& exp_config) {
  if (p_util_vals == nullptr || p_bid_context == nullptr) {
    return;
  }
  if (!bid_info_ptr) {
    return;
  }
  auto cost = p_bid_context->cost();
  auto left_budget = bid_info_ptr->left_budget;
  if (exp_config.enable_flow_control == 0) {
    return;
  }
  // 限定成本保护
  if (p_bid_context->speed_type() != 1) {
    return;
  }
  // 限定订单和 ROAS 两个优化目标
  if (p_bid_context->ocpc_action_type() != "EVENT_ORDER_PAIED" &&
      p_bid_context->ocpc_action_type() != "AD_MERCHANT_ROAS") {
    return;
  }
  // 限定 PC 端
  if (p_util_vals->is_fanstop) {
    return;
  }
  if (exp_config.flow_control_type == 1) {
    if (left_budget < exp_config.flow_control_left_budget_thre) {
      p_util_vals->is_flow_control = true;
    }
  } else if (exp_config.flow_control_type == 2) {
    double ratio = 1.0;
    if (left_budget + cost > 0) {
      ratio = left_budget / (left_budget + cost);
    }
    if (left_budget < exp_config.flow_control_left_budget_thre ||
        ratio < exp_config.flow_control_ratio_thre) {
      p_util_vals->is_flow_control = true;
    }
  }
  return;
}

inline void UtilValueUpdateProcessor::SetCostCap(const MerchantOcpmBidConfig& strategy_config,
  const BidStateInfoPtr& bid_info_ptr, OcpmBidContext* p_bid_context, ProcessUtilValues* p_util_vals,
  const MerchantOcpmMsg& msg) {
  if (!strategy_config.ocpm_cost_cap_config().enable_ocpm_cost_cap()) {
    return;
  }
  // 筛选 costcap 订单
  if (!bid_info_ptr) {
    return;
  }

  // 全站推广
  if ((bid_info_ptr->budget_smart_allocation == 3 || bid_info_ptr->scene_oriented_type == 30) &&
      (AdBaseKconfUtil::enableCboStoreWide()
      || AdBaseKconfUtil::cboStoreWideCampaignWhiteList()->count(p_bid_context->campaign_id()) > 0)) {
    p_util_vals->is_costcap_nobid = true;
    return;
  }
  if (!AdBaseKconfUtil::enableSkipOcpmCostCap()) {
    if (bid_info_ptr->speed_type != kuaishou::ad::AdEnum::SPEED_COST_CAP
        && bid_info_ptr->bid_strategy != kuaishou::ad::AdEnum::COST_CAP_BID_STRATEGY) {
      return;
    }
  }
  // 筛选白名单
  if (!IsOcpmCostCap(p_bid_context->unit_id(), bid_info_ptr)
      && !IsFanstopOcpmCostCap(msg.unit_id, msg.campaign_id, msg.payer_id, bid_info_ptr)) {
    return;
  }
  double fanstop_roi_upper_thre = MerchantKconfUtil::fanstopRoiUpperThre();
  double fanstop_roi_lower_thre = MerchantKconfUtil::fanstopRoiLowerThre();
  double fanstop_roi_ratio = static_cast<double>(bid_info_ptr->cpa_bid) / 1000;
  if (p_util_vals->is_fanstop && p_util_vals->is_roas &&
      fanstop_roi_ratio > fanstop_roi_lower_thre && fanstop_roi_ratio < fanstop_roi_upper_thre) {
    // campaign 尾号实验
    const auto fanstop_skip_costcap_campaign_tail_set =
                                        MerchantKconfUtil::fanstopSkipCostcapCampaignTailSet();
    if (std::count(fanstop_skip_costcap_campaign_tail_set->begin(),
                   fanstop_skip_costcap_campaign_tail_set->end(),
                   msg.campaign_id % 100) > 0) {
      p_util_vals->is_ocpm_bid_process = true;
      DebugCpabidType(bid_info_ptr, msg);
      return;
    }

    // payer 尾号实验
    const auto fanstop_skip_costcap_payer_tail_set = MerchantKconfUtil::fanstopSkipCostcapPayerTailSet();
    if (std::count(fanstop_skip_costcap_payer_tail_set->begin(), fanstop_skip_costcap_payer_tail_set->end(),
        msg.payer_id % 100) > 0) {
      p_util_vals->is_ocpm_bid_process = true;
      DebugCpabidType(bid_info_ptr, msg);
      return;
    }
  }
  p_util_vals->is_costcap_nobid = true;
}

inline void UtilValueUpdateProcessor::SetStoreWideNobid(const MerchantOcpmBidConfig& strategy_config,
  const BidStateInfoPtr& bid_info_ptr, OcpmBidContext* p_bid_context, ProcessUtilValues* p_util_vals,
  const MerchantOcpmMsg& msg) {
  if (!bid_info_ptr) {
    return;
  }
  // 全站推广暗投 nobid
  if (IsStorewideShadowNobid(bid_info_ptr)) {
    p_util_vals->is_costcap_nobid = true;
    p_util_vals->is_storewide_shadow_nobid = true;
  }
  return;
}

inline void UtilValueUpdateProcessor::DebugCpabidType(const BidStateInfoPtr& bid_info_ptr,
                                                      const MerchantOcpmMsg& msg) {
  LOG_EVERY_N(INFO, 100000) << "SetCostCap_skipCostCap:"
                            << ", unit_id:" << msg.unit_id
                            << ", payer_id:" << msg.payer_id
                            << ", account_type:" << msg.account_type
                            << ", campaign_type:" << msg.campaign_type
                            << ", ocpc_action_type:" << msg.ocpc_action_type
                            << ", speed_type:" << bid_info_ptr->speed_type
                            << ", bid_strategy:" << bid_info_ptr->bid_strategy
                            << ", cpa_bid:" << bid_info_ptr->cpa_bid
                            << std::endl;
}

static inline double GetRoasHierarchicalLowerBound(double price_ratio) {
  if (price_ratio <= 1.0 || !MerchantKconfUtil::enableRoasHierarchicalHardBound()) {
    return 0.0;
  }
  // key: 超成本比例如 1.2, value: 压价上限如 2.0（对应 roas lower_bound 0.5）
  auto bound_conf = MerchantKconfUtil::roasHierarchicalHardBoundConf();
  std::vector<std::tuple<double, double>> vals;
  double tmp_key;
  for (const auto &pair : *bound_conf) {
    if (absl::SimpleAtod(pair.first, &tmp_key)) {
      vals.push_back(std::make_tuple(tmp_key, pair.second));
    }
  }
  std::sort(vals.begin(), vals.end(),
            [](std::tuple<double, double> const& m,
               std::tuple<double, double> const& n)
            { return std::get<0>(m) < std::get<0>(n); });
  for (int i = 0; i < vals.size(); i++) {
    if (price_ratio < std::get<0>(vals[i]) && std::get<1>(vals[i]) > 0) {
      return 1.0 / std::get<1>(vals[i]);
    }
  }
  return 0.0;
}

inline void UtilValueUpdateProcessor::SetHardBound(const MerchantOcpmMsg& msg,
  const MerchantOcpmBidConfig& strategy_config, BidStateInfoPtr& bid_info_ptr,
  const MerchantOcpmExpConfig& exp_config, OcpmBidContext* p_bid_context, ProcessUtilValues* p_util_vals) {
  double hard_bid_lower_bound_rate = strategy_config.hard_bid_lower_bound_rate();
  double hard_bid_upper_bound_rate = strategy_config.hard_bid_upper_bound_rate();
  auto loan_white_list = MerchantKconfUtil::loansAuthorWhiteList();
  auto loan_ratio_list = MerchantKconfUtil::loansAuthorRatioList();
  kuaishou::ad::AdEnum::CampaignType campaign_type = kuaishou::ad::AdEnum::UNKNOWN_CAMPAIGN_TYPE;
  kuaishou::ad::AdEnum_CampaignType_Parse(msg.campaign_type, &campaign_type);
  std::string campaign_type_str = CampaignTypeUnify(campaign_type, bid_info_ptr);
  // 限制 ROI 的 bound
  if (p_util_vals->is_roas) {
    if (!IsLEZero(exp_config.hard_bid_upper_bound_rate_exp_roas)) {
      hard_bid_upper_bound_rate = exp_config.hard_bid_upper_bound_rate_exp_roas;
    }
    if (!IsLEZero(exp_config.hard_bid_lower_bound_rate_exp_roas)) {
      hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_exp_roas;
    }
    auto bound_group_config_map = MerchantKconfUtil::SwBidBoundGroupTagConfigMap();
    auto bound_group_iter = bound_group_config_map->find(p_bid_context->group_tag());
    bool bound_model_config = false;
    if (SPDM_enableFixBidBoundGroupExp() && bound_group_config_map &&
        bound_group_iter != bound_group_config_map->end()) {
      bound_model_config = bound_group_iter->second;
    }
    // 全店 roi 单独设置 bound
    if (msg.is_store_wide_roi_reco_conv || msg.is_storewide_with_order) {
      if (!IsLEZero(exp_config.hard_bid_upper_bound_rate_exp_storewide_roas)) {
        hard_bid_upper_bound_rate = exp_config.hard_bid_upper_bound_rate_exp_storewide_roas;
      }
      if (!IsLEZero(exp_config.hard_bid_lower_bound_rate_exp_storewide_roas)) {
        hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_exp_storewide_roas;
      }
      if (p_bid_context->reject_type() == 1 &&
          MerchantKconfUtil::storewideHardBoundTail()->count(msg.author_id % 100)) {
        //  全互斥单独设置 or 实验组单独设置
        if (!IsLEZero(exp_config.hard_bid_upper_bound_rate_exp_storewide_roas_qhc)) {
          hard_bid_upper_bound_rate = exp_config.hard_bid_upper_bound_rate_exp_storewide_roas_qhc;
        }
        if (!IsLEZero(exp_config.hard_bid_lower_bound_rate_exp_storewide_roas_qhc)) {
          hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_exp_storewide_roas_qhc;
        }
      }
      if ((exp_config.enable_storewide_hard_lower_dynamic ||
        MerchantKconfUtil::whiteHardBoundDynamic()->count(msg.author_id)) &&
       !MerchantKconfUtil::blackHardBoundDynamic()->IsOnFor(msg.author_id) ||
       (p_bid_context->cost_in_hour() > 0)) {
        const auto merchant_storewide = MerchantKconfUtil::MerchantStorewide();
        if (merchant_storewide) {
          const auto& bound_map_orign = merchant_storewide->data().cost_rate_to_bound_map();
          std::map<int64_t, kuaishou::ad::MerchantStorewideConfig_Bounds> bound_map;  // 变成有序 map
          for (const auto& pair : bound_map_orign) {
            bound_map.insert(pair);
          }
          double soomth_cost = 1e5;
          soomth_cost = std::max(soomth_cost, p_bid_context->yesterday_cost() * 0.2);
          const auto fixStBoundSmoothTail = MerchantKconfUtil::fixStBoundSmoothTail();
          bool fix_st_bound_smooth = fixStBoundSmoothTail &&
               fixStBoundSmoothTail->IsOnFor(p_bid_context->author_id());
          if (fix_st_bound_smooth || bound_model_config) {
            soomth_cost = 1e5;
          }
          auto storewideDynamicBoundTail = MerchantKconfUtil::storewideDynamicBoundTail();
          bool is_new_bs = storewideDynamicBoundTail &&
              storewideDynamicBoundTail->IsOnFor(msg.author_id) &&
              (p_bid_context->gimbal_price_before() > 0) && (p_bid_context->cost() > 100 * 1000);
          double use_cost = is_new_bs ? p_bid_context->gimbal_price_before() : p_bid_context->cost();
          double cost_ratio = (use_cost + soomth_cost) /
                              (p_bid_context->target_cost() + soomth_cost) * 100;
          for (auto iter = bound_map.begin(); iter != bound_map.end(); ++iter) {
            if (cost_ratio < iter->first &&
                IsGTZero(iter->second.lower()) && IsGTZero(iter->second.upper())) {
              hard_bid_lower_bound_rate = 1 / iter->second.lower();
              hard_bid_upper_bound_rate = 1 / iter->second.upper();
              LOG_EVERY_N(INFO, 4000) << "debug storewide_hard_lower_dynamic."
                        << " cost_ratio: " << cost_ratio
                        << ", campaign_id: " << p_bid_context->campaign_id()
                        << ", iter->first: " << iter->first
                        << ", iter->second.lower: " << iter->second.lower()
                        << ", iter->second.upper: " << iter->second.upper()
                        << ", hard_bid_lower_bound_rate: " << hard_bid_lower_bound_rate
                        << ", hard_bid_upper_bound_rate: " << hard_bid_upper_bound_rate;
              break;
            }
          }
          if (SPDM_enableByAuthorType() && bid_info_ptr &&
              MerchantKconfUtil::byAuthorTypeAdjustSwitch()->IsOnFor(msg.author_id)) {
            double adjust_ratio = 1.0;
            std::string author_type = "";
            if (bid_info_ptr->storewide_gmv_ratio > 0.8) {
              author_type = "a";  // 铁广类型
            } else if (bid_info_ptr->storewide_gmv_ratio > 0.6) {
              author_type = "b";  // 普通广告型
            } else if (bid_info_ptr->storewide_gmv_ratio > 0.2) {
              author_type = "c";  // 均衡型
            } else if (bid_info_ptr->storewide_gmv_ratio > 1e-6) {
              author_type = "d";  // 内容型
            }
            const auto& author_type_map = merchant_storewide->data().author_type_map();
            auto iter = author_type_map.find(author_type);
            if (iter != author_type_map.end() && iter->second > 0) {
              adjust_ratio = iter->second;
            }
            hard_bid_lower_bound_rate /= adjust_ratio;
            hard_bid_upper_bound_rate /= adjust_ratio;
            LOG_EVERY_N(INFO, 5000) << "debug byAuthorTypeAdjustSwitch."
                                << "author_type: " << author_type
                                <<", storewide_gmv_ratio: " << bid_info_ptr->storewide_gmv_ratio
                                << ", author id: " << msg.author_id
                                << ", adjust_ratio: " << adjust_ratio
                                << ", hard_bid_lower_bound_rate: " << hard_bid_lower_bound_rate
                                << ", hard_bid_upper_bound_rate: " << hard_bid_upper_bound_rate;
          }

          if (MerchantKconfUtil::enhanceBoundDynamic()->IsOnFor(msg.campaign_id)) {
            auto upper_status = p_bid_context->mutable_cost_rate_status()->upper_status();
            auto lower_status = p_bid_context->mutable_cost_rate_status()->lower_status();
            auto is_upper = p_bid_context->mutable_cost_rate_status()->is_upper();
            auto is_lower = p_bid_context->mutable_cost_rate_status()->is_lower();
            //  5 分钟一段，半小时分成 6 个桶
            int64_t now_index = (TimeUtil::Instance().GetTimestamp() / 1000000 / 60 / 5) % 6;
            if (cost_ratio < 80) {
              upper_status |= (1 << now_index);  // 将第 now_index 个桶改为 1
            } else {
              upper_status &= ~(1 << now_index);  // 将第 now_index 个桶改为 0
            }
            if (cost_ratio > 120) {
              lower_status |= (1 << now_index);  // 将第 now_index 个桶改为 1
            } else {
              lower_status &= ~(1 << now_index);  // 将第 now_index 个桶改为 0
            }
            if (upper_status == (1 << 6) - 1) {  // 所有桶都欠成本，表示这半小时一直欠成本
              is_upper = true;  // 启动欠成本策略
            }
            if (lower_status == (1 << 6) -  1) {  // 所有桶都超成本，表示这半小时一直超成本
              is_lower = true;  // 启动超成本策略
            }
            if (is_upper && upper_status == 0 && cost_ratio > 95) {
              // 如果最近半小时没有欠成本且成本率恢复，关闭欠策略
              is_upper = false;
            }
            if (is_lower && lower_status == 0 && cost_ratio < 105) {
              // 如果最近半小时没有超成本且成本率恢复，关闭超策略
              is_lower = false;
            }
            const double enhance_upper_rate = merchant_storewide->data().enhance_upper_rate();
            if (IsGTZero(enhance_upper_rate) && is_upper) {  // 欠成本时加强 bound
              hard_bid_upper_bound_rate /= enhance_upper_rate;
            }
            const double enhance_lower_rate = merchant_storewide->data().enhance_lower_rate();
            double lower_bound_start = hard_bid_lower_bound_rate;
            if (IsGTZero(merchant_storewide->data().lower_bound_start())) {
              lower_bound_start = merchant_storewide->data().lower_bound_start();
            }
            if (IsGTZero(enhance_lower_rate) && is_lower && hard_bid_lower_bound_rate >= lower_bound_start) {
              //  超成本时加强 bound
              hard_bid_lower_bound_rate /= enhance_lower_rate;
            }
            p_bid_context->mutable_cost_rate_status()->set_upper_status(upper_status);
            p_bid_context->mutable_cost_rate_status()->set_lower_status(lower_status);
            p_bid_context->mutable_cost_rate_status()->set_is_upper(is_upper);
            p_bid_context->mutable_cost_rate_status()->set_is_lower(is_lower);
            LOG_EVERY_N(INFO, 1000) << "debug enhanceBoundDynamic."
                      << "campaign id: " << msg.campaign_id
                      << ", cost_ratio: " << cost_ratio
                      << ", upper_status: " << upper_status
                      << ", lower_status: " << lower_status
                      << ", is_upper: " << is_upper
                      << ", is_lower: " << is_lower
                      << ", now_index: " << now_index
                      << ", enhance_upper_rate: " << enhance_upper_rate
                      << ", enhance_lower_rate: " << enhance_lower_rate
                      << ", hard_bid_upper_bound_rate: " << hard_bid_upper_bound_rate
                      << ", hard_bid_lower_bound_rate: " << hard_bid_lower_bound_rate;
          }
        }
      }
      const auto highRoiFixBoundTail = MerchantKconfUtil::highRoiFixBoundTail();
      bool high_roi_fix_bound = highRoiFixBoundTail &&
          highRoiFixBoundTail->IsOnFor(p_bid_context->author_id());
      if (SPDM_enableFixHighRoiBound() && high_roi_fix_bound && p_bid_context->roi_ratio() > 10) {
        double high_roi_upper_bound = MerchantKconfUtil::high_roi_upper_bound();
        hard_bid_upper_bound_rate = p_bid_context->roi_ratio() * high_roi_upper_bound;
      }
    }
    const auto fixStT7RoiBoundTail = MerchantKconfUtil::fixStT7RoiBoundTail();
    bool fix_st_t7_roi_bound = fixStT7RoiBoundTail &&
         fixStT7RoiBoundTail->IsOnFor(p_bid_context->author_id());
    // t7_roi 单独设置 bound
    if (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI" && !((msg.is_store_wide_roi_reco_conv ||
        msg.is_storewide_with_order) && (fix_st_t7_roi_bound || bound_model_config))) {
      if (!IsLEZero(exp_config.hard_bid_upper_bound_rate_exp_t7_roi)) {
        hard_bid_upper_bound_rate = exp_config.hard_bid_upper_bound_rate_exp_t7_roi;
      }
      if (!IsLEZero(exp_config.hard_bid_lower_bound_rate_exp_t7_roi)) {
        hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_exp_t7_roi;
      }
    }
    // 超成本时使用分级 bound
    bool enable_store_wide = p_util_vals->is_cbo_storewide_roas &&
          (exp_config.enable_storewide_hierarchical_bound ||
           p_bid_context->reject_type() == 1);
    bool enable_non_store_wide = !p_util_vals->is_storewide_roas && exp_config.enable_roas_hierarchical_bound;
    if (enable_store_wide || enable_non_store_wide) {
      if (p_bid_context->target_cost() > 0) {
        double price_ratio = 1.0 * p_bid_context->cost() / p_bid_context->target_cost();
        double new_bound = GetRoasHierarchicalLowerBound(price_ratio);
        if (new_bound > 0) {
          hard_bid_lower_bound_rate = new_bound;
        }
      }
    }
    // 移动端 ROI 限制
    if (p_util_vals->is_fanstop) {
      // [fanstopOcpmConfig]
      if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_roas_hard_upper_bound_rate)) {
        hard_bid_upper_bound_rate = exp_config.fanstop_ocpm_roas_hard_upper_bound_rate;
      }
      if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_roas_hard_lower_bound_rate)) {
        hard_bid_lower_bound_rate = exp_config.fanstop_ocpm_roas_hard_lower_bound_rate;
      }
    }
    // 成本上限 ROI 限制
    SetCostcapRoasBoundUtil(msg, exp_config, p_util_vals, bid_info_ptr, p_bid_context,
                            campaign_type_str,
                            &hard_bid_upper_bound_rate, &hard_bid_lower_bound_rate);
    // 高消耗单元下限实验
    if (p_bid_context->yesterday_cost() >= exp_config.cross_day_cost_threshold &&
        p_bid_context->cost() < exp_config.high_cost_unit_cost_threshold) {
      hard_bid_lower_bound_rate = strategy_config.high_cost_hard_bid_lower_bound_rate_roas();
    }
    // 流控实验单元上下界
    if (p_util_vals->is_flow_control) {
      hard_bid_lower_bound_rate = exp_config.roas_flow_control_lower_bound;
      hard_bid_upper_bound_rate = exp_config.roas_flow_control_upper_bound;
    }
    // 贷款单元下限实验
    if ((exp_config.enable_author_loans && loan_white_list) || (exp_config.enable_loans && loan_ratio_list)) {
      if (exp_config.enable_author_loans) {
        auto iter = loan_white_list->find(p_bid_context->author_id());
        if (iter != loan_white_list->end()) {
          hard_bid_lower_bound_rate = exp_config.roas_loan_lower_bound;
        }
      }
      if (exp_config.enable_loans) {
        auto iter = loan_ratio_list->find(p_bid_context->author_id());
        if (iter != loan_ratio_list->end()) {
          hard_bid_lower_bound_rate = exp_config.roas_loan_lower_bound;
        }
      }
    }

    if (msg.is_roas_hosting_live_roas_unit &&
        !IsLEZero(exp_config.hard_bid_lower_bound_rate_exp_roas_hosting_unit)) {
      hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_exp_roas_hosting_unit;
    }
    // 直播直投单独设置 bound
    if (msg.account_type == "ACCOUNT_ESP" &&
        p_util_vals->is_unit_ocpm_roas_pc && p_bid_context->item_type() == "ITEM_LIVE"
        && !IsLEZero(exp_config.hard_bid_lower_bound_rate_exp_roas_live)) {
      hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_exp_roas_live;
    }
  } else {
    // 移动端 cpa_bid 限制
    if (p_util_vals->is_fanstop) {
      // [fanstopOcpmConfig]
      if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_cpa_hard_upper_bound_rate)) {
        hard_bid_upper_bound_rate = exp_config.fanstop_ocpm_cpa_hard_upper_bound_rate;
      }
      if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_cpa_hard_lower_bound_rate)) {
        hard_bid_lower_bound_rate = exp_config.fanstop_ocpm_cpa_hard_lower_bound_rate;
      }
    }
    // 直播 cpa 的调价上限制
    if (p_util_vals->enable_cost_prior_algo) {
        bool get_suc;
        auto ocpx_unit_tail_pid_params = MerchantBidConfMgr::Instance().GetOcpxUnitTailPidParams(
          p_bid_context->ocpc_action_type(), p_bid_context->unit_id(), &get_suc);
        if (get_suc == true && !IsLEZero(ocpx_unit_tail_pid_params.pacing_upper_bound)) {
            hard_bid_upper_bound_rate = ocpx_unit_tail_pid_params.pacing_upper_bound;
        }
    }
    // 成本上限 ROI 限制
    if (p_util_vals->is_costcap_nobid
        && exp_config.enable_costcap_campaign_str.find(campaign_type_str) != std::string::npos) {
      if (!IsLEZero(exp_config.costcap_hard_cpa_bid_upper_bound_rate)) {
        hard_bid_upper_bound_rate = exp_config.costcap_hard_cpa_bid_upper_bound_rate;
      }
      if (!IsLEZero(exp_config.costcap_hard_cpa_bid_lower_bound_rate)) {
        hard_bid_lower_bound_rate = exp_config.costcap_hard_cpa_bid_lower_bound_rate;
      }
    }
    if (!p_util_vals->is_fanstop && !p_util_vals->is_costcap_nobid) {
      // 限制 cpa_bid 的 bound
      UpperBoundExp(exp_config, msg, &hard_bid_upper_bound_rate);
      LowerBoundExp(exp_config, msg, &hard_bid_lower_bound_rate);
    }
    if (p_bid_context->ocpc_action_type() == "AD_MERCHANT_FOLLOW" ||
        p_bid_context->ocpc_action_type() == "AD_MERCHANT_FOLLOW_QUALITY") {
      if (p_bid_context->item_type() == "ITEM_PHOTO_TO_LIVE") {
        hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_exp_p2l_follow;
      } else {
        hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_exp_live_follow;
      }
    }
    // 搜索明投上限实验
    if (IsSearchFlow(msg.page_id) && !msg.is_search_antou &&
        !IsLEZero(exp_config.hard_bid_upper_bound_rate_search_mingtou_order)) {
      hard_bid_upper_bound_rate = exp_config.hard_bid_upper_bound_rate_search_mingtou_order;
    }
    // 高消耗单元下限实验
    if (p_bid_context->yesterday_cost() >= exp_config.cross_day_cost_threshold &&
        p_bid_context->cost() < exp_config.high_cost_unit_cost_threshold) {
      hard_bid_lower_bound_rate = strategy_config.high_cost_hard_bid_lower_bound_rate();
    }
    // 流控实验单元上下界
    if (p_util_vals->is_flow_control) {
      hard_bid_lower_bound_rate = exp_config.eop_flow_control_lower_bound;
      hard_bid_upper_bound_rate = exp_config.eop_flow_control_upper_bound;
    }
    // 贷款单元下限实验
    if ((exp_config.enable_author_loans && loan_white_list) || (exp_config.enable_loans && loan_ratio_list)) {
      if (exp_config.enable_author_loans) {
        auto iter = loan_white_list->find(p_bid_context->author_id());
        if (iter != loan_white_list->end()) {
          hard_bid_lower_bound_rate = exp_config.eop_loan_lower_bound;
        }
      }
      if (exp_config.enable_loans) {
        auto iter = loan_ratio_list->find(p_bid_context->author_id());
        if (iter != loan_ratio_list->end()) {
          hard_bid_lower_bound_rate = exp_config.eop_loan_lower_bound;
        }
      }
    }
    if (IsLSPTravelLiveOrderUnit(exp_config, msg, bid_info_ptr)) {
      if (!IsLEZero(exp_config.lsp_travel_live_order_lower_bound)) {
        hard_bid_lower_bound_rate = exp_config.lsp_travel_live_order_lower_bound;
      }
      if (!IsLEZero(exp_config.lsp_travel_live_order_upper_bound)) {
        hard_bid_upper_bound_rate = exp_config.lsp_travel_live_order_upper_bound;
      }
    }
    // 直播直投单独设置 bound
    if (msg.account_type == "ACCOUNT_ESP" &&
        p_util_vals->is_unit_ocpm_eop_pc && p_bid_context->item_type() == "ITEM_LIVE"
        && !IsLEZero(exp_config.hard_bid_lower_bound_rate_exp_eop_live)) {
      hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_exp_eop_live;
    }
  }
  if (msg.is_roas_hosting_live) {
    if (!IsLEZero(exp_config.hard_bid_upper_bound_rate_live_hosting_roas)) {
      hard_bid_upper_bound_rate = exp_config.hard_bid_upper_bound_rate_live_hosting_roas;
    }
    if (!IsLEZero(exp_config.hard_bid_lower_bound_rate_live_hosting_roas)) {
      hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_live_hosting_roas;
    }
  }
  // 高客单提价上限实验
  if (exp_config.enable_high_atv_bid_rate_upper == 1 &&
    GetTargetAtv(p_bid_context) >= MerchantKconfUtil::high_atv_lower_bound()) {
      hard_bid_upper_bound_rate = MerchantKconfUtil::high_atv_bid_rate_upper();
  }

  // monitor
  p_util_vals->hard_bid_upper_bound_rate_monitor = hard_bid_upper_bound_rate;
  p_util_vals->hard_bid_lower_bound_rate_monitor = hard_bid_lower_bound_rate;
  // hard bound
  if (p_util_vals->is_roas) {
    double target_roi = GetTargetRoas(p_bid_context);
    double history_roi0 = 0;
    if (bid_info_ptr) {
      // 从内存读取 or 从正排获取
      history_roi0 = bid_info_ptr->history_roi0;
      // 从 redis 读取
      if (!SPDM_useWTRemoteTables()) {
        bool is_get_redis_success = false;
        if (AdKconfUtil::enableWTAuthorCache() &&
            AdKconfUtil::enableWTAuthorCacheUnitTail()->IsOnFor(bid_info_ptr->unit_id)) {
          is_get_redis_success =
          BidStateInfoAdTableHelper::Instance()->GetWTAuthor(bid_info_ptr, bid_info_ptr->wt_author_key_id);
          // 读不到为默认值
          double history_roi0_cache = bid_info_ptr->wt_author_info_ptr->history_roi0;
          bool is_same = (std::fabs(history_roi0_cache - history_roi0) < 1e-6);
          ks::infra::PerfUtil::CountLogStash(1, "ad.ad_bid_orbit_server",
                                              "wt_author_redis_diff", "UtilValueUpdateProcessor",
                                              is_same? "true" : "false");
          if (is_get_redis_success) {
            history_roi0 = bid_info_ptr->wt_author_info_ptr->history_roi0;
          }
        }
      }
    }
    if (p_util_vals->is_storewide_roas && bid_info_ptr && history_roi0 &&
        MerchantKconfUtil::storewideHardBoundTail()->count(msg.author_id % 100)) {
      target_roi = history_roi0;
    }
    p_util_vals->hard_upper_bound_auto_roas =
        std::min(strategy_config.hard_upper_bound_auto_roas(),
                 target_roi / hard_bid_lower_bound_rate);
    p_util_vals->hard_lower_bound_auto_roas =
        std::max(strategy_config.hard_lower_bound_auto_roas(),
                 target_roi / hard_bid_upper_bound_rate);
  } else {
    p_util_vals->hard_upper_bound_auto_cpa_bid =
      std::min(strategy_config.hard_upper_bound_auto_cpa_bid(),
      GetTargetCpaBid(p_bid_context) * hard_bid_upper_bound_rate);
    p_util_vals->hard_lower_bound_auto_cpa_bid =
      std::max(strategy_config.hard_lower_bound_auto_cpa_bid(),
      GetTargetCpaBid(p_bid_context) * hard_bid_lower_bound_rate);
  }
}

inline bool UtilValueUpdateProcessor::SetAccExplore(
  const MerchantOcpmBidConfig& strategy_config, const BidStateInfoPtr& bid_info_ptr,
  OcpmBidContext* p_bid_context, ProcessUtilValues* p_util_vals) {
  // explore bid
  p_util_vals->is_acc_explore_bid = IsAccExploreBid(
    strategy_config, bid_info_ptr, p_bid_context, p_util_vals);
  if (p_util_vals->is_acc_explore_bid) {
    double acc_explore_bid_target_cost_speed = 0.0;
    if (bid_info_ptr) {
      uint64_t explore_period_ms =
        bid_info_ptr->explore_time_period * MerchantBidConfMgr::Instance().GetAccExploreBaseMs();
      uint64_t left_ms =
        explore_period_ms -
          (TimeUtil::Instance().GetTimestamp()/1000 - bid_info_ptr->explore_budget_start_time);
      if (bid_info_ptr->explore_budget_status == 1 &&
          bid_info_ptr->explore_budget > 0 &&
          left_ms > 0) {
        acc_explore_bid_target_cost_speed =
          static_cast<double>(bid_info_ptr->explore_budget) / static_cast<double>(left_ms);
      }
    }
    p_util_vals->acc_explore_bid_target_cost_speed = acc_explore_bid_target_cost_speed;
    p_util_vals->target_cost_speed = p_util_vals->acc_explore_bid_target_cost_speed;
  }
  return p_util_vals->is_acc_explore_bid;
}

inline bool UtilValueUpdateProcessor::IsAccExploreBid(
  const MerchantOcpmBidConfig& strategy_config, const BidStateInfoPtr& bid_info_ptr,
  OcpmBidContext* p_bid_context, ProcessUtilValues* p_util_vals) {
  if (!strategy_config.enable_acc_explore_bid()) {
    return false;
  }
  if (p_bid_context->group_tag() == "merchant_acc_explore") {
    return true;
  }
  if (p_util_vals && p_util_vals->is_acc_increment) {
    return true;
  }
  return false;
}

inline bool UtilValueUpdateProcessor::IsLSPTravelLiveOrderUnit(const MerchantOcpmExpConfig& exp_config,
   const MerchantOcpmMsg& msg, BidStateInfoPtr& bid_info_ptr) {
  if (!bid_info_ptr) {
    return false;
  }
  if (!SPDM_useWTRemoteTables()) {
    if (AdKconfUtil::enableWTAuthorCache() &&
        AdKconfUtil::enableWTAuthorCacheUnitTail()->IsOnFor(bid_info_ptr->unit_id)) {
      if (!BidStateInfoAdTableHelper::Instance()->GetWTAuthor(bid_info_ptr, bid_info_ptr->live_user_id)) {
        return false;
      }
    }
  }
  if (exp_config.enable_lsp_travel_live_order_bound_exp == 0) {
    return false;
  }
  int32_t author_local_life_live_cate = 0;
  // 内存 or 正排
  author_local_life_live_cate = bid_info_ptr->author_local_life_live_cate;
  // from redis
  if (!SPDM_useWTRemoteTables()) {
    if (AdKconfUtil::enableWTAuthorCache() &&
        AdKconfUtil::enableWTAuthorCacheUnitTail()->IsOnFor(bid_info_ptr->unit_id) &&
        bid_info_ptr->wt_author_info_ptr) {
      author_local_life_live_cate = bid_info_ptr->wt_author_info_ptr->author_local_life_live_cate;
    }
  }
  if (msg.account_type == "ACCOUNT_LSP" &&
      msg.account_type == "LIVE_STREAM_PROMOTE" &&
      msg.ocpc_action_type == "EVENT_ORDER_PAIED" &&
      author_local_life_live_cate == 3) {
     LOG_EVERY_N(INFO, 100000)  << "IsLSPTravelLiveOrderUnit"
                                << ", unit_id:" << msg.unit_id;
    return true;
  }
  return false;
}

inline bool UtilValueUpdateProcessor::SetColdStart(const MerchantOcpmMsg& msg,
  const MerchantOcpmBidConfig& strategy_config, BidStateInfoPtr& bid_info_ptr,
  const MerchantOcpmExpConfig& exp_config,
  OcpmBidContext* p_bid_context, ProcessUtilValues* p_util_vals) {
  auto esp_live_eop_cold_start_trans_tail = MerchantKconfUtil::espLiveEopColdStartTransTail();
  auto esp_live_hosting_cold_start_trans_tail = MerchantKconfUtil::espLiveHostingColdStartTransTail();
  // cold start
  const auto& cold_start_config = strategy_config.cold_start_config();
  // 厘 / 毫秒 = 元 / 秒
  double cold_start_cost_li =
    static_cast<double>(cold_start_config.cold_start_cost_li());
  if (p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED" &&
      p_bid_context->cpa_bid() > 0 &&
      cold_start_config.cold_start_cost_conv_num() > 0) {
    cold_start_cost_li = p_bid_context->cpa_bid() * cold_start_config.cold_start_cost_conv_num();
    // 联盟自定义订单提交冷启消耗
    if ((msg.medium_attribute == 2 || msg.medium_attribute == 4) &&
        p_util_vals->is_costcap_nobid == false &&
        exp_config.enable_universe_exp_cold_start > 0 &&
        (!IsLEZero(exp_config.universe_exp_cold_start_conv_num))) {
      cold_start_cost_li = p_bid_context->cpa_bid() * exp_config.universe_exp_cold_start_conv_num;
    }
  }
  cold_start_cost_li = cold_start_cost_li * p_util_vals->ab_exp_ratio;

  p_util_vals->is_skip_cold_start = IsColdStartInvalidExp(cold_start_cost_li, msg, p_bid_context, exp_config,
                                                          cold_start_config, p_util_vals);
  bool is_hosting_live = bid_info_ptr &&
      (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
       (SPDM_enableLSPHosting() &&
        bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE));
  bool hosting_live_exp = is_hosting_live;
  p_util_vals->is_cold_start = !p_util_vals->is_skip_cold_start &&
      IsColdStart(strategy_config, p_bid_context, hosting_live_exp, exp_config, msg,
      cold_start_cost_li, p_util_vals, bid_info_ptr);

  if (p_util_vals->is_cold_start) {
    double cs_target_cost_speed =
        cold_start_cost_li / static_cast<double>(cold_start_config.cold_start_control_time_ms());
    p_util_vals->target_cost_speed = cs_target_cost_speed;

    double hard_bid_upper_bound_rate = cold_start_config.hard_bid_upper_bound_rate();
    double hard_bid_lower_bound_rate = cold_start_config.hard_bid_lower_bound_rate();
    // 冷启动 bound
    if (p_util_vals->is_roas) {
      if (!IsLEZero(exp_config.cold_start_upper_bound_rate_exp)) {
        hard_bid_upper_bound_rate = exp_config.cold_start_upper_bound_rate_exp;
      }
      if (!IsLEZero(exp_config.cold_start_lower_bound_rate_exp)) {
        hard_bid_lower_bound_rate = exp_config.cold_start_lower_bound_rate_exp;
      }
      // target_cost_pro 单独设置
      if ((msg.account_type == "ACCOUNT_ESP" || msg.account_type == "ACCOUNT_ESP_MOBILE") &&
          msg.campaign_type == "LIVE_STREAM_PROMOTE" &&
          msg.speed_type == 1 &&
          (msg.ocpc_action_type == "AD_MERCHANT_ROAS" ||
           msg.ocpc_action_type == "AD_MERCHANT_T7_ROI") &&
          !p_util_vals->is_cbo &&
          !IsLEZero(exp_config.target_cost_pro_cold_start_lower_bound_rate)) {
        hard_bid_lower_bound_rate = exp_config.target_cost_pro_cold_start_lower_bound_rate;
      }
      // 全站推广单独设置
      if (p_util_vals->is_storewide_roas) {
        // 全站全互斥客户冷启动使用单独上下限
        if (p_bid_context->reject_type() == 1 ||
          MerchantKconfUtil::storewideColdBoundTail()->count(msg.author_id % 100)) {
          if (!IsLEZero(exp_config.storewide_allin_cold_start_upper_bound)) {
            hard_bid_upper_bound_rate = exp_config.storewide_allin_cold_start_upper_bound;
          }
          if (!IsLEZero(exp_config.storewide_allin_cold_start_lower_bound)) {
            hard_bid_lower_bound_rate = exp_config.storewide_allin_cold_start_lower_bound;
          }
        }
      }
      double target_roi = GetTargetRoas(p_bid_context);
      double history_roi0 = 0;
      if (bid_info_ptr) {
        // 从内存读取 ro 正排获取
        history_roi0 = bid_info_ptr->history_roi0;
        // 从 redis 读取
        if (!SPDM_useWTRemoteTables()) {
          bool is_get_redis_success = false;
          if (AdKconfUtil::enableWTAuthorCache() &&
              AdKconfUtil::enableWTAuthorCacheUnitTail()->IsOnFor(bid_info_ptr->unit_id)) {
            is_get_redis_success =
            BidStateInfoAdTableHelper::Instance()->GetWTAuthor(bid_info_ptr, bid_info_ptr->author_id);
            // 读取不到为默认值
            double history_roi0_cache = bid_info_ptr->wt_author_info_ptr->history_roi0;
            bool is_same = (std::fabs(history_roi0_cache - history_roi0) < 1e-6);
            ks::infra::PerfUtil::CountLogStash(1, "ad.ad_bid_orbit_server",
                                              "wt_author_redis_diff", "UtilValueUpdateProcessor",
                                              is_same? "true" : "false");
            if (is_get_redis_success) {
              history_roi0 = bid_info_ptr->wt_author_info_ptr->history_roi0;
            }
          }
        }
      }
      if (p_util_vals->is_storewide_roas && bid_info_ptr && history_roi0 &&
          MerchantKconfUtil::storewideHardBoundTail()->count(msg.author_id % 100)) {
        target_roi = history_roi0;
      }
      p_util_vals->hard_upper_bound_auto_roas =
          std::min(p_util_vals->hard_upper_bound_auto_roas,
                   target_roi / hard_bid_lower_bound_rate);
      p_util_vals->hard_lower_bound_auto_roas =
          std::max(p_util_vals->hard_lower_bound_auto_roas,
                   target_roi / hard_bid_upper_bound_rate);
    } else {
      p_util_vals->hard_lower_bound_auto_cpa_bid =
          std::max(p_util_vals->hard_lower_bound_auto_cpa_bid,
                   GetTargetCpaBid(p_bid_context) * cold_start_config.hard_bid_lower_bound_rate());
      p_util_vals->hard_upper_bound_auto_cpa_bid =
          std::min(p_util_vals->hard_upper_bound_auto_cpa_bid,
                   GetTargetCpaBid(p_bid_context) * cold_start_config.hard_bid_upper_bound_rate());
      if (esp_live_eop_cold_start_trans_tail != nullptr &&
          esp_live_hosting_cold_start_trans_tail != nullptr &&
          ((p_util_vals->is_unit_ocpm_eop_pc && esp_live_eop_cold_start_trans_tail->IsOnFor(p_bid_context->unit_id())) ||  // NOLINT
          ((msg.is_order_hosting_live) &&
          (esp_live_hosting_cold_start_trans_tail->IsOnFor(p_bid_context->campaign_id()))))) {  // NOLINT
        // target_cost_pro 单独设置
        if (!IsLEZero(exp_config.target_cost_pro_cold_start_lower_bound_rate)) {
          hard_bid_lower_bound_rate = exp_config.target_cost_pro_cold_start_lower_bound_rate;
          if (SPDM_enableEspLiveEopColdStartSplitLowerBound()) {
            if (p_util_vals->is_order_hosting_live) {
              hard_bid_lower_bound_rate = exp_config.esp_live_eop_cold_start_hosting_lower_bound;
            } else {
              hard_bid_lower_bound_rate = exp_config.esp_live_eop_cold_start_lower_bound_exp;
            }
          } else {
            hard_bid_lower_bound_rate = exp_config.esp_live_eop_cold_start_lower_bound_exp;
          }
          p_util_vals->hard_lower_bound_auto_cpa_bid =
              std::max(p_util_vals->hard_lower_bound_auto_cpa_bid,
                      GetTargetCpaBid(p_bid_context) * hard_bid_lower_bound_rate);
        }
      }
      // 联盟自定义订单提交冷启上下界
      if ((msg.medium_attribute == 2 || msg.medium_attribute == 4) &&
          p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED" &&
          p_util_vals->is_costcap_nobid == false &&
          exp_config.enable_universe_exp_cold_start > 0 &&
          (!IsLEZero(exp_config.universe_exp_cold_start_hard_bid_lower_bound_rate)) &&
          (!IsLEZero(exp_config.universe_exp_cold_start_hard_bid_upper_bound_rate)) &&
          exp_config.universe_exp_cold_start_hard_bid_lower_bound_rate
            < exp_config.universe_exp_cold_start_hard_bid_upper_bound_rate) {
        p_util_vals->hard_lower_bound_auto_cpa_bid = GetTargetCpaBid(p_bid_context) *
                                  exp_config.universe_exp_cold_start_hard_bid_lower_bound_rate;
        p_util_vals->hard_upper_bound_auto_cpa_bid = GetTargetCpaBid(p_bid_context) *
                                  exp_config.universe_exp_cold_start_hard_bid_upper_bound_rate;
        hard_bid_lower_bound_rate = exp_config.universe_exp_cold_start_hard_bid_lower_bound_rate;
        hard_bid_upper_bound_rate = exp_config.universe_exp_cold_start_hard_bid_upper_bound_rate;
        LOG_EVERY_N(INFO, 100) << "universe_cold_start_exp"
                               << " unit_id=" << p_bid_context->unit_id()
                               << " unit_tail=" << (msg.unit_id % 100)
                               << " account_id=" << p_bid_context->account_id()
                               << " campaign_id=" << p_bid_context->campaign_id()
                               << " ocpc_action_type=" << p_bid_context->ocpc_action_type()
                               << " campaign_type=" << p_bid_context->campaign_type()
                               << " group_tag=" << p_bid_context->group_tag()
                               << " group_tag_num=" << p_bid_context->group_tag_num()
                               << " speed_type=" << p_bid_context->speed_type()
                               << " cpa_bid=" << p_bid_context->cpa_bid()
                               << " target_cpa_bid=" << GetTargetCpaBid(p_bid_context)
                               << " universe_exp_cold_start_conv_num="
                               << exp_config.universe_exp_cold_start_conv_num
                               << " cold_start_cost_li=" << cold_start_cost_li
                               << " universe_exp_cold_start_hard_bid_lower_bound_rate="
                               << exp_config.universe_exp_cold_start_hard_bid_lower_bound_rate
                               << " universe_exp_cold_start_hard_bid_upper_bound_rate="
                               << exp_config.universe_exp_cold_start_hard_bid_upper_bound_rate
                               << " hard_lower_bound_auto_cpa_bid="
                               << p_util_vals->hard_lower_bound_auto_cpa_bid
                               << " hard_upper_bound_auto_cpa_bid="
                               << p_util_vals->hard_upper_bound_auto_cpa_bid;
      }
    }
    if (msg.is_roas_hosting_live && !IsLEZero(exp_config.hard_bid_lower_bound_rate_live_hosting_roas_cold)) {
      hard_bid_lower_bound_rate = exp_config.hard_bid_lower_bound_rate_live_hosting_roas_cold;
      if (msg.is_roas_hosting_live_roas_unit ||
          msg.is_roas_hosting_live_t7_roas_unit) {
        p_util_vals->hard_upper_bound_auto_roas =
            std::min(p_util_vals->hard_upper_bound_auto_roas,
                     GetTargetRoas(p_bid_context) / hard_bid_lower_bound_rate);
      } else {
        p_util_vals->hard_lower_bound_auto_cpa_bid =
            std::max(p_util_vals->hard_lower_bound_auto_cpa_bid,
                     GetTargetCpaBid(p_bid_context) * hard_bid_lower_bound_rate);
      }
    }

    // monitor
    p_util_vals->hard_bid_upper_bound_rate_monitor = hard_bid_upper_bound_rate;
    p_util_vals->hard_bid_lower_bound_rate_monitor = hard_bid_lower_bound_rate;
  }

  return p_util_vals->is_cold_start;
}

inline bool UtilValueUpdateProcessor::IsColdStart(
  const MerchantOcpmBidConfig& strategy_config,
  const OcpmBidContext* p_bid_context, bool hosting_live_exp,
  const MerchantOcpmExpConfig& exp_config, const MerchantOcpmMsg& msg,
  const double cold_start_cost_li, ProcessUtilValues* p_util_vals,
  const BidStateInfoPtr& bid_info_ptr) {
  const auto& cold_start_config = strategy_config.cold_start_config();
  double cold_start_target_rate = cold_start_config.cold_start_target_rate();
  double cold_start_cost_rate = cold_start_config.cold_start_cost_rate();
  auto esp_live_eop_cold_start_trans_tail = MerchantKconfUtil::espLiveEopColdStartTransTail();
  auto esp_live_roas_cold_start_trans_tail = MerchantKconfUtil::espLiveRoasColdStartTransTail();
  auto esp_live_t7_cold_start_trans_tail = MerchantKconfUtil::espLiveT7ColdStartTransTail();
  auto esp_live_hosting_cold_start_trans_tail = MerchantKconfUtil::espLiveHostingColdStartTransTail();
  auto esp_live_roas_hosting_cold_start_trans_tail = MerchantKconfUtil::espLiveRoasHostingColdStartTransTail();  // NOLINT
  auto esp_live_t7_hosting_cold_start_trans_tail = MerchantKconfUtil::espLiveT7HostingColdStartTransTail();
  if (p_util_vals->is_debug) {
    LOG(WARNING) << "IsColdStart:tid:" << p_util_vals->thread_id
      << " cost: " << p_bid_context->cost()
      << " target_cost: " << p_bid_context->target_cost()
      << " delivery_interval_ms: "
      << p_bid_context->last_delivery_timestamp_ms() - p_bid_context->first_delivery_timestamp_ms()
      << " persistent_ms: " << cold_start_config.cold_start_persistent_ms()
      << " control_ms: " << cold_start_config.cold_start_control_time_ms()
      << " cold_cost_li: " << cold_start_cost_li;
  }
  if (SPDM_enableColdStardTailSplit()) {
    if (esp_live_eop_cold_start_trans_tail != nullptr && esp_live_hosting_cold_start_trans_tail != nullptr &&
        ((p_util_vals->is_unit_ocpm_eop_pc && esp_live_eop_cold_start_trans_tail->IsOnFor(p_bid_context->unit_id())) ||  // NOLINT
        (msg.is_order_hosting_live &&
        (esp_live_hosting_cold_start_trans_tail->IsOnFor(p_bid_context->campaign_id()))))) {  // NOLINT
      auto cold_start_cost_thre = std::min(MerchantKconfUtil::roasColdstartSmoothCostLi(),
                                          p_bid_context->cpa_bid());
      auto cold_start_thre = MerchantKconfUtil::roasColdstartSmoothCostLi();
      cold_start_thre = exp_config.esp_live_cold_start_fixed_thre;
      cold_start_cost_thre = std::min(cold_start_thre,
                                      p_bid_context->cpa_bid() * exp_config.eop_cold_start_convert_raito);
      // 去掉投放时间判断冷启动
      if (p_bid_context->cost() < cold_start_cost_thre) {
        return true;
      } else {
        return false;
      }
    } else if (esp_live_roas_hosting_cold_start_trans_tail != nullptr &&
               esp_live_t7_hosting_cold_start_trans_tail != nullptr &&
               ((msg.is_hosting_live && p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI" &&
                 esp_live_t7_hosting_cold_start_trans_tail->IsOnFor(p_bid_context->campaign_id())) ||
                (msg.is_roas_hosting_live &&
                 esp_live_roas_hosting_cold_start_trans_tail->IsOnFor(p_bid_context->campaign_id())))) {
      if (p_bid_context->cost() < cold_start_config.hosting_live_cold_start_cost_li()) {
        return true;
      } else {
        return false;
      }
    }
  } else {
    if (esp_live_eop_cold_start_trans_tail != nullptr && esp_live_hosting_cold_start_trans_tail != nullptr &&
        ((p_util_vals->is_unit_ocpm_eop_pc && esp_live_eop_cold_start_trans_tail->IsOnFor(p_bid_context->unit_id())) ||  // NOLINT
        (msg.is_order_hosting_live &&
        (esp_live_hosting_cold_start_trans_tail->IsOnFor(p_bid_context->campaign_id()))))) {  // NOLINT
      auto cold_start_cost_thre = std::min(MerchantKconfUtil::roasColdstartSmoothCostLi(),
                                          p_bid_context->cpa_bid());
      auto cold_start_thre = MerchantKconfUtil::roasColdstartSmoothCostLi();
      cold_start_thre = exp_config.esp_live_cold_start_fixed_thre;
      cold_start_cost_thre = std::min(cold_start_thre,
                                      p_bid_context->cpa_bid() * exp_config.eop_cold_start_convert_raito);
      // 去掉投放时间判断冷启动
      if (p_bid_context->cost() < cold_start_cost_thre) {
        return true;
      } else {
        return false;
      }
    } else if ((msg.is_hosting_live && p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI") ||
                msg.is_roas_hosting_live) {
      if (p_bid_context->cost() < cold_start_config.hosting_live_cold_start_cost_li()) {
        return true;
      } else {
        return false;
      }
    }
  }
  // 全站推广只使用消耗判断
  if (p_util_vals->is_cbo_storewide_roas) {
    // 全站多素材追投计划跳过冷启动
    auto multi_creative_bid_update = MerchantKconfUtil::espLiveMultiCreativeBidUpdateTail();
    if (bid_info_ptr && bid_info_ptr->rel_type == 1 &&
        bid_info_ptr->rel_campaign_id > 0 && multi_creative_bid_update &&
        multi_creative_bid_update->IsOnFor(p_bid_context->campaign_id())) {
      return false;
    }
    if (MerchantKconfUtil::coldStartTimeTail()->count(p_bid_context->author_id() % 100)) {
      return p_bid_context->ad_open_count() < MerchantKconfUtil::storeWideColdstartMinCount();
    }
    if (exp_config.enable_storewide_coldstart_adjust ||
        p_bid_context->reject_type() == 1) {
      const auto storewideColdstartAvgcostTail = MerchantKconfUtil::storewideColdstartAvgcostTail();
      bool storewide_coldstart_avgcost = storewideColdstartAvgcostTail &&
           storewideColdstartAvgcostTail->IsOnFor(p_bid_context->campaign_id());
      double avgcost_ratio = MerchantKconfUtil::storewide_cold_start_avgcost_ratio();
      double cold_start_cost = std::max(avgcost_ratio * p_bid_context->avg_day_cost(), 100000.0);
      cold_start_cost = std::min(cold_start_cost, 1000000.0);
      if (storewide_coldstart_avgcost) {
        if (p_bid_context->cost() < cold_start_cost) {
          return true;
        } else {
          return false;
        }
      }
      if (p_bid_context->cost() < MerchantKconfUtil::storeWideColdstartSmoothCostLi()) {
        return true;
      } else {
        return false;
      }
    }
  } else if ((p_util_vals->is_unit_ocpm_roas_pc && exp_config.enable_roas_coldstart_adjust) ||
      (p_util_vals->is_unit_ocpm_roas_fanstop && exp_config.enable_roas_coldstart_adjust_fanstop) ||
      (p_util_vals->is_costcap_nobid && p_util_vals->is_roas
       && exp_config.enable_roas_coldstart_adjust_costcap) ||
       p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI") {
    auto roas_cold_start_thre = MerchantKconfUtil::roasColdstartSmoothCostLi();
    auto auto_params = MerchantKconfUtil::espLiveAutoParmasRoasColdStartLvlThre();
    const auto& config_map = auto_params->data();
    auto exp_param = config_map.exp_param();
    std::string seller_range = "unknown";
    if (ConvRatioAuthorPostData::GetInstance() != nullptr) {
      ConvRatioAuthorPostData::GetInstance()->GetSellerRange(p_bid_context->author_id(), &seller_range);
    }
    auto unit_tail = p_bid_context->unit_id() % 100;
    for (const auto& item : exp_param) {
      if (item.tail_id_begin() <= unit_tail && item.tail_id_end() >= unit_tail) {
        if (seller_range == "L0") {
          roas_cold_start_thre = item.bidding_param().roas_l0_cs();
        } else if (seller_range == "L1") {
          roas_cold_start_thre = item.bidding_param().roas_l1_cs();
        } else if (seller_range == "L2") {
          roas_cold_start_thre = item.bidding_param().roas_l2_cs();
        } else if (seller_range == "L3") {
          roas_cold_start_thre = item.bidding_param().roas_l3_cs();
        } else if (seller_range == "L4") {
          roas_cold_start_thre = item.bidding_param().roas_l4_cs();
        }
        if (seller_range != "unknown") {
          p_util_vals->auto_param_exp_id = item.exp_id();
        }
      }
    }
    if (SPDM_enableColdStardTailSplit()) {
      if (esp_live_roas_cold_start_trans_tail != nullptr &&
          esp_live_t7_cold_start_trans_tail != nullptr &&
          ((p_util_vals->is_unit_ocpm_roas_pc && esp_live_roas_cold_start_trans_tail->IsOnFor(p_bid_context->unit_id())) ||  // NOLINT
           (p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI" && esp_live_t7_cold_start_trans_tail->IsOnFor(p_bid_context->unit_id())))) {  // NOLINT
        if (p_bid_context->cost() < roas_cold_start_thre) {
          return true;
        }
      } else if (p_bid_context->cost() < roas_cold_start_thre &&
          IsInTimeInterval(p_bid_context->first_delivery_timestamp_ms(),
                          p_bid_context->last_delivery_timestamp_ms(),
                          60 * 60 * 1000)) {
        return true;
      } else {
        return false;
      }
    } else {
      if (esp_live_eop_cold_start_trans_tail != nullptr &&
          (esp_live_eop_cold_start_trans_tail->IsOnFor(p_bid_context->unit_id()))) {
        if (p_bid_context->cost() < roas_cold_start_thre) {
          return true;
        }
      } else if (p_bid_context->cost() < roas_cold_start_thre &&
          IsInTimeInterval(p_bid_context->first_delivery_timestamp_ms(),
                          p_bid_context->last_delivery_timestamp_ms(),
                          60 * 60 * 1000)) {
        return true;
      } else {
        return false;
      }
    }
  }

  if (p_bid_context->speed_type() == 2) {
    if (p_util_vals->is_roas) {
      if (p_bid_context->cost() < p_bid_context->target_cost() * cold_start_cost_rate) {
        return false;
      }
    } else {
      if (p_bid_context->cost() < p_bid_context->target_cost() * cold_start_cost_rate) {
        return false;
      }
    }
    return true;
  }
  if (!hosting_live_exp && p_bid_context->conv_num() >= cold_start_config.cold_start_conv_num()) {
    return false;
  } else if (hosting_live_exp && p_bid_context->conv_num() >=
      cold_start_config.hosting_live_cold_start_conv_num()) {
    return false;
  }

  if (p_bid_context->cost() < p_bid_context->target_cost() * cold_start_cost_rate) {
    return false;
  }

  if (!hosting_live_exp && IsInTimeInterval(p_bid_context->first_delivery_timestamp_ms(),
        p_bid_context->last_delivery_timestamp_ms(),
        cold_start_config.cold_start_persistent_ms())) {
    return true;
  } else if (hosting_live_exp && IsInTimeInterval(p_bid_context->first_delivery_timestamp_ms(),
        p_bid_context->last_delivery_timestamp_ms(),
        cold_start_config.hosting_live_cold_start_persistent_ms())) {
    return true;
  }
  if (exp_config.enable_cold_start_control_time_exp == 1 &&
      (p_bid_context->last_delivery_timestamp_ms() - p_bid_context->first_delivery_timestamp_ms() >
       cold_start_config.cold_start_control_time_ms())) {
    return false;
  }
  if (!hosting_live_exp && p_bid_context->cost() < cold_start_cost_li) {
    return true;
  } else if (hosting_live_exp &&
        p_bid_context->cost() < cold_start_config.hosting_live_cold_start_cost_li()) {
    return true;
  }
  return false;
}

inline void UtilValueUpdateProcessor::SetPacingMethod(
  const MerchantOcpmMsg& msg, ProcessUtilValues* p_util_vals) {
  if (p_util_vals->is_roas) {
    p_util_vals->pacing_method_type = PacingMethodType::STANDARD_MULTIPLICATION_PID_METHOD;
  } else {
    p_util_vals->pacing_method_type = PacingMethodType::STANDARD_MULTIPLICATION_PID_METHOD;
  }
}

inline void UtilValueUpdateProcessor::SetPidParams(StrategySession* p_session) {
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;
  const MerchantOcpmMsg& msg = *(p_session->p_msg);

  p_util_vals->p_weight = strategy_config.pid_adjust_config().p_weight();
  p_util_vals->i_weight = strategy_config.pid_adjust_config().i_weight();
  p_util_vals->d_weight = strategy_config.pid_adjust_config().d_weight();
  // explore
  if (p_util_vals->enable_cost_prior_algo) {
    bool get_suc;
    auto ocpx_unit_tail_pid_params = MerchantBidConfMgr::Instance().GetOcpxUnitTailPidParams(
    p_bid_context->ocpc_action_type(), p_bid_context->unit_id(), &get_suc);
    if (get_suc != false) {
      p_util_vals->p_weight = ocpx_unit_tail_pid_params.k_p;
      p_util_vals->i_weight = ocpx_unit_tail_pid_params.k_i;
      p_util_vals->d_weight = ocpx_unit_tail_pid_params.k_d;
    }
  }
  if (p_util_vals->is_fanstop) {  // 移动端
    if (p_util_vals->is_roas) {
      // [fanstopOcpmConfig]
      if (p_util_vals->is_fanstop_ocpm && IsGEZero(exp_config.fanstop_ocpm_roas_p_weight_exp)) {
        p_util_vals->p_weight = exp_config.fanstop_ocpm_roas_p_weight_exp;
      }
      if (p_util_vals->is_fanstop_ocpm && IsGEZero(exp_config.fanstop_ocpm_roas_i_weight_exp)) {
        p_util_vals->i_weight = exp_config.fanstop_ocpm_roas_i_weight_exp;
      }
      if (p_util_vals->is_fanstop_ocpm && IsGEZero(exp_config.fanstop_ocpm_roas_d_weight_exp)) {
        p_util_vals->d_weight = exp_config.fanstop_ocpm_roas_d_weight_exp;
      }
    } else {
      // [fanstopOcpcConfig] 即将下线
      p_util_vals->p_weight = exp_config.exp_k_p;
      p_util_vals->i_weight = exp_config.exp_k_i;
      p_util_vals->d_weight = exp_config.exp_k_d;
      // [fanstopOcpmConfig]
      if (p_util_vals->is_fanstop_ocpm && IsGEZero(exp_config.fanstop_ocpm_cpa_p_weight_exp)) {
        p_util_vals->p_weight = exp_config.fanstop_ocpm_cpa_p_weight_exp;
      }
      if (p_util_vals->is_fanstop_ocpm && IsGEZero(exp_config.fanstop_ocpm_cpa_i_weight_exp)) {
        p_util_vals->i_weight = exp_config.fanstop_ocpm_cpa_i_weight_exp;
      }
      if (p_util_vals->is_fanstop_ocpm && IsGEZero(exp_config.fanstop_ocpm_cpa_d_weight_exp)) {
        p_util_vals->d_weight = exp_config.fanstop_ocpm_cpa_d_weight_exp;
      }
    }
  } else {  // PC 端
    if (p_util_vals->is_roas) {
      if (IsGEZero(exp_config.pc_roas_p_weight_exp)) {
        p_util_vals->p_weight = exp_config.pc_roas_p_weight_exp;
      }
      if (IsGEZero(exp_config.pc_roas_i_weight_exp)) {
        p_util_vals->i_weight = exp_config.pc_roas_i_weight_exp;
      }
      if (IsGEZero(exp_config.pc_roas_d_weight_exp)) {
        p_util_vals->d_weight = exp_config.pc_roas_d_weight_exp;
      }
    } else {
      if (IsGEZero(exp_config.pc_order_p_weight_exp)) {
        p_util_vals->p_weight = exp_config.pc_order_p_weight_exp;
      }
      if (IsGEZero(exp_config.pc_order_i_weight_exp)) {
        p_util_vals->i_weight = exp_config.pc_order_i_weight_exp;
      }
      if (IsGEZero(exp_config.pc_order_d_weight_exp)) {
        p_util_vals->d_weight = exp_config.pc_order_d_weight_exp;
      }
    }
    // roas 单步调价实验
    if (PacingType::ROAS_SINGLE_STEP == p_util_vals->pacing_type) {
      p_util_vals->p_weight = strategy_config.roas_pid_adjust_config().p_weight();
      p_util_vals->i_weight = strategy_config.roas_pid_adjust_config().i_weight();
      p_util_vals->d_weight = strategy_config.roas_pid_adjust_config().d_weight();
    }
    // 全站推广调价实验单独设置
    if (p_util_vals->is_storewide_roas &&
        PacingType::ROAS_SINGLE_STEP == p_util_vals->pacing_type) {
      p_util_vals->p_weight = strategy_config.storewide_pid_adjust_config().p_weight();
      p_util_vals->i_weight = strategy_config.storewide_pid_adjust_config().i_weight();
      p_util_vals->d_weight = strategy_config.storewide_pid_adjust_config().d_weight();
    }

    if (msg.is_roas_hosting_live) {
      if (!IsLEZero(exp_config.live_hosting_roas_p_weight_exp)) {
        p_util_vals->p_weight = exp_config.live_hosting_roas_p_weight_exp;
      }
      if (!IsLEZero(exp_config.live_hosting_roas_i_weight_exp)) {
        p_util_vals->i_weight = exp_config.live_hosting_roas_i_weight_exp;
      }
      if (!IsLEZero(exp_config.live_hosting_roas_d_weight_exp)) {
        p_util_vals->d_weight = exp_config.live_hosting_roas_d_weight_exp;
      }
    }
  }
}

static inline double GetRoasHierarchicalStepRatio(double price_ratio) {
  if (price_ratio <= 1.0 || !MerchantKconfUtil::enableRoasHierarchicalHardBound()) {
    return 0.0;
  }
  // key: 超成本比例如 1.2, value: 调价步长系数,如 0.5
  // 则对应 roas 调价 lower 步长变为 0.8 * 0.5, 压价速度是原来 2 倍
  auto bound_conf = MerchantKconfUtil::roasHierarchicalStepRatioConf();
  std::vector<std::tuple<double, double>> vals;
  double tmp_key;
  for (const auto &pair : *bound_conf) {
    if (absl::SimpleAtod(pair.first, &tmp_key)) {
      vals.push_back(std::make_tuple(tmp_key, pair.second));
    }
  }
  std::sort(vals.begin(), vals.end(),
            [](std::tuple<double, double> const& m,
               std::tuple<double, double> const& n)
            { return std::get<0>(m) < std::get<0>(n); });
  for (int i = 0; i < vals.size(); i++) {
    if (price_ratio < std::get<0>(vals[i]) && std::get<1>(vals[i]) > 0) {
      return std::get<1>(vals[i]);
    }
  }
  return 0.0;
}

void UtilValueUpdateProcessor::AdjustRateBound(const MerchantOcpmExpConfig& exp_config,
                                               ProcessUtilValues* p_util_vals,
                                               OcpmBidContext* p_bid_context,
                                               const MerchantOcpmMsg& msg) {
  if (p_util_vals->pacing_type == PacingType::LINEAR_PACING_WITH_DECAY_TIMERANGE ||
      p_util_vals->pacing_type == PacingType::LINEAR_PACING_DECAY_IERROR ||
      p_util_vals->pacing_type == PacingType::LINEAR_PACING_COMMON) {
    p_util_vals->adjust_rate_lower_bound = 0.9;
    p_util_vals->adjust_rate_upper_bound = 1.1;
    // fanstop 单次调价调参实验
    if (p_util_vals->is_fanstop) {
      if (p_util_vals->is_roas) {
        // [fanstopOcpmConfig]
        if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_roas_adjust_rate_lower_bound)) {
          p_util_vals->adjust_rate_lower_bound = exp_config.fanstop_ocpm_roas_adjust_rate_lower_bound;
        }
        if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_roas_adjust_rate_upper_bound)) {
          p_util_vals->adjust_rate_upper_bound = exp_config.fanstop_ocpm_roas_adjust_rate_upper_bound;
        }
      } else {
        // [fanstopOcpmConfig]
        if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_cpa_adjust_rate_lower_bound)) {
          p_util_vals->adjust_rate_lower_bound = exp_config.fanstop_ocpm_cpa_adjust_rate_lower_bound;
        }
        if (p_util_vals->is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_cpa_adjust_rate_upper_bound)) {
          p_util_vals->adjust_rate_upper_bound = exp_config.fanstop_ocpm_cpa_adjust_rate_upper_bound;
        }
      }
    }
  }

  // PC 调价步长
  if (!p_util_vals->is_fanstop && !p_util_vals->is_roas) {
    if (!IsLEZero(exp_config.adjust_rate_lower_bound_order_exp)) {
      p_util_vals->adjust_rate_lower_bound = exp_config.adjust_rate_lower_bound_order_exp;
    }
    if (!IsLEZero(exp_config.adjust_rate_upper_bound_order_exp)) {
      p_util_vals->adjust_rate_upper_bound = exp_config.adjust_rate_upper_bound_order_exp;
    }
  } else if (!p_util_vals->is_fanstop && p_util_vals->is_roas) {
    if (!IsLEZero(exp_config.adjust_rate_lower_bound_roas_exp)) {
      p_util_vals->adjust_rate_lower_bound = exp_config.adjust_rate_lower_bound_roas_exp;
    }
    if (!IsLEZero(exp_config.adjust_rate_upper_bound_roas_exp)) {
      p_util_vals->adjust_rate_upper_bound = exp_config.adjust_rate_upper_bound_roas_exp;
    }
    // 全店 roi 订单单独设置步长
    if (p_util_vals->is_storewide_roas) {
      if (!IsLEZero(exp_config.storewide_roas_adjust_rate_lower_bound)) {
        p_util_vals->adjust_rate_lower_bound = exp_config.storewide_roas_adjust_rate_lower_bound;
      }
      if (!IsLEZero(exp_config.storewide_roas_adjust_rate_upper_bound)) {
        p_util_vals->adjust_rate_upper_bound = exp_config.storewide_roas_adjust_rate_upper_bound;
      }
      // 全互斥客户单独设置步长
      if (p_bid_context->reject_type() == 1) {
        if (!IsLEZero(exp_config.storewide_allin_adjust_rate_lower_bound)) {
          p_util_vals->adjust_rate_lower_bound = exp_config.storewide_allin_adjust_rate_lower_bound;
        }
        if (!IsLEZero(exp_config.storewide_allin_adjust_rate_upper_bound)) {
          p_util_vals->adjust_rate_upper_bound = exp_config.storewide_allin_adjust_rate_upper_bound;
        }
      }
      // 超成本时使用分级步长
      bool enable_store_wide = p_util_vals->is_cbo_storewide_roas &&
           (exp_config.enable_storewide_hierarchical_bound ||
           p_bid_context->reject_type() == 1);
      bool enable_non_store_wide = !p_util_vals->is_storewide_roas &&
             exp_config.enable_roas_hierarchical_bound;
      if (enable_store_wide || enable_non_store_wide) {
        if (p_bid_context->target_cost() > 0) {
          double price_ratio = 1.0 * p_bid_context->cost() / p_bid_context->target_cost();
          double step_ratio = GetRoasHierarchicalStepRatio(price_ratio);
          if (step_ratio > 0) {
            p_util_vals->adjust_rate_lower_bound *= step_ratio;
          }
        }
      }
    }
  } else if (!p_util_vals->is_fanstop && p_util_vals->is_high_quality_follow) {
    if (!IsLEZero(exp_config.adjust_rate_lower_bound_follow_exp)) {
      p_util_vals->adjust_rate_lower_bound = exp_config.adjust_rate_lower_bound_follow_exp;
    }
  }
  // 托管表达 roas 计划单独设置步长，防止暗投明投不一样
  if (msg.is_roas_hosting_live && !IsLEZero(exp_config.adjust_rate_lower_bound_live_hosting_roas_exp)) {
    p_util_vals->adjust_rate_lower_bound = exp_config.adjust_rate_lower_bound_live_hosting_roas_exp;
  }
  if (msg.is_roas_hosting_live && !IsLEZero(exp_config.adjust_rate_upper_bound_live_hosting_roas_exp)) {
    p_util_vals->adjust_rate_upper_bound = exp_config.adjust_rate_upper_bound_live_hosting_roas_exp;
  }

  // 高消耗欠成本单元跨天调价步长
  if (!p_util_vals->is_fanstop && !p_util_vals->is_roas && exp_config.enable_cross_day_adjust_rate_bound &&
      p_bid_context->yesterday_cost() >= exp_config.cross_day_cost_threshold &&
      p_bid_context->yesterday_cost() <= p_bid_context->yesterday_target_cost() *
      exp_config.yesterday_ratio) {
    if (!IsLEZero(exp_config.cross_day_order_lower_bound)) {
      p_util_vals->adjust_rate_lower_bound = exp_config.cross_day_order_lower_bound;
    }
  } else if (!p_util_vals->is_fanstop && p_util_vals->is_roas && !p_util_vals->is_storewide_roas &&
             exp_config.enable_cross_day_adjust_rate_bound_roas &&
             p_bid_context->yesterday_cost() >= exp_config.cross_day_cost_threshold &&
             p_bid_context->yesterday_cost() <= p_bid_context->yesterday_target_cost() *
             exp_config.yesterday_ratio) {
    if (!IsLEZero(exp_config.cross_day_order_lower_bound_roas)) {
      p_util_vals->adjust_rate_lower_bound = exp_config.cross_day_order_lower_bound_roas;
    }
  }
  if (p_util_vals->is_flow_control && p_util_vals->is_roas) {
    p_util_vals->adjust_rate_lower_bound = exp_config.roas_flow_control_adjust_lower_bound;
    p_util_vals->adjust_rate_upper_bound = exp_config.roas_flow_control_adjust_upper_bound;
  } else if (p_util_vals->is_flow_control) {
    p_util_vals->adjust_rate_lower_bound = exp_config.eop_flow_control_adjust_lower_bound;
    p_util_vals->adjust_rate_upper_bound = exp_config.eop_flow_control_adjust_upper_bound;
  }

  if (p_util_vals->is_cold_start && !IsLEZero(exp_config.adjust_rate_lower_bound_order_cold_exp)) {
    p_util_vals->adjust_rate_lower_bound = exp_config.adjust_rate_lower_bound_order_cold_exp;
  }
  if (p_util_vals->is_cold_start && !IsLEZero(exp_config.adjust_rate_upper_bound_order_cold_exp)) {
    p_util_vals->adjust_rate_upper_bound = exp_config.adjust_rate_upper_bound_order_cold_exp;
  }

  if (p_util_vals->is_roas && p_util_vals->is_cold_start &&
      !IsLEZero(exp_config.adjust_rate_lower_bound_exp)) {
    p_util_vals->adjust_rate_lower_bound = exp_config.adjust_rate_lower_bound_exp;
  }
  if (p_util_vals->is_roas && p_util_vals->is_cold_start &&
      !IsLEZero(exp_config.adjust_rate_upper_bound_exp)) {
    p_util_vals->adjust_rate_upper_bound = exp_config.adjust_rate_upper_bound_exp;
  }

  if (p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED" &&
      p_bid_context->target_cost() > p_bid_context->cost() &&
      !IsLEZero(exp_config.order_owe_adjust_rate_lower_bound)) {
    p_util_vals->adjust_rate_lower_bound = exp_config.order_owe_adjust_rate_lower_bound;
  }
}

void UtilValueUpdateProcessor::SetCostcapRoasBoundUtil(const MerchantOcpmMsg& msg,
  const MerchantOcpmExpConfig& exp_config, ProcessUtilValues* p_util_vals,
  const BidStateInfoPtr& bid_info_ptr, OcpmBidContext* p_bid_context,
  const std::string& campaign_type_str,
  double* hard_bid_upper_bound_rate, double* hard_bid_lower_bound_rate) {
  // 准入 异常请求处理
  if (!bid_info_ptr) {
    return;
  }
  // 准入 costcap
  if (!p_util_vals->is_costcap_nobid) {
    return;
  }
  // 准入 roas
  if (!p_util_vals->is_roas) {
    return;
  }
  // 通用 bound
  if (exp_config.enable_costcap_campaign_str.find(campaign_type_str) != std::string::npos) {
    if (!IsLEZero(exp_config.costcap_hard_roi_ratio_upper_bound_rate)) {
      *hard_bid_upper_bound_rate = exp_config.costcap_hard_roi_ratio_upper_bound_rate;
    }
    if (!IsLEZero(exp_config.costcap_hard_roi_ratio_lower_bound_rate)) {
      *hard_bid_lower_bound_rate = exp_config.costcap_hard_roi_ratio_lower_bound_rate;
    }
  }
  // 移动端 ROAS 高出价订单 单独设置冷启动 bound
  double roi_ratio = GetTargetRoas(p_bid_context);
  double fanstop_roas_cutoff_guardbid_roi_min_ = MerchantKconfUtil::FanstopRoasCutoffGuardbidRoiMin();
  double fanstop_roas_cutoff_guardbid_roi_value_ = MerchantKconfUtil::FanstopRoasCutoffGuardbidRoiValue();
  if (campaign_type_str == "FANSTOP_LIVE" && roi_ratio < fanstop_roas_cutoff_guardbid_roi_min_ &&
      AdmitFanstopRoasHighRoiColdStart(msg, bid_info_ptr, exp_config, p_bid_context) &&
      !IsLEZero(exp_config.enable_costcap_roas_cutoff_autobid)) {
    if (!IsLEZero(fanstop_roas_cutoff_guardbid_roi_value_)) {
      *hard_bid_upper_bound_rate = roi_ratio / fanstop_roas_cutoff_guardbid_roi_value_;
    }
  }
  LOG_EVERY_N(INFO, 10000)  << "SetCostcapRoasBoundUtil_zhangtuchao:"
                            << ",unit_id:" << msg.unit_id
                            << ",account_type:" << msg.account_type
                            << ",campaign_type:" << msg.campaign_type
                            << ",ocpc_action_type:" << msg.ocpc_action_type
                            << ",bid_strategy:" << msg.bid_strategy
                            << ",cpa_bid:" << bid_info_ptr->cpa_bid
                            << ",campaign_type_str:" << campaign_type_str
                            << ",roi_ratio:" << roi_ratio
                            << ",fanstop_roas_cutoff_guardbid_roi_min_:"
                            << fanstop_roas_cutoff_guardbid_roi_min_
                            << ",fanstop_roas_cutoff_guardbid_roi_value_:"
                            << fanstop_roas_cutoff_guardbid_roi_value_
                            << ",hard_bid_upper_bound_rate:" << *hard_bid_upper_bound_rate
                            << ",hard_bid_lower_bound_rate:" << *hard_bid_lower_bound_rate
                            << std::endl;
}

bool UtilValueUpdateProcessor::AdmitFanstopRoasHighRoiColdStart(const MerchantOcpmMsg& msg,
  const BidStateInfoPtr& bid_info_ptr, const MerchantOcpmExpConfig& exp_config,
  OcpmBidContext* p_bid_context) {
  // 准入 异常处理
  if (!bid_info_ptr) {
    return false;
  }

  // 时长利用率 <= fanstop_costcap_roas_cold_start_time_ratio
  int64_t now_ms = TimeUtil::Instance().GetTimestamp() / 1000;
  int64_t begin_time_ms = bid_info_ptr->begin_time;
  int64_t end_time_ms = bid_info_ptr->end_time;
  double fanstop_costcap_roas_cold_start_time_ratio = exp_config.fanstop_costcap_roas_cold_start_time_ratio;
  if (now_ms >= begin_time_ms && now_ms <= end_time_ms &&
      !IsLEZero(fanstop_costcap_roas_cold_start_time_ratio) &&
      now_ms - begin_time_ms <= fanstop_costcap_roas_cold_start_time_ratio * (end_time_ms - begin_time_ms)) {
    return true;
  }

  // 消耗利用率 <= fanstop_costcap_roas_cold_start_time_ratio
  double cost = p_bid_context->cost();
  int64_t budget = bid_info_ptr->budget;
  double fanstop_costcap_roas_cold_start_cost_ratio = exp_config.fanstop_costcap_roas_cold_start_cost_ratio;
  if (!IsLEZero(fanstop_costcap_roas_cold_start_cost_ratio) &&
      cost <= fanstop_costcap_roas_cold_start_cost_ratio * budget) {
    return true;
  }
  LOG_EVERY_N(INFO, 10000)  << "AdmitFanstopRoasHighRoiColdStart_zhangtuchao:"
                            << ",unit_id:" << msg.unit_id
                            << ",account_type:" << msg.account_type
                            << ",campaign_type:" << msg.campaign_type
                            << ",ocpc_action_type:" << msg.ocpc_action_type
                            << ",bid_strategy:" << msg.bid_strategy
                            << ",cpa_bid:" << bid_info_ptr->cpa_bid
                            << ",now_ms:" << now_ms
                            << ",begin_time_ms:" << begin_time_ms
                            << ",end_time_ms:" << end_time_ms
                            << ",fanstop_costcap_roas_cold_start_time_ratio:"
                            << fanstop_costcap_roas_cold_start_time_ratio
                            << ",cost:" << cost
                            << ",budget:" << budget
                            << ",fanstop_costcap_roas_cold_start_cost_ratio:"
                            << fanstop_costcap_roas_cold_start_cost_ratio
                            << std::endl;
  return false;
}

void UtilValueUpdateProcessor::UniverseMonitorLog(StrategySession* p_session) {
  if (p_session == nullptr) {
    return;
  }
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  if (p_bid_context == nullptr || p_util_vals == nullptr || bid_info_ptr == nullptr) {
    return;
  }

  if (!IsUniverseMonitorLog(msg)) {
    return;
  }
  LOG_EVERY_N(INFO, 1) << "UtilValueUpdateProcessor_Process"
                        << " unit_id=" << p_bid_context->unit_id()
                        << " unit_tail=" << (msg.unit_id % 100)
                        << " account_id=" << p_bid_context->account_id()
                        << " campaign_id=" << p_bid_context->campaign_id()
                        << " ocpc_action_type=" << p_bid_context->ocpc_action_type()
                        << " campaign_type=" << p_bid_context->campaign_type()
                        << " group_tag=" << p_bid_context->group_tag()
                        << " group_tag_num=" << p_bid_context->group_tag_num()
                        << " speed_type=" << p_bid_context->speed_type()
                        << " total_cost=" << p_bid_context->cost()
                        << " target_cost=" << p_bid_context->target_cost()
                        << " ad_short_type=" << p_util_vals->ad_short_type
                        << " pacing_method_type=" << p_util_vals->pacing_method_type
                        << " p_weight=" << p_util_vals->p_weight
                        << " i_weight=" << p_util_vals->i_weight
                        << " d_weight=" << p_util_vals->d_weight
                        << " is_costcap_nobid=" << p_util_vals->is_costcap_nobid
                        << " hard_bid_upper_bound_rate_exp_all="
                        << exp_config.hard_bid_upper_bound_rate_exp_all
                        << " hard_bid_lower_bound_rate_exp_all="
                        << exp_config.hard_bid_lower_bound_rate_exp_all
                        << " hard_bid_upper_bound_rate_monitor="
                        << p_util_vals->hard_bid_upper_bound_rate_monitor
                        << " hard_bid_lower_bound_rate_monitor="
                        << p_util_vals->hard_bid_lower_bound_rate_monitor
                        << " hard_upper_bound_auto_cpa_bid=" << p_util_vals->hard_upper_bound_auto_cpa_bid
                        << " hard_lower_bound_auto_cpa_bid=" << p_util_vals->hard_lower_bound_auto_cpa_bid
                        << " is_acc_explore_bid=" << p_util_vals->is_acc_explore_bid
                        << " is_cold_start=" << p_util_vals->is_cold_start
                        << " left_budget_thre_upper_bound=" << exp_config.left_budget_thre_upper_bound
                        << " left_budget=" << bid_info_ptr->left_budget
                        << " is_ad_open=" << p_util_vals->is_ad_open
                        << " is_apply_adjust=" << p_util_vals->is_apply_adjust
                        << " is_update_adjust=" << p_util_vals->is_update_adjust
                        << " ad_status_tag=" << p_util_vals->ad_status_tag
                        << " last_ad_open_timestamp=" << p_bid_context->last_ad_open_timestamp()
                        << " adjust_rate_upper_bound=" << p_util_vals->adjust_rate_upper_bound
                        << " adjust_rate_lower_bound=" << p_util_vals->adjust_rate_lower_bound;
}

bool UtilValueUpdateProcessor::SetBudgetBound(StrategySession* p_session) {
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;

  if (bid_info_ptr == nullptr || p_bid_context == nullptr || p_util_vals == nullptr) {
    return false;
  }

  const auto& cold_start_config = strategy_config.cold_start_config();

  double cost_li = static_cast<double>(cold_start_config.cold_start_cost_li());
  if (p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED" && p_bid_context->cpa_bid() > 0 &&
      cold_start_config.cold_start_cost_conv_num() > 0) {
    cost_li = std::min(cost_li, p_bid_context->cpa_bid() * cold_start_config.cold_start_cost_conv_num());
  }

  if (bid_info_ptr->left_budget < cost_li && !IsLEZero(exp_config.left_budget_thre_upper_bound)) {
    p_util_vals->hard_upper_bound_auto_cpa_bid =
        std::min(p_util_vals->hard_upper_bound_auto_cpa_bid,
                 GetTargetCpaBid(p_bid_context) * exp_config.left_budget_thre_upper_bound);

    p_util_vals->hard_bid_upper_bound_rate_monitor = exp_config.left_budget_thre_upper_bound;
  }
  return true;
}

bool UtilValueUpdateProcessor::SetLiveHosting(StrategySession* p_session) {
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  if (msg.is_hosting_live) {
    p_util_vals->is_live_hosting = true;
  }
  p_util_vals->is_roas_hosting_live_order_unit = msg.is_roas_hosting_live_order_unit;
  p_util_vals->is_roas_hosting_live_roas_unit = msg.is_roas_hosting_live_roas_unit;
  p_util_vals->is_roas_hosting_live = msg.is_roas_hosting_live;
  p_util_vals->is_order_hosting_live = msg.is_order_hosting_live;
  return true;
}

}  // namespace bid_server
}  // namespace ks
