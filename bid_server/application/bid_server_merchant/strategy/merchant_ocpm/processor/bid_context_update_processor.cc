// Authors: <AUTHORS>
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/bid_context_update_processor.h"
#include <algorithm>
#include <map>
#include <string>
#include <vector>
#include <set>
#include <unordered_map>
#include "teams/ad/bid_server/application/bid_server_merchant/util/util.h"
#include "teams/ad/bid_server/application/bid_server_merchant/data_mgr/merchant_data_mgr.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/merchant_ocpm_common.h"
#include "teams/ad/bid_server/application/bid_server_merchant/manager/merchant_bid_conf_mgr.h"
#include "teams/ad/bid_server/framework/index/bid_index_manager.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/task/ocpm_target_cost_task.h"
#include "teams/ad/bid_server/application/bid_server_merchant/util/bid_no_diff_redis_save.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/task/page_bid_cali_task.h"
#include "teams/ad/bid_server/base/strategy/lowest_cost/cost_cap_util.h"
#include "teams/ad/bid_server/framework/index/default/remote_grid_index.h"

DECLARE_bool(enable_bid_no_diff_switch);
namespace ks {
namespace bid_server {

const int REDIS_STORE_TIME_SEC = 7 * 24 * 60 * 60;

thread_local std::unordered_map<int64_t, int64_t> BidContextUpdateProcessor::unit_version_info_;

void BidContextUpdateProcessor::Process(StrategySession* p_session) {
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  LOG_EVERY_N(INFO, 100000) << "BidContextUpdateProcessor::Process: unit_id: " << msg.unit_id;
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;

  // TODO(zhaizhiqiang): reset 无 diff
  if (!FLAGS_enable_bid_no_diff_switch) {
    CheckContextReset(p_session);
  }

  SetContextBaseInfo(msg, bid_info_ptr, p_bid_context, p_util_vals);
  bool is_qiandu = msg.ocpc_action_type == "AD_LIVE_AUDIENCE" || msg.ocpc_action_type == "AD_MERCHANT_FOLLOW";
  bool is_storewide_switch = (p_bid_context->scene_oriented_type() == 21 ||
                              p_bid_context->scene_oriented_type() == 30) && !is_qiandu;
  if (SPDM_enableAggrMerchantOcpmMsg()) {
    is_storewide_switch = msg.is_storewide_switch;
  }
  if (is_storewide_switch && MerchantKconfUtil::enableStorewideMonitor()) {
    // 只在灰度环境中输出日志
    LOG_EVERY_N(INFO, 10000) << "debug storewide event msg."
                            << ", ocpx bid context: " << p_bid_context->ocpc_action_type()
                            << ", ocpx msg: " << msg.ocpc_action_type
                            << ", author id: " << msg.author_id
                            << ", campaign id: " << msg.campaign_id
                            << ", unit id: " <<msg.unit_id
                            << ", cost: " << msg.cost
                            << ", target_cost: " << msg.target_cost
                             << ", gmv: " << msg.gmv
                            << ", target_gmv: " <<msg.target_gmv;
  }
  // 解析 author 的转化概率时间分桶 一天只需解析一次
  kuaishou::ad::tables::WTAuthor wt_author_data;
  if (!SPDM_useWTRemoteTables() && BidIndexManager::Instance().GetIndex() != nullptr) {
    BidIndexManager::Instance().GetIndex()->GetWTAuthor(p_bid_context->author_id(), &wt_author_data);
  }
  auto* pre_bucket_ratio_list_ptr = &wt_author_data.parse_fields().pre_target_cost_bucket_ratio();
  auto* index_bucket_string_list_ptr = &wt_author_data.parse_fields().index_bucket_string();
  bool get_remote_wt_author = false;
  kuaishou::ad::tables::WTAuthor* wt_author_data_remote = nullptr;
  if (SPDM_useWTRemoteTables()) {
    if (RemoteGridIndex::WTAuthorInfo(p_bid_context->author_id(), wt_author_data_remote) &&
      wt_author_data_remote != nullptr) {
      pre_bucket_ratio_list_ptr =
          &wt_author_data_remote->parse_fields().pre_target_cost_bucket_ratio();
      index_bucket_string_list_ptr =
          &wt_author_data_remote->parse_fields().index_bucket_string();
    }
  }
  if (p_bid_context->pred_conv_not_empty() == false && index_bucket_string_list_ptr->size() > 0
  && pre_bucket_ratio_list_ptr->size() > 0) {
    int64 index = 0;
    double ratio = 0.0;
    for (int i = 0; i < 60; i++) {
      if (p_bid_context->author_conv_bucket_size() < 60) {
        p_bid_context->add_author_conv_bucket(0.0);
      }
    }
    if (index_bucket_string_list_ptr->size() == pre_bucket_ratio_list_ptr->size()) {
      for (int i = 0; i < index_bucket_string_list_ptr->size(); i++) {
        index = (*index_bucket_string_list_ptr)[i];
        ratio = (*pre_bucket_ratio_list_ptr)[i];
        if (index < p_bid_context->author_conv_bucket_size()) {
          p_bid_context->mutable_author_conv_bucket()->at(index) = ratio;
        }
      }
    }
    p_bid_context->set_pred_conv_not_empty(true);
  }
  if (p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS") {
    (*p_bid_context->mutable_campaign_units())[static_cast<int64>(msg.unit_id)] = true;
  }
  if (p_bid_context->ocpc_action_type() == "AD_MERCHANT_ROAS"
      || p_bid_context->ocpc_action_type() == "AD_FANS_TOP_ROI"
      || p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI"
      || p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS" || is_storewide_switch) {
    if (p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS" || is_storewide_switch) {
      p_util_vals->is_storewide_roas = true;
      if (IsCboStoreWide(bid_info_ptr)) {
        p_util_vals->is_cbo_storewide_roas = true;
      }
      // 设置全站推广全互斥字段
      if (msg.is_storewide_incompatible) {
        p_bid_context->set_reject_type(1);
      } else {
        p_bid_context->set_reject_type(2);
      }
    }
  }
  if (IsCbo(bid_info_ptr) ||
      msg.is_storewide_with_order) {
    p_util_vals->is_cbo = true;
  }
  if (p_bid_context->speed_type() == 5) {
    p_util_vals->is_no_bid = true;
  }
  if (p_bid_context->campaign_type() == "LIVE_STREAM_PROMOTE" &&
      p_bid_context->ocpc_action_type() == "AD_MERCHANT_ROAS" &&
      p_bid_context->speed_type() == 1 &&
      !p_util_vals->is_cbo) {
    p_util_vals->is_unit_ocpm_roas_pc = true;
  }
  if (p_bid_context->campaign_type() == "LIVE_STREAM_PROMOTE" &&
      p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED" &&
      p_bid_context->speed_type() == 1 &&
      !p_util_vals->is_cbo) {
    p_util_vals->is_unit_ocpm_eop_pc = true;
  }
  if (IsFanstopLiveOcpm(p_bid_context->campaign_type(), msg.bid_strategy) &&
      p_bid_context->ocpc_action_type() == "AD_FANS_TOP_ROI" &&
      !p_util_vals->is_cbo) {
    p_util_vals->is_unit_ocpm_roas_fanstop = true;
  }
  if (IsFanstopLiveOcpm(p_bid_context->campaign_type(), msg.bid_strategy) &&
      p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED" &&
      !p_util_vals->is_cbo) {
    p_util_vals->is_unit_ocpm_eop_fanstop = true;
  }
  // cpa_bid & roi_ratio
  SetTargetValue(p_session);

  if (!FLAGS_enable_bid_no_diff_switch) {
    if (p_util_vals->is_skip_update) {
      return;
    }
  }
  // bid context
  UpdateGeneralBidContext(msg, p_bid_context, *p_util_vals, exp_config);
  UpdateBidContext(msg, strategy_config, exp_config, p_bid_context, *p_util_vals);


  // 蓄水池存储
  if (exp_config.enable_ocpm_cache_target_cost == 1 && OcpmTargetCostTask::Instance()->Admit(p_session)) {
    OcpmTargetCostTask::Instance()->DealTargetCost(p_bid_context, strategy_config.ocpm_target_cost_config(),
                                                    p_util_vals, msg.target_cost);
  }

  p_bid_context->set_last_update_context_timestamp(TimeUtil::Instance().GetTimestamp());
  CheckSyncContextRemote(strategy_config, p_bid_context, msg);
  if (p_util_vals->is_debug) {LOG(WARNING)
    << "BidContextUpdateProcessor::Process:tid:" << p_util_vals->thread_id;}

  // 联盟监控日志
  UniverseMonitorLog(msg, exp_config, p_util_vals, p_bid_context);
  // 全站增量成本监控
  if (p_bid_context->ocpc_action_type()== "AD_STOREWIDE_ROAS" &&
      (msg.page_id == 100022354 || msg.is_store_wide_roi_reco_conv && msg.storewide_inc_type == 1)) {
    ks::infra::PerfUtil::IntervalLogStash(msg.cost, "ad.bid-server-merchant",
                        "uplift", msg.roi_traffic_source, "cost");
    ks::infra::PerfUtil::IntervalLogStash(msg.target_cost, "ad.bid-server-merchant",
                        "uplift", msg.roi_traffic_source, "target_cost");
  }
  // perf
  if (SPDM_enableMerchantOcpmMsgPerf()
      && ad_base::AdRandom::GetInt(1, 100000) <= MerchantKconfUtil::merchantOcpmMsgPerfRate()) {
    FieldsPerfContext(p_bid_context);
    FieldsPerfContextMsg(msg);
  }
}

#define INTERVAL_LOG_STASH(field) \
  ks::infra::PerfUtil::IntervalLogStash( \
      msg.field, \
      "ad.bid-server-merchant", \
      "ocpm_context_msg_diff", \
      #field, \
      msg.ocpc_action_type, \
      msg.group_tag)


void BidContextUpdateProcessor::FieldsPerfContextMsg(const MerchantOcpmMsg& msg) const {
  INTERVAL_LOG_STASH(cost);
  INTERVAL_LOG_STASH(gimbal_price_before);
  INTERVAL_LOG_STASH(customer_hc_cost);
  INTERVAL_LOG_STASH(gmv);
  INTERVAL_LOG_STASH(indirect_gmv);
  INTERVAL_LOG_STASH(conv_num);
  INTERVAL_LOG_STASH(indirect_conv_num);
  INTERVAL_LOG_STASH(record_gsp_price);
  INTERVAL_LOG_STASH(target_cost);
  INTERVAL_LOG_STASH(indirect_target_cost);
  INTERVAL_LOG_STASH(ecpm);
  INTERVAL_LOG_STASH(price_ratio);
  INTERVAL_LOG_STASH(pred_conv);
  INTERVAL_LOG_STASH(pred_target_cost);
  INTERVAL_LOG_STASH(auction_bid);
  INTERVAL_LOG_STASH(price_before_billing_separate);
  INTERVAL_LOG_STASH(price_after_billing_separate);
  INTERVAL_LOG_STASH(msg_cnt);
}

#undef INTERVAL_LOG_STASH

#define INTERVAL_LOG_STASH(field) \
  ks::infra::PerfUtil::IntervalLogStash( \
      p_bid_context->field(), \
      "ad.bid-server-merchant", \
      "ocpm_context_diff", \
      #field, \
      p_bid_context->ocpc_action_type(), \
      p_bid_context->group_tag())

void BidContextUpdateProcessor::FieldsPerfContext(OcpmBidContext* p_bid_context) const {
  INTERVAL_LOG_STASH(cost);
  INTERVAL_LOG_STASH(adjust_auto_value_rate);
  INTERVAL_LOG_STASH(ad_off_target_cost);
  INTERVAL_LOG_STASH(yesterday_cost);
  INTERVAL_LOG_STASH(yesterday_target_cost);
  INTERVAL_LOG_STASH(target_atv);
  INTERVAL_LOG_STASH(roi_ratio);
  INTERVAL_LOG_STASH(relax_roi_ratio);
  INTERVAL_LOG_STASH(p_target_cost);
  INTERVAL_LOG_STASH(inspire_cost);
  INTERVAL_LOG_STASH(inspire_gmv);
  INTERVAL_LOG_STASH(non_inspire_cost);
  INTERVAL_LOG_STASH(non_inspire_gmv);
  INTERVAL_LOG_STASH(auto_bid_explore);
  INTERVAL_LOG_STASH(cost_count);
  INTERVAL_LOG_STASH(ad_cost);
  INTERVAL_LOG_STASH(softad_cost);
  INTERVAL_LOG_STASH(is_author_fans_cost);
  INTERVAL_LOG_STASH(gmv);
  INTERVAL_LOG_STASH(gmv_count);
  INTERVAL_LOG_STASH(conv_num);
  INTERVAL_LOG_STASH(rt_cpa_bid);
  INTERVAL_LOG_STASH(delivery_cnt);
  INTERVAL_LOG_STASH(dry_up_base_value);
  INTERVAL_LOG_STASH(cross_day_target_cost);
  INTERVAL_LOG_STASH(target_cost);
  INTERVAL_LOG_STASH(softad_target_cost);
  INTERVAL_LOG_STASH(ad_target_cost);
  INTERVAL_LOG_STASH(is_author_fans_target_cost);
  INTERVAL_LOG_STASH(indirect_target_cost_0);
  INTERVAL_LOG_STASH(indirect_target_cost_3);
  INTERVAL_LOG_STASH(indirect_target_cost_7);
  INTERVAL_LOG_STASH(pred_target_cost);
  INTERVAL_LOG_STASH(target_gmv);
  INTERVAL_LOG_STASH(record_gsp_price);
  INTERVAL_LOG_STASH(separate_gsp_price);
  INTERVAL_LOG_STASH(total_ecpm);
  INTERVAL_LOG_STASH(price_before_billing_separate);
  INTERVAL_LOG_STASH(price_after_billing_separate);
  INTERVAL_LOG_STASH(total_auction_bid);
  INTERVAL_LOG_STASH(gimbal_price_before);
  INTERVAL_LOG_STASH(customer_hc_cost);
  INTERVAL_LOG_STASH(adjust_auto_value_rate);
  INTERVAL_LOG_STASH(rt_cost_speed);
  INTERVAL_LOG_STASH(rt_delivery_speed);
  INTERVAL_LOG_STASH(softad_diff_ratio);
  INTERVAL_LOG_STASH(cpa_bid);
  INTERVAL_LOG_STASH(relax_cpa_bid);
  INTERVAL_LOG_STASH(last_update_cost);
  INTERVAL_LOG_STASH(last_update_target_cost);
  INTERVAL_LOG_STASH(adjust_rate_acc_increment);
  INTERVAL_LOG_STASH(hosting_dt_actions);
  INTERVAL_LOG_STASH(old_auto_roi_ratio);
  INTERVAL_LOG_STASH(auto_roi_ratio);
  INTERVAL_LOG_STASH(old_auto_cpa_bid);
  INTERVAL_LOG_STASH(adjust_auto_atv_rate);
  INTERVAL_LOG_STASH(old_adjust_auto_atv_rate);
  INTERVAL_LOG_STASH(opt_cost);
  INTERVAL_LOG_STASH(opt_target_cost);
  INTERVAL_LOG_STASH(p_value);
  INTERVAL_LOG_STASH(i_value);
  INTERVAL_LOG_STASH(d_value);
  INTERVAL_LOG_STASH(pacing_weight);
}

#undef INTERVAL_LOG_STASH

void BidContextUpdateProcessor::Process2(StrategySession* p_session) {
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  LOG_EVERY_N(INFO, 100000) << "BidContextUpdateProcessor::Process2: unit_id: " << msg.unit_id;
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;
  auto live_hosting_item_cail_tail = MerchantKconfUtil::liveHostingItemCailTail();
  auto follow_select_union_tail = MerchantKconfUtil::followSelectUnionTail();
  auto is_live_hosting_item_cail_exp = msg.is_hosting_live && live_hosting_item_cail_tail &&
                                   live_hosting_item_cail_tail->IsOnFor(msg.campaign_id % 100);
  const auto& storewide_page_tail = MerchantKconfUtil::storewidePageTail();
  const auto& storewide_all_page_tail = MerchantKconfUtil::storewideALLPageTail();
  bool is_storewide_live_exp = msg.is_storewide_with_order && storewide_page_tail &&
                              storewide_page_tail->IsOnFor(msg.campaign_id);
  bool is_all_storewide_live_exp = msg.is_storewide_with_order && storewide_all_page_tail &&
                              storewide_all_page_tail->IsOnFor(msg.campaign_id);
  auto esp_live_hosting_roas_union_bid_tail = MerchantKconfUtil::espLiveHostingRoasUnionBidTail();
  auto is_live_hosting_union_exp = msg.is_hosting_live && esp_live_hosting_roas_union_bid_tail &&
                                   esp_live_hosting_roas_union_bid_tail->IsOnFor(msg.campaign_id);
  if (p_util_vals->is_roas) {
    double target_roas = GetTargetRoas(p_bid_context);
    if (p_bid_context->is_cold_start() && !p_util_vals->is_cold_start && !is_live_hosting_union_exp &&
        p_bid_context->cost() > p_bid_context->target_cost()) {
      // 做实验跳过
      if ((p_util_vals->is_storewide_roas && !exp_config.storewide_skip_coldstart_end_strategy) ||
          (!p_util_vals->is_storewide_roas && !exp_config.roas_skip_coldstart_end_strategy)) {
        double adjust_roas = std::max(target_roas,
                p_bid_context->auto_roi_ratio() * strategy_config.cold_start_config().stop_up_rate());
        // 冷启动结束后限制新状态的 初始 auto_roi_ratio
        p_bid_context->set_adjust_auto_value_rate(target_roas / (adjust_roas + 1e-6));
      }
    }
  } else {
    double target_cpa_bid = GetTargetCpaBid(p_bid_context);
    if (p_bid_context->is_cold_start() && !p_util_vals->is_cold_start && !is_live_hosting_union_exp &&
        p_bid_context->cost() > p_bid_context->target_cost()) {
      double adjust_cpa_bid = std::min(target_cpa_bid,
        p_bid_context->auto_cpa_bid() / std::max(1.0, strategy_config.cold_start_config().stop_up_rate()));
      p_bid_context->set_adjust_auto_value_rate(adjust_cpa_bid / target_cpa_bid);
    }
  }
  p_bid_context->set_is_cold_start(p_util_vals->is_cold_start);
  p_bid_context->set_is_explore(p_util_vals->is_explore);
  // 设置默认的 auto_bid_explore
  auto live_auto_bid_explore_guard_config = engine_base::AdKconfUtil::liveAutoBidExploreGuardConfig();
  if (p_bid_context->auto_bid_explore() <= 0) {
    double default_rate = 1.0;
    auto iter = live_auto_bid_explore_guard_config->find(msg.ocpc_action_type);
    if (iter != live_auto_bid_explore_guard_config->end()) {
      default_rate = iter->second;
    }
    if (p_util_vals->is_roas) {
      double target_roas = GetTargetRoas(p_bid_context);
      p_bid_context->set_auto_bid_explore(target_roas * default_rate);
    } else {
      double target_cpa_bid = GetTargetCpaBid(p_bid_context);
      p_bid_context->set_auto_bid_explore(target_cpa_bid * default_rate);
    }
  }
  // 设置默认的 adjust_rate_acc_increment
  if (bid_info_ptr && bid_info_ptr->explore_put_type == 3 &&
      bid_info_ptr->campaign_explore_put_type == 3 && bid_info_ptr->campaign_explore_budget_status > 1) {
    double ratio = 1.0;
    if (live_auto_bid_explore_guard_config) {
      auto iter = live_auto_bid_explore_guard_config->find("explore");
      if (iter != live_auto_bid_explore_guard_config->end()) {
        ratio = iter->second;
      }
    }
    p_bid_context->set_adjust_rate_acc_increment(1.0 * ratio);
  }
  if (p_bid_context && p_bid_context->adjust_rate_acc_increment() <= 0) {
    double ratio = 1.0;
    if (live_auto_bid_explore_guard_config) {
      auto iter = live_auto_bid_explore_guard_config->find("explore");
      if (iter != live_auto_bid_explore_guard_config->end()) {
        ratio = iter->second;
      }
    }
    p_bid_context->set_adjust_rate_acc_increment(1.0 * ratio);
  }
  if (p_util_vals->is_ad_open == false && p_util_vals->ad_off_target_cost > 0) {
    p_bid_context->set_ad_off_target_cost(
      p_bid_context->ad_off_target_cost() + p_util_vals->ad_off_target_cost);
  }
  if (((msg.is_hosting_live && follow_select_union_tail->IsOnFor(msg.campaign_id)) ||
      (!msg.is_hosting_live && follow_select_union_tail->IsOnFor(msg.unit_id))) &&
      PageBidCaliTask::Instance() != nullptr) {
    PageBidCaliTask::Instance()->FollowSelectUnionCaliTask(msg, p_bid_context, p_util_vals, exp_config);
  }
  if (SPDM_enablePlayletPageCali() && PageBidCaliTask::Instance() != nullptr) {
    PageBidCaliTask::Instance()->PlayletSmallGamePageCaliRate(msg, p_bid_context, p_util_vals, exp_config);
  }
  if (is_storewide_live_exp || is_all_storewide_live_exp) {
    PageBidCaliTask::Instance()->StorewideCaliRate(msg, p_bid_context, p_util_vals, exp_config, p_session);
  } else {
    PageBidCaliTask::Instance()->PageCaliRate(msg, p_bid_context, p_util_vals, exp_config);
  }
  if (is_live_hosting_item_cail_exp && PageBidCaliTask::Instance() != nullptr) {
    PageBidCaliTask::Instance()->ItemCaliRate(msg, p_bid_context, p_util_vals, exp_config);
  }
  if (exp_config.enable_ocpm_debt_target_cost == 1 && OcpmTargetCostTask::Instance()->Admit(p_session)) {
    OcpmTargetCostTask::Instance()->DebtTargetCost(p_bid_context, strategy_config.ocpm_target_cost_config(),
                                                   p_util_vals, exp_config);
  }
  if (exp_config.enable_ocpm_cache_target_cost == 1 && OcpmTargetCostTask::Instance()->Admit(p_session)) {
    OcpmTargetCostTask::Instance()->FreeTargetCost(p_bid_context, strategy_config.ocpm_target_cost_config(),
                                                   p_util_vals, exp_config);
  }

  if (p_util_vals->is_debug) {LOG(WARNING)
    << "BidContextUpdateProcessor::Process2:tid:" << p_util_vals->thread_id;}
}

inline void BidContextUpdateProcessor::CheckContextReset(StrategySession* p_session) {
  auto cross_day_conver_attribute_tail = MerchantKconfUtil::orderSkipCrossDayResetTail();
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;
  if (msg.scene_oriented_type == 21 && (MerchantKconfUtil::skipResetTail()->count(msg.author_id %100) ||
      MerchantKconfUtil::skipResetTail()->count(msg.author_id))) {
      int64_t now_time = TimeUtil::Instance().GetTimestamp();
      if ((now_time - p_bid_context->last_ad_open_timestamp()) / 1000 < MerchantKconfUtil::notResetTime()) {
        //  直播全站跨天不重置
        perf::Count(1, "not_sikp_reset", std::to_string(p_bid_context->campaign_id()));
        LOG_EVERY_N(INFO, 1000) << "storewide sikp reset."
                                << ", msg.author_id: " << msg.author_id
                                << ", campaign id: " << msg.campaign_id
                                << ", strategy_config.is_reset: " << strategy_config.is_reset()
                                << ", p_bid_context->last_ad_open_timestamp(): "
                                << p_bid_context->last_ad_open_timestamp() / 1000;
        return;
      }
      LOG_EVERY_N(INFO, 10000) << "storewide sikp reset. v2"
                                << ", msg.author_id: " << msg.author_id
                                << ", campaign id: " << msg.campaign_id
                                << ", last_is_ad_open: " << p_bid_context->last_is_ad_open()
                                << ", p_util_vals->is_ad_open: " << p_util_vals->is_ad_open
                                << ", p_bid_context->last_ad_open_timestamp: "
                                << p_bid_context->last_ad_open_timestamp() / 1000;
  }
  if (bid_info_ptr != nullptr && cross_day_conver_attribute_tail != nullptr &&
      bid_info_ptr->campaign_charge_mode == kuaishou::ad::AdEnum::ORDER &&
      cross_day_conver_attribute_tail->IsOnFor(msg.unit_id)) {
    return;
  }
  auto old_cost = p_bid_context->cost();
  auto old_target_cost = p_bid_context->target_cost();
  if (MerchantKconfUtil::optYesterdayCostTail()->IsOnFor(msg.author_id) || old_cost < 100000) {
        old_cost = std::max(old_cost, p_bid_context->yesterday_cost());
        old_target_cost = std::max(old_target_cost, p_bid_context->yesterday_target_cost());
  }
  if (strategy_config.is_reset() || IsResetBidContext(msg, p_bid_context, exp_config)) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_bid_server", "unit_reset_bidcontext_new_day",
                                       std::to_string(msg.unit_id), p_bid_context->group_tag());
    ResetContext(p_bid_context, p_util_vals, exp_config);
    // 全互斥客户设置调价初始值
    if ((p_util_vals->is_storewide_roas || IsStorewideAccFixV3Exp(bid_info_ptr)) &&
        (!p_util_vals->is_storewide_acc2_kcb) &&
        p_util_vals->start_bid_rate > 0.0 && p_util_vals->start_bid_rate < 1.0) {
      p_bid_context->set_adjust_auto_value_rate(1.0 / p_util_vals->start_bid_rate);
      ks::infra::PerfUtil::IntervalLogStash(p_bid_context->adjust_auto_value_rate() * 1000,
                                            "ad.ad_bid_server",
                                            "cob_store_wide_startrate_newday_adjust_value",
                                            std::to_string(msg.author_id));
    }
    if ((p_util_vals->is_unit_ocpm_eop_pc || p_util_vals->is_unit_ocpm_roas_pc ||
       (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI" && !p_util_vals->is_cbo)) || msg.is_hosting_live) {
      const auto espLiveHostingStableBidTail = MerchantKconfUtil::espLiveHostingStableBidTail();
      bool esp_live_hosting_stable_bid = espLiveHostingStableBidTail &&
           espLiveHostingStableBidTail->IsOnFor(msg.campaign_id);
      const auto espLiveDefaultStableBidTail = MerchantKconfUtil::espLiveDefaultStableBidTail();
      bool esp_live_default_bid = espLiveDefaultStableBidTail &&
           espLiveDefaultStableBidTail->IsOnFor(msg.unit_id);
      if (((p_util_vals->is_unit_ocpm_eop_pc && esp_live_default_bid) ||
         (msg.is_order_hosting_live && esp_live_hosting_stable_bid))
         && p_util_vals->start_bid_rate > 1.0) {
        p_bid_context->set_adjust_auto_value_rate(1.0 * std::max(1.0, p_util_vals->start_bid_rate));
      }
      if ((((p_util_vals->is_unit_ocpm_roas_pc || (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI"
         && !p_util_vals->is_cbo)) && esp_live_default_bid) ||
         (msg.is_roas_hosting_live && esp_live_hosting_stable_bid))
         && p_util_vals->start_bid_rate > 0.0 && p_util_vals->start_bid_rate < 1.0) {
        p_bid_context->set_adjust_auto_value_rate(1.0 / p_util_vals->start_bid_rate);
      }
    }
    if (p_util_vals->is_storewide_roas) {
      p_bid_context->set_yesterday_cost(old_cost);
      p_bid_context->set_yesterday_target_cost(old_target_cost);
      ks::infra::PerfUtil::IntervalLogStash(old_cost,
                                            "ad.ad_bid_server",
                                            "old_cost",
                                            std::to_string(msg.campaign_id));
    }
  }
}

inline void BidContextUpdateProcessor::SetContextBaseInfo(
  const MerchantOcpmMsg& msg, const BidStateInfoPtr& bid_info_ptr,
  OcpmBidContext* p_bid_context, ProcessUtilValues* p_util_vals) {
  if (p_bid_context->unit_id() <= 0 && msg.unit_id > 0) {
    p_bid_context->set_unit_id(msg.unit_id);
  }
  if (p_bid_context->account_id() <= 0) {
    if (msg.account_id > 0) {
      p_bid_context->set_account_id(msg.account_id);
    } else if (bid_info_ptr && bid_info_ptr->account_id > 0) {
      p_bid_context->set_account_id(bid_info_ptr->account_id);
    }
  }
  if (p_bid_context->campaign_id() <= 0) {
    if (msg.campaign_id > 0) {
      p_bid_context->set_campaign_id(msg.campaign_id);
    } else if (bid_info_ptr && bid_info_ptr->campaign_id > 0) {
      p_bid_context->set_campaign_id(bid_info_ptr->campaign_id);
    }
  }
  if (p_bid_context->author_id() <= 0) {
    if (bid_info_ptr && bid_info_ptr->author_id) {
      p_bid_context->set_author_id(bid_info_ptr->author_id);
    } else if (msg.author_id > 0) {
      p_bid_context->set_author_id(msg.author_id);
    }
  }
  if (p_bid_context->live_stream_id() <= 0) {
    if (msg.live_stream_id > 0) {
      p_bid_context->set_live_stream_id(msg.live_stream_id);
    } else if (bid_info_ptr && bid_info_ptr->live_stream_id > 0) {
      p_bid_context->set_live_stream_id(bid_info_ptr->live_stream_id);
    }
  }
  if (msg.speed_type > 0) {
    p_bid_context->set_speed_type(msg.speed_type);
  } else if (bid_info_ptr && bid_info_ptr->speed_type > 0) {
    p_bid_context->set_speed_type(bid_info_ptr->speed_type);
  }
  if (p_bid_context->campaign_type().size() < 1 ||
      p_bid_context->campaign_type() == "UNKNOWN_CAMPAIGN_TYPE") {
    if (msg.campaign_type.size() > 1 && msg.campaign_type != "UNKNOWN_CAMPAIGN_TYPE") {
      p_bid_context->set_campaign_type(msg.campaign_type);
    } else if (bid_info_ptr && bid_info_ptr->campaign_type != kuaishou::ad::AdEnum::UNKNOWN_CAMPAIGN_TYPE) {
      p_bid_context->set_campaign_type(kuaishou::ad::AdEnum::CampaignType_Name(bid_info_ptr->campaign_type));
    }
  }
  bool is_qiandu = msg.ocpc_action_type == "AD_LIVE_AUDIENCE" || msg.ocpc_action_type == "AD_MERCHANT_FOLLOW";
  bool is_storewide_switch = (msg.scene_oriented_type == 21 || msg.scene_oriented_type == 30) && !is_qiandu;
  if (SPDM_enableAggrMerchantOcpmMsg()) {
    is_storewide_switch = msg.is_storewide_switch;
  }
  if (p_bid_context->scene_oriented_type() == 0 && msg.scene_oriented_type != 0) {
    p_bid_context->set_scene_oriented_type(msg.scene_oriented_type);
  }
  if (p_bid_context->ocpc_action_type().size() < 1 ||
      p_bid_context->ocpc_action_type() == "UNKNOWN_ACTION_TYPE") {
    if (msg.ocpc_action_type.size() > 1 && msg.ocpc_action_type != "UNKNOWN_ACTION_TYPE") {
      p_bid_context->set_ocpc_action_type(msg.ocpc_action_type);
      if (is_storewide_switch) {
        //  全站的订单支付单元在之后全走 AD_STOREWIDE_ROAS
        p_bid_context->set_ocpc_action_type("AD_STOREWIDE_ROAS");
      }
    } else if (bid_info_ptr && bid_info_ptr->ocpx_action_type) {
      p_bid_context->set_ocpc_action_type(kuaishou::ad::AdActionType_Name(bid_info_ptr->ocpx_action_type));
      if (is_storewide_switch) {
        //  全站的订单支付单元在之后全走 AD_STOREWIDE_ROAS
        p_bid_context->set_ocpc_action_type("AD_STOREWIDE_ROAS");
      }
    }

    auto esp_live_hosting_roas_union_bid_tail = MerchantKconfUtil::espLiveHostingRoasUnionBidTail();
    if (esp_live_hosting_roas_union_bid_tail &&
        esp_live_hosting_roas_union_bid_tail->IsOnFor(msg.campaign_id) && msg.is_roas_hosting_live) {
      p_bid_context->set_ocpc_action_type("AD_MERCHANT_ROAS");
    }
    // 修复 T7 调价问题
    auto esp_live_hosting_fix_t7_tail = MerchantKconfUtil::espLiveHostingFixT7Tail();
    if (msg.is_roas_hosting_live
        && msg.ocpc_action_type == "AD_MERCHANT_T7_ROI" && esp_live_hosting_fix_t7_tail &&
        esp_live_hosting_fix_t7_tail->IsOnFor(msg.campaign_id)) {
      p_bid_context->set_ocpc_action_type("AD_MERCHANT_T7_ROI");
    }
  }
  // 填充是否净成交 ROI 字段
  if (p_bid_context->bid_assist_type() <= 0 && bid_info_ptr && bid_info_ptr->bid_assist_type > 0) {
    p_bid_context->set_bid_assist_type(bid_info_ptr->bid_assist_type);
  }
  if (p_bid_context->promotion_type().size() < 1 ||
      p_bid_context->promotion_type() == "UNKNOWN_PROMOTION") {
    if (msg.promotion_type.size() > 1 && msg.promotion_type != "UNKNOWN_PROMOTION") {
      p_bid_context->set_promotion_type(msg.promotion_type);
    } else if (bid_info_ptr && bid_info_ptr->promotion_type) {
      p_bid_context->set_promotion_type(
        kuaishou::ad::AdEnum::CampaignPromotionType_Name(bid_info_ptr->promotion_type));
    }
  }
  if (p_bid_context->item_type().size() < 1) {
    if (msg.item_type.size() > 3) {
      p_bid_context->set_item_type(msg.item_type);
    }
  }
  if (p_bid_context->first_industry_name().size() < 1) {
    kuaishou::ad::tables::AccountIndustry account_industry;
    BidIndexManager::Instance().GetIndex()->GetIndustryV4(p_bid_context->account_id(), &account_industry);
    auto ind_info_iter = account_industry.industry_info().find("4.0");
    if (ind_info_iter != account_industry.industry_info().end()) {
      auto ind_info = ind_info_iter->second;
      p_bid_context->set_first_industry_name(ind_info.first_industry_name());
      p_bid_context->set_second_industry_name(ind_info.second_industry_name());
    }
  }

  if (p_bid_context->context_start_timestamp() <= 0) {
    p_bid_context->set_context_start_timestamp(TimeUtil::Instance().GetTimestamp());
  }
  if (p_bid_context->first_delivery_timestamp_ms() <= 0 && IsAdDelivery(msg, *p_util_vals)) {
    p_bid_context->set_first_delivery_timestamp_ms(msg.delivery_timestamp);
  }
}

inline void BidContextUpdateProcessor::SetTargetValue(StrategySession* p_session) {
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;

  if (bid_info_ptr) {
    p_bid_context->set_online(bid_info_ptr->online);
  }

  if (IsLEZero(p_bid_context->target_atv())) {
    auto atv = GetTargetAtv(p_bid_context);
    p_bid_context->set_target_atv(atv);
  }

  if (p_util_vals->is_roas && msg.ocpc_action_type != "EVENT_ORDER_PAIED") {
    double roi_ratio = p_bid_context->roi_ratio();
    if (bid_info_ptr && !msg.is_storewide_with_order) {
      if (p_util_vals->is_fanstop) {
        roi_ratio = static_cast<double>(bid_info_ptr->cpa_bid) / 1000;
      } else {
        roi_ratio = bid_info_ptr->roi_ratio;
      }
      if (IsLEZero(roi_ratio) && IsGTZero(msg.roi_ratio)) {
        roi_ratio = msg.roi_ratio;
      }
    } else {
      //  直播全站直接从 msg.roi_ratio 中取，msg 中的数据是从 campaign 中取的
      if (IsGTZero(msg.roi_ratio)) {
        roi_ratio = msg.roi_ratio;
      } else if (msg.is_storewide_with_order && IsLEZero(roi_ratio)) {
        //  全站没拿到时的兜底值
        roi_ratio = 2.01;
        perf::Count(1, "storewide_event", std::to_string(msg.campaign_id), "roi_error_0");
        LOG(INFO) << "error storewide event msg."
                  << ", author id: " << msg.author_id
                  << ", campaign id: " << msg.campaign_id
                  << ", unit id: " << msg.unit_id
                  << ", ocpx: " << msg.ocpc_action_type
                  << ", msg.roi_ratio: " << msg.roi_ratio
                  << ", bid_info_ptr->roi_ratio: " << (bid_info_ptr == nullptr ? -1: bid_info_ptr->roi_ratio)
                  << ", bid_info_ptr->roi_ratio: "
                  << (bid_info_ptr == nullptr ? -1: bid_info_ptr->project_roi_ratio)
                  << ", p_bid_context:" << p_bid_context->ShortDebugString();
      }
    }
    if (IsGTZero(p_bid_context->roi_ratio()) &&
        !IsEqual(roi_ratio, p_bid_context->roi_ratio())) {
      p_util_vals->is_target_modify = true;
      p_util_vals->target_modify_ratio = roi_ratio / p_bid_context->roi_ratio();
    }
    p_bid_context->set_roi_ratio(roi_ratio);
    p_bid_context->set_relax_roi_ratio(roi_ratio * strategy_config.target_value_relax_rate());
  } else {
    uint64_t cpa_bid = p_bid_context->cpa_bid();
    if (bid_info_ptr) {
      cpa_bid = bid_info_ptr->cpa_bid;
      if (IsLEZero(cpa_bid) && IsGTZero(msg.cpa_bid)) {
        cpa_bid = msg.cpa_bid;
      }
    } else {
      if (IsGTZero(msg.cpa_bid)) {
        cpa_bid = msg.cpa_bid;
      }
    }
    if (IsGTZero(p_bid_context->cpa_bid()) &&
        !IsEqual(cpa_bid, p_bid_context->cpa_bid())) {
      p_util_vals->is_target_modify = true;
      p_util_vals->target_modify_ratio = cpa_bid / p_bid_context->cpa_bid();
    }

    if (msg.is_roas_hosting_live_order_unit) {
      if (IsGTZero(p_bid_context->cpa_bid())) {
        cpa_bid = p_bid_context->cpa_bid();
      }
      if (p_bid_context->conv_num() > exp_config.enable_hosting_live_roasorder_conv_num) {
        double realtime_atv = p_bid_context->gmv() / p_bid_context->conv_num();
        double t_cpa_bid = msg.project_roi_ratio > 0.0 ? realtime_atv / msg.project_roi_ratio : cpa_bid;
        if (cpa_bid > 0 && (t_cpa_bid / cpa_bid > 1.2 || t_cpa_bid / cpa_bid < 0.8)) {
          cpa_bid = static_cast<uint64_t>(t_cpa_bid);
        }
      }
    }

    if (IsLEZero(cpa_bid) && msg.is_roas_hosting_live) {
      cpa_bid = msg.project_roi_ratio > 0.0 ? GetTargetAtv(p_bid_context) / msg.project_roi_ratio : cpa_bid;
    }

    if (IsLEZero(cpa_bid) && msg.is_storewide_switch) {
      cpa_bid = msg.roi_ratio > 0.0 ? GetTargetAtv(p_bid_context) / msg.roi_ratio : cpa_bid;
    }

    p_bid_context->set_cpa_bid(cpa_bid);
    p_bid_context->set_relax_cpa_bid(cpa_bid * strategy_config.target_value_relax_rate());
    if (msg.is_storewide_switch) {
      if (IsLEZero(p_bid_context->roi_ratio())) {
        if (IsGTZero(msg.roi_ratio)) {
          p_bid_context->set_roi_ratio(msg.roi_ratio);
          p_bid_context->set_relax_roi_ratio(msg.roi_ratio * strategy_config.target_value_relax_rate());
        } else {
            perf::Count(1, "storewide_event", std::to_string(msg.campaign_id), "roi_error");
            LOG_EVERY_N(INFO, 1) << "error storewide event msg."
                                 << ", author id: " << msg.author_id
                                 << ", campaign id: " << msg.campaign_id
                                 << ", unit id: " << msg.unit_id
                                 << ", ocpx: " << msg.ocpc_action_type
                                 << ", msg.roi_ratio: " << msg.roi_ratio
                                  << ", p_bid_context:" << p_bid_context->ShortDebugString();
          p_bid_context->set_roi_ratio(2.001);  // 设置一个兜底值
          p_bid_context->set_relax_roi_ratio(2.001 * strategy_config.target_value_relax_rate());
        }
      }
      LOG_EVERY_N(INFO, 1000) << "debug storewide event msg. 3"
                             << ", msg.cpa_bid: " << msg.cpa_bid
                             << ", p_bid_context->cpa_bid: " << p_bid_context->cpa_bid()
                             << ", p_bid_context->roi_ratio: " << p_bid_context->roi_ratio()
                             << ", ocpx: " << msg.ocpc_action_type
                             << ", msg.roi_ratio: " << msg.roi_ratio
                             << ", msg.project_roi_ratio: " << msg.project_roi_ratio;
    }
  }
  if (msg.target_cost > 0) {
    p_util_vals->ad_off_target_cost = msg.target_cost;
  }
}

inline void BidContextUpdateProcessor::UpdateGeneralBidContext(
  const MerchantOcpmMsg& msg, OcpmBidContext* p_bid_context,
  const ProcessUtilValues& p_util_vals, const MerchantOcpmExpConfig& exp_config) {
  auto page_id_to_key_dict = MerchantKconfUtil::pageIdToKeyDict();
  auto follow_select_union_dict = MerchantKconfUtil::followSelectUnionDict();
  auto esp_live_page_key = MerchantKconfUtil::espLivePageKeyDict();
  auto inspire_page_id = MerchantKconfUtil::inspirePageId();
  auto select_page_id = MerchantKconfUtil::selectPageId();
  const auto& storewide_page_key = MerchantKconfUtil::storewidePageKeyDict();
  const auto& storewide_page_key_v2 = MerchantKconfUtil::storewidePageKeyDictV2();
  const auto& storewide_page_key_ab = MerchantKconfUtil::storewidePageKeyABDict();
  const auto& storewide_pos_key = MerchantKconfUtil::storewidePosKeyDict();
  const auto& storewide_page_tail = MerchantKconfUtil::storewidePageTail();
  const auto& storewide_all_page_tail = MerchantKconfUtil::storewideALLPageTail();
  const auto& cross_day_conver_attribute_tail = MerchantKconfUtil::crossDayConverAttributeTail();
  const auto& storewide_item_type_tail = MerchantKconfUtil::storewideItemTypeCaliTail();
  const auto& storewide_nature_key = MerchantKconfUtil::storewideNatureDict();
  const auto& storewide_page_follow_select_key = MerchantKconfUtil::storewideFollowSelectDict();
  const auto& storewide_follow_select_tail = MerchantKconfUtil::storewideFollowSelectNatureCaliTail();
  const auto& storewide_nature_tail = MerchantKconfUtil::storewideNatureCaliTail();
  auto live_hosting_item_cail_tail = MerchantKconfUtil::liveHostingItemCailTail();
  auto esp_live_page_cali_t7_split_tail = MerchantKconfUtil::espLivePageCaliT7RoiSplitTail();
  auto is_live_hosting_item_cail_exp = msg.is_hosting_live && live_hosting_item_cail_tail &&
                                   live_hosting_item_cail_tail->IsOnFor(msg.campaign_id % 100);
  bool is_storewide_live_exp = msg.is_storewide_with_order && storewide_page_tail &&
                              storewide_page_tail->IsOnFor(msg.campaign_id);
  bool is_all_storewide_live_exp = msg.is_storewide_with_order && storewide_all_page_tail &&
                                  storewide_all_page_tail->IsOnFor(msg.campaign_id);
  bool is_storewide_item_exp = msg.is_storewide_with_order && storewide_item_type_tail &&
                              storewide_item_type_tail->IsOnFor(msg.campaign_id);
  bool is_sotrewide_nature_exp = msg.is_storewide_with_order && storewide_nature_tail &&
                                storewide_nature_tail->IsOnFor(msg.campaign_id);
  // 归因跨天预期花费过滤
  if ((p_util_vals.is_unit_ocpm_roas_pc || p_util_vals.is_unit_ocpm_eop_pc) &&
      cross_day_conver_attribute_tail->IsOnFor(msg.unit_id) && msg.ad_attributed_timestamp > 0 &&
      msg.target_cost > 0) {
    uint64_t today_date = GetTodayDate();
    uint64_t delivery_date = GetDateMs(msg.ad_attributed_timestamp);
    if (today_date != delivery_date) {
      return;
    }
  }
  if (p_bid_context->first_delivery_timestamp_ms() <= 0 && IsAdDelivery(msg, p_util_vals)) {
    p_bid_context->set_first_delivery_timestamp_ms(msg.delivery_timestamp);
  }
  if (msg.action_type == "AD_DELIVERY") {
    double avg_cpm = p_bid_context->avg_cpm();
    if (avg_cpm < DBL_EPSILON) {
      p_bid_context->set_avg_cpm(msg.cpm * 1.0);
    } else {
      p_bid_context->set_avg_cpm(0.05 * msg.cpm + 0.95 * avg_cpm);
    }
  }
  if (msg.action_type == "AD_LIVE_PLAYED_STARTED" &&
      msg.live_room_pattern == "STANDARD_LIVE_ROOM_PATTERN") {
    double p_target_cost = p_bid_context->p_target_cost();
    if (p_bid_context->scene_oriented_type() == 21) {
      double pred_target_cost = 0.0;
      if (msg.ocpc_action_type == "EVENT_ORDER_PAIED") {
        pred_target_cost = msg.pred_target_cost_eop;
      } else {
        pred_target_cost = msg.pred_target_cost_roas * 1000.0;
      }
      p_bid_context->set_p_target_cost(p_target_cost + pred_target_cost);
    }
    if (p_util_vals.is_unit_ocpm_eop_pc) {
      double p_cvr = msg.predict_unify_cvr;
      double pred_target_cost_eop = msg.pred_target_cost_eop;
      if (!SPDM_enableAggrMerchantOcpmMsg()) {
        p_bid_context->set_p_target_cost(p_target_cost + p_cvr * p_bid_context->cpa_bid());
      } else {
        p_bid_context->set_p_target_cost(p_target_cost + pred_target_cost_eop);
      }
    }
    if (p_util_vals.is_unit_ocpm_roas_pc ||
       (p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI" && !p_util_vals.is_cbo)) {
      double p_ltv = msg.unify_ltv;
      p_ltv = p_ltv * 1000;
      double pred_target_cost_roas = msg.pred_target_cost_roas;
      pred_target_cost_roas = pred_target_cost_roas * 1000;
      if (p_bid_context->roi_ratio() > 0) {
        if (!SPDM_enableAggrMerchantOcpmMsg()) {
          p_bid_context->set_p_target_cost(p_target_cost + p_ltv / p_bid_context->roi_ratio());
        } else {
          p_bid_context->set_p_target_cost(p_target_cost + pred_target_cost_roas);
        }
      }
    }
  }

  if (msg.is_hosting_live) {
    // 获取当前 minute
    uint64_t now_s = TimeUtil::Instance().GetTimestamp() / 1000000;
    int64_t days_since_epoch = (now_s + 28800) / 86400;
    int64_t midnight_time_stamp = days_since_epoch * 86400 - 28800;
    int64_t minute_index = (now_s - midnight_time_stamp) / 60;
    auto* mpc_slide_minute = p_bid_context->mutable_mpc_slide_minute();
    auto* mpc_slide_origin_price_sum = p_bid_context->mutable_mpc_slide_origin_price_sum();
    auto* mpc_slide_front_auction_bid_sum = p_bid_context->mutable_mpc_slide_front_auction_bid_sum();

    // 按分钟聚合：检查当前分钟是否存在，若存在则累加，否则新增条目
    if (!mpc_slide_minute->empty() &&
        mpc_slide_minute->Get(mpc_slide_minute->size() - 1) == minute_index) {
        // 累加当前分钟的数据
        (*mpc_slide_origin_price_sum)[mpc_slide_origin_price_sum->size() - 1] +=
          msg.origin_price;
        (*mpc_slide_front_auction_bid_sum)[mpc_slide_front_auction_bid_sum->size() - 1] +=
          msg.front_auction_bid;
    } else {
        // 插入新分钟条目到末尾
        mpc_slide_minute->Add(minute_index);
        mpc_slide_origin_price_sum->Add(msg.origin_price);
        mpc_slide_front_auction_bid_sum->Add(msg.front_auction_bid);
    }

    // 计算滑动窗口边界
    int32_t count_i = 0;
    int64_t slidewindow_minute = 10;
    if (MerchantKconfUtil::slide_window_minute() > 0) {
        slidewindow_minute = MerchantKconfUtil::slide_window_minute();
    }
    // 定位需要删除的过期数据
    while (count_i < mpc_slide_minute->size() &&
          (minute_index - mpc_slide_minute->Get(count_i) > slidewindow_minute)) {
        count_i++;
    }
    // 删除所有过期的分钟聚合数据（移除前 count_i 个元素）
    mpc_slide_minute->erase(mpc_slide_minute->begin(), mpc_slide_minute->begin() + count_i);
    mpc_slide_origin_price_sum->erase(mpc_slide_origin_price_sum->begin(),
                                      mpc_slide_origin_price_sum->begin() + count_i);
    mpc_slide_front_auction_bid_sum->erase(mpc_slide_front_auction_bid_sum->begin(),
                                           mpc_slide_front_auction_bid_sum->begin() + count_i);

    // 计算滑动窗口内的总和
    double temp_origin_price_sum = 0;
    double temp_front_auction_bid_sum = 0;
    for (int32_t i = 0; i < mpc_slide_origin_price_sum->size(); ++i) {
        temp_origin_price_sum += mpc_slide_origin_price_sum->Get(i);
        temp_front_auction_bid_sum += mpc_slide_front_auction_bid_sum->Get(i);
    }
    p_bid_context->set_origin_price_sum(temp_origin_price_sum);
    p_bid_context->set_front_auction_bid_sum(temp_front_auction_bid_sum);
  }

  if (msg.cost > 0) {
    p_bid_context->set_cost(p_bid_context->cost() + msg.cost);
    if (p_util_vals.is_local_life) {
      p_bid_context->set_bid_coef_cost(p_bid_context->bid_coef_cost() + msg.bid_coef_cost);
      p_bid_context->set_cpa_coef_cost(p_bid_context->cpa_coef_cost() + msg.cpa_coef_cost);
      if (IsGTZero(msg.bid_coef)) {
        p_bid_context->set_bid_cost(p_bid_context->bid_cost() + msg.cost);
      }
    }
    p_bid_context->set_cost_count(p_bid_context->cost_count() + msg.msg_cnt);
    if (msg.page_id != 100022354 && !msg.is_store_wide_roi_reco_conv) {
      p_bid_context->set_ad_cost(p_bid_context->ad_cost() + msg.cost);
    }
    {
      double b2x_cost = 0.0;
      double b2x_ecpm = 0.0;
      if (msg.ocpc_action_type == "AD_STOREWIDE_ROAS" ||
          msg.ocpc_action_type == "AD_MERCHANT_ROAS" || msg.ocpc_action_type == "AD_MERCHANT_T7_ROI") {
        if (msg.roi_ratio > 0) {
          b2x_cost = msg.origin_price * msg.auto_roas;  // = cost / auto_cpa_bid 下同
          if (msg.is_storewide_with_t7 && p_util_vals.roi7_convert_ratio > 0) {
            b2x_ecpm = msg.front_auction_bid * msg.auto_roas * p_util_vals.roi7_convert_ratio;
          } else {
            b2x_ecpm = msg.front_auction_bid * msg.auto_roas;
          }
        }
      } else if (msg.auto_cpa_bid > 0) {
        b2x_cost = msg.origin_price * msg.cpa_bid / msg.auto_cpa_bid * msg.roi_ratio;
        b2x_ecpm = msg.front_auction_bid * msg.cpa_bid / msg.auto_cpa_bid * msg.roi_ratio;
      }
      p_bid_context->set_b2x_cost(p_bid_context->b2x_cost() + b2x_cost);
      if (msg.action_type == "AD_DELIVERY") {
        p_bid_context->set_b2x_ecpm(p_bid_context->b2x_ecpm() + b2x_ecpm);
      }
      if (msg.page_id == 100022354 && msg.action_type == "AD_DELIVERY") {
        p_bid_context->set_b2x_reco_cost(p_bid_context->b2x_reco_cost() + b2x_cost);
        p_bid_context->set_b2x_reco_ecpm(p_bid_context->b2x_reco_ecpm() + b2x_ecpm);
      }
    }
    if (msg.is_soft) {
      p_bid_context->set_softad_cost(p_bid_context->softad_cost() + msg.cost);
    }
    if (msg.is_author_fans == 1) {
      p_bid_context->set_is_author_fans_cost(p_bid_context->is_author_fans_cost() + msg.cost);
    }
  }
  if (msg.gmv > 0) {
    p_bid_context->set_gmv(p_bid_context->gmv() + msg.gmv);
    // 净成交场景总支付金额(不包含退款)
    if (p_bid_context->bid_assist_type() == 1) {
      p_bid_context->set_total_callback_purchase_amount(
          p_bid_context->total_callback_purchase_amount() + msg.gmv);
    }
    if (msg.is_store_wide_roi_reco_conv) {
      p_bid_context->set_reco_gmv(p_bid_context->reco_gmv() + msg.gmv);
      if (msg.storewide_inc_type == 1) {
        p_bid_context->set_uplift_gmv(p_bid_context->uplift_gmv() + msg.gmv);
      }
    }
    p_bid_context->set_gmv_count(p_bid_context->gmv_count() + msg.msg_cnt);
  }
  // 净成交场景
  if (p_bid_context->bid_assist_type() == 1) {
    if (msg.callback_refund_amount_exact_sum > 0) {  // 退款金额
      p_bid_context->set_callback_refund_amount(p_bid_context->callback_refund_amount() +
        msg.callback_refund_amount_exact_sum);
    }
    if (msg.callback_refund_target_cost_sum > 0) {  // 退款预期花费
      p_bid_context->set_callback_refund_target_cost(p_bid_context->callback_refund_target_cost() +
        msg.callback_refund_target_cost_sum);
    }
    if (msg.pure_roi_purchase_amount_exact_sum > 0) {  // 净成交金额
      p_bid_context->set_pure_roi_purchase_amount(p_bid_context->pure_roi_purchase_amount() +
        msg.pure_roi_purchase_amount_exact_sum);
    }
    if (msg.pure_roi_purchase_target_cost_sum > 0) {  // 净成交预期花费
      p_bid_context->set_pure_roi_purchase_target(p_bid_context->pure_roi_purchase_target() +
        msg.pure_roi_purchase_target_cost_sum);
    }
  }

  if (p_util_vals.is_local_life) {
    if (IsGTZero(msg.bid_coef)) {
      p_bid_context->set_bid_coef_value_sum(p_bid_context->bid_coef_value_sum() + msg.bid_coef);
      p_bid_context->set_bid_coef_cnt_sum(p_bid_context->bid_coef_cnt_sum() + 1);
    }
    if (IsGTZero(msg.cpa_coef)) {
      p_bid_context->set_cpa_coef_value_sum(p_bid_context->cpa_coef_value_sum() + msg.cpa_coef);
      p_bid_context->set_cpa_coef_cnt_sum(p_bid_context->cpa_coef_cnt_sum() + 1);
    }
  }
  {
    double msg_auto_roi = msg.auto_roas;
    if (msg.ocpc_action_type == "EVENT_ORDER_PAIED" && msg.auto_cpa_bid > 0) {
      if (p_bid_context->roi_ratio() > 0) {
        msg_auto_roi = p_bid_context->cpa_bid() * p_bid_context->roi_ratio() /  msg.auto_cpa_bid;
      } else {
        msg_auto_roi = GetTargetAtv(p_bid_context) / msg.auto_cpa_bid;
      }
    }
    if (msg_auto_roi > 0 && msg.action_type == "AD_DELIVERY") {
      p_bid_context->set_unify_ltv(p_bid_context->unify_ltv() + msg.front_auction_bid * msg_auto_roi);
      if (msg.page_id == 100022354) {
        p_bid_context->set_uplift_unify_ltv(p_bid_context->uplift_unify_ltv() +
                  msg.front_auction_bid * msg_auto_roi);
      }
    }
  }
  if (msg.conv_num > 0) {
    p_bid_context->set_conv_num(p_bid_context->conv_num() + msg.conv_num);
  }

  if (msg.pred_conv > 0.0 && IsAdDelivery(msg, p_util_vals)) {
    p_bid_context->mutable_conv_ratio_adjust_context()->set_pred_conv(
        p_bid_context->conv_ratio_adjust_context().pred_conv() + msg.pred_conv);
  }
  if (p_util_vals.is_roas) {
    if (msg.gmv > 0) {
      p_bid_context->mutable_conv_ratio_adjust_context()->set_real_conv(
          p_bid_context->conv_ratio_adjust_context().real_conv() + msg.gmv);
    }
  } else {
    if (msg.conv_num > 0) {
      p_bid_context->mutable_conv_ratio_adjust_context()->set_real_conv(
          p_bid_context->conv_ratio_adjust_context().real_conv() + msg.conv_num);
    }
  }
  // 关注页精选页联合调价
  auto union_iter = follow_select_union_dict->find(msg.page_id);
  if (union_iter != follow_select_union_dict->end()) {
    bool is_valid = true;
    // 关注页单列
    if (msg.page_id == 11001 || msg.page_id ==10002) {
      if (msg.interactive_form != 2) {
        is_valid = false;
      }
    }
    if (is_valid) {
      auto page_key = union_iter->second;
      auto& cost_info = (*p_bid_context->mutable_page_cost_info())[page_key];
      if (msg.cost > 0) {
        cost_info.set_cost(cost_info.cost() + msg.cost);
      }
      if (msg.target_cost > 0) {
        cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
      }
    }
  }
  if ((msg.pos_id == 27217 || msg.pos_id == 27218 || msg.pos_id == 27219 || msg.pos_id == 27220) &&
      SPDM_enablePlayletPageCali()) {
    auto& cost_info = (*p_bid_context->mutable_page_cost_info())["playlet"];
    if (msg.cost > 0) {
      cost_info.set_cost(cost_info.cost() + msg.cost);
    }
    if (msg.target_cost > 0) {
      cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
    }
  }
  if (msg.page_id == 13001 && SPDM_enablePlayletPageCali()) {
    auto& cost_info = (*p_bid_context->mutable_page_cost_info())["small_game"];
    if (msg.cost > 0) {
      cost_info.set_cost(cost_info.cost() + msg.cost);
    }
    if (msg.target_cost > 0) {
      cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
    }
  }
  auto iter = page_id_to_key_dict->find(msg.page_id);
  if (iter != page_id_to_key_dict->end()) {
    auto page_key = iter->second;
    if ((page_key == "a" && msg.is_search_antou) || page_key != "a") {
      auto cali_page_key = absl::Substitute("$0_cali", page_key);
      auto& cost_info = (*p_bid_context->mutable_page_cost_info())[page_key];
      auto& cost_info_cali = (*p_bid_context->mutable_page_cost_info())[cali_page_key];
      if (msg.cost > 0) {
        cost_info.set_cost(cost_info.cost() + msg.cost);
        cost_info_cali.set_cost(cost_info_cali.cost() + msg.cost);
      }
      if (msg.target_cost > 0) {
        if (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI" &&
            esp_live_page_cali_t7_split_tail &&
            ((msg.is_hosting_live && esp_live_page_cali_t7_split_tail->IsOnFor(msg.campaign_id)) ||
            (!msg.is_hosting_live && esp_live_page_cali_t7_split_tail->IsOnFor(msg.unit_id)))) {
          double t7_convert_ratio = p_bid_context->t7_roi_convert_ratio() < 1.0 ?
                                      1.2 : p_bid_context->t7_roi_convert_ratio();
          t7_convert_ratio = 1.0;
          cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost * t7_convert_ratio);
          cost_info_cali.set_target_cost(cost_info_cali.target_cost() + msg.target_cost * t7_convert_ratio);
        } else {
          cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
          cost_info_cali.set_target_cost(cost_info_cali.target_cost() + msg.target_cost);
        }
      }
    }
  }
  auto page_iter = esp_live_page_key->find(msg.page_id);
  if (page_iter != esp_live_page_key->end()) {
    auto key = page_iter->second;
    auto cali_key = absl::Substitute("$0_diff", key);
    auto& cost_info = (*p_bid_context->mutable_page_cost_info())[cali_key];
    if (msg.cost > 0) {
      cost_info.set_cost(cost_info.cost() + msg.cost);
    }
    if (msg.target_cost > 0) {
      cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
    }
  }
  if (is_live_hosting_item_cail_exp) {
    if (msg.item_type == "ITEM_PHOTO_TO_LIVE" || msg.item_type == "ITEM_LIVE") {
      auto& cost_info = (*p_bid_context->mutable_page_cost_info())[msg.item_type];
      if (msg.cost > 0) {
        cost_info.set_cost(cost_info.cost() + msg.cost);
      }
      if (msg.target_cost > 0) {
        cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
      }
    }
    if (msg.ocpc_action_type == "AD_MERCHANT_ROAS" || msg.ocpc_action_type == "EVENT_ORDER_PAIED") {
      auto& cost_info = (*p_bid_context->mutable_page_cost_info())[msg.ocpc_action_type];
      if (msg.cost > 0) {
        cost_info.set_cost(cost_info.cost() + msg.cost);
      }
      if (msg.target_cost > 0) {
        cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
      }
    }
  }
  if (is_all_storewide_live_exp) {
    const auto& storewide_all_key = MerchantKconfUtil::storewideAllKeyDict();
    const std::vector<std::string> item_vec = {msg.item_type, ""};
    const std::vector<std::string> ocpx_vec = {msg.ocpc_action_type, ""};
    const std::vector<std::string> page_vec = {std::to_string(msg.page_id), ""};
    for (auto item : item_vec) {
      for (auto ocpx : ocpx_vec) {
        for (auto page : page_vec) {
          std::string key = item + ocpx + page;
          auto all_iter = storewide_all_key->find(key);
          if (all_iter == storewide_all_key->end()) {
            continue;
          }
          auto all_key = absl::Substitute("$0_storewide", all_iter->second);
          auto& cost_info = (*p_bid_context->mutable_page_cost_info())[all_key];
          if (msg.cost > 0) {
            cost_info.set_cost(cost_info.cost() + msg.cost);
          }
          if (msg.target_cost > 0) {
            cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
          }
        }
      }
    }
  } else {
    auto sw_iter = storewide_page_key->find(msg.page_id);
    if (is_storewide_live_exp && sw_iter != storewide_page_key->end()) {
      auto key = sw_iter->second;
      auto sw_key = absl::Substitute("$0_storewide", key);
      auto& cost_info = (*p_bid_context->mutable_page_cost_info())[sw_key];
      if (msg.cost > 0) {
        cost_info.set_cost(cost_info.cost() + msg.cost);
      }
      if (msg.target_cost > 0) {
        cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
      }
    }
  }
  if (msg.is_storewide_with_order && SPDM_enableStorewidePageAB() && storewide_page_key_ab) {
    auto ab_iter = storewide_page_key_ab->find(msg.page_id);
    if (ab_iter != storewide_page_key_ab->end()) {
      auto key = ab_iter->second;
      auto sw_key = absl::Substitute("$0_storewide", key);
      auto& cost_info = (*p_bid_context->mutable_page_cost_info())[sw_key];
      if (msg.cost > 0) {
        cost_info.set_cost(cost_info.cost() + msg.cost);
      }
      if (msg.target_cost > 0) {
        cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
      }
    }
  }
  if (is_storewide_item_exp) {
    if (SPDM_enableStorewidePageV2() && storewide_page_key_v2) {
      auto iter = storewide_page_key_v2->find(msg.page_id);
      if (iter != storewide_page_key_v2->end()) {
        auto page_key = iter->second;
        auto key = absl::Substitute("$0_$1_item_storewide", msg.item_type, page_key);
        auto& cost_info = (*p_bid_context->mutable_page_cost_info())[key];
        if (msg.cost > 0) {
          cost_info.set_cost(cost_info.cost() + msg.cost);
        }
        if (msg.target_cost > 0) {
          cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
        }
      }
    } else {
      auto iter = storewide_page_key->find(msg.page_id);
      if (iter != storewide_page_key->end()) {
        auto page_key = iter->second;
        auto key = absl::Substitute("$0_$1_item_storewide", msg.item_type, page_key);
        auto& cost_info = (*p_bid_context->mutable_page_cost_info())[key];
        if (msg.cost > 0) {
          cost_info.set_cost(cost_info.cost() + msg.cost);
        }
        if (msg.target_cost > 0) {
          cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
        }
      }
    }
  }
  if (is_storewide_item_exp) {
    auto iter = storewide_pos_key->find(msg.pos_id);
    if (iter != storewide_pos_key->end()) {
      auto page_key = iter->second;
      auto key = absl::Substitute("$0_$1_item_storewide", msg.item_type, page_key);
      auto& cost_info = (*p_bid_context->mutable_page_cost_info())[key];
      if (msg.cost > 0) {
        cost_info.set_cost(cost_info.cost() + msg.cost);
      }
      if (msg.target_cost > 0) {
        cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
      }
    }
  }
  if (is_sotrewide_nature_exp && storewide_nature_key) {
    auto iter = storewide_nature_key->find(msg.roi_traffic_source);
    if (iter != storewide_nature_key->end()) {
      auto nature_key = iter->second;
      auto sw_key = absl::Substitute("$0_storewide", nature_key);
      auto& cost_info = (*p_bid_context->mutable_page_cost_info())[sw_key];
      if (msg.cost > 0 && msg.storewide_inc_type == 1) {
        cost_info.set_cost(cost_info.cost() + msg.cost);
      }
      if (msg.target_cost > 0 && msg.storewide_inc_type == 1) {
        cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
      }
    }
  }
  if (SPDM_enableStorewideFollowSelectCali() && storewide_page_follow_select_key) {
    bool is_follow_select = false;
    auto fs_iter = storewide_page_follow_select_key->find(msg.roi_traffic_source);
    if (fs_iter != storewide_page_follow_select_key->end()) {
      if (msg.storewide_inc_type == 1) {
        is_follow_select = true;
      } else {
        if (storewide_follow_select_tail) {
          if (storewide_follow_select_tail->IsOnFor(msg.campaign_id)) {
            is_follow_select = true;
          }
        }
      }
    }
    fs_iter = storewide_page_follow_select_key->find(absl::StrCat(msg.page_id));
    if (fs_iter != storewide_page_follow_select_key->end()) {
      is_follow_select = true;
    }
    if (is_follow_select) {
      auto& cost_info = (*p_bid_context->mutable_page_cost_info())["follow_select_storewide"];
      if (msg.cost > 0) {
        cost_info.set_cost(cost_info.cost() + msg.cost);
      }
      if (msg.target_cost > 0) {
        cost_info.set_target_cost(cost_info.target_cost() + msg.target_cost);
      }
    }
  }
  // target_cost
  p_bid_context->set_rt_cpa_bid(p_bid_context->cost() / std::max(p_bid_context->conv_num(), 1.0));
  p_bid_context->set_rt_roas(p_bid_context->gmv() / std::max(p_bid_context->cost(), 1.0));
  if (IsAdDelivery(msg, p_util_vals)) {
    p_bid_context->set_delivery_cnt(p_bid_context->delivery_cnt() + msg.msg_cnt);
    if (msg.page_id == 100022354 && msg.action_type == "AD_DELIVERY") {
      p_bid_context->set_delivery_reco_cnt(p_bid_context->delivery_reco_cnt() + msg.msg_cnt);
      p_bid_context->set_reco_gpm(p_bid_context->reco_gpm() + msg.reco_gpm);
    }
    p_bid_context->set_dry_up_base_value(p_bid_context->adjust_auto_value_rate());
  }
  if (msg.target_cost > 0) {
    uint64_t today_date = GetTodayDate();
    uint64_t delivery_date = GetDateMs(msg.delivery_timestamp);
    if (exp_config.enable_cross_day_conver_save > 0.1 &&
        today_date != delivery_date) {
      p_bid_context->set_cross_day_target_cost(p_bid_context->cross_day_target_cost() + msg.target_cost);
    }
    p_bid_context->set_target_cost(p_bid_context->target_cost() + msg.target_cost);
    // 净成交场景，预期花费需要使用支付预期花费和退款预期花费一起预估
    double author_pure_gmv_ratio = 1.0;
    if (p_bid_context->bid_assist_type() == 1) {
      // 净成交场景支付总预期花费
      p_bid_context->set_total_callback_purchase_target(
          p_bid_context->total_callback_purchase_target() + msg.target_cost);
      auto pure_ratio_conf = MerchantKconfUtil::authorPureGmvRatio();
      if (pure_ratio_conf) {
        auto iter_pure = pure_ratio_conf->find(p_bid_context->author_id());
        if (iter_pure != pure_ratio_conf->end()) {
          author_pure_gmv_ratio = iter_pure->second;
        }
      }
      // 覆盖预期花费
      p_bid_context->set_target_cost(p_bid_context->pure_roi_purchase_target() +
          (p_bid_context->total_callback_purchase_target() - p_bid_context->callback_refund_target_cost()
          - p_bid_context->pure_roi_purchase_target()) * author_pure_gmv_ratio);
    }
    if (msg.is_soft) {
      p_bid_context->set_softad_target_cost(p_bid_context->softad_target_cost() + msg.target_cost);
    }
    if (msg.page_id != 100022354 && !msg.is_store_wide_roi_reco_conv) {
      p_bid_context->set_ad_target_cost(p_bid_context->ad_target_cost() + msg.target_cost);
    }
    if (msg.is_store_wide_roi_reco_conv && msg.storewide_inc_type == 1) {
      p_bid_context->set_uplift_target_cost(p_bid_context->uplift_target_cost() + msg.target_cost);
    }
    if (msg.is_author_fans == 1) {
      p_bid_context->set_is_author_fans_target_cost(
          p_bid_context->is_author_fans_target_cost() + msg.target_cost);
    }
  }
  // indirect_target_cost
  uint64_t msg_timestamp = msg.msg_timestamp / 1e3;  // us -> ms
  uint64_t delivery_timestamp = msg.delivery_timestamp;  // ms
  uint64_t delta_ts =
        GetDateBeginTimeStampMs(msg_timestamp) - GetDateBeginTimeStampMs(delivery_timestamp);
  int32_t conv_delay_days = delta_ts / (3600 * 24 * 1000);
  const std::string& conv_tag = msg.is_conversion ? "is_conv" : "no_conv";
  const std::string& indirect_conv_tag = msg.is_indirect_conversion ? "is_indirect_conv" : "no_indirect_conv";
  const std::string& conv_delay_tag = conv_delay_days > 7 ? ">7" : "<=7";

  if (msg.ocpc_action_type == "EVENT_ORDER_PAIED" ||
      msg.ocpc_action_type == "AD_MERCHANT_T7_ROI" ||
      msg.ocpc_action_type == "AD_MERCHANT_ROAS") {
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_bid_server",
            "target_cost_tag", msg.ocpc_action_type, conv_tag, indirect_conv_tag, conv_delay_tag);
    ks::infra::PerfUtil::IntervalLogStash(msg.target_cost, "ad.ad_bid_server",
            "target_cost", msg.ocpc_action_type, conv_tag, indirect_conv_tag, conv_delay_tag);
    ks::infra::PerfUtil::IntervalLogStash(msg.indirect_target_cost, "ad.ad_bid_server",
            "indirect_target_cost", conv_tag, indirect_conv_tag, conv_delay_tag);
  }

  if (msg.indirect_target_cost > 0) {
    if (conv_delay_days < 1) {
      p_bid_context->set_indirect_target_cost_0(
        p_bid_context->indirect_target_cost_0() + msg.indirect_target_cost);
    }
    if (conv_delay_days <= 3) {
      p_bid_context->set_indirect_target_cost_3(
        p_bid_context->indirect_target_cost_3() + msg.indirect_target_cost);
    }
    if (conv_delay_days <= 7) {  // 投放当日 + 投后 7 天
      p_bid_context->set_indirect_target_cost_7(
        p_bid_context->indirect_target_cost_7() + msg.indirect_target_cost);
    }
  }

  if (msg.pred_target_cost > 0) {
    p_bid_context->set_pred_target_cost(p_bid_context->pred_target_cost() + msg.pred_target_cost);
  }
  if (msg.target_gmv > 0) {
    p_bid_context->set_target_gmv(p_bid_context->target_gmv() + msg.target_gmv);
  }
  if (msg.record_gsp_price > 0) {
    p_bid_context->set_record_gsp_price(p_bid_context->record_gsp_price() + msg.record_gsp_price);
  }
  if (msg.separate_gsp_price > 0) {
    p_bid_context->set_separate_gsp_price(
      p_bid_context->separate_gsp_price() + msg.separate_gsp_price);
  }
  if (msg.ecpm > 0) {
    p_bid_context->set_total_ecpm(p_bid_context->total_ecpm() + msg.ecpm);
  }
  if (msg.price_before_billing_separate > 0) {
    p_bid_context->set_price_before_billing_separate(p_bid_context->price_before_billing_separate() +
                                                     msg.price_before_billing_separate);
  }
  if (msg.price_after_billing_separate > 0) {
    p_bid_context->set_price_after_billing_separate(p_bid_context->price_after_billing_separate() +
                                                    msg.price_after_billing_separate);
  }
  if (msg.auction_bid > 0) {
    p_bid_context->set_total_auction_bid(p_bid_context->total_auction_bid() + msg.auction_bid);
  }
  if (msg.gimbal_price_before > 0) {
    p_bid_context->set_gimbal_price_before(p_bid_context->gimbal_price_before() +
                                                    msg.gimbal_price_before);
  }
  if (msg.customer_hc_cost < 0) {  // 都是负数
    p_bid_context->set_customer_hc_cost(p_bid_context->customer_hc_cost() +
                                                    msg.customer_hc_cost);
  }
}

inline void BidContextUpdateProcessor::UpdateTargetValue(OcpmBidContext* p_bid_context,
                                                         const ProcessUtilValues& p_util_vals,
                                                         const MerchantOcpmExpConfig& exp_config,
                                                         const MerchantOcpmMsg& msg) {
  double cost = p_bid_context->cost();
  bool is_storewide_roas = p_util_vals.is_storewide_roas && p_util_vals.start_bid_rate > 0.0
                           && p_util_vals.start_bid_rate < 1.0;
  bool is_mobile_order_start_bid = p_util_vals.is_fanstop && p_util_vals.start_bid_rate > 1.0 &&
                              p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED";
  bool is_stable_roi_auto_bid = ((p_util_vals.is_unit_ocpm_roas_pc ||
      (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI" && !p_util_vals.is_cbo)) ||
      msg.is_roas_hosting_live) && p_util_vals.start_bid_rate > 0.0 &&
      p_util_vals.start_bid_rate < 1.0;
  bool is_stable_eop_auto_bid = (p_util_vals.is_unit_ocpm_eop_pc ||
        msg.is_order_hosting_live) && p_util_vals.start_bid_rate > 1.0;
  if (is_storewide_roas || (is_stable_roi_auto_bid || is_stable_eop_auto_bid)) {
    cost = cost - msg.cost;
  }
  if ((IsLEZero(cost) && exp_config.enable_yesterday_auto_bid_ratio == 0)
     || IsLEZero(p_bid_context->adjust_auto_value_rate())
     || ((IsLEZero(p_bid_context->auto_cpa_bid()) && IsLEZero(p_bid_context->auto_roi_ratio())))) {
    if (is_storewide_roas) {
      p_bid_context->set_adjust_auto_value_rate(1.0 / p_util_vals.start_bid_rate);
      ks::infra::PerfUtil::IntervalLogStash(p_bid_context->adjust_auto_value_rate() * 1000,
                                            "ad.ad_bid_server",
                                            "cob_store_wide_startrate_adjust_value",
                                            std::to_string(p_bid_context->author_id()));
      return;
    }
    if (is_mobile_order_start_bid) {
      p_bid_context->set_adjust_auto_value_rate(1.0 * std::max(1.0, p_util_vals.start_bid_rate));
      ks::infra::PerfUtil::IntervalLogStash(p_bid_context->adjust_auto_value_rate() * 1000,
                                            "ad.ad_bid_server",
                                            "mobile_order_start_bid_adjust_value",
                                            std::to_string(p_bid_context->unit_id()));
      return;
    }
    if (is_stable_roi_auto_bid || is_stable_eop_auto_bid) {
      const auto espLiveHostingStableBidTail = MerchantKconfUtil::espLiveHostingStableBidTail();
      bool esp_live_hosting_stable_bid = espLiveHostingStableBidTail &&
           espLiveHostingStableBidTail->IsOnFor(msg.campaign_id);
      const auto espLiveDefaultStableBidTail = MerchantKconfUtil::espLiveDefaultStableBidTail();
      bool esp_live_default_bid = espLiveDefaultStableBidTail &&
           espLiveDefaultStableBidTail->IsOnFor(msg.unit_id);
      if (is_stable_roi_auto_bid && ((!p_util_vals.is_cbo && esp_live_default_bid) ||
        (p_util_vals.is_cbo && esp_live_hosting_stable_bid)) && p_util_vals.start_bid_rate > 0) {
        p_bid_context->set_adjust_auto_value_rate(1.0 / p_util_vals.start_bid_rate);
      }
      if (is_stable_eop_auto_bid && ((!p_util_vals.is_cbo && esp_live_default_bid) ||
         (p_util_vals.is_cbo && esp_live_hosting_stable_bid))) {
        p_bid_context->set_adjust_auto_value_rate(1.0 * std::max(1.0, p_util_vals.start_bid_rate));
      }
      return;
    }
    p_bid_context->set_adjust_auto_value_rate(1.0 / std::max(1.0, p_util_vals.start_bid_rate));
  }
}

inline void BidContextUpdateProcessor::UpdateBidContext(const MerchantOcpmMsg& msg,
  const MerchantOcpmBidConfig& strategy_config, const MerchantOcpmExpConfig& exp_config,
  OcpmBidContext* p_bid_context, const ProcessUtilValues& p_util_vals) {
  if (IsAdDelivery(msg, p_util_vals)) {
    if (msg.delivery_timestamp > p_bid_context->last_delivery_timestamp_ms()) {
      p_bid_context->set_last_delivery_timestamp_ms(msg.delivery_timestamp);
    }
  }
  if (p_bid_context->cost() <= 0 && msg.cost > 0) {
    p_bid_context->set_cost_start_timestamp_ms(msg.delivery_timestamp);
  }
  UpdateCostSpeedContext(msg, strategy_config.cost_speed_config(),
                         p_bid_context->mutable_cost_speed_context(), p_util_vals);
  p_bid_context->set_rt_cost_speed(p_bid_context->cost_speed_context().batch_value_speed());
  UpdateDeliverySpeedContext(msg, strategy_config.delivery_speed_config(),
    p_bid_context->mutable_delivery_speed_context(), p_util_vals);
  p_bid_context->set_rt_delivery_speed(p_bid_context->delivery_speed_context().batch_value_speed());
  if (strategy_config.enable_collect_softad_cost()) {
    double ratio = (p_bid_context->softad_target_cost() + strategy_config.collect_softad_cost_smooth())
          / (1.0 + p_bid_context->softad_cost() + strategy_config.collect_softad_cost_smooth());
    p_bid_context->set_softad_diff_ratio(ratio);
  }
  UpdateTargetValue(p_bid_context, p_util_vals, exp_config, msg);
}

std::string BidContextUpdateProcessor::BuildRedisKey(int64_t unit_id, int64_t version) const {
  return absl::Substitute("$0_campaign_step1_tag_$1_$2", serving_base::GetHostName(), unit_id, version);
}

inline void BidContextUpdateProcessor::CheckSyncContextRemote(
  const MerchantOcpmBidConfig& strategy_config, OcpmBidContext* p_bid_context,
  const MerchantOcpmMsg& msg) {
  if (FLAGS_enable_bid_no_diff_switch) {
    if (BidNoDiffRedisSave::Instance()) {
      p_bid_context->set_last_sync_context_timestamp(TimeUtil::Instance().GetTimestamp());
      BidNoDiffRedisSave::Instance()->SaveNoDiffStepOne(p_bid_context, msg);
    }
  } else {
    if (ControllerDataMgr::Instance().EnableTTL() ||
        IsSyncContextRemote(strategy_config,
          p_bid_context->last_sync_context_timestamp())) {
      // update remote context
      p_bid_context->set_last_sync_context_timestamp(TimeUtil::Instance().GetTimestamp());
      MerchantDataMgr::Instance().RedisSave(
          p_bid_context->bid_context_key(), *p_bid_context, REDIS_STORE_TIME_SEC);
    }
  }
}

inline void BidContextUpdateProcessor::UniverseMonitorLog(const MerchantOcpmMsg& msg,
                                                          const MerchantOcpmExpConfig& exp_config,
                                                          ProcessUtilValues* p_util_vals,
                                                          OcpmBidContext* p_bid_context) {
  if (p_bid_context == nullptr || p_util_vals == nullptr) {
    return;
  }
  if (!IsUniverseMonitorLog(msg)) {
    return;
  }
  LOG_EVERY_N(INFO, 1) << "BidContextUpdateProcessor_Process"
                        << " unit_id=" << p_bid_context->unit_id()
                        << " unit_tail=" << (msg.unit_id % 100)
                        << " account_id=" << p_bid_context->account_id()
                        << " campaign_id=" << p_bid_context->campaign_id()
                        << " ocpc_action_type=" << p_bid_context->ocpc_action_type()
                        << " campaign_type=" << p_bid_context->campaign_type()
                        << " live_stream_id=" << p_bid_context->live_stream_id()
                        << " group_tag=" << p_bid_context->group_tag()
                        << " group_tag_num=" << p_bid_context->group_tag_num()
                        << " speed_type=" << p_bid_context->speed_type()
                        << " first_industry_name=" << p_bid_context->first_industry_name()
                        << " first_delivery_timestamp_ms=" << p_bid_context->first_delivery_timestamp_ms()
                        << " cpa_bid=" << p_bid_context->cpa_bid()
                        << " relax_cpa_bid=" << p_bid_context->relax_cpa_bid()
                        << " msg_cpa_bid=" << msg.cpa_bid
                        << " total_cost=" << p_bid_context->cost()
                        << " target_cost=" << p_bid_context->target_cost()
                        << " gmv=" << p_bid_context->gmv()
                        << " target_gmv=" << p_bid_context->target_gmv()
                        << " conv_num=" << p_bid_context->conv_num()
                        << " total_auction_bid=" << p_bid_context->total_auction_bid()
                        << " adjust_auto_value_rate=" << p_bid_context->adjust_auto_value_rate()
                        << " enable_ocpm_cache_target_cost=" << exp_config.enable_ocpm_cache_target_cost;
}

}  // namespace bid_server
}  // namespace ks
