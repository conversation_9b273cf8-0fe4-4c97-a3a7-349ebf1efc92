// Authors: <AUTHORS>
#pragma once

#include <memory>
#include <iostream>
#include <string>
#include "teams/ad/ad_proto/kuaishou/ad/dw/ad_log_for_algo.pb.h"
#include "teams/ad/ad_base/src/common/latency_record.h"
#include "teams/ad/bid_server/framework/manager/unit_shard_mgr.h"
#include "teams/ad/bid_server/application/bid_server_merchant/kconf/kconf.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/common/merchant_msg_base.h"
#include "teams/ad/bid_server/application/bid_server_merchant/util/util.h"
#include "teams/ad/bid_server/application/bid_server_merchant/data_mgr/merchant_data_mgr.h"
#include "teams/ad/bid_server/framework/utils/spdm_switches.h"
#include "teams/ad/bid_server/framework/utils/time.h"
#include "teams/ad/bid_server/framework/index/default/bid_state_info_ad_table_helper.h"
#include "teams/ad/bid_server/framework/utils/kconf.h"
namespace ks {
namespace bid_server {

struct MerchantOcpmMsg : public MsgBase {
  MerchantOcpmMsg() = default;
  ~MerchantOcpmMsg() = default;
  bool ToPb(google::protobuf::Message& pb) const; // NOLINT
  bool FromPb(const google::protobuf::Message& pb);
  explicit MerchantOcpmMsg(const AdLogForAlgo& log) {
    bid_info_ptr = BID_STATE.GetBidStateInfo(log.unit_id());

    is_author_fans = log.is_author_fans();
    if (MerchantKconfUtil::enableAccIncrement()) {
      is_increment_explore = log.is_increment_explore();
    }
    is_store_wide_roi_reco_conv = GetIsStoreWideRoiRecoConv(log);
    campaign_type = log.campaign_type();
    ocpc_action_type = log.ocpc_action_type();
    promotion_type = log.promotion_type();
    item_type = log.item_type();
    author_id = log.author_id();
    account_id = log.account_id();
    live_stream_id = log.live_stream_id();
    unit_id = log.unit_id();
    campaign_id = log.campaign_id();
    roi_traffic_source = log.roi_traffic_source();
    cost = GetCost(log, campaign_type);
    page_id = log.page_id();
    // roi_ratio auto_roas
    scene_oriented_type = log.scene_oriented_type();
    bool is_qiandu = ocpc_action_type == "AD_LIVE_AUDIENCE" || ocpc_action_type == "AD_MERCHANT_FOLLOW";
    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      is_storewide_switch = (scene_oriented_type == 21 || scene_oriented_type == 30) && !is_qiandu;
    } else {
      is_storewide_switch = log.aggr_merchant_ocpm_msg().is_storewide_switch();
    }
    is_storewide_with_order = (ocpc_action_type == "AD_STOREWIDE_ROAS" || is_storewide_switch);
    GetRoiRatio(log);
    if (is_storewide_with_order) {
      campaign_bid_info_ptr = BidIndexManager::Instance().GetIndex()->GetCampaignBidStateInfo(
          unit_id, campaign_id, account_id, author_id);
    }
    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      roi_ratio = GetRoiRatioV2(log, campaign_type, campaign_bid_info_ptr, is_storewide_switch);
    } else {
      roi_ratio = log.aggr_merchant_ocpm_msg().roi_ratio();
    }
    auto account_tail = MerchantKconfUtil::enableUseIndexRoiRatioAccountTail();
    if (MerchantKconfUtil::enableUseIndexRoiRatio() &&
        account_tail && account_tail->IsOnFor(account_id)) {
      roi_ratio = GetRoiRatioV2(log, campaign_type, campaign_bid_info_ptr, is_storewide_switch);
    }
    is_storewide_with_t7 = (MerchantKconfUtil::storewideAddT7Tail()->count(campaign_id % 100) &&
      scene_oriented_type == 21 && ocpc_action_type == "AD_MERCHANT_T7_ROI");
    cpa_bid = log.cpa_bid();
    interactive_form = log.interactive_form();
    delivery_timestamp = log.delivery_timestamp();
    ad_attributed_timestamp = log.ad_attributed_timestamp();
    pos_id = log.pos_id();
    origin_group_tag = GetMerchantBidGroupTag(log.bid_server_group_tag());
    group_tag = origin_group_tag;
    auto_cpa_bid = log.auto_cpa_bid();
    action_type = log.action_type();
    scene_oriented_type = log.scene_oriented_type();
    medium_attribute = log.medium_attribute();
    unify_ltv = log.unify_ltv();
    storewide_inc_type = log.storewide_inc_type();
    predict_unify_cvr = log.predict_unify_cvr();
    predict_unify_ctr = log.predict_unify_ctr();
    live_room_pattern = log.live_room_pattern();
    cpm = log.cpm();
    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      is_conversion = GetIsConv(log, is_storewide_switch);
    } else {
      is_conversion = log.aggr_merchant_ocpm_msg().is_conversion();
    }

    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      is_indirect_conversion = !is_conversion && GetIsIndirectConv(log);  // 避免重算
    } else {
      is_indirect_conversion = log.aggr_merchant_ocpm_msg().is_indirect_conversion();
    }

    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      gmv = GetGmv(log, is_conversion, is_storewide_switch);
    } else {
      gmv = log.aggr_merchant_ocpm_msg().gmv_sum();
    }

    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      indirect_gmv = is_indirect_conversion ? log.callback_purchase_amount() * 1000 : 0;
    } else {
      indirect_gmv = log.aggr_merchant_ocpm_msg().indirect_gmv_sum();
    }

    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      conv_num = GetConvNum(log, is_conversion);
    } else {
      conv_num = log.aggr_merchant_ocpm_msg().conv_num();
    }

    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      indirect_conv_num = GetConvNum(log, is_indirect_conversion);
    } else {
      indirect_conv_num = log.aggr_merchant_ocpm_msg().indirect_conv_num();
    }

    if (conv_num > 0) {
      msg_type = MerchantMsgType::kConvMsg;
    }
    ad_type = absl::Substitute("$0_$1", item_type, ocpc_action_type);
    msg_timestamp = TimeUtil::Instance().GetTimestamp();
    // target cost
    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      if (conv_num > 0) {
        if ((ocpc_action_type == "AD_MERCHANT_ROAS" || ocpc_action_type == "AD_FANS_TOP_ROI" ||
            ocpc_action_type == "AD_STOREWIDE_ROAS" || ocpc_action_type == "AD_MERCHANT_T7_ROI" ||
            is_storewide_switch) && roi_ratio > 0) {
          target_cost = gmv / roi_ratio;
        } else {
          target_cost = cpa_bid;
        }
      }
    } else {
      target_cost = log.aggr_merchant_ocpm_msg().target_cost_sum();
    }

    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      if (indirect_conv_num > 0) {  // 除了 roi7, 其它目标间接转化数据只用于打点比较
        if ((ocpc_action_type == "AD_MERCHANT_ROAS" || ocpc_action_type == "AD_FANS_TOP_ROI" ||
            is_storewide_switch ||
            ocpc_action_type == "AD_STOREWIDE_ROAS" || ocpc_action_type == "AD_MERCHANT_T7_ROI")
            && roi_ratio > 0) {
          indirect_target_cost = indirect_gmv / roi_ratio;
        } else {
          indirect_target_cost = cpa_bid;
        }
      }
    } else {
      indirect_target_cost = log.aggr_merchant_ocpm_msg().indirect_target_cost_sum();
    }

    // 关停后回传的 msg 可能取不到索引 ; 纯用 scene_oriented_type 数据流拼接又好像有问题
    is_hosting_live =
        (bid_info_ptr &&
         (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
          (SPDM_enableLSPHosting() &&
           bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE)) ||
        scene_oriented_type == 23 ||
        (SPDM_enableLSPHosting() && scene_oriented_type == 35));
    bool is_new_customer_hosting_live =
        campaign_type == "LIVE_STREAM_PROMOTE" &&
        ((bid_info_ptr &&
         bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_STORE_NEW_CUSTOMER_HOSTING_FEATURE) ||
        scene_oriented_type == 26);
    is_hosting_live = is_hosting_live || is_new_customer_hosting_live;
    const auto roiRatioFromAdLogTail = MerchantKconfUtil::roiRatioFromAdLog();
    bool roi_ratio_from_ad_log = roiRatioFromAdLogTail && roiRatioFromAdLogTail->IsOnFor(campaign_id);
    if (is_hosting_live) {
      campaign_bid_info_ptr = BidIndexManager::Instance().GetIndex()->GetCampaignBidStateInfo(
          unit_id, campaign_id, account_id, author_id);
      if (!SPDM_enableAggrMerchantOcpmMsg()) {
        if (campaign_bid_info_ptr && conv_num > 0 && campaign_type == "LIVE_STREAM_PROMOTE") {
          if (!roi_ratio_from_ad_log) {
            if (campaign_bid_info_ptr->roi_ratio > 0) {
              target_cost = gmv / campaign_bid_info_ptr->roi_ratio;
            } else if (campaign_bid_info_ptr->cpa_bid > 0) {
              target_cost = campaign_bid_info_ptr->cpa_bid;
            }
          } else {
            if (log.project_roi_ratio() > 0) {
              target_cost = gmv / log.project_roi_ratio();
            } else if (log.project_cpa_bid() > 0) {
              target_cost = log.project_cpa_bid();
            }
          }
        }
        if (ocpc_action_type  == "AD_LIVE_AUDIENCE") {
          target_cost = 0;
        }
        if (campaign_bid_info_ptr && gmv > 0 && campaign_type == "LIVE_STREAM_PROMOTE" &&
            ocpc_action_type  == "AD_LIVE_AUDIENCE") {
          if (!roi_ratio_from_ad_log) {
            if (campaign_bid_info_ptr->roi_ratio > 0) {
              target_cost = gmv / campaign_bid_info_ptr->roi_ratio;
            } else if (campaign_bid_info_ptr->cpa_bid > 0) {
              target_cost = campaign_bid_info_ptr->cpa_bid;
            }
          } else {
            if (log.project_roi_ratio() > 0) {
              target_cost = gmv / log.project_roi_ratio();
            } else if (log.project_cpa_bid() > 0) {
              target_cost = log.project_cpa_bid();
            }
          }
        }
      } else {
        target_cost = log.aggr_merchant_ocpm_msg().hosting_live_target_cost_sum();
      }

      if (campaign_bid_info_ptr) {
        project_cpa_bid = campaign_bid_info_ptr->cpa_bid;
        project_roi_ratio = campaign_bid_info_ptr->roi_ratio;
      } else {
        project_cpa_bid = log.project_cpa_bid();
        project_roi_ratio = log.project_roi_ratio();
      }
    }
    is_roas_hosting_live = is_hosting_live && project_roi_ratio > 0.0;
    is_roas_hosting_live_roas_unit = is_roas_hosting_live && ocpc_action_type == "AD_MERCHANT_ROAS";
    is_roas_hosting_live_order_unit = is_roas_hosting_live && ocpc_action_type == "EVENT_ORDER_PAIED";
    is_roas_hosting_live_t7_roas_unit = is_roas_hosting_live && ocpc_action_type == "AD_MERCHANT_T7_ROI";
    is_order_hosting_live = is_hosting_live && project_cpa_bid > 0;

    // target gmv
    // 当前 target_gmv 没有使用
    if ((is_storewide_switch ||
        ocpc_action_type == "AD_MERCHANT_ROAS" || ocpc_action_type == "AD_FANS_TOP_ROI" ||
        ocpc_action_type == "AD_STOREWIDE_ROAS" || ocpc_action_type == "AD_MERCHANT_T7_ROI")
        && roi_ratio > 0) {
      target_gmv = cost * roi_ratio;
    }
    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      if (log.ad_log_aggr_message_value().is_aggr_msg()) {
        ecpm = static_cast<double>(log.ad_log_aggr_message_value().cpm_sum()) / 1000000.0;
        {
          front_ecpm = log.ad_log_aggr_message_value().front_cpm_sum() / 1e6;
          front_auction_bid = log.ad_log_aggr_message_value().front_auction_bid_sum();
          origin_price = log.ad_log_aggr_message_value().origin_price_sum();
          if (page_id == 100022354) {  // 推荐侧的用 gpm 转化
            front_ecpm = log.ad_log_aggr_message_value().gpm_sum() / auto_roas;
            origin_price  = log.ad_log_aggr_message_value().gpm_sum() / auto_roas;
            front_auction_bid = log.ad_log_aggr_message_value().gpm_sum() / auto_roas;
            reco_gpm = log.ad_log_aggr_message_value().gpm_sum() / roi_ratio;
          }
        }
        if (cost > 0) {
          price_ratio = ecpm / cost;
        }
      } else {
        ecpm = static_cast<double>(log.cpm()) / 1000000.0;
        {
          front_ecpm = log.front_cpm() / 1e6;
          front_auction_bid = log.front_auction_bid();
          origin_price = log.origin_price();
          if (page_id == 100022354) {
            front_ecpm = log.gpm() / auto_roas;  // gpm 单位是厘
            origin_price = log.gpm() / auto_roas;
            front_auction_bid = log.gpm() / auto_roas;
            reco_gpm = log.gpm() / roi_ratio;
          }
        }
        if (cost > 0) {
          price_ratio = ecpm / cost;
        }
      }
    } else {
      ecpm = static_cast<double>(log.aggr_merchant_ocpm_msg().cpm_sum()) / 1000000.0;
      {
        front_ecpm = log.aggr_merchant_ocpm_msg().front_cpm_sum() / 1e6;
        if (page_id == 100022354) {
          front_ecpm = log.aggr_merchant_ocpm_msg().gpm_sum() / auto_roas;  // gpm 单位是厘
        }
        front_auction_bid = log.aggr_merchant_ocpm_msg().front_auction_bid_sum();
        origin_price = log.aggr_merchant_ocpm_msg().origin_price_sum();
        if (page_id == 100022354) {  // 推荐侧的用 gpm 转化
            front_ecpm = log.aggr_merchant_ocpm_msg().gpm_sum() / auto_roas;
            origin_price  = log.aggr_merchant_ocpm_msg().gpm_sum() / auto_roas;
            front_auction_bid = log.aggr_merchant_ocpm_msg().gpm_sum() / auto_roas;
        }
      }
      // 当前 price_ratio 没有使用
      if (cost > 0) {
        price_ratio = ecpm / cost;
      }
    }

    speed_type = log.speed_type();

    // set other info
    if (log.charge_action_type() != "AD_DELIVERY_NO_CHARGE") {
      if (!SPDM_enableAggrMerchantOcpmMsg()) {
        record_gsp_price = log.record_gsp_price();
      } else {
        record_gsp_price = log.aggr_merchant_ocpm_msg().record_gsp_price_sum();
      }

      // 当前 separate_gsp_price 没有使用
      separate_gsp_price = log.separate_gsp_price();
      if (log.price() > 0 && log.record_gsp_price() == 0) {
        if (!SPDM_enableAggrMerchantOcpmMsg()) {
          record_gsp_price = log.price();
        } else {
          record_gsp_price = log.aggr_merchant_ocpm_msg().price_sum();
        }

        separate_gsp_price = log.price();
      }
    }

    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      if (log.ad_log_aggr_message_value().is_aggr_msg()) {
        auction_bid = static_cast<double>(log.ad_log_aggr_message_value().auction_bid_sum());
      } else {
        auction_bid = static_cast<double>(log.auction_bid());
      }
    } else {
      auction_bid = static_cast<double>(log.aggr_merchant_ocpm_msg().auction_bid_sum());
    }

    if (action_type == "AD_DELIVERY") {
      if (is_storewide_switch ||
          ocpc_action_type == "AD_MERCHANT_ROAS" || ocpc_action_type == "AD_FANS_TOP_ROI"
          || ocpc_action_type == "AD_STOREWIDE_ROAS" || ocpc_action_type == "AD_MERCHANT_T7_ROI") {
        pred_conv = auction_bid * auto_roas;
      } else {
        pred_conv = IsLEZero(auto_cpa_bid) ? 0.0 : auction_bid / auto_cpa_bid;
      }
    }
    // 累加系统预期花费，从曝光开始
    if (campaign_type == "LIVE_STREAM_PROMOTE" &&
        (ocpc_action_type == "AD_MERCHANT_ROAS" || ocpc_action_type == "EVENT_ORDER_PAIED")) {
      if (hasClientShow(log, item_type, action_type)) {
        if (!SPDM_enableAggrMerchantOcpmMsg()) {
          pred_target_cost = static_cast<double>(log.pred_target_cost());
        } else {
          pred_target_cost = static_cast<double>(log.aggr_merchant_ocpm_msg().pred_target_cost_sum());
        }
      }
    }
    is_soft = log.ad_queue_type() == "SOFT_AD_QUEUE";
    if (action_type == "AD_DELIVERY") {
      if (!SPDM_enableAggrMerchantOcpmMsg()) {
        if (log.ad_log_aggr_message_value().is_aggr_msg()) {
          price_before_billing_separate = log.ad_log_aggr_message_value().price_before_billing_separate_sum();
          price_after_billing_separate = log.ad_log_aggr_message_value().price_after_billing_separate_sum();
        } else {
          price_before_billing_separate = log.price_before_billing_separate();
          price_after_billing_separate = log.price_after_billing_separate();
        }
      } else {
        price_before_billing_separate = log.aggr_merchant_ocpm_msg().price_before_billing_separate_sum();
        price_after_billing_separate = log.aggr_merchant_ocpm_msg().price_after_billing_separate_sum();
      }
    }
    page_id = log.page_id();
    // 移动端标识
    account_type = log.account_type();
    bid_type = log.bid_type();
    bid_strategy = log.bid_strategy();
    payer_id = log.payer_id();
    event_server_timestamp = log.event_server_timestamp();
    if (IsSearchFlow(page_id)) {
      if (!(log.bidword_params().winfo_id() > 0 || log.bidword_params().extend_search())) {
        is_search_antou = true;
      }
    }
    // 出价系数 & 带出价系数权重的消耗
    bid_coef = log.bid_coef();
    cpa_coef = log.cpa_coef();
    bid_coef_cost = log.aggr_merchant_ocpm_msg().bid_coef_cost_sum();
    cpa_coef_cost = log.aggr_merchant_ocpm_msg().cpa_coef_cost_sum();
    // 消息数量
    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      if (log.ad_log_aggr_message_value().is_aggr_msg()) {
        msg_cnt = log.ad_log_aggr_message_value().aggr_msg_cnt();
      } else {
        msg_cnt = 1;
      }
    } else {
      msg_cnt = log.aggr_merchant_ocpm_msg().msg_cnt();
    }
    // 填充 gimbal_price_before
    gimbal_price_before = GetGimbalPriceBefore(log);
    // 填充 customer_hc_cost
    customer_hc_cost = GetCustomerHcCost(log);
    // 填充全站计划是否全互斥
    if (is_storewide_switch || ocpc_action_type == "AD_STOREWIDE_ROAS") {
      bool unit_reject_type = bid_info_ptr && bid_info_ptr->reject_type == 1;
      bool campaign_reject_type = campaign_bid_info_ptr && campaign_bid_info_ptr->is_storewide_incompatible;
      if (MerchantKconfUtil::fixStorewideIncompatibleTypeCampaignTail()->count(campaign_id % 100) > 0) {
        is_storewide_incompatible = campaign_reject_type;
      } else {
        is_storewide_incompatible = unit_reject_type;
      }
      if (unit_reject_type != campaign_reject_type) {
        LOG_EVERY_N(INFO, 1000) << "wangyang10_debug: " << campaign_id << ", "
                  << unit_reject_type << ", "
                  << campaign_reject_type;
      }
    }

    pred_target_cost_roas = log.aggr_merchant_ocpm_msg().pred_target_cost_roas();
    pred_target_cost_eop = log.aggr_merchant_ocpm_msg().pred_target_cost_eop();
    // 投放净成交场景: https://docs.corp.kuaishou.com/k/home/<USER>/fcACfHOCMj-gvyP9TN0vIQZ5c // NOLINT
    // 原 gmv 和 target_cost 计算：bid_assist_type = 1 and action_type = 'EVENT_ORDER_PAIED',这里 is_conversion 都为 false // NOLINT
    // 以下字段来自延迟 1h 流的计算:
    // 对每条支付行为拼接 1h 退款得到 bid_assist_type = 1 and action_type = 'EVENT_ORDER_PAIED_LATE_FOR_PURE_ROI' // NOLINT
    // 其中 pure_roi_purchase_amount_exact = callback_purchase_amount - callback_refund_amount_exact, 当 > 0 时 is_conversion = true // NOLINT
    callback_refund_amount_exact_sum = log.aggr_merchant_ocpm_msg().callback_refund_amount_exact_sum();
    callback_refund_target_cost_sum = log.aggr_merchant_ocpm_msg().callback_refund_target_cost_sum();
    pure_roi_purchase_amount_exact_sum = log.aggr_merchant_ocpm_msg().pure_roi_purchase_amount_exact_sum();
    pure_roi_purchase_target_cost_sum = log.aggr_merchant_ocpm_msg().pure_roi_purchase_target_cost_sum();

    if (SPDM_enableMerchantOcpmMsgPerf()
        && ad_base::AdRandom::GetInt(1, 100000) <= MerchantKconfUtil::merchantOcpmMsgPerfRate()) {
      FieldsPerf();
    }
    // 测试后删除
    if (SPDM_enablePerfNobidCasePrice() && bid_info_ptr &&
        (KS_UNLIKELY(bid_info_ptr->bid_assist_type == 1 ||
        MerchantKconfUtil::enablePerfNobidCasePriceCampaign()->count(campaign_id) > 0))) {   // NOLINT
      perf::Interval(target_cost, "merchant_ocpm_msg_ad_log",
                    "target_cost",
                    absl::StrCat(campaign_id),
                    group_tag,
                    account_type,
                    ocpc_action_type);
      perf::Interval(gmv, "merchant_ocpm_msg_ad_log",
                    "gmv",
                    absl::StrCat(campaign_id),
                    group_tag,
                    account_type,
                    ocpc_action_type);
    }
  }
  explicit MerchantOcpmMsg(int64_t unit_id_param) {
    bid_info_ptr = BID_STATE.GetBidStateInfo(unit_id_param);

    account_id = bid_info_ptr->account_id;
    campaign_id = bid_info_ptr->campaign_id;
    unit_id = bid_info_ptr->unit_id;
    author_id = bid_info_ptr->author_id;
    payer_id = bid_info_ptr->payer_id;

    account_type = kuaishou::ad::AdEnum::AdDspAccountType_Name(bid_info_ptr->account_type);
    campaign_type = kuaishou::ad::AdEnum::CampaignType_Name(bid_info_ptr->campaign_type);
    ocpc_action_type = kuaishou::ad::AdActionType_Name(bid_info_ptr->ocpx_action_type);

    bid_type = kuaishou::ad::AdEnum::BidType_Name(bid_info_ptr->bid_type);
    bid_strategy = kuaishou::ad::AdEnum::BidStrategy_Name(bid_info_ptr->bid_strategy);
    speed_type = bid_info_ptr->speed_type;

    scene_oriented_type = bid_info_ptr->scene_oriented_type;
    item_type = kuaishou::ad::AdEnum::ItemType_Name(bid_info_ptr->item_type);
    promotion_type = kuaishou::ad::AdEnum::CampaignPromotionType_Name(bid_info_ptr->promotion_type);
    is_storewide_incompatible = bid_info_ptr->is_storewide_incompatible;

    cpa_bid = bid_info_ptr->cpa_bid;
    roi_ratio = bid_info_ptr->roi_ratio;
    // 测试后删除
    if (SPDM_enablePerfNobidCasePrice() && bid_info_ptr &&
        (KS_UNLIKELY(bid_info_ptr->bid_assist_type == 1 ||
        MerchantKconfUtil::enablePerfNobidCasePriceCampaign()->count(campaign_id) > 0))) {   // NOLINT
      perf::Interval(target_cost, "merchant_ocpm_msg_timer",
                    "target_cost",
                    absl::StrCat(campaign_id),
                    group_tag,
                    account_type,
                    ocpc_action_type);
      perf::Interval(gmv, "merchant_ocpm_msg_timer",
                    "gmv",
                    absl::StrCat(campaign_id),
                    group_tag,
                    account_type,
                    ocpc_action_type);
    }
  }


  bool is_storewide_incompatible{false};
  bool is_store_wide_roi_reco_conv{false};
  bool is_storewide_switch{false};
  bool is_storewide_with_order{false};
  bool is_storewide_with_t7{false};
  int is_author_fans{0};
  bool is_increment_explore{false};
  bool is_explore_bid_msg{false};
  std::string campaign_type{""};
  std::string ocpc_action_type{""};
  std::string promotion_type{""};
  std::string item_type{""};
  std::string roi_traffic_source{""};
  uint64_t author_id{0};
  uint64_t live_stream_id{0};
  double cost{0.0};
  double gimbal_price_before{0.0};
  double customer_hc_cost{0.0};
  double bid_coef{0.0};
  double bid_coef_cost{0.0};
  double cpa_coef{0.0};
  double cpa_coef_cost{0.0};
  double gmv{0.0};
  double callback_refund_amount_exact_sum{0.0};
  double pure_roi_purchase_amount_exact_sum{0.0};
  double indirect_gmv{0.0};
  uint64_t conv_num{0};
  uint64_t indirect_conv_num{0};
  double roi_ratio{0.0};
  double auto_roas{0.0};
  uint64_t cpa_bid{0};
  uint64_t delivery_timestamp{0};
  uint64_t ad_attributed_timestamp{0};
  uint64_t pos_id{0};
  std::string origin_group_tag{"adlogfull_ocpm"};
  std::string group_tag{"adlogfull_ocpm"};
  uint64_t auto_cpa_bid{0};
  uint64_t record_gsp_price{0};
  std::string action_type{""};
  MerchantMsgType msg_type = MerchantMsgType::kCostMsg;
  std::string ad_type{""};
  uint64_t msg_timestamp{0};
  uint64_t interactive_form{0};
  bool is_conversion{false};
  bool is_indirect_conversion{false};
  double target_cost{0.0};
  double callback_refund_target_cost_sum{0.0};
  double pure_roi_purchase_target_cost_sum{0.0};
  double indirect_target_cost{0.0};
  double target_gmv{0.0};
  double ecpm = 0.0;
  double front_ecpm = 0.0;
  double origin_price = 0.0;
  double front_auction_bid = 0.0;
  double reco_gpm = 0.0;
  int storewide_inc_type = 0;
  double price_ratio = 0.0;
  uint64_t speed_type{0};
  double pred_conv{0.0};
  double pred_target_cost{0.0};
  double unify_ltv{0.0};
  double predict_unify_cvr{0.0};
  double predict_unify_ctr{0.0};
  std::string live_room_pattern{""};
  uint64_t cpm{0};
  /* universe = {2,4}
  */
  int medium_attribute{0};
  bool is_hot_unit{false};
  bool is_acc_explore{false};
  bool is_degrade{false};
  // other info
  uint64_t separate_gsp_price{0};
  double auction_bid{0.0};
  bool is_soft{false};
  bool is_search_antou{false};
  uint64_t page_id{0};
  // 移动端标识
  std::string account_type{""};
  std::string bid_type{""};
  std::string bid_strategy{""};
  uint64_t payer_id{0};
  uint64_t scene_oriented_type{0};
  int price_before_billing_separate{0};
  int price_after_billing_separate{0};
  // 直播托管标识
  bool is_hosting_live{false};
  // 直播托管表达 roas
  bool is_roas_hosting_live{false};
  // 直播托管表达 roas 出价计划的 roas 单元
  bool is_roas_hosting_live_roas_unit{false};
  // 直播托管表达 roas 出价计划的暗投订单单元
  bool is_roas_hosting_live_order_unit{false};
  // 直播托管表达 t7_roas 出价计划
  bool is_roas_hosting_live_t7_roas_unit{false};
  // 直播托管表达订单
  bool is_order_hosting_live{false};
  // 直播托管表达 roas 的出价
  double project_roi_ratio{0.0};
  // 直播托管表达订单的出价
  int64_t project_cpa_bid{0};
  // unify_ltv / roi_ratio
  double pred_target_cost_roas{0.0};
  // predict_unify_cvr * cpa_bid
  double pred_target_cost_eop{0.0};
  // 无 diff 测试
  int64_t event_server_timestamp{0};
  int64_t msg_seq{0};
  BidStateInfoPtr bid_info_ptr;
  BidStateInfoPtr campaign_bid_info_ptr;

  int64_t msg_cnt{1};

  #define LOG_INTERVAL(field) \
    ks::infra::PerfUtil::IntervalLogStash( \
        field, \
        "ad.bid-server-merchant", \
        "ocpm_msg_diff", \
        #field, \
        ocpc_action_type, \
        group_tag)

  void FieldsPerf() const {
    LOG_INTERVAL(cost);
    LOG_INTERVAL(gimbal_price_before);
    LOG_INTERVAL(customer_hc_cost);
    LOG_INTERVAL(gmv);
    LOG_INTERVAL(indirect_gmv);
    LOG_INTERVAL(conv_num);
    LOG_INTERVAL(indirect_conv_num);
    LOG_INTERVAL(record_gsp_price);
    LOG_INTERVAL(target_cost);
    if (is_hosting_live) {
      double hosting_live_target_cost = target_cost;
      LOG_INTERVAL(hosting_live_target_cost);
    }
    LOG_INTERVAL(indirect_target_cost);
    LOG_INTERVAL(ecpm);
    LOG_INTERVAL(price_ratio);
    LOG_INTERVAL(pred_conv);
    LOG_INTERVAL(pred_target_cost);
    LOG_INTERVAL(auction_bid);
    LOG_INTERVAL(price_before_billing_separate);
    LOG_INTERVAL(price_after_billing_separate);
    LOG_INTERVAL(msg_cnt);
    if (is_conversion) {
      int is_conversion_msg_cnt = msg_cnt;
      LOG_INTERVAL(is_conversion_msg_cnt);
    }
    if (is_storewide_switch) {
      int is_storewide_switch_msg_cnt = msg_cnt;
      LOG_INTERVAL(is_storewide_switch_msg_cnt);
    }
    if (ocpc_action_type == "AD_STOREWIDE_ROAS") {
      int ad_store_roas_msg_cnt = msg_cnt;
      LOG_INTERVAL(ad_store_roas_msg_cnt);
    }
  }

  #undef LOG_INTERVAL

  void describe(std::ostream &os) const {
    os << " is_storewide_incompatible: " << is_storewide_incompatible
       << " is_store_wide_roi_reco_conv: " << is_store_wide_roi_reco_conv
       << " campaign_type: " << campaign_type
       << " ocpc_action_type: " << ocpc_action_type
       << " promotion_type: " << promotion_type
       << " item_type: " << item_type
       << " author_id: " << author_id
       << " account_id: " << account_id
       << " live_stream_id: " << live_stream_id
       << " unit_id: " << unit_id
       << " cost: " << cost
       << " gimbal_price_before: " << gimbal_price_before
       << " customer_hc_cost: " << customer_hc_cost
       << " gmv: " << gmv
       << " conv_num: " << conv_num
       << " roi_ratio: " << roi_ratio
       << " auto_roas: " << auto_roas
       << " cpa_bid: " << cpa_bid
       << " delivery_timestamp: " << delivery_timestamp
       << " group_tag: " << group_tag
       << " auto_cpa_bid: " << auto_cpa_bid
       << " record_gsp_price: " << record_gsp_price
       << " action_type: " << action_type
       << " msg_type: " << msg_type
       << " ad_type: " << ad_type
       << " msg_timestamp: " << msg_timestamp
       << " target_cost: " << target_cost
       << " pred_target_cost: " << pred_target_cost
       << " target_gmv: " << target_gmv
       << " ecpm: " << ecpm
       << " price_ratio: " << price_ratio
       << " speed_type: " << speed_type
       << " pred_conv: " << pred_conv
       << " separate_gsp_price: " << separate_gsp_price
       << " auction_bid: " << auction_bid
       << " account_type: " << account_type
       << " campaign_type: " << campaign_type
       << " ocpc_action_type: " << ocpc_action_type
       << " bid_type: " << bid_type
       << " bid_strategy: " << bid_strategy
       << " payer_id: " << payer_id
       << " campaign_id: " << campaign_id
       << " scene_oriented_type: " << scene_oriented_type
       << " event_server_timestamp: " << event_server_timestamp
       << " pred_target_cost_roas: " << pred_target_cost_roas
       << " pred_target_cost_eop: " << pred_target_cost_eop;
  }

  inline bool GetIsStoreWideRoiRecoConv(const AdLogForAlgo& log) {
    base::Json ext(base::StringToJson(log.callback_extra_info()));
    bool is_store_wide_roi_reco_conv;
    if (ext.IsObject() && ext.GetBoolean("isAllStation", &is_store_wide_roi_reco_conv)) {
      return is_store_wide_roi_reco_conv;
    }
    return false;
  }

  inline void GetRoiRatio(const AdLogForAlgo& log) {
    bool flag1 = false;
    bool flag2 = false;
    if (log.roi_ratio() > 0) {
      roi_ratio = log.roi_ratio();
      flag1 = true;
    }
    if (log.auto_roas() > 0) {
      auto_roas = log.auto_roas();
      flag2 = true;
    }
    if (flag1 && flag2) {
      return;
    }
  }

  inline double GetCost(const AdLogForAlgo& log, const std::string& campaign_type) {
    double cost_total = 0.0;
    if (!SPDM_enableAggrMerchantOcpmMsg()) {
      if (log.ad_log_aggr_message_value().is_aggr_msg()) {
        cost_total = log.ad_log_aggr_message_value().price_sum();
        if (IsFanstop(campaign_type)) {
          cost_total = log.ad_log_aggr_message_value().cost_total_sum();
        }
        // 不收费跳过
        if (log.charge_action_type() == "AD_DELIVERY_NO_CHARGE") {
          cost_total = 0.0;
        }
      } else {
        cost_total = log.price();
        if (IsFanstop(campaign_type)) {
          cost_total = log.cost_total();
        }
        // 不收费跳过
        if (log.charge_action_type() == "AD_DELIVERY_NO_CHARGE") {
          cost_total = 0.0;
        }
      }
    } else {
      cost_total = log.aggr_merchant_ocpm_msg().price_sum();
      if (IsFanstop(campaign_type)) {
        cost_total = log.aggr_merchant_ocpm_msg().cost_total_sum();
      }
      // 不收费跳过
      if (log.charge_action_type() == "AD_DELIVERY_NO_CHARGE") {
        cost_total = 0.0;
      }
    }

    return cost_total;
  }

  inline double GetGimbalPriceBefore(const AdLogForAlgo& log) {
    double gimbal_price_before = 0.0;
    if (log.action_type() == log.charge_action_type() &&
        (log.bid_strategy() == "CUSTOM_BID_STRATEGY" || log.speed_type() == 1 ||
         (log.scene_oriented_type() == 21)) &&
        (log.ocpc_action_type() == "AD_MERCHANT_ROAS" ||
         log.ocpc_action_type() == "AD_FANS_TOP_ROI" ||
         log.ocpc_action_type() == "AD_STOREWIDE_ROAS" ||
         log.ocpc_action_type() == "EVENT_ORDER_PAIED" ||
         log.ocpc_action_type() == "AD_MERCHANT_T7_ROI")) {
      if (!SPDM_enableAggrMerchantOcpmMsg()) {
        if (log.ad_log_aggr_message_value().is_aggr_msg()) {
          gimbal_price_before = log.ad_log_aggr_message_value().gimbal_price_before_sum();
        } else {
          gimbal_price_before = log.gimbal_price_before();
        }
      } else {
        gimbal_price_before = log.aggr_merchant_ocpm_msg().gimbal_price_before();
      }
    }
    // 不收费跳过
    if (log.charge_action_type() == "AD_DELIVERY_NO_CHARGE") {
      gimbal_price_before = 0.0;
    }
    return gimbal_price_before;
  }

  inline double GetCustomerHcCost(const AdLogForAlgo& log) {
    double customer_hc_cost = 0.0;
    if (log.action_type() == log.charge_action_type() &&
        (log.ocpc_action_type() == "AD_MERCHANT_ROAS" ||
         log.ocpc_action_type() == "AD_FANS_TOP_ROI" ||
         log.ocpc_action_type() == "AD_STOREWIDE_ROAS" ||
         log.ocpc_action_type() == "EVENT_ORDER_PAIED" ||
         log.ocpc_action_type() == "AD_MERCHANT_T7_ROI")) {
      if (!SPDM_enableAggrMerchantOcpmMsg()) {
        if (log.ad_log_aggr_message_value().is_aggr_msg()) {
          customer_hc_cost = log.ad_log_aggr_message_value().customer_hc_cost_sum();
        } else {
          customer_hc_cost = log.customer_hc_cost();
        }
      } else {
        customer_hc_cost = log.aggr_merchant_ocpm_msg().customer_hc_cost_sum();
      }
    }
    // 不收费跳过
    if (log.charge_action_type() == "AD_DELIVERY_NO_CHARGE") {
      customer_hc_cost = 0.0;
    }
    return customer_hc_cost;
  }


  bool GetIsConv(const AdLogForAlgo& log, const bool& is_storewide_switch) {
    bool is_conv = log.is_conversion();
    if (log.campaign_type() == "LIVE_STREAM_PROMOTE" || IsFanstop(log.campaign_type())) {
      if (log.ocpc_action_type() == "AD_MERCHANT_ROAS"
          || log.ocpc_action_type() == "AD_FANS_TOP_ROI"
          || log.ocpc_action_type() == "AD_STOREWIDE_ROAS" || is_storewide_switch) {
        if (log.is_conversion() && !IsLEZero(log.callback_purchase_amount())) {
          is_conv = log.is_conversion();
        } else {
          is_conv = false;
        }
      } else if (log.ocpc_action_type() == "AD_MERCHANT_T7_ROI") {  // roi7 要区分直接/间接转化
          is_conv = log.is_conversion() &&
                    log.action_type() == "EVENT_ORDER_PAIED" &&
                    !IsLEZero(log.callback_purchase_amount());
      } else {
        is_conv = log.is_conversion();
      }
    } else if (log.campaign_type() == "MERCHANT_RECO_PROMOTE") {
      if (log.ocpc_action_type() == "EVENT_ORDER_PAIED") {
        if (log.is_conversion() &&
            log.action_type() == "EVENT_ORDER_PAIED" &&
            log.is_single_commodity() == 1) {
          is_conv = log.is_conversion();
        } else {
          is_conv = false;
        }
      } else if (log.ocpc_action_type() == "AD_MERCHANT_ROAS" ||
                 log.ocpc_action_type() == "AD_MERCHANT_T7_ROI" ||
                 log.ocpc_action_type() == "AD_STOREWIDE_ROAS" || is_storewide_switch) {
        if (log.is_conversion() &&
            log.action_type() == "EVENT_ORDER_PAIED") {
          is_conv = log.is_conversion();
        } else {
          is_conv = false;
        }
      } else {
        is_conv = log.is_conversion();
      }
    }
    return is_conv;
  }

  bool GetIsIndirectConv(const AdLogForAlgo& log) {
    bool is_roi7 = log.ocpc_action_type() == "AD_MERCHANT_T7_ROI";
    bool is_deep_ocpc = log.ocpc_action_type() == "AD_MERCHANT_ROAS"
                        || log.ocpc_action_type() == "AD_FANS_TOP_ROI"
                        || log.ocpc_action_type() == "AD_STOREWIDE_ROAS"
                        || log.ocpc_action_type() == "EVENT_ORDER_PAIED";
    bool action_type_cond = log.action_type() == "EVENT_ORDER_PAYED_INDIRECT"
                            || log.action_type() == "EVENT_ORDER_PAYED_SERVER_FOLLOW";
    return action_type_cond
           && !IsLEZero(log.callback_purchase_amount())
           && (is_roi7 || is_deep_ocpc);
  }

  inline double GetGmv(const AdLogForAlgo& log, const bool& is_conv, const bool& is_storewide_switch) {
    double gmv = log.callback_purchase_amount() * 1000;
    if (log.ocpc_action_type() == "EVENT_ORDER_PAIED" ||
        log.ocpc_action_type() == "AD_MERCHANT_ROAS"  ||
        log.ocpc_action_type() == "AD_MERCHANT_T7_ROI"  ||
        log.ocpc_action_type() == "AD_FANS_TOP_ROI" ||
        log.ocpc_action_type() == "AD_STOREWIDE_ROAS" || is_storewide_switch) {
      if (is_conv) {
        gmv = log.callback_purchase_amount() * 1000;
      } else {
        gmv = 0.0;
      }
    } else {
      if (log.action_type() == "EVENT_ORDER_PAIED") {
        gmv = log.callback_purchase_amount() * 1000;
      } else {
        gmv = 0.0;
      }
    }
    return gmv;
  }

  inline double GetConvNum(const AdLogForAlgo& log, const bool& is_conv) {
    if (is_conv) {
      return 1;
    } else {
      return 0;
    }
  }

  inline bool IsActivate() const {
    return msg_type == MerchantMsgType::kActivateMsg ||
      msg_type == MerchantMsgType::kRequestActivateMsg;
  }

  inline bool IsFanstop(const std::string& campaign_type) {
    kuaishou::ad::AdEnum::CampaignType campaign_type_pb = kuaishou::ad::AdEnum::UNKNOWN_CAMPAIGN_TYPE;
    kuaishou::ad::AdEnum_CampaignType_Parse(campaign_type, &campaign_type_pb);
    if (campaign_type_pb == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_FANS
        || campaign_type_pb == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_SHOW
        || campaign_type_pb == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_ALL
        || campaign_type_pb == kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL
        || campaign_type_pb == kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS
        || campaign_type_pb == kuaishou::ad::AdEnum::AD_FANSTOP_TO_SHOW) {
      return true;
    }
    return false;
  }

  inline double GetRoiRatioV2(const AdLogForAlgo& log, const std::string& campaign_type,
                              const BidStateInfoPtr& campaign_bid_info_ptr, const bool& is_storewide_switch) {
    double roi_ratio_tmp = log.roi_ratio();
    if (IsFanstop(campaign_type)) {
      // 移动端 roi_ratio = cpa_bid * 1000
      roi_ratio_tmp = static_cast<double>(log.cpa_bid()) / 1000;
    } else if (campaign_type == "LIVE_STREAM_PROMOTE" &&
        (log.ocpc_action_type() == "AD_STOREWIDE_ROAS" &&
        log.speed_type() == 5 || is_storewide_switch)) {  // 全站 nobid 单元使用计划出价
        if (campaign_bid_info_ptr && campaign_bid_info_ptr->roi_ratio > 0) {
          roi_ratio_tmp = campaign_bid_info_ptr->roi_ratio;
      }
    }
    return roi_ratio_tmp;
  }

  inline bool hasClientShow(const AdLogForAlgo& log, const std::string& item_type,
                            const std::string& action_type) {
    if (item_type == "ITEM_LIVE") {
      if ((action_type == "AD_LIVE_IMPRESSION" || (action_type == "AD_LIVE_PLAYED_STARTED" &&
           log.live_room_pattern() == "SIMPLIFIED_LIVE_ROOM_PATTERN"))) {
        return true;
      }
    } else if (item_type == "ITEM_PHOTO_TO_LIVE") {
      if (action_type == "AD_PHOTO_IMPRESSION" || action_type == "AD_ITEM_IMPRESSION") {
        return true;
      }
    }
    return false;
  }
};

inline std::ostream &operator<<(std::ostream &os, const MerchantOcpmMsg& msg) {
  msg.describe(os);
  return os;
}

}  // namespace bid_server
}  // namespace ks
