// Authors: <AUTHORS>
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/merchant_ocpm_strategy.h"
#include <float.h>
#include <cmath>
#include <algorithm>
#include <cstdint>
#include <string>
#include <vector>
#include <unordered_map>
#include "absl/strings/substitute.h"
#include "base/thread/thread_util.h"
#include "falcon/counter.h"
#include "processor/task/ocpm_rl_feature_task.h"
#include "teams/ad/bid_server/public/api/msg.h"
#include "teams/ad/bid_server/public/base/common.h"
#include "teams/ad/bid_server/framework/utils/json_to_stable_str.h"
#include "base/common/sleep.h"
#include "teams/ad/ad_base/src/redis/redis_kv_pb_store.h"
#include "teams/ad/ad_base/src/common/latency_record_perfutil.h"
#include "base/strings/string_number_conversions.h"
#include "teams/ad/ad_base/src/common/scoped_guard.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/merchant_ocpm_exp_config.h"
#include "teams/ad/bid_server/application/bid_server_merchant/util/bid_status_data_storer_merchant.h"
#include "teams/ad/bid_server/application/bid_server_merchant/manager/merchant_bid_conf_mgr.h"
#include "teams/ad/bid_server/framework/manager/unit_shard_mgr.h"
#include "teams/ad/bid_server/application/bid_server_merchant/util/util.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/merchant_ocpm_common.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/bid_context_update_processor.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/util_value_update_processor.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/acc_explore_processor.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/ocpm_bid_processor.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/costcap_nobid_processor.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/adjust_value_check_processor.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/universe_adjust_value_check_processor.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/high_atv_price_billing_processor.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/processor/monitor_processor.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/lowest_cost/lowest_cost_util.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/lowest_cost/common_util.h"
#include "teams/ad/bid_server/application/bid_server_merchant/kconf/kconf.h"
#include "teams/ad/bid_server/base/kconf/kconf.h"
#include "teams/ad/bid_server/base/strategy/lowest_cost/cost_cap_util.h"
#include "teams/ad/bid_server/framework/utils/common_util.h"
#include "teams/ad/bid_server/framework/utils/kafka_util.h"
#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/common/common.h"
#include "teams/ad/bid_server/framework/utils/time.h"
#include "teams/ad/bid_server/framework/utils/kconf.h"
#include "teams/ad/bid_server/application/bid_server_merchant/util/bid_no_diff_redis_save.h"
#include "teams/ad/bid_server/framework/utils/spdm_switches.h"
#include "teams/ad/bid_server/application/bid_server_merchant/kml/merchant_kml_manager.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/merchant_ocpm_rl_msg.h"
#include "teams/ad/ad_table_lite/include/ad_table_lite/index_manager.h"
#include "teams/ad/bid_server/framework/index/default/bid_state_info_ad_table_helper.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/merchant_ocpm_backflow_msg.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/merchant_ocpm/merchant_ocpm_mpc_msg.h"
#include "teams/ad/bid_server/application/bid_server_merchant/util/merchant_rl_model_helper.h"

DECLARE_bool(enable_bid_no_diff_switch);
DEFINE_bool(enable_ocpm_step_two, true, "enable_step_two");

namespace ks {
namespace bid_server {
using kuaishou::ad::AdActionType;
using kuaishou::ad::MerchantOcpmBidConfig;
using kuaishou::ad::OperationConfigAutoROI;
using ::ks::ad_base::LatencyRecordPrefUtil;
using google::protobuf::util::MessageToJsonString;
using google::protobuf::util::JsonPrintOptions;
using ks::engine_base::pacing_box::PIDPacingMethod;
using ks::engine_base::pacing_box::PIDPacingParams;
using kuaishou::ad::LivePageInfo;

thread_local std::unordered_map<int64_t, int64_t> MerchantOcpmStrategy::unit_step_two_version_info_;

// ad.bidServer.merchantOcpmExpConfigMap
inline MerchantOcpmExpConfig MerchantOcpmStrategy::GetExpConfig(BidStateInfoPtr bid_info_ptr,
                                                                const uint64_t& unit_id,
                                                                const std::string& group_tag,
                                                                const uint64_t& author_id) {
  MerchantOcpmExpConfig exp_config = MerchantBidConfMgr::Instance().GetOcpmExpConfig(unit_id, group_tag);

  return exp_config;
}

void MerchantOcpmStrategy::ProcessResetBidContext(const ks::bid_server::merchant::ResetMsg& msg,
  BidStateInfoPtr bid_info_ptr, OcpmBidContext* p_bid_context, ProcessUtilValues* util_vals,
  const MerchantOcpmExpConfig& exp_config) {
  if (!p_bid_context || !bid_info_ptr || !util_vals) {
    if (!p_bid_context) {
      LOG(INFO) << "MerchantOcpmStrategy::ProcessResetBidContext,pid_bid_context null";
    }

    return;
  }
  if (msg.clear_cost_conv) {
    ResetContext(p_bid_context, util_vals, exp_config);
  }
  if (util_vals->is_roas) {
    util_vals->new_adjust_auto_value_rate = 1.0;
    double roi_ratio = bid_info_ptr->roi_ratio;
    // 移动端 ROI 索引在 cpa_bid 中
    if (util_vals->is_fanstop) {
      roi_ratio = static_cast<double>(bid_info_ptr->cpa_bid) / 1000;
    }
    p_bid_context->set_roi_ratio(roi_ratio);
    p_bid_context->set_relax_roi_ratio(roi_ratio);
  } else {
    util_vals->new_adjust_auto_value_rate = 1.0;
    p_bid_context->set_cpa_bid(bid_info_ptr->cpa_bid);
    p_bid_context->set_relax_cpa_bid(bid_info_ptr->cpa_bid);
  }
  AdjustAutoValue(p_bid_context, util_vals, exp_config, bid_info_ptr);
  SyncResultToDsp(bid_info_ptr, p_bid_context, msg.group_tag, util_vals);
  LOG(INFO) << "Reset, p_bid_context:" << p_bid_context->ShortDebugString();
}

void MerchantOcpmStrategy::ProcessReset(
  const ks::bid_server::merchant::ResetMsg& reset_msg,
  kuaishou::ad::AdMultiPidCache* pid_cache) {
  ResetMsg msg = reset_msg;
  BidStateInfoPtr bid_info_ptr =
    BID_STATE.GetBidStateInfo(msg.unit_id);
  if (!CheckMsg(bid_info_ptr, &msg)) {
    return;
  }
  MerchantOcpmBidConfig strategy_config = GetStrategyConfig(
    kuaishou::ad::AdEnum::CampaignType_Name(msg.campaign_type),
    kuaishou::ad::AdActionType_Name(msg.ocpx_action_type));

  auto key_id =  msg.unit_id;
  if (IsCboStoreWide(bid_info_ptr)) {
    key_id = msg.campaign_id;
  }
  if (IsCboCostCap(msg.unit_id, bid_info_ptr)) {
    key_id = msg.campaign_id;
  }
  if (IsHost(bid_info_ptr)) {
    key_id = msg.campaign_id;
  }
  MerchantOcpmExpConfig exp_config = GetExpConfig(bid_info_ptr, key_id, msg.group_tag, msg.author_id);
  ProcessUtilValues util_vals = GetUtilValues(
    kuaishou::ad::AdEnum::CampaignType_Name(msg.campaign_type),
    kuaishou::ad::AdActionType_Name(msg.ocpx_action_type));
  util_vals.unit_id = msg.unit_id;

  OcpmBidContext* p_bid_context{nullptr};
  if (reset_msg.is_cbo) {
    p_bid_context = GetBidContext(strategy_config, msg.campaign_id,
        msg.group_tag, +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL);
    ProcessResetBidContext(msg, bid_info_ptr, p_bid_context, &util_vals, exp_config);
  } else {
    // 直投直播物料
    p_bid_context = GetBidContext(strategy_config, msg.unit_id,
      msg.group_tag, +BidDataLevel::UNIT_BID_DATA_LEVEL);
    ProcessResetBidContext(msg, bid_info_ptr, p_bid_context, &util_vals, exp_config);
    // 作品引流
    p_bid_context = GetBidContext(strategy_config, msg.unit_id,
      msg.group_tag, +BidDataLevel::UNIT_P2L_BID_DATA_LEVEL);
    ProcessResetBidContext(msg, bid_info_ptr, p_bid_context, &util_vals, exp_config);
  }
  ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace, "reset", Name(),
    std::to_string(msg.unit_id), msg.group_tag);
}

void MerchantOcpmStrategy::UpdateLowestCostContext(const RawMsgPtr &raw_msg) {
  const NativeLowestCostMsg& msg = raw_msg->value->get_value<NativeLowestCostMsg>();
  if (FLAGS_enable_bid_no_diff_switch) {
    TimeUtil::Instance().SetTimeStamp(msg.event_server_timestamp * 1000);
    LOG(INFO) << "MerchantOcpmMsg1, UpdateLowestCostContext, seq:" << msg.msg_seq
              << ",  unit_id:" << msg.inner_data->unit_id
              << ",  event_server_timestamp:" << msg.event_server_timestamp * 1000;
  }
  uint64_t unit_id = msg.inner_data->unit_id;
  uint64_t campaign_id = msg.inner_data->campaign_id;
  uint64_t account_id = msg.inner_data->account_id;
  uint64_t author_id = msg.inner_data->author_id;
  auto speed_type = msg.inner_data->speed_type;
  auto scene_oriented_type = msg.inner_data->scene_oriented_type;
  auto bid_strategy = msg.inner_data->bid_strategy;
  auto item_type = msg.inner_data->str_item_type;
  const auto acc_explore_campaign_tail = MerchantKconfUtil::storewideAccExploreCampaignTail();
  auto ocpc_action_type = kuaishou::ad::AdActionType_Name(
          static_cast<kuaishou::ad::AdActionType>(msg.inner_data->ocpc_action_type));
  auto campaign_type = msg.inner_data->campaign_type;
  ks::infra::PerfUtil::CountLogStash(1, kNamespace_, "ocpm_costcap_msg_count",
                                     base::Uint64ToString(unit_id));
  std::string group_tag = GetGroupTag(msg);
  bool is_reset = false;
  BidStateInfoPtr bid_info_ptr = BID_STATE.GetBidStateInfo(unit_id);
  if (bid_info_ptr == nullptr) {
    return;
  }
  MerchantOcpmBidConfig strategy_config = GetStrategyConfig(
    kuaishou::ad::AdEnum::CampaignType_Name(bid_info_ptr->campaign_type),
    kuaishou::ad::AdActionType_Name(bid_info_ptr->ocpx_action_type));
  // 加速探索重构逻辑需要用 ocpm 分物料的 context，获取方式需一致
  OcpmBidContext* p_bid_context{nullptr};
  if (IsLiveAccIncrement(bid_info_ptr, true)) {
    p_bid_context = BidContextGetterNew(strategy_config, unit_id, campaign_id,
                               account_id, author_id, speed_type, scene_oriented_type,
                               group_tag, bid_strategy, ocpc_action_type,
                               campaign_type, item_type, bid_info_ptr);
  } else {
    if (scene_oriented_type == 23 ||
        (SPDM_enableLSPHosting() && scene_oriented_type == 35) ||
        (bid_info_ptr &&
         (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
          (SPDM_enableLSPHosting() &&
           bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE)))) {
      group_tag = static_cast<std::string>(absl::Substitute("$0_$1", ocpc_action_type, group_tag));
      auto esp_live_hosting_roas_union_bid_tail = MerchantKconfUtil::espLiveHostingRoasUnionBidTail();
      if (esp_live_hosting_roas_union_bid_tail &&
          esp_live_hosting_roas_union_bid_tail->IsOnFor(campaign_id)) {
        group_tag = GetGroupTag(msg);
      }
      p_bid_context =
          GetBidContext(strategy_config, campaign_id, group_tag, +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL);
    } else if (SPDM_enableStorewideAccCampaign() && acc_explore_campaign_tail &&
               acc_explore_campaign_tail->IsOnFor(campaign_id) && IsStorewideAccFixExpV2(bid_info_ptr)) {
      p_bid_context =
          GetBidContext(strategy_config, campaign_id, group_tag, +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL);
    } else if (IsStorewideAccFixExp(bid_info_ptr)) {
      p_bid_context =
          GetBidContext(strategy_config, msg.unit_id, group_tag, +BidDataLevel::UNIT_BID_DATA_LEVEL);
    } else if (IsCbo(bid_info_ptr) ||
               ocpc_action_type == "AD_STOREWIDE_ROAS") {
      p_bid_context = GetBidContext(strategy_config, msg.campaign_id,
          group_tag, +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL);
    } else {
      p_bid_context = GetBidContext(strategy_config, msg.unit_id,
          group_tag, +BidDataLevel::UNIT_BID_DATA_LEVEL);
    }
  }
  if (!p_bid_context) {
    return;
  }
  auto& lowest_cost_context = *(p_bid_context->mutable_native_lowest_cost_context());
  NoBidConfig nobid_strategy_config = GetNobidStrategyConfig(&lowest_cost_context, bid_info_ptr, group_tag);
  UpdateNobidContext(&lowest_cost_context, bid_info_ptr, msg, nobid_strategy_config, &is_reset);
  if (lowest_cost_context.pacing_rate() == 1.0) {
    p_bid_context->set_adjust_rate_acc_increment(1.0);
  }
  return;
}

void MerchantOcpmStrategy::InitLowestCostContext(const RawMsgPtr &raw_msg) {
  const MerchantOcpmMsg& msg = raw_msg->value->get_value<MerchantOcpmMsg>();
  uint64_t unit_id = msg.unit_id;
  uint64_t campaign_id = msg.campaign_id;
  uint64_t account_id = msg.account_id;
  uint64_t author_id = msg.author_id;
  auto speed_type = msg.speed_type;
  auto scene_oriented_type = msg.scene_oriented_type;
  const auto acc_reset_tail = MerchantKconfUtil::espLiveAccExploreResetContextTail();
  auto campaign_type = msg.campaign_type;
  BidStateInfoPtr bid_info_ptr = BID_STATE.GetBidStateInfo(msg.unit_id);
  if (bid_info_ptr == nullptr) {
    return;
  }
  bool is_tail = false;
  if (acc_reset_tail && acc_reset_tail->IsOnFor(author_id)) {
    is_tail = true;
  }
  if (!is_tail) {
    return;
  }
  MerchantOcpmBidConfig strategy_config = GetStrategyConfig(
    kuaishou::ad::AdEnum::CampaignType_Name(bid_info_ptr->campaign_type),
    kuaishou::ad::AdActionType_Name(bid_info_ptr->ocpx_action_type));
  // 加速探索重构逻辑需要用 ocpm 分物料的 context，获取方式需一致
  OcpmBidContext* p_bid_context{nullptr};
  if (scene_oriented_type == 21 && bid_info_ptr->campaign_explore_put_type == 3 &&
      bid_info_ptr->campaign_explore_budget_status > 1 &&
      bid_info_ptr->explore_ext_type != 1) {
    p_bid_context = GetBidContext(strategy_config, campaign_id, msg.group_tag, +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL);  // NOLINT
  }
  if (!p_bid_context) {
    return;
  }
  auto esp_live_acc_explore_reset_context_tail = MerchantKconfUtil::espLiveAccExploreResetContextTail();
  auto& lowest_cost_context = *(p_bid_context->mutable_native_lowest_cost_context());
  if (esp_live_acc_explore_reset_context_tail &&
      esp_live_acc_explore_reset_context_tail->IsOnFor(msg.campaign_id) &&
      bid_info_ptr->campaign_explore_budget_status > 1 &&
      bid_info_ptr->campaign_explore_put_type == 3 &&
      lowest_cost_context.explore_budget_status() == 1) {
    // 加速探索关闭重置 context
    auto esp_live_acc_explore_inti_bid = MerchantKconfUtil::EspLiveAccExploreInitBid();
    double init_rate = 1.0;
    if (esp_live_acc_explore_inti_bid) {
      auto iter = esp_live_acc_explore_inti_bid->find(0);
      if (iter != esp_live_acc_explore_inti_bid->end()) {
        init_rate = iter->second;
      }
      iter = esp_live_acc_explore_inti_bid->find(author_id);
      if (iter != esp_live_acc_explore_inti_bid->end()) {
        init_rate = iter->second;
      }
    }
    lowest_cost_context.set_explore_budget_status(bid_info_ptr->campaign_explore_budget_status);
    lowest_cost_context.set_pacing_rate(init_rate);
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_bid_orbit_server", "is_init", "stop_explore_2",
                                    absl::StrCat(campaign_id), msg.group_tag);
  }
  return;
}

void MerchantOcpmStrategy::ProcessStepOne(const RawMsgPtr &raw_msg) {
  if (raw_msg->type == +RawMsgType::MerchantOcpmCostCapMsg
      || raw_msg->type == +RawMsgType::MerchantOcpmNobidAccMsg) {
    UpdateLowestCostContext(raw_msg);
    return;
  }

  if (raw_msg->type == +RawMsgType::MerchantOcpmRlUpdateMsg ||
      raw_msg->type == +RawMsgType::MerchantOcpmBackflowMsg ||
      raw_msg->type == +RawMsgType::MerchantOcpmMpcMsg) {
    return;
  }

  InitLowestCostContext(raw_msg);

  MerchantOcpmMsg& msg = raw_msg->value->get_value<MerchantOcpmMsg>();
  // 跳过探索消息
  if (msg.is_explore_bid_msg) {
    ks::infra::PerfUtil::CountLogStash(1, kNamespace_, "acc_explore_msg",
                                       "skip_step_one", base::Uint64ToString(msg.unit_id),
                                       base::Uint64ToString(msg.campaign_id));
    return;
  }
  if (FLAGS_enable_bid_no_diff_switch) {
    TimeUtil::Instance().SetTimeStamp(msg.event_server_timestamp * 1000);
    LOG(INFO) << "MerchantOcpmMsg1, join strategy, seq:" << msg.msg_seq
              << ",  unit_id:" << msg.unit_id
              << ",  event_server_timestamp:" << msg.event_server_timestamp * 1000;
  }
  if (msg.cost > 0) {
    ks::infra::PerfUtil::CountLogStash(msg.cost, kNamespace_, "cost_count",
                                       base::Uint64ToString(msg.unit_id),
                                       base::Uint64ToString(msg.campaign_id));
  }

  // bid_state_info_ptr 在策略层重新获取
  BidStateInfoPtr bid_info_ptr = BID_STATE.GetBidStateInfo(msg.unit_id);
  // 测试后删除
  if (SPDM_enablePerfNobidCasePrice() && bid_info_ptr &&
      (KS_UNLIKELY(bid_info_ptr->bid_assist_type == 1 ||
       MerchantKconfUtil::enablePerfNobidCasePriceCampaign()->count(msg.campaign_id) > 0))) {   // NOLINT
    perf::Interval(msg.target_cost, "merchant_ocpm_step_one_msg",
                   "target_cost",
                  absl::StrCat(msg.campaign_id),
                  msg.group_tag,
                  msg.account_type,
                  msg.ocpc_action_type);
    perf::Interval(msg.gmv, "merchant_ocpm_step_one_msg",
                   "gmv",
                  absl::StrCat(msg.campaign_id),
                  msg.group_tag,
                  msg.account_type,
                  msg.ocpc_action_type);
  }

  MerchantOcpmExpConfig exp_config =
      GetExpConfig(bid_info_ptr, GetBidKeyId(msg, bid_info_ptr), msg.group_tag, msg.author_id);
  CheckMsg(bid_info_ptr, &msg);

  MerchantOcpmBidConfig strategy_config = GetStrategyConfig(
    msg.campaign_type, msg.ocpc_action_type);
  if (msg.is_storewide_with_order) {
    strategy_config = GetStrategyConfig(msg.campaign_type, "AD_STOREWIDE_ROAS");
  }
  OcpmBidContext* p_bid_context = BidContextGetter(strategy_config, msg, bid_info_ptr);
  if (!p_bid_context) {
    return;
  }

  if (FLAGS_enable_bid_no_diff_switch) {
    LOG(INFO) << "MerchantOcpmMsg2, unit_id:" << msg.unit_id
      << ", group_tag:" << msg.group_tag << ", action_type:" << msg.action_type
      << ", p_bid_context:" << p_bid_context;
  }
  ProcessUtilValues util_vals = GetUtilValues(msg, strategy_config, exp_config, bid_info_ptr);
  if (util_vals.is_debug) {
    LOG(WARNING) << "Debug:MerchantOcpmStrategy:"
                 << " tid:" << util_vals.thread_id
                 << " msg: " << msg << std::endl
                 << " strategy_config: " << strategy_config.ShortDebugString() << std::endl
                 << " exp_config: " << exp_config << std::endl
                 << " bid_context_key: " << p_bid_context->bid_context_key()
                 << " bid_context: " << p_bid_context->ShortDebugString() << std::endl
                 << " util_vals: " << util_vals;
  }
  StrategySession strategy_session(p_bid_context, &util_vals, &msg, &strategy_config, &exp_config,
                                   bid_info_ptr);
  // update context info
  // nobid 重构增量消息不更新原始 context
  if (!msg.is_increment_explore) {
    BidContextUpdateProcessor::Instance()->Process(&strategy_session);
  }
}

void MerchantOcpmStrategy::ProcessStepTwo(const RawMsgPtr& raw_msg) {
  if (FLAGS_enable_bid_no_diff_switch) {
    if (!FLAGS_enable_ocpm_step_two) {
      return;
    }
  }
  if (raw_msg->type == +RawMsgType::MerchantOcpmCostCapMsg
      || raw_msg->type == +RawMsgType::MerchantOcpmNobidAccMsg) {
    return;
  }

  if (raw_msg->type == +RawMsgType::MerchantOcpmRlUpdateMsg) {
    UpdateRlPrediction(raw_msg);
    return;
  }

  if (raw_msg->type == +RawMsgType::MerchantOcpmBackflowMsg) {
    // fuzhenqiang
    UpdateBackflowPrediction(raw_msg);
    return;
  }

  if (raw_msg->type == +RawMsgType::MerchantOcpmMpcMsg) {
    UpdateMpcPrediction(raw_msg);
    return;
  }

  MerchantOcpmMsg& msg = raw_msg->value->get_value<MerchantOcpmMsg>();

  // bid_state_info_ptr 在策略层重新获取
  BidStateInfoPtr bid_info_ptr = BID_STATE.GetBidStateInfo(msg.unit_id);
  MerchantOcpmExpConfig exp_config =
      GetExpConfig(bid_info_ptr, GetBidKeyId(msg, bid_info_ptr), msg.group_tag, msg.author_id);

  CheckMsg(bid_info_ptr, &msg);
  MerchantOcpmBidConfig strategy_config = GetStrategyConfig(msg.campaign_type, msg.ocpc_action_type);
  if (msg.is_storewide_with_order) {
    strategy_config = GetStrategyConfig(msg.campaign_type, "AD_STOREWIDE_ROAS");
  }
  OcpmBidContext* p_bid_context = BidContextGetter(strategy_config, msg, bid_info_ptr);
  if (!p_bid_context) {
    return;
  }
  ad_base::ScopeGuard guard([&]() { BidStatusDataStorer<OcpmBidContext>::Instance().Store(p_bid_context); });

  ProcessUtilValues util_vals = GetUtilValues(msg, strategy_config, exp_config, bid_info_ptr);
  if (util_vals.is_debug) {
    LOG(INFO) << "Debug:MerchantOcpmStrategy:"
                 << " tid:" << util_vals.thread_id << " msg: " << msg << std::endl
                 << " strategy_config: " << strategy_config.ShortDebugString() << std::endl
                 << " exp_config: " << exp_config << std::endl
                 << " bid_context: " << p_bid_context->ShortDebugString() << std::endl
                 << " util_vals: " << util_vals;
  }
  StrategySession strategy_session(p_bid_context, &util_vals, &msg, &strategy_config, &exp_config,
                                   bid_info_ptr);

  if ((util_vals.is_unit_ocpm_eop_pc || util_vals.is_unit_ocpm_roas_pc ||
      (!util_vals.is_cbo && p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI")) &&
      !SPDM_enableFixPreconvertRequest()) {
    const auto innerPreConvertModelTail = MerchantKconfUtil::innerPreConvertModelTail();
    bool inner_pre_convert_model = innerPreConvertModelTail &&
          innerPreConvertModelTail->IsOnFor(p_bid_context->unit_id());
    const auto innerPreConvertModelT7Tail = MerchantKconfUtil::innerPreConvertModelT7Tail();
    if (p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI") {
      inner_pre_convert_model = innerPreConvertModelT7Tail &&
                                innerPreConvertModelT7Tail->IsOnFor(p_bid_context->unit_id());
    }
    if (inner_pre_convert_model && MerchantKmlManager::Instance() != nullptr) {
    bool convert_suss_flag = false;
    if (p_bid_context->target_cost() > 0) {
      convert_suss_flag = MerchantKmlManager::Instance()->Push_V3(p_bid_context, "default",
                              MerchantInferRequestType::OcpmBackflow);
    } else {
      convert_suss_flag = MerchantKmlManager::Instance()->Push_V3(p_bid_context, "zero",
                                   MerchantInferRequestType::OcpmBackflow);
    }
      LOG_EVERY_N(INFO, 10000) << "inner_pre_convert_debug:" << convert_suss_flag;
    }
  }
  // update util_vals
  UtilValueUpdateProcessor::Instance()->Process(&strategy_session);
  // update 2
  BidContextUpdateProcessor::Instance()->Process2(&strategy_session);

  HighAtvPriceBillingProcessor::Instance()->Process(&strategy_session, bid_info_ptr);   // bid_info_ptr 透传
  // 重置为客户出价
  ResetAdjustValue(&strategy_session);

  util_vals.new_adjust_auto_value_rate = p_bid_context->adjust_auto_value_rate();
  util_vals.old_adjust_auto_value_rate = p_bid_context->adjust_auto_value_rate();
  if (FLAGS_enable_bid_no_diff_switch || util_vals.is_debug) {
    LOG(WARNING) << "no diff debug after reset adjust_auto_value_rate, "
      << p_bid_context->adjust_auto_value_rate()
      << " util_vals_new_adjust_auto_value_rate, "
      << util_vals.new_adjust_auto_value_rate
      << " util_vals_old_adjust_auto_value_rate, "
      << util_vals.old_adjust_auto_value_rate
      << " msg seq, " << msg.event_server_timestamp
      << " unit id, " << msg.unit_id;
  }
  if (!IsProcessUpdate(msg, p_bid_context, &util_vals)) {
      ks::infra::PerfUtil::IntervalLogStash(p_bid_context->target_cost(), kNamespace_,
          "IsProcessUpdate_false", base::Uint64ToString(msg.unit_id));
    return;
  }
  if (bid_info_ptr) {
    bool is_acc_live = IsLiveAccIncrement(bid_info_ptr, true);
    p_bid_context->set_is_live_acc_increment(is_acc_live);
  }
  auto storewide_acc_explore_log = MerchantKconfUtil::storewideAccExploreLogTail();
  // 全站加速探索商测试单客户排查日志
  if (storewide_acc_explore_log && storewide_acc_explore_log->IsOnFor(p_bid_context->campaign_id()) &&
      bid_info_ptr) {
    LOG(INFO) << "Debug AccExplore "
              << ", is_roas: " << util_vals.is_roas
              << ", is_acc_increment: " << util_vals.is_acc_increment
              << ", is_acc_explore_bid: " << util_vals.is_acc_explore_bid
              << ", is_nobid_acc: " << util_vals.is_nobid_acc
              << ", hard_lower_bound_auto_roas: " << util_vals.hard_lower_bound_auto_roas
              << ", hard_upper_bound_auto_roas: " << util_vals.hard_upper_bound_auto_roas
              << ", unit_id: " << p_bid_context->unit_id()
              << ", campaign_id: " << p_bid_context->campaign_id()
              << ", msg_unit_id: " << msg.unit_id
              << ", explore_budget_status: " << bid_info_ptr->explore_budget_status
              << ", explore_put_type: " << bid_info_ptr->explore_put_type
              << ", is_storewide_roas_acc: " << IsStorewideAccFixExpV2(bid_info_ptr)
              << ", ocpc_action_type: " << p_bid_context->ocpc_action_type();
  }
  if (ApplyOperationConfig(msg, bid_info_ptr, p_bid_context, &util_vals)) {
    if (util_vals.is_debug) {
      LOG(WARNING) << "Debug:ApplyOperationConfig:true:tid:" << util_vals.thread_id;
    }
    util_vals.is_apply_operation_config = true;
  } else {
    std::string acc_explore_in_strategy = "None";
    bool enable_bid_msg_skip_update_adjust = msg.is_explore_bid_msg;
    if (bid_info_ptr && bid_info_ptr->explore_put_type == 3) {
      enable_bid_msg_skip_update_adjust = (msg.is_explore_bid_msg && util_vals.is_ad_open);
    }
    if (bid_info_ptr &&
        (bid_info_ptr->explore_put_type == 3 || bid_info_ptr->explore_ext_type == 1)) {
      enable_bid_msg_skip_update_adjust = (msg.is_explore_bid_msg && util_vals.is_ad_open);
    }
    if (util_vals.is_update_adjust || enable_bid_msg_skip_update_adjust) {
      if (util_vals.is_acc_explore_bid) {
        if (util_vals.is_nobid_acc || util_vals.is_acc_increment) {
          // 当前只有增量消息触发重构调价
          AccExploreProcessor::Instance()->NewProcess(&strategy_session);
          acc_explore_in_strategy = "NewProcess";
        } else {
          AccExploreProcessor::Instance()->Process(&strategy_session);
          acc_explore_in_strategy = "Process";
        }
        // 增量消息也触发控成本调价, 探索消息不触发控成本调价
        if (util_vals.is_acc_increment && !msg.is_explore_bid_msg) {
          OcpmBidProcessor::Instance()->Process(&strategy_session);
          acc_explore_in_strategy += " timer";
        }
      } else if (util_vals.is_costcap_nobid) {
        CostCapNobidProcessor::Instance()->Process(&strategy_session);
      } else if (util_vals.is_cold_start || util_vals.is_ocpm_bid_process) {
        OcpmBidProcessor::Instance()->Process(&strategy_session);
      } else {
        util_vals.is_nothing_to_do = true;
      }

      if (!IsSkipValueCheck(&util_vals, bid_info_ptr)) {
        if (IsUniverse(msg.group_tag) && exp_config.enable_universe_adjust_value_check_processor &&
            p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED") {
          UniverseAdjustValueCheckProcessor::Instance()->Process(&strategy_session);
        } else {
          AdjustValueCheckProcessor::Instance()->Process(&strategy_session);
        }
      }
      util_vals.is_process = true;
    }
    // 直播托管构建 rl 请求
    bool is_hosting_live = bid_info_ptr &&
      (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
       (SPDM_enableLSPHosting() &&
        bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE));
    // 直播全站 rl 请求
    auto group_config_map = MerchantKconfUtil::SwLiveDtGroupTagConfigMap();
    auto group_iter = group_config_map->find(msg.group_tag);
    bool zhong_xiao_rl = false;
    if (bid_info_ptr && bid_info_ptr->tc_acc_explore_author_info_list.size() >= 3) {
        double live_arpu = bid_info_ptr->tc_acc_explore_author_info_list[1];
        auto zhongxiaoRlTail = MerchantKconfUtil::zhongxiaoRlTail();
        zhong_xiao_rl = SPDM_enableZhongxiaoRl() && live_arpu < 300 && zhongxiaoRlTail
         && zhongxiaoRlTail->IsOnFor(p_bid_context->author_id());
    }
    auto SwDtExpTail = MerchantKconfUtil::SwDtExpTail();
    bool sw_dt_tail = SwDtExpTail && SwDtExpTail->IsOnFor(p_bid_context->author_id());
    if (util_vals.is_update_adjust && p_bid_context->is_cbo() && (sw_dt_tail ||
        (SPDM_enableRlGroupTagExp() && group_iter != group_config_map->end()) || zhong_xiao_rl)
        && p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS"  &&
        p_bid_context->hosting_dt_bad_count() <= MerchantKconfUtil::hostingLiveRLBadCountThresh()) {
      std::vector<std::vector<float>> input_data_list;
      int32_t timesteps = p_bid_context->mutable_inner_rl_data()->timesteps();
      if (timesteps >= 4095) {
        timesteps = 0;
      }
      p_bid_context->mutable_inner_rl_data()->set_timesteps(timesteps + 1);
      std::string model_config;
      if (SPDM_enableRlGroupTagExp() && group_iter != group_config_map->end() &&
          !group_iter->second.empty()) {
        model_config = group_iter->second;
      }
      auto config_map = MerchantKconfUtil::SwLiveDtAuthorTailConfigMap();
      auto iter = config_map->find(p_bid_context->author_id() % MerchantKconfUtil::SwDtAuthorDivisor());
      if (iter != config_map->end() && (!SPDM_enableFixRlGroupExp() || sw_dt_tail)) {
          model_config = iter->second;
      }
      if (zhong_xiao_rl) {
        model_config = "zhong_xiao";
      }
      if (OcpmRlFeatureTask::Instance()->ConstructeMultiInputData(
        p_bid_context, &util_vals, bid_info_ptr, model_config, input_data_list)) {
        bool rl_succ_flag = MerchantKmlManager::Instance()->Push_V2(p_bid_context,
                            model_config, input_data_list, MerchantInferRequestType::OcpmRl);
        if (rl_succ_flag) {
          LOG_EVERY_N(INFO, 10000) << "xusimin_debug_sw_dt_push"
                    << " unit_id=" << p_bid_context->unit_id()
                    << " campaign_id=" << p_bid_context->campaign_id()
                    << " author_id=" << p_bid_context->author_id()
                    << " now_sec=" << TimeUtil::Instance().GetTimestamp() / 1e6
                    << " model_config=" << model_config
                    << " hosting_dt_bad_count=" << p_bid_context->hosting_dt_bad_count()
                    << " model_pred=" << p_bid_context->inner_rl_data().inner_rl_model_pred()
                    << " adjust_auto_value_rate=" << p_bid_context->adjust_auto_value_rate()
                    << " InnerRlData=" << p_bid_context->inner_rl_data().ShortDebugString();
        }
      }
    }
    auto hostingLiveDTCampaignRoasTail = MerchantKconfUtil::hostingLiveDTCampaignRoasTail();
    auto hostingLiveDTCampaignOrderTail = MerchantKconfUtil::hostingLiveDTCampaignOrderTail();
    auto hostingLiveDTCampaignT7Tail = MerchantKconfUtil::hostingLiveDTCampaignT7Tail();
    bool is_hosting_dt_roas = (util_vals.is_roas_hosting_live &&
     hostingLiveDTCampaignRoasTail && hostingLiveDTCampaignRoasTail->count(
     p_bid_context->campaign_id() % 100) > 0 && msg.ocpc_action_type != "AD_MERCHANT_T7_ROI");
    bool is_hosting_dt = is_hosting_dt_roas ||
     (util_vals.is_order_hosting_live && hostingLiveDTCampaignOrderTail &&
     hostingLiveDTCampaignOrderTail->count(p_bid_context->campaign_id() % 100) > 0) ||
     (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI"
      && hostingLiveDTCampaignT7Tail && hostingLiveDTCampaignT7Tail->count(
      p_bid_context->campaign_id() % 100) > 0);
    bool enable_hosting_dt_push = util_vals.is_update_adjust && is_hosting_live && is_hosting_dt
      && p_bid_context->hosting_dt_bad_count() <= MerchantKconfUtil::hostingLiveRLBadCountThresh();
    if (enable_hosting_dt_push) {
      std::vector<std::vector<float>> input_data_list;
      int32_t timesteps = p_bid_context->mutable_inner_rl_data()->timesteps();
      if (timesteps >= 4095) {
        timesteps = 0;
      }
      p_bid_context->mutable_inner_rl_data()->set_timesteps(timesteps + 1);
      std::string model_config;
      if (util_vals.is_roas_hosting_live) {
        model_config = exp_config.hosting_dt_roas_config;
        if (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI") {
          model_config = *MerchantKconfUtil::hostingDtT7roiconfig();
        }
      } else if (util_vals.is_order_hosting_live) {
        model_config = exp_config.hosting_dt_order_config;
      } else {
        model_config = "";
      }
      if (OcpmRlFeatureTask::Instance()->ConstructeMultiInputData(
        p_bid_context, &util_vals, bid_info_ptr, model_config, input_data_list)) {
        bool rl_succ_flag = MerchantKmlManager::Instance()->Push_V2(p_bid_context,
                            model_config, input_data_list, MerchantInferRequestType::OcpmRl);
        if (rl_succ_flag) {
          LOG_EVERY_N(INFO, 10000) << "xusimin_debug_dt_push"
                    << " unit_id=" << p_bid_context->unit_id()
                    << " campaign_id=" << p_bid_context->campaign_id()
                    << " now_sec=" << TimeUtil::Instance().GetTimestamp() / 1e6
                    << " is_roas_hosting_live=" << msg.is_roas_hosting_live
                    << " is_order_hosting_live=" << msg.is_order_hosting_live
                    << " model_config=" << model_config
                    << " hosting_dt_bad_count=" << p_bid_context->hosting_dt_bad_count()
                    << " InnerRlData=" << p_bid_context->inner_rl_data().ShortDebugString();
        }
      }
    }
    const auto hostingMpcModelDataTail = MerchantKconfUtil::hostingMpcModelDataTail();
    bool hosting_mpc_model_data = hostingMpcModelDataTail
                                  && is_hosting_live
                                  && hostingMpcModelDataTail->IsOnFor(p_bid_context->campaign_id())
                                  && util_vals.is_update_adjust;
    bool storewide_mpc_model_data = util_vals.is_update_adjust && p_bid_context->is_cbo() &&
                                    p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS";
    if (hosting_mpc_model_data || storewide_mpc_model_data) {
      auto* mpc_features = p_bid_context->mutable_inner_mpc_data();
      double excess_ratio = 0.0;
      if ((p_bid_context->target_cost() + 50000) > 0) {
        excess_ratio = (p_bid_context->cost() + 50000) / (p_bid_context->target_cost() + 50000);
      }
      if (mpc_features->mutable_mpc_raw_features()->size() >= 60) {
        int idx = mpc_features->mutable_mpc_raw_features()->size() - 60;
        double later_10min_cost =
          p_bid_context->cost() / 1000.0 - mpc_features->mutable_mpc_raw_features()->Get(idx).cost();
        mpc_features->add_later_10min_cost(std::max(0.0, later_10min_cost));
        double later_10min_target_cost = p_bid_context->target_cost() / 1000.0
          - mpc_features->mutable_mpc_raw_features()->Get(idx).target_cost();
        mpc_features->add_later_10min_target_cost(std::max(0.0, later_10min_target_cost));
      }
      // 添加新数据到数组末尾
      auto* new_feature = mpc_features->add_mpc_raw_features();
      new_feature->set_excess_ratio(excess_ratio);
      new_feature->set_cost(p_bid_context->cost() / 1000.0);
      new_feature->set_target_cost(p_bid_context->target_cost() / 1000.0);
      new_feature->set_pid_cost(util_vals.pid_cost  / 1000.0);
      new_feature->set_pid_target_cost(util_vals.pid_target_cost  / 1000.0);
      new_feature->set_adjust_auto_value_rate(p_bid_context->adjust_auto_value_rate());

      if (mpc_features->mutable_mpc_raw_features()->size() > 200) {
        int erase_count = mpc_features->mutable_mpc_raw_features()->size() - 200;
        mpc_features->mutable_mpc_raw_features()->erase(
                mpc_features->mutable_mpc_raw_features()->begin(),
                mpc_features->mutable_mpc_raw_features()->begin() + erase_count);
          mpc_features->mutable_later_10min_cost()->erase(
                mpc_features->mutable_later_10min_cost()->begin(),
                mpc_features->mutable_later_10min_cost()->begin() + erase_count);
          mpc_features->mutable_later_10min_target_cost()->erase(
                mpc_features->mutable_later_10min_target_cost()->begin(),
                mpc_features->mutable_later_10min_target_cost()->begin() + erase_count);
      }
    }
    const auto hostingMpcModelTail = MerchantKconfUtil::hostingMpcModelTail();
    bool is_hosting_mpc_t7 = util_vals.is_roas_hosting_live && msg.ocpc_action_type == "AD_MERCHANT_T7_ROI"
                             && hostingMpcModelTail && util_vals.is_update_adjust
                             && hostingMpcModelTail->IsOnFor(p_bid_context->campaign_id());
    const auto storewideMpcModelTail = MerchantKconfUtil::storewideMpcModelTail();
    bool storewide_mpc_model = storewideMpcModelTail && p_bid_context->is_cbo()
                  && p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS"
                  && storewideMpcModelTail->IsOnFor(p_bid_context->campaign_id())
                  && util_vals.is_update_adjust;
    if (is_hosting_mpc_t7 || storewide_mpc_model) {
      if (MerchantKmlManager::Instance() != nullptr) {
        bool mpc_suss_flag = false;
        uint64_t now_sec = TimeUtil::Instance().GetTimestamp() / 1e6;
        uint64_t mpc_send_ts = p_bid_context->mutable_inner_mpc_data()->mpc_send_ts();
        int step_num_control = MerchantKconfUtil::hostingMpcStepNumControl();
        int time_thresh = MerchantKconfUtil::hostingMpcTimeCostThresh();
        int step_size = p_bid_context->mutable_inner_mpc_data()->mutable_mpc_raw_features()->size();
        if (step_size > step_num_control && (mpc_send_ts == 0 || (now_sec - mpc_send_ts) < time_thresh)) {
          if (is_hosting_mpc_t7) {
            mpc_suss_flag = MerchantKmlManager::Instance()->Push_V3(p_bid_context, "default",
                          MerchantInferRequestType::HostingLiveMPC);
          } else if (storewide_mpc_model) {
            mpc_suss_flag = MerchantKmlManager::Instance()->Push_V3(p_bid_context, "storewide",
                          MerchantInferRequestType::HostingLiveMPC);
          }
        }
          LOG_EVERY_N(INFO, 10000) << "inner_mpc_debug:" << mpc_suss_flag;
      }
    }
    if ((util_vals.is_unit_ocpm_eop_pc || util_vals.is_unit_ocpm_roas_pc ||
      (!util_vals.is_cbo && p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI")) &&
      SPDM_enableFixPreconvertRequest() && util_vals.is_update_adjust) {
      const auto innerPreConvertModelTail = MerchantKconfUtil::innerPreConvertModelTail();
      bool inner_pre_convert_model = innerPreConvertModelTail &&
            innerPreConvertModelTail->IsOnFor(p_bid_context->unit_id());
      const auto innerPreConvertModelT7Tail = MerchantKconfUtil::innerPreConvertModelT7Tail();
      if (p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI") {
        inner_pre_convert_model = innerPreConvertModelT7Tail &&
                                  innerPreConvertModelT7Tail->IsOnFor(p_bid_context->unit_id());
      }
      if (inner_pre_convert_model && MerchantKmlManager::Instance() != nullptr) {
      bool convert_suss_flag = false;
      if (p_bid_context->target_cost() > 0) {
        convert_suss_flag = MerchantKmlManager::Instance()->Push_V3(p_bid_context, "default",
                                MerchantInferRequestType::OcpmBackflow);
      } else {
        convert_suss_flag = MerchantKmlManager::Instance()->Push_V3(p_bid_context, "zero",
                                    MerchantInferRequestType::OcpmBackflow);
      }
        LOG_EVERY_N(INFO, 10000) << "inner_pre_convert_debug:" << convert_suss_flag;
      }
    }
    if (bid_info_ptr) {
      if (bid_info_ptr->unit_id == MerchantKconfUtil::aAccStashUnitID()) {
        ks::infra::PerfUtil::CountLogStash(1, kNamespace_, "acc_explore_in_strategy",
                                      base::Uint64ToString(bid_info_ptr->unit_id), acc_explore_in_strategy);
        LOG(INFO) << " Acc explore debug is explore: " << msg.is_explore_bid_msg
                  << " Update adjust " << util_vals.is_update_adjust
                  << " is acc explore " << util_vals.is_acc_explore_bid
                  << " nobid acc " << util_vals.is_nobid_acc
                  << " acc inc " << util_vals.is_acc_increment;
      }
    }
    AdjustAutoValue(p_bid_context, &util_vals, exp_config, bid_info_ptr);
  }

  UtilValueUpdateProcessor::Instance()->Process2(&strategy_session);
  if (FLAGS_enable_bid_no_diff_switch || util_vals.is_debug) {
    LOG(WARNING) << "no diff debug util_vals.is_sync_to_dsp, " << util_vals.is_sync_to_dsp
      << " msg seq, " << msg.event_server_timestamp
      << " unit id, " << msg.unit_id;
  }
  // sync to remote
  if (util_vals.is_sync_to_dsp) {
    SyncResultToDsp(bid_info_ptr, p_bid_context, msg.origin_group_tag, &util_vals);
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
      "SyncResultToDsp", "util_vals", msg.origin_group_tag);
  } else {
    p_bid_context->set_send_result_status(3);
  }
  if (util_vals.is_update_adjust) {
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
     "SyncResultToDsp", "is_update_adjust", msg.origin_group_tag);
  }
  MonitorProcessor::Instance()->Process(&strategy_session);
  if (FLAGS_enable_bid_no_diff_switch) {
    BidNoDiffRedisSave::Instance()->SaveNoDiffStepTwo(p_bid_context, msg);
  }
  if (strategy_config.is_debug_on() || util_vals.is_debug) {
    DubugInfoPrint(&strategy_session);
  }
  if (util_vals.is_update_adjust) {
    p_bid_context->set_last_pred_target_cost(p_bid_context->pred_target_cost());
    p_bid_context->set_last_update_cost(p_bid_context->cost());
    p_bid_context->set_last_update_gimbal_price_before(p_bid_context->gimbal_price_before());
    p_bid_context->set_last_reco_gpm(p_bid_context->reco_gpm());
    p_bid_context->set_last_p_target_cost(p_bid_context->p_target_cost());
    p_bid_context->set_last_update_target_cost(p_bid_context->target_cost());
  }
}

bool MerchantOcpmStrategy::IsSkipValueCheck(ProcessUtilValues* p_util_vals,
                                            const BidStateInfoPtr& bid_info_ptr) {
  if (!p_util_vals || !bid_info_ptr) {
    return false;
  }
  // 主站加速探索 (联盟的 group_tag 不是 merchant_acc_explore)
  if (p_util_vals->is_acc_explore_bid && p_util_vals->is_nobid_acc) {
    return true;
  }
  // costcap 单元和计划尾号实验
  if (p_util_vals->is_costcap_nobid) {
    auto unit_id = bid_info_ptr->unit_id;
    auto campaign_id = bid_info_ptr->campaign_id;
    bool is_cbo_costcap = IsCboCostCap(unit_id, bid_info_ptr);
    int64_t id = is_cbo_costcap ? campaign_id : unit_id;
    auto unit_tail_config = MerchantKconfUtil::costCapSkipAdjustValueUnitTail();
    auto campaign_tail_config = MerchantKconfUtil::costCapSkipAdjustValueCampaignTail();
    auto tail_config = is_cbo_costcap ? campaign_tail_config : unit_tail_config;
    if (tail_config && tail_config->IsOnFor(id)) {
      return true;
    }
  }
  return false;
}

void MerchantOcpmStrategy::DubugInfoPrint(StrategySession* p_session) {
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);

  if (p_util_vals->is_cold_start || p_util_vals->is_explore) {
    LOG(WARNING) << "DebugInfo.Process::"
                  << " unit_id: " << msg.unit_id << " cost: " << p_bid_context->cost()
                  << " target_cost: " << p_bid_context->target_cost()
                  << " old_roas: " << p_bid_context->old_auto_roi_ratio()
                  << " roas: " << p_bid_context->auto_roi_ratio()
                  << " rt_cost_speed: " << p_bid_context->rt_cost_speed()
                  << " target_cost_speed: " << p_util_vals->target_cost_speed
                  << " target_delivery_speed: " << p_util_vals->target_delivery_speed
                  << " rt_delivery_speed: " << p_bid_context->rt_delivery_speed();
  }
  LOG(WARNING) << "[zhangtuchao]DebugInfo_ProcessStepTwo: "
                << ", unit_id: " << msg.unit_id
                << ", group_tag: " << msg.group_tag
                << ", is_fanstop: " << p_util_vals->is_fanstop
                << ", is_fanstop_ocpm: " << p_util_vals->is_fanstop_ocpm
                << ", campaign_id: " << msg.campaign_id
                << ", account_id: " << msg.account_id
                << ", author_id: " << msg.author_id
                << ", payer_id: " << msg.payer_id
                << ", account_type: " << msg.account_type
                << ", campaign_type: " << msg.campaign_type
                << ", ocpc_action_type: " << msg.ocpc_action_type
                << ", bid_type: " << msg.bid_type
                << ", speed_type: " << msg.speed_type
                << ", bid_strategy: " << msg.bid_strategy
                << ", item_type: " << msg.item_type
                << ", promotion_type: " << msg.promotion_type
                << ", is_apply_operation_config: " << p_util_vals->is_apply_operation_config
                << ", msg_type: " << msg.msg_type
                << ", is_update_adjust: " << p_util_vals->is_update_adjust
                << ", is_acc_explore_bid: " << p_util_vals->is_acc_explore_bid
                << ", is_cold_start: " << p_util_vals->is_cold_start
                << ", is_costcap_nobid: " << p_util_vals->is_costcap_nobid
                << ", is_ocpm_bid_process: " << p_util_vals->is_ocpm_bid_process
                << ", start_bid_rate: " << p_util_vals->start_bid_rate
                << ", budget: " << p_util_vals->budget
                << ", cpa_bid: " << msg.cpa_bid
                << ", roi_ratio: " << msg.roi_ratio
                << ", new_adjust_auto_value_rate: " << p_util_vals->new_adjust_auto_value_rate
                << ", auto_cpa_bid: " << msg.auto_cpa_bid
                << ", auto_roas: " << msg.auto_roas
                << ", exp_config: " << exp_config
                << ", update_interval_ms: " << p_util_vals->update_interval_ms
                << ", hard_bid_upper_bound_rate_monitor: " << p_util_vals->hard_bid_upper_bound_rate_monitor
                << ", hard_bid_lower_bound_rate_monitor: " << p_util_vals->hard_bid_lower_bound_rate_monitor
                << ", p_weight: " << p_util_vals->p_weight
                << ", i_weight: " << p_util_vals->i_weight
                << ", d_weight: " << p_util_vals->d_weight
                << ", prior_cost: " << p_util_vals->prior_cost
                << ", cold_start_cost_li_thr: " << p_util_vals->cold_start_cost_li_thr
                << ", cold_start_low_bound: " << p_util_vals->cold_start_low_bound
                << ", adjust_rate_lower_bound: " << p_util_vals->adjust_rate_lower_bound
                << ", adjust_rate_upper_bound: " << p_util_vals->adjust_rate_upper_bound
                << ", start_bid_rate: " << p_util_vals->start_bid_rate
                << ", auto_cpa_bid_adlog: " << msg.auto_cpa_bid;
}

inline void MerchantOcpmStrategy::AdjustAutoValue(OcpmBidContext* p_bid_context,
                                                  ProcessUtilValues* p_util_vals,
                                                  const MerchantOcpmExpConfig& exp_config,
                                                  BidStateInfoPtr bid_info_ptr) {
  if (!p_util_vals) return;
  if (p_util_vals->new_adjust_auto_value_rate == 0) {
    // 控成本系数兜底
    p_util_vals->new_adjust_auto_value_rate = 1.0;
    ks::infra::PerfUtil::CountLogStash(1, kNamespace_, "acc_explore_msg",
                                       "ocpm_new_value_default", base::Uint64ToString(p_util_vals->unit_id));
  }
  p_bid_context->set_adjust_auto_value_rate(p_util_vals->new_adjust_auto_value_rate);
  if (p_util_vals->is_acc_increment && p_util_vals->adjust_rate_acc_increment > 0) {
    p_bid_context->set_adjust_rate_acc_increment(p_util_vals->adjust_rate_acc_increment);
  }
  if (FLAGS_enable_bid_no_diff_switch || p_util_vals->is_debug) {
    LOG(WARNING) << "no diff debug after process adjust_auto_value_rate, "
      << p_bid_context->adjust_auto_value_rate()
      << " util_vals_new_adjust_auto_value_rate, "
      << p_util_vals->new_adjust_auto_value_rate
      << " msg seq, " << TimeUtil::Instance().GetTimestamp()
      << " unit id, " << p_bid_context->unit_id();
  }

  bool is_hosting_live = bid_info_ptr &&
      (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
       (SPDM_enableLSPHosting() &&
        bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE));
  bool is_hosting_live_wide = bid_info_ptr &&
      (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
       (SPDM_enableLSPHosting() &&
        bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE));
  if (MerchantKconfUtil::enablehostingLiveWideColdPhotoBoost()) {
    is_hosting_live_wide = is_hosting_live_wide || (bid_info_ptr &&
      (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_STORE_WIDE_LIVE_FEATURE));
  }
  auto hosting_live_cold_photo_author_tail =
      MerchantKconfUtil::enableHostingLiveColdPhotoAuthorV2Tail();
  auto hosting_live_cold_photo_author_list =
      MerchantKconfUtil::enableHostingLiveColdPhotoAuthorV2List();
  bool is_in_exp = (hosting_live_cold_photo_author_tail->count(p_bid_context->author_id() % 100) ||
                    hosting_live_cold_photo_author_list->count(p_bid_context->author_id()));
  int is_cold_unit_photo = 0;
  int is_cold_unit_photo_new = 0;
  if (bid_info_ptr &&
      (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
      bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_STORE_WIDE_LIVE_FEATURE ||
      (SPDM_enableLSPHosting() &&
       bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE))) {
    MerchantDataMgr::Instance().GetUnitIsColdInfo(p_bid_context->campaign_id(),
        bid_info_ptr->unit_id, &is_cold_unit_photo_new);
    p_bid_context->set_is_cold_unit_photo(is_cold_unit_photo_new);
  }
  if (is_hosting_live_wide && is_in_exp
      && !FLAGS_enable_bid_no_diff_switch) {
    is_cold_unit_photo = is_cold_unit_photo_new;
  }
  auto is_roas_hosting_live_order_unit = p_util_vals->is_roas_hosting_live_order_unit;
  double cold_photo_boost_ratio = MerchantKconfUtil::hostingLiveColdPhotoBoostRatio();

  // 用单元整体成本率对分物料调价做限制
  double unit_adjust_rate = 0.0;
  if (MerchantKconfUtil::enableUnitAdjustRate() && p_util_vals->is_update_adjust &&
      (p_util_vals->is_unit_ocpm_roas_pc || p_util_vals->is_unit_ocpm_eop_pc) &&
      exp_config.enable_unit_cost_rate_calibrate == 1) {
    int64_t time_now = base::GetTimestamp() / 1000000;
    int64_t time_gap_thresh = MerchantKconfUtil::pcLiveUnitCostTimeGap();
    double unit_cost_rate;
    int64_t pc_target_cost_today_sum_timestamp = 0;
    int64_t pc_target_cost_today_sum_price = 0;
    int64_t pc_target_cost_today_sum_expect_cost = 0;
    // 从正排读取
    if (bid_info_ptr) {
      pc_target_cost_today_sum_timestamp = bid_info_ptr->pc_target_cost_today_sum_timestamp;
      pc_target_cost_today_sum_price = bid_info_ptr->pc_target_cost_today_sum_price;
      pc_target_cost_today_sum_expect_cost = bid_info_ptr->pc_target_cost_today_sum_expect_cost;
    }
    // 从 Redis 读取
    // enableWTUnitCache 开关打开且读取成功后从 redis 读取的数据覆盖生效
    // enableWTUnitMemory = false, enableWTUnitCache = true 时完成数据切换
    bool is_get_redis_success = false;
    if (!SPDM_useWTRemoteTables() && AdKconfUtil::enableWTUnitCache() && bid_info_ptr) {
      is_get_redis_success = BidStateInfoAdTableHelper::Instance()->GetWTUnit(bid_info_ptr);
      if (is_get_redis_success) {
        pc_target_cost_today_sum_timestamp =
            bid_info_ptr->wt_unit_info_ptr->pc_target_cost_today_sum_timestamp;
        pc_target_cost_today_sum_price =
            bid_info_ptr->wt_unit_info_ptr->pc_target_cost_today_sum_price;
        pc_target_cost_today_sum_expect_cost =
            bid_info_ptr->wt_unit_info_ptr->pc_target_cost_today_sum_expect_cost;
      }
    }
    if (bid_info_ptr) {
      if (time_now - pc_target_cost_today_sum_timestamp < time_gap_thresh) {
        double unit_cost = pc_target_cost_today_sum_price;
        double unit_target_cost = pc_target_cost_today_sum_expect_cost;
        double min_cost_rate = exp_config.unit_cali_min_cost_rate;
        double max_cost_rate = exp_config.unit_cali_max_cost_rate;
        // 判断是否混投单元
        if (unit_cost > 0 && unit_cost > p_bid_context->cost() && unit_target_cost > 100 * 1000) {
          unit_cost_rate = (unit_cost + 200 * 1000) / (unit_target_cost + 200 * 1000);
          unit_cost_rate = std::max(unit_cost_rate, min_cost_rate);
          unit_cost_rate = std::min(unit_cost_rate, max_cost_rate);
          if (unit_cost_rate < exp_config.unit_cali_left_thres &&
              exp_config.unit_cali_left_thres > min_cost_rate) {
            unit_adjust_rate = exp_config.unit_cali_upper_bound -
                (unit_cost_rate - min_cost_rate) / (exp_config.unit_cali_left_thres - min_cost_rate)
                * (exp_config.unit_cali_upper_bound - 1.0);
          } else if (unit_cost_rate > exp_config.unit_cali_right_thres &&
                     exp_config.unit_cali_right_thres < max_cost_rate) {
            unit_adjust_rate = 1.0 - (unit_cost_rate - exp_config.unit_cali_right_thres) /
                (max_cost_rate - exp_config.unit_cali_right_thres) * (1.0 - exp_config.unit_cali_lower_bound);
          }
          if (unit_adjust_rate > 0) {
            unit_adjust_rate = std::max(unit_adjust_rate, exp_config.unit_cali_lower_bound);
            unit_adjust_rate = std::min(unit_adjust_rate, exp_config.unit_cali_upper_bound);
            p_util_vals->unit_adjust_rate = unit_adjust_rate;
          }
        }
        if (MerchantKconfUtil::enableUnitAdjustRateDebug()) {
          LOG(INFO) << "wangyang10_debug_total_cost:"
                    << "unit_id: " << p_bid_context->unit_id() << ", "
                    << "ocpx_action_type: " << p_bid_context->ocpc_action_type() << ", "
                    << "sum_timestamp: " << pc_target_cost_today_sum_timestamp << ", "
                    << "cost: " << p_bid_context->cost() << ", "
                    << "target_cost: " << p_bid_context->target_cost() << ", "
                    << "unit_cost: " << pc_target_cost_today_sum_price << ", "
                    << "unit_target_cost: " << pc_target_cost_today_sum_expect_cost << ", "
                    << "unit_cost_rate: " << unit_cost_rate << ", "
                    << "min_cost_rate: " << min_cost_rate << ", "
                    << "max_cost_rate: " << max_cost_rate << ", "
                    << "unit_adjust_rate: " << unit_adjust_rate;
        }
      }
    }
  }

  // rl 调价系数处理
  double res_adjust_auto_value_rate = p_bid_context->adjust_auto_value_rate();
  double old_adjust_auto_value_rate = p_bid_context->adjust_auto_value_rate();
  // 直播全站 rl 返回处理
  auto group_config_map = MerchantKconfUtil::SwLiveDtGroupTagConfigMap();
  auto group_iter = group_config_map->find(p_bid_context->group_tag());
  std::string model_config;
  if (SPDM_enableRlGroupTagExp() && group_iter != group_config_map->end() &&
      !group_iter->second.empty()) {
    model_config = group_iter->second;
  }
  auto config_map = MerchantKconfUtil::SwLiveDtAuthorTailConfigMap();
  auto iter = config_map->find(p_bid_context->author_id() % MerchantKconfUtil::SwDtAuthorDivisor());
  if (iter != config_map->end()) {
      model_config = iter->second;
  }
  bool zhong_xiao_rl = false;
  if (bid_info_ptr && bid_info_ptr->tc_acc_explore_author_info_list.size() >= 3) {
      double live_arpu = bid_info_ptr->tc_acc_explore_author_info_list[1];
      auto zhongxiaoRlTail = MerchantKconfUtil::zhongxiaoRlTail();
      zhong_xiao_rl = SPDM_enableZhongxiaoRl() && live_arpu < 300 && zhongxiaoRlTail
        && zhongxiaoRlTail ->IsOnFor(p_bid_context->author_id());
  }
  MerchantRLModelInterpreterV2 model_interpreter;
  auto SwDtExpTail = MerchantKconfUtil::SwDtExpTail();
  bool is_sw_rl_exp = GetRLModelInterpreterV2(model_config, &model_interpreter);
  if (p_util_vals->is_update_adjust && p_bid_context->is_cbo() &&
      (((SwDtExpTail && SwDtExpTail->IsOnFor(p_bid_context->author_id()) ||
      (SPDM_enableRlGroupTagExp() && group_iter != group_config_map->end()) || zhong_xiao_rl) &&
       !SPDM_enableRlSingleStepRatio()) || (SPDM_enableRlSingleStepRatio() && is_sw_rl_exp))
      && p_bid_context->ocpc_action_type() == "AD_STOREWIDE_ROAS"  &&
      p_bid_context->hosting_dt_bad_count() <= MerchantKconfUtil::hostingLiveRLBadCountThresh()) {
    p_bid_context->set_hosting_dt_actions(p_bid_context->adjust_auto_value_rate());
    uint64_t now_sec = TimeUtil::Instance().GetTimestamp() / 1e6;
    uint64_t rl_recv_ts = p_bid_context->inner_rl_data().inner_rl_recv_ts();
    uint64_t rl_time_cost_total = p_bid_context->inner_rl_data().inner_rl_cost_ts() + now_sec - rl_recv_ts;
    float model_pred = p_bid_context->inner_rl_data().inner_rl_model_pred();
    int is_ratio_model = 0;
    if (is_sw_rl_exp) {
      is_ratio_model = model_interpreter.model_interpreter.model_config().is_ratio_model();
    }
    // 请求模型时间到当前时间需小于一定阈值
    if (model_pred > 0 && rl_time_cost_total <= MerchantKconfUtil::hostingLiveRLTimeCostThresh()) {
      p_util_vals->rl_model_pred_all = model_pred;
      if ((model_pred >= MerchantKconfUtil::hostingDtLowerbound() * res_adjust_auto_value_rate &&
          model_pred <= MerchantKconfUtil::hostingDtUpperbound() * res_adjust_auto_value_rate) ||
          is_ratio_model == 1) {
        p_bid_context->set_hosting_dt_actions(model_pred);
        res_adjust_auto_value_rate = model_pred;
        if (SPDM_enableRlSingleStepRatio() && is_ratio_model == 1) {
          double upper_ratio = MerchantKconfUtil::step_ratio_upper_bound();
          double lower_ratio = MerchantKconfUtil::step_ratio_lower_bound();
          double step_ratio = std::max(std::min(model_pred * 1.0, upper_ratio), lower_ratio);
          double old_rate = p_bid_context->old_adjust_auto_value_rate() > 0.0
                            ? p_bid_context->old_adjust_auto_value_rate() : 1.0;
          res_adjust_auto_value_rate = old_rate * step_ratio;
        }
        p_util_vals->rl_adjust_rate = res_adjust_auto_value_rate;
        LOG_EVERY_N(INFO, 10000) << "xusimin_debug_sw_dt_res_act" << ", "
          << "is_ratio_model: " << is_ratio_model << ", "
          << "unit_id: " << p_bid_context->unit_id() << ", "
          << "campaign_id: " << p_bid_context->campaign_id() << ", "
          << "author_id: " << p_bid_context->author_id() << ", "
          << "now_sec: " << now_sec << ", "
          << "rl_time_cost_total: " << rl_time_cost_total << ", "
          << "old_adjust_auto_value_rate: " << old_adjust_auto_value_rate << ", "
          << "hosting_dt_bad_count: " << p_bid_context->hosting_dt_bad_count() << ", "
          << "pacing_weight: " << p_bid_context->pacing_weight() << ", "
          << "model_pred: " << p_bid_context->inner_rl_data().inner_rl_model_pred();
      }
    }
    bool is_bad_case;
    double low_bound = SPDM_enableRlGroupTagExp() ? MerchantKconfUtil::SwRlbidRatioLower() : 1.0;
    double upper_bound = SPDM_enableRlGroupTagExp() ? MerchantKconfUtil::SwRlbidRatioUpper() : 1.0;
    is_bad_case = (p_bid_context->cost() > MerchantKconfUtil::SwDtRatioLower() * p_bid_context->target_cost()
        && model_pred > upper_bound && p_bid_context->cost() >= MerchantKconfUtil::SwDtCostLower() &&
        p_bid_context->pacing_weight() < 1) || (p_bid_context->cost() < 0.8 * p_bid_context->target_cost() &&
        p_bid_context->pacing_weight() > 1 && model_pred < low_bound && model_pred > 0);
    if (is_bad_case) {
      p_bid_context->set_hosting_dt_bad_count(p_bid_context->hosting_dt_bad_count() + 1);
    }
    LOG_EVERY_N(INFO, 10000) << "xusimin_debug_sw_dt_res_all" << ", "
      << "is_ratio_model: " << is_ratio_model << ", "
      << "unit_id: " << p_bid_context->unit_id() << ", "
      << "campaign_id: " << p_bid_context->campaign_id() << ", "
      << "author_id: " << p_bid_context->author_id() << ", "
      << "now_sec: " << now_sec << ", "
      << "rl_time_cost_total: " << rl_time_cost_total << ", "
      << "old_adjust_auto_value_rate: " << old_adjust_auto_value_rate << ", "
      << "hosting_dt_bad_count: " << p_bid_context->hosting_dt_bad_count() << ", "
      << "pacing_weight: " << p_bid_context->pacing_weight() << ", "
      << "model_pred: " << p_bid_context->inner_rl_data().inner_rl_model_pred() << ", "
      << "InnerRlData: " << p_bid_context->inner_rl_data().ShortDebugString();
  }

  auto hostingLiveDTCampaignRoasTail = MerchantKconfUtil::hostingLiveDTCampaignRoasTail();
  auto hostingLiveDTCampaignOrderTail = MerchantKconfUtil::hostingLiveDTCampaignOrderTail();
  auto hostingLiveDTCampaignT7Tail = MerchantKconfUtil::hostingLiveDTCampaignT7Tail();
  bool is_hosting_dt_tail_roas = (p_util_vals->is_roas_hosting_live && hostingLiveDTCampaignRoasTail
    && hostingLiveDTCampaignRoasTail->count(p_bid_context->campaign_id() % 100) > 0 &&
    p_bid_context->ocpc_action_type() != "AD_MERCHANT_T7_ROI");
  bool is_hosting_dt_tail = is_hosting_dt_tail_roas ||
    (p_util_vals->is_order_hosting_live && hostingLiveDTCampaignOrderTail &&
    hostingLiveDTCampaignOrderTail->count(p_bid_context->campaign_id() % 100) > 0) ||
    (p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI" && hostingLiveDTCampaignT7Tail &&
    hostingLiveDTCampaignT7Tail->count(p_bid_context->campaign_id() % 100) > 0);
  bool is_hosting_rl_dt = (is_hosting_live && is_hosting_dt_tail);
  bool enable_hosting_dt_res = is_hosting_rl_dt && p_util_vals->is_update_adjust
    && p_bid_context->hosting_dt_bad_count() <= MerchantKconfUtil::hostingLiveRLBadCountThresh();
  if (enable_hosting_dt_res) {
    p_bid_context->set_hosting_dt_actions(p_bid_context->adjust_auto_value_rate());
    uint64_t now_sec = TimeUtil::Instance().GetTimestamp() / 1e6;
    uint64_t rl_recv_ts = p_bid_context->inner_rl_data().inner_rl_recv_ts();
    uint64_t rl_time_cost_total = p_bid_context->inner_rl_data().inner_rl_cost_ts() + now_sec - rl_recv_ts;
    float model_pred = p_bid_context->inner_rl_data().inner_rl_model_pred();
    // 请求模型时间到当前时间需小于一定阈值
    if (rl_time_cost_total <= MerchantKconfUtil::hostingLiveRLTimeCostThresh()) {
      p_util_vals->rl_model_pred_all = model_pred;
      if (model_pred >= MerchantKconfUtil::hostingDtLowerbound() * res_adjust_auto_value_rate &&
          model_pred <= MerchantKconfUtil::hostingDtUpperbound() * res_adjust_auto_value_rate) {
        p_bid_context->set_hosting_dt_actions(model_pred);
        res_adjust_auto_value_rate = model_pred;
        p_util_vals->rl_adjust_rate = res_adjust_auto_value_rate;
      }
    }
    auto hostingDTRoasSafeTail = MerchantKconfUtil::hostingDTRoasSafeTail();
    auto hostingDTOrderSafeTail = MerchantKconfUtil::hostingDTOrderSafeTail();
    bool is_hosting_dt_safe_tail = (p_util_vals->is_roas_hosting_live && hostingDTRoasSafeTail &&
      hostingDTRoasSafeTail->count(p_bid_context->campaign_id() % 100) > 0) ||
      (p_util_vals->is_order_hosting_live && hostingDTOrderSafeTail &&
      hostingDTOrderSafeTail->count(p_bid_context->campaign_id() % 100) > 0);
    auto hostingLiveDTRoasNewSafeTail = MerchantKconfUtil::hostingLiveDTRoasNewSafeTail();
    auto hostingLiveDTOrderNewSafeTail = MerchantKconfUtil::hostingLiveDTOrderNewSafeTail();
    auto hostingLiveDTT7NewSafeTail = MerchantKconfUtil::hostingLiveDTT7NewSafeTail();
    bool is_hosting_dt_safe_tail_new = (p_util_vals->is_roas_hosting_live && hostingLiveDTRoasNewSafeTail &&
      hostingLiveDTRoasNewSafeTail->count(p_bid_context->campaign_id() % 100) > 0 &&
      p_bid_context->ocpc_action_type() != "AD_MERCHANT_T7_ROI") ||
      (p_util_vals->is_order_hosting_live && hostingLiveDTOrderNewSafeTail &&
      hostingLiveDTOrderNewSafeTail->count(p_bid_context->campaign_id() % 100) > 0) ||
      (p_bid_context->ocpc_action_type() == "AD_MERCHANT_T7_ROI" && hostingLiveDTT7NewSafeTail &&
      hostingLiveDTT7NewSafeTail->count(p_bid_context->campaign_id() % 100) > 0);
    bool is_bad_case;
    if (is_hosting_dt_safe_tail_new) {
      is_bad_case = (p_bid_context->cost() > 1.2 * p_bid_context->target_cost() && model_pred > 1 &&
        p_bid_context->pacing_weight() < 1) || (p_bid_context->cost() < 0.8 * p_bid_context->target_cost() &&
        p_bid_context->pacing_weight() > 1 && model_pred < 1 && model_pred > 0);
    } else {
      is_bad_case = (p_bid_context->cost() > p_bid_context->target_cost() && model_pred > 1 &&
        p_bid_context->pacing_weight() < 1) || (p_bid_context->cost() < p_bid_context->target_cost() &&
        p_bid_context->pacing_weight() > 1 && model_pred < 1 && model_pred > 0);
    }
    if (is_bad_case && is_hosting_dt_safe_tail) {
      p_bid_context->set_hosting_dt_bad_count(p_bid_context->hosting_dt_bad_count() + 1);
    }
    LOG_EVERY_N(INFO, 10000) << "xusimin_debug_dt_res" << ", "
      << "unit_id: " << p_bid_context->unit_id() << ", "
      << "campaign_id: " << p_bid_context->campaign_id() << ", "
      << "ocpc_action_type: " << p_bid_context->ocpc_action_type() << ", "
      << "now_sec: " << now_sec << ", "
      << "rl_time_cost_total: " << rl_time_cost_total << ", "
      << "old_adjust_auto_value_rate: " << old_adjust_auto_value_rate << ", "
      << "hosting_dt_bad_count: " << p_bid_context->hosting_dt_bad_count() << ", "
      << "pacing_weight: " << p_bid_context->pacing_weight() << ", "
      << "model_pred: " << p_bid_context->inner_rl_data().inner_rl_model_pred() << ", "
      << "InnerRlData: " << p_bid_context->inner_rl_data().ShortDebugString();
  }

  // 全站 nobid 暗投处理
  if (p_util_vals->is_storewide_shadow_nobid) {
    NativeLowestCostContext* lowest_cost_context = p_bid_context->mutable_native_lowest_cost_context();
    res_adjust_auto_value_rate = lowest_cost_context->pacing_rate() > 0 ?
                                lowest_cost_context->pacing_rate() : 1.0;
    p_bid_context->set_adjust_auto_value_rate(res_adjust_auto_value_rate);
  }

  // 本地生活出价系数重置
  if (MerchantKconfUtil::enablelspLiveOp() && p_util_vals->is_local_life_live_ocpm) {
    int64 lsp_key_id = p_bid_context->is_cbo() ?
        p_bid_context->campaign_id() : p_bid_context->unit_id();
    const auto lsp_live_op_reset_tail = MerchantKconfUtil::lspLiveOpResetTail();
    if (lsp_live_op_reset_tail && lsp_live_op_reset_tail->IsOnFor(lsp_key_id) &&
        (exp_config.lsp_op_reset_ocpx_str == "" || exp_config.lsp_op_reset_ocpx_str.find(
            p_bid_context->ocpc_action_type()) != std::string::npos)) {
      p_bid_context->set_adjust_auto_value_rate(p_util_vals->new_adjust_auto_value_rate);
      res_adjust_auto_value_rate = p_util_vals->new_adjust_auto_value_rate;
      p_util_vals->lsp_bid_op_flag |= 1;
    }
  }
  // 本地生活保底消耗
  if (MerchantKconfUtil::enablelspLiveMinCost() && p_util_vals->is_local_life_live_ocpm) {
    // 所有变量先初始化
    p_util_vals->avg_bid_coef = 0.0;
    p_util_vals->min_day_cost_target_value_flag = 0;
    p_util_vals->min_day_cost_target_value = 0.0;
    p_util_vals->min_day_cost_strategy_admit = false;
    p_util_vals->min_day_cost_strategy_hit = false;
    p_util_vals->bid_coef_before_min_cost = 0.0;
    p_util_vals->bid_coef_after_min_cost = 0.0;

    // 获取 min_day_cost
    double min_day_cost = 0;
    int64 key_id = p_bid_context->is_cbo() ? p_bid_context->campaign_id() : p_bid_context->unit_id();
    const auto lsp_live_min_cost_tail = MerchantKconfUtil::lspLiveMinCostTail();
    const auto lsp_live_min_cost_config = MerchantKconfUtil::lspLiveMinCostConfig();
    // 黑盒
    if (lsp_live_min_cost_tail && lsp_live_min_cost_tail->IsOnFor(key_id)) {
      if (p_util_vals->is_roas) {
        min_day_cost = exp_config.roi_min_day_cost_target_value;
      } else {
        double target_cpa_bid = GetTargetCpaBid(p_bid_context);
        min_day_cost = std::max(exp_config.non_roi_min_day_cost_target_value,
            exp_config.non_roi_min_day_cost_target_cv * target_cpa_bid);
      }
      if (min_day_cost > 0) {
        p_util_vals->min_day_cost_target_value_flag = 1;
      }
    }
    // 白盒
    if (lsp_live_min_cost_config) {
      if (MerchantKconfUtil::enablelspLiveOp()) {
        auto iter_lsp_live_min_cost_account = lsp_live_min_cost_config->find(
            p_bid_context->account_id());
        if (iter_lsp_live_min_cost_account != lsp_live_min_cost_config->end()) {
          min_day_cost = iter_lsp_live_min_cost_account->second * 1000.0;  // 元 -> 厘
          p_util_vals->min_day_cost_target_value_flag = 2;  // 不需要判断 min_day_cost 大于 0
        }
      }
      auto iter_lsp_live_min_cost_config = lsp_live_min_cost_config->find(key_id);
      if (iter_lsp_live_min_cost_config != lsp_live_min_cost_config->end()) {
        min_day_cost = iter_lsp_live_min_cost_config->second * 1000.0;  // 元 -> 厘
        p_util_vals->min_day_cost_target_value_flag = 2;  // 不需要判断 min_day_cost 大于 0
      }
    }
    p_util_vals->min_day_cost_target_value = min_day_cost;

    // min_cost_adjust_auto_value_rate 可以对 avg_day_cost 做冷启加速
    double min_cost_adjust_auto_value_rate = exp_config.init_min_cost_adjust_auto_value_rate;

    // 依赖 avg_day_cost & avg_day_bid_coef_cost 计算平均出价系数和保底消耗系数
    double avg_day_cost = std::max(p_bid_context->avg_day_cost(), 1.0);
    double avg_day_bid_cost = std::max(p_bid_context->avg_day_bid_cost(), 1.0);
    double avg_day_bid_coef_cost = p_bid_context->avg_day_bid_coef_cost();
    double avg_bid_coef = avg_day_bid_coef_cost / avg_day_cost;
    if (exp_config.enable_avg_day_bid_cost == 1) {
      avg_bid_coef = avg_day_bid_coef_cost / avg_day_bid_cost;
    }
    double power_coef = IsGTZero(exp_config.min_cost_k) ? (1.0 / exp_config.min_cost_k) : 0.5;
    p_util_vals->avg_bid_coef = avg_bid_coef;
    if (avg_day_cost > exp_config.calc_min_cost_thres) {
      min_cost_adjust_auto_value_rate = avg_bid_coef * std::pow(min_day_cost / avg_day_cost, power_coef);
    }
    // 防止保底消耗提价过猛
    double upper_res_adjust_auto_value_rate =
        res_adjust_auto_value_rate * std::max(exp_config.min_cost_bid_coef_upper_ratio, 1.0);
    min_cost_adjust_auto_value_rate = std::min(min_cost_adjust_auto_value_rate,
        upper_res_adjust_auto_value_rate);
    // 确保保底消耗是提价策略
    min_cost_adjust_auto_value_rate = std::max(min_cost_adjust_auto_value_rate, 1.0);

    // 生效保底消耗策略
    p_util_vals->bid_coef_before_min_cost = res_adjust_auto_value_rate;
    p_util_vals->bid_coef_after_min_cost = min_cost_adjust_auto_value_rate;
    if (p_bid_context->cost() < p_util_vals->min_day_cost_target_value) {
      p_util_vals->min_day_cost_strategy_admit = true;
      if (min_cost_adjust_auto_value_rate > res_adjust_auto_value_rate) {
        p_util_vals->min_day_cost_strategy_hit = true;
        res_adjust_auto_value_rate = min_cost_adjust_auto_value_rate;
        p_bid_context->set_adjust_auto_value_rate(res_adjust_auto_value_rate);
        // 如果更新, 强制同步
        p_util_vals->is_sync_to_dsp = true;
      }
    }
  }

  bool is_storewide_live_order_acc = (bid_info_ptr && IsStorewideAccFixExp(bid_info_ptr) &&
                              bid_info_ptr->ocpx_action_type == 395);
  bool is_campaing_acc_explore = IsStorewideAccFixExpV2(bid_info_ptr);
  if (p_util_vals->is_roas && !is_roas_hosting_live_order_unit &&
      (!is_storewide_live_order_acc || is_campaing_acc_explore)) {
    double adjust_roas = GetTargetRoas(p_bid_context);
    if (p_bid_context->adjust_auto_value_rate() > 0.0) {
      adjust_roas /= res_adjust_auto_value_rate;
      adjust_roas = std::max(adjust_roas, p_util_vals->hard_lower_bound_auto_roas);
      adjust_roas = std::min(adjust_roas, p_util_vals->hard_upper_bound_auto_roas);
      p_util_vals->new_adjust_auto_value_rate = GetTargetRoas(p_bid_context) / (adjust_roas + 1e-6);
      const auto fixBidConvertBugTail = MerchantKconfUtil::fixBidConvertBugTail();
      bool fix_bid_convert_bug = fixBidConvertBugTail &&
            fixBidConvertBugTail->IsOnFor(p_bid_context->campaign_id());
      if (fix_bid_convert_bug && adjust_roas > 0) {
        p_util_vals->new_adjust_auto_value_rate = GetTargetRoas(p_bid_context) / adjust_roas;
      }
      p_bid_context->set_adjust_auto_value_rate(p_util_vals->new_adjust_auto_value_rate);
      if (is_cold_unit_photo == 1 && cold_photo_boost_ratio >= 1.0) {
        adjust_roas /= cold_photo_boost_ratio;
        adjust_roas = std::max(adjust_roas, p_util_vals->hard_lower_bound_auto_roas);
        adjust_roas = std::min(adjust_roas, p_util_vals->hard_upper_bound_auto_roas);
      } else if (!IsLEZero(unit_adjust_rate)) {
        adjust_roas /= unit_adjust_rate;
      }
    }
    SetAutoRoiRatio(p_bid_context, adjust_roas, p_util_vals);
    // 更新 auto_bid_explore
    if (p_bid_context->adjust_rate_acc_increment() > 0) {
      double auto_roas_explore = GetTargetRoas(p_bid_context) / p_bid_context->adjust_rate_acc_increment();
      p_bid_context->set_auto_bid_explore(auto_roas_explore);
      ks::infra::PerfUtil::SetLogStash(auto_roas_explore, "ad.ad_bid_orbit_server",
                                       "auto_roas_explore", std::to_string(p_bid_context->unit_id()),
                                       p_bid_context->group_tag(), "ocpm_acc",
                                       std::to_string(p_bid_context->campaign_id()));
    }
  } else {
    double target_cpa_bid = GetTargetCpaBid(p_bid_context);
    if (is_cold_unit_photo == 1 && cold_photo_boost_ratio >= 1.0) {
      SetAutoCpaBid(p_bid_context, bid_info_ptr, std::max(1.0,
          target_cpa_bid * res_adjust_auto_value_rate * cold_photo_boost_ratio), p_util_vals);
    } else if (!IsLEZero(unit_adjust_rate)) {
      SetAutoCpaBid(p_bid_context, bid_info_ptr, std::max(1.0,
          target_cpa_bid * res_adjust_auto_value_rate * unit_adjust_rate), p_util_vals);
    } else {
      if (enable_hosting_dt_res) {
        p_bid_context->set_adjust_auto_value_rate(res_adjust_auto_value_rate);
      }
      SetAutoCpaBid(p_bid_context, bid_info_ptr, std::max(1.0,
          target_cpa_bid * res_adjust_auto_value_rate), p_util_vals);
    }
    // 更新 auto_bid_explore
    if (p_bid_context->adjust_rate_acc_increment() > 0) {
      double auto_cpa_bid_explore = target_cpa_bid * p_bid_context->adjust_rate_acc_increment();
      p_bid_context->set_auto_bid_explore(auto_cpa_bid_explore);
      ks::infra::PerfUtil::SetLogStash(auto_cpa_bid_explore, "ad.ad_bid_orbit_server",
                                       "auto_cpa_bid_explore", std::to_string(p_bid_context->unit_id()),
                                       p_bid_context->group_tag(), "ocpm_acc",
                                       std::to_string(p_bid_context->campaign_id()));
    }
  }
  // 本地生活重写系统出价
  if (MerchantKconfUtil::enablelspLiveOp() && p_util_vals->is_local_life_live_ocpm) {
    int64 lsp_key_id = p_bid_context->is_cbo() ? p_bid_context->campaign_id() : p_bid_context->unit_id();
    const auto lsp_live_op_rewrite_tail = MerchantKconfUtil::lspLiveOpReWriteTail();
    if (lsp_live_op_rewrite_tail && lsp_live_op_rewrite_tail->IsOnFor(lsp_key_id) &&
        (exp_config.lsp_op_rewrite_ocpx_str == "" || exp_config.lsp_op_rewrite_ocpx_str.find(
            p_bid_context->ocpc_action_type()) != std::string::npos)) {
      double adjust_auto_value_rate = p_bid_context->adjust_auto_value_rate();
      p_util_vals->lsp_hard_bid_lower_bound_rate_monitor =
          p_util_vals->hard_bid_lower_bound_rate_monitor;
      p_util_vals->lsp_hard_bid_upper_bound_rate_monitor =
          p_util_vals->hard_bid_upper_bound_rate_monitor;
      if (p_util_vals->is_roas) {
        if (exp_config.enable_lsp_adjust_roi_bound == 1) {
          p_util_vals->lsp_hard_bid_lower_bound_rate_monitor =
              exp_config.lsp_adjust_roi_lower_bound;
          p_util_vals->lsp_hard_bid_upper_bound_rate_monitor =
              exp_config.lsp_adjust_roi_upper_bound;
        }
        adjust_auto_value_rate = std::max(adjust_auto_value_rate,
            p_util_vals->lsp_hard_bid_lower_bound_rate_monitor);
        adjust_auto_value_rate = std::min(adjust_auto_value_rate,
            p_util_vals->lsp_hard_bid_upper_bound_rate_monitor);
        double target_roas = GetTargetRoas(p_bid_context);
        if (!IsLEZero(adjust_auto_value_rate)) {
          double adjust_roas = target_roas / adjust_auto_value_rate;
          SetAutoRoiRatio(p_bid_context, adjust_roas, p_util_vals);
        }
      } else {
        if (exp_config.enable_lsp_adjust_non_roi_bound == 1) {
          p_util_vals->lsp_hard_bid_lower_bound_rate_monitor =
              exp_config.lsp_adjust_non_roi_lower_bound;
          p_util_vals->lsp_hard_bid_upper_bound_rate_monitor =
              exp_config.lsp_adjust_non_roi_upper_bound;
        }
        adjust_auto_value_rate = std::max(adjust_auto_value_rate,
                                          p_util_vals->lsp_hard_bid_lower_bound_rate_monitor);
        adjust_auto_value_rate = std::min(adjust_auto_value_rate,
                                          p_util_vals->lsp_hard_bid_upper_bound_rate_monitor);
        double target_cpa_bid = GetTargetCpaBid(p_bid_context);
        double adjust_cpa_bid = target_cpa_bid * adjust_auto_value_rate;
        SetAutoCpaBid(p_bid_context, bid_info_ptr, adjust_cpa_bid, p_util_vals);
      }
      p_util_vals->new_adjust_auto_value_rate = adjust_auto_value_rate;
      p_bid_context->set_adjust_auto_value_rate(p_util_vals->new_adjust_auto_value_rate);
      p_util_vals->lsp_bid_op_flag |= 2;
    }
  }
  if (SPDM_enableAddBidContext()) {
    if (p_bid_context->old_adjust_auto_value_rate() > 0 &&
        p_bid_context->adjust_auto_value_rate() > 0) {
      p_bid_context->set_single_bid_ratio(p_bid_context->adjust_auto_value_rate() /
                                p_bid_context->old_adjust_auto_value_rate() * 1.0);
    } else {
      p_bid_context->set_single_bid_ratio(1.0);
    }
    p_bid_context->set_old_adjust_auto_value_rate(p_bid_context->adjust_auto_value_rate());
  }
}

inline double MerchantOcpmStrategy::GetLiveHostingCpaBid(OcpmBidContext* p_bid_context,
                                    const BidStateInfoPtr& bid_info_ptr) {
  double cpa_bid = GetTargetCpaBid(p_bid_context);
  auto* jinniu_nobid_cpa_bid_instance = JinniuNobidCpaBid::GetInstance();
  if (!jinniu_nobid_cpa_bid_instance || !bid_info_ptr) {
    return cpa_bid;
  }
  auto order_bid_campaign_tail_set = AdKconfUtil::orderBidCampaignTailSet();
  auto order_bid_account_id_set = AdKconfUtil::orderBidAccountIdSet();
  bool is_cbo_order_bid_campaign = (order_bid_campaign_tail_set
      && order_bid_campaign_tail_set->find(p_bid_context->campaign_id() % 100) !=
      order_bid_campaign_tail_set->end()) || (order_bid_account_id_set
      && order_bid_account_id_set->find(p_bid_context->account_id()) !=
      order_bid_account_id_set->end());
  bool cbo_valid =
      is_cbo_order_bid_campaign && IsCboCostCap(bid_info_ptr->unit_id, bid_info_ptr) &&
      (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
       (SPDM_enableLSPHosting() &&
        bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE));
  if (cbo_valid && p_bid_context->ocpc_action_type() == "EVENT_ORDER_PAIED") {
    double order_pay_cpa_bid = 0.0;
    std::string order_pay_specialty_key =
                absl::Substitute("$0_SPECIALTY_PROMOTION_ITEM_LIVE_395", p_bid_context->author_id());
    std::string order_pay_specialty_p2l_key =
                absl::Substitute("$0_SPECIALTY_PROMOTION_ITEM_PHOTO_TO_LIVE_395", p_bid_context->author_id());
    std::string order_pay_flash_key =
                absl::Substitute("$0_FLASH_PROMOTION_ITEM_LIVE_395", p_bid_context->author_id());
    std::string order_pay_flash_p2l_key =
                absl::Substitute("$0_FLASH_PROMOTION_ITEM_PHOTO_TO_LIVE_395", p_bid_context->author_id());
    order_pay_cpa_bid = jinniu_nobid_cpa_bid_instance->GetNobidCpaBid(order_pay_specialty_key);
    order_pay_cpa_bid = std::min(order_pay_cpa_bid,
       jinniu_nobid_cpa_bid_instance->GetNobidCpaBid(order_pay_specialty_p2l_key));
    order_pay_cpa_bid = std::min(order_pay_cpa_bid,
       jinniu_nobid_cpa_bid_instance->GetNobidCpaBid(order_pay_flash_key));
    order_pay_cpa_bid = std::min(order_pay_cpa_bid,
       jinniu_nobid_cpa_bid_instance->GetNobidCpaBid(order_pay_flash_p2l_key));
    if (cpa_bid * AdKconfUtil::orderBidUpperbound() > order_pay_cpa_bid &&
        cpa_bid * AdKconfUtil::orderBidLowerbound() < order_pay_cpa_bid) {
      cpa_bid = order_pay_cpa_bid;
    }
  }
  return cpa_bid;
}

inline void MerchantOcpmStrategy::SetAutoRoiRatio(
  OcpmBidContext* p_bid_context, const double new_ratio, ProcessUtilValues* p_util_vals) {
  if (!std::isnormal(new_ratio)) {
    p_util_vals->is_normal = false;
    return;
  } else {
    p_bid_context->set_old_auto_roi_ratio(p_bid_context->auto_roi_ratio());
    p_bid_context->set_auto_roi_ratio(new_ratio);
    p_util_vals->is_normal = true;
  }
  if (p_util_vals->is_debug) {
    LOG(WARNING) << "SetAutoRoiRatio:tid:" << p_util_vals->thread_id
      << ", unit_id: " << p_bid_context->unit_id()
      << ", adjust_rate_lower_bound: " << p_util_vals->adjust_rate_lower_bound
      << ", adjust_rate_upper_bound: " << p_util_vals->adjust_rate_upper_bound
      << ", hard_lower_bound_auto_roas: " << p_util_vals->hard_lower_bound_auto_roas
      << ", hard_upper_bound_auto_roas: " << p_util_vals->hard_upper_bound_auto_roas
      << ", target_roi: " << GetTargetRoas(p_bid_context)
      << ", old auto_roi: " << p_bid_context->old_auto_roi_ratio()
      << ", new auto_roi: " << new_ratio;}
}

inline void MerchantOcpmStrategy::SetAutoCpaBid(
  OcpmBidContext* p_bid_context, const BidStateInfoPtr& bid_info_ptr,
  const double new_value, ProcessUtilValues* p_util_vals) {
  if (!std::isnormal(new_value)) {
    p_util_vals->is_normal = false;
    return;
  } else {
    p_bid_context->set_old_auto_cpa_bid(p_bid_context->auto_cpa_bid());
    if (p_util_vals->is_fanstop
        && !IsFanstopAutoBidLiExp(p_bid_context->unit_id(), bid_info_ptr)) {
      // Notice: 移动端的非 ROI 优化目标的单位是分
      p_bid_context->set_auto_cpa_bid(new_value / 10);
    } else {
      p_bid_context->set_auto_cpa_bid(new_value);
    }
    p_util_vals->is_normal = true;
  }
  if (p_util_vals->is_debug) {
    LOG(WARNING) << "SetAutoCpaBid:tid:" << p_util_vals->thread_id
                 << ", unit_id: " << p_bid_context->unit_id()
                 << ", old_auto_cpa_bid: " << p_bid_context->old_auto_cpa_bid()
                 << ", auto_cpa_bid: " << p_bid_context->auto_cpa_bid();
  }
}

inline MerchantOcpmBidConfig MerchantOcpmStrategy::GetStrategyConfig(
  const std::string& campaign_type, const std::string& ocpc_action_type) {
  if (ocpc_action_type == "AD_STOREWIDE_ROAS" ||
      ocpc_action_type == "AD_MERCHANT_T7_ROI") {
    return MerchantBidConfMgr::Instance().GetOcpmStrategyConfig();  // ad.bidServer.merchantOcpmBidConf
  }
  if (campaign_type == "LIVE_STREAM_PROMOTE" || IsFanstop(campaign_type)) {
    if (ocpc_action_type == "AD_LIVE_AUDIENCE") {
      return MerchantBidConfMgr::Instance().GetOcpmLiveAudienceStrategyConfig();
    } else if (ocpc_action_type == "AD_MERCHANT_FOLLOW") {
      return MerchantBidConfMgr::Instance().GetOcpmLiveFollowStrategyConfig();
    } else if (ocpc_action_type == "EVENT_ORDER_PAIED") {
      return MerchantBidConfMgr::Instance().GetOcpmLiveOrderStrategyConfig();
    }
    return MerchantBidConfMgr::Instance().GetOcpmStrategyConfig();
  } else {
    return MerchantBidConfMgr::Instance().GetOcpmPhotoStrategyConfig();
  }
  return MerchantBidConfMgr::Instance().GetOcpmStrategyConfig();
}

inline bool MerchantOcpmStrategy::IsProcessUpdate(const MerchantOcpmMsg& msg,
  OcpmBidContext* p_bid_context, ProcessUtilValues* p_util_vals) {
  if (msg.is_hot_unit) {return false;}
  if (p_util_vals->is_skip_update) {return false;}
  if (p_util_vals->is_no_bid) {return false;}
  if (p_bid_context->campaign_type() == "LIVE_STREAM_PROMOTE") {
    return MerchantKconfUtil::isOcpmLiveAd();
  } else if (p_bid_context->campaign_type() == "MERCHANT_RECO_PROMOTE") {
    if (p_util_vals->is_storewide_roas) {
      return true;
    }
    if (p_util_vals->is_nobid_acc) {
      return true;
    }
    return false;
  } else if (p_util_vals->is_fanstop) {
    return MerchantKconfUtil::isFanstopOcpmAd();
  } else {
    return false;
  }
}

inline bool MerchantOcpmStrategy::CheckMsg(BidStateInfoPtr bid_info_ptr, ResetMsg* p_msg) {
  if (!bid_info_ptr) {
    if (MerchantKconfUtil::enableResetLargeLog()) {
      LOG(INFO) << "Reset, MerchantOcpmStrategy::CheckMsg bid_info_ptr null";
    }
    return false;
  }
  if (bid_info_ptr->speed_type == 5) {
    if (MerchantKconfUtil::enableResetLargeLog()) {
      LOG(INFO) << "Reset, MerchantOcpmStrategy::CheckMsg speed type: "
                << kuaishou::ad::AdEnum::SpeedType_Name(bid_info_ptr->speed_type);
    }
    return false;
  }
  return true;
}

inline void MerchantOcpmStrategy::CheckMsg(BidStateInfoPtr bid_info_ptr, MerchantOcpmMsg* p_msg) {
  if (!bid_info_ptr) {
    ks::infra::PerfUtil::CountLogStash(1, kCommonPerfNameSpace,
      "bid_info_ptr_isnull", std::to_string(p_msg->unit_id), std::to_string(p_msg->campaign_id));
    return;
  }
  if (p_msg->conv_num > 0 && IsLEZero(p_msg->target_cost)) {
    if ((p_msg->ocpc_action_type == "AD_MERCHANT_ROAS" ||
         p_msg->ocpc_action_type == "AD_MERCHANT_T7_ROI" ||
         p_msg->ocpc_action_type == "AD_FANS_TOP_ROI" ||
         p_msg->is_storewide_with_order)
        && IsGTZero(bid_info_ptr->roi_ratio)) {
      p_msg->target_cost = p_msg->gmv / bid_info_ptr->roi_ratio;
    } else {
      p_msg->target_cost = bid_info_ptr->cpa_bid;
    }
  }
  if (p_msg->speed_type <= 0) {
    p_msg->speed_type = bid_info_ptr->speed_type;
  }
  if (p_msg->campaign_type.size() < 3) {
    p_msg->campaign_type = kuaishou::ad::AdEnum::CampaignType_Name(bid_info_ptr->campaign_type);
  }
  if (p_msg->ocpc_action_type.size() < 3) {
    p_msg->ocpc_action_type = kuaishou::ad::AdActionType_Name(bid_info_ptr->ocpx_action_type);
  }
}

inline OcpmBidContext* MerchantOcpmStrategy::GetBidContext(
  const MerchantOcpmBidConfig& strategy_config,
  uint64_t key_id, const std::string& group_tag, BidDataLevel bid_data_level) {
  std::string bid_context_key;
  bool is_cbo = false;
  bool enable_p2l_single = false;
  if (bid_data_level == +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL) {
    bid_context_key = GetCampaignBidContextKey(key_id, group_tag);
    is_cbo = true;
  } else if (bid_data_level == +BidDataLevel::UNIT_P2L_BID_DATA_LEVEL) {
    bid_context_key = GetP2LUnitBidContextKey(key_id, group_tag);
    enable_p2l_single = true;
  } else {
    bid_context_key = GetUnitBidContextKey(key_id, group_tag);
  }
  OcpmBidContext* p_bid_context =
    MerchantDataMgr::Instance().GetOcpmBidContext(bid_context_key);
  if (!p_bid_context) {
    return nullptr;
  }
  p_bid_context->set_is_cbo(is_cbo);
  p_bid_context->set_enable_p2l_single(enable_p2l_single);

  if (p_bid_context->bid_context_key().size() < 1) {
    uint64_t group_tag_num = GetGroupTagNum(group_tag);
    InitOcpmBidContext(bid_context_key, group_tag, group_tag_num, p_bid_context);
  }

  if (!strategy_config.context_auto_reload()) {
    return p_bid_context;
  }
  return p_bid_context;
}

inline OcpmBidContext* MerchantOcpmStrategy::BidContextGetterNew(
    const MerchantOcpmBidConfig& strategy_config,
    uint64_t unit_id,
    uint64_t campaign_id,
    uint64_t account_id,
    uint64_t author_id,
    uint64_t speed_type,
    uint64_t scene_oriented_type,
    const std::string& msg_group_tag,
    const std::string& bid_strategy,
    const std::string& ocpc_action_type,
    const std::string& campaign_type,
    const std::string& item_type,
    BidStateInfoPtr bid_info_ptr) {
  auto key_id = unit_id;
  auto bid_type = +BidDataLevel::UNIT_BID_DATA_LEVEL;
  std::string group_tag = msg_group_tag;
  const auto acc_explore_campaign_tail = MerchantKconfUtil::storewideAccExploreCampaignTail();
  auto is_acc_explore = IsAccExplore(group_tag);
  if (scene_oriented_type == 23 ||
      (SPDM_enableLSPHosting() && scene_oriented_type == 35) ||
      (bid_info_ptr &&
       (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
        (SPDM_enableLSPHosting() &&
         bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE)))) {
    key_id = campaign_id;
    bid_type = +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL;
    group_tag = static_cast<std::string>(absl::Substitute("$0_$1", ocpc_action_type, group_tag));
    auto esp_live_hosting_roas_union_bid_tail = MerchantKconfUtil::espLiveHostingRoasUnionBidTail();
    if (esp_live_hosting_roas_union_bid_tail && esp_live_hosting_roas_union_bid_tail->IsOnFor(campaign_id)) {
      group_tag = msg_group_tag;
    }
  } else if (SPDM_enableStorewideAccCampaign() && acc_explore_campaign_tail &&
             acc_explore_campaign_tail->IsOnFor(campaign_id) && IsStorewideAccFixExpV2(bid_info_ptr)) {
    key_id = campaign_id;
    bid_type = +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL;
  } else if (IsStorewideAccFixExp(bid_info_ptr)) {
    key_id = unit_id;
    bid_type = +BidDataLevel::UNIT_BID_DATA_LEVEL;
  } else if (IsCbo(bid_info_ptr) ||
             ocpc_action_type == "AD_STOREWIDE_ROAS") {
    key_id = campaign_id;
    bid_type = +BidDataLevel::CAMPAIGN_BID_DATA_LEVEL;
  } else {
    bool is_universe = IsUniverse(group_tag) && MerchantKconfUtil::enableUniverseSplitP2l();
    bool enable_p2l_single =
        !is_universe && !is_acc_explore &&
        IsEspLiveSplitExp(unit_id, speed_type, campaign_type, bid_strategy, ocpc_action_type);

    if (enable_p2l_single && IsP2lItemType(item_type)) {
      key_id = unit_id;
      bid_type = +BidDataLevel::UNIT_P2L_BID_DATA_LEVEL;
    } else {
      key_id = unit_id;
      bid_type = +BidDataLevel::UNIT_BID_DATA_LEVEL;
    }
  }
  return GetBidContext(strategy_config, key_id, group_tag, bid_type);
}

inline OcpmBidContext* MerchantOcpmStrategy::BidContextGetter(const MerchantOcpmBidConfig& strategy_config,
                                                              const MerchantOcpmMsg& msg,
                                                              BidStateInfoPtr bid_info_ptr) {
  return BidContextGetterNew(strategy_config, msg.unit_id, msg.campaign_id,
                             msg.account_id, msg.author_id, msg.speed_type, msg.scene_oriented_type,
                             msg.group_tag, msg.bid_strategy, msg.ocpc_action_type,
                             msg.campaign_type, msg.item_type, bid_info_ptr);
}

inline ProcessUtilValues MerchantOcpmStrategy::GetUtilValues(
  const std::string& campaign_type, const std::string& ocpc_action_type) {
  ProcessUtilValues util_vals;
  if (ocpc_action_type == "AD_MERCHANT_ROAS" || ocpc_action_type == "AD_FANS_TOP_ROI" ||
      ocpc_action_type == "AD_STOREWIDE_ROAS" ||
      ocpc_action_type == "AD_MERCHANT_T7_ROI") {
    util_vals.is_roas = true;
    if (ocpc_action_type == "AD_STOREWIDE_ROAS") {
      util_vals.is_storewide_roas = true;
    }
  }
  if (IsFanstop(campaign_type)) {
    util_vals.is_fanstop = true;
  }
  return util_vals;
}

inline ProcessUtilValues MerchantOcpmStrategy::GetUtilValues(const MerchantOcpmMsg& msg,
                                                             const MerchantOcpmBidConfig& strategy_config,
                                                             const MerchantOcpmExpConfig& exp_config,
                                                             BidStateInfoPtr bid_info_ptr) {
  ProcessUtilValues util_vals;
  util_vals.unit_id = msg.unit_id;
  util_vals.thread_id = thread::GetThreadID();
  if (MerchantBidConfMgr::Instance().GetDebugUnitIdSetPtr()->count(util_vals.unit_id) > 0) {
    util_vals.is_debug = true;
  }
  util_vals.current_ms = TimeUtil::Instance().GetTimestamp() / 1000;
  util_vals.start_bid_rate = strategy_config.start_bid_rate();

  util_vals.is_apply_operation_config = false;
  util_vals.is_start_process = false;
  if (bid_info_ptr) {
    util_vals.left_budget = bid_info_ptr->left_budget;
  }
  if (msg.medium_attribute == 2 || msg.medium_attribute == 4) {
    if (MerchantBidConfMgr::Instance().GetValieUniverseGroupTagSetPtr()->count(msg.group_tag) <= 0) {
      util_vals.is_skip_update = true;
    }
  }
  const auto acc_explore_campaign_tail = MerchantKconfUtil::storewideAccExploreCampaignTail();
  bool is_acc_explore_campaign = false;
  if (SPDM_enableStorewideAccCampaign() && acc_explore_campaign_tail && bid_info_ptr &&
      acc_explore_campaign_tail->IsOnFor(msg.campaign_id) && bid_info_ptr->explore_put_type == 3) {
    is_acc_explore_campaign = true;
  }
  if (msg.ocpc_action_type == "AD_MERCHANT_ROAS" || msg.ocpc_action_type == "AD_FANS_TOP_ROI" ||
      msg.is_storewide_with_order ||
      msg.ocpc_action_type == "AD_MERCHANT_T7_ROI") {
    util_vals.is_roas = true;
    if (msg.is_storewide_with_order) {
      util_vals.is_storewide_roas = true;
      if (IsCboStoreWide(bid_info_ptr)) {
        util_vals.is_cbo_storewide_roas = true;
      }
      if (bid_info_ptr && (bid_info_ptr->explore_put_type == 2 || is_acc_explore_campaign) &&
          !msg.is_increment_explore) {
        util_vals.is_storewide_acc2_kcb = true;
      }
    }
  } else if (msg.ocpc_action_type == "AD_MERCHANT_FOLLOW" ||
             msg.ocpc_action_type == "AD_MERCHANT_FOLLOW_QUALITY") {
    util_vals.is_high_quality_follow = true;
  }

  if (IsCbo(bid_info_ptr) ||
      msg.is_storewide_with_order) {
    util_vals.is_cbo = true;
  }
  if (msg.speed_type == 5) {
    util_vals.is_no_bid = true;
  }
  // 本地生活
  if ((bid_info_ptr && kuaishou::ad::AdEnum::AdDspAccountType_Name(bid_info_ptr->account_type) ==
      "ACCOUNT_LSP") || msg.account_type == "ACCOUNT_LSP") {
    util_vals.is_local_life = true;
    if ((bid_info_ptr && kuaishou::ad::AdEnum::CampaignType_Name(bid_info_ptr->campaign_type) ==
        "LIVE_STREAM_PROMOTE") || msg.campaign_type == "LIVE_STREAM_PROMOTE") {
      util_vals.is_local_life_live = true;
      if (!util_vals.is_no_bid) {
        util_vals.is_local_life_live_ocpm = true;
      }
    }
  }
  if (msg.campaign_type == "LIVE_STREAM_PROMOTE" &&
      msg.ocpc_action_type == "AD_MERCHANT_ROAS" &&
      msg.speed_type == 1 &&
      !util_vals.is_cbo) {
    util_vals.is_unit_ocpm_roas_pc = true;
  }
  if (msg.campaign_type == "LIVE_STREAM_PROMOTE" &&
      msg.ocpc_action_type == "EVENT_ORDER_PAIED" &&
      msg.speed_type == 1 &&
      !util_vals.is_cbo) {
    util_vals.is_unit_ocpm_eop_pc = true;
  }
  if (IsFanstopLiveOcpm(msg.campaign_type, msg.bid_strategy) &&
      msg.ocpc_action_type == "AD_FANS_TOP_ROI" &&
      !util_vals.is_cbo) {
    util_vals.is_unit_ocpm_roas_fanstop = true;
  }
  if (IsFanstopLiveOcpm(msg.campaign_type, msg.bid_strategy) &&
      msg.ocpc_action_type == "EVENT_ORDER_PAIED" &&
      !util_vals.is_cbo) {
    util_vals.is_unit_ocpm_eop_fanstop = true;
  }
  util_vals.is_debug_on = strategy_config.is_debug_on();
  if (bid_info_ptr) {
    util_vals.budget = bid_info_ptr->budget;
  }

  // 移动端实验配置
  if (IsFanstop(msg.campaign_type)) {
    util_vals.is_fanstop = true;
    // 使用时设置兜底值
    util_vals.fanstop_adjust_rate_lower_bound_roi = exp_config.fanstop_adjust_rate_lower_bound_roi;
    util_vals.fanstop_adjust_rate_upper_bound_roi = exp_config.fanstop_adjust_rate_upper_bound_roi;
    util_vals.fanstop_adjust_rate_lower_bound_cpa = exp_config.fanstop_adjust_rate_lower_bound_cpa;
    util_vals.fanstop_adjust_rate_upper_bound_cpa = exp_config.fanstop_adjust_rate_upper_bound_cpa;
    if (IsFanstopOcpm(msg.campaign_type, msg.bid_type) || !IsLEZero(exp_config.enable_unify_fanstop_params)) {
      util_vals.is_fanstop_ocpm = true;
    }
  }

  // 加速探索配置
  if (IsNobidAcc(bid_info_ptr)) {
    util_vals.is_nobid_acc = true;
  }
  util_vals.is_acc_increment = msg.is_increment_explore;

  // pacing_type 实验配置
  if (util_vals.is_fanstop_ocpm && !IsLEZero(exp_config.fanstop_ocpm_pacing_type)) {
    util_vals.pacing_type = static_cast<PacingType>(exp_config.fanstop_ocpm_pacing_type);
  }

  if (IsGTZero(exp_config.ab_exp_ratio) && exp_config.ab_exp_ratio <= 1.0) {
    if (msg.group_tag == "merchant_acc_explore") {
      util_vals.ab_exp_ratio = 1.0;
    } else if (msg.group_tag == "adlogfull_ocpm") {
      util_vals.ab_exp_ratio = 1 - exp_config.ab_exp_ratio;
    } else {
      util_vals.ab_exp_ratio = exp_config.ab_exp_ratio;
    }
  }

  // PC ROAS start_bid_rate 配置
  if (util_vals.is_roas && !IsLEZero(exp_config.start_bid_rate_exp)) {
    util_vals.start_bid_rate = exp_config.start_bid_rate_exp;
  }
  // PC 订单 start_bid_rate 配置
  if (!util_vals.is_roas && !IsLEZero(exp_config.start_bid_rate_order)) {
    util_vals.start_bid_rate = exp_config.start_bid_rate_order;
  }
  // PC 直播高质量涨粉
  if (util_vals.is_high_quality_follow && !IsLEZero(exp_config.start_bid_rate_follow)) {
    util_vals.start_bid_rate = exp_config.start_bid_rate_follow;
  }

  // 移动端 start_bid_rate 配置
  if (util_vals.is_fanstop) {
    // [fanstopOcpmConfig]
    if (util_vals.is_roas) {
      if (!IsLEZero(exp_config.fanstop_ocpm_roas_start_bid_rate)) {
        util_vals.start_bid_rate = exp_config.fanstop_ocpm_roas_start_bid_rate;
      }
    } else {
      if (!IsLEZero(exp_config.fanstop_ocpm_cpa_start_bid_rate)) {
        util_vals.start_bid_rate = exp_config.fanstop_ocpm_cpa_start_bid_rate;
      }
    }
  }
  if ((util_vals.is_unit_ocpm_eop_pc || util_vals.is_unit_ocpm_roas_pc ||
     (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI" && !util_vals.is_cbo)) || msg.is_hosting_live) {
    uint64_t now_s = TimeUtil::Instance().GetTimestamp() / 1000000;
    int64_t days_since_epoch = (TimeUtil::Instance().GetTimestamp() / 1000000 + 28800) / 86400;
    int64_t midnight_time_stamp = days_since_epoch * 86400 - 28800;
    int64_t index = (now_s - midnight_time_stamp) / 10800;
    double stable_bid_ratio = 0.0;
    std::string key = "";
    const auto espLiveHostingStableBidTail = MerchantKconfUtil::espLiveHostingStableBidTail();
    bool esp_live_hosting_stable_bid = espLiveHostingStableBidTail &&
         espLiveHostingStableBidTail->IsOnFor(msg.campaign_id);
    const auto espLiveDefaultStableBidTail = MerchantKconfUtil::espLiveDefaultStableBidTail();
    bool esp_live_default_bid = espLiveDefaultStableBidTail &&
         espLiveDefaultStableBidTail->IsOnFor(msg.unit_id);
    if (msg.is_hosting_live) {
      key = absl::StrCat(msg.campaign_id, "_", index);
      auto hosting_bid_map = MerchantKconfUtil::EspLiveHostingStableBid();
      if (esp_live_hosting_stable_bid && hosting_bid_map != nullptr) {
        auto iter = hosting_bid_map->find(key);
        if (iter != hosting_bid_map->end()) {
          stable_bid_ratio = iter->second;
          util_vals.stable_bid_flag = 1;
        } else {
          std::string ocpc_action_type = msg.ocpc_action_type;
          if (msg.is_roas_hosting_live) {
            // ocpc_action_type == "AD_MERCHANT_ROAS";
            if (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI") {
              ocpc_action_type = "AD_MERCHANT_T7_ROI";
            }
          }
          key = absl::StrCat(msg.author_id, "_", ocpc_action_type, "_", index);
          auto author_hosting_bid_map = MerchantKconfUtil::authorEspLiveHostingStableBid();
          if (esp_live_hosting_stable_bid && author_hosting_bid_map != nullptr) {
            auto iter = author_hosting_bid_map->find(key);
            if (iter != author_hosting_bid_map->end()) {
              stable_bid_ratio = iter->second;
              util_vals.stable_bid_flag = 2;
            }
          }
        }
      }
    } else if (!util_vals.is_cbo) {
      key = absl::StrCat(msg.unit_id, "_", msg.item_type, "_", index);
      auto default_bid_map = MerchantKconfUtil::EspLiveDefaultStableBid();
      if (esp_live_default_bid && default_bid_map != nullptr) {
        auto iter = default_bid_map->find(key);
        if (iter != default_bid_map->end()) {
          stable_bid_ratio = iter->second;
          util_vals.stable_bid_flag = 1;
        } else {
          key = absl::StrCat(msg.author_id, "_", msg.ocpc_action_type, "_", msg.item_type, "_", index);
          auto author_default_bid_map = MerchantKconfUtil::authorEspLiveDefaultStableBid();
          if (esp_live_default_bid && author_default_bid_map != nullptr) {
            auto iter = author_default_bid_map->find(key);
            if (iter != author_default_bid_map->end()) {
              stable_bid_ratio = iter->second;
              util_vals.stable_bid_flag = 2;
            }
          }
        }
      }
    }
    double eop_stable_bid_bound = MerchantKconfUtil::eop_stable_bid_bound();
    double roas_stable_bid_bound = MerchantKconfUtil::roas_stable_bid_bound();
    if (stable_bid_ratio > 0) {
      if (((util_vals.is_unit_ocpm_eop_pc && esp_live_default_bid) ||
          (msg.is_order_hosting_live && esp_live_hosting_stable_bid)) &&
          stable_bid_ratio > 1) {
        stable_bid_ratio = std::min(eop_stable_bid_bound, stable_bid_ratio);
        util_vals.start_bid_rate = stable_bid_ratio;
      }
      if ((((util_vals.is_unit_ocpm_roas_pc || (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI"
        && !util_vals.is_cbo)) && esp_live_default_bid) ||
        (msg.is_roas_hosting_live && esp_live_hosting_stable_bid)) && stable_bid_ratio < 1) {
        stable_bid_ratio = std::max(roas_stable_bid_bound, stable_bid_ratio);
        util_vals.start_bid_rate = stable_bid_ratio;
      }
    }
  }
  // 全站推广全互斥 / 加速探索 start_bid_rate 配置
  if (msg.is_storewide_with_order && (msg.is_storewide_incompatible ||
      MerchantKconfUtil::historyRoiTail()->count(msg.author_id % 100))||
      IsStorewideAccFixV3Exp(bid_info_ptr)) {
    double store_wide_start_bid_rate = MerchantKconfUtil::cboStoreWideDefaultStartBid();
    // 读历史投放商业化 PC 版直播推广的 ROI
    double history_ad_roi = 0.0;
    if (bid_info_ptr && bid_info_ptr->history_roi0 &&
        MerchantKconfUtil::historyRoiTail()->count(msg.author_id % 100)) {
      history_ad_roi = bid_info_ptr->history_roi0;
      ks::infra::PerfUtil::IntervalLogStash(history_ad_roi * 1000, "ad.ad_bid_server",
                                            "history_roi0",
                                            std::to_string(msg.author_id));
    } else {
      ConvRatioAuthorPostData::GetInstance()->GetHistoryAdRoi(msg.author_id, &history_ad_roi);
      history_ad_roi = std::max(history_ad_roi, msg.roi_ratio * 0.7);
    }
    if (history_ad_roi > 0 && msg.roi_ratio > 0) {
      if (history_ad_roi / msg.roi_ratio < 1) {
        store_wide_start_bid_rate = history_ad_roi / msg.roi_ratio;
      }
      ks::infra::PerfUtil::IntervalLogStash(history_ad_roi * 1000, "ad.ad_bid_server",
                                            "cob_store_wide_history_ad_roi",
                                            std::to_string(msg.author_id));
    }
    // 从白名单读取
    auto store_wide_init_bid_white_list = AdBaseKconfUtil::storeWideInitBidWhiteList();
    auto iter = store_wide_init_bid_white_list->find(msg.author_id);
    if (iter != store_wide_init_bid_white_list->end()) {
      store_wide_start_bid_rate = iter->second;
    }
    // 兜底截断
    if (MerchantKconfUtil::historyRoiTail()->count(msg.author_id % 100)) {
      store_wide_start_bid_rate = std::max(store_wide_start_bid_rate, 0.15);
    } else {
      store_wide_start_bid_rate = std::max(store_wide_start_bid_rate, 0.5);
    }
    store_wide_start_bid_rate = std::min(store_wide_start_bid_rate, 1.0);
    VLOG_EVERY_N(1, 4) << "cob_store_wide_set_start_bid:"
              << ", campaign_id: " << msg.campaign_id
              << ", author_id: " << msg.author_id
              << ", roi_ratio: " << msg.roi_ratio
              << ", promotion_type: " << msg.promotion_type << ", "
              << ", history_ad_roi: " << history_ad_roi
              << ", store_wide_start_bid_rate: " << store_wide_start_bid_rate;
    if (bid_info_ptr && (bid_info_ptr->explore_put_type == 2 || is_acc_explore_campaign)
        && IsInAccExploreStatus(bid_info_ptr, base::GetTimestamp() / 1000)) {
      store_wide_start_bid_rate = 1.0;
      LOG_EVERY_N(INFO, 10000) << "debug storewide start_bid_rate2."
                              << " unit id: " << msg.unit_id
                              << ", campaign id: " << msg.campaign_id;
    }
    util_vals.start_bid_rate = store_wide_start_bid_rate;
    if (util_vals.start_bid_rate < 1.0) {
      ks::infra::PerfUtil::IntervalLogStash(util_vals.start_bid_rate * 1000, "ad.ad_bid_server",
                                         "cob_store_wide_set_start_bid",
                                         std::to_string(msg.author_id));
    }
  }
  // 全站多素材追投跳过高初始出价逻辑
  auto multi_creative_bid_update = MerchantKconfUtil::espLiveMultiCreativeBidUpdateTail();
  if (bid_info_ptr && bid_info_ptr->rel_type == 1 &&
      bid_info_ptr->rel_campaign_id > 0 && multi_creative_bid_update &&
      multi_creative_bid_update->IsOnFor(msg.campaign_id)) {
    util_vals.start_bid_rate = 1.0;
  }

  return util_vals;
}

inline bool MerchantOcpmStrategy::ApplyOperationConfig(const MerchantOcpmMsg& msg,
    const BidStateInfoPtr& bid_info_ptr, OcpmBidContext* p_bid_context,
    ProcessUtilValues* p_util_vals) {
  if (p_util_vals->is_roas) {
    const auto& iter = MerchantBidConfMgr::Instance().OperactionUidToRoi().find(p_util_vals->unit_id);
    if (iter != MerchantBidConfMgr::Instance().OperactionUidToRoi().end()) {
      SetAutoRoiRatio(p_bid_context, iter->second, p_util_vals);
      return true;
    }
  } else {
    const auto& iter = MerchantBidConfMgr::Instance().OperactionUidToCpaBid().find(p_util_vals->unit_id);
    if (iter != MerchantBidConfMgr::Instance().OperactionUidToCpaBid().end()) {
      SetAutoCpaBid(p_bid_context, bid_info_ptr, iter->second, p_util_vals);
      return true;
    }
  }
  return false;
}

void MerchantOcpmStrategy::SyncResultToDsp(
  BidStateInfoPtr bid_info_ptr,
  OcpmBidContext* p_bid_context, const std::string& group_tag,
  ProcessUtilValues* p_util_vals) {
  kuaishou::ad::BidServerMerchantTraceLog trace_log;
  trace_log.set_msg_type(+RawMsgType::MerchantOcpmMsg);
  trace_log.set_ad_status_tag(p_util_vals->ad_status_tag);
  trace_log.set_is_update_adjust(p_util_vals->is_update_adjust);
  trace_log.set_is_ad_open(p_util_vals->is_ad_open);
  trace_log.set_is_apply_adjust(p_util_vals->is_apply_adjust);
  trace_log.set_is_update_interval_ms(p_util_vals->is_update_interval_ms);
  trace_log.set_is_cost_target_msg(p_util_vals->is_cost_target_msg);
  trace_log.set_is_flow_control(p_util_vals->is_flow_control);
  trace_log.set_is_cold_start(p_util_vals->is_cold_start);
  trace_log.set_pid_cost(p_util_vals->pid_cost);
  trace_log.set_auto_param_exp_id(p_util_vals->auto_param_exp_id);
  trace_log.set_pid_target_cost(p_util_vals->pid_target_cost);
  trace_log.set_hard_upper_bound_auto_cpa_bid(p_util_vals->hard_upper_bound_auto_cpa_bid);
  trace_log.set_hard_lower_bound_auto_cpa_bid(p_util_vals->hard_lower_bound_auto_cpa_bid);
  trace_log.set_hard_bid_upper_bound_rate_monitor(p_util_vals->hard_bid_upper_bound_rate_monitor);
  trace_log.set_hard_bid_lower_bound_rate_monitor(p_util_vals->hard_bid_lower_bound_rate_monitor);
  trace_log.set_update_interval_ms(p_util_vals->update_interval_ms);
  trace_log.set_ocpx_action_type(p_bid_context->ocpc_action_type());
  trace_log.set_adjust_auto_value_rate(p_bid_context->adjust_auto_value_rate());
  trace_log.set_relax_cpa_bid(p_bid_context->relax_cpa_bid());
  trace_log.set_p_value(p_bid_context->p_value());
  trace_log.set_i_value(p_bid_context->i_value());
  trace_log.set_d_value(p_bid_context->d_value());
  trace_log.set_pacing_weight(p_bid_context->pacing_weight());
  trace_log.set_last_delivery_timestamp_ms(p_bid_context->last_delivery_timestamp_ms());
  trace_log.set_first_delivery_timestamp_ms(p_bid_context->first_delivery_timestamp_ms());
  trace_log.set_last_update_adjust_timestamp(p_bid_context->last_update_adjust_timestamp());
  trace_log.set_conv_num(p_bid_context->conv_num());
  trace_log.set_ad_off_target_cost(p_bid_context->ad_off_target_cost());
  trace_log.set_yesterday_cost(p_bid_context->yesterday_cost());
  trace_log.set_yesterday_target_cost(p_bid_context->yesterday_target_cost());
  trace_log.set_ad_open_count(p_bid_context->ad_open_count());
  trace_log.set_author_id(p_bid_context->author_id());
  trace_log.set_roi7_convert_ratio(p_util_vals->roi7_convert_ratio);
  trace_log.set_item_type(p_bid_context->item_type());
  trace_log.set_ad_cost(p_bid_context->ad_cost());
  trace_log.set_ad_target_cost(p_bid_context->ad_target_cost());
  trace_log.set_rl_adjust_rate(p_util_vals->rl_adjust_rate);
  trace_log.set_inner_pre_convert(p_bid_context->inner_pre_convert());
  trace_log.set_rl_model_pred_all(p_bid_context->inner_rl_data().inner_rl_model_pred());
  trace_log.set_last_update_cost(p_bid_context->last_update_cost());
  trace_log.set_last_update_target_cost(p_bid_context->last_update_target_cost());
  trace_log.set_is_roas_hosting_live(p_util_vals->is_roas_hosting_live);
  trace_log.set_is_order_hosting_live(p_util_vals->is_order_hosting_live);
  trace_log.set_hosting_dt_actions(p_bid_context->hosting_dt_actions());
  trace_log.set_b2x_adjust_value(p_bid_context->b2x_adjust_value());
  trace_log.set_b2x_log(p_util_vals->b2x_log);
  trace_log.set_hosting_dt_bad_count(p_bid_context->hosting_dt_bad_count());
  trace_log.set_stable_bid_flag(p_util_vals->stable_bid_flag);
  trace_log.set_mpc_pred_cost(p_bid_context->mutable_inner_mpc_data()->mpc_pred_cost());
  trace_log.set_mpc_pred_target_cost(p_bid_context->mutable_inner_mpc_data()->mpc_pred_target_cost());
  trace_log.set_mpc_opt_cost(p_bid_context->opt_cost());
  trace_log.set_mpc_opt_target_cost(p_bid_context->opt_target_cost());
  trace_log.set_avg_day_cost(p_bid_context->avg_day_cost());
  trace_log.set_p_target_cost(p_bid_context->p_target_cost());
  trace_log.set_avg_cpm(p_bid_context->avg_cpm());
  trace_log.set_last_excess_ratio(p_bid_context->last_excess_ratio());
  trace_log.set_deliver_num(p_bid_context->delivery_cnt());
  trace_log.set_live_reco_gmv(p_bid_context->reco_gmv());
  trace_log.set_gimbal_price_before(p_bid_context->gimbal_price_before());
  trace_log.set_target_atv(p_bid_context->target_atv());
  trace_log.set_pred_target_cost(p_bid_context->pred_target_cost());
  trace_log.set_last_pred_target_cost(p_bid_context->last_pred_target_cost());
  trace_log.set_delivery_reco_cnt(p_bid_context->delivery_reco_cnt());
  trace_log.set_b2x_cost(p_bid_context->b2x_cost());
  trace_log.set_b2x_reco_cost(p_bid_context->b2x_reco_cost());
  trace_log.set_b2x_ecpm(p_bid_context->b2x_ecpm());
  trace_log.set_uplift_unify_ltv(p_bid_context->uplift_unify_ltv());
  trace_log.set_unify_ltv(p_bid_context->unify_ltv());
  trace_log.set_b2x_reco_ecpm(p_bid_context->b2x_reco_ecpm());
  trace_log.set_last_update_gimbal_price_before(p_bid_context->last_update_gimbal_price_before());
  trace_log.set_reco_gpm(p_bid_context->reco_gpm());
  trace_log.set_last_reco_gpm(p_bid_context->last_reco_gpm());
  trace_log.set_last_p_target_cost(p_bid_context->last_p_target_cost());
  trace_log.set_scene_oriented_type(p_bid_context->scene_oriented_type());
  trace_log.set_single_bid_ratio(p_bid_context->single_bid_ratio());
  if (SPDM_enableAddPageCostInfo()) {
    std::string page_info_map_str;
    JsonPrintOptions options;
    options.add_whitespace = false;
    options.always_print_primitive_fields = true;
    options.preserve_proto_field_names = true;
    const auto& page_raw = p_bid_context->page_cost_info();
    LivePageInfo page_raw_info;
    *page_raw_info.mutable_page_cost_info() = page_raw;
    MessageToJsonString(page_raw_info, &page_info_map_str, options);
    trace_log.set_page_info_map_str(page_info_map_str);
  }
  if (bid_info_ptr) {
    int32_t gmv_level = 0;
    double last_30d_cost = 0;
    int32_t is_vh = 0;
    // 从内存读取 or 从正排获取
    gmv_level = bid_info_ptr->gmv_level;
    last_30d_cost = bid_info_ptr->last_30d_cost;
    is_vh = bid_info_ptr->is_vh;
    // 打开 useWTRemoteTables, 关闭 redis 读取
    if (!SPDM_useWTRemoteTables()) {
      // 从 redis 读取
      bool is_get_redis_success = false;
      if (AdKconfUtil::enableWTAuthorCache() &&
          AdKconfUtil::enableWTAuthorCacheUnitTail()->IsOnFor(bid_info_ptr->unit_id)) {
        is_get_redis_success =
        BidStateInfoAdTableHelper::Instance()->GetWTAuthor(bid_info_ptr, bid_info_ptr->wt_author_key_id);
        // 读取不到为默认值
        auto gmv_level_cache = bid_info_ptr->wt_author_info_ptr->gmv_level;
        auto last_30d_cost_cache = bid_info_ptr->wt_author_info_ptr->last_30d_cost;
        auto is_vh_cache = bid_info_ptr->wt_author_info_ptr->is_vh;
        bool is_same = (gmv_level == gmv_level_cache) &&
                      (std::fabs(last_30d_cost - last_30d_cost_cache) < 1e6) &&
                      (is_vh == is_vh_cache);
        ks::infra::PerfUtil::CountLogStash(1, "ad.ad_bid_orbit_server",
                                              "wt_author_redis_diff", "MerchantOcpmStrategyTraceLog",
                                              is_same? "true" : "false");
        if (is_get_redis_success) {
          gmv_level = bid_info_ptr->wt_author_info_ptr->gmv_level;
          last_30d_cost = bid_info_ptr->wt_author_info_ptr->last_30d_cost;
          is_vh = bid_info_ptr->wt_author_info_ptr->is_vh;
        }
      }
    }
    trace_log.set_gmv_level(gmv_level);
    trace_log.set_last_30d_cost(last_30d_cost);
    trace_log.set_is_vh(is_vh);
    trace_log.set_is_pure_new_customer(bid_info_ptr->is_pure_new_customer);
    trace_log.set_bid_info_roi_ratio(bid_info_ptr->roi_ratio);
    trace_log.set_explore_budget_status(bid_info_ptr->explore_budget_status);
    trace_log.set_explore_bid_type(bid_info_ptr->explore_bid_type);
    trace_log.set_explore_put_type(bid_info_ptr->explore_put_type);
    trace_log.set_explore_budget(bid_info_ptr->explore_budget);
    trace_log.set_explore_budget_v2(bid_info_ptr->explore_budget);
  }
  trace_log.set_bid_context_key(p_bid_context->bid_context_key());
  if (p_bid_context->scene_oriented_type() == 21 || p_bid_context->scene_oriented_type() == 30) {
    LOG_EVERY_N(INFO, 10000) << "debug fill unit id."
              << ", unit id: " << p_bid_context->unit_id()
              << ",campaign id: " << p_bid_context->campaign_id();
    trace_log.set_unit_id(p_bid_context->unit_id());  // 全站直播填充真的 unit id
  }
  trace_log.set_bid_data_level(
      static_cast<uint32_t>(MerchantDataMgr::Instance().GetBidDataLevel(p_bid_context)));
  p_bid_context->set_last_sync_result_timestamp(TimeUtil::Instance().GetTimestamp());
  MerchantDataMgr::Instance().SendMerchantOcpmResultToRemote(
    p_bid_context, group_tag, trace_log, p_util_vals->dragon_type, bid_info_ptr);
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_bid_orbit_server", group_tag);
  LOG_EVERY_N(INFO, 1000000) << "MerchantOcpmStrategy::SyncResultToDsp.ShortDebugString: "
                           << p_bid_context->ShortDebugString();
}
void MerchantOcpmStrategy::SendCostCapTraceLog(OcpmBidContext* p_bid_context,
                                               const std::string& group_tag,
                                               BidStateInfoPtr bid_info_ptr) {
  const NativeLowestCostContext& lowest_cost_context = p_bid_context->native_lowest_cost_context();
  kuaishou::ad::BidServerMerchantTraceLog trace_log;
  // TODO(tangweiqi): 对 cbo costcap 需要兼容
  trace_log.set_unit_id(lowest_cost_context.unit_id());  // campaign 调价存储的是 campaign_id
  trace_log.set_log_timestamp_ms(TimeUtil::Instance().GetTimestamp());
  trace_log.set_group_tag(group_tag);
  trace_log.set_cost(lowest_cost_context.day_cost());
  trace_log.set_target_cost(lowest_cost_context.expected_cost());
  trace_log.set_business(FLAGS_business);
  // 暂不填写
  // trace_log.set_auto_cpa_bid(bid_param_info.current_cpa());
  // trace_log.set_auto_roi_ratio(bid_param_info.auto_roi());
  // trace_log.set_cpa_bid(bid_param_info.target_cpa());
  // trace_log.set_roi_ratio(bid_param_info.roi_ratio());
  FillCommonTraceLog(lowest_cost_context, &trace_log, bid_info_ptr);
  MerchantTraceLogKafkaUtil::Instance().Send(trace_log);
  PerfUtil::Instance().Count(1, "send_trace_log");
  ks::infra::PerfUtil::CountLogStash(1, "ad.ad_bid_orbit_server", "send_trace_log", "costcap_live");
}
void MerchantOcpmStrategy::ResetAdjustValue(StrategySession* p_session) {
  const MerchantOcpmExpConfig& exp_config = *(p_session->p_exp_config);
  OcpmBidContext* p_bid_context = p_session->p_bid_context;
  ProcessUtilValues* p_util_vals = p_session->p_util_vals;
  const MerchantOcpmBidConfig& strategy_config = *(p_session->p_strategy_config);
  const MerchantOcpmMsg& msg = *(p_session->p_msg);
  BidStateInfoPtr bid_info_ptr = p_session->bid_info_ptr;
  if (p_util_vals->is_fanstop)  {
    return;
  }
  bool is_reset = false;
  // 关停 & 重投的重置为客户出价
  if (p_bid_context->last_is_ad_open() != p_util_vals->is_ad_open) {
    is_reset = true;
    // 如果是今天刚开始投(消耗 < 1 元)，不重置; ps:使用 start_bid 时都要注意这里
    if (msg.is_storewide_with_order
        && msg.is_storewide_incompatible
        && (p_bid_context->cost() < msg.cost + 1000.0)) {
      is_reset = false;
    }
    if (((p_util_vals->is_unit_ocpm_eop_pc || p_util_vals->is_unit_ocpm_roas_pc ||
       (msg.ocpc_action_type == "AD_MERCHANT_T7_ROI" && !p_util_vals->is_cbo)) || msg.is_hosting_live)
       && (p_bid_context->cost() < msg.cost + 1000.0)) {
      const auto espLiveHostingStableBidTail = MerchantKconfUtil::espLiveHostingStableBidTail();
      bool esp_live_hosting_stable_bid = espLiveHostingStableBidTail &&
           espLiveHostingStableBidTail->IsOnFor(msg.campaign_id);
      const auto espLiveDefaultStableBidTail = MerchantKconfUtil::espLiveDefaultStableBidTail();
      bool esp_live_default_bid = espLiveDefaultStableBidTail &&
           espLiveDefaultStableBidTail->IsOnFor(msg.unit_id);
      if (msg.is_hosting_live && esp_live_hosting_stable_bid) {
        is_reset = false;
      }
      if (!p_util_vals->is_cbo && esp_live_default_bid) {
        is_reset = false;
      }
    }
  }
  auto reset_value = exp_config.enable_start_rate_reset > 0 ? p_util_vals->start_bid_rate : 1.0;
  if (is_reset) {
    p_bid_context->set_adjust_auto_value_rate(reset_value);
    p_bid_context->set_adjust_auto_atv_rate(reset_value);
    p_bid_context->set_old_adjust_auto_atv_rate(reset_value);
  }
  if (FLAGS_enable_bid_no_diff_switch || p_util_vals->is_debug) {
    LOG(INFO) << "no diff debug is last ad open, " << p_bid_context->last_is_ad_open()
      << " util_vals_is_ad_open, " << p_util_vals->is_ad_open
      << " util_vals_is_reset, " << is_reset
      << " msg seq, " << p_session->p_msg->event_server_timestamp
      << " unit id, " << p_session->p_msg->unit_id;
  }
  p_bid_context->set_last_is_ad_open(p_util_vals->is_ad_open);
}
// 移动端直播 OCPC
bool MerchantOcpmStrategy::IsFanstop(const std::string& campaign_type) {
  if (campaign_type ==
          kuaishou::ad::AdEnum::CampaignType_Name(kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_FANS)
      || campaign_type ==
          kuaishou::ad::AdEnum::CampaignType_Name(kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_SHOW)
      || campaign_type ==
          kuaishou::ad::AdEnum::CampaignType_Name(kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_ALL)
      || campaign_type ==
          kuaishou::ad::AdEnum::CampaignType_Name(kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS)
      || campaign_type ==
          kuaishou::ad::AdEnum::CampaignType_Name(kuaishou::ad::AdEnum::AD_FANSTOP_TO_SHOW)
      || campaign_type ==
          kuaishou::ad::AdEnum::CampaignType_Name(kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL)) {
    return true;
  }
  return false;
}
bool MerchantOcpmStrategy::IsFanstopOcpm(const std::string& campaign_type,
                                         const std::string& bid_type) {
  if (IsFanstop(campaign_type) && bid_type == "OCPM_DSP") {
    return true;
  }
  return false;
}
void MerchantOcpmStrategy::UpdateRlPrediction(const RawMsgPtr& raw_msg) {
  // 无 diff 环境跳过
  if (FLAGS_enable_bid_no_diff_switch) {
    return;
  }
  MerchantOcpmRlUpdateMsg& msg = raw_msg->value->get_value<MerchantOcpmRlUpdateMsg>();
  uint64_t rl_push_ts = msg.request_ts_us / 1e6;
  uint64_t now_sec = TimeUtil::Instance().GetTimestamp() / 1e6;
  // 模型返回耗时过长
  if (now_sec - rl_push_ts > MerchantKconfUtil::hostingLiveRLTimeCostThresh()) {
    LOG(INFO) << "rl taking too long";
    return;
  }
  // rl 返回结果
  uint64_t unit_id = msg.unit_id;
  uint64_t campaign_id = msg.campaign_id;
  std::string group_tag = msg.group_tag;
  float pred = msg.model_pred;
  LOG_EVERY_N(INFO, 1000000) << "Trigger Update Rl StepTwo: ";
  // 获取 exp_config
  auto exp_config = MerchantBidConfMgr::Instance().GetOcpmExpConfig(unit_id, group_tag);
  // 获取调价上下文, 粒度: campaign
  auto bid_context_key = GetCampaignBidContextKey(campaign_id, group_tag);
  // auto bid_context_key = GetUnitBidContextKey(unit_id, group_tag);
  OcpmBidContext* p_bid_context = MerchantDataMgr::Instance().GetOcpmBidContext(bid_context_key);
  if (!p_bid_context) {
    LOG(INFO) << "p_bid_context null";
    return;
  }
  // 返回的消息比当前环境消息更旧
  if (rl_push_ts < p_bid_context->inner_rl_data().inner_rl_send_ts()) {
    LOG(INFO) << "already have newest rl message";
    return;
  }
  // 需要将 rl 结果更新到 p_bid_context->mutable_inner_rl_data()
  p_bid_context->mutable_inner_rl_data()->set_inner_rl_send_ts(rl_push_ts);
  p_bid_context->mutable_inner_rl_data()->set_inner_rl_recv_ts(now_sec);
  p_bid_context->mutable_inner_rl_data()->set_inner_rl_model_pred(msg.model_pred);
  p_bid_context->mutable_inner_rl_data()->set_inner_rl_cost_ts(now_sec - rl_push_ts);
  LOG_EVERY_N(INFO, 10000) << "xusimin_debug_rl_recv unit_id=" << unit_id
            << " campaign_id=" << campaign_id
            << " author_id=" << p_bid_context->author_id()
            << " scene_oriented_type=" << p_bid_context->scene_oriented_type()
            << " group_tag=" << group_tag
            << " rl_send_ts=" << rl_push_ts
            << " rl_recv_ts=" << now_sec
            << " rl_cost_ts=" << now_sec - rl_push_ts
            << " rl pred=" << pred;
}
void MerchantOcpmStrategy::UpdateBackflowPrediction(const RawMsgPtr& raw_msg) {
  // 无 diff 环境跳过
  if (FLAGS_enable_bid_no_diff_switch) {
    return;
  }
  if (!raw_msg|| !raw_msg->value) {
    return;
  }
  MerchantOcpmBackflowMsg& msg = raw_msg->value->get_value<MerchantOcpmBackflowMsg>();
  uint64_t convert_push_ts = msg.request_ts_us / 1e6;
  uint64_t now_sec = TimeUtil::Instance().GetTimestamp() / 1e6;
  // 模型返回耗时过长
  if (now_sec - convert_push_ts > MerchantKconfUtil::innerPreConvertTimeCostThresh()) {
    LOG(INFO) << "inner_pre_convert taking too long";
    return;
  }
  uint64_t unit_id = msg.unit_id;
  uint64_t campaign_id = msg.campaign_id;
  std::string group_tag = msg.group_tag;
  std::string item_type = msg.item_type;
  bool enable_p2l_single = msg.enable_p2l_single;
  float pred = msg.model_pred;
  std::string bid_context_key;
  if (enable_p2l_single && IsP2lItemType(item_type)) {
    bid_context_key = GetP2LUnitBidContextKey(unit_id, group_tag);
  } else {
    bid_context_key = GetUnitBidContextKey(unit_id, group_tag);
  }
  OcpmBidContext* p_bid_context = MerchantDataMgr::Instance().GetOcpmBidContext(bid_context_key);
  if (!p_bid_context) {
    return;
  }
  p_bid_context->set_model_pre_convert(pred * 1000);
  LOG_EVERY_N(INFO, 10000) << "fuzhenqiang_debug_inner_pre_convert unit_id=" << unit_id
                      << " campaign_id=" << campaign_id
                      << " item_type=" << item_type
                      << " group_tag=" << group_tag
                      <<" target_cost=" << p_bid_context->target_cost()
                      << " cost= " << p_bid_context->cost()
                      << " pred=" << pred * 1000;
}
void MerchantOcpmStrategy::UpdateMpcPrediction(const RawMsgPtr& raw_msg) {
  // 无 diff 环境跳过
  if (FLAGS_enable_bid_no_diff_switch) {
    return;
  }
  if (!raw_msg|| !raw_msg->value) {
    return;
  }
  MerchantOcpmMpcMsg& msg = raw_msg->value->get_value<MerchantOcpmMpcMsg>();
  uint64_t mpc_push_ts = msg.request_ts_us / 1e6;
  uint64_t now_sec = TimeUtil::Instance().GetTimestamp() / 1e6;
  uint64_t unit_id = msg.unit_id;
  uint64_t campaign_id = msg.campaign_id;
  std::string group_tag = msg.group_tag;
  float cost = msg.cost_future;
  float target_cost = msg.target_cost_future;
  // 获取调价上下文, 粒度: campaign
  auto bid_context_key = GetCampaignBidContextKey(campaign_id, group_tag);
  OcpmBidContext* p_bid_context = MerchantDataMgr::Instance().GetOcpmBidContext(bid_context_key);
  if (!p_bid_context) {
    LOG(INFO) << "p_bid_context null";
    return;
  }
  if (mpc_push_ts < p_bid_context->inner_mpc_data().mpc_send_ts()) {
    LOG(INFO) << "already have newest mpc message";
    return;
  }
  p_bid_context->mutable_inner_mpc_data()->set_mpc_send_ts(mpc_push_ts);
  p_bid_context->mutable_inner_mpc_data()->set_mpc_recv_ts(now_sec);
  p_bid_context->mutable_inner_mpc_data()->set_mpc_pred_cost(msg.cost_future);
  p_bid_context->mutable_inner_mpc_data()->set_mpc_pred_target_cost(msg.target_cost_future);
  p_bid_context->mutable_inner_mpc_data()->set_mpc_cost_ts(now_sec - mpc_push_ts);
  LOG_EVERY_N(INFO, 10000) << "fuzhenqiang_debug_mpc_recv unit_id=" << unit_id
                      << " campaign_id=" << campaign_id
                      << " author_id=" << p_bid_context->author_id()
                      << " group_tag=" << group_tag
                      << " mpc_send_ts=" << mpc_push_ts
                      << " mpc_recv_ts=" << now_sec
                      << " mpc_cost_ts=" << now_sec - mpc_push_ts
                      << " mpc cost=" << cost
                      << " mpc target_cost=" << target_cost;
}
REGISTER_CLASS(MerchantStrategyBase, MerchantOcpmStrategy);
}  // namespace bid_server
}  // namespace ks
