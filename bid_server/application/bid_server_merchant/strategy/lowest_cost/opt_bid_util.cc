#include "teams/ad/bid_server/application/bid_server_merchant/strategy/lowest_cost/opt_bid_util.h"

#include <memory>
#include <algorithm>
#include <set>
#include <string>
#include <vector>
#include <utility>
#include "base/strings/string_printf.h"
#include "absl/strings/str_format.h"
#include "base/strings/string_number_conversions.h"
#include "falcon/counter.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_base/src/common/latency_record.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/bid_server/bid_server_main/kconf/kconf.h"
#include "teams/ad/bid_server/application/bid_server_merchant/kconf/kconf.h"
#include "teams/ad/bid_server/bid_server_main/strategy/context.h"
#include "teams/ad/bid_server/bid_server_main/strategy/controller_unit.h"
#include "teams/ad/bid_server/public/base/common.h"
#include "teams/ad/bid_server/framework/store/global_store.h"
#include "teams/ad/bid_server/framework/utils/perf_util.h"
#include "teams/ad/bid_server/application/bid_server_merchant/data_mgr/merchant_data_mgr.h"
#include "teams/ad/bid_server/application/bid_server_merchant/util/util.h"
#include "teams/ad/bid_server/application/bid_server_merchant/strategy/lowest_cost/common_util.h"
#include "teams/ad/bid_server/base/strategy/lowest_cost/cost_cap_util.h"
#include "teams/ad/bid_server/framework/utils/spdm_switches.h"
#include "teams/ad/bid_server/base/util/index_util.h"
#include "teams/ad/bid_server/framework/utils/perf_log.h"
#include "teams/ad/bid_server/framework/utils/perf_trace_log_manager.h"

namespace ks {
namespace bid_server {

using kuaishou::ad::AdEnum;
using ks::ad_base::AutoBidGroupTags;
using ks::ad_base::GetBidGroupByEnum;
const double MAX_VALUE = 2147483647;

bool IsUpdateOptBid(NativeLowestCostContext* lowest_cost_context,
                    const NoBidConfig& strategy_config, const NativeLowestCostMsg& msg,
                    const BidStateInfoPtr& bid_info_ptr) {
  uint64_t last_update_time_ms = lowest_cost_context->last_update_timestamp() / 1000;
  uint64_t now_ms = ControllerUnit::GetTimestampMS();
  uint64_t update_time_interval = strategy_config.adjust_bid_freq();
  // 热点单元降低更新频率
  if (msg.is_hot_unit && MerchantKconfUtil::nobidHotUpdateTimeInterval() > 0) {
    update_time_interval = MerchantKconfUtil::nobidHotUpdateTimeInterval();
  }
  if (!IsOptBidExp(strategy_config, msg, bid_info_ptr)) {
    return false;
  }
  // 固定间隔
  if (update_time_interval > 0
      && (now_ms - last_update_time_ms) >= update_time_interval * 1000) {
    return true;
  }
  // 日志条数
  int32_t update_batch_size = 1000;
  if (strategy_config.update_batch_size() > 0) {
    update_batch_size = strategy_config.update_batch_size();
  }
  if (msg.is_hot_unit && MerchantKconfUtil::nobidHotUpdateBatchSize() > 0) {
    update_batch_size = MerchantKconfUtil::nobidHotUpdateBatchSize();
  }
  if ((update_batch_size > 0)
      && (lowest_cost_context->update_count() >= update_batch_size)) {
    return true;
  }
  return false;
}

bool IsUpdateBid2X(NativeLowestCostContext* lowest_cost_context,
                    const NoBidConfig& strategy_config, const NativeLowestCostMsg& msg,
                    const BidStateInfoPtr& bid_info_ptr) {
  if (!strategy_config.enable_bid2x_exp()) {
    return false;
  }
  uint64_t last_update_time_ms = lowest_cost_context->last_update_timestamp() / 1000;
  uint64_t now_ms = ControllerUnit::GetTimestampMS();
  uint64_t update_time_interval = strategy_config.adjust_bid_freq();
  // 热点单元降低更新频率
  if (msg.is_hot_unit && MerchantKconfUtil::nobidHotUpdateTimeInterval() > 0) {
    update_time_interval = MerchantKconfUtil::nobidHotUpdateTimeInterval();
  }
  // 写过去时间窗信息
  SetBid2XWindowInfo(bid_info_ptr, lowest_cost_context, strategy_config);
  // 固定间隔
  if (update_time_interval > 0
      && (now_ms - last_update_time_ms) >= update_time_interval * 1000) {
    return true;
  }
  // 日志条数
  int32_t update_batch_size = 1000;
  if (strategy_config.update_batch_size() > 0) {
    update_batch_size = strategy_config.update_batch_size();
  }
  if (msg.is_hot_unit && MerchantKconfUtil::nobidHotUpdateBatchSize() > 0) {
    update_batch_size = MerchantKconfUtil::nobidHotUpdateBatchSize();
  }
  if ((update_batch_size > 0)
      && (lowest_cost_context->update_count() >= update_batch_size)) {
    return true;
  }
  return false;
}

bool IsUpdateMpcByPostCost(NativeLowestCostContext* lowest_cost_context,
                           const NoBidConfig& strategy_config, const NativeLowestCostMsg& msg) {
  uint64_t last_update_time_ms = lowest_cost_context->last_update_timestamp() / 1000;
  uint64_t now_ms = ControllerUnit::GetTimestampMS();
  uint64_t update_time_interval = strategy_config.adjust_bid_freq();
  // 热点单元降低更新频率
  if (msg.is_hot_unit && MerchantKconfUtil::nobidHotUpdateTimeInterval() > 0) {
    update_time_interval = MerchantKconfUtil::nobidHotUpdateTimeInterval();
  }
  // 当前针对速推 && 专推短视频 生效
  if (IsMpcByPostCostExp(strategy_config)
      && update_time_interval > 0
      && (now_ms - last_update_time_ms) >= update_time_interval * 1000) {
    LOG_IF(INFO, strategy_config.is_debug())
      << "merchant_opt_bid IsUpdateMpcByPostCost, unit_id: " << lowest_cost_context->unit_id();
    falcon::Inc("nobid_opt_bid.IsUpdateMpcByPostCost");
    return true;
  }
  return false;
}

bool IsOptBidExp(const NoBidConfig& strategy_config,
                 const NativeLowestCostMsg& msg,
                 const BidStateInfoPtr& bid_info_ptr) {
  // cbo 跳过
  if (!strategy_config.enable_cbo_opt_bid()
      && (IsCboNobid(msg.unit_id, bid_info_ptr) || IsCboCostCap(msg.unit_id, bid_info_ptr))) {
    return false;
  }
  // 移动端混投
  if (strategy_config.enable_fanstop_mix_skip_opt_bid()
      && IsFanstopCampaignBid(msg.inner_data->unit_id, msg.inner_data->campaign_id, bid_info_ptr)) {
    return false;
  }
  if (strategy_config.enable_nobid_opt_bid() && IsFlashLowestCost(msg, bid_info_ptr)) {
    return true;
  }
  if (strategy_config.enable_nobid_opt_bid_fanstop()) {
    return true;
  }
  if (strategy_config.enable_nobid_opt_bid_specialty()
      && IsSpecialtyLowestCost(msg, bid_info_ptr)) {
    return true;
  }
  return false;
}

bool IsMpcPidBidExp(const NoBidConfig& strategy_config) {
  if (strategy_config.enable_mpc_combine_pid()) {
      return true;
  }
  return false;
}

bool IsMpcByPostCostExp(const NoBidConfig& strategy_config) {
  return strategy_config.enable_mpc_by_post_cost();
}

Bid2Value GetBid2ValueMap(uint64_t unit_id, const NoBidConfig& strategy_config) {
  Bid2Value bid2value;
  auto now_time = TimeUtil::Instance().GetTimestamp() / 1000;
  base::Time::Exploded now_tm;
  base::Time::FromTimeT(now_time / 1000).LocalExplode(&now_tm);
  std::string prefix = strategy_config.opt_bid_prefix();
  uint64_t minute_index = (now_tm.hour * 60 + now_tm.minute) / 5;
  std::string key = absl::Substitute("$0_$1_$2", prefix, unit_id, minute_index);
  // 使用缓存
  if (MerchantKconfUtil::enableBid2valueCache()) {
    if (MerchantDataMgr::Instance().GetBid2ValueMap(unit_id, key, &bid2value)) {
      UpdateBid2ValueMap(&bid2value, unit_id);
    } else {
      LOG_IF(INFO, strategy_config.is_debug())
        << "merchant_opt_bid GetBid2ValueMap fail, key: " << key;
    }
  }
  return bid2value;
}

// 处理 bid_list & cost_list，原始数据未排序 & 未累加
void UpdateBid2ValueMap(Bid2Value* bid2value, uint64_t unit_id) {
  auto bid_list = bid2value->mutable_bid_list();
  auto cost_list = bid2value->mutable_cost_list();
  if (bid_list->size() == 0 || cost_list->size() == 0 || bid_list->size() != cost_list->size()) {
    return;
  }
  ks::infra::PerfUtil::SetLogStash(bid_list->size(), GetOptBidNameSpace(), "bid2value_size",
                                    std::to_string(unit_id));
  using PairOfIts = std::pair<float, float>;
  std::vector<PairOfIts> v;
  auto i = bid_list->begin();
  auto j = cost_list->begin();
  for (; i != bid_list->end(); ++i, ++j) {
    v.push_back(std::make_pair(*i, *j));
  }
  std::sort(v.begin(), v.end(), [](PairOfIts const& i, PairOfIts const& j) {
    return i.first < j.first;
  });
  std::vector<float> sorted_bid_list;
  std::vector<float> sorted_cost_list;
  std::vector<float> sum_cost_list;
  uint64_t index = 0;
  for (auto& x : v) {
    sorted_bid_list.push_back(x.first);
    float cost = x.second / 1e6;  // 单位换算到 厘
    sorted_cost_list.push_back(cost);
    if (sum_cost_list.size() == 0) {
      sum_cost_list.push_back(cost);
    } else {
      sum_cost_list.push_back(cost + sum_cost_list[index - 1]);
    }
    index++;
  }
  bid_list->CopyFrom({sorted_bid_list.begin(), sorted_bid_list.end()});
  cost_list->CopyFrom({sum_cost_list.begin(), sum_cost_list.end()});
}

bool IsBid2ValueValid(const Bid2Value& bid2value, const NoBidConfig& strategy_config) {
  if (bid2value.bid_list().size() == 0) {
    return false;
  }
  if (bid2value.bid_list().size() != bid2value.cost_list().size()) {
    return false;
  }
  // 参竞量级不满足条件过滤
  if (bid2value.length() < strategy_config.opt_bid_auction_times_limit()) {
    return false;
  }
  // 窗口时间戳非法过滤
  if (bid2value.start_time() <= 0 || bid2value.end_time() <= 0
      || bid2value.start_time() >= bid2value.end_time()) {
    return false;
  }
  return true;
}

void UpdateCumuContext(NativeLowestCostContext* lowest_cost_context, std::string tag) {
  std::string unit_id = base::Uint64ToString(lowest_cost_context->unit_id());
  auto key_id = GetKeyId(lowest_cost_context);
  if (lowest_cost_context->current_pred_cost() > 0) {
    lowest_cost_context->set_cumu_real_cost(lowest_cost_context->cumu_real_cost()
                                            + lowest_cost_context->current_real_cost());
    lowest_cost_context->set_cumu_pred_cost(lowest_cost_context->cumu_pred_cost()
                                            + lowest_cost_context->current_pred_cost());
    lowest_cost_context->set_cumu_pred_cost_after_cali(lowest_cost_context->cumu_pred_cost_after_cali()
                                            + lowest_cost_context->current_pred_cost_after_cali());
    ks::infra::PerfUtil::SetLogStash(
      lowest_cost_context->cumu_real_cost(), GetOptBidNameSpace(), "cumu_real_cost", key_id, tag);
    ks::infra::PerfUtil::SetLogStash(
      lowest_cost_context->cumu_pred_cost(), GetOptBidNameSpace(), "cumu_pred_cost", key_id, tag);
    ks::infra::PerfUtil::SetLogStash(
      lowest_cost_context->current_real_cost(), GetOptBidNameSpace(), "current_real_cost", key_id, tag);
    ks::infra::PerfUtil::SetLogStash(
      lowest_cost_context->current_pred_cost(), GetOptBidNameSpace(), "current_pred_cost", key_id, tag);
    ks::infra::PerfUtil::SetLogStash(
      lowest_cost_context->cumu_pred_cost_after_cali(), GetOptBidNameSpace(),
      "cumu_pred_cost_after_cali", key_id, tag);
    ks::infra::PerfUtil::SetLogStash(
      lowest_cost_context->current_pred_cost_after_cali(), GetOptBidNameSpace(),
      "current_pred_cost_after_cali", key_id, tag);
  }
}

bool CalcMpcOptBidOnce(NativeLowestCostContext* lowest_cost_context,
                       const NoBidConfig& strategy_config, double bid,
                       double pred_pv_ratio, double pred_cost_ratio) {
  auto mpc_ctx = lowest_cost_context->mutable_mpc_ctx();
  auto opt_ratio = mpc_ctx->opt_ratio();
  auto curr_ratio = MAX_VALUE;
  auto total_pv_ratio = lowest_cost_context->past_pv_ratio() + pred_pv_ratio;
  auto total_cost_ratio = lowest_cost_context->past_cost_ratio() + pred_cost_ratio;
  if (total_cost_ratio > 0 && total_pv_ratio > 0) {
    curr_ratio = std::fabs(total_pv_ratio / total_cost_ratio - 1.0);
  }
  if (curr_ratio < opt_ratio) {
    mpc_ctx->set_opt_ratio(curr_ratio);
    mpc_ctx->set_opt_bid(bid);
    mpc_ctx->set_last_update_timestamp_ms(TimeUtil::Instance().GetTimestamp() / 1000);
    LOG_IF(INFO, strategy_config.is_debug()) << "merchant_opt_bid CalcMpcOptBidOnce, unit_id: "
                         << lowest_cost_context->unit_id()
                         << " bid: " << bid
                         << " opt_ratio: " << curr_ratio
                         << " total_pv_ratio: " << total_pv_ratio
                         << " total_cost_ratio: " << total_cost_ratio
                         << " pred_pv_ratio: " << pred_pv_ratio
                         << " past_pv_ratio: " << lowest_cost_context->past_pv_ratio()
                         << " past_cost_ratio: " << lowest_cost_context->past_cost_ratio()
                         << " pred_cost_ratio: " << pred_cost_ratio;
    return true;
  }
  return false;
}

bool CalcPostMpcMpcOptBidOnce(NativeLowestCostContext* lowest_cost_context,
                              const NoBidConfig& strategy_config, double bid,
                              double pred_pv_ratio, double pred_cost_ratio) {
  auto mpc_ctx = lowest_cost_context->mutable_mpc_ctx();
  auto opt_ratio = mpc_ctx->opt_ratio();
  auto curr_ratio = MAX_VALUE;
  auto total_pv_ratio = lowest_cost_context->past_pv_ratio() + pred_pv_ratio;
  auto total_cost_ratio = lowest_cost_context->past_cost_ratio() + pred_cost_ratio;
  if (total_cost_ratio > 0 && total_pv_ratio > 0) {
    curr_ratio = std::fabs(total_pv_ratio / total_cost_ratio - 1.0);
  }
  if (lowest_cost_context->current_cpa() > 0) {
    double bid_ratio_diff = std::fabs(bid / lowest_cost_context->current_cpa() - 1.0);
    curr_ratio += bid_ratio_diff * strategy_config.bid_diff_ratio_coef();
  }
  if (curr_ratio < opt_ratio) {
    mpc_ctx->set_opt_ratio(curr_ratio);
    mpc_ctx->set_opt_bid(bid);
    mpc_ctx->set_last_update_timestamp_ms(TimeUtil::Instance().GetTimestamp() / 1000);
    LOG_EVERY_N(INFO, 1000) << "merchant_opt_bid CalcMpcOptBidOnce, unit_id: "
                         << lowest_cost_context->unit_id()
                         << " bid: " << bid
                         << " opt_ratio: " << curr_ratio
                         << " total_pv_ratio: " << total_pv_ratio
                         << " total_cost_ratio: " << total_cost_ratio
                         << " pred_pv_ratio: " << pred_pv_ratio
                         << " past_pv_ratio: " << lowest_cost_context->past_pv_ratio()
                         << " past_cost_ratio: " << lowest_cost_context->past_cost_ratio()
                         << " pred_cost_ratio: " << pred_cost_ratio;
    return true;
  }
  return false;
}

void UpdateMpcSnapShot(NativeLowestCostContext* lowest_cost_context_ptr) {
  kuaishou::ad::LowestCostSnapshot snapshot_tmp;
  auto& snapshot = *lowest_cost_context_ptr->mutable_snapshot_for_mpc();
  // remove old snapshot
  auto snapshot_size = snapshot.state_info_size();
  uint32_t max_snapshot_size = 30;  // 最多保留 30 个 state_info
  uint32_t start_point = 0;
  if (snapshot_size > max_snapshot_size - 1) {
    start_point = snapshot_size - max_snapshot_size + 1;
  }
  for (uint32_t i = start_point; i < snapshot_size; i++) {
    const auto& current_state_info = snapshot.state_info(i);
    auto* new_state_info = snapshot_tmp.add_state_info();
    new_state_info->CopyFrom(current_state_info);
  }
  auto* new_state_info = snapshot_tmp.add_state_info();
  new_state_info->set_cost(lowest_cost_context_ptr->day_cost());
  new_state_info->set_time(TimeUtil::Instance().GetTimestamp() / 1000000);
  new_state_info->set_bid(lowest_cost_context_ptr->current_cpa());
  snapshot.Swap(&snapshot_tmp);
}

Bid2Value GetPostBid2ValueMap(NativeLowestCostContext* const lowest_cost_context_ptr,
                              uint64_t unit_id, const NoBidConfig& strategy_config) {
  auto& snapshot = lowest_cost_context_ptr->snapshot_for_mpc();
  // bid 2 cost
  Bid2Value bid2value;
  for (uint32_t i = 0; i + 1 < snapshot.state_info_size(); i++) {
    if (snapshot.state_info(i + 1).time() > snapshot.state_info(i).time()) {
      bid2value.add_bid_list(snapshot.state_info(i).bid());
      bid2value.add_cost_list((snapshot.state_info(i + 1).cost() - snapshot.state_info(i).cost()) /
          (snapshot.state_info(i + 1).time() - snapshot.state_info(i).time()));
    }
  }
  UpdateBid2PostValueMap(&bid2value, unit_id);
  return bid2value;
}

// 处理 bid_list & cost_list，原始数据未排序
void UpdateBid2PostValueMap(Bid2Value* bid2value, uint64_t unit_id) {
  auto bid_list = bid2value->mutable_bid_list();
  auto cost_list = bid2value->mutable_cost_list();
  if (bid_list->size() == 0 || cost_list->size() == 0 || bid_list->size() != cost_list->size()) {
    return;
  }
  ks::infra::PerfUtil::SetLogStash(bid_list->size(), GetOptBidNameSpace(), "bid2value_size",
                                    std::to_string(unit_id));
  using PairOfIts = std::pair<float, float>;
  std::vector<PairOfIts> v;
  auto i = bid_list->begin();
  auto j = cost_list->begin();
  for (; i != bid_list->end(); ++i, ++j) {
    v.push_back(std::make_pair(*i, *j));
  }
  std::sort(v.begin(), v.end(), [](PairOfIts const& i, PairOfIts const& j) {
    return i.first < j.first;
  });
  std::vector<float> sorted_bid_list;
  std::vector<float> sorted_cost_list;
  uint64_t index = 0;
  float last_bid = 0;
  float total_sum_cost = 0;
  uint64_t bid_count = 0;
  for (auto& x : v) {
    if (bid_count > 0 && (x.first != last_bid)) {
      sorted_bid_list.push_back(last_bid);
      sorted_cost_list.push_back(total_sum_cost / bid_count);
      total_sum_cost = 0;
      bid_count = 0;
    }
    total_sum_cost += x.second;
    bid_count += 1;
    last_bid = x.first;
  }
  sorted_bid_list.push_back(last_bid);
  sorted_cost_list.push_back(total_sum_cost / bid_count);
  bid_list->CopyFrom({sorted_bid_list.begin(), sorted_bid_list.end()});
  cost_list->CopyFrom({sorted_cost_list.begin(), sorted_cost_list.end()});
}

void OptBidLogStashValues(const BidStateInfoPtr& bid_info_ptr,
                          NativeLowestCostContext* lowest_cost_context, double pred_pv_ratio,
                          double pred_cost_ratio, std::string tag) {
  std::string unit_id = base::Uint64ToString(lowest_cost_context->unit_id());
  auto key_id = GetKeyId(lowest_cost_context);
  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->past_pv_ratio() * 1000, GetOptBidNameSpace(), "past_pv_ratio", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->past_cost_ratio() * 1000, GetOptBidNameSpace(), "past_cost_ratio", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(pred_pv_ratio * 1000, GetOptBidNameSpace(), "pred_pv_ratio", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(pred_cost_ratio * 1000, GetOptBidNameSpace(), "pred_cost_ratio",
                                   key_id, tag);
  ks::infra::PerfUtil::SetLogStash((lowest_cost_context->past_pv_ratio() + pred_pv_ratio) * 1000,
                                   GetOptBidNameSpace(), "total_pv_ratio", key_id, tag);
  ks::infra::PerfUtil::SetLogStash((lowest_cost_context->past_cost_ratio() + pred_cost_ratio) * 1000,
                                   GetOptBidNameSpace(), "total_cost_ratio", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->cumu_real_cost(), GetOptBidNameSpace(), "cumu_real_cost", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->cumu_pred_cost(), GetOptBidNameSpace(), "cumu_pred_cost", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->day_cost(), GetOptBidNameSpace(), "cost", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->day_budget(), GetOptBidNameSpace(), "budget", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->day_remain_budget(), GetOptBidNameSpace(), "remain_budget", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->current_budget(), GetOptBidNameSpace(), "current_budget", key_id, tag);

  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->mpc_ctx().opt_bid(), GetOptBidNameSpace(), "opt_bid",
    key_id, tag, "native_lowest_cost");
  ks::infra::PerfUtil::SetLogStash(
    lowest_cost_context->mpc_ctx().checked_opt_bid(), GetOptBidNameSpace(), "checked_opt_bid_realtime",
    key_id, tag, "native_lowest_cost");
  kuaishou::ad::BidInnerOcpmPerfLog& perf_log = GetBidServerMerchantOcpmPerfLog();
  perf_log.set_past_pv_proportion(lowest_cost_context->past_pv_ratio() * 1000000);
  perf_log.set_past_cost_proportion(lowest_cost_context->past_cost_ratio() * 1000000);
  perf_log.set_current_budget(lowest_cost_context->current_budget());
  perf_log.set_day_budget(bid_info_ptr->day_budget);
  perf_log.set_budget(bid_info_ptr->budget);
  perf_log.set_day_budget_left(bid_info_ptr->day_budget_left);
  perf_log.set_day_remain_budget(lowest_cost_context->day_remain_budget());
  perf_log.set_auto_bid(lowest_cost_context->current_cpa() * 1000);
  perf_log.set_day_cost(lowest_cost_context->day_cost());
}

double CalibratePredCost(NativeLowestCostContext* lowest_cost_context,
                         const NoBidConfig& strategy_config,
                         double pred_cost) {
  // calibration
  double cumu_pred_value = lowest_cost_context->cumu_pred_cost();
  double cumu_real_value = lowest_cost_context->cumu_real_cost();
  double calibration_ratio = 0;
  double pred_cost_after_cali = pred_cost;
  if (strategy_config.enable_opt_bid_calibration()
      && cumu_real_value > strategy_config.opt_bid_calibration_threshold()
      && cumu_pred_value > 0) {
    calibration_ratio = cumu_real_value / cumu_pred_value;
    pred_cost_after_cali = pred_cost * calibration_ratio;
    LOG_IF(INFO, strategy_config.is_debug()) << "merchant_opt_bid, CalibratePredCost: "
                          << " unit_id:" << lowest_cost_context->unit_id()
                          << " pred_cost: " << pred_cost
                          << " pred_cost_after_cali: " << pred_cost_after_cali
                          << " cumu_pred_value: " << cumu_pred_value
                          << " cumu_real_value: " << cumu_real_value
                          << " calibration_ratio: " << calibration_ratio;
  }
  return pred_cost_after_cali;
}

// 预估下一个调价周期的 pv
double GetPredPv(double pred_window, const std::vector<double>& pv_proportion) {
  double pred_pv = 0.0;
  uint64_t start_time = TimeUtil::Instance().GetTimestamp() / 1000;
  uint64_t end_time = start_time + pred_window * 1000;
  pred_pv = GetIntervalPvRatio(pv_proportion, start_time, end_time);
  return std::max(pred_pv, 0.0);
}

// 预估下一个调价周期的 cost
double GetPredCost(NativeLowestCostContext* lowest_cost_context,
                   int64_t table_idx, const Bid2Value& bid2value, double pred_pv,
                   const std::vector<double>& pv_proportion, const NoBidConfig& strategy_config) {
  double pred_cost = 0.0;
  const auto& cost_list = bid2value.cost_list();
  const auto& bid_list = bid2value.bid_list();
  uint64_t unit_window_start_time = bid2value.start_time();
  uint64_t unit_window_end_time = bid2value.end_time();
  double unit_window_pv = GetIntervalPvRatio(pv_proportion, unit_window_start_time, unit_window_end_time);
  if (unit_window_pv > 0 && pred_pv > 0) {
    pred_cost = cost_list[table_idx] * pred_pv / unit_window_pv;
  } else {
    pred_cost = cost_list[table_idx];
  }
  LOG_EVERY_N(INFO, 1000) << "merchant_opt_bid, GetPredCost: "
                        << " unit_id:" << lowest_cost_context->unit_id()
                        << " bid: " << bid_list[table_idx]
                        << " cost: " << cost_list[table_idx]
                        << " pred_pv: " << pred_pv
                        << " unit_window_pv: " << unit_window_pv
                        << " pred_cost: " << pred_cost;
  return pred_cost;
}

bool GetMpcBound(NativeLowestCostContext* lowest_cost_context,
                 const NativeLowestCostMsg& msg,
                 const NoBidConfig& strategy_config,
                 double old_bid,
                 BidStateInfoPtr& bid_info_ptr,   // NOLINT
                 double* lower_bound,
                 double* upper_bound) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  std::string bound_reason = "mpc_default";
  std::string tag = GetGroupTag(msg);
  auto key_id = GetKeyId(lowest_cost_context);
  double init_bid = 10.0;
  if (strategy_config.enable_roi_ratio_guard()
        && IsROAS(bid_info_ptr->ocpx_action_type)) {
    init_bid = GetInitROIRatio(lowest_cost_context, strategy_config, "", bid_info_ptr, tag);
  } else {
    init_bid = GetInitCpaBid(lowest_cost_context, strategy_config, "", bid_info_ptr, tag);
  }

  auto ocpc_action_type = kuaishou::ad::AdActionType_Name(msg.inner_data->ocpc_action_type);
  auto account_id = msg.inner_data->account_id;
  std::string account_key = absl::Substitute("$0_$1", account_id, ocpc_action_type);
  old_bid = old_bid > 0 ? old_bid : init_bid;
  // bound 单位转化
  // 移动端：非 ROI 单位厘转换为分
  if (IsFanstop(bid_info_ptr->campaign_type)
      && msg.inner_data->ocpc_action_type != kuaishou::ad::AD_MERCHANT_ROAS
      && msg.inner_data->ocpc_action_type == kuaishou::ad::AD_STOREWIDE_ROAS
      && msg.inner_data->ocpc_action_type != kuaishou::ad::AD_FANS_TOP_ROI
      && !IsFanstopAutoBidLi(bid_info_ptr)) {
    init_bid = init_bid / 10;
  }
  // 这里 bound 都是针对 bid 的，roas 的为 1/roi，上调出价直接上调 upper_bound
  double lower_bound_cpa_bid = 0.0;
  double upper_bound_cpa_bid = 0.0;
  if (msg.inner_data->ocpc_action_type == kuaishou::ad::AD_MERCHANT_ROAS
      || msg.inner_data->ocpc_action_type == kuaishou::ad::AD_STOREWIDE_ROAS
      || msg.inner_data->ocpc_action_type == kuaishou::ad::AD_FANS_TOP_ROI) {
    lower_bound_cpa_bid = strategy_config.opt_bid_roi_lower_bound();
    upper_bound_cpa_bid = strategy_config.opt_bid_roi_upper_bound();
    // 大促白名单放开上限
    auto activity_upper_config = MerchantKconfUtil::activityNobidUpperConfig();
    double ratio = 1.0;
    auto iter = activity_upper_config->find(std::to_string(account_id));
    if (iter != activity_upper_config->end()) {
      ratio = iter->second;
    }
    upper_bound_cpa_bid *= ratio;
    if (strategy_config.enable_limit_step_ratio()) {
      double up_step_ratio = strategy_config.opt_bid_up_step_ratio();
      double down_step_ratio = strategy_config.opt_bid_down_step_ratio();
      upper_bound_cpa_bid = std::min(1.0 / old_bid + up_step_ratio, upper_bound_cpa_bid);
      lower_bound_cpa_bid = std::max(1.0 / old_bid - down_step_ratio, lower_bound_cpa_bid);
    }
  } else {
    lower_bound_cpa_bid = strategy_config.opt_bid_lower_ratio() * init_bid;
    upper_bound_cpa_bid = strategy_config.opt_bid_upper_ratio() * init_bid;
    // account 白名单上下限
    auto lower_config = *MerchantKconfUtil::NativeLowestCostDefaultLowerConfig();
    auto upper_config = *MerchantKconfUtil::NativeLowestCostDefaultUpperConfig();
    if (lower_config.find(account_key) != lower_config.end()) {
      lower_bound_cpa_bid = lower_config[account_key];
    }
    if (upper_config.find(account_key) != upper_config.end()) {
      upper_bound_cpa_bid = upper_config[account_key];
    }
    // 大促白名单放开上限
    auto activity_upper_config = MerchantKconfUtil::activityNobidUpperConfig();
    double ratio = 1.0;
    auto iter = activity_upper_config->find(std::to_string(account_id));
    if (iter != activity_upper_config->end()) {
      ratio = iter->second;
    }
    upper_bound_cpa_bid *= ratio;
    if (strategy_config.enable_limit_step_ratio()) {
      double up_step_ratio = strategy_config.opt_bid_up_step_ratio();
      // 拉新 account 白名单，放开上调幅度
      const auto account_step_map = MerchantKconfUtil::NativeOptBidAccountStepMap();
      auto iter = account_step_map->find(account_key);
      if (iter != account_step_map->end()) {
        up_step_ratio = iter->second;
      }
      upper_bound_cpa_bid = std::min(old_bid + up_step_ratio * init_bid, upper_bound_cpa_bid);
      lower_bound_cpa_bid = std::max(old_bid - strategy_config.opt_bid_down_step_ratio() * init_bid,
                                     lower_bound_cpa_bid);
    }
  }
  if (MPCNullConversionCustomizedBound(lowest_cost_context, bid_info_ptr,
                                msg, strategy_config,
                                &lower_bound_cpa_bid, &upper_bound_cpa_bid)) {
    bound_reason = "mpc_null_conversion_bound";
  }
  if (MPCBudgetCustomizedBound(lowest_cost_context, bid_info_ptr,
                            msg, strategy_config,
                            init_bid, tag,
                            &lower_bound_cpa_bid, & upper_bound_cpa_bid)) {
    bound_reason = "mpc_budget_customized_bound";
  }
  lowest_cost_context->set_bound_reason(bound_reason);
  *lower_bound = lower_bound_cpa_bid;
  *upper_bound = upper_bound_cpa_bid;
  return true;
}

bool CheckMpcBid(NativeLowestCostContext* lowest_cost_context,
                 const NativeLowestCostMsg& msg, const NoBidConfig& strategy_config, double bid,
                 double old_bid, double* new_bid, BidStateInfoPtr& bid_info_ptr) {      // NOLINT    // NOLINT
  if (bid_info_ptr == nullptr) {
    return false;
  }
  std::string tag = GetGroupTag(msg);
  auto key_id = GetKeyId(lowest_cost_context);
  double init_bid = 10.0;
  if (strategy_config.enable_roi_ratio_guard()
        && IsROAS(bid_info_ptr->ocpx_action_type)) {
    init_bid = GetInitROIRatio(lowest_cost_context, strategy_config, "", bid_info_ptr, tag);
  } else {
    init_bid = GetInitCpaBid(lowest_cost_context, strategy_config, "", bid_info_ptr, tag);
  }
  double lower_bound_cpa_bid = 0.0;
  double upper_bound_cpa_bid = 0.0;
  if (!GetMpcBound(lowest_cost_context, msg, strategy_config, old_bid, bid_info_ptr,
                  &lower_bound_cpa_bid, &upper_bound_cpa_bid)) {
    return false;
  }
  ks::infra::PerfUtil::SetLogStash(
    upper_bound_cpa_bid * 1000, GetOptBidNameSpace(), "upper_bound_cpa_bid", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    lower_bound_cpa_bid * 1000, GetOptBidNameSpace(), "lower_bound_cpa_bid", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    init_bid * 1000, GetOptBidNameSpace(), "init_bid", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    bid * 1000, GetOptBidNameSpace(), "bid", key_id, tag);
  ks::infra::PerfUtil::SetLogStash(
    old_bid * 1000, GetOptBidNameSpace(), "old_bid", key_id, tag);
  if (msg.inner_data->ocpc_action_type == kuaishou::ad::AD_MERCHANT_ROAS
      || msg.inner_data->ocpc_action_type == kuaishou::ad::AD_STOREWIDE_ROAS
      || msg.inner_data->ocpc_action_type == kuaishou::ad::AD_FANS_TOP_ROI) {
    *new_bid = 1.0 / std::min(std::max(1.0 / bid, lower_bound_cpa_bid), upper_bound_cpa_bid);
    // 此处 bound 对于 roas 为 roi
    if (lower_bound_cpa_bid > 0 && upper_bound_cpa_bid > 0) {
      lowest_cost_context->set_upper_bound(1.0 / lower_bound_cpa_bid);
      lowest_cost_context->set_lower_bound(1.0 / upper_bound_cpa_bid);
    }
  } else {
    *new_bid = std::min(std::max(bid, lower_bound_cpa_bid), upper_bound_cpa_bid);
    lowest_cost_context->set_upper_bound(upper_bound_cpa_bid);
    lowest_cost_context->set_lower_bound(lower_bound_cpa_bid);
  }
  // 同步更新 pacing_rate
  if (IsLive(bid_info_ptr) &&
      (msg.inner_data->ocpc_action_type == kuaishou::ad::AD_MERCHANT_ROAS
       || msg.inner_data->ocpc_action_type == kuaishou::ad::AD_STOREWIDE_ROAS
       || msg.inner_data->ocpc_action_type == kuaishou::ad::AD_FANS_TOP_ROI)) {
    // 对于 roi 目标，new_bid = auto_roas = init_bid / pacing_rate
    if (*new_bid > 0) {
      lowest_cost_context->set_pacing_rate(init_bid / (*new_bid));
    }
  } else {
    if (init_bid > 0) {
      lowest_cost_context->set_pacing_rate(*new_bid / init_bid);
    }
  }
  double final_bid = bid;
  if ((msg.inner_data->ocpc_action_type == kuaishou::ad::AD_MERCHANT_ROAS
      || msg.inner_data->ocpc_action_type == kuaishou::ad::AD_STOREWIDE_ROAS
      || msg.inner_data->ocpc_action_type == kuaishou::ad::AD_FANS_TOP_ROI)
      && bid > 0) {
    final_bid = 1.0 / bid;
  }
  if (final_bid >= lower_bound_cpa_bid && final_bid <= upper_bound_cpa_bid) {
    return true;
  }
  return false;
}

bool CheckBid(NativeLowestCostContext* lowest_cost_context,
              const NativeLowestCostMsg& msg, const NoBidConfig& strategy_config, double bid,
              double* new_bid) {
  auto ocpc_action_type = kuaishou::ad::AdActionType_Name(msg.inner_data->ocpc_action_type);
  auto lower_config = *MerchantKconfUtil::NativeLowestCostDefaultLowerConfig();
  auto upper_config = *MerchantKconfUtil::NativeLowestCostDefaultUpperConfig();
  double lower_bound = lower_config[ocpc_action_type];
  double upper_bound = upper_config[ocpc_action_type];

  if (strategy_config.enable_hard_bounds()) {
    lower_bound = strategy_config.hard_lower_bound();
    upper_bound = strategy_config.hard_upper_bound();
  }
  if (strategy_config.enable_gmv_bound() && lowest_cost_context->callback_purchase_amount() > 0) {
    // callback_purchase_amount 的单位是元
    auto gmv_lower_bound_ratio = strategy_config.gmv_lower_bound_ratio();
    auto gmv_upper_bound_ratio = strategy_config.gmv_upper_bound_ratio();
    if (gmv_lower_bound_ratio > 1e-6) {
      lower_bound = gmv_lower_bound_ratio * lowest_cost_context->callback_purchase_amount() * 1000;
    }
    if (gmv_upper_bound_ratio  > 1e-6) {
      upper_bound = gmv_upper_bound_ratio * lowest_cost_context->callback_purchase_amount() * 1000;
    }
  }
  if (strategy_config.enable_roi_upper_bound() && lowest_cost_context->day_cost() > 0) {
    auto ratio_diff = lowest_cost_context->past_pv_ratio() - lowest_cost_context->past_cost_ratio();
    auto roi = lowest_cost_context->day_gmv() * 1000.0 / lowest_cost_context->day_cost();
    if (roi >= strategy_config.target_roi() && ratio_diff > 0) {
      upper_bound = upper_bound * strategy_config.roi_kp();
    }
  }

  if (strategy_config.enable_roi_lower_bound() && lowest_cost_context->day_cost() > 0) {
    auto ratio_diff = lowest_cost_context->past_pv_ratio() - lowest_cost_context->past_cost_ratio();
    auto roi = lowest_cost_context->day_gmv() * 1000.0 / lowest_cost_context->day_cost();
    if (roi >= strategy_config.target_roi() && ratio_diff > 0) {
      lower_bound = lower_bound * strategy_config.roi_kp();
    }
  }
  bool res = true;
  if (bid > upper_bound || bid < lower_bound) {
    res = false;
    *new_bid = std::min(std::max(bid, lower_bound), upper_bound);
  } else {
    res = true;
  }
  return res;
}

bool MPCBudgetCustomizedBound(NativeLowestCostContext* lowest_cost_context,
                                  BidStateInfoPtr& bid_info_ptr,      // NOLINT
                                  const NativeLowestCostMsg& msg,
                                  const NoBidConfig& strategy_config,
                                  uint64_t init_bid,
                                  std::string tag,
                                  const double* lower_bound, double* upper_bound) {
  if (!strategy_config.enable_budget_customized_bound()) {
    return false;
  }
  uint64 total_cv = lowest_cost_context->total_cv();
  if (total_cv > 0) {
    return false;
  }
  uint64_t bid_p2p = 0;
  uint64_t account_id = bid_info_ptr->account_id;
  uint64_t author_id = bid_info_ptr->author_id;
  auto budget = GetBudget(bid_info_ptr, lowest_cost_context, strategy_config, tag);
  kuaishou::ad::AdActionType ocpc_action_type = bid_info_ptr->ocpx_action_type;
  kuaishou::ad::AdEnum::CampaignType campaign_type = bid_info_ptr->campaign_type;
  auto* init_cpa_bid_instance = NativeLowestCostInitCpaBid::GetInstance();
  bool is_order_mode = IsOrderMode(bid_info_ptr->campaign_id);
  bool is_order_mode_live = IsOrderMode(bid_info_ptr->campaign_id) && IsPcLive(campaign_type);
  std::string key = absl::Substitute("$0_$1", account_id, ocpc_action_type);
  if (IsFanstopLive(campaign_type) || is_order_mode_live) {
    key = absl::Substitute("$0_$1_$2", strategy_config.mix_init_bid_key(),
        author_id, ocpc_action_type);
  } else if (IsFanstopPhoto(campaign_type) || (IsPcPhoto(campaign_type) && is_order_mode)) {
    key = absl::Substitute("$0_$1_$2", strategy_config.mix_init_bid_key(),
        account_id, ocpc_action_type);
  }
  if (init_cpa_bid_instance && init_cpa_bid_instance->GetInitCpaBid(key) > 0) {
    bid_p2p = init_cpa_bid_instance->GetInitCpaBid(key);
  }

  std::string budget_seg = "default";
  if (bid_p2p > 0) {
    double budget_level = budget / bid_p2p * 1.0;
    if (budget_level < 0.8) {
      budget_seg = "below_0.8";
    } else if (budget_level >= 0.8 && budget_level < 1.0) {
      budget_seg = "between_0.8_1.0";
    } else if (budget_level >= 1.0 && budget_level < 1.5) {
      budget_seg = "between_1.0_1.5";
    } else if (budget_level >= 1.5 && budget_level < 2) {
      budget_seg = "between_1.5_2";
    } else if (budget_level >= 2 && budget_level < 3) {
      budget_seg = "between_2_3";
    } else {
      budget_seg = "above_3";
    }
  } else {
    budget_seg = "other";
  }
  const auto& budget_customized_bound_map = MerchantKconfUtil::budgetCustomizedBoundMap();
  auto iter = budget_customized_bound_map->find(
    absl::StrCat(strategy_config.budget_customized_bound_exp_name(), "_", budget_seg));
  if (iter != budget_customized_bound_map->end()) {
    double upper_bound_ratio = iter->second;
    *upper_bound = init_bid * upper_bound_ratio;
    return true;
  }
  return false;
}

bool MPCNullConversionCustomizedBound(NativeLowestCostContext* lowest_cost_context,
                                  BidStateInfoPtr& bid_info_ptr,      // NOLINT
                                  const NativeLowestCostMsg& msg,
                                  const NoBidConfig& strategy_config,
                                  const double* lower_bound, double* upper_bound) {
  auto config = strategy_config.null_conversion_bound_config();
  bool open_exp = config.open_exp();
  double past_cost_ratio_thr = config.past_cost_ratio_thr();
  double past_pred_cvr_thr = config.past_pred_cvr_thr();
  double bound_ratio = config.bound_ratio();
  if (!open_exp) {
    return false;
  }
  std::string tag = GetGroupTag(msg);
  double init_bid = 10.0;
  if (strategy_config.enable_roi_ratio_guard()
        && IsROAS(bid_info_ptr->ocpx_action_type)) {
    init_bid = GetInitROIRatio(lowest_cost_context, strategy_config, "", bid_info_ptr, tag);
  } else {
    init_bid = GetInitCpaBid(lowest_cost_context, strategy_config, "", bid_info_ptr, tag);
  }
  double origin_lower_bound = strategy_config.opt_bid_lower_ratio() * init_bid;
  double origin_upper_bound = strategy_config.opt_bid_upper_ratio() * init_bid;
  auto budget = GetBudget(bid_info_ptr, lowest_cost_context, strategy_config, tag);
  auto cost = lowest_cost_context->day_cost();
  double past_cost_proportion = CalcPastCostProportion(budget, cost);
  uint64 total_cv = lowest_cost_context->total_cv();
  double total_pred_cv = lowest_cost_context->total_pred_cv();
  bool is_self_service_account_tail = IsSelfServiceAccountExp(bid_info_ptr, strategy_config);
  bool is_less_preCV = total_pred_cv < past_pred_cvr_thr;
  if (total_cv == 0
      && is_self_service_account_tail
      && past_cost_proportion > past_cost_ratio_thr
      && is_less_preCV
      && past_cost_proportion > 0) {
    *upper_bound = std::min(origin_upper_bound,
  std::max(origin_upper_bound * bound_ratio * total_pred_cv/past_cost_proportion, origin_lower_bound));
    ks::infra::PerfUtil::CountLogStash(1, GetNameSpace(), "mpc_null_conversion_bound_in");
    return true;
  }
  return false;
}

}  // namespace bid_server
}  // namespace ks

