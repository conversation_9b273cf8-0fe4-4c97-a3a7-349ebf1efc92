#pragma once

#include <string>
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/bid_server/framework/utils/kconf_data/grouping_config.h"
#include "teams/ad/bid_server/framework/utils/kconf_data/kconf_data.pb.h"
#include "teams/ad/bid_server/application/bid_server_trigger/kconf/kconf_data.h"
#include "teams/ad/bid_server/application/bid_server_trigger/proto/merchant_bid.pb.h"
#include "teams/ad/bid_server/framework/utils/kconf_data/pv_distribution.h"
#include "teams/ad/ad_proto/kuaishou/ad/bid_service/request_unit_sender.pb.h"
#include "teams/ad/bid_server/base/kconf/kconf_data.pb.h"
#include "teams/ad/engine_base/kconf/degrade_conf_manage_conf.h"
#include "teams/ad/bid_server/application/bid_server_trigger/proto/merchant_fanstop_cost_cap.pb.h"

namespace ks {
namespace bid_server {
using namespace ks::ad_base::kconf;  // NOLINT
using kuaishou::ad::MerchantBidTimerMonitorConfig;
using kuaishou::ad::MerchantBidExpConfigMap;
using kuaishou::ad::MerchantROASBidConfig;
using kuaishou::ad::MerchantOcpmBidConfig;
using kuaishou::ad::OperationConfigAutoROI;
using kuaishou::ad::OperationConfigAutoCpaBid;
using kuaishou::ad::MerchantROASExpConfigMap;
using kuaishou::ad::RoiPidAjustStrategy;
using CostCapOcpxUnitTailMap = ks::bid_server::kconf::CostCapOcpxUnitTailMapNew;
using kuaishou::ad::MerchantPhotoROASBidConfig;
using kuaishou::ad::MerchantPhotoROASBidGroupConfig;
using kuaishou::ad::MerchantPhotoROASBidExpConfig;
using kuaishou::ad::BidServerMerchantServerConfig;
using kuaishou::ad::bidService::RequestUnitTaskConfig;
using ks::bid_server::kconf::BidServerFanstopMultiGroupConfig;
using ks::bid_server_merchant::kconf::ChargeTagBudgetRateInfoList;
using ks::bid_server_merchant::kconf::FansTopCostCapPidConfig;
using ks::bid_server_merchant::kconf::FansTopCostCapOcpcConfig;
using ks::bid_server_merchant::kconf::FansTopOcpxActionType2ActionType;
using ks::bid_server_merchant::kconf::StepConf;
using NativeNobidExpConfig = ks::bid_server::kconf::NativeNobidExpConfigNew;
using kuaishou::ad::InnerMerchantConvTimeDelayConfig;
using kuaishou::ad::MerchantOcpmLinearPacingGridConfigMap;
using kuaishou::ad::MerchantFanstopCostcapExpConfig;

class MerchantKconfUtil {
 public:
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, clearAllDelayMapInfo, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableParseMerchantOrderPaidLog, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableParseMerchantCostLog, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableBidContextNew, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMerchantBidBoostStart, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMerchantPhotoRoasClearContext, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, useMerchantConsumerTrigger, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMerchantFanstopCampaignTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableEspNobidBound, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCostCapSkipDynamicBound, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableLowestCostTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enablePlaySecondsAction, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enablePcAudiencePlaySecondsAction, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMobileTimerTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCampaignMobileTimerTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCampaignPcTimerTrigger, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, isUseIsConversion, true);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, isMerchantOcpmSkipAB, false);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAuthorUnionExp, true);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFixedPeriodBeginTime, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCostCapSendTraceLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAccNobidSendTraceLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableOptBidGetBound, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopNobidAll, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAccIncrementIndex, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableSkipSourceType, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAudienceFastTPerf, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCboCostCapToOcpm, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFlowControlRedis, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopCostCapFix, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableDarkNobid, false);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, cboStoreWideDefaultStartBid, 0.6)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableBidServerSmbFill, false)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, storeWideColdstartSmoothCostLi, 100000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, storeWideColdstartSmoothTargetCostLi, 100000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, roasColdstartSmoothCostLi, 100000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, roasColdstartSmoothTargetCostLi, 100000.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, roasPerrorBound, 10.0)
  DEFINE_SET_NODE_KCONF(int64, ad.adRank2, newCustomerBsTails);
  DEFINE_SET_NODE_KCONF(int64, ad.adRank2, pureNewCustomerBSTail);
  DEFINE_SET_NODE_KCONF(int64, ad.adRank2, pureNewCustomerGMVBSTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, campaign2UnitTailId);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fixStorewideIncompatibleTypeCampaignTail);
  DEFINE_SET_NODE_KCONF(int64, ad.adEsp, storewideAllIncompatibleSwitch);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableStorewideDebug, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableRoasHierarchicalHardBound, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableAccIncrement, false)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, roasHierarchicalHardBoundConf);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, roasHierarchicalStepRatioConf);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, espT7RoiDefalutGmv0, 10.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, espT7RoiDefalutGmv7, 6.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, espT7RoiDefalutIndirectGmv7, 6.0)

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableMerchantClickStatTailSet);
  // 数据回滚检查周期
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchant_rollbackCheckIntervalSeconds, 1);
  // 回滚配置
  DEFINE_PROTOBUF_NODE_KCONF(MerchantRollbackManagerConf, ad.bidServer, merchant_rollbackManagerConf);
  // 重置检查周期
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchant_resetCheckIntervalSeconds, 1);
  // 重置配置
  DEFINE_PROTOBUF_NODE_KCONF(MerchantResetManagerConf, ad.bidServer, merchant_resetManagerConf);
  /// merchant_order_paid_log
  // kafka topic 参数
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchangOrderPaidLogBidServerKafkaTopic, "merchant_order_paid_log_bidserver");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantOrderPaidLogBidServerKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  // ad_merchant_reco_order
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRecoOrderBidServerKafkaTopic, "ad_merchant_reco_order");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRecoOrderBidServerKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  // roi cost
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRoiCostLogKafkaTopic, "bid_server_merchant_roi_cost");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRoiCostLogKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  // roi order
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRoiOrderPaidLogKafkaTopic, "client_log_for_bid_server");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantRoiOrderPaidLogKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  // kafka topic 参数
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchangCostLogBidServerKafkaTopic, "merchant_cost_ad_log_full_bidserver");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchangCostLogBidServerNoDiffKafkaTopic, "ad_log_for_algo_bidserver_graph_diff_test");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantCostLogBidServerKafkaParam, "kuaishou.set.offset.ms.ago=10000");
  //  message delay
  DEFINE_INT64_KCONF_NODE(ad.bidServer, maxMerchantKafkaMessageDelaySec, 60*60*12);  // 12 hours
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchantRecoOrderConsumerNum, 10)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchantOrderPaidLogConsumerNum, 16)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchantCostLogConsumerNum, 16)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, merchantFanstopLogConsumerNum, 16)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, accExploreBaseMs, 600*1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitActivateDoIntervalMs, 30*1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, requestUnitActivateDoIntervalMs, 1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, sjMerchantTestUnitId, 0)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, merchantHotUnitFreqThreshold, 100000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nobidHotUpdateBatchSize, 10000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nobidHotUpdateTimeInterval, 300)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, merchantHotUnitReportFreqThreshold, 1000)

  // nobid
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativePvROICalcMixProportion)  // 短视频 ROI PV 混合分布比例
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, NativePvROIDynamicCostWeight, 1.0)  // 短视频 ROI PV 权重
  // merchant trans
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantLiveTransTailSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantPhotoTransTailSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, unitRequestActiveOffTailSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, innerLiveHighAtvAuthor)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, coldStartAuthorExpSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, coldStartAuthorExpTailSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, MobileFastAudienceUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, MobileFastAudienceUnitTail);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, mobileAutoBidLiUnitSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, mobileAutoBidLiCampaignSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, dragonInnerLoopDebugUnitIdTail)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileAutoBidLiOcpxUnitTailMap)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, espLiveGspPriceConfigV2)

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, innerRemoveSpamOrderTailSet);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, innerRemoveSpamOrderGroupTag, "");

  // 分行业设置低客单价系数以及折扣系数
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, industryThresholdPrice);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, industryDiscountRatio);

  // config
  DEFINE_PROTOBUF_NODE_KCONF(
    BidServerMerchantServerConfig, ad.bidServer, bidServerMerchantServerConfig);
  DEFINE_PROTOBUF_NODE_KCONF(
      MerchantBidTimerMonitorConfig, ad.bidServer, merchantBidTimerMonitorConfig);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantROASBidConfig, ad.bidServer, merchantROASBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantROASExpConfigMap, ad.bidServer, merchantROASExpConfigMap);

  // unit request activate
  DEFINE_PROTOBUF_NODE_KCONF(RequestUnitTaskConfig, ad.bidServer, requestUnitTaskConfig);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, unitRequestActivateConsumerNum, 16)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, requestUnitActivateGroupTagSet);

  // operation config
  DEFINE_PROTOBUF_NODE_KCONF(OperationConfigAutoROI, ad.bidServer, operationConfigAutoROI);
  DEFINE_PROTOBUF_NODE_KCONF(OperationConfigAutoCpaBid, ad.bidServer, operationConfigAutoCpaBid)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantMonitorAuthorIdSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantMonitorAccountIdSet);

  // ocpm
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmPhotoBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmLiveAudienceBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmLiveFollowBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmBidConfig, ad.bidServer, merchantOcpmLiveOrderBidConf);

  DEFINE_PROTOBUF_NODE_KCONF(MerchantBidExpConfigMap, ad.bidServer, merchantOcpmExpConfigMap);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantBidExpConfigMap, ad.bidServer, merchantUniverseOcpmExpConfigMap);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, isOcpmLiveAd, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, isFanstopOcpmAd, true)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, merchantRUADropRate, 0);
  // 分页面成本
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, pageBidCaliBound);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer, pageIdToNameDict);
  DEFINE_INT64_STRING_MAP_KCONF(ad.bidServer, pageIdToKeyDict);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, skipBidPageCaliAuthorList);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, unitConvNumsCostThreshold);
  // 直播托管冷启动不压价
  DEFINE_INT64_KCONF_NODE(ad.bidServer, hostingLiveColdBoostCostThresh, 50000);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espLiveHostingRoasUnionBidTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableEspLiveHostingMsgJudgeTail, "");
  // 托管冷启动素材 boost
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableHostingLiveColdPhotoAuthorV2List);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, enableHostingLiveColdPhotoAuthorV2Tail);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, hostingLiveColdPhotoBoostRatio, 1.0)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enablehostingLiveWideColdPhotoBoost, false);

  // 直播控成本拆分物料
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, espLiveUnitAutoSplitItemTypeWhite, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, espLiveUnitAutoSplitItemTypeWhiteExp, "");
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, espLiveUnitSplitItemTypeOcpxWhite);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, espLiveUnitSplitItemTypeOcpxWhiteExp);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableUniverseSplitP2l, false);

  // 7 日 ROI 拆分物料算转化系数
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, t7RoiCombineItemAuthorRatio, "");

  DEFINE_PROTOBUF_NODE_KCONF(NoDiffLevelConfStruct, ad.bidServer, noDiffLevelConf)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, NoDiffRedisTtl, 3600*2);

  // lowest cost
  DEFINE_KCONF_NODE_LOAD(PVDistribution, ad.bidServer, merchantPhotoFlowDistribution)

  DEFINE_SET_NODE_KCONF(int32, ad.bidServer, MerchantUniverseConfSplitSet);
  // merchant costcap
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, CostCapUnitMaxCostDiscount, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, CostCapUnitCpaBidDiscount, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, CostCapUnitRoiDiscount, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, nobidInitBaseCpaBidDiscount, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FanstopRoasCutoffGuardbidRoiMin, 1.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FanstopRoasCutoffGuardbidRoiValue, 1.5)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, costCapOcpxUnitTailMap)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableMerchantCostCap, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableSpecialtyOcpmCostCap, false)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmCostCapUnitIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmCostCapLogUnitIdSet)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, ocpmCostCapOcpxUnitTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, ocpmCostCapOcpxAccountIdMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, ocpmCostCapOcpxAccountTailMap)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, costCapRedisName, "adMerchantROASBid")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, bid2ValueRedisName, "adBidSimulator")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer, innerUnitCreativeNumRedisName, "adEmsHostingData")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, nobidAccTransAccountTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, accIncrementTransAccountTail, "")
  DEFINE_TAILNUMBERV2_KCONF(adData.whiteBox, diffLogInnerAccountTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, costCapSkipAdjustValueUnitTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, costCapSkipAdjustValueCampaignTail, "")

  DEFINE_PROTOBUF_NODE_KCONF(ks::engine_base::DegradeConfManageConf, ad.bidServer, DegradeConfManage);

  // 关注页 boost auto_roi
  DEFINE_STRING_STRING_MAP_KCONF(ad.bidServer, merchantRoiAuthorWhiteMap);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, belowZeroPacingRate, 0.5)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, boostAuthorIds)

  // fanstop skip costcap
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, fanstopRoiUpperThre, 1.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, fanstopRoiLowerThre, 0.3)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopSkipCostcapCampaignTailSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopSkipCostcapPayerTailSet)

  // merchant fanstop costcap
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopOcpmCostCapCampaignIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopOcpmCostCapPayerIdSet)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, fanstopOcpmCostCapOcpxUnitTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, fanstopOcpmCostCapOcpxCampaignTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, fanstopOcpmCostCapOcpxPayerIdMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, fanstopOcpmCostCapOcpxPayerTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, nobidPcInitOcpxUnitTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, nobidMobileInitOcpxCampaignTailMap)
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, ocpmNobidAccOcpxUnitTailMap)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmNobidAccAccountIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmAccIncrementAccountIdSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, ocpmAccIncrementUnitIdSet)
  // fanstop campaign
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopCampaign, true)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopCampaignLaunchType, false)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopCampaignWhiteList)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopHostingCampaignTail)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, mobileHostingCboTail, "");

  // mobile costcap
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, mobileUnitWhiteList)  // 移动端 costcap 暗投白名单
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileCostcapOcpxUnitTailMap)

  // mobile nobid
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileOcpxUnitTailMap)

  // 移动端调价策略架构合并
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, mobileUnit2PcOcpmWhiteList)  // 移动端直播迁移至 PC 端
  DEFINE_PROTOBUF_NODE_KCONF(CostCapOcpxUnitTailMap, ad.bidServer, mobileLiveTransPcOcpmOcpxUnitTail)

  // 放贷逻辑作者白名单
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, loansAuthorWhiteList);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer2, loansAuthorRatioList);

  // photo roas
  DEFINE_PROTOBUF_NODE_KCONF(MerchantPhotoROASBidConfig, ad.bidServer, merchantPhotoROASBidConf);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantBidExpConfigMap, ad.bidServer, merchantPhotoROASExpConfigMap);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantPhotoROASBidExpConfig, ad.bidServer, merchantPhotoROASBidExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(NativeNobidExpConfig, ad.bidServer, nativeNobidExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantPhotoROASBidGroupConfig, ad.bidServer, merchantPhotoROASBidGroupConf);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.bidServer, merchantPhotoROASColdUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, agentAccountWhiteListROAS);
  DEFINE_PROTOBUF_NODE_KCONF(RoiPidAjustStrategy, ad.bidServer2, roiPidAjustStrategy);

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantLiveBidABTailNums);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, merchantPhotoBidABTailNums);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, adMerchantValidGroupTagSet);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, smoothPacingOcpxSet);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, adMerchantValidOcpmGroupTagSet);
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, adMerchantValidUniverseGroupTagSet);

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, dropUnitSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopOcpmTargetCostTestAuthorSet);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, dropUnitRatio, 0);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, dropInnerUnitSet);
  DEFINE_INT32_KCONF_NODE(ad.bidServer, dropInnerUnitRatio, 0);
  DEFINE_INT64_INT64_MAP_KCONF(ad.bidServer, hotDataGuardMap);
  // debug
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, adMerchantDebugUnitIdSet);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableStoreWideRoiDebugLog, false);

  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, roasOnlyDirectOrderUnitTail);
  // 内循环迁移
  DEFINE_SET_NODE_KCONF(int32_t, ad.bidServer, merchantRecoUnitTailSet);
  DEFINE_SET_NODE_KCONF(int32_t, ad.bidServer, merchantFanstopNewNobidTriggerUnitTailSet);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, bid2valueFetchThreadNum, 2);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitEnterIndexFetchThreadNum, 2);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitIsColdFetchThreadNum, 2);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, bid2valueFetchMaxPendingNum, 1000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitEnterIndexFetchMaxPendingNum, 1000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitIsColdFetchMaxPendingNum, 1000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, bid2valueValidExpireTime, 300);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, bid2valueInValidExpireTime, 120);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitEnterIndexValidExpireTime, 300);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitEnterIndexInValidExpireTime, 120);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitIsColdValidExpireTime, 300);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, unitIsColdInValidExpireTime, 120);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableBid2valueCache, true);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapFetchThreadNum, 2);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, liveHostingOrderPayAuthorTail, 0);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, liveHostingAllOcpxAuthorTail, 0);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapFetchMaxPendingNum, 1000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapValidExpireTime, 300);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapInValidExpireTime, 120);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, costCapRedisExpireTime, 86400);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, newCustBSAcccountTailConfigs);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, selfServiceAccountBSConfig);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, selfServiceBSTail, "");
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, pureNewCustBsAccountConfigs);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, newCustBSFixTails, "");
  // TODO(wangtao21): 测试代码
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, fanstopCostCapV2TransTail, "")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, fanstopMerchantFollowTail, "")
  // 内粉业务使用
  DEFINE_STRING_INT64_MAP_KCONF(reco.inner_fanstop, archimedesLowestCostInitBidMap);
  DEFINE_STRING_DOUBLE_MAP_KCONF(reco.inner_fanstop, archimedesLowestParamMap);  // 公式超参控制
  // fanstop 业务使用
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, consumeCheckUnits)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, consumeDebugUnits)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, maxKafkaMessageDelaySec, 120);  // 2 分钟
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopCostCapBoostThreshold, 180000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, currentShowUnitFlushWindowUs, 1000000);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestBoostThreshold, 600000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestBoostThreshold, 240000)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, OcpmDspZeroBoostUnitSet)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, LowestCostLiveDefaultDuration, 7200000)
  DEFINE_PROTOBUF_NODE_KCONF(BidServerFanstopMultiGroupConfig, ad.fanstopServer, fanstopMultiGroupConfig)
  DEFINE_PROTOBUF_NODE_KCONF(ChargeTagBudgetRateInfoList, ad.adFlowControl,
      fanstopChargeTagBudgetRateInfoList)
  DEFINE_PROTOBUF_NODE_KCONF(BidServerFanstopMultiGroupConfig, ad.fanstopServer, fanstopGroupExpConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, FansTopPvMinuteCalcProportion)  // 流量分布比例
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestUpdateIntervalMs, 180000)  // 粉条 LowestCost 更新间隔
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestStoreRemoteSize, 20)  // 粉条  LowestCost 更新窗口
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestUpdateBatchSize, 20)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansLowestCostEndingBoostTail, 0)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestUpdateIntervalMs, 120000)  // 内粉 lc 更新间隔
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestStoreRemoteSize, 20)  // 粉条  LowestCost 更新窗口
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestUpdateBatchSize, 100000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerLiveFansTopLowestUpdateIntervalMs, 30000)  // 内粉 lc 更新间隔
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerLiveFansTopLowestStoreRemoteSize, 10)  // 粉条 LC 更新窗口
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerLiveFansTopLowestUpdateBatchSize, 10000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopLowestOcpmInitBidFen, 300)  // 粉条  LowestCost 涨粉初始出价
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansTopLowestOcpmInitBidFen, 600)  // 粉条  LowestCost 初始出价
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansLiveLowestOcpmInitBidFen, 10)   // 直播 CPM LC 初始出价
  DEFINE_INT64_KCONF_NODE(ad.bidServer, InnerFansLiveLowestOcpmInitBidFen, 10)   // 直播 CPM LC 初始出价
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableCouponCost, false)
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, GroupBudgetEnableDefault, true)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidLnWeight, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidLnWeight, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidRatioWeight, 0.2)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidRatioWeight, 0.05)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidParamP, 0.45)  // 粉条 LowestCost 调价参数 P
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidParamI, 0.0001)  // 粉条 LowestCost 调价参数 I
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidParamD, 0.15)  // 粉条 LowestCost 调价参数 D
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidParamP, 1.0)  // 粉条 LowestCost 调价参数 P
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidParamI, 0.0)  // 粉条 LowestCost 调价参数 I
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidParamD, 3.0)  // 粉条 LowestCost 调价参数 D
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidRatio, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopLowestCostPidRatioMul, 0.2)
    DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidRatio, 0.75)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFansTopLowestCostPidRatioMul, 0.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FanstopHotMsgSkipRatio, 0.95)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, InnerFanstopHotMsgSkipRatio, 0.95)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, LowestCostOuterBidBound, 100000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, LowestCostOuterBidLowerBound, 1)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, LowestCostInnerBidBound, 100000)
  DEFINE_STRING_INT64_MAP_KCONF(ad.fanstopServer, FansTopLowestCostInitBidConfig);
  DEFINE_STRING_INT64_MAP_KCONF(ad.bidServer, EspLiveNobidUpperBound);
  DEFINE_STRING_INT64_MAP_KCONF(ad.bidServer, EspLiveNobidLowerBound);
  DEFINE_STRING_INT64_MAP_KCONF(ad.fanstopServer, InnerFansTopLowestCostInitBidConfig);
  DEFINE_STRING_INT64_MAP_KCONF(ad.fanstopServer, FansTopLowestCostMaxBidConfig);
  DEFINE_BOOL_KCONF_NODE(ad.bidServer, enableFanstopTraceLog, true);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, KakfaLogPidRatio, 0.1);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopCostCapLogInterval, 100)  // 粉条 Cost Cap Log
  // fanstop costcap
  DEFINE_INT64_KCONF_NODE(ad.bidServer, FansTopCostCapV2CostAchieveThreFen, 10000)  // 显示达成率消耗阈值
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, fansTopCostCapV2UnitTailSet)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, fansTopCostCapV3UnitTailSet)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, fansTopHighValueMcbUnitTailSet)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopLowestCostPayerSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopLowestCostOcpxPayerSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopLcPayerPassSet);
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, fanstopLcPayerOcpxPassSet);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantFanstopCostcapExpConfig, ad.bidServer, merchantFanstopCostcapExpConfig);
  DEFINE_PROTOBUF_NODE_KCONF(FansTopCostCapPidConfig, ad.bidServer, fansTopCostCapPidConfig);
  DEFINE_PROTOBUF_NODE_KCONF(FansTopCostCapOcpcConfig, ad.bidServer, fansTopCostCapOcpcConfig);
  DEFINE_PROTOBUF_NODE_KCONF(FansTopOcpxActionType2ActionType, ad.bidServer,
                            fansTopOcpxActionType2ActionType);
  DEFINE_PROTOBUF_NODE_KCONF(InnerMerchantConvTimeDelayConfig, ad.bidServer,
                             MerchantConvTimeDelayConfig);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantOcpmLinearPacingGridConfigMap, ad.bidServer,
                             MerchantOcpmLinearPacingGridMap);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopCostCapInitPacingRate, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, FansTopCostCapInitBidPacingRate, 1.0);
  DEFINE_INT64_KCONF_NODE(ad.bidServer, currentShowUnitMaxDirty, 20);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
    fanstopCurrentShowTopic, "bid_server_fanstop_current_show");
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, hostingNobidFixRoasLowerBound, 0.5)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, hostingNobidFixOrderUpperBound, 100000.0)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, hostingNobidFixCampaignTail)
  DEFINE_INT32_KCONF_NODE(ad.bidServer, nativeNobidLogInterval, 10)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativePvMinuteCalcProportion)  // 流量分布比例
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeLivePvMinuteCalcProportion)  // 专推直播流量分布比例
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeFlashPvMinuteCalcProportion)  // 速推直播流量分布比例
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nativeNobidDelaySeconds, 1800)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeLowestCostPidParamMap)
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, nativeProportionNewWhiteList)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, nativeInitCpaBidDiscount, 0.8)
  DEFINE_STRING_INT64_MAP_KCONF(ad.bidServer, NativeLowestCostInitBidConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, nativeLowestCostPidResultCoef)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, nativeLowestCostStepRatio, 0.1)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeLowestCostDefaultLowerConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeLowestCostDefaultUpperConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, activityNobidUpperConfig)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, NativeOptBidAccountStepMap)
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer, merchantPriceRatioMap)
  // 丢弃 action_type
  // 直播配置
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantLiveDisableActionType)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantLiveExpDisableActionType)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, merchantLiveExpDisableActionTypeUnitTail)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantLiveHotDisableActionType)
  // 直播成本保护参竞实验行业白名单
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, innerOcpmMpcIndustryExp);

  // 联盟直播监控日志白名单
  DEFINE_SET_NODE_KCONF(int64, ad.bidServer, universeMerchantLiveMonitorLogWhiteConfig)

  // 短视频配置
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantRecoDisableActionType)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantRecoExpDisableActionType)
  DEFINE_SET_NODE_KCONF(int64_t, ad.bidServer, merchantRecoExpDisableActionTypeUnitTail)
  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, merchantRecoHotDisableActionType)

  DEFINE_SET_NODE_KCONF(std::string, ad.bidServer, stepInterfaceStragtegySet)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nativeNobidUpdateInterval, 10 * 1000 * 1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, nativeNobidBudgetCheckInterval, 10 * 1000 * 1000)
  DEFINE_INT64_KCONF_NODE(ad.bidServer, bidStepTwoInterval, 1 * 1000 * 1000)
  // 策略接口切换
  DEFINE_PROTOBUF_NODE_KCONF(StepConf, ad.bidServer, stepStrategyConf);
  // 无 diff 测试
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.bidServer,
      merchantNoDiffRedis, "adEngineFlowProcess");
  DEFINE_PROTOBUF_NODE_KCONF(ks::bid_server_merchant::kconf::MerchantNoDiffConfPB,
                             ad.bidServer, merchantNoDiffConf);
  DEFINE_TAILNUMBERV2_KCONF(adData.whiteBox, diffLogAccountTail, "");
  DEFINE_INT64_KCONF_NODE(ad.bidServer, noDiffAdLogKafkaStartTimestamp, -1)
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, innerAggTail, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, disableSingleOrderPayLog, "")

  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, roi7TargetCostRatioUpper, "");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, enableFanstopV2SkipCostcapFixTail, "");
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, merchantAccFixKconf, false);  // 加速探索打折修复
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, enableNobidAccResetAccountTail, "");
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, pcLiveUnitCostTimeGap, 600);
  DEFINE_PROTOBUF_NODE_KCONF(kuaishou::ad::UpDimensionCaliExpConfMap,
                             ad.bidServer2, UpDimensionCaliExpConfMap);  // 升维调价
  DEFINE_BOOL_KCONF_NODE(ad.bidServer3, enableAccountCIDBillingSeperate, false);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.bidServer3, AccountCIDBillingSeperateMap);
};

}  // namespace bid_server
}  // namespace ks
