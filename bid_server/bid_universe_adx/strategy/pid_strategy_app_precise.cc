#include "teams/ad/bid_server/bid_universe_adx/strategy/pid_strategy_app_precise.h"
#include <algorithm>
#include <iterator>
#include <string>
#include <map>
#include <utility>
#include "falcon/counter.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_base/src/common/latency_record.h"
#include "teams/ad/bid_server/framework/utils/kconf.h"
#include "teams/ad/bid_server/framework/utils/perf_util.h"
#include "teams/ad/bid_server/public/base/common.h"
#include "teams/ad/bid_server/bid_universe_adx/kconf/kconf.h"
#include "teams/ad/engine_base/kconf/kconf.h"


namespace ks {
namespace bid_server {
using kuaishou::ad::dw::AdLogFull;
using kuaishou::ad::BidUniverseAdxGlobalData;
static const auto kLocalTimezone = absl::LocalTimeZone();

PidStrategyAppPrecise::PidStrategyAppPrecise()
: universe_adx_data_("BidUniverseAdx"),
universe_adx_global_data_("BidUniverseAdxGlobal"),
universe_adx_target_data_() {
  // 初始化参数
  InitParams();
  // 从配置中获取参数
  GetConfigParams();
  // 初始化状态
  ResetState();
}

PidStrategyAppPrecise::~PidStrategyAppPrecise() {
}

void PidStrategyAppPrecise::InitParams() {
  pid_params_.kp = 1.0;
  pid_params_.ki = 1.0;
  pid_params_.kd = 1.0;
  pid_params_.upper_limit = 1.0;
  pid_params_.lower_limit = -0.5;
  pid_params_.max_val = 3.0;
  pid_params_.min_val = 0.3;
  pid_params_.min_integral = 0.0;
  pid_params_.max_integral = 0.0;
  pid_params_.enable_change_integral = false;
  pid_data_.target = 1.0;
  decay_ = 0.0;
  init_coff_ = 1.0;
  start_ts_ = base::GetTimestamp() / 1000000;
}

void PidStrategyAppPrecise::GetConfigParams() {
  pid_trigger_thr_cost_ = bid_universe_adx::AdKconfUtil::universeAdxUpdateMediaAppCostThr();
  auto pid_params_map = bid_universe_adx::AdKconfUtil::universeAdxPidParamsConfig();
  auto params_map = pid_params_map->pid_params_map;
  auto bool_params_map = pid_params_map->pid_bool_params_map;
  if (params_map.count("kp") > 0) {
    pid_params_.kp = params_map["kp"];
  }
  if (params_map.count("ki") > 0) {
    pid_params_.ki = params_map["ki"];
  }
  if (params_map.count("kd") > 0) {
    pid_params_.kd = params_map["kd"];
  }
  if (params_map.count("upper_limit") > 0) {
    pid_params_.upper_limit = params_map["upper_limit"];
  }
  if (params_map.count("lower_limit") > 0) {
    pid_params_.lower_limit = params_map["lower_limit"];
  }
  if (params_map.count("max_val") > 0) {
    pid_params_.max_val = params_map["max_val"];
  }
  if (params_map.count("min_val") > 0) {
    pid_params_.min_val = params_map["min_val"];
  }
  if (params_map.count("min_integral") > 0) {
    pid_params_.min_integral = params_map["min_integral"];
  }
  if (params_map.count("max_integral") > 0) {
    pid_params_.max_integral = params_map["max_integral"];
  }
  if (bool_params_map.count("enable_change_integral") > 0) {
    pid_params_.enable_change_integral = bool_params_map["enable_change_integral"];
  }
  if (params_map.count("target") > 0) {
    pid_data_.target = params_map["target"];
  }
  if (params_map.count("decay") > 0) {
    decay_ = params_map["decay"];
  }
  if (params_map.count("init_coff") > 0) {
    init_coff_ = params_map["init_coff"];
  }


  LOG_EVERY_N(INFO, 10000) << "BidUniverseAdxApp parameters kp: " << pid_params_.kp
                           << " ki: " << pid_params_.ki << " kd: " << pid_params_.kd
                           << " upper_limit: " << pid_params_.upper_limit
                           << " lower_limit: " << pid_params_.lower_limit
                           << " target: " << pid_data_.target << " decay: " << decay_
                           << " init coff: " << init_coff_;
}

void PidStrategyAppPrecise::GetConfigParamsApp() {
  // 新增一个 kconf 配置。APP 粒度的参数调节抓手。
  // 形式："123456789_ki" : 0.8;
  const auto params_map_app_ptr = bid_universe_adx::AdKconfUtil::universeAdxPidParamsAppConfig();
  if (!params_map_app_ptr) return;

  // 处理用于计算当前 input 的 decay_
  std::string app_decay = absl::StrCat(app_id_key_, "_decay");
  auto app_decay_iter = params_map_app_ptr->find(app_decay);
  if (app_decay_iter != params_map_app_ptr->end()) {
    decay_ = app_decay_iter->second;
  }
  // 处理 pid 在媒体维度的触发逻辑
  std::string app_pid_trigger_thr_cost = absl::StrCat(app_id_key_, "_pid_thr_cost");
  auto app_pid_trigger_thr_cost_iter = params_map_app_ptr->find(app_pid_trigger_thr_cost);
  if (app_pid_trigger_thr_cost_iter != params_map_app_ptr->end()) {
    pid_trigger_thr_cost_ = app_pid_trigger_thr_cost_iter->second;
  }
  // 处理 PidParams
  std::string app_kp = absl::StrCat(app_id_key_, "_kp");
  std::string app_ki = absl::StrCat(app_id_key_, "_ki");
  std::string app_kd = absl::StrCat(app_id_key_, "_kd");
  std::string app_upper_limit = absl::StrCat(app_id_key_, "_upper_limit");
  std::string app_lower_limit = absl::StrCat(app_id_key_, "_lower_limit");
  std::string app_max_val = absl::StrCat(app_id_key_, "_max_val");
  std::string app_min_val = absl::StrCat(app_id_key_, "_min_val");
  std::string app_min_integral = absl::StrCat(app_id_key_, "_min_integral");
  std::string app_max_integral = absl::StrCat(app_id_key_, "_max_integral");
  std::string app_enable_change_integral = absl::StrCat(app_id_key_, "_enable_change_integral");

  auto app_kp_iter = params_map_app_ptr->find(app_kp);
  auto app_ki_iter = params_map_app_ptr->find(app_ki);
  auto app_kd_iter = params_map_app_ptr->find(app_kd);
  auto app_upper_limit_iter = params_map_app_ptr->find(app_upper_limit);
  auto app_lower_limit_iter = params_map_app_ptr->find(app_lower_limit);
  auto app_max_val_iter = params_map_app_ptr->find(app_max_val);
  auto app_min_val_iter = params_map_app_ptr->find(app_min_val);
  auto app_min_integral_iter = params_map_app_ptr->find(app_min_integral);
  auto app_max_integral_iter = params_map_app_ptr->find(app_max_integral);
  auto app_enable_change_integral_iter = params_map_app_ptr->find(app_enable_change_integral);

  if (app_kp_iter != params_map_app_ptr->end()) {
    pid_params_.kp = app_kp_iter->second;
  }
  if (app_ki_iter != params_map_app_ptr->end()) {
    pid_params_.ki = app_ki_iter->second;
  }
  if (app_kd_iter != params_map_app_ptr->end()) {
    pid_params_.kd = app_kd_iter->second;
  }
  if (app_upper_limit_iter != params_map_app_ptr->end()) {
    pid_params_.upper_limit = app_upper_limit_iter->second;
  }
  if (app_lower_limit_iter != params_map_app_ptr->end()) {
    pid_params_.lower_limit = app_lower_limit_iter->second;
  }
  if (app_max_val_iter != params_map_app_ptr->end()) {
    pid_params_.max_val = app_max_val_iter->second;
  }
  if (app_min_val_iter != params_map_app_ptr->end()) {
    pid_params_.min_val = app_min_val_iter->second;
  }
  if (app_min_integral_iter != params_map_app_ptr->end()) {
    pid_params_.min_integral = app_min_integral_iter->second;
  }
  if (app_max_integral_iter != params_map_app_ptr->end()) {
    pid_params_.max_integral = app_max_integral_iter->second;
  }
  if (app_enable_change_integral_iter != params_map_app_ptr->end()) {
    bool enable_limit_integral = (static_cast<int32_t>(app_enable_change_integral_iter->second) != 0);
    pid_params_.enable_change_integral = enable_limit_integral;
  }
}

void PidStrategyAppPrecise::ResetState() {
  // 数据重置
  pid_data_.input = pid_data_.target * init_coff_;
  pid_data_.output = pid_data_.target * init_coff_;
  // pid 控制器重置
  pid_controller_.Reset();
}

void PidStrategyAppPrecise::ClearControlData() {
  item_imp_cnt_ = 0;
  dynamic_share_control_data_.Clear();
  auto& key_name = *bid_universe_adx::AdKconfUtil::dynamicShareControl();
  kuaishou::ad::BidUniverseAdx& bid_adx = *universe_adx_data_.MutableValue(key_name);
  bid_adx.Clear();
  universe_adx_data_.RedisSave(key_name, bid_adx, 86400);
  LOG_EVERY_N(INFO, 1) << "ClearControlData finish";
}

void PidStrategyAppPrecise::ClearData() {
  // 保留状态数据
  kuaishou::ad::BidUniverseAdx& bid_adx =
          *universe_adx_data_.MutableValue(app_id_key_);
  auto cpm_bid_coff = bid_adx.cpm_bid_coff();
  kuaishou::ad::BidUniverseAdxGlobalData& bid_adx_global =
        *universe_adx_global_data_.MutableValue(app_id_key_);
  auto decay_kuaishou_cost = bid_adx_global.kuaishou_decay_cost();
  auto decay_media_cost = bid_adx_global.media_decay_cost_precise();
  auto decay_ratio = bid_adx_global.decay_ratio();

  // redis 中间数据清除
  bid_adx.Clear();
  bid_adx_global.Clear();

  // 恢复状态数据
  bid_adx.set_cpm_bid_coff(cpm_bid_coff);
  // 保留上一天的部分数据做贝叶斯平滑
  bid_adx_global.set_kuaishou_decay_cost(decay_kuaishou_cost * 0.1);
  bid_adx_global.set_media_decay_cost_precise(decay_media_cost * 0.1);
  bid_adx_global.set_decay_ratio(decay_ratio);
  universe_adx_data_.RedisSave(app_id_key_, bid_adx, 86400);
  universe_adx_global_data_.RedisSave(app_id_key_, bid_adx_global, 86400);

  int64_t app_id_key_int = 0;
  if (absl::SimpleAtoi(app_id_key_, &app_id_key_int)) {
    bool enable_clear_pid_data_daily = bid_universe_adx::AdKconfUtil::enableClearPidDataDaily();
    auto universe_clear_pid_data_daily_tail_set_ptr =
    bid_universe_adx::AdKconfUtil::universeClearPidDataDailyTailSet();
    bool is_hit = universe_clear_pid_data_daily_tail_set_ptr->count(app_id_key_int % 100);
    if (enable_clear_pid_data_daily && is_hit) {
      pid_controller_.ClearData();
    }
  }
}

void PidStrategyAppPrecise::Process(const RawMsgPtr& msg) {
  if (msg->type == +RawMsgType::MediaShareUpdate) {
    // 更新 target_ratio
    const auto &media_share_info = msg->value->get_value<MediaShareUpdateMsg>();
    kuaishou::ad::BidUniverseAdxTargetData& target_data =
        *universe_adx_target_data_.MutableValue(media_share_info.media_app_id);
    target_data.set_target_ratio(media_share_info.share_ratio);

    LOG_EVERY_N(INFO, 10) << "BidUniverseAdxApp Process SetTargetRatio "
                          << " media_app_id: " << media_share_info.media_app_id
                          << " share_ratio: " << media_share_info.share_ratio;
  } else if (msg->type == +RawMsgType::Adlogfull) {
    if (base::GetTimestamp() / 1000000 - start_ts_ <
        bid_universe_adx::AdKconfUtil::universeAdxIgnoreTimeTs()) {
      ks::infra::PerfUtil::CountLogStash(1, kPerfutilNamespace, "ingore_ad_log_full");
      return;
    }
    const AdLogFull& adlog = msg->value->get_value<AdLogFull>();
    auto update_time = absl::Now();
    auto update_bd = update_time.In(kLocalTimezone);
    app_id_key_ = adlog.media_app_id();
    pid_params_.key_str = app_id_key_;
    kuaishou::ad::BidUniverseAdx& data = *universe_adx_data_.MutableValue(app_id_key_);
    falcon::Inc(("bid_universe_adx.rtb_ad_log_full_msg_cnt_" + app_id_key_).c_str());
    ks::infra::PerfUtil::CountLogStash(1, kPerfutilNamespace, "new_rtb_ad_log_full_msg_cnt", app_id_key_);
    ks::infra::PerfUtil::SetLogStash(1, kPerfutilNamespace, "rtb_ad_log_full_msg_log", app_id_key_);
    if (!clean_on_boot_finish_ && bid_universe_adx::AdKconfUtil::enableDynamicShareCleanRedisOnBoot()) {
      ClearControlData();
      clean_on_boot_finish_ = true;
    }
    if (data.last_yearday() != 0 && update_bd.yearday != data.last_yearday()) {
      // 新的一天 reset
      ResetState();
      ClearData();
      ClearControlData();
    }
    kuaishou::ad::BidUniverseAdx& bid_adx = *universe_adx_data_.MutableValue(app_id_key_);
    kuaishou::ad::BidUniverseAdxTargetData& target_data =
        *universe_adx_target_data_.MutableValue(app_id_key_);
    bid_adx.set_last_yearday(update_bd.yearday);
    ks::infra::PerfUtil::IntervalLogStash(bid_adx.last_yearday(), kPerfutilNamespace,
                                     "new_universe_adx_data_last_yearday", app_id_key_);
    // 实时获取配置参数
    GetConfigParams();
    GetConfigParamsApp();
    // 获取相应的 target_ratio
    if (target_data.target_ratio() > 0) {
      pid_data_.target = target_data.target_ratio();
    }
    LOG_EVERY_N(INFO, 10000) << "BidUniverseAdxApp Process GetTargetRatio "
                             << " media_app_id: " << app_id_key_
                             << " share_ratio: " << target_data.target_ratio();
    // 初始化 pid_controller_
    pid_controller_.Initialize(&pid_data_, &pid_params_);
    if (bid_adx.cpm_bid_coff() != 0) {
      pid_data_.output = bid_adx.cpm_bid_coff();
    }
    // pid 调价
    Update(adlog);
  }
}

void PidStrategyAppPrecise::SaveResult(const AdLogFull& adlog) {
  LOG_EVERY_N(INFO, 10) << "BidUniverseAdxApp Begin SaveResult app_id_key_: " << app_id_key_;
  kuaishou::ad::BidUniverseAdx& bid_adx = *universe_adx_data_.MutableValue(app_id_key_);
  auto kuaishou_cost = bid_adx.kuaishou_total_cost();
  auto media_cost = bid_adx.media_total_cost_precise();
  double result = std::min(pid_data_.output, pid_params_.max_val);
  result = std::max(result, pid_params_.min_val);
  LOG_EVERY_N(INFO, 10000) << "adlog.virtual_cost_total() = " << adlog.virtual_cost_total();
  bid_adx.set_kuaishou_total_cost(kuaishou_cost + adlog.cost_total() + adlog.virtual_cost_total());
  bid_adx.set_media_total_cost(media_cost + std::min(adlog.returned_second_price(), adlog.ecpm()));
  // 加一行精度存储
  bid_adx.set_media_total_cost_precise(media_cost + std::min(adlog.returned_second_price_precise(),
                                      adlog.ecpm_precise()));
  bid_adx.set_cpm_bid_coff(pid_data_.output);

  if (bid_universe_adx::AdKconfUtil::StoreUniverseAdxAppBidInfo()) {
    if (engine_base::AdKconfUtil::universeAdxHotKeyWhiteList()->count(app_id_key_) > 0) {
      // 如果是热 key，则 hash
      int slice_nums = engine_base::AdKconfUtil::universeAdxHotKeySliceNum();
      for (int i = 0; i < slice_nums; i++) {
        LOG_EVERY_N(INFO, 1000) << "BidUniverseAdxApp SaveResult hot key app_id_key_: "
                                << app_id_key_ << "_" << i;
        universe_adx_data_.RedisSave(absl::StrCat(app_id_key_, "_", i), bid_adx, 86400);  // 存入 redis
      }
    }
    ks::infra::PerfUtil::CountLogStash(1, kPerfutilNamespace, "new_save_result_data_to_redis", app_id_key_);
    universe_adx_data_.RedisSave(app_id_key_, bid_adx, 86400);  // 存入 redis
  }
}

void PidStrategyAppPrecise::Update(const AdLogFull& adlog) {
  LOG_EVERY_N(INFO, 100) << "BidUniverseAdxApp Begin Update app_id_key_: " << app_id_key_;
  kuaishou::ad::BidUniverseAdxGlobalData& bid_adx_global =
      *universe_adx_global_data_.MutableValue(app_id_key_);
  UpdateGlobalData(adlog, &bid_adx_global);
  if (bid_universe_adx::AdKconfUtil::enableDynamicShareWriteControlSet()) {
    UpdateDynamicShareControlData(adlog);
  }

  bool cost_exceed_update_threshold = bid_adx_global.kuaishou_periodic_cost() > pid_trigger_thr_cost_;
  LOG_EVERY_N(INFO, 10000) << "app_id_key: " << app_id_key_
                           << " pid_trigger_thr_cost: " << pid_trigger_thr_cost_
                           << " current_cost: " << bid_adx_global.kuaishou_periodic_cost()
                           << " cost_exceed_update_threshold: " << cost_exceed_update_threshold;
  if (cost_exceed_update_threshold || (bid_adx_global.periodic_item_impression()
         > bid_universe_adx::AdKconfUtil::universeAdxUpdateAppImpressionThr())) {
    ks::infra::PerfUtil::CountLogStash(1, kPerfutilNamespace, "rtb_update_trigger_msg_cnt", app_id_key_);
    bid_adx_global.set_media_total_cost_precise(bid_adx_global.media_total_cost_precise()
                                      + bid_adx_global.media_periodic_cost_precise());
    // 触发一次调价
    UpdateGlobalDecayData(&bid_adx_global);
    if (bid_adx_global.media_decay_cost_precise() > 0) {
      if (bid_universe_adx::AdKconfUtil::universeDecayRatioToRealRatioSet() != nullptr &&
          bid_universe_adx::AdKconfUtil::universeDecayRatioToRealRatioSet()->count(app_id_key_)) {
        pid_data_.input = bid_adx_global.total_ratio();
        LOG_EVERY_N(INFO, 10000) << "app_id: " << app_id_key_
                                 << " total_ratio: " << bid_adx_global.total_ratio();
      } else {
        pid_data_.input = bid_adx_global.decay_ratio();
      }
      pid_controller_.Update();

      // 保存调价结果
      SaveResult(adlog);
      if (bid_universe_adx::AdKconfUtil::StoreUniverseAdxAppBidInfo()) {
        ks::infra::PerfUtil::CountLogStash(1, kPerfutilNamespace,
                                           "new_save_global_data_to_redis", app_id_key_);
        universe_adx_global_data_.RedisSave(app_id_key_, bid_adx_global, 86400);  // 存入 redis
      }

      LOG_EVERY_N(INFO, 10) << "BidUniverseAdxApp parameters kp: " << pid_params_.kp
                            << " ki: " << pid_params_.ki << " kd: " << pid_params_.kd
                            << " upper_limit: " << pid_params_.upper_limit
                            << " lower_limit: " << pid_params_.lower_limit
                            << " target: " << pid_data_.target << " decay: " << decay_
                            << "app id " << app_id_key_
                            << ", global data: " << bid_adx_global.ShortDebugString();
      if (bid_adx_global.kuaishou_total_cost() > 1000000) {
        if (bid_adx_global.total_ratio() > 1.1 * pid_data_.target
            || bid_adx_global.total_ratio() < 0.9 * pid_data_.target) {
          LOG_EVERY_N(INFO, 100) << "target miss Warning!,app id " << app_id_key_
                                << ", global data: " << bid_adx_global.ShortDebugString();
        }
      }
    }

    ks::infra::PerfUtil::IntervalLogStash(bid_adx_global.kuaishou_periodic_cost(), kPerfutilNamespace,
                                   "new_kuaishou_periodic_cost", app_id_key_);
    ks::infra::PerfUtil::IntervalLogStash(bid_adx_global.media_periodic_cost_precise(), kPerfutilNamespace,
                                   "new_media_periodic_cost", app_id_key_);
    //  reset global data
    ResetGlobalPeriodicData(&bid_adx_global);
  }

  LOG_EVERY_N(INFO, 10000) << "app_id: " << app_id_key_
                           << "BidUniverseAdxApp global data: " << bid_adx_global.ShortDebugString();

  //  新监控
  ks::infra::PerfUtil::IntervalLogStash(1000 * bid_adx_global.total_ratio(), kPerfutilNamespace,
                                   "new_total_ratio", app_id_key_);
  ks::infra::PerfUtil::IntervalLogStash(1000 * pid_data_.target, kPerfutilNamespace,
                                   "new_target_ratio", app_id_key_);
  ks::infra::PerfUtil::IntervalLogStash(1000 * bid_adx_global.decay_ratio(), kPerfutilNamespace,
                                   "new_decay_ratio", app_id_key_);
  ks::infra::PerfUtil::IntervalLogStash(1000 * bid_adx_global.periodic_ratio(), kPerfutilNamespace,
                                   "new_periodic_ratio", app_id_key_);
  ks::infra::PerfUtil::IntervalLogStash(bid_adx_global.kuaishou_total_cost(), kPerfutilNamespace,
                                   "new_kuaishou_cost", app_id_key_);
  ks::infra::PerfUtil::IntervalLogStash(bid_adx_global.media_total_cost_precise(), kPerfutilNamespace,
                                   "new_media_cost", app_id_key_);
  ks::infra::PerfUtil::IntervalLogStash(bid_adx_global.kuaishou_decay_cost(), kPerfutilNamespace,
                                   "new_kuaishou_decay_cost", app_id_key_);
  ks::infra::PerfUtil::IntervalLogStash(bid_adx_global.media_decay_cost_precise(), kPerfutilNamespace,
                                   "new_media_decay_cost", app_id_key_);
  ks::infra::PerfUtil::IntervalLogStash(1000 * pid_data_.output, kPerfutilNamespace,
                                   "new_current_cpm_bid_coff", app_id_key_);
}

void PidStrategyAppPrecise::UpdateDynamicShareControlData(const AdLogFull& adlog) {
  int64_t kuaishou_cost = 0;
  double media_cost = 0.0;
  auto update_time = absl::Now();
  auto update_bd = update_time.In(kLocalTimezone);
  if (dynamic_share_control_data_.last_yearday() != 0
      && update_bd.yearday == dynamic_share_control_data_.last_yearday()) {
    kuaishou_cost = dynamic_share_control_data_.kuaishou_total_cost();
    media_cost = dynamic_share_control_data_.media_total_cost_precise();
  } else {
    dynamic_share_control_data_.Clear();
    dynamic_share_control_data_.set_last_yearday(update_bd.yearday);
  }
  kuaishou_cost += adlog.cost_total();
  media_cost += std::min(adlog.returned_second_price_precise(), adlog.ecpm_precise())
    - adlog.rtb_virtual_price_precise();
  dynamic_share_control_data_.set_kuaishou_total_cost(kuaishou_cost);
  dynamic_share_control_data_.set_media_total_cost_precise(media_cost);
  dynamic_share_control_data_.set_media_total_cost(static_cast<int64_t>(media_cost));
  item_imp_cnt_ += 1;
  if (bid_universe_adx::AdKconfUtil::dynamicShareControlItemImpThr() > 0 &&
      item_imp_cnt_ % bid_universe_adx::AdKconfUtil::dynamicShareControlItemImpThr() == 0) {
    auto& key_name = *bid_universe_adx::AdKconfUtil::dynamicShareControl();
    universe_adx_data_.RemoveInMemory(key_name);
    kuaishou::ad::BidUniverseAdx& bid_adx = *universe_adx_data_.MutableValue(key_name);
    bid_adx.set_last_yearday(update_bd.yearday);
    bid_adx.set_kuaishou_total_cost(
      bid_adx.kuaishou_total_cost() + dynamic_share_control_data_.kuaishou_total_cost());
    bid_adx.set_media_total_cost(
      bid_adx.media_total_cost() + dynamic_share_control_data_.media_total_cost());
    // 优先用不截断的高精度字段来计算后验分成率
    bid_adx.set_media_total_cost_precise(
      bid_adx.media_total_cost_precise() + dynamic_share_control_data_.media_total_cost_precise());
    dynamic_share_control_data_.Clear();
    dynamic_share_control_data_.set_last_yearday(update_bd.yearday);
    // 有效期一天
    if (!universe_adx_data_.RedisSave(key_name, bid_adx, 86400)) {
      LOG_EVERY_N(INFO, 1) << "UpdateDynamicShareControlData RedisSave failed, debug_string: "
        << bid_adx.ShortDebugString();
    } else {
      // 存入 redis
      kuaishou::ad::BidUniverseAdx& new_bid_adx = *universe_adx_data_.MutableValue(key_name);
      LOG_EVERY_N(INFO, 200) << "UpdateDynamicShareControlData RedisSave "
        << key_name << " last_yearday " << new_bid_adx.last_yearday()
        << " item_imp_cnt_ " << item_imp_cnt_
        << " kuaishou_total_cost " << new_bid_adx.kuaishou_total_cost()
        << " media_total_cost_precise " << new_bid_adx.media_total_cost_precise();
      ks::infra::PerfUtil::IntervalLogStash(new_bid_adx.kuaishou_total_cost(), kPerfutilNamespace,
        "write_dynamic_share_control_kuaishou_cost_new", key_name);
      ks::infra::PerfUtil::IntervalLogStash(new_bid_adx.media_total_cost_precise() * 1000, kPerfutilNamespace,
        "write_dynamic_share_control_media_cost_new", key_name);
    }
  }
}

void PidStrategyAppPrecise::UpdateGlobalData(const AdLogFull& adlog,
                                   BidUniverseAdxGlobalData* global_data) {
  if (global_data == nullptr) {
    return;
  }
  LOG_EVERY_N(INFO, 10000) << "adlog.virtual_cost_total() = " << adlog.virtual_cost_total();
  global_data->set_kuaishou_periodic_cost(global_data->kuaishou_periodic_cost()
                                          + adlog.cost_total() + adlog.virtual_cost_total());
  global_data->set_kuaishou_total_cost(global_data->kuaishou_total_cost()
                                       + adlog.cost_total() + adlog.virtual_cost_total());
  if (adlog.action_type() == "AD_ITEM_IMPRESSION") {
    auto media_cost = std::min(adlog.returned_second_price_precise(), adlog.ecpm_precise());
    LOG_EVERY_N(INFO, 100) << "returned_second_price_precise " << adlog.returned_second_price_precise()
                           << ", ecpm_precise " << adlog.ecpm_precise()
                           << ", media cost " << media_cost;
    global_data->set_item_impression(global_data->item_impression() + 1);
    global_data->set_media_periodic_cost_precise(global_data->media_periodic_cost_precise()
                                         + media_cost);
    // global_data->set_media_total_cost_precise(global_data->media_total_cost_precise()
    //                                      + media_cost);
    global_data->set_periodic_item_impression(global_data->periodic_item_impression() + 1);
  }
  if (adlog.action_type() == "AD_ITEM_CLICK") {
    global_data->set_item_click(global_data->item_click() + 1);
    global_data->set_periodic_item_click(global_data->periodic_item_click() + 1);
  }

  if (global_data->kuaishou_periodic_cost() > 0) {
    global_data->set_periodic_ratio(static_cast<double>(global_data->media_periodic_cost_precise())
                                    / global_data->kuaishou_periodic_cost());
  }

  if (global_data->kuaishou_total_cost() > 0) {
    global_data->set_total_ratio(static_cast<double>(global_data->media_total_cost_precise())
                                    / global_data->kuaishou_total_cost());
  }
}

void PidStrategyAppPrecise::UpdateGlobalDecayData(BidUniverseAdxGlobalData* global_data) {
  auto decay_kuaishou_cost = global_data->kuaishou_decay_cost() * decay_ +
                             global_data->kuaishou_periodic_cost() * (1 - decay_);
  auto decay_media_cost = global_data->media_decay_cost_precise() * decay_ +
                          global_data->media_periodic_cost_precise() * (1 - decay_);
  global_data->set_kuaishou_decay_cost(decay_kuaishou_cost);
  global_data->set_media_decay_cost_precise(decay_media_cost);
  if (global_data->kuaishou_decay_cost() > 0) {
    global_data->set_decay_ratio(static_cast<double>(global_data->media_decay_cost_precise())
                                 / global_data->kuaishou_decay_cost());
  } else {
    // 给一个较大值用于 pid 调节
    global_data->set_decay_ratio(20);
  }
}

void PidStrategyAppPrecise::ResetGlobalPeriodicData(BidUniverseAdxGlobalData* global_data) {
  LOG_EVERY_N(INFO, 100) << "BidUniverseAdxApp Begin ResetGlobalPeriodicData app_id_key_: " << app_id_key_
                         <<", debugstring: " << global_data->ShortDebugString();
  global_data->set_kuaishou_periodic_cost(0);
  global_data->set_media_periodic_cost(0);
  global_data->set_media_periodic_cost_precise(0.0);
  global_data->set_periodic_item_impression(0);
  global_data->set_periodic_item_click(0);
}

REGISTER_CLASS(BidStrategyBase, PidStrategyAppPrecise);

}  // namespace bid_server
}  // namespace ks
