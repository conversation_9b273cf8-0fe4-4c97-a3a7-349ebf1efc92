#include "teams/ad/bid_server/framework/utils/common_util.h"

#include <memory>
#include <algorithm>
#include <set>
#include <string>
#include <vector>
#include <utility>
#include "teams/ad/bid_server/base/kconf/kconf.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/bid_server/framework/utils/spdm_switches.h"
#include "teams/ad/bid_server/framework/utils/perf.h"
#include "teams/ad/bid_server/public/base/common.h"

namespace ks {
namespace bid_server {

bool IsInAccExploreStatus(const BidStateInfoPtr& bid_info_ptr, int64_t now_ms) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  int32_t explore_bid_type = static_cast<int32_t>(bid_info_ptr->explore_bid_type);
  int64_t explore_time_period = bid_info_ptr->explore_time_period;
  kuaishou::ad::AdEnum::UnitExploreTimeUnit explore_time_unit = bid_info_ptr->explore_time_unit;
  int32_t explore_budget_status = bid_info_ptr->explore_budget_status;
  int64_t explore_budget_start_time = bid_info_ptr->explore_budget_start_time;
  int64_t explore_hour = 6 * 3600 * 1000;
  if (explore_bid_type == static_cast<int32_t>(kuaishou::ad::AdEnum::EXPLORE_RECOVERY_BID_TYPE)) {
    explore_hour = explore_time_period * 3600 * 1000;
  } else if (explore_bid_type == static_cast<int32_t>(kuaishou::ad::AdEnum::EXPLORE_BUDGET_BID_TYPE)
      && explore_time_period > 0) {
    if (explore_time_unit == kuaishou::ad::AdEnum::EXPLORE_TIME_MINUTE) {
      explore_hour = explore_time_period * 60 * 1000;
    } else if (explore_time_unit == kuaishou::ad::AdEnum::EXPLORE_TIME_HOUR) {
      explore_hour = explore_time_period * 3600 * 1000;
    }
  }
  return (explore_budget_status == 1 && (now_ms - explore_budget_start_time < explore_hour) &&
          (now_ms > explore_budget_start_time));
}

bool IsStorewideRoasAcc(BidStateInfoPtr bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  if (bid_info_ptr->explore_budget_status != 1) {
    return false;
  }
  if (bid_info_ptr->explore_budget_start_time == 0 || bid_info_ptr->explore_budget == 0 ||
      bid_info_ptr->explore_time_period == 0) {
    return false;
  }
  // 全站加速探索放开
  int64_t now_ms = base::GetTimestamp() / 1000;
  if ((bid_info_ptr->scene_oriented_type == 21 || bid_info_ptr->scene_oriented_type == 30) &&
       IsInAccExploreStatus(bid_info_ptr, now_ms)) {
    return true;
  }
  return false;
}

bool IsStorewideRoasAccV2(BidStateInfoPtr bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  if (bid_info_ptr->explore_budget_status != 1) {
    return false;
  }
  if (bid_info_ptr->explore_budget_start_time == 0 || bid_info_ptr->explore_budget == 0 ||
      bid_info_ptr->explore_time_period == 0) {
    return false;
  }
  // explore_put_type 为 2 表示为浅度加速或者素材追投，走原链路；为 3 表示走计划维度加速探索 跳过该部分
  if (bid_info_ptr->explore_put_type != 3) {
    return false;
  }
  // 全站加速探索放开
  int64_t now_ms = base::GetTimestamp() / 1000;
  if ((bid_info_ptr->scene_oriented_type == 21 || bid_info_ptr->scene_oriented_type == 30 ||
      bid_info_ptr->scene_oriented_type == 24 ||
      bid_info_ptr->scene_oriented_type == 38) &&
      IsInAccExploreStatus(bid_info_ptr, now_ms)) {
    return true;
  }
  return false;
}

bool IsStorewideAccFixExp(BidStateInfoPtr bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  return IsStorewideRoasAcc(bid_info_ptr) &&
         (AdBaseKconfUtil::enableFixStorewideRoasAccTail()->IsOnFor(bid_info_ptr->campaign_id));
}

// 该函数与原函数的区别为 在 explore_put_type != 3 时候返回 false
bool IsStorewideAccFixExpV2(BidStateInfoPtr bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  return IsStorewideRoasAccV2(bid_info_ptr);
}

bool IsCostCap(const BidStateInfoPtr& bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  if (bid_info_ptr->speed_type == kuaishou::ad::AdEnum::SPEED_COST_CAP
      || bid_info_ptr->bid_strategy == kuaishou::ad::AdEnum::COST_CAP_BID_STRATEGY) {
    return true;
  }
  return false;
}

bool IsOcpmCostCap(uint64_t unit_id, BidStateInfoPtr bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  if (!AdBaseKconfUtil::enableSkipOcpmCostCap()) {
    bool cost_cap_trans_switch = false;
    const auto fpr_trans_tail = ks::engine_base::AdKconfUtil::fanstopPhotoRoasTransTail();
    const auto fpo_trans_tail = ks::engine_base::AdKconfUtil::fanstopPhotoOrderTransTail();
    if ((fpo_trans_tail != nullptr && fpo_trans_tail->IsOnFor(unit_id)) ||
      (fpr_trans_tail != nullptr && fpr_trans_tail->IsOnFor(unit_id))) {
      cost_cap_trans_switch = true;
    }
    if ((bid_info_ptr->speed_type != kuaishou::ad::AdEnum::SPEED_COST_CAP
        || (cost_cap_trans_switch
            && bid_info_ptr->bid_strategy != kuaishou::ad::AdEnum::COST_CAP_BID_STRATEGY))
        && !IsCboCostCap(bid_info_ptr->unit_id, bid_info_ptr)) {
      return false;
    }
    return true;
  } else {
    if (IsCboCostCap(bid_info_ptr->unit_id, bid_info_ptr)) {
      return true;
    }
    if (bid_info_ptr->speed_type == kuaishou::ad::AdEnum::SPEED_COST_CAP) {
      return true;
    }
    bool cost_cap_trans_switch = false;
    const auto fpr_trans_tail = ks::engine_base::AdKconfUtil::fanstopPhotoRoasTransTail();
    const auto fpo_trans_tail = ks::engine_base::AdKconfUtil::fanstopPhotoOrderTransTail();
    if ((fpo_trans_tail != nullptr && fpo_trans_tail->IsOnFor(unit_id)) ||
      (fpr_trans_tail != nullptr && fpr_trans_tail->IsOnFor(unit_id))) {
      cost_cap_trans_switch = true;
    }
    if (cost_cap_trans_switch && bid_info_ptr->bid_strategy == kuaishou::ad::AdEnum::COST_CAP_BID_STRATEGY) {
      return true;
    }
    // 控成本 roas 切 costcap 白名单
    if (IsOcpmLiveRoas2CostCap(unit_id, bid_info_ptr)) {
      return true;
    }
    if (IsOcpmPhotoRoas2Costcap(unit_id, bid_info_ptr)) {
      return true;
    }
    return false;
  }
}

bool IsOcpmPhotoRoas2Costcap(uint64_t unit_id, const BidStateInfoPtr& bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  const auto ocpm_photo_roas_2_costcap = AdBaseKconfUtil::ocpmPhotoRoas2CostCapUnitTail();
  if (bid_info_ptr->account_type == kuaishou::ad::AdEnum::ACCOUNT_ESP
      && bid_info_ptr->speed_type == kuaishou::ad::AdEnum::SPEED_FAST
      && bid_info_ptr->campaign_type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE
      && bid_info_ptr->ocpx_action_type == AdActionType::AD_MERCHANT_ROAS
      && ocpm_photo_roas_2_costcap
      && ocpm_photo_roas_2_costcap->IsOnFor(unit_id)) {
    return true;
  }
  return false;
}

bool IsOcpmLiveRoas2CostCap(uint64_t unit_id, const BidStateInfoPtr& bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  const auto budget_invalid_users = AdBaseKconfUtil::espLiveOcpmBudgetInvalidUserList();
  const auto ocpm_live_2_costcap = AdBaseKconfUtil::ocpmLiveRoas2CostCapUnitTail();

  auto is_hit_budget_invalid = budget_invalid_users && budget_invalid_users->count(bid_info_ptr->user_id) > 0;
  auto is_hit_unit_tail = ocpm_live_2_costcap && ocpm_live_2_costcap->IsOnFor(unit_id);
  if (bid_info_ptr->account_type == kuaishou::ad::AdEnum::ACCOUNT_ESP &&
      bid_info_ptr->speed_type == kuaishou::ad::AdEnum::SPEED_FAST &&
      bid_info_ptr->campaign_type == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE &&
      (bid_info_ptr->ocpx_action_type == AdActionType::AD_MERCHANT_ROAS ||
       bid_info_ptr->ocpx_action_type == AdActionType::EVENT_ORDER_PAIED) &&
      is_hit_budget_invalid && is_hit_unit_tail) {
    return true;
  }
  return false;
}

bool IsHost(const BidStateInfoPtr& bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  // 商品托管 && 商品全站
  if (bid_info_ptr->campaign_type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
      (bid_info_ptr->scene_oriented_type == 20 || bid_info_ptr->scene_oriented_type == 24 ||
       bid_info_ptr->scene_oriented_type == 26 || bid_info_ptr->scene_oriented_type == 34 ||
       (SPDM_enableLSPHosting() && bid_info_ptr->scene_oriented_type == 36) ||
       (bid_info_ptr->scene_oriented_type == 38))) {
    return true;
  }
  auto mobile_hosting_tail_ = ks::engine_base::AdKconfUtil::mobileHostingCampaignBidTail();
  if (bid_info_ptr->scene_oriented_type == 29 &&
      (bid_info_ptr->ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
      bid_info_ptr->ocpx_action_type == kuaishou::ad::AD_FANS_TOP_ROI) &&
      (bid_info_ptr->bid_strategy == kuaishou::ad::AdEnum::COST_CAP_BID_STRATEGY ||
      bid_info_ptr->bid_strategy == kuaishou::ad::AdEnum::CUSTOM_BID_STRATEGY) &&
      mobile_hosting_tail_ && mobile_hosting_tail_->count(bid_info_ptr->campaign_id % 10) > 0) {
    falcon::Inc("bid_server_merchant.mobile_hosting_campaign_bid");
    return true;
  }
  bool is_roas_v3 = bid_info_ptr->ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS;
  // 移动三期新口径
  if ((bid_info_ptr->scene_oriented_type == 20 || bid_info_ptr->scene_oriented_type == 23 ||
      bid_info_ptr->scene_oriented_type == 29 ||
      (bid_info_ptr->scene_oriented_type == 38)) &&
      bid_info_ptr->campaign_charge_mode == kuaishou::ad::AdEnum::ORDER &&
      (bid_info_ptr->ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
      bid_info_ptr->ocpx_action_type == kuaishou::ad::AD_FANS_TOP_ROI || is_roas_v3) &&
      (bid_info_ptr->speed_type == kuaishou::ad::AdEnum::SPEED_FAST ||
      bid_info_ptr->speed_type == kuaishou::ad::AdEnum::SPEED_COST_CAP) &&
      mobile_hosting_tail_ && mobile_hosting_tail_->count(bid_info_ptr->campaign_id % 10) > 0) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.bid_server", "mobile_v3_ocpm");
    return true;
  }
  return false;
}

bool IsCboCostCap(uint64_t unit_id, const BidStateInfoPtr& bid_info_ptr) {
  if (!ks::engine_base::AdKconfUtil::enableCboCostCap()) {
    return false;
  }
  if (bid_info_ptr == nullptr) {
    return false;
  }

  if (IsStorewideAccFixExp(bid_info_ptr)) {
    return false;
  }

  // 全站推广
  if ((bid_info_ptr->budget_smart_allocation == 3 || bid_info_ptr->scene_oriented_type == 30) &&
      (AdBaseKconfUtil::enableCboStoreWide()
      || AdBaseKconfUtil::cboStoreWideCampaignWhiteList()->count(bid_info_ptr->campaign_id) > 0)) {
    return true;
  }
  const auto campaign_set = ks::engine_base::AdKconfUtil::cboCostCapCampaignWhiteList();
  if (campaign_set->count(bid_info_ptr->campaign_id) > 0) {
    falcon::Inc("bid_server_merchant.IsCboCostCap_white");
    return true;
  }
  if (bid_info_ptr->budget_smart_allocation == 1
      && bid_info_ptr->speed_type == kuaishou::ad::AdEnum::SPEED_COST_CAP) {
    falcon::Inc("bid_server_merchant.IsCboCostCap");
    return true;
  }
  // 直播托管默认计划调价
  if (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_LIVE_HOSTING_PROJECT_BASE_FEATURE ||
      (SPDM_enableLSPHosting() &&
       bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_LSP_LIVE_HOSTING_FEATURE)) {
    falcon::Inc("bid_server_merchant.IsCboCostCap_LiveHosting");
    return true;
  }
  // 店铺新客默认计划调价
  if (bid_info_ptr->unit_feature == kuaishou::ad::AdEnum::UNIT_ESP_STORE_NEW_CUSTOMER_HOSTING_FEATURE &&
  bid_info_ptr->campaign_type == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {
    falcon::Inc("bid_server_merchant.IsCboCostCap_LiveNewCustomerHosting");
    return true;
  }
  return false;
}

bool IsCbo(const BidStateInfoPtr& bid_info_ptr) {
  return bid_info_ptr
    && (bid_info_ptr->budget_smart_allocation == 1
        || IsCboCostCap(bid_info_ptr->unit_id, bid_info_ptr)
        || IsCboStoreWide(bid_info_ptr)
        || IsHost(bid_info_ptr));
}

bool IsCboStoreWide(const BidStateInfoPtr& bid_info_ptr) {
  bool is_qiandu = bid_info_ptr &&
      (bid_info_ptr->ocpx_action_type == kuaishou::ad::AD_LIVE_AUDIENCE ||
       bid_info_ptr->ocpx_action_type == kuaishou::ad::AD_MERCHANT_FOLLOW);
  bool is_storewide_switch = bid_info_ptr && !is_qiandu &&
                      (bid_info_ptr->scene_oriented_type == 21 || bid_info_ptr->scene_oriented_type == 30);
  bool is_campaing_acc_explore = IsStorewideAccFixExpV2(bid_info_ptr);
  bool is_rel_qiandu = bid_info_ptr &&
      bid_info_ptr->scene_oriented_type == 21 &&
      (bid_info_ptr->project_ocpx_action_type == kuaishou::ad::AdActionType::AD_LIVE_AUDIENCE ||
      bid_info_ptr->project_ocpx_action_type == kuaishou::ad::AdActionType::AD_MERCHANT_FOLLOW);
  return bid_info_ptr && (!IsStorewideAccFixExp(bid_info_ptr) || is_campaing_acc_explore) &&
        (is_storewide_switch || bid_info_ptr->ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS
        || is_rel_qiandu)
         && (bid_info_ptr->budget_smart_allocation == 3 || bid_info_ptr->scene_oriented_type == 30);
}

bool IsP2lItemType(const std::string& item_type) {
  return item_type == "ITEM_PHOTO_TO_LIVE";
}

bool IsPhotoStoreWideTrans(uint64_t campaign_id, const BidStateInfoPtr& bid_info_ptr) {
  if (bid_info_ptr == nullptr) {
    return false;
  }
  const auto p_store_trans_tail = AdBaseKconfUtil::PhotoStoreWideTransTail();
  if (p_store_trans_tail != nullptr && p_store_trans_tail->IsOnFor(campaign_id)
      && bid_info_ptr->ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS
      && bid_info_ptr->campaign_type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE) {
    return true;
  }
  if (IsPhotoMultiOcpxCampaign(campaign_id, bid_info_ptr)) {
    return true;
  }
  return false;
}

bool IsPhotoStoreWideTrans(const kuaishou::ad::dw::AdLogForAlgo& msg) {
  const auto p_store_trans_tail = AdBaseKconfUtil::PhotoStoreWideTransTail();
  if (p_store_trans_tail != nullptr && p_store_trans_tail->IsOnFor(msg.campaign_id())
      && msg.ocpc_action_type() == "AD_STOREWIDE_ROAS"
      && msg.campaign_type() == "MERCHANT_RECO_PROMOTE") {
    return true;
  }
  auto bid_info_ptr = BID_STATE.GetBidStateInfo(msg.unit_id());
  if (IsPhotoMultiOcpxCampaign(msg.campaign_id(), bid_info_ptr)) {
    return true;
  }
  return false;
}

bool IsPhotoMultiOcpxCampaign(uint64_t campaign_id, const BidStateInfoPtr& bid_info_ptr) {
  if (!bid_info_ptr) {
    return false;
  }
  // 多目标暗投： 计划维度 AD_STOREWIDE_ROAS/AD_MERCHANT_ROAS
  if (bid_info_ptr->campaign_type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
      (bid_info_ptr->project_ocpx_action_type == kuaishou::ad::AdActionType::AD_MERCHANT_ROAS ||
       bid_info_ptr->project_ocpx_action_type == kuaishou::ad::AdActionType::AD_STOREWIDE_ROAS)) {
    // 商品托管
    if (bid_info_ptr->scene_oriented_type == 20 || bid_info_ptr->scene_oriented_type == 26 ||
         (SPDM_enableLSPHosting() && bid_info_ptr->scene_oriented_type == 36)) {
      return true;
    }
    // 商品全站
    if (bid_info_ptr->scene_oriented_type == 24) {
      return true;
    }
    return false;
  }
  return false;
}

bool IsPhotoAccIncrementTrans(const kuaishou::ad::dw::AdLogForAlgo& msg) {
  BidStateInfoPtr bid_info_ptr = nullptr;
  if (BidIndexManager::Instance().GetIndex()) {
    bid_info_ptr = BID_STATE.GetBidStateInfo(msg.unit_id());
  }
  const auto p_store_trans_tail = AdBaseKconfUtil::PhotoAccIncrementTransTail();
  if (bid_info_ptr
      && p_store_trans_tail
      && p_store_trans_tail->IsOnFor(msg.unit_id())
      && bid_info_ptr->explore_put_type == 2
      && bid_info_ptr->explore_bid_type == 1
      && msg.campaign_type() == "MERCHANT_RECO_PROMOTE") {
    return true;
  }
  return false;
}

bool IsFanstopPhotoFollowCostcap(const kuaishou::ad::dw::AdLogForAlgo& msg) {
    kuaishou::ad::AdEnum::AdDspAccountType account_type;
    kuaishou::ad::AdEnum::CampaignType campaign_type;
    kuaishou::ad::AdActionType ocpc_action_type;
    kuaishou::ad::AdEnum::BidType bid_type;
    kuaishou::ad::AdEnum::BidStrategy bid_strategy;

    kuaishou::ad::AdEnum_AdDspAccountType_Parse(msg.account_type(), &account_type);
    kuaishou::ad::AdEnum_CampaignType_Parse(msg.campaign_type(), &campaign_type);
    kuaishou::ad::AdActionType_Parse(msg.ocpc_action_type(), &ocpc_action_type);
    kuaishou::ad::AdEnum_BidType_Parse(msg.bid_type(), &bid_type);
    kuaishou::ad::AdEnum_BidStrategy_Parse(msg.bid_strategy(), &bid_strategy);

    return IsFanstopPhotoFollowCostcap(
          account_type, campaign_type, ocpc_action_type, bid_type, bid_strategy, msg.unit_id());
}
bool IsFanstopPhotoFollowCostcap(
            const kuaishou::ad::AdEnum::AdDspAccountType& account_type,
            const kuaishou::ad::AdEnum::CampaignType& campaign_type,
            const kuaishou::ad::AdActionType& ocpc_action_type,
            const kuaishou::ad::AdEnum::BidType& bid_type,
            const kuaishou::ad::AdEnum::BidStrategy& bid_strategy,
            uint64_t unit_id) {
  const auto follow_trans_tail = AdBaseKconfUtil::fanstopMerchantFollowTail();
  bool is_tail = false;
  if (follow_trans_tail != nullptr && follow_trans_tail->IsOnFor(unit_id)) {
    is_tail = true;
  }
  // 移动端, 排除 ACCOUNT_FANSTOP_V2
  bool is_mobile_esp = account_type == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE;
  // 排除直播
  bool is_fanstop_photo = (campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL ||
                           campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS ||
                           campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_TO_SHOW);
  bool cost_cap_bid = bid_type == kuaishou::ad::AdEnum::OCPM_DSP &&
                      bid_strategy != kuaishou::ad::AdEnum::SMART_BID_STRATEGY &&
                      bid_strategy != kuaishou::ad::AdEnum::COST_CAP_BID_STRATEGY;
  bool is_follow = ocpc_action_type == kuaishou::ad::AdActionType::AD_FANS_TOP_FOLLOW;
  return is_mobile_esp && is_fanstop_photo && cost_cap_bid && is_follow && is_tail;
}

void SetBidFieldByBound(const std::string& level,
  google::protobuf::Message* msg, const base::Json *json) {
  ks::ad_base::Proto::SetFieldBoundByName(msg, json,
      [&](const std::string& field, double prev_value, double set_value){
        perf::Interval(prev_value * 1000, "SetBidBound", level, field, std::to_string(set_value));
  });
}

bool IsExperimentStart(const kuaishou::ad::BidServerPidParamInfo & bid_pid_info,
    const kuaishou::ad::AdActionType& ocpx_action_type,
    const kuaishou::ad::AdEnum::CampaignType& campaign_type,
    const std::string& default_tag) {
  std::string root_tag = GetRootTagByDefaultTag(default_tag);
  std::string ab_key = absl::Substitute("$0_$1",
        root_tag, kuaishou::ad::AdActionType_Name(ocpx_action_type));
  auto kconf_ab = engine_base::AdKconfUtil::bidServerMerchantGroupTagABConf();
  const auto& kconf_ab_data = kconf_ab->data();
  static ks::ad_base::TargetKeyConvertor str_2_int64_;
  int64_t product_id = abs(str_2_int64_(bid_pid_info.product_name()));
  bool is_config_enabled = kconf_ab_data.IsExpEnabled(
                  ab_key,
                  bid_pid_info.account_id(),
                  campaign_type,
                  product_id);
  int64_t exp_start_ts = kconf_ab_data.GetABStartTime(ab_key);
  int64_t now_time = TimeUtil::Instance().GetTimestamp() / 1000000;
  bool result = exp_start_ts < now_time && is_config_enabled;
  return result;
}
}  // namespace bid_server
}  // namespace ks

