--log_dir=../log/
--asynclogtofile=true
--dynamic_json_config_filename=../config/dynamic_json_config.json

# zookeeper 相关
--zk_hosts_yz=config.zk.cluster.yz:2181
--zk_hosts_zw=config.zk.cluster.zw:2181

--infra_location_is_search_redis=true
--infra_location_is_find_neighbors=true

--web_server_port=20208

# ------ 重要的插件配置 --------
--business=account
--strategy=StrategyRouter
--thread=AccountTriggerConsumer,AccountRollbackTrigger,FanstopLowestCostSupervisor,BrandIndexTrigger,DiffBackUpTrigger,NodiffMessageLoad,InnerHashStatus

# ------开关--------
--adlogforalgo_sharding_by=account_id
--enable_grid_sdk_cache=true
--perf_table_lite_caller=true
# 打开P2P订阅模式
--enable_p2p_subscriber=true

# 简版索引指定 config key
--table_lite_conf_key=ad.table_lite.bid_server_tableLiteConfig

# perf name space
--perf_name_space=ad.bid_server_account

--enable_bid_no_diff_switch=false
