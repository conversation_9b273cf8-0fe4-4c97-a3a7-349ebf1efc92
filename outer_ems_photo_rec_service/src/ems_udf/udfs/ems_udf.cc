#include "teams/ad/outer_ems_photo_rec_service/src/ems_udf/udfs/ems_udf.h"

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include <sstream>

#include "teams/ad/outer_ems_photo_rec_service/src/ems_udf/src/ems_item_udf_mgr.h"
#include "teams/ad/outer_ems_photo_rec_service/src/kconf/kconf.h"

#include "teams/ad/outer_ems_photo_rec_service/src/proto/photo_service.pb.h"
#include "teams/ad/ad_proto/maven/video_projection.pb.h"
#include "teams/ad/ad_proto/maven/aigc/service.pb.h"
#include "teams/ad/ad_proto/maven/aigc/material.pb.h"

#include "serving_base/jansson/json.h"
#include "glog/logging.h"
#include "base/encoding/base64.h"

using kuaishou::ad::creative::center::aigc::ExtJsonKey;

namespace ks {
namespace platform {

EmsUdfItemMap ModelPredictJsonResultParseUdf(const EmsUdfParam& src) {
  const std::string& json_value = std::get<std::string>(src);
  EmsUdfItemMap parse_result;
  json_t *json_ptr = base::StringToJson(json_value);
  if (nullptr == json_ptr) {
    return parse_result;
  }
  base::Json json(json_ptr);

  if (!json.IsObject()) {
    return parse_result;
  }
  base::Json* photo_array = json.Get("photos");
  if (nullptr == photo_array || !photo_array->IsArray()) {
    return parse_result;
  }
  parse_result.reserve(photo_array->size());
  for (const auto* item_json : photo_array->array()) {
    if (item_json == nullptr || !item_json->IsObject()) {
      continue;
    }
    const int64_t& photo_id = item_json->GetInt("photo_id", 0);
    if (photo_id == 0) {
      LOG(WARNING) << "ModelPredictJsonResultParseUdf, photo_id is 0!!!";
      continue;
    }
    const int64_t& selected_creative_num =
        static_cast<int64_t>(item_json->GetNumber("selected_creative_num", 0.0));
    const int64_t& photo_user_id = item_json->GetInt("photo_user_id", 0);
    const int64_t& photo_source = item_json->GetInt("photo_source", 0);
    const double& reward = item_json->GetNumber("reward", 0.0);

    EmsUdfItem item;
    item.AppendItemAttr("photo_quota", selected_creative_num);
    item.AppendItemAttr("photo_user_id", photo_user_id);
    item.AppendItemAttr("photo_source", photo_source);
    item.AppendItemAttr("photo_reward", reward);
    item.AppendItemAttr("photo_id", photo_id);

    LOG(INFO) << "ModelPredictJsonResultParseUdf_ParseResult. item_key: " << photo_id
              << ", photo_quota: " << selected_creative_num
              << ", photo_user_id: " << photo_user_id
              << ", photo_source: " << photo_source
              << ", reward: " << reward;

    parse_result.insert(std::make_pair(photo_id, item));
  }
  return parse_result;
}

EmsUdfItemMap ModelPredictNewPhotoJsonResultParseUdf(const EmsUdfParam& src) {
  const std::string& json_value = std::get<std::string>(src);
  EmsUdfItemMap parse_result;
  json_t *json_ptr = base::StringToJson(json_value);
  if (nullptr == json_ptr) {
    return parse_result;
  }
  base::Json json(json_ptr);

  if (!json.IsObject()) {
    return parse_result;
  }
  base::Json* photo_array = json.Get("photos");
  if (nullptr == photo_array || !photo_array->IsArray()) {
    return parse_result;
  }
  parse_result.reserve(photo_array->size());
  for (const auto* item_json : photo_array->array()) {
    if (item_json == nullptr || !item_json->IsObject()) {
      continue;
    }
    const int64_t& photo_id = item_json->GetInt("photo_id", 0);
    if (photo_id == 0) {
      LOG(WARNING) << "ModelPredictNewPhotoJsonResultParseUdf, photo_id is 0!!!";
      continue;
    }
    const int64_t& photo_user_id = item_json->GetInt("photo_user_id", 0);
    const int64_t& photo_source = item_json->GetInt("photo_source", 0);

    auto& kconf_pb = AdKconfUtil::AdSmartUaxModelDecisionKconfConfig()->data();
    // 计算 quota
    int photo_quota = 1;
    if (photo_source == 16) {
        photo_quota = kconf_pb.defaltaigcnewphotoquota();
    } else {
        photo_quota = kconf_pb.defaltnewphotoquota();
    }

    EmsUdfItem item;
    item.AppendItemAttr("photo_quota", photo_quota);
    item.AppendItemAttr("photo_user_id", photo_user_id);
    item.AppendItemAttr("photo_source", photo_source);
    item.AppendItemAttr("photo_id", photo_id);

    LOG(INFO) << "ModelPredictNewPhotoJsonResultParseUdf_ParseResult. item_key: " << photo_id
              << ", photo_quota: " << photo_quota
              << ", photo_user_id: " << photo_user_id
              << ", photo_source: " << photo_source;

    parse_result.insert(std::make_pair(photo_id, item));
  }
  return parse_result;
}

EmsUdfItemMap GetByPhotoIdsResponseParserUdf(const EmsUdfParam& src) {
  EmsUdfItemMap result;
  const google::protobuf::Message* proto_msg_ptr = std::get<const google::protobuf::Message*>(src);
  if (nullptr == proto_msg_ptr) {
    return result;
  }
  const ::kuaishou::negative::PhotoMapResponse* response_ptr =
      dynamic_cast<const ::kuaishou::negative::PhotoMapResponse*>(proto_msg_ptr);
  if (nullptr == response_ptr) {
    return result;
  }
  // 解析 result
  result.reserve(response_ptr->photos_size());
  for (const auto& [photo_id, photo_info] : response_ptr->photos()) {
    const std::string& ext_params2 = photo_info.ext_params2();
    base::Json ext_params2_json(base::StringToJson(ext_params2));
    auto* mezzanine_info = ext_params2_json.Get("mezzanineInfo");
    if (nullptr == mezzanine_info) {
      continue;
    }
    const std::string pmm_ext_params = photo_info.pmm().ext_params();
    base::Json pmm_ext_params_json(base::StringToJson(pmm_ext_params));
    int width = pmm_ext_params_json.GetInt("w", 0);
    int height = pmm_ext_params_json.GetInt("h", 0);
    if (width == 0 || height == 0) {
      continue;
    }
    const std::string& video_id = mezzanine_info->GetString("videoId", "");
    EmsUdfItem item;
    item.AppendItemAttr("cover_width", width);
    item.AppendItemAttr("cover_height", height);
    item.AppendItemAttr("video_id", video_id);
    // 标记该 photo 是否解析 response 数据成功
    item.AppendItemAttr("GetByPhotoIdsResponseParserUdf_succ", true);
    result.insert(std::make_pair(photo_id, item));
  }
  return result;
}

EmsUdfItemMap MixAssetMetaResponseParserUdf(const EmsUdfParam& src) {
  EmsUdfItemMap result;
  const google::protobuf::Message* proto_msg_ptr = std::get<const google::protobuf::Message*>(src);
  if (nullptr == proto_msg_ptr) {
    return result;
  }
  const ::kuaishou::video::projection::MixAssetMetaResponse* response_ptr =
      dynamic_cast<const ::kuaishou::video::projection::MixAssetMetaResponse*>(proto_msg_ptr);
  if (nullptr == response_ptr) {
    return result;
  }
  // 解析 result
  for (const auto& [photo_id, mix_asset_meta_item] : response_ptr->mix_asset_meta()) {
    for (const auto& cover_image_info : mix_asset_meta_item.cover_image_info()) {
      if (cover_image_info.cover_name() != "DEFAULT_JPG") {
        continue;
      }
      if (cover_image_info.cdn_url_info_size() <= 0 || cover_image_info.cdn_url_info(0).url().empty()) {
        continue;
      }
      EmsUdfItem item;
      item.AppendItemAttr("cover_url", cover_image_info.cdn_url_info(0).url());
      item.AppendItemAttr("MixAssetMetaResponseParserUdf_succ", true);
      result.insert(std::make_pair(photo_id, item));
      break;
    }
  }
  return result;
}

EmsUdfItemMap QueryMaterialPageResponseParserUdf(const EmsUdfParam& src) {
  EmsUdfItemMap result;
  const google::protobuf::Message* proto_msg_ptr = std::get<const google::protobuf::Message*>(src);
  if (nullptr == proto_msg_ptr) {
    return result;
  }
  const ::kuaishou::ad::creative::center::aigc::QueryMaterialPageResponse* response_ptr =
      dynamic_cast<const ::kuaishou::ad::creative::center::aigc::QueryMaterialPageResponse*>(proto_msg_ptr);
  if (nullptr == response_ptr) {
    return result;
  }
  result.reserve(response_ptr->material_infos_size());
  // 解析 result
  for (const auto& material_info : response_ptr->material_infos()) {
    auto material_base_info = material_info.material_base_info();
    auto photo_id = material_base_info.photo_id();
    EmsUdfItem item;
    item.AppendItemAttr("photo_id", photo_id);
    auto ext_info = material_base_info.ext_info();
    if (ext_info.empty()) {
      continue;
    }
    json_t *json_ptr = base::StringToJson(ext_info);
    if (nullptr == json_ptr) {
      continue;
    }
    base::Json json(json_ptr);
    if (!json.IsObject()) {
      continue;
    }
    auto creatieve_count = json.GetInt(ExtJsonKey_Name(ExtJsonKey::MATERIAL_CREATIVE_COUNT), 0);
    item.AppendItemAttr("creatieve_count", creatieve_count);
    auto arpu = json.GetFloat(ExtJsonKey_Name(ExtJsonKey::MATERIAL_ARPU), 0.0);
    item.AppendItemAttr("arpu", arpu);
    auto hi = json.GetBoolean(ExtJsonKey_Name(ExtJsonKey::HQ_MATERIAL), false);
    auto ne = json.GetBoolean(ExtJsonKey_Name(ExtJsonKey::NEW_MATERIAL), false);

    item.AppendItemAttr("pool_name", hi ? "优质" : ne ? "新" : "");
    auto create_time = material_base_info.create_time();
    auto isSameDay = [](time_t time1, time_t time2) {
      std::tm* tm1 = std::localtime(&time1);
      std::tm* tm2 = std::localtime(&time2);
      if (nullptr == tm1 || nullptr == tm2) {
        return false;
      }
      return (tm1->tm_year == tm2->tm_year) && (tm1->tm_yday == tm2->tm_yday);
    };
    if (isSameDay(create_time, std::time(nullptr))) {
      item.AppendItemAttr("pool_name", "新");
    }
    result.insert(std::make_pair(photo_id, item));
  }
  return result;
}

EmsUdfItemMap PackagePhotoParserUdf(const EmsUdfParam& src) {
  const std::string& json_value = std::get<std::string>(src);
  EmsUdfItemMap parse_result;
  json_t *json_ptr = base::StringToJson(json_value);
  if (nullptr == json_ptr) {
    return parse_result;
  }
  base::Json json(json_ptr);
  if (!json.IsObject()) {
    return parse_result;
  }
  base::Json* photo_id_array = json.Get("photo_ids");
  if (nullptr == photo_id_array || !photo_id_array->IsArray()) {
    return parse_result;
  }
  parse_result.reserve(photo_id_array->size());
  for (const auto* photo_id_json : photo_id_array->array()) {
    if (photo_id_json == nullptr || !photo_id_json->IsObject()) {
      continue;
    }
    const int64_t& photo_id = photo_id_json->GetInt("photo_id", 0);
    EmsUdfItem item;
    item.AppendItemAttr("photo_id", photo_id);
    item.AppendItemAttr("impr", 0);
    item.AppendItemAttr("click", 0);
    item.AppendItemAttr("cost", 0.0);
    item.AppendItemAttr("impr_1day", 0);
    item.AppendItemAttr("cost_1day", 0.0);
    item.AppendItemAttr("conversion", 0);
    item.AppendItemAttr("idx_in_account", 0);
    item.AppendItemAttr("row_num_pvalue", 0);
    item.AppendItemAttr("row_num_cost", 0);
    item.AppendItemAttr("photo_source", 0);
    parse_result.insert(std::make_pair(photo_id, item));
  }
  return parse_result;
}

EmsUdfItemMap SelectedOptimisedPhotoParserUdf(const EmsUdfParam& src) {
  const std::string& json_value = std::get<std::string>(src);
  EmsUdfItemMap parse_result;
  json_t *json_ptr = base::StringToJson(json_value);
  // std::unique_ptr<json_t, decltype(&json_decref)> json_guard(json_ptr, json_decref);
  if (nullptr == json_ptr) {
    return parse_result;
  }
  base::Json json(json_ptr);
  if (!json.IsObject()) {
    return parse_result;
  }
  base::Json* photo_array = json.Get("photos");
  if (nullptr == photo_array || !photo_array->IsArray()) {
    return parse_result;
  }
  parse_result.reserve(photo_array->size());
  for (const auto* item_json : photo_array->array()) {
    if (item_json == nullptr || !item_json->IsObject()) {
      continue;
    }
    const int64_t& photo_id = item_json->GetInt("photo_id", 0);
    if (photo_id == 0) {
      LOG(WARNING) << "SelectedOptimisedPhotoParserUdf, photo_id is 0!!!";
      continue;
    }
    const int64_t& photo_user_id = item_json->GetInt("photo_user_id", 0);
    const int64_t& impr = item_json->GetInt("impr", 0);
    const int64_t& click = item_json->GetInt("click", 0);
    const double& cost = item_json->GetNumber("cost", 0.);
    const int64_t& impr_1day = item_json->GetInt("impr_1day", 0);
    const double& cost_1day = item_json->GetNumber("cost_1day", 0.);
    const int64_t& conversion = item_json->GetInt("conversion", 0);
    const int64_t& idx_in_account = item_json->GetInt("idx_in_account", 0);
    const int64_t& row_num_pvalue = item_json->GetInt("row_num_pvalue", 0);
    const int64_t& row_num_cost = item_json->GetInt("row_num_cost", 0);
    const int64_t& photo_source = item_json->GetInt("photo_source", 0);
    const double& pvalue = item_json->GetNumber("pvalue", 0.);
    const double& cost_rate = item_json->GetNumber("cost_rate", 1.);
    const double& predict_cost_tpm = item_json->GetNumber("predict_cost_tpm", 0.);
    const double& quantity_score = item_json->GetNumber("quantity_score", 0.);
    const double& diverse_score = item_json->GetNumber("diverse_score", 0.);
    const double& explore_score = item_json->GetNumber("explore_score", 0.);
    const double& cost_score = item_json->GetNumber("cost_score", 0.);
    const int64_t& native_strict_status = item_json->GetInt("native_strict_status", 0);
    base::Json* tag_array = item_json->Get("tags");
    std::vector<std::string> tags;
    if (nullptr == tag_array || !tag_array->IsArray()) {
      LOG(WARNING) << "SelectedOptimisedPhotoParserUdf, tags is empty for photo_id: " << photo_id;
    } else {
      tags.reserve(tag_array->size());
      for (const auto* tag_base64 : tag_array->array()) {
        if (tag_base64 == nullptr || !tag_base64->IsString()) {
          continue;
        }
        std::string tag_base64_str = tag_base64->ToString();
        std::string tag;
        if (base::Base64Decode(tag_base64_str, &tag)) {
          tags.emplace_back(std::move(tag));
        }
      }
    }

    EmsUdfItem item;
    item.AppendItemAttr("photo_id", photo_id);
    item.AppendItemAttr("photo_user_id", photo_user_id);
    item.AppendItemAttr("impr", impr);
    item.AppendItemAttr("click", click);
    item.AppendItemAttr("cost", cost);
    item.AppendItemAttr("impr_1day", impr_1day);
    item.AppendItemAttr("cost_1day", cost_1day);
    item.AppendItemAttr("conversion", conversion);
    item.AppendItemAttr("idx_in_account", idx_in_account);
    item.AppendItemAttr("row_num_pvalue", row_num_pvalue);
    item.AppendItemAttr("row_num_cost", row_num_cost);
    item.AppendItemAttr("photo_source", photo_source);
    item.AppendItemAttr("pvalue", pvalue);
    item.AppendItemAttr("cost_rate", cost_rate);
    item.AppendItemAttr("predict_cost_tpm", predict_cost_tpm);
    item.AppendItemAttr("quantity_score", quantity_score);
    item.AppendItemAttr("diverse_score", diverse_score);
    item.AppendItemAttr("explore_score", explore_score);
    item.AppendItemAttr("cost_score", cost_score);
    item.AppendItemAttr("native_strict_status", native_strict_status);
    item.AppendItemAttr("tags", tags);
    LOG(INFO) << "SelectedOptimisedPhotoParserUdf_ParseResult. item_key: " << photo_id
              << ", photo_user_id: " << photo_user_id << ", impr: " << impr
              << ", photo_source: " << photo_source << ", click: " << click;
    parse_result.insert(std::make_pair(photo_id, item));
  }
  return parse_result;
}

EmsUdfItemMap TotalOptimisedPhotoParserUdf(const EmsUdfParam& src) {
  EmsUdfItemMap result;
  return result;
}

EmsUdfItemMap WebcastOptimisedPhotoParserUdf(const EmsUdfParam& src) {
  const std::string& json_value = std::get<std::string>(src);
  EmsUdfItemMap parse_result;
  json_t *json_ptr = base::StringToJson(json_value);
  if (nullptr == json_ptr) {
    return parse_result;
  }
  base::Json json(json_ptr);
  if (!json.IsObject()) {
    return parse_result;
  }
  base::Json* photo_array = json.Get("photos");
  if (nullptr == photo_array || !photo_array->IsArray()) {
    return parse_result;
  }
  parse_result.reserve(photo_array->size());
  for (const auto* item_json : photo_array->array()) {
    if (item_json == nullptr || !item_json->IsObject()) {
      continue;
    }
    const int64_t& photo_id = item_json->GetInt("photo_id", 0);
    if (photo_id == 0) {
      LOG(WARNING) << "WebcastOptimisedPhotoParserUdf, photo_id is 0!!!";
      continue;
    }
    const int64_t& photo_user_id = item_json->GetInt("photo_user_id", 0);
    const int64_t& impr = item_json->GetInt("impr", 0);
    const int64_t& click = item_json->GetInt("click", 0);
    const double& cost = item_json->GetNumber("cost", 0.);
    const int64_t& impr_1day = item_json->GetInt("impr_1day", 0);
    const double& cost_1day = item_json->GetNumber("cost_1day", 0.);
    const int64_t& conversion = item_json->GetInt("conversion", 0);
    const int64_t& idx_in_account = item_json->GetInt("idx_in_account", 0);
    const int64_t& row_num_pvalue = item_json->GetInt("row_num_pvalue", 0);
    const int64_t& row_num_cost = item_json->GetInt("row_num_cost", 0);
    const int64_t& photo_source = item_json->GetInt("photo_source", 0);
    // tags 字段处理
    base::Json* tag_array = item_json->Get("tags");
    std::vector<std::string> tags;
    if (nullptr != tag_array && tag_array->IsArray()) {
      tags.reserve(tag_array->size());
      for (const auto* tag_base64 : tag_array->array()) {
        if (tag_base64 == nullptr || !tag_base64->IsString()) {
          continue;
        }
        std::string tag_base64_str = tag_base64->ToString();
        std::string tag;
        if (base::Base64Decode(tag_base64_str, &tag)) {
          tags.emplace_back(std::move(tag));
        }
      }
    }
    EmsUdfItem item;
    item.AppendItemAttr("photo_id", photo_id);
    item.AppendItemAttr("photo_user_id", photo_user_id);
    item.AppendItemAttr("impr", impr);
    item.AppendItemAttr("click", click);
    item.AppendItemAttr("cost", cost);
    item.AppendItemAttr("impr_1day", impr_1day);
    item.AppendItemAttr("cost_1day", cost_1day);
    item.AppendItemAttr("conversion", conversion);
    item.AppendItemAttr("idx_in_account", idx_in_account);
    item.AppendItemAttr("row_num_pvalue", row_num_pvalue);
    item.AppendItemAttr("row_num_cost", row_num_cost);
    item.AppendItemAttr("photo_source", photo_source);
    item.AppendItemAttr("tags", tags);
    LOG(INFO) << "WebcastOptimisedPhotoParserUdf_ParseResult. item_key: " << photo_id
              << ", photo_user_id: " << photo_user_id << ", impr: " << impr
              << ", photo_source: " << photo_source << ", click: " << click;
    parse_result.insert(std::make_pair(photo_id, item));
  }
  return parse_result;
}

EmsUdfItemMap AigcPhotoListParserUdf(const EmsUdfParam& src) {
  const std::vector<absl::string_view> &photo_id_list_from_redis
    = std::get<std::vector<absl::string_view>>(src);
  EmsUdfItemMap parse_result;
  std::vector<int64_t> photo_ids;
  for (const auto& photo_id_str : photo_id_list_from_redis) {
    std::vector<absl::string_view> ids = absl::StrSplit(photo_id_str, ',');
    for (const auto& id_str : ids) {
      int64_t id;
      if (absl::SimpleAtoi(id_str, &id)) {
        photo_ids.push_back(id);
        EmsUdfItem item;
        item.AppendItemAttr("photo_id", id);
        parse_result.insert(std::make_pair(id, item));
      }
    }
  }
  return parse_result;
}

REGIST_EMS_UDF(ModelPredictJsonResultParseUdf);
REGIST_EMS_UDF(ModelPredictNewPhotoJsonResultParseUdf);
REGIST_EMS_UDF(GetByPhotoIdsResponseParserUdf);
REGIST_EMS_UDF(MixAssetMetaResponseParserUdf);
REGIST_EMS_UDF(PackagePhotoParserUdf);
REGIST_EMS_UDF(SelectedOptimisedPhotoParserUdf);
REGIST_EMS_UDF(TotalOptimisedPhotoParserUdf);
REGIST_EMS_UDF(WebcastOptimisedPhotoParserUdf);
REGIST_EMS_UDF(QueryMaterialPageResponseParserUdf);
REGIST_EMS_UDF(AigcPhotoListParserUdf);
}  // namespace platform
}  // namespace ks
