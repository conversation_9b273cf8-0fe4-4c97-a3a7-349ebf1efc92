#!/usr/bin/env python3
# coding=utf-8

import os
import sys

from dragonfly.common_leaf_dsl import LeafFlow
from base_flow.photo_retrieve_base_flow import BasePhotoRetrieveFlow
from ad_dragonfly.outer.ad_ems_outer_mixin import AdEmsOuterApiMixin
from dragonfly.modular.module import module
from dragonfly.common_leaf_dsl import current_flow
from dragonfly.matx.dragonfly_context import DragonflyContext
from ad_dragonfly.base.utils.utils_mixin import UtilsApiMixin
from dragonfly.modular.data_manager import DataManager, data_manager, ab_param as ab, kconf_param as kconf

class AigcSeriesIdRetrieveFlowPythonFunctionSet:
  def __init__(self) -> None:
    pass

  def retrieve_admit_function(self, ctx: DragonflyContext) -> None:
    series_id = ctx.GetInt(b"series_id", 0)
    if series_id > 0:
      ctx.SetInt(b"retrieve_admit", 1)
    else:
      ctx.SetInt(b"retrieve_admit", 0)

## 系统补量结果召回 子流程，继承 BasePhotoRetrieveFlow 并重写相关子流程
class AigcSeriesIdRetrieveFlow(BasePhotoRetrieveFlow, AdEmsOuterApiMixin, UtilsApiMixin):
  def __init__(self, name, table_name):
    super().__init__(name, table_name)

  @module()
  def _retrieve_admit(self):
    with data_manager:
      current_flow()  \
      .enrich_attr_by_py(
        function_set = AigcSeriesIdRetrieveFlowPythonFunctionSet,
        py_function = AigcSeriesIdRetrieveFlowPythonFunctionSet.retrieve_admit_function
      )
    return self

  @module()
  def _retrieve(self):
    with data_manager:
      current_flow()  \
      .get_common_attr_from_redis(
        cluster_name = "adNieuwlandPrivateMatchDerivePhoto",
        is_async = False,
        redis_params = [
          {
            "key_prefix": "dsp_sketch_",
            "redis_key": "{{series_id}}",
            "redis_value_type": "string",
            "output_attr_name": "aigc_series_photo_str",
            "output_attr_type": "string"
          }
        ]
      ) \
      .split_string(
        input_common_attr = "aigc_series_photo_str",
        output_common_attr = "aigc_series_photo_list",
        delimiters=",",
      ) \
      .str_format(
        format_string = "%ld_%ld",
        input_attrs = ["series_id", "user_id"],
        output_attr = "series_user_key",
        fill_default_val = True
      ) \
      .get_common_attr_from_redis(
        cluster_name = "adNieuwlandPrivateMatchDerivePhoto",
        is_async = False,
        redis_params = [
          {
            "key_prefix": "dsp_sketch_",
            "redis_key": "{{series_user_key}}",
            "redis_value_type": "string",
            "output_attr_name": "aigc_series_user_photo_str",
            "output_attr_type": "string"
          }
        ]
      ) \
      .split_string(
        input_common_attr = "aigc_series_user_photo_str",
        output_common_attr = "aigc_series_user_photo_list",
        delimiters=",",
      ) \
      .retrieve_item_from_common_attr_value_by_udf(
        input_common_attr = "aigc_series_photo_list",
        udf_name = "AigcPhotoListParserUdf",
        reason = 12,
        input_common_attr_type = 3,
      ) \
      .retrieve_item_from_common_attr_value_by_udf(
        input_common_attr = "aigc_series_user_photo_list",
        udf_name = "AigcPhotoListParserUdf",
        reason = 12,
        input_common_attr_type = 3,
      )
    return self

  @module()
  def _rule_filter(self):
    ## 规则过滤
    return self

  @module()
  def _sort(self):
    ## 排序
    return self

  @module()
  def _truncate(self):
    return self