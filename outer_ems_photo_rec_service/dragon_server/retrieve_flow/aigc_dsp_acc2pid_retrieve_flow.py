#!/usr/bin/env python3
# coding=utf-8

import os
import sys

from dragonfly.common_leaf_dsl import LeafFlow
from base_flow.photo_retrieve_base_flow import BasePhotoRetrieveFlow
from ad_dragonfly.outer.ad_ems_outer_mixin import AdEmsOuterApiMixin
from dragonfly.modular.module import module
from dragonfly.common_leaf_dsl import current_flow
from dragonfly.matx.dragonfly_context import DragonflyContext
from ad_dragonfly.base.utils.utils_mixin import UtilsApiMixin
from dragonfly.modular.data_manager import DataManager, data_manager, ab_param as ab, kconf_param as kconf

class AigcDspAcc2PidRetrieveFlowPythonFunctionSet:
  def __init__(self) -> None:
    pass

  def retrieve_admit_function(self, ctx: DragonflyContext) -> None:
    series_id = ctx.GetInt(b"series_id", 0)
    in_sdpa_tag_account_list = ctx.GetInt(b"in_sdpa_tag_account_list", 0)
    in_sdpa_tag_product_list = ctx.GetInt(b"in_sdpa_tag_product_list", 0)
    in_sdpa_tag_second_industry_list = ctx.GetInt(b"in_sdpa_tag_second_industry_list", 0)
    in_sdpa_tag_license_list = ctx.GetInt(b"in_sdpa_tag_license_list", 0)
    in_aigc_purchase_account_list = ctx.GetInt(b"in_aigc_purchase_account_list", 0)
    if series_id > 0 or in_sdpa_tag_account_list == 1 or in_sdpa_tag_product_list == 1 or in_sdpa_tag_second_industry_list == 1 \
        or in_sdpa_tag_license_list == 1 or in_aigc_purchase_account_list == 1:
      ctx.SetInt(b"retrieve_admit", 0)
    else:
      ctx.SetInt(b"retrieve_admit", 1)

## 系统补量结果召回 子流程，继承 BasePhotoRetrieveFlow 并重写相关子流程
class AigcDspAcc2PidRetrieveFlow(BasePhotoRetrieveFlow, AdEmsOuterApiMixin, UtilsApiMixin):
  def __init__(self, name, table_name):
    super().__init__(name, table_name)

  @module()
  def _retrieve_admit(self):
    with data_manager:
      current_flow()  \
      .enrich_attr_by_py(
        function_set = AigcDspAcc2PidRetrieveFlowPythonFunctionSet,
        py_function = AigcDspAcc2PidRetrieveFlowPythonFunctionSet.retrieve_admit_function
      )
    return self

  @module()
  def _retrieve(self):
    with data_manager:
      current_flow()  \
      .get_common_attr_from_redis(
        cluster_name = "adNieuwlandPrivateMatchDerivePhoto",
        is_async = False,
        redis_params = [
          {
            "key_prefix": "dsp_acc2pid_",
            "redis_key": "{{account_id}}",
            "redis_value_type": "string",
            "output_attr_name": "aigc_account_photo_str",
            "output_attr_type": "string"
          }
        ]
      ) \
      .split_string(
        input_common_attr = "aigc_account_photo_str",
        output_common_attr = "aigc_account_photo_list",
        delimiters=",",
      ) \
      .retrieve_item_from_common_attr_value_by_udf(
        input_common_attr = "aigc_account_photo_list",
        udf_name = "AigcPhotoListParserUdf",
        reason = 13,
        input_common_attr_type = 3,
      )
    return self

  @module()
  def _rule_filter(self):
    ## 规则过滤
    return self

  @module()
  def _sort(self):
    ## 排序
    return self

  @module()
  def _truncate(self):
    with data_manager:
      current_flow()  \
      .shuffle()  \
      .truncate(
        size_limit = "{{aigc_maxMontageCount}}",
      )
    return self