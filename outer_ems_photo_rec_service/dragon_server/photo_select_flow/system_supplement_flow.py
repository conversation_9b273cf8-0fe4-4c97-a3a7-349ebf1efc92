#!/usr/bin/env python3
# coding=utf-8

import os
import sys
import math

code_path=os.path.abspath('../../../..')
sys.path.append(code_path)

from dragonfly.common_leaf_dsl import LeafFlow
from base_flow.photo_select_base_flow import BasePhotoSelectFlow
from ad_dragonfly.outer.ad_ems_outer_mixin import AdEmsOuterApiMixin
from ad_dragonfly.base.utils.utils_mixin import UtilsApiMixin
from retrieve_flow.account_photo_retrieve_flow import AccountPhotoRetrieveFlow
from retrieve_flow.native_account_photo_retrieve_flow import NativeAccountPhotoRetrieveFlow
from retrieve_flow.guaranteed_photo_results_retrieve_flow import GuaranteedPhotoResultsRetrieveFlow
from retrieve_flow.racing_photo_results_retrieve_flow import RacingPhotoResultsRetrieveFlow
from retrieve_flow.package_photo_retrieve_flow import PackagePhotoRetrieveFlow
from retrieve_flow.user_id_photo_retrieve_flow import UserIdPhotoRetrieveFlow
from retrieve_flow.webcast_cross_account_photo_retrieve_flow import Webcast<PERSON>ross<PERSON>ccountPhotoRetrieveFlow
from retrieve_flow.webcast_author_id_retrieve_flow import Webcast<PERSON>uthorIdRetrieve<PERSON>low
from retrieve_flow.aigc_book_id_retrieve_flow import AigcBookIdRetrieveFlow
from retrieve_flow.aigc_dsp_acc2pid_retrieve_flow import AigcDspAcc2PidRetrieveFlow
from retrieve_flow.aigc_unit_sdpa_retrieve_flow import AigcUnitSdpaRetrieveFlow
from retrieve_flow.aigc_series_id_retrieve_flow import AigcSeriesIdRetrieveFlow
from retrieve_flow.aigc_dsp_purchase_acc2pid_retrieve_flow import AigcDspPurchaseAcc2PidRetrieveFlow
from retrieve_flow.aigc_dsp_purchase2_acc2pid_retrieve_flow import AigcDspPurchase2Acc2PidRetrieveFlow
from retrieve_flow.aigc_tmall_account_retrieve_flow import AigcTmallAccountRetrieveFlow
from dragonfly.modular.module import module
from dragonfly.common_leaf_dsl import current_flow
from dragonfly.matx.dragonfly_context import DragonflyContext
from dragonfly.modular.data_manager import DataManager, data_manager, ab_param as ab, kconf_param as kconf
from dragonfly.decorators import async_mix
from teams.ad.ad_feature_index.remote_table_sdk.remote_table_mixin import RemoteAdTableMixin

feature_multi_table_config = {
  "remote_service": "ad-feature-proxy",
  "feature_use_cache": True,
  "request_tables": {
    "account_table": {
      "item_tables": ["account_table"],
      "remote_table_relation": {
        "ad_dsp_account": {
          "local_field_alias": {
            "user_id": "user_id",
            "product_name": "product_name",
            "first_industry_name_v6_1": "first_industry_name",
            "second_industry_name_v6_1": "second_industry_name",
            "corporation_name": "corporation_name",
            "agent_id": "agent_id",
            "license_no": "license_no"
          },
          "item_table_key_attr": "account_id"
        }
      },
      "local_field_type": {
        "user_id": "int",
        "product_name": "string",
        "first_industry_name": "string",
        "second_industry_name": "string",
        "corporation_name": "string",
        "agent_id": "int",
        "license_no": "string",
      }
    }
  }
}
aigc_table_name = "aigc_table"
item_table_name = "item_table"
guaranteed_table_name = "guaranteed_table"
racing_table_name = "racing_table"
main_table_name = ""
aigc_exploit_table_name = 'aigc_exploit_table'
aigc_new_photo_table_name= 'aigc_new_photo_table'
aigc_explore_table_name = 'aigc_explore_table'


logic_item_table_name = "logic_" + item_table_name
logic_racing_table_name = "logic_" + racing_table_name
logic_guaranteed_table_name = "logic_" + guaranteed_table_name
logic_aigc_table_name = "logic_" + aigc_table_name
logic_aigc_exploit_table_name = "logic_" + aigc_exploit_table_name
logic_aigc_new_photo_table_name = "logic_" + aigc_new_photo_table_name
logic_aigc_explore_table_name = "logic_" + aigc_explore_table_name

class SystemSupplementPhotoSelectFlowPythonFunctionSet:
  def __init__(self) -> None:
    pass
  def flow_admit_function(self, ctx: DragonflyContext) -> None:
    account_in_black_list = ctx.GetInt(b"account_in_black_list", 0)
    if account_in_black_list == 1:
      ctx.SetInt(b"flow_admit", 0)
    else:
      ctx.SetInt(b"flow_admit", 1)

  def in_aigc_photo_blacklist_function(self, ctx: DragonflyContext) -> None:
    campaign_type = ctx.GetInt(b"campaign_type", 0)
    book_id = ctx.GetInt(b"book_id", 0)
    series_id = ctx.GetInt(b"series_id", 0)
    first_industry_name = ctx.GetString(b"first_industry_name")
    second_industry_name = ctx.GetString(b"second_industry_name")
    in_aigc_account_blacklist = ctx.GetInt(b"in_aigc_account_blacklist", 0)
    in_aigc_product_name_blacklist = ctx.GetInt(b"in_aigc_product_name_blacklist", 0)
    in_aigc_license_blacklist = ctx.GetInt(b"in_aigc_license_blacklist", 0)
    enable_tag_black_aigc_account = ctx.GetInt(b"enable_tag_black_aigc_account", 0)
    in_tag_black_aigc_license_list = ctx.GetInt(b"in_tag_black_aigc_license_list", 0)
    in_tag_black_aigc_product_list = ctx.GetInt(b"in_tag_black_aigc_product_list", 0)
    in_tmall618_account_whitelist = ctx.GetInt(b"in_tmall618_account_whitelist", 0)
    aigc_first_industry_black_list = ctx.GetStringList(b"aigc_first_industry_black_list")
    aigc_second_industry_medical_white_list = ctx.GetStringList(b"aigc_second_industry_medical_white_list")
    enable_medical_white_list = ctx.GetInt(b"enable_medical_white_list", 0)
    in_medical_account_white_list = ctx.GetInt(b"in_medical_account_white_list", 0)
    if in_aigc_account_blacklist == 1 or in_aigc_product_name_blacklist == 1 or in_aigc_license_blacklist == 1:
      ctx.SetInt(b"in_aigc_photo_blacklist", 1)
    elif (enable_tag_black_aigc_account == 1 or in_tag_black_aigc_license_list == 1 or in_tag_black_aigc_product_list == 1) and in_tmall618_account_whitelist == 0:
      ctx.SetInt(b"in_aigc_photo_blacklist", 1)
    elif campaign_type != 34 and campaign_type != 20 and book_id == 0 and series_id == 0 and in_tmall618_account_whitelist == 0:
      ctx.SetInt(b"in_aigc_photo_blacklist", 1)
    elif first_industry_name in aigc_first_industry_black_list:
      if enable_medical_white_list and in_medical_account_white_list or second_industry_name in aigc_second_industry_medical_white_list:
        ctx.SetInt(b"in_aigc_photo_blacklist", 0)
      else:
        ctx.SetInt(b"in_aigc_photo_blacklist", 1)
    else:
      ctx.SetInt(b"in_aigc_photo_blacklist", 0)
  
  def in_sdpa_tag_function(self, ctx: DragonflyContext) -> None:
    in_sdpa_tag_account_list = ctx.GetInt(b"in_sdpa_tag_account_list", 0)
    in_sdpa_tag_product_list = ctx.GetInt(b"in_sdpa_tag_product_list", 0) 
    in_sdpa_tag_second_industry_list = ctx.GetInt(b"in_sdpa_tag_second_industry_list", 0)
    in_sdpa_tag_license_list = ctx.GetInt(b"in_sdpa_tag_license_list", 0)
    if in_sdpa_tag_account_list == 1 or in_sdpa_tag_product_list == 1 or in_sdpa_tag_second_industry_list == 1 or in_sdpa_tag_license_list == 1:
      ctx.SetInt(b"in_sdpa_tag_list", 1)
    else:
      ctx.SetInt(b"in_sdpa_tag_list", 0)

  def get_prefix_for_item(self, ctx: DragonflyContext) -> None:
    # 返回用于 adNieuwlandPrivateMatchDerivePhoto 的 key 前缀。
    need_creative_type = ctx.GetInt(b"needCreativeType", 0)
    adNieuwlandPrivateMatchDerivePhoto_prefix_setter = ctx.ItemAttrSetter(b"adNieuwlandPrivateMatchDerivePhoto_prefix")

    # item for 循环
    result_size = ctx.GetItemNum()
    for i in range(result_size):
      if need_creative_type == 3:
        adNieuwlandPrivateMatchDerivePhoto_prefix_setter.SetString(i, b"dsp_aigc_programmed_photo_effect_")
      else:
        adNieuwlandPrivateMatchDerivePhoto_prefix_setter.SetString(i, b"dsp_aigc_photo_effect_")

  def get_already_exploit_num(self, ctx: DragonflyContext) -> None:
    s8_getter = ctx.ItemAttrGetter(b"s8")
    predict_cost_quantile = ctx.GetDouble(b"predictCostQuantile", 0)

    # item for 循环
    lowerThresholdCnt = 0
    result_size = ctx.GetItemNum()
    for i in range(result_size):
      need_creative_type = s8_getter.GetDouble(i)
      if need_creative_type <= predict_cost_quantile:
        lowerThresholdCnt += 1
    ctx.SetInt(b"alreadyExploitNum", result_size - lowerThresholdCnt)

## 系统补量素材优选流程定义
class SystemSupplementPhotoSelectFlow(BasePhotoSelectFlow, AdEmsOuterApiMixin, RemoteAdTableMixin, UtilsApiMixin):
  def __init__(self, table_name):
    super().__init__("system_supplement_flow", table_name)
    ## 
    # self.account_key_retrieve_flow = AccountKeyRetrieveFlow("account_key_retrieve_flow", item_table_name)
    # with self.account_key_retrieve_flow, data_manager:
    #   self.account_key_retrieve_flow  \
    #     .photo_retrieval(name="account_key_recall")
    ## 
    self.account_photo_retrieve_flow = AccountPhotoRetrieveFlow("account_photo_retrieve_flow", item_table_name)
    with self.account_photo_retrieve_flow, data_manager:
      self.account_photo_retrieve_flow \
        .photo_retrieval(name="account_photo_recall")
    
    ##
    self.native_account_photo_retrieve_flow = NativeAccountPhotoRetrieveFlow("native_account_photo_retrieve_flow", item_table_name)
    with self.native_account_photo_retrieve_flow, data_manager:
      self.native_account_photo_retrieve_flow \
        .photo_retrieval(name="native_account_photo_recall")

    ##
    self.guaranteed_photo_results_retrieve_flow = GuaranteedPhotoResultsRetrieveFlow("guaranteed_photo_results_retrieve_flow", guaranteed_table_name)
    with self.guaranteed_photo_results_retrieve_flow, data_manager:
      self.guaranteed_photo_results_retrieve_flow \
        .photo_retrieval(name="guaranteed_photo_recall")

    ##
    self.racing_photo_results_retrieve_flow = RacingPhotoResultsRetrieveFlow("racing_photo_results_retrieve_flow", racing_table_name)
    with self.racing_photo_results_retrieve_flow, data_manager:
      self.racing_photo_results_retrieve_flow \
        .photo_retrieval(name="racing_photo_recall")
    
    ##
    self.package_photo_retrieve_flow = PackagePhotoRetrieveFlow("package_photo_retrieve_flow", item_table_name)
    with self.package_photo_retrieve_flow, data_manager:
      self.package_photo_retrieve_flow \
        .photo_retrieval(name="package_photo_recall")

    ##
    self.user_id_photo_retrieve_flow = UserIdPhotoRetrieveFlow("user_id_photo_retrieve_flow", item_table_name)
    with self.user_id_photo_retrieve_flow, data_manager:
      self.user_id_photo_retrieve_flow \
        .photo_retrieval(name="user_id_photo_recall")

    ##
    self.webcast_cross_account_photo_retrieve_flow = WebcastCrossAccountPhotoRetrieveFlow("webcast_cross_account_photo_retrieve_flow", item_table_name)
    with self.webcast_cross_account_photo_retrieve_flow, data_manager:
      self.webcast_cross_account_photo_retrieve_flow \
        .photo_retrieval(name="webcast_cross_account_photo_recall")
    
    ##
    self.webcast_author_id_retrieve_flow = WebcastAuthorIdRetrieveFlow("webcast_author_id_retrieve_flow", item_table_name)
    with self.webcast_author_id_retrieve_flow, data_manager:
      self.webcast_author_id_retrieve_flow \
        .photo_retrieval(name="webcast_author_id_photo_recall")
    
    ## Aigc相关召回流
    ##
    self.aigc_tmall_account_retrieve_flow = AigcTmallAccountRetrieveFlow("aigc_tmall_account_retrieve_flow", aigc_table_name)
    with self.aigc_tmall_account_retrieve_flow, data_manager:
      self.aigc_tmall_account_retrieve_flow \
        .photo_retrieval(name="aigc_tmall_account_photo_recall")
    
    ##
    self.aigc_book_id_retrieve_flow = AigcBookIdRetrieveFlow("aigc_book_id_retrieve_flow", aigc_table_name)
    with self.aigc_book_id_retrieve_flow, data_manager:
      self.aigc_book_id_retrieve_flow \
        .photo_retrieval(name="aigc_book_id_photo_recall")
    
    ##
    self.aigc_unit_sdpa_retrieve_flow = AigcUnitSdpaRetrieveFlow("aigc_unit_sdpa_retrieve_flow", aigc_table_name)
    with self.aigc_unit_sdpa_retrieve_flow, data_manager:
      self.aigc_unit_sdpa_retrieve_flow \
        .photo_retrieval(name="aigc_unit_sdpa_photo_recall")

    ##
    self.aigc_series_id_retrieve_flow = AigcSeriesIdRetrieveFlow("aigc_series_id_retrieve_flow", aigc_table_name)
    with self.aigc_series_id_retrieve_flow, data_manager:
      self.aigc_series_id_retrieve_flow \
        .photo_retrieval(name="aigc_series_id_photo_recall")
    
    ##
    self.aigc_dsp_acc2pid_retrieve_flow = AigcDspAcc2PidRetrieveFlow("aigc_dsp_acc2pid_retrieve_flow", aigc_table_name)
    with self.aigc_dsp_acc2pid_retrieve_flow, data_manager:
      self.aigc_dsp_acc2pid_retrieve_flow \
        .photo_retrieval(name="aigc_dsp_acc2pid_photo_recall")

    ##
    self.aigc_dsp_purchase2_acc2pid_retrieve_flow = AigcDspPurchase2Acc2PidRetrieveFlow("aigc_dsp_purchase2_acc2pid_retrieve_flow", aigc_table_name)
    with self.aigc_dsp_purchase2_acc2pid_retrieve_flow, data_manager:
      self.aigc_dsp_purchase2_acc2pid_retrieve_flow \
        .photo_retrieval(name="aigc_dsp_purchase2_acc2pid_photo_recall")
    
    ##
    self.aigc_dsp_purchase_acc2pid_retrieve_flow = AigcDspPurchaseAcc2PidRetrieveFlow("aigc_dsp_purchase_acc2pid_retrieve_flow", aigc_table_name)
    with self.aigc_dsp_purchase_acc2pid_retrieve_flow, data_manager:
      self.aigc_dsp_purchase_acc2pid_retrieve_flow \
        .photo_retrieval(name="aigc_dsp_purchase_acc2pid_photo_recall")


  @module()
  def _request_admit(self,name):
    self  \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.AdSmartDspAutoBuildEnablePhotoPackage",
          "value_type": "tail_number",
          "lookup_attr": "account_id",
          "output_attr": "enable_photo_package",
          "is_common_attr": True
        }]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.adems.photoExpConf",
            "json_path":  "enable_total_photo_recall",
            "export_common_attr": "enable_total_photo_recall",
            "default_value": 0.0
          }
        ]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.photoRecallAccountTailnumberConfigV1", ## 账户召回尾号配置
          "value_type": "tail_number",
          "lookup_attr": "account_id",
          "output_attr": "enable_photo_recall_account",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.photoRecallAccountTailnumberConfig",
          "value_type": "tail_number",
          "lookup_attr": "account_id",
          "output_attr": "enable_total_photo_recall_account",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.uaxNativeAddPhotosAccountList",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_native_recall_account_white_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.uaxShareAddPhotosAccountList",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_share_uid_recall_account_white_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.exploreGuaranteedAccountTailNumberConfig",
          "value_type": "tail_number",
          "lookup_attr": "account_id",
          "output_attr": "enable_photo_recall_explore_guaranteed",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.exploreRacingAccountTailNumberConfig",
          "value_type": "tail_number",
          "lookup_attr": "account_id",
          "output_attr": "enable_photo_recall_explore_racing",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.dsp2.adLiveFastAddCreativeByCreateForAccount",
          "value_type": "tail_number",
          "lookup_attr": "account_id",
          "output_attr": "enable_photo_recall_webcast_author_account",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.dsp2.adLiveFastAddCreativeByCreate",
          "value_type": "tail_number",
          "lookup_attr": "campaign_id",
          "output_attr": "enable_photo_recall_webcast_author_campaign",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoAccountWhiteList",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_webcast_author_recall_account_white_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoLicenseNoWhiteList",
          "value_type": "set_string",
          "lookup_attr": "license_no",
          "output_attr": "in_webcast_author_recall_license_white_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoProductWhiteList",
          "value_type": "set_string",
          "lookup_attr": "product_name",
          "output_attr": "in_webcast_author_recall_product_white_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoAccountBlackList",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_webcast_author_recall_account_black_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoLicenseNoBlackList",
          "value_type": "set_string",
          "lookup_attr": "license_no",
          "output_attr": "in_webcast_author_recall_license_black_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoProductBlackList",
          "value_type": "set_int64",
          "lookup_attr": "product_name",
          "output_attr": "in_webcast_author_recall_product_black_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.uaxWebcastAddPhotosCampaignList",
          "value_type": "set_int64",
          "lookup_attr": "campaign_id",
          "output_attr": "in_webcast_campaign_white_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.uaxRecallByAccountIdWhiteList",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_webcast_account_white_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.uaxWebcastByAccountBlackList",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "uax_webcast_account_in_blacklist",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.uaxWebcastByProductBlackList",
          "value_type": "set_string",
          "lookup_attr": "product_name",
          "output_attr": "uax_webcast_product_in_blacklist",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.uaxWebcastByFirstIndustryBlackList",
          "value_type": "set_string",
          "lookup_attr": "first_industry_name",
          "output_attr": "uax_webcast_first_industry_in_blacklist",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.uaxWebcastBySecondIndustryBlackList",
          "value_type": "set_string",
          "lookup_attr": "second_industry_name",
          "output_attr": "uax_webcast_second_industry_in_blacklist",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.tmall618AccountWhitelist",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_tmall_618_account_white_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.enableUaxSdpaAccountId",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_sdpa_tag_account_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.enableUaxSdpaProductName",
          "value_type": "set_string",
          "lookup_attr": "product_name",
          "output_attr": "in_sdpa_tag_product_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.enable_sdpa_second_industry_whitelist",
          "value_type": "set_string",
          "lookup_attr": "second_industry_name",
          "output_attr": "in_sdpa_tag_second_industry_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.enableSdpaLicenseNoWhiteList",
          "value_type": "set_string",
          "lookup_attr": "license_no",
          "output_attr": "in_sdpa_tag_license_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.dspAIGCOnlyPurchaseAccountWhitelist",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_aigc_purchase_account_list",
          "is_common_attr": True
        }]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.algorithm.AdSmartPredictCostKconfConfig",
            "json_path":  "campaignTypeList",
            "export_common_attr": "kconf_ad_algorithm_AdSmartPredictCostKconfConfig_campaignTypeList_json"
          }
        ]
      ) \
      .cast_attr_type(
        attr_type_cast_configs=[
          {
            "to_type": "int",
            "from_common_attr": "kconf_ad_algorithm_AdSmartPredictCostKconfConfig_campaignTypeList_json",
            "to_common_attr": "kconf_ad_algorithm_AdSmartPredictCostKconfConfig_campaignTypeList",
          },
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.adems.photoExpConf",
            "json_path":  "enable_order_creative_switch",
            "export_common_attr": "enable_order_creative_switch",
            "default_value": 1
          }
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.adems.photoExpConf",
            "json_path":  "photo_bind_creative_upper_limit",
            "export_common_attr": "photo_bind_creative_upper_limit",
            "default_value": 200.0
          }
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.adems.uaxWebcastDefaultMessage",
            "value_type": "string",
            "export_common_attr": "uax_webcast_default_message",
            "default_value": "推荐观看精彩内容"
          }
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.adems.photoExpConf",
            "json_path": "creative_strategy",
            "export_common_attr": "creative_strategy_json"
          }
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.adems.campaignPhotoTag",
            "json_path": "campaign_tail",
            "export_common_attr": "campaignPhotoTag_campaign_tail_json"
          }
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.adems.campaignPhotoTag",
            "json_path": "unit_tail",
            "export_common_attr": "campaignPhotoTag_unit_tail_json"
          }
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig",
            "json_path": "maxMontageCount",
            "export_common_attr": "aigc_maxMontageCount"
          }
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig",
            "json_path": "maxTemplateCount",
            "export_common_attr": "aigc_maxTemplateCount"
          }
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig",
            "json_path": "maxPurchaseCount",
            "export_common_attr": "aigc_maxPurchaseCount"
          }
        ]
      ) \
      .enrich_attr_by_py(
        function_set = SystemSupplementPhotoSelectFlowPythonFunctionSet,
        py_function = SystemSupplementPhotoSelectFlowPythonFunctionSet.flow_admit_function
      )
    return self


  @module()
  def _prepare(self,name):
    self \
        .lookup_kconf(
          kconf_configs = [{
            "kconf_key": "ad.adems.campaignPhotoTag",
            "value_type": "set_int64",
            "lookup_attr": "campaign_id",
            "output_attr": "enable_feature_index_proxy_account",
            "is_common_attr": True
          }]
        ) \
        .lookup_kconf(
          kconf_configs = [{
            "kconf_key": "ad.adems.enableFeatureIndexProxyAccount",
            "value_type": "set_int64",
            "lookup_attr": "account_id",
            "output_attr": "enable_feature_index_proxy_account",
            "is_common_attr": True
          }]
        ) \
        .process_already_insert_photo_list() \
        .if_ ("enable_feature_index_proxy_account == 1") \
          .retrieve_by_common_attr(
            attr="account_id",
            reason=999,
            item_table="account_table"
          ) \
          .dispatch_common_attr(
            from_common_attr = "account_id",
            to_item_attr = "account_id",
            item_table = "account_table"
          ) \
          .request_remote_table_v2(**feature_multi_table_config)  \
          .copy_attr(
            item_table = "account_table",
            attrs=[
              {
                "from_item": "user_id",
                "to_common": "user_id"
              },
              {
                "from_item": "product_name",
                "to_common": "product_name"
              },
              {
                "from_item": "first_industry_name",
                "to_common": "first_industry_name"
              },
              {
                "from_item": "second_industry_name",
                "to_common": "second_industry_name"
              },
              {
                "from_item": "corporation_name",
                "to_common": "corporation_name"
              },
              {
                "from_item": "agent_id",
                "to_common": "agent_id"
              },
              {
                "from_item": "license_no",
                "to_common": "license_no"
              }
            ]
          ) \
    .end_if_()
    return self

  @module()
  def _retrieve(self,name):
    with data_manager:
      current_flow()  \
      .if_("need_creative_type == 1 or need_creative_type == 2") \
        .retrieve_by_sub_flow(
          sub_flow = self.package_photo_retrieve_flow,
          item_table = item_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "package_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_quota", "photo_user_id", "photo_source", "photo_id", "impr",
            "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account",
            "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.account_photo_retrieve_flow,
          item_table = item_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list", 
            "package_recall_photo_retrieve_admit", "enable_photo_recall_account"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "account_photo_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click",
            "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue",
            "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status",
            "quantity_score", "diverse_score", "explore_score", "cost_score"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.native_account_photo_retrieve_flow,
          item_table = item_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name",
            "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit",
            "in_native_recall_account_white_list", "enable_photo_recall_account"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "native_account_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_quota", "photo_user_id", "photo_source", "photo_id", "impr",
            "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account",
            "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status",
            "quantity_score", "diverse_score", "explore_score", "cost_score"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.guaranteed_photo_results_retrieve_flow,
          item_table = guaranteed_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name",
            "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit",
            "enable_photo_recall_explore_guaranteed"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "guaranteed_account_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_quota", "photo_user_id", "photo_source", "photo_id", "impr",
            "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account",
            "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status",
            "quantity_score", "diverse_score", "explore_score", "cost_score"
          ]
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.racing_photo_results_retrieve_flow,
          item_table = racing_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name",
            "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit",
            "enable_photo_recall_explore_racing"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "racing_account_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_quota", "photo_user_id", "photo_source", "photo_id", "impr",
            "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account",
            "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status",
            "quantity_score", "diverse_score", "explore_score", "cost_score"
          ]
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.user_id_photo_retrieve_flow,
          item_table = item_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list",
            "package_recall_photo_retrieve_admit", "enable_photo_recall_account", "in_share_uid_recall_account_white_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "user_id_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_quota", "photo_user_id", "photo_source", "photo_id",
            "impr", "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account",
            "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status",
            "quantity_score", "diverse_score", "explore_score", "cost_score"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.webcast_cross_account_photo_retrieve_flow,
          item_table = item_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list",
            "package_recall_photo_retrieve_admit", "in_webcast_campaign_white_list", "in_webcast_account_white_list",
            "uax_webcast_account_in_blacklist", "uax_webcast_product_in_blacklist", "uax_webcast_first_industry_in_blacklist",
            "uax_webcast_second_industry_in_blacklist", "live_creative_type"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "webcast_cross_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click",
            "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue",
            "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status",
            "quantity_score", "diverse_score", "explore_score", "cost_score"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.webcast_author_id_retrieve_flow,
          item_table = item_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list",
            "package_recall_photo_retrieve_admit", "live_author_id", "enable_photo_recall_webcast_author_account",
            "enable_photo_recall_webcast_author_campaign", "in_webcast_author_recall_account_white_list", "in_webcast_author_recall_license_white_list",
            "in_webcast_author_recall_product_white_list", "in_webcast_author_recall_account_black_list", "in_webcast_author_recall_license_black_list",
            "in_webcast_author_recall_product_black_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "webcast_author_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click",
            "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue",
            "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status",
            "quantity_score", "diverse_score", "explore_score", "cost_score"
          ],
        ) \
      .end_if_() \
      .if_("need_creative_type == 0 or need_creative_type == 2 or need_creative_type == 3") \
        .retrieve_by_sub_flow(
          sub_flow = self.aigc_book_id_retrieve_flow,
          item_table = aigc_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "aigc_temlpate_id_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_id", "creatieve_count", "arpu", "pool_name"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.aigc_unit_sdpa_retrieve_flow,
          item_table = aigc_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "aigc_unit_sdpa_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_id"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.aigc_series_id_retrieve_flow,
          item_table = aigc_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "aigc_series_id_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_id"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.aigc_dsp_acc2pid_retrieve_flow,
          item_table = aigc_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "aigc_dsp_acc2pid_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_id"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.aigc_dsp_purchase2_acc2pid_retrieve_flow,
          item_table = aigc_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "aigc_dsp_purchase2acc2pid_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_id"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.aigc_dsp_purchase_acc2pid_retrieve_flow,
          item_table = aigc_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "aigc_dsp_purchase_acc2pid_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_id"
          ],
        ) \
        .retrieve_by_sub_flow(
          sub_flow = self.aigc_tmall_account_retrieve_flow,
          item_table = aigc_table_name,
          pass_common_attrs = [
            "account_id", "campaign_id", "user_id", "unit_id", "product_name",
            "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"
          ],
          merge_common_attrs = [
            {"name": "retrieve_admit", "as": "aigc_dsp_acc2pid_recall_photo_retrieve_admit"}
          ],
          merge_item_attrs = [
            "photo_id"
          ],
        ) \
      .end_if_()
    return self

  @module()
  def unaudited_photo_filter(self):
    with data_manager:
      current_flow()
    return self

  @module()
  def aigc_photo_programmed_unit_filter(self):
    with data_manager:
      current_flow()  \
      .if_("request_type ~= 1 and unit_type == 7")  \
        .filter_by_rule(
          rule = {
            "attr_name": "photo_source",
            "remove_if": "==",
            "compare_to": 16,
            "remove_if_attr_missing": False
          },
          filter_reason="aigc_photo_programmed_unit_filter"
        ) \
      .end_if_()
    return self
  
  @module()
  def recall_creative_bound_filter(self):
    with data_manager:
      current_flow()  \
      .cast_attr_type(
        attr_type_cast_configs=[
          {
          "to_type": "string",
          "from_item_attr": "photo_id",
          "to_item_attr": "photo_id_str"
          }
        ]
      ) \
      .get_item_attr_from_redis(
        cluster_name = "adMobileDataVisitors",
        is_async=False,
        key_prefix="{{return 'uaa_photo_creative_cnt_' .. account_id .. '_'}}",
        redis_key_from = "photo_id_str",
        save_value_to = "uaa_photo_creative_cnt_str",
      ) \
      .cast_attr_type(
        attr_type_cast_configs=[
          {
            "to_type": "int",
            "from_item_attr": "uaa_photo_creative_cnt_str",
            "to_item_attr": "uaa_photo_creative_cnt"
          }
        ]
      ) \
      .filter_by_rule(
        rule = {
          "attr_name": "uaa_photo_creative_cnt",
          "remove_if": "<",
          "compare_to": "{{photo_bind_creative_upper_limit}}"
        },
        filter_reason="recall_creative_bound_filter"
      )
    return self

  @module()
  def photo_unit_divisity_filter(self):
    with data_manager:
      current_flow()  \
      .if_("request_type ~= 1")  \
        .filter_by_common_attr( 
            on_item_attr="photo_id",
            common_attr=["selectedCreativesList"], ## TODO:selectedCreativesList需要转换成int_list @zhoushuaiyin
            exclude=True,
            filter_reason="photo_unit_divisity_filter"
        ) \
      .end_if_()
    return self
  
  @module()
  def aigc_photo_blacklist_filter(self):
    with data_manager:
      current_flow()  \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.AdSmartDspAutoBuildAIGCBlackList",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_aigc_account_blacklist",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.nieuwland_black_product_name_list",
          "value_type": "set_string",
          "lookup_attr": "product_name",
          "output_attr": "in_aigc_product_name_blacklist",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.nieuwlandBlackLicenseIdList",
          "value_type": "set_string",
          "lookup_attr": "license_no",
          "output_attr": "in_aigc_license_blacklist",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.AdSmartDspAutoBuildEnableTagAccountId",
          "value_type": "tail_number",
          "lookup_attr": "account_id",
          "output_attr": "enable_tag_black_aigc_account",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.photoTagLicenseWhiteList",
          "value_type": "set_string",
          "lookup_attr": "license_no",
          "output_attr": "in_tag_black_aigc_license_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.photoTagProductWhiteList",
          "value_type": "set_string",
          "lookup_attr": "license_no",
          "output_attr": "in_tag_black_aigc_product_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.tmall618AccountWhitelist",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_tmall618_account_whitelist",
          "is_common_attr": True
        }]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig",
            # "value_type": "json",
            "json_path": "aigcFirstIndustryBlackList",
            "export_common_attr": "aigc_first_industry_black_list_json"
          }
        ]
      ) \
      .cast_attr_type(
        attr_type_cast_configs=[
          {
            "to_type": "string",
            "from_common_attr": "aigc_first_industry_black_list_json",
            "to_common_attr": "aigc_first_industry_black_list",
          },
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig",
            # "value_type": "json",
            "json_path": "aigcSecondIndustryMedicalWhiteList",
            "export_common_attr": "aigc_second_industry_medical_white_list_json"
          }
        ]
      ) \
      .cast_attr_type(
        attr_type_cast_configs=[
          {
            "to_type": "string",
            "from_common_attr": "aigc_second_industry_medical_white_list_json",
            "to_common_attr": "aigc_second_industry_medical_white_list",
          },
        ]
      ) \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig",
            # "value_type": "json",
            "json_path": "enableMedicalWhiteList",
            "export_common_attr": "enable_medical_white_list"
          }
        ]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.medicalReviewAccountWhitelist",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_medical_account_white_list",
          "is_common_attr": True
        }]
      ) \
      .enrich_attr_by_py(
        function_set = SystemSupplementPhotoSelectFlowPythonFunctionSet,
        py_function = SystemSupplementPhotoSelectFlowPythonFunctionSet.in_aigc_photo_blacklist_function
      ) \
      .if_("in_aigc_photo_blacklist == 1")  \
      .filter_by_rule(
          rule = {
            "attr_name": "photo_source",
            "remove_if": "==",
            "compare_to": 16,
            "remove_if_attr_missing": False
          },
          filter_reason="aigc_photo_blacklist_filter"
        ) \
      .end_if_()
    return self
  
  @module()
  def sdpa_photo_filter(self):
    with data_manager:
      current_flow()  \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.enableUaxSdpaAccountId",
          "value_type": "set_int64",
          "lookup_attr": "account_id",
          "output_attr": "in_sdpa_tag_account_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.enableUaxSdpaProductName",
          "value_type": "set_string",
          "lookup_attr": "product_name",
          "output_attr": "in_sdpa_tag_product_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.enable_sdpa_second_industry_whitelist",
          "value_type": "set_string",
          "lookup_attr": "second_industry_name",
          "output_attr": "in_sdpa_tag_second_industry_list",
          "is_common_attr": True
        }]
      ) \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.adems.enableSdpaLicenseNoWhiteList",
          "value_type": "set_string",
          "lookup_attr": "license_no",
          "output_attr": "in_sdpa_tag_license_list",
          "is_common_attr": True
        }]
      ) \
      .enrich_attr_by_py(
        function_set = SystemSupplementPhotoSelectFlowPythonFunctionSet,
        py_function = SystemSupplementPhotoSelectFlowPythonFunctionSet.in_sdpa_tag_function
      ) \
      .if_("in_sdpa_tag_list == 1")  \
        .get_common_attr_from_redis(
          cluster_name = "adMobileDataVisitors",
          is_async=False,
          redis_params = [
            {
              "key_prefix": "new_uax_unit_sdpa_",
              "redis_key": "{{unit_id}}",
              "output_attr_name": "target_sdpa_id"
            }
          ]
        ) \
        .if_("target_sdpa_id ~= nil and target_sdpa_id ~= ''")  \
          .cast_attr_type(
            attr_type_cast_configs=[
              {
              "to_type": "string",
              "from_item_attr": "photo_id",
              "to_item_attr": "photo_id_str"
              }
            ]
          ) \
          .get_item_attr_from_redis(
            cluster_name = "adMobileDataVisitors",
            is_async=False,
            key_prefix="new_uax_photo_sdpa_",
            redis_key_from = "photo_id_str",
            value_type = "string",
            save_value_to = "photo_sdpa_result"
          ) \
            .if_("photo_sdpa_result ~= nil and photo_sdpa_result ~= ''")  \
              .split_string(
                input_item_attr = "photo_sdpa_result",
                output_item_attr = "photo_sdpa_result_list",
                delimiters=",",
              ) \
              .filter_by_rule(
                rule = {
                  "attr_name": "photo_sdpa_result_list",
                  "remove_if": "not contain",
                  "compare_to": "{{target_sdpa_id}}",  
                  "remove_if_attr_missing": True
                  },
                filter_reason="sdpa_photo_filter"
              ) \
            .end_if_() \
        .end_if_() \
      .end_if_()
    return self

  @module()
  def selected_photo_blacklist_filter(self):
    with data_manager:
      current_flow()  \
      .lookup_kconf(
        kconf_configs = [{
          "kconf_key": "ad.algorithm.AdSmartDspAutoBuildPhotoBlackListForSelected",
          "value_type": "set_int64",
          "lookup_attr": "photo_id",
          "output_attr": "in_photo_blacklist",
          "is_common_attr": False
        }]
      ) \
      .filter_by_rule(
        rule = {
          "attr_name": "in_photo_blacklist",
          "remove_if": "==",
          "compare_to": 1,
          "remove_if_attr_missing": False
        },
        filter_reason="selected_photo_blacklist_filter"
      )
    return self

  @module()
  def frequence_photo_filter(self):
    with data_manager:
      current_flow()
    return self

  @module()
  def aigc_creative_bound_filter(self):
    with data_manager:
      current_flow()  \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig",
            "json_path":  "CreativeUpperBound",
            "export_common_attr": "aigc_creative_upper_bound",
            "default_value": 150
          }
        ]
      ) \
      .cast_attr_type(
        attr_type_cast_configs = [{
          "to_type": "int",
          "from_common_attr": "aigc_creative_upper_bound",
          "to_common_attr": "aigc_creative_upper_bound_int",
        }]
      ) \
      .filter_by_rule(
        rule = {
          "attr_name": "creative_cnt",
          "remove_if": ">",
          "compare_to": "{{aigc_creative_upper_bound_int}}",
          "remove_if_attr_missing": False
        },
        filter_reason="aigc_creative_bound_filter"
      )
    return self
  
  @module()
  def aigc_photo_unit_divisity_filter(self):
    with data_manager:
      current_flow()  \
      .if_("request_type ~= 1")  \
        .filter_by_common_attr( 
            on_item_attr="photo_id",
            common_attr=["onlineAIGCCreativesList"], ## TODO:onlineAIGCCreativesList需要转换成int_list @liuxiaofei
            exclude=True,
            filter_reason="aigc_photo_unit_divisity_filter"
        ) \
      .end_if_()
    return self

  @module()
  def _post_data_proc(self,name):
    ## 各个召回通路结果返回合并后，对item集合进行去重
    with data_manager:
      self.do(self._post_data_proc_item()) \
      .do(self._post_data_proc_guarantee()) \
      .do(self._post_data_proc_racing()) \
      .do(self._post_data_proc_aigc())
    return self

  @module()
  def _sort_item(self):
    with data_manager:
      current_flow()  \
      .photo_sort(item_table = item_table_name, filter_reason="photo_sort")
    return self

  @module()
  def _sort_racing(self):
    with data_manager:
      current_flow() \
      .photo_sort(item_table = racing_table_name, filter_reason="photo_sort")
    return self

  @module()
  def _sort_guarantee(self):
    with data_manager:
      current_flow()  \
      .photo_sort(item_table = guaranteed_table_name, filter_reason="photo_sort")
    return self

  @module()
  def _sort_aigc_exploit(self):
    with data_manager:
      current_flow()  \
      .photo_sort(item_table = aigc_exploit_table_name, filter_reason="photo_sort")
    return self

  @module()
  def _sort_aigc_explore(self):
    with data_manager:
      current_flow()  \
      .photo_sort(item_table = aigc_explore_table_name, filter_reason="photo_sort")
    return self

  @module()
  def _sort_aigc_new_photo(self):
    with data_manager:
      current_flow()  \
      .photo_sort(item_table = aigc_new_photo_table_name, filter_reason="photo_sort")
    return self

  @module()
  #@async_mix()
  def _post_data_proc_item(self):
    ## item数据后处理
    with data_manager:
      current_flow()  \
      .deduplicate()  \
      .create_logic_table(
        item_table=item_table_name,
        logic_table=logic_item_table_name,
        select_attr=["photo_id"]
      ) \
      .unaudited_photo_filter()  \
      .aigc_photo_programmed_unit_filter()  \
      .recall_creative_bound_filter()  \
      .photo_unit_divisity_filter()  \
      .aigc_photo_blacklist_filter()  \
      .sdpa_photo_filter()  \
      .selected_photo_blacklist_filter()  \
      .frequence_photo_filter()  \
      .model_cmd_enricher(
        item_table = item_table_name,
        service_group = "PRODUCTION",
        kess_service = "grpc_adI18nPsDspNNRouterServerNew",
        timeout_ms = 100,
        ad_item_id = "photo_id",
        cmd_configs = [
          {
            "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9",
            "cmd_key": "{{cmd_key}}",
            "cmd_value_num": "{{cmd_value_num}}",
            "predict_attrs": [
              {
                "attr_name": "score",
                "predict_type": "{{predict_type}}"
              }
            ]
          }
        ]
      ) \
      ._sort_item()
    return self

  @module()
  #@async_mix()
  def _post_data_proc_racing(self):
    ## racing数据后处理
    with data_manager:
      current_flow()  \
      .deduplicate()  \
      .create_logic_table(
        item_table=racing_table_name,
        logic_table=logic_racing_table_name,
        select_attr=["photo_id"]
      ) \
      .unaudited_photo_filter()  \
      .aigc_photo_programmed_unit_filter()  \
      .recall_creative_bound_filter()  \
      .photo_unit_divisity_filter()  \
      .aigc_photo_blacklist_filter()  \
      .sdpa_photo_filter()  \
      .selected_photo_blacklist_filter()  \
      .frequence_photo_filter()  \
      .model_cmd_enricher(
        item_table = racing_table_name,
        service_group = "PRODUCTION",
        kess_service = "grpc_adI18nPsDspNNRouterServerNew",
        timeout_ms = 100,
        ad_item_id = "photo_id",
        cmd_configs = [
          {
            "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9",
            "cmd_key": "{{cmd_key}}",
            "cmd_value_num": "{{cmd_value_num}}",
            "predict_attrs": [
              {
                "attr_name": "score",
                "predict_type": "{{predict_type}}"
              }
            ]
          }
        ]
      ) \
      ._sort_racing()
    return self

  @module()
  #@async_mix()
  def _post_data_proc_guarantee(self):
    ## guarantee数据后处理
    with data_manager:
      current_flow()  \
      .deduplicate()  \
      .create_logic_table(
        item_table=guaranteed_table_name,
        logic_table=logic_guaranteed_table_name,
        select_attr=["photo_id"]
      ) \
      .unaudited_photo_filter()  \
      .aigc_photo_programmed_unit_filter()  \
      .recall_creative_bound_filter()  \
      .photo_unit_divisity_filter()  \
      .aigc_photo_blacklist_filter()  \
      .sdpa_photo_filter()  \
      .selected_photo_blacklist_filter()  \
      .frequence_photo_filter()  \
      .model_cmd_enricher(
        item_table = guaranteed_table_name,
        service_group = "PRODUCTION",
        kess_service = "grpc_adI18nPsDspNNRouterServerNew",
        timeout_ms = 100,
        ad_item_id = "photo_id",
        cmd_configs = [
          {
            "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9",
            "cmd_key": "{{cmd_key}}",
            "cmd_value_num": "{{cmd_value_num}}",
            "predict_attrs": [
              {
                "attr_name": "score",
                "predict_type": "{{predict_type}}"
              }
            ]
          }
        ]
      ) \
      ._sort_guarantee()
    return self

  @module()
  #@async_mix()
  def _post_data_proc_aigc(self):
    ## aigc数据后处理
    with data_manager:
      current_flow()  \
      .deduplicate()  \
      ._get_aigc_predicted_scores()  \
      .split_aigc_tables(
        item_table = aigc_table_name,
        select_attr=["photo_id", "s3", "s6", "s7", "s8", "s9"],
        to_table_names=[aigc_exploit_table_name, aigc_explore_table_name, aigc_new_photo_table_name]
      ) \
      .create_logic_table(
        item_table=aigc_exploit_table_name,
        logic_table=logic_aigc_exploit_table_name,
        select_attr=["photo_id"]
      ) \
      .create_logic_table(
        item_table=aigc_explore_table_name,
        logic_table=logic_aigc_explore_table_name,
        select_attr=["photo_id"]
      ) \
      .create_logic_table(
        item_table=aigc_new_photo_table_name,
        logic_table=logic_aigc_new_photo_table_name,
        select_attr=["photo_id"]
      )
      self._post_data_proc_aigc_exploit()
      self._post_data_proc_aigc_new_photo()
      self._post_data_proc_aigc_explore()
    return self

  @module()
  def process_already_insert_photo_list(self):
    ## 在投队列解析处理
    with data_manager:
      current_flow()  \
      .retrieve_by_common_attr(
        item_table = "already_insert_photo_table",
        attr="alreadyInsertedPhotoIdList",
        reason=7,
      ) \
      .cast_attr_type(
        item_table = "already_insert_photo_table",
        attr_type_cast_configs=[
          {
          "to_type": "string",
          "from_item_attr": "photo_id",
          "to_item_attr": "photo_id_str"
          }
        ]
      ) \
      .get_item_attr_from_redis(
        item_table = "already_insert_photo_table",
        cluster_name = "adMobileDataVisitors",
        key_prefix = "{{return 'uax_predict_cost_score_' .. account_id .. '_'}}",
        is_async=False,
        redis_key_from = "photo_id_str",
        save_value_to = "uax_predict_cost_score_json_str",
      ) \
      .enrich_attr_by_json(
        item_table = "already_insert_photo_table",
        json_attr = "uax_predict_cost_score_json_str",
        json_from_item_attr = True,
        json_configs = [
          {
            "json_path": "s8",
            "export_item_attr": "s8",
            "default_value": -1.0E5,
          }
        ]
      ) \
      .enrich_attr_by_py(
        item_table = "already_insert_photo_table",
        function_set = SystemSupplementPhotoSelectFlowPythonFunctionSet,
        py_function = SystemSupplementPhotoSelectFlowPythonFunctionSet.get_already_exploit_num
      )
    return self

  @module()
  def _get_aigc_predicted_scores(self):
    ## 获取 aigc 预估分
    with data_manager:
      current_flow()  \
      .get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.algorithm.AdSmartPredictCostKconfConfig",
            "json_path":  "{{return exp_partten .. '.bucketRatio'}}",
            "export_common_attr": "AdSmartPredictCostKconfConfig_bucketRatio",
            "default_value": 70.0
          }
        ]
      ) \
      .cast_attr_type(
        attr_type_cast_configs=[
          {
            "to_type": "int",
            "from_common_attr": "AdSmartPredictCostKconfConfig_bucketRatio",
            "to_common_attr": "AdSmartPredictCostKconfConfig_bucketRatio_int",
          },
        ]
      ) \
      .get_common_attr_from_redis(
        cluster_name = "adMobileDataVisitors",
        redis_params = [
          {
            "key_prefix": "{{return 'uax_aigc_predict_quantile_v2_p' .. AdSmartPredictCostKconfConfig_bucketRatio_int .. '_'}}",
            "redis_key" : "{{account_id}}",
            "output_attr_name": "uax_aigc_predict_json_str"
          }
        ]
      ) \
      .enrich_attr_by_json(
        json_attr = "uax_aigc_predict_json_str",
        json_configs = [
          {
            "json_path": "pred_cost",
            "export_common_attr": "uax_aigc_pred_cost_thresh",
            "default_value": 0.0
          }
        ]
      ) \
      .enrich_attr_by_py(
        function_set = SystemSupplementPhotoSelectFlowPythonFunctionSet,
        py_function = SystemSupplementPhotoSelectFlowPythonFunctionSet.get_prefix_for_item
      ) \
      .cast_attr_type(
        attr_type_cast_configs=[
          {
          "to_type": "string",
          "from_item_attr": "photo_id",
          "to_item_attr": "photo_id_str"
          }
        ]
      ) \
      .get_item_attr_from_redis(
        cluster_name = "adNieuwlandPrivateMatchDerivePhoto",
        key_prefix = "{{adNieuwlandPrivateMatchDerivePhoto_prefix}}",
        is_async=False,
        redis_key_from = "photo_id_str",
        save_value_to = "adNieuwlandPrivateMatchDerivePhoto_json_str",
      ) \
      .enrich_attr_by_json(
        json_attr = "adNieuwlandPrivateMatchDerivePhoto_json_str",
        json_from_item_attr = True,
        json_configs = [
          {
            "json_path": "creative_cnt",
            "export_item_attr": "creative_cnt",
            "default_value": 0.0
          },
          {
            "json_path": "impr",
            "export_item_attr": "impress_cnt",
            "default_value": 0.0
          },
          {
            "json_path": "conversion",
            "export_item_attr": "conversion_cnt",
            "default_value": 0.0
          },
          {
            "json_path": "predicted_score",
            "export_item_attr": "predicted_score_json_str",
            "default_value": ""
          }
        ]
      ) \
      .enrich_attr_by_json(
        json_attr = "predicted_score_json_str",
        json_from_item_attr = True,
        json_configs = [
          {
            "json_path": "s3",
            "export_item_attr": "s3",
            "default_value": 0.0
          },
          {
            "json_path": "s6",
            "export_item_attr": "s6",
            "default_value": 0.0
          },
          {
            "json_path": "s7",
            "export_item_attr": "s7",
            "default_value": 0.0
          }
        ]
      ) \
      .cast_attr_type(
        attr_type_cast_configs=[
          {
          "to_type": "string",
          "from_item_attr": "photo_id",
          "to_item_attr": "photo_id_str"
          }
        ]
      ) \
      .get_item_attr_from_redis(
        cluster_name = "adMobileDataVisitors",
        key_prefix = "{{return 'uax_predict_cost_score_' .. account_id .. '_'}}",
        is_async=False,
        redis_key_from = "photo_id_str",
        save_value_to = "uax_predict_cost_score_json_str",
      ) \
      .enrich_attr_by_json(
        json_attr = "uax_predict_cost_score_json_str",
        json_from_item_attr = True,
        json_configs = [
          {
            "json_path": "s8",
            "export_item_attr": "s8",
            "default_value": -1.0E5
          },
          {
            "json_path": "s9",
            "export_item_attr": "s9",
            "default_value": 1.0E-4
          }
        ]
      ) \
      .split_aigc_tables(
        item_table = aigc_table_name,
        select_attr=["photo_id", "s3", "s6", "s7", "s8", "s9", "uax_aigc_pred_cost_thresh"],
      )
    return self

  @module()
  #@async_mix()
  def _post_data_proc_aigc_exploit(self):
    ## aigc_exploit 数据后处理
    with data_manager:
      current_flow()  \
      .aigc_creative_bound_filter()  \
      .aigc_photo_unit_divisity_filter() \
      .model_cmd_enricher(
        item_table = aigc_exploit_table_name,
        service_group = "PRODUCTION",
        kess_service = "grpc_adI18nPsDspNNRouterServerNew",
        timeout_ms = 100,
        ad_item_id = "photo_id",
        cmd_configs = [
          {
            "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9",
            "cmd_key": "{{cmd_key}}",
            "cmd_value_num": "{{cmd_value_num}}",
            "predict_attrs": [
              {
                "attr_name": "score",
                "predict_type": "{{predict_type}}"
              }
            ]
          }
        ]
      ) \
      ._sort_aigc_exploit()
    return self

  @module()
  #@async_mix()
  def _post_data_proc_aigc_explore(self):
    ## aigc_exploration 数据后处理
    with data_manager:
      current_flow()  \
      .aigc_creative_bound_filter()  \
      .aigc_photo_unit_divisity_filter() \
      .model_cmd_enricher(
        item_table = aigc_explore_table_name,
        service_group = "PRODUCTION",
        kess_service = "grpc_adI18nPsDspNNRouterServerNew",
        timeout_ms = 100,
        ad_item_id = "photo_id",
        cmd_configs = [
          {
            "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9",
            "cmd_key": "{{cmd_key}}",
            "cmd_value_num": "{{cmd_value_num}}",
            "predict_attrs": [
              {
                "attr_name": "score",
                "predict_type": "{{predict_type}}"
              }
            ]
          }
        ]
      ) \
      ._sort_aigc_explore()
    return self

  @module()
  #@async_mix()
  def _post_data_proc_aigc_new_photo(self):
    ## aigc_new_photo 数据后处理
    with data_manager:
      current_flow()  \
      .aigc_creative_bound_filter()  \
      .aigc_photo_unit_divisity_filter() \
      .model_cmd_enricher(
        item_table = aigc_new_photo_table_name,
        service_group = "PRODUCTION",
        kess_service = "grpc_adI18nPsDspNNRouterServerNew",
        timeout_ms = 100,
        ad_item_id = "photo_id",
        cmd_configs = [
          {
            "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9",
            "cmd_key": "{{cmd_key}}",
            "cmd_value_num": "{{cmd_value_num}}",
            "predict_attrs": [
              {
                "attr_name": "score",
                "predict_type": "{{predict_type}}"
              }
            ]
          }
        ]
      ) \
      ._sort_aigc_new_photo()
    return self

  @module()
  def _select_strategy(self,name):
    with data_manager:
      current_flow()  \
      .if_("enable_order_creative_switch == 1") \
        .lookup_kconf(
          kconf_configs = [{
            "kconf_key": "ad.adems.photoRecallAccountTailnumberConfig",
            "value_type": "tail_number",
            "lookup_attr": "account_id",
            "output_attr": "photo_recall_account",
            "is_common_attr": True
          }]
        ) \
        .cast_attr_type(
          attr_type_cast_configs=[
            {
            "to_type": "string",
            "from_item_attr": "photo_id",
            "to_item_attr": "photo_id_str"
            }
          ]
        ) \
        .get_item_attr_from_redis(
          cluster_name = "adMobileDataVisitors",
          is_async=False,
          key_prefix="{{return 'uax_ucb_explore_score_' .. account_id .. '_'}}",
          redis_key_from = "photo_id_str",
          save_value_to = "photo_uax_ucb_explore_score_str",
        ) \
        .cast_attr_type(
          attr_type_cast_configs=[
            {
              "to_type": "double",
              "from_item_attr": "photo_uax_ucb_explore_score_str",
              "to_item_attr": "photo_uax_ucb_explore_score"
            }
          ]
        ) \
        .enrich_attr_by_light_function(
            function_name="CalPhotoCreateTimeDiff",
            class_name="AdLightFunctionSet",
        ) \
        .get_kconf_params(
          kconf_configs = [{
            "kconf_key": "ad.adems.uaxAccountRecallQuotaConfig", ## AIGC行业召回 quota 配置
            # "value_type": "json",
            "json_path": "{{first_industry_name}}",
            "export_common_attr": "first_industry_aigc_ratio"
          }]
        ) \
        .cast_attr_type(
          attr_type_cast_configs=[
            {
              "to_type": "double",
              "from_common_attr": "first_industry_aigc_ratio",
              "to_common_attr": "first_industry_aigc_ratio_double"
            }
          ]
        ) \
        .get_kconf_params(
          kconf_configs = [{
            "kconf_key": "ad.adems.uaxRecallQuotaAccountList", ## AIGC账户召回 quota 配置
            # "value_type": "json",
            "json_path": "{{account_id}}",
            "export_common_attr": "account_aigc_ratio"
          }]
        ) \
        .cast_attr_type(
          attr_type_cast_configs=[
            {
              "to_type": "double",
              "from_common_attr": "account_aigc_ratio",
              "to_common_attr": "account_aigc_ratio_double"
            }
          ]
        ) \
        .get_kconf_params(
          kconf_configs = [{
            "kconf_key": "ad.adems.uaxAigcPhotosMaxQuota", ## AIGC最大quota配置
            "value_type": "int64",
            "export_common_attr": "uax_aigc_photo_max_quota",
            "default_value": 20
          }]
        ) \
        .lookup_kconf(
          kconf_configs = [{
            "kconf_key": "ad.adems.uaxAigcAccountWhiteList", ## 综平仅AIGC账户加白
            "value_type": "set_int64",
            "lookup_attr": "account_id",
            "output_attr": "in_uax_aigc_account_white_list",
            "default_value": 0
          }]
        ) \
        .lookup_kconf(
          kconf_configs = [{
            "kconf_key": "ad.adems.uaxAigcProductWhiteList", ## 综平仅AIGC产品加白
            "value_type": "set_int64",
            "lookup_attr": "product_id",
            "output_attr": "in_uax_aigc_product_white_list",
            "default_value": 0
          }]
        ) \
        .get_kconf_params(
          kconf_configs = [
            {
              "kconf_key": "ad.adems.newExploreQuotaRatio",
              "value_type": "double",
              "export_common_attr": "new_explore_quota_ratio",
              "default_value": 0.5
            }
          ]
        ) \
        .get_kconf_params(
          kconf_configs = [
            {
              "kconf_key": "ad.adems.expExploreQuotaRatio",
              "value_type": "double",
              "export_common_attr": "explore_quota_predict_indicator",
              "default_value": 0.3
            }
          ]
        ) \
        .get_kconf_params(
          kconf_configs = [
            {
              "kconf_key": "ad.algorithm.AdSmartPredictCostKconfConfig",
              "json_path":  "{{return exp_partten .. '.explorationRatio'}}",
              "export_common_attr": "aigc_exploration_ratio",
              "default_value": 20.0
            }
          ]
        ) \
        .cast_attr_type(
          attr_type_cast_configs=[
            {
              "to_type": "double",
              "from_common_attr": "aigc_exploration_ratio",
              "to_common_attr": "aigc_exploration_ratio_double",
            },
          ]
        ) \
        .get_kconf_params(
          kconf_configs = [
            {
              "kconf_key": "ad.algorithm.AdSmartPredictCostKconfConfig",
              "json_path":  "{{return exp_partten .. '.newPhotoRatio'}}",
              "export_common_attr": "aigc_new_photo_ratio",
              "default_value": 12.5
            }
          ]
        ) \
        .cast_attr_type(
          attr_type_cast_configs=[
            {
              "to_type": "double",
              "from_common_attr": "aigc_new_photo_ratio",
              "to_common_attr": "aigc_new_photo_ratio_double",
            },
          ]
        ) \
        .get_kconf_params(
          kconf_configs = [
            {
              "kconf_key": "ad.dsp2.buildHighLightPhotoLimit",
              # "value_type": "json",
              "json_path":  "limitQuota",
              "export_common_attr": "high_light_photo_limit",
              "default_value": 7
            }
          ]
        ) \
        .cast_attr_type(
          attr_type_cast_configs=[
            {
              "to_type": "int",
              "from_common_attr": "high_light_photo_limit",
              "to_common_attr": "high_light_photo_limit_int",
            },
          ]
        ) \
        .get_kconf_params(
          kconf_configs = [
            {
              "kconf_key": "ad.adems.photoExpConf",
              # "value_type": "json",
              "json_path":  "param_alpha",
              "export_common_attr": "param_alpha",
              "default_value": 0.7
            }
          ]
        ) \
        .get_kconf_params(
          kconf_configs = [
            {
              "kconf_key": "ad.adems.photoExpConf",
              # "value_type": "json",
              "json_path":  "param_beta",
              "export_common_attr": "param_beta",
              "default_value": 0.1
            }
          ]
        ) \
        .get_kconf_params(
          kconf_configs = [
            {
              "kconf_key": "ad.adems.photoExpConf",
              # "value_type": "json",
              "json_path":  "param_gamma",
              "export_common_attr": "param_gamma",
              "default_value": 0.2
            }
          ]
        ) \
        .cal_quota() \
        .select_topk(
          item_table = item_table_name,
          to_table = main_table_name,
          quota_num = "{{item_table_quota}}",
          merge_attr_names=["photo_id"]
        ) \
        .select_topk(
          item_table = racing_table_name,
          to_table = main_table_name,
          quota_num = "{{racing_table_quota}}",
          merge_attr_names=["photo_id"]
        ) \
        .select_topk(
          item_table = guaranteed_table_name,
          to_table = main_table_name,
          quota_num = "{{guarantee_table_quota}}",
          merge_attr_names=["photo_id"]
        ) \
         .select_topk(
          item_table = aigc_exploit_table_name,
          to_table = main_table_name,
          quota_num = "{{aigc_exploit_table_quota}}",
          merge_attr_names=["photo_id"]
        ) \
        .select_topk(
          item_table = aigc_explore_table_name,
          to_table = main_table_name,
          quota_num = "{{aigc_explore_table_quota}}",
          merge_attr_names=["photo_id"]
        ) \
        .select_topk(
          item_table = aigc_new_photo_table_name,
          to_table = main_table_name,
          quota_num = "{{aigc_new_photo_table_quota}}",
          merge_attr_names=["photo_id"]
        ) \
      .end_if_() \
      .trace_log_send(
        physical_table_names = 
          [item_table_name,
          racing_table_name,
          guaranteed_table_name,
          aigc_exploit_table_name,
          aigc_explore_table_name,
          aigc_new_photo_table_name],
      )
    return self

  @module()
  def _post_proc(self,name):
    with data_manager:
      current_flow()  \
      .query_photo_info_by_rpc()  \
      .query_mix_asset_meta_by_rpc()  \
      .calc_photo_property() \
      .if_("need_creative_type == 2") \
        .get_item_attr_from_redis(
          cluster_name = "adMobileDataVisitors",
          key_prefix = "uax_webcast_message_",
          redis_key_from="photo_id",
          save_value_to="message_value"
        ) \
      .end_if_()
    return self
