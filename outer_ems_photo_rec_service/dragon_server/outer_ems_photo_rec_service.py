#!/usr/bin/env python3
# coding=utf-8

import os
import sys
dragon_path=os.path.abspath('../../../../dragon/')
outer_ems_photo_rec_service_path=os.path.abspath('..')

sys.path.append(dragon_path)
sys.path.append(outer_ems_photo_rec_service_path)
sys.path.append('../../../../')

from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from ad_dragonfly.outer.ad_ems_outer_mixin import AdEmsOuterApiMixin
from ks_leaf_bootstrap.utils import gen_service_graph_def
from dragonfly.modular.module import module
from dragonfly.visualization.dag import *
from dragonfly.common_leaf_dsl import LeafFlow, current_flow
from dragonfly.modular.data_manager import DataManager, data_manager, ab_param as ab, kconf_param as kconf
from dragon_server.package_conversion_predict_flow import PackageConversionPredictFlow
from dragon_server.photo_select_flow.model_decision_flow import ModelPredictPhotoSelectFlow
from dragon_server.photo_select_flow.system_supplement_flow import SystemSupplementPhotoSelectFlow

#========================================= Flow Define Start ================================
package_conversion_predict_flow = PackageConversionPredictFlow(name="default", item_table="item_table")
with package_conversion_predict_flow, data_manager:
  '''
  中小套餐包计划转化预估处理流程
  '''
  package_conversion_predict_flow \
  .get_package_conversion_predict_result()

#========================================= 机器决策 Flow ================================
photo_table_name = "photo_table"

model_decision_flow = ModelPredictPhotoSelectFlow(photo_table_name)
with model_decision_flow, data_manager:
  '''
  机器决策素材优选处理流程
  '''
  model_decision_flow \
    .select_photo(name="model_decision_predict_photo_select_flow")

#========================================= 素材补量 Flow ================================
system_supplement_table_name = ""

system_supplement_flow = SystemSupplementPhotoSelectFlow(system_supplement_table_name)
with system_supplement_flow, data_manager:
  '''
  系统补量素材优选处理流程
  '''
  system_supplement_flow \
    .select_photo(name="system_supplement_photo_select_flow")

def CookService(service):
  service.return_common_attrs([
  ])
  service.return_item_attrs([
  ])
  service.CHECK_UNUSED_ATTR = False
  service.IGNORE_NO_SOURCE_ATTR = ["photo_id", "photo_user_id", "photo_source", "video_id", "cover_height", "cover_width"]
  service.AUTO_INJECT_META_DATA = False
  current_folder = os.path.dirname(os.path.abspath(__file__))
  service.add_leaf_flows(leaf_flows=[package_conversion_predict_flow], request_type="package_conversion_predict") \
         .add_leaf_flows(leaf_flows=[model_decision_flow], request_type="model_decision") \
         .add_leaf_flows(leaf_flows=[system_supplement_flow], request_type="system_supplement") \
         .draw(to_dragonfly_viz = True, mode = "remote")

service = LeafService(kess_name="outer-ems-photo-rec-service")

service.PY_ENABLE_REMOTE_COMPILE = True  # 启用远程编译
service.PY_USE_REMOTE_DSO = True  # 启用远程 so

CookService(service)

ad_config = {
  "grpc" : {
    "test" : False,
    "server" : {
      "kess_name" : "USE_KSN_AS_SERVICE",
      "port" : 20182,
      "grpc_cq_num": 8,
      "kcs_grpc_port_key" : "AUTO_PORT1",
      "thread_num": 60,  ##  default cpu num
      "quit_wait_seconds" : 85,
      "start_warmup_seconds" : 10, #默认 10s
    },
    "client_map" : {
    }
  },
}

current_folder = os.path.dirname(os.path.abspath(__file__))
service.build(output_file=os.path.join(current_folder, "pub/outer-ems-photo-rec-service/config/dynamic_json_config.json"),
                extra_fields=ad_config)
