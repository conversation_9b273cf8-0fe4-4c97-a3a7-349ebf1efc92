{"_CONFIG_VERSION": "c989cca28659d2d3209393dc6a4e6838_local", "_DRAGONFLY_CREATE_TIME": "2025-09-03 14:39:57", "_DRAGONFLY_VERSION": "0.8.3", "default_request_type": "package_conversion_predict", "grpc": {"client_map": {}, "server": {"grpc_cq_num": 8, "kcs_grpc_port_key": "AUTO_PORT1", "kess_name": "USE_KSN_AS_SERVICE", "port": 20182, "quit_wait_seconds": 85, "start_warmup_seconds": 10, "thread_num": 60}, "test": false}, "kess_config": {"service_name": "outer-ems-photo-rec-service"}, "pipeline_manager_config": {"base_pipeline": {"processor": {"_branch_controller_A8330883": {"$branch_start": "_branch_controller_A8330883", "$code_info": "[if] A8330883 photo_select_base_flow.py in select_photo(): .if_ (\"flow_admit == 0 or flow_admit == nil\")", "export_common_attr": ["_if_control_attr_8"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["flow_admit"], "lua_script": "function evaluate() if (flow_admit == 0 or flow_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "_branch_controller_A8330883_1": {"$branch_start": "_branch_controller_A8330883_1", "$code_info": "[if] A8330883 photo_select_base_flow.py in select_photo(): .if_ (\"flow_admit == 0 or flow_admit == nil\")", "export_common_attr": ["_if_control_attr_25"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["flow_admit"], "lua_script": "function evaluate() if (flow_admit == 0 or flow_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "account_photo_recall::_branch_controller_8A24F361_2": {"$branch_start": "account_photo_recall::_branch_controller_8A24F361_2", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_10"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "account_photo_recall::_retrieve::get_common_attr_from_redis_E60EBC": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "uax_recall_photo_dim_account_id_1_", "output_attr_name": "uax_recall_photo_dim_account_json_value", "output_attr_type": "string", "redis_key": "{{account_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "account_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_467EEE": {"input_common_attr": "uax_recall_photo_dim_account_json_value", "input_common_attr_type": 0, "reason": 1, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "SelectedOptimisedPhotoParserUdf"}, "account_photo_recall::_retrieve_admit::enrich_attr_by_py_8C8BA1": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AccountPhotoRetrieveFlowPythonFunctionSet_clang_17589228de583c2cc01f53df0d87f9dc.so", "clang_so_name": "AccountPhotoRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "AccountPhotoRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "AccountPhotoRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AccountPhotoRetrieveFlowPythonFunctionSet_gcc_9aa6be5e9f1bcefeee7ce9c7ac523cf6.so", "gcc_so_name": "AccountPhotoRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["package_recall_photo_retrieve_admit", "enable_photo_recall_account"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "account_photo_recall::return__8EB8D5": {"skip": "{{_if_control_attr_10}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "aigc_book_id_photo_recall::_branch_controller_8A24F361_11": {"$branch_start": "aigc_book_id_photo_recall::_branch_controller_8A24F361_11", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_19"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "aigc_book_id_photo_recall::_retrieve::build_material_page_request_15A81F": {"output_column_name": "query_material_page_request", "type_name": "BuildMaterialPageRequest"}, "aigc_book_id_photo_recall::_retrieve::enrich_by_generic_grpc_99FE3B": {"downstream_processor": "aigc_book_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_B6D45B", "kess_service": "grpc_AIMaterialRpcService", "method_name": "/kuaishou.ad.creative.center.aigc.AIMaterialRpcService/QueryMaterialPageCached", "request_attr": "query_material_page_request", "response_attr": "query_material_page_response", "response_class": "kuaishou.ad.creative.center.aigc.QueryMaterialPageResponse", "timeout_ms": 200, "type_name": "CommonRecoGenericGrpcEnricher"}, "aigc_book_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_B6D45B": {"input_common_attr": "query_material_page_response", "input_common_attr_type": 4, "reason": 10, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "QueryMaterialPageResponseParserUdf"}, "aigc_book_id_photo_recall::_retrieve_admit::enrich_attr_by_py_FC6227": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcBookIdRetrieveFlowPythonFunctionSet_clang_ad3e6b5ac66f8ecf0d83686f8ed9b5cd.so", "clang_so_name": "AigcBookIdRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "AigcBookIdRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "AigcBookIdRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcBookIdRetrieveFlowPythonFunctionSet_gcc_3c8ccf15fdf587d4fd24517996d95a4c.so", "gcc_so_name": "AigcBookIdRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["book_id", "campaign_type", "kconf_ad_algorithm_AdSmartPredictCostKconfConfig_campaignTypeList"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "aigc_book_id_photo_recall::return__ACE1D9": {"skip": "{{_if_control_attr_19}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "aigc_dsp_acc2pid_photo_recall::_branch_controller_8A24F361_14": {"$branch_start": "aigc_dsp_acc2pid_photo_recall::_branch_controller_8A24F361_14", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_22"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "aigc_dsp_acc2pid_photo_recall::_retrieve::get_common_attr_from_redis_BEC36E": {"cluster_name": "adNieuwlandPrivateMatchDerivePhoto", "is_async": false, "redis_params": [{"key_prefix": "dsp_acc2pid_", "output_attr_name": "aigc_account_photo_str", "output_attr_type": "string", "redis_key": "{{account_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "aigc_dsp_acc2pid_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_E0EC2E": {"input_common_attr": "aigc_account_photo_list", "input_common_attr_type": 3, "reason": 13, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "AigcPhotoListParserUdf"}, "aigc_dsp_acc2pid_photo_recall::_retrieve::split_string_295FB2": {"delimiters": ",", "input_common_attr": "aigc_account_photo_str", "output_common_attr": "aigc_account_photo_list", "type_name": "CommonRecoStringSplitEnricher"}, "aigc_dsp_acc2pid_photo_recall::_retrieve_admit::enrich_attr_by_py_574E82": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcDspAcc2PidRetrieveFlowPythonFunctionSet_clang_2f89991d01613aef62a9584838b79fad.so", "clang_so_name": "AigcDspAcc2PidRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "AigcDspAcc2PidRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "AigcDspAcc2PidRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcDspAcc2PidRetrieveFlowPythonFunctionSet_gcc_8a309452208d6cb425cd13cf44ac8821.so", "gcc_so_name": "AigcDspAcc2PidRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["series_id", "in_sdpa_tag_account_list", "in_sdpa_tag_product_list", "in_sdpa_tag_second_industry_list", "in_sdpa_tag_license_list", "in_aigc_purchase_account_list"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "aigc_dsp_acc2pid_photo_recall::_truncate::shuffle_A597DE": {"type_name": "CommonRecoShuffleArranger"}, "aigc_dsp_acc2pid_photo_recall::_truncate::truncate_EFCECD": {"size_limit": "{{aigc_maxMontageCount}}", "type_name": "CommonRecoTruncateArranger"}, "aigc_dsp_acc2pid_photo_recall::return__61CF30": {"skip": "{{_if_control_attr_22}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "aigc_dsp_purchase2_acc2pid_photo_recall::_branch_controller_8A24F361_15": {"$branch_start": "aigc_dsp_purchase2_acc2pid_photo_recall::_branch_controller_8A24F361_15", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_23"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "aigc_dsp_purchase2_acc2pid_photo_recall::_retrieve::get_common_attr_from_redis_F0E829": {"cluster_name": "adNieuwlandPrivateMatchDerivePhoto", "is_async": false, "redis_params": [{"key_prefix": "dsp_purchase2_acc2pid_", "output_attr_name": "aigc_account_photo_str", "output_attr_type": "string", "redis_key": "{{account_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "aigc_dsp_purchase2_acc2pid_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_E0E7AE": {"input_common_attr": "aigc_account_photo_list", "input_common_attr_type": 3, "reason": 14, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "AigcPhotoListParserUdf"}, "aigc_dsp_purchase2_acc2pid_photo_recall::_retrieve::split_string_295FB2": {"delimiters": ",", "input_common_attr": "aigc_account_photo_str", "output_common_attr": "aigc_account_photo_list", "type_name": "CommonRecoStringSplitEnricher"}, "aigc_dsp_purchase2_acc2pid_photo_recall::_retrieve_admit::enrich_attr_by_py_D57D18": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcDspPurchase2Acc2PidRetrieveFlowPythonFunctionSet_clang_0c2bef8cde214fca31798716d528a0ba.so", "clang_so_name": "AigcDspPurchase2Acc2PidRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "AigcDspPurchase2Acc2PidRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "AigcDspPurchase2Acc2PidRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcDspPurchase2Acc2PidRetrieveFlowPythonFunctionSet_gcc_e6042665b7d4c9f99933bf06e2d49e6d.so", "gcc_so_name": "AigcDspPurchase2Acc2PidRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["series_id", "in_sdpa_tag_account_list", "in_sdpa_tag_product_list", "in_sdpa_tag_second_industry_list", "in_sdpa_tag_license_list", "in_aigc_purchase_account_list"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "aigc_dsp_purchase2_acc2pid_photo_recall::_truncate::shuffle_A597DE": {"type_name": "CommonRecoShuffleArranger"}, "aigc_dsp_purchase2_acc2pid_photo_recall::_truncate::truncate_16B4C7": {"size_limit": "{{aigc_maxPurchaseCount}}", "type_name": "CommonRecoTruncateArranger"}, "aigc_dsp_purchase2_acc2pid_photo_recall::return__26F3CB": {"skip": "{{_if_control_attr_23}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "aigc_dsp_purchase_acc2pid_photo_recall::_branch_controller_8A24F361_16": {"$branch_start": "aigc_dsp_purchase_acc2pid_photo_recall::_branch_controller_8A24F361_16", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_24"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "aigc_dsp_purchase_acc2pid_photo_recall::_retrieve::get_common_attr_from_redis_D1CA73": {"cluster_name": "adNieuwlandPrivateMatchDerivePhoto", "is_async": false, "redis_params": [{"key_prefix": "dsp_purchase_acc2pid_", "output_attr_name": "aigc_account_photo_str", "output_attr_type": "string", "redis_key": "{{account_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "aigc_dsp_purchase_acc2pid_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_7E741D": {"input_common_attr": "aigc_account_photo_list", "input_common_attr_type": 3, "reason": 15, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "AigcPhotoListParserUdf"}, "aigc_dsp_purchase_acc2pid_photo_recall::_retrieve::split_string_295FB2": {"delimiters": ",", "input_common_attr": "aigc_account_photo_str", "output_common_attr": "aigc_account_photo_list", "type_name": "CommonRecoStringSplitEnricher"}, "aigc_dsp_purchase_acc2pid_photo_recall::_retrieve_admit::enrich_attr_by_py_753587": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcDspPurchaseAcc2PidRetrieveFlowPythonFunctionSet_clang_cd03cfccc870317f6efd9cc48b1813e7.so", "clang_so_name": "AigcDspPurchaseAcc2PidRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "AigcDspPurchaseAcc2PidRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "AigcDspPurchaseAcc2PidRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcDspPurchaseAcc2PidRetrieveFlowPythonFunctionSet_gcc_38bcae523bc1830b462566ee33b89d2b.so", "gcc_so_name": "AigcDspPurchaseAcc2PidRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["series_id", "in_sdpa_tag_account_list", "in_sdpa_tag_product_list", "in_sdpa_tag_second_industry_list", "in_sdpa_tag_license_list", "in_aigc_purchase_account_list"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "aigc_dsp_purchase_acc2pid_photo_recall::_truncate::shuffle_A597DE": {"type_name": "CommonRecoShuffleArranger"}, "aigc_dsp_purchase_acc2pid_photo_recall::_truncate::truncate_57F3F1": {"size_limit": "{{aigc_maxTemplateCount}}", "type_name": "CommonRecoTruncateArranger"}, "aigc_dsp_purchase_acc2pid_photo_recall::return__51EABB": {"skip": "{{_if_control_attr_24}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "aigc_series_id_photo_recall::_branch_controller_8A24F361_13": {"$branch_start": "aigc_series_id_photo_recall::_branch_controller_8A24F361_13", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_21"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "aigc_series_id_photo_recall::_retrieve::get_common_attr_from_redis_5605A1": {"cluster_name": "adNieuwlandPrivateMatchDerivePhoto", "is_async": false, "redis_params": [{"key_prefix": "dsp_sketch_", "output_attr_name": "aigc_series_photo_str", "output_attr_type": "string", "redis_key": "{{series_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "aigc_series_id_photo_recall::_retrieve::get_common_attr_from_redis_DA3A17": {"cluster_name": "adNieuwlandPrivateMatchDerivePhoto", "is_async": false, "redis_params": [{"key_prefix": "dsp_sketch_", "output_attr_name": "aigc_series_user_photo_str", "output_attr_type": "string", "redis_key": "{{series_user_key}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "aigc_series_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_19378B": {"input_common_attr": "aigc_series_user_photo_list", "input_common_attr_type": 3, "reason": 12, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "AigcPhotoListParserUdf"}, "aigc_series_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_64AB05": {"input_common_attr": "aigc_series_photo_list", "input_common_attr_type": 3, "reason": 12, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "AigcPhotoListParserUdf"}, "aigc_series_id_photo_recall::_retrieve::split_string_602F8A": {"delimiters": ",", "input_common_attr": "aigc_series_photo_str", "output_common_attr": "aigc_series_photo_list", "type_name": "CommonRecoStringSplitEnricher"}, "aigc_series_id_photo_recall::_retrieve::split_string_9563A0": {"delimiters": ",", "input_common_attr": "aigc_series_user_photo_str", "output_common_attr": "aigc_series_user_photo_list", "type_name": "CommonRecoStringSplitEnricher"}, "aigc_series_id_photo_recall::_retrieve::str_format_BCDEC6": {"fill_default_val": true, "format_string": "%ld_%ld", "input_attrs": ["series_id", "user_id"], "output_attr": "series_user_key", "type_name": "CommonRecoStrFormatEnricher"}, "aigc_series_id_photo_recall::_retrieve_admit::enrich_attr_by_py_C12EA2": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcSeriesIdRetrieveFlowPythonFunctionSet_clang_a15006a9f012b7cf4e6342bef3de0353.so", "clang_so_name": "AigcSeriesIdRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "AigcSeriesIdRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "AigcSeriesIdRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcSeriesIdRetrieveFlowPythonFunctionSet_gcc_c4b0af60e5e251ecad71a725ea1fe841.so", "gcc_so_name": "AigcSeriesIdRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["series_id"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "aigc_series_id_photo_recall::return__D2F806": {"skip": "{{_if_control_attr_21}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "aigc_tmall_account_photo_recall::_branch_controller_8A24F361_10": {"$branch_start": "aigc_tmall_account_photo_recall::_branch_controller_8A24F361_10", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_18"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "aigc_tmall_account_photo_recall::_retrieve::get_common_attr_from_redis_847075": {"cluster_name": "adNieuwlandPrivateMatchDerivePhoto", "is_async": false, "redis_params": [{"key_prefix": "dsp_pid2pid_", "output_attr_name": "aigc_tmall_account_photo_id_list_from_redis", "output_attr_type": "string_list", "redis_key": "{{alreadyInsertedPhotoIdList}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "aigc_tmall_account_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_AD0182": {"input_common_attr": "aigc_tmall_account_photo_id_list_from_redis", "input_common_attr_type": 3, "reason": 9, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "AigcPhotoListParserUdf"}, "aigc_tmall_account_photo_recall::_retrieve_admit::enrich_attr_by_py_55FBEA": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcTmallAccountRetrieveFlowPythonFunctionSet_clang_4b385feeb4193b54c849c13c44f01d45.so", "clang_so_name": "AigcTmallAccountRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "AigcTmallAccountRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "AigcTmallAccountRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcTmallAccountRetrieveFlowPythonFunctionSet_gcc_148a9f0eed57f2809f8eb7880de9b0e8.so", "gcc_so_name": "AigcTmallAccountRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["in_tmall_618_account_white_list"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "aigc_tmall_account_photo_recall::return__7126B7": {"skip": "{{_if_control_attr_18}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "aigc_unit_sdpa_photo_recall::_branch_controller_8A24F361_12": {"$branch_start": "aigc_unit_sdpa_photo_recall::_branch_controller_8A24F361_12", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_20"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "aigc_unit_sdpa_photo_recall::_retrieve::get_common_attr_from_redis_03906A": {"cluster_name": "adNieuwlandPrivateMatchDerivePhoto", "is_async": false, "redis_params": [{"key_prefix": "{{dsp_sdpa_account_key}}", "output_attr_name": "sdpa_aigc_photo_id_str2", "output_attr_type": "string", "redis_key": "{{unit_sdpa_id_value}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "aigc_unit_sdpa_photo_recall::_retrieve::get_common_attr_from_redis_198862": {"cluster_name": "adNieuwlandPrivateMatchDerivePhoto", "is_async": false, "redis_params": [{"key_prefix": "{{dsp_sdpa_purchace_account_key}}", "output_attr_name": "sdpa_aigc_photo_id_str", "output_attr_type": "string", "redis_key": "{{unit_sdpa_id_value}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "aigc_unit_sdpa_photo_recall::_retrieve::get_common_attr_from_redis_DCAD71": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "uax_unit_sdpa_", "output_attr_name": "unit_sdpa_id_value", "output_attr_type": "string", "redis_key": "{{unit_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "aigc_unit_sdpa_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_5306F6": {"input_common_attr": "sdpa_aigc_photo_id_list", "input_common_attr_type": 3, "reason": 11, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "AigcPhotoListParserUdf"}, "aigc_unit_sdpa_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_C2B047": {"input_common_attr": "sdpa_aigc_photo_id_list2", "input_common_attr_type": 3, "reason": 11, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "AigcPhotoListParserUdf"}, "aigc_unit_sdpa_photo_recall::_retrieve::split_string_7B5498": {"delimiters": ",", "input_common_attr": "sdpa_aigc_photo_id_str", "output_common_attr": "sdpa_aigc_photo_id_list", "type_name": "CommonRecoStringSplitEnricher"}, "aigc_unit_sdpa_photo_recall::_retrieve::split_string_BE5592": {"delimiters": ",", "input_common_attr": "sdpa_aigc_photo_id_str2", "output_common_attr": "sdpa_aigc_photo_id_list2", "type_name": "CommonRecoStringSplitEnricher"}, "aigc_unit_sdpa_photo_recall::_retrieve::str_format_231B1F": {"fill_default_val": true, "format_string": "dsp_sdpa_%ld_", "input_attrs": ["account_id"], "output_attr": "dsp_sdpa_account_key", "type_name": "CommonRecoStrFormatEnricher"}, "aigc_unit_sdpa_photo_recall::_retrieve::str_format_562979": {"fill_default_val": true, "format_string": "dsp_sdpa_purchace_%ld_", "input_attrs": ["account_id"], "output_attr": "dsp_sdpa_purchace_account_key", "type_name": "CommonRecoStrFormatEnricher"}, "aigc_unit_sdpa_photo_recall::_retrieve_admit::enrich_attr_by_py_D0EAA4": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcUnitSdpaRetrieveFlowPythonFunctionSet_clang_abc8ca8fc4bc09fdae9627cac8b63e9d.so", "clang_so_name": "AigcUnitSdpaRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "AigcUnitSdpaRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "AigcUnitSdpaRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/AigcUnitSdpaRetrieveFlowPythonFunctionSet_gcc_7277bf1761a4f6411dbab725c53d37ac.so", "gcc_so_name": "AigcUnitSdpaRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["series_id", "in_sdpa_tag_account_list", "in_sdpa_tag_product_list", "in_sdpa_tag_second_industry_list", "in_sdpa_tag_license_list"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "aigc_unit_sdpa_photo_recall::return__E72A5F": {"skip": "{{_if_control_attr_20}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "get_package_conversion_predict_result::package_conversion_predict_enricher_92F3CA": {"input_attr": {"account_id_column": "account_id", "budget_column": "budget", "campaign_id_column": "campaign_id", "campaign_type_column": "campaign_type", "deep_conversion_type_column": "deep_conversion_type", "first_industry_name_column": "first_industry_name", "ocpc_action_type_column": "ocpc_action_type", "periodic_days_column": "periodic_days", "product_name_column": "product_name", "second_industry_name_column": "second_industry_name"}, "output_attr": {"max_conversion_count_column": "max_conversion_count", "min_conversion_count_column": "min_conversion_count"}, "type_name": "PackageConversionPredictEnricher"}, "guaranteed_photo_recall::_branch_controller_8A24F361_4": {"$branch_start": "guaranteed_photo_recall::_branch_controller_8A24F361_4", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_12"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "guaranteed_photo_recall::_retrieve::get_common_attr_from_redis_9F7A11": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "uax_recall_photo_dim_account_id_guaranteed_1_", "output_attr_name": "uax_recall_photo_dim_account_json_value", "output_attr_type": "string", "redis_key": "{{account_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "guaranteed_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_DD5405": {"input_common_attr": "uax_recall_photo_dim_account_json_value", "input_common_attr_type": 0, "reason": 7, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "SelectedOptimisedPhotoParserUdf"}, "guaranteed_photo_recall::_retrieve_admit::enrich_attr_by_py_84E5C2": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/GuaranteedPhotoResultsRetrieveFlowPythonFunctionSet_clang_0767f88392c0c71c492abc1f2c8a26c4.so", "clang_so_name": "GuaranteedPhotoResultsRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "GuaranteedPhotoResultsRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "GuaranteedPhotoResultsRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/GuaranteedPhotoResultsRetrieveFlowPythonFunctionSet_gcc_85933665c9b9c14eab51615ee617e160.so", "gcc_so_name": "GuaranteedPhotoResultsRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["package_recall_photo_retrieve_admit", "enable_photo_recall_explore_guaranteed"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "guaranteed_photo_recall::return__922D6C": {"skip": "{{_if_control_attr_12}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "model_recall_recall::_branch_controller_8A24F361": {"$branch_start": "model_recall_recall::_branch_controller_8A24F361", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_1"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "model_recall_recall::_retrieve::get_common_attr_from_redis_493E77": {"cluster_name": "kcache_uaxRecallPhotos", "is_async": false, "redis_params": [{"key_prefix": "uax_infra_allocation-", "output_attr_name": "model_predict_photo_json_value", "output_attr_type": "string", "redis_key": "{{account_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "model_recall_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_9EBF43": {"input_common_attr": "model_predict_photo_json_value", "input_common_attr_type": 0, "reason": 1, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "ModelPredictJsonResultParseUdf"}, "model_recall_recall::_retrieve_admit::enrich_attr_by_py_6144E5": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/ModelPredictPhotoRetrieveFlowPythonFunctionSet_clang_a771fb6bdaf553460ab7a110735c8631.so", "clang_so_name": "ModelPredictPhotoRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "ModelPredictPhotoRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "ModelPredictPhotoRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/ModelPredictPhotoRetrieveFlowPythonFunctionSet_gcc_1865a3eee0948a8aa421b8a025426da7.so", "gcc_so_name": "ModelPredictPhotoRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": [], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "model_recall_recall::_rule_filter::aigc_black_list_filter::_branch_controller_09D867B0": {"$branch_start": "model_recall_recall::_rule_filter::aigc_black_list_filter::_branch_controller_09D867B0", "$code_info": "[if] 09D867B0 model_predict_photo_retrieve_flow.py in is_in_aigc_black_list(): .if_(\"account_id_in_aigc_black_list == 1 or product_name_in_black_list == 1\")", "export_common_attr": ["_if_control_attr_3"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["account_id_in_aigc_black_list", "product_name_in_black_list"], "lua_script": "function evaluate() if (account_id_in_aigc_black_list == 1 or product_name_in_black_list == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "model_recall_recall::_rule_filter::aigc_black_list_filter::_branch_controller_436026F2": {"$branch_start": "model_recall_recall::_rule_filter::aigc_black_list_filter::_branch_controller_436026F2", "$code_info": "[if] 436026F2 model_predict_photo_retrieve_flow.py in is_in_aigc_black_list(): .if_(\"account_id_in_enable_tag_list == 1 or account_id_in_tmall_618_list == 1\")", "export_common_attr": ["_if_control_attr_4"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["account_id_in_enable_tag_list", "account_id_in_tmall_618_list"], "lua_script": "function evaluate() if (account_id_in_enable_tag_list == 1 or account_id_in_tmall_618_list == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "model_recall_recall::_rule_filter::aigc_black_list_filter::_branch_controller_7F9EAA2F": {"$branch_start": "model_recall_recall::_rule_filter::aigc_black_list_filter::_branch_controller_7F9EAA2F", "$code_info": "[if] 7F9EAA2F model_predict_photo_retrieve_flow.py in aigc_black_list_filter(): .if_(\"account_in_black_list == 1\")", "export_common_attr": ["_if_control_attr_5"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["account_in_black_list"], "lua_script": "function evaluate() if (account_in_black_list == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "model_recall_recall::_rule_filter::aigc_black_list_filter::enrich_attr_by_lua_09A2FB": {"export_common_attr": ["account_in_black_list"], "function_for_common": "accountInBlackList", "lua_script": "function accountInBlackList()\n              return 1\n            end", "skip": "{{_if_control_attr_3}}", "type_name": "CommonRecoLuaAttrEnricher"}, "model_recall_recall::_rule_filter::aigc_black_list_filter::enrich_attr_by_lua_73993A": {"export_common_attr": ["account_in_black_list"], "function_for_common": "accountInBlackList", "lua_script": "function accountInBlackList()\n              return 1\n            end", "skip": "{{_if_control_attr_4}}", "type_name": "CommonRecoLuaAttrEnricher"}, "model_recall_recall::_rule_filter::aigc_black_list_filter::filter_by_rule_6C0195": {"rule": {"attr_name": "photo_source", "compare_to": 16, "remove_if": "==", "remove_if_attr_missing": false}, "skip": "{{_if_control_attr_5}}", "type_name": "CommonRecoRuleFilterArranger"}, "model_recall_recall::_rule_filter::aigc_black_list_filter::lookup_kconf_305D2B": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildAIGCBlackList", "lookup_attr": "account_id", "output_attr": "account_id_in_aigc_black_list", "value_type": "set_int64"}, {"is_common_attr": true, "kconf_key": "ad.algorithm.nieuwland_black_product_name_list", "lookup_attr": "product_name", "output_attr": "product_name_in_black_list", "value_type": "set_string"}, {"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildEnableTagAccountId", "lookup_attr": "account_id", "output_attr": "account_id_in_enable_tag_list", "value_type": "tail_number"}, {"is_common_attr": true, "kconf_key": "ad.algorithm.tmall618A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_attr": "account_id", "output_attr": "account_id_in_tmall_618_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "model_recall_recall::_rule_filter::aigc_black_list_filter::return__D3550F": {"skip": "{{_if_control_attr_4}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "model_recall_recall::_rule_filter::aigc_black_list_filter::return__D6F978": {"skip": "{{_if_control_attr_3}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "model_recall_recall::_rule_filter::photo_id_black_list_filter::filter_by_rule_49321E": {"rule": {"attr_name": "photo_id_in_black_list", "compare_to": 1, "remove_if": "==", "remove_if_attr_missing": false}, "type_name": "CommonRecoRuleFilterArranger"}, "model_recall_recall::_rule_filter::photo_id_black_list_filter::lookup_kconf_08818B": {"kconf_configs": [{"is_common_attr": false, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildPhotoBlackListForSelected", "lookup_attr": "photo_id", "output_attr": "photo_id_in_black_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "model_recall_recall::_rule_filter::user_id_mismatch_filter::_branch_controller_6F5F875C": {"$branch_start": "model_recall_recall::_rule_filter::user_id_mismatch_filter::_branch_controller_6F5F875C", "$code_info": "[if] 6F5F875C model_predict_photo_retrieve_flow.py in user_id_mismatch_filter(): .if_(\"kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_enableFilterUseridMismatch == true\")", "export_common_attr": ["_if_control_attr_2"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_enableFilterUseridMismatch"], "lua_script": "function evaluate() if (kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_enableFilterUseridMismatch == true) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "model_recall_recall::_rule_filter::user_id_mismatch_filter::ad_enrich_common_attr_by_kconf_1D5C42": {"common_attr_prefix": "kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_", "kconf_key": "ad.algorithm.AdSmartUaxModelDecisionKconfConfig", "type_name": "AdKconfAttrEnricher"}, "model_recall_recall::_rule_filter::user_id_mismatch_filter::filter_by_rule_1EAA86": {"rule": {"attr_name": "photo_user_id", "compare_to": "{{user_id}}", "remove_if": "!=", "remove_if_attr_missing": false}, "skip": "{{_if_control_attr_2}}", "type_name": "CommonRecoRuleFilterArranger"}, "model_recall_recall::_sort::sort_by_score_407B25": {"attr": "reward", "desc": true, "stable_sort": true, "type_name": "CommonRecoScoreSortArranger"}, "model_recall_recall::_truncate::truncate_C2ED74": {"size_limit": "{{kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_subPhotoResultQuota}}", "type_name": "CommonRecoTruncateArranger"}, "model_recall_recall::return__38295C": {"skip": "{{_if_control_attr_1}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "native_account_photo_recall::_branch_controller_8A24F361_3": {"$branch_start": "native_account_photo_recall::_branch_controller_8A24F361_3", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_11"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "native_account_photo_recall::_retrieve::get_common_attr_from_redis_F170D8": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "uax_recall_photo_dim_photo_user_filter_account_1_", "output_attr_name": "uax_recall_photo_dim_native_account_json_value", "output_attr_type": "string", "redis_key": "{{account_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "native_account_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_C1758E": {"input_common_attr": "uax_recall_photo_dim_account_json_value", "input_common_attr_type": 0, "reason": 2, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "SelectedOptimisedPhotoParserUdf"}, "native_account_photo_recall::_retrieve_admit::enrich_attr_by_py_6BAE5D": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/NativeAccountPhotoRetrieveFlowPythonFunctionSet_clang_d2f561914452f5d3d75b3fa3d1df8fd2.so", "clang_so_name": "NativeAccountPhotoRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "NativeAccountPhotoRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "NativeAccountPhotoRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/NativeAccountPhotoRetrieveFlowPythonFunctionSet_gcc_7e83941de215fdc2ddb64f55f025bf7d.so", "gcc_so_name": "NativeAccountPhotoRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["package_recall_photo_retrieve_admit", "enable_photo_recall_account", "in_native_recall_account_white_list"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "native_account_photo_recall::return__4B35E8": {"skip": "{{_if_control_attr_11}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "new_photo_recall::_branch_controller_8A24F361_1": {"$branch_start": "new_photo_recall::_branch_controller_8A24F361_1", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_6"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "new_photo_recall::_retrieve::get_common_attr_from_redis_378BF7": {"cluster_name": "kcache_uaxRecallPhotos", "is_async": false, "redis_params": [{"key_prefix": "uax_model_decision_new_photo_", "output_attr_name": "model_predict_new_photo_json_value", "output_attr_type": "string", "redis_key": "{{account_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "new_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_C2126E": {"input_common_attr": "model_predict_new_photo_json_value", "input_common_attr_type": 0, "reason": 2, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "ModelPredictNewPhotoJsonResultParseUdf"}, "new_photo_recall::_retrieve_admit::ad_enrich_common_attr_by_kconf_1D5C42": {"common_attr_prefix": "kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_", "kconf_key": "ad.algorithm.AdSmartUaxModelDecisionKconfConfig", "type_name": "AdKconfAttrEnricher"}, "new_photo_recall::_retrieve_admit::enrich_attr_by_py_C599A4": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/NewPhotoRetrieveFlowPythonFunctionSet_clang_fd138120856556c6cca961beb251f042.so", "clang_so_name": "NewPhotoRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "NewPhotoRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "NewPhotoRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/NewPhotoRetrieveFlowPythonFunctionSet_gcc_0040247dc4502d6373c15b24e690eec3.so", "gcc_so_name": "NewPhotoRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_exploreRate"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "new_photo_recall::_rule_filter::black_new_photo_filter::_branch_controller_492A71AC": {"$branch_start": "new_photo_recall::_rule_filter::black_new_photo_filter::_branch_controller_492A71AC", "$code_info": "[if] 492A71AC new_photo_retrieve_flow.py in black_new_photo_filter(): .if_(\"account_in_new_photo_black_list == 1\")", "export_common_attr": ["_if_control_attr_7"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["account_in_new_photo_black_list"], "lua_script": "function evaluate() if (account_in_new_photo_black_list == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "new_photo_recall::_rule_filter::black_new_photo_filter::enrich_attr_by_py_EF6A1E": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/NewPhotoRetrieveFlowPythonFunctionSet_clang_fd138120856556c6cca961beb251f042.so", "clang_so_name": "NewPhotoRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "NewPhotoRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["account_in_new_photo_black_list"], "export_item_attr": [], "function_set": "NewPhotoRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/NewPhotoRetrieveFlowPythonFunctionSet_gcc_0040247dc4502d6373c15b24e690eec3.so", "gcc_so_name": "NewPhotoRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_enableBlackNewPhoto", "kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_accountBlackList", "kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_firstIndustryBlackList", "kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_secondIndustryBlackList", "kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_productBlackList", "kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_corporationBlackList", "account_id", "first_industry_name", "second_industry_name", "product_name", "corporation_name"], "import_item_attr": [], "py_function": "is_in_new_photo_black_list", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "new_photo_recall::_rule_filter::black_new_photo_filter::filter_by_rule_A8AFEE": {"rule": {"attr_name": "photo_source", "compare_to": 16, "remove_if": "!=", "remove_if_attr_missing": false}, "skip": "{{_if_control_attr_7}}", "type_name": "CommonRecoRuleFilterArranger"}, "new_photo_recall::_truncate::shuffle_A597DE": {"type_name": "CommonRecoShuffleArranger"}, "new_photo_recall::_truncate::truncate_D52A1B": {"size_limit": "{{kconf_ad_algorithm_AdSmartUaxModelDecisionKconfConfig_exploreNewPhotoQuota}}", "type_name": "CommonRecoTruncateArranger"}, "new_photo_recall::return__70F737": {"skip": "{{_if_control_attr_6}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "package_photo_recall::_branch_controller_8A24F361_6": {"$branch_start": "package_photo_recall::_branch_controller_8A24F361_6", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_14"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "package_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_E4A0F0": {"input_common_attr": "photo_packages_list", "input_common_attr_type": 0, "reason": 3, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "PackagePhotoParserUdf"}, "package_photo_recall::_retrieve_admit::enrich_attr_by_py_10436E": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/PackagePhotoRetrieveFlowPythonFunctionSet_clang_0d4f01435771eb52bafcb9186cfd1ddb.so", "clang_so_name": "PackagePhotoRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "PackagePhotoRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "PackagePhotoRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/PackagePhotoRetrieveFlowPythonFunctionSet_gcc_9fc5bae727b8753a20060ebc7352ce88.so", "gcc_so_name": "PackagePhotoRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["enable_photo_package", "photo_packages_list"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "package_photo_recall::return__870069": {"skip": "{{_if_control_attr_14}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "post_data_proc_model_decision_predict_photo_select_flow::build_protobuf_54B50A": {"class_name": "kuaishou.negative.GetByPhotoIdsRequest", "inputs": [{"append": true, "item_attr": "photo_id", "path": "photo_id"}], "output_common_attr": "get_by_photo_ids_request", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "post_data_proc_model_decision_predict_photo_select_flow::calc_photo_property::enrich_attr_by_py_9A98B1": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/BasePhotoSelectFlowPythonFunctionSet_clang_6bc4088aee67ef1c84dfde61c32e7e28.so", "clang_so_name": "BasePhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "BasePhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": [], "export_item_attr": ["creative_material_type", "creative_type"], "function_set": "BasePhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/BasePhotoSelectFlowPythonFunctionSet_gcc_26e8a3a833f7c7cf3c2c8cf6b744b0e9.so", "gcc_so_name": "BasePhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["is_in_smart_adv_white_list", "kconf_ad_algorithm_AdSmartDspAutoBuildProgrammaticRatio"], "import_item_attr": ["cover_width", "cover_height", "photo_source"], "py_function": "calc_photo_property_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_data_proc_model_decision_predict_photo_select_flow::calc_photo_property::get_kconf_params_9247E2": {"kconf_configs": [{"default_value": 0.5, "export_common_attr": "kconf_ad_algorithm_AdSmartDspAutoBuildProgrammaticRatio", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildProgrammaticRatio", "value_type": "double"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_model_decision_predict_photo_select_flow::calc_photo_property::is_smart_adv_white_user::enrich_attr_by_py_C24D2E": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/BasePhotoSelectFlowPythonFunctionSet_clang_6bc4088aee67ef1c84dfde61c32e7e28.so", "clang_so_name": "BasePhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "BasePhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["is_in_smart_adv_white_list"], "export_item_attr": [], "function_set": "BasePhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/BasePhotoSelectFlowPythonFunctionSet_gcc_26e8a3a833f7c7cf3c2c8cf6b744b0e9.so", "gcc_so_name": "BasePhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["kconf_is_in_smart_adv_account_white_list", "kconf_is_in_smart_adv_product_white_list", "kconf_is_in_smart_adv_license_white_list", "kconf_is_in_smart_adv_second_industry_white_list", "kconf_is_in_smart_adv_first_industry_white_list"], "import_item_attr": [], "py_function": "isSmartAdvWhiteUser", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_data_proc_model_decision_predict_photo_select_flow::calc_photo_property::is_smart_adv_white_user::lookup_kconf_FB591B": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeAccountSwitch", "lookup_attr": "account_id", "output_attr": "kconf_is_in_smart_adv_account_white_list", "value_type": "tail_number"}, {"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeProductSwitch", "lookup_attr": "product_name", "output_attr": "kconf_is_in_smart_adv_product_white_list", "value_type": "set_string"}, {"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeLicenseSwitch", "lookup_attr": "license_no", "output_attr": "kconf_is_in_smart_adv_license_white_list", "value_type": "set_string"}, {"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeSecondIndustrySwitch", "lookup_attr": "second_industry_name", "output_attr": "kconf_is_in_smart_adv_second_industry_white_list", "value_type": "set_string"}, {"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeFirstIndustrySwitch", "lookup_attr": "license_no", "output_attr": "kconf_is_in_smart_adv_first_industry_white_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_model_decision_predict_photo_select_flow::deduplicate_69DB73": {"type_name": "CommonRecoResultsDeduplicateArranger"}, "post_data_proc_model_decision_predict_photo_select_flow::enrich_by_generic_grpc_ECFC53": {"downstream_processor": "post_data_proc_model_decision_predict_photo_select_flow::enrich_item_attr_by_common_attr_20B9C0", "kess_service": "grpc_apiCorePhotoService", "method_name": "/kuaishou.negative.PhotoServiceRpc/GetByIdsFailFast", "request_attr": "get_by_photo_ids_request", "response_attr": "get_by_photo_ids_response", "response_class": "kuaishou.negative.PhotoMapResponse", "timeout_ms": 200, "type_name": "CommonRecoGenericGrpcEnricher"}, "post_data_proc_model_decision_predict_photo_select_flow::enrich_item_attr_by_common_attr_20B9C0": {"input_common_attr": "get_by_photo_ids_response", "input_common_attr_type": 4, "type_name": "EnrichItemAttrByCommonAttr", "udf_name": "GetByPhotoIdsResponseParserUdf"}, "post_data_proc_model_decision_predict_photo_select_flow::query_mix_asset_meta_by_rpc::build_mix_asset_request_F5154A": {"output_column_name": "mix_asset_meta_request", "photo_id": "photo_id", "type_name": "BuildMixAssetRequest", "video_id": "video_id"}, "post_data_proc_model_decision_predict_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_by_generic_grpc_7BE875": {"downstream_processor": "post_data_proc_model_decision_predict_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_item_attr_by_common_attr_F2CEBB", "kess_service": "grpc_videoProjectionService", "method_name": "/kuaishou.video.projection.VideoProjectionService/GetMixAssetMeta", "request_attr": "mix_asset_meta_request", "response_attr": "mix_asset_meta_response", "response_class": "kuaishou.video.projection.MixAssetMetaResponse", "timeout_ms": 200, "type_name": "CommonRecoGenericGrpcEnricher"}, "post_data_proc_model_decision_predict_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_item_attr_by_common_attr_F2CEBB": {"input_common_attr": "mix_asset_meta_response", "input_common_attr_type": 4, "type_name": "EnrichItemAttrByCommonAttr", "udf_name": "MixAssetMetaResponseParserUdf"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::cast_attr_type_8BD278": {"attr_type_cast_configs": [{"from_common_attr": "AdSmartPredictCostKconfConfig_bucketRatio", "to_common_attr": "AdSmartPredictCostKconfConfig_bucketRatio_int", "to_type": "int"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::cast_attr_type_8C41FB": {"attr_type_cast_configs": [{"from_item_attr": "photo_id", "to_item_attr": "photo_id_str", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_json_37D0BF": {"json_attr": "adNieuwlandPrivateMatchDerivePhoto_json_str", "json_configs": [{"default_value": 0.0, "export_item_attr": "creative_cnt", "json_path": "creative_cnt"}, {"default_value": 0.0, "export_item_attr": "impress_cnt", "json_path": "impr"}, {"default_value": 0.0, "export_item_attr": "conversion_cnt", "json_path": "conversion"}, {"default_value": "", "export_item_attr": "predicted_score_json_str", "json_path": "predicted_score"}], "json_from_item_attr": true, "type_name": "CommonRecoJsonStringAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_json_757086": {"json_attr": "uax_aigc_predict_json_str", "json_configs": [{"default_value": 0.0, "export_common_attr": "uax_aigc_pred_cost_thresh", "json_path": "pred_cost"}], "type_name": "CommonRecoJsonStringAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_json_A5A60C": {"json_attr": "uax_predict_cost_score_json_str", "json_configs": [{"default_value": -100000.0, "export_item_attr": "s8", "json_path": "s8"}, {"default_value": 0.0001, "export_item_attr": "s9", "json_path": "s9"}], "json_from_item_attr": true, "type_name": "CommonRecoJsonStringAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_json_E1336E": {"json_attr": "predicted_score_json_str", "json_configs": [{"default_value": 0.0, "export_item_attr": "s3", "json_path": "s3"}, {"default_value": 0.0, "export_item_attr": "s6", "json_path": "s6"}, {"default_value": 0.0, "export_item_attr": "s7", "json_path": "s7"}], "json_from_item_attr": true, "type_name": "CommonRecoJsonStringAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_py_4C542E": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_clang_38acbe0beb075d4b6d3db5ee929e614b.so", "clang_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": [], "export_item_attr": ["adNieuwlandPrivateMatchDerivePhoto_prefix"], "function_set": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc_905b92687a1b9894ea9449f9a1a90eb7.so", "gcc_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["needCreativeType"], "import_item_attr": [], "py_function": "get_prefix_for_item", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::get_common_attr_from_redis_61D0DA": {"$eval_common_attrs": ["AdSmartPredictCostKconfConfig_bucketRatio_int"], "cluster_name": "adMobileDataVisitors", "redis_params": [{"key_prefix": "{{return 'uax_aigc_predict_quantile_v2_p' .. AdSmartPredictCostKconfConfig_bucketRatio_int .. '_'}}", "output_attr_name": "uax_aigc_predict_json_str", "redis_key": "{{account_id}}"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::get_item_attr_from_redis_56E988": {"$eval_common_attrs": ["account_id"], "cluster_name": "adMobileDataVisitors", "is_async": false, "key_prefix": "{{return 'uax_predict_cost_score_' .. account_id .. '_'}}", "redis_key_from": "photo_id_str", "save_value_to": "uax_predict_cost_score_json_str", "type_name": "CommonRecoRedisItemAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::get_item_attr_from_redis_BCF045": {"cluster_name": "adNieuwlandPrivateMatchDerivePhoto", "is_async": false, "key_prefix": "{{adNieuwlandPrivateMatchDerivePhoto_prefix}}", "redis_key_from": "photo_id_str", "save_value_to": "adNieuwlandPrivateMatchDerivePhoto_json_str", "type_name": "CommonRecoRedisItemAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::get_kconf_params_2F3F2B": {"$eval_common_attrs": ["exp_partten"], "kconf_configs": [{"default_value": 70.0, "export_common_attr": "AdSmartPredictCostKconfConfig_bucketRatio", "json_path": "{{return exp_partten .. '.bucketRatio'}}", "kconf_key": "ad.algorithm.AdSmartPredictCostKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::split_aigc_tables_D6D053": {"item_table": "aigc_table", "select_attr": ["photo_id", "s3", "s6", "s7", "s8", "s9", "uax_aigc_pred_cost_thresh"], "type_name": "SplitAigcTablesMixer"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::_sort_aigc_exploit::photo_sort_1DB016": {"filter_reason": "photo_sort", "item_table": "aigc_exploit_table", "type_name": "PhotoSort<PERSON><PERSON>nger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_creative_bound_filter::cast_attr_type_B35DC0": {"attr_type_cast_configs": [{"from_common_attr": "aigc_creative_upper_bound", "to_common_attr": "aigc_creative_upper_bound_int", "to_type": "int"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_creative_bound_filter::filter_by_rule_402FAC": {"filter_reason": "aigc_creative_bound_filter", "rule": {"attr_name": "creative_cnt", "compare_to": "{{aigc_creative_upper_bound_int}}", "remove_if": ">", "remove_if_attr_missing": false}, "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_creative_bound_filter::get_kconf_params_0433A0": {"kconf_configs": [{"default_value": 150, "export_common_attr": "aigc_creative_upper_bound", "json_path": "CreativeUpperBound", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_photo_unit_divisity_filter::_branch_controller_077695C8_3": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_photo_unit_divisity_filter::_branch_controller_077695C8_3", "$code_info": "[if] 077695C8 system_supplement_flow.py in aigc_photo_unit_divisity_filter(): .if_(\"request_type ~= 1\")", "export_common_attr": ["_if_control_attr_47"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["request_type"], "lua_script": "function evaluate() if (request_type ~= 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_photo_unit_divisity_filter::filter_by_common_attr_5FF049": {"common_attr": ["onlineAIGCCreativesList"], "exclude": true, "filter_reason": "aigc_photo_unit_divisity_filter", "on_item_attr": "photo_id", "skip": "{{_if_control_attr_47}}", "type_name": "CommonRecoCommonAttrFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::model_cmd_enricher_5505AA": {"ad_item_id": "photo_id", "cmd_configs": [{"cmd_key": "{{cmd_key}}", "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9", "cmd_value_num": "{{cmd_value_num}}", "predict_attrs": [{"attr_name": "score", "predict_type": "{{predict_type}}"}]}], "item_table": "aigc_exploit_table", "kess_service": "grpc_adI18nPsDspNNRouterServerNew", "service_group": "PRODUCTION", "timeout_ms": 100, "type_name": "ModelCmdEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::_sort_aigc_explore::photo_sort_7FD3E7": {"filter_reason": "photo_sort", "item_table": "aigc_explore_table", "type_name": "PhotoSort<PERSON><PERSON>nger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_creative_bound_filter::cast_attr_type_B35DC0": {"attr_type_cast_configs": [{"from_common_attr": "aigc_creative_upper_bound", "to_common_attr": "aigc_creative_upper_bound_int", "to_type": "int"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_creative_bound_filter::filter_by_rule_402FAC": {"filter_reason": "aigc_creative_bound_filter", "rule": {"attr_name": "creative_cnt", "compare_to": "{{aigc_creative_upper_bound_int}}", "remove_if": ">", "remove_if_attr_missing": false}, "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_creative_bound_filter::get_kconf_params_0433A0": {"kconf_configs": [{"default_value": 150, "export_common_attr": "aigc_creative_upper_bound", "json_path": "CreativeUpperBound", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_photo_unit_divisity_filter::_branch_controller_077695C8_5": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_photo_unit_divisity_filter::_branch_controller_077695C8_5", "$code_info": "[if] 077695C8 system_supplement_flow.py in aigc_photo_unit_divisity_filter(): .if_(\"request_type ~= 1\")", "export_common_attr": ["_if_control_attr_49"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["request_type"], "lua_script": "function evaluate() if (request_type ~= 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_photo_unit_divisity_filter::filter_by_common_attr_C512E6": {"common_attr": ["onlineAIGCCreativesList"], "exclude": true, "filter_reason": "aigc_photo_unit_divisity_filter", "on_item_attr": "photo_id", "skip": "{{_if_control_attr_49}}", "type_name": "CommonRecoCommonAttrFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::model_cmd_enricher_E2228D": {"ad_item_id": "photo_id", "cmd_configs": [{"cmd_key": "{{cmd_key}}", "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9", "cmd_value_num": "{{cmd_value_num}}", "predict_attrs": [{"attr_name": "score", "predict_type": "{{predict_type}}"}]}], "item_table": "aigc_explore_table", "kess_service": "grpc_adI18nPsDspNNRouterServerNew", "service_group": "PRODUCTION", "timeout_ms": 100, "type_name": "ModelCmdEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::_sort_aigc_new_photo::photo_sort_00CC00": {"filter_reason": "photo_sort", "item_table": "aigc_new_photo_table", "type_name": "PhotoSort<PERSON><PERSON>nger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_creative_bound_filter::cast_attr_type_B35DC0": {"attr_type_cast_configs": [{"from_common_attr": "aigc_creative_upper_bound", "to_common_attr": "aigc_creative_upper_bound_int", "to_type": "int"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_creative_bound_filter::filter_by_rule_402FAC": {"filter_reason": "aigc_creative_bound_filter", "rule": {"attr_name": "creative_cnt", "compare_to": "{{aigc_creative_upper_bound_int}}", "remove_if": ">", "remove_if_attr_missing": false}, "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_creative_bound_filter::get_kconf_params_0433A0": {"kconf_configs": [{"default_value": 150, "export_common_attr": "aigc_creative_upper_bound", "json_path": "CreativeUpperBound", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_photo_unit_divisity_filter::_branch_controller_077695C8_4": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_photo_unit_divisity_filter::_branch_controller_077695C8_4", "$code_info": "[if] 077695C8 system_supplement_flow.py in aigc_photo_unit_divisity_filter(): .if_(\"request_type ~= 1\")", "export_common_attr": ["_if_control_attr_48"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["request_type"], "lua_script": "function evaluate() if (request_type ~= 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_photo_unit_divisity_filter::filter_by_common_attr_2FE0B1": {"common_attr": ["onlineAIGCCreativesList"], "exclude": true, "filter_reason": "aigc_photo_unit_divisity_filter", "on_item_attr": "photo_id", "skip": "{{_if_control_attr_48}}", "type_name": "CommonRecoCommonAttrFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::model_cmd_enricher_B6DB5F": {"ad_item_id": "photo_id", "cmd_configs": [{"cmd_key": "{{cmd_key}}", "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9", "cmd_value_num": "{{cmd_value_num}}", "predict_attrs": [{"attr_name": "score", "predict_type": "{{predict_type}}"}]}], "item_table": "aigc_new_photo_table", "kess_service": "grpc_adI18nPsDspNNRouterServerNew", "service_group": "PRODUCTION", "timeout_ms": 100, "type_name": "ModelCmdEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::create_logic_table_0F6535": {"item_table": "aigc_explore_table", "logic_table": "logic_aigc_explore_table", "select_attr": ["photo_id"], "type_name": "CommonRecoCreateLogicTableMixer"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::create_logic_table_55D657": {"item_table": "aigc_exploit_table", "logic_table": "logic_aigc_exploit_table", "select_attr": ["photo_id"], "type_name": "CommonRecoCreateLogicTableMixer"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::create_logic_table_F0F1D0": {"item_table": "aigc_new_photo_table", "logic_table": "logic_aigc_new_photo_table", "select_attr": ["photo_id"], "type_name": "CommonRecoCreateLogicTableMixer"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::deduplicate_69DB73": {"type_name": "CommonRecoResultsDeduplicateArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::split_aigc_tables_C82A85": {"item_table": "aigc_table", "select_attr": ["photo_id", "s3", "s6", "s7", "s8", "s9"], "to_table_names": ["aigc_exploit_table", "aigc_explore_table", "aigc_new_photo_table"], "type_name": "SplitAigcTablesMixer"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::_sort_guarantee::photo_sort_9900A0": {"filter_reason": "photo_sort", "item_table": "guaranteed_table", "type_name": "PhotoSort<PERSON><PERSON>nger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::_branch_controller_EB30DF95_1": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::_branch_controller_EB30DF95_1", "$code_info": "[if] EB30DF95 system_supplement_flow.py in aigc_photo_blacklist_filter(): .if_(\"in_aigc_photo_blacklist == 1\")", "export_common_attr": ["_if_control_attr_37"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["in_aigc_photo_blacklist"], "lua_script": "function evaluate() if (in_aigc_photo_blacklist == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::cast_attr_type_44DD6E": {"attr_type_cast_configs": [{"from_common_attr": "aigc_second_industry_medical_white_list_json", "to_common_attr": "aigc_second_industry_medical_white_list", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::cast_attr_type_FB3236": {"attr_type_cast_configs": [{"from_common_attr": "aigc_first_industry_black_list_json", "to_common_attr": "aigc_first_industry_black_list", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::enrich_attr_by_py_58C64C": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_clang_38acbe0beb075d4b6d3db5ee929e614b.so", "clang_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["in_aigc_photo_blacklist"], "export_item_attr": [], "function_set": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc_905b92687a1b9894ea9449f9a1a90eb7.so", "gcc_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["campaign_type", "book_id", "series_id", "first_industry_name", "second_industry_name", "in_aigc_account_blacklist", "in_aigc_product_name_blacklist", "in_aigc_license_blacklist", "enable_tag_black_aigc_account", "in_tag_black_aigc_license_list", "in_tag_black_aigc_product_list", "in_tmall618_account_whitelist", "aigc_first_industry_black_list", "aigc_second_industry_medical_white_list", "enable_medical_white_list", "in_medical_account_white_list"], "import_item_attr": [], "py_function": "in_aigc_photo_blacklist_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::filter_by_rule_6E0D92": {"filter_reason": "aigc_photo_blacklist_filter", "rule": {"attr_name": "photo_source", "compare_to": 16, "remove_if": "==", "remove_if_attr_missing": false}, "skip": "{{_if_control_attr_37}}", "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::get_kconf_params_017CD9": {"kconf_configs": [{"export_common_attr": "aigc_second_industry_medical_white_list_json", "json_path": "aigcSecondIndustryMedicalWhiteList", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::get_kconf_params_03F594": {"kconf_configs": [{"export_common_attr": "aigc_first_industry_black_list_json", "json_path": "aigcFirstIndustryBlackList", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::get_kconf_params_3DB952": {"kconf_configs": [{"export_common_attr": "enable_medical_white_list", "json_path": "enableMedicalWhiteList", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_3AEFD8": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildEnableTagAccountId", "lookup_attr": "account_id", "output_attr": "enable_tag_black_aigc_account", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_83438C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.medicalReviewAccount<PERSON><PERSON><PERSON>st", "lookup_attr": "account_id", "output_attr": "in_medical_account_white_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_86F279": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.tmall618A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_attr": "account_id", "output_attr": "in_tmall618_account_whitelist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_8A7801": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.photoTagProductWhiteList", "lookup_attr": "license_no", "output_attr": "in_tag_black_aigc_product_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_C60DFE": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.photoTagLicenseWhiteList", "lookup_attr": "license_no", "output_attr": "in_tag_black_aigc_license_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_D7406F": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.nieuwland_black_product_name_list", "lookup_attr": "product_name", "output_attr": "in_aigc_product_name_blacklist", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_D7F68C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.nieuwlandBlackLicenseIdList", "lookup_attr": "license_no", "output_attr": "in_aigc_license_blacklist", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_EAECB5": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildAIGCBlackList", "lookup_attr": "account_id", "output_attr": "in_aigc_account_blacklist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_programmed_unit_filter::_branch_controller_672B03EE_1": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_programmed_unit_filter::_branch_controller_672B03EE_1", "$code_info": "[if] 672B03EE system_supplement_flow.py in aigc_photo_programmed_unit_filter(): .if_(\"request_type ~= 1 and unit_type == 7\")", "export_common_attr": ["_if_control_attr_35"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["request_type", "unit_type"], "lua_script": "function evaluate() if (request_type ~= 1 and unit_type == 7) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_programmed_unit_filter::filter_by_rule_B775A3": {"filter_reason": "aigc_photo_programmed_unit_filter", "rule": {"attr_name": "photo_source", "compare_to": 16, "remove_if": "==", "remove_if_attr_missing": false}, "skip": "{{_if_control_attr_35}}", "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::create_logic_table_8DC577": {"item_table": "guaranteed_table", "logic_table": "logic_guaranteed_table", "select_attr": ["photo_id"], "type_name": "CommonRecoCreateLogicTableMixer"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::deduplicate_69DB73": {"type_name": "CommonRecoResultsDeduplicateArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::model_cmd_enricher_37F2E9": {"ad_item_id": "photo_id", "cmd_configs": [{"cmd_key": "{{cmd_key}}", "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9", "cmd_value_num": "{{cmd_value_num}}", "predict_attrs": [{"attr_name": "score", "predict_type": "{{predict_type}}"}]}], "item_table": "guaranteed_table", "kess_service": "grpc_adI18nPsDspNNRouterServerNew", "service_group": "PRODUCTION", "timeout_ms": 100, "type_name": "ModelCmdEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::photo_unit_divisity_filter::_branch_controller_077695C8_1": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::photo_unit_divisity_filter::_branch_controller_077695C8_1", "$code_info": "[if] 077695C8 system_supplement_flow.py in photo_unit_divisity_filter(): .if_(\"request_type ~= 1\")", "export_common_attr": ["_if_control_attr_36"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["request_type"], "lua_script": "function evaluate() if (request_type ~= 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::photo_unit_divisity_filter::filter_by_common_attr_155609": {"common_attr": ["selectedCreativesList"], "exclude": true, "filter_reason": "photo_unit_divisity_filter", "on_item_attr": "photo_id", "skip": "{{_if_control_attr_36}}", "type_name": "CommonRecoCommonAttrFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::recall_creative_bound_filter::cast_attr_type_01002C": {"attr_type_cast_configs": [{"from_item_attr": "uaa_photo_creative_cnt_str", "to_item_attr": "uaa_photo_creative_cnt", "to_type": "int"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::recall_creative_bound_filter::cast_attr_type_8C41FB": {"attr_type_cast_configs": [{"from_item_attr": "photo_id", "to_item_attr": "photo_id_str", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::recall_creative_bound_filter::filter_by_rule_0488E3": {"filter_reason": "recall_creative_bound_filter", "rule": {"attr_name": "uaa_photo_creative_cnt", "compare_to": "{{photo_bind_creative_upper_limit}}", "remove_if": "<"}, "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::recall_creative_bound_filter::get_item_attr_from_redis_66A0A0": {"$eval_common_attrs": ["account_id"], "cluster_name": "adMobileDataVisitors", "is_async": false, "key_prefix": "{{return 'uaa_photo_creative_cnt_' .. account_id .. '_'}}", "redis_key_from": "photo_id_str", "save_value_to": "uaa_photo_creative_cnt_str", "type_name": "CommonRecoRedisItemAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::_branch_controller_84C03475_1": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::_branch_controller_84C03475_1", "$code_info": "[if] 84C03475 system_supplement_flow.py in sdpa_photo_filter(): .if_(\"target_sdpa_id ~= nil and target_sdpa_id ~= ''\")", "export_common_attr": ["_if_control_attr_39"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_38", "target_sdpa_id"], "lua_script": "function evaluate() if (_if_control_attr_38 == 0 and (target_sdpa_id ~= nil and target_sdpa_id ~= '')) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::_branch_controller_89AE33C2_1": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::_branch_controller_89AE33C2_1", "$code_info": "[if] 89AE33C2 system_supplement_flow.py in sdpa_photo_filter(): .if_(\"in_sdpa_tag_list == 1\")", "export_common_attr": ["_if_control_attr_38"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["in_sdpa_tag_list"], "lua_script": "function evaluate() if (in_sdpa_tag_list == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::_branch_controller_CD7B5881_1": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::_branch_controller_CD7B5881_1", "$code_info": "[if] CD7B5881 system_supplement_flow.py in sdpa_photo_filter(): .if_(\"photo_sdpa_result ~= nil and photo_sdpa_result ~= ''\")", "export_common_attr": ["_if_control_attr_40"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_39", "photo_sdpa_result"], "lua_script": "function evaluate() if (_if_control_attr_39 == 0 and (photo_sdpa_result ~= nil and photo_sdpa_result ~= '')) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::cast_attr_type_1EF3A5": {"attr_type_cast_configs": [{"from_item_attr": "photo_id", "to_item_attr": "photo_id_str", "to_type": "string"}], "skip": "{{_if_control_attr_39}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::enrich_attr_by_py_F780CE": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_clang_38acbe0beb075d4b6d3db5ee929e614b.so", "clang_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["in_sdpa_tag_list"], "export_item_attr": [], "function_set": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc_905b92687a1b9894ea9449f9a1a90eb7.so", "gcc_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["in_sdpa_tag_account_list", "in_sdpa_tag_product_list", "in_sdpa_tag_second_industry_list", "in_sdpa_tag_license_list"], "import_item_attr": [], "py_function": "in_sdpa_tag_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::filter_by_rule_6E55E9": {"filter_reason": "sdpa_photo_filter", "rule": {"attr_name": "photo_sdpa_result_list", "compare_to": "{{target_sdpa_id}}", "remove_if": "not contain", "remove_if_attr_missing": true}, "skip": "{{_if_control_attr_40}}", "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::get_common_attr_from_redis_6CBB8C": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "new_uax_unit_sdpa_", "output_attr_name": "target_sdpa_id", "redis_key": "{{unit_id}}"}], "skip": "{{_if_control_attr_38}}", "type_name": "CommonRecoRedisCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::get_item_attr_from_redis_08F694": {"cluster_name": "adMobileDataVisitors", "is_async": false, "key_prefix": "new_uax_photo_sdpa_", "redis_key_from": "photo_id_str", "save_value_to": "photo_sdpa_result", "skip": "{{_if_control_attr_39}}", "type_name": "CommonRecoRedisItemAttrEnricher", "value_type": "string"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::lookup_kconf_370B0A": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableSdpaLicenseNoWhiteList", "lookup_attr": "license_no", "output_attr": "in_sdpa_tag_license_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::lookup_kconf_748278": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enable_sdpa_second_industry_whitelist", "lookup_attr": "second_industry_name", "output_attr": "in_sdpa_tag_second_industry_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::lookup_kconf_86D23C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableUaxSdpaProductName", "lookup_attr": "product_name", "output_attr": "in_sdpa_tag_product_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::lookup_kconf_EC4123": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableUaxSdpaAccountId", "lookup_attr": "account_id", "output_attr": "in_sdpa_tag_account_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::split_string_110A6F": {"delimiters": ",", "input_item_attr": "photo_sdpa_result", "output_item_attr": "photo_sdpa_result_list", "skip": "{{_if_control_attr_40}}", "type_name": "CommonRecoStringSplitEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::selected_photo_blacklist_filter::filter_by_rule_DD571C": {"filter_reason": "selected_photo_blacklist_filter", "rule": {"attr_name": "in_photo_blacklist", "compare_to": 1, "remove_if": "==", "remove_if_attr_missing": false}, "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::selected_photo_blacklist_filter::lookup_kconf_F941B1": {"kconf_configs": [{"is_common_attr": false, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildPhotoBlackListForSelected", "lookup_attr": "photo_id", "output_attr": "in_photo_blacklist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::_sort_item::photo_sort_2752ED": {"filter_reason": "photo_sort", "item_table": "item_table", "type_name": "PhotoSort<PERSON><PERSON>nger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::_branch_controller_EB30DF95": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::_branch_controller_EB30DF95", "$code_info": "[if] EB30DF95 system_supplement_flow.py in aigc_photo_blacklist_filter(): .if_(\"in_aigc_photo_blacklist == 1\")", "export_common_attr": ["_if_control_attr_31"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["in_aigc_photo_blacklist"], "lua_script": "function evaluate() if (in_aigc_photo_blacklist == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::cast_attr_type_44DD6E": {"attr_type_cast_configs": [{"from_common_attr": "aigc_second_industry_medical_white_list_json", "to_common_attr": "aigc_second_industry_medical_white_list", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::cast_attr_type_FB3236": {"attr_type_cast_configs": [{"from_common_attr": "aigc_first_industry_black_list_json", "to_common_attr": "aigc_first_industry_black_list", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::enrich_attr_by_py_58C64C": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_clang_38acbe0beb075d4b6d3db5ee929e614b.so", "clang_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["in_aigc_photo_blacklist"], "export_item_attr": [], "function_set": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc_905b92687a1b9894ea9449f9a1a90eb7.so", "gcc_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["campaign_type", "book_id", "series_id", "first_industry_name", "second_industry_name", "in_aigc_account_blacklist", "in_aigc_product_name_blacklist", "in_aigc_license_blacklist", "enable_tag_black_aigc_account", "in_tag_black_aigc_license_list", "in_tag_black_aigc_product_list", "in_tmall618_account_whitelist", "aigc_first_industry_black_list", "aigc_second_industry_medical_white_list", "enable_medical_white_list", "in_medical_account_white_list"], "import_item_attr": [], "py_function": "in_aigc_photo_blacklist_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::filter_by_rule_5E3397": {"filter_reason": "aigc_photo_blacklist_filter", "rule": {"attr_name": "photo_source", "compare_to": 16, "remove_if": "==", "remove_if_attr_missing": false}, "skip": "{{_if_control_attr_31}}", "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::get_kconf_params_017CD9": {"kconf_configs": [{"export_common_attr": "aigc_second_industry_medical_white_list_json", "json_path": "aigcSecondIndustryMedicalWhiteList", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::get_kconf_params_03F594": {"kconf_configs": [{"export_common_attr": "aigc_first_industry_black_list_json", "json_path": "aigcFirstIndustryBlackList", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::get_kconf_params_3DB952": {"kconf_configs": [{"export_common_attr": "enable_medical_white_list", "json_path": "enableMedicalWhiteList", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_3AEFD8": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildEnableTagAccountId", "lookup_attr": "account_id", "output_attr": "enable_tag_black_aigc_account", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_83438C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.medicalReviewAccount<PERSON><PERSON><PERSON>st", "lookup_attr": "account_id", "output_attr": "in_medical_account_white_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_86F279": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.tmall618A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_attr": "account_id", "output_attr": "in_tmall618_account_whitelist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_8A7801": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.photoTagProductWhiteList", "lookup_attr": "license_no", "output_attr": "in_tag_black_aigc_product_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_C60DFE": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.photoTagLicenseWhiteList", "lookup_attr": "license_no", "output_attr": "in_tag_black_aigc_license_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_D7406F": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.nieuwland_black_product_name_list", "lookup_attr": "product_name", "output_attr": "in_aigc_product_name_blacklist", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_D7F68C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.nieuwlandBlackLicenseIdList", "lookup_attr": "license_no", "output_attr": "in_aigc_license_blacklist", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_EAECB5": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildAIGCBlackList", "lookup_attr": "account_id", "output_attr": "in_aigc_account_blacklist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_programmed_unit_filter::_branch_controller_672B03EE": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_programmed_unit_filter::_branch_controller_672B03EE", "$code_info": "[if] 672B03EE system_supplement_flow.py in aigc_photo_programmed_unit_filter(): .if_(\"request_type ~= 1 and unit_type == 7\")", "export_common_attr": ["_if_control_attr_29"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["request_type", "unit_type"], "lua_script": "function evaluate() if (request_type ~= 1 and unit_type == 7) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_programmed_unit_filter::filter_by_rule_EE1631": {"filter_reason": "aigc_photo_programmed_unit_filter", "rule": {"attr_name": "photo_source", "compare_to": 16, "remove_if": "==", "remove_if_attr_missing": false}, "skip": "{{_if_control_attr_29}}", "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::create_logic_table_47689B": {"item_table": "item_table", "logic_table": "logic_item_table", "select_attr": ["photo_id"], "type_name": "CommonRecoCreateLogicTableMixer"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::deduplicate_69DB73": {"type_name": "CommonRecoResultsDeduplicateArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::model_cmd_enricher_6736F4": {"ad_item_id": "photo_id", "cmd_configs": [{"cmd_key": "{{cmd_key}}", "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9", "cmd_value_num": "{{cmd_value_num}}", "predict_attrs": [{"attr_name": "score", "predict_type": "{{predict_type}}"}]}], "item_table": "item_table", "kess_service": "grpc_adI18nPsDspNNRouterServerNew", "service_group": "PRODUCTION", "timeout_ms": 100, "type_name": "ModelCmdEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::photo_unit_divisity_filter::_branch_controller_077695C8": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::photo_unit_divisity_filter::_branch_controller_077695C8", "$code_info": "[if] 077695C8 system_supplement_flow.py in photo_unit_divisity_filter(): .if_(\"request_type ~= 1\")", "export_common_attr": ["_if_control_attr_30"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["request_type"], "lua_script": "function evaluate() if (request_type ~= 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::photo_unit_divisity_filter::filter_by_common_attr_FDFE66": {"common_attr": ["selectedCreativesList"], "exclude": true, "filter_reason": "photo_unit_divisity_filter", "on_item_attr": "photo_id", "skip": "{{_if_control_attr_30}}", "type_name": "CommonRecoCommonAttrFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::recall_creative_bound_filter::cast_attr_type_01002C": {"attr_type_cast_configs": [{"from_item_attr": "uaa_photo_creative_cnt_str", "to_item_attr": "uaa_photo_creative_cnt", "to_type": "int"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::recall_creative_bound_filter::cast_attr_type_8C41FB": {"attr_type_cast_configs": [{"from_item_attr": "photo_id", "to_item_attr": "photo_id_str", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::recall_creative_bound_filter::filter_by_rule_0488E3": {"filter_reason": "recall_creative_bound_filter", "rule": {"attr_name": "uaa_photo_creative_cnt", "compare_to": "{{photo_bind_creative_upper_limit}}", "remove_if": "<"}, "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::recall_creative_bound_filter::get_item_attr_from_redis_66A0A0": {"$eval_common_attrs": ["account_id"], "cluster_name": "adMobileDataVisitors", "is_async": false, "key_prefix": "{{return 'uaa_photo_creative_cnt_' .. account_id .. '_'}}", "redis_key_from": "photo_id_str", "save_value_to": "uaa_photo_creative_cnt_str", "type_name": "CommonRecoRedisItemAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::_branch_controller_84C03475": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::_branch_controller_84C03475", "$code_info": "[if] 84C03475 system_supplement_flow.py in sdpa_photo_filter(): .if_(\"target_sdpa_id ~= nil and target_sdpa_id ~= ''\")", "export_common_attr": ["_if_control_attr_33"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_32", "target_sdpa_id"], "lua_script": "function evaluate() if (_if_control_attr_32 == 0 and (target_sdpa_id ~= nil and target_sdpa_id ~= '')) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::_branch_controller_89AE33C2": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::_branch_controller_89AE33C2", "$code_info": "[if] 89AE33C2 system_supplement_flow.py in sdpa_photo_filter(): .if_(\"in_sdpa_tag_list == 1\")", "export_common_attr": ["_if_control_attr_32"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["in_sdpa_tag_list"], "lua_script": "function evaluate() if (in_sdpa_tag_list == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::_branch_controller_CD7B5881": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::_branch_controller_CD7B5881", "$code_info": "[if] CD7B5881 system_supplement_flow.py in sdpa_photo_filter(): .if_(\"photo_sdpa_result ~= nil and photo_sdpa_result ~= ''\")", "export_common_attr": ["_if_control_attr_34"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_33", "photo_sdpa_result"], "lua_script": "function evaluate() if (_if_control_attr_33 == 0 and (photo_sdpa_result ~= nil and photo_sdpa_result ~= '')) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::cast_attr_type_4238EF": {"attr_type_cast_configs": [{"from_item_attr": "photo_id", "to_item_attr": "photo_id_str", "to_type": "string"}], "skip": "{{_if_control_attr_33}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::enrich_attr_by_py_F780CE": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_clang_38acbe0beb075d4b6d3db5ee929e614b.so", "clang_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["in_sdpa_tag_list"], "export_item_attr": [], "function_set": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc_905b92687a1b9894ea9449f9a1a90eb7.so", "gcc_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["in_sdpa_tag_account_list", "in_sdpa_tag_product_list", "in_sdpa_tag_second_industry_list", "in_sdpa_tag_license_list"], "import_item_attr": [], "py_function": "in_sdpa_tag_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::filter_by_rule_8D70D0": {"filter_reason": "sdpa_photo_filter", "rule": {"attr_name": "photo_sdpa_result_list", "compare_to": "{{target_sdpa_id}}", "remove_if": "not contain", "remove_if_attr_missing": true}, "skip": "{{_if_control_attr_34}}", "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::get_common_attr_from_redis_E859E8": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "new_uax_unit_sdpa_", "output_attr_name": "target_sdpa_id", "redis_key": "{{unit_id}}"}], "skip": "{{_if_control_attr_32}}", "type_name": "CommonRecoRedisCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::get_item_attr_from_redis_D014DF": {"cluster_name": "adMobileDataVisitors", "is_async": false, "key_prefix": "new_uax_photo_sdpa_", "redis_key_from": "photo_id_str", "save_value_to": "photo_sdpa_result", "skip": "{{_if_control_attr_33}}", "type_name": "CommonRecoRedisItemAttrEnricher", "value_type": "string"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::lookup_kconf_370B0A": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableSdpaLicenseNoWhiteList", "lookup_attr": "license_no", "output_attr": "in_sdpa_tag_license_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::lookup_kconf_748278": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enable_sdpa_second_industry_whitelist", "lookup_attr": "second_industry_name", "output_attr": "in_sdpa_tag_second_industry_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::lookup_kconf_86D23C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableUaxSdpaProductName", "lookup_attr": "product_name", "output_attr": "in_sdpa_tag_product_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::lookup_kconf_EC4123": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableUaxSdpaAccountId", "lookup_attr": "account_id", "output_attr": "in_sdpa_tag_account_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::split_string_796A8F": {"delimiters": ",", "input_item_attr": "photo_sdpa_result", "output_item_attr": "photo_sdpa_result_list", "skip": "{{_if_control_attr_34}}", "type_name": "CommonRecoStringSplitEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::selected_photo_blacklist_filter::filter_by_rule_DD571C": {"filter_reason": "selected_photo_blacklist_filter", "rule": {"attr_name": "in_photo_blacklist", "compare_to": 1, "remove_if": "==", "remove_if_attr_missing": false}, "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::selected_photo_blacklist_filter::lookup_kconf_F941B1": {"kconf_configs": [{"is_common_attr": false, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildPhotoBlackListForSelected", "lookup_attr": "photo_id", "output_attr": "in_photo_blacklist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::_sort_racing::photo_sort_B4F042": {"filter_reason": "photo_sort", "item_table": "racing_table", "type_name": "PhotoSort<PERSON><PERSON>nger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::_branch_controller_EB30DF95_2": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::_branch_controller_EB30DF95_2", "$code_info": "[if] EB30DF95 system_supplement_flow.py in aigc_photo_blacklist_filter(): .if_(\"in_aigc_photo_blacklist == 1\")", "export_common_attr": ["_if_control_attr_43"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["in_aigc_photo_blacklist"], "lua_script": "function evaluate() if (in_aigc_photo_blacklist == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::cast_attr_type_44DD6E": {"attr_type_cast_configs": [{"from_common_attr": "aigc_second_industry_medical_white_list_json", "to_common_attr": "aigc_second_industry_medical_white_list", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::cast_attr_type_FB3236": {"attr_type_cast_configs": [{"from_common_attr": "aigc_first_industry_black_list_json", "to_common_attr": "aigc_first_industry_black_list", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::enrich_attr_by_py_58C64C": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_clang_38acbe0beb075d4b6d3db5ee929e614b.so", "clang_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["in_aigc_photo_blacklist"], "export_item_attr": [], "function_set": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc_905b92687a1b9894ea9449f9a1a90eb7.so", "gcc_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["campaign_type", "book_id", "series_id", "first_industry_name", "second_industry_name", "in_aigc_account_blacklist", "in_aigc_product_name_blacklist", "in_aigc_license_blacklist", "enable_tag_black_aigc_account", "in_tag_black_aigc_license_list", "in_tag_black_aigc_product_list", "in_tmall618_account_whitelist", "aigc_first_industry_black_list", "aigc_second_industry_medical_white_list", "enable_medical_white_list", "in_medical_account_white_list"], "import_item_attr": [], "py_function": "in_aigc_photo_blacklist_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::filter_by_rule_A70FF3": {"filter_reason": "aigc_photo_blacklist_filter", "rule": {"attr_name": "photo_source", "compare_to": 16, "remove_if": "==", "remove_if_attr_missing": false}, "skip": "{{_if_control_attr_43}}", "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::get_kconf_params_017CD9": {"kconf_configs": [{"export_common_attr": "aigc_second_industry_medical_white_list_json", "json_path": "aigcSecondIndustryMedicalWhiteList", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::get_kconf_params_03F594": {"kconf_configs": [{"export_common_attr": "aigc_first_industry_black_list_json", "json_path": "aigcFirstIndustryBlackList", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::get_kconf_params_3DB952": {"kconf_configs": [{"export_common_attr": "enable_medical_white_list", "json_path": "enableMedicalWhiteList", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_3AEFD8": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildEnableTagAccountId", "lookup_attr": "account_id", "output_attr": "enable_tag_black_aigc_account", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_83438C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.medicalReviewAccount<PERSON><PERSON><PERSON>st", "lookup_attr": "account_id", "output_attr": "in_medical_account_white_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_86F279": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.tmall618A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_attr": "account_id", "output_attr": "in_tmall618_account_whitelist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_8A7801": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.photoTagProductWhiteList", "lookup_attr": "license_no", "output_attr": "in_tag_black_aigc_product_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_C60DFE": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.photoTagLicenseWhiteList", "lookup_attr": "license_no", "output_attr": "in_tag_black_aigc_license_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_D7406F": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.nieuwland_black_product_name_list", "lookup_attr": "product_name", "output_attr": "in_aigc_product_name_blacklist", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_D7F68C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.nieuwlandBlackLicenseIdList", "lookup_attr": "license_no", "output_attr": "in_aigc_license_blacklist", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_EAECB5": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildAIGCBlackList", "lookup_attr": "account_id", "output_attr": "in_aigc_account_blacklist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_programmed_unit_filter::_branch_controller_672B03EE_2": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_programmed_unit_filter::_branch_controller_672B03EE_2", "$code_info": "[if] 672B03EE system_supplement_flow.py in aigc_photo_programmed_unit_filter(): .if_(\"request_type ~= 1 and unit_type == 7\")", "export_common_attr": ["_if_control_attr_41"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["request_type", "unit_type"], "lua_script": "function evaluate() if (request_type ~= 1 and unit_type == 7) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_programmed_unit_filter::filter_by_rule_C2F9E4": {"filter_reason": "aigc_photo_programmed_unit_filter", "rule": {"attr_name": "photo_source", "compare_to": 16, "remove_if": "==", "remove_if_attr_missing": false}, "skip": "{{_if_control_attr_41}}", "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::create_logic_table_3D6B4C": {"item_table": "racing_table", "logic_table": "logic_racing_table", "select_attr": ["photo_id"], "type_name": "CommonRecoCreateLogicTableMixer"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::deduplicate_69DB73": {"type_name": "CommonRecoResultsDeduplicateArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::model_cmd_enricher_4799B5": {"ad_item_id": "photo_id", "cmd_configs": [{"cmd_key": "{{cmd_key}}", "cmd_name": "/ad/dsp/i18n_ctr/rank:i18n_ctr_ybx_reco_ub_online_v9", "cmd_value_num": "{{cmd_value_num}}", "predict_attrs": [{"attr_name": "score", "predict_type": "{{predict_type}}"}]}], "item_table": "racing_table", "kess_service": "grpc_adI18nPsDspNNRouterServerNew", "service_group": "PRODUCTION", "timeout_ms": 100, "type_name": "ModelCmdEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::photo_unit_divisity_filter::_branch_controller_077695C8_2": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::photo_unit_divisity_filter::_branch_controller_077695C8_2", "$code_info": "[if] 077695C8 system_supplement_flow.py in photo_unit_divisity_filter(): .if_(\"request_type ~= 1\")", "export_common_attr": ["_if_control_attr_42"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["request_type"], "lua_script": "function evaluate() if (request_type ~= 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::photo_unit_divisity_filter::filter_by_common_attr_088D22": {"common_attr": ["selectedCreativesList"], "exclude": true, "filter_reason": "photo_unit_divisity_filter", "on_item_attr": "photo_id", "skip": "{{_if_control_attr_42}}", "type_name": "CommonRecoCommonAttrFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::recall_creative_bound_filter::cast_attr_type_01002C": {"attr_type_cast_configs": [{"from_item_attr": "uaa_photo_creative_cnt_str", "to_item_attr": "uaa_photo_creative_cnt", "to_type": "int"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::recall_creative_bound_filter::cast_attr_type_8C41FB": {"attr_type_cast_configs": [{"from_item_attr": "photo_id", "to_item_attr": "photo_id_str", "to_type": "string"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::recall_creative_bound_filter::filter_by_rule_0488E3": {"filter_reason": "recall_creative_bound_filter", "rule": {"attr_name": "uaa_photo_creative_cnt", "compare_to": "{{photo_bind_creative_upper_limit}}", "remove_if": "<"}, "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::recall_creative_bound_filter::get_item_attr_from_redis_66A0A0": {"$eval_common_attrs": ["account_id"], "cluster_name": "adMobileDataVisitors", "is_async": false, "key_prefix": "{{return 'uaa_photo_creative_cnt_' .. account_id .. '_'}}", "redis_key_from": "photo_id_str", "save_value_to": "uaa_photo_creative_cnt_str", "type_name": "CommonRecoRedisItemAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::_branch_controller_84C03475_2": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::_branch_controller_84C03475_2", "$code_info": "[if] 84C03475 system_supplement_flow.py in sdpa_photo_filter(): .if_(\"target_sdpa_id ~= nil and target_sdpa_id ~= ''\")", "export_common_attr": ["_if_control_attr_45"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_44", "target_sdpa_id"], "lua_script": "function evaluate() if (_if_control_attr_44 == 0 and (target_sdpa_id ~= nil and target_sdpa_id ~= '')) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::_branch_controller_89AE33C2_2": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::_branch_controller_89AE33C2_2", "$code_info": "[if] 89AE33C2 system_supplement_flow.py in sdpa_photo_filter(): .if_(\"in_sdpa_tag_list == 1\")", "export_common_attr": ["_if_control_attr_44"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["in_sdpa_tag_list"], "lua_script": "function evaluate() if (in_sdpa_tag_list == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::_branch_controller_CD7B5881_2": {"$branch_start": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::_branch_controller_CD7B5881_2", "$code_info": "[if] CD7B5881 system_supplement_flow.py in sdpa_photo_filter(): .if_(\"photo_sdpa_result ~= nil and photo_sdpa_result ~= ''\")", "export_common_attr": ["_if_control_attr_46"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["_if_control_attr_45", "photo_sdpa_result"], "lua_script": "function evaluate() if (_if_control_attr_45 == 0 and (photo_sdpa_result ~= nil and photo_sdpa_result ~= '')) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::cast_attr_type_CD173D": {"attr_type_cast_configs": [{"from_item_attr": "photo_id", "to_item_attr": "photo_id_str", "to_type": "string"}], "skip": "{{_if_control_attr_45}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::enrich_attr_by_py_F780CE": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_clang_38acbe0beb075d4b6d3db5ee929e614b.so", "clang_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["in_sdpa_tag_list"], "export_item_attr": [], "function_set": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc_905b92687a1b9894ea9449f9a1a90eb7.so", "gcc_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["in_sdpa_tag_account_list", "in_sdpa_tag_product_list", "in_sdpa_tag_second_industry_list", "in_sdpa_tag_license_list"], "import_item_attr": [], "py_function": "in_sdpa_tag_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::filter_by_rule_6CA83F": {"filter_reason": "sdpa_photo_filter", "rule": {"attr_name": "photo_sdpa_result_list", "compare_to": "{{target_sdpa_id}}", "remove_if": "not contain", "remove_if_attr_missing": true}, "skip": "{{_if_control_attr_46}}", "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::get_common_attr_from_redis_1FAB75": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "new_uax_unit_sdpa_", "output_attr_name": "target_sdpa_id", "redis_key": "{{unit_id}}"}], "skip": "{{_if_control_attr_44}}", "type_name": "CommonRecoRedisCommonAttrEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::get_item_attr_from_redis_A9F250": {"cluster_name": "adMobileDataVisitors", "is_async": false, "key_prefix": "new_uax_photo_sdpa_", "redis_key_from": "photo_id_str", "save_value_to": "photo_sdpa_result", "skip": "{{_if_control_attr_45}}", "type_name": "CommonRecoRedisItemAttrEnricher", "value_type": "string"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::lookup_kconf_370B0A": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableSdpaLicenseNoWhiteList", "lookup_attr": "license_no", "output_attr": "in_sdpa_tag_license_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::lookup_kconf_748278": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enable_sdpa_second_industry_whitelist", "lookup_attr": "second_industry_name", "output_attr": "in_sdpa_tag_second_industry_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::lookup_kconf_86D23C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableUaxSdpaProductName", "lookup_attr": "product_name", "output_attr": "in_sdpa_tag_product_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::lookup_kconf_EC4123": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableUaxSdpaAccountId", "lookup_attr": "account_id", "output_attr": "in_sdpa_tag_account_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::split_string_2F2B36": {"delimiters": ",", "input_item_attr": "photo_sdpa_result", "output_item_attr": "photo_sdpa_result_list", "skip": "{{_if_control_attr_46}}", "type_name": "CommonRecoStringSplitEnricher"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::selected_photo_blacklist_filter::filter_by_rule_DD571C": {"filter_reason": "selected_photo_blacklist_filter", "rule": {"attr_name": "in_photo_blacklist", "compare_to": 1, "remove_if": "==", "remove_if_attr_missing": false}, "type_name": "CommonRecoRuleFilterArranger"}, "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::selected_photo_blacklist_filter::lookup_kconf_F941B1": {"kconf_configs": [{"is_common_attr": false, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildPhotoBlackListForSelected", "lookup_attr": "photo_id", "output_attr": "in_photo_blacklist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_proc_system_supplement_photo_select_flow::_branch_controller_5C050AB0": {"$branch_start": "post_proc_system_supplement_photo_select_flow::_branch_controller_5C050AB0", "$code_info": "[if] 5C050AB0 system_supplement_flow.py in _post_proc(): .if_(\"need_creative_type == 2\")", "export_common_attr": ["_if_control_attr_51"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["need_creative_type"], "lua_script": "function evaluate() if (need_creative_type == 2) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "post_proc_system_supplement_photo_select_flow::build_protobuf_54B50A": {"class_name": "kuaishou.negative.GetByPhotoIdsRequest", "inputs": [{"append": true, "item_attr": "photo_id", "path": "photo_id"}], "output_common_attr": "get_by_photo_ids_request", "type_name": "CommonRecoProtobufBuildAttrEnricher"}, "post_proc_system_supplement_photo_select_flow::calc_photo_property::enrich_attr_by_py_9A98B1": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/BasePhotoSelectFlowPythonFunctionSet_clang_6bc4088aee67ef1c84dfde61c32e7e28.so", "clang_so_name": "BasePhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "BasePhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": [], "export_item_attr": ["creative_material_type", "creative_type"], "function_set": "BasePhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/BasePhotoSelectFlowPythonFunctionSet_gcc_26e8a3a833f7c7cf3c2c8cf6b744b0e9.so", "gcc_so_name": "BasePhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["is_in_smart_adv_white_list", "kconf_ad_algorithm_AdSmartDspAutoBuildProgrammaticRatio"], "import_item_attr": ["cover_width", "cover_height", "photo_source"], "py_function": "calc_photo_property_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_proc_system_supplement_photo_select_flow::calc_photo_property::get_kconf_params_9247E2": {"kconf_configs": [{"default_value": 0.5, "export_common_attr": "kconf_ad_algorithm_AdSmartDspAutoBuildProgrammaticRatio", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildProgrammaticRatio", "value_type": "double"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "post_proc_system_supplement_photo_select_flow::calc_photo_property::is_smart_adv_white_user::enrich_attr_by_py_C24D2E": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/BasePhotoSelectFlowPythonFunctionSet_clang_6bc4088aee67ef1c84dfde61c32e7e28.so", "clang_so_name": "BasePhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "BasePhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["is_in_smart_adv_white_list"], "export_item_attr": [], "function_set": "BasePhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/BasePhotoSelectFlowPythonFunctionSet_gcc_26e8a3a833f7c7cf3c2c8cf6b744b0e9.so", "gcc_so_name": "BasePhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["kconf_is_in_smart_adv_account_white_list", "kconf_is_in_smart_adv_product_white_list", "kconf_is_in_smart_adv_license_white_list", "kconf_is_in_smart_adv_second_industry_white_list", "kconf_is_in_smart_adv_first_industry_white_list"], "import_item_attr": [], "py_function": "isSmartAdvWhiteUser", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "post_proc_system_supplement_photo_select_flow::calc_photo_property::is_smart_adv_white_user::lookup_kconf_FB591B": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeAccountSwitch", "lookup_attr": "account_id", "output_attr": "kconf_is_in_smart_adv_account_white_list", "value_type": "tail_number"}, {"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeProductSwitch", "lookup_attr": "product_name", "output_attr": "kconf_is_in_smart_adv_product_white_list", "value_type": "set_string"}, {"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeLicenseSwitch", "lookup_attr": "license_no", "output_attr": "kconf_is_in_smart_adv_license_white_list", "value_type": "set_string"}, {"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeSecondIndustrySwitch", "lookup_attr": "second_industry_name", "output_attr": "kconf_is_in_smart_adv_second_industry_white_list", "value_type": "set_string"}, {"is_common_attr": true, "kconf_key": "ad.dsp2.smartAdvCreativeFirstIndustrySwitch", "lookup_attr": "license_no", "output_attr": "kconf_is_in_smart_adv_first_industry_white_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "post_proc_system_supplement_photo_select_flow::enrich_by_generic_grpc_5D0D69": {"downstream_processor": "post_proc_system_supplement_photo_select_flow::enrich_item_attr_by_common_attr_20B9C0", "kess_service": "grpc_apiCorePhotoService", "method_name": "/kuaishou.negative.PhotoServiceRpc/GetByIdsFailFast", "request_attr": "get_by_photo_ids_request", "response_attr": "get_by_photo_ids_response", "response_class": "kuaishou.negative.PhotoMapResponse", "timeout_ms": 200, "type_name": "CommonRecoGenericGrpcEnricher"}, "post_proc_system_supplement_photo_select_flow::enrich_item_attr_by_common_attr_20B9C0": {"input_common_attr": "get_by_photo_ids_response", "input_common_attr_type": 4, "type_name": "EnrichItemAttrByCommonAttr", "udf_name": "GetByPhotoIdsResponseParserUdf"}, "post_proc_system_supplement_photo_select_flow::get_item_attr_from_redis_0E3822": {"cluster_name": "adMobileDataVisitors", "key_prefix": "uax_webcast_message_", "redis_key_from": "photo_id", "save_value_to": "message_value", "skip": "{{_if_control_attr_51}}", "type_name": "CommonRecoRedisItemAttrEnricher"}, "post_proc_system_supplement_photo_select_flow::query_mix_asset_meta_by_rpc::build_mix_asset_request_F5154A": {"output_column_name": "mix_asset_meta_request", "photo_id": "photo_id", "type_name": "BuildMixAssetRequest", "video_id": "video_id"}, "post_proc_system_supplement_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_by_generic_grpc_60DC47": {"downstream_processor": "post_proc_system_supplement_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_item_attr_by_common_attr_F2CEBB", "kess_service": "grpc_videoProjectionService", "method_name": "/kuaishou.video.projection.VideoProjectionService/GetMixAssetMeta", "request_attr": "mix_asset_meta_request", "response_attr": "mix_asset_meta_response", "response_class": "kuaishou.video.projection.MixAssetMetaResponse", "timeout_ms": 200, "type_name": "CommonRecoGenericGrpcEnricher"}, "post_proc_system_supplement_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_item_attr_by_common_attr_F2CEBB": {"input_common_attr": "mix_asset_meta_response", "input_common_attr_type": 4, "type_name": "EnrichItemAttrByCommonAttr", "udf_name": "MixAssetMetaResponseParserUdf"}, "prepare_model_decision_predict_photo_select_flow::_branch_controller_B1685AE6": {"$branch_start": "prepare_model_decision_predict_photo_select_flow::_branch_controller_B1685AE6", "$code_info": "[if] B1685AE6 model_decision_flow.py in _prepare(): .if_ (\"enable_feature_index_proxy_account == 1\")", "export_common_attr": ["_if_control_attr_9"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["enable_feature_index_proxy_account"], "lua_script": "function evaluate() if (enable_feature_index_proxy_account == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare_model_decision_predict_photo_select_flow::copy_attr_8DE717": {"attrs": [{"from_item": "user_id", "to_common": "user_id"}, {"from_item": "product_name", "to_common": "product_name"}, {"from_item": "first_industry_name", "to_common": "first_industry_name"}, {"from_item": "second_industry_name", "to_common": "second_industry_name"}, {"from_item": "corporation_name", "to_common": "corporation_name"}, {"from_item": "agent_id", "to_common": "agent_id"}, {"from_item": "license_no", "to_common": "license_no"}], "item_table": "account_table", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoCopyAttrEnricher"}, "prepare_model_decision_predict_photo_select_flow::dispatch_common_attr_8EE2C5": {"from_common_attr": "account_id", "item_table": "account_table", "skip": "{{_if_control_attr_9}}", "to_item_attr": "account_id", "type_name": "CommonRecoCommonAttrDispatchEnricher"}, "prepare_model_decision_predict_photo_select_flow::lookup_kconf_9596F4": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableFeatureIndexProxyAccount", "lookup_attr": "account_id", "output_attr": "enable_feature_index_proxy_account", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "prepare_model_decision_predict_photo_select_flow::remote_table_v2_mixer_137045": {"feature_use_cache": true, "remote_service": "ad-feature-proxy", "request_tables": {"account_table": {"item_tables": ["account_table"], "local_field_type": {"agent_id": "int", "corporation_name": "string", "first_industry_name": "string", "license_no": "string", "product_name": "string", "second_industry_name": "string", "user_id": "int"}, "local_version": "6E7CEFE298526C3B477C35598466C8F2", "remote_table_relation": {"ad_dsp_account": {"item_table_key_attr": "account_id", "local_field_alias": {"agent_id": "agent_id", "corporation_name": "corporation_name", "first_industry_name_v6_1": "first_industry_name", "license_no": "license_no", "product_name": "product_name", "second_industry_name_v6_1": "second_industry_name", "user_id": "user_id"}}}, "sdk_version": "********************************"}}, "skip": "{{_if_control_attr_9}}", "type_name": "RemoteTableV2Mixer"}, "prepare_model_decision_predict_photo_select_flow::retrieve_by_common_attr_65D92A": {"attrs": [{"name": "account_id", "reason": 999}], "item_table": "account_table", "skip": "{{_if_control_attr_9}}", "type_name": "CommonRecoCommonAttrRetriever"}, "prepare_system_supplement_photo_select_flow::_branch_controller_B1685AE6_1": {"$branch_start": "prepare_system_supplement_photo_select_flow::_branch_controller_B1685AE6_1", "$code_info": "[if] B1685AE6 system_supplement_flow.py in _prepare(): .if_ (\"enable_feature_index_proxy_account == 1\")", "export_common_attr": ["_if_control_attr_26"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["enable_feature_index_proxy_account"], "lua_script": "function evaluate() if (enable_feature_index_proxy_account == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "prepare_system_supplement_photo_select_flow::copy_attr_CDDE7E": {"attrs": [{"from_item": "user_id", "to_common": "user_id"}, {"from_item": "product_name", "to_common": "product_name"}, {"from_item": "first_industry_name", "to_common": "first_industry_name"}, {"from_item": "second_industry_name", "to_common": "second_industry_name"}, {"from_item": "corporation_name", "to_common": "corporation_name"}, {"from_item": "agent_id", "to_common": "agent_id"}, {"from_item": "license_no", "to_common": "license_no"}], "item_table": "account_table", "skip": "{{_if_control_attr_26}}", "type_name": "CommonRecoCopyAttrEnricher"}, "prepare_system_supplement_photo_select_flow::dispatch_common_attr_CB5427": {"from_common_attr": "account_id", "item_table": "account_table", "skip": "{{_if_control_attr_26}}", "to_item_attr": "account_id", "type_name": "CommonRecoCommonAttrDispatchEnricher"}, "prepare_system_supplement_photo_select_flow::lookup_kconf_9596F4": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableFeatureIndexProxyAccount", "lookup_attr": "account_id", "output_attr": "enable_feature_index_proxy_account", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "prepare_system_supplement_photo_select_flow::lookup_kconf_A8BD34": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.campaignPhotoTag", "lookup_attr": "campaign_id", "output_attr": "enable_feature_index_proxy_account", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::cast_attr_type_60E382": {"attr_type_cast_configs": [{"from_item_attr": "photo_id", "to_item_attr": "photo_id_str", "to_type": "string"}], "item_table": "already_insert_photo_table", "type_name": "CommonRecoAttrTypeCastEnricher"}, "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::enrich_attr_by_json_1BEA5C": {"item_table": "already_insert_photo_table", "json_attr": "uax_predict_cost_score_json_str", "json_configs": [{"default_value": -100000.0, "export_item_attr": "s8", "json_path": "s8"}], "json_from_item_attr": true, "type_name": "CommonRecoJsonStringAttrEnricher"}, "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::enrich_attr_by_py_B723C3": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_clang_38acbe0beb075d4b6d3db5ee929e614b.so", "clang_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["alreadyExploitNum"], "export_item_attr": [], "function_set": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc_905b92687a1b9894ea9449f9a1a90eb7.so", "gcc_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["predictCostQuantile"], "import_item_attr": ["s8"], "item_table": "already_insert_photo_table", "py_function": "get_already_exploit_num", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::get_item_attr_from_redis_1FAF5A": {"$eval_common_attrs": ["account_id"], "cluster_name": "adMobileDataVisitors", "is_async": false, "item_table": "already_insert_photo_table", "key_prefix": "{{return 'uax_predict_cost_score_' .. account_id .. '_'}}", "redis_key_from": "photo_id_str", "save_value_to": "uax_predict_cost_score_json_str", "type_name": "CommonRecoRedisItemAttrEnricher"}, "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::retrieve_by_common_attr_798DE1": {"attrs": [{"name": "alreadyInsertedPhotoIdList", "reason": 7}], "item_table": "already_insert_photo_table", "type_name": "CommonRecoCommonAttrRetriever"}, "prepare_system_supplement_photo_select_flow::remote_table_v2_mixer_D99DDF": {"feature_use_cache": true, "remote_service": "ad-feature-proxy", "request_tables": {"account_table": {"item_tables": ["account_table"], "local_field_type": {"agent_id": "int", "corporation_name": "string", "first_industry_name": "string", "license_no": "string", "product_name": "string", "second_industry_name": "string", "user_id": "int"}, "local_version": "6E7CEFE298526C3B477C35598466C8F2", "remote_table_relation": {"ad_dsp_account": {"item_table_key_attr": "account_id", "local_field_alias": {"agent_id": "agent_id", "corporation_name": "corporation_name", "first_industry_name_v6_1": "first_industry_name", "license_no": "license_no", "product_name": "product_name", "second_industry_name_v6_1": "second_industry_name", "user_id": "user_id"}}}, "sdk_version": "********************************"}}, "skip": "{{_if_control_attr_26}}", "type_name": "RemoteTableV2Mixer"}, "prepare_system_supplement_photo_select_flow::retrieve_by_common_attr_007506": {"attrs": [{"name": "account_id", "reason": 999}], "item_table": "account_table", "skip": "{{_if_control_attr_26}}", "type_name": "CommonRecoCommonAttrRetriever"}, "racing_photo_recall::_branch_controller_8A24F361_5": {"$branch_start": "racing_photo_recall::_branch_controller_8A24F361_5", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_13"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "racing_photo_recall::_retrieve::get_common_attr_from_redis_1749E8": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "uax_recall_photo_dim_account_id_racing_1_", "output_attr_name": "uax_recall_photo_dim_account_json_value", "output_attr_type": "string", "redis_key": "{{account_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "racing_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_24B0A3": {"input_common_attr": "uax_recall_photo_dim_account_json_value", "input_common_attr_type": 0, "reason": 8, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "SelectedOptimisedPhotoParserUdf"}, "racing_photo_recall::_retrieve_admit::enrich_attr_by_py_B7DC95": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/RacingPhotoResultsRetrieveFlowPythonFunctionSet_clang_b06d3f3c97657c30feac8a64d9f3c0cf.so", "clang_so_name": "RacingPhotoResultsRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "RacingPhotoResultsRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "RacingPhotoResultsRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/RacingPhotoResultsRetrieveFlowPythonFunctionSet_gcc_87562e280ba389dde4d7949100daf425.so", "gcc_so_name": "RacingPhotoResultsRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["package_recall_photo_retrieve_admit", "enable_photo_recall_explore_racing"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "racing_photo_recall::return__861850": {"skip": "{{_if_control_attr_13}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "request_admit_model_decision_predict_photo_select_flow::enrich_attr_by_py_F513EE": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/ModelPredictPhotoSelectFlowPythonFunctionSet_clang_f67bbc0d50df708e52f4463c36b9d2bb.so", "clang_so_name": "ModelPredictPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "ModelPredictPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["flow_admit"], "export_item_attr": [], "function_set": "ModelPredictPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/ModelPredictPhotoSelectFlowPythonFunctionSet_gcc_5a9c0a9704217eac51dd0f91d1acef08.so", "gcc_so_name": "ModelPredictPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["account_in_black_list"], "import_item_attr": [], "py_function": "flow_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "request_admit_model_decision_predict_photo_select_flow::lookup_kconf_9A4001": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildNotValidAccountList", "lookup_attr": "account_id", "output_attr": "account_in_black_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::cast_attr_type_18BCF6": {"attr_type_cast_configs": [{"from_common_attr": "kconf_ad_algorithm_AdSmartPredictCostKconfConfig_campaignTypeList_json", "to_common_attr": "kconf_ad_algorithm_AdSmartPredictCostKconfConfig_campaignTypeList", "to_type": "int"}], "type_name": "CommonRecoAttrTypeCastEnricher"}, "request_admit_system_supplement_photo_select_flow::enrich_attr_by_py_2FC2F0": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_clang_38acbe0beb075d4b6d3db5ee929e614b.so", "clang_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_clang.so", "compile_object_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["flow_admit"], "export_item_attr": [], "function_set": "SystemSupplementPhotoSelectFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc_905b92687a1b9894ea9449f9a1a90eb7.so", "gcc_so_name": "SystemSupplementPhotoSelectFlowPythonFunctionSet_gcc.so", "import_common_attr": ["account_in_black_list"], "import_item_attr": [], "py_function": "flow_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_52EB4F": {"kconf_configs": [{"export_common_attr": "aigc_maxMontageCount", "json_path": "maxMontageCount", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_67951B": {"kconf_configs": [{"default_value": "推荐观看精彩内容", "export_common_attr": "uax_webcast_default_message", "kconf_key": "ad.adems.uaxWebcastDefaultMessage", "value_type": "string"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_689E43": {"kconf_configs": [{"export_common_attr": "aigc_maxTemplateCount", "json_path": "maxTemplateCount", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_724C52": {"kconf_configs": [{"export_common_attr": "kconf_ad_algorithm_AdSmartPredictCostKconfConfig_campaignTypeList_json", "json_path": "campaignTypeList", "kconf_key": "ad.algorithm.AdSmartPredictCostKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_8B056E": {"kconf_configs": [{"export_common_attr": "creative_strategy_json", "json_path": "creative_strategy", "kconf_key": "ad.adems.photoExpConf"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_90AD6D": {"kconf_configs": [{"export_common_attr": "campaignPhotoTag_campaign_tail_json", "json_path": "campaign_tail", "kconf_key": "ad.adems.campaignPhotoTag"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_931B52": {"kconf_configs": [{"export_common_attr": "aigc_maxPurchaseCount", "json_path": "maxPurchaseCount", "kconf_key": "ad.algorithm.AdSmartDspAutoBuildKconfConfig"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_970085": {"kconf_configs": [{"default_value": 0.0, "export_common_attr": "enable_total_photo_recall", "json_path": "enable_total_photo_recall", "kconf_key": "ad.adems.photoExpConf"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_9EC505": {"kconf_configs": [{"default_value": 1, "export_common_attr": "enable_order_creative_switch", "json_path": "enable_order_creative_switch", "kconf_key": "ad.adems.photoExpConf"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_C8E5EF": {"kconf_configs": [{"default_value": 200.0, "export_common_attr": "photo_bind_creative_upper_limit", "json_path": "photo_bind_creative_upper_limit", "kconf_key": "ad.adems.photoExpConf"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::get_kconf_params_D8A7BF": {"kconf_configs": [{"export_common_attr": "campaignPhotoTag_unit_tail_json", "json_path": "unit_tail", "kconf_key": "ad.adems.campaignPhotoTag"}], "type_name": "CommonRecoKconfCommonAttrEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_03FFF9": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.uaxWebcastByProductBlackList", "lookup_attr": "product_name", "output_attr": "uax_webcast_product_in_blacklist", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_05D5E4": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.uaxWebcastByFirstIndustryBlackList", "lookup_attr": "first_industry_name", "output_attr": "uax_webcast_first_industry_in_blacklist", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_065B82": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.dsp2.adLiveFastAddCreativeByCreateForAccount", "lookup_attr": "account_id", "output_attr": "enable_photo_recall_webcast_author_account", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_09D746": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoLicenseNoBlackList", "lookup_attr": "license_no", "output_attr": "in_webcast_author_recall_license_black_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_0E8F90": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.AdSmartDspAutoBuildEnablePhotoPackage", "lookup_attr": "account_id", "output_attr": "enable_photo_package", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_263083": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.dsp2.adLiveFastAddCreativeByCreate", "lookup_attr": "campaign_id", "output_attr": "enable_photo_recall_webcast_author_campaign", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_2EF62F": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoLicenseNoWhiteList", "lookup_attr": "license_no", "output_attr": "in_webcast_author_recall_license_white_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_370B0A": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableSdpaLicenseNoWhiteList", "lookup_attr": "license_no", "output_attr": "in_sdpa_tag_license_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_401064": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoAccountBlackList", "lookup_attr": "account_id", "output_attr": "in_webcast_author_recall_account_black_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_53569F": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoProductBlackList", "lookup_attr": "product_name", "output_attr": "in_webcast_author_recall_product_black_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_5E4935": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.exploreRacingAccountTailNumberConfig", "lookup_attr": "account_id", "output_attr": "enable_photo_recall_explore_racing", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_5FB55C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.uaxWebcastByAccountBlackList", "lookup_attr": "account_id", "output_attr": "uax_webcast_account_in_blacklist", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_68B2B4": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.tmall618A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lookup_attr": "account_id", "output_attr": "in_tmall_618_account_white_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_73F7A8": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.photoRecallAccountTailnumberConfig", "lookup_attr": "account_id", "output_attr": "enable_total_photo_recall_account", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_748278": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enable_sdpa_second_industry_whitelist", "lookup_attr": "second_industry_name", "output_attr": "in_sdpa_tag_second_industry_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_785B2F": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.uaxRecallByAccountIdWhiteList", "lookup_attr": "account_id", "output_attr": "in_webcast_account_white_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_7A78E1": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.uaxWebcastAddPhotosCampaignList", "lookup_attr": "campaign_id", "output_attr": "in_webcast_campaign_white_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_7FD66E": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.exploreGuaranteedAccountTailNumberConfig", "lookup_attr": "account_id", "output_attr": "enable_photo_recall_explore_guaranteed", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_86D23C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableUaxSdpaProductName", "lookup_attr": "product_name", "output_attr": "in_sdpa_tag_product_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_923F71": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.uaxWebcastBySecondIndustryBlackList", "lookup_attr": "second_industry_name", "output_attr": "uax_webcast_second_industry_in_blacklist", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_AA07A4": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.algorithm.dspAIGCOnlyPurchaseAccountW<PERSON>elist", "lookup_attr": "account_id", "output_attr": "in_aigc_purchase_account_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_B1C940": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.photoRecallAccountTailnumberConfigV1", "lookup_attr": "account_id", "output_attr": "enable_photo_recall_account", "value_type": "tail_number"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_C26909": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.uaxNativeAddPhotosAccountList", "lookup_attr": "account_id", "output_attr": "in_native_recall_account_white_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_D35AAD": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoProductWhiteList", "lookup_attr": "product_name", "output_attr": "in_webcast_author_recall_product_white_list", "value_type": "set_string"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_E266B7": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.webcastLiveAuthorIdRecallPhotoAccountWhiteList", "lookup_attr": "account_id", "output_attr": "in_webcast_author_recall_account_white_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_E9C6AE": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.uaxShareAddPhotosAccountList", "lookup_attr": "account_id", "output_attr": "in_share_uid_recall_account_white_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "request_admit_system_supplement_photo_select_flow::lookup_kconf_EC4123": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.enableUaxSdpaAccountId", "lookup_attr": "account_id", "output_attr": "in_sdpa_tag_account_list", "value_type": "set_int64"}], "type_name": "CommonRecoKconfLookupEnricher"}, "retrieve_model_decision_predict_photo_select_flow::retrieve_by_sub_flow_1CC6E8": {"downstream_processor": "post_data_proc_model_decision_predict_photo_select_flow::deduplicate_69DB73", "flow_name": "model_recall_photo_retrieve_flow", "merge_common_attrs": [{"as": "model_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_reward", "photo_id"], "pass_common_attrs": ["account_id", "user_id", "product_name"], "type_name": "CommonRecoPipelineRetriever"}, "retrieve_model_decision_predict_photo_select_flow::retrieve_by_sub_flow_EBE974": {"downstream_processor": "post_data_proc_model_decision_predict_photo_select_flow::deduplicate_69DB73", "flow_name": "model_predict_new_photo_retrieve_flow", "merge_common_attrs": [{"as": "model_recall_new_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_id"], "pass_common_attrs": ["account_id", "user_id", "product_name"], "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::_branch_controller_3DF33FA0": {"$branch_start": "retrieve_system_supplement_photo_select_flow::_branch_controller_3DF33FA0", "$code_info": "[if] 3DF33FA0 system_supplement_flow.py in _retrieve(): .if_(\"need_creative_type == 0 or need_creative_type == 2 or need_creative_type == 3\")", "export_common_attr": ["_if_control_attr_28"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["need_creative_type"], "lua_script": "function evaluate() if (need_creative_type == 0 or need_creative_type == 2 or need_creative_type == 3) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "retrieve_system_supplement_photo_select_flow::_branch_controller_4D83D41A": {"$branch_start": "retrieve_system_supplement_photo_select_flow::_branch_controller_4D83D41A", "$code_info": "[if] 4D83D41A system_supplement_flow.py in _retrieve(): .if_(\"need_creative_type == 1 or need_creative_type == 2\")", "export_common_attr": ["_if_control_attr_27"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["need_creative_type"], "lua_script": "function evaluate() if (need_creative_type == 1 or need_creative_type == 2) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_28113C": {"downstream_processor": "__pipeline_end", "flow_name": "aigc_dsp_purchase_acc2pid_retrieve_flow", "item_table": "aigc_table", "merge_common_attrs": [{"as": "aigc_dsp_purchase_acc2pid_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_id"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"], "skip": "{{_if_control_attr_28}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_3E3106": {"downstream_processor": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::create_logic_table_47689B", "flow_name": "user_id_photo_retrieve_flow", "item_table": "item_table", "merge_common_attrs": [{"as": "user_id_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status", "quantity_score", "diverse_score", "explore_score", "cost_score"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit", "enable_photo_recall_account", "in_share_uid_recall_account_white_list"], "skip": "{{_if_control_attr_27}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_456422": {"downstream_processor": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::create_logic_table_47689B", "flow_name": "account_photo_retrieve_flow", "item_table": "item_table", "merge_common_attrs": [{"as": "account_photo_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status", "quantity_score", "diverse_score", "explore_score", "cost_score"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit", "enable_photo_recall_account"], "skip": "{{_if_control_attr_27}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_4B63B3": {"downstream_processor": "__pipeline_end", "flow_name": "aigc_tmall_account_retrieve_flow", "item_table": "aigc_table", "merge_common_attrs": [{"as": "aigc_dsp_acc2pid_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_id"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"], "skip": "{{_if_control_attr_28}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_5A9F08": {"downstream_processor": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::create_logic_table_47689B", "flow_name": "webcast_cross_account_photo_retrieve_flow", "item_table": "item_table", "merge_common_attrs": [{"as": "webcast_cross_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status", "quantity_score", "diverse_score", "explore_score", "cost_score"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit", "in_webcast_campaign_white_list", "in_webcast_account_white_list", "uax_webcast_account_in_blacklist", "uax_webcast_product_in_blacklist", "uax_webcast_first_industry_in_blacklist", "uax_webcast_second_industry_in_blacklist", "live_creative_type"], "skip": "{{_if_control_attr_27}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_7292DD": {"downstream_processor": "__pipeline_end", "flow_name": "aigc_dsp_acc2pid_retrieve_flow", "item_table": "aigc_table", "merge_common_attrs": [{"as": "aigc_dsp_acc2pid_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_id"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"], "skip": "{{_if_control_attr_28}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_839A83": {"downstream_processor": "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_456422", "flow_name": "package_photo_retrieve_flow", "item_table": "item_table", "merge_common_attrs": [{"as": "package_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"], "skip": "{{_if_control_attr_27}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_93CE46": {"downstream_processor": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::create_logic_table_3D6B4C", "flow_name": "racing_photo_results_retrieve_flow", "item_table": "racing_table", "merge_common_attrs": [{"as": "racing_account_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status", "quantity_score", "diverse_score", "explore_score", "cost_score"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit", "enable_photo_recall_explore_racing"], "skip": "{{_if_control_attr_27}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_9519BE": {"downstream_processor": "__pipeline_end", "flow_name": "aigc_dsp_purchase2_acc2pid_retrieve_flow", "item_table": "aigc_table", "merge_common_attrs": [{"as": "aigc_dsp_purchase2acc2pid_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_id"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"], "skip": "{{_if_control_attr_28}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_98EAF3": {"downstream_processor": "__pipeline_end", "flow_name": "aigc_unit_sdpa_retrieve_flow", "item_table": "aigc_table", "merge_common_attrs": [{"as": "aigc_unit_sdpa_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_id"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"], "skip": "{{_if_control_attr_28}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_9CDB91": {"downstream_processor": "__pipeline_end", "flow_name": "aigc_book_id_retrieve_flow", "item_table": "aigc_table", "merge_common_attrs": [{"as": "aigc_temlpate_id_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_id", "creatieve_count", "arpu", "pool_name"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"], "skip": "{{_if_control_attr_28}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_A8CEFE": {"downstream_processor": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::create_logic_table_47689B", "flow_name": "native_account_photo_retrieve_flow", "item_table": "item_table", "merge_common_attrs": [{"as": "native_account_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status", "quantity_score", "diverse_score", "explore_score", "cost_score"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit", "in_native_recall_account_white_list", "enable_photo_recall_account"], "skip": "{{_if_control_attr_27}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_C6FB52": {"downstream_processor": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::create_logic_table_47689B", "flow_name": "webcast_author_id_retrieve_flow", "item_table": "item_table", "merge_common_attrs": [{"as": "webcast_author_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status", "quantity_score", "diverse_score", "explore_score", "cost_score"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit", "live_author_id", "enable_photo_recall_webcast_author_account", "enable_photo_recall_webcast_author_campaign", "in_webcast_author_recall_account_white_list", "in_webcast_author_recall_license_white_list", "in_webcast_author_recall_product_white_list", "in_webcast_author_recall_account_black_list", "in_webcast_author_recall_license_black_list", "in_webcast_author_recall_product_black_list"], "skip": "{{_if_control_attr_27}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_CA54B7": {"downstream_processor": "__pipeline_end", "flow_name": "aigc_series_id_retrieve_flow", "item_table": "aigc_table", "merge_common_attrs": [{"as": "aigc_series_id_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_id"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list"], "skip": "{{_if_control_attr_28}}", "type_name": "CommonRecoPipelineRetriever"}, "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_D9910E": {"downstream_processor": "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::create_logic_table_8DC577", "flow_name": "guaranteed_photo_results_retrieve_flow", "item_table": "guaranteed_table", "merge_common_attrs": [{"as": "guaranteed_account_recall_photo_retrieve_admit", "name": "retrieve_admit"}], "merge_item_attrs": ["photo_quota", "photo_user_id", "photo_source", "photo_id", "impr", "click", "cost", "impr_1day", "cost_1day", "conversion", "idx_in_account", "row_num_pvalue", "row_num_cost", "pvalue", "cost_rate", "predict_cost_tpm", "native_strict_status", "quantity_score", "diverse_score", "explore_score", "cost_score"], "pass_common_attrs": ["account_id", "campaign_id", "user_id", "unit_id", "product_name", "second_industry_name", "first_industry_name", "photo_tags_list", "photo_packages_list", "package_recall_photo_retrieve_admit", "enable_photo_recall_explore_guaranteed"], "skip": "{{_if_control_attr_27}}", "type_name": "CommonRecoPipelineRetriever"}, "return__3BE73E": {"skip": "{{_if_control_attr_25}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "return__53DAA6": {"skip": "{{_if_control_attr_8}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "select_strategy_system_supplement_photo_select_flow::_branch_controller_2FF7E698": {"$branch_start": "select_strategy_system_supplement_photo_select_flow::_branch_controller_2FF7E698", "$code_info": "[if] 2FF7E698 system_supplement_flow.py in _select_strategy(): .if_(\"enable_order_creative_switch == 1\")", "export_common_attr": ["_if_control_attr_50"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["enable_order_creative_switch"], "lua_script": "function evaluate() if (enable_order_creative_switch == 1) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::calculate_quota_enricher_98F519": {"skip": "{{_if_control_attr_50}}", "type_name": "Cal<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "select_strategy_system_supplement_photo_select_flow::cast_attr_type_0B4E95": {"attr_type_cast_configs": [{"from_common_attr": "first_industry_aigc_ratio", "to_common_attr": "first_industry_aigc_ratio_double", "to_type": "double"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "select_strategy_system_supplement_photo_select_flow::cast_attr_type_3EA9AC": {"attr_type_cast_configs": [{"from_common_attr": "aigc_new_photo_ratio", "to_common_attr": "aigc_new_photo_ratio_double", "to_type": "double"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "select_strategy_system_supplement_photo_select_flow::cast_attr_type_6BB4AE": {"attr_type_cast_configs": [{"from_common_attr": "aigc_exploration_ratio", "to_common_attr": "aigc_exploration_ratio_double", "to_type": "double"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "select_strategy_system_supplement_photo_select_flow::cast_attr_type_6F4828": {"attr_type_cast_configs": [{"from_item_attr": "photo_uax_ucb_explore_score_str", "to_item_attr": "photo_uax_ucb_explore_score", "to_type": "double"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "select_strategy_system_supplement_photo_select_flow::cast_attr_type_8F3C8A": {"attr_type_cast_configs": [{"from_item_attr": "photo_id", "to_item_attr": "photo_id_str", "to_type": "string"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "select_strategy_system_supplement_photo_select_flow::cast_attr_type_B1D879": {"attr_type_cast_configs": [{"from_common_attr": "account_aigc_ratio", "to_common_attr": "account_aigc_ratio_double", "to_type": "double"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "select_strategy_system_supplement_photo_select_flow::cast_attr_type_C283E0": {"attr_type_cast_configs": [{"from_common_attr": "high_light_photo_limit", "to_common_attr": "high_light_photo_limit_int", "to_type": "int"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoAttrTypeCastEnricher"}, "select_strategy_system_supplement_photo_select_flow::enrich_attr_by_light_function_7FD4D2": {"class_name": "AdLightFunctionSet", "function_name": "CalPhotoCreateTimeDiff", "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoLightFunctionEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_item_attr_from_redis_57ABF9": {"$eval_common_attrs": ["account_id"], "cluster_name": "adMobileDataVisitors", "is_async": false, "key_prefix": "{{return 'uax_ucb_explore_score_' .. account_id .. '_'}}", "redis_key_from": "photo_id_str", "save_value_to": "photo_uax_ucb_explore_score_str", "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoRedisItemAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_105701": {"$eval_common_attrs": ["exp_partten"], "kconf_configs": [{"default_value": 20.0, "export_common_attr": "aigc_exploration_ratio", "json_path": "{{return exp_partten .. '.explorationRatio'}}", "kconf_key": "ad.algorithm.AdSmartPredictCostKconfConfig"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_1CFE38": {"kconf_configs": [{"export_common_attr": "first_industry_aigc_ratio", "json_path": "{{first_industry_name}}", "kconf_key": "ad.adems.uaxAccountRecallQuotaConfig"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_49F00F": {"kconf_configs": [{"default_value": 20, "export_common_attr": "uax_aigc_photo_max_quota", "kconf_key": "ad.adems.uaxAigcPhotosMaxQuota", "value_type": "int64"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_59D3EA": {"kconf_configs": [{"default_value": 0.5, "export_common_attr": "new_explore_quota_ratio", "kconf_key": "ad.adems.newExploreQuotaRatio", "value_type": "double"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_6DB4DE": {"kconf_configs": [{"default_value": 0.7, "export_common_attr": "param_alpha", "json_path": "param_alpha", "kconf_key": "ad.adems.photoExpConf"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_6F4396": {"kconf_configs": [{"default_value": 7, "export_common_attr": "high_light_photo_limit", "json_path": "limit<PERSON>uo<PERSON>", "kconf_key": "ad.dsp2.buildHighLightPhotoLimit"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_92E252": {"kconf_configs": [{"default_value": 0.1, "export_common_attr": "param_beta", "json_path": "param_beta", "kconf_key": "ad.adems.photoExpConf"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_933EFE": {"kconf_configs": [{"default_value": 0.2, "export_common_attr": "param_gamma", "json_path": "param_gamma", "kconf_key": "ad.adems.photoExpConf"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_A49A80": {"$eval_common_attrs": ["exp_partten"], "kconf_configs": [{"default_value": 12.5, "export_common_attr": "aigc_new_photo_ratio", "json_path": "{{return exp_partten .. '.newPhotoRatio'}}", "kconf_key": "ad.algorithm.AdSmartPredictCostKconfConfig"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_C442AD": {"kconf_configs": [{"default_value": 0.3, "export_common_attr": "explore_quota_predict_indicator", "kconf_key": "ad.adems.expExploreQuotaRatio", "value_type": "double"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::get_kconf_params_D1E646": {"kconf_configs": [{"export_common_attr": "account_aigc_ratio", "json_path": "{{account_id}}", "kconf_key": "ad.adems.uaxRecallQuotaAccountList"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfCommonAttrEnricher"}, "select_strategy_system_supplement_photo_select_flow::lookup_kconf_0850F9": {"kconf_configs": [{"default_value": 0, "kconf_key": "ad.adems.uaxAigcProductWhiteList", "lookup_attr": "product_id", "output_attr": "in_uax_aigc_product_white_list", "value_type": "set_int64"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfLookupEnricher"}, "select_strategy_system_supplement_photo_select_flow::lookup_kconf_878F2C": {"kconf_configs": [{"is_common_attr": true, "kconf_key": "ad.adems.photoRecallAccountTailnumberConfig", "lookup_attr": "account_id", "output_attr": "photo_recall_account", "value_type": "tail_number"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfLookupEnricher"}, "select_strategy_system_supplement_photo_select_flow::lookup_kconf_C129DA": {"kconf_configs": [{"default_value": 0, "kconf_key": "ad.adems.uaxAigcAccountWhiteList", "lookup_attr": "account_id", "output_attr": "in_uax_aigc_account_white_list", "value_type": "set_int64"}], "skip": "{{_if_control_attr_50}}", "type_name": "CommonRecoKconfLookupEnricher"}, "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_43CBB1": {"item_table": "racing_table", "merge_attr_names": ["photo_id"], "quota_num": "{{racing_table_quota}}", "skip": "{{_if_control_attr_50}}", "to_table": "", "type_name": "SelectTopkByQuota"}, "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_980C2C": {"item_table": "item_table", "merge_attr_names": ["photo_id"], "quota_num": "{{item_table_quota}}", "skip": "{{_if_control_attr_50}}", "to_table": "", "type_name": "SelectTopkByQuota"}, "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_B01770": {"item_table": "aigc_explore_table", "merge_attr_names": ["photo_id"], "quota_num": "{{aigc_explore_table_quota}}", "skip": "{{_if_control_attr_50}}", "to_table": "", "type_name": "SelectTopkByQuota"}, "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_CEBBA6": {"item_table": "aigc_exploit_table", "merge_attr_names": ["photo_id"], "quota_num": "{{aigc_exploit_table_quota}}", "skip": "{{_if_control_attr_50}}", "to_table": "", "type_name": "SelectTopkByQuota"}, "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_F5E1B5": {"item_table": "aigc_new_photo_table", "merge_attr_names": ["photo_id"], "quota_num": "{{aigc_new_photo_table_quota}}", "skip": "{{_if_control_attr_50}}", "to_table": "", "type_name": "SelectTopkByQuota"}, "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_F97A5F": {"item_table": "guaranteed_table", "merge_attr_names": ["photo_id"], "quota_num": "{{guarantee_table_quota}}", "skip": "{{_if_control_attr_50}}", "to_table": "", "type_name": "SelectTopkByQuota"}, "select_strategy_system_supplement_photo_select_flow::trace_log_send_C6C103": {"physical_table_names": ["item_table", "racing_table", "guaranteed_table", "aigc_exploit_table", "aigc_explore_table", "aigc_new_photo_table"], "type_name": "TraceLogSendMixer"}, "trace_log_send_7451B8": {"physical_table_names": [], "skip": "{{_if_control_attr_8}}", "type_name": "TraceLogSendMixer"}, "trace_log_send_A20787": {"physical_table_names": [], "skip": "{{_if_control_attr_25}}", "type_name": "TraceLogSendMixer"}, "user_id_photo_recall::_branch_controller_8A24F361_7": {"$branch_start": "user_id_photo_recall::_branch_controller_8A24F361_7", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_15"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "user_id_photo_recall::_retrieve::get_common_attr_from_redis_C10651": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "uax_recall_photo_dim_account_user_id_1_", "output_attr_name": "uax_recall_photo_dim_user_json_value", "output_attr_type": "string", "redis_key": "{{user_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "user_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_13E8C6": {"input_common_attr": "uax_recall_photo_dim_user_json_value", "input_common_attr_type": 0, "reason": 4, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "SelectedOptimisedPhotoParserUdf"}, "user_id_photo_recall::_retrieve_admit::enrich_attr_by_py_BC2932": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/UserIdPhotoRetrieveFlowPythonFunctionSet_clang_f3cbaa39d4188a6fbf53c32a8c034d9d.so", "clang_so_name": "UserIdPhotoRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "UserIdPhotoRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "UserIdPhotoRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/UserIdPhotoRetrieveFlowPythonFunctionSet_gcc_6f091933b1b98c344f48ba7fbae26bb3.so", "gcc_so_name": "UserIdPhotoRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["package_recall_photo_retrieve_admit", "enable_photo_recall_account", "in_share_uid_recall_account_white_list"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "user_id_photo_recall::return__114141": {"skip": "{{_if_control_attr_15}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "webcast_author_id_photo_recall::_branch_controller_8A24F361_9": {"$branch_start": "webcast_author_id_photo_recall::_branch_controller_8A24F361_9", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_17"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "webcast_author_id_photo_recall::_retrieve::get_common_attr_from_redis_3DA708": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "uax_recall_photo_dim_live_author_id_1_", "output_attr_name": "uax_recall_photo_dim_account_json_value", "output_attr_type": "string", "redis_key": "{{live_author_id}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "webcast_author_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_BE09CA": {"input_common_attr": "uax_recall_photo_dim_account_json_value", "input_common_attr_type": 0, "reason": 6, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "SelectedOptimisedPhotoParserUdf"}, "webcast_author_id_photo_recall::_retrieve_admit::enrich_attr_by_py_CCD1E6": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/WebcastAuthorIdRetrieveFlowPythonFunctionSet_clang_d985c0b0d0e82152f10378bae693cd95.so", "clang_so_name": "WebcastAuthorIdRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "WebcastAuthorIdRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "WebcastAuthorIdRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/WebcastAuthorIdRetrieveFlowPythonFunctionSet_gcc_633a1f947177f9e1eb91fe704030821d.so", "gcc_so_name": "WebcastAuthorIdRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["package_recall_photo_retrieve_admit", "live_author_id", "enable_photo_recall_webcast_author_account", "enable_photo_recall_webcast_author_campaign", "in_webcast_author_recall_account_white_list", "in_webcast_author_recall_license_white_list", "in_webcast_author_recall_product_white_list", "in_webcast_author_recall_account_black_list", "in_webcast_author_recall_license_black_list", "in_webcast_author_recall_product_black_list"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "webcast_author_id_photo_recall::return__A2CA40": {"skip": "{{_if_control_attr_17}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}, "webcast_cross_account_photo_recall::_branch_controller_8A24F361_8": {"$branch_start": "webcast_cross_account_photo_recall::_branch_controller_8A24F361_8", "$code_info": "[if] 8A24F361 photo_retrieve_base_flow.py in photo_retrieval(): .if_ (\"retrieve_admit == 0 or retrieve_admit == nil\")", "export_common_attr": ["_if_control_attr_16"], "for_branch_control": true, "function_for_common": "evaluate", "import_common_attr": ["retrieve_admit"], "lua_script": "function evaluate() if (retrieve_admit == 0 or retrieve_admit == nil) then return false else return true end end", "type_name": "CommonRecoLuaAttrEnricher"}, "webcast_cross_account_photo_recall::_retrieve::get_common_attr_from_redis_9316F0": {"cluster_name": "adMobileDataVisitors", "is_async": false, "redis_params": [{"key_prefix": "uax_webcast_cross_account_{{product_name}}_", "output_attr_name": "uax_webcast_cross_account_recall_account_ids_json_value", "output_attr_type": "string", "redis_key": "{{license_no}}", "redis_value_type": "string"}], "type_name": "CommonRecoRedisCommonAttrEnricher"}, "webcast_cross_account_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_F1118A": {"input_common_attr": "uax_webcast_cross_account_recall_account_ids_json_value", "input_common_attr_type": 0, "reason": 5, "type_name": "RetrieveItemFromCommonAttrValue", "udf_name": "WebcastOptimisedPhotoParserUdf"}, "webcast_cross_account_photo_recall::_retrieve_admit::enrich_attr_by_py_BAB716": {"clang_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/WebcastCrossAccountPhotoRetrieveFlowPythonFunctionSet_clang_54713b09965b38713950d2e999f5dd0f.so", "clang_so_name": "WebcastCrossAccountPhotoRetrieveFlowPythonFunctionSet_clang.so", "compile_object_name": "WebcastCrossAccountPhotoRetrieveFlowPythonFunctionSet", "compile_tool": "both", "export_common_attr": ["retrieve_admit"], "export_item_attr": [], "function_set": "WebcastCrossAccountPhotoRetrieveFlowPythonFunctionSet", "gcc_remote_so_name": "ad/outer_ems_photo_rec_service/dragon_server/dso/WebcastCrossAccountPhotoRetrieveFlowPythonFunctionSet_gcc_1697aef01be4ce73febf6adcffe671c1.so", "gcc_so_name": "WebcastCrossAccountPhotoRetrieveFlowPythonFunctionSet_gcc.so", "import_common_attr": ["package_recall_photo_retrieve_admit", "in_webcast_campaign_white_list", "in_webcast_account_white_list", "uax_webcast_account_in_blacklist", "uax_webcast_product_in_blacklist", "uax_webcast_first_industry_in_blacklist", "uax_webcast_second_industry_in_blacklist", "live_creative_type"], "import_item_attr": [], "py_function": "retrieve_admit_function", "static_compile": false, "type_name": "CommonRecoPyAttrEnricher"}, "webcast_cross_account_photo_recall::return__854583": {"skip": "{{_if_control_attr_16}}", "status_code": 0, "type_name": "CommonRecoExecutionStatusEnricher"}}, "type_name": "CommonRecoPipeline"}, "pipeline_map": {"account_photo_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "item_table", "pipeline": ["account_photo_recall::_retrieve_admit::enrich_attr_by_py_8C8BA1", "account_photo_recall::_branch_controller_8A24F361_2", "account_photo_recall::return__8EB8D5", "account_photo_recall::_retrieve::get_common_attr_from_redis_E60EBC", "account_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_467EEE"]}, "aigc_book_id_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "aigc_table", "pipeline": ["aigc_book_id_photo_recall::_retrieve_admit::enrich_attr_by_py_FC6227", "aigc_book_id_photo_recall::_branch_controller_8A24F361_11", "aigc_book_id_photo_recall::return__ACE1D9", "aigc_book_id_photo_recall::_retrieve::build_material_page_request_15A81F", "aigc_book_id_photo_recall::_retrieve::enrich_by_generic_grpc_99FE3B", "aigc_book_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_B6D45B"]}, "aigc_dsp_acc2pid_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "aigc_table", "pipeline": ["aigc_dsp_acc2pid_photo_recall::_retrieve_admit::enrich_attr_by_py_574E82", "aigc_dsp_acc2pid_photo_recall::_branch_controller_8A24F361_14", "aigc_dsp_acc2pid_photo_recall::return__61CF30", "aigc_dsp_acc2pid_photo_recall::_retrieve::get_common_attr_from_redis_BEC36E", "aigc_dsp_acc2pid_photo_recall::_retrieve::split_string_295FB2", "aigc_dsp_acc2pid_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_E0EC2E", "aigc_dsp_acc2pid_photo_recall::_truncate::shuffle_A597DE", "aigc_dsp_acc2pid_photo_recall::_truncate::truncate_EFCECD"]}, "aigc_dsp_purchase2_acc2pid_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "aigc_table", "pipeline": ["aigc_dsp_purchase2_acc2pid_photo_recall::_retrieve_admit::enrich_attr_by_py_D57D18", "aigc_dsp_purchase2_acc2pid_photo_recall::_branch_controller_8A24F361_15", "aigc_dsp_purchase2_acc2pid_photo_recall::return__26F3CB", "aigc_dsp_purchase2_acc2pid_photo_recall::_retrieve::get_common_attr_from_redis_F0E829", "aigc_dsp_purchase2_acc2pid_photo_recall::_retrieve::split_string_295FB2", "aigc_dsp_purchase2_acc2pid_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_E0E7AE", "aigc_dsp_purchase2_acc2pid_photo_recall::_truncate::shuffle_A597DE", "aigc_dsp_purchase2_acc2pid_photo_recall::_truncate::truncate_16B4C7"]}, "aigc_dsp_purchase_acc2pid_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "aigc_table", "pipeline": ["aigc_dsp_purchase_acc2pid_photo_recall::_retrieve_admit::enrich_attr_by_py_753587", "aigc_dsp_purchase_acc2pid_photo_recall::_branch_controller_8A24F361_16", "aigc_dsp_purchase_acc2pid_photo_recall::return__51EABB", "aigc_dsp_purchase_acc2pid_photo_recall::_retrieve::get_common_attr_from_redis_D1CA73", "aigc_dsp_purchase_acc2pid_photo_recall::_retrieve::split_string_295FB2", "aigc_dsp_purchase_acc2pid_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_7E741D", "aigc_dsp_purchase_acc2pid_photo_recall::_truncate::shuffle_A597DE", "aigc_dsp_purchase_acc2pid_photo_recall::_truncate::truncate_57F3F1"]}, "aigc_series_id_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "aigc_table", "pipeline": ["aigc_series_id_photo_recall::_retrieve_admit::enrich_attr_by_py_C12EA2", "aigc_series_id_photo_recall::_branch_controller_8A24F361_13", "aigc_series_id_photo_recall::return__D2F806", "aigc_series_id_photo_recall::_retrieve::get_common_attr_from_redis_5605A1", "aigc_series_id_photo_recall::_retrieve::split_string_602F8A", "aigc_series_id_photo_recall::_retrieve::str_format_BCDEC6", "aigc_series_id_photo_recall::_retrieve::get_common_attr_from_redis_DA3A17", "aigc_series_id_photo_recall::_retrieve::split_string_9563A0", "aigc_series_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_64AB05", "aigc_series_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_19378B"]}, "aigc_tmall_account_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "aigc_table", "pipeline": ["aigc_tmall_account_photo_recall::_retrieve_admit::enrich_attr_by_py_55FBEA", "aigc_tmall_account_photo_recall::_branch_controller_8A24F361_10", "aigc_tmall_account_photo_recall::return__7126B7", "aigc_tmall_account_photo_recall::_retrieve::get_common_attr_from_redis_847075", "aigc_tmall_account_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_AD0182"]}, "aigc_unit_sdpa_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "aigc_table", "pipeline": ["aigc_unit_sdpa_photo_recall::_retrieve_admit::enrich_attr_by_py_D0EAA4", "aigc_unit_sdpa_photo_recall::_branch_controller_8A24F361_12", "aigc_unit_sdpa_photo_recall::return__E72A5F", "aigc_unit_sdpa_photo_recall::_retrieve::get_common_attr_from_redis_DCAD71", "aigc_unit_sdpa_photo_recall::_retrieve::str_format_562979", "aigc_unit_sdpa_photo_recall::_retrieve::get_common_attr_from_redis_198862", "aigc_unit_sdpa_photo_recall::_retrieve::split_string_7B5498", "aigc_unit_sdpa_photo_recall::_retrieve::str_format_231B1F", "aigc_unit_sdpa_photo_recall::_retrieve::get_common_attr_from_redis_03906A", "aigc_unit_sdpa_photo_recall::_retrieve::split_string_BE5592", "aigc_unit_sdpa_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_5306F6", "aigc_unit_sdpa_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_C2B047"]}, "default": {"__PARENT": "base_pipeline", "item_table": "item_table", "pipeline": ["get_package_conversion_predict_result::package_conversion_predict_enricher_92F3CA"]}, "guaranteed_photo_results_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "guaranteed_table", "pipeline": ["guaranteed_photo_recall::_retrieve_admit::enrich_attr_by_py_84E5C2", "guaranteed_photo_recall::_branch_controller_8A24F361_4", "guaranteed_photo_recall::return__922D6C", "guaranteed_photo_recall::_retrieve::get_common_attr_from_redis_9F7A11", "guaranteed_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_DD5405"]}, "model_predict_flow": {"__PARENT": "base_pipeline", "item_table": "photo_table", "pipeline": ["request_admit_model_decision_predict_photo_select_flow::lookup_kconf_9A4001", "request_admit_model_decision_predict_photo_select_flow::enrich_attr_by_py_F513EE", "_branch_controller_A8330883", "trace_log_send_7451B8", "return__53DAA6", "prepare_model_decision_predict_photo_select_flow::lookup_kconf_9596F4", "prepare_model_decision_predict_photo_select_flow::_branch_controller_B1685AE6", "prepare_model_decision_predict_photo_select_flow::retrieve_by_common_attr_65D92A", "prepare_model_decision_predict_photo_select_flow::dispatch_common_attr_8EE2C5", "prepare_model_decision_predict_photo_select_flow::remote_table_v2_mixer_137045", "prepare_model_decision_predict_photo_select_flow::copy_attr_8DE717", "retrieve_model_decision_predict_photo_select_flow::retrieve_by_sub_flow_1CC6E8", "retrieve_model_decision_predict_photo_select_flow::retrieve_by_sub_flow_EBE974", "post_data_proc_model_decision_predict_photo_select_flow::deduplicate_69DB73", "post_data_proc_model_decision_predict_photo_select_flow::build_protobuf_54B50A", "post_data_proc_model_decision_predict_photo_select_flow::enrich_by_generic_grpc_ECFC53", "post_data_proc_model_decision_predict_photo_select_flow::enrich_item_attr_by_common_attr_20B9C0", "post_data_proc_model_decision_predict_photo_select_flow::query_mix_asset_meta_by_rpc::build_mix_asset_request_F5154A", "post_data_proc_model_decision_predict_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_by_generic_grpc_7BE875", "post_data_proc_model_decision_predict_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_item_attr_by_common_attr_F2CEBB", "post_data_proc_model_decision_predict_photo_select_flow::calc_photo_property::is_smart_adv_white_user::lookup_kconf_FB591B", "post_data_proc_model_decision_predict_photo_select_flow::calc_photo_property::is_smart_adv_white_user::enrich_attr_by_py_C24D2E", "post_data_proc_model_decision_predict_photo_select_flow::calc_photo_property::get_kconf_params_9247E2", "post_data_proc_model_decision_predict_photo_select_flow::calc_photo_property::enrich_attr_by_py_9A98B1"]}, "model_predict_new_photo_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "photo_table", "pipeline": ["new_photo_recall::_retrieve_admit::ad_enrich_common_attr_by_kconf_1D5C42", "new_photo_recall::_retrieve_admit::enrich_attr_by_py_C599A4", "new_photo_recall::_branch_controller_8A24F361_1", "new_photo_recall::return__70F737", "new_photo_recall::_retrieve::get_common_attr_from_redis_378BF7", "new_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_C2126E", "new_photo_recall::_rule_filter::black_new_photo_filter::enrich_attr_by_py_EF6A1E", "new_photo_recall::_rule_filter::black_new_photo_filter::_branch_controller_492A71AC", "new_photo_recall::_rule_filter::black_new_photo_filter::filter_by_rule_A8AFEE", "new_photo_recall::_truncate::shuffle_A597DE", "new_photo_recall::_truncate::truncate_D52A1B"]}, "model_recall_photo_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "photo_table", "pipeline": ["model_recall_recall::_retrieve_admit::enrich_attr_by_py_6144E5", "model_recall_recall::_branch_controller_8A24F361", "model_recall_recall::return__38295C", "model_recall_recall::_retrieve::get_common_attr_from_redis_493E77", "model_recall_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_9EBF43", "model_recall_recall::_rule_filter::photo_id_black_list_filter::lookup_kconf_08818B", "model_recall_recall::_rule_filter::photo_id_black_list_filter::filter_by_rule_49321E", "model_recall_recall::_rule_filter::user_id_mismatch_filter::ad_enrich_common_attr_by_kconf_1D5C42", "model_recall_recall::_rule_filter::user_id_mismatch_filter::_branch_controller_6F5F875C", "model_recall_recall::_rule_filter::user_id_mismatch_filter::filter_by_rule_1EAA86", "model_recall_recall::_rule_filter::aigc_black_list_filter::lookup_kconf_305D2B", "model_recall_recall::_rule_filter::aigc_black_list_filter::_branch_controller_09D867B0", "model_recall_recall::_rule_filter::aigc_black_list_filter::enrich_attr_by_lua_09A2FB", "model_recall_recall::_rule_filter::aigc_black_list_filter::return__D6F978", "model_recall_recall::_rule_filter::aigc_black_list_filter::_branch_controller_436026F2", "model_recall_recall::_rule_filter::aigc_black_list_filter::enrich_attr_by_lua_73993A", "model_recall_recall::_rule_filter::aigc_black_list_filter::return__D3550F", "model_recall_recall::_rule_filter::aigc_black_list_filter::_branch_controller_7F9EAA2F", "model_recall_recall::_rule_filter::aigc_black_list_filter::filter_by_rule_6C0195", "model_recall_recall::_sort::sort_by_score_407B25", "model_recall_recall::_truncate::truncate_C2ED74"]}, "native_account_photo_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "item_table", "pipeline": ["native_account_photo_recall::_retrieve_admit::enrich_attr_by_py_6BAE5D", "native_account_photo_recall::_branch_controller_8A24F361_3", "native_account_photo_recall::return__4B35E8", "native_account_photo_recall::_retrieve::get_common_attr_from_redis_F170D8", "native_account_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_C1758E"]}, "package_photo_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "item_table", "pipeline": ["package_photo_recall::_retrieve_admit::enrich_attr_by_py_10436E", "package_photo_recall::_branch_controller_8A24F361_6", "package_photo_recall::return__870069", "package_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_E4A0F0"]}, "racing_photo_results_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "racing_table", "pipeline": ["racing_photo_recall::_retrieve_admit::enrich_attr_by_py_B7DC95", "racing_photo_recall::_branch_controller_8A24F361_5", "racing_photo_recall::return__861850", "racing_photo_recall::_retrieve::get_common_attr_from_redis_1749E8", "racing_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_24B0A3"]}, "system_supplement_flow": {"__PARENT": "base_pipeline", "pipeline": ["request_admit_system_supplement_photo_select_flow::lookup_kconf_0E8F90", "request_admit_system_supplement_photo_select_flow::get_kconf_params_970085", "request_admit_system_supplement_photo_select_flow::lookup_kconf_B1C940", "request_admit_system_supplement_photo_select_flow::lookup_kconf_73F7A8", "request_admit_system_supplement_photo_select_flow::lookup_kconf_C26909", "request_admit_system_supplement_photo_select_flow::lookup_kconf_E9C6AE", "request_admit_system_supplement_photo_select_flow::lookup_kconf_7FD66E", "request_admit_system_supplement_photo_select_flow::lookup_kconf_5E4935", "request_admit_system_supplement_photo_select_flow::lookup_kconf_065B82", "request_admit_system_supplement_photo_select_flow::lookup_kconf_263083", "request_admit_system_supplement_photo_select_flow::lookup_kconf_E266B7", "request_admit_system_supplement_photo_select_flow::lookup_kconf_2EF62F", "request_admit_system_supplement_photo_select_flow::lookup_kconf_D35AAD", "request_admit_system_supplement_photo_select_flow::lookup_kconf_401064", "request_admit_system_supplement_photo_select_flow::lookup_kconf_09D746", "request_admit_system_supplement_photo_select_flow::lookup_kconf_53569F", "request_admit_system_supplement_photo_select_flow::lookup_kconf_7A78E1", "request_admit_system_supplement_photo_select_flow::lookup_kconf_785B2F", "request_admit_system_supplement_photo_select_flow::lookup_kconf_5FB55C", "request_admit_system_supplement_photo_select_flow::lookup_kconf_03FFF9", "request_admit_system_supplement_photo_select_flow::lookup_kconf_05D5E4", "request_admit_system_supplement_photo_select_flow::lookup_kconf_923F71", "request_admit_system_supplement_photo_select_flow::lookup_kconf_68B2B4", "request_admit_system_supplement_photo_select_flow::lookup_kconf_EC4123", "request_admit_system_supplement_photo_select_flow::lookup_kconf_86D23C", "request_admit_system_supplement_photo_select_flow::lookup_kconf_748278", "request_admit_system_supplement_photo_select_flow::lookup_kconf_370B0A", "request_admit_system_supplement_photo_select_flow::lookup_kconf_AA07A4", "request_admit_system_supplement_photo_select_flow::get_kconf_params_724C52", "request_admit_system_supplement_photo_select_flow::cast_attr_type_18BCF6", "request_admit_system_supplement_photo_select_flow::get_kconf_params_9EC505", "request_admit_system_supplement_photo_select_flow::get_kconf_params_C8E5EF", "request_admit_system_supplement_photo_select_flow::get_kconf_params_67951B", "request_admit_system_supplement_photo_select_flow::get_kconf_params_8B056E", "request_admit_system_supplement_photo_select_flow::get_kconf_params_90AD6D", "request_admit_system_supplement_photo_select_flow::get_kconf_params_D8A7BF", "request_admit_system_supplement_photo_select_flow::get_kconf_params_52EB4F", "request_admit_system_supplement_photo_select_flow::get_kconf_params_689E43", "request_admit_system_supplement_photo_select_flow::get_kconf_params_931B52", "request_admit_system_supplement_photo_select_flow::enrich_attr_by_py_2FC2F0", "_branch_controller_A8330883_1", "trace_log_send_A20787", "return__3BE73E", "prepare_system_supplement_photo_select_flow::lookup_kconf_A8BD34", "prepare_system_supplement_photo_select_flow::lookup_kconf_9596F4", "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::retrieve_by_common_attr_798DE1", "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::cast_attr_type_60E382", "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::get_item_attr_from_redis_1FAF5A", "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::enrich_attr_by_json_1BEA5C", "prepare_system_supplement_photo_select_flow::process_already_insert_photo_list::enrich_attr_by_py_B723C3", "prepare_system_supplement_photo_select_flow::_branch_controller_B1685AE6_1", "prepare_system_supplement_photo_select_flow::retrieve_by_common_attr_007506", "prepare_system_supplement_photo_select_flow::dispatch_common_attr_CB5427", "prepare_system_supplement_photo_select_flow::remote_table_v2_mixer_D99DDF", "prepare_system_supplement_photo_select_flow::copy_attr_CDDE7E", "retrieve_system_supplement_photo_select_flow::_branch_controller_4D83D41A", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_839A83", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_456422", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_A8CEFE", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_D9910E", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_93CE46", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_3E3106", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_5A9F08", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_C6FB52", "retrieve_system_supplement_photo_select_flow::_branch_controller_3DF33FA0", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_9CDB91", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_98EAF3", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_CA54B7", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_7292DD", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_9519BE", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_28113C", "retrieve_system_supplement_photo_select_flow::retrieve_by_sub_flow_4B63B3", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::deduplicate_69DB73", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::create_logic_table_47689B", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_programmed_unit_filter::_branch_controller_672B03EE", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_programmed_unit_filter::filter_by_rule_EE1631", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::recall_creative_bound_filter::cast_attr_type_8C41FB", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::recall_creative_bound_filter::get_item_attr_from_redis_66A0A0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::recall_creative_bound_filter::cast_attr_type_01002C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::recall_creative_bound_filter::filter_by_rule_0488E3", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::photo_unit_divisity_filter::_branch_controller_077695C8", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::photo_unit_divisity_filter::filter_by_common_attr_FDFE66", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_EAECB5", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_D7406F", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_D7F68C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_3AEFD8", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_C60DFE", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_8A7801", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_86F279", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::get_kconf_params_03F594", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::cast_attr_type_FB3236", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::get_kconf_params_017CD9", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::cast_attr_type_44DD6E", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::get_kconf_params_3DB952", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::lookup_kconf_83438C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::enrich_attr_by_py_58C64C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::_branch_controller_EB30DF95", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::aigc_photo_blacklist_filter::filter_by_rule_5E3397", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::lookup_kconf_EC4123", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::lookup_kconf_86D23C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::lookup_kconf_748278", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::lookup_kconf_370B0A", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::enrich_attr_by_py_F780CE", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::_branch_controller_89AE33C2", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::get_common_attr_from_redis_E859E8", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::_branch_controller_84C03475", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::cast_attr_type_4238EF", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::get_item_attr_from_redis_D014DF", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::_branch_controller_CD7B5881", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::split_string_796A8F", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::sdpa_photo_filter::filter_by_rule_8D70D0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::selected_photo_blacklist_filter::lookup_kconf_F941B1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::selected_photo_blacklist_filter::filter_by_rule_DD571C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::model_cmd_enricher_6736F4", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_item::_sort_item::photo_sort_2752ED", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::deduplicate_69DB73", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::create_logic_table_8DC577", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_programmed_unit_filter::_branch_controller_672B03EE_1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_programmed_unit_filter::filter_by_rule_B775A3", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::recall_creative_bound_filter::cast_attr_type_8C41FB", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::recall_creative_bound_filter::get_item_attr_from_redis_66A0A0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::recall_creative_bound_filter::cast_attr_type_01002C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::recall_creative_bound_filter::filter_by_rule_0488E3", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::photo_unit_divisity_filter::_branch_controller_077695C8_1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::photo_unit_divisity_filter::filter_by_common_attr_155609", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_EAECB5", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_D7406F", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_D7F68C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_3AEFD8", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_C60DFE", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_8A7801", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_86F279", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::get_kconf_params_03F594", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::cast_attr_type_FB3236", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::get_kconf_params_017CD9", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::cast_attr_type_44DD6E", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::get_kconf_params_3DB952", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::lookup_kconf_83438C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::enrich_attr_by_py_58C64C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::_branch_controller_EB30DF95_1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::aigc_photo_blacklist_filter::filter_by_rule_6E0D92", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::lookup_kconf_EC4123", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::lookup_kconf_86D23C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::lookup_kconf_748278", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::lookup_kconf_370B0A", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::enrich_attr_by_py_F780CE", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::_branch_controller_89AE33C2_1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::get_common_attr_from_redis_6CBB8C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::_branch_controller_84C03475_1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::cast_attr_type_1EF3A5", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::get_item_attr_from_redis_08F694", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::_branch_controller_CD7B5881_1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::split_string_110A6F", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::sdpa_photo_filter::filter_by_rule_6E55E9", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::selected_photo_blacklist_filter::lookup_kconf_F941B1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::selected_photo_blacklist_filter::filter_by_rule_DD571C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::model_cmd_enricher_37F2E9", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_guarantee::_sort_guarantee::photo_sort_9900A0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::deduplicate_69DB73", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::create_logic_table_3D6B4C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_programmed_unit_filter::_branch_controller_672B03EE_2", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_programmed_unit_filter::filter_by_rule_C2F9E4", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::recall_creative_bound_filter::cast_attr_type_8C41FB", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::recall_creative_bound_filter::get_item_attr_from_redis_66A0A0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::recall_creative_bound_filter::cast_attr_type_01002C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::recall_creative_bound_filter::filter_by_rule_0488E3", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::photo_unit_divisity_filter::_branch_controller_077695C8_2", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::photo_unit_divisity_filter::filter_by_common_attr_088D22", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_EAECB5", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_D7406F", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_D7F68C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_3AEFD8", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_C60DFE", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_8A7801", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_86F279", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::get_kconf_params_03F594", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::cast_attr_type_FB3236", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::get_kconf_params_017CD9", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::cast_attr_type_44DD6E", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::get_kconf_params_3DB952", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::lookup_kconf_83438C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::enrich_attr_by_py_58C64C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::_branch_controller_EB30DF95_2", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::aigc_photo_blacklist_filter::filter_by_rule_A70FF3", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::lookup_kconf_EC4123", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::lookup_kconf_86D23C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::lookup_kconf_748278", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::lookup_kconf_370B0A", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::enrich_attr_by_py_F780CE", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::_branch_controller_89AE33C2_2", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::get_common_attr_from_redis_1FAB75", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::_branch_controller_84C03475_2", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::cast_attr_type_CD173D", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::get_item_attr_from_redis_A9F250", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::_branch_controller_CD7B5881_2", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::split_string_2F2B36", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::sdpa_photo_filter::filter_by_rule_6CA83F", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::selected_photo_blacklist_filter::lookup_kconf_F941B1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::selected_photo_blacklist_filter::filter_by_rule_DD571C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::model_cmd_enricher_4799B5", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_racing::_sort_racing::photo_sort_B4F042", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::deduplicate_69DB73", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::get_kconf_params_2F3F2B", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::cast_attr_type_8BD278", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::get_common_attr_from_redis_61D0DA", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_json_757086", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_py_4C542E", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::cast_attr_type_8C41FB", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::get_item_attr_from_redis_BCF045", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_json_37D0BF", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_json_E1336E", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::cast_attr_type_8C41FB", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::get_item_attr_from_redis_56E988", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::enrich_attr_by_json_A5A60C", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_get_aigc_predicted_scores::split_aigc_tables_D6D053", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::split_aigc_tables_C82A85", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::create_logic_table_55D657", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::create_logic_table_0F6535", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::create_logic_table_F0F1D0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_creative_bound_filter::get_kconf_params_0433A0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_creative_bound_filter::cast_attr_type_B35DC0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_creative_bound_filter::filter_by_rule_402FAC", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_photo_unit_divisity_filter::_branch_controller_077695C8_3", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::aigc_photo_unit_divisity_filter::filter_by_common_attr_5FF049", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::model_cmd_enricher_5505AA", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_exploit::_sort_aigc_exploit::photo_sort_1DB016", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_creative_bound_filter::get_kconf_params_0433A0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_creative_bound_filter::cast_attr_type_B35DC0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_creative_bound_filter::filter_by_rule_402FAC", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_photo_unit_divisity_filter::_branch_controller_077695C8_4", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::aigc_photo_unit_divisity_filter::filter_by_common_attr_2FE0B1", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::model_cmd_enricher_B6DB5F", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_new_photo::_sort_aigc_new_photo::photo_sort_00CC00", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_creative_bound_filter::get_kconf_params_0433A0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_creative_bound_filter::cast_attr_type_B35DC0", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_creative_bound_filter::filter_by_rule_402FAC", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_photo_unit_divisity_filter::_branch_controller_077695C8_5", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::aigc_photo_unit_divisity_filter::filter_by_common_attr_C512E6", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::model_cmd_enricher_E2228D", "post_data_proc_system_supplement_photo_select_flow::_post_data_proc_aigc::_post_data_proc_aigc_explore::_sort_aigc_explore::photo_sort_7FD3E7", "select_strategy_system_supplement_photo_select_flow::_branch_controller_2FF7E698", "select_strategy_system_supplement_photo_select_flow::lookup_kconf_878F2C", "select_strategy_system_supplement_photo_select_flow::cast_attr_type_8F3C8A", "select_strategy_system_supplement_photo_select_flow::get_item_attr_from_redis_57ABF9", "select_strategy_system_supplement_photo_select_flow::cast_attr_type_6F4828", "select_strategy_system_supplement_photo_select_flow::enrich_attr_by_light_function_7FD4D2", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_1CFE38", "select_strategy_system_supplement_photo_select_flow::cast_attr_type_0B4E95", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_D1E646", "select_strategy_system_supplement_photo_select_flow::cast_attr_type_B1D879", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_49F00F", "select_strategy_system_supplement_photo_select_flow::lookup_kconf_C129DA", "select_strategy_system_supplement_photo_select_flow::lookup_kconf_0850F9", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_59D3EA", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_C442AD", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_105701", "select_strategy_system_supplement_photo_select_flow::cast_attr_type_6BB4AE", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_A49A80", "select_strategy_system_supplement_photo_select_flow::cast_attr_type_3EA9AC", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_6F4396", "select_strategy_system_supplement_photo_select_flow::cast_attr_type_C283E0", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_6DB4DE", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_92E252", "select_strategy_system_supplement_photo_select_flow::get_kconf_params_933EFE", "select_strategy_system_supplement_photo_select_flow::calculate_quota_enricher_98F519", "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_980C2C", "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_43CBB1", "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_F97A5F", "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_CEBBA6", "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_B01770", "select_strategy_system_supplement_photo_select_flow::select_topk_by_quota_F5E1B5", "select_strategy_system_supplement_photo_select_flow::trace_log_send_C6C103", "post_proc_system_supplement_photo_select_flow::build_protobuf_54B50A", "post_proc_system_supplement_photo_select_flow::enrich_by_generic_grpc_5D0D69", "post_proc_system_supplement_photo_select_flow::enrich_item_attr_by_common_attr_20B9C0", "post_proc_system_supplement_photo_select_flow::query_mix_asset_meta_by_rpc::build_mix_asset_request_F5154A", "post_proc_system_supplement_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_by_generic_grpc_60DC47", "post_proc_system_supplement_photo_select_flow::query_mix_asset_meta_by_rpc::enrich_item_attr_by_common_attr_F2CEBB", "post_proc_system_supplement_photo_select_flow::calc_photo_property::is_smart_adv_white_user::lookup_kconf_FB591B", "post_proc_system_supplement_photo_select_flow::calc_photo_property::is_smart_adv_white_user::enrich_attr_by_py_C24D2E", "post_proc_system_supplement_photo_select_flow::calc_photo_property::get_kconf_params_9247E2", "post_proc_system_supplement_photo_select_flow::calc_photo_property::enrich_attr_by_py_9A98B1", "post_proc_system_supplement_photo_select_flow::_branch_controller_5C050AB0", "post_proc_system_supplement_photo_select_flow::get_item_attr_from_redis_0E3822"]}, "user_id_photo_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "item_table", "pipeline": ["user_id_photo_recall::_retrieve_admit::enrich_attr_by_py_BC2932", "user_id_photo_recall::_branch_controller_8A24F361_7", "user_id_photo_recall::return__114141", "user_id_photo_recall::_retrieve::get_common_attr_from_redis_C10651", "user_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_13E8C6"]}, "webcast_author_id_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "item_table", "pipeline": ["webcast_author_id_photo_recall::_retrieve_admit::enrich_attr_by_py_CCD1E6", "webcast_author_id_photo_recall::_branch_controller_8A24F361_9", "webcast_author_id_photo_recall::return__A2CA40", "webcast_author_id_photo_recall::_retrieve::get_common_attr_from_redis_3DA708", "webcast_author_id_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_BE09CA"]}, "webcast_cross_account_photo_retrieve_flow": {"__PARENT": "base_pipeline", "item_table": "item_table", "pipeline": ["webcast_cross_account_photo_recall::_retrieve_admit::enrich_attr_by_py_BAB716", "webcast_cross_account_photo_recall::_branch_controller_8A24F361_8", "webcast_cross_account_photo_recall::return__854583", "webcast_cross_account_photo_recall::_retrieve::get_common_attr_from_redis_9316F0", "webcast_cross_account_photo_recall::_retrieve::retrieve_item_from_common_attr_value_by_udf_F1118A"]}}}, "request_type_config": {"model_decision": ["model_predict_flow"], "package_conversion_predict": ["default"], "system_supplement": ["system_supplement_flow"]}}