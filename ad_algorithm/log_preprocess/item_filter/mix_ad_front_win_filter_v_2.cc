/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/log_preprocess/item_filter/mix_ad_front_win_filter_v_2.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/mix_ad_front_win_filter_v2.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

bool MixAdFrontWinFilterV2::operator()(const kuaishou::ad::algorithm::AdJointLabeledLog& log,
                                       const kuaishou::ad::algorithm::Item& item) const {
  auto x0 = get_adlog_map_int64_int64(log.context().info_common_attr(),
                                      ContextInfoCommonAttr::IS_THANOS_FRONT_DELIVERY_MAP);
  auto x1 = get_adlog_int64(item.ad_dsp_info().creative().base().id());
  auto x2 = get_value_from_map(x0, x1);
  auto x3 = cast_to_int64(x2);
  auto x4 = mix_ad_front_win_filter_v2(x3);
  return x4;
}

}  // namespace ad_algorithm
}  // namespace ks
