#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/log_preprocess/item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/mix_ad_front_filter.dark
class MixAdFrontWinFilterV2 : public ItemFilterBase {
 public:
  explicit MixAdFrontWinFilterV2(const std::string&) {}
  bool operator()(const kuaishou::ad::algorithm::AdJointLabeledLog& log,
                  const kuaishou::ad::algorithm::Item& item) const override;
};

REGISTER_PLUGIN(ItemFilterBase, MixAdFrontWinFilterV2, "mix_ad_front_win_filter_v_2");
}  // namespace ad_algorithm
}  // namespace ks
