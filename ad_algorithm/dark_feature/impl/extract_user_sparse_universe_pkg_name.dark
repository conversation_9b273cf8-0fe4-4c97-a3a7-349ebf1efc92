using("ExtractUserSparseUniversePackageNameRelevance",
    FeatureType::USER,
    "adlog.context.info_common_attr.UNIVERSE_TINY_SEARCH_PACKAGE_NAME_RELEVANCE:int64",
    -1,
    value_or
    );

using("ExtractUserSparseUniversePackageNamePos",
    FeatureType::COMBINE,
    "adlog.context.info_common_attr.UNIVERSE_TINY_SEARCH_PACKAGE_NAME_POS:map_int64_int64",
    "adlog.item.ad_dsp_info.advertiser_base.product_name",
    city_hash64,
    get_value_from_map,
    -1,
    value_or
    );

using("ExtractCombineUniversePackageNamePosRelevance",
    FeatureType::COMBINE,
    "adlog.context.info_common_attr.UNIVERSE_TINY_SEARCH_PACKAGE_NAME_RELEVANCE:int64",
    -1,
    value_or,
    "adlog.context.info_common_attr.UNIVERSE_TINY_SEARCH_PACKAGE_NAME_POS:map_int64_int64",
    "adlog.item.ad_dsp_info.advertiser_base.product_name",
    city_hash64,
    get_value_from_map,
    -1,
    value_or,
    combine
    );

using("ExtractCombineUniversePackageNamePosIndustry",
    FeatureType::COMBINE,
    "adlog.item.ad_dsp_info.advertiser_base.industry_id:int64",
    -1,
    value_or,
    "adlog.context.info_common_attr.UNIVERSE_TINY_SEARCH_PACKAGE_NAME_POS:map_int64_int64",
    "adlog.item.ad_dsp_info.advertiser_base.product_name",
    city_hash64,
    get_value_from_map,
    -1,
    value_or,
    combine
    );

using("ExtractCombineUniversePackageNameRelevanceIndustry",
    FeatureType::COMBINE,
    "adlog.context.info_common_attr.UNIVERSE_TINY_SEARCH_PACKAGE_NAME_RELEVANCE:int64",
    -1,
    value_or,
	"adlog.item.ad_dsp_info.advertiser_base.industry_id:int64",
    -1,
    value_or,
    combine
    );

using("ExtractCombineUniversePackageNamePosProductname",
    FeatureType::COMBINE,
    "adlog.item.ad_dsp_info.advertiser_base.product_name",
    city_hash64,
    "adlog.context.info_common_attr.UNIVERSE_TINY_SEARCH_PACKAGE_NAME_POS:map_int64_int64",
    "adlog.item.ad_dsp_info.advertiser_base.product_name",
    city_hash64,
    get_value_from_map,
    -1,
    value_or,
    combine
    );