using("ExtractCombineRankIndexUescorePctrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PCTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePlvtrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PLVTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePsvrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PSVTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePvtrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PVTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePwtdItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PWTD_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePcprItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PCPR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePltrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PLTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePwtrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PWTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePftrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PFTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePcmtrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PCMTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePhtrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PHTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePclickLiveItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PCLICK_LIVE_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PEFFECTIVE_WATCH_LIVE_TIME_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePptrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PPTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePepstrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PEPSTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePlstrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PLSTR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePetcmItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PETCM_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexUescorePcmefItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.FRRANK_UESCORE_PCMEF_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexInnerOrderCvrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.ADRANK_INNER_MERGE_CVR_RANK_INDEX:int64"
  );
using("ExtractCombineRankIndexLiveAudienceCtrItemcontextNoPrefix",
      FeatureType::COMBINE,
      "adlog.item.item_common_attr.ADRANK_LIVE_AUDIENCE_CTR_RANK_INDEX:int64"
  );