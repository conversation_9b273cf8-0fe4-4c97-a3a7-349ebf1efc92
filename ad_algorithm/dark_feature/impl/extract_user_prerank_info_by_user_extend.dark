using("ExtractUserPrerankInnerSoftItemIdListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          0,
          0,
          get_int_list_feat_from_str);
using("ExtractUserPrerankInnerSoftItemTypeListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          0,
          1,
          get_str_list_feat_from_str);
using("ExtractUserPrerankInnerSoftQueueTypeListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          0,
          2,
          get_str_list_feat_from_str);
using("ExtractUserPrerankInnerSoftLtrRankIdxListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          0,
          3,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankInnerSoftItemSizeListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          0,
          4,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankInnerSoftRankBenefitListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          0,
          5,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankInnerSoftCpmListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          0,
          6,
          get_int_list_feat_from_str,
          cast_to_float,
          15);

using("ExtractUserPrerankInnerHardItemIdListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          1,
          0,
          get_int_list_feat_from_str);
using("ExtractUserPrerankInnerHardItemTypeListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          1,
          1,
          get_str_list_feat_from_str);
using("ExtractUserPrerankInnerHardQueueTypeListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          1,
          2,
          get_str_list_feat_from_str);
using("ExtractUserPrerankInnerHardLtrRankIdxListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          1,
          3,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankInnerHardItemSizeListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          1,
          4,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankInnerHardRankBenefitListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          1,
          5,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankInnerHardCpmListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          1,
          6,
          get_int_list_feat_from_str,
          cast_to_float,
          15);

using("ExtractUserPrerankOuterSoftItemIdListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          2,
          0,
          get_int_list_feat_from_str);
using("ExtractUserPrerankOuterSoftItemTypeListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          2,
          1,
          get_str_list_feat_from_str);
using("ExtractUserPrerankOuterSoftQueueTypeListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          2,
          2,
          get_str_list_feat_from_str);
using("ExtractUserPrerankOuterSoftLtrRankIdxListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          2,
          3,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankOuterSoftItemSizeListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          2,
          4,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankOuterSoftRankBenefitListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          2,
          5,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankOuterSoftCpmListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          2,
          6,
          get_int_list_feat_from_str,
          cast_to_float,
          15);

using("ExtractUserPrerankOuterHardItemIdListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          3,
          0,
          get_int_list_feat_from_str);
using("ExtractUserPrerankOuterHardItemTypeListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          3,
          1,
          get_str_list_feat_from_str);
using("ExtractUserPrerankOuterHardQueueTypeListByUserExtend",
          FeatureType::USER,
          "adlog.user_extend",
          3,
          2,
          get_str_list_feat_from_str);
using("ExtractUserPrerankOuterHardLtrRankIdxListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          3,
          3,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankOuterHardItemSizeListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          3,
          4,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankOuterHardRankBenefitListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          3,
          5,
          get_int_list_feat_from_str,
          cast_to_float,
          15);
using("ExtractUserPrerankOuterHardCpmListByUserExtend",
          FeatureType::DENSE_USER,
          "adlog.user_extend",
          3,
          6,
          get_int_list_feat_from_str,
          cast_to_float,
          15);