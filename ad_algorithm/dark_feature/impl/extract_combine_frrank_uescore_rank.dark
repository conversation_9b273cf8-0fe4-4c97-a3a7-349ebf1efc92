using("ExtractCombineRankIndexUescorePctrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PCTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePlvtrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PLVTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePsvrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PSVTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePvtrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PVTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePwtdNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PWTD_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePcprNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PCPR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePltrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PLTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePwtrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PWTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePftrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PFTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePcmtrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PCMTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePhtrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PHTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePclickLiveNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PCLICK_LIVE_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePEffectiveWatchLiveTimeNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PEFFECTIVE_WATCH_LIVE_TIME_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePptrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PPTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePepstrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PEPSTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePlstrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PLSTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePetcmNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PETCM_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexUescorePcmefNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PCMEF_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexInnerOrderCvrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.ADRANK_INNER_MERGE_CVR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractCombineRankIndexLiveAudienceCtrNoPrefix",
      FeatureType::COMBINE,
      "adlog.context.info_common_attr.ADRANK_LIVE_AUDIENCE_CTR_LIST:map_int64_float",
      "adlog.item.ad_dsp_info.photo_info.id",
      get_rank_index_from_map
  );
using("ExtractUserRankInnerOrderNum",
     FeatureType::USER,
     "adlog.context.info_common_attr.ADRANK_INNER_MERGE_CVR_LIST:map_int64_float",
     get_map_size
  );
using("ExtractUserRankLiveAudienceNum",
     FeatureType::USER,
     "adlog.context.info_common_attr.ADRANK_LIVE_AUDIENCE_CTR_LIST:map_int64_float",
     get_map_size
  );
using("ExtractUserRankUescoreNum",
      FeatureType::USER,
      "adlog.context.info_common_attr.FRRANK_UESCORE_PCTR_LIST:map_int64_float",
      get_map_size
  );