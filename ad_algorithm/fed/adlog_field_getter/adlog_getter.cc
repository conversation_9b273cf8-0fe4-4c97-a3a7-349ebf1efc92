#include <cstdint>
#include <string>
#include <vector>

#include "google/protobuf/repeated_field.h"
#include "teams/ad/ad_algorithm/feature/fast/ad_log.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_user_info.pb.h"

namespace ks {
namespace ad_algorithm {

using kuaishou::ad::algorithm::AdActionBaseInfo;
using kuaishou::ad::algorithm::AdActionInfoList;
using kuaishou::ad::algorithm::SimpleAdDspInfo;
using kuaishou::ad::algorithm::SimpleAdDspInfos;
using kuaishou::ad::algorithm::SimpleAdDspInfosV2;
using kuaishou::ad::algorithm::SimpleAdDspInfoV2;
using kuaishou::ad::algorithm::SimpleFansTopInfo;
using kuaishou::ad::algorithm::SimpleLiveInfo;
using kuaishou::ad::algorithm::SimpleLiveInfos;

using kuaishou::ad::algorithm::SimplePhotoInfo;
using kuaishou::ad::algorithm::SimpleUserInfo;

using kuaishou::ad::CommonInfoAttr;
using kuaishou::ad::ContextInfoCommonAttr;
using kuaishou::ad::LabelAttr;
using kuaishou::ad::LabelInfoCommonAttr;

using google::protobuf::RepeatedField;
using google::protobuf::RepeatedPtrField;

uint64_t get_adlog_time(const AdLog& adlog) { return adlog.timestamp(); }

absl::optional<int64_t> get_adlog_live_or_photo_author_id(const AdLog& adlog, size_t pos) {
  const auto& item = adlog.item(pos);
  int64_t author_id = item.ad_dsp_info().live_info().author_info().id();
  if (author_id == 0) {
    author_id = item.ad_dsp_info().photo_info().author_info().id();
  }

  if (author_id == 0) {
    return absl::nullopt;
  }
  return absl::make_optional(author_id);
}

template <typename T, std::enable_if_t<std::is_integral<T>::value, bool>>
T get_adlog_int(T t) {
  return t;
}

template int get_adlog_int(int);
template int64_t get_adlog_int(int64_t);
template bool get_adlog_int(bool);

template <typename T, std::enable_if_t<std::is_integral<T>::value, bool>>
inline int64_t get_adlog_int64(T t) {
  return static_cast<int64_t>(t);
}

template int64_t get_adlog_int64(int32_t);
template int64_t get_adlog_int64(uint32_t);
template int64_t get_adlog_int64(int64_t);
template int64_t get_adlog_int64(uint64_t);
template int64_t get_adlog_int64(bool);

template <typename T>
absl::optional<int64_t> get_adlog_int64(const RepeatedPtrField<T>& attr_list, int64_t enum_value) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return absl::make_optional(attr.int_value());
    }
  }

  return absl::nullopt;
}

template absl::optional<int64_t> get_adlog_int64(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
template absl::optional<int64_t> get_adlog_int64(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);
template absl::optional<int64_t> get_adlog_int64(const RepeatedPtrField<LabelInfoCommonAttr>&, int64_t);

template <typename T, std::enable_if_t<std::is_integral<T>::value, bool>>
inline uint64_t get_adlog_uint64(T t) {
  return static_cast<uint64_t>(t);
}

template uint64_t get_adlog_uint64(int32_t);
template uint64_t get_adlog_uint64(uint32_t);
template uint64_t get_adlog_uint64(int64_t);
template uint64_t get_adlog_uint64(uint64_t);
template uint64_t get_adlog_uint64(bool);

template <typename T>
absl::optional<uint64_t> get_adlog_uint64(const RepeatedPtrField<T>& attr_list, int64_t enum_value) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return absl::make_optional(attr.uint64_value());
    }
  }

  return absl::nullopt;
}

template absl::optional<uint64_t> get_adlog_uint64(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);

template <typename T>
absl::Span<const int64_t> get_adlog_int64_list(const RepeatedPtrField<T>& attr_list, int64_t enum_value) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return absl::MakeSpan(attr.int_list_value().data(), attr.int_list_value_size());
    }
  }

  return absl::Span<const ::google::protobuf::int64>();
}

template
absl::Span<const int64_t> get_adlog_int64_list(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
template
absl::Span<const int64_t> get_adlog_int64_list(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);

template <typename T, std::enable_if_t<std::is_integral<T>::value &&
                                       !std::is_same<T, uint64_t>::value, bool>>
absl::Span<const T> get_adlog_int_list(const RepeatedField<T>& arr) {
  return absl::MakeSpan(arr.data(), arr.size());
}

template absl::Span<const int64_t> get_adlog_int_list(const RepeatedField<int64_t>&);
template absl::Span<const int32_t> get_adlog_int_list(const RepeatedField<int32_t>&);

template <typename T,
          std::enable_if_t<std::is_same<T, uint64_t>::value, bool>>
absl::Span<const int64_t> get_adlog_int_list(const RepeatedField<T>& arr) {
  return absl::MakeSpan(reinterpret_cast<const int64_t*>(arr.data()), arr.size());
}

template absl::Span<const int64_t> get_adlog_int_list(const RepeatedField<uint64_t>&);

template <typename T, std::enable_if_t<std::is_floating_point<T>::value, bool>>
float get_adlog_float(T t) {
  return static_cast<float>(t);
}

template float get_adlog_float(float);
template float get_adlog_float(double);

template <typename T>
absl::optional<float> get_adlog_float(const RepeatedPtrField<T>& attr_list, int64_t enum_value) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return absl::make_optional(attr.float_value());
    }
  }

  return absl::nullopt;
}

template
absl::optional<float> get_adlog_float(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
template
absl::optional<float> get_adlog_float(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);
template
absl::optional<float> get_adlog_float(const RepeatedPtrField<LabelInfoCommonAttr>&, int64_t);

template <typename T>
absl::Span<const float> get_adlog_float_list(const RepeatedPtrField<T>& attr_list, int64_t enum_value) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return absl::MakeSpan(attr.float_list_value().data(), attr.float_list_value_size());
    }
  }

  return absl::Span<const float>();
}

template
absl::Span<const float> get_adlog_float_list(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
template
absl::Span<const float> get_adlog_float_list(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);

template <typename T, std::enable_if_t<std::is_floating_point<T>::value, bool>>
absl::Span<const T> get_adlog_float_list(const RepeatedField<T>& arr) {
  return absl::MakeSpan(arr.data(), arr.size());
}

absl::optional<bool> get_adlog_bool(const RepeatedPtrField<LabelInfoCommonAttr>& attr_list,
                                    int64_t enum_value) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return absl::make_optional(attr.bool_value());
    }
  }

  return absl::nullopt;
}

template absl::Span<const float> get_adlog_float_list(const RepeatedField<float>&);
template absl::Span<const double> get_adlog_float_list(const RepeatedField<double>&);

template <typename T>
std::vector<absl::string_view> get_adlog_str_list(const RepeatedPtrField<T>& attr_list, int64_t enum_value) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      std::vector<absl::string_view> res;
      res.reserve(attr.string_list_value_size());
      for (size_t i = 0; i < attr.string_list_value_size(); i++) {
        res.emplace_back(
            absl::string_view(attr.string_list_value(i).data(), attr.string_list_value(i).size()));
      }

      return res;
    }
  }

  return {};
}

template
std::vector<absl::string_view> get_adlog_str_list(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
template
std::vector<absl::string_view> get_adlog_str_list(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);

std::vector<absl::string_view> get_adlog_str_list(const RepeatedPtrField<std::string>& arr) {
  std::vector<absl::string_view> res;
  res.reserve(arr.size());

  for (size_t i = 0; i < arr.size(); i++) { res.emplace_back(arr.Get(i)); }

  return res;
}

template <typename T>
absl::optional<const absl::string_view> get_adlog_str(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value
) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return absl::make_optional<absl::string_view>(attr.string_value().data(), attr.string_value().size());
    }
  }

  return absl::nullopt;
}

template absl::optional<const absl::string_view> get_adlog_str(
  const RepeatedPtrField<CommonInfoAttr>&,
  int64_t);
template absl::optional<const absl::string_view> get_adlog_str(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t);

absl::string_view get_adlog_str(const std::string& s) { return absl::string_view(s); }
absl::string_view get_adlog_str(const std::string* s) { return absl::string_view(*s); }

template <typename T>
ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value
) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return {&attr.map_int64_int64_value()};
    }
  }

  return {};
}

template ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(
  const RepeatedPtrField<CommonInfoAttr>&,
  int64_t);
template ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t);
template ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(
  const RepeatedPtrField<LabelAttr>&,
  int64_t);

template <typename T>
ProtoMapView<int64_t, float> get_adlog_map_int64_float(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value
) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return {&attr.map_int64_float_value()};
    }
  }

  return {};
}

template ProtoMapView<int64_t, float> get_adlog_map_int64_float(
  const RepeatedPtrField<CommonInfoAttr>&,
  int64_t);
template ProtoMapView<int64_t, float> get_adlog_map_int64_float(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t);

template <typename T>
ProtoMapView<uint64_t, bool> get_adlog_map_uint64_bool(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value
) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return {&attr.map_unit64_bool_value()};
    }
  }

  return {};
}

template ProtoMapView<uint64_t, bool> get_adlog_map_uint64_bool(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t
);

template <typename T>
ProtoMapView<int64_t, std::string> get_adlog_map_int64_str(const RepeatedPtrField<T>& attr_list,
                                                           int64_t enum_value) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return {&attr.map_int64_string_value()};
    }
  }

  return {};
}

template ProtoMapView<int64_t, std::string> get_adlog_map_int64_str(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t
);

template <typename T>
ProtoMapView<uint64_t, std::string> get_adlog_map_uint64_str(const RepeatedPtrField<T>& attr_list,
                                                             int64_t enum_value) {
  for (const auto& attr : attr_list) {
    if (attr.name_value() == enum_value) {
      return {&attr.map_unit64_string_value()};
    }
  }

  return {};
}

template ProtoMapView<uint64_t, std::string> get_adlog_map_uint64_str(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t
);

ProtoMapView<int32_t, int64_t> get_adlog_map_int32_int64(const ProtoMap<int32_t, int64_t> &m) {
  return ProtoMapView<int32_t, int64_t>{&m};
}

ProtoMapView<int32_t, uint64_t> get_adlog_map_int32_uint64(const ProtoMap<int32_t, uint64_t> &m) {
  return ProtoMapView<int32_t, uint64_t>{&m};
}

ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(const ProtoMap<int64_t, int64_t>& m) {
  return ProtoMapView<int64_t, int64_t>{&m};
}

ProtoMapView<int64_t, float> get_adlog_map_int64_float(const ProtoMap<int64_t, float>& m) {
  return ProtoMapView<int64_t, float>{&m};
}

ProtoMapView<int64_t, bool> get_adlog_map_int64_bool(const ProtoMap<int64_t, bool>& m) {
  return ProtoMapView<int64_t, bool>{&m};
}

ProtoMapView<uint64_t, bool> get_adlog_map_uint64_bool(const ProtoMap<uint64_t, bool>& m) {
  return ProtoMapView<uint64_t, bool>{&m};
}

template <template <typename, typename> class M, typename K, typename V, typename U>
absl::optional<int64_t> get_adlog_int64(const M<K, V>& label_infos, U key) {
  auto it = label_infos.find(static_cast<K>(key));
  if (it != label_infos.end()) {
    return absl::make_optional(it->second.int_value());
  }

  return absl::nullopt;
}

template <template <typename, typename> class M, typename K, typename V, typename U>
absl::optional<const absl::string_view> get_adlog_str(const M<K, V>& label_infos, U key) {
  auto it = label_infos.find(static_cast<K>(key));
  if (it != label_infos.end()) {
    return absl::make_optional<absl::string_view>(it->second.string_value().data(), it->second.string_value().size());   // NOLINT
  }

  return absl::nullopt;
}

template absl::optional<const absl::string_view> get_adlog_str(const LabelAttrMap<uint64_t>&, int32_t);
template absl::optional<const absl::string_view> get_adlog_str(const LabelAttrMap<uint64_t>&, int64_t);
template absl::optional<const absl::string_view> get_adlog_str(const LabelAttrMap<uint64_t>&, uint64_t);

template absl::optional<int64_t> get_adlog_int64(const LabelAttrMap<uint64_t>&, int32_t);
template absl::optional<int64_t> get_adlog_int64(const LabelAttrMap<uint64_t>&, int64_t);
template absl::optional<int64_t> get_adlog_int64(const LabelAttrMap<uint64_t>&, uint64_t);

template <template <typename, typename> class M, typename K, typename V, typename U>
absl::optional<float> get_adlog_float(const M<K, V>& label_infos, U key) {
  auto it = label_infos.find(static_cast<K>(key));
  if (it != label_infos.end()) {
    return absl::make_optional(it->second.float_value());
  }

  return absl::nullopt;
}

template absl::optional<float> get_adlog_float(const LabelAttrMap<uint64_t>&, int32_t);
template absl::optional<float> get_adlog_float(const LabelAttrMap<uint64_t>&, int64_t);
template absl::optional<float> get_adlog_float(const LabelAttrMap<uint64_t>&, uint64_t);

template <template <typename, typename> class M, typename K, typename V, typename U>
absl::optional<bool> get_adlog_bool(const M<K, V>& label_infos, U key) {
  auto it = label_infos.find(static_cast<K>(key));
  if (it != label_infos.end()) {
    return absl::make_optional(it->second.bool_value());
  }

  return absl::nullopt;
}

template absl::optional<bool> get_adlog_bool(const LabelAttrMap<uint64_t>&, int32_t);
template absl::optional<bool> get_adlog_bool(const LabelAttrMap<uint64_t>&, int64_t);
template absl::optional<bool> get_adlog_bool(const LabelAttrMap<uint64_t>&, uint64_t);

}  // namespace ad_algorithm
}  // namespace ks
