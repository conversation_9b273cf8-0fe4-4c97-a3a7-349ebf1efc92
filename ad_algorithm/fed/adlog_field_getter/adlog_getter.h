#pragma once

#include <cstddef>
#include <cstdint>
#include <iterator>
#include <string>
#include <tuple>
#include <type_traits>
#include <utility>
#include <vector>

#include "absl/types/span.h"
#include "absl/types/optional.h"
#include "base/common/logging.h"
#include "ks/base/container/common.h"
#include "absl/strings/string_view.h"
#include "google/protobuf/repeated_field.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_tool.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/proto_map_view.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_multi_getter.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/action_detail_getter.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/ad_algorithm/fed/compute/search_series_template.h"

namespace ks {
namespace ad_algorithm {

class AdLog;
class AdLogWrapper;

using kuaishou::ad::CommonInfoAttr;
using kuaishou::ad::ContextInfoCommonAttr;
using kuaishou::ad::LabelAttr;
using kuaishou::ad::LabelInfoCommonAttr;

using kuaishou::ad::algorithm::AdActionBaseInfo;
using kuaishou::ad::algorithm::AdActionInfoList;
using kuaishou::ad::algorithm::SimpleAdDspInfo;
using kuaishou::ad::algorithm::SimpleAdDspInfos;
using kuaishou::ad::algorithm::SimpleAdDspInfosV2;
using kuaishou::ad::algorithm::SimpleAdDspInfoV2;
using kuaishou::ad::algorithm::SimpleFansTopInfo;
using kuaishou::ad::algorithm::SimpleLiveInfo;
using kuaishou::ad::algorithm::SimpleLiveInfos;

using kuaishou::ad::algorithm::SimplePhotoInfo;
using kuaishou::ad::algorithm::SimpleUserInfo;

using google::protobuf::RepeatedField;
using google::protobuf::RepeatedPtrField;

uint64_t get_adlog_time(const AdLog& adlog);

inline bool get_adlog_bool(bool v) {
  return v;
}

absl::optional<int64_t> get_adlog_live_or_photo_author_id(const AdLog& adlog, size_t pos);

template <typename T, std::enable_if_t<std::is_integral<T>::value, bool> = true>
T get_adlog_int(T t);

extern template int get_adlog_int(int);
extern template int64_t get_adlog_int(int64_t);
extern template bool get_adlog_int(bool);

template <typename T, std::enable_if_t<std::is_integral<T>::value, bool> = true>
int64_t get_adlog_int64(T t);

extern template int64_t get_adlog_int64(int32_t);
extern template int64_t get_adlog_int64(uint32_t);
extern template int64_t get_adlog_int64(int64_t);
extern template int64_t get_adlog_int64(uint64_t);
extern template int64_t get_adlog_int64(bool);

template <typename T, std::enable_if_t<std::is_integral<T>::value, bool> = true>
uint64_t get_adlog_uint64(T t);

extern template uint64_t get_adlog_uint64(int32_t);
extern template uint64_t get_adlog_uint64(uint32_t);
extern template uint64_t get_adlog_uint64(int64_t);
extern template uint64_t get_adlog_uint64(uint64_t);
extern template uint64_t get_adlog_uint64(bool);

template <typename T, std::enable_if_t<std::is_enum<T>::value, bool> = true> int64_t get_adlog_int64(T t) {
  return static_cast<int64_t>(t);
}

template <typename T, std::enable_if_t<std::is_enum<T>::value, bool> = true> uint64_t get_adlog_uint64(T t) {
  return static_cast<uint64_t>(t);
}

// `CommonInfo` 相关函数。
template <typename T> absl::optional<int64_t> get_adlog_int64(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value);

extern template
absl::optional<int64_t> get_adlog_int64(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
extern template
absl::optional<int64_t> get_adlog_int64(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);

template <typename T> absl::optional<uint64_t> get_adlog_uint64(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value);

extern template
absl::optional<uint64_t> get_adlog_uint64(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);

template <typename T>
absl::Span<const int64_t> get_adlog_int64_list(const RepeatedPtrField<T>& attr_list, int64_t enum_value);

extern template
absl::Span<const int64_t> get_adlog_int64_list(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
extern template
absl::Span<const int64_t> get_adlog_int64_list(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);
extern template
absl::Span<const int64_t> get_adlog_int64_list(const RepeatedPtrField<LabelInfoCommonAttr>&, int64_t);

// 可能实际不是 int64 list, op 处理
template <typename T, std::enable_if_t<std::is_integral<T>::value &&
                                       !std::is_same<T, uint64_t>::value, bool> = true>
absl::Span<const T> get_adlog_int_list(const RepeatedField<T>& arr);

extern template absl::Span<const int64_t> get_adlog_int_list(const RepeatedField<int64_t>&);
extern template absl::Span<const int32_t> get_adlog_int_list(const RepeatedField<int32_t>&);

template <typename T, std::enable_if_t<std::is_same<T, uint64_t>::value, bool> = true>
absl::Span<const int64_t> get_adlog_int_list(const RepeatedField<T>& arr);

extern template absl::Span<const int64_t> get_adlog_int_list(const RepeatedField<uint64_t>&);

template <typename T, std::enable_if_t<std::is_floating_point<T>::value, bool> = true>
float get_adlog_float(T t);

extern template float get_adlog_float(float);
extern template float get_adlog_float(double);

template <typename T>
absl::optional<float> get_adlog_float(const RepeatedPtrField<T>& attr_list, int64_t enum_value);

extern template
absl::optional<float> get_adlog_float(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
extern template
absl::optional<float> get_adlog_float(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);
extern template
absl::optional<float> get_adlog_float(const RepeatedPtrField<LabelInfoCommonAttr>&, int64_t);

template <typename T>
absl::Span<const float> get_adlog_float_list(const RepeatedPtrField<T>& attr_list, int64_t enum_value);

extern template
absl::Span<const float> get_adlog_float_list(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
extern template
absl::Span<const float> get_adlog_float_list(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);

// 搜索 commom attr 特殊逻辑，是否自建链路强卡
template <>
inline int64_t get_adlog_search_call_feature_set(
  const AdLog& log,
  size_t pos,
  int64_t creative_id) {
  // 搜索 commom attr 特殊逻辑，是否自建链路强卡
  int64_t allow_bigcard = 0;
  for (const auto& attr : log.context().info_common_attr()) {
    if (attr.name_value() ==
        ::kuaishou::ad::ContextInfoCommonAttr_Name_SEARCH_RECALL_FEATURE_SET) {
        const auto& feature_map = attr.map_int64_multi_value();
        auto iter = feature_map.find(creative_id);
        if (iter != feature_map.end()) {
          const auto& features = iter->second.int_value();
          allow_bigcard = features.size() >= 14 ? features[13] : -1;
          allow_bigcard = allow_bigcard <= 0 ? 0 : allow_bigcard;
        }
      break;
    }
  }
  return allow_bigcard;
}

// 搜索 commom attr 特殊逻辑，获取 ktype
template <>
inline int64_t get_adlog_search_call_feature_set_ktype(
  const AdLog& log,
  size_t pos,
  int64_t creative_id) {
  // 搜索 commom attr 特殊逻辑，是否自建链路强卡
  int64_t ktype = 0;
  for (const auto& attr : log.context().info_common_attr()) {
    if (attr.name_value() ==
        ::kuaishou::ad::ContextInfoCommonAttr_Name_SEARCH_RECALL_FEATURE_SET) {
        const auto& feature_map = attr.map_int64_multi_value();
        auto iter = feature_map.find(creative_id);
        if (iter != feature_map.end()) {
          const auto& features = iter->second.int_value();
          ktype = features.size() >= 13 ? features[12] : -1;
          ktype = ktype <= 0 ? 0 : ktype;
        }
      break;
    }
  }
  return ktype;
}



// 可能是 float 或者 double
template <typename T, std::enable_if_t<std::is_floating_point<T>::value, bool> = true>
absl::Span<const T> get_adlog_float_list(const RepeatedField<T>& arr);

absl::optional<bool> get_adlog_bool(const RepeatedPtrField<LabelInfoCommonAttr>& attr_list,
                                    int64_t enum_value);

// str_list 统一用 std::vector<absl::string_view>
template <typename T>
std::vector<absl::string_view> get_adlog_str_list(const RepeatedPtrField<T>& attr_list, int64_t enum_value);

extern template
std::vector<absl::string_view> get_adlog_str_list(const RepeatedPtrField<CommonInfoAttr>&, int64_t);
extern template
std::vector<absl::string_view> get_adlog_str_list(const RepeatedPtrField<ContextInfoCommonAttr>&, int64_t);

std::vector<absl::string_view> get_adlog_str_list(const RepeatedPtrField<std::string>& arr);

template <typename T>
absl::optional<const absl::string_view> get_adlog_str(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value);

extern template absl::optional<const absl::string_view> get_adlog_str(
  const RepeatedPtrField<CommonInfoAttr>&,
  int64_t);
extern template absl::optional<const absl::string_view> get_adlog_str(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t);

absl::string_view get_adlog_str(const std::string& s);
absl::string_view get_adlog_str(const std::string* s);

template <typename T>
ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value);

extern template ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(
  const RepeatedPtrField<CommonInfoAttr>&,
  int64_t);
extern template ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t);

template <typename T>
ProtoMapView<int64_t, float> get_adlog_map_int64_float(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value);

extern template ProtoMapView<int64_t, float> get_adlog_map_int64_float(
  const RepeatedPtrField<CommonInfoAttr>&,
  int64_t);
extern template ProtoMapView<int64_t, float> get_adlog_map_int64_float(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t);

template <typename T>
ProtoMapView<int64_t, bool> get_adlog_map_int64_bool(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value);

extern template ProtoMapView<int64_t, bool> get_adlog_map_int64_bool(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t);

// proto 里字段写错了, 用 map_unit64_bool_value()
template <typename T>
ProtoMapView<uint64_t, bool> get_adlog_map_uint64_bool(
  const RepeatedPtrField<T>& attr_list,
  int64_t enum_value);

extern template ProtoMapView<uint64_t, bool> get_adlog_map_uint64_bool(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t);

ProtoMapView<int32_t, int64_t> get_adlog_map_int32_int64(const ProtoMap<int32_t, int64_t>& m);
ProtoMapView<int32_t, uint64_t> get_adlog_map_int32_uint64(const ProtoMap<int32_t, uint64_t>& m);

ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(const ProtoMap<int64_t, int64_t>& m);

ProtoMapView<int64_t, float> get_adlog_map_int64_float(const ProtoMap<int64_t, float>& m);

ProtoMapView<int64_t, bool> get_adlog_map_int64_bool(const ProtoMap<int64_t, bool>& m);

ProtoMapView<uint64_t, bool> get_adlog_map_uint64_bool(const ProtoMap<uint64_t, bool>& m);

template <typename T>
ProtoMapView<int64_t, std::string> get_adlog_map_int64_str(const RepeatedPtrField<T>& attr_list,
                                                           int64_t enum_value);

extern template ProtoMapView<int64_t, std::string> get_adlog_map_int64_str(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t
);

template <typename T>
ProtoMapView<uint64_t, std::string> get_adlog_map_uint64_str(const RepeatedPtrField<T>& attr_list,
                                                             int64_t enum_value);

extern template ProtoMapView<uint64_t, std::string> get_adlog_map_uint64_str(
  const RepeatedPtrField<ContextInfoCommonAttr>&,
  int64_t
);

template <typename K> using LabelAttrMap = ProtoMap<K, LabelAttr>;

template <template <typename, typename> class M, typename K, typename V, typename U>
absl::optional<int64_t> get_adlog_int64(const M<K, V>& label_infos, U key);

template <template <typename, typename> class M, typename K, typename V, typename U>
absl::optional<const absl::string_view> get_adlog_str(const M<K, V>& label_infos, U key);

extern template absl::optional<const absl::string_view> get_adlog_str(const LabelAttrMap<uint64_t>&, int32_t);
extern template absl::optional<const absl::string_view> get_adlog_str(const LabelAttrMap<uint64_t>&, int64_t);
extern template absl::optional<const absl::string_view> get_adlog_str(const LabelAttrMap<uint64_t>&, uint64_t);   // NOLINT

extern template absl::optional<int64_t> get_adlog_int64(const LabelAttrMap<uint64_t>&, int32_t);
extern template absl::optional<int64_t> get_adlog_int64(const LabelAttrMap<uint64_t>&, int64_t);
extern template absl::optional<int64_t> get_adlog_int64(const LabelAttrMap<uint64_t>&, uint64_t);

template <template <typename, typename> class M, typename K, typename V, typename U>
absl::optional<float> get_adlog_float(const M<K, V>& label_infos, U key);

extern template absl::optional<float> get_adlog_float(const LabelAttrMap<uint64_t>&, int32_t);
extern template absl::optional<float> get_adlog_float(const LabelAttrMap<uint64_t>&, int64_t);
extern template absl::optional<float> get_adlog_float(const LabelAttrMap<uint64_t>&, uint64_t);

template <template <typename, typename> class M, typename K, typename V, typename U>
absl::optional<bool> get_adlog_bool(const M<K, V>& label_infos, U key);

extern template absl::optional<bool> get_adlog_bool(const LabelAttrMap<uint64_t>&, int32_t);
extern template absl::optional<bool> get_adlog_bool(const LabelAttrMap<uint64_t>&, int64_t);
extern template absl::optional<bool> get_adlog_bool(const LabelAttrMap<uint64_t>&, uint64_t);

template <template <typename, typename> class M, typename K, typename V, typename U>
ProtoMapView<int64_t, int64_t> get_adlog_map_int64_int64(const M<K, V>& label_infos, U key) {
  auto it = label_infos.find(static_cast<K>(key));
  if (it != label_infos.end()) {
    return {&it->second.map_int64_int64_value()};
  }
  return {};
}

}  // namespace ad_algorithm
}  // namespace ks
