#pragma once

#include <cstdint>
#include <vector>
#include <string>
#include <algorithm>
#include "absl/types/span.h"
#include "absl/types/optional.h"
#include "absl/strings/string_view.h"
#include "teams/ad/ad_algorithm/fed/util/traits.h"

namespace ks {
namespace ad_algorithm {

// 生成随机数 get_random_normal_float
//
// 参数:
// - n: int64_t, 输入, 非负数，非 0
//
// 返回值:
// - 正态分布随机数
std::vector<float> get_random_normal_float(int64_t n);
// 生成随机数 get_random_uniform_int
//
// 参数:
// - n: int64_t, 输入, 非负数，非 0
//
// 返回值:
// - 均匀分布随机数
std::vector<int64_t> get_random_uniform_int(int64_t n);
// 计算自然对数 ln
//
// 参数:
// - x: float, 输入, 非负数，非 0
//
// 返回值:
// - x 合法时返回 ln(x), 否则返回 absl::nullopt
absl::optional<float> log_e(float x);

// 计算 log(1 + x)
//
// 参数:
// - x: float, 输入, 非负数
//
// 返回值:
// - x 合法时返回 log(1 + x), 否则返回 absl::nullopt
absl::optional<float> log_plus_1_e(float x);

// 计算自然对数 ln
//
// 参数:
// - x: float, 输入, 非负数，非 0
// - default_v: float, x 非法时候返回的默认值
//
// 返回值:
// - x 合法时返回 ln(x), 否则返回 default_v
float log_e_or_default(float x, float default_v);

// 计算自然对数 ln, 并转为 int64
//
// 参数:
// - x: float, 输入, 非负数，非 0
//
// 返回值:
// - x 合法时返回 static_cast<int64_t>(ln(x)), 否则返回 absl::nullopt
absl::optional<int64_t> log_e_cast_int(float x);

// 计算自然对数 ln, 并转为 int64
//
// 参数:
// - x: float, 输入, 非负数，非 0
// - default_v: int64_t, x 非法时候返回的默认值
//
// 返回值:
// - x 合法时返回 static_cast<int64_t>(ln(x)), 否则返回 default_v
int64_t log_e_or_default_cast_int(float x, int64_t default_v);

// 计算自然对数 log_10(x)
//
// 参数:
// - x: float, 输入, 非负数，非 0
//
// 返回值:
// - x 合法时返回 log_10(x), 否则返回 absl::nullopt
absl::optional<float> log_10(float x);

// 计算自然对数 log_10(x)
//
// 参数:
// - x: float, 输入, 非负数，非 0
// - default_v: float, x 非法时返回的默认值
//
// 返回值:
// - x 合法时返回 log_10(x), 否则返回 default_v
float log_10_or_default(float x, float default_v);

// 计算自然对数 log_10(x), 并转为 int64
//
// 参数:
// - x: float, 输入, 非负数，非 0
//
// 返回值:
// - x 合法时返回 static_cast<int64_t>(log_10(x)), 否则返回 absl::nullopt
absl::optional<int64_t> log_10_cast_int(float x);

// 计算自然对数 log_10(x), 并转为 int64
//
// 参数:
// - x: float, 输入, 非负数，非 0
// - default_v: int64_t, x 非法时返回的默认值
//
// 返回值:
// - x 合法时返回 static_cast<int64_t>(log_10(x)), 否则返回 default_v
int64_t log_10_or_default_cast_int(float x, int64_t default_v);

// combine 两个 int64_t, 占据 后 52 位，每个 int64_t 占据 26 位。
//
// 参数:
// - left: int64_t, 第一个参数
// - right: int64_t, 第二个参数
//
// 返回值:
// - combine 后的结果
int64_t combine(int64_t left, int64_t right);

template <typename T, std::enable_if_t<has_size_method<T>::value, bool> = true>
std::vector<int64_t> combine(const T& left_vec, int64_t right) {
  std::vector<int64_t> res(left_vec.size(), 0);

  for (size_t i = 0; i < left_vec.size(); i++) { res[i] = combine(left_vec[i], right); }

  return res;
}

template <typename T, std::enable_if_t<has_size_method<T>::value, bool> = true>
std::vector<int64_t> combine(const T& left_vec, absl::optional<int64_t> right) {
  if (right.has_value()) {
    return combine(left_vec, right.value());
  } else {
    return {};
  }
}

template <typename T, std::enable_if_t<has_size_method<T>::value, bool> = true>
std::vector<int64_t> combine(int64_t x, const T& y) {
  return combine(y, x);
}

int64_t combine(int64_t x, absl::optional<int64_t> y);

int64_t city_hash64(const std::string& s);

int64_t city_hash64(absl::string_view s);

int64_t city_hash64(absl::optional<absl::string_view> s);

template <typename T,
          std::enable_if_t<std::is_same<T, std::string>::value || std::is_same<T, absl::string_view>::value,
                            bool> = true>
std::vector<int64_t> city_hash64(absl::Span<const T> vec);

extern template std::vector<int64_t> city_hash64(absl::Span<const std::string>);
extern template std::vector<int64_t> city_hash64(absl::Span<const absl::string_view>);

template <typename T, std::enable_if_t<std::is_same<T, std::string>::value ||
                                        std::is_same<T, absl::string_view>::value, bool> = true>
std::vector<int64_t> city_hash64(const std::vector<T>& vec);

extern template std::vector<int64_t> city_hash64(const std::vector<std::string>&);
extern template std::vector<int64_t> city_hash64(const std::vector<absl::string_view>&);

std::vector<float> int64_split_to_float(int64_t v, int mod_num);

float div(float n, float m);
int64_t mod(int64_t n, int64_t m);
int64_t add(int64_t n, int64_t m);
int64_t max(int64_t n, int64_t m);
int64_t min(int64_t n, int64_t m);
std::vector<float> split_int64_to_3_float(absl::optional<int64_t> value);
std::vector<float> list_div_function(const std::vector<float> & v, const std::vector<float> & m);
// 时间戳转化，由秒转化为毫秒
std::vector<int64_t> timestamp_second_2_millisecond(absl::Span<const int64_t> vec);

template <typename T, typename U, typename V>
std::string concat_str_3(T t, U u, V v);

extern template std::string concat_str_3(int64_t, int64_t, int64_t);
extern template std::string concat_str_3(const std::string&, const std::string&, const std::string&);
extern template std::string concat_str_3(absl::string_view, absl::string_view, absl::string_view);

template <typename T>
absl::Span<const T> take(absl::Span<const T> vec, size_t limit);

extern template absl::Span<const int64_t> take(absl::Span<const int64_t>, size_t);
extern template absl::Span<const uint64_t> take(absl::Span<const uint64_t>, size_t);

template <typename T, std::enable_if_t<has_size_method<T>::value, bool> = true>
absl::optional<uint64_t> get_nth_uint64(const T& vec, size_t index);

extern template absl::optional<uint64_t> get_nth_uint64(const std::vector<int32_t>&, size_t);
extern template absl::optional<uint64_t> get_nth_uint64(const std::vector<int64_t>&, size_t);
extern template absl::optional<uint64_t> get_nth_uint64(const std::vector<uint64_t>&, size_t);

extern template absl::optional<uint64_t> get_nth_uint64(const absl::Span<const int32_t>&, size_t);
extern template absl::optional<uint64_t> get_nth_uint64(const absl::Span<const int64_t>&, size_t);
extern template absl::optional<uint64_t> get_nth_uint64(const absl::Span<const uint64_t>&, size_t);

// combine 三个 int64_t 参数, 占据 后 52 位
//
// 参数:
// - left: int64_t, 第一个参数
// - middle: int64_t, 第二个参数
// - right: int64_t, 第三个参数
//
// 返回值:
// - combine 后的结果
int64_t combine_3(absl::optional<int64_t> left, absl::optional<int64_t> middle,
                  absl::optional<int64_t> right);

int64_t combine_3(int64_t left, int64_t middle, int64_t right);

// 转化只包含一位有效数字的 float 参数为 int64_t
absl::optional<int64_t> float_10x_to_int(absl::optional<float> v);

// 根据 index 从数组中取值
//
// 参数:
// - list: int64_t 数组
// - index: int 索引
// 返回值:
// - 数组中对应索引的值，若没有返回默认值 0
int64_t get_value_from_list(absl::Span<const int64_t> list, int32_t index);

}  // namespace ad_algorithm
}  // namespace ks
