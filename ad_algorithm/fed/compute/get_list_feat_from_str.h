#pragma once

#include <vector>

#include "absl/strings/string_view.h"
#include "absl/types/optional.h"
#include "absl/types/span.h"
#include "base/strings/string_split.h"

namespace ks {
namespace ad_algorithm {

std::vector<int64_t> get_int_list_feat_from_str(absl::optional<absl::string_view> input_str,
                                                int64_t queue_idx, int64_t feature_idx);
std::vector<int64_t> get_str_list_feat_from_str(absl::optional<absl::string_view> input_str,
                                                int64_t queue_idx, int64_t feature_idx);

}  // namespace ad_algorithm
}  // namespace ks
