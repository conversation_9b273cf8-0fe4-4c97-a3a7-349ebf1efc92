#include <string>
#include <vector>
#include <functional>
#include <unordered_set>
#include <sstream>
#include <algorithm>

#include "glog/logging.h"
#include "absl/types/span.h"
#include "teams/ad/ad_algorithm/fed/compute/get_dense_ple.h"

namespace ks {
namespace ad_algorithm {

std::vector<float> get_dense_ple_helper(float val, const std::vector<float>& bins) {
  std::vector<float> res(bins.size() - 1, 0);
  if (val > bins[0]) {
    for (int i = 0; i < bins.size() - 1; ++i) {
      if (val >= bins[i + 1]) {
        res[i] = 1.0;
      } else {
        res[i] = (val - bins[i]) / (bins[i + 1] - bins[i]);
        break;
      }
    }
  }
  return res;
}

std::vector<float> get_ts_list_dense_ple_helper(
  int64_t current_ts,
  absl::Span<const int64_t> ts_list,
  const std::vector<float>& bins,
  size_t max_len
) {
  size_t bins_size = bins.size();
  std::vector<float> res((bins_size - 1) * max_len, 0);
  size_t ts_size = ts_list.size();
  size_t min_size = std::min(ts_size, max_len);

  for (int j = 0; j < min_size; ++j) {
    float val = static_cast<float>((current_ts - ts_list[j]) / 3600000) / 24;
    int idx_bias = j * (bins_size - 1);
    if (bins_size > 0 && val > bins[0]) {
      for (int i = 0; i < bins_size - 1; ++i) {
        if (val >= bins[i + 1]) {
          res[i + idx_bias] = 1.0;
        } else {
          if (bins[i + 1] != bins[i]) {
            res[i + idx_bias] = (val - bins[i]) / (bins[i + 1] - bins[i]);
          }
          break;
        }
      }
    }
  }
  return res;
}

std::vector<uint64_t> get_ts_list_bins_helper(
  int64_t current_ts,
  absl::Span<const int64_t> ts_list,
  const std::vector<float>& bins,
  size_t max_len
) {
  size_t bins_size = bins.size();
  size_t ts_size = ts_list.size();
  size_t min_size = std::min(ts_size, max_len);
  std::vector<uint64_t> res(min_size, 0);

  for (int j = 0; j < min_size; ++j) {
    float val = static_cast<float>((current_ts - ts_list[j]) / 3600000) / 24;
    for (int i = 0; i < bins_size; ++i) {
      if (val > bins[i]) {
        res[j] = i + 1;
      } else {
        break;
      }
    }
  }
  return res;
}

const std::vector<float>& get_a_item_cnt_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 5, 10, 20, 50, 200};
  return vec;
}

const std::vector<float>& get_a_item_price_cv_bins() {
  static const std::vector<float> vec = {-1, 0, 0.5, 1, 2, 5};
  return vec;
}

const std::vector<float>& get_lt_op_ts_bins() {
  static const std::vector<float> vec = {0, 2, 7, 30, 90};
  return vec;
}

const std::vector<float>& get_lt_op_ts_bins_detail() {
  static const std::vector<float> vec = {0, 1, 2, 7, 30, 60, 90, 180, 360, 720};
  return vec;
}

const std::vector<float>& get_lt_gs_ts_bins() {
  static const std::vector<float> vec = {0, 2, 7, 30, 90};
  return vec;
}

const std::vector<float>& get_lt_gs_ts_bins_detail() {
  static const std::vector<float> vec = {0, 1, 2, 7, 30, 60, 90};
  return vec;
}

const std::vector<float>& get_u_type_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 2, 3, 4, 5, 6};
  return vec;
}

const std::vector<float>& get_w_level_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 2, 3, 4, 5, 6};
  return vec;
}

const std::vector<float>& get_user_active_30d_bins() {
  static const std::vector<float> vec = {-2, -1, 0, 1, 5, 10, 20, 30};
  return vec;
}

const std::vector<float>& get_user_last_order_date_diff_bins() {
  static const std::vector<float> vec = {-1, 0, 7, 30, 90, 180, 360, 720};
  return vec;
}

const std::vector<float>& get_user_pay_30d_bins() {
  static const std::vector<float> vec = {-2, -1, 0, 1, 5, 20, 50, 200};
  return vec;
}

const std::vector<float>& get_user_pay_365d_bins() {
  static const std::vector<float> vec = {-2, -1, 0, 5, 20, 50, 200, 500};
  return vec;
}

const std::vector<float>& get_user_author_unique_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 5, 20, 50, 200};
  return vec;
}

const std::vector<float>& get_l_type_bins() {
  static const std::vector<float> vec = {-2, -1, 0, 1, 2, 3, 4};
  return vec;
}

const std::vector<float>& get_live_yellow_car_len_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 5, 20, 50, 200, 1000};
  return vec;
}

const std::vector<float>& get_author_history_sell_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 5, 20, 50};
  return vec;
}

const std::vector<float>& get_author_max_on_car_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 5, 20, 100};
  return vec;
}

const std::vector<float>& get_author_max_car_len_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 5, 20, 50, 200, 1000};
  return vec;
}

const std::vector<float>& get_author_item_exp_time_bins() {
  static const std::vector<float> vec = {-1, 0, 60, 120, 600, 1200, 3600};
  return vec;
}

const std::vector<float>& get_author_live_exp_cnt_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 2, 5, 10, 20, 100};
  return vec;
}

const std::vector<float>& get_author_exp_ratio_bins() {
  static const std::vector<float> vec = {-1, 0, 0.02, 0.05, 0.1, 0.3, 0.5, 1.0};
  return vec;
}

std::vector<float> follow_coeff(int64_t pos_id) {
  if (pos_id == 283) {
    return {1.0, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.1, 1.2};
  } else {
    return {1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0};
  }
}

const std::vector<float>& get_l_half_type_bins() {
  static const std::vector<float> vec = {-1, 0, 1, 2, 3, 4, 5, 6, 7, 8};
  return vec;
}

}  // namespace ad_algorithm
}  // namespace ks
