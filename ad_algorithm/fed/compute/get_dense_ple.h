#pragma once

#include <cstdint>
#include <vector>
#include "absl/types/span.h"
#include "absl/types/optional.h"
#include "absl/strings/string_view.h"

namespace ks {
namespace ad_algorithm {

// piecewise linear encoding 辅助函数
// 参数:
// - val: 目标值。
// - bins: 分桶区间。
//
// 返回值:
// - val encoding 的结果，是一个长度为 bins - 1 的 vector。
std::vector<float> get_dense_ple_helper(
  float val,
  const std::vector<float>& bins
);

// piecewise linear encoding 辅助函数, 处理 timestamp list
// 参数:
// - current_ts: 当前时刻。
// - ts_list: 目标值列表。
// - bins: 分桶区间。
// - max_len: 列表最大长度。
//
// 返回值:
// - ts_list encoding 的结果，是一个长度为 (bins - 1) * max_len 的 vector。
std::vector<float> get_ts_list_dense_ple_helper(
  int64_t current_ts,
  absl::Span<const int64_t> ts_list,
  const std::vector<float>& bins,
  size_t max_len
);

// 普通分桶辅助函数, 处理 timestamp list
// 参数:
// - current_ts: 当前时刻。
// - ts_list: 目标值列表。
// - bins: 分桶区间。
// - max_len: 列表最大长度。
//
// 返回值:
// - ts_list encoding 的结果，是一个长度为 max_len 的 vector。
std::vector<uint64_t> get_ts_list_bins_helper(
  int64_t current_ts,
  absl::Span<const int64_t> ts_list,
  const std::vector<float>& bins,
  size_t max_len
);

// 主播多品 ple 数组
const std::vector<float>& get_a_item_cnt_bins();

// 主播价差 ple 数组
const std::vector<float>& get_a_item_price_cv_bins();

// 全域电商用户购买历史时间戳 ple 数组
const std::vector<float>& get_lt_op_ts_bins();

// 全域电商用户购买历史时间戳 sparse 版数组
const std::vector<float>& get_lt_op_ts_bins_detail();

// 全域电商用户商品浏览历史时间戳 ple 数组
const std::vector<float>& get_lt_gs_ts_bins();

// 全域电商用户商品浏览历史时间戳 sparse 数组
const std::vector<float>& get_lt_gs_ts_bins_detail();

// u_type ple 数组
const std::vector<float>& get_u_type_bins();

// w_level ple 数组
const std::vector<float>& get_w_level_bins();

// 电商用户 30d 活跃天数 ple 数组
const std::vector<float>& get_user_active_30d_bins();

// 电商用户最近一次支付距离现在的天数 ple 数组
const std::vector<float>& get_user_last_order_date_diff_bins();

// 电商用户 30d 订单支付次数 ple 数组
const std::vector<float>& get_user_pay_30d_bins();

// 电商用户 365d 订单支付次数 ple 数组
const std::vector<float>& get_user_pay_365d_bins();

// 电商用户支付过不同主播数目 ple 数组
const std::vector<float>& get_user_author_unique_bins();

// l_type ple 数组
const std::vector<float>& get_l_type_bins();

// 直播间小黄车列表长度 ple 数组
const std::vector<float>& get_live_yellow_car_len_bins();

// 主播历史售卖不同商品个数 ple 数组
const std::vector<float>& get_author_history_sell_bins();

// 主播历史最大上架次数 ple 数组
const std::vector<float>& get_author_max_on_car_bins();

// 主播历史小黄车最大长度 ple 数组
const std::vector<float>& get_author_max_car_len_bins();

// 主播平均讲解时间 ple 数组
const std::vector<float>& get_author_item_exp_time_bins();

// 主播直播平均讲解次数 ple 数组
const std::vector<float>& get_author_live_exp_cnt_bins();

// 主播讲解占直播总时长比例 ple 数组
const std::vector<float>& get_author_exp_ratio_bins();

// 对关注页 283 返回不同的系数，用于乘在预估值上
std::vector<float> follow_coeff(int64_t pos_id);

// 主播 l_half_type ple 数组
const std::vector<float>& get_l_half_type_bins();

}  // namespace ad_algorithm
}  // namespace ks
