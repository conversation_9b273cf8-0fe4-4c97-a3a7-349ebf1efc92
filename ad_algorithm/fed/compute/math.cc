#include <cmath>
#include <string>
#include <vector>
#include <algorithm>
#include <sstream>
#include <chrono>
#include <random>

#include "absl/types/span.h"
#include "absl/types/optional.h"
#include "base/hash_function/city.h"
#include "absl/strings/string_view.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/util/traits.h"

namespace ks {
namespace ad_algorithm {

std::vector<float> get_random_normal_float(int64_t n) {
  std::vector<float> res;
  res.reserve(n);
  unsigned seed = std::chrono::system_clock::now().time_since_epoch().count();
  std::default_random_engine gen(seed);
  std::normal_distribution<float> norm{0.0, 1.0};
  for (int i = 0; i < n; i++) {
    res.push_back(norm(gen));
  }
  return res;
}

std::vector<int64_t> get_random_uniform_int(int64_t n) {
  std::vector<int64_t> res;
  res.reserve(n);
  std::random_device rd;
  std::mt19937 rng(rd());
  std::uniform_int_distribution<int> uni(0, 100);
  for (int i = 0; i < n; i++) {
    res.push_back(uni(rng));
  }
  return res;
}

absl::optional<float> log_e(float x) {
  if (x > 0) {
    return absl::make_optional(log(x));
  } else {
    return absl::nullopt;
  }
}

absl::optional<float> log_plus_1_e(float x) {
  if (x >= 0) {
    return absl::make_optional(log(1 + x));
  } else {
    return absl::nullopt;
  }
}

float log_e_or_default(float x, float default_v) {
  if (x > 0) {
    return log(x);
  } else {
    return default_v;
  }
}

absl::optional<int64_t> log_e_cast_int(float x) {
  if (x > 0) {
    return absl::make_optional(static_cast<int64_t>(log(x)));
  } else {
    return absl::nullopt;
  }
}

int64_t log_e_or_default_cast_int(float x, int64_t default_v) {
  if (x > 0) {
    return static_cast<int64_t>(log(x));
  } else {
    return default_v;
  }
}

absl::optional<float> log_10(float x) {
  if (x > 0) {
    return absl::make_optional(log10(x));
  } else {
    return absl::nullopt;
  }
}

float log_10_or_default(float x, float default_v) {
  if (x > 0) {
    return log10(x);
  } else {
    return default_v;
  }
}

absl::optional<int64_t> log_10_cast_int(float x) {
  if (x > 0) {
    return absl::make_optional(static_cast<int64_t>(log10(x)));
  } else {
    return absl::nullopt;
  }
}

int64_t log_10_or_default_cast_int(float x, int64_t default_v) {
  if (x > 0) {
    return static_cast<int64_t>(log10(x));
  } else {
    return default_v;
  }
}

int64_t combine(int64_t left, int64_t right) {
  static constexpr uint64_t prefix_shift_bits = 52;

  static constexpr uint64_t left_bits = 26;
  static constexpr uint64_t right_bits = 26;

  static constexpr uint64_t left_mask = (1ULL << (left_bits)) - 1;
  static constexpr uint64_t left_shift_bits = prefix_shift_bits - left_bits;

  static constexpr uint64_t right_mask = (1ULL << (left_bits)) - 1;
  static constexpr uint64_t right_shift_bits = prefix_shift_bits - left_bits - right_bits;

  return ((left & left_mask) << left_shift_bits) | ((right & right_mask) << right_shift_bits);
}

int64_t combine(int64_t x, absl::optional<int64_t> y) { return combine(x, y.value_or(0)); }

int64_t city_hash64(const std::string& s) {
  return static_cast<int64_t>(base::CityHash64(s.data(), s.size()));
}

int64_t city_hash64(absl::string_view s) {
  return static_cast<int64_t>(base::CityHash64(s.data(), s.size()));
}

int64_t city_hash64(absl::optional<absl::string_view> s) {
  if (s) {
    return static_cast<int64_t>(base::CityHash64(s->data(), s->size()));
  } else {
    return 0l;
  }
}

template <typename T,
          std::enable_if_t<std::is_same<T, std::string>::value ||
                           std::is_same<T, absl::string_view>::value, bool>>
std::vector<int64_t> city_hash64(absl::Span<const T> vec) {
  std::vector<int64_t> res(vec.size());

  for (size_t i = 0; i < vec.size(); i++) { res[i] = base::CityHash64(vec[i].data(), vec[i].size()); }

  return res;
}

template std::vector<int64_t> city_hash64(absl::Span<const std::string>);
template std::vector<int64_t> city_hash64(absl::Span<const absl::string_view>);

template <typename T,
          std::enable_if_t<std::is_same<T, std::string>::value ||
                           std::is_same<T, absl::string_view>::value, bool>>
std::vector<int64_t> city_hash64(const std::vector<T>& vec) {
  return city_hash64(absl::MakeSpan(vec));
}

template std::vector<int64_t> city_hash64(const std::vector<std::string>&);
template std::vector<int64_t> city_hash64(const std::vector<absl::string_view>&);

std::vector<float> int64_split_to_float(int64_t v, int mod_num) {
  std::vector<float> res(3, 0.0);
  res[0] = static_cast<float>(v % mod_num);
  res[1] = static_cast<float>((v / mod_num) % mod_num);
  res[2] = static_cast<float>(((v / mod_num) / mod_num) % mod_num);
  return res;
}

float div(float n, float m) {
  if (m > 0) {
    return n / m;
  } else {
    return 0.0;
  }
}

int64_t mod(int64_t n, int64_t m) { return n % m; }

int64_t add(int64_t n, int64_t m) { return n + m; }

int64_t max(int64_t n, int64_t m) {
  if (n > m) {
    return n;
  } else {
    return m;
  }
}

int64_t min(int64_t n, int64_t m) {
  if (n > m) {
    return m;
  } else {
    return n;
  }
}


std::vector<float> split_int64_to_3_float(
  absl::optional<int64_t> value
) {
  std::vector<float> result(3, 0.0);
  const int MOD_NUM = 10000000;
  result[0] = static_cast<float>(value.value_or(0) % MOD_NUM);
  result[1] = static_cast<float>((value.value_or(0) / MOD_NUM) % MOD_NUM);
  result[2] = static_cast<float>((value.value_or(0) / MOD_NUM / MOD_NUM) % MOD_NUM);
  return result;
}
std::vector<float> list_div_function(const std::vector<float> & v,
                                      const std::vector<float> & m) {
  std::vector<float> res(v.size(), 0.0);
  if (v.size() == m.size()) {
    for (size_t i = 0; i < v.size(); i++) {
      if (m[i] > 0) {
        res[i] = v[i] / m[i];
      } else {
        res[i] = 0.0;
      }
    }
  }
  return res;
}
std::vector<int64_t> timestamp_second_2_millisecond(absl::Span<const int64_t> vec) {
  std::vector<int64_t> res(vec.size());

  for (size_t i = 0; i < vec.size(); i++) { res[i] = vec[i] * 1000; }

  return res;
}

template <typename T, typename U, typename V>
std::string concat_str_3(T t, U u, V v) {
  std::ostringstream oss;
  oss << t << "_" << u << "_" << v;

  return oss.str();
}

template std::string concat_str_3(int64_t, int64_t, int64_t);
template std::string concat_str_3(const std::string&, const std::string&, const std::string&);
template std::string concat_str_3(absl::string_view, absl::string_view, absl::string_view);

template <typename T> absl::Span<const T> take(absl::Span<const T> vec, size_t limit) {
  return absl::Span<const T>{vec.data(), std::min(limit, vec.size())};
}

template absl::Span<const int64_t> take(absl::Span<const int64_t>, size_t);
template absl::Span<const uint64_t> take(absl::Span<const uint64_t>, size_t);

template <typename T, std::enable_if_t<has_size_method<T>::value, bool>>
absl::optional<uint64_t> get_nth_uint64(const T& vec, size_t index) {
  if (index < vec.size()) {
    return absl::make_optional(static_cast<uint64_t>(vec[index]));
  }

  return absl::nullopt;
}

template absl::optional<uint64_t> get_nth_uint64(const std::vector<int32_t>&, size_t);
template absl::optional<uint64_t> get_nth_uint64(const std::vector<int64_t>&, size_t);
template absl::optional<uint64_t> get_nth_uint64(const std::vector<uint64_t>&, size_t);

template absl::optional<uint64_t> get_nth_uint64(const absl::Span<const int32_t>&, size_t);
template absl::optional<uint64_t> get_nth_uint64(const absl::Span<const int64_t>&, size_t);
template absl::optional<uint64_t> get_nth_uint64(const absl::Span<const uint64_t>&, size_t);

int64_t combine_3(absl::optional<int64_t> left, absl::optional<int64_t> middle,
                  absl::optional<int64_t> right) {
  if (left.has_value() && middle.has_value() && right.has_value()) {
    return combine_3(left.value(), middle.value(), right.value());
  } else {
    return 0;
  }
}

int64_t combine_3(int64_t left, int64_t middle, int64_t right) {
  static constexpr uint64_t prefix_shift_bits = 52;
  static constexpr uint64_t left_bits = 22;
  static constexpr uint64_t middle_bits = 15;
  static constexpr uint64_t right_bits = 15;
  static constexpr uint64_t left_mask = (1ULL << (left_bits)) - 1;
  static constexpr uint64_t left_shift_bits = prefix_shift_bits - left_bits;
  static constexpr uint64_t middle_mask = (1ULL << (middle_bits)) - 1;
  static constexpr uint64_t middle_shift_bits = prefix_shift_bits - left_bits - middle_bits;
  static constexpr uint64_t right_mask = (1ULL << (right_bits)) - 1;
  static constexpr uint64_t right_shift_bits = prefix_shift_bits - left_bits - middle_bits - right_bits;
  return ((left & left_mask) << left_shift_bits) |
          ((middle & middle_mask) << middle_shift_bits) |
         ((right & right_mask) << right_shift_bits);
}

absl::optional<int64_t> float_10x_to_int(absl::optional<float> v) {
  if (v.has_value()) {
    return static_cast<int64_t>(v.value() * 10);
  } else {
    return absl::nullopt;
  }
}

int64_t get_value_from_list(absl::Span<const int64_t> list, int32_t index) {
  int32_t len = list.size();
  if (index < 0 || index >= len) {
    return 0;
  }

  return list[index];
}

}  // namespace ad_algorithm
}  // namespace ks
