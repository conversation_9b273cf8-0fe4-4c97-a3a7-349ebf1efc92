#include "teams/ad/ad_algorithm/fed/compute/get_list_feat_from_str.h"

#include <string>
#include <string_view>
#include <utility>
#include <vector>

#include "absl/strings/numbers.h"
#include "absl/strings/str_split.h"
#include "absl/strings/string_view.h"
#include "absl/types/optional.h"
#include "base/hash_function/fast_hash.h"
namespace ks {
namespace ad_algorithm {

std::vector<int64_t> get_int_list_feat_from_str(absl::optional<absl::string_view> input_str,
                                                int64_t queue_idx, int64_t feature_idx) {
  std::vector<int64_t> res_int_list;
  if (input_str && input_str.has_value()) {
    std::string input_string = std::string(input_str.value());
    std::vector<absl::string_view> queue_str_list = absl::StrSplit(input_string, ";");
    if (queue_idx < queue_str_list.size()) {
      std::vector<absl::string_view> item_str_list = absl::StrSplit(queue_str_list[queue_idx], "|");
      for (const auto& item_str : item_str_list) {
        std::vector<absl::string_view> feature_str_list = absl::StrSplit(item_str, ",");
        if (feature_idx < feature_str_list.size()) {
          int64_t feature_value = 0L;
          if (!absl::SimpleAtoi(feature_str_list[feature_idx], &feature_value)) {
            LOG(WARNING) << "convert feature_str to int64_t failed, feature_str: "
                         << feature_str_list[feature_idx] << ", set feature_value to 0!";
          }
          res_int_list.push_back(feature_value);
        } else {
          res_int_list.push_back(0L);
        }
      }
    }
  }
  return res_int_list;
}

std::vector<int64_t> get_str_list_feat_from_str(absl::optional<absl::string_view> input_str,
                                                int64_t queue_idx, int64_t feature_idx) {
  std::vector<int64_t> res_str_list;
  if (input_str && input_str.has_value()) {
    std::string input_string = std::string(input_str.value());
    std::vector<absl::string_view> queue_str_list = absl::StrSplit(input_string, ";");
    if (queue_idx < queue_str_list.size()) {
      std::vector<absl::string_view> item_str_list = absl::StrSplit(queue_str_list[queue_idx], "|");
      for (const auto& item_str : item_str_list) {
        std::vector<absl::string_view> feature_str_list = absl::StrSplit(item_str, ",");
        if (feature_idx < feature_str_list.size()) {
          int64_t hash_id = static_cast<int64_t>(
              base::CityHash64(feature_str_list[feature_idx].data(), feature_str_list[feature_idx].size()));
          res_str_list.push_back(hash_id);
        } else {
          res_str_list.push_back(0L);
        }
      }
    }
  }
  return res_str_list;
}

}  // namespace ad_algorithm
}  // namespace ks
