/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter/bs_mix_ad_front_win_filter_v_2.h"

#include <string>

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/item_filter/mix_ad_front_win_filter_v2.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

bool BSMixAdFrontWinFilterV2::operator()(const BSLog& bslog, size_t pos) const {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return false;
  }
  auto x0 = get_bslog_map_int64_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1594_key,
                                            BSFieldEnum::adlog_context_info_common_attr_key_1594_value, pos);
  auto x1 = get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_creative_base_id, pos);
  auto x2 = get_value_from_map(x0, x1);
  auto x3 = cast_to_int64(x2);
  auto x4 = mix_ad_front_win_filter_v2(x3);
  return x4;
}

}  // namespace ad_algorithm
}  // namespace ks
