#pragma once

/// Auto generated by dark, do not edit!!!

#include <string>

#include "teams/ad/ad_algorithm/bs_log_preprocess/bs_item_filter_base.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/item_filter/mix_ad_front_filter.dark
class BSMixAdFrontWinFilterV2 : public BSItemFilterBase {
 public:
  explicit BSMixAdFrontWinFilterV2(const std::string&) {}
  bool operator()(const BSLog& bslog, size_t pos) const override;
};

REGISTER_PLUGIN(BSItemFilterBase, BSMixAdFrontWinFilterV2, "bs_mix_ad_front_win_filter_v_2");
}  // namespace ad_algorithm
}  // namespace ks
