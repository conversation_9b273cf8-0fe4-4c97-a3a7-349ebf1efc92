/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_city_hash.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"

namespace ks {
namespace ad_algorithm {

ExtractProductNameCityHash::ExtractProductNameCityHash() : FastFeature(FeatureType::DENSE_ITEM) {}
void ExtractProductNameCityHash::Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_str(adlog.item(pos).ad_dsp_info().advertiser_base().product_name());
  auto x2 = city_hash64(x1);
  auto x3 = split_int64_to_3_float(x2);
  add_feature_result(x3, 3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
