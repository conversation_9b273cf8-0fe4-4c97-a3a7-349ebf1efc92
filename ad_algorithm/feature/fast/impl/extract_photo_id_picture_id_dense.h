#pragma once
#include <string>
#include <fstream>
#include <iostream>
#include <vector>
#include <unordered_map>
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"

namespace ks {
namespace ad_algorithm {

class ExtractPhotoIdPictureIdDense : public FastFeature {
 public:
  ExtractPhotoIdPictureIdDense():FastFeature(DENSE_ITEM) {}
  virtual void Extract(const AdLog & adlog, size_t pos, std::vector<ExtractResult>* result) {
    if (adlog.item_size() > pos) {
      auto photo_info = GetPhotoInfo(adlog.item(pos));
      if (photo_info == nullptr) {
        return;
      }
      if (adlog.item(pos).ad_dsp_info().creative().base().photo_id() > 0) {
        const auto& photo_id = photo_info->id();
        AddFeature(0, (float)(photo_id%MOD_NUM), result);
        AddFeature(1, (float)((photo_id/MOD_NUM)%MOD_NUM), result);
        AddFeature(2, (float)((photo_id/MOD_NUM/MOD_NUM)%MOD_NUM), result);
      } else {
        absl::optional<int64_t> picture_id
           = get_adlog_int64(adlog.item(pos).ad_dsp_info().common_info_attr(), 5106855);
        int64_t picid = picture_id.value_or(0);
        AddFeature(0, (float)(picid%MOD_NUM), result);
        AddFeature(1, (float)((picid/MOD_NUM)%MOD_NUM), result);
        AddFeature(2, (float)((picid/MOD_NUM/MOD_NUM)%MOD_NUM), result);
      }
    }
  }

 private:
  const int MOD_NUM = 10000000;
  DISALLOW_COPY_AND_ASSIGN(ExtractPhotoIdPictureIdDense);
};

REGISTER_EXTRACTOR(ExtractPhotoIdPictureIdDense);

}  // namespace ad_algorithm
}  // namespace ks
