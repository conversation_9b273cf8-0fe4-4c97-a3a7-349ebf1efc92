
#include "teams/ad/ad_algorithm/feature/fast/impl/group/group18.h"
#if !defined(ENABLE_BS_FAST_FEATURE) || defined(ENABLE_AD_BS_BOTH)

#include "teams/ad/ad_algorithm/pb_adaptor/adlog_pb_related/adlog_pb_related.h"

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_prm_leads_num_segment.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_wechat_num_segment.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_order_sku_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_order_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_item_g_v_fir.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_item_g_v_sec.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_item_g_v_thr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_sid_fir.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_sid_sec.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_rec_sid_thr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_is_new_product_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_product_stage.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_is_new_creative_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_creative_stage.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_is_new_sdpa_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_sdpa_stage.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_photo_cold_start_is_new_corp_v_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_valid_message_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_llm_dpsk_valid_user.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_llm_exp_1_user.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_photo_llm_dpsk_photo_top_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_inner_cid_top_account.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_item_live_top_layer_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_live_action_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_discount_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mini_game_fix_roi_ratio.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_msg_clk_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_msg_clk_type_x_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_msg_clk_type_x_author_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_dense_msg_cot.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_regular_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_home_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_work_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_residence_place_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_quant_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_uplift_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_mean_15m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_mean_30m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_std_30m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_gmv_rate_30m_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sparse_yellow_car_avg_price_v_2_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_aigc_live_order_author_seq_30d_match_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_explore_sample_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_use_main_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_p_2l_audience_elastic_c_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_p_2l_audience_elastic_c_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_msg_chat_stay_time.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_recl_2_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_recl_2l_3_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_lc_recl_3_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_user_live_action_real_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_mixup_dense_photo_label_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_profile_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_account_maa_cost_level_90d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_account_is_first_cost_in_cur_m.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_account_is_first_cost_in_cur_q.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_ctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_etr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_lvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_in_etr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_in_lvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_ltr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_wtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_uescore_live_htr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_inner_trigger_photo_id.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_inner_trigger_page.cc"    // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_search_inner_trigger_position.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sku_price_top_20_list_rm_dup.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_pos_imp_rate_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_imp_rate_5pp_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_imp_rate_10pp_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_imp_rate_15pp_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_cpm_rate_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_cpm_rate_5pp_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_cpm_rate_10pp_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_cpm_rate_15pp_1d.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inspire_search_query.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_dense_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_sparse_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_callback_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ecom_callback_event_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_visitor_lp_leave_time_7_day.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_sku_price_top_20_list_rm_dup_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_dense_abtest_param_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_sparse_abtest_param_hash_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_combine_query_to_ad_product_aggr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_semantic_id_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_semantic_id_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_semantic_id_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_semantic_id_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_whether_ks_or_fake_user.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_p_2l_audience_elastic_d_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_qcpx_live_p_2l_audience_elastic_d_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_playcard_sequence_share_combine.cc" // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_dense_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_sparse_live_cvr_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_low_cpm_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_cpm_new.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_cot_matched_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_search_order_hc.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_valid_action_semantic_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_valid_action_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_sequence_semantic_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_universe_action_sequence_embedding.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_search_pos_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_time_stamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_spu_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_brand_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_product_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_cate_1_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_bid_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_cate_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_cate_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_cate_4_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_item_title_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_shop_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_time_stamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_spu_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_brand_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_product_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_bid_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_cate_1_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_cate_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_cate_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_cate_4_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_item_title_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_shop_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_imp_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_time_stamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_item_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_spu_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_brand_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_product_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_bid_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_item_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_cate_1_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_cate_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_cate_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_cate_4_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_item_title_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_shop_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_gmv_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_last_pv_ad_max_score_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_last_pv_other_max_score_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_last_pv_lose_ad_max_score_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_last_pv_lose_other_max_score_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_item_set_ids.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_origin_price_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_front_auction_bid_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_search_query_quant_fix_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_incentive_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_mix_rank_unify_gpm.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_inner_order_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_live_audience_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_inner_order_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_inner_order_rank_topk_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_inner_order_rank_topk_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_inner_order_rank_topk_cpr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_inner_order_rank_topk_lvtr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_inner_order_rank_topk_wtr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_live_audience_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_live_audience_rank_topk_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_live_audience_rank_topk_cpr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_live_audience_rank_topk_lvtr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_live_audience_rank_topk_wtr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_live_audience_rank_topk_click_live.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_live_audience_rank_topk_ctr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_abtest_expected_charged_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_ad_delivery_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_ad_show_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_conversion_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_cost_total_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_cover_show_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_item_click_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_photo_played_3_s_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_photo_played_5_s_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_photo_played_duration_ms_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_photo_played_end_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_industry_id_v_3_photo_replayed_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_abtest_expected_charged_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_ad_delivery_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_ad_show_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_conversion_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_cost_total_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_cover_show_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_item_click_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_photo_played_3_s_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_photo_played_5_s_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_photo_played_duration_ms_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_photo_played_end_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_id_photo_replayed_cnt_6.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_shelf_retrieval_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_shelf_item_gyl_offline_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_shelf_item_bh_offline_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_shelf_item_mall_offline_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_mmu_emb_v_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_mmu_emb_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_mmu_cot_sid.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_mmu_cot_sid_single.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_live_id_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_duration_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_item_id_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_item_spu_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_item_category_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_item_x_7category_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_item_volume_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_item_price_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_face_id_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_face_cluster_ids_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_lsp_latest_live_segment_info_time_gap_value.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_expression_spu_level_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_click_spu_level_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_bid_spu_level_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_plvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_psvr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pvtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pwatchtime_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pwtd_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pcpr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pltr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pwtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pftr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pcmtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_phtr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pclick_live_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_p_effective_watch_live_time_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pptr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pepstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_plstr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_petcm_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_uescore_pcmef_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_inner_order_cvr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_live_audience_ctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rank_inner_order_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rank_live_audience_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_rank_uescore_num.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_account_dense_cid_miss_callback.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_live_sid.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_conv_product_ratios_buckets.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_conv_product_name_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_conv_total_cnt_bucket.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_conv_product_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_conv_second_industry_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_click_product_name_cnt.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_click_total_cnt_bucket.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_click_product_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_click_second_industry_names.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_click_product_ratios_buckets.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_impre_live_spu_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_live_spu_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_live_spu_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_impre_live_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_click_live_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_real_time_adx_bid_live_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_div_10.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_id_div_100.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_item_g_v_fir_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_item_g_v_sec_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_item_g_v_sec_v_2_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_item_g_v_thr_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_item_pay_fir_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_item_pay_sec_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_item_pay_sec_v_2_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_item_pay_thr_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_l_r_sid_fir_gv_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_l_r_sid_sec_gv_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_l_r_sid_thr_gv_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_l_r_sid_fir_pay_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_l_r_sid_sec_pay_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_match_l_r_sid_thr_pay_mix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_impression_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_click_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_conversion_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_ctr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_cvr_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_confidence_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_impression_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_click_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_conversion_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_ctr_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_cvr_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_title_select_confidence_0.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_refund_amount_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_refund_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_refund_amount.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_label_duan_ju.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_iaa_fake_action.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_account_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_product_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_industry_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_second_industry_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_time_gap_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_timestamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_is_action_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_sid_1_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_sid_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_sid_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_sid_4_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_sid_5_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_sid_6_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_sid_7_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_sid_8_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_label_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_p_2_c_mask_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_author_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_account_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_product_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_industry_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_second_industry_name_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_time_gap_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_timestamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_sid_1_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_sid_2_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_sid_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_sid_4_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_sid_5_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_sid_6_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_sid_7_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_sid_8_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_c_2_c_mask_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_dense_live_cvr_onemodel_bagging_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_sparse_live_cvr_onemodel_bagging_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_account_fanstop_mask.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_xtab_test.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_ordercnt_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_photo_cost_24h.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_photo_first_level_category_name_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_photo_expect_cv_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_photo_creative_cnt_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_photo_cv_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_photo_second_level_category_name_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_impression_cnt_1_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_target_cost_cnt_37_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_target_cost_cnt_1_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_conversion_cnt_0_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_conversion_cnt_2_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_cost_cnt_1_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_impression_cnt_0_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_impression_cnt_2_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_target_cost_cnt_2_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_target_cost_cnt_0_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_conversion_cnt_1_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_impression_cnt_37_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_cost_cnt_37_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_cost_cnt_2_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_cd_cost_cnt_0_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_ac_account_operation_owner_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_ac_account_operation_depart_level_2_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_ac_account_sale_owner_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_ac_account_sale_depart_level_2_k_v.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_inner_live_uplift_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_inner_photo_uplift_label.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_mix_rank_cpm_feature.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_mix_rank_gpm_feature.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_xtab_tube_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_msg_session_rounds.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_imp_spu_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_click_spu_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_bid_spu_cate_match_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_dense_live_ctr_p_2l_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_qcpx_sparse_live_ctr_p_2l_onemodel_tag.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_semantic_id_1.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_semantic_id_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_universe_pos_semantic_id_3.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_start_time_gap_plain_dis_src2_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_item_live_realtime_time_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_search_order_hc_filter_search.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_dense_outer_ctr_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_sparse_outer_ctr_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_outer_ctr_rank_topk_photo_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_outer_ctr_rank_topk_cvr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_outer_ctr_rank_topk_cpr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_outer_ctr_rank_topk_lvtr.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_local_promotion_style.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_playend_no_goodsview_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_playend_no_itemclick_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_playend_no_orderpay_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_itemclick_no_orderpay_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_user_id_cross_hour.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_ad_photo_played_end_dup_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_ad_item_click_dup_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_ad_item_click_timestamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_ad_item_click_industry_id_v_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_ad_item_pend_timestamp_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_ad_item_pend_industry_id_v_3_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_item_iaa_action_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_today_task_seq_view_coin_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_today_task_seq_normalized_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_today_task_seq_pre_5_sum_view_coin_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_today_task_seq_pre_5_avg_view_coin_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_today_task_seq_pre_5_avg_price_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_today_task_seq_till_now_task_cnt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_incentive_today_task_seq_till_now_task_amt_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_playend_no_itemclick_check_test.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_rank_index_outer_ctr_no_prefix.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_itemimp_no_played_3s_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_play_3s_no_playend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_play_3s_no_goodsview_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_play_3s_no_itemclick_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_realtime_new_play_3s_no_orderpay_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_ad_item_impression_dup_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_simple_product_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play_5s_extend_simple_product_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play_5s_extend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_playend_extend_short_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_short_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_short_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play_3s_extend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_1_no_click_2_extend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_1_no_play_3s_extend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_2_no_conv_extend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play_5s_extend_short_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_lps_extend_short_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play_3s_extend_short_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_extend_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_ecom_with_time_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_ecom_with_time_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_play_3s_extend_ecom_with_time_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_click_extend_with_time_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_click_extend_with_time_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_ad_item_impression_time_stamps_list_test.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_mix_rank_cpm_feature_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_mix_rank_gpm_feature_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_mix_item_set_ids_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_pri_msg_b_msg_type.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_inner_qcpx_live_roas_use_main_ltv.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_order_price_range.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_goods_video_2_item_item_emb.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_dense_ad_merchant_product_goods_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_ad_merchant_product_goods_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_sparse_goods_merchant_product_item_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_privatemsg_emb_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_private_msg_time_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_private_msg_emb_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_fanstop_dense_calibrate_rate.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_item_impression_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_impression_realtime_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_week_stay_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_ad_download_new_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_play_3s_action_nebula_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_click_action_nebula_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_item_click_action_nebula_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_u_ad_week_stay_dup_photo_id_list.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_mix_rank_cpm_bucket_feature.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_context_dense_mix_rank_cpm_bucket_feature_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_ad_time_seq.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_live_ad_time_seq_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_item_dense_unify_cvr_for_roi.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_playend_action_nebula_p_c.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_categroy_interest_clock.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_categroy_l_1_interest_clock.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_categroy_l_2_interest_clock.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_l_1_interest_clock.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_l_2_interest_clock.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_l_3_interest_clock.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_categroy_interest_clock_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_categroy_l_1_interest_clock_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_categroy_l_2_interest_clock_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_l_1_interest_clock_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_l_2_interest_clock_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_industry_l_3_interest_clock_v_2.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_ad_picture_id.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_l_half_type_ple_dense.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_universe_package_name_relevance.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_universe_package_name_pos.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_package_name_pos_relevance.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_package_name_pos_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_package_name_relevance_industry.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_package_name_pos_productname.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_soft_item_id_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_soft_item_type_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_soft_queue_type_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_soft_ltr_rank_idx_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_soft_item_size_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_soft_rank_benefit_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_soft_cpm_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_hard_item_id_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_hard_item_type_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_hard_queue_type_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_hard_ltr_rank_idx_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_hard_item_size_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_hard_rank_benefit_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_hard_cpm_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_soft_item_id_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_soft_item_type_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_soft_queue_type_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_soft_ltr_rank_idx_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_soft_item_size_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_soft_rank_benefit_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_soft_cpm_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_hard_item_id_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_hard_item_type_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_hard_queue_type_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_hard_ltr_rank_idx_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_hard_item_size_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_hard_rank_benefit_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_outer_hard_cpm_list_by_user_extend.cc"   // NOLINT
#include "teams/ad/ad_algorithm/feature/fast/impl/extract_product_name_city_hash.cc"   // NOLINT
void register_group_18() {}

#endif
