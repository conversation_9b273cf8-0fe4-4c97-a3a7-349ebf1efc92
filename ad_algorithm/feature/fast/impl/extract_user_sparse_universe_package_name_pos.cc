/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_universe_package_name_pos.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

ExtractUserSparseUniversePackageNamePos::ExtractUserSparseUniversePackageNamePos()
    : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractUserSparseUniversePackageNamePos::Extract(const AdLog& adlog, size_t pos,
                                                      std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_map_int64_int64(adlog.context().info_common_attr(),
                                      ContextInfoCommonAttr::UNIVERSE_TINY_SEARCH_PACKAGE_NAME_POS);
  auto x2 = get_adlog_str(adlog.item(pos).ad_dsp_info().advertiser_base().product_name());
  auto x3 = city_hash64(x2);
  auto x4 = get_value_from_map(x1, x3);
  auto x6 = value_or(x4, -1);
  add_feature_result(x6, result);
}

}  // namespace ad_algorithm
}  // namespace ks
