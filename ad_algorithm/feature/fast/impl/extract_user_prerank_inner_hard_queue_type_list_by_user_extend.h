#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_user_prerank_info_by_user_extend.dark
class ExtractUserPrerankInnerHardQueueTypeListByUserExtend : public FastFeatureNoPrefix {
 public:
  ExtractUserPrerankInnerHardQueueTypeListByUserExtend();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserPrerankInnerHardQueueTypeListByUserExtend);
};

REGISTER_EXTRACTOR(ExtractUserPrerankInnerHardQueueTypeListByUserExtend);
}  // namespace ad_algorithm
}  // namespace ks
