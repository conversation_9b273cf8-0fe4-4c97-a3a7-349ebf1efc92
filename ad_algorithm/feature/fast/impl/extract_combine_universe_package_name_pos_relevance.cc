/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_package_name_pos_relevance.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

ExtractCombineUniversePackageNamePosRelevance::ExtractCombineUniversePackageNamePosRelevance()
    : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractCombineUniversePackageNamePosRelevance::Extract(const AdLog& adlog, size_t pos,
                                                            std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.context().info_common_attr(),
                            ContextInfoCommonAttr::UNIVERSE_TINY_SEARCH_PACKAGE_NAME_RELEVANCE);
  auto x3 = value_or(x1, -1);
  auto x4 = get_adlog_map_int64_int64(adlog.context().info_common_attr(),
                                      ContextInfoCommonAttr::UNIVERSE_TINY_SEARCH_PACKAGE_NAME_POS);
  auto x5 = get_adlog_str(adlog.item(pos).ad_dsp_info().advertiser_base().product_name());
  auto x6 = city_hash64(x5);
  auto x7 = get_value_from_map(x4, x6);
  auto x9 = value_or(x7, -1);
  auto x10 = combine(x3, x9);
  add_feature_result(x10, result);
}

}  // namespace ad_algorithm
}  // namespace ks
