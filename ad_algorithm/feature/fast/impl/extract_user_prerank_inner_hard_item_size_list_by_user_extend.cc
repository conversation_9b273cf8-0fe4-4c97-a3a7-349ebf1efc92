/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_prerank_inner_hard_item_size_list_by_user_extend.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_list_feat_from_str.h"

namespace ks {
namespace ad_algorithm {

ExtractUserPrerankInnerHardItemSizeListByUserExtend::ExtractUserPrerankInnerHardItemSizeListByUserExtend()
    : FastFeature(FeatureType::DENSE_USER) {}
void ExtractUserPrerankInnerHardItemSizeListByUserExtend::Extract(const AdLog& adlog, size_t pos,
                                                                  std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_str(adlog.user_extend());
  auto x4 = get_int_list_feat_from_str(x1, 1, 4);
  auto x5 = cast_to_float(x4);
  add_feature_result(x5, 15, result);
}

}  // namespace ad_algorithm
}  // namespace ks
