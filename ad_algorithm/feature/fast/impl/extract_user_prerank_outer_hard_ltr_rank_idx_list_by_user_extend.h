#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_user_prerank_info_by_user_extend.dark
class ExtractUserPrerankOuterHardLtrRankIdxListByUserExtend : public FastFeature {
 public:
  ExtractUserPrerankOuterHardLtrRankIdxListByUserExtend();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserPrerankOuterHardLtrRankIdxListByUserExtend);
};

REGISTER_EXTRACTOR(ExtractUserPrerankOuterHardLtrRankIdxListByUserExtend);
}  // namespace ad_algorithm
}  // namespace ks
