/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_user_sparse_universe_package_name_relevance.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

ExtractUserSparseUniversePackageNameRelevance::ExtractUserSparseUniversePackageNameRelevance()
    : FastFeatureNoPrefix(FeatureType::USER) {}
void ExtractUserSparseUniversePackageNameRelevance::Extract(const AdLog& adlog, size_t pos,
                                                            std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.context().info_common_attr(),
                            ContextInfoCommonAttr::UNIVERSE_TINY_SEARCH_PACKAGE_NAME_RELEVANCE);
  auto x3 = value_or(x1, -1);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
