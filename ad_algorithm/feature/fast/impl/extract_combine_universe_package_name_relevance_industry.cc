/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_package_name_relevance_industry.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

ExtractCombineUniversePackageNameRelevanceIndustry::ExtractCombineUniversePackageNameRelevanceIndustry()
    : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractCombineUniversePackageNameRelevanceIndustry::Extract(const AdLog& adlog, size_t pos,
                                                                 std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.context().info_common_attr(),
                            ContextInfoCommonAttr::UNIVERSE_TINY_SEARCH_PACKAGE_NAME_RELEVANCE);
  auto x3 = value_or(x1, -1);
  auto x4 = get_adlog_int64(adlog.item(pos).ad_dsp_info().advertiser_base().industry_id());
  auto x6 = value_or(x4, -1);
  auto x7 = combine(x3, x6);
  add_feature_result(x7, result);
}

}  // namespace ad_algorithm
}  // namespace ks
