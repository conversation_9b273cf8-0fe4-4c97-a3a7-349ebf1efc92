/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_combine_universe_package_name_pos_productname.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

ExtractCombineUniversePackageNamePosProductname::ExtractCombineUniversePackageNamePosProductname()
    : FastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {}
void ExtractCombineUniversePackageNamePosProductname::Extract(const AdLog& adlog, size_t pos,
                                                              std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_str(adlog.item(pos).ad_dsp_info().advertiser_base().product_name());
  auto x2 = city_hash64(x1);
  auto x3 = get_adlog_map_int64_int64(adlog.context().info_common_attr(),
                                      ContextInfoCommonAttr::UNIVERSE_TINY_SEARCH_PACKAGE_NAME_POS);
  auto x5 = city_hash64(x1);
  auto x6 = get_value_from_map(x3, x5);
  auto x8 = value_or(x6, -1);
  auto x9 = combine(x2, x8);
  add_feature_result(x9, result);
}

}  // namespace ad_algorithm
}  // namespace ks
