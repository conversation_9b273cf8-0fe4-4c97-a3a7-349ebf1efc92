/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/feature/fast/impl/extract_author_l_half_type_ple_dense.h"

#include "teams/ad/ad_algorithm/fed/adlog_field_getter/adlog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_dense_ple.h"

namespace ks {
namespace ad_algorithm {

ExtractAuthorLHalfTypePleDense::ExtractAuthorLHalfTypePleDense() : FastFeature(FeatureType::DENSE_ITEM) {}
void ExtractAuthorLHalfTypePleDense::Extract(const AdLog& adlog, size_t pos,
                                             std::vector<ExtractResult>* result) {
  auto x1 = get_adlog_int64(adlog.item(pos).ad_dsp_info().live_info().common_info_attr(), 5106909);
  auto x3 = cast_to_float_with_default(x1, -1);
  const auto& x4 = get_l_half_type_bins();
  auto x5 = get_dense_ple_helper(x3, x4);
  add_feature_result(x5, 8, result);
}

}  // namespace ad_algorithm
}  // namespace ks
