#pragma once

/// Auto generated by dark, do not edit!!!

#include <vector>

#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature.h"
#include "teams/ad/ad_algorithm/feature/fast/frame/fast_feature_no_prefix.h"

namespace ks {
namespace ad_algorithm {

// dark_feature: teams/ad/ad_algorithm/dark_feature/impl/extract_user_sparse_universe_pkg_name.dark
class ExtractUserSparseUniversePackageNameRelevance : public FastFeatureNoPrefix {
 public:
  ExtractUserSparseUniversePackageNameRelevance();
  void Extract(const AdLog& adlog, size_t pos, std::vector<ExtractResult>* result);

 private:
  DISALLOW_COPY_AND_ASSIGN(ExtractUserSparseUniversePackageNameRelevance);
};

REGISTER_EXTRACTOR(ExtractUserSparseUniversePackageNameRelevance);
}  // namespace ad_algorithm
}  // namespace ks
