/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_prerank_inner_hard_item_type_list_by_user_extend.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_list_feat_from_str.h"

namespace ks {
namespace ad_algorithm {

BSExtractUserPrerankInnerHardItemTypeListByUserExtend::BSExtractUserPrerankInnerHardItemTypeListByUserExtend()
    : BSFastFeatureNoPrefix(FeatureType::USER) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_user_extend);
}
void BSExtractUserPrerankInnerHardItemTypeListByUserExtend::Extract(const BSLog& bslog, size_t pos,
                                                                    std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_str_or_default(*bs, BSFieldEnum::adlog_user_extend, pos);
  auto x4 = get_str_list_feat_from_str(x1, 1, 1);
  add_feature_result(x4, result);
}

}  // namespace ad_algorithm
}  // namespace ks
