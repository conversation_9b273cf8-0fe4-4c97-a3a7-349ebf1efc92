#pragma once
#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>
#include <vector>

#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_fast_feature.h"

namespace ks {
namespace ad_algorithm {

class BSExtractPhotoIdPictureIdDense : public BSFastFeature {
 public:
  BSExtractPhotoIdPictureIdDense();
  virtual void Extract(const BSLog& bslog, size_t pos, std::vector<ExtractResult>* result);

 private:
  const int MOD_NUM = 10000000;

 private:
  BSPhotoInfo<uint64_t> BSGetPhotoInfoId{"id"};
  BSHasPhotoInfoImpl BSHasPhotoInfo{""};
  DISALLOW_COPY_AND_ASSIGN(BSExtractPhotoIdPictureIdDense);
};

REGISTER_BS_EXTRACTOR(BSExtractPhotoIdPictureIdDense);

}  // namespace ad_algorithm
}  // namespace ks
