/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_universe_package_name_pos_industry.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

BSExtractCombineUniversePackageNamePosIndustry::BSExtractCombineUniversePackageNamePosIndustry()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1592_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1592_value);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
}
void BSExtractCombineUniversePackageNamePosIndustry::Extract(const BSLog& bslog, size_t pos,
                                                             std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id, pos);
  auto x3 = value_or(x1, -1);
  auto x4 = get_bslog_map_int64_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1592_key,
                                            BSFieldEnum::adlog_context_info_common_attr_key_1592_value, pos);
  auto x5 =
      get_bslog_str_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name, pos);
  auto x6 = city_hash64(x5);
  auto x7 = get_value_from_map(x4, x6);
  auto x9 = value_or(x7, -1);
  auto x10 = combine(x3, x9);
  add_feature_result(x10, result);
}

}  // namespace ad_algorithm
}  // namespace ks
