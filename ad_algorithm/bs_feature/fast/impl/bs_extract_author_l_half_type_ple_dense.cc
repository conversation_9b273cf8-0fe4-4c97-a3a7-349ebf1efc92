/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_author_l_half_type_ple_dense.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/cast.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/get_dense_ple.h"

namespace ks {
namespace ad_algorithm {

BSExtractAuthorLHalfTypePleDense::BSExtractAuthorLHalfTypePleDense()
    : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5106909);
}
void BSExtractAuthorLHalfTypePleDense::Extract(const BSLog& bslog, size_t pos,
                                               std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 =
      get_bslog_int64(*bs, BSFieldEnum::adlog_item_ad_dsp_info_live_info_common_info_attr_key_5106909, pos);
  auto x3 = cast_to_float_with_default(x1, -1);
  const auto& x4 = get_l_half_type_bins();
  auto x5 = get_dense_ple_helper(x3, x4);
  add_feature_result(x5, 8, result);
}

}  // namespace ad_algorithm
}  // namespace ks
