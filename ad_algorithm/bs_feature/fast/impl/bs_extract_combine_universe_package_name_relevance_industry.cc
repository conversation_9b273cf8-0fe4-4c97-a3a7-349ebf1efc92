/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_universe_package_name_relevance_industry.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

BSExtractCombineUniversePackageNameRelevanceIndustry::BSExtractCombineUniversePackageNameRelevanceIndustry()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1591);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id);
}
void BSExtractCombineUniversePackageNameRelevanceIndustry::Extract(const BSLog& bslog, size_t pos,
                                                                   std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1591, pos);
  auto x3 = value_or(x1, -1);
  auto x4 =
      get_bslog_int64_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_industry_id, pos);
  auto x6 = value_or(x4, -1);
  auto x7 = combine(x3, x6);
  add_feature_result(x7, result);
}

}  // namespace ad_algorithm
}  // namespace ks
