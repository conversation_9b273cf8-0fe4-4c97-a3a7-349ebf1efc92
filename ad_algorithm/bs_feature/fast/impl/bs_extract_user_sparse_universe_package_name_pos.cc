/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_universe_package_name_pos.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

BSExtractUserSparseUniversePackageNamePos::BSExtractUserSparseUniversePackageNamePos()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1592_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1592_value);
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
}
void BSExtractUserSparseUniversePackageNamePos::Extract(const BSLog& bslog, size_t pos,
                                                        std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_map_int64_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1592_key,
                                            BSFieldEnum::adlog_context_info_common_attr_key_1592_value, pos);
  auto x2 =
      get_bslog_str_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name, pos);
  auto x3 = city_hash64(x2);
  auto x4 = get_value_from_map(x1, x3);
  auto x6 = value_or(x4, -1);
  add_feature_result(x6, result);
}

}  // namespace ad_algorithm
}  // namespace ks
