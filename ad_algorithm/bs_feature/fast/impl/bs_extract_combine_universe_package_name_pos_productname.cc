/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_combine_universe_package_name_pos_productname.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

BSExtractCombineUniversePackageNamePosProductname::BSExtractCombineUniversePackageNamePosProductname()
    : BSFastFeatureNoPrefix(FeatureType::COMBINE, 26, 26) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1592_key);
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1592_value);
}
void BSExtractCombineUniversePackageNamePosProductname::Extract(const BSLog& bslog, size_t pos,
                                                                std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 =
      get_bslog_str_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name, pos);
  auto x2 = city_hash64(x1);
  auto x3 = get_bslog_map_int64_int64<true>(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1592_key,
                                            BSFieldEnum::adlog_context_info_common_attr_key_1592_value, pos);
  auto x5 = city_hash64(x1);
  auto x6 = get_value_from_map(x3, x5);
  auto x8 = value_or(x6, -1);
  auto x9 = combine(x2, x8);
  add_feature_result(x9, result);
}

}  // namespace ad_algorithm
}  // namespace ks
