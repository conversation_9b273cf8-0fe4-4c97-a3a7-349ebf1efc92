/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_product_name_city_hash.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/math.h"

namespace ks {
namespace ad_algorithm {

BSExtractProductNameCityHash::BSExtractProductNameCityHash() : BSFastFeature(FeatureType::DENSE_ITEM) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name);
}
void BSExtractProductNameCityHash::Extract(const BSLog& bslog, size_t pos,
                                           std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 =
      get_bslog_str_or_default(*bs, BSFieldEnum::adlog_item_ad_dsp_info_advertiser_base_product_name, pos);
  auto x2 = city_hash64(x1);
  auto x3 = split_int64_to_3_float(x2);
  add_feature_result(x3, 3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
