/// Auto generated by dark, do not edit!!!

#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_user_sparse_universe_package_name_relevance.h"

#include "teams/ad/ad_algorithm/fed/bslog_field_getter/bslog_getter.h"
#include "teams/ad/ad_algorithm/fed/compute/feature_appender.h"
#include "teams/ad/ad_algorithm/fed/compute/util.h"

namespace ks {
namespace ad_algorithm {

BSExtractUserSparseUniversePackageNameRelevance::BSExtractUserSparseUniversePackageNameRelevance()
    : BSFastFeatureNoPrefix(FeatureType::USER) {
  attr_metas_.emplace_back(BSFieldEnum::adlog_context_info_common_attr_key_1591);
}
void BSExtractUserSparseUniversePackageNameRelevance::Extract(const BSLog& bslog, size_t pos,
                                                              std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (KS_UNLIKELY(bs == nullptr)) {
    return;
  }
  auto x1 = get_bslog_int64(*bs, BSFieldEnum::adlog_context_info_common_attr_key_1591, pos);
  auto x3 = value_or(x1, -1);
  add_feature_result(x3, result);
}

}  // namespace ad_algorithm
}  // namespace ks
