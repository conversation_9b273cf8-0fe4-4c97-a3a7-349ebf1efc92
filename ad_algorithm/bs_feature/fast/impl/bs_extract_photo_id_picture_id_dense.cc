#include "teams/ad/ad_algorithm/bs_feature/fast/impl/bs_extract_photo_id_picture_id_dense.h"

namespace ks {
namespace ad_algorithm {
BSExtractPhotoIdPictureIdDense::BSExtractPhotoIdPictureIdDense() : BSFastFeature(DENSE_ITEM) {
  BSGetPhotoInfoId.fill_attr_metas(&attr_metas_);
  BSHasPhotoInfo.fill_attr_metas(&attr_metas_);
}

void BSExtractPhotoIdPictureIdDense::Extract(const BSLog& bslog, size_t pos,
                      std::vector<ExtractResult>* result) {
  auto bs = bslog.GetBS();
  if (bs == nullptr) {
    return;
  }

  if (!BSHasPhotoInfo(bs, pos)) {
    return;
  }
  if (BSFieldHelper::GetSingular<uint64_t>(*bs,
       BSFieldEnum::adlog_item_ad_dsp_info_creative_base_photo_id, pos) > 0) {
    AddFeature(0, (float)(BSGetPhotoInfoId(bs, pos) % MOD_NUM), result);
    AddFeature(1, (float)((BSGetPhotoInfoId(bs, pos) / MOD_NUM) % MOD_NUM), result);
    AddFeature(2, (float)((BSGetPhotoInfoId(bs, pos) / MOD_NUM / MOD_NUM) % MOD_NUM), result);
  } else {
    int64_t picture_id = get_bslog_int64_or_default(*bs,
              BSFieldEnum::adlog_item_ad_dsp_info_common_info_attr_key_5106855, pos);
    AddFeature(0, (float)(picture_id % MOD_NUM), result);
    AddFeature(1, (float)((picture_id / MOD_NUM) % MOD_NUM), result);
    AddFeature(2, (float)((picture_id / MOD_NUM / MOD_NUM) % MOD_NUM), result);
  }
}
}  // namespace ad_algorithm
}  // namespace ks
