#include "teams/ad/front_server/processor/mix_benefit/fill_other_mix_info_mixer.h"

#include <algorithm>
#include <cmath>
#include <memory>
#include <set>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/strings/str_format.h"
#include "absl/strings/substitute.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "google/protobuf/util/json_util.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_base/src/container/doubly_buffered_data.h"
#include "teams/ad/ad_base/src/container/singleton.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_base/src/redis/redis_util.h"
#include "teams/ad/ad_base/src/common/app_version_compare.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/utils/ad_utility.h"
#include "teams/ad/engine_base/dragon_node/skydome_node.h"
#include "teams/ad/front_server/util/kconf/kconf.h"
#include "teams/ad/front_server/util/spdm/spdm_switches.h"
#include "teams/ad/front_server/util/merchant_util/merchant_style_util.h"
#include "teams/ad/front_server/util/utility/ksn_util.h"
#include "teams/ad/front_server/util/adx/adx_common_util.h"

namespace ks {
namespace front_server {

FillOtherMixInfoMixer::FillOtherMixInfoMixer() {
}

bool FillOtherMixInfoMixer::InitProcessor() {
  return true;
}

void FillOtherMixInfoMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  ProcessInner(context);
}

bool FillOtherMixInfoMixer::LocalInitialize(ks::platform::AddibleRecoContextInterface* context) {
  auto ps_context = ks::engine_base::dragon::SkydomeBaseNode::GetAdContext<AdContext>(context);
  if (ps_context == nullptr) {
    return false;
  }
  session_data_ = ps_context->GetMutableContextData<ContextData>();
  if (session_data_ == nullptr) {
    return false;
  }
  ad_priority_num = 0;
  // 混排 gpm 校准系数初始化
  enable_mtb_unify_gpm_cali_ = SPDM_enable_mtb_unify_gpm_cali(session_data_->get_spdm_ctx());
  enable_add_multi_head_mix_gpm_live_ =
      SPDM_enable_add_multi_head_mix_gpm_live(session_data_->get_spdm_ctx());
  enable_add_multi_head_mix_gpm_photo_ =
      SPDM_enable_add_multi_head_mix_gpm_photo(session_data_->get_spdm_ctx());
  enable_mix_unify_gpm_min_value_ =
      SPDM_enable_mix_unify_gpm_min_value(session_data_->get_spdm_ctx());
  disable_multi_head_mix_gpm_outer_ =
      SPDM_disable_multi_head_mix_gpm_outer(session_data_->get_spdm_ctx());
  mix_unify_gpm_min_value_ =
      SPDM_mix_unify_gpm_min_value(session_data_->get_spdm_ctx());
  unify_gpm_cali_params_.set_soft_live_cali_ratio(1.0);
  unify_gpm_cali_params_.set_hard_live_cali_ratio(1.0);
  unify_gpm_cali_params_.set_soft_photo_cali_ratio(1.0);
  unify_gpm_cali_params_.set_hard_photo_cali_ratio(1.0);
  std::string mix_gpm_cali_tag = SPDM_mtb_unify_gpm_cali_tag(session_data_->get_spdm_ctx());
  const std::string& app_id = ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request()));
  auto ptr = FrontKconfUtil::unifyGpmCaliConf();
  if (ptr != nullptr) {
    auto& unify_gpm_cali_conf = ptr->data().tag_map();
    std::string cali_tag = absl::Substitute("$0_$1", mix_gpm_cali_tag, app_id);
    if (unify_gpm_cali_conf.find(cali_tag) != unify_gpm_cali_conf.end()) {
      unify_gpm_cali_params_ = unify_gpm_cali_conf.at(cali_tag);
    }
  }
  enable_mtb_v2_bonus_ratio_ = SPDM_enable_mtb_v2_bonus_ratio(session_data_->get_spdm_ctx());
  mtb_v2_bonus_ratio_ = SPDM_mtb_v2_bonus_ratio(session_data_->get_spdm_ctx());
  enable_mtb_v2_hc_gpm_using_max_ = SPDM_enable_mtb_v2_hc_gpm_using_max(session_data_->get_spdm_ctx());
  enable_fill_unify_bonus_info_inner_explore_ =
    SPDM_enable_fill_unify_bonus_info_inner_explore(session_data_->get_spdm_ctx());
  enable_replace_mix_bonus_with_cpm_inner_explore_ =
    SPDM_enable_replace_mix_bonus_with_cpm_inner_explore(session_data_->get_spdm_ctx());
  replace_mix_bonus_with_cpm_ratio_inner_explore_ =
    SPDM_replace_mix_bonus_with_cpm_ratio_inner_explore(session_data_->get_spdm_ctx());
  enable_mtb_v2_inner_bonus_include_hc_gpm_ =
      SPDM_enable_mtb_v2_inner_bonus_include_hc_gpm(session_data_->get_spdm_ctx());
  enable_mix_bonus_adjust_ = SPDM_enable_mix_bonus_adjust(session_data_->get_spdm_ctx());
  mix_bonus_adjust_enhance_ratio_ = SPDM_mix_bonus_adjust_enhance_ratio(session_data_->get_spdm_ctx());
  mix_unify_cpm_ratio_inner_ = SPDM_mix_unify_cpm_ratio_inner(session_data_->get_spdm_ctx());
  mix_unify_cpm_ratio_outer_ = SPDM_mix_unify_cpm_ratio_outer(session_data_->get_spdm_ctx());
  mix_unify_score_ratio_ = SPDM_mix_unify_score_exp_ratio(session_data_->get_spdm_ctx());
  mix_unify_score_ratio_a_ = SPDM_mix_unify_score_exp_ratio_a(session_data_->get_spdm_ctx());
  mix_unify_score_ratio_b_ = SPDM_mix_unify_score_exp_ratio_b(session_data_->get_spdm_ctx());
  mix_unify_cpm_ratio_ = SPDM_mix_unify_cpm_exp_ratio(session_data_->get_spdm_ctx());
  mix_unify_gpm_ratio_ = SPDM_mix_unify_gpm_exp_ratio(session_data_->get_spdm_ctx());
  mix_unify_bonus_ratio_ = SPDM_mix_unify_bonus_exp_ratio(session_data_->get_spdm_ctx());
  disable_fctr_fanstop_bonus_ = SPDM_disable_fctr_fanstop_bonus(session_data_->get_spdm_ctx());
  // 主站发现页内流混排因子
  enable_inner_explore_mix_hc_opt = SPDM_enable_inner_explore_mix_hc_opt(session_data_->get_spdm_ctx());
  enable_inner_explore_mix_cpm_opt = SPDM_enable_inner_explore_mix_cpm_opt(session_data_->get_spdm_ctx());
  const std::string& inner_mix_cpm_tag = SPDM_inner_explore_mix_cpm_tag(session_data_->get_spdm_ctx());
  enable_nogap_filter_by_cpm_ = SPDM_enable_nogap_filter_by_cpm(session_data_->get_spdm_ctx());
  enable_nogap_filter_by_wgroup_ = SPDM_enable_nogap_filter_by_wgroup(session_data_->get_spdm_ctx());
  nogap_cpm_thr_ = SPDM_nogap_cpm_thr(session_data_->get_spdm_ctx());
  enable_nogap_filter_by_jumpout_rate_ =
     SPDM_enable_nogap_filter_by_jumpout_rate(session_data_->get_spdm_ctx());
  nogap_jumpout_rate_thr_ = SPDM_nogap_jumpout_rate_thr(session_data_->get_spdm_ctx());
  inner_uplift_cpm_conf_.clear();
  auto data_conf = FrontKconfUtil::exploreFeedMixCpmConf();
  if (data_conf != nullptr) {
    const auto& score_item = data_conf->data().exp_conf();
    auto items = score_item.find(inner_mix_cpm_tag);
    if (items != score_item.end()) {
      for (const auto& item : items->second.data()) {
        inner_uplift_cpm_conf_.push_back(std::make_pair(item.score(), item.ratio()));
      }
    }
  }
  enable_inner_explore_mix_price_adjust_ratio_ = SPDM_enableInnerExploreMixPriceAdjust();
  enable_fix_set_mix_price_adjust_ratio_ = SPDM_enableFixSetMixPriceAdjustRatio();
  inner_explore_mix_price_adjust_ratio_lower_bound_ =
      SPDM_inner_explore_mix_price_adjust_ratio_lower_bound(session_data_->get_spdm_ctx());
  enable_mix_inner_explore_relative_hc_ =
      SPDM_enable_mix_inner_explore_relative_hc(session_data_->get_spdm_ctx());
  // live
  enale_inner_explore_live_score_ = SPDM_enale_inner_explore_live_score(session_data_->get_spdm_ctx());
  enale_inner_explore_live_soft_weight_ =
      SPDM_enale_inner_explore_live_soft_weight(session_data_->get_spdm_ctx());
  if (enale_inner_explore_live_score_) {
    mix_ad_fans_cpm_boost_weight_ = SPDM_fountain_mix_ad_fans_cpm_boost_weight(session_data_->get_spdm_ctx());
    mix_auction_top_fans_weight_ = SPDM_fountain_mix_auction_top_fans_weight(session_data_->get_spdm_ctx());
    mix_ad_dsp_eco_weight_ = SPDM_fountain_mix_ad_dsp_eco_weight(session_data_->get_spdm_ctx());
    mix_ad_top_fans_eco_weight_ = SPDM_fountain_mix_ad_top_fans_eco_weight(session_data_->get_spdm_ctx());
    mix_ad_live_score_alpha_ = SPDM_fountain_mix_ad_live_score_alpha(session_data_->get_spdm_ctx());
    mix_ad_live_score_bias_ = SPDM_fountain_mix_ad_live_score_bias(session_data_->get_spdm_ctx());
    mix_ad_live_score_pow_ = SPDM_fountain_mix_ad_live_score_pow(session_data_->get_spdm_ctx());
    enable_inner_explore_live_item_as_p2l_ =
        SPDM_enable_inner_explore_live_item_as_p2l(session_data_->get_spdm_ctx());
    auto explore_live_score_conf = FrontKconfUtil::exploreLiveScoreConf();
    if (explore_live_score_conf != nullptr) {
      const auto& live_score_param_conf = explore_live_score_conf->data().live_score_param_conf();
      const std::string& conf_key =
          SPDM_inner_explore_live_score_conf_key(session_data_->get_spdm_ctx());
      auto iter = live_score_param_conf.find(conf_key);
      if (iter != live_score_param_conf.end()) {
        explore_live_score_param_ = iter->second;
        const auto& user_tag_weight_map = explore_live_score_param_.user_tag_weight_map();
        auto iter = user_tag_weight_map.find(
            session_data_->get_ad_request()->ad_user_info().user_value_group_tag());
        if (iter != user_tag_weight_map.end()) {
          explore_live_score_param_.set_user_weight(iter->second);
        }
      }
    }
  }
  enable_inner_explore_fairness_rerank_ =
      SPDM_enable_inner_explore_fairness_rerank(session_data_->get_spdm_ctx()) ||
      SPDM_enable_inner_explore_ecpm_rerank(session_data_->get_spdm_ctx());
  enable_inner_explore_mix_ratio = SPDM_enable_inner_explore_mix_ratio(session_data_->get_spdm_ctx());
  inner_explore_mix_soft_ratio = SPDM_inner_explore_mix_soft_ratio(session_data_->get_spdm_ctx());
  inner_explore_mix_hard_ratio = SPDM_inner_explore_mix_hard_ratio(session_data_->get_spdm_ctx());
  enable_inner_explore_mix_ratio_opt_ =
          SPDM_enable_inner_explore_mix_ratio_opt(session_data_->get_spdm_ctx());
  inner_explore_mix_ratio_soft_live_ =
          SPDM_inner_explore_mix_ratio_soft_live(session_data_->get_spdm_ctx());
  inner_explore_mix_ratio_soft_photo_ =
          SPDM_inner_explore_mix_ratio_soft_photo(session_data_->get_spdm_ctx());
  inner_explore_mix_ratio_hard_live_ =
          SPDM_inner_explore_mix_ratio_hard_live(session_data_->get_spdm_ctx());
  inner_explore_mix_ratio_hard_photo_ =
          SPDM_inner_explore_mix_ratio_hard_photo(session_data_->get_spdm_ctx());
  mix_unify_cpm_ratio_v2 = SPDM_mix_unify_cpm_ratio_v2(session_data_->get_spdm_ctx());
  w5_extra_3_percent_bonus_weight_gamora_ =
    SPDM_w5_extra_3_percent_bonus_weight_gamora(session_data_->get_spdm_ctx());
  w5_extra_3_percent_bonus_weight_nebula_ =
    SPDM_w5_extra_3_percent_bonus_weight_nebula(session_data_->get_spdm_ctx());
  enable_bonus_balence_stategy_ = SPDM_enable_bonus_balence_stategy(session_data_->get_spdm_ctx());
  enable_bonus_sep_inner_outer_ = SPDM_enable_bonus_sep_inner_outer(session_data_->get_spdm_ctx());
  cpm_bonus_cali_ratio_outer_ = SPDM_cpm_bonus_cali_ratio_outer(session_data_->get_spdm_ctx());
  cpm_bonus_cali_ratio_inner_ = SPDM_cpm_bonus_cali_ratio_inner(session_data_->get_spdm_ctx());
  cpm_bonus_cali_extra_ratio_outer_ = SPDM_cpm_bonus_cali_extra_ratio_outer(session_data_->get_spdm_ctx());
  cpm_bonus_cali_extra_ratio_inner_ = SPDM_cpm_bonus_cali_extra_ratio_inner(session_data_->get_spdm_ctx());
  cpm_bonus_cali_ratio_ = SPDM_cpm_bonus_cali_ratio(session_data_->get_spdm_ctx());
  cpm_bonus_cali_extra_ratio_ = SPDM_cpm_bonus_cali_extra_ratio(session_data_->get_spdm_ctx());
  w5_extra_3_percent_bonus_cpm_lower_bound_ =
    SPDM_w5_extra_3_percent_bonus_cpm_lower_bound(session_data_->get_spdm_ctx()) * 1e6;
  w5_extra_3_percent_bonus_cpm_upper_bound_ =
    SPDM_w5_extra_3_percent_bonus_cpm_upper_bound(session_data_->get_spdm_ctx()) * 1e6;
  bonus_weight_for_rb_thr_to_add_bonus_ =
    SPDM_bonus_weight_for_rb_thr_to_add_bonus(session_data_->get_spdm_ctx());
  adjust_bonus_with_mix_rank_input_weight_ =
    SPDM_adjust_bonus_with_mix_rank_input_weight(session_data_->get_spdm_ctx());
  minus_bonus_with_mix_rank_input_weight_ =
    SPDM_minus_bonus_with_mix_rank_input_weight(session_data_->get_spdm_ctx());
  enable_adjust_bonus_with_mix_rank_input_use_two_switch_ =
    SPDM_enable_adjust_bonus_with_mix_rank_input_use_two_switch(session_data_->get_spdm_ctx());
  enable_move_mix_rank_input_hist_page_info_ =
    SPDM_enable_move_mix_rank_input_hist_page_info(session_data_->get_spdm_ctx());
  enable_close_unify_cpm_add_customer_hc_ =
    SPDM_enable_close_unify_cpm_add_customer_hc(session_data_->get_spdm_ctx());
  enable_close_unify_cpm_divide_sctr_ratio_ =
    SPDM_enable_close_unify_cpm_divide_sctr_ratio(session_data_->get_spdm_ctx());
  enable_fix_mix_unify_cpm_init_ =
    SPDM_enable_fix_mix_unify_cpm_init(session_data_->get_spdm_ctx());
  enable_mix_fctr_ = SPDM_enable_mix_fctr(session_data_->get_spdm_ctx());
  enable_inner_explore_mix_fctr_ = SPDM_enable_inner_explore_mix_fctr(session_data_->get_spdm_ctx());
  inner_explore_fctr_exp_tag_ = SPDM_inner_explore_fctr_exp_tag(session_data_->get_spdm_ctx());
  enable_unify_bonus_add_3_percent_bonus_groupBy_llsid_ = false;
  enable_set_negative_bonus_to_zero_ = SPDM_enable_set_negative_bonus_to_zero(session_data_->get_spdm_ctx());
  disable_fctr_live_bonus_ = SPDM_disable_fctr_live_bonus(session_data_->get_spdm_ctx());
  mix_fctr_model_exp_tag_ = SPDM_mix_fctr_model_exp_tag(session_data_->get_spdm_ctx());
  enable_fix_mix_bonus_ = SPDM_enable_fix_mix_bonus(session_data_->get_spdm_ctx());
  fix_mix_bonus_hc_ratio_ = SPDM_fix_mix_bonus_hc_ratio(session_data_->get_spdm_ctx());
  enable_bonus_strategy_for_mix_rb_cpm_ =
      SPDM_enable_bonus_strategy_for_mix_rb_cpm(session_data_->get_spdm_ctx());
  ori_bonus_ratio_for_mix_rb_cpm_ = SPDM_ori_bonus_ratio_for_mix_rb_cpm(session_data_->get_spdm_ctx());
  new_bonus_ratio_for_mix_rb_cpm_ = SPDM_new_bonus_ratio_for_mix_rb_cpm(session_data_->get_spdm_ctx());
  enable_client_ai_use_fctr_bonus_ = SPDM_enable_client_ai_use_fctr_bonus(session_data_->get_spdm_ctx());
  enable_bonus_fixed_ratio_experiment_v2_ =
      SPDM_enable_bonus_fixed_ratio_experiment_v2(session_data_->get_spdm_ctx());
  enable_mix_bonus_fixed_ratio_rct_experiment_ =
      SPDM_enable_mix_bonus_fixed_ratio_rct_experiment(session_data_->get_spdm_ctx());
  enable_inner_multi_queue_bonus_reallocation_ = SPDM_enable_inner_multi_queue_bonus_reallocation(
      session_data_->get_spdm_ctx());
  bonus_fixed_ratio_v2_ = SPDM_bonus_fixed_ratio_v2(session_data_->get_spdm_ctx());
  enable_fanstop_bonus_adjust_ = SPDM_enable_fanstop_bonus_adjust(session_data_->get_spdm_ctx());
  enable_fanstop_bonus_adjust_v2 = SPDM_enable_fanstop_bonus_adjust_v2(session_data_->get_spdm_ctx());
  inner_no_fanstop_bonus_adjust_ratio_ =
    SPDM_inner_no_fanstop_bonus_adjust_ratio(session_data_->get_spdm_ctx());
  fanstop_bonus_adjust_ratio_ = SPDM_fanstop_bonus_adjust_ratio(session_data_->get_spdm_ctx());
  disable_inner_multi_queue_flow_ =
    SPDM_disable_inner_multi_queue_flow(session_data_->get_spdm_ctx());
  enable_feed_explore_mix_price_adjust_ratio_ = SPDM_enableFeedExploreMixPriceAdjust();
  enable_feed_explore_mix_experience_value_ = SPDM_enableFeedExploreMixExperienceValue();
  is_feed_explore_req_ = ks::ad_base::IsFeedExploreRequest(session_data_->get_sub_page_id());
  enable_inner_explore_mix_experience_value_ =
      SPDM_enable_inner_explore_mix_experience_value(session_data_->get_spdm_ctx());
  explore_mix_experience_exp_tag_ = SPDM_explore_mix_experience_exp_tag(session_data_->get_spdm_ctx());
  explore_experience_conf_ = FrontKconfUtil::exploreExperienceConf();
  enable_explore_post_gpm_ = SPDM_enable_explore_post_gpm(session_data_->get_spdm_ctx());
  enable_feed_mix_score_new_ = SPDM_enable_feed_mix_score_new(session_data_->get_spdm_ctx());
  enable_bonus_reallocation_exp_ = SPDM_enable_bonus_reallocation_exp(session_data_->get_spdm_ctx());
  auto feed_mix_conf = FrontKconfUtil::feedMixScoreConf();
  if (feed_mix_conf != nullptr) {
    const auto& feed_param_conf = feed_mix_conf->data().feed_mix_score_param_conf();
    const std::string& conf_key = SPDM_feed_mix_score_conf_key(session_data_->get_spdm_ctx());
    auto iter = feed_param_conf.find(conf_key);
    if (iter != feed_param_conf.end()) {
      feed_mix_score_param_ = iter->second;
      const auto& user_tag_weight_map = feed_mix_score_param_.user_tag_weight_map();
      auto iter = user_tag_weight_map.find(
            session_data_->get_ad_request()->ad_user_info().user_value_group_tag());
      if (iter != user_tag_weight_map.end()) {
        feed_mix_score_user_tag_weight_ = iter->second;
      }
    }
  }
  // 混排限制最大 cpm
  enable_mix_rank_truncate =
     SPDM_enable_mix_rank_truncate(session_data_->get_spdm_ctx());
  mix_rank_truncate_max = SPDM_mix_rank_truncate_max(session_data_->get_spdm_ctx());
  enable_unify_bonus_exp_v2_ = SPDM_enable_unify_bonus_exp_v2(session_data_->get_spdm_ctx());
  unify_bonus_exp_tag_ = SPDM_unify_bonus_exp_tag(session_data_->get_spdm_ctx());
  enable_bonus_allocation_cpm_user_exp_ =
      SPDM_enable_bonus_allocation_cpm_user_exp(session_data_->get_spdm_ctx());
  bonus_allocation_cpm_user_exp_tag_ = SPDM_bonus_allocation_cpm_user_exp_tag(session_data_->get_spdm_ctx());
  unify_bonus_exp_conf_.clear();
  alloc_by_user_cpm_bonus_exp_confs_.clear();
  if (enable_bonus_allocation_cpm_user_exp_) {
    auto alloc_by_user_cpm_bonus_exp_kconf = FrontKconfUtil::adAllocByUserCpmBonusExpConf();
    if (alloc_by_user_cpm_bonus_exp_kconf != nullptr) {
      auto exp_conf_list =
        alloc_by_user_cpm_bonus_exp_kconf->data().exp_conf_map_.find(bonus_allocation_cpm_user_exp_tag_);
      if (exp_conf_list != alloc_by_user_cpm_bonus_exp_kconf->data().exp_conf_map_.end()) {
        alloc_by_user_cpm_bonus_exp_confs_ = exp_conf_list->second;
      }
      for (const auto& conf : alloc_by_user_cpm_bonus_exp_confs_) {
        for (auto iter = conf.user_type_bonus_ratio_map.begin(); \
          iter != conf.user_type_bonus_ratio_map.end(); iter++) {
        }
      }
    }
  } else if (enable_unify_bonus_exp_v2_) {
    auto unify_bonus_exp_kconf = FrontKconfUtil::adUnifyBonusRatioExpConfNew();
    if (unify_bonus_exp_kconf != nullptr) {
      auto exp_conf_list = unify_bonus_exp_kconf->data().exp_conf_map_.find(unify_bonus_exp_tag_);
      if (exp_conf_list != unify_bonus_exp_kconf->data().exp_conf_map_.end()) {
        unify_bonus_exp_conf_ = exp_conf_list->second;
      }
    }
  }

  bonus_reallocation_exp_tag_ = SPDM_bonus_reallocation_exp_tag(session_data_->get_spdm_ctx());
  bonus_reallocation_exp_conf_.clear();
  if (enable_bonus_reallocation_exp_) {
    auto bonus_reallocation_conf = FrontKconfUtil::adBonusReallocationExpConf();
    if (bonus_reallocation_conf != nullptr) {
      auto exp_conf_list = bonus_reallocation_conf->data().exp_conf_map_.find(bonus_reallocation_exp_tag_);
      if (exp_conf_list != bonus_reallocation_conf->data().exp_conf_map_.end()) {
        bonus_reallocation_exp_conf_ = exp_conf_list->second;
      }
    }
  }
  enable_skip_fctr_for_exp_ = SPDM_enable_skip_fctr_for_exp(session_data_->get_spdm_ctx());
  enable_excycle_skip_pass_tag_ = SPDM_enable_excycle_skip_pass_tag(session_data_->get_spdm_ctx());
  enable_excycle_skip_pass_tag_v2_ = SPDM_enable_excycle_skip_pass_tag_v2(session_data_->get_spdm_ctx());

  // [zhangyuxiang05] 异常 unify_bonus 修复相关
  enable_calib_abnormal_unify_bonus_by_thr_ =
    SPDM_enable_calib_abnormal_unify_bonus_by_thr(session_data_->get_spdm_ctx());
  enable_calib_abnormal_unify_bonus_by_cpm_ =
    SPDM_enable_calib_abnormal_unify_bonus_by_cpm(session_data_->get_spdm_ctx());
  calibration_weight_for_abnormal_unify_bonus_gamora1_ =
    SPDM_calibration_weight_for_abnormal_unify_bonus_gamora1(session_data_->get_spdm_ctx());
  calibration_weight_for_abnormal_unify_bonus_nebula1_ =
    SPDM_calibration_weight_for_abnormal_unify_bonus_nebula1(session_data_->get_spdm_ctx());
  calibration_weight_for_abnormal_unify_bonus_gamora2_ =
    SPDM_calibration_weight_for_abnormal_unify_bonus_gamora2(session_data_->get_spdm_ctx());
  calibration_weight_for_abnormal_unify_bonus_nebula2_ =
    SPDM_calibration_weight_for_abnormal_unify_bonus_nebula2(session_data_->get_spdm_ctx());
  ad_pv_bonus_frac_thr_gamora_ = SPDM_ad_pv_bonus_frac_thr_gamora(session_data_->get_spdm_ctx());
  ad_pv_bonus_frac_thr_nebula_ = SPDM_ad_pv_bonus_frac_thr_nebula(session_data_->get_spdm_ctx());
  cpm_times_thr_gamora_ = SPDM_cpm_times_thr_gamora(session_data_->get_spdm_ctx());
  cpm_times_thr_nebula_ = SPDM_cpm_times_thr_nebula(session_data_->get_spdm_ctx());
  enable_calib_abnormal_unify_bonus_by_max_cpm_and_thr_ =
    SPDM_enable_calib_abnormal_unify_bonus_by_max_cpm_and_thr(session_data_->get_spdm_ctx());
  calibration_weight_for_abnormal_unify_bonus_gamora3_ =
    SPDM_calibration_weight_for_abnormal_unify_bonus_gamora3(session_data_->get_spdm_ctx());
  calibration_weight_for_abnormal_unify_bonus_nebula3_ =
    SPDM_calibration_weight_for_abnormal_unify_bonus_nebula3(session_data_->get_spdm_ctx());
  // [zhangyuxiang05] bonus 留反实验实验流量标记
  enable_ad_unify_bonus_weight_exp_ =
    SPDM_enable_ad_unify_bonus_weight_exp(session_data_->get_spdm_ctx());
  calibration_weight_for_abnormal_unify_bonus_gamora4_ =
    SPDM_calibration_weight_for_abnormal_unify_bonus_gamora4(session_data_->get_spdm_ctx());
  calibration_weight_for_abnormal_unify_bonus_nebula4_ =
    SPDM_calibration_weight_for_abnormal_unify_bonus_nebula4(session_data_->get_spdm_ctx());
  ad_unify_bonus_weight_gamora_ =
    SPDM_ad_unify_bonus_weight_gamora(session_data_->get_spdm_ctx());
  ad_unify_bonus_weight_nebula_ =
    SPDM_ad_unify_bonus_weight_nebula(session_data_->get_spdm_ctx());


  return true;
}

void FillOtherMixInfoMixer::FillPvInfo(RankAdCommon* p_ad) {
  if (session_data_->get_ad_request() == nullptr) {
    return;
  }
  p_ad->ad_mix_info.page_id = session_data_->get_page_id();
  p_ad->ad_mix_info.sub_page_id = session_data_->get_sub_page_id();
  if (session_data_->get_ad_request()->ad_user_info().has_hist_gmv()) {
    p_ad->ad_mix_info.history_gmv = session_data_->get_ad_request()->ad_user_info().hist_gmv();
  }
  auto& ueq_user_info = session_data_->GetUeqManager().GetUeqUserInfo();
  p_ad->ad_mix_info.nebula_ad_sensitive_score_dau = ueq_user_info.adload_ptr;
  p_ad->ad_mix_info.nebula_ad_sensitive_score_time = ueq_user_info.adload_time;
  p_ad->ad_mix_info.kuaishou_ad_sensitive_score_dau = ueq_user_info.kwai_adload_ptr;
  p_ad->ad_mix_info.kuaishou_ad_sensitive_score_time = ueq_user_info.kwai_adload_time;
  p_ad->ad_mix_info.is_low_ad_filled_user =
      session_data_->get_ad_request()->ad_user_info().is_low_ad_filled_user();
  p_ad->ad_mix_info.w_user_level = session_data_->get_w_user_level();
}

kuaishou::ad::AdEnum_AdTpAudienceType FillOtherMixInfoMixer::GetPaidAudienctType(
    const StyleInfoItem& style_info) {
  // 判断该广告人群包与用户人群包是否有匹配, 匹配返回对应的付费人群包类型, 不匹配返回 UNKNOWN
  bool paid_audience_used = false;
  const auto& paid_audience = style_info.target().extend_fields().paid_audience();
  auto paid_audience_type = style_info.target().paid_audience_type();
  for (int i = 0; i < paid_audience.size(); ++i) {
    if (session_data_->get_user_paid_audience_set().count(paid_audience.Get(i)) > 0) {
      paid_audience_used = true;
      break;
    }
  }
  if (!paid_audience_used || paid_audience_type == kuaishou::ad::AdEnum::UNKNOWN_TP_AUDIENCE_TYPE) {
    return kuaishou::ad::AdEnum::UNKNOWN_TP_AUDIENCE_TYPE;
  } else {
    return paid_audience_type;
  }
}

void FillOtherMixInfoMixer::FillPaidAudienctType(RankAdCommon* p_ad, const StyleInfoItem* p_style_info) {
  if (p_style_info == nullptr) {
    return;
  }
  p_ad->ad_mix_info.paid_audience_type =
      static_cast<int64_t>(GetPaidAudienctType(*p_style_info));
}

void FillOtherMixInfoMixer::FillMixSourceType(RankAdCommon* p_ad, const StyleInfoItem* p_style_info) {
  auto* item = p_ad->GetResult();
  if (item == nullptr) {
    return;
  }
  if (p_style_info && p_style_info->unit_fanstop_support_info().fans_top_liked_biz_type() ==
      kuaishou::ad::AdEnum::B_FANS_TOP) {
    p_ad->ad_mix_info.ad_mixed_source_type = 2;  // AD_FANS_TOP
    p_ad->ad_mix_info.fans_top_type = 1;  // NORMAL_FANSTOP
  } else if (item->ad_source_type() == ::kuaishou::ad::FANS_TOP_V2) {
    p_ad->ad_mix_info.ad_mixed_source_type = 2;  // AD_FANS_TOP
    // 设置内部粉条特性
    if (p_ad->is_new_fanstop_inner_operation()) {
      p_ad->ad_mix_info.fans_top_type = 2;  // INNER_FANSTOP
    } else {
      p_ad->ad_mix_info.fans_top_type = 1;  // NORMAL_FANSTOP
    }
  } else {
    // 判断是否是软广，如果是软广需要做修改
    if (item->ad_deliver_info().soft_ad_queue()) {
      p_ad->ad_mix_info.ad_mixed_source_type = 3;  // AD_SOFT_TYPE
      session_data_->dot_perf->Count(1, "mix_source_type", "soft_type");
    } else {
      p_ad->ad_mix_info.ad_mixed_source_type = 1;  // AD_DSP
      session_data_->dot_perf->Count(1, "mix_source_type", "ad_dsp");
    }
  }
}

void FillOtherMixInfoMixer::FillBaseInfo(RankAdCommon* p_ad, const StyleInfoItem* p_style_info) {
  auto* item = p_ad->GetResult();
  if (item == nullptr) {
    return;
  }
  auto* p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result == nullptr) {
    return;
  }
  auto &rank_result = *p_rank_result;
  if (item->ad_source_type() == ::kuaishou::ad::AdSourceType::BRAND) {
    p_ad->ad_mix_info.ad_priority = 1;
    if (session_data_->get_is_brand_preview_user()) {
      p_ad->ad_mix_info.is_preview = true;
    }
  } else {
    p_ad->ad_mix_info.ad_priority = static_cast<int64_t>(item->ad_priority());
  }
  p_ad->ad_mix_info.uv_data_scores_str = "";
  p_ad->ad_mix_info.uv_data_user_daily_cost = 0.0;
  p_ad->ad_mix_info.uv_data_random_flow_factor = 0.0;
  FillMixSourceType(p_ad, p_style_info);
  FillPaidAudienctType(p_ad, p_style_info);
  p_ad->ad_mix_info.item_type = item->ad_deliver_info().ad_base_info().item_type();
  p_ad->ad_mix_info.bid_type = item->ad_deliver_info().ad_base_info().bid_type();
  if (p_style_info && (item->ad_source_type() == kuaishou::ad::FANS_TOP_V2
        || p_style_info->unit_fanstop_support_info().fans_top_liked_biz_type() ==
        kuaishou::ad::AdEnum::B_FANS_TOP)) {
    p_ad->ad_mix_info.fanstop_marketing_info_biz_type = p_style_info->photo_status().plc_biz_type();
  }
  if (item->ad_source_type() == ::kuaishou::ad::AdSourceType::ADX &&
      IsAdxWhiteUser(session_data_->get_ad_request()->ad_user_info().id())) {
    p_ad->ad_mix_info.ad_priority =
        static_cast<int64_t>(kuaishou::ad::AdEnum_AdPriority_GD_KEEP_ORDER_PRIORITY);
  }
  p_ad->ad_mix_info.ad_trace_dsp_type = static_cast<int32>(GetAdDspType(*item));

  if (session_data_->get_enable_good_native_photo_new_standard()) {
    int32_t native_quality_status = p_rank_result->rank_base_info().native_quality_status();
    if (native_quality_status == 2) {
      bool cpm_limit = !enable_nogap_filter_by_cpm_ ||
        item->ad_deliver_info().cpm() >= nogap_cpm_thr_;
      bool wgroup_limit = !enable_nogap_filter_by_wgroup_ ||
        (session_data_->get_w_user_level() == 5 ||
         session_data_->get_w_user_level() == 4 ||
          session_data_->get_w_user_level() == 3);
      bool jump_out_limit = !enable_nogap_filter_by_jumpout_rate_ ||
      (SPDM_enable_nogap_filter_by_jumpout_rate_bugfix(session_data_->get_spdm_ctx())
      && item->ad_deliver_info().ad_base_info().ocpc_action_type() != 180) ||
        (item->ad_deliver_info().ad_base_info().ocpc_action_type() == 180 &&
           rank_result.rank_rank_info().unify_cvr() < nogap_jumpout_rate_thr_);
      if (cpm_limit && wgroup_limit && jump_out_limit) {
        p_ad->ad_mix_info.is_native_quality_new_standard = true;
        session_data_->dot_perf->Count(1, "native_quality_new_standard");
      }
    }
  }


  if (SPDM_enable_mix_ad_inner_outer_flag(session_data_->get_spdm_ctx())) {
    p_ad->ad_mix_info.ad_inner_outer_flag = 0;

    // 短剧属于内循环实验
    if (item->ad_deliver_info().ad_base_info().industry_id_v3() == 2012
      || item->ad_deliver_info().second_industry_id_v5() == 2012) {
      p_ad->ad_mix_info.ad_inner_outer_flag = 1;
    }
  }

  // 外循环部分行业做实验走内循环链路实验 (获取 uescore)
  if (SPDM_enable_outerloop_industry_ad_inner_outer_flag(session_data_->get_spdm_ctx())) {
    auto campaign_type = item->ad_deliver_info().ad_base_info().campaign_type();
    auto ocpx = item->ad_deliver_info().ad_base_info().ocpc_action_type();
    bool is_fiction =
      campaign_type == kuaishou::ad::AdEnum::AD_KWAI_FICTION_PROMOTION;
    bool is_mini_game =
      (p_ad->is_game_ad() && campaign_type == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE);
    if ((is_fiction || is_mini_game) && ocpx == kuaishou::ad::AdActionType::AD_ROAS) {
      p_ad->ad_mix_info.ad_inner_outer_flag = 1;
    }
  }

  // 付费短剧聚合大卡标记，先在 ad_mix_info 保存，后面流程写入到混排字段
  bool pass_playlet_aggr_card_freq_control = session_data_->Attr(
    CommonIdx::pass_playlet_aggr_card_freq_control).GetIntValue().value_or(0) > 0 ? true : false;
  if (pass_playlet_aggr_card_freq_control) {
    auto campaign_type = item->ad_deliver_info().ad_base_info().campaign_type();
    if (campaign_type == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
      p_ad->ad_mix_info.is_serial_aggr_card = true;
    }
  }

  // 付费聚合大卡标记
  if (SPDM_enable_common_feed_card(session_data_->get_spdm_ctx())) {
    bool is_black_campaign = false;
    bool is_black_account = false;
    auto account_id = item->ad_deliver_info().ad_base_info().account_id();
    auto campaign_id = item->ad_deliver_info().ad_base_info().campaign_id();
    auto product_name = item->ad_deliver_info().ad_base_info().product_name();
    auto campaign_type = item->ad_deliver_info().ad_base_info().campaign_type();
    auto sub_page_id = session_data_->get_sub_page_id();
    auto is_valid_sub_page = false;
    if (sub_page_id == ******** || sub_page_id == ********) {
      is_valid_sub_page = true;
    }
    if (FrontKconfUtil::commonCardCampaignBlackList() != nullptr) {
      if (FrontKconfUtil::commonCardCampaignBlackList()->count(campaign_id)) {
        is_black_campaign = true;
      }
    }
    if (FrontKconfUtil::commonCardAccountBlackList() != nullptr) {
      if (FrontKconfUtil::commonCardAccountBlackList()->count(account_id)) {
        is_black_account = true;
      }
    }
    auto account_type = item->ad_deliver_info().ad_base_info().account_type();
    bool valid_account_type = false;
    if (account_type == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE ||
        account_type == kuaishou::ad::AdEnum::ACCOUNT_CPC ||
        account_type == kuaishou::ad::AdEnum::ACCOUNT_ESP) {
      valid_account_type = true;
    }
    auto app_version = session_data_->get_app_version();
    auto min_valid_version = SPDM_common_card_min_valid_version(session_data_->get_spdm_ctx());
    bool is_new_version = false;
    if (engine_base::CompareAppVersion(app_version, min_valid_version) >= 0) {
      is_new_version = true;
      session_data_->dot_perf->Count(1, "common_card_info", "mix_info.is_new_version");
    }
    if (!is_black_campaign && !is_black_account && valid_account_type && is_new_version &&
    is_valid_sub_page) {
      bool pass_playlet_common_card_freq_control = session_data_->Attr(
        CommonIdx::pass_playlet_common_card_freq_control).GetIntValue().value_or(0) > 0 ? true : false;
      int64 freq_control = session_data_->Attr(
        CommonIdx::pass_common_card_ind_freq_control).GetIntValue().value_or(0);
      bool is_qpon = p_rank_result->ad_rank_trans_info().qpon_info().has_qpon();
      session_data_->dot_perf->Count(1, "common_card_info", "before_qpon_filter_count");
      bool valid_item_type = false;
      if (item->ad_deliver_info().ad_base_info().item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO) {
        valid_item_type = true;
      }
      auto industry_id = p_ad->industry_parent_id_v3();
      if (p_style_info && industry_id == 0 && p_style_info->has_industry_v3()) {
        industry_id = p_style_info->industry_v3().parent_id();
      }
      bool is_merchant_item = false;
      if (p_style_info) {
        auto item_type = merchant_util::GetSmallShopItemType(*p_style_info);
        if (item_type == kuaishou::ad::AdEnum_MerchantItemPutType_MERCHANT_PUT_PRODUCT) {
          is_merchant_item = true;
        }
      }
      if (!is_qpon || (SPDM_enable_kuai_game_skip_qpon(session_data_->get_spdm_ctx()) && campaign_type ==
              kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE && industry_id == 1018)) {
        session_data_->dot_perf->Count(1, "common_card_info", "no_qpon_count");
        bool valid_inner = (freq_control & CommonCardFreqCType::VALID_INNER_LOOP) > 0 ? false : true;
        bool valid_serial = (freq_control & CommonCardFreqCType::VALID_SEREAL) > 0 ? false : true;
        bool valid_quick_kwai_game = (freq_control & CommonCardFreqCType::VALID_QUIK_KWAI_GAME) >
        0 ? false : true;
        bool valid_feed_card_kai_game = (freq_control & CommonCardFreqCType::VALID_PLAYABLE_GAME) >
        0 ? false : true;
        auto ocpx_action_type = item->ad_deliver_info().ad_base_info().ocpc_action_type();
        auto sec_industry_id = item->ad_deliver_info().ad_base_info().industry_id_v3();
        bool valid_industry = true;
        if (FrontKconfUtil::commonCardIndustryV2() != nullptr) {
          if (FrontKconfUtil::commonCardIndustryV2()->count(sec_industry_id)) {
            valid_industry = false;
          }
        }
        bool valid_account = false;
        if (FrontKconfUtil :: commonCardGameList() != nullptr) {
          valid_account = FrontKconfUtil::commonCardGameList()->count(account_id);
        }
        auto card_style = SPDM_common_card_style_ab(session_data_->get_spdm_ctx());
        auto enable_inner_loop = SPDM_enable_inner_common_feed_card(session_data_->get_spdm_ctx());
        auto enable_kwai_game = SPDM_enable_kwai_game_common_feed_card(session_data_->get_spdm_ctx());
        auto enable_short_serial =
        SPDM_enable_short_serial_common_feed_card(session_data_->get_spdm_ctx());
        auto user_id = session_data_->get_user_id();
        if (FrontKconfUtil::commonCardUserWhiteList() != nullptr) {
          if (FrontKconfUtil::commonCardUserWhiteList()->count(user_id)) {
            session_data_->dot_perf->Count(1, "common_card_info", "mix_info_hit_user_white_list_count");
            pass_playlet_common_card_freq_control = true;
            valid_inner = true;
            valid_quick_kwai_game = true;
            valid_feed_card_kai_game = true;
            valid_serial = true;
          }
        }
        // 快小游准入条件, 年龄约束 + 游戏白名单准入 + 机型判断 + 客户端版本号
        bool kuai_game_admit = true;
        bool enable_kuai_game_feed_card = SPDM_enable_kuai_game_feed_card(session_data_->get_spdm_ctx());
        bool enable_kuai_game_age_constraint =
                                    SPDM_enable_kuai_game_age_constraint(session_data_->get_spdm_ctx());
        bool enable_kuai_game_platform_version_constraint =
                        SPDM_enable_kuai_game_platform_version_constraint(session_data_->get_spdm_ctx());
        bool enable_kuai_game_whitelist_constraint =
                              SPDM_enable_kuai_game_whitelist_constraint(session_data_->get_spdm_ctx());
        bool enable_kuai_game_device_version_constraint =
                          SPDM_enable_kuai_game_device_version_constraint(session_data_->get_spdm_ctx());
        bool enable_kuai_game_isunlogin_constraint =
                              SPDM_enable_kuai_game_isunlogin_constraint(session_data_->get_spdm_ctx());
        if (enable_kuai_game_feed_card && pass_playlet_common_card_freq_control && industry_id == 1018
          && valid_feed_card_kai_game && campaign_type == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) {
          auto kuai_game_feedplay_conf = FrontKconfUtil::kuaiGameFeedPlayConf()->data();
          auto& admit_device_mod_set = kuai_game_feedplay_conf.device_mod_set;
          auto& admit_product_name_set = kuai_game_feedplay_conf.product_name_set;
          auto& admit_account_id_set = kuai_game_feedplay_conf.account_id_set;
          auto& admit_age_bound_list = kuai_game_feedplay_conf.age_bound_list;
          auto& admit_app_version_tag = kuai_game_feedplay_conf.app_version_tag;
          std::string platform_version = session_data_->get_ad_request()->ad_user_info().platform_version();
          int64_t user_age = session_data_->get_ad_request()->ad_user_info().age();
          bool is_unlogin_user = session_data_->get_ad_request()->ad_user_info().is_unlogin_user();
          // 年龄
          if (enable_kuai_game_age_constraint && kuai_game_admit) {
            if (admit_age_bound_list.size() == 2 && admit_age_bound_list[0] < admit_age_bound_list[1]
                  && user_age >= admit_age_bound_list[0] && user_age <= admit_age_bound_list[1]) {
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_age_admit_mix", absl::StrCat(user_age));
            } else {
              kuai_game_admit = false;
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_age_filter_mix", absl::StrCat(user_age));
            }
          }
          // 客户端版本号
          if (enable_kuai_game_platform_version_constraint && kuai_game_admit) {
            if (ad_base::AppVersionCompare::Compare(platform_version, admit_app_version_tag) > 0) {
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_platform_version_mix", platform_version);
            } else {
              kuai_game_admit = false;
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_platform_filter_mix", platform_version);
            }
          }
          // 账户产品白名单
          if (enable_kuai_game_whitelist_constraint && kuai_game_admit) {
            if (admit_product_name_set.find(product_name) != admit_product_name_set.end() ||
                        admit_account_id_set.find(account_id) != admit_account_id_set.end()) {
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_whitelist_admit_mix", product_name);
            } else {
              kuai_game_admit = false;
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_whitelist_filter_mix", product_name);
            }
          }
          // 机型准入
          bool final_admit_device_mod = true;
          for (const auto& device_info : session_data_->get_ad_request()->ad_user_info().device_info()) {
            std::string device_mod = device_info.device_mod();
            if (admit_device_mod_set.find(device_mod) != admit_device_mod_set.end()) {
              final_admit_device_mod = false;
              break;
            }
          }
          if (enable_kuai_game_device_version_constraint && kuai_game_admit) {
            if (final_admit_device_mod) {
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_device_mod_admit_mix");
            } else {
              kuai_game_admit = false;
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_device_mod_filter_mix");
            }
          }
          // 未登陆用户过滤
          if (enable_kuai_game_isunlogin_constraint && kuai_game_admit) {
            if (!is_unlogin_user) {
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_login_user_admit_mix");
            } else {
              kuai_game_admit = false;
              session_data_->dot_perf->Count(1, "kuai_game_feed_card_login_user_filter_mix");
            }
          }
        }
        if (pass_playlet_common_card_freq_control) {
          if ((valid_inner && valid_item_type && valid_industry && enable_inner_loop && is_merchant_item &&
              (
                (SPDM_enableCIDCardRemoveSwitch() &&
                  (ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
                    ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED))
                || (!SPDM_enableCIDCardRemoveSwitch() &&
                  (ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
                    ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
                    ocpx_action_type == kuaishou::ad::CID_ROAS ||
                    ocpx_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID))) &&
              IsInnerLoopAd(item->ad_deliver_info().ad_base_info().campaign_type()))) {
              session_data_->dot_perf->Count(1, "common_card_info", "mix_info_big_card_count");
              p_ad->ad_mix_info.is_big_card = true;
              p_ad->ad_mix_info.feed_card_industry = 1;
              p_ad->ad_mix_info.feed_card_style = card_style;
          } else if (valid_feed_card_kai_game
              && valid_item_type && enable_kuai_game_feed_card && kuai_game_admit
              && campaign_type == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE && industry_id == 1018) {
                session_data_->dot_perf->Count(1, "common_card_info.mix_info_quick_kwai_game_cnt_feed_card");
                p_ad->ad_mix_info.is_big_card = true;
                p_ad->ad_mix_info.feed_card_industry = 4;
                p_ad->ad_mix_info.feed_card_style = kuaishou::ad::AdEnum_CommonCardStyle_PLAYABLE_GAME_TYPE;
          } else if (valid_quick_kwai_game && valid_item_type && enable_kwai_game &&
              campaign_type ==
              kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE &&  // NO_LINT
              industry_id == 1018 && valid_account) {
                session_data_->dot_perf->Count(1, "common_card_info.mix_info_quick_kwai_game_cnt");
                p_ad->ad_mix_info.is_big_card = true;
                p_ad->ad_mix_info.feed_card_industry = 4;
                p_ad->ad_mix_info.feed_card_style = kuaishou::ad::AdEnum_CommonCardStyle_QUIK_KWAI_GAME_TYPE;
          } else if (valid_serial && campaign_type == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION &&
            enable_short_serial) {
                session_data_->dot_perf->Count(1, "common_card_info.mix_info_serial_cnt");
                p_ad->ad_mix_info.is_big_card = true;
                p_ad->ad_mix_info.feed_card_industry = 2;
                p_ad->ad_mix_info.feed_card_style = kuaishou::ad::AdEnum_CommonCardStyle_SHORT_SERIAL_TYPE;
          }
        }
      }
    }
  }

  // 透传外跳预算标记字段到 ad_mix_info
  if (p_style_info != nullptr && (enable_excycle_skip_pass_tag_ || enable_excycle_skip_pass_tag_v2_)) {
    const auto& unit = p_style_info->unit();
    if (unit.has_unit_algo_info() && unit.unit_algo_info().has_excycle_skip()) {
      int32_t excycle_skip_value = unit.unit_algo_info().excycle_skip();
      p_ad->ad_mix_info.excycle_skip = excycle_skip_value;
    }
  }
}

void FillOtherMixInfoMixer::FillUnifyBonusInfo(RankAdCommon* p_ad) {
  auto* item = p_ad->GetResult();
  if (item == nullptr) {
    return;
  }
  auto* p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result == nullptr) {
    return;
  }
  if (!(session_data_->IsThanosMixTraffic())) {
    return;
  }
  kuaishou::ad::AdEnum::AdMonitorType ad_monitor_type = GetAdMonitorType(*item);
  std::string item_tag = absl::Substitute(
      "$0_$1_$2_$3", ad_monitor_type,
      kuaishou::ad::AdEnum::AdQueueType_Name(item->ad_deliver_info().ad_queue_type()),
      kuaishou::ad::AdEnum::ItemType_Name(item->ad_deliver_info().ad_base_info().item_type()),
      kuaishou::ad::AdEnum::CampaignType_Name(item->ad_deliver_info().ad_base_info().campaign_type()));
  int industry_hc_tag = p_rank_result->ad_rank_trans_info().industry_hc_tag();
  double industry_hc_score = p_rank_result->ad_rank_trans_info().industry_hc_score();
  if (ad_monitor_type == kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT) {
    p_ad->ad_mix_info.mix_unify_bid_unify_bonus = p_ad->ad_mix_info.cpm_bonus + p_ad->ad_mix_info.hc_score;
  } else if (ad_monitor_type == kuaishou::ad::AdEnum_AdMonitorType_INNER_LOOP_DEFAULT) {
    if (enable_mtb_v2_hc_gpm_using_max_) {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
          p_ad->ad_mix_info.cpm_bonus + p_ad->ad_mix_info.hc_score - industry_hc_tag == 2 ? industry_hc_score
                                                                                          : 0.0;
    } else if (enable_mtb_v2_inner_bonus_include_hc_gpm_) {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus = p_ad->ad_mix_info.cpm_bonus + p_ad->ad_mix_info.hc_score;
    } else {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
          p_ad->ad_mix_info.cpm_bonus + p_ad->ad_mix_info.hc_score - p_ad->ad_mix_info.hc_gpm;
    }
  }

  if (enable_fix_mix_bonus_) {
    p_ad->ad_mix_info.mix_unify_bid_unify_bonus = p_ad->ad_mix_info.mix_unify_bid_unify_bonus
        - p_ad->ad_mix_info.cpm_bonus;
    p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= fix_mix_bonus_hc_ratio_;
  }

  // mix_bonus = mix_bonus - customer_hc
  p_ad->ad_mix_info.mix_unify_bid_unify_bonus -= p_rank_result->ad_rank_trans_info().customer_hc_score();
  session_data_->dot_perf->Interval(p_rank_result->ad_rank_trans_info().customer_hc_score(),
      "adjust_mix_bonus_by_customer_hc");

  if (enable_mtb_v2_bonus_ratio_) {
    p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= mtb_v2_bonus_ratio_;
  }
  if (enable_mix_bonus_adjust_) {
    p_ad->ad_mix_info.mix_unify_bid_unify_bonus += p_ad->ad_mix_info.mix_unify_bid_unify_cpm
        * (p_rank_result->ad_rank_trans_info().adload_control_rate() - 1.0 + mix_bonus_adjust_enhance_ratio_);
  }

  p_ad->ad_mix_info.mix_unify_bid_unify_cpm *= mix_unify_cpm_ratio_;
  p_ad->ad_mix_info.mix_unify_bid_unify_cpm *= mix_unify_cpm_ratio_v2;
  p_ad->ad_mix_info.mix_unify_bid_unify_gpm *= mix_unify_gpm_ratio_;
  p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= mix_unify_bonus_ratio_;

  mix_unify_score_ratio_ *= (mix_unify_score_ratio_a_ * mix_unify_score_ratio_b_);
  p_ad->ad_mix_info.mix_unify_bid_unify_cpm *= mix_unify_score_ratio_;
  p_ad->ad_mix_info.mix_unify_bid_unify_gpm *= mix_unify_score_ratio_;
  p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= mix_unify_score_ratio_;

  if (ad_monitor_type == kuaishou::ad::AdEnum_AdMonitorType_INNER_LOOP_DEFAULT) {
    p_ad->ad_mix_info.mix_unify_bid_unify_cpm *= mix_unify_cpm_ratio_inner_;
  } else if (ad_monitor_type == kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT) {
    p_ad->ad_mix_info.mix_unify_bid_unify_cpm *= mix_unify_cpm_ratio_outer_;
  }

  // bonus 随 cpm 校准的调整
  if (enable_bonus_balence_stategy_) {
    if (enable_bonus_sep_inner_outer_) {
      if (ad_monitor_type == kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= cpm_bonus_cali_ratio_outer_;
        // 最终的商业化对混排 bonus 报价:  w * bonus + w' * w * (cpm+bonus)
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus += cpm_bonus_cali_extra_ratio_outer_
          * (cpm_bonus_cali_ratio_outer_ * p_ad->ad_mix_info.mix_unify_bid_unify_cpm
        + p_ad->ad_mix_info.mix_unify_bid_unify_bonus);
      } else if (ad_monitor_type == kuaishou::ad::AdEnum_AdMonitorType_INNER_LOOP_DEFAULT) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= cpm_bonus_cali_ratio_inner_;
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus += cpm_bonus_cali_extra_ratio_inner_
          * (cpm_bonus_cali_ratio_inner_ * p_ad->ad_mix_info.mix_unify_bid_unify_cpm
        + p_ad->ad_mix_info.mix_unify_bid_unify_bonus);
      }
    } else {
      session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
                                      "unify_bonus_before_cali", item_tag);
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= cpm_bonus_cali_ratio_;
      session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
                                      "unify_bonus_intermediate stage", item_tag);
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus += cpm_bonus_cali_extra_ratio_
        * (cpm_bonus_cali_ratio_ * p_ad->ad_mix_info.mix_unify_bid_unify_cpm
      + p_ad->ad_mix_info.mix_unify_bid_unify_bonus);
    }
  }

  // 根据上一刷报价调整 bonus
  if (enable_change_bonus_with_last_llsid_mix_score_) {
    auto rank_benifit = p_rank_result->rank_price_info().rank_benifit();
    if (rank_benifit == 0) {
      return;
    }
    if (session_data_->get_ad_with_other_score_diff() < 0) {  // 广告混排分高，需要降低 bonus
      if (!enable_adjust_bonus_with_mix_rank_input_use_two_switch_) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus +=
            adjust_bonus_with_mix_rank_input_weight_ *
                rank_benifit * session_data_->get_ad_with_other_score_diff() + 1;
      } else {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus +=
            minus_bonus_with_mix_rank_input_weight_ *
                rank_benifit * session_data_->get_ad_with_other_score_diff() + 1;
      }
      if (p_ad->ad_mix_info.mix_unify_bid_unify_bonus <= 0) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus = 0;
        session_data_->dot_perf->Count(1, "adjust_bonus_with_mix_wyh_count",
                                        "bonus_minus_0");
      } else {
        session_data_->dot_perf->Count(1, "adjust_bonus_with_mix_wyh_count", "bonus_minus");
        session_data_->dot_perf->Interval(minus_bonus_with_mix_rank_input_weight_ *
                rank_benifit * session_data_->get_ad_with_other_score_diff() + 1,
                                      "adjust_bonus_with_mix_wyh_interval", "bonus_minus");
      }
    } else if (session_data_->get_ad_with_other_score_diff() > 0) {  // 广告混排分高，需要增加 bonus
      auto bonus_weight_rb = p_ad->ad_mix_info.mix_unify_bid_unify_bonus / rank_benifit;
      session_data_->dot_perf->Interval(bonus_weight_rb,
                                      "bonus_weight_with_mix_wyh_interval");
      // bonus 占比需要小于门槛才给增加 bonus
      if (bonus_weight_rb < bonus_weight_for_rb_thr_to_add_bonus_) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus +=
            adjust_bonus_with_mix_rank_input_weight_ *
                rank_benifit * session_data_->get_ad_with_other_score_diff() + 1;
        session_data_->dot_perf->Interval(adjust_bonus_with_mix_rank_input_weight_ *
                rank_benifit * session_data_->get_ad_with_other_score_diff() + 1,
                                      "adjust_bonus_with_mix_wyh_interval", "add_bonus");
        session_data_->dot_perf->Count(1, "adjust_bonus_with_mix_wyh_count", "add_bonus");
      } else {
        session_data_->dot_perf->Count(1, "adjust_bonus_with_mix_wyh_count",
                                        "add_bonus_false");
      }
    }
  }
  // [dingyiming05] 监控
  p_ad->ad_price.mix_unify_bonus_before_fctr = p_ad->ad_mix_info.mix_unify_bid_unify_bonus;
  if (enable_mix_fctr_ && !session_data_->get_is_fctr_model_failed() &&
        session_data_->IsThanosMixTraffic()) {
    // 精混排一致性 fctr 模型打分覆盖 bonus
    bool fctr_change_bonus_flag = true;
    // 直播
    if (disable_fctr_live_bonus_ && p_ad->item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
      fctr_change_bonus_flag = false;
    }
    // 粉条
    auto account_type = item->ad_deliver_info().ad_base_info().account_type();
    if (disable_fctr_fanstop_bonus_ && account_type == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
      fctr_change_bonus_flag = false;
    }
    if (enable_skip_fctr_for_exp_) {
      fctr_change_bonus_flag = false;
    }
    if (fctr_change_bonus_flag) {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus = p_ad->ad_mix_info.fctr_bonus_result * 1e6;
      if (enable_client_ai_use_fctr_bonus_) {
        auto p_item = p_ad->GetResult();
        if (p_item != nullptr) {
          p_item->set_fctr(p_ad->ad_mix_info.mix_unify_bid_unify_bonus);
        }
      }
    }
  }

  const std::string& app_id = ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request()));
  // w5 人群提高 3% bonus
  if (enable_unify_bonus_add_3_percent_bonus_groupBy_llsid_) {
    double w5_extra_3_percent_bonus_weight = 1.0;
    if (app_id == "kuaishou") {
      w5_extra_3_percent_bonus_weight = w5_extra_3_percent_bonus_weight_gamora_;
    } else {
      w5_extra_3_percent_bonus_weight = w5_extra_3_percent_bonus_weight_nebula_;
    }
    p_ad->ad_mix_info.mix_unify_bid_unify_bonus +=
          p_ad->ad_mix_info.mix_unify_bid_unify_cpm * w5_extra_3_percent_bonus_weight;
  }

  // unify_bonus 边际 ROI 探索
  if (enable_bonus_allocation_cpm_user_exp_ && session_data_->IsThanosMixTraffic()) {
    auto cpm = p_ad->ad_mix_info.mix_unify_bid_unify_cpm;
    auto user_type = session_data_->get_ad_request()->ad_user_info().buyer_effective_type();
    for (auto& conf : alloc_by_user_cpm_bonus_exp_confs_) {
      if (cpm >= conf.cpm_lbound * 1e6 && cpm < conf.cpm_ubound * 1e6 \
          && conf.user_type_bonus_ratio_map.count(user_type)) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
            p_ad->ad_mix_info.mix_unify_bid_unify_cpm * conf.user_type_bonus_ratio_map[user_type];
        break;
      } else if (cpm >= conf.cpm_lbound * 1e6 && cpm < conf.cpm_ubound * 1e6) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
            p_ad->ad_mix_info.mix_unify_bid_unify_cpm * conf.user_type_bonus_ratio_map["other"];
        break;
      }
    }
  } else if (enable_unify_bonus_exp_v2_ && session_data_->IsThanosMixTraffic()) {
    auto cpm = p_ad->ad_mix_info.mix_unify_bid_unify_cpm;
    for (const auto& conf : unify_bonus_exp_conf_) {
      if (cpm >= conf.cpm_lbound * 1e6 && cpm < conf.cpm_ubound * 1e6) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
            p_ad->ad_mix_info.mix_unify_bid_unify_cpm * conf.bonus_ratio;
        break;
      }
    }
  }

  if (enable_fanstop_bonus_adjust_ && session_data_->IsThanosMixTraffic()) {
    auto base_info_account_type = item->ad_deliver_info().ad_base_info().account_type();
    if (base_info_account_type == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= fanstop_bonus_adjust_ratio_;
    }
  }

  if (enable_fanstop_bonus_adjust_v2 && session_data_->IsThanosMixTraffic() && p_ad->is_inner_loop_ad()) {
    auto base_info_account_type = item->ad_deliver_info().ad_base_info().account_type();
    if (base_info_account_type == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 ||
        base_info_account_type == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_TEMU ||
        base_info_account_type == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP) {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= fanstop_bonus_adjust_ratio_;
    } else {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= inner_no_fanstop_bonus_adjust_ratio_;
    }
  }

  if (enable_bonus_reallocation_exp_ && session_data_->IsThanosMixTraffic() && p_ad->is_inner_loop_ad()) {
    auto cpm = p_ad->ad_mix_info.mix_unify_bid_unify_cpm;
    for (const auto& conf : bonus_reallocation_exp_conf_) {
      if (cpm >= conf.cpm_lbound * 1e6 && cpm < conf.cpm_ubound * 1e6) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
            p_ad->ad_mix_info.mix_unify_bid_unify_cpm * conf.bonus_ratio;
        break;
      }
    }
  }

  // bonus 理解实验
  if (enable_inner_multi_queue_bonus_reallocation_ && session_data_->IsThanosMixTraffic()) {
    std::string inner_multi_queue_exp_tag =
      SPDM_inner_multi_queue_exp_tag(session_data_->get_spdm_ctx());
    auto account_type = item->ad_deliver_info().ad_base_info().account_type();
    auto inner_multi_queue_flow = p_ad->base.inner_multi_queue_flow;
    std::string exp_item_tag = "_default";
    if (disable_inner_multi_queue_flow_) exp_item_tag = "_disable_flow";
    if (account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 && p_ad->is_inner_loop_ad() &&
        (disable_inner_multi_queue_flow_ || inner_multi_queue_flow)) {
      session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
        "bonus_inner_multi_queue_experiment_origin", "bonus" + exp_item_tag, inner_multi_queue_exp_tag);
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus = p_ad->base.hc_need_support * 1e6;
      session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
        "bonus_inner_multi_queue_experiment", "bonus" + exp_item_tag, inner_multi_queue_exp_tag);
      session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_cpm,
        "bonus_inner_multi_queue_experiment", "cpm" + exp_item_tag, inner_multi_queue_exp_tag);
    }
  }

  if (enable_bonus_fixed_ratio_experiment_v2_ && session_data_->IsThanosMixTraffic()) {
    // bonus 置为 cpm 的固定比例 for maximize cpm
    p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
        p_ad->ad_mix_info.mix_unify_bid_unify_cpm * bonus_fixed_ratio_v2_;
    session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
                                      "bonus_cpm_ratio_experiment", "max_cpm_bonus");
  }

  // 负 bonus 置 0
  if (enable_set_negative_bonus_to_zero_) {
    if (p_ad->ad_mix_info.mix_unify_bid_unify_bonus < 0) {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus = 0;
    }
  }

  // 异常 unify_bonus 截断处理 by ad_pv_bonus_frac_thr
  if (enable_calib_abnormal_unify_bonus_by_thr_ && session_data_->IsThanosMixTraffic()) {
    // 系数校准 & 截断
    if (app_id == "kuaishou") {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= calibration_weight_for_abnormal_unify_bonus_gamora1_;
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
        std::min(p_ad->ad_mix_info.mix_unify_bid_unify_bonus, ad_pv_bonus_frac_thr_gamora_ * 1e6);
    } else {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= calibration_weight_for_abnormal_unify_bonus_nebula1_;
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
        std::min(p_ad->ad_mix_info.mix_unify_bid_unify_bonus, ad_pv_bonus_frac_thr_nebula_ * 1e6);
    }
  }

  // 认知实验：异常 unify_bonus 截断处理 by cpm
  if (enable_calib_abnormal_unify_bonus_by_cpm_ && session_data_->IsThanosMixTraffic()) {
    // 系数校准 & 截断
    if (app_id == "kuaishou") {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= calibration_weight_for_abnormal_unify_bonus_gamora2_;
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
        std::min(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
          cpm_times_thr_gamora_ * p_ad->ad_mix_info.mix_unify_bid_unify_cpm);
    } else {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= calibration_weight_for_abnormal_unify_bonus_nebula2_;
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
        std::min(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
          cpm_times_thr_nebula_ * p_ad->ad_mix_info.mix_unify_bid_unify_cpm);
    }
  }

  // 认知实验：异常 unify_bonus 截断处理 by max(ad_pv_bonus_frac_thr,cpm)
  if (enable_calib_abnormal_unify_bonus_by_max_cpm_and_thr_ && session_data_->IsThanosMixTraffic()) {
    // 系数校准 & 截断
    if (app_id == "kuaishou") {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= calibration_weight_for_abnormal_unify_bonus_gamora3_;
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
        std::min(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
          std::max(cpm_times_thr_gamora_ * p_ad->ad_mix_info.mix_unify_bid_unify_cpm,
            ad_pv_bonus_frac_thr_gamora_ * 1e6));
    } else {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= calibration_weight_for_abnormal_unify_bonus_nebula3_;
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus =
        std::min(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
          std::max(cpm_times_thr_nebula_ * p_ad->ad_mix_info.mix_unify_bid_unify_cpm,
            ad_pv_bonus_frac_thr_nebula_ * 1e6));
    }
  }

  if (enable_ad_unify_bonus_weight_exp_ && session_data_->IsThanosMixTraffic()) {
    // bonus 留反实验流量置 0
    double eps = 1e-6;
    if (app_id == "kuaishou") {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= calibration_weight_for_abnormal_unify_bonus_gamora4_;
      if (ad_unify_bonus_weight_gamora_ < eps) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus = 0;
      }
    } else {
      p_ad->ad_mix_info.mix_unify_bid_unify_bonus *= calibration_weight_for_abnormal_unify_bonus_nebula4_;
      if (ad_unify_bonus_weight_nebula_ < eps) {
        p_ad->ad_mix_info.mix_unify_bid_unify_bonus = 0;
      }
    }
  }

  session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_cpm,
                                      "bonus_cpm_ratio_experiment", "cpm");
  session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
                                      "bonus_cpm_ratio_experiment", "bonus");
}

void FillOtherMixInfoMixer::JudgeAdd3PercentBonusGroupByLlsid(
    ks::platform::AddibleRecoContextInterface* context, ks::platform::AttrTable* item_table) {
  if (!item_table) {
    return;
  }
  if (session_data_->get_w_user_level() != 5) {  // 只针对 w5 人群 增加额外 3% bonus
    return;
  }
  double sum_unify_cpm = 0.0;
  int avg_unify_cpm_item_cnt = 0;
  double max_unify_cpm = 0.0;
  auto* ad_common_ptr_attr = item_table->GetOrInsertAttr("ad_common_ptr");
  auto& item_list = item_table->GetCommonRecoResults();
  for (auto iter = item_list.begin(); iter != item_list.end(); iter++) {
    const auto& item = *iter;
    auto raw_ad = context->GetExtraItemAttr(item, ad_common_ptr_attr);
    if (!raw_ad) {
      continue;
    }
    RankAdCommon* p_ad = *(boost::any_cast<std::shared_ptr<RankAdCommon*>>(*raw_ad));
    if (!p_ad) {
      continue;
    }
    auto* item_tt = p_ad->GetResult();
    if (item_tt == nullptr) {
      continue;
    }
    auto* p_rank_result = GetAdRankResult(session_data_, *item_tt);
    if (p_rank_result == nullptr) {
      continue;
    }
    if (p_rank_result->rank_price_info().cpm() > 0) {
      sum_unify_cpm += p_rank_result->rank_price_info().cpm();
      avg_unify_cpm_item_cnt += 1;
      if (p_rank_result->rank_price_info().cpm() > max_unify_cpm) {
        max_unify_cpm = p_rank_result->rank_price_info().cpm();
      }
    }
  }
  if (SPDM_enable_w5_add_3_percent_bonus_max(session_data_->get_spdm_ctx())) {
    if (max_unify_cpm <= 0) {
      return;
    }
    if (max_unify_cpm > w5_extra_3_percent_bonus_cpm_lower_bound_ &&
        max_unify_cpm <= w5_extra_3_percent_bonus_cpm_upper_bound_) {
      enable_unify_bonus_add_3_percent_bonus_groupBy_llsid_ = true;
    }
    return;
  } else if (avg_unify_cpm_item_cnt == 0) {
    return;
  } else {
    auto avg_unify_cpm = sum_unify_cpm / avg_unify_cpm_item_cnt;
    if (avg_unify_cpm > w5_extra_3_percent_bonus_cpm_lower_bound_
          && avg_unify_cpm <= w5_extra_3_percent_bonus_cpm_upper_bound_) {
      enable_unify_bonus_add_3_percent_bonus_groupBy_llsid_ = true;
    }
  }
}

size_t FillOtherMixInfoMixer::GetAdjustBonusItemIndex(ks::platform::AddibleRecoContextInterface* context,
       ks::platform::AttrTable* item_table) {
  size_t adjust_bonus_item_idx = -1;
  if (!item_table) {
    return adjust_bonus_item_idx;
  }
  int64 max_rank_benefit = 0;
  auto* ad_common_ptr_attr = item_table->GetOrInsertAttr("ad_common_ptr");
  if (ad_common_ptr_attr == nullptr) {
    return adjust_bonus_item_idx;
  }
  auto& item_list = item_table->GetCommonRecoResults();
  if (item_list.empty()) {
    return adjust_bonus_item_idx;
  }
  for (auto iter = item_list.begin(); iter != item_list.end(); iter++) {
      const auto& item = *iter;
      auto raw_ad = context->GetExtraItemAttr(item, ad_common_ptr_attr);
      if (!raw_ad) {continue;}
      RankAdCommon* p_ad = *(boost::any_cast<std::shared_ptr<RankAdCommon*>>(*raw_ad));
      if (!p_ad) {continue;}
      auto idx = item.GetAttrIndex();
      auto* item_tt = p_ad->GetResult();
      if (item_tt == nullptr) {
        continue;
      }
      auto* p_rank_result = GetAdRankResult(session_data_, *item_tt);
      if (p_rank_result == nullptr) {
        continue;
      }
      if (p_rank_result->rank_price_info().rank_benifit() > max_rank_benefit) {
        max_rank_benefit = p_rank_result->rank_price_info().rank_benifit();
        adjust_bonus_item_idx = idx;
      }
  }
  return adjust_bonus_item_idx;
}

void FillOtherMixInfoMixer::FillMtbInfo(RankAdCommon* p_ad) {
  auto* item = p_ad->GetResult();
  if (item == nullptr) {
    return;
  }
  auto* p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result == nullptr) {
    return;
  }
  if (!(session_data_->IsThanosMixTraffic())) {
    return;
  }
  kuaishou::ad::AdEnum::AdMonitorType ad_monitor_type = GetAdMonitorType(*item);
  std::string item_tag = absl::Substitute(
      "$0_$1_$2_$3", ad_monitor_type,
      kuaishou::ad::AdEnum::AdQueueType_Name(item->ad_deliver_info().ad_queue_type()),
      kuaishou::ad::AdEnum::ItemType_Name(item->ad_deliver_info().ad_base_info().item_type()),
      kuaishou::ad::AdEnum::CampaignType_Name(item->ad_deliver_info().ad_base_info().campaign_type()));
  const std::string& app_id = ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request()));
  double DEFAULT_SCTR_AVG = 0.4778;
  double sctr = DEFAULT_SCTR_AVG;
  if (p_rank_result->rank_rank_info().ctr_start() == kuaishou::ad::AdActionType::AD_DELIVERY
      && p_rank_result->rank_rank_info().ctr_end() == kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION
      && p_rank_result->rank_rank_info().unify_ctr() > 0.0
      && p_rank_result->rank_rank_info().unify_ctr() < 1.0) {
    // 当 unify ctr 为曝光率时，sctr 就是 unify_ctr
    sctr = p_rank_result->rank_rank_info().unify_ctr();
  } else if (p_rank_result->rank_rank_info().ctr_start() != kuaishou::ad::AdActionType::AD_DELIVERY) {
    // 当 unify ctr 的预估起始点不是下发时，表示没有对曝光率预估，cpm 中不包含曝光率
    sctr = 1.0;
  } else {
    // 当 unify ctr 预估起始点为下发，终点不是曝光时，曝光率采用后验统计均值
    sctr = session_data_->get_spdm_ctx().TryGetDouble(
        "ad_mix_client_ad_cpm_param_sctr_avg", DEFAULT_SCTR_AVG);
    if (sctr <= 0) {
      sctr = DEFAULT_SCTR_AVG;
    }
  }
  if (sctr <= 0.0) {
    sctr = DEFAULT_SCTR_AVG;
  }
  if (!enable_close_unify_cpm_divide_sctr_ratio_) {
    double client_cpm_sctr_ratio = 1.0;
    // 硬广需要除固定系数, 品牌归为硬广
    if (item->ad_deliver_info().ad_queue_type() == kuaishou::ad::AdEnum_AdQueueType_HARD_AD_QUEUE
        || item->ad_source_type() == ::kuaishou::ad::AdSourceType::BRAND) {
      client_cpm_sctr_ratio = app_id == "kuaishou" ? session_data_->get_client_cpm_sctr_ratio_main()
                                                    : session_data_->get_client_cpm_sctr_ratio_nebula();
    }
    if (client_cpm_sctr_ratio > 0) {
      // 修复 unify_cpm 调权失效问题，将初始化 ad_mix_info 的 unify_cpm 前移到 CalMixBenefitDirect
      if (enable_fix_mix_unify_cpm_init_) {
        p_ad->ad_mix_info.mix_unify_bid_unify_cpm /= client_cpm_sctr_ratio;
      } else {
        p_ad->ad_mix_info.mix_unify_bid_unify_cpm =
          p_rank_result->rank_price_info().cpm() / client_cpm_sctr_ratio;
      }
    }
  }

  // mix_cpm = mix_cpm + customer_hc
  if (!enable_close_unify_cpm_add_customer_hc_) {
    p_ad->ad_mix_info.mix_unify_bid_unify_cpm += p_rank_result->ad_rank_trans_info().customer_hc_score();
  }
  session_data_->dot_perf->Interval(p_rank_result->ad_rank_trans_info().customer_hc_score(),
      "adjust_mix_cpm_by_customer_hc");

  double unify_gpm_cali_ratio = 1.0;
  if (enable_mtb_unify_gpm_cali_) {
    auto item_type = item->ad_deliver_info().ad_base_info().item_type();
    auto ad_queue_type = item->ad_deliver_info().ad_queue_type();
    if (item_type == kuaishou::ad::AdEnum::ITEM_LIVE ||
        item_type == kuaishou::ad::AdEnum::ITEM_PHOTO_TO_LIVE) {
      if (ad_queue_type == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
       unify_gpm_cali_ratio =  unify_gpm_cali_params_.hard_live_cali_ratio();
      } else if (ad_queue_type == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
        unify_gpm_cali_ratio =  unify_gpm_cali_params_.soft_live_cali_ratio();
      }
    } else if (item_type == kuaishou::ad::AdEnum::ITEM_PHOTO) {
      if (ad_queue_type == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
        unify_gpm_cali_ratio =  unify_gpm_cali_params_.hard_photo_cali_ratio();
      } else if (ad_queue_type == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
        unify_gpm_cali_ratio =  unify_gpm_cali_params_.soft_photo_cali_ratio();
      }
    }
  }

  p_ad->ad_mix_info.mix_unify_bid_unify_gpm =
      p_rank_result->rank_price_info().mix_unify_gpm() * 1000 * kBenifitFactor * unify_gpm_cali_ratio;
  int industry_hc_tag = p_rank_result->ad_rank_trans_info().industry_hc_tag();
  double industry_hc_score = p_rank_result->ad_rank_trans_info().industry_hc_score();
  FillUnifyBonusInfo(p_ad);
  // unify_cpm 修改最后做个异常大值截断
  if (enable_mix_rank_truncate) {
    if (p_ad->ad_mix_info.mix_unify_bid_unify_cpm > mix_rank_truncate_max) {
      p_ad->ad_mix_info.mix_unify_bid_unify_cpm = mix_rank_truncate_max;
    }
  }
  // 内循环覆写, 外循环不操作
  if (!(disable_multi_head_mix_gpm_outer_ &&
        ad_monitor_type == kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT)) {
    if (enable_add_multi_head_mix_gpm_live_ &&
        (p_ad->item_type() == kuaishou::ad::AdEnum::ITEM_LIVE ||
            p_ad->item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO_TO_LIVE)) {
      p_ad->ad_mix_info.mix_unify_bid_unify_gpm = p_rank_result->ad_rank_trans_info().unify_gpm();
      session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_gpm,
                                          "multi_head_gpm_front", "live");
    }
    if (enable_add_multi_head_mix_gpm_photo_ && p_ad->item_type() == kuaishou::ad::AdEnum::ITEM_PHOTO) {
      p_ad->ad_mix_info.mix_unify_bid_unify_gpm = p_rank_result->ad_rank_trans_info().unify_gpm();
      session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_gpm,
                                          "multi_head_gpm_front", "photo");
    }
  }
  // 限定 gpm 最小值
  if (enable_mix_unify_gpm_min_value_ && p_ad->ad_mix_info.mix_unify_bid_unify_gpm == 0.0) {
    if (ad_monitor_type == kuaishou::ad::AdEnum_AdMonitorType_INNER_LOOP_DEFAULT) {
      p_ad->ad_mix_info.mix_unify_bid_unify_gpm = mix_unify_gpm_min_value_;
    }
  }
}

void FillOtherMixInfoMixer::FillFeedExplore(RankAdCommon* p_ad) {
  auto* item = p_ad->GetResult();
  if (item == nullptr) {
    return;
  }
  auto* p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result == nullptr) {
    return;
  }
  if (!((SPDM_enable_transparent_feed_mix_fields(session_data_->get_spdm_ctx()) ||
         enable_feed_mix_score_new_) &&
        ks::ad_base::IsFeedExploreRequest(session_data_->get_sub_page_id()))) {
    return;
  }
  if (enable_feed_explore_mix_experience_value_) {
    FillExploreExperience(p_ad);
    p_ad->ad_mix_info.feed_pctr =
        p_rank_result->ad_rank_trans_info().predict_cxr_package().predict_feed_ctr();
  }
  if (enable_feed_explore_mix_price_adjust_ratio_) {
    double price_adjust_ratio = 1.0;
    if (p_ad->auction_bid() > 0) {
      price_adjust_ratio = static_cast<double>(p_ad->price()) / static_cast<double>(p_ad->auction_bid());
      session_data_->dot_perf->Interval(price_adjust_ratio * 1e6,
          "feed_explore_price_adjust_ratio", "original", "use_auction_bid");
    }
    price_adjust_ratio = std::max(inner_explore_mix_price_adjust_ratio_lower_bound_,
                                  std::min(price_adjust_ratio, 1.0));
    session_data_->dot_perf->Interval(price_adjust_ratio * 1e6, "feed_explore_price_adjust_ratio", "final");
    p_ad->ad_mix_info.price_adjust_ratio = price_adjust_ratio;
  }
  double feed_ctr = p_rank_result->ad_rank_trans_info().predict_cxr_package().predict_feed_ctr();
  int64 rank_benifit = p_rank_result->rank_price_info().rank_benifit();
  // 采用 cpm 打平实验
  if (SPDM_enable_use_feed_cpm_fields(session_data_->get_spdm_ctx())) {
    rank_benifit = p_rank_result->rank_price_info().cpm();
  }
  // 发现页双列直播广告设置自动播放
  if (SPDM_enable_feed_set_auto_play(session_data_->get_spdm_ctx()) &&
      p_ad->item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
    p_ad->ad_mix_info.is_auto_play = true;
  }
  session_data_->dot_perf->Interval(feed_ctr * 1000000, "explore_feed", "predictFeedCtr");
  session_data_->dot_perf->Interval(rank_benifit, "explore_feed", "rank_benifit");
  // 对 rank_benifit 归一化, 可以做实验, 并可以离线更新
  if (session_data_->get_feed_distribution_conf() != nullptr) {
    int64 max_rank_benifit = session_data_->get_feed_distribution_conf()->max_rank_benifit();
    int64 min_rank_benifit = session_data_->get_feed_distribution_conf()->min_rank_benifit();
    int64 avg_rank_benifit = session_data_->get_feed_distribution_conf()->avg_rank_benifit();
    double fix_denominator = std::max(max_rank_benifit - min_rank_benifit, 1L) * 1.0;
    double normalized_value = std::max(rank_benifit - min_rank_benifit, 0L) / fix_denominator;
    double normalized_mean = std::max(avg_rank_benifit - min_rank_benifit, 0L) / fix_denominator;
    double normalized_result = std::min(std::max(0.0, normalized_value - (normalized_mean - 0.5)), 1.0);
    session_data_->dot_perf->Interval(normalized_result * 1000000,
        "explore_feed", "normalized_rank_benifit");
    p_ad->ad_mix_info.ad_biz_value = normalized_result;
    p_ad->ad_mix_info.feed_pctr = feed_ctr;
    LOG_EVERY_N(INFO, 100000) << "feed_mix_fields,llsid: " << session_data_->get_llsid()
                              << ",user_id = " << session_data_->get_ad_request()->ad_user_info().id()
                              << ",device_id = "
                              << session_data_->get_ad_request()->ad_user_info().device_id()
                              << ",creative_id = " << p_ad->creative_id()
                              << ",max_rank_benifit = " << max_rank_benifit
                              << ",min_rank_benifit = " << min_rank_benifit
                              << ",avg_rank_benifit = " << avg_rank_benifit
                              << ",fix_denominator = " << fix_denominator
                              << ",normalized_value = " << normalized_value
                              << ",normalized_mean = " << normalized_mean
                              << ",normalized_result = " << normalized_result << ",feed_ctr = " << feed_ctr;
  }
  if (enable_feed_mix_score_new_ && feed_mix_score_param_.avg_cpm() > 0) {
    double max_cpm = feed_mix_score_param_.max_cpm();
    double min_cpm = feed_mix_score_param_.min_cpm();
    double avg_cpm = feed_mix_score_param_.avg_cpm();
    double scale = std::max(1.0, max_cpm - min_cpm);
    double normalized_mean = (avg_cpm - min_cpm) / scale;
    double cpm = p_ad->ad_mix_info.cpm / 1000000.0;
    double original_score = (cpm - min_cpm) / scale - normalized_mean + 0.5;
    if (feed_mix_score_user_tag_weight_ > 0) {
      original_score *= feed_mix_score_user_tag_weight_;
    }
    if (feed_mix_score_param_.global_weight() > 0) {
      original_score *= feed_mix_score_param_.global_weight();
    }
    double normalized_result = std::min(std::max(0.0, original_score), 1.0);
    p_ad->ad_mix_info.ad_biz_value = normalized_result;
    session_data_->dot_perf->Interval(normalized_result * 1000, "explore_feed", "original_score");
    session_data_->dot_perf->Interval(normalized_result * 1000, "explore_feed", "ad_biz_value");
  }
}

void FillOtherMixInfoMixer::FillInnerExplore(RankAdCommon* p_ad, const RankAdCommon* pre_ad) {
  // 主站发现页内流混排特殊处理逻辑
  if (!session_data_->get_pos_manager().IsInnerExplore()) {
    return;
  }
  if (enable_inner_explore_mix_price_adjust_ratio_) {
    double price_adjust_ratio = 1.0;
    double cpm_mul_sctr = p_ad->cpm() * p_ad->ad_mix_info.sctr;
    if (cpm_mul_sctr > 0) {
      price_adjust_ratio = 1.0 * p_ad->price() * kBenifitFactor / cpm_mul_sctr;
      session_data_->dot_perf->Interval(price_adjust_ratio * 1e6,
          "inner_explore_price_adjust_ratio", "original", "use_cpm_mul_sctr");
    } else if (p_ad->auction_bid() > 0) {
      price_adjust_ratio = 1.0 * p_ad->price() / p_ad->auction_bid();
      session_data_->dot_perf->Interval(price_adjust_ratio * 1e6,
          "inner_explore_price_adjust_ratio", "original", "use_auction_bid");
    }
    price_adjust_ratio = std::max(inner_explore_mix_price_adjust_ratio_lower_bound_,
                                  std::min(price_adjust_ratio, 1.0));
    session_data_->dot_perf->Interval(price_adjust_ratio * 1e6, "inner_explore_price_adjust_ratio", "final");
    if (enable_fix_set_mix_price_adjust_ratio_) {
      p_ad->ad_mix_info.price_adjust_ratio = price_adjust_ratio;
    }
  }
  if (enable_inner_explore_mix_experience_value_) {
    FillExploreExperience(p_ad);
  }
  if (!enable_inner_explore_mix_ratio && !enable_inner_explore_mix_hc_opt) {
    return;
  }
  double inner_explore_mix_ratio = p_ad->ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE
                                       ? inner_explore_mix_soft_ratio
                                       : inner_explore_mix_hard_ratio;
  if (enable_inner_explore_mix_ratio_opt_) {
    if (p_ad->ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
      inner_explore_mix_ratio = p_ad->item_type() == kuaishou::ad::AdEnum::ITEM_LIVE
                                ? inner_explore_mix_ratio_soft_live_ : inner_explore_mix_ratio_soft_photo_;
    } else {
      inner_explore_mix_ratio = p_ad->item_type() == kuaishou::ad::AdEnum::ITEM_LIVE
                                ? inner_explore_mix_ratio_hard_live_ : inner_explore_mix_ratio_hard_photo_;
    }
  }
  double score_ratio = 1.0;
  if (enable_inner_explore_mix_cpm_opt && inner_uplift_cpm_conf_.size() > 0) {
    bool is_flag = true;
    for (auto& item : inner_uplift_cpm_conf_) {
      if (item.first > p_ad->ad_mix_info.cpm/1000000) {
        score_ratio = item.second;
        is_flag = false;
        break;
      }
    }
    if (is_flag) {  // 兜底策略，最后一档的值
      score_ratio = inner_uplift_cpm_conf_[inner_uplift_cpm_conf_.size()-1].second;
    }
    session_data_->dot_perf->Interval(score_ratio*100, "explore_feed_uplift_mix_cpm");
  }
  inner_explore_mix_ratio *= score_ratio;
  auto rank_result = p_ad->GetRankResult();
  if (enable_mix_inner_explore_relative_hc_ && rank_result != nullptr) {
    p_ad->ad_mix_info.cpm_bonus += static_cast<int64_t>(
        rank_result->ad_rank_trans_info().inner_explore_relative_hc());
  }
  if (enable_inner_explore_fairness_rerank_ && p_ad->get_innovation_flow_rerank_score() > 0) {
    p_ad->ad_mix_info.cpm = static_cast<int64_t>(p_ad->get_innovation_flow_rerank_score() *
                                                 inner_explore_mix_ratio);
    p_ad->ad_mix_info.cpm_bonus = static_cast<int64_t>(p_ad->ad_mix_info.cpm_bonus *
                                                       inner_explore_mix_ratio);
  } else {
    p_ad->ad_mix_info.cpm = static_cast<int64_t>(p_ad->ad_mix_info.cpm * inner_explore_mix_ratio);
    p_ad->ad_mix_info.cpm_bonus = static_cast<int64_t>(p_ad->ad_mix_info.cpm_bonus * inner_explore_mix_ratio);
  }
  if (enable_inner_explore_mix_hc_opt) {
    p_ad->ad_mix_info.cpm_bonus = static_cast<int64_t>(p_ad->ad_mix_info.cpm_bonus +
                                                       p_ad->hc_score());
  }

  if (enable_replace_mix_bonus_with_cpm_inner_explore_) {
    double bonus_cpm_ratio = p_ad->ad_mix_info.cpm_bonus /
                              std::max(1e-6, static_cast<double>(p_ad->ad_mix_info.cpm));
    session_data_->dot_perf->Interval(bonus_cpm_ratio * 10000, "inner_explore_bonus_cpm_ratio");

    p_ad->ad_mix_info.cpm_bonus = static_cast<int64_t>(p_ad->ad_mix_info.cpm *
                                                          replace_mix_bonus_with_cpm_ratio_inner_explore_);

    double bonus_cpm_ratio2 = p_ad->ad_mix_info.cpm_bonus /
                              std::max(1e-6, static_cast<double>(p_ad->ad_mix_info.cpm));
    session_data_->dot_perf->Interval(bonus_cpm_ratio2 * 10000, "inner_explore_bonus_cpm_ratio2");
  }

  if (enable_fill_unify_bonus_info_inner_explore_) {
    p_ad->ad_mix_info.mix_unify_bid_unify_bonus = p_ad->ad_mix_info.cpm_bonus;
  }

  p_ad->ad_price.mix_unify_bonus_before_fctr = p_ad->ad_mix_info.mix_unify_bid_unify_bonus;
  if (enable_inner_explore_mix_fctr_ && !session_data_->get_is_fctr_model_failed()) {
    // 精混排一致性 fctr 模型打分覆盖 bonus
    p_ad->ad_mix_info.cpm_bonus = static_cast<int64_t>(p_ad->ad_mix_info.fctr_bonus_result * 1e6);
    p_ad->ad_mix_info.mix_unify_bid_unify_bonus = p_ad->ad_mix_info.cpm_bonus;

    session_data_->dot_perf->Interval(p_ad->ad_price.mix_unify_bonus_before_fctr,
                  "inner_explore_bonus_fctr", inner_explore_fctr_exp_tag_, "bouns_before_fctr");
    session_data_->dot_perf->Interval(p_ad->ad_mix_info.mix_unify_bid_unify_bonus,
                  "inner_explore_bonus_fctr", inner_explore_fctr_exp_tag_, "bouns_after_fctr");
  }

  session_data_->dot_perf->Interval(p_ad->hc_score(), "inner_explore_mix_info", "hc");
  session_data_->dot_perf->Interval(p_ad->ad_mix_info.cpm_bonus, "inner_explore_mix_info", "mix_bouns");
  session_data_->dot_perf->Interval(p_ad->bonus_cpm(), "inner_explore_mix_info", "rank_bouns");
  session_data_->dot_perf->Interval(p_ad->cpm(), "inner_explore_mix_info", "rank_cpm");
  session_data_->dot_perf->Interval(p_ad->ad_mix_info.cpm, "inner_explore_mix_info", "mix_cpm");
}

void FillOtherMixInfoMixer::FillExploreExperience(RankAdCommon* p_ad) {
  if (p_ad == nullptr) {
    return;
  }
  if ((session_data_->get_pos_manager().IsInnerExplore() && enable_inner_explore_mix_experience_value_) ||
      (is_feed_explore_req_ && enable_feed_explore_mix_experience_value_)) {
    const std::string& ad_queue_type = kuaishou::ad::AdEnum::AdQueueType_Name(p_ad->ad_queue_type());
    const std::string& item_type = kuaishou::ad::AdEnum::ItemType_Name(p_ad->item_type());
    if (explore_experience_conf_ != nullptr) {
      const auto& inner_experience_score_map =
          (is_feed_explore_req_ && enable_feed_explore_mix_experience_value_) ?
              explore_experience_conf_->data().feed_explore_experience_score() :
              explore_experience_conf_->data().inner_explore_experience_score();
      const std::string& ocpx_action_type = kuaishou::ad::AdActionType_Name(p_ad->ocpx_action_type());
      const std::string& campaign_type = kuaishou::ad::AdEnum::CampaignType_Name(p_ad->campaign_type());
      auto key = absl::StrCat(explore_mix_experience_exp_tag_, "|", item_type, "|",
                              ad_queue_type, "|", campaign_type, "|", ocpx_action_type, "|",
                              session_data_->get_ad_request()->ad_user_info().user_value_group_tag());
      auto iter = inner_experience_score_map.find(key);
      if (iter != inner_experience_score_map.end()) {
        const auto& experience_score = iter->second;
        p_ad->ad_mix_info.ad_play_time = experience_score.ad_play_time();
        p_ad->ad_mix_info.play7s = experience_score.valid_play_rate();
        p_ad->ad_mix_info.lvtr = experience_score.long_play_rate();
        p_ad->ad_mix_info.cpr = experience_score.complete_rate();
        p_ad->ad_mix_info.svr = experience_score.short_play_rate();
        p_ad->ad_mix_info.ltr = experience_score.like_rate();
        p_ad->ad_mix_info.wtr = experience_score.follow_rate();
        p_ad->ad_mix_info.cmtr = experience_score.comment_rate();
        p_ad->ad_mix_info.ccr = experience_score.collect_rate();
        p_ad->ad_mix_info.ftr = experience_score.share_rate();
        p_ad->ad_mix_info.htr = experience_score.dislike_rate();
        p_ad->ad_mix_info.rtr = experience_score.report_rate();
        if (experience_score.gpm() > 0) {
          p_ad->ad_mix_info.gpm = experience_score.gpm();
        }
        session_data_->dot_perf->Interval(p_ad->ad_mix_info.ad_play_time * 1000,
            "inner_explore_experience_info", "ad_play_time", item_type, ad_queue_type);
        session_data_->dot_perf->Interval(p_ad->ad_mix_info.play7s * 1000000,
            "inner_explore_experience_info", "valid_paly_rate", item_type, ad_queue_type);
        session_data_->dot_perf->Interval(p_ad->ad_mix_info.lvtr * 1000000,
            "inner_explore_experience_info", "long_paly_rate", item_type, ad_queue_type);
        session_data_->dot_perf->Interval(p_ad->ad_mix_info.svr * 1000000,
            "inner_explore_experience_info", "short_play_rate", item_type, ad_queue_type);
        session_data_->dot_perf->Interval(p_ad->ad_mix_info.htr * 1000000,
            "inner_explore_experience_info", "dislike_rate", item_type, ad_queue_type);
      }
    }
    if (enable_explore_post_gpm_) {
      auto rank_result = p_ad->GetRankResult();
      if (rank_result != nullptr && rank_result->ad_rank_trans_info().post_gpm() > 0) {
        p_ad->ad_mix_info.gpm = rank_result->ad_rank_trans_info().post_gpm();
      }
      session_data_->dot_perf->Interval(p_ad->ad_mix_info.gpm,
          "inner_explore_gpm_stat", p_ad->ad_mix_info.gpm > 0 ? "1" : "0",
          item_type, ad_queue_type, p_ad->is_inner_loop_ad() ? "inner" : "outer");
    }
  }
}

void FillOtherMixInfoMixer::CalcInnerAdBizValue(RankAdCommon* p_ad, const RankAdCommon* pre_ad) {
  if (!enale_inner_explore_live_score_ || p_ad->item_type() != kuaishou::ad::AdEnum::ITEM_LIVE) {
    return;
  }
  if (enable_inner_explore_live_item_as_p2l_ && p_ad->item_type() == kuaishou::ad::AdEnum::ITEM_LIVE) {
    p_ad->ad_mix_info.item_type = kuaishou::ad::AdEnum::ITEM_PHOTO_TO_LIVE;
    session_data_->dot_perf->Count(1, "inner_explore_live_score", "live_as_p2l");
    return;
  }
  double score = 0;
  double original_score = 0;
  double cpm = p_ad->ad_mix_info.cpm + p_ad->ad_mix_info.cpm_bonus;
  auto strategy_id = explore_live_score_param_.strategy_id();
  session_data_->dot_perf->Count(
      1, "inner_explore_live_score", "strategy_hit", std::to_string(strategy_id));
  switch (strategy_id) {
  case ks::front_server::kconf::ExploreLiveScoreConf::LIVE_SCORE_STRATEGY_KEEP_ORDER: {
    if (pre_ad != nullptr) {
      double pre_cpm = pre_ad->ad_mix_info.cpm + pre_ad->ad_mix_info.cpm_bonus;
      cpm = std::max(pre_cpm - 1.0, cpm);
    }
    double eco_pow = p_ad->ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE
                     ? mix_ad_top_fans_eco_weight_
                     : mix_ad_dsp_eco_weight_;
    if (p_ad->ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
      cpm *= mix_ad_fans_cpm_boost_weight_ *
          std::pow(mix_auction_top_fans_weight_, 1.0 / std::max(1e-6, eco_pow));
    }
    auto auction_cpm = std::pow(cpm / 1e6, eco_pow / std::max(1e-6, mix_ad_live_score_pow_));
    original_score = (auction_cpm - mix_ad_live_score_bias_) / std::max(1.0, mix_ad_live_score_alpha_);
    break;
  }
  case ks::front_server::kconf::ExploreLiveScoreConf::LIVE_SCORE_STRATEGY_CPM_DIST: {
    if (enale_inner_explore_live_soft_weight_ &&
        p_ad->ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
      cpm *= mix_ad_fans_cpm_boost_weight_;
    }
    if (explore_live_score_param_.has_cpm_dist()) {
      double max_cpm = explore_live_score_param_.cpm_dist().max_cpm();
      double min_cpm = explore_live_score_param_.cpm_dist().min_cpm();
      double avg_cpm = explore_live_score_param_.cpm_dist().avg_cpm();
      double scale = std::max(1.0, max_cpm - min_cpm);
      double normalized_avg = (avg_cpm - min_cpm) / scale;
      original_score = ((cpm - min_cpm) / scale - normalized_avg + 1) * 0.5;
    }
    break;
  }
  case ks::front_server::kconf::ExploreLiveScoreConf::LIVE_SCORE_STRATEGY_CPM_STD: {
    if (enale_inner_explore_live_soft_weight_ &&
        p_ad->ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
      cpm *= mix_ad_fans_cpm_boost_weight_;
    }
    if (explore_live_score_param_.has_cpm_dist()) {
      double avg_cpm = explore_live_score_param_.cpm_dist().avg_cpm();
      double std_cpm = explore_live_score_param_.cpm_dist().std_cpm();
      original_score = ((cpm - avg_cpm) / std::max(std_cpm, 1.0) + 1) * 0.5;
    }
    break;
  }
  default:
    break;
  }
  if (original_score < 0) {
    session_data_->dot_perf->Count(
        1, "inner_explore_live_score", "illegal_original_score", std::to_string(strategy_id));
  } else {
    session_data_->dot_perf->Interval(static_cast<int64_t>(original_score * 1e6),
        "inner_explore_live_score", "original_score", std::to_string(strategy_id));
    double ratio = explore_live_score_param_.var_ratio();
    if (ratio > 0) {
      original_score = 0.5 + ratio * (original_score - 0.5);
    }
    session_data_->dot_perf->Interval(static_cast<int64_t>(original_score * 1e6),
        "inner_explore_live_score", "score_after_ratio", std::to_string(strategy_id));
    if (explore_live_score_param_.user_weight() > 0) {
      original_score *= explore_live_score_param_.user_weight();
    }
    session_data_->dot_perf->Interval(static_cast<int64_t>(original_score * 1e6),
        "inner_explore_live_score", "user_tag_weight_score", std::to_string(strategy_id));
    if (explore_live_score_param_.global_weight() > 0) {
      original_score *= explore_live_score_param_.global_weight();
    }
    session_data_->dot_perf->Interval(static_cast<int64_t>(original_score * 1e6),
        "inner_explore_live_score", "global_weight_score", std::to_string(strategy_id));
  }
  score = std::min(1.0, std::max(1e-6, original_score));
  p_ad->ad_mix_info.ad_biz_value = score;
  session_data_->dot_perf->Interval(static_cast<int64_t>(score * 1e6),
      "inner_explore_live_score", "final_score", std::to_string(strategy_id));
  return;
}

void FillOtherMixInfoMixer::FillUnifyInfo(RankAdCommon* p_ad) {
  auto* item = p_ad->GetResult();
  if (item == nullptr) {
    return;
  }
  auto* p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result == nullptr) {
    return;
  }
  if (!session_data_->IsThanosMixTraffic()) {
    return;
  }
  // 条件判断 and ab 参数，内粉额外考虑下
  bool is_hard_ad = item->ad_deliver_info().ad_queue_type()
      == kuaishou::ad::AdEnum_AdQueueType_HARD_AD_QUEUE;
  bool is_soft_ad = item->ad_deliver_info().ad_queue_type()
      == kuaishou::ad::AdEnum_AdQueueType_SOFT_AD_QUEUE;
  if (p_ad->ad_source_type() == ::kuaishou::ad::AdSourceType::BRAND) {
    is_hard_ad = true;
    is_soft_ad = false;
  }
  kuaishou::ad::AdEnum::AdMonitorType ad_monitor_type = GetAdMonitorType(*item);
  bool is_outer_loop_ad = ad_monitor_type
    == kuaishou::ad::AdEnum_AdMonitorType_EXT_LOOP_DEFAULT;
  bool is_inner_loop_ad = ad_monitor_type
    == kuaishou::ad::AdEnum_AdMonitorType_INNER_LOOP_DEFAULT;
  // front 透出的各因子
  double mix_benefit = p_ad->ad_mix_info.GetMixBenefit();    // 带报价系数
  double client_ad_cpm = p_ad->ad_mix_info.client_ad_cpm;  // 带报价系数
  int64_t cpm = p_ad->ad_mix_info.cpm;  // 迁移后带报价系数，迁移前硬广带固定系数，软广带调控系数和 gpm
  double unify_mix_weight = p_ad->ad_mix_info.unify_mix_weight;  // 报价系数
  if (unify_mix_weight <= 0.0) {
    session_data_->dot_perf->Count(1, "unify_mix_weight_less_zero");
    return;
  }
  // 待求解的 truthful bonus、mix_bid、gpm、cpm
  int64_t unify_cpm = -1;
  double unify_gpm = -1.0;
  double unify_bonus = -1.0;
  double unify_mix_bid = -1.0;
  double unify_bid_score = -1.0;

  // 计算 Bonus
  {
    // 软硬广一致性对齐后
    session_data_->dot_perf->Count(1, "after_consistency_transfer_num");
    unify_cpm = p_rank_result->rank_price_info().cpm();    // 真实的 cpm
    // 软硬广统一用 mix_benefit, hc_gpm 软硬广一致
    unify_mix_bid = p_ad->ad_mix_info.GetMixBenefit();   // 带有报价系数的报价分
    unify_gpm = p_ad->ad_mix_info.hc_gpm;

    // 内外循环统计 bonus 差异，内循环去掉 gpm， 外循环和内粉只去掉 cpm
    if (is_outer_loop_ad) {
      unify_bonus = unify_mix_bid - unify_cpm;
    } else if (is_inner_loop_ad) {
      unify_bonus = unify_mix_bid - unify_cpm - unify_gpm;
    } else {
      // 内粉 bonus = 带有报价系数 cpm + bonus - 真实 cpm
      unify_mix_bid = p_ad->ad_mix_info.cpm + p_ad->ad_mix_info.cpm_bonus;
      unify_bonus = p_ad->ad_mix_info.cpm + p_ad->ad_mix_info.cpm_bonus - unify_cpm;
    }
  }

  // 除掉报价系数传入混排，主要保证 cpm 是去掉报价系数的
  {
    if (unify_mix_weight > 0.0) {
      p_ad->ad_mix_info.cpm = static_cast<int64_t>(cpm / unify_mix_weight);
      p_ad->ad_mix_info.client_ad_cpm = client_ad_cpm / unify_mix_weight;
      p_ad->ad_mix_info.SetMixBenefit(mix_benefit / unify_mix_weight);
    } else {
      // 传入混排是带有报价系数的报价分
      session_data_->dot_perf->Count(1, "unify_mix_weight_less_zero");
    }
  }

  // 填充不带系数的报价分
  unify_bid_score = p_ad->ad_mix_info.GetMixBenefit();
  // 内粉单独处理
  if (!is_outer_loop_ad && !is_inner_loop_ad) {
    unify_bid_score = p_ad->ad_mix_info.cpm + p_ad->ad_mix_info.cpm_bonus;
  }

  // 品牌广告单独处理 : 精排透传的 cpm 有问题，后面 cpm 会强制设置为 900 厘, 将 bonus 置为 0
  if (item->ad_source_type() == ::kuaishou::ad::AdSourceType::BRAND) {
    unify_cpm = p_ad->ad_mix_info.cpm;
    unify_bonus = 0.0;
  }

  // bonus 计算口径更新 = 不带调控系数 bid - 不带调控系数 cpm - gpm
  {
    if (is_outer_loop_ad) {
      unify_bonus = unify_bid_score - unify_cpm;
    } else if (is_inner_loop_ad) {
      unify_bonus = unify_bid_score - unify_cpm - unify_gpm;
    } else {
      // 内粉 bonus = 带有报价系数 cpm + bonus - 真实 cpm
      unify_bonus = unify_bid_score - unify_cpm;
    }
  }

  p_ad->ad_mix_info.internal_unify_cpm = unify_cpm;
  p_ad->ad_mix_info.internal_unify_gpm = unify_gpm;
  p_ad->ad_mix_info.internal_unify_bonus = unify_bonus;
  p_ad->ad_mix_info.internal_unify_mix_bid = unify_mix_bid;
  p_ad->ad_mix_info.internal_unify_bid_score = unify_bid_score;
  // 监控
  std::string ad_info = absl::StrCat(is_soft_ad, "_",
          is_hard_ad, "_", is_outer_loop_ad, "_", is_inner_loop_ad);
  session_data_->dot_perf->Interval(unify_cpm, "unify_cpm", ad_info);   // 真实 cpm
  session_data_->dot_perf->Interval(unify_gpm, "unify_gpm", ad_info);   // 真实 gpm
  session_data_->dot_perf->Interval(unify_bonus, "unify_bonus", ad_info);   // bonus
  session_data_->dot_perf->Interval(unify_mix_bid, "unify_mix_bid", ad_info);   // 带报价系数的报价分
  session_data_->dot_perf->Interval(unify_bid_score, "unify_bid_score", ad_info);   // 不带报价系数的报价分
  session_data_->dot_perf->Interval(unify_mix_weight, "unify_mix_weight", ad_info);   // 报价系数
}

void FillOtherMixInfoMixer::FillRank(RankAdCommon* p_ad, const StyleInfoItem* p_style_info) {
  auto* item = p_ad->GetResult();
  if (item == nullptr) {
    return;
  }
  auto* p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result == nullptr) {
    return;
  }
  auto &rank_result = *p_rank_result;
  if (!session_data_->get_ad_mixed_info_without_ueq()) {
    p_ad->ad_mix_info.ueq = rank_result.rank_ueq_info().ueq();
  }
  p_ad->ad_mix_info.conv_est_duration = rank_result.ad_rank_trans_info().ad_price_extra_duration();
  p_ad->ad_mix_info.eco_score = rank_result.rank_price_info().eco_score();
  p_ad->ad_mix_info.ctcvr_percentile = rank_result.rank_price_info().ctcvr_percentile();
  p_ad->ad_mix_info.record_gsp_price = rank_result.ad_rank_trans_info().record_gsp_price();
  //  精排和竞价部分数据
  p_ad->ad_mix_info.unify_cvr = rank_result.rank_rank_info().unify_cvr();
  p_ad->ad_mix_info.unify_ctr = rank_result.rank_rank_info().unify_ctr();
  p_ad->ad_mix_info.sctr = rank_result.rank_rank_info().unify_sctr_value();
  p_ad->ad_mix_info.sctr_start = rank_result.rank_rank_info().sctr_start();
  p_ad->ad_mix_info.sctr_end = rank_result.rank_rank_info().sctr_end();
  p_ad->ad_mix_info.cpa_bid = item->ad_deliver_info().ad_base_info().cpa_bid();
  if (p_style_info && item->ad_source_type() == kuaishou::ad::FANS_TOP_V2 &&
      p_ad->bid_strategy() != kuaishou::ad::AdEnum_BidStrategy_SMART_BID_STRATEGY) {
    p_ad->ad_mix_info.cpa_bid = p_style_info->unit().bid();
  }
  p_ad->ad_mix_info.auto_cpa_bid = rank_result.rank_bid_info().ranking_auto_cpa_bid();
  p_ad->ad_mix_info.all_ctr = rank_result.rank_rank_info().all_ctr();
  p_ad->ad_mix_info.cvr_start = rank_result.rank_rank_info().cvr_start();
  p_ad->ad_mix_info.cvr_end = rank_result.rank_rank_info().cvr_end();
  p_ad->ad_mix_info.ctr_start = rank_result.rank_rank_info().ctr_start();
  p_ad->ad_mix_info.ctr_end = rank_result.rank_rank_info().ctr_end();
  p_ad->ad_mix_info.ctr_factor = rank_result.ad_rank_trans_info().ctr_factor();
  p_ad->ad_mix_info.cvr_factor = rank_result.ad_rank_trans_info().cvr_factor();
  p_ad->ad_mix_info.origin_c1_conv_rate = rank_result.rank_rank_info().origin_c1_conv_rate();
  p_ad->ad_mix_info.origin_c1_lps_rate = rank_result.rank_rank_info().origin_c1_lps_rate();
  p_ad->ad_mix_info.next_benifit = rank_result.rank_price_info().next_benifit();
  p_ad->ad_mix_info.next_cpm = rank_result.rank_price_info().next_cpm();
  p_ad->ad_mix_info.rank_benifit = rank_result.rank_price_info().rank_benifit();
  p_ad->ad_mix_info.is_brand = item->ad_deliver_info().ad_base_info().fanstop_ext_info().is_brand_order();
  auto fanstop_category = item->ad_deliver_info().ad_base_info().fanstop_ext_info().fanstop_category();
  p_ad->ad_mix_info.is_brand = (fanstop_category == kuaishou::ad::AdEnum::CATEGORY_BRAND_FANSTOP ||
      fanstop_category == kuaishou::ad::AdEnum::CATEGORY_SPEED_FANSTOP_PHOTO);
  p_ad->ad_mix_info.payer_id =
      item->ad_deliver_info().ad_base_info().fanstop_ext_info().payer_id();
  if (p_style_info &&
        p_style_info->campaign_fanstop_support_info().payer_id() > 0 &&
        (item->ad_source_type() == kuaishou::ad::FANS_TOP_V2
        || p_style_info->unit_fanstop_support_info().fans_top_liked_biz_type() ==
        kuaishou::ad::AdEnum::B_FANS_TOP)) {
    p_ad->ad_mix_info.payer_id = p_style_info->campaign_fanstop_support_info().payer_id();
  }
  p_ad->ad_mix_info.gpm = rank_result.rank_price_info().gpm();
  p_ad->ad_mix_info.gpm_ratio = rank_result.rank_price_info().gpm_ratio();
  p_ad->ad_mix_info.cpm_ratio = rank_result.rank_price_info().cpm_ratio();
  p_ad->ad_mix_info.origin_cpm = rank_result.rank_price_info().origin_cpm();
  p_ad->ad_mix_info.hc_score_other =
      p_ad->ad_mix_info.hc_score - p_ad->ad_mix_info.gpm * p_ad->ad_mix_info.gpm_ratio;
  p_ad->ad_mix_info.hc_experience = rank_result.ad_rank_trans_info().hc_experience();
  p_ad->ad_mix_info.hc_gpm = rank_result.ad_rank_trans_info().hc_gpm();
  p_ad->ad_mix_info.ad_monitor_type = GetAdMonitorType(*item);
  if (rank_result.ad_rank_trans_info().has_photo_quality_score_new()) {
    p_ad->ad_mix_info.photo_quality_score =
      rank_result.ad_rank_trans_info().photo_quality_score_new();
  } else {
    p_ad->ad_mix_info.photo_quality_score = 0;
  }
  p_ad->ad_mix_info.reco_vtr = rank_result.predict_origin_info().reco_vtr();
  if (rank_result.rank_rank_info().cvr_value() > 0.0) {
    p_ad->ad_mix_info.unify_cvr = rank_result.rank_rank_info().cvr_value();
  }
  if (rank_result.rank_rank_info().ctr_value() > 0.0) {
    p_ad->ad_mix_info.unify_ctr = rank_result.rank_rank_info().ctr_value();
  }
  if (!session_data_->get_ad_mixed_info_without_ueq()) {
    p_ad->ad_mix_info.calc_ltr_score = rank_result.rank_ueq_info().calc_ltr_score();
    p_ad->ad_mix_info.play3s = rank_result.predict_origin_info().play3s();
    p_ad->ad_mix_info.ntr = rank_result.predict_origin_info().ntr();
    p_ad->ad_mix_info.ad_play_time = rank_result.rank_ueq_info().playtime_ad();
  }
  bool enable_new_creative = (item->ad_deliver_info().ad_base_info().new_creative_tag() &
      static_cast<int>(ks::ad_server::NewCreative::NewModel)) > 0;
  if (item->ad_source_type() == ::kuaishou::ad::AdSourceType::ADX) {
    enable_new_creative = false;
  }
  p_ad->ad_mix_info.enable_new_creative = enable_new_creative;
  if (session_data_->get_enable_mix_unify_gpm_pass()) {
    p_ad->ad_mix_info.mix_unify_gpm =
        rank_result.rank_price_info().mix_unify_gpm() * session_data_->get_mix_unify_gpm_ratio();
    p_ad->ad_mix_info.mix_unify_gpm_ratio = session_data_->get_mix_unify_gpm_ratio();
  }
  p_ad->ad_mix_info.sort_tag = rank_result.rank_price_info().sort_tag();
  p_ad->ad_mix_info.cpm_thr = rank_result.rank_reserve_info().cpm_thr();

  auto item_type = item->ad_deliver_info().ad_base_info().item_type();
  double inner_qcpx_cvr_uplift_ratio = 1.0;
  if (item_type == kuaishou::ad::AdEnum::ITEM_PHOTO) {
    inner_qcpx_cvr_uplift_ratio = rank_result.ad_rank_trans_info().inner_qcpx_photo_cvr_uplift_ratio();
  } else if (item_type == kuaishou::ad::AdEnum::ITEM_LIVE ||
              item_type == kuaishou::ad::AdEnum::ITEM_PHOTO_TO_LIVE)  {
    inner_qcpx_cvr_uplift_ratio = rank_result.ad_rank_trans_info().inner_qcpx_live_cvr_uplift_ratio();
  }
  p_ad->ad_mix_info.inner_qcpx_cvr_uplift_ratio = std::max(inner_qcpx_cvr_uplift_ratio, 1.0);

  // 品牌广告
  if (item->ad_source_type() == ::kuaishou::ad::AdSourceType::BRAND) {
    if (FrontKconfUtil::brandTempCpm() != 0) {
      p_ad->ad_mix_info.cpm = FrontKconfUtil::brandTempCpm();
    }
    if (FrontKconfUtil::brandTempUeq() != 0) {
      p_ad->ad_mix_info.ueq = FrontKconfUtil::brandTempUeq();
    }
    if (FrontKconfUtil::brandTempltr() != 0) {
      p_ad->ad_mix_info.calc_ltr_score = FrontKconfUtil::brandTempltr();
    }
    p_ad->ad_mix_info.SetMixBenefit(FrontKconfUtil::brandMockMixBenefit());
    p_ad->ad_mix_info.cpm = FrontKconfUtil::brandMockCpm();
    p_ad->ad_mix_info.client_ad_cpm = FrontKconfUtil::brandMockMixBenefit();
    p_ad->ad_mix_info.unify_mix_weight = 1;
  }
}

bool FillOtherMixInfoMixer::ProcessInner(ks::platform::AddibleRecoContextInterface* context) {
  // step 1: 初始化
  if (!LocalInitialize(context)) {
    return false;
  }
  auto* item_table = context->GetOrInsertDataTable(GetTableName());
  if (!item_table) {
    return false;
  }
  std::string exp_tag = SPDM_soft_hard_union_exp_tag(session_data_->get_spdm_ctx());
  auto* ad_common_ptr_attr = item_table->GetOrInsertAttr("ad_common_ptr");
  auto& item_list = item_table->GetCommonRecoResults();
  size_t adjust_bonus_item_index = -1;
  if (enable_move_mix_rank_input_hist_page_info_) {
    adjust_bonus_item_index = GetAdjustBonusItemIndex(context, item_table);
  }
  if (SPDM_enable_w5_add_3_percent_bonus(session_data_->get_spdm_ctx())) {
    JudgeAdd3PercentBonusGroupByLlsid(context, item_table);
  }
  RankAdCommon* pre_ad = nullptr;
  for (auto iter = item_list.begin(); iter != item_list.end(); iter++) {
    const auto& item = *iter;
    auto raw_ad = context->GetExtraItemAttr(item, ad_common_ptr_attr);
    if (!raw_ad) {continue;}
    RankAdCommon* p_ad = *(boost::any_cast<std::shared_ptr<RankAdCommon*>>(*raw_ad));
    if (!p_ad) {continue;}
    auto idx = item.GetAttrIndex();
    if (enable_move_mix_rank_input_hist_page_info_ && adjust_bonus_item_index == idx) {
      enable_change_bonus_with_last_llsid_mix_score_ = true;
    } else {
      enable_change_bonus_with_last_llsid_mix_score_ = false;
    }
    const StyleInfoItem* p_style_info_item = nullptr;
    auto style_info_iter = session_data_->get_style_info_resp()->style_info().find(p_ad->creative_id());
    if (style_info_iter != session_data_->get_style_info_resp()->style_info().end()) {
      p_style_info_item = &(style_info_iter->second);
    }

    FillPvInfo(p_ad);
    FillBaseInfo(p_ad, p_style_info_item);
    FillRank(p_ad, p_style_info_item);
    FillMtbInfo(p_ad);
    FillFeedExplore(p_ad);
    FillInnerExplore(p_ad, pre_ad);
    if (session_data_->get_pos_manager().IsInnerExplore()) {
      CalcInnerAdBizValue(p_ad, pre_ad);
    }
    FillUnifyInfo(p_ad);
    pre_ad = p_ad;
  }

  if (enable_mix_bonus_fixed_ratio_rct_experiment_) {
    int32_t tag = get_mix_bonus_rank_ratio_tag();
    session_data_->set_mix_bonus_fixed_ratio_tag(tag);
  }

  return true;
}
int32_t FillOtherMixInfoMixer::get_mix_bonus_rank_ratio_tag() {
  if (!enable_bonus_fixed_ratio_experiment_v2_ || bonus_fixed_ratio_v2_ < 0 || bonus_fixed_ratio_v2_ > 1)
    return -1;
  return static_cast<int32_t>(std::lround(bonus_fixed_ratio_v2_ * 10));
}
}  // namespace front_server
}  // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FillOtherMixInfoMixer,
    ::ks::front_server::FillOtherMixInfoMixer);
