#pragma once
#include <string>
#include <vector>
#include <unordered_set>
#include <utility>
#include <memory>

#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_forward_index.pb.h"
#include "teams/ad/front_server/engine/server/init.h"
#include "teams/ad/front_server/engine/context_data/ad_common.h"
#include "teams/ad/front_server/engine/context_data/context_data.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"


namespace ks {
namespace front_server {

class FillOtherMixInfoMixer : public ::ks::platform::CommonRecoBaseMixer {
 public:
  FillOtherMixInfoMixer();
  ~FillOtherMixInfoMixer() = default;
  // dragon 接口
  void Mix(ks::platform::AddibleRecoContextInterface* context) override;

 private:
  bool InitProcessor() override;
  bool LocalInitialize(ks::platform::AddibleRecoContextInterface* context);
  void FillPvInfo(RankAdCommon* p_ad);
  void FillMixSourceType(RankAdCommon* p_ad, const StyleInfoItem* p_style_info);
  void AdPriorityProtect(RankAdCommon* p_ad);
  void FillBaseInfo(RankAdCommon* p_ad, const StyleInfoItem* p_style_info);
  void FillRank(RankAdCommon* p_ad, const StyleInfoItem* p_style_info);
  void FillUnifyInfo(RankAdCommon* p_ad);
  void FillMtbInfo(RankAdCommon* p_ad);
  size_t GetAdjustBonusItemIndex(ks::platform::AddibleRecoContextInterface* context,
                                  ks::platform::AttrTable* item_table);
  void JudgeAdd3PercentBonusGroupByLlsid(ks::platform::AddibleRecoContextInterface* context,
                                          ks::platform::AttrTable* item_table);
  void FillUnifyBonusInfo(RankAdCommon* p_ad);
  void FillFeedExplore(RankAdCommon* p_ad);
  void FillInnerExplore(RankAdCommon* p_ad, const RankAdCommon* pre_ad);
  kuaishou::ad::AdEnum_AdTpAudienceType GetPaidAudienctType(const StyleInfoItem& style_info);
  void FillPaidAudienctType(RankAdCommon* p_ad, const StyleInfoItem* p_style_info);
  void CalcInnerAdBizValue(RankAdCommon* p_ad, const RankAdCommon* pre_ad);
  void FillExploreExperience(RankAdCommon* p_ad);

 protected:
  bool ProcessInner(ks::platform::AddibleRecoContextInterface* context);
  int32_t get_mix_bonus_rank_ratio_tag();

 private:
  ContextData *session_data_;  // 请求上下文

  std::vector<UnifyBonusExpConf> unify_bonus_exp_conf_;
  int ad_priority_num = 0;
  bool enable_mtb_unify_gpm_cali_ = false;
  bool enable_add_multi_head_mix_gpm_live_ = false;
  bool enable_add_multi_head_mix_gpm_photo_ = false;
  bool disable_multi_head_mix_gpm_outer_ = false;
  bool enable_mix_unify_gpm_min_value_ = false;
  bool enable_mtb_v2_bonus_ratio_ = false;
  bool enable_mtb_v2_hc_gpm_using_max_ = false;
  bool enable_fill_unify_bonus_info_inner_explore_ = false;
  bool enable_replace_mix_bonus_with_cpm_inner_explore_ = false;
  double replace_mix_bonus_with_cpm_ratio_inner_explore_ = 0.15;
  bool enable_mtb_v2_inner_bonus_include_hc_gpm_ = false;
  double mtb_v2_bonus_ratio_ = 1.0;
  bool enable_mix_bonus_adjust_ = false;
  double mix_unify_gpm_min_value_ = 0.0;
  double mix_bonus_adjust_enhance_ratio_ = 0.0;
  double mix_unify_score_ratio_ = 1.0;
  double mix_unify_score_ratio_a_ = 1.0;
  double mix_unify_score_ratio_b_ = 1.0;
  double mix_unify_cpm_ratio_ = 1.0;
  double mix_unify_cpm_ratio_inner_ = 1.0;
  double mix_unify_cpm_ratio_outer_ = 1.0;
  double mix_unify_gpm_ratio_ = 1.0;
  double mix_unify_bonus_ratio_ = 1.0;
  ks::front_server::kconf::UnifyGpmCaliConf_CalibrationParams unify_gpm_cali_params_;
  bool enable_inner_explore_mix_ratio = false;
  bool enable_inner_explore_mix_cpm_opt = false;
  bool enable_inner_explore_mix_hc_opt = false;
  std::vector<std::pair<double, double>> inner_uplift_cpm_conf_;
  double inner_explore_mix_soft_ratio = 1.0;
  double inner_explore_mix_hard_ratio = 1.0;
  bool enable_inner_explore_mix_ratio_opt_ = false;
  double inner_explore_mix_ratio_soft_live_ = 1.0;
  double inner_explore_mix_ratio_soft_photo_ = 1.0;
  double inner_explore_mix_ratio_hard_live_ = 1.0;
  double inner_explore_mix_ratio_hard_photo_ = 1.0;
  double mix_unify_cpm_ratio_v2 = 1.0;
  double w5_extra_3_percent_bonus_weight_gamora_ = 1.0;
  double w5_extra_3_percent_bonus_weight_nebula_ = 1.0;
  bool enable_bonus_balence_stategy_ = false;
  bool enable_bonus_sep_inner_outer_ = false;
  double cpm_bonus_cali_ratio_outer_ = 1.0;
  double cpm_bonus_cali_extra_ratio_outer_ = 1.0;
  double cpm_bonus_cali_ratio_inner_ = 1.0;
  double cpm_bonus_cali_extra_ratio_inner_ = 1.0;
  double cpm_bonus_cali_ratio_ = 1.0;
  double cpm_bonus_cali_extra_ratio_ = 1.0;
  double w5_extra_3_percent_bonus_cpm_lower_bound_ = 0.0;
  double w5_extra_3_percent_bonus_cpm_upper_bound_ = 0.0;
  bool enable_inner_explore_fairness_rerank_ {false};
  bool enale_inner_explore_live_score_ = false;
  bool enale_inner_explore_live_soft_weight_ = false;
  bool enable_inner_explore_live_item_as_p2l_ = false;
  bool enable_mix_inner_explore_relative_hc_ = false;
  double mix_ad_fans_cpm_boost_weight_ = 1.0;
  double mix_auction_top_fans_weight_ = 1.0;
  double mix_ad_dsp_eco_weight_ = 1.0;
  double mix_ad_top_fans_eco_weight_ = 1.0;
  double mix_ad_live_score_alpha_ = 1.0;
  double mix_ad_live_score_bias_ = 0.0;
  double mix_ad_live_score_pow_ = 1.0;
  ks::front_server::kconf::ExploreLiveScoreConf::ExploreLiveScoreParam explore_live_score_param_;
  bool enable_change_bonus_with_last_llsid_mix_score_ = false;
  bool enable_adjust_bonus_with_mix_rank_input_use_two_switch_ = false;
  double adjust_bonus_with_mix_rank_input_weight_ = 1.0;
  double minus_bonus_with_mix_rank_input_weight_ = 1.0;
  double bonus_weight_for_rb_thr_to_add_bonus_ = 1.0;
  bool enable_move_mix_rank_input_hist_page_info_ = false;
  bool enable_close_unify_cpm_add_customer_hc_ = false;
  bool enable_close_unify_cpm_divide_sctr_ratio_ = false;
  bool enable_fix_mix_unify_cpm_init_ = false;
  bool enable_unify_bonus_add_3_percent_bonus_groupBy_llsid_ = false;
  bool enable_mix_fctr_ = false;
  bool enable_inner_explore_mix_fctr_ = false;
  std::string inner_explore_fctr_exp_tag_ = "base";
  bool enable_set_negative_bonus_to_zero_ = false;
  std::string mix_fctr_model_exp_tag_;
  bool disable_fctr_live_bonus_ = false;
  bool enable_nogap_filter_by_cpm_ = false;
  bool enable_nogap_filter_by_wgroup_ = false;
  double nogap_cpm_thr_ = 0.0;
  bool enable_nogap_filter_by_jumpout_rate_ = false;
  double nogap_jumpout_rate_thr_ = 0.0;
  bool enable_fix_mix_bonus_ = false;
  double fix_mix_bonus_hc_ratio_ = 1.0;
  bool enable_feed_explore_mix_price_adjust_ratio_ {false};
  bool enable_feed_explore_mix_experience_value_ {false};
  bool is_feed_explore_req_ {false};
  bool enable_inner_explore_mix_experience_value_ {false};
  bool enable_explore_post_gpm_ {false};
  std::string explore_mix_experience_exp_tag_;
  std::shared_ptr<ks::ad_base::kconf::ProtoKconf<kconf::ExploreExperienceConf>>
      explore_experience_conf_ {nullptr};
  bool enable_feed_mix_score_new_{false};
  double feed_mix_score_user_tag_weight_{0.0};
  bool enable_client_ai_use_fctr_bonus_ = false;
  bool enable_bonus_fixed_ratio_experiment_v2_ = false;
  bool enable_mix_bonus_fixed_ratio_rct_experiment_ = false;
  bool enable_inner_multi_queue_bonus_reallocation_ = false;
  double bonus_fixed_ratio_v2_ = 0.0;
  bool enable_bonus_strategy_for_mix_rb_cpm_ = false;
  double ori_bonus_ratio_for_mix_rb_cpm_ = 2.0 / 3.0;
  double new_bonus_ratio_for_mix_rb_cpm_ = 0.05;
  bool disable_fctr_fanstop_bonus_ = false;
  bool enable_mix_rank_truncate = false;
  int64_t mix_rank_truncate_max = 10000000000;
  ks::front_server::kconf::FeedMixScoreConf::FeedMixScoreParam feed_mix_score_param_;
  std::string unify_bonus_exp_tag_;
  bool enable_unify_bonus_exp_v2_ = false;
  bool disable_inner_multi_queue_flow_ = false;
  bool enable_skip_fctr_for_exp_ = false;
  bool enable_excycle_skip_pass_tag_ = false;
  bool enable_excycle_skip_pass_tag_v2_ = false;
  bool enable_inner_explore_mix_price_adjust_ratio_ = false;
  bool enable_fix_set_mix_price_adjust_ratio_ = false;
  double inner_explore_mix_price_adjust_ratio_lower_bound_ = 0.2;
  bool enable_fanstop_bonus_adjust_;
  double fanstop_bonus_adjust_ratio_;
  std::vector<UnifyBonusExpConf> bonus_reallocation_exp_conf_;
  std::string bonus_reallocation_exp_tag_;
  bool enable_bonus_reallocation_exp_ = false;
  bool enable_fanstop_bonus_adjust_v2;
  double inner_no_fanstop_bonus_adjust_ratio_ = 1.0;
  // [jiangqiqi03] bonus 按照 cpm 和 用户等级分桶实验
  bool enable_bonus_allocation_cpm_user_exp_ = false;
  std::string bonus_allocation_cpm_user_exp_tag_;
  std::vector<AllocByUserCpmBonusExpConf> alloc_by_user_cpm_bonus_exp_confs_;

  // [zhangyuxiang05] 异常 unify_bonus 修复相关
  bool enable_calib_abnormal_unify_bonus_by_thr_ = false;
  bool enable_calib_abnormal_unify_bonus_by_cpm_ = false;
  bool enable_calib_abnormal_unify_bonus_by_max_cpm_and_thr_ = false;
  double calibration_weight_for_abnormal_unify_bonus_gamora1_ = 1.0;
  double calibration_weight_for_abnormal_unify_bonus_nebula1_ = 1.0;
  double calibration_weight_for_abnormal_unify_bonus_gamora2_ = 1.0;
  double calibration_weight_for_abnormal_unify_bonus_nebula2_ = 1.0;
  double calibration_weight_for_abnormal_unify_bonus_gamora3_ = 1.0;
  double calibration_weight_for_abnormal_unify_bonus_nebula3_ = 1.0;
  double ad_pv_bonus_frac_thr_gamora_ = 32500.0;
  double ad_pv_bonus_frac_thr_nebula_ = 29500.0;
  double cpm_times_thr_gamora_ = 1.0;
  double cpm_times_thr_nebula_ = 1.0;
  // [zhangyuxiang05] bonus 留反实验实验流量标记
  bool enable_ad_unify_bonus_weight_exp_ = false;
  double ad_unify_bonus_weight_gamora_ = 1.0;
  double ad_unify_bonus_weight_nebula_ = 1.0;
  double calibration_weight_for_abnormal_unify_bonus_gamora4_ = 1.0;
  double calibration_weight_for_abnormal_unify_bonus_nebula4_ = 1.0;
};

}  // namespace front_server
}  // namespace ks
