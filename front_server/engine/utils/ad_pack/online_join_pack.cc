#include "teams/ad/front_server/engine/utils/ad_pack/online_join_pack.h"

#include <vector>
#include <utility>
#include <string>
#include <set>

#include "base/common/map_util-inl.h"
#include "infra/utility/src/utility/encrypted_id_cipher.h"
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_base/src/log_record/util.h"
#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/ad_base/src/common/app_version_compare.h"
#include "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_simplify_trace_log.pb.h"
#include "teams/ad/engine_base/cache_loader/author_gmv_interval_cache.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/front_server/util/adx/adx_common_util.h"
#include "teams/ad/front_server/util/kconf/kconf.h"
#include "teams/ad/front_server/util/spdm/spdm_switches.h"
#include "teams/ad/front_server/util/utility/front_logging.h"
#include "teams/ad/front_server/util/utility/utils.h"
#include "third_party/abseil/absl/strings/substitute.h"
#include "teams/ad/front_server/util/merchant_util/merchant_style_util.h"
#include "teams/ad/front_server/bg_task/smart_compute_power/smart_compute_power_inner.h"
#include "teams/ad/engine_base/p2p_cache_loader/universe_dark_ad_expect_control_p2p.h"
#include "teams/ad/engine_base/p2p_cache_loader/industry_playlet_sdpa.h"

namespace ks {
namespace front_server {

using namespace google::protobuf;  // NOLINT
void OnlineJoinPack::Process(
    ContextData* context, AdResult* ad_result, OnlineJoinParams* original_online_join_pb,
    const RankAdCommon* ad_common) {
  if (!Initialize(context, ad_result, original_online_join_pb, ad_common)) {
    return;
  }

  if (!GetRankResult()) {
    // 获取 rank_result 失败，直接返回
    return;
  }

  // splash adx 单独填充 bonus 相关字段
  if (need_online_join_pack_in_splash_adx_) {
    SetSplashAdxBonusData();
    return;
  }

  // 公共逻辑，各请求的独立判断逻辑不要加在这个函数里面
  SetCommonData();

  // 不同请求独立的逻辑
  SetIndependentData();

  // 添加一个 online join 的裁剪
  CompressOnlineJoin();

  // 统计打点
  Stats();
}

bool OnlineJoinPack::Initialize(ContextData* context, AdResult* ad_result,
                                OnlineJoinParams* original_online_join_pb,
                                const RankAdCommon* ad_common) {
  if (context == nullptr || ad_result == nullptr || original_online_join_pb == nullptr ||
      context->get_ad_request() == nullptr) {
    LOG(WARNING) << "Fail to do initialization, because input is invalid.";
    return false;
  }

  session_data_ = context;
  result_ = ad_result;
  online_join_pb_ = original_online_join_pb;
  ad_request_ = session_data_->mutable_ad_request();
  const auto& style_info_map = session_data_->get_style_info_resp()->style_info();
  int64_t creative_id = result_->ad_deliver_info().ad_base_info().creative_id();
  const auto& iter = style_info_map.find(creative_id);
  need_online_join_pack_in_splash_adx_ = false;
  if (iter == style_info_map.end()) {
    if (session_data_->GetUnifyScene() == FrontServerScene::SPLASH &&
        session_data_->get_ad_request()->universe_ad_request_info().splash_req_info().is_realtime() &&
        SPDM_enableSplashAdxPackOnlineJoinParams() && ad_result->ad_source_type() == kuaishou::ad::ADX) {
      ad_deliver_info_ = result_->mutable_ad_deliver_info();
      ad_base_info_ = result_->mutable_ad_deliver_info()->mutable_ad_base_info();
      need_online_join_pack_in_splash_adx_ = true;
      return true;
    }
    return false;
  }
  style_info_ = &(iter->second);
  target_bid_ = result_->mutable_target_bid();
  ad_deliver_info_ = result_->mutable_ad_deliver_info();
  ad_base_info_ = result_->mutable_ad_deliver_info()->mutable_ad_base_info();
  online_join_params_transparent_ =
      result_->mutable_ad_deliver_info()->mutable_online_join_params_transparent();
  ad_common_ = ad_common;
  brand_online_join_pb_.Clear();
  enable_mix_fctr_ = SPDM_enable_mix_fctr(session_data_->get_spdm_ctx());
  enable_inner_explore_mix_fctr_ = SPDM_enable_inner_explore_mix_fctr(session_data_->get_spdm_ctx());

  enable_add_rerank_req_info_ = SPDM_enable_add_rerank_req_info(session_data_->get_spdm_ctx());
  enable_add_rerank_req_info_live_use_photo_ =
    SPDM_enable_add_rerank_req_info_live_use_photo(session_data_->get_spdm_ctx());
  enable_calc_client_ai_rerank_score_adrank_ =
    SPDM_enable_calc_client_ai_rerank_score_adrank(session_data_->get_spdm_ctx());
  enable_photo_rerank_req_ = SPDM_enable_photo_rerank_req(session_data_->get_spdm_ctx());
  enable_control_prerank_ = SPDM_enable_control_prerank(session_data_->get_spdm_ctx());
  enable_client_ai_rerank_req_info_tag_ =
    SPDM_enable_client_ai_rerank_req_info_tag(
        session_data_->get_spdm_ctx());
  mix_mtb_bid_frac_ad_cpm_nebula_ = SPDM_mix_mtb_bid_frac_ad_cpm_nebula(session_data_->get_spdm_ctx());
  mix_mtb_bid_frac_ad_cpm_gamora_ = SPDM_mix_mtb_bid_frac_ad_cpm_gamora(session_data_->get_spdm_ctx());
  return true;
}

void OnlineJoinPack::SetCommonData() {
  SetBehaviorIntent();
  SetCorePopulation();
  SetTransparentInfo();
  SetStyleData();
  SetLiveInfo();
  SetDataFromOnlineJoinParamsTransparent();
  SetRankResultTransparent();
  SetAdDeliverInfoData();
  SetAdBaseInfoData();
  SetInfoFromContextData();
  SetTargetBidData();
  SetAdDataV2Data();
  SetAdxData();
  SetMerchantInfo();
  SetPecCouponInfo();
  SetEnvInfo();
  // 设置软硬广队列
  SetAdQueueType();
  SetOtherData();
  SetExploreInfo();
  SetRequestData();
  SetStidInfo();
  // 落地页组件信息
  if (SPDM_enableLpComponent3()) {
    SetLpComponent();
  }
  SetStrategyCrowdTag();
  SetSkipNewUserTag();
  SetSmartComputeScore();
  SetForceExploreData();
  SetDncExploreData();
  SetDncLtvData();
  SetEECVRData();
  SetAggrCardInfo();
  if (SPDM_enable_common_feed_card(session_data_->get_spdm_ctx())) {
    SetCommonCardInfo();
  }
  if (SPDM_enableSetImInfo()) {
    SetImInfo();
  }
  if (session_data_->get_enable_set_adjust_price_record()) {
    SetAdjustPriceRecord();
  }
  // 外循环破圈 EE
  SetOuterloopEEData();
  if (SPDM_enableBonusDotPerf()) {
    std::string exp_tag = SPDM_soft_hard_union_exp_tag(session_data_->get_spdm_ctx());
    session_data_->dot_perf->Interval(online_join_pb_->hc_shift_ratio() * 1000,
      "hc_shift_ratio", exp_tag, absl::StrCat(online_join_pb_->ad_queue_type()));
    session_data_->dot_perf->Interval(online_join_pb_->bonus_cpm(),
      "bonus_cpm", exp_tag, absl::StrCat(online_join_pb_->ad_queue_type()));
  }
}

void OnlineJoinPack::SetSplashAdxBonusData() {
  bool is_bonus = rank_result_->rank_price_info().rank_benifit() - rank_result_->rank_price_info().bonus_cpm()
                  < rank_result_->rank_price_info().next_benifit();
  online_join_pb_->set_is_bonus(is_bonus);
  online_join_pb_->set_bonus_cpm(ad_deliver_info_->bonus_cpm());
  online_join_pb_->set_bonus_cpm_tag(ad_deliver_info_->bonus_cpm_tag());
  online_join_pb_->set_bonus_cpm_project_id(ad_deliver_info_->bonus_cpm_project_id());
  online_join_pb_->set_is_black_bonus_project(ad_deliver_info_->is_black_bonus_project());
  session_data_->dot_perf->Count(1, "online_join.set_splash_adx_bonus_data");
}

void OnlineJoinPack::CompressOnlineJoin() {
  ClearPbDefaultField(online_join_pb_, true);
  auto online_join_params_white_list = FrontKconfUtil::adLogFullOnlineJoinParamsWhiteList();
  char ret[1000] = {0};
  auto underscore_to_hump = [&](const std::string &src) -> std::string {
    // char *ret = new char[src.length() + 1];
    memset(ret, 0, 1000);
    const char *cstr = src.c_str();
    for (int i = 0, j = 0; i < src.length() && i < 1000; i++) {
      // 简单点，不考虑头尾是 _ 和连续 _ 的情况，这种情况自行修改格式
      if (src.at(i) == '_' && i + 1 < src.length()) {
        ret[j++] = absl::ascii_toupper(cstr[++i]);
      } else {
        ret[j++] = cstr[i];
      }
    }
    std::string ret_str(ret);
    // if (ret != nullptr) delete []ret;
    return std::move(ret_str);
  };
  const Reflection *ref = online_join_pb_->GetReflection();
  if (ref == nullptr) return;
  typedef std::vector<const FieldDescriptor *> FieldList;
  FieldList field_list;
  ref->ListFields(*online_join_pb_, &field_list);
  bool clear_field = false;
  for (FieldList::const_iterator it = field_list.begin(); it != field_list.end(); ++it) {
    clear_field = false;
    const FieldDescriptor *field = *it;
    // 2 不在白名单的进行清理
    if (online_join_params_white_list->count(underscore_to_hump(field->name())) <= 0) {
      // debug_str2 = absl::StrCat(debug_str2,",", underscore_to_hump(field->name()));
      ref->ClearField(online_join_pb_, field);
    }
  }
}

void OnlineJoinPack::SetStrategyCrowdTag() {
  if (session_data_->TabName() == "universe" || session_data_->TabName() == "splash") {
    return;
  }
  // 只保留磁力金牛广告
  const auto& account_type = result_->ad_deliver_info().ad_base_info().account_type();
  if (account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE &&
      account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP) {
    return;
  }
  // author_id 无效的不填充
  auto author_id = result_->ad_deliver_info().ad_base_info().author_id();
  if (author_id <= 0) {
    return;
  }
  const auto& strategy_crowd_list = session_data_->get_ad_request()->ad_user_info().strategy_crowd_info();
  for (const auto& strategy_crowd : strategy_crowd_list) {
    if (strategy_crowd.author_id() == author_id) {
      online_join_pb_->mutable_hit_crowd_tag()->CopyFrom(strategy_crowd.tag());
      break;
    }
  }
}

void OnlineJoinPack::SetLpComponentForSku() {
  std::string page_component_str = style_info_->magic_site_sku_page().page_component();
  if (page_component_str.empty() || page_component_str == "[]") {
    return;
  }
  base::Json page_component_js(base::StringToJson(page_component_str));
  if (page_component_js.IsArray()) {
    bool has_form = false, has_weixin = false, has_service = false;
    int64_t form_sub_type = -1, weixin_sub_type = -1, service_sub_type = -1;
    for (auto iter = page_component_js.array_begin();
              iter != page_component_js.array_end(); ++iter) {
      int64_t type = (*iter)->GetInt("type", -1);
      int64_t sub_type = (*iter)->GetInt("subType", -1);
      if (type == 14) {
        has_form = true;
        form_sub_type = sub_type;
      }
      if (type == 15) {
        has_weixin = true;
        weixin_sub_type = sub_type;
      }
      if (type == 11) {
        has_service = true;
        service_sub_type = sub_type;
      }
    }
    if (has_form && has_weixin && has_service) {
      online_join_pb_->set_view_component_type(-5);
      online_join_pb_->set_sub_type(-5);
    } else if (has_form && has_weixin) {
      online_join_pb_->set_view_component_type(-4);
      online_join_pb_->set_sub_type(-4);
    } else if (has_weixin && has_service) {
      online_join_pb_->set_view_component_type(-3);
      online_join_pb_->set_sub_type(-3);
    } else if (has_form && has_service) {
      online_join_pb_->set_view_component_type(-2);
      online_join_pb_->set_sub_type(-2);
    } else if (has_form) {
      online_join_pb_->set_view_component_type(14);
      online_join_pb_->set_sub_type(form_sub_type);
    } else if (has_weixin) {
      online_join_pb_->set_view_component_type(15);
      online_join_pb_->set_sub_type(form_sub_type);
    } else if (has_service) {
      online_join_pb_->set_view_component_type(11);
      online_join_pb_->set_sub_type(form_sub_type);
    } else {
      online_join_pb_->set_view_component_type(-1);
      online_join_pb_->set_sub_type(-1);
    }
  }
}

void OnlineJoinPack::SetLpComponent() {
  std::string page_component_str = style_info_->magic_site_page().page_component();
  if (page_component_str.empty() || page_component_str == "[]") {
    return;
  }
  base::Json page_component_js(base::StringToJson(page_component_str));
  if (page_component_js.IsArray()) {
    bool has_form = false, has_weixin = false, has_service = false;
    int64_t form_sub_type = -1, weixin_sub_type = -1, service_sub_type = -1;
    for (auto iter = page_component_js.array_begin();
              iter != page_component_js.array_end(); ++iter) {
      int64_t type = (*iter)->GetInt("type", -1);
      int64_t sub_type = (*iter)->GetInt("subType", -1);
      if (type == 14) {
        has_form = true;
        form_sub_type = sub_type;
      }
      if (type == 15) {
        has_weixin = true;
        weixin_sub_type = sub_type;
      }
      if (type == 11) {
        has_service = true;
        service_sub_type = sub_type;
      }
    }
    if (has_form && has_weixin && has_service) {
      online_join_pb_->set_view_component_type(-5);
      online_join_pb_->set_sub_type(-5);
    } else if (has_form && has_weixin) {
      online_join_pb_->set_view_component_type(-4);
      online_join_pb_->set_sub_type(-4);
    } else if (has_weixin && has_service) {
      online_join_pb_->set_view_component_type(-3);
      online_join_pb_->set_sub_type(-3);
    } else if (has_form && has_service) {
      online_join_pb_->set_view_component_type(-2);
      online_join_pb_->set_sub_type(-2);
    } else if (has_form) {
      online_join_pb_->set_view_component_type(14);
      online_join_pb_->set_sub_type(form_sub_type);
    } else if (has_weixin) {
      online_join_pb_->set_view_component_type(15);
      online_join_pb_->set_sub_type(form_sub_type);
    } else if (has_service) {
      online_join_pb_->set_view_component_type(11);
      online_join_pb_->set_sub_type(form_sub_type);
    } else {
      online_join_pb_->set_view_component_type(-1);
      online_join_pb_->set_sub_type(-1);
    }
  }
}

void OnlineJoinPack::SetStidInfo() {
  if (session_data_->TabName() != "universe") {
    online_join_pb_->set_stid_container_ad(std::move(GetStidContainer(
        *result_, *ad_request_, session_data_,
        style_info_->campaign_fanstop_support_info().is_inner_delivery(), false)));
  }
}

void OnlineJoinPack::SetRequestData() {
  bool is_close = ks::ad_base::util::IsClosePersonaliseRecommend(*(session_data_->get_ad_request()));
  online_join_pb_->set_is_close_personalise_recommend(is_close);
  online_join_pb_->set_user_group_tag(session_data_->get_ad_request()->ad_user_info().user_group_tag());
  online_join_pb_->set_user_risk_level(
      session_data_->get_ad_request()->reco_user_info().feature_collection().risk_level());
  online_join_pb_->set_corona_video_tab_type(session_data_->get_ad_request()->corona_video_info().tab_type());
  online_join_pb_->set_ad_personal_switch_status(
      static_cast<int32_t>(session_data_->get_ad_request()->ad_personal_switch_status()));
}

void OnlineJoinPack::SetExploreInfo() {
  online_join_pb_->set_explore_budget(style_info_->unit_support_info().explore_budget());
  online_join_pb_->set_explore_budget_status(style_info_->unit_support_info().explore_budget_status());
  online_join_pb_->set_explore_bid_type(style_info_->unit_support_info().explore_bid_type());
  online_join_pb_->set_extends_account_id(style_info_->unit_support_info().extends_account_id());
  online_join_pb_->set_extends_unit_id(style_info_->unit_support_info().extends_unit_id());
}

void OnlineJoinPack::SetImInfo() {
  if (style_info_->campaign().type() == kuaishou::ad::AdEnum::KWAI_PROMOTION_CONSULTATION ||
      (style_info_->campaign().type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE &&
       style_info_->unit_support_info().live_component_type() == 3)) {
    online_join_pb_->set_im_bluev_ks_user_id(style_info_->im_bluev_app_link().ks_user_id());
    online_join_pb_->set_im_bluev_sixin_open_status(style_info_->im_bluev_app_link().sixin_open_status());
    online_join_pb_->set_im_bluev_identifier_report_info(
        style_info_->im_bluev_app_link().identifier_report_info());
    online_join_pb_->set_im_bluev_biz_id(style_info_->im_bluev_app_link().biz_id());
  }
}

void OnlineJoinPack::SetOtherData() {
  online_join_pb_->set_ad_trace_dsp_type(kuaishou::ad::AdTraceDspType_Name(GetAdDspType(*result_)));
  online_join_pb_->set_reco_ip(ad_request_->reco_user_info().location().ip());
  if (ad_request_->ad_user_info().used_gps_cache_timestamp() != 0) {
    online_join_pb_->set_reco_gps_ts(ad_request_->ad_user_info().used_gps_cache_timestamp());
  } else {
    online_join_pb_->set_reco_gps_ts(ad_request_->reco_user_info().location().lat_lon_time());
  }
  online_join_pb_->set_region_source_type(ad_request_->ad_user_info().adcode().region_source_type());
  online_join_pb_->set_region_source_type_v2(ad_request_->ad_user_info().adcode().region_source_type_v2());

  // 智能托管 project bid/roi_ratio
  online_join_pb_->set_hosting_project_bid(style_info_->hosting_project().cpa_bid());
  online_join_pb_->set_hosting_roi_ratio(style_info_->hosting_project().roi_ratio());
  online_join_pb_->set_hosting_scene(style_info_->hosting_project().hosting_scene());
  online_join_pb_->set_hosting_project_id(style_info_->hosting_project().id());
  if (ks::ad_base::IsLongVideoPatchRequest(session_data_->get_sub_page_id())) {
    if (session_data_->get_front_server_request()->universe_request().imp_info().ad_pos_info_size() > 0) {
      Json imp_ext_data(StringToJson(session_data_->get_front_server_request()->universe_request()
                                          .imp_info()
                                          .ad_pos_info(0)
                                          .imp_ext_data()));
      online_join_pb_->set_relative_photo_id(imp_ext_data.GetInt("photo_id", 0));
      // 兼容下 IOS
      if (online_join_pb_->relative_photo_id() == 0) {
        int64_t relative_photo_id = 0;
        base::StringToInt64(imp_ext_data.GetString("photoId", "0"), &relative_photo_id);
        online_join_pb_->set_relative_photo_id(relative_photo_id);
      }
    }
  }

  if (style_info_->magic_site_page().direct_call_type() != 0) {
    online_join_pb_->set_direct_call_type(style_info_->magic_site_page().direct_call_type());
  }
  // 电商中间页字段
  if (session_data_->get_is_inspire_merchant_req()) {
    auto* inspire_merchant_ext = session_data_->mutable_ad_request()->mutable_inspire_merchant_ext();
    online_join_pb_->set_splash_llsid(inspire_merchant_ext->splash_llsid());
    online_join_pb_->set_relation_item_id(inspire_merchant_ext->relation_item_id());
    online_join_pb_->set_middle_page_request_times(inspire_merchant_ext->request_times());
    online_join_pb_->set_middle_page_industry_ids(inspire_merchant_ext->industry_ids());
    online_join_pb_->set_middle_page_ad_biz_item_type(inspire_merchant_ext->ad_biz_item_type());
    online_join_pb_->set_middle_page_max_live_ad_num(inspire_merchant_ext->max_live_ad_num());
    online_join_pb_->set_splash_creative_id(inspire_merchant_ext->splash_creative_id());
  }
  // 猜喜商品详情页字段
  if (SPDM_enable_guess_like_product_detail_category(session_data_->get_spdm_ctx())) {
    if (session_data_->get_pos_manager().IsGuessYouLike()) {
      if (session_data_->get_pos_manager().IsGuessLikeProductDetailPage()) {
        const auto& ad_merchant_detail_page_info =
          session_data_->get_ad_request()->ad_merchant_detail_page_info();
        online_join_pb_->set_merchant_author_id(ad_merchant_detail_page_info.author_id());
        online_join_pb_->set_x7_category_first(ad_merchant_detail_page_info.x7_category_first());
        online_join_pb_->set_x7_category_second(ad_merchant_detail_page_info.x7_category_second());
        online_join_pb_->set_x7_category_third(ad_merchant_detail_page_info.x7_category_third());
      }
    }
  }
  online_join_pb_->set_degrade_level(absl::StrCat(session_data_->get_degrade_level()));
  if (SPDM_enableSetZhuitouData()) {
    online_join_pb_->set_rel_campaign_id(style_info_->campaign().rel_campaign_id());
    online_join_pb_->set_explore_material_type(style_info_->campaign().explore_material_type());
    online_join_pb_->set_rel_type(style_info_->campaign().rel_type());
    online_join_pb_->set_auax_mapping_ocpx_action_type(
        style_info_->campaign().auax_mapping_ocpx_action_type());
  }

  if (session_data_->common_r_->GetIntCommonAttr("enable_set_iaa_ad_play_type").value_or(0)) {
    auto ad_play_type_str = std::string(session_data_->common_r_->GetStringCommonAttr("iaa_ad_play_type").value_or(""));    // NOLINT
    online_join_pb_->set_ad_play_type(ad_play_type_str);
  }
}

void OnlineJoinPack::SetAdQueueType() {
  if (ks::ad_base::IsInspireLive(session_data_->get_sub_page_id(), session_data_->get_ad_request()) &&
             ad_base_info_->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
    online_join_pb_->set_ad_queue_type(kuaishou::ad::AdEnum_AdQueueType_SOFT_AD_QUEUE);
  } else {
    online_join_pb_->set_ad_queue_type(ks::ad_base::GetAdQueueType(result_->ad_deliver_info()));
  }
}
void OnlineJoinPack::SetIndependentData() {
  const std::string& tab_name = session_data_->TabName();
  if (tab_name == "explore") {
    SetExploreData();
  } else if (tab_name == "nearby") {
    SetNearbyData();
  } else if (tab_name == "kwai_galaxy") {
    SetGalaxyData();
  } else if (tab_name == "search") {
    SetSearchData();
  } else if (tab_name == "splash") {
    SetSplashData();
  }
}

bool OnlineJoinPack::GetRankResult() {
  // 非白名单和品牌广告获取 rankResult 失败时返回 false
  // 是否有 rankServer 透传结果
  bool has_rank_result = !(result_->ad_source_type() == kuaishou::ad::BRAND);
  if (IsAdxWhiteUser(session_data_->get_ad_request()->ad_user_info().id())) {
    // 白名单用户不请求精排，故没有 rankServer 透传结果
    has_rank_result = false;
  }

  if (has_rank_result) {
    auto *ad_rank_result = GetAdRankResult(session_data_, *result_);
    if (!ad_rank_result) {
      falcon::Inc("front_server.zt_no_ad_rank_result", 1);
      return false;
    }

    if (!session_data_->get_is_universe_flow()) {
      // 联盟没传 rank_info，不用判断
      auto *ad_rank_info = GetAdRankInfo(session_data_, *result_);
      if (!ad_rank_info) {
        falcon::Inc("front_server.zt_ad_rank_result_idx_overflow", 1);
        return false;
      }
      rank_info_ = ad_rank_info;
    }
    rank_result_ = ad_rank_result;
  } else {
    // has_rank_result 为 false 下的兜底逻辑.
    rank_result_ = &rank_result_backup_;
    rank_info_ = &rank_info_backup_;
  }
  return true;
}

void OnlineJoinPack::SetStyleData() {
  if (style_info_->has_creative()) {
    online_join_pb_->set_site_id(style_info_->creative().site_id());
    auto age_in_hour = (base::GetTimestamp() / 1000 - style_info_->creative().create_time()) / 1000 / 3600;
    online_join_pb_->set_age_in_hour(age_in_hour);
    online_join_pb_->set_creative_type(style_info_->creative().creative_type());
  }
  auto get_convert_type = [&] () {
    if (style_info_->unit().convert_id() > 0) {
      return static_cast<kuaishou::ad::AdCallbackLog_ConvertType>(style_info_->trace_util().type());
    } else if (style_info_->unit().convert_id() == 0 &&
               static_cast<int32>(style_info_->unit().taobao_url_type()) == 4) {  // 金牛
      return kuaishou::ad::AdCallbackLog_ConvertType::AdCallbackLog_ConvertType_ECOM;
    } else if (style_info_->unit().convert_id() == 0 &&
               static_cast<int32>(style_info_->unit().web_uri_type()) == 2) {  // 自建站
      return kuaishou::ad::AdCallbackLog_ConvertType::AdCallbackLog_ConvertType_SITE_DIY;
    }
    return kuaishou::ad::AdCallbackLog_ConvertType::AdCallbackLog_ConvertType_UNKNWON;
  };
  // 创意优选打点
  if (style_info_->has_creative()) {
    const auto style_iter =
        session_data_->get_style_data_for_online_join().find(style_info_->creative().id());
    if (style_iter != session_data_->get_style_data_for_online_join().end()) {
      for (const auto& sd : style_iter->second) {
        online_join_pb_->add_style_data()->CopyFrom(sd);
      }
    }
  }
  online_join_pb_->set_convert_type(get_convert_type());
  online_join_pb_->set_campaign_sub_type(style_info_->campaign().sub_type());
  online_join_pb_->set_auto_target(style_info_->unit().auto_target());
  online_join_pb_->set_fiction_id(style_info_->unit_support_info().fiction_id());
  online_join_pb_->set_behavior_intention_target(style_info_->unit().behavior_intention_target());
  online_join_pb_->set_ad_app_id(style_info_->app_release().app_id());  // 广告主推广的 app_id
  if (SPDM_enableRealUseSdkInOnlineJoin()) {
    // 是否嵌入了快手广告 SDK (系统解析)
    online_join_pb_->set_real_use_sdk(style_info_->app_release().real_use_sdk());
  }
  online_join_pb_->set_audience_prediction_num(style_info_->unit().audience_prediction_num());
  online_join_pb_->set_internal_invest_plan_id(style_info_->campaign().internal_invest_plan_id());

  // 一键复苏
  online_join_pb_->set_explore_version(style_info_->unit_support_info().explore_version());
  // 若不处于辅助探索, reference_unit_ids 为空或者 [] ，如果处于辅助探索，
  // 那么形如 [0] 的 josn string 的长度会大于 2,
  bool reference = style_info_->unit_support_info().reference_unit_ids().size() > 2 &&
                    style_info_->unit().study_status() == 1;
  online_join_pb_->set_in_reference(reference);
  // 设置离线创意分级标签
  int32_t exp_id = session_data_->get_spdm_ctx().TryGetInteger("program_creative_exp_type", 0);
  for (const auto& creative_label : style_info_->creative().extend_fields().creative_extend_score().creative_label()) {  // NOLINT
    if (creative_label.exp_id() == exp_id) {
      online_join_pb_->set_creative_label_from_creative_server(creative_label.label());
      falcon::Inc(
          absl::StrCat("front_server.creative_label_from_creative_server_cnt_",
                       creative_label.label()).data(), 1);
      LOG_EVERY_N(INFO, 1000) << "epx_id: " << exp_id << ", label: " << creative_label.label();
      break;
    }
  }
  online_join_pb_->set_enable_ska(style_info_->unit_support_info().use_ska());
  online_join_pb_->set_playable_id(style_info_->unit_support_info().playable_id());
  online_join_pb_->set_unit_source_type(style_info_->unit().unit_source_type());
  online_join_pb_->set_wechat_page_tag(style_info_->magic_site_page().page_tag());
  online_join_pb_->set_live_creative_type(style_info_->creative().live_creative_type());
  online_join_pb_->set_photo_info_md5(std::to_string(style_info_->photo_status().parse_field().md5_uint()));
  online_join_pb_->set_is_hit_cache_info(session_data_->get_is_hit_cache_info());
  if (style_info_->has_account()) {
    online_join_pb_->set_city_product_id(style_info_->account().city_product_id());
    online_join_pb_->set_account_tag(style_info_->account().account_tag());
  }
  if (session_data_->get_ky_ad_entrance().length() > 0) {
    online_join_pb_->set_ky_ad_entrance(session_data_->get_ky_ad_entrance());
  }

  auto dpa_type = ad_base_info_->dpa_type();

  if (dpa_type == 2 || dpa_type == 4) {
    // SDPA 逻辑, 从正排 style_info 获取字段
    online_join_pb_->set_dpa_outer_id(style_info_->unit_support_info().dpa_outer_id());
    online_join_pb_->set_dpa_industry_id(style_info_->unit_support_info().dpa_industry_id());
    online_join_pb_->set_dpa_id(style_info_->unit_support_info().dpa_id());
    online_join_pb_->set_library_id(style_info_->unit().library_id());
  } else if (dpa_type == 1) {
    // DPA 逻辑，从 dpa 正排获取字段
    const auto* dpa_creative_info =
      (*session_data_->mutable_dpa_creative_info_map())[ad_base_info_->creative_id()];
    const auto* dpa_unit_info = (*session_data_->mutable_dpa_unit_info_map())[ad_base_info_->unit_id()];
    if (dpa_creative_info != nullptr) {
      online_join_pb_->set_dpa_outer_id(dpa_creative_info->creative().outer_id());
    } else {
      session_data_->dot_perf->Count(1, "online_join.dpa.creative.fail");
    }
    if (dpa_unit_info != nullptr) {
      online_join_pb_->set_dpa_industry_id(dpa_unit_info->unit().unit_support_info().dpa_industry_id());
      online_join_pb_->set_dpa_id(dpa_unit_info->unit().unit_support_info().dpa_id());
      online_join_pb_->set_library_id(dpa_unit_info->unit().library_id());
    } else {
      session_data_->dot_perf->Count(1, "online_join.dpa.unit.fail");
    }
  }
  online_join_pb_->set_agent_type(style_info_->agent().crm_agent_type());

  if ((ad_deliver_info_->is_expansion() == kuaishou::ad::IsExpansion::NOT_EXPANSION
       || ad_deliver_info_->is_expansion() == kuaishou::ad::IsExpansion::EXPANSION
       || ad_deliver_info_->is_expansion() == kuaishou::ad::IsExpansion::ESP_EXPANSION) &&
      (style_info_->target().celebrity_label().size() > 0
       || style_info_->target().purchase_intention_label().size() > 0
       || style_info_->target().extend_fields().population_size() > 0
       || style_info_->target().extend_fields().audience_size() > 0
       || style_info_->target().extend_fields().paid_audience_size() > 0)) {
    online_join_pb_->set_is_hit_soft_target(true);
  } else {
    online_join_pb_->set_is_hit_soft_target(false);
  }

  if (session_data_->get_app_id().compare("kuaishou") == 0 ||
      session_data_->get_app_id().compare("kuaishou_nebula") == 0) {
    if (session_data_->get_force_direct_call_cids().count(style_info_->creative().id()) > 0 ||
        IsSmallGameAdDirectCall(session_data_, *style_info_)) {
      // 强开直跳 或 广告主主动开了直跳, 都上报为直跳
      online_join_pb_->set_is_wechat_game_direct_call(true);
    }
  }
  if ((session_data_->IsInspire() ||
      session_data_->RequestType() == kuaishou::ad::FrontRequestType::EXPLORE_REQUEST ||
      session_data_->RequestType() == kuaishou::ad::FrontRequestType::NEARBY_REQUEST)) {
    if (style_info_->magic_site_page().direct_call_type() == 3) {
      online_join_pb_->set_wechat_company_add_fans_direct_call_avail(true);
      if (session_data_->get_enable_wechat_company_add_fans_direct_jump()) {
        online_join_pb_->set_is_wechat_company_add_fans_direct_call(true);
      }
    }
    if ((style_info_->magic_site_page().direct_call_type() == 4 &&
         FrontKconfUtil::qywxCustomerAcqDirectJumpBlackList()
         ->count(style_info_->account().license_no()) == 0) ||
        style_info_->magic_site_page().direct_call_type() == 5) {
      online_join_pb_->set_wechat_company_add_fans_direct_call_avail(true);
      if (session_data_->common_r_->GetIntCommonAttr("enable_wechat_customer_acq_direct_jump").value_or(0) == 1) {  // NOLINT
        online_join_pb_->set_is_wechat_company_add_fans_direct_call(true);
      }
    }

    if (FrontKconfUtil::qywxCustomerAcqDirectJumpBlackList()->count(
        style_info_->account().license_no()) == 0) {
      if (style_info_->magic_site_sku_page().direct_call_type() == 3 ||
          style_info_->magic_site_sku_page().direct_call_type() == 4) {
        online_join_pb_->set_is_wechat_company_add_fans_direct_call(true);
      }
    } else {
      if (style_info_->magic_site_sku_page().direct_call_type() == 3 ||
          style_info_->magic_site_sku_page().direct_call_type() == 5) {
        online_join_pb_->set_is_wechat_company_add_fans_direct_call(true);
        session_data_->dot_perf->Count(1, "enable_wechat_company_add_fans_direct_call", "succ_3_5");
      }
    }
  }
  // 一次请求标记需要二次请求信息
  const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
  if (ad_data_v2 != nullptr) {
    const kuaishou::ad::AdDataV2_AdClientAIInfo ad_client_ai_info = ad_data_v2->ad_client_ai_info();
    if (ad_client_ai_info.first_req_is_rerank_item()) {
      online_join_pb_->set_first_req_is_rerank_item(true);
      if (enable_photo_rerank_req_) {
        session_data_->dot_perf->Count(1, "PhotoRerankReq", "set_to_log");
      } else if (!enable_control_prerank_) {
        session_data_->dot_perf->Count(1, "LiveRerankReq", "set_to_log");
      }
    }
  }
  if (session_data_->get_is_live_rerank_req()) {
    online_join_pb_->set_is_live_rerank(true);
    session_data_->dot_perf->Count(1, "LiveRerankReq", "second_req_set_to_log");
    SetClientAiRerankReqInfoLive();
  }
  if (session_data_->get_is_photo_rerank_req()) {
    online_join_pb_->set_is_photo_rerank(true);
    session_data_->dot_perf->Count(1, "PhotoRerankReq", "second_req_set_to_log");
    SetClientAiRerankReqInfoPhoto();
  }
  // 重请求流量 tag 埋点
  if (enable_client_ai_rerank_req_info_tag_) {
    if (session_data_->get_sub_page_id() == 11001001) {
      online_join_pb_->set_mtb_exp_cpm_cali_flag(mix_mtb_bid_frac_ad_cpm_nebula_);
    } else if (session_data_->get_sub_page_id() == 10011001) {
      online_join_pb_->set_mtb_exp_cpm_cali_flag(mix_mtb_bid_frac_ad_cpm_gamora_);
    }
  }

  online_join_pb_->set_creative_das_data(style_info_->creative().creative_support_info().creative_das_data());
  online_join_pb_->set_unit_das_data(style_info_->unit_support_info().unit_das_data());
  CleanJsonData(online_join_pb_->mutable_creative_das_data(),
                   FrontKconfUtil::creativeDasDataWhiteSet().get());
  CleanJsonData(online_join_pb_->mutable_unit_das_data(),
                   FrontKconfUtil::unitDasDataWhiteSet().get());
  online_join_pb_->set_target_type(style_info_->unit_support_info().target_type());
  online_join_pb_->set_target_setting_type(style_info_->unit_support_info().target_setting_type());
  online_join_pb_->set_target_extend(style_info_->unit_support_info().target_extend());
  online_join_pb_->set_scene_oriented_type(style_info_->campaign().scene_oriented_type());
  online_join_pb_->set_auto_target_modified(style_info_->target().auto_target_modified());
  online_join_pb_->set_ad_risk_level(style_info_->photo_status().risk_level());
  online_join_pb_->set_ad_live_risk_level(style_info_->live_stream_user_info().risk_level());
  if (style_info_->has_live_stream_user_info()) {
    online_join_pb_->set_is_recruiting_live(style_info_->live_stream_user_info().is_recruiting_live());
  }
  if (style_info_->campaign().type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
    online_join_pb_->set_series_id(style_info_->unit_support_info().series_id());
  }
  if (style_info_->has_creative() &&
      style_info_->creative().has_extend_fields()) {
    online_join_pb_->mutable_risk_labels()
                    ->CopyFrom(style_info_->creative().extend_fields().risk_labels());
    if (session_data_->get_enable_transport_risk_info()) {
      online_join_pb_->mutable_risk_tag()->CopyFrom(
          style_info_->creative().parse_field().risk_tag());
      online_join_pb_->set_risk_photo_hash(style_info_->creative().parse_field().risk_photo_hash());
    }
  }
  online_join_pb_->set_is_trace_api_detection(session_data_->get_is_trace_api_detection());
  if (style_info_->has_magic_site_page() &&
      style_info_->magic_site_page().has_adlp_data_report_info()) {
    online_join_pb_->set_adlp_data_report_info(style_info_->magic_site_page().adlp_data_report_info());
  }
  if (style_info_->has_magic_site_page() &&
      style_info_->magic_site_page().has_page_report_info() &&
      SPDM_enableFillPageReportInfo()) {
    session_data_->dot_perf->Count(1, "fill_page_report_info");
    online_join_pb_->set_page_report_info(style_info_->magic_site_page().page_report_info());
  }

  if (style_info_->has_magic_site_page()) {
    if (style_info_->magic_site_page().has_conversion_path()) {
      online_join_pb_->set_conversion_path(style_info_->magic_site_page().conversion_path());
      session_data_->dot_perf->Count(1, "conversion_path", "magic_site_page");
    }
    if (style_info_->magic_site_page().has_sub_conversion_path()) {
      online_join_pb_->set_sub_conversion_path(style_info_->magic_site_page().sub_conversion_path());
      session_data_->dot_perf->Count(1, "sub_conversion_path", "magic_site_page");
    }
  }

  online_join_pb_->set_download_page_type(style_info_->unit_support_info().download_page_type());
  online_join_pb_->set_download_page_url(style_info_->unit_support_info().download_page_url());
  online_join_pb_->set_account_auto_manage(style_info_->account_support_info().account_auto_manage());
  online_join_pb_->set_periodic_delivery_type(style_info_->campaign().periodic_delivery_type());
  online_join_pb_->set_unit_sdpa_name(style_info_->wt_unit().sdpa_name());
  online_join_pb_->set_unit_cluster_id(style_info_->wt_unit().cluster_id());
}

void OnlineJoinPack::SetClientAiRerankReqInfoPhoto() {
  if (enable_add_rerank_req_info_) {
    // 短视频
    const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
    if (ad_data_v2 == nullptr) {
      return;
    }
    const kuaishou::ad::AdDataV2_AdClientAIInfo ad_client_ai_info = ad_data_v2->ad_client_ai_info();
    // 旧分和 id 信息来自二次请求体透传
    online_join_pb_->mutable_client_ai_rerank_req_info()->set_llsid(
      session_data_->get_ad_request()->photo_rerank_req_info().llsid());
    online_join_pb_->mutable_client_ai_rerank_req_info()->set_creative_id(
      session_data_->get_ad_request()->photo_rerank_req_info().creative_id());
    online_join_pb_->mutable_client_ai_rerank_req_info()->set_origin_ad_old_score(
      session_data_->get_ad_request()->photo_rerank_req_info().score());
    // 新分来 item 的信息
    online_join_pb_->mutable_client_ai_rerank_req_info()->set_score(ad_client_ai_info.score());
    online_join_pb_->mutable_client_ai_rerank_req_info()->set_origin_ad_new_score(
      ad_client_ai_info.origin_ad_new_score_int());
  }
}

void OnlineJoinPack::SetClientAiRerankReqInfoLive() {
  if (enable_add_rerank_req_info_) {
    // 直播
    const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
    if (ad_data_v2 == nullptr) {
      return;
    }
    const kuaishou::ad::AdDataV2_AdClientAIInfo ad_client_ai_info = ad_data_v2->ad_client_ai_info();
    // 旧分和 id 信息来自二次请求体透传
    if (enable_add_rerank_req_info_live_use_photo_) {
      const auto* ad_request = session_data_->get_ad_request();
      if (ad_request) {
        auto photo_rerank_req_info = ad_request->photo_rerank_req_info();
        online_join_pb_->mutable_client_ai_rerank_req_info()->set_llsid(photo_rerank_req_info.llsid());
        online_join_pb_->mutable_client_ai_rerank_req_info()->set_creative_id(
          photo_rerank_req_info.creative_id());
        online_join_pb_->mutable_client_ai_rerank_req_info()->set_origin_ad_old_score(
          photo_rerank_req_info.score());
      }
    } else {
      auto &live_rerank_infos = *session_data_->mutable_live_rerank_infos();
      if (!live_rerank_infos.empty()) {
        online_join_pb_->mutable_client_ai_rerank_req_info()->set_llsid(live_rerank_infos[0].llsid);
        online_join_pb_->mutable_client_ai_rerank_req_info()->set_creative_id(
          live_rerank_infos[0].creative_id);
        online_join_pb_->mutable_client_ai_rerank_req_info()->set_origin_ad_old_score(
          live_rerank_infos[0].score);
      }
    }
    // 新分来 item 的信息
    online_join_pb_->mutable_client_ai_rerank_req_info()->set_score(ad_client_ai_info.score());
    // 直播暂时没有这个分数
    if (enable_calc_client_ai_rerank_score_adrank_) {
      online_join_pb_->mutable_client_ai_rerank_req_info()->set_origin_ad_new_score(
        ad_client_ai_info.origin_ad_new_score_int());
    } else {
      online_join_pb_->mutable_client_ai_rerank_req_info()->set_origin_ad_new_score(0);
    }
  }
}

void OnlineJoinPack::SetLiveInfo() {
  if (style_info_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
        IsNonMerchantLivePromote(*style_info_)) {
    if (style_info_->creative().live_creative_type() ==
              kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE ||
          IsNonMerchantLiveStream(*style_info_)) {
      online_join_pb_->set_is_live(true);
    } else {
      online_join_pb_->set_is_live_to_photo(true);  // 是否为短视频带直播
      if (session_data_->TabName() == "explore" || session_data_->TabName() == "nearby"
          || session_data_->TabName() == "splash" || session_data_->TabName() == "follow") {
        online_join_pb_->set_p2l_imp_follow(rank_result_->predict_origin_info().item_impression_wtr());
        online_join_pb_->set_p2l_ctr(rank_result_->predict_origin_info().ctr());
        online_join_pb_->set_p2l_p3s(rank_result_->predict_origin_info().cvr());
      }
    }
    online_join_pb_->set_live_p3s(
            rank_result_->predict_origin_info().live_play_3s_rate());  // 直播展现到 p3s
    online_join_pb_->set_live_audience_slide(
            rank_result_->predict_origin_info().ad_live_audience());  // 直播展现到 liveplaystarted
    online_join_pb_->set_photo2live_pay_rate(
            rank_result_->predict_origin_info().photo2live_pay_rate());  // 直播展现到 liveplaystarted
    online_join_pb_->set_live_goods_view(
            rank_result_->predict_origin_info().live_goods_view_rate());  // 直播商品访问率
    online_join_pb_->set_live_order_paied(
            rank_result_->predict_origin_info().live_order_paid_rate());  // 直播商品购买率
    online_join_pb_->set_predict_unify_deep_cvr(
            rank_result_->predict_origin_info().live_order_paid_rate());  // 直播
    online_join_pb_->set_live_server_show_p3s_slide(
            rank_result_->predict_origin_info().live_server_show_play_3s_slide());  // 直播滑滑 p3s 率
    online_join_pb_->set_live_server_show_p3s_feed(
            rank_result_->predict_origin_info().live_server_show_play_3s_feed());
    if (ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request())) != AdEnum::FLOW_UNIVERSE) {
      online_join_pb_->set_live_p3s_follow(
          rank_result_->predict_origin_info().live_p3s_follow());       // 联盟没有
    }
    online_join_pb_->set_auto_roas(rank_result_->ad_rank_trans_info().auto_roas());
  }

  // 粉条直播透传 live_order_source
  static absl::flat_hash_set<AdEnum::CampaignType> fanstop_live_types{
      AdEnum::AD_FANSTOP_LIVE_TO_ALL, AdEnum::AD_FANSTOP_LIVE_TO_FANS, AdEnum::AD_FANSTOP_LIVE_TO_SHOW};
  if (fanstop_live_types.count(style_info_->campaign().type())) {
    online_join_pb_->set_live_order_source(
        style_info_->campaign_fanstop_support_info().live_order_source());
    VLOG_IF(2, online_join_pb_->live_order_source() != 0)
        << ", campaign_type = " << style_info_->campaign().type()
        << ", ad_source_type = " << result_->ad_source_type()
        << ", live_order_source = " << online_join_pb_->live_order_source()
        << ", account_type = " << result_->ad_deliver_info().ad_base_info().account_type();
  }
  if (SPDM_enableFillBsLive() && style_info_->magic_site_sku_page().id() > 0) {
    if (style_info_->magic_site_sku_page().parse_fields().sku_ids_size() > 0) {
      online_join_pb_->set_bs_live_spu_id(style_info_->magic_site_sku_page().parse_fields().sku_ids(0));
    }
    if (style_info_->magic_site_sku_page().direct_call_type() != 0) {
      online_join_pb_->set_direct_call_type(style_info_->magic_site_sku_page().direct_call_type());
    }
    if (style_info_->magic_site_sku_page().has_adlp_data_report_info()) {
      online_join_pb_->set_adlp_data_report_info(style_info_->magic_site_sku_page().adlp_data_report_info());
    }
    if (style_info_->magic_site_sku_page().has_page_report_info() &&
        SPDM_enableFillPageReportInfo()) {
      session_data_->dot_perf->Count(1, "fill_page_report_info");
      online_join_pb_->set_page_report_info(style_info_->magic_site_sku_page().page_report_info());
    }

    if (style_info_->magic_site_sku_page().has_conversion_path()) {
      online_join_pb_->set_conversion_path(style_info_->magic_site_sku_page().conversion_path());
      session_data_->dot_perf->Count(1, "conversion_path", "magic_site_sku_page");
    }
    if (style_info_->magic_site_sku_page().has_sub_conversion_path()) {
      online_join_pb_->set_sub_conversion_path(style_info_->magic_site_sku_page().sub_conversion_path());
      session_data_->dot_perf->Count(1, "sub_conversion_path", "magic_site_sku_page");
    }

    online_join_pb_->set_wechat_page_tag(style_info_->magic_site_sku_page().page_tag());
    SetLpComponentForSku();
  }
}

void OnlineJoinPack::SetTransparentInfo() {
  online_join_pb_->mutable_ad_target_trans_info()->CopyFrom(
                    online_join_params_transparent_->ad_target_trans_info());
  online_join_pb_->mutable_ad_server_trans_info()->CopyFrom(
                    online_join_params_transparent_->ad_server_trans_info());
  online_join_pb_->mutable_ad_server_trans_info()->set_stay_time_scores("");
  online_join_pb_->mutable_ad_rank_trans_info()->CopyFrom(
                    rank_result_->ad_rank_trans_info());
  if (SPDM_enableRemoveOnlineJoinPriceRecord()) {
    online_join_pb_->mutable_ad_rank_trans_info()->mutable_adjust_price_record()->clear();
  }
}

void OnlineJoinPack::SetDataFromOnlineJoinParamsTransparent() {
  online_join_pb_->set_work_flow_type(online_join_params_transparent_->work_flow_type());
  online_join_pb_->set_ad_strategy_tag(online_join_params_transparent_->ad_strategy_tag());
  online_join_pb_->set_dynamic_retrieval_queue_size(
                      online_join_params_transparent_->dynamic_retrieval_queue_size());
  online_join_pb_->set_dynamic_prerank_queue_size(
                      online_join_params_transparent_->dynamic_prerank_queue_size());
  online_join_pb_->set_is_search_celebrity(online_join_params_transparent_->is_search_celebrity());
  online_join_pb_->set_is_search_mid_page(online_join_params_transparent_->is_search_mid_page());
  online_join_pb_->set_calculate_gap_target(online_join_params_transparent_->calculate_gap_target());
  online_join_pb_->set_hostname(online_join_params_transparent_->ad_server_hostname());
  online_join_pb_->set_pred_score(online_join_params_transparent_->pred_score());
  online_join_pb_->set_ecpm_ctr_score(online_join_params_transparent_->ecpm_ctr_score());
  online_join_pb_->set_ecpm_cvr_score(online_join_params_transparent_->ecpm_cvr_score());
  online_join_pb_->set_recall_sort_type(online_join_params_transparent_->recall_sort_type());
  online_join_pb_->set_twin_bid_strategy(online_join_params_transparent_->twin_bid_strategy());
  online_join_pb_->set_speed_type(online_join_params_transparent_->speed_type());
  online_join_pb_->set_auto_cpa_bid_modify_code(online_join_params_transparent_->auto_cpa_bid_modify_code());
  online_join_pb_->set_auto_roas_modify_tag(online_join_params_transparent_->auto_roas_modify_tag());
  online_join_pb_->set_rank_hostname(online_join_params_transparent_->rank_hostname());
  online_join_pb_->set_programmatic_retrieval_type(
                      online_join_params_transparent_->programmatic_retrieval_type());
  online_join_pb_->set_is_skip_bid_server(online_join_params_transparent_->is_skip_bid_server() ||
                                          online_join_pb_->is_skip_bid_server());
  online_join_pb_->set_union_strategy_tag(online_join_params_transparent_->union_strategy_tag());
  online_join_pb_->set_skip_bid_server_tag(online_join_params_transparent_->skip_bid_server_tag());
  // dsp 目前没有设置
  online_join_pb_->set_deep_twin_bound(online_join_params_transparent_->deep_twin_bound());
  online_join_pb_->set_retrieval_tag_new(online_join_params_transparent_->retrieval_tag_new());
  online_join_pb_->set_multi_retrieval_tag(online_join_params_transparent_->multi_retrieval_tag());
  online_join_pb_->mutable_multi_retrieval_bitset()->CopyFrom(
                      online_join_params_transparent_->multi_retrieval_bitset());
  online_join_pb_->set_prerank_type(online_join_params_transparent_->prerank_type());
  online_join_pb_->set_smart_matching_pred(online_join_params_transparent_->smart_matching_pred());
  online_join_pb_->set_creative_activity_type(
                      online_join_params_transparent_->creative_activity_type());
  online_join_pb_->set_target_server_shard_type(
                      online_join_params_transparent_->target_shard_type());
  online_join_pb_->set_is_lookalike(online_join_params_transparent_->is_lookalike());
  online_join_pb_->mutable_prerank_dynamic_info()->CopyFrom(
      online_join_params_transparent_->prerank_dynamic_info());
  if (!session_data_->get_is_universe_flow()) {
    online_join_pb_->set_auto_deep_cpa_bid(online_join_params_transparent_->auto_deep_cpa_bid());
  } else {
    online_join_pb_->set_auto_deep_cpa_bid(target_bid_->auto_deep_cpa_bid());
  }
  if (session_data_->TabName() != "kwai_galaxy" && session_data_->TabName() != "search") {
    online_join_pb_->set_dynamic_ad_pos_gap_date(online_join_params_transparent_->dynamic_ad_pos_gap_date());
    online_join_pb_->set_cur_req_scene_str(online_join_params_transparent_->cur_req_scene_str());
  }
  const std::string &target_hostname =
    (online_join_params_transparent_->target_shard_type() !=
        kuaishou::ad::AdEnum_TargetServerShardType_COLD_SHARD)
              ? online_join_params_transparent_->target_hostname()
              : online_join_params_transparent_->cold_target_hostname();
  online_join_pb_->set_target_hostname(target_hostname);
  online_join_pb_->set_merchant_dsp_hostname(online_join_params_transparent_->merchant_hostname());
  if (online_join_params_transparent_->target_order() != 0) {
    online_join_pb_->set_target_order(
          online_join_params_transparent_->target_order());
    session_data_->dot_perf->Interval(online_join_params_transparent_->target_order(),
                                      "front_server.target_order");
    falcon::Stat("front_server.target_order", online_join_params_transparent_->target_order());
  }
  // set look alike
  absl::flat_hash_set<int64_t> lookalike;
  bool hit_lookalike_user = false;
  const auto &user_info = session_data_->get_ad_request()->ad_user_info();
  for (const auto &upload_population_orientation : style_info_->populations()) {
    if (upload_population_orientation.population_type() == 5) {
      lookalike.insert(upload_population_orientation.orientation_id());
    }
  }
  if (lookalike.size() > 0) {
    for (const int64_t pop_id : user_info.orientation()) {
      if (lookalike.find(pop_id) != lookalike.end()) {
        hit_lookalike_user = true;
        break;
      }
    }
  }
  if (hit_lookalike_user != online_join_params_transparent_->hit_lookalike_user()) {
    falcon::Inc("front_server.hit_lookalike_user_diff", 1);
  }
  online_join_pb_->set_hit_lookalike_user(hit_lookalike_user);
  // 外循环数据下发
  online_join_pb_->set_ecom_quality(online_join_params_transparent_->ecom_quality());
  online_join_pb_->set_first_click_score(online_join_params_transparent_->first_click_score());
  online_join_pb_->set_multi_touch_score(online_join_params_transparent_->multi_touch_score());
  online_join_pb_->set_calibrated_cvr(online_join_params_transparent_->calibrated_cvr());
  online_join_pb_->set_ptds_search_label(online_join_params_transparent_->ptds_search_label());
  online_join_pb_->set_account_mark(online_join_params_transparent_->account_mark());
  online_join_pb_->set_rta_trace_id(online_join_params_transparent_->rta_trace_req_id());
  online_join_pb_->set_rta_strategy_id(online_join_params_transparent_->rta_strategy_id());
  online_join_pb_->set_rta_second_direct_bid(online_join_params_transparent_->rta_second_direct_bid());
  online_join_pb_->set_rta_feature_id(
      online_join_params_transparent_->rta_feature_id());
  online_join_pb_->set_trace_request_id(
      online_join_params_transparent_->trace_request_id());
  online_join_pb_->set_adx_cpm_precise_bid(
      online_join_params_transparent_->adx_cpm_precise_bid());
  online_join_pb_->set_rta_sta_tag(
      online_join_params_transparent_->rta_sta_tag());
  online_join_pb_->set_rta_ratio(
      online_join_params_transparent_->first_click_score() / 1000000.0);
  if (online_join_params_transparent_->ad_direct_merchant_biz() !=
      kuaishou::ad::AdEnum::MERCH_BIZ_NONE) {
    online_join_pb_->set_ad_direct_merchant_biz(
        online_join_params_transparent_->ad_direct_merchant_biz());
    online_join_pb_->set_ad_direct_merchant_stage(
        online_join_params_transparent_->ad_direct_merchant_stage());
    auto key_str = absl::StrCat(kuaishou::ad::AdEnum_AdDirectMerchantBiz_Name(
                    online_join_params_transparent_->ad_direct_merchant_biz()),
                   "#", kuaishou::ad::AdEnum_AdDirectMerchantStage_Name(
                    online_join_params_transparent_->ad_direct_merchant_stage()));
    session_data_->dot_perf->Interval(1, "front_server.ad_direct_merchant_info", key_str);
  }
  online_join_pb_->set_is_filtered_by_punish_code(
                    online_join_params_transparent_->is_filtered_by_punish_code());
}

void OnlineJoinPack::SetRankResultTransparent() {
  online_join_pb_->set_auction_bid_conv(rank_result_->rank_bid_info().auction_bid_conv());
  online_join_pb_->set_auction_bid_ecpc(rank_result_->rank_bid_info().auction_bid_ecpc());
  online_join_pb_->set_auction_bid_deep(rank_result_->rank_bid_info().auction_bid_deep());
  online_join_pb_->set_real_sctr(rank_result_->rank_bid_info().real_sctr());
  online_join_pb_->set_nature_ad_tag(rank_result_->rank_bid_info().nature_ad_tag());
  online_join_pb_->set_pre_ntr(rank_result_->predict_origin_info().ntr());
  online_join_pb_->set_cpm_thr(rank_result_->rank_reserve_info().cpm_thr());
  online_join_pb_->set_cpm_tag(static_cast<int64>(rank_result_->rank_reserve_info().cpm_tag()));
  online_join_pb_->set_server_show_cvr(rank_result_->predict_origin_info().server_show_cvr());
  online_join_pb_->set_clientshow_click2(rank_result_->predict_origin_info().clientshow_click2());
  online_join_pb_->set_click2_conv(rank_result_->predict_origin_info().click2_conv());
  online_join_pb_->set_click2_lps(rank_result_->predict_origin_info().click2_lps());
  online_join_pb_->set_click2_purchase(rank_result_->predict_origin_info().click2_purchase());
  online_join_pb_->set_click2_shop_action(rank_result_->predict_origin_info().click2_shop_action());
  online_join_pb_->set_click2_nextstay(rank_result_->predict_origin_info().click2_nextstay());
  online_join_pb_->set_click2_credit_rate(rank_result_->predict_origin_info().click2_credit_rate());
  online_join_pb_->set_credit_eval_rate(rank_result_->predict_origin_info().credit_eval_rate());
  online_join_pb_->set_click2_purchase_device(rank_result_->predict_origin_info().click2_purchase_device());
  online_join_pb_->set_click_purchase_device(rank_result_->predict_origin_info().click_purchase_device());
  online_join_pb_->set_click_insur_purchase_rate(
                    rank_result_->predict_origin_info().click_insur_purchase_rate());
  online_join_pb_->set_c2_insur_purchase_rate(rank_result_->predict_origin_info().c2_insur_purchase_rate());
  online_join_pb_->set_is_skip_bid_server(rank_result_->rank_base_info().is_skip_bid_server() ||
                                          online_join_pb_->is_skip_bid_server());
  online_join_pb_->set_sort_tag(rank_result_->rank_price_info().sort_tag());
  LOG_EVERY_N(INFO, 100000) << "online join pb sort tag: " << rank_result_->rank_price_info().sort_tag();
  online_join_pb_->set_new_creative_app_conversion_rate(
                    rank_result_->predict_origin_info().new_creative_app_conversion_rate());
  online_join_pb_->set_jinjian_credit_grant(rank_result_->predict_origin_info().credit_conv_grant_rate());
  online_join_pb_->set_prod_apr(rank_result_->predict_origin_info().prod_apr());
  online_join_pb_->set_server_client_show_rate(
                    rank_result_->predict_origin_info().server_client_show_rate());
  online_join_pb_->set_server_show_ctr(rank_result_->predict_origin_info().server_show_ctr());
  online_join_pb_->set_origin_price(rank_result_->rank_price_info().origin_price());
  int64_t origin_price_protect = FrontKconfUtil::originPriceProtect();
  if (rank_result_->rank_price_info().origin_price() > origin_price_protect) {
    online_join_pb_->set_origin_price(origin_price_protect);
  }
  online_join_pb_->set_next_price(rank_result_->rank_price_info().next_price());
  online_join_pb_->set_price_undiscount(rank_result_->rank_price_info().price_undiscount());
  online_join_pb_->set_conv_ltv(rank_result_->predict_origin_info().conv_ltv());
  online_join_pb_->set_purchase_ltv(rank_result_->predict_origin_info().purchase_ltv());
  online_join_pb_->set_auto_roas(rank_result_->ad_rank_trans_info().auto_roas());
  online_join_pb_->set_prophet_boost_exp_tag(rank_result_->ad_rank_trans_info().prophet_boost_exp_tag());
  online_join_pb_->set_prophet_boost_ratio(rank_result_->ad_rank_trans_info().prophet_boost_ratio());
  online_join_pb_->set_finance_unify_lps_cvr(rank_result_->ad_rank_trans_info().finance_unify_lps_cvr());
  online_join_pb_->set_finance_jinjian_credit_ratio(
          rank_result_->ad_rank_trans_info().finance_jinjian_credit_ratio());
  online_join_pb_->set_finance_use_credit_ratio(
          rank_result_->ad_rank_trans_info().finance_use_credit_ratio());
  online_join_pb_->set_edu_lps_cvr(rank_result_->ad_rank_trans_info().edu_lps_cvr());
  online_join_pb_->set_landingpage_submit_rate_c2(
          rank_result_->ad_rank_trans_info().landingpage_submit_rate_c2());
  online_join_pb_->set_unify_cvr_calibration_model_rate(
          rank_result_->ad_rank_trans_info().unify_cvr_calibration_model_rate());
  online_join_pb_->set_clue_lps_cvr(rank_result_->ad_rank_trans_info().clue_lps_cvr());
  online_join_pb_->set_poi_min_distance(rank_result_->ad_rank_trans_info().poi_min_distance());
  online_join_pb_->set_clue_clk_lps_cvr(rank_result_->ad_rank_trans_info().clue_clk_lps_cvr());
  online_join_pb_->set_sdpa_unify_cvr(rank_result_->ad_rank_trans_info().sdpa_unify_cvr());
  online_join_pb_->set_edu_lps_deep_ecpc_rate(rank_result_->ad_rank_trans_info().edu_lps_deep_ecpc_rate());
  online_join_pb_->set_fin_credit_roi_rate(rank_result_->ad_rank_trans_info().fin_credit_roi_rate());
  online_join_pb_->set_building_conv_rate(rank_result_->ad_rank_trans_info().building_conv_rate());
  online_join_pb_->set_lps_deep_generalization_score(rank_result_->ad_rank_trans_info().lps_deep_generalization_score());  // NOLINT
  online_join_pb_->set_lps_deep_generalization_score_cmd_id(rank_result_->ad_rank_trans_info().lps_deep_generalization_score_cmd_id());  // NOLINT
  if (SPDM_enable_iaa_acq_gen_score_tags(session_data_->get_spdm_ctx())) {
    session_data_->dot_perf->Interval(rank_result_->ad_rank_trans_info().iaa_acq_gen_score(),
     "onlineJoinPack", "score");
    session_data_->dot_perf->Interval(rank_result_->ad_rank_trans_info().iaa_acq_gen_score_cmd_id(),
     "onlineJoinPack", "cmd_id");
    online_join_pb_->set_iaa_acq_gen_score(rank_result_->ad_rank_trans_info().iaa_acq_gen_score());  // NOLINT
    online_join_pb_->set_iaa_acq_gen_score_cmd_id(rank_result_->ad_rank_trans_info().iaa_acq_gen_score_cmd_id());  // NOLINT
  }
  online_join_pb_->set_lps_acquisition_clk_lps_score(rank_result_->ad_rank_trans_info().lps_acquisition_clk_lps_score());  // NOLINT
  online_join_pb_->set_wangfu_purchase_rate(rank_result_->ad_rank_trans_info().wangfu_purchase_rate());
  online_join_pb_->set_wangfu_purchase_rate_exp(
                  rank_result_->ad_rank_trans_info().wangfu_purchase_rate_exp());
  online_join_pb_->set_ad_adx_thanos_cvr_exp(
                  rank_result_->ad_rank_trans_info().ad_adx_thanos_cvr_exp());
  online_join_pb_->set_ecom_app_conversion_rate(
    rank_result_->ad_rank_trans_info().ecom_app_conversion_rate());
  online_join_pb_->set_car_purchase_rate(rank_result_->ad_rank_trans_info().car_purchase_rate());
  online_join_pb_->set_common_purchase_rate(rank_result_->ad_rank_trans_info().common_purchase_rate());
  online_join_pb_->set_common_ctr_cali_rate(
                  rank_result_->ad_rank_trans_info().common_ctr_cali_rate());
  online_join_pb_->set_common_cvr_cali_rate(
                  rank_result_->ad_rank_trans_info().common_cvr_cali_rate());
  online_join_pb_->set_common_dcvr_cali_rate(
                  rank_result_->ad_rank_trans_info().common_dcvr_cali_rate());
  online_join_pb_->set_offline_calibrate_ctr_score(
                  rank_result_->ad_rank_trans_info().offline_calibrate_ctr_score());
  online_join_pb_->set_offline_calibrate_cvr_score(
                  rank_result_->ad_rank_trans_info().offline_calibrate_cvr_score());
  online_join_pb_->set_offline_calibrate_dcvr_score(
                  rank_result_->ad_rank_trans_info().offline_calibrate_dcvr_score());
  online_join_pb_->set_inner_cpr(rank_result_->predict_origin_info().inner_cpr());
  online_join_pb_->set_merchant_ltv(rank_result_->predict_origin_info().merchant_ltv());
  online_join_pb_->set_live_p3s_ltv(rank_result_->predict_origin_info().live_p3s_ltv());
  online_join_pb_->set_order_paied(rank_result_->predict_origin_info().c1_order_paied());  // 短视频商品购买率
  online_join_pb_->set_is_retarget_ad(rank_result_->rank_base_info().is_retarget_ad());
  online_join_pb_->set_retarget_tool_tag(rank_result_->rank_base_info().retarget_tool_tag());
  online_join_pb_->set_native_quality_status(rank_result_->rank_base_info().native_quality_status());
  online_join_pb_->set_c1_order_paid(rank_result_->predict_origin_info().c1_order_paied());
  online_join_pb_->set_inner_uplift_order_paid(rank_result_->predict_origin_info().inner_uplift_order_paid());
  online_join_pb_->set_spu_order_paid(rank_result_->predict_origin_info().spu_order_paid());
  online_join_pb_->set_author_order_paid(rank_result_->predict_origin_info().author_order_paid());
  online_join_pb_->set_before_heritage_cvr(rank_result_->predict_origin_info().before_heritage_cvr());
  online_join_pb_->set_product_min_price(rank_result_->rank_base_info().product_min_price());
  online_join_pb_->set_before_heritage_cmd_id(rank_result_->predict_origin_info().before_heritage_cmd_id());
  online_join_pb_->set_ltv_after_calibration(rank_result_->predict_origin_info().ltv_after_calibration());
  online_join_pb_->set_wtr(rank_result_->predict_origin_info().wtr());
  online_join_pb_->set_cvr(rank_result_->predict_origin_info().cvr());
  online_join_pb_->set_click_app_invoked(rank_result_->predict_origin_info().click_app_invoked());
  online_join_pb_->set_inner_loop_ecpc_imp_conv_rate(rank_result_->ad_rank_trans_info().inner_loop_ecpc_imp_conv_rate());  // NOLINT
  online_join_pb_->set_predict_future_cvr(rank_result_->ad_rank_trans_info().predict_future_cvr());
  online_join_pb_->set_future_cvr_adjust(rank_result_->ad_rank_trans_info().future_cvr_adjust());
  online_join_pb_->set_video_play_7s(rank_result_->ad_rank_trans_info().video_play_7s());  // NOLINT
  online_join_pb_->set_short_video_play_7s(rank_result_->ad_rank_trans_info().short_video_play_7s());  // NOLINT
  online_join_pb_->set_effective_play_7s(rank_result_->ad_rank_trans_info().effective_play_7s());  // NOLINT
  online_join_pb_->set_video_effective_play_7s(rank_result_->ad_rank_trans_info().video_effective_play_7s());  // NOLINT
  online_join_pb_->set_short_video_effective_play_7s(rank_result_->ad_rank_trans_info().short_video_effective_play_7s());  // NOLINT
  online_join_pb_->set_live_audience(rank_result_->predict_origin_info().ad_live_audience());
  online_join_pb_->set_shop_jump(rank_result_->predict_origin_info().shop_jump());
  online_join_pb_->set_live_play_1m(rank_result_->predict_origin_info().live_play_1m_rate());
  online_join_pb_->set_shop_cart_rate(rank_result_->predict_origin_info().shop_cart_rate());
  online_join_pb_->set_photo2live_rate(rank_result_->predict_origin_info().photo2live_rate());
  online_join_pb_->set_predict_auc_score_base(rank_result_->rank_rank_info().predict_auc_score_base());
  online_join_pb_->set_auc_cmd_id_base(rank_result_->rank_rank_info().auc_cmd_id_base());
  online_join_pb_->set_predict_auc_score_exp(rank_result_->rank_rank_info().predict_auc_score_exp());
  online_join_pb_->set_auc_cmd_id_exp(rank_result_->rank_rank_info().auc_cmd_id_exp());
  online_join_pb_->set_predict_auc_score_base_fusion(rank_result_->rank_rank_info().predict_auc_score_base_fusion()); // NOLINT
  online_join_pb_->set_auc_cmd_id_base_fusion(rank_result_->rank_rank_info().auc_cmd_id_base_fusion());  // NOLINT
  online_join_pb_->set_predict_auc_score_exp_fusion(rank_result_->rank_rank_info().predict_auc_score_exp_fusion());  // NOLINT
  online_join_pb_->set_auc_cmd_id_exp_fusion(rank_result_->rank_rank_info().auc_cmd_id_exp_fusion());  // NOLINT
  online_join_pb_->set_predict_fusion_base_param_w1(rank_result_->rank_rank_info().predict_fusion_base_param_w1());  // NOLINT
  online_join_pb_->set_predict_fusion_base_param_w2(rank_result_->rank_rank_info().predict_fusion_base_param_w2());  // NOLINT
  online_join_pb_->set_predict_fusion_exp_param_w1(rank_result_->rank_rank_info().predict_fusion_exp_param_w1());  // NOLINT
  online_join_pb_->set_predict_fusion_exp_param_w2(rank_result_->rank_rank_info().predict_fusion_exp_param_w2());  // NOLINT
  online_join_pb_->set_next_benefit(rank_result_->rank_price_info().next_benifit());
  online_join_pb_->set_auc_hit_tag(rank_result_->rank_rank_info().auc_hit_tag());
  online_join_pb_->set_pos_in_rank(rank_result_->rank_rank_info().pos_in_rank());
  online_join_pb_->set_fanstop_reco_wtr(rank_result_->predict_origin_info().reco_wtr());
  online_join_pb_->set_reco_vtr(rank_result_->predict_origin_info().reco_vtr());
  online_join_pb_->set_playtime(rank_result_->predict_origin_info().playtime());
  online_join_pb_->set_inner_storewide_live_uplift_prob1(rank_result_->predict_origin_info().inner_storewide_live_uplift_prob1());  // NOLINT
  online_join_pb_->set_inner_storewide_live_uplift_prob2(rank_result_->predict_origin_info().inner_storewide_live_uplift_prob2());  // NOLINT
  online_join_pb_->set_inner_storewide_live_uplift_ratio(rank_result_->predict_origin_info().inner_storewide_live_uplift_ratio());  // NOLINT
  online_join_pb_->set_inner_storewide_merchant_uplift_prob1(rank_result_->predict_origin_info().inner_storewide_merchant_uplift_prob1());  // NOLINT
  online_join_pb_->set_inner_storewide_merchant_uplift_prob2(rank_result_->predict_origin_info().inner_storewide_merchant_uplift_prob2());  // NOLINT
  online_join_pb_->set_nday_refund_rate(rank_result_->predict_origin_info().nday_refund_rate());
  online_join_pb_->set_follow_fnl(rank_result_->predict_origin_info().follow_fnl());
  if (SPDM_enable_set_prerank_trigger_relative_score(session_data_->get_spdm_ctx())) {
    online_join_pb_->set_prerank_trigger_relative_score(rank_result_->rank_rank_info().trigger_relative_score());  // NOLINT
    online_join_pb_->set_prerank_trigger_relative_idx(rank_result_->rank_rank_info().trigger_relative_score_idx());  // NOLINT
  }

  online_join_pb_->mutable_rank_cmd_model()->insert({ "ad_dsp_item_imp_ecom_cmd",
    session_data_->get_spdm_ctx().TryGetString("ad_dsp_item_imp_ecom_cmd", "") });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "c1_direct_ecom_rate",
    session_data_->get_spdm_ctx().TryGetString("c1_direct_ecom_rate", "") });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "c1_nebula_order_paied_cmd",
    session_data_->get_spdm_ctx().TryGetString("c1_nebula_order_paied_cmd", "") });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "c1_order_paid_rate",
    session_data_->get_spdm_ctx().TryGetString("c1_order_paid_rate", "") });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "server_show_merchant_follow_ratio",
    session_data_->get_spdm_ctx().TryGetString("server_show_merchant_follow_ratio", "") });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "c1_merchant_follow_rate",
    session_data_->get_spdm_ctx().TryGetString("c1_merchant_follow_rate", "") });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "ecom_roas_cvr",
    session_data_->get_spdm_ctx().TryGetString("ecom_roas_cvr", "") });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "ad_dsp_merchant_jinniu_ltv",
    session_data_->get_spdm_ctx().TryGetString("ad_dsp_merchant_jinniu_ltv", "") });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "ad_dsp_merchant_reco_ltv",
    session_data_->get_spdm_ctx().TryGetString("ad_dsp_merchant_reco_ltv", "") });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "ad_dsp_server_show_item_imp_inner_ecom",
    session_data_->get_spdm_ctx().TryGetString("ad_dsp_server_show_item_imp_inner_ecom", "") });
  online_join_pb_->set_leverage_score(rank_result_->rank_price_info().leverage_score());
  online_join_pb_->set_mix_unify_gpm(rank_result_->rank_price_info().mix_unify_gpm());
  if (ad_common_ != nullptr) {
    online_join_pb_->set_hc_shift_ratio(ad_common_->ad_price.hc_shift_ratio);
    online_join_pb_->set_industry_hc_score_v2(ad_common_->ad_price.industry_hc_score_v2);
    online_join_pb_->set_traffic_hc_score_v2(ad_common_->ad_price.traffic_hc_score_v2);
    online_join_pb_->set_suppress_hc_score_v2(ad_common_->ad_price.suppress_hc_score_v2);
    online_join_pb_->set_other_hc_score_v2(ad_common_->ad_price.other_hc_score_v2);
    online_join_pb_->set_total_hc_score_v2(ad_common_->ad_price.total_hc_score_v2);
    online_join_pb_->set_mix_unify_bonus_before_fctr(ad_common_->ad_price.mix_unify_bonus_before_fctr);

    if (enable_mix_fctr_) {
      online_join_pb_->set_mix_fctr(ad_common_->base_np.fctr_result);
    }
    if (enable_inner_explore_mix_fctr_ &&
        session_data_->get_pos_manager().IsInnerExplore()) {
      online_join_pb_->set_mix_fctr(ad_common_->base_np.fctr_result);
    }
  }

  if (ad_base_info_->promotion_type() == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
    // falcon::Inc("front_server.ad_pack_online_join_pb.native");
    LOG_EVERY_N(INFO, 10000) << "ad_pack_online_join_pb creative_id = " << ad_base_info_->creative_id()
        << ", merchant_ltv = " << online_join_pb_->merchant_ltv()
        << ", server_show_ctr = " << online_join_pb_->server_show_ctr()
        << ", c1_order_paied = " << online_join_pb_->c1_order_paid()
        << ", wtr = " << online_join_pb_->wtr()
        << ", ad_live_audience = " << online_join_pb_->live_audience()
        << ", shop_jump = " << online_join_pb_->shop_jump();
  }

  // 注意注意注意！！！！
  // 注意注意注意！！！！
  // 注意注意注意！！！！
  // 以下字段的赋值都是带有判断逻辑的，若新加字段没有任何判断逻辑，请写在这段注释之前
  if (rank_result_->predict_origin_info().conv_nextstay() > 0) {
    online_join_pb_->set_conv_nextstay(rank_result_->predict_origin_info().conv_nextstay());
  }
  if (engine_base::IsSingleBidPurchase(target_bid_->ocpx_action_type())) {
    online_join_pb_->set_deep_rate(rank_result_->predict_origin_info().click_purchase_rate_single_bid());
    online_join_pb_->set_click2_deep_rate(
                      rank_result_->predict_origin_info().click2_purchase_rate_single_bid());
  } else {
    online_join_pb_->set_deep_rate(rank_result_->predict_origin_info().deep_rate());
    online_join_pb_->set_click2_deep_rate(rank_result_->predict_origin_info().click2_deep_rate());
  }

  online_join_pb_->set_price_bounded(rank_result_->rank_base_info().price_bounded());

  bool is_bonus = rank_result_->rank_price_info().rank_benifit() - rank_result_->rank_price_info().bonus_cpm()
                  < rank_result_->rank_price_info().next_benifit();
  online_join_pb_->set_is_bonus(is_bonus);
  online_join_pb_->set_general_review(rank_result_->rank_base_info().feature_index_info().general_review());
  online_join_pb_->set_hot_review(rank_result_->rank_base_info().feature_index_info().hot_review());
  online_join_pb_->set_topk_review(rank_result_->rank_base_info().feature_index_info().topk_review());
  if (ad_base_info_->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE &&
      rank_result_->rank_stat_info().live_release_time() > 0) {
    online_join_pb_->set_coldboot_gap_time((base::GetTimestamp() / 1000000 -
    rank_result_->rank_stat_info().live_release_time()) / 60);
  } else if ((ad_base_info_->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO ||
      ad_base_info_->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE) &&
      rank_result_->rank_stat_info().photo_release_time() > 0) {
    online_join_pb_->set_coldboot_gap_time((base::GetTimestamp() / 1000000 -
    rank_result_->rank_stat_info().photo_release_time()) / 60);
  }
  // 注意注意注意！！！！
  // 注意注意注意！！！！
  // 注意注意注意！！！！
  // 以下字段赋值逻辑都是部分类型流量有，部分没有，如果添加新字段对所有流量都生效，请写在这段注释之前
  if (session_data_->TabName() == "explore" || session_data_->TabName() == "nearby") {
    // explore nearby 有
    online_join_pb_->set_xdt_bonus_cpm_config_id(rank_result_->rank_base_info().xdt_bonus_cpm_config_id());
    online_join_pb_->set_xdt_bonus_cpm(rank_result_->rank_price_info().xdt_bonus_cpm());
  }
  if (session_data_->TabName() != "kwai_galaxy" && session_data_->TabName() != "search") {
    // galaxy 没有
    online_join_pb_->set_item_impression_nextstay(
                      rank_result_->predict_origin_info().item_impression_nextstay());
  }
  // 填充 unify_deep_cvr

  SetUnifyDeepCvr();

  SetUnifyRValue();
}

void OnlineJoinPack::SetUnifyRValue() {
  bool is_enable_unify_rvalue = session_data_->
       get_spdm_ctx().TryGetBoolean("enable_unify_rvalue", false);

  if (is_enable_unify_rvalue) {
    online_join_pb_->set_predict_unify_sctr(rank_result_->rank_rank_info().unify_sctr_value());
    online_join_pb_->set_model_sctr_start(
        kuaishou::ad::AdActionType_Name(rank_result_->rank_rank_info().sctr_start()));
    online_join_pb_->set_model_sctr_end(
        kuaishou::ad::AdActionType_Name(rank_result_->rank_rank_info().sctr_end()));
    online_join_pb_->set_predict_unify_ctr(rank_result_->rank_rank_info().ctr_value());
    online_join_pb_->set_model_ctr_start(kuaishou::ad::AdActionType_Name(
            rank_result_->rank_rank_info().ctr_start()));
    online_join_pb_->set_model_ctr_end(kuaishou::ad::AdActionType_Name(
            rank_result_->rank_rank_info().ctr_end()));
    online_join_pb_->set_predict_unify_cvr(rank_result_->rank_rank_info().cvr_value());
    online_join_pb_->set_model_cvr_start(kuaishou::ad::AdActionType_Name(
            rank_result_->rank_rank_info().cvr_start()));
    online_join_pb_->set_model_cvr_end(kuaishou::ad::AdActionType_Name(
            rank_result_->rank_rank_info().cvr_end()));
    online_join_pb_->set_predict_unify_deep_cvr(
        rank_result_->rank_rank_info().deep_cvr_value());
    online_join_pb_->set_deep_model_cvr_start(kuaishou::ad::AdActionType_Name(
            rank_result_->rank_rank_info().deep_cvr_start()));
    online_join_pb_->set_deep_model_cvr_end(kuaishou::ad::AdActionType_Name(
            rank_result_->rank_rank_info().deep_cvr_end()));
    if (!session_data_->get_is_universe_flow()) {
      online_join_pb_->set_ctr_tag(std::to_string(RUnifyTag()));
    }
  } else {
      online_join_pb_->set_predict_unify_ctr(rank_info_->unify_ctr());
      online_join_pb_->set_predict_unify_cvr(rank_info_->unify_cvr());
  }
}

void OnlineJoinPack::SetUnifyDeepCvr() {
  // 除联盟外的 deep_cvr 逻辑
  // set predict_unify_deep_cvr 瀑布流直接取对应 deep_cvr , 滑滑模式则计算二跳到深度转化行为
  bool is_ocpc2 = session_data_->get_is_thanos_request();
  switch (target_bid_->deep_conversion_type()) {
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY:
        if (online_join_params_transparent_->twin_bid_strategy() ==
            kuaishou::ad::AdEnum::MIN_OCPC_DEEP_DEVICE ||
            online_join_params_transparent_->twin_bid_strategy() ==
            kuaishou::ad::AdEnum::ONLY_DEEP_DEVICE) {
          if (is_ocpc2) {
            online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click2_purchase_device());
          } else {
            online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click_purchase_device());
          }
      } else {
        if (is_ocpc2) {
          online_join_pb_->set_predict_unify_deep_cvr(
              rank_result_->predict_origin_info().click2_purchase());
        } else {
          online_join_pb_->set_predict_unify_deep_cvr(
              rank_result_->predict_origin_info().click_purchase_rate());
        }
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY:
      if (is_ocpc2) {
        online_join_pb_->set_predict_unify_deep_cvr(
            rank_result_->predict_origin_info().click2_nextstay());
      } else {
        online_join_pb_->set_predict_unify_deep_cvr(
            rank_result_->predict_origin_info().click_retention_rate());
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT:
      if (is_ocpc2) {
        online_join_pb_->set_predict_unify_deep_cvr(
            rank_result_->predict_origin_info().click2_credit_rate());
      } else {
        online_join_pb_->set_predict_unify_deep_cvr(
            rank_result_->predict_origin_info().click_credit_rate());
      }
      break;
    default:
      break;
  }
}

void OnlineJoinPack::SetUniverseUnifyDeepCvr() {
  // set predict_unify_deep_cvr 瀑布流直接取对应 deep_cvr , 联盟计算二跳到深度转化行为
  switch (target_bid_->deep_conversion_type()) {
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY:
      if (online_join_params_transparent_->twin_bid_strategy() ==
          kuaishou::ad::AdEnum::MIN_OCPC_DEEP_DEVICE ||
          online_join_params_transparent_->twin_bid_strategy() ==
          kuaishou::ad::AdEnum::ONLY_DEEP_DEVICE) {
        online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click2_purchase_device());
      } else {
        online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click2_purchase());
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY:
      online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click2_nextstay());
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT:
      online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click2_credit_rate());
      break;
    default:
      break;
  }
}

void OnlineJoinPack::SetAdDeliverInfoData() {
  online_join_pb_->set_rewarded_mingtou_rerank_bid_tag(
    ad_deliver_info_->online_join_params_transparent().rewarded_mingtou_rerank_bid_tag());
  if (online_join_pb_->rewarded_mingtou_rerank_bid_tag()) {
    online_join_pb_->set_rewarded_mingtou_delta_price(
    ad_deliver_info_->online_join_params_transparent().rewarded_mingtou_delta_price());
  }
  online_join_pb_->set_pre_conv2_purchase(ad_deliver_info_->predict_info().pre_conv2_purchase());
  online_join_pb_->set_bonus_cpm(ad_deliver_info_->bonus_cpm());
  online_join_pb_->set_bonus_cpm_tag(ad_deliver_info_->bonus_cpm_tag());
  online_join_pb_->set_bonus_cpm_project_id(ad_deliver_info_->bonus_cpm_project_id());
  online_join_pb_->set_is_black_bonus_project(ad_deliver_info_->is_black_bonus_project());
  online_join_pb_->mutable_pid_tag()->CopyFrom(ad_deliver_info_->pid_tag());
  online_join_pb_->set_rank_type(ad_deliver_info_->rank_type());
  online_join_pb_->set_pre_conv_rate(ad_deliver_info_->predict_info().pre_conversion_rate());
  online_join_pb_->set_pre_lps_rate(ad_deliver_info_->predict_info().pre_landingpage_rate());
  online_join_pb_->set_pre_delivery_rate(ad_deliver_info_->predict_info().pre_delivery_rate());
  online_join_pb_->set_prerank_cpm_ltr(ad_deliver_info_->online_join_params_transparent().prerank_cpm_ltr());
  online_join_pb_->set_is_auto_expansion(static_cast<int32_t>(ad_deliver_info_->is_expansion()));
  online_join_pb_->set_pre_server_show_cvr(ad_deliver_info_->predict_info().pre_server_show_cvr());
  online_join_pb_->set_pre_click2_conv(ad_deliver_info_->predict_info().pre_click2_conv());
  online_join_pb_->set_pre_click2_lps(ad_deliver_info_->predict_info().pre_click2_lps());
  online_join_pb_->set_prerank_thanos_xdt_order_paied(ad_deliver_info_->predict_info().prerank_thanos_xdt_order_paied());  // NOLINT
  online_join_pb_->set_pre_deep_rate(ad_deliver_info_->predict_info().pre_deep_rate());
  online_join_pb_->set_pre_click2_deep_rate(ad_deliver_info_->predict_info().pre_click2_deep_rate());
  online_join_pb_->set_pre_merchant_follow(ad_deliver_info_->predict_info().pre_merchant_follow());
  online_join_pb_->set_pre_c2_merchant_follow(ad_deliver_info_->predict_info().pre_c2_merchant_follow());
  online_join_pb_->set_pre_purchase(ad_deliver_info_->predict_info().pre_purchase());
  online_join_pb_->set_pre_c2_purchase(ad_deliver_info_->predict_info().pre_c2_purchase());
  online_join_pb_->set_prerank_universe_invoke(ad_deliver_info_->predict_info().prerank_universe_invoke());
  online_join_pb_->set_prerank_universe_c1_event_order(
                       ad_deliver_info_->predict_info().prerank_universe_c1_event_order());
  if (ad_deliver_info_->predict_info().pre_conv_nextstay() > 0) {
    online_join_pb_->set_pre_conv_nextstay(
        ad_deliver_info_->predict_info().pre_conv_nextstay());
  }
  online_join_pb_->set_deep_cvr_click(
        ad_deliver_info_->predict_info().deep_cvr_info().deep_cvr_click());
  online_join_pb_->set_ori_hostpage_pos_id(ad_deliver_info_->pos());
  online_join_pb_->set_prerank_merchant_ltv(ad_deliver_info_->predict_info().prerank_merchant_ltv());
  online_join_pb_->set_landingpage_rate(ad_deliver_info_->predict_info().landingpage_rate());
  if (session_data_ != nullptr &&
      (session_data_->TabName() == "search" || session_data_->get_is_universe_flow())) {
    online_join_pb_->set_pre_ctr(ad_deliver_info_->predict_info().pre_ctr());
  }
  online_join_pb_->set_inner_live_mix_rb(ad_deliver_info_->inner_live_mix_rb());
  online_join_pb_->set_inner_live_mix_cpm(ad_deliver_info_->inner_live_mix_cpm());
  if (SPDM_enable_bad_photo_gather_taxes(session_data_->get_spdm_ctx())) {
    int64_t creative_id = result_->ad_deliver_info().ad_base_info().creative_id();
    auto &tax_ratio_map = *session_data_->mutable_tax_ratio_map();
    auto &tax_amount_map = *session_data_->mutable_tax_amount_map();
    if (tax_ratio_map.count(creative_id)) {
      online_join_pb_->set_tax_rate(tax_ratio_map[creative_id]);
    }
    if (tax_amount_map.count(creative_id)) {
      online_join_pb_->set_tax_amount(tax_amount_map[creative_id]);
    }
  }
  online_join_pb_->set_disable_ad_mark(ad_deliver_info_->disable_ad_mark());
  online_join_pb_->set_plc_biz_type(style_info_->photo_status().plc_biz_type());
}

void OnlineJoinPack::SetAdBaseInfoData() {
  online_join_pb_->set_photo_transform_type(ad_base_info_->photo_transform_type());
  if (ad_base_info_->photo_transform_type() == kuaishou::ad::AdEnum::UNKNOWN_REPLACY_TYPE) {
    online_join_pb_->set_photo_transform_type(ad_base_info_->photo_transform_type());
  }
  online_join_pb_->set_unit_type(ad_base_info_->unit_type());
  online_join_pb_->set_is_30d_sell_flag(ad_base_info_->is_30d_sell_flag());
  online_join_pb_->set_virtual_creative_id(ad_base_info_->virtual_creative_id());
  online_join_pb_->set_bg_cover_id(ad_base_info_->bg_cover_id());
  // online_join_pb_->set_prophet_boost_exp_tag(ad_base_info_->prophet_boost_exp_tag());
  // online_join_pb_->set_prophet_boost_ratio(ad_base_info_->prophet_boost_ratio());
  online_join_pb_->set_cover_title_id(ad_base_info_->cover_title_id());
  online_join_pb_->set_cover_sticker_style_id(ad_base_info_->cover_sticker_style_id());
  online_join_pb_->set_description_title_id(ad_base_info_->description_title_id());
  online_join_pb_->set_display_info(ad_base_info_->display_info());
  online_join_pb_->set_action_bar_display_info(ad_base_info_->client_style_info()
                                                         .landing_page_actionbar_info()
                                                         .action_bar_display_info());
  online_join_pb_->set_new_creative_tag(ad_base_info_->new_creative_tag());
  online_join_pb_->set_new_creative_tag_realtime(ad_base_info_->new_creative_tag_realtime());
  online_join_pb_->set_industry_id(ad_base_info_->new_industry_id());
  if (ad_base_info_->counterpart_photo_id() > 0) {
    online_join_pb_->set_counterpart_photo_id(ad_base_info_->counterpart_photo_id());
    session_data_->dot_perf->Count(1, "replace_photo_id_online_param");
  }
  online_join_pb_->set_industry_id_v3(ad_base_info_->industry_id_v3());

  // 素材类型
  if (ad_base_info_->has_material_info()) {
    if (ad_base_info_->material_info().material_feature_size() > 0) {
      const auto& feature = ad_base_info_->material_info().material_feature(0);
      online_join_pb_->mutable_material_feature()->mutable_material_size()->set_height(
          feature.material_size().height());
      online_join_pb_->mutable_material_feature()->mutable_material_size()->set_width(
          feature.material_size().width());
      online_join_pb_->mutable_material_feature()->set_material_feature_type(
          feature.material_feature_type());
      online_join_pb_->mutable_material_feature()->set_material_time_duration(
          feature.material_time_duration());
      online_join_pb_->set_imp_material_type(ad_base_info_->material_info().derivative_material_type());
      online_join_pb_->set_rule_id(feature.rule_id());
      online_join_pb_->set_material_exp_tag(feature.material_exp_tag());
      online_join_pb_->set_template_id(feature.template_id());
      if (!session_data_->get_is_universe_flow()) {
        // 联盟横版图片来源 1:DSP 原生图片 2:应用宝三图 3:开眼图片
        if (feature.material_feature_type() ==
              kuaishou::ad::AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE) {
            online_join_pb_->set_universe_picture_material_source(static_cast<int32_t>
                                                        (feature.universe_picture_material_source()));
        }
      }
    }
  }
  auto author_id = result_->ad_deliver_info().ad_base_info().author_id();
  // 设置客户 gmv 区间
  auto gmv_interval = ks::engine_base::AuthorGmvIntervalCache::GetInstance()->GetAuthorGmvInterval(author_id);
  online_join_pb_->set_gmv_interval(gmv_interval);
  if (session_data_->get_follow_ids().find(author_id) != session_data_->get_follow_ids().end()) {
    online_join_pb_->set_is_author_fans(1);
    result_->mutable_ad_deliver_info()->mutable_online_join_params_transparent()->set_is_fan_follow(true);
    result_->mutable_ad_deliver_info()->mutable_online_join_params_transparent()->set_is_author_follow(1);
  } else {
    online_join_pb_->set_is_author_fans(2);
    result_->mutable_ad_deliver_info()->mutable_online_join_params_transparent()->set_is_author_follow(2);
  }

  online_join_pb_->set_allow_search_bigv_live_card(
      ad_base_info_->allow_search_bigv_live_card());
  if (session_data_->TabName() == "search" &&
      result_->ad_deliver_info().bidword_params().quick_search() == 1) {
    online_join_pb_->set_search_type(1);
  }
  if (session_data_->TabName() == "search") {
    online_join_pb_->mutable_search_source_trace()->CopyFrom(
        session_data_->get_ad_request()->search_query_transport_info().search_source_traces());
  }
  if (session_data_->get_is_guess_you_like_req() ||
      ::ks::ad_base::IsMallTabReuqest(session_data_->get_sub_page_id()) ||
      ::ks::ad_base::IsZhuanQianTabReuqest(session_data_->get_sub_page_id()) ||
        session_data_->get_page_id() == ks::ad_base::AdPageId::kBuyerHomePagePageId) {
    online_join_pb_->set_merchant_card_type(result_->ad_deliver_info().ad_base_info().merchant_card_type());
  }

  if (session_data_->common_r_->GetIntCommonAttr("enable_clear_playlet_sdpa_info").value_or(0) == 1) {
    // 跳过下面 else if
  } else if (session_data_->common_r_->GetIntCommonAttr("disable_industry_playlet_sdpa_p2p").value_or(0) == 1) {  // NOLINT
    online_join_pb_->set_playlet_name(rank_result_->ad_rank_trans_info().playlet_name());
    online_join_pb_->set_playlet_plot(rank_result_->ad_rank_trans_info().playlet_plot());
  }
  if (ad_common_) {
    online_join_pb_->set_customer_hc_ratio(1.0);  // default 1.0
    if ((ad_common_->get_customer_hc_ratio() - 0.0) > DBL_MIN) {
      online_join_pb_->set_customer_hc_ratio(ad_common_->get_customer_hc_ratio());
    }
  }
}

void OnlineJoinPack::SetInfoFromContextData() {
  online_join_pb_->set_register_timestamp(
      session_data_->get_ad_request()->ad_user_info().register_timestamp());
  online_join_pb_->set_first_active_time_ms(
      session_data_->get_ad_request()->ad_user_info().first_active_time_ms());
  online_join_pb_->set_page_size(session_data_->get_ad_request()->page_size());
  online_join_pb_->set_cold_start(session_data_->get_ad_request()->reco_request_info().pcursor().length());
  online_join_pb_->set_interactive_form(
      static_cast<int64>(ks::ad_base::GetInteractiveForm(*(session_data_->get_ad_request()))));
  online_join_pb_->set_refresh_direction(
      static_cast<int64>(ks::ad_base::GetRefreshDirection(*(session_data_->get_ad_request()))));
  online_join_pb_->set_last_pv_timestamp(
      session_data_->get_ad_request()->front_internal_data().last_explore_pv_timestamp());
  online_join_pb_->set_last_page_size(
      session_data_->get_ad_request()->front_internal_data().last_explore_pv_page_size());
  online_join_pb_->set_last_ad_pos(
      session_data_->get_ad_request()->front_internal_data().last_explore_pv_last_ad_pos());
  online_join_pb_->set_last_first_screen_ad_timestamp(
      session_data_->get_ad_request()->front_internal_data().first_screen_ad_shw_timestamp());
  online_join_pb_->set_browse_ad_interval_seconds(
      session_data_->get_ad_request()->front_internal_data().browse_ad_interval_seconds());
  online_join_pb_->set_browse_ads_num(session_data_->get_ad_request()
                                    ->front_internal_data().browse_ads_num());
  online_join_pb_->set_sdk_version(session_data_->get_ad_request()
                                    ->universe_ad_request_info().sdk_version());
  online_join_pb_->set_sdk_type(session_data_->get_ad_request()
                                    ->universe_ad_request_info().sdk_type());
  online_join_pb_->set_protocol_version(session_data_->get_ad_request()
                                    ->universe_ad_request_info().protocol_version());
  online_join_pb_->set_native_fiction_user_server_crowd_tag(
      session_data_->get_ad_request()->ad_user_info().native_fiction_user_group_tag());

  for (const auto& predict_score : session_data_->get_ad_request()->predict_score()) {
    online_join_pb_->add_predict_score()->CopyFrom(predict_score);
  }
  if (session_data_->TabName() != "kwai_galaxy" && session_data_->TabName() != "search") {
      online_join_pb_->set_enable_person_ad_pos_gap_exptag(0);
  }
  online_join_pb_->set_sess_engage_one_tenth(0);
  online_join_pb_->set_sess_engage_two_tenth(0);
  online_join_pb_->set_sess_engage_third_tenth(0);
  online_join_pb_->set_sess_engage_avg_play_time(0);
  online_join_pb_->set_sess_engage_effect_count(0);
  online_join_pb_->set_reco_fresh_type_bit(
      session_data_->get_ad_request()->reco_user_info().reco_fresh_type_bit());
  online_join_pb_->set_is_live_stream_core_user(session_data_->get_ad_request()->is_live_stream_core_user());
  online_join_pb_->set_order_conv_type(session_data_->get_ad_request()->ad_user_info().order_conv_type());
  online_join_pb_->set_buyer_effective_type(
      session_data_->get_ad_request()->ad_user_info().buyer_effective_type());
  online_join_pb_->set_outer_ecom_conv_type(
      session_data_->get_ad_request()->ad_user_info().outer_ecom_conv_type());
  online_join_pb_->set_user_refund_score(session_data_->get_ad_request()->ad_user_info().user_refund_score());
  online_join_pb_->set_user_badcomn_score(
      session_data_->get_ad_request()->ad_user_info().user_badcomn_score());
  online_join_pb_->set_user_level_v2_risk(
      session_data_->get_ad_request()->ad_user_info().user_level_v2_risk());
  online_join_pb_->set_outer_adload_group_level(std::to_string(
      session_data_->get_ad_request()->ad_user_info().outer_adload_group_level()));
  online_join_pb_->set_nearline_exp_tag(
      SPDM_inner_nearline_exp_name(session_data_->get_spdm_ctx()));
  if (session_data_->get_pos_manager().IsInnerExplore() ||
      ad_base::IsWanhe(session_data_->get_sub_page_id())) {
      online_join_pb_->set_relative_photo_id(session_data_->get_ad_request()->relative_photo_id());
  }
  if (SPDM_enableFillInnerExploreUserTag() && session_data_->get_pos_manager().IsInnerExplore()) {
    online_join_pb_->set_inner_explore_user_tag(
      session_data_->get_ad_request()->ad_user_info().inner_explore_user_tag());
  }
  online_join_pb_->set_playlet_user_tag(
  session_data_->get_ad_request()->ad_user_info().smart_offer_user_tag());
  online_join_pb_->set_playlet_control_score(
  session_data_->get_ad_request()->ad_user_info().offer_control_score());
  online_join_pb_->set_playlet_treatment_score(
  session_data_->get_ad_request()->ad_user_info().offer_treatment_score());
}

void OnlineJoinPack::SetTargetBidData() {
  const auto &unit = style_info_->unit();
  online_join_pb_->set_cpa_ratio(1.0);
  online_join_pb_->set_deep_risk_control_rate(
      target_bid_->deep_risk_control_rate());
  online_join_pb_->set_deep_flow_control_rate(target_bid_->deep_flow_control_rate());
  online_join_pb_->set_deep_min_coef(target_bid_->deep_min_coef());
  online_join_pb_->set_deep_min_bid_coef(target_bid_->deep_min_bid_coef());
  if (online_join_params_transparent_->speed_type() == kuaishou::ad::AdEnum::SPEED_NO_BID ||
      ad_base_info_->bid_type() == kuaishou::ad::AdEnum::MCB) {  // get from target
    online_join_pb_->set_roi_ratio(target_bid_->roi_ratio());
    // 全站计划下的 nobid 单元不用 target 也不用正排出价，而是填充计划出价
    if (style_info_->unit().ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS &&
        style_info_->has_ecom_hosting_project()) {
      bool enable_nobid_store_wide_roi_fix =
        SPDM_enable_nobid_store_wide_roi_fix(session_data_->get_spdm_ctx());
      if (enable_nobid_store_wide_roi_fix
          && style_info_->ecom_hosting_project().roi_ratio() == 0) {
        online_join_pb_->set_roi_ratio(target_bid_->roi_ratio());
      } else {
        online_join_pb_->set_roi_ratio(style_info_->ecom_hosting_project().roi_ratio());
      }
    }
  } else {  // get from index
    online_join_pb_->set_roi_ratio(unit.roi_ratio());
  }
}

void OnlineJoinPack::SetAdDataV2Data() {
  // 设置下载类中间页自动填充埋点
  const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
  online_join_pb_->set_is_download_landing_page_mould(
      ad_data_v2->h5_control_info().is_download_landing_page_mould());
  if (ad_data_v2->h5_control_info().is_download_landing_page_mould()) {
    falcon::Inc("front_server.is_download_landing_page_mould_res");
  }
  // 设置下载中间页 url type
  if (result_->ad_deliver_info().ad_base_info().campaign_type()
      == ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_APP) {
    int32 download_mid_url_type = -1;
    if (ad_data_v2->has_h5_url()) {
      const auto& h5_url = ad_data_v2->h5_url();
      // 系统提供的模版 url
      const auto& download_mid_page_url_type_map = FrontKconfUtil::kuaishouDownloadMidPageUrlTypeMap();
      const auto& iter = download_mid_page_url_type_map->find(h5_url);
      if (iter != download_mid_page_url_type_map->end()) {
        download_mid_url_type = iter->second;
      } else {
        download_mid_url_type = 10;
      }
    }
    online_join_pb_->set_download_mid_url_type(download_mid_url_type);
    falcon::Inc(absl::StrCat("front_server.download_mid_url_type_", download_mid_url_type).data());
  }

  // ad_server 填充空，此处保持一致，填充为空
  online_join_pb_->set_display_info_origin(std::string());
  online_join_pb_->mutable_knews_params_transparent()->set_inspire_action_type(
      ad_data_v2->inspire_ad_info().inspire_action().type());
  // web card type
  const auto& origin_style_info = ad_data_v2->origin_style_info();
  if (origin_style_info.ad_prefer_style_size() > 0) {
    online_join_pb_->set_web_card_type(origin_style_info.ad_prefer_style(0).resource_type());
  }
  online_join_pb_->set_magicsite_page_form(style_info_->magic_site_page().page_form());
  // playlet smart offers
  online_join_pb_->set_has_smart_offer(ad_data_v2->has_smart_offer());
  online_join_pb_->set_smart_offer_value(ad_data_v2->smart_offer_value());
  online_join_pb_->set_smart_offer_result(ad_data_v2->smart_offer_result());
  online_join_pb_->set_smart_offer_lessons(ad_data_v2->smart_offer_lessons());
  online_join_pb_->set_playlet_series_pay_mode(ad_data_v2->playlet_series_pay_mode());
  online_join_pb_->set_playlet_series_template_id(ad_data_v2->playlet_series_template_id());
  online_join_pb_->set_smart_offer_multi_random_sample(ad_data_v2->smart_offer_multi_random_sample());
  if (ad_data_v2->has_smart_offer()) {
    if (ad_data_v2->smart_offer_value() == 0 || ad_data_v2->smart_offer_result() == 0) {
      session_data_->dot_perf->Count(1, "step2_HasSmartOfferInfo", "failed",
                            absl::StrCat(result_->ad_deliver_info().ad_base_info().ocpc_action_type()),
                            absl::StrCat(result_->ad_deliver_info().ad_base_info().deep_conversion_type()),
                            absl::StrCat(ad_data_v2->smart_offer_value()) + "|" +
                            absl::StrCat(ad_data_v2->smart_offer_result()));
    } else {
      session_data_->dot_perf->Count(1, "step2_HasSmartOfferInfo", "success",
                            absl::StrCat(result_->ad_deliver_info().ad_base_info().ocpc_action_type()),
                            absl::StrCat(result_->ad_deliver_info().ad_base_info().deep_conversion_type()));
    }
  }
  if (SPDM_enable_report_native_fiction_info(session_data_->get_spdm_ctx())) {
    online_join_pb_->set_native_fiction_algo_crowd_tag(ad_data_v2->native_fiction_algo_crowd_tag());
    online_join_pb_->set_common_subsidy_strategy_ratio(ad_data_v2->common_subsidy_strategy_ratio());
  }
  if (SPDM_enableFictionReportExtData()) {
    online_join_pb_->mutable_strategy_report_ext_data()->CopyFrom(ad_data_v2->strategy_report_ext_data());
  }
}

void OnlineJoinPack::SetAdxData() {
  if (result_->ad_source_type() == kuaishou::ad::ADX) {
    online_join_pb_->set_adx_tag_id(ad_base_info_->tag_id());
    online_join_pb_->set_adx_winprice(GetAdxWinPrice(*result_));
  }
  online_join_pb_->set_adx_req_admit_fail_reason(
      session_data_->get_ad_response()->adx_req_admit_fail_reason());
}

void OnlineJoinPack::SetEnvInfo() {
  online_join_pb_->set_kws_env(ks::ad_base::util::GeAdServiceVersion().kws_env());
  online_join_pb_->set_front_hostname(
                            ks::ad_base::util::GeAdServiceVersion().kws_env() + '/' +
                            serving_base::GetHostName() + '/' +
                            ks::ad_base::util::GetMyPodName());
}

void OnlineJoinPack::SetExploreData() {
  if (SPDM_enableInspireBrandPk()) {
    SetBrandPKData();
  } else {
    if (result_->ad_source_type() == kuaishou::ad::BRAND) {
      const auto& brand_online_join_params = result_->ad_deliver_info().online_join_params();
      ::google::protobuf::util::JsonStringToMessage(brand_online_join_params, &brand_online_join_pb_);
      online_join_pb_->set_brand_net_cpm(brand_online_join_pb_.brand_net_cpm());
      online_join_pb_->set_brand_pesudo_cpm(brand_online_join_pb_.brand_pesudo_cpm());
      online_join_pb_->set_dsp_pk_ecpm(brand_online_join_pb_.dsp_pk_ecpm());
      online_join_pb_->set_dsp_pk_price(brand_online_join_pb_.dsp_pk_price());
      online_join_pb_->set_dsp_second_ecpm(brand_online_join_pb_.dsp_second_ecpm());
    }
  }

  online_join_pb_->set_grant_browse_type(
      session_data_->get_ad_request()->reco_request_info().grant_browse_type());
  // 样式物料 id，业务原因，名字是反的
  const auto& creative_id = ad_base_info_->creative_id();
  if (const auto* style_pack_result = FindOrNull(session_data_->get_style_data_pack_results(), creative_id)) {
    if (style_pack_result->style_material_id > 0) {
      online_join_pb_->set_style_material_lp_id(style_pack_result->style_material_id);
    }
    if (style_pack_result->lp_style_material_id > 0) {
      online_join_pb_->set_style_material_id(style_pack_result->lp_style_material_id);
    } else {
      online_join_pb_->set_adv_lp_use_default(true);
    }
  }
  auto ad_rank_trans_info = online_join_pb_->mutable_ad_rank_trans_info();
  ad_rank_trans_info->set_next_cpm(rank_result_->rank_price_info().next_cpm());
  ad_rank_trans_info->set_next_benifit(rank_result_->rank_price_info().next_benifit());
  session_data_->dot_perf->Count(1, "online_grant_browse_type", online_join_pb_->grant_browse_type());
}

void OnlineJoinPack::SetSearchData() {
  // 样式物料 id，业务原因，名字是反的
  const auto& creative_id = ad_base_info_->creative_id();
  if (const auto* style_pack_result = FindOrNull(session_data_->get_style_data_pack_results(), creative_id)) {
    if (style_pack_result->style_material_id > 0) {
      online_join_pb_->set_style_material_lp_id(style_pack_result->style_material_id);
    }
    if (style_pack_result->lp_style_material_id > 0) {
      online_join_pb_->set_style_material_id(style_pack_result->lp_style_material_id);
    } else {
      online_join_pb_->set_adv_lp_use_default(true);
    }
  }
  online_join_pb_->set_search_cover_replace_type(
                              result_->ad_deliver_info().ad_base_info().search_cover_replace_type());
  online_join_pb_->set_cover_tag(style_info_->creative().creative_support_info().cover_tag());
  if (result_->ad_deliver_info().ad_kbox_info().kbox_type() ==
      kuaishou::ad::AdKboxInfo_AdKboxType_ITEM_KBOX) {
    online_join_pb_->set_is_show_item_kbox(true);
    if (session_data_->get_mixed_goods_live_combo()) {
      online_join_pb_->set_is_mixed_goods_live(true);
    }
  }
  if (result_->ad_deliver_info().ad_kbox_info().kbox_type() ==
      kuaishou::ad::AdKboxInfo_AdKboxType_LIVE_KBOX) {
    online_join_pb_->set_is_show_live_kbox(true);
  }
  if (result_->ad_deliver_info().ad_base_info().search_app_card().enable()) {
    online_join_pb_->set_is_search_app_card(true);
  }
  if (result_->ad_deliver_info().ad_base_info().search_form_submit_card().enable()) {
    online_join_pb_->set_is_search_form_submit_card(true);
  }
  online_join_pb_->mutable_cross_feature_info()->CopyFrom(
                              rank_result_->ad_rank_trans_info().cross_feature_info());
  online_join_pb_->set_search_card_style_type(
                  result_->ad_deliver_info().ad_base_info().search_card_style_type());
  session_data_->dot_perf->Count(1, "search_card_style_type",
                        kuaishou::ad::AdEnum_SearchCardStyleType_Name(
                          online_join_pb_->search_card_style_type()));
  if (online_join_pb_->search_card_style_type() != kuaishou::ad::AdEnum::DEFAULT_FEED_TYPE) {
    session_data_->mutable_ad_select_stage_infos()->AddAdSearchCardType(
        rank_result_, ad_base_info_->creative_id(), online_join_pb_->search_card_style_type(),
        result_->ad_deliver_info().ad_queue_type() == kuaishou::ad::AdEnum_AdQueueType_HARD_AD_QUEUE);
  }
  if (online_join_pb_->search_card_style_type() != kuaishou::ad::AdEnum::DEFAULT_FEED_TYPE) {
    session_data_->set_enable_combo_search_req_ad_pack(true);
  }
  online_join_pb_->set_search_sub_keyword(result_->ad_deliver_info().ad_base_info().search_sub_keyword());
  online_join_pb_->set_search_kbox_pos(result_->ad_deliver_info().ad_kbox_info().pos());
  online_join_pb_->set_search_sctr1(online_join_params_transparent_->search_sctr1());
  online_join_pb_->set_search_sctr2(online_join_params_transparent_->search_sctr2());
  online_join_pb_->set_search_bid_boost(online_join_params_transparent_->search_bid_boost());
  online_join_pb_->set_hit_activity_id(online_join_params_transparent_->hit_activity_id());
  const auto search_source = session_data_->get_ad_request()->search_info().search_source();
  if (search_source ==
      kuaishou::ad::SearchInfo_SearchSource_INNER_STREAM_COMBO_SEARCH) {
    auto& transport_info =
      session_data_->get_ad_request()->search_query_transport_info();
    auto& inner_stream_params =
      session_data_->get_ad_request()->search_query_transport_info().inner_stream_params();
    online_join_pb_->set_search_inner_stream_page(inner_stream_params.page());
    online_join_pb_->set_search_inner_stream_pos(inner_stream_params.pos());
    online_join_pb_->set_search_inner_stream_refer_photo(transport_info.refer_photo_id());
    int64_t refer_live_id = 0;
    if (absl::SimpleAtoi(inner_stream_params.refer_live_id(), &refer_live_id)) {
      online_join_pb_->set_search_inner_stream_refer_live(refer_live_id);
    } else {
      LOG_EVERY_N(WARNING, 100000) << " failed to parse search refer_live_id "
                                   << inner_stream_params.refer_live_id();
    }
    if (inner_stream_params.refer_search_session_id().size() < 80) {
      online_join_pb_->set_search_inner_stream_refer_session(
        inner_stream_params.refer_search_session_id());
    }
    online_join_pb_->set_search_inner_stream_refer_user(
      std::to_string(inner_stream_params.refer_user_id()));
  }
  if (SPDM_enable_search_dup_between_inner_stream(session_data_->get_spdm_ctx()) &&
      ad_request_->search_info().combo_search_params().strategy_data().size() > 0) {
    auto iter = ad_request_->search_info().combo_search_params().strategy_data().find("signalParams");
    if (iter != ad_request_->search_info().combo_search_params().strategy_data().end()) {
      kuaishou::search::SeClientRealTimeActionList inner_stream_params_pb;
      if (ks::ad_base::pb_utils::ParseBase64PB(inner_stream_params_pb, iter->second)) {
        int64_t error_signal_type_cnt = 0;
        int64_t error_decrypt_photo_id_cnt = 0;
        int64_t error_refer_session_id_cnt = 0;
        int64_t error_decrypt_live_stream_id_cnt = 0;
        if (inner_stream_params_pb.ad_action_list_size() > 0) {
          for (auto& action_list : inner_stream_params_pb.ad_action_list()) {
            if (action_list.signal_type() != "AD_SHOW") {
              error_signal_type_cnt++;
              continue;
            }
            base::Json extra{base::StringToJson(action_list.extra())};
            std::string engine_ext = extra.GetString("engine_ext", "");
            base::Json engine_ext_json{base::StringToJson(engine_ext)};
            std::string imp_refer_session_id = engine_ext_json.GetString("refer_session_id", "");
            if (imp_refer_session_id == "") {
              error_refer_session_id_cnt++;
            }
            auto combo_siganl_param = online_join_pb_->add_combo_siganl_params();
            combo_siganl_param->set_refer_session_id(imp_refer_session_id);
            combo_siganl_param->set_signal_type(action_list.content_type());
            combo_siganl_param->set_session_id(action_list.session_id());
            int64_t parse_photo_id = 0;
            int64_t parse_live_stream_id = 0;
            // content_type: 1 视频, 2 直播
            if (action_list.content_type() == 1) {
              if (!utility::DecryptPhotoId(action_list.content_id(), &parse_photo_id)) {
                error_decrypt_photo_id_cnt++;
                continue;
              }
              combo_siganl_param->set_signal_id(parse_photo_id);
            } else if (action_list.content_type() == 2) {
              if (!utility::DecryptLiveId(action_list.content_id(), &parse_live_stream_id)) {
                error_decrypt_live_stream_id_cnt++;
                continue;
              }
              combo_siganl_param->set_signal_id(parse_live_stream_id);
            }
          }
        }
        session_data_->dot_perf->Interval(error_signal_type_cnt,
                                          "Combo_search_params_error", "error_signal_type_cnt");
        session_data_->dot_perf->Interval(error_refer_session_id_cnt,
                                          "Combo_search_params_error", "error_refer_session_id_cnt");
        session_data_->dot_perf->Interval(error_decrypt_photo_id_cnt,
                                          "Combo_search_params_error", "error_decrypt_photo_id_cnt");
        session_data_->dot_perf->Interval(error_decrypt_live_stream_id_cnt,
                                          "Combo_search_params_error", "error_decrypt_live_stream_id_cnt");
      }
    }
  }
  const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
  const auto& product_info = ad_data_v2->origin_style_info().merchant_info().product_info();
  if (!product_info.icon_url().empty()
    && product_info.price() > 0
    && !product_info.item_name().empty()) {
    online_join_pb_->set_is_search_item_card(true);
  }
  // 搜索激励任务埋点信息
  int32_t inspire_task_id =
    session_data_->Attr(CommonIdx::search_inspire_task_id).GetIntValue().value_or(0);
  if (inspire_task_id > 0) {
    online_join_pb_->set_incentive_task_id(inspire_task_id);
  }
  // 导流埋点信息
  if (session_data_->get_ad_request()->search_query_transport_info().has_search_ad_diversion_info()) {
    const auto& source_trace = session_data_->get_ad_request()
                                   ->search_query_transport_info()
                                   .search_ad_diversion_info()
                                   .source_trace();
    online_join_pb_->set_diversion_is_commerce(source_trace.is_commerce());
    online_join_pb_->set_diversion_recall_source(source_trace.recall_source());
    online_join_pb_->set_diversion_rank_model(source_trace.rank_model());
    online_join_pb_->set_diversion_ecpm(source_trace.ecpm());
    online_join_pb_->set_diversion_xtr(source_trace.xtr());
    online_join_pb_->set_diversion_mixrank_score(source_trace.mixrank_score());
    online_join_pb_->set_diversion_source(source_trace.diversion_source());
    online_join_pb_->set_diversion_boost_type(source_trace.boost_type());
    online_join_pb_->set_diversion_extend_info(source_trace.extend_info());
  }
}

void OnlineJoinPack::SetSplashData() {
  if (IsInnerRtbGray(*style_info_)) {
    const auto& related_id = style_info_->creative().creative_support_info().auto_deliver_related_id();
    const auto& iter = session_data_->get_rtb_splash_derived_item_type_map().find(related_id);
    if (iter != session_data_->get_rtb_splash_derived_item_type_map().end()) {
      online_join_pb_->set_derived_item_type(iter->second);
      session_data_->dot_perf->Count(1, "online_join_pb_set_derived_item_type");
    }
  }
}

void OnlineJoinPack::SetNearbyData() {
  if (ks::ad_base::IsInspireLive(session_data_->get_sub_page_id(), session_data_->get_ad_request())) {
    online_join_pb_->set_is_inspire_live_cache_res(session_data_->get_is_inspire_live_cached());
  }
}

void OnlineJoinPack::SetGalaxyData() {
  if (SPDM_enableInspireBrandPk()) {
    SetBrandPKData();
  }

  if (SPDM_enableLogTvCopyright()) {
    int32 tv_copyright = session_data_->get_ad_request()->corona_video_info().tv_copyright();
    online_join_pb_->set_tv_copyright(tv_copyright);
  }
  // kenws 设置 is_universe_fake_use
  if (session_data_->get_is_matrix_flow()) {
    const auto &user_info = session_data_->get_ad_request()->ad_user_info();
    online_join_pb_->set_is_universe_fake_user(user_info.is_universe_fake_user());
    online_join_pb_->mutable_screen_size()->set_width(
                        session_data_->UniverseRequest().device_info().screen_size().width());
    online_join_pb_->mutable_screen_size()->set_height(
                        session_data_->UniverseRequest().device_info().screen_size().height());
    online_join_pb_->mutable_knews_params_transparent()->set_knews_user_id(
                        session_data_->UniverseRequest().user_info().app_user_id());
    online_join_pb_->set_matrix_app_vip_type(
        session_data_->UniverseRequest().user_info().matrix_app_vip_type());
    if (!session_data_->get_ad_request()->kkd_ext().empty()) {
      const base::Json ext_json(base::StringToJson(session_data_->get_ad_request()->kkd_ext()));
      const base::Json* app_store_info = ext_json.Get("appStoreInfo");
      bool support  = false;
      int32_t detail_style = 0;
      if (app_store_info && app_store_info->IsObject()) {
        support = app_store_info->GetBoolean("support", false);
        detail_style = atoi(app_store_info->GetString("detailStyle", "0").c_str());
      }
      if (support) {
        online_join_pb_->set_minimarket_page(detail_style);
      }
    }
    if (session_data_->get_is_matrix_splash_flow()) {
      online_join_pb_->set_is_realtime(
          session_data_->get_ad_request()->universe_ad_request_info().splash_req_info().is_realtime());
    }
    auto iter = session_data_->get_style_material_map().find(ad_base_info_->creative_id());
    if (iter != session_data_->get_style_material_map().end() &&
        iter->second.size() > 0) {
      online_join_pb_->set_style_material_id(iter->second[0].id);
      online_join_pb_->set_bind_account_id(iter->second[0].bind_account_id);
    }
  }
}

void OnlineJoinPack::SetBehaviorIntent() {
  // set behavior intent
  int64_t hit_behavior_intent = 0;
  const auto& style_target = style_info_->target();
  if (session_data_->get_ad_request()->ad_user_info().special_field().behavior_interest_changed()) {
    hit_behavior_intent |= 1;
  }
  if (style_target.extend_fields().behavior_interest_keyword_size() > 0) {
    hit_behavior_intent |= 1 << 1;
  }
  const auto& target_tag = result_->ad_deliver_info().target_hit_tag();
  if (target_tag.behavior_interest_size() > 0) {
    hit_behavior_intent |= 1 << 2;
  }
  online_join_pb_->set_hit_behavior_intent(hit_behavior_intent);
}

void OnlineJoinPack::SetCorePopulation() {
  const auto &user_info = session_data_->get_ad_request()->ad_user_info();
  const auto &author_list = user_info.core_population_label();
  // 广告主快手 id
  auto author_id = GetAuthorID(session_data_, *style_info_);
  for (auto aid : author_list) {
    if (aid == author_id) {
      // 实际命中高质量涨粉
      online_join_pb_->set_core_population_label(style_info_->target().core_population_label());
      online_join_pb_->set_core_population_category_ids(style_info_->target().core_population_category_ids());
      break;
    }
  }
  // 空值检查
  if (online_join_pb_->core_population_category_ids().empty()) {
    online_join_pb_->set_core_population_category_ids("[]");
  }
  if (online_join_pb_->core_population_label().empty()) {
    online_join_pb_->set_core_population_label("[]");
  }
  int user_tag = -1;
  const std::string &sensitive_user_redis_result =
    session_data_->get_ad_request()->diversity_sensitive_user_info();
  if (sensitive_user_redis_result.size() > 0) {
    base::Json imp_json(base::StringToJson(sensitive_user_redis_result));
    if (imp_json.IsObject()) {
      user_tag = imp_json.GetInt("is_dpp_crowd", -1);
    }
  }
  if (user_tag > 0) {
    online_join_pb_->set_diversity_user_tag(user_tag);
  }
}

void OnlineJoinPack::SetMerchantInfo() {
  auto campaign_type = style_info_->campaign().type();
  using kuaishou::ad::AdEnum;
  if (campaign_type != AdEnum::TAOBAO
      && campaign_type != AdEnum::LIVE_STREAM_PROMOTE
      && campaign_type != AdEnum::MERCHANT_RECO_PROMOTE
      && campaign_type != AdEnum::AD_POP_RECRUIT_LIVE) {
    return;
  }
  if (style_info_->has_unit_small_shop_merchant_support_info()) {
    online_join_pb_->set_merchant_spu_id(style_info_->unit_small_shop_merchant_support_info().spu_id());
    online_join_pb_
        ->set_creative_build_type(style_info_->unit_small_shop_merchant_support_info().creative_build_type());  // NOLINT
    // 直营电商（金牛）下发 pay_type 付款方式字段
    online_join_pb_->set_ecom_product_pay_type(
        style_info_->unit_small_shop_merchant_support_info().pay_type());
    online_join_pb_->set_product_label_type(
        style_info_->unit_small_shop_merchant_support_info().product_label_type());
    online_join_pb_->set_product_label_end_time(
        style_info_->unit_small_shop_merchant_support_info().product_label_end_time());
  }

  if (style_info_->has_small_shop_spu()) {
    online_join_pb_->set_merchant_index_time(style_info_->small_shop_spu().create_time());
  }
  if (style_info_->has_live_stream_user_info()) {
    online_join_pb_->set_account_is_live(style_info_->live_stream_user_info().is_live());
  }
  if (style_info_->has_industry_v3()) {
    online_join_pb_->set_first_industry_id_v3(style_info_->industry_v3().parent_id());
  }
  const auto& ad_user_info_ = session_data_->get_ad_request()->ad_user_info();
  if (ad_user_info_.has_hist_gmv()) {
    online_join_pb_->set_hist_gmv(ad_user_info_.hist_gmv());
  }

  if (session_data_->TabName() != "universe") {
    const auto ad_shop_id = result_->ad_deliver_info().ad_base_info().author_id();
    const auto &buy_list = ad_user_info_.purchase_shop_id_list();

    online_join_pb_->set_is_shop_new_customer(true);
    for (int i = 0; i < buy_list.size(); i += 2) {
      const auto buy_shop_id = ad_user_info_.purchase_shop_id_list(i);
      if (ad_shop_id == buy_shop_id) {
        online_join_pb_->set_is_shop_new_customer(false);
        break;
      }
    }
  }
}

void OnlineJoinPack::SetPecCouponInfo() {
  const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
  const auto& origin_style_info = ad_data_v2->origin_style_info();
  auto& coupon_info = origin_style_info.merchant_info().coupon_info();
  online_join_pb_->set_coupon_template_id(coupon_info.coupon_template_id());
  online_join_pb_->set_coupon_type(coupon_info.coupon_type());
  online_join_pb_->set_coupon_threshold(coupon_info.coupon_rule().threshold());
  online_join_pb_->set_discount_amount(coupon_info.coupon_rule().discount_amount());
  online_join_pb_->set_reduce_amount(coupon_info.coupon_rule().reduce_amount());
  online_join_pb_->set_capped_amount(coupon_info.coupon_rule().capped_amount());
  auto author_id = result_->ad_deliver_info().ad_base_info().author_id();

  if (FrontKconfUtil::pecMultiCouponConfig()->data().IsAuthorHit(author_id) > 0) {
    online_join_pb_->set_is_pec_coupon_white_author(true);
    session_data_->dot_perf->Count(1, "pec_coupon.hit_author_count");
  } else {
    online_join_pb_->set_is_pec_coupon_white_author(false);
  }

  if (FrontKconfUtil::livePecMultiCouponConfig()->data().IsAuthorHit(author_id) > 0) {
    online_join_pb_->set_is_live_pec_coupon_white_author(true);
    session_data_->dot_perf->Count(1, "pec_coupon.live_hit_author_count");
  } else {
    online_join_pb_->set_is_live_pec_coupon_white_author(false);
  }

  if (FrontKconfUtil::p2lPecMultiCouponConfig()->data().IsAuthorHit(author_id) > 0) {
    online_join_pb_->set_is_p2l_pec_coupon_white_author(true);
    session_data_->dot_perf->Count(1, "pec_coupon.p2l_hit_author_count");
  } else {
    online_join_pb_->set_is_p2l_pec_coupon_white_author(false);
  }
}

void OnlineJoinPack::SetSkipNewUserTag() {
  bool enable = false;
  enable = (session_data_->get_app_id() == "kuaishou" || session_data_->get_app_id() == "kuaishou_nebula");
  if (enable) {
    std::string extra_tag;
    if (session_data_->get_ad_request()->front_internal_data().is_nr_tnu_user()) {
      online_join_pb_->add_skip_new_user_tag(2);
      absl::StrAppend(&extra_tag, "2");
    }
    if (session_data_->get_ad_request()->front_internal_data().is_new_device()) {
      online_join_pb_->add_skip_new_user_tag(1);
      absl::StrAppend(&extra_tag, extra_tag.empty() ? "" : "_", "1");
    }
    if (session_data_->get_ad_request()->front_internal_data().is_reflux_device() ||
        session_data_->get_ad_request()->front_internal_data().is_new_device_reflux_user()) {
      online_join_pb_->add_skip_new_user_tag(3);
      absl::StrAppend(&extra_tag, extra_tag.empty() ? "" : "_", "3");
    }
    if (session_data_->get_ad_request()->front_internal_data().is_new_user()) {
      online_join_pb_->add_skip_new_user_tag(4);
      absl::StrAppend(&extra_tag, extra_tag.empty() ? "" : "_", "4");
    }
  }
}

void OnlineJoinPack::SetSmartComputeScore() {
  auto iter = FrontKconfUtil::dcaf_biz_sub_page_id()->find(absl::StrCat(session_data_->get_sub_page_id()));
  if (iter != FrontKconfUtil::dcaf_biz_sub_page_id()->end()) {
    ModelRespScore scores_struct;
    auto* instance = ks::front_server::SmartComputePowerInnerUtil::GetInstance();
    if (instance->GetRouterScore(session_data_->get_llsid(), &scores_struct)) {
      auto fill_score = [](const std::vector<double>& scores_list,
                           ::google::protobuf::RepeatedField<double>* dst_field) {
        if (dst_field != nullptr) {
          for (const auto& score : scores_list) { dst_field->Add(score); }
        }
      };
      fill_score(scores_struct.unify_scores, online_join_pb_->mutable_dcaf_rank_unify_cmd());
      fill_score(scores_struct.outer_hard_scores, online_join_pb_->mutable_dcaf_rank_outer_hard_cmd());
      fill_score(scores_struct.inner_soft_photo_scores,
                  online_join_pb_->mutable_dcaf_rank_inner_soft_photo_cmd());
      fill_score(scores_struct.inner_hard_photo_scores,
                  online_join_pb_->mutable_dcaf_rank_inner_hard_photo_cmd());
    }

    auto print_repeated = [] (const ::google::protobuf::RepeatedField< double >& array) {
      std::stringstream ss;
      ss << "[";
      for (const auto& item : array) {
        ss << item << " ";
      }
      ss << "]";
      return ss.str();
    };
    LOG_EVERY_N(INFO, 10000) << "zt-test-online join smart compute score. "
                             << " llsid: " << session_data_->get_llsid()
                             << ", unify: " << print_repeated(online_join_pb_->dcaf_rank_unify_cmd())
                             << ", outer_hard: "
                             << print_repeated(online_join_pb_->dcaf_rank_outer_hard_cmd())
                             << ", inner_soft_photo: "
                             << print_repeated(online_join_pb_->dcaf_rank_inner_soft_photo_cmd())
                             << ", inner_hard_photo: "
                             << print_repeated(online_join_pb_->dcaf_rank_inner_hard_photo_cmd());
  }
}

void OnlineJoinPack::SetForceExploreData() {
  auto& front_force_explore_log_map = *session_data_->mutable_front_force_explore_log_map();
  auto creative_id = result_->ad_deliver_info().ad_base_info().creative_id();
  auto iter = front_force_explore_log_map.find(creative_id);
  if (iter != front_force_explore_log_map.end()) {
    online_join_pb_->set_is_adfront_force_explore(iter->second.is_adfront_force_explore);
    online_join_pb_->set_is_adfront_force_explore_gd_order(
                      iter->second.is_adfront_force_explore_gd_order);
  }
}

void OnlineJoinPack::SetDncExploreData() {
  if (SPDM_enable_dnc_cem_base_group(session_data_->get_spdm_ctx())) {
    auto cem_params = FrontKconfUtil::outerloopDncCemExploreParam();
    if (cem_params != nullptr) {
      auto iter = cem_params->find("hash_version");
      if (iter != cem_params->end() && iter->second > 0) {
        int64_t hash_version = static_cast<int64_t>(iter->second);
        online_join_pb_->set_dnc_cem_explore_group(hash_version * 100 + 99);
      }
    }
  }
}

void OnlineJoinPack::SetDncLtvData() {
  double dnc_ltv = 0;
  double dnc_ctcvr = 0;
  double dnc_score = 0;
  if (rank_result_) {
    dnc_ltv = rank_result_->rank_rank_info().dnc_ltv();
    dnc_ctcvr = rank_result_->rank_rank_info().dnc_ctcvr();
    dnc_score = rank_result_->rank_rank_info().dnc_score();
  }
  online_join_pb_->set_dnc_ltv(dnc_ltv);
  online_join_pb_->set_dnc_ctcvr(dnc_ctcvr);
  online_join_pb_->set_dnc_score(dnc_score);
  if (SPDM_enable_add_prerank_dnc_ltv(session_data_->get_spdm_ctx()) && rank_result_) {
    online_join_pb_->set_prerank_dnc_ctcvr(rank_result_->rank_rank_info().prerank_dnc_ctcvr());
    online_join_pb_->set_prerank_dnc_ltv_idx(rank_result_->rank_rank_info().prerank_dnc_ltv_idx());
  }
  online_join_pb_->set_calibrated_cpm(rank_result_->rank_rank_info().calibrated_cpm());
}

void OnlineJoinPack::SetEECVRData() {
  if (SPDM_enable_ee_purchase_cvr_tags(session_data_->get_spdm_ctx())) {
    double ee_score = 0;
    if (rank_result_) {
      ee_score = rank_result_->rank_rank_info().ee_purchase_cvr();
    }
    online_join_pb_->set_ee_purchase_cvr(ee_score);
  }
}

void OnlineJoinPack::SetOuterloopEEData() {
  std::string ee_pid_tag = "";
  double ee_epsilon = 0.0;
  double ee_norm_cpm = 0.0;
  double ee_pid_control_ratio = 0.0;
  double outerloop_ee_boost_ratio = 0.0;
  double ee_price_discount_ratio = 0.0;
  if (rank_result_) {
    ee_pid_tag = rank_result_->rank_base_info().ee_pid_tag();
    ee_epsilon = rank_result_->rank_base_info().ee_epsilon();
    ee_norm_cpm = rank_result_->rank_base_info().ee_norm_cpm();
    ee_pid_control_ratio = rank_result_->rank_base_info().ee_pid_control_ratio();
    outerloop_ee_boost_ratio = rank_result_->rank_base_info().outerloop_ee_boost_ratio();
    for (const auto& discount_price_record_info : online_join_pb_->discount_price_record()) {
      if (discount_price_record_info.price_tag() == 75) {
        ee_price_discount_ratio = discount_price_record_info.price_discount();
      }
    }
  }
  online_join_pb_->set_ee_pid_tag(ee_pid_tag);
  online_join_pb_->set_ee_epsilon(ee_epsilon);
  online_join_pb_->set_ee_norm_cpm(ee_norm_cpm);
  online_join_pb_->set_ee_pid_control_ratio(ee_pid_control_ratio);
  online_join_pb_->set_outerloop_ee_boost_ratio(outerloop_ee_boost_ratio);
  online_join_pb_->set_ee_price_discount_ratio(ee_price_discount_ratio);
}

void OnlineJoinPack::SetAggrCardInfo() {
  bool pass_playlet_aggr_card_freq_control = session_data_->Attr(
    CommonIdx::pass_playlet_aggr_card_freq_control).GetIntValue().value_or(0) > 0 ? true : false;
  if (pass_playlet_aggr_card_freq_control &&
      style_info_->campaign().type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
      online_join_pb_->set_is_serial_aggr_card(true);  // 这个实际也是染色标记
  }
}


CommonCardFilterStage OnlineJoinPack::GetCommonCardFilterStage() {
  // 1. 检查频控
  int64 freq_control = session_data_->Attr(
    CommonIdx::pass_common_card_ind_freq_control).GetIntValue().value_or(0);
  int32 filter_stage = session_data_->Attr(
    CommonIdx::common_card_filter_stage).GetIntValue().value_or(0);
  bool valid_inner = (freq_control & CommonCardFreqCType::VALID_INNER_LOOP) > 0 ? false : true;
  bool valid_serial = (freq_control & CommonCardFreqCType::VALID_SEREAL) > 0 ? false : true;
  bool valid_quick_kwai_game = (freq_control & CommonCardFreqCType::VALID_QUIK_KWAI_GAME) >
  0 ? false : true;
  bool pass_playlet_common_card_freq_control = session_data_->Attr(
    CommonIdx::pass_playlet_common_card_freq_control).GetIntValue().value_or(0) > 0;
  if (!pass_playlet_common_card_freq_control || !valid_inner || !valid_serial || !valid_quick_kwai_game) {
    return static_cast<CommonCardFilterStage>(filter_stage);
  }
  auto app_version = session_data_->get_app_version();
  if (engine_base::CompareAppVersion(app_version, "12.11.40") < 0) {
    return FILTER_INVALID_APP_VERSION;
  }
  auto account_type = result_->ad_deliver_info().ad_base_info().account_type();
  if (account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE &&
       account_type != kuaishou::ad::AdEnum::ACCOUNT_CPC &&
       account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP) {
    return FILTER_INVALID_ACCOUNT_TYPE;
  }
  auto account_id = result_->ad_deliver_info().ad_base_info().account_id();
  auto campaign_id = result_->ad_deliver_info().ad_base_info().campaign_id();
  if (FrontKconfUtil::commonCardCampaignBlackList() != nullptr &&
      FrontKconfUtil::commonCardCampaignBlackList()->count(campaign_id)) {
    return FILTER_BLACKLIST_CAMPAIGN;
  }
  if (FrontKconfUtil::commonCardAccountBlackList() != nullptr &&
      FrontKconfUtil::commonCardAccountBlackList()->count(account_id)) {
    return FILTER_BLACKLIST_ACCOUNT;
  }
  auto* p_rank_result = GetAdRankResult(session_data_, *result_);
  if (p_rank_result != nullptr && p_rank_result->ad_rank_trans_info().qpon_info().has_qpon()) {
    return FILTER_HAS_QPON;
  }
  auto sec_industry_id = result_->ad_deliver_info().ad_base_info().industry_id_v3();
  if (FrontKconfUtil::commonCardIndustryV2() != nullptr &&
      FrontKconfUtil::commonCardIndustryV2()->count(sec_industry_id)) {
    return FILTER_INVALID_INNER_LOOP_INDUSTRY;
  }
  return PASS_FILTER;
}

void OnlineJoinPack::SetCommonCardInfo() {
  bool is_black_campaign = false;
  bool is_black_account = false;
  auto account_id = result_->ad_deliver_info().ad_base_info().account_id();
  auto campaign_id = result_->ad_deliver_info().ad_base_info().campaign_id();
  auto product_name = result_->ad_deliver_info().ad_base_info().product_name();
  auto campaign_type = result_->ad_deliver_info().ad_base_info().campaign_type();
  auto industry_id = result_->ad_deliver_info().ad_base_info().first_industry_id_v3();
  auto filter_stage = GetCommonCardFilterStage();
  if (FrontKconfUtil::commonCardCampaignBlackList() != nullptr) {
    if (FrontKconfUtil::commonCardCampaignBlackList()->count(campaign_id)) {
      is_black_campaign = true;
    }
  }
  if (FrontKconfUtil::commonCardAccountBlackList() != nullptr) {
    if (FrontKconfUtil::commonCardAccountBlackList()->count(account_id)) {
      is_black_account = true;
    }
  }
  auto account_type = result_->ad_deliver_info().ad_base_info().account_type();
  bool valid_account_type = false;
  if (account_type == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE ||
      account_type == kuaishou::ad::AdEnum::ACCOUNT_CPC ||
      account_type == kuaishou::ad::AdEnum::ACCOUNT_ESP) {
    valid_account_type = true;
  }
  auto item_type = merchant_util::GetSmallShopItemType(*style_info_);
  bool is_merchant_item = false;
  if (item_type == kuaishou::ad::AdEnum_MerchantItemPutType_MERCHANT_PUT_PRODUCT) {
    is_merchant_item = true;
  }
  auto app_version = session_data_->get_app_version();
  bool is_new_version = false;
  auto min_valid_version = SPDM_common_card_min_valid_version(session_data_->get_spdm_ctx());
  if (engine_base::CompareAppVersion(app_version, min_valid_version) >= 0) {
    is_new_version = true;
    session_data_->dot_perf->Count(1, "common_card_info.online_params_is_new_version");
  }
  if (is_black_campaign || is_black_account || !valid_account_type || !is_new_version) {
    return;
  }
  bool pass_playlet_common_card_freq_control = session_data_->Attr(
    CommonIdx::pass_playlet_common_card_freq_control).GetIntValue().value_or(0) > 0 ? true : false;
  int64 freq_control = session_data_->Attr(
    CommonIdx::pass_common_card_ind_freq_control).GetIntValue().value_or(0);
  auto* p_rank_result = GetAdRankResult(session_data_, *result_);
  if (p_rank_result == nullptr) {
    return;
  }
  bool is_qpon = p_rank_result->ad_rank_trans_info().qpon_info().has_qpon();
  if (is_qpon) {
    session_data_->dot_perf->Count(1, "common_card_info.is_qpon_vestify_judge");;
  }
  session_data_->dot_perf->Count(1, "common_card_info.common_card_candidates");;
  if (!is_qpon || (SPDM_enable_kuai_game_skip_qpon(session_data_->get_spdm_ctx()) && campaign_type ==
              kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE && industry_id == 1018)) {
    session_data_->dot_perf->Count(1, "common_card_info.after_qpon_filter");
    bool valid_inner = (freq_control & CommonCardFreqCType::VALID_INNER_LOOP) > 0 ? false : true;
    bool valid_serial = (freq_control & CommonCardFreqCType::VALID_SEREAL) > 0 ? false : true;
    bool valid_quick_kwai_game = (freq_control & CommonCardFreqCType::VALID_QUIK_KWAI_GAME) >
    0 ? false : true;
    bool valid_feed_card_kai_game = (freq_control & CommonCardFreqCType::VALID_PLAYABLE_GAME) >
    0 ? false : true;
    bool valid_industry = true;
    auto sec_industry_id =  result_->ad_deliver_info().ad_base_info().industry_id_v3();
    if (FrontKconfUtil::commonCardIndustryV2() != nullptr) {
      if (FrontKconfUtil::commonCardIndustryV2()->count(sec_industry_id)) {
        valid_industry = false;
      }
    }
    bool valid_item_type = false;
    if (result_->ad_deliver_info().ad_base_info().item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO) {
      valid_item_type = true;
    }
    bool valid_account = false;
    if (FrontKconfUtil :: commonCardGameList() != nullptr) {
      valid_account = FrontKconfUtil::commonCardGameList()->count(account_id);
    }
    auto ocpx_action_type = result_->ad_deliver_info().ad_base_info().ocpc_action_type();
    auto enable_inner_loop = SPDM_enable_inner_common_feed_card(session_data_->get_spdm_ctx());
    auto enable_kwai_game = SPDM_enable_kwai_game_common_feed_card(session_data_->get_spdm_ctx());
    auto enable_short_serial = SPDM_enable_short_serial_common_feed_card(session_data_->get_spdm_ctx());
    auto user_id = session_data_->get_user_id();
    if (FrontKconfUtil::commonCardUserWhiteList() != nullptr) {
      if (FrontKconfUtil::commonCardUserWhiteList()->count(user_id)) {
        session_data_->dot_perf->Count(1, "common_card_info.user_white_list_count");
        pass_playlet_common_card_freq_control = true;
        valid_inner = true;
        valid_quick_kwai_game = true;
        valid_feed_card_kai_game = true;
        valid_serial = true;
      }
    }
    // 快小游准入条件, 年龄约束 + 游戏白名单准入 + 机型判断 + 客户端版本号
    bool kuai_game_admit = true;
    bool enable_kuai_game_feed_card = SPDM_enable_kuai_game_feed_card(session_data_->get_spdm_ctx());
    bool enable_kuai_game_age_constraint =
                                SPDM_enable_kuai_game_age_constraint(session_data_->get_spdm_ctx());
    bool enable_kuai_game_platform_version_constraint =
                    SPDM_enable_kuai_game_platform_version_constraint(session_data_->get_spdm_ctx());
    bool enable_kuai_game_whitelist_constraint =
                          SPDM_enable_kuai_game_whitelist_constraint(session_data_->get_spdm_ctx());
    bool enable_kuai_game_device_version_constraint =
                      SPDM_enable_kuai_game_device_version_constraint(session_data_->get_spdm_ctx());
    bool enable_kuai_game_isunlogin_constraint =
                          SPDM_enable_kuai_game_isunlogin_constraint(session_data_->get_spdm_ctx());
    if (enable_kuai_game_feed_card && pass_playlet_common_card_freq_control && industry_id == 1018
        && valid_feed_card_kai_game && campaign_type == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) {
      auto kuai_game_feedplay_conf = FrontKconfUtil::kuaiGameFeedPlayConf()->data();
      auto& admit_device_mod_set = kuai_game_feedplay_conf.device_mod_set;
      auto& admit_product_name_set = kuai_game_feedplay_conf.product_name_set;
      auto& admit_account_id_set = kuai_game_feedplay_conf.account_id_set;
      auto& admit_age_bound_list = kuai_game_feedplay_conf.age_bound_list;
      auto& admit_app_version_tag = kuai_game_feedplay_conf.app_version_tag;
      std::string platform_version = session_data_->get_ad_request()->ad_user_info().platform_version();
      int64_t user_age = session_data_->get_ad_request()->ad_user_info().age();
      bool is_unlogin_user = session_data_->get_ad_request()->ad_user_info().is_unlogin_user();
      // 年龄
      if (enable_kuai_game_age_constraint && kuai_game_admit) {
        if (admit_age_bound_list.size() == 2 && admit_age_bound_list[0] < admit_age_bound_list[1]
              && user_age >= admit_age_bound_list[0] && user_age <= admit_age_bound_list[1]) {
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_age_admit_pack", absl::StrCat(user_age));
        } else {
          kuai_game_admit = false;
          filter_stage = KUAIGAME_AGE_FILTER;
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_age_filter_pack", absl::StrCat(user_age));
        }
      }
      // 客户端版本号
      if (enable_kuai_game_platform_version_constraint && kuai_game_admit) {
        if (ad_base::AppVersionCompare::Compare(platform_version, admit_app_version_tag) > 0) {
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_platform_version_pack", platform_version);
        } else {
          kuai_game_admit = false;
          filter_stage = KUAIGAME_PLATFORM_FILTER;
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_platform_filter_pack", platform_version);
        }
      }
      // 账户产品白名单
      if (enable_kuai_game_whitelist_constraint && kuai_game_admit) {
        if (admit_product_name_set.find(product_name) != admit_product_name_set.end() ||
                    admit_account_id_set.find(account_id) != admit_account_id_set.end()) {
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_whitelist_admit_pack", product_name);
        } else {
          kuai_game_admit = false;
          filter_stage = KUAIGAME_WHITELIST_FILTER;
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_whitelist_filter_pack", product_name);
        }
      }
      // 机型准入
      bool final_admit_device_mod = true;
      for (const auto& device_info : session_data_->get_ad_request()->ad_user_info().device_info()) {
        std::string device_mod = device_info.device_mod();
        if (admit_device_mod_set.find(device_mod) != admit_device_mod_set.end()) {
          final_admit_device_mod = false;
          break;
        }
      }
      if (enable_kuai_game_device_version_constraint && kuai_game_admit) {
        if (final_admit_device_mod) {
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_device_mod_admit_pack");
        } else {
          kuai_game_admit = false;
          filter_stage = KUAIGAME_DEVICEMOD_FILTER;
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_device_mod_filter_pack");
        }
      }
      // 未登陆用户过滤
      if (enable_kuai_game_isunlogin_constraint && kuai_game_admit) {
        if (!is_unlogin_user) {
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_login_user_admit_pack");
        } else {
          kuai_game_admit = false;
          filter_stage = KUAIGAME_UNLOGIN_FILTER;
          session_data_->dot_perf->Count(1, "kuai_game_feed_card_login_user_filter_pack");
        }
      }
    }
    if (pass_playlet_common_card_freq_control) {
      if ((valid_inner && valid_item_type && valid_industry && enable_inner_loop &&
          (
            (SPDM_enableCIDCardRemoveSwitch() &&
              (ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
                ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED))
            || (!SPDM_enableCIDCardRemoveSwitch() &&
              (ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
                ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
                ocpx_action_type == kuaishou::ad::CID_ROAS ||
                ocpx_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID))
          )
            && is_merchant_item &&
          IsInnerLoopAd(result_->ad_deliver_info().ad_base_info().campaign_type()))) {
            filter_stage = INNER_LOOP_PASS_FILTER;
            session_data_->dot_perf->Count(1, "common_card_info.inner_loop_cnt");
            online_join_pb_->set_is_big_card(true);
            online_join_pb_->set_feed_card_industry(kuaishou::ad::AdEnum_FeedCardIndustry_INNER_LOOP);
      } else if (valid_feed_card_kai_game &&
        valid_item_type && enable_kuai_game_feed_card && industry_id == 1018 &&
        campaign_type == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE && kuai_game_admit) {
            filter_stage = KUAI_GAME_FEED_CARD_PASS_FILTER;
            session_data_->dot_perf->Count(1, "common_card_info.quick_kwai_game_cnt_feed_card");
            online_join_pb_->set_is_big_card(true);
            online_join_pb_->set_feed_card_industry(kuaishou::ad::AdEnum_CommonCardStyle_PLAYABLE_GAME_TYPE);
      } else if (valid_quick_kwai_game && valid_item_type && enable_kwai_game &&
          campaign_type ==
          kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE &&  // NO_LINT
          industry_id == 1018 && valid_account) {
            filter_stage = QUICK_KWAI_GAME_PASS_FILTER;
            session_data_->dot_perf->Count(1, "common_card_info.quick_kwai_game_cnt");
            online_join_pb_->set_is_big_card(true);
            online_join_pb_->set_feed_card_industry(kuaishou::ad::AdEnum_FeedCardIndustry_QUIK_KWAI_GAME);
      } else if (valid_serial && campaign_type == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION
                 && enable_short_serial) {
            filter_stage = SERIAL_PASS_FILTER;
            session_data_->dot_perf->Count(1, "common_card_info.serial_cnt");
            online_join_pb_->set_is_big_card(true);
            online_join_pb_->set_feed_card_industry(kuaishou::ad::AdEnum_FeedCardIndustry_SHORT_SERIAL);
      }
    }
    online_join_pb_->set_common_card_filter_stage(filter_stage);
  }
}

void OnlineJoinPack::SetAdjustPriceRecord() {
  if (ad_common_ == nullptr) {
    return;
  }
  int64_t old_price = 1;
  if (rank_result_) {
    old_price = rank_result_->rank_price_info().origin_price();
  }
  double price_discount = 1.0;
  for (const auto& record : ad_common_->base_np.adjust_price_tag_2_price) {
    if (old_price > 0) {
      price_discount = record.second * 1.0 / old_price;
    } else {
      price_discount = 1.0;
    }
    old_price = record.second;
    if (price_discount == 1.0 && SPDM_enableSimplifyDiscountPrice()) continue;
    auto *record_item = online_join_pb_->add_discount_price_record();
    record_item->set_price_tag(record.first);
    record_item->set_price_discount(price_discount);
  }
}

void OnlineJoinPack::SetBrandPKData() {
  if (result_->ad_source_type() == kuaishou::ad::BRAND) {
    const auto& brand_online_join_params = result_->ad_deliver_info().online_join_params();
    ::google::protobuf::util::JsonStringToMessage(brand_online_join_params, &brand_online_join_pb_);
    online_join_pb_->set_brand_net_cpm(brand_online_join_pb_.brand_net_cpm());
    online_join_pb_->set_brand_pesudo_cpm(brand_online_join_pb_.brand_pesudo_cpm());
    online_join_pb_->set_dsp_pk_ecpm(brand_online_join_pb_.dsp_pk_ecpm());
    online_join_pb_->set_dsp_pk_price(brand_online_join_pb_.dsp_pk_price());
    online_join_pb_->set_dsp_second_ecpm(brand_online_join_pb_.dsp_second_ecpm());
  }
}

void OnlineJoinPack::Stats() {
}

}  // namespace front_server
}  // namespace ks
