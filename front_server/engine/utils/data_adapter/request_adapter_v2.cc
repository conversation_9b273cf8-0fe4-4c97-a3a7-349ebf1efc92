#include "teams/ad/front_server/engine/utils/data_adapter/request_adapter_v2.h"

#include <vector>

#include "absl/strings/substitute.h"
#include "glog/logging.h"
#include "base/encoding/base64.h"
#include "ks/base/abtest/abtest_globals.h"
#include "ks/base/abtest/abtest_instance.h"
#include "ks/base/abtest/session_context_factory.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/front_server/bg_task/brand_preview/preview_manager_v2.h"
#include "teams/ad/front_server/engine/context_data/utility.h"
#include "teams/ad/front_server/engine/utils/data_adapter/universe_ad_request_info_adapter.h"
#include "teams/ad/front_server/engine/utils/data_adapter/user_info_adapter.h"
#include "teams/ad/front_server/util/ad_browse_set/ad_browse_set.h"
#include "teams/ad/front_server/util/ad_user_info/ad_user_info_builder.h"
#include "teams/ad/front_server/util/kconf/kconf.h"
#include "teams/ad/front_server/util/spdm/spdm_switches.h"
#include "teams/ad/front_server/util/utility/ksn_util.h"
#include "teams/ad/front_server/bg_task/creator_forbidden_industry_p2p/creator_forbidden_p2p.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/front_server/engine/utils/pos_flags.h"

using kuaishou::ad::AdRequest;
using kuaishou::ad::AdUserInfo;
using kuaishou::ad::AthenaExploreRequest;
using kuaishou::ad::AthenaFollowRequest;
using kuaishou::ad::AthenaNearbyRequest;
using kuaishou::ad::AthenaRequest;
using kuaishou::ad::FrontServerRequest;
using kuaishou::ad::UniverseAdRequestInfo;
using kuaishou::ad::universe::AthenaAdRequest;
using kuaishou::newsmodel::RecoClientRequestInfo;

using google::protobuf::Arena;

namespace ks {
namespace front_server {

void RequestAdapterV2::BuildExtCommonData(kuaishou::ad::FrontServerRequest *front_server_request,
                                          ContextData* session_data) {
  auto* ad_request = session_data->mutable_ad_request();
  auto* fanstop_request = session_data->mutable_fanstop_request();
  auto scene = session_data->GetUnifyScene();
  const ks::infra::kenv::RpcTraceContext &rpc_trace_ctx = ks::infra::kenv::ServiceMeta::GetRpcTraceContext();
  const ks::infra::kenv::RpcStressTestContext &stress_test = rpc_trace_ctx.GetRpcStressTestCtx();
  const std::string &biz_name = stress_test.GetBizName();

  if (KS_UNLIKELY(ad_request == nullptr)) {
    return;
  }
  ad_request->set_page_id(session_data->get_page_id());
  if (ad_request->sub_page_id() <= 0 && session_data->get_sub_page_id() > 0) {
    ad_request->set_sub_page_id(session_data->get_sub_page_id());
  }
  ad_request->set_tab_name(session_data->get_tab_name());
  ad_request->set_llsid(session_data->get_llsid());
  FillAdRequestPosFlags(session_data);
  if (ad_request->has_extra_request_ctx() && !ad_request->extra_request_ctx().empty()) {
    Json extra_ctx(StringToJson(ad_request->extra_request_ctx()));
    int page = extra_ctx.GetInt("page", 0);
    if (page > 0) {
      ad_request->set_page(page);
    }
    // 激励直播的刷次和发现页这些字段不同
    if (ks::ad_base::IsInspireLive(session_data->get_sub_page_id(), session_data->get_ad_request())) {
      page = extra_ctx.GetInt("refreshTimes", 0);
      if (page > 0) {
        // 激励直播的数据从 1 开始计数，每请求一次引擎自增 1，进入页面时重置
        ad_request->set_page(page);
      }
    }
  }
  ad_request->set_front_antispam_code(session_data->get_antispam_code());
  ad_request->set_open_mix_to_ad_pack(
      ad_request->mutable_front_internal_data()->open_mix_to_ad_pack());
  ad_request->set_enable_front_auction_move_rank(true);
  if (session_data->get_enable_hard_soft_union()) {
    ad_request->set_enable_hard_soft_union(true);
  }
  if (SPDM_enable_ad_history_filter_id_unify(session_data->get_spdm_ctx())) {
    ad_request->set_enable_ad_history_filter_id_unify(true);
  }
  if (session_data->common_r_->GetIntCommonAttr("enable_live_copy_refactor").value_or(0) > 0
      || session_data->common_r_->GetIntCommonAttr(
      "enable_live_copy_refactor_without_union").value_or(0) > 0) {
    ad_request->set_enable_live_copy_refactor(true);
  }
  ad_request->set_live_copy_refactor_type(
      session_data->common_r_->GetIntCommonAttr("live_copy_refactor_type").value_or(0));
  if (ks::ad_base::IsCplRequest(session_data->get_sub_page_id())) {
    ad_request->set_home_page_user_id(
        session_data->UniverseRequest().cpl_request_info().home_page_user_id());
    for (auto industry_id : session_data->UniverseRequest().cpl_request_info().ad_industry_id()) {
      ad_request->add_ad_industry_id(industry_id);
    }
  }
  // 广告个性化推荐开关
  ad_request->set_ad_personal_switch_status(CheckAdPersonalStatus(*front_server_request));
  if (session_data->get_is_matrix_flow()) {
    bool disable_programmatic_ad_feed = false;
    if (!session_data->get_ad_request()->kkd_ext().empty()) {
      const base::Json ext_json(base::StringToJson(session_data->get_ad_request()->kkd_ext()));
      if (ext_json.IsObject() && ext_json.get()) {
        disable_programmatic_ad_feed = ext_json.GetBoolean("disableProgrammaticAd", false);
      }
    }
    if (disable_programmatic_ad_feed ||
        session_data->UniverseRequest().user_info().disable_programmatic_ad()) {
      // 快看点流量复用主站个性化逻辑
      ad_request->set_ad_personal_switch_status(kuaishou::ad::AdPersonalSwitchStatus::KNEWS_CLOSED);
      session_data->dot_perf->Count(1, "knews_ad_personal_switch",
          front_server_request->universe_request().app_info().app_id());
    }
  }
  // 广告个性化推荐状态监控
  ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "ad_personal_switch",
        absl::StrCat(front_server_request->type()),
        absl::StrCat(ad_request->ad_personal_switch_status()));

  // 将无 diff 测试流量的 fake type 进行转换
  if (front_server_request->fake_type() != ::kuaishou::ad::UserFakeType::NORMAL_USER) {
    if (ad_request != nullptr) {
      ad_request->set_fake_type(front_server_request->fake_type());
    }
  }
  if (SPDM_enableUseNewPressTestFlag()) {
    if (ks::infra::kenv::IsStressTestFlow()) {
      if (ad_request != nullptr) {
        ad_request->set_fake_type(kuaishou::ad::UserFakeType::PRESS_FROM_LANE);
      }
      ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "is_stress_test_flow",
                                           kuaishou::ad::FrontRequestType_Name(front_server_request->type()));
    }
  } else {
    // 将商业化压力测试流量的 fake type 进行转换
    if (biz_name.length() > 0 && FrontKconfUtil::enableBiznameControl()) {
      if (ad_request != nullptr) {
        ad_request->set_fake_type(::kuaishou::ad::UserFakeType::PRESS_FROM_LANE);
        ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "biz_name_request_num", biz_name,
                                           kuaishou::ad::FrontRequestType_Name(front_server_request->type()));
      }
    }
  }
  // 识别搜索评测平台流量
  if (front_server_request->type() == kuaishou::ad::SEARCH_REQUEST) {
    const std::string& session_id = ad_request->search_info().session_id();
    const std::string prefix = *FrontKconfUtil::openSearchSessionIdPrefix();
    if (!prefix.empty() && session_id.substr(0, prefix.size()) == prefix) {
      ad_request->set_fake_type(::kuaishou::ad::UserFakeType::PRESS_FROM_LANE);
      ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "seweb_request_num");
    }
  }
  if (ad_request && front_server_request->has_request_hash()) {
    ad_request->mutable_request_hash()->CopyFrom(front_server_request->request_hash());
  }
  if (session_data->get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    utility::SetTraceLogSamplingFlag(ad_request, scene, true);
    utility::SetTraceLogV2TableSamplingFlag(ad_request, true);
  } else {
    utility::SetTraceLogSamplingFlag(ad_request, scene);
    utility::SetTraceLogV2TableSamplingFlag(ad_request);
  }

  {
    // front 透传外流进入作品的关联 photo_id
    if (SPDM_fix_relative_photo_id_explore(session_data->get_spdm_ctx())
            ? session_data->get_pos_manager().IsInnerExplore()
            : front_server_request->flow_type() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_EXPLORE) {
      // 发现页
      ad_request->set_relative_photo_id(ad_request->reco_request_info().source_photo_id());
      ks::ad_base::AdPerf::IntervalLogStash(ad_request->relative_photo_id() != 0 ? 1 : 0,
        "ad.ad_front", "get_relative_photo_id_explore", absl::StrCat(front_server_request->sub_page_id()));
    } else if (ad_base::IsWanhe(front_server_request->sub_page_id())) {
      // 磁力万合
      if (front_server_request->universe_request().imp_info().ad_pos_info_size() > 0) {
        Json imp_ext_data(StringToJson(front_server_request->universe_request()
                                            .imp_info()
                                            .ad_pos_info(0)
                                            .imp_ext_data()));
        if (imp_ext_data.IsObject()) {
          ad_request->set_relative_photo_id(imp_ext_data.GetInt("photo_id", 0));
          if (SPDM_enable_photo_id_string_to_int(session_data->get_spdm_ctx())) {
            // 兼容 P 页皮肤和内流
            if (ad_request->relative_photo_id() == 0) {
              int64_t relative_photo_id = 0;
              if (absl::SimpleAtoi(imp_ext_data.GetString("photo_id", "0"), &relative_photo_id)) {
                ad_request->set_relative_photo_id(relative_photo_id);
              }
            }
          }
          // 兼容下 IOS
          if (ad_request->relative_photo_id() == 0) {
            int64_t relative_photo_id = 0;
            base::StringToInt64(imp_ext_data.GetString("photoId", "0"), &relative_photo_id);
            ad_request->set_relative_photo_id(relative_photo_id);
          }
        }
      }
      ks::ad_base::AdPerf::IntervalLogStash(ad_request->relative_photo_id() != 0 ? 1 : 0,
        "ad.ad_front", "get_relative_photo_id_wanhe", absl::StrCat(front_server_request->sub_page_id()));
    }
  }
  session_data->set_is_inspire_mix(session_data->get_pos_manager().IsInspireMix());
  if (session_data->get_is_inspire_mix()) {
    ParseInspireMixRequestType(session_data);
  }
  if (session_data->GetUnifyScene() == FrontServerScene::GALAXY) {
    if (front_server_request->universe_request().imp_info().ad_pos_info_size() > 0) {
      Json imp_ext_data(StringToJson(front_server_request->universe_request()
                                          .imp_info()
                                          .ad_pos_info(0)
                                          .imp_ext_data()));
      if (imp_ext_data.IsObject() && imp_ext_data.get()) {
        // 获取激励视频请求 快小游相关信息
        std::string media_exp_data_str = imp_ext_data.GetString("mediaExtData");
        const base::Json media_exp_data(base::StringToJson(media_exp_data_str));
        if (media_exp_data.IsObject() && media_exp_data.get()) {
          std::string ext_str = media_exp_data.GetString("ext");
          const base::Json ext(base::StringToJson(ext_str));
          if (ext.IsObject() && ext.get()) {
            auto minigame_from_type_v2 = ext.GetString("game_scene_from_v2");
            session_data->dot_perf->Count(minigame_from_type_v2 != "", "vaild_minigame_from_type_v2");
            session_data->common_w_->SetStringCommonAttr(common_attr::minigame_from_type_v2, minigame_from_type_v2); // NOLINT
          }
          std::string tmp_minigame_app_id = media_exp_data.GetString("appId");
          // 快小游透传 minigame_app_id 到 ad_rank
          if (tmp_minigame_app_id != "") {
            ad_request->set_minigame_app_id(tmp_minigame_app_id);
          }
          // 小说透传 kwai_book_id 字段到 ad_rank
          if (SPDM_enable_fiction_iaa_ecpc_by_put_book_new(session_data->get_spdm_ctx())) {
            std::string book_id_str = media_exp_data.GetString("book_id");
            if (!book_id_str.empty()) {
              int64_t kwai_book_id = std::stoll(book_id_str);
              if (kwai_book_id > 0) {
                ad_request->set_kwai_book_id(kwai_book_id);
              }
            }
          }
        }
        int64 xf_tube_id = imp_ext_data.GetInt("xf_tube_id", 0);
        session_data->common_w_->SetIntCommonAttr(common_attr::xf_tube_id, xf_tube_id);

        if (session_data->common_r_->GetIntCommonAttr("enable_send_iaa_source_type").value_or(0)) {
          auto iaa_source_type_str = imp_ext_data.GetString("source_type");
          int64_t iaa_source_type = 0;
          if (base::StringToInt64(iaa_source_type_str, &iaa_source_type)) {
            session_data->common_w_->SetIntCommonAttr(common_attr::iaa_source_type, iaa_source_type);
          }

          auto iaa_sub_source_type_str = imp_ext_data.GetString("sub_source_type");
          int64_t iaa_sub_source_type = 0;
          if (base::StringToInt64(iaa_sub_source_type_str, &iaa_sub_source_type)) {
            session_data->common_w_->SetIntCommonAttr(common_attr::iaa_sub_source_type, iaa_sub_source_type);
          }
        }
        if (SPDM_disableXiFanInnerLoop()) {
          auto xf_install_ks_app = imp_ext_data.GetInt("xf_install_ks_app", 0);
          session_data->common_w_->SetIntCommonAttr(common_attr::xf_install_ks_app, xf_install_ks_app);
        }
        if (session_data->common_r_->GetIntCommonAttr("enable_xifan_replace_kwai_url").value_or(0)) {
          const std::string xifan_ext_data_str = imp_ext_data.GetString("xifanExtData");
          const base::Json xifan_ext_data(base::StringToJson(xifan_ext_data_str));
          if (xifan_ext_data.IsObject() && xifan_ext_data.get()) {
            const std::string xf_ks_app_version = xifan_ext_data.GetString("xfKsAppVersion");
            const std::string xf_ks_nebula_app_version = xifan_ext_data.GetString("xfKsNebulaAppVersion");
            session_data->common_w_->SetIntCommonAttr(common_attr::xf_has_ks_app_version, !xf_ks_app_version.empty());    // NOLINT
            session_data->common_w_->SetIntCommonAttr(common_attr::xf_has_ks_nebula_app_version, !xf_ks_nebula_app_version.empty());  // NOLINT
          }
        }
        if (session_data->common_r_->GetIntCommonAttr("enable_set_iaa_ad_play_type").value_or(0)) {
          const std::string ad_play_type_str = imp_ext_data.GetString("ad_play_type");
          session_data->common_w_->SetStringCommonAttr("iaa_ad_play_type", ad_play_type_str);
        }
      }
    }
  }
  if (SPDM_enableStorewideGuaranteed()) {
    FillStorewideGuaranteedInfo(session_data);
  }
}

void RequestAdapterV2::FillStorewideGuaranteedInfo(ContextData* session_data) {
  ASSERT_NOT_NULL_OTHERWISE_RETURN(session_data);
  if (FrontKconfUtil::storewide_guaranteed_info() == nullptr) {
    return;
  }
  auto iter = FrontKconfUtil::storewide_guaranteed_info()->find(absl::StrCat(session_data->get_user_id()));
  if (iter != FrontKconfUtil::storewide_guaranteed_info()->end()) {
    session_data->mutable_ad_request()->set_enable_storewide_guaranteed(true);
    std::vector<std::string> split_items = absl::StrSplit(iter->second, ",");
    for (const auto& split_item : split_items) {
      std::vector<std::string> split_types = absl::StrSplit(split_item, "_");
      if (split_types.size() != 3) {
         continue;
      }
      int64_t creative_id = 0;
      if (!absl::SimpleAtoi(split_types[0], &creative_id) || creative_id <= 0) {
         continue;
      }
      session_data->mutable_ad_request()->add_storewide_guaranteed_creatives(creative_id);
      session_data->dot_perf->Count(1, "debug_storewide_guaranteed",
          absl::StrCat(session_data->get_user_id(), "_", creative_id));
    }
  }
}

ReqAdapterCode RequestAdapterV2::FillEapiCommonData(ContextData* session_data) {
  auto* front_request = session_data->mutable_front_server_request();
  // 这个时候 ad_request 已不会为 nullptr
  AdRequest* ad_request = session_data->mutable_ad_request();
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  UserInfoAdapter::FillKuaishouAppUserInfo(*front_request, ad_request, &req_adapter_code,
                                           session_data->mutable_ad_pos_info_vec(),
                                           session_data->get_is_matrix_flow(),
                                           session_data->get_spdm_ctx());

  // 设置 PosUtil
  if (ad_request->universe_ad_request_info().imp_info_size() > 0) {
    auto& imp_info = ad_request->universe_ad_request_info().imp_info(0);
    auto ad_request_flow_type =
          ks::ad_base::GetAdRequestType(imp_info.page_id(), imp_info.sub_page_id());
    auto interactive_form =
        ks::ad_base::GetInteractiveForm(0, 0, ad_request_flow_type, session_data->get_app_id(), "",
                                        session_data->get_sub_page_id(), session_data->get_page_id());
    if (ad_request_flow_type == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_SEARCH) {
      // 搜索广告需要通过 ad_request->search_info().layout_set() 判断单双列
      interactive_form = ks::ad_base::GetInteractiveForm(ad_request->search_info().layout_set(),
                                                          0, ad_request_flow_type,
                                                          "", "", imp_info.sub_page_id());
      // 搜索广告盒子 interactive_form 修改
      // 广告盒子单列 切换 interactive_form
      if (SPDM_ad_box_thanos_interactive_form_update(session_data->get_spdm_ctx()) &&
          ad_request->search_info().request_source() == 2 &&
          ks::ad_base::kconf::searchInspireThanosSubPageIds()->count(session_data->get_sub_page_id())) {
        interactive_form = kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS;
      }
    }
    if (ad_request_flow_type == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_EXPLORE &&
      engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(ks::ad_base::GetRequestAppId(*ad_request))) {
      interactive_form = kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS;
    }
    if (FrontKconfUtil::galaxyThanosSubPageId()->count(session_data->get_sub_page_id()) > 0) {
      interactive_form = kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS;
    }
    int64_t page_id = session_data->get_ad_request()->page_id();
    if (SPDM_enable_fix_change_to_double_col(session_data->get_spdm_ctx())) {
      page_id = session_data->get_page_id();
    }
    if (FrontKconfUtil::guessYouLikeFeedSubPageIds()->count(session_data->get_sub_page_id()) > 0) {
      if (SPDM_enable_guess_like_change_interactive_form(session_data->get_spdm_ctx())) {
        interactive_form = kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_FEED;
      }
    }
    // 激励特殊广告位修改 interactive_form
    const auto& incentive_feed_sub_page_ids =
      FrontKconfUtil::incentiveFeedInteractiveFormSubPageIds();
    if (incentive_feed_sub_page_ids && incentive_feed_sub_page_ids->count(session_data->get_sub_page_id())) {
      interactive_form = kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_FEED;
    }
    SetAdRequestPosUtil(ad_request,
                        ad_request_flow_type,
                        interactive_form,
                        imp_info.sub_page_id());
  }
  session_data->mutable_pos_manager()->Initialize(*ad_request);
  return req_adapter_code;
}

// 设置从 nearby, follow, explore 等 reco 传来的接口字段转换, 非必要不要动
ReqAdapterCode RequestAdapterV2::FillRecoCommonData(ContextData* session_data,
                                                    AdEnum_AdRequestFlowType ad_request_flow_type,
                                                    bool allow_unlogin) {
  auto* ad_request = session_data->mutable_ad_request();
  auto photo_page = ad_request->reco_request_info().photo_page();
  auto* front_server_request = session_data->mutable_front_server_request();
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  bool remove_mock_user_id = SPDM_remove_mock_user_id(session_data->get_spdm_ctx());
  // 1. 先初始化 user info 的一些基础数据，这个不需要依赖外部服务
  UserInfoAdapter::BuildBaseUserInfo(ad_request, *front_server_request,
                                     GetTabName(front_server_request->type()),
                                     allow_unlogin, remove_mock_user_id);
  // 2. 设置广告位相关
  auto browse_type = ad_request->reco_request_info().browse_type();
  auto source = ad_request->reco_request_info().source();
  auto universe_ad_request_info = ad_request->mutable_universe_ad_request_info();
  kuaishou::ad::AdEnum_InteractiveForm interactive_form = ks::ad_base::GetInteractiveForm(
      browse_type, source, ad_request_flow_type, session_data->get_app_id(), photo_page,
      session_data->get_sub_page_id(), session_data->get_page_id());
  UniverseAdRequestInfoAdapter::FillBrowseModeV2(front_server_request->type(), ad_request, interactive_form,
                                                 session_data->get_sub_page_id());
  if (!FillUniverseAdRequest(session_data->get_app_id(), session_data->get_page_id(),
                             session_data->get_sub_page_id(), AdPosAction::kDefaultAction,
                             universe_ad_request_info, session_data->mutable_ad_pos_info_vec())) {
    req_adapter_code = ReqAdapterCode::kGetPosError;
  }

  SetAdRequestPosUtil(ad_request, ad_request_flow_type, interactive_form, session_data->get_sub_page_id());
  session_data->mutable_pos_manager()->Initialize(*ad_request);
  return req_adapter_code;
}

ReqAdapterCode RequestAdapterV2::BuildAdRequest(ContextData* session_data) {
  auto* front_server_request = session_data->mutable_front_server_request();
  ReqAdapterCode req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  if (front_server_request == nullptr) {
    LOG(ERROR) << "front_server_request is nullptr";
    req_adapter_code = ReqAdapterCode::kNullPointerError;
    return req_adapter_code;
  }
  // 构建同城的时候会用到 fanstop_request, 所以直接在这里构建了
  session_data->set_fanstop_request(GetTabName(front_server_request->type()) == "nearby"
                    ? front_server_request->mutable_nearby_request()->mutable_fans_top_request()
                    : Arena::CreateMessage<kuaishou::fanstop::FansTopRequest>(session_data->GetArena()));

  switch (front_server_request->type()) {
    case kuaishou::ad::EXPLORE_REQUEST:
      req_adapter_code = BuildExploreAdRequest(session_data);
      break;
    case kuaishou::ad::FOLLOW_REQUEST:
      req_adapter_code = BuildFollowAdRequest(session_data);
      break;
    case kuaishou::ad::NEARBY_REQUEST:
      req_adapter_code = BuildNearbyAdRequest(session_data);
      break;
    // case kuaishou::ad::UNIVERSE_REQUEST:   目前不处理联盟
    case kuaishou::ad::SPLASH_REQUEST:
      req_adapter_code = BuildSplashAdRequest(session_data);
      break;
    case kuaishou::ad::KWAI_GALAXY_REQUEST:
      req_adapter_code = BuildKwaiAdRequest(session_data);
      break;
    case kuaishou::ad::SEARCH_REQUEST:
      req_adapter_code = BuildSearchAdRequest(session_data);
      break;
    case kuaishou::ad::MERCHANT_REQUEST:
      req_adapter_code = BuildMerchantAdRequest(session_data);
      break;
    default:
      req_adapter_code = ReqAdapterCode::kNullPointerError;
      // LOG(WARNING) << "Unhandled FrontRequestType: " << front_server_request->type();
      break;
  }
  // 一些公共数据添充
  BuildExtCommonData(front_server_request, session_data);
  return req_adapter_code;
}

ReqAdapterCode RequestAdapterV2::BuildExploreAdRequest(ContextData* session_data) {
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  auto* front_server_request = session_data->mutable_front_server_request();
  session_data->set_ad_request(front_server_request->mutable_explore_request()->mutable_ad_request());
  auto* ad_request = session_data->mutable_ad_request();

  auto& explore_request = front_server_request->explore_request();
  ad_request->set_page_size(explore_request.pagesize());
  ad_request->set_ad_request_type(::kuaishou::ad::AdEnum::EXPLORE_AD_REQUEST);

  req_adapter_code = FillRecoCommonData(session_data,
                                        kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_EXPLORE,
                                        true);

  if (ks::ad_base::IsSelectedRequest(session_data->get_sub_page_id()) &&
      session_data->get_ad_request()->cold_start() &&
      session_data->get_ad_request()->page_size() < FrontKconfUtil::defaultThanosPageSize()) {
    session_data->dot_perf->Count(1, "cold_start_request_no_ad_num");
    session_data->mutable_ad_request()->mutable_front_internal_data()->set_cold_start_request_type(
        kuaishou::ad::FrontInternalData_ColdStartRequestType_kColdStartNoAd);
  }
  if (session_data->get_ad_request()->is_live_rerank_req()) {
    session_data->dot_perf->Count(1, "LiveRerankReq", "second_req_num");
  }
  return req_adapter_code;
}

ReqAdapterCode RequestAdapterV2::BuildFollowAdRequest(ContextData* session_data) {
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  auto* front_server_request = session_data->mutable_front_server_request();
  session_data->set_ad_request(front_server_request->mutable_follow_request()->mutable_ad_request());
  auto* ad_request = session_data->mutable_ad_request();
  session_data->mutable_ad_request()->mutable_reco_user_info()->CopyFrom(
                front_server_request->follow_request().reco_user_info());

  req_adapter_code = FillRecoCommonData(session_data,
                                        kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_FOLLOW,
                                        false);

  auto& follow_request = front_server_request->follow_request();
  ad_request->mutable_ad_user_info()->set_is_unlogin_user(false);
  ad_request->mutable_ad_user_info()->set_id(session_data->get_user_id());
  auto* reco_user_ptr = ad_request->mutable_reco_user_info();
  for (auto uid : follow_request.follow()) {
    reco_user_ptr->add_follow_list()->mutable_user()->set_id(uid);
  }
  return req_adapter_code;
}

ReqAdapterCode RequestAdapterV2::BuildNearbyAdRequest(ContextData* session_data) {
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  auto* front_server_request = session_data->mutable_front_server_request();
  session_data->set_ad_request(Arena::CreateMessage<AdRequest>(session_data->GetArena()));

  auto* ad_request = session_data->mutable_ad_request();
  auto* fanstop_request = session_data->mutable_fanstop_request();

  ad_request->set_nearby_refresh_times(
      front_server_request->nearby_request().fans_top_request().nearby_refresh_times());
  auto* nearby_request = front_server_request->mutable_nearby_request();
  ad_request->mutable_hetu_tag_v2()->CopyFrom(nearby_request->fans_top_request().hetu_tags());
  ad_request->set_allocated_ad_user_info(nearby_request->mutable_ad_user_info());
  ad_request->set_allocated_reco_user_info(fanstop_request->mutable_reco_user_info());
  ad_request->set_product(static_cast<Product>(fanstop_request->product()));
  ad_request->set_extra_request_ctx(fanstop_request->extra_request_ctx());
  ad_request->set_page_size(fanstop_request->page_size());
  ad_request->mutable_reco_request_info()->set_browse_type(nearby_request->browse_type());
  ad_request->mutable_reco_request_info()->set_source(fanstop_request->reco_request_info().source());
  ad_request->set_ad_request_type(::kuaishou::ad::AdEnum::NEARBY_AD_REQUEST);
  ad_request->set_ad_client_info(fanstop_request->ad_client_info());
  ad_request->mutable_inspire_task_progress_info()->CopyFrom(
      nearby_request->fans_top_request().task_progress_info());

  //鸿蒙信息设置
  ad_request->set_origin_kpf(fanstop_request->origin_kpf());

  int32_t ad_req_type = nearby_request->ad_req_type();
  if (ad_req_type > 3 || ad_req_type < 0) {
    ad_req_type = 0;
  }
  ad_request->set_ad_req_type(ad_req_type);
  fanstop_request->set_nearby_browse_type(nearby_request->browse_type());
  if (fanstop_request->page_id() == 0 && nearby_request->page_id() != 0) {
    fanstop_request->set_page_id(nearby_request->page_id());
    fanstop_request->set_sub_page_id(nearby_request->sub_page_id());
  }
  AdEnum_AdRequestFlowType ad_request_flow_type = kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEARBY;
  if (ad_request->product() == KUAISHOU_NEBULA) {
    ad_request_flow_type = kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEBULA_NEARBY;
  }
  req_adapter_code = FillRecoCommonData(session_data, ad_request_flow_type, false);
  session_data->set_only_request_fanstop(session_data->NearbyOnlyRequestFanstop());
  ad_request->mutable_front_internal_data()->set_only_request_fanstop(
      session_data->get_only_request_fanstop());
  return req_adapter_code;
}

ReqAdapterCode RequestAdapterV2::BuildSplashAdRequest(ContextData* session_data) {
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  session_data->set_ad_request(Arena::CreateMessage<AdRequest>(session_data->GetArena()));
  auto* front_server_request = session_data->mutable_front_server_request();
  auto* ad_request = session_data->mutable_ad_request();
  if (front_server_request->universe_request().has_splash_req_info()) {
    ad_request->mutable_universe_ad_request_info()->mutable_splash_req_info()->CopyFrom(
        front_server_request->universe_request().splash_req_info());
  }
  ad_request->set_sub_page_id(session_data->get_sub_page_id());
  req_adapter_code = FillEapiCommonData(session_data);
  if (FrontKconfUtil::enableRefreshSplashRequest()) {
    session_data->RefreshSplashRequest();
  }
  const auto& splash_req_info =
    session_data->get_ad_request()->universe_ad_request_info().splash_req_info();
  if (splash_req_info.is_realtime()) {
    session_data->dot_perf->Count(splash_req_info.splash_id_size(),
        "splash_realtime_cache_ad_count");
  } else {
    session_data->dot_perf->Count(splash_req_info.splash_id_size(),
        "splash_prefetch_cache_ad_count");
  }
  if (ad_request->ad_user_info().id() <= 0  && FrontKconfUtil::enableSplashUnloginUser()) {
    ad_request->mutable_ad_user_info()->set_is_unlogin_user(true);
    if (!SPDM_remove_mock_user_id(session_data->get_spdm_ctx())) {
      ad_request->mutable_ad_user_info()->set_id(
          UserInfoAdapter::GenerateUnloginUserId(ad_request, session_data->get_app_id()));
    }
  }
  return req_adapter_code;
}

ReqAdapterCode RequestAdapterV2::BuildKwaiAdRequest(ContextData* session_data) {
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  session_data->set_ad_request(Arena::CreateMessage<AdRequest>(session_data->GetArena()));
  auto* front_server_request = session_data->mutable_front_server_request();
  auto* ad_request = session_data->mutable_ad_request();

  const auto& key = front_server_request->universe_request().kkd_ext();
  if (!key.empty()) ad_request->set_kkd_ext(key);
  ad_request->set_ad_request_type(::kuaishou::ad::AdEnum::KWAI_GALAXY_AD_REQUEST);
  req_adapter_code = FillEapiCommonData(session_data);

  auto *ad_user_info = ad_request->mutable_ad_user_info();
  uint64_t kwai_user_id = ad_user_info->id();
  auto ad_request_flow_type =
      ks::ad_base::GetAdRequestType(session_data->get_page_id(), session_data->get_sub_page_id());
  if (FrontKconfUtil::enableSmallGameUnloginUser() &&
      ad_request_flow_type == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_SMALL_GAME &&
    kwai_user_id <= 0) {
    ad_user_info->set_is_unlogin_user(true);
    if (!SPDM_remove_mock_user_id(session_data->get_spdm_ctx())) {
      ad_user_info->set_id(UserInfoAdapter::GenerateUnloginUserId(ad_request, session_data->get_app_id()));
    }
  }

  // set tube_param
  if (front_server_request->universe_request().has_tube_param()) {
    session_data->mutable_ad_request()->mutable_tube_param()->CopyFrom(
      front_server_request->universe_request().tube_param());
    session_data->dot_perf->Count(1, "ad_request_tube_param_fill");
  }

  // knews mock user id
  if (engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(ks::ad_base::GetRequestAppId(*ad_request))) {
    auto app_user_id = front_server_request->universe_request().user_info().app_user_id();
    if (app_user_id <= 0) {
      ad_user_info->set_is_unlogin_user(true);
    }
    if (kwai_user_id <= 0) {
      ad_user_info->set_id(session_data->get_abtest_user_id());
      session_data->set_user_id(ad_user_info->id());
      ad_user_info->set_is_universe_fake_user(session_data->get_is_fake_user());
    }
  } else if (ad_user_info->id() <= 0) {
    ad_user_info->set_is_unlogin_user(true);
    if (!SPDM_remove_mock_user_id(session_data->get_spdm_ctx())) {
      ad_user_info->set_id(UserInfoAdapter::GenerateUnloginUserId(ad_request, session_data->get_app_id()));
    }
  }

  // 传递 referrer_info 信息
  if (front_server_request->universe_request().has_referrer_info()) {
    ad_request->mutable_referrer_info()->CopyFrom(front_server_request->universe_request().referrer_info());
  }

  if (front_server_request->universe_request().has_inspire_req_info()) {
    const auto& inspire_req_info = front_server_request->universe_request().inspire_req_info();
    ad_request->mutable_inspire_req_info()->CopyFrom(inspire_req_info);
  }
  // tvc 广告信息
  if (front_server_request->universe_request().has_tvc_info()) {
    for (const auto& id : front_server_request->universe_request().tvc_info().live_stream_ids()) {
      ad_request->mutable_t_v_c_info()->add_live_stream_ids(id);
    }
  }
  // 中视频
  ad_request->mutable_corona_video_info()->CopyFrom(
      front_server_request->universe_request().corona_video_info());
  if (front_server_request->universe_request().has_plc_brand_info()) {
    ad_request->mutable_plc_brand_info()->CopyFrom(
        front_server_request->universe_request().plc_brand_info());
  }
  // p 页广告信息
  if (front_server_request->universe_request().has_brand_content_info()) {
    ad_request->mutable_brand_content_info()->CopyFrom(
      front_server_request->universe_request().brand_content_info());
  }
  ad_request->set_creator_uid(front_server_request->universe_request().creator_uid());
  // 聚合竞价二次请求 客户端透传数据
  if (SPDM_enableFillAggBiddingRelease()) {
    ParseAggrSecondInfo(session_data);
  }
  // Feed 大卡二次请求 解析客户端透传数据
  if (SPDM_enableSerialAggrCard()) {
    ParseSerialAggrSecondInfo(session_data);
  }
  if (SPDM_enableCommonCardSecondRequest()) {
    ParseCommonCardSecondRequestInfo(session_data);
  }
  // 线索多卖聚合页参数解析
  if (FrontKconfUtil::aggregateLeadFlowSet()->count(session_data->get_sub_page_id()) > 0) {
    ParseAggregateLeadFlowParams(session_data);
  }
  // 侧滑小窗或者 Profile 页流量参数解析
  if (session_data->get_ad_request()->ad_request_flow_type() ==
          kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_SIDE_WINDOW ||
      FrontKconfUtil::personalizedFlowSet()->count(session_data->get_sub_page_id()) > 0) {
    ParsePersonlizedFlowParams(session_data);
    session_data->dot_perf->Count(1, "personlized_req_with_uid",
                                  ad_request->creator_uid() > 0 ? "has_cuid" : "no_cuid");
  }
  // 快聘相似职位参数解析
  if (::ks::ad_base::IsRecruitSimilarPositionPage(session_data->get_page_id())) {
    ParseRecruitInfo(session_data);
  }
  if (session_data->get_pos_manager().IsInspireUnlockTube()) {
    ParseKwaiImpExtData(session_data);
  }
  // photo 二次请求解析
  if ((SPDM_enable_photo_rerank_req(session_data->get_spdm_ctx())  || SPDM_enablePhotoRerankReq()) &&
      (session_data->get_sub_page_id() == 11001001 || session_data->get_sub_page_id() == 10011001)) {
    ParsePhotoRerankInfo(session_data);
  }
  // 短剧激励获取 ad_client_info
  if (SPDM_enableTubeInspireSetAdClientInfo() &&
      (session_data->get_sub_page_id() == 100028372 || session_data->get_sub_page_id() == 100028373)) {
    auto& universe_req_ad_client_info =
        session_data->get_front_server_request()->universe_request().ad_client_info();
    if (ad_request->ad_client_info().empty() && !universe_req_ad_client_info.empty()) {
      ad_request->set_ad_client_info(universe_req_ad_client_info);
    }
  }
  return req_adapter_code;
}

void RequestAdapterV2::ParseAggregateLeadFlowParams(ContextData* session_data) {
  if (KS_UNLIKELY(session_data == nullptr)) {
    return;
  }
  const auto& universe_request = session_data->get_front_server_request()->universe_request();
  auto* ad_request = session_data->mutable_ad_request();
  if (KS_UNLIKELY(ad_request == nullptr)) {
    return;
  }
  if (universe_request.imp_info().ad_pos_info_size() > 0) {
    // 限定只有一个广告位，只取第一位
    const std::string& imp_ext = universe_request.imp_info().ad_pos_info(0).imp_ext_data();
    if (imp_ext.empty()) {
      return;
    }
    const base::Json imp_ext_data(base::StringToJson(imp_ext));
    int32_t pass_field_cnt = 0;
    if (imp_ext_data.IsObject()) {
      base::Json* agg_ext = imp_ext_data.Get("aggregateAdExtInfo");
      if (agg_ext != nullptr && agg_ext->IsObject()) {
        auto* lead_agg_ext = ad_request->mutable_galaxy_ext_info()->mutable_lead_aggregate_extra();
        if (KS_UNLIKELY(lead_agg_ext == nullptr)) {
          return;
        }
        std::string page_version = agg_ext->GetString("aggregatePageVersion");
        if (!page_version.empty()) {
          lead_agg_ext->set_aggregate_page_version(page_version);
          lead_agg_ext->set_city_page_version(base::CityHash64(page_version.c_str(), page_version.length()));
          pass_field_cnt++;
        }
        std::string product_name = agg_ext->GetString("productName");
        if (!product_name.empty()) {
          lead_agg_ext->set_product_name(product_name);
          lead_agg_ext->set_city_product_name(base::CityHash64(product_name.c_str(), product_name.length()));
          pass_field_cnt++;
        }
        int64_t industry_id = agg_ext->GetInt("firstIndustryId", -1);
        if (industry_id > 0) {
          lead_agg_ext->set_first_industry_id(industry_id);
          pass_field_cnt++;
        }
        int64_t sec_industry_id = agg_ext->GetInt("secondIndustryId", -1);
        if (sec_industry_id > 0) {
          lead_agg_ext->set_second_industry_id(sec_industry_id);
          pass_field_cnt++;
        }
        std::string licence_id = agg_ext->GetString("licenceIdNum");
        if (!licence_id.empty()) {
          lead_agg_ext->set_licence_id_num(licence_id);
          lead_agg_ext->set_city_licence_id_num(base::CityHash64(licence_id.c_str(), licence_id.length()));
          pass_field_cnt++;
        }
        auto* impression_json = agg_ext->Get("impressionProductName");
        if (impression_json != nullptr && impression_json->IsArray()) {
          for (auto idx = 0; idx < impression_json->size(); idx++) {
            std::string product;
            if (impression_json->GetString(idx, &product)) {
              lead_agg_ext->mutable_previous_product_name()->insert(
                  {product, base::CityHash64(product.c_str(), product.length())});
              pass_field_cnt++;
            }
          }
        }
      }
    }
    session_data->dot_perf->Count(pass_field_cnt, "pass_aggregate_lead_field");
    session_data->dot_perf->Count(pass_field_cnt > 0, "pass_aggregate_lead_pv");
  }
}

void RequestAdapterV2::ParsePersonlizedFlowParams(ContextData* session_data) {
  if (KS_UNLIKELY(session_data == nullptr)) {
    return;
  }
  const auto& universe_request = session_data->get_front_server_request()->universe_request();
  auto* ad_request = session_data->mutable_ad_request();
  if (KS_UNLIKELY(ad_request == nullptr)) {
    return;
  }

  int64_t creator = -1;
  if (ad_request->creator_uid() <= 0) {
    if (universe_request.imp_info().ad_pos_info_size() > 0) {
      // 限定只有一个广告位，只取第一位
      const std::string& imp_ext = universe_request.imp_info().ad_pos_info(0).imp_ext_data();
      if (imp_ext.empty()) {
        return;
      }
      const base::Json imp_ext_data(base::StringToJson(imp_ext));
      if (imp_ext_data.IsObject()) {
        std::string aid_str = imp_ext_data.GetString("p_author_id");
        if (aid_str.empty()) {
          // 使用驼峰方式再取一次
          aid_str = imp_ext_data.GetString("pAuthorId");
        }
        if (!aid_str.empty()) {
          if (absl::SimpleAtoi(aid_str, &creator) && creator > 0) {
            ad_request->set_creator_uid(creator);
          } else {
            session_data->dot_perf->Count(1, "failed_to_get_valid_creator_uid");
            return;
          }
        }
      }
    }
  } else {
    creator = ad_request->creator_uid();
  }
  if (creator < 0) {
    return;
  }
  auto* forbid_industry_conf =
      CreatorForbiddenIndustry::GetInstance()->GetCreatorForbiddenIndustry(creator);
  if (forbid_industry_conf != nullptr) {
    ad_request->mutable_galaxy_ext_info()->mutable_creator_restrict_info()->MergeFrom(
        *forbid_industry_conf);
    session_data->dot_perf->Count(1, "debug_p2p_info", "forbid_industry_conf",
        "has_value");
  } else {
    session_data->dot_perf->Count(1, "debug_p2p_info", "forbid_industry_conf",
        "empty");
  }
}

ReqAdapterCode RequestAdapterV2::BuildSearchAdRequest(ContextData* session_data) {
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  session_data->set_ad_request(Arena::CreateMessage<AdRequest>(session_data->GetArena()));
  auto* front_server_request = session_data->mutable_front_server_request();
  auto* ad_request = session_data->mutable_ad_request();

  ad_request->mutable_search_info()->CopyFrom(front_server_request->universe_request().search_info());
  // 搜索有数据依赖, FillEapiCommonData 需放在 search_info 填充后
  req_adapter_code = FillEapiCommonData(session_data);
  ad_request->mutable_referrer_info()->CopyFrom(front_server_request->universe_request().referrer_info());
  ad_request->mutable_debug_param()->CopyFrom(front_server_request->universe_request().debug_param());
  ad_request->mutable_reco_request_info()->set_browse_type(
                                            ad_request->universe_ad_request_info().browse_type());

  if (ad_request->ad_user_info().id() <= 0) {
    ad_request->mutable_ad_user_info()->set_is_unlogin_user(true);
    if (!SPDM_remove_mock_user_id(session_data->get_spdm_ctx())) {
      ad_request->mutable_ad_user_info()->set_id(
          UserInfoAdapter::GenerateUnloginUserId(ad_request, session_data->get_app_id()));
    }
  }
  if (front_server_request->universe_request().has_inspire_req_info()) {
    const auto& inspire_req_info = front_server_request->universe_request().inspire_req_info();
    ad_request->mutable_inspire_req_info()->CopyFrom(inspire_req_info);
  }

  if (!SupplementPosIdAndAdNum(ad_request->mutable_universe_ad_request_info())) {
    session_data->set_req_adapter_code(ReqAdapterCode::kGetPosError);
  }
  // 搜索广告 debug 白名单
  const auto trace_white_set = engine_base::AdKconfUtil::searchDebugUserList();
  session_data->set_is_search_debug_white_list(trace_white_set &&
                                               trace_white_set->count(session_data->get_user_id()));

  // 搜索广告 ab 试验分组名
  const auto& ab_names = FrontKconfUtil::abTestName()->data().ab_names();
  int ab_num = 0;
  std::string search_ab_group_name;
  for (const auto& ab_name : ab_names) {
    std::string group_name = session_data->get_spdm_ctx().TryGetString(ab_name, "default");
    search_ab_group_name += group_name + "|";
    // 至多允许 3 个分组名，防止对 perf 压力过大
    if (++ab_num == 3) {
      break;
    }
  }
  if (ab_num > 0) {
    session_data->set_search_ab_group_name(search_ab_group_name);
  }
  ad_request->set_creator_uid(session_data->get_search_creator_uid());
  return req_adapter_code;
}

ReqAdapterCode RequestAdapterV2::BuildMerchantAdRequest(ContextData* session_data) {
  auto req_adapter_code = ReqAdapterCode::kAdapterSuccess;
  session_data->set_ad_request(Arena::CreateMessage<AdRequest>(session_data->GetArena()));
  auto* front_server_request = session_data->mutable_front_server_request();
  auto* ad_request = session_data->mutable_ad_request();
  req_adapter_code = FillEapiCommonData(session_data);
  if (front_server_request->universe_request().has_referrer_info()) {
    ad_request->mutable_referrer_info()->CopyFrom(front_server_request->universe_request().referrer_info());
  }
  if (front_server_request->universe_request().has_inspire_req_info()) {
    ad_request->mutable_inspire_req_info()->CopyFrom(
      front_server_request->universe_request().inspire_req_info());
  }
  return req_adapter_code;
}

void RequestAdapterV2::ParseRecruitInfo(ContextData* session_data) {
  if (KS_UNLIKELY(session_data == nullptr ||
                  session_data->get_front_server_request() == nullptr ||
                  session_data->get_ad_request() == nullptr)) {
    return;
  }
  const auto& universe_request = session_data->get_front_server_request()->universe_request();
  if (KS_UNLIKELY(!universe_request.has_imp_info() ||
                  universe_request.imp_info().ad_pos_info_size() <= 0)) {
    return;
  }
  const auto& pos_info = universe_request.imp_info().ad_pos_info(0);
  if (KS_UNLIKELY(pos_info.imp_ext_data().empty())) {
    return;
  }
  const base::Json imp_ext_data(base::StringToJson(pos_info.imp_ext_data()));
  if (KS_UNLIKELY(!imp_ext_data.IsObject())) {
    return;
  }
  auto * const recruit_info = session_data->mutable_ad_request()->mutable_recruit_info();

  #define GET_VALUE(json_key, pb_key) \
    int64 json_key; \
    if (KS_LIKELY(imp_ext_data.GetInt(#json_key, &json_key))) { \
      recruit_info->set_##pb_key(json_key); \
    } else { \
      session_data->dot_perf->Count(1, "recruit_similar_pos", \
                                    "parse_athena_request", "field_miss", #json_key); \
    }

  GET_VALUE(jobId, job_id);
  GET_VALUE(orgId, org_id);
  GET_VALUE(jobCategory, job_category);

  #undef GET_VALUE

  auto const * const job_address = imp_ext_data.Get("jobAddress");
  if (KS_UNLIKELY(job_address == nullptr || !job_address->IsArray())) {
    session_data->dot_perf->Count(1, "recruit_similar_pos",
                                  "parse_athena_request", "field_miss", "job_address");
    return;
  }
  for (int i = 0; i < job_address->size(); ++i) {
    int64 value;
    if (KS_UNLIKELY(!job_address->GetInt(i, &value))) {
      recruit_info->clear_job_address();
      session_data->dot_perf->Count(1, "recruit_similar_pos",
                                    "parse_athena_request", "field_miss", "job_address");
      return;
    }
    recruit_info->add_job_address(value);
  }
  session_data->dot_perf->Count(1, "recruit_similar_pos",
                                "parse_athena_request", "parse_end");
  }

  void RequestAdapterV2::ParseKwaiImpExtData(ContextData* session_data) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN(session_data);
    ASSERT_NOT_NULL_OTHERWISE_RETURN(session_data->get_front_server_request());
    const auto& universe_request = session_data->get_front_server_request()->universe_request();
    ASSERT_OTHERWISE(universe_request.has_imp_info(), return);
    ASSERT_OTHERWISE(universe_request.imp_info().ad_pos_info_size() > 0, return);
    const auto& pos_info = universe_request.imp_info().ad_pos_info(0);
    const base::Json imp_ext_data(base::StringToJson(pos_info.imp_ext_data()));
    ASSERT_OTHERWISE(imp_ext_data.IsObject(), return);

    #define GET_VALUE(method, key, dest) \
      session_data->dot_perf->Count( \
        1, "parse_imp_ext_data", #key, \
        imp_ext_data.method(#key, &dest) ? "filled" : "not_filled");

    // imp_ext_data 字段解析放这里
    if (SPDM_removeIsInspireUnlockTube()) {
      int64_t tmp_val = 0;
      GET_VALUE(GetInt, subBizId, tmp_val);
      session_data->set_sub_biz_id(tmp_val);
    } else {
      if (session_data->get_pos_manager().IsInspireUnlockTube()) {
        int64_t tmp_val = 0;
        GET_VALUE(GetInt, subBizId, tmp_val);
        session_data->set_sub_biz_id(tmp_val);
      }
    }
    #undef GET_VALUE
  }

  void RequestAdapterV2::ParseInspireMixRequestType(ContextData* session_data) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN(session_data);
    ASSERT_NOT_NULL_OTHERWISE_RETURN(session_data->get_front_server_request());
    const auto& universe_request = session_data->get_front_server_request()->universe_request();
    ASSERT_OTHERWISE(universe_request.has_imp_info(), return);
    ASSERT_OTHERWISE(universe_request.imp_info().ad_pos_info_size() > 0, return);
    const auto& pos_info = universe_request.imp_info().ad_pos_info(0);
    const base::Json imp_ext_data(base::StringToJson(pos_info.imp_ext_data()));
    ASSERT_OTHERWISE(imp_ext_data.IsObject(), return);
    session_data->mutable_ad_request()->set_inspire_mix_request_type(
        imp_ext_data.GetInt("incentiveRequestType", 0));
    session_data->dot_perf->Count(1, "inspire_mix_request_type",
        absl::StrCat(session_data->get_ad_request()->inspire_mix_request_type()));
  }

void RequestAdapterV2::ParseAggrSecondInfo(ContextData* session_data) {
  ASSERT_NOT_NULL_OTHERWISE_RETURN(session_data);
  auto* ad_request = session_data->mutable_ad_request();
  ASSERT_NOT_NULL_OTHERWISE_RETURN(ad_request);
  const auto& universe_request = session_data->get_front_server_request()->universe_request();
  if (universe_request.imp_info().ad_pos_info_size() > 0) {
    // 限定只有一个广告位，只取第一位
    const std::string& imp_ext = universe_request.imp_info().ad_pos_info(0).imp_ext_data();
    if (imp_ext.empty()) {
      return;
    }
    const base::Json imp_ext_data(base::StringToJson(imp_ext));
    if (imp_ext_data.IsObject()) {
      bool is_aggr_second_req = imp_ext_data.GetInt("isAggrSecondReq", 0) == 1;
      if (is_aggr_second_req) {
        std::string second_trans_info = imp_ext_data.GetString("aggrSecondTransInfo", "");
        if (!second_trans_info.empty()) {
          std::string decode_string;
          if (!base::Base64Decode(second_trans_info, &decode_string)) {
            session_data->dot_perf->Count(1, "agg_bidding_release", "tranks_info_base_decode_failed");
            return;
          }
          kuaishou::ad::AdAggregationBaseInfos ad_aggr_info;
          if (!::google::protobuf::util::JsonStringToMessage(decode_string, &ad_aggr_info).ok()) {
            session_data->dot_perf->Count(1, "agg_bidding_release", "trans_info_json_to_pb_failed");
            return;
          }
          ad_request->mutable_ad_aggr_infos()->CopyFrom(ad_aggr_info);
          ad_request->set_is_aggr_second_req(true);
          session_data->SetOpenMixToAdPack(false);  // 不走混排
          session_data->dot_perf->Count(1, "agg_bidding_release", "parse_trans_info_suc");
          session_data->dot_perf->Count(1, "agg_bidding_release", "llsid_check",
              (ad_aggr_info.llsid() == session_data->get_llsid()) ? "valid" : "invalid");
          if (SPDM_enableAggregationBiddingDebugLog()) {
            LOG(INFO) << "[AGG_LOG] llsid: " << session_data->get_llsid()
                      << " parse trans info pb :" << ad_aggr_info.ShortDebugString();
          }
        }
      }
    }
  }
}

void RequestAdapterV2::ParseSerialAggrSecondInfo(ContextData* session_data) {
  ASSERT_NOT_NULL_OTHERWISE_RETURN(session_data);
  auto* ad_request = session_data->mutable_ad_request();
  ASSERT_NOT_NULL_OTHERWISE_RETURN(ad_request);
  const auto& universe_request = session_data->get_front_server_request()->universe_request();
  if (universe_request.imp_info().ad_pos_info_size() > 0) {
    // 限定只有一个广告位，只取第一位
    const std::string& imp_ext = universe_request.imp_info().ad_pos_info(0).imp_ext_data();
    if (imp_ext.empty()) {
      return;
    }
    const base::Json imp_ext_data(base::StringToJson(imp_ext));
    if (imp_ext_data.IsObject()) {
      bool is_serial_aggr_card = imp_ext_data.GetInt("isSerialAggrCard", 0) == 1;
      ad_request->set_is_serial_aggr_card(is_serial_aggr_card);
      if (is_serial_aggr_card) {
        session_data->dot_perf->Count(1, "serial_aggr_card", "qps");
      }
    }
  }
}

void RequestAdapterV2::ParseCommonCardSecondRequestInfo(ContextData* session_data) {
  ASSERT_NOT_NULL_OTHERWISE_RETURN(session_data);
  auto* ad_request = session_data->mutable_ad_request();
  ASSERT_NOT_NULL_OTHERWISE_RETURN(ad_request);
  const auto& universe_request = session_data->get_front_server_request()->universe_request();
  if (universe_request.imp_info().ad_pos_info_size() > 0) {
    // 限定只有一个广告位，只取第一位
    const std::string& imp_ext = universe_request.imp_info().ad_pos_info(0).imp_ext_data();
    if (imp_ext.empty()) {
      return;
    }
    const base::Json imp_ext_data(base::StringToJson(imp_ext));
    if (imp_ext_data.IsObject()) {
      auto second_request_info_str = imp_ext_data.GetString("secondRequestInfo");
      if (second_request_info_str.empty()) {
        return;
      }
      std::string decode_string;
      if (!base::Base64Decode(second_request_info_str, &decode_string)) {
        session_data->dot_perf->Count(1, "common_aggr_card", "second_request_decode_failed");
        return;
      }
      if (::google::protobuf::util::JsonStringToMessage(decode_string, ad_request->mutable_common_card_request_info()).ok()) {  // NOLINT
        ad_request->set_is_common_card_second_request(true);
        bool is_small_screen = imp_ext_data.GetBoolean("isSmallScreen", false);
        ad_request->set_is_small_screen(is_small_screen);
        // 兼容短剧聚合大卡二次请求
        if (ad_request->common_card_request_info().feed_card_ind() == kuaishou::ad::AdEnum::SHORT_SERIAL) {
          ad_request->set_is_serial_aggr_card(true);
        }
        session_data->dot_perf->Count(1, "common_aggr_card", "qps", absl::StrCat(ad_request->common_card_request_info().feed_card_ind()));    // NOLINT
      } else {
        session_data->dot_perf->Count(1, "common_aggr_card", "second_request_parse_failed");
      }
    }
  }
}

void RequestAdapterV2::ParsePhotoRerankInfo(ContextData* session_data) {
  if (KS_UNLIKELY(session_data == nullptr)) {
    return;
  }
  const auto& universe_request = session_data->get_front_server_request()->universe_request();
  auto* ad_request = session_data->mutable_ad_request();
  if (KS_UNLIKELY(ad_request == nullptr)) {
    return;
  }
  if (universe_request.imp_info().ad_pos_info_size() < 1) {
    return;
  }
  // 限定只有一个广告位，只取第一位
  const std::string& imp_ext = universe_request.imp_info().ad_pos_info(0).imp_ext_data();
  if (imp_ext.empty()) {
    return;
  }
  const base::Json imp_ext_json(base::StringToJson(imp_ext));
  if (!imp_ext_json.IsObject()) {
    return;
  }

  base::Json* rerank_param = imp_ext_json.Get("rerankParam");
  if (!(rerank_param && rerank_param->IsObject())) {
    return;
  }
  base::Json* rerank_feed = rerank_param->Get("rerankFeed");
  if (rerank_feed && rerank_feed->IsObject()) {
    bool ok = false;
    std::string encode_string = rerank_feed->GetString("photoRerankReqInfo");
    std::string json_string;
    if (!encode_string.empty() && base::Base64Decode(encode_string, &json_string)) {
      ok = ::google::protobuf::util::JsonStringToMessage(json_string,
          ad_request->mutable_photo_rerank_req_info()).ok();
      ok = ok && 0 < ad_request->photo_rerank_req_info().creative_id();
      ad_request->set_is_photo_rerank_req(ok);
      session_data->set_is_photo_rerank_req(ok);
    }
    session_data->dot_perf->Count(ok, "ParsePhotoRerankInfo");
    session_data->dot_perf->Count(ok, "PhotoRerankReq", "second_req_num");
  }
  std::string ad_client_info = rerank_param->GetString("adClientInfo");
  if (!ad_client_info.empty()) {
    ad_request->set_ad_client_info(ad_client_info);
  }
}

// End of inner class AthenaAdRequest2AdReqeust implemenation
// End of inner class AthenaAdRequest2AdReqeust implemenation
// End of inner class AthenaAdRequest2AdReqeust implemenation

}  // namespace front_server
}  // namespace ks
