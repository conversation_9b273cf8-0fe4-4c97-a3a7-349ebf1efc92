#pragma once

#include <map>
#include <memory>
#include <ostream>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/time/time.h"
#include "base/common/basic_types.h"
#include "base/time/time.h"
#include "dragon/src/core/common_reco_context_interface.h"
#include "glog/logging.h"
#include "google/protobuf/arena.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "ks/base/abtest/session_context.h"
#include "nlohmann/json.hpp"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/ad_base/src/bthread/bthread_task.h"
#include "teams/ad/ad_base/src/clock_cache/expired_clock_cache.h"
#include "teams/ad/ad_base/src/common/ad_session_context.h"
#include "teams/ad/ad_base/src/common_data/freq_data.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_base/src/klog/ad_engine_chain_trace_log.h"
#include "teams/ad/ad_base/src/klog/ad_ylog.h"
#include "teams/ad/ad_base/src/klog/klog.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_base.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_base/src/redis/redis_util.h"
#include "teams/ad/ad_base/src/spdm_lib/src/context.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_forward_index.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_pack_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_style_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adsocial/ad_social_follow_reco.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_simplify_always_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/fanstop/fans_top_follow_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"
#include "teams/ad/ad_proto/maven/corerpc/photo/photo_author_service.kess.grpc.pb.h"
#include "teams/ad/engine_base/kconf/rta_config.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/engine_base/rta_retrieval/rta_ad_data.h"
#include "teams/ad/front_server/engine/context_data/ad_common.h"
#include "teams/ad/front_server/engine/context_data/ad_list.h"
#include "teams/ad/front_server/engine/context_data/base_data.h"
#include "teams/ad/front_server/engine/context_data/ueq_manager.h"
#include "teams/ad/front_server/engine/strategy/enums.h"
#include "teams/ad/front_server/trace/ad_front_simplify_always_log.h"
#include "teams/ad/front_server/trace/ad_perf_info_log.h"
#include "teams/ad/front_server/trace/ad_select_stage_info_log.h"
#include "teams/ad/front_server/util/ad_pos_manager/ad_pos_calculor.h"
#include "teams/ad/front_server/util/ad_pos_manager/ad_pos_manager.h"
#include "teams/ad/front_server/util/ad_pos_manager/ad_pos_strategy.h"
#include "teams/ad/front_server/util/ad_pos_manager/fans_pos_calculor.h"
#include "teams/ad/front_server/util/kconf/kconf.h"
#include "teams/ad/front_server/util/request_merger/dsp_request_merger.h"
#include "teams/ad/front_server/util/utility/front_logging.h"
#include "teams/ad/front_server/util/utility/strategy_util.h"
#include "teams/ad/front_server/util/utility/utils.h"
#include "teams/ad/engine_base/fanstop_common/fans_def.h"
#include "teams/ad/front_server/engine/context_data/fanstop_context/fanstop_session_data.h"
#include "teams/ad/front_server/engine/context_data/common_attrs.h"
using kuaishou::ad::AdEnum;
using kuaishou::ad::FrontServerRequest;
using kuaishou::ad::FrontServerResponse;
using kuaishou::ad::AdRequest;
using kuaishou::ad::AdResponse;
using kuaishou::ad::AdRankPassThrough;
using kuaishou::fanstop::FansTopRequest;
using kuaishou::fanstop::FansTopResponse;
using kuaishou::ad::forward_index::GetStyleInfoReq;
using kuaishou::ad::forward_index::GetStyleInfoResp;
using kuaishou::ad::forward_index::GetCreativeReq;
using kuaishou::ad::forward_index::GetCreativeResp;
using kuaishou::ad::forward_index::GetUnitReq;
using kuaishou::ad::forward_index::GetUnitResp;
using kuaishou::ad::forward_index::CreativeItem;
using kuaishou::ad::forward_index::UnitItem;
using kuaishou::ad::forward_index::UserIdPreviewInfo;
using kuaishou::ad::forward_index::UserIdPreviewResp;
using kuaishou::ad::FollowSocialRequest;
using kuaishou::ad::FollowSocialResponse;
using kuaishou::ad::FollowFanstopRequest;
using kuaishou::ad::FollowFanstopResponse;
using kuaishou::ad::AdPackRequest;
using kuaishou::ad::AdPackResponse;
using kuaishou::ad::tables::AdStyleMaterial;
using kuaishou::ad::tables::AdMagicSitePageDas;
using kuaishou::ad::tables::AdMatrixStyleMaterial;
using kuaishou::ad::tables::AdCreativePreview;
using kuaishou::ad::tables::AdEspProductLabelInfoParseFields;

namespace ks {
namespace front_server {
int64_t GenerateUnloginUserId(kuaishou::ad::AdRequest* ad_request, const std::string& app_id);

class BaseRetrievalHandler;
struct ForwardHandlerCommon;
class RetrievalRtaAdsStrategy;
class StrategyManager;

struct UniverseAuctionListParams {
  std::string key_prefix;
  std::unique_ptr<ad_base::BthreadTask> auction_list_worker;
  ks::infra::RedisResponse<std::vector<std::string>> auction_list_value;

  void Clear() {
    key_prefix = "";
    auction_list_worker.reset(nullptr);
    auction_list_value = ks::infra::RedisResponse<std::vector<std::string>>();
  }
};
struct MixBenefitParams {
  std::string high_mix_bid_ad_discount_exp_tag_ = "";
  void Clear() {
    high_mix_bid_ad_discount_exp_tag_ = "";
  }
};

class AuctionParams;
class NativeAuctionParams;

struct ResourceWhiteBoxInfo {
 public:
  std::string white_box_flow_tag;

  void Clear() {
    white_box_flow_tag.clear();
  }
};

#define _Attr_(v) v
enum CommonIdx : int {
  ALL_COMMON_ATTRS,  // 这个是上面的宏展开, 不要在这里加变量
  MAX_ATTR_NUM
};
#undef _Attr_
// 请求的 context，记录请求级的参数
struct ContextData final {
 private:
  ContextData(const ContextData& other) = delete;
  ContextData& operator = (const ContextData& other) = delete;
  static const char * attr_names_[CommonIdx::MAX_ATTR_NUM];
  mutable std::array<ks::platform::ItemAttr*, CommonIdx::MAX_ATTR_NUM> attrs_;
  bool open_mix_to_ad_pack = false;  // 单独放开 mix_rank -> ad_pack 的逻辑
  bool open_front_to_ad_pack = false;  // 单独放开 ad_front -> ad_pack 的逻辑
  kuaishou::ad::UserLastPvInfo req_pv_info;
  std::string abtest_app_id = "kuaishou";
  std::string peer_info_;  // 上游 peer 数据，包括 ip， 端口信息

  // common attr use local addr
  AdCreativePreview user_preview_info;
  AdPerfInfoLog ad_perf_info;
  DegradeLevel degrade_level = DegradeLevel::kLevelDefault;
  FansPosCalculor fans_pos_calculor;
  KnewsInspirePosStrategyInfo knews_inspire_pos_strategy_info;
  KnewsSplashPosStrategyInfo knews_splash_pos_strategy_info;
  ks::abtest2::AbtestMappingId abtest_mapping_id;
  ks::ad_base::KLog ad_klog;
  ks::ad_base::PosManagerBase pos_manager;
  ks::engine_base::RtaAdData rta_ad_data;
  kuaishou::ad::AdRankPassThrough ad_rank_pass_through;
  kuaishou::log::ad::AdTraceFilterCondition last_filter_condition_;
  kuaishou::log::ad::AdTraceFilterCondition soft_last_filter_condition_;
  MixBenefitParams mix_benefit_params;
  ResourceWhiteBoxInfo resource_white_box_info;
  UeqManager ueq_manager;
  UniverseData universe;
  UniverseRequestMergeInfo universe_request_merge_info;
  ks::spdm::Context spdm_ctx;
  ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode medium_valid_err_code =
                  ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::SUCCESS;

  kuaishou::log::ad::AdTraceFilterCondition inner_reason =
      kuaishou::log::ad::AdTraceFilterCondition::NOT_IN_ROARING_INDEX;

  // // common attr use raw ptr
  AdRequest* ad_request = nullptr;
  FansTopRequest* fanstop_request = nullptr;
  FrontServerRequest* front_server_request_ = nullptr;
  std::shared_ptr<AuctionParams> auction_params;
  std::shared_ptr<const ks::AbtestUserInfo> ab_user_info;  // 给代码中 abtestInstance 构建 user_info
  std::shared_ptr<kconf::FeedDistributioConf> feed_distribution_conf;
  std::shared_ptr<kconf::OuterNativeNegFilterRateConfig> photo_negative_tags;
  std::shared_ptr<::ks::front_server::FansTopSessionData> fanstop_ctx_;
  std::shared_ptr<::ks::infra::TailNumberV2> boost_bid_control_tail;
  std::shared_ptr<NativeAuctionParams> native_auction_params;
  std::shared_ptr<absl::flat_hash_set<int64_t>> juxing_supplement_exp_account;
  std::shared_ptr<absl::flat_hash_set<int64_t>> outer_native_diff_author_open_neg;
  std::shared_ptr<absl::flat_hash_set<std::string>> iap_ocpx_set;
  std::shared_ptr<absl::flat_hash_set<std::string>> small_game_ad_force_direct_call_black_list;
  std::shared_ptr<absl::flat_hash_set<std::string>> small_game_ad_force_direct_call_white_list;
  std::shared_ptr<absl::flat_hash_set<std::string>> cartoon_product_set;
  // common attr use local string
  std::string abtest_device_id;
  std::string antispam_ext;
  std::string app_id;
  std::string app_version;
  std::string browsed_data_redis;
  std::string device_id;
  std::string device_id_hash_key;
  std::string ky_ad_entrance;
  std::string media_wx_small_app_id;
  std::string micro_app_charge_info_key;
  std::string minigame_app_id;
  std::string minigame_from_type;
  std::string origin_age_segment = "";
  std::string search_ab_group_name;      // 搜索广告 ab 实验分组名
  std::string search_dot_query = "";
  std::string splash_boot_time;
  std::string splash_file_time;
  std::string splash_update_time;
  std::string tab_name;
  std::string test_device_id;
  std::string test_zd_ab_group_name;      // 搜索广告 ab 实验分组名 test_zd
  std::string fctr_strategy_info;
  bool lazy_init_attr_ = false;
  void LazyInitAttr(const CommonIdx &idx);

 public:
  // dragon commonattr, 读写分离, 建立 ContextData 和 dragon attr 之间的关系,
  // 1. 如果上下文中有 dragon, 请直接使用 dragon 的接口
  // 2. 如果上下文中没有 dragon, 可使用 context 获取合适的 common_r_, common_w_ 进行读写
  ks::platform::MutableRecoContextInterface* common_w_ = nullptr;
  ks::platform::ReadableRecoContextInterface* common_r_ = nullptr;
  ks::platform::ItemAttr& Attr(const CommonIdx &idx) const {
    if (attrs_[idx] == nullptr) {
      // 暂不考虑线程安全, 认为这是在单线程使用的
      attrs_[idx] = common_w_->GetCommonAttrAccessor(attr_names_[idx]);
      // 沙雕了, 大量 get 函数是 const 的, 所以必须保持函数的 const 属性
      const_cast<ContextData*>(this)->LazyInitAttr(idx);
    }
    return *attrs_[idx];
  }

  #include "context_data-context_data.inl"  // NOLINT
  #include "context_data-mix_benefit_params.inl"  // NOLINT
  #include "context_data-resource_white_box_info.inl"  // NOLINT
  #include "context_data-universe_auction_list_params.inl"  // NOLINT
  // ------ 原变量都放下面 ----------------------------------------------
  static ks::ad_base::ExpiredClockCache<std::string, int> empty_cache1;
  static ks::ad_base::ExpiredClockCache<std::string, int> empty_cache2;
  std::unique_ptr<ks::ad_base::Dot> dot_perf;  // 暂不 attr
  AdPosCalculor ad_pos_calculor;  // 这个里面有个变量用错了，暂时先不改了

 public:
  ContextData();
  ~ContextData();

  void AdPosSelectAbtestInitialize();

  void Initialize(const kuaishou::ad::FrontServerRequest& front_server_request, const std::string& peer_info,
                  StrategyManager* p_strategy_manager);
  void InitializeDragonContext(ks::platform::AddibleRecoContextInterface* context);

 public:
  // TODO(jiangyuzhen03): 后续考虑类似地把 attr 交互封装一下
  inline void SetBoolCommonAttr(const std::string& key, bool val) {
    common_w_->SetIntCommonAttr(key, val ? 1 : 0, false, false);
  }

  inline bool GetBoolCommonAttr(const std::string& key, bool _default = false) const {
    return common_r_->GetIntCommonAttr(key).value_or(_default ? 1 : 0) == 0 ? false : true;
  }

  template<typename MessageType>
  bool GetPbListCommonAttr(const std::string& key, std::vector<MessageType>* dest) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(dest, false);
    auto optional = common_r_->GetStringListCommonAttr(key);
    if (!optional.has_value()) {
      return false;
    }
    auto str_list = optional.value();
    dest->clear();
    dest->reserve(str_list.size());
    for (const auto& str : str_list) {
      dest->emplace_back();
      if (!dest->back().ParseFromString(std::string(str))) {
        dest->clear();
        return false;
      }
    }
    return true;
  }

  template<typename MessageType>
  bool GetPbListCommonAttr(const std::string& key, ::google::protobuf::RepeatedPtrField<MessageType>* dest) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(dest, false);
    auto optional = common_r_->GetStringListCommonAttr(key);
    if (!optional.has_value()) {
      return false;
    }
    auto str_list = optional.value();
    dest->Clear();
    for (const auto& str : str_list) {
      if (!dest->Add()->ParseFromString(std::string(str))) {
        dest->Clear();
        return false;
      }
    }
    return true;
  }

  template<typename MessageType>
  bool GetPbMessageCommonAttr(const std::string& key, MessageType* dest) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(dest, false);
    auto optional = common_r_->GetStringCommonAttr(key);
    if (!optional.has_value()) {
      return false;
    }
    return dest->ParseFromString(std::string(optional.value()));
  }

  template<typename EnumType>
  EnumType GetEnumCommonAttr(const std::string& key, EnumType default_value = static_cast<EnumType>(0)) {
    return static_cast<EnumType>(
        common_r_->GetIntCommonAttr(key).value_or(static_cast<int64_t>(default_value)));
  }

  template<typename KT, typename VT, template<typename, typename, typename...> typename MP>
  bool GetMapCommonAttr(const std::string& key, MP<KT, VT>* dest) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(dest, false);
    auto key_key = key + ".key";
    auto val_key = key + ".val";
    absl::Span<const KT> key_list =
      common_r_->GetListCommonAttr<KT>(key_key).value_or(absl::Span<const KT>());
    absl::Span<const VT> val_list =
      common_r_->GetListCommonAttr<VT>(key_key).value_or(absl::Span<const VT>());
    if (key_list.size() != val_list.size()) {
      return false;
    }
    for (int64_t i = 0; i < key_list.size(); ++i) {
      dest->insert({key_list[i], val_list[i]});
    }
    return true;
  }

  template<typename KT, typename VT, template<typename, typename, typename...> typename MP>
  void SetMapCommonAttr(const std::string& key, const MP<KT, VT>& mp) {
    auto key_key = key + ".key";
    auto val_key = key + ".val";
    std::vector<KT> key_list;
    std::vector<VT> val_list;
    for (const auto& [k, v] : mp) {
      key_list.push_back(k);
      val_list.push_back(v);
    }
    common_w_->SetListCommonAttr<KT>(key_key, std::move(key_list), false, false);
    common_w_->SetListCommonAttr<VT>(val_key, std::move(val_list), false, false);
  }

 private:
  // 一些基础数据需要留在 context data 的，先单独包一下
  void BaseDataInit(const kuaishou::ad::FrontServerRequest& front_server_request);
  void AbtestInitialize();
  void FollowAbtestInitialize();
  void NearbyAbtestInitialize();
  void SplashAbtestInitialize();
  void ExploreInitialize();
  void FollowInitialize();
  void NearbyInitialize();
  void SplashInitialize();
  void KwaiGalaxyInitialize();
  void MerchantInitialize();
  void SearchInitialize();
  void FillPvRecordTime(const kuaishou::ad::AdRequest& ad_request);
  void EyeMaxAdjuctPos(const kuaishou::ad::AdRequest &ad_request);
  void DyPosAdd(const kuaishou::ad::AdRequest &request);
  void FillOrientationInfo(const kuaishou::ad::AdRequest& ad_request);
  bool ExplorePreFilter(const kuaishou::ad::AthenaExploreRequest& request);
  bool FollowPreFilter(const kuaishou::ad::AthenaFollowRequest& request);
  bool NearbyPreFilter(const kuaishou::ad::AthenaNearbyRequest& request);
  bool UniversePreFilter(const kuaishou::ad::universe::AthenaAdRequest& request);
  bool SplashPreFilter(const kuaishou::ad::universe::AthenaAdRequest& request);
  bool KwaiGalaxyPreFilter(const kuaishou::ad::universe::AthenaAdRequest& request);
  bool SearchPreFilter(const kuaishou::ad::universe::AthenaAdRequest& request);
  bool IsTraceApiTraffic();
  void CheckThanosAdmitDiff(std::string req_tab_name) const;
  int64_t GetAdFanstopCreativeIdFromKconf();
  bool GetAdCreativeIdFromKconf(int64_t* unit_id, int64_t* creative_id);
  bool IsColdStartNoAdReq() const;
  bool EnableNewAdLibMerge() const;
  void InitWhiteCreativeList();
  void SplashInitWhiteCreativeList();
  void SearchInitWhiteCreativeList();
  int64_t GetAdWhiteCreativeID();
  bool GetUserPreviewAd();
  bool IsValidSearchPreview(const std::string& search_keywords);
  bool GetFanstopWhiteCreativeID(int64_t* unit_id, int64_t* creative_id);
  void InitFrontRedisConfig();
  // 获取准入保留的 type
  std::shared_ptr<absl::flat_hash_set<std::string>> GetAdmitFilterSet();
  bool IsMatrixAppInspireTraffic() const;
  // 获取真实的 user_id / device_id / gid, 用来初始化 AB
  void GetAbBaseIdInfo();
  bool IsPreviewOrDebug();
  bool AdFlowDegradedControl();
  // 分级降级
  bool IsDegradedByLevel();
  bool NewAcceptFlowExp();

 public:
  void RefreshSplashRequest();
  void PrepareDynamicAdPosition();
  void SetInventoryPosInfo(const kuaishou::ad::AdRequest &ad_request);

  void ParseMultiInfoFromResp();
  void DotMultiTag(const std::string& metric_key,
      const std::unordered_map<int32_t, int32_t>& tag_retr) const;

  bool IsUgMonopolizeFlow() const;
  FrontServerScene GetUnifyScene() const;
  bool PreFilter(const FrontServerRequest& request);
  bool IsCartoon(const std::string& product_name) const;
  bool IsTestRequest() const;
  bool IsPressTestRequest() const;
  bool IsEnableKnewsDynamicPos() const;
  bool IsEnableKnewsServerSplash(double lower_bound, double upper_bound) const;
  // 粉条 pk 中被过滤的信息流广告信息
  void AddPkFilterInfo(uint64 creative_id, uint64 position_id, uint64 price,
      kuaishou::ad::AdEnum_BidType bid_type,
      kuaishou::ad::FrontFilterInfo::FilterType filter_type);
  void RecordHardLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition);
  void RecordSoftLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition);

  bool IsFirstLoadMore() const;
  UeqManager& GetUeqManager();
  // 白名单用户判断
  bool IsWhiteListUser() const;
  bool EnableSimplifyAlwaysLog() const;
  // 计算广告位 & rank 对部分长时间不看广告用户 cpm 门槛打折 使用
  int64_t GetBrowseAdIntervalSecs(const kuaishou::ad::AdRequest &ad_request);

  bool IsSkipCommonAdmit() const;
  bool IsKnewsBrandPrefetch() const;
  bool IsKwaiMatrixFeedTraffic() const;
  bool IsSmallGameTraffic() const;
  bool IsNebulaTaskEntranceTraffic() const;
  bool IsAdmitPreFilter();
  std::string From() const;
  void GetLastPvAdInfo(const AdRequest& ad_request,
                       const bool enable_dynamic_pos,
                       kuaishou::ad::UserLastPvInfo* last_pv_info);
  bool NeedRecordPvAdInfo();

  // 判断是否是在过滤的列表里
  bool InValidFilterSet(std::string plugin_name);

  void Clear();

  bool IsKuaishouApp(const kuaishou::ad::universe::AthenaAdRequest& athena_request) const;
  bool IsKwaiMatrixApp(const kuaishou::ad::universe::AthenaAdRequest& athena_request) const;
  void Build(const ::kuaishou::ad::universe::AthenaAdRequest& athena_ad_request);
  bool IsKuaishouApp() const;
  bool IsKwaiMatrixApp() const;
  bool IsNebulaInspireTraffic() const;
  bool IsInspire() const {
    return IsNebulaInspireTraffic() || get_is_inspire_live_req() || get_is_inspire_merchant_req();
  }

  bool IsKnewsSplashTraffic() const;
  bool IsSplashTraffic() const;
  bool IsSplashRealtime() const;
  bool IsSplashPrefetch() const;
  // 公域单列混排流量
  bool IsThanosMixTraffic() const;
  int GetFanstopInvalidFlag() const;
  int GetAdInvalidFlag() const;
  void SetAdInvalidFlag(int val);
  void SetPassPreFilter(bool val);
  bool GetPassPreFilter() const;
  void SetRankMigrationSwitches();
  bool IsOneModelInvalidFlow();

  static bool GetAppId(const kuaishou::ad::AthenaFollowRequest& request, std::string* app_id) {
    if (request.product() == Product::KUAISHOU) {
      *app_id = "kuaishou";
    } else if (request.product() == Product::KUAISHOU_NEBULA) {
      *app_id = "kuaishou_nebula";
    } else {
      falcon::Inc("front_server.failed_to_get_app_id");
      return false;
    }
    return true;
  }
  // 主要是检查用户每天看广告次数是否达到限制
  bool AdBrowsedInfoFilter();

 public:
  bool NearbyOnlyRequestFanstop();
  bool IsPassDowngrade(const FrontServerRequest& front_request, const AdRequest& request);

  bool PassKspGroupCheck(const FrontServerRequest& request);
  void SetMixRequestStatus();
  void SetOpenMixToAdPack();
  void SetBrowsedDataRedis();
  bool IsUnionPlayableExpand();

  void FillAdRequestExtInfo();

  bool OpenFrontToAdPack() const;
  bool OpenMixToAdPack() const;
  void SetOpenMixToAdPack(int val);
  void RecordExpInfo(const std::string& record_tag);
  void MergeUnifyAdList();
  void SplitUnifyAdList();
  void LogInfo(const FrontServerResponse& front_response);
  // 添加额外参数，防止 fanstop 调用此函数覆盖信息流的数据
  // 后续如果 tag 不一样可能需要重构
  void ParseRankResult(const AdResponse *ad_resp, bool overwrite_tag = true);
  void RecordTimeStart(const std::string& key);

  void RecordTimeEnd(const std::string& key);

  kuaishou::ad::FrontRequestType RequestType() const {
      return front_server_request_->type();
  }

  std::string RequestShortDebugString() const {
    return front_server_request_->ShortDebugString();
  }

  const kuaishou::ad::AthenaExploreRequest& ExploreRequest() const {
    return front_server_request_->explore_request();
  }

  const kuaishou::ad::AthenaFollowRequest& FollowRequest() const {
    return front_server_request_->follow_request();
  }

  const kuaishou::ad::AthenaNearbyRequest& NearbyRequest() const {
    return front_server_request_->nearby_request();
  }

  const kuaishou::ad::universe::AthenaAdRequest& UniverseRequest() const {
    return front_server_request_->universe_request();
  }

  const std::string& TabName() const {
    return get_tab_name();
  }

  // Initialize 之后才可以用
  google::protobuf::Arena* GetArena() {
    return mutable_arena();
  }

  kuaishou::ad::ServiceDeployName GetDeployName() const {
    if (IsSplashTraffic()) {
      return kuaishou::ad::ServiceDeployName::AD_FRONT_SPLASH;
    } else if (get_pos_manager().IsSearchRequest()) {
      return kuaishou::ad::ServiceDeployName::AD_FRONT_SEARCH;
    } else if (get_is_universe_flow()) {
      return kuaishou::ad::ServiceDeployName::AD_FRONT_UNIVERSE;
    } else {
      return kuaishou::ad::ServiceDeployName::AD_FRONT_DEFAULT;
    }
  }
};

struct TimeRecorder {
  TimeRecorder(ContextData* session_data, int32 node_type) {
    enter_time_ = base::GetTimestamp();
    session_data_ = session_data;
    time_cost_.set_node_type(node_type);
  }
  ~TimeRecorder() {
    if (session_data_) {
      time_cost_.set_time_cost_ms((base::GetTimestamp() - enter_time_) / 1000);
      time_cost_.set_time_enter_ms((enter_time_ - session_data_->get_start_ts()) / 1000);
      session_data_->mutable_node_time_cost()->push_back(time_cost_);
      if (FrontKconfUtil::enableNodeCostTimePerf()) {
        session_data_->dot_perf->Interval(time_cost_.time_cost_ms(),
            "front_node_cost_time", kuaishou::ad::AdFrontNodeType_Name(time_cost_.node_type()),
            "normal");
        session_data_->dot_perf->Interval(time_cost_.time_enter_ms(),
            "front_node_enter_cost_time", kuaishou::ad::AdFrontNodeType_Name(time_cost_.node_type()),
            "normal");
      }
      time_cost_.Clear();
    }
    session_data_ = nullptr;
  }
  int64_t enter_time_ = 0;
  ContextData* session_data_ = nullptr;
  kuaishou::ad::PerfInfo::NodeTimeCost time_cost_;
};

}  // namespace front_server
}  // namespace ks
