#include "teams/ad/front_server/engine/context_data/context_data.h"

#include <algorithm>
#include <stdexcept>

#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/encoding/base64.h"
#include "base/hash_function/md5.h"
#include "base/time/timestamp.h"
#include "teams/ad/front_server/engine/context_data/base_data.h"
#include "teams/ad/front_server/engine/context_data/common_attrs.h"
#include "falcon/counter.h"
#include "gflags/gflags_declare.h"
#include "google/protobuf/repeated_field.h"
#include "infra/location/src/location/region.h"
#include "kenv/context.h"
#include "ks/base/abtest/abtest_globals.h"
#include "ks/base/abtest/abtest_instance.h"
#include "ks/base/abtest/session_context_factory.h"
#include "ks/base/abtest/single_file_dynamic_config.h"
#include "ks/serving_util/dynamic_config.h"
#include "ks/util/json.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "serving_base/region/region_dict.h"
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_base/src/admit/admit.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_base/src/common/enum.h"
#include "teams/ad/ad_base/src/common/multi_retrieval_tag.h"
#include "teams/ad/ad_base/src/common/utility.h"
#include "teams/ad/ad_base/src/kconf_flags/kconf_udf.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/log_record/ad_pv_record_util.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/util/check_reco_request_type.h"
#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/engine_base/search/util/string/query_string_normalize.h"
#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_pack_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ssp/ad_ssp.pb.h"
#include "teams/ad/ad_proto/kuaishou/newsmodel/reco_user_info.pb.h"
#include "teams/ad/ad_proto/kuaishou/search/search_realtime_action.pb.h"
#include "teams/ad/engine_base/cache_loader/support_project_p2p_cache.h"
#include "teams/ad/engine_base/cache_loader/support_project_ratio_limit.h"
#include "teams/ad/engine_base/fanstop_common/util.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/kconf/special_regions.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/engine_base/p2p_cache_loader/universe_agg_pos_replace.h"
#include "teams/ad/engine_base/rta_retrieval/rta_kconf_proxy.h"
#include "teams/ad/engine_base/spdm/spdm_switches.h"
#include "teams/ad/engine_base/universe/rank/utils/universe_media_cpm_bound_realtime.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/engine_base/search/util/card_style/card_style_utiils.h"
#include "teams/ad/front_server/bg_task/brand_preview/preview_manager_v2.h"
#include "teams/ad/front_server/bg_task/fanstop_preview_manager/fanstop_preview_manager.h"
#include "teams/ad/front_server/bg_task/universe_elastic_control/universe_agg_pos_cluster.h"
#include "teams/ad/front_server/bg_task/universe_elastic_control/universe_agg_pos_cluster_v2.h"
#include "teams/ad/front_server/bg_task/universe_phy_pos_cluster_sign/universe_phy_pos_cluster_sign.h"
#include "teams/ad/front_server/engine/context_data/utility.h"
#include "teams/ad/front_server/engine/utils/data_adapter/request_adapter.h"
#include "teams/ad/front_server/engine/utils/data_adapter/universe_ad_request_info_adapter.h"
#include "teams/ad/front_server/engine/utils/data_adapter/user_info_adapter.h"
#include "teams/ad/front_server/trace/ad_front_simplify_always_log.h"
#include "teams/ad/front_server/util/ad_browse_set/ad_browse_set.h"
#include "teams/ad/front_server/engine/strategy/retrieval_rta_ads.h"
#include "teams/ad/front_server/util/ad_pos_manager/ad_pos_manager.h"
#include "teams/ad/front_server/util/ad_user_info/ad_user_info_builder.h"
#include "teams/ad/front_server/util/app_version/app_version_constant.h"
#include "teams/ad/front_server/util/kconf/kconf.h"
#include "teams/ad/front_server/util/kconf/kconf_data.pb.h"
#include "teams/ad/front_server/util/request_merger/dsp_request_merger.h"
#include "teams/ad/front_server/engine/strategy/strategy_manager.h"
#include "teams/ad/ad_base/src/common/scoped_guard.h"
#include "teams/ad/front_server/util/spdm/spdm_switches.h"
#include "teams/ad/front_server/util/utility/ksn_util.h"
#include "teams/ad/front_server/util/utility/redis_util.h"
#include "teams/ad/front_server/util/utility/strategy_util.h"
#include "teams/ad/front_server/util/utility/utils.h"
#include "base/common/map_util-inl.h"
#include "teams/ad/front_server/engine/node/forward_handler_common.h"
#include "teams/ad/front_server/engine/node/plugins/auction/native_auction_param.h"
#include "teams/ad/front_server/engine/node/plugins/auction/auction_param.h"
#include "teams/ad/front_server/bg_task/search_hot_spot_manager/search_hot_spot_manager.h"
#include "teams/ad/front_server/bg_task/search_hot_spot_manager/search_hot_spot_manager_opt.h"
#include "teams/ad/front_server/util/abtest_id_mapping/id_mapping_client.h"
#include "teams/ad/front_server/engine/utils/pos_flags.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adx/track/adx_track.pb.h"

DECLARE_int32(ksp_group_deploy_type);
DEFINE_bool(open_chain_log, false, "open chain log");
DEFINE_UDF_KCONF(ks::front_server::kconf::ComboPressConfig, "ad.engine.comboPressConfig", comboPressConfig);

namespace ks {
namespace front_server {
// DEFINE_bool(open_chain_log, false, "open chain log");
static const auto kLocalTimeZoneCd = absl::LocalTimeZone();
#define _Attr_(v) #v
const char * ContextData::attr_names_[CommonIdx::MAX_ATTR_NUM] = { ALL_COMMON_ATTRS };
#undef _Attr_

#define DOT_PERF_RECORD_FLOW_NOT_MATCH(request_type)    \
{  \
  if (FrontKconfUtil::enableDumpPeerInfo()) {  \
    dot_perf->Count(1, "ksp_group_check_filter", ksp_group, request_type, peer_info_);  \
  } else {  \
    dot_perf->Count(1, "ksp_group_check_filter", ksp_group, request_type);  \
  }  \
}

using ks::kess::rpc::grpc::Options;

ks::ad_base::ExpiredClockCache<std::string, int> ContextData::empty_cache1;
ks::ad_base::ExpiredClockCache<std::string, int> ContextData::empty_cache2;

ContextData::ContextData() {
  Clear();  // 也是无语了, 必须先 Clear 才能用, 回头再定位是哪个变量的问题
}

void ContextData::AbtestInitialize() {
  if (FrontKconfUtil::enableNewBaseIdInit()) {
    set_user_id(get_abtest_user_id());
    set_device_id(get_abtest_device_id());
    set_app_id(abtest_app_id);
  }
  if (get_is_fake_user()) {
    set_kconf_session_context_dsp(spdm_ctx.Reset(get_llsid(), 0, get_device_id(), ks::AbtestBiz::AD_DSP,
        abtest_mapping_id));
    ab_user_info = ks::AbtestUserInfo::Builder(0, get_device_id(), get_llsid())
        .SetMappingId(abtest_mapping_id).Build();
  } else {
    set_kconf_session_context_dsp(
      spdm_ctx.Reset(get_llsid(), get_user_id(), get_device_id(), ks::AbtestBiz::AD_DSP, abtest_mapping_id));
    ab_user_info = ks::AbtestUserInfo::Builder(get_user_id(), get_device_id(), get_llsid())
        .SetMappingId(abtest_mapping_id).Build();
  }
}

void ContextData::SplashAbtestInitialize() {
  if (FrontKconfUtil::enableNewBaseIdInit()) {
    set_user_id(get_abtest_user_id());
    set_device_id(get_abtest_device_id());
    set_app_id(abtest_app_id);
  }

  set_enable_splash_rtb_realtime_recall(SPDM_enable_splash_rtb_realtime_recall(spdm_ctx));
}

bool ContextData::IsTraceApiTraffic() {
  auto redis_client =
      ks::ad_base::KconfRedis::Instance().GetAdRedisClient(AD_TRACE, ad_base::DependDataLevel::WEAK_DEPEND);
  if (redis_client == nullptr) {
    return false;
  }
  bool ret = false;
  static const int32_t kRedisTimeOutMs = 10;
  std::string value{};
  std::string key = absl::Substitute("$0$1", ks::ad_base::kTraceApiDetectionPrefix, get_user_id());
  ks::infra::RedisErrorCode code = redis_client->Get(key, &value, kRedisTimeOutMs);
  if (code == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
    auto traceutil_detection = std::make_shared<base::Json>(base::StringToJson(value));
    if (traceutil_detection->get()) {
      const std::string session_id_str = traceutil_detection->GetString("sessionId", "");
      int64_t session_id = 0;
      if (!session_id_str.empty()) {
        const auto user_info = traceutil_detection->Get("userInfo");
        const auto campaign_info = traceutil_detection->Get("campaignInfo");
        const auto unit_info = traceutil_detection->Get("unitInfo");
        const auto creative_info = traceutil_detection->Get("creativeInfo");
        if (user_info && campaign_info && unit_info && creative_info) {
          ret = true;
          Attr(CommonIdx::traceutil_detection_json).SetPtrValue(0, traceutil_detection);
        }
      }
    }
    ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "front_trace_api_detection");
  }
  return ret;
}

bool ContextData::EnableSimplifyAlwaysLog() const {
  bool enable_simplify_always_log = spdm_ctx.TryGetBoolean("enable_simplify_always_log", false);
  if (enable_simplify_always_log || FrontKconfUtil::enableSimplifyAlwaysLog()) {
    return true;
  }
  return false;
}

bool ContextData::EnableNewAdLibMerge() const {
  if (!spdm_ctx.TryGetBoolean("enable_new_adlibmerge", false)) {
    return false;
  }
  FrontServerScene scene = GetUnifyScene();
  if (FrontKconfUtil::newAdlibMergeSceneSet()->count(static_cast<int32_t>(scene)) < 1) {
    return false;
  }
  return true;
}

int64_t ContextData::GetAdWhiteCreativeID() {
  // 信息流获取用户预览 cid，平迁自 InitWhiteCreativeList
  set_is_new_preview_logic(true);
  if (GetUserPreviewAd()) {
    return user_preview_info.creative_id();
  } else {
    return -1;
  }
}


bool ContextData::IsValidSearchPreview(const std::string& search_keywords) {
  if (search_keywords.empty()) {
    return false;
  }
  base::Json search_keyword_raw_json(base::StringToJson(search_keywords));
  if (!search_keyword_raw_json.IsObject()) {
    LOG(WARNING) << "search json is not valid, " << search_keywords;
    return false;
  }
  auto search_keyword_json = search_keyword_raw_json.Get("searchKeywords");
  if (!search_keyword_json) {
    return false;
  }
  if (!search_keyword_json->IsArray() || search_keyword_json->array().size() <= 0) {
    return false;
  }
  const std::string query = front_server_request_->universe_request().search_info().query();
  for (int64_t i = 0; i < search_keyword_json->array().size(); i++) {
    auto keyword = search_keyword_json->GetString(i, "");
    if (query == keyword) {
      return true;
    }
  }
  return false;
}

bool ContextData::GetUserPreviewAd() {
  // 信息流获取用户预览 cid
  auto redis_client = ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
      AD_USER_EXPERIENCE, ad_base::DependDataLevel::WEAK_DEPEND);
  if (redis_client == nullptr) {
    return false;
  }
  std::string value{};
  std::string key = absl::Substitute("$0_$1", "creative_preview", get_user_id());
  auto code = redis_client->Get(key, &value);
  if (code == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR &&
      user_preview_info.ParseFromString(value)) {
    if (user_preview_info.creative_id() > 0 &&
        user_preview_info.unit_id() > 0) {
      if (front_server_request_->type() == kuaishou::ad::SEARCH_REQUEST) {
        if (!IsValidSearchPreview(user_preview_info.search_keywords())) {
          TLOG(INFO) << "[preview ad] user " << get_user_id() <<
              " search preview data not match, user_preview_info: "
              << user_preview_info.ShortDebugString();
          return false;
        }
      }
      set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
      TLOG(INFO) << "[preview ad] get user " << get_user_id()
                << " preview info: "
                << user_preview_info.ShortDebugString();
      return true;
    } else {
      TLOG(INFO) << "[preview ad] user " << get_user_id()
                << " preview data invalid, user_preview_info: "
                << user_preview_info.ShortDebugString();
      return false;
    }
  }
  return false;
}

// 新粉条
bool ContextData::GetFanstopWhiteCreativeID(int64_t* unit_id, int64_t* creative_id) {
  ::kuaishou::fanstop::PreviewInfo preview_info;
  auto is_follow_live = front_server_request_->type() == kuaishou::ad::FOLLOW_REQUEST;
  // 调用太靠前，pos_flags 还没生成，但只对预览生效，暂时不替换接口
  auto fanstop_type = ks::engine_base::GetFanstopType(get_page_id(), get_sub_page_id());
  auto page = ks::engine_base::GetFanstopPage(fanstop_type);
  if (!::ks::front_server::FanstopPreviewManager::Instance()->GetPreviewInfo(
        get_user_id(), page, &preview_info, true)) {
    return false;
  }
  *unit_id = preview_info.unit_id();
  *creative_id = preview_info.creative_id();
  return (*unit_id > 0 && *creative_id > 0);
}

int64_t ContextData::GetAdFanstopCreativeIdFromKconf() {
  auto ptr = FrontKconfUtil::tempFanstopPreviewConfig();
  if (ptr == nullptr) {
    return -1;
  }
  const auto& user_creatives_map = ptr->data().user_creatives();
  auto iter = user_creatives_map.find(std::to_string(get_user_id()));
  if (iter != user_creatives_map.end()) {
    for (const auto& creative_id : iter->second.creative_id()) {
      set_preview_creative_id(creative_id);
    }
  }
  const auto& user_units_map = ptr->data().user_units();
  auto itr = user_units_map.find(std::to_string(get_user_id()));
  if (itr != user_units_map.end()) {
    for (const auto& unit_id : itr->second.unit_id()) {
       return unit_id;
    }
  }
  return -1;
}

bool ContextData::GetAdCreativeIdFromKconf(int64_t* unit_id, int64_t* creative_id) {
  const auto& ptr = FrontKconfUtil::tempPreviewConfig();
  const auto& user_creatives_map = ptr->data().user_creatives();
  auto iter = user_creatives_map.find(std::to_string(get_user_id()));
  if (iter != user_creatives_map.end()) {
    for (const auto& id : iter->second.creative_id()) {
      *creative_id = id;
    }
  }
  const auto& user_units_map = ptr->data().user_units();
  auto itr = user_units_map.find(std::to_string(get_user_id()));
  if (itr != user_units_map.end()) {
    for (const auto& id : itr->second.unit_id()) {
      *unit_id = id;
    }
  }
  return (*unit_id > 0 && *creative_id > 0);
}

FrontServerScene ContextData::GetUnifyScene() const {
  if (get_is_trace_api_detection()) {
    return FrontServerScene::TRACEAPI;
  }

  auto request_type = RequestType();
  if (request_type == kuaishou::ad::FrontRequestType::EXPLORE_REQUEST) {
    return FrontServerScene::EXPLORE;
  } else if (request_type == kuaishou::ad::FrontRequestType::FOLLOW_REQUEST) {
    return FrontServerScene::FOLLOW;
  } else if (request_type == kuaishou::ad::FrontRequestType::NEARBY_REQUEST) {
    return FrontServerScene::NEARBY;
  } else if (request_type == kuaishou::ad::FrontRequestType::SPLASH_REQUEST) {
    return FrontServerScene::SPLASH;
  } else if (request_type == kuaishou::ad::FrontRequestType::KWAI_GALAXY_REQUEST) {
    return FrontServerScene::GALAXY;
  } else if (request_type == kuaishou::ad::FrontRequestType::SEARCH_REQUEST) {
    return FrontServerScene::SEARCH;
  } else if (request_type == kuaishou::ad::FrontRequestType::MERCHANT_REQUEST) {
    return FrontServerScene::MERCHANT;
  }
  return FrontServerScene::UNIVERSE;
}

void ContextData::InitWhiteCreativeList() {
  if (get_front_server_request() != nullptr) {
    const auto &universe_request = get_front_server_request()->universe_request();
    if (universe_request.imp_info().ad_pos_info_size() > 0) {
      const std::string& imp_ext = universe_request.imp_info().ad_pos_info(0).imp_ext_data();
      if (!imp_ext.empty()) {
        const base::Json imp_ext_data(base::StringToJson(imp_ext));
        if (imp_ext_data.IsObject()) {
          auto second_request_info_str = imp_ext_data.GetString("secondRequestInfo");
          if (!second_request_info_str.empty()) {
            // 商业化大卡二次请求不走预览链路
            return;
          }
        }
      }
    }
  }
  // 命中冷启动第一刷不走白名单
  if (IsColdStartNoAdReq()) {
    dot_perf->Count(1, "cold_start_pass_white_list");
    TLOG(INFO) << "[preview ad]: cold start pass white list";
    return;
  }
  // 软广在这配置
  int64_t unit_id = 0;
  int64_t creative_id = 0;
  if (GetAdCreativeIdFromKconf(&unit_id, &creative_id)) {
    user_preview_info.set_creative_id(creative_id);
    user_preview_info.set_unit_id(unit_id);
    set_preview_type(PreviewType::kPreviewAd);
    set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
    TLOG(INFO) << "[preview ad]: kconf ad preview, creative:" << creative_id
              << " unit:" << unit_id;
  }
  {
    // 广告体验, 老板模式
    const auto& boss_preview_info = FrontKconfUtil::bossModeAdPreview()->data().user_info();
    auto iter = boss_preview_info.find(get_user_id());
    const auto current_date_str = absl::FormatTime("%4Y%2m%2d%2H", absl::Now(), kLocalTimeZoneCd);
    int64 current_date = 0;
    base::StringToInt64(current_date_str, &current_date);

    if (iter != boss_preview_info.end()) {
      auto rand = ad_base::AdRandom::GetInt(1, 100);
      TLOG(INFO) << "[boss mode preview ad] get_user_id(): " << get_user_id()
                << ", preview_info: " << iter->second.ShortDebugString() << ", rank_num: " << rand
                << ", current_date: " << current_date;
      if (current_date > iter->second.expire_time() || rand > iter->second.prob()) {
        // 过时间了, 或没命中概率，不出体验
        set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::UNKONWN_FETCHER_TYPE);
        user_preview_info.Clear();
        dot_perf->Count(1, "ad_preview_boss_mode", "online");
        return;
      } else {
        set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
        set_preview_type(PreviewType::kPreviewAd);
        set_is_new_preview_logic(true);
        user_preview_info.set_creative_id(iter->second.creative_id());
        user_preview_info.set_unit_id(iter->second.unit_id());
        dot_perf->Count(1, "ad_preview_boss_mode", "preview");
        return;
      }
    }
  }

  // 1.0 正式体验重构链路
  auto ad_creative_id = GetAdWhiteCreativeID();
  if (ad_creative_id != -1) {
    set_preview_creative_id(ad_creative_id);
    set_preview_type(PreviewType::kPreviewAd);
    set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
  }

  if (IsKwaiMatrixApp()) {
    //  快看点 app_user_id 维度线上预览
    const auto &knews_preview_ptr = FrontKconfUtil::knewsPreviewWithAppUserId();
    const auto& knews_user_creatives_map = knews_preview_ptr->data().user_creatives();
    const auto app_user_id = UniverseRequest().user_info().app_user_id();
    auto knews_iter = knews_user_creatives_map.find(std::to_string(app_user_id));
    if (knews_iter != knews_user_creatives_map.end()) {
      auto & preview_creative_set = *mutable_preview_creative_set();
      for (const auto& creative_id_iter : knews_iter->second.creative_id()) {
        preview_creative_set.push_back(creative_id_iter);
      }
      // random
      ad_base::AdRandomShuffle::Shuffle(preview_creative_set.begin(), preview_creative_set.end());
      set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
      TLOG(INFO) << "[preview ad]: knews preview: app_user_id: " << app_user_id << " in preview mode";
    }
    return;
  }

  if (ad_base::IsCplRequest(get_sub_page_id())) {
    auto ext_info = UniverseRequest().cpl_request_info().ext_info();
    auto creative_iter = ext_info.find("preview_creative_id");
    auto unit_iter = ext_info.find("preview_unit_id");
    if (creative_iter != ext_info.end() && unit_iter != ext_info.end()) {
      int64_t preview_creative_id = 0;
      int64_t preview_unit_id = 0;
      if (base::StringToInt64(creative_iter->second, &preview_creative_id) && preview_creative_id > 0 &&
          base::StringToInt64(unit_iter->second, &preview_unit_id) && preview_unit_id > 0) {
        user_preview_info.set_creative_id(preview_creative_id);
        user_preview_info.set_unit_id(preview_unit_id);
        set_preview_creative_id(preview_creative_id);
        set_preview_type(PreviewType::kPreviewAd);
        set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
      }
    }
  }

  // 如果同一个 user 同时配置了粉条和信息流的预览，随机出一个
  if (ad_creative_id == -1 || ::ks::ad_base::AdRandom::GetDouble() > 0.5) {
    int64_t both_unit_id = 0;
    int64_t both_creative_id = 0;
    // Note(cuiyanliang) :后端平台预览
    if (GetFanstopWhiteCreativeID(&both_unit_id, &both_creative_id)) {
      user_preview_info.set_creative_id(both_creative_id);
      user_preview_info.set_unit_id(both_unit_id);
      set_preview_type(PreviewType::kPreviewFanstop);
      set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
    } else {
      // kconf 预览
      both_unit_id = GetAdFanstopCreativeIdFromKconf();

      if (both_unit_id != -1 && get_preview_creative_id() > 0) {
        user_preview_info.set_creative_id(get_preview_creative_id());
        user_preview_info.set_unit_id(both_unit_id);
        set_preview_type(PreviewType::kPreviewFanstop);
        set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
      }
    }
  }

  // 品牌体验白名单
  auto* preview_manager_v2 = ::ks::front_server::BrandPreviewManagerV2::GetInstance();
  if (preview_manager_v2) {
    set_is_brand_preview_user(preview_manager_v2->IsPreviewWhiteUser(get_user_id()));
  }
}

void ContextData::SplashInitWhiteCreativeList() {
  // Kconf 体验配置
  const auto ptr = FrontKconfUtil::tempPreviewConfig();
  const auto& user_creatives_map = ptr->data().user_creatives();
  auto iter = user_creatives_map.find(std::to_string(get_user_id()));
  if (iter != user_creatives_map.end()) {
    for (const auto& creative_id : iter->second.creative_id()) {
      set_preview_creative_id(creative_id);
    }
    set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
    set_preview_type(PreviewType::kBrand);
    LOG(INFO) << "User Id " << get_user_id() << " has kconf previe,  creative_id is "
              << get_preview_creative_id();
  }
  // RTB 体验白名单
  auto ad_creative_id = GetAdWhiteCreativeID();
  if (ad_creative_id != -1) {
    set_preview_creative_id(ad_creative_id);
    set_preview_type(PreviewType::kBrand);
    set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
    VLOG(4) << "User Id " << get_user_id() << " has redis preview, creative_id is " << ad_creative_id;
  }

  // 品牌体验白名单
  auto* preview_manager_v2 = ::ks::front_server::BrandPreviewManagerV2::GetInstance();
  if (preview_manager_v2) {
    set_is_brand_preview_user(preview_manager_v2->IsPreviewWhiteUser(get_user_id()));
    if (get_is_brand_preview_user()) {
      set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::UNKONWN_FETCHER_TYPE);
    }
    VLOG(4) << "User Id " << get_user_id() << " has brand preview ? " << get_is_brand_preview_user();
  }
}

void ContextData::SearchInitWhiteCreativeList() {
  const int64_t uid = front_server_request_->universe_request().user_info().user_id();
  const std::string query = front_server_request_->universe_request().search_info().query();
  VLOG(4) << "user id: " << uid << ", query: " << query;

  bool preview_fanstop = false;
  AdCreativePreview preview;
  auto &search_preview_data = *mutable_search_preview_data();
  auto &preview_relevant_author = search_preview_data.relevant_author;
  auto &multi_user_preview_info = *mutable_multi_user_preview_info();
  auto apply_kconf_rule = [&](const SearchPreviewConfig_Rule &rule) {
    LOG(INFO) << "[preview] get_llsid(): " << get_llsid() << ", user id: " << uid << ", query: " << query
              << ", apply rule: " << rule.ShortDebugString();
    if (rule.ad_infos_size() > 0) {
      for (const auto ad_info : rule.ad_infos()) {
        multi_user_preview_info.emplace_back();
        AdCreativePreview& preview_ad = multi_user_preview_info.back();
        preview_ad.set_creative_id(ad_info.creative_id());
        preview_ad.set_unit_id(ad_info.unit_id());
        SearchPreviewAdInfo detail_info;
        detail_info.is_item_kbox = ad_info.is_item_kbox();
        detail_info.kbox_strategy_for_combo = ad_info.kbox_strategy_for_combo();
        detail_info.is_live_kbox = ad_info.is_live_kbox();
        detail_info.is_search_mid_page = ad_info.is_search_mid_page();
        detail_info.is_series_card = ad_info.is_series_card();
        detail_info.is_search_fiction_card = ad_info.is_search_fiction_card();
        detail_info.product_show.CopyFrom(ad_info.product_show());
        detail_info.search_celebrity_id = ad_info.search_celebrity_id();
        detail_info.is_local_life_kbox = ad_info.is_local_life_kbox();
        detail_info.local_life_show.CopyFrom(ad_info.local_life_show());
        detail_info.is_series_kbox = ad_info.is_series_kbox();
        detail_info.deduplicate_id = ad_info.deduplicate_id();
        detail_info.series_show.CopyFrom(ad_info.series_show());
        detail_info.is_mini_game_kbox = ad_info.is_mini_game_kbox();
        detail_info.mini_game_show.CopyFrom(ad_info.mini_game_show());
        for (auto photo_id : ad_info.photo_bigv_photo_ids()) {
          detail_info.photo_bigv_photo_ids.push_back(photo_id);
        }
        search_preview_data.creative_2_info.emplace(ad_info.creative_id(), detail_info);
      }
    }
    preview_fanstop = rule.is_fanstop();
    preview.set_creative_id(rule.creative_id());
    preview.set_unit_id(rule.unit_id());
    const auto &rule_relevant_author = rule.relevant_author();
    preview_relevant_author.set_recall_type(
        (kuaishou::ad::AdBaseInfo_SearchRelevantAuthor_RecallType)rule_relevant_author.recall_type());
    preview_relevant_author.set_reason(rule_relevant_author.reason());

    search_preview_data.product_show.CopyFrom(rule.product_show());

    search_preview_data.local_life_show.CopyFrom(rule.local_life_show());
    search_preview_data.series_show.CopyFrom(rule.series_show());
    search_preview_data.mini_game_show.CopyFrom(rule.mini_game_show());

    search_preview_data.multi_retrieval_tag = rule.multi_retrieval_tag();
    search_preview_data.pos = rule.pos();
    search_preview_data.is_search_celebrity = rule.is_search_celebrity();
    search_preview_data.is_search_fiction_card = rule.is_search_fiction_card();
    search_preview_data.is_search_item_recall = rule.is_search_item_recall();
    search_preview_data.is_super_card = rule.is_super_card();
    for (const auto item_id : rule.big_v_item_ids()) {
      search_preview_data.big_v_item_vec.push_back(item_id);
    }

    search_preview_data.app_card.CopyFrom(rule.app_card());
    search_preview_data.form_card.CopyFrom(rule.form_card());
    search_preview_data.pos0 = rule.pos0();
  };

  auto check_kconf = [&](decltype(FrontKconfUtil::searchPreviewConfig()) kconf) {
    const auto &preview_config = kconf->data();
    bool rule_applied = false;

    // 基于 group 的 rule
    for (const auto &group : preview_config.groups()) {
      bool group_match = false;
      for (int64_t group_user_id : group.user_ids()) {
        if (group_user_id == uid) {
          group_match = true;
          break;
        }
      }
      if (!group_match) {
        continue;
      }
      auto iter = group.queries().find(query);
      if (iter == group.queries().end()) {
        continue;
      }
      const auto &rule = iter->second;
      apply_kconf_rule(rule);
      rule_applied = true;
      break;
    }

    // 如果基于 group 的 rule 能匹配上，不再检查老的 query 的配置
    if (rule_applied) {
      return;
    }

    auto query_config_iter = preview_config.queries().find(query);
    if (query_config_iter == preview_config.queries().end()) {
      return;
    }
    const auto &query_config = query_config_iter->second;
    for (const auto &rule : query_config.rules()) {
      bool rule_match = false;
      for (int64_t rule_user_id : rule.user_ids()) {
        if (rule_user_id == uid) {
          rule_match = true;
          break;
        }
      }
      if (!rule_match) {
        continue;
      }
      apply_kconf_rule(rule);
      rule_applied = true;
      break;
    }
  };

  check_kconf(FrontKconfUtil::searchPreviewConfig());
  check_kconf(FrontKconfUtil::searchPreviewConfigV2());  // 若匹配上，覆盖前面的

  // 后端配置的粉条预览
  // 参考 GetFanstopWhiteCreativeID，但自己提供 page
  const auto fanstop_preview_query = FrontKconfUtil::searchFanstopPreviewQuery();
  if (query == *fanstop_preview_query) {
    using FansTopEnum = kuaishou::fanstop::FansTopEnum;
    bool single_layout = kuaishou::ds::search::LayoutsStyle::SINGLE_STYLE ==
                         front_server_request_->universe_request().search_info().layout_set();
    FansTopEnum::Page fanstop_page = single_layout ? FansTopEnum::SEARCH_SINGLE : FansTopEnum::SEARCH_DUAL;
    kuaishou::fanstop::PreviewInfo fanstop_preview_info;
    bool kIsNewFanstop = true;
    if (ks::front_server::FanstopPreviewManager::Instance()->GetPreviewInfo(
            uid, fanstop_page, &fanstop_preview_info, kIsNewFanstop)) {
      preview_fanstop = true;
      preview.set_creative_id(fanstop_preview_info.creative_id());
      preview.set_unit_id(fanstop_preview_info.unit_id());
    }
  }

  if (!preview.creative_id() && multi_user_preview_info.size() == 0) {
    return;
  }

  LOG(INFO) << "[debug] get_llsid(): " << get_llsid() << ", user id: " << uid << ", query: " << query
            << ", is_fanstop = " << preview_fanstop << ", preview creative id: " << preview.creative_id()
            << ", unit id: " << preview.unit_id();

  set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
  set_is_new_preview_logic(true);
  set_preview_type(preview_fanstop ? PreviewType::kSearchFanstop : PreviewType::kSearch);
  user_preview_info.CopyFrom(std::move(preview));
}

void ContextData::FollowAbtestInitialize() {
  if (FrontKconfUtil::enableNewBaseIdInit()) {
    set_user_id(get_abtest_user_id());
    set_device_id(get_abtest_device_id());
    set_app_id(abtest_app_id);
  }
  if (get_is_fake_user()) {
    set_kconf_session_context_dsp(spdm_ctx.Reset(get_llsid(), 0, get_device_id(), ks::AbtestBiz::AD_DSP,
        abtest_mapping_id));
    ab_user_info = ks::AbtestUserInfo::Builder(0, get_device_id(), get_llsid())
        .SetMappingId(abtest_mapping_id).Build();
  } else {
    set_kconf_session_context_dsp(
      spdm_ctx.Reset(get_llsid(), get_user_id(), get_device_id(), ks::AbtestBiz::AD_DSP, abtest_mapping_id));
    ab_user_info = ks::AbtestUserInfo::Builder(get_user_id(), get_device_id(), get_llsid())
        .SetMappingId(abtest_mapping_id).Build();
  }
  LOG_EVERY_N(INFO, FrontKconfUtil::logReqDetailFrequency())
      << "Build follow abtest context, get_llsid()=" << get_llsid()
      << ", get_user_id()=" << get_user_id()
      << ", get_device_id()=" << get_device_id();
}

void ContextData::FollowInitialize() {
  // front_service VisitGraph 依赖于此, 不能迁移
  InitWhiteCreativeList();
}

void ContextData::ExploreInitialize() {
  // front_service VisitGraph 依赖于此, 不能迁移
  InitWhiteCreativeList();
  // 这个在进入 graph 之前就要用到, 一定要在 context_data 中初始化
  set_is_trace_api_detection(IsTraceApiTraffic());
  // 发现页外流 ab 初始化
  if (SPDM_enable_transparent_feed_mix_fields(spdm_ctx) &&
      ks::ad_base::IsFeedExploreRequest(get_sub_page_id())) {
    const auto& kconf_config = FrontKconfUtil::rankBenifitDistributionConf();
    std::string ab_name = SPDM_feed_mix_ab_name(spdm_ctx);
    const auto& ab_config = kconf_config->data().abtest();
    auto config_iter = ab_config.find(ab_name);
    if (config_iter != ab_config.end()) {
      feed_distribution_conf = std::make_shared<kconf::FeedDistributioConf>(config_iter->second);
    }
  }
}

// (不包含联盟) 获取 llsid / user_id / device_id 来初始化 a
void ContextData::GetAbBaseIdInfo() {
  std::string abtest_gid;
  int64_t abtest_gid_long = 0;
  static std::unordered_map<Product, std::string> product_to_appid {
    { KUAISHOU_NEBULA, "kuaishou_nebula" },
    { WECHAT_SMALL_APP, "kswechatapp" }
  };
  Product product;
  auto& device_info = front_server_request_->universe_request().device_info();
  switch (front_server_request_->type()) {
    case kuaishou::ad::EXPLORE_REQUEST:
      {
        Json extra_ctx(StringToJson(
            front_server_request_->explore_request().ad_request().extra_request_ctx()));
        set_llsid(extra_ctx.GetInt("llsid", -1));
        set_is_inner_explore_first(extra_ctx.GetInt("page", 0) == 1 ? true : false);
        // 极速版相关视频内流第一刷不请求 ad, page = 2 时将该请求当做首刷处理, 默认上一刷 page_size = 6
        if (ks::ad_base::IsInnerNebulaRelationVideoRequest(get_sub_page_id()) &&
            extra_ctx.GetInt("page", 0) == 2) {
          set_is_nebula_relation_video_first(true);
        }
      }
      set_abtest_user_id(front_server_request_->explore_request().ad_request().reco_user_info().id());
      set_abtest_device_id(
          front_server_request_->explore_request().ad_request().reco_user_info().device_id());
      abtest_gid = front_server_request_->explore_request().ad_request().gid();
      abtest_gid_long = front_server_request_->explore_request().ad_request().gid_long();
      product = front_server_request_->explore_request().ad_request().product();
      if (product_to_appid.count(product)) abtest_app_id = product_to_appid[product];
      break;
    case kuaishou::ad::FOLLOW_REQUEST:
      set_llsid(front_server_request_->follow_request().llsid());
      set_abtest_user_id(front_server_request_->follow_request().user_id());
      set_abtest_device_id(front_server_request_->follow_request().reco_user_info().device_id());
      abtest_gid = front_server_request_->follow_request().ad_request().gid();
      abtest_gid_long = front_server_request_->follow_request().ad_request().gid_long();
      product = front_server_request_->follow_request().ad_request().product();
      if (product_to_appid.count(product)) abtest_app_id = product_to_appid[product];
      break;
    case kuaishou::ad::NEARBY_REQUEST:
      {
        Json extra_ctx(StringToJson(
            front_server_request_->nearby_request().fans_top_request().extra_request_ctx()));
        set_llsid(extra_ctx.GetInt("llsid", -1));
        set_no_bonus_inspire_request(extra_ctx.GetBoolean("taskCompleted", false));
      }
      set_abtest_user_id(front_server_request_->nearby_request().fans_top_request().reco_user_info().id());
      set_abtest_device_id(
        front_server_request_->nearby_request().fans_top_request().reco_user_info().device_id());
      product = static_cast<Product>(front_server_request_->nearby_request().fans_top_request().product());
      if (product_to_appid.count(product)) abtest_app_id = product_to_appid[product];
      break;
    case kuaishou::ad::SPLASH_REQUEST:
    case kuaishou::ad::KWAI_GALAXY_REQUEST:
    case kuaishou::ad::SEARCH_REQUEST:
    case kuaishou::ad::MERCHANT_REQUEST:
      set_abtest_user_id(front_server_request_->universe_request().user_info().user_id());
      abtest_gid = front_server_request_->universe_request().user_info().gid();
      abtest_gid_long = front_server_request_->universe_request().user_info().gid_long();
      abtest_app_id = front_server_request_->universe_request().app_info().app_id();
      set_llsid(front_server_request_->universe_request().llsid());
      if (!device_info.device_id().empty()) {
        set_abtest_device_id(device_info.device_id());
      } else {
        using kuaishou::ad::universe::DeviceInfo;
        if (device_info.os_type() == DeviceInfo::OsType::DeviceInfo_OsType_ANDROID) {
          if (!device_info.android_id().empty()) {
            set_abtest_device_id("ANDROID_" + device_info.android_id());
          } else if (!device_info.android_id_md5().empty() &&
                    device_info.android_id_md5() != "d41d8cd98f00b204e9800998ecf8427e") {  // md5("")
            set_abtest_device_id("ANDROID_MD5_" + device_info.android_id_md5());
          } else if (!device_info.imei().empty() && device_info.imei() != " " &&
                     device_info.imei() != "KwAd_DEFAULT_IMEI" &&
                     device_info.imei() != "000000000000000") {
            set_abtest_device_id("IMEI_" + device_info.imei());
          } else if (!device_info.imei_md5().empty() &&
                     device_info.imei_md5() != "d41d8cd98f00b204e9800998ecf8427e") {
            set_abtest_device_id("IMEI_MD5_" + device_info.imei_md5());
          } else {
            set_abtest_device_id("OAID_" + device_info.oaid());
          }
        } else if (device_info.os_type() == DeviceInfo::OsType::DeviceInfo_OsType_IOS) {
          set_abtest_device_id("IDFA_" + device_info.idfa());
        }
      }
      break;
    case kuaishou::ad::UNIVERSE_REQUEST:
      // 联盟直接返回
      return;
    default:
      break;
  }
  if (get_abtest_user_id() <= 0) {
    AdRequest tmp_ad_request;  // gid / get_device_id() / gid_long
    tmp_ad_request.mutable_ad_user_info()->set_device_id(get_abtest_device_id());
    tmp_ad_request.set_gid(abtest_gid);
    tmp_ad_request.set_gid_long(abtest_gid_long);
    if (IsKwaiMatrixApp()) {
      // 这里从画像中碰撞 user_id
      AdUserInfoBuilder::BuildKnewsAdUserId(front_server_request_->universe_request(),
                                            &tmp_ad_request, front_server_request_->from());
      if (tmp_ad_request.ad_user_info().id() > 0) {
        set_abtest_user_id(tmp_ad_request.ad_user_info().id());
        return;   // 取到直接返回
      }
    }
    // 用 gid & device_id fake 一个用来做 ab
    if (!SPDM_remove_mock_user_id(spdm_ctx)) {
      set_abtest_user_id(UserInfoAdapter::GenerateUnloginUserId(&tmp_ad_request, abtest_app_id));
    }
    set_is_fake_user(true);
  }
  if (FrontKconfUtil::enableGetAbMappingIdFromReq()) {
    ks::abtest2::AbtestMappingId tmp_id;
    // 优先取上游下发的 mapping_id
    tmp_id = AdUserInfoBuilder::GetAbtestMappingId(*front_server_request_);
    if (tmp_id.mapping_ids().size() == 0) {
      // 访问 mapping_id_service 获取
      if (SPDM_enableAbtestMappingIdService() &&
          FrontKconfUtil::requestMappingIdServiceFlowType()->count(front_server_request_->type()) > 0) {
        tmp_id = ks::front_server::IdMappingClient::Instance().GetIdMapping(
        std::to_string(get_llsid()), get_abtest_user_id(), get_abtest_device_id());
        // 请求 mapping_id_service 监控
        dot_perf->Count(1, "req_mapping_service",
                    kuaishou::ad::FrontRequestType_Name(front_server_request_->type()),
                    (tmp_id.mapping_ids_size() > 0 ? "valid" : "invalid"));
      }
    }
    abtest_mapping_id.Swap(&tmp_id);
    const_cast<ks::platform::CommonRecoRequest*>(common_r_->GetRequest())
        ->mutable_abtest_mapping_id()
        ->CopyFrom(abtest_mapping_id);
  }
}

bool ContextData::IsCartoon(const std::string& product_name) const {
  return cartoon_product_set && cartoon_product_set->count(product_name) > 0;
}

bool ContextData::IsTestRequest() const {
  // 为了临时测试，可以指定某台机器放开测试可写操作
  if (FrontKconfUtil::enableOpenTest()) {
    return false;
  }
  // 测试环境和压测流量先都当成测试流量，不能进行数据的写操作
  return (get_for_test() || get_fake_user_type() != ::kuaishou::ad::UserFakeType::NORMAL_USER);
}

bool ContextData::IsPressTestRequest() const {
  return get_fake_user_type() != ::kuaishou::ad::UserFakeType::NORMAL_USER;
}

void ContextData::SearchInitialize() {
  SearchInitWhiteCreativeList();
  if (FrontKconfUtil::enableSearchNewPreiview() && get_preview_creative_set().empty() &&
      user_preview_info.creative_id() == 0) {
    InitWhiteCreativeList();
  }
  set_search_inner_ab_exp(ks::abtest::AbtestInstance::GetInteger(ks::AbtestBiz::USER_SEARCH,
      "search_ads_inner_stream_stratergy", get_user_id(), get_device_id(), 0));
  set_enable_search_brand_one_ad_list(SPDM_enableSearchBrandOneAdList());
  int32_t from_page = front_server_request_->universe_request().search_info().from_page();
  const auto& ext_params =
      front_server_request_->universe_request().search_info().combo_search_params().ext_params();
  int32_t sug_type = 0;
  if (!ext_params.empty()) {
    base::Json ext_params_json(StringToJson(ext_params));
    if (ext_params_json.IsObject()) {
      sug_type = ext_params_json.GetInt("sugType", 0);
      set_search_creator_uid(ext_params_json.GetInt("creator_uid", 0));
    }
    bool is_from_inspire = ext_params_json.GetBoolean("isFromInspire", false);
    if (is_from_inspire) {
      int32_t inspire_task_id = ext_params_json.GetInt("taskId", 0);
      if (inspire_task_id > 0) {
        Attr(CommonIdx::search_inspire_task_id).SetIntValue(inspire_task_id);
      }
    }
    set_is_user_sug_scene((from_page == 2 && sug_type == 1));
    if (get_is_user_sug_scene()) {
      auto& statege_data =
          front_server_request_->universe_request().search_info().combo_search_params().strategy_data();
      auto strategy_data_iter = statege_data.find("signalParams");
      if (strategy_data_iter != statege_data.end()) {
        std::string signal_params_encode = strategy_data_iter->second;
        std::string signal_params_decode;
        ::kuaishou::search::SeClientRealTimeActionList signal_params_pb;
        if (signal_params_encode != "" && base::Base64Decode(signal_params_encode, &signal_params_decode)) {
          if (signal_params_pb.ParseFromString(signal_params_decode)) {
            const auto& se_inner_ex_param = signal_params_pb.search_inner_signal().inner_params_list();
            for (const auto& se_inner_param : se_inner_ex_param) {
              if (se_inner_param.key() == "author_id") {
                int64_t sug_recommend_uid = 0;
                base::StringToInt64(se_inner_param.value(), &sug_recommend_uid);
                set_sug_recommend_uid(sug_recommend_uid);
              }
            }
          }
        }
      }
    }
  }
  dot_perf->Count(1, "search_from_page_info", std::to_string(from_page), std::to_string(sug_type),
                  get_sug_recommend_uid() == 0 ? "has_uid" : "no_uid");
  dot_perf->Count(1, "search_creator_uid", std::to_string(from_page),
                  get_search_creator_uid() == 0 ? "has_uid" : "no_uid");
  const auto search_source = front_server_request_->universe_request().search_info().search_source();
  const auto& query = front_server_request_->universe_request().search_info().query();
  set_test_zd_ab_group_name(spdm_ctx.TryGetString("search_kbox_group", "default"));
  dot_perf->Count(1, "test_zd_front", "req", get_test_zd_ab_group_name());
  set_enable_combo_search_req_ad_pack(true);
  // 搜索广告染色实验
  set_search_ad_ghost(spdm_ctx.TryGetBoolean("search_ad_ghost", false));
  // 兼容搜索容器实验
  int32_t search_shield = ks::abtest::AbtestInstance::GetInteger(
      ks::AbtestBiz::KUAISHOU_APPS, "enable_shield_search_backend", get_user_id(), get_device_id(), 0);
  dot_perf->Count(1,
                  "search_ad_ghost_merge",
                  get_search_ad_ghost() ? "ad-true" : "ad-false",
                  search_shield ? "ds-true" : "ds-false");
  set_search_ad_ghost(get_search_ad_ghost() || search_shield);

  // 搜索综搜命中热点词不出广告
  if (SearchHotSpotManagerOpt::Instance()->HitHotSpotStrategy(query)) {
    dot_perf->Count(1, "search_hit_hot_spot_filter");
    // 新增热点词避让大 V 逻辑
    auto query_hit_big_v = [&]() -> bool {
      const absl::TimeZone kLocalTimeZone = absl::LocalTimeZone();
      const auto hot_spot_avoid_config_shared = FrontKconfUtil::searchHotSpotAvoidBigVConf();
      const auto &hot_spot_avoid_config_data = hot_spot_avoid_config_shared->data();
      if (!hot_spot_avoid_config_data.enable()) return false;
      const auto& query_avoid_config_map = hot_spot_avoid_config_data.query_avoid_config();
      auto it = query_avoid_config_map.find(query);
      if (it == query_avoid_config_map.end()) return false;
      const auto& query_avoid_config = it->second;

      // 配置生效时间判断
      if (query_avoid_config.has_schedule_config()) {
        const std::string& start_time_str = query_avoid_config.schedule_config().start_time();
        const std::string& end_time_str = query_avoid_config.schedule_config().end_time();
        if (start_time_str.empty() || end_time_str.empty()) {
          return false;
        }
        std::string errmsg;
        absl::Time start_time;
        absl::Time end_time;
        absl::Time now = absl::Now();
        if (!absl::ParseTime("%Y-%m-%d %H:%M:%S", start_time_str, kLocalTimeZone, &start_time, &errmsg)) {
          dot_perf->Count(1, "hot_spot_avoid_big_v", "time_parse_error",
                          query);
          return false;
        }
        if (!absl::ParseTime("%Y-%m-%d %H:%M:%S", end_time_str, kLocalTimeZone, &end_time, &errmsg)) {
          dot_perf->Count(1, "hot_spot_avoid_big_v", "time_parse_error",
                          query);
          return false;
        }
        if (start_time >= end_time) {
          dot_perf->Count(1, "hot_spot_avoid_big_v", "time_config_invalid",
                          query);
          return false;
        }
        auto duration = end_time - start_time;
        int32_t days = absl::ToInt64Seconds(duration) / (60 * 60 * 24);
        if (days > 7) {
          dot_perf->Count(1, "hot_spot_avoid_big_v", "time_config_too_long",
                          query);
          return false;
        }
        if (now < start_time || now > end_time) {
          if (now > end_time) {
            dot_perf->Count(1, "hot_spot_avoid_big_v", "time_config_expired",
                            query);
          }
          return false;
        }
        dot_perf->Count(1, "hot_spot_avoid_big_v", "success");
        return true;
      } else {
        dot_perf->Count(1, "hot_spot_avoid_big_v", "not_time_config");
      }
      return false;
    };
    if (!query_hit_big_v()) {
      set_search_hit_hot_spot_query(true);
    }
  }
  set_search_dot_query(ks::ad_target_search::GetQueryNeedDot(query));

  /* 分广告位 query 干预配置
   * operation_type 枚举值介绍：
   *  0为 默认值, query 未命中黑名单,不干预
   *  1为 首刷 且在 top4 不出广告
   *  2为 首刷不出广告
   *  3为 所有刷次不出广告
  */
  const auto tab_query_intervene_config_shared = FrontKconfUtil::searchTabQueryInterveneConf();
  const auto &query_intervene_config_data = tab_query_intervene_config_shared->data();
  auto iter = query_intervene_config_data.query_intervene_conf().find(query);
  if (iter != query_intervene_config_data.query_intervene_conf().end() &&
    std::count(iter->second.sub_page_id_list().begin(),
              iter->second.sub_page_id_list().end(),
              get_sub_page_id()) > 0) {
    auto &search_intervene_conf = *mutable_search_intervene_conf();
    search_intervene_conf = iter->second.detail_conf();
    dot_perf->Count(1, "search_tab_query_intervene",
                  std::to_string(search_intervene_conf.operation_type()));
  }
}

void ContextData::NearbyInitialize() {
  // front_service VisitGraph 依赖于此, 不能迁移
  InitWhiteCreativeList();
}

int64_t ContextData::GetBrowseAdIntervalSecs(const kuaishou::ad::AdRequest &ad_req) {
  int64_t max_timestamp_ms = 0;
  for (int i = 0; i < ad_req.ad_user_info().ad_browsed_info_size(); ++i) {
    const auto& ad_browsed_info = ad_req.ad_user_info().ad_browsed_info(i);
    const int64_t timestamp = ad_browsed_info.timestamp() / 1000;
    if (timestamp > max_timestamp_ms) {
      max_timestamp_ms = timestamp;
    }
  }
  int64_t now_ms = base::GetTimestamp() / 1000;
  return (now_ms - max_timestamp_ms) / 1000;
}

void ContextData::FillAdRequestExtInfo() {
  if (!ad_request) {
    LOG_EVERY_N(INFO, 1000) << "TTTT:" << (ad_request == nullptr);
    return;
  }

  auto press_config = kconf_comboPressConfig();
  if (!press_config || press_config->world().empty()) {
    return;
  }

  SingleWorldExperimentInfo info = spdm_ctx.Raw()->TryGetExpGroupInfo(press_config->world());
  if (press_config->exp().empty() || info.experimentId == press_config->exp()) {
    ad_request->mutable_ext_info()->set_combo_group(info.groupId);
  }
  LOG_EVERY_N(INFO, 1000) << "TTTT: user_id" << ad_request->ad_user_info().id()
                          << " combo_group:" << info.groupId << " ext_info"
                          << ad_request->ext_info().ShortDebugString()
                          << " config:" << kconf_comboPressConfig()->ShortDebugString();
}

void ContextData::SplashInitialize() {
  SplashAbtestInitialize();
  SplashInitWhiteCreativeList();
}

void ContextData::RefreshSplashRequest() {
  auto* mutable_splash_req_info = ad_request->mutable_universe_ad_request_info()
    ->mutable_splash_req_info();
  if (mutable_splash_req_info == nullptr ||
      mutable_splash_req_info->origin_client_req_info().empty()) {
    return;
  }
  // 新版清除旧的 splash
  mutable_splash_req_info->clear_splash_id();
  kuaishou::ad::DeviceInfo *device_info = nullptr;
  if (ad_request->mutable_ad_user_info()->device_info_size() > 0) {
    device_info = ad_request->mutable_ad_user_info()->mutable_device_info(0);
  } else {
    device_info = ad_request->mutable_ad_user_info()->add_device_info();
  }
  kuaishou::ad::UniverseImpressionInfo *imp_info = nullptr;
  if (ad_request->mutable_universe_ad_request_info()->imp_info_size() > 0) {
    imp_info = ad_request->mutable_universe_ad_request_info()->mutable_imp_info(0);
  } else {
    imp_info = ad_request->mutable_universe_ad_request_info()->add_imp_info();
  }
  base::Json splash_req_json(
      base::StringToJson(mutable_splash_req_info->origin_client_req_info()));
  for (auto iter = splash_req_json.object_begin();
      iter != splash_req_json.object_end(); ++iter) {
    if (iter->first == "splashId" || iter->first == "splashMaterialIds") {
      if (iter->second->IsArray() && iter->second->size() > 0) {
        mutable_splash_req_info->clear_splash_id();
        for (const auto& item : iter->second->array()) {
          mutable_splash_req_info->add_splash_id(item->StringValue(""));
        }
      }
    } else if (iter->first == "lastImpressionTime") {
      mutable_splash_req_info->set_last_impression_time(iter->second->IntValue(1));
    } else if (iter->first == "totalImpressionCount") {
      mutable_splash_req_info->set_total_impression_count(iter->second->IntValue(1));
    } else if (iter->first == "appStartType") {
      auto app_start_type = ::kuaishou::ad::SplashReqInfo_AppStartType::
        SplashReqInfo_AppStartType_COLD;
      if (iter->second->IntValue(-1) == 1) {
        app_start_type = ::kuaishou::ad::SplashReqInfo_AppStartType::
          SplashReqInfo_AppStartType_HOT;
      } else if (iter->second->IntValue(-1) == 2) {
        app_start_type = ::kuaishou::ad::SplashReqInfo_AppStartType::
          SplashReqInfo_AppStartType_WARM;
      }
      mutable_splash_req_info->set_app_start_type(app_start_type);
    } else if (iter->first == "coldStartTimes") {
      mutable_splash_req_info->set_cold_start_times(iter->second->IntValue(1));
    } else if (iter->first == "warmStartTimes") {
      mutable_splash_req_info->set_warm_start_times(iter->second->IntValue(1));
    } else if (iter->first == "hotStartTimes") {
      mutable_splash_req_info->set_hot_start_times(iter->second->IntValue(1));
    } else if (iter->first == "lastPrefetchTimestamp") {
      mutable_splash_req_info->set_last_prefetch_time(iter->second->IntValue(1));
    } else if (iter->first == "imei") {
      device_info->set_imei(iter->second->StringValue(""));
    } else if (iter->first == "idfa") {
      device_info->set_idfa(iter->second->StringValue(""));
    } else if (iter->first == "oaid") {
      device_info->set_oaid(iter->second->StringValue(""));
    } else if (iter->first == "width") {
      if (ad_request->ad_user_info().platform().compare("ios") == 0 &&
          engine_base::CompareAppVersion(ad_request->ad_user_info().platform_version(), "9.2.50") < 0) {
        int64_t width = 1;
        base::StringToInt64(iter->second->StringValue("1"), &width);
        imp_info->set_width(width);
      } else {
        imp_info->set_width(iter->second->IntValue(1));
      }
    } else if (iter->first == "height") {
      if (ad_request->ad_user_info().platform().compare("ios") == 0 &&
          engine_base::CompareAppVersion(ad_request->ad_user_info().platform_version(), "9.2.50") < 0) {
        int64_t height = 1;
        base::StringToInt64(iter->second->StringValue("1"), &height);
        imp_info->set_height(height);
      } else {
        imp_info->set_height(iter->second->IntValue(1));
      }
    } else if (iter->first == "enableRealtime") {
      mutable_splash_req_info->set_enable_realtime(iter->second->BooleanValue(false));
    } else if (iter->first == "userType") {
      if (iter->second->StringValue("") == "lahuo") {
        ad_request->mutable_front_internal_data()->set_is_lahuo_user(true);
      }
    } else if (iter->first == "supportGyroscope") {
      set_is_support_gyroscope(iter->second->BooleanValue(false));
    } else if (iter->first == "reason") {
      std::string failed_reason = iter->second->StringValue("");
      mutable_splash_req_info->set_reason(failed_reason);
    } else if (iter->first == "kenyIds") {
      base::Json keny_ids_json(base::StringToJson(iter->second->StringValue("")));
      if (keny_ids_json.IsArray() && keny_ids_json.size() == 2) {
        const auto& kenyid0 = keny_ids_json.Get(0);
        const auto& kenyid1 = keny_ids_json.Get(1);
        if (kenyid0->GetString("version", "").compare(kenyid1->GetString("version", "")) > 0) {
          device_info->set_current_caid(kenyid0->GetString("kenyId", ""));
          device_info->set_current_caid_version(kenyid0->GetString("version", ""));
          device_info->set_last_caid(kenyid1->GetString("kenyId", ""));
          device_info->set_last_caid_version(kenyid1->GetString("version", ""));
        } else {
          device_info->set_current_caid(kenyid1->GetString("kenyId", ""));
          device_info->set_current_caid_version(kenyid1->GetString("version", ""));
          device_info->set_last_caid(kenyid0->GetString("kenyId", ""));
          device_info->set_last_caid_version(kenyid0->GetString("version", ""));
        }
      }
    } else if (iter->first == "fileTime") {
      set_splash_file_time(iter->second->StringValue(""));
    } else if (iter->first == "updateTime") {
      set_splash_update_time(iter->second->StringValue(""));
    } else if (iter->first == "bootTime") {
      set_splash_boot_time(iter->second->StringValue(""));
    }
  }
  if (ad_request->ad_user_info().platform().compare("ios") == 0) {
    set_is_support_gyroscope(true);
  }
}

void ContextData::KwaiGalaxyInitialize() {
  //首先判断是否进入预览模式
  InitWhiteCreativeList();
}

void ContextData::MerchantInitialize() {
  set_enable_guess_like_reset_product_id(SPDM_enable_guess_like_reset_product_id(spdm_ctx));
  InitWhiteCreativeList();
}

// 返回 true 表示通过过滤，可以进行后续处理，false 表示未通过过滤
bool ContextData::ExplorePreFilter(const kuaishou::ad::AthenaExploreRequest& request) {
  auto& ad_req = request.ad_request();
  std::string product_name = Product_Name(ad_req.product());
  ClientId client_id = static_cast<ClientId>(ad_req.reco_user_info().client_id());

  auto filter = [&]() -> bool {
    const std::string& app_ver = ad_req.reco_user_info().app_version();
    if (ad_req.product() == WECHAT_SMALL_APP) {
      return true;
    }
    if (client_id >= CLIENT_ID_NUM) {
      dot_perf->Count(1, "front_server_prefilter", "invalid_client_id", "explore", product_name);
      return false;
    }

    if (ad_req.product() == KUAISHOU_NEBULA) {
      // 快手极速版 android 单独进行版本控制， ios 版本不出广告
      if (AppVerConstantUtil::NEBULA_AD().NotSupport(app_ver, client_id)) {
        dot_perf->Count(1, "front_server_prefilter", "nebula_ad_not_support", "explore", product_name);
        return false;
      }
      if ((ad_req.reco_user_info().id() % 100) > FrontKconfUtil::nebulaExploreSwitch()) {
        dot_perf->Count(1, "front_server_prefilter", "nebula_explore_switch", "explore", product_name);
        return false;
      }
    } else if (AppVerConstantUtil::AD().NotSupport(app_ver, client_id)) {
      dot_perf->Count(1, "front_server_prefilter", "ad_not_support", "explore", product_name);
      return false;
    }
    return true;
  };

  dot_perf->Count(1, "front_server_prefilter_process_num", "explore", product_name);
  if (!filter()) {
    dot_perf->Count(1, "front_server_prefilter", "total_prefilter_num", "explore", product_name);
    return false;
  }
  return true;
}

bool ContextData::FollowPreFilter(const kuaishou::ad::AthenaFollowRequest& request) {
  std::string product_name = Product_Name(Product::UNKNOWN_PRODUCT);
  dot_perf->Count(1, "front_server_prefilter_process_num", "follow", product_name);
  if (!FrontKconfUtil::enableFollow()) {
    dot_perf->Count(1, "front_server_prefilter", "enable_follow_prefilter", "follow", product_name);
    return false;
  }
  return true;
}

bool ContextData::UniversePreFilter(const kuaishou::ad::universe::AthenaAdRequest& request) {
  // 如果本次请求对应的广告位都被关闭 直接过滤掉本次 pv
  if (universe.universe_imp_info.empty()) {
    auto product_name = request.app_info().name();
    dot_perf->Count(1, "front_server_prefilter", "empty_universe_imp_info", "universe", product_name);
    return false;
  }
  return true;
}

bool ContextData::NearbyPreFilter(const kuaishou::ad::AthenaNearbyRequest& request) {
  auto& fanstop_req = request.fans_top_request();
  ClientId client_id = static_cast<ClientId>(fanstop_req.reco_user_info().client_id());
  std::string product_name = Product_Name(static_cast<Product>(fanstop_req.product()));

  auto filter = [&]() -> bool {
    const std::string& app_ver = fanstop_req.reco_user_info().app_version();
    if (client_id >= CLIENT_ID_NUM) {
      dot_perf->Count(1, "front_server_prefilter", "invalid_client_id", "nearby", product_name);
      return false;
    }
    if (fanstop_req.reco_user_info().id() <= 0) {
      dot_perf->Count(1, "front_server_prefilter", "invalid_reco_user_id", "nearby", product_name);
      return false;
    }
    if (fanstop_req.product() == KUAISHOU_NEBULA) {
      // 快手极速版在出口做版本控制
      if ((fanstop_req.reco_user_info().id() % 100) > FrontKconfUtil::nebulaNearbySwitch()) {
        dot_perf->Count(1, "front_server_prefilter", "nebula_nearby_switch_filter", "nearby", product_name);
        return false;
      }
    } else if (AppVerConstantUtil::AD().NotSupport(app_ver, client_id)) {
      dot_perf->Count(1, "front_server_prefilter", "ad_not_support", "nearby", product_name);
      return false;
    }
    return true;
  };

  dot_perf->Count(1, "front_server_prefilter_process_num", "nearby", product_name);
  if (!filter()) {
    dot_perf->Count(1, "front_server_prefilter", "total_prefilter_num", "nearby", product_name);
    return false;
  }

  return true;
}

bool ContextData::SplashPreFilter(const kuaishou::ad::universe::AthenaAdRequest& request) {
  auto app_name = request.app_info().name();
  if (request.user_info().user_id() < 0) {
    dot_perf->Count(1, "front_server_prefilter", "invalid_user_id", "splash", app_name);
    return false;
  }
  const std::string& appid = request.app_info().app_id();
  if (appid != "kuaishou" && appid != "kuaishou_nebula" &&
      appid != "m2u" && appid != "kmovie") {
    LOG(WARNING) << "invalid appid for splash: " << appid;
    return false;
  }
  return true;
}

bool ContextData::KwaiGalaxyPreFilter(const kuaishou::ad::universe::AthenaAdRequest& request) {
  return true;
}

bool ContextData::SearchPreFilter(const kuaishou::ad::universe::AthenaAdRequest& request) {
  const std::string& appid = request.app_info().app_id();
  if (appid != "kuaishou" && appid != "kuaishou_nebula") {
    dot_perf->Count(1, "front_server_prefilter", "invalid_app_id", "search", appid);
    return false;
  }

  return true;
}

// 返回 true 表示通过过滤，可以进行后续处理，false 表示未通过过滤
bool ContextData::PreFilter(const FrontServerRequest& request) {
  switch (request.type()) {
    case ::kuaishou::ad::FrontRequestType::EXPLORE_REQUEST:
      if (!ExplorePreFilter(request.explore_request())) {
        return false;
      }
      break;
    case ::kuaishou::ad::FrontRequestType::FOLLOW_REQUEST:
      if (!FollowPreFilter(request.follow_request())) {
        return false;
      }
      break;
    case ::kuaishou::ad::FrontRequestType::UNIVERSE_REQUEST:
      if (!UniversePreFilter(request.universe_request())) {
        return false;
      }
      break;
    case ::kuaishou::ad::FrontRequestType::NEARBY_REQUEST:
      if (!NearbyPreFilter(request.nearby_request())) {
        return false;
      }
      break;
    case ::kuaishou::ad::FrontRequestType::SPLASH_REQUEST:
      if (!SplashPreFilter(request.universe_request())) {
        return false;
      }
      break;
    case ::kuaishou::ad::FrontRequestType::KWAI_GALAXY_REQUEST:
      if (!KwaiGalaxyPreFilter(request.universe_request())) {
        return false;
      }
      break;
    case ::kuaishou::ad::FrontRequestType::SEARCH_REQUEST:
      if (!SearchPreFilter(request.universe_request())) {
        return false;
      }
      break;
    case ::kuaishou::ad::FrontRequestType::MERCHANT_REQUEST:
      return true;
      break;
    default:
      return false;
  }
  return true;
}

void ContextData::Initialize(
    const kuaishou::ad::FrontServerRequest& front_server_request, const std::string& peer_info,
    StrategyManager* p_strategy_manager) {
  // 异常判断
  ad_base::ScopeGuard guard([&]() {
    if (get_is_universe_flow() && ad_request && ad_request->universe_ad_request_info().imp_info_size() == 0) {
      if (dot_perf) {
        dot_perf->Count(1, "universe_imp_info_empty", absl::StrCat(get_context_init_error()));
      }
      LOG(INFO) << "universe_imp_info_empty, request:" << front_server_request.ShortDebugString();
      set_context_init_error(true);
    }
  });
  set_front_server_request(const_cast<FrontServerRequest*>(&front_server_request));
  auto arena = front_server_request.GetArena();
  set_arena(arena);
  using google::protobuf::Arena;
  bool is_universe_aggre_task = false;
  set_strategy_manager(p_strategy_manager);
  // 在 front_grpc.cc 中申请好了
  BaseDataInit(front_server_request);
  set_ad_pack_request(Arena::CreateMessage<kuaishou::ad::AdPackRequest>(arena));
  peer_info_ = peer_info;
  auction_params = std::make_shared<AuctionParams>();
  native_auction_params = std::make_shared<NativeAuctionParams>();
  // time
  auto time_now = absl::Now();
  *mutable_bd() = time_now.In(kLocalTimeZoneCd);
  // front 接收到的所有请求打点
  dot_perf->Count(1, "front_request_num_before_filter",
                  GetTabName(front_server_request_->type()));
  if (get_byte_size_dot()) {
    dot_perf->Interval(front_server_request_->ByteSize(), "front_server_request_size",
                       kuaishou::ad::AdEnum::AdRequestFlowType_Name(front_server_request.flow_type()));
  }
  if (FrontKconfUtil::oneModelNearline_enable() &&
      IsOneModelInvalidFlow()) {
    return;
  }

  // 降级操作放到最前面
  if (IsDegradedByLevel() ||
      AdFlowDegradedControl()) {
    set_context_init_error(true);
    return;
  }

  if (front_server_request.type() == kuaishou::ad::FrontRequestType::UNIVERSE_REQUEST ||
      front_server_request.type() == kuaishou::ad::FrontRequestType::KWAI_GALAXY_REQUEST ||
      front_server_request.type() == kuaishou::ad::FrontRequestType::SPLASH_REQUEST) {
    Build(front_server_request.universe_request());
  }

  // 此处做 PreFilter,避免后续无效初始化
  set_pass_pre_filter(PreFilter(front_server_request));
  if (!is_universe_aggre_task && !get_pass_pre_filter()) {
    set_context_init_error(true);
    dot_perf->Count(1, "pass_pre_filter_num");
    return;
  }

  // 目前有部分流量打错机器，在这里判断流量是否在对的机器上\
  // 不符合的请求在初始化之前过滤掉
  if (!PassKspGroupCheck(front_server_request)) {
    set_context_init_error(true);
    set_pass_pre_filter(false);
    dot_perf->Count(1, "ksp_group_check_filter", "total");
    return;
  }

  // 提前获取 user_id / device_id / app_id 等数据
  GetAbBaseIdInfo();

  local_llsid = get_llsid();
  set_tab_name(GetTabName(front_server_request_->type()));
  // 把 message 都托管到 arena 上设置到 attr 里面
#define REGISTER_ARENA_ATTR(type, attr) \
    Attr(CommonIdx::attr).SetPtrValue(0, Arena::CreateMessage<type>(arena))
  REGISTER_ARENA_ATTR(kuaishou::ad::SessionRankInfo, brand_ad_default_rank_info);
  REGISTER_ARENA_ATTR(kuaishou::ad::RankResult, brand_ad_default_rank_result);
  REGISTER_ARENA_ATTR(kuaishou::ad::algorithm::UsedItem, detail_used_item);
  REGISTER_ARENA_ATTR(kuaishou::ad::algorithm::UsedItem, union_ltr_used_item);
  REGISTER_ARENA_ATTR(kuaishou::ad::algorithm::UsedItem, used_item);
  REGISTER_ARENA_ATTR(kuaishou::ad::algorithm::UsedItem, new_creative_used_item);
  REGISTER_ARENA_ATTR(kuaishou::ad::algorithm::UsedItem, fanstop_used_item);
  REGISTER_ARENA_ATTR(kuaishou::ad::algorithm::UsedItem, archimedes_used_item);
  REGISTER_ARENA_ATTR(kuaishou::ad::TransparentPackInfo, trans_pack_info);
  REGISTER_ARENA_ATTR(AdResponse, ad_response);
  REGISTER_ARENA_ATTR(AdResponse, brand_response);
  REGISTER_ARENA_ATTR(AdResponse, amd_response);
  REGISTER_ARENA_ATTR(AdResponse, fanstop_ad_response);
  REGISTER_ARENA_ATTR(AdResponse, splash_effect_ad_response);
  REGISTER_ARENA_ATTR(GetStyleInfoReq, style_info_req);
  REGISTER_ARENA_ATTR(GetStyleInfoResp, style_info_resp);
  REGISTER_ARENA_ATTR(GetCreativeReq, dpa_creative_info_req);
  REGISTER_ARENA_ATTR(GetCreativeResp, dpa_creative_info_resp);
  REGISTER_ARENA_ATTR(GetUnitReq, dpa_unit_info_req);
  REGISTER_ARENA_ATTR(GetUnitResp, dpa_unit_info_resp);
#undef REGISTER_ARENA_ATTR

  set_start_time_ns(absl::GetCurrentTimeNanos());
  set_start_time_us(base::GetTimestamp());

  set_enable_ab_mapping_id(FrontKconfUtil::enableAbMappingId());

  // 初始化 ab
  if (front_server_request_->type() != kuaishou::ad::UNIVERSE_REQUEST) {
    if (front_server_request_->type() == kuaishou::ad::FOLLOW_REQUEST) {
      FollowAbtestInitialize();
    } else {
      AbtestInitialize();
    }
  }

  if (!(DoesFlowMatchDefaultKsn() || DoesFlowMatchFollowKsn())) {
    set_fanstop_ad_request(Arena::CreateMessage<AdRequest>(arena));
  }

  switch (front_server_request_->type()) {
    case kuaishou::ad::EXPLORE_REQUEST:
      ExploreInitialize();
      break;
    case kuaishou::ad::FOLLOW_REQUEST:
      FollowInitialize();
      break;
    case kuaishou::ad::NEARBY_REQUEST:
      NearbyInitialize();
      break;
    case kuaishou::ad::SPLASH_REQUEST:
      SplashInitialize();
      break;
    case kuaishou::ad::KWAI_GALAXY_REQUEST:
      KwaiGalaxyInitialize();
      break;
    case kuaishou::ad::SEARCH_REQUEST:
      SearchInitialize();
      break;
    case kuaishou::ad::MERCHANT_REQUEST:
      MerchantInitialize();
      break;
    default:
      // 默认过滤掉
      set_pass_pre_filter(false);
      set_context_init_error(true);
      return;
  }

  mutable_ad_select_stage_infos()->Initialize(this);
  // 新接入流量的放量实验统一在该函数中
  if (NewAcceptFlowExp()) {
    set_flow_exp_context_init_error(true);
    return;
  }

  // 广告个性化推荐状态监控
  ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "ad_personal",
                                     absl::StrCat(front_server_request_->type()),
                                     absl::StrCat(CheckAdPersonalLabel(*front_server_request_)));
  if (ks::ad_base::IsInnerNebulaRelationVideoRequest(get_sub_page_id()) &&
      FrontKconfUtil::enableInnerNebulaRelationVideoAdPosReq()) {
    auto is_enable = spdm_ctx.TryGetBoolean(
                                "enable_inner_nebula_relation_video_ad_pos", false);
    if (!is_enable) {
      set_pass_pre_filter(false);
      set_context_init_error(true);
      dot_perf->Count(1, "filter_inner_nebula_relation_video_req");
      return;
    }
  }
  set_enable_preview_use_fanstop(SPDM_enablePreviewUserFanstop());
  set_enable_skip_simple_no_dsp(SPDM_enableSkipSimpleNoDsp());
  set_enable_set_adjust_price_record(SPDM_enableSetAdjustPriceRecord());

  set_enable_kxy_subsidy_global_nc(SPDM_enable_kxy_subsidy_global_nc(spdm_ctx));

  iap_ocpx_set = FrontKconfUtil::ocpcActionTypeKminiGameIAP();

  set_enable_rank_pass_through_v2(!(IsPreviewOrDebug() ||
                                  get_is_universe_flow() || (get_tab_name() == "search")));

  // 聚星商广补余相关
  juxing_supplement_exp_account = FrontKconfUtil::juxingSupplementExpAccount();
  boost_bid_control_tail = FrontKconfUtil::boostBidControlAccountTail();
  set_enable_side_window_author_uplift_score(SPDM_enable_side_window_author_uplift_score(spdm_ctx));

  // 万合改曝光计费
  set_enable_guess_you_like_charge_action_type(SPDM_enable_guess_you_like_charge_action_type(spdm_ctx));
  set_enable_wanhe_charge_action_type_with_fanstop(
      SPDM_enable_wanhe_charge_action_type_with_fanstop(spdm_ctx));
  set_enable_wanhe_charge_action_type_subpage_id(false);
  if (FrontKconfUtil::wanheChargeActionTypeSubPageId() &&
      FrontKconfUtil::wanheChargeActionTypeSubPageId()->count(get_sub_page_id()) > 0) {
    set_enable_wanhe_charge_action_type_subpage_id(true);
  }

  set_enable_mix_info_rebuild(false);
  set_enable_mix_info_refactor(true);
  set_has_ad_credict_user_score_redis(false);
  set_is_multi_quota_flow(ks::ad_base::IsThanosMixRequest(get_sub_page_id())
      || ks::ad_base::IsFeedExploreRequest(get_sub_page_id())
      || ks::ad_base::IsInnerExploreRequest(get_sub_page_id()));
  set_online_mix_rank_diff(false);

  bool enable_split_shelf_merchant = (ks::ad_base::IsShelfMerchantRequest(get_sub_page_id()) &&
                                      SPDM_enable_split_shelf_merchant(spdm_ctx)) ||
                                     (ks::ad_base::IsMallTabReuqest(get_sub_page_id()) &&
                                      SPDM_enable_split_shelf_merchant_mall(spdm_ctx)) ||
                                     (ks::ad_base::IsBuyerHomeRequest(get_sub_page_id()) &&
                                      SPDM_enable_split_shelf_merchant_buyer(spdm_ctx)) ||
                                     (ks::ad_base::IsGuessYouLikeRequest(get_sub_page_id()) &&
                                      SPDM_enable_split_shelf_merchant_guess(spdm_ctx)) ||
                                     (ks::ad_base::IsZhuanQianTabReuqest(get_sub_page_id()) &&
                                      SPDM_enable_split_shelf_merchant_zhuanqian(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_split_shelf_merchant", enable_split_shelf_merchant);
  common_w_->SetIntCommonAttr("enable_qcpx_shelf_auction_operator",
            SPDM_enable_qcpx_shelf_auction_operator(spdm_ctx));
  bool is_mix_predict_flow = ks::ad_base::IsThanosMixRequest(get_sub_page_id());
  bool is_inner_explore_mix_predict_flow = ad_base::IsInnerExploreRequest(get_sub_page_id());
  common_w_->SetIntCommonAttr("enable_mix_predict",
      ((SPDM_enable_mix_predict(spdm_ctx) && is_mix_predict_flow) ||
              (SPDM_enable_inner_explore_mix_fctr(spdm_ctx) && is_inner_explore_mix_predict_flow)));
  common_w_->SetIntCommonAttr("enable_mix_feature_prepare",
      SPDM_enable_mix_feature_prepare(spdm_ctx) && is_mix_predict_flow);
  common_w_->SetIntCommonAttr("enable_direct_mix_predict",
        (SPDM_enable_direct_mix_predict(spdm_ctx)|| SPDM_enable_direct_mix_predict_kconf()));
  common_w_->SetIntCommonAttr("enable_direct_mix_predict_debug",  SPDM_enable_direct_mix_predict_debug());
  common_w_->SetIntCommonAttr("enable_ai_cover_v2", SPDM_enable_ai_cover_v2(spdm_ctx));
  common_w_->SetIntCommonAttr("use_data_converter_v2",
        (SPDM_use_data_converter_v2(spdm_ctx)|| SPDM_useDataConverterV2()));
  bool is_hard_soft_union_flow = (ks::ad_base::IsThanosMixRequest(get_sub_page_id())
      || ks::ad_base::IsFeedExploreRequest(get_sub_page_id())
      || ks::ad_base::IsInnerExploreRequest(get_sub_page_id()));
  if (!SPDM_hard_soft_union_only_for_thanos_mix(spdm_ctx)) {
    set_enable_hard_soft_union(SPDM_enable_hard_soft_union(spdm_ctx) && is_hard_soft_union_flow);
  } else {
    set_enable_hard_soft_union(SPDM_enable_hard_soft_union(spdm_ctx) &&
        ks::ad_base::IsThanosMixRequest(get_sub_page_id()));
  }
  if (SPDM_enable_hard_soft_union_nearby(spdm_ctx) &&
      RequestType() == kuaishou::ad::FrontRequestType::NEARBY_REQUEST) {
    set_enable_hard_soft_union(true);
    dot_perf->Count(1, "enable_hard_soft_union", "nearby");
  }
  if (SPDM_enable_hard_soft_union_follow(spdm_ctx) &&
      RequestType() == kuaishou::ad::FrontRequestType::FOLLOW_REQUEST) {
    set_enable_hard_soft_union(true);
    dot_perf->Count(1, "enable_hard_soft_union", "follow");
  }
  common_w_->SetIntCommonAttr("enable_live_copy_refactor",
      SPDM_enable_live_copy_refactor(spdm_ctx) && ks::ad_base::IsThanosMixRequest(get_sub_page_id()));
  common_w_->SetIntCommonAttr("enable_live_copy_refactor_without_union",
      SPDM_enable_live_copy_refactor_without_union(spdm_ctx) &&
      ks::ad_base::IsThanosMixRequest(get_sub_page_id()));
  common_w_->SetIntCommonAttr("live_copy_refactor_type", SPDM_live_copy_refactor_type(spdm_ctx));

  bool enable_second_forward_index =  SPDM_enableSecondForwardIndex();
  common_w_->SetIntCommonAttr("enable_second_forward_index", enable_second_forward_index);

  common_w_->SetIntCommonAttr("enable_wechat_customer_acq_direct_jump",
      SPDM_enable_wechat_customer_acq_direct_jump(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_move_back_merchant_select_ad",
      SPDM_enable_move_back_merchant_select_ad(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_split_merchant_mix_select",
      SPDM_enable_split_merchant_mix_select(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_skip_merchant_mix_select",
        SPDM_enable_skip_merchant_mix_select(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_fix_front_modify_item_info",
      SPDM_enable_fix_front_modify_item_info(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_send_iaa_source_type", SPDM_enableSendIaaSourceType());
  common_w_->SetIntCommonAttr("enable_send_rta_second_bid", SPDM_enableSendRtaSecondBid());
  common_w_->SetIntCommonAttr("disable_industry_playlet_sdpa_p2p",
      SPDM_disable_industry_playlet_sdpa_p2p(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_clear_playlet_sdpa_info",
      SPDM_enable_clear_playlet_sdpa_info(spdm_ctx));
  common_w_->SetIntCommonAttr("enable_xifan_replace_kwai_url", SPDM_enableXiFanReplaceKwaiUrl());
  common_w_->SetIntCommonAttr("enable_set_adx_third_convert_info", SPDM_enableSetAdxThirdConvertInfo());
  common_w_->SetIntCommonAttr("enable_get_router_user_info",
      SPDM_enable_get_router_user_info(spdm_ctx) || SPDM_enableGetRouterUserInfo());
  common_w_->SetIntCommonAttr("enable_set_iaa_ad_play_type", SPDM_enableSetIaaAdPlayType());
  if (DoesFlowMatchDefaultKsn()) {
    // enable_prerank_user_info_bs_truncate 设置
    // 1. kconf > 0 优先级最高: 1 开启 2 关闭
    // 2. ab 优先级其次: kconf = 0 且 ab > 0 开启
    common_w_->SetIntCommonAttr("enable_prerank_user_info_bs_truncate",
      0 < SPDM_prerankUserInfoBSTruncate() ?
      (1 == SPDM_prerankUserInfoBSTruncate()) : SPDM_enable_prerank_user_info_bs_truncate(spdm_ctx));
    common_w_->SetIntCommonAttr("enable_prerank_user_info_bs_compress",
      SPDM_enable_prerank_user_info_bs_compress(spdm_ctx) || SPDM_prerankUserInfoBSCompress());
    auto prerank_cmd_list = GetPrerankCmdAdmitList(&spdm_ctx);
    if (prerank_cmd_list) {
      common_w_->SetPtrCommonAttr("prerank_cmd_admit_list", prerank_cmd_list);
    }
    common_w_->SetIntCommonAttr("enable_send_user_feature_to_adserver",
      SPDM_enable_send_user_feature_to_adserver(spdm_ctx) || SPDM_enableSendUserFeatureToAdServer());
  } else if (DoesFlowMatchSearchKsn()) {
    common_w_->SetIntCommonAttr(
        "enable_search_get_router_user_info",
        SPDM_enable_search_get_router_user_info(spdm_ctx) || SPDM_enableGetRouterUserInfo());
    // 暂时不透传到 ad_server
    common_w_->SetIntCommonAttr("enable_send_user_feature_to_adserver", 0);
  } else if (DoesFlowMatchSplashKsn()) {
    common_w_->SetIntCommonAttr(
        "enable_splash_get_router_user_info",
        SPDM_enable_splash_get_router_user_info(spdm_ctx) || SPDM_enableGetRouterUserInfo());
    // 暂时不透传到 ad_server
    common_w_->SetIntCommonAttr("enable_send_user_feature_to_adserver",
        SPDM_enable_splash_send_user_feature_to_adserver(spdm_ctx) || SPDM_enableSendUserFeatureToAdServer());
  }
  common_w_->SetIntCommonAttr("enable_unify_grid_unit_id",
      SPDM_enable_unify_grid_unit_id(spdm_ctx) &&
      (FrontKconfUtil::unifyGridUnitIdPageid()->count(get_page_id()) > 0 ||
       FrontKconfUtil::unifyGridUnitIdSubpageid()->count(get_sub_page_id()) > 0));
  common_w_->SetIntCommonAttr("nearline_recall_front_processor_flag",
      SPDM_nearline_recall_front_processor_flag(spdm_ctx));

  common_w_->SetIntCommonAttr("enable_request_ad_match_server",
      SPDM_enable_request_ad_match_server(spdm_ctx));

  // TODO(jiangyuzhen03) 待删，录制流量 debug 用
  common_w_->SetStringCommonAttr("front_hostname", serving_base::GetHostName());
  SetRankMigrationSwitches();
  if (common_r_->GetIntCommonAttr(
        common_attr::enable_rank_migration_stage).value_or(0) >= BUILD_AD_RESPONSE_DIFF) {
    common_w_->SetPtrCommonAttr(
      common_attr::rank_response, std::move(Arena::CreateMessage<kuaishou::ad::AdRankResponse>(arena)));
    common_w_->SetPtrCommonAttr(
      common_attr::ad_response_new, std::move(Arena::CreateMessage<AdResponse>(arena)));
    common_w_->SetPtrCommonAttr(
      common_attr::jk_response,
      std::move(Arena::CreateMessage<kuaishou::ad::adx::track::inner::TrackResponse>(arena)));
  }

  set_small_game_ad_force_direct_call_switch(SPDM_small_game_ad_force_direct_call_switch(spdm_ctx));
  small_game_ad_force_direct_call_white_list = FrontKconfUtil::smallGameForceDirectWhiteList();
  cartoon_product_set = FrontKconfUtil::cartoonSeriesProductNameSet();
  small_game_ad_force_direct_call_black_list = FrontKconfUtil::smallGameForceDirectBlackList();

  set_enable_inspire_style_form(SPDM_enable_inspire_style_form(spdm_ctx));

  set_print_factor_info_random_num(ad_base::AdRandom::GetInt(0, 15));
  set_enable_print_all_factor_info(SPDM_enablePrintAllFactorInfo());
  set_disable_mix_rank_flow_price_bound_in_front(SPDM_disable_mix_rank_flow_price_bound_in_front(spdm_ctx));

  // 投中负向信号配置
  const auto& photo_negative_kconf_config = FrontKconfUtil::outerloopAbNegFilterRateConfig();
  const auto& ab_neg_rate_config = photo_negative_kconf_config->data().ab_config();
  auto ab_neg_rate_config_iter = ab_neg_rate_config.find(
      spdm_ctx.TryGetString("outer_native_neg_filter_rate_exp_tag", "exp0"));
  if (ab_neg_rate_config_iter != ab_neg_rate_config.end()) {
    photo_negative_tags =
      std::make_shared<kconf::OuterNativeNegFilterRateConfig>(ab_neg_rate_config_iter->second);
  }
  outer_native_diff_author_open_neg = FrontKconfUtil::outerNativeDiffAuthorOpenNeg();
}

void ContextData::InitializeDragonContext(ks::platform::AddibleRecoContextInterface* context) {
  bool need_clear = (common_r_ == nullptr);
  common_w_ = context;
  common_r_ = context;
  attrs_.fill(nullptr);
  lazy_init_attr_ = SPDM_enableLazyInitExtraCommonAttr();
  if (!lazy_init_attr_) {
    #define __Attr  Attr
    #include "context_data-context_data.init.extra"  // NOLINT
    #undef __Attr
  }

  if (need_clear) {
    Clear();  // 首次调用时初始化一下
  }
  set_for_test(ks::ad_base::AdKessClient::Instance().IsTestEnv());
}

void ContextData::LazyInitAttr(const CommonIdx &idx) {
  if (!lazy_init_attr_)
    return;
#define __Attr(x) break; case (x): (*attrs_[idx])
  switch (idx) {
    case (CommonIdx::for_test):  // 占位, 为了后面的宏能展开
      #include "context_data-context_data.init.extra"  // NOLINT
      break;
    default:
      break;
  }
#undef __Attr
}

bool ContextData::IsPreviewOrDebug() {
  if (get_tab_name() == "universe" && UniverseRequest().universe_debug_param().debug_mode()) {
    return true;
  } else if (get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    return true;
  }

  return false;
}

ContextData::~ContextData() {
}

bool ContextData::IsDegradedByLevel() {
  auto [degrade_level_tmp, ratio] = FrontKconfUtil::degradeLevelConfig()->data().GetDegradeLevelAndRatio(
    front_server_request_->flow_type(),
    get_page_id(),
    get_sub_page_id());
  degrade_level = degrade_level_tmp;
  bool is_degraded = ::ks::ad_base::AdRandom::GetInt(1, 100) > ratio;
  if (KS_LIKELY(dot_perf != nullptr)) {
    dot_perf->Count(1, "degrade_by_level",
                    absl::StrCat(degrade_level),
                    is_degraded ? "degraded" : "admitted",
                    AdEnum_AdRequestFlowType_Name(front_server_request_->flow_type()));
  }
  return is_degraded;
}

bool ContextData::AdFlowDegradedControl() {
  if (front_server_request_->type() == kuaishou::ad::FOLLOW_REQUEST) {
    bool enable_follow_skip_degrade = ks::abtest::AbtestInstance::GetBoolean(
              ks::AbtestBiz::AD_DSP, "enable_follow_skip_degrade",
              front_server_request_->follow_request().user_id(),
              front_server_request_->follow_request().reco_user_info().device_id(), false);
    if (enable_follow_skip_degrade) {
      return false;
    }
  }
  auto& flow_graded_config = FrontKconfUtil::flowGradeConfig()->data();
  int32_t ratio = 100;
  // 1 先获取当前流量 ratio
  if (front_server_request_->flow_type() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_EXPLORE &&
      flow_graded_config.pb().explore_config().ratio() < 100) {
    // a. 发现页特殊处理
    auto need_degraded = [&]() -> bool {
      auto sub_page_id_mapping = FrontKconfUtil::subPageIdMapping();
      auto mapping_iter = sub_page_id_mapping->find(absl::StrCat(get_sub_page_id()));
      if (mapping_iter == sub_page_id_mapping->end()) {
        dot_perf->Count(1, "explore_id_mapping_error", "", "", "", "", false);
        // 找不到关联中文，直接降级，发现页的流量必须配置中文映射
        return true;
      }
      auto degraded_iter =
          flow_graded_config.pb().explore_config().need_degraded_list().find(mapping_iter->second);
      if (degraded_iter != flow_graded_config.pb().explore_config().need_degraded_list().end()) {
        dot_perf->Count(
            1, "explore_ad_flow_degraded", mapping_iter->second,
            absl::StrCat("old:", ratio, ",new:", flow_graded_config.pb().explore_config().ratio()),
            absl::StrCat(degraded_iter->second), "", false);
        return degraded_iter->second;
      }
      // 找不到配置，不降级
      return false;
    };
    if (need_degraded()) {
      ratio = std::min(ratio, flow_graded_config.pb().explore_config().ratio());
    }
  }
  if (flow_graded_config.pb().sub_page_config().ratio() < 100 &&
      flow_graded_config.InSubPageIdBlackList(get_sub_page_id()) > 0) {
    // b. sub_page_id 配置读取
    dot_perf->Count(1, "ad_flow_degraded_sub_page_id",
                    kuaishou::ad::AdEnum_AdRequestFlowType_Name(front_server_request_->flow_type()),
                    absl::StrCat("old:", ratio, ",new:", flow_graded_config.pb().sub_page_config().ratio()),
                    "", "", false);
    ratio = std::min(ratio, flow_graded_config.pb().sub_page_config().ratio());
  }
  auto iter = flow_graded_config.pb().ad_request_flow_type_config().find(
      kuaishou::ad::AdEnum_AdRequestFlowType_Name(front_server_request_->flow_type()));
  if (iter != flow_graded_config.pb().ad_request_flow_type_config().end() && iter->second.ratio() < 100) {
    // c. request_flow_type 配置读取
    ratio = std::min(ratio, iter->second.ratio());
    dot_perf->Count(1, "ad_flow_degraded_flow_type",
                    kuaishou::ad::AdEnum_AdRequestFlowType_Name(front_server_request_->flow_type()),
                    absl::StrCat("old:", ratio, ",new:", iter->second.ratio()), "", "", false);
  }
  // 2 再对每个下游进行 ratio 赋值
  auto random = ::ks::ad_base::AdRandom::GetInt(0, 99);
  if (ratio <= random) {
    // 对四个下游进行降级赋值
    auto &ad_flow_degraded_config = *mutable_ad_flow_degraded_config();
    ad_flow_degraded_config.ad_server_default_degraded =
        flow_graded_config.pb().downstream().ad_server_default();
    ad_flow_degraded_config.ad_server_fanstop_degraded =
        flow_graded_config.pb().downstream().ad_server_fanstop();
    ad_flow_degraded_config.ad_brand_degraded = flow_graded_config.pb().downstream().ad_brand();
    if (ad_flow_degraded_config.ad_server_default_degraded &&
        ad_flow_degraded_config.ad_server_fanstop_degraded && ad_flow_degraded_config.ad_brand_degraded) {
      // 所有下游都要降级的时候才在这个地方进行降级
      dot_perf->Count(1, "ad_flow_degraded_control", ad_flow_degraded_config.ToString(), "", "", "", false);
      return true;
    } else {
      dot_perf->Count(1, "skip_ad_flow_degraded_control", ad_flow_degraded_config.ToString(), "", "", "",
                      false);
    }
  }
  // 默认不降级
  return false;
}

// 只改了外循环，内循环后面需要自己来修改
void ContextData::ParseMultiInfoFromResp() {
  const auto * ad_resp = get_ad_response();
  if (!ad_resp) {
    return;
  }
  auto &multi_retr_cmd = *mutable_multi_retr_cmd();
  for (const auto &info : ad_resp->global_ext_data().multi_retrieval_info()) {
    multi_retr_cmd.emplace(info.tag(), info.index_name());
  }
}

void ContextData::DotMultiTag(const std::string& metric_key,
                              const std::unordered_map<int32_t, int32_t>& retr_tag_map) const {
  const auto &multi_retr_cmd = get_multi_retr_cmd();
  for (const auto& pair : retr_tag_map) {
    // 打点监控
    if (pair.second > 0) {  // NOLINT
      auto maybe_tag = ks::ad_target::multi_retr::RetrievalTag::_from_integral_nothrow(pair.first);  // NOLINT
      if (!maybe_tag) {
        continue;
      }

      std::string cmd;
      auto iter = multi_retr_cmd.find(pair.first);
      if (iter != multi_retr_cmd.end()) {
        cmd = iter->second;
      }

      const std::string& tag_string = maybe_tag->_to_string();  // NOLINT
      dot_perf->Interval(pair.second, metric_key, tag_string, cmd);
    }
  }
  return;
}

void ContextData::BaseDataInit(const kuaishou::ad::FrontServerRequest &front_server_request) {
  // 1. dot_perf 初始化
  set_page_id(front_server_request.page_id());
  set_sub_page_id(front_server_request.sub_page_id());
  // 联盟的兜底一下
  if (front_server_request.type() == kuaishou::ad::UNIVERSE_REQUEST) {
    set_page_id(19000);
    set_sub_page_id(19000001);
  }
  // dot_perf 的初始化放到最前面
  dot_perf.reset(new ks::ad_base::Dot(Product::UNKNOWN_PRODUCT,
                                      kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNKNOWN,
                                      AdEnum::INTERACTIVE_UNKNOWN_FROM, get_sub_page_id()));
  // 添加一个打点的采样
  set_byte_size_dot(
      ks::ad_base::AdRandom::GetInt(0, 10000) <= FrontKconfUtil::dotSizeSampleRate() ? true : false);
}

bool ContextData::NewAcceptFlowExp() {
  // 返回为 true, 流量会被过滤
  // 测试和预览流量不被过滤
  if (get_for_test() || get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    return false;
  }
  // 封面视频流量放量开关
  if (!SPDM_enable_cover_dsp_request(spdm_ctx) && get_sub_page_id() == 100014437) {
    return true;
  }
  // 小程序左图右文流量统一放量开关
  if (!SPDM_enableCoverMicroApp() && get_page_id() == 100016399) {
    return true;
  }
  // 默认正常请求
  return false;
}

void ContextData::SetOpenMixToAdPack() {
  // mix_rank -> ad_pack 可以单独放开推全
  open_mix_to_ad_pack = ks::ad_base::IsMixRankSupportType(get_sub_page_id()) ||
                        ks::ad_base::IsFeedExploreRequest(get_sub_page_id()) ||
                        FrontKconfUtil::subPageIdsSupportMixToAdPack()->count(get_sub_page_id());
  // 重排请求跳过混排
  if (ad_request && ad_request->is_live_rerank_req()) {
    open_mix_to_ad_pack = false;
  }
  if (ad_request && ad_request->is_aggr_second_req()) {
    open_mix_to_ad_pack = false;
  }
  if (ad_request && ad_request->is_serial_aggr_card()) {
    open_mix_to_ad_pack = false;
  }
  if (ad_request && ad_request->is_common_card_second_request()) {
    open_mix_to_ad_pack = false;
  }
  if (get_is_photo_rerank_req()) {
    // photo 重请求跳过混排
    open_mix_to_ad_pack = false;
  }
  if (open_mix_to_ad_pack) {
    dot_perf->Count(1, "open_mix_to_ad_pack_num");
  }
  ad_request->mutable_front_internal_data()->set_open_mix_to_ad_pack(open_mix_to_ad_pack);
}

void ContextData::SetMixRequestStatus() {
  if (ad_request->ad_mixed_req()) {
    dot_perf->Count(1, "reco_mixed_request_num");
  }
  open_front_to_ad_pack = spdm_ctx.TryGetBoolean(("open_front_to_ad_pack_" + get_tab_name()), false);
  open_front_to_ad_pack = open_front_to_ad_pack || FrontKconfUtil::enableAllAdPck();
  // 喜番 IAA 混排 front 跳过 ad_pack，下移至 playlet_rpc
  if (SPDM_enable_xifan_skip_front_to_ad_pack(spdm_ctx) &&
      ks::ad_base::IsXifanIaaMixRank(get_sub_page_id())) {
    open_front_to_ad_pack = false;
  }
  ad_request->mutable_front_internal_data()->set_open_front_to_ad_pack(open_front_to_ad_pack);
  set_is_mixed_request(ks::ad_base::IsMixRankSupportType(get_sub_page_id()));  //NOLINT
  if (FrontKconfUtil::enableAllAdMixed() && ks::ad_base::IsMixRankSupportType(get_sub_page_id())) {
    // 为了方便测试，可以针对一台机器放开所有的混排
    set_is_mixed_request(true);
  }
  if (get_is_mixed_request()) {
    dot_perf->Count(1, "mixed_request_num");
  }
  if (get_is_mixed_request()) {
    if (!(DoesFlowMatchDefaultKsn() || DoesFlowMatchFollowKsn())) {
      mutable_fanstop_ad_request()->set_ad_mixed_req(true);
    }
    ad_request->set_ad_mixed_req(true);
  }
}

// 非 AdMix 流量 & AdPack 开关，使用 AdPack 发送 ServerShow
bool ContextData::OpenFrontToAdPack() const {
  return !open_mix_to_ad_pack && open_front_to_ad_pack;
}

// 混排流量使用 AdMix 发送 ServerShow
bool ContextData::OpenMixToAdPack() const {
  return open_mix_to_ad_pack;
}

void ContextData::SetOpenMixToAdPack(int val) {
  open_mix_to_ad_pack = val;
}

// Detail/Explore Context Build, allow_unlogin = true
// FansTopRequest can use this, allow_unlogin = false;
void ContextData::Build(const ::kuaishou::ad::universe::AthenaAdRequest& athena_ad_request) {
  set_llsid(athena_ad_request.llsid());
  universe.Init(athena_ad_request);
  auto universe_ad_switch = FrontKconfUtil::universeAdSwitch2();
  for (auto iter = universe.universe_imp_info.begin();
       iter != universe.universe_imp_info.end();) {
    if (!universe_ad_switch->Allowed(universe.app_id,
                                     iter->page_id,
                                     iter->sub_page_id)) {
      // 对应广告位被关闭
      iter = universe.universe_imp_info.erase(iter);
    } else {
      iter++;
    }
  }
  dot_perf->Count(1, Product_Name(Product::UNKNOWN_PRODUCT), "universe", "process");
}

bool ContextData::IsUgMonopolizeFlow() const {
  auto ug_monopolize_flow = engine_base::AdKconfUtil::ugMonopolizeFlowCpm();
  if (pos_manager.request_imp_infos.empty()) {
    return false;
  }
  int64_t pos_id = pos_manager.request_imp_infos[0].pos_id;
  if (ug_monopolize_flow->count(pos_id) != 0) {
    return true;
  }
  return false;
}

void ContextData::SetBrowsedDataRedis() {
  auto app = ad_request->universe_ad_request_info().app_id();
  if (app == "kuaishou") {
    set_browsed_data_redis("adBrowseSetKuaishou");
  } else if (app == "kuaishou_nebula") {
    set_browsed_data_redis("adBrowseSetNebula");
  } else {
    set_browsed_data_redis("adBrowseSetUniverse");
  }
}

void ContextData::MergeUnifyAdList() {
  mutable_ad_list()->Compact();
  mutable_native_ad_list()->Compact();
  mutable_unify_ad_list()->clear();
  for (auto* p_ad : mutable_ad_list()->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    mutable_unify_ad_list()->push_back(p_ad);
  }
  for (auto* p_ad : mutable_native_ad_list()->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    mutable_unify_ad_list()->push_back(p_ad);
  }
  mutable_ad_list()->ClearOnlyList();
  mutable_native_ad_list()->ClearOnlyList();
}

void ContextData::SplitUnifyAdList() {
  mutable_ad_list()->ClearOnlyList();
  mutable_native_ad_list()->ClearOnlyList();
  for (auto* p_ad : get_unify_ad_list()) {
    if (p_ad == nullptr) {
      continue;
    }
    if (p_ad->ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
      mutable_native_ad_list()->Add(p_ad);
    } else {
      mutable_ad_list()->Add(p_ad);
    }
  }
  mutable_ad_list()->Compact();
  mutable_native_ad_list()->Compact();
  mutable_unify_ad_list()->clear();
}

void ContextData::RecordExpInfo(const std::string& record_tag) {
  const std::string& exp_tag = SPDM_rank_dragon_update_exp_tag(spdm_ctx);
  int64_t inner_hard_cnt = 0;
  int64_t outer_hard_cnt = 0;
  int64_t inner_soft_cnt = 0;
  int64_t outer_soft_cnt = 0;
  for (RankAdCommon *ad : mutable_ad_list()->Ads()) {
    if (ad->is_inner_loop_ad()) {
      dot_perf->Interval(ad->bonus_cpm(),
          absl::StrCat("record_exp_" + record_tag + "_bonus_cpm"),
          "inner_hard", exp_tag, "", "", false);
      inner_hard_cnt++;
    } else {
      dot_perf->Interval(ad->bonus_cpm(),
          absl::StrCat("record_exp_" + record_tag + "_bonus_cpm"),
          "outer_hard", exp_tag, "", "", false);
      outer_hard_cnt++;
    }
  }
  for (RankAdCommon *ad : mutable_native_ad_list()->Ads()) {
    if (ad->is_inner_loop_ad()) {
      dot_perf->Interval(ad->bonus_cpm(),
          absl::StrCat("record_exp_" + record_tag + "_bonus_cpm"),
          "inner_soft", exp_tag, "", "", false);
      inner_soft_cnt++;
    } else {
      dot_perf->Interval(ad->bonus_cpm(),
          absl::StrCat("record_exp_" + record_tag + "_bonus_cpm"),
          "outer_soft", exp_tag, "", "", false);
      outer_soft_cnt++;
    }
  }
  dot_perf->Interval(inner_hard_cnt,
      absl::StrCat("record_exp_" + record_tag + "_cnt"),
      "inner_hard", exp_tag, "", "", false);
  dot_perf->Interval(outer_hard_cnt,
      absl::StrCat("record_exp_" + record_tag + "_cnt"),
      "outer_hard", exp_tag, "", "", false);
  dot_perf->Interval(inner_soft_cnt,
      absl::StrCat("record_exp_" + record_tag + "_cnt"),
      "inner_soft", exp_tag, "", "", false);
  dot_perf->Interval(outer_soft_cnt,
      absl::StrCat("record_exp_" + record_tag + "_cnt"),
      "outer_soft", exp_tag, "", "", false);
}

// operator<< 重载定义在 util/common/log_info.h 中
void ContextData::LogInfo(const FrontServerResponse& front_response) {
  std::string world_name_to_trace =
      spdm_ctx.TryGetString("ab_test_world_to_trace", "");
  LOG_EVERY_N(INFO, 50) << "FrontRequestType:"
                        << kuaishou::ad::FrontRequestType_Name(front_server_request_->type())
                        << "|llsid:" << get_llsid() << "|user_id:" << get_user_id()
                        << "|pass_pre_filter:" << get_pass_pre_filter() << "|device_id:" << get_device_id()
                        << "|only_request_fanstop:" << get_only_request_fanstop()
                        << "|total_cost:" << (base::GetTimestamp() - get_start_time_us()) << "us"
                        << "|exp_id:" << spdm_ctx.Raw()->TryGetExperimentId(world_name_to_trace)
                        << "|group_id:" << spdm_ctx.Raw()->TryGetGroupId(world_name_to_trace)
                        << "|invalid_flag:" << GetAdInvalidFlag();
}

bool ContextData::NearbyOnlyRequestFanstop() {
  if (ad_request->ad_user_info().id() <= 0) {
    return true;
  }
  uint64_t pageid = front_server_request_->nearby_request().page_id();
  uint64_t sub_pageid = front_server_request_->nearby_request().sub_page_id();
  const auto inspire_live_subpage_conf = FrontKconfUtil::inspireLiveSubpageConf();
  auto inspire_live_subpage_iter = inspire_live_subpage_conf->find(sub_pageid);
  if (pageid == 10007 || pageid == 11004 ||           // 直播广场
      (pageid == 10002 && sub_pageid == 10002011) ||  // 发现页直播 tab
      (pageid == 11101 && sub_pageid == 11101006) ||  // 极速版任务中心活动激励直播
      (pageid == 10012 && sub_pageid == 10012001)) {  // 主版任务中心活动激励直播
    falcon::Inc(
        ("front_server.no_need_to_request_adserver_request_count_page_" + std::to_string(pageid)).c_str(),
        1);
    return true;
  }
  if ((pageid == 100011291 && sub_pageid == 100011292) ||
      (inspire_live_subpage_iter != inspire_live_subpage_conf->end() &&
       (inspire_live_subpage_iter->second == 100011291 ||
        inspire_live_subpage_iter->second == 100014023))) {  // 主版任务中心激励直播
    if (spdm_ctx.TryGetBoolean("enable_inspire_live_request_adserver", false)) {
      return false;
    }
    return true;
  }
  if ((pageid == 100011502 && sub_pageid == 100011503) ||        // 极速版单列激励直播
      (pageid == 100012060 && sub_pageid == 100012061) ||        // 极速版双列激励直播
      (inspire_live_subpage_iter != inspire_live_subpage_conf->end() &&
       (inspire_live_subpage_iter->second == 100011502 ||
        inspire_live_subpage_iter->second == 100012060))) {
    // 极速版双列实验
    bool enable_xdt_pk = ks::abtest::AbtestInstance::GetBoolean(
            ks::AbtestBiz::NEBULA_AD_DSP, "enable_xdt_pk",
            ad_request->ad_user_info().id(),
            ad_request->ad_user_info().device_id(), true);
    if (!enable_xdt_pk) {
      return true;
    }

    if (spdm_ctx.TryGetBoolean("enable_nebula_inspire_live_request_adserver", false)) {
      return false;
    }
    return true;
  }
  // page size 为 10 的时候， 偶数刷只出粉条，不请求 adServer
  auto refresh_times = front_server_request_->nearby_request().fans_top_request().nearby_refresh_times();
  if (front_server_request_->nearby_request().fans_top_request().page_size() == 10 &&
      FrontKconfUtil::enableSkipAdServerForNewNearby() && refresh_times > 0 && refresh_times % 2 == 0) {
    return true;
  }

  // 如果是主 App 同城设置版内流流量, 那么只有奇数刷出广告, 偶数刷过滤掉
  if (front_server_request_->nearby_request().kuaishou_setup_internal_flow() &&
      refresh_times > 0 && refresh_times % 2 == 0) {
    return true;
  }

  return false;
}

void ContextData::FillPvRecordTime(const kuaishou::ad::AdRequest& ad_req) {
  // 极速版发现页、极速版同城页、底导版精选页均未推全，redis key 只对实验用户生效
  // 极速版发现页动态广告 只对命中实验的用户生成 redis
  std::string appid = ks::ad_base::GetRequestAppId(ad_req);
  const auto ad_request_flow_type = ks::ad_base::GetAdRequestType(ad_req);
  if (ad_request_flow_type == kuaishou::ad::AdEnum::FLOW_NEBULA_NEARBY &&
    "kuaishou_nebula" == appid) {
    falcon::Inc("front_server.dy_pos_filter_by_nearby_type", 1);
    return;
  }

  // knews 只对命中实验的用户生产 redis
  if (IsKwaiMatrixFeedTraffic() && !enable_knews_dynamic_pos) {
    return;
  }

  auto redis_client = ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
      AD_SERVER_PV_RECORD, ad_base::DependDataLevel::STRONG_DEPEND);
  if (redis_client == nullptr) {
    LOG(ERROR) << "redis_client is nullptr";
    return;
  }

  std::string key = ks::ad_base::GetPvRecordKey(ad_req.universe_ad_request_info().app_id(),
                                                ad_req.ad_request_flow_type(), get_user_id(), get_page_id(),
                                                ad_req.reco_request_info().browse_type(), get_sub_page_id());
  if (key.empty()) {
    return;
  }

  std::string value;
  auto* mutable_ad_request = const_cast<kuaishou::ad::AdRequest*>(&ad_req);
  ks::infra::RedisErrorCode code = redis_client->Get(key, &value);

  if (code == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
    if (req_pv_info.ParseFromString(value)) {
      mutable_ad_request->mutable_front_internal_data()->set_last_explore_pv_timestamp(
          req_pv_info.last_explore_pv_timestamp());
      mutable_ad_request->mutable_front_internal_data()->set_last_explore_pv_page_size(
          req_pv_info.last_explore_pv_page_size());
      mutable_ad_request->mutable_front_internal_data()->set_last_explore_pv_last_ad_pos(
          req_pv_info.last_explore_pv_last_ad_pos());
      mutable_ad_request->mutable_front_internal_data()->set_first_screen_ad_shw_timestamp(
          req_pv_info.first_screen_ad_shw_timestamp());
      // 如果上一刷是冷启动第一刷不出广告（精选页冷启 pagesize = 4 的实验），这里需要标识一下
      // cold_request_type
      if (req_pv_info.last_pv_is_cold_start()) {
        if (ad_request->first_load_more()) {
          mutable_ad_request->mutable_front_internal_data()->set_cold_start_request_type(
              kuaishou::ad::FrontInternalData_ColdStartRequestType_kFirstLoadMore);
          dot_perf->Count(1, "last_is_cold_start_and_load_more");
        } else {
          dot_perf->Count(1, "last_is_cold_start_not_load_more");
        }
      }

      // ad_request->mutable_debug_message()->mutable_req_pv_info()->CopyFrom(req_pv_info);
      LOG_EVERY_N(INFO, 100000) << "FillPvRecordTime Get redis success, key=" << key
                                << " last_explore_pv_timestamp=" << req_pv_info.last_explore_pv_timestamp()
                                << " last_explore_pv_page_size=" << req_pv_info.last_explore_pv_page_size()
                                << " last_explore_pv_last_ad_pos="
                                << req_pv_info.last_explore_pv_last_ad_pos()
                                << " first_screen_ad_shw_timestamp="
                                << req_pv_info.first_screen_ad_shw_timestamp();
    }
  }
  if (IsColdStartNoAdReq()) {
    // 如果是精选页冷启不出广告流量，上次 pv 数据清 0
    mutable_ad_request->mutable_front_internal_data()->set_last_explore_pv_page_size(0);
    mutable_ad_request->mutable_front_internal_data()->set_last_explore_pv_last_ad_pos(0);
  }

  // 双列内流 && 第一刷，清空之前的刷次信息
  if (get_is_inner_explore() && get_is_inner_explore_first()) {
    mutable_ad_request->mutable_front_internal_data()->set_last_explore_pv_page_size(0);
    mutable_ad_request->mutable_front_internal_data()->set_last_explore_pv_last_ad_pos(0);
  }
  // 极速版相关视频内流 && 第一刷，清空之前的刷次信息
  if (ks::ad_base::IsInnerNebulaRelationVideoRequest(get_sub_page_id()) &&
      get_is_nebula_relation_video_first()) {
    mutable_ad_request->mutable_front_internal_data()->set_last_explore_pv_page_size(0);
    mutable_ad_request->mutable_front_internal_data()->set_last_explore_pv_last_ad_pos(0);
  }

  // eyeMax 导致广告位被过滤相关的调整，这个依赖 redis 的状态，必须放到 redis 获取之后处理。
  EyeMaxAdjuctPos(ad_req);
}

// eyeMax 导致广告位被过滤相关的调整
void ContextData::EyeMaxAdjuctPos(const kuaishou::ad::AdRequest &ad_req) {
  bool is_eyemax_showed = false;
  int64_t abandoned_creative_id = 0;
  auto* mutalbe_ad_request = const_cast<kuaishou::ad::AdRequest*>(&ad_req);
  GetRequestEyemaxInfo(ad_req, &is_eyemax_showed, &abandoned_creative_id);
  dot_perf->Count(1, "is_eyemax_showed", absl::StrCat(is_eyemax_showed));
  if (is_eyemax_showed && get_is_thanos_request() &&
      spdm_ctx.TryGetBoolean("enable_eyeamx_abandoned_salvaged", false)) {
    // 如果上一刷是 eyeMax, 相当于上一刷的广告位置是 1
    if (abandoned_creative_id > 0) {
      mutalbe_ad_request->mutable_front_internal_data()->set_last_explore_pv_last_ad_pos(1);
      dot_perf->Count(1, "eyemax_showed_and_abandoned");
      LOG_EVERY_N(INFO, 1000) << "enable_eyeamx_abandoned_salvaged , abandoned_creative_id ="
                              << abandoned_creative_id;
    } else {
      mutalbe_ad_request->mutable_front_internal_data()->set_last_explore_pv_last_ad_pos(
          std::max(1u, ad_req.front_internal_data().last_explore_pv_last_ad_pos()));
      // 如果是精选页 page_size = 4 的实验，这里不能再当成 kFirstLoadMore 类型来处理了，需当成普通广告处理
      if (IsFirstLoadMore()) {
        mutalbe_ad_request->mutable_front_internal_data()->set_cold_start_request_type(
            kuaishou::ad::FrontInternalData_ColdStartRequestType_kNormalReqType);
        dot_perf->Count(1, "eyemax_showed_and_first_load_more");
      }
    }
  }
}

void ContextData::DyPosAdd(const kuaishou::ad::AdRequest& request) {
  std::string appid = ks::ad_base::GetRequestAppId(request);
  const auto interactive_form = ks::ad_base::GetInteractiveForm(request);
  auto* mutable_request = const_cast<kuaishou::ad::AdRequest*>(&request);
  if ((request.reco_request_info().browse_type() == 3 &&
        "kuaishou" == appid &&
        interactive_form ==
            kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS) ||
       (request.reco_request_info().browse_type() == 4 &&
        "kuaishou" == appid &&
        interactive_form == kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS) ||
       ("kuaishou_nebula" == appid &&
        interactive_form == kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS)) {
    mutable_request->mutable_front_internal_data()->set_enable_ad_add_pos(true);
  }
  if (ks::ad_base::IsInnerNebulaRelationVideoRequest(get_sub_page_id())) {
    mutable_request->mutable_front_internal_data()->set_enable_ad_add_pos(false);
  }
}

std::ostream& operator<<(std::ostream& os, const std::vector<int32_t>& v) {
  os << "size: " << v.size() << "| ";
  for (int32_t pos : v) {
    os << pos << ",";
  }
  return os;
}

void ContextData::PrepareDynamicAdPosition() {
  auto request_type = ks::ad_base::GetAdRequestType(*ad_request);
  auto refresh_direction = ks::ad_base::GetRefreshDirection(*ad_request);
  if (request_type == kuaishou::ad::AdEnum::FLOW_EXPLORE ||
      request_type == kuaishou::ad::AdEnum::FLOW_NEBULA_NEARBY) {
    FillPvRecordTime(*ad_request);
  }

  ad_request->mutable_debug_message()->set_disable_adserver_dynamic_pos(true);

  DyPosAdd(*ad_request);

  ad_pos_calculor.Clear();
  ad_pos_calculor.Initialize(this, *ad_request);

  for (auto tmp : ad_pos_calculor.normal_pos) {
    ad_request->mutable_front_internal_data()->add_normal_pos(tmp);
  }
  if (ad_request->mutable_front_internal_data()->normal_pos_size() == 0 &&
      ad_request->universe_ad_request_info().imp_info_size()) {
    ad_request->mutable_front_internal_data()->add_normal_pos(
        ad_request->universe_ad_request_info().imp_info(0).position_id());
  }

  auto* debug_message = ad_request->mutable_debug_message();
  for (int32_t pos : ad_pos_calculor.normal_pos) {
    debug_message->add_normal_pos(pos);
  }
  for (int32_t pos : ad_pos_calculor.topic_pos) {
    debug_message->add_topic_pos(pos);
  }
  for (int32_t pos : ad_pos_calculor.detail_pos) {
    debug_message->add_detail_pos(pos);
  }
  debug_message->set_pv_req_times(0);
  debug_message->set_need_predict_user_next_stay_time(false);
  debug_message->set_calculate_gap_target(0.0);
  debug_message->set_dynamic_ad_pos_gap_date("");
  debug_message->set_cur_req_scene_str("");
}

bool ContextData::IsSkipCommonAdmit() const {
  if (UniverseRequest().universe_debug_param().aggregate_page_new_link_switch()) {
    dot_perf->Count(1, "front_server.aggregate_page_new_link");
    return true;
  }
  if (UniverseRequest().universe_task_type() > 0) {
    dot_perf->Count(1, "front_server.aggregate_page_link",
        std::to_string(UniverseRequest().universe_task_type()));
    return true;
  }
  auto appid = ad_request->universe_ad_request_info().app_id();
  if (!get_is_universe_flow() && !engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(appid)) {
    return false;
  }

  auto did = ad_request->ad_user_info().device_id();
  auto config = FrontKconfUtil::skipCommonAdmitConfig();
  auto iter = config->data().app_device_white_list().find(appid);
  if (iter == config->data().app_device_white_list().end()) {
    return false;
  }
  if ((std::count(iter->second.device_id().begin(),
                iter->second.device_id().end(),
                "*") > 0)
    || (std::count(iter->second.device_id().begin(),
                iter->second.device_id().end(),
                did) > 0)) {
    dot_perf->Count(1, "front_server.skip_common_admit", appid);
    return true;
  }
  return false;
}

void ContextData::AddPkFilterInfo(uint64 creative_id, uint64 position_id, uint64 price,
    kuaishou::ad::AdEnum_BidType bid_type,
    kuaishou::ad::FrontFilterInfo::FilterType filter_type) {
  // 不记录出价，设置为 0
  uint64_t tmp_price = 0;
  if (bid_type == kuaishou::ad::AdEnum_BidType::AdEnum_BidType_OCPM_DSP) {
    // 出价类型为 ocpm 时，记录出价
    tmp_price = price;
  }
  mutable_filterInfos()->emplace_back(creative_id, filter_type, tmp_price, position_id);
}

void ContextData::AdPosSelectAbtestInitialize() {
  ueq_manager.AdPosSelectUeqAbtestInitialize(spdm_ctx);
}

bool ContextData::IsPassDowngrade(const FrontServerRequest& front_request, const AdRequest& request) {
  // 压测流量可通过开关关闭
  if (FrontKconfUtil::disableFakeLaneReq() &&
      request.fake_type() == ::kuaishou::ad::UserFakeType::PRESS_FROM_LANE) {
    dot_perf->Count(1, "filter_press_from_lane_req");
    return false;
  }
  return true;
}

bool ContextData::PassKspGroupCheck(const FrontServerRequest& request) {
  // 此处不过滤测试环境的请求
  if (get_for_test() || FrontKconfUtil::enablePassKspGroupCheck()) {
    return true;
  }

  auto ksp_group = ks::ad_base::AdKessClient::Instance().GetKspGroup();
  bool temp_pre_filter_status = get_pass_pre_filter();
  LOG_EVERY_N(INFO, 100000) << "ksp_group: " << ksp_group
          << ", request_name: " << kuaishou::ad::FrontRequestType_Name(request.type());
  ksp_group = ksp_group.empty() ? "default" : ksp_group;
  switch (request.type()) {
    case kuaishou::ad::EXPLORE_REQUEST:
      if (!DoesFlowMatchDefaultKsn()) {
        // default 服务获取到的 ksp_group 是空的
        // default 走发现页双列，thanos 走发现页单列
        temp_pre_filter_status = false;
        DOT_PERF_RECORD_FLOW_NOT_MATCH("explore")
      }
      break;
    case kuaishou::ad::UNIVERSE_REQUEST:
      if (!DoesFlowMatchUniverseKsn()) {
        temp_pre_filter_status = false;
        DOT_PERF_RECORD_FLOW_NOT_MATCH("universe")
      }
      break;
    case kuaishou::ad::SPLASH_REQUEST:
      if (!DoesFlowMatchSplashKsn()) {
        temp_pre_filter_status = false;
        DOT_PERF_RECORD_FLOW_NOT_MATCH("splash")
      }
      break;
    case kuaishou::ad::NEARBY_REQUEST:
      if (!DoesFlowMatchDefaultKsn()) {
        // 同城页请求走 default，也应该是空
        temp_pre_filter_status = false;
        DOT_PERF_RECORD_FLOW_NOT_MATCH("nearby")
      }
      break;
    case kuaishou::ad::FOLLOW_REQUEST:
      if (!DoesFlowMatchFollowKsn()) {
        temp_pre_filter_status = false;
        DOT_PERF_RECORD_FLOW_NOT_MATCH("follow")
      }
      break;
    case kuaishou::ad::KWAI_GALAXY_REQUEST:
      if (!DoesFlowMatchGalaxyKsn() && !DoesFlowMatchContentKsn() && !DoesFlowMatchSearchKsn()) {
        temp_pre_filter_status = false;
        DOT_PERF_RECORD_FLOW_NOT_MATCH("kwai_galaxy")
      }
      break;
    case kuaishou::ad::SEARCH_REQUEST:
      if (!DoesFlowMatchSearchKsn()) {
        temp_pre_filter_status = false;
        DOT_PERF_RECORD_FLOW_NOT_MATCH("search")
      }
      break;
    case kuaishou::ad::MERCHANT_REQUEST:
      if (!DoesFlowMatchMerchantKsn()) {
        temp_pre_filter_status = false;
        DOT_PERF_RECORD_FLOW_NOT_MATCH("merchant")
      }
      break;
    default:
      temp_pre_filter_status = false;
      DOT_PERF_RECORD_FLOW_NOT_MATCH("unkonw")
      break;
  }
  if (FrontKconfUtil::enableKspGroupCheck()->data->GetBoolean(
        kuaishou::ad::FrontRequestType_Name(request.type()), false)) {
    set_pass_pre_filter(temp_pre_filter_status);
  }
  return get_pass_pre_filter();
}

bool ContextData::AdBrowsedInfoFilter() {
  // 如果用户看过 N 个或以上的广告，就不再出广告
  // 只统计最近一天（配置值）的广告数,
  // 这里做了新老字段的小流量对比实验
  int64 now_ms = base::GetTimestamp() / 1000;
  int64 browse_ads_time_range = 86400;
  int64 browse_ad_start_ms = now_ms - browse_ads_time_range * 1000;
  int64 ads_num = 0;
  int64 max_timestamp_ms = 0;
  for (int i = 0; i < ad_request->ad_user_info().ad_browsed_info_size(); ++i) {
    const auto& ad_browsed_info = ad_request->ad_user_info().ad_browsed_info(i);
    const int64 timestamp = ad_browsed_info.timestamp() / 1000;
    if (timestamp >= browse_ad_start_ms && timestamp <= now_ms) {
      // 浏览记录中一次请求可能返回多个广告。
      ads_num += ad_browsed_info.ad_detail_info_size();
    }
    if (timestamp > max_timestamp_ms) {
      max_timestamp_ms = timestamp;
    }
  }

  // TODO(mahang) 广告次数限制逻辑待迁移
  int64 interval_seconds = (now_ms - max_timestamp_ms) / 1000;
  if (interval_seconds < 0) interval_seconds = 0;
  ad_request->mutable_front_internal_data()->set_browse_ad_interval_seconds(interval_seconds);
  ad_request->mutable_front_internal_data()->set_browse_ads_num(ads_num);

  return false;
}

bool ContextData::IsEnableKnewsDynamicPos() const {
  if (!IsKwaiMatrixFeedTraffic()) {
    return false;
  }
  const auto sub_pageid = ad_request->sub_page_id();
  kuaishou::ad::tables::AdPositionStrategy ad_position_strategy;
  int64 pos_id = ad_request->universe_ad_request_info().imp_info(0).position_id();
  bool haichuan_dynamic = false;
  auto* pos_strategy_manager = AdPosManager::GetAdPosManager();
  if (FrontKconfUtil::enableKnewsUseHaiChuan() &&
      pos_strategy_manager->GetAdPositionStrategy(pos_id, &ad_position_strategy)) {
    base::Json strategy_json(base::StringToJson(ad_position_strategy.strategy()));
    int64 value = 0;
    haichuan_dynamic = strategy_json.GetInt("top_refresh", &value) && value != 0;
  }
  bool res = haichuan_dynamic;
  if (sub_pageid == 13003001 &&  // 上下滑
      spdm_ctx.TryGetInteger("slide_first_pos", 0) != 0) {
    res = true;
  } else if (sub_pageid == 100011024 &&  // 图文 feed 流
             spdm_ctx.TryGetInteger("material_feed_first_pos", 0) != 0) {
    res = true;
  } else if (sub_pageid == 13005001 &&  // 视频 feed 流
             spdm_ctx.TryGetInteger("photo_feed_first_pos", 0) != 0) {
    res = true;
  } else if (sub_pageid == 100011034 &&  // 更多推荐（视频）
             spdm_ctx.TryGetInteger("more_rec_photo_first_pos", 0) != 0) {
    res = true;
  } else if (sub_pageid == 100011040 &&  // 小剧场上下滑
             spdm_ctx.TryGetInteger("small_theater_slide_first_pos", 0) != 0) {
    res = true;
  }
  return res;
}

bool ContextData::IsEnableKnewsServerSplash(double lower_bound, double upper_bound) const {
  const int64_t width = UniverseRequest().device_info().screen_size().width();
  const int64_t height = UniverseRequest().device_info().screen_size().height();
  if (width == 0 || height == 0) {
    return false;
  }
  double screen_width_height_ratio = (width*1000/height)/1000.0;
  return screen_width_height_ratio >= lower_bound
          && screen_width_height_ratio <= upper_bound;
}

void ContextData::ParseRankResult(const AdResponse *ad_resp, bool overwrite_tag) {
  if (!ad_resp) {
    return;
  }
  // preivew 请求不会走 rank，可以继续用 ad_rank_pass_through
  auto &rank_2_front_info = *mutable_rank_2_front_info();
  rank_2_front_info.Init(this);
  rank_2_front_info.Parse(*ad_resp);

  const std::string &rank_result_b64 = ad_resp->ad_rank_result_pass_through();
  if (rank_result_b64.empty()) {
    return;
  }

  std::string pass_through_str;
  kuaishou::ad::AdRankPassThrough tmp;
  if (!(base::Base64Decode(rank_result_b64, &pass_through_str)
      && tmp.ParseFromString(pass_through_str))) {
    falcon::Inc("front_server.ad_rank_result_parse_failed", 1);
    return;
  }

  int start_index = ad_rank_pass_through.ad_rank_result_size();
  auto &creative_id_2_idx = *mutable_creative_id_2_idx();
  for (int i = 0; i < tmp.ad_rank_result_size(); ++i) {
    int64_t creative_id = tmp.ad_rank_result(i).creative_id();
    creative_id_2_idx[creative_id] = i + start_index;
    ad_rank_pass_through.add_ad_rank_result()->Swap(tmp.mutable_ad_rank_result(i));
    ad_rank_pass_through.add_rank_info()->Swap(tmp.mutable_rank_info(i));
  }

  if (overwrite_tag) {
    ad_rank_pass_through.mutable_group_tag()->swap(*tmp.mutable_group_tag());
    ad_rank_pass_through.mutable_deep_group_tag()->swap(*tmp.mutable_deep_group_tag());
  }
  falcon::Stat("front_server.rank_result_size", ad_rank_pass_through.ad_rank_result_size());
  falcon::Stat("front_server.rank_info_size", ad_rank_pass_through.rank_info_size());
}

// 在 Set 里的是需要处理的
bool ContextData::InValidFilterSet(std::string plugin_name) {
  auto valid_filter_set = GetAdmitFilterSet();
  if (nullptr== valid_filter_set) {
    return true;
  }
  if (valid_filter_set->find(plugin_name) == valid_filter_set->end()) {
    return false;
  }
  return true;
}

std::shared_ptr<absl::flat_hash_set<std::string>> ContextData::GetAdmitFilterSet() {
  if (pos_manager.GetAdRequestType() ==
            kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEBULA_INSPIRE) {
    return FrontKconfUtil::newNebulaInspireAdEnabledAdmitFilterConfig();
  } else if (pos_manager.GetAdRequestType() ==
            kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_SMALL_GAME) {
    return FrontKconfUtil::newSmallGameInspireAdmitFilterConfig();
  }
  FrontServerScene scene = GetUnifyScene();
  if (FrontKconfUtil::hardFilterSceneSet()->count(static_cast<int32_t>(scene)) < 1) {
    return nullptr;
  }
  if (scene == FrontServerScene::GALAXY && IsKwaiMatrixApp()) {
    return nullptr;
  }
  if (IsInspire()) {
    return FrontKconfUtil::defaultInspireFilterSet();
  }
  return FrontKconfUtil::defaultHardFilterSet();
}

bool ContextData::IsAdmitPreFilter() {
  auto filter = !(get_pass_pre_filter() && get_req_adapter_code() == ReqAdapterCode::kAdapterSuccess);
  if (filter) {
    dot_perf->Count(1, "pass_pre_or_adpos_filter",
                                   absl::StrCat(get_req_adapter_code()));
  }
  return filter;
}
void ContextData::GetLastPvAdInfo(const AdRequest& ad_req,
                                  const bool enable_dynamic_pos,
                                  kuaishou::ad::UserLastPvInfo* last_pv_info) {
  const auto& front_internal_data = ad_req.front_internal_data();
  last_pv_info->set_last_explore_pv_timestamp(front_internal_data.last_explore_pv_timestamp());
  last_pv_info->set_last_explore_pv_page_size(front_internal_data.last_explore_pv_page_size());
  last_pv_info->set_last_explore_pv_last_ad_pos(front_internal_data.last_explore_pv_last_ad_pos());
  last_pv_info->set_first_screen_ad_shw_timestamp(front_internal_data.first_screen_ad_shw_timestamp());
  if (IsColdStartNoAdReq()) {
    last_pv_info->set_last_pv_is_cold_start(true);
  } else {
    last_pv_info->set_last_pv_is_cold_start(false);
  }
  last_pv_info->set_disable_dynamic_pos(!enable_dynamic_pos);
}
bool ContextData::NeedRecordPvAdInfo() {
  if (ks::ad_base::GetAdRequestType(*ad_request) != kuaishou::ad::AdEnum::FLOW_EXPLORE &&
      ks::ad_base::GetAdRequestType(*ad_request) != kuaishou::ad::AdEnum::FLOW_NEBULA_NEARBY) {
    return false;
  }
  if (ad_request->page_size() == 0) {
    return false;
  }
  if (IsTestRequest()) {
    return false;
  }

  // 极速版同城页
  if (ks::ad_base::IsNebulaNearbyRequest(get_sub_page_id())) {
    return false;
  }

  // knews 只对命中实验的用户生产 redis
  if (IsKwaiMatrixFeedTraffic() && !enable_knews_dynamic_pos) {
    return false;
  }
  return true;
}

void ContextData::SetInventoryPosInfo(const kuaishou::ad::AdRequest &ad_req) {
  if (ad_req.universe_ad_request_info().imp_info_size() != 0) {
    auto &request_inventory_pos_id = *mutable_request_inventory_pos_id();
    for (int i = ad_req.universe_ad_request_info().imp_info_size() - 1; i >= 0; --i) {
      const auto& imp = ad_req.universe_ad_request_info().imp_info(i);
      if (imp.ad_num() == 0) {
        continue;
      }
      request_inventory_pos_id.push_back(imp.position_id());
    }
  }
}

// 判断是否为试玩流量
bool ContextData::IsUnionPlayableExpand() {
  if (ad_request == nullptr || ad_request->universe_ad_request_info().imp_info_size() == 0) {
    return false;
  }
  bool is_ads_sdk = ad_request->universe_ad_request_info().sdk_type() == 1;
  bool is_content_sdk = ad_request->universe_ad_request_info().sdk_type() == 2;
  bool valid_sdk_version_336 =
          engine_base::CompareAppVersion(
                  ad_request->universe_ad_request_info().sdk_version(),
                  "3.3.6") >= 0;
  bool valid_sdk_version_3321 =
          engine_base::CompareAppVersion(
                  ad_request->universe_ad_request_info().sdk_version(),
                  "3.3.21") >= 0;
  bool valid_sdk_version_3310 =
          engine_base::CompareAppVersion(
                  ad_request->universe_ad_request_info().sdk_version(),
                  "3.3.10") >= 0;
  bool valid_sdk_version_3313 =
          engine_base::CompareAppVersion(
                  ad_request->universe_ad_request_info().sdk_version(),
                  "3.3.13") >= 0;
  bool valid_sdk_version_3323 =
          engine_base::CompareAppVersion(
                  ad_request->universe_ad_request_info().sdk_version(),
                  "3.3.23") >= 0;
  int32_t ad_style = ad_request->universe_ad_request_info().imp_info(0).ad_style();
  bool is_inspire = ad_style == 2 || ad_style == 12;
  if (valid_sdk_version_336 && is_ads_sdk && is_inspire) {  // 只有激励视频请求出试玩广告
    return true;
  }
  if (valid_sdk_version_3321 && is_ads_sdk && ad_style == 3) {
    return true;
  }
  if (valid_sdk_version_3310 && is_content_sdk && is_inspire) {
    return true;
  }
  if (valid_sdk_version_3313 && is_content_sdk && ad_style == 3) {
    return true;
  }
  if (ad_style == 13 &&
      spdm_ctx.TryGetBoolean("enable_playable_table_screen", false) &&
      FrontKconfUtil::playAbleSdkMinVersionConfig()) {
    const std::string &table_screen_ads_sdk_min_version =
            FrontKconfUtil::playAbleSdkMinVersionConfig()->data().table_screen_ads_sdk();
    const std::string &table_screen_content_sdk_min_version =
            FrontKconfUtil::playAbleSdkMinVersionConfig()->data().table_screen_content_sdk();
    if (is_ads_sdk && !table_screen_ads_sdk_min_version.empty() &&
        engine_base::CompareAppVersion(
                ad_request->universe_ad_request_info().sdk_version(),
                table_screen_ads_sdk_min_version) >= 0) {
      return true;
    }
    if (is_content_sdk && !table_screen_content_sdk_min_version.empty() &&
        engine_base::CompareAppVersion(
                ad_request->universe_ad_request_info().sdk_version(),
                table_screen_content_sdk_min_version) >= 0) {
      return true;
    }
  }
  return false;
}

static const std::unordered_set<kuaishou::log::ad::AdTraceFilterCondition> invalid_filter_set{
    kuaishou::log::ad::AdTraceFilterCondition::ADX_RETRIEVAL_RESULTS,
    kuaishou::log::ad::AdTraceFilterCondition::LUCKY_ONE,
    kuaishou::log::ad::AdTraceFilterCondition::FRONT_LUCKY_ONE};

void ContextData::RecordHardLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition) {
  if (invalid_filter_set.count(condition)) {
    return;
  }
  last_filter_condition_ = condition;
}

void ContextData::RecordSoftLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition) {
  if (!invalid_filter_set.count(condition)) {
    soft_last_filter_condition_ = condition;
  }
}

// !!!! 所有的成员变量都需要重新 Init
void ContextData::Clear() {
  front_server_request_ = nullptr;
  fanstop_request = nullptr;
  ad_request = nullptr;
  iap_ocpx_set = nullptr;
  abtest_app_id = "kuaishou";
  open_front_to_ad_pack = false;
  open_mix_to_ad_pack = false;

  auction_params.reset();
  native_auction_params.reset();
  last_filter_condition_ = kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION;
  soft_last_filter_condition_ = kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION;
  user_preview_info.Clear();
  // 特殊体验配置
  universe.Clear();
  pos_manager.Clear();
  spdm_ctx.Clear();
  ab_user_info.reset();
  ueq_manager.Clear();
  mix_benefit_params.Clear();
  if (fanstop_ctx_ != nullptr) {
    fanstop_ctx_->Clear();
    fanstop_ctx_.reset();
  }

  // 联盟请求合并相关
  if (universe_request_merge_info.need_erase) {
    DspRequestMerger::Instance().EraseKey(universe_request_merge_info.universe_merge_req_key);
  }
  universe_request_merge_info.Clear();
  dot_perf.reset();
  kuaishou::ad::AdRankPassThrough().Swap(&ad_rank_pass_through);
  knews_splash_pos_strategy_info.clear();
  knews_inspire_pos_strategy_info.clear();
  ks::abtest2::AbtestMappingId().Swap(&abtest_mapping_id);
  req_pv_info.Clear();
  ad_pos_calculor.Clear();
  fans_pos_calculor.Clear();
  ad_perf_info.Clear();
  if (FrontKconfUtil::enableFixContextDataClear()) {
    // context data 中，以下成员变量没有 clear
    // 加个 kconf 控制执行 clear，防止直接上线出问题
    ad_klog.Clear();
  }
  medium_valid_err_code =
                ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::SUCCESS;
  inner_reason = kuaishou::log::ad::AdTraceFilterCondition::NOT_IN_ROARING_INDEX;
  rta_ad_data.Clear();
  boost_bid_control_tail = nullptr;
  feed_distribution_conf = nullptr;
  photo_negative_tags = nullptr;
  small_game_ad_force_direct_call_white_list = nullptr;
  cartoon_product_set = nullptr;
  small_game_ad_force_direct_call_black_list = nullptr;
  degrade_level = DegradeLevel::kLevelDefault;
  resource_white_box_info.Clear();
  minigame_app_id.clear();
}

bool ContextData::IsColdStartNoAdReq() const {
  if (ad_request && ad_request->has_front_internal_data() &&
      ad_request->front_internal_data().has_cold_start_request_type()) {
    return ad_request->front_internal_data().cold_start_request_type() ==
            kuaishou::ad::FrontInternalData_ColdStartRequestType_kColdStartNoAd;
  }
  return false;
}
bool ContextData::IsMatrixAppInspireTraffic() const {
  return IsKwaiMatrixApp() &&
      pos_manager.GetAdRequestType() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEBULA_INSPIRE;
}

bool ContextData::IsFirstLoadMore()  const {
  if (ad_request && ad_request->has_front_internal_data() &&
      ad_request->front_internal_data().has_cold_start_request_type()) {
    return ad_request->front_internal_data().cold_start_request_type() ==
            kuaishou::ad::FrontInternalData_ColdStartRequestType_kFirstLoadMore;
  }
  return false;
}

UeqManager& ContextData::GetUeqManager() {
  return *mutable_ueq_manager();
}

// 白名单用户判断
bool ContextData::IsWhiteListUser() const {
  // 判断是否是白名单用户
  return get_ac_fetcher_type() != kuaishou::ad::AdTargetResponse::UNKONWN_FETCHER_TYPE;
}

bool ContextData::IsKnewsBrandPrefetch() const {
  return IsKnewsSplashTraffic() &&
          !ad_request->universe_ad_request_info().splash_req_info().is_realtime();
}

bool ContextData::IsKwaiMatrixFeedTraffic() const {
  const auto& appid = ks::ad_base::GetRequestAppId(*ad_request);
  return engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(appid) &&
      ks::ad_base::GetAdRequestType(*ad_request) == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_EXPLORE;
}

bool ContextData::IsSmallGameTraffic() const {
  return pos_manager.GetAdRequestType() ==
              kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_SMALL_GAME;
}
bool ContextData::IsNebulaTaskEntranceTraffic() const {
  return pos_manager.IsNebulaTaskEntranceTraffic();
}
std::string ContextData::From() const {
  if (nullptr == front_server_request_) {
    return "";
  }
  return front_server_request_->from();
}
bool ContextData::IsKuaishouApp(const kuaishou::ad::universe::AthenaAdRequest& athena_request) const {
  const std::string& appid = athena_request.app_info().app_id();
  return appid == "kuaishou" || appid == "kuaishou_nebula";
}

bool ContextData::IsKwaiMatrixApp(const kuaishou::ad::universe::AthenaAdRequest& athena_request) const {
  const std::string& appid = athena_request.app_info().app_id();
  return engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(appid);
}

bool ContextData::IsKuaishouApp() const {
  return get_app_id() == "kuaishou" || get_app_id() == "kuaishou_nebula";
}

bool ContextData::IsKwaiMatrixApp() const {
  return engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(get_app_id()) ||
          (ad_request && engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(
                            ks::ad_base::GetRequestAppId(*ad_request))) ||
          (front_server_request_ &&
          engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(
              front_server_request_->universe_request().app_info().app_id()));  // NOLINT
}


bool ContextData::IsNebulaInspireTraffic() const {
  return pos_manager.GetAdRequestType() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEBULA_INSPIRE;
}

bool ContextData::IsKnewsSplashTraffic() const {
  if (!ad_request) return false;
  return ks::ad_base::GetAdRequestType(*ad_request) ==
                      kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_KNEWS_SPLASH;
}
bool ContextData::IsSplashTraffic() const {
  if (!ad_request) return false;
  return pos_manager.GetAdRequestType() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_SPLASH ||
      pos_manager.GetAdRequestType() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEBULA_SPLASH;
}
bool ContextData::IsSplashRealtime() const {
  return (IsSplashTraffic() && ad_request->universe_ad_request_info().splash_req_info().is_realtime());
}
bool ContextData::IsSplashPrefetch() const {
  return (IsSplashTraffic() && !ad_request->universe_ad_request_info().splash_req_info().is_realtime());
}
// 公域单列混排流量
bool ContextData::IsThanosMixTraffic() const {
  // 主站精选（10011001）或极速发现（11001001）
  return pos_manager.GetSubPageId() == 10011001 || pos_manager.GetSubPageId() == 11001001;
}

int ContextData::GetFanstopInvalidFlag() const {
  if (!ad_request) return 0;
  return ad_request->mutable_front_internal_data()->fanstop_invalid_flag();
}

int ContextData::GetAdInvalidFlag() const {
  if (!ad_request) return 0;
  return ad_request->mutable_front_internal_data()->ad_invalid_flag();
}

void ContextData::SetAdInvalidFlag(int val) {
  if (!ad_request) return;
  ad_request->mutable_front_internal_data()->set_ad_invalid_flag(val);
}

void ContextData::SetPassPreFilter(bool val) {
  if (!ad_request) return;
  ad_request->mutable_front_internal_data()->set_pass_pre_filter(val);
}

bool ContextData::GetPassPreFilter() const {
  if (!ad_request) return false;
  return ad_request->mutable_front_internal_data()->pass_pre_filter();
}

void ContextData::SetRankMigrationSwitches() {
  int enable_rank_migration_stage = 0;
  int search_del_ad_server_stage = 0;
  int rank_migration_ad_server_timeout_adjust = SPDM_rankMigrationAdServerTimeoutAdjust();
  int rank_migration_ad_rank_timeout_adjust = SPDM_rankMigrationAdRankTimeoutAdjust();
  if (DoesFlowMatchDefaultKsn()) {
    if (int enable_rank_migration_stage_kconf = SPDM_enableRankMigrationStageDefault();
        enable_rank_migration_stage_kconf != 0) {
      enable_rank_migration_stage = std::max(0, enable_rank_migration_stage_kconf);
    } else {
      enable_rank_migration_stage = SPDM_enable_rank_migration_stage_default(spdm_ctx);
    }
    if (rank_migration_ad_server_timeout_adjust == 0) {
      rank_migration_ad_server_timeout_adjust =
        SPDM_rank_migration_ad_server_timeout_adjust_default(spdm_ctx);
    }
    if (rank_migration_ad_rank_timeout_adjust == 0) {
      rank_migration_ad_rank_timeout_adjust = SPDM_rank_migration_ad_rank_timeout_adjust_default(spdm_ctx);
    }
  } else if (DoesFlowMatchSearchKsn()) {
    if (int enable_rank_migration_stage_kconf = SPDM_enableRankMigrationStageSearch();
        enable_rank_migration_stage_kconf != 0) {
      enable_rank_migration_stage = std::max(0, enable_rank_migration_stage_kconf);
    } else {
      enable_rank_migration_stage = SPDM_enable_rank_migration_stage_search(spdm_ctx);
    }
    if (rank_migration_ad_server_timeout_adjust == 0) {
      rank_migration_ad_server_timeout_adjust =
        SPDM_rank_migration_ad_server_timeout_adjust_search(spdm_ctx);
    }
    if (rank_migration_ad_rank_timeout_adjust == 0) {
      rank_migration_ad_rank_timeout_adjust = SPDM_rank_migration_ad_rank_timeout_adjust_search(spdm_ctx);
    }
    if (enable_rank_migration_stage >= REQUEST_RANK &&
      SPDM_search_del_ad_server_stage(spdm_ctx) == 2) {
      search_del_ad_server_stage = 2;
    }
  } else if (DoesFlowMatchSplashKsn()) {
    if (int enable_rank_migration_stage_kconf = SPDM_enableRankMigrationStageSplash();
        enable_rank_migration_stage_kconf != 0) {
      enable_rank_migration_stage = std::max(0, enable_rank_migration_stage_kconf);
    } else {
      enable_rank_migration_stage = SPDM_enable_rank_migration_stage_splash(spdm_ctx);
    }
    if (rank_migration_ad_server_timeout_adjust == 0) {
      rank_migration_ad_server_timeout_adjust =
        SPDM_rank_migration_ad_server_timeout_adjust_splash(spdm_ctx);
    }
    if (rank_migration_ad_rank_timeout_adjust == 0) {
      rank_migration_ad_rank_timeout_adjust = SPDM_rank_migration_ad_rank_timeout_adjust_splash(spdm_ctx);
    }
  }
  common_w_->SetIntCommonAttr(
    common_attr::enable_rank_migration_stage,
    enable_rank_migration_stage);
  common_w_->SetIntCommonAttr(
    common_attr::search_del_ad_server_stage,
    search_del_ad_server_stage);
  std::string rank_migration_tag = enable_rank_migration_stage >= REQUEST_RANK ? "exp" : "base";
  set_rank_migration_tag(rank_migration_tag);
  dot_perf->Count(1, "rank_mig.pv_life", rank_migration_tag, "front_entrance");
  if (enable_rank_migration_stage > 0) {
    common_w_->SetIntCommonAttr(
      common_attr::ad_server_to_rank_item_attr_flag, 2);
  }
  if (DoesFlowMatchDefaultKsn()) {
    rank_migration_ad_server_timeout_adjust =
      engine_base::AdKconfUtil::extraTimeoutConfig()->data().GetAdServerExtraTimeoutForRankMigration(
        *get_kconf_session_context_dsp(),
        front_server_request_->flow_type());
  }
  common_w_->SetIntCommonAttr(
    common_attr::rank_migration_ad_server_timeout_adjust, rank_migration_ad_server_timeout_adjust);
  common_w_->SetIntCommonAttr(
    common_attr::rank_migration_ad_rank_timeout_adjust, rank_migration_ad_rank_timeout_adjust);
  SetBoolCommonAttr(common_attr::enable_split_ad_server_attr, SPDM_enableSplitAdServerAttr());
}

void ContextData::RecordTimeStart(const std::string& key) {
  (*mutable_time_record_map())[key] = base::GetTimestamp();
}

void ContextData::RecordTimeEnd(const std::string& key) {
  int64_t cost_ms = -1;
  auto time_record_map = get_time_record_map();
  if (const auto& iter = time_record_map.find(key); iter != time_record_map.end()) {
    cost_ms = (base::GetTimestamp() - iter->second) / 1000;
  }
  common_w_->SetIntCommonAttr(key, cost_ms, false, false);
}

bool ContextData::IsOneModelInvalidFlow() {
  if (!FrontKconfUtil::oneModelNearline_enableFlowFilter()) {
    return false;
  }
  const auto group = ks::infra::KEnv::GetKWSInfo()->GetServiceGroup();
  const bool is_nearline_flow = get_is_one_model_nearline_flow();
  dot_perf->Count(1, "one_model_nearline_flow", is_nearline_flow ? "nearline" : "online");
  if (KS_UNLIKELY(group == "pre-gray5" && !is_nearline_flow)) {
    return true;
  }
  if (KS_UNLIKELY(group != "pre-gray5" && is_nearline_flow)) {
    return true;
  }
  return false;
}

#include "context_data-context_data.cc.inl"  // NOLINT
}  // namespace front_server
}  // namespace ks
