// auto gen public member 6 for ContextData

inline AdPosCalculor* mutable_ad_pos_calculor() {
  return Attr(CommonIdx::ad_pos_calculor).GetMutablePtrValue<AdPosCalculor>();
}
inline const AdPosCalculor& get_ad_pos_calculor() const {
  return *Attr(CommonIdx::ad_pos_calculor).GetPtrValue<AdPosCalculor>();
}
// auto gen private member 361 for ContextData

inline absl::flat_hash_map<int64_t, int64_t >* mutable_ad_strateg_tag_map() {
  return Attr(CommonIdx::ad_strateg_tag_map).GetMutablePtrValue<absl::flat_hash_map<int64_t, int64_t >>();
}
inline const absl::flat_hash_map<int64_t, int64_t >& get_ad_strateg_tag_map() const {
  return *Attr(CommonIdx::ad_strateg_tag_map).GetPtrValue<absl::flat_hash_map<int64_t, int64_t >>();
}

inline absl::flat_hash_map<int64_t, std::string >* mutable_multi_retr_cmd() {
  return Attr(CommonIdx::multi_retr_cmd).GetMutablePtrValue<absl::flat_hash_map<int64_t, std::string >>();
}
inline const absl::flat_hash_map<int64_t, std::string >& get_multi_retr_cmd() const {
  return *Attr(CommonIdx::multi_retr_cmd).GetPtrValue<absl::flat_hash_map<int64_t, std::string >>();
}

inline absl::flat_hash_set<int64_t >* mutable_cart_items() {
  return Attr(CommonIdx::cart_items).GetMutablePtrValue<absl::flat_hash_set<int64_t >>();
}
inline const absl::flat_hash_set<int64_t >& get_cart_items() const {
  return *Attr(CommonIdx::cart_items).GetPtrValue<absl::flat_hash_set<int64_t >>();
}

inline absl::flat_hash_set<int64_t >* mutable_live_rerank_cache_list() {
  return Attr(CommonIdx::live_rerank_cache_list).GetMutablePtrValue<absl::flat_hash_set<int64_t >>();
}
inline const absl::flat_hash_set<int64_t >& get_live_rerank_cache_list() const {
  return *Attr(CommonIdx::live_rerank_cache_list).GetPtrValue<absl::flat_hash_set<int64_t >>();
}

inline absl::flat_hash_set<int64_t >* mutable_purchase_items() {
  return Attr(CommonIdx::purchase_items).GetMutablePtrValue<absl::flat_hash_set<int64_t >>();
}
inline const absl::flat_hash_set<int64_t >& get_purchase_items() const {
  return *Attr(CommonIdx::purchase_items).GetPtrValue<absl::flat_hash_set<int64_t >>();
}

inline absl::Time::Breakdown* mutable_bd() {
  return Attr(CommonIdx::bd).GetMutablePtrValue<absl::Time::Breakdown>();
}
inline const absl::Time::Breakdown& get_bd() const {
  return *Attr(CommonIdx::bd).GetPtrValue<absl::Time::Breakdown>();
}

inline AdCreativePreview* mutable_user_preview_info() {
  return Attr(CommonIdx::user_preview_info).GetMutablePtrValue<AdCreativePreview>();
}
inline const AdCreativePreview& get_user_preview_info() const {
  return *Attr(CommonIdx::user_preview_info).GetPtrValue<AdCreativePreview>();
}

inline AdFlowDegradedConfig* mutable_ad_flow_degraded_config() {
  return Attr(CommonIdx::ad_flow_degraded_config).GetMutablePtrValue<AdFlowDegradedConfig>();
}
inline const AdFlowDegradedConfig& get_ad_flow_degraded_config() const {
  return *Attr(CommonIdx::ad_flow_degraded_config).GetPtrValue<AdFlowDegradedConfig>();
}

inline AdFrontStageInfoLog* mutable_ad_select_stage_infos() {
  return Attr(CommonIdx::ad_select_stage_infos).GetMutablePtrValue<AdFrontStageInfoLog>();
}
inline const AdFrontStageInfoLog& get_ad_select_stage_infos() const {
  return *Attr(CommonIdx::ad_select_stage_infos).GetPtrValue<AdFrontStageInfoLog>();
}

inline AdList* mutable_ad_list() {
  return Attr(CommonIdx::ad_list).GetMutablePtrValue<AdList>();
}
inline const AdList& get_ad_list() const {
  return *Attr(CommonIdx::ad_list).GetPtrValue<AdList>();
}

inline AdList* mutable_brand_ad_list() {
  return Attr(CommonIdx::brand_ad_list).GetMutablePtrValue<AdList>();
}
inline const AdList& get_brand_ad_list() const {
  return *Attr(CommonIdx::brand_ad_list).GetPtrValue<AdList>();
}

inline AdList* mutable_native_ad_list() {
  return Attr(CommonIdx::native_ad_list).GetMutablePtrValue<AdList>();
}
inline const AdList& get_native_ad_list() const {
  return *Attr(CommonIdx::native_ad_list).GetPtrValue<AdList>();
}

inline AdPerfInfoLog* mutable_ad_perf_info() {
  return Attr(CommonIdx::ad_perf_info).GetMutablePtrValue<AdPerfInfoLog>();
}
inline const AdPerfInfoLog& get_ad_perf_info() const {
  return *Attr(CommonIdx::ad_perf_info).GetPtrValue<AdPerfInfoLog>();
}

// ad_request just declare funcs, impl in cc.inl
void set_ad_request(AdRequest * v);
AdRequest * mutable_ad_request() const ;
const AdRequest * get_ad_request() const;

// fanstop_ad_request just declare funcs, impl in cc.inl
void set_fanstop_ad_request(AdRequest * v);
AdRequest * mutable_fanstop_ad_request() const ;
const AdRequest * get_fanstop_ad_request() const;

// ad_response just declare funcs, impl in cc.inl
AdResponse * mutable_ad_response() const ;
const AdResponse * get_ad_response() const;

// amd_response just declare funcs, impl in cc.inl
AdResponse * mutable_amd_response() const ;
const AdResponse * get_amd_response() const;

// brand_response just declare funcs, impl in cc.inl
AdResponse * mutable_brand_response() const ;
const AdResponse * get_brand_response() const;

// fanstop_ad_response just declare funcs, impl in cc.inl
AdResponse * mutable_fanstop_ad_response() const ;
const AdResponse * get_fanstop_ad_response() const;

// splash_effect_ad_response just declare funcs, impl in cc.inl
AdResponse * mutable_splash_effect_ad_response() const ;
const AdResponse * get_splash_effect_ad_response() const;

// traceutil_detection_json just declare funcs, impl in cc.inl
const base::Json * get_traceutil_detection_json() const;

inline void set_already_has_card_style(bool v) {
  Attr(CommonIdx::already_has_card_style).SetIntValue(0, v, false, false);
}
inline bool get_already_has_card_style() const {
  return Attr(CommonIdx::already_has_card_style).GetIntValue().value_or(false);
}

inline void set_byte_size_dot(bool v) {
  Attr(CommonIdx::byte_size_dot).SetIntValue(0, v, false, false);
}
inline bool get_byte_size_dot() const {
  return Attr(CommonIdx::byte_size_dot).GetIntValue().value_or(false);
}

inline void set_choosed_mid_page(bool v) {
  Attr(CommonIdx::choosed_mid_page).SetIntValue(0, v, false, false);
}
inline bool get_choosed_mid_page() const {
  return Attr(CommonIdx::choosed_mid_page).GetIntValue().value_or(false);
}

inline void set_client_ai_is_high_value_user(bool v) {
  Attr(CommonIdx::client_ai_is_high_value_user).SetIntValue(0, v, false, false);
}
inline bool get_client_ai_is_high_value_user() const {
  return Attr(CommonIdx::client_ai_is_high_value_user).GetIntValue().value_or(false);
}

inline void set_context_init_error(bool v) {
  Attr(CommonIdx::context_init_error).SetIntValue(0, v, false, false);
}
inline bool get_context_init_error() const {
  return Attr(CommonIdx::context_init_error).GetIntValue().value_or(false);
}

inline void set_enable_hard_soft_union(bool v) {
  Attr(CommonIdx::enable_hard_soft_union).SetIntValue(0, v, false, false);
}
inline bool get_enable_hard_soft_union() const {
  return Attr(CommonIdx::enable_hard_soft_union).GetIntValue().value_or(false);
}

inline void set_enable_ab_mapping_id(bool v) {
  Attr(CommonIdx::enable_ab_mapping_id).SetIntValue(0, v, false, false);
}
inline bool get_enable_ab_mapping_id() const {
  return Attr(CommonIdx::enable_ab_mapping_id).GetIntValue().value_or(false);
}

inline void set_enable_ad_material_derived_photo(bool v) {
  Attr(CommonIdx::enable_ad_material_derived_photo).SetIntValue(0, v, false, false);
}
inline bool get_enable_ad_material_derived_photo() const {
  return Attr(CommonIdx::enable_ad_material_derived_photo).GetIntValue().value_or(false);
}

inline void set_enable_ad_material_nieuwland_derived_photo(bool v) {
  Attr(CommonIdx::enable_ad_material_nieuwland_derived_photo).SetIntValue(0, v, false, false);
}
inline bool get_enable_ad_material_nieuwland_derived_photo() const {
  return Attr(CommonIdx::enable_ad_material_nieuwland_derived_photo).GetIntValue().value_or(false);
}

inline void set_enable_buyer_home_page_impression_charge(bool v) {
  Attr(CommonIdx::enable_buyer_home_page_impression_charge).SetIntValue(0, v, false, false);
}
inline bool get_enable_buyer_home_page_impression_charge() const {
  return Attr(CommonIdx::enable_buyer_home_page_impression_charge).GetIntValue().value_or(false);
}

inline void set_enable_buyer_home_page_photo_impression_charge(bool v) {
  Attr(CommonIdx::enable_buyer_home_page_photo_impression_charge).SetIntValue(0, v, false, false);
}
inline bool get_enable_buyer_home_page_photo_impression_charge() const {
  return Attr(CommonIdx::enable_buyer_home_page_photo_impression_charge).GetIntValue().value_or(false);
}

inline void set_enable_cache_for_leveled_traffic(bool v) {
  Attr(CommonIdx::enable_cache_for_leveled_traffic).SetIntValue(0, v, false, false);
}
inline bool get_enable_cache_for_leveled_traffic() const {
  return Attr(CommonIdx::enable_cache_for_leveled_traffic).GetIntValue().value_or(0);
}

inline void set_enable_combo_search_req_ad_pack(bool v) {
  Attr(CommonIdx::enable_combo_search_req_ad_pack).SetIntValue(0, v, false, false);
}
inline bool get_enable_combo_search_req_ad_pack() const {
  return Attr(CommonIdx::enable_combo_search_req_ad_pack).GetIntValue().value_or(false);
}

inline void set_enable_detail_cpm_kafka(bool v) {
  Attr(CommonIdx::enable_detail_cpm_kafka).SetIntValue(0, v, false, false);
}
inline bool get_enable_detail_cpm_kafka() const {
  return Attr(CommonIdx::enable_detail_cpm_kafka).GetIntValue().value_or(false);
}

inline void set_enable_use_full_computing_power(bool v) {
  Attr(CommonIdx::enable_use_full_computing_power).SetIntValue(0, v, false, false);
}
inline bool get_enable_use_full_computing_power() const {
  return Attr(CommonIdx::enable_use_full_computing_power).GetIntValue().value_or(false);
}

inline void set_enable_direct_search_skip_budget_control_universe(bool v) {
  Attr(CommonIdx::enable_direct_search_skip_budget_control_universe).SetIntValue(0, v, false, false);
}
inline bool get_enable_direct_search_skip_budget_control_universe() const {
  return Attr(CommonIdx::enable_direct_search_skip_budget_control_universe).GetIntValue().value_or(false);
}

inline void set_enable_fill_cache_origin_llsid(bool v) {
  Attr(CommonIdx::enable_fill_cache_origin_llsid).SetIntValue(0, v, false, false);
}
inline bool get_enable_fill_cache_origin_llsid() const {
  return Attr(CommonIdx::enable_fill_cache_origin_llsid).GetIntValue().value_or(false);
}

inline void set_enable_guess_like_reset_product_id(bool v) {
  Attr(CommonIdx::enable_guess_like_reset_product_id).SetIntValue(0, v, false, false);
}
inline bool get_enable_guess_like_reset_product_id() const {
  return Attr(CommonIdx::enable_guess_like_reset_product_id).GetIntValue().value_or(false);
}

inline void set_enable_guess_you_like_charge_action_type(bool v) {
  Attr(CommonIdx::enable_guess_you_like_charge_action_type).SetIntValue(0, v, false, false);
}
inline bool get_enable_guess_you_like_charge_action_type() const {
  return Attr(CommonIdx::enable_guess_you_like_charge_action_type).GetIntValue().value_or(false);
}

inline void set_enable_inspire_style_form(bool v) {
  Attr(CommonIdx::enable_inspire_style_form).SetIntValue(0, v, false, false);
}
inline bool get_enable_inspire_style_form() const {
  return Attr(CommonIdx::enable_inspire_style_form).GetIntValue().value_or(false);
}

inline void set_enable_knews_dynamic_pos(bool v) {
  Attr(CommonIdx::enable_knews_dynamic_pos).SetIntValue(0, v, false, false);
}
inline bool get_enable_knews_dynamic_pos() const {
  return Attr(CommonIdx::enable_knews_dynamic_pos).GetIntValue().value_or(false);
}

inline void set_enable_kxy_subsidy_global_nc(bool v) {
  Attr(CommonIdx::enable_kxy_subsidy_global_nc).SetIntValue(0, v, false, false);
}
inline bool get_enable_kxy_subsidy_global_nc() const {
  return Attr(CommonIdx::enable_kxy_subsidy_global_nc).GetIntValue().value_or(false);
}

inline void set_enable_mall_impression_charge(bool v) {
  Attr(CommonIdx::enable_mall_impression_charge).SetIntValue(0, v, false, false);
}
inline bool get_enable_mall_impression_charge() const {
  return Attr(CommonIdx::enable_mall_impression_charge).GetIntValue().value_or(false);
}

inline void set_enable_mall_live_impression_charge(bool v) {
  Attr(CommonIdx::enable_mall_live_impression_charge).SetIntValue(0, v, false, false);
}
inline bool get_enable_mall_live_impression_charge() const {
  return Attr(CommonIdx::enable_mall_live_impression_charge).GetIntValue().value_or(false);
}

inline void set_enable_mix_info_rebuild(bool v) {
  Attr(CommonIdx::enable_mix_info_rebuild).SetIntValue(0, v, false, false);
}
inline bool get_enable_mix_info_rebuild() const {
  return Attr(CommonIdx::enable_mix_info_rebuild).GetIntValue().value_or(false);
}

inline void set_enable_mix_info_refactor(bool v) {
  Attr(CommonIdx::enable_mix_info_refactor).SetIntValue(0, v, false, false);
}
inline bool get_enable_mix_info_refactor() const {
  return Attr(CommonIdx::enable_mix_info_refactor).GetIntValue().value_or(false);
}

inline void set_enable_opt_white_box(bool v) {
  Attr(CommonIdx::enable_opt_white_box).SetIntValue(0, v, false, false);
}
inline bool get_enable_opt_white_box() const {
  return Attr(CommonIdx::enable_opt_white_box).GetIntValue().value_or(false);
}

inline void set_enable_pack_stable_category_thrid_id(bool v) {
  Attr(CommonIdx::enable_pack_stable_category_thrid_id).SetIntValue(0, v, false, false);
}
inline bool get_enable_pack_stable_category_thrid_id() const {
  return Attr(CommonIdx::enable_pack_stable_category_thrid_id).GetIntValue().value_or(false);
}

inline void set_enable_preview_use_fanstop(bool v) {
  Attr(CommonIdx::enable_preview_use_fanstop).SetIntValue(0, v, false, false);
}
inline bool get_enable_preview_use_fanstop() const {
  return Attr(CommonIdx::enable_preview_use_fanstop).GetIntValue().value_or(false);
}

inline void set_enable_print_all_factor_info(bool v) {
  Attr(CommonIdx::enable_print_all_factor_info).SetIntValue(0, v, false, false);
}
inline bool get_enable_print_all_factor_info() const {
  return Attr(CommonIdx::enable_print_all_factor_info).GetIntValue().value_or(false);
}

inline void set_enable_print_factor_info(bool v) {
  Attr(CommonIdx::enable_print_factor_info).SetIntValue(0, v, false, false);
}
inline bool get_enable_print_factor_info() const {
  return Attr(CommonIdx::enable_print_factor_info).GetIntValue().value_or(false);
}

inline void set_enable_profile_skin_flow(bool v) {
  Attr(CommonIdx::enable_profile_skin_flow).SetIntValue(0, v, false, false);
}
inline bool get_enable_profile_skin_flow() const {
  return Attr(CommonIdx::enable_profile_skin_flow).GetIntValue().value_or(false);
}

inline void set_enable_quick_search_skip_budget_control_universe(bool v) {
  Attr(CommonIdx::enable_quick_search_skip_budget_control_universe).SetIntValue(0, v, false, false);
}
inline bool get_enable_quick_search_skip_budget_control_universe() const {
  return Attr(CommonIdx::enable_quick_search_skip_budget_control_universe).GetIntValue().value_or(false);
}

inline void set_enable_rank_pass_through_v2(bool v) {
  Attr(CommonIdx::enable_rank_pass_through_v2).SetIntValue(0, v, false, false);
}
inline bool get_enable_rank_pass_through_v2() const {
  return Attr(CommonIdx::enable_rank_pass_through_v2).GetIntValue().value_or(false);
}

inline void set_enable_search_brand_one_ad_list(bool v) {
  Attr(CommonIdx::enable_search_brand_one_ad_list).SetIntValue(0, v, false, false);
}
inline bool get_enable_search_brand_one_ad_list() const {
  return Attr(CommonIdx::enable_search_brand_one_ad_list).GetIntValue().value_or(false);
}

inline void set_enable_set_adjust_price_record(bool v) {
  Attr(CommonIdx::enable_set_adjust_price_record).SetIntValue(0, v, false, false);
}
inline bool get_enable_set_adjust_price_record() const {
  return Attr(CommonIdx::enable_set_adjust_price_record).GetIntValue().value_or(false);
}

inline void set_enable_set_schema_url_refactor_diff_test(bool v) {
  Attr(CommonIdx::enable_set_schema_url_refactor_diff_test).SetIntValue(0, v, false, false);
}
inline bool get_enable_set_schema_url_refactor_diff_test() const {
  return Attr(CommonIdx::enable_set_schema_url_refactor_diff_test).GetIntValue().value_or(false);
}

inline void set_enable_set_schema_url_refactor(bool v) {
  Attr(CommonIdx::enable_set_schema_url_refactor).SetIntValue(0, v, false, false);
}
inline bool get_enable_set_schema_url_refactor() const {
  return Attr(CommonIdx::enable_set_schema_url_refactor).GetIntValue().value_or(false);
}

inline void set_enable_set_url_refactor_diff_test(bool v) {
  Attr(CommonIdx::enable_set_url_refactor_diff_test).SetIntValue(0, v, false, false);
}
inline bool get_enable_set_url_refactor_diff_test() const {
  return Attr(CommonIdx::enable_set_url_refactor_diff_test).GetIntValue().value_or(false);
}

inline void set_enable_set_url_refactor(bool v) {
  Attr(CommonIdx::enable_set_url_refactor).SetIntValue(0, v, false, false);
}
inline bool get_enable_set_url_refactor() const {
  return Attr(CommonIdx::enable_set_url_refactor).GetIntValue().value_or(false);
}

inline void set_enable_side_window_author_uplift_score(bool v) {
  Attr(CommonIdx::enable_side_window_author_uplift_score).SetIntValue(0, v, false, false);
}
inline bool get_enable_side_window_author_uplift_score() const {
  return Attr(CommonIdx::enable_side_window_author_uplift_score).GetIntValue().value_or(false);
}

inline void set_enable_skip_simple_no_dsp(bool v) {
  Attr(CommonIdx::enable_skip_simple_no_dsp).SetIntValue(0, v, false, false);
}
inline bool get_enable_skip_simple_no_dsp() const {
  return Attr(CommonIdx::enable_skip_simple_no_dsp).GetIntValue().value_or(false);
}

inline void set_enable_splash_rtb_realtime_recall(bool v) {
  Attr(CommonIdx::enable_splash_rtb_realtime_recall).SetIntValue(0, v, false, false);
}
inline bool get_enable_splash_rtb_realtime_recall() const {
  return Attr(CommonIdx::enable_splash_rtb_realtime_recall).GetIntValue().value_or(false);
}

inline void set_enable_tiny_search_white_list(bool v) {
  Attr(CommonIdx::enable_tiny_search_white_list).SetIntValue(0, v, false, false);
}
inline bool get_enable_tiny_search_white_list() const {
  return Attr(CommonIdx::enable_tiny_search_white_list).GetIntValue().value_or(false);
}

inline void set_enable_transport_risk_info(bool v) {
  Attr(CommonIdx::enable_transport_risk_info).SetIntValue(0, v, false, false);
}
inline bool get_enable_transport_risk_info() const {
  return Attr(CommonIdx::enable_transport_risk_info).GetIntValue().value_or(false);
}

inline void set_enable_union_ltr_sample(bool v) {
  Attr(CommonIdx::enable_union_ltr_sample).SetIntValue(0, v, false, false);
}
inline bool get_enable_union_ltr_sample() const {
  return Attr(CommonIdx::enable_union_ltr_sample).GetIntValue().value_or(false);
}

inline void set_enable_wanhe_charge_action_type_subpage_id(bool v) {
  Attr(CommonIdx::enable_wanhe_charge_action_type_subpage_id).SetIntValue(0, v, false, false);
}
inline bool get_enable_wanhe_charge_action_type_subpage_id() const {
  return Attr(CommonIdx::enable_wanhe_charge_action_type_subpage_id).GetIntValue().value_or(false);
}

inline void set_enable_wanhe_charge_action_type_with_fanstop(bool v) {
  Attr(CommonIdx::enable_wanhe_charge_action_type_with_fanstop).SetIntValue(0, v, false, false);
}
inline bool get_enable_wanhe_charge_action_type_with_fanstop() const {
  return Attr(CommonIdx::enable_wanhe_charge_action_type_with_fanstop).GetIntValue().value_or(false);
}

inline void set_enable_wechat_company_add_fans_direct_jump(bool v) {
  Attr(CommonIdx::enable_wechat_company_add_fans_direct_jump).SetIntValue(0, v, false, false);
}
inline bool get_enable_wechat_company_add_fans_direct_jump() const {
  return Attr(CommonIdx::enable_wechat_company_add_fans_direct_jump).GetIntValue().value_or(false);
}

inline void set_flow_exp_context_init_error(bool v) {
  Attr(CommonIdx::flow_exp_context_init_error).SetIntValue(0, v, false, false);
}
inline bool get_flow_exp_context_init_error() const {
  return Attr(CommonIdx::flow_exp_context_init_error).GetIntValue().value_or(false);
}

inline void set_for_test(bool v) {
  Attr(CommonIdx::for_test).SetIntValue(0, v, false, false);
}
inline bool get_for_test() const {
  return Attr(CommonIdx::for_test).GetIntValue().value_or(0);
}

inline void set_has_ad_credict_user_score_redis(bool v) {
  Attr(CommonIdx::has_ad_credict_user_score_redis).SetIntValue(0, v, false, false);
}
inline bool get_has_ad_credict_user_score_redis() const {
  return Attr(CommonIdx::has_ad_credict_user_score_redis).GetIntValue().value_or(false);
}

inline void set_has_mingtou_ad(bool v) {
  Attr(CommonIdx::has_mingtou_ad).SetIntValue(0, v, false, false);
}
inline bool get_has_mingtou_ad() const {
  return Attr(CommonIdx::has_mingtou_ad).GetIntValue().value_or(false);
}

inline void set_has_retrieve_dsp(bool v) {
  Attr(CommonIdx::has_retrieve_dsp).SetIntValue(0, v, false, false);
}
inline bool get_has_retrieve_dsp() const {
  return Attr(CommonIdx::has_retrieve_dsp).GetIntValue().value_or(false);
}

inline void set_has_search_ad_response(bool v) {
  Attr(CommonIdx::has_search_ad_response).SetIntValue(0, v, false, false);
}
inline bool get_has_search_ad_response() const {
  return Attr(CommonIdx::has_search_ad_response).GetIntValue().value_or(false);
}

inline void set_is_brand_preview_user(bool v) {
  Attr(CommonIdx::is_brand_preview_user).SetIntValue(0, v, false, false);
}
inline bool get_is_brand_preview_user() const {
  return Attr(CommonIdx::is_brand_preview_user).GetIntValue().value_or(false);
}

inline void set_is_client_white_box_test_user(bool v) {
  Attr(CommonIdx::is_client_white_box_test_user).SetIntValue(0, v, false, false);
}
inline bool get_is_client_white_box_test_user() const {
  return Attr(CommonIdx::is_client_white_box_test_user).GetIntValue().value_or(false);
}

inline void set_is_client_white_box_user(bool v) {
  Attr(CommonIdx::is_client_white_box_user).SetIntValue(0, v, false, false);
}
inline bool get_is_client_white_box_user() const {
  return Attr(CommonIdx::is_client_white_box_user).GetIntValue().value_or(false);
}

inline void set_is_dsp_channel(bool v) {
  Attr(CommonIdx::is_dsp_channel).SetIntValue(0, v, false, false);
}
inline bool get_is_dsp_channel() const {
  return Attr(CommonIdx::is_dsp_channel).GetIntValue().value_or(false);
}

inline void set_is_fake_user(bool v) {
  Attr(CommonIdx::is_fake_user).SetIntValue(0, v, false, false);
}
inline bool get_is_fake_user() const {
  return Attr(CommonIdx::is_fake_user).GetIntValue().value_or(false);
}

inline void set_is_featured_selected(bool v) {
  Attr(CommonIdx::is_featured_selected).SetIntValue(0, v, false, false);
}
inline bool get_is_featured_selected() const {
  return Attr(CommonIdx::is_featured_selected).GetIntValue().value_or(false);
}

inline void set_is_guess_you_like_req(bool v) {
  Attr(CommonIdx::is_guess_you_like_req).SetIntValue(0, v, false, false);
}
inline bool get_is_guess_you_like_req() const {
  return Attr(CommonIdx::is_guess_you_like_req).GetIntValue().value_or(false);
}

inline void set_is_honor_device(bool v) {
  Attr(CommonIdx::is_honor_device).SetIntValue(0, v, false, false);
}
inline bool get_is_honor_device() const {
  return Attr(CommonIdx::is_honor_device).GetIntValue().value_or(false);
}

inline void set_is_hot_query(bool v) {
  Attr(CommonIdx::is_hot_query).SetIntValue(0, v, false, false);
}
inline bool get_is_hot_query() const {
  return Attr(CommonIdx::is_hot_query).GetIntValue().value_or(false);
}

inline void set_is_huawei_device(bool v) {
  Attr(CommonIdx::is_huawei_device).SetIntValue(0, v, false, false);
}
inline bool get_is_huawei_device() const {
  return Attr(CommonIdx::is_huawei_device).GetIntValue().value_or(false);
}

inline void set_is_inner_explore(bool v) {
  Attr(CommonIdx::is_inner_explore).SetIntValue(0, v, false, false);
}
inline bool get_is_inner_explore() const {
  return Attr(CommonIdx::is_inner_explore).GetIntValue().value_or(false);
}

inline void set_is_inner_explore_first(bool v) {
  Attr(CommonIdx::is_inner_explore_first).SetIntValue(0, v, false, false);
}
inline bool get_is_inner_explore_first() const {
  return Attr(CommonIdx::is_inner_explore_first).GetIntValue().value_or(false);
}

inline void set_is_inner_follow(bool v) {
  Attr(CommonIdx::is_inner_follow).SetIntValue(0, v, false, false);
}
inline bool get_is_inner_follow() const {
  return Attr(CommonIdx::is_inner_follow).GetIntValue().value_or(false);
}

inline void set_is_inner_live_request(bool v) {
  Attr(CommonIdx::is_inner_live_request).SetIntValue(0, v, false, false);
}
inline bool get_is_inner_live_request() const {
  return Attr(CommonIdx::is_inner_live_request).GetIntValue().value_or(0);
}

inline void set_is_inspire_live_cached(bool v) {
  Attr(CommonIdx::is_inspire_live_cached).SetIntValue(0, v, false, false);
}
inline bool get_is_inspire_live_cached() const {
  return Attr(CommonIdx::is_inspire_live_cached).GetIntValue().value_or(false);
}

inline void set_is_inspire_live_req(bool v) {
  Attr(CommonIdx::is_inspire_live_req).SetIntValue(0, v, false, false);
}
inline bool get_is_inspire_live_req() const {
  return Attr(CommonIdx::is_inspire_live_req).GetIntValue().value_or(false);
}

inline void set_is_inspire_merchant_req(bool v) {
  Attr(CommonIdx::is_inspire_merchant_req).SetIntValue(0, v, false, false);
}
inline bool get_is_inspire_merchant_req() const {
  return Attr(CommonIdx::is_inspire_merchant_req).GetIntValue().value_or(false);
}

inline void set_is_inspire_mix(bool v) {
  Attr(CommonIdx::is_inspire_mix).SetIntValue(0, v, false, false);
}
inline bool get_is_inspire_mix() const {
  return Attr(CommonIdx::is_inspire_mix).GetIntValue().value_or(false);
}

inline void set_is_ios_platform(bool v) {
  Attr(CommonIdx::is_ios_platform).SetIntValue(0, v, false, false);
}
inline bool get_is_ios_platform() const {
  return Attr(CommonIdx::is_ios_platform).GetIntValue().value_or(false);
}

inline void set_is_kuaishou_feed(bool v) {
  Attr(CommonIdx::is_kuaishou_feed).SetIntValue(0, v, false, false);
}
inline bool get_is_kuaishou_feed() const {
  return Attr(CommonIdx::is_kuaishou_feed).GetIntValue().value_or(false);
}

inline void set_is_live_rerank_req(bool v) {
  Attr(CommonIdx::is_live_rerank_req).SetIntValue(0, v, false, false);
}
inline bool get_is_live_rerank_req() const {
  return Attr(CommonIdx::is_live_rerank_req).GetIntValue().value_or(false);
}

inline void set_is_matrix_flow(bool v) {
  Attr(CommonIdx::is_matrix_flow).SetIntValue(0, v, false, false);
}
inline bool get_is_matrix_flow() const {
  return Attr(CommonIdx::is_matrix_flow).GetIntValue().value_or(false);
}

inline void set_is_matrix_splash_flow(bool v) {
  Attr(CommonIdx::is_matrix_splash_flow).SetIntValue(0, v, false, false);
}
inline bool get_is_matrix_splash_flow() const {
  return Attr(CommonIdx::is_matrix_splash_flow).GetIntValue().value_or(false);
}

inline void set_is_mixed_request(bool v) {
  Attr(CommonIdx::is_mixed_request).SetIntValue(0, v, false, false);
}
inline bool get_is_mixed_request() const {
  return Attr(CommonIdx::is_mixed_request).GetIntValue().value_or(false);
}

inline void set_is_multi_quota_flow(bool v) {
  Attr(CommonIdx::is_multi_quota_flow).SetIntValue(0, v, false, false);
}
inline bool get_is_multi_quota_flow() const {
  return Attr(CommonIdx::is_multi_quota_flow).GetIntValue().value_or(false);
}

inline void set_is_nebula_relation_video_first(bool v) {
  Attr(CommonIdx::is_nebula_relation_video_first).SetIntValue(0, v, false, false);
}
inline bool get_is_nebula_relation_video_first() const {
  return Attr(CommonIdx::is_nebula_relation_video_first).GetIntValue().value_or(false);
}

inline void set_is_nebula_request(bool v) {
  Attr(CommonIdx::is_nebula_request).SetIntValue(0, v, false, false);
}
inline bool get_is_nebula_request() const {
  return Attr(CommonIdx::is_nebula_request).GetIntValue().value_or(false);
}

inline void set_is_new_preview_logic(bool v) {
  Attr(CommonIdx::is_new_preview_logic).SetIntValue(0, v, false, false);
}
inline bool get_is_new_preview_logic() const {
  return Attr(CommonIdx::is_new_preview_logic).GetIntValue().value_or(false);
}

inline void set_is_only_live_recall_reqeust(bool v) {
  Attr(CommonIdx::is_only_live_recall_reqeust).SetIntValue(0, v, false, false);
}
inline bool get_is_only_live_recall_reqeust() const {
  return Attr(CommonIdx::is_only_live_recall_reqeust).GetIntValue().value_or(false);
}

inline void set_is_pv_recalled(bool v) {
  Attr(CommonIdx::is_pv_recalled).SetIntValue(0, v, false, false);
}
inline bool get_is_pv_recalled() const {
  return Attr(CommonIdx::is_pv_recalled).GetIntValue().value_or(false);
}

inline void set_is_rewarded(bool v) {
  Attr(CommonIdx::is_rewarded).SetIntValue(0, v, false, false);
}
inline bool get_is_rewarded() const {
  return Attr(CommonIdx::is_rewarded).GetIntValue().value_or(false);
}

inline void set_is_search_debug_white_list(bool v) {
  Attr(CommonIdx::is_search_debug_white_list).SetIntValue(0, v, false, false);
}
inline bool get_is_search_debug_white_list() const {
  return Attr(CommonIdx::is_search_debug_white_list).GetIntValue().value_or(false);
}

inline void set_is_support_gyroscope(bool v) {
  Attr(CommonIdx::is_support_gyroscope).SetIntValue(0, v, false, false);
}
inline bool get_is_support_gyroscope() const {
  return Attr(CommonIdx::is_support_gyroscope).GetIntValue().value_or(false);
}

inline void set_is_thanos_middle_page(bool v) {
  Attr(CommonIdx::is_thanos_middle_page).SetIntValue(0, v, false, false);
}
inline bool get_is_thanos_middle_page() const {
  return Attr(CommonIdx::is_thanos_middle_page).GetIntValue().value_or(false);
}

inline void set_is_thanos_request(bool v) {
  Attr(CommonIdx::is_thanos_request).SetIntValue(0, v, false, false);
}
inline bool get_is_thanos_request() const {
  return Attr(CommonIdx::is_thanos_request).GetIntValue().value_or(false);
}

inline void set_is_trace_api_detection(bool v) {
  Attr(CommonIdx::is_trace_api_detection).SetIntValue(0, v, false, false);
}
inline bool get_is_trace_api_detection() const {
  return Attr(CommonIdx::is_trace_api_detection).GetIntValue().value_or(false);
}

inline void set_is_universe_cached_res(bool v) {
  Attr(CommonIdx::is_universe_cached_res).SetIntValue(0, v, false, false);
}
inline bool get_is_universe_cached_res() const {
  return Attr(CommonIdx::is_universe_cached_res).GetIntValue().value_or(false);
}

inline void set_is_universe_flow(bool v) {
  Attr(CommonIdx::is_universe_flow).SetIntValue(0, v, false, false);
}
inline bool get_is_universe_flow() const {
  return Attr(CommonIdx::is_universe_flow).GetIntValue().value_or(false);
}

inline void set_is_universe_inner_loop_admit_traffic(bool v) {
  Attr(CommonIdx::is_universe_inner_loop_admit_traffic).SetIntValue(0, v, false, false);
}
inline bool get_is_universe_inner_loop_admit_traffic() const {
  return Attr(CommonIdx::is_universe_inner_loop_admit_traffic).GetIntValue().value_or(false);
}

inline void set_is_universe_rtb_flow(bool v) {
  Attr(CommonIdx::is_universe_rtb_flow).SetIntValue(0, v, false, false);
}
inline bool get_is_universe_rtb_flow() const {
  return Attr(CommonIdx::is_universe_rtb_flow).GetIntValue().value_or(false);
}

inline void set_is_unlogin_user(bool v) {
  Attr(CommonIdx::is_unlogin_user).SetIntValue(0, v, false, false);
}
inline bool get_is_unlogin_user() const {
  return Attr(CommonIdx::is_unlogin_user).GetIntValue().value_or(false);
}

inline void set_is_user_sug_scene(bool v) {
  Attr(CommonIdx::is_user_sug_scene).SetIntValue(0, v, false, false);
}
inline bool get_is_user_sug_scene() const {
  return Attr(CommonIdx::is_user_sug_scene).GetIntValue().value_or(false);
}

inline void set_mixed_goods_live_combo(bool v) {
  Attr(CommonIdx::mixed_goods_live_combo).SetIntValue(0, v, false, false);
}
inline bool get_mixed_goods_live_combo() const {
  return Attr(CommonIdx::mixed_goods_live_combo).GetIntValue().value_or(false);
}

inline void set_need_amd(bool v) {
  Attr(CommonIdx::need_amd).SetIntValue(0, v, false, false);
}
inline bool get_need_amd() const {
  return Attr(CommonIdx::need_amd).GetIntValue().value_or(false);
}

inline void set_need_fanstop(bool v) {
  Attr(CommonIdx::need_fanstop).SetIntValue(0, v, false, false);
}
inline bool get_need_fanstop() const {
  return Attr(CommonIdx::need_fanstop).GetIntValue().value_or(0);
}

inline void set_need_outer_retrieval(bool v) {
  Attr(CommonIdx::need_outer_retrieval).SetIntValue(0, v, false, false);
}
inline bool get_need_outer_retrieval() const {
  return Attr(CommonIdx::need_outer_retrieval).GetIntValue().value_or(true);
}

inline void set_need_splash_effect(bool v) {
  Attr(CommonIdx::need_splash_effect).SetIntValue(0, v, false, false);
}
inline bool get_need_splash_effect() const {
  return Attr(CommonIdx::need_splash_effect).GetIntValue().value_or(false);
}

inline void set_no_bonus_inspire_request(bool v) {
  Attr(CommonIdx::no_bonus_inspire_request).SetIntValue(0, v, false, false);
}
inline bool get_no_bonus_inspire_request() const {
  return Attr(CommonIdx::no_bonus_inspire_request).GetIntValue().value_or(false);
}

inline void set_online_mix_rank_diff(bool v) {
  Attr(CommonIdx::online_mix_rank_diff).SetIntValue(0, v, false, false);
}
inline bool get_online_mix_rank_diff() const {
  return Attr(CommonIdx::online_mix_rank_diff).GetIntValue().value_or(false);
}

inline void set_only_request_fanstop(bool v) {
  Attr(CommonIdx::only_request_fanstop).SetIntValue(0, v, false, false);
}
inline bool get_only_request_fanstop() const {
  return Attr(CommonIdx::only_request_fanstop).GetIntValue().value_or(false);
}

inline void set_pass_pre_filter(bool v) {
  Attr(CommonIdx::pass_pre_filter).SetIntValue(0, v, false, false);
}
inline bool get_pass_pre_filter() const {
  return Attr(CommonIdx::pass_pre_filter).GetIntValue().value_or(false);
}

inline void set_perf_ab_param_bool(bool v) {
  Attr(CommonIdx::perf_ab_param_bool).SetIntValue(0, v, false, false);
}
inline bool get_perf_ab_param_bool() const {
  return Attr(CommonIdx::perf_ab_param_bool).GetIntValue().value_or(false);
}

inline void set_rewarded_has_more(bool v) {
  Attr(CommonIdx::rewarded_has_more).SetIntValue(0, v, false, false);
}
inline bool get_rewarded_has_more() const {
  return Attr(CommonIdx::rewarded_has_more).GetIntValue().value_or(false);
}

inline void set_rewarded_change_one(bool v) {
  Attr(CommonIdx::rewarded_change_one).SetIntValue(0, v, false, false);
}
inline bool get_rewarded_change_one() const {
  return Attr(CommonIdx::rewarded_change_one).GetIntValue().value_or(false);
}

inline void set_incntv_ad_pgs_expire_time(int64_t v) {
  Attr(CommonIdx::incntv_ad_pgs_expire_time).SetIntValue(0, v, false, false);
}
inline int64_t get_incntv_ad_pgs_expire_time() const {
  return Attr(CommonIdx::incntv_ad_pgs_expire_time).GetIntValue().value_or(0);
}

inline void set_rta_ads_stra_start(bool v) {
  Attr(CommonIdx::rta_ads_stra_start).SetIntValue(0, v, false, false);
}
inline bool get_rta_ads_stra_start() const {
  return Attr(CommonIdx::rta_ads_stra_start).GetIntValue().value_or(false);
}

inline void set_search_ad_ghost(bool v) {
  Attr(CommonIdx::search_ad_ghost).SetIntValue(0, v, false, false);
}
inline bool get_search_ad_ghost() const {
  return Attr(CommonIdx::search_ad_ghost).GetIntValue().value_or(false);
}

inline void set_search_choosed_big_v(bool v) {
  Attr(CommonIdx::search_choosed_big_v).SetIntValue(0, v, false, false);
}
inline bool get_search_choosed_big_v() const {
  return Attr(CommonIdx::search_choosed_big_v).GetIntValue().value_or(false);
}

inline void set_search_choosed_big_v_new(bool v) {
  Attr(CommonIdx::search_choosed_big_v_new).SetIntValue(0, v, false, false);
}
inline bool get_search_choosed_big_v_new() const {
  return Attr(CommonIdx::search_choosed_big_v_new).GetIntValue().value_or(false);
}

inline void set_search_has_app_card(bool v) {
  Attr(CommonIdx::search_has_app_card).SetIntValue(0, v, false, false);
}
inline bool get_search_has_app_card() const {
  return Attr(CommonIdx::search_has_app_card).GetIntValue().value_or(false);
}

inline void set_search_has_brand(bool v) {
  Attr(CommonIdx::search_has_brand).SetIntValue(0, v, false, false);
}
inline bool get_search_has_brand() const {
  return Attr(CommonIdx::search_has_brand).GetIntValue().value_or(false);
}

inline void set_search_hit_hot_spot_query(bool v) {
  Attr(CommonIdx::search_hit_hot_spot_query).SetIntValue(0, v, false, false);
}
inline bool get_search_hit_hot_spot_query() const {
  return Attr(CommonIdx::search_hit_hot_spot_query).GetIntValue().value_or(false);
}

inline void set_should_be_filtered_by_cpm_thres(bool v) {
  Attr(CommonIdx::should_be_filtered_by_cpm_thres).SetIntValue(0, v, false, false);
}
inline bool get_should_be_filtered_by_cpm_thres() const {
  return Attr(CommonIdx::should_be_filtered_by_cpm_thres).GetIntValue().value_or(true);
}

inline void set_search_only_normal_region(bool v) {
  Attr(CommonIdx::search_only_normal_region).SetIntValue(0, v, false, false);
}
inline bool get_search_only_normal_region() const {
  return Attr(CommonIdx::search_only_normal_region).GetIntValue().value_or(false);
}

inline void set_small_game_ad_force_direct_call_switch(bool v) {
  Attr(CommonIdx::small_game_ad_force_direct_call_switch).SetIntValue(0, v, false, false);
}
inline bool get_small_game_ad_force_direct_call_switch() const {
  return Attr(CommonIdx::small_game_ad_force_direct_call_switch).GetIntValue().value_or(false);
}

inline void set_use_fanstop_exclusive_pos(bool v) {
  Attr(CommonIdx::use_fanstop_exclusive_pos).SetIntValue(0, v, false, false);
}
inline bool get_use_fanstop_exclusive_pos() const {
  return Attr(CommonIdx::use_fanstop_exclusive_pos).GetIntValue().value_or(false);
}

// curr_ad_data_v2 just declare funcs, impl in cc.inl
void set_curr_ad_data_v2(const kuaishou::ad::AdDataV2 * v);
const kuaishou::ad::AdDataV2 * get_curr_ad_data_v2() const;

inline DegradeLevel* mutable_degrade_level() {
  return Attr(CommonIdx::degrade_level).GetMutablePtrValue<DegradeLevel>();
}
inline const DegradeLevel& get_degrade_level() const {
  return *Attr(CommonIdx::degrade_level).GetPtrValue<DegradeLevel>();
}

inline void set_dynamic_cpm_threshold(double v) {
  Attr(CommonIdx::dynamic_cpm_threshold).SetDoubleValue(0, v, false, false);
}
inline double get_dynamic_cpm_threshold() const {
  return Attr(CommonIdx::dynamic_cpm_threshold).GetDoubleValue().value_or(-1.0);
}

inline void set_fine_sort_ecpm_thres(double v) {
  Attr(CommonIdx::fine_sort_ecpm_thres).SetDoubleValue(0, v, false, false);
}
inline double get_fine_sort_ecpm_thres() const {
  return Attr(CommonIdx::fine_sort_ecpm_thres).GetDoubleValue().value_or(0);
}

inline void set_fctr_roi_threshold(double v) {
  Attr(CommonIdx::fctr_roi_threshold).SetDoubleValue(0, v, false, false);
}
inline double get_fctr_roi_threshold() const {
  return Attr(CommonIdx::fctr_roi_threshold).GetDoubleValue().value_or(0.2);
}

inline void set_fctr_strategy_info(const std::string& v) {
  fctr_strategy_info = v;
  auto val = v; Attr(CommonIdx::fctr_strategy_info).SetStringValue(0, std::move(val), false, false);
}
inline std::string get_fctr_strategy_info() const {
  return fctr_strategy_info;
}

inline void set_last_pv_ad_max_score(double v) {
  Attr(CommonIdx::last_pv_ad_max_score).SetDoubleValue(0, v, false, false);
}
inline double get_last_pv_ad_max_score() const {
  return Attr(CommonIdx::last_pv_ad_max_score).GetDoubleValue().value_or(0);
}

inline void set_last_pv_other_max_score(double v) {
  Attr(CommonIdx::last_pv_other_max_score).SetDoubleValue(0, v, false, false);
}
inline double get_last_pv_other_max_score() const {
  return Attr(CommonIdx::last_pv_other_max_score).GetDoubleValue().value_or(0);
}

inline void set_last_pv_lose_ad_max_score(double v) {
  Attr(CommonIdx::last_pv_lose_ad_max_score).SetDoubleValue(0, v, false, false);
}
inline double get_last_pv_lose_ad_max_score() const {
  return Attr(CommonIdx::last_pv_lose_ad_max_score).GetDoubleValue().value_or(0);
}

inline void set_last_pv_lose_other_max_score(double v) {
  Attr(CommonIdx::last_pv_lose_other_max_score).SetDoubleValue(0, v, false, false);
}
inline double get_last_pv_lose_other_max_score() const {
  return Attr(CommonIdx::last_pv_lose_other_max_score).GetDoubleValue().value_or(0);
}

inline void set_ad_with_other_score_diff(double v) {
  Attr(CommonIdx::ad_with_other_score_diff).SetDoubleValue(0, v, false, false);
}
inline double get_ad_with_other_score_diff() const {
  return Attr(CommonIdx::ad_with_other_score_diff).GetDoubleValue().value_or(0);
}

inline void set_prev_commercial_min_win_score(double v) {
  Attr(CommonIdx::prev_commercial_min_win_score).SetDoubleValue(0, v, false, false);
}
inline double get_prev_commercial_min_win_score() const {
  return Attr(CommonIdx::prev_commercial_min_win_score).GetDoubleValue().value_or(0);
}

inline void set_is_fctr_model_failed(bool v) {
  Attr(CommonIdx::is_fctr_model_failed).SetIntValue(0, v, false, false);
}
inline bool get_is_fctr_model_failed() const {
  return Attr(CommonIdx::is_fctr_model_failed).GetIntValue().value_or(false);
}

inline void set_reward_avg_tcpm(double v) {
  Attr(CommonIdx::reward_avg_tcpm).SetDoubleValue(0, v, false, false);
}
inline double get_reward_avg_tcpm() const {
  return Attr(CommonIdx::reward_avg_tcpm).GetDoubleValue().value_or(-1.0);
}

inline void set_reward_user_percentile(double v) {
  Attr(CommonIdx::reward_user_percentile).SetDoubleValue(0, v, false, false);
}
inline double get_reward_user_percentile() const {
  return Attr(CommonIdx::reward_user_percentile).GetDoubleValue().value_or(-1.0);
}

inline void set_universe_rtb_coff(double v) {
  Attr(CommonIdx::universe_rtb_coff).SetDoubleValue(0, v, false, false);
}
inline double get_universe_rtb_coff() const {
  return Attr(CommonIdx::universe_rtb_coff).GetDoubleValue().value_or(1.0);
}

inline FansPosCalculor* mutable_fans_pos_calculor() {
  return Attr(CommonIdx::fans_pos_calculor).GetMutablePtrValue<FansPosCalculor>();
}
inline const FansPosCalculor& get_fans_pos_calculor() const {
  return *Attr(CommonIdx::fans_pos_calculor).GetPtrValue<FansPosCalculor>();
}

// fanstop_request just declare funcs, impl in cc.inl
void set_fanstop_request(FansTopRequest * v);
FansTopRequest * mutable_fanstop_request() const ;
const FansTopRequest * get_fanstop_request() const;

inline FanstopSupportInfo* mutable_fanstop_support_info() {
  return Attr(CommonIdx::fanstop_support_info).GetMutablePtrValue<FanstopSupportInfo>();
}
inline const FanstopSupportInfo& get_fanstop_support_info() const {
  return *Attr(CommonIdx::fanstop_support_info).GetPtrValue<FanstopSupportInfo>();
}

// forward_handler_common just declare funcs, impl in cc.inl
void set_forward_handler_common(ForwardHandlerCommon * v);
ForwardHandlerCommon * mutable_forward_handler_common() const ;
const ForwardHandlerCommon * get_forward_handler_common() const;

// front_server_request_ just declare funcs, impl in cc.inl
void set_front_server_request(FrontServerRequest * v);
FrontServerRequest * mutable_front_server_request() const ;
const FrontServerRequest * get_front_server_request() const;

// front_server_response_ just declare funcs, impl in cc.inl
void set_front_server_response(FrontServerResponse * v);
FrontServerResponse * mutable_front_server_response() const ;
const FrontServerResponse * get_front_server_response() const;

// dpa_creative_info_req just declare funcs, impl in cc.inl
GetCreativeReq * mutable_dpa_creative_info_req() const ;
const GetCreativeReq * get_dpa_creative_info_req() const;

// dpa_creative_info_resp just declare funcs, impl in cc.inl
GetCreativeResp * mutable_dpa_creative_info_resp() const ;
const GetCreativeResp * get_dpa_creative_info_resp() const;

// style_info_req just declare funcs, impl in cc.inl
GetStyleInfoReq * mutable_style_info_req() const ;
const GetStyleInfoReq * get_style_info_req() const;

// style_info_resp just declare funcs, impl in cc.inl
GetStyleInfoResp * mutable_style_info_resp() const ;
const GetStyleInfoResp * get_style_info_resp() const;

// dpa_unit_info_req just declare funcs, impl in cc.inl
GetUnitReq * mutable_dpa_unit_info_req() const ;
const GetUnitReq * get_dpa_unit_info_req() const;

// dpa_unit_info_resp just declare funcs, impl in cc.inl
GetUnitResp * mutable_dpa_unit_info_resp() const ;
const GetUnitResp * get_dpa_unit_info_resp() const;

// arena_ just declare funcs, impl in cc.inl
void set_arena(google::protobuf::Arena * v);
google::protobuf::Arena * mutable_arena() const ;
const google::protobuf::Arena * get_arena() const;

inline void set_app_card_photo_num(int32_t v) {
  Attr(CommonIdx::app_card_photo_num).SetIntValue(0, v, false, false);
}
inline int32_t get_app_card_photo_num() const {
  return Attr(CommonIdx::app_card_photo_num).GetIntValue().value_or(0);
}

inline void set_form_card_photo_num(int32_t v) {
  Attr(CommonIdx::form_card_photo_num).SetIntValue(0, v, false, false);
}
inline int32_t get_form_card_photo_num() const {
  return Attr(CommonIdx::form_card_photo_num).GetIntValue().value_or(0);
}

inline void set_grid_unit_id(int32_t v) {
  Attr(CommonIdx::grid_unit_id).SetIntValue(0, v, false, false);
}
inline int32_t get_grid_unit_id() const {
  return Attr(CommonIdx::grid_unit_id).GetIntValue().value_or(0);
}

inline void set_jinniu_branch_test(int32_t v) {
  Attr(CommonIdx::jinniu_branch_test).SetIntValue(0, v, false, false);
}
inline int32_t get_jinniu_branch_test() const {
  return Attr(CommonIdx::jinniu_branch_test).GetIntValue().value_or(-1);
}

inline void set_print_factor_info_random_num(int32_t v) {
  Attr(CommonIdx::print_factor_info_random_num).SetIntValue(0, v, false, false);
}
inline int32_t get_print_factor_info_random_num() const {
  return Attr(CommonIdx::print_factor_info_random_num).GetIntValue().value_or(0);
}

inline void set_pv_recalled_reason(int32_t v) {
  Attr(CommonIdx::pv_recalled_reason).SetIntValue(0, v, false, false);
}
inline int32_t get_pv_recalled_reason() const {
  return Attr(CommonIdx::pv_recalled_reason).GetIntValue().value_or(0);
}

inline void set_search_inner_ab_exp(int32_t v) {
  Attr(CommonIdx::search_inner_ab_exp).SetIntValue(0, v, false, false);
}
inline int32_t get_search_inner_ab_exp() const {
  return Attr(CommonIdx::search_inner_ab_exp).GetIntValue().value_or(0);
}

inline void set_strong_card_photo_num(int32_t v) {
  Attr(CommonIdx::strong_card_photo_num).SetIntValue(0, v, false, false);
}
inline int32_t get_strong_card_photo_num() const {
  return Attr(CommonIdx::strong_card_photo_num).GetIntValue().value_or(0);
}

inline void set_traffic_level(int32_t v) {
  Attr(CommonIdx::traffic_level).SetIntValue(0, v, false, false);
}
inline int32_t get_traffic_level() const {
  return Attr(CommonIdx::traffic_level).GetIntValue().value_or(-1);
}

inline void set_abtest_user_id(int64_t v) {
  Attr(CommonIdx::abtest_user_id).SetIntValue(0, v, false, false);
}
inline int64_t get_abtest_user_id() const {
  return Attr(CommonIdx::abtest_user_id).GetIntValue().value_or(0);
}

inline void set_antispam_code(int64_t v) {
  Attr(CommonIdx::antispam_code).SetIntValue(0, v, false, false);
}
inline int64_t get_antispam_code() const {
  return Attr(CommonIdx::antispam_code).GetIntValue().value_or(0);
}

inline void set_cache_origin_llsid(int64_t v) {
  Attr(CommonIdx::cache_origin_llsid).SetIntValue(0, v, false, false);
}
inline int64_t get_cache_origin_llsid() const {
  return Attr(CommonIdx::cache_origin_llsid).GetIntValue().value_or(0);
}

inline void set_llsid(int64_t v) {
  Attr(CommonIdx::llsid).SetIntValue(0, v, false, false);
}
inline int64_t get_llsid() const {
  return Attr(CommonIdx::llsid).GetIntValue().value_or(-1);
}

inline void set_low_quality_ads_filter_tag(int64_t v) {
  Attr(CommonIdx::low_quality_ads_filter_tag).SetIntValue(0, v, false, false);
}
inline int64_t get_low_quality_ads_filter_tag() const {
  return Attr(CommonIdx::low_quality_ads_filter_tag).GetIntValue().value_or(0);
}

inline void set_native_ad_project_id(int64_t v) {
  Attr(CommonIdx::native_ad_project_id).SetIntValue(0, v, false, false);
}
inline int64_t get_native_ad_project_id() const {
  return Attr(CommonIdx::native_ad_project_id).GetIntValue().value_or(0);
}

inline void set_page_id(int64_t v) {
  Attr(CommonIdx::page_id).SetIntValue(0, v, false, false);
}
inline int64_t get_page_id() const {
  return Attr(CommonIdx::page_id).GetIntValue().value_or(0);
}

inline void set_preview_creative_id(int64_t v) {
  Attr(CommonIdx::preview_creative_id).SetIntValue(0, v, false, false);
}
inline int64_t get_preview_creative_id() const {
  return Attr(CommonIdx::preview_creative_id).GetIntValue().value_or(0);
}

inline void set_project_id(int64_t v) {
  Attr(CommonIdx::project_id).SetIntValue(0, v, false, false);
}
inline int64_t get_project_id() const {
  return Attr(CommonIdx::project_id).GetIntValue().value_or(0);
}

inline void set_search_creator_uid(int64_t v) {
  Attr(CommonIdx::search_creator_uid).SetIntValue(0, v, false, false);
}
inline int64_t get_search_creator_uid() const {
  return Attr(CommonIdx::search_creator_uid).GetIntValue().value_or(0);
}

inline void set_selected_photo_bigv_id(int64_t v) {
  Attr(CommonIdx::selected_photo_bigv_id).SetIntValue(0, v, false, false);
}
inline int64_t get_selected_photo_bigv_id() const {
  return Attr(CommonIdx::selected_photo_bigv_id).GetIntValue().value_or(-1);
}

inline void set_soft_auction_size(int64_t v) {
  Attr(CommonIdx::soft_auction_size).SetIntValue(0, v, false, false);
}
inline int64_t get_soft_auction_size() const {
  return Attr(CommonIdx::soft_auction_size).GetIntValue().value_or(0);
}

inline void set_start_time_ns(int64_t v) {
  Attr(CommonIdx::start_time_ns).SetIntValue(0, v, false, false);
}
inline int64_t get_start_time_ns() const {
  return Attr(CommonIdx::start_time_ns).GetIntValue().value_or(0);
}

inline void set_start_time_us(int64_t v) {
  Attr(CommonIdx::start_time_us).SetIntValue(0, v, false, false);
}
inline int64_t get_start_time_us() const {
  return Attr(CommonIdx::start_time_us).GetIntValue().value_or(0);
}

inline void set_start_ts(int64_t v) {
  Attr(CommonIdx::start_ts).SetIntValue(0, v, false, false);
}
inline int64_t get_start_ts() const {
  return Attr(CommonIdx::start_ts).GetIntValue().value_or(0);
}

inline void set_sub_biz_id(int64_t v) {
  Attr(CommonIdx::sub_biz_id).SetIntValue(0, v, false, false);
}
inline int64_t get_sub_biz_id() const {
  return Attr(CommonIdx::sub_biz_id).GetIntValue().value_or(-1);
}

inline void set_sub_page_id(int64_t v) {
  Attr(CommonIdx::sub_page_id).SetIntValue(0, v, false, false);
}
inline int64_t get_sub_page_id() const {
  return Attr(CommonIdx::sub_page_id).GetIntValue().value_or(0);
}

inline void set_sug_recommend_uid(int64_t v) {
  Attr(CommonIdx::sug_recommend_uid).SetIntValue(0, v, false, false);
}
inline int64_t get_sug_recommend_uid() const {
  return Attr(CommonIdx::sug_recommend_uid).GetIntValue().value_or(0);
}

inline void set_traffic_marking(int64_t v) {
  Attr(CommonIdx::traffic_marking).SetIntValue(0, v, false, false);
}
inline int64_t get_traffic_marking() const {
  return Attr(CommonIdx::traffic_marking).GetIntValue().value_or(0);
}

inline void set_user_id(int64_t v) {
  Attr(CommonIdx::user_id).SetIntValue(0, v, false, false);
}
inline int64_t get_user_id() const {
  return Attr(CommonIdx::user_id).GetIntValue().value_or(0);
}

inline void set_work_flow_type(int64_t v) {
  Attr(CommonIdx::work_flow_type).SetIntValue(0, v, false, false);
}
inline int64_t get_work_flow_type() const {
  return Attr(CommonIdx::work_flow_type).GetIntValue().value_or(0);
}

inline void set_reward_treatment_coin(int v) {
  Attr(CommonIdx::reward_treatment_coin).SetIntValue(0, v, false, false);
}
inline int get_reward_treatment_coin() const {
  return Attr(CommonIdx::reward_treatment_coin).GetIntValue().value_or(0);
}

inline KnewsInspirePosStrategyInfo* mutable_knews_inspire_pos_strategy_info() {
  return Attr(CommonIdx::knews_inspire_pos_strategy_info).GetMutablePtrValue<KnewsInspirePosStrategyInfo>();
}
inline const KnewsInspirePosStrategyInfo& get_knews_inspire_pos_strategy_info() const {
  return *Attr(CommonIdx::knews_inspire_pos_strategy_info).GetPtrValue<KnewsInspirePosStrategyInfo>();
}

inline KnewsSplashPosStrategyInfo* mutable_knews_splash_pos_strategy_info() {
  return Attr(CommonIdx::knews_splash_pos_strategy_info).GetMutablePtrValue<KnewsSplashPosStrategyInfo>();
}
inline const KnewsSplashPosStrategyInfo& get_knews_splash_pos_strategy_info() const {
  return *Attr(CommonIdx::knews_splash_pos_strategy_info).GetPtrValue<KnewsSplashPosStrategyInfo>();
}

inline ks::abtest2::AbtestMappingId* mutable_abtest_mapping_id() {
  return Attr(CommonIdx::abtest_mapping_id).GetMutablePtrValue<ks::abtest2::AbtestMappingId>();
}
inline const ks::abtest2::AbtestMappingId& get_abtest_mapping_id() const {
  return *Attr(CommonIdx::abtest_mapping_id).GetPtrValue<ks::abtest2::AbtestMappingId>();
}

inline ks::ad_base::KLog* mutable_ad_klog() {
  return Attr(CommonIdx::ad_klog).GetMutablePtrValue<ks::ad_base::KLog>();
}
inline const ks::ad_base::KLog& get_ad_klog() const {
  return *Attr(CommonIdx::ad_klog).GetPtrValue<ks::ad_base::KLog>();
}

inline ks::ad_base::PosManagerBase* mutable_pos_manager() {
  return Attr(CommonIdx::pos_manager).GetMutablePtrValue<ks::ad_base::PosManagerBase>();
}
inline const ks::ad_base::PosManagerBase& get_pos_manager() const {
  return *Attr(CommonIdx::pos_manager).GetPtrValue<ks::ad_base::PosManagerBase>();
}

inline ks::engine_base::RtaAdData* mutable_rta_ad_data() {
  return Attr(CommonIdx::rta_ad_data).GetMutablePtrValue<ks::engine_base::RtaAdData>();
}
inline const ks::engine_base::RtaAdData& get_rta_ad_data() const {
  return *Attr(CommonIdx::rta_ad_data).GetPtrValue<ks::engine_base::RtaAdData>();
}

// kconf_session_context_dsp just declare funcs, impl in cc.inl
void set_kconf_session_context_dsp(ks::SessionContext * v);
ks::SessionContext * mutable_kconf_session_context_dsp() const ;
const ks::SessionContext * get_kconf_session_context_dsp() const;

inline void set_rta_req_filter_reason(const kuaishou::ad::AdEnum_RtaReqFilterReason v) {
  Attr(CommonIdx::rta_req_filter_reason).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const kuaishou::ad::AdEnum_RtaReqFilterReason get_rta_req_filter_reason() const {
  return static_cast<kuaishou::ad::AdEnum_RtaReqFilterReason>(Attr(CommonIdx::rta_req_filter_reason).GetIntValue().value_or(0));
}

// ad_pack_request_ just declare funcs, impl in cc.inl
void set_ad_pack_request(kuaishou::ad::AdPackRequest * v);
kuaishou::ad::AdPackRequest * mutable_ad_pack_request() const ;
const kuaishou::ad::AdPackRequest * get_ad_pack_request() const;

inline kuaishou::ad::AdRankPassThrough* mutable_ad_rank_pass_through() {
  return Attr(CommonIdx::ad_rank_pass_through).GetMutablePtrValue<kuaishou::ad::AdRankPassThrough>();
}
inline const kuaishou::ad::AdRankPassThrough& get_ad_rank_pass_through() const {
  return *Attr(CommonIdx::ad_rank_pass_through).GetPtrValue<kuaishou::ad::AdRankPassThrough>();
}

inline kuaishou::ad::algorithm::SampleSpec* mutable_native_used_item() {
  return Attr(CommonIdx::native_used_item).GetMutablePtrValue<kuaishou::ad::algorithm::SampleSpec>();
}
inline const kuaishou::ad::algorithm::SampleSpec& get_native_used_item() const {
  return *Attr(CommonIdx::native_used_item).GetPtrValue<kuaishou::ad::algorithm::SampleSpec>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_archimedes_used_item() {
  return Attr(CommonIdx::archimedes_used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_archimedes_used_item() const {
  return *Attr(CommonIdx::archimedes_used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_detail_used_item() {
  return Attr(CommonIdx::detail_used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_detail_used_item() const {
  return *Attr(CommonIdx::detail_used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_fanstop_used_item() {
  return Attr(CommonIdx::fanstop_used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_fanstop_used_item() const {
  return *Attr(CommonIdx::fanstop_used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_new_creative_used_item() {
  return Attr(CommonIdx::new_creative_used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_new_creative_used_item() const {
  return *Attr(CommonIdx::new_creative_used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_union_ltr_used_item() {
  return Attr(CommonIdx::union_ltr_used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_union_ltr_used_item() const {
  return *Attr(CommonIdx::union_ltr_used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_used_item() {
  return Attr(CommonIdx::used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_used_item() const {
  return *Attr(CommonIdx::used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::RankResult* mutable_brand_ad_default_rank_result() {
  return Attr(CommonIdx::brand_ad_default_rank_result).GetMutablePtrValue<kuaishou::ad::RankResult>();
}
inline const kuaishou::ad::RankResult& get_brand_ad_default_rank_result() const {
  return *Attr(CommonIdx::brand_ad_default_rank_result).GetPtrValue<kuaishou::ad::RankResult>();
}

inline kuaishou::ad::SearchMidPageConfig* mutable_mid_page_config() {
  return Attr(CommonIdx::mid_page_config).GetMutablePtrValue<kuaishou::ad::SearchMidPageConfig>();
}
inline const kuaishou::ad::SearchMidPageConfig& get_mid_page_config() const {
  return *Attr(CommonIdx::mid_page_config).GetPtrValue<kuaishou::ad::SearchMidPageConfig>();
}

inline kuaishou::ad::SessionRankInfo* mutable_brand_ad_default_rank_info() {
  return Attr(CommonIdx::brand_ad_default_rank_info).GetMutablePtrValue<kuaishou::ad::SessionRankInfo>();
}
inline const kuaishou::ad::SessionRankInfo& get_brand_ad_default_rank_info() const {
  return *Attr(CommonIdx::brand_ad_default_rank_info).GetPtrValue<kuaishou::ad::SessionRankInfo>();
}

inline kuaishou::ad::TransparentPackInfo* mutable_trans_pack_info() {
  return Attr(CommonIdx::trans_pack_info).GetMutablePtrValue<kuaishou::ad::TransparentPackInfo>();
}
inline const kuaishou::ad::TransparentPackInfo& get_trans_pack_info() const {
  return *Attr(CommonIdx::trans_pack_info).GetPtrValue<kuaishou::ad::TransparentPackInfo>();
}

inline void set_fake_user_type(const kuaishou::ad::UserFakeType v) {
  Attr(CommonIdx::fake_user_type).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const kuaishou::ad::UserFakeType get_fake_user_type() const {
  return static_cast<kuaishou::ad::UserFakeType>(Attr(CommonIdx::fake_user_type).GetIntValue().value_or(static_cast<int64_t>(::kuaishou::ad::UserFakeType::NORMAL_USER)));
}

inline kuaishou::log::ad::AdDspSimplifyAlwaysLog* mutable_simplify_always_log() {
  return Attr(CommonIdx::simplify_always_log).GetMutablePtrValue<kuaishou::log::ad::AdDspSimplifyAlwaysLog>();
}
inline const kuaishou::log::ad::AdDspSimplifyAlwaysLog& get_simplify_always_log() const {
  return *Attr(CommonIdx::simplify_always_log).GetPtrValue<kuaishou::log::ad::AdDspSimplifyAlwaysLog>();
}

inline kuaishou::log::ad::AdDspTraceAlwaysLog* mutable_trace_always_log() {
  return Attr(CommonIdx::trace_always_log).GetMutablePtrValue<kuaishou::log::ad::AdDspTraceAlwaysLog>();
}
inline const kuaishou::log::ad::AdDspTraceAlwaysLog& get_trace_always_log() const {
  return *Attr(CommonIdx::trace_always_log).GetPtrValue<kuaishou::log::ad::AdDspTraceAlwaysLog>();
}

inline kuaishou::log::ad::AdTraceFilterCondition* mutable_last_filter_condition() {
  return Attr(CommonIdx::last_filter_condition).GetMutablePtrValue<kuaishou::log::ad::AdTraceFilterCondition>();
}
inline const kuaishou::log::ad::AdTraceFilterCondition& get_last_filter_condition() const {
  return *Attr(CommonIdx::last_filter_condition).GetPtrValue<kuaishou::log::ad::AdTraceFilterCondition>();
}

inline kuaishou::log::ad::AdTraceFilterCondition* mutable_soft_last_filter_condition() {
  return Attr(CommonIdx::soft_last_filter_condition).GetMutablePtrValue<kuaishou::log::ad::AdTraceFilterCondition>();
}
inline const kuaishou::log::ad::AdTraceFilterCondition& get_soft_last_filter_condition() const {
  return *Attr(CommonIdx::soft_last_filter_condition).GetPtrValue<kuaishou::log::ad::AdTraceFilterCondition>();
}

inline kuaishou::photo::PhotoIdTimeResponse* mutable_author_photo_resp() {
  return Attr(CommonIdx::author_photo_resp).GetMutablePtrValue<kuaishou::photo::PhotoIdTimeResponse>();
}
inline const kuaishou::photo::PhotoIdTimeResponse& get_author_photo_resp() const {
  return *Attr(CommonIdx::author_photo_resp).GetPtrValue<kuaishou::photo::PhotoIdTimeResponse>();
}

inline MerchantDetailInfo* mutable_merchant_detail_info() {
  return Attr(CommonIdx::merchant_detail_info).GetMutablePtrValue<MerchantDetailInfo>();
}
inline const MerchantDetailInfo& get_merchant_detail_info() const {
  return *Attr(CommonIdx::merchant_detail_info).GetPtrValue<MerchantDetailInfo>();
}

inline void set_preview_type(const PreviewType v) {
  Attr(CommonIdx::preview_type).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const PreviewType get_preview_type() const {
  return static_cast<PreviewType>(Attr(CommonIdx::preview_type).GetIntValue().value_or(static_cast<int64_t>(PreviewType::kPreviewAd)));
}

inline Rank2FrontInfo* mutable_rank_2_front_info() {
  return Attr(CommonIdx::rank_2_front_info).GetMutablePtrValue<Rank2FrontInfo>();
}
inline const Rank2FrontInfo& get_rank_2_front_info() const {
  return *Attr(CommonIdx::rank_2_front_info).GetPtrValue<Rank2FrontInfo>();
}

inline void set_req_adapter_code(const ReqAdapterCode v) {
  Attr(CommonIdx::req_adapter_code).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const ReqAdapterCode get_req_adapter_code() const {
  return static_cast<ReqAdapterCode>(Attr(CommonIdx::req_adapter_code).GetIntValue().value_or(static_cast<int64_t>(ReqAdapterCode::kAdapterSuccess)));
}

inline SearchPreviewData* mutable_search_preview_data() {
  return Attr(CommonIdx::search_preview_data).GetMutablePtrValue<SearchPreviewData>();
}
inline const SearchPreviewData& get_search_preview_data() const {
  return *Attr(CommonIdx::search_preview_data).GetPtrValue<SearchPreviewData>();
}

inline SearchTabQueryInterveneConf_DetailConf* mutable_search_intervene_conf() {
  return Attr(CommonIdx::search_intervene_conf).GetMutablePtrValue<SearchTabQueryInterveneConf_DetailConf>();
}
inline const SearchTabQueryInterveneConf_DetailConf& get_search_intervene_conf() const {
  return *Attr(CommonIdx::search_intervene_conf).GetPtrValue<SearchTabQueryInterveneConf_DetailConf>();
}

inline absl::flat_hash_map<int64_t, std::string >* mutable_aigc_photo_cover_map() {
  return Attr(CommonIdx::aigc_photo_cover_map).GetMutablePtrValue<absl::flat_hash_map<int64_t, std::string >>();
}
inline const absl::flat_hash_map<int64_t, std::string >& get_aigc_photo_cover_map() const {
  return *Attr(CommonIdx::aigc_photo_cover_map).GetPtrValue<absl::flat_hash_map<int64_t, std::string >>();
}

inline absl::flat_hash_map<int64_t, std::string >* mutable_white_box_base_values_map() {
  return Attr(CommonIdx::white_box_base_values_map).GetMutablePtrValue<absl::flat_hash_map<int64_t, std::string >>();
}
inline const absl::flat_hash_map<int64_t, std::string >& get_white_box_base_values_map() const {
  return *Attr(CommonIdx::white_box_base_values_map).GetPtrValue<absl::flat_hash_map<int64_t, std::string >>();
}

inline absl::flat_hash_map<int64_t, std::string >* mutable_white_box_values_map() {
  return Attr(CommonIdx::white_box_values_map).GetMutablePtrValue<absl::flat_hash_map<int64_t, std::string >>();
}
inline const absl::flat_hash_map<int64_t, std::string >& get_white_box_values_map() const {
  return *Attr(CommonIdx::white_box_values_map).GetPtrValue<absl::flat_hash_map<int64_t, std::string >>();
}

inline std::shared_ptr<AuctionParams >* mutable_auction_params() {
  return &auction_params;
}
inline const std::shared_ptr<AuctionParams >& get_auction_params() const {
  return auction_params;
}

inline std::shared_ptr<const ks::AbtestUserInfo >* mutable_ab_user_info() {
  return &ab_user_info;
}
inline const std::shared_ptr<const ks::AbtestUserInfo >& get_ab_user_info() const {
  return ab_user_info;
}

inline std::shared_ptr<kconf::FeedDistributioConf >* mutable_feed_distribution_conf() {
  return &feed_distribution_conf;
}
inline const std::shared_ptr<kconf::FeedDistributioConf >& get_feed_distribution_conf() const {
  return feed_distribution_conf;
}

inline std::shared_ptr<kconf::OuterNativeNegFilterRateConfig >* mutable_photo_negative_tags() {
  return &photo_negative_tags;
}
inline const std::shared_ptr<kconf::OuterNativeNegFilterRateConfig >& get_photo_negative_tags() const {
  return photo_negative_tags;
}

inline std::shared_ptr <::ks::front_server::FansTopSessionData >* mutable_fanstop_ctx() {
  return &fanstop_ctx_;
}
inline const std::shared_ptr <::ks::front_server::FansTopSessionData >& get_fanstop_ctx() const {
  return fanstop_ctx_;
}

inline std::shared_ptr <::ks::infra::TailNumberV2 >* mutable_boost_bid_control_tail() {
  return &boost_bid_control_tail;
}
inline const std::shared_ptr <::ks::infra::TailNumberV2 >& get_boost_bid_control_tail() const {
  return boost_bid_control_tail;
}

inline std::shared_ptr<NativeAuctionParams >* mutable_native_auction_params() {
  return &native_auction_params;
}
inline const std::shared_ptr<NativeAuctionParams >& get_native_auction_params() const {
  return native_auction_params;
}

inline std::shared_ptr<absl::flat_hash_set<int64_t> >* mutable_juxing_supplement_exp_account() {
  return &juxing_supplement_exp_account;
}
inline const std::shared_ptr<absl::flat_hash_set<int64_t> >& get_juxing_supplement_exp_account() const {
  return juxing_supplement_exp_account;
}

inline std::shared_ptr<absl::flat_hash_set<int64_t> >* mutable_outer_native_diff_author_open_neg() {
  return &outer_native_diff_author_open_neg;
}
inline const std::shared_ptr<absl::flat_hash_set<int64_t> >& get_outer_native_diff_author_open_neg() const {
  return outer_native_diff_author_open_neg;
}

inline std::shared_ptr<absl::flat_hash_set<std::string> >* mutable_iap_ocpx_set() {
  return &iap_ocpx_set;
}
inline const std::shared_ptr<absl::flat_hash_set<std::string> >& get_iap_ocpx_set() const {
  return iap_ocpx_set;
}

inline std::shared_ptr<absl::flat_hash_set<std::string> >* mutable_small_game_ad_force_direct_call_black_list() {
  return &small_game_ad_force_direct_call_black_list;
}
inline const std::shared_ptr<absl::flat_hash_set<std::string> >& get_small_game_ad_force_direct_call_black_list() const {
  return small_game_ad_force_direct_call_black_list;
}

inline std::shared_ptr<absl::flat_hash_set<std::string> >* mutable_small_game_ad_force_direct_call_white_list() {
  return &small_game_ad_force_direct_call_white_list;
}
inline const std::shared_ptr<absl::flat_hash_set<std::string> >& get_small_game_ad_force_direct_call_white_list() const {
  return small_game_ad_force_direct_call_white_list;
}

inline std::shared_ptr<absl::flat_hash_set<std::string> >* mutable_cartoon_product_set() {
  return &cartoon_product_set;
}
inline const std::shared_ptr<absl::flat_hash_set<std::string> >& get_cartoon_product_set() const {
  return cartoon_product_set;
}

inline std::vector<int64_t>* mutable_incntv_ad_pgs_thr() {
  return Attr(CommonIdx::incntv_ad_pgs_thr).GetMutablePtrValue<std::vector<int64_t >>();
}
inline const std::vector<int64_t>& get_incntv_ad_pgs_thr() const {
  return *Attr(CommonIdx::incntv_ad_pgs_thr).GetPtrValue<std::vector<int64_t >>();
}
inline std::vector<int64_t>* mutable_incntv_ad_pgs_amt() {
  return Attr(CommonIdx::incntv_ad_pgs_amt).GetMutablePtrValue<std::vector<int64_t >>();
}
inline const std::vector<int64_t>& get_incntv_ad_pgs_amt() const {
  return *Attr(CommonIdx::incntv_ad_pgs_amt).GetPtrValue<std::vector<int64_t >>();
}

inline void set_abtest_device_id(const std::string& v) {
  abtest_device_id = v;
  auto val = v; Attr(CommonIdx::abtest_device_id).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_abtest_device_id() const {
  return abtest_device_id;
}

inline void set_antispam_ext(const std::string& v) {
  antispam_ext = v;
  auto val = v; Attr(CommonIdx::antispam_ext).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_antispam_ext() const {
  return antispam_ext;
}

inline void set_app_id(const std::string& v) {
  app_id = v;
  auto val = v; Attr(CommonIdx::app_id).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_app_id() const {
  return app_id;
}

inline void set_app_version(const std::string& v) {
  app_version = v;
  auto val = v; Attr(CommonIdx::app_version).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_app_version() const {
  return app_version;
}

inline void set_browsed_data_redis(const std::string& v) {
  browsed_data_redis = v;
  auto val = v; Attr(CommonIdx::browsed_data_redis).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_browsed_data_redis() const {
  return browsed_data_redis;
}

inline void set_device_id(const std::string& v) {
  device_id = v;
  auto val = v; Attr(CommonIdx::device_id).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_device_id() const {
  return device_id;
}

inline void set_device_id_hash_key(const std::string& v) {
  device_id_hash_key = v;
  auto val = v; Attr(CommonIdx::device_id_hash_key).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_device_id_hash_key() const {
  return device_id_hash_key;
}

inline void set_ky_ad_entrance(const std::string& v) {
  ky_ad_entrance = v;
  auto val = v; Attr(CommonIdx::ky_ad_entrance).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_ky_ad_entrance() const {
  return ky_ad_entrance;
}

inline void set_media_wx_small_app_id(const std::string& v) {
  media_wx_small_app_id = v;
  auto val = v; Attr(CommonIdx::media_wx_small_app_id).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_media_wx_small_app_id() const {
  return media_wx_small_app_id;
}

inline void set_micro_app_charge_info_key(const std::string& v) {
  micro_app_charge_info_key = v;
  auto val = v; Attr(CommonIdx::micro_app_charge_info_key).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_micro_app_charge_info_key() const {
  return micro_app_charge_info_key;
}

inline void set_minigame_app_id(const std::string& v) {
  minigame_app_id = v;
  auto val = v; Attr(CommonIdx::minigame_app_id).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_minigame_app_id() const {
  return minigame_app_id;
}

inline void set_minigame_from_type(const std::string& v) {
  minigame_from_type = v;
  auto val = v; Attr(CommonIdx::minigame_from_type).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_minigame_from_type() const {
  return minigame_from_type;
}

inline void set_origin_age_segment(const std::string& v) {
  origin_age_segment = v;
  auto val = v; Attr(CommonIdx::origin_age_segment).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_origin_age_segment() const {
  return origin_age_segment;
}

inline void set_search_ab_group_name(const std::string& v) {
  search_ab_group_name = v;
  auto val = v; Attr(CommonIdx::search_ab_group_name).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_search_ab_group_name() const {
  return search_ab_group_name;
}

inline void set_search_dot_query(const std::string& v) {
  search_dot_query = v;
  auto val = v; Attr(CommonIdx::search_dot_query).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_search_dot_query() const {
  return search_dot_query;
}

inline void set_splash_boot_time(const std::string& v) {
  splash_boot_time = v;
  auto val = v; Attr(CommonIdx::splash_boot_time).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_splash_boot_time() const {
  return splash_boot_time;
}

inline void set_splash_file_time(const std::string& v) {
  splash_file_time = v;
  auto val = v; Attr(CommonIdx::splash_file_time).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_splash_file_time() const {
  return splash_file_time;
}

inline void set_splash_update_time(const std::string& v) {
  splash_update_time = v;
  auto val = v; Attr(CommonIdx::splash_update_time).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_splash_update_time() const {
  return splash_update_time;
}

inline void set_tab_name(const std::string& v) {
  tab_name = v;
  auto val = v; Attr(CommonIdx::tab_name).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_tab_name() const {
  return tab_name;
}

inline void set_test_device_id(const std::string& v) {
  test_device_id = v;
  auto val = v; Attr(CommonIdx::test_device_id).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_test_device_id() const {
  return test_device_id;
}

inline void set_test_zd_ab_group_name(const std::string& v) {
  test_zd_ab_group_name = v;
  auto val = v; Attr(CommonIdx::test_zd_ab_group_name).SetStringValue(0, std::move(val), false, false);
}
inline const std::string& get_test_zd_ab_group_name() const {
  return test_zd_ab_group_name;
}

inline std::unordered_map<int64, int >* mutable_creative_id_2_idx() {
  return Attr(CommonIdx::creative_id_2_idx).GetMutablePtrValue<std::unordered_map<int64, int >>();
}
inline const std::unordered_map<int64, int >& get_creative_id_2_idx() const {
  return *Attr(CommonIdx::creative_id_2_idx).GetPtrValue<std::unordered_map<int64, int >>();
}

inline std::unordered_map<int64_t, AdMatrixStyleMaterial >* mutable_matrix_style_material_cover_map() {
  return Attr(CommonIdx::matrix_style_material_cover_map).GetMutablePtrValue<std::unordered_map<int64_t, AdMatrixStyleMaterial >>();
}
inline const std::unordered_map<int64_t, AdMatrixStyleMaterial >& get_matrix_style_material_cover_map() const {
  return *Attr(CommonIdx::matrix_style_material_cover_map).GetPtrValue<std::unordered_map<int64_t, AdMatrixStyleMaterial >>();
}

inline std::unordered_map<int64_t, AdStyleMaterial >* mutable_rtb_style_info_map() {
  return Attr(CommonIdx::rtb_style_info_map).GetMutablePtrValue<std::unordered_map<int64_t, AdStyleMaterial >>();
}
inline const std::unordered_map<int64_t, AdStyleMaterial >& get_rtb_style_info_map() const {
  return *Attr(CommonIdx::rtb_style_info_map).GetPtrValue<std::unordered_map<int64_t, AdStyleMaterial >>();
}

inline std::unordered_map<int64_t, AdStyleMaterial >* mutable_style_material_dpa_card_map() {
  return Attr(CommonIdx::style_material_dpa_card_map).GetMutablePtrValue<std::unordered_map<int64_t, AdStyleMaterial >>();
}
inline const std::unordered_map<int64_t, AdStyleMaterial >& get_style_material_dpa_card_map() const {
  return *Attr(CommonIdx::style_material_dpa_card_map).GetPtrValue<std::unordered_map<int64_t, AdStyleMaterial >>();
}

inline std::unordered_map<int64_t, AdStyleMaterial >* mutable_style_material_land_page_component_map() {
  return Attr(CommonIdx::style_material_land_page_component_map).GetMutablePtrValue<std::unordered_map<int64_t, AdStyleMaterial >>();
}
inline const std::unordered_map<int64_t, AdStyleMaterial >& get_style_material_land_page_component_map() const {
  return *Attr(CommonIdx::style_material_land_page_component_map).GetPtrValue<std::unordered_map<int64_t, AdStyleMaterial >>();
}

inline std::unordered_map<int64_t, AdStyleMaterial >* mutable_style_material_pic_card_map() {
  return Attr(CommonIdx::style_material_pic_card_map).GetMutablePtrValue<std::unordered_map<int64_t, AdStyleMaterial >>();
}
inline const std::unordered_map<int64_t, AdStyleMaterial >& get_style_material_pic_card_map() const {
  return *Attr(CommonIdx::style_material_pic_card_map).GetPtrValue<std::unordered_map<int64_t, AdStyleMaterial >>();
}

inline std::unordered_map<int64_t, const CreativeItem * >* mutable_dpa_creative_info_map() {
  return Attr(CommonIdx::dpa_creative_info_map).GetMutablePtrValue<std::unordered_map<int64_t, const CreativeItem * >>();
}
inline const std::unordered_map<int64_t, const CreativeItem * >& get_dpa_creative_info_map() const {
  return *Attr(CommonIdx::dpa_creative_info_map).GetPtrValue<std::unordered_map<int64_t, const CreativeItem * >>();
}

inline std::unordered_map<int64_t, const UnitItem * >* mutable_dpa_unit_info_map() {
  return Attr(CommonIdx::dpa_unit_info_map).GetMutablePtrValue<std::unordered_map<int64_t, const UnitItem * >>();
}
inline const std::unordered_map<int64_t, const UnitItem * >& get_dpa_unit_info_map() const {
  return *Attr(CommonIdx::dpa_unit_info_map).GetPtrValue<std::unordered_map<int64_t, const UnitItem * >>();
}

inline std::unordered_map<int64_t, double >* mutable_tax_ratio_map() {
  return Attr(CommonIdx::tax_ratio_map).GetMutablePtrValue<std::unordered_map<int64_t, double >>();
}
inline const std::unordered_map<int64_t, double >& get_tax_ratio_map() const {
  return *Attr(CommonIdx::tax_ratio_map).GetPtrValue<std::unordered_map<int64_t, double >>();
}

inline std::unordered_map<int64_t, FrontForceExploreLog >* mutable_front_force_explore_log_map() {
  return Attr(CommonIdx::front_force_explore_log_map).GetMutablePtrValue<std::unordered_map<int64_t, FrontForceExploreLog >>();
}
inline const std::unordered_map<int64_t, FrontForceExploreLog >& get_front_force_explore_log_map() const {
  return *Attr(CommonIdx::front_force_explore_log_map).GetPtrValue<std::unordered_map<int64_t, FrontForceExploreLog >>();
}

inline std::unordered_map<int64_t, int64_t >* mutable_creative_lives() {
  return Attr(CommonIdx::creative_lives).GetMutablePtrValue<std::unordered_map<int64_t, int64_t >>();
}
inline const std::unordered_map<int64_t, int64_t >& get_creative_lives() const {
  return *Attr(CommonIdx::creative_lives).GetPtrValue<std::unordered_map<int64_t, int64_t >>();
}

inline std::unordered_map<int64_t, int64_t >* mutable_tax_amount_map() {
  return Attr(CommonIdx::tax_amount_map).GetMutablePtrValue<std::unordered_map<int64_t, int64_t >>();
}
inline const std::unordered_map<int64_t, int64_t >& get_tax_amount_map() const {
  return *Attr(CommonIdx::tax_amount_map).GetPtrValue<std::unordered_map<int64_t, int64_t >>();
}

inline std::unordered_map<int64_t, kuaishou::ad::AdEnum_ItemType >* mutable_rtb_splash_derived_item_type_map() {
  return Attr(CommonIdx::rtb_splash_derived_item_type_map).GetMutablePtrValue<std::unordered_map<int64_t, kuaishou::ad::AdEnum_ItemType >>();
}
inline const std::unordered_map<int64_t, kuaishou::ad::AdEnum_ItemType >& get_rtb_splash_derived_item_type_map() const {
  return *Attr(CommonIdx::rtb_splash_derived_item_type_map).GetPtrValue<std::unordered_map<int64_t, kuaishou::ad::AdEnum_ItemType >>();
}

inline std::unordered_map<int64_t, kuaishou::ad::AdMixedInfo >* mutable_auction_calced_mixed_info() {
  return Attr(CommonIdx::auction_calced_mixed_info).GetMutablePtrValue<std::unordered_map<int64_t, kuaishou::ad::AdMixedInfo >>();
}
inline const std::unordered_map<int64_t, kuaishou::ad::AdMixedInfo >& get_auction_calced_mixed_info() const {
  return *Attr(CommonIdx::auction_calced_mixed_info).GetPtrValue<std::unordered_map<int64_t, kuaishou::ad::AdMixedInfo >>();
}

inline std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >* mutable_style_material_land_page_map() {
  return Attr(CommonIdx::style_material_land_page_map).GetMutablePtrValue<std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >>();
}
inline const std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >& get_style_material_land_page_map() const {
  return *Attr(CommonIdx::style_material_land_page_map).GetPtrValue<std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >>();
}

inline std::unordered_map<int64_t, std::string >* mutable_keywords_photo_results_map() {
  return Attr(CommonIdx::keywords_photo_results_map).GetMutablePtrValue<std::unordered_map<int64_t, std::string >>();
}
inline const std::unordered_map<int64_t, std::string >& get_keywords_photo_results_map() const {
  return *Attr(CommonIdx::keywords_photo_results_map).GetPtrValue<std::unordered_map<int64_t, std::string >>();
}

inline std::unordered_map<int64_t, std::string >* mutable_spu_photo_results_map() {
  return Attr(CommonIdx::spu_photo_results_map).GetMutablePtrValue<std::unordered_map<int64_t, std::string >>();
}
inline const std::unordered_map<int64_t, std::string >& get_spu_photo_results_map() const {
  return *Attr(CommonIdx::spu_photo_results_map).GetPtrValue<std::unordered_map<int64_t, std::string >>();
}

inline std::unordered_map<int64_t, std::string >* mutable_tag_photo_results_map() {
  return Attr(CommonIdx::tag_photo_results_map).GetMutablePtrValue<std::unordered_map<int64_t, std::string >>();
}
inline const std::unordered_map<int64_t, std::string >& get_tag_photo_results_map() const {
  return *Attr(CommonIdx::tag_photo_results_map).GetPtrValue<std::unordered_map<int64_t, std::string >>();
}

inline std::unordered_map<int64_t, std::string >* mutable_target_break_map() {
  return Attr(CommonIdx::target_break_map).GetMutablePtrValue<std::unordered_map<int64_t, std::string >>();
}
inline const std::unordered_map<int64_t, std::string >& get_target_break_map() const {
  return *Attr(CommonIdx::target_break_map).GetPtrValue<std::unordered_map<int64_t, std::string >>();
}

inline std::unordered_map<int64_t, std::vector<AdStyleMaterial> >* mutable_style_material_download_mid_page_map() {
  return Attr(CommonIdx::style_material_download_mid_page_map).GetMutablePtrValue<std::unordered_map<int64_t, std::vector<AdStyleMaterial> >>();
}
inline const std::unordered_map<int64_t, std::vector<AdStyleMaterial> >& get_style_material_download_mid_page_map() const {
  return *Attr(CommonIdx::style_material_download_mid_page_map).GetPtrValue<std::unordered_map<int64_t, std::vector<AdStyleMaterial> >>();
}

inline std::unordered_map<int64_t, std::vector<AdStyleMaterial> >* mutable_style_material_novel_map() {
  return Attr(CommonIdx::style_material_novel_map).GetMutablePtrValue<std::unordered_map<int64_t, std::vector<AdStyleMaterial> >>();
}
inline const std::unordered_map<int64_t, std::vector<AdStyleMaterial> >& get_style_material_novel_map() const {
  return *Attr(CommonIdx::style_material_novel_map).GetPtrValue<std::unordered_map<int64_t, std::vector<AdStyleMaterial> >>();
}

inline std::unordered_map<int64_t, std::vector<AdStyleMaterialSub> >* mutable_style_material_map() {
  return Attr(CommonIdx::style_material_map).GetMutablePtrValue<std::unordered_map<int64_t, std::vector<AdStyleMaterialSub> >>();
}
inline const std::unordered_map<int64_t, std::vector<AdStyleMaterialSub> >& get_style_material_map() const {
  return *Attr(CommonIdx::style_material_map).GetPtrValue<std::unordered_map<int64_t, std::vector<AdStyleMaterialSub> >>();
}

inline std::unordered_map<int64_t, std::vector<kuaishou::ad::AdEnum_AdMiddlePageType> >* mutable_app_mid_page_type_res() {
  return Attr(CommonIdx::app_mid_page_type_res).GetMutablePtrValue<std::unordered_map<int64_t, std::vector<kuaishou::ad::AdEnum_AdMiddlePageType> >>();
}
inline const std::unordered_map<int64_t, std::vector<kuaishou::ad::AdEnum_AdMiddlePageType> >& get_app_mid_page_type_res() const {
  return *Attr(CommonIdx::app_mid_page_type_res).GetPtrValue<std::unordered_map<int64_t, std::vector<kuaishou::ad::AdEnum_AdMiddlePageType> >>();
}

inline std::unordered_map<int64_t, std::vector<kuaishou::ad::StyleData> >* mutable_style_data_for_online_join() {
  return Attr(CommonIdx::style_data_for_online_join).GetMutablePtrValue<std::unordered_map<int64_t, std::vector<kuaishou::ad::StyleData> >>();
}
inline const std::unordered_map<int64_t, std::vector<kuaishou::ad::StyleData> >& get_style_data_for_online_join() const {
  return *Attr(CommonIdx::style_data_for_online_join).GetPtrValue<std::unordered_map<int64_t, std::vector<kuaishou::ad::StyleData> >>();
}

inline std::unordered_map<int64_t, StyleDataPackResult >* mutable_style_data_pack_results() {
  return Attr(CommonIdx::style_data_pack_results).GetMutablePtrValue<std::unordered_map<int64_t, StyleDataPackResult >>();
}
inline const std::unordered_map<int64_t, StyleDataPackResult >& get_style_data_pack_results() const {
  return *Attr(CommonIdx::style_data_pack_results).GetPtrValue<std::unordered_map<int64_t, StyleDataPackResult >>();
}

inline std::unordered_map<std::string, std::unordered_map<std::string, int64_t> >* mutable_universe_time_cost_map() {
  return Attr(CommonIdx::universe_time_cost_map).GetMutablePtrValue<std::unordered_map<std::string, std::unordered_map<std::string, int64_t> >>();
}
inline const std::unordered_map<std::string, std::unordered_map<std::string, int64_t> >& get_universe_time_cost_map() const {
  return *Attr(CommonIdx::universe_time_cost_map).GetPtrValue<std::unordered_map<std::string, std::unordered_map<std::string, int64_t> >>();
}

inline std::unordered_set<int64_t >* mutable_creative_with_half_screen() {
  return Attr(CommonIdx::creative_with_half_screen).GetMutablePtrValue<std::unordered_set<int64_t >>();
}
inline const std::unordered_set<int64_t >& get_creative_with_half_screen() const {
  return *Attr(CommonIdx::creative_with_half_screen).GetPtrValue<std::unordered_set<int64_t >>();
}

inline std::unordered_set<int64_t >* mutable_filter_creative_id_set() {
  return Attr(CommonIdx::filter_creative_id_set).GetMutablePtrValue<std::unordered_set<int64_t >>();
}
inline const std::unordered_set<int64_t >& get_filter_creative_id_set() const {
  return *Attr(CommonIdx::filter_creative_id_set).GetPtrValue<std::unordered_set<int64_t >>();
}

inline std::unordered_set<int64_t >* mutable_hot_live_id_set() {
  return Attr(CommonIdx::hot_live_id_set).GetMutablePtrValue<std::unordered_set<int64_t >>();
}
inline const std::unordered_set<int64_t >& get_hot_live_id_set() const {
  return *Attr(CommonIdx::hot_live_id_set).GetPtrValue<std::unordered_set<int64_t >>();
}

inline std::unordered_set<int64_t >* mutable_hot_photo_id_set() {
  return Attr(CommonIdx::hot_photo_id_set).GetMutablePtrValue<std::unordered_set<int64_t >>();
}
inline const std::unordered_set<int64_t >& get_hot_photo_id_set() const {
  return *Attr(CommonIdx::hot_photo_id_set).GetPtrValue<std::unordered_set<int64_t >>();
}

inline std::unordered_set<int64_t >* mutable_third_duanju_creative_list() {
  return Attr(CommonIdx::third_duanju_creative_list).GetMutablePtrValue<std::unordered_set<int64_t >>();
}
inline const std::unordered_set<int64_t >& get_third_duanju_creative_list() const {
  return *Attr(CommonIdx::third_duanju_creative_list).GetPtrValue<std::unordered_set<int64_t >>();
}

inline std::unordered_set<int64_t >* mutable_series_list_card_creative_list() {
  return Attr(CommonIdx::series_list_card_creative_list).GetMutablePtrValue<std::unordered_set<int64_t >>();
}
inline const std::unordered_set<int64_t >& get_series_list_card_creative_list() const {
  return *Attr(CommonIdx::series_list_card_creative_list).GetPtrValue<std::unordered_set<int64_t >>();
}

inline std::unordered_set<int64 >* mutable_user_paid_audience_set() {
  return Attr(CommonIdx::user_paid_audience_set).GetMutablePtrValue<std::unordered_set<int64 >>();
}
inline const std::unordered_set<int64 >& get_user_paid_audience_set() const {
  return *Attr(CommonIdx::user_paid_audience_set).GetPtrValue<std::unordered_set<int64 >>();
}

inline std::unordered_set<std::string >* mutable_kxy_subsidy_products1() {
  return Attr(CommonIdx::kxy_subsidy_products1).GetMutablePtrValue<std::unordered_set<std::string >>();
}
inline const std::unordered_set<std::string >& get_kxy_subsidy_products1() const {
  return *Attr(CommonIdx::kxy_subsidy_products1).GetPtrValue<std::unordered_set<std::string >>();
}

inline std::unordered_set<std::string >* mutable_kxy_subsidy_products2() {
  return Attr(CommonIdx::kxy_subsidy_products2).GetMutablePtrValue<std::unordered_set<std::string >>();
}
inline const std::unordered_set<std::string >& get_kxy_subsidy_products2() const {
  return *Attr(CommonIdx::kxy_subsidy_products2).GetPtrValue<std::unordered_set<std::string >>();
}

inline std::unordered_set<uint64 >* mutable_follow_ids() {
  return Attr(CommonIdx::follow_ids).GetMutablePtrValue<std::unordered_set<uint64 >>();
}
inline const std::unordered_set<uint64 >& get_follow_ids() const {
  return *Attr(CommonIdx::follow_ids).GetPtrValue<std::unordered_set<uint64 >>();
}

inline std::unordered_set<uint64 >* mutable_force_direct_call_cids() {
  return Attr(CommonIdx::force_direct_call_cids).GetMutablePtrValue<std::unordered_set<uint64 >>();
}
inline const std::unordered_set<uint64 >& get_force_direct_call_cids() const {
  return *Attr(CommonIdx::force_direct_call_cids).GetPtrValue<std::unordered_set<uint64 >>();
}

inline std::vector<AdCreativePreview >* mutable_multi_user_preview_info() {
  return Attr(CommonIdx::multi_user_preview_info).GetMutablePtrValue<std::vector<AdCreativePreview >>();
}
inline const std::vector<AdCreativePreview >& get_multi_user_preview_info() const {
  return *Attr(CommonIdx::multi_user_preview_info).GetPtrValue<std::vector<AdCreativePreview >>();
}

inline std::vector<AdPosInfo >* mutable_ad_pos_info_vec() {
  return Attr(CommonIdx::ad_pos_info_vec).GetMutablePtrValue<std::vector<AdPosInfo >>();
}
inline const std::vector<AdPosInfo >& get_ad_pos_info_vec() const {
  return *Attr(CommonIdx::ad_pos_info_vec).GetPtrValue<std::vector<AdPosInfo >>();
}

inline std::vector<BaseRetrievalHandler * >* mutable_retrieval_handler_list() {
  return Attr(CommonIdx::retrieval_handler_list).GetMutablePtrValue<std::vector<BaseRetrievalHandler * >>();
}
inline const std::vector<BaseRetrievalHandler * >& get_retrieval_handler_list() const {
  return *Attr(CommonIdx::retrieval_handler_list).GetPtrValue<std::vector<BaseRetrievalHandler * >>();
}

inline std::vector<FilterInfo >* mutable_filterInfos() {
  return Attr(CommonIdx::filterInfos).GetMutablePtrValue<std::vector<FilterInfo >>();
}
inline const std::vector<FilterInfo >& get_filterInfos() const {
  return *Attr(CommonIdx::filterInfos).GetPtrValue<std::vector<FilterInfo >>();
}

inline std::vector<int64_t >* mutable_preview_creative_set() {
  return Attr(CommonIdx::preview_creative_set).GetMutablePtrValue<std::vector<int64_t >>();
}
inline const std::vector<int64_t >& get_preview_creative_set() const {
  return *Attr(CommonIdx::preview_creative_set).GetPtrValue<std::vector<int64_t >>();
}

inline std::vector<RankAdCommon* >* mutable_unify_ad_list() {
  return Attr(CommonIdx::unify_ad_list).GetMutablePtrValue<std::vector<RankAdCommon* >>();
}
inline const std::vector<RankAdCommon* >& get_unify_ad_list() const {
  return *Attr(CommonIdx::unify_ad_list).GetPtrValue<std::vector<RankAdCommon* >>();
}

inline std::vector<kuaishou::ad::BigRExploreRecord>* mutable_big_r_explore_record_list() {
  return Attr(CommonIdx::big_r_explore_record_list).GetMutablePtrValue<std::vector<kuaishou::ad::BigRExploreRecord>>();
}
inline const std::vector<kuaishou::ad::BigRExploreRecord>& get_big_r_explore_record_list() const {
  return *Attr(CommonIdx::big_r_explore_record_list).GetPtrValue<std::vector<kuaishou::ad::BigRExploreRecord>>();
}

inline std::vector<int64_t >* mutable_request_inventory_pos_id() {
  return Attr(CommonIdx::request_inventory_pos_id).GetMutablePtrValue<std::vector<int64_t >>();
}
inline const std::vector<int64_t >& get_request_inventory_pos_id() const {
  return *Attr(CommonIdx::request_inventory_pos_id).GetPtrValue<std::vector<int64_t >>();
}

inline std::vector<kuaishou::ad::PerfInfo::NodeTimeCost >* mutable_node_time_cost() {
  return Attr(CommonIdx::node_time_cost).GetMutablePtrValue<std::vector<kuaishou::ad::PerfInfo::NodeTimeCost >>();
}
inline const std::vector<kuaishou::ad::PerfInfo::NodeTimeCost >& get_node_time_cost() const {
  return *Attr(CommonIdx::node_time_cost).GetPtrValue<std::vector<kuaishou::ad::PerfInfo::NodeTimeCost >>();
}

inline std::vector<LiveRerankInfo >* mutable_live_rerank_infos() {
  return Attr(CommonIdx::live_rerank_infos).GetMutablePtrValue<std::vector<LiveRerankInfo >>();
}
inline const std::vector<LiveRerankInfo >& get_live_rerank_infos() const {
  return *Attr(CommonIdx::live_rerank_infos).GetPtrValue<std::vector<LiveRerankInfo >>();
}

inline std::vector<std::string >* mutable_fanstop_author_browsed_info() {
  return Attr(CommonIdx::fanstop_author_browsed_info).GetMutablePtrValue<std::vector<std::string >>();
}
inline const std::vector<std::string >& get_fanstop_author_browsed_info() const {
  return *Attr(CommonIdx::fanstop_author_browsed_info).GetPtrValue<std::vector<std::string >>();
}

inline std::vector<std::string >* mutable_user_exp_results() {
  return Attr(CommonIdx::user_exp_results).GetMutablePtrValue<std::vector<std::string >>();
}
inline const std::vector<std::string >& get_user_exp_results() const {
  return *Attr(CommonIdx::user_exp_results).GetPtrValue<std::vector<std::string >>();
}

// strategy_manager just declare funcs, impl in cc.inl
void set_strategy_manager(StrategyManager * v);
StrategyManager * mutable_strategy_manager() const ;
const StrategyManager * get_strategy_manager() const;

inline UeqManager* mutable_ueq_manager() {
  return Attr(CommonIdx::ueq_manager).GetMutablePtrValue<UeqManager>();
}
inline const UeqManager& get_ueq_manager() const {
  return *Attr(CommonIdx::ueq_manager).GetPtrValue<UeqManager>();
}

inline void set_book_id(uint64_t v) {
  Attr(CommonIdx::book_id).SetIntValue(0, v, false, false);
}
inline uint64_t get_book_id() const {
  return Attr(CommonIdx::book_id).GetIntValue().value_or(0);
}

inline UniverseData* mutable_universe() {
  return Attr(CommonIdx::universe).GetMutablePtrValue<UniverseData>();
}
inline const UniverseData& get_universe() const {
  return *Attr(CommonIdx::universe).GetPtrValue<UniverseData>();
}

inline UniverseRequestMergeInfo* mutable_universe_request_merge_info() {
  return Attr(CommonIdx::universe_request_merge_info).GetMutablePtrValue<UniverseRequestMergeInfo>();
}
inline const UniverseRequestMergeInfo& get_universe_request_merge_info() const {
  return *Attr(CommonIdx::universe_request_merge_info).GetPtrValue<UniverseRequestMergeInfo>();
}

inline void set_ac_fetcher_type(const kuaishou::ad::AdTargetResponse_AcFetcherType v) {
  Attr(CommonIdx::ac_fetcher_type).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const kuaishou::ad::AdTargetResponse_AcFetcherType get_ac_fetcher_type() const {
  return static_cast<kuaishou::ad::AdTargetResponse_AcFetcherType>(Attr(CommonIdx::ac_fetcher_type).GetIntValue().value_or(static_cast<int64_t>(kuaishou::ad::AdTargetResponse::UNKONWN_FETCHER_TYPE)));
}

inline ks::spdm::Context* mutable_spdm_ctx() {
  return Attr(CommonIdx::spdm_ctx).GetMutablePtrValue<ks::spdm::Context>();
}
inline const ks::spdm::Context& get_spdm_ctx() const {
  return *Attr(CommonIdx::spdm_ctx).GetPtrValue<ks::spdm::Context>();
}

inline std::unordered_map<int64_t, std::pair<std::string,std::string>>* mutable_style_ai_description_map() {
  return Attr(CommonIdx::style_ai_description_map).GetMutablePtrValue<std::unordered_map<int64_t, std::pair<std::string,std::string> >>();
}
inline const std::unordered_map<int64_t, std::pair<std::string,std::string> >& get_style_ai_description_map() const {
  return *Attr(CommonIdx::style_ai_description_map).GetPtrValue<std::unordered_map<int64_t, std::pair<std::string,std::string> >>();
}

inline std::unordered_map<int64_t, std::pair<std::int64_t,std::string>>* mutable_style_ai_cover_map() {
  return Attr(CommonIdx::style_ai_cover_map).GetMutablePtrValue<std::unordered_map<int64_t, std::pair<std::int64_t,std::string> >>();
}
inline const std::unordered_map<int64_t, std::pair<std::int64_t,std::string> >& get_style_ai_cover_map() const {
  return *Attr(CommonIdx::style_ai_cover_map).GetPtrValue<std::unordered_map<int64_t, std::pair<std::int64_t,std::string> >>();
}

inline std::unordered_map<int64_t, std::string>* mutable_style_esp_title_map() {
  return Attr(CommonIdx::style_esp_title_map).GetMutablePtrValue<std::unordered_map<int64_t, std::string>>();
}
inline const std::unordered_map<int64_t, std::string>& get_style_esp_title_map() const {
  return *Attr(CommonIdx::style_esp_title_map).GetPtrValue<std::unordered_map<int64_t, std::string>>();
}

inline std::unordered_map<int64_t, std::tuple<int64_t, int64_t, AdEspProductLabelInfoParseFields::ProductLabel>>* mutable_style_esp_map() {
  return Attr(CommonIdx::style_esp_map).GetMutablePtrValue<std::unordered_map<int64_t, std::tuple<int64_t, int64_t, AdEspProductLabelInfoParseFields::ProductLabel>>>();
}

inline const std::unordered_map<int64_t, std::tuple<int64_t, int64_t, AdEspProductLabelInfoParseFields::ProductLabel>>& get_style_esp_map() const {
  return *Attr(CommonIdx::style_esp_map).GetPtrValue<std::unordered_map<int64_t, std::tuple<int64_t, int64_t, AdEspProductLabelInfoParseFields::ProductLabel>>>();
}

inline std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >* mutable_style_material_app_land_page_map() {
  return Attr(CommonIdx::style_material_app_land_page_map).GetMutablePtrValue<std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >>();
}
inline const std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >& get_style_material_app_land_page_map() const {
  return *Attr(CommonIdx::style_material_app_land_page_map).GetPtrValue<std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >>();
}

inline ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode* mutable_medium_valid_err_code() {
  return Attr(CommonIdx::medium_valid_err_code).GetMutablePtrValue<::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode>();
}
inline const ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode& get_medium_valid_err_code() const {
  return *Attr(CommonIdx::medium_valid_err_code).GetPtrValue<::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode>();
}

inline kuaishou::log::ad::AdTraceFilterCondition* mutable_inner_reason() {
  return Attr(CommonIdx::inner_reason).GetMutablePtrValue<kuaishou::log::ad::AdTraceFilterCondition>();
}
inline const kuaishou::log::ad::AdTraceFilterCondition& get_inner_reason() const {
  return *Attr(CommonIdx::inner_reason).GetPtrValue<kuaishou::log::ad::AdTraceFilterCondition>();
}

inline void set_search_mingtou_trigger_query_hash(int64_t v) {
  Attr(CommonIdx::search_mingtou_trigger_query_hash).SetIntValue(0, v, false, false);
}

inline int64_t get_search_mingtou_trigger_query_hash() const {
  return Attr(CommonIdx::search_mingtou_trigger_query_hash).GetIntValue().value_or(0);
}

inline void set_is_photo_rerank_req(bool v) {
  Attr(CommonIdx::is_photo_rerank_req).SetIntValue(0, v, false, false);
}

inline bool get_is_photo_rerank_req() const {
  return Attr(CommonIdx::is_photo_rerank_req).GetIntValue().value_or(false);
}

inline int64_t get_mix_bonus_fixed_ratio_tag() const {
  return Attr(CommonIdx::mix_bonus_fixed_ratio_tag).GetIntValue().value_or(-1);
}

inline void set_mix_bonus_fixed_ratio_tag(int64_t v) {
  Attr(CommonIdx::mix_bonus_fixed_ratio_tag).SetIntValue(0, v, false, false);
}

inline std::unordered_map<int64_t, int64_t>* mutable_mix_context_cpm_info_map() {
  return Attr(CommonIdx::mix_context_cpm_info_map).GetMutablePtrValue<std::unordered_map<int64_t, int64_t>>();
}
inline const std::unordered_map<int64_t, int64_t>& get_mix_context_cpm_info_map() const {
  return *Attr(CommonIdx::mix_context_cpm_info_map).GetPtrValue<std::unordered_map<int64_t, int64_t>>();
}

inline std::unordered_map<int64_t, int64_t>* mutable_mix_context_gpm_info_map() {
  return Attr(CommonIdx::mix_context_gpm_info_map).GetMutablePtrValue<std::unordered_map<int64_t, int64_t>>();
}
inline const std::unordered_map<int64_t, int64_t>& get_mix_context_gpm_info_map() const {
  return *Attr(CommonIdx::mix_context_gpm_info_map).GetPtrValue<std::unordered_map<int64_t, int64_t>>();
}

inline std::unordered_map<int64_t, std::string>* mutable_mix_context_set_item_info_map() {
  return Attr(CommonIdx::mix_context_set_item_info_map).GetMutablePtrValue<std::unordered_map<int64_t, std::string>>();
}
inline const std::unordered_map<int64_t, std::string>& get_mix_context_set_item_info_map() const {
  return *Attr(CommonIdx::mix_context_set_item_info_map).GetPtrValue<std::unordered_map<int64_t, std::string>>();
}

inline absl::flat_hash_set<int64_t>* mutable_mix_candidate_ad_id_list() {
  return Attr(CommonIdx::mix_candidate_ad_id_list).GetMutablePtrValue<absl::flat_hash_set<int64_t>>();
}
inline const absl::flat_hash_set<int64_t>& get_mix_candidate_ad_id_list() const {
  return *Attr(CommonIdx::mix_candidate_ad_id_list).GetPtrValue<absl::flat_hash_set<int64_t>>();
}

inline std::unordered_map<int64_t, int64_t>* mutable_mix_candidate_ad_map() {
  return Attr(CommonIdx::mix_candidate_ad_map).GetMutablePtrValue<std::unordered_map<int64_t, int64_t>>();
}
inline const std::unordered_map<int64_t, int64_t>& get_mix_candidate_ad_map() const {
  return *Attr(CommonIdx::mix_candidate_ad_map).GetPtrValue<std::unordered_map<int64_t, int64_t>>();
}


#define WRAP(...) __VA_ARGS__
#define PTR_COMMON_ATTR(type, name) \
inline type* mutable_##name() { \
  return Attr(CommonIdx::name).GetMutablePtrValue<type>(); \
} \
inline const type& get_##name() const { \
  return *Attr(CommonIdx::name).GetPtrValue<type>(); \
}
#define STRING_COMMON_ATTR(name) \
inline void set_##name(const std::string& val) { \
  Attr(CommonIdx::name).SetStringValue(0, std::string(val), false, false); \
} \
inline std::string get_##name() const { \
  return std::string(Attr(CommonIdx::name).GetStringValue().value_or("")); \
}
#define BOOL_COMMON_ATTR(name) \
inline void set_##name(bool v) { \
  Attr(CommonIdx::name).SetIntValue(0, v ? 1 : 0, false, false); \
} \
inline bool get_##name() const { \
  return Attr(CommonIdx::name).GetIntValue().value_or(0) == 0 ? false : true; \
}
#define INT_COMMON_ATTR(name) \
inline void set_##name(int64_t v) { \
  Attr(CommonIdx::name).SetIntValue(0, v, false, false); \
} \
inline int64_t get_##name() const { \
  return Attr(CommonIdx::name).GetIntValue().value_or(0); \
}
#define ENUM_COMMON_ATTR(type, name) \
inline void set_##name(type v) { \
  Attr(CommonIdx::name).SetIntValue(0, static_cast<int64_t>(v), false, false); \
} \
inline type get_##name() const { \
  return static_cast<type>(Attr(CommonIdx::name).GetIntValue().value_or(0)); \
}

PTR_COMMON_ATTR(WRAP(std::unordered_map<std::string, int64_t>), time_record_map)

STRING_COMMON_ATTR(rank_migration_tag)

BOOL_COMMON_ATTR(request_success_target_default)
BOOL_COMMON_ATTR(request_success_target_live)
BOOL_COMMON_ATTR(request_success_target_inner_photo)
BOOL_COMMON_ATTR(request_success_ad_rank)
BOOL_COMMON_ATTR(request_success_ad_server)
BOOL_COMMON_ATTR(disable_mix_rank_flow_price_bound_in_front)
BOOL_COMMON_ATTR(is_one_model_nearline_flow)

INT_COMMON_ATTR(request_status_target_default)
INT_COMMON_ATTR(request_status_target_live)
INT_COMMON_ATTR(request_status_target_inner_photo)
INT_COMMON_ATTR(request_status_ad_rank)
INT_COMMON_ATTR(request_status_ad_server)
INT_COMMON_ATTR(request_ad_server_time_cost)
INT_COMMON_ATTR(request_ad_rank_time_cost)
INT_COMMON_ATTR(request_target_default_time_cost)
INT_COMMON_ATTR(request_target_live_time_cost)
INT_COMMON_ATTR(request_target_inner_photo_time_cost)
INT_COMMON_ATTR(enable_prerank_rank_ica)
INT_COMMON_ATTR(selected_rank_infos_inner_loop_empty)
INT_COMMON_ATTR(is_hit_cache_info)
INT_COMMON_ATTR(time_left_ms)

ENUM_COMMON_ATTR(kuaishou::ad::AdEnum_AdRequestFlowType, ad_request_flow_type)

#undef PTR_COMMON_ATTR
#undef WRAP
