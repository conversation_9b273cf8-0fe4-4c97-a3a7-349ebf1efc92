#include "teams/ad/front_server/engine/node/splash/splash_style_postproc.h"

#include <algorithm>
#include <cctype>
#include <climits>
#include <map>
#include <set>
#include <limits>

#include "absl/strings/substitute.h"
#include "absl/time/time.h"
#include "absl/strings/numbers.h"
#include "base/strings/string_printf.h"
#include "base/encoding/url_encode.h"
#include "base/hash_function/city.h"
#include "base/strings/string_number_conversions.h"
#include "base/time/timestamp.h"
#include "glog/logging.h"
#include "google/protobuf/util/json_util.h"
#include "google/protobuf/util/message_differencer.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "infra/utility/src/utility/aes_crypter.h"
#include "perfutil/perfutil.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/region/region_dict.h"
#include "serving_base/crypt/aes_crypter.h"
#include "serving_base/util/urlsafe_base64.h"
#include "teams/ad/ad_base/src/common/app_version_compare.h"
#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/ad_base/src/container/stl_helper.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_base/src/geohash/location.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/pb_helper/pb_helper.h"
#include "teams/ad/ad_base/src/redis/redis_util.h"
#include "teams/ad/ad_base/src/trace_filter/trace_filter.h"
#include "teams/ad/ad_base/src/util/check_reco_request_type.h"
#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/ad_base/src/log_record/util.h"

#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"

#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/front_server/bg_task/cdn_dispatch/cdn_dispatch_manager.h"
#include "teams/ad/front_server/engine/strategy/style_postproc/common_style_postproc_strategy.h"
#include "teams/ad/front_server/engine/strategy/style_postproc/merchant_style_strategy.h"
#include "teams/ad/front_server/engine/utils/ad_pack/base_info_pack/common_base_info_pack.h"
#include "teams/ad/front_server/engine/utils/merchant/merchant_logic.h"
#include "teams/ad/front_server/engine/utils/target_check.h"
#include "teams/ad/front_server/util/creative_parser/creative_parser.h"
#include "teams/ad/front_server/util/kconf/kconf.h"
#include "teams/ad/front_server/util/spdm/spdm_switches.h"
#include "teams/ad/front_server/util/track/tracking.h"
#include "teams/ad/front_server/util/utility/front_logging.h"
#include "teams/ad/front_server/util/utility/utils.h"
#include "teams/ad/front_server/util/utility/ipv6_util.h"
namespace ks {
namespace front_server {
namespace {
int64_t key_convertor_splash_style_post(const std::string &str) {
  uint64_t uint64_sign = base::CityHash64(str.data(), str.size());
  return *(reinterpret_cast<int64_t*>(&uint64_sign));
}

}   // namespace

void SplashStylePostProc::Initialize(ContextData *context) {
  session_data_ = context;
  is_ios_platform_ = (session_data_->get_ad_request()->ad_user_info().platform() == "ios");
  app_version_ = session_data_->get_ad_request()->ad_user_info().platform_version();
  StrategyManager* p_strategy_manager = context_->GetContextSystem<StrategyManager>();
  local_llsid = session_data_->get_llsid();
  common_style_post_strategy_ =
      p_strategy_manager->GetStrategy<CommonStylePostStrategy>(AdStrategyType::CommonStylePostStrategy);
  merchant_style_strategy_ =
      p_strategy_manager->GetStrategy<MerchantStyleStrategy>(AdStrategyType::MerchantStyle);
  light_interactive_kconf_ = FrontKconfUtil::splashLightInteractiveConfig();
  splash_adx_cache_hour_ = FrontKconfUtil::splashAdxCacheHour();
  pdd_win_notice_url_helper_ = std::make_unique<PddWinNoticeUrlHelper>();
  enable_splash_cid_replace_schema_ = SPDM_enableSplashCidReplaceSchema();
  splash_pic_show_time_ = SPDM_splash_pic_show_time(session_data_->get_spdm_ctx());
  deliver_packer_params_.Initialize(session_data_);
  enable_jk_track_in_splash_ =
      FrontKconfUtil::enableRtbJkMonitor() && SPDM_enable_jk_track_in_splash(session_data_->get_spdm_ctx()) &&
      ks::engine_base::CompareAppVersion(app_version_, *FrontKconfUtil::minSplashRtbJKVersion()) >= 0;
}

void SplashStylePostProc::Clear() {
  session_data_ = nullptr;
  splash_pic_show_time_ = 4;
}

bool SplashStylePostProc::ProcessInner() {
  TimeRecorder timer(context_->GetMutableContextData<ContextData>(),
    static_cast<int32_t>(kuaishou::ad::AdFrontNodeType::FRONT_NODE_STYLE_POSTPROC_TYPE));
  ContextData *context = context_->GetMutableContextData<ContextData>();
  if (context == nullptr) {
    TLOG(ERROR) << "style proc failed, context is null";
    return false;
  }
  Initialize(context);
  if (KS_UNLIKELY(!session_data_->get_need_splash_effect())) {
    return true;
  }

  // 添充公共数据
  common_style_post_strategy_->FillCommonResult();

  // 填充单条广告
  session_data_->mutable_ad_list()->GetAllWithFunc([this](kuaishou::ad::AdResult* ad_result,
                                                          RankAdCommon* ad_common) {
    if (!session_data_->get_ad_request()->universe_ad_request_info().splash_req_info().is_realtime()) {
      if (ad_result->ad_source_type() == kuaishou::ad::ADX) {
        ProcessAdxPrefetchAdResult(ad_result);
      } else {
        ProcessPrefetchAdResult(ad_result);
      }
    } else {
      if (ad_result->ad_source_type() == kuaishou::ad::ADX) {
        ProcessAdxRealtimeAdResult(ad_result);
      } else {
        if (session_data_->get_enable_splash_rtb_realtime_recall() && ad_result->ad_deliver_info()
                                                                          .online_join_params_transparent()
                                                                          .ad_target_trans_info()
                                                                          .is_realtime_recall_ad()) {
          ProcessPrefetchAdResult(ad_result, true);
        } else {
          ProcessRealtimeAdResult(ad_result);
        }
      }
    }
  });

  PERF_SPLASH_FINAL_AD_LIST(session_data_);
  Clear();
  return true;
}

bool SplashStylePostProc::SelectMaterialInfo(const StyleInfoItem& style_info,
    AdResult* item, SplashMaterialInfo* splash_material_info) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  std::string material_str = style_info.creative().creative_support_info().splash_photos();
  if (base_info->creative_material_type() ==
      kuaishou::ad::AdEnum_CreativeMaterialType_SPLASH_IMAGES) {
    material_str = style_info.creative().creative_support_info().splash_pictures();
  }
  double ratio = 16.0 / 9.0;
  int64_t height = 0;
  int64_t width = 0;
  if (session_data_->get_ad_request()->has_universe_ad_request_info() &&
      session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() > 0) {
    height = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).height();
    width = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).width();
    if (height > 0 && width > 0) {
      ratio = static_cast<double>(height) / width;
    }
  }
  base::Json material_json(StringToJson(material_str));
  if (!material_json.IsArray()) {
    LOG(INFO) << "material_str is not json, " << material_str;
    return false;
  }
  bool has_selected = false;
  bool select_cur_item = false;
  double global_ratio_diff = std::numeric_limits<double>::max();
  int32_t global_width_diff = std::numeric_limits<int>::max();
  for (auto* item : material_json.array()) {
    select_cur_item = false;
    if (!item->IsObject()) {
      continue;
    }
    int64_t cover_height = item->GetInt("height", 0);
    int64_t cover_width = item->GetInt("width", 0);
    if (cover_height <= 0 || cover_width <= 0) {
      continue;
    }
    double cover_ratio = static_cast<double>(cover_height) / cover_width;
    double abs_diff = std::fabs(cover_ratio - ratio);
    // 比例相同时，选宽差距最小的
    if (std::fabs(abs_diff - global_ratio_diff) < 0.0001) {
      int32_t width_diff = std::abs(cover_width - width);
      if (width_diff < global_width_diff) {
        global_width_diff = width_diff;
        has_selected = true;
        select_cur_item = true;
      }
    } else if (abs_diff < global_ratio_diff) {
      global_ratio_diff = abs_diff;
      global_width_diff = std::abs(cover_width - width);
      has_selected = true;
      select_cur_item = true;
    }
    if (select_cur_item) {
      splash_material_info->photo_id = item->GetInt("photoId", 0);
      splash_material_info->dup_photo_id = item->GetInt("dupPhotoId", 0);
      splash_material_info->cover_id = item->GetInt("coverId", 0);
      splash_material_info->pic_id = item->GetInt("picId", 0);
      splash_material_info->dup_cover_id = item->GetInt("dupCoverId", 0);
      splash_material_info->cover_url = item->GetString("coverUrl", "");
      splash_material_info->height = cover_height;
      splash_material_info->width = cover_width;
    }
  }
  return true;
}

bool SplashStylePostProc::SelectMaterialInfoGray(const StyleInfoItem& style_info,
    AdResult* item, SplashMaterialInfo* splash_material_info) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  int64_t creative_id = style_info.creative().id();
  if (session_data_->get_rtb_style_info_map().count(creative_id) <= 0) {
    LOG(WARNING) << "creative id has no style info, " << creative_id;
    return false;
  }
  const auto& style_material = session_data_->get_rtb_style_info_map().at(creative_id);
  double ratio = 16.0 / 9.0;
  int64_t height = 0;
  int64_t width = 0;
  if (session_data_->get_ad_request()->has_universe_ad_request_info() &&
      session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() > 0) {
    height = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).height();
    width = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).width();
    if (height > 0 && width > 0) {
      ratio = static_cast<double>(height) / width;
    }
  }
  base::Json style_json(StringToJson(style_material.style_content()));
  if (!style_json.IsObject()) {
    LOG(INFO) << "style_content is not json, " << style_material.style_content();
    return false;
  }
  auto material_json = style_json.Get("splashPhotos");
  if (!material_json || !material_json->IsArray()) {
    LOG_EVERY_N(INFO, 10000) << "style_content is not json, " << style_material.style_content();
    return false;
  }
  bool has_selected = false;
  bool select_cur_item = false;
  double global_ratio_diff = std::numeric_limits<double>::max();
  int32_t global_width_diff = std::numeric_limits<int>::max();
  for (auto* item : material_json->array()) {
    select_cur_item = false;
    if (!item->IsObject()) {
      continue;
    }
    int64_t cover_height = item->GetInt("height", 0);
    int64_t cover_width = item->GetInt("width", 0);
    if (cover_height <= 0 || cover_width <= 0) {
      continue;
    }
    double cover_ratio = static_cast<double>(cover_height) / cover_width;
    double abs_diff = std::fabs(cover_ratio - ratio);
    // 比例相同时，选宽差距最小的
    if (std::fabs(abs_diff - global_ratio_diff) < 0.0001) {
      int32_t width_diff = std::abs(cover_width - width);
      if (width_diff < global_width_diff) {
        global_width_diff = width_diff;
        has_selected = true;
        select_cur_item = true;
      }
    } else if (abs_diff < global_ratio_diff) {
      global_ratio_diff = abs_diff;
      global_width_diff = std::abs(cover_width - width);
      has_selected = true;
      select_cur_item = true;
    }
    if (select_cur_item) {
      splash_material_info->photo_id = item->GetInt("photoId", 0);
      splash_material_info->dup_photo_id = item->GetInt("dupPhotoId", 0);
      splash_material_info->height = cover_height;
      splash_material_info->width = cover_width;
    }
  }
  kuaishou::ad::StyleData sd;
  sd.set_style_type(style_material.style_type());
  sd.set_resource_type(style_material.resource_type());
  sd.set_style_material_id(style_material.id());
  (*session_data_->mutable_style_data_for_online_join())[creative_id].push_back(sd);
  return true;
}

void SplashStylePostProc::SetMaterialInfo(const StyleInfoItem& style_info, AdResult* item,
    const SplashMaterialInfo& splash_material_info) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto p_material_info = base_info->mutable_material_info();
  p_material_info->set_material_type(
      kuaishou::ad::AdEnum::AdMaterialType::AdEnum_AdMaterialType_UNKONWUN_MATERIAL_TYPE);
  auto feature = p_material_info->add_material_feature();
  feature->mutable_material_size()->set_height(splash_material_info.height);
  feature->mutable_material_size()->set_width(splash_material_info.width);
  feature->set_material_url(splash_material_info.cover_url);
  if (splash_material_info.photo_id != 0) {
    feature->set_photo_id(splash_material_info.photo_id);
    feature->set_material_feature_type(
        kuaishou::ad::AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE);
  } else {
    feature->set_material_feature_type(
        kuaishou::ad::AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE);
  }
  auto cdn_dispatch_manager_ptr = ks::front_server::CdnDispatchManager::GetInstance();
  std::string cdn_dispatch_material_url = splash_material_info.cover_url;
  if (cdn_dispatch_manager_ptr) {
    cdn_dispatch_manager_ptr->DispatchStatic(session_data_->get_user_id(), splash_material_info.cover_url,
                                             &cdn_dispatch_material_url);
  }
  feature->set_material_url(cdn_dispatch_material_url);
  if (splash_material_info.photo_id != 0) {
    base_info->set_photo_id(splash_material_info.photo_id);
  }
  if (splash_material_info.cover_id != 0) {
    base_info->set_cover_id(splash_material_info.cover_id);
  }
  if (splash_material_info.pic_id != 0) {
    base_info->set_pic_id(splash_material_info.pic_id);
    falcon::Inc("frontserver.splash_pic_id_not_zero");
  }

  return;
}

void SplashStylePostProc::SetMaterialInfoGray(const StyleInfoItem& style_info, AdResult* item,
    const SplashMaterialInfo& splash_material_info) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto p_material_info = base_info->mutable_material_info();
  p_material_info->set_material_type(
      kuaishou::ad::AdEnum::AdMaterialType::AdEnum_AdMaterialType_UNKONWUN_MATERIAL_TYPE);
  auto feature = p_material_info->add_material_feature();
  feature->mutable_material_size()->set_height(splash_material_info.height);
  feature->mutable_material_size()->set_width(splash_material_info.width);
  if (splash_material_info.photo_id != 0) {
    feature->set_photo_id(splash_material_info.photo_id);
    feature->set_material_feature_type(
        kuaishou::ad::AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE);
    base_info->set_photo_id(splash_material_info.photo_id);
  }
  return;
}

void SplashStylePostProc::SetRtbEyemaxInfo(const StyleInfoItem& style_info) {
  Json display_info(base::StringToJson(style_info.creative().display_info()));
  std::string action_bar_content;
  std::string action_bar_color;
  action_bar_color = *FrontKconfUtil::defaultActionBarColor();
  if (display_info.IsObject()) {
    action_bar_content = display_info.GetString("actionBar", "");
    action_bar_color = display_info.GetString("actionbarColor", *FrontKconfUtil::defaultActionBarColor());
  }
  ad_data_v2_.mutable_origin_style_info()->set_action_bar_bg_color(action_bar_color);
  auto *p_action_bar_info = ad_data_v2_.mutable_action_bar_info();
  p_action_bar_info->set_action_bar_load_time(1);
  p_action_bar_info->set_real_show_delay_time(3000);
  p_action_bar_info->set_downloaded_action_bar_load_time(1000);
  p_action_bar_info->set_action_bar_style(1);
  p_action_bar_info->set_action_bar_color(action_bar_color);
  p_action_bar_info->set_display_info(action_bar_content);
  // 填充落地页样式信息
  auto *p_landing_page_info = ad_data_v2_.mutable_landing_page_info();
  p_landing_page_info->set_comment_tag_visible(true);
  // 填充 comment actionbar 信息
  auto *p_comment_actionbar_info = ad_data_v2_.mutable_comment_action_bar_info();
  p_comment_actionbar_info->set_action_bar_style(2);
  p_comment_actionbar_info->set_action_bar_color(action_bar_color);
  p_comment_actionbar_info->set_display_info(action_bar_content);
  p_comment_actionbar_info->set_action_bar_location(2);
  // 填充播放页结束信息
  auto *p_play_end_info = ad_data_v2_.mutable_play_end_info();
  p_play_end_info->set_show_end_option(true);
  p_play_end_info->set_play_end_style(0);
  // 填充广告页面权限控制信息
  ad_data_v2_.set_adpagebuttoncontrol(0);
}

void SplashStylePostProc::SetPrefetchSplashInfo(const StyleInfoItem& style_info, AdResult* item,
    const SplashMaterialInfo& splash_material_info) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto splash_info = base_info->mutable_splash_info();
  auto splash_base_info = splash_info->mutable_base_info();
  int64_t begin_time = style_info.unit().begin_time() / 1000;
  int64_t end_time = style_info.unit().end_time() / 1000;
  int64_t now_s = base::GetTimestamp() / 1000000;
  int64_t max_splash_cache_hour =
    session_data_->get_spdm_ctx().TryGetInteger("max_splash_cache_hour", 24);
  if (base_info->rta_source_type() != kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    std::string rta_source_name = kuaishou::ad::RtaSourceType_Name(base_info->rta_source_type());
    if (FrontKconfUtil::splashRtaCacheTime()->count(rta_source_name) > 0) {
      max_splash_cache_hour = FrontKconfUtil::splashRtaCacheTime()->at(rta_source_name);
    }
  }
  std::string product_name = style_info.account().product_name();
  if (FrontKconfUtil::splashProductCacheTime()->count(product_name) > 0) {
    max_splash_cache_hour = FrontKconfUtil::splashProductCacheTime()->at(product_name);
  }
  std::string account_id_str = std::to_string(style_info.account().id());
  if (FrontKconfUtil::splashAccountCacheTime()->count(account_id_str) > 0) {
    max_splash_cache_hour = FrontKconfUtil::splashAccountCacheTime()->at(account_id_str);
  }
  if (IsRtbGray(style_info)) {
    max_splash_cache_hour = FrontKconfUtil::splashGrayCacheTime();
  }


  if (end_time == 0 ||
      end_time > std::max(begin_time, now_s) + max_splash_cache_hour * 3600) {
    end_time = std::max(begin_time, now_s) + max_splash_cache_hour * 3600;
  }
  splash_base_info->set_start_time(begin_time);  // 单位秒
  splash_base_info->set_end_time(end_time);  // 单位秒
  splash_base_info->set_splash_id(std::to_string(base_info->creative_id()));

  // 下发信息-基本信息-开屏展示信息-素材信息
  if (base_info->creative_material_type() ==
      kuaishou::ad::AdEnum_CreativeMaterialType_SPLASH_IMAGES) {
    splash_info->set_splash_ad_material_type(2);
  } else {
    splash_info->set_splash_ad_material_type(1);
  }
  splash_info->set_material_width(splash_material_info.width);
  splash_info->set_material_height(splash_material_info.height);
  splash_info->set_photo_id(std::to_string(splash_material_info.photo_id));
  splash_info->set_splash_ad_display_style(2);  // 全部为沉浸式

  // cdn 动态域名调整
  auto cdn_dispatch_manager_ptr = ks::front_server::CdnDispatchManager::GetInstance();
  std::string cdn_dispatch_material_url = splash_material_info.cover_url;
  if (cdn_dispatch_manager_ptr) {
    cdn_dispatch_manager_ptr->DispatchStatic(session_data_->get_user_id(), splash_material_info.cover_url,
                                             &cdn_dispatch_material_url);
  }
  if (!cdn_dispatch_material_url.empty()) {
    splash_info->add_image_urls(cdn_dispatch_material_url);
  }

  if (base_info->creative_material_type() !=
      kuaishou::ad::AdEnum_CreativeMaterialType_SPLASH_IMAGES) {  // 开屏时长
    splash_info->set_skip_tag_show_time(0);
    splash_info->set_splash_ad_duration(5);
  } else {
    splash_info->set_skip_tag_show_time(0);
    splash_info->set_splash_ad_duration(splash_pic_show_time_);
  }
  splash_info->set_splash_ad_type(2);  // 非 topview
  splash_info->set_splash_touch_control("000000");  // 开屏手势控制策略
  if ((session_data_->get_ad_request()->ad_user_info().platform().compare("android") == 0 ||
      session_data_->get_ad_request()->ad_user_info().platform().compare("harmony") == 0)) {
    splash_info->set_splash_touch_control("0000001");  // 开屏手势控制策略
  }
  splash_info->set_audio_button_visible(false);
  splash_info->set_splash_show_control(0);
  splash_info->set_enable_4g_cache(true);
  splash_info->set_force_display_normal_splash_for_eyemax(false);
  if (SPDM_enableRTBEyemax() && IsRtbEyemax(style_info)) {
    splash_info->set_splash_ad_type(1);  // eyemax
    splash_info->set_preload_dur_ms(FrontKconfUtil::preloadDurMs());
    splash_info->set_is_eyemax(true);
  }
  // 点击按钮
  SetClickButtonInfo(splash_info);
  // 设置轻互动样式
  SetLightInteractive(splash_info, base_info);
  // ad data v2 填充
  ad_data_v2_.Clear();
  ad_data_v2_.mutable_splash_info()->CopyFrom(*splash_info);
  // eyemax 样式
  if (SPDM_enableRTBEyemax() && IsRtbEyemax(style_info)) {
    SetRtbEyemaxInfo(style_info);
  }
  if (base_info->splash_interactive_style() > 0) {
    ad_data_v2_.set_unit_style_type(4);  // LIGHT_INTERACTIVE 轻互动
  }
  if ((!(style_info.creative().download_page_url().empty())) &&
      style_info.campaign().type() == kuaishou::ad::AdEnum::APP) {
    ad_data_v2_.set_h5_url(style_info.creative().download_page_url());
    falcon::Inc("frontserver.enable_splash_h5_url");
  }
  if (style_info.campaign().type() == kuaishou::ad::AdEnum::APP &&
      (session_data_->get_ad_request()->ad_user_info().platform().compare("android") == 0 ||
        session_data_->get_ad_request()->ad_user_info().platform().compare("harmony") == 0) &&
      ad_data_v2_.h5_url().empty()) {
    ad_data_v2_.set_h5_url(*FrontKconfUtil::kuaishouDefaultH5Url());
  }
  if (style_info.campaign().type() == kuaishou::ad::AdEnum::APP ||
      style_info.campaign().type() == kuaishou::ad::AdEnum::APP_ADVANCE) {
    const auto& campaign_type_str = kuaishou::ad::AdEnum_CampaignType_Name(
        static_cast<kuaishou::ad::AdEnum_CampaignType>(style_info.campaign().type()));
    auto* origin_style_info = ad_data_v2_.mutable_origin_style_info();
    origin_style_info->set_app_name(style_info.app_release().app_name());
  }

  if (SPDM_enableBrandHeaderUA()) {
    ad_data_v2_.set_new_user_agent_style(1);
  }

  auto get_action_bar_text = [](const std::string& config, const std::string& name) -> std::string {
    if (config.empty()) {
      return std::string{};
    }
    base::Json display_config(base::StringToJson(config));
    if (display_config.IsObject()) {
      return display_config.GetString(name, "");
    }
    return std::string{};
  };
  ad_data_v2_.mutable_origin_style_info()->set_action_bar_text(
      get_action_bar_text(style_info.creative().display_info(), "actionBar"));
  ad_data_v2_.mutable_origin_style_info()->set_action_bar_desc(
      get_action_bar_text(style_info.creative().display_info(), "description"));
  std::string ad_data_v2_json_str;
  ::google::protobuf::util::MessageToJsonString(ad_data_v2_, &ad_data_v2_json_str);
  base_info->set_ad_data_v2(ad_data_v2_json_str);
  session_data_->set_curr_ad_data_v2(&ad_data_v2_);
}

void SplashStylePostProc::SetLightInteractiveRealtime(const StyleInfoItem& style_info,
                                                      kuaishou::ad::AdBaseInfo* base_info) {
  if (!light_interactive_kconf_.get()) {
    return;
  }
  auto& light_interactive_style = light_interactive_kconf_->data().splash_light_interactive_style();
  std::vector<int32_t> interactive_styles;
  for (const auto& item : light_interactive_style) {
    if (!(::kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_IsValid(
          item.interactive_style()))) {
      continue;
    }
    if (session_data_->get_ad_request()->ad_user_info().platform().compare("ios") == 0) {
      if (engine_base::CompareAppVersion(session_data_->get_ad_request()->ad_user_info().platform_version(),
          item.min_ios_version()) < 0) {
        continue;
      }
    } else {
      if (engine_base::CompareAppVersion(session_data_->get_ad_request()->ad_user_info().platform_version(),
          item.min_android_version()) < 0) {
        continue;
      }
    }
    if (!session_data_->get_is_support_gyroscope() && item.interactive_style() != 2 &&
        item.interactive_style() != 3) {
      continue;
    }
    interactive_styles.emplace_back(item.interactive_style());
  }
  if (interactive_styles.size() <= 0) {
    return;
  }
  int32_t select_interactive_style = 0;  // 兜底不下发轻互动
  // 不支持陀螺仪
  if (!session_data_->get_is_support_gyroscope()) {
    select_interactive_style = session_data_->get_spdm_ctx().TryGetInteger(
        "splash_rtb_no_gyroscope_interactive_style", 2);
  } else {
    // ios 和 android 不同的参数

    if (session_data_->get_ad_request()->ad_user_info().platform().compare("ios") == 0) {
      select_interactive_style = session_data_->get_spdm_ctx().TryGetInteger(
          "splash_rtb_gyroscope_ios_interactive_style", 2);
    } else {
      select_interactive_style = session_data_->get_spdm_ctx().TryGetInteger(
          "splash_rtb_gyroscope_android_interactive_style", 1);
    }
  }
  auto interactive_style_unit_white = FrontKconfUtil::interactiveStyleUnitWhiteMap();
  if (interactive_style_unit_white.get()->count(std::to_string(style_info.creative().unit_id())) > 0) {
    select_interactive_style =
        interactive_style_unit_white.get()->at(std::to_string(style_info.creative().unit_id()));
  }
  bool splash_rtb_light = false;
  for (const auto& style : interactive_styles) {
    if (style == select_interactive_style) {
      splash_rtb_light = true;
      break;
    }
  }
  if (!splash_rtb_light) {
    return;
  }
  // 埋点
  for (const auto& item : light_interactive_style) {
    if (item.interactive_style() == select_interactive_style) {
      session_data_->dot_perf->Count(1, "splash_rtb_interactive_style",
          std::to_string(select_interactive_style), "realtime");
      base_info->set_splash_interactive_style(item.interactive_style());
      break;
    }
  }
}

void SplashStylePostProc::SetLightInteractive(::kuaishou::ad::AdDataV2::SplashInfo *splash_info,
    kuaishou::ad::AdBaseInfo* base_info) {
  if (!light_interactive_kconf_.get()) {
    return;
  }
  auto& light_interactive_style = light_interactive_kconf_->data().splash_light_interactive_style();
  std::vector<int32_t> interactive_styles;
  for (const auto& item : light_interactive_style) {
    if (!(::kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_IsValid(
          item.interactive_style()))) {
      continue;
    }
    if (session_data_->get_ad_request()->ad_user_info().platform().compare("ios") == 0) {
      if (engine_base::CompareAppVersion(session_data_->get_ad_request()->ad_user_info().platform_version(),
          item.min_ios_version()) < 0) {
        continue;
      }
    } else {
      if (engine_base::CompareAppVersion(session_data_->get_ad_request()->ad_user_info().platform_version(),
          item.min_android_version()) < 0) {
        continue;
      }
    }
    if (!session_data_->get_is_support_gyroscope() && item.interactive_style() != 2 &&
        item.interactive_style() != 3) {
      continue;
    }
    interactive_styles.emplace_back(item.interactive_style());
  }
  if (interactive_styles.size() <= 0) {
    return;
  }
  int32_t select_interactive_style = 0;  // 兜底不下发轻互动
  // 不支持陀螺仪
  if (!session_data_->get_is_support_gyroscope()) {
    select_interactive_style = session_data_->get_spdm_ctx().TryGetInteger(
        "splash_rtb_no_gyroscope_interactive_style", 2);
  } else {
    // ios 和 android 不同的参数
    if (session_data_->get_ad_request()->ad_user_info().platform().compare("ios") == 0) {
      select_interactive_style = session_data_->get_spdm_ctx().TryGetInteger(
          "splash_rtb_gyroscope_ios_interactive_style", 2);
    } else {
      select_interactive_style = session_data_->get_spdm_ctx().TryGetInteger(
          "splash_rtb_gyroscope_android_interactive_style", 1);
    }
  }
  auto interactive_style_unit_white = FrontKconfUtil::interactiveStyleUnitWhiteMap();
  if (interactive_style_unit_white.get()->count(std::to_string(base_info->unit_id())) > 0) {
    select_interactive_style =
        interactive_style_unit_white.get()->at(std::to_string(base_info->unit_id()));
  }
  bool splash_rtb_light = false;
  for (const auto& style : interactive_styles) {
    if (style == select_interactive_style) {
      splash_rtb_light = true;
      break;
    }
  }
  if (!splash_rtb_light) {
    return;
  }
  for (const auto& item : light_interactive_style) {
    if (item.interactive_style() == select_interactive_style) {
      splash_info->mutable_interaction_info()->set_interactive_style(
          static_cast<::kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle>(
          item.interactive_style()));
      if (item.interactive_style() == 2) {
        splash_info->mutable_interaction_info()->mutable_shake_info()->set_component_index(
            item.interactive_component());
      } else if (item.interactive_style() == 3) {
        splash_info->mutable_interaction_info()->mutable_slide_info()->set_component_index(
            item.interactive_component());
      } else {
        splash_info->mutable_interaction_info()->mutable_rotate_info()->set_rotate_type(
            item.interactive_component());
        splash_info->mutable_interaction_info()->mutable_rotate_info()->set_component_index(
            item.interactive_component());
      }
      session_data_->dot_perf->Count(1, "splash_rtb_interactive_style",
          std::to_string(select_interactive_style), "prefetch");
      base_info->set_splash_interactive_style(item.interactive_style());
      break;
    }
  }
}

void SplashStylePostProc::SetClickButtonInfo(::kuaishou::ad::AdDataV2::SplashInfo *splash_info) {
  auto click_button_info_kconf = FrontKconfUtil::clickButtonInfo();
  const auto click_button_info = click_button_info_kconf->data();
  splash_info->mutable_click_button_info()->set_button_width(click_button_info.button_width());
  splash_info->mutable_click_button_info()->set_button_height(click_button_info.button_height());
  splash_info->mutable_click_button_info()->set_button_bottom_margin(
    click_button_info.button_bottom_margin());
  splash_info->mutable_click_button_info()->set_button_title(click_button_info.button_title());
  splash_info->mutable_click_button_info()->set_button_corner_radius(
    click_button_info.button_corner_radius());
}

void SplashStylePostProc::AddBackUrlToSchemaUrl(kuaishou::ad::AdResult* item) {
  if (IsDeepLinkWhiteAccount(item->ad_deliver_info().ad_base_info().account_id())) {
    return;
  }
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  static const std::string back_url = "backURL=kwai://action/bringToFront";
  static const std::string back_url_encode = "backURL=kwai%3a%2f%2faction%2fbringToFront";
  const std::string &applink = base_info->schema_url();
  if (!FrontKconfUtil::enableAddBackUrl() || applink.empty()) {
    return;
  }
  if (ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())) == "kuaishou_nebula") {
    return;
  }
  if (item->ad_source_type() == kuaishou::ad::BRAND && applink.find("backURL") != std::string::npos) {
    return;
  }
  auto disableApplinkBackUrlDeepLinkPrefixs = FrontKconfUtil::disableApplinkBackUrlDeepLinkPrefix();
  if (disableApplinkBackUrlDeepLinkPrefixs != nullptr) {
    for (auto it = disableApplinkBackUrlDeepLinkPrefixs->begin();
        it != disableApplinkBackUrlDeepLinkPrefixs->end(); ++it) {
      const std::string& prefix = *it;
      if (prefix.empty()) {
        continue;
      }
      if (absl::StartsWith(applink, prefix)) {
        return;
      }
    }
  }
  auto disableApplinkBackUrlAccountList = FrontKconfUtil::disableApplinkBackUrlAccountList();
  if (disableApplinkBackUrlAccountList != NULL
    && disableApplinkBackUrlAccountList->find(base_info->account_id())
       != disableApplinkBackUrlAccountList->end()) {
    return;
  }

  std::string applink_decoded;
  if (!base::DecodeUrlComponent(applink.c_str(), &applink_decoded)) {
    TLOG_EVERY_N(ERROR, 1) << "decoded applink url error, url: " << applink;
    return;
  }
  if (std::string::npos == applink_decoded.find(back_url)) {
    std::string join_str("");
    if (std::string::npos != applink_decoded.find("?")) {
      join_str = "&";
    } else {
      join_str = "?";
    }
    std::string new_applink = base::StringPrintf("%s%s%s", applink.c_str(),
        join_str.c_str(), back_url_encode.c_str());
    base_info->set_schema_url(new_applink);
  }
}

void SplashStylePostProc::ReplaceSchemaUrlMacro(kuaishou::ad::AdResult* item) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  std::string schema_url = base_info->schema_url();
  absl::flat_hash_map<std::string, std::string> replace_macro_map;
  if (enable_splash_cid_replace_schema_) {
    replace_macro_map.emplace(kCIDKeyWords, std::to_string(base_info->creative_id()));
  }
  replace_macro_map.insert(std::make_pair(kSplashCIDKeyWords, std::to_string(base_info->creative_id())));
  ReplaceUrlMacro(&schema_url, replace_macro_map);
  base_info->set_schema_url(schema_url);
}

void SplashStylePostProc::SetTrackInfo(const StyleInfoItem& style_info, AdResult* item) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  item->mutable_ad_deliver_info()->clear_track();
  SetThirdTrack(style_info, item);
  SetLandingTrack(style_info, item);
  // 填充京快监测链接
  if (enable_jk_track_in_splash_ &&
      base_info->unit_type() == kuaishou::ad::AdEnum_UnitType_JK_UNIT) {
    SetJKTrackUrl(item);
    session_data_->dot_perf->Count(1, "splash_style_post_proc.set_jk_track_url",
                                   (session_data_->IsSplashRealtime() ? "realtime" : "prefetch"));
  }
  if (FrontKconfUtil::enableIosUAReplace() &&
      is_ios_platform_ && engine_base::CompareAppVersion(app_version_, "7.6.20") < 0) {
    auto* track = item->mutable_ad_deliver_info()->mutable_track();
    for (auto iter = track->begin(); iter != track->end(); ++iter) {
      TLOG_EVERY_N(INFO, 1000) << "url: " << iter->url();
      if (iter->url().find("__UA__") != std::string::npos) {
        std::string new_url;
        base::FastStringReplace(iter->url(), "__UA__", "", true, &new_url);
        TLOG_EVERY_N(INFO, 1000) << "url: " << iter->url() << ". new_url: " << new_url;
        iter->set_url(new_url);
      }
    }
  }
}

void SplashStylePostProc::SetLiveReservationInfo(const StyleInfoItem& style_info,
                                                kuaishou::ad::AdBaseInfo* base_info) {
  if (style_info.has_unit_small_shop_merchant_support_info()) {
    auto& unit_small_shop_merchant_support_info = style_info.unit_small_shop_merchant_support_info();
    base_info->set_reservation_id(unit_small_shop_merchant_support_info.reservation_id());
    base_info->set_live_start_time(unit_small_shop_merchant_support_info.live_start_time());
    base_info->set_live_end_time(unit_small_shop_merchant_support_info.live_end_time());
    base_info->set_merchant_item_put_type(unit_small_shop_merchant_support_info.item_type());
  }
}

void SplashStylePostProc::SetIsSite(kuaishou::ad::AdBaseInfo* base_info, const StyleInfoItem& style_info) {
  if (style_info.has_creative()) {
    if (style_info.creative().site_id() != 0 &&
        style_info.has_magic_site_page() &&
        style_info.magic_site_page().id() == style_info.creative().site_id()) {
      base_info->set_is_site(style_info.creative().is_site());
      base_info->set_site_id(style_info.creative().site_id());
      const auto& magic_site_page_info = style_info.magic_site_page();
      base_info->set_magic_site_page_type(magic_site_page_info.magic_site_page_type());
      base_info->set_page_source_type(magic_site_page_info.page_source_type());
    }
  }
  if (!base_info->is_site() && style_info.has_unit() && style_info.unit().is_site()) {
    base_info->set_is_site(style_info.unit().is_site());
    base_info->set_site_id(style_info.unit().site_id());
    if (style_info.has_magic_site_page() &&
        style_info.magic_site_page().id() == style_info.unit().site_id()) {
      const auto& magic_site_page_info = style_info.magic_site_page();
      base_info->set_magic_site_page_type(magic_site_page_info.magic_site_page_type());
      base_info->set_page_source_type(magic_site_page_info.page_source_type());
    }
  }
  if (SPDM_enableFillBsLive() && style_info.magic_site_sku_page().site_id() > 0
      && !base_info->is_site()) {
    base_info->set_is_site(true);
    base_info->set_site_id(style_info.magic_site_sku_page().site_id());
    base_info->set_magic_site_page_type(style_info.magic_site_sku_page().magic_site_page_type());
    base_info->set_page_source_type(style_info.magic_site_sku_page().page_source_type());
  }
}

int SplashStylePostProc::GetRequestScene(const StyleInfoItem& style_info, AdResult* item) {
  std::unordered_set<int64_t> resource_ids;
  base::Json resource_json(StringToJson(style_info.unit().resource_ids()));
  if (resource_json.IsArray()) {
    for (auto* item : resource_json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        resource_ids.insert(id);
      }
    }
  }
  if (style_info.unit().resource_id() == engine_base::SPLASH_SCENE ||
      ad_base::stl::HasKey(engine_base::SPLASH_SCENE, resource_ids)) {
    return engine_base::SPLASH_SCENE;
  }
  if (ks::ad_base::GetInteractiveForm(*(session_data_->get_ad_request())) ==
      kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS) {
    if (style_info.unit().resource_id() == engine_base::OPT_SCENE ||
        ad_base::stl::HasKey(engine_base::OPT_SCENE, resource_ids)) {
      return engine_base::OPT_SCENE;
    } else if (style_info.unit().resource_id() == engine_base::THONAS_SCENE ||
               ad_base::stl::HasKey(engine_base::THONAS_SCENE, resource_ids)) {
      return engine_base::THONAS_SCENE;
    } else if (style_info.unit().resource_id() == engine_base::UNIVERSE_SCENE ||
               ad_base::stl::HasKey(engine_base::UNIVERSE_SCENE, resource_ids)) {
      return engine_base::UNIVERSE_SCENE;
    }
    return engine_base::FEED_SCENE;
  } else {
    if (style_info.unit().resource_id() == engine_base::OPT_SCENE ||
        ad_base::stl::HasKey(engine_base::OPT_SCENE, resource_ids)) {
      return engine_base::OPT_SCENE;
    } else if (style_info.unit().resource_id() == engine_base::COVER_FEED_SCENE ||
               ad_base::stl::HasKey(engine_base::COVER_FEED_SCENE, resource_ids)) {
      return engine_base::COVER_FEED_SCENE;
    }
    return engine_base::FEED_SCENE;
  }
}

void SplashStylePostProc::SetThirdTrack(const StyleInfoItem& style_info, AdResult* item) {
  std::string impression_url = style_info.creative().impression_url();
  std::string click_url = style_info.creative().click_url();
  std::string live_track_url = style_info.creative().live_track_url();
  std::string actionbar_click_url = style_info.creative().actionbar_click_url();
  int64_t timestamp = base::GetTimestamp() / 1000000;   // 秒级时间戳
  absl::flat_hash_map<std::string, std::string> replace_macro_map;
  int scene = style_info.unit().resource_id();
  if (FrontKconfUtil::enableRequestSceneForTrack()) {
    scene = GetRequestScene(style_info, item);
  }

  replace_macro_map.insert(
      std::make_pair(kAccountKeyword, absl::StrCat(style_info.creative().account_id())));
  replace_macro_map.insert(std::make_pair(kAIDKeyWords, std::to_string(style_info.creative().unit_id())));
  if (!IsInnerRtbGray(style_info)) {
    replace_macro_map.insert(std::make_pair(kCIDKeyWords,
        std::to_string(style_info.creative().id())));
  } else {
    replace_macro_map.insert(std::make_pair(kCIDKeyWords,
        std::to_string(style_info.creative().creative_support_info().auto_deliver_related_id())));
  }
  replace_macro_map.insert(
      std::make_pair(kDIDKeyWords, std::to_string(style_info.creative().campaign_id())));
  std::string encode_campaign_name;
  base::FastEncodeUrlComponent(style_info.campaign().name().c_str(), &encode_campaign_name);
  replace_macro_map.insert(std::make_pair(kDNAMEKeyWords, encode_campaign_name));

  replace_macro_map.insert(std::make_pair(kCSITEKeyWords, std::to_string(scene)));

  std::string account_mul_exp = common_style_post_strategy_->GetAccountMulExpValue(style_info);
  if (!account_mul_exp.empty()) {
    replace_macro_map.insert(make_pair(kVIDKeyWords, account_mul_exp));
  }
  if (session_data_->get_ad_request()->universe_ad_request_info().splash_req_info().is_realtime() &&
      pdd_win_notice_url_helper_) {
    auto rta_source_type = item->ad_deliver_info().ad_base_info().rta_source_type();
    const std::string& rta_source_type_str = kuaishou::ad::RtaSourceType_Name(rta_source_type);
    if (pdd_win_notice_url_helper_->Process(style_info.creative().account_id(),
        *session_data_, timestamp, item, &replace_macro_map)) {
      session_data_->dot_perf->Count(1, "splash_replace_pdd_rta_win_notice_url", rta_source_type_str);
    } else {
      session_data_->dot_perf->Count(1, "splash_replace_pdd_not_rta_win_notice_url", rta_source_type_str);
    }
  }

  // rta trace request id
  if (item->ad_deliver_info().ad_base_info().rta_source_type() !=
    kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    const auto &online_join_params_transparent = item->ad_deliver_info().online_join_params_transparent();
    if (online_join_params_transparent.trace_request_id() > 0) {
      replace_macro_map.insert(make_pair(kTraceReqIdKeyWords,
            std::to_string(online_join_params_transparent.trace_request_id())));
      session_data_->dot_perf->Count(1, "rta_ad_track_url_pack_has_trace_id");
    } else {
      std::string extra1 = std::to_string(
          session_data_->get_ad_request()->universe_ad_request_info().splash_req_info().is_realtime())
          + "-" + std::to_string(item->ad_deliver_info().ad_base_info().real_time_type())
          + "-" + std::to_string(item->ad_deliver_info().ad_base_info().rta_source_type());
      session_data_->dot_perf->Count(1, "rta_ad_track_url_pack_miss_trace_id", extra1);
    }
    auto *p_rank_result = GetAdRankResult(session_data_, *item);
    if (p_rank_result &&
        session_data_->common_r_->GetIntCommonAttr("enable_send_rta_second_bid").value_or(0) == 1) {
      replace_macro_map.insert(make_pair(kRtaBidSecondKeyWords,
          std::to_string(p_rank_result->ad_rank_trans_info().is_rta_bid_second())));
    }
    replace_macro_map.insert(make_pair(kRtaBidKeyWords,
          std::to_string(online_join_params_transparent.rta_sta_tag())));
    replace_macro_map.insert(
        make_pair(kRtaFeatureIdKeyWords,
        std::to_string(online_join_params_transparent.rta_feature_id())));
    replace_macro_map.insert(std::make_pair(kRequestKeyword, std::to_string(session_data_->get_llsid())));
    replace_macro_map.insert(make_pair(kRtaBidRatio,
          std::to_string(online_join_params_transparent.first_click_score() / 1000000.0f)));
  }
  if (style_info.unit().dpa_type() > 1 &&
      !style_info.unit().unit_support_info().dpa_outer_id().empty()) {
    replace_macro_map.insert(make_pair(kProductIdKeyWords,
                            style_info.unit().unit_support_info().dpa_outer_id()));
  }
  if (FrontKconfUtil::enableReplaceCredit()) {
    replace_macro_map.insert(make_pair(kUserCreditKeyWords, absl::StrFormat("%.4f",
        session_data_->common_r_->GetDoubleCommonAttr("ud_use_credit").value_or(0))));  // 用信意愿分
    replace_macro_map.insert(make_pair(kProvideCreditKeyWords, absl::StrFormat("%.4f",
        session_data_->common_r_->GetDoubleCommonAttr("ud_provide_credit").value_or(0))));  // 授信质量分
  }
  if (FrontKconfUtil::enableReplaceStrategyId()) {
    const auto &online_join_param = item->ad_deliver_info().online_join_params_pb();
    replace_macro_map.insert(make_pair(kStrategyId, absl::StrCat(online_join_param.rta_strategy_id())));
  }

  ReplaceUrlMacro(&impression_url, replace_macro_map);
  ReplaceUrlMacro(&click_url, replace_macro_map);
  ReplaceUrlMacro(&live_track_url, replace_macro_map);
  ReplaceUrlMacro(&actionbar_click_url, replace_macro_map);

  int64_t account_id = style_info.creative().account_id();

  if (!IsRtbGray(style_info) && !IsInnerRtbGray(style_info)) {
    AddTrack(item, kuaishou::ad::AD_SPLASH_IMPRESSION, Tracking::AddCallbackParam(impression_url),
        Tracking::GetTrackOperationType(impression_url, account_id));
    AddTrack(item, kuaishou::ad::AD_SPLASH_CLICK, Tracking::AddCallbackParam(click_url),
        Tracking::GetTrackOperationType(click_url, account_id));
    if (style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
      AddTrack(item, kuaishou::ad::AD_SPLASH_IMPRESSION, Tracking::AddCallbackParam(live_track_url),
          Tracking::GetTrackOperationType(live_track_url, account_id));
    }
  } else {
    AddTrack(item, kuaishou::ad::AD_SPLASH_IMPRESSION, Tracking::AddCallbackParam(click_url),
        Tracking::GetTrackOperationType(impression_url, account_id));
    AddTrack(item, kuaishou::ad::AD_SPLASH_CLICK, Tracking::AddCallbackParam(actionbar_click_url),
        Tracking::GetTrackOperationType(click_url, account_id));
  }
}

void SplashStylePostProc::AddTrack(AdResult* item, const kuaishou::ad::AdActionType& type,
    const std::string& url, const kuaishou::ad::AdTrackInfo::TrackUrlOperationType& operation_type) {
  if (url.compare("") > 0) {
    auto* adTrackInfo = item->mutable_ad_deliver_info()->add_track();
    adTrackInfo->set_type(type);
    adTrackInfo->set_url(url);
    adTrackInfo->set_track_url_operation_type(operation_type);
    if (FrontKconfUtil::enableDefaultMacroSet()->count(
        item->ad_deliver_info().ad_base_info().account_id()) > 0) {
      adTrackInfo->set_enable_default_macro(true);
    }
  }
}

void SplashStylePostProc::SetLandingTrack(const StyleInfoItem &style_info,
    kuaishou::ad::AdResult *item) {
  absl::flat_hash_map<std::string, std::string> replace_macro_map;
  replace_macro_map.insert(make_pair(kAIDKeyWords, std::to_string(style_info.unit().id())));
  if (!IsInnerRtbGray(style_info)) {
    replace_macro_map.insert(make_pair(kCIDKeyWords, std::to_string(style_info.creative().id())));
  } else {
    replace_macro_map.insert(make_pair(kCIDKeyWords,
          std::to_string(style_info.creative().creative_support_info().auto_deliver_related_id())));
  }
  replace_macro_map.insert(make_pair(kDIDKeyWords, std::to_string(style_info.campaign().id())));
  if (style_info.campaign().name().length() > 0) {
    std::string encode_campaign_name;
    base::FastEncodeUrlComponent(style_info.campaign().name().c_str(), &encode_campaign_name);
    replace_macro_map.insert(make_pair(kDNAMEKeyWords, encode_campaign_name));
  }
  std::string account_mul_exp = common_style_post_strategy_->GetAccountMulExpValue(style_info);
  if (!account_mul_exp.empty()) {
      replace_macro_map.insert(make_pair(kVIDKeyWords, account_mul_exp));
  }
  // rta trace request id
  if (item->ad_deliver_info().ad_base_info().rta_source_type() !=
    kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    const auto &online_join_params_transparent = item->ad_deliver_info().online_join_params_transparent();
    if (online_join_params_transparent.trace_request_id() > 0) {
      replace_macro_map.insert(make_pair(kTraceReqIdKeyWords,
            std::to_string(online_join_params_transparent.trace_request_id())));
      session_data_->dot_perf->Count(1, "rta_ad_url_pack_has_trace_id");
    } else {
      session_data_->dot_perf->Count(1, "rta_ad_url_pack_miss_trace_id");
    }
    auto *p_rank_result = GetAdRankResult(session_data_, *item);
    if (p_rank_result &&
        session_data_->common_r_->GetIntCommonAttr("enable_send_rta_second_bid").value_or(0) == 1) {
      replace_macro_map.insert(make_pair(kRtaBidSecondKeyWords,
          std::to_string(p_rank_result->ad_rank_trans_info().is_rta_bid_second())));
    }
    replace_macro_map.insert(make_pair(kRtaBidKeyWords,
          std::to_string(online_join_params_transparent.rta_sta_tag())));
    replace_macro_map.insert(
        make_pair(kRtaFeatureIdKeyWords,
        std::to_string(online_join_params_transparent.rta_feature_id())));
    replace_macro_map.insert(make_pair(kRequestKeyword, std::to_string(session_data_->get_llsid())));
    replace_macro_map.insert(make_pair(kRtaBidRatio,
          std::to_string(online_join_params_transparent.first_click_score() / 1000000.0f)));
    session_data_->dot_perf->Count(
        1, "rta_ad_trace_campare",
        online_join_params_transparent.trace_request_id() == session_data_->get_llsid() ? "true" : "false",
        std::to_string(item->ad_deliver_info().ad_base_info().rta_source_type()));
  }
  if (style_info.unit().dpa_type() > 1 &&
      !style_info.unit().unit_support_info().dpa_outer_id().empty()) {
    replace_macro_map.insert(make_pair(kProductIdKeyWords,
                            style_info.unit().unit_support_info().dpa_outer_id()));
  }
  if (FrontKconfUtil::enableReplaceCredit()) {
    replace_macro_map.insert(make_pair(kUserCreditKeyWords, absl::StrFormat("%.4f",
        session_data_->common_r_->GetDoubleCommonAttr("ud_use_credit").value_or(0))));  // 用信意愿分
    replace_macro_map.insert(make_pair(kProvideCreditKeyWords, absl::StrFormat("%.4f",
        session_data_->common_r_->GetDoubleCommonAttr("ud_provide_credit").value_or(0))));  // 授信质量分
  }
  if (FrontKconfUtil::enableReplaceStrategyId()) {
    const auto &online_join_param = item->ad_deliver_info().online_join_params_pb();
    replace_macro_map.insert(make_pair(kStrategyId, absl::StrCat(online_join_param.rta_strategy_id())));
  }

  ReplaceUrlMacro(item->mutable_ad_deliver_info()->mutable_ad_base_info()->mutable_url(),
                  replace_macro_map);
  ReplaceUrlMacro(item->mutable_ad_deliver_info()->mutable_ad_base_info()->mutable_schema_url(),
                  replace_macro_map);
}

void SplashStylePostProc::SetCallbackPassback(const StyleInfoItem& style_info, kuaishou::ad::AdResult* item) {
  const kuaishou::ad::AdDeliverInfo& ad_deliver_info = item->ad_deliver_info();
  const kuaishou::ad::UniverseAdDeliverInfo& universe_ad_deliver_info
      = ad_deliver_info.universe_ad_deliver_info();
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  std::string callback_passback_json_str;
  kuaishou::ad::AdBaseInfo::AdCallbackPassback callback_passback;
  callback_passback.set_charge_action_type(base_info->charge_action_type());
  callback_passback.set_price(ad_deliver_info.price());
  callback_passback.set_bid_server_group_idx(item->target_bid().group_idx());
  callback_passback.set_ocpc_action_type(base_info->ocpc_action_type());

  // 注意，必须在 AdBaseInfo 设置 bid_type 之后
  callback_passback.set_bid_type(base_info->bid_type());
  callback_passback.set_ad_source_type(item->ad_source_type());
  callback_passback.set_convert_id(style_info.trace_util().id());
  callback_passback.set_convert_type(style_info.trace_util().type());
  callback_passback.set_item_type(base_info->item_type());
  if (callback_passback.price() < 0) {
    callback_passback.set_price(0);
  }
  bool ali_exclude_flag = false;
  auto *p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result) {
    if (p_rank_result->ad_rank_trans_info().ali_exclude_flag() == 2) {  // NOLINT
      ali_exclude_flag = true;
    }
  }
  bool rta_exclude_flag = false;
  if (item->ad_deliver_info().ad_base_info().rta_source_type() !=
        kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    rta_exclude_flag = item->ad_deliver_info().ad_base_info().rta_info().is_dynamic_bidding();
  }
  auto uaa_game_account_set = FrontKconfUtil::UaaGameAccountWhiteSet();
  auto increment_explore_hit_set = FrontKconfUtil::incrementExploreHitSet();
  const kuaishou::ad::AdBidTransInfo& ad_bid_trans_info =
      item->ad_deliver_info().online_join_params_transparent().
          ad_target_trans_info().ad_bid_trans_info();
  int32_t hit_white_account_explore_cost = engine_base::GetBidTransInfoValueWithDefault<int32_t>(
      ad_bid_trans_info, "hit_white_account_explore_cost", 0);
  if (base_info->ad_bid_server_group_tag() ==
      ad_base::AutoBidGroupTags::unit_acc_cold_start ||
      FrontKconfUtil::testAccExploreChargeInfo()) {
    callback_passback.set_charge_type(CallbackChargeType::kAccColdStart);
  } else if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::unit_recovery_start) {
    callback_passback.set_charge_type(CallbackChargeType::kRecoveryStart);  // 表示一键复苏
  } else if (ali_exclude_flag && base_info->campaign_sub_type() ==
             kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE) {
    callback_passback.set_charge_type(CallbackChargeType::kAliDeepCvr);
  } else if (rta_exclude_flag) {
    callback_passback.set_charge_type(CallbackChargeType::kRtaBidRatio);
    session_data_->dot_perf->Count(1, "rta_set_charge_info");
  } else if (base_info->ad_bid_server_group_tag() ==
                  ad_base::AutoBidGroupTags::ecpc) {
    callback_passback.set_charge_type(CallbackChargeType::kEcpc);
  }  else if (base_info->ad_bid_server_group_tag() ==
                  ad_base::AutoBidGroupTags::ocpm2_min_cost) {
    callback_passback.set_charge_type(CallbackChargeType::ocpm2MinCost);
  } else if (EnableBoostBidControl(session_data_, item)) {
    callback_passback.set_charge_type(CallbackChargeType::kBoostBid);
  } else if (FrontKconfUtil::enableUaaSetChargeType() &&
              uaa_game_account_set->count(base_info->account_id())) {
    callback_passback.set_charge_type(CallbackChargeType::kUaaUnionPurchase);
  }
  if (FrontKconfUtil::enableIncrementExploreSetChargeType() &&
      base_info->is_increment_explore() &&
      increment_explore_hit_set->count(hit_white_account_explore_cost) > 0) {
    callback_passback.set_charge_type(CallbackChargeType::kIncrementExplore);
  }
  std::map<int64, int64> account_wentou_bid_coef_hit_flag_map = engine_base::ParseStringToMap<int64>(
      engine_base::GetBidTransInfoValueWithDefault<std::string>(ad_bid_trans_info,
      "account_wentou_bid_coef_hit_flag_str", ""));
  auto account_wentou_iter = account_wentou_bid_coef_hit_flag_map.find(base_info->campaign_id());
  if (account_wentou_iter != account_wentou_bid_coef_hit_flag_map.end()) {
    if ((account_wentou_iter->second & 2) == 2) {
      callback_passback.set_charge_type(CallbackChargeType::wentouEcpc);
    }
    if ((account_wentou_iter->second & 4) == 4) {
      callback_passback.set_charge_type(CallbackChargeType::wentouOcpm2MinCost);
    }
    if ((account_wentou_iter->second & 8) == 8) {
      callback_passback.set_charge_type(CallbackChargeType::wentouManual);
    }
  }
  int64_t pos_id = universe_ad_deliver_info.pos_id();
  // 从 posManager 获取 adScene
  FillAdScene(&callback_passback, pos_id, session_data_->get_ad_pos_info_vec());
  if (item->ad_source_type() == kuaishou::ad::DSP) {
    if (GetRequestScene(style_info, item) == engine_base::SPLASH_SCENE ||
        GetRequestScene(style_info, item) == engine_base::OPT_SCENE) {
      callback_passback.set_ad_scene(kuaishou::ad::AdEnum_AdScene_SPLASH_SCENE);
    } else {
      callback_passback.set_ad_scene(kuaishou::ad::AdEnum_AdScene_UP_DOWN_SLIDING);
    }
  }
  std::string falcon_key = "ad.ad_front.splash_ad_scene_" + std::to_string(callback_passback.ad_scene());
  falcon::Inc(falcon_key.c_str());
  if (GetRequestScene(style_info, item) == engine_base::UNIVERSE_SCENE) {
    callback_passback.set_placement_type(::kuaishou::ad::AdEnum_PlacementType_PT_UNION);
  } else {
    callback_passback.set_placement_type(::kuaishou::ad::AdEnum_PlacementType_PT_KWAI);
  }
  callback_passback.set_pos_id(pos_id);
  callback_passback.set_adx_source_type(base_info->adx_source_type());
  callback_passback.set_reco_client_browse_type(
      session_data_->get_ad_request()->reco_request_info().browse_type());
  callback_passback.set_virtual_creative_id(base_info->creative_id());
  if (style_info.has_unit()) {
    callback_passback.set_unit_type(style_info.unit().unit_type());
  }
  // 灰投广告填充原始创意相关 id
  if (IsRtbGray(style_info)) {
    callback_passback.set_derived_exchange(true);
    callback_passback.set_derived_creative_id(style_info.creative().id());
    callback_passback.set_derived_photo_id(style_info.creative().photo_id());
    callback_passback.set_derived_cover_id(base_info->cover_id());
    int64_t material_id;
    if (absl::SimpleAtoi(style_info.creative().material_id(), &material_id)) {
      callback_passback.set_derived_material_id(material_id);
    }
  } else if (IsInnerRtbGray(style_info)) {
    callback_passback.set_derived_exchange(true);
    callback_passback.set_derived_creative_id(
        style_info.creative().creative_support_info().auto_deliver_related_id());
    callback_passback.set_derived_photo_id(
        style_info.creative().creative_support_info().shadow_photo_id());
  }
  callback_passback.set_ad_queue_type(item->ad_deliver_info().ad_queue_type());
  callback_passback_json_str.clear();
  ::google::protobuf::util::MessageToJsonString(callback_passback, &callback_passback_json_str);
  auto callback_account_set = FrontKconfUtil::callbackAccountSet();
  if (callback_account_set->find(base_info->account_id())
      != callback_account_set->end()) {
    base_info->set_version(1);
    base_info->mutable_ad_callback_passback()->CopyFrom(callback_passback);
  }
  TLOG_EVERY_N(INFO, FrontKconfUtil::logReqDetailFrequency()) <<
        "callback json str: " << callback_passback_json_str;
  bool enable_passback_pb = FrontKconfUtil::enablePassbackPb();
  callback_passback.set_cover_id(base_info->cover_id());
  callback_passback.set_creative_material_type(base_info->creative_material_type());
  if (enable_passback_pb) {
    base_info->set_version(1);
    base_info->mutable_ad_callback_passback()->CopyFrom(callback_passback);
  } else if (callback_account_set->find(base_info->account_id())
          != callback_account_set->end()) {
    base_info->set_version(1);
    base_info->mutable_ad_callback_passback()->CopyFrom(callback_passback);
  }
  base_info->set_callback_passback(callback_passback_json_str);

  if (session_data_->get_spdm_ctx().TryGetBoolean("clear_str_callback_passback", false)) {
    base_info->clear_callback_passback();
  }
}

void SplashStylePostProc::SetDerivedInfo(const StyleInfoItem& style_info, kuaishou::ad::AdResult* item) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  // 灰投广告填充原始创意相关 id
  if (IsRtbGray(style_info)) {
    base_info->set_derived_exchange(true);
    base_info->set_derived_creative_id(style_info.creative().id());
    base_info->set_derived_photo_id(style_info.creative().photo_id());
    base_info->set_derived_cover_id(base_info->cover_id());
    int64_t material_id;
    if (absl::SimpleAtoi(style_info.creative().material_id(), &material_id)) {
      base_info->set_derived_material_id(material_id);
    }
    base_info->set_is_splash_gray(true);
  } else if (IsInnerRtbGray(style_info)) {
    base_info->set_derived_exchange(true);
    base_info->set_derived_creative_id(
        style_info.creative().creative_support_info().auto_deliver_related_id());
    base_info->set_derived_photo_id(
        style_info.creative().creative_support_info().shadow_photo_id());
    base_info->set_is_splash_gray(true);
  }
  base_info->set_auto_deliver_type(
      style_info.creative().creative_support_info().auto_deliver_type());
}

void SplashStylePostProc::SetEncodeChargeInfo(const StyleInfoItem& style_info, AdResult* item) {
  // TODO(liulong03) price 的兜底先放到这里，等 adFront 会依赖 adPack 的返回值以后，这里进行删除
  // chargeInfo 生成之前处理价格兜底
  ks::ad_base::util::PriceProtection(
      session_data_->get_sub_page_id(), session_data_->get_ad_request()->ad_request_flow_type(), item,
      session_data_->get_universe_rtb_coff(), session_data_->get_is_universe_rtb_flow());
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  kuaishou::ad::AdChargeInfo charge_info;
  ContextData *context = context_->GetMutableContextData<ContextData>();
  AddSesionExpChargeInfo(&charge_info, context);
  std::string charge_info_string;
  std::string encode_charge_info_string;
  charge_info.set_device_id(session_data_->get_ad_request()->ad_user_info().device_id());
  charge_info.set_visitor_id(session_data_->get_ad_request()->ad_user_info().id());
  charge_info.set_interactive_form(4);
  charge_info.mutable_ad_deliver_info()->CopyFrom(item->ad_deliver_info());
  bool ali_exclude_flag = false;
  auto *p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result) {
    if (p_rank_result->ad_rank_trans_info().ali_exclude_flag() == 2) {  // NOLINT
      ali_exclude_flag = true;
    }
  }
  bool rta_exclude_flag = false;
  if (item->ad_deliver_info().ad_base_info().rta_source_type() !=
        kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    rta_exclude_flag = item->ad_deliver_info().ad_base_info().rta_info().is_dynamic_bidding();
  }

  // 填充性别、年龄字段，用于升维调价
  if (FrontKconfUtil::enableUpDimensionCaliFilling()) {
    charge_info.set_gender(session_data_->get_ad_request()->ad_user_info().gender());
    charge_info.set_age_segment(session_data_->get_ad_request()->ad_user_info().age_segment());
  }

  auto uaa_game_account_set = FrontKconfUtil::UaaGameAccountWhiteSet();
  auto increment_explore_hit_set = FrontKconfUtil::incrementExploreHitSet();
  const kuaishou::ad::AdBidTransInfo& ad_bid_trans_info =
      item->ad_deliver_info().online_join_params_transparent().
          ad_target_trans_info().ad_bid_trans_info();
  int32_t hit_white_account_explore_cost = engine_base::GetBidTransInfoValueWithDefault<int32_t>(
      ad_bid_trans_info, "hit_white_account_explore_cost", 0);
  if (base_info->ad_bid_server_group_tag() ==
      ad_base::AutoBidGroupTags::unit_acc_cold_start) {
    charge_info.set_charge_type(CallbackChargeType::kAccColdStart);
  } else if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::unit_recovery_start) {
    charge_info.set_charge_type(CallbackChargeType::kRecoveryStart);  // 表示一键复苏
  } else if (ali_exclude_flag && base_info->campaign_sub_type() ==
             kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE) {
    charge_info.set_charge_type(CallbackChargeType::kAliDeepCvr);
  } else if (rta_exclude_flag) {
    charge_info.set_charge_type(CallbackChargeType::kRtaBidRatio);
    session_data_->dot_perf->Count(1, "rta_set_callback_passback");
  } else if (base_info->ad_bid_server_group_tag() ==
                  ad_base::AutoBidGroupTags::ecpc) {
    charge_info.set_charge_type(CallbackChargeType::kEcpc);
  }  else if (base_info->ad_bid_server_group_tag() ==
                  ad_base::AutoBidGroupTags::ocpm2_min_cost) {
    charge_info.set_charge_type(CallbackChargeType::ocpm2MinCost);
  } else if (EnableBoostBidControl(session_data_, item)) {
    charge_info.set_charge_type(CallbackChargeType::kBoostBid);
  } else if (FrontKconfUtil::enableUaaSetChargeType() &&
              uaa_game_account_set->count(base_info->account_id())) {
    charge_info.set_charge_type(CallbackChargeType::kUaaUnionPurchase);
    session_data_->dot_perf->Count(1, "uaa_union_purchase_charge_info_cnt");
  }
  if (FrontKconfUtil::enableIncrementExploreSetChargeType() &&
      base_info->is_increment_explore() &&
      increment_explore_hit_set->count(hit_white_account_explore_cost) > 0) {
    charge_info.set_charge_type(CallbackChargeType::kIncrementExplore);
  }
  std::map<int64, int64> account_wentou_bid_coef_hit_flag_map = engine_base::ParseStringToMap<int64>(
      engine_base::GetBidTransInfoValueWithDefault<std::string>(ad_bid_trans_info,
      "account_wentou_bid_coef_hit_flag_str", ""));
  auto account_wentou_iter = account_wentou_bid_coef_hit_flag_map.find(base_info->campaign_id());
  if (account_wentou_iter != account_wentou_bid_coef_hit_flag_map.end()) {
    if ((account_wentou_iter->second & 2) == 2) {
      charge_info.set_charge_type(CallbackChargeType::wentouEcpc);
    }
    if ((account_wentou_iter->second & 4) == 4) {
      charge_info.set_charge_type(CallbackChargeType::wentouOcpm2MinCost);
    }
    if ((account_wentou_iter->second & 8) == 8) {
      charge_info.set_charge_type(CallbackChargeType::wentouManual);
    }
  }

  CleanChargeInfo(session_data_, &charge_info);
  // 清除部分 chargeinfo 字段，与现有 adserver 逻辑一致
  charge_info.mutable_ad_deliver_info()->mutable_ad_base_info()->clear_alert_net_mobile();
  charge_info.mutable_ad_deliver_info()->mutable_ad_base_info()->
    mutable_client_style_info()->clear_landing_page_actionbar_info();
  charge_info.mutable_ad_deliver_info()->mutable_ad_base_info()->clear_target_user_id();
  bool code = charge_info.SerializeToString(&charge_info_string);
  if (FrontKconfUtil::enableChargeInfoPrint()) {
    LOG(INFO) << "ChargeInfo: " << charge_info.ShortDebugString();
  }

  ChargeInfoByteSizeAlert(session_data_->dot_perf,
                          ks::ad_base::GetAdRequestType(*session_data_->get_ad_request()),
                          GetAdDspType(*item), item->ad_deliver_info().ad_monitor_type(), charge_info,
                          session_data_->get_byte_size_dot());

  serving_base::AesCrypter aes_crypter;
  base::Base64Encode(aes_crypter.Encrypt(charge_info_string), &encode_charge_info_string);
  if (KS_UNLIKELY(encode_charge_info_string.empty())) {
    falcon::Inc("front_server.charge_info_encode_failed", 1);
  }
  item->set_encode_charge_info(encode_charge_info_string);
}

void SplashStylePostProc::SetAdxPrefetchSplashInfo(AdResult* ad_result) {
  auto* base_info = ad_result->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto* splash_info = base_info->mutable_splash_info();
  auto* splash_base_info = splash_info->mutable_base_info();
  int64_t now_s = base::GetTimestamp() / 1000000;
  std::string account_id_str = std::to_string(ad_result->ad_deliver_info().ad_base_info().account_id());
  if (FrontKconfUtil::adxSplashAccountCacheTime()->count(account_id_str) > 0) {
    splash_adx_cache_hour_ = FrontKconfUtil::adxSplashAccountCacheTime()->at(account_id_str);
  }
  // splash base 信息
  splash_base_info->set_start_time(now_s);  // 单位秒
  splash_base_info->set_end_time(now_s + splash_adx_cache_hour_ * 3600);  // 单位秒
  splash_base_info->set_splash_id(std::to_string(base_info->creative_id()));
  if (base_info->photo_id() <= 0) {
    splash_info->set_splash_ad_material_type(2);
  } else {
    splash_info->set_splash_ad_material_type(1);
  }
  if (base_info->has_material_info()
      && base_info->material_info().material_feature_size() > 0) {
    auto& feature = base_info->material_info().material_feature(0);
    splash_info->set_material_width(feature.material_size().width());
    splash_info->set_material_height(feature.material_size().height());
    splash_info->add_image_urls(feature.material_url());
  }
  splash_info->set_photo_id(std::to_string(base_info->photo_id()));
  splash_info->set_splash_ad_display_style(2);  // 全部为沉浸式
  if (base_info->photo_id() > 0) {  // 开屏时长
    splash_info->set_skip_tag_show_time(0);
    splash_info->set_splash_ad_duration(5);
  } else {
    splash_info->set_skip_tag_show_time(0);
    splash_info->set_splash_ad_duration(splash_pic_show_time_);
  }
  splash_info->set_splash_ad_type(2);  // 非 topview
  splash_info->set_splash_touch_control("000000");  // 开屏手势控制策略
  if ((session_data_->get_ad_request()->ad_user_info().platform().compare("android") == 0 ||
      session_data_->get_ad_request()->ad_user_info().platform().compare("harmony") == 0)) {
    splash_info->set_splash_touch_control("0000001");  // 开屏手势控制策略
  }
  splash_info->set_audio_button_visible(false);
  splash_info->set_splash_show_control(0);
  splash_info->set_enable_4g_cache(true);
  splash_info->set_force_display_normal_splash_for_eyemax(false);
  splash_info->set_clear_material_when_impression(true);

  // 点击按钮
  SetClickButtonInfo(splash_info);
  SetLightInteractive(splash_info, base_info);
  // ad data v2 填充
  ad_data_v2_.Clear();
  ad_data_v2_.mutable_splash_info()->CopyFrom(*splash_info);
  if (base_info->splash_interactive_style() > 0) {
    ad_data_v2_.set_unit_style_type(4);  // LIGHT_INTERACTIVE 轻互动
  }
  std::string ad_data_v2_json_str;
  ::google::protobuf::util::MessageToJsonString(ad_data_v2_, &ad_data_v2_json_str);
  base_info->set_ad_data_v2(ad_data_v2_json_str);
}

void SplashStylePostProc::SetAdxRealtimeSplashInfo(AdResult* item) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto splash_info = base_info->mutable_splash_info();
  auto splash_base_info = splash_info->mutable_base_info();
  splash_base_info->set_splash_id(std::to_string(base_info->creative_id()));
  auto splash_policy =
    session_data_->mutable_splash_effect_ad_response()->mutable_global_ext_data()->mutable_splash_policy();
  auto new_meta = splash_policy->add_splash_ad_meta_list();
  auto p_splash_info_base_info = new_meta->add_creative_info_list();
  p_splash_info_base_info->CopyFrom(*splash_base_info);

  std::string realtime_splash_str;
  kuaishou::ad::AdBaseInfo::RealtimeSplashInfo realtime_splash_info;
  realtime_splash_info.set_splash_id(std::to_string(base_info->creative_id()));
  std::string ip_v4 = session_data_->get_ad_request()->reco_user_info().location().ip();
  std::string ip_v6 = "";
  // 命中白名单时用请求里带的 ipv6
  if (FrontKconfUtil::ipv6RealtimeSplashInfoWhiteProductList()->count(base_info->product_name())) {
    ip_v6 = Ipv6BinToString(session_data_->get_ad_request()->reco_user_info().location().ipv6());
  } else if (session_data_->get_ad_request()->has_ad_user_info() &&
      session_data_->get_ad_request()->ad_user_info().device_info_size() >= 1 &&
      session_data_->get_ad_request()->ad_user_info().device_info(0).has_ipv6()) {
    ip_v6 = Ipv6BinToString(session_data_->get_ad_request()->ad_user_info().device_info(0).ipv6());
  }
  std::string ip = ip_v4;
  if (ip.empty() && !ip_v6.empty()) {
    ip = ip_v6;
  }
  realtime_splash_info.set_ip(ip);
  realtime_splash_info.set_ip_v4(ip_v4);
  if (ip_v4.empty()) {
    realtime_splash_info.set_ip_v6(ip_v6);
  }
  if (item->ad_deliver_info().ad_base_info().adx_source_type() == kuaishou::ad::ADX_JINGMEI_DSP) {
    realtime_splash_info.set_charge_info(item->encode_charge_info());
  }
  std::string callback_info_str = GenerateAdxCallbackInfo(item);
  realtime_splash_info.set_callback_info(callback_info_str);
  ::google::protobuf::util::MessageToJsonString(realtime_splash_info, &realtime_splash_str);
  base_info->set_realtime_splash_info(realtime_splash_str);
}

void SplashStylePostProc::SetAdxCommonTrack(AdResult* item) {
  auto adx_source_type = item->ad_deliver_info().ad_base_info().adx_source_type();
  std::string price_string;
  FormatADXPrice(*item, &price_string);
  for (const auto& ad_common_track : item->ad_deliver_info().ad_base_info().adx_style_info().adx_tracks()) {
    if (ad_common_track.url().compare("") > 0) {
      auto* ad_track_info = item->mutable_ad_deliver_info()->add_track();
      ad_track_info->set_type(ad_common_track.type());
      ad_track_info->set_track_url_operation_type(ad_common_track.track_url_operation_type());
      std::string new_url;
      if (adx_source_type == kuaishou::ad::ADX_GDT_DSP) {
        base::FastStringReplace(ad_common_track.url(), gdtWinPriceKeywords, price_string, true, &new_url);
      } else {
        base::FastStringReplace(ad_common_track.url(), kWinPriceKeywords, price_string, true, &new_url);
      }
      ad_track_info->set_url(new_url);
      ad_track_info->set_adx_win_notice(ad_common_track.adx_win_notice());
    }
  }
  std::string win_notice_url = item->ad_deliver_info().ad_base_info().adx_style_info().win_notice_url();
  if (!(win_notice_url.empty())) {
    std::string new_win_notice_url{};
    if (adx_source_type == kuaishou::ad::ADX_GDT_DSP) {
      base::FastStringReplace(win_notice_url,
          gdtWinPriceKeywords, price_string, true, &new_win_notice_url);
    } else {
      base::FastStringReplace(win_notice_url,
          kWinPriceKeywords, price_string, true, &new_win_notice_url);
    }
    item->mutable_ad_deliver_info()->set_win_notice_url(new_win_notice_url);
  }
  if (FrontKconfUtil::enableIosUAReplace() &&
      is_ios_platform_ && engine_base::CompareAppVersion(app_version_, "7.6.20") < 0) {
    auto* track = item->mutable_ad_deliver_info()->mutable_track();
    for (auto iter = track->begin(); iter != track->end(); ++iter) {
      if (iter->url().find("__UA__") != std::string::npos) {
        std::string new_url;
        base::FastStringReplace(iter->url(), "__UA__", "", true, &new_url);
        iter->set_url(new_url);
      }
    }
  }
  return;
}

kuaishou::ad::AdActionType SplashStylePostProc::GetAdxChargeActionType(
    const kuaishou::ad::AdBaseInfo *base_info) {
  kuaishou::ad::AdActionType charge_action_type =
      kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION;
  switch (base_info->bid_type()) {
    case kuaishou::ad::AdEnum::CPM:
    case kuaishou::ad::AdEnum::OCPM:
      charge_action_type = kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION;
      break;
    case kuaishou::ad::AdEnum::CPC:
    case kuaishou::ad::AdEnum::OCPC:
      charge_action_type = kuaishou::ad::AdActionType::AD_ITEM_CLICK;
      break;
    default:
      break;
  }
  return charge_action_type;
}

void SplashStylePostProc::ProcessAdxPrefetchAdResult(AdResult* ad_result) {
  StyleInfoItem style_info;
  CreativeParser creative_parser;
  auto* base_info = ad_result->mutable_ad_deliver_info()->mutable_ad_base_info();
  base_info->set_ad_source_type(ad_result->ad_source_type());
  base_info->set_charge_action_type(GetAdxChargeActionType(base_info));
  SetAdxPrefetchSplashInfo(ad_result);
  common_deliver_info_pack_.Process(&style_info, &creative_parser, ad_result, &deliver_packer_params_,
      nullptr);
  SetAdxCommonTrack(ad_result);
  SetOriginalLandingPageUrl(ad_result);
  SetCallbackPassback(style_info, ad_result);
  SetEncodeChargeInfo(style_info, ad_result);
}

void SplashStylePostProc::ProcessAdxRealtimeAdResult(AdResult* ad_result) {
  StyleInfoItem style_info;
  CreativeParser creative_parser;
  auto* base_info = ad_result->mutable_ad_deliver_info()->mutable_ad_base_info();
  base_info->set_ad_source_type(ad_result->ad_source_type());
  base_info->set_charge_action_type(GetAdxChargeActionType(base_info));
  common_deliver_info_pack_.Process(&style_info, &creative_parser, ad_result, &deliver_packer_params_,
      nullptr);
  SetAdxCommonTrack(ad_result);
  SetOriginalLandingPageUrl(ad_result);
  SetCallbackPassback(style_info, ad_result);
  if (ad_result->ad_deliver_info().ad_base_info().adx_source_type() != kuaishou::ad::ADX_JINGMEI_DSP) {
    SetAdxRealtimeSplashInfo(ad_result);
  }
  SetLightInteractiveRealtime(style_info, base_info);
  SetEncodeChargeInfo(style_info, ad_result);
  if (ad_result->ad_deliver_info().ad_base_info().adx_source_type() == kuaishou::ad::ADX_JINGMEI_DSP) {
    SetAdxRealtimeSplashInfo(ad_result);
  }
}

void SplashStylePostProc::ProcessPrefetchAdResult(AdResult* ad_result, bool is_realtime_ad) {
  auto& item = *ad_result;

  auto* style_info_map = session_data_->mutable_style_info_resp()->mutable_style_info();
  uint64_t creative_id = item.ad_deliver_info().ad_base_info().creative_id();
  auto& style_info = (*style_info_map)[creative_id];

  TargetCheck tc(*session_data_, style_info);
  tc.SetTargetTag(&item);
  tc.SetDmpTargetTag(&item);
  CreativeParser creative_parser;
  creative_parser.Parse(style_info.creative(), session_data_->get_llsid());

  CommonAdBaseInfoPack common_base_pack(session_data_, &creative_parser, &style_info, &item, nullptr);
  common_base_pack.Process();
  auto* base_info = ad_result->mutable_ad_deliver_info()->mutable_ad_base_info();
  std::string url_with_market_uri = common_style_post_strategy_->GetUrlWithMarketUri(style_info, item);
  base_info->set_url(url_with_market_uri);
  SetIsSite(base_info, style_info);
  SetLiveReservationInfo(style_info, base_info);
  SplashMaterialInfo splash_material_info;
  if (!IsRtbGray(style_info)) {
    SelectMaterialInfo(style_info, &item, &splash_material_info);
    SetMaterialInfo(style_info, &item, splash_material_info);
  } else {
    SelectMaterialInfoGray(style_info, &item, &splash_material_info);
    SetMaterialInfoGray(style_info, &item, splash_material_info);
  }

  SetPrefetchSplashInfo(style_info, &item, splash_material_info);

  common_deliver_info_pack_.Process(&style_info, &creative_parser, ad_result, &deliver_packer_params_,
      nullptr);
  dpa_base_info_pack_.Process(&style_info, session_data_, &item);

  ReplaceSchemaUrlMacro(&item);
  if (!ConvertKsBackUrl(ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())), &item)) {
    AddBackUrlToSchemaUrl(&item);
  }
  SetTrackInfo(style_info, &item);
  SetOriginalLandingPageUrl(&item);
  SetCallbackPassback(style_info, &item);
  SetDerivedInfo(style_info, &item);
  MerchantMarketingAddFlags(base_info, style_info, session_data_,
      item.ad_deliver_info().ad_queue_type());

  std::string fal = "adfront.prefetch_promote_type_"
    + std::to_string(base_info->promotion_type());
  falcon::Inc(fal.c_str());
  if (is_realtime_ad) {
    SetRealtimeSplashMaterialType(style_info, &item, splash_material_info);
    SetLightInteractiveRealtime(style_info, base_info);
    SetRealTimeSplashShowType(base_info);
  }
  // 所有 deliverinfo 字段填充都放到 chargeinfo 前面
  SetEncodeChargeInfo(style_info, &item);
  // 实时开屏字段
  if (is_realtime_ad) {
    SetRealtimeSplashInfo(style_info, &item, splash_material_info);
  }
  if (SPDM_enableSearchSplashSetSpeed() &&
      base_info->speed() == static_cast<int32_t>(kuaishou::ad::AdEnum::UNKNOWN_SPEED_TYPE)) {
    base_info->set_speed(style_info.unit().speed());
  }
}

void SplashStylePostProc::SetJKTrackUrl(AdResult* item) {
  if (KS_UNLIKELY(nullptr == item)) {
    return;
  }
  auto* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  if (KS_UNLIKELY(nullptr == base_info)) {
    return;
  }
  const auto adx_style_info = item->ad_deliver_info().ad_base_info().adx_style_info();
  if (session_data_->IsSplashPrefetch()) {
    for (const auto& ad_common_track : adx_style_info.adx_tracks()) {
      if (ad_common_track.url().length() > 0) {
        auto ad_track_info = item->mutable_ad_deliver_info()->add_track();
        ad_track_info->set_type(kuaishou::ad::AD_SPLASH_IMPRESSION);
        ad_track_info->set_track_url_operation_type(ad_common_track.track_url_operation_type());
        ad_track_info->set_url(ad_common_track.url());
        ad_track_info->set_adx_win_notice(ad_common_track.adx_win_notice());
      }
    }
    // set h5_url
    if (adx_style_info.url().length() > 0) {
      base_info->set_url(adx_style_info.url());
    }
    // set deeplink
    if (adx_style_info.schema_url().length() > 0) {
      base_info->set_schema_url(adx_style_info.schema_url());
    }
  } else if (session_data_->IsSplashRealtime()) {
    std::string price_string;
    std::string first_price_string;
    // jk 价格格式化
    FormatJKPrice(*item, &price_string, &first_price_string);
    std::string win_notice_url = item->ad_deliver_info().ad_base_info().adx_style_info().win_notice_url();
    if (!(win_notice_url.empty())) {
      std::string new_win_notice_url{};
      base::FastStringReplace(win_notice_url, kWinPriceKeywords, price_string, true, &new_win_notice_url);
      std::string temp_win_notice_url{};
      base::FastStringReplace(new_win_notice_url, kFirstPriceKeywords, first_price_string, true,
                              &temp_win_notice_url);
      item->mutable_ad_deliver_info()->set_win_notice_url(temp_win_notice_url);
    }
  }
}

void SplashStylePostProc::SetRealTimeSplashShowType(kuaishou::ad::AdBaseInfo *base_info) {
  auto splash_info = base_info->mutable_splash_info();
  splash_info->set_splash_ad_show_type(kuaishou::ad::AdDataV2_SplashInfo::REALTIME_SHOW);
}

std::string SplashStylePostProc::AesEncrypt(const std::string& plain) {
  std::string key = "UV0GvNqjvODrqiyK";
  std::string iv = "vrsjF7RkKmIRLx6A";
  std::string cipher;
  utility::AesCrypter::Encrypt(key, iv, plain, &cipher);
  return cipher;
}

std::string SplashStylePostProc::GenerateAdxCallbackInfo(AdResult* item) {
  kuaishou::ad::CallbackInfo callback_info;
  callback_info.set_visitor_id(session_data_->get_user_id());
  callback_info.set_device_id(session_data_->get_ad_request()->ad_user_info().device_id());
  callback_info.set_photo_id(item->ad_deliver_info().ad_base_info().photo_id());
  callback_info.set_creative_id(item->ad_deliver_info().ad_base_info().creative_id());
  callback_info.set_unit_id(item->ad_deliver_info().ad_base_info().unit_id());
  callback_info.set_campaign_id(item->ad_deliver_info().ad_base_info().campaign_id());
  callback_info.set_account_id(item->ad_deliver_info().ad_base_info().account_id());
  callback_info.set_user_id(item->ad_deliver_info().ad_base_info().author_id());
  callback_info.set_llsid(session_data_->get_llsid());
  callback_info.set_time_stamp(base::GetTimestamp() / 1000);
  callback_info.set_version(item->ad_deliver_info().ad_base_info().version());
  if (callback_info.version() == 1) {
    callback_info.mutable_ad_callback_passback()->CopyFrom(
        item->ad_deliver_info().ad_base_info().ad_callback_passback());
  } else {
    callback_info.set_callback_passback(
        item->ad_deliver_info().ad_base_info().callback_passback());
  }

  std::string callback_info_string;
  std::string encode_callback_info_string;
  std::string encode_callback_info_string_nourl;
  callback_info.SerializeToString(&callback_info_string);
  base::URLSafeBase64Encode(AesEncrypt(callback_info_string), &encode_callback_info_string);
  return encode_callback_info_string;
}

std::string SplashStylePostProc::GenerateCallbackInfo(const StyleInfoItem& style_info, AdResult* item) {
  kuaishou::ad::CallbackInfo callback_info;
  callback_info.set_visitor_id(session_data_->get_user_id());
  callback_info.set_device_id(session_data_->get_ad_request()->ad_user_info().device_id());
  callback_info.set_photo_id(item->ad_deliver_info().ad_base_info().photo_id());
  callback_info.set_creative_id(style_info.creative().id());
  callback_info.set_unit_id(style_info.unit().id());
  callback_info.set_campaign_id(style_info.unit().campaign_id());
  callback_info.set_account_id(style_info.unit().account_id());
  callback_info.set_user_id(item->ad_deliver_info().ad_base_info().author_id());
  callback_info.set_llsid(session_data_->get_llsid());
  callback_info.set_time_stamp(base::GetTimestamp() / 1000);
  callback_info.set_version(item->ad_deliver_info().ad_base_info().version());
  if (callback_info.version() == 1) {
    callback_info.mutable_ad_callback_passback()->CopyFrom(
        item->ad_deliver_info().ad_base_info().ad_callback_passback());
  } else {
    callback_info.set_callback_passback(
        item->ad_deliver_info().ad_base_info().callback_passback());
  }

  std::string callback_info_string;
  std::string encode_callback_info_string;
  std::string encode_callback_info_string_nourl;
  callback_info.SerializeToString(&callback_info_string);
  base::URLSafeBase64Encode(AesEncrypt(callback_info_string), &encode_callback_info_string);
  LOG_EVERY_N(INFO, 10) << "encode string: " << encode_callback_info_string
    << " debug str, " << callback_info.ShortDebugString();
  return encode_callback_info_string;
}

void SplashStylePostProc::SetRealtimeSplashMaterialType(const StyleInfoItem& style_info, AdResult* item,
    const SplashMaterialInfo& splash_material_info) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto splash_info = base_info->mutable_splash_info();
  if (base_info->creative_material_type() ==
      kuaishou::ad::AdEnum_CreativeMaterialType_SPLASH_IMAGES) {
    splash_info->set_splash_ad_material_type(2);
  } else {
    splash_info->set_splash_ad_material_type(1);
  }
}

void SplashStylePostProc::SetRealtimeSplashInfo(const StyleInfoItem& style_info, AdResult* item,
    const SplashMaterialInfo& splash_material_info) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto splash_info = base_info->mutable_splash_info();
  auto splash_base_info = splash_info->mutable_base_info();
  if (SPDM_enableRTBEyemax() && IsRtbEyemax(style_info)) {
    splash_info->set_splash_ad_type(1);  // eyemax
    splash_info->set_is_eyemax(true);
  }
  splash_base_info->set_splash_id(std::to_string(base_info->creative_id()));
  auto splash_policy =
    session_data_->mutable_splash_effect_ad_response()->mutable_global_ext_data()->mutable_splash_policy();
  auto new_meta = splash_policy->add_splash_ad_meta_list();
  auto p_splash_info_base_info = new_meta->add_creative_info_list();
  p_splash_info_base_info->CopyFrom(*splash_base_info);


  std::string realtime_splash_str;
  kuaishou::ad::AdBaseInfo::RealtimeSplashInfo realtime_splash_info;
  realtime_splash_info.set_splash_id(std::to_string(base_info->creative_id()));
  std::string ip_v4 = session_data_->get_ad_request()->reco_user_info().location().ip();
  std::string ip_v6 = "";
  if (session_data_->get_ad_request()->has_ad_user_info() &&
      session_data_->get_ad_request()->ad_user_info().device_info_size() >= 1 &&
      session_data_->get_ad_request()->ad_user_info().device_info(0).has_ipv6()) {
    ip_v6 = Ipv6BinToString(session_data_->get_ad_request()->ad_user_info().device_info(0).ipv6());
  }
  std::string ip = ip_v4;
  if (ip.empty() && !ip_v6.empty()) {
    ip = ip_v6;
  }
  realtime_splash_info.set_ip(ip);
  realtime_splash_info.set_ip_v4(ip_v4);
  if (ip_v4.empty()) {
    realtime_splash_info.set_ip_v6(ip_v6);
  }
  if (style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
      IsNonMerchantLivePromote(style_info)) {
    realtime_splash_info.set_live_stream_ids(style_info.live_stream_user_info().live_stream_id_encrypted());
    realtime_splash_info.set_to_live_type(1);  // 跳转直播间
    realtime_splash_info.set_bind_ad_to_live(true);
    realtime_splash_info.set_bind_ad_to_live_stream_ids(
        style_info.live_stream_user_info().live_stream_id_encrypted());
    if (FrontKconfUtil::enableLiveAgent()) {
      realtime_splash_info.set_live_stream_ids(style_info.live_stream_user_info().live_stream_id_encrypted());
      realtime_splash_info.set_bind_ad_to_live_stream_ids(
          style_info.live_stream_user_info().live_stream_id_encrypted());
    }
    if (EnableServerExpTag(session_data_)) {
      realtime_splash_info.set_server_exp_tag(GetServerExpTagv2(style_info,
            session_data_->mutable_ad_request(), session_data_->get_front_server_request()->type(),
            session_data_->get_llsid()));
    }
  } else if (style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
    realtime_splash_info.set_server_exp_tag(GetServerExpTagSplashReco(
        style_info, session_data_->mutable_ad_request(), session_data_->get_front_server_request()->type(),
        session_data_->get_llsid(), splash_material_info.cover_id));
  }
  realtime_splash_info.set_charge_info(item->encode_charge_info());
  std::string callback_info_str = GenerateCallbackInfo(style_info, item);
  realtime_splash_info.set_callback_info(callback_info_str);
  if (session_data_->IsSplashRealtime() && enable_jk_track_in_splash_ &&
      base_info->unit_type() == kuaishou::ad::AdEnum_UnitType_JK_UNIT) {
    std::string price_string;
    std::string first_price_string;
    FormatJKPrice(*item, &price_string, &first_price_string);
    auto* win_price_macro = realtime_splash_info.add_replace_macro_info();
    win_price_macro->set_macro_key(kWinPriceKeywords);
    win_price_macro->set_macro_value(price_string);
    auto* first_price_macro = realtime_splash_info.add_replace_macro_info();
    first_price_macro->set_macro_key(kFirstPriceKeywords);
    first_price_macro->set_macro_value(first_price_string);
  }
  ::google::protobuf::util::MessageToJsonString(realtime_splash_info, &realtime_splash_str);
  base_info->set_realtime_splash_info(realtime_splash_str);
}

void SplashStylePostProc::ProcessRealtimeAdResult(AdResult* ad_result) {
  auto& item = *ad_result;
  ad_data_v2_.Clear();
  session_data_->set_curr_ad_data_v2(&ad_data_v2_);

  auto* style_info_map = session_data_->mutable_style_info_resp()->mutable_style_info();
  uint64_t creative_id = item.ad_deliver_info().ad_base_info().creative_id();
  auto* base_info = ad_result->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto& style_info = (*style_info_map)[creative_id];

  TargetCheck tc(*session_data_, style_info);
  tc.SetTargetTag(&item);
  tc.SetDmpTargetTag(&item);
  CreativeParser creative_parser;
  creative_parser.Parse(style_info.creative(), session_data_->get_llsid());

  CommonAdBaseInfoPack common_base_pack(session_data_, &creative_parser, &style_info, &item, nullptr);
  common_base_pack.Process();
  SetLiveReservationInfo(style_info, base_info);
  ReplaceSchemaUrlMacro(&item);
  if (!ConvertKsBackUrl(ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())), &item)) {
    AddBackUrlToSchemaUrl(&item);
  }
  SetTrackInfo(style_info, &item);
  SplashMaterialInfo splash_material_info;
  if (!IsRtbGray(style_info)) {
    SelectMaterialInfo(style_info, &item, &splash_material_info);
    SetMaterialInfo(style_info, &item, splash_material_info);
  } else {
    SelectMaterialInfoGray(style_info, &item, &splash_material_info);
    SetMaterialInfoGray(style_info, &item, splash_material_info);
  }

  common_deliver_info_pack_.Process(&style_info, &creative_parser, ad_result, &deliver_packer_params_,
      nullptr);
  dpa_base_info_pack_.Process(&style_info, session_data_, &item);
  SetCallbackPassback(style_info, &item);
  SetRealtimeSplashMaterialType(style_info, &item, splash_material_info);
  SetDerivedInfo(style_info, &item);
  MerchantMarketingAddFlags(base_info, style_info, session_data_,
      item.ad_deliver_info().ad_queue_type());
  std::string fal = "adfront.splash_promote_type_"
    + std::to_string(item.ad_deliver_info().ad_base_info().promotion_type());
  falcon::Inc(fal.c_str());
  SetIsSite(base_info, style_info);

  SetLightInteractiveRealtime(style_info, base_info);
  // 所有 deliverinfo 字段填充都放到 chargeinfo 前面
  SetEncodeChargeInfo(style_info, &item);
  SetRealtimeSplashInfo(style_info, &item, splash_material_info);
  if (SPDM_enableSearchSplashSetSpeed() &&
      base_info->speed() == static_cast<int32_t>(kuaishou::ad::AdEnum::UNKNOWN_SPEED_TYPE)) {
    base_info->set_speed(style_info.unit().speed());
  }
}

}  // namespace front_server
}  // namespace ks
