#pragma once

#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace front_server {

// SPDM 使用文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIDGfeGPH2sQAxiBwuiaJ4

// 声明 kconf 开关:  DECLARE_SPDM_KCONF_BOOL(key);  // [owner] 开关用途/作用
// 声明 abtest 开关: DECLARE_SPDM_ABTEST_BOOL(key);  // [owner] 开关用途/作用

// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.

// 以下为 kconf 开关声明.
DECLARE_SPDM_KCONF_BOOL(enableLazyInitExtraCommonAttr);
DECLARE_SPDM_KCONF_BOOL(enableRecordCrmCenter);
DECLARE_SPDM_KCONF_BOOL(enableSkipPackageIdCheck);
DECLARE_SPDM_KCONF_BOOL(enableShowAggrInfo);
DECLARE_SPDM_KCONF_BOOL(enableCmdKeyRecord)
DECLARE_SPDM_KCONF_BOOL(enableAdWxMiniApp)
DECLARE_SPDM_KCONF_BOOL(enableFixBillTime)
DECLARE_SPDM_KCONF_BOOL(enableNewCutomCheck)
DECLARE_SPDM_KCONF_BOOL(enableNearlinRequestGap)
DECLARE_SPDM_KCONF_BOOL(enableInnerCidOuterId)
DECLARE_SPDM_KCONF_BOOL(enableToXifanCpmRatio)
DECLARE_SPDM_KCONF_BOOL(disableInvokeRetetion)
DECLARE_SPDM_KCONF_BOOL(enableModelBillTime)
DECLARE_SPDM_KCONF_BOOL(enableFillField)
DECLARE_SPDM_KCONF_BOOL(enablePlayletCampaignTypeOcpxDiscountV2)
DECLARE_SPDM_KCONF_BOOL(enableTransIsAggregationStatus)
DECLARE_SPDM_ABTEST_BOOL(enable_wentou_gimbal_ratio_dynamic)  // [caijiawen] 稳投 gimbal 动态系数标志
DECLARE_SPDM_ABTEST_BOOL(enable_rta_gimbal_ratio_dynamic)  // [yuchengyuan] rta gimbal 动态系数标志
DECLARE_SPDM_KCONF_BOOL(enableIaaUpliftOri)  // [yushengkai] iaa 个性化
DECLARE_SPDM_KCONF_BOOL(enableXifanFeedAd)  // [lizemin] xifan feed
DECLARE_SPDM_KCONF_BOOL(universePreviewStyleCheck);     // [wanglei10] 联盟白盒预览增加正排数据校验
DECLARE_SPDM_KCONF_BOOL(universePreviewDisableOriginLive);     // [wanglei10] 联盟白盒预览支持原生直播
DECLARE_SPDM_KCONF_BOOL(enableFixPriceLow);  // [xiaoyan] bidding 出价计费类型修复
DECLARE_SPDM_KCONF_BOOL(enableRealTimeBounsCost);  // [xiaoyan] bidding 出价计费类型修复
DECLARE_SPDM_KCONF_BOOL(disableCacheLiveAd);  // [duxiaomeng] 客户端不缓存直播广告和粉条硬广
DECLARE_SPDM_KCONF_BOOL(enableEspBudgetPartition);  // [liming11] 磁力金牛是否使用独立的预算隔离
DECLARE_SPDM_KCONF_BOOL(enableOnlyEspBudgetPartition);  // [liming11] 磁力金牛是否只使用独立的预算隔离
DECLARE_SPDM_KCONF_BOOL(enableInsteadAssert);          // [wangjiabin05] 移动专推字段填充
DECLARE_SPDM_KCONF_BOOL(enableSendInnerDspAckDataFlow);  // [wangjiabin05] 内循环新版本物料流
DECLARE_SPDM_KCONF_BOOL(enableSetLocalLifePromotionStyle);  // [wangjiabin05] 本地门店私信智能优选填充
DECLARE_SPDM_KCONF_BOOL(enableEspOrderInSpecialtyFillFanstop);          // [wangjiabin05] 移动专推字段填充
DECLARE_SPDM_KCONF_BOOL(enableEspNoMobileOrderInSpecialtyFillFanstop);  // [wangjiabin05] 专推字段填充
DECLARE_SPDM_KCONF_BOOL(enableSetZhuitouData);          // [wangjiabin05] 透传素材追投字段
DECLARE_SPDM_KCONF_BOOL(enableFillRoiOrder);          // [wangjiabin05] 支持全站锁预算
DECLARE_SPDM_KCONF_BOOL(enableStorewideGuaranteed);          // [wangjiabin05] 全站保送
DECLARE_SPDM_KCONF_BOOL(enableFillMerchantUserTag);          // [wangjiabin05] 填充商业化用户标签
DECLARE_SPDM_KCONF_BOOL(enableSetLlsidForSample);          // [wangjiabin05] 样本设置 llsid
DECLARE_SPDM_KCONF_BOOL(enableSetZhuitouInfo);          // [wangjiabin05] 设置追投信息
DECLARE_SPDM_KCONF_BOOL(enableSetNeedComponentItem);       // [wangjiabin05] 是否设置分域
DECLARE_SPDM_KCONF_BOOL(enableSetFeaLocalLifePromotionStyle);       // [luyuanquan] 是否下发门店智能优选特征
DECLARE_SPDM_KCONF_BOOL(enableLiveCopyQueueTypeFixV2);  // 直播队列优化修复
DECLARE_SPDM_KCONF_BOOL(enableLiveCopyQueueTypeFix);  // 直播队列优化修复
DECLARE_SPDM_KCONF_BOOL(enableCplOnlineJoin);   // [linyuhao03] cpl 流量启用 onlinejoin
DECLARE_SPDM_KCONF_BOOL(enableCplAck);          // [wangjiabin05] cpl 是否发送 ack
DECLARE_SPDM_KCONF_BOOL(enableSetEnvInfo);      // [wangjiabin05] 是否设置环境信息
DECLARE_SPDM_KCONF_BOOL(enableSetKeepWebView);          // [wangjiabin05] 是否设置 web view
DECLARE_SPDM_KCONF_BOOL(enableSetImInfo);       // [wangjiabin05] 是否填充快手号私信信息
DECLARE_SPDM_KCONF_BOOL(enableCheckRegionV2);   // [yangyifan10] 是否使用新的 region code 填充逻辑
DECLARE_SPDM_KCONF_BOOL(enableLiveAdAddTrack);        // [wangjiabin05] 是否新增直播相关监测链接
DECLARE_SPDM_KCONF_BOOL(enableCplRequestForMainChain);    // [wangjiabin05] cpl 流量准入
DECLARE_SPDM_KCONF_BOOL(enableFixSameLiveCopy);    // [wangjiabin05] 修复直播队列同创意问题
DECLARE_SPDM_KCONF_BOOL(enableAdMixRankTopMixer);        // [wangjiabin05] 是否开启混排 top 选取功能
DECLARE_SPDM_KCONF_BOOL(enableRemoveTraceLogCopy);        // [wangjiabin05] 是否禁用 tracelog 拷贝功能
DECLARE_SPDM_KCONF_BOOL(enableFillPageReportInfo);        // [wangjiabin05] 是否填充建站上报信息
DECLARE_SPDM_KCONF_BOOL(enablePaidDuanjuModifyConversionType);        // [wangjiabin05] 是否修改短剧转化类型
DECLARE_SPDM_KCONF_BOOL(enableLawModifyConversionType);        // [wangjiabin05] 是否修改法律转化类型
DECLARE_SPDM_KCONF_BOOL(enableRemoveOnlineJoinPriceRecord);   // [wangjiabin05] onlinejoin 不下发 pricerecord
DECLARE_SPDM_KCONF_BOOL(disablePriceRecordForOrigin);  // [wangjiabin05] 原始出价不计 record
DECLARE_SPDM_KCONF_BOOL(enablePrintAuctionDebugInfo);        // [wangjiabin05] 是否打印竞价 debug
DECLARE_SPDM_KCONF_BOOL(enableUserContextAllStation);        // [wangjiabin05] 未胜出 ack 信息是否填充用户信息
DECLARE_SPDM_KCONF_BOOL(enableSendDspAckDataFlowAllStationV2);   // [wangjiabin05] 是否发送未胜出 ack 信息
DECLARE_SPDM_KCONF_BOOL(enableTransReservationIds);
DECLARE_SPDM_KCONF_BOOL(enableSetAdSmartOffersInfo);        // [wangjiabin05] 是否填充 c 补信息
DECLARE_SPDM_KCONF_BOOL(enableSetCommonSmartOffersInfo);    // [wangjiabin05] 是否填充通用 c 补信息
DECLARE_SPDM_KCONF_BOOL(enableSetNativeDiffAuthorFlag);    // [jiangjinling] 是否填充 is_outer_diff_author_id
DECLARE_SPDM_KCONF_BOOL(enableFixFictionPanelPriceRct);    // [jiangjinling] 定价模型 rct 流量屏蔽定价
DECLARE_SPDM_KCONF_BOOL(forbidFictionPanelPriceRct);    // [jiangjinling] 定价模型 rct 流量屏蔽
DECLARE_SPDM_KCONF_BOOL(enableFictionReportExtData);   // [jiangjinling] 是否允许小说策略字段上报
DECLARE_SPDM_KCONF_BOOL(enableFictionReportNovelPrice);   // [jiangjinling] 小说营销价格字段上报
DECLARE_SPDM_KCONF_BOOL(enableFictionBookStrategyHighPriority);
DECLARE_SPDM_KCONF_BOOL(enableADBoxBrowsedDuplicate);   // [liwnei06] 广告盒子去重
DECLARE_SPDM_KCONF_BOOL(disableChargeInDuitou);  // [liqinglong] 本地对投不计费
DECLARE_SPDM_KCONF_INT32(maxADServerShowHour);       // [linwei06]
DECLARE_SPDM_KCONF_BOOL(enableFixFictionPriceTagGroupTag);
DECLARE_SPDM_KCONF_BOOL(enableReportAllFictionPrice);
DECLARE_SPDM_KCONF_BOOL(enableFictionRandomPriceRandomStyle);

DECLARE_SPDM_KCONF_BOOL(enablePriceRb);             // [wangjiabin05] 样式填充使用 price rb
DECLARE_SPDM_KCONF_BOOL(enablePreInstallFilterFor2024);     // [wangjiabin05]  2024 预装包过滤
DECLARE_SPDM_KCONF_BOOL(enablePreInstallFilterFor2025);     // [wangjiabin05]  2025 预装包过滤
DECLARE_SPDM_KCONF_BOOL(enableFillAigcInfo);     // [wangjiabin05]  端上白盒是否填充 agic
DECLARE_SPDM_KCONF_BOOL(enableFillItemStid);             // [wangjiabin05] 是否填充 item type stid id
DECLARE_SPDM_KCONF_BOOL(enableFillBsLive);             // [wangjiabin05] 是否填充建站小钥匙信息
DECLARE_SPDM_KCONF_BOOL(enableAllAdTargetBreakDelete);          // [wangjiabin05] 软广非法定向删除开关
DECLARE_SPDM_KCONF_BOOL(enableTargetBreakDelete);          // [chengyuxuan] 非法定向删除开关
DECLARE_SPDM_KCONF_BOOL(enableUniversePreviewStyleByKconf);  // [wanglei10] 联盟白盒预览优先出 kconf 配置模板
DECLARE_SPDM_KCONF_BOOL(enableSplashCidReplaceSchema);       // [chenqi07] 替换 schema url 宏
DECLARE_SPDM_KCONF_BOOL(enableDeleteAggrePageOldLink);  // [liuyibo] 删除聚合中间页旧链路
DECLARE_SPDM_KCONF_BOOL(enableAccIncrementExploreSign);  // [tangweiqi] 加速探索增量标记开关
DECLARE_SPDM_KCONF_BOOL(enableIncrementExploreSkipBs);  // [tangweiqi] 加速探索增量计费跳过计费分离开关
DECLARE_SPDM_KCONF_BOOL(enableProjectBidSend);  // [weizhiyong03] 增加字段下发
DECLARE_SPDM_KCONF_BOOL(enable_soft_ad_disable_ad_mark_with_review_fix);  // [jiyang] 优质软广判定规则修复
DECLARE_SPDM_KCONF_BOOL(enableEspRegionAb);  // [heqian] 御风金牛实验
DECLARE_SPDM_KCONF_BOOL(enableSimplifyDiscountPrice);  // [heqian]
DECLARE_SPDM_KCONF_BOOL(disableAllPerf);  // [heqian] perf 总开关
DECLARE_SPDM_KCONF_BOOL(fixSplashRtaLlsidAtoi);  // [zhangzhicong] 修复开屏 rta llsid 转换
DECLARE_SPDM_KCONF_BOOL(enableUniversePlayT10sUrlEndUrl);  // [tanghaihong] 联盟 10s 监测链接 + 完播链接开关
DECLARE_SPDM_KCONF_BOOL(enableUniverseFillSixinConversionInfo);  // [tanghaihong] 联盟填写私信 conversion_info
DECLARE_SPDM_KCONF_BOOL(enableUpdateTrace);  // [sunkang] trace 日志更新
DECLARE_SPDM_KCONF_BOOL(enableUpdateDebug);  // [sunkang] 端上白盒更新
DECLARE_SPDM_KCONF_BOOL(enableSetBrandSplashSensitiveGroup);  // [zhangzhicong] 填充开屏用户正排数据
DECLARE_SPDM_KCONF_BOOL(enableTransferLifeExtendFileds);  // [jiangyuzhen03] 外循环衰退素材微改字段透传
DECLARE_SPDM_KCONF_BOOL(enableSearchSuperCardType);  // [zhangzhicong] 搜索超级样式开关
DECLARE_SPDM_KCONF_BOOL(enableSetHostInfoForServerShow);  // [zhangzhicong] server show 填充 hostinfo
DECLARE_SPDM_KCONF_BOOL(enableAlwaysLogSetStageAz);  // [zhangzhicong] always log 填充 az 信息
DECLARE_SPDM_KCONF_BOOL(enableTubeInspireSetAdClientInfo);  // [zhangzhicong] 短剧激励填充 ad_client_info
DECLARE_SPDM_KCONF_BOOL(enableCIDCardRemoveSwitch);

// [baizongyao] 广告位切换兜底 kconf 开关
DECLARE_SPDM_KCONF_BOOL(enableUseKconfPosInfo);
DECLARE_SPDM_KCONF_BOOL(enablePrintAllFactorInfo);  // [wangjiabin05] 是否打印所有因子信息
DECLARE_SPDM_KCONF_BOOL(enableSetAdjustPriceRecord);  // [wangjiabin05] 设置计费调价记录
DECLARE_SPDM_KCONF_BOOL(enableSkipSimpleNoDsp);  // [wangjiabin05] 不请求 adserver 时不采样
DECLARE_SPDM_KCONF_BOOL(enableFixQueueTypeForGalaxyPreview);  // [wangjiabin05] 修复万合体验问题
DECLARE_SPDM_KCONF_BOOL(enablePreviewUserFanstop);  // [wangjiabin05] 粉条预览使用软广队列
DECLARE_SPDM_KCONF_BOOL(enableUseDebugPageId);  // [baizongyao] debug_key 统一使用 page_id 前缀
DECLARE_SPDM_KCONF_BOOL(enableAllScene);
DECLARE_SPDM_KCONF_BOOL(enableLogTvCopyright);
DECLARE_SPDM_KCONF_BOOL(enableCoverMicroApp);
DECLARE_SPDM_KCONF_BOOL(enableFixRtbBouns);   // [xiaoyan] 修复 rtb 补贴的下发字段
DECLARE_SPDM_KCONF_BOOL(enableUniverseRtbCpmBouns);   // [pengzhengbo03] 基于目标 ecpm 的 rtb 补贴开关
DECLARE_SPDM_KCONF_BOOL(enableAliOuterDeviceInfo)
// [xianglinbo] 是否允许阿里外投 win_notice_url 填充设备信息
DECLARE_SPDM_KCONF_BOOL(enableRtaFilterInfoLog);  //  [lizemin] rta 请求过滤原因写 alwayslog
DECLARE_SPDM_KCONF_BOOL(enableNewInnerFanstopStid);  // [chenqi07] 新内粉的 stid 填充逻辑与老内粉保持一致
// [zhaizhiqiang]
DECLARE_SPDM_KCONF_BOOL(enableInspireMixDoubleTag);
DECLARE_SPDM_KCONF_BOOL(enableTotalFanstopDarkChargeTag);
DECLARE_SPDM_KCONF_BOOL(enableNewAdExposedInfo)  // [lizemin] 新版广告披露工具
DECLARE_SPDM_KCONF_BOOL(enableNodeTimeCost)  // [lizemin] 节点耗时写 trace
DECLARE_SPDM_KCONF_BOOL(enableLpComponent)  // [zhanshenxin] 下发部分落地页组件标志
DECLARE_SPDM_KCONF_BOOL(enableLpComponent3)  // [zhanshenxin] 下发部分落地页组件标志
DECLARE_SPDM_KCONF_BOOL(disableMinProgramCoverUrlReplace)  // [liulong03] 删除 redis url 替换操作
DECLARE_SPDM_KCONF_BOOL(enableBrandHeaderUA);  // [chudawei] header 中携带 UA 信息
DECLARE_SPDM_KCONF_BOOL(enableUniverseStrictNativeAd);  // [zhangmengxin] 联盟 下发原生字段
DECLARE_SPDM_KCONF_BOOL(enableUniverseBannerAds);  // [tanghaihong] 联盟支持 banner 广告

DECLARE_SPDM_KCONF_BOOL(enableSearchSplashSetSpeed);  // [chudawei] 是否设置搜索 & 开屏视频 speed
DECLARE_SPDM_KCONF_BOOL(enableSearchProductShowSellerIdFixV2);  // [weiyilong] 修复短视频商品商家 id v2
DECLARE_SPDM_KCONF_BOOL(enableSplashAdxPackOnlineJoinParams);  // [zhangzhicong]开屏 adx 填充 OnlineJoinParams
DECLARE_SPDM_KCONF_BOOL(enableSplashNewAndRefluxDeviceFilterV2);  // [zhangzhicong]开屏新回用户屏蔽 2.0
DECLARE_SPDM_KCONF_BOOL(enableBrandTubeOrderPriceControl);  // [zhangzhicong]品牌短剧订单价格成本控制

DECLARE_SPDM_ABTEST_STRING(search_age_wise_throttling_group);     // [gaokaiming] search ads

// [zhaoyi13]
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_user_interest_tags);    // 用户兴趣题材标签下发

// [zengdi] cid 策略
DECLARE_SPDM_ABTEST_BOOL(enable_cid_use_goods_type_support);    // [zengdi] cid 打折策略 v2 开关

DECLARE_SPDM_KCONF_BOOL(enableTubeInspireBrandLogCheckFakeType);  // [weiyilong] 短剧激励 IAA 品牌检查压测流量
DECLARE_SPDM_KCONF_BOOL(enableFillComboPackageSubSourceType);  // [zhouting03] 省心投字段下发
DECLARE_SPDM_KCONF_BOOL(enableBrandDuanjuCostToRedis);   // [weiyilong] 短剧激励 IAA 品牌分成金额累计到 Redis
DECLARE_SPDM_KCONF_BOOL(enableSendTubeInspireBrandLog);  // [weiyilong] 短剧激励 IAA 发送品牌分成金额
DECLARE_SPDM_KCONF_BOOL(enableInspireBrandReqSceneFilter);  // [weiyilong] 激励品牌判断 requestSceneType
DECLARE_SPDM_KCONF_BOOL(enableInspireBrandPk);  // [weiyilong] 激励品牌 pk
DECLARE_SPDM_KCONF_BOOL(enableSearchkeepDeleteNativePos);  // [linwei06] 保留被删除软广 pos gap
DECLARE_SPDM_KCONF_BOOL(enableSearchTraceHotQuery);  // [weiyilong] 搜索高商 Query 提高采样率
DECLARE_SPDM_KCONF_BOOL(enableAdServerTimeToServerShow);  // [weiyilong] 调用 adServer 情况落表
DECLARE_SPDM_KCONF_BOOL(enableUpdateSearchSampleTag);  // [chudawei] 支持搜索精排样本缺失修复的实验
DECLARE_SPDM_KCONF_BOOL(enableSplitShelfMerchant);  // [xiaoyuhao] 货架电商流量拆分
DECLARE_SPDM_KCONF_BOOL(enableSendEspBaseInfo);  // [xiaoyuhao] 下发 esp 相关字段
DECLARE_SPDM_KCONF_BOOL(enableSecondForwardIndex);  // [xiaoyuhao] 二次请求物料正排
DECLARE_SPDM_KCONF_BOOL(enableGuessYouLikeMixRankReq);  // [xiaoyuhao] 猜喜请求 live-target
DECLARE_SPDM_KCONF_BOOL(enableGuessYouLikeLiveCard);  // [xiaoyuhao] 猜喜出直播卡
DECLARE_SPDM_KCONF_BOOL(enableGuessYouLikeBuildDelivery);  // [xiaoyuhao] 猜喜下发 build_delivery
DECLARE_SPDM_KCONF_BOOL(enableGuessYouLikeLiveCardProduct);  // [xiaoyuhao] 猜喜请求 live-target 商详
DECLARE_SPDM_KCONF_BOOL(enableGuessYouLikeLiveCardOther);  // [xiaoyuhao] 猜喜请求 live-target 非商详
DECLARE_SPDM_KCONF_BOOL(enableKuaishuoNovelAd);  // [xiaoyuhao] 主版小说下发广告
DECLARE_SPDM_KCONF_BOOL(enableVideoIncomeTaskAd);  // [xiaoyuhao] 看视频提收任务下发广告
DECLARE_SPDM_KCONF_BOOL(enableDrawFlowAd);  // [xiaoyuhao] draw 流准入
DECLARE_SPDM_ABTEST_BOOL(enable_tube_inner_loop_by_campaign_type);  // [yushengkai] 短剧内循环化切换口径
DECLARE_SPDM_KCONF_BOOL(enableSendIaaSourceType);  // [xiaoyuhao] 下发 iaa 小说入口字段
DECLARE_SPDM_KCONF_BOOL(disableXiFanInnerLoop);  // [xiaoyuhao] 屏蔽喜番请求内循环 photo target
DECLARE_SPDM_KCONF_BOOL(disableXiFanInnerLoopLive);  // [xiaoyuhao] 屏蔽喜番请求内循环 live target
DECLARE_SPDM_KCONF_BOOL(enableXiFanReplaceKwaiUrl);  // [xiaoyuhao] 喜番替换快链 schema_url
DECLARE_SPDM_KCONF_BOOL(enableSerialAggrCard);  // [xiaoyuhao] Feed 大卡开关
DECLARE_SPDM_KCONF_BOOL(enableCommonCardSecondRequest);  // [xiaoyuhao] 商业化大卡二次请求开关
DECLARE_SPDM_KCONF_BOOL(enableSendRtaSecondBid);  // [xiaoyuhao] Rta 二次出价宏替换
DECLARE_SPDM_KCONF_BOOL(enableUseNewPressTestFlag);  // [xiaoyuhao] 使用新压测标识
DECLARE_SPDM_KCONF_BOOL(enableInnerExploreMixPriceAdjust);  // [duanxinning] 发现页内流混排透传二价率  //NOLINT
DECLARE_SPDM_KCONF_BOOL(enableFixSetMixPriceAdjustRatio);  // [duanxinning] 发现页内流混排透传二价率 fix  //NOLINT
DECLARE_SPDM_KCONF_BOOL(enableFeedExploreMixPriceAdjust);  // [duanxinning] 发现页外流混排透传二价率  //NOLINT
DECLARE_SPDM_KCONF_BOOL(enableFeedExploreMixExperienceValue);  // [duanxinning] 发现页外流混排体验分  //NOLINT

DECLARE_SPDM_KCONF_BOOL(enableAbtestMappingIdService);  // [zhangzhicong] 请求 AbtestMappingId 服务开关
DECLARE_SPDM_KCONF_BOOL(enableSearchTransferLifeExtendFileds);  // [zhangzhicong] 外循环衰退素材微改字段透传
DECLARE_SPDM_KCONF_BOOL(enableSendPayModeToMix);  //  [shoulifu03] 透传一些字段到混排
DECLARE_SPDM_KCONF_BOOL(enableFixFillMixedInfo);  //  [shoulifu03] 修复 mixed_info 填充问题
DECLARE_SPDM_KCONF_BOOL(enableSplashWeiliang);  //  [duantao]  品牌尾量广告开关
DECLARE_SPDM_KCONF_BOOL(enableT7ROISkipBillingSeparate);  // [wangtao21] roi7 跳过计费分离
DECLARE_SPDM_KCONF_BOOL(enableAdMerchantStrategyV2);  // [wangtao21] adMerchantConf 支持多 state_name
DECLARE_SPDM_KCONF_BOOL(enableUniverseSmallGameDirectCall);  //  [shanminghui] 允许联盟直跳小游戏
DECLARE_SPDM_KCONF_BOOL(enableTransAdForceRecoTag);  //  [shoulifu03] 强出 tag 落混排日志
DECLARE_SPDM_KCONF_BOOL(enableMixRankMonitorData);  //  [shoulifu03] 透传混排监控需要的数据
DECLARE_SPDM_KCONF_BOOL(enableFillAggBiddingRelease);  // [wangyangrui] 聚合明测开关
DECLARE_SPDM_KCONF_BOOL(enableUseAggrCpmRatioForPrice);  // [zhaijianwei] 聚合竞价计费策略升级开关
DECLARE_SPDM_KCONF_BOOL(enableAggregationBiddingDebugLog);  // [wangyangrui] 聚合明测 测试环境打点开关
DECLARE_SPDM_KCONF_BOOL(enableSearchBrandOneAdList);  // [zhangzhicong] enable_search_brand_one_ad_list 参数推全  //NOLINT
DECLARE_SPDM_KCONF_BOOL(enableFillInnerExploreUserTag);  // [duanxinning] 发现页用户标签落 ad_log_full  //NOLINT
DECLARE_SPDM_KCONF_BOOL(enableHarmonyDeviceInBrand);  // [zhangzhicong] 鸿蒙流量请求品牌开关
DECLARE_SPDM_KCONF_BOOL(enableFetchGpmFeatureForMixRank);  // [xiaoyuchao] 获取精排 GPM 用于混排特征落盘开关
DECLARE_SPDM_KCONF_BOOL(enableMatrixSplashAddUaWebview);  // [zhangzhicong] 矩阵开屏 divice_info 填充 UA
DECLARE_SPDM_KCONF_BOOL(enableBrandPreviewSkipMixRank);   // [zhangzhicong] 品牌体验用户跳过混排
DECLARE_SPDM_KCONF_BOOL(zeroCopyRequestData);  // [jiangyuzhen03] 零拷贝
DECLARE_SPDM_KCONF_BOOL(enableFrontAdxSellerAndSidList);  // [sunmingjie] ADX 商家字段和语义字段
DECLARE_SPDM_KCONF_BOOL(enableFrontZcAuthorListHourly);  // [wangpeng16] 7 日种草数据 小时表
// ------------------------------ kconf int32 参数定义 ---------------------------------
DECLARE_SPDM_KCONF_INT32(requestDebugInfoRatio);  // [baizongyao] debug 采样比率
DECLARE_SPDM_KCONF_INT32(antiMicorAppExpireSec);  // [baizongyao] 小程序反作弊 key 过期时间
DECLARE_SPDM_KCONF_INT32(maxAntiMicorAppLength);  // [baizongyao] redis list 最大长度
DECLARE_SPDM_KCONF_INT32(allStationDataFlowRate);  // [wangjiabin05] ack 全站特征
DECLARE_SPDM_KCONF_INT64(cplReturnNum);  // [wangjiabin05] cpl 请求返回数
DECLARE_SPDM_KCONF_INT32(negativeRestrictTime);  // [liuxiaoyan] 负反馈不出广告时长
DECLARE_SPDM_KCONF_INT32(maxUniverseClickRewardCntOneDay);  // [zhangyunhao03] 联盟点击激励广告每日频控
DECLARE_SPDM_KCONF_INT32(maxUniverseClickRewardCntOneHour);  // [zhangyunhao03] 联盟点击激励广告每小时频控
DECLARE_SPDM_KCONF_BOOL(enableLogAdStid);  // [baizongyao] stid 标识
DECLARE_SPDM_KCONF_INT32(direct_merchant_discount_exp_tag);  // [sixianbo] 淘系直投打折实验 tag
DECLARE_SPDM_KCONF_INT32(bidGimbalTailNumber);  // [zhaoziyou03] bid_gimbal 生效尾号
// ------------------------------ kconf double 参数定义 ---------------------------------
DECLARE_SPDM_KCONF_INT64(minPriceBound);  // [heqian] min_max price ratio bound
DECLARE_SPDM_KCONF_INT64(maxPriceBound);  // [heqian] min_max price ratio bound

DECLARE_SPDM_ABTEST_STRING(force_product_set_tag);  // [lining] 产品强出优质素材实验
DECLARE_SPDM_ABTEST_STRING(rank_dragon_update_exp_tag);
DECLARE_SPDM_KCONF_BOOL(enableTraceLogKeySplit);  //   tarce log key 区分软硬广
DECLARE_SPDM_KCONF_BOOL(enableRTBEyemax)  // [zhangzhicong] 是否下发 RTB eyemax
DECLARE_SPDM_KCONF_BOOL(enableSearchAdWxMiniApp);       // [zhaodi] 搜索广告微信小程序开关
DECLARE_SPDM_KCONF_BOOL(enableMatchServerResMigDiff);       // [zhaodi] MatchServerReq 构造上移 diff 开关
DECLARE_SPDM_KCONF_BOOL(enableSearchAdSeriesKbox);  // [qianyangchao03] 搜索广告接入短剧 kbox
DECLARE_SPDM_KCONF_BOOL(enableSearchAdMiniGameKbox);  // [qianyangchao03] 搜索广告接入小游戏 kbox
DECLARE_SPDM_KCONF_BOOL(enableSearchAdIsSingleStyle);  // [qianyangchao03] 搜索广告透传单列信号
DECLARE_SPDM_KCONF_BOOL(enableGoodsTabFilterCheckInNewWay);  // [qianyangchao03] 搜索商品 tab 判断采用新形式开关  // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableFanstopBsOcpxBlacklist);  // [jiayalong] 粉条计费分离优化目标屏蔽
DECLARE_SPDM_KCONF_BOOL(enableMinMaxDiscountPriceLog);  // [heqian]
DECLARE_SPDM_KCONF_BOOL(enableInnerPhotoItemTargetRequest);  // [zhangruyuan] inner_photo target 货架开关
DECLARE_SPDM_ABTEST_BOOL(enable_inner_photo_item_target_req);  // [zhangruyuan] inner_photo target 货架开关
DECLARE_SPDM_KCONF_BOOL(enableSplitAdServerAttr);  // [jiangyuzhen03] 拆分 ad-server attr
DECLARE_SPDM_KCONF_INT32(annRouterPrefetchLatency);
DECLARE_SPDM_KCONF_BOOL(enableResidenceAdcodeTarget);   // [limiaochen] 居住地域定向
// rank 上移阶段开关，优先级高于 ab 开关
// 1. ad_resp 构造上移 nodiff
// 2. ad_resp 构造上移 nodiff && 切换
// 3. ad_resp 构造上移 切换
// 4. 请求 rank 上移
// 5. 请求京快上移
DECLARE_SPDM_KCONF_INT32(enableRankMigrationStageDefault);  // [jiangyuzhen03] rank 上移(default) // NOLINT
DECLARE_SPDM_KCONF_INT32(enableRankMigrationStageSearch);  // [jiangyuzhen03] rank 上移(search) 不含阶段 5 // NOLINT
DECLARE_SPDM_KCONF_INT32(enableRankMigrationStageSplash);  // [jiangyuzhen03] rank 上移(splash) // NOLINT
DECLARE_SPDM_KCONF_INT32(rankMigrationAdServerTimeoutAdjust);  // [jiangyuzne03] rank 上移 ad-server 超时调整(default) // NOLINT
DECLARE_SPDM_KCONF_INT32(rankMigrationAdRankTimeoutAdjust);  // [jiangyuzne03] rank 上移 ad-rank 超时调整(default) // NOLINT

DECLARE_SPDM_KCONF_BOOL(enableLspSetSchemaUrl);  // [jiangyuznen03] 本地推透传 uri 到 schema_url
DECLARE_SPDM_KCONF_BOOL(enableYiTianResourceId);  // [wanglei39] dsp 明投版位新增一甜
DECLARE_SPDM_KCONF_BOOL(enableOpenHarmonyReq);  // [wanglei39] 打开鸿蒙请求处理
DECLARE_SPDM_KCONF_BOOL(disableNebulaFollowOuter);  // [wanglei39] 关闭极速版关注页外流请求
DECLARE_SPDM_KCONF_BOOL(disableNebulaFollowLiveInnerCardEntry);  // [wanglei39] 关闭关注页直播内流请求
DECLARE_SPDM_KCONF_BOOL(enableBonusDotPerf);  // [wanglei39] bonus 打点
DECLARE_SPDM_KCONF_BOOL(enableDiffCompareAd);  // [wanglei39] diff 比较
DECLARE_SPDM_KCONF_INT32(e2eAvailabilityTimeoutMsDelta);  // [jiangyuzhen03] front 端到端可用性 timeout 余量  //NOLINT
DECLARE_SPDM_KCONF_INT32(e2eAvailabilityTimeoutMsDeltaOld);  // [jiangyuzhen03] front 端到端可用性 timeout 余量  //NOLINT
// [limiaochen] 修复 front 定向 hit 输出
DECLARE_SPDM_KCONF_BOOL(enableFixFreAdcodeOutput);
DECLARE_SPDM_ABTEST_INT64(common_card_style_ab);
DECLARE_SPDM_KCONF_BOOL(enableHardAdRerankUseAllPeople);  // [wangwenguang] 硬广短视频二次请求打开全人群
DECLARE_SPDM_KCONF_INT32(adMixModelItemSetTopKNum);  // [sunsong] 混排模型 feature item set top k 数量
// 以下为 abtest 开关声明.
DECLARE_SPDM_ABTEST_BOOL(enable_search_series_card_fix_v2);  // [weiyilong] 搜索短剧强样式 adDataV2 标修复
DECLARE_SPDM_ABTEST_INT64(fanstop_browse_info_days);  // [limingxi] 粉条浏览天数
DECLARE_SPDM_ABTEST_BOOL(enable_industry_hc_pid_adjust);  // [zhoushuaiyin] 行业 hc pid 控制
DECLARE_SPDM_ABTEST_BOOL(enable_search_series_list_card);  // [duantao] 短剧列表强样式二期

DECLARE_SPDM_ABTEST_INT64(disable_ad_mark_ad_default_pos);  // [zhoushuaiyin] 优质软广队列默认 pos
DECLARE_SPDM_ABTEST_INT64(disable_ad_mark_ad_quota_gomara);  // [zhoushuaiyin] 优质软广队列极速版 quota
DECLARE_SPDM_ABTEST_INT64(disable_ad_mark_ad_quota_nebula);  // [zhoushuaiyin] 优质软广队列精选页 quota
DECLARE_SPDM_ABTEST_BOOL(enable_disable_ad_mark_ad_quota);  // [zhoushuaiyin] 优质软广队列开关

DECLARE_SPDM_ABTEST_BOOL(enable_kxy_subsidy_global_nc);  // [jiangnan07] 快小游 c 补全局首充用户
DECLARE_SPDM_ABTEST_BOOL(enable_compat_mini_app_id);  // [yanfeng] 兼容小游戏 id 获取
DECLARE_SPDM_ABTEST_BOOL(enable_small_game_get_reco_user_info);  //  [sunkang] 快小游获取 reco user info 开关
DECLARE_SPDM_ABTEST_BOOL(enable_pack_stable_category_thrid_id);  // [zhoushuaiyin] 三级类目填充
DECLARE_SPDM_ABTEST_BOOL(enable_cpm_delta_ratio_tag);  // [zhangyameng] 是否开启 cpm_delta_ratio 个性化设置
DECLARE_SPDM_ABTEST_BOOL(enable_cid_magic_site_replace);  // [zhoushuaiyin] cid 替换实验
DECLARE_SPDM_ABTEST_BOOL(enable_cid_magic_site_replace_v2);  // [zhoushuaiyin] cid 替换实验
DECLARE_SPDM_ABTEST_BOOL(enable_wechat_company_add_fans_direct_jump);  // [lizemin] 企微加粉直跳开关
DECLARE_SPDM_ABTEST_BOOL(enabel_universe_biddding_cpm);  // [xiaoyan]  是否开启 bidding 个性化门槛系数
DECLARE_SPDM_ABTEST_BOOL(enable_universe_bouns_cost_real_time);  // [xiaoyan]  是否开启 bidding 个性化门槛系数
DECLARE_SPDM_ABTEST_BOOL(enable_universe_cpm_bound_fix);  // [zhangyunhao03] cpm 门槛修复
DECLARE_SPDM_ABTEST_BOOL(enable_remove_universe_cpm_floor_filter);  // [xiecong03] 去除门槛过滤
DECLARE_SPDM_ABTEST_BOOL(enable_fix_random_charge);  // [xiecong03] 修复随机计费
DECLARE_SPDM_ABTEST_DOUBLE(universe_cpm_filter_whole);  // [xiecong03] 大盘 cpm 门槛过滤分级实验
DECLARE_SPDM_ABTEST_BOOL(enable_universe_profit_flow_explore);  // [xiaoyan]  利润补贴打折实验
DECLARE_SPDM_ABTEST_BOOL(enable_rtb_presonalized_upgrade);  // [xiaoyan]  利润补贴打折实验
DECLARE_SPDM_ABTEST_STRING(universe_ranking_cache_read_plugin_types);   // [yinliang]  读缓存准入插件
DECLARE_SPDM_ABTEST_STRING(universe_ranking_cache_write_plugin_types);  // [yinliang]  写缓存准入插件
DECLARE_SPDM_ABTEST_BOOL(enable_universe_rtb_bouns_upgrade);  // [xiaoyan]  rtb 补贴竞胜率升级
DECLARE_SPDM_ABTEST_BOOL(enable_universe_rtb_generic_cpm_bonus);  // [pengzhengbo03]  是否开启 rtb 通补
DECLARE_SPDM_ABTEST_BOOL(enable_cpm_explore_tag);      // [zhangyameng] 判断 cpm 是否低于个性化设置
DECLARE_SPDM_ABTEST_BOOL(small_game_ad_force_direct_call_revert);  // [wuyonghong] 小游戏强制免直跳
DECLARE_SPDM_ABTEST_BOOL(enable_guided_feedback_style_id_1)    // [mengfangyuan] 引导式负反馈样式 1
DECLARE_SPDM_ABTEST_BOOL(enable_guided_feedback_style_id_2)    // [mengfangyuan] 引导式负反馈样式 2
DECLARE_SPDM_ABTEST_BOOL(enable_convert_exp_tag)    // [mengfangyuan] 未转化人群样式实验
DECLARE_SPDM_ABTEST_BOOL(enable_guided_feedback_soft_ad_photo)    // [mengfangyuan] 优质软广下发引导式负反馈
DECLARE_SPDM_ABTEST_BOOL(disable_product_distinct_filter);    // [mengfangyuan] 产品名去重
DECLARE_SPDM_ABTEST_BOOL(enable_product_distinct_filter_fix);    // [mengfangyuan] 产品名去重实验
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_stid);  // [yushengkai]
DECLARE_SPDM_ABTEST_BOOL(enable_target_distance_check);  // [zhangruyuan]
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_cbu_roi_by_unify_ltv);
DECLARE_SPDM_ABTEST_STRING(adjust_unify_ltv_range);
DECLARE_SPDM_ABTEST_BOOL(enable_stop_cbu_by_unify_ltv);
DECLARE_SPDM_ABTEST_BOOL(ad_box_thanos_interactive_form_update);  // [wanglei10] 盒子单列切换 interactive_form
DECLARE_SPDM_ABTEST_BOOL(search_thanos_change_adx_charge_point_v2);  // [weiyilong] 盒子单列切换 adx 计费点 v2
DECLARE_SPDM_ABTEST_DOUBLE(unify_ltv_range_for_cbu);
DECLARE_SPDM_ABTEST_BOOL(adbox_quick_search_ad_scene_update);  // [wanglei10] 广告盒子 快投 ad_scene 修改
DECLARE_SPDM_ABTEST_BOOL(enable_stid_inner_exclude_fanstop);  // [liubingqi05] 内循环 stid 不包含粉条
DECLARE_SPDM_ABTEST_BOOL(enable_stid_inner_exclude_fanstop_v2);  // [liubingqi05] 内循环 stid 不包含粉条 v2

DECLARE_SPDM_ABTEST_BOOL(enable_request_nearline);  // [liqikun] 近线请求开关
DECLARE_SPDM_KCONF_BOOL(enable_request_nearline_conf)  // [liqikun] 近线请求开关 kconf
// [chenxian]
DECLARE_SPDM_ABTEST_STRING(random_playlet_smart_offer_info);
DECLARE_SPDM_ABTEST_STRING(random_playlet_smart_offer_info_dnc);
DECLARE_SPDM_ABTEST_STRING(random_playlet_smart_offer_info_doc);
DECLARE_SPDM_ABTEST_BOOL(enable_random_playlet_smart_offer);
DECLARE_SPDM_ABTEST_BOOL(enable_random_cbu_skip);
DECLARE_SPDM_ABTEST_BOOL(enable_random_cbu_skip_client);
DECLARE_SPDM_ABTEST_INT64(random_playlet_smart_offer_ratio_dnc);
DECLARE_SPDM_ABTEST_INT64(random_playlet_smart_offer_ratio_doc);
DECLARE_SPDM_ABTEST_BOOL(enable_random_stop_playlet_smart_offer);
DECLARE_SPDM_ABTEST_INT64(random_stop_playlet_smart_offer_ratio_dnc);
DECLARE_SPDM_ABTEST_INT64(random_stop_playlet_smart_offer_ratio_doc);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_smart_offer_default_by_rank);
DECLARE_SPDM_ABTEST_BOOL(enable_user_action_admit);
DECLARE_SPDM_ABTEST_INT64(user_aciont_admit_stratagy);

// [chenxian] 均衡补贴
DECLARE_SPDM_ABTEST_BOOL(enable_bonus_amount_coef_switch);
DECLARE_SPDM_ABTEST_BOOL(enable_write_bonus_amount_coef_2_ad_delivery_info);
// [liuxiaoyan] 刷内去重
DECLARE_SPDM_ABTEST_BOOL(enable_pv_first_industry_filter_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_filter_native_seperate_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_second_industry_duplication_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_brand_name_duplication_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_category_duplication_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_spu_id_v3_duplication_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_first_industry_duplication);
DECLARE_SPDM_ABTEST_BOOL(enable_closd_search_retarget);
DECLARE_SPDM_ABTEST_BOOL(enable_item_id_duplication_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_cluster_id_duplication_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_only_live_duplicate);
// [liyuanqing]
DECLARE_SPDM_ABTEST_BOOL(enable_random_user_playlet_smart_offer);
DECLARE_SPDM_ABTEST_BOOL(enable_random_user_playlet_skip_cbu);
DECLARE_SPDM_ABTEST_STRING(user_playlet_smart_offer);
DECLARE_SPDM_ABTEST_INT64(playlet_user_tag_id);

// [jianghao07] nogap cpm 门槛
DECLARE_SPDM_ABTEST_BOOL(enable_nogap_filter_by_cpm);
DECLARE_SPDM_ABTEST_DOUBLE(nogap_cpm_thr);

DECLARE_SPDM_ABTEST_BOOL(enable_nogap_filter_by_jumpout_rate);
DECLARE_SPDM_ABTEST_BOOL(enable_nogap_filter_by_jumpout_rate_bugfix);
DECLARE_SPDM_ABTEST_DOUBLE(nogap_jumpout_rate_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_nogap_filter_by_wgroup);

DECLARE_SPDM_ABTEST_DOUBLE(force_reco_rb_thrd);
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_rb_thrd);
DECLARE_SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_same_second_industry);
DECLARE_SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_update);  // [zhaokun03] 短剧强出避让策略升级
DECLARE_SPDM_ABTEST_BOOL(enable_common_front_mobile_soft_to_hard);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_multi_queue_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_multi_queue_quota_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_buyer_level_mix_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_only_high_value_queue);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_top_gpm_thresh);
DECLARE_SPDM_ABTEST_BOOL(enable_rerank_inner_multi_queue_fix);
DECLARE_SPDM_ABTEST_BOOL(enable_rerank_merchant_buyer_multi_queue);
DECLARE_SPDM_ABTEST_STRING(merchant_buyer_multi_queue_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_rerank_inner_multi_queue_reallocate_hc);
DECLARE_SPDM_ABTEST_BOOL(enable_extra_suppport_by_roi1);
DECLARE_SPDM_ABTEST_BOOL(enable_rerank_inner_multi_q_ue_fix);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_multi_queue_stage2);
DECLARE_SPDM_ABTEST_DOUBLE(inner_multi_queue_roi1_strong_thres);
DECLARE_SPDM_ABTEST_DOUBLE(inner_multi_queue_roi1_weak_thres);
DECLARE_SPDM_ABTEST_DOUBLE(inner_multi_queue_inner_stage2_roi_thres);
DECLARE_SPDM_ABTEST_STRING(inner_multi_queue_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_multi_queue_support_hc_bound);
DECLARE_SPDM_ABTEST_DOUBLE(inner_multi_queue_support_hc_bound);
DECLARE_SPDM_ABTEST_DOUBLE(prev_score_weight_params);
DECLARE_SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_photo);
DECLARE_SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_p2l);
DECLARE_SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_live);
DECLARE_SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_hard_ad);
DECLARE_SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_soft_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_multi_queue_roi_adjust);
DECLARE_SPDM_ABTEST_DOUBLE(incremental_roi1_thres);
DECLARE_SPDM_ABTEST_BOOL(enable_hit_mix_bonus_cap);
DECLARE_SPDM_ABTEST_DOUBLE(bonus_cap_num);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_multi_queue_roi_thres_adjust);
DECLARE_SPDM_ABTEST_BOOL(add_multi_queue_trace_log);
DECLARE_SPDM_ABTEST_BOOL(enable_page_conf);
DECLARE_SPDM_ABTEST_DOUBLE(roi1_p10_lower_param);
DECLARE_SPDM_ABTEST_DOUBLE(roi1_p10_lower_param_2);
DECLARE_SPDM_ABTEST_DOUBLE(roi1_p10_lower_param_3);
DECLARE_SPDM_ABTEST_DOUBLE(roi1_p50_greater_param);
DECLARE_SPDM_ABTEST_BOOL(use_prev_inner_ad_min_win_day_score);
DECLARE_SPDM_ABTEST_BOOL(use_greater_params_as_thres);
DECLARE_SPDM_ABTEST_BOOL(disable_inner_multi_queue_thres_hour_adj);
DECLARE_SPDM_ABTEST_BOOL(use_p10_as_thres);
DECLARE_SPDM_ABTEST_BOOL(disable_p50_greater_thres);
DECLARE_SPDM_ABTEST_BOOL(disable_p50_thres);
DECLARE_SPDM_ABTEST_BOOL(disable_p25_thres);
DECLARE_SPDM_ABTEST_BOOL(use_p25_as_thres);
DECLARE_SPDM_ABTEST_DOUBLE(hc_adj_sctr_params);
DECLARE_SPDM_ABTEST_BOOL(enable_multi_layer_roi_greater_branch);
DECLARE_SPDM_ABTEST_BOOL(enable_whole_score_adj_by_cpm);
DECLARE_SPDM_ABTEST_BOOL(enable_multi_layer_roi);
DECLARE_SPDM_ABTEST_INT64(inner_multi_queue_final_sort_top_num);
DECLARE_SPDM_ABTEST_INT64(inner_top_gpm_thresh);
DECLARE_SPDM_ABTEST_INT64(inner_top_gpm_cpm_quota);
DECLARE_SPDM_ABTEST_INT64(final_top_inner_quota);
DECLARE_SPDM_ABTEST_INT64(final_top_merchant_buyer_quota);
DECLARE_SPDM_ABTEST_INT64(inner_multi_queue_replace_lower_bound_num);
DECLARE_SPDM_ABTEST_INT64(inner_multi_queue);
DECLARE_SPDM_ABTEST_BOOL(enable_common_change_mobile_soft_to_hard);
DECLARE_SPDM_ABTEST_BOOL(enable_common_move_review_through_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_sort_by_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_gpm_in_auction_log);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_bonus_to_conv);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_bonus_to_conv_tail);
DECLARE_SPDM_ABTEST_BOOL(disable_hard_price_protect);  // [heqian] 计费打折 min max
DECLARE_SPDM_ABTEST_BOOL(enable_price_rounded_up);  // [heqian] 计费打折 min max
DECLARE_SPDM_ABTEST_BOOL(enable_client_second_industry_v5);   // [liuxiaoyan] 端缓存升级行业 5.0
DECLARE_SPDM_ABTEST_BOOL(enable_client_rerank_add_author_id);  // [wuyinhao] 端智能增加下发作者 ID
DECLARE_SPDM_ABTEST_BOOL(enable_client_rerank_add_dup_photo_id);  // [wuyinhao] 端智能增加下发 dup photo id
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_add_is_high_value);  // [wuyinhao] 端智能是否添加高价值人群标签
// [wuyinhao] 端智能是否使用 cpm 判断高价值人群
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_is_high_value_use_threshold);
// [wuyinhao] 端智能使用 cpm 还是 rank_benefit 作为门槛 0 表示 rank_benefit ，1 表示 cpm
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_use_cpm_or_rankbenifit);
// [wuyinhao] 端智能是否使用 cpm 或 rank_benefit 的门槛值
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_use_threshold_value);
// [wuyinhao] 端智能是否使用用户标签来表示高价值人群
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_is_high_value_use_profile);
// [wuyinhao] 端智能使用多少天的高价值人群标签
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_user_days);
// [wuyinhao] 端智能高价值人群标签使用前 n%
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_user_pctiles);
// [wuyinhao] 端智能直播 rerank 是否降低下发计费值
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_p1_live_ad_price_reduce);
// [wuyinhao] 端智能直播非 rerank 下发计费降低比例
DECLARE_SPDM_ABTEST_DOUBLE(client_ai_p1_live_ad_price_reduce_ratio);
// [wuyinhao] 端智能直播 rerank 下发计费降低比例
DECLARE_SPDM_ABTEST_DOUBLE(client_ai_p1_rerank_live_ad_price_reduce_ration);
// [wuyinhao] 端智能直播 rerank 是否提高直播 cpm 值
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_p1_live_ad_cpm_increase);
// [wuyinhao] 端智能直播 rerank cpm 提高比例
DECLARE_SPDM_ABTEST_DOUBLE(client_ai_p1_live_ad_increase_ratio);
// [wuyinhao] 端智能 p0 rerank 是否调整下发计费系数
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_p0_adjust_price);
// [wuyinhao] 端智能 p0 rerank 下发计费调整系数
DECLARE_SPDM_ABTEST_DOUBLE(client_ai_p0_rerank_adjust_price_ratio);
// [wuyinhao] 端智能 pk 分数是否使用混排分
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_pk_score_use_mix_score);
// [wuyinhao] 端智能相关权重系数
DECLARE_SPDM_ABTEST_DOUBLE(client_ai_pk_score_unify_cpm_inner_weight);
DECLARE_SPDM_ABTEST_DOUBLE(client_ai_pk_score_unify_cpm_outer_weight);
DECLARE_SPDM_ABTEST_DOUBLE(client_ai_pk_score_unify_bonus_inner_weight);
DECLARE_SPDM_ABTEST_DOUBLE(client_ai_pk_score_unify_bonus_outer_weight);
DECLARE_SPDM_ABTEST_INT64(ad_mix_rank_score_aligned_mix_strategy);  // [wuyinhao] 精混排一致性策略
DECLARE_SPDM_ABTEST_BOOL(enable_rank_aligned_mix_score_to_hive);
DECLARE_SPDM_ABTEST_DOUBLE(w5_extra_3_percent_bonus_weight_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(w5_extra_3_percent_bonus_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(outer_dsp_ad_rank_aligned_mix_weight);
DECLARE_SPDM_ABTEST_DOUBLE(outer_dsp_ad_rank_aligned_mix_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(outer_soft_ad_rank_aligned_mix_weight);
DECLARE_SPDM_ABTEST_DOUBLE(outer_soft_ad_rank_aligned_mix_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_rank_aligned_mix_weight);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_rank_aligned_mix_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_live_rank_aligned_mix_weight);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_live_rank_aligned_mix_weight_nebula);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_bonus_admit);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_ad_roas_iaap);

DECLARE_SPDM_ABTEST_BOOL(enable_inner_rb_minus_gpm);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_inner_rpc);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_new_rank_weight_by_page);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_rb_not_use_mix_params);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_live_rb_weight);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_rb_weight);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_live_rb_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_rb_weight_nebula);
DECLARE_SPDM_ABTEST_INT64(dsp_ad_new_rank_item_to_mix_quota);
DECLARE_SPDM_ABTEST_INT64(soft_ad_new_rank_item_to_mix_quota);

DECLARE_SPDM_ABTEST_BOOL(enable_new_rank_weight_by_mix);  // [dingyiming05] 对齐混排参数
// [dingyiming05] 混排参数注册
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_cpm_weight_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_cpm_bias_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_cpm_exp_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_ad_cpm_weight_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_ad_cpm_bias_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_ad_cpm_exp_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_softad_cpm_weight_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_softad_cpm_bias_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_softad_cpm_exp_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_weight_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_bias_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_exp_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_weight_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_bias_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_exp_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(unify_photo_ue_score_weight_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(unify_photo_ue_score_bias_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(unify_photo_ue_score_exp_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(unify_live_ue_score_weight_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(unify_live_ue_score_bias_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(unify_live_ue_score_exp_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_cpm_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_cpm_bias_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(inner_ad_cpm_exp_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_ad_cpm_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_ad_cpm_bias_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_ad_cpm_exp_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_softad_cpm_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_softad_cpm_bias_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(outloop_softad_cpm_exp_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_bias_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_exp_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_bias_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_exp_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(unify_photo_ue_score_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(unify_photo_ue_score_bias_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(unify_photo_ue_score_exp_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(unify_live_ue_score_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(unify_live_ue_score_bias_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(unify_live_ue_score_exp_nebula);
DECLARE_SPDM_ABTEST_BOOL(enable_pm_fwh_ee);
DECLARE_SPDM_ABTEST_BOOL(enable_jk_track_in_splash);  // [zhangzhicong] 开屏京快监测链接开关
DECLARE_SPDM_ABTEST_BOOL(enable_splash_brand_pd_pk);  // [zhangzhicong] 开屏 PD 品效 pk 实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_brand_splash_prefetch_extend_timeout);  // [zhangzhicong] 开屏预加载超时时间调整 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_set_trans_macros);  // [lizemin] 给聚星传宏参
DECLARE_SPDM_ABTEST_INT64(max_hard_ad_force_reco_tag_size);  // [wuyonghong] 有强插 tag 硬广数量上限
DECLARE_SPDM_ABTEST_INT64(max_native_ad_force_reco_tag_size);  // [wuyonghong] 有强插 tag 软广数量上限
DECLARE_SPDM_ABTEST_INT64(max_user_request_history_info);    //  [wuyonghong] 是否读取历史请求信息
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_ann_prefetch_notify)  // [lichanggang] 联盟内循环 ann 预取
DECLARE_SPDM_ABTEST_BOOL(enable_reco_user_info_trim_for_ps)   // reco_user_info 裁剪 for ps
DECLARE_SPDM_ABTEST_BOOL(enable_native_revenue_optimize);    // [guoyuan03] 计费
DECLARE_SPDM_ABTEST_BOOL(revenue_optimize_all_bid_type);    // [guoyuan03] 计费
DECLARE_SPDM_ABTEST_BOOL(enable_revenue_optimize_price_jili);    // [dingyiming05] 计费
DECLARE_SPDM_ABTEST_BOOL(enable_hc_bonus_shift);  // [dingyiming05] hc + bonus 去打折

// [jiangjinling]
DECLARE_SPDM_ABTEST_BOOL(enable_native_fiction_user_feature_collection);   // 允许获取 na 小说用户特征
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_subsidy_smart_offer);   // na 小说 c 补准入开关
DECLARE_SPDM_ABTEST_STRING(fiction_subsidy_conf_group_tag);  // na 小说 c 补配置组别
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_subsidy_total_new_pkg);   // na 小说纯新用户人群包限制
DECLARE_SPDM_ABTEST_BOOL(enable_report_native_fiction_info);   // na 小说字段上报
DECLARE_SPDM_ABTEST_BOOL(enable_enlarge_native_fiction_new_user_tag);   // na 小说纯新用户限制放开
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_subsidy_white_list_config);   // na 小说 c 补白名单配置开关
DECLARE_SPDM_ABTEST_BOOL(fiction_retarget_subsidy_discount);
DECLARE_SPDM_ABTEST_DOUBLE(ratio_of_fiction_retarget_subsidy_discount);
DECLARE_SPDM_ABTEST_STRING(fiction_subsidy_delivery_freq_limit);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_subsidy_delivery_freq_limit);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_subsidy_freq_limit_transfer);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_subsidy_show_style_adjust);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_subsidy_freq_limit_adjust);
DECLARE_SPDM_ABTEST_STRING(fiction_subsidy_panel_show_style_group_key);
DECLARE_SPDM_ABTEST_INT64(fiction_old_customer_active_gap_day);
DECLARE_SPDM_ABTEST_INT64(fiction_retain_min_commodity_price_thresh);
DECLARE_SPDM_ABTEST_INT64(fiction_remit_freq_max_commodity_price_thresh);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_delivery_freq_ltv_thresh);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_big_day_discount);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_second_subsidy);
DECLARE_SPDM_ABTEST_BOOL(enable_decide_fiction_dynamic_panel_price);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_panel_v4_fixed_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_panel_v4_any_base_fixed_strategy);
DECLARE_SPDM_ABTEST_STRING(fiction_panel_v4_fixed_subsidy_ratio_group_key);
DECLARE_SPDM_ABTEST_STRING(fiction_panel_v4_fixed_subsidy_style_group_key);
DECLARE_SPDM_ABTEST_STRING(fiction_dynamic_panel_price_group_key);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_subsidy_use_pay_ltv);
DECLARE_SPDM_ABTEST_STRING(fiction_dynamic_panel_subsidy_conf_key);
DECLARE_SPDM_ABTEST_BOOL(enable_jump_fiction_panel_product_name_white);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_no_subsidy_forbid);
DECLARE_SPDM_ABTEST_BOOL(useNovelSubsidy);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_price_map_panel_price);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_fiction_params_init);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_panel_v3_unify_ltv_group);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_iaa_ecpc_by_put_book_new);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_rct_subsidy_ratio);
DECLARE_SPDM_ABTEST_INT64(fiction_rct_subsidy_ratio_upper_number);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_panel_v3_pay_rate_group);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_smart_panel_price_v4_strategy_new);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_smart_panel_random_price);
DECLARE_SPDM_ABTEST_INT64(fiction_random_panel_price_probability);
DECLARE_SPDM_ABTEST_INT64(fiction_random_retain_subsidy_panel_style_prob);
DECLARE_SPDM_ABTEST_INT64(fiction_random_pv_subsidy_style_prob);
DECLARE_SPDM_ABTEST_DOUBLE(default_fiction_iap_subsidy_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(default_nc_fiction_iap_subsidy_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_iap_nc_explore_strategy);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_smart_panel_price_v4_max_price);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_smart_panel_price_v4_min_price);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_smart_panel_price_v4_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_smart_panel_retain_price_v4_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_smart_panel_price_v4_bias);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_multi_style_panel_split_price_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_panel_v4_subsidy_ratio);
DECLARE_SPDM_ABTEST_BOOL(forbid_fiction_panel_big_day_config);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_big_day_exp_map_subsidy_config);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_app_na_explore_subsidy);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_interest_user_explore);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_interest_user_discount);
DECLARE_SPDM_ABTEST_STRING(fiction_big_day_exp_map_config_key);
DECLARE_SPDM_ABTEST_BOOL(enable_cal_fiction_iap_ios_price);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_ios_platform_tax_share_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iap_panel_origin_price_weight);
DECLARE_SPDM_ABTEST_INT64(fiction_iap_panel_origin_price_bias);
DECLARE_SPDM_ABTEST_BOOL(enable_delete_fiction_panel_v3_code);

DECLARE_SPDM_ABTEST_INT64(novelPanelSelectType);    //  [yangxuan06] na 小说动态面板类型
DECLARE_SPDM_ABTEST_STRING(fiction_opt_panel_exp_key);    //  [yangxuan06] na 小说动态面板配置
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_subsidy_type_fix);    //  [yangxuan06] 小说准入调整
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_iap_industry_explore);

// [zhaoyi13]
DECLARE_SPDM_ABTEST_BOOL(enable_fill_novel_unify_ltv);

// [yupeng05] 是否基于审核数据去软广标
DECLARE_SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review);

// [jiyang]  优质软广判定规则调整-小流量实验方案
DECLARE_SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review_old);  // 生效老规则
DECLARE_SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review_new);  // 生效新规则
// [jiyang] 过滤低 pvtr photo 样本
DECLARE_SPDM_ABTEST_BOOL(enable_photo_pvtr_filter_sample);
DECLARE_SPDM_ABTEST_BOOL(reduce_vtr_sigmoid);
// [jiyang] 过滤低 pvtr photo 样本 尾号分流实验
DECLARE_SPDM_ABTEST_BOOL(enable_photo_pvtr_filter_sample_tailcut);
// [jiyang] 关注页和同城页 holdout 开关
DECLARE_SPDM_ABTEST_BOOL(enable_GamoraFollowFentiao);
DECLARE_SPDM_ABTEST_BOOL(enable_GamoraFollowOuter);
DECLARE_SPDM_ABTEST_BOOL(enable_GamoraFollowInner);
DECLARE_SPDM_ABTEST_BOOL(enable_GamoraFollowLiveInner1);
DECLARE_SPDM_ABTEST_BOOL(enable_GamoraFollowLiveInner2);
DECLARE_SPDM_ABTEST_BOOL(enable_GamoraFollowLiveInner3);
DECLARE_SPDM_ABTEST_BOOL(enable_NebulaFollowInner);
DECLARE_SPDM_ABTEST_BOOL(enable_NebulaFollowLiveInner2);
DECLARE_SPDM_ABTEST_BOOL(enable_NebulaFollowSide);
DECLARE_SPDM_ABTEST_BOOL(enable_NebulaFollowTop);
DECLARE_SPDM_ABTEST_BOOL(enable_NebulaFollowLiveInner1);
DECLARE_SPDM_ABTEST_BOOL(enable_FollowAll);
DECLARE_SPDM_ABTEST_BOOL(enable_common_feed_card);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_common_feed_card);
DECLARE_SPDM_ABTEST_BOOL(enable_kwai_game_common_feed_card);
DECLARE_SPDM_ABTEST_BOOL(enable_short_serial_common_feed_card);
DECLARE_SPDM_ABTEST_INT64(enable_common_feed_card_min_day);
// [jiyang] 付费短剧 feed 大卡
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_adform_trans_feedcard);
DECLARE_SPDM_ABTEST_INT64(playlet_adform_trans_feedcard_min_fresh_time);
// [jiyang]
DECLARE_SPDM_ABTEST_BOOL(enable_follow_increase_mix_ecpm);
DECLARE_SPDM_ABTEST_DOUBLE(follow_increase_mix_ecpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(follow_increase_mix_ecpm_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_increase_mix_ecpm_2);
DECLARE_SPDM_ABTEST_DOUBLE(follow_increase_mix_ecpm_ratio_2);
DECLARE_SPDM_ABTEST_DOUBLE(follow_increase_mix_ecpm_thr_2);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_rank_refactor_dot);

DECLARE_SPDM_ABTEST_BOOL(enable_GamoraCitywideInner);
DECLARE_SPDM_ABTEST_BOOL(enable_GamoraCitywideInoutInner);
DECLARE_SPDM_ABTEST_BOOL(enable_GamoraCitywideTop5);
DECLARE_SPDM_ABTEST_BOOL(enable_NebulaCitywideInoutInner);
DECLARE_SPDM_ABTEST_BOOL(enable_NebulaCitywideInoutOuter);
DECLARE_SPDM_ABTEST_BOOL(enable_NebulaCitywideInoutOuter2);
DECLARE_SPDM_ABTEST_BOOL(enable_NebulaCitywide);
DECLARE_SPDM_ABTEST_BOOL(enable_CitywideAll);

// [jiyang] 关注页跳过 dpp 过滤
DECLARE_SPDM_ABTEST_BOOL(enable_follow_gamora_outer_skip_dpp_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_gamora_inner_skip_dpp_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_nebula_skip_dpp_filter);
DECLARE_SPDM_ABTEST_STRING(follow_ecpm_threshold_init_tag);
DECLARE_SPDM_ABTEST_STRING(follow_ecpm_threshold_init_tag_2);
// [jiyang] 关注页跳过 DUPLICATED_AUTHOR_ID
DECLARE_SPDM_ABTEST_BOOL(enable_follow_skip_DUPLICATED_AUTHOR_ID);
// [jiyang] 关注页透传 bonus 、 hc 到混排
DECLARE_SPDM_ABTEST_BOOL(enable_follow_opt_allow_hc);
DECLARE_SPDM_ABTEST_DOUBLE(follow_opt_allow_hc_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_opt_allow_bonus);
DECLARE_SPDM_ABTEST_DOUBLE(follow_opt_allow_bonus_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(follow_opt_allow_bonus_ratio_c);
DECLARE_SPDM_ABTEST_DOUBLE(follow_opt_allow_bonus_ratio_b);
DECLARE_SPDM_ABTEST_DOUBLE(follow_opt_allow_bonus_ratio_g);

DECLARE_SPDM_ABTEST_BOOL(enable_follow_opt_allow_bonus_level);
DECLARE_SPDM_ABTEST_STRING(target_bonus_l);

// [jiyang] 同城页透传字段到混排
DECLARE_SPDM_ABTEST_BOOL(enable_trans_field_to_nearby_mixrank);
DECLARE_SPDM_ABTEST_DOUBLE(nearby_mixrank_feed_boost_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(nearby_mixrank_thaos_boost_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(nearby_mixrank_cpm_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_trans_field_to_nearby_mixrank_v2);

// [jiyang] 内循环大卡二次请求
DECLARE_SPDM_ABTEST_BOOL(enable_skip_firstind_innerloopbigcard_secondreq);
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_quota_innerloopbigcard_secondreq);
DECLARE_SPDM_ABTEST_INT64(card_style_1_small_num);
DECLARE_SPDM_ABTEST_INT64(card_style_1_num);
DECLARE_SPDM_ABTEST_INT64(card_style_2_num);
DECLARE_SPDM_ABTEST_BOOL(enable_setfanstopamd_innerloopbigcard_secondreq);
DECLARE_SPDM_ABTEST_BOOL(enable_pk_innerloopbigcard_secondreq);
DECLARE_SPDM_ABTEST_BOOL(enable_pk_innerloopbigcard_secondreq_fix);

// [jiyang] 同城修复混排字段透传
DECLARE_SPDM_ABTEST_BOOL(enable_fix_trans_field_to_mixrank);

// [jiyang] 关注页接硬广
DECLARE_SPDM_ABTEST_BOOL(enable_follow_inner_add_hard_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_inner_add_hard_ad_2);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_increase_live_mix_ecpm);
DECLARE_SPDM_ABTEST_DOUBLE(follow_increase_live_mix_ecpm_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_hard_soft_merge_truncate);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_hard_soft_merge_truncate_2);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_inc_soft_truncate_size);
DECLARE_SPDM_ABTEST_INT64(follow_inc_soft_truncate_size_num);
DECLARE_SPDM_ABTEST_BOOL(enable_close_follow_id_filter);

DECLARE_SPDM_ABTEST_STRING(cpm_thr_08_exp_name);
DECLARE_SPDM_ABTEST_STRING(followMixExpTag);

DECLARE_SPDM_ABTEST_BOOL(enable_follow_opt_allow_bonus_v2);
DECLARE_SPDM_ABTEST_STRING(follow_bonus_target_level);
DECLARE_SPDM_ABTEST_STRING(follow_mixrank_exp_name);

DECLARE_SPDM_ABTEST_BOOL(enable_live_independent_quota);
DECLARE_SPDM_ABTEST_INT64(live_independent_quota);

DECLARE_SPDM_ABTEST_BOOL(enable_sort_by_rankbenefit);

DECLARE_SPDM_ABTEST_BOOL(enable_FillChuangxinModelFeatureInfo);
DECLARE_SPDM_ABTEST_BOOL(enable_add_chuangxin_model_feature);

DECLARE_SPDM_ABTEST_BOOL(enable_mix_rank_attr_to_rank_info_follow);

DECLARE_SPDM_ABTEST_BOOL(enable_follow_ad_merch_gpm);

// [yupeng05] 是否去除外粉的软广标基于 plc
DECLARE_SPDM_ABTEST_BOOL(enable_normal_fans_disable_ad_mark_without_plc);
// [yupeng05] 是否去除流量助推广告标，基于白名单
DECLARE_SPDM_ABTEST_BOOL(enable_flow_boost_ad_disable_ad_mark_with_whitelist);
DECLARE_SPDM_ABTEST_BOOL(enable_auction_filter_plugin);
DECLARE_SPDM_ABTEST_INT64(nebula_user_refresh_times_thrd);
DECLARE_SPDM_ABTEST_INT64(nebula_user_value_filter_auction_bid_thrd);
// [liuxiaoyan] 低质染色实验
DECLARE_SPDM_ABTEST_BOOL(enable_low_quality_stain_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_low_quality_filter);
DECLARE_SPDM_ABTEST_INT64(low_quality_exp_tag);
// [huangwenbin] 联盟内循环
DECLARE_SPDM_ABTEST_BOOL(enable_redis_universe_purchase_crowd);  //  [huangwenbin] 主站用户行为数据写 redis 联盟开关  //  NOLINT
// [zengzhengda] 联盟出价工具跳过赔付开关 v2
DECLARE_SPDM_ABTEST_BOOL(enable_universe_bid_tool_skip_compensation_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_calc_hidden_cost_plugin);
DECLARE_SPDM_ABTEST_BOOL(enable_dnc_cem_base_group);  // [liangyukang] dnc_cem_explore_base_group 落表  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ee_purchase_cvr_tags);    // [yanghang06] ee 行业模型字段落盘开关
DECLARE_SPDM_ABTEST_BOOL(enable_add_prerank_dnc_ltv);  // [yangfukang03] dnc ltv 字段
DECLARE_SPDM_ABTEST_BOOL(fix_force_quota);  //  [guoyuan03] fix_force_quota
DECLARE_SPDM_ABTEST_BOOL(enable_app_detail_use_release);  //  [wangjiabin05] 使用 release 替换 app 信息
DECLARE_SPDM_ABTEST_BOOL(disable_platform_new_customer_v2);  // [wangjiabin05] 是否禁用获取新平台用户标识
DECLARE_SPDM_ABTEST_BOOL(disable_dpp_for_soft_hard_union);  //  [wangjiabin05] 软硬广融合关闭 dpp
DECLARE_SPDM_ABTEST_BOOL(enable_ad_history_filter_id_unify);  // [wangjiabin05]  刷次调度过滤优化升级
DECLARE_SPDM_ABTEST_BOOL(enable_set_keep_web_view);  // [wangjiabin05] 是否设置 keep web view
DECLARE_SPDM_ABTEST_BOOL(disable_merchant_exp_tag);  // [wangjiabin05] 是否禁用电商 exp tag
DECLARE_SPDM_ABTEST_BOOL(enable_h5_doudi_for_app_advance);  // [wangjiabin05] 应用拉活兜底
DECLARE_SPDM_ABTEST_BOOL(enable_hard_soft_quota_split);  // 软硬广 quota 独立
DECLARE_SPDM_ABTEST_BOOL(remove_mock_user_id);  // 未登录用户 user id mock 逻辑清理
DECLARE_SPDM_ABTEST_BOOL(enable_rm_auction_p2p);  // 是否移除竞价 p2p
DECLARE_SPDM_ABTEST_BOOL(diasble_unlogin_ann_prefetch_notify);  // 禁用未登录用户 ann 预取
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_valid_flag);  //  [wangjiabin05] 精排 ps 预取请求考虑软广准入
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_tag_replace);  //  [wangjiabin05] 使用用户正排电商用户标签
DECLARE_SPDM_ABTEST_BOOL(disable_again_photo_mix_request_live);  //  [wangjiabin05] 连续 photo 不请求直播
DECLARE_SPDM_ABTEST_BOOL(disable_incr_req_times);  //  [wangjiabin05] 禁用粉条请求统计
DECLARE_SPDM_ABTEST_BOOL(enable_remove_detail_unit_type);  //  [wangjiabin05] 禁用 detail unit type
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_tag_remove);  //  [wangjiabin05] 禁用 merchant tag
DECLARE_SPDM_ABTEST_BOOL(enable_hard_soft_union);  // [wangjiabin05] 软硬广统一
DECLARE_SPDM_ABTEST_BOOL(enable_hard_soft_union_nearby);  // [wangjiabin05] 同城软硬广统一
DECLARE_SPDM_ABTEST_BOOL(enable_hard_soft_union_follow);  // [wangjiabin05] 关注软硬广统一
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_unify_pk);  //  [gaozepeng] 激励统一 pk
DECLARE_SPDM_ABTEST_BOOL(hard_soft_union_only_for_thanos_mix);  // [wangjiabin05] 软硬广统一只针对单列生效
DECLARE_SPDM_ABTEST_BOOL(enable_mix_predict);  // [wangjiabin05] 混排预估
DECLARE_SPDM_ABTEST_BOOL(enable_mix_feature_prepare);  // [sunsong] 混排特征准备
DECLARE_SPDM_ABTEST_INT64(auction_dpp_top_size);  // [wangjiabin05] dpp top 硬广保送大小
DECLARE_SPDM_ABTEST_BOOL(enable_fill_fanstop_author);  //  [wangjiabin05] 填充粉条作者频控信息
DECLARE_SPDM_ABTEST_BOOL(enable_use_photo_id_in_forward);  // [wangjiabin05]  正排请求时增加 photoid
DECLARE_SPDM_ABTEST_BOOL(disable_app_detail);  //  [wangjiabin05] 禁用 app detail
DECLARE_SPDM_ABTEST_INT64(max_hard_quota);  //  [wangjiabin05] 最大硬广 quota
DECLARE_SPDM_ABTEST_INT64(max_soft_quota);  //  [wangjiabin05] 最大软广 quota
DECLARE_SPDM_ABTEST_INT64(total_quota_for_mix_rank);  //  [wangjiabin05] 总 quota 数
DECLARE_SPDM_ABTEST_INT64(merchant_total_quota_for_mix_rank);  //  [yuchengyuan] 货架 mix—rank 总 quota 数
DECLARE_SPDM_ABTEST_INT64(inner_high_quality_soft);  //  [wangjiabin05] 内循环优质软广
DECLARE_SPDM_ABTEST_INT64(inner_soft);  //  [jianghao07] 内循环软广
DECLARE_SPDM_ABTEST_INT64(inner_soft_live);  //  [wangjiabin05] 内循环直播软广
DECLARE_SPDM_ABTEST_INT64(inner_soft_photo);  //  [wangjiabin05] 内循环软广短视频
DECLARE_SPDM_ABTEST_BOOL(enable_general_quota_v2);  // [hanhao05] 内循环 quota
DECLARE_SPDM_ABTEST_INT64(inner_live_quota);  //  [hanhao05] 内循环直播
DECLARE_SPDM_ABTEST_INT64(outer_high_quality_soft);  //  [wangjiabin05] 外循环优质软广
DECLARE_SPDM_ABTEST_INT64(outer_soft);  //  [jianghao07] 外循环软广
DECLARE_SPDM_ABTEST_INT64(outer_soft_live);  //  [wangjiabin05] 外循环软广直播
DECLARE_SPDM_ABTEST_INT64(outer_soft_photo);  //  [wangjiabin05] 外循环软广短视频
DECLARE_SPDM_ABTEST_INT64(mix_hard_quota);  //  [wangjiabin05] 混排优选硬广 quota
DECLARE_SPDM_ABTEST_BOOL(enable_front_max_quota_limit);  // [sunsong] front 送混最大 quota 限制开关
DECLARE_SPDM_ABTEST_BOOL(disable_front_fill_full_quota);  // [sunsong] front 送混不填满 quota 限制开关
DECLARE_SPDM_ABTEST_INT64(max_inner_high_quality_soft);  // [sunsong] 内循环优质软广 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_inner_soft);  // [sunsong] 内循环软广 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_inner_soft_live);  // [sunsong] 内循环软广直播 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_inner_soft_photo);  // [sunsong] 内循环软广短视频 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_outer_high_quality_soft);  // [sunsong] 外循环优质软广 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_outer_soft);  // [sunsong] 外循环软广 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_outer_soft_live);  // [sunsong] 外循环软广直播 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_outer_soft_photo);  // [sunsong] 外循环软广短视频 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_inner_hard_quota);  // [sunsong] 内循环硬广 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_outer_hard_quota);  // [sunsong] 外循环硬广 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_all_hard_quota);  // [sunsong] 总硬广 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_all_soft_quota);  // [sunsong] 总软广 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_all_live_quota);  // [sunsong] 总直播 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_inner_quota);  // [sunsong] 内循环 quota 最大限制
DECLARE_SPDM_ABTEST_INT64(max_outer_quota);  // [sunsong] 外循环 quota 最大限制
DECLARE_SPDM_ABTEST_BOOL(enable_hard_quota_split);  // [sunsong] 硬广内外循环独立 quota 开关关
DECLARE_SPDM_ABTEST_INT64(inner_hard_quota);  // [sunsong] 内循环硬广 quota
DECLARE_SPDM_ABTEST_INT64(outer_hard_quota);  // [sunsong] 外循环硬广 quota
DECLARE_SPDM_ABTEST_BOOL(enable_inner_outer_quota_exp);  // [sunsong] 内循环外循环独立 quota 开关
DECLARE_SPDM_ABTEST_INT64(inner_quota);  // [sunsong] 内循环 quota
DECLARE_SPDM_ABTEST_INT64(outer_quota);  // [sunsong] 外循环 quota
// [liubingqi05] 内循环 live 的 quota 中剔除粉条
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_exclude_fanstop);
// [liubingqi05] 内循环 photo 的 quota 中剔除粉条
DECLARE_SPDM_ABTEST_BOOL(enable_inner_photo_exclude_fanstop);

// [zhangyuxiang05] 一天中观看广告数最大门槛实验是否开启
DECLARE_SPDM_ABTEST_BOOL(enable_max_browsed_ads_num_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_max_browsed_ads_num_exp_new);
// [zhangyuxiang05] 一天中观看广告数最大门槛
DECLARE_SPDM_ABTEST_INT64(max_browsed_ads_num_exp);
DECLARE_SPDM_ABTEST_INT64(max_browsed_ads_num_exp_new);
// [zhangyuxiang05] 特殊地区一天中观看广告数最大门槛
DECLARE_SPDM_ABTEST_INT64(special_region_max_browsed_ads_num_exp);
DECLARE_SPDM_ABTEST_INT64(special_region_max_browsed_ads_num_exp_new);
// [zhangyuxiang05] 是否对异常 unify_bonus 进行截断
DECLARE_SPDM_ABTEST_BOOL(enable_calib_abnormal_unify_bonus_by_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_calib_abnormal_unify_bonus_by_cpm);
DECLARE_SPDM_ABTEST_BOOL(enable_calib_abnormal_unify_bonus_by_max_cpm_and_thr);
// [zhangyuxiang05] 对异常 unify_bonus 的校准系数
DECLARE_SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_gamora1);
DECLARE_SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_nebula1);
DECLARE_SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_gamora2);
DECLARE_SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_nebula2);
DECLARE_SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_gamora3);
DECLARE_SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_nebula3);
DECLARE_SPDM_ABTEST_DOUBLE(ad_pv_bonus_frac_thr_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(ad_pv_bonus_frac_thr_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_times_thr_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_times_thr_nebula);
// [zhangyuxiang05] bonus 留反实验实验流量标记
DECLARE_SPDM_ABTEST_BOOL(enable_ad_unify_bonus_weight_exp);
DECLARE_SPDM_ABTEST_DOUBLE(ad_unify_bonus_weight_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(ad_unify_bonus_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_gamora4);
DECLARE_SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_nebula4);

//  [wangjiabin05] 拷贝方式 0 默认 1 以硬广排序 2 以软广排序
DECLARE_SPDM_ABTEST_INT64(live_copy_refactor_type);
DECLARE_SPDM_ABTEST_BOOL(enable_live_copy_refactor);  //  [wangjiabin05] 是否启动直播直投队列拷贝
DECLARE_SPDM_ABTEST_BOOL(enable_live_copy_refactor_without_union);  //  [wangjiabin05] 直播直投队列拷贝
DECLARE_SPDM_ABTEST_BOOL(enable_mix_rank_inner_live_force);  //  [wangjiabin05] 内循环强出
DECLARE_SPDM_ABTEST_BOOL(enable_mix_rank_inner_live_extra);  //  [wangjiabin05] 内循环额外 quota
DECLARE_SPDM_ABTEST_STRING(exp_tag_in_rank_feature_test_for_hq);  // [wangjiabin05] 实验 tag
DECLARE_SPDM_ABTEST_BOOL(enable_fill_front_item_attr_by_rank);  // [wanglei39] 通过 rank 透传的字段填充 front
DECLARE_SPDM_ABTEST_BOOL(enable_fix_front_modify_item_info);  // [wanglei39] 修复 front 设置 item_info 信息
DECLARE_SPDM_ABTEST_BOOL(enable_move_back_merchant_select_ad);  // [yuchengyuan] 将货架广告截断后移
DECLARE_SPDM_ABTEST_BOOL(enable_split_merchant_mix_select);  // [yuchengyuan] 拆分货架单独多队列算子
DECLARE_SPDM_ABTEST_BOOL(enable_skip_merchant_mix_select);  // [yuchengyuan] 货架跳过多队列算子
DECLARE_SPDM_ABTEST_STRING(soft_hard_union_exp_tag);  // [wanglei39] 软硬广融合实验组
DECLARE_SPDM_ABTEST_STRING(ad_select_quota_front_config_key);  //  [wangjiabin05] quota 选取 config key
DECLARE_SPDM_ABTEST_BOOL(enable_refresh_count_merge_posid);  //  [yangyanxi] 刷次 pos_id 全流量生效
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_fanstop_hard_pk);  //  [yangyanxi] 激励接入粉条与硬广 pk
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_invoked_product_retrieval);  //  [gaozepeng] 激励唤端产品召回  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_skip_pos_select);  //  [gaozepeng] 激励直播跳过选坑位
DECLARE_SPDM_ABTEST_DOUBLE(inspire_mix_pk_ratio);  //  [jiangfeng06] 激励接入软硬广 pk 系数（软广直播）
DECLARE_SPDM_ABTEST_DOUBLE(inspire_soft_photo_pk_ratio);  //  [gaozepeng] 激励接入软硬广 pk 系数（软广视频）
DECLARE_SPDM_ABTEST_INT64(native_ad_reserve_price_thanos);                          // [guoyuan03] 保留价
DECLARE_SPDM_ABTEST_INT64(native_ad_reserve_price_inspire);                         // [guoyuan03] 保留价
DECLARE_SPDM_ABTEST_INT64(match_server_req_mig_tag);        // [zhaodi] match_server_req 上移实验
DECLARE_SPDM_ABTEST_INT64(search_del_ad_server_stage);        // [zhaodi] 搜索 adServer 下线实验
DECLARE_SPDM_ABTEST_INT64(user_data_center_kv_config);  // [chenchen13] 用户正排配置化
DECLARE_SPDM_ABTEST_INT64(merge_exp_to_adserer_timeout);  // [wangyangrui] adserver 融合实验放超时
DECLARE_SPDM_ABTEST_BOOL(fix_inspire_reco_user_info);  // [wangyangrui] fix 激励填充 reco_user_info 问题
DECLARE_SPDM_ABTEST_BOOL(enable_fill_item_id_info_front);  // [wangyangrui] 正样本添加 item_id_info
DECLARE_SPDM_ABTEST_BOOL(enable_p2live_show_live_stream_1);  // [wanglei10] 联盟信息流场景 短视频引流转直播流
DECLARE_SPDM_ABTEST_BOOL(enable_p2live_show_live_stream_13);  // [wanglei10] 联盟插屏场景 短视频引流转直播流
DECLARE_SPDM_ABTEST_BOOL(enable_universe_age_expand);   // [liuyibo05] 联盟年龄定向扩展实验
DECLARE_SPDM_ABTEST_INT64(mix_benefit_cpm_boost_key);
DECLARE_SPDM_ABTEST_INT64(mix_benefit_show_rate_key);
DECLARE_SPDM_ABTEST_INT64(mix_benefit_item_type_show_rate_key);
// [zengzhengda] 联盟防爆量策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_univ_prevent_explosion_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_search_sku_upgrade);  // [yangxibo] 商品 tab 商品搜索
DECLARE_SPDM_ABTEST_BOOL(enable_combo_search_sku_upgrade);  // [zhangchaoyi03] 综搜商品搜索
DECLARE_SPDM_ABTEST_BOOL(enable_search_item_recall_on_feeds);  // [yangxibo] 商品召回可以出在双列
//  [yangyanxi] 刷次 pos_id 全流量生效实验
DECLARE_SPDM_ABTEST_BOOL(enable_refresh_count_merge_posid_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_cache_strategy_info);       // [duxiaomeng] 客户端缓存广告信息
DECLARE_SPDM_ABTEST_INT64(ad_pk_score_type_copy);  // [wuyinhao]
DECLARE_SPDM_ABTEST_BOOL(enable_fill_adx_app_privacy_info);  // [lizemin] adx 应用隐私合规信息透传
DECLARE_SPDM_ABTEST_BOOL(enable_merge_negative_session);  // [zhangruyuan] 负反馈跨端
DECLARE_SPDM_ABTEST_BOOL(enable_short_play_action);  // [zhangruyuan] 快滑相关 action
DECLARE_SPDM_ABTEST_BOOL(enable_upgrade_behavior_interest);       // [zhangruyuan] 行为意向升级 4.0
DECLARE_SPDM_ABTEST_BOOL(enable_hybrid_auction_nobid_protect);
DECLARE_SPDM_ABTEST_BOOL(enable_hybrid_auction_nobid_protect_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_rtb_win_rate_loss_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(ad_soft_front_ad_score_weight_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(ad_soft_front_ad_score_weight_gamora);
DECLARE_SPDM_ABTEST_BOOL(disable_playlet_campaign_type_discount);
DECLARE_SPDM_ABTEST_BOOL(enable_ub_discount_gfp);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_rtb_win_rate_predict_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_native_hybrid_auction);
DECLARE_SPDM_ABTEST_BOOL(enable_enhance_game_premiere);
DECLARE_SPDM_ABTEST_BOOL(enable_adload_control_native_ratio_bound);
DECLARE_SPDM_ABTEST_DOUBLE(enhance_game_premiere_default);
DECLARE_SPDM_ABTEST_DOUBLE(enhance_game_premiere_max);
DECLARE_SPDM_ABTEST_DOUBLE(enhance_game_premiere_min);
DECLARE_SPDM_ABTEST_DOUBLE(max_gsp_unify_billing_ratio);  // [tanweihan] max_gsp_unify_billing_ratio
DECLARE_SPDM_ABTEST_DOUBLE(adload_control_native_ratio_max);
// [xuxu] 激励视频金币相关开关
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_coin_bias);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_rewarded_coin_multi_key);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_rewarded_ban_user);
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_coin_set_fix_num);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_rewarded_coin_for_multi_treatment);
DECLARE_SPDM_ABTEST_BOOL(enable_multi_treatment_new_redis);
DECLARE_SPDM_ABTEST_BOOL(enable_price_protect);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_real_roi_control);
DECLARE_SPDM_ABTEST_BOOL(enable_select_active_more_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_task_info_avaliable_admit);
DECLARE_SPDM_ABTEST_BOOL(enable_select_subpage_more_coin);
DECLARE_SPDM_ABTEST_STRING(valid_subpage_ids);
DECLARE_SPDM_ABTEST_STRING(valid_user_active_levels);
DECLARE_SPDM_ABTEST_DOUBLE(user_active_ratio);
DECLARE_SPDM_ABTEST_INT64(min_user_active_coin);
DECLARE_SPDM_ABTEST_INT64(low_active_stg_freq_admit_task_cnt);  // [zhangyuyang05] 激励|低活|次数
DECLARE_SPDM_ABTEST_INT64(low_active_stg_freq_admit_task_coin);  // [zhangyuyang05] 激励|低活|金额
DECLARE_SPDM_ABTEST_BOOL(enable_low_active_strategy_admit_by_prob);
DECLARE_SPDM_ABTEST_DOUBLE(low_active_strategy_admit_dau_prob_thres);
DECLARE_SPDM_ABTEST_DOUBLE(low_active_strategy_admit_incntv_ad_dau_prob_thres);
DECLARE_SPDM_ABTEST_DOUBLE(low_active_strategy_admit_uplift_rnkp_thres);
DECLARE_SPDM_ABTEST_DOUBLE(low_active_strategy_admit_dau_prob_thres_lower);
DECLARE_SPDM_ABTEST_DOUBLE(low_active_strategy_admit_incntv_ad_dau_prob_thres_lower);
DECLARE_SPDM_ABTEST_DOUBLE(low_active_strategy_admit_uplift_rnkp_thres_lower);
DECLARE_SPDM_ABTEST_STRING(inspire_low_active_strategy_exp_tag_for_log);
DECLARE_SPDM_ABTEST_STRING(incntv_ad_monitor_tag);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_real_roi_lower);
DECLARE_SPDM_ABTEST_INT64(inspire_real_roi_control_min_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_lowest_coin_random_for_all);
DECLARE_SPDM_ABTEST_INT64(inspire_lowest_coin_random_upper);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_video_real_gross_lower);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_video_real_roi_lower);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_video_jfb_lower);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_real_roi_lower);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_jfb_lower);
DECLARE_SPDM_ABTEST_BOOL(enable_append_mobile_direct_live_rank_list);
DECLARE_SPDM_ABTEST_BOOL(enable_append_mobile_p2l_rank_list);
DECLARE_SPDM_ABTEST_BOOL(enable_append_amd_photo_ranking_list);
DECLARE_SPDM_ABTEST_BOOL(enable_append_mobile_photo_rank_list);
DECLARE_SPDM_ABTEST_BOOL(enable_append_pc_photo_rank_list);
DECLARE_SPDM_ABTEST_BOOL(enable_biz_info_new_token);
DECLARE_SPDM_ABTEST_BOOL(enable_transparent_feed_mix_fields);
DECLARE_SPDM_ABTEST_STRING(feed_mix_score_conf_key);
DECLARE_SPDM_ABTEST_BOOL(enable_feed_mix_score_new);
DECLARE_SPDM_ABTEST_BOOL(enable_use_feed_cpm_fields);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_merchant_live_charge_discount);  // [liubing05] 激励电商直播打折系数开关  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_pec_sensitive_user);  // pec 金币不敏感用户准入
DECLARE_SPDM_ABTEST_BOOL(enable_read_server_rewarded_user_info);
DECLARE_SPDM_ABTEST_BOOL(enabel_pec_coin_for_price);  // pec 金币计费
DECLARE_SPDM_ABTEST_BOOL(enable_dsp_live_deep_reward);  // 信息流直播激励准入
DECLARE_SPDM_ABTEST_BOOL(enable_read_hc_rewarded_coin_user_info);  // 金币 hidden_cost 人群信息
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_user_coupon_info);
DECLARE_SPDM_ABTEST_BOOL(enable_rb_rank_first_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_next_n_ad);
DECLARE_SPDM_ABTEST_INT64(neoMaxLookAgainDialogCount);
DECLARE_SPDM_ABTEST_INT64(rewarded_next_n_ad_price_thr);
DECLARE_SPDM_ABTEST_DOUBLE(rewarded_next_n_ad_coin_thr);
DECLARE_SPDM_ABTEST_DOUBLE(rewarded_next_n_ad_roi_thr);
DECLARE_SPDM_ABTEST_DOUBLE(rewarded_next_n_ad_gross_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_next_n_preview_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_next_n_set_coin2);
DECLARE_SPDM_ABTEST_DOUBLE(preview_coin_rct_p);
DECLARE_SPDM_ABTEST_DOUBLE(preview_coin_def_roi);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_task_cd);  // 激励广告 任务 cd
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_task_cnt_refactor);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_task_cnt_price_thres);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_task_cnt_coin_thres);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_task_cnt_gross_thres);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_task_cnt_roi_thres);
DECLARE_SPDM_ABTEST_INT64(inpsire_task_cd_time);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_long_task_cd_price_thres);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_long_task_cd_avg_pre3_task_coin_thres);
DECLARE_SPDM_ABTEST_INT64(inspire_long_task_cd_time);
DECLARE_SPDM_ABTEST_INT64(inspire_risk_user_task_cd_time);
DECLARE_SPDM_ABTEST_BOOL(enable_engine_coin1_risk_control);
DECLARE_SPDM_ABTEST_DOUBLE(dynamic_task_cnt_by_ucc_score_alpha);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_has_more);
DECLARE_SPDM_ABTEST_BOOL(enable_box_task_cd);
DECLARE_SPDM_ABTEST_BOOL(enable_budget_allocation_user_roi_alpha_bias);
DECLARE_SPDM_ABTEST_INT64(incntv_ad_queue_info_log_num);
DECLARE_SPDM_ABTEST_DOUBLE(iaa_next_n_coin_ucpm_lower);
DECLARE_SPDM_ABTEST_DOUBLE(iaa_next_n_coin_ucpm_upper);
DECLARE_SPDM_ABTEST_INT64(iaa_next_n_coin_value);
DECLARE_SPDM_ABTEST_BOOL(disable_rewarded_next_n_task_cnt_upper);
DECLARE_SPDM_ABTEST_INT64(rewarded_next_n_task_cnt_upper);
DECLARE_SPDM_ABTEST_BOOL(disable_rewarded_next_n_task_coin_upper);
DECLARE_SPDM_ABTEST_INT64(rewarded_next_n_task_coin_upper);
DECLARE_SPDM_ABTEST_BOOL(disable_rewarded_next_n_risk_user);
DECLARE_SPDM_ABTEST_BOOL(enable_coin_discount_task_cnt_upper);
DECLARE_SPDM_ABTEST_INT64(coin_discount_task_cnt_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_coin_discount_task_coin_upper);
DECLARE_SPDM_ABTEST_INT64(coin_discount_task_coin_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_coin_discount_risk_user);
DECLARE_SPDM_ABTEST_INT64(coin_lower_for_coin_discount);
DECLARE_SPDM_ABTEST_INT64(deep_coin_lower_for_coin_discount);
DECLARE_SPDM_ABTEST_BOOL(enable_coin_discount_refund);
DECLARE_SPDM_ABTEST_BOOL(enable_coin_discount_refund_for_target_ocpx);
DECLARE_SPDM_ABTEST_BOOL(enable_coin_discount_by_risk_score);
DECLARE_SPDM_ABTEST_INT64(refund_num_thres_for_coin_discount);
DECLARE_SPDM_ABTEST_DOUBLE(refund_rate_lower_for_coin_discount);
DECLARE_SPDM_ABTEST_DOUBLE(refund_rate_coef_for_coin_discount);
DECLARE_SPDM_ABTEST_DOUBLE(refund_num_decay_for_coin_discount);
DECLARE_SPDM_ABTEST_DOUBLE(risk_score_coef_for_coin_discount);
DECLARE_SPDM_ABTEST_BOOL(enable_extra_coin_budget_allocation);
DECLARE_SPDM_ABTEST_BOOL(enable_extra_coin_budget_allocation_rct);
DECLARE_SPDM_ABTEST_DOUBLE(extra_coin_budget_allocation_rct_p);
DECLARE_SPDM_ABTEST_STRING(extra_coin_rct_increase_ratio);
DECLARE_SPDM_ABTEST_INT64(incntv_ad_fill_tp_rs_inner_cnt_stat_thres);
DECLARE_SPDM_ABTEST_BOOL(enable_incntv_ad_realtime_progress);
DECLARE_SPDM_ABTEST_STRING(incntv_ad_realtime_pgs_thr);
DECLARE_SPDM_ABTEST_DOUBLE(incntv_ad_pgs_roi_coef);
DECLARE_SPDM_ABTEST_STRING(incntv_ad_realtime_pgs_amt_ratio);
DECLARE_SPDM_ABTEST_INT64(incntv_realtime_pgs_expire_time);
DECLARE_SPDM_ABTEST_DOUBLE(incntv_ad_realtime_pgs_admit_price_thr);
DECLARE_SPDM_ABTEST_DOUBLE(incntv_ad_pgs_common_idx_rct_p);
DECLARE_SPDM_ABTEST_BOOL(enable_incntv_ad_pgs_log_rnd);
DECLARE_SPDM_ABTEST_BOOL(enable_incntv_ad_pgs_queue_coin);
DECLARE_SPDM_ABTEST_DOUBLE(incntv_ad_pgs_coin_budget_adjust);
DECLARE_SPDM_ABTEST_BOOL(fix_incntv_ad_pgs_has_more_condition2);
DECLARE_SPDM_ABTEST_DOUBLE(preview_coin_1st_discount);

// [wanglei10] 联盟修复应用商店直投相关开关
DECLARE_SPDM_ABTEST_BOOL(enable_univ_inner_loop_filter_uplift_front_entrance);  // [huangwenbin] 联盟内循环广告屏蔽实验开关 front 入口阶段  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_univ_inner_loop_filter_uplift_front_exit);  // [huangwenbin] 联盟内循环广告屏蔽实验开关 front 出口阶段  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_fix_native_origin_price);  // [guoyuan03] 1
DECLARE_SPDM_ABTEST_BOOL(enable_splash_send_user_feature_to_adserver);
DECLARE_SPDM_ABTEST_BOOL(enable_search_smart_photo_replace_dye);  // [niejinlong] 搜索智能封面染色实验
DECLARE_SPDM_ABTEST_BOOL(disable_inspire_unlock_tube_brand);  // [weiyilong] 短剧激励 IAA 不出品牌实验
DECLARE_SPDM_ABTEST_BOOL(enable_search_app_store_device_mod_lower);  // [weiyilong] 搜索应用直跳 机型转小写
DECLARE_SPDM_ABTEST_BOOL(disable_combo_search_non_first_page);  // [weiyilong] 搜索综搜非首页不出广告实验
DECLARE_SPDM_ABTEST_BOOL(fix_search_item_kbox_normal_region);  // [weiyilong] 搜索商品 kbox 普通区域判断修正
DECLARE_SPDM_ABTEST_BOOL(universe_skip_cpm_filter_qps_protection);  // [liujiayi07] 联盟跳过 cpm 门槛过滤
DECLARE_SPDM_ABTEST_BOOL(enable_dsp_live_rerank_price_correct);  // [songchao] live rerank price 修改
DECLARE_SPDM_ABTEST_INT64(dsp_live_pk_type);  // [songchao] dsp live rerank py type
DECLARE_SPDM_ABTEST_DOUBLE(dsp_live_rerank_price_correct_ratio);  // [songchao] dsp live rerank price 矫正系数
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_mix_record_live_browse_set);  // [jiangfeng06] 激励混竞记录直播浏览记录  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_bad_photo_gather_taxes);  // [liubing05] 低质扣税
DECLARE_SPDM_ABTEST_BOOL(enable_fill_all_author_id_for_splash);
DECLARE_SPDM_ABTEST_BOOL(disable_universe_dynamic_cpm_threshold);  // [zhangyunfei03] 使用动态 CPM 门槛
DECLARE_SPDM_ABTEST_BOOL(disable_hard_ad_new);  //  [tanweihan] 屏蔽硬广
DECLARE_SPDM_ABTEST_BOOL(enable_flow_control_cpm_threshold);  // [zhangyunfei03] 使用流控状态控制 CPM 门槛
DECLARE_SPDM_ABTEST_BOOL(enable_fill_cart_item_info);  //  [liubing05] 填充购物车信息
DECLARE_SPDM_ABTEST_BOOL(enable_user_negative_admit);  // [liuxiaoyan] 负反馈用户 x 分钟不出广告开关
DECLARE_SPDM_ABTEST_BOOL(enable_hack_mix_server_show_rate);
DECLARE_SPDM_ABTEST_BOOL(enable_hack_mix_server_show_rate_by_ori);
DECLARE_SPDM_ABTEST_BOOL(ab_switch_enable_user_ad_risk_control);  // [zhouting03] 用户广告风控开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_merchant_category_value);  // [chenwu03] 联盟用户类目价值数据开关  // NOLINT
// [zhouting03] 一甜视频 & 图片修改保存页面 banner 广告位对照组实验
DECLARE_SPDM_ABTEST_BOOL(hit_m2u_new_banner_ad_pos_skip);

// [liuxiaoyan] 用户体验 & 多样性
DECLARE_SPDM_ABTEST_DOUBLE(server_client_show_rate_thanos);
DECLARE_SPDM_ABTEST_DOUBLE(server_client_show_rate_nebula);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_first_industry_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_dup_photo_fix_size);
DECLARE_SPDM_ABTEST_BOOL(enable_second_industry_duplication);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_queue_seperate);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_duplication_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_category_duplication);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_dup_photo_duplication);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_brand_name_duplication);
DECLARE_SPDM_ABTEST_BOOL(enable_nearby_inner_stream);  // [jiangyuzhen03] 主版同城页内流接流
DECLARE_SPDM_ABTEST_BOOL(enable_spu_id_v3_duplication);
DECLARE_SPDM_ABTEST_BOOL(enable_native_ad_list);
// [mengfangyuan] 搜索重定向
DECLARE_SPDM_ABTEST_INT64(search_retarget_min_cpm);
DECLARE_SPDM_ABTEST_INT64(dac_user_type);

DECLARE_SPDM_ABTEST_BOOL(enable_ft_fairness_correction);
DECLARE_SPDM_ABTEST_BOOL(enable_ft_fairness_correction_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_search_coudan_ads);  // 凑单页开关
DECLARE_SPDM_ABTEST_BOOL(enable_ft_fairness_correction_skip_nobid);
DECLARE_SPDM_ABTEST_DOUBLE(ft_fairness_correction_p_max);
DECLARE_SPDM_ABTEST_DOUBLE(ft_fairness_correction_p_min);

// [rentingyu] dpp
DECLARE_SPDM_ABTEST_BOOL(enable_dpp_diversity_filter);
DECLARE_SPDM_ABTEST_BOOL(base_dpp_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_benefit_as_dpp_similarity_score);
DECLARE_SPDM_ABTEST_BOOL(enable_paid_duanju_skip_dpp_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_skip_dpp_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_skip_dpp_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_skip_dpp_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_ctcvr_explore_skip_cpm_thr_boost_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_adx_rta_retarget_bid_boost);
DECLARE_SPDM_ABTEST_DOUBLE(adx_rta_explore_bid_boost_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(adx_rta_explore_bid_boost_value);
DECLARE_SPDM_ABTEST_STRING(adx_rta_retarget_tags_front);
DECLARE_SPDM_ABTEST_INT64(high_pcvr_mechant_user_request_time_thd);
DECLARE_SPDM_ABTEST_BOOL(enable_high_pcvr_mechant_user_level_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_high_pcvr_boost_for_inner_loop);
DECLARE_SPDM_ABTEST_DOUBLE(user_explore_boost_value_one);
DECLARE_SPDM_ABTEST_DOUBLE(user_explore_boost_value_multi);
DECLARE_SPDM_ABTEST_INT64(user_explore_cpm_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(user_explore_boost_rate_one);
DECLARE_SPDM_ABTEST_DOUBLE(user_explore_boost_rate_multi);
DECLARE_SPDM_ABTEST_DOUBLE(user_explore_cvr_bid);
DECLARE_SPDM_ABTEST_DOUBLE(user_explore_cvr_bid_mix);
DECLARE_SPDM_ABTEST_BOOL(enable_spu_id_pos_in_rank_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_brand_name_pos_in_rank_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_third_category_pos_in_rank_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_cluster_id_pos_in_rank_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_item_id_pos_in_rank_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_global_app_id_in_rank_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_pv_sensitive_third_category_id_filter);
DECLARE_SPDM_ABTEST_STRING(pv_sensitive_third_category_id_filter_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_photo_md5_in_rank_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_dup_photo_id_in_rank_filter);
DECLARE_SPDM_ABTEST_BOOL(disable_rank_pv_filter_feed_explore);
DECLARE_SPDM_ABTEST_BOOL(disable_rank_pv_filter_inner_explore);
DECLARE_SPDM_ABTEST_BOOL(disable_rank_pv_filter_thanos_mix);
DECLARE_SPDM_ABTEST_BOOL(enable_spu_id_v3_filter_compat_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_ad_skip_dpp);
DECLARE_SPDM_ABTEST_BOOL(enable_control_force_reco_dpp);
DECLARE_SPDM_ABTEST_BOOL(enable_soft_hard_split_dpp);
DECLARE_SPDM_ABTEST_BOOL(enable_user_interest_diversity_score);
DECLARE_SPDM_ABTEST_BOOL(enable_dpp_diversity_accept_score);
DECLARE_SPDM_ABTEST_DOUBLE(max_interest_diversity_score);
DECLARE_SPDM_ABTEST_BOOL(enable_explore_ad_filter_jump_out);
DECLARE_SPDM_ABTEST_INT64(explore_feed_ad_filter_pp_jump_out);
DECLARE_SPDM_ABTEST_INT64(explore_inner_ad_filter_pp_jump_out);
DECLARE_SPDM_ABTEST_BOOL(enable_random_shield_limit_page);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_block_filter_jump_out);
DECLARE_SPDM_ABTEST_INT64(ad_block_pp_jump_out);

// [zhouting03] 激励直播软硬广 pk, 硬广系数
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_inner_outer_pk_hard_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_product_explore);  // [liyilin06] 联盟 iaa 产品 bonus 策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_product_bonus_post_filter);  // [liyilin06] 联盟 iaa 产品补贴过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_product_name_adjust_ratio);  // [yangjinhui] 联盟产品维度冷启动扶持
DECLARE_SPDM_ABTEST_BOOL(enable_universe_close_second_price);  // [yangjinhui] 联盟关闭二价计费开关
DECLARE_SPDM_ABTEST_BOOL(enable_pinduoduo_second_price_add_log);   // [yangjinhui] 联盟 pdd 伪二价 rtb 落表
DECLARE_SPDM_ABTEST_BOOL(enable_pinduoduo_second_price_add_kconf);   // [yangjinhui] 联盟 pdd 伪二价支持配置
DECLARE_SPDM_ABTEST_BOOL(enable_land_page_component_post_select);  // [guanpingyin] 动态组件优选
DECLARE_SPDM_ABTEST_BOOL(enable_ad_material_derived_photo);        // [hutao] 素材派生 photo_id 替换
DECLARE_SPDM_ABTEST_BOOL(enable_ad_material_nieuwland_derived_photo);    // [hutao] 纽兰德派生 photo_id 替换
DECLARE_SPDM_ABTEST_BOOL(enable_person_mark_physical_posid);  // [zhangyunhao03] 使用算法挖掘的物理 pos
DECLARE_SPDM_ABTEST_BOOL(enable_calc_mix_benefit);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_mix_client_ad_cpm);

// [tiankaibin] ai description
DECLARE_SPDM_ABTEST_BOOL(enable_ai_description);
DECLARE_SPDM_ABTEST_BOOL(enable_esp_product_title);

DECLARE_SPDM_ABTEST_BOOL(enable_ai_cover);  // [panshunda] aigc 生产封面
DECLARE_SPDM_ABTEST_BOOL(enable_ai_cover_v2);  // [panshunda] aigc 生产封面 2

DECLARE_SPDM_ABTEST_INT64(inner_live_truncate_num);  // [wangyangrui] 直播内流流量准入 返回个数
DECLARE_SPDM_ABTEST_BOOL(enable_hc_score_include_bonus_fake);
DECLARE_SPDM_ABTEST_BOOL(enable_billing_separate_gfp);  // [tanweihan] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_skip_bid_limit);  // [tanweihan] 跳过超 auction_bid 约束 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_bs_inner_loop);  // [tanweihan] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enabel_unify_billing_ratio);  // [tanweihan] 固定计费比
DECLARE_SPDM_ABTEST_BOOL(enabel_unify_billing_ratio_fix);  // [tanweihan] 固定计费比
DECLARE_SPDM_ABTEST_BOOL(enabel_revenue_optimize_price);  // [tanweihan] 现金优化调整开关
DECLARE_SPDM_ABTEST_BOOL(enabel_price_control_skip_mcb);  // [tanweihan] 现金优化调整开关
DECLARE_SPDM_ABTEST_DOUBLE(adjust_price_reserve_price_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_price_reserve_price_stra);
DECLARE_SPDM_ABTEST_BOOL(close_billing_separate);  // [tanweihan] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_gyk_item_card_bs_spdm);  // [luwei] 商品卡明投计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_live_hosting_skip_bid_bs_spdm);  // [luwei] 直播托管跳过计费分离
DECLARE_SPDM_ABTEST_DOUBLE(gyk_item_card_bs_ratio);  // [luwei] 商品卡明投计费分离系数
DECLARE_SPDM_ABTEST_BOOL(enable_inner_loop_bs_trans_to_adpack);  // [wangyang10] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_migrate_new_cust_bs);  // [lihantong] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_inner_self_service_admit);  // [lihantong] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_pure_new_customer_bs_fix);  // [lihantong] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_fill_inner_user_plateco_info);  // [lihantong] 电商数据
DECLARE_SPDM_ABTEST_BOOL(enable_billing_separate_rewarded_video);  // [xuxu] 激励视频计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_dynamic_pos);  // [wangyangrui] 猜你喜欢动态广告位
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_random_price);  // [luwei] 猜你喜欢低 cpm 随机不计费
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_playlet_random_price);
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_soft_random_price);  // [luwei] 猜你喜欢软广低 cpm 随机不计费
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_price_discount);  // [luwei] 猜你喜欢计费打折
DECLARE_SPDM_ABTEST_DOUBLE(gyl_price_discount_ratio);  // [luwei] 猜你喜欢计费打折系数
DECLARE_SPDM_ABTEST_BOOL(enabel_merchant_roas_gsp_price);  // [luwei] 短视频 target_cost roas 二价计费
DECLARE_SPDM_ABTEST_BOOL(fix_inner_loop_min_price);  // [wangyang10] 内循环最低计费
DECLARE_SPDM_ABTEST_BOOL(enable_inner_nobid_all_gsp);  // [lihantong] 关注页 nobid 二价
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_nobid_all_gsp);  // [lihantong] 关注页 nobid 二价
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_item_card_price_discount);  // [luwei] 猜你喜欢明投计费打折
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio);  // [luwei] 猜你喜欢明投计费打折系数
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_ad_redis);  // [zengjiangwei03] 猜你喜欢参竞广告写入 redis
// [yuancuili] h 点击 item 相似品重定向 开关
DECLARE_SPDM_ABTEST_BOOL(enable_user_h_v1_sim_clk_item_read);
DECLARE_SPDM_ABTEST_BOOL(enable_search_global_dynamic_interval);  // [niejinlong] 开启全局动态位置
DECLARE_SPDM_ABTEST_BOOL(enable_search_ad_inner_stream_v2);  // [huangwei06] 搜索内流 v2 方案
DECLARE_SPDM_ABTEST_BOOL(enable_search_bigv_card_in_shop_tab);  // [zhangzhicong] 商品 tab 下出直播大卡
DECLARE_SPDM_ABTEST_BOOL(enable_search_bigv_card_in_live_tab);  // [zhangzhicong] 直播 tab 下出直播大卡
DECLARE_SPDM_ABTEST_BOOL(enable_search_live_reservation_filter);  // [zhangzhicong] 搜索直播已预约过滤
DECLARE_SPDM_ABTEST_BOOL(enable_search_image_text_card);  // [zhangzhicong] aigc * 搜索图文强样式
DECLARE_SPDM_ABTEST_BOOL(enable_search_get_router_user_info);  // [zhangzhicong] 搜索 front 从 picasso 获取 user_info // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_splash_get_router_user_info);  // [zhangzhicong] 开屏 front 从 picasso 获取 user_info // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_search_goods_tab_live_filter_acc_ad);  // [qianyangchao03] 商品tab&小店搜索&商城搜索 直播筛选项接广告 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_search_goods_tab_sales_filter_acc_ad);  // [qianyangchao03] 商品tab&小店搜索&商城搜索 销量筛选项接广告 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_search_inner_stream_ad);  // [huangwei06] 不能搜索内流广告
DECLARE_SPDM_ABTEST_BOOL(enable_search_target_break_filter_down_front);  // [huangwei06]
DECLARE_SPDM_ABTEST_BOOL(enable_splash_rtb_realtime_recall);  // [huangwei06]开屏 RTB 实时开屏
DECLARE_SPDM_ABTEST_BOOL(enable_rank_search_index);  // [huangwei06]使用 rank 的队列标识
DECLARE_SPDM_ABTEST_BOOL(enable_rank_kbox_pos);  // [huangwei06] kbox 位置下移
DECLARE_SPDM_ABTEST_BOOL(splash_rtb_gray_status_dou_di);  // [huangwei06] 开屏 rtb 灰投兜底
DECLARE_SPDM_ABTEST_BOOL(enable_cover_dsp_request);  //  [baizongyao] 原生封面视频流量是否请求效果
DECLARE_SPDM_ABTEST_INT64(gmv_filter_status);  //  [zhoushuaiyin] 淘京拼，广告过滤实验
DECLARE_SPDM_ABTEST_BOOL(enable_guided_survey_ab);  // [qijiangtao]  引导式反馈开关
DECLARE_SPDM_ABTEST_BOOL(enable_search_ad_kbox);  // [chudawei] 开启搜索广告入 kbox
DECLARE_SPDM_ABTEST_BOOL(enable_search_ad_item_kbox);  // [chudawei] 开启搜索广告入 kbox
DECLARE_SPDM_ABTEST_BOOL(enable_item_kbox_from_top_ads);  // [zhangchaoyi03] 搜索商品 kbox 最优广告
DECLARE_SPDM_ABTEST_BOOL(enable_search_ad_live_kbox);  // [chudawei] 开启搜索广告入 kbox
DECLARE_SPDM_ABTEST_BOOL(enable_click_after_recommend);  // [duantao] 点后推
DECLARE_SPDM_ABTEST_BOOL(enable_esp_mobile_price_ratio);   // [zhouxinyu] 移动版订单支付排序计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_refresh_count_manage);   // [zhoushuaiyin] 刷次间隔总开关
DECLARE_SPDM_ABTEST_BOOL(inner_nobid_charge_exp);   // [gaowei03] 联盟内循环 nobid 计费实验
DECLARE_SPDM_ABTEST_BOOL(inner_cost_cap_charge_exp);   // [gaowei03] 联盟内循环 cost cap 计费实验
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_cost_cap_second_bid);
DECLARE_SPDM_ABTEST_BOOL(enable_kmovie_explore_flow_fix_vip_req_filter);  //  [lizemin] 矩阵产品 vip 用户不出广告在 flow_explore 生效 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_merchant_retention_live_ad);   // [liubing05] 激励电商视频保送
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_merchant_fill_browse_set);   // [gaozepeng] 激励电商填充浏览记录
DECLARE_SPDM_ABTEST_DOUBLE(inspire_video_discount_ratio_lower);  // [gaozepeng] 激励视频计费打折系数下限
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_specific_pos_skip_pk)  // [gaozepeng] 激励特定广告位跳过 pk
DECLARE_SPDM_ABTEST_DOUBLE(incentive_small_game_adless_countdown_ratio);  //  [zhangxingyu03] 小游戏倒计时 ratio  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_change_one_ad);  // [zhangxingyu03]
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_all_fill_stid);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_one_more);  // [zhangxingyu03] 激励直播生效再看一个和换一个
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_deep_task_holdout);  // [zhangxingyu03] 深度激励 holdout 实验
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_iaa_sensitive_user_bill_time_adjust)
DECLARE_SPDM_ABTEST_STRING(incentive_iaa_sensitive_user_bill_time_adjust_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_sdpa_discount);  // [guanpingyin] 短剧新剧打折
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_reset_undiscount);  // [tiangeng] 短剧计费回写开关
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_discount_by_hour_v2);  // [tiangeng] 短剧小时级打折
DECLARE_SPDM_ABTEST_BOOL(enable_close_na_fiction_price_discount);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_sdpa_discount_skip_mcb);  // [guanpingyin] 短剧新剧打折跳过 mcb
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_total_discount);  // [guanpingyin] 短剧打折
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_total_discount_sup_mcb);  // [guanpingyin] 短剧打折支持 mcb
DECLARE_SPDM_ABTEST_BOOL(enable_train_smart_offer_realtime_feature);
DECLARE_SPDM_ABTEST_BOOL(enable_wechat_new_product_auto_price_discount);  // [jiangnan07] 小游戏新品自动打折  // NOLINT
DECLARE_SPDM_ABTEST_INT64(enhance_ecpm_strategy_default_tag);   //  [songxu] 商家计费客户侧实验
DECLARE_SPDM_ABTEST_BOOL(enable_cid_spu_strategy_exp);      // [songxu] 商家计费新品实验
DECLARE_SPDM_ABTEST_BOOL(enable_cid_spu_restart_strategy_exp);      // [songxu] 商家放衰退策略
DECLARE_SPDM_ABTEST_BOOL(enable_cid_corp_default_strategy_exp);  // [songxu] 商家无标记打折实验
DECLARE_SPDM_ABTEST_DOUBLE(cid_corp_default_price_ratio);        // [songxu] 商家无标记打折默认打折系数
DECLARE_SPDM_ABTEST_BOOL(enable_new_product_strategy);      // [yuchengyuan] 行业新客计费实验开关
DECLARE_SPDM_ABTEST_STRING(new_product_strategy_exp);   // [yuchengyuan] 行业新客计费实验标记
DECLARE_SPDM_ABTEST_BOOL(enable_new_product_discount_strategy);      // [yuchengyuan] 行业新客实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_lpsdeep_support);  // [xuyanyan03] 线索有效获客扶持开关
DECLARE_SPDM_ABTEST_STRING(enable_lpsdeep_support_decay);  // [xutaotao03] 线索有效获客扶持 退坡
DECLARE_SPDM_ABTEST_BOOL(enable_pm_poi_search_user_ecpc);  // [xutaotao03] 汽车品牌字段透传
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_acq_gen_score_tags);  // [liangyukang] iaa 跨产品泛化落预估值
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_price_private_message);  // [zhaijianwei] 私信扶持开关
DECLARE_SPDM_ABTEST_STRING(private_message_support_decay_group);  // [zhaijianwei] 私信扶持 退坡
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_price_simple_promotion);  // [zhaijianwei] 商品速推扶持开关
DECLARE_SPDM_ABTEST_STRING(simple_promotion_support_decay_group);  // [zhaijianwei] 商品速推扶持 退坡
DECLARE_SPDM_ABTEST_BOOL(enable_user_kgame_duration_data);  // [jiangpeng07] 快小游停留时长字段下发
DECLARE_SPDM_ABTEST_BOOL(enable_user_game_explore_data);  // [jiangpeng07] 快小用户探索字段下发
DECLARE_SPDM_ABTEST_DOUBLE(cid_discount_ratio_lower);    // [songxu] cid 计费打折系数下限
DECLARE_SPDM_ABTEST_DOUBLE(cid_discount_ratio_upper);    // [songxu] cid 计费打折系数上限
DECLARE_SPDM_ABTEST_BOOL(enable_cid_quality_strategy_exp);  // [songxu] 商家计费质量分层实验
DECLARE_SPDM_ABTEST_INT64(cid_quality_strategy_tag);      // [songxu] cid 质量分层 tag
DECLARE_SPDM_ABTEST_INT64(cid_spu_strategy_tag);          // [songxu] cid spu 策略
DECLARE_SPDM_ABTEST_BOOL(enable_cid_bid_boost_roas_exp);           // [songxu] 商家扶持 boost
DECLARE_SPDM_ABTEST_BOOL(enable_cid_global_ratio_exp);
DECLARE_SPDM_ABTEST_BOOL(disable_cid_price_strategy_exp);  // [songxu] 商家扶持开关
DECLARE_SPDM_ABTEST_BOOL(enable_cid_ab_global_ratio_exp);  // [songxu] 商家扶持 global 系数 ab 开关
DECLARE_SPDM_ABTEST_DOUBLE(cid_ab_global_price_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_account_zk_strategy_ocpx_exp);  // [songxu] 商家折扣白名单
DECLARE_SPDM_ABTEST_BOOL(enable_cid_price_record_rewrited);  // [songxu] 商家折扣重写进入日志
DECLARE_SPDM_ABTEST_BOOL(enable_user_zhongcao_data);  // [linwei] 内循环用户种草行为

// [zhangxingyu03]
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_coin_widget);  // [zhangxingyu03] draw 流下发金币挂件黑名单 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_bill_time_use_iaa_score_new);
DECLARE_SPDM_ABTEST_BOOL(enable_game_iaa_countdown_time_adjust);
DECLARE_SPDM_ABTEST_INT64(incentive_game_iaa_countdown_time);

//  [gaowei03] 联盟内循环 cost_cap next_cpm
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_video_pay_charge_exp);  //  [gaowei03] 联盟订单收费上限开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_nobid_second_bid)   // [gaowei03] 联盟内循环 nobid next_cpm
DECLARE_SPDM_ABTEST_INT64(refresh_count_redis_cache_timestamp);  //  [zhoushuaiyin] 刷次间隔时间区间
DECLARE_SPDM_ABTEST_INT64(guess_you_like_multi_return_num);  // [wangyangrui] 猜你喜欢动态广告位返回个数
DECLARE_SPDM_ABTEST_INT64(cache_remove_live_type);  // [yinliang] 精排缓存删除直播的类型
DECLARE_SPDM_ABTEST_BOOL(enable_universe_use_new_hash_did);  // [yinliang] 联盟缓存 did 升级
DECLARE_SPDM_ABTEST_INT64(refresh_count_cache_ad_quota);  //  [zhoushuaiyin] 刷次间隔最大个数
DECLARE_SPDM_ABTEST_BOOL(enable_new_direct_merchant_discount);   // [sixianbo] 淘系直投打折实验 ab 开关
DECLARE_SPDM_ABTEST_BOOL(use_new_order);
DECLARE_SPDM_ABTEST_BOOL(enable_small_app_direct_jump);  // [wanglei10] 小程序广告直跳
//  [fengyajuan] 联盟二价计费考虑二位 boost 后计价
DECLARE_SPDM_ABTEST_BOOL(enable_non_conversion_visitor_explore);   // [wanghouzhi] 无转化人群探索
DECLARE_SPDM_ABTEST_BOOL(enable_archimedes_nebula_rpc_admit);  // [mateng05] 内粉极速版 rpc 侧流量准入实验
DECLARE_SPDM_ABTEST_INT64(inspire_live_default_coin_num);  //  [shoulifu03] 激励直播下发金币默认值
DECLARE_SPDM_ABTEST_INT64(inner_photo_refresh_times_pos);  // [luoqiang] 内粉刷次实验，默认广告位
DECLARE_SPDM_ABTEST_INT64(universe_agg_scene_ad_num);   // [wanglei10] 联盟激励聚合场景返回广告数
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_cpm_bound_explore);  // [wanglei10] 联盟内循环顶价配置
DECLARE_SPDM_ABTEST_BOOL(enable_universe_forced_impression_exp);  // [yangjinhui] 联盟强出逻辑开关  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inner_nobid_charge_ratio_upper);  // [gaowei03] 联盟内循环 nobid 计费最大比
DECLARE_SPDM_ABTEST_DOUBLE(merchant_video_pay_charge_ratio_upper);  // [gaowei03] 联盟内循环支付计费最大比
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_impression_price);
DECLARE_SPDM_ABTEST_DOUBLE(inner_cost_cap_charge_ratio_upper);   // [gaowei03] 联盟内循环 cost_cap 计费最大比
DECLARE_SPDM_ABTEST_DOUBLE(inner_live_nobid_charge_ratio_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_ranking_list);
DECLARE_SPDM_ABTEST_BOOL(rewarded_ranking_list_only_outer);
// [liyichen front]
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_fan_author);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_item_impression_ts);
DECLARE_SPDM_ABTEST_BOOL(enable_ud_paied_tube_trade_event_ts_list);
DECLARE_SPDM_ABTEST_BOOL(enable_judou_chaoxiaoe_no_cost);
DECLARE_SPDM_ABTEST_BOOL(enable_chaoxiaoe_no_cbu);
DECLARE_SPDM_ABTEST_BOOL(enable_judou_cost_more);
DECLARE_SPDM_ABTEST_BOOL(enable_dabao_chaoxiaoe_no_cost);
DECLARE_SPDM_ABTEST_BOOL(enable_dabao_cost_more);
DECLARE_SPDM_ABTEST_DOUBLE(C_bu_min_filter_price);
DECLARE_SPDM_ABTEST_BOOL(enable_cbu_exp_tail);
DECLARE_SPDM_ABTEST_BOOL(chaoxiaoe_enable_cbu_exp_tail);
DECLARE_SPDM_ABTEST_DOUBLE(xifan_coin_shopping_around_thres);
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_coin_shopping_around);
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_filter_cpm_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_skip_zijianzhan);
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_restrict_ad_num);
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_restrict_ad_num_new);
DECLARE_SPDM_ABTEST_INT64(tmp_offer_result);   //  短剧 C 补用户展示价格涨跌值  尾号实验用
DECLARE_SPDM_ABTEST_INT64(tmp_offer_value);   //  短剧 C 补广告主补贴价格涨跌值  尾号实验用
DECLARE_SPDM_ABTEST_INT64(tmp_activity_interval);   // 短剧 C 补实验频控 尾号实验用
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_take_inner_live_stright);

// [wangxiaohu front]
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_skip_front_to_ad_pack);

// [cuihongyi]
DECLARE_SPDM_ABTEST_BOOL(disable_maill_tab_ad_front);
DECLARE_SPDM_ABTEST_BOOL(disable_zhuanqian_tab_ad_front);
DECLARE_SPDM_ABTEST_BOOL(disable_buyer_home_ad_front);
DECLARE_SPDM_ABTEST_BOOL(disable_guess_you_like_ad_front);

DECLARE_SPDM_ABTEST_BOOL(enable_kxy_user_subsidies);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_increment_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_increment_price_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_native_black_game_roi);
DECLARE_SPDM_ABTEST_BOOL(enable_dark_acc_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_native_increment);

// [zhangwei26]
DECLARE_SPDM_ABTEST_BOOL(enable_kconf_author_id_redis_native);
DECLARE_SPDM_ABTEST_BOOL(enable_kconf_author_id_redis_normal);
DECLARE_SPDM_ABTEST_BOOL(enable_kconf_author_id_redis);

// [lichunchi]
DECLARE_SPDM_ABTEST_BOOL(enable_inner_buyer_industry_id_list); // 内循环自助新客行业列表传入 // NOLINT


DECLARE_SPDM_ABTEST_BOOL(disable_explore_feed_ad_front_admit);  // [yesiqi] 发现页双列外流商业化侧 hold 实验
DECLARE_SPDM_ABTEST_BOOL(disable_explore_inner_ad_front_admit);  // [yesiqi] 发现页内流商业化侧 hold 实验

// [gaowei03] 联盟内循环 cost_cap 计费最大比
// [moqi] 金牛 PC 硬广大客白名单一价计费打折系数
DECLARE_SPDM_ABTEST_DOUBLE(head_customer_author_seperate_discount_ratio);

// [luoqiang]
DECLARE_SPDM_ABTEST_BOOL(enable_inner_select_first_refresh_exp);  // 内粉精选页首刷位置实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_inner_refresh_times_pos_exp);     // 内粉刷次位置实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_inner_flow_optimization_exp);     // 内粉流量优选实验开关
DECLARE_SPDM_ABTEST_INT64(inner_fanstop_select_first_pos);        // 内粉精选页首刷位置
DECLARE_SPDM_ABTEST_INT64(inner_fanstop_select_first_pos_in_target_city);  // 目标城市内粉精选页首刷位置
DECLARE_SPDM_ABTEST_INT64(inner_fanstop_select_vv_step_in_target_city);  // 目标城市内粉精选页 vv 间隔
DECLARE_SPDM_ABTEST_BOOL(enable_inner_universe_request_sessionSvr);
DECLARE_SPDM_ABTEST_BOOL(disable_splash_ad);   // [duantao] 命中实验不出开屏
DECLARE_SPDM_ABTEST_BOOL(enable_ad_merchant_guess_you_like_flow);  // [lizemin] merchant guess you like admit
DECLARE_SPDM_ABTEST_BOOL(enable_ad_side_window_flow);  // [lizemin] 侧滑小窗流量控制
DECLARE_SPDM_ABTEST_BOOL(enable_rta_ab_exp);  // [lizemin] rta ab 实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_creator_profile_ad);  // [wuyonghong] 客态 P 页流量控制
DECLARE_SPDM_ABTEST_BOOL(enable_profile_skin_flow);  // [wangyangrui] P 页皮肤广告位准入
DECLARE_SPDM_ABTEST_BOOL(enable_explore_feed_filter);  // [hehandong] 主站发现页外流广告位准入
// [zhouting03] 混排流量 brand dsp 广告统一排序，不再走 pk
DECLARE_SPDM_ABTEST_BOOL(enable_follow_realtime_high_gmv_author);  // [lishaozhe] 内循环涨粉实时高 gmv 作者列表数据开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_skip_min_price_ratio);   // [yushengkai] 外循环跳过最低计费比
DECLARE_SPDM_ABTEST_BOOL(enable_origin_price_fix);
DECLARE_SPDM_ABTEST_INT64(native_thanos_explore_max_price);  // [fukunyang] 软广精选页以及发现页单列单独的计费上限  //  NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_agg_scene_discount_ratio);  // [wanglei10] 联盟激励聚合场景非首位广告打折系数  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(hard_selected_price_cail);  // [fukunyang] 硬广精选页收费校准
DECLARE_SPDM_ABTEST_BOOL(enable_user_rfm_data);  // [fukunyang] 接入用户高客单价 rfm 分
DECLARE_SPDM_ABTEST_BOOL(enable_photo_self_recall);  // [fukunyang] 重参竞 photo 写入开关

// [gaowei03]  联盟内循环请求 session_server 开关
DECLARE_SPDM_ABTEST_BOOL(enable_app_page_to_id);  // [fanshaopu] 下载类中间页 id 化迁移
// [shoulifu03]  激励直播下单金币个性化
DECLARE_SPDM_ABTEST_INT64(splash_pic_show_time);  // 开屏图片广告展现时间
DECLARE_SPDM_ABTEST_INT64(splash_cache_limit);  // 开屏预加载素材缓存限制
DECLARE_SPDM_ABTEST_INT64(inspire_live_order_award_default_value);
DECLARE_SPDM_ABTEST_INT64(splash_combo_frequency_threshold);  // [zhangyunhao03] 组合样式频控
DECLARE_SPDM_ABTEST_INT64(inspire_shake_frequency_threshold);  // [zhangyunhao03] 激励视频摇动频控
DECLARE_SPDM_ABTEST_INT64(splash_shake_frequency_threshold);  // [zhangyunhao03] 开屏摇动频控
DECLARE_SPDM_ABTEST_INT64(splash_rotate_frequency_threshold);  // [zhangyunhao03] 开屏转动频控
DECLARE_SPDM_ABTEST_INT64(splash_slide_frequency_threshold);  // [zhangyunhao03] 开屏滑动频控
DECLARE_SPDM_ABTEST_INT64(splash_combo2_frequency_threshold);  // [zhangyunhao03] 开屏组合样式 2 频控
DECLARE_SPDM_ABTEST_INT64(fullscreen_combo3_frequency_threshold);  // [shanminghui] 全屏 combo3 频控
DECLARE_SPDM_ABTEST_INT64(inspire_combo3_frequency_threshold);  // [shanminghui] 激励 combo3 频控
DECLARE_SPDM_ABTEST_INT64(interstitial_combo2_frequency_threshold);  // [zhangyunhao03] 插屏组合样式 2 频控
DECLARE_SPDM_ABTEST_INT64(inspire_rotate_frequency_threshold);  // [zhangyunhao03] 激励视频扭动频控
DECLARE_SPDM_ABTEST_INT64(fullscreen_rotate_frequency_threshold);  // [zhangyunhao03] 全屏视频扭动频控
DECLARE_SPDM_ABTEST_STRING(inner_nearline_exp_name);   // [lihantong] 近线实验名
DECLARE_SPDM_ABTEST_STRING(inner_explore_dsp_fixed_ad_pos);   // [zhouting03] 发现页内流内循环固定广告位置
DECLARE_SPDM_ABTEST_STRING(da_jian_kang_text);   // [zhouting03] 大健康广告标
DECLARE_SPDM_ABTEST_STRING(inner_explore_fanstop_fixed_ad_pos);  // [zhouting03] 发现页内流外循环固定广告位置
DECLARE_SPDM_ABTEST_STRING(feed_mix_ab_name);  // [liubing05] 发现页外流混排 ab 参数配置
DECLARE_SPDM_ABTEST_STRING(buyer_home_page_multiple_ad_pos);   // [yesiqi] 买家首页硬广固定广告位置
DECLARE_SPDM_ABTEST_STRING(guess_you_like_multi_pos);   // [huangzhaokai] 猜你喜欢硬广固定广告位置
DECLARE_SPDM_ABTEST_INT64(page_size_thr_first);  // [liubing05] 发现页内流 page size 阈值
DECLARE_SPDM_ABTEST_INT64(mix_rank_support_multiple_ad_num_for_inner_explore);  // 发现页内流软一返多数量
DECLARE_SPDM_ABTEST_BOOL(enable_multiple_ad_num_for_inner_explore);  // [liubing05] 发现页内流软一返多开关
DECLARE_SPDM_ABTEST_BOOL(enable_soft_min_one_ad);  // [liubing05] 发现页内流软广最少广开关
DECLARE_SPDM_ABTEST_BOOL(add_rule_id_to_label_info);  // [liyilin06] 落 rule_id 到数据流
DECLARE_SPDM_ABTEST_BOOL(enable_native_ad_billing_seperate);        // [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_append_native_ranking_list);        // [huangzhaokai] 软广精排列表增加开关
DECLARE_SPDM_ABTEST_BOOL(enable_append_native_pc_live);             // [huangzhaokai] 软广电脑端直播增加开关
DECLARE_SPDM_ABTEST_BOOL(enable_append_native_pc_photo);            // [huangzhaokai] 软广电脑端短视频增加开关
DECLARE_SPDM_ABTEST_BOOL(enable_append_native_mobile_direct_live);  // [huangzhaokai] 软广移动端直投增加开关
DECLARE_SPDM_ABTEST_BOOL(enable_append_native_mobile_p2l);          // [huangzhaokai] 软广移动端引流增加开关
DECLARE_SPDM_ABTEST_BOOL(enable_append_native_mobile_photo);        // [huangzhaokai] 软广移动端短视频增加开关
DECLARE_SPDM_ABTEST_INT64(max_native_ranking_list_size);            // [huangzhaokai] 软广精排列表缓存最大长度
DECLARE_SPDM_ABTEST_INT64(max_amd_photo_ranking_list_size);         // [huangzhaokai] 硬广精排列表缓存最大长度
DECLARE_SPDM_ABTEST_INT64(max_native_ranking_list_idx);             // [huangzhaokai] 软广精排列表选取的最大个数  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(max_amd_photo_ranking_list_idx);          // [huangzhaokai] 硬广精排列表选取的最大个数  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_hard_p2l_price_uplimit_protect);    // [huangzhaokai] 移动端硬广引流收费上限保护开关  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_hard_live_price_uplimit_protect);   // [huangzhaokai] 移动端硬广直投收费上限保护开关  //  NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(mobile_hard_p2l_price_uplimit_value);           // [huangzhaokai] 移动端硬广引流收费上限保护值  //  NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(mobile_hard_live_price_uplimit_value);          // [huangzhaokai] 移动端硬广直投收费上限保护值  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(native_ad_enable_native_price_autocpa_diff);     // [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_fill_shadow_cid_field);
DECLARE_SPDM_ABTEST_BOOL(enable_enable_append_splash_cid);
DECLARE_SPDM_ABTEST_DOUBLE(native_ad_bs_price_ratio_lower);    //  [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_DOUBLE(native_ad_bs_price_ratio_upper);    //  [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_DOUBLE(native_ad_billing_separate_additional_ratio);    //  [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_INT64(native_ad_bs_price_lower);           // [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_INT64(native_ad_bs_price_upper);           // [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_INT64(inner_loop_min_price);           // [wangyang10] 内循环最小计费
DECLARE_SPDM_ABTEST_BOOL(enable_delivery_upthr_adjust);  // [nizhihao] 调整下发计费上限开关
DECLARE_SPDM_ABTEST_BOOL(enable_rta_ratio_precision_fix);  // [lizemin] rta_bid_ratio 精度修复
DECLARE_SPDM_ABTEST_DOUBLE(delivery_up_thr);  // [nizhihao] 下发计费上限值
DECLARE_SPDM_ABTEST_BOOL(enable_biz_extra_info);                  // [songchao] 为混排透传 biz_extra_info 字段
DECLARE_SPDM_ABTEST_BOOL(enable_new_biz_extra_info);  // [songchao] 为混排透传 new_biz_extra_info 字段
DECLARE_SPDM_ABTEST_BOOL(enable_biz_extra_add_info);              // [songchao] 为混排透传 biz_extra_info 字段
DECLARE_SPDM_ABTEST_BOOL(enable_cid_refund_request_exp);      // [songxu] cid 退单策略
DECLARE_SPDM_ABTEST_BOOL(disable_cache_all_live_ad);  //  [songchao] 所有直播广告参与缓存 pk 开关
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_bs_ab);  // [shengmingyang] 直播全站计费分离流量侧实验
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_bs_ab_v2);  // [shengmingyang] 直播全站计费分离流量侧实验 v2
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_skip_bs_ab);  // [zhanglong08] 直播全站跳过计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_bs_control);  // [zhanglong08] 直播全站直投作品计费分离控制
DECLARE_SPDM_ABTEST_DOUBLE(storewide_live_bs_overcharge_ratio);  // [shengmingyang] 直播全站计费分离超收系数
DECLARE_SPDM_ABTEST_DOUBLE(storewide_live_bs_discount_ratio);  // [shengmingyang] 直播全站计费分离打折系数
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_price_discount);  //  [huangzhaokai] 移动端收费打折开关
DECLARE_SPDM_ABTEST_BOOL(disable_author_pk);      // [guoyuan03] 开关
DECLARE_SPDM_ABTEST_BOOL(outer_author_pk_check);      // [zhangmengxin] 修复检查外循环逻辑
DECLARE_SPDM_ABTEST_INT64(new_inner_fanstop_avoid_top_k);  // [shengmingyang] 新内粉不出前 k 位
DECLARE_SPDM_ABTEST_DOUBLE(native_cpm_zoom_factor);  // [shengmingyang] 软广 cpm 缩放系数
DECLARE_SPDM_ABTEST_DOUBLE(inner_native_cpm_zoom_factor);  // [yesiqi] 内循环软广 cpm 缩放系数
DECLARE_SPDM_ABTEST_DOUBLE(native_gpm_to_cpm_factor);  // [shengmingyang] 软广 gpm 报价系数
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_coin_roi);  //  [shoulifu03]  激励直播金币 roi
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_ctr_calibration_ratio);  //  [shoulifu03]  激励直播 ctr 校准系数
DECLARE_SPDM_ABTEST_STRING(inspire_live_coin_exp_tag);  //  [shoulifu03]  激励直播金币实验 tag
DECLARE_SPDM_ABTEST_STRING(mobile_price_discount_exp_tag);  //  [huangzhaokai]  移动端打折实验 tag
DECLARE_SPDM_ABTEST_INT64(inspire_live_custom_coin_lower);  //  [shoulifu03] 激励直播金币上限
DECLARE_SPDM_ABTEST_INT64(inspire_live_custom_coin_upper);  //  [shoulifu03] 激励直播金币下限
DECLARE_SPDM_ABTEST_BOOL(enable_search_user_risk_control);  //  [chengyuxuan] 搜索风控黑名单过滤需求
DECLARE_SPDM_ABTEST_BOOL(enable_guess_like_reset_product_id);  //  [hehandong] 猜喜商品 ID 重新赋值
DECLARE_SPDM_ABTEST_BOOL(enable_big_v_card_decoupling);  //  [duantao] 用户大卡解耦
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_request_brand_ads);  //  [yangxibo] 品牌接入买家首页
DECLARE_SPDM_ABTEST_BOOL(enable_gyk_pk_with_cpm);  //  [luwei] 猜喜排序使用 cpm
DECLARE_SPDM_ABTEST_BOOL(enable_gyk_diff_product_id);  //  [luwei] 猜喜多坑位使用不同商品
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_photo_card);  //  [huangzhaokai] 猜喜短视频出短视频卡
DECLARE_SPDM_ABTEST_DOUBLE(guess_like_photo_card_probability);  //  [liubing05] 猜喜出短视频卡的概率
// [yechen05] 允许请求 redis 获取用户本地兴趣标签开关
DECLARE_SPDM_ABTEST_BOOL(enable_user_local_tag_request_redis);
DECLARE_SPDM_ABTEST_BOOL(enable_adfront_force_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_enable_adfront_force_explore_gd_order);
DECLARE_SPDM_ABTEST_BOOL(enable_enable_adfront_force_explore_gd_disorder);
DECLARE_SPDM_ABTEST_BOOL(enable_force_product_photo_explore_v2);
DECLARE_SPDM_ABTEST_STRING(outer_high_value_user_tag_new_key);

// [yechen05] 允许请求 redis 获取用户本地生活各种兴趣标签开关
DECLARE_SPDM_ABTEST_BOOL(enable_user_local_life_request_redis);
// // 行业直播红包类计费比
// DECLARE_SPDM_ABTEST_BOOL(enable_industry_live_price_ratio);

DECLARE_SPDM_ABTEST_DOUBLE(normal_hc_to_cpm_factor);
DECLARE_SPDM_ABTEST_BOOL(enable_normal_hack_cpm_plugin);
DECLARE_SPDM_ABTEST_BOOL(enable_all_normal_hack_cpm);
DECLARE_SPDM_ABTEST_BOOL(enable_industry_normal_hack_cpm);
DECLARE_SPDM_ABTEST_BOOL(enable_boost_normal_mix_bid);
DECLARE_SPDM_ABTEST_BOOL(enable_set_url_refactor_diff_test);
DECLARE_SPDM_ABTEST_BOOL(enable_set_schema_url_refactor_diff_test);
DECLARE_SPDM_ABTEST_BOOL(enable_set_url_refactor);
DECLARE_SPDM_ABTEST_BOOL(enable_set_schema_url_refactor);
DECLARE_SPDM_ABTEST_BOOL(enable_search_buttom_bar_no_ads);
DECLARE_SPDM_ABTEST_STRING(server_show_rate_tag);
DECLARE_SPDM_ABTEST_STRING(adload_control_rate_tag);

DECLARE_SPDM_ABTEST_BOOL(enabel_photo_ceiling_front_explore);   // [guoqi03] 素材天花板
DECLARE_SPDM_ABTEST_BOOL(enable_game_shoufa_front_explore);     // [guoqi03] 游戏首发
// [zhengchaofan] 搜索广告内流固定坑位 fix
DECLARE_SPDM_ABTEST_BOOL(enable_search_ad_inner_stream_fix_interval);
DECLARE_SPDM_ABTEST_BOOL(enable_search_ad_inner_skip_rs);  // [zhengchaofan] 搜索广告内流跳过 RS 间隔减一逻辑
// [zhengchaofan] 搜索广告点后推商业得分权重
DECLARE_SPDM_ABTEST_DOUBLE(click_after_reco_commerce_weight);
DECLARE_SPDM_ABTEST_DOUBLE(click_after_reco_rele_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(outer_fans_use_extra_weight);  // [zengdi] add outer fans extra weight

// [yupeng05] 广告短剧属于内循环实验
DECLARE_SPDM_ABTEST_BOOL(enable_mix_ad_inner_outer_flag);
// [shoulifu03] 优质软广去标新标准
DECLARE_SPDM_ABTEST_BOOL(enable_good_soft_ad_new_standard);
// [wangyuan11] 内循环计划层级调价
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_use_price);
DECLARE_SPDM_ABTEST_BOOL(enable_live_hosting_photo_to_redis);
DECLARE_SPDM_ABTEST_DOUBLE(inner_campaign_bid_price_ratio_lower);  // [wangyuan11] 内循环计划层级调价单元计费分离下限  //  NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inner_campaign_bid_price_ratio_upper);  // [wangyuan11] 内循环计划层级调价单元计费分离上限  //  NOLINT
// [lihantong] 内循环中小计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_inner_smb_bid_unit_price);  // [lihantong] 内循环中小计费分离  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_smb_price_after_bs);  // [lihantong] 内循环中小计费分离  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_smb_bid_unit_price_skip_all);  // [lihantong] 内循环中小计费分离  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_smb_account_price_skip_all);  // [lihantong] 内循环中小计费分离  //  NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inner_video_roas_achieve_ratio_lower);  // [lihantong] 内循环中小计费分离  //  NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(new_cust_bs_weight_constant);  // [lihantong] 内循环中小计费分离  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_new_customer_bs);  // [yushenglong] 内循环中小计费打折
DECLARE_SPDM_ABTEST_BOOL(enable_inner_new_customer_bs_backup);  // [yushenglong] 内循环中小计费打折
DECLARE_SPDM_ABTEST_BOOL(enable_smb_price_discount);  // [yushenglong] 内循环中小计费打折
DECLARE_SPDM_ABTEST_BOOL(enable_smb_acccount_control);  // [lihantong] 内循环中小计费打折
DECLARE_SPDM_ABTEST_BOOL(enable_smb_unit_over_charge);  // [lihantong] 内循环中小计费打折
DECLARE_SPDM_ABTEST_BOOL(enable_price_discount_record_admit);  // [yushenglong] 内循环中小打折前费用记录

DECLARE_SPDM_ABTEST_BOOL(enable_bs_dynamix_ratio);
// [yuanwei09] 添加 user 侧生态分数
DECLARE_SPDM_ABTEST_STRING(adx_shop_action_prefix);    // [yuanwei09] add adx_shop_action_prefix
DECLARE_SPDM_ABTEST_STRING(user_blue_sea_category_prefix);  // [yuanwei09] add user_blue_sea_category
DECLARE_SPDM_ABTEST_STRING(ecology_score_prefix);    // [yuanwei09] 生态分前缀
DECLARE_SPDM_ABTEST_BOOL(enable_shop_data_filled);    // [wanghongfei] add shop data
DECLARE_SPDM_ABTEST_STRING(commercial_user_group_prefix);  // [shoulifu03] 商业化用户分层字段前缀
DECLARE_SPDM_ABTEST_BOOL(enable_change_to_double_col);  //  [liubing05] 猜喜切换单双列
DECLARE_SPDM_ABTEST_BOOL(enable_change_to_double_col_for_merchant_card);  //  [liubing05] 猜喜切换单双列
DECLARE_SPDM_ABTEST_BOOL(enable_fix_change_to_double_col);

// [wangkai26]
DECLARE_SPDM_ABTEST_DOUBLE(shelf_express_gyl_price_ratio);  // 货架明投计费打折猜喜
DECLARE_SPDM_ABTEST_DOUBLE(shelf_express_bh_price_ratio);  // 货架明投计费打折买首
DECLARE_SPDM_ABTEST_DOUBLE(shelf_express_mall_price_ratio);  // 货架明投计费打折商城
DECLARE_SPDM_ABTEST_DOUBLE(shelf_express_program_price_ratio);  // 货架明投计费打折客户无感知部分
DECLARE_SPDM_ABTEST_DOUBLE(shelf_express_customize_price_ratio);  // 货架明投计费打折客户有感知部分
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_new);  // 猜你喜欢商品卡计费打折系数
DECLARE_SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio);  // 猜喜短引计费打折系数
DECLARE_SPDM_ABTEST_DOUBLE(bh_p2l_price_discount_ratio);  // 买首短引计费打折系数
DECLARE_SPDM_ABTEST_DOUBLE(mail_p2l_price_discount_ratio);  // 商城短引计费打折系数
DECLARE_SPDM_ABTEST_DOUBLE(zq_p2l_price_discount_ratio);  // 赚钱短引计费打折系数
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm);  //  货架使用 gpm 选取
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm_new);  //  货架使用后验 gpm 选取
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm_gyl);  //  猜喜使用 gpm 选取
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm_mail);  //  商城使用 gpm 选取
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm_bh);  //  买首使用 gpm 选取
DECLARE_SPDM_ABTEST_BOOL(enable_request_search_action_item);  //请求 colossus table
DECLARE_SPDM_ABTEST_INT64(search_action_item_list_size);  // 请求 colossus table 序列长度
DECLARE_SPDM_ABTEST_BOOL(enable_request_search_action_item_fix);  //请求 colossus table
DECLARE_SPDM_ABTEST_BOOL(enable_user_search_action_data);  // 货架搜索数据

// [qiancheng10]
DECLARE_SPDM_ABTEST_BOOL(enable_cid_dup_photo_id_write_userinfo);  // cid 参竞 dup_photo_id 数据写入 userinfo
DECLARE_SPDM_ABTEST_BOOL(enable_cid_account_id_write_userinfo);  // cid 参竞 account_id 数据写入 userinfo
// cid 站外数据助攻营业执照数据写入 userinfo
DECLARE_SPDM_ABTEST_BOOL(enable_cid_assit_corporation_write_userinfo);

// [tangsiyuan] 粉条计费分离保成本参数
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_price_ratio_upper);
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_price_ratio_lower);
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_global_price_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_nobid_bs_ratio_lower);
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_nobid_bs_ratio_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_prms_discount);
DECLARE_SPDM_ABTEST_BOOL(disable_fanstop_temu_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_esp_self_service_mix_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_industry_ad_inner_outer_flag);
DECLARE_SPDM_ABTEST_BOOL(enable_phone_call_use_outer_auction_info);

// [jiayalong] 粉条计费分离相关
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_bs_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_bs_price_ratio_lower);
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_bs_price_ratio_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_specific_bs_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_billing_on_cost_ratio);

// [jiayalong] 外循环不顶价策略
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_gsp_skip_same_agent_normal);
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_gsp_skip_same_agent_bidtype_set);
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_gsp_skip_same_agent_ocpx_set);
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_using_universe_retarget);
DECLARE_SPDM_ABTEST_BOOL(universe_tiny_user_query_new_key);

DECLARE_SPDM_ABTEST_DOUBLE(
    user_group_server_show_ratio_lower_bound);  //  [shoulifu03]  分人群 adload 控制兜底曝光系数
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_price_ratio);      // [wantao21] 内循环计费打折

DECLARE_SPDM_ABTEST_DOUBLE(po_quan_price_discount_extra_discount);
DECLARE_SPDM_ABTEST_DOUBLE(po_quan_price_discount_min_discount);
DECLARE_SPDM_ABTEST_BOOL(enable_touke_user_impression_city_product_id_list);
DECLARE_SPDM_ABTEST_STRING(po_quan_strategy_versions);
DECLARE_SPDM_ABTEST_DOUBLE(po_quan_price_discount_product_name_extra_discount);

// [xiongyajiao]
DECLARE_SPDM_ABTEST_BOOL(enable_unify_inner_fanstop_rate);
DECLARE_SPDM_ABTEST_BOOL(enable_soft_boost_for_inner_fanstop);
DECLARE_SPDM_ABTEST_DOUBLE(default_mix_weight_ratio_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(default_mix_weight_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(mix_weight_origin_ratio_native);
DECLARE_SPDM_ABTEST_DOUBLE(mix_weight_origin_ratio_normal);
DECLARE_SPDM_ABTEST_DOUBLE(mix_weight_origin_ratio_native_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(mix_weight_origin_ratio_normal_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(soft_hard_boost_rate);
DECLARE_SPDM_ABTEST_DOUBLE(soft_for_fanstop_boost_rate);
DECLARE_SPDM_ABTEST_DOUBLE(mix_inner_fanstop_boost_rate);
DECLARE_SPDM_ABTEST_INT64(mix_inner_fanstop_upper_bound);

// [nizhihao]
DECLARE_SPDM_ABTEST_DOUBLE(normal_cpm_zoom_factor_v2);
DECLARE_SPDM_ABTEST_DOUBLE(normal_cpm_zoom_factor_nebula_v2);
DECLARE_SPDM_ABTEST_DOUBLE(native_cpm_zoom_factor_v2);
DECLARE_SPDM_ABTEST_DOUBLE(native_cpm_zoom_factor_nebula_v2);
DECLARE_SPDM_ABTEST_DOUBLE(server_show_rate_lower_v2);
DECLARE_SPDM_ABTEST_DOUBLE(server_show_rate_upper_v2);
DECLARE_SPDM_ABTEST_DOUBLE(adload_control_rate_lower_v2);
DECLARE_SPDM_ABTEST_DOUBLE(adload_control_rate_upper_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_adload_control_transfer_fix);
DECLARE_SPDM_ABTEST_BOOL(enable_user_kgame_pay_cnt_data);
DECLARE_SPDM_ABTEST_DOUBLE(adjust_adload_rate_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_adload_rate_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_new_hc_score);
DECLARE_SPDM_ABTEST_DOUBLE(mix_weight_lower_v2);
DECLARE_SPDM_ABTEST_DOUBLE(mix_weight_upper_v2);
DECLARE_SPDM_ABTEST_DOUBLE(server_client_show_rate_soft_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_holdout_unify_server_show_rate);
DECLARE_SPDM_ABTEST_DOUBLE(cid_holdout_unify_server_show_rate);
DECLARE_SPDM_ABTEST_DOUBLE(client_cpm_sctr_ecpc_fix_main);
DECLARE_SPDM_ABTEST_DOUBLE(client_cpm_sctr_ecpc_fix_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_cpm_ratio_v2);

DECLARE_SPDM_ABTEST_BOOL(enable_skip_live_big_r_admit);

DECLARE_SPDM_ABTEST_BOOL(enable_new_jifeibi);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_c_subsidy_admit);
DECLARE_SPDM_ABTEST_BOOL(iaa_game_ad_free_switch);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_ad_free_admit);
DECLARE_SPDM_ABTEST_BOOL(disable_normal_ProtectPrice);
DECLARE_SPDM_ABTEST_BOOL(enable_sctr_ratio_fix);
DECLARE_SPDM_ABTEST_DOUBLE(mix_cpm_to_bonus_factor);
DECLARE_SPDM_ABTEST_DOUBLE(bid_optim_ratio_upper);
DECLARE_SPDM_ABTEST_DOUBLE(bid_optim_ratio_lower);
DECLARE_SPDM_ABTEST_BOOL(enable_request_ad_wise_user_group);
DECLARE_SPDM_ABTEST_BOOL(enable_request_merchant_user_group_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_rta_user_score_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_game_ad_free);
DECLARE_SPDM_ABTEST_BOOL(enable_game_big_r_explore_redis);
DECLARE_SPDM_ABTEST_BOOL(game_big_r_explore_admit);
DECLARE_SPDM_ABTEST_STRING(game_big_r_explore_prefix);
DECLARE_SPDM_ABTEST_BOOL(enable_bid_optim_redis_request);
DECLARE_SPDM_ABTEST_BOOL(enable_bid_optim_param_parse);
DECLARE_SPDM_ABTEST_BOOL(enable_admit_bid_optim_strategy);
DECLARE_SPDM_ABTEST_STRING(ad_wise_user_group_prefix);
DECLARE_SPDM_ABTEST_STRING(merchant_user_group_tag_prefix);
DECLARE_SPDM_ABTEST_STRING(mini_game_c_subsidy_group_tag);
DECLARE_SPDM_ABTEST_STRING(rta_user_score_prefix);
DECLARE_SPDM_ABTEST_STRING(iaa_game_ad_free_prefix);
DECLARE_SPDM_ABTEST_STRING(bid_optim_exp_tag);
DECLARE_SPDM_ABTEST_DOUBLE(game_stability_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(game_stability_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(default_iaa_game_ad_free_ecpm);
DECLARE_SPDM_ABTEST_BOOL(enable_kuai_game_feed_card);
DECLARE_SPDM_ABTEST_BOOL(enable_kuai_game_skip_qpon);
DECLARE_SPDM_ABTEST_BOOL(enable_kuai_game_age_constraint);
DECLARE_SPDM_ABTEST_BOOL(enable_kuai_game_platform_version_constraint);
DECLARE_SPDM_ABTEST_BOOL(enable_kuai_game_whitelist_constraint);
DECLARE_SPDM_ABTEST_BOOL(enable_kuai_game_device_version_constraint);
DECLARE_SPDM_ABTEST_BOOL(enable_kuai_game_isunlogin_constraint);
DECLARE_SPDM_ABTEST_BOOL(enable_game_stability_discount);
DECLARE_SPDM_ABTEST_BOOL(enable_reset_mini_c_subsidy_record);
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_bigcustomer_conf);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_admit_zero_ltv);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_multi_head_judge);
DECLARE_SPDM_ABTEST_STRING(mini_game_subsidy_strategy_tag);
DECLARE_SPDM_ABTEST_STRING(mini_game_seven_subsidy_strategy_tag);
DECLARE_SPDM_ABTEST_INT64(mini_game_redis_period);
DECLARE_SPDM_ABTEST_BOOL(enable_set_mini_game_buyer_style);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_c_subsidy_transfer_admit);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_c_subsidy_admit_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_c_subsidy_transfer_admit);
DECLARE_SPDM_ABTEST_BOOL(enable_offline_fiction_subsidy_uplift_model);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_uplift_subsidy_style);
DECLARE_SPDM_ABTEST_STRING(mini_game_second_subsidy_tag);
DECLARE_SPDM_ABTEST_STRING(mini_game_second_subsidy_group_tag);
DECLARE_SPDM_ABTEST_INT64(mini_game_second_subsidy_style);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_second_subsidy_admit);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_second_subsidy_admit_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_second_subsidy_record);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_fix_amount);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_pv_random_subsidy);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_calculate_real_ltv);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_real_value_alpha);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_real_value_beta);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_real_value_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_real_value_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_pv_random_thrd);
DECLARE_SPDM_ABTEST_INT64(mini_game_smart_subsidy_lower_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_calculate_real_ltv_by_conv_ltv);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_strategy_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_calc_price_v1);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_calc_price_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_reset_mini_game_smart_subsidy_record);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_smart_subsidy_constraint);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_7r_smart_subsidy_constraint);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_subsidy_percentage);
DECLARE_SPDM_ABTEST_INT64(mini_game_smart_subsidy_upper_bound);
DECLARE_SPDM_ABTEST_INT64(mini_game_smart_subsidy_lower_bound_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_subsidy_lower_thrd);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_subsidy_upper_thrd);
DECLARE_SPDM_ABTEST_INT64(mini_game_subsidy_amount_bias);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_with_t0);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_subsidy_percentage_below_real_ltv);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_subsidy_percentage_above_real_ltv);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_constraint);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_subsidy_price_weight);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_subsidy_price_bias);
DECLARE_SPDM_ABTEST_INT64(mini_game_price_upper_constraint);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_price_strategy_v3);
DECLARE_SPDM_ABTEST_BOOL(enable_game_big_r_explore_redis_v2);
DECLARE_SPDM_ABTEST_BOOL(game_big_r_explore_admit_v2);
DECLARE_SPDM_ABTEST_STRING(game_big_r_explore_prefix_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_freq_control);
DECLARE_SPDM_ABTEST_INT64(mini_game_smart_subsidy_remain_freq_count);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_c_subsidy_freq_control);
DECLARE_SPDM_ABTEST_INT64(mini_game_c_subsidy_remain_freq_count);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_front_update);
DECLARE_SPDM_ABTEST_BOOL(enable_game_sdk_add_log);
// [nizhihao end]

// [yangzhao07 start]
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_price_strategy);
DECLARE_SPDM_ABTEST_INT64(mini_game_adjust_subsidy_amount_bias);
DECLARE_SPDM_ABTEST_INT64(mini_game_adjust_subsidy_amount_gap);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_adjust_subsidy_amount_w);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_discount_ratio_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_discount_ratio_upper_bound_v2);
DECLARE_SPDM_ABTEST_INT64(mini_game_smart_subsidy_upper_bound_high);
DECLARE_SPDM_ABTEST_INT64(mini_game_smart_subsidy_lower_bound_high);
DECLARE_SPDM_ABTEST_INT64(mini_game_subsidy_amount_bias_high);
DECLARE_SPDM_ABTEST_INT64(mini_game_7r_subsidy_amount_bias);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_uniform_discount_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_uniform_discount_strategy_v2);
DECLARE_SPDM_ABTEST_INT64(mini_game_real_price_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_smart_subsidy_constraint_high);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost_new);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost_new_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_first_day_subsidy);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost_new_v3);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_subsidy_amount_lower);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_subsidy_amount_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_iaa_with_industry);
DECLARE_SPDM_ABTEST_DOUBLE(iaa_conv_pay_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(7r_iaa_conv_pay_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_roi_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_history_conv_non_csubsidy);  // 历史激活准入 首日
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_adroas_iaap_csubsidy);  // 优化目标准入 首日
DECLARE_SPDM_ABTEST_STRING(mini_game_c_subsidy_group_tag_new);    // 定价 tag 首日
DECLARE_SPDM_ABTEST_STRING(mini_game_seven_c_subsidy_group_tag_new);    // 定价 tag 7 日
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_discount_ratio_low_bound_new);  // 折扣率下限 首日
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_discount_ratio_up_bound_new);  // 折扣率上限 首日
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_roi_subsidy);  // roi 补贴 首日
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_bias_subsidy);  // bias 补贴开关
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_first_bias_subsidy);  // bias 补贴开关
DECLARE_SPDM_ABTEST_BOOL(mini_game_first_bias_subsidy);   // bias 补贴开关 首日
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_csubsidy_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_adroas_sevenday_iaap_csubsidy);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_pay_ltv_dingjia);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_first_pay_ltv_dingjia);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_discount_ratio_dingjia);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_first_discount_ratio_dingjia);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_7r_real_value_alpha);
DECLARE_SPDM_ABTEST_DOUBLE(day_index_discount_ratio_delta);
DECLARE_SPDM_ABTEST_DOUBLE(pay_cnt_discount_ratio_delta);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_random_discount);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_smart_7r_subsidy_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_first_roi_subsidy);
DECLARE_SPDM_ABTEST_BOOL(enable_random_discount_ratio);
// [yangzhao07 end]

DECLARE_SPDM_ABTEST_BOOL(enable_explore_feed_w_user_group);   // [yesiqi] 发现页双列 w 人群 CPM 实验//NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_explore_inner_w_user_group);  // [yesiqi] 发现页双列 w 人群 CPM 实验//NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_min_page_size_admit);  // [yesiqi] 发现页内流最小可请求广告 page_size 准入开关//NOLINT
DECLARE_SPDM_ABTEST_INT64(inner_explore_min_page_size_admit);  // [yesiqi] 发现页内流最小可请求广告 page_size
DECLARE_SPDM_ABTEST_INT64(inner_explore_fixed_page_size_admit);  // [yesiqi] 发现页内流可请求广告 page_size
// [duanxinning] 主站发现页内流接入直播
DECLARE_SPDM_ABTEST_BOOL(enale_inner_explore_live_score);
DECLARE_SPDM_ABTEST_BOOL(enale_inner_explore_live_soft_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_live_extra);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_live_item_as_p2l);
DECLARE_SPDM_ABTEST_STRING(inner_explore_live_score_conf_key);
DECLARE_SPDM_ABTEST_DOUBLE(fountain_mix_ad_fans_cpm_boost_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fountain_mix_auction_top_fans_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fountain_mix_ad_dsp_eco_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fountain_mix_ad_top_fans_eco_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fountain_mix_ad_live_score_alpha);
DECLARE_SPDM_ABTEST_DOUBLE(fountain_mix_ad_live_score_bias);
DECLARE_SPDM_ABTEST_DOUBLE(fountain_mix_ad_live_score_pow);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_mix_live_quota);
DECLARE_SPDM_ABTEST_INT64(inner_explore_live_mix_quota);

DECLARE_SPDM_ABTEST_BOOL(enable_high_ue_explore_force);  // [qiaolin] ue_score 开关
// [duanxinning] 主站发现页内流 rerank
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_fairness_rerank);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_fairness_with_bonus);
DECLARE_SPDM_ABTEST_STRING(inner_explore_fairness_rerank_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_ecpm_rerank);
DECLARE_SPDM_ABTEST_STRING(inner_explore_ecpm_rerank_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_ecpm_rerank_fix);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_rerank_add_dims);

// 发现页内流体验指标
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_mix_experience_value);
DECLARE_SPDM_ABTEST_STRING(explore_mix_experience_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_explore_post_gpm);
DECLARE_SPDM_ABTEST_BOOL(enable_mix_inner_explore_relative_hc);
DECLARE_SPDM_ABTEST_BOOL(skip_inner_explore_ad_gap_adjust);
DECLARE_SPDM_ABTEST_BOOL(skip_feed_explore_fanstop_pk);
DECLARE_SPDM_ABTEST_BOOL(skip_feed_explore_fanstop_pk_fix);
DECLARE_SPDM_ABTEST_BOOL(skip_inner_explore_fanstop_pk);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_mix_ratio_opt);
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_mix_ratio_soft_live);
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_mix_ratio_soft_photo);
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_mix_ratio_hard_live);
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_mix_ratio_hard_photo);
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_inner_explore_new);
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_mix_price_adjust_ratio_lower_bound);

// [jinhui05]
// w 人群 CPM 实验
DECLARE_SPDM_ABTEST_BOOL(enable_side_window_user_group_w_cpm_thr);
// 关注页最大计费
DECLARE_SPDM_ABTEST_INT64(follow_max_price);
DECLARE_SPDM_ABTEST_INT64(follow_fanstop_v2_max_price);
// 关注页欠成本优化黑名单
DECLARE_SPDM_ABTEST_BOOL(disable_follow_cost_ratio_account_black_list);
// [jinhui05] 内流 trigger item 对应的河图标签
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_inner_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_wanhe);
// [jinhui05] 发现页内流打折计费对齐
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_price_explore_inner);
DECLARE_SPDM_ABTEST_DOUBLE(gamora_model_sctr_price_correct_weight);
DECLARE_SPDM_ABTEST_DOUBLE(mix_dsp_live_price_correct_ratio_gamora);
DECLARE_SPDM_ABTEST_DOUBLE(gamora_mix_softad_reprice_weight);
DECLARE_SPDM_ABTEST_DOUBLE(gamora_mix_soft_live_price_correct_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(adjust_price_explore_inner_rel_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(adjust_price_explore_inner_abs_ratio);
// [jinhui05] 主站发现页内流混排特殊处理
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_mix_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_mix_soft_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_mix_hard_ratio);
// [zhaokun03] 内循环聚合大卡二次请求相关信息落 trace 日志
DECLARE_SPDM_ABTEST_BOOL(enable_fill_common_card_second_request);
// [zhaokun03] 发现页混排 quota 优化
DECLARE_SPDM_ABTEST_BOOL(inner_explore_sort_ad_queue_with_mix);
DECLARE_SPDM_ABTEST_BOOL(enable_select_mix_quoat_with_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_mix_quota_opt);
DECLARE_SPDM_ABTEST_INT64(total_quota_for_mix_rank_with_explore);
DECLARE_SPDM_ABTEST_INT64(inner_soft_min_quota);
DECLARE_SPDM_ABTEST_INT64(inner_hard_min_quota);
DECLARE_SPDM_ABTEST_INT64(inner_explore_mix_quota_thr);
DECLARE_SPDM_ABTEST_INT64(mix_live_quota_with_explore);
DECLARE_SPDM_ABTEST_INT64(mix_hard_quota_with_explore);
DECLARE_SPDM_ABTEST_INT64(mix_soft_quota_with_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_feed_set_auto_play);  // [zhaokun03] 发现页双列直播广告设置自动播放

DECLARE_SPDM_ABTEST_BOOL(enable_set_prerank_trigger_relative_score);  // [duanxinning] trigger 相关性得分
DECLARE_SPDM_ABTEST_BOOL(disable_kuaishou_splash_rtb_ads);  // [yangxibo] 开屏屏蔽 rtb 广告实验
DECLARE_SPDM_ABTEST_BOOL(disable_kuaishou_nebula_splash_rtb_ads);  // [yangxibo] 开屏屏蔽品牌广告实验
DECLARE_SPDM_ABTEST_BOOL(enable_guess_like_product_detail_category);
DECLARE_SPDM_ABTEST_BOOL(enable_good_native_photo_new_standard);  //  [shoulifu03]  优质原生素材新标准实验
DECLARE_SPDM_ABTEST_DOUBLE(native_outer_make_up_gpm_factor);  // [zhangmengxin] 软广外循环混排分数 cpm 替换 gpm 系数  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_keep_mix_rank_bid_order);  //  [shoulifu03]  混排报价保序开关
DECLARE_SPDM_ABTEST_BOOL(enable_power_boost_mix_rank_bid);  // [sholifu03] 指数卷混排报价开关
DECLARE_SPDM_ABTEST_DOUBLE(mix_rank_bid_power_ratio_ub);  // [shoulifu03] 混排报价幂指数提价上限
DECLARE_SPDM_ABTEST_BOOL(enable_side_window_author_uplift_score);
DECLARE_SPDM_ABTEST_BOOL(enable_invo_traffic_inner_ranking_list_reids);  //  [zhaokun03] 创新流量参竞信息写入 redis  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_invo_traffic_rank_redis_update);  //  [zhaokun03] 创新流量参竞信息写入 redis 更新  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(invo_traffic_rank_redis_nums);  //  [zhaokun03] 创新流量参竞信息写入 redis 单次数量
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_item_hetu_emb_inner_explore);  //  [zhaokun03] 发现页内流河图 emb 写入
DECLARE_SPDM_ABTEST_STRING(side_window_author_uplift_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_mix_hc_opt);  // [zhaokun03] 发现页内流混排 hc 优化
DECLARE_SPDM_ABTEST_BOOL(enable_watch_video_mission_use_page_size);
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_multi_ad_use_page_size);
DECLARE_SPDM_ABTEST_DOUBLE(wanhe_hard_pk_ratio);  // [zhaokun03] 万合软硬广 pk 策略中硬广系数
DECLARE_SPDM_ABTEST_INT64(wanhe_max_price);  // [zhaokun03] 万合最大计费
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_charge_action_type_with_fanstop);  // [zhaokun03] 万合粉条广告切曝光计费
DECLARE_SPDM_ABTEST_BOOL(enable_fix_splash_purchase_info);  // [zhaokun03] 修复开屏信息填充逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_ud_duanju_user_force_info);  // [zhaokun03] 短剧强出人群泛题材信息接入
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_mix_cpm_opt);  // [zhaokun03] 发现页内流混排 cpm 优化
DECLARE_SPDM_ABTEST_STRING(inner_explore_mix_cpm_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_exposed_qcpx_has_qpon);  // [liqinglong] 端上白盒加 qcpx 信息
DECLARE_SPDM_ABTEST_STRING(extra_timeout_tag);  // [liqinglong] 引擎超时实验 tag
DECLARE_SPDM_ABTEST_BOOL(enable_cid_action_bar_track);  // [liqinglong] 修复 cid trace type

DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_shelf_auction_operator);  // [yanqi08] qcpx 货架

DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_charge_action_type);  // [luwei] 猜喜切曝光计费
DECLARE_SPDM_ABTEST_DOUBLE(bh_item_card_price_discount_ratio);  // [zengjiangwei03] 货架统一计费打折-买首商品卡  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(mall_item_card_price_discount_ratio);  // [zengjiangwei03] 货架统一计费打折-商城商品卡  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(zq_item_card_price_discount_ratio);  // [yuchengyuan] 货架统一计费打折-赚钱商品卡
DECLARE_SPDM_ABTEST_DOUBLE(bh_price_discount_ratio);  // [zengjiangwei03] 货架统一计费打折-买首
DECLARE_SPDM_ABTEST_DOUBLE(mall_price_discount_ratio);  // [zengjiangwei03] 货架统一计费打折-商城
DECLARE_SPDM_ABTEST_DOUBLE(zq_price_discount_ratio);  // [yuchengyuan] 货架统一计费打折-赚钱
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_ranking_info);  // [zengjiangwei03] 电商精排信息接入
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_speed_test);  // [zengjiangwei03] 商品卡测品
DECLARE_SPDM_ABTEST_INT64(item_card_speed_test_mix_rank_ratio);  // [zengjiangwei03] 商品卡测品混排权重  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_buyer_home_page_mix_rank);  // [zengjiangwei03] 买首生效混排分权重
DECLARE_SPDM_ABTEST_INT64(buyer_home_page_mix_rank_live_card_weight);  // [zengjiangwei03] 买首直播卡混排分权重 // NOLINT
DECLARE_SPDM_ABTEST_INT64(buyer_home_page_mix_rank_item_card_weight);  // [zengjiangwei03] 买首商品卡混排分权重 // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(buyer_home_page_mix_rank_live_card_weight_new);  // [zengjiangwei03] 买首直播卡混排分权重 // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(buyer_home_page_mix_rank_item_card_weight_new);  // [zengjiangwei03] 买首商品卡混排分权重 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_request_eds);  // [liqikun] 关掉请求 eds 特征
DECLARE_SPDM_ABTEST_BOOL(enable_request_merchant_good_show_item);  // [zengjiangwei03] 请求 colossus table
DECLARE_SPDM_ABTEST_INT64(merchant_good_show_item_list_size);  // [zengjiangwei03] 请求 colossus table 序列长度  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_request_good_search_item);  // [zengjiangwei03] 请求 colossus table
DECLARE_SPDM_ABTEST_INT64(good_search_item_list_size);  // [zengjiangwei03] 请求 colossus table 序列长度
DECLARE_SPDM_ABTEST_BOOL(enable_set_bid_to_mix_info);

// [chenziping]
DECLARE_SPDM_ABTEST_BOOL(enable_calc_impress_pos_mix_benefit);
DECLARE_SPDM_ABTEST_BOOL(enable_impress_pos_mixbenefit_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_impress_pos_predict_cpm);

// [xiaoyuhao]
DECLARE_SPDM_ABTEST_BOOL(enable_front_request_rank_select_one_more);
DECLARE_SPDM_KCONF_BOOL(enableFrontRequestRankSelectOneMore);
DECLARE_SPDM_KCONF_INT64(requestRankSelectOneTimes);
DECLARE_SPDM_KCONF_BOOL(enableSetAdxThirdConvertInfo);
DECLARE_SPDM_ABTEST_BOOL(enable_front_skip_ps_prepare);
DECLARE_SPDM_KCONF_BOOL(enableMerchantAutoDeliverType);
DECLARE_SPDM_ABTEST_BOOL(enable_get_router_user_info);
DECLARE_SPDM_ABTEST_BOOL(enable_send_user_feature_to_adserver);
DECLARE_SPDM_KCONF_BOOL(enableGetRouterUserInfo);
DECLARE_SPDM_KCONF_BOOL(enableSendUserFeatureToAdServer);
DECLARE_SPDM_KCONF_BOOL(enableSendUserFeatureOnlyBs);
DECLARE_SPDM_ABTEST_BOOL(enable_guess_like_change_interactive_form);
DECLARE_SPDM_KCONF_BOOL(enableAlwaysLogSetRerankReqInfo);
DECLARE_SPDM_ABTEST_BOOL(enable_unify_grid_unit_id);
DECLARE_SPDM_ABTEST_BOOL(enable_request_ad_match_server);
DECLARE_SPDM_ABTEST_INT64(nearline_recall_front_processor_flag);
DECLARE_SPDM_KCONF_INT64(orientationTruncateSize);
DECLARE_SPDM_ABTEST_BOOL(enable_user_orientation_truncate);
DECLARE_SPDM_KCONF_BOOL(enableUserOrientationTruncate);
DECLARE_SPDM_ABTEST_STRING(user_orientation_truncate_exp_group);
DECLARE_SPDM_KCONF_BOOL(enableGetRecoUserInfoFromClotho);
DECLARE_SPDM_ABTEST_BOOL(enable_get_reco_user_info_from_clotho);
DECLARE_SPDM_KCONF_BOOL(enableRecoUserInfoCmpDiff);
DECLARE_SPDM_ABTEST_BOOL(disable_inspire_get_reco_user_info);
DECLARE_SPDM_KCONF_BOOL(enableFrontTrafficCopy);
DECLARE_SPDM_ABTEST_BOOL(enable_front_traffic_copy);
DECLARE_SPDM_KCONF_BOOL(enableFrontTrafficCopyCache);
DECLARE_SPDM_KCONF_BOOL(enableSetIaaAdPlayType);

// [zhaokun03] 万合最大计费
DECLARE_SPDM_ABTEST_INT64(wanhe_fanstop_v2_max_price);
DECLARE_SPDM_ABTEST_STRING(mix_rank_bid_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_keep_mix_rank_bid_order_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_pred_target_cost);  // [wangyang10] pred_target_cost 落表  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_imp_info_check_size);  // [qianyangchao] 获取 imp_info 的时候检查大小
DECLARE_SPDM_ABTEST_BOOL(enable_mix_bid_user_info_get_by_did);  // [shoulifu03] 使用 did 获取用户信息
DECLARE_SPDM_ABTEST_BOOL(enable_high_mix_bid_ad_discount);  //  [shoulifu03] 高报价广告混排报价打折开关
DECLARE_SPDM_ABTEST_STRING(high_mix_bid_ad_discount_exp_tag);  //  [shoulifu03] 高报价广告混排报价打折实验配置
DECLARE_SPDM_ABTEST_BOOL(enable_search_query_norm);  // [qianyangchao] 对 search_query 进行归一化
DECLARE_SPDM_ABTEST_BOOL(enable_mix_unify_gpm_pass);   // [xiaoyuhao] 允许透传混排 gpm 字段
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_gpm_ratio);  // [xiaoyuhao] 混排 gpm ratio 字段
DECLARE_SPDM_ABTEST_BOOL(small_game_ad_force_direct_call_switch);  //  微信小游戏直跳/二跳策略实验参数  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_mix_bid_discount_exp_new_strategy);  //  [shoulifu03] 混排报价打折实验新策略
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_style_form);   // [xiaoyuhao] 表单激励开关
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant);   // [xiaoyuhao] 货架电商流量拆分
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_mall);   // [xiaoyuhao] 货架电商流量拆分-商城
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_buyer);   // [xiaoyuhao] 货架电商流量拆分-买首
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_guess);   // [xiaoyuhao] 货架电商流量拆分-猜喜
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_zhuanqian);   // [yuchengyuan] 货架电商流量拆分-赚钱
DECLARE_SPDM_ABTEST_BOOL(enable_native_gsp_price);  //  [dingyiming05] 软广二价计费
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_gsp);  //  [dingyiming05] 粉条二价计费
DECLARE_SPDM_ABTEST_BOOL(enable_hard_gsp_price);  //  [dingyiming05] 硬广二价开关
DECLARE_SPDM_ABTEST_DOUBLE(price_protect_lower_bound);  //  [dingyiming05] 一价保护下界
DECLARE_SPDM_ABTEST_DOUBLE(price_ratio_lower_v2);  //  [dingyiming05] RevenueOptimize 一价保护下界
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower);  // [dingyiming05] 计费比下界
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper);  // [dingyiming05] 计费比上界
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower);  // [dingyiming05] 软广计费比下界
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper);  // [dingyiming05] 软广计费比上界
DECLARE_SPDM_ABTEST_BOOL(enable_request_user_imp_opt);  // [dingyiming05] UserImpOpt redis 请求开关
DECLARE_SPDM_ABTEST_BOOL(enable_mix_bonus_adjust);  // [dingyiming05] unify_bonus 调控开关
DECLARE_SPDM_ABTEST_DOUBLE(mix_bonus_adjust_enhance_ratio);  // [dingyiming05] unify_bonus 实验调控系数
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_score_exp_ratio);  // [dingyiming05] unify_score 实验调控系数
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_score_exp_ratio_a);  // [dingyiming05] unify_score 实验调控系数 a
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_score_exp_ratio_b);  // [dingyiming05] unify_score 实验调控系数 b
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_cpm_exp_ratio);  // [dingyiming05] unify_cpm 实验调控系数
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_cpm_ratio_inner);  // [dingyiming05] unify_cpm 内循环实验调控系数
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_cpm_ratio_outer);  // [dingyiming05] unify_cpm 外循环实验调控系数
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_gpm_exp_ratio);  // [dingyiming05] unify_gpm 实验调控系数
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_bonus_exp_ratio);  // [dingyiming05] unify_bonus 实验调控系数
DECLARE_SPDM_ABTEST_BOOL(disable_adload_control_rate);  // [dingyiming05] adload 调控系数重置开关
// [dingyiming05] 广告整体屏蔽实验
DECLARE_SPDM_ABTEST_BOOL(enable_ad_block_filter);
DECLARE_SPDM_ABTEST_INT64(ad_block_pp);
// [shoulif03] 新混排去除硬广精排 sctr 系数
DECLARE_SPDM_ABTEST_DOUBLE(client_cpm_sctr_ratio_main);
DECLARE_SPDM_ABTEST_DOUBLE(client_cpm_sctr_ratio_nebula);
DECLARE_SPDM_ABTEST_BOOL(enable_search_dup_between_inner_stream);  // [yangxibo] 搜索同一外流的内流间去重
DECLARE_SPDM_ABTEST_BOOL(disable_search_ad_mark_by_review_status);  // [zhangzhicong] 搜索广告去标优化
DECLARE_SPDM_ABTEST_BOOL(enable_splash_in_fold_phone);  // [zhangzhicong] 开屏折叠屏放开开关
DECLARE_SPDM_ABTEST_BOOL(enable_brand_inner_explore);  // [zhangzhicong] 品牌广告接入信息流内流开关
DECLARE_SPDM_ABTEST_BOOL(enable_search_innerloop_userdata);  // [zhangzhicong] 搜索广告请求 innerloop 用户信息
DECLARE_SPDM_ABTEST_BOOL(enable_phone_material_fill);  // [shanminghui] 是否开启厂商素材补齐
DECLARE_SPDM_ABTEST_BOOL(enable_kuaishou_deep_inspire);   // [xiaoyuhao] 主站深度激励开关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_native_into_follow);  // [xiaoyuhao] 关注页接入外循环软广开关
DECLARE_SPDM_ABTEST_BOOL(fix_relative_photo_id_explore);  // [jinhui05] front 透传 relative_photo_id
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_charge_action_list);  // [xiaoyuhao] 磁力万合构造 charge_action_list
DECLARE_SPDM_ABTEST_BOOL(enable_profile_relation_link_ad);  // [xiaoyuhao] 磁力万和 P 页关系链广告准入
DECLARE_SPDM_ABTEST_INT64(mall_tab_multi_return_num);  // [xiaoyuhao] 商城 tab 广告位返回个数
DECLARE_SPDM_ABTEST_INT64(zhuanqian_tab_multi_return_num);  // [yuchengyuan] 赚钱 tab 广告位返回个数
DECLARE_SPDM_ABTEST_BOOL(enable_photo_id_string_to_int);  // [xiaoyuhao] 透传 relative photo id 兼容 p 页内流
DECLARE_SPDM_ABTEST_BOOL(enable_follow_live_inner_ad_kuaishou);   // [xiaoyuhao] 主版关注页直播内流广告准入
DECLARE_SPDM_ABTEST_BOOL(enable_follow_live_inner_ad_nebula);    // [xiaoyuhao] 极速版关注页直播内流广告准入
DECLARE_SPDM_ABTEST_BOOL(enable_pass_is_kol_ad);      // [xiaoyuhao] 透传 a 投 b 场景判别字段
DECLARE_SPDM_ABTEST_BOOL(enable_nebula_novel_ad);      // [xiaoyuhao] 允许极速版小说出广告
DECLARE_SPDM_ABTEST_BOOL(enable_kuaishuo_novel_ad);      // [xiaoyuhao] 允许主版小说出广告
DECLARE_SPDM_ABTEST_BOOL(enable_video_income_task_ad);      // [xiaoyuhao] 允许看视频提收广告位出广告
DECLARE_SPDM_ABTEST_BOOL(enable_draw_flow_ad);      // [xiaoyuhao] 允许 draw 流广告位出广告
DECLARE_SPDM_ABTEST_BOOL(enable_follow_holdout_fix);      // [xiaoyuhao] 关注页 holdout 兜底修复
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_req_inner_loop);      // [xiaoyuhao] 喜番准入内循环广告
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_inspire_req_inner_loop);      // [yushengkai] 喜番激励准入内循环广告
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_feed_req_inner_loop);      // [yushengkai] 喜番信息流准入内循环广告
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_inspire_inner_loop_version_control);  // [yushengkai] 喜番激励内循环版控
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_inner_loop_sort);  // [yushengkai] 喜番内循环软硬广排序
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_uplift_strategy);  // [yushengkai] iaa 变现端个性化策略
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_transport);  // [yushengkai] iaa 透传信息
DECLARE_SPDM_ABTEST_INT64(default_iaa_bill_second);  // [yushengkai] 个性化时长默认值
DECLARE_SPDM_ABTEST_STRING(defualt_iaa_bill_second_by_episode);  // [yushengkai] 个性化时长默认值
DECLARE_SPDM_ABTEST_STRING(iaa_uplift_exp_tag);  // [yushengkai] iaa 变现端策略 tag
DECLARE_SPDM_ABTEST_BOOL(enable_no_tag_ad_standard_switch);  //  [shoulifu03]  优质软广标准切换
DECLARE_SPDM_ABTEST_BOOL(enable_mtb_unify_gpm_cali);  // [shoulifu03] 混排 gpm 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_add_multi_head_mix_gpm_live);  // [wuyinhao] 混排 gpm 使用多头模型预测结果开关
DECLARE_SPDM_ABTEST_BOOL(disable_multi_head_mix_gpm_outer);  // [wangwenguang] 混排 gpm 外循环不使用多头模型

// [wuyinhao] 混排 gpm 使用多头模型预测结果开关
DECLARE_SPDM_ABTEST_BOOL(enable_add_multi_head_mix_gpm_photo);
DECLARE_SPDM_ABTEST_BOOL(enable_mix_unify_gpm_min_value);  // [wangwenguang] 是否限定透传给混排的 unify_gpm
DECLARE_SPDM_ABTEST_DOUBLE(mix_unify_gpm_min_value);  // [wangwenguang] 限定透传给混排的 unify_gpm 的最小值
DECLARE_SPDM_ABTEST_STRING(mtb_unify_gpm_cali_tag);  // [shoulifu03] 混排 gpm 校准配置 tag
DECLARE_SPDM_ABTEST_BOOL(enable_record_search_budget_in_universe);  // [shanminghui] 是否记录联盟暗投搜索预算
DECLARE_SPDM_ABTEST_BOOL(enable_record_search_budget_in_universe_v2);  // [tanghaihong] 是否记录联盟暗投搜索预算 v2 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_quick_search_skip_budget_control_universe);  // [tanghaihong] 联盟小系统快投广告跳过暗投控比 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_direct_search_skip_budget_control_universe);  // [tanghaihong] 联盟小系统直投广告跳过暗投控比 // NOLINT

// [zhangruikang] 用户宽表数据
DECLARE_SPDM_ABTEST_BOOL(user_data_center_history_gmv);
DECLARE_SPDM_ABTEST_BOOL(user_data_colossus_order_leaf_category);
DECLARE_SPDM_ABTEST_BOOL(user_data_center_playlet_offer_info);
DECLARE_SPDM_ABTEST_BOOL(user_data_center_bigr_tag);    // [chenchen13] 从用户数据中心获取 bigr_tag;
DECLARE_SPDM_ABTEST_BOOL(enable_merge_fanstop_session_info);  // [zhangruikang] 曝光频控合并
DECLARE_SPDM_ABTEST_BOOL(enable_merge_virtual_session_info);  // [zhangruikang] 虚拟下发曝光频控合并
DECLARE_SPDM_ABTEST_BOOL(enable_merge_fanstop_browsed_info);  // [zhangruikang] 下发频控合并
DECLARE_SPDM_ABTEST_INT64(colossus_auto_live_item_list_size);  // [zhangruikang] auto_live_item 截断长度
DECLARE_SPDM_ABTEST_BOOL(enable_use_clotho_data);

//  [shoulifu03] mtb2.1 补贴系数
DECLARE_SPDM_ABTEST_BOOL(enable_mtb_v2_bonus_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(mtb_v2_bonus_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_mtb_v2_hc_gpm_using_max);
DECLARE_SPDM_ABTEST_BOOL(enable_mtb_v2_inner_bonus_include_hc_gpm);
DECLARE_SPDM_ABTEST_BOOL(enable_fill_unify_bonus_info_inner_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_replace_mix_bonus_with_cpm_inner_explore);
DECLARE_SPDM_ABTEST_DOUBLE(replace_mix_bonus_with_cpm_ratio_inner_explore);
// [shoulifu03] 高价值用户曝光系数上限
DECLARE_SPDM_ABTEST_BOOL(enable_core_user_server_show_ratio_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(core_user_server_show_ratio_upper_bound);

// [zengjiangwei03]
DECLARE_SPDM_ABTEST_BOOL(enable_bh_u4_mix_benefit_discount);
DECLARE_SPDM_ABTEST_DOUBLE(bh_u4_mix_benefit_discount_ratio_item);
DECLARE_SPDM_ABTEST_DOUBLE(bh_u4_mix_benefit_discount_ratio_live);
DECLARE_SPDM_ABTEST_BOOL(disable_gyl_item_card);
DECLARE_SPDM_ABTEST_BOOL(disable_mall_item_card);
DECLARE_SPDM_ABTEST_BOOL(disable_zq_item_card);
DECLARE_SPDM_ABTEST_BOOL(disable_bh_item_card);
DECLARE_SPDM_ABTEST_BOOL(disable_gyl_live_card);
DECLARE_SPDM_ABTEST_BOOL(disable_mall_live_card);
DECLARE_SPDM_ABTEST_BOOL(disable_bh_live_card);
DECLARE_SPDM_ABTEST_BOOL(disable_zq_live_card);
DECLARE_SPDM_ABTEST_BOOL(enable_increase_mall_mix_rank_quota);
DECLARE_SPDM_ABTEST_INT64(mall_mix_rank_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_increase_bh_mix_rank_quota);
DECLARE_SPDM_ABTEST_INT64(bh_mix_rank_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_increase_gl_mix_rank_quota);
DECLARE_SPDM_ABTEST_INT64(gl_mix_rank_quota);

// [chenxian] 短剧 C 补配置
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_chaoxiaoe_strategy);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_chaoxiaoe_stop_cbu_panel_prices);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_chaoxiaoe_stop_cbu_lesson_prices);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_chaoxiaoe_fix_init);
DECLARE_SPDM_ABTEST_STRING(playlet_cbu_exp_tag);
DECLARE_SPDM_ABTEST_STRING(playlet_chaoxiaoe_panel_info);

DECLARE_SPDM_ABTEST_BOOL(enable_set_playlet_series_info_v2);

DECLARE_SPDM_ABTEST_BOOL(enable_switch_nc_user_tag);
// 漫剧
DECLARE_SPDM_ABTEST_BOOL(enable_switch_cartoon_user_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_trans_cartoon_user_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_cartoon_cbu_bonus);
DECLARE_SPDM_ABTEST_BOOL(enable_switch_cartoon_dnc_independent_smart_offer);
// [chenxian] 番茄策略
DECLARE_SPDM_ABTEST_BOOL(enable_tomato_playlet_skip_cbu);
DECLARE_SPDM_ABTEST_BOOL(enable_tomato_playlet_no_cbu_bonus);
// [chenxian] 鲜花绿叶
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_smart_offer_green_leaf_panel);
DECLARE_SPDM_ABTEST_BOOL(enable_green_leaf_panel_skip_admit);
DECLARE_SPDM_ABTEST_BOOL(enable_green_leaf_panel_differentiate_coins);
DECLARE_SPDM_ABTEST_STRING(playlet_smart_offer_dnc_green_leaf_panel_info);
DECLARE_SPDM_ABTEST_STRING(playlet_smart_offer_doc_green_leaf_panel_info);
DECLARE_SPDM_ABTEST_INT64(playlet_smart_offer_green_leaf_price_coefficient);
DECLARE_SPDM_ABTEST_STRING(playlet_smart_offer_coins_green_leaf_panel_info);
DECLARE_SPDM_ABTEST_BOOL(enable_green_leaf_panel_select_by_ltv);
DECLARE_SPDM_ABTEST_BOOL(enable_green_leaf_panel_switch_industry_pay_ltv);
DECLARE_SPDM_ABTEST_DOUBLE(green_leaf_panel_adjust_cbu_bonus);
DECLARE_SPDM_ABTEST_STRING(playlet_smart_offer_ltv_green_leaf_panel_info);
DECLARE_SPDM_ABTEST_STRING(playlet_smart_offer_ltv_green_leaf_panel_info_for_coin);
DECLARE_SPDM_ABTEST_STRING(playlet_smart_offer_ltv_green_leaf_adjust_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_green_leaf_panel_adjust_customer_panel);
DECLARE_SPDM_ABTEST_BOOL(enable_all_panel_adjust_customer_panel);
// [chenxian] 多面板优选
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_with_cbu);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_switch);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_all_mode);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_default);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_random);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_by_ltv);
DECLARE_SPDM_ABTEST_STRING(playlet_multi_panel_ltv_range_info);
DECLARE_SPDM_ABTEST_STRING(playlet_multi_panel_ltv_range_info_judou);
DECLARE_SPDM_ABTEST_INT64(enable_playlet_multi_panel_min_template_id);
// [chenxian] c 补 模型配置
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_two_head_uplift_model_front);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_skip_all_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_model_front);
DECLARE_SPDM_ABTEST_BOOL(enable_normal_smart_offer_multi_uplift_model_front);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_dnc_default);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_dnc_no_multi_cbu);
DECLARE_SPDM_ABTEST_DOUBLE(multi_uplift_model_cbu_value_dnc_bonus);
DECLARE_SPDM_ABTEST_DOUBLE(multi_uplift_model_cbu_value_doc_bonus);
DECLARE_SPDM_ABTEST_DOUBLE(unify_multi_uplift_model_cbu_value_bonus);
// [chenxian] 漫剧新用户策略
DECLARE_SPDM_ABTEST_BOOL(enable_cartoon_product_name_kconf_switch);
DECLARE_SPDM_ABTEST_BOOL(enable_cartoon_dnc_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_cartoon_dnc_offer_sub_one);
DECLARE_SPDM_ABTEST_DOUBLE(cartoon_dnc_max_offer_result);
DECLARE_SPDM_ABTEST_BOOL(enable_cartoon_offer_result_plus_one_strategy);
DECLARE_SPDM_ABTEST_DOUBLE(cartoon_offer_result_plus_num);

// [yangxuan06] 短剧 C 补配置
DECLARE_SPDM_ABTEST_STRING(smart_offer_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_exp_tag_switch);
DECLARE_SPDM_ABTEST_STRING(smart_offer_exp_tag_for_two_head);
DECLARE_SPDM_ABTEST_STRING(smart_offer_exp_tag_for_multi_head);
DECLARE_SPDM_ABTEST_STRING(normal_smart_offer_exp_tag);
DECLARE_SPDM_ABTEST_STRING(C_activity);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_lower_bound_pay);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_lower_bound_offer);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_upper_bound_pay);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_upper_bound_offer);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_default_roi);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_min_pay_amount);
DECLARE_SPDM_ABTEST_INT64(smart_offer_lessons_adjust);
DECLARE_SPDM_ABTEST_INT64(smart_offer_coins_adjust);
DECLARE_SPDM_ABTEST_INT64(retention_discount);
DECLARE_SPDM_ABTEST_INT64(retention_subsidy);
DECLARE_SPDM_ABTEST_INT64(pay_threshold);
DECLARE_SPDM_ABTEST_INT64(stay_retention_discount);
DECLARE_SPDM_ABTEST_INT64(stay_retention_subsidy);
DECLARE_SPDM_ABTEST_INT64(stay_pay_threshold);
DECLARE_SPDM_ABTEST_INT64(watch_task_checkpiont);
DECLARE_SPDM_ABTEST_INT64(watch_task_ratio_lower_bound);
DECLARE_SPDM_ABTEST_INT64(watch_task_ratio_upper_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_watch_task_price);
DECLARE_SPDM_ABTEST_BOOL(enable_watch_task);
DECLARE_SPDM_ABTEST_BOOL(C_all_subsidy);
DECLARE_SPDM_ABTEST_BOOL(Paid_retention);
DECLARE_SPDM_ABTEST_BOOL(Stay_retention);
DECLARE_SPDM_ABTEST_BOOL(enable_second_price_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_early_return_offers);
DECLARE_SPDM_ABTEST_BOOL(C_second_price);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_unify_ltv_value);
DECLARE_SPDM_ABTEST_BOOL(enable_nearby_ad_by_req_type);  // [liubing05] 极速版同城内流广告位分发开关
DECLARE_SPDM_ABTEST_BOOL(enable_buyer_home_request_photo);  // [xiaoyuhao] 商城 tab 请求 live-target
DECLARE_SPDM_ABTEST_BOOL(enable_mall_tab_request_live);  // [xiaoyuhao] 商城 tab 请求 live-target
DECLARE_SPDM_ABTEST_BOOL(enable_mall_tab_request_live_v2);  // [zengjiangwei03] 商城 tab 请求 live-target 新参数 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_mix_rank_req);  // [xiaoyuhao] 猜喜电商混排请求 adpack
DECLARE_SPDM_ABTEST_BOOL(enable_guess_like_mix_rank_req_subpage);  // [xiaoyuhao] 猜喜电商混排请求 adpack (区分页面) // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_live_card);  // [xiaoyuhao] 猜喜出直播卡
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_live_card_product);  // [xiaoyuhao] 猜喜请求 live-target 商详
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_live_card_other);  // [xiaoyuhao] 猜喜请求 live-target 非商详
DECLARE_SPDM_ABTEST_BOOL(enable_wechat_customer_acq_direct_jump);  // [xiaoyuhao] 企微获客助手直跳开关
DECLARE_SPDM_ABTEST_BOOL(disable_industry_playlet_sdpa_p2p);  // [xiaoyuhao] 禁用 industry_playlet_sdpa p2p
DECLARE_SPDM_ABTEST_BOOL(enable_clear_playlet_sdpa_info);  // [xiaoyuhao] 清空 playlet_sdpa 相关字段
DECLARE_SPDM_ABTEST_BOOL(enable_use_new_merchant_gpm);  // [yuchengyuan]  货架场景使用新的 egpm
// [shoulifu03] 广告返回 quota 新参数
DECLARE_SPDM_ABTEST_BOOL(enbale_use_new_total_quota);
DECLARE_SPDM_ABTEST_INT64(max_hard_quota_new);
DECLARE_SPDM_ABTEST_INT64(max_soft_quota_new);
DECLARE_SPDM_ABTEST_BOOL(enable_commerical_independ_rank_gamora);
DECLARE_SPDM_ABTEST_BOOL(enable_commerical_independ_rank_nebula);

// [sunsong] w5 人群 3% 额外 bonus 策略新参数
DECLARE_SPDM_ABTEST_DOUBLE(w5_extra_3_percent_bonus_cpm_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(w5_extra_3_percent_bonus_cpm_upper_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_w5_add_3_percent_bonus);
DECLARE_SPDM_ABTEST_BOOL(enable_w5_add_3_percent_bonus_max);
// [wuyinhao] 是否关闭 unify_cpm 上的一些其它变化
DECLARE_SPDM_ABTEST_BOOL(enable_close_unify_cpm_add_customer_hc);
DECLARE_SPDM_ABTEST_BOOL(enable_close_unify_cpm_divide_sctr_ratio);

// [wuyinhao] 根据混排上一刷结果调整 bonus
DECLARE_SPDM_ABTEST_BOOL(enable_move_mix_rank_input_hist_page_info);
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_bonus_with_mix_rank_input_use_two_switch);
DECLARE_SPDM_ABTEST_DOUBLE(bonus_weight_for_rb_thr_to_add_bonus);
DECLARE_SPDM_ABTEST_DOUBLE(adjust_bonus_with_mix_rank_input_weight);
DECLARE_SPDM_ABTEST_DOUBLE(minus_bonus_with_mix_rank_input_weight);
// [wuyinhao] 优质软广豁免客户端过滤
DECLARE_SPDM_ABTEST_BOOL(enable_all_ad_retain_top);

// [sunsong] 混排 fctr 模型
DECLARE_SPDM_ABTEST_DOUBLE(mix_model_roi_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(mix_fctr_roi_bonus_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(mix_fctr_model_bonus_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(mix_fctr_model_bonus_bias);
DECLARE_SPDM_ABTEST_BOOL(enable_set_negative_bonus_to_zero);
DECLARE_SPDM_ABTEST_BOOL(enable_fctr_model_strategy_new);
DECLARE_SPDM_ABTEST_BOOL(enable_fctr_score_strategy_new);
DECLARE_SPDM_ABTEST_STRING(mix_fctr_model_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_fctr_model_strategy_new_v5);
DECLARE_SPDM_ABTEST_BOOL(enable_fctr_score_new_formula);
DECLARE_SPDM_ABTEST_STRING(fctr_model_score_formula_conf);
DECLARE_SPDM_ABTEST_BOOL(disable_fctr_live_bonus);
DECLARE_SPDM_ABTEST_BOOL(enable_new_fctr_model_cmd_config);
DECLARE_SPDM_ABTEST_STRING(ad_fctr_model_inner_cmd);
DECLARE_SPDM_ABTEST_STRING(ad_fctr_model_outer_cmd);
DECLARE_SPDM_ABTEST_STRING(ad_fctr_model_inner_cmd_key);
DECLARE_SPDM_ABTEST_STRING(ad_fctr_model_outer_cmd_key);
DECLARE_SPDM_ABTEST_BOOL(enable_pid_fctr_threshold_bgtask);
DECLARE_SPDM_ABTEST_BOOL(enable_mix_fctr);
DECLARE_SPDM_ABTEST_BOOL(enable_fctr_isotonic_adjust_with_rb);
DECLARE_SPDM_ABTEST_BOOL(enable_fctr_max_cpm_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_fctr_mix_rb_cpm_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_bonus_strategy_for_mix_rb_cpm);
DECLARE_SPDM_ABTEST_DOUBLE(ori_bonus_ratio_for_mix_rb_cpm);
DECLARE_SPDM_ABTEST_DOUBLE(new_bonus_ratio_for_mix_rb_cpm);
DECLARE_SPDM_ABTEST_BOOL(disable_fctr_fanstop_bonus);
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_use_fctr_bonus);
DECLARE_SPDM_ABTEST_BOOL(enable_change_fctr_bonus_for_mix_rb_cpm);
DECLARE_SPDM_ABTEST_BOOL(enable_new_fctr_model);

// [wangzixu05] 发现页内流 混排 fctr 模型
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_roi_threshold);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_mix_fctr);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_fctr_sort_by_cpm_fctr_bonus);
DECLARE_SPDM_ABTEST_STRING(inner_explore_ad_fctr_model_inner_cmd);
DECLARE_SPDM_ABTEST_STRING(inner_explore_ad_fctr_model_outer_cmd);
DECLARE_SPDM_ABTEST_STRING(inner_explore_ad_fctr_model_inner_cmd_key);
DECLARE_SPDM_ABTEST_STRING(inner_explore_ad_fctr_model_outer_cmd_key);
DECLARE_SPDM_ABTEST_STRING(inner_explore_fctr_exp_tag);

// [jiangyuzhen03]
DECLARE_SPDM_KCONF_BOOL(disableChargeInNovelRecommend);  // 小说在投书籍接流
// [jiangyuzhen03] end

// [sunsong] 认知实验跳过 fctr 模型
DECLARE_SPDM_ABTEST_BOOL(enable_skip_fctr_for_exp);

DECLARE_SPDM_ABTEST_BOOL(enable_only_insert_high_ue_in_short_seq_front);
DECLARE_SPDM_ABTEST_INT64(high_ue_short_seq_len_front);
DECLARE_SPDM_ABTEST_BOOL(enable_only_insert_high_ue_in_short_seq_front_v2);
DECLARE_SPDM_ABTEST_INT64(high_ue_short_seq_len_front_v2);

// [sunsong] 混排软广 hc 和 bonus 修复
DECLARE_SPDM_ABTEST_BOOL(enable_fix_mix_bonus);
DECLARE_SPDM_ABTEST_DOUBLE(fix_mix_bonus_hc_ratio);

// [wangwenguang] 混排 unify_cpm 修复
DECLARE_SPDM_ABTEST_BOOL(enable_fix_mix_unify_cpm_init);
DECLARE_SPDM_ABTEST_BOOL(enable_mix_rank_truncate);
DECLARE_SPDM_ABTEST_INT64(mix_rank_truncate_max);

// [wangwenguang] 端智能重请求新增埋点
DECLARE_SPDM_ABTEST_BOOL(enable_add_rerank_req_info);
DECLARE_SPDM_ABTEST_BOOL(enable_add_rerank_req_info_live_use_photo);
DECLARE_SPDM_ABTEST_BOOL(enable_control_prerank);
DECLARE_SPDM_ABTEST_BOOL(enable_calc_client_ai_rerank_score_adrank);
DECLARE_SPDM_ABTEST_BOOL(enable_live_rerank_retrival_outer);

// [lihongji03] 重请求流量 tag 埋点
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_rerank_req_info_tag);
DECLARE_SPDM_ABTEST_STRING(mix_mtb_bid_frac_ad_cpm_gamora);
DECLARE_SPDM_ABTEST_STRING(mix_mtb_bid_frac_ad_cpm_nebula);

// [lihongji03] 外循环非外跳透传
DECLARE_SPDM_ABTEST_BOOL(enable_excycle_skip_pass_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_excycle_skip_pass_tag_v2);

// [sunsong] 计费比系数
DECLARE_SPDM_ABTEST_BOOL(enable_jifeibi_adjust);
DECLARE_SPDM_ABTEST_DOUBLE(mix_jifeibi_coef);

// [sunsong] unify_bonus 实验
DECLARE_SPDM_ABTEST_BOOL(enable_unify_bonus_exp_v2);
DECLARE_SPDM_ABTEST_STRING(unify_bonus_exp_tag);

// [zhaozuodong] bonus 认知实验， bonus 置为 cpm 固定比例
DECLARE_SPDM_ABTEST_BOOL(enable_bonus_fixed_ratio_experiment_v2);
DECLARE_SPDM_ABTEST_DOUBLE(bonus_fixed_ratio_v2);

// [sunsong] 混排 context 特征开关
DECLARE_SPDM_ABTEST_BOOL(enable_mix_candidate_ad_map);

// [sunsong] 送混 quota 实验
DECLARE_SPDM_ABTEST_BOOL(enable_mix_quota_calib_exp);
DECLARE_SPDM_ABTEST_DOUBLE(mix_quota_new_rb_score_bonus_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(mix_quota_exp_gpm_ratio);
DECLARE_SPDM_ABTEST_INT64(mix_quota_first_soft_quota);
DECLARE_SPDM_ABTEST_INT64(mix_quota_first_hard_quota);
DECLARE_SPDM_ABTEST_INT64(mix_quota_total_soft_quota);
DECLARE_SPDM_ABTEST_INT64(mix_quota_total_hard_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_mix_quota_rerank_by_cpm);
DECLARE_SPDM_ABTEST_BOOL(enable_rerank_quota_by_cpm_and_rs);
DECLARE_SPDM_ABTEST_DOUBLE(rerank_quota_by_cpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(rerank_quota_by_rs_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(rerank_quota_by_rb_ratio);

// [jiangqiqi03] RCT 实验
DECLARE_SPDM_ABTEST_BOOL(enable_mix_bonus_fixed_ratio_rct_experiment);
DECLARE_SPDM_ABTEST_BOOL(enable_update_ad_gpm_trace);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_gpm_auto_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_bonus_allocation_cpm_user_exp);
DECLARE_SPDM_ABTEST_STRING(bonus_allocation_cpm_user_exp_tag);
DECLARE_SPDM_ABTEST_STRING(ad_gpm_auto_ratio_exp_tag)

// [hanhao05] bonus 重分配理解实验
DECLARE_SPDM_ABTEST_BOOL(enable_inner_multi_queue_bonus_reallocation);
DECLARE_SPDM_ABTEST_BOOL(disable_inner_multi_queue_flow);
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_bonus_adjust);
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_bonus_adjust_ratio);

// rank 上移阶段开关，优先级低于 kconf 开关
// 1. ad_resp 构造上移 nodiff
// 2. ad_resp 构造上移 nodiff && 切换
// 3. ad_resp 构造上移 切换
// 4. 请求 rank 上移
// 5. 请求京快上移
DECLARE_SPDM_ABTEST_INT64(enable_rank_migration_stage_default);  // [jiangyuzhen03] rank 上移(default) // NOLINT
DECLARE_SPDM_ABTEST_INT64(enable_rank_migration_stage_search);  // [jiangyuzhen03] rank 上移(search) 不含阶段 5 // NOLINT
DECLARE_SPDM_ABTEST_INT64(enable_rank_migration_stage_splash);  // [jiangyuzhen03] rank 上移(splash) // NOLINT
DECLARE_SPDM_ABTEST_INT64(rank_migration_ad_server_timeout_adjust_default);  // [jiangyuzne03] rank 上移 ad-server 超时调整(default) // NOLINT
DECLARE_SPDM_ABTEST_INT64(rank_migration_ad_server_timeout_adjust_search);  // [jiangyuzne03] rank 上移 ad-server 超时调整(search) // NOLINT
DECLARE_SPDM_ABTEST_INT64(rank_migration_ad_server_timeout_adjust_splash);  // [jiangyuzne03] rank 上移 ad-server 超时调整(splash) // NOLINT
DECLARE_SPDM_ABTEST_INT64(rank_migration_ad_rank_timeout_adjust_default);  // [jiangyuzne03] rank 上移 ad-rank 超时调整(default) // NOLINT
DECLARE_SPDM_ABTEST_INT64(rank_migration_ad_rank_timeout_adjust_search);  // [jiangyuzne03] rank 上移 ad-rank 超时调整(search) // NOLINT
DECLARE_SPDM_ABTEST_INT64(rank_migration_ad_rank_timeout_adjust_splash);  // [jiangyuzne03] rank 上移 ad-rank 超时调整(splash) // NOLINT
DECLARE_SPDM_ABTEST_INT64(merge_exp_to_adrank_timeout)  // [wangyangrui] 融合实验 rank 额外超时
DECLARE_SPDM_ABTEST_INT64(adrank_extra_timeout_for_refactor);  // [jiangyuzhen] rank 全图化改造额外超时
DECLARE_SPDM_ABTEST_INT64(xifan_native_cnt);  // [yushengkai] 喜番软广返回条数
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_native_cnt);

// [qiaolin] 混排 bonus 平衡性优化
DECLARE_SPDM_ABTEST_BOOL(enable_bonus_balence_stategy);
DECLARE_SPDM_ABTEST_BOOL(enable_bonus_sep_inner_outer);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_bonus_cali_ratio_inner);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_bonus_cali_extra_ratio_inner);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_bonus_cali_ratio_outer);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_bonus_cali_extra_ratio_outer);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_bonus_cali_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_bonus_cali_extra_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(to_xifan_cpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(to_xifan_cpm_ratio_v2);
// [liaibao] 用户体验优化人群策略标签数据接入开关
DECLARE_SPDM_ABTEST_BOOL(enable_ue_user_strategy_tag_data);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_item_purchase_list);  // [yangxuan06] front 短剧付费用户正排开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_kimi_user_ecpc_type);  // [yangxuan06] 正排开关用户类型
DECLARE_SPDM_ABTEST_BOOL(enable_na_book_list);  // [yangxuan06] 正排开关
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_pk);  // [yushengkai] 喜番 PK

// [qiaolin] 队列去重选择 ecpm 最大的保留
DECLARE_SPDM_ABTEST_BOOL(enable_ecpm_rankidx_dedup);
DECLARE_SPDM_ABTEST_BOOL(enable_rb_rankidx_dedup);

// [guochangyu]
DECLARE_SPDM_ABTEST_BOOL(enable_pass_outer_ecom_conv_type);
DECLARE_SPDM_ABTEST_BOOL(enable_pass_goods_cate_2nd_ids);
DECLARE_SPDM_ABTEST_BOOL(enable_pass_user_interested_behavior_type);
DECLARE_SPDM_ABTEST_BOOL(enable_pass_user_layered_tags);

// [yuhanzhang]
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_cpm_modify_ratio);  // [yuhanzhang] 快小游探索 nc 调价稀疏
DECLARE_SPDM_ABTEST_BOOL(enable_kmini_game_explore_nc_force_reco);  // [yuhanzhang] 快小游探索 nc 开关
DECLARE_SPDM_ABTEST_INT64(mini_game_max_hard_ad_force_reco_tag_size);  // [yuhanzhang] 快小游探索 nc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_resort_mini_game_change_cpm);  // [yuhanzhang] 快小游改价开关
DECLARE_SPDM_ABTEST_BOOL(enable_kmini_game_explore_nc_user_stain);  // [yuhanzhang] iaa/iap/iaap/小说用户染色

DECLARE_SPDM_ABTEST_BOOL(enable_game_iaa_free_user_ltv_adjust);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_supress_coef);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_lower_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_normal_coef);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_upper_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_boost_coef);
DECLARE_SPDM_ABTEST_STRING(iaa_coin_task_conf_tag_user_ltv);

DECLARE_SPDM_ABTEST_BOOL(enable_iaa_coin_switch);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_coin_duration_task);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_coin_ipu_task);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_coin_user_ltv_level_boost);
DECLARE_SPDM_ABTEST_STRING(iaa_coin_task_conf_tag);
DECLARE_SPDM_ABTEST_STRING(iaa_ad_coin_abtest_tag);
DECLARE_SPDM_ABTEST_STRING(iaa_ad_free_abtest_tag);
DECLARE_SPDM_ABTEST_INT64(ad_rank_server_extra_timeout);  // [jiangyuzhen03] rank 超时调整开关
DECLARE_SPDM_ABTEST_INT64(ad_rank_server_extra_timeout2);  // [jiangyuzhen03] rank 超时调整开关 2
DECLARE_SPDM_ABTEST_INT64(ad_rank_server_extra_timeout3);  // [jiangyuzhen03] rank 超时调整开关 3
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_coin_ipu_task_threshold_supress_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_coin_ipu_task_threshold_v2);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_coin_duration_task_threshold_supress_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_game_iaa_coin_duration_task_threshold_v2);

DECLARE_SPDM_ABTEST_BOOL(enable_iaa_ad_free_duration_suppress);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_ad_free_duration_suppress_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_ad_free_duration_suppress_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_ad_free_ipu_suppress);
DECLARE_SPDM_ABTEST_INT64(game_iaa_ad_free_ipu_suppress_ipu_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_ad_free_ipu_suppress_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_ad_free_user_response_rate_drop);
DECLARE_SPDM_ABTEST_BOOL(subpage_admit_ad_target_server_merchant_10008001);  // [jiangyuzhen03] 关注页 10008001 接入内循环硬广 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_front_upstream_timeout);  // [jiangyuzhen03] 端到端可用性引入上游超时
DECLARE_SPDM_ABTEST_DOUBLE(user_iaa_ad_free_response_rate_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(user_iaa_ad_free_response_rate_drop_rate);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_coin_amount_lower_bound);
DECLARE_SPDM_ABTEST_STRING(common_card_min_valid_version);

// [zhangpuyang]
DECLARE_SPDM_ABTEST_BOOL(enable_direct_mix_predict);  // 混排预估 router 上移
DECLARE_SPDM_KCONF_BOOL(enable_direct_mix_predict_kconf);
DECLARE_SPDM_KCONF_BOOL(enable_direct_mix_predict_debug);  // 混排预估 router 上移
DECLARE_SPDM_KCONF_BOOL(enableBidAssistType);
DECLARE_SPDM_KCONF_BOOL(removeIsInspireUnlockTube);
DECLARE_SPDM_KCONF_BOOL(enableRealUseSdkInOnlineJoin);
DECLARE_SPDM_ABTEST_BOOL(close_prerank_prepare_router);  // 关闭粗排 router 预取 ab
DECLARE_SPDM_ABTEST_BOOL(enable_send_user_feature_pb);  // [zhangpuyang] 透传 pb 开关
DECLARE_SPDM_ABTEST_BOOL(use_data_converter_v2);
DECLARE_SPDM_KCONF_BOOL(useDataConverterV2);
DECLARE_SPDM_ABTEST_BOOL(enable_bonus_reallocation_exp);
DECLARE_SPDM_ABTEST_STRING(bonus_reallocation_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_bonus_adjust_v2);
DECLARE_SPDM_ABTEST_DOUBLE(inner_no_fanstop_bonus_adjust_ratio);

// [menjunyi]
DECLARE_SPDM_ABTEST_BOOL(enable_photo_rerank_req);  // photo 重请求开关
DECLARE_SPDM_KCONF_BOOL(enablePhotoRerankReq);
DECLARE_SPDM_KCONF_BOOL(enableOcpxActionSupportTypeToMatrix);
DECLARE_SPDM_KCONF_BOOL(enableDeviceSupportTaid);
DECLARE_SPDM_KCONF_INT32(maxRtaUserOrientationSize);
DECLARE_SPDM_ABTEST_BOOL(close_ranking_prepare_router_splash);  // [zhangzhicong] 关闭开屏精排 router 预取 ab
DECLARE_SPDM_KCONF_BOOL(closeRankingPrepareRouter);  // 关闭精排 router 预取 kconf
DECLARE_SPDM_ABTEST_BOOL(unlogin_user_skip_inner_photo_target);  // unlogin user skip inner_photo target
DECLARE_SPDM_ABTEST_BOOL(unlogin_user_skip_live_target);  // unlogin user skip live target
DECLARE_SPDM_KCONF_INT32(prerankUserInfoBSTruncate);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_user_info_bs_truncate);
DECLARE_SPDM_KCONF_BOOL(prerankUserInfoBSCompress);
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_user_info_bs_compress);

DECLARE_SPDM_ABTEST_BOOL(enable_discount_effect_exp);  // [jiangyuzhen03] 计费打折关闭实验
DECLARE_SPDM_ABTEST_BOOL(disable_mix_rank_flow_price_bound_in_front);  // [jiangyuzhen03] 关闭 front 流量混排流量计费兜底  //NOLINT

// [liujiahui10]
DECLARE_SPDM_ABTEST_BOOL(enable_live_cold_gimbal);  // [liujiahui10] 冷启动 gimbal
DECLARE_SPDM_ABTEST_BOOL(enable_nearline_update_gap);  // [liujiahui10] 近线频控升级
DECLARE_SPDM_ABTEST_BOOL(enable_nearline_request_sampling);  // [liujiahui10] 近线频控升级
DECLARE_SPDM_ABTEST_BOOL(is_photo_nearline_request);  // [liujiahui10] 近线频控升级
DECLARE_SPDM_ABTEST_BOOL(is_live_nearline_request);  // [liujiahui10] 近线频控升级
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_skip_fanstop_pos);  // [yushengkai]
// [lifeiyang03] 商品通路召回时，控制是否出优惠卷
DECLARE_SPDM_ABTEST_BOOL(enable_big_v_items_first);

// [liuxianyi]
DECLARE_SPDM_ABTEST_BOOL(enable_nobid_store_wide_roi_fix);

// [huliren]
DECLARE_SPDM_ABTEST_BOOL(enable_log_title_select_score);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_dup_photo_id_write_cvr_gmv_info);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_fill_dup_info_prependicular_logic);

DECLARE_SPDM_ABTEST_BOOL(enable_search_trans_all_style_info);  // [lifeiyang03] 强样式透穿 admatrix

DECLARE_SPDM_KCONF_BOOL(enableAdBrandUseDragon);  // [qianyangchao03] 访问 ad_brand 使用 Recommend 接口
DECLARE_SPDM_KCONF_BOOL(enableSendUserinfoToAdBrand);  // [qianyangchao03] 向 ad_brand 透传 userinfo

// [liweijie06]
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_subpage_price_discount);  // 猜你喜欢不同子页面计费打折
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_pdp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_olp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_odp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_psp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_ddp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_pdp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_olp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_odp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_psp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_ddp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_pdp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_olp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_odp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_psp);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_ddp);
}  // namespace front_server
}  // namespace ks
