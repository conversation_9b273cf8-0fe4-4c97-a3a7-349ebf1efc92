#include "teams/ad/front_server/util/spdm/spdm_switches.h"
#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace front_server {

// 定义 kconf 开关:  SPDM_KCONF_BOOL(path, key);
// 定义 abtest 开关: SPDM_ABTEST_BOOL(key, AbtestBiz);

// 以下为 kconf 开关定义.
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableLazyInitExtraCommonAttr);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillLiveStreamCoreUser);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSkipPackageIdCheck);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableRecordCrmCenter);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableShowAggrInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAdWxMiniApp);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableInnerCidOuterId);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableCmdKeyRecord)
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFixBillTime);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableEspRegionAb);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillField)
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePrintAllFactorInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableToXifanCpmRatio);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableXifanFeedAd);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableNewCutomCheck);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableTransIsAggregationStatus);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetAdjustPriceRecord);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSkipSimpleNoDsp);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePreviewUserFanstop);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFixQueueTypeForGalaxyPreview);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUseDebugPageId);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAllScene);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableModelBillTime);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableLogTvCopyright);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSplashCidReplaceSchema);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFixPriceLow);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableIaaUpliftOri);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableLogAdStid);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableDeleteAggrePageOldLink);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePlayletCampaignTypeOcpxDiscountV2);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAccIncrementExploreSign);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableIncrementExploreSkipBs);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableProjectBidSend);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, disableCacheLiveAd);
SPDM_KCONF_BOOL(ad.adFlowControl, enableEspBudgetPartition);
SPDM_KCONF_BOOL(ad.adFlowControl, enableOnlyEspBudgetPartition);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableEspOrderInSpecialtyFillFanstop);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableEspNoMobileOrderInSpecialtyFillFanstop);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableInsteadAssert);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSendInnerDspAckDataFlow);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetLocalLifePromotionStyle);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetLlsidForSample);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetZhuitouInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetZhuitouData);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillRoiOrder);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableStorewideGuaranteed);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillMerchantUserTag);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetNeedComponentItem);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetFeaLocalLifePromotionStyle);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableLiveCopyQueueTypeFixV2);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableLiveCopyQueueTypeFix);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableCplOnlineJoin);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableCplAck);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetEnvInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetKeepWebView);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetImInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableCheckRegionV2);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableLiveAdAddTrack);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableCplRequestForMainChain);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFixSameLiveCopy);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAdMixRankTopMixer);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePaidDuanjuModifyConversionType);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableLawModifyConversionType);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePrintAuctionDebugInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, disablePriceRecordForOrigin);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableRemoveOnlineJoinPriceRecord);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUserContextAllStation);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSendDspAckDataFlowAllStationV2);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableRemoveTraceLogCopy);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillPageReportInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetAdSmartOffersInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetCommonSmartOffersInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetNativeDiffAuthorFlag);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFixFictionPanelPriceRct);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, forbidFictionPanelPriceRct);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePriceRb);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableOpenHarmonyReq);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, disableNebulaFollowOuter);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, disableNebulaFollowLiveInnerCardEntry);
SPDM_KCONF_BOOL(ad.frontserver2, enableNearlinRequestGap);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFictionReportExtData);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFictionBookStrategyHighPriority);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFictionReportNovelPrice);
SPDM_KCONF_BOOL(ad.ad_query_recommend, enableADBoxBrowsedDuplicate);
SPDM_KCONF_BOOL(ad.frontserver2, disableChargeInDuitou);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFixFictionPriceTagGroupTag);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableReportAllFictionPrice);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFictionRandomPriceRandomStyle);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableInnerPhotoItemTargetRequest);
SPDM_ABTEST_BOOL(enable_inner_photo_item_target_req, ks::AbtestBiz::AD_DSP);

SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableTransReservationIds);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePreInstallFilterFor2024);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePreInstallFilterFor2025);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillAigcInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillItemStid);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillBsLive);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAllAdTargetBreakDelete);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableTargetBreakDelete);
SPDM_KCONF_BOOL(ad.frontserver, enableUseKconfPosInfo);
SPDM_KCONF_BOOL(ad.frontserver, enableUniversePreviewStyleByKconf);
SPDM_KCONF_BOOL(ad.adRank2, enableUpdateTrace);
SPDM_KCONF_BOOL(ad.adRank2, enableFillAggBiddingRelease);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUseAggrCpmRatioForPrice);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAggregationBiddingDebugLog);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUpdateDebug);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableCoverMicroApp);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAliOuterDeviceInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableRtaFilterInfoLog);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableRealTimeBounsCost);
SPDM_KCONF_BOOL(ad.frontserver, enableNewAdExposedInfo);
SPDM_KCONF_BOOL(ad.frontserver, enableLpComponent);
SPDM_KCONF_BOOL(ad.frontserver, enableLpComponent3);
SPDM_KCONF_BOOL(ad.adRank, enableNodeTimeCost);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, universePreviewStyleCheck);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, universePreviewDisableOriginLive);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, disableMinProgramCoverUrlReplace);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableNewInnerFanstopStid);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSearchSplashSetSpeed);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSimplifyDiscountPrice);
SPDM_KCONF_BOOL(ad.frontserver, enableFixRtbBouns);
SPDM_KCONF_BOOL(ad.frontserver, enableUniverseRtbCpmBouns);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableTubeInspireBrandLogCheckFakeType);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillComboPackageSubSourceType);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableBrandDuanjuCostToRedis);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSendTubeInspireBrandLog);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableInspireBrandReqSceneFilter);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableInspireBrandPk);
SPDM_KCONF_BOOL(ad.frontserver2, enableSearchkeepDeleteNativePos);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSearchTraceHotQuery);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAdServerTimeToServerShow);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUpdateSearchSampleTag);
SPDM_KCONF_BOOL(ad.adRank2, enableSplitShelfMerchant);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSendEspBaseInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSecondForwardIndex);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableGuessYouLikeMixRankReq);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableGuessYouLikeLiveCard);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableGuessYouLikeBuildDelivery);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableGuessYouLikeLiveCardProduct);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableGuessYouLikeLiveCardOther);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableKuaishuoNovelAd);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableVideoIncomeTaskAd);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableDrawFlowAd);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSendIaaSourceType);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, disableXiFanInnerLoop);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, disableXiFanInnerLoopLive);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableXiFanReplaceKwaiUrl);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSerialAggrCard);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableCommonCardSecondRequest);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSendRtaSecondBid);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUseNewPressTestFlag);
SPDM_KCONF_BOOL(ad.frontserver, enableTraceLogKeySplit);
SPDM_KCONF_BOOL(ad.frontserver, enableAbtestMappingIdService);
SPDM_KCONF_BOOL(ad.frontserver, enableSearchTransferLifeExtendFileds);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableMinMaxDiscountPriceLog);
SPDM_KCONF_BOOL(ad.frontserver, enableSendPayModeToMix);
SPDM_KCONF_BOOL(ad.frontserver, enableFixFillMixedInfo);
SPDM_KCONF_BOOL(ad.frontserver, enableSearchAdWxMiniApp);
SPDM_KCONF_BOOL(ad.frontserver, enableMatchServerResMigDiff);
SPDM_KCONF_BOOL(ad.frontserver, enableSearchAdSeriesKbox);
SPDM_KCONF_BOOL(ad.frontserver, enableSearchAdMiniGameKbox);
SPDM_KCONF_BOOL(ad.frontserver, enableSearchAdIsSingleStyle);
SPDM_KCONF_BOOL(ad.frontserver2, enableGoodsTabFilterCheckInNewWay);
SPDM_KCONF_BOOL(ad.frontserver, enableRTBEyemax);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSearchProductShowSellerIdFixV2);
SPDM_KCONF_BOOL(ad.frontserver, enableSplashAdxPackOnlineJoinParams);
SPDM_KCONF_BOOL(ad.frontserver2, enableSplashNewAndRefluxDeviceFilterV2);
SPDM_KCONF_BOOL(ad.frontserver2, enableBrandTubeOrderPriceControl);
SPDM_KCONF_BOOL(ad.frontserver, enableTotalFanstopDarkChargeTag);
SPDM_KCONF_BOOL(ad.frontserver, enableInspireMixDoubleTag);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUniverseStrictNativeAd);
SPDM_KCONF_BOOL(ad.adMix, enable_soft_ad_disable_ad_mark_with_review_fix);
SPDM_KCONF_BOOL(ad.brandserver, enableBrandHeaderUA);
SPDM_KCONF_BOOL(ad.frontserver, enableSplashWeiliang);
SPDM_KCONF_BOOL(ad.adRank2, enableT7ROISkipBillingSeparate);
SPDM_KCONF_BOOL(ad.adRank2, enableAdMerchantStrategyV2);
SPDM_KCONF_BOOL(ad.frontserver, enableUniverseSmallGameDirectCall);
SPDM_KCONF_BOOL(ad.frontserver, enableFanstopBsOcpxBlacklist);
SPDM_KCONF_BOOL(ad.adRank, disableAllPerf);
SPDM_KCONF_BOOL(ad.adRank, enableTransAdForceRecoTag);
SPDM_KCONF_BOOL(ad.frontserver2, fixSplashRtaLlsidAtoi);
SPDM_KCONF_BOOL(ad.frontserver2, enableUniverseBannerAds);
SPDM_KCONF_BOOL(ad.adRank2, enableMixRankMonitorData);
SPDM_KCONF_BOOL(ad.frontserver2, enableUniversePlayT10sUrlEndUrl);
SPDM_KCONF_BOOL(ad.frontserver2, enableUniverseFillSixinConversionInfo);
SPDM_KCONF_BOOL(ad.frontserver2, disableInvokeRetetion)
SPDM_KCONF_INT32(ad.frontserver2, enableRankMigrationStageDefault, 0);
SPDM_KCONF_INT32(ad.frontserver2, enableRankMigrationStageSearch, 0);
SPDM_KCONF_INT32(ad.frontserver2, enableRankMigrationStageSplash, 0);
SPDM_KCONF_INT32(ad.frontserver2, rankMigrationAdServerTimeoutAdjust, 0);
SPDM_KCONF_INT32(ad.frontserver2, rankMigrationAdRankTimeoutAdjust, 0);
SPDM_KCONF_INT32(ad.frontserver2, e2eAvailabilityTimeoutMsDelta, -10);
SPDM_KCONF_INT32(ad.frontserver2, e2eAvailabilityTimeoutMsDeltaOld, -20);
SPDM_KCONF_BOOL(ad.frontserver2, enableLspSetSchemaUrl);
SPDM_KCONF_BOOL(ad.frontserver2, enableSearchBrandOneAdList);
SPDM_KCONF_BOOL(ad.frontserver2, enableHarmonyDeviceInBrand);
SPDM_KCONF_BOOL(ad.frontserver2, enableSplitAdServerAttr);
SPDM_KCONF_BOOL(ad.frontserver2, enableSetBrandSplashSensitiveGroup);
SPDM_KCONF_BOOL(ad.adRank, enableFetchGpmFeatureForMixRank);
SPDM_KCONF_BOOL(ad.frontserver2, enableMatrixSplashAddUaWebview);
SPDM_KCONF_BOOL(ad.frontserver2, enableBrandPreviewSkipMixRank);
SPDM_KCONF_BOOL(ad.frontserver2, enableFrontAdxSellerAndSidList);
SPDM_KCONF_BOOL(ad.frontserver2, enableFrontZcAuthorListHourly);
SPDM_KCONF_BOOL(ad.frontserver2, enableTransferLifeExtendFileds);
SPDM_KCONF_BOOL(ad.frontserver2, enableSearchSuperCardType);
SPDM_KCONF_BOOL(ad.frontserver2, enableSetHostInfoForServerShow);
SPDM_KCONF_BOOL(ad.frontserver2, enableYiTianResourceId);
SPDM_KCONF_BOOL(ad.frontserver2, zeroCopyRequestData);
SPDM_KCONF_BOOL(ad.frontserver2, enableFillInnerExploreUserTag);
SPDM_KCONF_BOOL(ad.adtarget3, enableResidenceAdcodeTarget);
SPDM_KCONF_BOOL(ad.adRank2, enableBonusDotPerf);
SPDM_KCONF_BOOL(ad.adRank2, enableDiffCompareAd);
SPDM_KCONF_BOOL(ad.frontserver2, enableHardAdRerankUseAllPeople);
SPDM_KCONF_BOOL(ad.frontserver2, enable_request_nearline_conf);
SPDM_KCONF_BOOL(ad.frontserver2, enableCIDCardRemoveSwitch);
SPDM_KCONF_BOOL(ad.frontserver2, enableAlwaysLogSetStageAz);
SPDM_KCONF_BOOL(ad.frontserver2, enableTubeInspireSetAdClientInfo);
SPDM_KCONF_BOOL(ad.frontserver2, enableInnerExploreMixPriceAdjust);
SPDM_KCONF_BOOL(ad.frontserver2, enableFixSetMixPriceAdjustRatio);
SPDM_KCONF_BOOL(ad.frontserver2, enableFeedExploreMixPriceAdjust);
SPDM_KCONF_BOOL(ad.frontserver2, enableFeedExploreMixExperienceValue);
SPDM_KCONF_BOOL(ad.frontserver2, enableAdBrandUseDragon);
SPDM_KCONF_BOOL(ad.frontserver2, enableSendUserinfoToAdBrand);

// ------------------------------ kconf int32 参数定义 ---------------------------------
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, requestDebugInfoRatio, 1);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, antiMicorAppExpireSec, 86400);
SPDM_KCONF_INT32(ad.frontserver, negativeRestrictTime, 10);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, allStationDataFlowRate, 500);
SPDM_KCONF_INT64(ad.adFrontDiffSwitches, cplReturnNum, 100);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, maxAntiMicorAppLength, 50);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, maxUniverseClickRewardCntOneDay, 30);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, maxUniverseClickRewardCntOneHour, 10);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, direct_merchant_discount_exp_tag, 0);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, bidGimbalTailNumber, 0);
SPDM_KCONF_INT32(ad.ad_query_recommend, maxADServerShowHour, 12);
SPDM_KCONF_INT32(ad.frontserver2, adMixModelItemSetTopKNum, 30);
// ------------------------------ kconf double 参数定义 ---------------------------------
SPDM_KCONF_INT64(ad.adFrontDiffSwitches, minPriceBound, 0);
SPDM_KCONF_INT64(ad.adFrontDiffSwitches, maxPriceBound, 1000000);

SPDM_KCONF_INT32(ad.frontserver2, annRouterPrefetchLatency, 10);
// [limiaochen] 修复 front 定向 hit 输出
SPDM_KCONF_BOOL(ad.frontserver2, enableFixFreAdcodeOutput);

// 以下为 abtest 开关定义.
SPDM_ABTEST_BOOL(enable_request_nearline, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_cache_strategy_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ad_pk_score_type_copy, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_industry_hc_pid_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wentou_gimbal_ratio_dynamic, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_gimbal_ratio_dynamic, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_target_distance_check, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_bonus_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_ad_roas_iaap, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pack_stable_category_thrid_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_magic_site_replace, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_magic_site_replace_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wechat_company_add_fans_direct_jump, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_adx_app_privacy_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_compat_mini_app_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_small_game_get_reco_user_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rtb_win_rate_loss_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_rtb_win_rate_predict_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_stid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_send_user_feature_to_adserver, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_bonus_to_conv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_bonus_to_conv_tail, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_smart_photo_replace_dye, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_inspire_unlock_tube_brand, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_app_store_device_mod_lower, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_combo_search_non_first_page, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_search_item_kbox_normal_region, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_skip_cpm_filter_qps_protection, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_universe_dynamic_cpm_threshold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_hard_ad_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_negative_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_jk_track_in_splash, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_brand_pd_pk, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_brand_splash_prefetch_extend_timeout, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_series_list_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_trans_macros, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reco_user_info_trim_for_ps, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_flow_control_cpm_threshold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cpm_delta_ratio_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cpm_explore_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_sku_upgrade, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_combo_search_sku_upgrade, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_item_recall_on_feeds, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_cpm_bound_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tube_inner_loop_by_campaign_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_coin_bias, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_ann_prefetch_notify, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_rewarded_coin_multi_key, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_rewarded_ban_user, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_inner_rpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_playlet_campaign_type_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_second_industry_v5, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_rerank_add_author_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_rerank_add_dup_photo_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_ai_add_is_high_value, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_ai_is_high_value_use_threshold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(adbox_quick_search_ad_scene_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(search_thanos_change_adx_charge_point_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(ad_box_thanos_interactive_form_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ad_mix_rank_score_aligned_mix_strategy, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(w5_extra_3_percent_bonus_weight_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(w5_extra_3_percent_bonus_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_rank_aligned_mix_score_to_hive, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outer_dsp_ad_rank_aligned_mix_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outer_dsp_ad_rank_aligned_mix_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outer_soft_ad_rank_aligned_mix_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outer_soft_ad_rank_aligned_mix_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_rank_aligned_mix_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_rank_aligned_mix_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_live_rank_aligned_mix_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_live_rank_aligned_mix_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_stid_inner_exclude_fanstop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_stid_inner_exclude_fanstop_v2, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_user_action_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(user_aciont_admit_stratagy, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_BOOL(enable_inner_rb_minus_gpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_new_rank_weight_by_page, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_rb_not_use_mix_params, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_ad_live_rb_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_rb_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_live_rb_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_rb_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(dsp_ad_new_rank_item_to_mix_quota, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(soft_ad_new_rank_item_to_mix_quota, ks::AbtestBiz::AD_DSP, 1);

SPDM_ABTEST_BOOL(enable_new_rank_weight_by_mix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_ad_cpm_weight_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_cpm_bias_gamora, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inner_ad_cpm_exp_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outloop_ad_cpm_weight_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outloop_ad_cpm_bias_gamora, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(outloop_ad_cpm_exp_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outloop_softad_cpm_weight_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outloop_softad_cpm_bias_gamora, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(outloop_softad_cpm_exp_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_weight_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_bias_gamora, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_exp_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_weight_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_bias_gamora, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_exp_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(unify_photo_ue_score_weight_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(unify_photo_ue_score_bias_gamora, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(unify_photo_ue_score_exp_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(unify_live_ue_score_weight_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(unify_live_ue_score_bias_gamora, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(unify_live_ue_score_exp_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_cpm_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_ad_cpm_bias_nebula, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inner_ad_cpm_exp_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outloop_ad_cpm_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outloop_ad_cpm_bias_nebula, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(outloop_ad_cpm_exp_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outloop_softad_cpm_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outloop_softad_cpm_bias_nebula, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(outloop_softad_cpm_exp_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_bias_nebula, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_live_gpm_exp_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_bias_nebula, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(rrms_merchant_photo_gpm_exp_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(unify_photo_ue_score_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(unify_photo_ue_score_bias_nebula, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(unify_photo_ue_score_exp_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(unify_live_ue_score_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(unify_live_ue_score_bias_nebula, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(unify_live_ue_score_exp_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_rb_rank_first_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_next_n_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(neoMaxLookAgainDialogCount, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(rewarded_next_n_ad_price_thr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(rewarded_next_n_ad_coin_thr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(rewarded_next_n_ad_roi_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(rewarded_next_n_ad_gross_thr, ks::AbtestBiz::AD_DSP, -10);
SPDM_ABTEST_BOOL(enable_inspire_next_n_preview_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_next_n_set_coin2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(preview_coin_rct_p, ks::AbtestBiz::AD_DSP, 0.02);
SPDM_ABTEST_DOUBLE(preview_coin_def_roi, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_BOOL(enable_inspire_task_cd, ks::AbtestBiz::AD_DSP);  // 激励广告 任务 cd
SPDM_ABTEST_BOOL(enable_inspire_task_cnt_refactor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inspire_task_cnt_price_thres, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inspire_task_cnt_coin_thres, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inspire_task_cnt_gross_thres, ks::AbtestBiz::AD_DSP, -10.0);
SPDM_ABTEST_DOUBLE(inspire_task_cnt_roi_thres, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(inpsire_task_cd_time, ks::AbtestBiz::AD_DSP, 60000);  // 单位毫秒
SPDM_ABTEST_DOUBLE(inspire_long_task_cd_price_thres, ks::AbtestBiz::AD_DSP, -1.0);
SPDM_ABTEST_DOUBLE(inspire_long_task_cd_avg_pre3_task_coin_thres, ks::AbtestBiz::AD_DSP, -1.0);
SPDM_ABTEST_INT64(inspire_long_task_cd_time, ks::AbtestBiz::AD_DSP, 1800000);  // 单位毫秒
SPDM_ABTEST_INT64(inspire_risk_user_task_cd_time, ks::AbtestBiz::AD_DSP, 3600000);
SPDM_ABTEST_BOOL(enable_engine_coin1_risk_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(dynamic_task_cnt_by_ucc_score_alpha, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_rank_has_more, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_box_task_cd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_budget_allocation_user_roi_alpha_bias, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(incntv_ad_queue_info_log_num, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_DOUBLE(iaa_next_n_coin_ucpm_lower, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(iaa_next_n_coin_ucpm_upper, ks::AbtestBiz::AD_DSP, 100000.0);
SPDM_ABTEST_INT64(iaa_next_n_coin_value, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_BOOL(disable_rewarded_next_n_task_cnt_upper, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(rewarded_next_n_task_cnt_upper, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_BOOL(disable_rewarded_next_n_task_coin_upper, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(rewarded_next_n_task_coin_upper, ks::AbtestBiz::AD_DSP, 1000000);
SPDM_ABTEST_BOOL(disable_rewarded_next_n_risk_user, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_coin_discount_task_cnt_upper, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(coin_discount_task_cnt_upper, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_BOOL(enable_coin_discount_task_coin_upper, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(coin_discount_task_coin_upper, ks::AbtestBiz::AD_DSP, 1000000);
SPDM_ABTEST_BOOL(enable_coin_discount_risk_user, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(coin_lower_for_coin_discount, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(deep_coin_lower_for_coin_discount, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_BOOL(enable_coin_discount_refund, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_coin_discount_refund_for_target_ocpx, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_coin_discount_by_risk_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_extra_coin_budget_allocation, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_extra_coin_budget_allocation_rct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(extra_coin_budget_allocation_rct_p, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(extra_coin_rct_increase_ratio, ks::AbtestBiz::AD_DSP, "0.1,0.2");
SPDM_ABTEST_INT64(incntv_ad_fill_tp_rs_inner_cnt_stat_thres, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_incntv_ad_realtime_progress, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(incntv_ad_realtime_pgs_thr, ks::AbtestBiz::AD_DSP, "1,2,3");
SPDM_ABTEST_DOUBLE(incntv_ad_pgs_roi_coef, ks::AbtestBiz::AD_DSP, 1.5);
SPDM_ABTEST_STRING(incntv_ad_realtime_pgs_amt_ratio, ks::AbtestBiz::AD_DSP, "1.0,1.0,1.0");
SPDM_ABTEST_INT64(incntv_realtime_pgs_expire_time, ks::AbtestBiz::AD_DSP, 180);
SPDM_ABTEST_DOUBLE(incntv_ad_realtime_pgs_admit_price_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(incntv_ad_pgs_common_idx_rct_p, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_incntv_ad_pgs_queue_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(incntv_ad_pgs_coin_budget_adjust, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(fix_incntv_ad_pgs_has_more_condition2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(preview_coin_1st_discount, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_BOOL(enable_ee_purchase_cvr_tags, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_add_prerank_dnc_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(refund_num_thres_for_coin_discount, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(refund_rate_lower_for_coin_discount, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(refund_rate_coef_for_coin_discount, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(refund_num_decay_for_coin_discount, ks::AbtestBiz::AD_DSP, 0.95);
SPDM_ABTEST_DOUBLE(risk_score_coef_for_coin_discount, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(client_ai_high_value_use_cpm_or_rankbenifit, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(client_ai_high_value_use_threshold_value, ks::AbtestBiz::AD_DSP, 1000000);
SPDM_ABTEST_BOOL(enable_client_ai_is_high_value_use_profile, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(client_ai_high_value_user_days, ks::AbtestBiz::AD_DSP, 7);
SPDM_ABTEST_INT64(client_ai_high_value_user_pctiles, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_BOOL(enable_client_ai_p1_live_ad_price_reduce, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(client_ai_p1_live_ad_price_reduce_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(client_ai_p1_rerank_live_ad_price_reduce_ration, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_client_ai_p1_live_ad_cpm_increase, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(client_ai_p1_live_ad_increase_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_client_ai_p0_adjust_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(client_ai_p0_rerank_adjust_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_client_ai_pk_score_use_mix_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(client_ai_pk_score_unify_cpm_inner_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(client_ai_pk_score_unify_cpm_outer_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(client_ai_pk_score_unify_bonus_inner_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(client_ai_pk_score_unify_bonus_outer_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_rewarded_coin_set_fix_num, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_multi_treatment_new_redis, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_rewarded_coin_for_multi_treatment, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_price_protect, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_common_front_mobile_soft_to_hard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_multi_queue_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_multi_queue_quota_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_buyer_level_mix_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rerank_inner_multi_queue_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rerank_merchant_buyer_multi_queue, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(merchant_buyer_multi_queue_exp_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_rerank_inner_multi_queue_reallocate_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_extra_suppport_by_roi1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rerank_inner_multi_q_ue_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_multi_queue_stage2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_multi_queue_roi1_strong_thres, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_multi_queue_roi1_weak_thres, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inner_multi_queue_inner_stage2_roi_thres, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(inner_multi_queue_exp_tag, ks::AbtestBiz::AD_DSP, "exp1");
SPDM_ABTEST_BOOL(enable_inner_multi_queue_support_hc_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_multi_queue_support_hc_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(prev_score_weight_params, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_photo, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_p2l, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_live, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_soft_ad, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_multi_queue_roi_weight_hard_ad, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_inner_multi_queue_roi_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(incremental_roi1_thres, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_hit_mix_bonus_cap, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(bonus_cap_num, ks::AbtestBiz::AD_DSP, 32.5);
SPDM_ABTEST_BOOL(enable_inner_multi_queue_roi_thres_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(add_multi_queue_trace_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_page_conf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_inner_multi_queue_thres_hour_adj, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(use_p10_as_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_p50_greater_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_p50_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_p25_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(use_p25_as_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(hc_adj_sctr_params, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roi1_p10_lower_param, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roi1_p10_lower_param_2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roi1_p10_lower_param_3, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roi1_p50_greater_param, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(use_prev_inner_ad_min_win_day_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_whole_score_adj_by_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(use_greater_params_as_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_multi_layer_roi_greater_branch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_multi_layer_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_multi_queue_final_sort_top_num, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_INT64(inner_top_gpm_cpm_quota, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(final_top_inner_quota, ks::AbtestBiz::AD_DSP, 8);
SPDM_ABTEST_INT64(final_top_merchant_buyer_quota, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(inner_multi_queue_replace_lower_bound_num, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(inner_multi_queue, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_inner_top_gpm_thresh, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_top_gpm_thresh, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_inner_only_high_value_queue, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_common_change_mobile_soft_to_hard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_common_move_review_through_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_age_expand, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_real_roi_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inspire_real_roi_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_INT64(inspire_real_roi_control_min_coin, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_DOUBLE(user_active_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_select_active_more_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_task_info_avaliable_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_select_subpage_more_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(valid_subpage_ids, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(valid_user_active_levels, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(min_user_active_coin, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(low_active_stg_freq_admit_task_cnt, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(low_active_stg_freq_admit_task_coin, ks::AbtestBiz::AD_DSP, 10000);
SPDM_ABTEST_BOOL(enable_inspire_lowest_coin_random_for_all, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inspire_lowest_coin_random_upper, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_BOOL(enable_low_active_strategy_admit_by_prob, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(low_active_strategy_admit_dau_prob_thres, ks::AbtestBiz::AD_DSP, -1.0);
SPDM_ABTEST_DOUBLE(low_active_strategy_admit_incntv_ad_dau_prob_thres, ks::AbtestBiz::AD_DSP, -1.0);
SPDM_ABTEST_DOUBLE(low_active_strategy_admit_uplift_rnkp_thres, ks::AbtestBiz::AD_DSP, -1.0);
SPDM_ABTEST_DOUBLE(low_active_strategy_admit_dau_prob_thres_lower, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(low_active_strategy_admit_incntv_ad_dau_prob_thres_lower, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(low_active_strategy_admit_uplift_rnkp_thres_lower, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(inspire_low_active_strategy_exp_tag_for_log, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(incntv_ad_monitor_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_fill_item_id_info_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_inspire_reco_user_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_prevent_explosion_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_hard_price_protect, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_price_rounded_up, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inspire_video_real_gross_lower, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inspire_video_real_roi_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inspire_video_jfb_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(inspire_live_real_roi_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inspire_live_jfb_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_append_mobile_direct_live_rank_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_mobile_p2l_rank_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_amd_photo_ranking_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_mobile_photo_rank_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_pc_photo_rank_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_merchant_live_charge_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pinduoduo_second_price_add_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pinduoduo_second_price_add_kconf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pec_sensitive_user, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_read_hc_rewarded_coin_user_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_user_coupon_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_read_server_rewarded_user_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dsp_live_deep_reward, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enabel_pec_coin_for_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_product_name_adjust_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_close_second_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_product_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_product_bonus_post_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inspire_live_inner_outer_pk_hard_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_guess_you_like_dynamic_pos,  ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_random_price,  ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_playlet_random_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_soft_random_price,  ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gyl_price_discount,  ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kxy_subsidy_global_nc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(gyl_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_gyl_item_card_price_discount,  ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_guess_you_like_ad_redis, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_h_v1_sim_clk_item_read, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_land_page_component_post_select, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_bid_tool_skip_compensation_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_material_derived_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_material_nieuwland_derived_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enabel_price_control_skip_mcb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(ab_switch_enable_user_ad_risk_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enabel_universe_biddding_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_person_mark_physical_posid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_billing_separate_gfp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bs_inner_loop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_bid_limit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_ratio_precision_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(close_billing_separate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_billing_separate_rewarded_video, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_migrate_new_cust_bs, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_loop_bs_trans_to_adpack, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_self_service_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pure_new_customer_bs_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enabel_revenue_optimize_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adjust_price_reserve_price_stra, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enabel_unify_billing_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enabel_merchant_roas_gsp_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_inner_loop_min_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_nobid_all_gsp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_nobid_all_gsp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_calc_mix_benefit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_mix_client_ad_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_inner_loop_filter_uplift_front_entrance, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_inner_loop_filter_uplift_front_exit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gyk_item_card_bs_spdm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_hosting_skip_bid_bs_spdm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(gyk_item_card_bs_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(adjust_price_reserve_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_BOOL(enable_ai_description, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ai_cover, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ai_cover_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_esp_product_title, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_STRING(outer_high_value_user_tag_new_key, ks::AbtestBiz::AD_DSP, "outer_high_value_");
SPDM_ABTEST_BOOL(enabel_unify_billing_ratio_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hc_score_include_bonus_fake, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_global_dynamic_interval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_ad_inner_stream_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_bigv_card_in_shop_tab, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_bigv_card_in_live_tab, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_live_reservation_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_search_query_user_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_image_text_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_get_router_user_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_get_router_user_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_goods_tab_live_filter_acc_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_goods_tab_sales_filter_acc_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_search_inner_stream_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_target_break_filter_down_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_search_index, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(splash_rtb_gray_status_dou_di, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_kbox_pos, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_rtb_realtime_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_ad_kbox, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_ad_item_kbox, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_kbox_from_top_ads, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_ad_live_kbox, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_min_price_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_origin_price_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cover_dsp_request, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_all_author_id_for_splash, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(dsp_live_pk_type, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(inner_live_truncate_num, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_BOOL(enable_dsp_live_rerank_price_correct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(dsp_live_rerank_price_correct_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_inspire_mix_record_live_browse_set, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_first_industry_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_dup_photo_fix_size, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_second_industry_duplication, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_queue_seperate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_duplication_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_category_duplication, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_dup_photo_duplication, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_brand_name_duplication, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_spu_id_v3_duplication, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_only_live_duplicate, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_pv_first_industry_filter_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_filter_native_seperate_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_second_industry_duplication_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_brand_name_duplication_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_category_duplication_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_spu_id_v3_duplication_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_first_industry_duplication, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_closd_search_retarget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_id_duplication_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cluster_id_duplication_v2, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_native_ad_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(search_retarget_min_cpm, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(dac_user_type, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_BOOL(enable_dpp_diversity_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_force_reco_ad_skip_dpp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_control_force_reco_dpp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_hard_split_dpp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_interest_diversity_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dpp_diversity_accept_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_feed_set_auto_play, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_ad_filter_jump_out, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(explore_feed_ad_filter_pp_jump_out, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(explore_inner_ad_filter_pp_jump_out, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_random_shield_limit_page, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_block_filter_jump_out, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ad_block_pp_jump_out, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(max_interest_diversity_score, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_BOOL(base_dpp_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_paid_duanju_skip_dpp_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_xifan_skip_dpp_filter, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_coin_shopping_around, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_impression_price, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_filter_cpm_exp, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_iaa_uplift_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_transport, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(default_iaa_bill_second, ks::AbtestBiz::AD_DSP, 26);
SPDM_ABTEST_STRING(defualt_iaa_bill_second_by_episode, ks::AbtestBiz::AD_DSP, "0-4:20,5-9:23");
SPDM_ABTEST_STRING(iaa_uplift_exp_tag,  ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_DOUBLE(user_explore_boost_rate_multi, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_xifan_pk, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_outer_live_skip_dpp_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_skip_dpp_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_benefit_as_dpp_similarity_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(user_explore_boost_rate_one, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(xifan_coin_shopping_around_thres, ks::AbtestBiz::XIFAN_PLAY_APPS, 80000);
SPDM_ABTEST_DOUBLE(user_explore_cvr_bid, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(user_explore_cvr_bid_mix, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_ctcvr_explore_skip_cpm_thr_boost_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adx_rta_retarget_bid_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(adx_rta_explore_bid_boost_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(adx_rta_explore_bid_boost_value, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(adx_rta_retarget_tags_front, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(high_pcvr_mechant_user_request_time_thd, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_high_pcvr_mechant_user_level_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_high_pcvr_boost_for_inner_loop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_spu_id_v3_filter_compat_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_spu_id_pos_in_rank_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_brand_name_pos_in_rank_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_third_category_pos_in_rank_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cluster_id_pos_in_rank_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_id_pos_in_rank_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_global_app_id_in_rank_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pv_sensitive_third_category_id_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(pv_sensitive_third_category_id_filter_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_ad_photo_md5_in_rank_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dup_photo_id_in_rank_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_rank_pv_filter_feed_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_rank_pv_filter_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_rank_pv_filter_thanos_mix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(user_explore_boost_value_one, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(user_explore_boost_value_multi, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(user_explore_cpm_lower_bound, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_search_series_card_fix_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fanstop_browse_info_days, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_hack_mix_server_show_rate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hack_mix_server_show_rate_by_ori, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_cart_item_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_click_after_recommend, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(server_client_show_rate_thanos, ks::AbtestBiz::AD_DSP, 2.7);
SPDM_ABTEST_DOUBLE(server_client_show_rate_nebula, ks::AbtestBiz::AD_DSP, 3.8);
SPDM_ABTEST_BOOL(enable_bad_photo_gather_taxes, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(inner_nobid_charge_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merge_negative_session, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_short_play_action, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_video_pay_charge_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(inner_cost_cap_charge_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_upgrade_behavior_interest, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_bouns_cost_real_time, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_rtb_bouns_upgrade, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_rtb_generic_cpm_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_profit_flow_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rtb_presonalized_upgrade, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_nobid_second_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_cost_cap_second_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kmovie_explore_flow_fix_vip_req_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(gmv_filter_status, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(refresh_count_redis_cache_timestamp, ks::AbtestBiz::AD_DSP, 7200);
SPDM_ABTEST_INT64(refresh_count_cache_ad_quota, ks::AbtestBiz::AD_DSP, 400);
SPDM_ABTEST_BOOL(enable_refresh_count_manage, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(hit_m2u_new_banner_ad_pos_skip, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_enhance_game_premiere, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_non_conversion_visitor_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ft_fairness_correction, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ft_fairness_correction_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ft_fairness_correction_skip_nobid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(enhance_game_premiere_default, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(enhance_game_premiere_max, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(enhance_game_premiere_min, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(ft_fairness_correction_p_max, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ft_fairness_correction_p_min, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_guided_survey_ab, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(max_hard_ad_force_reco_tag_size, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(max_native_ad_force_reco_tag_size, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(force_reco_rb_thrd, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_BOOL(enable_force_reco_rb_thrd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_same_second_industry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_force_reco_sort_by_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gpm_in_auction_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(max_user_request_history_info, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_BOOL(enable_rewarded_fanstop_hard_pk, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_invoked_product_retrieval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_live_skip_pos_select, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_force_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_app_detail_use_release, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_dpp_for_soft_hard_union, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_history_filter_id_unify, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_keep_web_view, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_merchant_exp_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_h5_doudi_for_app_advance, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_platform_new_customer_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hard_soft_quota_split, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(remove_mock_user_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rm_auction_p2p, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(diasble_unlogin_ann_prefetch_notify, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_incr_req_times, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_tag_remove, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_remove_detail_unit_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hard_soft_union, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hard_soft_union_nearby, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hard_soft_union_follow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_unify_pk, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(hard_soft_union_only_for_thanos_mix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(inner_explore_sort_ad_queue_with_mix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_select_mix_quoat_with_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_common_card_second_request, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_mix_quota_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_hard_min_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(inner_soft_min_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(inner_explore_mix_quota_thr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(mix_live_quota_with_explore, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(total_quota_for_mix_rank_with_explore, ks::AbtestBiz::AD_DSP, 12);
SPDM_ABTEST_INT64(mix_hard_quota_with_explore, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(mix_soft_quota_with_explore, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_mix_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_feature_prepare, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(auction_dpp_top_size, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_fill_fanstop_author, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_photo_id_in_forward, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_valid_flag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_tag_replace, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_again_photo_mix_request_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_app_detail, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_revenue_optimize, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(revenue_optimize_all_bid_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_revenue_optimize_price_jili, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hc_bonus_shift, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inspire_mix_pk_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inspire_soft_photo_pk_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(disable_splash_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_general_quota_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_auction_filter_plugin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(nebula_user_refresh_times_thrd, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(nebula_user_value_filter_auction_bid_thrd, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_calc_hidden_cost_plugin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(max_hard_quota, ks::AbtestBiz::AD_DSP, 8);
SPDM_ABTEST_INT64(max_soft_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(total_quota_for_mix_rank, ks::AbtestBiz::AD_DSP, 16);
SPDM_ABTEST_INT64(merchant_total_quota_for_mix_rank, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(inner_high_quality_soft, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(inner_soft, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(inner_soft_live, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(inner_soft_photo, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(inner_live_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(outer_high_quality_soft, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(outer_soft, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(outer_soft_live, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(outer_soft_photo, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(mix_hard_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(live_copy_refactor_type, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_live_copy_refactor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_copy_refactor_without_union, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_rank_inner_live_extra, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_rank_inner_live_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_front_max_quota_limit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_front_fill_full_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(max_inner_high_quality_soft, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_inner_soft, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_inner_soft_live, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_inner_soft_photo, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_outer_high_quality_soft, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_outer_soft, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_outer_soft_live, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_outer_soft_photo, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_inner_hard_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_outer_hard_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_all_hard_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_all_soft_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_all_live_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_inner_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_outer_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_BOOL(enable_hard_quota_split, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_hard_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outer_hard_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_inner_outer_quota_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outer_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_inner_live_exclude_fanstop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_photo_exclude_fanstop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(ad_select_quota_front_config_key, ks::AbtestBiz::AD_DSP, "front-base");
SPDM_ABTEST_STRING(exp_tag_in_rank_feature_test_for_hq, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(soft_hard_union_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_refresh_count_merge_posid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_refresh_count_merge_posid_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_cache_all_live_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(native_ad_reserve_price_thanos, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(native_ad_reserve_price_inspire, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(small_game_ad_force_direct_call_revert, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guided_feedback_style_id_1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guided_feedback_style_id_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_convert_exp_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guided_feedback_soft_ad_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_product_distinct_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_product_distinct_filter_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review_old, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_normal_fans_disable_ad_mark_without_plc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_flow_boost_ad_disable_ad_mark_with_whitelist, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(da_jian_kang_text, ks::AbtestBiz::AD_DSP, "成都磁力广告");
SPDM_ABTEST_BOOL(enable_esp_mobile_price_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_redis_universe_purchase_crowd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_archimedes_nebula_rpc_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_nobid_charge_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(inner_cost_cap_charge_ratio_upper, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(inner_live_nobid_charge_ratio_upper, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(merchant_video_pay_charge_ratio_upper, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(head_customer_author_seperate_discount_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(force_product_set_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(rank_dragon_update_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_wechat_new_product_auto_price_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(enhance_ecpm_strategy_default_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(inspire_video_discount_ratio_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_playlet_sdpa_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_reset_undiscount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_discount_by_hour_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_na_fiction_price_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_sdpa_discount_skip_mcb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_total_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_total_discount_sup_mcb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_train_smart_offer_realtime_feature, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_p2live_show_live_stream_1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_p2live_show_live_stream_13, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_hybrid_auction, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hybrid_auction_nobid_protect, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hybrid_auction_nobid_protect_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ad_soft_front_ad_score_weight_nebula, ks::AbtestBiz::AD_DSP, 0.08);
SPDM_ABTEST_DOUBLE(ad_soft_front_ad_score_weight_gamora, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(universe_agg_scene_discount_ratio, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_INT64(universe_agg_scene_ad_num, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_BOOL(enable_ub_discount_gfp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adload_control_native_ratio_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(adload_control_native_ratio_max, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(max_gsp_unify_billing_ratio, ks::AbtestBiz::AD_DSP, 5.0)
SPDM_ABTEST_INT64(match_server_req_mig_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(search_del_ad_server_stage, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(user_data_center_kv_config, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(merge_exp_to_adserer_timeout, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(mix_benefit_cpm_boost_key, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(mix_benefit_show_rate_key, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(mix_benefit_item_type_show_rate_key, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_cid_spu_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_spu_restart_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_corp_default_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cid_corp_default_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(cid_discount_ratio_lower, ks::AbtestBiz::AD_DSP, 0.515);
SPDM_ABTEST_DOUBLE(cid_discount_ratio_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(cid_quality_strategy_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(cid_spu_strategy_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_cid_quality_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_bid_boost_roas_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_global_ratio_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_cid_price_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_ab_global_ratio_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cid_ab_global_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_cid_account_zk_strategy_ocpx_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_price_record_rewrited, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_STRING(search_age_wise_throttling_group, ks::AbtestBiz::AD_DSP, "base");

SPDM_ABTEST_INT64(inspire_live_default_coin_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(guess_you_like_multi_return_num, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(cache_remove_live_type, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_universe_use_new_hash_did, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(splash_pic_show_time, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_INT64(splash_cache_limit, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(splash_combo_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(inspire_shake_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(splash_shake_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(splash_rotate_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(splash_slide_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(splash_combo2_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(fullscreen_combo3_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(inspire_combo3_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(interstitial_combo2_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(inspire_rotate_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(fullscreen_rotate_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(inner_photo_refresh_times_pos, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_new_direct_merchant_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(use_new_order, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_low_quality_stain_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_low_quality_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(low_quality_exp_tag, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_fix_native_origin_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_select_first_refresh_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_refresh_times_pos_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_merchant_retention_live_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_merchant_fill_browse_set, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_flow_optimization_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_biz_info_new_token, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_transparent_feed_mix_fields, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_feed_cpm_fields, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_fanstop_select_first_pos, ks::AbtestBiz::AD_DSP, 31);
SPDM_ABTEST_INT64(inner_fanstop_select_first_pos_in_target_city, ks::AbtestBiz::AD_DSP, 31);
SPDM_ABTEST_INT64(inner_fanstop_select_vv_step_in_target_city, ks::AbtestBiz::AD_DSP, 14);
SPDM_ABTEST_BOOL(enable_inner_universe_request_sessionSvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_merchant_guess_you_like_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_side_window_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_profile_skin_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_feed_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_ab_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_creator_profile_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_app_page_to_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inspire_live_order_award_default_value, ks::AbtestBiz::AD_DSP, 110);
SPDM_ABTEST_BOOL(enable_universe_inner_merchant_category_value, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_coudan_ads, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_realtime_high_gmv_author, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inner_explore_dsp_fixed_ad_pos, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(inner_nearline_exp_name, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(inner_explore_fanstop_fixed_ad_pos, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(feed_mix_ab_name, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_universe_inner_cpm_bound_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_forced_impression_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_product_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_product_discount_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(new_product_strategy_exp, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(buyer_home_page_multiple_ad_pos, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(guess_you_like_multi_pos, ks::AbtestBiz::AD_DSP, "5");
SPDM_ABTEST_INT64(page_size_thr_first, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(mix_rank_support_multiple_ad_num_for_inner_explore, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_multiple_ad_num_for_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_min_one_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(delivery_up_thr, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_BOOL(enable_delivery_upthr_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(add_rule_id_to_label_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_ad_billing_seperate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_native_ranking_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_native_pc_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_native_pc_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_native_mobile_direct_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_native_mobile_p2l, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_append_native_mobile_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(max_native_ranking_list_size, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(max_amd_photo_ranking_list_size, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(max_native_ranking_list_idx, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_amd_photo_ranking_list_idx, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_BOOL(enable_mobile_hard_p2l_price_uplimit_protect, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mobile_hard_live_price_uplimit_protect, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mobile_hard_p2l_price_uplimit_value, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mobile_hard_live_price_uplimit_value, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_user_rfm_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_photo_self_recall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(hard_selected_price_cail, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(native_thanos_explore_max_price, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_BOOL(native_ad_enable_native_price_autocpa_diff, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_shadow_cid_field, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_enable_append_splash_cid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(native_ad_bs_price_ratio_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_ad_bs_price_ratio_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_ad_billing_separate_additional_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(native_ad_bs_price_lower, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(native_ad_bs_price_upper, ks::AbtestBiz::AD_DSP, 100000);
SPDM_ABTEST_INT64(inner_loop_min_price, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_biz_extra_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_biz_extra_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_biz_extra_add_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_refund_request_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_bs_ab, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_bs_ab_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_skip_bs_ab, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_bs_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(storewide_live_bs_overcharge_ratio, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(storewide_live_bs_discount_ratio, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_BOOL(enable_mobile_price_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_author_pk, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(outer_author_pk_check, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(new_inner_fanstop_avoid_top_k, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(native_cpm_zoom_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_native_cpm_zoom_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_gpm_to_cpm_factor, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inspire_live_coin_roi,  ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(inspire_live_ctr_calibration_ratio,  ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(inspire_live_coin_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(mobile_price_discount_exp_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_INT64(inspire_live_custom_coin_lower, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(inspire_live_custom_coin_upper, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_BOOL(enable_merchant_request_brand_ads, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gyk_pk_with_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gyk_diff_product_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_photo_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(guess_like_photo_card_probability, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_user_local_tag_request_redis, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_user_risk_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_like_reset_product_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_v_card_decoupling, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adfront_force_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_enable_adfront_force_explore_gd_order, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_force_product_photo_explore_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_enable_adfront_force_explore_gd_disorder, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_local_life_request_redis, ks::AbtestBiz::AD_DSP);
// SPDM_ABTEST_BOOL(enable_industry_live_price_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enabel_photo_ceiling_front_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_shoufa_front_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_ad_inner_stream_fix_interval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_ad_inner_skip_rs, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_normal_hack_cpm_plugin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_all_normal_hack_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_normal_hack_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_boost_normal_mix_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_url_refactor_diff_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_schema_url_refactor_diff_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_url_refactor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_schema_url_refactor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_live_use_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_hosting_photo_to_redis, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(click_after_reco_commerce_weight,  ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(click_after_reco_rele_threshold,  ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(normal_hc_to_cpm_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_campaign_bid_price_ratio_lower, ks::AbtestBiz::AD_DSP, 0.9);
SPDM_ABTEST_DOUBLE(inner_campaign_bid_price_ratio_upper, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_STRING(server_show_rate_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(adload_control_rate_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(outer_fans_use_extra_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_mix_ad_inner_outer_flag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_good_soft_ad_new_standard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shop_data_filled, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(adx_shop_action_prefix, ks::AbtestBiz::AD_DSP, "adxhc");
SPDM_ABTEST_STRING(user_blue_sea_category_prefix, ks::AbtestBiz::AD_DSP, "blue_sea");
SPDM_ABTEST_STRING(ecology_score_prefix, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_change_to_double_col, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_change_to_double_col_for_merchant_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_change_to_double_col, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_smb_bid_unit_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smb_price_after_bs, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_smb_bid_unit_price_skip_all, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_smb_account_price_skip_all, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_price_discount_record_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smb_price_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smb_acccount_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smb_unit_over_charge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_new_customer_bs, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_new_customer_bs_backup, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_video_roas_achieve_ratio_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(new_cust_bs_weight_constant, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_bs_dynamix_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(commercial_user_group_prefix, ks::AbtestBiz::AD_DSP, "user_group_w");
SPDM_ABTEST_BOOL(enable_lpsdeep_support, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_acq_gen_score_tags, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(enable_lpsdeep_support_decay, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(feed_mix_score_conf_key, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_feed_mix_score_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pm_poi_search_user_ecpc, ks::AbtestBiz::AD_DSP);
// [zhaijianwei front]
SPDM_ABTEST_BOOL(enable_adjust_price_private_message, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(private_message_support_decay_group, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_adjust_price_simple_promotion, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(simple_promotion_support_decay_group, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_user_kgame_duration_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_kgame_pay_cnt_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_game_explore_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_remove_universe_cpm_floor_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_random_charge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_cpm_filter_whole, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_BOOL(enable_rewarded_ranking_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(rewarded_ranking_list_only_outer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_zhongcao_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nearby_inner_stream, ks::AbtestBiz::AD_DSP);
// [liyichen front]
SPDM_ABTEST_BOOL(enable_playlet_fan_author, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_item_impression_ts, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ud_paied_tube_trade_event_ts_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_judou_chaoxiaoe_no_cost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_chaoxiaoe_no_cbu, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_judou_cost_more, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dabao_chaoxiaoe_no_cost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dabao_cost_more, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(C_bu_min_filter_price, ks::AbtestBiz::AD_DSP, 300);
SPDM_ABTEST_BOOL(enable_cbu_exp_tail, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(chaoxiaoe_enable_cbu_exp_tail, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(tmp_offer_result, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(tmp_activity_interval, ks::AbtestBiz::AD_DSP, 86400000);
SPDM_ABTEST_INT64(tmp_offer_value, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_xifan_take_inner_live_stright, ks::AbtestBiz::XIFAN_PLAY_APPS);

// [wangxiaohu]
SPDM_ABTEST_BOOL(enable_xifan_skip_front_to_ad_pack, ks::AbtestBiz::XIFAN_PLAY_APPS);

// [jianghao07]
SPDM_ABTEST_BOOL(enable_nogap_filter_by_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(nogap_cpm_thr, ks::AbtestBiz::AD_DSP, 0.0);

// [qiaolin]
SPDM_ABTEST_BOOL(enable_nogap_filter_by_jumpout_rate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nogap_filter_by_jumpout_rate_bugfix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(nogap_jumpout_rate_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_nogap_filter_by_wgroup, ks::AbtestBiz::AD_DSP);

// [cuihongyi]
SPDM_ABTEST_BOOL(disable_maill_tab_ad_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_zhuanqian_tab_ad_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_buyer_home_ad_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_guess_you_like_ad_front, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_kxy_user_subsidies, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_increment_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_increment_price_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_black_game_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dark_acc_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_native_increment, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(disable_explore_feed_ad_front_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_explore_inner_ad_front_admit, ks::AbtestBiz::AD_DSP);

// [zhangwei26]
SPDM_ABTEST_BOOL(enable_kconf_author_id_redis_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kconf_author_id_redis_normal, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kconf_author_id_redis, ks::AbtestBiz::AD_DSP);

// [zhangxingyu03]
SPDM_ABTEST_BOOL(enable_iaa_bill_time_use_iaa_score_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_all_fill_stid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_coin_widget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(incentive_small_game_adless_countdown_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_incentive_deep_task_holdout, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_live_one_more, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_specific_pos_skip_pk, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_iaa_sensitive_user_bill_time_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(incentive_iaa_sensitive_user_bill_time_adjust_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_game_iaa_countdown_time_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(incentive_game_iaa_countdown_time, ks::AbtestBiz::AD_DSP, 20000);

// [xiongyajiao]
SPDM_ABTEST_BOOL(enable_unify_inner_fanstop_rate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_boost_for_inner_fanstop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(default_mix_weight_ratio_nebula, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(default_mix_weight_ratio, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(mix_weight_origin_ratio_native, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(mix_weight_origin_ratio_normal, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(mix_weight_origin_ratio_native_nebula, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(mix_weight_origin_ratio_normal_nebula, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(soft_hard_boost_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(soft_for_fanstop_boost_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_inner_fanstop_boost_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(mix_inner_fanstop_upper_bound, ks::AbtestBiz::AD_DSP, 10000000000000);

// [lichunchi]
SPDM_ABTEST_BOOL(enable_inner_buyer_industry_id_list, ks::AbtestBiz::AD_DSP);

// [wangkai26]
SPDM_ABTEST_DOUBLE(shelf_express_gyl_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(shelf_express_bh_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(shelf_express_mall_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(shelf_express_program_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(shelf_express_customize_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_new, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(bh_p2l_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mail_p2l_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(zq_p2l_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm_gyl, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm_mail, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_pk_with_gpm_bh, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_search_action_item, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(search_action_item_list_size, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_BOOL(enable_request_search_action_item_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_search_action_data, ks::AbtestBiz::AD_DSP);

// [zhaoyi13]
SPDM_ABTEST_BOOL(enable_fiction_user_interest_tags, ks::AbtestBiz::AD_DSP);

// [zengdi] cid 策略
SPDM_ABTEST_BOOL(enable_cid_use_goods_type_support, ks::AbtestBiz::AD_DSP);

// [jiyang]
SPDM_ABTEST_BOOL(enable_photo_pvtr_filter_sample, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(reduce_vtr_sigmoid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_rank_refactor_dot, ks::AbtestBiz::AD_DSP);

// [jiyang] 关注页透传 bonus 、 hc 到混排
SPDM_ABTEST_BOOL(enable_follow_opt_allow_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(follow_opt_allow_hc_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_follow_opt_allow_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(follow_opt_allow_bonus_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(follow_opt_allow_bonus_ratio_c, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(follow_opt_allow_bonus_ratio_b, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(follow_opt_allow_bonus_ratio_g, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_BOOL(enable_follow_opt_allow_bonus_level, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(target_bonus_l, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_BOOL(enable_photo_pvtr_filter_sample_tailcut, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_GamoraFollowFentiao, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_GamoraFollowOuter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_GamoraFollowInner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_GamoraFollowLiveInner1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_GamoraFollowLiveInner2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_GamoraFollowLiveInner3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_NebulaFollowInner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_NebulaFollowLiveInner2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_NebulaFollowSide, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_NebulaFollowTop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_NebulaFollowLiveInner1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_FollowAll, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_GamoraCitywideInner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_GamoraCitywideInoutInner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_GamoraCitywideTop5, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_NebulaCitywideInoutInner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_NebulaCitywideInoutOuter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_NebulaCitywideInoutOuter2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_NebulaCitywide, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_CitywideAll, ks::AbtestBiz::AD_DSP);

// [jiyang] 关注页跳过 dpp 过滤
SPDM_ABTEST_BOOL(enable_follow_gamora_outer_skip_dpp_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_gamora_inner_skip_dpp_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_nebula_skip_dpp_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(follow_ecpm_threshold_init_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(follow_ecpm_threshold_init_tag_2, ks::AbtestBiz::AD_DSP, "");
// [jiyang] 关注页跳过 DUPLICATED_AUTHOR_ID
SPDM_ABTEST_BOOL(enable_follow_skip_DUPLICATED_AUTHOR_ID, ks::AbtestBiz::AD_DSP);

// [jiyang] 付费短剧 feed 大卡
SPDM_ABTEST_BOOL(enable_playlet_adform_trans_feedcard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(playlet_adform_trans_feedcard_min_fresh_time, ks::AbtestBiz::AD_DSP, 1);

// [jiyang]
SPDM_ABTEST_BOOL(enable_follow_increase_mix_ecpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(follow_increase_mix_ecpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(follow_increase_mix_ecpm_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_follow_increase_mix_ecpm_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(follow_increase_mix_ecpm_ratio_2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(follow_increase_mix_ecpm_thr_2, ks::AbtestBiz::AD_DSP, 0.0);

// [jiyang]
SPDM_ABTEST_BOOL(enable_trans_field_to_nearby_mixrank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(nearby_mixrank_feed_boost_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(nearby_mixrank_thaos_boost_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(nearby_mixrank_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_trans_field_to_nearby_mixrank_v2, ks::AbtestBiz::AD_DSP);

// [jiyang] 内循环大卡二次请求
SPDM_ABTEST_BOOL(enable_skip_firstind_innerloopbigcard_secondreq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adjust_quota_innerloopbigcard_secondreq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(card_style_1_small_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(card_style_1_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(card_style_2_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_setfanstopamd_innerloopbigcard_secondreq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pk_innerloopbigcard_secondreq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pk_innerloopbigcard_secondreq_fix, ks::AbtestBiz::AD_DSP);

// [jiyang] 同城修复混排字段透传
SPDM_ABTEST_BOOL(enable_fix_trans_field_to_mixrank, ks::AbtestBiz::AD_DSP);

// [jiyang] 关注页接硬广
SPDM_ABTEST_BOOL(enable_follow_inner_add_hard_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_inner_add_hard_ad_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_increase_live_mix_ecpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(follow_increase_live_mix_ecpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_follow_hard_soft_merge_truncate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_hard_soft_merge_truncate_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_inc_soft_truncate_size, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(follow_inc_soft_truncate_size_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_close_follow_id_filter, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_STRING(cpm_thr_08_exp_name, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(followMixExpTag, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_BOOL(enable_follow_opt_allow_bonus_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(follow_bonus_target_level, ks::AbtestBiz::AD_DSP, "ALL");
SPDM_ABTEST_STRING(follow_mixrank_exp_name, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_BOOL(enable_live_independent_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(live_independent_quota, ks::AbtestBiz::AD_DSP, 3);

SPDM_ABTEST_BOOL(enable_sort_by_rankbenefit, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_FillChuangxinModelFeatureInfo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_add_chuangxin_model_feature, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_mix_rank_attr_to_rank_info_follow, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_pm_fwh_ee, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_follow_ad_merch_gpm, ks::AbtestBiz::AD_DSP);

// [jiangjinling]
SPDM_ABTEST_BOOL(enable_native_fiction_user_feature_collection, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_subsidy_smart_offer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_subsidy_total_new_pkg, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_report_native_fiction_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_enlarge_native_fiction_new_user_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(fiction_subsidy_conf_group_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_fiction_subsidy_white_list_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fiction_retarget_subsidy_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ratio_of_fiction_retarget_subsidy_discount, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_STRING(fiction_subsidy_delivery_freq_limit, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_fiction_subsidy_delivery_freq_limit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_subsidy_freq_limit_transfer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(fiction_subsidy_panel_show_style_group_key, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_INT64(fiction_old_customer_active_gap_day, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_BOOL(enable_fiction_subsidy_show_style_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fiction_retain_min_commodity_price_thresh, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(fiction_remit_freq_max_commodity_price_thresh, ks::AbtestBiz::AD_DSP, 1200);
SPDM_ABTEST_DOUBLE(fiction_delivery_freq_ltv_thresh, ks::AbtestBiz::AD_DSP, 0.7);
SPDM_ABTEST_BOOL(enable_fiction_subsidy_freq_limit_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_big_day_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_second_subsidy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_decide_fiction_dynamic_panel_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(fiction_dynamic_panel_price_group_key, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_fiction_subsidy_use_pay_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_smart_panel_price_v4_strategy_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fiction_smart_panel_price_v4_max_price, ks::AbtestBiz::AD_DSP, 29.9);
SPDM_ABTEST_DOUBLE(fiction_smart_panel_price_v4_min_price, ks::AbtestBiz::AD_DSP, 1.9);
SPDM_ABTEST_DOUBLE(fiction_smart_panel_price_v4_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(fiction_smart_panel_retain_price_v4_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_fiction_multi_style_panel_split_price_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fiction_smart_panel_price_v4_bias, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_fiction_panel_v4_subsidy_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_panel_v4_fixed_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_panel_v4_any_base_fixed_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(fiction_panel_v4_fixed_subsidy_ratio_group_key, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(fiction_panel_v4_fixed_subsidy_style_group_key, ks::AbtestBiz::AD_DSP, "roi_opt");
SPDM_ABTEST_BOOL(enable_cal_fiction_iap_ios_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_delete_fiction_panel_v3_code, ks::AbtestBiz::AD_DSP);


SPDM_ABTEST_STRING(fiction_dynamic_panel_subsidy_conf_key, ks::AbtestBiz::AD_DSP, "conf1");
SPDM_ABTEST_BOOL(enable_jump_fiction_panel_product_name_white, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_no_subsidy_forbid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(useNovelSubsidy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_price_map_panel_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_fiction_params_init, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_panel_v3_unify_ltv_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_iaa_ecpc_by_put_book_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_rct_subsidy_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fiction_rct_subsidy_ratio_upper_number, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_BOOL(enable_fiction_smart_panel_random_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fiction_random_panel_price_probability, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(fiction_random_retain_subsidy_panel_style_prob, ks::AbtestBiz::AD_DSP, 70);
SPDM_ABTEST_INT64(fiction_random_pv_subsidy_style_prob, ks::AbtestBiz::AD_DSP, 50);
SPDM_ABTEST_DOUBLE(default_fiction_iap_subsidy_ratio, ks::AbtestBiz::AD_DSP, 0.4);
SPDM_ABTEST_DOUBLE(default_nc_fiction_iap_subsidy_ratio, ks::AbtestBiz::AD_DSP, 0.7);
SPDM_ABTEST_BOOL(enable_fiction_iap_nc_explore_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_panel_v3_pay_rate_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(forbid_fiction_panel_big_day_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_big_day_exp_map_subsidy_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_app_na_explore_subsidy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_interest_user_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fiction_interest_user_discount, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(fiction_big_day_exp_map_config_key, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(fiction_ios_platform_tax_share_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(fiction_iap_panel_origin_price_weight, ks::AbtestBiz::AD_DSP, 2.5);
SPDM_ABTEST_INT64(fiction_iap_panel_origin_price_bias, ks::AbtestBiz::AD_DSP, 0);

// [zhaoyi13]
SPDM_ABTEST_BOOL(enable_fill_novel_unify_ltv, ks::AbtestBiz::AD_DSP);

// [yangxuan06]
SPDM_ABTEST_INT64(novelPanelSelectType, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(fiction_opt_panel_exp_key, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_kimi_user_ecpc_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_subsidy_type_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_na_book_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_iap_industry_explore, ks::AbtestBiz::AD_DSP);
// [nizhihao]
SPDM_ABTEST_DOUBLE(normal_cpm_zoom_factor_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(normal_cpm_zoom_factor_nebula_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_cpm_zoom_factor_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_cpm_zoom_factor_nebula_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_adload_control_transfer_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adjust_adload_rate_upper, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(client_cpm_sctr_ecpc_fix_main, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(client_cpm_sctr_ecpc_fix_nebula, ks::AbtestBiz::AD_DSP, 0.88);
SPDM_ABTEST_DOUBLE(adjust_adload_rate_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(server_show_rate_lower_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(server_show_rate_upper_v2, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(adload_control_rate_lower_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(adload_control_rate_upper_v2, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(mix_weight_lower_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_weight_upper_v2, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_BOOL(enable_new_hc_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_holdout_unify_server_show_rate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cid_holdout_unify_server_show_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_small_app_direct_jump, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_jifeibi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_normal_ProtectPrice, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sctr_ratio_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mix_cpm_to_bonus_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(bid_optim_ratio_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(bid_optim_ratio_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_request_ad_wise_user_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_merchant_user_group_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_user_score_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_game_ad_free, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_big_r_explore_redis, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(game_big_r_explore_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(game_big_r_explore_prefix, ks::AbtestBiz::AD_DSP, "bigr");
SPDM_ABTEST_BOOL(enable_bid_optim_redis_request, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bid_optim_param_parse, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_admit_bid_optim_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_c_subsidy_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(iaa_game_ad_free_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_ad_free_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(ad_wise_user_group_prefix, ks::AbtestBiz::AD_DSP, "cpm");
SPDM_ABTEST_STRING(mini_game_c_subsidy_group_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(merchant_user_group_tag_prefix, ks::AbtestBiz::AD_DSP, "merchant");
SPDM_ABTEST_STRING(rta_user_score_prefix, ks::AbtestBiz::AD_DSP, "rta");
SPDM_ABTEST_STRING(iaa_game_ad_free_prefix, ks::AbtestBiz::AD_DSP, "adfree");
SPDM_ABTEST_STRING(bid_optim_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(mix_unify_cpm_ratio_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_game_stability_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reset_mini_c_subsidy_record, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_bigcustomer_conf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_admit_zero_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_multi_head_judge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(mini_game_subsidy_strategy_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(mini_game_seven_subsidy_strategy_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_DOUBLE(game_stability_lower_bound, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(game_stability_upper_bound, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(default_iaa_game_ad_free_ecpm, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_BOOL(enable_kuai_game_feed_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kuai_game_skip_qpon, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kuai_game_age_constraint, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kuai_game_platform_version_constraint, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kuai_game_whitelist_constraint, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kuai_game_device_version_constraint, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kuai_game_isunlogin_constraint, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(mini_game_second_subsidy_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(mini_game_second_subsidy_group_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_INT64(mini_game_second_subsidy_style, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_mini_game_second_subsidy_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_second_subsidy_admit_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_second_subsidy_record, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_fix_amount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_pv_random_subsidy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_calculate_real_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mini_game_real_value_alpha, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mini_game_real_value_beta, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(mini_game_real_value_lower_bound, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(mini_game_real_value_upper_bound, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(mini_game_pv_random_thrd, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_INT64(mini_game_smart_subsidy_lower_bound, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_BOOL(enable_mini_game_calculate_real_ltv_by_conv_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_strategy_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_calc_price_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_calc_price_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reset_mini_game_smart_subsidy_record, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mini_game_smart_subsidy_constraint, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_DOUBLE(mini_game_7r_smart_subsidy_constraint, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_DOUBLE(mini_game_subsidy_percentage, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_INT64(mini_game_smart_subsidy_upper_bound, ks::AbtestBiz::AD_DSP, 10000000);
SPDM_ABTEST_INT64(mini_game_smart_subsidy_lower_bound_v2, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mini_game_subsidy_lower_thrd, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(mini_game_subsidy_upper_thrd, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_INT64(mini_game_subsidy_amount_bias, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_DOUBLE(mini_game_subsidy_amount_lower, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(mini_game_subsidy_amount_upper, ks::AbtestBiz::AD_DSP, 300);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_with_t0, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mini_game_subsidy_percentage_below_real_ltv, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(mini_game_subsidy_percentage_above_real_ltv, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_constraint, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mini_game_subsidy_price_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mini_game_subsidy_price_bias, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(mini_game_price_upper_constraint, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_price_strategy_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_big_r_explore_redis_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(game_big_r_explore_admit_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(game_big_r_explore_prefix_v2, ks::AbtestBiz::AD_DSP, "bigrv2");
SPDM_ABTEST_BOOL(enable_mini_game_smart_subsidy_freq_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mini_game_smart_subsidy_remain_freq_count, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_mini_game_c_subsidy_freq_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mini_game_c_subsidy_remain_freq_count, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_big_r_front_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_sdk_add_log, ks::AbtestBiz::AD_DSP);
// [nizhiihao end]

// [yangzhao07 start]
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_price_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mini_game_adjust_subsidy_amount_bias, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(mini_game_adjust_subsidy_amount_w, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(mini_game_adjust_subsidy_amount_gap, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(mini_game_discount_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(mini_game_discount_ratio_upper_bound_v2, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_INT64(mini_game_smart_subsidy_upper_bound_high, ks::AbtestBiz::AD_DSP, 10000000);
SPDM_ABTEST_INT64(mini_game_smart_subsidy_lower_bound_high, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(mini_game_subsidy_amount_bias_high, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(mini_game_7r_subsidy_amount_bias, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_mini_game_uniform_discount_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_uniform_discount_strategy_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mini_game_real_price_lower_bound, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_DOUBLE(mini_game_smart_subsidy_constraint_high, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost_new_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_first_day_subsidy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_boost_new_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_iaa_with_industry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(iaa_conv_pay_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(7r_iaa_conv_pay_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_roi_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_history_conv_non_csubsidy, ks::AbtestBiz::AD_DSP);  // 历史激活准入 首日
SPDM_ABTEST_BOOL(enable_mini_game_adroas_iaap_csubsidy, ks::AbtestBiz::AD_DSP);       // 优化目标准入 首日
SPDM_ABTEST_STRING(mini_game_c_subsidy_group_tag_new, ks::AbtestBiz::AD_DSP, "default");  // 定价 tag 首日
SPDM_ABTEST_STRING(mini_game_seven_c_subsidy_group_tag_new, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_DOUBLE(mini_game_discount_ratio_low_bound_new, ks::AbtestBiz::AD_DSP, 0.1);  // 折扣率下限 首日
SPDM_ABTEST_DOUBLE(mini_game_discount_ratio_up_bound_new, ks::AbtestBiz::AD_DSP, 1.0);  // 折扣率上限 首日
SPDM_ABTEST_BOOL(enable_mini_game_roi_subsidy, ks::AbtestBiz::AD_DSP);                    // roi 补贴 首日
SPDM_ABTEST_BOOL(enable_mini_game_bias_subsidy, ks::AbtestBiz::AD_DSP);  // bias 补贴开关 首日
SPDM_ABTEST_BOOL(enable_mini_game_first_bias_subsidy, ks::AbtestBiz::AD_DSP);  // bias 补贴开关 首日
SPDM_ABTEST_BOOL(enable_mini_game_csubsidy_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_adroas_sevenday_iaap_csubsidy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_pay_ltv_dingjia, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_first_pay_ltv_dingjia, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_discount_ratio_dingjia, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_first_discount_ratio_dingjia, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mini_game_7r_real_value_alpha, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(day_index_discount_ratio_delta, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(pay_cnt_discount_ratio_delta, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_mini_game_random_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_smart_7r_subsidy_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_first_roi_subsidy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(mini_game_first_bias_subsidy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_random_discount_ratio, ks::AbtestBiz::AD_DSP);
// [yangzhao07 end]

SPDM_ABTEST_BOOL(enable_explore_feed_w_user_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_inner_w_user_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_min_page_size_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_explore_min_page_size_admit, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(inner_explore_fixed_page_size_admit, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(mini_game_redis_period, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_set_mini_game_buyer_style, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_c_subsidy_transfer_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_c_subsidy_admit_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_c_subsidy_transfer_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_offline_fiction_subsidy_uplift_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_live_big_r_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_uplift_subsidy_style, ks::AbtestBiz::AD_DSP);

// [zengjiangwei03]
SPDM_ABTEST_BOOL(enable_bh_u4_mix_benefit_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(bh_u4_mix_benefit_discount_ratio_item, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(bh_u4_mix_benefit_discount_ratio_live, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_request_merchant_good_show_item, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(merchant_good_show_item_list_size, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_BOOL(enable_request_good_search_item, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(good_search_item_list_size, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_BOOL(disable_gyl_item_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_mall_item_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_zq_item_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_bh_item_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_gyl_live_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_mall_live_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_bh_live_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_zq_live_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_bid_to_mix_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_increase_mall_mix_rank_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mall_mix_rank_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_increase_bh_mix_rank_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(bh_mix_rank_quota, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_increase_gl_mix_rank_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(gl_mix_rank_quota, ks::AbtestBiz::AD_DSP, 2);

// [chenziping]
SPDM_ABTEST_BOOL(enable_calc_impress_pos_mix_benefit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_impress_pos_mixbenefit_coef, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_impress_pos_predict_cpm, ks::AbtestBiz::AD_DSP);

// [xiaoyuhao]
SPDM_ABTEST_BOOL(enable_front_request_rank_select_one_more, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFrontRequestRankSelectOneMore);
SPDM_KCONF_INT64(ad.adFrontDiffSwitches, requestRankSelectOneTimes, 1);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetAdxThirdConvertInfo);
SPDM_ABTEST_BOOL(enable_front_skip_ps_prepare, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableMerchantAutoDeliverType);
SPDM_ABTEST_BOOL(enable_get_router_user_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_send_user_feature_to_adserver, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableGetRouterUserInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSendUserFeatureToAdServer);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSendUserFeatureOnlyBs);
SPDM_ABTEST_BOOL(enable_guess_like_change_interactive_form, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAlwaysLogSetRerankReqInfo);
SPDM_ABTEST_BOOL(enable_unify_grid_unit_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_ad_match_server, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(nearline_recall_front_processor_flag, ks::AbtestBiz::AD_DSP, 0);
SPDM_KCONF_INT64(ad.adFrontDiffSwitches, orientationTruncateSize, -1);
SPDM_ABTEST_BOOL(enable_user_orientation_truncate, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUserOrientationTruncate);
SPDM_ABTEST_STRING(user_orientation_truncate_exp_group, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_get_reco_user_info_from_clotho, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableGetRecoUserInfoFromClotho);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableRecoUserInfoCmpDiff);
SPDM_ABTEST_BOOL(disable_inspire_get_reco_user_info, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFrontTrafficCopy);
SPDM_ABTEST_BOOL(enable_front_traffic_copy, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFrontTrafficCopyCache);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSetIaaAdPlayType);

// [jinhui05]
SPDM_ABTEST_BOOL(enable_side_window_user_group_w_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(follow_max_price, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_INT64(follow_fanstop_v2_max_price, ks::AbtestBiz::AD_DSP, 8000);
SPDM_ABTEST_BOOL(disable_follow_cost_ratio_account_black_list, ks::AbtestBiz::AD_DSP);
// [jinhui05] 内流 trigger item 对应的河图标签
SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_wanhe, ks::AbtestBiz::AD_DSP);
// [jinhui05] 发现页内流打折计费对齐
SPDM_ABTEST_BOOL(enable_adjust_price_explore_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(gamora_model_sctr_price_correct_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_dsp_live_price_correct_ratio_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gamora_mix_softad_reprice_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gamora_mix_soft_live_price_correct_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(adjust_price_explore_inner_rel_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(adjust_price_explore_inner_abs_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// [jinhui05] 主站发现页内流混排特殊处理
SPDM_ABTEST_BOOL(enable_inner_explore_mix_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_explore_mix_soft_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_explore_mix_hard_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_inner_explore_mix_ratio_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_explore_mix_ratio_soft_live, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_explore_mix_ratio_soft_photo, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_explore_mix_ratio_hard_live, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_explore_mix_ratio_hard_photo, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_inner_explore_new, ks::AbtestBiz::AD_DSP);
// [duanxinning] 主站发现页内流接入直播
SPDM_ABTEST_BOOL(enale_inner_explore_live_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enale_inner_explore_live_soft_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_live_extra, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_live_item_as_p2l, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inner_explore_live_score_conf_key, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(fountain_mix_ad_fans_cpm_boost_weight, ks::AbtestBiz::AD_DSP, 1.6);
SPDM_ABTEST_DOUBLE(fountain_mix_auction_top_fans_weight, ks::AbtestBiz::AD_DSP, 1.15);
SPDM_ABTEST_DOUBLE(fountain_mix_ad_dsp_eco_weight, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(fountain_mix_ad_top_fans_eco_weight, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(fountain_mix_ad_live_score_alpha, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(fountain_mix_ad_live_score_bias, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(fountain_mix_ad_live_score_pow, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_inner_explore_mix_live_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_explore_live_mix_quota, ks::AbtestBiz::AD_DSP, 1);
// [duanxinning] 主站发现页内流 rerank
SPDM_ABTEST_BOOL(enable_inner_explore_fairness_rerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_fairness_with_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inner_explore_fairness_rerank_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_inner_explore_ecpm_rerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inner_explore_ecpm_rerank_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_inner_explore_ecpm_rerank_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_rerank_add_dims, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_prerank_trigger_relative_score, ks::AbtestBiz::AD_DSP);
// 发现页内流体验指标
SPDM_ABTEST_BOOL(enable_inner_explore_mix_experience_value, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(explore_mix_experience_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_explore_post_gpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_inner_explore_relative_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(skip_inner_explore_ad_gap_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(skip_feed_explore_fanstop_pk, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(skip_feed_explore_fanstop_pk_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(skip_inner_explore_fanstop_pk, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_explore_mix_price_adjust_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 0.2);

SPDM_ABTEST_BOOL(enable_high_ue_explore_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(disable_ad_mark_ad_default_pos, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(disable_ad_mark_ad_quota_gomara, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(disable_ad_mark_ad_quota_nebula, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_disable_ad_mark_ad_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_price_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fanstop_price_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(fanstop_price_ratio_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(fanstop_global_price_ratio, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(fanstop_nobid_bs_ratio_upper, ks::AbtestBiz::AD_DSP, 50.0);
SPDM_ABTEST_DOUBLE(fanstop_nobid_bs_ratio_lower, ks::AbtestBiz::AD_DSP, 0.02);
SPDM_ABTEST_BOOL(enable_fanstop_prms_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_bs_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_fanstop_temu_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_esp_self_service_mix_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_industry_ad_inner_outer_flag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_phone_call_use_outer_auction_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_specific_bs_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fanstop_bs_price_ratio_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(fanstop_bs_price_ratio_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_fanstop_billing_on_cost_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(user_group_server_show_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(server_client_show_rate_soft_upper, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_BOOL(disable_kuaishou_splash_rtb_ads, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_kuaishou_nebula_splash_rtb_ads, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_like_product_detail_category, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_good_native_photo_new_standard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(native_outer_make_up_gpm_factor, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_keep_mix_rank_bid_order, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_buttom_bar_no_ads, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_power_boost_mix_rank_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mix_rank_bid_power_ratio_ub, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_side_window_author_uplift_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_charge_action_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(bh_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mall_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(zq_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(bh_item_card_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mall_item_card_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(zq_item_card_price_discount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_ecom_ranking_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_speed_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(item_card_speed_test_mix_rank_ratio, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_buyer_home_page_mix_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(buyer_home_page_mix_rank_live_card_weight, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(buyer_home_page_mix_rank_item_card_weight, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(buyer_home_page_mix_rank_live_card_weight_new, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(buyer_home_page_mix_rank_item_card_weight_new, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_inner_explore_mix_hc_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_watch_video_mission_use_page_size, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_multi_ad_use_page_size, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(wanhe_rerank_opt_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_inner_explore_mix_cpm_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inner_explore_mix_cpm_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_exposed_qcpx_has_qpon, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_qcpx_shelf_auction_operator, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(extra_timeout_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_invo_traffic_inner_ranking_list_reids, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invo_traffic_rank_redis_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_trigger_item_hetu_emb_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(invo_traffic_rank_redis_nums, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_DOUBLE(wanhe_hard_pk_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(wanhe_max_price, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_BOOL(enable_wanhe_charge_action_type_with_fanstop, ks::AbtestBiz::AD_DSP)
SPDM_ABTEST_BOOL(enable_fix_splash_purchase_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ud_duanju_user_force_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_request_eds, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(wanhe_fanstop_v2_max_price, ks::AbtestBiz::AD_DSP, 8000);
SPDM_ABTEST_STRING(side_window_author_uplift_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(mix_rank_bid_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_keep_mix_rank_bid_order_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pred_target_cost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dnc_cem_base_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_imp_info_check_size, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_bid_user_info_get_by_did, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_high_mix_bid_ad_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(high_mix_bid_ad_discount_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_search_query_norm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_unify_gpm_pass, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mix_unify_gpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(small_game_ad_force_direct_call_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_bid_discount_exp_new_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_style_form, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_mall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_zhuanqian, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_buyer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_guess, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_gsp_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_gsp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hard_gsp_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(price_protect_lower_bound, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(price_ratio_lower_v2, ks::AbtestBiz::AD_DSP, 0.52);
SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_BOOL(enable_request_user_imp_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_bonus_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mix_bonus_adjust_enhance_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(mix_unify_score_exp_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_unify_score_exp_ratio_a, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_unify_score_exp_ratio_b, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_unify_cpm_exp_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_unify_cpm_ratio_inner, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_unify_cpm_ratio_outer, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_unify_gpm_exp_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_unify_bonus_exp_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(disable_adload_control_rate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_block_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ad_block_pp, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(client_cpm_sctr_ratio_main, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(client_cpm_sctr_ratio_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_search_dup_between_inner_stream, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_search_ad_mark_by_review_status, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_in_fold_phone, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_brand_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_innerloop_userdata, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_phone_material_fill, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kuaishou_deep_inspire, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_native_into_follow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_relative_photo_id_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wanhe_charge_action_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_profile_relation_link_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nebula_novel_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kuaishuo_novel_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_video_income_task_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_draw_flow_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_holdout_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_xifan_req_inner_loop, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_inspire_req_inner_loop, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_feed_req_inner_loop, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_inspire_inner_loop_version_control, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_restrict_ad_num, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_restrict_ad_num_new, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_inner_loop_sort, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_skip_zijianzhan, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_INT64(mall_tab_multi_return_num, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(zhuanqian_tab_multi_return_num, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_photo_id_string_to_int, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_live_inner_ad_kuaishou, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_live_inner_ad_nebula, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pass_is_kol_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_no_tag_ad_standard_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mtb_unify_gpm_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_add_multi_head_mix_gpm_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_add_multi_head_mix_gpm_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_multi_head_mix_gpm_outer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_unify_gpm_min_value, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mix_unify_gpm_min_value, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(mtb_unify_gpm_cali_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(user_data_center_history_gmv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(user_data_center_playlet_offer_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(user_data_colossus_order_leaf_category, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(user_data_center_bigr_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merge_fanstop_session_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merge_virtual_session_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merge_fanstop_browsed_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_clotho_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(colossus_auto_live_item_list_size, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_BOOL(enable_mtb_v2_bonus_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mtb_v2_bonus_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_mtb_v2_hc_gpm_using_max, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_unify_bonus_info_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_replace_mix_bonus_with_cpm_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(replace_mix_bonus_with_cpm_ratio_inner_explore, ks::AbtestBiz::AD_DSP, 0.15);
SPDM_ABTEST_BOOL(enable_mtb_v2_inner_bonus_include_hc_gpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_core_user_server_show_ratio_upper_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(core_user_server_show_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_record_search_budget_in_universe, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_record_search_budget_in_universe_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_quick_search_skip_budget_control_universe, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_direct_search_skip_budget_control_universe, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(smart_offer_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_smart_offer_exp_tag_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(smart_offer_exp_tag_for_multi_head, ks::AbtestBiz::AD_DSP, "offer_value_multi_uplift_2h");
SPDM_ABTEST_STRING(smart_offer_exp_tag_for_two_head, ks::AbtestBiz::AD_DSP, "offer_value_national_day_v12");
SPDM_ABTEST_STRING(normal_smart_offer_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_ranking_cache_read_plugin_types, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_ranking_cache_write_plugin_types, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(C_activity, ks::AbtestBiz::AD_DSP, "ActivityA");
SPDM_ABTEST_DOUBLE(smart_offer_lower_bound_pay, ks::AbtestBiz::AD_DSP, 2.9);
SPDM_ABTEST_DOUBLE(smart_offer_lower_bound_offer, ks::AbtestBiz::AD_DSP, 2.9);
SPDM_ABTEST_DOUBLE(smart_offer_upper_bound_pay, ks::AbtestBiz::AD_DSP, 25.0);
SPDM_ABTEST_DOUBLE(smart_offer_upper_bound_offer, ks::AbtestBiz::AD_DSP, 25.0);
SPDM_ABTEST_DOUBLE(smart_offer_default_roi, ks::AbtestBiz::AD_DSP, 3.0);
SPDM_ABTEST_DOUBLE(smart_offer_min_pay_amount, ks::AbtestBiz::AD_DSP, 7.9);
SPDM_ABTEST_INT64(smart_offer_lessons_adjust, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(smart_offer_coins_adjust, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(retention_discount, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(retention_subsidy, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(pay_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(stay_retention_discount, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(stay_retention_subsidy, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(stay_pay_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(watch_task_checkpiont, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(watch_task_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(watch_task_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(C_all_subsidy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_second_price_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_early_return_offers, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(C_second_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_unify_ltv_value, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(Paid_retention, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(Stay_retention, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_watch_task_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_watch_task, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_change_one_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nearby_ad_by_req_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_buyer_home_request_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mall_tab_request_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mall_tab_request_live_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_mix_rank_req, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_like_mix_rank_req_subpage, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_live_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_live_card_product, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_live_card_other, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_new_merchant_gpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wechat_customer_acq_direct_jump, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_industry_playlet_sdpa_p2p, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_clear_playlet_sdpa_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enbale_use_new_total_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(max_soft_quota_new, ks::AbtestBiz::AD_DSP, 8);
SPDM_ABTEST_INT64(max_hard_quota_new, ks::AbtestBiz::AD_DSP, 8);
SPDM_ABTEST_BOOL(enable_commerical_independ_rank_gamora, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_commerical_independ_rank_nebula, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(enable_rank_migration_stage_default, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(enable_rank_migration_stage_search, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(enable_rank_migration_stage_splash, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(rank_migration_ad_server_timeout_adjust_default, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(rank_migration_ad_server_timeout_adjust_search, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(rank_migration_ad_server_timeout_adjust_splash, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(rank_migration_ad_rank_timeout_adjust_default, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(rank_migration_ad_rank_timeout_adjust_search, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(rank_migration_ad_rank_timeout_adjust_splash, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(merge_exp_to_adrank_timeout, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adrank_extra_timeout_for_refactor, ks::AbtestBiz::AD_DSP, 0);
// chenxian
SPDM_ABTEST_BOOL(enable_adjust_cbu_roi_by_unify_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_stop_cbu_by_unify_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(unify_ltv_range_for_cbu, ks::AbtestBiz::AD_DSP, 100000.0);
SPDM_ABTEST_STRING(adjust_unify_ltv_range, ks::AbtestBiz::AD_DSP, "20-30,5;30-40,6;40-1000,100");
SPDM_ABTEST_STRING(random_playlet_smart_offer_info,
            ks::AbtestBiz::AD_DSP, "3.9,20,1.5;4.9,20,1.5;5.9,20,1.5");
SPDM_ABTEST_STRING(random_playlet_smart_offer_info_dnc,
            ks::AbtestBiz::AD_DSP, "2.9,1.9,15;2.9,1.9,20;3.9,1.9,20;4.9,1.9,20;5.9,2.9,20;5.9,2.9,25;7.9,3.9,20;7.9,3.9,35;8.9,3.9,20");  // NOLINT
SPDM_ABTEST_STRING(random_playlet_smart_offer_info_doc,
            ks::AbtestBiz::AD_DSP, "2.9,1.9,15;2.9,1.9,20;3.9,1.9,20;4.9,1.9,20;5.9,2.9,20;5.9,2.9,25;7.9,3.9,20;7.9,3.9,35;8.9,3.9,20");  // NOLINT
SPDM_ABTEST_BOOL(enable_random_playlet_smart_offer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_random_cbu_skip, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_random_cbu_skip_client, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(random_playlet_smart_offer_ratio_doc, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(random_playlet_smart_offer_ratio_dnc, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_BOOL(enable_random_stop_playlet_smart_offer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_smart_offer_default_by_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(random_stop_playlet_smart_offer_ratio_doc, ks::AbtestBiz::AD_DSP, 6);
SPDM_ABTEST_INT64(random_stop_playlet_smart_offer_ratio_dnc, ks::AbtestBiz::AD_DSP, 6);
SPDM_ABTEST_BOOL(enable_playlet_chaoxiaoe_fix_init, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_chaoxiaoe_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(playlet_chaoxiaoe_stop_cbu_panel_prices, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(playlet_chaoxiaoe_stop_cbu_lesson_prices, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(playlet_chaoxiaoe_panel_info,
            ks::AbtestBiz::AD_DSP, "0,100.0,0.3,0.35,0.5;0,3.0,0.2,0.35,0.75;0.0,3.0,0.4,0.5,0.75");

// [均衡补贴]
SPDM_ABTEST_BOOL(enable_bonus_amount_coef_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_write_bonus_amount_coef_2_ad_delivery_info, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_switch_nc_user_tag, ks::AbtestBiz::AD_DSP);
// 漫剧
SPDM_ABTEST_BOOL(enable_switch_cartoon_user_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_trans_cartoon_user_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cartoon_cbu_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_switch_cartoon_dnc_independent_smart_offer, ks::AbtestBiz::AD_DSP);
// [chenxian] 番茄策略
SPDM_ABTEST_BOOL(enable_tomato_playlet_skip_cbu, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tomato_playlet_no_cbu_bonus, ks::AbtestBiz::AD_DSP);
// [chenxian] c 补 模型配置
SPDM_ABTEST_BOOL(enable_smart_offer_two_head_uplift_model_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_skip_all_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_model_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_normal_smart_offer_multi_uplift_model_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_dnc_default, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_dnc_no_multi_cbu, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(multi_uplift_model_cbu_value_dnc_bonus, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(multi_uplift_model_cbu_value_doc_bonus, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(unify_multi_uplift_model_cbu_value_bonus, ks::AbtestBiz::AD_DSP, 1.0);

// [chenxian] 漫剧新用户策略
SPDM_ABTEST_BOOL(enable_cartoon_product_name_kconf_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cartoon_dnc_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cartoon_dnc_offer_sub_one, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cartoon_dnc_max_offer_result, ks::AbtestBiz::AD_DSP, 1000.0);
SPDM_ABTEST_BOOL(enable_cartoon_offer_result_plus_one_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cartoon_offer_result_plus_num, ks::AbtestBiz::AD_DSP, 1.0);
// [chenxian] 多面板优选
SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_with_cbu, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_all_mode, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_default, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_random, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_multi_panel_select_by_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(playlet_multi_panel_ltv_range_info,
            ks::AbtestBiz::AD_DSP, "0,4,0,5;4,10,5,10;10,20,10,20;20,1000,20,1000");
SPDM_ABTEST_STRING(playlet_multi_panel_ltv_range_info_judou,
            ks::AbtestBiz::AD_DSP, "0,4,0,5;4,10,5,10;10,20,10,20;20,1000,20,1000");
SPDM_ABTEST_INT64(enable_playlet_multi_panel_min_template_id, ks::AbtestBiz::AD_DSP, 0);
// [chenxian] 鲜花绿叶，平台推荐价
SPDM_ABTEST_BOOL(enable_playlet_smart_offer_green_leaf_panel, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_green_leaf_panel_skip_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_green_leaf_panel_differentiate_coins, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_green_leaf_panel_adjust_cbu, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(green_leaf_panel_adjust_cbu_bonus, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(playlet_smart_offer_dnc_green_leaf_panel_info,
                                ks::AbtestBiz::AD_DSP, "390,990;7,19");  // dnc 面板价 * 100 ; 打包剧集数
SPDM_ABTEST_STRING(playlet_smart_offer_doc_green_leaf_panel_info,
                                ks::AbtestBiz::AD_DSP, "390,990;7,19");  // doc 面板价 * 100 ; 打包剧集数
SPDM_ABTEST_INT64(playlet_smart_offer_green_leaf_price_coefficient, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_STRING(playlet_smart_offer_coins_green_leaf_panel_info,
            ks::AbtestBiz::AD_DSP, "1990,2990,3990,6990,9990;1990,2990,3990,6990,9990");  // 剧豆面板
SPDM_ABTEST_STRING(playlet_smart_offer_ltv_green_leaf_panel_info,
                                ks::AbtestBiz::AD_DSP, "0-5@390,990;7,19|0-5@390,990;7,19|0-5@390,990;7,19");
SPDM_ABTEST_BOOL(enable_green_leaf_panel_select_by_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_green_leaf_panel_switch_industry_pay_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(playlet_smart_offer_ltv_green_leaf_panel_info_for_coin,
        ks::AbtestBiz::AD_DSP, "0-10000@1990,2990,3990,6990,9990,19900;60,50,40,30,20,10|10000-500000@390,990;7,19"); // NOLINT
SPDM_ABTEST_STRING(playlet_cbu_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(playlet_smart_offer_ltv_green_leaf_adjust_ratio,
                                ks::AbtestBiz::AD_DSP, "0-5@1.0|5-7@1.0");
SPDM_ABTEST_BOOL(enable_green_leaf_panel_adjust_customer_panel, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_all_panel_adjust_customer_panel, ks::AbtestBiz::AD_DSP);
// [chenxian]
SPDM_ABTEST_BOOL(enable_set_playlet_series_info_v2, ks::AbtestBiz::AD_DSP);

// [liyuanqing]
SPDM_ABTEST_BOOL(enable_random_user_playlet_smart_offer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_random_user_playlet_skip_cbu, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(user_playlet_smart_offer, ks::AbtestBiz::AD_DSP, "2.9,2.9,20");
SPDM_ABTEST_INT64(playlet_user_tag_id, ks::AbtestBiz::AD_DSP, 1);

// 混排 bonus 平衡性优化
SPDM_ABTEST_BOOL(enable_bonus_balence_stategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bonus_sep_inner_outer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cpm_bonus_cali_ratio_inner, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(cpm_bonus_cali_extra_ratio_inner, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(cpm_bonus_cali_ratio_outer, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(cpm_bonus_cali_extra_ratio_outer, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(cpm_bonus_cali_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(cpm_bonus_cali_extra_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_ue_user_strategy_tag_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_inner_user_plateco_info, ks::AbtestBiz::AD_DSP);

// 混排 w5 人群 bonus 策略优化
SPDM_ABTEST_DOUBLE(w5_extra_3_percent_bonus_cpm_lower_bound, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(w5_extra_3_percent_bonus_cpm_upper_bound, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_w5_add_3_percent_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_w5_add_3_percent_bonus_max, ks::AbtestBiz::AD_DSP);
// unify_cpm 关闭其它变化开关
SPDM_ABTEST_BOOL(enable_close_unify_cpm_add_customer_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_unify_cpm_divide_sctr_ratio, ks::AbtestBiz::AD_DSP);

// 根据混排上一刷结果调整 bonus
SPDM_ABTEST_BOOL(enable_move_mix_rank_input_hist_page_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adjust_bonus_with_mix_rank_input_use_two_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(bonus_weight_for_rb_thr_to_add_bonus, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(adjust_bonus_with_mix_rank_input_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(minus_bonus_with_mix_rank_input_weight, ks::AbtestBiz::AD_DSP, 1.0);
// 所有广告豁免客户端过滤
SPDM_ABTEST_BOOL(enable_all_ad_retain_top, ks::AbtestBiz::AD_DSP);

// 头客追击-破圈 E-E 方案
SPDM_ABTEST_DOUBLE(po_quan_price_discount_extra_discount, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(po_quan_price_discount_min_discount, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_touke_user_impression_city_product_id_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(po_quan_strategy_versions, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_playlet_item_purchase_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(po_quan_price_discount_product_name_extra_discount, ks::AbtestBiz::AD_DSP, 1.0);
// 外循环不顶价策略
SPDM_ABTEST_BOOL(enable_outerloop_gsp_skip_same_agent_normal, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_gsp_skip_same_agent_bidtype_set, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_gsp_skip_same_agent_ocpx_set, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_using_universe_retarget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_tiny_user_query_new_key, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecpm_rankidx_dedup, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rb_rankidx_dedup, ks::AbtestBiz::AD_DSP);

// [guochangyu]
SPDM_ABTEST_BOOL(enable_pass_outer_ecom_conv_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pass_goods_cate_2nd_ids, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pass_user_interested_behavior_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pass_user_layered_tags, ks::AbtestBiz::AD_DSP);

// [yuhanzhang]
SPDM_ABTEST_DOUBLE(mini_game_cpm_modify_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_kmini_game_explore_nc_force_reco, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mini_game_max_hard_ad_force_reco_tag_size, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_force_reco_resort_mini_game_change_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kmini_game_explore_nc_user_stain, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_iaa_free_user_ltv_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_supress_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_lower_threshold, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_normal_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_upper_threshold, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(game_iaa_free_user_ltv_adjust_boost_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(to_xifan_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.01);
SPDM_ABTEST_DOUBLE(to_xifan_cpm_ratio_v2, ks::AbtestBiz::XIFAN_PLAY_APPS, 1.0);
SPDM_ABTEST_BOOL(enable_xifan_skip_fanstop_pos, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_INT64(xifan_native_cnt, ks::AbtestBiz::XIFAN_PLAY_APPS, 6);
SPDM_ABTEST_BOOL(enable_xifan_native_cnt, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_STRING(iaa_coin_task_conf_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(iaa_ad_coin_abtest_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(iaa_ad_free_abtest_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_iaa_coin_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_coin_duration_task, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_coin_ipu_task, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_coin_user_ltv_level_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(iaa_coin_task_conf_tag_user_ltv, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(game_iaa_coin_ipu_task_threshold_supress_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_iaa_coin_ipu_task_threshold_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_iaa_coin_duration_task_threshold_supress_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_game_iaa_coin_duration_task_threshold_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_ad_free_duration_suppress, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_iaa_ad_free_duration_suppress_threshold, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(game_iaa_ad_free_duration_suppress_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_iaa_ad_free_user_response_rate_drop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_ad_free_ipu_suppress, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(game_iaa_ad_free_ipu_suppress_ipu_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(game_iaa_ad_free_ipu_suppress_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(user_iaa_ad_free_response_rate_threshold, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(user_iaa_ad_free_response_rate_drop_rate, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(game_iaa_coin_amount_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);

// [sunsong]
SPDM_ABTEST_DOUBLE(mix_model_roi_threshold, ks::AbtestBiz::AD_DSP, 0.16);
SPDM_ABTEST_DOUBLE(mix_fctr_roi_bonus_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_fctr_model_bonus_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_fctr_model_bonus_bias, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_set_negative_bonus_to_zero, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fctr_model_strategy_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fctr_score_strategy_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(mix_fctr_model_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_fctr_model_strategy_new_v5, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fctr_score_new_formula, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(fctr_model_score_formula_conf, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(disable_fctr_live_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_fctr_model_cmd_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(ad_fctr_model_inner_cmd, ks::AbtestBiz::AD_DSP, "/ad/dsp/fctr:ad_fctr_mtl_model_inner_cmd_key"); // NOLINT
SPDM_ABTEST_STRING(ad_fctr_model_outer_cmd, ks::AbtestBiz::AD_DSP, "/ad/dsp/fctr:ad_fctr_mtl_model_outer_cmd_key"); // NOLINT
SPDM_ABTEST_STRING(ad_fctr_model_inner_cmd_key, ks::AbtestBiz::AD_DSP, "ad_fctr_mtl_model_inner_cmd");
SPDM_ABTEST_STRING(ad_fctr_model_outer_cmd_key, ks::AbtestBiz::AD_DSP, "ad_fctr_mtl_model_outer_cmd");
SPDM_ABTEST_STRING(common_card_min_valid_version, ks::AbtestBiz::AD_DSP, "12.11.10");
SPDM_ABTEST_BOOL(enable_pid_fctr_threshold_bgtask, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_fctr, ks::AbtestBiz::AD_DSP);  // 统一开关，兼容新老架构
SPDM_ABTEST_BOOL(enable_fctr_isotonic_adjust_with_rb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fctr_max_cpm_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fctr_mix_rb_cpm_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bonus_strategy_for_mix_rb_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ori_bonus_ratio_for_mix_rb_cpm, ks::AbtestBiz::AD_DSP, 2.0 / 3.0);
SPDM_ABTEST_DOUBLE(new_bonus_ratio_for_mix_rb_cpm, ks::AbtestBiz::AD_DSP, 0.05);
SPDM_ABTEST_BOOL(disable_fctr_fanstop_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_ai_use_fctr_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_change_fctr_bonus_for_mix_rb_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_fctr_model, ks::AbtestBiz::AD_DSP);

// [wangzixu05]
SPDM_ABTEST_BOOL(enable_inner_explore_mix_fctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_explore_roi_threshold, ks::AbtestBiz::AD_DSP, 0.7);
SPDM_ABTEST_BOOL(enable_inner_explore_fctr_sort_by_cpm_fctr_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inner_explore_ad_fctr_model_inner_cmd, ks::AbtestBiz::AD_DSP, "/ad/dsp/fctr:fctr_bonus_inner_explore_inner_v1"); // NOLINT
SPDM_ABTEST_STRING(inner_explore_ad_fctr_model_outer_cmd, ks::AbtestBiz::AD_DSP, "/ad/dsp/fctr:fctr_bonus_inner_explore_outer_v1"); // NOLINT
SPDM_ABTEST_STRING(inner_explore_ad_fctr_model_inner_cmd_key, ks::AbtestBiz::AD_DSP, "inner_explore_ad_fctr_model_inner_cmd"); // NOLINT
SPDM_ABTEST_STRING(inner_explore_ad_fctr_model_outer_cmd_key, ks::AbtestBiz::AD_DSP, "inner_explore_ad_fctr_model_outer_cmd"); // NOLINT
SPDM_ABTEST_STRING(inner_explore_fctr_exp_tag, ks::AbtestBiz::AD_DSP, "base");

SPDM_ABTEST_BOOL(enable_only_insert_high_ue_in_short_seq_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(high_ue_short_seq_len_front, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_BOOL(enable_only_insert_high_ue_in_short_seq_front_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(high_ue_short_seq_len_front_v2, ks::AbtestBiz::AD_DSP, 3);

// [jiangyuzhen03]
SPDM_KCONF_BOOL(ad.frontserver2, disableChargeInNovelRecommend);
// [jiangyuzhen03] end

// [sunsong]
SPDM_ABTEST_BOOL(enable_fix_mix_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fix_mix_bonus_hc_ratio, ks::AbtestBiz::AD_DSP, 1.0);

// [sunsong]
SPDM_ABTEST_BOOL(enable_jifeibi_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mix_jifeibi_coef, ks::AbtestBiz::AD_DSP, 1.0);

// [sunsong]
SPDM_ABTEST_BOOL(enable_unify_bonus_exp_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(unify_bonus_exp_tag, ks::AbtestBiz::AD_DSP, "default");

// [sunsong]
SPDM_ABTEST_BOOL(enable_skip_fctr_for_exp, ks::AbtestBiz::AD_DSP);

// [sunsong]
SPDM_ABTEST_BOOL(enable_mix_candidate_ad_map, ks::AbtestBiz::AD_DSP);

// [wangwenguang]
SPDM_ABTEST_BOOL(enable_fix_mix_unify_cpm_init, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_rank_truncate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mix_rank_truncate_max, ks::AbtestBiz::AD_DSP, 10000000000);

// [wangwenguang] 端智能重请求新增埋点
SPDM_ABTEST_BOOL(enable_add_rerank_req_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_add_rerank_req_info_live_use_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_control_prerank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_calc_client_ai_rerank_score_adrank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_rerank_retrival_outer, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_INT64(common_card_style_ab, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_common_feed_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_common_feed_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kwai_game_common_feed_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_short_serial_common_feed_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(subpage_admit_ad_target_server_merchant_10008001, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_front_item_attr_by_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_front_modify_item_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_move_back_merchant_select_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_merchant_mix_select, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_merchant_mix_select, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(enable_common_feed_card_min_day, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_BOOL(enable_cid_action_bar_track, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_front_upstream_timeout, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ad_rank_server_extra_timeout, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(ad_rank_server_extra_timeout2, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(ad_rank_server_extra_timeout3, ks::AbtestBiz::AD_DSP, 0);

// [lihongji03] 重请求流量 tag 埋点
SPDM_ABTEST_BOOL(enable_client_ai_rerank_req_info_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(mix_mtb_bid_frac_ad_cpm_gamora, ks::AbtestBiz::AD_DSP, "ad_cpm_allgroup");
SPDM_ABTEST_STRING(mix_mtb_bid_frac_ad_cpm_nebula, ks::AbtestBiz::AD_DSP, "ad_cpm_allgroup");

// [lihongji03] 外循环非外跳透传
SPDM_ABTEST_BOOL(enable_excycle_skip_pass_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_excycle_skip_pass_tag_v2, ks::AbtestBiz::AD_DSP);

// [zhangpuyang]
SPDM_ABTEST_BOOL(enable_direct_mix_predict, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enable_direct_mix_predict_kconf);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enable_direct_mix_predict_debug);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableBidAssistType);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, removeIsInspireUnlockTube);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableRealUseSdkInOnlineJoin);
SPDM_ABTEST_BOOL(close_prerank_prepare_router, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_send_user_feature_pb, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, useDataConverterV2);
SPDM_ABTEST_BOOL(use_data_converter_v2, ks::AbtestBiz::AD_DSP);

// [menjunyi]
SPDM_ABTEST_BOOL(enable_photo_rerank_req, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePhotoRerankReq);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableOcpxActionSupportTypeToMatrix);
SPDM_KCONF_BOOL(ad.adRank2, enableDeviceSupportTaid);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, maxRtaUserOrientationSize, 0);
SPDM_ABTEST_BOOL(close_ranking_prepare_router_splash, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, closeRankingPrepareRouter);
SPDM_ABTEST_BOOL(unlogin_user_skip_inner_photo_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(unlogin_user_skip_live_target, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_INT32(ad.prerank_server, prerankUserInfoBSTruncate, 0);
SPDM_ABTEST_BOOL(enable_prerank_user_info_bs_truncate, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.prerank_server, prerankUserInfoBSCompress);
SPDM_ABTEST_BOOL(enable_prerank_user_info_bs_compress, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(disable_mix_rank_flow_price_bound_in_front, ks::AbtestBiz::AD_DSP);

// [liujiahui10]
SPDM_ABTEST_BOOL(enable_live_cold_gimbal, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nearline_update_gap, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nearline_request_sampling, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(is_live_nearline_request, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(is_photo_nearline_request, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bonus_reallocation_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(bonus_reallocation_exp_tag, ks::AbtestBiz::AD_DSP, "exp6");
SPDM_ABTEST_BOOL(enable_fanstop_bonus_adjust_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_no_fanstop_bonus_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);


// [liuxianyi]
SPDM_ABTEST_BOOL(enable_nobid_store_wide_roi_fix, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_discount_effect_exp, ks::AbtestBiz::AD_DSP);

// [zhaozuodong] bonus 认知实验， bonus 置为 cpm 固定比例
SPDM_ABTEST_BOOL(enable_bonus_fixed_ratio_experiment_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(bonus_fixed_ratio_v2, ks::AbtestBiz::AD_DSP, 0.0);

// [sunsong] 送混 quota 实验
SPDM_ABTEST_BOOL(enable_mix_quota_calib_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mix_quota_new_rb_score_bonus_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mix_quota_exp_gpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(mix_quota_first_soft_quota, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_INT64(mix_quota_first_hard_quota, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_INT64(mix_quota_total_soft_quota, ks::AbtestBiz::AD_DSP, 6);
SPDM_ABTEST_INT64(mix_quota_total_hard_quota, ks::AbtestBiz::AD_DSP, 6);
SPDM_ABTEST_BOOL(enable_mix_quota_rerank_by_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rerank_quota_by_cpm_and_rs, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(rerank_quota_by_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(rerank_quota_by_rs_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(rerank_quota_by_rb_ratio, ks::AbtestBiz::AD_DSP, 1.0);

// [zhangyuxiang05] 一天中观看广告数最大门槛实验是否开启
SPDM_ABTEST_BOOL(enable_max_browsed_ads_num_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_max_browsed_ads_num_exp_new, ks::AbtestBiz::AD_DSP);
// [zhangyuxiang05] 一天中观看广告数最大门槛
SPDM_ABTEST_INT64(max_browsed_ads_num_exp, ks::AbtestBiz::AD_DSP, 300);
SPDM_ABTEST_INT64(max_browsed_ads_num_exp_new, ks::AbtestBiz::AD_DSP, 300);
// [zhangyuxiang05] 特殊地区一天中观看广告数最大门槛
SPDM_ABTEST_INT64(special_region_max_browsed_ads_num_exp, ks::AbtestBiz::AD_DSP, 40);
SPDM_ABTEST_INT64(special_region_max_browsed_ads_num_exp_new, ks::AbtestBiz::AD_DSP, 40);
// [zhangyuxiang05] 是否对异常 unify_bonus 进行截断
SPDM_ABTEST_BOOL(enable_calib_abnormal_unify_bonus_by_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_calib_abnormal_unify_bonus_by_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_calib_abnormal_unify_bonus_by_max_cpm_and_thr, ks::AbtestBiz::AD_DSP);
// [zhangyuxiang05] 对异常 unify_bonus 的校准系数
SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_gamora1, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_nebula1, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_gamora2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_nebula2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_gamora3, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_nebula3, ks::AbtestBiz::AD_DSP, 1.0);
// [zhangyuxiang05] 主站商业化单 pv bonus 约束
SPDM_ABTEST_DOUBLE(ad_pv_bonus_frac_thr_gamora, ks::AbtestBiz::AD_DSP, 32500.0);
// [zhangyuxiang05] 极速版商业化单 pv bonus 约束
SPDM_ABTEST_DOUBLE(ad_pv_bonus_frac_thr_nebula, ks::AbtestBiz::AD_DSP, 29500.0);
// [zhangyuxiang05] 主站商业化 cpm 百分比约束
SPDM_ABTEST_DOUBLE(cpm_times_thr_gamora, ks::AbtestBiz::AD_DSP, 1.0);
// [zhangyuxiang05] 极速版商业化 cpm 百分比约束
SPDM_ABTEST_DOUBLE(cpm_times_thr_nebula, ks::AbtestBiz::AD_DSP, 1.0);
// [zhangyuxiang05] bonus 留反实验实验流量标记
SPDM_ABTEST_BOOL(enable_ad_unify_bonus_weight_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ad_unify_bonus_weight_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ad_unify_bonus_weight_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_gamora4, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(calibration_weight_for_abnormal_unify_bonus_nebula4, ks::AbtestBiz::AD_DSP, 1.0);

// [lifeiyang03] 商品通路召回时，控制是否出优惠卷
SPDM_ABTEST_BOOL(enable_big_v_items_first, ks::AbtestBiz::AD_DSP);

// [huliren]
SPDM_ABTEST_BOOL(enable_log_title_select_score, ks::AbtestBiz::AD_DSP);

// [qiancheng10]
SPDM_ABTEST_BOOL(enable_cid_dup_photo_id_write_userinfo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_assit_corporation_write_userinfo, ks::AbtestBiz::AD_DSP);

// [qiancheng10]
SPDM_ABTEST_BOOL(enable_cid_account_id_write_userinfo, ks::AbtestBiz::AD_DSP);
// [lizuxin]
SPDM_ABTEST_BOOL(enable_cid_dup_photo_id_write_cvr_gmv_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_fill_dup_info_prependicular_logic, ks::AbtestBiz::AD_DSP);
// [hanhao05]
SPDM_ABTEST_BOOL(enable_inner_multi_queue_bonus_reallocation, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_inner_multi_queue_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_bonus_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fanstop_bonus_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);

// [jiangqiqi03]
SPDM_ABTEST_BOOL(enable_mix_bonus_fixed_ratio_rct_experiment, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_update_ad_gpm_trace, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_gpm_auto_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(ad_gpm_auto_ratio_exp_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_bonus_allocation_cpm_user_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(bonus_allocation_cpm_user_exp_tag, ks::AbtestBiz::AD_DSP, "default");

// [lifeiyang03]
SPDM_ABTEST_BOOL(enable_search_trans_all_style_info, ks::AbtestBiz::AD_DSP);

// [liweijie06]
SPDM_ABTEST_BOOL(enable_gyl_subpage_price_discount,  ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_pdp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_olp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_odp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_psp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_item_card_price_discount_ratio_ddp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_pdp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_olp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_odp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_psp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_live_price_discount_ratio_ddp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_pdp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_olp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_odp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_psp, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_p2l_price_discount_ratio_ddp, ks::AbtestBiz::AD_DSP, 1.0);
}  // namespace front_server
}  // namespace ks
