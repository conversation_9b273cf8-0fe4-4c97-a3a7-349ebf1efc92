syntax  = "proto3";
option cc_enable_arenas = true;
package ks.front_server.kconf;

import "teams/ad/ad_proto/kuaishou/ad/ad_task.proto";
import "teams/ad/ad_proto/kuaishou/ad/ad_base.proto";
import "teams/ad/ad_proto/kuaishou/ad/industry_opt/game_opt.proto";
import "teams/ad/ad_proto/kuaishou/ad/common_ad_log.proto";

message ServerStrategyConf {
  message PluginConf {
    string name = 1;
    string enable_tag  = 2;
  }
  message CallPoint {
    repeated PluginConf explore_scene = 1;
    repeated PluginConf follow_scene = 2;
    repeated PluginConf nearby_scene = 3;
    repeated PluginConf detail_scene = 4;
    repeated PluginConf splash_scene = 5;
    repeated PluginConf galaxy_scene = 6;
    repeated PluginConf universe_scene = 7;
    repeated PluginConf search_scene = 8;
    repeated PluginConf merchant_scene = 9;
    repeated PluginConf traceapi_scene = 10;
    repeated PluginConf default_scene = 11;
  }
  map<string, CallPoint> admit = 1;
  map<string, CallPoint> pretreatment = 2;
  map<string, CallPoint> auction = 3;
  map<string, CallPoint> ad_filter = 4;
}

message UniverseAgeMergeItem {
  repeated int64 first_industry_id = 1;
  repeated int64 second_industry_id = 2;
  repeated string product_name = 3;
  repeated int32 resource_id = 4;    // 联盟暗投：0 联盟直投：1 联盟优选：2
  repeated int32 user_type = 5;   // 快手用户：0 非快手用户：1
}

message UserActionType {
  message PickStratagy {
    int32 stratagy_id = 1;
    repeated kuaishou.ad.AdActionType ad_action_type = 2;
  }
  repeated PickStratagy pick_stratagy = 1;
}

message BidOptimExpConfig {
  message FuncParam {
   double alpha_i = 1;
   double beta_i = 2;
   double gamma_i = 3;
  }

  message FuncSwitch {
   bool enable_fit_log_func = 1;
   bool enable_fit_exp_func = 2;
  }

  message UserTagSwitch {
    bool enable_commerical_user_tag = 1;
    bool enable_merchant_user_tag = 2;
  }

  message AdValueSwitch {
    bool enable_optim_cpm = 1;
    bool enable_optim_rank_benefit = 2;
  }

  message StraFeatDim {
    repeated int64 ad_monitor_type_set = 1;
    repeated int64 ad_queue_type_set = 2;
    repeated string user_level_set = 3;
    FuncParam func_param = 4;
    repeated string page_id_set = 5;
  }

  message UnifyStraParam {
    bool enable_admit = 1;
    bool enable_uplift_control = 2;
    UserTagSwitch user_tag_switch = 3;
    AdValueSwitch ad_value_switch = 4;
    FuncSwitch func_switch = 5;
  }
  message ExpConfig {
    UnifyStraParam unify_strategy_param = 1;
    map<string, StraFeatDim> strategy_feature_dim = 2;
  }

  map<string, ExpConfig> exp_list = 1;
}

message BidOptimCalcHyperParam {
  message ExpParam {
    double lambda = 1;
  }
  map<string, ExpParam> exp_list = 1;
}

message ClientLimiter {
  map<string, int64> config = 1;
}

message UniverseProductBonusCombinePB {
    map<string, double> bonus_map = 1;  // 预算 map: key: product_name value: bugdet( 元 )
    map<string, int64> product_timestamp = 2;  //  补贴开始时间 key: product_name value: 补贴开始时间
    int64 product_bonus_upperbound = 3;  // 天级总补贴约束（元）
    int64 bonus_duration = 4;  // 补贴上限时长(天)
    int64 cost_threshold = 5;  // 空耗上限
    double cost_rate_threshold = 6;  // 成本率上限
    int64 conv_cnt = 7;  //  转化数上限
    map<string, double> cpm_threshold = 8;  // 补贴 cpm 上限 key: 产品名字 , value: 上限系数
}

message UniverseTinyForceCpm {
  repeated string product_names = 1;
  map<int64, int64> pos_id_2_cpm = 2;
  bool close_product_names = 3;
}

message NewProductDiscount {
  message ExpConf{
    map<string, double> product_discount_info = 1;
    map<int64, double> account_discount_info = 2;
  }
  map<string, ExpConf> discount_conf = 1;
}

message AdAntiSpamRequstBlackIds {
  message DeviceInfo {
    repeated string imei = 1;
    repeated string device_id = 2;
    repeated string ip = 3;
    repeated string idfa = 4;
  }
  DeviceInfo kuaishou = 1;
  DeviceInfo union = 2;
}

message AccountAllowedDeeplinkPrefix {
  message AllowedPrefix {
    repeated string deeplink_prefix = 1;
  }
  bool function_open = 1;
  string min_main_android_version = 2;
  string min_main_iOS_version = 3;
  map<string, AllowedPrefix> account_list_detail = 4;
  repeated string common_support_prefix = 5;
}

message MerchantPhotoStyleConfPb {
  repeated int32 played_report_time = 1;
}

message PhotoDisplayVerson {
  string main_app_android_ver = 1;
  string main_app_ios_ver = 2;
  string nebula_app_android_ver = 3;
  string nebula_app_ios_ver = 4;
}

message MerchantRpcConfig {
  bool enable = 1;
  bool enable_rpc_user_cache = 2;
  bool enable_rpc_user_count = 3;
  bool enable_rpc_merchant_item_info = 4;
  bool enable_rpc_merchant_item_sales = 5;
  bool enable_rpc_seckill = 6;
  bool enable_rpc_price = 7;
  bool enable_rpc_merchant_product_detail = 8;
}

message PreviewConfig {
  message Creatives {
    repeated int64 creative_id = 1;
  }
  message Units {
    repeated int64 unit_id = 1;
  }
  map<string, Creatives> user_creatives = 1;
  map<string, Units> user_units = 2;
}

message KnewsSubpageTestConfig {
  message ExpConfig {
    message SubpageConfig {
      message AutoDownloadInfos {
        int32 scene = 1;  // 场景
        int32 auto_download_delay = 2;  // 多少ms后自动下载
      }
      int64 skip_tag_show_time = 1;  // 开屏“跳过”标签出现时间，从视频开始播放开始计时，单位秒
      int64 splash_ad_duration = 2;  // 开屏广告持续时间，单位秒
      int64 count_down_delay_time_ms = 3;  // 激励视频关闭按钮延时出现时间，单位ms，大于等于0有效
      bool enable_ad_info_blank_click = 4;  // 激励视频广告信息卡片上空白区域是否可点击
      bool enable_play_end_blank_click = 5;  // 激励视频播放结束卡片上空白区域是否可点击引擎
      int64 auto_download_delay = 6;  // 下载类广告多少ms后自动下载
      repeated AutoDownloadInfos auto_download_infos = 7;  // 自动下载各场景对应的信息
      bool unable_app_download_pause = 8;  // 是否支持暂停下载
    }
    map<int64, SubpageConfig> sub_page_config = 1;
  }
  map<string, ExpConfig> config = 1;
}

message KnewsEffectSplashImageUrlsConfig {
  message IntervalInfo {
    message ImageInfo {
      int64 width = 1;
      int64 height = 2;
      string url = 3;
    }
    string begin_date = 1;
    string end_date = 2;
    repeated ImageInfo image_urls = 3;
    int64 priority = 4;
  }
  map<string, IntervalInfo> image_urls_config = 1;
}

message UniversePlayableConfig {
  message Config {
    string playable_url = 1;  // 试玩h5链接
    string playable_style_info = 2;  // 试玩h5样式信息, json格式
  }
  map<int64, Config> unit_config_map = 1;
}

message FilterInvalidResponseConfig {
  map<string, bool> explore = 1;
  map<string, bool> nearby = 2;
  map<string, bool> detail = 3;
  map<string, bool> galaxy = 4;
  map<string, bool> universe = 5;
  map<string, bool> follow = 6;
}

message ExploreFeedMixCpmConf {
  message ScoreUpliftItem {
    double score = 1;
    double ratio = 2;
  }
  message SegmentCpmConf {
    repeated ScoreUpliftItem data = 1;
  }
  map<string, SegmentCpmConf> exp_conf = 1;
}

message ExploreLiveScoreConf {
  enum LiveScoreStrategy {
    DEFAULT = 0;
    LIVE_SCORE_STRATEGY_KEEP_ORDER = 1;
    LIVE_SCORE_STRATEGY_CPM_DIST = 2;
    LIVE_SCORE_STRATEGY_CPM_STD = 3;
  }
  message CpmDistribution {
    double min_cpm = 1;
    double max_cpm = 2;
    double avg_cpm = 3;
    double std_cpm = 4;
  }
  message ExploreLiveScoreParam {
    LiveScoreStrategy strategy_id = 1;
    CpmDistribution cpm_dist = 2;
    double var_ratio = 3;
    double global_weight = 4;
    map<string, double> user_tag_weight_map = 5;  // 用户分层权重
    double user_weight = 6;
  }
  map<string, ExploreLiveScoreParam> live_score_param_conf = 1;
}

message FeedMixScoreConf {
  message FeedMixScoreParam {
    double min_cpm = 1;
    double max_cpm = 2;
    double avg_cpm = 3;
    double global_weight = 4;
    map<string, double> user_tag_weight_map = 5;
  }
  map<string, FeedMixScoreParam> feed_mix_score_param_conf = 1;
}

message ExploreExperienceConf {
  message ExperienceScore {
    double ad_play_time = 1;       // 播放时长
    double valid_play_rate = 2;    // 有效播放率
    double long_play_rate = 3;     // 长播率    
    double complete_rate = 4;      // 完播率
    double short_play_rate = 5;    // 短播率
    double like_rate = 6;          // 点赞率
    double comment_rate = 7;       // 评论率
    double follow_rate = 8;        // 关注率
    double share_rate = 9;         // 分享率
    double collect_rate = 10;      // 收藏率
    double dislike_rate = 11;      // dislike 率
    double report_rate = 12;       // 举报率
    double gpm = 13;
    double pctr = 14;
  }
  map<string, ExperienceScore> inner_explore_experience_score = 1;
  map<string, ExperienceScore> feed_explore_experience_score = 2;
}

message InnovationTrafficEcpcConfigUpdateV3 {
  message RatioConf {
    repeated double parameters = 1;
  }
  map<string, RatioConf> key_conf = 1;
}

message UeqInfoConfig {
  message UeqObjectInfo {
    string redis_name = 1;
    string redis_key_prefix = 2;
    repeated int32 sub_page_id_list = 3;
    repeated int32 stra_type_list = 4;
    int32 redis_key_type = 5;
    repeated int32 page_id_list = 6;
    int32 is_open = 7;
    int32 stra_id = 8;
  }
  map<string, UeqObjectInfo> ueq_info_config_mapping = 1;
  map<string, UeqObjectInfo> strategy_info_config_mapping = 2;
}

message CoverSizeInfoConfig {
  message CoverSizeInfo {
    repeated int32 sub_page_id_list = 1;
    int32 width = 2;
    int32 height = 3;
  }
  repeated CoverSizeInfo cover_size_info_list = 1;
}

message ColossusReqInfoConfig {
  message ColossusInfo {
    string name = 1;
    string type = 2;
    int32 limit_size = 3;
  }
  repeated ColossusInfo colossus_info_list = 1;
  float enable_ratio = 2;
}

message SortParameterConfig {
  message ParameterObjectInfo {
    string cpm_pa = 1;
    string ueq_pa = 2;
    string p3tr_pa = 3;
    string p5tr_pa = 4;
  }
  map<string, ParameterObjectInfo> kwai_config_mapping = 1;
  map<string, ParameterObjectInfo> nebula_config_mapping = 2;
}

message UniverseSkipAdmitFilterId {
  repeated string device_id = 1;
  repeated string idfa = 2;
  repeated string imei = 3;
  repeated string oaid = 4;
  repeated string user_id = 5;
}

message MerchantJinniuStyle {
  bool enable = 1;  // 是否开启金牛样式小店通落地页
  bool enable_white_account = 2;  // 是否使用 account 白名单
  repeated int64 white_account_ids = 3;  // unit 白名单列表
  string xiaodian_uri = 4;  // 小店通原落地页uri
  string jinniu_uri = 5;  // 小店通金牛样式落地页uri
  string hyid = 6;  // 小店通金牛样式落地页hyid
}

message LandingPageVersionControl {
  bool enable_control = 1;
  string ios_min_version = 2;
  string android_min_version = 3;
}

message KwaiGalaxyBrandConfig {
  message FlowBrandConfig {
    message BrandConfig {
      bool enable_kwai_galaxy_brand = 1;
      repeated int32 sub_page_ids = 2;
    }
    map<string, BrandConfig> flow_brand_config = 1;
  }
  map<string, FlowBrandConfig> app_brand_config = 1;
}

message MixedFecherInfo {
  bool enable_mixed_fecher = 1;
  int64 cpm = 2;
  int64 ueq = 3;
  int64 bonus_cpm = 4;
  int32 ad_priority = 5;
}

message BillingSeparateAccountExpConf {
  message SortParam {
    double auto_bid_weight = 1;
    double default_billing_ratio = 2;
    double owe_control_weight = 3;
    double owe_ratio_thrd = 4;
    double show_ratio = 5;
  }

  message PriceParam {
    double k = 1;
    double alpha = 2;
    double owe_alpha = 3;
    bool enable_gfp = 4;
    double gfp_ratio = 5;
    bool enable_owe_only_adjust_price = 6;
    bool enable_skip_bid_limit = 7;
    bool enable_billing_separate_weight_v2 = 8;
  }

  message ExpConfig {
    bool enable_config = 1;
    SortParam sort_param = 2;
    PriceParam price_param = 3;
    repeated int64 account_id = 4;
  }

  map<string, ExpConfig> conf_list = 1;
}

message SearchBrandVersionControl {
  string android_main_ver = 1;
  string android_nebula_ver = 2;
  string ios_main_ver = 3;
  string ios_nebula_ver = 4;
}

message SearchSingleLayoutVersionControl {
  string android_main_ver = 1;
  string android_nebula_ver = 2;
  string ios_main_ver = 3;
  string ios_nebula_ver = 4;
}

message AdDBTypeConfig {
  message AdSceneInfo {
    map<int64, bool> scene_id = 1;    // true 执行，否则不执行
    map<int64, bool> page_id = 2;     // true 执行，false 不执行, 不存在则看scene_id
    map<int64, bool> sub_page_id = 3; // true 执行，false 不执行, 不存在则看page_id
    map<int64, bool> pos_id = 4;      // true 执行，false 不执行, 不存在则看sub_page_id
  }
  map<int32, AdSceneInfo> db_types = 1;       // dbtype对应的配置
}

message SearchPreviewConfig {
  // 见 ad_base.proto SearchRelevantUser
  message RelevantAuthor {
    int32 recall_type = 1;  // 召回类型
    string reason = 2;  // 推荐理由
  }

  message ProductShow {
    int64 item_id = 1;  // 商品 ID
    bool cover_show = 2;  // 是否搜索结果页外露商品
    int64 photo_id = 3;  // 粉条广告要借助 photo id 才能拿到 item id

    // 临时：kconf 覆盖 API 结果
    // 见 ad_base.proto ProductShow
    bool override = 10;
    bool override_sales = 11;
    bool override_price_suffix = 12;

    string icon_url = 21;  // 商品图片
    int64 sales_count = 22;  // 商品销量
    int32 price = 23;  // 价格，单位为分
    string price_suffix = 24;  // 价格显示后缀，例如 “起”
    string item_name = 25;  // 商品名称
    string jump_url = 26;  // 跳转链接
    int32 show_type = 27; // show type
    int32 sku_explain_statu = 28; // explain statu
    int64 seller_id = 29;
  }

  message AppCard {
    message ExtraElement {
      int64 photo_id = 1;
      string description = 2;
    }
    repeated ExtraElement extra_elements = 1;
    int32 strong_card_type = 2; // 1:精确匹配， 2: 非精确匹配， 3: 列表强样式
    string aigc_description = 3;
  }

  message AdInfo {
    int64 unit_id = 1;
    int64 creative_id = 2;
    bool is_item_kbox = 3;
    bool is_live_kbox = 4;
    ProductShow product_show = 5;  // 商品信息外露
    int64 search_celebrity_id = 6;  // 搜索大 V 用户 id
    bool is_search_mid_page = 7;
    kuaishou.ad.SearchMidPageConfig mid_page_config = 8;
    repeated int64 photo_bigv_photo_ids = 9;
    bool is_series_card = 10;  // 短剧强样式
    bool is_local_life_kbox = 11;
    kuaishou.ad.AdBaseInfo.LocalLifeShow local_life_show = 12;
    bool is_search_fiction_card = 13;  // 自建小说强样式
    bool is_series_kbox = 14;
    kuaishou.ad.AdBaseInfo.SeriesShow series_show = 15;
    bool is_mini_game_kbox = 16;
    kuaishou.ad.AdBaseInfo.MiniGameShow mini_game_show = 17;
    string deduplicate_id = 18;
    int32 kbox_strategy_for_combo = 19;  // 大搜 kbox 策略字段
  }
  message Rule {
    repeated int64 user_ids = 1;
    repeated int64 creative_ids = 2;
    bool is_fanstop = 3;
    RelevantAuthor relevant_author = 4;
    ProductShow product_show = 5;  // 商品信息外露

    // 正排升级到 GetStyleInfoRouteByUnit 后需要 unit_id
    int64 creative_id = 6;
    int64 unit_id = 7;

    int64 multi_retrieval_tag = 8;
    int32 pos = 9;
    bool is_search_celebrity = 10;  // 直播用户大卡
    AppCard app_card = 11;  // 下载类强样式
    bool pos0 = 12;
    repeated AdInfo ad_infos = 13;  //  多个广告预览，与单个广告预览互斥
    AppCard form_card = 14;  // 表单强样式
    repeated int64 big_v_item_ids = 15; // 直播大卡小黄车商品 ID
    kuaishou.ad.AdBaseInfo.LocalLifeShow local_life_show = 16;
    bool is_search_fiction_card = 17;  // 自建小说强样式
    bool is_search_item_recall = 18;  // 是否商品通路召回
    bool is_super_card = 19;  // 是否为超级样式；如直播超级样式，下载超级样式
    kuaishou.ad.AdBaseInfo.SeriesShow series_show = 20;
    kuaishou.ad.AdBaseInfo.MiniGameShow mini_game_show = 21;
  }
  message QueryConfig {
    repeated Rule rules = 1;
  }

  message Group {
    string name = 1;
    repeated int64 user_ids = 2;
    map<string, Rule> queries = 3;  // 忽略其中的 user_ids
  }

  map<string, QueryConfig> queries = 1;
  repeated Group groups = 2;
}

message ADDBMergeConfig {
  message PluginConf {
    int32 db_type = 1;
    string score_func_name = 2;
    repeated string filter_func_names = 3;
  }
  message MergeInfos {
    repeated PluginConf merge_infos = 1;
  }
  map<int64, MergeInfos> scene_id_merge_info = 1;
  map<int64, MergeInfos> page_id_merge_info = 2;
  map<int64, MergeInfos> sub_page_id_merge_info = 3;
  map<int64, MergeInfos> pos_id_merge_info = 4;
}

message SiXinServicePageComponentPb {
  message PageComponent {
    int32 type = 1;
    int32 sub_type = 2;
  }
  repeated PageComponent config = 1;
}

message SkipCommonAdmitConfig{
  message WhiteDevice {
    repeated string device_id = 1;
  }
  map<string, WhiteDevice> app_device_white_list = 1;
}

message RtaUselessFieldFilter {
  map<string, int32> ad_user_info = 1;
  map<string, int32> universe_ad_request_info = 2;
}

message UniverseGenericFlowControlConfig {
  message Item {
    int64 max_qps = 1;
    int64 target_qps = 2;
    int64 max_cache_time = 3;
    double k_p = 4;
    double k_i = 5;
    double k_d = 6;
  }
  double exp_proportion = 1;
  map<string, Item> config_map = 2;
}

message AdmitFlowBlackListConfigPb {
  repeated int64 page_id = 1;
  repeated int64 sub_page_id = 2;
}

message newFlowAdmitConfigPb {
  map<int64, string> config_map = 1;
  map<int64, string> page_id_config = 2;
}

message UnionMidPageWhiteConf {
  message ProductWhiteConf {
    repeated int64 account_ids = 1;
  }
  map<string,ProductWhiteConf> product_white_conf = 1;
}

message SearchSmartPhotoCoverConf {
  message SmartPhotoExpConf {
    repeated int64 shield_actual_pos_ids = 1;
  }
  map<string,SmartPhotoExpConf> exp_conf = 1;
}

message SearchTabQueryInterveneConf {
  message DetailConf {
    int32 operation_type = 1;  // 1为 top4 不出广告，2为首刷不出广告
    repeated string shield_item_title = 2;
  }
  message TabQueryInterveneConf {
    repeated int64 sub_page_id_list = 1;
    DetailConf detail_conf = 2;
  }
  map<string, TabQueryInterveneConf> query_intervene_conf = 1;
}

message MerchantMultiQueueConf {
  message QueueSampleCfg {
    double gpm_ratio = 1;
    int64  quota_gpm = 2;
    int64  quota_opm = 3;
    int64  quota_cpm = 4;
    int64  quota_rb = 5;
  }
  message ExpConf{
     map<int64, QueueSampleCfg> info = 1;
  }
  map<string, ExpConf> page_conf = 1;
}

message SearchKboxReleThreshConf {
  message ReleThreshConf {
    double live_kbox_thresh = 1;  // 直播 kbox 阈值
    double photo_item_kbox_thresh = 2;  // 短视频商品 kbox 阈值
    double live_item_kbox_thresh = 3;  // 直播商品 kbox 阈值
    int64 live_kbox_ecpm_thresh = 4;  // 直播 kbox ecpm 阈值
    int64 photo_item_kbox_ecpm_thresh = 5;  // 短视频商品 kbox ecpm 阈值
    int64 live_item_kbox_ecpm_thresh = 6;  // 直播商品 kbox ecpm 阈值
    double photo_local_life_kbox_thresh = 7;  // 短视频本地生活 kbox 阈值
    double live_local_life_kbox_thresh = 8;  // 直播本地生活 kbox 阈值
    int64 photo_local_life_kbox_ecpm_thresh = 9;  // 短视频本地生活 kbox ecpm 阈值
    int64 live_local_life_kbox_ecpm_thresh = 10;  // 直播本地生活 kbox ecpm 阈值
    int64 series_kbox_thresh = 11;  // 短剧 kbox 阈值
    int64 series_kbox_ecpm_thresh = 12; // 短剧 kbox ecpm 阈值
    int64 mini_game_kbox_thresh = 13;  // 小游戏 kbox 阈值
    int64 mini_game_kbox_ecpm_thresh = 14;  // 小游戏 kbox ecpm 阈值
  }
  map<string,ReleThreshConf> exp_conf = 1;
}

message ClickButtonInfo {
  int32 button_width = 1;          // 按钮的宽度
  int32 button_height = 2;         // 按钮的高度
  int32 button_bottom_margin = 3;  // 按钮距离底部的距离
  string button_title = 4;         // 按钮上的文案
  int32 button_corner_radius = 5;  // 按钮的圆角半径大小
}

message AbTestName {
  repeated string ab_names = 1;
};

message MultiRewardedCoinDataConf {
  message SingleRewardedCoinDataConf {
    string prefix = 1;
    string enable_tag  = 2;
    string global_cpm_tag = 3;
    string user_value_tag = 4;
    string ecpm_bias_tag = 5;
    string coin_upper_tag = 6;
    string coin_lower_tag = 7;
    string ratio_upper_tag = 8;
    string ratio_lower_tag = 9;
    int64  base_coin_num = 10;
    double user_low_value_percentile_thr = 11;
    double user_low_value_ratio = 12;
    double user_percentile_bias = 13;
    double global_cpm = 14;
    double new_user_value_ratio = 15;
  }
  map<int64, SingleRewardedCoinDataConf> rewarded_coin_conf_list = 1;
}

message DeepRewardedCoinDataConf {
  message SingleDeepRewardedCoinDataConf {
    double low_value_ctcvr_k = 1;
    double low_value_cvr_k = 2;
    double low_value_uplift_k = 3;
    double low_value_thr = 4;
    int64  base_coin_num = 5;
    double user_percentile_bias = 6;
    double new_user_value_ratio = 7;
    string enable_tag  = 8;
    string user_value_tag = 9;
    string ecpm_bias_tag = 10;
    string coin_upper_tag = 11;
    string coin_lower_tag = 12;
    string ratio_upper_tag = 13;
    string ratio_lower_tag = 14;
    string enable_treatment_tag = 15;
  }
  map<string, SingleDeepRewardedCoinDataConf> deep_rewarded_coin_conf_list = 1;
}

message RewardedCoinScalingByAccountConf {
  message RewardedCoinScalingConf {
    string coin_scaling_coef_tag = 1;
    string coin_upper_tag  = 2;
    string coin_lower_tag = 3;
    string deep_coin_scaling_coef_tag = 4;
    string deep_coin_upper_tag = 5;
    string deep_coin_lower_tag = 6;
  }
  map<int64, RewardedCoinScalingConf> rewarded_coin_scaling_conf_list = 1;
}

message IncntvAdBinToLinerCoef {
  double lower = 1;
  double upper = 2;
  double weight = 3;
  double bias = 4;
}
message IncntvAdPredictNext1ViewValue2Coef {
  message IncntvAdBinToLinerCoefList {
    repeated IncntvAdBinToLinerCoef bin_wb_list = 1;
  }
  map<string, IncntvAdBinToLinerCoefList> incntv_ad_predict_next_1view_value_2_coef = 1;
}

message ChargePriceDefenderConfig {
  message Threshold {
    int64 min_threshold = 1;
    int64 max_threshold = 2;
  }
  bool enabled = 1;
  Threshold default_threshold = 2;
  map<string, Threshold> source_type_threshold = 3;
  map<string, Threshold> campaign_type_threshold = 4;
  map<string, Threshold> bid_type_threshold = 5;
  map<string, Threshold> charge_action_type_threshold = 6;
}

message SensitiveThirdCategoryConfig {
  message SensitiveIdList {
    repeated int64 ids = 1;
  }
  map<string, SensitiveIdList> exp_list = 1;
}

message KnewsTimeOutConfig {
  int64 front_time_out = 1;
  int64 ad_server_time_out = 2;
  int64 target_time_out = 3;
}

message NewInduceMaterialBlackMediumConfig {
  repeated string app_ids = 1;
  repeated int64 pos_ids = 2;
}

message AdQueryUserEmbeddingRedisConfig {
  message AbtestInfo {
    string prefix = 1;
  }
  string kcc_name = 1;
  int32 timeout_ms = 2;
  map<string, AbtestInfo> abtest = 3;
}

message AdPackSpecialAdsConfig {
  repeated int64 page_ids = 1;
  repeated int64 sub_page_ids = 2;
}

message MiniAppPageIdConf {
  string app_id = 1;
  repeated string page_id = 2;
}

message GlobalTimeoutConfig {
  message Config {
    int32 front_to_ad_server_extra = 1;
    int32 ad_server_to_target_search = 2;
    int32 ad_server_to_ad_rank_extra = 3;
    int32 target_search_to_prerank_ps_router = 4;
    int32 ad_rank_to_ps_router = 5;
    int32 front_to_match_server_extra = 6;
  }
  map<string, Config> abtest = 1;
}

message SearchProductShow {
  string redis_name = 1;
  int32 redis_timeout_ms = 2;
  string kwaishop_product_detail_prefix = 3;
  bool kwaishop_url_fix = 5;
}

message HotSpotAvoidBigVConfig {
  message AvoidConfig {
    message ScheduleConfig {
      string start_time = 1;                    // 开始时间
      string end_time = 2;                      // 结束时间
    }
    ScheduleConfig schedule_config = 1;         // 配置生效时间
  }
  bool enable = 1;
  map<string, AvoidConfig> query_avoid_config = 2;
}

message ForceProductSet {
    message Values {
        map<string, forceSet> force_set = 1;  // 产品名 -> set
    }
    message forceSet {
        repeated int64 force_photo = 1;
    }
    map<string, Values> force_product = 1;
}

message MerchantKwaishopAddr {
  string cache_name = 1;
  int32 cache_timeout_ms = 2;
  string cache_key_prefix = 3;
  bool enable_cache_read = 4;
  bool enable_cache_write_async = 5;
  int32 cache_write_parallel = 6;
  bool enable_use_adcode = 7;
  double degrade_ratio = 8;
  bool enable_redis_write = 9;
  int32 cache_expire_sec = 10;
}

message AdMixComboConfig {
  message AbTestInfo {
    bool enable_person_ad_pos_gap_open = 1;
    int32 cal_pos_gap_exp_tag = 2;
    bool need_realtime_calgap = 3;
    bool need_realtime_predict = 4;
    bool adjust_5p5_to_5 = 5;
    int32 featured_videos_num_per_ad_for_thanos_down = 6;
    bool enable_nebula_thanos_shw_ad_per_num_of_videos = 7;
    bool enable_nebula_thanos_shw_ad_per_time_gap = 8;
    bool enable_gamora_last_page_size_fill_pos = 9;
    bool enable_gamora_adjust_videos_num = 10;
    bool enable_adjust_first_ad_pos = 11;
    bool enable_nebula_last_page_size_fill_pos = 12;
    bool enable_nebula_adjust_videos_num = 13;
  }
  AbTestInfo hold_out = 1; // hold_out 组
  AbTestInfo combo_exp = 2; // combo 组
}
message AdFlowGradeConfig {
  message DownStream {
    bool ad_server_default = 1;
    bool ad_server_fanstop = 2;
    bool ad_fanstop = 3;
    bool ad_brand = 4;
    bool ad_social = 5;
  }
  message ExploreConfig {
    int32 ratio = 1;
    map<string, bool> need_degraded_list = 2;
  }
  message SubPageConfig {
    int32 ratio = 1;
    repeated int64 sub_page_id_list = 3;
  }
  message AdRequetFlowTypeObj {
    int32 ratio = 1;
  }
  DownStream downstream = 1;
  ExploreConfig explore_config = 2;
  SubPageConfig sub_page_config = 3;
  map<string, AdRequetFlowTypeObj> ad_request_flow_type_config = 4;
}

message FrontRpcDegrade {
  message RatioDegrade {
    int32 eds_pass_ratio = 1;
    int32 forward_index_pass_ratio = 2;
    int32 ad_pack_pass_ratio = 3;
    int32 livestream_count_query_pass_ratio = 4;
    int32 merchant_commodity_apply_pass_ratio = 5;
    int32 merchant_commodity_item_price_apply_pass_ratio = 6;
  }
  message EnableDegrade {
    bool enable_request_style_forward = 1;
    bool enable_brand_preview = 2;
  }

  RatioDegrade ratio_degrade = 1;
  EnableDegrade enable_degrade = 2;
  map<string, int32> rpc_degrade_ratio = 3;
}

message AdServerMergeBlacklistPb {
  repeated int64 page_id_list = 1;
  repeated int64 sub_page_id_list = 2;
}

message SplashLightInteractiveConfig {
  message SplashLightInteractiveStyle {
    int32 interactive_style = 1;
    string min_ios_version = 2;
    string min_android_version = 3;
    int32 interactive_component = 4;
  }

  repeated SplashLightInteractiveStyle splash_light_interactive_style = 1;
}

message PlayAbleSdkMinVersionConfig {
  string table_screen_ads_sdk = 1;
  string table_screen_content_sdk = 2;
};

message NoLimitVirtualGoldSceneConfig {
  repeated string ad_request_flow_type_config = 1;
  repeated int64 page_id_config = 2;
  repeated int64 sub_page_id_config = 3;
}

message SearchBigvLiveConfig {
  string title = 1; // 标题
  string title_icon_text = 2; // 标题栏右侧按钮文案
  string title_icon_url = 3; // 标题栏右侧按钮 icon url
  string title_icon_link_url = 4; // 标题栏右侧按钮跳转 url
}

message HardTargetFilterConfig {
  bool enable_hard_target_filter = 1;
  repeated int64 black_sub_page_id_list = 2;
}

message SearchAdDynamicPositionConfig {
  message DynamicConfig {
    message DynamicInterval {
      repeated int64 sub_interval_cpm_thres = 1;  // 每大于一个阈值，可缩小间距 1
      repeated int64 add_interval_cpm_thres = 2;  // 每小于一个阈值，可扩大间距 1
      int32 start_page = 3;  // 动态策略起始页
      repeated int64 inner_sub_interval_cpm_thres = 4;  // 内循环广告
      repeated int64 inner_add_interval_cpm_thres = 5;  // 内循环广告
    }
    DynamicInterval dynamic_interval = 1;
    bool enable_single = 2;  // 单列开关
    bool enable_double = 3;  // 双列开关
    float not_first_page_ratio = 4; // 非首页阈值系数
    float inner_ratio = 5; // 内循环阈值系数
  }
  map<string, DynamicConfig> abtest = 1;
  int32 max_sub_interval = 2;   // 最大可减小的间距
  int32 max_add_interval = 3;   // 最大可增加的间距
  string dot_perf_group_param = 4;  // 打点使用的 ab 组名参数
}

message SearchDesKeywordSubConf {
  message AdCanSubList {
    repeated string product_name_list = 1;
  }
  map <string, AdCanSubList> brand_sub_map = 1;
}

message SearchAgeWiseThrottling {
  message AgeWiseThrottling {
    double ratio = 1;      // throttling ratio (0~1)
    int32 min = 2;         // min age
    int32 max = 3;         // max age
  }

  message AgeWiseThrottlingList {
    repeated AgeWiseThrottling throttling = 1;
  }

  map<string, AgeWiseThrottlingList> group = 1;
}

message AdPosPremiumConfig {
  repeated int64 feed = 1;
  repeated int64 thanos = 2;
}

message CommonDyeingEraseConfig {
  message EraseConfig {
    repeated string front_request_type = 1;
    repeated string product_name_list = 2;
    repeated int64  account_id_list = 3;
    repeated int32  account_type_list = 4;
  }
  map<int32, EraseConfig> config = 1;
}

message PecAdPreviewInfo {
  message RewardedInfo {
    bool is_pec_effect = 1;   // 本次是否出激励任务
    int32 deep_rewarded_type = 2;   // 深度激励类型; 0:拉活,1:激活,2:下单
    int64 pec_rewarded_price = 3;   // 本次深度激励金额
    string display_type = 4;
    int32 rewarded_stage = 5;   // 信息流激励阶段
    int32 inspire_style_type = 6;     // 激励样式类型, 对应 pb 枚举 RewardStyleType
    int32 right_id = 7;   // 激励 pec 样式权益 id
    int32 pec_rewarded_stage = 8;   // 信息流激励类型
    int32 shallow_rewarded_point = 9;   // 浅度激励点
    int32 deep_rewarded_point = 10;   // 深度激励点
    int64 deep_pec_rewarded_price = 11;   // 深度激励金额
    int64 reward_coin = 12;  // 激励视频深度激励金额
    int32 pec_coin_type = 13;  // 奖励类型: 金币或快币
    bool is_first_pay_rewarded = 14;  // 是否首日充值激励
    bool enable_rebate_coin = 15; // 用户付费是否返点
    double rebate_percent = 16; // 返点比例
    int64  max_rebate_num = 17; // 最大返点金额
    int32  pec_rewarded_style = 18; // 判断本次是否为强样式，在原有样式上加元素
    bool is_hit = 19; // 是否命中金币暴击
    int32 richness_type = 20; // 金币暴击类型
    int32 richness_threshold = 21; // 金币暴击条件
    int64 richness_amount = 22; // 金币暴击奖励金额
    int64 deep_rewarded_coin = 23; // 深度激励金币数
    int32 order_deep_incentive_type = 24;  // 下单激励类型
    double coin_discount_ratio = 25;  // 下单激励金币折扣
  }
  map<int64, RewardedInfo> preview_info = 1;    // <creative_id, rewardedInfo>
}

message CommonSmartOfferPreviewInfo {
  map<int64, kuaishou.ad.industry_opt.AdSmartStrategyInfo> preview_info = 1; // <creative_id, adSmartStrategyInfo>
}

message PecCouponPreviewInfo {
  message CouponInfo {
    uint64 coupon_template_id = 1;
    uint32 coupon_type = 2;
    uint32 threshold_type = 3;
    uint64 threshold = 4;
    uint64 discount_amount = 5;
    uint64 threshold_upper = 6;
    uint64 discount_amount_upper = 7;
    uint64 expire_minutes = 8;
    uint32 coupon_display_type = 9;
    uint64 pre_boost_discount_amount = 10;
    uint64 pre_boost_reduce_amount = 11;
    uint32 optimal_style_disable_feed_card = 12;
  }
  map<int64, CouponInfo> preview_info = 1;
}

message PecCouponPreviewInfoV2 {
  message CouponInfo {
    uint64 coupon_template_id = 1;
    uint32 coupon_type = 2;
    uint32 threshold_type = 3;
    uint64 threshold = 4;
    uint64 discount_amount = 5;
    uint64 threshold_upper = 6;
    uint64 discount_amount_upper = 7;
    uint64 reduce_amount = 8;
    uint64 capped_amount = 9;
    uint64 expire_minutes = 10;
    uint32 coupon_display_type = 11;
    uint64 pre_boost_discount_amount = 12;
    uint64 pre_boost_reduce_amount = 13;
    uint32 optimal_style_disable_feed_card = 14;
  }
  map<int64, CouponInfo> preview_info = 1;
}

message MerchantPreviewInfo {
  message MerchantInfo {
    int32 price = 1;
    string icon_url = 2;
  }
  map<int64, MerchantInfo> preview_info = 1;
}

message MatrixStylePreviewInfo {
  message MatrixStyleInfo {
    int64 matrix_style_material_id = 1;
    int32 matrix_style_type = 2;
    int32 display_type = 3;
  }
  map<int64, MatrixStyleInfo> preview_info = 1;    // <creative_id, matrixStyleInfo>
}

message BossModePreviewInfo {
  message PreviewInfo {
    int64 creative_id = 1;
    int64 unit_id = 2;
    int32 prob = 3;   // 出该体验广告的概率
    int64 expire_time = 4;  // 2022052507 xxxx-xx-xx-xx
    int32 freq_gap_time = 5;  // 频控间隔时间, 单位秒
  }
  map<int64, PreviewInfo> user_info = 1;  // <uid, preview_info>
}

message OuterNativeSoftCpmThrConfig {
  message ExpCpmThrConfig {
    map<string, double> campaign_type_cpm_config = 1;  // <type, cpm_thr>
  }
  map<string, ExpCpmThrConfig> exp_cpm_thr_config = 1;  // <exp_tag, ExpCpmThrConfig>
}

message OfflineAppStore {
  repeated string offline_app_stores = 1;
}

message EngineTraceLogSceneRateMap {
  message V2TableConf {
    int64 mod_base = 1;   // uid % mod_base < scene_rate v2 表发数据
    int32 default_rate = 2;    // scene_rate 中 flow_type 没配置，用这个
    map<string, int32> scene_rate = 3;
    int32 shift_interval_day = 4;  // 抽样位移时间间隔(day)
    int32 shift_step = 5;  // 抽样位移步长
  }
  map<int64, double> v1_table_conf = 1;     // 这张表给白盒用
  V2TableConf v2_table_conf = 2;     // 这张表给数仓用
}

message FrontToBrandCommonAttrList {
  enum CommonAttrType {
    UNKNOWN = 0;
    INT = 1;
    DOUBLE = 2;
    STRING = 3;
    INT_LIST = 4;
    DOUBLE_LIST = 5;
    STRING_LIST = 6;
  }
  message CommonAttrName {
    repeated string attr_names = 1;
  }
  map<string, CommonAttrName> config = 1;
}

message ComboPressConfig {
  string world = 1;
  string exp = 2;
  string group = 3;
}

message UniversePhyPosClusterSignConf {
  message StartTime {
    int64 hour = 1;
    int64 min = 2;
  }
  StartTime start_time = 1;
  int64 duration_ts = 2;
  int64 timeout = 3;
  int64 excute_times = 4;
}

message PecMultiCouponConf {
  message CouponConfig {
    int64 coupon_template_id = 1;
    repeated int64 author_id = 2;
  }
  repeated CouponConfig coupon_config = 1;
}

message GameAdForceDirectCallWhiteList {
  repeated string product_names = 1;
}

message CreatorAllowedIndustryConf {
  message IndustryMap {
    map<string, int32> industry_map = 1;
  }
  map<string, IndustryMap> allowed_industry = 1;
}

message PremiumSoftAdReviewConfig {
  repeated int32 general_review_include = 1;
  repeated int32 hot_review_include = 2;
  repeated int32 topk_review_exclude = 3;
}

message OuterNativeNegFilterRateConfig {
  message Rate {
    map<int32, double> flow_ratio = 1;
  }
  map<int64, Rate> config = 1;
}

message OuterloopAbNegFilterRateConfig {
  map<string, OuterNativeNegFilterRateConfig> ab_config = 1;
}

message MixBidDiscountConf {
  message StrategyConf {
    repeated int32 item_type_set = 1;
    repeated int32 ad_queue_type_set = 2;
    repeated int32 ad_monitor_type_set = 3;
    repeated string product_set = 4;
    double mix_bid_thr = 5;
    double discount_ratio = 6;
    double discount_bias = 7;
    double client_ad_cpm_thr = 8;
    bool enable_mix_benefit = 9;
    bool enable_client_ad_cpm = 10;
  }

  message ExpConf {
    map<string, StrategyConf> strategy_list = 1;
  }

  map<string, ExpConf>  exp_list = 1;
}

message KuaiGameFeedPlayConf {
   repeated string device_mod_conf = 1;
   repeated string product_name_conf = 2;  
   repeated int64 account_conf = 3;
   repeated int64 age_bound_conf = 4;
   string app_version_conf = 5;
}

message MiniGameSubSidyConf {
  message SubSidyInfo {
    repeated int64 subsidy_info_list = 1;
  }
  message SubSidyConf {
    repeated double ltv_index_list = 1;
    repeated int64 pay_cnt_index_list = 2;
    repeated int64 pay_info_list = 3;
    map<string, SubSidyInfo> subsidy_info = 4;
  }
  map<string, SubSidyConf> exp_list = 1;
}


message MiniGameSecondSubSidyConf {
  message SubSidyConf {
    repeated int64 pay_info_list = 1;
    repeated int64 subsidy_info_list = 2;
  }
  map<string, SubSidyConf> exp_list = 1;
}

message MiniGameDiscountRatioConf {
  message DiscountRatioConf {
    repeated int64 pay_info_list = 1;
    repeated int64 day_cnt_list = 2;
    repeated int64 pay_cnt_list = 3;
    map<string, double> discount_ratio_lower_bound_info = 4;
    map<string, double> discount_ratio_upper_bound_info = 5;
    repeated double random_discount_list = 6;
    int32 random_ratio = 7;
  }
  map<string, DiscountRatioConf> exp_list = 1;
}

message FictionPanelPriceParamsConfig {
  message PanelPriceConf {
    repeated double ltv_grades = 1;
    map<string, PriceParam> param_conf_map = 2;
  }
  message PriceParam {
    double price_weight = 1;
    int32 price_bias = 2;
    double origin_price_weight = 3;
    int32 origin_price_bias = 4;
    double price_max_panel_bound_weight = 5;
    int32 price_max_panel_bound_bias = 6;
    double price_min_panel_bound_weight = 7;
    int32 price_min_panel_bound_bias = 8;
    int64 price_upper_limit = 9;
    int64 price_lower_limit = 10;
    double ios_platform_tax_share_weight = 11;
  }
  map<string, PanelPriceConf> exp_group = 1;
}

message FictionSmartSubsidyAdjustingConfig {
  message FictionWhiteListConfig {
    string conf_key = 1;
    repeated int64 book_id_white_list = 2;
    repeated int64 ks_uid_white_list = 3;
    repeated int64 account_id_white_list = 4;
  }
  repeated FictionWhiteListConfig config_list = 1;
}


message FictionSmartSubsidyStrategyConfig {
  message DiscountInfo {
    repeated int32 discount_percent_list = 1;
  }
  message FictionSubsidyConf {
    repeated double ltv_score_grades = 1;
    map<int32, DiscountInfo> crowd_subsidy_map = 2;
    int32 paid_user_calm_day = 3;
    int32 subsidy_activity_id = 4;
    repeated double pay_panel_grades = 5;
  }
  enum CrowdUserTag {
    UNKNOWN_USER_TAG = 0;
    NEW_IMPRESSION = 1;
    AD_COGNITIVE = 2;
    FICTION_COGNITIVE = 3;
    PAIED_RECENTLY = 4;
    PAID_LONG_PAST = 5;
  } 
  map<string, FictionSubsidyConf> exp_list = 1;
}
message FictionSubsidyStyleConf {
  message SubsidyStyleConf {
    double ltv_min_thresh = 1;
    double ltv_max_thresh = 2;
    int32 delay_ms = 3;
    repeated double ltv_grades = 4;
    map<string, StyleRateInfo> style_rate_index_map = 5;
  }
  message StyleRateInfo {
    repeated int32 rate_index = 1;
    double retain_discount_ratio = 2;
    double origin_subsidy_weight= 3;
  }
  map<string, SubsidyStyleConf> exp_group = 1;
}

message FictionBigDaySubsidyConf {
  message DiscountSubsidyConf {
    int64 start_time_ms = 1;
    int64 end_time_ms = 2;
    int32 global_additional_discount = 3;
    string global_group_tag_conf_key = 4;
    repeated WhiteListConf white_configs = 5;
    int32 common_subsidy_discount = 6;  // 智能定价适配全局折扣
    int32 nc_subsidy_discount = 7;  // 智能定价适配 nc 冲量折扣
  }
  message WhiteListConf {
    int32 activity_id = 1;
    int32 additional_discount = 2;
    string group_tag_conf_key = 3;
    repeated int64 book_id_white_list = 4;
    repeated int64 account_id_white_list = 5;
    repeated int64 ks_uid_white_list = 6;
    int32 special_subsidy_discount = 7;  // 智能定价适配白名单折扣
  }
  repeated DiscountSubsidyConf conf_list = 1;
}
message FictionBigDaySubsidyMapConf {
  map<string, FictionBigDaySubsidyConf> exp_conf_group = 1;
}
message FictionSubsidyFreqLimitConf {
  message ExpConf {
    map<int32, int64> gap_ms_map = 1;
  }
  map<string, ExpConf> freq_conf_map = 1;
}

message FictionSubsidyPayPanelConf {
  message ExpConf {
    repeated double ltv_score_grades = 1;
    repeated int64 pay_panel_grades = 2;
  }
  map<string, ExpConf> panel_conf_map = 1;
}

message AdSelectQuotaConf {
  message ExpConf {
    map<string, int64> quota_list = 1;
  }
  map<string, ExpConf> exp_list = 1;
}

message FeedDistributioConf {
  int64 max_rank_benifit = 1;
  int64 min_rank_benifit = 2;
  int64 avg_rank_benifit = 3;
}

message CidMagicSiteReplaceInfoMap {
  message MagicSiteInfo {
    int64 pg_lp_page_id = 1;
    int32 magic_site_page_type = 2;
    int32 page_source_type = 3;
  }
  map<string, MagicSiteInfo> data = 1;
}

message RankBenifitDistributionConf {
  map<string, FeedDistributioConf> abtest = 1;
}

message AppInfo {
    repeated string appstore = 1;
    repeated string testflight = 2;
}
message appStoreInReview {
  map<string,  AppInfo> data = 1;
}
message UnifyGpmCaliConf {
  message CalibrationParams {
    double soft_live_cali_ratio = 1;
    double hard_live_cali_ratio = 2;
    double soft_photo_cali_ratio = 3;
    double hard_photo_cali_ratio = 4;
  }
  map<string, CalibrationParams> tag_map = 1;
}

message SplashPrefetchRecordWhiteListPb {
  repeated int64 account_ids = 1;
  repeated int64 unit_ids = 2;
  repeated int64 creative_ids = 3;
}

message ForcedImperssionExp {
  float ratio_thr = 1;
  int64 cpm_delta_thr = 2;
}

message DegradeLevelConfig {
  message LevelConfig {
    repeated int64 sub_page_id_list = 1;
    repeated int64 page_id_list = 2;
    repeated string request_flow_type_list = 3;
  }

  message LevelConfigs {
    LevelConfig level_1 = 1;
    LevelConfig level_2 = 2;
  }

  bool enable = 1;
  int32 level_1 = 2;
  int32 level_2 = 3;
  int32 level_default = 4;
  LevelConfigs level_configs = 5;
}

message ResourceWhiteBoxFlowTagConfig {
  message SubPageIds {
    repeated int64 sub_page_id_list = 1;
  }

  bool enabled = 1;
  map<string, SubPageIds> flow_tag_to_sub_page_ids = 2;
  map<string, string> flow_tag_to_ad_request_flow_type = 3;
}

message AdSmartOfferValues {
  message DiscountInfo {
    repeated uint64 single_price_range = 1;
    int32 discount_ratio = 2;
    int32 offer_ratio = 3;
    uint64 interval = 4;
  }
  message SmartOfferValue {
    bool use_default = 1;
    double default_ratio = 2;
    double pay_value = 3;
    double offer_value = 4;
    int64 package_lessons = 5;
    int64 ms_coin_amount = 6;
    int64 activity_interval = 7;
    double pay_adjust = 8;
    double offer_adjust = 9;
    double pay_weight = 10;
    double offer_weight = 11;
    map<int64, int64> tiered_value = 12;
    double min_conf_pay = 13;
    double min_conf_offer = 14;
    double max_conf_pay = 15;
    double max_conf_offer = 16;
    bool enable_mini_offer = 17;
    double ltv_score_thd  = 18;
    int64 lessons_without_offer = 19;
    int64 coins_without_offer = 20;
    double pay_without_offer = 21;
    double mini_offer = 22;
    repeated DiscountInfo package_discount = 23;
    DiscountInfo retain_info = 24;
    double uplift_result_ratio = 25;
    double uplift_result_adjust = 26;
    int64 chaoxiaoe_filtered_activity_interval = 27;
    double chaoxiaoe_filtered_pay_value = 28;
    double chaoxiaoe_filtered_offer_value = 29;
  }
    map<string, SmartOfferValue> version = 1;
}

message AdSmartOfferConfig {
  message ExpConf {
    map<string, string> item_conf =1;
    map<string, string> user_conf =2;
  }
  map<string, ExpConf> exp_list = 1;
}

message PlayletIaaUpliftConfig {
  message MultiConfig {
    double iaa_score_lower = 1;  // 小于等于 0 表示全部人群
    double iaa_score_upper = 2;
    repeated int64 series_ids = 3;  // 元素个数为空表示不限制剧
    repeated BillSecondByEpisodeInfo bill_second_by_episode_infos = 4;  // by 集设时长，优先级最高 
    string bill_second_by_episode_infos_str = 5;
    repeated int64 orientation_ids = 6;
  }
  message BillSecondByEpisodeInfo {
    int32 start_episode = 1;  // 为负表示所有集
    int32 end_episode = 2;
    int32 bill_second = 3;  // 大于 0 才生效
  }
  message ExpConfig {
    int32 inspire_iaa_bill_second = 1;    // 强制广告时长, 优先级低于 by 集, 高于线上默认 26s
    int32 commercial_iaa_award_count = 2;    // iaa广告的gap，0表示客户设置，x（x>=1）表示看一个广告解锁x集
    int32 iaa_timegap = 3;
    repeated MultiConfig multi_configs = 4;  // 人、剧、集
    bool is_bill_second_by_model = 5;
  }
  map<string, ExpConfig> exp_map = 1;  // 多个实验 tag
}

message TargetFlowConfig {
  message FlowAdmitConfig {
    map<string, bool> flow_type_config = 1;
    map<int64, bool> sub_page_id_config = 2;
    map<int64, bool> page_id_config = 3;
  }
  map<string, FlowAdmitConfig> target_flow_admit = 1;
}

message MapStringToInt64 {
  int64 default_value = 1;
  map<string, int64> map_data = 2;
}

message ClueAggrCpmPriceRatioConf {
  message QueueSampleCfg {
    double clue_aggr_a_ad_price_ratio = 1;  // a 计费系数
    double clue_aggr_bcd_ad_price_ratio = 2;  // bcd 计费系数
    double clue_aggr_bcd_ad_price_loss_ratio = 3;  // bcd 考虑下发到端上折损&二次请求折损的系数
  }
  map<string, QueueSampleCfg> product_conf = 1;
}

message WhiteBoxDebugSpecialFilterReasonsMsg {
  repeated string reasons = 1;
}

message DisableFactorConfigPb {
  repeated string udf = 1;
  repeated string ecpc = 2;
  repeated string filter = 3;
}

message EngineServiceExtraTimeout {
  map<string, int32> conf = 1;
}

message GameIaaCoinTaskConf {
  message ExpConfig {
    map<string, CoinTask> coin_config = 1;
  }
  message StageDetailContent {
    int64 coin_amount = 1;
    int64 default_threshold = 2;
    int64 delta_threshold = 3;
  }
  message CoinTask {
    int64 stage_num = 1;
    map<int64, StageDetailContent> stage_content = 2;
  }
  map<string, ExpConfig> exp_config = 1;
}

message IncentiveIaaAdSensitiveUserBillTimeConfig {
  message IaaBillTimeConfig {
    map<string, double> sensitive_user_bill_time_config = 1;
  }
  map<string, IaaBillTimeConfig> exp_config = 1;
}

message GameIaaCoinTaskUserLtvBoostConf {
  message ExpConfig {
    map<string, double> user_ltv_conf = 1;
  }
  map<string, ExpConfig> exp_config = 1;
}

message SearchInspireParamsConf {
  message SearchInspireParamsDataConf {
    string enable_has_more = 1;
    string enable_change_one = 2;
    string has_more_prob = 3;
    string change_one_prob = 4;
    string photo_bill_time = 5;
    string live_bonus_time = 6;
  }
  map<int64, SearchInspireParamsDataConf> search_inspire_params_config = 1;
}

message AdUnifyBonusRatioExp{
  message ExpConf {
    double cpm_lbound = 1;
    double cpm_ubound = 2;
    double bonus_ratio = 3;
  }
  message ExpConfList {
    repeated ExpConf exp_conf_list = 1;
  }
  map<string, ExpConfList> exp_conf_map = 1;
}

message AdCpmUserUnifyBonusRatioExp{
  message ExpConf {
    double cpm_lbound = 1;
    double cpm_ubound = 2;
    map<string, double> user_type_bonus_ratio_map = 3;
  }
  message ExpConfList {
    repeated ExpConf exp_conf_list = 1;
  }
  map<string, ExpConfList> exp_conf_map = 1;
}

message AdLiveRerankTimeExp{
  message ExpConf {
    int64 cpm_lbound = 1;
    int64 cpm_ubound = 2;
    int32 gap_time = 3;
    int32 poll_time = 4;
  }
  message ExpConfList {
    repeated ExpConf exp_conf_list = 1;
  }
  map<string, ExpConfList> exp_conf_map = 1;
}

message innerMixScoreConf {
  map<string, ScoreMetricConfig> photo_conf = 1;
  map<string, ScoreMetricConfig> live_conf  = 2;
}

message AdGpmAutoRatioExp {
  message ExpConf {
    int64 weekday = 1;
    double gpm_ratio = 2;
  }
  message ExpConfList {
    repeated ExpConf exp_conf_list = 1;
  }
  map<string, ExpConfList> exp_conf_map = 1;
}

message ScoreMetricConfig {
  double weight = 1;
  double bias   = 2;
  double exp    = 3;
}

message SendMixNumConfig {
  map<string, int64> send_mix_num = 1;
  map<string, double> roi1_strong_thres = 2;
  map<string, double> roi1_weak_thres = 3;
  map<string, double> stage2_roi_thres = 4;
  map<string, double> incremental_roi1_thres = 5;
  map<string, double> commercial_thres_weight = 6;
  map<string, double> roi_weight = 7;
  map<string, double> score_adj_by_cpm = 8;
}

message AdInnerMultiQueueConf {
  map<string, SendMixNumConfig> confs = 1;
}
