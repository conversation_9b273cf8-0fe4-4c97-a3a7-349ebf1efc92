#pragma once
#ifndef IMPL_BLOCK_BEGIN        /* auto generated by reset_kconf.py, you can ignore. */
  #define IMPL_BLOCK_BEGIN 0    /* auto generated by reset_kconf.py, you can ignore. */
#endif                          /* auto generated by reset_kconf.py, you can ignore. */
#ifndef IMPL_BLOCK_END          /* auto generated by reset_kconf.py, you can ignore. */
  #define IMPL_BLOCK_END 10000  /* auto generated by reset_kconf.py, you can ignore. */
#endif                          /* auto generated by reset_kconf.py, you can ignore. */

#include <string>

#include "base/common/basic_types.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_base/src/kconf/common/ad_bg_task_config.h"
#include "teams/ad/front_server/util/kconf/kconf_data.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_task.pb.h"
#include "teams/ad/engine_base/kconf/id_selector.h"
#include "teams/ad/engine_base/kconf/app_version_control.h"


// 实现 cc 中带实现， header 中只带声明
#ifndef KCONF_CC_WITH_IMPL
  #undef  DEFINE_KCONF_NODE
  #define DEFINE_KCONF_NODE(type, config_path, config_key, default_value)                    \
    API_KCONF_NODE(type, config_path, config_key, default_value)

  #undef DEFINE_KCONF_NODE_LOAD
  #define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key)                              \
    API_KCONF_NODE_LOAD(type, config_path, config_key)

  #undef DEFINE_SET_NODE_KCONF
  #define DEFINE_SET_NODE_KCONF(type, config_path, config_key)                                  \
    API_SET_NODE_KCONF(type, config_path, config_key)

  #undef DEFINE_LIST_NODE_KCONF
  #define DEFINE_LIST_NODE_KCONF(type, config_path, config_key)                                  \
    API_LIST_NODE_KCONF(type, config_path, config_key)

  #undef DEFINE_KCONF_MAP_KCONF
  #define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
    API_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key)
#else
  #undef DEFINE_KCONF_NODE_ATTR
  #define DEFINE_KCONF_NODE_ATTR __attribute__ ((used))
#endif

struct AdDisplayInfoFilterConf;
struct AdDescriptionFormatConf;
struct MultiRewardedCoinDataList;
struct RewardedCoinScalingByAccountList;
struct CorporationCeilingWhiteListConfig;
struct IncntvAdPredictNext1ViewValue2Coef;
struct SearchInspireParamsConfig;
struct AdUnifyBonusRatioExpConfNew;
struct AdCpmUserUnifyBonusRatioExpConf;
struct AdLiveRerankTimeGapExpConf;
struct UserActionTypeConf;
struct AdGpmAutoRatioExpConf;

namespace ks {
namespace front_server {
using namespace ks::ad_base::kconf;  // NOLINT
class FrontKconfUtil {
 public:
  // bool: (namespace , node_name, default_value)

#if 0 >= IMPL_BLOCK_BEGIN and 0 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, follow_bonus_weight)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, disableBrandSingleFeedFixPosUnitList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, boostReservePriceAccountSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, enablePauseAppSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, cateThirdIdFlowSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.splashserver, shiledSplashDeviceMod);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.splashserver, shiledSplashHarmonyDeviceMod);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.splashserver, shieldFoldDeviceMod);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, PreInstallAppVersionFilterSet);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, skipMinPriceRatioOcpxSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, highRiskPageIdSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, requestMappingIdServiceFlowType);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.engine, caseDebugAccountSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, refreshCountPosIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, refreshCountPosIdBlacklistSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, refreshCountPosIdBlacklistExpSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.brandserver, brandShowPosWhiteUnit)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, adPreloadWhiteAccount)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, adPreloadWhiteUnit)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, adShowAndGuideWhiteAccount)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, adShowAndGuideWhiteUnit)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, deeplinkWhiteAccount)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, inspireLiveInnerOuterPkPosList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, duanjuNewNameSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, playletSdpaBlackPriceRatio)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableFakePhotoIdToLiveAd, false)
  DEFINE_BOOL_KCONF_NODE(ad.adFrontDiffSwitches, enableUniverseTrafficControl, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableJinniuBlackUser, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableHighReturnRateUser, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableInnerExplore, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableExploreHoldExp, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSimplifyAlwaysLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disableUnloginMixMulti, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableLogBrowseAd, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, skipRtaService, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, merchantDynamicPosSubpageSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, watchVideoSubpageSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, watchVideoPageSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, drawFlowItemLiveBlacklist)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, xifanAdFeedInnerPosSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, incentiveIaaSubPageIdSetForBillTime)
  DEFINE_PROTOBUF_NODE_KCONF(
    IncentiveIaaAdSensitiveUserBillTimeConfig, ad.frontserver2, incentiveIaaAdSensitiveUserBillTimeConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, xifanCoinsShoppingPosSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, inspireSkipPkSubpageSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, inspireMultiadPkSubpageSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, inspireMultiadPkPageSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, commonCardIndustryV2)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, commonCardUserWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, commonCardGameList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, commonCardCampaignBlackList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, commonCardAccountBlackList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, inspireFlowRequestLiveTargetWhiteList)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableRtaClearUnusedField, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFrontSetBenefit, true)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, specialRegionExclusiveUserList)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableLogChainVersion, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableNodeCostTimePerf, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableUnlogInTraffic, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableKnewsDynamicAdPos, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, knewsImpressionUrlToClickUrl, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableBrandAdvancedCard, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableKnewsUseHaiChuan, false)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableSendNativeAck, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFrontGetSortParamRedisData, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableNewNativeAdReason, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFollowNewNativeAdReason, false)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, fanstopAdjustEcpmRatio, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, dspAdjustEcpmRatio, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, playletDiscountHourRatio, 1.0)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableGetAbMappingIdFromReq, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableGetAbMappingIdFromSearchInfo, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableForwardMissLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, enableJkNebulaIos, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, closeJKFakeRequest, true)
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, enableAdxShortDebug, false)
  DEFINE_INT64_KCONF_NODE(ad.adserver2, JKPlanRequestRatio, 100)
  DEFINE_INT64_KCONF_NODE(ad.frontserver2, guidedSurveyGapHours, 24);
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, rCountTestR, 1.1)
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, enableTraceRate, 0.0)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, playletDiscountByHour)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableForwardTargetCheck, false)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, ocpcActionTypeKminiGameIAP);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, CSubsidyBigCustomerConf);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, CLongTermSubsidyAppIdConf);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, MiniGameSmartSubsidyConf);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adRank, subsidyIaapWhiteList);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSmallGameUnloginUser, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSplashUnloginUser, true)
  DEFINE_BOOL_KCONF_NODE(ad.engine, enableJKLandingPage, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableXifanJump, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFanstopFollowBrowsedMove, false)
  DEFINE_BOOL_KCONF_NODE(ad.adFrontDiffSwitches, enableAppDetailInfoOpt, false)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, tubeNormalPosSize, 20)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, enableMappingIdFlowRatio, 100)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, brandMockMixBenefit, 9e8)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, brandMockCpm, 9e8)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, clientDebugUserAdNum, 0)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, dmpHitPackageSize, 0)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, sleepDurationMicroSeconds, 1000)
  DEFINE_INT32_KCONF_NODE(ad.adserver, adServerReqRtaDeliveryRatio, 100)
  DEFINE_PROTOBUF_NODE_KCONF(RtaUselessFieldFilter, ad.adserver2, rtaUselessFieldFilter);
  DEFINE_PROTOBUF_NODE_KCONF(CidMagicSiteReplaceInfoMap, ad.frontserver, cidMagicSiteReplaceInfoMap);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableTargetCheck, false)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, chargeInfoByteSizeAlert, 9000)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, universeClientLimitRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, gameNewProductPriceRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(firefly.playset, playsetPriceRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, playletSdpaCampaignPriceRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, playletCampaignTypePriceRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.universeAdAutoParam, universeQualityScoreGradeThreshold)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, cidMagicSiteReplaceMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver2, xifanCpmFilterKconf)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, cidMagicSiteReplaceMapV2)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, cidMagicSiteUrlMap)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, retrievalTagNameMap)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, generalReviewNameMap)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, hotReviewNameMap)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver2, storewide_guaranteed_info)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, topKReviewNameMap)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, adActionTypeNameMap)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.frontserver, BrandFeedFixedPositionUnitList)
  DEFINE_PROTOBUF_NODE_KCONF(AdSelectQuotaConf, ad.frontserver, adSelectQuotaConf)
  DEFINE_PROTOBUF_NODE_KCONF(MixBidDiscountConf, ad.adAutoParam, mixBidDiscountConf)
  DEFINE_PROTOBUF_NODE_KCONF(MiniGameSubSidyConf, ad.adAutoParam, miniGameSubSidyConf)
  DEFINE_PROTOBUF_NODE_KCONF(MiniGameSecondSubSidyConf, ad.adAutoParam, miniGameSecondSubSidyConf)
  DEFINE_PROTOBUF_NODE_KCONF(
    FictionSmartSubsidyStrategyConfig, ad.adAutoParam, fictionSmartSubsidyStrategyConfig)
  DEFINE_PROTOBUF_NODE_KCONF(
    FictionSmartSubsidyAdjustingConfig, ad.frontserver2, fictionSmartSubsidyAdjustingConfig)
  DEFINE_PROTOBUF_NODE_KCONF(
    FictionPanelPriceParamsConfig, ad.frontserver2, fictionPanelPriceParamsConfig)
  DEFINE_PROTOBUF_NODE_KCONF(
    FictionSubsidyFreqLimitConf, ad.frontserver2, fictionSubsidyFreqLimitConf)
  DEFINE_PROTOBUF_NODE_KCONF(FictionBigDaySubsidyConf, ad.frontserver2, fictionBigDaySubsidyConf)
  DEFINE_PROTOBUF_NODE_KCONF(FictionBigDaySubsidyConf, ad.frontserver2, fictionSmartPriceBigDaySubsidyConf)
  DEFINE_PROTOBUF_NODE_KCONF(
    FictionBigDaySubsidyMapConf, ad.frontserver2, fictionSmartPriceBigDaySubsidyConfV2)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver2, fictionIndustrySubsidyConf);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, fictionPanelPriceProductNameWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, fictionPanelPriceProductNameBlackList)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver2, testMockModelScoreDoubleParam, 2.0)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, testFictionAdUserWhiteList)
  DEFINE_LIST_NODE_KCONF(int32, ad.frontserver2, fictionSubsidyRandomRatio)
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.frontserver2, CSubsidyBiasConf)
  DEFINE_LIST_NODE_KCONF(int32, ad.frontserver2, fictionPanelRandomRealPayPrice)
  DEFINE_LIST_NODE_KCONF(int32, ad.frontserver2, fictionIosCalibratedPayAmountList)

  DEFINE_PROTOBUF_NODE_KCONF(GameIaaCoinTaskConf,
    ad.frontserver2, gameIaaCoinTaskConf)
  DEFINE_PROTOBUF_NODE_KCONF(GameIaaCoinTaskUserLtvBoostConf,
    ad.frontserver2, gameIaaCoinTaskUserLtvBoostConf)
  DEFINE_PROTOBUF_NODE_KCONF(MiniGameDiscountRatioConf,
    ad.frontserver2, MiniGameDiscountRatio)
  DEFINE_PROTOBUF_NODE_KCONF(UserActionTypeConf, ad.frontserver2, userActionTypeConf)
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, nearlineActionGapSecond, 3600)

#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 1 >= IMPL_BLOCK_BEGIN and 1 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_PROTOBUF_NODE_KCONF(FictionSubsidyStyleConf, ad.frontserver2, fictionSubsidyStyleConf)
  DEFINE_PROTOBUF_NODE_KCONF(
    FictionSubsidyPayPanelConf, ad.frontserver2, fictionSubsidyPayPanelConf)
  DEFINE_PROTOBUF_NODE_KCONF(BidOptimExpConfigClass, ad.adAutoParam, bidOptimExpConfig)
  DEFINE_PROTOBUF_NODE_KCONF(BidOptimCalcHyperParamClass, ad.adAutoParam, bidOptimCalcHyperParam)
  DEFINE_PROTOBUF_NODE_KCONF(KuaiGameFeedPlayConfClass, ad.adAutoParam, kuaiGameFeedPlayConf)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::EngineServiceExtraTimeout, ad.adserver2, AdEngineServiceExtraTimeout)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFixUniverseTinySkipAddChargeTag, false)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, enhanceGamePremiereProductMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, hardAdCpmRatio)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, softAdCpmRatio)
  DEFINE_HASH_SET_NODE_KCONF(std::string, adData.realtime, frontTraceLogAdExtendInfoWhiteList)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableDoubleWriteNewCreativeFlow, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, flowSearchHotBoardBgTask, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disablePvDataTraceLog, false)

  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, inspireFanstopRatio, 1.0)
  // 线索聚合页参数
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableClueaggregatepageRerank, false)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, clueaggregatepageTopSameIndustryCount, 3)
  // 加速探索增量标记参数
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, ocpmAccIncrementAccountIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, adxForceWhiteUserSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, ocpmAccIncrementUnitIdSet)
  // 过滤进入自然模型的低 pvtr photo 样本
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, photoPvtrFilterThr)
  // 过滤进入自然模型的低 pvtr photo 样本 尾号分流
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, photoPvtrFilterThrV2)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, photoPvtrFilterPvtrExceptionValue, -2.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, photoPvtrFilterPvtrNewOldMultiple, 3.3671)

  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver2, innerLiveMixRatioRb, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver2, innerLiveMixRatioCpm, 1.0)
  DEFINE_INT64_KCONF_NODE(ad.frontserver2, originPriceProtect, 100000)

  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, gmvProductNameSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, vivoAppStoreFlowSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, inspireMerchantSubPageIdSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, subPageIdSkipDspRetrievalSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, gmvAccountIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, knewsImpressionUrlToClickUrlWhiteAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, knewsImpressionUrlToClickUrlSubPageId)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, appPackageDetailInfoBlackList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, searchAdBoxNonInspireSubPageId)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, customBlackList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, aliOuterDeliveryAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, photoSwtichPathExpOneAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, photoSwtichPathExpTwoAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, photoSwtichPathExpThreeAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adMatrix, merchantProductJumpJinNiuLandingPageWhiteAccount)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adMagicSite, qywxCustomerAcqDirectJumpBlackList)
  DEFINE_PROTOBUF_NODE_KCONF(UnionMidPageWhiteConfig, ad.universeApi,
                                              aggregatePageProductNameAccountWhiteList)
  DEFINE_PROTOBUF_NODE_KCONF(SearchSmartCoverConfig, ad.frontserver,
                                              searchSmartPhotoCoverConf)
  DEFINE_PROTOBUF_NODE_KCONF(SearchTabQueryInterveneConf, ad.frontserver,
                                              searchTabQueryInterveneConf)
  DEFINE_PROTOBUF_NODE_KCONF(HotSpotAvoidBigVConfig, ad.frontserver2,
                                              searchHotSpotAvoidBigVConf)
  DEFINE_PROTOBUF_NODE_KCONF(SearchKboxReleThreshConf, ad.frontserver, searchKboxReleThreshConf)
  DEFINE_PROTOBUF_NODE_KCONF(MerchantMultiQueueConf, ad.frontserver, merchantMultiQueueConf)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, searchHotSpotRanTypeSet)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disableComboSearchDelivery, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSearchHotSpotBgTask, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSearchHotSpotBgTaskOpt, true)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchImageTextCardMinVersion, "12.4.10")
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableAliPredict, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableCheckFanstopCampaignSupport, true)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, smartComputePowerRedisKeyLifeTime, 3600);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, smartComputePowerOfflineRandomMax, 100);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, smartComputePowerRedisTimeOut, 10);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableExpSplitReq, false);

  // 跳过计费分离摸底实验单元尾号
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, skipBillingSeparateUnitTail);

  // 外循环不顶价策略相关
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, outerloopGspSkipSameAgentBidtypeSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, outerloopGspSkipSameAgentOcpxSet);

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, unifyGridUnitIdSubpageid);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, unifyGridUnitIdPageid);

  // 短剧 C 补贴实验尾号
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver2, adCbuExpTail, "100;;;");
  // 粉条计费分离保成本相关
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer, fanstopLiveCapExpAccountWhitelist, "");
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, fanstopBsCapExpWhitelist);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.bidServer, fanstopBsCapCustombidMap)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 2 >= IMPL_BLOCK_BEGIN and 2 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_BOOL_KCONF_NODE(ad.bidServer2, enableFanstopBsCurrentCpaBase, false);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, fanstopTestWhitelistOcpx);
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, fanstopBsRatioTail, "");
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver2, fanstopBsRatioForSptail);
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver2, fanstopBillingOnCostRatioTail, "");
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, fanstopBsOcpxBlacklist);
  // 粉条降成本实验计费调整相关
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, fanstopPrmsDiscountMap);

  // 移动端短视频涨粉计费调整相关
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, enableFanstopFollowPriceTail, "100;;;");
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, fanstopFollowBoundtail);
  // 粉条 server show 是否清空用户兴趣数据
  DEFINE_BOOL_KCONF_NODE(ad.adserver, newUserWithRegisterTimeEmpty, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, knewsNewUserWithRegisterTimeEmpty, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, knewsNewUserUserFirstInstallTime, true)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, closeTimeMinute, 5)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disableInspireLiveRefreshTimes, true)
  DEFINE_INT64_KCONF_NODE(ad.adserver, defaultFeedHighestPos, 4)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFillFullLinkSimpleTrace, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disableFillFullLinkSimpleTraceForMixInFront, false)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, fanstopTagFreqControlServerDataMinutes, 5)

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, traceLogUserDeviceInfoWhiteUserList)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, InspireLiveAdNumPerPv, 5);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, InspireNormalFeedLiveAdNumPerPv, 20);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, randomSkipEmptyCache, 0);
  DEFINE_INT32_KCONF_NODE(ad.adserver, fanstopCandidateKeepSamples, 19)
  DEFINE_INT32_KCONF_NODE(ad.adserver, defaultThanosPageSize, 6)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, splashPrefetchExtraTimeoutMs, 0);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, validDeliverBidTransAttrNameList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, pecRewardedInfoSubPageIdList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, hcRewardedCoinUserInfoSubPageIdList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, EcomUserCouponInfoSubPageIdList)
  DEFINE_JSON_NODE_KCONF(ad.adserver, webcardInfo)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableComponentCard, false);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, componentCard);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, webViewVersionConfig);
  DEFINE_JSON_NODE_KCONF(ad.adserver, nebulaInspireAdConfig);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, knewsEffectSplashConfig);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, nearbyDomesticLiveText, "推广");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, nearbyLivePkMinVersion, "7.7.10");
  // todo(cuiyanliang):  这俩等客户端上线再配置
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, nebulaIosNearbyLivePkMinVersion, "");  // 3.0.10
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, nebulaAndroidNearbyLivePkMinVersion, "");  // 3.0.20
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, xifanFeedInnerLoopMinVersion, "1.10.1.1");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, xifanInspireInnerLoopMinVersion, "1.11.1.1");

  // [wucunlin] cid 预览用户白名单
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.frontserver, CidQcpxPreviewMAP)

  // [zhangxingyu03]
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, incentiveCoinWidgetAdmitSubPageIds)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, incentiveFeedInteractiveFormSubPageIds)

  // [zhangwei26]
  DEFINE_INT64_KCONF_NODE(ad.frontserver, authorRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, maxAuthorRankingLisSize, 100)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adtarget, adAuthor2Author)

  // [jiangjiaxin]
  DEFINE_INT64_KCONF_NODE(ad.frontserver, outerIndusRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, maxOuterIndusRankingLisSize, 100)

  // [cuihongyi]
  DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableInnerInspireLiveDarkFixAccountTail, false)
  DEFINE_TAILNUMBERV2_KCONF(ad.adtarget3, innerInspireLiveDarkFixAccountTail, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, fixMobile2SpecialityAccountTail, "100;;;")

  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableProductNameRevealBottom, false)

  DEFINE_BOOL_KCONF_NODE(ad.frontserver, openKnewsDisableProgrammaticAd, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUnloginServerShow, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, closeLargeCreativesPlayed, false)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, coverVideoPassCoverUrlBlackList)

  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableDefaultStyleForwardNode, false)  // 主站样式正排请求开关
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUniverseOverseasFlowFilter, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableMutipleAdMixRank, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFillBrowseModeV2, false)
  // 为 true，就独占请求, false，就和人 pk
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, previewReqStyleServer, true)
  // int32: (namespace , node_name, default_value)
  // 是否 SlIDE 模式也要加 ad_request_times
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableKuaishouUnlogInTraffic, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableNebulaUnlogInTraffic, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, testFanstopPvRecord, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableAddBackUrl, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFollow, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableAdClientRerank, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableNebulaCommentActionbar, true)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, emptyDataSize, 10);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, asyncRouterRequestUtilThreadNum, 30);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableMerchandiseInfo, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, clickUrlToPlay3rWhiteAccount)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, merchantSmallAndMedieumAccountIdSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, hardFilterAdmitInvalidFlagSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, reflowPosIdList)
  DEFINE_PROTOBUF_NODE_KCONF(UniverseSkipAdmitFilterId, ad.frontserver, universeSkipAdmitFilterId)
  DEFINE_PROTOBUF_NODE_KCONF(UniversePhyPosClusterSignConf, ad.frontserver, universePhyPosClusterSignConf)
  DEFINE_PROTOBUF_NODE_KCONF(SiXinServicePageComponent, ad.frontserver2, siXinServicePageComponent)

  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, knewsSkipSplashScreen)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, ipv6RealtimeSplashInfoWhiteProductList)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, convertUnitMigrate, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUserKwaiShopAddr, true)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableRequestSceneForTrack, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableIosUAReplace, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableRtaRequestIdReplace, true)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, operationOriginAccountSet)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableOperationOrigin, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUniverseKeyWordFilter, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUniverseMaterialInfoFilter, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableFanstopSocialReason, true)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 3 >= IMPL_BLOCK_BEGIN and 3 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disableMerchantDescription, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableBiznameControl, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableOpenTest, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableCleanOnlineJoin, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableCleanJsonData, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableCleanFanstopExtInfo, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disableFakeLaneReq, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, shutdownAdFlowType)
  DEFINE_DOUBLE_KCONF_NODE(ad.adserver, nebulaThanosFilterRate, 0.5)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disableNewFans2DspLogFlow, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, adCleanChargeInfoSwitch, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableLiveAgent, false)
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, enableEspAgent, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableAddPkNodePreview, true)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableFanstopAuctionLog, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableAllGalaxyFillH5, true)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, minPriceRaiseRatio, 1.05);
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, maxPriceRaiseRatio, 1.50);
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, campaignGimbalDynamicTail, "100;;;")
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, campaignGimbalDynamicOcpx)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, emptyCacheAppIdBlackList)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, disableWeakWaitReady, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableFrontParallelInitProcessor, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableFrontGracefulShutdown, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableKthp, false)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, maxStartWaitTime, 60000)
  // 是否过滤含有不合法必填字段 的 adLog
  DEFINE_STRING_BOOL_HASH_MAP_KCONF(ad.frontserver, enableNodeRebuildAdListConfig)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, useExtendSticker, true)
  DEFINE_JSON_NODE_KCONF(ad.frontserver, enableKspGroupCheck)
  // 新客户打折 AB 功能
  DEFINE_PROTOBUF_NODE_KCONF(NewProductDiscount, ad.adserver, newProductDiscount);

  // user_profile 降级开关
  DEFINE_INT32_KCONF_NODE(ad.frontserver, userProfilePassRatio, 100)
  // 快看点阈值上拉刷行控制
  DEFINE_INT32_KCONF_NODE(ad.frontserver, refreshNumCtl, 2)
  // 极速版电商任务流量接入开关
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableNebulaTaskEntraceTraffic, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableNebulaTaskEntraceRecall, false)
  // 快看点流量接入开关
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableKnewsTraffic, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFilterInvalidForwardResponse, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableEyemaxLog, false)
  // 用户体验框架配置
  DEFINE_PROTOBUF_NODE_KCONF(UeqInfoConfig, ad.frontserver, ueqInfoConfig);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enablePrintZeroImpReq, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableArchimedesCandidate, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSendInnerLoopCandidate, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disableSendEmptyInnerLoopCandidate, true)

  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableChargeInfoPrint, false)
  DEFINE_JSON_NODE_KCONF(ad.adserver, adChargeInfoRemoveFileds);
  DEFINE_PROTOBUF_NODE_KCONF(AntiSpamBlacklist, ad.adData, adAntiSpamRequstBlackIds);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adserver, spamFilterDeviceWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, allowTraceTimeCostToServerShowLog)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, nativeSourceAdPreviewCreativeList)
  DEFINE_KCONF_NODE_LOAD(AntiSpamConfig, ad.adData, antispamRequestRules)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, spamFilterPosIdWhiteList)
  DEFINE_JSON_NODE_KCONF(ad.adxCommon, adxWriteUserCreativeList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.dpaServer, dpaWhiteUsers)
  DEFINE_JSON_NODE_KCONF(ad.adserver, newAdDspWhiteUserAccountMap)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.splashserver, brandSVIPUser);
  DEFINE_INT32_KCONF_NODE(ad.adserver, stressTestAdmitRatio, 0)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, newNebulaInspireAdEnabledAdmitFilterConfig)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, newSmallGameInspireAdmitFilterConfig)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, dspChannelConfig)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, wechatGameBlackList)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, ButtonCLickCpaAuthorSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, cpmSpeedPayerTailSet)  // 速推 CPM 计费实验白名单
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank, fanstopBrandClientShowPayerSet, "100;;;");  // 粉条品牌曝光计费白名单
  DEFINE_TAILNUMBERV2_KCONF(ad.adRank2, t7ROISkipBillingSeparateUnitTail, "100;;;")  // roi7 跳过计费分离尾号
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adRank, LeadsSubmitCpaAuthorSet);
  // 是否对新的同城页不出广告的刷次跳过请求 adServer
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSkipAdServerForNewNearby, true)

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, eyeMaxCityBlackListAdFilter)

  DEFINE_INT64_KCONF_NODE(ad.adserver, minFanCount, 100000)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableWriteResponseCheckLog, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableAccountExp, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableReplaceCoverStickerTitle, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enablePlatformNewCustomer, true);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableInnerLoopUserData, true);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, testSceneAccountList)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, invalidFiledWriteLog)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUpdateUniversePositionOnlyUniverse, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUniverseAdDspPosP2pQuery, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableHalfPageType, false);
  DEFINE_INT32_KCONF_NODE(ad.adserver, strictWaitTime, 0)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, disableBgTaskStop, false)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, dotSizeSampleRate, 1);

  // universe 展示形态动态配置开关
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUniverseDynamicAdStyle, true)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 4 >= IMPL_BLOCK_BEGIN and 4 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_BOOL_KCONF_NODE(ad.adtarget, openPlayable_3_0, true);

  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.frontserver, unitIdTestPrice)
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.frontserver, subPageIdCloseTimeMinute)
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.frontserver, huaweiAdBlockerMap)
  DEFINE_LIST_NODE_KCONF(std::string, ad.frontserver, huaweiDeviceList)
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.frontserver, billTimeConfigMicroApp)
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.frontserver, kmovieAppVersionFilterByPos)

  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, universeMarketWhiteProductList)

  // 联盟聚合买量任务
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUniverseAggreTask, false);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, universeAggreTaskLogFreq, 1000)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adAutoParam, dcaf_biz_sub_page_id)

  DEFINE_PROTOBUF_NODE_KCONF(UniverseGenericFlowControlConfig, ad.frontserver,
      universeGenericFlowControlConfig);
  // 开屏相关
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, omitUserIdAppSet)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 5 >= IMPL_BLOCK_BEGIN and 5 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_PROTOBUF_NODE_KCONF(SplashPrefetchRecordWhiteList, ad.frontserver, splashPrefetchRecordWhiteList);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, splashPrefetchRecordSamplingRatio, 100)
  DEFINE_INT32_KCONF_NODE(ad.adserver, adDspFilterInvalidLlsid, 1)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, nebulaExploreSwitch, 100)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, nebulaNearbySwitch, 100)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, logReqDetailFrequency, 10000)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, logDisplayInfoNullFrequency, 100)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, addDpaButtonControlValue, -1);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, delDpaButtonControlValue, -1);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, defaultUniverseDynamicAdStyle, "{}")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, inspireAppUniverseDynamicAdStyle, "{}")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, inspireNoAppUniverseDynamicAdStyle, "{}")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, inspireJinniuUniverseDynamicAdStyle, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, kuaishouAppDownloadUrl, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, kuaishouIosAppDownloadUrl, "")
  DEFINE_INT32_KCONF_NODE(ad.frontserver, pearlAdNumPerPv, 1);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, nebulaNearbyAdNumPerPv, 1);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, splashMonitorUnit);  // 开屏监控的 unit
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, idMappingOnlineKessName,
                                        "reco-id-mapping-service-dryrun")
  DEFINE_INT64_KCONF_NODE(ad.frontserver, idMappingClientTimeOutMs, 20)  // id_mapping 广告单独配置超时
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, idMappingDefaultKey, "uid_did_mapping_v2")
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, requestIdMappingOnlineSwitch, "100;;;")
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, outerloopDncCemExploreParam);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.brandserver, adBrandContentValidPosId)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.brandserver, adBrandPinpaiValidPosId)

  // 联盟直播 author 白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, universeLivingAuthorWhiteList);

  DEFINE_KCONF_NODE_LOAD(UniverseAdSwitch, ad.frontserver, universeAdSwitch2);
  // 小店通点击头像跳转落地页白名单
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, headIconLandingPageUnitIDSet)
  // int64: (namespace , node_name, default_value)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, fanstopAdFlowRatio, 0);        // 新粉条流量
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFanstopV2ChargeActionList, false)  // 新粉条多计费点开关
  DEFINE_INT64_KCONF_NODE(ad.frontserver, availableFanstopBrowseInfoDays, 7);  // 粉条流量记录有效期
  DEFINE_INT64_KCONF_NODE(ad.frontserver, availableFanstopLiveBrowseInfoDays, 1)  // 直播粉条浏览记录有效期
  DEFINE_INT64_KCONF_NODE(ad.frontserver, availableFanstopAuthorBrowseInfoDays, 1)  // 作者维度频控记录有效期
  DEFINE_INT64_KCONF_NODE(ad.frontserver, availableFanstopTagBrowseInfoDays, 1)  // 作者维度频控记录有效期
  // 粉条解析客户端浏览记录最大长度
  DEFINE_INT64_KCONF_NODE(ad.frontserver, fanstopMaxClientBrowsedLength, 700)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableTruncateNativeBrowsedLength, true)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, commentDisableAccountList)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, h5Url, "")
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUniverseUploadH5Url, true);  // 允许使用内容场景广告主上传 url
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, universeDefaultH5Url, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, universeDefaultBuildH5Url, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, knewsDefaultH5Url, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, kuaishouInspireDefaultH5Url, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, kuaishouSmartMidPageH5Url, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, kuaishouSmartMidPageV2H5UrlT1, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, kuaishouSmartMidPageV2H5UrlT2, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, kuaishouSmartMidPageV2H5UrlT3, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, kuaishouInspireSmartMidPageV2H5UrlT1, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, kuaishouDefaultH5Url,
                                        "https://activity.e.kuaishou.com/ksDownloadPage/") // 兜底中间页
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, microAppDefaultH5Url, "")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, actionBarDefaultColor,
                                        "FE3666");  // action_bar 兜底色
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.frontserver,
                                kuaishouDownloadMidPageUrlTypeMap);  // 系统默认模版 url 与 type 的映射关系
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 6 >= IMPL_BLOCK_BEGIN and 6 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_PROTOBUF_NODE_KCONF(CoverSizeInfoConfig, ad.frontserver, CoverSizeInfo)
  DEFINE_PROTOBUF_NODE_KCONF(ColossusReqInfoConfig, ad.frontserver, ColossusReqInfo)
  DEFINE_PROTOBUF_NODE_KCONF(ColossusReqInfoConfig, ad.frontserver, ColossusOrderReqInfo)
  DEFINE_PROTOBUF_NODE_KCONF(ColossusReqInfoConfig, ad.frontserver, ColossusAutoLiveItemReqInfo)
  DEFINE_PROTOBUF_NODE_KCONF(ColossusReqInfoConfig, ad.frontserver, ColossusVideoItemReqInfo)
  DEFINE_PROTOBUF_NODE_KCONF(ColossusReqInfoConfig, ad.frontserver, ColossusMerchantGoodShowItemReqInfo)
  DEFINE_PROTOBUF_NODE_KCONF(ColossusReqInfoConfig, ad.frontserver, ColossusGoodSearchItemReqInfo)
  DEFINE_PROTOBUF_NODE_KCONF(ColossusReqInfoConfig, ad.frontserver, ColossusSearchActionItemReqInfo)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSetAdPageAttentionButtonControl, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableMultiScreenAd, false);  // 分屏广告开关
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, multiScreenFilterTwoLine, true);  // 分屏广告屏蔽单列形式
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableDpaForwardIndex, true);  // 是否打开 dpa 正排的获取
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFillTraceApiAdScene, true);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUniversePhysicalPosInfo, true);
  DEFINE_INT64_KCONF_NODE(ad.frontserver, brandTempCpm, 0)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, brandTempUeq, 0)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, brandTempltr, 0)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, fanstopTempltr, 100000000)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver2, innerNearlineRequestAdmit);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver2, nearlineKcacheName, "kcache_ecomPhotoInfoKCache")
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, inspireMerchantSubpageConf)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, merchantFlowPageConf)
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.frontserver, subPageIdMaxAdNumMap)
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.frontserver, subPageIdAdNumMap)
  DEFINE_INT64_INT64_HASH_MAP_KCONF(ad.engine, inspireLiveSubpageConf);

  // 粉条上次下发记录的过期时间
  DEFINE_INT64_KCONF_NODE(ad.frontserver, fanstopLastDeliveryTimesExpireHour, 24)
  // 是否记录同城粉条下发记录的刷次信息
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableRecordFanstopLastDeliveryTimes, true);
  // 不需要下发原始 adResult 的 page_id
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, originalAdResultPageIdBlackList);
  // 聚星商广补余订单账户配置
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, juxingSupplementExpAccount)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, livePromotionMinMainVersion, "9.5.20")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, livePromotionMinNebulaVersion, "9.5.20")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adserver, gameCenterMinMainVersion, "7.2.4")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adserver, gameCenterMinNebulaVersion, "1.0.0")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adserver, gameCenterMinIosMainVersion, "7.2.4")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adserver, gameCenterMinIosNebulaVersion, "1.0.0")
  DEFINE_BOOL_KCONF_NODE(ad.adserver, testAccExploreChargeInfo, false)  // 后链路测试开关
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enablePassbackPb, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableAdSocialChargeClick, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableSearchCpcPhotoClick, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFixAppScoreBug, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableTestWechatMiniProgram, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableCardItem, true)
  DEFINE_INT32_KCONF_NODE(ad.adserver, adServerRequestBrandServerRatio, 0)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableTestDisallowDownloadDialog, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableTransportActionbar, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableResetActionbarTime, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSetClickUrl, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enablePassKspGroupCheck, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableJumpToLive, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSetPosInfo, true)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, jumpToLiveWhiteSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, daJianKangIndustryIdSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, dasExtDataWhiteSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, BilingSeparateOcpx)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, SpecialRegionId)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, unitDasDataWhiteSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, creativeDasDataWhiteSet)
  DEFINE_PROTOBUF_NODE_KCONF(AdmitFlowBlackListConfig, ad.frontserver, admitFlowBlackList)
  DEFINE_PROTOBUF_NODE_KCONF(newFlowAdmitConfig, ad.frontserver, newFlowAdmit)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::AdMixComboConfig, ad.frontserver, adMixComboConfig)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PecAdPreviewInfo, ad.frontserver, adPreviewPecRewardedInfo)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PecCouponPreviewInfo, ad.frontserver, adPreviewPecCouponInfo)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PecCouponPreviewInfoV2, ad.frontserver, adPreviewPecCouponInfoV2)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::CommonSmartOfferPreviewInfo, ad.frontserver, adPreviewCommonSmartOffer)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::MerchantPreviewInfo, ad.frontserver, adPreviewMerchantInfo)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::MatrixStylePreviewInfo, ad.frontserver, matrixStylePreviewInfo)
  DEFINE_PROTOBUF_NODE_KCONF(AdFlowGradeConfigConf, ad.frontserver, flowGradeConfig)
  DEFINE_PROTOBUF_NODE_KCONF(DegradeLevelConfigData, ad.frontserver, degradeLevelConfig)
  DEFINE_PROTOBUF_NODE_KCONF(ResourceWhiteBoxFlowTagConfigData, ad.frontserver2,
                             resourceWhiteBoxFlowTagConfig)
  DEFINE_PROTOBUF_NODE_KCONF(FrontRpcDegradeData, ad.frontserver, frontRpcDegrade)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enablePhoneBrandType, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, enableDefaultMacroSet)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableInspireScene, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSearchScene, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableLiveProductCover, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSetSurveyStyleOriginInfo, false)
  DEFINE_LIST_NODE_KCONF(int64, ad.frontserver, surveyIdList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, innerExploreWhitelist)
  DEFINE_PROTOBUF_NODE_KCONF(NoLimitVirtualGoldSceneConfigData, ad.frontserver,
                                  noLimitVirtualGoldSceneConfig)

  DEFINE_INT32_KCONF_NODE(ad.frontserver, operationInspireAdBillTime, 15000);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, riskControlMinUserLevel, 4);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, userAdRiskControlSkipPageIdSet)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 7 >= IMPL_BLOCK_BEGIN and 7 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, splashDebugUserList)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, PsPrepareRecallRouterSceneSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, enableInspireDefaultH5PageSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, adData.realtime, adLogFullOnlineJoinParamsWhiteList)
  //  激励视频
  DEFINE_PROTOBUF_NODE_KCONF(MultiRewardedCoinDataList, ad.frontserver, multiRewardedCoinDataList);
  DEFINE_PROTOBUF_NODE_KCONF(RewardedCoinScalingByAccountList, ad.frontserver,
      rewardedCoinScalingByAccountList);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, inspireManualInnerRiskUserSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, inspireManualOuterRiskUserSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, inspireInnerRiskUserBlackOcpxSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget2, inspireOuterRiskUserBlackOcpxSet);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, IncentiveAdFrontUserRiskModuleWhiteListForDev);

  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandServer, defaultActionBarColor, "168FFF");
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFrontFillLookalike, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFrontCalPaidAudience, false);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, selfServiceAccountPreviewPriceUid);
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.frontserver, incntvAdNextNSubPageFreqControl);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, enableNextNPreviewCoinCurNextCoinDecisionSubPageIds);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, enableIaaHasNextCoinSubPageIds);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, enableInspireTaskCDSubPageIds);
  DEFINE_PROTOBUF_NODE_KCONF(IncntvAdPredictNext1ViewValue2Coef, ad.frontserver,
                             incntvAdPredictNext1ViewCpm2NextPrice);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, enableExtraCoinBudgetAllocationSubPageIds);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, enableFillTaskPageRankScore);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, enableIncntvAdRealtimeProgressSubPageIds);

  // adx 专用
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableAdxUrl, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, adxLargeCreativeList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, aggrXPhotoBlackList)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver,
      jingmeiAdxEnctyprEKey, "e649642505ca45e3af52e92d1764fc19");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, jingmeiAdxEnctyprIKey,
      "d43641e690844e6bade364fc9004f3cc");

  // 搜索广告
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchQueryBlacklist);
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.frontserver, searchSourceWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchQueryNotBrandBlacklist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchQueryBrandBlacklist);
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.frontserver, searchPageIds);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, searchAdNum, 2);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, searchAdPositionOffset, 2);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, searchAdPositionInterval, 6);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, searchStrongStyleUpliftAdPosition, 2);
  DEFINE_PROTOBUF_NODE_KCONF(SearchAdDynamicPositionConfig, ad.frontserver, searchAdDynamicPositionConfig);
  DEFINE_INT64_KCONF_NODE(ad.frontserver, searchUserIdSkipRangeMin, INT64_MAX);
  DEFINE_INT64_KCONF_NODE(ad.frontserver, searchUserIdSkipRangeMax, INT64_MIN);
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, searchReqBrandRate, 0);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSearchCleanSearchInfo, false);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchTraceQueryWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, searchCoverTagSubWhitelist)
  DEFINE_PROTOBUF_NODE_KCONF(SearchDesKeywordSubConf, ad.frontserver, searchDesKeywordSub);

  DEFINE_HASH_SET_NODE_KCONF(std::string, adqa.adCheckEnv, searchCheckQuery);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, rubbishFlowList);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, searchDisableCaptionProductName, true);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, searchMerchantDisableCaptionProductName, true);

  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSearchNewPreiview, false);
  DEFINE_INT64_KCONF_NODE(ad.frontserver, mingtouAckRatio, 10);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchQueryNewWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchQueryBroadBlackList);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchFanstopPreviewQuery, "");
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSearchLiveTabNonLiveFilter, true);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchNewDoubleStyleNotAppVersion, "9.11.32");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchNewDoubleStyleIosTenUpVersion, "10.0.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchNewDoubleStyleAndroidTenUpVersion, "10.0.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchInnerStreamIosVersion, "10.8.30");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchInnerStreamAndroidVersion, "10.8.30");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchLiveProductCoverVersion, "10.2.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, searchKboxMinVersion, "10.9.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchBigvCardOtherTabVersion, "11.10.40");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, xifanKsMiniProgramMarketUri, "");
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, searchServerExpTagBizCodeBySearchSource);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, openSearchSessionIdPrefix, "seweb_");
  DEFINE_PROTOBUF_NODE_KCONF(SearchBigvLiveConfig, ad.frontserver, searchBigvLiveConfig);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, searchBigvLiveFromPageWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, searchBigvLiveOtherSearchSource);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enablesearchEspTrackAll, false);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, searchBigvLiveAuthorIdBlacklist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchBigvLiveQueryBlacklist);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, searchEspTrackWhitelist);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchAppCardAvoidKboxBizNames);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchFormCardAvoidKboxBizNames);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, duanjuAvoidKboxBizNames);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, bigVcardAvoidKboxBizNames);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, bigVcardUpperKboxBizNames);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, bigVcardUpperKboxBizNamesGoodTab);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchFictionAvoidKboxBizNames);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchFictionUpperKboxBizNames);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, livePunishFilterWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, fixDeepConversionTypeWhitelist)
  // gaokaiming
  DEFINE_PROTOBUF_NODE_KCONF(SearchAgeWiseThrottling, ad.frontserver, searchAgeWiseThrottling);

  // 微信小程序
  DEFINE_INT32_KCONF_NODE(ad.frontserver, wechatAdPositionInterval, 3);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, wechatJinniuChannel, "102101102");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, wechatJinniuAddParam, "&channelType=1");

  // qq 小程序
  DEFINE_INT32_KCONF_NODE(ad.frontserver, QQAppAdPositionInterval, 6);

  // 快看点效果开屏支持非下载类广告最低版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, knewsSplahAllowNotAppVersion, "10.0.0")
  // 电商活动流量黑名单
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSpecialtyFlash, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, ecomFestivalBlackUser)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, ecomFestivalBlackRegion)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, ecomFestivalWhiteUser)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, juxingSoftSubPageId)

  DEFINE_INT32_KCONF_NODE(ad.frontserver, nebulaAdjustPosInterval, 2)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, innerExploreAdjustPosInterval, 2)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, directLiveStreamCpmThr, 10)

  // 粉条预览迁移
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enablePreview, false)  // 是否支持预览功能
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableSelectedPreviewExplore, true)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, onlyReadRefreshValueFromRedis, false)
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, followMaxFansBrowseSet, 100)  // 关注页 browse set 长度

  // 动态 pk 使用
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 8 >= IMPL_BLOCK_BEGIN and 8 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.frontserver, userComplaintFilterRatio)

  // 猜你喜欢广告位
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.frontserver, merchantGuessYouLikePageIds);
  // 猜你喜欢切换双列广告位
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver, guessYouLikeFeedSubPageIds);
  // 猜你喜欢双列广告位
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver, merchantGuessYouLikeSubPageIds);
  // 开屏白名单
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver, splashWhiteSet)
  // 开屏频控白名单
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver, splashIntervalWhiteSet)
  // 主站 ios 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKuaishouIos, "6.10.1");
  // 主站 android 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKuaishouAndroid, "6.10.1");
  // 主站 harmony 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKuaishouHarmony, "12.7.40");
  // 极速版 ios 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minNebulaIos, "1.1.1.12");
  // 极速版 android 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minNebulaAndroid, "1.10.2");
  // 极速版 harmony 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minNebulaHarmony, "12.7.40");
  // 开屏特殊机型放开最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minFoldDeviceModVersion, "12.8.20");
  // 不出广告的最小消费
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, livePayDaily, 100.0);
  // 用户体验相关
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableLahuoUserProtect, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableLossUserProtect, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSleepUserProtect, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashExitUserProtect, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableFrontUeNebulaRedisChange, false);

  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSplashNewAdmit, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableRefreshSplashRequest, true);

  DEFINE_INT64_KCONF_NODE(ad.splashserver, splashPreferchInterval, 3600)
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, enableRtbJkMonitor, true);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.adserver2, minSplashRtbJKVersion, "12.3.30");
  // eyemax 视频最长时间
  DEFINE_INT64_KCONF_NODE(ad.splashserver, preloadDurMs, 15000);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSplashEffect, false);
  DEFINE_INT64_KCONF_NODE(ad.frontserver, splashEffectRatio, 100);
  DEFINE_INT64_KCONF_NODE(ad.frontserver, splashAdxCacheHour, 24)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, selectSplashCreativeId, 0)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSplashRtbAck, false)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver, rtbBlackList);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver, splashPDPKAccountWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, rtbBlackRegionSet)
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, disableSplashEffectHarmony, true);
  DEFINE_PROTOBUF_NODE_KCONF(ClickButtonInfo, ad.splashserver, clickButtonInfo);
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.splashserver, interactiveStyleUnitWhiteMap);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableInnerNebulaRelationVideoAdPosReq, false);
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.frontserver, splashProductCacheTime)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.frontserver, splashAccountCacheTime)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.frontserver, adxSplashAccountCacheTime)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, splashGrayCacheTime, 12)
  DEFINE_PROTOBUF_NODE_KCONF(SearchBrandVersionControl, ad.frontserver, searchBrandVersionControl);
  DEFINE_PROTOBUF_NODE_KCONF(SearchSingleLayoutVersionControl, ad.frontserver,
                             searchLayoutSingleVersionControl);
  DEFINE_PROTOBUF_NODE_KCONF(SearchProductShow, ad.frontserver, searchProductShow);
  DEFINE_PROTOBUF_NODE_KCONF(MerchantKwaishopAddr, ad.frontserver, merchantKwaishopAddr);
  DEFINE_PROTOBUF_NODE_KCONF(GlobalTimeoutConfig, ad.target_search, globalTimeoutConfig);
  DEFINE_PROTOBUF_NODE_KCONF(AdQueryUserEmbeddingRedisConfig, ad.frontserver,
                             adQueryUserEmbeddingRedisConfig);
  DEFINE_PROTOBUF_NODE_KCONF(AdQueryUserEmbeddingRedisConfig, ad.frontserver,
                             adQueryLiveStreamTagRedisConfig);
  DEFINE_PROTOBUF_NODE_KCONF(ClientLimiter, ad.frontserver, clientLimiter);
  DEFINE_PROTOBUF_NODE_KCONF(AdPackSpecialAdsConfigData, ad.frontserver, adPackSpecialAdsConfig);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::KwaiGalaxyBrandConfig, ad.frontserver, kwaiGalaxyBrandConfig)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::UniversePlayableConfig, ad.frontserver, universePlayableConfig)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::SplashLightInteractiveConfig, ad.frontserver,
      splashLightInteractiveConfig)
  DEFINE_PROTOBUF_NODE_KCONF(SortParameterConfig, ad.frontserver, sortParameterConfig)
  // AdResponse 含有非法字段时是否过滤的开关
  DEFINE_PROTOBUF_NODE_KCONF(FilterInvalidResponseConfig, ad.frontserver, enableFilterInvalidAdResponse);
  DEFINE_PROTOBUF_NODE_KCONF(FilterInvalidResponseConfig, ad.frontserver,
      enableFilterInvalidFanstopAdResponse);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::LandingPageVersionControl, ad.frontserver, landingPageVersionControl)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::MixedFecherInfo, ad.frontserver, mixedFecherInfo)
  DEFINE_PROTOBUF_NODE_KCONF(HardTargetFilterConfigData, ad.frontserver, hardTargetFilterConfig)
  DEFINE_PROTOBUF_NODE_KCONF(AdPosPremiumConfigData, ad.adtarget, adPosPremium)
  DEFINE_PROTOBUF_NODE_KCONF(OuterloopAbNegFilterRateConfig, ad.adtarget2, outerloopAbNegFilterRateConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adtarget, outerNativeDiffAuthorOpenNeg)

  // map<int64, double>: (namespace, name)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.adserver, paidAudienceChargeRatio)
  // map<string, int64>
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.frontserver, posIdFrequencyControlMap);
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.adserver, newAdDspWhiteUserList)
  DEFINE_STRING_INT64_MAP_KCONF(ad.adserver, abtestForAdForms)
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.frontserver, previewCreativeStyleBind)

  // map<string, int32>
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.frontserver, splashRtaCacheTime)
  DEFINE_STRING_INT32_HASH_MAP_KCONF(ad.frontserver, validFlagFilterTime)
#endif                                            /* auto generated by reset_kconf.py, you can ignore. */
#if 9 >= IMPL_BLOCK_BEGIN and 9 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_KCONF_NODE_LOAD(ks::ad_base::ModelCmdConf, ad.predict, model_cmd)

  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, grpcClientPassRatio);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver, PosAdjustEcpmRatioMap)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, InnerLoopT7RoiSmkvhPriceParam)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver2, shelfMerchantPosMixBenefitMap)

  // map<string, string>: (namespace, name)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, adHolidayMapConfig);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, adPhoneBrandMapConfig);
  // 电商 adx 卡片样式兜底
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, licenseAdslogoMap);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, subPageIdMapping);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adserver, jkAdsActionBarConf)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adxCommon, adxPriceEncodeToken)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, accountExpMap);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, productExpMap);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, accountMulExpMap);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adDiagApi, keyToPosInfo);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adDiagApi, posIdToPosInfo);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, importantAdPositionMap);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, importantAdPositionPosIdMap);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, universeDynamicAdStyle);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, universePlayCardNewStyle);
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.adserver, brand2Detailbrand)
  //短视频点击头像和左滑跳转路径实验版本控制
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PhotoDisplayVerson, ad.frontserver, PhotoDisplayVerson)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PhotoDisplayVerson, ad.frontserver, localStoreVersion)

  DEFINE_PROTOBUF_NODE_KCONF(SearchPreviewConfig, ad.frontserver, searchPreviewConfig);
  DEFINE_PROTOBUF_NODE_KCONF(SearchPreviewConfig, ad.queryRetrieval, searchPreviewConfigV2);
  DEFINE_PROTOBUF_NODE_KCONF(AbTestName, ad.target_search, abTestName);

  // user define data with load:  (type, namespace, name)
  DEFINE_KCONF_NODE_LOAD(AdDynamicCreative, ad.frontserver, adDynamicCreative)
  DEFINE_KCONF_NODE_LOAD(KconfAdpageButtonControl, ad.engine, adpagebuttoncontrol)
  DEFINE_KCONF_NODE_LOAD(NegativeMenusInfoPb, ad.adserver, NegativeMenuInfo)
  DEFINE_KCONF_NODE_LOAD(NegativeMenusInfoPb, ad.adserver, NebulaNegativeMenuInfo)
  DEFINE_KCONF_NODE_LOAD(NegativeMenusInfoPb, ad.adserver, SetUpNegativeMenuInfo)
  DEFINE_KCONF_NODE_LOAD(NegativeMenusInfoPb, ad.frontserver, knewsNegativeMenuInfo)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 10 >= IMPL_BLOCK_BEGIN and 10 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  // 新的测试白名单配置
  DEFINE_KCONF_NODE_LOAD(TestWhiteList, ad.frontserver, testWhiteList)
  // 电商使用
  // 电商直播推广样式配置
  DEFINE_KCONF_NODE_LOAD(MerchantLiveStyleConf, ad.frontserver, merchantLiveStyleConf)
  DEFINE_PROTOBUF_NODE_KCONF(MerchantPhotoStyleConf, ad.frontserver, merchantPhotoStyleConf)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::MerchantRpcConfig, ad.frontserver, merchantRpcConfig)
  DEFINE_PROTOBUF_NODE_KCONF(KnewsEffectSplashImageUrlsConfig, ad.frontserver, \
                              knewsEffectSplashImageUrlsConfig)

  DEFINE_PROTOBUF_NODE_KCONF(kconf::AccountAllowedDeeplinkPrefix, ad.frontserver, allowedDeeplinkPrefix);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PreviewConfig, ad.frontserver, tempPreviewConfig);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PreviewConfig, ad.frontserver, knewsPreviewWithAppUserId);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PreviewConfig, ad.frontserver, tempFanstopPreviewConfig);

  DEFINE_KCONF_NODE_LOAD(MerchantProfileAdConfig, ad.frontserver, merchantProfileAdConfig)
  DEFINE_KCONF_NODE_LOAD(TestWhiteListUniverse, ad.frontserver, testWhiteListUniverse)
  // fanstop config
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableNebulaIOSFanstopLiveDelivery,
                         false)  // 是否开启极速版 IOS 投放
  DEFINE_INT32_KCONF_NODE(ad.fanstopServer, thanosInnerFreshStep,
                          5)  // 主站单列发现页内部粉条刷次间隔
  DEFINE_INT32_KCONF_NODE(ad.fanstopServer, thanosNormalFreshStep,
                          6)  // 主站单列发现页外部粉条刷次间隔
  DEFINE_INT64_KCONF_NODE(ad.fanstopServer, selectedLiveTimeStep, 1200)
  DEFINE_INT32_KCONF_NODE(ad.fanstopServer, selectedTabInnerFanstopVVStep,
                          14)  // 精选页内部粉条的间隔 vv 数  // NOLINT
  DEFINE_INT32_KCONF_NODE(ad.fanstopServer, selectedTabInnerFirstPos, 31)  // 精选页第一个出内部粉条的位置

  // set<int64>
  // 内循环预览专用，这些 visitor 进行预览时，被强行标记为硬广
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 11 >= IMPL_BLOCK_BEGIN and 11 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, innerLoopPreviewHardQueueVisitorList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, adServerRequestBrandServerWhiteList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, dynamicRegionList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, dedumpAuthorDiscountAuthorList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, headCustomerDiscountAuthorList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, callbackAccountSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, disableApplinkBackUrlDeepLinkPrefix)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, disableApplinkBackUrlAccountList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, notAllowOpenProfile)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, wechatMiniProgramAccountSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, disallowDownloadDialogAccountSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.fanstopServer, innerRefreshCitySet)  // 内粉进行刷次实验的城市
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.fanstopServer,
                        fanstopRecordRefreshRequestTimesPageList)  // 粉条自己记录刷次信息的页面
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, brandBiddingPkGroups)  // 品牌效果 pk 的分组
  // 品牌和效果 pk 时信息流 cpm 的调整系数
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, dspCpmAdjustRatioForPk, 1.25)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver2, brandInnerExploreMinVersion, "12.11.20");
  DEFINE_PROTOBUF_NODE_KCONF(kconf::FrontToBrandCommonAttrList, ad.frontserver2, frontToBrandCommonAttrList);

  DEFINE_INT32_KCONF_NODE(ad.fanstopServer, selectedTabFreshVVCount, 6)  // 精选页每一刷的 vv 数量

  // 软广去标字段传给 deliver info
  DEFINE_JSON_NODE_KCONF(ad.adMix, softAdDisableAdMarkWithReviewJson);
  // frontserver 透传给 ad_pack 的数据清理
  DEFINE_JSON_NODE_KCONF(ad.frontserver, transparentAdServerShowLogClean);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, transparentAdServerShowLogCleanV2);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, transparentFanstopServerShowLogClean);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, transparentAdAckInfoClean);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, transparentTraceAlwaysLogClean);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, adPackAdResultClean);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, adPackAdRequestClean);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, adPackForSearchMixRankClean);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, adRequestUserProfileLogWhiteList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, adRequestUserProfileLogFlowBlackList);
  DEFINE_JSON_NODE_KCONF(ad.frontserver, adSearchInfoConfig);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableTestLog, false)
  DEFINE_BOOL_KCONF_NODE(ad.engine, enableAbMappingId, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFixContextDataClear, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enablePlayEndInfoRevealBottom, false)

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adserver, fakePhotoIdSetForMerchantLive)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableAllAdPck, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableAllAdMixed, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableBackUrlConvert, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableLogRequestError, false);

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, universeTencentRtbPosSet)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableNewBaseIdInit, true)
  DEFINE_LIST_NODE_KCONF(std::string, ad.frontserver, commonStrategyFilterList)
  DEFINE_LIST_NODE_KCONF(std::string, ad.frontserver, splashStrategyFilterList)
  DEFINE_LIST_NODE_KCONF(std::string, ad.frontserver, hardEnterList)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, hardFilterSceneSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, defaultHardFilterSet)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, defaultInspireFilterSet)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, newAdlibMergeSceneSet)
  DEFINE_PROTOBUF_NODE_KCONF(AdDBTypeConfig, ad.frontserver, adDBTypeConfig)
  DEFINE_PROTOBUF_NODE_KCONF(ADDBMergeConfig, ad.frontserver, adDBMergeConfig)
  DEFINE_PROTOBUF_NODE_KCONF(BillingSeparateAccountExpConf, ad.frontserver, billingSeparateAccountExpConf);
  DEFINE_INT64_KCONF_NODE(ad.frontserver, LongTailTimeThreshold, 400000)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, AdjustEcpmPosLimit, 13)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::EngineTraceLogSceneRateMap, ad.engine, engineTraceLogSceneRateMap)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, simplifyTraceLogUserWhiteSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, simplifyTraceLogSubPageIdWhiteSet)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, chargeTagBlackList)

  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.adserver2, WechatUrlWhitelist)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, reqVisitorConvertRedisRatio, 0)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, fanstopServerBrowsedTTLMinutes, 20)

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, clientDebugUserWhitelist)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, clientDebugUserWhiteTestlist)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, InvalidAgeSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, InvalidRegionSet)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, InvalidOrientationSet)
  // 新计算框架节点配置
  DEFINE_PROTOBUF_NODE_KCONF(ServerStrategyConf, ad.frontserver, commonAdmitStrategyConf)
  DEFINE_PROTOBUF_NODE_KCONF(ServerStrategyConf, ad.frontserver, fanstopAdmitStrategyConf)
  DEFINE_PROTOBUF_NODE_KCONF(ServerStrategyConf, ad.frontserver, adFilterStrategyConf)
  DEFINE_PROTOBUF_NODE_KCONF(kconf::BossModePreviewInfo, ad.frontserver, bossModeAdPreview)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableLivePromotionNewTrack, false)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, adserverFlowRouterPercentage, 0)

  DEFINE_PROTOBUF_NODE_KCONF(SkipCommonAdmitConfig, ad.frontserver, skipCommonAdmitConfig)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableAcCreativeMicroReplace, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableDumpPeerInfo, false)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, traceLogDumpRate, 10000)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableTraceLogDump, false)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, PsPrepareSceneSet)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableFollowFreqUp, false)

  DEFINE_BOOL_KCONF_NODE(ad.frontserver, mergeInnerLoopUsedItem, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableMaterialSelectorOcpxWhite, false)
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.frontserver, materialSelectorOcpxWhiteSet);

  // 长播打点
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.fanstopServer, livePlayedmsSet)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.fanstopServer, assoPlayedmsSet)
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.frontserver, liveTotalPlayedmsSet)

  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableUserRequestHistory, true);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, userRequestHistoryMaxLength, 15);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, userRequestHistoryExpireDays, 5);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, userRequestHistoryBlackSubPageList)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, AvgCvrLeadsCoeff, 0.2);
  DEFINE_PROTOBUF_NODE_KCONF(ClueAggrCpmPriceRatioConf, ad.frontserver, clueAggrCpmPriceRatioConf);

  DEFINE_INT32_KCONF_NODE(ad.frontserver, universeTrafficMaxLevel, 50);
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.frontserver, cpmByTime);

#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 12 >= IMPL_BLOCK_BEGIN and 12 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, sendUserProfileTailNumber, "100;;;");
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espMobileNativeBsTail, "10;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.bidServer2, espMobileLiveDeepBsPriceRatio, "10;;;")

  // 新粉条激励直播兜底

  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, inspireConvStyleSubPageIdSet)

  // front server 打印 node_cost
  DEFINE_INT32_KCONF_NODE(ad.frontserver, showNodeTimecostRatio, 0);
  //
  // auction 参数
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, industryLivePriceAccountSet)
  DEFINE_DOUBLE_KCONF_NODE(ad.frontserver, industryLivePriceRatio, 1.0)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, smallGamePriceRatio, 1.2)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableCpmGfp, false)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, searchPriceOverBid, 1.0)
  DEFINE_INT64_KCONF_NODE(ad.adRank, rankingListExpireHours, 72)
  DEFINE_INT64_KCONF_NODE(ad.adRank, splashRankingListExpireHours, 72)
  DEFINE_INT64_KCONF_NODE(ad.adRank, rewardedRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, xdtAmdLiveRankingListExpire, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, nativeRankingListExpire, 72)
  DEFINE_INT64_KCONF_NODE(ad.adRank, ecomRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, adHostingRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, adJkAliouterRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, adRewardedRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, dpaRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adRank, sdpaRankingListExpireHours, 168)
  DEFINE_INT64_KCONF_NODE(ad.adserver2, universeBoostRankingListExpireHours, 24)
  DEFINE_INT64_KCONF_NODE(ad.adserver2, maxAmdLiveRankingBucketSize, 10007)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, SysMaxPrice, 100000)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, SysMaxCpaPrice, 100000)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, SysMinPrice, 1)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.splashserver, brandWeiliangUnitId);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.splashserver, brandWeiliangOderId);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.splashserver, brandWeiliangAccountId);
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableCollectColdStartData, true)
  DEFINE_INT64_KCONF_NODE(ad.adRank, maxAdCommonCount, 400)
  DEFINE_DOUBLE_KCONF_NODE(ad.bidServer, auctionLogSendRatio, 0.0)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, enableProduceMpcLog, false)
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank, produceMpcLogRatio, 0.0)
  DEFINE_BOOL_KCONF_NODE(ad.adRank, produceMpcLogDirect, false)
  DEFINE_INT32_KCONF_NODE(ad.adRank, sendMpcLogRatio, 100)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableAuctionRecordDirect, false)
  DEFINE_INT32_KCONF_NODE(ad.adserver, auctionLogRecordRatio, 100)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, skipFilterDupCover, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enablePvSecondIndustryFilter, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableSkipStorewideLiveBsTail, false)
  DEFINE_KCONF_NODE_LOAD(OnlinePriceDiscountConfig, ad.adserver, onlinePriceDiscountConfig)
  DEFINE_KCONF_NODE_LOAD(OnlinePriceDiscountConfig, ad.frontserver, innerPriceDiscountConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, t7roasSkipBillingSeparateUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, skipBsAutobidPriceRatioUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, enableLiveHostingPhotoToRedisCampaignAndScene);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, eopSkipBillingSeparateUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, storewideSkipBillingSeparateCampaignTailRank)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, pcLiveSkipHardPriceProtectUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, pcLiveAllPageBsUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, itemCardPhotoBsUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, storewideBsCampaignTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, merchantStorewideBsCampaignTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, skipStorewideLiveBsTail)
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, liveHostingBsTail, "100;;;");
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, InnerLoopBsRatioTransToPackUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank3, InnerLoopBsRatioTransToPackCampaignTail)
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.frontserver2, innerMultiMixBuyerConf)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, smbSkipBillingSeparateUserTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, smbSkipBillingSeparateAccountTail)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 13 >= IMPL_BLOCK_BEGIN and 13 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, newCustomerBsTails)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer, storewideColdStartTail)
  DEFINE_INT64_STRING_HASH_MAP_KCONF(ad.bidServer2, storewideColdStartState)
  DEFINE_INT64_KCONF_NODE(ad.bidServer2, costDeltaMaxSecond, 600)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, pureNewCustomerBSTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, pureNewCustomerGMVBSTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, selfServiceBsTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.bidServer2, inspireLiveUsePriceUnitTail)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, DisableBonusDiscountProjectList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, bonusTransferProjectList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adFrontDiffSwitches, inspireMiddlePageSubPageId)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, projectBonusTransferDateList)
  DEFINE_PROTOBUF_NODE_KCONF(SensitiveThirdCategoryConfig, ad.adRank2, sensitiveThirdCategoryConfig)
  DEFINE_PROTOBUF_NODE_KCONF(ChargePriceDefenderConfig, ad.adRank, chargePriceDefender)
  DEFINE_PROTOBUF_NODE_KCONF(KnewsTimeOutConfig, ad.engine, knewsTimeOutConfig);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableAddFanstopChargeTag, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, disableFanstopInspireLiveChargeTag, true);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, P3sTrackUrlWhiteAccount)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, P3sTrackUrlWhitePageId)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, elementClickTrackUrlCampaign)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, elementClickTrackUrlPage)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, livePlayStartedTrackClickPosId)
  DEFINE_LIST_NODE_KCONF(int32, ad.frontserver, wechatGameDirectJumpAreaEnumList)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, styleMaterialPicCardWidth, 800);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, styleMaterialPicCardHeight, 450);
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableOptimizeTime, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableSkipCoverFilter, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, serverShowFilterPressReq, true)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, aggregateLeadMaxAdReturn, 6);
  DEFINE_INT64_KCONF_NODE(ad.frontserver, merchantFlowMaxAdReturn, 10);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, RewardedCoinGlobalList);
  DEFINE_PROTOBUF_NODE_KCONF(PlayAbleSdkMinVersionConfig, ad.adtarget, playAbleSdkMinVersionConfig)
  DEFINE_KCONF_NODE_LOAD(AppStoreInReview, qa.app-release, appstore_in_review)
  DEFINE_PROTOBUF_NODE_KCONF(CommonDyeingEraseConfigData, ad.frontserver, commonDyeingEraseConfig)
  DEFINE_INT64_DOUBLE_HASH_MAP_KCONF(ad.frontserver, cidPriceDiscountConfig)
  DEFINE_INT64_KCONF_NODE(ad.frontserver, universeTinyQueryRecalledProductNum,
                          50);
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, rtaRequestFieldOptRatio, "100;;;")
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 14 >= IMPL_BLOCK_BEGIN and 14 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_JSON_NODE_KCONF(ad.frontserver, rtaRequestFieldConfig)
  DEFINE_JSON_NODE_KCONF(ad.frontserver, recoUserActionListFieldConfig)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.rta, rtaInvalidIdfaSet)
  DEFINE_STRING_KCONF_NODE(ad.frontserver, pddRtaWinNoticeUrl)
  DEFINE_STRING_KCONF_NODE(ad.frontserver, aliOuterWinNoticeUrl)
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver, rtaWinNoticeUrl);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, convertPriceAdxSourceTypeSet)
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver, NativeAdFlowBlackList)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, AdReservePirceAuthors)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, AdReservePirceActions)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.fanstopServer, hetuFilterDupTagSet)  // 粉条河图标签去重集合
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver, aggregateLeadFlowSet)  // 线索多卖聚合页 sub page id 集合  // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver, personalizedFlowSet)  // 创作者个人页相关 sub page id 集合  // NOLINT
  DEFINE_INT32_KCONF_NODE(ad.frontserver, clickAfterWordsMaxlen, 3);
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.adserver, merchantSupportPhotoPageIds)
  DEFINE_LIST_NODE_KCONF(int32, ad.adStyleServer, inspireLiveCoinRandomConfig)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, merchantFollowListSubpageIds)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, searchEntrySourceNoAds)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver, searchMidPageVersion, "11.9.40")
  DEFINE_PROTOBUF_NODE_KCONF(PecMultiCouponConfig, ad.adRank, pecMultiCouponConfig)
  DEFINE_PROTOBUF_NODE_KCONF(PecMultiCouponConfig, ad.adRank, livePecMultiCouponConfig)
  DEFINE_PROTOBUF_NODE_KCONF(PecMultiCouponConfig, ad.adRank, p2lPecMultiCouponConfig)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, disableExposedFieldSet)
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, enableAnnPrefetchNotify, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, boostBidControlAccountTail, "100;;;")
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver, merchantRoasGspTail, "100;;;")
  DEFINE_BOOL_KCONF_NODE(ad.frontserver, enableNewinnerMerchantReason, false)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, recoAfterClickBlackQuery)  // 点后推黑名单
  DEFINE_PROTOBUF_NODE_KCONF(PremiumSoftAdReviewConfig, ad.adMix, softAdDisableAdMarkWithReviewConf)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, guidedFeedbackSoftPhoto)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, dacExpOcpxActionType)
  DEFINE_PROTOBUF_NODE_KCONF(RankBenifitDistributionConf, ad.frontserver, rankBenifitDistributionConf)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, wanheChargeActionTypeSubPageId);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, maxInnerInvoTrafficRankingListSize, 50)
  DEFINE_PROTOBUF_NODE_KCONF(InnovationTrafficEcpcConfigUpdateV3, ad.adRank2,
                             innovationTrafficEcpcConfigUpdateV3);
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 15 >= IMPL_BLOCK_BEGIN and 15 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, frontTrafficCopySubPageId);
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, frontTrafficCopyPageId);
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.adRank2, innovationTrafficRerankFairness);
  DEFINE_PROTOBUF_NODE_KCONF(ExploreFeedMixCpmConf, ad.frontserver2, exploreFeedMixCpmConf);  // 发现页混排 cpm uplift 策略 // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(ExploreLiveScoreConf, ad.frontserver2, exploreLiveScoreConf);  // 发现页直播报价分参数 // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(FeedMixScoreConf, ad.frontserver2, feedMixScoreConf);  // 发现页直播报价分参数 // NOLINT
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver2, innovationTrafficRerankEcpm);  // 发现页内流 ecpm rerank // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(ExploreExperienceConf, ad.frontserver2, exploreExperienceConf);  // 发现页体验指标统计值 // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank2, gamePayModeAccountList);
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, smallGameForceDirectWhiteList);  // 微信小游戏直跳/二跳实验白名单  // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver, smallGameForceDirectBlackList);  // 微信小游戏直跳/二跳实验黑名单  // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, nativeGspProtectAccountTail)  // 软广二价保护尾号名单
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver, holdoutShieldSubPageSet)  // 硬广屏蔽实验 sub page id 集合  // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adRank, followCostRatioAccountBlackList);  // 关注页成本率优化账户黑名单 // NOLINT 
  DEFINE_HASH_SET_NODE_KCONF(int64, firefly.bidServer, UaaGameAccountWhiteSet);  // Uaa 游戏联合买量加白账户  // NOLINT
  DEFINE_BOOL_KCONF_NODE(firefly.bidServer, enableUaaSetChargeType, false);
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableIncrementExploreSetChargeType, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, incrementExploreHitSet)
  DEFINE_PROTOBUF_NODE_KCONF(UnifyGpmCaliConf, ad.frontserver, unifyGpmCaliConf)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableUniverseLiveAudienceFilter, false);  // 联盟直播进人广告下发速率控制开关  // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver2, universeLiveAudienceRateLimitOcpxTypes);  // 联盟需要控制下发速率的直播相关转化目标  // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver2, innerDefaultSceneType);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver2, innerHostingSceneType);
  DEFINE_HASH_SET_NODE_KCONF(int32, ad.frontserver2, innerStoreSceneType);
  DEFINE_PROTOBUF_NODE_KCONF(AdSmartOfferValues, ad.frontserver2, adSmartOfferValues);  // 短剧C补实验参数配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(AdSmartOfferConfig, ad.frontserver2, adSmartOfferConfig);  // 短剧C补实验参数配置  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(PlayletIaaUpliftConfig, ad.frontserver2, playletIaaUpliftConfig);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.frontserver2, playletIaaTestUidSet);
  DEFINE_TAILNUMBERV2_KCONF(ad.frontserver2, adInvokedBonusLtvFixTail, "100;;;");
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.fanstopServer, supportedPosIdSetV2);
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.fanstopServer, supportedPosIdSetInspire);
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.fanstopServer, FANSTOP_WHITE_USER_LIST)  // 发现 & 同城 粉条短视频白名单  // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(TargetFlowConfig, ad.adserver2, targetFlowConfig)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, supplementarySignInVersion, "9.2.50")
  DEFINE_PROTOBUF_NODE_KCONF(ks::engine_base::IdSelector, ad.fanstopServer, DisableInspireLiveAdmitSelector)
  DEFINE_KCONF_NODE_LOAD(MerchantConfig, ad.adserver, merchantConfig)
  DEFINE_STRING_KCONF_NODE(ad.fanstopServer, thanos_android_app_version_black_range)  // "1.0.0,2.3.4", 包含 start，不包含 end  // NOLINT
  DEFINE_STRING_KCONF_NODE(ad.fanstopServer, nebula_android_min_app_version_support_photo)  // 极速版 安卓最低可投放客户端版本  // NOLINT
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, nebula_ios_min_app_version_support_photo, "1.0.0")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, nebulaIosLiveMinVersion, "1.0.0")
  DEFINE_KCONF_NODE_LOAD(ExploreLiveConfig, ad.fanstopServer, exploreLiveConfig)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, liveSquareIosMinNebulaVersion, "2.2.0")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, liveSquareAndroidMinNebulaVersion, "2.2.0")
  DEFINE_STRING_KCONF_NODE(ad.frontserver2, aggrbiddingXUrlTemplate);
  DEFINE_STRING_KCONF_NODE(ad.fanstopServer, nebula_min_ios_version_assiciated_unit)
  DEFINE_STRING_KCONF_NODE(ad.fanstopServer, nebula_min_android_version_assiciated_unit)
  DEFINE_STRING_KCONF_NODE(ad.fanstopServer, main_min_ios_version_assiciated_unit)
  DEFINE_STRING_KCONF_NODE(ad.fanstopServer, main_min_android_version_assiciated_unit)
  DEFINE_STRING_KCONF_NODE(ad.fanstopServer, nebula_android_app_version_black_range)  // "1.0.0,2.3.4", 包含 start，不包含 end  // NOLINT
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, liveSquareIosMinMainVersion, "7.2.0")
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.fanstopServer, liveSquareAndroidMinMainVersion, "7.2.0")
  DEFINE_PROTOBUF_NODE_KCONF(PageSize2MaxReturn, ad.fanstopServer, pageSize2MaxReturn)
  DEFINE_HASH_SET_NODE_KCONF(int32_t, ad.adserver, merchantSupportLivePageIds)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adserver2, merchantSupportLiveSubPageIds)
  DEFINE_KCONF_NODE_LOAD(ks::engine_base::AppVersionControlConfig, ad.fanstopServer, appVersionControlConfig)
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, disableNearbyPhoto, false)  // 是否关闭同城页短视频投放
  DEFINE_BOOL_KCONF_NODE(ad.fanstopServer, enableNearbySetupInnerFlow, false)
  DEFINE_HASH_SET_NODE_KCONF(int64_t, ad.adtarget2, inspireMixPkSubPageIdSet)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
#if 16 >= IMPL_BLOCK_BEGIN and 16 < IMPL_BLOCK_END  /* auto generated by reset_kconf.py, you can ignore. */
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, ColossusGoodOrderItemNeedTimeDays, 7)
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, ColossusAutoLiveItemSqlTimeDays, 10)
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, ColossusVideoItemSqlTimeS, 3)
  // [jiangyuzhen03] one-model 近线
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, oneModelNearline_enable, false)  // 总开关
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, oneModelNearline_enableFlowFilter, true)  // 流量过滤功能
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, oneModelNearline_adServerExtraTimeoutMs, 3000)  // adserver 额外超时  //NOLINT
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, oneModelNearline_adRankExtraTimeoutMs, 3000)  // rank 额外超时

  DEFINE_SET_NODE_KCONF(std::string, ad.frontserver2, adResponseRebuildDiffWhiteList)
  DEFINE_SET_NODE_KCONF(std::string, ad.frontserver2, adResponseRankResultDiffWhiteList)
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, adResponseRebuildDiffReportStepSize, 1)
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, adResponseRankResultDiffReportStepSize, 1)
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, adResponseRebuildDiffRate, 1)
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, frontTrafficCopyCacheTTL, 600)
  DEFINE_INT32_KCONF_NODE(ad.frontserver2, customLruCacheCapacity, 100000)
  DEFINE_SET_NODE_KCONF(int64_t, ad.adtarget4, localLifeDuiTouPosIds);
  // 鲜花绿叶
  DEFINE_STRING_STRING_HASH_MAP_KCONF(ad.frontserver2, adSmartOfferGreenLeafPanelSeriesBonusMap)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, adSmartOfferGreenLeafPanelSeriesDarkList)  // 鲜花绿叶 暗测名单 // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, adSmartOfferGreenLeafPanelSeriesWhiteFromOperator)  // 鲜花绿叶 经营者平台 // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, adSmartOfferGreenLeafPanelSeriesWhiteList)  // 鲜花绿叶短剧剧集白名单 // NOLINT
  DEFINE_STRING_INT64_HASH_MAP_KCONF(ad.frontserver2, adSmartOfferSeriesEpisodePriceMap)  // 短剧剧集单集价格映射  // NOLINT
  DEFINE_INT64_KCONF_NODE(ad.frontserver2, adSmartOfferSeriesEpisodeDefaultPrice, 100)  // 短剧剧集单集价格默认值  // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, adSmartOfferSeriesWhiteList)  // 短剧剧集白名单 // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, poQuanExtraDiscountProductNameList) // E-E破圈计费打折额外折扣 // NOLINT
  DEFINE_PROTOBUF_NODE_KCONF(DebugSpecialFilterReasons, ad.adserver2, whiteBoxDebugSpecialFilterReasons)
  DEFINE_BOOL_KCONF_NODE(ad.adserver, enableTransparentDpa, false)
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, enableTransAdFieldToFront, false)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, ksFlagGrpcForceUseEpollsig, true)
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableRecoUserInfoSkipCache, false)
  DEFINE_KCONF_NODE_LOAD(KessGrpcClients, ad.engine, kessGrpcClients)
  DEFINE_PROTOBUF_NODE_KCONF(DisableFactorConfig, ad.adRank2, disableFactorConfig)
  DEFINE_STRING_DOUBLE_HASH_MAP_KCONF(ad.frontserver2, adCartoonProductNameCbuBonusMap)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.miniSeries, cartoonSeriesProductNameSet);  // 漫剧产品名单——统一 // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.frontserver2, adCartoonProductNameSet)  // 漫剧产品名单-后续废弃 // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, adSmartOfferTomatoSeriesIdWhiteList)  // 番茄短剧剧集白名单 // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, adSmartOfferUserWriteListSet)  // cbu user 测试名单 // NOLINT
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver, follow_subpageid_top3)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, adxSetMaterialTypePageid)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, galaxyThanosSubPageId)
  DEFINE_INT32_KCONF_NODE(ad.frontserver, adboxLimitAdSize, 10)
  DEFINE_JSON_NODE_KCONF(ad.frontserver2, adFctrBonusRoiThresholdPidConfig)
  DEFINE_JSON_NODE_KCONF(ad.frontserver2, adFctrNewFormulaConf)
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, enableReplaceCredit, false)  // kUserCreditKeyWords 宏替换开关
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, enableReplaceCurrentTaid, false)  // kCurrentTaid 宏替换开关
  DEFINE_BOOL_KCONF_NODE(ad.adserver2, enableReplaceStrategyId, false)  // kCurrentTaid 宏替换开关
  DEFINE_LIST_NODE_KCONF(std::string, ad.frontserver2, adFctrExpTagList)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.frontserver2, adFctrPidKconfTag, "default")
  DEFINE_BOOL_KCONF_NODE(ad.frontserver2, enableUpDimensionCaliFilling, false)
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.frontserver2, subPageIdsSupportMixToAdPack)  // 混排请求 pack 流量
  DEFINE_HASH_SET_NODE_KCONF(int64, ad.adFrontDiffSwitches, guessLikeMixRankReqSubpageid)
  DEFINE_PROTOBUF_NODE_KCONF(SearchInspireParamsConfig, ad.frontserver2, searchInspireParamsConfig)
  DEFINE_PROTOBUF_NODE_KCONF(innerMixScoreConf, ad.adRank3, InnerMixScoreConf)
  DEFINE_BOOL_KCONF_NODE(ad.router, adRouterGetKessNameIgnoreEnv, false);  // zhangpuyang router  上移测试
  DEFINE_PROTOBUF_NODE_KCONF(AdUnifyBonusRatioExpConfNew, ad.frontserver2, adUnifyBonusRatioExpConfNew)
  DEFINE_PROTOBUF_NODE_KCONF(AdCpmUserUnifyBonusRatioExpConf, ad.frontserver2, adAllocByUserCpmBonusExpConf)
  DEFINE_PROTOBUF_NODE_KCONF(AdInnerMultiQueueConf, ad.frontserver2, adInnerMultiQueueConf)
  DEFINE_PROTOBUF_NODE_KCONF(AdAutoGpmRatioExpConf, ad.frontserver2, adAutoGpmRatio)
  DEFINE_PROTOBUF_NODE_KCONF(AdLiveRerankTimeGapExpConf, ad.frontserver2, adLiveRerankTimeGapExpConf)
  DEFINE_PROTOBUF_NODE_KCONF(AdUnifyBonusRatioExpConfNew, ad.frontserver2, adBonusReallocationExpConf)
  DEFINE_HASH_SET_NODE_KCONF(std::string, ad.prerank_server, prerankCmdKeyAdmitList)
#endif                                              /* auto generated by reset_kconf.py, you can ignore. */
};
}  // namespace front_server
}  // namespace ks



#ifndef KCONF_CC_WITH_IMPL
  #undef  DEFINE_KCONF_NODE
  #define DEFINE_KCONF_NODE(type, config_path, config_key, default_value)                    \
    DEFINE_KCONF_NODE_BODY(type, config_path, config_key, default_value)

  #undef DEFINE_KCONF_NODE_LOAD
  #define DEFINE_KCONF_NODE_LOAD(type, config_path, config_key)                              \
    DEFINE_KCONF_NODE_LOAD_BODY(type, config_path, config_key)

  #undef DEFINE_SET_NODE_KCONF
  #define DEFINE_SET_NODE_KCONF(type, config_path, config_key)                                  \
    DEFINE_SET_NODE_KCONF_BODY(type, config_path, config_key)

  #undef DEFINE_LIST_NODE_KCONF
  #define DEFINE_LIST_NODE_KCONF(type, config_path, config_key)                                  \
    DEFINE_LIST_NODE_KCONF_BODY(type, config_path, config_key)

  #undef DEFINE_KCONF_MAP_KCONF
  #define DEFINE_KCONF_MAP_KCONF(key_type, value_type, config_path, config_key) \
    DEFINE_KCONF_MAP_KCONF_BODY(key_type, value_type, config_path, config_key)
#else
  #undef DEFINE_KCONF_NODE_ATTR
  #define DEFINE_KCONF_NODE_ATTR
#endif
