#include "teams/ad/front_server/util/kconf/kconf_data.h"
#include <stdlib.h>
#include <algorithm>
#include <random>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>
#include "base/hash_function/md5.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "google/protobuf/reflection.h"
#include "google/protobuf/util/message_differencer.h"
#include "infra/utility/src/utility/aes_crypter.h"
#include "base/hash_function/city.h"
#include "absl/time/time.h"
#include "absl/time/clock.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_filter_condition.pb.h"

namespace ks {
namespace front_server {

RTTR_REGISTRATION {
  rttr::registration::class_<UniverseAdSwitch>("UniverseAdSwitch")
    .constructor()(rttr::policy::ctor::as_object)
    .property("config", &UniverseAdSwitch::config);

  rttr::registration::class_<KconfAdpageButtonControl>("KconfAdpageButtonControl")
    .constructor()(rttr::policy::ctor::as_object)
    .property("allow_comment", &KconfAdpageButtonControl::allow_comment)
    .property("allow_attention", &KconfAdpageButtonControl::allow_attention)
    .property("allow_open_profile", &KconfAdpageButtonControl::allow_open_profile)
    .property("allow_forword", &KconfAdpageButtonControl::allow_forword)
    .property("allow_like", &KconfAdpageButtonControl::allow_like);

  rttr::registration::class_<AdDynamicCreative>("AdDynamicCreative")
      .constructor()(rttr::policy::ctor::as_object)
      .property("city_default", &AdDynamicCreative::city_default)
      .property("prov_default", &AdDynamicCreative::prov_default)
      .property("date_default", &AdDynamicCreative::date_default)
      .property("weekday_default", &AdDynamicCreative::weekday_default)
      .property("holiday_default", &AdDynamicCreative::holiday_default)
      .property("man", &AdDynamicCreative::man)
      .property("women", &AdDynamicCreative::women)
      .property("gender_default", &AdDynamicCreative::gender_default)
      .property("appear_man", &AdDynamicCreative::appear_man)
      .property("appear_women", &AdDynamicCreative::appear_women)
      .property("appear_default", &AdDynamicCreative::appear_default)
      .property("anti_man", &AdDynamicCreative::anti_man)
      .property("anti_women", &AdDynamicCreative::anti_women)
      .property("anti_default", &AdDynamicCreative::anti_default)
      .property("anti_wife", &AdDynamicCreative::anti_wife)
      .property("anti_husband", &AdDynamicCreative::anti_husband)
      .property("anti_couple", &AdDynamicCreative::anti_couple)
      .property("age_default", &AdDynamicCreative::age_default)
      .property("ios", &AdDynamicCreative::ios)
      .property("android", &AdDynamicCreative::android)
      .property("brand_default", &AdDynamicCreative::brand_default)
      .property("phone_brand_default", &AdDynamicCreative::phone_brand_default);

  rttr::registration::class_<KconfAdpageButtonControl>("KconfAdpageButtonControl")
    .constructor()(rttr::policy::ctor::as_object)
    .property("allow_comment", &KconfAdpageButtonControl::allow_comment)
    .property("allow_attention", &KconfAdpageButtonControl::allow_attention)
    .property("allow_open_profile", &KconfAdpageButtonControl::allow_open_profile)
    .property("allow_forword", &KconfAdpageButtonControl::allow_forword)
    .property("allow_like", &KconfAdpageButtonControl::allow_like);
};

std::vector<std::string> AppStoreInReview::FindVersions(const std::string& type) {
  std::vector<std::string> ret_vec;
  if (review_versions.count(type) > 0) {
    ret_vec = review_versions.at(type);
  }
  return ret_vec;
}

bool AppStoreInReview::Load(const std::string& json_str) {
  if (json_str.empty()) {
    return true;
  }
  base::Json json_obj(base::StringToJson(json_str));
  if (!json_obj.IsObject()) {
    LOG(WARNING) << "json read failed, string = " << json_str;
    return true;
  }
  for (const auto& v : json_obj.objects()) {
    std::string key = v.first;
    if (nullptr == v.second) {
      continue;
    }
    if (!v.second->IsObject()) {
      LOG(ERROR) << "merchent_conf parse error, key = " << v.first << " value = " << v.second->ToString();
      continue;
    }
    const auto& json_value = v.second;
    for (const auto& preview_v : json_value->objects()) {
      if (preview_v.first == "appstore") {
        if (nullptr == preview_v.second) {
          continue;
        }
        if (preview_v.second->IsArray()) {
          for (int32_t i = 0; i < preview_v.second->size(); i++) {
            std::string app = preview_v.second->GetString(i, "");
            review_versions[key].emplace_back(app);
          }
        }
      }
    }
  }
  return true;
}

bool AdMerchantCardConfig::Load(const std::string& json_str) {
  if (json_str.empty()) {
    return true;
  }
  debug_str = json_str;
  base::Json json_obj(base::StringToJson(json_str));
  if (!json_obj.IsObject()) {
    LOG(WARNING) << "json read failed, string = " << json_str;
    return true;
  }
  for (const auto& v : json_obj.objects()) {
    std::string key = v.first;
    if (nullptr == v.second) {
      continue;
    }
    if (!v.second->IsObject()) {
      LOG(ERROR) << "merchent_conf parse error, key = " << v.first << " value = " << v.second->ToString();
      continue;
    }
    auto *product_json = v.second;
    AdMerchantProduct product;
    product.icon = product_json->GetString("icon", "");
    product.title = product_json->GetString("title", "");
    product.origin_price = product_json->GetInt("origin_price", 0);
    product.selling_price = product_json->GetInt("selling_price", 0);
    int64_t unit_id(0);
    base::StringToInt64(key, &unit_id);
    merchant_conf[unit_id] = product;
  }
  std::string idstr;
  for (auto e : merchant_conf) {
    idstr += std::to_string(e.first) + ",";
  }
  return true;
}

bool KconfAdpageButtonControl::Load(const rapidjson::Value &v) {
  return ks::ad_base::JsonSerializer::FromJson(v, *this);
}

bool KconfAdpageButtonControl::Load(const std::string &v) {
  return ks::ad_base::JsonSerializer::FromJson(v, *this);
}

void KconfAdpageButtonControl::describe(std::ostream &os) const {
  os << ks::ad_base::JsonSerializer::ToJson(*this);
}

bool UserActionTypeConf::Init() {
  user_action_set.clear();
  for (const auto& stratagy : pb().pick_stratagy()) {
    auto& stratagy_group = user_action_set[stratagy.stratagy_id()];
    stratagy_group.insert(stratagy.ad_action_type().begin(), stratagy.ad_action_type().end());
  }
  return true;
}

bool ForceProductSet::Init() {
  force_product_photo_set.clear();
  for (const auto &force_product : pb().force_product()) {
    std::string group_name = force_product.first;
    for (auto &kv : force_product.second.force_set()) {
      int64 force_product_id = base::CityHash64(kv.first.c_str(), kv.first.length());
      const auto &force_photo = kv.second.force_photo();
      for (auto elem : force_photo) {
        force_product_photo_set[group_name][force_product_id].insert(elem);
      }
    }
  }
  return true;
}
std::unordered_map<std::string, std::unordered_map<int64, std::unordered_set<int64>>>
    ForceProductSet::GetParams() const {
  return force_product_photo_set;
}

bool AdmitFlowBlackListConfig::Init() {
  page_id_set.clear();
  sub_page_id_set.clear();
  page_id_set.insert(pb().page_id().begin(), pb().page_id().end());
  sub_page_id_set.insert(pb().sub_page_id().begin(), pb().sub_page_id().end());
  return true;
}

bool AdmitFlowBlackListConfig::HitBlackList(const int64_t& page_id, const int64_t& sub_page_id) const {  // NOLINT
  return page_id_set.count(page_id) > 0 ||
         sub_page_id_set.count(sub_page_id) > 0;
}

bool BidOptimExpConfigClass::Init() {
  strategy_map.clear();
  unify_param_map.clear();
  for (auto& [exp_tag, exp_config] : pb().exp_list()) {
    UnifyStraParam unfiy_stra_param;
    const auto& unify_strategy_param = exp_config.unify_strategy_param();
    const auto& strategy_feature_dim = exp_config.strategy_feature_dim();

    // 解析 unfiy_stra_param
    unfiy_stra_param.enable_admit = unify_strategy_param.enable_admit();
    unfiy_stra_param.enable_uplift_control = unify_strategy_param.enable_uplift_control();
    const auto& user_tag_switch = unify_strategy_param.user_tag_switch();
    const auto& ad_value_switch = unify_strategy_param.ad_value_switch();
    const auto& func_switch = unify_strategy_param.func_switch();
    unfiy_stra_param.enable_commerical_user_tag = user_tag_switch.enable_commerical_user_tag();
    unfiy_stra_param.enable_merchant_user_tag = user_tag_switch.enable_merchant_user_tag();
    unfiy_stra_param.enable_optim_cpm = ad_value_switch.enable_optim_cpm();
    unfiy_stra_param.enable_optim_rank_benefit = ad_value_switch.enable_optim_rank_benefit();
    unfiy_stra_param.enable_fit_log_func = func_switch.enable_fit_log_func();
    unfiy_stra_param.enable_fit_exp_func = func_switch.enable_fit_exp_func();
    unify_param_map[exp_tag] = unfiy_stra_param;

    // 解析 stra_feat_dim
    absl::flat_hash_map<std::string, StraFeatDim> stra_feat_dim_map;
    for (auto& [tag, feat_dim] : strategy_feature_dim) {
      StraFeatDim stra_feat_dim;
      stra_feat_dim.ad_monitor_type_set.insert(
                  feat_dim.ad_monitor_type_set().begin(), feat_dim.ad_monitor_type_set().end());
      stra_feat_dim.ad_queue_type_set.insert(
                  feat_dim.ad_queue_type_set().begin(), feat_dim.ad_queue_type_set().end());
      stra_feat_dim.user_level_set.insert(
                  feat_dim.user_level_set().begin(), feat_dim.user_level_set().end());
      stra_feat_dim.page_id_set.insert(
                  feat_dim.page_id_set().begin(), feat_dim.page_id_set().end());
      stra_feat_dim.alpha_i = feat_dim.func_param().alpha_i();
      stra_feat_dim.beta_i = feat_dim.func_param().beta_i();
      stra_feat_dim.gamma_i = feat_dim.func_param().gamma_i();
      stra_feat_dim_map[tag] = stra_feat_dim;
    }
    strategy_map[exp_tag] = stra_feat_dim_map;
  }
  return true;
}

bool KuaiGameFeedPlayConfClass::Init() {
  device_mod_set.clear();
  product_name_set.clear();
  account_id_set.clear();
  age_bound_list.clear();
  app_version_tag = "";
  device_mod_set.insert(pb().device_mod_conf().begin(), pb().device_mod_conf().end());
  product_name_set.insert(pb().product_name_conf().begin(), pb().product_name_conf().end());
  account_id_set.insert(pb().account_conf().begin(), pb().account_conf().end());
  for (auto& age_element : pb().age_bound_conf()) {
    age_bound_list.push_back(age_element);
  }
  app_version_tag = pb().app_version_conf();
  return true;
}

bool BidOptimCalcHyperParamClass::Init() {
  bid_constraint_map.clear();
  for (auto& [tag, exp_param] : pb().exp_list()) {
    bid_constraint_map[tag] = exp_param.lambda();
  }
  return true;
}

bool newFlowAdmitConfig::Init() {
  return true;
}

bool newFlowAdmitConfig::GetAbKey(const int64_t& sub_page_id, std::string* ab_key) const {
  auto it = pb().config_map().find(sub_page_id);
  if (it == pb().config_map().end()) {
    return false;
  }
  *ab_key = it->second;
  return true;
}

bool newFlowAdmitConfig::GetAbKeyByPageId(const int64_t& page_id, std::string* ab_key) const {
  auto it = pb().page_id_config().find(page_id);
  if (it == pb().page_id_config().end()) {
    return false;
  }
  *ab_key = it->second;
  return true;
}

bool UnionMidPageWhiteConfig::Init() {
  product_white_map_.clear();
  for (const auto& map_item : pb().product_white_conf()) {
    int64_t key = base::CityHash64(map_item.first.c_str(), map_item.first.size());
    if (key == 0) {
      continue;
    }
    absl::flat_hash_set<int64_t> account_ids;
    account_ids.insert(map_item.second.account_ids().begin(), map_item.second.account_ids().end());
    product_white_map_.emplace(key, account_ids);
  }
  return true;
}

bool UnionMidPageWhiteConfig::IsInWhiteList(const std::string& product_name, int64_t account_id) const {
  int64_t key = base::CityHash64(product_name.c_str(), product_name.size());
  auto it = product_white_map_.find(key);
  if (it == product_white_map_.end()) return false;
  if (it->second.size() == 0) return true;
  return it->second.count(key);
}

bool SearchSmartCoverConfig::Init() {
  exp_map_.clear();
  for (const auto& map_item : pb().exp_conf()) {
    absl::flat_hash_set<int64_t> pos_ids;
    pos_ids.insert(map_item.second.shield_actual_pos_ids().begin(),
                  map_item.second.shield_actual_pos_ids().end());
    exp_map_.emplace(map_item.first, pos_ids);
  }
  return true;
}

bool SearchSmartCoverConfig::IsInShieldList(const std::string& exp_name,
                                  int64_t page_num, int64_t pos) const {
  auto it = exp_map_.find(exp_name);
  if (it == exp_map_.end()) return false;
  if (it->second.size() == 0) return false;
  int64_t actual_pos = page_num * 10 + pos;
  return it->second.count(actual_pos);
}

NegativeMenusInfoPb::~NegativeMenusInfoPb() {
  if (negative_menu_info_pb) delete negative_menu_info_pb;
}

bool NegativeMenusInfoPb::Load(const std::string& value) {
  if (value.empty()) {
    LOG(WARNING) << "Parse NegativeMenuInfo fail: no value";
    return true;
  }
  if (negative_menu_info_pb) delete negative_menu_info_pb;
  negative_menu_info_pb = new kuaishou::ad::AdDataV2_NegativeMenuInfo();

  auto status = ::google::protobuf::util::JsonStringToMessage(value, negative_menu_info_pb);
  if (!status.ok()) {
    LOG(WARNING) << "Parse NegativeMenuInfo fail: " << status.ToString();
    return true;
  }
  return true;
}

bool UniverseAdSwitch::Allowed(const std::string &app_id,
                               const int64_t page_id,
                               const int64_t sub_page_id) {
  std::string key = absl::Substitute("$0_$1_$2", app_id, page_id, sub_page_id);
  if (config.find(key) == config.end()) {
    return true;
  }
  if (ad_base::AdRandom::GetInt(0, 99) < config[key]) {
    return true;
  }
  return false;
}

ClientLimiter::~ClientLimiter() {
  for (auto& it : qps_bucket) {
    delete it.second;
  }
  qps_bucket.clear();
}

bool ClientLimiter::Init() { return InitQpsBucket(); }

void ClientLimiter::describe(std::ostream& os) const { os << ks::ad_base::JsonSerializer::ToJson(*this); }

bool ClientLimiter::InitQpsBucket() {
  for (auto& it : pb().config()) {
    if (it.second >= 0) {
      qps_bucket.insert(
          {it.first, new ccb::TokenBucket(it.second, it.second / 5, it.second / 10, nullptr, true)});
    }
  }
  return true;
}

bool AdDynamicCreative::Load(const rapidjson::Value &v) {
  return ks::ad_base::JsonSerializer::FromJson(v, *this);
}

bool AdDynamicCreative::Load(const std::string &v) {
  return ks::ad_base::JsonSerializer::FromJson(v, *this);
}

void AdDynamicCreative::describe(std::ostream &os) const {
  os << ks::ad_base::JsonSerializer::ToJson(*this);
}

bool AntiSpamBlacklist::Init() {
#define REP_INSERT(container, balcklist_map, blacklist_type) \
  for (const auto& element : container) {                    \
    balcklist_map[blacklist_type].insert(element);           \
  }

  REP_INSERT(pb().kuaishou().imei(), kuaishou_blacklist_map_, "imei")
  REP_INSERT(pb().kuaishou().device_id(), kuaishou_blacklist_map_, "device_id")
  REP_INSERT(pb().kuaishou().ip(), kuaishou_blacklist_map_, "ip")
  REP_INSERT(pb().kuaishou().idfa(), kuaishou_blacklist_map_, "idfa")

  REP_INSERT(pb().union_().imei(), union_blacklist_map_, "imei")
  REP_INSERT(pb().union_().device_id(), union_blacklist_map_, "device_id")
  REP_INSERT(pb().union_().ip(), union_blacklist_map_, "ip")
  REP_INSERT(pb().union_().idfa(), union_blacklist_map_, "idfa")

#undef REP_INSERT

  return true;
}

bool AntiSpamBlacklist::IsInBlacklist(const std::string& flow_type, const std::string& blacklist_type,
                                      const std::string& id) const {
  if (flow_type == "kuaishou") {
    auto it = kuaishou_blacklist_map_.find(blacklist_type);
    return it != kuaishou_blacklist_map_.end() && it->second.count(id);
  } else if (flow_type == "union") {
    auto it = union_blacklist_map_.find(blacklist_type);
    return it != union_blacklist_map_.end() && it->second.count(id);
  }
  return false;
}

bool AntiSpamConfig::Load(const std::string& json_str) {
  debug_str = json_str;
  base::Json d(base::StringToJson(debug_str));
  if (!d.IsArray()) {
    LOG(WARNING) << "Parsing json failed, string = " << json_str;
    return true;
  }

  auto iter = d.array_begin();
  for (; iter != d.array_end(); ++iter) {
    if (nullptr == (*iter) || (!(*iter)->IsObject())) {
      continue;
    }

    bool rule_valid = true;
    std::string rule_name;
    std::vector<std::string> unique_id_v;
    for (const auto& v : (*iter)->objects()) {
      if (nullptr == v.second) {
        continue;
      }
      if (v.first == "is_available") {
        if (v.second->BooleanValue(&rule_valid)) {
          if (!rule_valid) {
            break;
          }
        } else {
          break;
        }
      } else if (v.first == "rule_name") {
        if (!v.second->StringValue(&rule_name)) {
          break;
        }
      } else if (v.first == "unique_id") {
        if (v.second->IsArray()) {
          auto iter_unique_id = v.second->array_begin();
          for (; iter_unique_id != v.second->array_end(); ++iter_unique_id) {
            std::string unique_key;
            if (nullptr != (*iter_unique_id) && (*iter_unique_id)->StringValue(&unique_key)) {
              unique_id_v.push_back(unique_key);
            }
          }
        }
      }
    }
    if (rule_valid && rule_name != "" && unique_id_v.size() > 0) {
      rule_map.insert(std::make_pair(rule_name, unique_id_v));
    }
  }

  return true;
}

void AntiSpamConfig::GetRuleKey(const std::string& device_id,
                                const std::string& device_ip,
                                const ::kuaishou::ad::DeviceInfo::OsType& device_os,
                                const std::string& device_idfa,
                                const std::string& device_imei,
                                std::vector<std::string>* key_vector) {
  if (nullptr == key_vector) {
    return;
  }
  auto iter = rule_map.begin();
  for (; iter != rule_map.end(); ++iter) {
    std::string key = iter->first;
    bool has_one_valid = false;
    for (uint32_t i = 0; i < iter->second.size(); ++i) {
      if (iter->second[i] == "device_id" && device_id != "") {
        key += "_" + device_id;
        has_one_valid = true;
      } else if (iter->second[i] == "ip" && device_ip != "") {
        key += "_" + device_ip;
        has_one_valid = true;
      } else if (iter->second[i] == "imei" &&
          (device_os == ::kuaishou::ad::DeviceInfo::ANDROID ||
            device_os == ::kuaishou::ad::DeviceInfo::HARMONY) && device_imei != "") {
        key += "_" + device_imei;
        has_one_valid = true;
      } else if (iter->second[i] == "idfa" &&
          device_os == ::kuaishou::ad::DeviceInfo::IOS && device_idfa != "") {
        key += "_" + device_idfa;
        has_one_valid = true;
      }
    }
    if (has_one_valid) {
      key_vector->push_back(MD5String(key));
    }
  }
}

bool AdDisplayInfoFilterConf::Load(const std::string& json_str) {
  base::Json d(base::StringToJson(json_str));
  if (!d.IsObject()) {
    LOG(ERROR) << json_str << "display info filter config is  not json object";
    return false;
  }
  for (const auto& v : d.objects()) {
    if (nullptr == v.second) {
      continue;
    }
    if (v.first == "first_industry_id_list" && v.second->IsArray()) {
      int64_t first_industry_id;
      for (int i = 0; i < v.second->size(); ++i) {
        if (v.second->GetInt(i, &first_industry_id)) {
          first_industry_set.insert(first_industry_id);
        } else {
          LOG(ERROR) << "first industry id json parse error";
          continue;
        }
      }
    }
    if (v.first == "second_industry_id_list" && v.second->IsArray()) {
      int64_t second_industry_id;
      for (int i = 0; i < v.second->size(); ++i) {
        if (v.second->GetInt(i, &second_industry_id)) {
          second_industry_set.insert(second_industry_id);
        } else {
          LOG(ERROR) << "second industry id parse failed, i = " << i;
          continue;
        }
      }
    }
    if (v.first == "account_id_list" && v.second->IsArray()) {
      int64_t account_id;
      for (int i = 0; i < v.second->size(); ++i) {
        if (v.second->GetInt(i, &account_id)) {
          account_id_set.insert(account_id);
        } else {
          LOG(ERROR) << "account id parse failed, i = " << i;
          continue;
        }
      }
    }
    if (v.first == "product_name_list" && v.second->IsArray()) {
      std::string product_name;
      for (int i = 0; i < v.second->size(); ++i) {
        if (v.second->GetString(i, &product_name)) {
          product_name_set.insert(product_name);
        } else {
          LOG(ERROR) << "product name parse failed, i = " << i;
          continue;
        }
      }
    }
  }
  return true;
}

// 广告作品广告语符号映射
bool AdDescriptionFormatConf::Load(const std::string& json_str) {
  base::Json d(base::StringToJson(json_str));
  if (!d.IsObject()) {
    LOG(ERROR) << " Parsing json failed, string = " << json_str;
    return false;
  }

  for (const auto& data_pair : d.objects()) {
    std::string origin_symbol = data_pair.first;
    if (nullptr == data_pair.second) {
      LOG(ERROR) << "parse ad description config fail";
      return false;
    }
    std::string format_symbol = data_pair.second->StringValue();
    description_symbol_map[origin_symbol] = format_symbol;
  }
  return true;
}

bool AdCoverMediaTestInfo::Load(const std::string &json_str) {
  Clear();
  debug_str = json_str;
  if (json_str.size() == 0) {
    return true;
  }
  json.reset(new base::Json(base::StringToJson(json_str)));
  for (auto i = json->object_begin(); i != json->object_end(); i++) {
    int64 photo_id = 0;
    base::StringToInt64(i->first, &photo_id);
    if (nullptr != i->second) {
      photo_map[photo_id] = i->second;
    }
  }
  return true;
}

bool AdCoverMediaTestInfo::Fill(int64 photo_id, int cover_type, ::Json::Value* p_body) {
  if (nullptr == p_body) {
    return false;
  }
  ::Json::Value& body = *p_body;
  auto it = photo_map.find(photo_id);
  if (photo_map.end() == it) {
    return false;
  }
  const auto& config = *(it->second);
  const auto& arr = config.array();
  if (cover_type <= 0 || cover_type > arr.size()) {
    LOG_EVERY_N(ERROR, 100000) << " cover_type not found photo_id=" << photo_id
                               << " type=" << cover_type
                               << " arr" << arr.size();
    return false;
  }
  if (nullptr == arr[cover_type-1]) {
    return false;
  }
  const auto& style = arr[cover_type-1]->array();
  if (style.size() < 4) {
    LOG_EVERY_N(ERROR, 100000) << " style size not 4 photo_id=" << photo_id
                              << " style_size=" << style.size();
    return false;
  }
  int64 cover_start = 0;
  int64 cover_duration = 0;
  if (nullptr != style[0]) {
    style[0]->IntValue(&cover_start);
  }
  if (nullptr != style[1]) {
    style[1]->IntValue(&cover_duration);
  }
  body["mediaType"] = (::Json::Value::Int)1;
  body["materialType"] = (::Json::Value::Int)cover_type;
  body["coverStart"] = (::Json::Value::Int)cover_start;
  body["coverDuration"] = (::Json::Value::Int)cover_duration;
  ::Json::Value urls;
  for (int i=2; i < style.size(); i++) {
    if (nullptr == style[i]) {
      continue;
    }
    const auto& url_json = style[i]->array();
    if (url_json.size() != 2) {
      LOG_EVERY_N(ERROR, 100000) << " url_json size wrong " << url_json.size();
      continue;
    }
    ::Json::Value url_info;
    std::string cdn;
    std::string url;
    if (nullptr != url_json[0]) {
      url_json[0]->StringValue(&cdn);
    }
    if (nullptr != url_json[1]) {
      url_json[1]->StringValue(&url);
    }
    url_info["cdn"] = ::Json::Value(cdn);
    url_info["url"] = ::Json::Value(url);
    urls.append(url_info);
  }
  body["coverUrls"] = urls;
  return true;
}

bool AdCoverMediaTestInfo::Clear() {
  debug_str = "";
  photo_map.clear();
  json.reset();
  return true;
}

bool MerchantProfileAdConfig::Load(const std::string& json_str) {
  raw_json_str = json_str;
  base::Json json_obj(base::StringToJson(json_str));
  if (!json_obj.IsObject()) {
    LOG(WARNING) << "Parse merchant profile config json failed, string: " << json_str;
    return false;
  }

  android_main_app_ver = json_obj.GetString("android_main_app_ver", "7.4.30");
  android_nebula_app_ver = json_obj.GetString("android_nebula_app_ver", "2.4.3");
  ios_main_app_ver = json_obj.GetString("ios_main_app_ver", "7.4.30");
  ios_nebula_app_ver = json_obj.GetString("ios_nebula_app_ver", "2.4.0");
  filter_profile_ad_for_old_app = json_obj.GetBoolean("filter_profile_ad_for_old_app", false);

  return true;
}

void MerchantProfileAdConfig::describe(std::ostream& os) const {
  os << base::StringToJson(raw_json_str);
}

bool TestWhiteList::Load(const std::string& json_str) {
  base::Json json_obj(base::StringToJson(json_str));
  if (!json_obj.IsArray()) {
    LOG(WARNING) << "json_string config error, string: " << json_str;
    return false;
  }
  for (int i = 0; i < json_obj.size(); ++i) {
    auto array_item = json_obj.Get(i);
    auto* user_ids = array_item->Get("user_id");
    if (nullptr == user_ids || !user_ids->IsArray()) {
      auto log_info = nullptr == user_ids ? \
          "key user_id is not configured" : "key user_id configure error, shoud be array";
      LOG(WARNING) << log_info;
      continue;
    }
    auto* creative_ids = array_item->Get("creative_id");
    if (creative_ids == nullptr || !creative_ids->IsArray()) {
      auto log_info = nullptr == user_ids ? \
        "key creative_id is not configured" : "key creative_id configure error, shoud be array";
      LOG(WARNING) << log_info;
      continue;
    }
    struct TestDataItem data_item;
    bool enable_test_style = false;
    int position_id;
    array_item->GetBoolean("enable_test_style", &enable_test_style);
    position_id = array_item->GetInt("position_id", 0);
    data_item.enable_test_style = enable_test_style;
    data_item.position_id = position_id;

    auto cid_iter = creative_ids->array_begin();
    for (; cid_iter != creative_ids->array_end(); ++cid_iter) {
      int64_t cid;
      if (!((*cid_iter)->IntValue(&cid))) {
        continue;
      }
      data_item.creative_ids.emplace_back(cid);
      // 读取 live_id, 如果有的话
      auto live_id_str = array_item->GetString(absl::StrCat(cid), "");
      if (!live_id_str.empty()) {
        int64_t live_id;
        base::StringToInt64(live_id_str, &live_id);
        if (data_item.live_ids.find(cid) == data_item.live_ids.end()) {
          data_item.live_ids.emplace(std::make_pair(cid, live_id));
        }
      }
    }
    auto uid_iter = user_ids->array_begin();
    for (; uid_iter != user_ids->array_end(); ++uid_iter) {
      int64_t uid;
      if (!((*uid_iter)->IntValue(&uid))) {
        continue;
      }
      white_list_data.emplace(std::make_pair(uid, data_item));
    }
  }
  return true;
}

bool TestWhiteList::IsInWhiteList(int64_t user_id) {
  if (white_list_data.find(user_id) == white_list_data.end()) {
    return false;
  }
  return true;
}

bool TestWhiteList::IsTestStyle(int64_t user_id) {
  if (IsInWhiteList(user_id)) {
    return white_list_data[user_id].enable_test_style;
  }
  return false;
}

bool TestWhiteList::GetPositionId(int64_t user_id, int64_t* position_id) {
  if (nullptr == position_id) {
    return false;
  }
  if (IsInWhiteList(user_id)) {
    (*position_id) = white_list_data[user_id].position_id;
    return true;
  }
  (*position_id) = 0;
  return false;
}

bool TestWhiteList::GetCreativeId(int64_t user_id, std::vector<int64_t>* creative_ids) {
  if (nullptr == creative_ids) {
    return false;
  }
  if (IsInWhiteList(user_id)) {
    (*creative_ids) = white_list_data[user_id].creative_ids;
    return true;
  }
  creative_ids->clear();
  return false;
}

bool TestWhiteList::GetLiveId(int64_t user_id, int64_t creative_id, int64_t* live_id) {
  if (nullptr == live_id) {
    return false;
  }
  if (IsInWhiteList(user_id)) {
    auto& live_ids = white_list_data[user_id].live_ids;
    if (live_ids.find(creative_id) != live_ids.end()) {
      (*live_id) = live_ids[creative_id];
      return true;
    }
  }
  (*live_id) = 0;
  return false;
}

bool GenerateCreativeRedisConfig::Load(const std::string& json_str) {
  if (json_str.empty()) {
    return true;
  }
  base::Json d(base::StringToJson(json_str));
  zk_path = d.GetString("zk_path", "");
  timeout_ms = d.GetInt("timeout_ms", -1);
  key_prefix_map["generate_cover_prefix"] = d.GetString("generate_cover_prefix", "");
  key_prefix_map["clip_photo_prefix"] = d.GetString("clip_photo_prefix", "");
  key_prefix_map["transpose_photo_prefix"] = d.GetString("transpose_photo_prefix", "");
  return true;
}

std::string KnewsEffectSplashImageUrlsConfig::GetSuitableImageUrl(int64_t width, int64_t height) const {
  std::string result;
  std::string current_date = absl::FormatTime("%Y%m%d", absl::Now(), absl::LocalTimeZone());
  int64_t selected_priority = -1;
  std::string selected_key;
  for (auto it : pb().image_urls_config()) {
    if (it.second.begin_date().compare(current_date) <= 0 &&
        it.second.end_date().compare(current_date) >= 0 && it.second.priority() > selected_priority) {
      selected_priority = it.second.priority();
      selected_key = it.first;
    }
  }
  if (selected_priority != -1) {
    return GetImageUrlFromArrayByKey(selected_key, width, height);
  } else {
    return "";
  }
}

std::string KnewsEffectSplashImageUrlsConfig::GetImageUrlFromArrayByKey(std::string key, int64_t width,
                                                                        int64_t height) const {
  auto it = pb().image_urls_config().find(key);
  if (it != pb().image_urls_config().end() && it->second.image_urls().size() > 0) {
    std::string result;
    if (width == 0) {
      result = it->second.image_urls().Get(it->second.image_urls().size() - 1).url();
    } else {
      double min_diff = 100000000.0;
      double screen_height_width_ratio = (double)height / width;
      for (const auto& image_info : it->second.image_urls()) {
        double current_height_width_ratio = (double)image_info.height() / image_info.width();
        double current_diff = std::abs(current_height_width_ratio - screen_height_width_ratio);
        if (current_diff < min_diff) {
          min_diff = current_diff;
          result = image_info.url();
        }
      }
    }
    return result;
  } else {
    return "";
  }
}

KnewsEffectSplashImageUrlsConfig::~KnewsEffectSplashImageUrlsConfig() {}

bool KnewsEffectSplashImageUrlsConfig::Init() { return true; }

bool MerchantLiveStyleConf::Load(const std::string& json_str) {
  base::Json json_obj(base::StringToJson(json_str));
  if (!json_obj.IsObject()) {
    LOG(WARNING) << "Parsing json failed, string = " << json_str;
    return false;
  }

  auto played_report_time_list = json_obj.Get("played_report_time");
  if (played_report_time_list != nullptr && played_report_time_list->IsArray()) {
    for (int index = 0; index < played_report_time_list->size(); ++index) {
      auto time = played_report_time_list->GetInt(index, -1);
      if (time < 0) {
        continue;
      }
      played_report_time.emplace_back(time);
    }
    std::sort(played_report_time.begin(), played_report_time.end());
  }

  no_action_bar_android_main_app_ver = json_obj.GetString("android_main_app_ver", "8.1.30");
  no_action_bar_android_nebula_app_ver = json_obj.GetString("android_nebula_app_ver", "3.1.30");
  no_action_bar_ios_main_app_ver = json_obj.GetString("ios_main_app_ver", "8.1.30");
  no_action_bar_ios_nebula_app_ver = json_obj.GetString("ios_nebula_app_ver", "3.1.40");

  return true;
}

bool TestWhiteListUniverse::Load(const std::string& json_str) {
  base::Json json_obj(base::StringToJson(json_str));
  if (!json_obj.IsArray()) {
    LOG(WARNING) << "json_string config error, string: " << json_str;
    return false;
  }
  for (int i = 0; i < json_obj.size(); ++i) {
    auto array_item = json_obj.Get(i);
    auto* device_ids = array_item->Get("test_device_id");
    if (nullptr == device_ids || !device_ids->IsArray()) {
      auto log_info = nullptr == device_ids ? \
          "key device_id is not configured" : "key device_id configure error, shoud be array";
      LOG(WARNING) << log_info;
      continue;
    }
    auto* creative_ids = array_item->Get("creative_id");
    if (creative_ids == nullptr || !creative_ids->IsArray()) {
      auto log_info = nullptr == device_ids ? \
        "key creative_id is not configured" : "key creative_id configure error, shoud be array";
      LOG(WARNING) << log_info;
      continue;
    }
    struct TestDataItem data_item;
    bool enable_test_style = false;
    int position_id;
    array_item->GetBoolean("enable_test_style", &enable_test_style);
    position_id = array_item->GetInt("position_id", 0);
    data_item.enable_test_style = enable_test_style;
    data_item.position_id = position_id;

    auto cid_iter = creative_ids->array_begin();
    for (; cid_iter != creative_ids->array_end(); ++cid_iter) {
      int64_t cid;
      if (!((*cid_iter)->IntValue(&cid))) {
        continue;
      }
      data_item.creative_ids.emplace_back(cid);
      // 读取 live_id, 如果有的话
      auto live_id_str = array_item->GetString(absl::StrCat(cid), "");
      if (!live_id_str.empty()) {
        int64_t live_id;
        base::StringToInt64(live_id_str, &live_id);
        if (data_item.live_ids.find(cid) == data_item.live_ids.end()) {
          data_item.live_ids.emplace(std::make_pair(cid, live_id));
        }
      }
    }
    auto did_iter = device_ids->array_begin();
    for (; did_iter != device_ids->array_end(); ++did_iter) {
      std::string did;
      if (!((*did_iter)->StringValue(&did))) {
        continue;
      }
      white_list_data.emplace(std::make_pair(did, data_item));
    }
  }
  return true;
}

bool TestWhiteListUniverse::IsInWhiteList(std::string test_device_id) {
  if (white_list_data.find(test_device_id) == white_list_data.end()) {
    return false;
  }
  return true;
}

bool TestWhiteListUniverse::IsTestStyle(std::string test_device_id) {
  if (IsInWhiteList(test_device_id)) {
    return white_list_data[test_device_id].enable_test_style;
  }
  return false;
}

bool TestWhiteListUniverse::GetPositionId(std::string test_device_id, int64_t* position_id) {
  if (nullptr == position_id) {
    return false;
  }
  if (IsInWhiteList(test_device_id)) {
    (*position_id) = white_list_data[test_device_id].position_id;
    return true;
  }
  (*position_id) = 0;
  return false;
}

bool TestWhiteListUniverse::GetCreativeId(std::string test_device_id, std::vector<int64_t>* creative_ids) {
  if (nullptr == creative_ids) {
    return false;
  }
  if (IsInWhiteList(test_device_id)) {
    (*creative_ids) = white_list_data[test_device_id].creative_ids;
    return true;
  }
  creative_ids->clear();
  return false;
}

bool TestWhiteListUniverse::GetLiveId(std::string test_device_id, int64_t creative_id, int64_t* live_id) {
  if (nullptr == live_id) {
    return false;
  }
  if (IsInWhiteList(test_device_id)) {
    auto& live_ids = white_list_data[test_device_id].live_ids;
    if (live_ids.find(creative_id) != live_ids.end()) {
      (*live_id) = live_ids[creative_id];
      return true;
    }
  }
  (*live_id) = 0;
  return false;
}

bool FilterInvalidResponseConfig::Init() {
  auto iter = pb().explore().begin();
  for (; iter != pb().explore().end(); ++iter) {
    explore_config[iter->first] = iter->second;
  }
  iter = pb().galaxy().begin();
  for (; iter != pb().galaxy().end(); ++iter) {
    galaxy_config[iter->first] = iter->second;
  }
  iter = pb().nearby().begin();
  for (; iter != pb().nearby().end(); ++iter) {
    nearby_config[iter->first] = iter->second;
  }
  iter = pb().universe().begin();
  for (; iter != pb().universe().end(); ++iter) {
    universe_config[iter->first] = iter->second;
  }
  iter = pb().follow().begin();
  for (; iter != pb().follow().end(); ++iter) {
    follow_config[iter->first] = iter->second;
  }
  return true;
}

FilterInvalidResponseConfig::~FilterInvalidResponseConfig() {}

bool MerchantPhotoStyleConf::Init() {
  std::unordered_set<int32_t> tmp_set(pb().played_report_time().begin(), pb().played_report_time().end());
  played_report_time_set.swap(tmp_set);
  return true;
}

bool AdFlowGradeConfigConf::Init() {
  std::unordered_set<int64_t> tmp_set(pb().sub_page_config().sub_page_id_list().begin(),
                                      pb().sub_page_config().sub_page_id_list().end());
  sub_page_id_set.swap(tmp_set);
  return true;
}

bool AdFlowGradeConfigConf::InSubPageIdBlackList(int64_t sub_page_id) const {
  return sub_page_id_set.count(sub_page_id) > 0;
}

bool AdServerMergeBlacklist::Init() {
  std::unordered_set<int64_t> new_page_id_set(pb().page_id_list().begin(),
                                              pb().page_id_list().end());
  std::unordered_set<int64_t> new_sub_page_id_set(pb().sub_page_id_list().begin(),
                                                  pb().sub_page_id_list().end());
  page_id_set.swap(new_page_id_set);
  sub_page_id_set.swap(new_sub_page_id_set);
  return true;
}

bool AdServerMergeBlacklist::InPageIdSet(const int64_t num) const {
  return page_id_set.count(num) > 0;
}

bool AdServerMergeBlacklist::InSubPageIdSet(const int64_t num) const {
  return sub_page_id_set.count(num) > 0;
}

// both ad_rank and ad_server using
bool OnlinePriceDiscountConfig::Load(const std::string& json_str) {
  debug_str = json_str;
  base::Json d(base::StringToJson(debug_str));
  if (!d.IsObject()) {
    LOG(WARNING) << "Parsing json failed, string = " << json_str;
    return true;
  }
  double ratio = 0.0;
  for (const auto& v : d.objects()) {
    if ("account" == v.first) {
      int64_t account_id = 0;
      for (const auto& w : v.second->objects()) {
        account_id = 0;
        if (!absl::SimpleAtoi(w.first, &account_id)) {
          LOG(ERROR) << "convert json_key to int64 failed, json_key = " << w.first;
          continue;
        }
        if (true == w.second->FloatValue(&ratio)) {
          account_discount_map[account_id] = ratio;
        }
      }
    }
    if ("campaign" == v.first) {
      int campaign_id = 0;
      for (const auto& w : v.second->objects()) {
        campaign_id = 0;
        if (!absl::SimpleAtoi(w.first, &campaign_id)) {
          LOG(ERROR) << "convert json_key to int64 failed, json_key = " << w.first;
          continue;
        }
        if (true == w.second->FloatValue(&ratio)) {
          campaign_discount_map[campaign_id] = ratio;
        }
      }
    }
    if ("unit" == v.first) {
      int64_t unit_id = 0;
      for (const auto& w : v.second->objects()) {
        unit_id = 0;
        if (!absl::SimpleAtoi(w.first, &unit_id)) {
          LOG(ERROR) << "convert json_key to int64 unit_id failed, json_key = " << w.first;
          continue;
        }
        if (true == w.second->FloatValue(&ratio)) {
          unit_discount_map[unit_id] = ratio;
        }
      }
    }
  }
  return true;
}

bool MultiRewardedCoinDataList::Init() {
  std::unordered_map<int64, RewardedCoinDataBase> infomap;
  for (const auto& element : pb().rewarded_coin_conf_list()) {
    RewardedCoinDataBase base;
    base.prefix = element.second.prefix();
    base.enable_tag = element.second.enable_tag();
    base.global_cpm_tag = element.second.enable_tag();
    base.user_value_tag = element.second.user_value_tag();
    base.ecpm_bias_tag = element.second.ecpm_bias_tag();
    base.coin_upper_tag = element.second.coin_upper_tag();
    base.coin_lower_tag = element.second.coin_lower_tag();
    base.ratio_upper_tag = element.second.ratio_upper_tag();
    base.ratio_lower_tag = element.second.ratio_lower_tag();
    base.base_coin_num = element.second.base_coin_num();
    base.user_low_value_percentile_thr = element.second.user_low_value_percentile_thr();
    base.user_low_value_ratio = element.second.user_low_value_ratio();
    base.user_percentile_bias = element.second.user_percentile_bias();
    base.global_cpm = element.second.global_cpm();
    base.new_user_value_ratio = element.second.new_user_value_ratio();
    infomap[element.first] = base;
  }
  config_datas = infomap;
  return true;
}

bool RewardedCoinScalingByAccountList::Init() {
  std::unordered_map<int64, RewardedCoinScalingBase> infomap;
  for (const auto& element : pb().rewarded_coin_scaling_conf_list()) {
    RewardedCoinScalingBase base;
    base.coin_scaling_coef_tag = element.second.coin_scaling_coef_tag();
    base.coin_upper_tag = element.second.coin_upper_tag();
    base.coin_lower_tag = element.second.coin_lower_tag();
    base.deep_coin_scaling_coef_tag = element.second.deep_coin_scaling_coef_tag();
    base.deep_coin_upper_tag = element.second.deep_coin_upper_tag();
    base.deep_coin_lower_tag = element.second.deep_coin_lower_tag();
    infomap[element.first] = base;
  }
  config_datas = infomap;
  return true;
}

bool IncntvAdPredictNext1ViewValue2Coef::Init() {
  std::unordered_map<std::string, std::vector<IncntvAdBinToLinerCoef>> info_map;
  for (const auto& kv : pb().incntv_ad_predict_next_1view_value_2_coef()) {
    std::vector<IncntvAdBinToLinerCoef> info_list;
    for (const auto& elem : kv.second.bin_wb_list()) {
      IncntvAdBinToLinerCoef data;
      data.lower = elem.lower();
      data.upper = elem.upper();
      data.weight = elem.weight();
      data.bias = elem.bias();
      info_list.push_back(data);
    }
    info_map.insert({kv.first, info_list});
  }
  subpageid_to_bin_wb_list = info_map;
  return true;
}

bool SearchInspireParamsConfig::Init() {
  std::unordered_map<int64, SearchInspireParamsDataBase> infomap;
  for (const auto& element : pb().search_inspire_params_config()) {
    SearchInspireParamsDataBase base;
    base.enable_has_more = element.second.enable_has_more();
    base.enable_change_one = element.second.enable_change_one();
    base.has_more_prob = element.second.has_more_prob();
    base.change_one_prob = element.second.change_one_prob();
    base.photo_bill_time = element.second.photo_bill_time();
    base.live_bonus_time = element.second.live_bonus_time();
    infomap[element.first] = base;
  }
  config_datas = infomap;
  return true;
}

bool AdAutoGpmRatioExpConf::Init() {
  std::unordered_map<std::string, std::vector<AdGpmAutoRatio>> info_map;
  for (const auto& element : pb().exp_conf_map()) {
    std::vector<AdGpmAutoRatio> exp_conf_list;
    for (const auto& exp_conf : element.second.exp_conf_list()) {
      AdGpmAutoRatio exp_conf_data;
      exp_conf_data.weekday = exp_conf.weekday();
      exp_conf_data.gpm_ratio = exp_conf.gpm_ratio();
      exp_conf_list.push_back(exp_conf_data);
    }
    info_map[element.first] = exp_conf_list;
  }
  exp_conf_map_ = info_map;
  return true;
}

bool AdCpmUserUnifyBonusRatioExpConf::Init() {
  std::unordered_map<std::string, std::vector<AllocByUserCpmBonusExpConf>> info_map;
  for (const auto& element : pb().exp_conf_map()) {
    std::vector<AllocByUserCpmBonusExpConf> exp_conf_list;
    for (const auto& exp_conf : element.second.exp_conf_list()) {
      AllocByUserCpmBonusExpConf exp_conf_data;
      exp_conf_data.cpm_lbound = exp_conf.cpm_lbound();
      exp_conf_data.cpm_ubound = exp_conf.cpm_ubound();
      for (const auto& it : exp_conf.user_type_bonus_ratio_map()) {
        exp_conf_data.user_type_bonus_ratio_map[it.first] = it.second;
      }
      exp_conf_list.push_back(exp_conf_data);
    }
    info_map[element.first] = exp_conf_list;
  }
  exp_conf_map_ = info_map;
  return true;
}

bool AdUnifyBonusRatioExpConfNew::Init() {
  std::unordered_map<std::string, std::vector<UnifyBonusExpConf>> info_map;
  for (const auto& element : pb().exp_conf_map()) {
    std::vector<UnifyBonusExpConf> exp_conf_list;
    for (const auto& exp_conf : element.second.exp_conf_list()) {
      UnifyBonusExpConf exp_conf_data;
      exp_conf_data.cpm_lbound = exp_conf.cpm_lbound();
      exp_conf_data.cpm_ubound = exp_conf.cpm_ubound();
      exp_conf_data.bonus_ratio = exp_conf.bonus_ratio();
      exp_conf_list.push_back(exp_conf_data);
    }
    info_map[element.first] = exp_conf_list;
  }
  exp_conf_map_ = info_map;
  return true;
}

bool AdLiveRerankTimeGapExpConf::Init() {
  std::unordered_map<std::string, std::vector<LiveRerankTime>> info_map;
  for (const auto& element : pb().exp_conf_map()) {
    std::vector<LiveRerankTime> exp_conf_list;
    for (const auto& exp_conf : element.second.exp_conf_list()) {
      LiveRerankTime exp_conf_data;
      exp_conf_data.cpm_lbound = exp_conf.cpm_lbound();
      exp_conf_data.cpm_ubound = exp_conf.cpm_ubound();
      exp_conf_data.gap_time = exp_conf.gap_time();
      exp_conf_data.poll_time = exp_conf.poll_time();
      exp_conf_list.push_back(exp_conf_data);
    }
    info_map[element.first] = exp_conf_list;
  }
  exp_conf_map_ = info_map;
  return true;
}

bool AdPackSpecialAdsConfigData::Init() {
  for (const auto& page_id : pb().page_ids()) {
    special_page_ids.emplace(page_id);
  }
  for (const auto& sub_page_id : pb().sub_page_ids()) {
    special_sub_page_ids.emplace(sub_page_id);
  }
  return true;
}

bool AdPackSpecialAdsConfigData::IsSpecialPageId(const int64& page_id) const {
  if (special_page_ids.count(page_id) == 0) {
    return false;
  }
  return true;
}

bool AdPackSpecialAdsConfigData::IsSpecialSubPageId(const int64& sub_page_id) const {
  if (special_sub_page_ids.count(sub_page_id) == 0) {
    return false;
  }
  return true;
}

bool FrontRpcDegradeData::Init() {
  return true;
}

int32_t FrontRpcDegradeData::GetRpcDegradeRatio(const std::string& rpc_name) const {
  if (rpc_name.empty()) {
    return 100;
  }
  auto iter = pb().rpc_degrade_ratio().find(rpc_name);
  if (iter == pb().rpc_degrade_ratio().end()) {
    return 100;
  }
  return iter->second;
}

bool NoLimitVirtualGoldSceneConfigData::Init() {
  flow_type_set.insert(pb().ad_request_flow_type_config().begin(), pb().ad_request_flow_type_config().end());
  page_id_set.insert(pb().page_id_config().begin(), pb().page_id_config().end());
  sub_page_id_set.insert(pb().sub_page_id_config().begin(), pb().sub_page_id_config().end());
  return true;
}

bool NoLimitVirtualGoldSceneConfigData::IsNoLimitVirtualGoldScene(const std::string& request_flow_type,
                                                                  int64_t page_id,
                                                                  int64_t sub_page_id) const {
  if (flow_type_set.find(request_flow_type) != flow_type_set.end()) {
    return true;
  }
  if (page_id_set.find(page_id) != page_id_set.end()) {
    return true;
  }
  if (sub_page_id_set.find(sub_page_id) != sub_page_id_set.end()) {
    return true;
  }
  return false;
}

bool HardTargetFilterConfigData::Init() {
  black_sub_page_id_set.insert(pb().black_sub_page_id_list().begin(), pb().black_sub_page_id_list().end());
  return true;
}

bool HardTargetFilterConfigData::EnableHardTargetFilter(int64_t sub_page_id) const {
  if (!pb().enable_hard_target_filter()) {
    return false;
  }

  if (black_sub_page_id_set.count(sub_page_id)) {
    return false;
  }
  return true;
}

bool AdPosPremiumConfigData::Init() {
  feed_flow_pos_premium.insert(pb().feed().begin(), pb().feed().end());
  thanos_flow_pos_premium.insert(pb().thanos().begin(), pb().thanos().end());
  return true;
}

bool CommonDyeingEraseConfigData::Init() {
  for (auto iter = pb().config().begin(); iter != pb().config().end(); iter++) {
    ks::front_server::CommonDyeingEraseConfigData::DyeingEraseMeta meta;
    for (auto it = iter->second.front_request_type().begin();
                        it != iter->second.front_request_type().end(); it++) {
      if (*it == "EXPLORE_REQUEST") {
        meta.front_request_type_set.insert(1);
      }
      if (*it == "FOLLOW_REQUEST") {
        meta.front_request_type_set.insert(2);
      }
      if (*it == "NEARBY_REQUEST") {
        meta.front_request_type_set.insert(5);
      }
      if (*it == "KWAI_GALAXY_REQUEST") {
        meta.front_request_type_set.insert(8);
      }
      if (*it == "SEARCH_REQUEST") {
        meta.front_request_type_set.insert(9);
      }
      if (*it == "SPLASH_REQUEST") {
        meta.front_request_type_set.insert(7);
      }
    }
    meta.product_name_set.insert(iter->second.product_name_list().begin(),
                                 iter->second.product_name_list().end());
    meta.account_id_set.insert(iter->second.account_id_list().begin(),
                                 iter->second.account_id_list().end());
    meta.account_type_set.insert(iter->second.account_type_list().begin(),
                                 iter->second.account_type_list().end());
    data[iter->first] = meta;
  }
  return true;
}

bool PecMultiCouponConfig::Init() {
  author_id_set_.clear();
  for (auto& element : this->pb().coupon_config()) {
    const auto& author_ids = element.author_id();
    author_id_set_.insert(author_ids.begin(), author_ids.end());
  }
  VLOG_EVERY_N(3, 100) << "PecMultiCouponConfig, author id:"
                       << std::accumulate(author_id_set_.begin(), author_id_set_.end(), std::string(),
                                          [](std::string& s, int64 p) {
                                            return s + (s.empty() ? "" : ",") + std::to_string(p);
                                          });
  return true;
}

bool PecMultiCouponConfig::IsAuthorHit(int64 author_id) const {
  if (author_id_set_.count(author_id) > 0) {
    return true;
  }
  return false;
}

bool GameAdForceDirectCallWhiteListConfig::Init() {
  for (auto& product_name : this->pb().product_names()) {
    product_names.insert(base::CityHash64(product_name.c_str(), product_name.size()));
  }
  return true;
}

bool GameAdForceDirectCallWhiteListConfig::InWhiteList(const std::string& product_name) const {
  if (product_names.count(base::CityHash64(product_name.c_str(), product_name.size())) > 0) {
    return true;
  }
  return false;
}

bool SplashPrefetchRecordWhiteList::Init() {
  for (const int64_t account_id : pb().account_ids()) {
    account_ids.insert(account_id);
  }
  for (const int64_t unit_id : pb().unit_ids()) {
    unit_ids.insert(unit_id);
  }
  for (const int64_t creative_id : pb().creative_ids()) {
    creative_ids.insert(creative_id);
  }
  return true;
}

bool SplashPrefetchRecordWhiteList::IsInWhiteList(int64_t account_id,
                                                  int64_t unit_id,
                                                  int64_t creative_id) const {
  if (account_ids.count(account_id) || unit_ids.count(unit_id) || creative_ids.count(creative_id)) {
    return true;
  }
  return false;
}

bool SiXinServicePageComponent::Init() {
  type_2_sub_types_.clear();
  for (const auto& page_component : pb().config()) {
    auto type = page_component.type();
    auto sub_type = page_component.sub_type();
    type_2_sub_types_[type].emplace(sub_type);
  }
  return true;
}

bool SiXinServicePageComponent::IsSiXinServicePage(int32_t type, int32_t sub_type) const {
  auto iter = type_2_sub_types_.find(type);
  if (iter != type_2_sub_types_.end()) {
    if (iter->second.count(sub_type) > 0) {
      return true;
    }
  }
  return false;
}

bool DegradeLevelConfigData::Init() {
  sub_page_id_to_level_.clear();
  page_id_to_level_.clear();
  request_flow_type_to_level_.clear();

  if (!pb().enable() || !pb().has_level_configs()) {
    return true;
  }

  // 填充 id 到 level 的 map，用于查询
  const auto fill_map = [] (DegradeLevel degrade_level, const auto& list, auto& map) {
    for (const auto& key : list) {
      const auto iter = map.find(key);
      if (iter != map.end()) {
        LOG(WARNING) << "[DegradeLevelConfig] sub_page_id/page_id/request_flow_type "
                     << key << " is duplicated.";
        continue;
      }
      map.insert({key, degrade_level});
    }
  };

  const auto fill_level = [this, &fill_map] (const auto& level_config, DegradeLevel degrade_level) {
    fill_map(degrade_level, level_config.sub_page_id_list(), sub_page_id_to_level_);
    fill_map(degrade_level, level_config.page_id_list(), page_id_to_level_);
    fill_map(degrade_level, level_config.request_flow_type_list(), request_flow_type_to_level_);
  };

  if (pb().level_configs().has_level_1()) {
    fill_level(pb().level_configs().level_1(), DegradeLevel::kLevel1);
  }
  if (pb().level_configs().has_level_2()) {
    fill_level(pb().level_configs().level_2(), DegradeLevel::kLevel2);
  }
  return true;
}

int32_t DegradeLevelConfigData::GetRatioByDegradeLevel(DegradeLevel level) const {
  switch (level) {
    case DegradeLevel::kLevel1:
      return pb().level_1();
    case DegradeLevel::kLevel2:
      return pb().level_2();
    case DegradeLevel::kLevelDefault:
      return pb().level_default();
    default:
      LOG(ERROR) << "[DegradeLevelConfig] unsupported level " << static_cast<int32_t>(level);
      return 0;
  }
}

std::pair<DegradeLevel, int32_t> DegradeLevelConfigData::GetDegradeLevelAndRatio(
    AdEnum_AdRequestFlowType request_flow_type,
    int64_t page_id,
    int64_t sub_page_id) const {
  if (!pb().enable()) {
    return {DegradeLevel::kLevelDefault, 100};
  }
  auto request_flow_type_str = AdEnum_AdRequestFlowType_Name(request_flow_type);
  // 在三个 map 中查询 level，有重叠的以 ratio 较小的为准，查询不到默认为 level_default
  int ratio = INT_MAX;
  auto degrade_level = DegradeLevel::kLevelDefault;
  const auto find_level_in = [&] (const auto& key, const auto& map) {
    const auto iter = map.find(key);
    if (iter != map.end()) {
      int level_ratio = GetRatioByDegradeLevel(iter->second);
      if (level_ratio <= ratio) {
        ratio = level_ratio;
        degrade_level = iter->second;
      }
    }
  };
  find_level_in(request_flow_type_str, request_flow_type_to_level_);
  find_level_in(page_id, page_id_to_level_);
  find_level_in(sub_page_id, sub_page_id_to_level_);
  if (degrade_level == DegradeLevel::kLevelDefault) {
    ratio = GetRatioByDegradeLevel(DegradeLevel::kLevelDefault);
  }
  return {degrade_level, ratio};
}

bool ResourceWhiteBoxFlowTagConfigData::Init() {
  if (KS_UNLIKELY(!pb().enabled())) {
    return true;
  }
  sub_page_id_to_flow_tag_.clear();
  request_flow_type_to_flow_tag_.clear();
  const auto& flow_tag_to_sub_page_ids = pb().flow_tag_to_sub_page_ids();
  const auto& flow_tag_to_request_flow_type = pb().flow_tag_to_ad_request_flow_type();

  for (const auto& [flow_tag, sub_page_ids] : flow_tag_to_sub_page_ids) {
    for (const auto& sub_page_id : sub_page_ids.sub_page_id_list()) {
      sub_page_id_to_flow_tag_.insert({sub_page_id, flow_tag});
    }
  }

  for (const auto& [flow_tag, request_flow_type_name] : flow_tag_to_request_flow_type) {
    auto request_flow_type = kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNKNOWN;
    bool ret = kuaishou::ad::AdEnum_AdRequestFlowType_Parse(request_flow_type_name, &request_flow_type);
    ASSERT_OTHERWISE(ret, continue);
    request_flow_type_to_flow_tag_.insert({request_flow_type, flow_tag});
  }

  return true;
}

std::string ResourceWhiteBoxFlowTagConfigData::GetFlowTag(AdEnum_AdRequestFlowType request_flow_type,
                                                          int64_t sub_page_id) const {
  if (KS_UNLIKELY(!pb().enabled())) {
    return "";
  }
  const auto& iter = sub_page_id_to_flow_tag_.find(sub_page_id);
  if (iter != sub_page_id_to_flow_tag_.end()) {
    return iter->second;
  }
  const auto& iter1 = request_flow_type_to_flow_tag_.find(request_flow_type);
  if (iter1 != request_flow_type_to_flow_tag_.end()) {
    return iter1->second;
  }
  return "";
}

bool MerchantConfig::Load(const std::string& json_str) {
  raw_json_str = json_str;
  base::Json json_obj(base::StringToJson(json_str));
  if (!json_obj.IsObject()) {
    LOG(WARNING) << "Parse merchant config json failed, string: " << json_str;
    return false;
  }

  merchant_ratio = json_obj.GetInt("merchant_ratio", -1);
  main_merchant_ratio = json_obj.GetInt("main_merchant_ratio", -1);
  nebula_merchant_ratio = json_obj.GetInt("nebula_merchant_ratio", -1);
  thanos_merchant_ratio = json_obj.GetInt("thanos_merchant_ratio", -1);
  max_retrieval_record_number = json_obj.GetInt("max_retrieval_record_number", 100);
  max_ranking_prepare_left_number = json_obj.GetInt("max_ranking_prepare_left_number", 20);
  android_main_app_ver = json_obj.GetString("android_main_app_ver", "7.4.30");
  android_nebula_app_ver = json_obj.GetString("android_nebula_app_ver", "2.4.3");
  ios_main_app_ver = json_obj.GetString("ios_main_app_ver", "7.4.30");
  ios_nebula_app_ver = json_obj.GetString("ios_nebula_app_ver", "2.4.0");
  android_main_nearby_app_ver = json_obj.GetString("android_main_nearby_app_ver", "7.5.50");
  android_nebula_nearby_app_ver = json_obj.GetString("android_nebula_nearby_app_ver", "2.6.1");
  ios_main_nearby_app_ver = json_obj.GetString("ios_main_nearby_app_ver", "8.0.10");
  ios_nebula_nearby_app_ver = json_obj.GetString("ios_nebula_nearby_app_ver", "3.0.10");
  android_feature_selected_app_ver = json_obj.GetString("android_feature_selected_app_ver", "8.0.30");
  ios_feature_selected_app_ver = json_obj.GetString("ios_feature_selected_app_ver", "8.0.30");
  android_inner_explore_app_ver = json_obj.GetString("android_feature_selected_app_ver", "9.5.50");
  ios_inner_explore_app_ver = json_obj.GetString("ios_feature_selected_app_ver", "9.6.50");

  android_main_detail_app_ver = json_obj.GetString("android_main_detail_app_ver", "8.0.10");
  android_nebula_detail_app_ver = json_obj.GetString("android_nebula_detail_app_ver", "3.0.10");
  ios_main_detail_app_ver = json_obj.GetString("ios_main_detail_app_ver", "8.0.10");
  ios_nebula_detail_app_ver = json_obj.GetString("ios_nebula_detail_app_ver", "3.0.10");

  android_main_splash_app_ver = json_obj.GetString("android_main_splash_app_ver", "");
  android_nebula_splash_app_ver = json_obj.GetString("android_nebula_splash_app_ver", "");
  ios_main_splash_app_ver = json_obj.GetString("ios_main_splash_app_ver", "");
  ios_nebula_splash_app_ver = json_obj.GetString("ios_nebula_splash_app_ver", "");

  enable_ab_test = json_obj.GetBoolean("enable_ab_test", false);
  enable_universe_request = json_obj.GetBoolean("enable_universe_request", false);
  enable_detail_request = json_obj.GetBoolean("enable_detail_request", false);
  enable_splash_request = json_obj.GetBoolean("enable_splash_request", false);
  enable_merchant_trace_partial = json_obj.GetBoolean("enable_merchant_trace_partial", true);
  enable_merchant = json_obj.GetBoolean("enable_merchant", false);
  enable_unlogin_request = json_obj.GetBoolean("enable_unlogin_request", false);
  merge_retrieval_record = json_obj.GetBoolean("merge_retrieval_record", true);
  merge_retrieval_statistics_to_session = json_obj.GetBoolean("merge_retrieval_statistics_to_session", true);
  use_fake_photo_id_for_live = json_obj.GetBoolean("use_fake_photo_id_for_live", true);
  fill_default_display_info_for_live = json_obj.GetBoolean("fill_default_display_info_for_live", true);
  enable_rank_test_value = json_obj.GetBoolean("enable_rank_test_value", true);
  enable_main_nearby = json_obj.GetBoolean("enable_main_nearby", false);
  enable_nebula_nearby = json_obj.GetBoolean("enable_nebula_nearby", false);
  use_independ_amd_request = json_obj.GetBoolean("use_independ_amd_request", false);
  use_target_live_res = json_obj.GetBoolean("use_target_live_res", false);
  enable_radom_explore = json_obj.GetBoolean("enable_radom_explore", false);

  enable_knews_request_merchant = json_obj.GetBoolean("enable_knews_request_merchant", false);
  enable_kswechatapp_request_merchant = json_obj.GetBoolean("enable_kswechatapp_request_merchant", false);
  enable_aggregate_request_merchant = json_obj.GetBoolean("enable_aggregate_request_merchant", false);

  return true;
}

void MerchantConfig::describe(std::ostream& os) const {
  os << base::StringToJson(raw_json_str);
}

bool ExploreLiveConfig::Load(const std::string& json_str) {
  raw_json_str = json_str;
  base::Json json_obj(base::StringToJson(json_str));
  if (!json_obj.IsObject()) {
    LOG(WARNING) << "Parse explore live config json failed, string: " << json_str;
    return false;
  }
  enable_explore_live_fans_top = json_obj.GetBoolean("enable_explore_live_fans_top", false);
  enable_main_ab_test = json_obj.GetBoolean("enable_main_ab_test", false);
  enable_thanos_ab_test = json_obj.GetBoolean("enable_thanos_ab_test", false);
  enable_nebula_ab_test = json_obj.GetBoolean("enable_nebula_ab_test", false);
  explore_live_fans_top_ratio = json_obj.GetInt("explore_live_fans_top_ratio", 0);
  main_explore_live_ratio = json_obj.GetInt("main_explore_live_ratio", 0);
  nebula_explore_live_ratio = json_obj.GetInt("nebula_explore_live_ratio", 0);
  thanos_explore_live_ratio = json_obj.GetInt("thanos_explore_live_ratio", 0);
  explore_live_main_vv_step = json_obj.GetInt("explore_live_main_vv_step", 0);
  explore_live_thanos_vv_step = json_obj.GetInt("explore_live_thanos_vv_step", 0);
  explore_live_nebula_vv_step = json_obj.GetInt("explore_live_nebula_vv_step", 0);
  explore_live_main_time_sec_step = json_obj.GetInt("explore_live_main_time_sec_step", 3600);
  explore_live_thanos_time_sec_step = json_obj.GetInt("explore_live_thanos_time_sec_step", 3600);
  explore_live_nebula_time_sec_step = json_obj.GetInt("explore_live_nebula_time_sec_step", 3600);
  explore_live_day_limit = json_obj.GetInt("explore_live_day_limit", 0);
  explore_live_retrieval_max_count = json_obj.GetInt("explore_live_retrieval_max_count", 200);
  predict_max_ps_live = json_obj.GetInt("predict_max_ps_live", 0);
  android_main_app_ver = json_obj.GetString("android_main_app_ver", "7.4.30");
  android_nebula_app_ver = json_obj.GetString("android_nebula_app_ver", "2.4.3");
  ios_main_app_ver = json_obj.GetString("ios_main_app_ver", "7.4.30");
  ios_nebula_app_ver = json_obj.GetString("ios_nebula_app_ver", "2.4.0");
  return true;
}

void ExploreLiveConfig::describe(std::ostream& os) const {
  os << raw_json_str << ", reality values: {"
     << "enable_explore_live_fans_top: " << enable_explore_live_fans_top
     << ", enable_main_ab_test: " << enable_main_ab_test
     << ", enable_thanos_ab_test: " << enable_thanos_ab_test
     << ", enable_nebula_ab_test: " << enable_nebula_ab_test
     << ", explore_live_fans_top_ratio: " << explore_live_fans_top_ratio
     << ", main_explore_live_ratio: " << main_explore_live_ratio
     << ", nebula_explore_live_ratio: " << nebula_explore_live_ratio
     << ", thanos_explore_live_ratio: " << thanos_explore_live_ratio
     << ", explore_live_main_vv_step: " << explore_live_main_vv_step
     << ", explore_live_thanos_vv_step: " << explore_live_thanos_vv_step
     << ", explore_live_nebula_vv_step: " << explore_live_nebula_vv_step
     << ", explore_live_main_time_sec_step: " << explore_live_main_time_sec_step
     << ", explore_live_thanos_time_sec_step: " << explore_live_thanos_time_sec_step
     << ", explore_live_nebula_time_sec_step: " << explore_live_nebula_time_sec_step
     << ", explore_live_day_limit: " << explore_live_day_limit
     << ", explore_live_retrieval_max_count: " << explore_live_retrieval_max_count
     << ", predict_max_ps_live: " << predict_max_ps_live << ", android_main_app_ver: " << android_main_app_ver
     << ", android_nebula_app_ver: " << android_nebula_app_ver << ", ios_main_app_ver: " << ios_main_app_ver
     << ", ios_nebula_app_ver: " << ios_nebula_app_ver << "}";
}

int64_t PageSize2MaxReturn::GetMaxReturn(int64_t page_size) const {
  auto it = pb().map_data().find(std::to_string(page_size));
  if (it == pb().map_data().end())
    return pb().default_value();
  return it->second;
}

bool DebugSpecialFilterReasons::Init() {
  kuaishou::log::ad::AdTraceFilterCondition filter_reason_enum =
      kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION;
  for (const std::string& reason : pb().reasons()) {
    if (kuaishou::log::ad::AdTraceFilterCondition_Parse(reason, &filter_reason_enum)) {
      reasons.insert(static_cast<int64_t>(filter_reason_enum));
    }
  }
  return true;
}

int64_t DebugSpecialFilterReasons::IsSpecialReason(const int64_t& reason) const {
  if (reasons.count(reason) > 0) {
    return true;
  }
  return false;
}

bool DisableFactorConfig::Init() {
  udf.clear();
  ecpc.clear();
  filter.clear();
  for (const auto& factor : pb().udf()) {
    udf.emplace(factor);
  }
  for (const auto& factor : pb().ecpc()) {
    ecpc.emplace(factor);
  }
  for (const auto& factor : pb().filter()) {
    filter.emplace(factor);
  }
  return true;
}

bool KessGrpcClients::Load(const std::string& json_str) {
  timeout_ms_ = -1;
  const std::string ksn = getenv("KWS_SERVICE_NAME");
  base::Json json(base::StringToJson(json_str));
  if (!json.IsArray()) {
    return false;
  }
  for (auto it = json.array_begin(); it != json.array_end(); ++it) {
    const base::Json* conf = *it;
    if (conf != nullptr && conf->IsObject()) {
      const std::string kess_name = conf->GetString("kess_name", "");
      if (kess_name == ksn) {
        timeout_ms_ = conf->GetInt("time_out", -1);
        break;
      }
    }
  }
  return true;
}

int64_t KessGrpcClients::GetTimeoutMs() {
  return timeout_ms_;
}

}  // namespace front_server
}  // namespace ks
