#pragma once
#include <atomic>
#include <map>
#include <memory>
#include <set>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <utility>

#include "base/common/basic_types.h"
#include "base/strings/string_number_conversions.h"
#include "ccbase/token_bucket.h"
#include "kconf/kconf.h"
#include "rapidjson/document.h"
#include "absl/container/flat_hash_set.h"
#include "absl/container/flat_hash_map.h"
#include "rttr/registration"
#include "serving_base/jansson/json.h"
#include "third_party/abseil/absl/strings/substitute.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_base/src/serialization/serializer.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/front_server/util/kconf/kconf_data.pb.h"
#include "teams/ad/ad_index/framework/target/target_key.h"
#include "teams/ad/front_server/util/common/common.h"

using namespace ks::front_server::kconf;  // NOLINT
using ::kuaishou::ad::AdEnum_AdRequestFlowType;

namespace ks {
namespace front_server {

struct UniverseAdSwitch {
  UniverseAdSwitch() = default;
  virtual ~UniverseAdSwitch() = default;

  std::unordered_map<std::string, int64_t> config;
  bool Load(const rapidjson::Value &v) {
    return ks::ad_base::JsonSerializer::FromJson(v, *this);
  }

  bool Load(const std::string &v) {
    return ks::ad_base::JsonSerializer::FromJson(v, *this);
  }

  void describe(std::ostream &os) const {
    os << ks::ad_base::JsonSerializer::ToJson(*this);
  }

  bool Allowed(const std::string &app_id,
               const int64_t page_id,
               const int64_t sub_page_id);

  RTTR_ENABLE();
};

struct AdGpmAutoRatio {
  int32_t weekday = 0;
  int32_t hour = 0;
  double gpm_ratio = 0.0;
  void clear() {
    weekday = 0;
    hour = 0;
    gpm_ratio = 0.0;
  }
};

struct AdAutoGpmRatioExpConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::AdGpmAutoRatioExp> {
  std::unordered_map<std::string, std::vector<AdGpmAutoRatio>> exp_conf_map_;
  bool Init() override;
};

// 联盟产品级别补贴参数设置
struct UniverseProductBonusCombineParams {
  int64_t product_bonus_upperbound;  // 天级总补贴约束（元）
  int64_t bonus_duration;  // 补贴上限时长
  int64_t cost_threshold;  // 空耗上限
  double cost_rate_threshold;  // 成本率上限
  int64 conv_cnt;  // 转化数上限
  std::unordered_map<std::string, double> cpm_map;  // 补贴 cpm 上限 key: 产品名字 , value: 上限系数
  std::unordered_map<uint64_t, int64_t> timestart;
  std::unordered_map<uint64_t, double> budget;
  // key: city_hash 的 product_name, value: 天级产品维度的补贴预算
};

struct ForceProductSet
    : public ks::ad_base::kconf::KconfInitProto<kconf::ForceProductSet> {
  bool Init();
  std::unordered_map<std::string, std::unordered_map<int64, std::unordered_set<int64>>> GetParams() const;

 private:
  std::unordered_map<std::string, std::unordered_map<int64, std::unordered_set<int64>>>
      force_product_photo_set;
};

struct UnifyBonusExpConf {
  double cpm_lbound = 0.0;
  double cpm_ubound = 0.0;
  double bonus_ratio = 0.0;
  void Clear() {
    cpm_lbound = 0.0;
    cpm_ubound = 0.0;
    bonus_ratio = 0.0;
  }
};

struct AllocByUserCpmBonusExpConf {
  double cpm_lbound = 0.0;
  double cpm_ubound = 0.0;
  std::unordered_map<std::string, double> user_type_bonus_ratio_map;
  void Clear() {
    cpm_lbound = 0.0;
    cpm_ubound = 0.0;
    user_type_bonus_ratio_map.clear();
  }
};

struct AdUnifyBonusRatioExpConfNew
    : public ks::ad_base::kconf::KconfInitProto<kconf::AdUnifyBonusRatioExp> {
  std::unordered_map<std::string, std::vector<UnifyBonusExpConf>> exp_conf_map_;
  bool Init() override;
};

struct AdCpmUserUnifyBonusRatioExpConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::AdCpmUserUnifyBonusRatioExp> {
  std::unordered_map<std::string, std::vector<AllocByUserCpmBonusExpConf>> exp_conf_map_;
  bool Init() override;
};

struct LiveRerankTime {
  int64_t cpm_lbound = 0;
  int64_t cpm_ubound = 0;
  int32_t gap_time = 0;
  int32_t poll_time = 0;
  void Clear() {
    cpm_lbound = 0;
    cpm_ubound = 0;
    gap_time = 0;
    poll_time = 0;
  }
};

struct AdLiveRerankTimeGapExpConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::AdLiveRerankTimeExp> {
  std::unordered_map<std::string, std::vector<LiveRerankTime>> exp_conf_map_;
  bool Init() override;
};

struct UnionMidPageWhiteConfig : public ks::ad_base::kconf::KconfInitProto<kconf::UnionMidPageWhiteConf> {
 public:
  bool Init();
  bool IsInWhiteList(const std::string& product_name, int64_t account_id) const;

 private:
  std::unordered_map<int64_t, absl::flat_hash_set<int64_t>> product_white_map_;
};

struct SearchSmartCoverConfig : public ks::ad_base::kconf::KconfInitProto<kconf::SearchSmartPhotoCoverConf> {
 public:
  bool Init();
  bool IsInShieldList(const std::string& exp_name, int64_t page_num, int64_t pos) const;

 private:
  std::unordered_map<std::string, absl::flat_hash_set<int64_t>> exp_map_;
};

struct AdmitFlowBlackListConfig : public ks::ad_base::kconf::KconfInitProto<kconf::AdmitFlowBlackListConfigPb> {  // NOLINT
 public:
  bool Init();
  bool HitBlackList(const int64_t& page_id, const int64_t& sub_page_id) const;

 private:
  std::unordered_set<int64_t> page_id_set;
  std::unordered_set<int64_t> sub_page_id_set;
};

struct StraFeatDim {
  absl::flat_hash_set<int64_t> ad_monitor_type_set;
  absl::flat_hash_set<int64_t> ad_queue_type_set;
  absl::flat_hash_set<std::string> user_level_set;
  absl::flat_hash_set<std::string> page_id_set;
  double alpha_i = 1.0;
  double beta_i = 1.0;
  double gamma_i = 1.0;

  void Clear() {
    ad_monitor_type_set.clear();
    ad_queue_type_set.clear();
    user_level_set.clear();
    page_id_set.clear();
    alpha_i = 1.0;
    beta_i = 1.0;
    gamma_i = 1.0;
  }
};

struct UnifyStraParam {
  bool enable_admit = false;
  bool enable_uplift_control = false;
  bool enable_commerical_user_tag = false;
  bool enable_merchant_user_tag = false;
  bool enable_optim_cpm = false;
  bool enable_optim_rank_benefit = false;
  bool enable_fit_log_func = false;
  bool enable_fit_exp_func = false;

  void Clear() {
    enable_admit = false;
    enable_uplift_control = false;
    enable_commerical_user_tag = false;
    enable_merchant_user_tag = false;
    enable_optim_cpm = false;
    enable_optim_rank_benefit = false;
    enable_fit_log_func = false;
    enable_fit_exp_func = false;
  }
};

struct BidOptimExpConfigClass : public ks::ad_base::kconf::KconfInitProto<kconf::BidOptimExpConfig> {
 public:
  bool Init();
  // 特征 map
  absl::flat_hash_map<std::string, absl::flat_hash_map<std::string, StraFeatDim>> strategy_map;
  // 全局参数 map
  absl::flat_hash_map<std::string, UnifyStraParam> unify_param_map;
};


struct BidOptimCalcHyperParamClass :
                        public ks::ad_base::kconf::KconfInitProto<kconf::BidOptimCalcHyperParam> {
 public:
  bool Init();
  // 全局 PID 调参 map
  absl::flat_hash_map<std::string, double> bid_constraint_map;
};

struct KuaiGameFeedPlayConfClass : public ks::ad_base::kconf::KconfInitProto<kconf::KuaiGameFeedPlayConf> {
 public:
  bool Init();
  absl::flat_hash_set<std::string> device_mod_set;
  absl::flat_hash_set<std::string> product_name_set;
  absl::flat_hash_set<int64> account_id_set;
  std::vector<int64> age_bound_list;
  std::string app_version_tag;
};

struct newFlowAdmitConfig : public ks::ad_base::kconf::KconfInitProto<kconf::newFlowAdmitConfigPb> {  // NOLINT
 public:
  bool Init();
  bool GetAbKey(const int64_t& sub_page_id, std::string* ab_key) const;
  bool GetAbKeyByPageId(const int64_t& page_id, std::string* ab_key) const;
};

struct UserActionTypeConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::UserActionType> {
  absl::flat_hash_map<int32_t, absl::flat_hash_set<int32_t>> user_action_set;
  bool Init() override;
  const absl::flat_hash_set<int32_t>* get_user_action_set(int32_t pick_stratagy) const {
    auto iter = user_action_set.find(pick_stratagy);
    if (iter != user_action_set.end()) {
      return &(iter->second);
    }
    return nullptr;
  }
};
struct AdMerchantCardConfig {
  AdMerchantCardConfig() = default;
  virtual ~AdMerchantCardConfig() = default;
  struct AdMerchantProduct {
    std::string icon;
    std::string title;
    int32_t origin_price;  //单位 cent
    int32_t selling_price;  //单位 cent
  };
  std::unordered_map<int64, AdMerchantProduct> merchant_conf;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct AppStoreInReview {
  AppStoreInReview() = default;
  ~AppStoreInReview() = default;
  bool Load(const std::string &v);
  bool IsReviewVersion(const std::string& version);
  std::vector<std::string> FindVersions(const std::string& type);
  std::unordered_map<std::string, std::vector<std::string>> review_versions;
};

struct SubCardItem {
  std::string url;
  std::string title;
  std::string sub_title;
};

struct TestDataItem {
  bool enable_test_style;
  int64_t position_id;
  std::vector<int64_t> creative_ids;
  absl::flat_hash_map<int64_t, int64_t> live_ids;  // creative_id : live_id
};

struct TestWhiteList {
  absl::flat_hash_map<int64, struct TestDataItem> white_list_data;
  bool Load(const std::string& json_str);
  bool IsInWhiteList(int64_t user_id);
  // 不在白名单返回 false，在白名单返回白名单配置
  bool IsTestStyle(int64_t user_id);
  bool GetPositionId(int64_t user_id, int64_t* position_id);
  bool GetCreativeId(int64_t user_id, std::vector<int64_t>* creative_ids);
  bool GetLiveId(int64_t user_id, int64_t creative_id, int64_t* live_id);
};

struct ClientLimiter : public ks::ad_base::kconf::KconfInitProto<kconf::ClientLimiter> {
  ClientLimiter() = default;
  virtual ~ClientLimiter();
  bool Init() override;

  void describe(std::ostream &os) const;
  bool InitQpsBucket();
  bool Allow(const std::string &who, uint32_t token_num) const {
    auto it = qps_bucket.find(who);
    if (it == qps_bucket.end()) {
      return true;
    }
    if (it->second->Get(token_num)) {
        return true;
    }
    it->second->Gen();
    return it->second->Get(token_num);
  }

  mutable std::unordered_map<std::string, ccb::TokenBucket*> qps_bucket;
};

struct AdDynamicCreative {
  AdDynamicCreative() = default;
  virtual ~AdDynamicCreative() = default;
  std::string city_default;
  std::string prov_default;
  std::string date_default;
  std::string weekday_default;
  std::string holiday_default;
  std::string man;
  std::string women;
  std::string gender_default;
  std::string appear_man;
  std::string appear_women;
  std::string appear_default;
  std::string anti_man;
  std::string anti_women;
  std::string anti_default;
  std::string anti_wife;
  std::string anti_husband;
  std::string anti_couple;
  std::string age_default;
  std::string ios;
  std::string android;
  std::string brand_default;
  std::string phone_brand_default;

  bool Load(const rapidjson::Value &v);

  bool Load(const std::string &v);

  void describe(std::ostream &os) const;

RTTR_ENABLE();
};

inline std::ostream &operator<<(std::ostream &os, const AdDynamicCreative &ok) {
  ok.describe(os);
  return os;
}

struct AntiSpamConfig {
  absl::flat_hash_map<std::string, std::vector<std::string>> rule_map;
  std::string debug_str;

  bool Load(const std::string& json_str);

  void GetRuleKey(const std::string& device_id,
                  const std::string& device_ip,
                  const ::kuaishou::ad::DeviceInfo::OsType& device_os,
                  const std::string& device_idfa,
                  const std::string& device_imei,
                  std::vector<std::string>* key_vector);
};

struct AntiSpamBlacklist : public ks::ad_base::kconf::KconfInitProto<kconf::AdAntiSpamRequstBlackIds> {
  bool Init() override;
  bool IsInBlacklist(const std::string& flow_type, const std::string& blacklist_type,
                     const std::string& id) const;

 private:
  absl::flat_hash_map<std::string, absl::flat_hash_set<std::string>> kuaishou_blacklist_map_;
  absl::flat_hash_map<std::string, absl::flat_hash_set<std::string>> union_blacklist_map_;
};

struct AdDisplayInfoFilterConf {
  absl::flat_hash_set<int64_t> first_industry_set;
  absl::flat_hash_set<int64_t> second_industry_set;
  absl::flat_hash_set<int64_t> account_id_set;
  absl::flat_hash_set<std::string> product_name_set;
  bool Load(const std::string& json_str);
};

// 广告作品广告语格式化配置
struct AdDescriptionFormatConf {
  std::unordered_map<std::string, std::string> description_symbol_map;
  bool Load(const std::string& json_str);
};

struct KconfAdpageButtonControl {
  KconfAdpageButtonControl() = default;
  virtual ~KconfAdpageButtonControl() = default;

  std::unordered_set<int64_t> allow_comment;
  std::unordered_set<int64_t> allow_attention;
  std::unordered_set<int64_t> allow_open_profile;
  std::unordered_set<int64_t> allow_forword;
  std::unordered_set<int64_t> allow_like;

  bool Load(const rapidjson::Value &v);

  bool Load(const std::string &v);

  void describe(std::ostream &os) const;

RTTR_ENABLE();
};

inline std::ostream &operator<<(std::ostream &os, const KconfAdpageButtonControl &ok) {
  ok.describe(os);
  return os;
}

struct NegativeMenusInfoPb {
  ~NegativeMenusInfoPb();
  // todo(cuiyanliang):使用 unique_ptr 编译不过，不好处理，先用普通指针
  // 最好不用把 proto 里的东西作指针用，需要把 proto 拆的更细一些
  kuaishou::ad::AdDataV2_NegativeMenuInfo* negative_menu_info_pb = nullptr;
  bool Load(const std::string& value);
};

struct KconfAdPosPremium {
  std::unordered_set<int64_t> feed_flow_pos_premium;
  std::unordered_set<int64_t> thanos_flow_pos_premium;
  bool Load(const std::string &v);
};

struct AdCoverMediaTestInfo {
  std::string debug_str;
  std::shared_ptr<base::Json> json;
  std::unordered_map<int64, base::Json*> photo_map;

  bool Load(const std::string &json_str);

  bool Fill(int64 photo_id, int cover_type, ::Json::Value* body);

  bool Clear();

  void describe(std::ostream& os) const {
    os << debug_str;
  }
};

inline std::ostream& operator<<(std::ostream& os, const AdCoverMediaTestInfo& rs) {
  rs.describe(os);
  return os;
}

struct MerchantProfileAdConfig {
  MerchantProfileAdConfig() = default;
  virtual ~MerchantProfileAdConfig() = default;

  std::string android_main_app_ver;    // 安卓版主版 app 版本限制
  std::string android_nebula_app_ver;  // 安卓版极速版 app 版本限制
  std::string ios_main_app_ver;        // iOS 版主版 app 版本限制
  std::string ios_nebula_app_ver;      // iOS 版极速版 app 版本限制
  bool filter_profile_ad_for_old_app;  // 是否在老版本上过滤掉涨粉广告

  bool Load(const std::string& json_str);

  void describe(std::ostream& os) const;
  RTTR_ENABLE();

 private:
  std::string raw_json_str;
};

inline std::ostream& operator<<(std::ostream& os, const MerchantProfileAdConfig& profile_conf) {
  profile_conf.describe(os);
  return os;
}

struct GenerateCreativeRedisConfig {
  GenerateCreativeRedisConfig() = default;
  virtual ~GenerateCreativeRedisConfig() = default;

  std::string zk_path;
  int timeout_ms;
  std::unordered_map<std::string, std::string> key_prefix_map;
  bool Load(const std::string& value);
  void describe(std::ostream &os) const;
};

inline std::ostream &operator<<(std::ostream &os, const GenerateCreativeRedisConfig &ok) {
  ok.describe(os);
  return os;
}

struct KnewsEffectSplashImageUrlsConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::KnewsEffectSplashImageUrlsConfig> {
  KnewsEffectSplashImageUrlsConfig() = default;
  virtual ~KnewsEffectSplashImageUrlsConfig();
  bool Init() override;

  // 获取广告位的宽高以及时间选择最适合当前广告位 image_url
  std::string GetSuitableImageUrl(int64_t width, int64_t height) const;
  std::string GetImageUrlFromArrayByKey(std::string key, int64_t width, int64_t height) const;
};
struct MerchantLiveStyleConf {
  std::vector<int32_t> played_report_time;
  std::string no_action_bar_android_main_app_ver{"8.1.30"};    // 无 action_bar 安卓版主版 app 版本限制
  std::string no_action_bar_android_nebula_app_ver{"3.1.30"};  // 无 action_bar 安卓版极速版 app 版本限制
  std::string no_action_bar_ios_main_app_ver{"8.1.30"};        // 无 action_bar iOS 版主版 app 版本限制
  std::string no_action_bar_ios_nebula_app_ver{"3.1.40"};      // 无 action_bar iOS 版极速版 app 版本限制

  bool Load(const std::string& json_str);
};

struct TestWhiteListUniverse {
  absl::flat_hash_map<std::string, struct TestDataItem> white_list_data;
  bool Load(const std::string& json_str);
  bool IsInWhiteList(std::string test_device_id);
  // 不在白名单返回 false，在白名单返回白名单配置
  bool IsTestStyle(std::string test_device_id);
  bool GetPositionId(std::string test_device_id, int64_t* position_id);
  bool GetCreativeId(std::string test_device_id, std::vector<int64_t>* creative_ids);
  bool GetLiveId(std::string test_device_id, int64_t creative_id, int64_t* live_id);
};

struct FilterInvalidResponseConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::FilterInvalidResponseConfig> {
  FilterInvalidResponseConfig() = default;
  virtual ~FilterInvalidResponseConfig();
  bool Init() override;
  absl::flat_hash_map<std::string, bool> explore_config;
  absl::flat_hash_map<std::string, bool> galaxy_config;
  absl::flat_hash_map<std::string, bool> nearby_config;
  absl::flat_hash_map<std::string, bool> universe_config;
  absl::flat_hash_map<std::string, bool> follow_config;
};

struct MerchantPhotoStyleConf : public ks::ad_base::kconf::KconfInitProto<kconf::MerchantPhotoStyleConfPb> {
  bool Init() override;
  std::unordered_set<int32_t> played_report_time_set;
};

struct TargetOpData {
  int64_t ocpx_action_type;
  int64_t target_id;
  std::unordered_set<int64_t> account_id_set;
  std::unordered_set<int64_t> product_id_set;
  std::unordered_set<std::string> product_name_set;
  std::unordered_set<std::string> gender_set;
  std::unordered_set<int64_t> orient_id_set;
  std::unordered_set<int64_t> age_set;
  std::unordered_set<int64_t> region_id_set;
  std::unordered_set<std::string> platform_set;
  std::unordered_set<std::string> network_set;
  std::unordered_set<std::string> consumption_level_set;
  std::unordered_set<std::string> interest_set;
  std::unordered_set<std::string> brand_set;
  std::unordered_set<int64_t> flow_tag_set;
  std::unordered_set<int64_t> account_tail_id_set;
};

struct OnlinePriceDiscountConfig {
  std::unordered_map<int64_t, double> account_discount_map;
  std::unordered_map<int64_t, double> campaign_discount_map;
  std::unordered_map<int64_t, double> unit_discount_map;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct NewGameBaseData {
  std::unordered_set<int64> account_ids_set;
  std::unordered_set<int64> unit_ids_set;
  std::unordered_set<int64> creative_ids_set;
};

struct RewardedCoinDataBase {
  std::string prefix;
  std::string enable_tag;
  std::string global_cpm_tag;
  std::string user_value_tag;
  std::string ecpm_bias_tag;
  std::string coin_upper_tag;
  std::string coin_lower_tag;
  std::string ratio_upper_tag;
  std::string ratio_lower_tag;
  int64  base_coin_num;
  double user_low_value_percentile_thr;
  double user_low_value_ratio;
  double user_percentile_bias;
  double global_cpm;
  double new_user_value_ratio;
};

struct MultiRewardedCoinDataList
    : public ks::ad_base::kconf::KconfInitProto<kconf::MultiRewardedCoinDataConf> {
  std::unordered_map<int64, RewardedCoinDataBase> config_datas;
  std::string debug_str;
  bool Init() override;
};

struct DeepRewardedCoinDataBase {
  std::string enable_tag;
  std::string enable_treatment_tag;
  std::string user_value_tag;
  std::string ecpm_bias_tag;
  std::string coin_upper_tag;
  std::string coin_lower_tag;
  std::string ratio_upper_tag;
  std::string ratio_lower_tag;
  int64  base_coin_num;
  double low_value_ctcvr_k;
  double low_value_cvr_k;
  double low_value_uplift_k;
  double low_value_thr;
  double user_percentile_bias;
  double new_user_value_ratio;
};

struct RewardedCoinScalingBase {
  std::string coin_scaling_coef_tag;
  std::string coin_upper_tag;
  std::string coin_lower_tag;
  std::string deep_coin_scaling_coef_tag;
  std::string deep_coin_upper_tag;
  std::string deep_coin_lower_tag;
};

struct RewardedCoinScalingByAccountList
    : public ks::ad_base::kconf::KconfInitProto<kconf::RewardedCoinScalingByAccountConf> {
  std::unordered_map<int64, RewardedCoinScalingBase> config_datas;
  std::string debug_str;
  bool Init() override;
};

struct IncntvAdBinToLinerCoef {
  double lower;
  double upper;
  double weight;
  double bias;
};

struct IncntvAdPredictNext1ViewValue2Coef
    : public ks::ad_base::kconf::KconfInitProto<kconf::IncntvAdPredictNext1ViewValue2Coef> {
  std::unordered_map<std::string, std::vector<IncntvAdBinToLinerCoef>> subpageid_to_bin_wb_list;
  bool Init() override;
};

struct AdPackSpecialAdsConfigData : public ks::ad_base::kconf::KconfInitProto<kconf::AdPackSpecialAdsConfig> {
  bool Init() override;
  bool IsSpecialPageId(const int64& page_id) const;
  bool IsSpecialSubPageId(const int64& sub_page_id) const;
 private:
  std::unordered_set<int64> special_page_ids;
  std::unordered_set<int64> special_sub_page_ids;
};

struct AdFlowGradeConfigConf : public ks::ad_base::kconf::KconfInitProto<kconf::AdFlowGradeConfig> {
  bool Init() override;
  bool InSubPageIdBlackList(int64_t sub_page_id) const;

  std::unordered_set<int64_t> sub_page_id_set;
};

struct FrontRpcDegradeData : public ks::ad_base::kconf::KconfInitProto<kconf::FrontRpcDegrade> {
  FrontRpcDegradeData() = default;
  virtual ~FrontRpcDegradeData() {}
  bool Init() override;
  int32_t GetRpcDegradeRatio(const std::string& rpc_name) const;
};

struct AdServerMergeBlacklist : public ks::ad_base::kconf::KconfInitProto<kconf::AdServerMergeBlacklistPb> {
  bool Init() override;
  bool InPageIdSet(const int64_t num) const;
  bool InSubPageIdSet(const int64_t num) const;
  std::unordered_set<int64_t> page_id_set;
  std::unordered_set<int64_t> sub_page_id_set;
};

struct NoLimitVirtualGoldSceneConfigData
    : public ks::ad_base::kconf::KconfInitProto<kconf::NoLimitVirtualGoldSceneConfig> {
  bool Init() override;
  bool IsNoLimitVirtualGoldScene(const std::string& request_flow_type, int64_t page_id,
                                 int64_t sub_page_id) const;

  std::unordered_set<int64_t> page_id_set;
  std::unordered_set<int64_t> sub_page_id_set;
  std::unordered_set<std::string> flow_type_set;
};

struct HardTargetFilterConfigData : public ks::ad_base::kconf::KconfInitProto<kconf::HardTargetFilterConfig> {
  bool Init() override;
  bool EnableHardTargetFilter(int64_t sub_page_id) const;

  std::unordered_set<int64_t> black_sub_page_id_set;
};

struct AdPosPremiumConfigData : public ks::ad_base::kconf::KconfInitProto<kconf::AdPosPremiumConfig> {
  bool Init() override;

  std::unordered_set<int64_t> feed_flow_pos_premium;
  std::unordered_set<int64_t> thanos_flow_pos_premium;
};

struct CommonDyeingEraseConfigData :
    public ks::ad_base::kconf::KconfInitProto<kconf::CommonDyeingEraseConfig> {
  struct DyeingEraseMeta {
    std::unordered_set<int32> front_request_type_set;
    std::unordered_set<std::string> product_name_set;
    std::unordered_set<int64> account_id_set;
    std::unordered_set<int32> account_type_set;
  };

  bool Init() override;
  std::unordered_map<int64_t, DyeingEraseMeta> data;
};

class PecMultiCouponConfig : public ks::ad_base::kconf::KconfInitProto<kconf::PecMultiCouponConf> {
 public:
  bool Init() override;
  bool IsAuthorHit(int64 author_id) const;

 private:
  std::unordered_set<int64> author_id_set_;
};

struct GameAdForceDirectCallWhiteListConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::GameAdForceDirectCallWhiteList> {
 public:
  bool Init() override;
  bool InWhiteList(const std::string& product_name) const;
 private:
  std::unordered_set<int64> product_names;
};

struct SplashPrefetchRecordWhiteList
    : public ks::ad_base::kconf::KconfInitProto<kconf::SplashPrefetchRecordWhiteListPb> {
 public:
  bool Init() override;
  bool IsInWhiteList(int64_t account_id, int64_t unit_id, int64_t creative_id) const;
 private:
  std::unordered_set<int64_t> account_ids;
  std::unordered_set<int64_t> unit_ids;
  std::unordered_set<int64_t> creative_ids;
};

struct DegradeLevelConfigData
    : public ks::ad_base::kconf::KconfInitProto<kconf::DegradeLevelConfig> {
 public:
  bool Init() override;
  std::pair<DegradeLevel, int32_t> GetDegradeLevelAndRatio(
    AdEnum_AdRequestFlowType request_flow_type,
    int64_t page_id,
    int64_t sub_page_id) const;

 private:
  int32_t GetRatioByDegradeLevel(DegradeLevel level) const;

 private:
  std::unordered_map<int64_t, DegradeLevel> sub_page_id_to_level_;
  std::unordered_map<int64_t, DegradeLevel> page_id_to_level_;
  std::unordered_map<std::string, DegradeLevel> request_flow_type_to_level_;
};

struct SiXinServicePageComponent
    : public ks::ad_base::kconf::KconfInitProto<kconf::SiXinServicePageComponentPb> {
 public:
  bool Init() override;
  bool IsSiXinServicePage(int32_t type, int32_t sub_type) const;
 private:
  absl::flat_hash_map<int32_t, absl::flat_hash_set<int32_t>> type_2_sub_types_;
};

struct ResourceWhiteBoxFlowTagConfigData
      : public ks::ad_base::kconf::KconfInitProto<kconf::ResourceWhiteBoxFlowTagConfig> {
 public:
  bool Init() override;
  std::string GetFlowTag(AdEnum_AdRequestFlowType request_flow_type, int64_t sub_page_id) const;

 private:
  std::unordered_map<int64_t, std::string> sub_page_id_to_flow_tag_;
  std::unordered_map<AdEnum_AdRequestFlowType, std::string> request_flow_type_to_flow_tag_;
};

struct MerchantConfig {
  MerchantConfig() = default;
  virtual ~MerchantConfig() = default;

  int32_t merchant_ratio;   // 总体放量比例，按照 user_id 尾两位号控制
  int32_t main_merchant_ratio;  // 主版双 feed 模式总体放量比例，按照 user_id 尾两位号控制
  int32_t nebula_merchant_ratio;  // 极速版总体放量比例，按照 user_id 尾两位号控制
  int32_t thanos_merchant_ratio;  // 主版本滑滑模式总体放量比例，按照 user_id 尾两位号控制
  int32_t max_retrieval_record_number;   // retrieval record item 信息的最大记录数
  int32_t max_ranking_prepare_left_number;  // 最大进入精排数量，超过该限制的进行阶段
  std::string android_main_app_ver;    // 安卓版主版 app 版本限制
  std::string android_nebula_app_ver;   // 安卓版极速版 app 版本限制
  std::string ios_main_app_ver;   // iOS 版主版 app 版本限制
  std::string ios_nebula_app_ver;   // iOS 版极速版 app 版本限制
  std::string android_main_nearby_app_ver;       // 安卓版主版发现页 app 版本限制
  std::string android_nebula_nearby_app_ver;     // 安卓版极速版发现页 app 版本限制
  std::string ios_main_nearby_app_ver;           // iOS 版主版发现页 app 版本限制
  std::string ios_nebula_nearby_app_ver;         // iOS 版极速版发现页 app 版本限制
  std::string android_feature_selected_app_ver;  // 安卓版底导精选页 app 版本限制
  std::string ios_feature_selected_app_ver;      // iOS 版底导精选页 app 版本限制
  std::string android_inner_explore_app_ver;     // 安卓版发现页内流 app 版本限制
  std::string ios_inner_explore_app_ver;         // iOS 版发现页内流 app 版本限制
  // 快享版本控制
  std::string android_main_detail_app_ver;       // 安卓版主版快享 app 版本限制
  std::string android_nebula_detail_app_ver;     // 安卓版极速版快享 app 版本限制
  std::string ios_main_detail_app_ver;           // iOS 版主版快享 app 版本限制
  std::string ios_nebula_detail_app_ver;         // iOS 版极速版快享 app 版本限制
  // 开屏版本控制
  std::string android_main_splash_app_ver;       // 安卓版主版开屏 app 版本限制
  std::string android_nebula_splash_app_ver;     // 安卓版极速版开屏 app 版本限制
  std::string ios_main_splash_app_ver;           // iOS 版主版开屏 app 版本限制
  std::string ios_nebula_splash_app_ver;         // iOS 版极速版开屏 app 版本限制

  bool enable_ab_test;  // 是否开启 AB 实验
  bool enable_universe_request;   // 是否允许联盟流量做电商召回
  bool enable_detail_request;  // 是否允许快享流量召回
  bool enable_splash_request;  // 是否允许开屏流量召回
  bool enable_merchant_trace_partial;  // 是否允许 merchant 召回这一路 trace partial 信息
  bool enable_merchant;  // 是否允许请求 merchant target
  bool enable_unlogin_request;  // 是否允许未登录用户请求召回电商广告
  bool merge_retrieval_record;  // 是否合并记录 trace retrieval 信息
  bool merge_retrieval_statistics_to_session;  // 是否两路召回数据进行加和
  bool use_fake_photo_id_for_live;   // 是否为直投直播的订单素材使用虚假的 photo id
  bool fill_default_display_info_for_live;  // 是否需要为直播订单设置默认的 display_info
  bool enable_rank_test_value;   // 是否使用测试用的 rank 测试值
  bool enable_main_nearby;   // 是否允许主站同城的投放
  bool enable_nebula_nearby;   // 是否允许极速版同城的投放
  bool use_independ_amd_request;  // amd target 请求参数是否进行 copyFrom 稳定后下掉
  bool use_target_live_res;  // 使用 amd target 返回 account support 信息
  bool enable_radom_explore;  // 是否允许 amd target 广告走随机广告逻辑

  bool enable_knews_request_merchant;  // 是否允许快看点走 amd target
  bool enable_kswechatapp_request_merchant;  // 是否允许微信小程序走 amd target
  bool enable_aggregate_request_merchant;  // 是否允许二期聚合页走 amd target

  bool Load(const std::string &json_str);

  void describe(std::ostream& os) const;
  RTTR_ENABLE();

 private:
  std::string raw_json_str;
};

struct ExploreLiveConfig {
  ExploreLiveConfig() = default;
  virtual ~ExploreLiveConfig() = default;

  bool enable_explore_live_fans_top;    // 是否允许发现页投放直播粉条
  bool enable_main_ab_test;             // 是否允许主版 AB 实验
  bool enable_thanos_ab_test;           // 是否允许滑滑版 AB 实验
  bool enable_nebula_ab_test;           // 是否允许极速版 AB 实验
  int32_t explore_live_fans_top_ratio;  // 控制总的发现页直播粉条的占比，用户尾号后两位
  int32_t main_explore_live_ratio;  // 控制主版双列发现页直播粉条的占比，用户尾号后两位
  int32_t nebula_explore_live_ratio;  // 控制极速版发现页直播粉条的占比，用户尾号后两位
  int32_t thanos_explore_live_ratio;  // 控制滑滑版发现页直播粉条的占比，用户尾号后两位
  int32_t explore_live_main_vv_step;  // 控制主版发现页直播粉条的 vv 步长
  int32_t explore_live_thanos_vv_step;  // 控制滑滑版发现页直播粉条的 vv 步长
  int32_t explore_live_nebula_vv_step;  // 控制极速版版发现页直播粉条的 vv 步长
  int32_t explore_live_main_time_sec_step;  // 控制主版发现页直播粉条的出现时长步长，单位：秒
  int32_t explore_live_thanos_time_sec_step;  // 控制滑滑发现页直播粉条的出现时长步长，单位：秒
  int32_t explore_live_nebula_time_sec_step;  // 控制极速版发现页直播粉条的出现时长步长，单位：秒
  int32_t explore_live_day_limit;             // 控制发现页直播粉条每天每用户最大展示量
  int32_t explore_live_retrieval_max_count;  // 控制发现页最大召回的直播数量
  int32_t predict_max_ps_live;               // 控制请求 ps 的最大量
  std::string android_main_app_ver;          // 安卓主版最低版本号
  std::string android_nebula_app_ver;        // 安卓极速版最低版本号
  std::string ios_main_app_ver;              // iOS 主版最低版本号
  std::string ios_nebula_app_ver;            // iOS 极速版最低版本号
  bool Load(const std::string& json_str);

  void describe(std::ostream& os) const;

 private:
  std::string raw_json_str;
};

class PageSize2MaxReturn : public ks::ad_base::kconf::KconfInitProto<MapStringToInt64> {
 public:
  bool Init() override {
    return true;
  };
  int64_t GetMaxReturn(int64_t page_size) const;
};

class DebugSpecialFilterReasons
    : public ks::ad_base::kconf::KconfInitProto<WhiteBoxDebugSpecialFilterReasonsMsg> {
 private:
  std::unordered_set<int64_t> reasons;

 public:
  bool Init() override;
  int64_t IsSpecialReason(const int64_t& reason) const;
};

struct DisableFactorConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::DisableFactorConfigPb> {
  bool Init() override;
  bool InUdfList(const std::string &factor_name) const {
    return udf.find(factor_name) != udf.end();
  }
  bool InEcpcList(const std::string &factor_name) const {
    return ecpc.find(factor_name) != ecpc.end();
  }
  bool InFilterList(const std::string &factor_name) const {
    return filter.find(factor_name) != filter.end();
  }
 private:
  std::unordered_set<std::string> udf;
  std::unordered_set<std::string> ecpc;
  std::unordered_set<std::string> filter;
};

class KessGrpcClients {
 public:
  bool Load(const std::string& json_str);
  int64_t GetTimeoutMs();

 private:
  int64_t timeout_ms_ = -1;
};

struct SearchInspireParamsDataBase {
  std::string enable_has_more;
  std::string enable_change_one;
  std::string has_more_prob;
  std::string change_one_prob;
  std::string photo_bill_time;
  std::string live_bonus_time;
};
struct SearchInspireParamsConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireParamsConf> {
  std::unordered_map<int64, SearchInspireParamsDataBase> config_datas;
  bool Init() override;
};

}  // namespace front_server
}  // namespace ks
