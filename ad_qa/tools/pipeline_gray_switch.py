# -*- coding: utf-8 -*-
import argparse
try:
  import commands  # python2
except:
  import subprocess as commands  # python3
import json
import logging
import os
import socket
try:
  import urllib2  # python2
except:
  import urllib.request as urllib2  # python3
import traceback
import os
import sys
reload(sys)
sys.setdefaultencoding('utf8')


###########################################
#dbinfo=dict(host='bjpg-d383.yz02', user='test_rw', passwd='54rltyi5BCdcm06wu22A0brvvzU5uDgB', db='gifshow', port=15095, charset='utf8', connect_timeout=10)

module_dir = {
    "adrank": "ad_rank ad_base engine_base",
    "adrank_search": "ad_rank_search ad_base engine_base",
    "adfront": "front_server ad_base engine_base" ,
    "adfront_search": "front_server_search ad_base engine_base" ,
    "adfront_universe": "front_server_universe ad_base engine_base" ,
    "adtarget": "ad_target ad_base engine_base ad_server/adindex ad_server/util/data_push ad_server/util/location",
    "adserver": "ad_server ad_base engine_base",
    "adserver_splash": "ad_server_splash ad_base engine_base",
    "adserver_universe": "ad_server_universe ad_base engine_base",
    "adrank_universe": "ad_rank_universe",
    "adtarget_universe": "ad_target_universe",
    "adtarget_search": "ad_target_search",
    "adtarget_splash": "ad_target_splash",
    "adtarget_archimedes": "ad_target_archimedes",
    "bidwordsearch_default":"bidword_search"
        }

module_dir_oversea = {
    "adrank": "ad_i18n/ad_rank ad_i18n/engine_base ad_i18n/ad_base",
    "adfront": "ad_i18n/front_server ad_i18n/engine_base ad_i18n/ad_base" ,
    "adtarget": "ad_i18n/ad_target ad_i18n/ad_index ad_i18n/engine_base"
        }

prod_name = {
    "adrank": "ad_rank_server",
    "adrank_search": "ad_rank_server_search",
    "adfront": "ad_front_server" ,
    "adfront_search": "ad_front_server_search",
    "adfront_universe": "ad_front_server_universe" ,
    "adtarget": "ad_target_server",
    "adserver": "ad_server",
    "adrank_universe": "ad_rank_server_universe",
    "adtarget_universe": "ad_target_server_universe",
    "adtarget_search": "ad_target_search_server",
    "adtarget_splash": "ad_target_server_splash",
    "adtarget_archimedes": "ad_target_archimedes",
    "bidwordsearch_default":"bidword_search"
        }
prod_name_oversea = {
    "adrank": "ad_rank_server_i18n",
    "adfront": "front_server_i18n" ,
    "adtarget": "ad_target_i18n"
        }
service_name_oversea = {
    "adrank": "ad_rank_server_i18n",
    "adfront": "ad_front_server_i18n" ,
    "adtarget": "ad_target_server_i18n"
        }
FLOW_TYPE = "default"
AUTOPUB_VERSION = "1.0.5026-b370853_c370853_master_c2667315_all"
COMMITID = "1194920"
CUR_PATH = ""
MODULE="adfront"
JOB_TYPE="0"
INNER=""
OVERSEA=""

class AllFlowTypeGray:

  def send_message(self, content):
      owner = ["liujie12"]
      url = "https://ad-env.corp.kuaishou.com/kim/send"
      header = "Content-Type: application/json"
      report={"usernames": owner, "msgType": "markdown", "markdown": {"content": content}}
      command = "curl -l -H '%s' -X POST -d '%s' '%s'" % (header, json.dumps(report), url) 
      ret, res = self.execute(command)
      try :
          if ret == 0:
              return True
          else:
              logging.error("Send message Bad response --> resp: %s" % (json.dumps(res)))
              print ("Send message Bad response --> resp: %s" % (json.dumps(res)))
              return False
      except Exception as e:
          msg = traceback.format_exc()
          logging.warning("Send message Exception: %s\n%s" % (str(e), msg))
          print ("Send message Exception: %s\n%s" % (str(e), msg))
          return False

  def delete_last_switch(self, module, jobid):
      url = "https://ad-env.corp.kuaishou.com/diffreport/removeRdSwitch?type=GRAY&jobid="
      
      url = url + module + "_" + jobid
      ret, res = self.execute('curl -X POST \"' + url + '\" -H "accept: */*" -H "Content-Type: application/json" -s')
      resp = json.loads(res)
      try :
          if ret == 0 and str(resp['status']) == "0":
              logging.info("delete resp message: %s\n" % (json.dumps(resp)))
              return True
          else:
              logging.error("Delete api Bad response for url: %s --> resp: %s" % (url, json.dumps(resp)))
              self.send_message("Delete api Bad response for url: %s --> resp: %s" % (url, json.dumps(resp)))
              return False
      except Exception as e:
          msg = traceback.format_exc()
          logging.warning("Delete Exception: %s\n%s" % (str(e), msg))
          self.send_message("Delete Exception: %s\n%s" % (str(e), msg))
          return False

  def execute(self, cmd):
      logging.debug("执行 [%s]" % cmd)
      print cmd
      code, output = commands.getstatusoutput(cmd)
      return code, output
  
  def get_owner_list(self, git_log):
      owner_list = []
      if gitLog:
          gitLog_list = gitLog.split('%%')
          for one in gitLog_list[1:]:
              owner_list.append(one.split(" | ")[1].replace(" ", ""))
          return owner_list
      return owner_list

  def get_commitid_list(self, git_log):
      commitid_list = []
      if git_log:
          gitLog_list = git_log.split('%%')
          for one in gitLog_list[1:]:
              if "Reviewed at Kdev: " in one:
                  commitid_list.append(one.split("Reviewed at Kdev:")[1].split("(")[0].replace(" ", ""))
          return commitid_list
      return commitid_list

  def get_prod_autopub_gitlog(self, modulename=""):
    url = "https://ad-checker.test.gifshow.com/api/gray_last_version?server=" + modulename
    if OVERSEA:
        url = "https://ad-checker.test.gifshow.com/api/gray_last_version?i18n=true&server=" + modulename
    ret, res = self.execute('curl -s \"' + url + '\" ')
    logging.info("res= %s " % res)
    text=res.decode("utf-8-sig").encode("utf-8")
    resp = json.loads(text)
    logging.info("resp= %s ,status= %s " % (resp, resp['status']))
    autopub_commitid = ""
    autopub_flow = ""
    global INNER
    version_module_name=modulename
    #特殊处理begin
    if "_" in modulename:
        version_module_name = modulename.split("_")[0]
    #特殊处理end
    print res
    try :
        if ret == 0 and str(resp['status']) == "0":
             logging.info("resp message: %s\n" % (json.dumps(resp)))
             if INNER:
                 version_module_name=modulename+"_"+INNER
             if "data" in resp.keys() and version_module_name in resp["data"].keys():
                 if "flow" in str(resp["data"][version_module_name]):
                    logging.info("autopb0 = %s " % json.dumps(resp["data"][version_module_name]))
                    pbjson = json.dumps(resp["data"][version_module_name])
                    autopb = json.loads(pbjson)
                    logging.info("autopb = %s " % autopb["flow"])
                    global FLOW_TYPE
                    FLOW_TYPE = str(autopb["flow"]).replace("u'","").replace("'","").replace("[","\"").replace("]","\"")
                    global AUTOPUB_VERSION 
                    AUTOPUB_VERSION = autopb["autopub"]
                    autopub_commitid = AUTOPUB_VERSION.split("_")[3]
                    logging.info("get_prod_autopub_gitlog for url: %s, module: %s --> autopub_version: %s ; autopub_flow: %s" %
                             (modulename, url, AUTOPUB_VERSION,FLOW_TYPE))
        else:
           logging.error("%s , get_prod_autopub_gitlog for url: %s --> resp: %s ; " % 
                  (str(modulename), url, json.dumps(resp)))
           self.send_message("%s  ,get_prod_autopub_gitlog Bad response for url: %s --> resp: %s ;" % 
                  (str(modulename), url, json.dumps(resp)))
        # 本地调试，临时初始化
        #prod_version = "aa2d6aea"
        ret, cur_path = self.execute("pwd")
        global CUR_PATH
        CUR_PATH = cur_path
        prod_version = os.getenv('WORKFLOW_SERVICE_LATEST_VERSION_BEGIN_COMMIT_GITISH')
        git_log = "cd teams/ad && git log " + autopub_commitid + "..." + prod_version + " --pretty=format:'----@@----%ncommitid:%h%nauthor:%al%ntime:%cd%nmsg:%B----modify----' --name-status --date=iso-local  --no-merges -- " + str(module_dir[modulename]) + " > " + cur_path+"/teams/ad/ad_qa/tools/git_log.txt"
        if OVERSEA:
            git_log = "cd teams/ad && git log " + autopub_commitid + "..." + prod_version + " --pretty=format:'----@@----%ncommitid:%h%nauthor:%al%ntime:%cd%nmsg:%B----modify----' --name-status --date=iso-local  --no-merges -- " + str(module_dir_oversea[modulename]) + " > " + cur_path+"/teams/ad/ad_qa/tools/git_log.txt"
        retg, resg = self.execute(git_log)
        retg1, resg1 = self.execute("pwd")
        logging.info("git_log = %s" % resg1)
        if ret == 0:
            commitid = "cat " + cur_path+"/teams/ad/ad_qa/tools/git_log.txt | grep \"Reviewed at Kdev:\" |cut -d ':' -f2 | cut -d '(' -f1"
            ret,res = self.execute("pwd")
            logging.info("commitid = %s " % commitid)
            ret,res = self.execute(commitid)
            logging.info("commitid = %s " % res.replace("\n", ","))
            global COMMITID
            COMMITID = str(res.replace("\n", ",").replace(" ",""))
            return True
        return False
    except Exception as e:
      msg = traceback.format_exc()
      logging.warning("%s  ,get_prod_autopub_gitlog Exception: %s\n%s" % (str(modulename), str(e), msg))
      self.send_message("%s  ,get_prod_autopub_gitlog Exception: %s\n%s" % (str(modulename), str(e), msg))
      return False

  def run_gray(self,module):
      global FLOW_TYPE
      logging.info("FLOW_TYPE = %s " % str(FLOW_TYPE))
      flow_type = FLOW_TYPE
      flow = flow_type.replace("\"","").replace(",","")
      logging.info("flow = %s " % flow)
      if FLOW_TYPE:
          # 脚本路径需要修改
          global CUR_PATH
          command = "python "+ CUR_PATH+"/teams/ad/ad_qa/tools/upload_product.py -p %s -f %s" % (module, flow ) 
          logging.info("run_gray command = %s " % command)
          ret, res = self.execute(command)
          if ret == 0 and "ERROR" in res:
              logging.info("ret = %s,Fres = %s " % (ret,res))
              return False
      logging.info("ret = %s,Tres = %s " % (ret,res))
      return True  
      #sh -x $gray_tool "${product_name}" "${flow_type}" "${prod_name}" "${gray_version}" "${cr_runner}"
      #sh -x ./SRC/ad_qa/auto_test/gray_test/gray_run.sh "adfrontserver" "default" "adfront" "${gray_version}" "${cr_runner}"
  
  def run_gray_autopub(self,module):
      global FLOW_TYPE
      global AUTOPUB_VERSION
      global COMMITID
      global MODULE
      logging.info("FLOW_TYPE = %s " % str(FLOW_TYPE))
      logging.info("AUTOPUB_VERSION = %s " % AUTOPUB_VERSION)
      flow_type = FLOW_TYPE
      flow = flow_type.replace(" ","")
      logging.info("flow = %s " % flow)
      rdSwitch=""
      #特殊处理begin
      module_name = module
      if "_" in module:
        module_name = module.split("_")[0]
      #特殊处理end
      queryRd = "curl -s \"https://diffenv.test.gifshow.com/getrdswitch?module="+module_name+"&crid="+COMMITID+"\""
      retRd,resRd = self.execute(queryRd)
      logging.info("resRd= %s " % resRd)
      # 开关组合
      text=resRd.decode("utf-8-sig").encode("utf-8")
      resp = json.loads(text)
      switch = ""
      if retRd == 0 and resp['code'] == 0:
          logging.info("resp= %s ,rdswitchlist= %s " % (resp, resp['data']))
          if str(resp['data']):
              rdSwitch = '\{\'' + str(resp['data']) + '\'\}'
              logging.info("rdSwitch= %s " % rdSwitch)
              switch = rdSwitch.replace("=","':'").replace(",","','").replace("'","\'")
              # grayflag={'ks::AbtestBiz::AD_DSP.enable_universe_live_ks_user_check':'true','ad.adFrontDiffSwitches.enableUniverseInnerLoopAdmitFront':true}         
      if FLOW_TYPE:
          # 脚本路径需要修改
          global CUR_PATH
          global JOB_TYPE
          global INNER
          if JOB_TYPE == 2:
              switch = ""
          #module adtarget,adrank需要去除下划线后面
          command = "sh -x " + CUR_PATH+"/teams/ad/ad_qa/auto_test/gray_test/gray_run.sh %s %s %s %s jenkins \"%s\" %s" % (module_name,flow,prod_name[module],AUTOPUB_VERSION,switch,JOB_TYPE ) 
          if OVERSEA:
              command = "sh -x " + CUR_PATH+"/teams/ad/ad_qa/auto_test/gray_test/gray_run.sh %s %s %s %s jenkins \"%s\" %s" % (module,flow,prod_name_oversea[module],AUTOPUB_VERSION,switch,JOB_TYPE ) 
          if INNER:
              command = "sh -x " + CUR_PATH+"/teams/ad/ad_qa/auto_test/gray_test/gray_run.sh %s %s %s_fanstop %s jenkins \"%s\" %s" % (module,flow,prod_name[module],AUTOPUB_VERSION,switch,JOB_TYPE ) 
          logging.info("run_gray command = %s " % command)
          ret, res = self.execute(command)
          if ret == 0:
              return True
      return False  
    
  
  def open_gray_switch(self, module=""):
    global MODULE
    global AUTOPUB_VERSION
    MODULE=module

    ret = self.get_prod_autopub_gitlog(module)
    #     # 本地调试，环境变量获取先初始化化
    kci_build_id = os.getenv('KCI_BUILD_ID')
    #kci_build_id = "6892316"
    try :
      if ret :
        gray_res = self.run_gray_autopub(module)
        logging.info("run_gray end ...")
        if gray_res:
            logging.info("success,gray_res = %s " % gray_res)
        else:
            logging.info("fail,gray_res = %s " % gray_res)

            # 增加海外例行任务失败,打标逻辑
            if OVERSEA:
                url_i18n = "http://ad-checker.test.gifshow.com/api/autopub_mark"
                header_fri = "accept: */*"
                header_sec = "Content-Type: application/json"
                command = "curl -X POST '%s' -H '%s' -H '%s' -d '{ \"user\": \"xuxicheng\", \"production\": \"ad\", \"service_name\": \"'%s'\", \"version\": \"'%s'\", \"mark_status\": \"1\",\"mark_text\": \"mission Fail\"}' " % (url_i18n, header_fri, header_sec, service_name_oversea[module], AUTOPUB_VERSION)
                ret_i18n, res_i18n = self.execute(command)

      else:
          logging.error("%s , %s , open_gray_switch Bad response for url: %s --> resp: %s  " % 
                  (str(module), str(kci_build_id), url, json.dumps(resp)))
          self.send_message("%s , %s ,Add autopub config Bad response for url: %s --> resp: %s " % 
                  (str(module), str(kci_build_id), url, json.dumps(resp)))
          return False
    except Exception as e:
      msg = traceback.format_exc()
      logging.warning("%s , %s ,open_gray_switchg Exception: %s\n%s" % (str(module), str(kci_build_id), str(e), msg))
      self.send_message("%s , %s ,open_gray_switchg Exception: %s\n%s" % (str(module), str(kci_build_id), str(e), msg))
      return False

###########################################
if __name__ == '__main__':
  logging.basicConfig(level=logging.INFO,
          format="[%(asctime)s] <%(levelname)s> %(message)s")
  parser = argparse.ArgumentParser(usage="Command: -h/--help for help")
  parser.add_argument("-m", "--module", help="module", default="front")
  parser.add_argument("-j", "--jobtype", help="jobtype", type=int, default="0")
  parser.add_argument("-i", "--inner", help="inner", default="")
  parser.add_argument("-o", "--oversea", help="oversea", default="")
  args = parser.parse_args()
  JOB_TYPE=args.jobtype
  INNER=args.inner
  OVERSEA=args.oversea
  deployer = AllFlowTypeGray()
  deployer.open_gray_switch(args.module)
#   deployer.run_gray_autopub(args.module)
  
