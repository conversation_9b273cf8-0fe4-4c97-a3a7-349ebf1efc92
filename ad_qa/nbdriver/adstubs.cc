#include "base/common/base.h"
#include "base/common/logging.h"
#include "framework/application.h"
#include <google/protobuf/message.h>
#include "base/file/file_util.h"
#include "google/protobuf/descriptor.h"
#include "google/protobuf/reflection.h"
#include "google/protobuf/util/json_util.h"
#include "infra/codis_client/inner/redis_command.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "infra/kess_grpc-v1100/src/main/rpc/kess/rpc/grpc/client2.h"
#include "kenv/service_meta.h"
#include "kenv/trace_context.h"
#include "ktrace/core/span_context.h"
#include "ktrace/core/tracer_manager.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/utility/signal.h"
#include "serving_base/utility/system_util.h"
#include "serving_base/util/array.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_forward_index.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_grpc_service.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_grpc_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_pack_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_user_profile_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adsocial/ad_social_follow_reco.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adx/track/adx_track.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/dsp/ad_dsp_base_rpc_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/fanstop/fans_top_whitelist_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/brandv2/info/ad_brand_info_query_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/brandv2/account/ad_brand_account_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ssp/ad_ssp.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/corerpc/user/user_cache_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/corerpc/user/user_count.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/fanstop/fans_top_service.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/fanstop/fans_top_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/merchant/commodity/item/apply/merchant_commodity_apply_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/merchant/commodity/price/merchant_commodity_item_price_apply_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/merchant/commodity/sales/merchant_commodity_sales_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/merchant/marketing/resource/merchant_marketing_resource_query_service.kess.grpc.pb.h"
#include "teams/ad/ad_i18n/ad_proto/i18n/forward_index_service/forward_index_service.kess.grpc.pb.h"
#include "teams/ad/ad_i18n/forward_index/service/forward_index_service.h"
#include "teams/ad/ad_i18n/ad_proto/i18n/ad_target/target_service.kess.grpc.pb.h"
#include "teams/ad/ad_i18n/ad_proto/i18n/predict_service/predict_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/counter/ad_counter_service.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/counter/ad_counter_service.kess.grpc.pb.h"
#include "teams/ad/ad_qa/nbdriver/utils/defines.h"
#include "teams/ad/ad_qa/nbdriver/utils/utils.h"
#include "teams/ad/ad_qa/nbdriver/web_service.h"
#include "third_party/abseil/absl/strings/str_join.h"
#include "third_party/abseil/absl/strings/str_split.h"
#include <functional>
#include <iostream>
#include <string>
#include "teams/ad/ad_proto/kuaishou/ad/ad_style_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_style_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_ueq_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_ueq_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adx/cxr/adx_cxr.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adx/cxr/adx_cxr.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_reco_service.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_reco_service.kess.grpc.pb.h"
#include "teams/ad/picasso/proto/picasso_service.kess.grpc.pb.h"
#include "teams/ad/picasso/sdk/proto/gateway.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/rta/rta_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/rta/rta_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/bid_service/ad_bid_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/bid_service/ad_bid_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/embedding_retr_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/embedding_retr_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/riskControl/ad_risk_control_rpc_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/riskControl/ad_risk_control_rpc_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_style_server.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_style_server.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adx/adx_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adx/adx_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/buyer/ad_buyer_server.kess.grpc.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.kess.grpc.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_retr_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_retr_service.pb.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "base/hash_function/city.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "teams/ad/ad_base/src/hash/absl_hash.h"
#include <kess/proto/rpc/common.pb.h>
#include "teams/ad/ad_proto/kuaishou/ad/ad_data_arbiter.kess.grpc.pb.h"
#include "teams/ad/ad_p2p_arbiter/service/ad_p2p_arbiter_service.h"
#include "teams/ad/ad_proto/kuaishou/ad/search_ads/search_relevance_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/search_ads/search_relevance_service.pb.h"

#include "ks/reco_proto/common_reco/leaf/proto/common_reco.kess.grpc.pb.h"
#include "ks/reco_proto/common_reco/leaf/proto/common_reco.generic_rpc.pb.h"

#include "kess/rpc/brpc/brpc_server_builder.h"
#include "kess/rpc/brpc/server.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_grpc_service.generic_rpc.pb.h"

#include "teams/ad/ad_proto/kuaishou/ad/ad_model_calibrate.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_model_calibrate.pb.h"
#include "teams/ad/ad_feature_index/proto/msg.kess.grpc.pb.h"
#include "teams/ad/ad_feature_index/proto/msg.pb.h"

#include "teams/ad/ad_proto/maven/kwaishop_selection_user_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/ad/platform/ad_ecom_hosting.pb.h"
#include "teams/ad/ad_proto/maven/ad/platform/ad_ecom_hosting.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/ad/platform/ad_core_live.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/ad/platform/ad_esp_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/corerpc/photo/photo_author_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_dsi_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/fans_top_gateway_order_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/corerpc/photo/photo_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/corerpc/photo/photo_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/fanstop/fans_top_photo_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/fanstop/fans_top_photo_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/maven/kwaishop/flow/live/kwaishop_flow_live_car_item_service.kess.grpc.pb.h"
//#include "teams/ad/ad_proto/maven/ad/dmp/ad_estimate_service.kess.grpc.pb.h"
//#include "teams/ad/ad_proto/maven/ad/dmp/ad_dmp_service.kess.grpc.pb.h"
//#include "teams/ad/ad_qa/qa_proto/ad_dmp_service_proxy.kess.grpc.pb.h"

#include "teams/ad/ad_proto/kuaishou/ad/bidword_retr.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/search_info.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_audition_service.generic_rpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_audition_service.kess.grpc.pb.h"
#include "teams/ad/grid/proto/msg.kess.grpc.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"

using namespace kuaishou::ad;
using namespace kuaishou::ad::dsp;
using namespace kuaishou::ad::ssp;
using namespace kuaishou::fanstop;
using namespace kuaishou::user::cache;
using namespace kuaishou::usercount;
using namespace kuaishou::ad::social;
using namespace kuaishou::ad::adx::track::inner;
using namespace kuaishou::ad::forward_index;
using namespace kuaishou::plateco::merchant::commodity::apply;
using namespace kuaishou::plateco::merchant::commodity;
using namespace kuaishou::plateco::merchant::marketing::mr;
//using kuaishou::ad::brandv2::info::kess::AdBrandQueryRpcService;
using namespace ::mix::kuaishou::ad;
//using namespace kuaishou::ad::rta;
using ::kuaishou::ad::rta::kess::RtaService;
//namespace rpc = ::ks::kess::rpc;
using ::ks::ad_feature_index::proto::GetBatchSchemaFreeDataReq;
using ::ks::ad_feature_index::proto::GetBatchSchemaFreeDataResp;
using kuaishou::kwaishop::selection::user::service::QueryBuyerTagsRequest;
using kuaishou::kwaishop::selection::user::service::QueryBuyerTagsResponse;
using namespace kuaishou::ad::platform;
using namespace kuaishou::ad::fanstop;
using kuaishou::negative::kess::PhotoServiceRpc;
using kuaishou::negative::GetByPhotoIdsRequest;
using kuaishou::negative::PhotoMapResponse;
using kuaishou::kwaishop::flow::live::kess::KwaishopFlowLiveCarItemService;
using kuaishou::kwaishop::flow::live::KwaishopFlowLiveCarItemListRequest;
using kuaishou::kwaishop::flow::live::KwaishopFlowLiveCarItemListResponse;
using kuaishou::ad::adx::kess::AdxService;
using kuaishou::ad::buyer::kess::BrandBuyerServer;
//using ks::ad_target::multi_retr::RetrievalTag;

DEFINE_int32(thread_num, 5, "");
DEFINE_int32(web_thread_num, 5, "");
//DEFINE_int32(prerank_thread_num, 5, "");
DEFINE_bool(is_redis, false, "");
DEFINE_string(lane_id, "ad_diff", "lane id: ad_diff is front, ad_diff_rank is ad_rank, ad_diff_predict is predict");
DEFINE_int32(web_server_port, 10092, "the port for web service, front default is 10092, rank default is 10093");
DEFINE_bool(get_response_from_file, false, "get_response_from_file");
DEFINE_string(file_name, "test.json", "test.json");
DEFINE_string(kess_type, "testing", "haiwai kess-sg");
DEFINE_int32(i18nforward_port, 21003, "haiwai forward_index port setting");
DEFINE_int32(i18ntarget_port, 21004, "haiwai target port setting");
DEFINE_int32(i18npredict_port, 21005, "haiwai predict port setting");
DEFINE_bool(is_support_adrank_stub, false, "support ad_rank stub");
DEFINE_bool(is_support_adrank_stub_for_q, false, "support ad_rank stub for q");
DEFINE_bool(is_support_predict_stub, false, "support predict stub");
DEFINE_bool(is_support_adtarget_stub, false, "support adtarget stub");
DEFINE_bool(is_support_adserver_stub, false, "support adserver stub");
DEFINE_bool(is_support_adfront_stub, false, "support adfront stub");
DEFINE_bool(is_support_i18n_stub, false, "support i18n stub");
DEFINE_bool(is_support_addragon_stub, false, "support addragon stub");
DEFINE_bool(is_support_adforward_index_stub, false, "support forward index stub");
DEFINE_bool(is_support_adfeature_proxy_stub, false, "support feature proxy stub");
DEFINE_bool(is_support_adforward_algorithm_stub, false, "support forward algorithm stub");
DEFINE_bool(is_support_brand_stub, false, "support brand stub");
DEFINE_bool(is_support_multi_stub, false, "support multi stub");
DEFINE_bool(is_support_grid_stub, false, "support grid stub");

DEFINE_string(redis_cluster_name, "adInterfaceTest", "front: adInterfaceTest, rank: adEngineFlowRecord ");
DEFINE_string(redis_prerank_cluster_name, "adInterfaceTest", "prerank redis");
DEFINE_int32(thread_timeout, 1000, "service thread timeout");
//prerank request router specially process
DEFINE_bool(is_prerank_request_router_online, false, "request online router and store response");
//DEFINE_string(env_type, "", "base and test");
DEFINE_int32(prerank_router_timeout, 200, "prerank request router timeout");
DEFINE_int32(ttl, 1, "redis key expire time");
DEFINE_int32(sleep_times, 50, "sleep times");
DEFINE_bool(debug_print_response_hash, false, "print response hash");
DEFINE_bool(is_check_already_request_online, false, "request online router already flag");

//test tdm router response
DEFINE_bool(is_debug_lill_tdm_router, false, "tdm router debug response size");

DEFINE_string(shard_num, "0", "bidword shard num");

//0225 dragon request online
DEFINE_bool(is_dragon_request_online, false, "request online dragon and store response");

//0919 ad-ems-shard
DEFINE_bool(is_support_ems_stub, false, "support ems");

//std::string filekey = FLAGS_file_name;
//LOG(INFO) << "key is: " << key << ", response is: " << res;
namespace ad::test::stub {
#define REGISTER_HANDLER_TEST(RpcName, ReqName, ResName)                                                               \
  ::grpc::Status RpcName(::grpc::ServerContext *context, const ReqName *req, ResName *response) override {         \
    ::grpc::Status status = ::grpc::Status::OK;                                                                        \
    auto trace_id = ks::infra::kenv::ServiceMeta::GetRecordContext().GetBizContextValue("ad", "trace_id");   \
    auto lane_id = ks::infra::kenv::ServiceMeta::GetLane();     \
    const auto need_qa_tag_kess_set = ad::test::nbdriver::parse_get_qa_tag_set(); \
    if (trace_id.empty()) \
      return status;     \
    std::string key = trace_id + "_" + kess_name;    \
    std::string method_name = #RpcName;  \
    if (FLAGS_is_support_adfront_stub || FLAGS_is_support_adrank_stub || FLAGS_is_support_adtarget_stub || FLAGS_is_support_adserver_stub || FLAGS_is_support_i18n_stub || FLAGS_is_support_adforward_algorithm_stub || FLAGS_is_support_adfeature_proxy_stub || FLAGS_is_support_adforward_index_stub || FLAGS_is_support_addragon_stub || FLAGS_is_support_brand_stub || FLAGS_is_support_grid_stub) { \
      key = trace_id + "_" + kess_name + "_" + method_name;  \
    }\
    LOG(INFO) << "lill debug: key is: " << key << ", RpcName is: " << method_name; \
    std::string res{};  \
    GetKey(key, &res);          \
    if (!res.empty()) {                                                                                                \
      response->ParseFromString(res);                                                                                  \
      LOG(INFO) << "res is not empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".ok";                                                                      \
      falcon::Inc(falcon_key.data());                                                                                  \
    } else {                                                                                                           \
      LOG(INFO) << "res is empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".err";                                                                     \
      falcon::Inc(falcon_key.data());                                                                                  \
    }                                                                                                                  \
    return status;                                                                                                     \
  }

#define REGISTER_HANDLER_TEST_NEW(RpcName, ReqName, ResName)                                                               \
  ::grpc::Status RpcName(::grpc::ServerContext *context, const ReqName *req, ResName *response) {         \
    ::grpc::Status status = ::grpc::Status::OK;                                                                        \
    auto trace_id = ks::infra::kenv::ServiceMeta::GetRecordContext().GetBizContextValue("ad", "trace_id");   \
    auto lane_id = ks::infra::kenv::ServiceMeta::GetLane();     \
    const auto need_qa_tag_kess_set = ad::test::nbdriver::parse_get_qa_tag_set(); \
    if (trace_id.empty()) \
      return status;     \
    std::string key = trace_id + "_" + kess_name;    \
    std::string method_name = #RpcName;  \
    if (FLAGS_is_support_adfront_stub || FLAGS_is_support_adrank_stub || FLAGS_is_support_adtarget_stub || FLAGS_is_support_adserver_stub || FLAGS_is_support_i18n_stub || FLAGS_is_support_adforward_algorithm_stub || FLAGS_is_support_adfeature_proxy_stub || FLAGS_is_support_adforward_index_stub) { \
      key = trace_id + "_" + kess_name + "_" + method_name;  \
    }\
    LOG(INFO) << "lill debug: key is: " << key << ", RpcName is: " << method_name; \
    std::string res{};  \
    GetKey(key, &res);          \
    if (!res.empty()) {                                                                                                \
      response->ParseFromString(res);                                                                                  \
      LOG(INFO) << "res is not empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".ok";                                                                      \
      falcon::Inc(falcon_key.data());                                                                                  \
    } else {                                                                                                           \
      LOG(INFO) << "res is empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".err";                                                                     \
      falcon::Inc(falcon_key.data());                                                                                  \
    }                                                                                                                  \
    return status;                                                                                                     \
  }


#define REGISTER_HANDLER_BRPC_TEST(RpcName, ReqName, ResName)                                                               \
  void RpcName(::google::protobuf::RpcController* controller, const ReqName *req, ResName *response, ::google::protobuf::Closure* done) override {         \
    ::brpc::ClosureGuard done_guard(done);   \
    ::grpc::Status status = ::grpc::Status::OK;                                                                        \
    auto trace_id = ks::infra::kenv::ServiceMeta::GetRecordContext().GetBizContextValue("ad", "trace_id");   \
    auto lane_id = ks::infra::kenv::ServiceMeta::GetLane();     \
    const auto need_qa_tag_kess_set = ad::test::nbdriver::parse_get_qa_tag_set(); \
    if (trace_id.empty()) \
      return ;     \
    std::string key = trace_id + "_" + kess_name;    \
    std::string method_name = #RpcName;  \
    if (FLAGS_is_support_adfront_stub || FLAGS_is_support_adrank_stub || FLAGS_is_support_adtarget_stub || FLAGS_is_support_adserver_stub || FLAGS_is_support_i18n_stub || FLAGS_is_support_addragon_stub) { \
      key = trace_id + "_" + kess_name + "_" + method_name;  \
    }\
    LOG(INFO) << "lill debug: key is: " << key << ", RpcName is: " << method_name; \
    std::string res{};  \
    GetKey(key, &res);          \
    if (!res.empty()) {                                                                                                \
      response->ParseFromString(res);                                                                                  \
      LOG(INFO) << "res is not empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".ok";                                                                      \
      falcon::Inc(falcon_key.data());                                                                                  \
    } else {                                                                                                           \
      LOG(INFO) << "res is empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".err";                                                                     \
      falcon::Inc(falcon_key.data());                                                                                  \
    }                                                                                                                  \
    return;                                                                                                     \
  }


#define REGISTER_HANDLER_TEST_QA_TAG(RpcName, ReqName, ResName)                                                               \
  ::grpc::Status RpcName(::grpc::ServerContext *context, const ReqName *req, ResName *response) override {         \
    ::grpc::Status status = ::grpc::Status::OK;                                                                        \
    auto trace_id = ks::infra::kenv::ServiceMeta::GetRecordContext().GetBizContextValue("ad", "trace_id");   \
    auto lane_id = ks::infra::kenv::ServiceMeta::GetLane();     \
    const auto need_qa_tag_kess_set = ad::test::nbdriver::parse_get_qa_tag_set(); \
    if (trace_id.empty()) \
      return status;     \
    std::string key = trace_id + "_" + kess_name;    \
    std::string method_name = #RpcName;  \
    if (FLAGS_is_support_adrank_stub) { \
      int qa_tag = 0; \
      if (!need_qa_tag_kess_set) { \
        LOG(ERROR) << "need_qa_tag_kess_set is empty"; \
      }else { \
        if (need_qa_tag_kess_set->find(kess_name) != need_qa_tag_kess_set->end()) {\
            qa_tag = req->qa_tag(); \
            LOG(INFO) << "get qa_tag from request, kess_name is: " << kess_name << ", qa_tag is: " << qa_tag; \
        } \
      } \
      if (qa_tag != 0) { \
        key = trace_id + "_" + kess_name + "_" + method_name + "_" + std::to_string(qa_tag);  \
      }else { \
        key = trace_id + "_" + kess_name + "_" + method_name;  \
      }\
    }else if (FLAGS_is_support_adtarget_stub || FLAGS_is_support_adserver_stub || FLAGS_is_support_adfront_stub) { \
      key = trace_id + "_" + kess_name + "_" + method_name;  \
    }\
    LOG(INFO) << "lill debug: key is: " << key << ", RpcName is: " << method_name; \
    std::string res{};  \
    GetKey(key, &res);          \
    if (!res.empty()) {                                                                                                \
      response->ParseFromString(res);                                                                                  \
      LOG(INFO) << "res is not empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".ok";                                                                      \
      falcon::Inc(falcon_key.data());                                                                                  \
    } else {                                                                                                           \
      LOG(INFO) << "res is empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".err";                                                                     \
      falcon::Inc(falcon_key.data());                                                                                  \
    }                                                                                                                  \
    return status;                                                                                                     \
  }

#define REGISTER_HANDLER_TEST_QA_TAG_PREDICT(RpcName, ReqName, ResName)                                                               \
  ::grpc::Status RpcName(::grpc::ServerContext *context, const ReqName *req, ResName *response) override {         \
    ::grpc::Status status = ::grpc::Status::OK;                                                                        \
    auto trace_id = ks::infra::kenv::ServiceMeta::GetRecordContext().GetBizContextValue("ad", "trace_id");   \
    auto lane_id = ks::infra::kenv::ServiceMeta::GetLane();     \
    const auto need_qa_tag_kess_set = ad::test::nbdriver::parse_get_qa_tag_set(); \
    if (trace_id.empty()){ \
      return status;     \
    }\
    std::string tmp_kess_name;\
    if(kess_name == "grpc_ad_router_predict_stub") { \
      tmp_kess_name = kess_name;\
      kess_name = req->group_name();\
      LOG(INFO) << "dxp_debug: get group_name from request, kess_name is: " << kess_name << ", group_name is: " << req->group_name();\
      } \
    std::string key = trace_id + "_" + kess_name;    \
    std::string method_name = #RpcName;  \
    if (FLAGS_is_support_adrank_stub || FLAGS_is_support_adtarget_stub || FLAGS_is_support_addragon_stub) { \
      int qa_tag = 0; \
      if (!need_qa_tag_kess_set) { \
        LOG(ERROR) << "need_qa_tag_kess_set is empty"; \
      }else if(FLAGS_is_support_adrank_stub){ \
        if (need_qa_tag_kess_set->find(kess_name) != need_qa_tag_kess_set->end()) {\
            qa_tag = req->qa_tag(); \
            LOG(INFO) << "get qa_tag from request, kess_name is: " << kess_name << ", qa_tag is: " << qa_tag; \
        } \
      }else if (FLAGS_is_support_adtarget_stub || FLAGS_is_support_addragon_stub){ \
        if (need_qa_tag_kess_set->find(kess_name) != need_qa_tag_kess_set->end() && \
            (kess_name == "grpc_adTwinTowersRouterServer" || \
             kess_name == "grpc_adTwinTowersRouterServer_thanos" || \
             kess_name == "ad-twin-towers-router" || \
             kess_name == "ad-twin-towers-router-universe" || \
             kess_name == "ad-twin-towers-router-thanos" || \
             kess_name == "ad-twin-towers-router-universe-tiny" || \
             kess_name == "ad-twin-towers-router_diff_stub" || \
             kess_name == "ad-twin-towers-router-thanos_diff_stub" || \
             kess_name == "ad-twin-towers-router-universe_diff_stub" || \
             kess_name == "ad-twin-towers-router-universe-tiny_diff_stub" || \
             kess_name == "grpc_adTwinTowersRouterServer_universe")) { \
            qa_tag = req->qa_tag(); \
            LOG(INFO) << "get target adTwinTowersRouterServer or ad-twin-towers-router qa_tag from request, kess_name is: " << kess_name << ", qa_tag is: " << qa_tag; \
        } \
      } \
      if (qa_tag != 0) { \
        key = trace_id + "_" + kess_name + "_" + method_name + "_" + std::to_string(qa_tag);  \
      }else { \
        key = trace_id + "_" + kess_name + "_" + method_name;  \
      }\
    }else if (FLAGS_is_support_adserver_stub || FLAGS_is_support_adfront_stub || FLAGS_is_support_brand_stub) { \
      key = trace_id + "_" + kess_name + "_" + method_name;  \
    }\
    if(!tmp_kess_name.empty()){\
      LOG(INFO) << "dxp_debug tmp_kess_name: "<< tmp_kess_name << ",key is: " << key << ", RpcName is: " << method_name << ", lane_id is: " << lane_id; \
    }\
    LOG(INFO) << "lill debug: key is: " << key << ", RpcName is: " << method_name << ", lane_id is: " << lane_id; \
    std::string res{}; \
    auto prerank_req_online_router_set = ad::test::nbdriver::prerank_req_online_router();  \
    auto dragon_server_req_online_set = ad::test::nbdriver::dragon_server_req_online();    \
    if (FLAGS_is_prerank_request_router_online && prerank_req_online_router_set && prerank_req_online_router_set->find(kess_name) != prerank_req_online_router_set->end()) { \
      GetNewKey(kess_name, lane_id, key, &res, req, response);          \
      return status;     \
    }else if (FLAGS_is_support_addragon_stub && dragon_server_req_online_set && dragon_server_req_online_set->find(kess_name) != dragon_server_req_online_set->end()) {  \
      GetNewKey(kess_name, lane_id, key, &res, req, response);          \
      return status; \
    }else {  \
      GetKey(key, &res); \
    }\
    if (!res.empty()) {                                                                                                \
      response->ParseFromString(res);                                                                                  \
      if(!tmp_kess_name.empty()){\
        LOG(INFO) << "dxp_debug res is not empty, key is: " << key << ", response size is: " << res.size(); \
      }\
      LOG(INFO) << "res is not empty, key is: " << key << ", response size is: " << res.size(); \
      if (kess_name == "grpc_adTdmRouterServer" && FLAGS_is_debug_lill_tdm_router) { \
          LOG(INFO) << "lill debug: grpc_adTdmRouterServer debug, key is: " << key << ", embedding_result_size is: " << response->embedding_result_size() << ", lane_id is: " << lane_id; \
      }\
      if (FLAGS_is_support_adrank_stub_for_q){  \
          adps_variation_coefficient = ad::test::nbdriver::adPsVariationCoefficient(); \
          for (int i = 0; i < response->predict_result_size(); ++i) { \
              for (int j =0; j < response->mutable_predict_result(i)->value_size(); j++){ \
                  value = response->mutable_predict_result(i)->value(j); \
                  response->mutable_predict_result(i)->set_value(j, value * adps_variation_coefficient); \
		  LOG(INFO) << "response_old : " << value << ", response_new : " << response->predict_result(i).value(j); \
              }\
          } \
      }\
      std::string falcon_key = kess_name + ".ok";                                                                      \
      falcon::Inc(falcon_key.data());                                                                                  \
    } else {                                                                                                           \
      LOG(INFO) << "res is empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".err";                                                                     \
      falcon::Inc(falcon_key.data());                                                                                  \
    }                                                                                                                  \
    return status;                                                                                                     \
  }

#define REGISTER_HANDLER_TEST_DRAGON(RpcName, ReqName, ResName)                                                               \
  ::grpc::Status RpcName(::grpc::ServerContext *context, const ReqName *req, ResName *response) override {         \
    ::grpc::Status status = ::grpc::Status::OK;                                                                        \
    auto trace_id = ks::infra::kenv::ServiceMeta::GetRecordContext().GetBizContextValue("ad", "trace_id");   \
    auto lane_id = ks::infra::kenv::ServiceMeta::GetLane();     \
    const auto need_qa_tag_kess_set = ad::test::nbdriver::parse_get_qa_tag_set(); \
    if (trace_id.empty()){ \
      return status;     \
    }\
    std::string tmp_kess_name; \
    if(kess_name == "grpc_ad_router_dragon_predict_stub") { \
      tmp_kess_name = kess_name; \
      for (const auto& attr : req->common_attr()) { \
        if (attr.name() == "group_name" && attr.type() ==  kuiba::CommonSampleEnum::STRING_ATTR && !attr.string_value().empty()) { \
            kess_name =  attr.string_value(); \
            LOG(INFO) << "dxp_debug: get group_name from request, kess_name is: " << kess_name; \
            break; \
        } \
      } \
    } \
    std::string key = trace_id + "_" + kess_name;    \
    std::string method_name = #RpcName;  \
    if (FLAGS_is_support_adrank_stub || FLAGS_is_support_adtarget_stub || FLAGS_is_support_addragon_stub) { \
      int qa_tag = 0; \
      if (!need_qa_tag_kess_set) { \
        LOG(ERROR) << "need_qa_tag_kess_set is empty"; \
      }else if(FLAGS_is_support_adrank_stub){ \
        if (need_qa_tag_kess_set->find(kess_name) != need_qa_tag_kess_set->end()) {\
            qa_tag = 0; \
            LOG(INFO) << "get qa_tag from request, kess_name is: " << kess_name << ", qa_tag is: " << qa_tag; \
        } \
      }else if (FLAGS_is_support_adtarget_stub || FLAGS_is_support_adtarget_stub){ \
	if (kess_name == "ad-dragon-prerank-server-outer" || "ad-dragon-prerank-server") {\
          for (auto& column : req->common_data().columns()) {\
            if (column.name() == "qa_tag" && !column.value().empty()) {\
	      base::ConstArray<int64> int_list;\
	      int_list.SetData(column.value());\
	      for (int64 val : int_list) {\
	        qa_tag = val;\
		break;\
              }\
              break;\
	    }\
	  }\
	  LOG(INFO) << "get target prerank qa_tag from request, kess_name is: " << kess_name << ", qa_tag is: " << qa_tag; \
	} else if (need_qa_tag_kess_set->find(kess_name) != need_qa_tag_kess_set->end() && \
            (kess_name == "grpc_adTwinTowersRouterServer" || \
             kess_name == "grpc_adTwinTowersRouterServer_thanos" || \
             kess_name == "ad-twin-towers-router" || \
             kess_name == "ad-twin-towers-router-universe" || \
             kess_name == "ad-twin-towers-router-universe-tiny" || \
             kess_name == "ad-twin-towers-router-thanos" || \
             kess_name == "ad-twin-towers-router_diff_stub" || \
             kess_name == "ad-twin-towers-router-thanos_diff_stub" || \
             kess_name == "ad-twin-towers-router-universe_diff_stub" || \
             kess_name == "ad-twin-towers-router-universe-tiny_diff_stub" || \
             kess_name == "grpc_adTwinTowersRouterServer_universe")) { \
            qa_tag = 0; \
            LOG(INFO) << "get target adTwinTowersRouterServer or ad-twin-towers-router qa_tag from request, kess_name is: " << kess_name << ", qa_tag is: " << qa_tag; \
        } \
      } \
      if (qa_tag != 0) { \
        key = trace_id + "_" + kess_name + "_" + method_name + "_" + std::to_string(qa_tag);  \
      }else { \
        key = trace_id + "_" + kess_name + "_" + method_name;  \
      }\
    }else if (FLAGS_is_support_adserver_stub || FLAGS_is_support_adfront_stub) { \
      key = trace_id + "_" + kess_name + "_" + method_name;  \
    }\
    if(!tmp_kess_name.empty()){\
       LOG(INFO) << "dxp_debug tmp_kess_name is: "<< tmp_kess_name << ", key is: " << key << ", RpcName is: " << method_name << ", lane_id is: " << lane_id; \
    }\
    LOG(INFO) << "lill debug: key is: " << key << ", RpcName is: " << method_name << ", lane_id is: " << lane_id; \
    std::string res{}; \
    auto dragon_server_req_online_set = ad::test::nbdriver::dragon_server_req_online();    \
    if (FLAGS_is_dragon_request_online && dragon_server_req_online_set && dragon_server_req_online_set->find(kess_name) != dragon_server_req_online_set->end()) {       \
      GetDragonNewKey(kess_name, lane_id, key, &res, req, response);       \
      return status;         \
    }else {  \
      GetKey(key, &res); \
    }\
    if (!res.empty()) {                                                                                                \
      response->ParseFromString(res);                                                                                  \
      if(!tmp_kess_name.empty()){\
       LOG(INFO) << "dxp_debug  key is: " << key << ", response size is: " << res.size(); \
      }\
      LOG(INFO) << "res is not empty, key is: " << key << ", response size is: " << res.size(); \
      if (kess_name == "grpc_adTdmRouterServer" && FLAGS_is_debug_lill_tdm_router) { \
          LOG(INFO) << "lill debug: grpc_adTdmRouterServer debug, key is: " << key << ", lane_id is: " << lane_id; \
      }\
      std::string falcon_key = kess_name + ".ok";                                                                      \
      falcon::Inc(falcon_key.data());                                                                                  \
    } else {                                                                                                           \
      LOG(INFO) << "res is empty, key is: " << key << ", response size is: " << res.size();    \
      std::string falcon_key = kess_name + ".err";                                                                     \
      falcon::Inc(falcon_key.data());                                                                                  \
    }                                                                                                                  \
    return status;                                                                                                     \
  }

class baseStub {
public:
  baseStub(const std::string &kess_name) : kess_name(std::move(kess_name)) {
    if ( FLAGS_is_support_i18n_stub ) {
      redis_client = ::ks::infra::RedisProxyClient::GetRedisClientByKccFromKconf(FLAGS_redis_cluster_name, 100, 100, "adqa", "adStubs");
    }else{
      redis_client = ::ks::infra::RedisProxyClient::GetRedisClientByKcc(FLAGS_redis_cluster_name, 100, 100, "adqa", "adStubs");
      if (FLAGS_is_prerank_request_router_online) {
        redis_client_prerank = ::ks::infra::RedisProxyClient::GetRedisClientByKcc(FLAGS_redis_prerank_cluster_name, 100, 100, "adqa", "adStubs");
      }
    }
    if (redis_client == nullptr) {
      LOG(ERROR) << "redis_client inti failed";
    }
    if (FLAGS_is_prerank_request_router_online && (redis_client_prerank == nullptr)) {
      LOG(ERROR) << "redis_client_prerank inti failed";
    }
  }

  void RequestRouterAndSetRedis(const std::string kess_name, const std::string key, std::string &res, const kuaishou::ad::algorithm::UniversePredictRequest &req, kuaishou::ad::algorithm::UniversePredictResponse *predict_response) {
    using kuaishou::ad::algorithm::kess::UniversePredictService;
    //kuaishou::ad::algorithm::UniversePredictResponse *predict_response = nullptr ;
    //去掉diff_stub的后缀,转发到真实的kess上
    std::vector<std::string> split_kess_info = absl::StrSplit(kess_name, "_diff_");
    std:: string real_kess_name = "";
    if (split_kess_info.size() == 2) {
      real_kess_name = split_kess_info.at(0);
      LOG(INFO) << "lill debug: prerank, real kess_name send to prod, kess_name is: " << real_kess_name;
    }
    if (real_kess_name == "") {
      LOG(INFO) << "lill debug: prerank, RequestRouterAndSetRedis, client is null ...";
      return;
    }
	auto client = ks::ad_base::AdKessClient::ClientOfKey<UniversePredictService>(real_kess_name);
    std::string hash_str = absl::StrCat(req.user_id(), "PsUserHash");
    auto hash = base::CityHash64(hash_str.c_str(), hash_str.length());
    bool need_select_one = false;
    if (real_kess_name == "grpc_adPsUniverseRouterServer" || real_kess_name == "ad-predict-router-universe") {
      hash = 0;
      //线上用device_id来代替user_id,但是桩无法获取target请求例的device_id,所以不用hash的方式
      //if (req.request_hash()) {
      //  hash = req.request_hash().key_id();
      //}
      if (hash == 0) {
        need_select_one = true;
      }
    }
    auto stub = need_select_one ? client.second->SelectOne() : client.second->SelectByHash(hash);
    auto status = stub->Predict(ks::ad_base::OptionsFromMilli(FLAGS_prerank_router_timeout), req, predict_response);

    LOG(INFO) << "lill debug: prerank, RequestRouterAndSetRedis, hash_is : " << hash << ", error code is: " << status.error_code() << ", time_out is: " << client.first->time_out << ", division is: " << ks::kess::configurator::internal::DefaultDivision() << ", kess_name is: " << client.first->kess_name << ", key is: " << key;
    if (!status.ok()) {
      LOG(INFO) << "key is: " << key << ", prerank or recall_predict_failed, status.ok is: " << status.ok() << ", error message is: " << status.error_message();
    }else if (real_kess_name != "grpc_adTdmRouterServer" &&
              real_kess_name != "ad-twin-towers-router-thanos" &&
              real_kess_name != "ad-twin-towers-router" &&
              real_kess_name != "ad-twin-towers-router-universe" &&
              real_kess_name != "ad-twin-towers-router-universe-tiny" &&
              !FLAGS_is_support_addragon_stub &&
              predict_response->predict_result_size() <= 0) {
      LOG(INFO) << "key is: " << key << ", prerank_predict_size is 0";
    }else if ((real_kess_name == "grpc_adTdmRouterServer" ||
               real_kess_name == "ad-twin-towers-router-thanos" ||
               real_kess_name == "ad-twin-towers-router" ||
               real_kess_name == "ad-twin-towers-router-universe" ||
               real_kess_name == "ad-twin-towers-router-universe-tiny") &&
               !FLAGS_is_support_addragon_stub &&
              predict_response->embedding_result_size() <=0) {
      LOG(INFO) << "key is: " << key << ", recal router embedding result_size is 0";
    }else if (predict_response->status() != kuaishou::ad::algorithm::STATUS_OK) {
      LOG(INFO) << "key is: " << key << ", prerank or recall predict_response_error, predict_response.status() is: " << predict_response->status();
    }else{
      //请求成功存Redis
      int32 ttl = FLAGS_ttl * 3600;
      predict_response->SerializeToString(&res);
      int64_t start_sec = base::GetTimestamp();
      if (auto err = redis_client_prerank->SetEx(key, res, ttl, 500); err != ::ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
        LOG(INFO) << "set redis is error, prerank, redis_key is: " << key << ", error code is: " << err << ", res size is: " << res.size();
      }else{
        std::string tmp_str = predict_response->ShortDebugString();
        uint64 res_hash = base::CityHash64(tmp_str.c_str(), tmp_str.size());
        LOG(INFO) << "set redis is success, prerank, key is: " << key << ", res size is: " << res.size() << ", predict_response hash is: " << res_hash << ", err is: " << err << ", start_sec is: " << start_sec;
      }
    }
  }

  void RequestDragonAndSetRedis(const std::string kess_name, const std::string key, std::string &res, const ks::platform::CommonRecoRequest &req, ks::platform::CommonRecoResponse *dragon_response) {
    using ks::platform::kess::CommonRecoLeafService;
    //kuaishou::ad::algorithm::UniversePredictResponse *predict_response = nullptr ;
    //去掉diff_stub的后缀,转发到真实的kess上
    std::vector<std::string> split_kess_info = absl::StrSplit(kess_name, "_diff_");
    std:: string real_kess_name = "";
    if (split_kess_info.size() == 2) {
      real_kess_name = split_kess_info.at(0);
      LOG(INFO) << "lill debug: dragon, real kess_name send to prod, kess_name is: " << real_kess_name;
    }
    if (real_kess_name == "") {
      LOG(INFO) << "lill debug: RequestDragonAndSetRedis, client is null ...";
      return;
    }
	auto client = ks::ad_base::AdKessClient::ClientOfKey<CommonRecoLeafService>(real_kess_name);
    std::string hash_str = absl::StrCat(req.user_id(), "PsUserHash");
    auto hash = base::CityHash64(hash_str.c_str(), hash_str.length());
    bool need_select_one = false;
    if (real_kess_name == "grpc_adPsUniverseRouterServer" || real_kess_name == "ad-predict-router-universe") {
      hash = 0;
      //线上用device_id来代替user_id,但是桩无法获取target请求例的device_id,所以不用hash的方式
      //if (req.request_hash()) {
      //  hash = req.request_hash().key_id();
      //}
      if (hash == 0) {
        need_select_one = true;
      }
    }
    auto stub = need_select_one ? client.second->SelectOne() : client.second->SelectByHash(hash);
    auto status = stub->Recommend(ks::ad_base::OptionsFromMilli(FLAGS_prerank_router_timeout), req, dragon_response);

    LOG(INFO) << "lill debug: dragon, RequestDragonAndSetRedis, hash_is : " << hash << ", error code is: " << status.error_code() << ", time_out is: " << client.first->time_out << ", division is: " << ks::kess::configurator::internal::DefaultDivision() << ", kess_name is: " << client.first->kess_name << ", key is: " << key;
    if (!status.ok()) {
      LOG(INFO) << "key is: " << key << ", dragon request_failed, status.ok is: " << status.ok() << ", error message is: " << status.error_message();
    //}else if (dragon_response->status() != kuaishou::ad::algorithm::STATUS_OK) {
    //  LOG(INFO) << "key is: " << key << ", request dragon_response_error, predict_response.status() is: " << dragon_response->status();
    }else{
      //请求成功存Redis
      int32 ttl = FLAGS_ttl * 3600;
      dragon_response->SerializeToString(&res);
      int64_t start_sec = base::GetTimestamp();
      if (auto err = redis_client_prerank->SetEx(key, res, ttl, 500); err != ::ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
        LOG(INFO) << "set redis is error, dragon, redis_key is: " << key << ", error code is: " << err << ", res size is: " << res.size();
      }else{
        std::string tmp_str = dragon_response->ShortDebugString();
        uint64 res_hash = base::CityHash64(tmp_str.c_str(), tmp_str.size());
        LOG(INFO) << "set redis is success, dragon, key is: " << key << ", res size is: " << res.size() << ", dragon_response hash is: " << res_hash << ", err is: " << err << ", start_sec is: " << start_sec;
      }
    }
  }

  void GetDragonNewKey(std::string &kess_name, std::string &lane_id, std::string &key, std::string *res, const ks::platform::CommonRecoRequest *req, ks::platform::CommonRecoResponse *dragon_response) {
    //res.clear();
    std::vector<std::string> split_lane_info = absl::StrSplit(lane_id, ".");
    std::string report_id = "";
    if (split_lane_info.size() == 2) {
      report_id = split_lane_info.at(1);
      key = absl::StrJoin({key, report_id}, "_");
    }else if (split_lane_info.size() == 3) {
      report_id = split_lane_info.at(2);
      key = absl::StrJoin({key, report_id}, "_");
    }else {
      LOG(ERROR) << "lane_id is error, not multi lane info, key is: " << key << ", lane_id is: " << lane_id;
    }
    int64_t start_sec = base::GetTimestamp();
    LOG(INFO) << "lill debug: GetDragonNewKey, key is: " << key << ", kess_name is: " << kess_name << ", lane_id is: " << lane_id << ", start_sec is: " << start_sec;
    auto dragon_server_req_online_set = ad::test::nbdriver::dragon_server_req_online();
    if (FLAGS_is_dragon_request_online && dragon_server_req_online_set && dragon_server_req_online_set->find(kess_name) != dragon_server_req_online_set->end()) {
      int32_t j = 0;
      bool is_get_success = false;
      int32_t retry_num = 3;
      ::ks::kess::proto::rpc::TraceContext pb;
      pb.mutable_stress_test_context()->set_biz_name("ad_diff");
      ::ks::infra::kenv::RpcStressTestContext stress_test_ctx;
      stress_test_ctx.SetBizName(pb.stress_test_context().biz_name());
      ::ks::infra::kenv::RpcTraceContext rpc_trace_context;
      rpc_trace_context.SetRpcStressTestCtx(stress_test_ctx);
      ::ks::infra::kenv::ServiceMeta::SetRpcTraceContext(rpc_trace_context);

      start_sec = base::GetTimestamp();
      while (j < retry_num ) {
        if (auto err = redis_client_prerank->Get(key, res); err != ::ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
          start_sec = base::GetTimestamp();
          LOG(ERROR) << "get redis dragon error, key is: " << key << ", error is: " << err << ", res size is: " << res->size() << ", lane_id is: " << lane_id << ", retry time is: " << j << ", start_sec is: " << start_sec << ", retry num is: " << j;
          res->clear();
          j++;
        } else {
          start_sec = base::GetTimestamp();
          is_get_success = true;
          LOG(INFO) << "get redis dragon success, key is: " << key << ", res size is: " << res->size() << ", lane_id is: " << lane_id << ", is_get_success is: " << is_get_success << ", start_sec is: " << start_sec << ", retry num is: " << j;
          break;
        }
      }
      if (!is_get_success) {
        std::string end_flag = "end_already_request_online";
        std::string already_request_online_flag_key = absl::StrJoin({key, end_flag}, "_");
        std::string tmp_res = "";
        bool is_need_request_online = true;
        if (FLAGS_is_check_already_request_online) {
          if (auto err = redis_client_prerank->Get(already_request_online_flag_key, &tmp_res); err != ::ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
            //没有从redis里获取已访问线上router的标志
            LOG(INFO) << "already_request_online_flag_key is: " << already_request_online_flag_key << ", not request online ever, so need request ...";
          }else{
            LOG(INFO) << "already_request_online_flag_key is: " << already_request_online_flag_key << ", no need request online again ...";
            is_need_request_online = false;
          }
        }
        if (is_need_request_online) {
          int k = 0;
          while (k < retry_num) {
            if (res->empty()) {
              LOG(INFO) << "lill debug: key is: " << key << ", res size is: " << res->size() << ", lane_id is: " << lane_id << ", need request dragon online" << ", retry request online dragon num is: " << k;
              dragon_response->Clear();
              res->clear();
              RequestDragonAndSetRedis(kess_name, key, *res, *req, dragon_response);
              k++;
            }else{
              break;
            }
          }
          //访问完线上router之后将标记写入redis,用于下次同样请求不再访问
          if (auto err = redis_client_prerank->SetEx(already_request_online_flag_key, end_flag, 3600, 100); err != ::ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
            LOG(INFO) << "lill debug: set end request online router flag is fail, key is: " << already_request_online_flag_key; 
          }else{
            LOG(INFO) << "lill debug: set end request online router flag is success, key is: " << already_request_online_flag_key;
          }
          //if (k == retry_num || res->empty()) {
          if (res->empty()) {
            falcon::Inc("prerank.online.fail", 1);
          }else{
            falcon::Inc("prerank.online.success", 1);
          }
          falcon::Inc("prerank.online.total", 1);
        }
      }
      if (!res->empty() && is_get_success) {
        dragon_response->Clear();
        dragon_response->ParseFromString(*res);
      } else {
      }
      std::string tmp_str = dragon_response->ShortDebugString();
      //uint64 res_hash = base::CityHash64(tmp_str.c_str(), tmp_str.size());
      uint64 res_hash = ks::ad_base::HashOf(tmp_str);
      LOG(INFO) << "GetDragonNewKey: finally response, is_get_success from redis is: " << is_get_success << ", key is: " << key << ", dragon response hash is: " << res_hash;
      return;
    }
 }

  void GetNewKey(std::string &kess_name, std::string &lane_id, std::string &key, std::string *res, const kuaishou::ad::algorithm::UniversePredictRequest *req, kuaishou::ad::algorithm::UniversePredictResponse *predict_response) {
    //res.clear();
    std::vector<std::string> split_lane_info = absl::StrSplit(lane_id, ".");
    std::string report_id = "";
    if (split_lane_info.size() == 2) {
      report_id = split_lane_info.at(1);
      key = absl::StrJoin({key, report_id}, "_");
    }else if(split_lane_info.size() == 3) {
      report_id = split_lane_info.at(2);
      key = absl::StrJoin({key, report_id}, "_");
    }else {
      LOG(ERROR) << "lane_id is error, not multi lane info, key is: " << key << ", lane_id is: " << lane_id;
    }
    int64_t start_sec = base::GetTimestamp();
    LOG(INFO) << "lill debug: GetNewKey, key is: " << key << ", kess_name is: " << kess_name << ", lane_id is: " << lane_id << ", start_sec is: " << start_sec;
    auto prerank_req_online_router_set = ad::test::nbdriver::prerank_req_online_router();
    if (FLAGS_is_prerank_request_router_online && prerank_req_online_router_set && prerank_req_online_router_set->find(kess_name) != prerank_req_online_router_set->end()) {
      int32_t j = 0;
      bool is_get_success = false;
      int32_t retry_num = 3;
      ::ks::kess::proto::rpc::TraceContext pb;
      pb.mutable_stress_test_context()->set_biz_name("ad_diff");
      ::ks::infra::kenv::RpcStressTestContext stress_test_ctx;
      stress_test_ctx.SetBizName(pb.stress_test_context().biz_name());
      ::ks::infra::kenv::RpcTraceContext rpc_trace_context;
      rpc_trace_context.SetRpcStressTestCtx(stress_test_ctx);
      ::ks::infra::kenv::ServiceMeta::SetRpcTraceContext(rpc_trace_context);

      start_sec = base::GetTimestamp();
      while (j < retry_num ) {
        if (auto err = redis_client_prerank->Get(key, res); err != ::ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
          start_sec = base::GetTimestamp();
          LOG(ERROR) << "get redis prerank error, key is: " << key << ", error is: " << err << ", res size is: " << res->size() << ", lane_id is: " << lane_id << ", retry time is: " << j << ", start_sec is: " << start_sec << ", retry num is: " << j;
          res->clear();
          j++;
        } else {
          start_sec = base::GetTimestamp();
          is_get_success = true;
          LOG(INFO) << "get redis prerank success, key is: " << key << ", res size is: " << res->size() << ", lane_id is: " << lane_id << ", is_get_success is: " << is_get_success << ", start_sec is: " << start_sec << ", retry num is: " << j;
          break;
        }
      }
      if (!is_get_success) {
        std::string end_flag = "end_already_request_online";
        std::string already_request_online_flag_key = absl::StrJoin({key, end_flag}, "_");
        std::string tmp_res = "";
        bool is_need_request_online = true;
        if (FLAGS_is_check_already_request_online) {
          if (auto err = redis_client_prerank->Get(already_request_online_flag_key, &tmp_res); err != ::ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
            //没有从redis里获取已访问线上router的标志
            LOG(INFO) << "already_request_online_flag_key is: " << already_request_online_flag_key << ", not request online ever, so need request ...";
          }else{
            LOG(INFO) << "already_request_online_flag_key is: " << already_request_online_flag_key << ", no need request online again ...";
            is_need_request_online = false;
          }
        }
        if (is_need_request_online) {
          int k = 0;
          while (k < retry_num) {
            if (res->empty()) {
              LOG(INFO) << "lill debug: key is: " << key << ", res size is: " << res->size() << ", lane_id is: " << lane_id << ", need request online" << ", retry request online num is: " << k;
              predict_response->Clear();
              res->clear();
              RequestRouterAndSetRedis(kess_name, key, *res, *req, predict_response);
              k++;
            }else{
              break;
            }
          }
          //访问完线上router之后将标记写入redis,用于下次同样请求不再访问
          if (auto err = redis_client_prerank->SetEx(already_request_online_flag_key, end_flag, 3600, 100); err != ::ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
            LOG(INFO) << "lill debug: set end request online router flag is fail, key is: " << already_request_online_flag_key; 
          }else{
            LOG(INFO) << "lill debug: set end request online router flag is success, key is: " << already_request_online_flag_key;
          }
          //if (k == retry_num || res->empty()) {
          if (res->empty()) {
            falcon::Inc("prerank.online.fail", 1);
          }else{
            falcon::Inc("prerank.online.success", 1);
          }
          falcon::Inc("prerank.online.total", 1);
        }
      }
      if (!res->empty() && is_get_success) {
        predict_response->Clear();
        predict_response->ParseFromString(*res);
      } else {
      }
      std::string tmp_str = predict_response->ShortDebugString();
      //uint64 res_hash = base::CityHash64(tmp_str.c_str(), tmp_str.size());
      uint64 res_hash = ks::ad_base::HashOf(tmp_str);
      LOG(INFO) << "GetNewKey: finally response, is_get_success from redis is: " << is_get_success << ", key is: " << key << ", predict_response->predict_result_size() is: " << predict_response->predict_result_size() << ", embedding_result_size() is: " << predict_response->embedding_result_size() << ", predict response hash is: " << res_hash;
      return;
    }
 }

  void GetKey(const std::string &key, std::string *res) {
    //res.clear();
    int32_t i = 0;
    while (i < 3) {
      if (auto err = redis_client->Get(key, res); err != ::ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
        LOG(ERROR) << "redis get key error, key is: " << key << ", redis error code is: " << err << ", res is: " << *res << ", res size is: " << res->size();
        res->clear();
        i++;
      } else {
        uint64 finally_res_hash = 0;
        if (FLAGS_debug_print_response_hash) {
          std::string tmp_str = *res;
          //finally_res_hash = base::CityHash64(tmp_str.c_str(), tmp_str.size());
          finally_res_hash = ks::ad_base::HashOf(tmp_str);
        }
        LOG(INFO) << "get redis success, key is: " << key << ", res size is: " << res->size() << ", finally_res_hash is: " << finally_res_hash;
        break;
      }
    }
  }

  template <typename ResName>
  static int GetFile(const std::string &key, ResName* rn) {
    std::string content;
    bool succ = base::file_util::ReadFileToString(key, &content);
    if (!succ) {
      LOG(ERROR) << "parse from file failed, file path: " << key;
      return -1;
    }
    google::protobuf::util::JsonParseOptions options;
    options.ignore_unknown_fields = true;
    auto status = google::protobuf::util::JsonStringToMessage(content, rn, options);
    if (!status.ok()) {
      LOG(ERROR) << "json to message failed! " << key;
      return -2;
    }
    return 0;
  }
protected:
  ks::infra::RedisClient *redis_client = nullptr;
  ks::infra::RedisClient *redis_client_prerank = nullptr;
  //std::string res = {};
  std::string rpc_func_name = {};
  std::string kess_name = {};
  double adps_variation_coefficient = 0.0;
  float value = 0.0;
};
class BrandInfoQueryInStub : public kuaishou::ad::brandv2::info::kess::AdBrandQueryRpcService::Service, public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(QueryPreviewInfo, kuaishou::ad::brandv2::info::PreviewInfoRequest, kuaishou::ad::brandv2::info::PreviewInfoResponse)
  REGISTER_HANDLER_TEST(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class RECOIDMAPPINGSTUB : public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class AdServerStub : public kuaishou::ad::kess::AdGrpcService::Service, public baseStub{
public:
  REGISTER_HANDLER_TEST(GetAdResult, AdRequest, AdResponse)
  REGISTER_HANDLER_TEST(GetKnewsAdResult, AdRequest, AdResponse)
  REGISTER_HANDLER_TEST(RetrievalAd, AdRetrievalResult, AdRetrievalResponse)
  REGISTER_HANDLER_TEST(GetAdPhotoResponse, AdPhotoRequest, AdPhotoResponse)
  REGISTER_HANDLER_TEST(GetAdCreative, AdCreativeRequest, AdCreativeResponse)
  REGISTER_HANDLER_TEST(GetAdPhotoExtInfo, AdPhotoExtRequest, AdPhotoExtResponse)
  //REGISTER_HANDLER_TEST_NEW(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class AdServerStubDragon : public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  //新增recommend的接口
  REGISTER_HANDLER_TEST(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class RtaServerStub : public RtaService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetRtaResult, kuaishou::ad::rta::RtaRequest, kuaishou::ad::rta::RtaResponse)
  using baseStub::baseStub;
};

class AdTargetStub : public kuaishou::ad::kess::AdTargetGrpcService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetTargetAdResult, AdRequest, AdTargetResponse)
  using baseStub::baseStub;
};

class AdGridStub : public ks::grid::proto::kess::GridService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(BatchGetTable, ::ks::grid::proto::GridRequest, ::ks::grid::proto::GridResponse)
  using baseStub::baseStub;
};

class AdTargetStubDragon : public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  //新增recommend的接口
  REGISTER_HANDLER_TEST(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class AdTargetBrpcServiceImpl : public kuaishou::ad::generic_rpc::AdTargetGrpcService, public baseStub {
public:
  REGISTER_HANDLER_BRPC_TEST(GetTargetAdResult, AdRequest, AdTargetResponse)
  using baseStub::baseStub;
};

class AdMerchantDspServerStub : public kuaishou::ad::kess::AdTargetGrpcService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetTargetAdResult, AdRequest, AdTargetResponse)
  using baseStub::baseStub;
};

class AdMerchantDspServerStubDragon : public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class AdFrontStub : public kuaishou::ad::kess::FrontService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetAdResult, FrontServerRequest, FrontServerResponse)
  using baseStub::baseStub;
};

class AdRankStub : public kuaishou::ad::kess::AdRankService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(Rank, AdRankRequest, AdRankResponse)
  REGISTER_HANDLER_TEST(RankForGalaxyFlow, AdRankRequest, AdRankResponse)
  using baseStub::baseStub;
};

class AdRankStubDragon : public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  //新增recommend的接口
  REGISTER_HANDLER_TEST(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class AdBrandBuyerStub : public kuaishou::ad::buyer::kess::BrandBuyerServer::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetAdResult, AdRequest, AdResponse)
  using baseStub::baseStub;
};

class AdPsRouterStub : public kuaishou::ad::algorithm::kess::UniversePredictService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST_QA_TAG_PREDICT(Predict, kuaishou::ad::algorithm::UniversePredictRequest, kuaishou::ad::algorithm::UniversePredictResponse)
  using baseStub::baseStub;
};

class AdPsRouterDragonStub :  public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST_DRAGON(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class AdPsRouterPrepareStub : public kuaishou::ad::algorithm::kess::UniversePredictService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(Prepare, kuaishou::ad::algorithm::PreparePredictRequest, kuaishou::ad::algorithm::PreparePredictResponse)
  using baseStub::baseStub;
};

class AdRetrServiceStub : public kuaishou::ad::kess::AdRetrService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetRetrResult, AdRetrRequest, AdRetrResponse)
  using baseStub::baseStub;
};

class AdBidwordRetrServiceStub : public kuaishou::ad::kess::BidwordRetrService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetBidWordRetrResult, BidwordRetrRequest, BidwordRetrResponse)
  using baseStub::baseStub;
};


class AdSearchRelevanceKvStub : public kuaishou::ad::kess::SearchRelevanceKVService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetRelevanceScore, ::kuaishou::ad::KVRequest, ::kuaishou::ad::KVResponse)
  using baseStub::baseStub;
};

class adPsUniverseUnionItemCreativeService : public kuaishou::ad::reco::kess::IndexService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetCreative, kuaishou::ad::reco::IndexRequest, kuaishou::ad::reco::IndexResponse)
  using baseStub::baseStub;
};

class AdPicassoGatewayService : public xlib::picasso::pb::gateway::kess::GatewayService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(KvOp, xlib::picasso::pb::gateway::KvRequest, xlib::picasso::pb::gateway::KvResponse)
  using baseStub::baseStub;
};

class AdPostServiceStub : public ks::ad_counter::kess::AdCounterService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetUnitPostData, ks::ad_counter::PostDataReq, ks::ad_counter::UnitResp)
  REGISTER_HANDLER_TEST(GetAccountPostData, ks::ad_counter::PostDataReq, ks::ad_counter::AccountResp)
  REGISTER_HANDLER_TEST(GetCreativePhotoPostData, ks::ad_counter::PostDataReq, ks::ad_counter::PhotoResp)
  REGISTER_HANDLER_TEST(GetPidData, ks::ad_counter::PidRequest, ks::ad_counter::PidResponse)
  REGISTER_HANDLER_TEST(GetLiveNewTagData, ks::ad_counter::PostLiveNewTagReq, ks::ad_counter::LiveNewTagResp)
  REGISTER_HANDLER_TEST(GetLivePcvrData, ks::ad_counter::PostLivePcvrDataReq, ks::ad_counter::LivePcvrDataResp)
  REGISTER_HANDLER_TEST(GetPostData, ks::ad_counter::PostRequest, ks::ad_counter::PostResponse)
  using baseStub::baseStub;
};

class AdBidServiceStub : public kuaishou::ad::bidService::kess::AdBidRpcService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(QueryAutoBid, kuaishou::ad::bidService::AdBidServiceAutoBidRequest, kuaishou::ad::bidService::AdBidServiceAutoBidResponse)
  using baseStub::baseStub;
};

class AdDragonBidServiceStub : public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST_DRAGON(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class AdDragonPidServiceStub : public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST_DRAGON(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};


class AdDpaServiceStub : public kuaishou::ad::kess::AdDpaService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetDpaResult, AdRequest, AdResponse)
  using baseStub::baseStub;
};

class AdRiskControlServerStub : public kuaishou::ad::risk::kess::AdRiskControlService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetRiskDemotionResponse, kuaishou::ad::EmptyParam, kuaishou::ad::risk::AdRiskControlDemotionResponse)\
  using baseStub::baseStub;
};

class AdStyleServerStub : public kuaishou::ad::kess::AdStyleServer::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(StyleSelection, AdStyleSelectionRequest, AdStyleSelectionResponse)
  using baseStub::baseStub;
};

class AdAdxServerStub : public kuaishou::ad::adx::kess::AdxService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetAdResult, AdRequest, AdResponse)
  using baseStub::baseStub;
};

class AdEmbeddingRetrRouterStub : public kuaishou::ad::kess::AdEmbeddingRetrGrpcService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(Search, AnnRequest, AnnResponse)
  REGISTER_HANDLER_TEST(NotifySearch, AnnRequest, AnnResponse)
  REGISTER_HANDLER_TEST(NotifyPrefetch, NotifyPrefetchRequest, NotifyPrefetchResponse)
  using baseStub::baseStub;
};

class AdPostServicePidStub : public ks::ad_counter::kess::AdCounterService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetPidData, ks::ad_counter::PidRequest, ks::ad_counter::PidResponse) 
  using baseStub::baseStub;
};

class AdKwaiShopSelectionStub : public kuaishou::kwaishop::selection::user::service::kess::KwaishopSelectionUserService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(QueryBuyerTags, QueryBuyerTagsRequest, QueryBuyerTagsResponse)
  using baseStub::baseStub;
};

class AdStyleServiceStub : public kuaishou::ad::kess::AdStyleService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetStyleResult, StyleRequest, StyleResponse)
  using baseStub::baseStub;
};

class AdUeqServiceStub : public kuaishou::ad::kess::AdUeqService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetUeqResult, AdUeqServiceRequest, AdUeqServiceResponse)
  using baseStub::baseStub;
};

class AdxDcxrServiceStub : public kuaishou::ad::adx::cxr::inner::kess::AdxDcxrService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetCxrInfo, kuaishou::ad::adx::cxr::inner::CxrRequest, kuaishou::ad::adx::cxr::inner::CxrResponse)
  REGISTER_HANDLER_TEST(GetDspSecondBidInfo, kuaishou::ad::adx::cxr::inner::SecondBidInfoRequest, kuaishou::ad::adx::cxr::inner::SecondBidInfoResponse)
  using baseStub::baseStub;
};

class AdFanstopCommonLeafStub : public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

/*
class AdFcServerV2Stub : public kuaishou::flowcontrol::kess::AdFCGrpcService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST_QA_TAG(GetFCResult, kuaishou::flowcontrol::AdFCRequest, kuaishou::flowcontrol::AdFCResponse)
  using baseStub::baseStub;
};
*/

class AdModleCalibrateStub : public kuaishou::ad::ad_model_calibrate::kess::AdModleCalibrateServer::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(Calibrate, kuaishou::ad::ad_model_calibrate::CalibrateRequest, kuaishou::ad::ad_model_calibrate::CalibrateResponse)
  using baseStub::baseStub;
};

class AdBrandStub : public kuaishou::ad::kess::AdBrandService::Service, public baseStub { public:
  REGISTER_HANDLER_TEST(GetBrandResult, AdRequest, AdResponse)
  using baseStub::baseStub;
};

class AdEcomHostingServiceStub : public kuaishou::ad::platform::kess::AdEcomHostingService::Service, public baseStub
{
  public:
    REGISTER_HANDLER_TEST(AddEcomHostingProject, AddEcomHostingProjectRequest, AddEcomHostingProjectResponse)
    REGISTER_HANDLER_TEST(ModEcomHostingProject, ModEcomHostingProjectRequest, ModEcomHostingProjectResponse)
    REGISTER_HANDLER_TEST(GetEcomHostingProject, GetEcomHostingProjectRequest, GetEcomHostingProjectResponse)
    REGISTER_HANDLER_TEST(QueryEcomHostingProject, QueryEcomHostingProjectRequest, QueryEcomHostingProjectResponse)
    REGISTER_HANDLER_TEST(QueryEcomHostingProjectNoCount, QueryEcomHostingProjectRequest, QueryEcomHostingProjectResponse)
    REGISTER_HANDLER_TEST(ModEcomHostingProjectStatus, ModEcomHostingProjectStatusRequest, ModEcomHostingProjectStatusResponse)
    REGISTER_HANDLER_TEST(AddEcomHostingUnit, AdEcomUnitModRequest, AdEcomUnitModResponse)
    REGISTER_HANDLER_TEST(ModEcomHostingUnit, AdEcomUnitModRequest, AdEcomUnitModResponse)
    REGISTER_HANDLER_TEST(DelEcomHostingUnit, AdEcomUnitDelRequest, AdEcomUnitDelResponse)
    REGISTER_HANDLER_TEST(ModEcomUnitExplore, AdEcomUnitExploreRequest, AdEcomUnitExploreResponse)
    REGISTER_HANDLER_TEST(ModEcomUnitColdStart, AdEcomUnitColdStartRequest, AdEcomUnitColdStartResponse)
    REGISTER_HANDLER_TEST(AddEcomHostingCreative, AdEcomCreativeModRequest, AdEcomCreativeModResponse)
    REGISTER_HANDLER_TEST(ModEcomHostingCreative, AdEcomCreativeModRequest, AdEcomCreativeModResponse)
    REGISTER_HANDLER_TEST(DelEcomHostingCreative, AdEcomCreativeModRequest, AdEcomCreativeModResponse)
    REGISTER_HANDLER_TEST(GetCampaignUnitCreativePage, GetCampaignUnitCreativeRequest, CampaignUnitCreativePageResponse)
    REGISTER_HANDLER_TEST(GetCampaignUnitPage, GetCampaignUnitRequest, CampaignUnitPageResponse)
    using baseStub::baseStub;
};

class AdCoreLiveServiceStub : public kuaishou::ad::platform::kess::AdCoreLiveService::Service, public baseStub{
  public:
    REGISTER_HANDLER_TEST(CreateHighLightCreative, AdDspHighLightCreativeAddRequest, AdDspHighLightCreativeAddResponse)
    REGISTER_HANDLER_TEST(QueryHighLightPhoto, AdDspHighLightPhotoQueryRequest, AdDspHighLightPhotoQueryResponse)
    REGISTER_HANDLER_TEST(DelCustomizedCreative, CustomizedCreativeDelRequest, CustomizedCreativeDelResponse)
    using baseStub::baseStub;
};

class AdEspServiceStub: public kuaishou::ad::platform::kess::AdEspService::Service, public baseStub{
  public:
    REGISTER_HANDLER_TEST(AppendProgramCreative, kuaishou::ad::platform::AppendProgramCreativeRequest,kuaishou::ad::platform::AppendProgramCreativeResponse);
    REGISTER_HANDLER_TEST(RemoveProgramCreative, kuaishou::ad::platform::RemoveProgramCreativeRequest,kuaishou::ad::platform::RemoveProgramCreativeResponse);
    REGISTER_HANDLER_TEST(AppendEspLiveOrder, kuaishou::ad::platform::AppendEspLiveOrderRequest, kuaishou::ad::platform::AppendEspLiveOrderResponse);
    using baseStub::baseStub;
};

class ApiCorePhotoAuthorServiceStub : public kuaishou::photo::kess::PhotoAuthorRpc::Service, public baseStub {
  public:
    REGISTER_HANDLER_TEST(GetAuthorPhotoByCursor, kuaishou::photo::PhotoTimeCursorRequest, kuaishou::photo::PhotoIdTimeResponse)
    REGISTER_HANDLER_TEST(GetAuthorPhotoByTimeRange, kuaishou::photo::PhotoTimeRangeRequest, kuaishou::photo::PhotoIdTimeResponse)
    using baseStub::baseStub;
};

class AdDsiServiceStub : public kuaishou::ad::kess::AdDsiService::Service, public baseStub {
  public:
    REGISTER_HANDLER_TEST(DsiQuery, kuaishou::ad::DsiRequest, kuaishou::ad::DsiResponse)
    using baseStub::baseStub;
};

class FansTopGatewayOrderServiceStub : public kuaishou::ad::fanstop::kess::FansTopGatewayOrderService::Service, public baseStub
{
  public:
    REGISTER_HANDLER_TEST(CreateFastSpreadOrder, FastSpreadOrderRequest, FastSpreadOrderResponse)
    REGISTER_HANDLER_TEST(CreatePhotoOrder, PhotoOrderCreateRequest, FastSpreadOrderResponse)
    using baseStub::baseStub;
};

/*
class AdDmpServiceStub : public kuaishou::ad::kess::AdDmpService::Service, public baseStub {
  public:
    REGISTER_HANDLER_TEST(FansIncreaseOrientationInfo, kuaishou::ad::FansIncreaseOrientationRequest, kuaishou::ad::FansIncreaseOrientationResponse)
    using baseStub::baseStub;
};
*/

class ApiCorePhotoServiceStub : public kuaishou::negative::kess::PhotoServiceRpc::Service, public baseStub {
  public:
    REGISTER_HANDLER_TEST(GetByIdsFailFast, GetByPhotoIdsRequest, PhotoMapResponse)
    REGISTER_HANDLER_TEST(GetSomeByIds, GetByPhotoIdsRequest, PhotoMapResponse)
    REGISTER_HANDLER_TEST(GetByIdsContainsPending, GetByPhotoIdsRequest, PhotoMapResponse)
    REGISTER_HANDLER_TEST(GetByIdsContainsDeleted, GetByPhotoIdsRequest, PhotoMapResponse)
    REGISTER_HANDLER_TEST(GetSomeContainsDeleted, GetByPhotoIdsRequest, PhotoMapResponse)
    REGISTER_HANDLER_TEST(GetMaxPhotoId, EmptyRequest, LongResponse)
    using baseStub::baseStub;
};

class FanstopPhotoServiceStub : public kuaishou::ad::fanstop::kess::FansTopPhotoService::Service, public baseStub {
  public:
    REGISTER_HANDLER_TEST(JudgePhotoCanBuy, FansTopPhotoCanBuyRequest, FansTopPhotoCanBuyReponse)
    REGISTER_HANDLER_TEST(GetPhotoList, FansTopGetPhotoListRequest, FansTopPhotoListResponse)
    REGISTER_HANDLER_TEST(AuthorWeeklyAnalyzeJudgePhotoCanBuy, FansTopPhotoCanBuyRequest, FansTopPhotoCanBuyReponse)
    using baseStub::baseStub;
};

class KwaiShopFlowLiveServiceStub : public kuaishou::kwaishop::flow::live::kess::KwaishopFlowLiveCarItemService::Service, public baseStub {
  public:
    REGISTER_HANDLER_TEST(BatchGetLiveCarItemListWithCache, KwaishopFlowLiveCarItemListRequest, KwaishopFlowLiveCarItemListResponse)
    REGISTER_HANDLER_TEST(BatchGetLiveCarItemList, KwaishopFlowLiveCarItemListRequest, KwaishopFlowLiveCarItemListResponse)
    using baseStub::baseStub;
};

/*
class EstimateServiceStub : public kuaishou::ad::kess::AdEstimateService::Service, public baseStub {
  public:
    REGISTER_HANDLER_TEST(CustomAudienceEstimate, CustomAudienceEstimateRequest, CustomAudienceEstimateResponse)
    using baseStub::baseStub;
};
*/

class AdFanstopStub : public kuaishou::fanstop::kess::FansTopService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetFansTopResult, FansTopRequest, FansTopResponse)
  using baseStub::baseStub;
};
class AdUserProfileStub : public kuaishou::ad::kess::AdUserProfileService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetProfile, AdUserProfileRequest, AdUserProfileResponse)
  REGISTER_HANDLER_TEST(GetKnewsUid, AdUserProfileRequest, AdUserProfileResponse)
  REGISTER_HANDLER_TEST(GetProfileByMultiTypeKey, AdUserProfileByMultiTypeKeyRequest, AdUserProfileResponse)
  REGISTER_HANDLER_TEST(GetUserAssignInterest, AdUserAssignInterestRequest, AdUserAssignInterestResponse)
  REGISTER_HANDLER_TEST(GetUserRecoInterest, AdUserRecoInterestRequest, AdUserRecoInterestResponse)
  REGISTER_HANDLER_TEST(GetUserDeleteInterest, AdUserDeleteInterestRequest, AdUserDeleteInterestResponse)
  REGISTER_HANDLER_TEST(GetContentUnionInfo, UnionContentRequest, UnionContentResponse)
  using baseStub::baseStub;
};
class AdSocialRecoFollowStub : public kuaishou::ad::social::kess::AdSocialFollowRecoService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetAdSocialFollowReco, FollowSocialRequest, FollowSocialResponse);
  using baseStub::baseStub;
};
class AdxTraceStub : public kuaishou::ad::adx::track::inner::kess::AdxTrackService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetTrackInfo, TrackRequest, TrackResponse);
  using baseStub::baseStub;
};

class AdStyleMaterialSTub : public kuaishou::ad::forward_index::kess::AdForwardIndexService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetTableByIds, BaseTableRequest, BaseTableResponse)
  using baseStub::baseStub;
};

class AdForwardSTub : public kuaishou::ad::forward_index::kess::AdForwardIndexService::Service, public baseStub {
public:
  //REGISTER_HANDLER_TEST(GetStyleInfo, GetStyleInfoReq, GetStyleInfoResp)
  REGISTER_HANDLER_TEST(GetStyleInfoRouteByUnit, GetStyleInfoReq, GetStyleInfoResp)
  using baseStub::baseStub;
};

class AdForwardOfflineStub : public kuaishou::ad::forward_index::kess::AdForwardIndexService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetTableData, GetTableDataRequest, GetTableDataResponse)
  using baseStub::baseStub;
};

class AdFeatureSTub : public ks::ad_feature_index::proto::kess::AdFeatureIndexService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetBatchSchemaFreeData, GetBatchSchemaFreeDataReq, GetBatchSchemaFreeDataResp)
  using baseStub::baseStub;
};

class AdFeatureRecoSTub : public ks::platform::kess::CommonRecoLeafService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(Recommend, ks::platform::CommonRecoRequest, ks::platform::CommonRecoResponse)
  using baseStub::baseStub;
};

class AdPackStub : public mix::kuaishou::ad::kess::AdPackService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetAdResult, AdPackRequest, AdPackResponse)
  using baseStub::baseStub;
};
class AdDspStub : public kuaishou::ad::dsp::kess::AdDspBaseRpcService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetDspAccountIndustryV3, AdDspAccountIndustryInfoRequest, AdDspAccountIndustryInfoResponse)
  using baseStub::baseStub;
};
class MerchantCommodityApplyProductStub
    : public kuaishou::plateco::merchant::commodity::kess::MerchantCommodityApplyProductService::Service,
      public baseStub {
public:
  REGISTER_HANDLER_TEST(GetShelfItemInfo, GetShelfItemCacheInfoRequest, GetShelfItemCacheInfoResponse)
  using baseStub::baseStub;
};
class MerchantCommodityItermPriceApplyStub
    : public kuaishou::plateco::merchant::commodity::apply::kess::MerchantCommodityItemPriceApplyService::Service,
      public baseStub {
public:
  REGISTER_HANDLER_TEST(GetItemPriceMap, GetItemPriceMapRequest, GetItemPriceMapResponse)
  using baseStub::baseStub;
};
class MerchantCommoditySalesStub
    : public kuaishou::plateco::merchant::commodity::kess::MerchantCommoditySalesService::Service,
      public baseStub {
public:
  REGISTER_HANDLER_TEST(GetItemSales, GetItemSalesRequest, GetSalesResponse)
  REGISTER_HANDLER_TEST(GetSellerSales, GetSellerSalesRequest, GetSalesResponse)
  REGISTER_HANDLER_TEST(GetItemSalesDetail, GetItemSalesDetailRequest, GetItemSalesDetailResponse)
  using baseStub::baseStub;
};
class MerchantMarketingResourceQueryStub
    : public kuaishou::plateco::merchant::marketing::mr::kess::MerchantMarketingResourceQueryService::Service,
      public baseStub {
public:
  REGISTER_HANDLER_TEST(ListMarketingResourceByBizEntityKey, ListMarketingResourceByBizEntityKeyRequest,
                        ListMarketingResourceByBizEntityKeyResponse)
  REGISTER_HANDLER_TEST(GetMarketingResource, GetMarketingResourceRequest, GetMarketingResourceResponse)
  REGISTER_HANDLER_TEST(GetMarketingResourceProgress, GetMarketingResourceProgressRequest,
                        GetMarketingResourceProgressResponse)
  REGISTER_HANDLER_TEST(GetRealtimeMarketingResourceProgress, GetMarketingResourceProgressRequest,
                        GetMarketingResourceProgressResponse)
  REGISTER_HANDLER_TEST(BatchGetMarketingResourceProgress, BatchGetMarketingResourceProgressRequest,
                        BatchGetMarketingResourceProgressResponse)
  using baseStub::baseStub;
};
class FanstopWhiteListStub : public kuaishou::fanstop::kess::FansTopWhiteListService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetPreviewWhitelist, PreviewWhitelistRequest, PreviewWhitelistResponse)
  using baseStub::baseStub;
};
class ApiCoreUserCacheStub : public kuaishou::user::cache::kess::UserCacheRpc::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetByIds, GetByIdsRequest, kuaishou::user::cache::GetByIdsResponse)
  REGISTER_HANDLER_TEST(IsValidUserId, IsValidUserIdRequest, BooleanResponse)
  REGISTER_HANDLER_TEST(ClearCache, ClearCacheRequest, VoidResponse)
  REGISTER_HANDLER_TEST(GetByIdsFailFast, GetByIdsFailFastRequest, GetByIdsFailFastResponse)
  REGISTER_HANDLER_TEST(GetSomeByIds, GetSomeByIdsRequest, GetSomeByIdsResponse)
  using baseStub::baseStub;
};
class ApiCoreUserCountStub : public kuaishou::usercount::kess::UserCountRpc::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(UpdateByCount, UpdateByCountRequest, VoidResponse)
  REGISTER_HANDLER_TEST(UpdateByDelta, UpdateByDeltaRequest, VoidResponse)
  REGISTER_HANDLER_TEST(UpdateByBit, UpdateByBitRequest, VoidResponse)
  REGISTER_HANDLER_TEST(GetByType, GetByTypeRequest, UserValueResponse)
  REGISTER_HANDLER_TEST(GetByIds, IdsParams, kuaishou::usercount::GetByIdsResponse)
  REGISTER_HANDLER_TEST(GetMostFansUsers, GetMostFansUsersRequest, UserValueResponse)
  using baseStub::baseStub;
};
class SspStub : public kuaishou::ad::ssp::kess::AdSspService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetAllPositions, kuaishou::ad::EmptyParam, kuaishou::ad::ssp::AdUniversePositionResponse)
  using baseStub::baseStub;
};
class SplashStub : public kuaishou::ad::kess::AdSplashService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(GetSplashResult, AdRequest, AdResponse)
  using baseStub::baseStub;
};

class AdAuditionServiceStub : public kuaishou::ad::kess::AdAuditionService::Service, public baseStub {
public:
  REGISTER_HANDLER_TEST(Audition, kuaishou::ad::AuditionServiceRequest, kuaishou::ad::AuditionServiceResponse)
  using baseStub::baseStub;
};

class AdI18nForwardIndexTestStub : public kuaishou::ad::i18n::forward_index::
                                          kess::AdForwardIndexService::Service, public baseStub{
public:
  REGISTER_HANDLER_TEST(GetAllCreatives, kuaishou::ad::i18n::forward_index::GetAllCreativesReq, kuaishou::ad::i18n::forward_index::GetAllCreativesResp)
  REGISTER_HANDLER_TEST(GetStyleInfo, kuaishou::ad::i18n::forward_index::GetStyleInfoReq, kuaishou::ad::i18n::forward_index::GetStyleInfoResp)
  using baseStub::baseStub;
};

class AdI18nTargetStub: public kuaishou::ad::i18n::kess::AdI18nTargetService::Service, public baseStub{
  public:
  REGISTER_HANDLER_TEST(GetAdResult, kuaishou::ad::i18n::AdTargetRequest, kuaishou::ad::i18n::AdTargetResponse)
  using baseStub::baseStub;
};

class AdI18nPredictStub : public kuaishou::ad::i18n::predict_service::kess::UniversePredictService::Service, public baseStub{
public:
  REGISTER_HANDLER_TEST(Predict, kuaishou::ad::i18n::predict_service::UniversePredictRequest, kuaishou::ad::i18n::predict_service::UniversePredictResponse)
  using baseStub::baseStub;
};

};
//注意SetGroup是rank的时候新加的,不确定老的front会不会重启放到这个group下面,所以单独写了个NEW
//同步变异步试试SetSyncThreadNum
#define REGISTER_SERVER_STUB_NEW(ServiceName, ServerName, GrpcPort, KessName)                                              \
  ServiceName ServerName(#KessName);                                                                                   \
  auto application_##ServerName = ks::framework::Application::ForGrpc(#KessName, GrpcPort)                             \
                                      .SetDivision(FLAGS_kess_type)                                                          \
                                      .SetGroup(::ks::infra::KEnv::GetKWSInfo()->GetKSN())                             \
                                      .SetLane(FLAGS_lane_id)                                                          \
                                      .SetExitWaitTime(std::chrono::seconds(FLAGS_thread_timeout), std::chrono::seconds(FLAGS_thread_timeout))         \
                                      .Build();                                                                        \
  auto server_##ServerName = application_##ServerName->GrpcServerBuilder()                                             \
                                 .SetAsyncThreadNum(FLAGS_thread_num)                                                   \
                                 .RegisterService(&ServerName)                                                         \
                                 .BuildAndStart();                                                                     \
  LOG(INFO) << #KessName << " " << #ServerName << " server started";                                                   \
  application_##ServerName->Start();

  #define REGISTER_SERVER_STUB_NEW_TWO_SERVICE(ServiceName, ServerName, ServiceName1, ServerName1, GrpcPort, KessName)                                              \
  ServiceName ServerName(#KessName);                                                                                   \
  ServiceName1 ServerName1(#KessName);                                                                                 \
  auto application_##ServerName = ks::framework::Application::ForGrpc(#KessName, GrpcPort)                             \
                                      .SetDivision(FLAGS_kess_type)                                                          \
                                      .SetGroup(::ks::infra::KEnv::GetKWSInfo()->GetKSN())                             \
                                      .SetLane(FLAGS_lane_id)                                                          \
                                      .SetExitWaitTime(std::chrono::seconds(FLAGS_thread_timeout), std::chrono::seconds(FLAGS_thread_timeout))         \
                                      .Build();                                                                        \
  auto server_##ServerName = application_##ServerName->GrpcServerBuilder()                                             \
                                 .SetAsyncThreadNum(FLAGS_thread_num)                                                   \
                                 .RegisterService(&ServerName)                                                          \
                                 .RegisterService(&ServerName1)                                                         \
                                 .BuildAndStart();                                                                     \
  LOG(INFO) << #KessName << " " << #ServerName << " server started";                                                   \
  application_##ServerName->Start();

//用于ksn和kessname同名可以注册,先用于新kessname
#define REGISTER_SERVER_STUB_NEW_V2(ServiceName, ServerName, GrpcPort, KessName)                                              \
  ServiceName ServerName(#KessName);                                                                                   \
  auto application_##ServerName = ks::framework::Application::ForGrpc(#KessName, GrpcPort)                             \
                                      .SetDivision(FLAGS_kess_type)                                                          \
                                      .SetGroup(#KessName)                             \
                                      .SetLane(FLAGS_lane_id)                                                          \
                                      .SetExitWaitTime(std::chrono::seconds(FLAGS_thread_timeout), std::chrono::seconds(FLAGS_thread_timeout))         \
                                      .Build();                                                                        \
  auto server_##ServerName = application_##ServerName->GrpcServerBuilder()                                             \
                                 .SetAsyncThreadNum(FLAGS_thread_num)                                                   \
                                 .RegisterService(&ServerName)                                                         \
                                 .BuildAndStart();                                                                     \
  LOG(INFO) << #KessName << " " << #ServerName << " server started";                                                   \
  application_##ServerName->Start();

//用于gprc的分片注册kess,比如forward
#define REGISTER_SERVER_STUB_NEW_V2_SHARD(ServiceName, ServerName, GrpcPort, KessName, ShardNum)                                              \
  ServiceName ServerName(#KessName);                                                                                   \
  auto application_##ServerName = ks::framework::Application::ForGrpc(#KessName, GrpcPort)                             \
                                      .SetDivision(FLAGS_kess_type)                                                          \
                                      .SetGroup(#KessName)                             \
                                      .SetLane(FLAGS_lane_id)                                                          \
                                      .SetShard(std::to_string(ShardNum))  \
                                      .SetExitWaitTime(std::chrono::seconds(FLAGS_thread_timeout), std::chrono::seconds(FLAGS_thread_timeout))         \
                                      .Build();                                                                        \
  auto server_##ServerName = application_##ServerName->GrpcServerBuilder()                                             \
                                 .SetAsyncThreadNum(FLAGS_thread_num)                                                   \
                                 .RegisterService(&ServerName)                                                         \
                                 .BuildAndStart();                                                                     \
  LOG(INFO) << #KessName << " " << #ServerName << " server started";                                                   \
  application_##ServerName->Start();



#define REGISTER_SERVER_STUB(ServiceName, ServerName, GrpcPort, KessName)                                              \
  ServiceName ServerName(#KessName);                                                                                   \
  auto application_##ServerName = ks::framework::Application::ForGrpc(#KessName, GrpcPort)                             \
                                      .SetDivision(FLAGS_kess_type)                                                          \
                                      .SetLane(FLAGS_lane_id)                                                          \
                                      .SetExitWaitTime(std::chrono::seconds(FLAGS_thread_timeout), std::chrono::seconds(FLAGS_thread_timeout))         \
                                      .Build();                                                                        \
  auto server_##ServerName = application_##ServerName->GrpcServerBuilder()                                             \
                                 .SetAsyncThreadNum(FLAGS_thread_num)                                                   \
                                 .RegisterService(&ServerName)                                                         \
                                 .BuildAndStart();                                                                     \
  LOG(INFO) << #KessName << " " << #ServerName << " server started";                                                   \
  application_##ServerName->Start();

//给bidword的brpc试试,否则grpc起的桩,brpc的ads调用无法请求到
//先不设置分片试试，.SetShard(FLAGS_shard_num)
#define REGISTER_SERVER_BRPC_BIDWORD_STUB(ServiceName, ServerName, GrpcPort, KessName)                                              \
  ServiceName ServerName(#KessName);                                                                                   \
  auto application_##ServerName = ks::framework::Application::ForBrpc(#KessName, GrpcPort)                             \
                                      .SetDivision(FLAGS_kess_type)                                                          \
                                      .SetLane(FLAGS_lane_id)                                                          \
                                      .SetShard(FLAGS_shard_num)  \
                                      .SetExitWaitTime(std::chrono::seconds(FLAGS_thread_timeout), std::chrono::seconds(FLAGS_thread_timeout))         \
                                      .Build();                                                                        \
  auto server_##ServerName = application_##ServerName->Apply([&ServerName]() {  \
       auto builder = ks::kess::rpc::brpc::BrpcServerBuilder();  \
       auto& options = builder.Options();  \
       options.num_threads = FLAGS_thread_num; \
       return builder.AddService(&ServerName).BuildAndStart();  \
  }); \
  LOG(INFO) << #KessName << " " << #ServerName << " brpc bidword server before start";                                                   \
  application_##ServerName->Start();  \
  LOG(INFO) << #KessName << " " << #ServerName << " brpc bidword server started";                                                   \

#define CLOSE_SERVER_STUB(ServerName)                                                                                  \
  application_##ServerName->Shutdown();                                                                                \
  application_##ServerName->WaitForExit();                                                                             \
  server_##ServerName->Shutdown(); \
  LOG(INFO) << #ServerName << " server shutdown";         
  
#define CLOSE_SERVER_BRPC_STUB(ServerName)                                                                                  \
  server_##ServerName->Stop();                                                                                \
  server_##ServerName->Join();                                                                             \
  server_##ServerName.reset();                                                                             \
  LOG(INFO) << #ServerName << " server shutdown";                                                   \



int main(int argc, char **argv) {
  base::InitApp(&argc, &argv, "engine flow copy server");
  FLAGS_log_dir = "../log/";
  using namespace ad::test::stub;
  ad::test::stub::AdWebService web_service(FLAGS_web_thread_num);
  net::WebServer::Options web_server_option;
  web_server_option.port = FLAGS_web_server_port;
  web_server_option.backlog = 1024;
  net::WebServer web_server(web_server_option, &web_service);
  web_server.Start();
  LOG(INFO) << "web server started";
  //gflag控制单独起rank的桩,泳道也是ad_diff_rank
  //多模块支持的只能放前面，因为还得同步打开is_support_adrank_stub等，放后面会冲突
  if (FLAGS_is_support_multi_stub && FLAGS_is_support_adrank_stub){
    //支持rank的diff的桩
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsDspNNRouterKsnServerStub, 20670, ad-predict-router-dsp)
    REGISTER_SERVER_STUB_NEW(AdPostServiceStub, adPostServiceKsnStub, 20672, ad-post-server)
    REGISTER_SERVER_STUB_NEW(AdxDcxrServiceStub, adxDcxrServiceStub, 20677, grpc_adxDcxrService)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsSearchRouterServerStub, 22123, grpc_adPsSearchRouterServer)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsSearchRouterServerKsnStub, 22134, ad-predict-router-dsp-search)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsFanstopRouterKsnServerStub, 22133, ad-predict-router-dsp-ic)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, adPsRouterKsnServiceStub, 22224, ad-predict-router)
    REGISTER_SERVER_STUB_NEW(AdModleCalibrateStub, adModleCalibrateServiceStub, 22225, ad-model-calibrate-server)
    REGISTER_SERVER_STUB_NEW_TWO_SERVICE(AdFeatureSTub, adFeatureProxyServiceStub, AdFeatureRecoSTub, adFeatureProxyRecoServiceStub, 22226, ad-feature-proxy)
    REGISTER_SERVER_STUB_NEW(AdStyleServerStub, adStyleServerStub, 22227, ad-style-server)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adPxtrCalcRankFreqStub, 22228, grpc_adPxtrCalcRankFreq)
    REGISTER_SERVER_STUB_NEW(AdDragonPidServiceStub, AdDragonPidServiceKsnStub, 22229, ad-dragon-pid-service)

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "Kess instance stoped";

    CLOSE_SERVER_STUB(AdPsDspNNRouterKsnServerStub)
    CLOSE_SERVER_STUB(adPostServiceKsnStub)
    CLOSE_SERVER_STUB(adxDcxrServiceStub)
    CLOSE_SERVER_STUB(AdPsSearchRouterServerStub)
    CLOSE_SERVER_STUB(AdPsSearchRouterServerKsnStub)
    CLOSE_SERVER_STUB(AdPsFanstopRouterKsnServerStub)
    CLOSE_SERVER_STUB(adPsRouterKsnServiceStub)
    CLOSE_SERVER_STUB(adModleCalibrateServiceStub)
    CLOSE_SERVER_STUB(adFeatureProxyServiceStub)
    CLOSE_SERVER_STUB(adStyleServerStub)
    CLOSE_SERVER_STUB(adPxtrCalcRankFreqStub)
    CLOSE_SERVER_STUB(AdDragonPidServiceKsnStub)
  } else if (FLAGS_is_support_multi_stub && FLAGS_is_support_adserver_stub){
    //支持adserver的桩
    REGISTER_SERVER_STUB(AdRiskControlServerStub, adRiskControlServiceStub, 25013, grpc_adRiskControlRpcService)
    REGISTER_SERVER_STUB(AdStyleServerStub, adStyleServerUniverseStub, 25015, ad-style-server-universe)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adTargetStubKsn, 26016, ad-target-server)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adSplashTargetStubKsn, 26018, ad-target-server-splash)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adUniverseTargetStubKsn, 26019, ad-target-server-universe)
    REGISTER_SERVER_STUB_NEW(AdTargetStubDragon, adUniverseTinyTargetStubKsn, 25010, ad-target-server-universe-tiny)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adFanstopTargetStubKsn, 26020, ad-target-server-fanstop)
    REGISTER_SERVER_STUB_NEW(AdTargetStubDragon, adArchimedesTargetStubKsn, 25124, ad-target-server-archimedes)
    REGISTER_SERVER_STUB_NEW(AdTargetStubDragon, adMatchServerStubKsn, 26021, ad-search-match-server)
    REGISTER_SERVER_STUB_NEW(AdMerchantDspServerStubDragon, adMerchantDspServiceNewStubKsn, 26124, ad-target-server-merchant)
    REGISTER_SERVER_STUB_NEW(AdMerchantDspServerStubDragon, adMerchantDspLiveServiceNewStubKsn, 26125, ad-target-server-merchant-live)
    REGISTER_SERVER_STUB_NEW(AdAdxServerStub, adAdxServiceStub, 25027, grpc_adxService)
    REGISTER_SERVER_STUB_NEW(AdAdxServerStub, adAdxTrackServiceStub, 25028, grpc_adxTrackService)
    REGISTER_SERVER_STUB_NEW(AdPicassoGatewayService, AdPicassoGatewayServiceStub, 25029, grpc_adPicassoGatewayService)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adTargetInnerStubKsn, 26017, ad-target-server-inner-photo)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonPrerankServerOuterKsnStub, 26018, ad-dragon-prerank-server-outer)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonPrerankServerKsnStub, 26019, ad-dragon-prerank-server)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchInnerStubKsn0, 26023, ad-searcher-inner-photo, 0)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchInnerStubKsn1, 26037, ad-searcher-inner-photo, 1)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchStubKsn0, 26036, ad-searcher, 0)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchStubKsn1, 26038, ad-searcher, 1)

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "Kess instance stoped";

    CLOSE_SERVER_STUB(adRiskControlServiceStub)
    CLOSE_SERVER_STUB(adStyleServerUniverseStub)
    CLOSE_SERVER_STUB(adTargetStubKsn)
    CLOSE_SERVER_STUB(adSplashTargetStubKsn)
    CLOSE_SERVER_STUB(adUniverseTargetStubKsn)
    CLOSE_SERVER_STUB(adUniverseTinyTargetStubKsn)
    CLOSE_SERVER_STUB(adFanstopTargetStubKsn)
    CLOSE_SERVER_STUB(adArchimedesTargetStubKsn)
    CLOSE_SERVER_STUB(adMatchServerStubKsn)
    CLOSE_SERVER_STUB(adMerchantDspServiceNewStubKsn)
    CLOSE_SERVER_STUB(adMerchantDspLiveServiceNewStubKsn)
    CLOSE_SERVER_STUB(adAdxServiceStub)
    CLOSE_SERVER_STUB(adAdxTrackServiceStub)
    CLOSE_SERVER_STUB(AdPicassoGatewayServiceStub)
    CLOSE_SERVER_STUB(adTargetInnerStubKsn)
    CLOSE_SERVER_STUB(adDragonPrerankServerOuterKsnStub)
    CLOSE_SERVER_STUB(adDragonPrerankServerKsnStub)
    CLOSE_SERVER_STUB(adSearchInnerStubKsn0)
    CLOSE_SERVER_STUB(adSearchInnerStubKsn1)
    CLOSE_SERVER_STUB(adSearchStubKsn0)
    CLOSE_SERVER_STUB(adSearchStubKsn1)
  } else if (FLAGS_is_support_multi_stub && FLAGS_is_support_adfront_stub){
    REGISTER_SERVER_STUB(AdRankStubDragon, adRankStubKsn, 20671, ad-rank-server)
    REGISTER_SERVER_STUB(AdBrandStub, brandStubV2, 21001, ad-brand)
    REGISTER_SERVER_STUB(AdBrandStub, brandUniverseStubV2, 21000, ad-brand-universe)
    REGISTER_SERVER_STUB(AdBrandStub, brandPinPaiStubV2, 22000, ad-brand-pinpai)
    REGISTER_SERVER_STUB(AdBrandStub, brandSearchStubV2, 23000, ad-brand-search)
    REGISTER_SERVER_STUB(AdBrandStub, brandContentStubV2, 21002, ad-brand-content)
    REGISTER_SERVER_STUB(AdDspStub, dspStub, 20731, grpc_adDspBaseRpcService)
    REGISTER_SERVER_STUB(AdForwardSTub, forwardStubKsn, 22003, ad-forward-index)
    REGISTER_SERVER_STUB_NEW_V2(AdStyleMaterialSTub, styleMaterialStubKsn, 21082, ad-forward-style-material-index)
    REGISTER_SERVER_STUB(AdPackStub, adPackStubKsn, 22011, ad-pack-server)
    REGISTER_SERVER_STUB(AdPackStub, adPackUniverseStubKsn, 22012, ad-pack-server-universe)
    REGISTER_SERVER_STUB_NEW_V2(AdPackStub, adPackLongTailStubKsn, 22013, ad-pack-server-long-tail-flow)
    REGISTER_SERVER_STUB(RtaServerStub, adRtaServerStub, 20091, grpc_rtaService)
    REGISTER_SERVER_STUB(AdSocialRecoFollowStub, adSocialStub, 20061, grpc_adSocialRecoFollowService)
    REGISTER_SERVER_STUB(AdUserProfileStub, adUserProfileKsnStub, 21004, ad-user-profile)
    REGISTER_SERVER_STUB_NEW_V2(AdUserProfileStub, adUserProfileKnewsKsnStub, 21005, ad-user-profile-knews)
    REGISTER_SERVER_STUB(AdUserProfileStub, adUserProfileUniverseKsnStub, 21006, ad-user-profile-universev2)
    REGISTER_SERVER_STUB(ApiCoreUserCacheStub, apiCoreUserCacheStub, 20197, grpc_apiCoreUserCacheService)
    REGISTER_SERVER_STUB(ApiCoreUserCountStub, apiCoreUserCountStub, 20223, grpc_apiCoreUserCountService)
    REGISTER_SERVER_STUB(FanstopWhiteListStub, fanstopWhiteListStub, 20852, grpc_fansTopWhiteListService)
    REGISTER_SERVER_STUB(SspStub, sspStub, 20703, grpc_sspService)
    REGISTER_SERVER_STUB_NEW(AdStyleServerStub, adStyleServerSplashStub, 20389, ad-style-server-splash)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, adPsRouterKsnFrontServiceStub, 20663, ad-predict-router)
    REGISTER_SERVER_STUB_NEW(AdPsRouterPrepareStub, adPsRouterPrepareKsnFrontServiceStub, 20665, ad-predict-router-universe-tiny)
    REGISTER_SERVER_STUB_NEW(AdPsRouterPrepareStub, adTwinTowerPrepareKsnFrontServiceStub, 20666, ad-twin-towers-router-universe)
    REGISTER_SERVER_STUB_NEW(AdPsRouterPrepareStub, adTwinTowerPrepareKsnFrontServiceTinyStub, 20667, ad-twin-towers-router-universe-tiny)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adAnnRouterServiceStub, 20664, ad-ann-router-universe)
    REGISTER_SERVER_STUB_NEW(AdPostServicePidStub, adPostServicePidKsnStub, 20669, ad-post-server-pid)
    REGISTER_SERVER_STUB_NEW(AdKwaiShopSelectionStub, adKwaiShopSelectionStub, 20668, kwaishop-selection-user-service)

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "Kess instance stoped";

    CLOSE_SERVER_STUB(adRankStubKsn)
    CLOSE_SERVER_STUB(brandStubV2)
    CLOSE_SERVER_STUB(brandUniverseStubV2)
    CLOSE_SERVER_STUB(brandPinPaiStubV2)
    CLOSE_SERVER_STUB(brandSearchStubV2)
    CLOSE_SERVER_STUB(brandContentStubV2)
    CLOSE_SERVER_STUB(dspStub)
    CLOSE_SERVER_STUB(forwardStubKsn)
    CLOSE_SERVER_STUB(styleMaterialStubKsn)
    CLOSE_SERVER_STUB(adPackStubKsn)
    CLOSE_SERVER_STUB(adPackUniverseStubKsn)
    CLOSE_SERVER_STUB(adPackLongTailStubKsn)
    CLOSE_SERVER_STUB(adRtaServerStub)
    CLOSE_SERVER_STUB(adSocialStub)
    CLOSE_SERVER_STUB(adUserProfileKsnStub)
    CLOSE_SERVER_STUB(adUserProfileKnewsKsnStub)
    CLOSE_SERVER_STUB(adUserProfileUniverseKsnStub)
    CLOSE_SERVER_STUB(apiCoreUserCacheStub)
    CLOSE_SERVER_STUB(apiCoreUserCountStub)
    CLOSE_SERVER_STUB(fanstopWhiteListStub)
    CLOSE_SERVER_STUB(sspStub)
    CLOSE_SERVER_STUB(adStyleServerSplashStub)
    CLOSE_SERVER_STUB(adPsRouterKsnFrontServiceStub)
    CLOSE_SERVER_STUB(adPsRouterPrepareKsnFrontServiceStub)
    CLOSE_SERVER_STUB(adTwinTowerPrepareKsnFrontServiceStub)
    CLOSE_SERVER_STUB(adTwinTowerPrepareKsnFrontServiceTinyStub)
    CLOSE_SERVER_STUB(adAnnRouterServiceStub)
    CLOSE_SERVER_STUB(adPostServicePidKsnStub)
    CLOSE_SERVER_STUB(adKwaiShopSelectionStub)
  } else if (FLAGS_is_support_adrank_stub) { 
    //支持rank的diff的桩
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsDspNNRouterServerStub, 20901, grpc_adPsDspNNRouterServer)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsDspNNRouterKsnServerStub, 21901, ad-predict-router-dsp)
    REGISTER_SERVER_STUB_NEW(AdPostServiceStub, adPostServiceStub, 22001, grpc_AdPostService)
    REGISTER_SERVER_STUB_NEW(AdPostServiceStub, adPostServiceKsnStub, 22103, ad-post-server)
    REGISTER_SERVER_STUB_NEW(AdPostServicePidStub, adPostServicePidStub, 22112, grpc_AdPostService_pid)
    REGISTER_SERVER_STUB_NEW(AdPostServicePidStub, adPostServicePidKsnStub, 22002, ad-post-server-pid)

    REGISTER_SERVER_STUB_NEW(AdStyleServiceStub, adStyleServiceStub, 22101, grpc_adStyleService)
    REGISTER_SERVER_STUB_NEW(AdUeqServiceStub, adUeqServiceStub, 22110, grpc_adUeqService)
    REGISTER_SERVER_STUB_NEW(AdxDcxrServiceStub, adxDcxrServiceStub, 22120, grpc_adxDcxrService)
    REGISTER_SERVER_STUB_NEW(AdFanstopCommonLeafStub, adInnerFanstopCommonLeafStub, 22121, grpc_ProIncInnerFanstop_RankCommonLeaf)
    //REGISTER_SERVER_STUB_NEW(AdFcServerV2Stub, adFCServiceV2Stub, 22122, grpc_adFcService_v2)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsSearchRouterServerStub, 22123, grpc_adPsSearchRouterServer)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsSearchRouterServerKsnStub, 22134, ad-predict-router-dsp-search)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsFanstopRouterKsnServerStub, 22133, ad-predict-router-dsp-ic)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, adPsRouterServiceStub, 22124, grpc_adPsRouterServer)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, adPsRouterKsnServiceStub, 22224, ad-predict-router)
    REGISTER_SERVER_STUB_NEW(AdModleCalibrateStub, adModleCalibrateServiceStub, 22225, ad-model-calibrate-server)
    REGISTER_SERVER_STUB_NEW_TWO_SERVICE(AdFeatureSTub, adFeatureProxyServiceStub, AdFeatureRecoSTub, adFeatureProxyRecoServiceStub, 22226, ad-feature-proxy)
	  REGISTER_SERVER_STUB_NEW(AdStyleServerStub, adStyleServerStub, 22227, ad-style-server)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adPxtrCalcRankFreqStub, 22228, grpc_adPxtrCalcRankFreq)
    REGISTER_SERVER_STUB_NEW(AdDragonPidServiceStub, AdDragonPidServiceKsnStub, 22229, ad-dragon-pid-service)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, adRouterPredictStub, 22230, grpc_ad_router_predict_stub)
    REGISTER_SERVER_STUB_NEW(AdPsRouterDragonStub, adRouterDragonPredicStub, 22231, grpc_ad_router_dragon_predict_stub)

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "Kess instance stoped";
    CLOSE_SERVER_STUB(AdPsDspNNRouterServerStub)
    CLOSE_SERVER_STUB(AdPsDspNNRouterKsnServerStub)
    CLOSE_SERVER_STUB(adPostServiceStub)
    CLOSE_SERVER_STUB(adPostServiceKsnStub)
    CLOSE_SERVER_STUB(adPostServicePidStub)
    CLOSE_SERVER_STUB(adPostServicePidKsnStub)
    CLOSE_SERVER_STUB(adStyleServiceStub)
    CLOSE_SERVER_STUB(adUeqServiceStub)
    CLOSE_SERVER_STUB(adxDcxrServiceStub)
    CLOSE_SERVER_STUB(adInnerFanstopCommonLeafStub)
    //CLOSE_SERVER_STUB(adFCServiceV2Stub)
    CLOSE_SERVER_STUB(AdPsSearchRouterServerStub)
    CLOSE_SERVER_STUB(AdPsSearchRouterServerKsnStub)
    CLOSE_SERVER_STUB(AdPsFanstopRouterKsnServerStub)
    CLOSE_SERVER_STUB(adPsRouterServiceStub)
    CLOSE_SERVER_STUB(adPsRouterKsnServiceStub)
    CLOSE_SERVER_STUB(adModleCalibrateServiceStub)
    CLOSE_SERVER_STUB(adFeatureProxyServiceStub)
    CLOSE_SERVER_STUB(adStyleServerStub)
    CLOSE_SERVER_STUB(adPxtrCalcRankFreqStub)
    CLOSE_SERVER_STUB(AdDragonPidServiceKsnStub)
    CLOSE_SERVER_STUB(adRouterPredictStub)
    CLOSE_SERVER_STUB(adRouterDragonPredicStub)
  }else if (FLAGS_is_support_adtarget_stub) {
    //支持target的桩
    REGISTER_SERVER_STUB_NEW(AdPostServiceStub, adPostServiceStub, 24001, grpc_AdPostService_pid)
    REGISTER_SERVER_STUB_NEW(AdPostServiceStub, adPostPidServiceKsnStub, 24101, ad-post-server-pid)
    REGISTER_SERVER_STUB_NEW(AdBidServiceStub, adBidServiceKsnStub, 24102, ad-bid-service)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonBidServiceKsnStub, 24004, ad-dragon-bid-service)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonBidServiceUniverseKsnStub, 24204, ad-dragon-bid-service-universe)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonBidServiceDiffKsnStub, 24121, ad-dragon-bid-service_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonBidServiceUniverseDiffKsnStub, 24221, ad-dragon-bid-service-universe_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonPrerankServerKsnStub, 24002, ad-dragon-prerank-server)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonPrerankServerDiffKsnStub, 24106, ad-dragon-prerank-server_diff_stub)

    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonPrerankServerOuterKsnStub, 24222, ad-dragon-prerank-server-outer)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonPrerankServerOuterDiffKsnStub, 24223, ad-dragon-prerank-server-outer_diff_stub)

    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterServiceStub, 24003, grpc_adEmbeddingRetrRouterServer)
    REGISTER_SERVER_STUB_NEW(AdPicassoGatewayService, AdPicassoGatewayServiceStub, 24005, grpc_adPicassoGatewayService)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsDspNNRouterServerStub, 24006, grpc_adPsDspNNRouterServer)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsDspRetrievalRouterServerStub, 24007, grpc_adPsDspRetrievalRouterServer_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsDspRetrievalKsnRouterServerStub, 24107, ad-predict-router-dsp-retrieval_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, AdTagRetrivevServerStub, 24008, grpc_adTagRetrieveServer_offline)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, AdTagRetrivevServerKsnStub, 24108, ad-tag-retrieve-offline)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTdmRouterServerStub, 24009, grpc_adTdmRouterServer)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTdmRouterServerDiffStub, 24109, grpc_adTdmRouterServer_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTwinTowersRouterServerStub, 24010, ad-twin-towers-router_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTwinTowersRouterServerKsnStub, 24110, ad-twin-towers-router)
    REGISTER_SERVER_STUB_NEW(AdBidServiceStub, adUniverseBidServiceStub, 24011, grpc_adBidService_universe)
    REGISTER_SERVER_STUB_NEW(AdBidServiceStub, adUniverseBidServiceKsnStub, 24111, ad-bid-service-universe)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterFanstopServiceStub, 24012, grpc_adEmbeddingRetrRouterServer_fanstop)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterFanstopServiceKsnStub, 24112, ad-ann-router-fanstop)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsFanstopRouterServerStub, 24013, grpc_adPsFanstopRouterServer)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPDNRouterServerStub, 24014, grpc_adPDNRouterServer)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsIcRetrievalRouterServerStub, 24015, grpc_adPsIcRetrievalRouterServer_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsIcRetrievalKsnRouterServerStub, 24115, ad-predict-router-ic-retrieval_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdFanstopCommonLeafStub, adInnerRandomRetrievalServerStub, 24016, grpc_innerRandomRetrService)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, adFanstopLiveRecallPredictServerStub, 24017, grpc_ad_fanstop_retrieval_fanstop_live_tag_recall_predict_server)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, adFanstopPhotoRecallPredictServerStub, 24018, grpc_ad_fanstop_retrieval_fanstop_photo_tag_recall_predict_server)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterThanosServiceStub, 24019, grpc_adEmbeddingRetrRouterServer_thanos)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterThanosServiceKsnStub, 24119, ad-ann-router-thanos)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterServiceKsnStub, 24219, ad-ann-router)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTwinTowersRouterThanosServerStub, 24020, ad-twin-towers-router-thanos_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTwinTowersRouterThanosServerKsnStub, 24120, ad-twin-towers-router-thanos)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPcreativeSelectServerStub, 24021, grpc_pcreative_select_ht_twi_del_ad_audition_svr)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTwinTowersRouterUniverseServerStub, 24022, ad-twin-towers-router-universe_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTwinTowersRouterUniverseServerKsnStub, 24122, ad-twin-towers-router-universe)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsUniverseRouterServerStub, 24023, grpc_adPsUniverseRouterServer_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsUniverseKsnRouterServerStub, 24123, ad-predict-router-universe_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterSearchServiceStub, 24024, grpc_adEmbeddingRetrRouterServer_search)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, adRetrServerStub, 24025, grpc_adRetrService)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, adRetrExpServerStub, 24026, grpc_adRetrService_exp)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, adRetrFanstopServerStub, 24027, grpc_adRetrService_fanstop)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, adRetrPhotoSkuServerStub, 24034, grpc_adRetrService_photo_sku)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, AdTagRetrivevSearchServerStub, 24028, grpc_adTagRetrieveServer_search)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, AdQ2u2uDefaultServerStub, 24036, grpc_search_default_q2u2u_embedding_retr)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, AdSkuRetrievalServerStub, 24029, grpc_skuRetrievalService)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, AdSkuRetrievalFilterServerStub, 24030, grpc_skuRetrievalService_filter)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdDspRetrievalLiveRoomServerStub, 24031, grpc_ad_dsp_retrieval_live_room_wsy_test_predict_server)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdEmbeddingDeliveryLtrServerStub, 24032, grpc_ad_embedding_delivery_ltr_32_lk_select_v2_user_predict_server)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterUniverseServiceStub, 24033, grpc_adEmbeddingRetrRouterServer_universe)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterUniverseServiceKsnStub, 24133, ad-ann-router-universe)
    REGISTER_SERVER_STUB_NEW(AdSearchRelevanceKvStub, AdSearchRelevanceKvServiceStub, 24035, grpc_adSearchRelevanceKVService)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTwinTowersRouterUniverseTinyServerStub, 24037, ad-twin-towers-router-universe-tiny_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdTwinTowersRouterUniverseTinyServerKsnStub, 24137, ad-twin-towers-router-universe-tiny)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, GrpcAdDspRetrievalTargetCxaAuditionLtrSsbKlearnUserV10PredictServer, 24138, grpc_ad_dsp_retrieval_target_cxa_audition_ltr_ssb_klearn_user_v1_0_predict_server)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, GrpcAdDspRetrievalTargetYcyAuditionLtrNewUserV6PredictServer, 24139, grpc_ad_dsp_retrieval_target_ycy_audition_ltr_new_user_v6_predict_server)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, GrpcAdDspRetrievalLivePreselect4UserV3PredictServer, 24140, grpc_ad_dsp_retrieval_live_preselect_4_user_v3_predict_server)
    REGISTER_SERVER_STUB(AdTargetStubDragon, AdTriggerServer, 24141, ad-trigger-server)
    REGISTER_SERVER_STUB_NEW(AdAuditionServiceStub, grpcTargetCxaAuditionLtrSsbKlearnUserV10AdAuditionSvr, 24142, grpc_target_cxa_audition_ltr_ssb_klearn_user_v1_0_ad_audition_svr)
    REGISTER_SERVER_STUB_NEW(AdAuditionServiceStub, grpcLivePreselect4UserV3AdAuditionSvr, 24143, grpc_live_preselect_4_user_v3_ad_audition_svr)
    //REGISTER_SERVER_STUB_NEW(AdRtaClientServiceStub, AdRtaClientServerStub, 24011, grpc_ad_rta_client)
    //REGISTER_SERVER_STUB_NEW(AdRetrBruteGpuEmbServiceStub, AdRetrBruteGpuEmbServerStub, 24012, grpc_retr_brute_gpu_emb_client)

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "Kess instance stoped";
    CLOSE_SERVER_STUB(adPostServiceStub)
    CLOSE_SERVER_STUB(adPostPidServiceKsnStub)
    CLOSE_SERVER_STUB(adBidServiceKsnStub)
    CLOSE_SERVER_STUB(adDragonBidServiceKsnStub)
    CLOSE_SERVER_STUB(adDragonBidServiceUniverseKsnStub)
    CLOSE_SERVER_STUB(adDragonBidServiceDiffKsnStub)
    CLOSE_SERVER_STUB(adDragonBidServiceUniverseDiffKsnStub)
    CLOSE_SERVER_STUB(adDragonPrerankServerKsnStub)
    CLOSE_SERVER_STUB(adDragonPrerankServerOuterKsnStub)
    CLOSE_SERVER_STUB(adDragonPrerankServerDiffKsnStub)
    CLOSE_SERVER_STUB(adDragonPrerankServerOuterDiffKsnStub)
    CLOSE_SERVER_STUB(adUniverseBidServiceStub)
    CLOSE_SERVER_STUB(adUniverseBidServiceKsnStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterServiceStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterFanstopServiceStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterFanstopServiceKsnStub)
    CLOSE_SERVER_STUB(AdPicassoGatewayServiceStub)
    CLOSE_SERVER_STUB(AdPsDspNNRouterServerStub)
    CLOSE_SERVER_STUB(AdPsDspRetrievalRouterServerStub)
    CLOSE_SERVER_STUB(AdPsDspRetrievalKsnRouterServerStub)
    CLOSE_SERVER_STUB(AdTagRetrivevServerStub)
    CLOSE_SERVER_STUB(AdTagRetrivevServerKsnStub)
    CLOSE_SERVER_STUB(AdTdmRouterServerStub)
    CLOSE_SERVER_STUB(AdTdmRouterServerDiffStub)
    CLOSE_SERVER_STUB(AdTwinTowersRouterServerStub)
    CLOSE_SERVER_STUB(AdTwinTowersRouterServerKsnStub)
    CLOSE_SERVER_STUB(AdPDNRouterServerStub)
    CLOSE_SERVER_STUB(AdPsIcRetrievalRouterServerStub)
    CLOSE_SERVER_STUB(AdPsIcRetrievalKsnRouterServerStub)
    CLOSE_SERVER_STUB(adInnerRandomRetrievalServerStub)
    CLOSE_SERVER_STUB(adFanstopLiveRecallPredictServerStub)
    CLOSE_SERVER_STUB(adFanstopPhotoRecallPredictServerStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterThanosServiceStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterThanosServiceKsnStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterServiceKsnStub)
    CLOSE_SERVER_STUB(AdTwinTowersRouterThanosServerStub)
    CLOSE_SERVER_STUB(AdTwinTowersRouterThanosServerKsnStub)
    CLOSE_SERVER_STUB(AdPcreativeSelectServerStub)
    CLOSE_SERVER_STUB(AdTwinTowersRouterUniverseServerStub)
    CLOSE_SERVER_STUB(AdTwinTowersRouterUniverseServerKsnStub)
    CLOSE_SERVER_STUB(AdTwinTowersRouterUniverseTinyServerStub)
    CLOSE_SERVER_STUB(AdTwinTowersRouterUniverseTinyServerKsnStub)
    CLOSE_SERVER_STUB(AdPsUniverseRouterServerStub)
    CLOSE_SERVER_STUB(AdPsUniverseKsnRouterServerStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterSearchServiceStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterUniverseServiceStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterUniverseServiceKsnStub)
    CLOSE_SERVER_STUB(adRetrServerStub)
    CLOSE_SERVER_STUB(adRetrExpServerStub)
    CLOSE_SERVER_STUB(adRetrFanstopServerStub)
    CLOSE_SERVER_STUB(adRetrPhotoSkuServerStub)
    CLOSE_SERVER_STUB(AdTagRetrivevSearchServerStub)
    CLOSE_SERVER_STUB(AdQ2u2uDefaultServerStub)
    CLOSE_SERVER_STUB(AdSkuRetrievalServerStub)
    CLOSE_SERVER_STUB(AdSkuRetrievalFilterServerStub)
    CLOSE_SERVER_STUB(AdDspRetrievalLiveRoomServerStub)
    CLOSE_SERVER_STUB(AdEmbeddingDeliveryLtrServerStub)
    CLOSE_SERVER_STUB(AdSearchRelevanceKvServiceStub)
    CLOSE_SERVER_STUB(GrpcAdDspRetrievalTargetCxaAuditionLtrSsbKlearnUserV10PredictServer)
    CLOSE_SERVER_STUB(GrpcAdDspRetrievalTargetYcyAuditionLtrNewUserV6PredictServer)
    CLOSE_SERVER_STUB(GrpcAdDspRetrievalLivePreselect4UserV3PredictServer)
    CLOSE_SERVER_STUB(AdTriggerServer)
    CLOSE_SERVER_STUB(grpcTargetCxaAuditionLtrSsbKlearnUserV10AdAuditionSvr)
    CLOSE_SERVER_STUB(grpcLivePreselect4UserV3AdAuditionSvr)
    //CLOSE_SERVER_STUB(AdRtaClientServerStub)
    //CLOSE_SERVER_STUB(AdRetrBruteGpuEmbServerStub)
  }else if (FLAGS_is_support_adserver_stub) {
    //REGISTER_SERVER_STUB_NEW(AdPostServiceStub, adPostServiceStub, 25001, grpc_AdPostService)
    REGISTER_SERVER_STUB_NEW(AdBidServiceStub, adBidServiceStub, 25002, grpc_adBidService)
    REGISTER_SERVER_STUB_NEW(AdDpaServiceStub, adDpaServiceStub, 25003, grpc_adDpaService)
    REGISTER_SERVER_STUB_NEW(AdDpaServiceStub, adDpaUniverseServiceStub, 25004, grpc_adDpaService_universe)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, adPsRouterServiceStub, 25005, grpc_adPsRouterServer)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adEmbeddingRetrRouterUniverseServiceStub, 25006, grpc_adEmbeddingRetrRouterServer_universe)
    REGISTER_SERVER_STUB(AdRankStubDragon, adRankStubKsn, 26007, ad-rank-server)
    REGISTER_SERVER_STUB(AdRankStubDragon, adUniverseRankStubKsn, 26009, ad-rank-server-universe)
    REGISTER_SERVER_STUB_NEW(AdRankStubDragon, adUniverseTinyRankStubKsn, 26010, ad-rank-server-universe-tiny)
    //REGISTER_SERVER_STUB(AdRankStub, adFanstopRankStubKsn, 26010, ad-rank-server-fanstop)
    REGISTER_SERVER_STUB(AdRankStubDragon, adSearchRankStubKsn, 26011, ad-rank-server-search)
    REGISTER_SERVER_STUB(AdRankStubDragon, adSplashRankStubKsn, 26012, ad-rank-server-splash)
    REGISTER_SERVER_STUB_NEW(AdRankStubDragon, adArchimedesRankStubKsn, 250031, ad-rank-server-archimedes)
    REGISTER_SERVER_STUB(AdRiskControlServerStub, adRiskControlServiceStub, 25013, grpc_adRiskControlRpcService)
    //REGISTER_SERVER_STUB(AdRiskControlServerStub, adRiskControlServiceTestStub, 25010, grpc_adRiskControlRpcServiceTest)
    REGISTER_SERVER_STUB(AdStyleServerStub, adStyleServerDefaultStub, 25014, grpc_adStyleServer)
    REGISTER_SERVER_STUB(AdStyleServerStub, adStyleServerUniverseStub, 25015, grpc_adStyleServer_universe)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adTargetStubKsn, 26016, ad-target-server)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adSplashTargetStubKsn, 26018, ad-target-server-splash)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adUniverseTargetStubKsn, 26019, ad-target-server-universe)
    REGISTER_SERVER_STUB_NEW(AdTargetStubDragon, adUniverseTinyTargetStubKsn, 25032, ad-target-server-universe-tiny)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adFanstopTargetStubKsn, 26020, ad-target-server-fanstop)
    REGISTER_SERVER_STUB_NEW(AdTargetStubDragon, adArchimedesTargetStubKsn, 25124, ad-target-server-archimedes)
    REGISTER_SERVER_STUB_NEW(AdTargetStubDragon, adMatchServerStubKsn, 26021, ad-search-match-server)
    REGISTER_SERVER_STUB(AdTargetStub, adSearchTargetStub, 25022, grpc_adTargetSearchService)
    REGISTER_SERVER_STUB(AdTargetStub, adSearchTargetStubKsn, 26035, ad-target-search-default)
    REGISTER_SERVER_STUB(AdTargetStub, adSearchTargetNewStubKsn, 26122, ad-target-server-search)
    REGISTER_SERVER_STUB(AdTargetStub, adBidWordSearchStub, 25023, grpc_bidwordSearchService)
    REGISTER_SERVER_STUB_NEW(AdMerchantDspServerStub, adMerchantDspServiceStubKsn, 26024, ad-merchant-dsp)
    REGISTER_SERVER_STUB_NEW(AdMerchantDspServerStubDragon, adMerchantDspServiceNewStubKsn, 26124, ad-target-server-merchant)
    REGISTER_SERVER_STUB_NEW(AdMerchantDspServerStub, adMerchantDspLiveServiceStubKsn, 26025, ad-merchant-dsp-live)
    REGISTER_SERVER_STUB_NEW(AdMerchantDspServerStubDragon, adMerchantDspLiveServiceNewStubKsn, 26125, ad-target-server-merchant-live)
    REGISTER_SERVER_STUB_NEW(AdAdxServerStub, adAdxServiceStub, 25027, grpc_adxService)
    REGISTER_SERVER_STUB_NEW(AdAdxServerStub, adAdxTrackServiceStub, 25028, grpc_adxTrackService)
    REGISTER_SERVER_STUB_NEW(AdPicassoGatewayService, AdPicassoGatewayServiceStub, 25037, grpc_adPicassoGatewayService)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adTargetInnerStubKsn, 26017, ad-target-server-inner-photo)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonPrerankServerOuterKsnStub, 26033, ad-dragon-prerank-server-outer)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adDragonPrerankServerKsnStub, 26034, ad-dragon-prerank-server)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchInnerStubKsn0, 26023, ad-searcher-inner-photo, 0)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchInnerStubKsn1, 26037, ad-searcher-inner-photo, 1)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchStubKsn0, 26036, ad-searcher, 0)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchStubKsn1, 26038, ad-searcher, 1)
    //一定放到最后
    REGISTER_SERVER_BRPC_BIDWORD_STUB(AdTargetBrpcServiceImpl, adBidWordSearchNewStub, 25123, ad-target-server-search-bidword)
    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "Kess instance stoped"; 
    //CLOSE_SERVER_STUB(adPostServiceStub)
    CLOSE_SERVER_STUB(adBidServiceStub)
    CLOSE_SERVER_STUB(adDpaServiceStub)
    CLOSE_SERVER_STUB(adDpaUniverseServiceStub)
    CLOSE_SERVER_STUB(adPsRouterServiceStub)
    CLOSE_SERVER_STUB(adEmbeddingRetrRouterUniverseServiceStub)
    CLOSE_SERVER_STUB(adRankStubKsn)
    CLOSE_SERVER_STUB(adUniverseRankStubKsn)
    CLOSE_SERVER_STUB(adUniverseTinyRankStubKsn)
    CLOSE_SERVER_STUB(adSearchRankStubKsn)
    CLOSE_SERVER_STUB(adSplashRankStubKsn)
    CLOSE_SERVER_STUB(adArchimedesRankStubKsn)
    //CLOSE_SERVER_STUB(adFanstopRankStubKsn)
    CLOSE_SERVER_STUB(adRiskControlServiceStub)
    CLOSE_SERVER_STUB(adStyleServerDefaultStub)
    CLOSE_SERVER_STUB(adStyleServerUniverseStub)
    CLOSE_SERVER_STUB(adTargetStubKsn)
    CLOSE_SERVER_STUB(adSplashTargetStubKsn)
    CLOSE_SERVER_STUB(adUniverseTargetStubKsn)
    CLOSE_SERVER_STUB(adUniverseTinyTargetStubKsn)
    CLOSE_SERVER_STUB(adFanstopTargetStubKsn)
    CLOSE_SERVER_STUB(adArchimedesTargetStubKsn)
    CLOSE_SERVER_STUB(adMatchServerStubKsn)
    CLOSE_SERVER_STUB(adSearchTargetStub)
    CLOSE_SERVER_STUB(adSearchTargetStubKsn)
    CLOSE_SERVER_STUB(adSearchTargetNewStubKsn)
    CLOSE_SERVER_STUB(adBidWordSearchStub)
    CLOSE_SERVER_BRPC_STUB(adBidWordSearchNewStub)
    CLOSE_SERVER_STUB(adMerchantDspServiceStubKsn)
    CLOSE_SERVER_STUB(adMerchantDspServiceNewStubKsn)
    CLOSE_SERVER_STUB(adMerchantDspLiveServiceStubKsn)
    CLOSE_SERVER_STUB(adMerchantDspLiveServiceNewStubKsn)
    CLOSE_SERVER_STUB(adAdxServiceStub)
    CLOSE_SERVER_STUB(adAdxTrackServiceStub)
    CLOSE_SERVER_STUB(AdPicassoGatewayServiceStub)
    CLOSE_SERVER_STUB(adTargetInnerStubKsn)
    CLOSE_SERVER_STUB(adDragonPrerankServerOuterKsnStub)
    CLOSE_SERVER_STUB(adDragonPrerankServerKsnStub)
    CLOSE_SERVER_STUB(adSearchInnerStubKsn0)
    CLOSE_SERVER_STUB(adSearchInnerStubKsn1)
    CLOSE_SERVER_STUB(adSearchStubKsn0)
    CLOSE_SERVER_STUB(adSearchStubKsn1)

  }else if(FLAGS_is_support_predict_stub) {
    //支持predict的diff桩
    REGISTER_SERVER_STUB_NEW(AdPicassoGatewayService, AdPicassoGatewayServiceStub, 23100, grpc_adPicassoGatewayServiceUserInfo)
    REGISTER_SERVER_STUB_NEW(adPsUniverseUnionItemCreativeService, adPsUniverseUnionItemCreativeServiceStub, 23110, grpc_ad_ps_universe_union_item_creative_service_ps_item_server)

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "Kess instance stoped";

    CLOSE_SERVER_STUB(AdPicassoGatewayServiceStub)
    CLOSE_SERVER_STUB(adPsUniverseUnionItemCreativeServiceStub)
  }else if(FLAGS_is_support_i18n_stub){
    REGISTER_SERVER_STUB_NEW(AdI18nForwardIndexTestStub, adI18nForwardIndexStub, FLAGS_i18nforward_port, grpc_adI18nForwardIndexService_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdI18nTargetStub, adI18nTargetStub, FLAGS_i18ntarget_port, grpc_adI18nTargetService_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdI18nPredictStub, adI18nPredictStub, FLAGS_i18npredict_port, grpc_adI18nPsDspNNRouterServer_diff_stub)

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "Kess instance stoped";
    CLOSE_SERVER_STUB(adI18nForwardIndexStub)
    CLOSE_SERVER_STUB(adI18nTargetStub)
    CLOSE_SERVER_STUB(adI18nPredictStub)
  }else if (FLAGS_is_support_addragon_stub) {
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsDspRetrievalDiffRouterDragonStub, 26100, ad-predict-router-dsp-retrieval)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, AdPsDspRetrievalDiffStubRouterServerStub, 26101, ad-predict-router-dsp-retrieval_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, AdTagRetrieveServerStub, 26109, grpc_adTagRetrieveServer_search)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, AdRetrPhotoSkuMultiServerStub, 26110, ad-retr-search-server-photo-sku-multi)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, AdRetrPhotoSkuMultiExpServerStub, 26111, ad-search-sku-retrieval-server-multi-realtime-exp)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, AdRetrFanstopSkuMultiServerStub, 26112, ad-retr-search-server-fanstop-multi)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, AdRetrSearchMultiServerStub, 26113, ad-retr-search-server-multi)
    REGISTER_SERVER_STUB_NEW(AdRetrServiceStub, AdRetrRealtimeMultiServerStub, 26114, ad-retr-search-server-multi-realtime)
    REGISTER_SERVER_STUB_NEW(AdBidwordRetrServiceStub, AdRetrBidwordServerStub, 26115, ad-bidword-retrieval-multi)

    REGISTER_SERVER_STUB(AdForwardSTub, forwardStubKsn, 26106, ad-forward-index)

    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adBidServiceDiffDragonStub, 26102, ad-dragon-bid-service)
    REGISTER_SERVER_STUB_NEW(AdDragonBidServiceStub, adBidServiceDiffStubDragonStub, 26103, ad-dragon-bid-service_diff_stub)
    REGISTER_SERVER_STUB_NEW(AdTargetStubDragon, adSearchBidwordTriggerStubKsn, 26106, ad-search-bidword-trigger)
    REGISTER_SERVER_STUB_NEW(AdTargetStubDragon, adSearchLiveTriggerStubKsn, 26107, ad-search-live-trigger)
    REGISTER_SERVER_STUB_NEW(AdTargetStubDragon, adSearchPhotoTriggerStubKsn, 26108, ad-search-photo-trigger)
    REGISTER_SERVER_STUB_NEW(AdTargetStub, adSearchTargetNewStubKsn, 26104, ad-target-server-search)
    REGISTER_SERVER_BRPC_BIDWORD_STUB(AdTargetBrpcServiceImpl, adBidWordSearchNewStub, 26105, ad-target-server-search-bidword)

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "dragon stub Kess instance stoped";

    CLOSE_SERVER_STUB(AdPsDspRetrievalDiffRouterDragonStub)
    CLOSE_SERVER_STUB(AdPsDspRetrievalDiffStubRouterServerStub)
    CLOSE_SERVER_STUB(AdTagRetrieveServerStub)
    CLOSE_SERVER_STUB(AdRetrPhotoSkuMultiServerStub)
    CLOSE_SERVER_STUB(AdRetrPhotoSkuMultiExpServerStub)
    CLOSE_SERVER_STUB(AdRetrFanstopSkuMultiServerStub)
    CLOSE_SERVER_STUB(AdRetrSearchMultiServerStub)
    CLOSE_SERVER_STUB(AdRetrRealtimeMultiServerStub)
    CLOSE_SERVER_STUB(AdRetrBidwordServerStub)
    CLOSE_SERVER_STUB(forwardStubKsn)
    CLOSE_SERVER_STUB(adBidServiceDiffDragonStub)
    CLOSE_SERVER_STUB(adBidServiceDiffStubDragonStub)
    CLOSE_SERVER_STUB(adSearchBidwordTriggerStubKsn)
    CLOSE_SERVER_STUB(adSearchLiveTriggerStubKsn)
    CLOSE_SERVER_STUB(adSearchPhotoTriggerStubKsn)
    CLOSE_SERVER_STUB(adSearchTargetNewStubKsn)
    CLOSE_SERVER_BRPC_STUB(adBidWordSearchNewStub)

  }else if (FLAGS_is_support_adforward_index_stub) {
    //0425 给ad-forward-index的diff使用
    //REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardSTub, adforwardShardStubKsn0, 27100, ad-forward-data-shard, 0);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardStubKsn0, 27100, ad-forward-data-shard, 0);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardStubKsn1, 27101, ad-forward-data-shard, 1);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardStubKsn2, 27102, ad-forward-data-shard, 2);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardStubKsn3, 27103, ad-forward-data-shard, 3);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardStubKsn4, 27104, ad-forward-data-shard, 4);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardStubKsn5, 27105, ad-forward-data-shard, 5);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardStubKsn6, 27106, ad-forward-data-shard, 6);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardStubKsn7, 27107, ad-forward-data-shard, 7);
    REGISTER_SERVER_STUB_NEW_V2(AdForwardOfflineStub, adforwardOfflineStubKsn, 27108, ad-forward-data);

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "forward stub Kess instance stoped";

    CLOSE_SERVER_STUB(adforwardShardStubKsn0)
    CLOSE_SERVER_STUB(adforwardShardStubKsn1)
    CLOSE_SERVER_STUB(adforwardShardStubKsn2)
    CLOSE_SERVER_STUB(adforwardShardStubKsn3)
    CLOSE_SERVER_STUB(adforwardShardStubKsn4)
    CLOSE_SERVER_STUB(adforwardShardStubKsn5)
    CLOSE_SERVER_STUB(adforwardShardStubKsn6)
    CLOSE_SERVER_STUB(adforwardShardStubKsn7)
    CLOSE_SERVER_STUB(adforwardOfflineStubKsn)


  }else if (FLAGS_is_support_brand_stub) {
    REGISTER_SERVER_STUB_NEW_V2(AdPsRouterStub, AdPsRouterDspServerStub, 22901, ad-predict-router-dsp)
    REGISTER_SERVER_STUB_NEW_V2(AdBrandBuyerStub, AdBrandBuyerServerStub, 22902, grpc_brandBuyerServer)

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "brand  Kess instance stoped";

    CLOSE_SERVER_STUB(AdPsRouterDspServerStub)
    CLOSE_SERVER_STUB(AdBrandBuyerServerStub)

  }else if (FLAGS_is_support_adfeature_proxy_stub) {
    //0425 给ad-forward-index的diff使用
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdFeatureSTub, adfeatureShardStubKsn0, 28100, ad-feature-shard-photo, 0);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdFeatureSTub, adfeatureShardStubKsn1, 28101, ad-feature-shard-photo, 1);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdFeatureSTub, adfeatureShardStubKsn2, 28102, ad-feature-shard-photo, 2);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdFeatureSTub, adfeatureShardStubKsn3, 28103, ad-feature-shard-photo, 3);

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "forward stub Kess instance stoped";

    CLOSE_SERVER_STUB(adfeatureShardStubKsn0)
    CLOSE_SERVER_STUB(adfeatureShardStubKsn1)
    CLOSE_SERVER_STUB(adfeatureShardStubKsn2)
    CLOSE_SERVER_STUB(adfeatureShardStubKsn3)

  }else if (FLAGS_is_support_adforward_algorithm_stub) {
    //0425 给ad-forward-index的diff使用
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardOfflineStubKsn0, 29100, ad-forward-data-shard-offline, 0);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardOfflineStubKsn1, 29101, ad-forward-data-shard-offline, 1);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardOfflineStubKsn2, 29102, ad-forward-data-shard-offline, 2);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardOfflineStubKsn3, 29103, ad-forward-data-shard-offline, 3);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardOfflineStubKsn4, 29104, ad-forward-data-shard-offline, 4);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardOfflineStubKsn5, 29105, ad-forward-data-shard-offline, 5);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardOfflineStubKsn6, 29106, ad-forward-data-shard-offline, 6);
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdForwardOfflineStub, adforwardShardOfflineStubKsn7, 29107, ad-forward-data-shard-offline, 7);
    REGISTER_SERVER_STUB_NEW_V2(AdForwardOfflineStub, adforwardOfflineStubKsn, 29108, ad-forward-data-offline);

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "forward shard offline stub Kess instance stoped";

    CLOSE_SERVER_STUB(adforwardShardOfflineStubKsn0)
    CLOSE_SERVER_STUB(adforwardShardOfflineStubKsn1)
    CLOSE_SERVER_STUB(adforwardShardOfflineStubKsn2)
    CLOSE_SERVER_STUB(adforwardShardOfflineStubKsn3)
    CLOSE_SERVER_STUB(adforwardShardOfflineStubKsn4)
    CLOSE_SERVER_STUB(adforwardShardOfflineStubKsn5)
    CLOSE_SERVER_STUB(adforwardShardOfflineStubKsn6)
    CLOSE_SERVER_STUB(adforwardShardOfflineStubKsn7)
    CLOSE_SERVER_STUB(adforwardOfflineStubKsn)

  }else if (FLAGS_is_support_ems_stub) {
    REGISTER_SERVER_STUB_NEW_V2(AdEcomHostingServiceStub, adEcomHostingService, 22189, grpc_adEcomHostingService);
    REGISTER_SERVER_STUB_NEW_V2(AdCoreLiveServiceStub, adCoreLiveServiceStub, 22190, grpc_adCoreLiveService);
    REGISTER_SERVER_STUB_NEW_V2(AdEspServiceStub, adEspServiceStub, 22191, grpc_adEspService);
    REGISTER_SERVER_STUB_NEW_V2(ApiCorePhotoAuthorServiceStub, apiCorePhotoAuthorServiceStub, 22192, grpc_apiCorePhotoAuthorService);
    REGISTER_SERVER_STUB_NEW_V2(AdDsiServiceStub, adDsiServiceStub, 22193, grpc_adDsiService);
    REGISTER_SERVER_STUB_NEW_V2(FansTopGatewayOrderServiceStub, fansTopGatewayOrderServiceStub, 22194, grpc_fansTopGatewayOrderService);
    //REGISTER_SERVER_STUB_NEW_V2(AdDmpServiceStub, adDmpServiceStub, 22195, ad-dmp-service-rpc);
    REGISTER_SERVER_STUB_NEW_V2(ApiCorePhotoServiceStub, apiCorePhotoServiceStub, 22196, grpc_apiCorePhotoService);
    REGISTER_SERVER_STUB_NEW_V2(FanstopPhotoServiceStub, fanstopPhotoServiceStub, 22197, grpc_fansTopPhotoService);
    REGISTER_SERVER_STUB_NEW_V2(KwaiShopFlowLiveServiceStub, kwaishopFlowLiveServiceStub, 22198, kwaishop-flow-live-service);
    //REGISTER_SERVER_STUB_NEW_V2(EstimateServiceStub, adEstimateServiceStub, 22199, grpc_adEstimateService);

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "ems-shard stub Kess instance stoped";

    CLOSE_SERVER_STUB(adEcomHostingService)
    CLOSE_SERVER_STUB(adCoreLiveServiceStub)
    CLOSE_SERVER_STUB(adEspServiceStub)
    CLOSE_SERVER_STUB(apiCorePhotoAuthorServiceStub)
    CLOSE_SERVER_STUB(adDsiServiceStub)
    CLOSE_SERVER_STUB(fansTopGatewayOrderServiceStub)
    //CLOSE_SERVER_STUB(adDmpServiceStub)
    CLOSE_SERVER_STUB(apiCorePhotoServiceStub)
    CLOSE_SERVER_STUB(fanstopPhotoServiceStub)
    CLOSE_SERVER_STUB(kwaishopFlowLiveServiceStub)
    //CLOSE_SERVER_STUB(adEstimateServiceStub)

  }else if (FLAGS_is_support_grid_stub){
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdGridStub, adGridOnlineShard0, 21190,ad-grid-service-online-shard, 0)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdGridStub, adGridOnlineShard1, 21191, ad-grid-service-online-shard, 1)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdGridStub, adGridOnlineShard2, 21192, ad-grid-service-online-shard, 2)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdGridStub, adGridOnlineShard3, 21193, ad-grid-service-online-shard, 3)

    CLOSE_SERVER_STUB(adGridOnlineShard0)
    CLOSE_SERVER_STUB(adGridOnlineShard1)
    CLOSE_SERVER_STUB(adGridOnlineShard2)
    CLOSE_SERVER_STUB(adGridOnlineShard3)
  }else {
    //这里注册下需要的服务， 先这么粗暴注册， 每个服务5个线程
    //REGISTER_SERVER_STUB(BrandInfoQueryInStub, brandInfoQueryStub, 21009, grpc_adBrandInfoQueryServiceV2_stub)
    //REGISTER_SERVER_STUB(AdBrandStub, brandStub, 21002, grpc_adBrandServer_stub)
    //REGISTER_SERVER_STUB(AdBrandStub, brandStubV2, 21001, grpc_adBrandService)
    REGISTER_SERVER_STUB(AdRankStubDragon, adRankStubKsn, 20671, ad-rank-server)
    REGISTER_SERVER_STUB(AdBrandStub, brandStubV2, 21001, ad-brand)
    REGISTER_SERVER_STUB(AdBrandStub, brandUniverseStubV2, 21000, ad-brand-universe)
    REGISTER_SERVER_STUB(AdBrandStub, brandPinPaiStubV2, 22000, ad-brand-pinpai)
    REGISTER_SERVER_STUB(AdBrandStub, brandSearchStubV2, 23000, ad-brand-search)
    REGISTER_SERVER_STUB(AdBrandStub, brandContentStubV2, 21002, ad-brand-content)
    REGISTER_SERVER_STUB(AdDspStub, dspStub, 20731, grpc_adDspBaseRpcService)
    REGISTER_SERVER_STUB(AdForwardSTub, forwardStub, 21003, grpc_adForwardIndexService)
    REGISTER_SERVER_STUB(AdForwardSTub, forwardStubKsn, 22003, ad-forward-index)
    REGISTER_SERVER_STUB(AdPackStub, adPackStub, 21011, grpc_adPackService)
    REGISTER_SERVER_STUB(AdPackStub, adPackStubKsn, 22011, ad-pack-server)
    // REGISTER_SERVER_STUB(AdPackStub, adPackUniverseStub, 21012, grpc_adPackService_universe)
    REGISTER_SERVER_STUB(AdPackStub, adPackUniverseStubKsn, 22012, ad-pack-server-universe)
    REGISTER_SERVER_STUB_NEW_V2(AdPackStub, adPackLongTailStubKsn, 22013, ad-pack-server-long-tail-flow)
    //REGISTER_SERVER_STUB(AdServerStub, adServerStub, 20082, grpc_adServer)
    //REGISTER_SERVER_STUB(AdServerStub, adServerNewStubKsn, 21182, ad-server)
    REGISTER_SERVER_STUB(AdServerStubDragon, adServerNewStubDragonKsn, 20182, ad-server)
    //REGISTER_SERVER_STUB(AdServerStub, adServerArchimedesStubKsn, 21083, ad-server-archimedes)
    REGISTER_SERVER_STUB(AdServerStubDragon, adServerArchimedesStubDragonKsn, 20083, ad-server-archimedes)
    //REGISTER_SERVER_STUB(AdServerStub, adServerSplashNewStubKsn, 21185, ad-server-splash)
    REGISTER_SERVER_STUB(AdServerStubDragon, adServerSplashNewStubDragonKsn, 20185, ad-server-splash)
    //REGISTER_SERVER_STUB(AdServerStub, adServerSearchStubKsn, 21088, ad-server-search)
    REGISTER_SERVER_STUB(AdServerStubDragon, adServerSearchStubDragonKsn, 20088, ad-server-search)
    //REGISTER_SERVER_STUB(AdServerStub, adServerUniverseNewStubKsn, 21189, ad-server-universe)
    REGISTER_SERVER_STUB(AdServerStubDragon, adServerUniverseNewStubDragonKsn, 20189, ad-server-universe)
    //REGISTER_SERVER_STUB(AdServerStub, adServerUniverseTinyNewStubKsn, 21190, ad-server-universe-tiny)
    REGISTER_SERVER_STUB(AdServerStubDragon, adServerUniverseTinyNewStubDragonKsn, 20190, ad-server-universe-tiny)
    REGISTER_SERVER_STUB(RtaServerStub, adRtaServerStub, 20091, grpc_rtaService)
    REGISTER_SERVER_STUB(AdSocialRecoFollowStub, adSocialStub, 20061, grpc_adSocialRecoFollowService)
    REGISTER_SERVER_STUB(AdUserProfileStub, adUserProfileKsnStub, 21004, ad-user-profile)
    REGISTER_SERVER_STUB_NEW_V2(AdUserProfileStub, adUserProfileKnewsKsnStub, 21005, ad-user-profile-knews)
    REGISTER_SERVER_STUB(AdUserProfileStub, adUserProfileUniverseKsnStub, 21006, ad-user-profile-universev2)
    REGISTER_SERVER_STUB(ApiCoreUserCacheStub, apiCoreUserCacheStub, 20197, grpc_apiCoreUserCacheService)
    REGISTER_SERVER_STUB(ApiCoreUserCountStub, apiCoreUserCountStub, 20223, grpc_apiCoreUserCountService)
    REGISTER_SERVER_STUB(FanstopWhiteListStub, fanstopWhiteListStub, 20852, grpc_fansTopWhiteListService)
    REGISTER_SERVER_STUB(SspStub, sspStub, 20703, grpc_sspService)
    REGISTER_SERVER_STUB(AdI18nForwardIndexTestStub, adI18nForwardIndexTestStub, FLAGS_i18nforward_port, grpc_adI18nForwardIndexService_test)
    REGISTER_SERVER_STUB_NEW(AdStyleServerStub, adStyleServerSplashStub, 20389, ad-style-server-splash)
    REGISTER_SERVER_STUB_NEW(AdPsRouterStub, adPsRouterKsnFrontServiceStub, 20663, ad-predict-router)
    REGISTER_SERVER_STUB_NEW(AdPsRouterPrepareStub, adPsRouterPrepareKsnFrontServiceStub, 20665, ad-predict-router-universe-tiny)
    REGISTER_SERVER_STUB_NEW(AdPsRouterPrepareStub, adTwinTowerPrepareKsnFrontServiceStub, 20666, ad-twin-towers-router-universe)
    REGISTER_SERVER_STUB_NEW(AdPsRouterPrepareStub, adTwinTowerPrepareKsnFrontServiceTinyStub, 20667, ad-twin-towers-router-universe-tiny)
    REGISTER_SERVER_STUB_NEW(AdEmbeddingRetrRouterStub, adAnnRouterServiceStub, 20664, ad-ann-router-universe)
    REGISTER_SERVER_STUB_NEW(AdPostServicePidStub, adPostServicePidKsnStub, 20665, ad-post-server-pid)
    REGISTER_SERVER_STUB_NEW(AdKwaiShopSelectionStub, adKwaiShopSelectionStub, 20668, kwaishop-selection-user-service)
    REGISTER_SERVER_STUB(AdTargetStubDragon, adTargetInnerStubKsn, 26017, ad-target-server-inner-photo)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchInnerStubKsn0, 26023, ad-searcher-inner-photo, 0)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchInnerStubKsn1, 26037, ad-searcher-inner-photo, 1)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchStubKsn0, 26036, ad-searcher, 0)
    REGISTER_SERVER_STUB_NEW_V2_SHARD(AdTargetStubDragon, adSearchStubKsn1, 26038, ad-searcher, 1)
    REGISTER_SERVER_STUB_NEW(RECOIDMAPPINGSTUB, recoIdMappingStub, 26039, reco-id-mapping-service-online)
    REGISTER_SERVER_STUB(AdUserProfileStub, grpcadUserProfileKsnStub, 21004, grpc_adUserProfileService)
    

    serving_base::SignalCatcher::WaitForSignal(); // 等待信号
    LOG(INFO) << "Kess instance stoped";

    //CLOSE_SERVER_STUB(brandStub)
    CLOSE_SERVER_STUB(adRankStubKsn)
    CLOSE_SERVER_STUB(brandStubV2)
    CLOSE_SERVER_STUB(brandUniverseStubV2)
    CLOSE_SERVER_STUB(brandPinPaiStubV2)
    CLOSE_SERVER_STUB(brandSearchStubV2)
    CLOSE_SERVER_STUB(brandContentStubV2)
    CLOSE_SERVER_STUB(dspStub)
    CLOSE_SERVER_STUB(forwardStub)
    CLOSE_SERVER_STUB(forwardStubKsn)
    // CLOSE_SERVER_STUB(styleMaterialStubKsn)
    CLOSE_SERVER_STUB(adPackStub)
    CLOSE_SERVER_STUB(adPackStubKsn)
    // CLOSE_SERVER_STUB(adPackUniverseStub)
    CLOSE_SERVER_STUB(adPackUniverseStubKsn)
    CLOSE_SERVER_STUB(adPackLongTailStubKsn)
    //CLOSE_SERVER_STUB(adServerStub)
    //CLOSE_SERVER_STUB(adServerNewStubKsn)
    CLOSE_SERVER_STUB(adServerNewStubDragonKsn)
    //CLOSE_SERVER_STUB(adServerArchimedesStubKsn)
    CLOSE_SERVER_STUB(adServerArchimedesStubDragonKsn)
    //CLOSE_SERVER_STUB(adServerSplashNewStubKsn)
    CLOSE_SERVER_STUB(adServerSplashNewStubDragonKsn)
    //CLOSE_SERVER_STUB(adServerSearchStubKsn)
    CLOSE_SERVER_STUB(adServerSearchStubDragonKsn)
    //CLOSE_SERVER_STUB(adServerUniverseNewStubKsn)
    CLOSE_SERVER_STUB(adServerUniverseNewStubDragonKsn)
    //CLOSE_SERVER_STUB(adServerUniverseTinyNewStubKsn)
    CLOSE_SERVER_STUB(adServerUniverseTinyNewStubDragonKsn)
    CLOSE_SERVER_STUB(adRtaServerStub)
    CLOSE_SERVER_STUB(adSocialStub)
    CLOSE_SERVER_STUB(adUserProfileKsnStub)
    CLOSE_SERVER_STUB(adUserProfileKnewsKsnStub)
    CLOSE_SERVER_STUB(adUserProfileUniverseKsnStub)
    CLOSE_SERVER_STUB(apiCoreUserCacheStub)
    CLOSE_SERVER_STUB(apiCoreUserCountStub)
    CLOSE_SERVER_STUB(fanstopWhiteListStub)
    CLOSE_SERVER_STUB(sspStub)
    //CLOSE_SERVER_STUB(brandInfoQueryStub)
    CLOSE_SERVER_STUB(adI18nForwardIndexTestStub)
    CLOSE_SERVER_STUB(adStyleServerSplashStub)
    CLOSE_SERVER_STUB(adPsRouterKsnFrontServiceStub)
    CLOSE_SERVER_STUB(adPsRouterPrepareKsnFrontServiceStub)
    CLOSE_SERVER_STUB(adTwinTowerPrepareKsnFrontServiceStub)
    CLOSE_SERVER_STUB(adTwinTowerPrepareKsnFrontServiceTinyStub)
    CLOSE_SERVER_STUB(adAnnRouterServiceStub)
    CLOSE_SERVER_STUB(adPostServicePidKsnStub)
    CLOSE_SERVER_STUB(adKwaiShopSelectionStub)
    CLOSE_SERVER_STUB(adSearchInnerStubKsn0)
    CLOSE_SERVER_STUB(adSearchInnerStubKsn1)
    CLOSE_SERVER_STUB(adSearchStubKsn0)
    CLOSE_SERVER_STUB(adSearchStubKsn1)
    CLOSE_SERVER_STUB(adTargetInnerStubKsn)
    CLOSE_SERVER_STUB(recoIdMappingStub)
    CLOSE_SERVER_STUB(grpcadUserProfileKsnStub)

  }
  ::google::FlushLogFiles(::google::INFO);
  return 0;
}
