syntax = "proto3";
package ks.grid.proto;

message DBconf {
  string base_task = 1;        // 基准任务名
  string inc_topic = 2;        // 增量名
  bool enable_flyweight_mode = 3;  // 复杂数据结构启用享元模式
  bool enable_alluxio = 4;  // 是否从 alluxio 集群读数据
  string alluxio_path = 5;  // 索引数据的原始 hdfs 目录
  bool use_shard_mode = 6;  // 分片加载模式
  bool enable_local_subscribe = 7;  // 是否开启索引本地订阅
  string biz_name = 8;
  bool use_datalink_table_schema = 9; // 使用 datalink下发schema
  string build_key = 10; // 构建任务标识，用于获取 buildConfig
}

message DBconfList {
  map<string, string> tables = 1;
  map<string, DBconf> groups = 2;
}

message CacheOptions {
  bool use = 1;
  uint64 ttl = 2;
  uint64 max_idle = 3;
  uint64 init_cache_size = 4;
  bool enable_cache_null = 5;
  bool disable_evict = 6;
}

message CacheConfig {
  map<string, CacheOptions> cache_options = 1;
}


message TableMeta {
  string ksn = 1;
  int32 shard_num = 2;
  string prefix = 3;
}

message TableRouterConfig {
  map<string, TableMeta> table_metas = 1;
}

// 流量调度配置
message KsnRouterMeta {
  int32 shard_num = 1;
  string prefix = 2;
  int32 ratio = 3;
}

message KsnRouterConfig {
  repeated KsnRouterMeta ksn_metas = 1;
}

message TrafficRouterConfig {
  map<string, KsnRouterConfig> router_metas = 1;
}