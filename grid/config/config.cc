#include <string>
#include "teams/ad/grid/config/config.h"
#include "absl/strings/str_join.h"
#include "kenv/kenv.h"
#include "kconf/kconf.h"
#include "glog/logging.h"

namespace ks {
namespace grid {
static const uint64_t kMicrosecondsPerSec = 1000000;
bool TableRouterConfig::Init() {
  LOG(INFO) << "TableRouterConfig init, raw pb is " << pb().DebugString();
  absl::flat_hash_map<std::string, ks::grid::proto::TableMeta> table_metas_tmp;
  for (const auto& item : pb().table_metas()) {
    LOG(INFO) << item.first << " " << item.second.DebugString();
    table_metas_tmp[item.first] = item.second;
  }

  table_metas_.swap(table_metas_tmp);

  if (table_metas_.empty()) {
    LOG(ERROR) << "table_metas_ is empty";
  }
  return true;
}

template <typename Proto> class ProtoParser {
 public:
  bool operator()(const kuaishou::config::Value& value, Proto* pb) const {
    google::protobuf::StringValue string_val;
    if (value.type() != kuaishou::config::ConfigType::STRING || !string_val.ParseFromString(value.data())) {
      return false;
    }
    google::protobuf::util::JsonParseOptions option;
    option.ignore_unknown_fields = true;
    auto parse_status = google::protobuf::util::JsonStringToMessage(string_val.value(), pb, option);
    LOG(INFO) << "grid conf is loading now: " << pb->GetDescriptor()->full_name() << " = "
              << pb->DebugString();
    return parse_status.ok();
  }
};

const ks::grid::proto::DBconfList& GridKconf::GetDBConfig() {
  static const std::string prefix = "ad.adTable.loadConfig_";
  static auto ksn = ks::infra::KEnv::GetKWSInfo()->GetKSN();
  static std::string kconf_key = prefix + ksn;
  static auto handler = ks::infra::KConf().Get<proto::DBconfList>(kconf_key,
    std::make_shared<proto::DBconfList>(), ProtoParser<proto::DBconfList>());
  static auto db_config = handler->Get();
  return *db_config;
}

bool CacheConfig::Init() {
  for (const auto & [table_name, cache_option] : pb().cache_options()) {
    proto::CacheOptions option = cache_option;
    // sec convert to microsec
    option.set_ttl(cache_option.ttl() * kMicrosecondsPerSec);
    option.set_max_idle(cache_option.max_idle() * kMicrosecondsPerSec);
    LOG(INFO) << "[Grid SDK] cache " << table_name
              << " option: " << option.ShortDebugString()
              << ", time unit is Microseconds";
    call_2_cache_options_[table_name] = option;
  }
  if (call_2_cache_options_.find("default") == call_2_cache_options_.end()) {
    LOG(ERROR)  << "[Grid SDK] default cache option is not found";
    return false;
  }
  return true;
}

proto::CacheOptions CacheConfig::GetCacheOptions(const std::string& table_name) const {
  auto iter = call_2_cache_options_.find(table_name);
  if (iter != call_2_cache_options_.end()) {
    return iter->second;
  }
  return {};
}

}  // namespace grid
}  // namespace ks
