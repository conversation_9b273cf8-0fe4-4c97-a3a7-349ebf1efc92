#include <unistd.h>

#include <iostream>
#include <string>
#include <vector>

#include "base/common/base.h"
#include "gflags/gflags.h"
#include "kenv/kenv.h"
#include "kenv/service_meta.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/grid/client/sdk_client.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/sdk/grid_query.h"
#include "teams/ad/grid/client/grid_query_config.h"
#include "teams/ad/grid/config/global_dynamic_config.h"

using namespace ks::grid;  // NOLINT
GridQuery BuildForwardQuery(const std::vector<std::string>& cols, const std::vector<int64_t>& ids) {
  GridQuery query;
  for (auto col : cols) { query.select_fields.push_back(col); }
  for (int i = 0; i < ids.size(); ++i) {
    Querykey key;
    key.i64 = ids[i];
    query.ids.push_back(key);
  }
  return query;
}

void TestBody(const std::vector<GridQuery>& req) {
  static std::string timeout_ms = "ad.grid.test_timeout_ms";
  std::cout << "********************" << std::endl;
  std::vector<GridData> resp;
  auto timeout_getter = ks::grid::GlobalDynamicConfig<int32_t>::GetConfig(timeout_ms);

  GridQueryConfig config;
  config.timeout_ms = timeout_getter->Get();
  config.timeout_ms = 10;
  LOG(INFO) << "debug timeout_ms:" << config.timeout_ms;
  SDKClient handler(config);
  auto res = handler.BatchGetTable(req);
  if (res != 0) {
    std::cout << "error. res: " << res << std::endl;
    return;
  }
  res = handler.Wait(&resp);
  if (res != 0) {
    std::cout << "error. res: " << res << std::endl;
    return;
  }
  if (resp.size() != req.size()) {
    std::cout << "error. resp size: " << resp.size() << " req size: " << req.size() << std::endl;
    return;
  }
  for (int i = 0; i < resp.size(); ++i) {
    std::cout << "--------------------" << std::endl;
    auto& grid_data = resp[i];
    if (grid_data.IsValid()) {
      std::cout << "col_size:" << grid_data.ColSize() << std::endl;
      std::cout << "row_size:" << grid_data.RowSize() << std::endl;
      for (int i = 0; i < grid_data.RowSize(); ++i) {
        std::cout << "Debugstring: " << grid_data.DebugString(i) << std::endl;
      }
    } else {
      std::cout << "invalid" << std::endl;
    }
    std::cout << "--------------------" << std::endl;
  }
  std::cout << "********************" << std::endl;
}

int main(int argc, char** argv) {
  base::InitApp(&argc, &argv, "");
  AD_KESS_CLIENT_INIT_CHECK("init error");
  auto lane = ks::infra::kenv::ServiceMeta::GetLane();
  std::cout << "debug lane:" << lane << std::endl;
  ks::infra::kenv::ServiceMeta::SetLane("ad_prt");
  std::cout << "debug lane:" << ks::infra::kenv::ServiceMeta::GetLane() << std::endl;
  FLAGS_enable_grid_sdk_cache = true;
  {
    std::cout << "test for timeout" << std::endl;
    std::vector<std::string> cols = {"id", "user_id", "test"};
    std::vector<int64_t> ids = {
      ********, ********, ********, ********, ********
    };

    GridQuery query = BuildForwardQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    TestBody(req);
  }
  return 0;
  {
    std::cout << "normal forward case: account" << std::endl;
    std::vector<std::string> cols = {"id", "user_id", "test"};
    std::vector<int64_t> ids = {
      912, 912
    };

    GridQuery query = BuildForwardQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    TestBody(req);
  }

  {
    std::cout << "normal forward case: account hit cache" << std::endl;
    std::vector<std::string> cols = {"id", "user_id", "test"};
    std::vector<int64_t> ids = {
      912,  912
    };

    GridQuery query = BuildForwardQuery(cols, ids);
    query.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query};
    TestBody(req);
  }


  {
    std::cout << "three normal forward case: two hit cache" << std::endl;
    std::vector<std::string> cols = {"id", "user_id", "test"};
    std::vector<int64_t> ids1 = {
      844,  844
    };
    std::vector<int64_t> ids2 = {
      912, 940, 964, 1004,
    };
    std::vector<int64_t> ids3 = {
      844,  844
    };

    GridQuery query1 = BuildForwardQuery(cols, ids1);
    GridQuery query2 = BuildForwardQuery(cols, ids2);
    GridQuery query3 = BuildForwardQuery(cols, ids3);
    query1.table_name = "ad_dsp_account";
    query2.table_name = "ad_dsp_account";
    query3.table_name = "ad_dsp_account";
    std::vector<GridQuery> req{query1, query2, query3};
    TestBody(req);
  }

  {
    std::cout << "three normal forward case: two different table" << std::endl;
    std::vector<std::string> cols1 = {
      "id", "user_id", "product_name", "put_status"
    };
    std::vector<int64_t> ids1 = {
      844, 912, 940, 964, 1004
    };

    std::vector<std::string> cols2 = {
      "app_id", "app_name", "package_id"
    };
    std::vector<int64_t> ids2 = {
      *********, *********, *********
    };

    GridQuery query_account = BuildForwardQuery(cols1, ids1);
    GridQuery query_app = BuildForwardQuery(cols2, ids2);
    query_account.table_name = "ad_dsp_account";
    query_app.table_name = "ad_app_release";
    std::vector<GridQuery> req{query_account, query_app};
    TestBody(req);
  }


  {
    std::cout << "shard case" << std::endl;
    std::vector<std::string> cols1 = {
      "id", "user_id", "product_name", "put_status"
    };
    std::vector<int64_t> ids1 = {
      844, 2, 912, 940, 964, 1
    };
    GridQuery query_account = BuildForwardQuery(cols1, ids1);
    query_account.table_name = "ad_dsp_account";
    query_account.CalculateMd5();

    std::vector<std::string> cols2 = {
      "id", "account_id", "campaign_id"
    };
    std::vector<int64_t> ids2 = {
      ***********, ***********, ***********, ***********
    };
    std::vector<int64_t> ids3 = {
      ***********, ***********, ***********, ***********
    };

    GridQuery query_unit = BuildForwardQuery(cols2, ids2);
    query_unit.table_name = "ad_dsp_unit";
    GridQuery query_unit2 = BuildForwardQuery(cols2, ids3);
    query_unit2.table_name = "ad_dsp_unit";

    std::vector<GridQuery> req{query_account, query_unit, query_unit2};
    TestBody(req);
  }
  return 0;
}
