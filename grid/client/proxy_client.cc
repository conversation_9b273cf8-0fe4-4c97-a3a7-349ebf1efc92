#include "teams/ad/grid/client/proxy_client.h"

#include <cstdint>
#include <memory>
#include <utility>
#include <vector>

#include "kess/rpc/batch_waiter.h"
#include "rpc/rpc_helper.h"
#include "teams/ad/grid/cache/cache_list.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/grid_generated.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/utils/utils.h"

namespace ks {
namespace grid {
static const int K128_BYTE_SIZE = 16;
static const int K64_BYTE_SIZE = 8;

StatusCode ProxyClient::Init(const GridRequest& request) {
  const auto& table_name_map = GridKconf::TableRouterConfig()->data().GetTable();
  search_contexts_.resize(request.grid_querys_size());  // 暂不需要，正排 & 缓存场景使用
  for (int i = 0; i < request.grid_querys_size(); ++i) {
    auto ctx = &search_contexts_[i];
    auto& query = request.grid_querys(i);
    auto& table_name = query.table_name();
    if (table_name.empty()) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] empty table_name";
      return StatusCode::ARGUMENT_ERROR;
    }
    if (query.select_fields_size() == 0) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] empty select_fields" << " table_name:" << table_name;
      return StatusCode::ARGUMENT_ERROR;
    }
    if (query.keys().buf().empty()) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] empty ids" << " table_name:" << table_name;
      return StatusCode::ARGUMENT_ERROR;
    }
    if (table_name_map.find(table_name) == table_name_map.end()) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name=" << table_name << " not found in router_option";
      return StatusCode::RPC_CONFIG_ERROR;
    }
    if (query.index_name().empty()) {
      if (UNLIKELY(query.keys().width() != K64_BYTE_SIZE)) {
        LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] table_name" << table_name
                                 << ", index_name:" << query.index_name() << ", keys width is not 8";
        return StatusCode::ARGUMENT_ERROR;
      }
      int32_t row_size = query.keys().buf().size()/query.keys().width();
      search_contexts_[i].query_size = row_size;
      search_contexts_[i].data = std::make_shared<std::vector<FbRow>>(row_size);
    } else {
      if (UNLIKELY(query.limit() == 0 && query.posting_list_length_limit() == 0)) {
        LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] limit & posting_list_length_limit is empty"
                                 << " table_name:" << table_name << ", index_name:" << query.index_name();
        return StatusCode::ARGUMENT_ERROR;
      }
      if (UNLIKELY(query.keys().width() != K128_BYTE_SIZE)) {
        LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] table_name" << table_name
                                 << ", index_name:" << query.index_name() << ", keys width is not 16";
        return StatusCode::ARGUMENT_ERROR;
      }
      search_contexts_[i].query_size = query.keys().buf().size()/query.keys().width();
      search_contexts_[i].data = std::make_shared<std::vector<FbRow>>();
    }
    if (UNLIKELY(query.keys().buf().size() % query.keys().width() != 0)) {
      LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] table_name" << table_name
                               << ", index_name:" << query.index_name()
                               << ", keys size is not multiple of 16";
      return StatusCode::ARGUMENT_ERROR;
    }

    ctx->table_name = table_name;
    ctx->index_name = query.index_name();
    ctx->result = response_->add_results();

    auto ksn_meta = table_name_map.at(table_name);
    if (GridKconf::EnableTrafficRouter()) {
      const std::string &ksn = ksn_meta.ksn();
      bool res = GridKconf::TrafficRouterConfig()->data().SelectKsnMeta(ksn, ksn_meta);
      if (!res) {
        LOG(ERROR) << "[Grid SDK] traffic router config not found, ksn=" << ksn
                    << " , use default config";
        return StatusCode::RPC_CONFIG_ERROR;
      }
    }
    if (ksn_rpc_helper_.find(ksn_meta.ksn()) == ksn_rpc_helper_.end()) {
      std::shared_ptr<RpcHelper> helper = std::make_shared<RpcHelper>(ksn_meta);
      helper->SetVersion(request.version());
      helper->SetTimeoutMs(grid_query_config_.timeout_ms);
      ksn_rpc_helper_[ksn_meta.ksn()] = helper;
    }
    rpc_helper_[table_name] = ksn_rpc_helper_[ksn_meta.ksn()];
  }

  batch_waiter_ = std::make_shared<ks::kess::rpc::BatchWaiter>();
  return StatusCode::OK;
}

StatusCode ProxyClient::BatchGetTable(const GridRequest& request, GridResponse* response,
                                      const std::string& caller) {
  if (response == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] response is nullptr";
    return StatusCode::ARGUMENT_ERROR;
  }
  response_ = response;

  auto ret = Init(request);
  if (ret != StatusCode::OK) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] Prepare failed";
    return ret;
  }

  for (int i = 0; i < request.grid_querys_size(); ++i) {
    auto& query = request.grid_querys(i);
    BuildRequest(query, i);
  }
  Request(batch_waiter_.get(), &ksn_rpc_helper_, caller);
  if (FLAGS_enable_grid_sdk_cache) {
    FillCacheBlockFB(request);
  }
  batch_waiter_->Wait();
  if (FLAGS_enable_grid_sdk_cache) {
    UpdateCache();
  }
  return StatusCode::OK;
}

void ProxyClient::BuildRequest(const proto::GridQuery& query, uint32_t index) {
  if (index >= search_contexts_.size()) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] index out of range";
    return;
  }
  if (rpc_helper_[query.table_name()] == nullptr) {
    LOG_EVERY_N(INFO, 1000) << "[Grid SDK] " << query.table_name() << " rpc_helper is nullptr";
    return;
  }
  auto& ctx = search_contexts_[index];
  auto& rpc_helper = rpc_helper_[query.table_name()];
  if (!query.index_name().empty()) {
    rpc_helper->AddIndexQuery(query, &ctx);
  } else {
    if (FLAGS_enable_grid_sdk_cache) {
      TryGetFromCache(query, &ctx);
      if (ctx.cache_hit == ctx.query_size) {
        LOG_EVERY_N(INFO, 8192)<< "[Grid SDK] " << query.table_name() << " cache hit";
        return;
      }
      auto filter = [cache = ctx.data](int32_t i) {
        return (*cache)[i];
      };
      rpc_helper->AddForwardQuery(query, filter, &ctx);
    } else {
      rpc_helper->AddForwardQuery(query, [](int32_t i) { return false; }, &ctx);
    }
  }
  return;
}

void ProxyClient::TryGetFromCache(const proto::GridQuery& query, GridQueryContext* ctx) {
  auto& table_name = query.table_name();
  absl::Span<const int64_t> search_keys(
    reinterpret_cast<const int64_t*>(
      query.keys().buf().data()), ctx->query_size);

  auto cache_ptr = CacheList<FbRow>::Instance()
      .Cache(table_name, query.md5())
      .value_or(nullptr);

  if (cache_ptr == nullptr) {
    LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] table_name=" << table_name << " cache instance not Found";
    return;
  }
  ctx->cache = cache_ptr;
  std::vector<TKey> keys;
  keys.resize(search_keys.size());
  for (int j = 0; j < search_keys.size(); ++j) {
    keys[j] = search_keys[j];
  }
  // TODO(@huhao11) :BatchGet 新增 Span 接口，避免构造 vec
  int ret = cache_ptr->BatchGet(keys, ctx->data.get());
  ctx->cache_hit = ret;
  ctx->keys2cache.reserve(keys.size() - ret);
  ctx->rows2cache.reserve(keys.size() - ret);
  ctx->column_desc = cache_ptr->GetTableSchema();
  return;
}

void ProxyClient::UpdateCache() {
  for (GridQueryContext& ctx : search_contexts_) {
    auto cache_ptr = ctx.cache;
    // 不支持倒排缓存
    if (!ctx.index_name.empty()) {
      continue;
    }
    if (!cache_ptr) {
      LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] cache_ptr is nullptr"
                   << "table_name=" << ctx.table_name << " cache instance not Found";
      continue;
    }
    if (ctx.grid_results.empty()) {
      continue;
    }
    UpdateOneCache(&ctx);
  }
}

void ProxyClient::UpdateOneCache(GridQueryContext* ctx) {
  if (UNLIKELY(ctx == nullptr)) {
    LOG(ERROR) << "[Grid SDK] ctx is nullptr";
    return;
  }
  auto cache_ptr = ctx->cache;
  if (UNLIKELY(cache_ptr == nullptr)) {
    LOG(ERROR) << "[Grid SDK] cache_ptr is nullptr"
                 << "table_name=" << ctx->table_name << " cache instance not Found";
    return;
  }
  for (auto grid_result : ctx->grid_results) {
    for (auto& block : *(grid_result.mutable_blocks())) {
      std::shared_ptr<std::string> data = std::make_shared<std::string>(std::move(block));
      auto fb_table = flatbuffers::GetRoot<flat::GridTable>(data->data());
      if (fb_table == nullptr || fb_table == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, data=" << *data;
        continue;
      }
      if (fb_table->name()->size() == 0 || fb_table->columns() == 0 || fb_table->rows() == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, table is empty, name_size:"
                                  << fb_table->name()->size()
                                  << " columns_size:" << fb_table->columns()->size()
                                  << " rows_size:" << fb_table->rows()->size();
        continue;
      }
      auto col_size = fb_table->columns()->size();
      auto row_size = fb_table->rows()->size();
      for (int row_id = 0; row_id < row_size; ++row_id) {
        auto row = fb_table->rows()->Get(row_id);
        int64_t id = row->id();
        auto row_data = const_cast<flatbuffers::Vector<flatbuffers::Offset<flat::Value>>*>(row->data());
        FbRow fb_row;
        if (row_data != 0 && row_data->size() == col_size) {
          fb_row.guard = data;
          fb_row.row = row_data;
          ctx->keys2cache.push_back(id);
          ctx->rows2cache.push_back(fb_row);
        }
      }
      if (cache_ptr->NeedInit()) {
        cache_ptr->Init(fb_table->columns());
      }
    }
  }
  cache_ptr->BatchSet(ctx->keys2cache, ctx->rows2cache);
}

void ProxyClient::FillCacheBlockFB(const GridRequest& request) {
  for (int i = 0; i < request.grid_querys_size(); ++i) {
    auto& query = request.grid_querys(i);
    GridQueryContext& ctx = search_contexts_[i];
    auto& table_name = query.table_name();
    auto& cache_data = ctx.data;
    auto& cache_ptr = ctx.cache;

    if (cache_data == nullptr || ctx.cache_hit == 0 || ctx.result == nullptr) {
      continue;
    }
    absl::Span<const int64_t> search_keys(
      reinterpret_cast<const int64_t*>(
        query.keys().buf().data()), ctx.query_size);
    flatbuffers::FlatBufferBuilder fb_builder;
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flat::Column>>> col_vec;
    // 构造列
    if (cache_ptr->NeedInit()) {
      continue;
    } else {
      col_vec = BuildFBColumnDesc(cache_ptr->GetTableSchema(), &fb_builder);
    }
    // 构造行
    std::vector<flatbuffers::Offset<flat::Row>> rows;
    for (int i = 0; i < cache_data->size(); ++i) {
      auto cache_row = (*cache_data)[i].row;
      int64_t key = search_keys[i];
      if (!(cache_row)) {
        continue;
      }
      auto row = CopyFBRow(key, cache_row, &fb_builder);
      rows.push_back(row);
    }
    if (rows.size() == 0) {
      continue;
    }
    // 构造 table
    auto rows_vec = fb_builder.CreateVector(rows);
    auto name_fb = fb_builder.CreateString(table_name);
    auto fb_table = flat::CreateGridTable(fb_builder, name_fb, col_vec, rows_vec);
    fb_builder.Finish(fb_table);
    auto block = ctx.result->add_blocks();
    block->reserve(fb_builder.GetSize());
    block->assign(fb_builder.GetBufferPointer(),
                  fb_builder.GetBufferPointer() + fb_builder.GetSize());
  }
}

void ProxyClient::Request(
    ks::kess::rpc::BatchWaiter* batch_waiter,
    absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper,
    const std::string& caller) {
  auto parser = [this](GridResponse* response, std::vector<GridQueryContext*>& ctxs) {
    this->ParserResponse(response, ctxs);
  };

  for (auto& [ksn, rpc_helper] : *ksn_rpc_helper) {
    if (rpc_helper == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] rpc_helper is nullptr";
      continue;
    }
    rpc_helper->Request(batch_waiter, parser, caller);
  }
  return;
}

void ProxyClient::ParserResponse(GridResponse* response, std::vector<GridQueryContext*>& contexts) {
  if (response == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] response is nullptr";
    return;
  }
  if (response->results_size() == 0) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] response is empty";
    return;
  }
  if (response->results_size() != contexts.size()) {
    LOG(ERROR) << "[Grid SDK] response size not match " << response->results_size() << " vs "
               << contexts.size();
    return;
  }
  std::vector<uint32_t>* table_index = nullptr;
  for (int i = 0; i < response->results_size(); ++i) {
    GridQueryContext* ctx = contexts[i];
    if (UNLIKELY(ctx == nullptr)) {
      LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] search_context is nullptr";
      continue;
    }
    if (UNLIKELY(ctx->result == nullptr)) {
      LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] search_result is nullptr";
      continue;
    }
    proto::GridResult* grid_result = response->mutable_results(i);
    if (grid_result->blocks_size() == 0) {
      LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] grid_result is empty";
      continue;
    }
    for (int j = 0; j < grid_result->blocks_size(); ++j) {
      ctx->result->add_blocks(grid_result->blocks(j));
    }
    ctx->result->mutable_offsets()->Add(grid_result->offsets().begin(), grid_result->offsets().end());
  }
  if (FLAGS_enable_grid_sdk_cache) {
    for (int i = response->results_size()-1; i >= 0; --i) {
      GridQueryContext* ctx = contexts[i];
      if (ctx == nullptr) {
        LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] search_context is nullptr";
        continue;
      }
      ctx->grid_results.push_back(std::move(*(response->mutable_results(i))));
    }
  }
}

}  // namespace grid
}  // namespace ks
