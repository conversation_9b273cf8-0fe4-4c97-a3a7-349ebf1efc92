#pragma once
#include <functional>
#include <memory>
#include <string>
#include <vector>

#include "kess/rpc/batch_waiter.h"
#include "kess/rpc/grpc/options.h"
#include "teams/ad/grid/client/grid_query_context.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/sdk/grid_query.h"
#include "teams/ad/grid/client/rpc/compress_type.h"

using ::ks::grid::proto::GridRequest;
using ::ks::grid::proto::GridResponse;
using ::ks::grid::proto::KVQuery;
using TKey = uint64_t;


/// RpcHelper 负责请求的构造和发送
namespace ks {
namespace grid {
class RpcHelper {
 public:
  using Filter = std::function<bool(int)>;
  using Parser = std::function<void(GridResponse*)>;
  using ParserWithCtx = std::function<void(GridResponse*, std::vector<GridQueryContext*>&)>;  // NOLINT

  void AddIndexQuery(const GridQuery& query, Filter filter, GridQueryContext* ctx);
  void AddIndexQuery(const proto::GridQuery& query, GridQueryContext* ctx);

  void AddForwardQuery(const GridQuery& query, Filter filter, GridQueryContext* ctx);
  void AddForwardQuery(const proto::GridQuery& query, Filter filter, GridQueryContext* ctx);

  void Request(ks::kess::rpc::BatchWaiter* waiter, ParserWithCtx parser, const std::string& caller = "");

  void SetVersion(ks::grid::proto::Version version) {
    for (auto& req : requests_) {
      req.set_version(version);
    }
  }

  void SetClientCompressType(const std::string& algorithm) {
    compress_type_ = GetClientCompressionAlgorithm(algorithm);
  }

  void SetTimeoutMs(int32_t timeout_ms) {
    timeout_ms_ = timeout_ms;
  }

  void SetServerCompressType(const std::string& algorithm) {
    response_compress_type_ = algorithm;
  }

  void Perf();

  explicit RpcHelper(const ks::grid::proto::TableMeta& meta);
  ~RpcHelper() {
    Perf();
  }

 private:
  std::string ksn_;
  int32_t shard_number_;
  std::string prefix_;
  std::vector<GridRequest> requests_;
  std::vector<GridResponse> responses_;

  int32_t timeout_ms_ = 0;
  ks::kess::rpc::CompressionAlgorithm compress_type_ = ks::kess::rpc::CompressionAlgorithm::COMPRESS_NONE;
  std::string response_compress_type_;

  std::vector<std::vector<GridQueryContext*>> ref_ctx_;

  uint32_t req_cnt_ = 0;
  uint32_t req_success_cnt_ = 0;
};

}  // namespace grid
}  // namespace ks
