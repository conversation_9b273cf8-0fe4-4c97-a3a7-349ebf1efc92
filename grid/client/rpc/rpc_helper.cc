#include "teams/ad/grid/client/rpc/rpc_helper.h"

#include <fcntl.h>

#include <climits>
#include <cstring>
#include <utility>
#include <vector>

#include "gflags/gflags.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/msg.kess.grpc.pb.h"
#include "teams/ad/grid/proto/msg.pb.h"
#include "teams/ad/grid/utils/metric.h"

static const int K128_BYTE_SIZE = 16;
static const int K64_BYTE_SIZE = 8;
/// RpcHelper 负责请求的构造和发送
namespace ks {
namespace grid {
RpcHelper::RpcHelper(const ks::grid::proto::TableMeta& meta) {
  ksn_ = meta.ksn();
  shard_number_ = meta.shard_num();
  prefix_ = meta.prefix();
  if (ksn_.empty()) {
    LOG(ERROR) << "[Grid SDK] ksn is empty";
    ksn_ = "ad-grid-service-offline";
  }
  if (shard_number_ <= 0) {
    LOG(ERROR) << "[Grid SDK] shard_number is invalid";
    shard_number_ = 1;
  }
  requests_.resize(shard_number_);
  for (auto& request : requests_) { request.set_type(proto::ProtoType::FB); }
  ref_ctx_.resize(shard_number_);
  responses_.resize(shard_number_);
}

void RpcHelper::AddIndexQuery(const GridQuery& query, Filter filter, GridQueryContext* ctx) {
  std::string data;
  data.resize(K128_BYTE_SIZE * (query.ids.size() - ctx->cache_hit));
  char* dst = &data[0];
  for (int i = 0; i < query.ids.size(); i++) {
    if (!filter(i)) {
      const Querykey& key = query.ids[i];
      memcpy(dst, &key.i128.low, sizeof(int64_t));
      dst += sizeof(int64_t);
      memcpy(dst, &key.i128.high, sizeof(int64_t));
      dst += sizeof(int64_t);

      ctx->pos2remote.push_back(i);
    }
  }
  for (int i = 0; i < shard_number_; i++) {
    GridRequest& request = requests_[i];
    request.set_version(::ks::grid::proto::Version::V2);
    auto kv_query = request.add_grid_querys();
    kv_query->mutable_select_fields()->Assign(query.select_fields.begin(), query.select_fields.end());
    kv_query->set_table_name(query.table_name);
    kv_query->set_index_name(query.index_name);
    kv_query->set_md5(query.md5);
    kv_query->set_limit(query.limit);
    kv_query->set_op(query.op);
    kv_query->set_posting_list_length_limit(query.posting_list_length_limit);

    auto key_array = kv_query->mutable_keys();
    key_array->set_width(K128_BYTE_SIZE);
    if (i == shard_number_ - 1) {
      key_array->set_buf(std::move(data));
    } else {
      key_array->set_buf(data);
    }
    ref_ctx_[i].push_back(ctx);
  }
}

void RpcHelper::AddIndexQuery(const proto::GridQuery& query,
                              GridQueryContext* ctx) {
  for (int i = 0; i < shard_number_; i++) {
    GridRequest& request = requests_[i];
    auto grid_query = request.add_grid_querys();
    grid_query->CopyFrom(query);
    ref_ctx_[i].push_back(ctx);
  }
}

void RpcHelper::AddForwardQuery(const GridQuery& query, Filter filter, GridQueryContext* ctx) {
  std::vector<std::vector<int64_t>> ids(shard_number_);
  for (int i = 0; i < query.ids.size(); i++) {
    if (!filter(i)) {
      uint64_t key = query.ids[i].i64;
      ids[key % shard_number_].push_back(key);
    }
  }

  for (int i = 0; i < shard_number_; i++) {
    if (ids[i].empty()) {
      continue;
    }
    GridRequest& request = requests_[i];
    request.set_version(::ks::grid::proto::Version::V2);
    auto kv_query = request.add_grid_querys();
    kv_query->mutable_select_fields()->Assign(query.select_fields.begin(), query.select_fields.end());
    kv_query->set_table_name(query.table_name);
    kv_query->set_md5(query.md5);

    std::string buf;
    std::vector<int64_t>& data = ids[i];
    size_t byte_size = data.size() * sizeof(int64_t);
    buf.resize(byte_size);
    memcpy(&buf[0], data.data(), byte_size);

    auto key_array = kv_query->mutable_keys();
    key_array->set_width(K64_BYTE_SIZE);
    key_array->set_buf(std::move(buf));
    ref_ctx_[i].push_back(ctx);
  }
}

void RpcHelper::AddForwardQuery(const proto::GridQuery& query, Filter filter, GridQueryContext* ctx) {
  std::vector<std::vector<int64_t>> ids(shard_number_);
  absl::Span<const int64_t> search_keys(reinterpret_cast<const int64_t*>(query.keys().buf().data()),
                                        ctx->query_size);
  for (int i = 0; i < search_keys.size(); i++) {
    if (!filter(i)) {
      uint64_t key = search_keys[i];
      ids[key % shard_number_].push_back(key);
    }
  }

  for (int i = 0; i < shard_number_; i++) {
    if (ids[i].empty()) {
      continue;
    }
    GridRequest& request = requests_[i];
    auto kv_query = request.add_grid_querys();
    kv_query->mutable_select_fields()->CopyFrom(query.select_fields());
    kv_query->set_table_name(query.table_name());
    kv_query->set_md5(query.md5());

    std::string buf;
    std::vector<int64_t>& data = ids[i];
    size_t byte_size = data.size() * sizeof(int64_t);
    buf.resize(byte_size);
    memcpy(&buf[0], data.data(), byte_size);

    auto key_array = kv_query->mutable_keys();
    key_array->set_width(K64_BYTE_SIZE);
    key_array->set_buf(std::move(buf));
    ref_ctx_[i].push_back(ctx);
  }
}

void RpcHelper::Request(ks::kess::rpc::BatchWaiter* waiter, ParserWithCtx parser, const std::string& caller) {
  if (shard_number_ > 1) {
    for (int shard_id = 0; shard_id < shard_number_; shard_id++) {
      auto callback = [this, parser, shard_id](const ::grpc::Status& status,
                                               ks::grid::proto::GridResponse* one_resp) {
        if (!status.ok()) {
          LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] call " << this->ksn_
                                   << " response failed, status: " << status.error_code()
                                   << " message: " << status.error_message()
                                   << " shard_id: " << shard_id
                                   << " prefix:" << this->prefix_;
          return;
        }
        // batch_waiter 本地单线程执行 callback ，无需加锁
        if (one_resp == nullptr) {
          return;
        }
        ++req_success_cnt_;
        auto& search_contexts = ref_ctx_[shard_id];
        parser(one_resp, search_contexts);
      };
      auto& request = requests_[shard_id];
      if (request.querys_size() == 0 && request.grid_querys_size() == 0) {
        continue;
      }
      request.set_caller(caller);
      request.set_response_compress_type(response_compress_type_);
      ++req_cnt_;
      std::string shard_id_str = absl::StrCat(prefix_, shard_id);
      waiter->Add(AD_KESS_CLIENT_ASYNC_BYSHARD_COMPRESS(ks::grid::proto::kess::GridService, BatchGetTable,
                                                       shard_id_str, SelectOne(), ksn_, request,
                                                       &(responses_[shard_id]), timeout_ms_, compress_type_),
                    callback);
    }
  } else {
    auto callback = [this, parser](const ::grpc::Status& status, ks::grid::proto::GridResponse* one_resp) {
      if (!status.ok()) {
        LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] call " << this->ksn_
                                 << " response failed, status: " << status.error_code()
                                 << " message: " << status.error_message()
                                 << " prefix:" << this->prefix_;
        return;
      }
      // batch_waiter 本地单线程执行 callback ，无需加锁
      if (one_resp == nullptr) {
        return;
      }
      ++req_success_cnt_;
      auto& search_contexts = ref_ctx_[0];
      parser(one_resp, search_contexts);
    };
    auto& request = requests_[0];
    if (request.querys_size() == 0 && request.grid_querys_size() == 0) {
      return;
    }
    request.set_caller(caller);
    request.set_response_compress_type(response_compress_type_);
    ++req_cnt_;
    if (prefix_.empty()) {
      waiter->Add(
          AD_KESS_CLIENT_ASYNC_COMPRESS(ks::grid::proto::kess::GridService, BatchGetTable, SelectOne(), ksn_,
                                        request, &(responses_[0]), timeout_ms_, compress_type_),
          callback);
    } else {
      std::string shard_id_str = absl::StrCat(prefix_, 0);
      waiter->Add(AD_KESS_CLIENT_ASYNC_BYSHARD_COMPRESS(ks::grid::proto::kess::GridService, BatchGetTable,
                                                        shard_id_str, SelectOne(), ksn_, request,
                                                        &(responses_[0]), timeout_ms_, compress_type_),
                  callback);
    }
  }
}

void RpcHelper::Perf() {
  Mertric::StatInfo& stat_info = Mertric::GetThreadStatInfo("");
  Mertric::RpcStat& stat = stat_info.downstreams[ksn_];
  stat.req_count += req_cnt_;
  stat.success_count += req_success_cnt_;
  stat_info.Report("");
}

}  // namespace grid
}  // namespace ks
