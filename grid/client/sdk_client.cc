#include "teams/ad/grid/client/sdk_client.h"

#include <cstdint>
#include <memory>
#include <sstream>
#include <utility>
#include <vector>
#include <queue>
#include <algorithm>

#include "kess/rpc/batch_waiter.h"
#include "rpc/rpc_helper.h"
#include "teams/ad/grid/cache/cache_list.h"
#include "teams/ad/grid/proto/common.pb.h"
#include "teams/ad/grid/proto/grid_generated.h"
#include "teams/ad/grid/sdk/grid_data.h"
#include "teams/ad/grid/sdk/grid_query.h"
#include "teams/ad/grid/client/grid_query_config.h"
#include "teams/ad/grid/client/grid_query_context.h"
#include "teams/ad/grid/config/traffic_router_config.h"

DECLARE_bool(enable_grid_sdk_cache);

namespace ks {
namespace grid {
void SelectKElementsBFS(const std::vector<std::vector<FbRow>>& blocks_data, std::vector<FbRow>* result,
                        int k) {
  std::queue<std::pair<int, int>> q;  // 存储<block_index, element_index>
  for (int i = 0; i < blocks_data.size(); i++) {
    if (!blocks_data[i].empty()) {
      q.push({i, 0});
    }
  }
  while (!q.empty() && result->size() < k) {
    int sz = q.size();
    for (int i = 0; i < sz && result->size() < k; i++) {
      auto [block_idx, elem_idx] = q.front();
      q.pop();
      if (block_idx < blocks_data.size() && elem_idx < blocks_data[block_idx].size()) {
        result->push_back(blocks_data[block_idx][elem_idx]);
        std::pair<int, int> next = {block_idx, elem_idx + 1};
        q.push(next);
      }
    }
  }
  return;
}

StatusCode SDKClient::Init(const std::vector<GridQuery>& querys) {
  const auto& table_name_map = GridKconf::TableRouterConfig()->data().GetTable();
  search_contexts_.resize(querys.size());
  for (int i = 0; i < querys.size(); ++i) {
    auto& query = querys[i];
    GridQueryContext& ctx = search_contexts_[i];
    if (query.table_name.empty() || query.select_fields.size() == 0) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name or select_fields is empty"
                               << " query:" << query.DebugString();
      return StatusCode::ARGUMENT_ERROR;
    }
    // 空 id 不下发 不算参数错误
    if (query.ids.empty()) {
      ctx.is_valid = false;
      continue;
    }
    if (query.index_name.empty()) {
      ctx.data = std::make_shared<std::vector<FbRow>>(query.ids.size());
    } else {
      if (query.limit <= 0 && query.posting_list_length_limit <= 0) {
        LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] limit is empty: " << query.DebugString();
        return StatusCode::ARGUMENT_ERROR;
      }
      if (query.op == proto::IndexOP::UNION_ALL) {
        ctx.indexkeys.reserve(query.ids.size());
        ctx.pos2remote.reserve(query.ids.size());
        ctx.index2rows.reserve(query.ids.size());
        for (auto& id : query.ids) {
          ctx.indexkeys.emplace_back(id.i128.low, id.i128.high);
          ctx.index2rows.emplace_back(std::make_shared<std::vector<FbRow>>());
        }
      }
      ctx.op = query.op;
      ctx.limit = query.limit;
      ctx.posting_list_length_limit = query.posting_list_length_limit;
      ctx.data = std::make_shared<std::vector<FbRow>>();
    }
    ctx.query_size = query.ids.size();
    ctx.table_name = query.table_name;
    ctx.index_name = query.index_name;

    if (table_name_map.find(query.table_name) == table_name_map.end()) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name=" << query.table_name
                               << " not found in router_option";
      return StatusCode::RPC_CONFIG_ERROR;
    }
    auto ksn_meta = table_name_map.at(query.table_name);
    if (GridKconf::EnableTrafficRouter()) {
      const std::string &ksn = ksn_meta.ksn();
      bool res = GridKconf::TrafficRouterConfig()->data().SelectKsnMeta(ksn, ksn_meta);
      if (!res) {
        LOG(ERROR) << "[Grid SDK] traffic router config not found, ksn=" << ksn
                    << " , use default config";
        return StatusCode::RPC_CONFIG_ERROR;
      }
    }
    if (ksn_rpc_helper_.find(ksn_meta.ksn()) == ksn_rpc_helper_.end()) {
      std::shared_ptr<RpcHelper> helper = std::make_shared<RpcHelper>(ksn_meta);
      helper->SetTimeoutMs(grid_query_config_.timeout_ms);
      helper->SetClientCompressType(grid_query_config_.client_compress_type);
      helper->SetServerCompressType(grid_query_config_.response_compress_type);
      ksn_rpc_helper_[ksn_meta.ksn()] = helper;
    }
    rpc_helper_[query.table_name] = ksn_rpc_helper_[ksn_meta.ksn()];
  }
  batch_waiter_ = std::make_shared<ks::kess::rpc::BatchWaiter>();
  return StatusCode::OK;
}

StatusCode SDKClient::BatchGetTable(const std::vector<GridQuery>& querys) {
  if (querys.size() == 0) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] querys size is 0";
    return StatusCode::ARGUMENT_ERROR;
  }
  auto ret = Init(querys);
  if (ret != StatusCode::OK) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] Prepare failed";
    return ret;
  }
  for (uint32_t i = 0; i < querys.size(); ++i) {
    BuildRequest(querys[i], &search_contexts_[i]);
  }
  Request(batch_waiter_.get(), &ksn_rpc_helper_);
  return StatusCode::OK;
}

StatusCode SDKClient::Wait(std::vector<GridData>* resp) {
  if (resp == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] resp is nullptr";
    return StatusCode::ARGUMENT_ERROR;
  }
  if (batch_waiter_ == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] batch_waiter_ is nullptr";
    return StatusCode::BATCH_WAITER_NOT_INIT;
  }
  batch_waiter_->Wait();
  for (int i = 0; i < search_contexts_.size(); ++i) { FillResult(&search_contexts_[i]); }
  resp->clear();
  resp->resize(search_contexts_.size());
  for (int i = 0; i < search_contexts_.size(); ++i) {
    GridQueryContext& ctx = search_contexts_[i];
    if (ctx.is_valid == false) {
      continue;
    }
    auto& table_name = search_contexts_[i].table_name;
    auto res = (*resp)[i].ResetFB(ctx.data, ctx.column_desc, table_name);
    if (!res) {
      bool data_null = ctx.data == nullptr;
      bool column_desc_null = ctx.column_desc == nullptr;
      LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] table_name:" << table_name << " index_name:" << ctx.index_name
                               << " ResetFB failed, data_null:" << data_null
                               << " column_desc_null:" << column_desc_null;
      continue;
    }
    if (ctx.op == proto::IndexOP::UNION_ALL) {
      (*resp)[i].SetOffset(std::move(ctx.offsets));
    }
  }
  if (grid_query_config_.auto_update_cache) {
    UpdateCache();
  }
  return StatusCode::OK;
}

void SDKClient::Clear() {
  search_contexts_.clear();
  rpc_helper_.clear();
  ksn_rpc_helper_.clear();
  batch_waiter_.reset();
}

void SDKClient::BuildRequest(const GridQuery& query, GridQueryContext* ctx) {
  if (ctx->is_valid == false) {
    LOG_EVERY_N(INFO, 1024) << "[Grid SDK] " << query.table_name << " is_valid is false";
    return;
  }
  auto rpc_itr = rpc_helper_.find(query.table_name);
  if (rpc_itr == rpc_helper_.end()) {
    LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] " << query.table_name << " rpc_helper is nullptr";
    return;
  }
  // 请求不下发
  if (query.ids.empty()) {
    LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] " << query.table_name << " ids is empty";
    return;
  }
  auto& rpc_helper = rpc_itr->second;
  if (!query.index_name.empty()) {
    if (query.op == proto::IndexOP::UNION_ALL && FLAGS_enable_grid_sdk_cache &&
        query.posting_list_length_limit > 0 && GridKconf::EnableIndexCache()) {
      TryGetFromIndexCache(query, ctx);
      if (ctx->cache_hit == query.ids.size()) {
        LOG_EVERY_N(INFO, 8192) << "[Grid SDK] table_name:  " << query.table_name
                                << " index_name: " << query.index_name << " cache hit";
        return;
      }
      auto filter = [&cache = ctx->index2rows](int32_t i) { return !(cache[i]->empty()); };
      rpc_helper->AddIndexQuery(query, filter, ctx);
    } else {
      rpc_helper->AddIndexQuery(query, [](int32_t i) { return false; }, ctx);
    }
  } else {
    for (int i = 0; i < query.ids.size(); ++i) {
      ctx->pos_index_map[query.ids[i].i64].push_back(i);
    }
    if (FLAGS_enable_grid_sdk_cache) {
      TryGetFromCache(query, ctx);
      if (ctx->cache_hit == query.ids.size()) {
        LOG_EVERY_N(INFO, 8192)<< "[Grid SDK] " << query.table_name << " cache hit";
        return;
      }
      auto filter = [cache = ctx->data](int32_t i) {
        return (*cache)[i];
      };
      rpc_helper->AddForwardQuery(query, filter, ctx);
    } else {
      rpc_helper->AddForwardQuery(query, [](int32_t i) { return false; }, ctx);
    }
  }
}

void SDKClient::Request(ks::kess::rpc::BatchWaiter* batch_waiter,
                            absl::flat_hash_map<std::string, std::shared_ptr<RpcHelper>>* ksn_rpc_helper) {
  auto parser = [this](GridResponse* response, std::vector<GridQueryContext*>& ctxs) {
    this->ParserResponse(response, ctxs);
  };

  for (auto& [ksn, rpc_helper] : *ksn_rpc_helper) {
    if (rpc_helper == nullptr) {
      LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] rpc_helper is nullptr";
      continue;
    }

    rpc_helper->Request(batch_waiter, parser);
  }
  return;
}

void SDKClient::TryGetFromCache(const GridQuery& query, GridQueryContext* ctx) {
  if (query.ids.size() == 0) {
    return;
  }

  auto cache_ptr = CacheList<FbRow>::Instance()
    .Cache(query.table_name, query.md5)
    .value_or(nullptr);

  if (cache_ptr == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] table_name="
                              << query.table_name << " cache instance not Found";
    return;
  }
  ctx->cache = cache_ptr;
  std::vector<TKey> keys;
  keys.resize(query.ids.size());
  for (int j = 0; j < query.ids.size(); ++j) {
    keys[j] = query.ids[j].i64;
  }
  int ret = cache_ptr->BatchGet(keys, ctx->data.get());
  ctx->cache_hit = ret;
  ctx->keys2cache.reserve(keys.size() - ret);
  ctx->rows2cache.reserve(keys.size() - ret);
  ctx->column_desc = cache_ptr->GetTableSchema();
}

void SDKClient::TryGetFromIndexCache(const GridQuery& query, GridQueryContext* ctx) {
  if (query.ids.size() == 0) {
    return;
  }
  auto cache_ptr = CacheList<IndexValueSP, IndexKey>::Instance()
                       .Cache(query.table_name + ":" + query.index_name, query.md5)
                       .value_or(nullptr);
  if (cache_ptr == nullptr) {
    LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] table_name=" << query.table_name
                              << " index_name=" << query.index_name << " cache instance not Found";
    return;
  }
  ctx->index_cache = cache_ptr;
  ctx->cache_hit = cache_ptr->BatchGet(ctx->indexkeys, &(ctx->index2rows));
  ctx->column_desc = cache_ptr->GetTableSchema();
  ctx->indexkeys2cache.reserve(ctx->indexkeys.size() - ctx->cache_hit);
  ctx->indexrows2cache.reserve(ctx->indexkeys.size() - ctx->cache_hit);
}

void SDKClient::UpdateCache() {
  for (GridQueryContext& ctx : search_contexts_) {
    auto& keys = ctx.keys2cache;
    auto& rows = ctx.rows2cache;
    auto cache_ptr = ctx.cache;
    auto index_cache_ptr = ctx.index_cache;
    if (cache_ptr) {
      cache_ptr->BatchSet(keys, rows);
    }
    if (index_cache_ptr) {
      index_cache_ptr->BatchSet(ctx.indexkeys2cache, ctx.indexrows2cache);
    }
  }
}

void SDKClient::ParserResponse(GridResponse* response, std::vector<GridQueryContext*>& search_contexts) {
  if (response == nullptr) {
    LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] response is nullptr";
    return;
  }
  if (response->results_size() == 0) {
    LOG_EVERY_N(ERROR, 1024) << "[Grid SDK] response is empty";
    return;
  }
  if (search_contexts.size() != response->results_size()) {
    LOG(ERROR) << "[Grid SDK] search_contexts.size() != response->results_size()";
    return;
  }

  for (int i = 0; i < response->results_size(); ++i) {
    auto ctx = search_contexts[i];
    if (ctx == nullptr) {
      LOG(ERROR) << "[Grid SDK] search_contexts[i] is nullptr";
      continue;
    }
    auto grid_result = response->mutable_results(i);
    ctx->grid_results.push_back(*grid_result);
  }
}

void SDKClient::FillResult(GridQueryContext* ctx) {
  if (ctx->is_valid == false || ctx->table_name.empty()) {
    return;
  }
  if (ctx->index_name.empty()) {
    ParserForwardResult(ctx);
  } else {
    switch (ctx->op) {
      case proto::IndexOP::UNION: {
        ParserIndexUnionResult(ctx);
        break;
      };
      case proto::IndexOP::UNION_ALL: {
        ParserIndexUnionALLResult(ctx);
        break;
      };
      default : {
        LOG(ERROR) << "[Grid SDK] unknown op, " << ctx->op
                    << ", table_name" << ctx->table_name
                    << ", index_name" << ctx->index_name;
        break;
      }
    }
  }
}

void SDKClient::ParserIndexUnionResult(GridQueryContext* ctx) {
  if (ctx->grid_results.empty()) {
    return;
  }
  auto block_size = 0;
  for (auto& grid_result : ctx->grid_results) {
    block_size += grid_result.blocks_size();
  }
  std::vector<std::vector<FbRow>> blocks_data(block_size);
  uint32_t index = 0;
  auto& table_cols = ctx->column_desc;
  for (auto& grid_result : ctx->grid_results) {
    for (int i = grid_result.blocks_size()-1; i >= 0; --i) {
      std::vector<FbRow>& table_data = blocks_data[index++];
      table_data.reserve(ctx->limit);

      auto block = std::shared_ptr<std::string>(grid_result.mutable_blocks()->ReleaseLast());
      auto fb_table = flatbuffers::GetRoot<flat::GridTable>(block->data());
      if (fb_table == nullptr || fb_table == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, data=" << *block;
        continue;
      }
      if (fb_table->name()->size() == 0 || fb_table->columns() == 0 || fb_table->rows() == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, table is empty, name_size:"
                                  << fb_table->name()->size()
                                  << " columns_size:" << fb_table->columns()->size()
                                  << " rows_size:" << fb_table->rows()->size();
        continue;
      }
      auto col_size = fb_table->columns()->size();
      auto row_size = fb_table->rows()->size();

      for (int row_id = 0; row_id < row_size; ++row_id) {
        auto row = fb_table->rows()->Get(row_id);
        auto row_data = const_cast<flatbuffers::Vector<flatbuffers::Offset<flat::Value>>*>(row->data());

        FbRow fb_row;
        if (row_data != 0 && row_data->size() == col_size) {
          fb_row.guard = block;
          fb_row.row = row_data;
          table_data.emplace_back(std::move(fb_row));
        }
      }
      if (table_cols == nullptr) {
        auto columns = fb_table->columns();
        table_cols = std::make_shared<std::vector<ColumnDesc>>(columns->size());
        for (size_t i = 0; i < columns->size(); ++i) {
          const auto& column = columns->Get(i);
          (*table_cols)[i].col_name = column->name()->str();
          (*table_cols)[i].type = ad_index_meta::proto::ColumnType(static_cast<int>(column->type()));
        }
      }
    }
  }

  SelectKElementsBFS(blocks_data, ctx->data.get(), ctx->limit);
  return;
}

void SDKClient::ParserIndexUnionALLResult(GridQueryContext* ctx) {
  if ((ctx->cache_hit == 0 && ctx->grid_results.empty()) || ctx->indexkeys.empty()) {
    return;
  }
  auto& indexkeys = ctx->indexkeys;
  auto& index2rows = ctx->index2rows;
  auto& table_cols = ctx->column_desc;
  auto index_cache_ptr = ctx->index_cache;
  for (auto& grid_result : ctx->grid_results) {
    uint32_t index = 0;
    for (int i = 0; i < grid_result.blocks_size(); ++i) {
      auto block = std::make_shared<std::string>(std::move(*(grid_result.mutable_blocks(i))));
      auto fb_table = flatbuffers::GetRoot<flat::GridTable>(block->data());
      if (fb_table == nullptr || fb_table == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, data=" << *block;
        continue;
      }
      if (fb_table->name()->size() == 0 || fb_table->columns() == 0 || fb_table->rows() == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, table is empty, name_size:"
                                  << fb_table->name()->size()
                                  << " columns_size:" << fb_table->columns()->size()
                                  << " rows_size:" << fb_table->rows()->size();
        continue;
      }
      auto col_size = fb_table->columns()->size();
      auto row_size = fb_table->rows()->size();

      for (int row_id = 0; row_id < row_size && index < grid_result.offsets_size(); ++row_id) {
        uint32_t cur_index = index++;
        auto row = fb_table->rows()->Get(row_id);
        auto row_data = const_cast<flatbuffers::Vector<flatbuffers::Offset<flat::Value>>*>(row->data());

        FbRow fb_row;
        if (row_data != 0 && row_data->size() == col_size) {
          uint32_t offset = grid_result.offsets(cur_index);
          if (KS_UNLIKELY(offset >= ctx->pos2remote.size())) {
            LOG(ERROR) << "[Grid SDK] parse response failed, index:" << cur_index << " offset:" << offset
                       << " pos2remote size:" << ctx->pos2remote.size() << " table_name:" << ctx->table_name
                       << " index_name:" << ctx->index_name;
            continue;
          }
          int32_t index = ctx->pos2remote[offset];
          fb_row.guard = block;
          fb_row.row = row_data;
          index2rows[index]->emplace_back(std::move(fb_row));
        }
      }
      if (index_cache_ptr) {
        if (index_cache_ptr->NeedInit()) {
          index_cache_ptr->Init(fb_table->columns());
        }
        if (table_cols == nullptr) {
          table_cols = index_cache_ptr->GetTableSchema();
        }
      }
      if (table_cols == nullptr) {
        auto columns = fb_table->columns();
        table_cols = std::make_shared<std::vector<ColumnDesc>>(columns->size());
        for (size_t i = 0; i < columns->size(); ++i) {
          const auto& column = columns->Get(i);
          (*table_cols)[i].col_name = column->name()->str();
          (*table_cols)[i].type = ad_index_meta::proto::ColumnType(static_cast<int>(column->type()));
        }
      }
    }
  }
  // TODO(@huhao11): 补充 limit 逻辑
  auto& offsets = ctx->offsets;
  offsets.resize(ctx->indexkeys.size() + 1, 0);
  for (int i = 0; i < indexkeys.size(); ++i) {
    auto& index2row = index2rows[i];
    uint32_t result_size = index2row->size();
    if (ctx->posting_list_length_limit > 0) {
      result_size = std::min(result_size, ctx->posting_list_length_limit);
    }
    offsets[i+1] = offsets[i] + result_size;
    for (int j = 0; j < result_size; ++j) {
      ctx->data->emplace_back((*index2row)[j]);
    }
  }
  if (ctx->index_cache) {
    for (int32_t pos : ctx->pos2remote) {
      if (ctx->index2rows[pos]->empty()) {
        continue;
      }
      ctx->indexkeys2cache.emplace_back(indexkeys[pos]);
      ctx->indexrows2cache.emplace_back(ctx->index2rows[pos]);
    }
  }
}

void SDKClient::ParserForwardResult(GridQueryContext* ctx) {
  if (ctx->grid_results.empty() || ctx->query_size == ctx->cache_hit) {
    return;
  }

  auto& table_data = ctx->data;
  auto& table_cols = ctx->column_desc;
  auto& pos_index = ctx->pos_index_map;
  auto& keys2cache = ctx->keys2cache;
  auto& rows2cache = ctx->rows2cache;
  auto cache_ptr = ctx->cache;
  if (table_data == nullptr) {
    return;
  }
  for (auto& grid_result : ctx->grid_results) {
    for (int i = grid_result.blocks_size()-1; i >= 0; --i) {
      auto data = std::shared_ptr<std::string>(grid_result.mutable_blocks()->ReleaseLast());
      auto fb_table = flatbuffers::GetRoot<flat::GridTable>(data->data());
      if (fb_table == nullptr || fb_table == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, data=" << *data;
        continue;
      }
      if (fb_table->name()->size() == 0 || fb_table->columns() == 0 || fb_table->rows() == 0) {
        LOG_EVERY_N(ERROR, 1000) << "[Grid SDK] parse response failed, table is empty, name_size:"
                                  << fb_table->name()->size()
                                  << " columns_size:" << fb_table->columns()->size()
                                  << " rows_size:" << fb_table->rows()->size();
        continue;
      }
      auto col_size = fb_table->columns()->size();
      auto row_size = fb_table->rows()->size();
      for (int row_id = 0; row_id < row_size; ++row_id) {
        auto row = fb_table->rows()->Get(row_id);
        int64_t id = row->id();
        auto row_data = const_cast<flatbuffers::Vector<flatbuffers::Offset<flat::Value>>*>(row->data());
        FbRow fb_row;
        auto& id_index = pos_index[id];
        if (row_data != 0 && row_data->size() == col_size) {
          for (uint32_t pos_id : id_index) {
            (*table_data)[pos_id].guard = data;
            (*table_data)[pos_id].row = row_data;
          }
          fb_row.guard = data;
          fb_row.row = row_data;
        }
        if (!id_index.empty() && cache_ptr) {
          keys2cache.push_back(id);
          rows2cache.push_back(fb_row);
        }
        id_index.clear();
      }
      if (cache_ptr) {
        if (cache_ptr->NeedInit()) {
          cache_ptr->Init(fb_table->columns());
        }
        if (table_cols == nullptr) {
          table_cols = cache_ptr->GetTableSchema();
        }
      }
      if (table_cols == nullptr) {
        auto columns = fb_table->columns();
        table_cols = std::make_shared<std::vector<ColumnDesc>>(columns->size());
        for (size_t i = 0; i < columns->size(); ++i) {
          const auto& column = columns->Get(i);
          (*table_cols)[i].col_name = column->name()->str();
          (*table_cols)[i].type = ad_index_meta::proto::ColumnType(static_cast<int>(column->type()));
        }
      }
    }
  }
}

}  // namespace grid
}  // namespace ks
