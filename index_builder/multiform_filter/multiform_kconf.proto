syntax = "proto3";

package ks.index_builder;

message TableList {
  message Fields {
    repeated string fields = 1;
  }
  map<string, Fields> table_white_lists = 1; // Enum -> fields
  map<string, Fields> table_black_lists = 2; // Enum -> fields
}

message StyleServerIndexBlackFilter {
  message BlackList {
    repeated int64 ad_style_material__style_type = 1;
    repeated int64 ad_matrix_style_material__type = 2;
    repeated int64 ad_esp_product_label_info__type = 3;
  }
  map<string, BlackList> black_configs = 1;
}

message ProtoList {
  message Fields {
    repeated string fields = 1;
  }
  map<string, Fields> proto_white_lists = 1;  // type_name -> fields
  map<string, Fields> proto_black_lists = 2;  // type_name -> fields
}

message MultiProtoList {
  map<string, ProtoList> multi_conf = 1;
}

message DeployParam {
  enum Param {
    UNKNOWN_DEPLOY_PARAM = 0;
    ORIGINAL = 1;
    EXT1 = 2;
    EXT2 = 3;
    EXT3 = 4;
    EXT4 = 5;
    ALL = 6;
  }
}

// 联盟低耗时部署
message UniverseSwiftIndexFilterConfig {
  bool enable_ocpx_white_list = 1;
  repeated int32 ocpx_white_list = 2;
  bool enable_campaign_type_black_list = 3;
  repeated int32 campaign_type_black_list = 4;
}

// 联盟屏蔽广告类型配置
message UniverseFilteredRoaringTypeItem {
  message DeepOcpxMixType {
    int32 mix_deep_type = 1;
    int32 mix_ocpx_type = 2;
  }
  repeated int64 bid_type = 1;
  repeated int64 ocpx_type = 2;
  repeated int64 deep_type = 3;
  repeated DeepOcpxMixType ocpx_deepconv_list = 4;
}
message UniverseFilteredRoaringType {
  map<string, UniverseFilteredRoaringTypeItem> items = 1;
}
