#include "teams/ad/index_builder/multiform_filter/ad_filter.h"

#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <vector>
#include "absl/base/call_once.h"
#include "absl/strings/str_join.h"
#include "perfutil/perfutil.h"
#include "base/hash_function/city.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/pid_tag_enum.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/tables/all.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/index_builder/multiform_filter/adapter_creative_filter.h"
#include "teams/ad/index_builder/multiform_filter/fanstop_filter.h"
#include "teams/ad/index_builder/multiform_filter/programmed_creative_filter.h"
#include "teams/ad/index_builder/multiform_filter/search_filter.h"
#include "teams/ad/index_builder/utils/cache_loader/rtb_white_list_no_smr.h"
#include "teams/ad/index_builder/multiform_filter/multiform_kconf.h"
#include "teams/ad/engine_base/material_feature_type/material_feature_type.h"
#include "teams/ad/index_builder/multiform_filter/sf_micro.h"
namespace ks {
namespace index_builder {
using ks::infra::PerfUtil;

static bool enableRtbWhiteList = false;
static bool enableUniverseSupportSplashOnlyData = false;
static bool enableTargetSearchQuickSearchData = false;
static bool enableTargetSearchExtendSearchData = false;
static bool enableFlashPromotionPhotoInSearch = false;
static bool enableUniverseFilterItemCard = false;
static bool enableEspMobileAccount = false;
static bool enable_universe_support_esp_mobile = false;
static bool enable_universe_support_kwai_series = false;
static bool enableUniverseTinySearchData = false;
static bool enableUniverseSearchAllCampaign = false;
static bool enableUniverseInvalidTypeFilter = false;
static bool enableUniverseExtendSearch = false;
static bool enableUniverseBlockAigcModifyPhoto = false;
static bool enable_universe_tiny_filter = false;
static bool enableSearchBlockAigcModifyPhoto = false;
static bool enableSplashBlockAigcModifyPhoto = false;
// 联盟 DPA 账户白名单
static std::shared_ptr<::ks::infra::TailNumberV2> universe_dpa_account_white_list;
absl::once_flag init_once;

// 为了减少依赖，直接 copy 一份代码
class TargetKeyConvertor {
 public:
  int64_t operator()(const std::string& str) const {
    uint64_t uint64_sign = base::CityHash64(str.data(), str.size());
    return *(reinterpret_cast<int64_t*>(&uint64_sign));
  }
};
static const TargetKeyConvertor conv;

// used for creative config init
void InitOnce() {
  enableRtbWhiteList = MultiFormKconfUtil::enableRtbWhiteList();
  enableUniverseSupportSplashOnlyData = MultiFormKconfUtil::enableUniverseSupportSplashOnlyData();
  enableTargetSearchQuickSearchData = MultiFormKconfUtil::enableTargetSearchQuickSearchData();
  enableTargetSearchExtendSearchData = MultiFormKconfUtil::enableTargetSearchExtendSearchData();
  enableFlashPromotionPhotoInSearch = MultiFormKconfUtil::enableFlashPromotionPhotoInSearch();
  enableUniverseFilterItemCard = MultiFormKconfUtil::enableUniverseFilterItemCard();
  enableEspMobileAccount = MultiFormKconfUtil::enableEspMobileAccount();
  enable_universe_support_esp_mobile = MultiFormKconfUtil::enableUniverseSupportEspMobile();
  enable_universe_support_kwai_series  = MultiFormKconfUtil::enableUniverseSupportKwaiSerial();
  enableUniverseTinySearchData = MultiFormKconfUtil::enableUniverseTinySearchData();
  enableUniverseSearchAllCampaign = MultiFormKconfUtil::enableUniverseSearchAllCampaign();
  enableUniverseInvalidTypeFilter = MultiFormKconfUtil::enableUniverseInvalidTypeFilter();
  enableUniverseExtendSearch = MultiFormKconfUtil::enableUniverseExtendSearch();
  enableUniverseBlockAigcModifyPhoto = MultiFormKconfUtil::enableUniverseBlockAigcModifyPhoto();
  enableSearchBlockAigcModifyPhoto = MultiFormKconfUtil::enableSearchBlockAigcModifyPhoto();
  enableSplashBlockAigcModifyPhoto = MultiFormKconfUtil::enableSplashBlockAigcModifyPhoto();
  if (enableUniverseInvalidTypeFilter) {
    InitUniverseUnitInvalidTypeConfig();
  }
  enable_universe_tiny_filter = MultiFormKconfUtil::enableUniverseTinyFilter();
  universe_dpa_account_white_list = MultiFormKconfUtil::allianceDpaUserAccountId();
  InitUniverseSwiftConfig();
}

// 联盟明暗投通用过滤配置
static std::unordered_set<int64_t> bid_type_to_filter;
static std::unordered_set<int64_t> ocpx_type_to_filter;
static std::unordered_set<int64_t> deep_type_to_filter;
static std::unordered_map<int64_t, std::unordered_set<int64_t>> ocpx_deep_to_filter;
// 联盟暗投过滤配置
static std::unordered_set<int64_t> dark_bid_type_to_filter;
static std::unordered_set<int64_t> dark_ocpx_type_to_filter;
static std::unordered_set<int64_t> dark_deep_type_to_filter;
static std::unordered_map<int64_t, std::unordered_set<int64_t>> dark_ocpx_deep_to_filter;

void InitUniverseUnitInvalidTypeConfig() {
  auto configs = MultiFormKconfUtil::universeFilteredRoaringType();
  const auto &items = configs->data().items();
  const auto base_cfg_it = items.find("base");
  if (base_cfg_it != items.end()) {
    const auto &filtered_config = base_cfg_it->second;
    bid_type_to_filter.insert(filtered_config.bid_type().begin(), filtered_config.bid_type().end());
    ocpx_type_to_filter.insert(filtered_config.ocpx_type().begin(), filtered_config.ocpx_type().end());
    deep_type_to_filter.insert(filtered_config.deep_type().begin(), filtered_config.deep_type().end());
    for (auto &one_mix : filtered_config.ocpx_deepconv_list()) {
      ocpx_deep_to_filter[one_mix.mix_ocpx_type()].insert(one_mix.mix_deep_type());
    }
  }
  const auto dark_cfg_it = items.find("dark");
  if (dark_cfg_it != items.end()) {
    const auto &dark_config = dark_cfg_it->second;
    dark_bid_type_to_filter.insert(dark_config.bid_type().begin(), dark_config.bid_type().end());
    dark_ocpx_type_to_filter.insert(dark_config.ocpx_type().begin(), dark_config.ocpx_type().end());
    dark_deep_type_to_filter.insert(dark_config.deep_type().begin(), dark_config.deep_type().end());
    for (auto &one_mix : dark_config.ocpx_deepconv_list()) {
      dark_ocpx_deep_to_filter[one_mix.mix_ocpx_type()].insert(one_mix.mix_deep_type());
    }
  }
}

// 联盟低耗时部署相关配置
static bool enable_universe_swift_ocpx_white_list;
static std::unordered_set<int32_t> universe_swift_ocpx_white_list;
static bool enable_universe_swift_campaign_type_black_list;
static std::unordered_set<int32_t> universe_swift_campaign_type_black_list;

void InitUniverseSwiftConfig() {
  auto config = MultiFormKconfUtil::universeSwiftIndexFilterConfig();

  enable_universe_swift_ocpx_white_list = config->data().enable_ocpx_white_list();
  universe_swift_ocpx_white_list.clear();
  universe_swift_ocpx_white_list.insert(config->data().ocpx_white_list().begin(),
                                        config->data().ocpx_white_list().end());

  enable_universe_swift_campaign_type_black_list = config->data().enable_campaign_type_black_list();
  universe_swift_campaign_type_black_list.clear();
  universe_swift_campaign_type_black_list.insert(config->data().campaign_type_black_list().begin(),
                                                 config->data().campaign_type_black_list().end());
}

using kuaishou::ad::AdEnum;

using namespace kuaishou::ad::tables;  // NOLINT

static const std::unordered_set<AdEnum::AdInstanceType> esp_mobile_check_types = {
            kuaishou::ad::AdEnum::CREATIVE, kuaishou::ad::AdEnum::UNIT,
            kuaishou::ad::AdEnum::CAMPAIGN, kuaishou::ad::AdEnum::ACCOUNT};
static const std::unordered_set<std::string> esp_mobile_check_types_sf = {
            "ad_dsp_creative", "ad_dsp_unit", "ad_dsp_campaign", "ad_dsp_account"};

bool IsAdData(const kuaishou::ad::AdInstance* ad) {
  if (IsFanstopAccount(ad)) {
    return false;
  }
  if (!IsNotFanstopData(ad)) {
    return false;
  }
  if (!IsNotAdapterCreative(ad)) {
    return false;
  }
  // 金牛数据过滤
  if (IsEspSpecialOnlyData(ad)) {
    return false;
  }
  if (IsEspHaowuOnlyData(ad)) {
    return false;
  }
  if (IsEspFlashOnlyData(ad)) {
    return false;
  }
  if (IsSplashOnlyData(ad)) {
    return false;
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) {
    return false;
  }
  return true;
}

bool IsThanosData(const kuaishou::ad::AdInstance* ad) {
  if (!IsNotFanstopData(ad)) {
    return false;
  }
  if (!IsNotAdapterCreative(ad)) {
    return false;
  }
  // 金牛数据过滤
  if (IsEspSpecialOnlyData(ad)) {
    return false;
  }
  if (IsEspHaowuOnlyData(ad)) {
    return false;
  }
  if (IsEspFlashOnlyData(ad)) {
    return false;
  }
  if (IsDpaSdpaData(ad)) {
    return false;
  }
  if (IsSplashOnlyData(ad)) {
    return false;
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) {
    return false;
  }
  return true;
}

bool AdAmdSkipData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    auto creative_feature = creative.creative_feature();
    auto live_creative_type = creative.live_creative_type();
    static const std::unordered_set<AdEnum::AdDspCreativeFeature>
        creative_feature_type_set{
            kuaishou::ad::AdEnum::CREATIVE_MERCHANT_RECO_PROMOTION,
            kuaishou::ad::AdEnum::CREATIVE_ESP_SPECIALTY_PROMOTION};
    static const std::unordered_set<AdEnum::LiveCreativeType>
        live_creative_type_set{
            kuaishou::ad::AdEnum::LIVE_STREAM_CREATIVE_TYPE,
            kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE,
            kuaishou::ad::AdEnum::PIC_TO_LIVE_STREAM_CREATIVE_TYPE};
    return (creative_feature_type_set.count(creative_feature) > 0 ||
        live_creative_type_set.count(live_creative_type) > 0);
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    auto unit_feature = unit.unit_feature();
    static const std::unordered_set<AdEnum::AdDspUnitFeature> expected_type_set{
        kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTION,
        kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTION,
        kuaishou::ad::AdEnum::ESP_SPECIALTY_PROMOTION};
    return expected_type_set.count(unit_feature) > 0;
  }
  return false;
}

bool IsAdDataV2(const kuaishou::ad::AdInstance* ad) {
  if (!IsAdData(ad)) {
    return false;
  }
  if (AdAmdSkipData(ad)) {
    return false;
  }
  return true;
}

bool IsAdServerData(const kuaishou::ad::AdInstance* ad) {
  if (!IsNotFanstopData(ad)) {
    return false;
  }
  if (!IsNotAdapterCreative(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) {
    return false;
  }
  return true;
}

bool IsNonDspData(const kuaishou::ad::AdInstance *ad) {
  // 粉条、金牛、多端适配
  // TODO(caoyifan03): NonDsp 应该只针对 Creative 相关进行过滤，
  // 放行所有 Account/Campaign/Unit。重构一下代码让这个逻辑更清楚一些。
  return IsFansTopData(ad) || IsAdapterCreative(ad) ||
         IsEspSpecialOnlyData(ad) || IsEspHaowuOnlyData(ad) || IsEspFlashOnlyData(ad);
}

bool ReserveAll(const kuaishou::ad::AdInstance* ad) { return true; }

bool IsKnewsData(const kuaishou::ad::AdInstance* ad) {
  if (IsFanstopAccount(ad)) {
    return false;
  }
  if (!IsNotFanstopData(ad)) {
    return false;
  }
  // 金牛数据过滤
  if (IsEspSpecialOnlyData(ad)) {
    return false;
  }
  if (IsEspHaowuOnlyData(ad)) {
    return false;
  }
  if (IsEspFlashOnlyData(ad)) {
    return false;
  }
  if (IsDpaSdpaData(ad)) {
    return false;
  }
  if (IsSplashOnlyData(ad)) {
    return false;
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  return true;
}

bool IsEspMoblieAccount(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type != kuaishou::ad::AdEnum::ACCOUNT) {
    return false;
  }
  const Account& account = ad->GetExtension(Account::account_old);
  if (account.account_type() == AdEnum::ACCOUNT_ESP_MOBILE) {
    return true;
  }
  return false;
}

bool IsAdBudget(const kuaishou::ad::AdInstance* ad) {
  absl::call_once(init_once, InitOnce);
  if (IsFansTopOnly(ad) && (!enableEspMobileAccount || !IsEspMoblieAccount(ad))) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) {
    return false;
  }

  auto msg_type = ad->type();
  // 套餐广告需求, 维护 @zhaizhiqiang
  if (msg_type == kuaishou::ad::AdEnum::AD_CHARGE_BALANCE) {
    const AdChargeBalance& charge =
        ad->GetExtension(AdChargeBalance::ad_charge_balance_old);
    auto dsp_budget_order_biz_list = MultiFormKconfUtil::dspBudgetOrderBizList();
    if (dsp_budget_order_biz_list == nullptr) {
      return true;
    }
    std::string biz_type = kuaishou::ad::AdEnum_ChargeBizType_Name(charge.biz_type());
    if (dsp_budget_order_biz_list->find(biz_type) == dsp_budget_order_biz_list->end()) {
      return false;
    }
    return true;
  }
  return true;
}

static std::set<AdEnum::AdInstanceType> splash_check_types = {
    kuaishou::ad::AdEnum::CREATIVE, kuaishou::ad::AdEnum::UNIT};
static std::set<std::string> splash_check_types_sf = {"ad_dsp_creative", "ad_dsp_unit"};
bool IsSplashDataImp(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  LOG_ASSERT(splash_check_types_sf.count(table_name) > 0)
      << "unexpected_type: " << table_name;
  if (table_name == "ad_dsp_creative") {
    auto creative_material_type = static_cast<AdEnum::CreativeMaterialType>(
                                    table_row->GetValue<int32_t>("creative_material_type"));
    static const std::set<AdEnum::CreativeMaterialType> match_type_sf{
        AdEnum::SPLASH_PHOTO, AdEnum::SPLASH_IMAGES, AdEnum::ESP_EYE_MAX};
    if (match_type_sf.count(creative_material_type) == 0) {
      return false;
    }
  } else if (table_name == "ad_dsp_nuit") {
    auto unit_add_on_feature = table_row->GetValue<int64_t>("unit_support_info__unit_add_on_feature");
    if (0 == (unit_add_on_feature & 0x3)) {
      return false;
    }
  }
  return true;
}
bool IsSplashDataImp(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  LOG_ASSERT(splash_check_types.count(msg_type) > 0)
      << "unexpected_type: " << msg_type;
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    auto creative_material_type = creative.creative_material_type();
    static const std::set<AdEnum::CreativeMaterialType> match_type{
        AdEnum::SPLASH_PHOTO, AdEnum::SPLASH_IMAGES, AdEnum::ESP_EYE_MAX};
    if (match_type.count(creative_material_type) == 0) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    auto unit_add_on_feature = unit.unit_support_info().unit_add_on_feature();
    if (0 == (unit_add_on_feature & 0x3)) {
      return false;
    }
  }
  return true;
}
bool IsSplashData(const ks::ad::build_service::TableRow* table_row) {
  // todo[zhangruikang] delete this ？
  absl::call_once(init_once, InitOnce);
  CHECKTABLEROW(table_row)
  if (IsSeriesCampaignData(table_row)) {
    return false;
  }
  if (IsFanstopAccount(table_row)) {
    return false;
  }
  // 开屏 RTB 不支持微改素材
  if (enableSplashBlockAigcModifyPhoto && IsAigcModifyPhoto(table_row)) {
    return false;
  }
  if (enableRtbWhiteList && table_name == "ad_dsp_creative") {
    auto creative_id = table_row->GetValue<int64_t>("id");
    if (RtbWhiteList::GetInstance()->IsInWhiteList(creative_id)) {
      return true;
    }
  }

  if (splash_check_types_sf.count(table_name) > 0) {
    return IsSplashDataImp(table_row);
  }
  if (IsSearchOnlyData(table_row)) {
    return false;
  }
  if (IsDpaNotContainSdpa(table_row)) {
    return false;
  }
  return true;
}
bool IsSplashData(const kuaishou::ad::AdInstance* ad) {
  absl::call_once(init_once, InitOnce);
  // 短剧物料不投放开屏
  if (IsSeriesCampaignData(ad)) {
    return false;
  }
  if (IsFanstopAccount(ad)) {
    return false;
  }
  // 开屏 RTB 不支持微改素材
  if (enableSplashBlockAigcModifyPhoto && IsAigcModifyPhoto(ad)) {
    return false;
  }
  auto msg_type = ad->type();
  if (enableRtbWhiteList && msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    auto creative_id = creative.id();
    if (RtbWhiteList::GetInstance()->IsInWhiteList(creative_id)) {
      return true;
    }
  }

  bool ret = true;
  if (splash_check_types.count(msg_type) > 0) {
    return IsSplashDataImp(ad);
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) {
    return false;
  }
  return true;
}

bool IsSplashDataKtable(const ks::ad::build_service::TableRow* table_row) {
  // todo[zhangruikang] delete this ？
  absl::call_once(init_once, InitOnce);
  CHECKTABLEROW(table_row)
  if (IsSeriesCampaignData(table_row)) {
    return false;
  }
  if (IsFanstopAccount(table_row)) {
    return false;
  }
  // 开屏 RTB 不支持微改素材
  if (enableSplashBlockAigcModifyPhoto && IsAigcModifyPhoto(table_row)) {
    return false;
  }
  if (enableRtbWhiteList && table_name == "ad_dsp_creative") {
    bool is_rtb_gray = table_row->GetValue<bool>("is_rtb_gray");
    if (is_rtb_gray) {
      return true;
    }
  }

  if (splash_check_types_sf.count(table_name) > 0) {
    return IsSplashDataImp(table_row);
  }
  if (IsSearchOnlyData(table_row)) {
    return false;
  }
  if (IsDpaNotContainSdpa(table_row)) {
    return false;
  }
  return true;
}

bool IsSplashDataKtable(const kuaishou::ad::AdInstance* ad) {
  absl::call_once(init_once, InitOnce);
  // 短剧物料不投放开屏
  if (IsSeriesCampaignData(ad)) {
    return false;
  }
  if (IsFanstopAccount(ad)) {
    return false;
  }
  // 开屏 RTB 不支持微改素材
  if (enableSplashBlockAigcModifyPhoto && IsAigcModifyPhoto(ad)) {
    return false;
  }
  auto msg_type = ad->type();
  if (enableRtbWhiteList && msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    if (creative.is_rtb_gray()) {
      return true;
    }
  }

  bool ret = true;
  if (splash_check_types.count(msg_type) > 0) {
    return IsSplashDataImp(ad);
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) {
    return false;
  }
  return true;
}

bool IsSplashOnlyData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_creative") {
    return IsSplashDataImp(table_row);
  }
  return false;
}
bool IsSplashOnlyData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    return IsSplashDataImp(ad);
  }
  return false;
}

bool IsDpaSdpaDataImp(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_creative") {
    auto create_source_type = static_cast<AdEnum::CreativeSourceType>(
                                table_row->GetValue<int32_t>("create_source_type"));
    std::set<AdEnum::CreativeSourceType> match_set{
        AdEnum::DPA_SOURCE_CREATIVE, AdEnum::SDPA_SOURCE_CREATIVE};
    if (match_set.count(create_source_type) > 0) {
      return true;
    }
  } else if (table_name == "ad_dsp_unit") {
    auto library_id = table_row->GetValue<int64_t>("library_id");
    if (library_id > 0) {
      return true;
    }
  }
  return false;
}
bool IsDpaSdpaDataImp(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    auto create_source_type = creative.create_source_type();
    std::set<AdEnum::CreativeSourceType> match_set{
        AdEnum::DPA_SOURCE_CREATIVE, AdEnum::SDPA_SOURCE_CREATIVE};
    if (match_set.count(create_source_type) > 0) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    if (unit.library_id() > 0) {
      return true;
    }
  }
  return false;
}

bool IsDpaDataImp(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    auto create_source_type = creative.create_source_type();
    if (create_source_type == AdEnum::DPA_SOURCE_CREATIVE) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    if (unit.library_id() > 0 && unit.dpa_type() == 1) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    if (campaign.type() ==
        kuaishou::ad::AdEnum::CampaignType::AdEnum_CampaignType_DPA_CAMPAIGN) {
      return true;
    }
  }
  return false;
}

bool IsDpaData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE ||
      msg_type == kuaishou::ad::AdEnum::UNIT ||
      msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    return IsDpaDataImp(ad);
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  if (IsFanstopAccount(ad)) {
    return false;
  }
  return true;
}

bool IsDpaSdpaData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_creative") {
    return IsDpaSdpaDataImp(table_row);
  }
  return false;
}
bool IsDpaSdpaData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    return IsDpaSdpaDataImp(ad);
  }
  return false;
}

bool IsDpaNotContainSdpa(const kuaishou::ad::AdInstance* ad) {
  if (ad == nullptr) {
    return false;
  }

  static std::set<AdEnum::CreativeSourceType> match {
    AdEnum::DPA_SOURCE_CREATIVE };

  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    auto& creative = ad->GetExtension(Creative::creative_old);
    auto  type = creative.create_source_type();
    if (match.count(type) > 0) {
      return true;
    }
  }
  return false;
}

bool IsDpaNotContainSdpa(const ks::ad::build_service::TableRow* table_row) {
  if (table_row == nullptr) {
    return false;
  }
  // 表配置解析
  auto table_name = table_row->GetTableName();
  if (table_name.empty()) {
    LOG_EVERY_N(WARNING, 1000) << "get table name  fail, build_key=";
    return false;
  }
  static std::set<AdEnum::CreativeSourceType> match {
    AdEnum::DPA_SOURCE_CREATIVE };

  if (table_name == "ad_dsp_creative") {
    auto type = static_cast<AdEnum::CreativeSourceType>(
                 table_row->GetValue<int32_t>("create_source_type"));
    if (match.count(type) > 0) {
      return true;
    }
  }
  return false;
}

DebugSourceDataType GetDebugDataType(const kuaishou::ad::AdInstance* ad) {
  if (IsProgrammedCreativeOnly(ad)) {
    return DebugSourceDataType::programmed_creative;
  } else if (IsAdapterCreativeOnly(ad)) {
    return DebugSourceDataType::adapter_creative;
  } else if (IsFansTopOnly(ad)) {
    return DebugSourceDataType::fanstop;
  } else if (IsEspFlashOnlyData(ad)) {
    return DebugSourceDataType::esp_flash;
  } else if (IsEspSpecialOnlyData(ad)) {
    return DebugSourceDataType::esp_specialty;
  } else if (IsEspHaowuOnlyData(ad)) {
    return DebugSourceDataType::esp_haowu;
  } else if (IsAmdOnlyData(ad)) {
    return DebugSourceDataType::amd;
  }
  return DebugSourceDataType::ad_norm;
}

bool IsKnewWithAdapterData(const kuaishou::ad::AdInstance* ad) {
  if (!IsNotFanstopData(ad)) {
    return false;
  }
  // 金牛数据过滤
  if (IsEspSpecialOnlyData(ad)) {
    return false;
  }
  if (IsEspHaowuOnlyData(ad)) {
    return false;
  }
  if (IsEspFlashOnlyData(ad)) {
    return false;
  }
  if (IsAdapterCreativeUniverseOnly(ad)) {
    return false;
  }
  if (IsDpaSdpaData(ad)) {
    return false;
  }
  if (IsSplashOnlyData(ad)) {
    return false;
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  return true;
}


// 联盟专用 放出符合条件的磁力金牛移动端广告
bool IsEspMobileOnly(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (esp_mobile_check_types_sf.count(table_name) == 0) {
    return false;
  }
  if (table_name == "ad_dsp_creative") {
    auto creative_feature =
        static_cast<AdEnum::AdDspCreativeFeature>(table_row->GetValue<int32_t>("creative_feature"));
    if (creative_feature != AdEnum::CREATIVE_ESP_MOBILE_FEATURE &&
        creative_feature != AdEnum::CREATIVE_ESP_MOBILE_FEATURE_NO_MERCHANT) {
      return false;
    }
    auto fans_top_liked_biz_type = static_cast<AdEnum::FansTopLikedBizType>(
        table_row->GetValue<int32_t>("creative_fanstop_support_info__fans_top_liked_biz_type"));
    if (fans_top_liked_biz_type == kuaishou::ad::AdEnum::B_FANS_TOP) {
      return false;
    }
    // 高光聚合创意不进索引
    auto creative_type = static_cast<AdEnum::AdDspCreativeType>(
                              table_row->GetValue<int32_t>("creative_type"));
    if (creative_type == AdEnum::ESP_HIGHLIGHT_FLASH) {
      return false;
    }
  } else if (table_name == "ad_dsp_unit") {
    auto unit_feature = static_cast<AdEnum::AdDspUnitFeature>(
                              table_row->GetValue<int32_t>("unit_feature"));
    if (unit_feature != AdEnum::UNIT_ESP_MOBILE_FEATURE &&
        unit_feature != AdEnum::UNIT_ESP_MOBILE_FEATURE_NO_MERCHANT) {
      return false;
    }
    // todo[zrk] 这里 fans_top_liked_biz_type 在 hub 配置里有问题，后面 check 下哈
    auto fans_top_liked_biz_type = static_cast<AdEnum::FansTopLikedBizType>(
                              table_row->GetValue<int32_t>("fans_top_liked_biz_type"));
    if (fans_top_liked_biz_type == kuaishou::ad::AdEnum::B_FANS_TOP) {
      return false;
    }
  } else if (table_name == "ad_dsp_campaign") {
    auto type = static_cast<AdEnum::CampaignType>(table_row->GetValue<int32_t>("type"));
    if (type != AdEnum::AD_FANSTOP_LIVE_TO_ALL) {
      return false;
    }
    auto fans_top_liked_biz_type = static_cast<AdEnum::FansTopLikedBizType>(
        table_row->GetValue<int32_t>("campaign_fanstop_support_info__fans_top_liked_biz_type"));
    if (fans_top_liked_biz_type == kuaishou::ad::AdEnum::B_FANS_TOP) {
      return false;
    }
  } else if (table_name == "ad_dsp_account") {
    auto account_type = static_cast<AdEnum::AdDspAccountType>(
                              table_row->GetValue<int32_t>("account_type"));
    if (account_type != AdEnum::ACCOUNT_ESP_MOBILE) {
      return false;
    }
  }
  return true;
}
bool IsEspMobileOnly(const kuaishou::ad::AdInstance* ad) {
  if (ad == nullptr) {
    return false;
  }
  auto msg_type = ad->type();
  if (esp_mobile_check_types.count(msg_type) == 0) {
    return false;
  }
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    auto creative_feature = creative.creative_feature();
    if (creative_feature != AdEnum::CREATIVE_ESP_MOBILE_FEATURE &&
        creative_feature != AdEnum::CREATIVE_ESP_MOBILE_FEATURE_NO_MERCHANT) {
      return false;
    }
    const CreativeFanstopSupportInfo& support_info = creative.creative_fanstop_support_info();
    if (support_info.fans_top_liked_biz_type() == kuaishou::ad::AdEnum::B_FANS_TOP) {
      return false;
    }
    // 高光聚合创意不进索引
    if (creative.creative_type() == AdEnum::ESP_HIGHLIGHT_FLASH) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    auto unit_feature = unit.unit_feature();
    if (unit_feature != AdEnum::UNIT_ESP_MOBILE_FEATURE &&
        unit_feature != AdEnum::UNIT_ESP_MOBILE_FEATURE_NO_MERCHANT) {
      return false;
    }
    const UnitFanstopSupportInfo& support_info = unit.unit_fanstop_support_info();
    if (support_info.fans_top_liked_biz_type() == kuaishou::ad::AdEnum::B_FANS_TOP) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    auto type = campaign.type();
    if (type != AdEnum::AD_FANSTOP_LIVE_TO_ALL) {
      return false;
    }
    const CampaignFanstopSupportInfo& support_info = campaign.campaign_fanstop_support_info();
    if (support_info.fans_top_liked_biz_type() == kuaishou::ad::AdEnum::B_FANS_TOP) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::ACCOUNT) {
    const Account& account = ad->GetExtension(Account::account_old);
    auto account_type = account.account_type();
    if (account_type != AdEnum::ACCOUNT_ESP_MOBILE) {
      return false;
    }
  }
  return true;
}
bool IsUnivserNoAdapterData(const ks::ad::build_service::TableRow* table_row) {
  absl::call_once(init_once, InitOnce);
  // 商品卡创意不投放联盟
  if (enableUniverseFilterItemCard && IsItemCardCreative(table_row)) {
    return false;
  }
  // 短剧物料不投放联盟
  if (!enable_universe_support_kwai_series && IsSeriesCampaignData(table_row)) {
    return false;
  }
  // 联盟放出 ACCOUNT_ESP_MOBILE
  if (enable_universe_support_esp_mobile && IsEspMobileOnly(table_row)) {
    return true;
  }
  if (IsFanstopAccount(table_row)) {
    return false;
  }
  // 放入金牛数据
  if (!IsNotFanstopData(table_row)) {
    return false;
  }
  if (IsAdapterCreativeOnly(table_row)) {
    return false;
  }
  if (IsDpaSdpaData(table_row)) {
    return false;
  }
  if (IsSplashOnlyData(table_row) && !enableUniverseSupportSplashOnlyData) {
    return false;
  }
  if (enableUniverseTinySearchData) {
    if (IsSearchOnlyDataForUniverse(table_row)) {
      return false;
    }
  } else {
    if (IsSearchOnlyData(table_row)) {
      return false;
    }
  }
  if (enableUniverseBlockAigcModifyPhoto && IsAigcModifyPhoto(table_row)) {
    return false;
  }
  return true;
}
bool IsUnivserNoAdapterData(const kuaishou::ad::AdInstance* ad) {
  absl::call_once(init_once, InitOnce);
  // 商品卡创意不投放联盟
  if (enableUniverseFilterItemCard && IsItemCardCreative(ad)) {
    return false;
  }
  // 短剧物料不投放联盟
  if (!enable_universe_support_kwai_series && IsSeriesCampaignData(ad)) {
    return false;
  }
  // 联盟放出 ACCOUNT_ESP_MOBILE
  if (enable_universe_support_esp_mobile && IsEspMobileOnly(ad)) {
    return true;
  }
  if (IsFanstopAccount(ad)) {
    return false;
  }
  // 放入金牛数据
  if (!IsNotFanstopData(ad)) {
    return false;
  }
  if (IsAdapterCreativeOnly(ad)) {
    return false;
  }
  if (IsDpaSdpaData(ad)) {
    return false;
  }
  if (IsSplashOnlyData(ad) && !enableUniverseSupportSplashOnlyData) {
    return false;
  }
  if (enableUniverseTinySearchData) {
    if (IsSearchOnlyDataForUniverse(ad)) {
      return false;
    }
  } else {
    if (IsSearchOnlyData(ad)) {
      return false;
    }
  }
  if (enableUniverseBlockAigcModifyPhoto && IsAigcModifyPhoto(ad)) {
    return false;
  }
  return true;
}

bool IsUnivserNoAdapterDataV2(const ks::ad::build_service::TableRow* table_row) {
  if (!IsUnivserNoAdapterData(table_row)) {
    return false;
  }
  // 联盟过滤不支持物料类型
  if (enableUniverseInvalidTypeFilter && IsUniverseInvalidType(table_row)) {
    return false;
  }
  return true;
}

bool IsUnivserNoAdapterDataV2(const kuaishou::ad::AdInstance* ad) {
  if (!IsUnivserNoAdapterData(ad)) {
    return false;
  }
  // 联盟过滤不支持物料类型
  if (enableUniverseInvalidTypeFilter && IsUniverseInvalidType(ad)) {
    return false;
  }
  return true;
}

bool IsUnivserTinyData(const ks::ad::build_service::TableRow* table_row) {
  if (!IsUnivserNoAdapterData(table_row)) {
    return false;
  }
  // 联盟小系统过滤不支持物料类型
  if (enable_universe_tiny_filter && IsUniverseTinyFilterData(table_row)) {
    return false;
  }
  return true;
}

bool IsUnivserTinyData(const kuaishou::ad::AdInstance* ad) {
  if (!IsUnivserNoAdapterData(ad)) {
    return false;
  }
  // 联盟小系统过滤不支持物料类型
  if (enable_universe_tiny_filter && IsUniverseTinyFilterData(ad)) {
    return false;
  }
  return true;
}

bool IsUniverseDpa(const kuaishou::ad::AdInstance* ad) {
  // 接入 AdMatrixStyleMaterial 物料
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::AD_MATRIX_STYLE_MATERIAL) {
    const AdMatrixStyleMaterial& ad_matrix_style_material =
        ad->GetExtension(AdMatrixStyleMaterial::ad_matrix_style_material_old);
    int64_t product_id = 0;
    int64_t library_id = 0;
    if (!absl::SimpleAtoi(ad_matrix_style_material.biz_id(), &product_id)) {
      return false;
    }

    // productLibrary + "_" + thirdCategoryId
    std::vector<std::string> p_t_strings =
        absl::StrSplit(ad_matrix_style_material.sub_biz_id(), "_", absl::SkipEmpty());
    if (p_t_strings.size() != 2) {
      return false;
    }
    if (!absl::SimpleAtoi(p_t_strings[0], &library_id)) {
      return false;
    }
    auto type = ad_matrix_style_material.type();
    static const int64_t VALID_PERIOD = 90ull * 24 * 3600 * 1000;
    int64_t create_diff = base::GetTimestamp() / 1000 - ad_matrix_style_material.create_time();
    auto is_valid = ad_matrix_style_material.put_status() == kuaishou::ad::AdEnum::PUT_STATUS_OPEN &&
             ad_matrix_style_material.exeunt_flag() != kuaishou::ad::AdEnum::EXEUNT_OFFLINE &&
             (ad_matrix_style_material.create_time() > 0 ? create_diff < VALID_PERIOD : true);
    if (!(type == 16 && is_valid)) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(type));
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    if (creative.creative_material_type() != kuaishou::ad::AdEnum::DPA_MATERIAL_TEMPLATE) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt", "CREATIVE");
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    if (unit.unit_type() != AdEnum::DPA_UNIT) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt", "UNIT");
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    if (campaign.type() != AdEnum::DPA_CAMPAIGN) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt", "CAMPAIGN");
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::ACCOUNT) {
    const Account& account = ad->GetExtension(Account::account_old);
    if (!(universe_dpa_account_white_list && universe_dpa_account_white_list->IsOnFor(account.id()))) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt", "ACCOUNT");
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::ACCOUNT_INDUSTRY) {
    const AccountIndustry& account_industry = ad->GetExtension(AccountIndustry::account_industry_old);
    if (!(universe_dpa_account_white_list &&
        universe_dpa_account_white_list->IsOnFor(account_industry.account_id()))) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt", "ACCOUNT_INDUSTRY"); // NOLINT
      return false;
    }
  }
  return IsUnivserNoAdapterData(ad);
}

bool IsUniverseDpaOpt(const kuaishou::ad::AdInstance* ad) {
  // 接入 AdMatrixStyleMaterial 物料
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::AD_MATRIX_STYLE_MATERIAL) {
    const AdMatrixStyleMaterial& ad_matrix_style_material =
        ad->GetExtension(AdMatrixStyleMaterial::ad_matrix_style_material_old);
    int64_t product_id = 0;
    int64_t library_id = 0;
    if (!absl::SimpleAtoi(ad_matrix_style_material.biz_id(), &product_id)) {
      return false;
    }

    // productLibrary + "_" + thirdCategoryId
    std::vector<std::string> p_t_strings =
        absl::StrSplit(ad_matrix_style_material.sub_biz_id(), "_", absl::SkipEmpty());
    if (p_t_strings.size() != 2) {
      return false;
    }
    if (!absl::SimpleAtoi(p_t_strings[0], &library_id)) {
      return false;
    }
    auto type = ad_matrix_style_material.type();
    static const int64_t VALID_PERIOD = 90ull * 24 * 3600 * 1000;
    int64_t create_diff = base::GetTimestamp() / 1000 - ad_matrix_style_material.create_time();
    auto is_valid = ad_matrix_style_material.put_status() == kuaishou::ad::AdEnum::PUT_STATUS_OPEN &&
             ad_matrix_style_material.exeunt_flag() != kuaishou::ad::AdEnum::EXEUNT_OFFLINE &&
             (ad_matrix_style_material.create_time() > 0 ? create_diff < VALID_PERIOD : true);
    if (!(type == 16 && is_valid)) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(type));
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    if (creative.creative_material_type() != kuaishou::ad::AdEnum::DPA_MATERIAL_TEMPLATE) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt", "CREATIVE");
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    if (unit.unit_type() != AdEnum::DPA_UNIT) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt", "UNIT");
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    if (campaign.type() != AdEnum::DPA_CAMPAIGN) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt", "CAMPAIGN");
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::ACCOUNT) {
    const Account& account = ad->GetExtension(Account::account_old);
    if (!(universe_dpa_account_white_list && universe_dpa_account_white_list->IsOnFor(account.id()))) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe_dpa_opt", "ACCOUNT");
      return false;
    }
  }
  return IsUnivserNoAdapterData(ad);
}

bool IsUniverseSwiftData(const ks::ad::build_service::TableRow* table_row) {
  if (!IsUnivserNoAdapterData(table_row)) {
    return false;
  }
  if (IsUniverseSwiftFilterData(table_row)) {
    return false;
  }
  return true;
}

bool IsUniverseSwiftData(const kuaishou::ad::AdInstance* ad) {
  if (!IsUnivserNoAdapterData(ad)) {
    return false;
  }
  if (IsUniverseSwiftFilterData(ad)) {
    return false;
  }
  return true;
}

bool IsUnivserWithAdapterData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    static const std::set<AdEnum::CreativeMaterialType> limited_type{
        AdEnum::HORIZONTAL_SCREEN, AdEnum::HORIZONTAL_IMAGE};
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    auto creative_material_type = creative.creative_material_type();
    return IsAdapterCreativeUniverseOnly(ad) ||
           limited_type.count(creative_material_type) > 0;
  }
  if (IsFansTopOnly(ad)) {
    return false;
  }
  // 金牛数据过滤
  if (IsEspSpecialOnlyData(ad)) {
    return false;
  }
  if (IsEspHaowuOnlyData(ad)) {
    return false;
  }
  if (IsEspFlashOnlyData(ad)) {
    return false;
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) {
    return false;
  }
  return true;
}

bool IsPsMaterialAlphaData(const kuaishou::ad::AdInstance* ad) {
  if (!IsNotAdapterCreative(ad)) {
    return false;
  }
  // 搜索直投智能扩量物料
  if (MultiFormKconfUtil::enableSearchMingtouToInnerOuter() && IsExtendSearchData(ad)) {
    return true;
  }
  // 搜索直投人群追投物料
  if (MultiFormKconfUtil::enableSearchRetargetingToInnerOuter() && IsSearchPopulationRetargeting(ad)) {
    return true;
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) {
    return false;
  }
  const auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::ACCOUNT) {
    return true;
  }
  return true;
}

DebugSourceDataType GetDebugDataTypeV2Creative(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type != kuaishou::ad::AdEnum::CREATIVE) {
    return DebugSourceDataType::unknown;
  }
  const Creative& creative = ad->GetExtension(Creative::creative_old);
  auto create_source_type = creative.create_source_type();
  if (create_source_type == kuaishou::ad::AdEnum::ADVANCED_PROGRAMMED_CREATIVE) {
    if (creative.derivative_creative_flag()) {
      auto biz_id = creative.biz_id();
      static const std::unordered_map<int, DebugSourceDataType> biz2type{
          {1, DebugSourceDataType::adapter_universe},
          {2, DebugSourceDataType::adapter_knews},
          {3, DebugSourceDataType::adapter_applets},
          {4, DebugSourceDataType::adapter_splash},
          {5, DebugSourceDataType::adapter_inspire}};
      auto map_it = biz2type.find(biz_id);
      if (map_it == biz2type.end()) {
        return DebugSourceDataType::unknown;
      } else {
        return map_it->second;
      }
    } else {
      return DebugSourceDataType::programmed_creative;
    }
  }
  const auto& live_creative_type = creative.live_creative_type();
  const auto& creative_feature = creative.creative_feature();
  if (IsAmdUniqueOnlyData(ad)) {
    return DebugSourceDataType::amd_unique;
  }
  if (IsAmdOnlyData(ad)) {
    return DebugSourceDataType::amd;
  }
  if (IsEspFlashOnlyData(ad)) {
    return DebugSourceDataType::esp_flash;
  }
  if (IsEspSpecialOnlyData(ad)) {
    return DebugSourceDataType::esp_specialty;
  }
  if (IsEspHaowuOnlyData(ad)) {
    return DebugSourceDataType::esp_haowu;
  }
  if (IsFansTopOnly(ad)) {
    return DebugSourceDataType::fanstop;
  }
  if (IsSplashOnlyData(ad)) {
    return DebugSourceDataType::splash;
  }
  if (create_source_type == AdEnum::SDPA_SOURCE_CREATIVE) {
    return DebugSourceDataType::sdpa;
  }
  if (create_source_type == AdEnum::DPA_SOURCE_CREATIVE) {
    return DebugSourceDataType::dpa;
  }
  return DebugSourceDataType::ad_norm;
}

DebugSourceDataType GetDebugDataTypeV2Unit(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type != kuaishou::ad::AdEnum::UNIT) {
    return DebugSourceDataType::unknown;
  }
  if (IsEspFlashOnlyData(ad)) {
    return DebugSourceDataType::esp_flash;
  }
  if (IsEspSpecialOnlyData(ad)) {
    return DebugSourceDataType::esp_specialty;
  }
  if (IsEspHaowuOnlyData(ad)) {
    return DebugSourceDataType::esp_haowu;
  }
  if (IsFansTopOnly(ad)) {
    return DebugSourceDataType::fanstop;
  }
  if (IsAmdUniqueOnlyData(ad)) {
    return DebugSourceDataType::amd_unique;
  }
  if (IsAmdData(ad)) {
    return DebugSourceDataType::amd;
  }
  if (IsSplashOnlyData(ad)) {
    return DebugSourceDataType::splash;
  }
  return DebugSourceDataType::ad_norm;
}

DebugSourceDataType GetDebugDataTypeV2Campaign(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type != kuaishou::ad::AdEnum::CAMPAIGN) {
    return DebugSourceDataType::unknown;
  }
  if (IsEspFlashOnlyData(ad)) {
    return DebugSourceDataType::esp_flash;
  }
  if (IsEspSpecialOnlyData(ad)) {
    return DebugSourceDataType::esp_specialty;
  }
  if (IsEspHaowuOnlyData(ad)) {
    return DebugSourceDataType::esp_haowu;
  }
  if (IsFansTopOnly(ad)) {
    return DebugSourceDataType::fanstop;
  }
  if (IsAmdUniqueOnlyData(ad)) {
    return DebugSourceDataType::amd_unique;
  }
  if (IsAmdData(ad)) {
    return DebugSourceDataType::amd;
  }
  return DebugSourceDataType::ad_norm;
}

DebugSourceDataType GetDebugDataTypeV2Account(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type != kuaishou::ad::AdEnum::ACCOUNT) {
    return DebugSourceDataType::unknown;
  }
  if (IsFansTopOnly(ad)) {
    return DebugSourceDataType::fanstop;
  }
  if (IsEspOnlyData(ad)) {
    return DebugSourceDataType::esp_account;
  }
  return DebugSourceDataType::ad_norm;
}

DebugSourceDataType GetDebugDataTypeV2Material(
    const kuaishou::ad::AdInstance* ad) {
  const auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::MATERIAL) {
    const Material& material = ad->GetExtension(Material::material_old);
    auto derivative_creative_flag = material.derivative_creative_flag();
    if (derivative_creative_flag) {
      return DebugSourceDataType::adapter_creative;
    }
  }
  return DebugSourceDataType::ad_norm;
}

DebugSourceDataType GetDebugDataTypeV2Photo(
    const kuaishou::ad::AdInstance* ad) {
  const auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::PHOTO_STATUS) {
    const PhotoStatus& photo_status =
        ad->GetExtension(PhotoStatus::photo_status_old);
    auto derivative_creative_flag = photo_status.derivative_creative_flag();
    if (derivative_creative_flag) {
      return DebugSourceDataType::adapter_creative;
    }
  }
  return DebugSourceDataType::ad_norm;
}

using TYPE2FUNC =
    std::unordered_map<AdEnum::AdInstanceType,
                       std::function<DebugSourceDataType(const kuaishou::ad::AdInstance*)>>;

DebugSourceDataType GetDebugDataTypeV2(const kuaishou::ad::AdInstance* ad) {
  static const TYPE2FUNC type2function{
      {AdEnum::CREATIVE, GetDebugDataTypeV2Creative},
      {AdEnum::UNIT, GetDebugDataTypeV2Unit},
      {AdEnum::CAMPAIGN, GetDebugDataTypeV2Campaign},
      {AdEnum::ACCOUNT, GetDebugDataTypeV2Account},
      {AdEnum::MATERIAL, GetDebugDataTypeV2Material},
      {AdEnum::PHOTO_STATUS, GetDebugDataTypeV2Photo}};
  auto msg_type = ad->type();
  auto func_it = type2function.find(msg_type);
  if (func_it != type2function.end()) {
    return (func_it->second)(ad);
  } else {
    return DebugSourceDataType::ad_norm;
  }
}

std::string GetCreativeTypeV3(const kuaishou::ad::AdInstance* ad) {
  std::vector<std::string> tags;
  const Creative& creative = ad->GetExtension(Creative::creative_old);
  auto creative_feature = creative.creative_feature();
  auto live_creative_type = creative.live_creative_type();
  auto create_source_type = creative.create_source_type();
  tags.push_back(AdEnum_AdDspCreativeFeature_Name(creative_feature));
  tags.push_back(AdEnum_LiveCreativeType_Name(live_creative_type));
  tags.push_back(AdEnum_CreativeSourceType_Name(create_source_type));
  std::string ret = absl::StrJoin(tags, "-");
  return ret;
}

std::string GetUnitTypeV3(const kuaishou::ad::AdInstance* ad) {
  std::vector<std::string> tags;
  const Unit& unit = ad->GetExtension(Unit::unit_old);
  auto unit_type = unit.unit_type();
  auto unit_feature = unit.unit_feature();
  tags.push_back(AdEnum_UnitType_Name(unit_type));
  tags.push_back(AdEnum_AdDspUnitFeature_Name(unit_feature));
  std::string ret = absl::StrJoin(tags, "-");
  return ret;
}

std::string GetCampaignTypeV3(const kuaishou::ad::AdInstance* ad) {
  std::vector<std::string> tags;
  const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
  auto promotion_type = campaign.promotion_type();
  auto type = campaign.type();
  tags.push_back(AdEnum_CampaignType_Name(type));
  tags.push_back(AdEnum_CampaignPromotionType_Name(promotion_type));
  std::string ret = absl::StrJoin(tags, "-");
  return ret;
}

std::string GetAccountTypeV3(const kuaishou::ad::AdInstance* ad) {
  std::vector<std::string> tags;
  const Account& account = ad->GetExtension(Account::account_old);
  auto account_type = account.account_type();
  tags.push_back(AdEnum_AdDspAccountType_Name(account_type));
  std::string ret = absl::StrJoin(tags, "-");
  return ret;
}

using TYPE2FUNCV3 = std::unordered_map<
    AdEnum::AdInstanceType,
    std::function<std::string(const kuaishou::ad::AdInstance*)>>;

std::string GetDebugDataTypeV3(const kuaishou::ad::AdInstance* ad) {
  TYPE2FUNCV3 type2function{{AdEnum::CREATIVE, GetCreativeTypeV3},
                            {AdEnum::UNIT, GetUnitTypeV3},
                            {AdEnum::CAMPAIGN, GetCampaignTypeV3},
                            {AdEnum::ACCOUNT, GetAccountTypeV3}};
  auto msg_type = ad->type();
  auto func_it = type2function.find(msg_type);
  if (func_it != type2function.end()) {
    return (func_it->second)(ad);
  } else {
    return "AD_NORM";
  }
}

// Note(cuiyanliang) : B 端粉条是一种预算在 dsp， 使用粉条投放能力的物料
// Note(chenqi07)：聚星流量助推 2.0 内循环复用了 B 粉的 fans_top_liked_biz_type 值
bool IsBusinessFanstopData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  using kuaishou::ad::AdEnum;

  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    const CreativeFanstopSupportInfo& support_info = creative.creative_fanstop_support_info();
    return support_info.fans_top_liked_biz_type() == kuaishou::ad::AdEnum::B_FANS_TOP;
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    const UnitFanstopSupportInfo& support_info = unit.unit_fanstop_support_info();
    return support_info.fans_top_liked_biz_type() == kuaishou::ad::AdEnum::B_FANS_TOP;
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    const CampaignFanstopSupportInfo& support_info = campaign.campaign_fanstop_support_info();
    return support_info.fans_top_liked_biz_type() == kuaishou::ad::AdEnum::B_FANS_TOP;
  } else {
    return false;
  }
}

bool IsSearchData(const kuaishou::ad::AdInstance* ad) {
  // 本地推投搜索
  if (IsLocalPromote(ad)) {
    return true;
  }
  // Note(cuiyanliang): B 端粉条不投放搜索
  if (IsBusinessFanstopData(ad)) {
    return false;
  }
  if (IsFansTopOnly(ad)) {
    return true;
  }
  if (!IsNotAdapterCreative(ad)) {
    return false;
  }
  // 金牛数据过滤
  // 放开专推
  if (IsEspSpecialOnlyData(ad)) {
    return true;
  }
  if (IsEspHaowuOnlyData(ad)) {
    return false;
  }
  // 放开速推
  if (IsEspFlashOnlyData(ad)) {
    // 如果允许短视频，那就是全部速推物料都放过
    absl::call_once(init_once, InitOnce);
    if (enableFlashPromotionPhotoInSearch) {
      return true;
    }
    // 否则只允许直播推广
    auto msg_type = ad->type();
    auto* pb = ad;
    if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
      const Creative& creative = pb->GetExtension(Creative::creative_old);
      auto live_creative_type = creative.live_creative_type();
      static const std::unordered_set<AdEnum::LiveCreativeType>
          live_creative_type_set{
              kuaishou::ad::AdEnum::LIVE_STREAM_CREATIVE_TYPE,
              kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE,
              kuaishou::ad::AdEnum::PIC_TO_LIVE_STREAM_CREATIVE_TYPE};
      if (live_creative_type_set.count(live_creative_type) == 0) {
        return false;
      }
    } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
      const Campaign& campaign = pb->GetExtension(Campaign::campaign_old);
      auto campaign_type = campaign.type();
      if (campaign_type != AdEnum::LIVE_STREAM_PROMOTE) {
        return false;
      }
    }
  }
  if (IsDpaSdpaData(ad)) {
    return false;
  }
  if (IsSplashOnlyData(ad)) {
    return false;
  }
  return true;
}

// 搜索暗投
bool IsTargetSearchData(const kuaishou::ad::AdInstance* ad) {
  absl::call_once(init_once, InitOnce);

  // Note(cuiyanliang): B 端粉条不投放搜索
  if (IsBusinessFanstopData(ad)) {
    return false;
  }
  // 搜索暗投不支持素材微改
  if (enableSearchBlockAigcModifyPhoto && IsAigcModifyPhoto2(ad)) {
    return false;
  }
  // target search 放入明投专业版智能扩量
  if (enableTargetSearchExtendSearchData && IsExtendSearchData(ad)) {
    return true;
  }
  // target search 过滤明投数据
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  // target search 过滤快投数据
  if (!enableTargetSearchQuickSearchData && IsQuickSearchData(ad)) {
    return false;
  }
  return IsSearchData(ad);
}

bool IsFanstopAccount(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name != "ad_dsp_account") {
    return false;
  }
  auto type = static_cast<AdEnum::AdDspAccountType>(table_row->GetValue<int32_t>("account_type"));
  if (type == AdEnum::ACCOUNT_FANSTOP_V2 || type == AdEnum::ACCOUNT_ESP_MOBILE) {
    return true;
  }
  return false;
}
bool IsFanstopAccount(const kuaishou::ad::AdInstance* ad) {
  auto type = ad->type();
  if (type != kuaishou::ad::AdEnum::ACCOUNT) {
    return false;
  }
  const Account& account = ad->GetExtension(Account::account_old);
  if (account.account_type() == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2
      || account.account_type() == kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE) {
    return true;
  }
  return false;
}
bool IsSeriesCampaignData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name != "ad_dsp_campaign") {
    return false;
  }
  auto type = static_cast<AdEnum::CampaignType>(table_row->GetValue<int32_t>("type"));
  if (type == AdEnum::CampaignType::AdEnum_CampaignType_AD_KWAI_SERIAL_PROMOTION) {
    return true;
  }
  return false;
}
bool IsSeriesCampaignData(const kuaishou::ad::AdInstance* ad) {
  auto type = ad->type();
  if (type != kuaishou::ad::AdEnum::CAMPAIGN) {
    return false;
  }
  const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
  if (campaign.type() ==
        kuaishou::ad::AdEnum::CampaignType::AdEnum_CampaignType_AD_KWAI_SERIAL_PROMOTION) {
    return true;
  }
  return false;
}

static std::set<AdEnum::AdInstanceType> bidword_search_check_types = {
    kuaishou::ad::AdEnum::CREATIVE, kuaishou::ad::AdEnum::UNIT};


// 搜索明投
bool IsBidwordSearchData(const kuaishou::ad::AdInstance* ad) {
  // Note(cuiyanliang): B 端粉条不投放搜索
  if (IsBusinessFanstopData(ad)) {
    return false;
  }
  auto msg_type = ad->type();
  if (bidword_search_check_types.count(msg_type) > 0) {
    return (IsSearchOnlyData(ad) || IsQuickSearchData(ad));
  }
  return true;
}

bool IsSearchOnlyDataForUniverse(const ks::ad::build_service::TableRow* table_row) {
  // 联盟搜索暗投小系统 放开搜索直投 & 智能扩词打开 & 拉新
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_unit") {
    // todo[zrk] 这里的 ad_type 在 hub 配置中错误，先写成 ad_type 之后再 check 下哈
    auto ad_type = table_row->GetValue<int32_t>("ad_type");
    if (ad_type == AdType::search) {
      auto extend_search = table_row->GetValue<int64_t>("extend_search");
      if (extend_search == 1 || enableUniverseExtendSearch) {
        // 智能扩词放开
        return false;
      }
      return true;
    }
  } else if (table_name == "ad_dsp_campaign") {
    auto ad_type = table_row->GetValue<int32_t>("ad_type");
    if (ad_type == AdType::search) {
      auto type = static_cast<AdEnum::CampaignType>(table_row->GetValue<int32_t>("type"));
      if (type == AdEnum::CampaignType::AdEnum_CampaignType_APP ||
          enableUniverseSearchAllCampaign) {
        // 拉新放开
        return false;
      }
      return true;
    }
  } else if (table_name == "ad_dsp_winfo") {
    return true;
  } else if (table_name == "ad_dsp_negative_word") {
    return true;
  }
  return false;
}

bool IsSearchOnlyDataForUniverse(const kuaishou::ad::AdInstance* ad) {
  // 联盟搜索暗投小系统 放开搜索直投 & 智能扩词打开 & 拉新
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    if (unit.unit_support_info().ad_type() == AdType::search) {
      if (unit.unit_support_info().extend_search() == 1 || enableUniverseExtendSearch) {
        // 智能扩词放开
        return false;
      }
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    if (campaign.ad_type() == AdType::search) {
      if (campaign.type() == AdEnum::CampaignType::AdEnum_CampaignType_APP ||
          enableUniverseSearchAllCampaign) {
        // 拉新放开
        return false;
      }
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_DSP_WINFO) {
    return true;
  } else if (msg_type == kuaishou::ad::AdEnum::AD_DSP_NEGATIVE_WORD) {
    return true;
  }
  return false;
}

bool IsAigcModifyPhoto(const ks::ad::build_service::TableRow* table_row) {
  // 搜索、联盟、开屏 RTB 不支持 AIGC 微改素材
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_photo") {
    auto photo_source = table_row->GetValue<int32_t>("photo_source");
    if (photo_source == 22 || photo_source == 23) {
      return true;
    }
  }
  return false;
}

bool IsAigcModifyPhoto(const kuaishou::ad::AdInstance* ad) {
  // 搜索、联盟、开屏 RTB 不支持 AIGC 微改素材
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::PHOTO_STATUS) {
    const PhotoStatus& photo_status = ad->GetExtension(PhotoStatus::photo_status_old);
    if (photo_status.photo_source() == 22 || photo_status.photo_source() == 23) {
      return true;
    }
  }
  return false;
}

bool IsAigcModifyPhoto2(const kuaishou::ad::AdInstance* ad) {
  // 搜索过滤 AIGC_MODIFY_DSP
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::PHOTO_STATUS) {
    const PhotoStatus& photo_status = ad->GetExtension(PhotoStatus::photo_status_old);
    if (photo_status.photo_source() == kuaishou::ad::AdEnum::AIGC_MODIFY_DSP) {
      return true;
    }
  }
  return false;
}

static const std::unordered_set<AdEnum::AdDspAccountType> universe_support_account_types = {
  AdEnum::AdDspAccountType::AdEnum_AdDspAccountType_ACCOUNT_CPC,
  AdEnum::AdDspAccountType::AdEnum_AdDspAccountType_ACCOUNT_ESP
};

static const std::unordered_set<AdEnum::CampaignType> universe_support_campaign_types = {
  AdEnum::CampaignType::AdEnum_CampaignType_APP_ADVANCE,
  AdEnum::CampaignType::AdEnum_CampaignType_APP,
  AdEnum::CampaignType::AdEnum_CampaignType_TAOBAO,
  AdEnum::CampaignType::AdEnum_CampaignType_LANDING_PAGE,
  AdEnum::CampaignType::AdEnum_CampaignType_SITE_PAGE,
  AdEnum::CampaignType::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE,
  AdEnum::CampaignType::AdEnum_CampaignType_LIVE_STREAM_PROMOTE,
  AdEnum::CampaignType::AdEnum_CampaignType_DPA_CAMPAIGN,
  AdEnum::CampaignType::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE,
  AdEnum::CampaignType::AdEnum_CampaignType_AD_WX_MINI_APP,
  AdEnum::CampaignType::AdEnum_CampaignType_AD_KWAI_SERIAL_PROMOTION,
  AdEnum::CampaignType::AdEnum_CampaignType_AD_CID,
  AdEnum::CampaignType::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION
};

bool IsUniverseInvalidType(const ks::ad::build_service::TableRow* table_row) {
  // 联盟过滤不支持物料类型
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_unit") {
    // 明暗投通用过滤逻辑
    auto bid_type = table_row->GetValue<int32_t>("bid_type");
    auto ocpx_action_type = table_row->GetValue<int32_t>("ocpx_action_type");
    auto deep_conversion_type = table_row->GetValue<int32_t>("deep_conversion_type");
    if (bid_type_to_filter.count(bid_type) > 0 ||
        ocpx_type_to_filter.count(ocpx_action_type) > 0 ||
        deep_type_to_filter.count(deep_conversion_type) > 0 ||
        (ocpx_deep_to_filter.find(ocpx_action_type) != ocpx_deep_to_filter.end() &&
         ocpx_deep_to_filter[ocpx_action_type].count(deep_conversion_type) > 0)) {
      return true;
    }
    // 暗投过滤逻辑
    bool is_dark = true;
    std::string resource_ids = table_row->GetValue<std::string>("resource_ids");
    base::Json json(base::StringToJson(resource_ids));
    if (!json.IsArray()) {
      return false;
    }
    for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
      int64_t id = 0;
      if ((*iter)->IntValue(&id) && (id == 5 || id == 10)) {
        is_dark = false;
        break;
      }
    }
    if (is_dark &&
        (dark_bid_type_to_filter.count(bid_type) > 0 ||
         dark_ocpx_type_to_filter.count(ocpx_action_type) > 0 ||
         dark_deep_type_to_filter.count(deep_conversion_type) > 0 ||
         (dark_ocpx_deep_to_filter.find(ocpx_action_type) != dark_ocpx_deep_to_filter.end() &&
          dark_ocpx_deep_to_filter[ocpx_action_type].count(deep_conversion_type) > 0))) {
      return true;
    }
  } else if (table_name == "ad_dsp_campaign") {
    auto type = static_cast<AdEnum::CampaignType>(
                              table_row->GetValue<int32_t>("type"));
    if (universe_support_campaign_types.count(type) <= 0) {
      return true;
    }
  } else if (table_name == "ad_dsp_account") {
    auto account_type = static_cast<AdEnum::AdDspAccountType>(
                              table_row->GetValue<int32_t>("account_type"));
    if (universe_support_account_types.count(account_type) <= 0) {
      return true;
    }
  }
  return false;
}

bool IsUniverseInvalidType(const kuaishou::ad::AdInstance* ad) {
  // 联盟过滤不支持物料类型
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    // 明暗投通用过滤逻辑
    auto bid_type = unit.bid_type();
    auto ocpx_action_type = unit.ocpx_action_type();
    auto deep_conversion_type = unit.deep_conversion_type();
    if (bid_type_to_filter.count(bid_type) > 0 ||
        ocpx_type_to_filter.count(ocpx_action_type) > 0 ||
        deep_type_to_filter.count(deep_conversion_type) > 0 ||
        (ocpx_deep_to_filter.find(ocpx_action_type) != ocpx_deep_to_filter.end() &&
         ocpx_deep_to_filter[ocpx_action_type].count(deep_conversion_type) > 0)) {
      return true;
    }
    // 暗投过滤逻辑
    bool is_dark = true;
    base::Json json(base::StringToJson(unit.resource_ids()));
    if (!json.IsArray()) {
      return false;
    }
    for (auto iter = json.array_begin(); iter != json.array_end(); ++iter) {
      int64_t id = 0;
      if ((*iter)->IntValue(&id) && (id == 5 || id == 10)) {
        is_dark = false;
        break;
      }
    }
    if (is_dark &&
        (dark_bid_type_to_filter.count(bid_type) > 0 ||
         dark_ocpx_type_to_filter.count(ocpx_action_type) > 0 ||
         dark_deep_type_to_filter.count(deep_conversion_type) > 0 ||
         (dark_ocpx_deep_to_filter.find(ocpx_action_type) != dark_ocpx_deep_to_filter.end() &&
          dark_ocpx_deep_to_filter[ocpx_action_type].count(deep_conversion_type) > 0))) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    if (universe_support_campaign_types.count(campaign.type()) <= 0) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::ACCOUNT) {
    const Account& account = ad->GetExtension(Account::account_old);
    if (universe_support_account_types.count(account.account_type()) <= 0) {
      return true;
    }
  }
  return false;
}

bool IsUniverseTinyFilterData(const ks::ad::build_service::TableRow* table_row) {
  // 联盟小系统过滤不支持物料数据
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_creative") {
    auto live_creative_type = table_row->GetValue<int32_t>("live_creative_type");
    if (live_creative_type != 0) {
      return true;
    }
  } else if (table_name == "ad_dsp_unit") {
    auto campaign_type =
        static_cast<AdEnum::CampaignType>(table_row->GetValue<int32_t>("unit_upper_level_info__type"));
    auto ocpx_action_type =
        static_cast<kuaishou::ad::AdActionType>(table_row->GetValue<int32_t>("ocpx_action_type"));
    static std::set<kuaishou::ad::AdActionType> ocpx_sets{kuaishou::ad::AD_CONVERSION,
                                                          kuaishou::ad::AD_PURCHASE,
                                                          kuaishou::ad::EVENT_NEXTDAY_STAY};
    if (!(campaign_type == AdEnum::APP && ocpx_sets.count(ocpx_action_type) > 0)) {
      return true;
    }
  } else if (table_name == "ad_dsp_campaign") {
    auto type = static_cast<AdEnum::CampaignType>(
                              table_row->GetValue<int32_t>("type"));
    if (type != AdEnum::APP) {
      return true;
    }
  } else if (table_name == "ad_dsp_account") {
    auto account_type = static_cast<AdEnum::AdDspAccountType>(
                              table_row->GetValue<int32_t>("account_type"));
    auto hard_review_status = static_cast<AdEnum::ReviewStatus>(
                              table_row->GetValue<int32_t>("hard_review_status"));
    auto hard_auth_status = static_cast<AdEnum::AuthenticationStatus>(
                              table_row->GetValue<int32_t>("hard_auth_status"));
    if (!(account_type == AdEnum::AdDspAccountType::AdEnum_AdDspAccountType_ACCOUNT_CPC &&
          hard_review_status == AdEnum::REVIEW_THROUGH &&
          hard_auth_status == AdEnum::AUTH_THROUGH)) {
      return true;
    }
  }
  return false;
}

bool IsUniverseTinyFilterData(const kuaishou::ad::AdInstance* ad) {
  // 联盟小系统过滤不支持物料数据
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    if (creative.live_creative_type() != kuaishou::ad::AdEnum::UNKNOWN_LIVE_CREATIVE_TYPE) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    static std::set<kuaishou::ad::AdActionType> ocpx_sets{kuaishou::ad::AD_CONVERSION,
                                                          kuaishou::ad::AD_PURCHASE,
                                                          kuaishou::ad::EVENT_NEXTDAY_STAY};
    static int64_t android_platform_hash = conv("android");
    bool is_android_ad = unit.target().platform_ids_vec().empty() ||
        std::find(unit.target().platform_ids_vec().begin(),
                  unit.target().platform_ids_vec().end(),
                  android_platform_hash) != unit.target().platform_ids_vec().end();
    if (!(unit.unit_upper_level_info().type() == AdEnum::APP &&
          ocpx_sets.count(unit.ocpx_action_type()) > 0 && is_android_ad)) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    if (campaign.type() != AdEnum::APP) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::ACCOUNT) {
    const Account& account = ad->GetExtension(Account::account_old);
    if (!(account.account_type() == AdEnum::AdDspAccountType::AdEnum_AdDspAccountType_ACCOUNT_CPC &&
          account.hard_review_status() == AdEnum::REVIEW_THROUGH &&
          account.hard_auth_status() == AdEnum::AUTH_THROUGH)) {
      return true;
    }
  }
  return false;
}

bool IsUniverseSwiftFilterData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_unit") {
    auto ocpx_action_type = table_row->GetValue<int32_t>("ocpx_action_type");
    if (enable_universe_swift_ocpx_white_list &&
        universe_swift_ocpx_white_list.count(ocpx_action_type) <= 0) {
      return true;
    }
  } else if (table_name == "ad_dsp_campaign") {
    auto campaign_type = table_row->GetValue<int32_t>("type");
    if (enable_universe_swift_campaign_type_black_list &&
        universe_swift_campaign_type_black_list.count(campaign_type) > 0) {
      return true;
    }
  }
  return false;
}

bool IsUniverseSwiftFilterData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    auto ocpx_action_type = unit.ocpx_action_type();
    if (enable_universe_swift_ocpx_white_list &&
        universe_swift_ocpx_white_list.count(ocpx_action_type) <= 0) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = ad->GetExtension(Campaign::campaign_old);
    auto campaign_type = campaign.type();
    if (enable_universe_swift_campaign_type_black_list &&
        universe_swift_campaign_type_black_list.count(campaign_type) > 0) {
      return true;
    }
  }
  return false;
}

// 搜索快投
bool IsQuickSearchData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = ad->GetExtension(Creative::creative_old);
    if (creative.creative_support_info().quick_search() == 1) {
      return true;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    return true;
  }
  return false;
}

bool IsGalaxyData(const kuaishou::ad::AdInstance* ad) {
  if (IsFanstopAccount(ad)) {
    return false;
  }
  if (IsFansTopOnly(ad)) {
    return false;
  }
  if (IsAdapterCreativeOnly(ad)) {
    return false;
  }
  if (IsDpaSdpaData(ad)) {
    return false;
  }
  if (IsSplashOnlyData(ad)) {
    return false;
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }
  return true;
}

static std::set<AdEnum::AdInstanceType> internal_check_types = {
    kuaishou::ad::AdEnum::CREATIVE, kuaishou::ad::AdEnum::UNIT,
    kuaishou::ad::AdEnum::CAMPAIGN};
static std::set<std::string> internal_check_types_sf = {
    "ad_dsp_creative", "ad_dsp_unit", "ad_dsp_campaign"};

bool IsInteralDataOnly(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (internal_check_types_sf.count(table_name) == 0) {
    return false;
  }
  if (table_name == "ad_dsp_creative") {
    auto creative_feature = static_cast<AdEnum::AdDspCreativeFeature>(
                              table_row->GetValue<int32_t>("creative_feature"));
    return ks::engine_base::IsCreativeInternalCirculation(creative_feature);
  } else if (table_name == "ad_dsp_unit") {
    auto unit_feature = static_cast<AdEnum::AdDspUnitFeature>(
                              table_row->GetValue<int32_t>("unit_feature"));
    return ks::engine_base::IsUnitInternalCirculation(unit_feature);
  } else if (table_name == "ad_dsp_campaign") {
    auto type = static_cast<AdEnum::CampaignType>(
                              table_row->GetValue<int32_t>("type"));
    return ks::engine_base::IsCampaignInternalCirculation(type);
  }
  return false;
}
bool IsInteralDataOnly(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (internal_check_types.count(msg_type) == 0) {
    return false;
  }
  auto* pb = ad;
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    const Creative& creative = pb->GetExtension(Creative::creative_old);
    auto creative_feature = creative.creative_feature();
    return ks::engine_base::IsCreativeInternalCirculation(creative_feature);
    // static const std::unordered_set<AdEnum::AdDspCreativeFeature>
    //     creative_feature_type_set{
    //         kuaishou::ad::AdEnum::CREATIVE_LIVE_STREAM_PROMOTION,
    //         kuaishou::ad::AdEnum::CREATIVE_MERCHANT_RECO_PROMOTION,
    //         kuaishou::ad::AdEnum::CREATIVE_FANSTOP_MERCHANT,
    //         kuaishou::ad::AdEnum::CREATIVE_FANSTOP_NO_MERCHANT,
    //         kuaishou::ad::AdEnum::CREATIVE_ESP_FLASH_PROMOTION,
    //         kuaishou::ad::AdEnum::CREATIVE_ESP_SPECIALTY_PROMOTION,
    //         kuaishou::ad::AdEnum::CREATIVE_ESP_HAOWU_PROMOTION,
    //         kuaishou::ad::AdEnum::CREATIVE_ESP_MOBILE_FEATURE,
    //         kuaishou::ad::AdEnum::CREATIVE_ESP_MOBILE_FEATURE_NO_MERCHANT};
    // if (creative_feature_type_set.count(creative_feature) > 0) {
    //   return true;
    // }
  } else if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = pb->GetExtension(Unit::unit_old);
    auto unit_feature = unit.unit_feature();
    return ks::engine_base::IsUnitInternalCirculation(unit_feature);
    // static const std::unordered_set<AdEnum::AdDspUnitFeature> expected_type_set{
    //     kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTION,
    //     kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTION,
    //     kuaishou::ad::AdEnum::FANSTOP_MERCHANT,
    //     kuaishou::ad::AdEnum::FANSTOP_NO_MERCHANT,
    //     kuaishou::ad::AdEnum::ESP_FLASH_PROMOTION,
    //     kuaishou::ad::AdEnum::ESP_SPECIALTY_PROMOTION,
    //     kuaishou::ad::AdEnum::ESP_HAOWU_PROMOTION,
    //     kuaishou::ad::AdEnum::UNIT_ESP_MOBILE_FEATURE,
    //     kuaishou::ad::AdEnum::UNIT_ESP_MOBILE_FEATURE_NO_MERCHANT};
    // if (expected_type_set.count(unit_feature) > 0) {
    //   return true;
    // }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign& campaign = pb->GetExtension(Campaign::campaign_old);
    auto type = campaign.type();
    return ks::engine_base::IsCampaignInternalCirculation(type);
    // static const std::unordered_set<AdEnum::CampaignType> type_type_set{
    //     kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE,
    //     kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE,
    //     kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS,
    //     kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL,
    //     kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_FANS,
    //     kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_SHOW,
    //     kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_ALL};
    // if (type_type_set.count(type) > 0) {
    //   return true;
    // }
  }
  return false;
}

bool IsBidServiceData(const kuaishou::ad::AdInstance* ad) {
  return true;
}

bool IsBidServiceData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "wt_pid_server_output") {
    auto pid_tag = static_cast<kuaishou::ad::PidServer::PidTag>(
                    table_row->GetValue<int32_t>("pid_tag"));
    auto pid_level = static_cast<kuaishou::ad::PidServer::PidLevel>(
                    table_row->GetValue<int32_t>("pid_level"));
    return pid_tag == kuaishou::ad::PidServer::PidTag::INSPIRE_PHOTO_ROI_TAG &&
           pid_level == kuaishou::ad::PidServer::PidLevel::ACCOUNT_LEVEL;
  }
  return true;
}

bool IsBudgetAllData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  auto* pb = ad;
  if (msg_type == kuaishou::ad::AdEnum::WT_PID_OUTPUT_DATA) {
    const WTPidOutputData& wt_pid_server_output = pb->GetExtension(WTPidOutputData::wt_pid_output_data_old);
    return (wt_pid_server_output.pid_tag() == kuaishou::ad::PidServer::PidTag::INSPIRE_PHOTO_ROI_TAG ||
          wt_pid_server_output.pid_tag() == kuaishou::ad::PidServer::PidTag::INCENTIVE_AUTO_DARK_BTR_TAG) &&
          wt_pid_server_output.pid_level() == kuaishou::ad::PidServer::PidLevel::ACCOUNT_LEVEL;
  }
  return true;
}

bool IsBudgetAllData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "wt_pid_server_output") {
    auto pid_tag = static_cast<kuaishou::ad::PidServer::PidTag>(
                    table_row->GetValue<int32_t>("pid_tag"));
    auto pid_level = static_cast<kuaishou::ad::PidServer::PidLevel>(
                    table_row->GetValue<int32_t>("pid_level"));
    return (pid_tag == kuaishou::ad::PidServer::PidTag::INSPIRE_PHOTO_ROI_TAG ||
            pid_tag == kuaishou::ad::PidServer::PidTag::INCENTIVE_AUTO_DARK_BTR_TAG) &&
           pid_level == kuaishou::ad::PidServer::PidLevel::ACCOUNT_LEVEL;
  }
  return true;
}

bool IsNotExternalBudget(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (table_name == "wt_ad_budget_status") {
    int32_t bit_info = table_row->GetValue<int32_t>("bit_info");
    auto unit_feature = static_cast<AdEnum::AdDspUnitFeature>(
                                  table_row->GetValue<int32_t>("unit_feature"));
    return ((bit_info & 1) != 1) || !ks::engine_base::IsUnitExternalCirculation(unit_feature);
  }
  return false;
}

// 内循环取反
bool IsExternalData(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  if (IsSplashImagesData(table_row)) {
    // 信息流过滤开屏图片广告
    return false;
  }
  if (IsNotExternalBudget(table_row)) {
    return false;
  }
  if (IsFanstopAccount(table_row)) {
    return false;
  }
  // 搜索直投智能扩量物料
  if (MultiFormKconfUtil::enableSearchMingtouToInnerOuter() && IsExtendSearchData(table_row)) {
    return true;
  }
  // 搜索直投人群追投物料
  if (MultiFormKconfUtil::enableSearchRetargetingToInnerOuter() && IsSearchPopulationRetargeting(table_row)) {
    return true;
  }
  if (IsSearchOnlyData(table_row)) {
    return false;
  }
  if (IsInteralDataOnly(table_row)) {
    return false;
  }
  if (IsDpaNotContainSdpa(table_row)) {
    return false;
  }
  if (internal_check_types_sf.count(table_name) == 0) {
    return true;
  }
  return true;
}
bool IsExternalData(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (IsFanstopAccount(ad)) {
    return false;
  }
  // 搜索直投智能扩量物料
  if (MultiFormKconfUtil::enableSearchMingtouToInnerOuter() && IsExtendSearchData(ad)) {
    return true;
  }
  // 搜索直投人群追投物料
  if (MultiFormKconfUtil::enableSearchRetargetingToInnerOuter() && IsSearchPopulationRetargeting(ad)) {
    return true;
  }
  if (IsSearchOnlyData(ad)) {
    return false;
  }

  if (IsInteralDataOnly(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) {
    return false;
  }
  if (internal_check_types.count(msg_type) == 0) {
    return true;
  }
  return true;
}

bool IsOrientationPreferData(const kuaishou::ad::AdInstance* ad) {
  static std::set<AdEnum::AdInstanceType> esp_check_types = {
      kuaishou::ad::AdEnum::CREATIVE, kuaishou::ad::AdEnum::UNIT,
      kuaishou::ad::AdEnum::CAMPAIGN, kuaishou::ad::AdEnum::ACCOUNT};
  auto msg_type = ad->type();

  if (esp_check_types.count(msg_type) == 0) {
    return true;
  }
  if (IsEspSpecialOnlyData(ad)) {
    return true;
  }
  if (IsEspFlashOnlyData(ad)) {
    return true;
  }
  return false;
}
bool IsHostingData(const ks::ad::build_service::TableRow* table_row) {
    // 兼容之前逻辑
  if (!IsUnivserNoAdapterData(table_row)) {
    return false;
  }
  if (IsDpaNotContainSdpa(table_row)) return false;

  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_unit") {
    static const std::unordered_set<AdEnum::UnitSourceType> unit_source_types{
        AdEnum::UNIT_SOURCE_TYPE_HOSTING,
        AdEnum::UNIT_SOURCE_TYPE_HOSTING_MCB,
        AdEnum::UNIT_SOURCE_TYPE_FURIOUS,
        AdEnum::UNIT_SOURCE_TYPE_BOOST};
    auto unit_source_type = static_cast<AdEnum::UnitSourceType>(
                              table_row->GetValue<int32_t>("unit_source_type"));
    if (unit_source_types.count(unit_source_type) == 0) {
      return false;
    }
  } else if (table_name == "ad_dsp_advanced_programmed_package") {
    static const std::unordered_set<AdEnum::AdDspCreativeType> package_type{
        AdEnum::AdDspCreativeType::AdEnum_AdDspCreativeType_HOSTING,
        AdEnum::AdDspCreativeType::AdEnum_AdDspCreativeType_CREATIVE_FURIOUS,
        AdEnum::AdDspCreativeType::AdEnum_AdDspCreativeType_CREATIVE_HOSTING_MCB,
    };
    auto derive_type = static_cast<AdEnum::AdDspCreativeType>(
                              table_row->GetValue<int32_t>("derive_type"));
    if (package_type.count(derive_type) == 0) {
      return false;
    }
  } else if (table_name == "ad_dsp_hosting_project") {
    auto project_type = static_cast<AdEnum::ProjectType>(
                              table_row->GetValue<int32_t>("project_type"));
    if (project_type != AdEnum::NORMAL_PROJECT) {
      return false;
    }
  } else if (table_name == "ad_dsp_campaign") {
    auto create_source_type = static_cast<AdEnum::CampaignCreateSourceType>(
                              table_row->GetValue<int32_t>("create_source_type"));
    // 主动清理波义尔
    if (create_source_type == AdEnum::CAMPAIGN_RAGE) {
      return false;
    }
  }
  return true;
}
bool IsHostingData(const kuaishou::ad::AdInstance* ad) {
  // 兼容之前逻辑
  if (!IsUnivserNoAdapterData(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) return false;

  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    static const std::unordered_set<AdEnum::UnitSourceType> unit_source_types{
        AdEnum::UNIT_SOURCE_TYPE_HOSTING,
        AdEnum::UNIT_SOURCE_TYPE_HOSTING_MCB,
        AdEnum::UNIT_SOURCE_TYPE_FURIOUS,
        AdEnum::UNIT_SOURCE_TYPE_BOOST};
    auto unit_source_type = kuaishou::ad::AdEnum_UnitSourceType(unit.unit_source_type());
    if (unit_source_types.count(unit_source_type) == 0) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_DSP_ADVANCED_PROGRAMMED_PACKAGE) {
    const AdDspAdvancedProgramedPackage &package =
        ad->GetExtension(AdDspAdvancedProgramedPackage::ad_dsp_advanced_programmed_package_old);
    static const std::unordered_set<AdEnum::AdDspCreativeType> package_type{
        AdEnum::AdDspCreativeType::AdEnum_AdDspCreativeType_HOSTING,
        AdEnum::AdDspCreativeType::AdEnum_AdDspCreativeType_CREATIVE_FURIOUS,
        AdEnum::AdDspCreativeType::AdEnum_AdDspCreativeType_CREATIVE_HOSTING_MCB,
    };
    if (package_type.count(package.derive_type()) == 0) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_DSP_HOSTING_PROJECT) {
    const AdDspHostingProject &project = ad->GetExtension(AdDspHostingProject::hosting_project_old);
    if (project.project_type() != AdEnum::NORMAL_PROJECT) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign &campaign = ad->GetExtension(Campaign::campaign_old);
    auto create_source_type = AdEnum::CampaignCreateSourceType(campaign.create_source_type());
    // 主动清理波义尔
    if (create_source_type == AdEnum::CAMPAIGN_RAGE) {
      return false;
    }
  }
  return true;
}

bool IsBoyleData(const ks::ad::build_service::TableRow* table_row) {
  // 兼容之前逻辑
  if (!IsUnivserNoAdapterData(table_row)) {
    return false;
  }
  if (IsDpaNotContainSdpa(table_row)) return false;
  CHECKTABLEROW(table_row)
  if (table_name == "ad_dsp_unit") {
    static const std::unordered_set<AdEnum::UnitSourceType> unit_source_types{
        AdEnum::UNIT_SOURCE_TYPE_RAGE};
    auto unit_source_type = kuaishou::ad::AdEnum_UnitSourceType(
                              table_row->GetValue<int32_t>("unit_source_type"));
    if (unit_source_types.count(unit_source_type) == 0) {
      return false;
    }
  } else if (table_name == "ad_dsp_hosting_project") {
    auto project_type = static_cast<AdEnum::ProjectType>(
                          table_row->GetValue<int32_t>("project_type"));
    if (project_type != AdEnum::BOYLE_PROJECT) {
      return false;
    }
  } else if (table_name == "ad_dsp_campaign") {
    auto create_source_type = AdEnum::CampaignCreateSourceType(
                                table_row->GetValue<int32_t>("create_source_type"));
    if (create_source_type != AdEnum::CAMPAIGN_RAGE) {
      return false;
    }
  }
  return true;
}
bool IsBoyleData(const kuaishou::ad::AdInstance *ad) {
  // 兼容之前逻辑
  if (!IsUnivserNoAdapterData(ad)) {
    return false;
  }
  if (IsDpaNotContainSdpa(ad)) return false;

  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::UNIT) {
    const Unit& unit = ad->GetExtension(Unit::unit_old);
    static const std::unordered_set<AdEnum::UnitSourceType> unit_source_types{
        AdEnum::UNIT_SOURCE_TYPE_RAGE};
    auto unit_source_type = kuaishou::ad::AdEnum_UnitSourceType(unit.unit_source_type());
    if (unit_source_types.count(unit_source_type) == 0) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_DSP_HOSTING_PROJECT) {
    const AdDspHostingProject& project = ad->GetExtension(AdDspHostingProject::hosting_project_old);
    if (project.project_type() != AdEnum::BOYLE_PROJECT) {
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::CAMPAIGN) {
    const Campaign &campaign = ad->GetExtension(Campaign::campaign_old);
    auto create_source_type = AdEnum::CampaignCreateSourceType(campaign.create_source_type());
    if (create_source_type != AdEnum::CAMPAIGN_RAGE) {
      return false;
    }
  }
  return true;
}

bool IsInternalData(const ks::ad::build_service::TableRow* table_row) {
  if (IsSplashImagesData(table_row)) {
    // 过滤开屏图片广告
    return false;
  }
  if (IsAmdData(table_row)) {
    return true;
  }
  if (IsFansTopData(table_row)) {
    return true;
  }
  // 搜索直投智能扩量物料
  if (MultiFormKconfUtil::enableSearchMingtouToInnerOuter() && IsExtendSearchData(table_row)) {
    return true;
  }
  // 搜索直投人群追投物料
  if (MultiFormKconfUtil::enableSearchRetargetingToInnerOuter() && IsSearchPopulationRetargeting(table_row)) {
    return true;
  }
  return false;
}
bool IsInternalData(const kuaishou::ad::AdInstance* ad) {
  if (IsAmdData(ad)) {
    return true;
  }
  if (IsFansTopData(ad)) {
    return true;
  }
  // 搜索直投智能扩量物料
  if (MultiFormKconfUtil::enableSearchMingtouToInnerOuter() && IsExtendSearchData(ad)) {
    return true;
  }
  // 搜索直投人群追投物料
  if (MultiFormKconfUtil::enableSearchRetargetingToInnerOuter() && IsSearchPopulationRetargeting(ad)) {
    return true;
  }
  return false;
}

// 开屏过滤 维护 @zhangyunhao03
bool IsStyleSplash(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("Splash");
  if (black_iter == configs.black_configs().end()) { return true; }

  // [todo]zrk 这里的 ad_style_material 没有配置，后面再 check
  // [todo]zrk 这里的 ad_matrix_style_material 没有配置，后面再 check
  if (table_name == "ad_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = table_row->GetValue<int32_t>("style_type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_splash",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (table_name == "ad_matrix_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = table_row->GetValue<int32_t>("type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_splash",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  }
  return true;
}
bool IsStyleSplash(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("Splash");
  if (black_iter == configs.black_configs().end()) { return true; }

  if (msg_type == kuaishou::ad::AdEnum::AD_STYLE_MATERIAL) {
    const AdStyleMaterial& style =
        ad->GetExtension(AdStyleMaterial::ad_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = style.style_type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_splash",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_MATRIX_STYLE_MATERIAL) {
    const AdMatrixStyleMaterial& style =
        ad->GetExtension(AdMatrixStyleMaterial::ad_matrix_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = style.type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_splash",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  }
  return true;
}

// 主站过滤 维护 @zhangyunhao03
bool IsStyleDefault(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("Default");
  if (black_iter == configs.black_configs().end()) { return true; }

  // [todo]zrk 这里的 ad_style_material 没有配置，后面再 check
  // [todo]zrk 这里的 ad_matrix_style_material 没有配置，后面再 check
  if (table_name == "ad_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = table_row->GetValue<int32_t>("style_type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_default",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (table_name == "ad_matrix_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = table_row->GetValue<int32_t>("type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_default",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (table_name == "ad_esp_product_label_info") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_esp_product_label_info__type().begin(),
                           black_iter->second.ad_esp_product_label_info__type().end());
    auto label_type = table_row->GetValue<int32_t>("type");
    if (not_expect_type.count(label_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_default",
                              "AD_ESP_PRODUCT_LABEL_INFO", absl::StrCat(label_type));
      return false;
    }
  }
  return true;
}
bool IsStyleDefault(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("Default");
  if (black_iter == configs.black_configs().end()) { return true; }

  if (msg_type == kuaishou::ad::AdEnum::AD_STYLE_MATERIAL) {
    const AdStyleMaterial& style =
        ad->GetExtension(AdStyleMaterial::ad_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = style.style_type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_default",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_MATRIX_STYLE_MATERIAL) {
    const AdMatrixStyleMaterial& style =
        ad->GetExtension(AdMatrixStyleMaterial::ad_matrix_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = style.type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_default",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_ESP_PRODUCT_LABEL_INFO) {
    const AdEspProductLabelInfo& label_info =
        ad->GetExtension(AdEspProductLabelInfo::ad_esp_product_label_info_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_esp_product_label_info__type().begin(),
                           black_iter->second.ad_esp_product_label_info__type().end());
    auto label_type = label_info.type();
    if (not_expect_type.count(label_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_default",
                              "AD_ESP_PRODUCT_LABEL_INFO", absl::StrCat(label_type));
      return false;
    }
  }
  return true;
}

// 联盟过滤 维护 @zhangyunhao03
bool IsStyleUniverse(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("Universe");
  if (black_iter == configs.black_configs().end()) { return true; }

  // [todo]zrk 这里的 ad_style_material 没有配置，后面再 check
  // [todo]zrk 这里的 ad_matrix_style_material 没有配置，后面再 check
  if (table_name == "ad_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = table_row->GetValue<int32_t>("style_type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (table_name == "ad_matrix_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = table_row->GetValue<int32_t>("type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  }
  return true;
}
bool IsStyleUniverse(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("Universe");
  if (black_iter == configs.black_configs().end()) { return true; }

  if (msg_type == kuaishou::ad::AdEnum::AD_STYLE_MATERIAL) {
    const AdStyleMaterial& style =
        ad->GetExtension(AdStyleMaterial::ad_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = style.style_type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_MATRIX_STYLE_MATERIAL) {
    const AdMatrixStyleMaterial& style =
        ad->GetExtension(AdMatrixStyleMaterial::ad_matrix_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = style.type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_universe",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  }
  return true;
}

// 联盟素材过滤 维护 @zhangyunhao03
bool IsMaterialUniverse(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("MaterialUniverse");
  if (black_iter == configs.black_configs().end()) { return true; }

  // [todo]zrk 这里的 ad_style_material 没有配置，后面再 check
  // [todo]zrk 这里的 ad_matrix_style_material 没有配置，后面再 check
  if (table_name == "ad_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = table_row->GetValue<int32_t>("style_type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_material_universe",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (table_name == "ad_matrix_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = table_row->GetValue<int32_t>("type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_material_universe",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  }
  return true;
}
bool IsMaterialUniverse(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("MaterialUniverse");
  if (black_iter == configs.black_configs().end()) { return true; }

  if (msg_type == kuaishou::ad::AdEnum::AD_STYLE_MATERIAL) {
    const AdStyleMaterial& style =
        ad->GetExtension(AdStyleMaterial::ad_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = style.style_type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_material_universe",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_MATRIX_STYLE_MATERIAL) {
    const AdMatrixStyleMaterial& style =
        ad->GetExtension(AdMatrixStyleMaterial::ad_matrix_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = style.type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_material_universe",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  }
  return true;
}

// 样式通用过滤 维护 @zhangyunhao03
bool IsStyleAll(const ks::ad::build_service::TableRow* table_row) {
  CHECKTABLEROW(table_row)
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("All");
  if (black_iter == configs.black_configs().end()) { return true; }

  // [todo]zrk 这里的 ad_style_material 没有配置，后面再 check
  // [todo]zrk 这里的 ad_matrix_style_material 没有配置，后面再 check
  if (table_name == "ad_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = table_row->GetValue<int32_t>("style_type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_all",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (table_name == "ad_matrix_style_material") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = table_row->GetValue<int32_t>("type");
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_all",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (table_name == "ad_esp_product_label_info") {
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_esp_product_label_info__type().begin(),
                           black_iter->second.ad_esp_product_label_info__type().end());
    auto label_type = table_row->GetValue<int32_t>("type");
    if (not_expect_type.count(label_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_all",
                              "AD_ESP_PRODUCT_LABEL_INFO", absl::StrCat(label_type));
      return false;
    }
  }
  return true;
}
bool IsStyleAll(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  auto configs = MultiFormKconfUtil::styleServerIndexBlackFilter()->data();
  auto black_iter = configs.black_configs().find("All");
  if (black_iter == configs.black_configs().end()) { return true; }

  if (msg_type == kuaishou::ad::AdEnum::AD_STYLE_MATERIAL) {
    const AdStyleMaterial& style =
        ad->GetExtension(AdStyleMaterial::ad_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_style_material__style_type().begin(),
                           black_iter->second.ad_style_material__style_type().end());
    auto style_type = style.style_type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_all",
                              "AD_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_MATRIX_STYLE_MATERIAL) {
    const AdMatrixStyleMaterial& style =
        ad->GetExtension(AdMatrixStyleMaterial::ad_matrix_style_material_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_matrix_style_material__type().begin(),
                           black_iter->second.ad_matrix_style_material__type().end());
    auto style_type = style.type();
    if (not_expect_type.count(style_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_all",
                              "AD_MATRIX_STYLE_MATERIAL", absl::StrCat(style_type));
      return false;
    }
  } else if (msg_type == kuaishou::ad::AdEnum::AD_ESP_PRODUCT_LABEL_INFO) {
    const AdEspProductLabelInfo& label_info =
        ad->GetExtension(AdEspProductLabelInfo::ad_esp_product_label_info_old);
    std::unordered_set<int32_t> not_expect_type;
    not_expect_type.insert(black_iter->second.ad_esp_product_label_info__type().begin(),
                           black_iter->second.ad_esp_product_label_info__type().end());
    auto label_type = label_info.type();
    if (not_expect_type.count(label_type) != 0) {
      PerfUtil::CountLogStash(1, "ad.index_message_proxy", "filtered_by_all",
                              "AD_ESP_PRODUCT_LABEL_INFO", absl::StrCat(label_type));
      return false;
    }
  }
  return true;
}

// 全量虚拟创意
bool IsStoreWideVirtualCreative(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    auto& creative = ad->GetExtension(Creative::creative_old);
    if (creative.creative_support_info().auto_deliver_type() ==
        kuaishou::ad::AdEnum_AutoDeliverType_STOREWIDE_VIRTUAL_CREATIVE) {
      // 过滤全店虚拟创意
      return true;
    }
  }
  return false;
}
bool IsStoreWideVirtualCreative(const ks::ad::build_service::TableRow* table_row) {
  if (table_row == nullptr) {
    return false;
  }
  auto table_name = table_row->GetTableName();
  if (table_name.empty()) {
    LOG_EVERY_N(WARNING, 1000) << "get table name fail";
    return false;
  }
  if (table_name == "ad_dsp_creative") {
    auto type = static_cast<AdEnum::AutoDeliverType>(
                 table_row->GetValue<int32_t>("auto_deliver_type"));
    if (type == kuaishou::ad::AdEnum_AutoDeliverType_STOREWIDE_VIRTUAL_CREATIVE) {
      // 过滤全店虚拟创意
      return true;
    }
  }
  return false;
}

// 商品卡创意
bool IsItemCardCreative(const kuaishou::ad::AdInstance* ad) {
  auto msg_type = ad->type();
  if (msg_type == kuaishou::ad::AdEnum::CREATIVE) {
    auto& creative = ad->GetExtension(Creative::creative_old);
    if (creative.creative_material_type() ==
        kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD) {
      return true;
    }
  }
  return false;
}
bool IsItemCardCreative(const ks::ad::build_service::TableRow* table_row) {
  if (table_row == nullptr) {
    return false;
  }
  auto table_name = table_row->GetTableName();
  if (table_name.empty()) {
    LOG_EVERY_N(WARNING, 1000) << "get table name fail";
    return false;
  }
  if (table_name == "ad_dsp_creative") {
    auto type = static_cast<AdEnum::CreativeMaterialType>(
                 table_row->GetValue<int32_t>("creative_material_type"));
    if (type == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD) {
      return true;
    }
  }
  return false;
}

bool IsI18nAll(const kuaishou::ad::AdInstance* ad) { return true; }

bool IsI18nAll(const ks::ad::build_service::TableRow* table_row) {
  LOG_EVERY_N(INFO, 100) << "ldebug process admit row "
                         << const_cast<ks::ad::build_service::TableRow*>(table_row)->ShortDebugString();
  return true;
}


}  // namespace index_builder
}  // namespace ks
