/* 
Copy From: kuaishou-ad-c2s-proto-1.0.213-jar/kuaishou/ad/common_ad_log.proto
Sync Time: 2025-09-03 11:13:37
*/
syntax = "proto3";

package kuaishou.ad;


enum AdActionType {
  UNKNOWN_ACTION_TYPE = 0;

  // ad item
  AD_ITEM_IMPRESSION = 1; // 打开播放页就上报,区分上下滑点击、进入方式放在client_params,itemShowType，直播预约投快享卡片参数放入了client_params
  AD_ITEM_CLICK = 2; // 点击转化链接，四个地方：蓝条，重播转化按钮，caption“查看详情”，电商caption首位链接,client_params中增加itemClickType，直播预约投快享卡片参数放入了client_params
  AD_ITEM_CLOSE = 3; // 返回、上滑、下滑->关闭，仅在这三种情况下上报，需要上报各种播放时长（分享回来、切home键、进profile返回->resume),关闭方式放在client_params,itemCloseType
  AD_ITEM_NEGATIVE = 4; // 播放页减少此类作品
  AD_ITEM_BOTTOM_ICON_IMPRESSION = 5; // 详情页右下角转化链接icon曝光
  AD_ITEM_IOS_NEW_CLICK = 6; // iOS新详情页item_click上报,仅限iOS新详情页
  AD_ITEM_MEDIA_BAR_IMPRESSION = 7; // 全屏视频下,转化入口弹现时上报,需要上报弹窗类型MediaBarType
  AD_ITEM_MEDIA_BAR_CLOSE = 8; // 全屏视频下,转化入口关闭时上报,需要上报弹窗类型MediaBarType
  AD_ITEM_COMMENTS_BAR_IMPRESSION = 9; // 评论区首屏转化条曝光包括android&ios
  AD_ITEM_DOWNLOAD_STARTED_AUTO = 610; //触发了自动下载功能，开始下载时上报
  AD_ITEM_DOWNLOAD_COMPLETED_AUTO = 611; //自动下载完成时上报
  AD_ITEM_DOWNLOAD_DELETED_AUTO = 612; //触发了预下载功能，删除下载内容时上报

  // ad photo
  AD_PHOTO_IMPRESSION = 10; // 展示，瀑布流
  AD_PHOTO_CLICK = 11; // 点击，瀑布流
  AD_PHOTO_LIKE = 12; // 点赞，红心+双击
  AD_PHOTO_COMMENT = 13; // 转发，不区分原始评论/回复别人
  AD_PHOTO_FOLLOW = 14; // 关注，详情页点关注
  AD_PHOTO_SHARE = 15; // 转发，需要上报转发的目标渠道,client_params带上转发渠道
  AD_PHOTO_CANCEL_LIKE = 16; // 取消点赞
  AD_PHOTO_REPORT = 17; // 举报
  AD_PHOTO_NEGATIVE = 18; // 瀑布流减少此类作品
  AD_PHOTO_UNFOLLOW = 19; // 取消关注
  AD_PHOTO_BLOCK = 20; // 加入黑名单
  AD_PHOTO_PLAYED_3S = 21; // 已播放 3 秒（第一遍）
  AD_PHOTO_PLAYED_5S = 22; // 已播放 5 秒（第一遍）
  AD_PHOTO_PLAYED_END = 23; // 已播放完成（第一遍）
  AD_PHOTO_REPLAYED = 24; // 视频重播
  AD_PHOTO_HATE = 25; // 点踩
  AD_PHOTO_CANCEL_HATE = 26; // 取消点踩
  AD_HIDE_PHOTO_INFO = 27; // 点击视频播放页面，隐藏视频信息时上报
  AD_PHOTO_LEAVE = 28; // 看完视频返回发现页信息流
  AD_PHOTO_50_IMPRESSION = 29; // 展示超过50%时上报

  // Android downloader
  AD_ITEM_DOWNLOAD_STARTED = 30; // 开始下载
  AD_ITEM_DOWNLOAD_COMPLETED = 31; // 下载完成
  AD_ITEM_DOWNLOAD_INSTALLED = 32; // 安装完成，同时上报参数IsPackageChanged
  AD_ITEM_DOWNLOAD_PAUSED = 33; // 暂停，同步上报下载进度
  AD_ITEM_DOWNLOAD_RESUMED = 34; // 继续
  AD_ITEM_DOWNLOAD_DELETED = 35; // 删除，同步上报下载进度
  AD_ITEM_DOWNLOAD_LOW_STORAGE = 36; // 内存不足，同步上报下载进度
  AD_ITEM_DOWNLOAD_INSTALL_STARTED = 37; // 点击"立即安装"
  AD_ITEM_DOWNLOAD_OPENED = 38; // 点击"立即打开"
  AD_STORAGE_PERMISSION_REFUSED = 39; // 拒绝授权存储权限
  AD_ITEM_DOWNLOAD_FAILED = 40; // 下载失败，同时上报失败原因download_failed_reason
  AD_ITEM_DOWNLOAD_FORCE_OPENED = 41; // 安卓下载类广告安装后强制打开app

  // Android install
  AD_ITEM_INSTALL_NOTICE_CONFIRMED = 45; //应用下载未安装提示,确认
  AD_ITEM_INSTALL_NOTICE_CANCELLED = 46; //应用下载未安装提示,取消
  AD_ITEM_CONVERSION_NOTICE_CONFIRMED = 47; //应用下载未激活提示,确认
  AD_ITEM_CONVERSION_NOTICE_CANCELLED = 48; //应用下载未激活提示,取消

  // ad landing page
  AD_LANDING_PAGE_ENTERED = 50; // 进入广告H5页面时上报,添加来源入口参数ClientParams.LandingPageEntrySource,页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_LOADED = 51; // 请求第一个元素返回时,添加来源入口参数ClientParams.LandingPageEntrySource,页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_CLOSED = 52; // 页面退出：右滑和关闭,添加来源入口参数ClientParams.LandingPageEntrySource,页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_FORM_SUBMITTED = 53; // 落地页面点击“提交”时上报,添加来源入口参数ClientParams.LandingPageEntrySource,页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_POP = 54; // 落地页弹出,添加页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_UP_SLIDE = 55; // 落地页被上滑（打开),添加页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_DOWN_SLIDE = 56; // 落地页被下滑（关闭),添加页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_CLICK = 57; // 落地页面内有效点击行为,添加页面类型参数ClientParams.LandingPageType
  AD_LANDING_PAGE_RETURNED = 58; // 落地页返回按钮点击成功即上报
  AD_LANDING_PAGE_ENTER_FAILED = 59 ; // 前置落地页加载失败&落地页加载失败

  //living action
  AD_LIVE_IMPRESSION = 60; //直播展现
  AD_LIVE_CLICK = 61; //直播点击
  AD_LIVE_PLAYED_3S = 62; //直播播放3秒
  AD_LIVE_PLAYED_1M = 63; //直播播放一分钟
  AD_LIVE_PLAYED_MEDIAN = 64; //直播播放时间到达统计中位数时间(当前14s)
  AD_LIVE_PLAYED_MEAN = 65; //直播播放时间到达统计平均数时间(当前200s)
  AD_LIVE_PLAYED_END = 66; //直播播放完毕 // 20200609 同时上报played_seconds
  AD_LIVE_LEAVE = 67; //离开直播间
  AD_LIVE_PLAYED_STARTED = 68; //进入直播间开始直播
  AD_LIVE_PLAYED_SECONDS = 69; //直播播放x秒事件，配合played_seconds参数使用

  //ad report 广告举报
  AD_PHOTO_REPORT_SUBMIT = 70; //提交广告举报信息，点击广告举报提交按钮,client_params带上举报类型
  AD_PHOTO_50_IMPRESSION_1S = 71; // 封面50%曝光超过1s

  // 信息流电商的涨粉行为
  AD_MERCHANT_FOLLOW = 72; // 信息流电商曝光一定时间后的关注为涨粉

  //开屏广告 85 ~ 99
  AD_SPLASH_IMPRESSION = 85;  // 开屏广告曝光
  AD_SPLASH_CLICK = 86;       // 开屏广告点击（包括开屏action_bar的点击，行为与开屏点击一致）
  AD_SPLASH_FEED_PLAY_START = 87;   // 开屏缩短到信息流进行动画播放成功
  AD_SPLASH_FAIL = 88; // 开屏曝光失败
  AD_SPLASH_PRELOAD_START = 89; // 开屏素材开始预加载
  AD_SPLASH_PRELOAD_SUCCESS = 90; // 开屏素材预加载成功
  AD_SPLASH_PRELOAD_FAIL = 91; // 开屏素材预加载失败


  //电商事件
  AD_MERCHANT_CAPTION_URL_CLICK = 100; //电商正文caption url及第三方链接点击上报，通过put_type及id来确认点击具体内容
  AD_MERCHANT_YELLOW_SHOPPING_TROLLEY_IMPRESSION = 101; //展示电商作品黄色购物车,补充参数ext_data.default_type标识默认态
  AD_MERCHANT_YELLOW_SHOPPING_TROLLEY_OPEN = 102; //打开电商作品黄色购物车,补充参数ext_data.default_type标识默认态
  AD_MERCHANT_YELLOW_SHOPPING_TROLLEY_CLOSE = 103; //关闭电商作品黄色购物车,补充参数ext_data.default_type标识默认态
  AD_MERCHANT_PHOTO_ITEM_FLOAT_WINDOW_IMPRESSION = 104; //展示电商作品商品悬浮卡片,补充参数ext_data.default_type标识默认态,ext_data.expand_type标识展开方式
  AD_MERCHANT_PHOTO_ITEM_FLOAT_WINDOW_CLICK = 105; //点击电商作品商品卡片,补充参数ext_data.default_type标识默认态
  AD_COURSE_FREE_CONTENT_IMPRESSION = 106; //课堂免费内容卡片曝光
  AD_COURSE_FREE_CONTENT_CLICK = 107; // 课堂免费内容卡片点击
  AD_MERCHANT_PHOTO_ITEM_FLOAT_WINDOW_CLOSE = 108; //点击电商作品商品卡片关闭按钮,补充参数ext_data.default_type标识默认态
  AD_MERCHANT_THANOS_PHOTO_ITEM_IMPRESSION = 109; //滑滑版短视频商品曝光
  AD_MERCHANT_THANOS_PHOTO_ITEM_CLICK = 110; //滑滑板短视频商品点击
  AD_MERCHANT_YELLOW_SHOPPING_TROLLEY_CLICK = 111; //弱样式小黄车点击
  AD_MERCHANT_ORDER_PAY_RESULT_PAGE_IMPRESSION = 112; //订单支付成功页面曝光
  AD_MERCHANT_THANOS_PHOTO_ITEM_V1_IMPRESSION = 113; //滑滑板短视频商品曝光,小黄车图标空心样式
  AD_MERCHANT_THANOS_PHOTO_ITEM_V1_CLICK = 114; //滑滑板短视频商品点击,小黄车图标空心样式
  AD_MERCHANT_ITEM_CARD_CLICK = 115; //电商商品卡点击事件

  // Profile页相关
  AD_PHOTO_TO_PROFILE = 120; //从详情页进入Profile页:左滑，非直播状态点击头像，点击视频简单作者名称，点击评论区作者回复评论名称
  AD_PHOTO_TO_PROFILE_FOLLOW = 121; //从详情页进入Profile页，点击关注
  AD_PHOTO_TO_PROFILE_UNFOLLOW_CLICK = 122; //从详情页进入Profile页，点击取消关注
  AD_PHOTO_TO_PROFILE_UNFOLLOW_CONFIRM = 123; //从详情页进入Profile页，确定取消关注
  AD_PHOTO_TO_LIVE = 124; // 从详情页进入直播页:直播状态点击头像
  AD_PHOTO_TO_PROFILE_TO_LIVE = 125; // 从粉条作品详情页进入profile页再进直播


  // 广告素材相关行为 140 ~ 159
  AD_ELEMENT_IMPRESSION = 140;    // 标识广告元素的曝光，配合参数ElementType 标识具体的广告元素
  AD_ELEMENT_CLICK = 141;         // 标识广告元素的点击，配合参数ElementType 标识具体的广告元素
  AD_PENDANT_CLICK = 142;         // 红包挂件点击

  AD_OPEN_DOWNLOAD_CENTER_ITEM = 150; //进入下载中心点击相关app(or h5)

  // 详情页相关事件
  AD_DETAIL_PAGE_CLOSED = 160; //退出详情页，返回、上滑、下滑->关闭，仅在这三种情况下上报，需要上报各种播放时长

  AD_EXPAND_COMMENT_DIALOG = 170; // 评论弹窗展开：1：上下滑状态详情页底部评论按钮点击，2：上下滑部分评论状态下详情页底部评论按钮点击，3：上下滑部分评论状态下展开更多评论按钮点击,
  // 上报时client_params带上弹窗展开方式
  AD_CONVERSION = 180; //激活
  AD_PURCHASE = 190; //付费
  AD_ROAS = 191; // Return on Ad Spend 目前没有数据
  AD_MERCHANT_ROAS = 192; // 小店电商 ROAS 出价产品
  AD_MERCHANT_T7_ROI = 193; // 7日ROI出价产品

  AD_PHOTO_INTRO = 200; // 快手广告共享计划介绍

  AD_NEW_DETAIL_PAGE_LOADED = 210; //  新详情页加载完成、WebView内页面加载完成

  // 负反馈相关事件
  // 还有已经定义的AD_PHOTO_NEGATIVE AD_ITEM_NEGATIVE
  AD_PHOTO_NEGATIVE_IMPRESSION = 220;
  AD_ITEM_NEGATIVE_IMPRESSION = 221;
  AD_ITEM_DISLIKE_IMPRESSION = 222;
  AD_ITEM_SHIELD_IMPRESSION = 223;
  AD_ITEM_QUALITY_PANEL_IMPRESSION = 224;
  AD_ITEM_QUALITY_PANEL_CLICK = 225;

  // 通用卡片事件
  AD_ACTIONBAR_COLOR_CHANGED = 240;
  AD_CARD_IMPRESSION = 241;
  AD_CARD_IMPRESSION_FAILED = 242;
  AD_CARD_CLOSE = 243;

  //live action 添加
  AD_SIMPLE_LIVE_CLICK = 280; // 直播点击,点击简版直播间进入正式直播间时上报
  AD_LIVE_SHOP_IMPRESSION = 281; // 直播购物车曝光
  AD_LIVE_ITEM_IMPRESSION = 282; // 直播商品列表曝光,同时上报AdLiveItemImpressionType
  AD_LIVE_JUMP_CLICK = 283; // 点击简版直播间进入正式直播间时上报
  AD_LIVE_TO_PROFILE = 284; // （极速版+主APP设置版）下直播直投：当滑到直播流且为结束态时，显示5s倒计时，期间点击进入Profile页,同时上报AD_LIVE_TO_PROFILE_TYPE
  AD_LIVE_SKU_ITEM_IMPRESSION = 285; //半屏sku页商品曝光
  LIVE_STREAM_COMPONENT_CLICK = 286; // 直播组件点击，仅用作优化目标，转化点为小铃铛点击AD_SOCIAL_LIVE_CONVERSION_ENTRANCE_CLICK和小钥匙点击AD_BUTTON_CLICK https://team.corp.kuaishou.com/task/********

  AD_LIVE_FOLLOW = 300; //直播关注
  AD_LIVE_LIKE = 301; //直播点赞
  AD_LIVE_REWARD = 302; //直播打赏
  AD_LIVE_SHARE = 303; //直播分享
  AD_LIVE_CANCEL_FOLLOW = 304; //直播取消关注
  AD_LIVE_REPORT = 305; //直播举报
  AD_LIVE_HATE = 306; //直播hate上报
  AD_LIVE_SHOP_CLICK = 307; //直播购物车点击 // 20200606 同时上报AdLiveShopClickType
  AD_LIVE_SHOP_LINK_JUMP = 308; //直播购物车链接跳转 // 20200606 同时上报AD_LIVE_SHOP_LINK_JUMP_TYPE&merchant_item_id(商品ID)
  AD_LIVE_LANDING_PAGE_LOAD_FAILED = 309; //直播落地页加载失败

  AD_NEW_DETAIL_PAGE_CLICK = 310; // 视频播放页改造:WebView页面内有效点击行为
  AD_NEW_DETAIL_PAGE_ENTERED = 311; // WebView内页面开始加载
  AD_NEW_DETAIL_PAGE_TAB_CLICK = 312; // 详情页tab点击，具体点击位置通过ClientParams.NewDetailPageTabClickType上报
  AD_NEW_DETAIL_PAGE_GUIDE_IMPRESSION = 313; // 新详情页引导按钮曝光
  AD_NEW_DETAIL_PAGE_GUIDE_CLICK = 314; // 新详情页引导按钮点击

  // APP Deeplink 事件 320 ~ 329
  AD_DEEPLINK_EVOCATION_SUCCESS = 320; // Deeplink唤起成功
  AD_DEEPLINK_EVOCATION_FAIL = 321; // Deeplink唤起失败，可以上报失败原因deeplink_failed_reason
  AD_DEEPLINK_REBOUND_SUCCESS = 322; // Deeplink回跳成功
  AD_APPSTORE_DEEPLINK_EVOCATION_SUCCESS = 323; // 厂商应用商店详情页deeplink唤起成功

  EVENT_APP_INVOKED = 324;  // app唤端

  AD_KUAIXIANG_NEGATIVE = 330; // 快享计划主动关闭广告

  AD_POI_LABEL_IMPRESSION = 340; // POI字段曝光
  AD_POI_LABEL_CLICK = 341; // POI字段点击
  AD_POI_DETAIL_PAGE_IMPRESSION = 342; // POI详情页曝光
  AD_POI_DETAIL_PAGE_DIAL_CLICK = 343; // POI详情页-电话拨打点击数据
  AD_POI_DETAIL_PAGE_ITEM_CLICK = 344; // POI详情页-action按钮点击

  EVENT_NEXTDAY_STAY = 346; //次日留存（自然日）
  EVENT_WEEK_STAY = 347; //7日留存
  EVENT_VALID_CLUES = 348; //有效线索
  EVENT_PAY = 349; //付费

  AD_IMPRESSION_MODULE = 380; // 快享计划广告按钮转化条曝光

  AD_ITEM_MEDIA_BAR_CLICK = 381; // 广告信息搜集卡片转化点击
  AD_ITEM_MEDIA_BAR_DETAIL = 382; // 广告信息搜集卡片了解详情点击

  AD_CREDIT_GRANT = 383; // 广告用户授信
  EVENT_JINJIAN = 384; // 金融广告完件

  AD_DEEPLINK_EVOCATION_WEB_RECEIVE = 385; //跳转信息接收监听
  AD_DEEPLINK_EVOCATION_WEB_SUCCESS = 386; //跳转成功

  AD_PHOTO_IMPRESSION_END = 387; //信息流作品曝光结束
  AD_BRAND_TOP_IMPRESSION = 388; //该下拉广告素材曝光结束触发，动作以素材为准

  EVENT_ACTIVE = 389; //打开APP、被认为一次活跃的事件
  EVENT_HIGH_QUALITY = 390;//优质用户，优化模型使用
  EVENT_FORM_SUBMIT = 391; //表单提交
  EVENT_GOODS_VIEW = 392; //电商的商品浏览行为
  EVENT_ADD_SHOPPINGCART = 393; //电商的添加购物车
  EVENT_ORDER_SUBMIT = 394; //电商的提交订单成功
  EVENT_ORDER_PAIED = 395; //订单付款成功
  EVENT_REGISTER = 396; // 注册
  AD_TEST_EVENT = 397; // 主要用于新增临时样式的特殊行为（如点击浮窗视频放大为后贴片全屏广告）

  AD_DELIVERY = 398; // 广告从 athena 吐出事件
  AD_ITEM_PLAY_START = 399;  // 广告开始播放 用于中台
  AD_ITEM_PLAY_END = 400;  // 广告播放完成 用于中台
  AD_ITEM_CLICK_BACK = 401; // 广告视频详情页二跳点击并返回
  AD_PHOTO_PLAYED_NS = 402; // 广告播放play N秒
  AD_APPROXIMATE_PURCHASE = 403; // 近似购买
  // 20200420 新加
  EVENT_BUTTON_CLICK = 404; // 按钮点击，后续会作为一个ocpc_action_type
  AD_PHOTO_PLAYED_2S = 405; // 广告播放play 2秒
  EVENT_ORDER_PAIED_FOR_MODEL = 406; // 订单付款成功 for 模型

  //直营电商事件
  AD_ECOM_SHOPPING_CARD_IMPRESSION = 430; //直营电商卡片视频区曝光
  AD_ECOM_SHOPPING_CARD_COMMENT_AREA_IMPRESSION = 431; //直营电商卡片评论区曝光
  AD_ECOM_SHOPPING_TROLLEY_IMPRESSION = 432; //直营电商小购物车曝光
  AD_ECOM_SHOPPING_TROLLEY_CLICK = 433; //直营电商小购物车点击

  // 极速版广告播放页&播放结束页埋点需求
  AD_DOWNLOAD_BOX_IMPRESSION = 434; // 仅使用于android端下载类场景，点击相关按钮后触发曝光立即下载悬浮框
  AD_DOWNLOAD_BOX_CANCELLED = 435; // 仅使用于android端下载类场景，立即下载悬浮框关闭，需要上报BoxCancelledType

  AD_DOWNLOAD_MANAGEMENT_IMPRESSION = 440; //Android&iOS,点击设置页后，进入下载管理器页面曝光；同时上报任务个数参数download_amount；同时上报展开按钮是否出现参数is_download_extend
  AD_DOWNLOAD_DELETE_POPUP_IMPRESSION = 445; //Android&iOS,从下载管理器页面，删除弹窗页面曝光

  // 广告弹窗
  AD_CONFIRM_CLOSED_CARD_IMPRESSION = 450; // 确认退出广告卡片曝光
  AD_CONTINUE_VIEW_CLICK = 451; // 用户点击继续观看按钮触发上报

  // AD ITEM 无位置，后续接这里
  AD_ITEM_SKIPED = 501; // 用户成功点击跳过按钮
  AD_ITEM_IMPRESSION_1FRAME = 502; // 第一帧曝光

  //POI打点相关
  AD_POI_COMMENT_AREA_IMPRESSION = 520; //POI评论区位置曝光

  //快接单
  AD_SOCIAL_NATIVE_IMPRESSION = 530; //快接单弱样式卡片曝光

  AD_ITEM_PLAY_END_CARD_IMPRESSION = 531; //当视频播放结束，且通用卡片元素渲染完毕后上报一条播放结束页卡片曝光；两种样式播放结束语均统一上报

  //快享相关
  AD_KUAIXIANG_PREVIOUS_IMPRESSION = 532; //快享极速版-前链曝光，作品播放Ns后出现转化条记一次曝光
  AD_KUAIXIANG_PREVIOUS_CLICK = 533; //快享极速版-前链点击，点击转化条记一次点击

  //直播预约投快享相关
  AD_LIVE_RESERVATION_HALF_IMPRESSION = 534; //直播预约半层曝光
  AD_LIVE_RESERVATION_HALF_BUTTON_CLICK = 535; //直播预约半层按钮点击
  AD_LIVE_RESERVATION_HALF_HEAD_CLICK = 536;  //直播预约半层头像点击
  AD_LIVE_RESERVATION_SUCCESS = 537; //直播预约成功
  AD_LIVE_RESERVATION_FAIL = 538;  //直播预约失败
  AD_LIVE_RESERVATION_PUSH_IMPRESSION = 539; //直播预约push权限弹窗曝光，弹窗参数放入了ClientParams.live_reservation_push
  AD_LIVE_RESERVATION_PUSH_CLICK = 540; //直播预约push权限弹窗点击,弹窗参数放入了ClientParams.live_reservation_push
  AD_LIVE_RESERVATION_CANCEL = 541; //直播预约取消

  //快直播打点
  AD_SOCIAL_LIVE_CONVERSION_ENTRANCE_CLICK = 550; //快直播转化入口点击
  AD_SOCIAL_LIVE_CONVERSION_HINT_IMPRESSION = 551; //快直播转化提示曝光
  AD_SOCIAL_LIVE_CONVERSION_FORM_IMPRESSION = 552; //快直播转化表格曝光
  AD_SOCIAL_LIVE_CONVERSION_FORM_CLICK = 553; //快直播转化表格输入，输入任意一项就上报，只上报一次
  AD_SOCIAL_LIVE_CONVERSION_FORM_SUBMIT = 554; //快直播转化表格提交，预约成功时上报
  AD_SOCIAL_LIVE_CONVERSION_ENTRANCE_IMPRESSION = 555; //快直播转化任务入口曝光

  //粉条事件
  AD_FANS_TOP_SERVER_FOLLOW = 570; //粉条服务端的关注事件，客户端不要使用
  AD_LIVE_SERVER_FOLLOW = 571; // 直播的关注事件，客户端不要使用
  AD_FANS_TOP_FOLLOW = 572; // 粉条服务端的关注事件包含作品和直播，客户端不要使用


  //联盟相关
  AD_WIN_NOTICE_UNION = 600; // 联盟竞价成功后，上报actionType
  AD_SPREAD_CACHE_QUERY = 601; // 开屏缓存查询事件
  EVENT_PAY_UNION = 602; // 联盟ROI变现使用

  //金牛相关620~629
  AD_DELIVERY_NO_CHARGE = 620; //金牛聚合落地页,引流广告不计费

  // 浮窗页相关
  AD_PLAYABLE_PAGE_IMPRESSION = 630;  // 浮窗曝光，进入浮窗页面时上报
  AD_PLAYABLE_PAGE_INCLICK = 631;     // 浮窗内点击
  AD_PLAYABLE_PAGE_OUTCLICK = 632;    // 浮窗外点击
  AD_PLAYABLE_PAGE_CLOSED = 633;      // 浮窗关闭

  // 预约表单
  EVENT_APPOINT_FORM = 634; // 预约表单
  EVENT_APPOINT_JUMP_CLICK = 635; // 预约跳转点击

  // 浮窗页20200927新加
  AD_PLAYABLE_PAGE_PLAYED_END = 636; // 浮窗，惊喜视频播放完成

  //分析型字段
  AD_ABANDON = 650; // 广告丢弃,同时上报丢弃场景DiscardScene
  AD_APPSTORE_IOS_IMPRESSION = 651; // IOS APPSTORE应用商店详情页展现

  AD_APPSTORE_DEEPLINK_EVOCATION_FAILED = 652 ; // APPStore deeplink唤起失败
  AD_CONFIRM_DOWNLOAD_BOX_IMPRESSION = 653 ; // 确认下载框曝光
  AD_CONFIRM_DOWNLOAD_BOX_CANCELLED = 654 ; // 确认下载框取消
  AD_VPN_STARTED = 655 ; // 反厂商拦截VPN开启
  AD_SPLASH_ANDROID_RETURN_CLICK = 656 ; // 开屏5s阶段，安卓返回键点击

  AD_GUIDE_PAGE_IMPRESSION = 700 ; //引导场景页面的曝光

  AD_RERANK_STOCK_NOT_ENOUGH = 710; //用户命中频次ad_load or 时长ad_load策略需要展现广告,但服务端并未下发广告时上报，同时上报参数client_params.exposure_reason
  AD_LANDING_PAGE_IMPRESSION = 711; // 前置落地页展现X%时即上报，目前X=90

  AD_LOAD_PHOTO_FAILED = 712; //国庆节活动激励广告视频,接收广告下发后上报
  AD_ITEM_DOWNLOAD_CARD_CLOSED = 713; // 下载卡片关闭
  AD_GOLD_BUTTON_CLICK = 714; // 点击弹窗中金币icon，弹框消失，视频继续播放

  EVENT_ADD_WECHAT = 715;// 微信复制
  EVENT_MULTI_CONVERSION = 716; // opcx优化目标：多转化事件
  EVENT_AD_WATCH_TIMES = 717; //ocpx优化目标：广告观看次数

  AD_SOCIAL_LIVE_CONVERSION_CLOSED = 718; // 快直播转化任务关闭
  AD_SOCIAL_LIVE_ELEMENT_CLICK = 719; // 快直播转化任务中元素点击

  AD_IS_INNER_DELIVERY = 720;   // 粉条内部订单

  AD_TRY_PLAY_GAME_PRELOAD = 721; //试玩页面开始加载
  AD_TRY_PLAY_GAME_LOADED = 722; // 试玩页面加载完成
  AD_TRY_PLAY_GAME_START = 723; //开始试玩
  AD_TRY_PLAY_GAME_END = 724; //完成试玩

  AD_DETAIL_PAGE_IMPRESSION = 725; // 详情页曝光
  AD_APPSTORE_IOS_CLOSED = 726; // AppStore关闭
  AD_ITEM_NEGATIVE_CANCEL = 727; // 负反馈，取消按钮点击

  AD_LIVE_COMMENT = 728; //直播粉条-直播间评论事件action，用户点击发送评论时触发上报
  AD_ACTIONBAR_IMPRESSION = 729;  // actionbar的曝光事件
  AD_DATA_VERIFICATION_SUCCESS = 730; // 激励视频数据校验成功

  EVENT_AD_WATCH_5_TIMES = 731; // 广告观看，累计到5次
  EVENT_AD_WATCH_10_TIMES = 732; // 广告观看，累计到10次
  EVENT_AD_WATCH_20_TIMES = 733; // 广告观看，累计到20次

  AD_FANSTOP_PHOTO_SHOW = 734; // 粉条作品曝光
  AD_FANSTOP_MERCHANT_SHOW = 735; //粉条商品曝光

  AD_DATA_RECEIVED_SUCCESS = 736; //客户端成功接收引擎端下发的广告时上报

  EVENT_WATCH_APP_AD = 737; //广告观看事件，当前仅在dsp平台的追踪工具联调目标中使用

  AD_BANNERCARD_IMPRESSION = 738; // 激励视频bannercard曝光

  EVENT_7_DAY_PAY_TIMES = 739; // 七日付费次数

  AD_LIVE_AUDIENCE = 740;  // 新增直播间进人 ocpc

  AD_PROFILE_TO_LIVE = 741; // 点击头像跳转个人主页再跳转正式直播间 https://docs.corp.kuaishou.com/d/home/<USER>

  AD_STANDARD_LIVE_PLAYED_STARTED = 742; // 进入标准直播间开始直播, 目前非客户端上报，仅作为模型数据流使用（使用AD_LIVE_PLAYED_STARTED和log proto中的live_room_pattern判断）
  //正负反馈
  AD_PHOTO_SEE = 743; //点击为何看到此广告按钮
  AD_PHOTO_DISLIKE_IMPRESSION = 744; // 不感兴趣页面曝光

  AD_PHOTO_PLAYED_RATE = 745;//75%进度播放数

  AD_PHOTO_REPORT_IMPRESSION = 746;//举报页面曝光

  AD_PHOTO_SEE_IMPRESSION = 747;//为何看到此广告页面	曝光

  AD_PHOTO_PUT_IMPRESSION = 748;//我也相投广告页面 曝光

  AD_PHOTO_PUT = 749;//点击想投广告按钮	

  AD_PHOTO_PUT_SUBMIT = 750;//点击0元开通按钮		

  AD_PHOTO_INTEREST = 751;// 广告兴趣管理-仅点击按钮

  AD_LIVE_UNFOLLOW = 752;// 直播取消关注
  AD_LIVE_BLOCK = 753;// 直播加入黑名单

  AD_LANDING_PAGE_SHARE_CLICK = 754; // 落地页分享按钮点击
  AD_LANDING_PAGE_SHARE_SUCCESS = 755; // 落地页分享成功

  AD_REQUEST_SKA_DATA_FAILED = 756; //请求SKA失败

  AD_BUTTON_IMPRESSION = 757; // 图标曝光
  AD_BUTTON_CLICK = 758; // 图标点击

  AD_COUNT_DOWN_END = 759; //代表激励任务完成，包括一阶段任务，二阶段任务（激活APP、下单）

  AD_FANS_TOP_PLAY = 760; //粉条播放事件

  EVENT_PHONE_GET_THROUGH = 761; // 营销链路中，电话建联成功，区别于智能电话拨通；eg.招商加盟、家居建材、生活服务、教育
  EVENT_INTENTION_CONFIRMED = 762; // 营销链路中，意向确认；eg.招商加盟、家居建材、生活服务、教育
  EVENT_WECHAT_CONNECTED = 763; // 营销链路中，微信加粉；eg.招商加盟、家居建材、生活服务、教育
  EVENT_ORDER_SUCCESSED = 764; // 营销链路中，成交；eg.招商加盟、家居建材、IT消电
  RESERVATION_BUTTON_CLICK = 765; //预约组建点击
  AD_BUTTON_CLICK_DOWNLOAD = 766; //下载图标点击
  AD_BUTTON_CLICK_CONSULT = 767; //咨询图标点击

  AD_FORCE_ACTIVE = 768; //强制激活
  AD_BUTTON_IMPRESSION_DOWNLOAD = 769; //下载图标曝光
  AD_BUTTON_IMPRESSION_CONSULT = 770; //咨询图标曝光

  AD_JUMP_TO_BROWSER_SUCCESS = 771;//尝试跳转浏览器成功时上报
  AD_JUMP_TO_BROWSER_FAILED = 772;//尝试跳转浏览器失败时上报

  EVENT_KEY_INAPP_ACTION = 773; // 关键行为 优化目标
  AD_SEVEN_DAY_ROAS = 774; // 七日ROI 优化目标

  AD_BUTTON_CLICK_DEEPLINK_INVOKED = 775; //点击半屏模板中的跳转小程序按钮时上报

  AD_HALF_PAGE_BANNER_IMPRESSION = 776; //半屏直播导流条曝光  https://docs.corp.kuaishou.com/d/home/<USER>

  EVENT_PHONE_CARD_ACTIVATE = 777; //IT消电行业，电话卡激活 超深度优化目标
  EVENT_MEASUREMENT_HOUSE = 778; //针对客户三亚菲林、业之峰，新增用户量房事件 超深度优化目标

  // 服务号后链路行为
  CLUE_LIST_PV = 779; // 商品列表页-咨询按钮曝光
  CLUE_LIST_CLICK = 780; // 商品列表页-咨询按钮点击
  GOODS_DETAIL_PV = 781; // 商品详情页-咨询按钮曝光
  GOODS_DETAIL_CLICK = 782; // 商品详情页-咨询按钮点击
  CLUE_PV = 783; // 免费咨询页-提交线索按钮曝光
  CLUE_CLICK = 784; // 免费咨询页-提交线索按钮点击
  CLUE_SUCCESS = 785; // 线索提交成功 (h5埋点)
  LEADS_SUBMIT = 786; // 线索提交成功（后端落库）
  DOWNLOAD_PV = 787; // 应用下载弹窗页曝光
  DOWNLOAD_CLICK = 788; // 应用下载弹窗页-下载按钮点击


  AD_ITEM_CLICK_DOWNLOAD   = 789; //聚星链接点击
  AD_ITEM_CLICK_CLUE       = 790; //咨询点击
  AD_ITEM_CLICK_TROLLEY    = 791; //小黄车点击
  AD_ITEM_CLICK_PROGRAM    = 792; //小程序点击
  AD_ITEM_CLICK_POI        = 793; //位置点击
  AD_ITEM_CLICK_GAME       = 794; //游戏点击
  AD_ITEM_CLICK_LIVE_RESERVATION       = 795; //直播预约点击

  AD_COVER_LIKE = 796; // 封面点赞
  AD_COVER_CANCEL_LIKE = 797; // 封面取消点赞

  //20210618 add
  AD_LIVE_PLAYED_30S = 798; //直播播放30S

  AD_FANS_TOP_LIVE_SHOW = 799;
  AD_FANS_TOP_ROI = 800;

  EVENT_ORDER_PAYED_INDIRECT = 801; //间接链路（点击）带来的成交
  EVENT_ORDER_PAYED_SERVER_FOLLOW = 802; //涨粉链路带来的成交
  AD_TRUE_DEEPLINK_EVOCATION_SUCCESS = 803; //deeplink真实唤起成功

  //20210708新增
  AD_AWARD_SUCCESS = 804; // 成功领取积分/金币
  AD_AWARD_FAIL = 805; //未成功领取积分/金币
  //20210730 add
  AD_LOSE_NOTICE_UNION = 806; //联盟竞价失败
  //20210802新增
  AD_AUDIENCE_FAST = 807;  //粉条CPC极速进人

  AD_WEAK_ACTIONBAR_IMPRESSION = 808; //弱样式actionbar成功曝光时上报

  AD_IMPRESSION_FAIL_UNION = 809; //联盟返回广告但未成功曝光

  //20210825 add
  AD_PURCHASE_CONVERSION = 810; //激活付费优化目标

  // 兼容AdCallbackLog::EventType类型
  EVENT_CONVERSION = 900; // 激活
  EVENT_PRODUCT_BUY_CLICK = 901; // 立即购买按钮点击,品牌模型使用
  EVENT_ORDER_SUBMIT_CLICK = 902; // 订单提交按钮点击，品牌模型使用
  AD_LIVE_PLAYED_SECIOND_TOTAL=903; // (已废弃，迁移到905）用户进入简易/正式直播间到离开一次消费时长

  //20210909 add, 投放平台新增优化目标
  EVENT_AUDITION = 904; //首次试听到课

  AD_LIVE_PLAYED_SECONDS_TOTAL = 905; // 用户进入简易/正式直播间到离开一次消费时长ElementType

  AD_LIVE_EFFECTIVE_PLAY = 906; // ESP 增加转化目标短视频推广-有效播放
  //20210916 聚星新增
  COME_BACK_MAIN_MEDIA = 907; //返回快手app
  //20211025 add 激励电商
  AD_GOODS_VIDEO_PLAYED_STARTED = 908; // 商品视频素材开始播放
  AD_GOODS_VIDEO_PLAYED_PAUSE = 909; // 商品视频素材暂停播放
  AD_GOODS_VIDEO_PLAYED_END = 910;// 商品视频素材播放完成
  //20211027 add 中间页
  AD_MIDDLE_LANDING_PAGE_LOADED = 911; //中间页进入广告H5页面时上报，本次用于智能下载中间页
  AD_MIDDLE_LANDING_PAGE_IMPRESSION = 912; //中间页曝光
  AD_MIDDLE_LANDING_PAGE_CLOSED = 913; // 中间页的关闭
  //20211101 add
  AD_SENSOR_FAILED = 914 ;// 传感器失败打点，目前包括摇一摇和扭一扭传感器
  EVENT_24H_STAY  = 915 ;// 激活后24小时次日留存
  //20211125 cyl_add
  AD_BLACKLIST_URL_BLOCK = 916 ;//黑名单url发生拦截时打点，一般是在loading页的时候发生黑名单拦截的情况
  //20220104 add 磁力金牛
  AD_MERCHANT_FOLLOW_ROI = 917;// 涨粉roi
  //20220127 cyl_add,收藏
  AD_PHOTO_FAVORITES = 918 ;//点击收藏
  AD_PHOTO_CANCEL_FAVORITES = 919 ;//点击取消收藏
  //20220217 长视频
  AD_LONG_BANNER_SHOW = 920 ;//长视频组件曝光
  // 20220310 cyl_add负反馈
  AD_NEGATIVE_REVOKE = 921 ;//负反馈，撤销不感兴趣点击。是已经点击不感兴趣之后的撤销
  //20220328 cyl_add 联盟相关
  AD_UNION_CONVERSION_BAR_CLICK = 922; //联盟转化点击。主要用于联盟跳转主站二次点击的转化
  AD_SCREEN_SWITCH = 923 ; //横竖屏切换
  AD_PHOTO_PLAYED_10S = 924 ;//已播放10s（第一遍）
  AD_COUPON_RECEIVED = 925 ; //优惠券领取
  EVENT_PRIVATE_MESSAGE_SENT = 926; //快手号营销-私信咨询-发送私信消息
  AD_MERCHANT_FOLLOW_QUALITY = 927; //  涨粉质量优先
  //20220811 cyl_add
  AD_FALL_ELEMENT_IMPRESSION = 928 ;//飘落彩蛋曝光，目前用于开屏
  //20220817 cyl_add 激励互动卡片
  AD_AWARD_INITIALIZE_IMPRESSION = 929 ;// 激励互动卡片初始样式曝光
  AD_AWARD_CARD_IMPRESSION = 930 ; //激励互动卡片卡片曝光
  AD_AWARD_CARD_END_IMPRESSION = 931 ;//激励互动卡片最终样式曝光
  AD_COMPONENT_IMPRESSION = 932 ;//组件曝光
  AD_LIVE_AUDIENCE_QUALITY = 933; // 直播进人质量优化目标
  //20220901 cyl_add
  AD_ITEM_SLIDE = 934 ;//表示用户从当前广告，划走到下一个广告，或划走到非联盟广告区域。这个重复上报，指标计算的时候不去重
  AD_DEEPLINK_EVOCATION_WEB_FAIL = 935 ;//跳转失败.广告主链接换端
  AD_COMPONENT_CLICK = 936 ;//组件点击
  EVENT_ENTER_MINI_PROGRAM = 937 [deprecated = true]; // 进入微信小程序优化目标，由于没有体现roi，现阶段废弃，改用MINI_APP_ROAS
  //20221117 cyl_add 搜索有效曝光
  AD_RATE_IMPRESSION = 938 ;//曝光比例，搜索有效曝光控制为100%曝光。
  AD_LIVE_AUDIENCE_FAST = 939 ;// 新增快速进人目标
  AD_SIDE_SLIDE_WINDOW_CLOSE = 940 ;////侧滑小窗退出
  AD_MERCHANT_FOLLOW_FAST = 941; // ESP-诉求版-进人速度最优
  AD_EVOCATION_WEB_RECEIVE = 942; //端内跳转信息接收监听，新增
  MINI_APP_ROAS = 943 ;// 小程序ROI的优化目标
  AD_STOREWIDE_ROAS = 944 ;// 磁力金牛全店 ROI
  AD_PHOTO_CO_CREATOR_IMPRESSION = 945;// 共创者头像曝光。目前这三个聚星在使用
  AD_PROFILE_PAGE_IMPRESSION  = 946;//个人主页曝光
  AD_PROFILE_PAGE_CLOSED  = 947 ; //个人主页关闭
  AD_EFFECTIVE_CUSTOMER_ACQUISITION = 948;//有效客户获取，优化目标
  AD_ITEM_POSITIVE = 949;//正反馈
  AD_PROFILE_TAG_IMPRESSION = 950; //p页标签曝光，品牌
  AD_COLLECTION_LANDING_PAGE_ITEM_IMPRESSION = 951; //合集页素材曝光
  AD_SERVICE_TAB_IMPRESSION = 952; //服务tab曝光
  AD_FANSTOP_PUSH = 953; // 粉条push相关优化目标
  AD_SERVICE_MODULE_IMPRESSION = 954 ;// 服务tab下首个模块曝光
  AD_ITEM_NEGATIVE_SHORT_LINK = 955 ;//短链路负反馈点击，和AD_ITEM_NEGATIVE功能一致，只是负反馈链路缩短
  AD_ITEM_DISLIKE_IMPRESSION_SHORT_LINK = 956 ;//短链路负反馈不感兴趣页面曝光，和AD_ITEM_DISLIKE_IMPRESSION功能一致
  AD_ITEM_NEGATIVE_2ND_PAGE_SHORT_LINK = 957 ;//短链路负反馈二级页面点击，和AD_ITEM_NEGATIVE的二级页面一致
  AD_ON_FOREGROUND = 958 ;//从后台切换回快手广告
  AD_ON_BACKGROUND = 959 ;//从广告切换至后台
  AD_GET_NEO_GOLD_SUCCESS = 960 ;//激励金币金领取成功
  AD_LANDING_PAGE_LCP_IMPRESSION = 961 ;//落地页最大元素曝光，web的largest contentful paint
  AD_CID_ROAS = 962 ; // cid roi 出价
  AD_POI_CLICK = 963 ; // 本地推 poi 点击
  AD_GOODS_CLICK = 964 ; // 本地推 团购 点击
  AD_PHOTO_CO_CREATOR_CLICK = 965 ;//聚星共创模式下，点击共创头像。业务：聚星
  EVENT_RETENTION_DAYS = 966; //每日留存，https://team.corp.kuaishou.com/task/T4079073
  AD_ITEM_DOWNLOAD_DELETE_APPS_FOR_MEMORY = 967 ;// 下载内存不足，点击删除其他app
  AD_PAGE_LEAVE = 968;//广告页面离开，不包括上下滑AD_DETAIL_PAGE_CLOSED场景，比如tab切换，头像点击等
  //20231114 add 短剧相关事件
  AD_COLLECTION_LANDING_PAGE_ITEM_CLOSE = 969 ;//合集页素材关闭，短剧的合集页的单素材详情页关闭的时候上报
  AD_COLLECTION_LANDING_PAGE_IMPRESSION = 970 ;//合集页面板曝光，短剧的合集面板出现的时候上报
  AD_COLLECTION_LANDING_PAGE_CLICK  = 971 ;//合集页面板点击，短剧合集面板，点击某一集
  AD_COLLECTION_FAVORITES = 972  ;//合集页点击收藏，收藏的是整个合集
  AD_COLLECTION_CANCEL_FAVORITES = 973 ;//合集页点击取消收藏
  AD_COLLECTION_SHARE = 974 ;//合集页分享
  AD_PURCHASE_PAGE_IMPRESSION = 975 ;//付费面板曝光，短剧付费面板
  AD_PURCHASE_PAGE_CLICK = 976 ;//付费面板套餐点击，每次点击都重复上报
  AD_PURCHASE_PAGE_ACTIONBAR_CLICK = 977 ;//付费面板支付点击，付费面板的actionbar点击的时候上报
  AD_IAA_ROAS = 978;  // 首日变现ROI（iaa专用）
  //20240418 add 私信-表单挂载私信组件的相关事件
  AD_IM_COMPONENT_IMPRESSION = 979;   // 私信组件曝光
  AD_IM_COMPONENT_CLICK = 980;    // 私信组件点击
  AD_ITEM_SHARE_CLICK = 981; //点击快小游新样式分享按钮上报
  EVENT_FORM_SUBMIT_INDIRECT = 982; // 间接链路（提交）带来的成交，商业化归因使用，无实际对应的客户端埋点
  AD_IM_COMPONENT_BUBBLE_IMPRESSION = 983; // 表单挂载私信组件气泡曝光
  AD_IM_COMPONENT_BUBBLE_CLICK = 984; // 表单挂载私信组件气泡点击
  AD_ROAS_IAAP = 985;  // 混变 roi
  EVENT_PHONE_CALL = 986; // 400电话拨打
  AD_MIDPAGE_ITEM_SHOW = 987;// 线索中间页组件曝光
  AD_MIDPAGE_ITEM_CLICK = 988;// 线索中间页组件点击
  AD_FEED_PHOTO_SUCCESS_UNLOCK = 989;// 观看信息流视频成功解锁
  AD_SERIAL_IAA_ROAS = 990;  // 首日变现 ROI（短剧 IAA 专用）
  //20241025
  AD_PHOTO_PLAYED_MID = 991; // 视频播放中点
  AD_PHOTO_LONG_PRESS_SPEED= 992; //联盟-长按广告视频加速
  AD_PHOTO_INTERRUPT_SPEED =993; //联盟-中断视频加速
  //20241104
  AD_POI_PAGE_SHOW = 994; // POI页页面曝光
  AD_POI_CLUE_BUTTON_SHOW = 995; // POI页线索组件曝光
  AD_POI_CLUE_BUTTON_CLICK = 996; // POI页线索组件点击
  AD_POI_CONSULT_BUTTON_SHOW = 997; // POI页咨询组件曝光
  AD_POI_CONSULT_BUTTON_CLICK = 998; // POI页咨询组件点击
  AD_POI_PURCHASE_GROUP_PAGE_SHOW = 999; // 团购详情页页面曝光
  AD_POI_PURCHASE_GROUP_CLUE_BUTTON_SHOW = 1000; // 团购详情页线索组件曝光
  AD_POI_PURCHASE_GROUP_CLUE_BUTTON_CLICK = 1001; // 团购详情页线索组件点击
  AD_POI_PURCHASE_GROUP_CONSULT_BUTTON_SHOW = 1002; // 团购详情页咨询组件曝光
  AD_POI_PURCHASE_GROUP_CONSULT_BUTTON_CLICK = 1003; // 团购详情页咨询组件点击
  AD_POI_CLUE_FORM_RESERVE_SHOW = 1004; // 线索表单预约曝光
  AD_POI_CLUE_IM_MERCHANT_CONSULT_SHOW = 1005; // 线索咨询私信商家曝光
  AD_POI_CLUE_IM_MERCHANT_CONSULT_CLICK = 1006; // 线索咨询私信商家曝光
  // 20241208
  AD_MI_SDK_EVOKE_APPSTORE_SUCCESS = 1007; // ADX-米盟sdk调起应用商店落地页成功事件
  AD_MI_SDK_EVOKE_APP_SUCCESS = 1008; // ADX-米盟sdk拉活成功事件
  // 20250207 add 磁力金牛cid相关转化目标
  CID_ROAS = 1009; // 引流电商订单提交roi
  CID_EVENT_ORDER_PAID = 1010; // 引流电商订单提交
  AD_IAA_7DAY_ROAS = 1011; // 7日变现ROI（iaa专用）
  AD_SEVEN_DAY_ROAS_IAAP = 1012; // 7日混变ROI（iaap专用）
  AD_UNION_PRELOAD_START = 1013; //联盟素材开始预加载
  AD_ZAPIS_ITEM_END_PREDOWNLOAD =1014; //快小游预加载结束
  AD_ZAPIS_ITEM_START_PREDOWNLOAD=1015; //快小游预加载开始
  // 20250507新增硬广滚动评论区组件
  AD_SCROLL_AREA_IMPRESSION=1016; // 滚动评论区组件曝光
  // 20250508美团聚合页商品浏览行为
  AD_ITEM_GOODS_VIEW = 1017; //美团聚合页商品浏览行为
  // 20250613 新增开屏游戏样式
  AD_PICTURE_SAVING = 1018; //图片或者壁纸保存成功
  AD_SAVING_PERMISSION_REFUSED=1019;//拒绝授权相册权限
  //20250804 zhoubo12 add
  AD_UNION_CLOSURE_LOGIN_IMPRESSION = 1020;//联盟闭环登录页曝光
  AD_UNION_CLOSURE_LOGIN_CANCEL = 1021;//联盟闭环取消登录
  AD_UNION_CLOSURE_LOGIN_FAIL = 1022;//联盟闭环登录失败
  AD_UNION_CLOSURE_LOGIN_SUCCESS =1023;//联盟闭环登录成功
  AD_UNION_LOGIN_AUTHORIZATION = 1024;//联盟登录授权
  // 20250818
  AD_DRAW_CREDIT_LINE_ROAS = 1025; //优化目标，当日用信ROI
  // 20250902
  AD_OPEN_APP_WINDOW_IMPRESSION = 1026; // 已安装APP启动弹窗曝光
}

enum NewDetailPageTabClickType {
  UNKNOWN_NEW_DETAIL_PAGE_TAB_CLICK_TYPE = 0;
  COMMENT_TAB_CLICK = 1; // 评论tab点击时上报
  DETAIL_TAB_CLICK = 2; // 详情tab点击时上报
}

enum LandingPageEntrySource {
  UNKNOWN_LANDING_PAGE_ENTRY_SOURCE = 0;
  FROM_OLD_DETAIL_PAGE = 1; // 老视频播放页
  FROM_NEW_DETAIL_PAGE = 2; // 新详情页
  FROM_FLOATING_LANDING_PAGE = 3; // 浮层落地页
}

enum LandingPageType {
  UNKNOWN_LANDING_PAGE_TYPE = 0;
  NORMAL_LANDING_PAGE = 1; // 普通落地页
  NEW_DETAIL_LANDING_PAGE = 2; // webview内嵌落地页
  FLOATING_LANDING_PAGE = 3; // 浮层落地页
  INTERACT_LANDING_PAGE = 4; // 互动落地页
  AUTHORITY_LANDING_PAGE=5; // 权限信息落地页
}

enum DownloadSource {
  UNKNOWN_DOWNLOAD_SOURCE = 0;
  FROM_LANDING_PAGE = 1; // 落地页下载
  FROM_DIRECT_INSTALLATION = 2; // 直接下载
  COMPANY_APP_STORE_INSTALL = 3; //厂商商店下载
  FROM_BELL_CARD = 4; //小铃铛提示卡片下载
  ENTER_LIVE_ROOM_POPUP=5;//弹窗actionbar下载
}

enum IsPackageChanged {
  UNKNOWN_IS_PACKAGE_CHANGED = 0;
  CHANGED = 1; // 被换包
  NOT_CHANGED = 2; // 未换包
}

enum ItemClickType {
    UNKNOWN_ITEM_CLICK_TYPE = 0;
    ACTION_BAR_CLICK = 1; // 蓝条点击
    PLAY_END_CLICK = 2; // 重播处点击
    CAPTION_CLICK = 3; // caption中“查看详情”链接点击
    CAPTION_ITEM_CLICK = 4; // 电商caption首位链接
    PROFILE_ITEM_CLICK = 5; // profile页点击
    BRAND_PROMOTION_ITEM_CLICK = 6; // caption首位(caption上方)品牌推广链接点击
    APP_DOWNLOAD_ITEM_CLICK = 7; // caption首位(caption上方)应用下载链接点击
    COMMENT_ITEM_CLICK = 8; // 评论区转化链接点击
    BOTTOM_ICON_CLICK = 9; // 详情页右下角转化链接icon点击
    MERCHANT_SHOWCASE_CLICK = 10; //  商品橱窗卡片商品点击
    DYNAMIC_CARD_CLICK = 11; // 动态卡片点击
    LEFT_SLIDE_CLICK = 12; //左滑视频跳转
    PORTRAIT_CLICK = 13; //点击头像跳转
    COMMENT_CLICK = 14; //评论区点击跳转
    PLAY_END_ICON_CLICK = 15; // 播放结束页点击icon跳转落地页
    PLAY_END_ICON_TITLE_CLICK = 16; // 播放结束页点击icon标题跳转落地页
    PLAY_END_ICON_DESCRIPTION_CLICK = 17; // 播放结束页点击icon描述跳转落地页
    FEED_COVER_ACTION_BAR = 18; // 信息流封面页的action bar
    ECOM_CARD_CLICK = 19; // 直营电商卡片视频区点击
    ECOM_SHOPPING_TROLLEY_CLICK = 20; // 直营电商购物车视频区点击
    ECOM_CARD_COMMENT_AREA_CLICK = 21; // 直营电商卡片评论区点击
    ECOM_DISCOUNT_COMMENT_AREA_CLICK = 22; // 直营电商评论区折扣点击

    // 极速版广告播放页&播放结束页埋点需求
    COMMENT_PHOTO_CLICK = 23; // 用户成功点击描述区作者头像，头像在右侧
    COMMENT_NAME_CLICK = 24; // 用户成功点击描述区作者名称
    COMMENT_DETAIL_CLICK = 25; // 用户成功点击描述区详细描述触发转化时跳转；当场景为用户点击详情展示完整信息时，不触发此事件，只有跳转时才进行跳转；
    PLAYEND_ACTION_BAR_CLICK = 26; // 成功点击播放结束页查看详情
    PLAYEND_APP_SCORE_CLICK = 27; // 成功点击播放结束页app评分
    PLAYEND_ACTION_BAR_SUSPENSION = 28; // 用户成功点击悬浮框下载按钮，android 下载类型

    // 通用卡片埋点需求
    CARD_ACTION_BAR_CLICK = 29; // 用户成功点击卡片上的actionbar跳转到落地页时即可触发
    CARD_ICON_CLICK = 30; // 用户成功点击卡片里的app头像跳转到落地页时即可触发
    CARD_ICON_NAME_CLICK = 31; // 用户成功点击卡片里的app名称跳转到落地页时即可触发
    CARD_ICON_COMMENT_CLICK = 32; // 用户成功点击卡片里的描述信息跳转到落地页时即可触发

    // 评论区Action Bar点击
    COMMENTS_TOP_ACTIONBAR_CLICK = 33; // 用户成功点击首屏评论区转化条触发上报
    COMMENTS_HANG_ACTIONBAR_CLICK = 34; // 用户成功点击评论区非首悬浮转化条触发上报

    BLANK_AREA_CLICK = 35; // 任意背景位点击

    TRY_PLAY_GAME_CLICK = 36; // 试玩页面的行为点击

    PLAY_END_CLICK1 = 37; // 联盟播放结束页双button--左侧点击按钮
    PLAY_END_CLICK2 = 38; // 联盟播放结束页双button-右侧点击按钮
    PLAY_END_CALL_CLICK = 39; // 联盟播放结束页点击号召按钮
    PLAY_DETAIL_CALL_CLICK = 40; // 联盟播放详情页击号召按钮

    PENDANT_ICON_CLICK = 41; // 直播挂件点击挂件icon
    PENDANT_BUTTON_CLICK = 42; // 直播挂件点击按钮
    PENDANT_OTHER_AREA_CLICK = 43; // 直播挂件点击其他区域

    PLAYEND_APP_DOWNLOAD_NUM = 44; // 播放结束页下载次数元素
    // 实际也是通用卡片埋点需求
    CARD_APP_SCORE_CLICK = 45; // 通用卡片评分元素
    CARD_APP_DOWNLOAD_NUM = 46; // 通用卡片下载次数元素

    // 增加媒体形态
    COMMENT_AREA_NAME_CLICK = 47; // 评论命名元素
    COMMENT_AREA_DETAIL_CLICK = 48; // 评论描述元素

    // 评论区描述
    COMMENT_AREA_ICON_CLICK = 49; // 评论头像因素

    // 点击中间页actionbar
    LANDING_PAGE_ACTION_BAR_CLICK = 50; // 点击中间页actionbar

    AD_WEAK_PATCH_AD_CLICK = 51 ; // 弱样式广告描述条点击

    CARD_ICON_TESTBOX_CLICK = 52 ; // 卡片文本框点击时上报
    CARD_ICON_OTHERAREA_CLICK = 53 ; // 卡片其他区域点击时上报
    CARD_ICON_MOBILE_CLICK = 54 ; // 点击手机号码输入框时触发

    PHOTO_PORTRAIT_CLICK = 55; // 点击视频详情页头像进入直播间时上报
    POPUP_WINDOW_CLICK = 56 ;  // 弹窗点击

    // Android（不含下载类）&iOS - 【双feed】多利益点卡片组件埋点
    CARD_PICTURE_CLICK_ONE = 57 ; // 样式1
    CARD_PICTURE_CLICK_TWO = 58 ; // 样式2
    CARD_PICTURE_CLICK_THREE = 59 ; // 样式3
    CARD_PICTURE_TITLE_CLICK_ONE = 60 ;
    CARD_PICTURE_TITLE_CLICK_TWO = 61 ;
    CARD_PICTURE_TITLE_CLICK_THREE = 62 ;
    CARD_PICTURE_SUBTITLE_CLICK_ONE = 63 ;
    CARD_PICTURE_SUBTITLE_CLICK_TWO = 64 ;
    CARD_PICTURE_SUBTITLE_CLICK_THREE = 65 ;

    RED_PACKET_AREACLICK = 66 ; // 红包区域元素
    PENDANT_NAME_CLICK = 67 ;  // 直播挂件点击名称描述
    BRAND_WALK_CLICK = 68 ; //品牌去逛逛点击
    BRAND_MORE_CLICK = 69 ; //品牌发现更多点击
    UP_SLIDE_FRONT_LANDING_PAGE_CLICK = 70; //上滑跳转前置落地页
    CARD_ICON_CLOSE_CLICK = 71 [deprecated=true];  //通用卡片关闭(废弃)
    ANY_AREA_CLICK = 72 ; // 播放页任意位置
    GUIDE_ICON_CLICK = 73 ; // 引导页侧边栏点击（任意位置），进入落地页
    GUIDE_LEFT_SLIDE_CLICK = 74 ; //引导页面场景左滑，进入落地页
    CARD_RANK_CLICK = 75 ; // 用户成功点击卡片上的评分，跳转到落地页时即可触发
    CARD_DOWNLOAD_CNT_CLICK = 76 ; // 用户成功点击卡片里的下载次数，跳转到落地页时即可触发
    CARD_TAG_CLICK =  77 ; // 用户成功点击卡片里的二级行业，跳转到落地页时即可触发
    CARD_LABEL_CLICK = 78 ; // 用户成功点击卡片里的标签跳转到落地页时即可触发

    ITEM_LABEL_CLICK = 79 ; // 视频播放页，广告标签点击
    ITEM_BULLET_CLICK = 80; // 弹幕点击；安卓：点击转化弹幕后跳转中间页；ios：跳转应用商店

    CONFIRM_DOWNLOAD_BOX_CONFIRMED = 81; // 弹出下载弹窗，点击确认时上报

    PHOTO_COMMENT_NAME_CLICK = 82; //封面作者名称点击
    PHOTO_ACTION_BAR_CLICK = 83; // 封面号召按钮点击

    CARD_RECO_CLICK = 84; //用户点击推荐理由
    CARD_PAGE_CLICK = 85; // 卡片弹窗点击
    PLAYEND_CARD_CLICK = 86; // 播放结束页卡片点击
    PLAYEND_PAGE_CLICK = 87; // 播放结束页点击
    PLAYEND_ICON_RECO_CLICK = 88; // 点击播放结束页推荐理由

    COMMON_CARD_CLICK = 89; // 通用卡片点击

    COMMENTS_HANG_NAME_CLICK = 90; // 用户成功点击评论区，非首(第一位)悬浮广告，命名元素点击时上报
    COMMENTS_HANG_DETAIL_CLICK = 91; // 用户成功点击评论区，非首(第一位)悬浮广告，描述元素点击时上报

    TRY_PLAYING_BUTTON_CLICK = 92; // 试玩过程转化button点击；
    TRY_PLAYED_BUTTON_CLICK = 93; // 试玩结束转化button点击；
    TRY_PLAYING_ANY_AREA_CLICK = 94; // 试玩过程任意区域转化点击
    TRY_PLAYED_ANY_AREA_CLICK = 95; // 试玩结束任意区域转化点击；

    SHARE_BUTTON_CLICK = 96; //点击分享跳转
    COMMENT_BUTTON_CLICK = 97; //点击评论跳转待定名字
    ALTERNATIVE_PAGE_ACTION_BAR_CLICK = 98; // 备选页ActionBar点击
    PLAYING_LANDINGPAGE_ACTION_BAR_CLICK = 99; // 播放过程落地页的action bar点击
    COMMENT_PICTURE_CLICK = 100; // 评论图片点击

    COMMENT_RECO_CLICK = 101; //点击推荐理由
    LANDING_PAGE_APP_AUTO_DOWNLOAD = 102; // 落地页内成功开始自动下载时上报
    AGG_PAGE_CLICK = 103; // 聚合页点击跳转
    PLAYEND_ICON_OTHERAREA_CLICK = 104; // 播放结束页卡片其他区域点击时上报

    LIVE_RESERVATION_CLICK = 105; //直播预约点击
    //20210112 add
    KWAI_ENTER_INTRODUCE_CLICK = 106; //快手入口引导元素点击，应用于联盟增长类广告

    LANDING_PAGE_TOP_ACTION_BAR_CLICK = 107; //点击落地页内顶部小actbar

    //20210304 add
    PHOTO_BLANK_AREA_CLICK = 108; //封面其他区域点击
    //20210307 add
    AUTO_ENTER_LANDING_PAGE = 109; //自动跳转落地页
    INTERACT_LANDING_PAGE_CLICK = 110; //互动落地页内行为点击
    INSTANT_TRY_WEEK_CLICK = 111;   // “即刻体验”弱样式按钮点击
    INSTANT_TRY_STRONG_CLICK = 112; // "即刻体验”强样式按钮点击
    MISOPERATION_CLICK = 113;   // 强卡右上角误触按钮点击
    SPLASH_SMALL_WINDOW_CLICK = 114; //开屏小窗点击

    //20210325 add
    ACTIONBAR_ICON_CLICK = 115; // 成功点击actbar里的app头像跳转到时即可触发
    ACTIONBAR_ICON_NAME_CLICK = 116; // 用户成功点击actbar里的app名称跳转时即可触发
    ACTIONBAR_ICON_COMMENT_CLICK = 117; // 用户成功点击actbar里的描述信息跳转时即可触发
    ACTIONBAR_ICON_OTHER_AREA_CLICK = 118 ; // actbar其他空白区域点击时上报

    //20210413 add
    POPUP_WINDOW_ICON_CLICK = 119; //弹窗头像点击
    POPUP_WINDOW_OTHERAREA_CLICK = 120; // 弹窗其他区域点击时上报

    //20210425 add
    PHOTO_PICTURE_CLICK = 121; //封面图片点击
    PHOTO_DETAIL_CLICK = 122; //封面描述点击
    RECO_DETAIL_CLICK = 123; //推荐广告的描述点击
    RECO_COMMENT_NAME_CLICK = 124; //推荐广告的作者名称点击
    RECO_PICTURE_CLICK = 125; //推荐广告的图片点击
    RECO_ACTION_BAR_CLICK = 126; //推荐广告的号召按钮点击

    //20210425
    CLOSE_BULLET_ICON_CLICK = 127;//点击图标
    CLOSE_BULLET_ICON_TITLE_CLICK = 128;//点击名称
    CLOSE_BULLET_ICON_DESCRIPTION_CLICK = 129;//点击广告语
    CLOSE_BULLET_OTHERAREA_CLICK = 130;//点击退出弹框白底区域（包含“观看多少秒。。”的文字区域
    CLOSE_BULLET_ACTION_BAR_CLICK = 131;//点击行为按钮时上报
    //20210510 add
    SPLASH_CONVERSION_BUTTON_CLICK = 132; //开屏转化按钮点击
    SPLASH_SKIPED_5S_CLICK = 133; //开屏跳过倒计时5秒点击
    SPLASH_SMALL_WINDOW_PICTURE_CLICK = 134; //开屏图片小窗点击
    //20210512 add
    POPUP_WINDOW_ICON_NAME_CLICK = 135; //弹窗应用名称点击时上报
    POPUP_WINDOW_ICON_COMMENT_CLICK = 136; // 弹窗应用描述点击时上报
    POPUP_WINDOW_ICON_DETAIL_CLICK = 137; // 弹窗了解详情点击时上报

    //搜索结果页 非action bar
    PORTRAIT_CLICK_SEARCH = 138; //点击搜索结果页作者头像跳转
    NAME_CLICK_SEARCH = 139;//点击搜索结果页作者昵称/名称跳转
    DETAIL_CLICK_SEARCH = 140;//点击搜索结果页广告描述区（除点击“更多”外部分）调整
    OTHER_AREA_CLICK_SEARCH = 141;//点击搜索结果页空白区域跳转

    //搜索结果页 action bar
    CARD_ACTION_BAR_CLICK_SEARCH = 142; // 用户成功点击卡片上的actionbar跳转即可触发
    CARD_ICON_CLICK_SEARCH = 143; // 用户成功点击卡片里的头像跳转时即可触发
    CARD_ICON_NAME_CLICK_SEARCH = 144; // 用户成功点击卡片里的app名称跳转时即可触发
    CARD_ICON_COMMENT_CLICK_SEARCH = 145; // 用户成功点击卡片里的描述信息跳转时即可触发
    CARD_ICON_OTHER_AREA_CLICK_SEARCH = 146; //用户成功点击卡片上其他空白区域点击时上报
    IOS_STOREKIT_AUTO_JUMP = 147; //ios storekit自动跳转
    FRONT_CARD_RENDER_SUCCESS_CLICK = 148; //渲染成功前卡点击
    FRONT_CARD_RENDER_FAIL_CLICK = 149; //渲染失败前卡点击

    CARD_LEFT_ACTION_BAR_CLICK = 150; //点击左边actionBar区域 上报
    // POPLAY 彩蛋弹窗
    POPLAY_PICTURE_CLICK = 151; // POPLAY 彩蛋弹窗，彩蛋图片点击
    POPLAY_WORD_CLICK = 152; // POPLAY 彩蛋弹窗，彩蛋文字点击
    //20210630 add
    FINGER_SWIPE_CLICK = 153; //手动滑动进入广告,用户在开屏广告上朝任意方向滑动都可以进入广告
    //20210714 add
    ONLINE_AUDIENCE_NUM = 154; //在线观众人数点击
    H5_LANDING_PAGE_AUTO = 155; //h5落地页自动跳转
    //20210722 add
    RETAIN_CARD_BUTTON = 156; //激励视频挽留弹窗按钮

    //20210825 ADD
    SHAKE_ICON_SHACK = 157;//用户成功摇一摇进入落地页
    SHAKE_BIG_ICON_CLICK = 158;//用户成功点击摇一摇区域进入落地页
    SHAKE_SMALL_ICON_CLICK = 159;//用户成功点击摇一摇区域进入落地页

    ITEM_CLICK_ACTION_OPEN_POPSHOW = 160; //打开全屏彩蛋播放页 废弃
    //20210908 add  扭一扭相关
    ROTATE_ICON_ROTATE = 161;  //用户扭一扭进入落地页
    ROTATE_BIG_ICON_CLICK = 162;  //用户成功点击扭一扭大挂件区域进入落地页
    ROTATE_SMALL_ICON_CLICK = 163; //用户成功点击扭一扭小挂件区域进入落地页
    //20210915  add 激励视频中小黄车
    TROLLEY_ICON_CLICK = 164; //小黄车的点击
    //20210922 add
    FINGER_SWIPE_CLICK_ACTIONBAR = 165; // 开屏广告滑动，滑动轨迹经过actionbar区域后进入广告，用户无感知
  //20211018 add 挽留弹窗点击
    CLOSE_BULLET_SECOND_STEP_ACTION_BAR_CLICK = 166; //点击继续二阶段按钮上报
  //20211105 add
    MARQUEE_COMMENT_CLICK = 167; //点击跑马灯文案跳转快手小店。
  //20220216 cyl_add。高级创意卡片、表单卡片
     NEXT_CARD_ACTION_BAR_CLICK = 168 ;//用户成功点击卡片上的actionbar跳转到落地页时上报
     NEXT_CARD_OTHERAREA_CLICK=169;  //用用户成功点击卡片里的[非actionbar]跳转到落地页时上报
  //20220217 长视频点击
    LONG_BANNER_CLICK = 170 ;//长视频组件点击
  //20220303 落地页相关
    LANDING_PAGE_OTHER_AREA_CLICK = 171 ;//落地页其他区域点击
  //20220520 cyl_add
    CARD_FOLLOW_NUM = 172 ; //卡片关注人数元素
    FALL_ICON_CLICK = 173 ;// 飘落样式的点击
    BROKEN_FRAME_BIG_ICON_CLICK = 174 ;// 破框区域点击
    LIVE_BUTTON_CLICK=175;// 联盟-直播直投 点击进入直播间标签
    IN_LIVE_BUTTON_CLICK=176; // 联盟-直播直投 点击直播中标签
    DIRECT_H5_PAGE_CLICK = 177; //直出h5落地页点击
    GOODS_ITEM_CARD_CLICK=178;//联盟-直播流多商品卡样式
    GOODS_ITEM_CARD_SLIDE_CLICK =179;// 联盟-直播流多商品卡样式 滑动点击
    ITEM_HOT_BUY_STYLE_CLICK=180;// 联盟-轻热卖样式点击
    GUIDE_ENTER_LIVE_CLICK=181;//联盟-引导进入直播间弹窗
    AUTO_CLICK_OTHER_AREA = 182; // 联盟-其他区域自动点击
    COMMENT_TOP_BAR_CLICK = 183;//评论区顶bar点击
    LIVE_GOOD_LUCK = 184; //  直播好运来
    COMMENT_AREA_OTHER_CLICK = 185; // 评论区第一条其他区域点击
    POPLAY_FALL_IOCN = 186 ; // 飘落物
    ROTATE_SHAKE_BIG_ICON_CLICK = 187;//晃一晃点击
    //20230723 add
    ACTION_BAR_BUTTON_CLICK = 188;//视频详情页底部actionbar的点击，和plc区分开来
    RETAIN_AGG_CARD_CLICK = 189; // 挽留用户激励聚合卡片点击
    PLAYEND_AGG_CARD_CLICK = 190; //播放结束页激励聚合卡片点击
    SURPRISE_BOX_CLICK = 191; //惊喜盒子点击
    BUBBLE_RAIN_CLICK = 192; //气泡雨点击
    //搜索品专
    SEARCH_PHOTO_CLICK = 193 ;//搜索品专素材区视频点击
    SEARCH_LIVE_CLICK = 194 ;//搜索品专素材区直播点击
    NATURE_PHOTO_CLICK = 195;// 搜索品专自然作品点击
    TOP_BANNER_CLICK = 196;//搜索品专顶部banner点击
    BOTTOM_BANNER_CLICK = 197 ;//搜索品专底部banner点击
    SHOP_CARD_CLICK = 198;//搜索品专店铺卡
    LIVE_CARD_ACTION_BAR_CLICK = 199 ;//直播卡片点击
    GRAVITY_SENSOR_ICON_CLICK = 200 ;//重力感应样式点击
    TUBE_BIG_CARD_PHOTO = 201 ;//上下滑短剧大卡视频封面
    TUBE_BIG_CARD_PLAY_BUTTON = 202 ;//上下滑短剧大卡立即观看按钮
    CARD_GRAVITY_SENSOR_ICON_CLICK = 203; //重力感应卡片点击
    LONG_PRESS_SPEED_AREA_CLICK=204; //联盟-长按加速区域点击
    LONG_PRESS_SPEED_COVER_LAYER_CLICK=205; //联盟-长按加速蒙层点击
    ROTATE_COVER_LAYER_CLICK=206; //联盟-扭一扭蒙层点击
    PHOTO_SPEED_RETAIN_BUTTON_CLICK=207; //联盟-视频加速激励弹窗按钮点击
    PHOTO_SPEED_RETAIN_COVER_LAYER_CLICK=208; //联盟-视频加速激励弹窗蒙层点击
    PHOTO_SPEED_RETAIN_VIEW_CLICK=209; //联盟-视频加速激励弹窗观看按钮点击
    GAME_ROLE_CARD_CLICK=210; //游戏角色卡点击
    GAME_BOX_CARD_CLICK=211; //游戏礼盒点击
    //  20241208 add ADX米盟sdk
    ADX_MI_SDK_EVOKE_APPSTORE=212;  //ADX-小米sdk调起应用商店落地页
    ADX_MI_SDK_EVOKE_APP=213; //小米sdk拉活
    PROMOTE_CARD_ICON_CLICK=214; //激励卡片，点击图标对应logo
    PROMOTE_CARD_ICON_TITLE_CLICK=215; //激励卡片，点击标题名称
    PROMOTE_CARD_ICON_DESCRIPTION_CLICK=216; //激励卡片，点击描述信息
    PROMOTE_CARD_CONTINUE_ACTION_BAR_CLICK=217; //激励卡片，点击继续激励奖励
    PROMOTE_CARD_CLOSE_ACTION_BAR_CLICK=218; //激励卡片，点击退出激励奖励
    PROMOTE_CARD_FURTHER_ACTION_BAR_CLICK=219; //激励卡片，点击领取深度激励奖励
    ITEM_LONG_PRESS_SPEED_ICON = 220; //加速播放按钮
    MANUFACTURER_INVOKE_RETAIN_ACTIONBAR_CLICK = 221; //联盟-厂商挽留弹窗actionbar点击
    MANUFACTURER_INVOKE_RETAIN_POPUP_CLICK = 222; //联盟-厂商挽留弹窗点击
    // 20250507新增硬广滚动评论区组件
    SCROLL_AREA_CLICK = 223;  // 滚动区域点击
    BIG_CARD_ACTION_BAR_CLICK = 224; //大卡页面点击
    SWIPE_BIG_ICON_CLICK = 225; //用户成功点击滑动区域进入落地页
    AD_DEEPLINK_EVOCATION_TASK = 226; // 换端任务
    GRAVITY_SENSOR_OTHER_CLICK = 227; // 重力感应其他区域点击

}

//广告元素点击类型 (已废弃，请添加事件类型到 ElementType中)
enum ElementClickType{
  UNKNOWN_ELEMENT_CLICK_TYPE = 0 ;
  ELEMENT_POPUP_WINDOW_CLICK = 1 ; // 弹窗点击
}

enum PhotoToLiveEntrySource{
  UNKNOWN_PHOTO_TO_LIVE_SOURCE = 0; // 未知作品到直播间来源
  FROM_DETAIL_PAGE_PORTRAIT = 1; // 视频详情页头像
  FROM_PHOTO_PORTRAIT = 2; // 视频封面头像点击
  FROM_HALF_PAGE_BANNER = 3; // 半屏直播导流条点击  https://docs.corp.kuaishou.com/d/home/<USER>
  FROM_LEFT_SLIDE = 4; // 视频详情页左滑
}

enum LiveRoomPattern{
  UNKNOWN_LIVE_ROOM_PATTERN = 0; // 未知直播间样式
  STANDARD_LIVE_ROOM_PATTERN = 1; // 标准版直播间样式
  SIMPLIFIED_LIVE_ROOM_PATTERN = 2; // 简化版直播间样式
}

enum BoxCancelledType {
  UNKNOWN_BOX_CANCELLED_TYPE = 0;
  CANCEL_BUTTON_CLICK = 1; // 点击"取消"按钮
  BACK_AREA_CLICK = 2; // 点击"对话框外部区域"
  PHYCIAL_BACK_CLICK = 3; // 物理back键
  OTHER_BOX_CANCELLED_CLICK = 4; // 其他
}

enum CommonCardType {
  UNKNOWN_CARD_TYPE = 0;
  APP_INSTALL_CARD = 1;
  FORM_CARD = 2 ;     // 金融表单
  GAME_APPOINT = 3 ; // 游戏预约
  // 多利益点卡片组件
  MULTIPOINT_CARD_TYPE_ONE = 4 ;
  MULTIPOINT_CARD_TYPE_TWO = 5 ;
  MULTIPOINT_CARD_TYPE_THREE = 6 ;
  PICTURE_CARD = 7; // 图片卡片
}

enum ItemPlayType {
  UNKNOWN_ITEM_PLAY_TYPE = 0;
  BACKGROUND_CLICK_REPLAY = 1; //播放结束页点击背景重播
  BUTTON_CLICK_REPLAY = 2; // 当用户点击重播按钮，触发重播
  CARD_BACKGROUND_CLICK_REPLAY = 3; // 当用户点击卡片白色背景，触发重播
  RETURN_KEY_CLICK_REPLAY = 4; // 播放结束页点击back键关闭播放结束页并进入重播视频流程
}

// 隐私风险提示场景
enum PrivacyPosition {
    UNKNOWN_PRIVICY_POSITION= 0;
    APPLICATION_POSITION= 1; // 点击"应用信息"
    AUTHORITY_POSITION= 2; // 点击"权限信息"
    PRIVICY_POLICY_POSITION= 3; // 点击“隐私政策”
}

// 广告元素类型，作为 AD_ELEMENT_XXX 事件的参数
enum ElementType {
    UNKONWN_ELEMENT_TYPE = 0;
    ELEMENT_SPLASH = 1; // 开屏主体，AD_SPLASH_CLICK时候会设置
    ELEMENT_SPLASH_ACTION_BAR = 2; // 开屏action bar
    ELEMENT_SPLASH_JUMP_OVER = 3; // 开屏跳过按钮
    ELEMENT_COVER_ACTION_BAR = 4; // 封面的action bar
    ELEMENT_MERCHANT_SHOPPING = 5; // 小店中间页的去购买

    ELEMENT_CARD_ICON_CLICK = 6; // android 下载场景点击通用卡片app icon、
    ELEMENT_CARD_ICON_NAME_CLICK = 7; //  android 下载场景点击通用卡片app 名称
    ELEMENT_CARD_ICON_COMMENT_CLICK = 8; // android 下载场景点击通用卡片app描述
    ELEMENT_PLAY_END_ICON_CLICK = 9; // 用户成功点击播放结束页appicon时触发，android端点击触发成功后跳转悬浮框
    ELEMENT_PLAY_END_ICON_TITLE_CLICK = 10; // 用户成功点击app名称时触发，android端点击触发成功后跳转悬浮框
    ELEMENT_PLAY_END_ICON_DESCRIPTION_CLICK = 11; // 用户成功点击详细描述时触发，android端点击触发成功后跳转悬浮框
    ELEMENT_PLAYEND_APP_SCORE_CLICK = 12; // 用户成功点击app评分时触发，android端点击触发成功后跳转悬浮框
    ELEMENT_PENDANT = 13; // 红包挂件曝光, 根据具体广告位信息上报

    ELEMENT_COVER_LAYER = 14; // 蒙层
    ELEMENT_PLAY_AGAIN_ON_COVER_LAYER = 15; // 蒙层上的【再玩一次】按钮
    ELEMENT_CARD_IMPRESSION = 16; // 弹窗卡片展现

    ELEMENT_PLAY_END_CALL_IMPRESSION = 17; // 播放结束页号召按钮元素被成功曝光
    ELEMENT_PLAY_DETAIL_CALL_IMPRESSION = 18; // 播放详情页号召按钮元素被成功曝光
    ELEMENT_ACTION_BAR = 19; // 蓝条转化按钮曝光
    ELEMENT_LABEL_IMPRESSION = 20; // 展示标签时上报
    ELEMENT_LABEL_CLICK = 21; // 点击标签时上报
    ELEMENT_PORTRAIT_CLICK = 22; // 点击头像进入中间页
    ELEMENT_COMMENT_CLICK = 23; // 点击名字进入中间页
    ELEMENT_COMMENT_DETAIL_CLICK = 24; // 点击作品描述进入中间页
    ELEMENT_LEFT_SLIDE_CLICK = 25; // 左滑进入中间页
    ELEMENT_COMMENT_AREA_ICON_CLICK = 26; // 评论区第一条头像跳转中间页
    ELEMENT_COMMENT_AREA_NAME_CLICK = 27; // 评论区第一条名字跳转中间页
    ELEMENT_COMMENT_AREA_DETAIL_CLICK = 28; // 评论区第一条描述跳转中间页

    ELEMENT_BULLET_IMPRESSION = 29; // 弹幕曝光时上报
    ELEMENT_BULLET_CLICK = 30 ; // 点击弹幕时上报（无跳转）

    ELEMENT_PENDANT_CLICK = 31 ; // 直播页面，挂件广告点击
    ELEMENT_VOLUME_OFF_CLICK = 32 ; // 播放中转静音
    ELEMENT_VOLUME_ON_CLICK = 33 ; // 静音转播放中

    // Android（不含下载类）&iOS - 【双feed】多利益点卡片组件埋点：图标、图片、主标题、子标题等
    ELEMENT_CARD_ICON_OTHERAREA_CLICK = 34 ;
    ELEMENT_CARD_PICTURE_CLICK_ONE = 35 ;
    ELEMENT_CARD_PICTURE_CLICK_TWO = 36 ;
    ELEMENT_CARD_PICTURE_CLICK_THREE = 37 ;
    ELEMENT_CARD_PICTURE_TITLE_CLICK_ONE = 38;
    ELEMENT_CARD_PICTURE_TITLE_CLICK_TWO = 39 ;
    ELEMENT_CARD_PICTURE_TITLE_CLICK_THREE = 40 ;
    ELEMENT_CARD_PICTURE_SUBTITLE_CLICK_ONE = 41 ;
    ELEMENT_CARD_PICTURE_SUBTITLE_CLICK_TWO = 42 ;
    ELEMENT_CARD_PICTURE_SUBTITLE_CLICK_THREE = 43 ;

    ELEMENT_CONFIRM_DOWNLOAD_POPUP_WINDOW_POSITIVE_CLICK = 44 ; // 非actionbar的点击：点击下载确认弹窗的确认按钮

    ELEMENT_PENDANT_ICON_CLICK = 45 ;//点击挂件icon
    ELEMENT_PENDANT_BUTTON_CLICK = 46 ;//点击按钮
    ELEMENT_PENDANT_NAME_CLICK = 47 ;  // 直播挂件点击名称描述
    ELEMENT_PENDANT_OTHER_AREA_CLICK = 48 ;//点击其他区域
    ELEMENT_BUSINESS_GOODS = 49 ;//电商商品
    ELEMENT_BUSINESS_COUPON = 50 ;//电商优惠券
    ELEMENT_EXTEND_CLICK  = 51 ; //展开按钮点击
    ELEMENT_ONEBUTTON_INSTALL_CLICK = 52 ; //一键安装页面中，一键安装按钮点击数量
    ELEMENT_DOWNLOAD_DELETE_CANCELLED = 53 ; //从下载管理器页面，删除弹窗页面关闭；同时上报参数BoxCancelledType
    ELEMENT_DOWNLOAD_DELETE_CONFIRMED = 54 ; //从下载管理器页面，进入删除弹窗页面中，确定按钮点击
    ELEMENT_DOWNLOAD_MANAGEMENT_ENTRY_IMPRESSION = 55 ; // 广告短视频播放页，下载管理器入口曝光时上报；同时上报红点是否出现参数ClientParams.is_reddot_show；
    ELEMENT_DOWNLOAD_MANAGEMENT_ENTRY_CLICK = 56 ; // 下载管理器入口点击；同时上报红点是否出现参数ClientParams.is_reddot_show
    ELEMENT_DOWNLOAD_MANAGEMENT_ENTRY_CLOSE = 57 ; // 下载管理器入口下方关闭按钮点击；同时上报红点是否出现参数ClientParams.is_reddot_show；

    ELEMENT_BULLET_CANCEL_LIKE = 58; // 点击点赞弹幕取消点赞

    ELEMENT_CAPTION_CLICK = 59; // caption中“查看详情”链接点击
    ELEMENT_COMMENT_ITEM_CLICK = 60; // 评论区转化链接点击
    ELEMENT_CARD_RANK_CLICK = 61; // 用户成功点击卡片上的评分
    ELEMENT_CARD_DOWNLOAD_CNT_CLICK = 62; // 用户成功点击卡片里的下载次数
    ELEMENT_CARD_TAG_CLICK = 63; // 用户成功点击卡片里的二级行业
    ELEMENT_CARD_LABEL_CLICK = 64; // 用户点击卡片里的标签

    ELEMENT_INTERACTIVE_COVER_TAG = 65;//分屏广告封面标签
    ELEMENT_INTERACTIVE_PHOTO_TAG = 66;//分屏广告素材标签
    ELEMENT_CARD_ACTION_BAR_CLICK = 67; //H5卡片中，ActionBar点击
    ELEMENT_CARD_VIEW_DETAIL_CLICK = 68; //H5卡片中，点击查看详情

    ELEMENT_CLOSE_ICON = 69; // 关闭按钮

    // 隐私提示相关
    ELEMENT_PRIVACY_IMPRESSION = 70; //隐私提示曝光时上报
    ELEMENT_PRIVACY_CLICK = 71; //点击隐私提示时上报
    ELEMENT_PRIVACY_BULLET_IMPRESSION = 72; //隐私弹框展现
    ELEMENT_PRIVACY_BULLET_CLICK = 73; //点击隐私弹框Tab，同时上报场景参数
    ELEMENT_PRIVACY_BULLET_CLICK_CANCELLED = 74; //隐私弹框关闭，同时上报参数
    ELEMENT_PRIVACY_BULLET_CLICK_RETURNED = 75; //点击隐私二级弹框返回按钮
    ELEMENT_PRIVACY_PROMPT_IMPRESSION = 76; //落地页风险提示横幅展现
    ELEMENT_PRIVACY_PROMPT_CLICK_CLOSED = 77; //点击隐私弹框框关闭按钮
    ELEMENT_PRIVACY_PROMPT_SLIDE_CLOSED = 78; //风险提示-上滑落地页关闭提示条

    ELEMENT_BLANK_AREA_CLICK = 79; // 任务空白区域点击

    ELEMENT_CARD_RECO_IMPRESSION = 80; //推荐理由曝光
    ELEMENT_CARD_RECO_CLICK = 81; // 用户点击推荐理由
    ELEMENT_PLAYABLE_JUMP_OVER = 82 ;//playable跳过
    ELEMENT_PHOTO_CLICK = 83; //点击作品描述进入中间页

    //存储权限引导弹窗按钮
    ELEMENT_DOWNLOAD_MEMORY_FORBIDDEN_TOAST = 84;
    ELEMENT_DOWNLOAD_MEMORY_FORBIDDEN_FOREVER_TOAST = 85;

    ELEMENT_DOWNLOAD_CARD_IMPRESSION = 86; // 下载卡片中间页曝光
    ELEMENT_PLAYEND_CARD_IMPRESSION = 87; // 播放结束页卡片展示
    ELEMENT_PLAYEND_ICON_RECO_CLICK = 88; // 点击播放结束页推荐理由

    ELEMENT_COMMENTS_ACTION_BAR_IMPRESSION = 89; // 评论区蓝条转化按钮曝光
    ELEMENT_PRIVACY_POLICY = 90; // 快直播任务，隐私政策点击
    ELEMENT_PERMISSION_INFO = 91; // 快直播任务，权益信息点击

    ELEMENT_INSTALL_NOTICE_IMPRESSION = 92; // 安装提醒元素曝光
    ELEMENT_CONVERSION_NOTICE_IMPRESSION = 93; // 激活提醒元素曝光

    ELEMENT_VPN_AUTHORITY_GUIDE_IMPRESSION = 94; //VPN权限引导 - 卡片曝光
    ELEMENT_INSTALL_INTERCEPTION_DESCRIPTION_CLICK = 95; //VPN权限引导 - 点击「什么是安装拦截」
    ELEMENT_VPN_AUTHORIZATION_CLICK = 96; //VPN权限引导 - 点击「立即授权」
    ELEMENT_VPN_AUTHORITY_GUIDE_CANCELLED = 97; //VPN权限引导 - 点击「关闭」
    ELEMENT_GET_INSTALL_INTERCEPTION_CLICK = 98; //VPN权限引导 - 点击「我知道了」
    ELEMENT_PLAYING_LANDINGPAGE_IMPRESSION = 99; //播放过程落地页展示
    ELEMENT_PLAYING_FORM_CARD_IMPRESSION = 100; //播放过程中的表单卡片展示
    ELEMENT_PLAYING_FORM_CARD_CLICK = 101; //播放过程中的表单卡片点击
    ELEMENT_PLAYEND_FORM_CARD_CLICK = 102; //播放结束页卡片点击
    ELEMENT_CONFIRM_CLOSED_CARD_IMPRESSION = 103; //确认退出广告卡片曝光
    ELEMENT_CONFIRM_CLOSED_CARD_STAY_CLICK = 104; //退出拦截弹窗中“留下看看”按钮点击
    ELEMENT_CONFIRM_CLOSED_CARD_LEAVE_CLICK = 105; //退出拦截弹窗中“残忍离开”按钮点击
    ELEMENT_CONFIRM_CLOSED_CARD_BLANK_AREA_CLICK = 106; //退出拦截弹窗外空白区域点击

    TAG = 107; //标签点击 已废弃
    ELEMENT_TAG_CLICK = 108; //标签点击

    ELEMENT_PAUSE = 109; //暂停按钮点击
    ELEMENT_CLEARSCREEN = 110; //清屏操作点击

    ELEMENT_PAUSE_DOWNLOAD_CONFIRMED_IMPRESSION = 111;//弹框成功曝光时上报
    ELEMENT_CONTINUE_DOWNLOAD_CONFIRMED = 112;//用户成功点击继续按钮
    ELEMENT_PAUSE_DOWNLOAD_CONFIRMED = 113;//用户成功关闭弹窗时上报
    ELEMENT_SPLASH_ENTER_HOMEPAGE_BUTTON_CLICK = 114; //开屏进入首页按钮点击
    ELEMENT_SPLASH_SMALL_WINDOW_IMPRESSION = 115; //开屏小窗曝光
    ELEMENT_INSTALL_NOTICE_CLOSED = 116; // 引导弹窗成功关闭

    ELEMENT_CARD_NON_ACTION_BAR_CLICK = 117; //点击非actionBar区域 上报
    ELEMENT_CARD_LEFT_ACTION_BAR_CLICK = 118; // 点击左边actionBar区域 上报

    ELEMENT_GET_POINTS = 119; //领取积分

    ELEMENT_SURVEY = 120; //广告信任生态-用户反馈入口：问卷
    ELEMENT_PENDANT_ICON_DESCRIPTION_CLICK = 121;//点击图标描述

    ELEMENT_SEARCH_PHOTO_CLICK = 122; // 搜索结果页点击，进入视频详情页
    //20210510 add
    SPLASH_CONVERSION_BUTTON_IMPRESSION = 123; //开屏转化按钮曝光
    SPLASH_SKIPED_5S_IMPRESSION = 124; //开屏跳过倒计时5秒曝光
    ELEMENT_SPLASH_SMALL_WINDOW_PICTURE_IMPRESSION = 125; //开屏图片小窗曝光
    ELEMENT_SEARCH_TOPIC_CLICK = 126; // 搜索结果页-点击广告描述区的话题高亮部分，跳转话题广场
    //20210527 add
    ELEMENT_BIG_CARD_IMPRESSION = 127; //大表单曝光
    ELEMENT_BIG_CARD_CLOSE_CLICK = 128; //大表单关闭按钮点击
    ELEMENT_BIG_CARD_SUBMIT_CLICK = 129; //大表单提交按钮点击
    ELEMENT_BIG_CARD_ENTER_LANDING_PAGE = 130; //大表单跳转至落地页按钮点击

    ELEMENT_TOAST_CLOSE = 131; //toast关闭
    //20210618 add
    ELEMENT_VISIRER_AREA = 132; //观众区域
    ELEMENT_GUAID_AREA = 133; //用户引导区域
    ELEMENT_NEW_GOODS_REMINDING = 134; //新上架气泡
    ELEMENT_GOODS_REVIEW_PAGE = 135; //回放页点击
    ELEMENT_GOODS_DETAIL_PAGE = 136; //商品详情页
    ELEMENT_BIG_SHOP_CARD = 137; //正在讲解卡片
    ELEMENT_GOODS_ITEM = 138; // 列表商品
    ELEMENT_STRONG_CARD = 139; //强卡片
    ELEMENT_WEAK_CARD = 140; //弱卡片
    ELEMENT_ITEM_PLAY_BACK_CLICK = 141; //商品讲解回放点击
    ELEMENT_ITEM_CARD_CLICK = 142; //回放页-商品卡片点击
    ELEMENT_CARD_STRONG_STYLE = 143; //强样式卡片
    ELEMENT_CARD_WEAK_STYLE = 144; //弱样式卡片
    ELEMENT_ITEM_CLOSE_ICON_CLICK = 145; //回放页-关闭icon点击
    ELEMENT_RETURN_LIVEROOM_CLICK = 146; //回放页-返回直播间点击
    ELEMENT_PAUSE_BUTTOM_CLICK = 147; //回放页-暂停按钮点击
    ELEMENT_PROGRESS_BUTTON_CLICK = 148; //回放页-进度条点击
    //20210622 add
    ELEMENT_RETAIN_CARD_IMPRESSION = 149; //挽留用户弹窗曝光
    ELEMENT_RETAIN_CARD_CONTINUE_CLICK = 150; //挽留用户弹窗内继续观看按钮点击
    ELEMENT_RETAIN_CARD_LEAVE_CLICK = 151; //挽留用户弹窗内离开按钮点击
    MIDDLE_PAGE_DOWNLOAD_BUTTON_CLICK = 152; //中间页触发下载按钮点击
    MIDDLE_PAGE_BACK_BUTTON_CLICK = 153; //中间页返回上一级按钮点击
    // POPLAY 彩蛋弹窗
    ELEMENT_POPLAY_PICTURE_IMPRESSION = 154; // POPLAY 彩蛋弹窗，弹窗曝光，浮现时上报埋点，重新播放时重新上报
    ELEMENT_POPLAY_CLOSE_CLICK = 155; // POPLAY 彩蛋弹窗，点击彩蛋弹窗关闭按钮
    ELEMENT_POPLAY_OTHER_CLICK = 156; // POPLAY 彩蛋弹窗，彩蛋蒙层其他区域点击，点击后无反应
    ELEMENT_SPLASH_INTERACTION_ROTATE_CLICK = 157; // 开屏轻互动点击（通过旋转手机触发或手势触发）
    EVOCK_DOUBLE_CARD_IMPRESSION = 158; //再次开启引导弹窗曝光
    EVOCK_DOUBLE_CARD_OK_CLICK = 159; //再次开启引导弹窗中「好的」点击
    EVOCK_DOUBLE_CARD_CANCEL_CLICK = 160; //再次开启引导弹窗中「取消」点击
    //20210714 add
    FORCE_WATCH_ELEMENT_IMPRESSION = 161; //强制观看元素曝光
    //20210716 add
    ELEMENT_ANVIDEO_CONFIRMED = 162; // 激励视频再看一个弹窗中「再看一个按钮」
    ELEMENT_ANVIDEO_CLOSE = 163; // 激励视频再看一个弹窗中「坚持退出」按钮
    ELEMENT_PLAYEND_ANVIDEO = 164; //激励视频播放结束页「再看一个广告按钮」
    //20210722 add
    ELEMENT_AD_AUTO_CLOSE_IMPRESSION = 165; //自动离开
    ELEMENT_SPLASH_SHAKE_CLICK = 166; // 开屏摇一摇点击（通过摇动手机触发）
    ELEMENT_SPLASH_SLIDE_CLICK = 167; // 开屏横划解锁点击（通过滑动触发）
    //20210727 add
    ELEMENT_RETAIN_CARD_FAILED_IMPRESSION = 168; //挽留用户弹窗因触发频控未展示上报

    //20210825 add
    ELEMENT_IMMEDIATELY_OBTAIN_IMPRESSION = 169;//立即领取元素曝光
    ELEMENT_COPY_CODE_IMPRESSION = 170;//复制礼包码元素曝光
    ELEMENT_IMMEDIATELY_OBTAIN_CLICK = 171;//立即领取元素点击
    ELEMENT_PROMPT_COPY_CODE_CLICK = 172;//复制礼包码元素点击
    ELEMENT_OBTAIN_SUCCESS_WINDOW_IMPRESSION = 173;//领取成功弹窗曝光
    ELEMENT_COPY_CODE_CONFIRM_CLICK = 174;//复制礼包码确认元素点击
    ELEMENT_COPY_CODE_CLOSE_CLICK = 175;//复制礼包码取消元素点击
    ELEMENT_TOAST_COPY_CODE_IMPRESSION = 176;//复制礼包码成功toast提示弹窗曝光

    //20210915 add 激励视频小黄车
    ELEMENT_TROLLEY_IMPRESSION = 177; //小黄车元素的曝光
    //20211025 add 激励电商
    ELEMENT_GOODS_PHOTO_IMPRESSION = 178; //商品素材曝光
    //20211027 add 中间页
    ELEMENT_MIDDLE_PAGE_CLICK = 179; //中间页图片点击，本次用于智能下载中间页
    //20211029 add
    ELEMENT_PLAY_PAUSE = 180; //点击视频暂停播放，适用于效果广告业务
    ELEMENT_PLAY_CONTINUE = 181;//点击视频继续播放，适用于效果广告业务
    ELEMENT_RECOMMEND_CARD_IMPRESSION = 182;// 相关推荐场景卡片曝光
    ELEMENT_RECOMMEND_CARD_ICON_CLICK = 183;//  相关推荐场景卡片点击
    //20211101 add
    ELEMENT_ROTATE_ACTION_BAR  = 184 ;//扭一扭元素曝光
    ELEMENT_SHAKE_ACTION_BAR = 185;// 摇一摇元素曝光
    ELEMENT_SPLASH_BALL_ROLL_CLICK = 186; // 滚动小球开屏
    ELEMENT_SPLASH_FLIP_X_CLICK = 187; // 翻转 x 轴开屏
    ELEMENT_SPLASH_FLIP_Y_CLICK = 188; // 翻转 y 轴开屏
    //20211116 add
    ELEMENT_PAGE = 189 ;//页面元素，配合业务场景类型business_scene_type，可以知道是什么页面
    //20211125  cyl_add
    ELEMENT_FINGER_SWIPE = 190 ;//滑动元素，通过滑动进入广告
    ELEMENT_POP_UP = 191 ;//弹窗元素
    //20211202 cyl_add
    ELEMENT_PENDANT_TRY_PLAY = 192; //试玩挂件元素
    //20211220 cyl_add
    ELEMENT_FINGER_SWIPE_ACTIONBAR = 193 ;//滑动元素，必须经过actionbar，和ELEMENT_FINGER_SWIPE的区别是必须经过actionbar
    //20211222 cyl_add
    ELEMENT_BUTTON = 194 ;//按钮元素，可以配合button_type进行判断按钮类型
    //20220217 cyl_add
    ELEMENT_NEO_PENDANT_MERCHANT_COUPON = 195  ;//激励电商券挂件
     //20220307 聚星add
    ELEMENT_RESERVE_DOWNLOAD = 196 ;//预约下载按钮，目前用于聚星，之后可以用于其他业务线
    //20220324 cyl_add
    ELEMENT_BLUE_V =197 ; //蓝v元素
    //20220613 cyl_add
    ELEMENT_STORE = 198 ;//进店元素
    //20220728 cyl_add
    ELEMENT_COMBINATION1_ACTION_BAR =199 ;//组合acionbar的曝光，用于联盟开屏摇一摇、扭一扭等组合actionbar
    ELEMENT_COMBINATION2_ACTION_BAR =200 ; //组合acionbar的曝光,用于联盟开屏扭一扭等组合actionbar，不包含摇一摇
    ELEMENT_FALL_ICON = 201 ;// 飘落彩蛋元素
    //品牌破框元素
    ELEMENT_BIG_BROKEN_FRAME = 202 ;//破框元素大挂件
    ELEMENT_SMALL_BROKEN_FRAME = 203 ; //破框元素小挂件
    ELEMENT_FRONT_LANDING_PAGE=204;//联盟-激励前置落地页
    ELEMENT_PLAY_END_SHAKE_ACTION_BAR=205;//联盟--开屏结束页摇一摇元素
    ELEMENT_NOT_DOWNLOAD_GUIDE=206; //联盟-非下载类引导页面
    ELEMENT_DIRECT_H5_PAGE=207; //联盟-插屏直出H5页面
    ELEMENT_GOODS_ITEM_CARD=208; //联盟-直播流多商品卡
    ELEMENT_CARD_ENTER_LIVEROOM = 209; //联盟-引导进入直播间弹窗
    ELEMENT_ITEM_HOT_BUY_STYLE = 210; //联盟-轻热卖样式
    ELEMENT_PLAYEND_GOODS_CARD=211; //联盟-直播结束页商品列表样式
    ELEMENT_PLAYEND_LIVE_AUTHOR_CARD=212; //联盟-直播结束页主播信息样式
    ELEMENT_COMMENT_TOP_BAR_IMPRESSION = 213; //评论区顶bar曝光
    ELEMENT_PROMPT_CLICK=214;//提示条点击
    ELEMENT_PROMPT_DETAIL_IMPRESSION = 215;//提示详情页曝光
    ELEMENT_PROMPT_DETAIL_CLOSE=216;//提示详情页关闭
    ELEMENT_SPLASH_CARD_ACTIONBAR = 217 ;//开屏卡片actionbar元素
    ELEMENT_PHOTO = 218 ;//视频元素
    ELEMENT_LIVE_GOOD_LUCK=219; //好运来actionbar
    ELEMENT_LIVE_RESERVATION=220; //直播预约卡片
    ELEMENT_LABEL_IMPRESSION_WHITE = 221; // 展示标签时上报,白底
    ELEMENT_POPLAY_FALL_IOCN = 222; // 交互挂件
    ELEMENT_PHOTO_CAPTION_SEARCH_WORD = 223; //作品caption搜索词
    ELEMENT_COMMENT_CAPTION_SEARCH_WORD = 224; //评论区caption搜索词
    ELEMENT_COMMENT_GUESS_SEARCH_WORD = 225; //评论区猜你想搜搜索词
    ELEMENT_NEGATIVE_ENTRANCE = 226; //负反馈入口
    ELEMENT_ROTATE_SHAKE_ACTION_BAR  = 227 ;//晃一晃元素曝光
    ELEMENT_PICTURE = 228 ;//图片元素
    ELEMENT_DOUBLE_CONFIRM_IMPRESSION = 229 ;//二次确认弹窗元素曝光
    ELEMENT_DOUBLE_CONFIRM_CLICK = 230 ;//二次确认弹窗元素曝光-点击确认
    ELEMENT_DOUBLE_CONFIRM_CANCEL_CLICK = 231 ;//二次确认弹窗元素曝光-点击取消
    ELEMENT_RETAIN_AGG_CARD_IMPRESSION = 232 ; //挽留用户激励聚合卡片曝光
    ELEMENT_PLAYEND_AGG_CARD_IMPRESSION = 233; // 播放结束页激励聚合卡片曝光
    ELEMENT_PLAYLET_MORE = 234; //短剧点击更多
    ELEMENT_PLAYLET_FAVORITES = 235; //短剧点击收藏
    ELEMENT_PLAYLET_SELECTTIONS = 236; //短剧选集半屏曝光
    ELEMENT_SURPRISE_BOX= 237; //惊喜盒子曝光
    ELEMENT_BUBBLE_RAIN= 238; //气泡雨曝光
    ELEMENT_ECOMMERCE_INDUSTRY_COUPON = 239 ;//联盟电商行业优惠券模板
    ELEMENT_CARD_SHAKE = 240; // 卡片摇动样式曝光
    ELEMENT_CARD_BREATH = 241; // 卡片呼吸样式曝光
    ELEMENT_SECOND_IMPRESSION = 242 ;//搜索结果页二屏曝光
    ELEMENT_COMBINATION3_ACTION_BAR = 243 ; //组合acionbar的曝光 滑动+点击
    ELEMENT_PLAYING_CARD_IMPRESSION = 244; // 播放页元素曝光
    ELEMENT_STORY_INTRODUCTION_BUTTON = 245; //短剧--付费面板剧情介绍按钮
    ELEMENT_CONTINUE_PURCHASE_BUTTON = 246; //短剧--简介页继续购买按钮
    ELEMENT_TUBE_IAP_RETAIN_POPUP_JUMP_BUTTON=247; //短剧——付费挽留弹窗领券购买按钮
    ELEMENT_TUBE_IAP_RETAIN_POPUP_LEAVE_BUTTON=248; //短剧--付费挽留弹窗狠心离开按钮
    ELEMENT_PURCHASE_TUBE_SAME_GUIDE_POPUP=249; //同剧引导弹窗
    ELEMENT_PURCHASE_TUBE_POPUP_FINGER_ANIMATION=250; //短剧--付费面板手指动效
    ELEMENT_PLAY_END_CARD_TAG=251; //短剧-结束页卡片标签（开启跳转至榜单页）
    ELEMENT_GRAVITY_SENSOR_IMPRESSION=252; //重力感应曝光
    ELEMENT_LONG_PRESS_SPEED_IMPRESSION=253; //联盟-长按加速组件曝光
    ELEMENT_PHOTO_SPEED_RETAIN_POPUP_IMPRESSION=254; //联盟-长按加速激励挽留弹窗曝光
    ELEMENT_GAME_ROLE_CARD=255; //游戏角色卡片
    ELEMENT_GAME_BOX_CARD=256; //游戏礼盒
    ELEMENT_MIDDLE_PAGE_IMPRESSION=257; // 中间页组件曝光
    ELEMENT_PHOTO_BAR_SEARCH_WORD=258; // 视频底bar搜索词入口
    ELEMENT_PROMOTE_CARD_IMPRESSION=259;//激励卡片曝光
    ELEMENT_EASY_PLAYABLE_IMPRESSION=260;//联盟广告轻互动样式曝光
    ELEMENT_AVATAR=261; //虚拟主播或者数字人形象
    ELEMENT_CARD_ACTION_BAR_IMPRESSION=262; //搜索品专引导文案曝光
    ELEMENT_RECO_ACTION_BAR=263; //对话引导卡推荐问题
    ELEMENT_ENTER_ACTION_BAR=264; //对话框输入按钮
    ELEMENT_LONG_PRESS_SPEED_ICON = 265; //加速播放按钮
    ELEMENT_FINISH_LONG_PRESS_SPEED_ICON = 266; //加速播放完成按钮
    ELEMENT_LIVE_CANCEL_CLICK = 267; //点击取消按钮，取消进入标准直播间
    ELEMENT_MANUFACTURER_INVOKE_RETAIN_POPUP = 268; //联盟-厂商唤端挽留弹窗曝光
    ELEMENT_RETAIN_CARD_CHANGE_ANOTHER_PHOTO = 269; // 挽留弹窗'换个视频'点击
    ELEMENT_PHOTO_DEATIL_LIVE_INTERACTION = 270; //聚星-视频详情直播互动组件
    ELEMENT_COMMENT_LOTTERY = 271;//聚星-评论区抽奖组件
    ELEMENT_CARD_QUICK_COMMENT = 272; //高级创意-快捷评论
    //20250804 zhoubo12 add
    ELEMENT_COUPON_AUTHORIZATION_IMPRESSION = 273; //联盟-授权优惠券弹窗曝光
    ELEMENT_COUPON_CONFIRM_CLICK = 274; //联盟-授权优惠券确认按钮点击
    ELEMENT_COUPON_CLOSE_CLICK = 275; //联盟-授权优惠券关闭按钮点击
    ELEMENT_RULE_DESCRIPTION_DETAIL = 276; //规则说明详情
    ELEMENT_RULE_DESCRIPTION_CARD = 277; //规则说明卡片
}

enum MediaBarType {
  UNKNOWN_MEDIA_BAR_TYPE = 0;
  DOWNLOAD_MEDIA_TYPE = 1; // 下载类多媒体
  MERCHANT_SHOWCASE = 2; // 商品橱窗卡片
  DYNAMIC_CARD = 3; // 动态卡片
  ELE_BUSINESS = 4; //电商类卡片
  INFO_RECEIVE = 5; //信息采集类卡片
}

enum MediaBarSource {
  UNKNOWN_MEDIA_BAR_SOURCE = 0;
  CARD_CLICK = 1; // 点击调起
  AUTO = 2; // 7s 自动调起
  LIKE = 3; // 点赞调起
  BOTTOM_BAR = 4; // 吸顶 bar
}

enum AdPhotoToProfileType {
  UNKNOWN_AD_PHOTO_TO_PROFILE_TYPE = 0;
  LEFT_SLIDE_TO_PROFILE = 1; // 左滑进入profile页
  PORTRAIT_CLICK_TO_PROFILE = 2; // 非直播状态下头像点击进profile页
  CAPTION_NAME_CLICK_TO_PROFILE = 3; // caption区域作者名字点击进profile页
  COMMENT_NAME_CLICK_TO_PROFILE = 4; // 评论区作者名字点击进profile页
  COMMENT_PORTRAIT_CLICK_TO_PROFILE = 5; // 评论区头像点击进profile页
  CO_CREATO_CLICK_TO_PROFILE = 6;// 通过点击共创者头像进入个人主页。目前聚星使用
  OTHER_AREA_CLICK_TO_PROFILE= 7;//点击其他区域进profile页
}

enum AdSubSourceType {
  UNKNOWN_SUB_SOURCE_TYPE = 0;
  ESP_MOBILE = 1;   //   磁力金牛移动端（随心推）
  BUSINESS_FANSTOP = 2;  // b端粉条
  COMBO_ORDER = 3; // 磁力金牛套餐
  AD_SOCIAL_ORDER = 4;  // 聚星流量助推 2.0
  DSP_SELF_SERVICE = 5;  // DSP自助户
  FANS_TOP_TEMU = 6; // 粉条企业暗户，主要用于拼多多C端投激活
  COMBO_PACKAGE = 7;  // 外循环中小专项-套餐包
  FANS_TOP_V2_REAL_TIME = 8; // 粉条持续投
  ROI_ORDER = 9; // 全站锁预算
}

enum AdSourceType {
  UNKNOWN_SOURCE_TYPE = 0;
  DSP = 1; // 效果广告
  ALI_BAICHUAN = 2; // 阿里百川dsp
  BAIDU_MOBADS = 3; // 百度联盟广告dsp
  FANS_TOP = 4; // 粉丝头条
  AD_SOCIAL = 5; //社交广告
  TANX_SSP = 6; //阿里TanX-ssp
  MEITUAN_WAIMAI = 7; //美团外卖dsp
  GR_PHOTO = 8; //GR视频
  GR_LIVE = 9; //GR直播
  JD_ADS = 10; //京东广告dsp
  ALI_DONGFENG = 11; //阿里东风广告dsp
  MERCHANT = 12; //电商作品
  NETEASE_ADS = 13; //网易广告dsp
  TANX_DSP = 14; //阿里DSP（与TANX_SSP不同，这个是阿里作为DSP，kuaishou是ADX）
  DOMOB_DSP = 15; //多盟广告dsp
  ALI_GAME = 16; //阿里游戏广告dsp
  IFLYTEK_DSP = 17; //科大讯飞广告dsp
  YOUDAO_DSP = 18; //有道广告dsp
  PINYOU_DSP = 19; //品友dsp
  YOUMI_DSP = 20; //有米dsp
  SANTKJ_DSP = 21; //三体科技dsp
  ADX = 22; //adx统一sourceType,adSourceType里的之后弃用,使用AdxSourceType
  ALI_ZHIZUAN = 23; //阿里智钻
  SOGOU_DSP = 24; //搜狗广告dsp
  FANS_TOP_LIVE = 25; //粉条直播推广
  SUNING_DSP = 26; //苏宁dsp
  UC_DSP = 27; // UC dsp
  ZHIXING_DSP = 28; //之行dsp
  MEITUAN_POI = 29; // 美团poi
  TECENT_GAME_DSP = 30; //腾讯游戏dsp
  DPA = 31; //dpa广告
  BRAND = 32; // 品牌广告
  FANS_TOP_OPERATION = 33;  //粉丝头条内部运营
  FANS_TOP_LIVE_BY_DSP = 34;   // 直播粉条广告
  QUICK_TASK = 35; //全民任务
  FANS_TOP_ENGINE = 36;  // add by cuiyanliang: 粉条引擎用做全链路监控
  LIVE_RESERVATION = 37; //直播预约投快享
  FANS_TOP_V2 = 38;  // 粉条信息流合并后粉条的标志
  KE_DSP = 39;      // 贝壳dsp
  SHENNONGJIA = 40; // 神农架 电商联盟 liqinglong
//  UNION_BIDDING = 41; // 聚合买量, 联盟 chihuanqi
//  UNIVERSE_RTA = 42; // 联盟rta, 联盟做为rta, 接入腾讯等外部广告平台 chihuanqi
  BLUE_V = 43; // 企业蓝V liuwenhui
  DSP_NATURE = 44; // 广告自然流量 zhangzhanhui
}

// 标识日志的业务来源, 用于数据流区分处理
// reviewer: raoli
enum AdLogSourceType {
    UNKNOWN_LOG_SOURCE_TYPE = 0; // 未知流量和不需要区分处理的流量
    // 1-100 预留
    UNIVERSE_FAKE = 101; // 联盟fake广告: 基于实际的dsp创意构造, 不走广告引擎投放, ad_log缺失部分字段. 不进模型调价,不做个性化分成
}

enum DpaSourceType {
  UNKNOWN_DPA_SOURCE_TYPE = 0;
  DPA_VIP = 1;  // 唯品会 dpa 广告
  DPA_ALI = 2;  // 阿里 dpa 广告
  DPA_SUNING = 3; // 苏宁 dpa 广告
  DPA_JD = 4; // 京东 dpa 广告
  DPA_MOGU = 5; // 蘑菇街 dpa 广告
  DPA_PDD = 6; // 拼多多 dpa 广告
  DPA_TAOBAO_SPECIAL = 7; // 淘宝特价版 dpa 广告
  DPA_XIANYU = 8; // 闲鱼 dpa 广告
  DPA_TAOBAO_JUHUASUAN = 9; // 淘宝聚划算 dpa 广告
  DPA_QIYUE = 10; // 七悦书城 dpa 广告
  DPA_FICTION = 11; // 书库DPA枚举
  DPA_DIANZHONG = 12; // 点众阳光书城 dpa 广告
  DPA_ANJUKE = 13; // 安居客
  DPA_COURSE = 14; // 课程库DPA枚举
  DPA_HUASHENG = 15; // 花生书城
  DPA_WEIMIAO = 16; //微淼
  DPA_XIAOPENG = 17; // 小鹏汽车
  DPA_ZHONGAN = 18; // 众安保险
  DPA_WANGYIWENXUE = 19; // 网易文学
  DPA_SHUIDIBAOXIAN = 20; // 水滴保险
  DPA_AIBANGBAOXIAN = 21; // 爱邦保险
  DPA_HEINIUBAOXIAN = 22; // 黑牛保险
  DPA_COMMON = 23; //DPA通用枚举
}

enum AdxSourceType {
  UNKNOWN_ADX_SOURCE_TYPE = 0;
  ADX_ALI_BAICHUAN = 2; // 阿里百川dsp
  ADX_BAIDU_MOBADS = 3; // 百度联盟广告dsp
  ADX_TANX_SSP = 6; //阿里TanX-ssp
  ADX_MEITUAN_WAIMAI = 7; //美团外卖dsp
  ADX_JD_ADS = 10; //京东广告dsp
  ADX_ALI_DONGFENG = 11; //阿里东风广告dsp
  ADX_NETEASE_ADS = 13; //网易广告dsp
  ADX_TANX_DSP = 14; //阿里DSP（与TANX_SSP不同，这个是阿里作为DSP，kuaishou是ADX）
  ADX_DOMOB_DSP = 15; //多盟广告dsp
  ADX_ALI_GAME = 16; //阿里游戏广告dsp
  ADX_IFLYTEK_DSP = 17; //科大讯飞广告dsp
  ADX_YOUDAO_DSP = 18; //有道广告dsp
  ADX_PINYOU_DSP = 19; //品友dsp
  ADX_YOUMI_DSP = 20; //有米dsp
  ADX_SANTKJ_DSP = 21; //三体科技dsp
  ADX_ALI_ZHIZUAN = 23; //阿里智钻
  ADX_SOGOU = 24; //搜狗dsp
  ADX_SUNING = 26; //苏宁dsp
  ADX_UC_DSP = 27; //UC dsp
  ADX_ZHIXING_DSP = 28; //之行dsp
  ADX_TECENT_GAME_DSP = 30; //腾讯游戏dsp
  ADX_ALI_TANX_SHUCHAO = 31; //阿里数巢
  ADX_VIPSHOP_DSP = 32; // 唯品会dsp
  ADX_GDT_DSP = 33; //腾讯广点通
  ADX_FANCY_DSP = 34; // fancy
  ADX_DIDI_DSP = 35; //滴滴
  ADX_MEITUAN_DSP = 36; //美团
  ADX_QUNAR_DSP = 37; //去哪儿
  ADX_ALI_DPA = 38; //阿里DPA当成ADX处理
  ADX_58 = 39; //58同城
  ADX_BEHE = 40; //壁合广告
  ADX_MOGU = 41; //蘑菇街
  ADX_KANGYUAN = 42; //康远广告
  ADX_YOYI = 43; //悠易互通
  ADX_BAIDU_UNION = 44; //百度联盟
  ADX_TECENT_INFORMATION_FLOW = 45; // 腾讯看点
  ADX_KOKO_DSP = 46; // 可口可乐 开屏专用
  ADX_MTG_DSP = 47; // 腾讯看点
  ADX_YEZI_DSP = 48; // 椰子DSP
  ADX_XIAOYUN_DSP = 49; // 小云DSP
  ADX_OPPO_DSP = 50; // oppo
  ADX_BAOJIE_DSP = 51; // 宝洁品牌
  ADX_BUDWEISER_DSP = 52; // 百威啤酒品牌DSP
  ADX_PAIZE_DSP = 53; //派择效果dsp
  ADX_PAIZE_SPLASH_DSP = 54; //派择品牌dsp
  ADX_YUM_DSP = 55; // 百盛品牌DSP(肯德基)
  ADX_GROUPM_DSP = 56; // 群邑品牌DSP
  ADX_YOYI_BRAND_DSP = 57; // YOYI品牌DSP
  ADX_MEITUAN_DAODIAN = 58; // 美团到店
  ADX_PINDUODUO = 59; // 拼多多
  ADX_IFLYTEK_BRAND_DSP = 60; // 科大讯飞品牌DSP
  ADX_YIYANG_BRAND_DSP = 61; // 厦门易扬品牌DSP
  ADX_INMOBI_BRAND_DSP = 62; // inmobi品牌DSP
  ADX_IPINYOU_BRAND_DSP = 63; // 品友互动品牌DSP
  ADX_RUANGAOWANG_BRAND_DSP = 64; // 软告网品牌DSP
  ADX_INMOBI_DSP = 65; //inmobi效果广告dsp
  ADX_VIPSHOP_BRAND_DSP = 66; //唯品会品牌广告
  ADX_HUAWEI_BRAND_DSP = 67; //华为品牌广告
  ADX_JD_TG_BRAND_DSP = 68; //京东天宫品牌广告
  ADX_NETEASE_GAME_DSP = 69; //网易游戏广告
  ADX_KUAISHOU_USER_GROWTH_DSP = 70; //快手用户增长业务，信息流广告投放
  ADX_LONGXI_BRAND_DSP = 71; //珑玺品牌广告
  ADX_YILI_BRAND_DSP = 72; //伊利品牌广告
  ADX_MIFU_BRAND_DSP = 73; //米赋品牌广告
  ADX_XINYI_BRAND_DSP = 74; //新义互联品牌广告
  ADX_MEITUAN_APP_DSP = 75; // 美团app主站
  ADX_TAOBAO_APP_DSP = 76; // 手机淘宝
  ADX_KE_DSP = 77;          // 贝壳dsp
  ADX_TUIA_DSP = 78;        // 推啊
  ADX_BIANXIAN_CAT_DSP = 79; //变现猫
  ADX_FAN_ZHOU_DSP = 80;//泛舟
  ADX_MEI_SHU_DSP = 81;//美数
  ADX_PDD_BRAND_DSP = 82;// 拼多多品牌DSP
  ADX_YI_DIAN_DSP = 83;//亿典dsp
  ADX_SIYINIAO_DSP = 84; // 四翼鸟dsp
  ADX_MEITUAN_SPLASH_DSP = 85; // 美团开屏DSP
  ADX_WANGMAI_DSP = 86; // 旺脉DSP
  ADX_XINYI_DSP = 87; // 新义互联DSP
  ADX_JIATOU_DSP = 88;// 上海佳投DSP
  ADX_TAP_TAP = 89; // taptap
  ADX_XIAOXIONG_DSP = 90; // 小熊博望DSP
  ADX_MIMAN_DSP = 91; //米漫DSP
  ADX_TAOBAO_APP_SPLASH_DSP = 92; // 手机淘宝开屏dsp
  ADX_HAINABAICHUAN_DSP = 93; // 海纳百川
  ADX_UMENG = 94; // 友盟
  ADX_TENCENT_O2_BRAND_DSP = 95; //品牌腾讯O2dsp
  ADX_ZHIZUAN_SPLASH_DSP = 96; // 阿里智钻开屏
  ADX_SHENGYING_DSP = 97;  // 圣盈科技
  ADX_RUISHI_DSP = 98;  // 瑞狮科技
  ADX_JINGMEI_DSP = 99; // 京媒
  ADX_FANWEI_DSP = 100; // 泛为
  ADX_MEITUAN_BRAND_DSP = 101; //美团
  ADX_ZHILONG_DSP = 102; // 志隆
  ADX_KWAI_USER_GROWTH_DSP = 103; // 用户增长部门
  ADX_YIMENG_DSP = 104; // 邑盟
  ADX_JIZHUN_DSP = 105; // 极准
  ADX_RUANGAO_DSP = 106; // 软告
  ADX_SHUYIN_DSP = 107; // 数因
  ADX_DOUJING_DSP = 108; // 斗鲸
  ADX_JIXIN_DSP = 109; // 吉欣
  ADX_YOULIANGHUI_DSP = 110; // 优量汇
  ADX_AUTO_DSP_ID_111 = 111; // 语意不明确已废弃，请不要使用
  ADX_AUTO_DSP_ID_112 = 112; // 语意不明确已废弃，请不要使用
  ADX_AUTO_DSP_ID_113 = 113; // 语意不明确已废弃，请不要使用
  ADX_AUTO_DSP_ID_114 = 114; // 语意不明确已废弃，请不要使用
  ADX_AUTO_DSP_ID_115 = 115; // 语意不明确已废弃，请不要使用
  ADX_AUTO_ANFU_DSP = 116; // 安阜
  ADX_AUTO_YOUJING_DSP = 117; // 有境
  ADX_AUTO_KUAIYOU_DSP = 118; // 快有
  ADX_AUTO_BULUKEN_DSP = 119; // 布鲁肯
  ADX_AUTO_YIQU_DSP = 120; // 易去
  ADX_AUTO_MOBITE_DSP= 121; // 摩比特
  ADX_AUTO_SHUZIYUEDONG_DSP = 122; // 数字悦动
  ADX_AUTO_LETUI_DSP = 123; // 乐推
  ADX_AUTO_DIRECT_VIVO_DSP = 124; // 直客vivo
  ADX_AUTO_JIAMING_DSP = 125; // 嘉名
  ADX_GUANGTUI_DSP = 126; // 广推科技
  ADX_AUTO_HUTUI_DSP = 127; // 互推科技
  ADX_AUTO_MEISHU_DSP = 128; // 美数
  ADX_AUTO_MIEJIKE_DSP = 129; // 咩叽科
  ADX_GUZHI_DSP = 130; // 古芝
  ADX_XIAODE_DSP = 131; // 晓得
  ADX_SANSHENG_DSP = 132; // 广推主站
  ADX_BEIJING_LING_JU = 133; // 北京灵驹
  ADX_AUTO_HAILIANG_DSP = 134; // 嗨量科技
  ADX_AUTO_JUGAO_DSP = 135; // 聚告德业
  ADX_AUTO_YOUYOU_DSP = 136; // 数优汇能
  ADX_KS_TEST = 137; // 快手内部测试专用
  ADX_AUTO_QUKANDIAN_DSP = 138; // 上海芸晞信息科技有限公司
  ADX_AUTO_WANGMAI_DSP = 139; // 上海旺脉信息科技集团有限公司
  ADX_AUTO_FENGHUANG_DSP = 150; // 凤凰飞扬（北京）新媒体信息技术有限公司
  ADX_AUTO_FUXIANG_DSP = 151; // 上海赋详网络科技有限公司
  ADX_AUTO_MANGTUO_DSP = 152; // 上海芒拓网络科技有限公司
  ADX_AUTO_HUAWEI_DSP = 153; // 华为技术有限公司
  ADX_AUTO_MOGUO_DSP = 154; // 上海墨果网络科技有限公司
  ADX_AUTO_DUITUI_DSP = 155; // 霍尔果斯兑推网络有限公司
  ADX_AUTO_QIHU_DSP = 156; // 北京奇虎科技有限公司(360)
  ADX_AUTO_XIAOMI_DSP = 157; // 小米科技有限责任公司
  ADX_AUTO_SHANGYOU_DSP = 158; // 天津尚友科技有限公司
  ADX_AUTO_YOUTUI_DSP = 159; // 摩比特(新代理)
  ADX_WANG_MAI_DSP = 160; // 旺脉
  ADX_AUTO_WANG_JU_DSP = 161; // 北京一点网聚科技有限公司
  ADX_AUTO_SUI_SHEN_YUN_DSP = 162; // 随身云（南京）信息技术有限公司
  ADX_AUTO_TA_KU_DSP = 163; // 广州塔酷信息科技有限公司
  ADX_AUTO_ZHONG_YUAN_DSP = 164; // 北京中元关怀科技发展有限公司
  ADX_AUTO_SHUN_FEI_DSP = 165; //广州舜飞信息科技有限公司
  ADX_GT_ASIA_LIMITED = 166; // GT_ASIA_LIMITED
  ADX_HUICHUAN_DSP = 167;// 汇川-海量
  ADX_SHUN_FEI_BRAND_DSP = 168; // 舜飞品牌
  ADX_BEI_YE_BRAND_DSP = 169; // 倍业品牌
  ADX_XIN_SHU = 170; // 新数
  ADX_HUICHUAN_NO_DYNAMIC = 171;// 阿里汇川非海量
  ADX_VIPSHOP_DSP_NEW_NO_DYNAMIC = 172;// 唯品会新dsp非海量
  ADX_FENGLAN_DSP = 173;// 枫岚互动海量
  ADX_2345_JUYOU = 174;//2345聚优
  ADX_HZ_AIDI_DSP = 175; // 杭州艾狄墨搏信息服务有限公司
  ADX_HN_XINWU_DSP = 176; // 海南鑫吾信息科技有限公司
  ADX_JS_LIANYUE_DSP = 177; // 江苏联阅网络技术有限公司
  ADX_HZ_QIZHENG_DSP = 178; // 杭州启筝网络科技有限公司
  ADX_HUAXIA_LEYOU = 179; // 华夏乐游
  ADX_SHANGSHI_WUXIAN = 180; // 尚世无限
  ADX_DIANSI_DSP = 181; // 点思-非海量
  ADX_YOULAI_DSP = 182; // 友莱
  ADX_QISHU_DSP = 183; // 七数
  ADX_SHIDAI_YILIAN_DSP = 184; // 时代亿联
  ADX_DOMOB_BRAND_DSP = 185; // 多盟品牌
  ADX_HUIXING_RUIHE = 186;//辉兴瑞和
  ADX_ZHONGLIAN_SHIJI = 187;//众联世纪
  ADX_BAIXUN_XINXI = 188;//百寻信息科技(上海)
  ADX_YUELIAN = 189;//联阅（非海量）
  ADX_YUEKE = 190;//阅客
  ADX_BUSI = 191;//卜思
  ADX_LANZHI = 192;//蓝智
  ADX_BRAND_STRESS_TEST1_DSP = 193; // 品牌压测使用DSP
  ADX_YILING = 194;//屹凌
  ADX_PDD_MUT = 195;//拼多多搜索海量
  ADX_WANGMAI_MUT = 196; // 旺脉海量
  ADX_AUTO_SHANGYOU_MUT = 197; // 天津尚友海量
  ADX_AI_PIN_KE = 198;//爱拼客
  ADX_WEI_LI = 199;//微鲤
  ADX_DOMOB_DYNAMIC = 200;//多盟海量
  ADX_IQIYI_DYNAMIC = 201;//爱奇艺海量
  ADX_360_UNI = 202;//360联盟
  ADX_QIANJIN_DYNAMIC = 203;// 钱进科技海量
  ADX_HUANXIAO = 204;// 欢效网络
  ADX_XIAMEN_ZHONGLIAN = 205;// 厦门众联流量果
  ADX_HUANTAI = 206;// 深圳欢太科技
  ADX_WENQI_RUIZE = 207;//上海文绮睿择
  ADX_KE_JIE = 208;//四川客捷网络
  ADX_EMARK_BRAND_DSP = 209;//emark品牌
  ADX_XIAOLAN_DYNAMIC = 210;// 小蓝企鹅海量
  ADX_XIAOMI_DYNAMIC = 211;// 深圳小米海量
  ADX_JIEWA_DYNAMIC = 212;// 上海杰蛙网络海量
  ADX_MEISHU_DYNAMIC = 213;// 上海美数海量
  ADX_DIANLIANG = 214;//点量(上海)信息科技有限公司
  ADX_ZHONGYUAN_GUANHUAI_NEW = 215;//中元关怀科技（新）
  ADX_IFLYTEK_DSP_NEW = 216;//科大讯飞（新）
  ADX_HUANXIAO_NO_DYNAMIC = 217;//欢效网络（非海量）
  ADX_QIYI = 218;//祺弈网络
  ADX_XIAORAN = 219;//笑然信息科技
  ADX_SAIDIAN_JULIANG = 220;//赛点聚量网络科技
  ADX_SHANG_YOU_BRAND_DSP = 221;//天津尚友世纪科技有限公司品牌
  ADX_XINGMENG_WENHUA_DSP = 222;//星梦文化传媒有限公司
  ADX_LANJING_DSP = 223;//上海揽晶网络科技
  ADX_ZHANGSHANG_FENGXING_DSP = 224;//北京掌上风行科技有限公司
  ADX_LIAOYUAN_ZHIHUO_DSP = 225;//燎原之火科技有限公司
  ADX_WANCHONGSHAN_DSP = 226;//北京万重山科技有限公司
  ADX_BEIZI_DSP = 227;//上海倍孜网络技术有限公司
  ADX_SHUZIYUEDONG_NEW_DSP = 228;//数字悦动新主体
  ADX_YOU_JING_BRAND_DSP = 229;//北京有境网络科技有限公司品牌
  ADX_MINGRUI_XINXI_DSP = 230;//明睿信息技术
  ADX_HAILIANG_DYNAMIC_DSP = 231;//北京嗨量科技有限公司
  ADX_MUOGUO_DYNAMIC_DSP = 232;//上海墨果网络科技有限公司
  ADX_KEJIE_DYNAMIC_DSP = 233;//四川客捷网络科技有限公司
  ADX_SHANGSHI_WUXIAN_DYNAMIC_DSP = 234;//北京尚世无限科技有限责任公司
  ADX_LIANYAN_DYNAMIC_DSP = 235;//上海连妍网络技术有限公司
  ADX_JIATOU_DYNAMIC_DSP = 236;//上海佳投互联网技术集团有限公司
  ADX_IFLYTEK_DYNAMIC_DSP = 237;//科大讯飞（上海）科技有限公司
  ADX_WANG_MAI_BRAND_DSP = 238;//旺脉品牌
  ADX_AUTO_SHUN_FEI_202502_DSP = 239;//广州舜飞信息科技有限公司（20250227 切换代理商，新建 dsp id）
  ADX_TENCENT_DYNAMIC_DSP = 240;//腾讯科技(深圳)有限公司：主站或者联盟有跑过，客户想要生成新的DSP_ID
  ADX_YOULIANGHUI_DYNAMIC_DSP = 241;//【优量汇】深圳市腾讯计算机系统有限公司
  ADX_ADMATE_BRAND_DSP = 242;//北京美数信息科技有限公司品牌
  ADX_BAIDU_CORP_DYNAMIC_DSP = 243;//百度时代网络技术（北京）有限公司 直投
  ADX_WEI_LI_DYNAMIC_DSP = 244;//北京微鲤科技有限公司
  ADX_JS_LIANYUE_DYNAMIC_DSP = 245; // 江苏联阅网络技术有限公司
  ADX_LANJING_DYNAMIC_DSP = 246;//上海揽晶网络科技
  ADX_YINHUIXINGMENG_DYNAMIC_DSP = 247;//武汉银辉星梦文化传媒有限公司
  ADX_DOUJING_DYNAMIC_DSP = 248; // 深圳市斗鲸网络科技有限公司
  ADX_YUNDONG_DYNAMIC_DSP = 249;//云洞(上海)科技股份有限公司
  ADX_VLION_BRAND_DSP = 250;//上海瑞狮网络科技有限公司品牌
  ADX_ALI_JULANG_BRAND_DSP = 251;//淘宝（中国）软件有限公司品牌-阿里巨浪
  ADX_SUNYUE_DYNAMIC_DSP = 252;//杭州旬阅科技有限公司 海量
  ADX_LANYU_DYNAMIC_DSP = 253;//北京澜宇科技有限公司 海量
  ADX_ZHONGCHENGJIASHI_DYNAMIC_DSP = 254;//湖北众宸嘉实信息科技有限公司 海量
  ADX_QINGSHAN_DYNAMIC_DSP = 255;//青山信息技术开发（深圳）有限公司 海量
  ADX_XINGDU_DYNAMIC_DSP = 256;//厦门星读科技有限公司 海量
  ADX_JINGTANGSHENGSHI_DYNAMIC_DSP = 257;//北京景唐盛世科技有限公司 海量
  ADX_SHENYANZHINENG_DYNAMIC_DSP = 258;//北京深演智能科技股份有限公司 海量
  ADX_YOUTUI_BRAND_DSP = 259;//北京乐游阳光科技有限公司品牌
  ADX_FANGGE_DYNAMIC_DSP = 260;//上海方歌网络科技有限公司 海量
  ADX_FENGTENG_DYNAMIC_DSP = 261;//广州风腾网络科技有限公司 海量
  ADX_LETUI_DYNAMIC_DSP = 262;//乐推（上海）文化传播有限公司 海量
  ADX_YOUSHI_DSP = 263;//优视科技（中国）有限公司 非海量
  ADX_BANTANGQUBING_DSP = 264;//半糖去冰科技（北京）有限公司 非海量
  ADX_AIDIANXIAO_DSP = 265;//霍尔果斯爱点效信息技术有限公司 非海量
  ADX_MAI_SHOU_BRAND_DSP = 266;//上海麦收文化传播有限公司品牌
  ADX_MIE_JI_BRAND_DSP = 267;//上海咩叽科网络科技有限公司品牌
  ADX_SOUL_BRAND_DSP = 268;//上海任意门科技有限公司品牌
  ADX_DISHANGTI_DYNAMIC_DSP = 269;//北京低熵体科技有限公司 海量
  ADX_HUANTAI_DYNAMIC_DSP = 270;//深圳市欢太科技有限公司 海量
  ADX_JINGTANGSHENGSHI_DYNAMIC_2_DSP = 271;//北京景唐盛世科技有限公司 海量
  ADX_XIAORAN_DYNAMIC_DSP = 272;//北京笑然信息科技有限公司 海量
  ADX_YIDIAN_DYNAMIC_DSP = 273;//北京亿典科技有限公司 海量
  ADX_ZHONGYUANGUANHUAI_DYNAMIC_DSP = 274;//北京中元关怀科技发展有限公司 海量
  ADX_DEWU_DSP = 275;//上海得物信息集团有限公司
  ADX_HUOYANYAN_DYNAMIC_DSP = 276;//海南火炎焱科技有限公司 海量
  ADX_YINHUIXINGMENG_DSP = 277;//武汉银辉星梦文化传媒有限公司 非海量
  ADX_ZHANGSHANGLEYOU_DYNAMIC_DSP = 278;//北京掌上乐游科技有限公司 海量
  ADX_TENCENT_DSP = 279;//腾讯科技（深圳）有限公司 非海量
  ADX_JING_TANG_SHENG_SHI_BRAND_DSP = 280;//北京景唐盛世科技有限公司品牌
  ADX_IQIYI_DYNAMIC_2_DSP = 281;//北京爱奇艺科技有限公司 海量
  ADX_YUESHENG_DYNAMIC_DSP = 282;//上海跃胜网络科技有限公司 海量
  ADX_JUWANG_DYNAMIC_DSP = 283;//杭州炬望网络科技有限公司 海量
  ADX_TAKU_DYNAMIC_DSP = 284;//广州塔酷信息科技有限公司 海量
  ADX_BANTANGQUBING_2_DSP = 285;//半糖去冰科技（北京）有限公司 非海量
  ADX_SI_YI_NIAO_BRAND_DSP = 286;//北京四翼鸟科技有限公司品牌
  ADX_XIAO_RAN_BRAND_DSP = 287;//北京笑然信息科技有限公司品牌
  ADX_YI_DIAN_BRAND_DSP = 288;//北京亿典科技有限公司品牌
  ADX_MOSKEN_BRAND_DSP = 289;//北京数优之行科技股份有限公司品牌
  ADX_YOU_SHI_BRAND_DSP = 290;//优视科技（中国）有限公司品牌
  ADX_MOWEN_DYNAMIC_DSP = 291;//北京墨问科技有限公司 海量
  ADX_SIQING_DYNAMIC_DSP = 292;//北京思擎网络科技有限公司 海量
  ADX_YISU_DYNAMIC_DSP = 293;//北京宜苏科技有限公司 海量
  ADX_JINDAI_DYNAMIC_DSP = 294;// 广州金袋信息技术有限公司 海量
  ADX_MANGO_DYNAMIC_DSP = 295;// 芒果互动传媒（深圳）有限公司 海量
  ADX_RUILINXI_DYNAMIC_DSP = 296;// 南京瑞麟熙信息科技有限公司 海量
  ADX_FANLIAN_DYNAMIC_DSP = 297;// 北京泛连科技有限公司 海量
  ADX_YOURONG_DYNAMIC_DSP = 298;// 广州有容数据网络有限公司 海量
  ADX_SIGEYEZI_DYNAMIC_DSP = 299;// 北京四个椰子信息技术有限公司 海量
  ADX_JINMO_DYNAMIC_DSP = 300;// 北京锦墨科技有限公司 海量
  ADX_WOHONG_DYNAMIC_DSP = 301;// 北京沃泓网络科技有限公司 海量
  ADX_HAINACHUNQIU_DYNAMIC_DSP = 302;// 北京海纳春秋科技有限公司 海量
  ADX_YOUKU_DYNAMIC_DSP = 303;// 北京优酷科技有限公司 海量
  ADX_MINGRUI_DYNAMIC_DSP = 304;// 上海明睿信息技术有限公司 海量
  ADX_YAOGUNHU_DYNAMIC_DSP = 305;// 厦门摇滚虎鲸科技有限公司 海量
  ADX_HUIHUANGMINGTIAN_DSP = 306;// 湖州辉煌明天科技有限公司
  ADX_SHENZHEN_XIAOMI_DYNAMIC_DSP = 307;// 深圳小米信息技术有限公司 海量
  ADX_YOUXIRUANJIAN_DYNAMIC_DSP = 308;// 深圳市悠熙软件科技有限公司 海量
  ADX_HANGZHOU_MENGFA_DSP = 309;// 杭州盟发信息科技有限公司 非海量
  ADX_SIQING_BRAND_DSP = 310;// 北京思擎网络科技有限公司 品牌广告
  ADX_YUNWU_DSP = 311;// 杭州云坞科技有限公司 非海量
  ADX_RANYAOWEILAI_DYNAMIC_DSP = 312;// 上海燃耀未来网络科技有限公司 海量
  ADX_YULINZAIXIAN_DYNAMIC_DSP = 313;// 成都钰林在线科技有限公司 海量
  ADX_ZHIHEWENHUA_DYNAMIC_DSP = 314;// 上海智合文化传播有限公司 海量
  ADX_YINGSHIWANGLUO_DYNAMIC_DSP = 315;// 杭州萤石网络股份有限公司 海量
  ADX_WANGYIYOUXI_BRAND_DSP = 316;// 网易游戏 品牌广告
  ADX_YUQIANKEJI_DYNAMIC_DSP = 317;// 北京驭骞科技有限责任公司 海量
  ADX_HAINACHUNQIU_ZHUZHAN_DYNAMIC_DSP = 318;// 北京海纳春秋科技有限公司 海量
  ADX_HANGZHOUCHENXI_DSP = 319;// 杭州晨夕信息技术有限公司 非海量
  ADX_ZHIMEILIANHE_DYNAMIC_DSP = 320;// 上海智媒连合科技有限公司 海量
  ADX_AIDEEN_DYNAMIC_DSP = 321;// 上海艾得蒽数字科技有限公司 海量
  ADX_QIDONGXINGLIAN_DYNAMIC_DSP = 322;// 重庆企动星联科技有限公司 海量
  ADX_YINGHECHUANGSHI_DYNAMIC_DSP = 323;// 北京硬核创世科技有限公司 海量
}

enum RtaSourceType {
  UNKNOWN_RTA_SOURCE_TYPE = 0;
  RTA_UMONEY = 1; // 百度有钱花rta
  RTA_TAOBAO = 2; // 淘宝rta
  RTA_ALIPAY = 3; // 支付宝rta
  RTA_360JIE = 4; // 360借条rta
  RTA_BAIDU = 5; // 手机百度rta
  RTA_360JIE_EAST = 6; // 360借条(华东)rta
  RTA_QUKAN = 7; // 趣头条
  RTA_WANKA = 8; // 玖富万卡
  RTA_JD_JR = 9; // 京东金融
  RTA_WETEC = 10; // 致维
  RTA_IWANVI = 11; // 中文万维
  RTA_WESURE = 12; // 腾讯微保
  RTA_HUANBEI = 13; // 还呗
  RTA_DIDI = 14; // 滴滴出行
  RTA_TANTAN = 15; // 探探
  RTA_BUDUODUO = 16; // 步多多
  RTA_OCTOPUS_IME = 17; // 章鱼输入法
  RTA_AUTO_NAVI = 18; // 高德
  RTA_KANIU = 19; // 卡牛
  RTA_360INSURANCE = 20; // 360保险
  RTA_UC = 21; // uc
  RTA_1688 = 22; // 1688
  RTA_RAISE_PIG = 23; // 阳光养猪场
  RTA_IQIYI = 24; // 爱奇艺
  RTA_37 = 25; // 三七互娱
  RTA_ZHUANZHUAN = 26; // 转转
  RTA_ACFUN = 27; // A站
  RTA_CRAZY_CCY = 28; // 疯狂猜成语
  RTA_KUAISHOU = 29;  //快手主app
  RTA_KUAISHOU_INCUBATOR = 30;  //快手内容孵化部
  RTA_HUANBEI_NEW = 31; // 还呗新接口
  RTA_QQ_LIVE = 32; // 腾讯视频
  RTA_ALIBABA_DONGFENG = 33; //阿里东风
  RTA_ALIBABA_JULANG = 34; //阿里巨浪
  RTA_PAIPAIDAI = 35; //拍拍贷
  RTA_BAIDU_JINRONG = 36; //百度金融
  RTA_VIPSHOP = 37; // 唯品会
  RTA_WEIBAO = 38; // 微保
  RTA_JD_MUMEI = 39; //京东钼媒
  RTA_PPDAI = 40; //拍拍贷,废弃，使用前面那个拍拍贷
  RTA_QQ_BROWSER = 41;  //腾讯浏览器
  RTA_QU_JIE_QIAN = 42; //趣借钱
  RTA_PINDUODUO = 43; //拼多多
  RTA_MEITUAN_ZHUZHAN = 44; //美团主站
  RTA_YI_TIAN = 45; //一甜相机
  RTA_YI_DUI = 46; //伊对
  RTA_ZUOYEBNAG = 47; //作业帮
  RTA_SEVEN_CAT = 48; //七猫小说
  RTA_DOMOB_DSP = 49; //多盟广告
  RTA_58 = 50; //58同城
  RTA_CHU_BAO = 51; //触宝
  RTA_TECENT_MAP = 52; //腾讯地图
  RTA_RED_BOOK = 53; //小红书
  RTA_SOUL = 54; //soul
  RTA_TU_HU = 55; //途虎养车
  RTA_XIMALAYA = 56; //喜马拉雅
  RTA_TT = 57; //TT语音
  RTA_YOUKU = 58; //优酷
  RTA_WEIBO = 59; //微博
  RTA_KUGOU = 60; //酷狗
  RTA_ZHONG_AN = 61; //众安保险
  RTA_PDD_ZHUZHAN = 62; //拼多多主站
  BRAND_RTA_TAOBAO = 63; // 品牌手淘
  RTA_SUNING = 64; // 苏宁
  RTA_ANJUKE = 65; // 安居客
  RTA_WEBEYE = 66; // 网眼
  RTA_LIANXIN = 67; //连信
  RTA_YICHE = 68; //易车
  RTA_TENCENT_WEISHI = 69; //腾讯微视
  RTA_TENCENT_MUSIC = 70; //QQ音乐
  RTA_JD = 71; // 京东
  BRAND_RTA_ZHIHU = 72; // 品牌知乎
  RTA_BIXIN = 73; //比心
  RTA_ZHIHU = 74; // 知乎
  RTA_TUIA = 75; // 推啊
  RTA_XINYU = 76; // 心遇
  RTA_PINGAN = 77; // 平安集团
  RTA_HUI_SEN = 78; // 回森
  RTA_SHUI_DI = 79; // 水滴保
  RTA_MEITUAN_WAIMAI = 80; // 美团外卖
  RTA_QUNAR = 81; // 去哪儿
  RTA_BOSS = 82; // BOSS
  RTA_YOUKU_LAXIN = 83; //优酷拉新
  RTA_233 = 84; //233乐园
  RTA_AUTO_CAR = 85; // 高德打车
  RTA_MEIRUAN_YOUXUAN = 86; // 美团优选
  RTA_BAIDU_MAP = 87; // 百度地图
  RTA_TENCENT_PIANDUODUO = 88; // 腾讯片多多
  RTA_JD_FINANCE = 89; // 京东金融
  RTA_BILIBILI = 90; // bilibili(B站)
  RTA_UMENG = 91; // 友盟
  RTA_TECENT_GAME_DSP = 92; // 腾讯游戏
  RTA_SHUIMOO = 93; // 水母
  RTA_NETEASE_MUSIC = 94; //网易云音乐
  RTA_WE_SING = 95; // 全民K歌
  RTA_TANTAN_LAXIN = 96; // 探探拉新
  RTA_DEWU = 97; // 得物
  RTA_SHENGBEI = 98; // 省呗
  RTA_MOMO = 99; // 陌陌
  RTA_CESHI = 100;  // 100测试占用
  RTA_YY_LIVE = 101;//YY直播
  RTA_XINYU_LAXIN = 102; // 心遇-拉新
  RTA_KUAIYING = 103; // 快影
  RTA_XIAOMI_SHANGDIAN = 104; // 小米商店
  RTA_WEIPAI = 105; // 微派
  RTA_ANYIHUA = 106; // 安逸花
  BRAND_RTA_DUXIAOMAN = 107;  // 品牌广告度小满
  RTA_TAP_TAP = 108; // TapTap
  RTA_GUAZI = 109; // 瓜子二手车
  RTA_GANJI = 110; // 赶集网
  RTA_JD_JINGXI = 111; // 京东-京喜
  RTA_MEITUAN_DIANPING = 112; // 美团-大众点评
  RTA_E_COMMERCE_BANK = 113; // 网商银行
  RTA_ALI_HUICHUAN = 114;  // 阿里汇川
  RTA_TUYOU = 115;  // 途游
  RTA_T3_CHUXING = 116;  // T3出行
  RTA_YUANBAO = 117;  // 元宝
  RTA_DOYU = 118;  // 斗鱼
  RTA_ALIPAY_NEW = 119; // 支付宝rta（新）
  RTA_BAISHENG = 120; // 百胜（肯德基）
  RTA_MEITUAN_DIANPING_LAXIN = 121; // 美团-大众点评-拉新
  RTA_BRAND_NEQUAL = 122; //品牌 恩亿科
  RTA_SUOYIN_TECH = 123; //索引科技
  RTA_XIE_CHENG = 124; // 携程
  RTA_MIHOYO = 125; // 米哈游
  RTA_MENGYUE = 126; // 蒙悦科技
  RTA_LINGMAO = 127; // 灵猫
  RTA_QINIU = 128; // 启牛
  RTA_FANQIE_NOVEL = 129; // 番茄小说
  RTA_TUHU = 130; // 途虎
  RTA_HUI_SEN_UNI = 131; // 回森联盟专用
  RTA_YUN_QI = 132; // 广州云琪
  RTA_BAOBAO_KAN_JIA = 133; //  宝宝砍价
  RTA_QUDIAN_READ = 134; // 趣点阅读
  RTA_HUI_XUAN = 135; // 回旋
  RTA_JI_RONG = 136; // 极融
  RTA_YANG_QIAN_GUAN = 137; // 洋钱罐
  RTA_SHOU_TAO = 138; // 废弃，手淘
  RTA_TAOBAO_APP = 139; // 手淘
  RTA_SOUL_UNIVERSE = 140; // Soul拆分, for 主站流量
  RTA_TENCENT_MINI_PROGRAM = 141; // 腾讯微信小游戏，数据服务提供客户 非正常RTA客户
  RTA_TAOBAO_UNION = 142; // 淘宝联盟
  RTA_VIVO_SHOP = 143; // vivo商店
  RTA_233_GAME = 144; // 233游戏
  RTA_JD_LAXIN = 145; // 京东拉新
  RTA_KEEP = 146; // keep
  RTA_ZHONG_YUAN = 147; // 中原消费金融
  RTA_WLD_MARKET = 148; // 微粒贷
  RTA_RONG_360 = 149; // 融360
  RTA_YUAN_BAO = 150; // 元保
  RTA_YUN_MAN_MAN = 151; // 运满满
  RTA_ZHI_LIAN = 152; // 智联招聘
  RTA_QI_NIU = 153; // 启牛学堂
  RTA_BLACK_CARD = 154; // 重庆市黑卡小额贷-好分期
  RTA_TA_QU = 155; // 他趣
  RTA_XING_YE = 156; // 星野
  RTA_JIANGZHENXUETANG = 157; //讲真学堂
  RTA_SOUL_NEW = 158; // soul新ID 上海任意门科技有限公司
  RTA_BAIDU_BCE = 159; // 百度爱采购
  RTA_TONGCHENG = 160; // 同程
  RTA_BEIKE = 161; // 贝壳
  RTA_DINGDONG_MAICAI = 162; // 叮咚买菜
  RTA_SOHU_TV = 163; // 搜狐视频
  RTA_XIAMEN_XIAOWAI = 164; // 厦门小歪
  RTA_XIAMEN_KUAIFU = 165; // 厦门快付
  RTA_MOU_DING_TIAN_XIA = 166; // 谋定天下
  RTA_OPPO = 167; // oppo
  RTA_ZUOYEBANG = 168; // 作业帮
  RTA_ALI_JIANKANG = 169; // 阿里健康
  RTA_QIANSHOU = 170; // 牵手
  RTA_HUAKU = 171; // 酷划
  RTA_NETEASE_GAME = 172;  // 网易游戏
  RTA_NANJING_XINWANG = 173; // 南京欣网
  RTA_YOUSHI = 174; // 优视科技（中国）科技有限公司
  RTA_LILISI = 175; // 莉莉丝
  RTA_YIXIANGHUA = 176; // 宜享花
  RTA_SOUL_BRAND = 177; // soul品牌
  RTA_ALI_ZHIZUAN = 178; // 阿里智钻
  RTA_HUAWEI_SHOP = 179; // 华为商店
  RTA_HONOR_SHOP = 180; // 荣耀商店
  RTA_SAIDIANJULIANG = 181; // 赛点聚量
  RTA_XINYONGFEI = 182; // 信用飞
  RTA_BILIBILI_2 = 183; // 哔哩哔哩
  RTA_FENQILE = 184; // 分期乐
  RTA_CILIJINNIU = 185; // 磁力金牛
  RTA_YIDIANTIANXIA = 186; // 易点天下
  RTA_YOUYOUQIUSHUI = 187; // 优优秋水
  RTA_GUANGTUIKEJI = 188; // 广推科技
  RTA_LELONGKEJI = 189; // 乐龙科技
  RTA_XINSHENGXIN = 190; // 新盛鑫
  RTA_HAIBO = 191; // 嗨播
  RTA_GUANGZHOUHULIAN = 192; // 广州互联
  RTA_LITIAN = 193; // 力天
  RTA_JUNBO = 194; // 骏伯
  RTA_YUANFUDAO = 195; // 猿辅导
  RTA_CILIJINNIU_2 = 196; // 磁力金牛2
  RTA_JISU = 197; // 极速
}


// 详情页广告类型
enum ItemAdSourceType {
  UNKNOWN_ITEM_SOURCE_TYPE = 0;
  BETWEEN_COMMENTS = 1; // 评论区广告
  ABOVE_COMMENTS = 2; // 评论区上方广告
  PLAY_END = 3; // 视频播放结束贴片广告
}

// 转化类型
enum AdConversionType {
  UNKNOWN_CONVERSION_TYPE = 0;
  APP_DOWNLOAD = 1;
  OPEN_HTML5 = 2;
  OPEN_HTML5_JS_TAOBAO = 3;
  APP_ADVANCE = 4;
  CONVERSION_TOPIC = 5; // 话题标签页
  CONVERSION_OPEN_LIVE = 6; // 直播间
  CONVERSION_OPEN_PROFILE = 7; // 个人页
  FOLLOW = 8; // 点击关注 https://docs.corp.kuaishou.com/d/home/<USER>
}

//举报类型
enum ReportType {
  UNKNOWN_REPORT_TYPE = 0;
  FALSE = 1; //虚假广告
  SIMILAR = 2; //雷同广告
  CLICKBAIT = 3; //标题党
  BORING = 4; //内容乏味
  UNPLEASANT = 5; //内容反感

  PORN = 11; // 涉嫌低俗色情
  FRAUD = 12; // 涉及欺诈
  NOT_MATCH = 13; // 广告信息不实
  INFRINGEMENT = 14; // 涉嫌侵权
  DECOY_CLICK = 15; // 涉嫌诱骗点击
  INCONSISTENT_TITLES = 16; //涉嫌标题党
  FALSE_PROPAGANDA = 17; // 涉及虚假、夸大宣传
  NEGATIVE_ENERGY = 18; // 不良导向、价值观、负能量等
  CONTENT_UNCOMFORTABLE = 19; // 内容引人恶心、不适
  LACK_ANTI_ADDICTION = 20; // 游戏内无未成年人防沉迷限制
  OTHER = 100; // 其他
}

enum ItemShowType {
  UNKNOWN_SHOW_TYPE = 0;
  CLICK_SHOW = 1; // 点击进入
  LEFT_PULL_SHOW = 2; // 左滑进入
  RIGHT_PULL_SHOW = 3; // 右滑进入
  UP_SLIDE_SHOW = 4; // 上滑进入
  DOWN_SLIDE_SHOW = 5; // 下滑进入
}

enum ItemCloseType {
  UNKNOWN_CLOSE_TYPE = 0;
  CLICK_CLOSE = 1; // 点击关闭
  LEFT_PULL_CLOSE = 2; // 左滑关闭
  RIGHT_PULL_CLOSE = 3; // 右滑关闭
  UP_SLIDE_CLOSE = 4; // 上滑关闭
  DOWN_SLIDE_CLOSE = 5; // 下滑关闭
  PLAYEDN_CLICK_CLOSE = 6; // 点击播放结束页点击按钮关闭
  POPUP_WINDOW_CLICK_CLOSE = 7; // 点击弹窗中btn，按钮继续观看、继续做任务
  PLAYEND_CLOSE = 8; // 直播挂件-播放完成关闭退出

  WEAK_PATCH_AD_CLOSE = 9 ; // 弱样式关闭按钮
  STRONG_PATCH_AD_CLOSE = 10 ; // 强样式关闭按钮
  RETURN_KEY_CLICK_CLOSE = 11; // 返回键点击关闭
  BLANK_AREA_CLICK_CLOSE = 12; // 点击弹窗外，置灰区域
  BOTTOM_ICON_CLOSE = 13; // 详情页右下角转化链接icon点击关闭
  OVERTIME_AUTOMATIC_CLOSE = 14; //超过规定时长后自动关闭
  // 20210512 add
  POPUP_WINDOW_CLICK_CLOSE_CHANGE_PHOTO = 15; // 点击弹窗中btn，换一个视频
  //20210618 add
  AUTO_FINISH_SHOW = 16; //播放完成自动离开
  //20220630
  JOIN_VIP_CLOSE = 17 ;//开通会员关闭
  SWITCH_VEDIO_CLOSE = 18 ;//切换剧集时关闭
}

enum CardCloseType {
  UNKNOWN_CARD_CLOSE_TYPE = 0;
  CARD_CLICK_CLOSE = 1; // 点击卡片关闭按钮关闭
  CARD_AUTO_CLOSE = 2; // 卡片自动关闭
  CARD_INTERACTIVE_SUCCESS =3 ;//卡片互动成功关闭
  CARD_INTERACTIVE_FAILED = 4 ;//卡片互动失败关闭
}

enum ShareChannel {
  UNKNOWN_SHARE_CHANNEL = 0;
  SMS_FRIENDS = 1; // 私信好友
  WECHAT_MOMENTS = 2; // 微信朋友圈
  WECHAT_FRIENDS = 3; // 微信好友
  QQ_FRIENDS = 4; // QQ好友
  QQ_QZONE = 5; // QQ空间
  SINA_WEIBO = 6; // 新浪微博
  FACEBOOK = 7; // facebook
  INSTAGRAM = 8; // instagram
  YOUTUBE = 9; // youbute
  SHUOSHUO = 10; // 说说
}

enum CommentDialogExpandType {
  UNKNOWN_EXPAND_TYPE = 0;
  BUTTOM_COMMENT_ICON = 1; // 底部评论图标按钮点击
  ROLLING_HOT_COMMENT = 2; // 滚动热评点击
}

// 广告位场景
enum AdPosition {
  UNKNOWN_AD_POSITION = 0;
  FEED_FLOW = 1; // 信息流
  COMMENT_AREA = 2; // 评论区
  COMMENT_TOP = 3; // 评论区顶部
  VIDEO_END = 4; // 后贴片
  SPLASH = 5; // 开屏
  MERCHANT_FEED = 6; // 小店买家首页
}

enum DisplayType {
  UNKNOWN_DISPLAY_TYPE = 0;
  DISPLAY_DYNAMIC_BAR_GREEN = 1; // 动效色条（绿）, 需要字段 title/url
  DISPLAY_DYNAMIC_BAR_BLUE = 2; // 动效色条（蓝）, 需要字段 title/url
  DISPLAY_FLOATING_LINK = 3; // 文字链浮层, 需要字段 title/url
  DISPLAY_FLOATING_IMAGE = 4; // 小图浮层, 需要字段 title/url/imageUrl
  DISPLAY_BUTTON_IN_CAPTION = 5; // 作品介绍中的文字+按钮，需要字段 title/url
  DISPLAY_EMBEDDED = 6; // 新详情页内嵌样式
  DISPLAY_FANS_TOP = 7; // 粉丝头条

  URL_FEED = 10; // 使用第三方url作为feed的播放url
  DISPLAY_DYNAMIC_BAR_NONE = 11; // 透明actionBar
  ICON_DESC_TITLE_LINK = 12; // 推广弱样式
  AD_TOPIC = 13; // 广告话题活动推广展示样式

  DYNAMIC_BAR_YELLOW = 100; // 动效色条（黄-参阅电商MRD）, 需要字段 title/url
  ITEM_CELL_TITLE_PRICE = 101; // 电商作品样式：标题+描述+按钮，商品title和商品描述，需要字段 title/url/merchantTitle/merchantDesc
  ITEM_CELL_PRICE = 102; // 电商作品样式：描述+按钮，商品描述，需要字段 title/url/merchantDesc
  ITEM_LOOK_IN_CAPTION = 103;// 电商作品样式 作品介绍中的文字+电商按钮1 需要字段 title/url
  ITEM_BUTTON_YELLOW_IN_CAPTION = 104;// 电商作品样式 作品介绍中的文字+电商按钮2带颜色 需要字段 title/url
  ITEM_LINK_IN_CAPTION = 105;// 电商作品样式 作品介绍中的文字+电商按钮3 需要字段 title/url
  ITEM_UP_LINK = 106;// 电商作品样式 位置+标题+电商按钮 需要字段 title/url/merchantTitle
  ITEM_UP_LOOK = 107;// 电商作品样式 位置+标题+电商按钮 需要字段 title/url/merchantTitle
  ITEM_DOWN_LINK = 108;// 电商作品样式 位置+标题+电商按钮 需要字段 title/url/merchantTitle
  ITEM_DOWN_LOOK = 109;// 电商作品样式 位置+标题+电商按钮 需要字段 title/url/merchantTitle
}

// 参考component下ad_base.proto中的TopicType
enum TemplateType {
  UNKNOWN_TEMPLATE_TYPE = 0; // 未知
  TEMPLATE_TOPIC = 1; // 话题页
  TEMPLATE_H5 = 2; // H5页
}

enum EvocationType {
  UNKNOWN_EVOCATION_TYPE = 0;
  APP_LINK_EVOCATION = 1; // applink唤醒
  H5_EVOCATION = 2; // H5页面跳转唤醒
}

// 开屏失败原因
enum SplashFailType {
  UNKNOWN_FAIL_TYPE = 0;
  NO_CACHE_FAIL_TYPE = 1; // 素材未缓存
  PLAYER_FAIL_TYPE = 2; // 播放器失败
}

// 已废弃
enum Module {
  UNKNOWN_MODULE = 0;
  PHOTO_PICTURE = 1; // 快享:快享广告图片处点击
  PHOTO_BUTTON = 2; // 快享:快享广告图片处按钮点击
  PHOTO_BAR = 3; // 快享:快享广告收起后转化条处点击
  SPLASH_ACTION_BAR = 4; // 开屏action bar
  SPLASH_JUMP_OVER = 5; // 开屏跳过按钮
  COVER_ACTION_BAR = 6; // 封面的action bar

}
//直营电商卡片状态，视频为竖屏模式下点击购物车时有效
enum EcomCardState {
  UNKNOWN_STATE = 0;
  EXPAND = 1; // 展开
  HIDE = 2; // 隐藏
}

enum PhotoSizeStyle {
  UNKNOWN_SIZE = 0;
  HORIZONTAL_SCREEN = 1; // 横屏
  VERTICAL_SCREEN = 2; // 竖屏
}

// 负反馈来源动作
enum AdPhotoNegativeSource {
  UNKNOWN_NEGATIVE_SOURCE_TYPE = 0;
  NEGATIVE_CLICK_ACTION = 1; // 点击上报负反馈
  NEGATIVE_LONG_PRESS_ACTION = 2; // 长安上报负反馈
  NEGATIVE_LONG_PRESS_ITEM = 3;// 视频-长按
  NEGATIVE_CLICK_ACTION_ITEM = 4;//视频-点击左上方按钮
  NEGATIVE_CLICK_SHARE_ITEM = 5;//视频-点击分享
  NEGATIVE_CLICK_ACTION_LANDINGPAGE = 6;//广告落地页举报
  NEGATIVE_REPORT_CLICK_ACTION = 7;//封面举报提交
  NEGATIVE_REPORT_CLICK_ITEM = 8;//视频举报提交
  NEGATIVE_SHOW_BOTTOM_LEAD = 9 ;//视频-底导栏
  NEGATIVE_CLICK_DISLIKE_ITEM = 10 ;//视频--点击不喜欢按钮
}

// 负反馈区分点击上下文场景
enum AdNegativeType {
  UNKNOWN_NEGATIVE_TYPE = 0;
  FEED_UNINTERESTED = 1; // feed 不感兴趣
  UNINTERESTED_FOR_LOW_QUALITY = 2; // 质量差
  UNINTERESTED_FOR_REPEAT = 3; // 推荐重复
  UNINTERESTED_FOR_CHEAT_CLICK = 4; // 标题党，骗点击
  SET_INTERESTED_AD = 5; // 设置感兴趣的广告
  SHIELD = 6; // 视频内屏蔽
  SHIELD_FOR_APP = 7; // 不喜欢:镇魔曲手游(app名字)
  SHIELD_FOR_INDUSTRY = 8; // 不喜欢:策略游戏类广告(二级行业)
  COMPLAIN_THIS_AD = 9; // 举报此广告
  PLAYING_UNINTERESTED = 10; // playing 不感兴趣
  UNINTERESTED_LEVEL1_LAYER = 11; //不感兴趣一级页面-浮层
  UNINTERESTED_LEVEL1_DIALOG = 12;//不感兴趣一级页面-弹框	
  PHOTO_UNINTERESTED = 13;//不喜欢该内容	
  REPORT_PAGE_LEVEL1_LAYER = 14;//举报一级页面-浮层	
  REPORT_PAGE_LEVEL1_DIALOG = 15;//举报一级页面-弹框	
  WHY_SEE_AD_LEVEL1_LAYER = 16;//为何看到此广告一级页面-浮层	
  WHY_SEE_AD_LEVEL1_DIALOG = 17;//为何看到此广告一级页面-弹框
  AD_UNFOLLOW = 18 ; //取消关注
  PHOTO_INTERESTED =19; //还不错
  AD_SLIDE =20  ;//视频滑走，移除广告视频
  AD_CANCEL = 21;//负反馈取消
  WANHE_CREATOR_SHARED = 22; //磁力万合创作者广告共享计划
  SHIELD_FOR_POSITION  = 23; //不喜欢该位置展示广告
  DISLIKE_CATEGORY = 24;//不喜欢该类目
  AD_DENSE = 25;//广告密集
  VERY_DENSE = 26;//非常密集
  LITTLE_DENSE = 27;//较为密集
  DUPLICATE_CATEGORY = 28;//同类目重复
  DUPLICATE_PRODUCT = 29;//同产品重复
  DUPLICATE_APP = 30;//同APP重复
  EXAGGERATION = 31;//虚假夸张
  OFFENSIVE = 32;//引人不适
  OBSCENE = 33;//色情低俗
  OTHER_NEGATIVE = 34;//其他
  DISLIKE_LIVE = 35; //不喜欢这个直播
  DISLIKE_AUTHOR = 36; //不喜欢这个作者
  UNRELATED = 37; //不相关
}

//正反馈区分上下文场景
enum AdPositiveType {
  UNKNOWN_AD_POSITIVE_TYPE = 0; //未知正反馈上下文类型
  LIKE_CATEGORYA = 1; //喜欢这个类目
  LIKE_LIVE = 2; //喜欢这个直播
  USEFUL_TO_ME = 3; //对我有用
  GOOD_QUALITY = 4; //内容质量好
  LIKE_AUTHOR = 5; //喜欢这个作者
  FEED_INTERESTED = 6; //感兴趣
}

//负反馈区分浮层的标题内容
enum AdNegativeQualitySource {
  UNKNOWN_NEGATIVE_QUALITY_SOURCE = 0;
  NEGATIVE_NOT_INTEREST_FOR_AD = 1; // 对这个广告不感兴趣
  NEGATIVE_NOT_INTEREST_FOR_INDUSTRY = 2; // 对这个行业不感兴趣
  NEGATIVE_CLICKBAIT = 3; // 标题党
}

//负反馈区分不感兴趣的类型
enum AdNegativeQualityType {
  UNKNOWN_NEGATIVE_QUALITY_TYPE = 0;
  NEGATIVE_LIKE = 1; // 是
  NEGATIVE_UNLIKE = 2; // 不是
  NEGATIVE_NOT_SURE = 3; // 不确定
}

// 开屏广告展示类型
enum SplashDisplayType {
  UNKNOWN_SPLASH_DISPLAY_TYPE = 0;
  SPLASH_TOP_VIEW = 1; // 开屏topview展示
  SPLASH_NORMAL_VIEW = 2; // 开屏会收回到feed展示
}

// 开屏广告展示素材类型
enum SplashMaterialDisplayType {
  UNKNOWN_SPLASH_MATERIAL_DISPLAY_TYPE = 0;
  VIDEO = 1; // 视频
  PICTURE = 2; // 图片
  BACKUP_PICTURE = 3; // 打底图片
}

// 开屏广告预加载素材类型，与素材展示不同，多了品牌互动广告开屏
enum SplashMaterialPreloadType {
  SPLASH_MATERIAL_PRELOAD_UNKNOWN = 0; // 未知类型
  SPLASH_MATERIAL_PRELOAD_VIDEO = 1; // 视频
  SPLASH_MATERIAL_PRELOAD_PICTURE = 2; // 图片
  SPLASH_MATERIAL_PRELOAD_BACKUP_PICTURE = 3; // 打底图片
  SPLASH_MATERIAL_PRELOAD_PLAYABLE_PICTURE = 4; // 互动广告图片
  SPLASH_MATERIAL_PRELOAD_PLAYABLE_VIDEO = 5; // 互动视频图片
}

// POI位置
enum PoiPosition {
  UNKNOWN_POI_POSITION = 0;
  COMMENT_TOP_AREA = 1; //评论区上方
  VIDEO_BOTTOM_LEFT = 2; //视屏左下角
}

// POI样式
enum PoiStyle {
  UNKNOWN_POI_STYLE = 0;
  WEEK = 1; //弱样式
  STRONG = 2; //强样式
}

// 区分事件来源
enum ActionSourceType {
  UNKNOWN_ACTION_SOURCE_TYPE = 0; // 目前native页面取值为该类型
  H5_ACTION = 1; // H5页面
}

// 事件触发的时机
enum EventTriggerType {
  UNKNOWN_TRIGGER_TYPE = 0;
  DOUBLE_CLICK_TRIGGER = 1;
  UP_SLIDE_TRIGGER = 2;
  DOWN_SLIDE_TRIGGER = 3;
  LEFT_SLIDE_TRIGGER = 4;
  RIGHT_SLIDE_TRIGGER = 5;
  CLICK_TRIGGER = 6; // 单击点击

  // 20200602 新增
  PLAYABLE_SLIDE_TRIGGER = 7; // 互动广告滑动点击
  //20220127 新增自动
  AUTO_TRIGER =8 ;//自动触发
}

// 直播间内部点击类型
enum AdLiveClickType {
  UNKNOWN_CLICK_TYPE = 0;
  COMMENT_AREA_CLICK = 1; //评论区去看看点击
  AD_SOCIAL_LIVE_BOTTOM_BELL_CLICK = 2; //快直播转化任务底部铃铛点击
  AD_SOCIAL_LIVE_REMIND_CARD_CLICK=3;  //提示卡片组件点击
  AD_SOCIAL_LIVE_COMMON_CARD_CLICK=4;  //聚星-星直播普通实体卡片
  AD_SOCIAL_LIVE_BOTTOM_BELL_AUTO_CLICK = 5; //快直播转化任务底部铃铛自动点击。和AD_SOCIAL_LIVE_BOTTOM_BELL_CLICK区分开，本次是客户端模拟点击
  AD_SOCIAL_LIVE_H5_BELL_CLICK = 6; //快直播h5页面小铃铛点击
  CONVERSION_GUIDE_BAR_CLICK =7;   //小铃铛强引导
  CONVERSION_MARQUEE_CLICK=8;//跑马灯
  CONVERSION_LABEL_TOP_CARD_CLICK=9; //讲解卡上方标签
  ENTER_LIVE_ROOM_POPUP_CLICK=10;//进入直播间弹窗点击
}

// 直播间内部曝光位置
enum AdLiveShowPosition {
  UNKNOWN_POSITION = 0;
  COMMENT_AREA_SHOW = 1; //评论区位置曝光
  AD_SOCIAL_LIVE_BOTTOM_BELL_SHOW = 2; //直播间底部曝光
  AD_SOCIAL_LIVE_REMIND_CARD_SHOW=3;  //新增提示卡片组件
  AD_SOCIAL_LIVE_COMMON_CARD_SHOW=4;  //聚星-星直播普通实体卡片
  CONVERSION_GUIDE_BAR_SHOW=5;//小铃铛强引导
  CONVERSION_MARQUEE_SHOW=6;//跑马灯
  CONVERSION_LABEL_TOP_CARD_SHOW=7; //讲解卡上方标签
  ENTER_LIVE_ROOM_POPUP_SHOW=8;//进入直播间弹窗曝光

}


// 联盟区分广告样式
enum AdStyleSub {
  UNKNOWN_SCREEN_VERSION = 0;
  SHU_SCREEN_SHU_VERSION = 1; // 竖屏竖版素材版式
  SHU_SCREEN_HENG_VERSION = 2; // 竖屏横版素材版式
  HENG_SCREEN_SHU_VERSION = 3; // 横屏竖版素材版式
  HENG_SCREEN_HENG_VERSION = 4; // 横屏横版素材版式
}

// 播放状态
enum InitVoiceStatus {
  UNKNOWN_INIT_VOICE_STATUS = 0 ; // 未知
  MUTE_STATUS = 1 ; // 静音状态
  OUTWARD_STATUS = 2 ; // 外放状态
}

// deeplink类型
enum DeeplinkType {
  UNKNOWN_DEEPLINK_TYPE = 0 ; // 未知
  NATIVE_ELEMENT_CLICK = 1 ; // 点击原生页面元素跳转
  H5_CLICK = 2 ;  // 点击H5跳转
  IN_STATION = 3 ;//跳转站内
  OUT_STATION = 4 ;//跳转站外
}

// H5卡片额外上报的 adAction 打点, 对应 ClientAdLog 中 client_h5_card_ext_data json 结构
message ClientCardTrickType {
  AdActionType action_type = 1; // H5卡片上报的 AdActionType
  ItemClickType item_click_type = 2; // H5卡片上报的用来覆盖 ClientAdLog 中的 ItemClickType
  ElementType element_type = 3; // H5卡片上报的用来覆盖 ClientParams 中的 ElementType
}
//参考https://docs.corp.kuaishou.com/k/home/<USER>/fcAAgOrSMx0BFNdtjhosmn8id
enum DiscardScene {
  UNKNOWN_DISCARD_SCENE = 0;
  FIRST_REQUEST_TOP2 = 1; // 首刷前两位
  MISS_FIXED_POSITION = 2; // 错过固定位置被丢弃
  DUP_PHOTO_ID = 3; //photo_id重复被丢弃
  NEGATIVE_ABANDON = 4;//负反馈丢弃
  KILL_CACHE = 5;//缓存被杀
  EYEMAX_REQUEST = 6;//与eyemax同刷被丢弃
  AD_EXPIRED = 7;//广告过期被丢弃
  SHIELD_NEXT_PHOTO = 8 ;//丢弃下一个作品（作者被封禁等case）
  WEAK_NETWORK = 9 ;//弱网
  INCORRECT_POS = 10 ;//针对强定坑述求的广告（引擎会下发期望位置），如果实际展示位置不符合要求，则丢弃不展示

}

// WebView容器-落地页类型
enum LandingPageWebViewType {
  UNKNOWN_LANDING_PAGE_WEB_VIEW_TYPE = 0 ;
  FRONT_LANDING_PAGE = 1 ; // 前置落地页
  NONFRONT_LANDING_PAGE = 2 ; // 非前置落地页
}

// 直播购物车点击来源
enum AdLiveShopClickType {
  UNKNOWN_AD_LIVE_SHOP_CLICK_TYPE = 0; // 未知来源
  YELLOW_SHOPPING_TROLLEY_CLICK = 1; // 点击“小黄车”按钮
  MORE_MERCHANT_ITEM_CLICK = 2; // 点击商品上架气泡的“更多商品”按钮
  GATHER_POPULAR_CLICK_TYPE = 3; // 点击聚人气商品
  NEO_MERCHANT_COUPON_CLICK = 4 ;//激励电商券的点击
}

// 直播商品列表样式
enum AdLiveItemImpressionType {
  UNKNOWN_AD_LIVE_ITEM_IMPRESSION_TYPE = 0; // 未知列表样式
  NORMAL_ITEM_LIST_TYPE = 1; // 普通列表样式
  BUBBLE_ITEM_LIST_TYPE = 2; // 气泡列表样式
  PENDANT_ITEM_LIST_TYPE = 3; // 挂件列表样式
  GATHER_POPULAR_LIST_TYPE = 4; // 聚人气样式列表
}

// 商品点击来源
enum AdLiveShopLinkJumpType {
  UNKNOWN_AD_LIVE_SHOP_LINK_JUMP_TYPE = 0; // 未知商品点击来源
  NORMAL_ITEM_LIST_JUMP_TYPE = 1; // 列表
  BUBBLE_ITEM_LIST_JUMP_TYPE = 2; // 气泡
  PENDANT_ITEM_LIST_JUMP_TYPE = 3; // 挂件
  COMMENT_AREA_JUMP_TYPE = 4; // 评论区
  SKU_HALF_SCREEN = 5; //电商SKU半屏
  GATHER_POPULAR_JUMP_TYPE = 6; // 聚人气商品
  SEARCH_PRODUCT_CARD_JUMP_TYPE = 7 ;//搜索商品卡片跳转
  ITEM_CARD_JUMP_TYPE = 8; //商品卡片点击跳转
}

// 直播流结束态倒计时期间点击进入Profile页的类型
enum AdLiveToProfileType {
  UNKNOWN_AD_LIVE_TO_PROFILE_TYPE = 0; // 未知点击类型
  AD_LIVE_PORTRAIT_CLICK = 1; // 直播结束倒计时期间点击头像进入Profile页
  AD_LIVE_DESCRIPTION_CLICK = 2; // 直播结束倒计时期间点击描述信息进入Profile页
  AD_LIVE_OTHER_AREA_CLICK = 3; // 直播结束倒计时期间点击其他区域进入Profile页
  AD_LIVE_LEFT_SLIDE = 4; // 直播结束倒计时期间左划进入Profile页
}

// 曝光原因
enum ExposureReason {
    UNKNOWN_EXPOSURE_REASON = 0;
    DURATION_MEETS = 1; // 满足时长
    INTERVAL_MEETS = 2; // 满足间隔
    ELSE = 3; // 其他
}

// 广告标签布局样式：标签数量变更，位置变更，都需要定义新的布局样式
enum AdLabelLayoutStyle {
    UNKNOWN_AD_LABEL_LAYOUT_TYPE = 0 ;
    LEFT_BOTTOM_DOUBEL_LABEL = 1 ; // 左下角双标签布局样式：播放页，广告主名称上方的双标签
}
// 广告标签信息
message AdLabelInfo {
    int32 label_no = 1 ; // 标签编号，根据标签布局设置
    string label_content = 2 ; // 标签内容
}

// 表单类型
enum FormType {
    UNKNOWN_FORM_TYPE = 0;
    CLUE_FORM = 1; // 线索表单
    GIFT_FORM = 2; // 礼包码表单
}

// 下载状态（如：弹窗取消时下载状态上报）
enum DownloadStatus {
    UNKNOWN_DOWNLOAD_STATUS = 0; // 未知
    DOWNLOAD_NOT_START = 1; // 未下载
    DOWNLOAD_START = 2; // 下载中
    DOWNLOAD_FINISHED = 3; // 下载完成
}

enum ItemClickAction {
    ITEM_CLICK_ACTION_UNKNOWN           =0;// 未知类
    ITEM_CLICK_ACTION_OPEN_LIVE         =1;//打开直播间
    ITEM_CLICK_ACTION_OPEN_PROFILE      =2;//打开PROFILE
    ITEM_CLICK_ACTION_DEEPLINK          =3;//打开DEEPLINK
    ITEM_CLICK_ACTION_OPEN_H5_GAME      =4;//打开H5游戏
    ITEM_CLICK_ACTION_OPEN_APP          =5;//打开已安装APP
    ITEM_CLICK_ACTION_OPEN_INSTALLER    =6;//已经下载完成，打开安装器
    ITEM_CLICK_ACTION_OPEN_APP_MARKET   =7;//打开应用商店（安卓）
    ITEM_CLICK_ACTION_OPEN_APP_STORE    =8;//打开APPSTORE(IOS)
    ITEM_CLICK_ACTION_OPEN_PENDANT      =9;//打开游戏试玩（准备下掉）
    ITEM_CLICK_ACTION_PERMISSION_REFUSE         =10;//没有获取存储权限
    ITEM_CLICK_ACTION_ENQUEUE_DOWNLOAD_LIST     =11;//仅WIFI下载，加入下载队列
    ITEM_CLICK_ACTION_START_DOWNLOAD            =12;//开始下载
    ITEM_CLICK_ACTION_OPEN_H5                   =13;//打开H5落地页
    ITEM_CLICK_ACTION_OPEN_TAOBAO_H5            =14;//打开淘宝H5落地页
    ITEM_CLICK_ACTION_OPEN_DOWNLOAD_H5          =15;//进入客户的下载中间页
    ITEM_CLICK_ACTION_OPEN_DOWNLOAD_H5_MOULD    =16;//进入自动化中间页
    ITEM_CLICK_ACTION_DOWNLOAD_CONFIRM_DIALOG   =17;//非ACTIONBAR点击打开确认弹窗
    ITEM_CLICK_ACTION_OPEN_GOODS_DETAIL_PAGE         =18; //打开商品详情页
    ITEM_CLICK_ACTION_TO_FOLLOW = 19; // 点击关注
    ITEM_CLICK_ACTION_OPEN_DETAIL_PAGE = 20; // 去往开屏的素材播放页

    ITEM_CLICK_ACTION_OPEN_BROWSER = 21;//点击后成功跳转至浏览器成功时上报

    ITEM_CLICK_ACTION_OPEN_NATIVE = 22; //去往native半屏落地页

    ITEM_CLICK_ACTION_OPEN_POPSHOW_PAGE = 23; //打开全屏彩蛋播放页
    //20211116 add
    ITEM_CLICK_ACTION_OPEN_AR = 24 ;//跳转AR页，只有信息流里有

    ITEM_CLICK_ACTION_OPEN_COUPON= 25 ;//跳转领取优惠券，品牌信息流样式

}

enum SplashClickJumpType {
    SPLASH_CLICK_JUMP_UNKNOWN = 0; // 未知类型
    SPLASH_CLICK_JUMP_LIVE = 1; // 跳转直播间
    SPLASH_CLICK_JUMP_H5 = 2; // 跳转H5
    SPLASH_CLICK_JUMP_DEEPLINK = 3; // Deeplink唤起
}

enum FollowsStatus {
    FOLLOWS_STATUS_UNKNOWN = 0; //未知
    FOLLOWS_STATUS_UNFOLLOW = 1; //未关注
    FOLLOWS_STATUS_FOLLOW = 2; //已关注
}

enum AdComponentType {
    AD_COMPONENT_TYPE_UNKNOWN = 0; // 未知
    AD_COMPONENT_TYPE_COUPON = 1; // 优惠券
    AD_LANDING_PAGE_BANNER = 2 ;//落地页banner组件
    AD_COMPONENT_TYPE_BLUE_V_DOWNLOAD = 3;//蓝v下载组件
    AD_COMPONENT_TYPE_COMMENT_TOP_BAR = 4;//评论区顶部actionbar
    AD_COMPONENT_DOWNLOAD_PENDANT = 5;//下载任务列表挂件
    AD_COMPONENT_ACTIONBAR =6 ;//actionbar组件
    AD_COMPONENT_DOWNLOAD_PENDANT_TRY_PLAY = 7 ;//试玩页下载挂件
}

enum IdfaAccessType {
    IDFA_UNKNOWN_ACCESS = 0; //未知或者获取不到
    IDFA_DIRECT_ACCESS = 1; //直接获取
    IDFA_COMPENSATION_ACCESS = 2; //补偿获取
}

enum AppLaunchType {
    APP_LAUNCH_UNKNOWN = 0; // 未知
    APP_LAUNCH_COLD = 1; // 冷启动
    APP_LAUNCH_WARM = 2; // 温启动：进程还在，页面已全部销毁，重新启动
    APP_LAUNCH_HOT = 3; // 热启动：业界30s等其热启动
}

// 激励视频endcard进入方式，废弃
enum EndcardEntryType {
    PLAY_AUTO_END = 0;  // 自动播放结束
    CLICK_CLOSE_BUTTON = 1; // 右上角关闭按钮点击
    RETURN_KEY_CLICK_CLOSE_BUTTON = 2; // 返回按键点击
}

// 激励视频endcard进入方式
enum EndcardEnterType {
  UNKNOWN_ENTER_TYPE = 0; // 未知
  AUTO_PLAY_END = 1;  // 自动播放结束
  BUTTON_CLICK_CLOSE = 2; // 右上角关闭按钮点击
  RETURN_BUTTON_CLICK_CLOSE = 3; // 返回按键点击
}

// server模板开屏，如果规定的时间内图片还没有下载完成，就放弃server模板开屏，直接使用native模板开屏
enum SplashImpressionMaterialType {
  UNKNOWN_MATERIAL= 0; // 未知
  SERVER_MATERIAL_LOAD_SECCESS= 1; // 成功下发展示了server素材
  SERVER_MATERIAL_LOAD_FAILED=2; // 未成功下发展示server素材
  NOT_SERVER_MATERIAL =3; // 未下发server素材
}

//请求SKA失败类型
enum RequestSkaDataFailType
{ SKA_UNKNOWN_FAIL_TYPE = 0;
  PHOTO_IMPRESSION_FAIL_TYPE= 1; // 封面曝光时请求ska失败
  ITEM_IMPRESSION_FAIL_TYPE= 2; // 素材曝光时请求ska失败
  ITEM_CLICK_FAIL_TYPE= 3; // 素材点击时请求ska失败
}

// 接入粉条的业务线类型
enum BusinessAccessType {
  UNKNOWN_BUSINESS_TYPE = 0;
  SERVICE = 1; // 服务号
  GAME = 2; // 游戏
}

// bid server 类型标识
enum BidServerType {
    BID_SERVER_MAIN = 0;
    BID_SERVER_MERCHANT_MAIN = 1;
    BID_SERVER_MERCHANT = 2;
    BID_SERVER_FANSTOP = 3;
    BID_SERVER_I18N = 4;
    BID_SERVER_ACCOUNT = 5;
    BID_SERVER_CAMPAIGN = 6;
    BID_SERVER_BOYLE = 7;
    BID_SERVER_I18N_LOWESTCOST = 8;
    BID_SERVER_I18N_ACCOUNT = 9;
}

enum ButtonType {
  UNKNOWN_BUTTON_TYPE = 0;
  DOWNLOAD = 1; // 下载
  CONSULT_APPOINTMENT = 2; // 预约咨询
  GAME_HANDLE = 3; // 游戏手柄
  FEEDBACK_BUTTON = 4 ;//反馈按钮
  RECRUITMENT = 5 ;//招聘
  MINIPROGRAM = 6;//快手小程序
  PURCHASE = 7;  //团购
  JINGLE = 8 ;//小铃铛
  HOUSE  = 9 ;//房产
  DSP_RECRUIT = 10; //快聘

}

enum ButtonStyle {
  UNKNOWN_BUTTON_STYLE = 0;
  CONVERSION_OPERATION_BAR = 1; // 转化操作条
  CONVERSION_ICON = 2; // 转化icon
  CONVERSION_CARD = 3 ;// 转化卡片，卡片整体
  CONVERSION_CARD_BAR = 4 ;//  转化卡片 ActionBar
  CONVERSION_CARD_OTHER_AREA  = 5 ;// 转化卡片 其他区域
  CONVERSION_MARQUEE = 6 ;//跑马灯
  CONVERSION_GUIDE_BAR = 7 ;//强引导卡片，在评论区位置，优先级：强引导组件>强提醒组件，一直是高亮样式
  CONVERSION_LABEL_TOP_CARD = 8 ;//讲解卡上方标签
  CONVERSION_AUTO_CLICK =9;//快直播转化任务小铃铛自动点击
}
enum AdPhotoCommentSource {
  UNKNOWN_SOURCE = 0;
  QUICK_COMMENT_PHOTO = 1;
  QUICK_COMMENT_INPUT_BOX = 2;
}
 //用于区分快捷评论-图片区的类型
enum QuickCommentPhotoType {
  UNKNOWN_QUICK_COMMENT_TYPE = 0;
  PHOTO = 1;
  EMOJI = 2;
}

enum AndroidOs {
  ANDROID_DEFAULT = 0; // 默认的Android系统
  ANDROID_HARMONY = 1; // 华为鸿蒙操作系统
}
//挽留弹窗类型ItemClickType
enum RetainCodeType {
    UNKNOWN_RETAIN_CARD_TYPE = 0;
    PHOTO_PAGE_WATCH_PHOTO = 1; //视频播放页点击关闭按钮，触发观看时长挽留弹窗
    PHOTO_PAGE_WATCH_LANDING = 2; //视频播放页点击关闭按钮，触发浏览落地页挽留弹窗
    LANDING_PAGE_WATCH_LANDING = 3; //落地页点击关闭按钮，触发浏览落地页挽留弹窗
    PHOTO_PAGE_ACTIVE_APP = 4;//视频播放页点击关闭按钮，触发二阶段激活挽留弹窗
    PHOTO_PAGE_PLACE_ORDER = 5; //视频播放页点击关闭按钮, 触发二阶段下单挽留弹窗
    PHOTO_PAGE_PLAY_AND_INVOKED = 6;//触发二阶段挽留弹窗，弹窗为拉活弹窗
    PHOTO_PAGE_PLAY_AND_STAY = 7;//触发二阶段挽留弹窗，弹窗为留存弹窗
    RETAIN_CARD_TYPE_1 = 8 ;//挽留弹窗卡片样式1
    RETAIN_CARD_TYPE_2 = 9 ;//挽留弹窗卡片样式2
    RETAIN_CARD_TYPE_3 = 10 ;//挽留弹窗卡片样式3
    RETAIN_CARD_TYPE_4 = 11 ;//挽留弹窗卡片样式4
    INTERCEPT_DIALOG = 12 ;//确认继续弹窗
    LEAD_TO_TASK = 13 ;//引导去做任务弹窗
    RETAIN_CARD_TYPE_5 = 14 ;//联盟全屏挽留弹窗卡片样式5
    RETAIN_CARD_TYPE_6 = 15 ;//联盟全屏挽留弹窗卡片样式6
    RETAIN_CARD_TYPE_7 = 16 ;//联盟全屏挽留弹窗-激励挽留弹窗卡片样式-用于线上已经现存的弹窗
    RETAIN_CARD_TYPE_8 = 17 ;//激励挽留弹窗卡片样式8
    RETAIN_CARD_TYPE_9 = 18 ;//激励挽留弹窗卡片样式9
    RETAIN_CARD_TYPE_10 = 19 ;//激励挽留弹窗卡片样式10
    RETAIN_CARD_TYPE_11 = 20 ;//激励挽留弹窗卡片样式11
    PHOTO_PAGE_PLAY_AND_FORM = 21; //二阶段表单挽留弹窗。点击二阶段的表单关闭弹出的弹窗。深度激励：表单激励类型
    WATCH_PHOTO_FINISHED_WATCH_ANOTHER = 22; // 视频播放页，任务完成的情况下点击关闭按钮，触发再看一个挽留弹窗

}

enum BusinessSceneType{
  UNKNOWN_SCENE_TYPE = 0 ;
  PAGE_LOADING_POP_AR = 1;//pop_ar的loading页
  PAGE_GAME_COUNTDOWN= 2 ; //游戏倒计时页面
  PAGE_GAME_PLAYING = 3;//  游戏页面
  PAGE_GAME_SUCCESS_FINISHED = 4;//  游戏成功结束页面
  PAGE_GAME_FAILED_FINISHED = 5;//  游戏失败结束页面
  PAGE_PLAY_END = 6;//播放结束页
  POP_UP_NEO_REMIND = 7 ;//收益提醒弹窗
  PAGE_REFLOW_FULL_SCREEN = 8 ;//联盟激励视频全屏回流页
  PAGE_REFLOW_HALF_SCREEN  = 9 ;//联盟激励视频半屏回流页
  POP_UP_REMIND_TO_AUTO_EVOCATION = 10 ;// 自动唤起app提示弹窗
  POP_UP_RESERVATION = 11 ;// 预约弹窗，目前用于联盟跳转主站预约[暂时不能复用其他地方]
  PAGE_SEARCH_RESULT = 12 ;//搜索结果页
  PAGE_STANDARD_LIVE_ROOM= 13 ;//直播间
  POP_UP_GOLD_TASK_STATUS = 14 ;// 金币任务状态弹窗
  POP_UP_FOLLOW  = 15 ;// 涨粉弹窗，目前用于联盟跳转主站涨粉关注[暂时不能复用其他地方]
  POP_UP_LEADTO_STORE  = 16 ;// 引导去店铺弹窗。目前用于联盟点击跳转去店铺
  POP_UP_LEADTO_LIVE  = 17 ;// 引导去直播间弹窗。目前用于联盟点击跳转去直播间
  POP_UP_DOWNLOAD_CONFIRM = 18 ;//下载确认弹窗
  AREA_OF_CARD  = 19 ;// 卡片区域，用于切换按钮
  AREA_OF_COMMENT_DETAIL = 20 ; //描述区域,用于切换按钮
  POP_UP_REWARD_AGAIN = 21 ; //再次奖励弹窗
  PAGE_PHOTO_PLAY = 22 ;//播放页
  POP_UP_INSTALL_CONFIRM = 23 ;//安装确认弹窗
  PAGE_LIVE_END = 24;//直播结束页面
  POP_UP_LEAD_OPEN_APP =25 ;//引导打开app引导弹窗
  POP_UP_COUPON = 26 ;//优惠券弹窗，包括红包等优惠
  POP_UP_SECOND_JUMP_CONFIRD = 27 ;// 二次跳转确认弹窗
  PAGE_LOTTERY=28;//聚合中间页-抽奖页
  PAGE_SCRAPE=29;//聚合中间页-刮开页
  PAGE_DRAW=30;//聚合中间页-领取页
  POP_UP_RULES_DESC = 31 ;//规则说明弹窗
  SIDE_SLIDE_WINDOW = 32; //侧滑小窗
  PAGE_AGGREGATION = 33;//聚合页
  PAGE_FEED_MIDDLE = 34 ;//图文详情页：表示的是FEED流图文广告里的中间页
  NOTIFICATION_BAR =35 ; //系统通知栏位置
  POP_UP_WARN_FOR_DOWNLOAD_MEMORY = 36 ;//下载内存不足提示弹窗
  POP_UP_RETAIN_FOR_DOWNLOAD_PAUSE = 37;//手动下载暂停挽留弹窗
  PAGE_TUBE_MIDDLE = 38 ;//短剧中间页
  PAGE_PROFILE= 39 ;//个人主页
  POP_UP_RETAIN_FOR_CANCEL_INVOKE = 40;//取消唤端触发挽留弹窗
}

// 曝光原因
enum ExposureReasonNew {
  UNKNOWN_EXPOSURE_REASON_NEW = 0; // 存在之前版本不上报的情况；没有命中实验
  DURATION_MOVE_FORWARD = 1;// 广告曝光时，该广告是通过前移到达该位置的
  NO_FORWARD_WITH_LIMIT = 2;//广告曝光时，该广告前移位置正好是原来位置；该广告满足时长但因为adgap约束没有前移；品牌不前移
  NO_FORWARD = 3 ;//默认值。广告曝光时，未满足条件所以没有前移，目前是时长原因
  WEAK_NETWORK_PK_SUCCESS = 4 ;//弱网pk成功
  CACHE_PK_WIN_DUP_PHOTO_ID = 5 ;//photo_id重复被丢弃后，缓存pk成功
  CACHE_PK_WIN_AD_EXPIRED = 6 ;//广告过期被丢弃后，缓存pk成功
  CACHE_PK_WIN_SHIELD_NEXT_PHOTO = 7 ;//作者被封禁等case被丢弃后，缓存pk成功
  HIGH_SCORE_PK_WIN  = 8 ;// 高分pk成功，包括缓存pk和下发pk成功
  LOW_SCORE_PK_FAILED = 9 ;//低分pk失败，下发列表被替换之后，被替换的广告曝光的时候属于失败
  SERVER_PK_WIN = 10 ;//server端pk成功
}
enum AwardReceiveStage{
  UNKNOWN_AWARD_STAGE = 0;//默认值
  AWARD_WATCH_PHOTO = 1;//一阶段观看完成
  AWARD_ACTIVE_APP_NOT_WATCH_PHOTO = 2;//二阶段激活完成，一阶段观看没有完成
  AWARD_ACTIVE_APP_WITH_WATCH_PHOTO = 3;//二阶段激活完成，一阶段观看完成
  AWARD_LANDING_PAGE_NOT_WATCH_PHOTO = 4; //二阶段浏览落地页完成，一阶段观看视频没有完成
  AWARD_LANDING_PAGE_WITH_WATCH_PHOTO = 5; //二阶段浏览落地页完成，一阶段观看视频完成
}

enum BrowsedShowType {
  UNKNOWN_BROWSED_TYPE = 0; // 未知
  CLIENT_SHOW_TYPE = 1;  // client show
  SERVER_SHOW_TYPE = 2;  // server show
}
enum PageTryplayEntrySource{
  UNKNOWN_TRYPLAY_ENTRY_SOURCE = 0 ;//默认情况
  PLAY_FINISHED_NORMAL = 1 ;// 正常情况：激励视频播放结束后自动跳转
  PENDANT_CLICK_NOT_AUTO = 2 ;//【试玩前置】非自动跳转挂件点击
  PENDANT_CLICK_AUTO = 3 ;//【试玩前置】自动跳转挂件点击
  PENDANT_AUTO = 4;// 【试玩前置】自动跳转挂件倒计时结束自动跳转
  ACTIONBAR_CLICK = 5;//【试玩一下】actionBar 试玩一下按钮点击
  ENDCARD_CLICK = 6;//【试玩一下】endcard 试玩一下按钮点击

}
enum ElementShowType{
  UNKNOWN_ELEMENT_SHOW_TYPE  =0 ;// 未知，因为pb不能命名重复，加了个item
  CLICK_ELEMENT_SHOW_TYPE = 1 ;//点击唤起，这个一般是默认情况
  AUTO_ELEMENT_SHOW_TYPE = 2 ; //自动唤起
}

message ClientParams {
    ItemShowType item_show_type = 1;
    ItemClickType item_click_type = 2;
    ItemCloseType item_close_type = 3;
    ShareChannel share_channel = 4;
    ReportType report_type = 5;
    CommentDialogExpandType comment_dialog_expand_type = 6;
    string negative_type = 7;
    AdPhotoToProfileType ad_photo_to_profile_type = 8;
    NewDetailPageTabClickType new_detail_page_tab_click_type = 9;
    LandingPageEntrySource landing_page_entry_source = 10;
    TemplateType template_type = 11; //话题模板类型
    string installed_from = 12; // recall:召回安装,app_stopped:应用关闭状态下安装,为空时为默认安装方式,download_management:来自于下载管理器
    EvocationType evocation_type = 13; // DEEPLINK唤起时上报唤起类型
    LandingPageType landing_page_type = 14; // 落地页类型
    string ad_photo_to_live_stream_id = 15; // action_type为AD_PHOTO_TO_LIVE/AD_PHOTO_TO_PROFILE_TO_LIVE时上报live_stream_id
    MediaBarType media_bar_type = 16; // 多媒体弹窗页面类型
    Module module = 17 [deprecated=true];// 无用，已被删除
    MediaBarSource media_bar_source = 18; //广告信息搜集卡片弹出来源
    string custom_message = 19; //客户信息
    string evocation_parameter = 20; //跳转协议报头
    int32 material_type = 21; //信息流广告封面视频化的物料来源类型
    int64 duration = 22; //视频封面播放时长（单位：毫秒)
    double show_proportion = 23; //最大曝光比例（2 位有效小数
    int64 loading_duration = 24; //视频封面加载时长（单位：毫秒) // 20200527新增在AD_APPSTORE_IOS_IMPRESSION时上报,记录从广告点击加载到加载成功时间
    string report_desc = 25; // 举报描述
    repeated string report_pic_url = 26; // 举报截图链接
    string report_contact = 27; // 举报联系方式
    ItemPlayType item_play_type = 28; //播放类型相关CLOSE_BULLET_ICON_CLICK
    ElementType element_type = 29; // 广告元素类型，作为 AD_ELEMENT_XXX 事件的参数
    uint64 ad_item_click_back_duration = 30; //视屏详情页二跳点击到返回视屏详情页的时长，单位ms，当ActionType=AD_ITEM_CLICK_BACK时有效
    uint64 photo_play_second = 31; //广告播放play N秒，当ActionType=AD_PHOTO_PLAYED_NS时有效
    SplashFailType splash_fail_type = 32; // 开屏失败原因, 当ActionType=AD_SPLASH_FAIL时有效
    EcomCardState ecom_card_state = 33; //直营电商购物卡片是否展开或收起
    PhotoSizeStyle photo_size_style = 34; //直营电商购物卡片曝光、点击时区分场景是横屏还是竖屏
    AdPhotoNegativeSource ad_photo_negative_source = 35; // 负反馈操作上报动作来源，点击或长按
    AdNegativeType ad_negative_type_context = 36; // 区分负反馈点击上下文场景
    ItemClickType last_item_click_type = 37; // 上一个item的点击类型，如：重播处点击、profile点击等
    CommonCardType common_card_type = 38 [deprecated=true]; // 客户端H5卡片类型, 易触发IOS端解析pb问题，不使用enum类型
    string card_impression_failed_reason = 39; // 识别曝光失败原因
    int32 played_seconds = 40; //配合AD_LIVE_PLAYED_SECONDS使用，代表是播放x秒事件
    SplashDisplayType splash_display_type = 41; // 开屏广告展示类型
    CardCloseType card_close_type = 42; // 通用卡片的关闭类型
    AdNegativeQualitySource ad_quality_panel_source = 43; //ID可用来区分浮层的标题内容
    AdNegativeQualityType ad_quality_panel_type = 44; //ID可用来区分不感兴趣的类型
    int32 common_card_type_id = 45; // 客户端H5卡片类型id, 1=APP_INSTALL_CARD
    int64 poi_id = 46; //poi id
    PoiPosition poi_position = 47; //POI位置
    PoiStyle poi_style = 48; //poi样式
    EventTriggerType trigger_type = 49; // 事件触发的时机
    int64 stay_time = 50; // 记录停留时长
    int32 played_rate = 51; // 播放百分比,来区分是否播放结束 0-100
    int32 replay_times = 52; // 第几次重播束
    SplashMaterialDisplayType splash_material_display_type = 53; // 开屏广告展示素材类型
    int32 ad_social_live_transform_id = 54 [deprecated=true]; //废弃，请使用ad_social_live_conversion_id
    bool is_realtime = 55; // 开屏广告是否实时请求
    string landing_page_url = 56; // 落地页真正展示的页面url, 当ActionType = AD_LANDING_PAGE_LOADED时有效
    int64 ad_social_live_conversion_id = 57; //快直播转化任务id
    AdLiveClickType ad_live_click_type = 58; //直播间内部点击类型
    string download_failed_reason = 59; // 下载失败原因，日志只保留异常堆栈第一行内容
    DownloadSource download_source = 60; // 下载来源
    IsPackageChanged is_package_changed = 61; // 是否被换包
    BoxCancelledType box_cancelled_type = 62; // 下载弹窗关闭类型
    int64 ad_social_live_scene_id = 63; //快直播转化任务场景id
    int64 ad_social_live_conversion_type = 64; //快直播转化任务类型
    AdLiveShowPosition ad_live_show_position = 65; //快直播曝光位置
    int32 app_cache = 66; // 是否是缓存安装；使用int32为防止未来有新的类型
    PhotoToLiveEntrySource photo_to_live_entry_source = 67; // AD_PHOTO_TO_LIVE的入口来源
    LiveRoomPattern live_room_pattern = 68; // 直播间样式,用于区分简版还是正式
    ClickPositionLog click_position_info = 69; // 用于记录点击上报位置
    DiscardScene discard_scene = 70; // 丢弃场景

    InitVoiceStatus init_voice_status = 71 ; // 播放状态
    DeeplinkType deeplink_type = 72 ; // deeplink 类型

    int64 playable_page_stay_time = 73; // 浮窗停留时长

    string preload_id = 74 ;
    int32 is_valid_returned = 75 ;  // 是否返回成功；0代表失败，1代表成功
    int64 returned_time = 76 ; // 返回媒体时间
    int64 spread_time = 77 ; // 缓存时间

    AdLiveShopClickType ad_live_shop_click_type = 78; // 直播购物车点击来源
    AdLiveItemImpressionType ad_live_item_impression_type = 79; // 直播商品列表样式
    AdLiveShopLinkJumpType ad_live_shop_link_jump_type = 80; // 直播商品点击来源
    int64 merchant_item_id = 81 [deprecated = true]; // 商品ID
    AdLiveToProfileType ad_live_to_profile_type = 82; // 直播流结束态倒计时期间点击进入Profile页的类型

    LandingPageWebViewType landing_page_web_view_type = 83 ; //
    int64 loading_landing_page_time = 84; // 用户加载落地页时间，load完成时间戳 - enter时间戳
    int64 experience_time = 85; // 落地页用户体验时间，load完成时间戳 - ad_item_click时间戳
    int64 leave_time = 86; // 落地页用户离开时间，close时间戳 - enter时间戳
    string progress_bar_status = 87 ; // AD_LANDING_PAGE_CLOSED触发的进度条状态，例如99%或100%
    string item_id = 88; // 商品ID,因类型问题,用于替代merchant_item_id

    string failed_reason = 89 ; // 应用商店打开失败即上报,失败原因包含：1没有安装应用商店/2不在白名单范围内/3结果异常
    ElementClickType element_click_type = 90 [deprecated=true] ; // 广告元素点击类型，#请使用ElementType
    int64 business_goods_position = 91 ;//电商商品位置

    string is_vpn = 92 ; // 安卓反厂商拦截VPN状态：open_vpn_success、open_vpn_fail
    int64 install_time = 93 ; // 安装时间

    int32 download_amount = 94; // 下载管理器任务个数
    int32 is_download_extend = 95; // 是否展开按钮出现

    int32 is_changed_endcard = 96 ; // 是否为落地结束页替换endcard，1: 是 0 ： 否

    string task_check_id = 97 ; // 流量回放的case唯一id，结合hostname和当前时间生成
    string task_event_id = 98 ; // 流量回放case对应的那条ad_log的event_id

    int32 is_reddot_show = 99 ; // 下载管理器入口，红点是否出现
    ExposureReason exposure_reason = 100; // 曝光原因

    uint32 device_stat_battery = 101; // 剩余电量，百分比%
    uint32 device_stat_memory = 102; // 剩余内存大小，MB
    int32 is_package_name_consistent = 103; // 下载后的包名是否与广告返回的包名一致；0:无法判断，位置 1：一致 2：不一致

    int64 relay_times = 104; // 重播放的次数，第一次是0
    int64 played_duration = 105; // 已经播放的时间，单位ms
    int64 item_duration = 106; // 视频的总时长，单位ms
    int32 play_action = 107; // 广告播放行为，0:未知，1：自动播放 2：用户点击播放
    int32 play_type = 108; // 广告播放类型, 3：重新开始播放,2：暂停后继续播放,1：第一次播放,0:未知

    int32 is_auto_download = 109 ; // 是否来源预约自动下载，1：是 0：否
    uint32 device_stat_diskfree = 110; // 剩余磁盘容量，MB

    repeated AdLabelInfo ad_label_info = 111 ; // 广告标签信息
    AdLabelLayoutStyle ad_label_layout_style = 112; //广告标签类型

    int32 lp_page_enter_type = 113 ; // 落地页进入类型，1：通过正常广告视频进入；2：通过商品聚合页进入；0：未知
    string server_package_name = 114 ; // 服务端下发的包名
    string installed_package_name = 115 ; // 安装的包名

    FormType form_type = 116; // 表单类型
    string is_pass_through = 117; // 是否通过，PASS_THROUGH：通过 ,PASS_THROUGH_FAIL：未通过

    uint32 close_button_impression_time = 118; // 关闭按钮曝光时间
    uint32 close_button_click_time = 119; // 区分激励视频关闭按钮在第几秒点击关闭,单位s

    //直播预约投快享卡片参数
    int32 live_status = 120; //直播状态，0:未知，1：直播前，2：直播中，3：直播后
    int32 live_reservation_status = 121;  //直播预约状态，0:未知，1：未预约，2 :已预约
    string live_reservation_fail_reason = 122 ;//直播预约失败参数,失败原因（e.g 主播不能预约自己的直播等）
    int32 live_reservation_push = 123;  //直播预约弹窗类型，0:未知，1：总弹窗（日历+push），2: push 弹窗，3:日历弹窗

    DownloadStatus download_status = 124; // 点击下载弹窗关闭按钮时的下载状态

    int32 live_reservation_half_button_status = 125; //直播预约半层弹窗按钮状态，0：未知，1：预约，2：取消预约
    PrivacyPosition privacy_position = 126; // 隐私风险提示
    //20201015 add
    ItemClickAction item_click_action =127  ;

    string splash_preload_fail_reason = 128; // 开屏素材预加载失败原因
    SplashClickJumpType splash_click_jump_type = 129; // 开屏点击跳转类型
    string splash_click_jump_live_stream_id = 130; // 开屏点击跳转直播间id
    SplashMaterialPreloadType splash_material_preload_type = 131; // 开屏预加载素材类型

    bool item_click_for_model = 132; // 是否触发转化流程
    FollowsStatus follows_status = 133; // 关注状态 0:未知 1:未关注 2:已关注
    AdComponentType ad_component_type = 134; // 0:未知 1:优惠券
    IdfaAccessType idfa_access_type = 135; //IDFA获取类型
    int32 download_card_type = 136; // 表示落地页的不同类型，0-未知；1-自有落地页；2-拼接落地页；3-兜底落地页
    ItemCloseType appstore_close_type = 137; // App Store关闭类型
    ItemCloseType detail_page_close_type = 138; // 详情页关闭类型

    AppLaunchType app_launch_type = 139; // 应用启动类型
    int64 played_to_x_ms = 140; // 视频播放到第几毫秒

    //20201222 add
    int32 render_container = 141; //渲染容器,0：h5，webview渲染。1: Tachikoma渲染

    int32 sub_action_type = 142;  //(已废弃)可复用的事件参数。用于标识一个事件的多种触发时机枚举，或者事件触发后带来的多种结果枚举。具体枚举值可查看对应埋点的设计文档。

    string sub_action2 = 143;  //可复用的事件参数。用于标识一个事件的多种触发时机枚举，或者事件触发后带来的多种结果枚举。具体枚举值可查看对应埋点的设计文档。

    string live_reservation_source = 144; //直播预约来源，half_button: 半层按钮点击直播预约，profile：进入profile点击直播预约

    //20201224 add
    string apk_package_name = 145; //下载apk的包名
    string apk_package_md5 = 146; //下载apk的md5

    //20210105 add
    int64 landing_page_loaded_duration = 147; //落地页加载时长，从进入埋点落地页到请求第一个元素返回的时长

    EndcardEntryType endcard_entry_type = 148; // 废弃

    EndcardEnterType endcard_enter_type = 149; // 激励视频endcard进入方式

    //20210308 add
    SplashImpressionMaterialType splash_impression_material_type = 150; // server模板开屏，如果规定的时间内图片还没有下载完成，就放弃server模板开屏，直接使用native模板开屏
    //20210312 add
    int32 is_cache_ads = 151; // 是否为缓存加载广告，0：未知，1：是缓存数据，2：非缓存数据
  //20210318 add
    string ad_photo_see_type = 152;//用于区分点击按钮的场景

    //20210323 add
    string photo_play_rate = 153;//区分75%播放进度

    string ad_photo_report_source = 154;
    string ad_photo_put_source = 155;
    string ad_photo_put_pv_source = 156;
    string ad_photo_put_type = 157;
    string ad_photo_interest_source = 158;

    int32 splash_information_type = 159; // 开屏附加信息类型，0：无信息，1：个性化开屏分享信息
    int32 splash_is_information = 160; // 开屏曝光时，有无附加信息，0：无信息，1：有信息
    int64 landing_page_action_time = 161; // 落地页行为时间，action发生时间戳 - enter时间戳
    //20210408 add
    RequestSkaDataFailType request_skadata_fail_type = 162;//请求SKA失败时上报

    BusinessAccessType business_access_type = 163; // 接入粉条的业务线类型
    ButtonType button_type = 164; // 按钮类型
    ButtonStyle button_style = 165; // 按钮样式
    int32 ad_agg_page_source = 166; // 聚合广告页来源，0未知，1点击引导入口主动进入，2退出表单落地页进入
    int32 ad_agg_page_photo_type = 167; //聚合广告页素材样式，1信息流样式；2卡片样式
    int64 ad_order = 168; //广告组件的顺序
    AdPhotoCommentSource ad_photo_comment_source = 169; //评论来源
    QuickCommentPhotoType quick_comment_photo_type = 170; //快捷评论-图片区的类型
    //20210416 add
    int64 error_code = 171; //错误编码
    string error_msg = 172; //错误信息
    int64 count_down_time = 173; //倒计时时长
    //20210527 add
    string front_card_render_success = 174; //渲染成功
    string front_card_render_fail = 175; //渲染失败

    string deeplink_from = 176; //deeplink形式，auto_deeplink-自动跳转、click_deeplink-用户主动点击跳转。快任务618活动新增。

    AndroidOs android_os = 177;//区分是否鸿蒙系统
    //20210618 add
    int32 ad_business_type = 178; //联盟子业务分类   1.联盟内容/ 2.联盟电商
    int32 content_style = 179; //内容分类，1. 视频 2 直播
    int32 content_sub_type = 180; //内容来源，1. 内容，2.广告，3.电商
    int64 ad_position_num = 181; //直播间在列表中出现的位置(统一从1开始)
    string deeplink_app_name = 182 ; //换端的app名称,kuaishou、kuaishou_nebula, taobao_h5, taobao, jingdong_h5, jingdong
    int32 ad_agg_page_location = 183; //聚合广告展示的位置:0未知、1全聚合广告页；2广告视频完播页
    int32 ad_banner_type = 184; //banner广告样式，0未知，1纯文字，2纯图片，3图文描述，4视频类
    int32 ad_banner_size_type = 185; //banner广告大小样式，1小样式；2大样式
    //20210622 add
    int32 ad_interstitial_source = 186; //插屏广告来源，0未知，1按Kconf配置逻辑播放触发，2人为点击暂停触发，3冷启动&热启动进入主feed流
    bool whe_show_commission = 187; //是否展示返佣信息
    string splash_interaction_rotate_angle = 188; // 开屏轻互动角度信息，包含 x、y、z 方向，json 格式
    int32 group_image_dynamic_type = 189; //组图动态样式：0  未知，1  非动态，2  翻转样式
    //20210714 add
    int32 ad_interstitial_type = 190; //插屏广告样式，默认值为-1
    //20210722 add
    int32 ad_close_type = 191; // 广告关闭方式 0：未知 1：手动关闭 2：系统自动关闭
    float splash_shake_acceleration = 192; // 开屏摇动加速度
    int32 splash_slide_percent = 193; // 开屏滑动百分比
    int32 lose_reason = 194; //竞价失败原因，0-其他，1-广告超时，2-广告竞价失败
    int64 time_limit = 195; //媒体设置的超时时间
    int64 win_ecpm = 196; //竞价胜出的ecpm,单位都是分/千次，
  //20210805 add
    int32 ecpm_type = 197; //ecpm的填充类型，1-媒体设置，2-客户端兜底填充
    int32 imp_fail_reason = 198; //广告曝光失败原因0-其他，1-媒体侧底价过滤，2-广告竞价失败，同时上报win_ecpm；3-缓存失效，4-曝光优先级降低

    //20210820 add
    RetainCodeType retain_code_type = 199;//挽留弹窗类型
    //20210916 聚星新增
    int64 leave_time_long = 200; //从快手离开换端，到首次返回到快手的时长，单位ms
    string leave_type = 201; //跳转类型,1：一跳类型；2：二跳类型
     //20211029 add
    int64 photo_duration = 202; // 素材整体时长，单位ms
    //20211110 add
    int32 try_play_step_id = 203; //试玩步骤的元素点击。具体含义由前端维护
    int32 download_install_type = 204; //区分下是用户点击唤起安装，还是下载完成唤起安装。0：默认；1：用户主动(点击)唤起安装；2：SDK自动唤起安装
    bool universe_second_ad = 205;  // 联盟激励视频再看一个广告
    //20211116 add
    BusinessSceneType business_scene_type = 206;//业务场景类型，之后可以通用，弹窗的类型也可以放在这里，只要是无法区分是什么业务场景的都用这个参数
    int64 game_played_duration = 207 ;//游戏时长；从loading结束开始计时，跳转落地页、返回信息流的时候，都上报这个参数
    string client_pk_fail_ad_info = 208 ;//客户端缓存pk失败的广告，是原本需要曝光的广告信息。是一个json串，里面包括被替换的llsid、creative_id、score（ecpm）
    //20211117 add
    int32  pendant_type = 209;//福利类型
    int32 is_live_auto_clip = 210 ;//直播封面是否为优选素材 0为不是，1为是
    //20211118 add
    ExposureReasonNew  exposure_reason_new = 211;//曝光原因，之后ExposureReason就废弃了
    int32  move_forward_steps = 212 ;//前移的步数，比如第3位移动到第2位，值为1。配合exposure_reason_new使用
    int32  is_adx = 213 ;//是否聚合竞.1:是; 0:否 ;-1:默认
    AwardReceiveStage award_receive_stage = 214;//领取金币奖励时的阶段
    //20211125  cyl_add
    int32 finger_swipe_type = 215 ;//滑动的方向，0为默认值，1表示向上滑；2为想左滑；3为向右滑
    int32 finger_swipe_distance = 216 ;//滑动的距离，配合滑动场景使用
    string black_url_info = 217 ;  //黑名单拦截时返回的黑名单url信息，返回一个json串，包括advertiser_url、black_url、block_timestamp等信息，还包括一些AD_LANDING_PAGE_LOADED事件的信息
    //20211202 cyl_add
    PageTryplayEntrySource page_tryplay_entry_source = 218;//进入试玩页的来源，
    //20211220 cyl_add
    ElementShowType element_show_type = 219 ;//展示类型，例如卡片展示时吊起的类型，有点击吊起，有自动吊起。
    int32 landing_page_modle_type = 220 ;//落地页优选模版的类型 ，目前只有1，3，4，5，6种类型
   //20211222 cyl_add
    int32 app_store_page_type = 221 ;//跳转应用商店路径。0是默认值跳到应用商店内部下载；1是直接弹出小米应用详情页下载。
    int32 install_status = 222 ;// 0是默认值未检测到已安装，1是已安装
    string nature_photo_id = 223 ;// 自然作品id
  //20220106 cyl_add
    string fingerprint_time_id = 224 ;// 取值逻辑为MD5（启动时间 + ":" + 系统更新时间），用来标识设备id。32位字符，类似E911FC897763834E94D1AA01AF4E156E
    //20220119 add
    repeated UniverseOperationData universe_operation_datas = 225;  // 联盟运营配置反馈数据
    //20220307 聚星add
    int32 reservation_button_status = 226 ;//预约状态。0：未知，1：预约，2：取消预约。目前用于聚星，之后可以用于其他业务线
    int32 element_show_index = 227 ; //展现序号。可以包括图片序号，或者其他元素展示序号
    RewardingInfo rewarding_info = 228;  // 联盟-聚合买量-激励信息
    //20220407 add --houhuanran
    int32 is_realtime_show = 229 ;//是否实时曝光,0是预加载素材曝光；1是 实时素材曝光
    //20220512 add  --brand
    string dva_plugin_version = 230;// 使用了插件化功能（如 POPAR 等）的插件版本号
    //20220525 add
    string item_id_list = 231 ;//商品id列表，用于多个商品一次曝光的时候
    //20220712 add
    bool click_instant_reserve = 232 ;// 是否点击立即预约广告
    string deeplink_failed_reason =233 ;//deeplink换端失败原因
    int32 deeplink_length = 234 ;//deeplink长度
    //20220908 bidding竞价相关
    int32 adn_type = 235 ;//竞价第一名的平台。枚举值为1：快手其他广告，2：其他平台（平台名，选填），3：自售直客
    string adn_name = 236 ;//当adn_type=2时，媒体上报的的平台名，枚举值为chuanshanjia；guangdiantong；baidu；other
    int64 highest_loss_price = 237 ;//我方竞价成功时，第二名的价格，即最大竞败方出价。单位都是分/千次，
    //20220920 adx竞价相关
    string adx_name = 238;  // adx厂商名
    int32 bid_fail_code = 239;  // 广告竞价失败状态码
    string bid_fail_reason = 240;  // 广告竞价失败原因
    string ad_render_area=241; //广告渲染区域，格式为长和宽，英文逗号分隔，长和宽为int类型 单位为像素值
    //20221024 add
    int32 finger_swipe_time = 242 ;//滑动时长，单位为毫秒
    int32 finger_swipe_angle = 243 ;//滑动角度，返回数值，比如60度则返回60
    int32 simplified_live_room_type=244;//区分简易直播间类型 0 --简易直播间，1 --lite直播间
    string  second_page_url = 245;  //二跳url
    string  search_words=246; //推荐词
    string lp_aggregate_page_id = 247;//建站走信息流的参数，聚合落地页id
    string splash_interaction_preload_threshold = 248 ;//预加载阶段最低阈值，能触发轻互动的最小阈值。如果是扭动，则返回扭动角度；如果是滑动，则返回距离
    string splash_interaction_realtime_threshold = 249;//实时阶段阈值，能触发轻互动的最小阈值。能触发轻互动的最小阈值。如果是扭动，则返回扭动角度；如果是滑动，则返回距离
    string nature_author_id = 250;//自然作品的作者id
    int32 is_service_tab_showed = 251 ;//是否有服务展示，1表示有服务tab，0表示没有服务tab
    int32 is_service_tab_auto_selected = 252 ;//服务tab是否默认被选中。1表示默认展示服务tab，0表是默认展示其他tab
    int32 is_store_showed = 253;//是否有店铺，1表示有，0表示没有
    int32 is_detail_page_close = 254; //是否关闭详情页,1表示关闭详情页，0表示不关闭
    int64 ad_impression_amount = 255; //广告曝光个数
    string photo_search_word = 256; // 搜索词，聚星
    int64  anchor_distance = 257 ;//广告真实曝光位置与进入视频之间的相对位置；目前只作用于p页内流
    string inner_session_id =258 ;//用户每次进入内流唯一的id，能够标识这个内流session。目前只作用于p页内流
    int32 ad_gap_continuous_count = 259; //同类广告连出个数，如果连续出现3个硬广，则第三个硬广的这个值就为3
    int32 ad_gap_all_continuous_count = 260 ;//所有广告连出个数，不区分是否是同类型。
    int32 cache_type = 261;// 是否为缓存重传上报。0为否，1为是。
    int32 v_tag_type = 262;//品牌v标类型，1表示包含金V标，有金V标的时候进行上报。业务：品牌蓝v
    int32 co_creator_live_status = 263;//共创者是否处于直播状态，1是，0否。业务：聚星
    string follow_source = 264 ;//关注来源，“co_creator_panel”表示从共创面板来的。业务：聚星
    string client_list_pk_ad_info = 265;//下发列表pk广告信息
    string deeplink_app_version = 266;//换端app对应的版本号
    uint32 ad_report_category = 267; // 举报分类
    string ad_report_category_detail = 268; // 举报分类详情
    repeated AdReportType2 ad_report_type2 =269; // 二级举报类型
    int32 action_status = 270;//动作状态。1表示主动【目前用于客户端主动替换安装包】 cyl_add
    double ad_pk_model_score = 271 ;//pk成功广告的客户端模型分，这个分值用来做pk。用客户端模型分pk的方式，才会有值
    int32 is_progress_bar_operated = 272 ;//是否操作过进度条，1表示操作过，没操作过不上报。目前是视频的进度条操作
    // 20240801 联盟bidding相关
    string adn_advertiser_name = 273; // 竞胜方dsp的广告主名称
    string adn_title = 274; // 竞胜方的广告标题
    string media_req_id = 275; // 媒体侧的请求id（用于关联竞胜和点击行为）
    int32 adn_show_type = 276; // 竞胜方dsp本次请求是否展示（非快手广告是否被展示），值如下：0：未展示 1：展示 2：未知
    int32 adn_click_type = 277; // 竞胜方dsp本次pv是否被点击（非快手广告是否被点击），值如下：0：未点击 1：点击 2：未知
    bool is_non_item_click_range = 278; // 是否不计入行为数，默认false（for行为数剔重统计使用）
    uint64 system_update_timestamp = 279; // 请求下记录首次item_click时间戳，首次不打，默认给0（参数废弃，for行为数剔重统计使用）
    uint64 first_item_click_client_timestamp = 280; // 请求下记录首次item_click时间戳，首次不打，默认给0（for行为数剔重统计使用）
    string ad_negative_type_context_list = 281; //负反馈点击上下文场景集合,透传对象ArrayList<AdNegativeType>.toString
    string ad_submit_cause = 282; //提交原因
    AdPositiveType ad_positive_type_context = 283;//正反馈区分上下文场景
    string ad_positive_type_context_list = 284;//正反馈区分上下文场景集合,透传对象ArrayList<AdPositiveType>.toString
    int32 adn_material_type = 285; // 广告竞价失败时，竞价方的素材类型, 1:横版图片, 2:竖版图片, 3:横版视频, 4:竖版视频, 5:三图, 6:横幅, 7:其他, 9999:未回传
    string adn_material_url = 286;
    bool is_from_simple_live_room_to_official = 287; //简易直播间->正式直播间标签
    int32 universe_query_type = 288; // 联盟广告ad与搜索词匹配度
    string search_query = 289; // 联盟广告搜索词
}

message AdReportType2 {
  uint32 type2_id = 1; // 二级举报类型ID
  string type2_text = 2; // 二级举报类型文案
}

message UniverseOperationData {
  int32 universe_operation_type = 1;  // 联盟命中配置类型
  int64 universe_operation_stra_id = 2;  // 联盟命中配置策略 id
  int64 universe_operation_ad_cluster_id = 3;  // 联盟命中配置广告蔟 id
  int64 universe_operation_media_cluster_id = 4;  // 联盟命中配置媒体蔟 id
}

message UniverseClientAdLog {
  int64 page_id = 1;  //  广场场景 id
  int64 sub_page_id = 2;  //  广告位 id
  string grid_unit_id = 3;  //  广告格 unit id
  int32 grid_pos = 4;  //  广告格在广告位的位置
  int32 ad_pos = 5;  //  广告在广告格中的位置
  int32 action = 6;  //  广告场景触发行文
  int32 type = 7;  //  广告格格式标识 id
  string ext_field = 8;  //  客户端透传字段，采用 json 的 key: value 结构，方便sdk用户自定义埋点
  string app_id = 9; // app 标识 id
  string vistor_id = 10; // 用户标识 id
  string sdk_version = 11;  //  sdk版本号
  string protocol_version = 12;  //  协议版本号
  int32 ad_place_position = 13;  // 信息流广告位的总体排序，如：4、24、45等
  int32 pos_sequence = 14; // 信息流广告位在一刷之内实际的展示位置，如信息流的第4位、第5位
  int64 pos_id = 15; // 广告位唯一标识 id
  string context_provider = 16; // 流量上下文的提供方信息，可复用于：小游戏提供方Id，视频片源方 等
  string enter_action = 17; // 视频进入播放页的方式
  SplashDisplayType splash_display_type = 18[deprecated = true]; // 开屏广告的展示方式
  int32 refresh_page_num = 19; // 顶部刷新的page_id为1，底部加载page_id递增
  int64 refresh_top_page_ts = 20; // 本次page所在的顶部刷新时间戳
  int64 medium_uid = 21; // 联盟开发者 id
  string kpn = 22; // kpn参数，例如：KUAISHOU_ANTIMAN_APP
  string button_detail = 23; // 广告点击按钮文案描述
  AdStyleSub ad_style_sub = 24; // 联盟区分广告版式
  int32 action_bar_style = 25; // 样式配置1，2 ，3，4，5，10，11，12
  int32 action_bar_load_time = 26[deprecated = true]; // 上下滑场景按vedio time百分比控制，取整，最小为1s
  int64 real_show_delay_time = 27[deprecated = true]; // 变色时间
  int64 card_delay_time = 28[deprecated = true]; // 通用卡片出现时间
  int64 card_show_time = 29[deprecated = true]; // 通用卡片展示时间
  int64 returned_second_price = 30; // 联盟作为dsp竞价成功后, adx媒体返回的二价, 单位:厘/单次曝光
  int64 ecpm = 31;  // 联盟作为dsp竞价时的ecpm出价，预估的千次展现的收益，单位: 厘/单次曝光
  int64 cooperation_mode = 32;  // 联盟媒体接入方式
  int32 sdk_type = 33;  // sdk类型: 1:广告 2:广告和内容
  int64 rtb_ecpm = 34;  // rtb 竞价 ecpm, 单位厘/单次曝光
  int64 rtb_virtual_price = 35;  // rtb 实际扣费补贴金，单位厘/单次曝光
  float returned_second_price_precise = 36; // returned_second_price的浮点表示. 联盟作为dsp竞价成功后, adx媒体返回的二价(精确值), 单位:厘/单次曝光
  float ecpm_precise = 37; // ecpm的浮点表示. 联盟作为dsp竞价时的ecpm出价，预估的千次展现的收益(精确值)，单位: 厘/单次曝光
  bool is_live_stream_cut_ad = 38; //
  //20211025 add
  int32 display_index = 39; ///商品展现序号
  // 20220105 add
  int64 media_first_industry_id = 40;  // 媒体一级行业
  int64 media_second_industry_id = 41;  // 媒体二级行业
  RequestDeviceInfo request_device_info = 42; // 客户端请求里的设备信息, 反作弊专用
  RewardingInfo rewarding_info = 43[deprecated=true];  // 激励信息
  repeated uint32 feature_freq_type = 44; // 带频控的功能的列表, 枚举见:kuaishou-ad-new-biz-proto:universe_feature_freq.proto
  bool is_universe_outer_live_ad = 45; // 联盟外循环行业直播直投广告
  int64 rule_id = 46;  // 衍生物料生产模版 id
  bool is_inspire_live = 47;  // 是否为激励直播流量
  float rtb_ecpm_share = 48;  // rtb_ecpm的分成后价格, 单位:厘/单次曝光
  float rtb_virtual_price_precise = 49;  // rtb实际扣费补贴金的浮点表示，单位厘/单次曝光
}

message ClientAdLog {
  // 触发事件类型
  AdActionType action_type = 1;

  // ad, in feed
  uint64 creative_id = 2;
  string charge_info = 3;
  AdSourceType source_type = 4; // client 不需要了解，原样传回即可
  AdConversionType conversion_type = 5; // client 不需要了解，原样传回即可

  // photo
  uint64 photo_id = 6;
  uint64 author_id = 7;
  uint64 llsid = 8;
  string exp_tag = 9;

  // device info for ios
  string idfa = 10;

  // device info for android
  string imei = 11;
  string android_id = 12;
  string mac = 13; // 需要大写并且用:分割，例如 88:CB:87:C1:19:DB
  string advertising_id = 14;
  string oaid = 15; // Open Anonymous Device Identifier，匿名设备标识符，功能类似于IMEI，后续IMEI可能无法获取用此OAID来替代

  string phone_model = 16; // 手机型号
  uint64 system_update_timestamp = 17; // 系统更新时间，毫秒
  uint64 system_boot_timestamp = 18; // 手机启动时间，秒
  string ua = 19; // 浏览器user-agent

  // Optional, android downloader
  uint64 total_length = 20;
  uint64 downloaded_length = 21;

  ItemClickType item_click_type = 22;
  uint64 order_id = 23; //社交广告和粉丝头条的订单Id

  ReportType report_type = 24; //广告举报类型
  string report_detail = 25; //广告举报详情
  string ext_data = 26; //各业务独立的扩展data，如dsp，fanstop，social，merchant，gr等
  string photo_page = 27; //页面请求类型
  uint32 caption_url_index = 28; //正文url列表点击索引值

  string s_photo_id = 29;
  string s_author_id = 30;

  string client_ext_data = 31; //各业务独立的扩展data，与ext_data的区别是client_ext_data的内容可以由客户端自由填写，ext_data的内容来自于后端透传

  ClientParams client_params = 32; // 用于放置扩展业务信息

  AdPosition ad_position = 33; // 用于判断广告出现的场景，如：开屏、信息流、后贴等, 客户端必须上传

  //livestream
  string live_stream_id = 34;

  uint64 cover_id = 35; // 封面id
  DisplayType display_type = 36; // 展示样式

  UniverseClientAdLog universe_client_ad_log = 37;  //  客户端SDK需要上报的新字段
  string ftt = 38;  // 免流卡标识字段

  uint64 feed_id = 39; // 话题ID
  bool hide_label = 40; // 话题页是否隐藏广告标
  uint64 client_timestamp = 41;
  uint32 photo_play_count = 42; //标识作品播放的次数
  string test_event_type = 43; // 当ActionType=AD_EVENT_TEST时有效，主要用于区分临时样式特殊行为具体值
  string client_h5_card_ext_data = 44; // 客户端H5卡片页面上报的打点json串
  ActionSourceType action_source_type = 45; // 区分事件来源
  uint64 mission_id = 46;
  uint64 task_id = 47;
  uint64 interviewee_id = 48;  //被访问者id,在直播间对非本直播间主播的第三者的操作时，上报第三者的user_id

  //直播预约投快享
  string live_reservation_id = 49; //直播预约ID
  uint64 live_reservation_author_id = 50; //直播预约主播ID
  string order_source =  51; //订单来源，TRAFFIC:流量中台，FANS:粉条
  string traffic_source = 52; //流量来源，RECO : reco，FANS: 粉条
  // caid相关字段【ios未来idfa可能获取不到，caid会取代idfa】
  string caid = 53; // 当前最新获取的caid，主站使用current_caid
  string previous_caid = 54; // 上个版本的caid信息，信通院的算法会更新；CAID可能会变；主站使用last_caid
  string caid_version = 55; // 信通院用来标示CAID的版本号，对齐主站

  string plc_ext_data = 56; //plc相关扩展data，如直播预约投粉条贴片信息等
  string did = 57; //did，类似于device_id，切换账户、退出登录态时did不变，但是重装微信或者删除小程序后did会重置
  string open_id = 58; //跟微信号一一对应

  int64 plc_biz_type = 59; //plc业务类型，遵循客户端要求不使用enum方式，具体枚举值含义见https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=369671614

  string previous_caid_version = 60; //上个版本的CAID的版本号，对齐主站；主站使用last_caid_version

  uint32 ad_gap = 61;//同类广告作品(信息流-信息流，外部粉条-外部粉条)两个广告中间自然作品曝光个数
  uint32 ad_gap_all = 62;//所有广告作品(信息流-信息流 + 外部粉条-外部粉条)两个广告中间自然作品曝光个数
  uint32 ad_type_for_gap = 63;//信息流资源位(含品牌GD投放信息流资源位广告)、外部粉条资源位
  AdConversionType sup_conversion_type = 64; // 高优(超级)转化类型，和conversion_type公用pb枚举，存在sup_conversion_type时，ActionBar点击使用sup_conversion_type，nonActionBar点击使用conversion_type  https://docs.corp.kuaishou.com/d/home/<USER>
  string gid = 65;  // 设备指纹 gid String

  string survey_id = 66;//问卷ID
  string survey_reasons_id = 67;//用户所选的问卷选项ID

   // feedLogContext
  string feed_log_context = 68; // 统一的流量属性内容大约"{\"stidContainer\":\"xxx\"}" jsonString 具体方案：https://docs.corp.kuaishou.com/k/home/<USER>/fcACBZHzyCTwoNTAe7p5fcVZQ
  //20210922 add
  uint32 ad_type_for_gap_last = 69;//上一个相邻广告资源位类型：int（上一个广告曝光的ad_type_for_gap）
  uint32 focus_user = 70 ;//是否头部用户,1为是，0为否
  //20221017 add 蓝v添加
  string ext_data2 = 71 ;//蓝v扩展字段，后续其他业务的扩展字段也可以复用这个，和ext_data功能一致
  uint32 ad_gap_all_side_window = 72 ;//从侧滑小窗出来之后，侧滑小窗最后一个广告和精选页两个广告中间自然作品曝光个数。
}

// 用于POI客户端日志上报, 跟ClientAdLog定义相似, 同名字段含义相同
message ClientPoiLog {
  AdActionType action_type = 1; // 触发事件类型

  string charge_info = 2; // 业务用不到, 透传
  AdSourceType source_type = 3; // 来源类别, 如MEITUAN_POI
  AdConversionType conversion_type = 4; // 转化类型, OPEN_HTML5, 后续可能会新增DEEPLINK吊起

  // photo
  uint64 photo_id = 5; // POI_LABEL所在作品photo_id
  uint64 author_id = 6; // POI_LABEL所在作品author_id
  uint64 llsid = 7; // 请求id
  string exp_tag = 8; // 暂时不传

  // device info for ios
  string idfa = 9;

  // device info for android
  string imei = 10; //
  string android_id = 11;
  string mac = 12; // 需要大写并且用:分割，例如 88:CB:87:C1:19:DB
  string advertising_id = 13; // 暂时为空

  string photo_page = 14; // 请求页面的类型: f=follow，n=nearby，h=hot，p=profile

  int64 poi_id = 15; // 我方地图的poi_id
  string r_poi_id = 16; // 第三方的poi_id, 没有可以不传

  string oaid = 17; // Open Anonymous Device Identifier，匿名设备标识符，功能类似于IMEI，后续IMEI可能无法获取用此OAID来替代
}

// 用于记录点击上报位置
message ClickPositionLog {
  int32 ad_item_width = 1; // 实际广告位的宽
  int32 ad_item_height = 2; //  实际广告位的高
  int32 ad_down_x = 3; // 用户手指按下的横坐标
  int32 ad_down_y = 4; // 用户手指按下的纵坐标
  int32 ad_up_x = 5; // 用户手指离开的横坐标
  int32 ad_up_y = 6; // 用户手指离开的纵坐标
}

// 客户端请求上报设备信息
message RequestDeviceInfo {
    string device_id = 1;
    string imei = 2;
    string idfa = 3;
    string android_id = 4;
    string oaid = 5;
    string mac = 6;
    string idfv = 7;
    int32 screen_width = 8;  // 屏幕宽度
    int32 screen_height = 9;  // 屏幕高度
}

// 激励信息
message RewardingInfo {
  int64 task_id = 1;  // 任务id
  repeated TaskTreeInfo task_tree_info = 2; // 任务树信息，从顶级任务到当前任务
  bool dark_ad = 3; // 是否暗投
}

// 激励任务树信息
message TaskTreeInfo {
  int64 task_id = 1;  // 任务id
}