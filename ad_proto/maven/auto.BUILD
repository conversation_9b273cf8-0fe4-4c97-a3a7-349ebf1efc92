'''auto generate from ad.crm.internal.cycle.label/ad_crm_internal_cycle_label_base.proto'''
proto_library (
  name = 'ad__crm__internal__cycle__label_ad_crm_internal_cycle_label_base__proto',
  srcs = [
    'ad.crm.internal.cycle.label/ad_crm_internal_cycle_label_base.proto'
  ],
)

'''auto generate from ad.crm.internal.cycle.label/ad_crm_internal_cycle_label_service.proto'''
proto_library (
  name = 'ad__crm__internal__cycle__label_ad_crm_internal_cycle_label_service__proto',
  srcs = [
    'ad.crm.internal.cycle.label/ad_crm_internal_cycle_label_service.proto'
  ],
  deps = [
    ':ad__crm__internal__cycle__label_ad_crm_internal_cycle_label_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad_adr.proto'''
proto_library (
  name = 'ad_adr__proto',
  srcs = [
    'ad_adr.proto'
  ],
  deps = [
    ':ad_adr_meta__proto',
    ':ad_adr_stream__proto',
    ':ad_adr_threshold__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad_adr_meta.proto'''
proto_library (
  name = 'ad_adr_meta__proto',
  srcs = [
    'ad_adr_meta.proto'
  ],
)

'''auto generate from ad_adr_stream.proto'''
proto_library (
  name = 'ad_adr_stream__proto',
  srcs = [
    'ad_adr_stream.proto'
  ],
)

'''auto generate from ad_adr_threshold.proto'''
proto_library (
  name = 'ad_adr_threshold__proto',
  srcs = [
    'ad_adr_threshold.proto'
  ],
)

'''auto generate from ad/asset/ad_asset_common.proto'''
proto_library (
  name = 'ad_asset_ad_asset_common__proto',
  srcs = [
    'ad/asset/ad_asset_common.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_asset_ad_asset_sync_log__proto',
  ],
)

'''auto generate from ad/asset/ad_asset_package.proto'''
proto_library (
  name = 'ad_asset_ad_asset_package__proto',
  srcs = [
    'ad/asset/ad_asset_package.proto'
  ],
  deps = [
    ':ad_asset_ad_asset_common__proto',
  ],
)

'''auto generate from ad/asset/ad_asset_photo.proto'''
proto_library (
  name = 'ad_asset_ad_asset_photo__proto',
  srcs = [
    'ad/asset/ad_asset_photo.proto'
  ],
  deps = [
    ':ad_asset_ad_asset_common__proto',
  ],
)

'''auto generate from ad/asset/ad_asset_pic.proto'''
proto_library (
  name = 'ad_asset_ad_asset_pic__proto',
  srcs = [
    'ad/asset/ad_asset_pic.proto'
  ],
  deps = [
    ':ad_asset_ad_asset_common__proto',
  ],
)

'''auto generate from ad/asset/ad_asset_profile_info.proto'''
proto_library (
  name = 'ad_asset_ad_asset_profile_info__proto',
  srcs = [
    'ad/asset/ad_asset_profile_info.proto'
  ],
  deps = [
    ':ad_asset_ad_asset_common__proto',
  ],
)

'''auto generate from ad/asset/ad_asset_service.proto'''
proto_library (
  name = 'ad_asset_ad_asset_service__proto',
  srcs = [
    'ad/asset/ad_asset_service.proto'
  ],
  deps = [
    ':ad_asset_ad_asset_common__proto',
    ':ad_asset_ad_asset_package__proto',
    ':ad_asset_ad_asset_photo__proto',
    ':ad_asset_ad_asset_pic__proto',
    ':ad_asset_ad_asset_profile_info__proto',
    ':ad_asset_ad_asset_text__proto',
    ':ad_asset_ad_photo_package__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/asset/ad_asset_text.proto'''
proto_library (
  name = 'ad_asset_ad_asset_text__proto',
  srcs = [
    'ad/asset/ad_asset_text.proto'
  ],
  deps = [
    ':ad_asset_ad_asset_common__proto',
  ],
)

'''auto generate from ad/asset/ad_photo_package.proto'''
proto_library (
  name = 'ad_asset_ad_photo_package__proto',
  srcs = [
    'ad/asset/ad_photo_package.proto'
  ],
  deps = [
    ':ad_asset_ad_asset_common__proto',
  ],
)

'''auto generate from ad/core/intervention/ad_core_intervention.proto'''
proto_library (
  name = 'ad_core_intervention_ad_core_intervention__proto',
  srcs = [
    'ad/core/intervention/ad_core_intervention.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/coupon/ad_coupon_base.proto'''
proto_library (
  name = 'ad_coupon_ad_coupon_base__proto',
  srcs = [
    'ad/coupon/ad_coupon_base.proto'
  ],
  deps = [
    ':ad_coupon_ad_coupon_enum__proto',
  ],
)

'''auto generate from ad/coupon/ad_coupon_base_x7.proto'''
proto_library (
  name = 'ad_coupon_ad_coupon_base_x7__proto',
  srcs = [
    'ad/coupon/ad_coupon_base_x7.proto'
  ],
)

'''auto generate from ad/coupon/ad_coupon_biz.proto'''
proto_library (
  name = 'ad_coupon_ad_coupon_biz__proto',
  srcs = [
    'ad/coupon/ad_coupon_biz.proto'
  ],
  deps = [
    ':ad_coupon_ad_coupon_enum__proto',
  ],
)

'''auto generate from ad/coupon/ad_coupon_cache.proto'''
proto_library (
  name = 'ad_coupon_ad_coupon_cache__proto',
  srcs = [
    'ad/coupon/ad_coupon_cache.proto'
  ],
  deps = [
    ':ad_coupon_ad_coupon_base__proto',
    ':ad_coupon_ad_coupon_base_x7__proto',
    ':ad_coupon_ad_coupon_biz__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/coupon/ad_coupon_enum.proto'''
proto_library (
  name = 'ad_coupon_ad_coupon_enum__proto',
  srcs = [
    'ad/coupon/ad_coupon_enum.proto'
  ],
)

'''auto generate from ad/dmp/ad_dmp.proto'''
proto_library (
  name = 'ad_dmp_ad_dmp__proto',
  srcs = [
    'ad/dmp/ad_dmp.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/dmp/ad_dmp_keywords.proto'''
proto_library (
  name = 'ad_dmp_ad_dmp_keywords__proto',
  srcs = [
    'ad/dmp/ad_dmp_keywords.proto'
  ],
)

'''auto generate from ad/dmp/ad_dmp_service.proto'''
proto_library (
  name = 'ad_dmp_ad_dmp_service__proto',
  srcs = [
    'ad/dmp/ad_dmp_service.proto'
  ],
  deps = [
    ':ad_dmp_ad_dmp__proto',
    ':ad_dmp_ad_dmp_keywords__proto',
    ':ad_dmp_data_exchange__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/dmp/ad_estimate_service.proto'''
proto_library (
  name = 'ad_dmp_ad_estimate_service__proto',
  srcs = [
    'ad/dmp/ad_estimate_service.proto'
  ],
  deps = [
    ':ad_dmp_ad_dmp__proto',
    ':ad_dmp_ad_dmp_service__proto',
    ':ad_dmp_data_exchange__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/dmp/data_exchange.proto'''
proto_library (
  name = 'ad_dmp_data_exchange__proto',
  srcs = [
    'ad/dmp/data_exchange.proto'
  ],
)

'''auto generate from ad/dpa/ad_dpa_common.proto'''
proto_library (
  name = 'ad_dpa_ad_dpa_common__proto',
  srcs = [
    'ad/dpa/ad_dpa_common.proto'
  ],
)

'''auto generate from ad/dpa/ad_dpa_library_service.proto'''
proto_library (
  name = 'ad_dpa_ad_dpa_library_service__proto',
  srcs = [
    'ad/dpa/ad_dpa_library_service.proto'
  ],
  deps = [
    ':ad_dpa_ad_dpa_common__proto',
    ':ad_dpa_ad_dpa_library_service_v2__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/dpa/ad_dpa_library_service_v2.proto'''
proto_library (
  name = 'ad_dpa_ad_dpa_library_service_v2__proto',
  srcs = [
    'ad/dpa/ad_dpa_library_service_v2.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/dpa/ad_dpa_product_core_service.proto'''
proto_library (
  name = 'ad_dpa_ad_dpa_product_core_service__proto',
  srcs = [
    'ad/dpa/ad_dpa_product_core_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    ':ad_dpa_ad_dpa_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/dsp/ad_dsp_base_rpc_service.proto'''
proto_library (
  name = 'ad_dsp_ad_dsp_base_rpc_service__proto',
  srcs = [
    'ad/dsp/ad_dsp_base_rpc_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_resource_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad_dsp_auto_build_service.proto'''
proto_library (
  name = 'ad_dsp_auto_build_service__proto',
  srcs = [
    'ad_dsp_auto_build_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/fanstop/biz/fans_top_biz.proto'''
proto_library (
  name = 'ad_fanstop_biz_fans_top_biz__proto',
  srcs = [
    'ad/fanstop/biz/fans_top_biz.proto'
  ],
)

'''auto generate from ad/fanstop/boost/boost_hosting_service.proto'''
proto_library (
  name = 'ad_fanstop_boost_boost_hosting_service__proto',
  srcs = [
    'ad/fanstop/boost/boost_hosting_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/fanstop/fans_top_common.proto'''
proto_library (
  name = 'ad_fanstop_fans_top_common__proto',
  srcs = [
    'ad/fanstop/fans_top_common.proto'
  ],
  deps = [
    ':ad_fanstop_fans_top_new_force_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/fanstop/fans_top_count_service.proto'''
proto_library (
  name = 'ad_fanstop_fans_top_count_service__proto',
  srcs = [
    'ad/fanstop/fans_top_count_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/fanstop/fans_top_new_force_service.proto'''
proto_library (
  name = 'ad_fanstop_fans_top_new_force_service__proto',
  srcs = [
    'ad/fanstop/fans_top_new_force_service.proto'
  ],
  deps = [
    ':ad_fanstop_fans_top_count_service__proto',
    ':ad_fanstop_fans_top_order_service__proto',
    ':ad_fanstop_message_fans_top_new_force__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/fanstop/fans_top_order_service.proto'''
proto_library (
  name = 'ad_fanstop_fans_top_order_service__proto',
  srcs = [
    'ad/fanstop/fans_top_order_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/fanstop/message/fans_top_log_message.proto'''
proto_library (
  name = 'ad_fanstop_message_fans_top_log_message__proto',
  srcs = [
    'ad/fanstop/message/fans_top_log_message.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:fanstop_common_fans_top_enums__proto',
    ':ad_fanstop_biz_fans_top_biz__proto',
  ],
)

'''auto generate from ad/fanstop/message/fans_top_new_force.proto'''
proto_library (
  name = 'ad_fanstop_message_fans_top_new_force__proto',
  srcs = [
    'ad/fanstop/message/fans_top_new_force.proto'
  ],
)

'''auto generate from ad_live_photo_relation_service.proto'''
proto_library (
  name = 'ad_live_photo_relation_service__proto',
  srcs = [
    'ad_live_photo_relation_service.proto'
  ],
  deps = [
    ':common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/mms/ad_esp_mms_common.proto'''
proto_library (
  name = 'ad_mms_ad_esp_mms_common__proto',
  srcs = [
    'ad/mms/ad_esp_mms_common.proto'
  ],
)

'''auto generate from ad/mms/ad_esp_mms_rpc.proto'''
proto_library (
  name = 'ad_mms_ad_esp_mms_rpc__proto',
  srcs = [
    'ad/mms/ad_esp_mms_rpc.proto'
  ],
  deps = [
    ':ad_mms_ad_esp_mms_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/moss/ad_material_opt.proto'''
proto_library (
  name = 'ad_moss_ad_material_opt__proto',
  srcs = [
    'ad/moss/ad_material_opt.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/moss/ad_material_product.proto'''
proto_library (
  name = 'ad_moss_ad_material_product__proto',
  srcs = [
    'ad/moss/ad_material_product.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad_photo_item_matching.proto'''
proto_library (
  name = 'ad_photo_item_matching__proto',
  srcs = [
    'ad_photo_item_matching.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/platform/ad_core_asset.proto'''
proto_library (
  name = 'ad_platform_ad_core_asset__proto',
  srcs = [
    'ad/platform/ad_core_asset.proto'
  ],
  deps = [
    ':ad_platform_ad_core_enums__proto',
  ],
)

'''auto generate from ad/platform/ad_core_campaign.proto'''
proto_library (
  name = 'ad_platform_ad_core_campaign__proto',
  srcs = [
    'ad/platform/ad_core_campaign.proto'
  ],
  deps = [
    ':ad_platform_ad_core_enums__proto',
  ],
)

'''auto generate from ad/platform/ad_core_creative.proto'''
proto_library (
  name = 'ad_platform_ad_core_creative__proto',
  srcs = [
    'ad/platform/ad_core_creative.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    ':ad_platform_ad_core_enums__proto',
  ],
)

'''auto generate from ad/platform/ad_core_enums.proto'''
proto_library (
  name = 'ad_platform_ad_core_enums__proto',
  srcs = [
    'ad/platform/ad_core_enums.proto'
  ],
)

'''auto generate from ad/platform/ad_core_hosting.proto'''
proto_library (
  name = 'ad_platform_ad_core_hosting__proto',
  srcs = [
    'ad/platform/ad_core_hosting.proto'
  ],
  deps = [
    ':ad_platform_ad_core_campaign__proto',
    ':ad_platform_ad_core_creative__proto',
    ':ad_platform_ad_core_enums__proto',
    ':ad_platform_ad_core_unit__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/platform/ad_core_live.proto'''
proto_library (
  name = 'ad_platform_ad_core_live__proto',
  srcs = [
    'ad/platform/ad_core_live.proto'
  ],
  deps = [
    ':ad_platform_ad_core_asset__proto',
    ':ad_platform_ad_core_creative__proto',
    ':ad_platform_ad_core_enums__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/platform/ad_core_material.proto'''
proto_library (
  name = 'ad_platform_ad_core_material__proto',
  srcs = [
    'ad/platform/ad_core_material.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/platform/ad_core_platform_common.proto'''
proto_library (
  name = 'ad_platform_ad_core_platform_common__proto',
  srcs = [
    'ad/platform/ad_core_platform_common.proto'
  ],
  deps = [
    ':ad_platform_ad_core_target__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/platform/ad_core_search_word.proto'''
proto_library (
  name = 'ad_platform_ad_core_search_word__proto',
  srcs = [
    'ad/platform/ad_core_search_word.proto'
  ],
  deps = [
    ':ad_platform_ad_core_material__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/platform/ad_core_target.proto'''
proto_library (
  name = 'ad_platform_ad_core_target__proto',
  srcs = [
    'ad/platform/ad_core_target.proto'
  ],
)

'''auto generate from ad/platform/ad_core_unit.proto'''
proto_library (
  name = 'ad_platform_ad_core_unit__proto',
  srcs = [
    'ad/platform/ad_core_unit.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    ':ad_platform_ad_core_enums__proto',
    ':ad_platform_ad_core_target__proto',
  ],
)

'''auto generate from ad/platform/ad_ecom_base.proto'''
proto_library (
  name = 'ad_platform_ad_ecom_base__proto',
  srcs = [
    'ad/platform/ad_ecom_base.proto'
  ],
)

'''auto generate from ad/platform/ad_ecom_creative.proto'''
proto_library (
  name = 'ad_platform_ad_ecom_creative__proto',
  srcs = [
    'ad/platform/ad_ecom_creative.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    ':ad_platform_ad_core_enums__proto',
  ],
)

'''auto generate from ad/platform/ad_ecom_hosting.proto'''
proto_library (
  name = 'ad_platform_ad_ecom_hosting__proto',
  srcs = [
    'ad/platform/ad_ecom_hosting.proto'
  ],
  deps = [
    ':ad_platform_ad_core_search_word__proto',
    ':ad_platform_ad_ecom_base__proto',
    ':ad_platform_ad_ecom_creative__proto',
    ':ad_platform_ad_ecom_unit__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/platform/ad_ecom_unit.proto'''
proto_library (
  name = 'ad_platform_ad_ecom_unit__proto',
  srcs = [
    'ad/platform/ad_ecom_unit.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    ':ad_platform_ad_core_enums__proto',
    ':ad_platform_ad_core_search_word__proto',
    ':ad_platform_ad_core_target__proto',
    ':ad_platform_ad_ecom_base__proto',
  ],
)

'''auto generate from ad/platform/ad_esp_combo.proto'''
proto_library (
  name = 'ad_platform_ad_esp_combo__proto',
  srcs = [
    'ad/platform/ad_esp_combo.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    ':ad_platform_ad_core_enums__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ad/platform/ad_esp_service.proto'''
proto_library (
  name = 'ad_platform_ad_esp_service__proto',
  srcs = [
    'ad/platform/ad_esp_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    ':ad_platform_ad_core_enums__proto',
    ':ad_platform_ad_core_target__proto',
    ':ad_platform_ad_ecom_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from aigc/material.proto'''
proto_library (
  name = 'aigc_material__proto',
  srcs = [
    'aigc/material.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
  ],
)

'''auto generate from aigc/service.proto'''
proto_library (
  name = 'aigc_service__proto',
  srcs = [
    'aigc/service.proto'
  ],
  deps = [
    ':aigc_material__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from antispam/antispam_rpc.proto'''
proto_library (
  name = 'antispam_antispam_rpc__proto',
  srcs = [
    'antispam/antispam_rpc.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:antispam_realtime_antispam__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from arrival/frigate_fetch_arrival_signal.proto'''
proto_library (
  name = 'arrival_frigate_fetch_arrival_signal__proto',
  srcs = [
    'arrival/frigate_fetch_arrival_signal.proto'
  ],
  deps = [
    ':frigate_fetch_code_sdk__proto',
  ],
)

'''auto generate from arrival/zt_location_arrival_signal_v2.proto'''
proto_library (
  name = 'arrival_zt_location_arrival_signal_v2__proto',
  srcs = [
    'arrival/zt_location_arrival_signal_v2.proto'
  ],
  deps = [
    ':frigate_fetch_code_sdk__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from audit/kwai_audit_user_mark_label.proto'''
proto_library (
  name = 'audit_kwai_audit_user_mark_label__proto',
  srcs = [
    'audit/kwai_audit_user_mark_label.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from authentication/user_authentication.proto'''
proto_library (
  name = 'authentication_user_authentication__proto',
  srcs = [
    'authentication/user_authentication.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from basedata/basedata.proto'''
proto_library (
  name = 'basedata_basedata__proto',
  srcs = [
    'basedata/basedata.proto'
  ],
)

'''auto generate from basedata/basedata_ares.proto'''
proto_library (
  name = 'basedata_basedata_ares__proto',
  srcs = [
    'basedata/basedata_ares.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    ':basedata_basedata__proto',
    ':basedata_basedata_titan__proto',
  ],
)

'''auto generate from basedata/basedata_meta.proto'''
proto_library (
  name = 'basedata_basedata_meta__proto',
  srcs = [
    'basedata/basedata_meta.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
)

'''auto generate from basedata/basedata_reflux_service.proto'''
proto_library (
  name = 'basedata_basedata_reflux_service__proto',
  srcs = [
    'basedata/basedata_reflux_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    ':basedata_basedata__proto',
    ':basedata_basedata_titan__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from basedata/basedata_rhea_service.proto'''
proto_library (
  name = 'basedata_basedata_rhea_service__proto',
  srcs = [
    'basedata/basedata_rhea_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    ':basedata_basedata__proto',
    ':basedata_basedata_ares__proto',
    ':basedata_basedata_reflux_service__proto',
    ':basedata_basedata_titan_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from basedata/basedata_titan.proto'''
proto_library (
  name = 'basedata_basedata_titan__proto',
  srcs = [
    'basedata/basedata_titan.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    ':basedata_basedata__proto',
    ':basedata_basedata_meta__proto',
  ],
)

'''auto generate from basedata/basedata_titan_service.proto'''
proto_library (
  name = 'basedata_basedata_titan_service__proto',
  srcs = [
    'basedata/basedata_titan_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    ':basedata_basedata__proto',
    ':basedata_basedata_ares__proto',
    ':basedata_basedata_titan__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from c/kwaishop_product_c_common_info.proto'''
proto_library (
  name = 'c_kwaishop_product_c_common_info__proto',
  srcs = [
    'c/kwaishop_product_c_common_info.proto'
  ],
)

'''auto generate from c/kwaishop_product_dynamicattr_service.proto'''
proto_library (
  name = 'c_kwaishop_product_dynamicattr_service__proto',
  srcs = [
    'c/kwaishop_product_dynamicattr_service.proto'
  ],
  deps = [
    ':kwaishop_product_c_baseinfo_response_object__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from c/kwaishop_product_jinniu_response_object.proto'''
proto_library (
  name = 'c_kwaishop_product_jinniu_response_object__proto',
  srcs = [
    'c/kwaishop_product_jinniu_response_object.proto'
  ],
)

'''auto generate from colossus/common_item.proto'''
proto_library (
  name = 'colossus_common_item__proto',
  srcs = [
    'colossus/common_item.proto'
  ],
)

'''auto generate from com/kuaishou/kwaishop/product/platformmanage/realtimeindex/dto.proto'''
proto_library (
  name = 'com_kuaishou_kwaishop_product_platformmanage_realtimeindex_dto__proto',
  srcs = [
    'com/kuaishou/kwaishop/product/platformmanage/realtimeindex/dto.proto'
  ],
)

'''auto generate from com/kuaishou/kwaishop/product/platformmanage/realtimeindex/item_realtime_index_snapshot.proto'''
proto_library (
  name = 'com_kuaishou_kwaishop_product_platformmanage_realtimeindex_item_realtime_index_snapshot__proto',
  srcs = [
    'com/kuaishou/kwaishop/product/platformmanage/realtimeindex/item_realtime_index_snapshot.proto'
  ],
)

'''auto generate from com/kuaishou/kwaishop/product/platformmanage/realtimeindex/service.proto'''
proto_library (
  name = 'com_kuaishou_kwaishop_product_platformmanage_realtimeindex_service__proto',
  srcs = [
    'com/kuaishou/kwaishop/product/platformmanage/realtimeindex/service.proto'
  ],
  deps = [
    ':com_kuaishou_kwaishop_product_platformmanage_realtimeindex_dto__proto',
    ':com_kuaishou_kwaishop_product_platformmanage_realtimeindex_item_realtime_index_snapshot__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from common.proto'''
proto_library (
  name = 'common__proto',
  srcs = [
    'common.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
  ],
)

'''auto generate from common/api_feed_type.proto'''
proto_library (
  name = 'common_api_feed_type__proto',
  srcs = [
    'common/api_feed_type.proto'
  ],
)

'''auto generate from common/base.proto'''
proto_library (
  name = 'common_base__proto',
  srcs = [
    'common/base.proto'
  ],
)

'''auto generate from common/blob_store.proto'''
proto_library (
  name = 'common_blob_store__proto',
  srcs = [
    'common/blob_store.proto'
  ],
)

'''auto generate from common/bucket.proto'''
proto_library (
  name = 'common_bucket__proto',
  srcs = [
    'common/bucket.proto'
  ],
)

'''auto generate from common/common_request_response.proto'''
proto_library (
  name = 'common_common_request_response__proto',
  srcs = [
    'common/common_request_response.proto'
  ],
)

'''auto generate from common/ids.proto'''
proto_library (
  name = 'common_ids__proto',
  srcs = [
    'common/ids.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
  ],
)

'''auto generate from common/product_platform.proto'''
proto_library (
  name = 'common_product_platform__proto',
  srcs = [
    'common/product_platform.proto'
  ],
)

'''auto generate from common/raw_message.proto'''
proto_library (
  name = 'common_raw_message__proto',
  srcs = [
    'common/raw_message.proto'
  ],
)

'''auto generate from corerpc/livestream/live_stream_display_count_query.proto'''
proto_library (
  name = 'corerpc_livestream_live_stream_display_count_query__proto',
  srcs = [
    'corerpc/livestream/live_stream_display_count_query.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/audio_multi_rate_service.proto'''
proto_library (
  name = 'corerpc_photo_audio_multi_rate_service__proto',
  srcs = [
    'corerpc/photo/audio_multi_rate_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''corerpc/photo/common_photo_watermark.proto depend photo_url.proto not ok'''

'''auto generate from corerpc/photo/location_core_service.proto'''
proto_library (
  name = 'corerpc_photo_location_core_service__proto',
  srcs = [
    'corerpc/photo/location_core_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/ohter_platform_photo_author_service.proto'''
proto_library (
  name = 'corerpc_photo_ohter_platform_photo_author_service__proto',
  srcs = [
    'corerpc/photo/ohter_platform_photo_author_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':corerpc_photo_photo_author_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_add_watermark_timestamp.proto'''
proto_library (
  name = 'corerpc_photo_photo_add_watermark_timestamp__proto',
  srcs = [
    'corerpc/photo/photo_add_watermark_timestamp.proto'
  ],
)

'''auto generate from corerpc/photo/photo_api_operation_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_api_operation_service__proto',
  srcs = [
    'corerpc/photo/photo_api_operation_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_author_count_dto.proto'''
proto_library (
  name = 'corerpc_photo_photo_author_count_dto__proto',
  srcs = [
    'corerpc/photo/photo_author_count_dto.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':corerpc_photo_photo_author_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''corerpc/photo/photo_author_feed_service.proto depend blob_store.proto not ok'''

'''auto generate from corerpc/photo/photo_author_large_index_store.proto'''
proto_library (
  name = 'corerpc_photo_photo_author_large_index_store__proto',
  srcs = [
    'corerpc/photo/photo_author_large_index_store.proto'
  ],
)

'''auto generate from corerpc/photo/photo_author_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_author_service__proto',
  srcs = [
    'corerpc/photo/photo_author_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_change.proto'''
proto_library (
  name = 'corerpc_photo_photo_change__proto',
  srcs = [
    'corerpc/photo/photo_change.proto'
  ],
)

'''auto generate from corerpc/photo/photo_corona_corp.proto'''
proto_library (
  name = 'corerpc_photo_photo_corona_corp__proto',
  srcs = [
    'corerpc/photo/photo_corona_corp.proto'
  ],
)

'''auto generate from corerpc/photo/photo_count_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_count_service__proto',
  srcs = [
    'corerpc/photo/photo_count_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_cover.proto'''
proto_library (
  name = 'corerpc_photo_photo_cover__proto',
  srcs = [
    'corerpc/photo/photo_cover.proto'
  ],
)

'''auto generate from corerpc/photo/photo_crawler.proto'''
proto_library (
  name = 'corerpc_photo_photo_crawler__proto',
  srcs = [
    'corerpc/photo/photo_crawler.proto'
  ],
)

'''auto generate from corerpc/photo/photo_delogo_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_delogo_service__proto',
  srcs = [
    'corerpc/photo/photo_delogo_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_download_ended.proto'''
proto_library (
  name = 'corerpc_photo_photo_download_ended__proto',
  srcs = [
    'corerpc/photo/photo_download_ended.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
)

'''auto generate from corerpc/photo/photo_edit_upload.proto'''
proto_library (
  name = 'corerpc_photo_photo_edit_upload__proto',
  srcs = [
    'corerpc/photo/photo_edit_upload.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kuaishou_music_music__proto',
    ':kuaishou_photo_photo_meta__proto',
  ],
)

'''auto generate from corerpc/photo/photo_edit_upload_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_edit_upload_service__proto',
  srcs = [
    'corerpc/photo/photo_edit_upload_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_feed_view_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_feed_view_service__proto',
  srcs = [
    'corerpc/photo/photo_feed_view_service.proto'
  ],
  deps = [
    ':corerpc_photo_photo_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_filter_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_filter_service__proto',
  srcs = [
    'corerpc/photo/photo_filter_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_frame_upload.proto'''
proto_library (
  name = 'corerpc_photo_photo_frame_upload__proto',
  srcs = [
    'corerpc/photo/photo_frame_upload.proto'
  ],
  deps = [
    ':corerpc_photo_photo_upload_param__proto',
    ':kuaishou_photo_photo_frame__proto',
  ],
)

'''auto generate from corerpc/photo/photo_generate_sprite.proto'''
proto_library (
  name = 'corerpc_photo_photo_generate_sprite__proto',
  srcs = [
    'corerpc/photo/photo_generate_sprite.proto'
  ],
)

'''auto generate from corerpc/photo/photo_id_seq_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_id_seq_service__proto',
  srcs = [
    'corerpc/photo/photo_id_seq_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_info_shield_reason.proto'''
proto_library (
  name = 'corerpc_photo_photo_info_shield_reason__proto',
  srcs = [
    'corerpc/photo/photo_info_shield_reason.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
)

'''auto generate from corerpc/photo/photo_like.proto'''
proto_library (
  name = 'corerpc_photo_photo_like__proto',
  srcs = [
    'corerpc/photo/photo_like.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
)

'''auto generate from corerpc/photo/photo_meta_zstd_cache.proto'''
proto_library (
  name = 'corerpc_photo_photo_meta_zstd_cache__proto',
  srcs = [
    'corerpc/photo/photo_meta_zstd_cache.proto'
  ],
)

'''auto generate from corerpc/photo/photo_mezzanine.proto'''
proto_library (
  name = 'corerpc_photo_photo_mezzanine__proto',
  srcs = [
    'corerpc/photo/photo_mezzanine.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_mock_count.proto'''
proto_library (
  name = 'corerpc_photo_photo_mock_count__proto',
  srcs = [
    'corerpc/photo/photo_mock_count.proto'
  ],
)

'''auto generate from corerpc/photo/photo_mongo_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_mongo_service__proto',
  srcs = [
    'corerpc/photo/photo_mongo_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kuaishou_photo_photo_meta__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_multi_rate_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_multi_rate_service__proto',
  srcs = [
    'corerpc/photo/photo_multi_rate_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_out_site_view_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_out_site_view_service__proto',
  srcs = [
    'corerpc/photo/photo_out_site_view_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''corerpc/photo/photo_p2sp.proto depend increment_cache.proto not ok'''

'''auto generate from corerpc/photo/photo_pending_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_pending_service__proto',
  srcs = [
    'corerpc/photo/photo_pending_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    ':corerpc_photo_photo_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_pk_cover.proto'''
proto_library (
  name = 'corerpc_photo_photo_pk_cover__proto',
  srcs = [
    'corerpc/photo/photo_pk_cover.proto'
  ],
)

'''auto generate from corerpc/photo/photo_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_service__proto',
  srcs = [
    'corerpc/photo/photo_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:music_music__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_edit__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_video_info__proto',
    ':corerpc_photo_photo_change__proto',
    ':corerpc_photo_soundtrack__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_smart_cover.proto'''
proto_library (
  name = 'corerpc_photo_photo_smart_cover__proto',
  srcs = [
    'corerpc/photo/photo_smart_cover.proto'
  ],
)

'''auto generate from corerpc/photo/photo_upload_param.proto'''
proto_library (
  name = 'corerpc_photo_photo_upload_param__proto',
  srcs = [
    'corerpc/photo/photo_upload_param.proto'
  ],
)

'''corerpc/photo/photo_url.proto depend blob_store.proto not ok'''

'''auto generate from corerpc/photo/photo_video_quality.proto'''
proto_library (
  name = 'corerpc_photo_photo_video_quality__proto',
  srcs = [
    'corerpc/photo/photo_video_quality.proto'
  ],
)

'''auto generate from corerpc/photo/photo_viewer.proto'''
proto_library (
  name = 'corerpc_photo_photo_viewer__proto',
  srcs = [
    'corerpc/photo/photo_viewer.proto'
  ],
)

'''auto generate from corerpc/photo/photo_visible_count_dto.proto'''
proto_library (
  name = 'corerpc_photo_photo_visible_count_dto__proto',
  srcs = [
    'corerpc/photo/photo_visible_count_dto.proto'
  ],
)

'''corerpc/photo/photo_watermark.proto depend blob_store.proto not ok'''

'''auto generate from corerpc/photo/photo_watermark_diverse.proto'''
proto_library (
  name = 'corerpc_photo_photo_watermark_diverse__proto',
  srcs = [
    'corerpc/photo/photo_watermark_diverse.proto'
  ],
)

'''auto generate from corerpc/photo/photo_watermark_diverse_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_watermark_diverse_service__proto',
  srcs = [
    'corerpc/photo/photo_watermark_diverse_service.proto'
  ],
  deps = [
    ':corerpc_photo_photo_watermark_diverse__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_watermark_for_ug_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_watermark_for_ug_service__proto',
  srcs = [
    'corerpc/photo/photo_watermark_for_ug_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/photo_watermark_service.proto'''
proto_library (
  name = 'corerpc_photo_photo_watermark_service__proto',
  srcs = [
    'corerpc/photo/photo_watermark_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':corerpc_photo_photo_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/plc_correlation_audit.proto'''
proto_library (
  name = 'corerpc_photo_plc_correlation_audit__proto',
  srcs = [
    'corerpc/photo/plc_correlation_audit.proto'
  ],
)

'''auto generate from corerpc/photo/plc_feature_entry.proto'''
proto_library (
  name = 'corerpc_photo_plc_feature_entry__proto',
  srcs = [
    'corerpc/photo/plc_feature_entry.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kuaishou_ad_ad_service__proto',
    ':kuaishou_fanstop_fans_top_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/plc_mmu_keyword_detect.proto'''
proto_library (
  name = 'corerpc_photo_plc_mmu_keyword_detect__proto',
  srcs = [
    'corerpc/photo/plc_mmu_keyword_detect.proto'
  ],
)

'''auto generate from corerpc/photo/plc_production_entrance.proto'''
proto_library (
  name = 'corerpc_photo_plc_production_entrance__proto',
  srcs = [
    'corerpc/photo/plc_production_entrance.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/recognized_ad_photo_service.proto'''
proto_library (
  name = 'corerpc_photo_recognized_ad_photo_service__proto',
  srcs = [
    'corerpc/photo/recognized_ad_photo_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/photo/soundtrack.proto'''
proto_library (
  name = 'corerpc_photo_soundtrack__proto',
  srcs = [
    'corerpc/photo/soundtrack.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:music_music__proto',
  ],
)

'''auto generate from corerpc/relation/relation_following_service.proto'''
proto_library (
  name = 'corerpc_relation_relation_following_service__proto',
  srcs = [
    'corerpc/relation/relation_following_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':corerpc_relation_relation_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/relation/relation_service.proto'''
proto_library (
  name = 'corerpc_relation_relation_service__proto',
  srcs = [
    'corerpc/relation/relation_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/user/user_cache_service.proto'''
proto_library (
  name = 'corerpc_user_user_cache_service__proto',
  srcs = [
    'corerpc/user/user_cache_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from corerpc/user/user_count.proto'''
proto_library (
  name = 'corerpc_user_user_count__proto',
  srcs = [
    'corerpc/user/user_count.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from crc_dispose_common_enum.proto'''
proto_library (
  name = 'crc_dispose_common_enum__proto',
  srcs = [
    'crc_dispose_common_enum.proto'
  ],
)

'''auto generate from crc_dispose_engine_service.proto'''
proto_library (
  name = 'crc_dispose_engine_service__proto',
  srcs = [
    'crc_dispose_engine_service.proto'
  ],
  deps = [
    ':crc_dispose_common_enum__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from creative_message.proto'''
proto_library (
  name = 'creative_message__proto',
  srcs = [
    'creative_message.proto'
  ],
)

'''auto generate from creative_rtb_service.proto'''
proto_library (
  name = 'creative_rtb_service__proto',
  srcs = [
    'creative_rtb_service.proto'
  ],
  deps = [
    ':creative_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from detail/comment/kwaishop_product_comment_info.proto'''
proto_library (
  name = 'detail_comment_kwaishop_product_comment_info__proto',
  srcs = [
    'detail/comment/kwaishop_product_comment_info.proto'
  ],
  deps = [
    ':detail_kwaishop_product_detail_common__proto',
  ],
)

'''auto generate from detail/kwaishop_product_detail_base.proto'''
proto_library (
  name = 'detail_kwaishop_product_detail_base__proto',
  srcs = [
    'detail/kwaishop_product_detail_base.proto'
  ],
)

'''auto generate from detail/kwaishop_product_detail_common.proto'''
proto_library (
  name = 'detail_kwaishop_product_detail_common__proto',
  srcs = [
    'detail/kwaishop_product_detail_common.proto'
  ],
)

'''auto generate from detail/kwaishop_product_detail_componentized.proto'''
proto_library (
  name = 'detail_kwaishop_product_detail_componentized__proto',
  srcs = [
    'detail/kwaishop_product_detail_componentized.proto'
  ],
  deps = [
    ':detail_kwaishop_product_detail_base__proto',
    ':detail_kwaishop_product_detail_common__proto',
    ':detail_kwaishop_product_detail_dto__proto',
  ],
)

'''auto generate from detail/kwaishop_product_detail_content_card.proto'''
proto_library (
  name = 'detail_kwaishop_product_detail_content_card__proto',
  srcs = [
    'detail/kwaishop_product_detail_content_card.proto'
  ],
  deps = [
    ':detail_kwaishop_product_detail_base__proto',
    ':detail_kwaishop_product_detail_common__proto',
    ':detail_kwaishop_product_detail_dto__proto',
  ],
)

'''auto generate from detail/kwaishop_product_detail_dto.proto'''
proto_library (
  name = 'detail_kwaishop_product_detail_dto__proto',
  srcs = [
    'detail/kwaishop_product_detail_dto.proto'
  ],
  deps = [
    ':detail_comment_kwaishop_product_comment_info__proto',
    ':detail_kwaishop_product_detail_base__proto',
    ':detail_kwaishop_product_detail_common__proto',
    ':detail_kwaishop_product_detail_third_dto__proto',
    ':detail_shop_kwaishop_product_shop_info__proto',
    ':kwaishop_marketing_tools_welfare_ref__proto',
    ':merchant_external__platform_item_merchant_third_coupon_message__proto',
    ':stock_rt_common__proto',
  ],
)

'''auto generate from detail/kwaishop_product_detail_h5_dto.proto'''
proto_library (
  name = 'detail_kwaishop_product_detail_h5_dto__proto',
  srcs = [
    'detail/kwaishop_product_detail_h5_dto.proto'
  ],
  deps = [
    ':detail_comment_kwaishop_product_comment_info__proto',
    ':detail_kwaishop_product_detail_common__proto',
    ':detail_kwaishop_product_detail_dto__proto',
    ':detail_kwaishop_product_half_page__proto',
    ':detail_shop_kwaishop_product_shop_info__proto',
  ],
)

'''auto generate from detail/kwaishop_product_detail_reco_dto.proto'''
proto_library (
  name = 'detail_kwaishop_product_detail_reco_dto__proto',
  srcs = [
    'detail/kwaishop_product_detail_reco_dto.proto'
  ],
  deps = [
    ':detail_kwaishop_product_detail_common__proto',
  ],
)

'''auto generate from detail/kwaishop_product_detail_service.proto'''
proto_library (
  name = 'detail_kwaishop_product_detail_service__proto',
  srcs = [
    'detail/kwaishop_product_detail_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':detail_kwaishop_product_detail_common__proto',
    ':detail_kwaishop_product_detail_componentized__proto',
    ':detail_kwaishop_product_detail_content_card__proto',
    ':detail_kwaishop_product_detail_dto__proto',
    ':detail_kwaishop_product_detail_h5_dto__proto',
    ':detail_kwaishop_product_detail_reco_dto__proto',
    ':detail_kwaishop_product_detail_third_dto__proto',
    ':detail_kwaishop_product_half_page__proto',
    ':kwaishop_mpa_rebate_fulfil_service__proto',
    ':merchant_order_place_base__proto',
    ':merchant_order_place_info_base__proto',
    ':merchant_order_place_purchase_simplify__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from detail/kwaishop_product_detail_third_dto.proto'''
proto_library (
  name = 'detail_kwaishop_product_detail_third_dto__proto',
  srcs = [
    'detail/kwaishop_product_detail_third_dto.proto'
  ],
  deps = [
    ':detail_comment_kwaishop_product_comment_info__proto',
    ':detail_kwaishop_product_detail_base__proto',
    ':detail_kwaishop_product_detail_common__proto',
    ':detail_sellerinfo_kwaishop_product_seller_info__proto',
    ':detail_shop_kwaishop_product_shop_info__proto',
    ':merchant_external__platform_item_merchant_third_coupon_message__proto',
  ],
)

'''auto generate from detail/kwaishop_product_half_page.proto'''
proto_library (
  name = 'detail_kwaishop_product_half_page__proto',
  srcs = [
    'detail/kwaishop_product_half_page.proto'
  ],
  deps = [
    ':detail_kwaishop_product_detail_common__proto',
    ':detail_kwaishop_product_detail_dto__proto',
    ':merchant_order_place_base__proto',
    ':merchant_order_place_info_base__proto',
    ':merchant_order_place_purchase_simplify__proto',
  ],
)

'''auto generate from detail/sellerinfo/kwaishop_product_seller_info.proto'''
proto_library (
  name = 'detail_sellerinfo_kwaishop_product_seller_info__proto',
  srcs = [
    'detail/sellerinfo/kwaishop_product_seller_info.proto'
  ],
)

'''auto generate from detail/shop/kwaishop_product_shop_info.proto'''
proto_library (
  name = 'detail_shop_kwaishop_product_shop_info__proto',
  srcs = [
    'detail/shop/kwaishop_product_shop_info.proto'
  ],
  deps = [
    ':detail_kwaishop_product_detail_common__proto',
  ],
)

'''auto generate from draco_sirius_punish_data_rpc.proto'''
proto_library (
  name = 'draco_sirius_punish_data_rpc__proto',
  srcs = [
    'draco_sirius_punish_data_rpc.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from dsp/service/smart/ad_dsp_smart_service.proto'''
proto_library (
  name = 'dsp_service_smart_ad_dsp_smart_service__proto',
  srcs = [
    'dsp/service/smart/ad_dsp_smart_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from fans_top_gateway_common_service.proto'''
proto_library (
  name = 'fans_top_gateway_common_service__proto',
  srcs = [
    'fans_top_gateway_common_service.proto'
  ],
  deps = [
    ':ad_fanstop_fans_top_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from fans_top_gateway_order_service.proto'''
proto_library (
  name = 'fans_top_gateway_order_service__proto',
  srcs = [
    'fans_top_gateway_order_service.proto'
  ],
  deps = [
    ':ad_fanstop_message_fans_top_log_message__proto',
    ':fans_top_gateway_common_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from fragment/creative_center_common.proto'''
proto_library (
  name = 'fragment_creative_center_common__proto',
  srcs = [
    'fragment/creative_center_common.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
  ],
)

'''auto generate from fragment/creative_center_event.proto'''
proto_library (
  name = 'fragment_creative_center_event__proto',
  srcs = [
    'fragment/creative_center_event.proto'
  ],
  deps = [
    ':fragment_creative_center_common__proto',
  ],
)

'''auto generate from frigate_common_wifi.proto'''
proto_library (
  name = 'frigate_common_wifi__proto',
  srcs = [
    'frigate_common_wifi.proto'
  ],
)

'''auto generate from frigate_fetch_arrvial_info.proto'''
proto_library (
  name = 'frigate_fetch_arrvial_info__proto',
  srcs = [
    'frigate_fetch_arrvial_info.proto'
  ],
)

'''auto generate from frigate_fetch_code_sdk.proto'''
proto_library (
  name = 'frigate_fetch_code_sdk__proto',
  srcs = [
    'frigate_fetch_code_sdk.proto'
  ],
)

'''auto generate from frigate_fetch_ip_location_info.proto'''
proto_library (
  name = 'frigate_fetch_ip_location_info__proto',
  srcs = [
    'frigate_fetch_ip_location_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':frigate_fetch_code_sdk__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from frigate_fetch_location_profiling.proto'''
proto_library (
  name = 'frigate_fetch_location_profiling__proto',
  srcs = [
    'frigate_fetch_location_profiling.proto'
  ],
)

'''auto generate from frigate_fetch_location_profiling_v2.proto'''
proto_library (
  name = 'frigate_fetch_location_profiling_v2__proto',
  srcs = [
    'frigate_fetch_location_profiling_v2.proto'
  ],
  deps = [
    ':frigate_fetch_location_profiling__proto',
  ],
)

'''auto generate from frigate_fetch_location_sdk.proto'''
proto_library (
  name = 'frigate_fetch_location_sdk__proto',
  srcs = [
    'frigate_fetch_location_sdk.proto'
  ],
  deps = [
    ':arrival_frigate_fetch_arrival_signal__proto',
    ':arrival_zt_location_arrival_signal_v2__proto',
    ':frigate_common_wifi__proto',
    ':frigate_fetch_arrvial_info__proto',
    ':frigate_fetch_code_sdk__proto',
    ':frigate_fetch_location_profiling__proto',
    ':frigate_fetch_location_profiling_v2__proto',
    ':geo_frigate_reverse_geo__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from geo/frigate_reverse_geo.proto'''
proto_library (
  name = 'geo_frigate_reverse_geo__proto',
  srcs = [
    'geo/frigate_reverse_geo.proto'
  ],
)

'''auto generate from iludc_service.proto'''
proto_library (
  name = 'iludc_service__proto',
  srcs = [
    'iludc_service.proto'
  ],
  deps = [
    ':umdc_target_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/abtest2/abtest_mapping_id.proto'''
proto_library (
  name = 'kuaishou_abtest2_abtest_mapping_id__proto',
  srcs = [
    'kuaishou/abtest2/abtest_mapping_id.proto'
  ],
)

'''auto generate from kuaishou/ad/ad_base.proto'''
proto_library (
  name = 'kuaishou_ad_ad_base__proto',
  srcs = [
    'kuaishou/ad/ad_base.proto'
  ],
  deps = [
    '//ks/algo-engine-proto/BUILD:algo_engine_proto',
    '//ks/base/abtest2/BUILD:abtest2_proto',
    '//ks/reco_proto/common_reco/leaf/BUILD:common_reco_proto',
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_component__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_dynamic_creative__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_search_ads_query_livestream_tag__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_search_ads_query_user_celebrity__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_search_ads_search_query_feature_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_enrich_user_data__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_galaxy_extern__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_innerloop_iludc_proxy_common__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_nebula_encourage_ad_exchange__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_search_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_tables_table_extend_fields__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_user_action_statictic__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_user_action_unit_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_user_model_score__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:retrieval_retrieval_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_search_realtime_action__proto',
  ],
)

'''auto generate from kuaishou/ad/ad_charge.proto'''
proto_library (
  name = 'kuaishou_ad_ad_charge__proto',
  srcs = [
    'kuaishou/ad/ad_charge.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
)

'''auto generate from kuaishou/ad/ad_component.proto'''
proto_library (
  name = 'kuaishou_ad_ad_component__proto',
  srcs = [
    'kuaishou/ad/ad_component.proto'
  ],
)

'''auto generate from kuaishou/ad/ad_component_assembly.proto'''
proto_library (
  name = 'kuaishou_ad_ad_component_assembly__proto',
  srcs = [
    'kuaishou/ad/ad_component_assembly.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
  ],
)

'''auto generate from kuaishou/ad/ad_dynamic_creative.proto'''
proto_library (
  name = 'kuaishou_ad_ad_dynamic_creative__proto',
  srcs = [
    'kuaishou/ad/ad_dynamic_creative.proto'
  ],
)

'''auto generate from kuaishou/ad/ad_info.proto'''
proto_library (
  name = 'kuaishou_ad_ad_info__proto',
  srcs = [
    'kuaishou/ad/ad_info.proto'
  ],
  deps = [
    '//ks/base/abtest2/BUILD:abtest2_proto',
    '//ks/reco_proto/proto/BUILD:client_log',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_trace_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_service_ad_predict_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_engine_trace_log_ad_simplify_always_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_full_link_pos_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_merchant_ad_merchant__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_tables_table_extend_fields__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_user_reco_base_split_reco_user_text__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_server_show_log__proto',
  ],
)

'''auto generate from kuaishou/ad/ad_log.proto'''
proto_library (
  name = 'kuaishou_ad_ad_log__proto',
  srcs = [
    'kuaishou/ad/ad_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_charge__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_resource_tags__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_social_charge__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_antispam_ad_antispam_clue__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
)

'''auto generate from kuaishou/ad/ad_rank_to_front.proto'''
proto_library (
  name = 'kuaishou_ad_ad_rank_to_front__proto',
  srcs = [
    'kuaishou/ad/ad_rank_to_front.proto'
  ],
)

'''auto generate from kuaishou/ad/ad_resource_service.proto'''
proto_library (
  name = 'kuaishou_ad_ad_resource_service__proto',
  srcs = [
    'kuaishou/ad/ad_resource_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_dsp_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/ad_resource_tags.proto'''
proto_library (
  name = 'kuaishou_ad_ad_resource_tags__proto',
  srcs = [
    'kuaishou/ad/ad_resource_tags.proto'
  ],
)

'''auto generate from kuaishou/ad/ad_server_to_front.proto'''
proto_library (
  name = 'kuaishou_ad_ad_server_to_front__proto',
  srcs = [
    'kuaishou/ad/ad_server_to_front.proto'
  ],
)

'''auto generate from kuaishou/ad/ad_server_to_rank.proto'''
proto_library (
  name = 'kuaishou_ad_ad_server_to_rank__proto',
  srcs = [
    'kuaishou/ad/ad_server_to_rank.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
)

'''auto generate from kuaishou/ad/ad_service.proto'''
proto_library (
  name = 'kuaishou_ad_ad_service__proto',
  srcs = [
    'kuaishou/ad/ad_service.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:client_log',
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_rank_to_front__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_server_to_front__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_target_to_rank__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_trace_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_ueq_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_joint_labeled_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_user_like_ads__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_query_user__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_service_ad_predict_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_detail_ad_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_galaxy_extern__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_rta_rta_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_search_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_session_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_tables_table_extend_fields__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_universe_computility_data__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_universe_delivered_qpon_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_user_action_statictic__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:fanstop_common_fans_top_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_user_info__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/ad_social_charge.proto'''
proto_library (
  name = 'kuaishou_ad_ad_social_charge__proto',
  srcs = [
    'kuaishou/ad/ad_social_charge.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
)

'''auto generate from kuaishou/ad/ad_target_to_rank.proto'''
proto_library (
  name = 'kuaishou_ad_ad_target_to_rank__proto',
  srcs = [
    'kuaishou/ad/ad_target_to_rank.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_tables_table_extend_fields__proto',
  ],
)

'''auto generate from kuaishou/ad/ad_task.proto'''
proto_library (
  name = 'kuaishou_ad_ad_task__proto',
  srcs = [
    'kuaishou/ad/ad_task.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/ad_tensor.proto'''
proto_library (
  name = 'kuaishou_ad_ad_tensor__proto',
  srcs = [
    'kuaishou/ad/ad_tensor.proto'
  ],
)

'''auto generate from kuaishou/ad/ad_trace_filter_condition.proto'''
proto_library (
  name = 'kuaishou_ad_ad_trace_filter_condition__proto',
  srcs = [
    'kuaishou/ad/ad_trace_filter_condition.proto'
  ],
)

'''auto generate from kuaishou/ad/ad_trace_log.proto'''
proto_library (
  name = 'kuaishou_ad_ad_trace_log__proto',
  srcs = [
    'kuaishou/ad/ad_trace_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_trace_filter_condition__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
)

'''auto generate from kuaishou/ad/ad_ueq_info.proto'''
proto_library (
  name = 'kuaishou_ad_ad_ueq_info__proto',
  srcs = [
    'kuaishou/ad/ad_ueq_info.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_action_label.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_action_label__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_action_label.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_adx_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_adx_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_adx_info.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_algorithm_cs_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_algorithm_cs_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_algorithm_cs_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_base.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_base__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_base.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_bid_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_bid_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_bid_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_bid_log.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_bid_log__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_bid_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_bid_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_joint_labeled_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_dsp_base.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_dsp_base__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_dsp_base.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_joint_labeled_log.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_joint_labeled_log__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_joint_labeled_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_component_assembly__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_adx_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_bid_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_dsp_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_live_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_photo_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_pic_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_user_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_detail_ad_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:log_reco_label_set__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_knew_user_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_knew_user_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_knew_user_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_live_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_live_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_live_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_algorithm_cs_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_photo_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_retrieval_live_info__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_photo_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_photo_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_photo_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_retrieval_photo_info__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_pic_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_pic_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_pic_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_photo_info__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_retrieval_live_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_retrieval_live_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_retrieval_live_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_retrieval_photo_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_retrieval_photo_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_retrieval_photo_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_retrieval_user_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_retrieval_user_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_retrieval_user_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_user_hdfs_feature.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_user_hdfs_feature__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_user_hdfs_feature.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_user_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_user_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_user_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_trace_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_action_label__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_knew_user_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_retrieval_user_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_user_hdfs_feature__proto',
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/ad_user_like_ads.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_ad_user_like_ads__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/ad_user_like_ads.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/model/query_user.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_model_query_user__proto',
  srcs = [
    'kuaishou/ad/algorithm/model/query_user.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/search_ads/query_livestream_tag.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_search_ads_query_livestream_tag__proto',
  srcs = [
    'kuaishou/ad/algorithm/search_ads/query_livestream_tag.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/search_ads/query_user_celebrity.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_search_ads_query_user_celebrity__proto',
  srcs = [
    'kuaishou/ad/algorithm/search_ads/query_user_celebrity.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/search_ads/search_query_feature_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_search_ads_search_query_feature_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/search_ads/search_query_feature_info.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/service/ad_bs_user_info.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_service_ad_bs_user_info__proto',
  srcs = [
    'kuaishou/ad/algorithm/service/ad_bs_user_info.proto'
  ],
)

'''auto generate from kuaishou/ad/algorithm/service/ad_predict_service.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_service_ad_predict_service__proto',
  srcs = [
    'kuaishou/ad/algorithm/service/ad_predict_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_component_assembly__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_tensor__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_bid_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_bid_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_joint_labeled_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_user_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_service_ad_bs_user_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_service_feature__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_debug_common_debug_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_debug_data_type__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_embedding_retr_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_search_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:retrieval_sample__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/algorithm/service/feature.proto'''
proto_library (
  name = 'kuaishou_ad_algorithm_service_feature__proto',
  srcs = [
    'kuaishou/ad/algorithm/service/feature.proto'
  ],
)

'''auto generate from kuaishou/ad/antispam/ad_antispam_clue.proto'''
proto_library (
  name = 'kuaishou_ad_antispam_ad_antispam_clue__proto',
  srcs = [
    'kuaishou/ad/antispam/ad_antispam_clue.proto'
  ],
)

'''auto generate from kuaishou/ad/antispam/ad_antispam_u_msg.proto'''
proto_library (
  name = 'kuaishou_ad_antispam_ad_antispam_u_msg__proto',
  srcs = [
    'kuaishou/ad/antispam/ad_antispam_u_msg.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_charge__proto',
  ],
)

'''auto generate from kuaishou/ad/antispam/service/union_ad_request_service.proto'''
proto_library (
  name = 'kuaishou_ad_antispam_service_union_ad_request_service__proto',
  srcs = [
    'kuaishou/ad/antispam/service/union_ad_request_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_antispam_ad_antispam_u_msg__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_athena_universe_interface__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_union_server_show__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/asset/ad_asset_sync_log.proto'''
proto_library (
  name = 'kuaishou_ad_asset_ad_asset_sync_log__proto',
  srcs = [
    'kuaishou/ad/asset/ad_asset_sync_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
)

'''auto generate from kuaishou/ad/athena_universe_interface.proto'''
proto_library (
  name = 'kuaishou_ad_athena_universe_interface__proto',
  srcs = [
    'kuaishou/ad/athena_universe_interface.proto'
  ],
  deps = [
    '//ks/base/abtest2/BUILD:abtest2_proto',
    '//ks/reco_proto/common_reco/leaf/BUILD:common_reco_proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_task__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_full_link_pos_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_search_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_user_action_statictic__proto',
  ],
)

'''auto generate from kuaishou/ad/brand/creative/ad_brand_creative_info.proto'''
proto_library (
  name = 'kuaishou_ad_brand_creative_ad_brand_creative_info__proto',
  srcs = [
    'kuaishou/ad/brand/creative/ad_brand_creative_info.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/brand_rta/ad_brand_rta_service.proto'''
proto_library (
  name = 'kuaishou_ad_brand_rta_ad_brand_rta_service__proto',
  srcs = [
    'kuaishou/ad/brand_rta/ad_brand_rta_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/brandv2/account/ad_brand_account_service.proto'''
proto_library (
  name = 'kuaishou_ad_brandv2_account_ad_brand_account_service__proto',
  srcs = [
    'kuaishou/ad/brandv2/account/ad_brand_account_service.proto'
  ],
  deps = [
    ':ad_dsp_ad_dsp_base_rpc_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/brandv2/activity/ad_brand_activity_service.proto'''
proto_library (
  name = 'kuaishou_ad_brandv2_activity_ad_brand_activity_service__proto',
  srcs = [
    'kuaishou/ad/brandv2/activity/ad_brand_activity_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/brandv2/info/ad_brand_info_query_service.proto'''
proto_library (
  name = 'kuaishou_ad_brandv2_info_ad_brand_info_query_service__proto',
  srcs = [
    'kuaishou/ad/brandv2/info/ad_brand_info_query_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_brandv2_account_ad_brand_account_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    ':ad_dsp_ad_dsp_base_rpc_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/brandv2/splash/ad_brand_splash_coin_data.proto'''
proto_library (
  name = 'kuaishou_ad_brandv2_splash_ad_brand_splash_coin_data__proto',
  srcs = [
    'kuaishou/ad/brandv2/splash/ad_brand_splash_coin_data.proto'
  ],
)

'''auto generate from kuaishou/ad/brandv2/unit/ad_brand_live_author_info.proto'''
proto_library (
  name = 'kuaishou_ad_brandv2_unit_ad_brand_live_author_info__proto',
  srcs = [
    'kuaishou/ad/brandv2/unit/ad_brand_live_author_info.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/buyer/ad_buyer_server.proto'''
proto_library (
  name = 'kuaishou_ad_buyer_ad_buyer_server__proto',
  srcs = [
    'kuaishou/ad/buyer/ad_buyer_server.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/common_ad_log.proto'''
proto_library (
  name = 'kuaishou_ad_common_ad_log__proto',
  srcs = [
    'kuaishou/ad/common_ad_log.proto'
  ],
)

'''auto generate from kuaishou/ad/common/enums.proto'''
proto_library (
  name = 'kuaishou_ad_common_enums__proto',
  srcs = [
    'kuaishou/ad/common/enums.proto'
  ],
)

'''auto generate from kuaishou/ad/debug/common_debug_info.proto'''
proto_library (
  name = 'kuaishou_ad_debug_common_debug_info__proto',
  srcs = [
    'kuaishou/ad/debug/common_debug_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_debug_data_type__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_debug_value__proto',
  ],
)

'''auto generate from kuaishou/ad/debug/data_type.proto'''
proto_library (
  name = 'kuaishou_ad_debug_data_type__proto',
  srcs = [
    'kuaishou/ad/debug/data_type.proto'
  ],
)

'''auto generate from kuaishou/ad/debug/value.proto'''
proto_library (
  name = 'kuaishou_ad_debug_value__proto',
  srcs = [
    'kuaishou/ad/debug/value.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_debug_data_type__proto',
  ],
)

'''auto generate from kuaishou/ad/detail_ad_service.proto'''
proto_library (
  name = 'kuaishou_ad_detail_ad_service__proto',
  srcs = [
    'kuaishou/ad/detail_ad_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/embedding_retr_item.proto'''
proto_library (
  name = 'kuaishou_ad_embedding_retr_item__proto',
  srcs = [
    'kuaishou/ad/embedding_retr_item.proto'
  ],
)

'''auto generate from kuaishou/ad/embedding_retr_service.proto'''
proto_library (
  name = 'kuaishou_ad_embedding_retr_service__proto',
  srcs = [
    'kuaishou/ad/embedding_retr_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_embedding_retr_item__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/engine_trace_log/ad_simplify_always_log.proto'''
proto_library (
  name = 'kuaishou_ad_engine_trace_log_ad_simplify_always_log__proto',
  srcs = [
    'kuaishou/ad/engine_trace_log/ad_simplify_always_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_trace_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
)

'''auto generate from kuaishou/ad/enrich_user_data.proto'''
proto_library (
  name = 'kuaishou_ad_enrich_user_data__proto',
  srcs = [
    'kuaishou/ad/enrich_user_data.proto'
  ],
)

'''auto generate from kuaishou/ad/full_link_pos_info.proto'''
proto_library (
  name = 'kuaishou_ad_full_link_pos_info__proto',
  srcs = [
    'kuaishou/ad/full_link_pos_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
)

'''auto generate from kuaishou/ad/galaxy_extern.proto'''
proto_library (
  name = 'kuaishou_ad_galaxy_extern__proto',
  srcs = [
    'kuaishou/ad/galaxy_extern.proto'
  ],
)

'''auto generate from kuaishou/ad/innerloop/iludc_proxy_common.proto'''
proto_library (
  name = 'kuaishou_ad_innerloop_iludc_proxy_common__proto',
  srcs = [
    'kuaishou/ad/innerloop/iludc_proxy_common.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_innerloop_iludc_rpc_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/innerloop/iludc_rpc_common.proto'''
proto_library (
  name = 'kuaishou_ad_innerloop_iludc_rpc_common__proto',
  srcs = [
    'kuaishou/ad/innerloop/iludc_rpc_common.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/merchant/ad_merchant.proto'''
proto_library (
  name = 'kuaishou_ad_merchant_ad_merchant__proto',
  srcs = [
    'kuaishou/ad/merchant/ad_merchant.proto'
  ],
)

'''auto generate from kuaishou/ad/nebula_encourage_ad_exchange.proto'''
proto_library (
  name = 'kuaishou_ad_nebula_encourage_ad_exchange__proto',
  srcs = [
    'kuaishou/ad/nebula_encourage_ad_exchange.proto'
  ],
)

'''auto generate from kuaishou/ad/rta/rta.proto'''
proto_library (
  name = 'kuaishou_ad_rta_rta__proto',
  srcs = [
    'kuaishou/ad/rta/rta.proto'
  ],
)

'''auto generate from kuaishou/ad/rta/rta_service.proto'''
proto_library (
  name = 'kuaishou_ad_rta_rta_service__proto',
  srcs = [
    'kuaishou/ad/rta/rta_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_rta_rta__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/search_info.proto'''
proto_library (
  name = 'kuaishou_ad_search_info__proto',
  srcs = [
    'kuaishou/ad/search_info.proto'
  ],
  deps = [
    '//ks/base/abtest2/BUILD:abtest2_proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_search_ads_query_livestream_tag__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_layouts_style__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_query_ec_result__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:mmu_combo_search_params__proto',
    '//teams/ad/ad_proto/mmu/BUILD:query_analysis__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_query_rewriter__proto',
  ],
)

'''auto generate from kuaishou/ad/session_service.proto'''
proto_library (
  name = 'kuaishou_ad_session_service__proto',
  srcs = [
    'kuaishou/ad/session_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ad/tables/table_extend_fields.proto'''
proto_library (
  name = 'kuaishou_ad_tables_table_extend_fields__proto',
  srcs = [
    'kuaishou/ad/tables/table_extend_fields.proto'
  ],
)

'''auto generate from kuaishou/ad/track/tp_ad_track.proto'''
proto_library (
  name = 'kuaishou_ad_track_tp_ad_track__proto',
  srcs = [
    'kuaishou/ad/track/tp_ad_track.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
  ],
)

'''auto generate from kuaishou/ad/union_server_show.proto'''
proto_library (
  name = 'kuaishou_ad_union_server_show__proto',
  srcs = [
    'kuaishou/ad/union_server_show.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_athena_universe_interface__proto',
  ],
)

'''auto generate from kuaishou/ad/universe_computility_data.proto'''
proto_library (
  name = 'kuaishou_ad_universe_computility_data__proto',
  srcs = [
    'kuaishou/ad/universe_computility_data.proto'
  ],
)

'''auto generate from kuaishou/ad/universe_delivered_qpon_info.proto'''
proto_library (
  name = 'kuaishou_ad_universe_delivered_qpon_info__proto',
  srcs = [
    'kuaishou/ad/universe_delivered_qpon_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
  ],
)

'''auto generate from kuaishou/ad/user_action_statictic.proto'''
proto_library (
  name = 'kuaishou_ad_user_action_statictic__proto',
  srcs = [
    'kuaishou/ad/user_action_statictic.proto'
  ],
)

'''auto generate from kuaishou/ad/user_action_unit_info.proto'''
proto_library (
  name = 'kuaishou_ad_user_action_unit_info__proto',
  srcs = [
    'kuaishou/ad/user_action_unit_info.proto'
  ],
)

'''auto generate from kuaishou/ad/user_model_score.proto'''
proto_library (
  name = 'kuaishou_ad_user_model_score__proto',
  srcs = [
    'kuaishou/ad/user_model_score.proto'
  ],
)

'''auto generate from kuaishou/aggrcard/aggr_card_base.proto'''
proto_library (
  name = 'kuaishou_aggrcard_aggr_card_base__proto',
  srcs = [
    'kuaishou/aggrcard/aggr_card_base.proto'
  ],
)

'''auto generate from kuaishou/antispam/antispam_ad_union_request_service.proto'''
proto_library (
  name = 'kuaishou_antispam_antispam_ad_union_request_service__proto',
  srcs = [
    'kuaishou/antispam/antispam_ad_union_request_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_antispam_service_union_ad_request_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/antispam/captcha.proto'''
proto_library (
  name = 'kuaishou_antispam_captcha__proto',
  srcs = [
    'kuaishou/antispam/captcha.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/antispam/realtime_antispam.proto'''
proto_library (
  name = 'kuaishou_antispam_realtime_antispam__proto',
  srcs = [
    'kuaishou/antispam/realtime_antispam.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:antispam_captcha__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_meta__proto',
    '//teams/ad/ad_proto/mmu/BUILD:hand_writing_recognition__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/cdn/common/storage/unite_store.proto'''
proto_library (
  name = 'kuaishou_cdn_common_storage_unite_store__proto',
  srcs = [
    'kuaishou/cdn/common/storage/unite_store.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/client/client_real_action_logs.proto'''
proto_library (
  name = 'kuaishou_client_client_real_action_logs__proto',
  srcs = [
    'kuaishou/client/client_real_action_logs.proto'
  ],
)

'''auto generate from kuaishou/diagnosis/diagnosis_base.proto'''
proto_library (
  name = 'kuaishou_diagnosis_diagnosis_base__proto',
  srcs = [
    'kuaishou/diagnosis/diagnosis_base.proto'
  ],
)

'''auto generate from kuaishou/diagnosis/diagnosis_log.proto'''
proto_library (
  name = 'kuaishou_diagnosis_diagnosis_log__proto',
  srcs = [
    'kuaishou/diagnosis/diagnosis_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:diagnosis_diagnosis_base__proto',
  ],
)

'''auto generate from kuaishou/ds/index/feature.proto'''
proto_library (
  name = 'kuaishou_ds_index_feature__proto',
  srcs = [
    'kuaishou/ds/index/feature.proto'
  ],
  deps = [
    '//ks/algo-engine-proto/BUILD:algo_engine_proto',
  ],
)

'''auto generate from kuaishou/ds/pic_info.proto'''
proto_library (
  name = 'kuaishou_ds_pic_info__proto',
  srcs = [
    'kuaishou/ds/pic_info.proto'
  ],
)

'''auto generate from kuaishou/ds/search/combo_search_common.proto'''
proto_library (
  name = 'kuaishou_ds_search_combo_search_common__proto',
  srcs = [
    'kuaishou/ds/search/combo_search_common.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_search_tab__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_vertical_source__proto',
  ],
)

'''auto generate from kuaishou/ds/search/combo_search_common_split.proto'''
proto_library (
  name = 'kuaishou_ds_search_combo_search_common_split__proto',
  srcs = [
    'kuaishou/ds/search/combo_search_common_split.proto'
  ],
)

'''auto generate from kuaishou/ds/search/combo_search_common_split_layouts_style.proto'''
proto_library (
  name = 'kuaishou_ds_search_combo_search_common_split_layouts_style__proto',
  srcs = [
    'kuaishou/ds/search/combo_search_common_split_layouts_style.proto'
  ],
)

'''auto generate from kuaishou/ds/search/combo_search_common_split_query_ec_result.proto'''
proto_library (
  name = 'kuaishou_ds_search_combo_search_common_split_query_ec_result__proto',
  srcs = [
    'kuaishou/ds/search/combo_search_common_split_query_ec_result.proto'
  ],
)

'''auto generate from kuaishou/ds/search/combo_search_common_split_search_tab.proto'''
proto_library (
  name = 'kuaishou_ds_search_combo_search_common_split_search_tab__proto',
  srcs = [
    'kuaishou/ds/search/combo_search_common_split_search_tab.proto'
  ],
)

'''auto generate from kuaishou/ds/search/combo_search_common_split_vertical_source.proto'''
proto_library (
  name = 'kuaishou_ds_search_combo_search_common_split_vertical_source__proto',
  srcs = [
    'kuaishou/ds/search/combo_search_common_split_vertical_source.proto'
  ],
)

'''auto generate from kuaishou/ds/search/combo_search_component.proto'''
proto_library (
  name = 'kuaishou_ds_search_combo_search_component__proto',
  srcs = [
    'kuaishou/ds/search/combo_search_component.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:goods_info__proto',
    '//teams/ad/ad_proto/mmu/BUILD:search_aladdin__proto',
  ],
)

'''auto generate from kuaishou/ds/search/combo_search_log.proto'''
proto_library (
  name = 'kuaishou_ds_search_combo_search_log__proto',
  srcs = [
    'kuaishou/ds/search/combo_search_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_suggestion_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:music_music__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_search_client_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_search_source__proto',
  ],
)

'''auto generate from kuaishou/ds/search/combo_search_suggestion_service.proto'''
proto_library (
  name = 'kuaishou_ds_search_combo_search_suggestion_service__proto',
  srcs = [
    'kuaishou/ds/search/combo_search_suggestion_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_debug__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ds/search/common_feature.proto'''
proto_library (
  name = 'kuaishou_ds_search_common_feature__proto',
  srcs = [
    'kuaishou/ds/search/common_feature.proto'
  ],
)

'''auto generate from kuaishou/ds/search/common_vision_search_service.proto'''
proto_library (
  name = 'kuaishou_ds_search_common_vision_search_service__proto',
  srcs = [
    'kuaishou/ds/search/common_vision_search_service.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ds/search/index_search_api.proto'''
proto_library (
  name = 'kuaishou_ds_search_index_search_api__proto',
  srcs = [
    'kuaishou/ds/search/index_search_api.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/ds/search/search_message_clone.proto'''
proto_library (
  name = 'kuaishou_ds_search_search_message_clone__proto',
  srcs = [
    'kuaishou/ds/search/search_message_clone.proto'
  ],
)

'''auto generate from kuaishou/ds/user_reco_base_split_reco_user.proto'''
proto_library (
  name = 'kuaishou_ds_user_reco_base_split_reco_user__proto',
  srcs = [
    'kuaishou/ds/user_reco_base_split_reco_user.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_user_reco_base_split_reco_user_text__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_user_reco_reason__proto',
  ],
)

'''auto generate from kuaishou/ds/user_reco_base_split_reco_user_text.proto'''
proto_library (
  name = 'kuaishou_ds_user_reco_base_split_reco_user_text__proto',
  srcs = [
    'kuaishou/ds/user_reco_base_split_reco_user_text.proto'
  ],
)

'''auto generate from kuaishou/ds/user_reco_reason.proto'''
proto_library (
  name = 'kuaishou_ds_user_reco_reason__proto',
  srcs = [
    'kuaishou/ds/user_reco_reason.proto'
  ],
)

'''auto generate from kuaishou/fanstop/common/fans_top_enums.proto'''
proto_library (
  name = 'kuaishou_fanstop_common_fans_top_enums__proto',
  srcs = [
    'kuaishou/fanstop/common/fans_top_enums.proto'
  ],
)

'''auto generate from kuaishou/fanstop/fans_top_base.proto'''
proto_library (
  name = 'kuaishou_fanstop_fans_top_base__proto',
  srcs = [
    'kuaishou/fanstop/fans_top_base.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:client_log',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_enums__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_full_link_pos_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_user_action_statictic__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:fanstop_common_fans_top_enums__proto',
  ],
)

'''auto generate from kuaishou/fanstop/fans_top_server_show_log.proto'''
proto_library (
  name = 'kuaishou_fanstop_fans_top_server_show_log__proto',
  srcs = [
    'kuaishou/fanstop/fans_top_server_show_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_base__proto',
  ],
)

'''auto generate from kuaishou/fishpond/service.proto'''
proto_library (
  name = 'kuaishou_fishpond_service__proto',
  srcs = [
    'kuaishou/fishpond/service.proto'
  ],
  deps = [
    '//ks/algo-engine-proto/BUILD:algo_engine_proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/log/common_attr.proto'''
proto_library (
  name = 'kuaishou_log_common_attr__proto',
  srcs = [
    'kuaishou/log/common_attr.proto'
  ],
)

'''auto generate from kuaishou/log/ext_attr.proto'''
proto_library (
  name = 'kuaishou_log_ext_attr__proto',
  srcs = [
    'kuaishou/log/ext_attr.proto'
  ],
)

'''auto generate from kuaishou/log/reco_label_set.proto'''
proto_library (
  name = 'kuaishou_log_reco_label_set__proto',
  srcs = [
    'kuaishou/log/reco_label_set.proto'
  ],
)

'''auto generate from kuaishou/log/stid.proto'''
proto_library (
  name = 'kuaishou_log_stid__proto',
  srcs = [
    'kuaishou/log/stid.proto'
  ],
)

'''auto generate from kuaishou/mmu/combo_search_params.proto'''
proto_library (
  name = 'kuaishou_mmu_combo_search_params__proto',
  srcs = [
    'kuaishou/mmu/combo_search_params.proto'
  ],
)

'''auto generate from kuaishou/music/music.proto'''
proto_library (
  name = 'kuaishou_music_music__proto',
  srcs = [
    'kuaishou/music/music.proto'
  ],
)

'''auto generate from kuaishou/music/music_client_log.proto'''
proto_library (
  name = 'kuaishou_music_music_client_log__proto',
  srcs = [
    'kuaishou/music/music_client_log.proto'
  ],
  deps = [
    ':kuaishou_music_music__proto',
  ],
)

'''auto generate from kuaishou/music/music_common.proto'''
proto_library (
  name = 'kuaishou_music_music_common__proto',
  srcs = [
    'kuaishou/music/music_common.proto'
  ],
  deps = [
    ':kuaishou_music_music__proto',
  ],
)

'''auto generate from kuaishou/music/music_count_delta.proto'''
proto_library (
  name = 'kuaishou_music_music_count_delta__proto',
  srcs = [
    'kuaishou/music/music_count_delta.proto'
  ],
  deps = [
    ':kuaishou_music_music__proto',
  ],
)

'''auto generate from kuaishou/music/music_feature_cache.proto'''
proto_library (
  name = 'kuaishou_music_music_feature_cache__proto',
  srcs = [
    'kuaishou/music/music_feature_cache.proto'
  ],
)

'''kuaishou/music/music_log.proto depend blob_store.proto not ok'''

'''kuaishou/music/photo_soundtrack_classification.proto depend blob_store.proto not ok'''

'''auto generate from kuaishou/newsmodel/reco_auto_params.proto'''
proto_library (
  name = 'kuaishou_newsmodel_reco_auto_params__proto',
  srcs = [
    'kuaishou/newsmodel/reco_auto_params.proto'
  ],
)

'''auto generate from kuaishou/newsmodel/reco_base.proto'''
proto_library (
  name = 'kuaishou_newsmodel_reco_base__proto',
  srcs = [
    'kuaishou/newsmodel/reco_base.proto'
  ],
  deps = [
    '//ks/base/abtest2/BUILD:abtest2_proto',
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:log_reco_label_set__proto',
  ],
)

'''auto generate from kuaishou/newsmodel/reco_leaf_info.proto'''
proto_library (
  name = 'kuaishou_newsmodel_reco_leaf_info__proto',
  srcs = [
    'kuaishou/newsmodel/reco_leaf_info.proto'
  ],
  deps = [
    '//ks/algo-engine-proto/BUILD:algo_engine_proto',
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_user_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_user_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_browseset_reco_browse_set__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_reco_common__proto',
  ],
)

'''auto generate from kuaishou/newsmodel/reco_ranking.proto'''
proto_library (
  name = 'kuaishou_newsmodel_reco_ranking__proto',
  srcs = [
    'kuaishou/newsmodel/reco_ranking.proto'
  ],
)

'''auto generate from kuaishou/newsmodel/reco_relation.proto'''
proto_library (
  name = 'kuaishou_newsmodel_reco_relation__proto',
  srcs = [
    'kuaishou/newsmodel/reco_relation.proto'
  ],
)

'''auto generate from kuaishou/newsmodel/reco_user_base.proto'''
proto_library (
  name = 'kuaishou_newsmodel_reco_user_base__proto',
  srcs = [
    'kuaishou/newsmodel/reco_user_base.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_relation__proto',
  ],
)

'''auto generate from kuaishou/newsmodel/reco_user_info.proto'''
proto_library (
  name = 'kuaishou_newsmodel_reco_user_info__proto',
  srcs = [
    'kuaishou/newsmodel/reco_user_info.proto'
  ],
  deps = [
    '//ks/algo-engine-proto/BUILD:algo_engine_proto',
    '//ks/reco_proto/action/BUILD:sample_list_service',
    '//ks/reco_proto/proto/BUILD:client_log',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_auto_params__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_ranking__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_user_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_user_info_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_user_profile__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_browseset_reco_browse_set__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_reco_user_profile_base__proto',
  ],
)

'''auto generate from kuaishou/newsmodel/reco_user_info_base.proto'''
proto_library (
  name = 'kuaishou_newsmodel_reco_user_info_base__proto',
  srcs = [
    'kuaishou/newsmodel/reco_user_info_base.proto'
  ],
)

'''auto generate from kuaishou/newsmodel/user_profile.proto'''
proto_library (
  name = 'kuaishou_newsmodel_user_profile__proto',
  srcs = [
    'kuaishou/newsmodel/user_profile.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_base__proto',
  ],
)

'''auto generate from kuaishou/photo/activity_photo_deleted.proto'''
proto_library (
  name = 'kuaishou_photo_activity_photo_deleted__proto',
  srcs = [
    'kuaishou/photo/activity_photo_deleted.proto'
  ],
)

'''auto generate from kuaishou/photo/disaster_alert_photo.proto'''
proto_library (
  name = 'kuaishou_photo_disaster_alert_photo__proto',
  srcs = [
    'kuaishou/photo/disaster_alert_photo.proto'
  ],
)

'''auto generate from kuaishou/photo/hot_photo_push_dto.proto'''
proto_library (
  name = 'kuaishou_photo_hot_photo_push_dto__proto',
  srcs = [
    'kuaishou/photo/hot_photo_push_dto.proto'
  ],
)

'''auto generate from kuaishou/photo/new_user_photo.proto'''
proto_library (
  name = 'kuaishou_photo_new_user_photo__proto',
  srcs = [
    'kuaishou/photo/new_user_photo.proto'
  ],
)

'''auto generate from kuaishou/photo/photo.proto'''
proto_library (
  name = 'kuaishou_photo_photo__proto',
  srcs = [
    'kuaishou/photo/photo.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_ad_info.proto'''
proto_library (
  name = 'kuaishou_photo_photo_ad_info__proto',
  srcs = [
    'kuaishou/photo/photo_ad_info.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_count_changed.proto'''
proto_library (
  name = 'kuaishou_photo_photo_count_changed__proto',
  srcs = [
    'kuaishou/photo/photo_count_changed.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_count_recorder.proto'''
proto_library (
  name = 'kuaishou_photo_photo_count_recorder__proto',
  srcs = [
    'kuaishou/photo/photo_count_recorder.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_edit.proto'''
proto_library (
  name = 'kuaishou_photo_photo_edit__proto',
  srcs = [
    'kuaishou/photo/photo_edit.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_music__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_record__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_video_info__proto',
  ],
)

'''auto generate from kuaishou/photo/photo_feedback_push.proto'''
proto_library (
  name = 'kuaishou_photo_photo_feedback_push__proto',
  srcs = [
    'kuaishou/photo/photo_feedback_push.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_frame.proto'''
proto_library (
  name = 'kuaishou_photo_photo_frame__proto',
  srcs = [
    'kuaishou/photo/photo_frame.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_liked_dto.proto'''
proto_library (
  name = 'kuaishou_photo_photo_liked_dto__proto',
  srcs = [
    'kuaishou/photo/photo_liked_dto.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_live_clip.proto'''
proto_library (
  name = 'kuaishou_photo_photo_live_clip__proto',
  srcs = [
    'kuaishou/photo/photo_live_clip.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_meta.proto'''
proto_library (
  name = 'kuaishou_photo_photo_meta__proto',
  srcs = [
    'kuaishou/photo/photo_meta.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_ad_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_edit__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_live_clip__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_record__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_share__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_video_info__proto',
  ],
)

'''auto generate from kuaishou/photo/photo_music.proto'''
proto_library (
  name = 'kuaishou_photo_photo_music__proto',
  srcs = [
    'kuaishou/photo/photo_music.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_record.proto'''
proto_library (
  name = 'kuaishou_photo_photo_record__proto',
  srcs = [
    'kuaishou/photo/photo_record.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_music__proto',
  ],
)

'''kuaishou/photo/photo_saw_dto.proto depend client_event.proto not ok'''

'''auto generate from kuaishou/photo/photo_share.proto'''
proto_library (
  name = 'kuaishou_photo_photo_share__proto',
  srcs = [
    'kuaishou/photo/photo_share.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_similar.proto'''
proto_library (
  name = 'kuaishou_photo_photo_similar__proto',
  srcs = [
    'kuaishou/photo/photo_similar.proto'
  ],
)

'''auto generate from kuaishou/photo/photo_video_info.proto'''
proto_library (
  name = 'kuaishou_photo_photo_video_info__proto',
  srcs = [
    'kuaishou/photo/photo_video_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_music__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_photo_record__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:photo_story_common__proto',
  ],
)

'''auto generate from kuaishou/photo/photo_view_discard_log.proto'''
proto_library (
  name = 'kuaishou_photo_photo_view_discard_log__proto',
  srcs = [
    'kuaishou/photo/photo_view_discard_log.proto'
  ],
)

'''auto generate from kuaishou/photo/story_common.proto'''
proto_library (
  name = 'kuaishou_photo_story_common__proto',
  srcs = [
    'kuaishou/photo/story_common.proto'
  ],
)

'''auto generate from kuaishou/photo/story_local.proto'''
proto_library (
  name = 'kuaishou_photo_story_local__proto',
  srcs = [
    'kuaishou/photo/story_local.proto'
  ],
  deps = [
    ':kuaishou_photo_story_common__proto',
  ],
)

'''auto generate from kuaishou/reco/browseset/reco_browse_set.proto'''
proto_library (
  name = 'kuaishou_reco_browseset_reco_browse_set__proto',
  srcs = [
    'kuaishou/reco/browseset/reco_browse_set.proto'
  ],
)

'''auto generate from kuaishou/reco/platform/browseset/common_browse_set.proto'''
proto_library (
  name = 'kuaishou_reco_platform_browseset_common_browse_set__proto',
  srcs = [
    'kuaishou/reco/platform/browseset/common_browse_set.proto'
  ],
)

'''auto generate from kuaishou/reco/platform/id/product_full_id.proto'''
proto_library (
  name = 'kuaishou_reco_platform_id_product_full_id__proto',
  srcs = [
    'kuaishou/reco/platform/id/product_full_id.proto'
  ],
)

'''auto generate from kuaishou/reco/platform/leaf/common_reco.proto'''
proto_library (
  name = 'kuaishou_reco_platform_leaf_common_reco__proto',
  srcs = [
    'kuaishou/reco/platform/leaf/common_reco.proto'
  ],
  deps = [
    '//ks/algo-engine-proto/BUILD:algo_engine_proto',
    '//ks/base/abtest2/BUILD:abtest2_proto',
    '//ks/reco_proto/common_reco/leaf/BUILD:common_reco_proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_browseset_reco_browse_set__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/reco/platform/log/common_sample_log.proto'''
proto_library (
  name = 'kuaishou_reco_platform_log_common_sample_log__proto',
  srcs = [
    'kuaishou/reco/platform/log/common_sample_log.proto'
  ],
)

'''auto generate from kuaishou/reco/platform/userprofile/app_user_info.proto'''
proto_library (
  name = 'kuaishou_reco_platform_userprofile_app_user_info__proto',
  srcs = [
    'kuaishou/reco/platform/userprofile/app_user_info.proto'
  ],
)

'''auto generate from kuaishou/reco/reco.proto'''
proto_library (
  name = 'kuaishou_reco_reco__proto',
  srcs = [
    'kuaishou/reco/reco.proto'
  ],
  deps = [
    '//ks/algo-engine-proto/BUILD:algo_engine_proto',
    '//ks/reco_proto/proto/BUILD:client_log',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/common/BUILD:product_platform__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_service__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:aggrcard_aggr_card_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:client_client_real_action_logs__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_user_reco_base_split_reco_user__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_user_reco_base_split_reco_user_text__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_leaf_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_user_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_browseset_reco_browse_set__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_platform_id_product_full_id__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_platform_userprofile_app_user_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_reco_common__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_reco_leaf__proto',
  ],
)

'''auto generate from kuaishou/reco/reco_common.proto'''
proto_library (
  name = 'kuaishou_reco_reco_common__proto',
  srcs = [
    'kuaishou/reco/reco_common.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:api_feed_type__proto',
  ],
)

'''auto generate from kuaishou/reco/reco_leaf.proto'''
proto_library (
  name = 'kuaishou_reco_reco_leaf__proto',
  srcs = [
    'kuaishou/reco/reco_leaf.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:raw_message__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:diagnosis_diagnosis_log__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_base__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_leaf_info__proto',
  ],
)

'''auto generate from kuaishou/reco/reco_slide_rerank.proto'''
proto_library (
  name = 'kuaishou_reco_reco_slide_rerank__proto',
  srcs = [
    'kuaishou/reco/reco_slide_rerank.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:reco_reco__proto',
  ],
)

'''auto generate from kuaishou/reco/reco_user_profile_base.proto'''
proto_library (
  name = 'kuaishou_reco_reco_user_profile_base__proto',
  srcs = [
    'kuaishou/reco/reco_user_profile_base.proto'
  ],
)

'''auto generate from kuaishou/reco/sample_list.proto'''
proto_library (
  name = 'kuaishou_reco_sample_list__proto',
  srcs = [
    'kuaishou/reco/sample_list.proto'
  ],
  deps = [
    '//ks/algo-engine-proto/BUILD:algo_engine_proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/retrieval/bits_index.proto'''
proto_library (
  name = 'kuaishou_retrieval_bits_index__proto',
  srcs = [
    'kuaishou/retrieval/bits_index.proto'
  ],
)

'''auto generate from kuaishou/retrieval/cluster_type.proto'''
proto_library (
  name = 'kuaishou_retrieval_cluster_type__proto',
  srcs = [
    'kuaishou/retrieval/cluster_type.proto'
  ],
)

'''auto generate from kuaishou/retrieval/retrieval_service.proto'''
proto_library (
  name = 'kuaishou_retrieval_retrieval_service__proto',
  srcs = [
    'kuaishou/retrieval/retrieval_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_tensor__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_tables_table_extend_fields__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:retrieval_bits_index__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/retrieval/sample.proto'''
proto_library (
  name = 'kuaishou_retrieval_sample__proto',
  srcs = [
    'kuaishou/retrieval/sample.proto'
  ],
)

'''auto generate from kuaishou/search/abtest_common_list.proto'''
proto_library (
  name = 'kuaishou_search_abtest_common_list__proto',
  srcs = [
    'kuaishou/search/abtest_common_list.proto'
  ],
  deps = [
    '//ks/base/abtest2/BUILD:abtest2_proto',
  ],
)

'''auto generate from kuaishou/search/common_feature.proto'''
proto_library (
  name = 'kuaishou_search_common_feature__proto',
  srcs = [
    'kuaishou/search/common_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_common_feature__proto',
  ],
)

'''auto generate from kuaishou/search/doc_info_server.proto'''
proto_library (
  name = 'kuaishou_search_doc_info_server__proto',
  srcs = [
    'kuaishou/search/doc_info_server.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/search/extractor_service.proto'''
proto_library (
  name = 'kuaishou_search_extractor_service__proto',
  srcs = [
    'kuaishou/search/extractor_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/search/goods_intent.proto'''
proto_library (
  name = 'kuaishou_search_goods_intent__proto',
  srcs = [
    'kuaishou/search/goods_intent.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/search/goods_parent_log.proto'''
proto_library (
  name = 'kuaishou_search_goods_parent_log__proto',
  srcs = [
    'kuaishou/search/goods_parent_log.proto'
  ],
)

'''auto generate from kuaishou/search/group_chat_info.proto'''
proto_library (
  name = 'kuaishou_search_group_chat_info__proto',
  srcs = [
    'kuaishou/search/group_chat_info.proto'
  ],
)

'''auto generate from kuaishou/search/music_parent_log.proto'''
proto_library (
  name = 'kuaishou_search_music_parent_log__proto',
  srcs = [
    'kuaishou/search/music_parent_log.proto'
  ],
)

'''auto generate from kuaishou/search/photo_counter.proto'''
proto_library (
  name = 'kuaishou_search_photo_counter__proto',
  srcs = [
    'kuaishou/search/photo_counter.proto'
  ],
)

'''auto generate from kuaishou/search/se_common.proto'''
proto_library (
  name = 'kuaishou_search_se_common__proto',
  srcs = [
    'kuaishou/search/se_common.proto'
  ],
)

'''auto generate from kuaishou/search/search_client_log.proto'''
proto_library (
  name = 'kuaishou_search_search_client_log__proto',
  srcs = [
    'kuaishou/search/search_client_log.proto'
  ],
)

'''auto generate from kuaishou/search/search_collection_query_log.proto'''
proto_library (
  name = 'kuaishou_search_search_collection_query_log__proto',
  srcs = [
    'kuaishou/search/search_collection_query_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_log__proto',
  ],
)

'''auto generate from kuaishou/search/search_common_msg.proto'''
proto_library (
  name = 'kuaishou_search_search_common_msg__proto',
  srcs = [
    'kuaishou/search/search_common_msg.proto'
  ],
)

'''auto generate from kuaishou/search/search_custom_sink.proto'''
proto_library (
  name = 'kuaishou_search_search_custom_sink__proto',
  srcs = [
    'kuaishou/search/search_custom_sink.proto'
  ],
)

'''kuaishou/search/search_degrade.proto depend combo_search_service.proto not ok'''

'''auto generate from kuaishou/search/search_inner_signal.proto'''
proto_library (
  name = 'kuaishou_search_search_inner_signal__proto',
  srcs = [
    'kuaishou/search/search_inner_signal.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:search_search_client_log__proto',
  ],
)

'''auto generate from kuaishou/search/search_layout_style.proto'''
proto_library (
  name = 'kuaishou_search_search_layout_style__proto',
  srcs = [
    'kuaishou/search/search_layout_style.proto'
  ],
)

'''auto generate from kuaishou/search/search_realtime_action.proto'''
proto_library (
  name = 'kuaishou_search_search_realtime_action__proto',
  srcs = [
    'kuaishou/search/search_realtime_action.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:search_search_inner_signal__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_search_source_traces__proto',
  ],
)

'''kuaishou/search/search_recommend.proto depend plain_tag_type.proto not ok'''

'''auto generate from kuaishou/search/search_recommend_log.proto'''
proto_library (
  name = 'kuaishou_search_search_recommend_log__proto',
  srcs = [
    'kuaishou/search/search_recommend_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kuaishou_search_search_client_log__proto',
  ],
)

'''auto generate from kuaishou/search/search_source.proto'''
proto_library (
  name = 'kuaishou_search_search_source__proto',
  srcs = [
    'kuaishou/search/search_source.proto'
  ],
)

'''auto generate from kuaishou/search/search_source_traces.proto'''
proto_library (
  name = 'kuaishou_search_search_source_traces__proto',
  srcs = [
    'kuaishou/search/search_source_traces.proto'
  ],
)

'''auto generate from kuaishou/search/semantic_retrieval.proto'''
proto_library (
  name = 'kuaishou_search_semantic_retrieval__proto',
  srcs = [
    'kuaishou/search/semantic_retrieval.proto'
  ],
  deps = [
    ':kuaishou_newsmodel_reco_user_info__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/search/sensitive.proto'''
proto_library (
  name = 'kuaishou_search_sensitive__proto',
  srcs = [
    'kuaishou/search/sensitive.proto'
  ],
)

'''auto generate from kuaishou/search/smart_title.proto'''
proto_library (
  name = 'kuaishou_search_smart_title__proto',
  srcs = [
    'kuaishou/search/smart_title.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_layouts_style__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_debug__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/search/user_search_info.proto'''
proto_library (
  name = 'kuaishou_search_user_search_info__proto',
  srcs = [
    'kuaishou/search/user_search_info.proto'
  ],
)

'''auto generate from kuaishou/search/user_search_punish_service.proto'''
proto_library (
  name = 'kuaishou_search_user_search_punish_service__proto',
  srcs = [
    'kuaishou/search/user_search_punish_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/user/user_property.proto'''
proto_library (
  name = 'kuaishou_user_user_property__proto',
  srcs = [
    'kuaishou/user/user_property.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/video/feature/spherical_video_feature.proto'''
proto_library (
  name = 'kuaishou_video_feature_spherical_video_feature__proto',
  srcs = [
    'kuaishou/video/feature/spherical_video_feature.proto'
  ],
)

'''auto generate from kuaishou/video/feature/video_feature.proto'''
proto_library (
  name = 'kuaishou_video_feature_video_feature__proto',
  srcs = [
    'kuaishou/video/feature/video_feature.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/video/live_record.proto'''
proto_library (
  name = 'kuaishou_video_live_record__proto',
  srcs = [
    'kuaishou/video/live_record.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/kuaishou/BUILD:cdn_common_storage_unite_store__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:video_live_record_common__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:video_video_live_common_model__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kuaishou/video/live_record_common.proto'''
proto_library (
  name = 'kuaishou_video_live_record_common__proto',
  srcs = [
    'kuaishou/video/live_record_common.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:video_video_live_common_model__proto',
  ],
)

'''auto generate from kuaishou/video/video_live_common_model.proto'''
proto_library (
  name = 'kuaishou_video_video_live_common_model__proto',
  srcs = [
    'kuaishou/video/video_live_common_model.proto'
  ],
)

'''auto generate from kuaishou/videostore/assets.proto'''
proto_library (
  name = 'kuaishou_videostore_assets__proto',
  srcs = [
    'kuaishou/videostore/assets.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/kuaishou/BUILD:video_feature_spherical_video_feature__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:video_feature_video_feature__proto',
  ],
)

'''auto generate from kwaishop_address_common.proto'''
proto_library (
  name = 'kwaishop_address_common__proto',
  srcs = [
    'kwaishop_address_common.proto'
  ],
)

'''auto generate from kwaishop_address_dto.proto'''
proto_library (
  name = 'kwaishop_address_dto__proto',
  srcs = [
    'kwaishop_address_dto.proto'
  ],
  deps = [
    ':kwaishop_address_common__proto',
  ],
)

'''auto generate from kwaishop_address_open_service.proto'''
proto_library (
  name = 'kwaishop_address_open_service__proto',
  srcs = [
    'kwaishop_address_open_service.proto'
  ],
  deps = [
    ':kwaishop_address_common__proto',
    ':kwaishop_address_dto__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_address_service.proto'''
proto_library (
  name = 'kwaishop_address_service__proto',
  srcs = [
    'kwaishop_address_service.proto'
  ],
  deps = [
    ':kwaishop_address_dto__proto',
    ':kwaishop_address_switch_dto__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_address_switch_dto.proto'''
proto_library (
  name = 'kwaishop_address_switch_dto__proto',
  srcs = [
    'kwaishop_address_switch_dto.proto'
  ],
  deps = [
    ':kwaishop_address_dto__proto',
  ],
)

'''auto generate from kwaishop_browsing_history_common.proto'''
proto_library (
  name = 'kwaishop_browsing_history_common__proto',
  srcs = [
    'kwaishop_browsing_history_common.proto'
  ],
)

'''auto generate from kwaishop_browsing_history_dto.proto'''
proto_library (
  name = 'kwaishop_browsing_history_dto__proto',
  srcs = [
    'kwaishop_browsing_history_dto.proto'
  ],
  deps = [
    ':kwaishop_browsing_history_common__proto',
  ],
)

'''auto generate from kwaishop_browsing_history_service.proto'''
proto_library (
  name = 'kwaishop_browsing_history_service__proto',
  srcs = [
    'kwaishop_browsing_history_service.proto'
  ],
  deps = [
    ':kwaishop_browsing_history_common__proto',
    ':kwaishop_browsing_history_dto__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop/c/aggregation/common/kwaishop_convergence_common_message.proto'''
proto_library (
  name = 'kwaishop_c_aggregation_common_kwaishop_convergence_common_message__proto',
  srcs = [
    'kwaishop/c/aggregation/common/kwaishop_convergence_common_message.proto'
  ],
  deps = [
    ':merchant_base__proto',
  ],
)

'''auto generate from kwaishop/c/aggregation/item/kwaishop_product_list_convergence_message.proto'''
proto_library (
  name = 'kwaishop_c_aggregation_item_kwaishop_product_list_convergence_message__proto',
  srcs = [
    'kwaishop/c/aggregation/item/kwaishop_product_list_convergence_message.proto'
  ],
)

'''auto generate from kwaishop/c/aggregation/item/kwaishop_product_list_convergence_service.proto'''
proto_library (
  name = 'kwaishop_c_aggregation_item_kwaishop_product_list_convergence_service__proto',
  srcs = [
    'kwaishop/c/aggregation/item/kwaishop_product_list_convergence_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kwaishop_c_aggregation_common_kwaishop_convergence_common_message__proto',
    ':kwaishop_c_aggregation_item_kwaishop_product_list_convergence_message__proto',
    ':merchant_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop/flow/common/kwaishop_flow_live_common_message.proto'''
proto_library (
  name = 'kwaishop_flow_common_kwaishop_flow_live_common_message__proto',
  srcs = [
    'kwaishop/flow/common/kwaishop_flow_live_common_message.proto'
  ],
)

'''auto generate from kwaishop/flow/live/kwaishop_flow_live_car_item_message.proto'''
proto_library (
  name = 'kwaishop_flow_live_kwaishop_flow_live_car_item_message__proto',
  srcs = [
    'kwaishop/flow/live/kwaishop_flow_live_car_item_message.proto'
  ],
  deps = [
    ':kwaishop_flow_common_kwaishop_flow_live_common_message__proto',
  ],
)

'''auto generate from kwaishop/flow/live/kwaishop_flow_live_car_item_service.proto'''
proto_library (
  name = 'kwaishop_flow_live_kwaishop_flow_live_car_item_service__proto',
  srcs = [
    'kwaishop/flow/live/kwaishop_flow_live_car_item_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kwaishop_flow_common_kwaishop_flow_live_common_message__proto',
    ':kwaishop_flow_live_kwaishop_flow_live_car_item_message__proto',
    ':kwaishop_flow_live_kwaishop_flow_live_shelf_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop/flow/live/kwaishop_flow_live_shelf_message.proto'''
proto_library (
  name = 'kwaishop_flow_live_kwaishop_flow_live_shelf_message__proto',
  srcs = [
    'kwaishop/flow/live/kwaishop_flow_live_shelf_message.proto'
  ],
  deps = [
    ':kwaishop_flow_common_kwaishop_flow_live_common_message__proto',
  ],
)

'''auto generate from kwaishop_marketing_prem_commercial_service.proto'''
proto_library (
  name = 'kwaishop_marketing_prem_commercial_service__proto',
  srcs = [
    'kwaishop_marketing_prem_commercial_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':merchant_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_marketing_tools_welfare_common.proto'''
proto_library (
  name = 'kwaishop_marketing_tools_welfare_common__proto',
  srcs = [
    'kwaishop_marketing_tools_welfare_common.proto'
  ],
  deps = [
    ':merchant_base__proto',
  ],
)

'''auto generate from kwaishop_marketing_tools_welfare_ref.proto'''
proto_library (
  name = 'kwaishop_marketing_tools_welfare_ref__proto',
  srcs = [
    'kwaishop_marketing_tools_welfare_ref.proto'
  ],
)

'''auto generate from kwaishop_marketing_tools_welfare_service.proto'''
proto_library (
  name = 'kwaishop_marketing_tools_welfare_service__proto',
  srcs = [
    'kwaishop_marketing_tools_welfare_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':merchant_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_mpa_rebate_common.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_common__proto',
  srcs = [
    'kwaishop_mpa_rebate_common.proto'
  ],
)

'''auto generate from kwaishop_mpa_rebate_dto.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_dto__proto',
  srcs = [
    'kwaishop_mpa_rebate_dto.proto'
  ],
  deps = [
    ':kwaishop_mpa_rebate_common__proto',
  ],
)

'''auto generate from kwaishop_mpa_rebate_fulfil_common.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_fulfil_common__proto',
  srcs = [
    'kwaishop_mpa_rebate_fulfil_common.proto'
  ],
)

'''auto generate from kwaishop_mpa_rebate_fulfil_inner_service.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_fulfil_inner_service__proto',
  srcs = [
    'kwaishop_mpa_rebate_fulfil_inner_service.proto'
  ],
  deps = [
    ':kwaishop_mpa_rebate_common__proto',
    ':kwaishop_mpa_rebate_inner_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_mpa_rebate_fulfil_mq_common.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_fulfil_mq_common__proto',
  srcs = [
    'kwaishop_mpa_rebate_fulfil_mq_common.proto'
  ],
)

'''auto generate from kwaishop_mpa_rebate_fulfil_reward_service.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_fulfil_reward_service__proto',
  srcs = [
    'kwaishop_mpa_rebate_fulfil_reward_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kwaishop_mpa_rebate_common__proto',
    ':merchant_base__proto',
  ],
)

'''auto generate from kwaishop_mpa_rebate_fulfil_service.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_fulfil_service__proto',
  srcs = [
    'kwaishop_mpa_rebate_fulfil_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kwaishop_mpa_rebate_common__proto',
    ':kwaishop_mpa_rebate_fulfil_common__proto',
    ':kwaishop_mpa_rebate_fulfil_reward_service__proto',
    ':kwaishop_mpa_rebate_service__proto',
    ':merchant_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_mpa_rebate_fulfil_support_service.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_fulfil_support_service__proto',
  srcs = [
    'kwaishop_mpa_rebate_fulfil_support_service.proto'
  ],
  deps = [
    ':kwaishop_mpa_rebate_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_mpa_rebate_fulfil_task_service.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_fulfil_task_service__proto',
  srcs = [
    'kwaishop_mpa_rebate_fulfil_task_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kwaishop_mpa_rebate_common__proto',
    ':kwaishop_mpa_rebate_fulfil_common__proto',
    ':kwaishop_mpa_rebate_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_mpa_rebate_inner_service.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_inner_service__proto',
  srcs = [
    'kwaishop_mpa_rebate_inner_service.proto'
  ],
  deps = [
    ':kwaishop_mpa_rebate_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_mpa_rebate_service.proto'''
proto_library (
  name = 'kwaishop_mpa_rebate_service__proto',
  srcs = [
    'kwaishop_mpa_rebate_service.proto'
  ],
  deps = [
    ':kwaishop_mpa_rebate_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_product_baseinfo_common.proto'''
proto_library (
  name = 'kwaishop_product_baseinfo_common__proto',
  srcs = [
    'kwaishop_product_baseinfo_common.proto'
  ],
)

'''auto generate from kwaishop_product_c_baseinfo_response_object.proto'''
proto_library (
  name = 'kwaishop_product_c_baseinfo_response_object__proto',
  srcs = [
    'kwaishop_product_c_baseinfo_response_object.proto'
  ],
  deps = [
    ':c_kwaishop_product_c_common_info__proto',
  ],
)

'''auto generate from kwaishop_product_c_baseinfo_service.proto'''
proto_library (
  name = 'kwaishop_product_c_baseinfo_service__proto',
  srcs = [
    'kwaishop_product_c_baseinfo_service.proto'
  ],
  deps = [
    ':c_kwaishop_product_jinniu_response_object__proto',
    ':kwaishop_product_c_baseinfo_response_object__proto',
    ':merchant_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_product_shelf_item_query_service.proto'''
proto_library (
  name = 'kwaishop_product_shelf_item_query_service__proto',
  srcs = [
    'kwaishop_product_shelf_item_query_service.proto'
  ],
  deps = [
    ':kwaishop_product_baseinfo_common__proto',
    ':merchant_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_selection_user_common.proto'''
proto_library (
  name = 'kwaishop_selection_user_common__proto',
  srcs = [
    'kwaishop_selection_user_common.proto'
  ],
)

'''auto generate from kwaishop_selection_user_dto.proto'''
proto_library (
  name = 'kwaishop_selection_user_dto__proto',
  srcs = [
    'kwaishop_selection_user_dto.proto'
  ],
)

'''auto generate from kwaishop_selection_user_service.proto'''
proto_library (
  name = 'kwaishop_selection_user_service__proto',
  srcs = [
    'kwaishop_selection_user_service.proto'
  ],
  deps = [
    ':kwaishop_selection_user_common__proto',
    ':kwaishop_selection_user_dto__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from kwaishop_trade_cart_message.proto'''
proto_library (
  name = 'kwaishop_trade_cart_message__proto',
  srcs = [
    'kwaishop_trade_cart_message.proto'
  ],
)

'''auto generate from live_material_info.proto'''
proto_library (
  name = 'live_material_info__proto',
  srcs = [
    'live_material_info.proto'
  ],
)

'''auto generate from locallife/poi_simple_info_service.proto'''
proto_library (
  name = 'locallife_poi_simple_info_service__proto',
  srcs = [
    'locallife/poi_simple_info_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from merchant/base.proto'''
proto_library (
  name = 'merchant_base__proto',
  srcs = [
    'merchant/base.proto'
  ],
)

'''auto generate from merchant/commodity/item/apply/merchant_commodity_apply_service.proto'''
proto_library (
  name = 'merchant_commodity_item_apply_merchant_commodity_apply_service__proto',
  srcs = [
    'merchant/commodity/item/apply/merchant_commodity_apply_service.proto'
  ],
  deps = [
    ':merchant_base__proto',
    ':merchant_commodity_item_apply_merchant_product_regulation__proto',
    ':merchant_commodity_item_merchant_commodity_item_model__proto',
    ':merchant_commodity_item_merchant_commodity_type__proto',
    ':merchant_commodity_prop_merchant_commodity_prop_model__proto',
    ':merchant_tag_merchant_tag_produce_service__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from merchant/commodity/item/apply/merchant_product_regulation.proto'''
proto_library (
  name = 'merchant_commodity_item_apply_merchant_product_regulation__proto',
  srcs = [
    'merchant/commodity/item/apply/merchant_product_regulation.proto'
  ],
)

'''auto generate from merchant/commodity/item/merchant_commodity_item_model.proto'''
proto_library (
  name = 'merchant_commodity_item_merchant_commodity_item_model__proto',
  srcs = [
    'merchant/commodity/item/merchant_commodity_item_model.proto'
  ],
  deps = [
    ':merchant_commodity_item_merchant_commodity_status__proto',
    ':merchant_commodity_item_merchant_commodity_type__proto',
  ],
)

'''auto generate from merchant/commodity/item/merchant_commodity_status.proto'''
proto_library (
  name = 'merchant_commodity_item_merchant_commodity_status__proto',
  srcs = [
    'merchant/commodity/item/merchant_commodity_status.proto'
  ],
)

'''auto generate from merchant/commodity/item/merchant_commodity_type.proto'''
proto_library (
  name = 'merchant_commodity_item_merchant_commodity_type__proto',
  srcs = [
    'merchant/commodity/item/merchant_commodity_type.proto'
  ],
)

'''auto generate from merchant/commodity/price/merchant_commodity_item_price_apply_service.proto'''
proto_library (
  name = 'merchant_commodity_price_merchant_commodity_item_price_apply_service__proto',
  srcs = [
    'merchant/commodity/price/merchant_commodity_item_price_apply_service.proto'
  ],
  deps = [
    ':merchant_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from merchant/commodity/prop/merchant_commodity_prop_model.proto'''
proto_library (
  name = 'merchant_commodity_prop_merchant_commodity_prop_model__proto',
  srcs = [
    'merchant/commodity/prop/merchant_commodity_prop_model.proto'
  ],
)

'''auto generate from merchant/commodity/sales/merchant_commodity_sales_service.proto'''
proto_library (
  name = 'merchant_commodity_sales_merchant_commodity_sales_service__proto',
  srcs = [
    'merchant/commodity/sales/merchant_commodity_sales_service.proto'
  ],
  deps = [
    ':merchant_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from merchant/common/base.proto'''
proto_library (
  name = 'merchant_common_base__proto',
  srcs = [
    'merchant/common/base.proto'
  ],
)

'''auto generate from merchant/common/order_common.proto'''
proto_library (
  name = 'merchant_common_order_common__proto',
  srcs = [
    'merchant/common/order_common.proto'
  ],
)

'''auto generate from merchant/external.platform/basic.proto'''
proto_library (
  name = 'merchant_external__platform_basic__proto',
  srcs = [
    'merchant/external.platform/basic.proto'
  ],
)

'''auto generate from merchant/external.platform/item/merchant_third_coupon_message.proto'''
proto_library (
  name = 'merchant_external__platform_item_merchant_third_coupon_message__proto',
  srcs = [
    'merchant/external.platform/item/merchant_third_coupon_message.proto'
  ],
  deps = [
    ':merchant_external__platform_basic__proto',
  ],
)

'''auto generate from merchant/external.platform/service.proto'''
proto_library (
  name = 'merchant_external__platform_service__proto',
  srcs = [
    'merchant/external.platform/service.proto'
  ],
)

'''auto generate from merchant/marketing/merchant_marketing_base.proto'''
proto_library (
  name = 'merchant_marketing_merchant_marketing_base__proto',
  srcs = [
    'merchant/marketing/merchant_marketing_base.proto'
  ],
)

'''auto generate from merchant/marketing/resource/merchant_marketing_resource_base.proto'''
proto_library (
  name = 'merchant_marketing_resource_merchant_marketing_resource_base__proto',
  srcs = [
    'merchant/marketing/resource/merchant_marketing_resource_base.proto'
  ],
  deps = [
    ':merchant_marketing_merchant_marketing_base__proto',
    ':merchant_quoted_price_merchant_quoted_price_base__proto',
  ],
)

'''auto generate from merchant/marketing/resource/merchant_marketing_resource_query_service.proto'''
proto_library (
  name = 'merchant_marketing_resource_merchant_marketing_resource_query_service__proto',
  srcs = [
    'merchant/marketing/resource/merchant_marketing_resource_query_service.proto'
  ],
  deps = [
    ':merchant_common_base__proto',
    ':merchant_marketing_merchant_marketing_base__proto',
    ':merchant_marketing_resource_merchant_marketing_resource_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from merchant/order/out/order_out_event_base.proto'''
proto_library (
  name = 'merchant_order_out_order_out_event_base__proto',
  srcs = [
    'merchant/order/out/order_out_event_base.proto'
  ],
  deps = [
    ':merchant_common_order_common__proto',
  ],
)

'''auto generate from merchant_order_place_base.proto'''
proto_library (
  name = 'merchant_order_place_base__proto',
  srcs = [
    'merchant_order_place_base.proto'
  ],
)

'''auto generate from merchant_order_place_info_base.proto'''
proto_library (
  name = 'merchant_order_place_info_base__proto',
  srcs = [
    'merchant_order_place_info_base.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':authentication_user_authentication__proto',
    ':kwaishop_address_common__proto',
    ':merchant_common_base__proto',
    ':merchant_order_place_base__proto',
    ':tax_cross_border_tax__proto',
  ],
)

'''merchant_order_place_page_service.proto depend merchant_cart_order_place_info_base.proto not ok'''

'''auto generate from merchant_order_place_purchase_simplify.proto'''
proto_library (
  name = 'merchant_order_place_purchase_simplify__proto',
  srcs = [
    'merchant_order_place_purchase_simplify.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kwaishop_address_common__proto',
    ':merchant_common_base__proto',
    ':merchant_order_place_base__proto',
    ':merchant_order_place_info_base__proto',
  ],
)

'''merchant_order_place_url_base.proto depend base.proto not ok'''

'''auto generate from merchant/photo/merchant_photo_item_info_service.proto'''
proto_library (
  name = 'merchant_photo_merchant_photo_item_info_service__proto',
  srcs = [
    'merchant/photo/merchant_photo_item_info_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':merchant_common_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from merchant/quoted/price/merchant_quoted_price_base.proto'''
proto_library (
  name = 'merchant_quoted_price_merchant_quoted_price_base__proto',
  srcs = [
    'merchant/quoted/price/merchant_quoted_price_base.proto'
  ],
)

'''auto generate from merchant/tag/merchant_tag_produce_service.proto'''
proto_library (
  name = 'merchant_tag_merchant_tag_produce_service__proto',
  srcs = [
    'merchant/tag/merchant_tag_produce_service.proto'
  ],
  deps = [
    ':merchant_common_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from merchant/volume/merchant_volume_message.proto'''
proto_library (
  name = 'merchant_volume_merchant_volume_message__proto',
  srcs = [
    'merchant/volume/merchant_volume_message.proto'
  ],
  deps = [
    ':merchant_common_order_common__proto',
  ],
)

'''auto generate from mmu/ad_photo_dup.proto'''
proto_library (
  name = 'mmu_ad_photo_dup__proto',
  srcs = [
    'mmu/ad_photo_dup.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/ads_validator.proto'''
proto_library (
  name = 'mmu_ads_validator__proto',
  srcs = [
    'mmu/ads_validator.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/ann_engine.proto'''
proto_library (
  name = 'mmu_ann_engine__proto',
  srcs = [
    'mmu/ann_engine.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:common_type__proto',
  ],
)

'''auto generate from mmu/ann_index.proto'''
proto_library (
  name = 'mmu_ann_index__proto',
  srcs = [
    'mmu/ann_index.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:common_type__proto',
  ],
)

'''auto generate from mmu/audio_classification.proto'''
proto_library (
  name = 'mmu_audio_classification__proto',
  srcs = [
    'mmu/audio_classification.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_conversion.proto'''
proto_library (
  name = 'mmu_audio_conversion__proto',
  srcs = [
    'mmu/audio_conversion.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_dedup.proto'''
proto_library (
  name = 'mmu_audio_dedup__proto',
  srcs = [
    'mmu/audio_dedup.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_denoise.proto'''
proto_library (
  name = 'mmu_audio_denoise__proto',
  srcs = [
    'mmu/audio_denoise.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_dup_query.proto'''
proto_library (
  name = 'mmu_audio_dup_query__proto',
  srcs = [
    'mmu/audio_dup_query.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:audio_feature__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_emotion.proto'''
proto_library (
  name = 'mmu_audio_emotion__proto',
  srcs = [
    'mmu/audio_emotion.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_feature.proto'''
proto_library (
  name = 'mmu_audio_feature__proto',
  srcs = [
    'mmu/audio_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_finger_print.proto'''
proto_library (
  name = 'mmu_audio_finger_print__proto',
  srcs = [
    'mmu/audio_finger_print.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_fragment.proto'''
proto_library (
  name = 'mmu_audio_fragment__proto',
  srcs = [
    'mmu/audio_fragment.proto'
  ],
)

'''auto generate from mmu/audio_openl3_feature.proto'''
proto_library (
  name = 'mmu_audio_openl3_feature__proto',
  srcs = [
    'mmu/audio_openl3_feature.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_porn.proto'''
proto_library (
  name = 'mmu_audio_porn__proto',
  srcs = [
    'mmu/audio_porn.proto'
  ],
)

'''auto generate from mmu/audio_recognization.proto'''
proto_library (
  name = 'mmu_audio_recognization__proto',
  srcs = [
    'mmu/audio_recognization.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_seperation.proto'''
proto_library (
  name = 'mmu_audio_seperation__proto',
  srcs = [
    'mmu/audio_seperation.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_stft_feature.proto'''
proto_library (
  name = 'mmu_audio_stft_feature__proto',
  srcs = [
    'mmu/audio_stft_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/audio_vgg_feature.proto'''
proto_library (
  name = 'mmu_audio_vgg_feature__proto',
  srcs = [
    'mmu/audio_vgg_feature.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/avatar_dedup.proto'''
proto_library (
  name = 'mmu_avatar_dedup__proto',
  srcs = [
    'mmu/avatar_dedup.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/border.proto'''
proto_library (
  name = 'mmu_border__proto',
  srcs = [
    'mmu/border.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:bow_index__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/bow_index.proto'''
proto_library (
  name = 'mmu_bow_index__proto',
  srcs = [
    'mmu/bow_index.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:compute_video_clusterid__proto',
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
    ':mmu_obj_detection__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/brand_result.proto'''
proto_library (
  name = 'mmu_brand_result__proto',
  srcs = [
    'mmu/brand_result.proto'
  ],
)

'''auto generate from mmu/cct_inferit.proto'''
proto_library (
  name = 'mmu_cct_inferit__proto',
  srcs = [
    'mmu/cct_inferit.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/chat_search.proto'''
proto_library (
  name = 'mmu_chat_search__proto',
  srcs = [
    'mmu/chat_search.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/city_hotspot_geo_result.proto'''
proto_library (
  name = 'mmu_city_hotspot_geo_result__proto',
  srcs = [
    'mmu/city_hotspot_geo_result.proto'
  ],
)

'''auto generate from mmu/collection_service.proto'''
proto_library (
  name = 'mmu_collection_service__proto',
  srcs = [
    'mmu/collection_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/combo_search_params.proto'''
proto_library (
  name = 'mmu_combo_search_params__proto',
  srcs = [
    'mmu/combo_search_params.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:search_award_center__proto',
  ],
)

'''auto generate from mmu/commodity_retrieval_service.proto'''
proto_library (
  name = 'mmu_commodity_retrieval_service__proto',
  srcs = [
    'mmu/commodity_retrieval_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/common_embedding_service.proto'''
proto_library (
  name = 'mmu_common_embedding_service__proto',
  srcs = [
    'mmu/common_embedding_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/common_message.proto'''
proto_library (
  name = 'mmu_common_message__proto',
  srcs = [
    'mmu/common_message.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization_base__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
  ],
)

'''auto generate from mmu/common_nlp.proto'''
proto_library (
  name = 'mmu_common_nlp__proto',
  srcs = [
    'mmu/common_nlp.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/common_service.proto'''
proto_library (
  name = 'mmu_common_service__proto',
  srcs = [
    'mmu/common_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/common_tiny_service.proto'''
proto_library (
  name = 'mmu_common_tiny_service__proto',
  srcs = [
    'mmu/common_tiny_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/common_type.proto'''
proto_library (
  name = 'mmu_common_type__proto',
  srcs = [
    'mmu/common_type.proto'
  ],
)

'''auto generate from mmu/competitor_detection.proto'''
proto_library (
  name = 'mmu_competitor_detection__proto',
  srcs = [
    'mmu/competitor_detection.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/compute_video_clusterid.proto'''
proto_library (
  name = 'mmu_compute_video_clusterid__proto',
  srcs = [
    'mmu/compute_video_clusterid.proto'
  ],
  deps = [
    ':kuaishou_newsmodel_reco_base__proto',
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
    ':mmu_obj_detection__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/consumer_delay_mark.proto'''
proto_library (
  name = 'mmu_consumer_delay_mark__proto',
  srcs = [
    'mmu/consumer_delay_mark.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/copyright_face_monitor.proto'''
proto_library (
  name = 'mmu_copyright_face_monitor__proto',
  srcs = [
    'mmu/copyright_face_monitor.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
)

'''auto generate from mmu/dj_audiowave_detection.proto'''
proto_library (
  name = 'mmu_dj_audiowave_detection__proto',
  srcs = [
    'mmu/dj_audiowave_detection.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/dj_audiowave_img_detection.proto'''
proto_library (
  name = 'mmu_dj_audiowave_img_detection__proto',
  srcs = [
    'mmu/dj_audiowave_img_detection.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/dl_dct_hash_cal.proto'''
proto_library (
  name = 'mmu_dl_dct_hash_cal__proto',
  srcs = [
    'mmu/dl_dct_hash_cal.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/dl_dct_img_dedup.proto'''
proto_library (
  name = 'mmu_dl_dct_img_dedup__proto',
  srcs = [
    'mmu/dl_dct_img_dedup.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/dl_feature.proto'''
proto_library (
  name = 'mmu_dl_feature__proto',
  srcs = [
    'mmu/dl_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/dl_random_sample.proto'''
proto_library (
  name = 'mmu_dl_random_sample__proto',
  srcs = [
    'mmu/dl_random_sample.proto'
  ],
)

'''auto generate from mmu/dl_user_review_info.proto'''
proto_library (
  name = 'mmu_dl_user_review_info__proto',
  srcs = [
    'mmu/dl_user_review_info.proto'
  ],
)

'''auto generate from mmu/dup_validator.proto'''
proto_library (
  name = 'mmu_dup_validator__proto',
  srcs = [
    'mmu/dup_validator.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/ecmc_intention.proto'''
proto_library (
  name = 'mmu_ecmc_intention__proto',
  srcs = [
    'mmu/ecmc_intention.proto'
  ],
)

'''auto generate from mmu/edu_params.proto'''
proto_library (
  name = 'mmu_edu_params__proto',
  srcs = [
    'mmu/edu_params.proto'
  ],
)

'''auto generate from mmu/embedding.proto'''
proto_library (
  name = 'mmu_embedding__proto',
  srcs = [
    'mmu/embedding.proto'
  ],
)

'''auto generate from mmu/event_cluster.proto'''
proto_library (
  name = 'mmu_event_cluster__proto',
  srcs = [
    'mmu/event_cluster.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/face_cluster.proto'''
proto_library (
  name = 'mmu_face_cluster__proto',
  srcs = [
    'mmu/face_cluster.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:original_photo__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/face_crop.proto'''
proto_library (
  name = 'mmu_face_crop__proto',
  srcs = [
    'mmu/face_crop.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/face_property_for_reco.proto'''
proto_library (
  name = 'mmu_face_property_for_reco__proto',
  srcs = [
    'mmu/face_property_for_reco.proto'
  ],
)

'''auto generate from mmu/face_recognization.proto'''
proto_library (
  name = 'mmu_face_recognization__proto',
  srcs = [
    'mmu/face_recognization.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization_base__proto',
    '//teams/ad/ad_proto/mmu/BUILD:img_dedup_validator__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/face_recognization_base.proto'''
proto_library (
  name = 'mmu_face_recognization_base__proto',
  srcs = [
    'mmu/face_recognization_base.proto'
  ],
)

'''auto generate from mmu/face_retrieval.proto'''
proto_library (
  name = 'mmu_face_retrieval__proto',
  srcs = [
    'mmu/face_retrieval.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/face_video_crop.proto'''
proto_library (
  name = 'mmu_face_video_crop__proto',
  srcs = [
    'mmu/face_video_crop.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_crop__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/face_vivo_detect.proto'''
proto_library (
  name = 'mmu_face_vivo_detect__proto',
  srcs = [
    'mmu/face_vivo_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/feature_dist.proto'''
proto_library (
  name = 'mmu_feature_dist__proto',
  srcs = [
    'mmu/feature_dist.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/field_type.proto'''
proto_library (
  name = 'mmu_field_type__proto',
  srcs = [
    'mmu/field_type.proto'
  ],
)

'''auto generate from mmu/fomo_porn_validator.proto'''
proto_library (
  name = 'mmu_fomo_porn_validator__proto',
  srcs = [
    'mmu/fomo_porn_validator.proto'
  ],
)

'''auto generate from mmu/gas.proto'''
proto_library (
  name = 'mmu_gas__proto',
  srcs = [
    'mmu/gas.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:ann_engine__proto',
    '//teams/ad/ad_proto/mmu/BUILD:ann_index__proto',
    '//teams/ad/ad_proto/mmu/BUILD:common_type__proto',
    '//teams/ad/ad_proto/mmu/BUILD:gas_extend__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_debug__proto',
    '//teams/ad/ad_proto/mmu/BUILD:vision_search_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/gas_extend.proto'''
proto_library (
  name = 'mmu_gas_extend__proto',
  srcs = [
    'mmu/gas_extend.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:vision_search_object__proto',
  ],
)

'''auto generate from mmu/general_python_service.proto'''
proto_library (
  name = 'mmu_general_python_service__proto',
  srcs = [
    'mmu/general_python_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/geo_img_dedup.proto'''
proto_library (
  name = 'mmu_geo_img_dedup__proto',
  srcs = [
    'mmu/geo_img_dedup.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/goods_info.proto'''
proto_library (
  name = 'mmu_goods_info__proto',
  srcs = [
    'mmu/goods_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common__proto',
  ],
)

'''auto generate from mmu/goods_status_service.proto'''
proto_library (
  name = 'mmu_goods_status_service__proto',
  srcs = [
    'mmu/goods_status_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/gossiper_validator.proto'''
proto_library (
  name = 'mmu_gossiper_validator__proto',
  srcs = [
    'mmu/gossiper_validator.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/grail_tree_tag_evaluate_result.proto'''
proto_library (
  name = 'mmu_grail_tree_tag_evaluate_result__proto',
  srcs = [
    'mmu/grail_tree_tag_evaluate_result.proto'
  ],
)

'''auto generate from mmu/hand_writing_recognition.proto'''
proto_library (
  name = 'mmu_hand_writing_recognition__proto',
  srcs = [
    'mmu/hand_writing_recognition.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/hashtag_params.proto'''
proto_library (
  name = 'mmu_hashtag_params__proto',
  srcs = [
    'mmu/hashtag_params.proto'
  ],
)

'''auto generate from mmu/head_analysis.proto'''
proto_library (
  name = 'mmu_head_analysis__proto',
  srcs = [
    'mmu/head_analysis.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/hello_world.proto'''
proto_library (
  name = 'mmu_hello_world__proto',
  srcs = [
    'mmu/hello_world.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/hetero_video_key_frame.proto depend blob_store.proto not ok'''

'''auto generate from mmu/hot_show_dedup.proto'''
proto_library (
  name = 'mmu_hot_show_dedup__proto',
  srcs = [
    'mmu/hot_show_dedup.proto'
  ],
)

'''auto generate from mmu/hot_spot_discover.proto'''
proto_library (
  name = 'mmu_hot_spot_discover__proto',
  srcs = [
    'mmu/hot_spot_discover.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/hotspot_query_dedup.proto'''
proto_library (
  name = 'mmu_hotspot_query_dedup__proto',
  srcs = [
    'mmu/hotspot_query_dedup.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/human_loop_dto.proto'''
proto_library (
  name = 'mmu_human_loop_dto__proto',
  srcs = [
    'mmu/human_loop_dto.proto'
  ],
)

'''auto generate from mmu/human_recognization.proto'''
proto_library (
  name = 'mmu_human_recognization__proto',
  srcs = [
    'mmu/human_recognization.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/i18n_source.proto'''
proto_library (
  name = 'mmu_i18n_source__proto',
  srcs = [
    'mmu/i18n_source.proto'
  ],
)

'''auto generate from mmu/id_card_ocr.proto'''
proto_library (
  name = 'mmu_id_card_ocr__proto',
  srcs = [
    'mmu/id_card_ocr.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/ik_analyzer.proto'''
proto_library (
  name = 'mmu_ik_analyzer__proto',
  srcs = [
    'mmu/ik_analyzer.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/image_avatar.proto'''
proto_library (
  name = 'mmu_image_avatar__proto',
  srcs = [
    'mmu/image_avatar.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/image_content_retrieval.proto'''
proto_library (
  name = 'mmu_image_content_retrieval__proto',
  srcs = [
    'mmu/image_content_retrieval.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:sift_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/image_cut.proto'''
proto_library (
  name = 'mmu_image_cut__proto',
  srcs = [
    'mmu/image_cut.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/image_definition.proto'''
proto_library (
  name = 'mmu_image_definition__proto',
  srcs = [
    'mmu/image_definition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/image_processing.proto'''
proto_library (
  name = 'mmu_image_processing__proto',
  srcs = [
    'mmu/image_processing.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/image_relation.proto'''
proto_library (
  name = 'mmu_image_relation__proto',
  srcs = [
    'mmu/image_relation.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/img_classification.proto'''
proto_library (
  name = 'mmu_img_classification__proto',
  srcs = [
    'mmu/img_classification.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/img_convert.proto'''
proto_library (
  name = 'mmu_img_convert__proto',
  srcs = [
    'mmu/img_convert.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/img_dedup_validator.proto'''
proto_library (
  name = 'mmu_img_dedup_validator__proto',
  srcs = [
    'mmu/img_dedup_validator.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_video_edge_detection__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/img_detect.proto'''
proto_library (
  name = 'mmu_img_detect__proto',
  srcs = [
    'mmu/img_detect.proto'
  ],
)

'''auto generate from mmu/img_resize.proto'''
proto_library (
  name = 'mmu_img_resize__proto',
  srcs = [
    'mmu/img_resize.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:img_convert__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/img_spectral_hash.proto'''
proto_library (
  name = 'mmu_img_spectral_hash__proto',
  srcs = [
    'mmu/img_spectral_hash.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/img_violation_validator.proto'''
proto_library (
  name = 'mmu_img_violation_validator__proto',
  srcs = [
    'mmu/img_violation_validator.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/imgl_feature_dto.proto depend video_key_frame.proto not ok'''

'''auto generate from mmu/indexing_message.proto'''
proto_library (
  name = 'mmu_indexing_message__proto',
  srcs = [
    'mmu/indexing_message.proto'
  ],
)

'''auto generate from mmu/india_selection_classfication_query.proto'''
proto_library (
  name = 'mmu_india_selection_classfication_query__proto',
  srcs = [
    'mmu/india_selection_classfication_query.proto'
  ],
)

'''auto generate from mmu/int8_encode_decode.proto'''
proto_library (
  name = 'mmu_int8_encode_decode__proto',
  srcs = [
    'mmu/int8_encode_decode.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/int8_quantize.proto'''
proto_library (
  name = 'mmu_int8_quantize__proto',
  srcs = [
    'mmu/int8_quantize.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/karaoke_correction.proto'''
proto_library (
  name = 'mmu_karaoke_correction__proto',
  srcs = [
    'mmu/karaoke_correction.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/key_frame_detection.proto'''
proto_library (
  name = 'mmu_key_frame_detection__proto',
  srcs = [
    'mmu/key_frame_detection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/kg_message.proto'''
proto_library (
  name = 'mmu_kg_message__proto',
  srcs = [
    'mmu/kg_message.proto'
  ],
)

'''auto generate from mmu/kmovie_speech_recognition.proto'''
proto_library (
  name = 'mmu_kmovie_speech_recognition__proto',
  srcs = [
    'mmu/kmovie_speech_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/ksearch_platform_web.proto'''
proto_library (
  name = 'mmu_ksearch_platform_web__proto',
  srcs = [
    'mmu/ksearch_platform_web.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/ksearch_splatform_web_api.proto'''
proto_library (
  name = 'mmu_ksearch_splatform_web_api__proto',
  srcs = [
    'mmu/ksearch_splatform_web_api.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/ksing_score.proto'''
proto_library (
  name = 'mmu_ksing_score__proto',
  srcs = [
    'mmu/ksing_score.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/kv_parent_log.proto'''
proto_library (
  name = 'mmu_kv_parent_log__proto',
  srcs = [
    'mmu/kv_parent_log.proto'
  ],
)

'''auto generate from mmu/kwai_audio_service.proto'''
proto_library (
  name = 'mmu_kwai_audio_service__proto',
  srcs = [
    'mmu/kwai_audio_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_kwai_result_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/kwai_offsite_dedup.proto'''
proto_library (
  name = 'mmu_kwai_offsite_dedup__proto',
  srcs = [
    'mmu/kwai_offsite_dedup.proto'
  ],
)

'''auto generate from mmu/kwai_pro_service.proto'''
proto_library (
  name = 'mmu_kwai_pro_service__proto',
  srcs = [
    'mmu/kwai_pro_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/kwai_similar_retrieval_result.proto'''
proto_library (
  name = 'mmu_kwai_similar_retrieval_result__proto',
  srcs = [
    'mmu/kwai_similar_retrieval_result.proto'
  ],
)

'''auto generate from mmu/language_identification.proto'''
proto_library (
  name = 'mmu_language_identification__proto',
  srcs = [
    'mmu/language_identification.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/leader_head_detection.proto'''
proto_library (
  name = 'mmu_leader_head_detection__proto',
  srcs = [
    'mmu/leader_head_detection.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_asr_offline_start.proto'''
proto_library (
  name = 'mmu_live_asr_offline_start__proto',
  srcs = [
    'mmu/live_asr_offline_start.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_chat_screenshot.proto'''
proto_library (
  name = 'mmu_live_chat_screenshot__proto',
  srcs = [
    'mmu/live_chat_screenshot.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_room_goods_asr_cat_tag.proto'''
proto_library (
  name = 'mmu_live_room_goods_asr_cat_tag__proto',
  srcs = [
    'mmu/live_room_goods_asr_cat_tag.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_search.proto'''
proto_library (
  name = 'mmu_live_search__proto',
  srcs = [
    'mmu/live_search.proto'
  ],
)

'''auto generate from mmu/live_shot_detection.proto'''
proto_library (
  name = 'mmu_live_shot_detection__proto',
  srcs = [
    'mmu/live_shot_detection.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_stream_audio.proto'''
proto_library (
  name = 'mmu_live_stream_audio__proto',
  srcs = [
    'mmu/live_stream_audio.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:live_stream_pull__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_stream_audio_porn.proto'''
proto_library (
  name = 'mmu_live_stream_audio_porn__proto',
  srcs = [
    'mmu/live_stream_audio_porn.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_stream_chatroom_label.proto'''
proto_library (
  name = 'mmu_live_stream_chatroom_label__proto',
  srcs = [
    'mmu/live_stream_chatroom_label.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_stream_comment_filter.proto'''
proto_library (
  name = 'mmu_live_stream_comment_filter__proto',
  srcs = [
    'mmu/live_stream_comment_filter.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_stream_count_feature.proto'''
proto_library (
  name = 'mmu_live_stream_count_feature__proto',
  srcs = [
    'mmu/live_stream_count_feature.proto'
  ],
)

'''auto generate from mmu/live_stream_cover_dedup.proto'''
proto_library (
  name = 'mmu_live_stream_cover_dedup__proto',
  srcs = [
    'mmu/live_stream_cover_dedup.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_stream_detect.proto'''
proto_library (
  name = 'mmu_live_stream_detect__proto',
  srcs = [
    'mmu/live_stream_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
  ],
)

'''auto generate from mmu/live_stream_goods_recog_log.proto'''
proto_library (
  name = 'mmu_live_stream_goods_recog_log__proto',
  srcs = [
    'mmu/live_stream_goods_recog_log.proto'
  ],
)

'''mmu/live_stream_moment_edit.proto depend blob_store.proto not ok'''

'''auto generate from mmu/live_stream_photo_check.proto'''
proto_library (
  name = 'mmu_live_stream_photo_check__proto',
  srcs = [
    'mmu/live_stream_photo_check.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_stream_photo_feature.proto'''
proto_library (
  name = 'mmu_live_stream_photo_feature__proto',
  srcs = [
    'mmu/live_stream_photo_feature.proto'
  ],
)

'''auto generate from mmu/live_stream_pull.proto'''
proto_library (
  name = 'mmu_live_stream_pull__proto',
  srcs = [
    'mmu/live_stream_pull.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition_e2e__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/live_stream_screenshots_result.proto'''
proto_library (
  name = 'mmu_live_stream_screenshots_result__proto',
  srcs = [
    'mmu/live_stream_screenshots_result.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    ':mmu_media_common__proto',
  ],
)

'''auto generate from mmu/live_stream_sell_detect.proto'''
proto_library (
  name = 'mmu_live_stream_sell_detect__proto',
  srcs = [
    'mmu/live_stream_sell_detect.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/livesteam_predict_summary.proto'''
proto_library (
  name = 'mmu_livesteam_predict_summary__proto',
  srcs = [
    'mmu/livesteam_predict_summary.proto'
  ],
)

'''mmu/logo_detection.proto depend blob_store.proto not ok'''

'''auto generate from mmu/long_video_segment_merge.proto'''
proto_library (
  name = 'mmu_long_video_segment_merge__proto',
  srcs = [
    'mmu/long_video_segment_merge.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/lyrics_recognition.proto'''
proto_library (
  name = 'mmu_lyrics_recognition__proto',
  srcs = [
    'mmu/lyrics_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/media_common.proto'''
proto_library (
  name = 'mmu_media_common__proto',
  srcs = [
    'mmu/media_common.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
  ],
)

'''auto generate from mmu/media_common_result_status.proto'''
proto_library (
  name = 'mmu_media_common_result_status__proto',
  srcs = [
    'mmu/media_common_result_status.proto'
  ],
)

'''auto generate from mmu/media_conversion.proto'''
proto_library (
  name = 'mmu_media_conversion__proto',
  srcs = [
    'mmu/media_conversion.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/merchat_goods_comment.proto'''
proto_library (
  name = 'mmu_merchat_goods_comment__proto',
  srcs = [
    'mmu/merchat_goods_comment.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_ad.proto'''
proto_library (
  name = 'mmu_mmu_ad__proto',
  srcs = [
    'mmu/mmu_ad.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_ad_cover_reco.proto'''
proto_library (
  name = 'mmu_mmu_ad_cover_reco__proto',
  srcs = [
    'mmu/mmu_ad_cover_reco.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:mmu_classification_predict__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_human_detect__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_ocr_detect__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_ad_info_label_result.proto'''
proto_library (
  name = 'mmu_mmu_ad_info_label_result__proto',
  srcs = [
    'mmu/mmu_ad_info_label_result.proto'
  ],
)

'''mmu/mmu_aicreation.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_algorithm_evaluate_label.proto'''
proto_library (
  name = 'mmu_mmu_algorithm_evaluate_label__proto',
  srcs = [
    'mmu/mmu_algorithm_evaluate_label.proto'
  ],
)

'''auto generate from mmu/mmu_another_me.proto'''
proto_library (
  name = 'mmu_mmu_another_me__proto',
  srcs = [
    'mmu/mmu_another_me.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_antispam_feature.proto'''
proto_library (
  name = 'mmu_mmu_antispam_feature__proto',
  srcs = [
    'mmu/mmu_antispam_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_app_detection.proto'''
proto_library (
  name = 'mmu_mmu_app_detection__proto',
  srcs = [
    'mmu/mmu_app_detection.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_audio_feature.proto depend blob_store.proto not ok'''

'''mmu/mmu_audio_retrieval.proto depend blob_store.proto not ok'''

'''mmu/mmu_audio_tag.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_avatar_retrieval.proto'''
proto_library (
  name = 'mmu_mmu_avatar_retrieval__proto',
  srcs = [
    'mmu/mmu_avatar_retrieval.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_base_service.proto'''
proto_library (
  name = 'mmu_mmu_base_service__proto',
  srcs = [
    'mmu/mmu_base_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:ocr_validator__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
    ':mmu_obj_detection__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_batch_retrieval.proto'''
proto_library (
  name = 'mmu_mmu_batch_retrieval__proto',
  srcs = [
    'mmu/mmu_batch_retrieval.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:geo_img_dedup__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_boost.proto'''
proto_library (
  name = 'mmu_mmu_boost__proto',
  srcs = [
    'mmu/mmu_boost.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_brand_validate.proto'''
proto_library (
  name = 'mmu_mmu_brand_validate__proto',
  srcs = [
    'mmu/mmu_brand_validate.proto'
  ],
)

'''auto generate from mmu/mmu_business_service.proto'''
proto_library (
  name = 'mmu_mmu_business_service__proto',
  srcs = [
    'mmu/mmu_business_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition_keyword__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_caption_generate.proto'''
proto_library (
  name = 'mmu_mmu_caption_generate__proto',
  srcs = [
    'mmu/mmu_caption_generate.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_classification_predict.proto'''
proto_library (
  name = 'mmu_mmu_classification_predict__proto',
  srcs = [
    'mmu/mmu_classification_predict.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_comment_gen.proto'''
proto_library (
  name = 'mmu_mmu_comment_gen__proto',
  srcs = [
    'mmu/mmu_comment_gen.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_comment_score.proto'''
proto_library (
  name = 'mmu_mmu_comment_score__proto',
  srcs = [
    'mmu/mmu_comment_score.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_common_predict.proto'''
proto_library (
  name = 'mmu_mmu_common_predict__proto',
  srcs = [
    'mmu/mmu_common_predict.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_content_id.proto'''
proto_library (
  name = 'mmu_mmu_content_id__proto',
  srcs = [
    'mmu/mmu_content_id.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_content_level_prediction.proto'''
proto_library (
  name = 'mmu_mmu_content_level_prediction__proto',
  srcs = [
    'mmu/mmu_content_level_prediction.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_contract_audit.proto'''
proto_library (
  name = 'mmu_mmu_contract_audit__proto',
  srcs = [
    'mmu/mmu_contract_audit.proto'
  ],
)

'''mmu/mmu_copyright_match_tool.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_copyright_photo.proto'''
proto_library (
  name = 'mmu_mmu_copyright_photo__proto',
  srcs = [
    'mmu/mmu_copyright_photo.proto'
  ],
)

'''auto generate from mmu/mmu_corona.proto'''
proto_library (
  name = 'mmu_mmu_corona__proto',
  srcs = [
    'mmu/mmu_corona.proto'
  ],
)

'''auto generate from mmu/mmu_crawler.proto'''
proto_library (
  name = 'mmu_mmu_crawler__proto',
  srcs = [
    'mmu/mmu_crawler.proto'
  ],
)

'''auto generate from mmu/mmu_danmaku_service.proto'''
proto_library (
  name = 'mmu_mmu_danmaku_service__proto',
  srcs = [
    'mmu/mmu_danmaku_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_data_backtrace.proto'''
proto_library (
  name = 'mmu_mmu_data_backtrace__proto',
  srcs = [
    'mmu/mmu_data_backtrace.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_data_tracking.proto'''
proto_library (
  name = 'mmu_mmu_data_tracking__proto',
  srcs = [
    'mmu/mmu_data_tracking.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_dedup_photo_status.proto'''
proto_library (
  name = 'mmu_mmu_dedup_photo_status__proto',
  srcs = [
    'mmu/mmu_dedup_photo_status.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_demo.proto'''
proto_library (
  name = 'mmu_mmu_demo__proto',
  srcs = [
    'mmu/mmu_demo.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:audio_seperation__proto',
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:kmovie_speech_recognition__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_another_me__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_detection_template.proto'''
proto_library (
  name = 'mmu_mmu_detection_template__proto',
  srcs = [
    'mmu/mmu_detection_template.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:dup_validator__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_douyin_video.proto'''
proto_library (
  name = 'mmu_mmu_douyin_video__proto',
  srcs = [
    'mmu/mmu_douyin_video.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:bow_index__proto',
    '//teams/ad/ad_proto/mmu/BUILD:dup_validator__proto',
  ],
)

'''auto generate from mmu/mmu_dt_result_message.proto'''
proto_library (
  name = 'mmu_mmu_dt_result_message__proto',
  srcs = [
    'mmu/mmu_dt_result_message.proto'
  ],
)

'''auto generate from mmu/mmu_dujuan_user.proto'''
proto_library (
  name = 'mmu_mmu_dujuan_user__proto',
  srcs = [
    'mmu/mmu_dujuan_user.proto'
  ],
)

'''auto generate from mmu/mmu_dup_set.proto'''
proto_library (
  name = 'mmu_mmu_dup_set__proto',
  srcs = [
    'mmu/mmu_dup_set.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_dup_set_sdk.proto'''
proto_library (
  name = 'mmu_mmu_dup_set_sdk__proto',
  srcs = [
    'mmu/mmu_dup_set_sdk.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_dup_strategy.proto'''
proto_library (
  name = 'mmu_mmu_dup_strategy__proto',
  srcs = [
    'mmu/mmu_dup_strategy.proto'
  ],
)

'''auto generate from mmu/mmu_ecmc_emb_cluster_service.proto'''
proto_library (
  name = 'mmu_mmu_ecmc_emb_cluster_service__proto',
  srcs = [
    'mmu/mmu_ecmc_emb_cluster_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_edu_category.proto'''
proto_library (
  name = 'mmu_mmu_edu_category__proto',
  srcs = [
    'mmu/mmu_edu_category.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:edu_params__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_emb_cluster_message.proto'''
proto_library (
  name = 'mmu_mmu_emb_cluster_message__proto',
  srcs = [
    'mmu/mmu_emb_cluster_message.proto'
  ],
)

'''auto generate from mmu/mmu_emotion_cluster.proto'''
proto_library (
  name = 'mmu_mmu_emotion_cluster__proto',
  srcs = [
    'mmu/mmu_emotion_cluster.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_face_analysis.proto'''
proto_library (
  name = 'mmu_mmu_face_analysis__proto',
  srcs = [
    'mmu/mmu_face_analysis.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_face_cluster.proto'''
proto_library (
  name = 'mmu_mmu_face_cluster__proto',
  srcs = [
    'mmu/mmu_face_cluster.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_face_detect.proto'''
proto_library (
  name = 'mmu_mmu_face_detect__proto',
  srcs = [
    'mmu/mmu_face_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_face_v3__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_face_lib.proto'''
proto_library (
  name = 'mmu_mmu_face_lib__proto',
  srcs = [
    'mmu/mmu_face_lib.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_retrieval__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_face_v3.proto'''
proto_library (
  name = 'mmu_mmu_face_v3__proto',
  srcs = [
    'mmu/mmu_face_v3.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_famous_face.proto'''
proto_library (
  name = 'mmu_mmu_famous_face__proto',
  srcs = [
    'mmu/mmu_famous_face.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_content_id__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_feature_loader.proto'''
proto_library (
  name = 'mmu_mmu_feature_loader__proto',
  srcs = [
    'mmu/mmu_feature_loader.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_feature_replay.proto'''
proto_library (
  name = 'mmu_mmu_feature_replay__proto',
  srcs = [
    'mmu/mmu_feature_replay.proto'
  ],
)

'''auto generate from mmu/mmu_feature_result.proto'''
proto_library (
  name = 'mmu_mmu_feature_result__proto',
  srcs = [
    'mmu/mmu_feature_result.proto'
  ],
)

'''auto generate from mmu/mmu_feature_retrieval.proto'''
proto_library (
  name = 'mmu_mmu_feature_retrieval__proto',
  srcs = [
    'mmu/mmu_feature_retrieval.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_flanker.proto'''
proto_library (
  name = 'mmu_mmu_flanker__proto',
  srcs = [
    'mmu/mmu_flanker.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:geo_img_dedup__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_flanker_proxy.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_game_face.proto'''
proto_library (
  name = 'mmu_mmu_game_face__proto',
  srcs = [
    'mmu/mmu_game_face.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_general_data_sync_log.proto'''
proto_library (
  name = 'mmu_mmu_general_data_sync_log__proto',
  srcs = [
    'mmu/mmu_general_data_sync_log.proto'
  ],
)

'''auto generate from mmu/mmu_goods.proto'''
proto_library (
  name = 'mmu_mmu_goods__proto',
  srcs = [
    'mmu/mmu_goods.proto'
  ],
)

'''auto generate from mmu/mmu_goods_hot.proto'''
proto_library (
  name = 'mmu_mmu_goods_hot__proto',
  srcs = [
    'mmu/mmu_goods_hot.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_goods_publish.proto'''
proto_library (
  name = 'mmu_mmu_goods_publish__proto',
  srcs = [
    'mmu/mmu_goods_publish.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_goods_retrieval.proto'''
proto_library (
  name = 'mmu_mmu_goods_retrieval__proto',
  srcs = [
    'mmu/mmu_goods_retrieval.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_goods_service.proto'''
proto_library (
  name = 'mmu_mmu_goods_service__proto',
  srcs = [
    'mmu/mmu_goods_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_groovy_engine_service.proto'''
proto_library (
  name = 'mmu_mmu_groovy_engine_service__proto',
  srcs = [
    'mmu/mmu_groovy_engine_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_guess_you_want_label.proto'''
proto_library (
  name = 'mmu_mmu_guess_you_want_label__proto',
  srcs = [
    'mmu/mmu_guess_you_want_label.proto'
  ],
)

'''auto generate from mmu/mmu_hackathon.proto'''
proto_library (
  name = 'mmu_mmu_hackathon__proto',
  srcs = [
    'mmu/mmu_hackathon.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_hash_tag.proto'''
proto_library (
  name = 'mmu_mmu_hash_tag__proto',
  srcs = [
    'mmu/mmu_hash_tag.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_hetu_tag_service.proto'''
proto_library (
  name = 'mmu_mmu_hetu_tag_service__proto',
  srcs = [
    'mmu/mmu_hetu_tag_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_hot_face_cluster.proto'''
proto_library (
  name = 'mmu_mmu_hot_face_cluster__proto',
  srcs = [
    'mmu/mmu_hot_face_cluster.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_hot_keyword.proto'''
proto_library (
  name = 'mmu_mmu_hot_keyword__proto',
  srcs = [
    'mmu/mmu_hot_keyword.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_hot_photo.proto'''
proto_library (
  name = 'mmu_mmu_hot_photo__proto',
  srcs = [
    'mmu/mmu_hot_photo.proto'
  ],
)

'''auto generate from mmu/mmu_hot_related_video.proto'''
proto_library (
  name = 'mmu_mmu_hot_related_video__proto',
  srcs = [
    'mmu/mmu_hot_related_video.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_human_detect.proto'''
proto_library (
  name = 'mmu_mmu_human_detect__proto',
  srcs = [
    'mmu/mmu_human_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_id_card_verify.proto'''
proto_library (
  name = 'mmu_mmu_id_card_verify__proto',
  srcs = [
    'mmu/mmu_id_card_verify.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_im_media_detection.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_image_detect.proto'''
proto_library (
  name = 'mmu_mmu_image_detect__proto',
  srcs = [
    'mmu/mmu_image_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_web_image_detection__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_image_download.proto'''
proto_library (
  name = 'mmu_mmu_image_download__proto',
  srcs = [
    'mmu/mmu_image_download.proto'
  ],
)

'''auto generate from mmu/mmu_image_match.proto'''
proto_library (
  name = 'mmu_mmu_image_match__proto',
  srcs = [
    'mmu/mmu_image_match.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_image_object_feature.proto'''
proto_library (
  name = 'mmu_mmu_image_object_feature__proto',
  srcs = [
    'mmu/mmu_image_object_feature.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
  ],
)

'''auto generate from mmu/mmu_image_orientation.proto'''
proto_library (
  name = 'mmu_mmu_image_orientation__proto',
  srcs = [
    'mmu/mmu_image_orientation.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_index_message.proto'''
proto_library (
  name = 'mmu_mmu_index_message__proto',
  srcs = [
    'mmu/mmu_index_message.proto'
  ],
)

'''auto generate from mmu/mmu_intown_result_message.proto'''
proto_library (
  name = 'mmu_mmu_intown_result_message__proto',
  srcs = [
    'mmu/mmu_intown_result_message.proto'
  ],
)

'''auto generate from mmu/mmu_item_attr.proto'''
proto_library (
  name = 'mmu_mmu_item_attr__proto',
  srcs = [
    'mmu/mmu_item_attr.proto'
  ],
)

'''auto generate from mmu/mmu_kg_graph.proto'''
proto_library (
  name = 'mmu_mmu_kg_graph__proto',
  srcs = [
    'mmu/mmu_kg_graph.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_kg_hashtag.proto'''
proto_library (
  name = 'mmu_mmu_kg_hashtag__proto',
  srcs = [
    'mmu/mmu_kg_hashtag.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_kiwi_data_flow.proto'''
proto_library (
  name = 'mmu_mmu_kiwi_data_flow__proto',
  srcs = [
    'mmu/mmu_kiwi_data_flow.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_kkd_service.proto'''
proto_library (
  name = 'mmu_mmu_kkd_service__proto',
  srcs = [
    'mmu/mmu_kkd_service.proto'
  ],
  deps = [
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_kmeans_query.proto'''
proto_library (
  name = 'mmu_mmu_kmeans_query__proto',
  srcs = [
    'mmu/mmu_kmeans_query.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_kmovie_service.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_kwai_asr.proto'''
proto_library (
  name = 'mmu_mmu_kwai_asr__proto',
  srcs = [
    'mmu/mmu_kwai_asr.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_kwai_result_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_kwai_audio_language.proto'''
proto_library (
  name = 'mmu_mmu_kwai_audio_language__proto',
  srcs = [
    'mmu/mmu_kwai_audio_language.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_kwai_result_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_kwai_game_detect.proto'''
proto_library (
  name = 'mmu_mmu_kwai_game_detect__proto',
  srcs = [
    'mmu/mmu_kwai_game_detect.proto'
  ],
)

'''auto generate from mmu/mmu_kwai_pro_message.proto'''
proto_library (
  name = 'mmu_mmu_kwai_pro_message__proto',
  srcs = [
    'mmu/mmu_kwai_pro_message.proto'
  ],
  deps = [
    ':mmu_common_message__proto',
  ],
)

'''auto generate from mmu/mmu_kwai_result_message.proto'''
proto_library (
  name = 'mmu_mmu_kwai_result_message__proto',
  srcs = [
    'mmu/mmu_kwai_result_message.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/common/BUILD:bucket__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:video_live_record__proto',
    '//teams/ad/ad_proto/mmu/BUILD:common_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:kwai_offsite_dedup__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_oversea_result_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:oversea_ocr_ext__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition_e2e__proto',
  ],
)

'''auto generate from mmu/mmu_kwaipro_audio_multi_tag.proto'''
proto_library (
  name = 'mmu_mmu_kwaipro_audio_multi_tag__proto',
  srcs = [
    'mmu/mmu_kwaipro_audio_multi_tag.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_label_event.proto'''
proto_library (
  name = 'mmu_mmu_label_event__proto',
  srcs = [
    'mmu/mmu_label_event.proto'
  ],
)

'''mmu/mmu_learning_data.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_license_detect.proto'''
proto_library (
  name = 'mmu_mmu_license_detect__proto',
  srcs = [
    'mmu/mmu_license_detect.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_live_cp.proto'''
proto_library (
  name = 'mmu_mmu_live_cp__proto',
  srcs = [
    'mmu/mmu_live_cp.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:ocr_validator__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_live_message.proto'''
proto_library (
  name = 'mmu_mmu_live_message__proto',
  srcs = [
    'mmu/mmu_live_message.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/mmu/BUILD:common_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:dl_feature__proto',
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:model_serving__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition_e2e__proto',
  ],
)

'''mmu/mmu_live_stream_asr.proto depend mmu_media_safety_detect.proto not ok'''

'''auto generate from mmu/mmu_live_stream_cluster.proto'''
proto_library (
  name = 'mmu_mmu_live_stream_cluster__proto',
  srcs = [
    'mmu/mmu_live_stream_cluster.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_live_stream_detect.proto'''
proto_library (
  name = 'mmu_mmu_live_stream_detect__proto',
  srcs = [
    'mmu/mmu_live_stream_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
  ],
)

'''mmu/mmu_live_stream_download.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_live_stream_embedding.proto'''
proto_library (
  name = 'mmu_mmu_live_stream_embedding__proto',
  srcs = [
    'mmu/mmu_live_stream_embedding.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_live_stream_face.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_live_stream_game_score.proto'''
proto_library (
  name = 'mmu_mmu_live_stream_game_score__proto',
  srcs = [
    'mmu/mmu_live_stream_game_score.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:live_stream_detect__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_game_query__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_live_stream_k_music.proto'''
proto_library (
  name = 'mmu_mmu_live_stream_k_music__proto',
  srcs = [
    'mmu/mmu_live_stream_k_music.proto'
  ],
)

'''auto generate from mmu/mmu_live_stream_realtime_detect.proto'''
proto_library (
  name = 'mmu_mmu_live_stream_realtime_detect__proto',
  srcs = [
    'mmu/mmu_live_stream_realtime_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_live_stream_score.proto'''
proto_library (
  name = 'mmu_mmu_live_stream_score__proto',
  srcs = [
    'mmu/mmu_live_stream_score.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:live_stream_detect__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_live_stream_tag.proto'''
proto_library (
  name = 'mmu_mmu_live_stream_tag__proto',
  srcs = [
    'mmu/mmu_live_stream_tag.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_liveness_detect.proto'''
proto_library (
  name = 'mmu_mmu_liveness_detect__proto',
  srcs = [
    'mmu/mmu_liveness_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_livesku_history.proto'''
proto_library (
  name = 'mmu_mmu_livesku_history__proto',
  srcs = [
    'mmu/mmu_livesku_history.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:live_stream_detect__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_location_service.proto'''
proto_library (
  name = 'mmu_mmu_location_service__proto',
  srcs = [
    'mmu/mmu_location_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_logo_detection_sdk.proto depend blob_store.proto not ok'''

'''mmu/mmu_long_video_search.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_lr_predict.proto'''
proto_library (
  name = 'mmu_mmu_lr_predict__proto',
  srcs = [
    'mmu/mmu_lr_predict.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_maintitle_push_embedding.proto'''
proto_library (
  name = 'mmu_mmu_maintitle_push_embedding__proto',
  srcs = [
    'mmu/mmu_maintitle_push_embedding.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_marketing_attr.proto'''
proto_library (
  name = 'mmu_mmu_marketing_attr__proto',
  srcs = [
    'mmu/mmu_marketing_attr.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_media_content_detect.proto depend mmu_media_detect.proto not ok'''

'''mmu/mmu_media_detect.proto depend blob_store.proto not ok'''

'''mmu/mmu_media_safety_detect.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_merchant_order.proto'''
proto_library (
  name = 'mmu_mmu_merchant_order__proto',
  srcs = [
    'mmu/mmu_merchant_order.proto'
  ],
)

'''auto generate from mmu/mmu_merchant_photo.proto'''
proto_library (
  name = 'mmu_mmu_merchant_photo__proto',
  srcs = [
    'mmu/mmu_merchant_photo.proto'
  ],
)

'''auto generate from mmu/mmu_merchant_reco.proto'''
proto_library (
  name = 'mmu_mmu_merchant_reco__proto',
  srcs = [
    'mmu/mmu_merchant_reco.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':kuaishou_reco_platform_log_common_sample_log__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_merchant_service.proto'''
proto_library (
  name = 'mmu_mmu_merchant_service__proto',
  srcs = [
    'mmu/mmu_merchant_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:common_type__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_message_entity.proto'''
proto_library (
  name = 'mmu_mmu_message_entity__proto',
  srcs = [
    'mmu/mmu_message_entity.proto'
  ],
)

'''auto generate from mmu/mmu_mmr_feature_retrieval.proto'''
proto_library (
  name = 'mmu_mmu_mmr_feature_retrieval__proto',
  srcs = [
    'mmu/mmu_mmr_feature_retrieval.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_moco_similar.proto'''
proto_library (
  name = 'mmu_mmu_moco_similar__proto',
  srcs = [
    'mmu/mmu_moco_similar.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_motion_vector.proto'''
proto_library (
  name = 'mmu_mmu_motion_vector__proto',
  srcs = [
    'mmu/mmu_motion_vector.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_mulight_service.proto'''
proto_library (
  name = 'mmu_mmu_mulight_service__proto',
  srcs = [
    'mmu/mmu_mulight_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_music.proto'''
proto_library (
  name = 'mmu_mmu_music__proto',
  srcs = [
    'mmu/mmu_music.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:dl_feature__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_music_dup_query.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_music_feature.proto'''
proto_library (
  name = 'mmu_mmu_music_feature__proto',
  srcs = [
    'mmu/mmu_music_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:audio_dup_query__proto',
    '//teams/ad/ad_proto/mmu/BUILD:music_mars__proto',
  ],
)

'''mmu/mmu_music_reco.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_music_reco_label.proto'''
proto_library (
  name = 'mmu_mmu_music_reco_label__proto',
  srcs = [
    'mmu/mmu_music_reco_label.proto'
  ],
)

'''auto generate from mmu/mmu_music_recognition.proto'''
proto_library (
  name = 'mmu_mmu_music_recognition__proto',
  srcs = [
    'mmu/mmu_music_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_music_rhythm.proto'''
proto_library (
  name = 'mmu_mmu_music_rhythm__proto',
  srcs = [
    'mmu/mmu_music_rhythm.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':kuaishou_music_music__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_music_tag.proto'''
proto_library (
  name = 'mmu_mmu_music_tag__proto',
  srcs = [
    'mmu/mmu_music_tag.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_new_user_photo.proto'''
proto_library (
  name = 'mmu_mmu_new_user_photo__proto',
  srcs = [
    'mmu/mmu_new_user_photo.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:compute_video_clusterid__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_nlp_service.proto'''
proto_library (
  name = 'mmu_mmu_nlp_service__proto',
  srcs = [
    'mmu/mmu_nlp_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:model_serving__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_non_original_user.proto'''
proto_library (
  name = 'mmu_mmu_non_original_user__proto',
  srcs = [
    'mmu/mmu_non_original_user.proto'
  ],
)

'''auto generate from mmu/mmu_ocr_detect.proto'''
proto_library (
  name = 'mmu_mmu_ocr_detect__proto',
  srcs = [
    'mmu/mmu_ocr_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_ocr_fg_detect.proto'''
proto_library (
  name = 'mmu_mmu_ocr_fg_detect__proto',
  srcs = [
    'mmu/mmu_ocr_fg_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_origin_user_apply.proto'''
proto_library (
  name = 'mmu_mmu_origin_user_apply__proto',
  srcs = [
    'mmu/mmu_origin_user_apply.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_original_protection.proto'''
proto_library (
  name = 'mmu_mmu_original_protection__proto',
  srcs = [
    'mmu/mmu_original_protection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_oversea_illegal_hit.proto'''
proto_library (
  name = 'mmu_mmu_oversea_illegal_hit__proto',
  srcs = [
    'mmu/mmu_oversea_illegal_hit.proto'
  ],
)

'''mmu/mmu_oversea_logo_detection_sdk.proto depend logo_detection.proto not ok'''

'''auto generate from mmu/mmu_oversea_music.proto'''
proto_library (
  name = 'mmu_mmu_oversea_music__proto',
  srcs = [
    'mmu/mmu_oversea_music.proto'
  ],
)

'''auto generate from mmu/mmu_oversea_result_message.proto'''
proto_library (
  name = 'mmu_mmu_oversea_result_message__proto',
  srcs = [
    'mmu/mmu_oversea_result_message.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/kuaishou/BUILD:video_live_record__proto',
    '//teams/ad/ad_proto/mmu/BUILD:audio_dup_query__proto',
    '//teams/ad/ad_proto/mmu/BUILD:common_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
  ],
)

'''auto generate from mmu/mmu_oversea_safety_image_search.proto'''
proto_library (
  name = 'mmu_mmu_oversea_safety_image_search__proto',
  srcs = [
    'mmu/mmu_oversea_safety_image_search.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_oversea_video.proto'''
proto_library (
  name = 'mmu_mmu_oversea_video__proto',
  srcs = [
    'mmu/mmu_oversea_video.proto'
  ],
)

'''mmu/mmu_photo_detect.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_photo_embedding.proto'''
proto_library (
  name = 'mmu_mmu_photo_embedding__proto',
  srcs = [
    'mmu/mmu_photo_embedding.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_photo_hash_tag.proto'''
proto_library (
  name = 'mmu_mmu_photo_hash_tag__proto',
  srcs = [
    'mmu/mmu_photo_hash_tag.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_photo_md5.proto'''
proto_library (
  name = 'mmu_mmu_photo_md5__proto',
  srcs = [
    'mmu/mmu_photo_md5.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_photo_message.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_photo_score.proto'''
proto_library (
  name = 'mmu_mmu_photo_score__proto',
  srcs = [
    'mmu/mmu_photo_score.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_photo_sound_track_content.proto'''
proto_library (
  name = 'mmu_mmu_photo_sound_track_content__proto',
  srcs = [
    'mmu/mmu_photo_sound_track_content.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_photo_text_detail.proto'''
proto_library (
  name = 'mmu_mmu_photo_text_detail__proto',
  srcs = [
    'mmu/mmu_photo_text_detail.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_proxy_service.proto'''
proto_library (
  name = 'mmu_mmu_proxy_service__proto',
  srcs = [
    'mmu/mmu_proxy_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_qrcode.proto'''
proto_library (
  name = 'mmu_mmu_qrcode__proto',
  srcs = [
    'mmu/mmu_qrcode.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_quality_score_detect.proto'''
proto_library (
  name = 'mmu_mmu_quality_score_detect__proto',
  srcs = [
    'mmu/mmu_quality_score_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_query_photo_emb.proto'''
proto_library (
  name = 'mmu_mmu_query_photo_emb__proto',
  srcs = [
    'mmu/mmu_query_photo_emb.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_reco_feature.proto'''
proto_library (
  name = 'mmu_mmu_reco_feature__proto',
  srcs = [
    'mmu/mmu_reco_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_reco_text_bert_emb.proto'''
proto_library (
  name = 'mmu_mmu_reco_text_bert_emb__proto',
  srcs = [
    'mmu/mmu_reco_text_bert_emb.proto'
  ],
)

'''auto generate from mmu/mmu_reco_user_emb.proto'''
proto_library (
  name = 'mmu_mmu_reco_user_emb__proto',
  srcs = [
    'mmu/mmu_reco_user_emb.proto'
  ],
  deps = [
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_record_good_time.proto'''
proto_library (
  name = 'mmu_mmu_record_good_time__proto',
  srcs = [
    'mmu/mmu_record_good_time.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_related_video_search.proto'''
proto_library (
  name = 'mmu_mmu_related_video_search__proto',
  srcs = [
    'mmu/mmu_related_video_search.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:common_type__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_retr.proto'''
proto_library (
  name = 'mmu_mmu_retr__proto',
  srcs = [
    'mmu/mmu_retr.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_rt_speech.proto'''
proto_library (
  name = 'mmu_mmu_rt_speech__proto',
  srcs = [
    'mmu/mmu_rt_speech.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:stream_speech_recognition__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_safe_detect.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_safety_to_b.proto'''
proto_library (
  name = 'mmu_mmu_safety_to_b__proto',
  srcs = [
    'mmu/mmu_safety_to_b.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_sds_detect.proto'''
proto_library (
  name = 'mmu_mmu_sds_detect__proto',
  srcs = [
    'mmu/mmu_sds_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_search_common_message.proto'''
proto_library (
  name = 'mmu_mmu_search_common_message__proto',
  srcs = [
    'mmu/mmu_search_common_message.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:video_search_dataflow__proto',
  ],
)

'''auto generate from mmu/mmu_search_cover.proto'''
proto_library (
  name = 'mmu_mmu_search_cover__proto',
  srcs = [
    'mmu/mmu_search_cover.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_search_evaluate.proto'''
proto_library (
  name = 'mmu_mmu_search_evaluate__proto',
  srcs = [
    'mmu/mmu_search_evaluate.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_search_feature.proto'''
proto_library (
  name = 'mmu_mmu_search_feature__proto',
  srcs = [
    'mmu/mmu_search_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_search_feed_hot.proto'''
proto_library (
  name = 'mmu_mmu_search_feed_hot__proto',
  srcs = [
    'mmu/mmu_search_feed_hot.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_search_feedback.proto'''
proto_library (
  name = 'mmu_mmu_search_feedback__proto',
  srcs = [
    'mmu/mmu_search_feedback.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_search_general_index_doc.proto'''
proto_library (
  name = 'mmu_mmu_search_general_index_doc__proto',
  srcs = [
    'mmu/mmu_search_general_index_doc.proto'
  ],
)

'''mmu/mmu_search_home_content.proto depend search_aladdin_content.proto not ok'''

'''auto generate from mmu/mmu_search_home_hot.proto'''
proto_library (
  name = 'mmu_mmu_search_home_hot__proto',
  srcs = [
    'mmu/mmu_search_home_hot.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_debug__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_search_operation_log.proto'''
proto_library (
  name = 'mmu_mmu_search_operation_log__proto',
  srcs = [
    'mmu/mmu_search_operation_log.proto'
  ],
)

'''auto generate from mmu/mmu_search_resource.proto'''
proto_library (
  name = 'mmu_mmu_search_resource__proto',
  srcs = [
    'mmu/mmu_search_resource.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_search_words.proto'''
proto_library (
  name = 'mmu_mmu_search_words__proto',
  srcs = [
    'mmu/mmu_search_words.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_self_dup.proto'''
proto_library (
  name = 'mmu_mmu_self_dup__proto',
  srcs = [
    'mmu/mmu_self_dup.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_shop_item_detect.proto'''
proto_library (
  name = 'mmu_mmu_shop_item_detect__proto',
  srcs = [
    'mmu/mmu_shop_item_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
)

'''auto generate from mmu/mmu_shop_live_smart_edit.proto'''
proto_library (
  name = 'mmu_mmu_shop_live_smart_edit__proto',
  srcs = [
    'mmu/mmu_shop_live_smart_edit.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_livesku_history__proto',
    '//teams/ad/ad_proto/mmu/BUILD:stream_speech_recognition__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_shop_object_retrieval.proto'''
proto_library (
  name = 'mmu_mmu_shop_object_retrieval__proto',
  srcs = [
    'mmu/mmu_shop_object_retrieval.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
    ':mmu_obj_detection__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_sim_hash.proto'''
proto_library (
  name = 'mmu_mmu_sim_hash__proto',
  srcs = [
    'mmu/mmu_sim_hash.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_similar_feature.proto'''
proto_library (
  name = 'mmu_mmu_similar_feature__proto',
  srcs = [
    'mmu/mmu_similar_feature.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_similar_user.proto'''
proto_library (
  name = 'mmu_mmu_similar_user__proto',
  srcs = [
    'mmu/mmu_similar_user.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_similar_video_tag.proto'''
proto_library (
  name = 'mmu_mmu_similar_video_tag__proto',
  srcs = [
    'mmu/mmu_similar_video_tag.proto'
  ],
  deps = [
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_snack_service.proto'''
proto_library (
  name = 'mmu_mmu_snack_service__proto',
  srcs = [
    'mmu/mmu_snack_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_snap_detection_sdk.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_speech_recognition.proto'''
proto_library (
  name = 'mmu_mmu_speech_recognition__proto',
  srcs = [
    'mmu/mmu_speech_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_subtitle_generation.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_subtitle_recognition.proto'''
proto_library (
  name = 'mmu_mmu_subtitle_recognition__proto',
  srcs = [
    'mmu/mmu_subtitle_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_tag.proto'''
proto_library (
  name = 'mmu_mmu_tag__proto',
  srcs = [
    'mmu/mmu_tag.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_tag_detect.proto'''
proto_library (
  name = 'mmu_mmu_tag_detect__proto',
  srcs = [
    'mmu/mmu_tag_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_common_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_tag_emb.proto'''
proto_library (
  name = 'mmu_mmu_tag_emb__proto',
  srcs = [
    'mmu/mmu_tag_emb.proto'
  ],
)

'''auto generate from mmu/mmu_tag_photo_admin.proto'''
proto_library (
  name = 'mmu_mmu_tag_photo_admin__proto',
  srcs = [
    'mmu/mmu_tag_photo_admin.proto'
  ],
)

'''auto generate from mmu/mmu_tag_query.proto'''
proto_library (
  name = 'mmu_mmu_tag_query__proto',
  srcs = [
    'mmu/mmu_tag_query.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_tag_relevance.proto'''
proto_library (
  name = 'mmu_mmu_tag_relevance__proto',
  srcs = [
    'mmu/mmu_tag_relevance.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_template_info.proto'''
proto_library (
  name = 'mmu_mmu_template_info__proto',
  srcs = [
    'mmu/mmu_template_info.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_temporary_dup_template.proto'''
proto_library (
  name = 'mmu_mmu_temporary_dup_template__proto',
  srcs = [
    'mmu/mmu_temporary_dup_template.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
)

'''mmu/mmu_text_detect.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_text_feature.proto'''
proto_library (
  name = 'mmu_mmu_text_feature__proto',
  srcs = [
    'mmu/mmu_text_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_text_to_speech.proto'''
proto_library (
  name = 'mmu_mmu_text_to_speech__proto',
  srcs = [
    'mmu/mmu_text_to_speech.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_translate_service.proto'''
proto_library (
  name = 'mmu_mmu_translate_service__proto',
  srcs = [
    'mmu/mmu_translate_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_tx_copyright_dujuan.proto'''
proto_library (
  name = 'mmu_mmu_tx_copyright_dujuan__proto',
  srcs = [
    'mmu/mmu_tx_copyright_dujuan.proto'
  ],
)

'''auto generate from mmu/mmu_tx_du_juan_guess.proto'''
proto_library (
  name = 'mmu_mmu_tx_du_juan_guess__proto',
  srcs = [
    'mmu/mmu_tx_du_juan_guess.proto'
  ],
)

'''auto generate from mmu/mmu_tx_page_query.proto'''
proto_library (
  name = 'mmu_mmu_tx_page_query__proto',
  srcs = [
    'mmu/mmu_tx_page_query.proto'
  ],
)

'''auto generate from mmu/mmu_tx_service.proto'''
proto_library (
  name = 'mmu_mmu_tx_service__proto',
  srcs = [
    'mmu/mmu_tx_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_union_audio.proto depend mmu_audio_retrieval.proto not ok'''

'''auto generate from mmu/mmu_upload_logo_detect.proto'''
proto_library (
  name = 'mmu_mmu_upload_logo_detect__proto',
  srcs = [
    'mmu/mmu_upload_logo_detect.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_upload_tag.proto'''
proto_library (
  name = 'mmu_mmu_upload_tag__proto',
  srcs = [
    'mmu/mmu_upload_tag.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_user_brand_profile.proto'''
proto_library (
  name = 'mmu_mmu_user_brand_profile__proto',
  srcs = [
    'mmu/mmu_user_brand_profile.proto'
  ],
)

'''auto generate from mmu/mmu_user_image_detection.proto'''
proto_library (
  name = 'mmu_mmu_user_image_detection__proto',
  srcs = [
    'mmu/mmu_user_image_detection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_user_info.proto'''
proto_library (
  name = 'mmu_mmu_user_info__proto',
  srcs = [
    'mmu/mmu_user_info.proto'
  ],
)

'''auto generate from mmu/mmu_user_portrait.proto'''
proto_library (
  name = 'mmu_mmu_user_portrait__proto',
  srcs = [
    'mmu/mmu_user_portrait.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_user_profile.proto'''
proto_library (
  name = 'mmu_mmu_user_profile__proto',
  srcs = [
    'mmu/mmu_user_profile.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_user_seach_query.proto'''
proto_library (
  name = 'mmu_mmu_user_seach_query__proto',
  srcs = [
    'mmu/mmu_user_seach_query.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_user_search_rule.proto'''
proto_library (
  name = 'mmu_mmu_user_search_rule__proto',
  srcs = [
    'mmu/mmu_user_search_rule.proto'
  ],
  deps = [
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_user_tag.proto'''
proto_library (
  name = 'mmu_mmu_user_tag__proto',
  srcs = [
    'mmu/mmu_user_tag.proto'
  ],
  deps = [
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_video_dedup.proto depend logo_detection.proto not ok'''

'''mmu/mmu_video_detection.proto depend blob_store.proto not ok'''

'''auto generate from mmu/mmu_video_dup.proto'''
proto_library (
  name = 'mmu_mmu_video_dup__proto',
  srcs = [
    'mmu/mmu_video_dup.proto'
  ],
)

'''auto generate from mmu/mmu_video_edge_detection.proto'''
proto_library (
  name = 'mmu_mmu_video_edge_detection__proto',
  srcs = [
    'mmu/mmu_video_edge_detection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_video_edge_detection_oversea.proto'''
proto_library (
  name = 'mmu_mmu_video_edge_detection_oversea__proto',
  srcs = [
    'mmu/mmu_video_edge_detection_oversea.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_video_edge_detection__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_video_ex_info.proto'''
proto_library (
  name = 'mmu_mmu_video_ex_info__proto',
  srcs = [
    'mmu/mmu_video_ex_info.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_video_frame.proto'''
proto_library (
  name = 'mmu_mmu_video_frame__proto',
  srcs = [
    'mmu/mmu_video_frame.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:feature_dist__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_video_gender_detection_oversea.proto'''
proto_library (
  name = 'mmu_mmu_video_gender_detection_oversea__proto',
  srcs = [
    'mmu/mmu_video_gender_detection_oversea.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/mmu_video_k_music.proto depend blob_store.proto not ok'''

'''mmu/mmu_video_logo_detection.proto depend logo_detection.proto not ok'''

'''auto generate from mmu/mmu_video_oversea_service.proto'''
proto_library (
  name = 'mmu_mmu_video_oversea_service__proto',
  srcs = [
    'mmu/mmu_video_oversea_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:ocr_validator__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_video_pure_detect.proto'''
proto_library (
  name = 'mmu_mmu_video_pure_detect__proto',
  srcs = [
    'mmu/mmu_video_pure_detect.proto'
  ],
)

'''auto generate from mmu/mmu_vision_similar.proto'''
proto_library (
  name = 'mmu_mmu_vision_similar__proto',
  srcs = [
    'mmu/mmu_vision_similar.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_vote_option_recognition.proto'''
proto_library (
  name = 'mmu_mmu_vote_option_recognition__proto',
  srcs = [
    'mmu/mmu_vote_option_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_vtuber.proto'''
proto_library (
  name = 'mmu_mmu_vtuber__proto',
  srcs = [
    'mmu/mmu_vtuber.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_web_common_log.proto'''
proto_library (
  name = 'mmu_mmu_web_common_log__proto',
  srcs = [
    'mmu/mmu_web_common_log.proto'
  ],
)

'''auto generate from mmu/mmu_web_data.proto'''
proto_library (
  name = 'mmu_mmu_web_data__proto',
  srcs = [
    'mmu/mmu_web_data.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_web_image_detection.proto'''
proto_library (
  name = 'mmu_mmu_web_image_detection__proto',
  srcs = [
    'mmu/mmu_web_image_detection.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_young_woman_detect.proto'''
proto_library (
  name = 'mmu_mmu_young_woman_detect__proto',
  srcs = [
    'mmu/mmu_young_woman_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/mmu_zt_music_reco.proto'''
proto_library (
  name = 'mmu_mmu_zt_music_reco__proto',
  srcs = [
    'mmu/mmu_zt_music_reco.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/model_serving.proto'''
proto_library (
  name = 'mmu_model_serving__proto',
  srcs = [
    'mmu/model_serving.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/model_strategy_service.proto'''
proto_library (
  name = 'mmu_model_strategy_service__proto',
  srcs = [
    'mmu/model_strategy_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/movie_cluster.proto'''
proto_library (
  name = 'mmu_movie_cluster__proto',
  srcs = [
    'mmu/movie_cluster.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/multi_media_search.proto'''
proto_library (
  name = 'mmu_multi_media_search__proto',
  srcs = [
    'mmu/multi_media_search.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/multi_media_search_meta.proto'''
proto_library (
  name = 'mmu_multi_media_search_meta__proto',
  srcs = [
    'mmu/multi_media_search_meta.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:mmu_flanker__proto',
    '//teams/ad/ad_proto/mmu/BUILD:model_serving__proto',
    '//teams/ad/ad_proto/mmu/BUILD:multi_media_search__proto',
    ':mmu_media_common__proto',
  ],
)

'''auto generate from mmu/music_alignment.proto'''
proto_library (
  name = 'mmu_music_alignment__proto',
  srcs = [
    'mmu/music_alignment.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/music_common.proto'''
proto_library (
  name = 'mmu_music_common__proto',
  srcs = [
    'mmu/music_common.proto'
  ],
)

'''mmu/music_composition.proto depend blob_store.proto not ok'''

'''auto generate from mmu/music_doc.proto'''
proto_library (
  name = 'mmu_music_doc__proto',
  srcs = [
    'mmu/music_doc.proto'
  ],
)

'''auto generate from mmu/music_forward_index.proto'''
proto_library (
  name = 'mmu_music_forward_index__proto',
  srcs = [
    'mmu/music_forward_index.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:music_doc__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/music_mars.proto'''
proto_library (
  name = 'mmu_music_mars__proto',
  srcs = [
    'mmu/music_mars.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:music_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/music_peaq.proto'''
proto_library (
  name = 'mmu_music_peaq__proto',
  srcs = [
    'mmu/music_peaq.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/music_query.proto'''
proto_library (
  name = 'mmu_music_query__proto',
  srcs = [
    'mmu/music_query.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:audio_dup_query__proto',
  ],
)

'''auto generate from mmu/music_search.proto'''
proto_library (
  name = 'mmu_music_search__proto',
  srcs = [
    'mmu/music_search.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/music_search_server_log.proto'''
proto_library (
  name = 'mmu_music_search_server_log__proto',
  srcs = [
    'mmu/music_search_server_log.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':kuaishou_music_music_client_log__proto',
  ],
)

'''auto generate from mmu/music_venus.proto'''
proto_library (
  name = 'mmu_music_venus__proto',
  srcs = [
    'mmu/music_venus.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:model_serving__proto',
    '//teams/ad/ad_proto/mmu/BUILD:music_mars__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/new_app_porn_detection.proto'''
proto_library (
  name = 'mmu_new_app_porn_detection__proto',
  srcs = [
    'mmu/new_app_porn_detection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/obj_detection.proto'''
proto_library (
  name = 'mmu_obj_detection__proto',
  srcs = [
    'mmu/obj_detection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/object_detection.proto'''
proto_library (
  name = 'mmu_object_detection__proto',
  srcs = [
    'mmu/object_detection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/ocr_validator.proto'''
proto_library (
  name = 'mmu_ocr_validator__proto',
  srcs = [
    'mmu/ocr_validator.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:obj_detection__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/origin_dedup.proto'''
proto_library (
  name = 'mmu_origin_dedup__proto',
  srcs = [
    'mmu/origin_dedup.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/original_photo.proto'''
proto_library (
  name = 'mmu_original_photo__proto',
  srcs = [
    'mmu/original_photo.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/oversea_dedup.proto'''
proto_library (
  name = 'mmu_oversea_dedup__proto',
  srcs = [
    'mmu/oversea_dedup.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/oversea_doc_info.proto'''
proto_library (
  name = 'mmu_oversea_doc_info__proto',
  srcs = [
    'mmu/oversea_doc_info.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:bucket__proto',
  ],
)

'''auto generate from mmu/oversea_music.proto'''
proto_library (
  name = 'mmu_oversea_music__proto',
  srcs = [
    'mmu/oversea_music.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:audio_dup_query__proto',
  ],
)

'''auto generate from mmu/oversea_ocr_ext.proto'''
proto_library (
  name = 'mmu_oversea_ocr_ext__proto',
  srcs = [
    'mmu/oversea_ocr_ext.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:ocr_validator__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/oversea_params.proto'''
proto_library (
  name = 'mmu_oversea_params__proto',
  srcs = [
    'mmu/oversea_params.proto'
  ],
)

'''auto generate from mmu/oversea_reco_feature.proto'''
proto_library (
  name = 'mmu_oversea_reco_feature__proto',
  srcs = [
    'mmu/oversea_reco_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    ':mmu_common_message__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/parser_tool.proto'''
proto_library (
  name = 'mmu_parser_tool__proto',
  srcs = [
    'mmu/parser_tool.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/photo_cat_info.proto'''
proto_library (
  name = 'mmu_photo_cat_info__proto',
  srcs = [
    'mmu/photo_cat_info.proto'
  ],
)

'''auto generate from mmu/photo_comment_robot.proto'''
proto_library (
  name = 'mmu_photo_comment_robot__proto',
  srcs = [
    'mmu/photo_comment_robot.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/photo_count.proto'''
proto_library (
  name = 'mmu_photo_count__proto',
  srcs = [
    'mmu/photo_count.proto'
  ],
)

'''auto generate from mmu/photo_dedup.proto'''
proto_library (
  name = 'mmu_photo_dedup__proto',
  srcs = [
    'mmu/photo_dedup.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/photo_dup_pair.proto depend vision_retrieval.proto not ok'''

'''auto generate from mmu/photo_music.proto'''
proto_library (
  name = 'mmu_photo_music__proto',
  srcs = [
    'mmu/photo_music.proto'
  ],
)

'''auto generate from mmu/photo_profile.proto'''
proto_library (
  name = 'mmu_photo_profile__proto',
  srcs = [
    'mmu/photo_profile.proto'
  ],
)

'''auto generate from mmu/photo_search_tag.proto'''
proto_library (
  name = 'mmu_photo_search_tag__proto',
  srcs = [
    'mmu/photo_search_tag.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/photo_shop_item_check.proto'''
proto_library (
  name = 'mmu_photo_shop_item_check__proto',
  srcs = [
    'mmu/photo_shop_item_check.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/photo_tag_correlation.proto'''
proto_library (
  name = 'mmu_photo_tag_correlation__proto',
  srcs = [
    'mmu/photo_tag_correlation.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/poi_goods.proto'''
proto_library (
  name = 'mmu_poi_goods__proto',
  srcs = [
    'mmu/poi_goods.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split__proto',
  ],
)

'''auto generate from mmu/portal_search_params.proto'''
proto_library (
  name = 'mmu_portal_search_params__proto',
  srcs = [
    'mmu/portal_search_params.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split__proto',
    '//teams/ad/ad_proto/mmu/BUILD:combo_search_params__proto',
  ],
)

'''auto generate from mmu/qr_code.proto'''
proto_library (
  name = 'mmu_qr_code__proto',
  srcs = [
    'mmu/qr_code.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/qr_message.proto'''
proto_library (
  name = 'mmu_qr_message__proto',
  srcs = [
    'mmu/qr_message.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_search_tab__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_common_feature__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_rule__proto',
  ],
)

'''auto generate from mmu/query_analysis.proto'''
proto_library (
  name = 'mmu_query_analysis__proto',
  srcs = [
    'mmu/query_analysis.proto'
  ],
)

'''auto generate from mmu/real_time_sample.proto'''
proto_library (
  name = 'mmu_real_time_sample__proto',
  srcs = [
    'mmu/real_time_sample.proto'
  ],
)

'''auto generate from mmu/reco_common_retrieval.proto'''
proto_library (
  name = 'mmu_reco_common_retrieval__proto',
  srcs = [
    'mmu/reco_common_retrieval.proto'
  ],
)

'''auto generate from mmu/result_message.proto'''
proto_library (
  name = 'mmu_result_message__proto',
  srcs = [
    'mmu/result_message.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_pic_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_query_ec_result__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_vertical_source__proto',
    '//teams/ad/ad_proto/mmu/BUILD:audio_dup_query__proto',
    '//teams/ad/ad_proto/mmu/BUILD:dl_feature__proto',
    '//teams/ad/ad_proto/mmu/BUILD:indexing_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:ocr_validator__proto',
    '//teams/ad/ad_proto/mmu/BUILD:photo_search_tag__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_dataflow__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_query_analysis__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_query_rewriter__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_query_rewriter_split_search_type__proto',
  ],
)

'''auto generate from mmu/retake_validator.proto'''
proto_library (
  name = 'mmu_retake_validator__proto',
  srcs = [
    'mmu/retake_validator.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/salience_detection.proto'''
proto_library (
  name = 'mmu_salience_detection__proto',
  srcs = [
    'mmu/salience_detection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/sample_attr.proto'''
proto_library (
  name = 'mmu_sample_attr__proto',
  srcs = [
    'mmu/sample_attr.proto'
  ],
)

'''auto generate from mmu/search_aladdin.proto'''
proto_library (
  name = 'mmu_search_aladdin__proto',
  srcs = [
    'mmu/search_aladdin.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_se_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:goods_info__proto',
  ],
)

'''mmu/search_aladdin_content.proto depend combo_search_service.proto not ok'''

'''auto generate from mmu/search_api.proto'''
proto_library (
  name = 'mmu_search_api__proto',
  srcs = [
    'mmu/search_api.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:search_console__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/search_award_center.proto'''
proto_library (
  name = 'mmu_search_award_center__proto',
  srcs = [
    'mmu/search_award_center.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/search_collection.proto'''
proto_library (
  name = 'mmu_search_collection__proto',
  srcs = [
    'mmu/search_collection.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/search_console.proto'''
proto_library (
  name = 'mmu_search_console__proto',
  srcs = [
    'mmu/search_console.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:search_platform_web__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/search_embedding_proxy.proto'''
proto_library (
  name = 'mmu_search_embedding_proxy__proto',
  srcs = [
    'mmu/search_embedding_proxy.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/search_goods_roc_index_status.proto'''
proto_library (
  name = 'mmu_search_goods_roc_index_status__proto',
  srcs = [
    'mmu/search_goods_roc_index_status.proto'
  ],
)

'''auto generate from mmu/search_highlight_keyword.proto'''
proto_library (
  name = 'mmu_search_highlight_keyword__proto',
  srcs = [
    'mmu/search_highlight_keyword.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/search_index.proto'''
proto_library (
  name = 'mmu_search_index__proto',
  srcs = [
    'mmu/search_index.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/search_joint_reco_log_parse.proto'''
proto_library (
  name = 'mmu_search_joint_reco_log_parse__proto',
  srcs = [
    'mmu/search_joint_reco_log_parse.proto'
  ],
)

'''auto generate from mmu/search_photo_profile.proto'''
proto_library (
  name = 'mmu_search_photo_profile__proto',
  srcs = [
    'mmu/search_photo_profile.proto'
  ],
)

'''auto generate from mmu/search_photo_query.proto'''
proto_library (
  name = 'mmu_search_photo_query__proto',
  srcs = [
    'mmu/search_photo_query.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/search_platform_web.proto'''
proto_library (
  name = 'mmu_search_platform_web__proto',
  srcs = [
    'mmu/search_platform_web.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/search_query_embedding.proto depend data_model.proto not ok'''

'''auto generate from mmu/search_query_review.proto'''
proto_library (
  name = 'mmu_search_query_review__proto',
  srcs = [
    'mmu/search_query_review.proto'
  ],
)

'''auto generate from mmu/search_query_similar_music.proto'''
proto_library (
  name = 'mmu_search_query_similar_music__proto',
  srcs = [
    'mmu/search_query_similar_music.proto'
  ],
)

'''auto generate from mmu/search_roc_doc.proto'''
proto_library (
  name = 'mmu_search_roc_doc__proto',
  srcs = [
    'mmu/search_roc_doc.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:field_type__proto',
  ],
)

'''auto generate from mmu/search_rule_predictor.proto'''
proto_library (
  name = 'mmu_search_rule_predictor__proto',
  srcs = [
    'mmu/search_rule_predictor.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:video_search_rule__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/search_semantic_retrieval_result.proto'''
proto_library (
  name = 'mmu_search_semantic_retrieval_result__proto',
  srcs = [
    'mmu/search_semantic_retrieval_result.proto'
  ],
)

'''auto generate from mmu/shiva_strategy.proto'''
proto_library (
  name = 'mmu_shiva_strategy__proto',
  srcs = [
    'mmu/shiva_strategy.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/sift_feature.proto'''
proto_library (
  name = 'mmu_sift_feature__proto',
  srcs = [
    'mmu/sift_feature.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/similar_music.proto'''
proto_library (
  name = 'mmu_similar_music__proto',
  srcs = [
    'mmu/similar_music.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/similar_video.proto'''
proto_library (
  name = 'mmu_similar_video__proto',
  srcs = [
    'mmu/similar_video.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/similar_video_result.proto'''
proto_library (
  name = 'mmu_similar_video_result__proto',
  srcs = [
    'mmu/similar_video_result.proto'
  ],
  deps = [
    ':mmu_obj_detection__proto',
  ],
)

'''auto generate from mmu/similarity_detection.proto'''
proto_library (
  name = 'mmu_similarity_detection__proto',
  srcs = [
    'mmu/similarity_detection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:live_room_goods_asr_cat_tag__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/smart_title_log.proto'''
proto_library (
  name = 'mmu_smart_title_log__proto',
  srcs = [
    'mmu/smart_title_log.proto'
  ],
)

'''auto generate from mmu/snack_result_message.proto'''
proto_library (
  name = 'mmu_snack_result_message__proto',
  srcs = [
    'mmu/snack_result_message.proto'
  ],
)

'''auto generate from mmu/snap_detection.proto'''
proto_library (
  name = 'mmu_snap_detection__proto',
  srcs = [
    'mmu/snap_detection.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/speaker_gender_recognition.proto'''
proto_library (
  name = 'mmu_speaker_gender_recognition__proto',
  srcs = [
    'mmu/speaker_gender_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/speaker_recognition.proto'''
proto_library (
  name = 'mmu_speaker_recognition__proto',
  srcs = [
    'mmu/speaker_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/speech_recognition.proto'''
proto_library (
  name = 'mmu_speech_recognition__proto',
  srcs = [
    'mmu/speech_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/speech_recognition_e2e.proto'''
proto_library (
  name = 'mmu_speech_recognition_e2e__proto',
  srcs = [
    'mmu/speech_recognition_e2e.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:vad_recognition__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/speech_recognition_keyword.proto'''
proto_library (
  name = 'mmu_speech_recognition_keyword__proto',
  srcs = [
    'mmu/speech_recognition_keyword.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/speech_recognition_query.proto'''
proto_library (
  name = 'mmu_speech_recognition_query__proto',
  srcs = [
    'mmu/speech_recognition_query.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/mmu/BUILD:live_stream_pull__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/speech_robot.proto'''
proto_library (
  name = 'mmu_speech_robot__proto',
  srcs = [
    'mmu/speech_robot.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:stream_speech_recognition__proto',
    '//teams/ad/ad_proto/mmu/BUILD:stream_text_to_speech__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/speech_vtts.proto depend blob_store.proto not ok'''

'''mmu/stream_sell_detect.proto depend blob_store.proto not ok'''

'''auto generate from mmu/stream_sound_hound.proto'''
proto_library (
  name = 'mmu_stream_sound_hound__proto',
  srcs = [
    'mmu/stream_sound_hound.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:stream_speech_recognition__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/stream_speech_recognition.proto'''
proto_library (
  name = 'mmu_stream_speech_recognition__proto',
  srcs = [
    'mmu/stream_speech_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/stream_text_to_speech.proto'''
proto_library (
  name = 'mmu_stream_text_to_speech__proto',
  srcs = [
    'mmu/stream_text_to_speech.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
)

'''auto generate from mmu/tag_annotator.proto'''
proto_library (
  name = 'mmu_tag_annotator__proto',
  srcs = [
    'mmu/tag_annotator.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/tag_reco_model.proto depend tag_reco.proto not ok'''

'''auto generate from mmu/tag_relevance.proto'''
proto_library (
  name = 'mmu_tag_relevance__proto',
  srcs = [
    'mmu/tag_relevance.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/tencent_audio_callback.proto'''
proto_library (
  name = 'mmu_tencent_audio_callback__proto',
  srcs = [
    'mmu/tencent_audio_callback.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/text_bert.proto'''
proto_library (
  name = 'mmu_text_bert__proto',
  srcs = [
    'mmu/text_bert.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/text_category.proto'''
proto_library (
  name = 'mmu_text_category__proto',
  srcs = [
    'mmu/text_category.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/text_classifier.proto'''
proto_library (
  name = 'mmu_text_classifier__proto',
  srcs = [
    'mmu/text_classifier.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/text_dependency_analysis.proto'''
proto_library (
  name = 'mmu_text_dependency_analysis__proto',
  srcs = [
    'mmu/text_dependency_analysis.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/text_detection.proto'''
proto_library (
  name = 'mmu_text_detection__proto',
  srcs = [
    'mmu/text_detection.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/text_feature.proto'''
proto_library (
  name = 'mmu_text_feature__proto',
  srcs = [
    'mmu/text_feature.proto'
  ],
  deps = [
    ':mmu_dl_feature__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/text_segment.proto'''
proto_library (
  name = 'mmu_text_segment__proto',
  srcs = [
    'mmu/text_segment.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/text_tag.proto'''
proto_library (
  name = 'mmu_text_tag__proto',
  srcs = [
    'mmu/text_tag.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/text_to_speech.proto depend live_text_to_speech.proto not ok'''

'''auto generate from mmu/text_validator.proto'''
proto_library (
  name = 'mmu_text_validator__proto',
  srcs = [
    'mmu/text_validator.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/topic_detection.proto'''
proto_library (
  name = 'mmu_topic_detection__proto',
  srcs = [
    'mmu/topic_detection.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/trending_query_for_debug.proto'''
proto_library (
  name = 'mmu_trending_query_for_debug__proto',
  srcs = [
    'mmu/trending_query_for_debug.proto'
  ],
)

'''auto generate from mmu/unorder_compare.proto'''
proto_library (
  name = 'mmu_unorder_compare__proto',
  srcs = [
    'mmu/unorder_compare.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/user_antispam_index.proto'''
proto_library (
  name = 'mmu_user_antispam_index__proto',
  srcs = [
    'mmu/user_antispam_index.proto'
  ],
)

'''auto generate from mmu/user_message.proto'''
proto_library (
  name = 'mmu_user_message__proto',
  srcs = [
    'mmu/user_message.proto'
  ],
)

'''auto generate from mmu/user_portrait.proto'''
proto_library (
  name = 'mmu_user_portrait__proto',
  srcs = [
    'mmu/user_portrait.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/user_profile.proto'''
proto_library (
  name = 'mmu_user_profile__proto',
  srcs = [
    'mmu/user_profile.proto'
  ],
)

'''auto generate from mmu/user_validator.proto'''
proto_library (
  name = 'mmu_user_validator__proto',
  srcs = [
    'mmu/user_validator.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/vad_recognition.proto'''
proto_library (
  name = 'mmu_vad_recognition__proto',
  srcs = [
    'mmu/vad_recognition.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_collection.proto'''
proto_library (
  name = 'mmu_video_collection__proto',
  srcs = [
    'mmu/video_collection.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_delete_history.proto'''
proto_library (
  name = 'mmu_video_delete_history__proto',
  srcs = [
    'mmu/video_delete_history.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_duanzi_detection.proto'''
proto_library (
  name = 'mmu_video_duanzi_detection__proto',
  srcs = [
    'mmu/video_duanzi_detection.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_feature_offline.proto'''
proto_library (
  name = 'mmu_video_feature_offline__proto',
  srcs = [
    'mmu/video_feature_offline.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_feature_query__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_new_classification__proto',
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_feature_query.proto'''
proto_library (
  name = 'mmu_video_feature_query__proto',
  srcs = [
    'mmu/video_feature_query.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:face_recognization__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_lda_classification_cache_service__proto',
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_fusion.proto'''
proto_library (
  name = 'mmu_video_fusion__proto',
  srcs = [
    'mmu/video_fusion.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_game_query.proto'''
proto_library (
  name = 'mmu_video_game_query__proto',
  srcs = [
    'mmu/video_game_query.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:live_stream_screenshots_result__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''mmu/video_key_frame.proto depend blob_store.proto not ok'''

'''auto generate from mmu/video_label_push.proto'''
proto_library (
  name = 'mmu_video_label_push__proto',
  srcs = [
    'mmu/video_label_push.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_lda_classification_cache_service.proto'''
proto_library (
  name = 'mmu_video_lda_classification_cache_service__proto',
  srcs = [
    'mmu/video_lda_classification_cache_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_new_classification.proto'''
proto_library (
  name = 'mmu_video_new_classification__proto',
  srcs = [
    'mmu/video_new_classification.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_lda_classification_cache_service__proto',
    ':mmu_dl_feature__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_ocr_result.proto'''
proto_library (
  name = 'mmu_video_ocr_result__proto',
  srcs = [
    'mmu/video_ocr_result.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_relation.proto'''
proto_library (
  name = 'mmu_video_relation__proto',
  srcs = [
    'mmu/video_relation.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_search.proto'''
proto_library (
  name = 'mmu_video_search__proto',
  srcs = [
    'mmu/video_search.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_layouts_style__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_query_ec_result__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_vertical_source__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_common_feature__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_search_message_clone__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_abtest_common_list__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_common_feature__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_group_chat_info__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_search_common_msg__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_sensitive__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:search_user_search_info__proto',
    '//teams/ad/ad_proto/mmu/BUILD:combo_search_params__proto',
    '//teams/ad/ad_proto/mmu/BUILD:edu_params__proto',
    '//teams/ad/ad_proto/mmu/BUILD:field_type__proto',
    '//teams/ad/ad_proto/mmu/BUILD:goods_info__proto',
    '//teams/ad/ad_proto/mmu/BUILD:hashtag_params__proto',
    '//teams/ad/ad_proto/mmu/BUILD:i18n_source__proto',
    '//teams/ad/ad_proto/mmu/BUILD:indexing_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:live_search__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_music__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_oversea_music__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_oversea_video__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_search_common_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:mmu_search_general_index_doc__proto',
    '//teams/ad/ad_proto/mmu/BUILD:ocr_validator__proto',
    '//teams/ad/ad_proto/mmu/BUILD:oversea_params__proto',
    '//teams/ad/ad_proto/mmu/BUILD:poi_goods__proto',
    '//teams/ad/ad_proto/mmu/BUILD:portal_search_params__proto',
    '//teams/ad/ad_proto/mmu/BUILD:qr_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_robot__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_debug__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_exp_param__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_feature__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_split_geo_params__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_search_dataflow.proto'''
proto_library (
  name = 'mmu_video_search_dataflow__proto',
  srcs = [
    'mmu/video_search_dataflow.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:indexing_message__proto',
  ],
)

'''auto generate from mmu/video_search_debug.proto'''
proto_library (
  name = 'mmu_video_search_debug__proto',
  srcs = [
    'mmu/video_search_debug.proto'
  ],
)

'''auto generate from mmu/video_search_exp_param.proto'''
proto_library (
  name = 'mmu_video_search_exp_param__proto',
  srcs = [
    'mmu/video_search_exp_param.proto'
  ],
)

'''auto generate from mmu/video_search_feature.proto'''
proto_library (
  name = 'mmu_video_search_feature__proto',
  srcs = [
    'mmu/video_search_feature.proto'
  ],
)

'''auto generate from mmu/video_search_hot_word.proto'''
proto_library (
  name = 'mmu_video_search_hot_word__proto',
  srcs = [
    'mmu/video_search_hot_word.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:video_search_rule__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_search_query_analysis.proto'''
proto_library (
  name = 'mmu_video_search_query_analysis__proto',
  srcs = [
    'mmu/video_search_query_analysis.proto'
  ],
)

'''auto generate from mmu/video_search_query_rewriter.proto'''
proto_library (
  name = 'mmu_video_search_query_rewriter__proto',
  srcs = [
    'mmu/video_search_query_rewriter.proto'
  ],
)

'''auto generate from mmu/video_search_query_rewriter_split_search_type.proto'''
proto_library (
  name = 'mmu_video_search_query_rewriter_split_search_type__proto',
  srcs = [
    'mmu/video_search_query_rewriter_split_search_type.proto'
  ],
)

'''auto generate from mmu/video_search_rule.proto'''
proto_library (
  name = 'mmu_video_search_rule__proto',
  srcs = [
    'mmu/video_search_rule.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ds_search_combo_search_common_split_search_tab__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:video_search_rule_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/video_search_rule_base.proto'''
proto_library (
  name = 'mmu_video_search_rule_base__proto',
  srcs = [
    'mmu/video_search_rule_base.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:search_se_common__proto',
  ],
)

'''auto generate from mmu/video_search_split_geo_params.proto'''
proto_library (
  name = 'mmu_video_search_split_geo_params__proto',
  srcs = [
    'mmu/video_search_split_geo_params.proto'
  ],
)

'''mmu/vision_retrieval.proto depend blob_store.proto not ok'''

'''auto generate from mmu/vision_search.proto'''
proto_library (
  name = 'mmu_vision_search__proto',
  srcs = [
    'mmu/vision_search.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:vision_search_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:vision_search_object__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/vision_search_common.proto'''
proto_library (
  name = 'mmu_vision_search_common__proto',
  srcs = [
    'mmu/vision_search_common.proto'
  ],
)

'''auto generate from mmu/vision_search_object.proto'''
proto_library (
  name = 'mmu_vision_search_object__proto',
  srcs = [
    'mmu/vision_search_object.proto'
  ],
)

'''auto generate from mmu/voice_conversion.proto'''
proto_library (
  name = 'mmu_voice_conversion__proto',
  srcs = [
    'mmu/voice_conversion.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/web_demo_view.proto'''
proto_library (
  name = 'mmu_web_demo_view__proto',
  srcs = [
    'mmu/web_demo_view.proto'
  ],
  deps = [
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/zt_audio_tag_detect.proto'''
proto_library (
  name = 'mmu_zt_audio_tag_detect__proto',
  srcs = [
    'mmu/zt_audio_tag_detect.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    ':mmu_media_common__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from mmu/zt_speech_recognition.proto'''
proto_library (
  name = 'mmu_zt_speech_recognition__proto',
  srcs = [
    'mmu/zt_speech_recognition.proto'
  ],
  deps = [
    '//ks/reco_proto/proto/BUILD:common_replace',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/mmu/BUILD:lyrics_recognition__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common__proto',
    '//teams/ad/ad_proto/mmu/BUILD:media_common_result_status__proto',
    '//teams/ad/ad_proto/mmu/BUILD:result_message__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition__proto',
    '//teams/ad/ad_proto/mmu/BUILD:speech_recognition_query__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from resource/flow_search_hot_board.proto'''
proto_library (
  name = 'resource_flow_search_hot_board__proto',
  srcs = [
    'resource/flow_search_hot_board.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:common_request_response__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from stock_rt_common.proto'''
proto_library (
  name = 'stock_rt_common__proto',
  srcs = [
    'stock_rt_common.proto'
  ],
)

'''auto generate from tax/cross_border_tax.proto'''
proto_library (
  name = 'tax_cross_border_tax__proto',
  srcs = [
    'tax/cross_border_tax.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from ug/start/reco/common_reco.proto'''
proto_library (
  name = 'ug_start_reco_common_reco__proto',
  srcs = [
    'ug/start/reco/common_reco.proto'
  ],
  deps = [
    '//ks/algo-engine-proto/BUILD:algo_engine_proto',
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:newsmodel_reco_base__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from umdc_target_service.proto'''
proto_library (
  name = 'umdc_target_service__proto',
  srcs = [
    'umdc_target_service.proto'
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from union_creative_material_cvr_service.proto'''
proto_library (
  name = 'union_creative_material_cvr_service__proto',
  srcs = [
    'union_creative_material_cvr_service.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/kuaishou/BUILD:ad_common_ad_log__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

'''auto generate from video_projection.proto'''
proto_library (
  name = 'video_projection__proto',
  srcs = [
    'video_projection.proto'
  ],
  deps = [
    '//teams/ad/ad_proto/common/BUILD:ids__proto',
    '//teams/ad/ad_proto/kuaishou/BUILD:videostore_assets__proto',
  ],
  enable_kess = True,
  enable_grpc = True,
  enable_generic_rpc = True,
  use_grpc_ver = "v1100",
)

