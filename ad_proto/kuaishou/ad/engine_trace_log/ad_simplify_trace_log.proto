/* 
Copy From: ad-new-pb-component/kuaishou-ad-new-common-proto/src/main/proto/kuaishou/ad/engine_trace_log/ad_simplify_trace_log.proto
Sync Time: 2025-09-02 15:45:24
*/
syntax = "proto3";

package kuaishou.ad;
import "teams/ad/ad_proto/kuaishou/ad/ad_log.proto";
import "teams/ad/ad_proto/kuaishou/ad/common/enums.proto";
import "teams/ad/ad_proto/kuaishou/ad/common_ad_log.proto";
import "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_trace_common.proto";
import "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.proto";
import "teams/ad/ad_proto/kuaishou/ad/ad_base.proto";

option cc_enable_arenas = true;

message AdTraceFormulaInfo {
  string key = 1; // 因子 exp : i_hc_1;
  string belonged = 2; // 因子所属公式 exp : i_hc;
}

message AdRequestBaseInfo {
  int64 user_id = 1;
  int64 llsid = 2;
  int64 page_id = 3;
  int64 sub_page_id = 4;
  int64 pos_id = 5;
  string app_id = 6;
  uint64 timestamp = 7;  // ms
  kuaishou.ad.AdEnum.AdRequestFlowType ad_request_flow_type = 8;
  AdRequestExtend ad_request_extend = 9;
  AdTraceHostInfo host_info = 10;
  ServiceDeployName service_deploy_name = 11;
  PerfInfo perf_info = 12;
  string device_id = 13;
  int32 trace_log_sampling_flag = 14;
  bool is_only_write_request_data = 15;  // true 只采样请求数据, false 所有数据都采样
  string platform = 16;                  // 手机系统
  int64 interactive_form = 17;           // 交互形态
  int64 explore_strategy = 18;
  int32 reco_client_browse_type = 19;  // 交互版本设置
  int32 medium_attribute = 20;  // 联盟特有的媒体属性  0:kuaishou, 2:内部产品矩阵媒体, 4:外部内体
  int64 medium_uid = 21;  // 开发者 id
  int32 union_ad_style = 22;  // // 联盟广告样式：1:信息流, 2:激励视频, 3:全屏视频, 4:开屏, 5:banner
  int32 age = 23;             // 年龄
  int32 gender = 24;  // 性别, 1 女 2 男
  repeated InstalledAppInfo install_app_info = 25;  // 用户已安装应用列表, 【仅白名单用户填写】
  int64 ad_code = 26;  // 用户实时位置
  string user_real_time_location = 27;  // 用户实时位置-明文, 【仅白名单用户填写】
  repeated AdTraceFormulaInfo ad_formula_info = 28; // 公式-因子关联关系
  int32 ac_fetcher_type = 29;                           // 体验广告类型, 0 为正常流量，1 为体验流量
  repeated AdConvertInfo ad_convert_info = 30; // 历史转化广告 front 读 redis 填充, 【仅白名单用户填写】
  bool is_one_model_nearline_flow = 31;  // 是否 one_model 近线链路
  bool is_target_request = 32;  // 区分是否为 target 流量，兼容 trace 拆分模块
}
message AdTraceHostInfo {
  string service_stage = 1;   // 当前服务的不同的部署环境，如 post_online, prod 等
  string service_region = 2;  // 用于区分南北机房
  string host_name = 3;       // 机器名
  string service_az = 4;      // 同于区分 zw、yz，与 service_stage 联合使用区分 autopub
}

message TargetBreak {
  int64 id = 1;    // unit_id
  string msg = 2;  // 定向突破相信信息
}

message ModelMeta {
  int64 cmd_key_id = 1;
  int64 cmd_id = 2;
}
message TSMModelMeta {
  string cmd_key = 1;
  string cmd = 2;
}
message InnerMultiQueueInfo {
  double prev_commercial_min_win_score = 1;
  double whole_score = 2;
  int64 round1_win_vector_size = 3;
  double round1_multi_queue_roi1 = 4;
  double extra_support_roi1_value = 5;
  int64 common_ids_size = 6;
  int64 final_sort_top_num = 7;
  repeated double round2_roi_list = 8;
  double hc_need_support = 9;
  int64 replace_lower_bound_num = 10;
  bool set_hc_support = 11;
  double prev_inner_ad_p10_score = 12;
  double prev_inner_ad_p25_score = 13;
  double prev_inner_ad_p50_score = 14;
  bool is_inner_final_top_ad = 15;
}

message AdRequestExtend {
  int32 seize_seat = 1;                     // 占位一下
  string search_query = 2;                  // 搜索词
  repeated TargetBreak target_break = 3;    // 定向突破记录 目前只在 front
  repeated string search_query_tokens = 4;  // 搜索分词结果
  // flink 用这个标判断是否将这条日志落 v2 表,
  // 当 is_only_write_request_data=false, 该字段一定为 true,
  // is_only_write_request_data=true, 才可能为 false
  bool is_flink_task_drop = 5;
  bool is_search_delivery_from_combo_search = 6;  // 搜索下发回传
  repeated int64 search_app_card_accounts = 7;  // 搜索下载类强样式 account
  repeated int64 search_form_submit_card_accounts = 8;  // 搜索表单提交强样式 account
  int32 page_num = 9;                       // 分页，目前搜索使用
  string session_id = 10;  // 搜索 session_id
  repeated int64 search_bigv_authors = 11;  // 搜索直播大卡列表
  string universe_elastic_config_group_idx = 12;  // 联盟弹性队列分组
  bool is_common_card_second_request = 13;  // 聚合大卡二次请求
  AdEnum.FeedCardIndustry feed_card_ind = 14;  // 聚合大卡二次请求的类型
  int32 is_hit_cache_info = 15;   // 粗排缓存
}

// 服务的性能指标数据
message PerfInfo {
  message NodeTimeCost {
    int32 node_type = 1;  // 对应的 AdFrontNodeType/AdRankNodeType/AdTargetNodeType/AdServerNodeType 的枚举值
    int64 time_cost_ms = 2;  // 当前 node 的耗时
    int64 time_enter_ms = 3;  // 进入 node 的耗时
  }
  message DownStreamServiceStatus {
    ServiceDeployName server_name = 1;  // 下游服务名
    int64 time_cost_ms = 3;             // 下游服务耗时
    int32 status = 4;  // -1:没有调用, 其他使用 grpc 服务状态码(0:调用成功, 4:超时)
  }
  // 当前服务整体耗时
  int64 server_time_cost_ms = 1;
  // 分node的耗时
  repeated NodeTimeCost node_cost = 2;
  // 下游服务调用耗时
  repeated DownStreamServiceStatus down_stream_service_status = 3;
}

// 服务部署类型，主要用于相同流量会发给多个服务的情况使用
enum ServiceDeployName {
  DEFAULT_DEPLOY = 0;         // unknow
  AD_FRONT_DEFAULT = 1;       // frontServer
  AD_MIXRANK_DEFAULT = 2;     // mixRank
  AD_RANK_DEFAULT = 10;       // rankServer
  AD_RANK_FANSTOP = 11;       // rankServer_fanstop
  AD_TARGET_DEFAULT = 20;     // targetServer
  AD_TARGET_FANSTOP = 21;     // targetServer_fanstop
  AD_TARGET_AMD = 22;         // targetServer_amd
  AD_TARGET_LIVE = 23;        // targetServer_live
  AD_TARGET_INNER_PHOTO = 24; // targetServer_inner_photo
  AD_SERVER_DEFAULT = 30;     // adServer
  AD_SERVER_FANSTOP = 31;     // adServer_fanstop
  AD_SERVER_BRAND = 32;       // adServer_brand
  AD_SERVER_SOCIAL = 33;      // adServer_social
  AD_FANSTOP_SERVER = 34;     // fanstopServer
  AD_FORWARD_DEFAULT = 35;    // forward
  AD_FORWARD_FANSTOP = 36;    // forward_fanstop
  AD_FORWARD_DPA = 37;        // forward_dpa
  AD_PACK = 38;               // ad_pack
  PREDICT_SERVER = 40;        // predict_server
  AD_TARGET_SEARCH = 41;      // ad target search
  BIDWORD_SEARCH = 42;        // bidword search
  AD_FRONT_ARCHIMEDES = 43;   // frontServer_archimedes
  AD_RANK_ARCHIMEDES = 44;    // rankServer_archimedes
  AD_SERVER_ARCHIMEDES = 45;  // adServer_archimedes
  AD_TARGET_ARCHIMEDES = 46;  // targetServer_archimedes
  PREDICT_SERVER_OUTER = 47;
  PREDICT_SERVER_INNER_NORMAL = 48;
  PREDICT_SERVER_INNER_NATIVE = 49;
  AD_RANK_SPLASH = 50;
  AD_RANK_SEARCH = 51;
  AD_RANK_UNIVERSE = 52;
  AD_FRONT_SEARCH = 53;
  AD_FRONT_SPLASH = 54;
  AD_FRONT_UNIVERSE = 55;
  AD_SERVER_SPLASH = 56;
  AD_SERVER_SEARCH = 57;
  AD_SERVER_UNIVERSE = 58;
  AD_SERVER_UNIVERSE_TINY = 59;
  AD_TARGET_UNIVERSE = 60;
  AD_TARGET_UNIVERSE_TINY = 61;
  AD_RANK_UNIVERSE_TINY = 62;
  AD_MIXRANK_LIVE_INNER_STREAM = 63;
}

enum AdTraceDspType {
  /*
    广告DSP库类型
    下游对格式有依赖，添加新字段，请保持如下格式:

      枚举变量 = num;  // show_order;枚举变量说明

    分号为英文格式的分号
    如有问题请联系 baizongyao, liulong03
  */
  UNKNOW_AD_QUEUE_TYPE = 0;
  NORMAL_DSP_TYPE = 1;                                     // 100;普通硬广 DSP
  AMD_DSP_TYPE = 2;                                        // 废弃
  NORMAL_FANSTOP_DSP_TYPE = 3;                             // 200;外部粉条软广
  FLASH_PROMOTION_DSP_TYPE = 4 [ deprecated = true ];      // 300;速推(将废弃)
  SPECIALTY_PROMOTION_DSP_TYPE = 5 [ deprecated = true ];  // 400;专推(将废弃)
  INNER_FANSTOP_DSP_TYPE = 6;                              // 500;内部粉条
  ADX_DSP_TYPE = 7;                                        // 700;外部ADX
  DPA_DSP_TYPE = 8;                                        // 800;DPA
  BRAND_DSP_TYPE = 9;                                      // 600;品牌
  SOCIAL_DSP_TYPE = 10;                                    // 900;聚星
  FLASH_PROMOTION_HARD_DSP_TYPE = 11;                      // 1000;速推硬广
  SPECIALTY_PROMOTION_SOFT_DSP_TYPE = 12;                  // 1100;专推软广
  FLASH_PROMOTION_SOFT_DSP_TYPE = 13;                      // 1200;速推软广
  SPECIALTY_PROMOTION_HARD_DSP_TYPE = 14;                  // 1300;专推硬广
  SEARCH_ANTOU_DSP_TYPE = 15;                              // 1400;搜索暗投
  SEARCH_MINGTOU_DSP_TYPE = 16;                            // 1500;搜索明投
  MINGTOU_DSP_TYPE = 17;                                   // 1700;明投
  ESP_MOBILE_HARD_DSP_TYPE = 18;                           // 1800;磁力金牛移动版硬广
  ESP_MOBILE_SOFT_DSP_TYPE = 19;                           // 1900;磁力金牛移动版软广
  SOFT_AD_DSP_TYPE = 20;                                   // 2000;DSP软广
  NATIVE_AD_DSP_TYPE = 21;                                 // 2100;DSP原生广告
  INNER_FANSTOP_V2_DSP_TYPE = 22;                          // 500;新内粉
  B_CLIENT_FANSTOP = 23;                                   // 2200;B端粉条
  HARD_FANSTOP_DSP_TYPE = 24;                              // 2300;外部粉条硬广
}

// 广告基础数据
message AdTraceBaseInfo {
  uint64 account_id = 1;
  uint64 campaign_id = 2;
  uint64 unit_id = 3;
  uint64 creative_id = 4;
  uint64 photo_id = 5;
  uint64 live_stream_id = 6;
  kuaishou.ad.AdEnum.ItemType item_type = 7;  // 物料类型，用于区分短视频还是直播
  AdTraceDspType ad_dsp_type = 8;
  AdEnum.AdQueueType ad_queue_type = 9;                 // 软广或硬广
  uint64 agent_id = 10;                                 // 代理商 id
  int64 second_industry_id = 100;                        // 二级行业 id
  int64 first_industry_id = 101;                        // 一级行业 id
  int64 multi_retrieval_tag = 102;                      // 多路召回 tag
  int64 new_creative_tag = 103;                         // 大于0表示新创意
  int64 creative_label = 104;                           // 创意优选标记
  kuaishou.ad.AdEnum.CampaignType campaign_type = 105;  // 计划类型
  uint64 target_id = 106;                               // 定向id
  int64 cover_id = 107;                                 // 封面 id
  bool is_pt_union = 108;                               // 用于区分 adx 明投/暗投
  kuaishou.ad.AdEnum.AdDspAccountType account_type = 109;
  kuaishou.ad.AdEnum.CampaignPromotionType campaign_promotion_type = 110;
  kuaishou.ad.AdSourceType ad_source_type = 111;
  kuaishou.ad.AdxSourceType adx_source_type = 112;
  kuaishou.ad.AdEnum.TargetServerShardType target_shard_type = 113 [ deprecated = true ];  // 废弃
  string merchant_product_id = 114;
  string product_name = 115;                             // 产品名
  uint64 category_level_1_id = 116;                      // 商品类目 一级
  uint64 category_level_2_id = 117;                      // 商品类目 二级
  uint64 category_level_3_id = 118;                      // 商品类目 三级
  uint64 industry_level_1_id = 119;                      // 行业 ID  一级, v3
  uint64 industry_level_2_id = 120;                      // 行业 ID  二级, v3
  uint64 industry_level_3_id = 121;                      // 行业 ID  三级, v3
  uint64 author_id = 122;                                // 广告主快手 ID
  kuaishou.ad.AdEnum.AdForceRecoTag ad_force_reco_tag = 123;  // 广告强出标记
  int64 kol_user_type = 124;                             // DSP平台-广告主侧主动开启原生广告,包含达人授权原生 & 服务号蓝v原生
  bool is_outer_loop_native = 125;                       // 是否外循环原生广告
  int32 native_strict_status = 126;                      // 窄口径原生标识
  repeated int32 crowd_tag = 127;                        // 广告人群标识
  bool is_c_creative = 128;  // c 类创意标识
  uint32 placement_type = 129; // 标记联盟直投1、联盟优选2、暗投3
}

// 触发基础信息
message TriggerTraceBaseInfo {
  uint64 photo_id = 1;
  uint64 live_stream_id = 2;
  uint64 user_id = 3;
  uint64 sku_id = 4;
  uint64 dmp_id = 5;
  uint64 spu_id = 6;
  uint64 series_id = 7;
  repeated uint64 sku_id_vec = 8;
  int32 recall_strategy_type = 9;
  int32 sub_recall_strategy_type = 10;
  int32 match_type = 11;
  int32 sub_match_type = 12;
  int32 extend_type = 13;
  int32 sub_extend_type = 14;
  int32 trigger_item_type = 15;
  float rank_score = 16;
  float relevance_score = 17;
  float ensemble_score = 18;
  float qr_score = 19;
  float retrieval_weight = 20;
  float sctr = 21;
  float counter_ecpm = 22;
  bool is_qr = 23;
  bool is_fanstop_recall = 24;
  bool after_merge = 25;
  uint64 creative_id = 26;
  int32 relevance_type = 27;
  uint64 dpa_id = 28;
  uint64 book_id = 29;
}

message AdTraceBidInfo {
  int64 auto_cpa_bid = 1;
  int64 cpa_bid = 2;
  int64 auto_deep_cpa_bid = 3;
  int64 deep_cpa_bid = 4;
  kuaishou.ad.AdEnum.BidType bid_type = 5;
  kuaishou.ad.AdActionType ocpc_action_type = 6;
  AdEnum.OcpcDeepBidType ocpc_deep_bid_type = 7;
  kuaishou.ad.AdCallbackLog.EventType deep_conversion_type = 8;
  int64 ocpc_bid_modify_tag = 9;
  int64 auction_bid = 10;
  int64 bid = 11;
  double roi_ratio = 12;
  double auto_roas = 13;
  double ecpc_adjust_ratio = 14;  // 废弃
  int64 ecpc_adjust_tag = 15;     // 废弃
  kuaishou.ad.AdEnum.SpeedType speed_type = 16;
}
message AdTraceBenifitInfo {
  int64 cpm = 1;
  int64 price = 3;
  int64 rank_benifit = 4;
  int64 origin_cpm = 5;
  int64 ueq = 6;
  int64 leverage_score = 7;
  double hc_score = 8;
  double hc_gpm = 9;
  double hc_experience = 10;
  double explore_cpm = 11;
  double ecpc_ratio = 12;
  double customer_hc = 13;
  double unify_cpm = 14;
  double unify_bonus = 15;
  int64 pos_in_rank = 16;
}

message AdTraceFactorInfo {
  string phase_name = 1; // 阶段信息：processer 名 or module 名
  string key = 2; // 因子名 exp key = rank_benifit
  bool  admit = 3; // 准入条件
  double value = 4; // 取值；
}

message AdTraceUeqInfo { int64 ueq = 1; }
enum CmdMissType {
  SUCCESS = 0;
  ITEM_MISS = 1;
  REQUEST_TIMEOUT = 2;
  RESULT_NAN = 3;
}
message CxrInfo {
  int64 value = 1;
  AdActionType start_type = 2;
  AdActionType end_type = 3;
  int32 unify_tag = 4;
  int32 cmd_id = 5;            // cmd_id
  CmdMissType miss_type = 6;   // cmd miss 类型
  repeated int32 cmd_ids = 7;  // cmd_ids
  int64 origin_value = 8;      // 原始值
}
message AdTraceCxrInfo {
  int64 ctr = 1;                         // 废弃
  int64 cvr = 2;                         // 废弃
  int64 deep_cvr = 3;                    // 废弃
  AdActionType ctr_start_type = 4;       // 废弃
  AdActionType ctr_end_type = 5;         // 废弃
  AdActionType cvr_start_type = 6;       // 废弃
  AdActionType cvr_end_type = 7;         // 废弃
  AdActionType deep_cvr_start_type = 8;  // 废弃
  AdActionType deep_cvr_end_type = 9;    // 废弃
  int32 ctr_unify_tag = 10;              // 废弃
  int32 cvr_unify_tag = 11;              // 废弃
  int32 deep_cvr_unify_tag = 12;         // 废弃
  CxrInfo ctr_info = 13;
  CxrInfo cvr_info = 14;
  CxrInfo deep_cvr_info = 15;
  CxrInfo sctr_info = 16;
  CxrInfo ltv_info = 17;
  double event_7day_pay_times = 18;
}
message AdTraceBonusInfo {
  int64 bonus_cpm = 1;
  int64 bonus_cpm_tag = 2;
}

message PredictTypeUseStat {
  int32 predict_type = 1;
  int32 cmd_id = 2;
  int32 use_counter = 3;
  double score = 4;
  bool is_bounded = 5;
  int32 cmd_key_id = 6;
  int32 r_type = 7;
}

// 精排信息
message RankStageInfo {
  // 过滤信息
  message AdTraceFilterInfo {
    string node_stage_name = 1;              // 废弃
    string point_stage_name = 2;             // 废弃
    string plugin_stage_name = 3;            // 废弃
    int64 filter_condition = 4;              // 废弃
    AdRankNodeType node_stage_type = 5;      // 第一层，被过滤时所处的 Node,对应的 int 类型
    AdRankPointType point_stage_type = 6;    // 第二层，被过滤时所处的 Point,对应的 int 类型
    AdRankPluginType plugin_stage_type = 7;  // 第三层，被过滤时所处的 Plugin,对应的 int 类型
    kuaishou.log.ad.AdTraceFilterCondition filter_reason = 8;  // 过滤原因
  }

  // 各自服务自定义的字段，需要通过kconf配置管理起来
  message AdExtendInfo {
    enum SearchTargeType {
      DEFAULT_TARGET = 0;  // 暗投
      BIDWORD_TARGET = 1;  // 明投专业版
      QUICK_SEARCH = 2;    // 快投放
    }
    enum BidwordRetrievalType {
      KEYWORD = 0;      // 关键词召回
      AUTO_TARGET = 1;  // 智能扩量
      TARGET_SEARCH = 2;  // 暗投触发
    }

    message HcRecordMeta {
      int32 hc_type = 1;
      double hc_score = 2;
    }

    enum AggrStatusType {
      AGGR_UNKNOW = 0;  // 非聚合广告
      AGGR_A_SUC = 1;  // A 聚合成功
      AGGR_BCD_SUC = 2;  // BCD 聚合成功
      AGGR_RANK_LOWER = 3;  // 聚合排序靠后, 未胜出
      AGGR_GET_X_FAILED = 4;  // 获取 X 失败
    }

    int64 xxx = 1;
    repeated int32 cmd_id = 2;            // 当前广告请求了哪些cmd_id
    bool is_fan_follow = 3;               // 是否是粉丝
    uint64 multi_overlay_tag = 4;         // 多路召回召回源 重叠标识 [0-63]
    uint64 multi_overlay_tag_extend = 5;  // 多路召回召回源 [64-127]
    double delivery_rate = 6;             // 粗排下发率
    double pred_score = 7;                // 多路召回模型预估值
    double retrieval_post_score = 8;      // 召回后验分数
    double prerank_score = 9;
    double prerank_ecpm = 10;                                // 粗排阶段的 ecpm 值
    kuaishou.ad.RtaSourceType rta_source_type = 11;          // RTA source
    int64 ad_strategy_tag = 13;                              // 广告策略标识
    repeated PredictTypeUseStat predict_type_use_stat = 14;  // 预估值使用监控
    double ctr_factor = 15;                                  // 自动调参
    double cvr_factor = 16;                                  // 自动调参
    double cpm_zoom_factor = 17;                             // 自动调参
    double show_factor = 18;                                 // 自动调参
    double ocpx_action_type_weight = 19;      //  统一模型预估的浅度转化率预估值权重
    double deep_conversion_type_weight = 20;  //  统一模型预估的深度转化率预估值权重
    double original_unify_cvr = 21;           //  原始的浅度转化率
    double original_unify_deep_cvr = 22;      //  原始的深度转化率
    double unified_model_cvr = 23;            //  统一模型预估的浅度转化率
    double unified_model_deep_cvr = 24;       //  统一模型预估的深度转化率
    uint64 merchant_auxiliary_rank_key = 25;  // 电商精排助攻统计维度 hashkey
    double merchant_auxiliary_cvr = 26;       // 电商精排助攻统计维度预估值
    int64 live_start_gap = 27;                // 直播从开播到 trace 时间 gap
    double p3r = 28;                          // p3s ratio
    int64 cpm_thr = 29;                       // cpm 阈值
    int64 cpm_thr_from_tag = 30;              // 广告标签
    int64 photo_quality_score = 31;           // 视频质量分
    int64 ad_monitor_type = 32;               // 用于内外循环判断使用
    bool is_splash_gray = 33;                 // 是否 RTB 灰度投放
    double consult_ctr = 34;                  // 粉条直播的预估组件点击率
    double leads_submit_rate = 35;            // 粉条直播的预估线索提交率
    BidwordRetrievalType bidword_retrieval_type = 36;  // 明投召回方式
    SearchTargeType search_targe_type = 37;            // 搜索广告召回方式
    double prerank_ctcvr = 38;                // 内循环粗排预估ctcvr
    double rank_ctcvr = 39;                   // 内循环精排预估ctcvr
    int64 mmu_common_quality_score = 40;      // 粉条作品 mmu 通用质量分
    int32 search_app_card = 41;               // 搜索广告下载类强样式 1-入口打标 2-选中
    int64 prerank_append_tag = 42;            //粗排爬坡策略标记
    int64 prerank_append_group = 43;          //粗排爬坡组标记
    double rta_ratio = 44;                    // rta 动态出价 一次请求系数
    double rta_ratio_second_bid = 45;         // rta 动态出价 二次请求系数
    int32 search_form_card = 46;              // 搜索广告表单强样式 1-入口打标 2-选中
    repeated AdEnum.SearchCardStyleType search_card_type = 47;   // 在此环节，搜索打上了什么强样式的标
    double rta_bid = 48;                      // rta 动态出价 一次请求 直接出价
    double rta_bid_second_bid = 49;           // rta 动态出价 二次请求 直接出价
    int64 rta_sta_tag = 50;                   // rta 出价标记
    double p5r = 51;                          // p5s ratio
    double p10r = 52;                         // p10s ratio
    double vtr = 53;                          // vtr ratio
    repeated HcRecordMeta traffic_hc_record = 54;
    repeated HcRecordMeta industry_hc_record = 55;
    repeated HcRecordMeta other_hc_record = 56;
    repeated HcRecordMeta suppress_hc_record = 57;
    uint64 rta_trace_id = 58;                 // rta trace id
    double rta_bid_ratio = 59;                // rta 动态出价 一次请求系数 对齐字段数仓用
    double rta_bid_ratio_second_bid = 60;     // rta 动态出价 二次请求系数 对齐字段数仓用
    int32 universe_tiny_query_type = 61;      // 联盟搜索词匹配类型
    ModelMeta model_meta = 62;                // 模型元信息
    repeated ModelMeta model_meta_list = 64;
    double live_audience_score = 65;           // 直播模型原始预估值--直投进人
    double cvr_score = 66;           // 直播模型原始预估值--引流进人
    double order_paid_score = 67;           // 直播模型原始预估值--直播订单
    double live_pay_rate_score = 68;           // 直播模型原始预估值--直播订单
    double photo2live_pay_rate_score = 69;           // 直播模型原始预估值--引流进人
    double gmv_score_score = 70;           // 直播模型原始预估值--gmv
    double live_p3s_ltv_score = 71;      // 直播模型原始预估值--gmv
    double inner_live_roas_score = 72;     // 直播模型原始预估值--gmv
    repeated TSMModelMeta tsm_model_meta_list = 73 [deprecated = true];
    int64 prerank_retarget_tag = 74;
    double ntr = 75;
    double ad_ntr = 76;
    double reco_htr = 77;
    AggrStatusType aggr_status_type = 78; // 聚合竞价状态
    int64 game_retarget_status = 79;   // 游戏重定向状态
    bool is_game_retarget_ad = 80;    // 是否重定向
    bool is_paid_fiction_retarget = 81;  // na 小说重定向
    double other_hc_ntr_score = 82;  // ntr_hc
    double inner_storewide_live_uplift_prob1 = 83;  // 直播全站增量建模预估值 prob1
    double inner_storewide_live_uplift_prob2 = 84;  // 直播全站增量建模预估值 prob2
    int64 photo_md5 = 85;
    double inner_storewide_live_uplift_ratio = 86;  // 直播全站增量建模增效系数
    double inner_storewide_live_uplift_cali_cvr = 87;  // 直播全站增量建模校准 cvr
    double support_hc_score = 88;  // 流量扶持hc分数
    double suppress_hc_score = 89;  // 流量打压hc分数
    double industry_hc_score = 90;  // 行业hc分数
    double other_hc_score = 91;  // 其他hc分数
    double gsp_ratio = 92;  // 二价系数
    bool is_inner_cid_dup_photo_replace = 93;  // 内循环 cid 是否预估值替换
    bool is_inner_cid_cpm_replace = 94;  // 内循环 cid 是否同 pv cpm 替换
    double inner_cid_before_replace_cvr = 95;  // 内循环 cid 预估值替换前 cvr
    double inner_cid_after_replace_cvr = 96;  // 内循环 cid 预估值替换后 cvr
    double inner_cid_before_replace_gmv = 97;  // 内循环 cid 预估值替换前 gmv
    double inner_cid_after_replace_gmv = 98;  // 内循环 cid 预估值替换后 gmv
    AdEnum.SearchStrongCardType search_strong_card_type = 99;  // 搜索强样式匹配类型
    int32 cid_replace_type = 100;  // 内循环 cid 替换类型（0 不替换 1 验证实验 2 大流量rb 3 user_id 替换cvr 4. pv仅替换cvr）
    double cid_cpm_replace_before = 101;  // 内循环 cid 替换前 cpm
    double cid_hc_replace_before = 102;  // 内循环 cid 替换前 hc
    double cid_rank_benifit_before = 103;  // 内循环 cid 替换前 rank_benifit
    double cid_replace_ocpc_action_type = 104;  // 内循环 cid 替换前 ocpc_action_type
    int64 cid_replace_cpm_creative_id = 105;  // 内循环 cid 替换前 创意id
    double search_relevance = 106;  // 搜索相关性分数
    int32 is_hit_cache_info = 107;  // 粗排缓存
    double shelf_gpm = 108;  // 货架后验 gpm
    double shelf_pgpm = 109;  // 货架预估 gpm
  }
  // 广告item数据
  message AdTraceItemInfo {
    AdTraceBaseInfo ad_base_info = 1;  // 广告基础信息
    bool is_alive = 3;  // 是否返回给了粗排，如果被过滤了，才有 ad_filter_info 信息
    AdTraceFilterInfo ad_filter_info = 4;    // 广告被过滤的信息
    AdTraceBidInfo ad_bid_info = 5;          // 出价信息
    AdTraceBenifitInfo ad_benifit_info = 6;  // cpm/benifit相关信息
    AdTraceCxrInfo ad_cxr_info = 7;          // cxr相关信息
    AdTraceBonusInfo ad_bonus_info = 8;      // bonum相关信息
    AdTraceUeqInfo ad_ueq_info = 9;          // ueq相关信息
    AdExtendInfo ad_extend_info = 10;        // 当前服务需要的扩展字段
    repeated AdTraceFactorInfo ad_factor_info = 11;   // 因子信息
    kuaishou.ad.AdStrategyInfo ad_strategy_info = 12; // 联盟广告策略信息
  }

  message AdPredictMissInfo {
    int32 miss_reason = 1;
    int32 miss_count = 2;
  }

  message AdPredictStat {
    int32 predict_type = 1;
    int32 cmd_id = 2;
    int32 total_item_count = 3;  // 参与预估的总量
    int32 time_cost = 4;
    double avg_score = 5;
    repeated AdPredictMissInfo miss_info = 6;  // 预估 miss 的数量
    int64 cmd_key_id = 7;
    int32 predict_count = 8;  // 正常预估的数量
  }

  int64 ad_list_num = 1;                       // 整体进入精排的 ad_list 大小 (普通 dsp + amd)
  repeated AdTraceItemInfo ad_item_info = 2;   //
  repeated AdPredictStat ad_predict_stat = 3;  // 精排模型预估值监控
}

message PreRankStageInfo {}

message TriggerStageInfo {

  enum TriggerStageType {
    UNKNOWN = 0;             // 默认
    ORIGINAL_TRIGGER = 1;    // 原始触发
    TARGET_MERGE = 2;        // target 触发合并
    TARGET_RELE = 3;         // target 过相关性阶段
  }

  message TriggerTraceFilterInfo {
    AdTriggerNodeType node_stage_type = 1;
    kuaishou.log.ad.AdTraceFilterCondition filter_reason = 2;   // 过滤原因
  }

  message TriggerTraceItemInfo {
    TriggerTraceBaseInfo trigger_base_info = 1;      // 触发 item 的基础信息
    TriggerStageType trigger_stage_type = 2;         // 触发阶段标识
    bool is_alive = 3;
    TriggerTraceFilterInfo trigger_filter_info = 4;  // 触发过滤原因
  }

  message NodeCounter {
    AdTriggerNodeType node_stage_type = 1;
    int64 trigger_item_list_size = 2;
  }

  int64 trigger_list_num = 1;
  repeated TriggerTraceItemInfo trigger_item_info = 2;
  repeated NodeCounter node_counter = 3;             // 触发各阶段漏斗大小
}

message TargetStageInfo {
  // 召回 类型： 下发率效果，非下发率效果，生态，政策
  enum RetrType {
    UNKNOWN_RETR_TYPE = 0;
    NORMAL_EFFECT = 1;        // 普通效果层
    NON_DELIVERY_EFFECT = 2;  // 非下发率效果
    ECOLOGY = 3;              // 生态层
    POLICY = 4;               // 政策层
    CHOOSE_PHOTO = 5;         // 圈 photo 层
  }

  // 广告队列属于那个阶段的
  // 用于区分粗排下移后，区分出召回、出粗排队列
  enum AdStageType {
    UNKNOWN = 0;
    RECALL = 1;      // 进入召回
    PRERANKING = 2;  // 进入粗排
    RANKING = 3;     // 进入精排
    POST_RECALL = 4;     // 出召回
  }

  enum RecallStage {
    UNKNOWN_STAGE = 0;
    TRIGGER = 1;
    SEARCHER = 2;
    RANKER = 3;
    MERGER = 4;
    RULE_FILTER = 5;
    PRERANK = 6;
    POST_PROC = 7;
  }

  message RecallStat {
    RecallStage stage = 1;
    string cmd_key = 2;
    string cmd = 3;
    int64 quota = 4;
    int64 item_num = 5;
  }
  message TSMQuota {
    int32 trigger_quota = 1;
    int32 searcher_quota = 2;
    int32 merger_quota = 3;
  }
  message TsmPathStat {
    string biz_name = 1;
    TSMQuota biz_quota = 2;
    int32 layer_id = 3;
    string lay_exp = 4;
    TSMQuota layer_quota = 5;
    int32 path_id = 6;
    string owner = 7;
    string path_exp = 8;
    TSMQuota path_quota = 9;
    repeated string path_strategy = 10;
    int32 scene_id = 11;
    string type = 12;
    int32 trigger_num = 13;
    int32 searcher_num = 14;
    int32 merger_num = 15;
    string cmd = 16;
    string cmd_key = 17;
    bool implemented = 18;
  }

  message AdPredictMissInfo {
    int32 miss_reason = 1;
    int32 miss_count = 2;
  }

  message AdPredictStat {
    int32 predict_type = 1;
    int32 cmd_id = 2;
    int32 total_item_count = 3;  // 参与预估的总量
    int32 time_cost = 4;
    double avg_score = 5;
    repeated AdPredictMissInfo miss_info = 6;  // 预估 miss 的数量
    int64 cmd_key_id = 7;
    int32 predict_count = 8;  // 正常预估的数量
  }

  // ranking prepare
  message PreRankingInfo {
    // 主路策略
    message Strategy {
      message StrategyInfo {
        int64 from_tag = 1;  // 主路tag
        int32 pos = 2;
        float score = 3;
        PrerankTagType prerank_tag = 4;
      }

      // 扶持策略
      message SupportStrategyInfo {
        int32 from_group = 1;  // 来自那个爬坡层，政策、生态
        int32 from_tag = 2;
        int32 sort_method_tag = 3;
        int32 pos = 4;
        float score = 5;
        PrerankAppendGroupType group_type = 6;
      }

      // 要么是主路要么是扶持
      bool is_support = 1;
      StrategyInfo main_strategy = 2;
      SupportStrategyInfo support_strategy = 3;
      int32 pos_in_prerank = 4;
    }

    message AdTraceCxrInfo {
      kuaishou.ad.AdActionType ocpc_action_type = 1;
      double ecpm_ctr = 2;
      double ecpm_cvr = 3;
      double unify_ctr = 4;       // 统一 ctr
      double unify_cvr = 5;       // 统一 cvr
      int32 cmd_id = 6;           // cmd_id
      CmdMissType miss_type = 7;  // cmd miss 类型
      double prerank_cpm_ltr = 8;
      double prerank_ensemble_score = 9;
      double prerank_ctcvr_score = 10;
      double unify_deep_cvr = 11;                 // 统一 deep cvr
      double prerank_ecpm = 12;                   // 粗排 ecpm
      double prerank_delivery_rate = 13;          // 粗排 delivery rate
      double prerank_append_ensemble_score = 14;  // 粗排爬坡融合分
      double prerank_ctcvr = 15;                  // 粗排 ctcvr 预估值
      double neg_rate = 16;                       // 负反馈率
      double prerank_gpm = 17;                    // 粗排 gpm
      double prerank_real_action = 18;            // 粗排 real_action
      double trigger_relative_score = 19;         // 粗排 内流相关性分
      double prerank_mix_ltr_score = 20;          // 粗排 mix_ltr_score
    }

    AdTraceCxrInfo ad_cxr_info = 1;
    AdTraceBidInfo bid_info = 2;  // 粗排bid_info
    Strategy strategy_info = 3;
  }

  message AdTraceFilterInfo {
    AdTargetNodeType node_stage_type = 1;
    int64 filter_condition = 2;                                // 废弃
    kuaishou.log.ad.AdTraceFilterCondition filter_reason = 3;  // 过滤原因
  }

  // target 广告序相关
  message StrategyInfo {
    int32 pos_in_multi_tag = 1;
    int32 pos_in_ranking = 2;
    int32 strategy_tag = 3;  // ranking 策略tag 是否可以复用到爬坡那些标示
    float score = 4;         // 排序分
    kuaishou.ad.AdActionType ocpx_action_type = 5;
    RetrType retr_type = 6;
  }

  message RecallCounter {
    int64 tag_id = 1;
    int64 cmd_id = 2;                 //先暂时不填 cmd_id
    int32 recall_candidate_size = 3;  // 召回结果大小
    int32 intersect_result_size = 4;  // 召回结果求交大小
    int32 request_type = 6;           // 0:ann 1:ps
    int32 search_num = 7;             // search num
    int32 quota = 8;                  // quota
    int32 layer = 9;                  // level0 level1 level2 取后缀id
    int32 machine_num = 10;           // 占用机器数
  }

  // eg invert step 100w -> forward 7000 -> rulefilter 3000
  message NodeCounter {
    AdTargetNodeType node_stage_type = 1;
    int64 ad_list_size = 2;
  }

  // 各自服务自定义的字段，需要通过kconf配置管理起来
  message AdExtendInfo {
    enum SearchTargeType {
      DEFAULT_TARGET = 0;  // 暗投
      BIDWORD_TARGET = 1;  // 明投专业版
      QUICK_SEARCH = 2;    // 快投放
    }
    enum OneStepSortType {
      UNKOWN = 0;  // 默认
      ECPM = 1;  // ecpm 通道
      DELIVERY = 2;    // 下发率通道
    }
    enum BidwordRetrievalType {
      KEYWORD = 0;      // 关键词召回
      AUTO_TARGET = 1;  // 智能扩量
      TARGET_SEARCH = 2;  // 暗投触发
    }

    message RankRecord {
      int32  strategy_tag = 1;
      double strategy_value = 2;
    }

    enum FlowControlType {
      FLOW_CONTROL_TYPE_UNKNOWN = 0;
      FLOW_CONTROL_TYPE_DARK = 1;  // 暗投
      FLOW_CONTROL_TYPE_PARTITION = 2; // 预算隔离
      FLOW_CONTROL_TYPE_BTR = 3; // btr 流控
      FLOW_CONTROL_TYPE_LEFT_BUDGET = 4; // 剩余预算
      FLOW_CONTROL_TYPE_VIRTUAL = 5; // 虚拟金
    }

    enum FlowControlSkipType {
      FLOW_CONTROL_SKIP_TYPE_UNKNOWN = 0;
      FLOW_CONTROL_SKIP_TYPE_INSPIRE_MIX = 1;  // 激励混投跳过端视频
      FLOW_CONTROL_SKIP_TYPE_INSPIRE_FLOW_SMALL = 2; // 快小游跳过
      FLOW_CONTROL_SKIP_TYPE_NEBULA_TASK_ENTRANCE = 3; // 极速电商任务入口跳过流控
      FLOW_CONTROL_SKIP_TYPE_INSPIRE_GMV_CONTROL = 4; // 激励自动控比跳过
      FLOW_CONTROL_SKIP_TYPE_MINGTOU = 5; // 明投跳过
      FLOW_CONTROL_SKIP_TYPE_INSPIRE_MIX_LIVE = 6;  // 激励混投跳过直播
      FLOW_CONTROL_SKIP_TYPE_TARGET_SERVER_RATIO = 7;  // targetServer abTest暗投控比ratio跳过
      FLOW_CONTROL_SKIP_TYPE_INSPIRE_DJ_IAA = 8;  // iaa 短剧暗投激励跳过
      FLOW_CONTROL_SKIP_TYPE_INSPIRE_ROAS = 9;  // ROAS 类跳过
      FLOW_CONTROL_SKIP_TYPE_LIVE_RESERVATION_PAGE = 10;  // 直播大客预约页面跳过
      FLOW_CONTROL_SKIP_TYPE_INCENTIVE_LIVE_ONLY_TASK = 11;  // 激励直播任务跳过
      FLOW_CONTROL_SKIP_TYPE_INCENTIVE_SILENT_FANS_TASK = 12;  // 激励沉默粉丝唤醒任务跳过
    }

    message FlowControlSkipInfo {
      FlowControlType flow_control_type = 1;
      FlowControlSkipType flow_control_skip_type = 2;
    }

    int64 merchant_tag_recall_sid = 1;    // 电商标签召回的标签
    bool is_fan_follow = 2;               // 是否是粉丝
    uint64 multi_overlay_tag = 3;         // 多路召回召回源 重叠标识 [0-63]
    uint64 multi_overlay_tag_extend = 4;  // 多路召回召回源 [64-127]
    string cmd = 5;                       // cmd
    double delivery_rate = 6;             // 粗排下发率
    double pred_score = 7;                // 多路召回模型预估值
    double retrieval_post_score = 8;      // 召回后验分数
    double prerank_score = 9;
    double prerank_ecpm = 10;                          // 粗排阶段的 ecpm 值
    kuaishou.ad.RtaSourceType rta_source_type = 11;    // RTA source
    int64 ad_strategy_tag = 12;                        // 广告策略标识
    double retrieval_rank_score = 13;                  // 召回后的排序分
    int64 recall_cmd_id = 14;                          // 召回模型cmd_id
    uint64 merchant_auxiliary_rank_key = 15;           // 电商精排助攻统计维度 hashkey
    int64 live_start_gap = 16;                         // 直播从开播到 trace 时间 gap
    string search_trigger_word = 17;                   // 搜索明投触发词
    int64 search_trigger_type = 18;                    // 搜索明投触发类型
    int64 search_bid_type = 19;                        // 搜索明投购买类型
    double search_relevance = 20;                      // 搜索相关性
    float ecpm_ctr_score = 21;                         // ecpm模型 ctr打分值
    float ecpm_cvr_score = 22;                         // ecpm模型 cvr打分值
    string search_product_name = 23;                   // 搜索产品名
    int64 ad_monitor_type = 24;                        // 用于内外循环判断使用
    int64 resource_id = 25;                            // 广告投放位置
    bool is_inner_rewarded_mingtou = 26;               // 是否内循环激励明投
    BidwordRetrievalType bidword_retrieval_type = 27;  // 明投召回方式
    SearchTargeType search_targe_type = 28;            // 搜索广告召回方式
    bool is_photo_represent_creative = 29;             // photo 找一个代表的 creative
    double prerank_ctcvr = 30;                         // 粗排阶段 ctcvr 预估值
    double rta_bid_ratio = 31;                         // rta 动态出价系数
    int64 rta_sta_tag = 32;                            // rta 动态出价标记
    double bonus_rate = 33;                            // 粗排bonus用于计算ensemble score
    double delivery_pred_score = 34;                   // 一段式浅度目标e2e模型预估分
    double retrieval_ecpm = 35;                        // 一段式ecpm模型预估分
    int32 ecpm_idx = 36;                               // ensmeble sort ecpm idx
    int32 e2e_idx = 37;                                // ensmeble sort e2e idx
    int32 cpm_ltr_idx = 38;                            // ensmeble sort ctcvr idx
    OneStepSortType sort_type = 39;                    // live-target ecpm or delivery 透出 1 ecpm 2 delivery
    bool search_app_card = 40;                         // 搜索广告下载类强样式标记
    int64 prerank_append_tag = 41;                     // 粗排爬坡策略标记
    int64 prerank_append_group = 42;                   // 粗排爬坡组标记
    uint64 before_swap_creative_id = 43;               // 继承标记
    int32 swap_reason = 44;                            // 继承类型
    bool search_form_submit_card = 45;                 // 搜索广告表单提交强样式标记
    repeated AdEnum.SearchCardStyleType search_card_type = 46;   // 在此环节，搜索打上了什么强样式的标
    int32 search_relevance_type = 47;                  // 相关性打分类型
    int32 search_app_card_status = 48;                 // 搜索广告下载类强样式 正数为成功 负数为失败
    double rta_bid = 49;                               // rta 动态出价 直接出价 一次出价
    repeated RankRecord auto_cpa_bid_record = 50;      // 记录 auto cpa bid 的变更
    repeated RankRecord bonus_cpm_record = 51;         // 记录 bonus cpm 的变更
    repeated RankRecord auction_bid_record = 52;       // 记录 auction bid 的变更
    int32 search_form_submit_card_status = 53;         // 搜索广告表单类强样式 正数为成功 负数为失败
    int64 rta_trace_id = 54;                           // rta trace id
    int32 universe_tiny_query_type = 55;               // 联盟搜索词匹配类型
    float explore_score = 56;                          // 冷启动探索分
    ModelMeta model_meta = 57;                         // 模型元信息
    repeated ModelMeta model_meta_list = 58;
    repeated TSMModelMeta tsm_model_meta_list = 59;
    uint64 prerank_retarget_tag = 60;
    repeated FlowControlSkipInfo flow_control_skip_info = 61;
    int64 game_retarget_status = 62;    // 游戏重定向状态
    bool is_game_retarget_ad = 63;    // 是否重定向
    int32 gpm_idx = 64;       // ensmeble sort gpm idx
    int32 neg_idx = 65;       // ensmeble sort gpm idx
    int32 real_action_idx = 66;    // ensmeble sort real action idx
    bool is_paid_fiction_retarget = 67;  // na 小说重定向
    int32 trigger_relative_idx = 68;    // 内流 trigger relative idx
    uint64 multi_overlay_tag_extend1 = 69;  // 多路召回召回源 [-191]
    uint64 multi_overlay_tag_extend2 = 70;  // 多路召回召回源 [-255]
    uint64 multi_overlay_tag_extend3 = 71;  // 多路召回召回源 [-319]
    uint64 multi_overlay_tag_extend4 = 72;  // 多路召回召回源 [-383]
    uint64 multi_overlay_tag_extend5 = 73;  // 多路召回召回源 [-447]
    AdEnum.SearchStrongCardType search_strong_card_type = 74;  // 搜索强样式匹配类型
    double incentive_account_refund_rate = 75;
    double incentive_account_filter_rate = 76;
    int32 is_hit_cache_info = 77;   // 粗排缓存
  }

  message AdTraceItemInfo {
    AdTraceBaseInfo ad_base_info = 1;  // 广告基础信息
    AdTraceFilterInfo ad_filter_info = 2;
    bool is_alive = 3;
    StrategyInfo strategy_info = 4;      // 多路排序
    AdExtendInfo ad_extend_info = 5;     // 扩展字段
    PreRankingInfo preranking_info = 6;  // 粗排信息
    AdStageType ad_stage_type = 7;       // 区分进入召回、进入粗排、进入精排
  }

  int64 ad_list_num = 1;                      // 7000 那个值
  repeated AdTraceItemInfo ad_item_info = 2;  // size = rule_filter + ranking_truncate + output
  repeated NodeCounter node_counter = 3;
  repeated RecallCounter multi_counter = 4;
  repeated AdPredictStat ad_predict_stat = 5;  // 粗排模型预估值监控
  string exp_id = 6 [ deprecated = true ];     // 废弃
  repeated uint64 auto_param_exp_id = 7;       // 自动调参实验
  repeated RecallStat recall_stat = 8;         // 召回分阶段统计
  repeated TsmPathStat tsm_path_stat = 9;      // 召回通路信息
}

message AdServerStageInfo {
  message MainStrategyInfo {
    int64 tag = 1;                   // 主路tag(ecpm、delivery、ensemble)，后面废弃
    int32 index = 2;                 // 该路序
    float score = 3;                 // 该路的分
    PrerankTagType prerank_tag = 4;  // 主路tag(ecpm、delivery、ensemble)
  }

  message AppendStrategyInfo {
    int32 append_group = 1;  // 来自哪个爬坡层级政策、生态，后面废弃
    int32 append_tag = 2;    // 具体爬坡策略：起量扶持、首发游戏扶持、绿色通道等
    // 爬坡依赖的 tag/score/index
    int32 sort_tag = 3;
    int32 index = 4;
    float score = 5;
    PrerankAppendGroupType append_group_type = 6;  // 来自哪个爬坡层级政策、生态
  }

  message StrategyInfo {
    // 主路和爬坡互斥，只能命中一个
    MainStrategyInfo main_strategy_info = 1;
    AppendStrategyInfo append_strategy_info = 2;
    int32 pos_in_prerank = 3;  // 在粗排中的顺位
  }

  message AdServerTraceFilterInfo {
    AdServerNodeType node_stage_type = 1;
    int64 filter_condition = 2;                                // 废弃
    kuaishou.log.ad.AdTraceFilterCondition filter_reason = 3;  // 过滤原因
  }

  message AdTraceCxrInfo {
    kuaishou.ad.AdActionType ocpc_action_type = 1;
    double ecpm_ctr = 2;
    double ecpm_cvr = 3;
    double unify_ctr = 4;       // 统一 ctr
    double unify_cvr = 5;       // 统一 cvr
    int32 cmd_id = 6;           // cmd_id
    CmdMissType miss_type = 7;  // cmd miss 类型
  }

  // 各自服务自定义的字段，需要通过kconf配置管理起来
  message AdExtendInfo {
    repeated int32 cmd_id = 2;            // 当前广告请求了哪些cmd_id
    int64 merchant_tag_recall_sid = 3;    // 电商标签召回的标签
    bool is_fan_follow = 4;               // 是否是粉丝
    uint64 multi_overlay_tag = 5;         // 多路召回召回源 重叠标识 [0-63]
    uint64 multi_overlay_tag_extend = 6;  // 多路召回召回源 [64-127]
    int64 ad_monitor_type = 7;            // 用于内外循环判断使用
    ModelMeta model_meta = 8;             // 模型元信息
    repeated ModelMeta model_meta_list = 9;
  }

  message AdTraceItemInfo {
    AdTraceBaseInfo ad_trace_info = 1;  // 已包含 dsp、adx、amd分类
    AdTraceCxrInfo ad_cxr_info = 2;     // 粗排cxr_info
    AdTraceBidInfo bid_info = 3;        // 粗排bid_info
    StrategyInfo strategy_info = 4;     // 融合策略
    AdServerTraceFilterInfo filter_info = 5;
    AdExtendInfo ad_extend_info = 6;
    bool is_alive = 7;
  }

  int32 ad_list_num = 1;  // adx + dsp + amd
  repeated AdTraceItemInfo ad_item_info = 2;
}

// AdRank
message AdRankSimplifyTraceLog {
  AdRequestBaseInfo ad_request_base_info = 1;
  RankStageInfo rank_stage_info = 2;  // 精排
}

// adServer
message AdServerSimplifyTraceLog {
  AdRequestBaseInfo ad_request_base_info = 1;
  AdServerStageInfo ad_server_stage_info = 2;  // 粗排
}

// adTarget
message AdTargetSimplifyTraceLog {
  AdRequestBaseInfo ad_request_base_info = 1;
  TargetStageInfo target_stage_info = 2;  // 定向召回
  SearchRecallStageInfo recall_stage_info = 3;  // 搜索触发信息
}

// adTrigger
message AdTriggerSimplifyTraceLog {
  AdRequestBaseInfo ad_request_base_info = 1;
  TriggerStageInfo trigger_stage_info = 2;  // 搜索触发结果
}

enum SearchRecallMaterialType {
  UNKNOWN_SEARCH_RECALL_MATERIAL_TYPE = 0;
  SEARCH_RECALL_PHOTO_ID = 1;
  SEARCH_RECALL_LIVE_ID = 2;
  SEARCH_RECALL_AUTHOR_ID = 3;
  SEARCH_RECALL_ACCOUNT_ID = 4;
  SEARCH_RECALL_CREATIVE_ID = 5;
}
message RecallExtInfo {
  string trigger_query = 1;  // 触发 query
}
message SearchRecallFilterInfo {
  AdTargetNodeType node_stage_type = 1;
  kuaishou.log.ad.AdTraceFilterCondition filter_reason = 2;  // 过滤原因
}
message SearchRecallItem {
  int64 item_id = 1;   // photo_id live_id 等id
  SearchRecallMaterialType item_type = 2;
  int64 retrieval_tag = 3;
  int32 match_type = 4;
  double match_score = 5;
  SearchRecallFilterInfo filter_info = 6;
  RecallExtInfo ext_info = 7;
}
message SearchRecallStageInfo {
  repeated SearchRecallItem recall_item = 1;
}
// Recall
message SearchRecallTraceLog {
  AdRequestBaseInfo ad_request_base_info = 1;
  SearchRecallStageInfo recall_stage_info = 2;
}

// adFront

message RequestAccessInfo {
  message AccessItemInfo {
    int32 invalid_flag = 1;  // 准入类型
    int32 last_ad_pos = 2;   // 上次广告位置信息
  }
  AccessItemInfo dsp_access_info = 1;            // 硬广准入信息
  AccessItemInfo soft_access_info = 2;           // 软广准入信息
  AccessItemInfo inner_fanstop_access_info = 3;  // 内粉准入信息
  int32 page_size = 4;
}
message FrontAdTraceInfo {
  int64 merge_rank_score = 1;  // merge 时排序使用的得分
  int64 merge_rank_pos = 2;    // merge 队列中排序顺序
  int32 ad_pos = 3;            // 当前广告的位置信息
}
message FrontAdTraceItemInfo {
  // 过滤信息
  message AdFrontTraceFilterInfo {
    AdFrontNodeType node_stage_type = 1;      // 第一层，被过滤时所处的 Node,对应的 int 类型
    AdFrontPointType point_stage_type = 2;    // 第二层，被过滤时所处的 Point,对应的 int 类型
    AdFrontPluginType plugin_stage_type = 3;  // 第三层，被过滤时所处的 Plugin,对应的 int 类型
    int64 filter_condition = 4;               // 废弃
    kuaishou.log.ad.AdTraceFilterCondition filter_reason = 5;  // 过滤原因
  }
  message FrontAdExtendInfo {
    message AdjustPriceRecord {
      int32 price_tag = 1;  // 计费打折 tag
      int64 price = 2;  // 计费打折后 price
    }
    bool is_fan_follow = 1;               // 是否是粉丝
    int64 ad_monitor_type = 2;            // 用于内外循环判断使用
    uint64 multi_overlay_tag = 3;         // 多路召回召回源 重叠标识 [0-63]
    uint64 multi_overlay_tag_extend = 4;  // 多路召回召回源 [64-127]
    double delivery_rate = 6;             // 粗排下发率
    double pred_score = 7;                // 多路召回模型预估值
    double retrieval_post_score = 8;      // 召回后验分数
    double prerank_score = 9;
    double prerank_ecpm = 10;                        // 粗排阶段的 ecpm 值
    bool is_playable = 11;                           // 是否为试玩广告
    kuaishou.ad.RtaSourceType rta_source_type = 12;  // RTA source
    int64 ad_strategy_tag = 13;                      // 广告策略标识
    double retrieval_rank_score = 14;                // 召回后的排序分
    bool is_front_force = 15;                        // 天花板竞价强出实验标记
    bool inner_ceiling_force = 16;                        // 内循环天花板竞价强出实验标记
    repeated AdEnum.SearchCardStyleType search_card_type = 17;   // 在此环节，搜索打上了什么强样式的标
    int32 search_ad_pos = 18;                         // 搜索广告下发广告位置
    int32 search_ad_interval = 19;                    // 搜索广告下发广告间隔
    repeated AdjustPriceRecord adjust_price_recode = 20;  // 计费打折策略执行记录
    double rta_bid_ratio = 21;                         // rta 动态出价系数 一次请求
    double rta_bid_ratio_second_bid = 22;              // rta 动态出价系数 二次请求
    double rta_bid = 23;                      // rta 动态出价 一次请求 直接出价
    double rta_bid_second_bid = 24;           // rta 动态出价 二次请求 直接出价
    uint64 rta_trace_id = 25;                 // rta trace id
    int64 rta_sta_tag = 26;                   // rta 出价标记
    int32 universe_tiny_query_type = 27;      // 联盟搜索词匹配类型
    double search_relevance = 28;             // 搜索相关性
    ModelMeta model_meta = 29;                // 模型元信息
    repeated ModelMeta model_meta_list = 30;
    uint64 prerank_retarget_tag = 31;
    int64 game_retarget_status = 32;    // 游戏重定向状态
    bool is_game_retarget_ad = 33;    // 是否重定向
    bool is_paid_fiction_retarget = 34;  // na 小说重定向
    double fctr = 35;  // 精混排一致性 fCTR
    AdEnum.SearchStrongCardType search_strong_card_type = 36;  // 搜索强样式匹配类型
    InnerMultiQueueInfo inner_multi_queue_info = 37;  // 内循环多队列信息
    double mix_unify_gpm = 38;  // 混排口径 gpm
    double mix_unify_rs = 39;  // 预测 rs
  }
  AdTraceBaseInfo ad_base_info = 1;  // 广告基础信息
  bool is_alive = 2;  // 是否返回成功了，如果被过滤了，才有 ad_filter_info 信息
  AdFrontTraceFilterInfo ad_filter_info = 3;  // 广告被过滤的信息
  AdTraceBidInfo ad_bid_info = 4;             // 出价信息
  AdTraceBenifitInfo ad_cpm_info = 5;         // cpm/benifit相关信息
  AdTraceCxrInfo ad_cxr_info = 6;             // cxr相关信息
  AdTraceBonusInfo ad_bonus_info = 7;         // bonum相关信息
  AdTraceUeqInfo ad_ueq_info = 8;             // ueq 相关信息
  FrontAdTraceInfo front_ad_trace_info = 9;   // front添加的一些基础信息
  FrontAdExtendInfo ad_extend_info = 10;      // 当前服务需要的扩展字段
  repeated AdTraceFactorInfo ad_factor_info = 11;   // 因子信息
}
message AdFrontStageInfo {
  enum AdLibRetrievalType {
    RETRIEVAL_DSP = 0;          // adServer
    RETRIEVAL_BRAND = 1;        // adBrand
    RETRIEVAL_FANSTOP = 2;      // adFanstop
    RETRIEVAL_NEW_FANSTOP = 3;  // adServer_fanstop
    RETRIEVAL_SOCIAL = 4;       // adSocial
  }
  repeated FrontAdTraceItemInfo ad_trace_items_info = 1;  // 广告维度信息
  int64 ad_list_num = 2;                                  // 该广告库广告数量
  AdLibRetrievalType ad_select_type = 3;                  // 广告库信息
}
message AdFrontSimplifyTraceLog {
  // 全链路的放到同一个结构里面，各服务只需要写自己需要结构就可以
  AdRequestBaseInfo ad_request_base_info = 1;
  RequestAccessInfo request_access_info = 2;          // 流量控制
  repeated AdFrontStageInfo ad_front_stage_info = 3;  // 广告融合
}

message MixRankStageInfo {
  message MixRankAdInfo {
    int64 ad_pos = 1;                               // 混排后的广告位置
    int64 raw_ad_pos = 2;                           // 引擎下发的原始位置
    kuaishou.ad.AdEnum.AdPriority ad_priority = 3;  // 广告优先级 [保量广告、保位置广告、普通广告]
    double organic_score = 4;
    double ecpm_score = 5;
    double final_score = 6;
  }
  message MixRankTraceFilterInfo {
    string processor_name = 1;
    int64 filter_condition = 2;                                // 废弃
    kuaishou.log.ad.AdTraceFilterCondition filter_reason = 3;  // 过滤原因
    MixRankNodeType node_stage_type = 4;                       // 被过滤时所处的processor类型
  }
  message MixRankExtAdInfo {
    int64 ad_monitor_type = 1;                      // 用于内外循环判断使用
    uint64 multi_overlay_tag = 2;                   // 多路召回召回源 重叠标识 [0-63]
    uint64 multi_overlay_tag_extend = 3;            // 多路召回召回源 [64-127]
    kuaishou.ad.RtaSourceType rta_source_type = 4;  // RTA source
    bool is_fan_follow = 5;                         // 是否是粉丝
    double rta_bid_ratio = 6;                       // rta出价系数
    double rta_bid = 7;                             // rta直接出价
    int64 rta_sta_tag = 8;                          // rta 出价标记
    int64 rta_trace_id = 9;                         // rta trace id
    double ue_score = 10;                           // ue 体验分
    double whole_ue_score = 11;                     // 变换后的 ue 体验分
    double auction_score = 12;                      // 营收竞价分
    double commercial_indep_ensemble_score = 13;    // 商业化二阶段内外循环融合分
    double gpm = 14;                                // 原始 gpm, mix_unify_bid().unify_gpm
    double calib_gpm = 15;                          // 校准 gpm
    double whole_gpm = 16;                          // 变换后的 gpm
    double cpm = 17;                                // 原始 cpm, mix_unify_bid().unify_cpm
    double calib_cpm = 18;                          // 校准 cpm
    double whole_cpm = 19;                          // 变换后的 cpm
    double bonus = 20;                              // 原始 bonus, mix_unify_bid().unify_bonus
    double restrict_bonus = 21;                     // 限制后的 bonus
  }
  message MixRankTraceItemInfo {
    AdTraceBaseInfo ad_base_info = 1;
    AdTraceBidInfo ad_bid_info = 2;
    AdTraceBenifitInfo ad_benifit_info = 3;
    AdTraceCxrInfo ad_cxr_info = 4;
    AdTraceBonusInfo ad_bonus_info = 5;
    AdTraceUeqInfo ad_ueq_info = 6;
    MixRankAdInfo mix_rank_ad_info = 7;
    MixRankTraceFilterInfo mix_rank_filter_info = 8;
    MixRankExtAdInfo mix_rank_ext_ad_info = 9;  // 后续新字段都放在这个字段中
    bool is_alive = 10;
  }
  repeated MixRankTraceItemInfo mix_rank_trace_item_info = 1;
  int64 ad_list_num = 2;
}

message MixRankRequestInfo {
  int64 real_show_page_size = 1;
  int64 natural_photo_size = 2;
  int64 natural_live_size = 3;
}

message MixRankSimplifyTraceLog {
  AdRequestBaseInfo ad_request_base_info = 1;    // 基础请求数据
  MixRankRequestInfo mix_rank_request_info = 2;  // mix rank 服务 pv 级别信息
  MixRankStageInfo mix_rank_stage_info = 3;      // 广告详细数据
}

message AdInnerAnalyseTraceLog {
  message Candidate {
    int64 tag_id = 1;
    string index_name = 2;
    repeated int64 ids = 3;
  }
  AdRequestBaseInfo ad_request_base_info = 1;
  repeated Candidate candidate = 2;
}

message AdNearlineMergedTraceLog {
  message CreativeInfo {
    bool join_ad_log_full_success = 1;  // 是否成功join到ad_log_full
    uint64 account_id = 2;
    uint64 unit_id = 3;
    uint64 creative_id = 4;
    uint64 ad_photo_id = 5;
    string campaign_type = 6;
    string first_industry_name_v3 = 7;
    int64 multi_retrieval_tag = 8;
    int64 new_creative_tag = 9;
    int64 cpa_bid = 10;
    int64 auto_cpa_bid = 11;
    string ocpc_action_type = 12;
    int64 cpm = 13;
    int64 rank_benifit = 14;
    int64 bonus_cpm = 15;
    string tag_multi = 16;
    double server_show_ctr = 17;
    uint64 author_id = 18;
    bool is_in_rank = 19;
    bool is_delivery = 20;
    bool is_imp = 21;
    bool is_p3s = 22;
    bool is_p5s = 23;
    bool is_item_clk = 24;
  }

  message DefaultTraceItemInfo {
    AdTraceBaseInfo ad_base_info = 1;                 // 广告基础信息
    TargetStageInfo.AdExtendInfo ad_extend_info = 2;  // 扩展字段
  }

  int64 llsid = 1;
  repeated AdInnerAnalyseTraceLog.Candidate retrieved_candidates = 2;  // 求交前的trace log信息
  repeated AdTraceBaseInfo targeted_items = 3;                         // 求交后的trace log信息
  repeated CreativeInfo online_ads = 4;  // 与simplify trace log和ad_log_full join得到的结果
  bool join_simplify_trace_success = 5;  // 是否成功join到simplify trace log
  uint64 timestamp = 6;                  // 毫秒时间戳
  repeated DefaultTraceItemInfo default_trace_item_info = 7;  //外循环使用求交后的trace log信息
  AdRequestBaseInfo ad_request_base_info = 8;                 // 近线trace log中的request info
}

message NearlineTraceLog {
  int64 log_timestamp_ms = 1;
  int64 llsid = 2;
  int64 user_id = 3;
  repeated NearlineCreativeInfo creative_info = 4;
  string device_id = 5;
}

message NearlineCreativeInfo {
  int64 creative_id = 1;
  int64 author_id = 2;
  double predict_cvr = 3;
  int64 cpa_bid = 4;
  double auto_atv = 5;
  double calc_cpa_bid = 6;
  double bid_cvr = 7;
  double roi_ratio = 8;
  int64 ocpx_action_type = 9;
  double predict_ctr = 10;
  uint64 photo_id = 11;
  kuaishou.ad.AdEnum.ItemType item_type = 12;
  double recent_cost = 13;
  bool is_write = 14;
}