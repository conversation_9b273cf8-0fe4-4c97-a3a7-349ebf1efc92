/* 
Copy From: kuaishou-ad-new-biz-legacy-proto-1.0.8778-jar/kuaishou/ad/asset/ad_asset_sync_log.proto
Sync Time: 2025-09-03 15:14:47
*/
syntax = "proto3";

package kuaishou.ad.platform;


import "teams/ad/ad_proto/kuaishou/ad/common/enums.proto";

// 同步日志定义
message AdAssetSyncLog {
    oneof event_log {
        AdAssetSyncItem ad_asset_sync_item = 1; //素材同步（算法使用）
        AdAssetCreativeSyncItem ad_asset_creative_sync_item = 2; //创意同步

        AdAssetSyncMaterialItem ad_asset_sync_material_item = 3; //素材同步
    }
}

// 素材库物料同步
message AdAssetSyncMaterialItem {
    AdAssetSourceType asset_source = 1; // 素材来源
    repeated AdAssetPhoto photos = 2; //视频
}

//视频
message AdAssetPhoto{
    uint64 photo_id = 1;
    uint64 user_id = 2;
    uint64 account_id = 3; //账号ID,非必填
    uint64 photo_original_id = 4; // 可以存储业务方原始id，非必填
    uint64 create_time = 5; // 创建时间
    AdEnum.OuterLoopNative outer_loop_native = 6;  // 是否开启原生
    string pre_audit_unique_id = 7; // 预审视频唯一id
}

// 素材库，同步日志格式
message AdAssetSyncItem {
    uint64 account_id = 1; //素材的账号ID
    uint64 unit_id = 2; //追加创意时必传
    repeated AdAssetItem asset_items = 3; // 同步的素材对象列表
    AdAssetOpType asset_op = 4; //操作类型
    uint64 sync_time = 5;
}

// 追加创意至unit
message AdAssetCreativeSyncItem {
    uint64 account_id = 1; //素材的账号ID
    uint64 unit_id = 2; //追加创意时必传
    repeated uint64 creative_id = 3; // 创意拷贝需要新的id
    CreativeCopyType creative_copy_type = 4; //copy模式
    uint64 sync_time = 5;
    AdEnum.UnitType source_unit_type = 6;//来源unit类型
}

//标识一条素材记录
message AdAssetItem {
    uint64 asset_id = 1; //素材唯一ID
    AdAssetType asset_type = 2; //素材类型
    AdAssetSourceType asset_source = 3; // 素材来源
    string score = 4; //分数
    string algo_tags = 5; // 算法标签
    repeated AdAssetItem original_asset_info = 6; //素材来源对象（派生来源）
    string asset_extra_param = 7; //其它属性(预留),json格式
    uint64 sync_time = 8;
}

enum CreativeCopyType {
    CREATIVE_COPY_TYPE_DEFAULT = 0;
    RESERVE_ID = 1; //保留ID
    NEW_ID = 2; //使用新ID
}

// 素材类型
enum AdAssetType {
    AD_ASSET_TYPE_DEFAULT = 0;
    VIDEO = 1; //视频
    PIC_ = 2; // 图片
    TEXT = 3; //文案
    PACKAGE = 8; //素材包
}

// 素材来源
enum AdAssetSourceType {
    LOCAL = 0; //本地
    CREATIVE_CENTER = 1; //开眼
    AD_CREATOR = 2; //素造
    ALGO = 3; //算法
    MAPI = 4; // mapi
    SMART_COVER = 6; //智能抽帧
    AD_SOCIAL = 7; //快接单
    LA = 8;  // LA 素材上传
    APP = 10; // 从主页同步
    AGENT_MADE = 11; //代理商自制
    SYNC_PROMOTION = 12; // 磁力金牛同步推广
    SMALL_SHOP_PHOTO = 13; // 小店上传
}

enum AdAssetOpType {
    AD_ASSET_OP_TYPE_DEFAULT = 0;
    CREATE = 1; //创建素材
    UPDATE_SCORE = 2; //更新素材
    UPDATE_ALGO_TAG = 3; // 更新算法标签
}

