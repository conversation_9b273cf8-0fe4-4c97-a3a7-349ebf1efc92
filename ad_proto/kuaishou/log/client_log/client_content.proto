/* 
Copy From: kuaishou-common-log-proto/src/main/proto/kuaishou/log/client_log/client_content.proto
Sync Time: 2025-09-03 15:12:11
*/
syntax = "proto3";

package kuaishou.client.log;

import "teams/ad/ad_proto/kuaishou/log/client_log/client_base.proto";

// feed透传数据，用于PhotoPackage、LiveStreamPackage等传递策略通道和扩展数据
message FeedLogContext {
    string inter_stid_container = 1; // 策略通道私参，pb格式
    string inter_st_ex_params = 2; // 策略通道扩展信息私参
    string biz_log_params = 3; // 业务埋点透传参数
}

// 废弃，请不要使用
enum LiveEntranceType {
    LiveEntranceType_Default = 0;
    LiveEntranceType_Music_Station = 1; // 音悦台入口
}

enum LiveStreamContentType {
    LiveStreamContentType_Default = 0;
    LiveStreamContentType_Music_Station = 1; // 音悦台直播
    LiveStreamContentType_Thanos = 2; // 滑滑板直播
    LiveStreamContentType_Normal_Slide = 3; // 普通上下滑直播
    LiveStreamContentType_Live_Aggregate = 4; // 关注页直播聚合
    LiveStreamContentType_Follow_Live = 5;// 关注页改版（feed流）直播
    LiveStreamContentType_VOICE_PARTY = 6;// 聊天室直播
    LiveStreamContentType_KTV = 7;// KTV直播
    LiveStreamContentType_GZONE_SLIDE = 8; // 游戏专区上下滑直播
    LiveStreamContentType_GzoneLiveNormal = 9; // 游戏新版直播
    LiveStreamContentType_GzoneLiveSlide = 10; // 游戏新版直播上下滑
    LiveStreamContentType_GZONE_BANNER = 11; // 游戏专区banner直播
    LiveStreamContentType_Live_More_Square_Live = 12; //直播广场二级页面下的直播
    LiveStreamContentType_GZONE_LIVE_CARD_AUTO_PLAY = 13; //游戏专区直播卡片自动播放
    LiveStreamContentType_Hot_Preview = 14; //发现页feed预览直播
    LiveStreamContentType_GZONE_COMPETITION_BANNER = 15; //游戏中心赛事banner直播
    LiveStreamContentType_Profile_Live=16;//个人主页作品tab下直播
    LiveStreamContentType_FREQUENTLY_VISITED_AUTHOR=17;//常看的人页面直播
    LiveStreamContentType_LIVE_PREVIEW_BEAUTIFUL_TIME=18;//简易直播间有精彩时刻

}

// 直播来源
enum LiveSourceType {
    LS_UNKNOWN = 0;
    LS_FEED = 1; // 三大Tab，废弃，使用4，5，6
    LS_PUSH = 2; // Push
    LS_LIVE_SUBSCRIPTION = 3; // 直播订阅
    LS_FOLLOW = 4; // 关注页
    LS_HOT = 5; // 热门页；发现页
    LS_NEARBY = 6; // 同城页
    LS_NEARBY_ROAMING = 7; // 同城漫游页
    LS_SHARE = 8; // 分享
    LS_LIVE_PK = 9; // 直播PK(点击pk对方主播头像，进入对方主播半屏个人页，从半屏个人页中的直播CARD进入对方直播间)
    LS_WEB = 10;
    LS_SMALL_PROGRAM = 11; // 小程序
    LS_FANS_TOP = 12; // 直播粉条
    LS_PRIVATE_MESSAGE = 13; // 私信
    LS_BROADCAST_GIFT = 14; // 穿云箭
    LS_BROADCAST_GIFT_RED_PACKAGE = 15; // 穿云箭红包
    LS_PROFILE = 16; // profile页面
    LS_LIVE_PROFILE_CARD = 17; // 直播页面profile卡片
    LS_LIVE_CLOSE_PAGE = 18; // 直播结束页
    LS_LIVE_MUSIC_STATION_CAPTION = 19; // 直播中音悦台台标
    LS_PROFILE_LIKE = 20; // 目前仅音悦台短视频作品用
    LS_FEED_DETAIL_USER_AVATAR = 21; // 个人作品用户头像
    LS_MUSIC_STATION_USER_AVATAR = 22; // 音悦台作品用户头像
    LS_MUSIC_STATION_USER_PRODUCTS_PAGE = 23; // 音悦台用户作品聚合
    LS_NEWS = 24; // 动态页
    LS_LIVE_FOLLOW_CHANNEL = 25; // 直播间关注人的直播列表
    LS_GAMEZONE_LIVE_GAME_WIDGET = 26; // 直播间挂件
    LS_GAMEZONE_VIDEO_GAME_TAG = 27; // 短视频游戏标签
    LS_GAMEZONE_NEARBY_GAME_ENTRY = 28; // 同城游戏入口
    LS_GAMEZONE_SEARCH_GAME_ENTRY = 29; // 搜索页游戏入口
    LS_GAMEZONE_SIDEBAR_ENTRY = 30; // 侧边栏-游戏
    LS_GAMEZONE_LINK_EXTERNAL = 31; // 游戏外部链接
    LS_GAMEZONE_WEB = 32; // 游戏直播web地址
    LS_VOICE_PARTY_AGGREGATION_RECOMMEND = 33; // 聊天室聚合页广场Tab
    LS_VOICE_PARTY_AGGREGATION_NEARBY = 34; // 聊天室聚合页附近Tab
    LS_VOICE_PARTY_AGGREGATION_KTV = 35; // 聊天室聚合页KTV房间Tab
    LS_VOICE_PARTY_AGGREGATION_TOPIC = 36; // 聊天室聚合页话题房间Tab
    LS_NEARBY_LIVE = 37; // 同城直播聚合
    LS_MUSIC_STATION_HELP = 38; // 音悦台帮助页
    LS_GAMEZONE_TOPICTAG_GAME_SEARCH_RECOMMEND = 39; // 搜索推荐位置话题标签
    LS_GAMEZONE_TOPICTAG_GAME_SEARCH_KEYWORD = 40; // 搜索结果位置话题标签
    LS_GAMEZONE_TOPICTAG_GAME_DETAIL = 41; // 短视频详情页话题标签
    LS_LIVE_PUSH_ARROW_REDPACK = 42; // 直播红包push
    LS_MUSIC_STATION_USER_CENTER = 43; // 音悦台个人中心
    LS_MUSIC_STATION_SIX_SIX_RING = 44; // 66铃声
    LS_FOLLOW_CARD = 45; // 关注页直播单个卡片（直播详情页）
    LS_FOLLOW_AGGR_CARD = 46; // 关注页直播聚合卡片（直播详情页）
    LS_FOLLOW_AUTO_PLAY = 47; // 关注页单个卡片自动播放（关注页）
    LS_LIVE_WATCH_SIDEBAR = 48; // 直播详情页侧边栏（直播详情页）
    LS_FEED_DETAIL_BROADCAST_GIFT = 49; // 短视频详情页穿云箭
    LS_GAMEZONE_GAME_SUBSCRIBE_MESSAGE = 50; // 游戏订阅私信消息
    LS_FOLLOW_CARD_USER_AVATAR = 51; //关注页点击作品头像
    LS_HOT_LIVE_CHANNEL = 52; //发现页直播频道
    LS_GAMEZONE_LAB_BY_GAME = 53; //实验室游戏聚合页入口
    LS_FOLLOW_CARD_AUTO_ENTER = 54; // 关注页直播单个卡片自动进入（直播详情页）
    LS_THANOS_LIVE_SQUARE = 55; // 大屏版直播广场
    LS_MUSIC_STATION_AGGRGATE_PAGE = 56;// 音悦台聚合页
    LS_MUSIC_STATION_MY_FOLLOW_NOTICE = 57;//我关注的人上音悦台提醒
    LS_MUSIC_STATION_TOP_GUIDE_CARD = 58;//音悦台详情页顶部卡片
    LS_GAMEZONE_AGGREGATION = 59;//游戏聚合页面
    LS_RECO_LIVE_SQUARE_AGGREGATE_PAGE = 60;// 大家都在看直播广场聚合页
    LS_CAMERA_CHAIN_LIVE = 61; //锁链直播侧边栏
    LS_DISTRICT_RANK_LIVE = 62; //直播地区小时榜
    LS_NEARBY_RESOURCE_LOCATION = 63; //直播同城地区小时榜资源位
    LS_VOICE_PARTY_CHANNEL_TOPIC_ITEM = 64; //直播聊天室频道话题条栏
    LS_LIVE_ROBOT_PET_CONTRIBUTE_LIST = 65;//直播宠物机器人贡献榜列表
    LS_LIVE_ROBOT_PET_SOCIAL_LIST = 66;//直播宠物机器人社交列表
    LS_THANOS_LIVE_SQUARE_AGGREGATE_PAGE = 67;// 设置大屏版直播广场聚合页
    LS_MUSIC_STATION_KWAI_VOICE = 68; //直播音悦台快手好声音决赛排行榜
    LS_MUSIC_STATION_KWAI_VOICE_MOMMENT = 69; //直播音悦台快手好声音说说
    LS_SEARCH_MUSIC_STATION_CHANNEL = 70; //搜索下的音悦台频道
    LS_MUSIC_STATION_TAG_ENTRANCE = 71; //快手音悦台标签页立即进入
    LS_MUSIC_STATION_KWAI_VOICE_ENTRANCE = 72; //快手音悦台快手好声音挂件入口
    LS_MUSIC_STATION_KWAI_VOICE_H5 = 73; //快手音悦台快手好声音H5页面
    LS_FOLLOW_LIVE = 74;  //关注页直播聚合
    LS_NOTIFICATIONS = 75;  //消息通知入口
    LS_FEATURED_LIVE_SQUARE_AGGREGATE_PAGE = 76;// 底导精选的直播广场聚合页
    LS_MENU_LIVE_SQUARE_AGGREGATE_PAGE = 77;// 侧边栏直播广场聚合页
    LS_MENU = 78;// 侧边栏
    LS_GIFT_WHEEL_EXPENSIVE_GIFT = 79;//直播礼物转盘高价值礼物广播
    LS_VOICE_PARTY_AGGREGATION_RECOMMEND_CHANNEL = 80; //聊天室聚合页下的频道
    LS_SF_PREHEAT_TASK = 81; //春节预热活动的任务体系，观看10s音悦台可得金币（二级页面）
    LS_SF_MAIN_BREAKOUT_VENUE_RESOURCE = 82; //春节当晚分会场页面某一个资源位（三级页面）
    LS_GAMECENTER_VIDEO_FEEDS = 83; // 游戏中心视频列表
    LS_SFENTRANCE = 84; // 春节入口
    LS_NEARBY_LIVE_SQUARE_AGGREGATE_PAGE = 85;// 同城附近直播广场聚合页
    LS_SF2020_LIVE_SQUARE_AGGREGATE_PAGE = 86; //春节直播广场聚合页
    LS_SF2020_LIVE_THANKS_RED_PACK_LIST = 87; //感恩红包榜单
    LS_SF2020_LIVE_THANKS_RED_PACK_NOTIFICATION = 88; //感恩红包直播间消息通知
    LS_SF2020_LIVE_THANKS_RED_PACK_TOKEN_POPUP = 89;  //感恩红包/百万红包口令弹窗
    LS_VOICE_PARTY_HOTROOM_PANDENT = 90;  //聊天室内热门房间挂件
    LS_SEARCH = 91; //搜索
    LS_NEBULA_LIVE_SQUARE_AGGREGATE_PAGE = 92;// 极速版直播广场聚合页
    NEW_LIVE_MORE_SQUARE = 93; //新版直播广场（只有新版直播广场二级页面/抽屉广场）
    MY_FOLLOW_LIVE = 94; //我的关注直播页
    LS_FOLLOW_RECO = 95; //关注页下猜你喜欢推荐入口
    LS_GAMEZONE_NEW_LIVE_GAME_TV_TAB = 96; // 新版游戏直播间游戏TvTab
    LS_FOLLOW_FVA = 97; // 关注页顶部常看的人
    LS_OPERATION_H5 = 98; //运营活动H5页
    LS_FANS_TOP_ORDER_HELP_BUY = 99; //通过我也帮买的粉丝头条
    LS_NATION_RANK_LIVE = 100; //直播全国小时榜（榜单位置）
    LS_SQUARE_RESOURCE_LOCATION = 101; //抽屉广场全国小时榜资源位
    LS_NEBULA_GOLD_LIVE_SQUARE_AGGREGATE_PAGE = 102; //极速版金币挂件任务直播广场
    LS_NEBULA_HOT_PUSH = 103; //极速版发现页直播push
    LS_HOT_PREVIEW_LIVE = 104; //发现页feed预览直播
    LS_BROADCAST_PUSH=105;//直播间内push挂件，共用穿云箭挂件
    LS_GAME_CENTER_COMPETITION=106;//赛事
    LS_NEARBY_LOCAL_TV=107;//同城页本地电视台
    LS_PK_RANK_GAME_OPPONENT = 108;//pk排位赛对手直播间(已下线)
    LS_PK_RANK_GAME_THIS_WEEK_LIST = 109;//pk排位赛本周榜单(已下线)
    LS_PK_RANK_GAME_WINNING_STREAK_LIST = 110;//pk排位赛连胜榜单(已下线)
    LS_PK_RANK_GAME_PREWEEK_TOP_LIST = 111;//pk排位赛上周Top榜单(已下线)
    LS_NEBULA_HOT_TIPS=112;//极速版发现页引导直播tips
    LS_MORE_SQUARE_SEARCH=113;//二级直播广场搜索
    LS_SEARCH_ALADDIN_MORE=114;//搜索阿拉丁更多入口
    LS_NEW_GAME_CENTER_DETAIL = 115; //新版游戏中心详情页
    MUSIC_STATION_HOME =116;  //新版音悦台首页Tab
    MUSIC_TAG_V1 =117;  //老版音乐标签页面
    MUSIC_TAG_V2 =118;  //新版音乐标签页面
    TEXT_MUSIC_TAG =119;  //文字标签页面
    FREQUENTLY_VISITED_AUTHOR=120;//常看的人页面
    LS_AUTO_PROFILE=121;//profile页自动播放
    LS_ASSISTANT_LIVE_PUSH=122;//直播推送页-助手端
    LIVE_FRONT=123;//发现页or详情页直播间透出(废弃)
    RIGHT_HEAD_OF_AUTHOR=124;//发现页or详情页右侧作者头像(废弃)
    LS_TOPIC_HOMEPAGE_AVATAR=125; //话题圈首页点击用户头像
    LS_TOPIC_DETAIL_AVATAR=126; //话题圈详情页点击用户头像
    LS_GIFT_HINT=127;//礼物冠名
    LS_GUARDIAN_HEAD=128;//守护位头像
    LS_BOTTOM_SIDEBAR_FEATURED=129; //底导模式-精选直播广场双列card
    LS_FEATURED_PREVIEW=130; //底导模式精选透出的直播间
    LS_LIVE_ONLINE_USER_PROFILE_CARD = 131; // 在线观众-个人profile卡片
    LS_LIVE_WEEK_RANK_PROFILE_CARD = 132; // 周榜-profile卡片
    LS_KUAIXIANG_CONVENIENCE = 133; // 快享便利贴，贴片一般位于作品左下，覆盖强弱两种样式
    LS_LIVE_ORDER_CALENDAR = 134; // 直播预约日历
    LS_LIVE_ORDER_PRIVATE_LETTER = 135; // 直播预约私信
    LS_BOTTOM_SIDEBAR_FEATURED_SINGLE_LINE=136; //底导模式-精选直播广场单列card
    LS_BOTTOM_SIDEBAR_FEATURED_SINGLE_LINE_AVATAR=137; //底导模式-精选直播广场单列形态下点击常看主播头像
    LS_BOTTOM_SIDEBAR_FEATURED_DOUBLE_LINE_AVATAR=138; //底导模式-精选直播广场双列形态下点击常看主播头像
    LS_LIVE_FREQUENTLY_VISITED=139; //常见主播页-正在直播card
    LS_HOT_PREVIEW_LIVE_RECO = 140; //单列发现页feed预览直播-热门直播入口
    LS_HOT_LIVE = 141; //热门直播页
    LS_LIVE_ORDER_PLC = 142 ;//直播预约plc入口
    LS_LIVE_ORDER_PUSH = 143 ;//直播预约push入口
    NEW_LIVE_MORE_SQUARE_AVATAR = 144; //新版直播广场（抽屉广场）- 外漏的常看主播头像
    LS_LIVE_SHOT_MESSAGE = 145; //直播短信入口
    LS_LIVE_GIFT_EXPIRE_SHOT_MESSAGE = 146; //背包礼物临近过期时发送私信-私信入口
    LS_LIVE_SPLASH_AD = 147; //开屏广告
    LS_TAG_GAME_LIVE = 148; //标签页-游戏标签直播tab
    LS_SQUARE_RESOURCE_DISTRICT_RANK = 149; //抽屉广场地区小时榜资源位
    LS_BUSINESS_RANK_LIVE = 150; // 直播小时榜挂件-电商榜
    LS_LIVE_ONLINE = 151; // 直播连线(点击连线对方主播头像，进入对方主播半屏个人页，从半屏个人页中的直播CARD进入对方直播间)
    LS_MENU_KWAISHOP_HOMEPAGE = 152; // 侧边栏快手小店首页
    LS_FOLLOW_TIME_TAB = 153;//关注页时间筛选器
    LS_LIVE_SQUARE_SLIDE_UPDOWN = 154;// 上下滑形态直播广场
    LS_LIVE_WATCH_NO_MORE_LIVE_POP_LIVE_CARD = 155; //没有更多作品时-推荐弹框中直播card
    LS_LIVE_WATCH_NO_MORE_LIVE_POP_BUTTON = 156; //没有更多作品时-推荐弹框中更多按钮
    LS_EXIT_LIVE_WATCH_RECOMMENDED_LIVE_CARD = 157; //退出直播间-更多精彩直播推荐弹框-直播CARD
    LS_EXIT_LIVE_WATCH_RECOMMENDED_LIVE_AUTHOR_OVERT = 158; // 退出直播间-更多精彩直播推荐弹框-用户live头像
    LS_LIVE_WATCH_NO_MORE_LIVE_POP_AUTHOR_OVERT = 159;// 没有更多作品时-推荐弹框中-用户live头像热区
    LS_KSNEBULA_COIN_CONTROL_MODULE = 160; // 快手极速版-H5任务页(赚钱页)-可配置的模块直播区域
    LS_KSNEBULA_COIN_TASK_CARD = 161; // 快手极速版-H5任务页(赚钱页)-日常任务列表-“看视频赚金币”
    LS_BUSINESS_COIN_TASK_CENTER = 162; // H5电商赚钱页(天天赚快米)-日常任务列表（废弃）
    LS_HOT_CHANNEL_KWAISHOP_HOMEPAGE = 163; // 发现页垂类tab(小店)-快手小店首页
    LS_TOP_NAVIGATION_KWAISHOP_HOMEPAGE = 164; // 顶部导航-小店tab-快手小店首页
    LS_TABBAR_NEARBY_CHANNEL_KWAISHOP_HOMEPAGE = 165; // 底导模式-同城-小店tab-快手小店首页
    LS_CHANNEL_PAGE_KWAISHOP_HOMEPAGE = 166; // 二级频道页-小店tab-快手小店首页
    LS_CLOSE_FROM_ANCHOR_LIVE_ENTER_OTHER_LIVE = 167; // 主播退出连麦直播间引导跳入聊天室-弹框
    LS_CORONA_GAME_BANNER = 168; // 栏目banner
    LS_CORONA_GAME_SUB_CHANNEL = 169; // 栏目游戏二级频道
    LS_ACTIVITY_RED_PACKET_RAIN = 170; // 活动-直播红包雨
    LS_LIVE_PK_ADVERSARY_SCREEN = 171; // 直播PK-对方主播画面直接点击进入对手直播间
    LS_LIVE_ONLINE_ADVERSARY_SCREEN = 172; // 直播连线-对方主播画面直接点击进入对手直播间
    LS_LIVE_SEND_RED_PACKET_HEAD_BUTTON = 173;//直播间快速发红包头像入口
    LS_GAMEZONE_ACCOMPANY_SQUARE = 174;//游戏陪玩带粉广场
    LS_LIVE_RECEIVE_RED_PACKET_RECOMMEND_LIST = 175;//直播间领红包推荐直播列表入口
    LS_FIND_VERTICAL_FIELD_FEED_CARD = 176; // 双列发现页顶导样式「垂类tab」-外漏的直播CARD(除直播tab外)
    LS_NEARBY_FEED_CARD = 177;  //同城feed卡片拉流
    LS_FIND_FEED_CARD = 178; //双列发现页feed卡片拉流
    LS_GLOBAL_LIVE_ROOM = 179; //全局直播间
    LS_GAMEZONE_LIVE_PLAY_TOGETHER_TAB = 180; //游戏直播间一起玩tab
    LS_KWAISHOP_PAY_SUCCESS_LAOTIE = 181;//快手小店支付成功页老铁领优惠
    LS_KWAISHOP_BUSINESS_COIN_LAOTIE = 182;//快手小店频道页老铁领优惠
    LS_NAVIGATION_MODULE_CARD = 183;//极速版底导探索tab二级直播页模块卡片
    LS_MORE_MODULE_CARD =184;//极速版底导探索tab三级更多模块页模块卡片
    LS_KWAISHOP_BUYER_HOME_NEWBUYER_COUPON = 185;// 快手小店买家首页新人弹窗
    LS_THANOS_FIND_FEATURED_RECO_CARD = 186;// 极速版滑滑板发现页混排推荐卡片进直播间
    LIVE_PREVIEW_UN_BEAUTIFUL_TIME = 187;//简易直播间有精彩时刻
    LS_LIVE_WATCH_TOPIC_CIRCLE_PANEL = 188; //直播观看页-话题面板上的直播CARD
    LS_EXPLORE_CARD = 189; //极速版底导探索tab页直播模块卡片
    LS_FRIENDS = 190;//好友页直播
    LS_KWAISHOP_MARCHANT_MORECATEGORY = 191;//快手小店买家首页更多分类页
    LS_KWAISHOP_LABOR_DAY_COLLECT_CARD = 192;//电商五一集卡活动（废弃）
    LS_APPEARANCE_HOURLY_RANK = 193;//颜值小时榜
    LS_NEARBY_TOP_RANK_LIVE = 194;//同城顶级小时榜
    LS_LIVE_COMMON_NOTIFICATION_SHOW = 195;//直播间顶部跑马灯
    LS_LIVE_DSP_FEED_AD = 196;//feed流中的广告
    LS_NEWS_FEED = 197;//动态上下滑feed中的直播
    LS_LIVE_GOLDEN_HOURLY_RANK = 198;// 黄金小时榜
    LS_LIVE_WATCH_TOPIC_CIRCLE_PANEL_REFEREAL_AUCHOR = 199; //直播观看页-话题面板上的话题推荐官
    LS_KWAISHOP_LIVE_TRUST_CARD_BANNER_COUPON = 200;//电商直播间信任卡BANNER优惠券（废弃）
    LS_KWAISHOP_ESHOP_SHOPPING_DAY = 201;//电商616购物节主会场（废弃）
    LS_AWARD_VIDEO_AD = 202;//激励视频引流直播（商业化）
    LS_EXPLORE_LIVE =203;//极速版底导模式探索直播页透出的直播间
    LIVE_PREVIEW_MERCHANT_COUPON_EXPLAIN = 204;//简易直播间电商优惠券或讲解挂件
    LS_FOLLOW_TO_AGGREGATION_LIVE_CARD = 205;//关注页->二级页面双列聚合卡片-进入上下滑直播间
    LS_EXPLORE_POPULAR_DIVERSION_POSITION =206;//极速版底导探索页热榜资源位
    LS_LIVE_VOICE_PARTY_LIST_FIND=207;//从间内放映厅放映列表中的【奥运】列表中点击【找人看】，匹配房间进入新放映厅。
    LS_LIVE_VOICE_PARTY_LIST_FILM_FIND = 208;//从间内放映厅放映列表中的【影视】列表中点击【找人看】，匹配房间进入新放映厅。
    LS_MOMENT_LIVE = 209;//说说中的直播
    LS_KWAISHOP_ESHOP_CENT_LOTTERY = 210;//电商一分抢宝（废弃）
    LS_FOLLOW_FVA_USER_RECOMMEND = 211;//关注页常看的人更多推荐用户的直播
    LS_KWAISHOP_ESHOP_OLYMPICS = 212;//电商奥运活动（废弃）
    LS_KWAISHOP_TRUST_CHANNEL = 213;//电商信任频道（废弃）
    LS_KWAISHOP_OLYMPICS_TASK_NEWBUYER = 214;//电商奥运任务新客（废弃）
    LS_KWAISHOP_OLYMPICS_WALLET_SEARCH = 215;//电商奥运钱包搜索页（废弃）
    LS_KWAISHOP_OLYMPICS_NEWBUYER_COUPON = 216;//电商奥运我的优惠券列表（废弃）
    LS_LIVE_GROUP_CHAT_NOTICE = 217; // 群聊页点击顶部通知进入直播间
    LS_FIND_LIVE_TAB_HOURLY_RANK = 218;//发现页直播垂类tab下小时榜入口
    LS_FIND_LIVE_TAB_OWN_LIVE= 219;//发现页直播垂类tab下我的直播H5页入口
    LS_OLYMIC_H5_HOMEPAGE_FIND_WATCH= 220;//奥运活动大话奥运h5页面点击找人一起看跳转直播间
    SOCIAL_GROUP_CHAT_MESSAGE_DETAIL_SHOW_PHOTO = 221;//群聊消息详情页，半屏个人页下展示的作品
    SOCIAL_MY_PROFILE = 222;//主态个人页
    FIND_CHANNEL_AUTHOR_COLUMN_LIVE_CARD = 223;//发现页垂类tab，作者通栏下直播卡片外漏
    LS_SINGLE_LINE_PHOTO_FEED_USER_AVATAR_RIGHT_BAR = 224;//精选/单列发现页，右侧的用户live头像
    LS_SINGLE_LINE_PHOTO_FEED_USER_AVATAR_BOTTOM_BAR = 225;//精选/单列发现页，左下的用户live头像
    LS_SINGLE_LINE_PHOTO_FEED_SIDEBAR_SHOW_PHOTO = 226;//精选/单列发现页，侧边作品栏直播的CARD
    LS_FIND_VOICE_PARTY_ONE_CLICK = 227;//发现页-聊天室垂类下 一键匹配
    LS_LIVE_VOICE_PARTY_ONE_CLICK = 228;//直播间内  一键匹配
    LS_LIVE_QIXI_ACTIVITY_RED_PACKET =229;//直播七夕活动H5页面热门红包位
    LS_LIVE_SUPER_DIVERSION_POSITION_GIFT = 230; // 传送门礼物
    LS_GZONE_LIVE_HOUR_RANK = 231; // 游戏直播小时榜挂件
    LS_KWAISHOP_QIXI_FEED = 232;//电商七夕会场双feed（废弃）
    LS_INTERSTITIAL_AD = 233; // 插屏广告跳直播(商业化)
    LS_NEARBY_VOICE_PARTY_ONE_CLICK = 234; //同城页聊天室一键匹配
    LS_GZONE_LIVE_KSHELL_RANK = 235; //游戏直播猫粮榜
    LS_GAMEZONE_BET_SQUARE = 236;//游戏直播竞猜广场
    LS_PROFILE_BACKGROUD = 237;//profile的背景图
    LS_ACTIVITY_SUPER_FANS_GROUP_PAGE = 238;//直播超级粉丝团活动页（包括超级粉丝团活动页红包和超级粉丝团活动页主播头像--临时）
    LS_SINGLE_LINE_PHOTO_FEED_AT_FRIEND = 239;//精选，描述区@好友(直播中)
    LS_POPULARITY_RANK_LIVE=240;// 直播间小时榜-人气榜
    LS_ACTIVITY_SUPER_AUTHOR_CHALLENGE_PAGE = 241;//直播超级主播挑战周活动页（点击热门推荐进入上下滑直播间--临时）
    LS_EXIT_LIVE_WATCH_RECOMMENDED_LIVE_SEE_NOW = 242; //退出直播间-更多精彩直播推荐弹框-立即去看按钮
    LS_KSNEBULA_COIN_TASK_LIST_WATCH_LIVE = 243; // 快手极速版-H5任务页(赚钱页)-日常任务列表-“每日看直播1小时”
    LS_KSNEBULA_COIN_TASK_LIST_SEND_LIVE_GIFTS = 244; // 快手极速版-H5任务页(赚钱页)-日常任务列表-“直播间打赏礼物”
    LS_KWAISHOP_DOUBLE_ELEVEN_LIVE_COMMODITY = 245;//电商双十一会场直播间爆款好物（废弃）
    LS_KWAISHOP_DOUBLE_ELEVEN_RECOMMEND = 246;//电商双十一会场好物推荐官（废弃）
    LS_KWAISHOP_DOUBLE_ELEVEN_SUB_LIVE_COMMODITY= 247;//电商双十一直播间爆款好物二级页（废弃）
    LS_KWAISHOP_DOUBLE_ELEVEN_COUPON_LIVE = 248;//电商双十一券使用落地页直播间（废弃）
    LS_LIVE_SHUANGSHIYI_ACTIVITY_RED_PACKET =249;//直播双十一活动H5页面热门红包位-临时
    LS_KWAISHOP_DOUBLE_ELEVEN_OPERATE_RECOMMEND= 250;//电商双十一会场运营位好物推荐（废弃）
    LS_KWAISHOP_MERCHANT_ANCHOR_RECOMMEND = 251;//异形短视频电商主播推荐
    LS_KWAISHOP_MERCHANT_ANCHOR_RECOMMEND_LIVE = 252;//异形短视频电商主播推荐直播间
    LS_KS_COIN_TASK_LIST_WATCH_LIVE = 253; // 快手主站-H5任务页(赚钱页)-日常任务列表-“每日看直播1小时”
    LS_KS_COIN_TASK_LIST_SEND_LIVE_GIFTS = 254; // 快手主站-H5任务页(赚钱页)-日常任务列表-“直播间打赏礼物”
    LS_SPECIAL_FOLLOW_WIDGET_POPUP = 255; // 手机桌面组件上的特别关注挂件中显示正在直播的用户头像，点击用户头像后跳转直播间
    LS_KWAI_ACTIVE_PENDANT_USER_HEAD =256; //直播间右下角挂件-点击用户头像进到新直播间（2021年度盛典活动亲友团挂件）
    LS_LIVE_NIANDUSHENGDIAIN_ACTIVITY_RED_PACKET =257;//直播年度盛典活动H5页面热门红包位
    LS_LIVE_ACTIVITY_CHUNJIE_CONTENT_RECO_LIVE_HEAD=258;//直播2022春节活动-春节内容线活动页推荐直播卡片（废弃）
    LS_LIVE_ACTIVITY_CHUNJIE_CONTENT_TRAILER_HEAD=259;//直播2022春节活动-春节内容线活动页预告直播中头像（废弃）
    LS_LIVE_ACTIVITY_CHUNJIE_CONTENT_HOT_CARD=260;//直播2022春节活动-春节内容线活动页最新热点
    LS_BUCKCAR_LIVE_CARD=261;//领克直播页点击直播卡片进入领克直播间-领克车机产品
    LS_BUCKCAR_ENTER_LIVE_BUTTON=262;//领克简易直播间点击进入领克直播间-领克车机产品
    LS_LIVE_TAKE_A_SHOT=263;// 直播间使用摇一摇功能进入新的直播间（22春节-营收活动引入）
    LS_NEARBY_MAP_LIVE=264;//同城地图附近直播
    LS_NEARBY_MAP_HOT=265;//同城地图附近热点
    LS_LIVE_NIANDUSHENGDIAIN_ACTIVITY_HOT_CRAD=266;//直播2022年度盛典活动热门标签词条
    LS_LIVE_NIANDUSHENGDIAIN_ACTIVITY_LIVE_CARD=267;//直播2022年度盛典活动热门看点页左方直播卡片
    LS_LIVE_NIANDUSHENGDIAIN_ACTIVITY_HOT_WORD=268;//直播2022年度盛典活动热门看点页热门词条卡片
    LS_ACTIVITY_HOMEPAGE_H5_TEMPORARY_ENTRANCE=269;//H5页面非常驻功能直播间入口通用的source（包括日常的运营活动、临时性功能等）
    LS_OP_ACTIVITY_MAIN_PAGE_BANNER=270;//冬奥主会场banner
    LS_CNY_TASK_RECHANGE=271;//春节任务-充值任务进入直播间
    LS_CNY_WARM_UP=272;//春节预热-直播格子跳转直播间
    LS_CNY_TASK_FANSGROUP=273;//春节任务-粉丝团进入直播间
    LS_CNY_TASK_WISH=274;//春节任务-去直播间许愿
    LS_MY_PROFILE_WISH_ENTER=275; //P页许愿直播间入口
    LS_NEARBY_POST_GROUP=276; //招聘聚合页进入招聘直播间
    LS_ESP_MOBILE_H5=277; //直播推广H5页增加进入直播间入口-自动拉起下单h5页。
    LS_FOLLOW_PREVIEW=278;//原【关注视频详情页简易直播间进入的直播上下滑】，修改为关注单列简易直播间。
    LS_CNY_WISH_ROOM_NOTIFICATIONS = 279;  //许愿直播间-许愿消息通知入口
    LS_LIVE_HOURLY_RANK_TOP_AUTHOR = 280; //直播小时榜榜单顶部卡片top3主播
    LS_NEWS_SLIDE = 281;//朋友在看简易直播间
    LS_TV_STATION = 282;//放映厅直播间入口
    LS_FOLLOWING_LIST_HEAD = 283;//关注列表页-live头像
    LS_SINGLE_LINE_PHOTO_FEED_USER_AVATAR_RIGHT_BAR_GREAT_VIDEO = 284;//精彩片段视频的右侧的用户live头像
    LS_SINGLE_LINE_PHOTO_FEED_USER_AVATAR_BOTTOM_BAR_GREAT_VIDEO = 285;//精彩片段视频的左下的用户live头像
    LS_SINGLE_LINE_PHOTO_FEED_SIDEBAR_SHOW_PHOTO_GREAT_VIDEO = 286;//精彩片段视频的侧边作品栏直播的CARD
    LS_LIVE_MEMBER_MANAGEMENT_LIST_HEAD=287;//间内会员管理页-点击更多会员直播间列表
    LS_SEARCH_LIST_LIVE=288;//搜索首页榜单-直播榜
    LS_SEARCH_LIST_SHOP=289;//搜索首页榜单-购物榜
    LIVE_VOICE_PARTY_GUIDE_MIC=290;//由未成功上麦聊天室引导到其他聊天室上麦
    LS_KISS_GIFT_H5_HEAD=293;//亲吻蛙礼物h5页面榜单用户头像
    LS_LIVE_INTERACTIVEGAME_TV=296;//从游戏tv进入的互动游戏直播间
    LS_MERCHANT_CS_CRM=297;//从电商客服CRM跳转至直播间
    LS_ALLIANCE_PROFILE=298;//从联盟链接进入P页后跳转至直播间
    LS_MERCHANT_GOODS_LIST_ITEM=299;//短视频小黄车吊起商品列表页点击商品（作品引流广告可以跳转）
    LS_NEAABY_PREVIEW=300;//同城单列简易直播间
    LS_FOLLOW_PREVIEW_SECOND=301;//关注视频详情页简易直播间进入的直播上下滑
    LS_KSNEBULA_COIN_EXCHANGE_POPUP = 302; // 快手主站&极速版-快币兑换完成弹窗
    LS_KSNEBULA_GOLD_COIN_EXCHANGE_LIST = 303;//快手主站&极速版-金币商城兑换记录列表
    LS_LIVE_MULTI_PK = 304;//直播多人PK场景
    LS_AD_WATCH_LIST_DETAIL=305;//商业化看播任务详情页
    LS_LIVE_PUSH=306;//直播开播/红包/PK提醒push
    LS_KSNEBULA_COIN_TASK_SPEED_CARD = 307; // 快手极速版-H5任务页(赚钱页)-加速领现金任务列表-“直播打赏领现金”
    LS_LVIE_BLIND_DATA_WRITE_RESULT_PAGE_BUTTON = 308;//相亲信息提交页去看更多相亲直播按钮
    LS_WISH_LIVE_WISH_POPUP=309;//姻缘直播间中点击心愿弹窗去看直播按钮进到新的直播间
    LS_DELIVERY_JOB_LIVE_AVATAR=310;//点击求职列表的live头像进入直播间
    LS_MERCHANT_BUYER_MALL_TAB=311;//电商商城tab直播卡片
    LS_DP_CHANEL=312;//增长平台DP投放类型
    LS_MERCHANT_LXJ_AVATAR=313;//理想家房产小程序头像
    LS_MERCHANT_LXJ_DETAIL_AVATAR=314;//理想家房产详情页头像
    LS_SIMPLE_BOTTOM_EXPENTION=315;//简易直播间底部内容泛化条
    LS_LIVE_HOTSPOT_DETAIL=316; //直播热点详情页
    LS_POI_DETAIL_LIVE_MODULE=317; //POI详情页直播模块入口
    LS_HOURLY_RANK_CITY_LIVE=318; //直播小时榜榜单-城市榜（榜单位置）
    LS_AND_HONGMENG_CARD_H5_ENTRY=319;//安卓鸿蒙卡片跳转H5页面后，再跳转进入端内直播间。
    LS_LIVE_POST_RANK=320; //招聘直播间热门职位推荐榜
    LS_KWAISHOP_RECO=321;//电商业务猜你喜欢直播卡片，多个页面都会有此类卡片
    LS_BROADCAST_GIFT_HIGH_VALUE=322;//推送高价值直播间穿云箭
    LS_LIVE_COMMENT_NOTICE=323;//评论区强提醒跳转直播间
    LS_HOT_INSIDE_FEED_PREVIEW_LIVE=324;//热榜内流feed简易直播间
    LS_HOT_SEARCH_KBOX_CARD_LIVE=325;//搜索热榜Kbox直播卡片
    LS_HOT_SINGLE_AGGR_CARD_LIVE=326;//单列热榜卡直播卡片
    LS_FIND_CARD_USER_AVATAR=327;//发现页点击作品头像
    LS_LITE_SIDE_LIVE_CARD=328;//LITE点击更多直播卡片观看
    LS_LITE_SIDE_RETURN=329;//LITE更多直播返回精选/单列发现外流观看
    LS_GRABCARD_AUTO_ENTER_ACTIVITY_LIVE=330; //使用暴富卡抢红包-自动进入下一个活动直播间（适用于营收红包活动，间内功能非H5入口）
    LS_ANCHOR_ENDPAGE_RECO_LIVE=331; //直播结束页(主播端)-优秀直播间推荐模块
    LS_LIVE_MASK_MORE_LIVE=332;//直播间内遮罩-更多直播按钮
    LS_LIVE_2023_4TAB = 333; //23春节专用，第4tab
    LS_PROFILE_FANGCHAN_CARD = 334;//个人资料页房产服务跳转直播间
    LS_FANGCHAN_MARKET_ACTIVITY_PAGE = 335; //房产大促活动页跳转直播间
    LS_XTAB_LIFE_CARD_LIVING_PHOTO = 336; //XTab生活tab卡片live头像
    LS_HOTSPOT_PREVIEW_LIVE=337;//XTAB热点简易直播间
    LS_NEARBY_MAP_LIVE_VOICE=338;//同城地图此刻语音聊天室
    LS_LIVE_RESERVE_STICKER=339;//直播预约贴纸点击进入直播间
    LS_TV_STATION_CARD=340;//放映厅直播预览卡片点击进入直播间
    LS_COIN_TASK_GANDI_CARD=341;//赚钱页-肝帝计划-看直播赚金币任务
    LS_HOT_LIVE_CHANNEL_PREVIEW=342;//直播tab简易直播间
    LS_SCHOOL_MAP=343;//双列顶导校园tab直播间入口
    LS_SEARCH_COMMODITY_MINI_PREVIEW=344;//搜索商品单元内流简易直播间
    LS_FEATURE_FLOATING_LAYER=345;//间外推荐更多直播浮层
    LS_LIVE_GUIDE_POPUP=346;//全局直播间内导流弹窗
    LS_INTERACTIVE_INTERACTION=347;//小玩法PK连屏点击对方主播画面进入直播间
    LS_MENU_VOICE_PARTY = 348;// 侧边栏_聊天室
    LS_DANMU_RANK_LIVE=349;// 直播间小时榜-弹幕排行榜
    LS_LIVE_NEW_RANK=350;//直播间小时榜-新人小时榜
    LS_HOT_TALK=351;//新版热榜详情页
    LS_SINGLE_LINE_PHOTO_FEED_QUICK_INTERACTIVE = 352;//精选/单列发现页/P页，底部快捷互动区
    LS_SIMPLE_BOTTOM_CARD=353;//简易直播间底部推荐卡
    LS_TALENT_LIST=354;//才艺榜榜单头像入口
    LS_KWAISHOP_FLOATING_WINDOW=355;//电商买首、商城直播小窗点击进间
    LS_FIND_JOB_SQUARE=356;//快聘职位广场
    LS_NEARBY_SIMPLE=357;//同城单列简易直播间
    LS_FIND_FEED_SIMPLE=358;//主站双列发现页简易直播间
    LS_BARRAGE_GAMEPLAY_CENTER_MAIN_PAGE=359;//弹幕玩法中心页面
    LS_RVIP_FEATURED_CHANNEL=360;//大Rvip精选页面
    LS_DANMU_GAMEPLAY=361;//弹幕互动榜下的弹幕玩法榜单
    LS_PROFILE_BUTTON_GO_LIVE=362;//直播半屏P页，pk/连线时点击底部按钮进入对方主播主播间
    LS_MENU_OFTEN_WATCH=363;//app侧边栏-常看的人
    LS_SINGLE_PLACE_TAG=364;//单列位置标签，详情见需求文档：https://docs.corp.kuaishou.com/d/home/<USER>
    LS_NEARBY_DOUBLE_SIMPLE=365;//同城页双列短视频内流出简易直播间，详情见需求文档：https://docs.corp.kuaishou.com/d/home/<USER>
    LS_DANMU_SCREEN_TREASURE_BOX_VIEWING_TASK=366;//弹幕宝箱观看任务去观看按钮
    LS_TUANBO_RANK_LIVE=367;//小时榜-团播榜
    LS_GONGGE_RANK_LIVE=368;//小时榜-宫格榜
    LS_COMMON_DETAIL_PREVIEW=369;//通用上下滑场景接入 简易直播间消费FT，https://docs.corp.kuaishou.com/d/home/<USER>
    LS_SINGLE_BIG_CARD=370;//点击单列热门推荐主播的直播大卡，进入直播间上下滑，https://docs.corp.kuaishou.com/d/home/<USER>
    LS_PHOTO_ANCHOR_NAME=371;//点击短视频的@好友名称的live头像，进入直播间上下滑，https://docs.corp.kuaishou.com/d/home/<USER>
    LS_DOUBLE_FIND_NEW_FOLLOW_CARD=372;//双列发现页新关大卡点击进入直播间，https://docs.corp.kuaishou.com/d/home/<USER>
    LS_BANBAN_LIVE_CARD=373;//直播伴伴直播卡片，点击进入直播间，https://docs.corp.kuaishou.com/d/home/<USER>
    LS_INCREASE_GOLD_CARD=374;//增长金币激励挂件，点击进入直播间，https://docs.corp.kuaishou.com/k/home/<USER>/fcACTkpL7kFO7giOt4OPJkSLF?ro=false
    LS_FIND_FRIENDS_PYMK=375;//推荐朋友PYMK头像，点击进入直播间,https://docs.corp.kuaishou.com/d/home/<USER>
    LS_WIDGETER_LIVE_REMINDER=376;//桌面开播提醒组件，跳转进入直播间，https://docs.corp.kuaishou.com/d/home/<USER>
    LS_PUSH_POPUP=377;//从站外push已关弹窗，跳转进入直播间，https://docs.corp.kuaishou.com/d/home/<USER>
    LS_FOLLOW_SEE_MORE=378;//关注页天窗查看更多直播页面的直播头像，跳转进入直播间，https://docs.corp.kuaishou.com/d/home/<USER>
    LS_WIDGETER_LIVE_REMINDER_BOOKMARK=379;//桌面开播提醒书签，跳转进入直播间，https://docs.corp.kuaishou.com/d/home/<USER>
    LS_SIMPLE_LIVE_PUSH=380;//Push落地简易直播间，跳转进入直播间，https://docs.corp.kuaishou.com/k/home/<USER>/fcAA5xQ5SYWJ55iTZ5LeElfU-
    }

message LiveStreamPackage {
    // 已废弃，使用LiveSourceType
    enum SourceType {
        UNKNOWN = 0;
        FEED = 1; // 三大Tab，废弃，使用4，5，6
        PUSH = 2; // Push
        LIVE_SUBSCRIPTION = 3; // 直播订阅
        FOLLOW = 4; // 关注页
        HOT = 5; // 热门页
        NEARBY = 6; // 同城页
        NEARBY_ROAMING = 7; // 同城漫游页
        SHARE = 8; // 分享
        LIVE_PK = 9; // 直播PK
        WEB = 10;
        SMALL_PROGRAM = 11; // 小程序
        FANS_TOP = 12; // 直播粉条
        PRIVATE_MESSAGE = 13; // 私信
        BROADCAST_GIFT = 14; // 穿云箭
        BROADCAST_GIFT_RED_PACKAGE = 15; // 穿云箭红包
        PROFILE = 16; // profile页面
        LIVE_PROFILE_CARD = 17; // 直播页面profile卡片
        LIVE_CLOSE_PAGE = 18; // 直播结束页
    }

    // feed流外显图标
    enum ExternalIcon {
        UNKNOWN1 = 0;
        NOMAL_LIVE = 1; // 普通直播
        RED_PACKET = 2; // 红包
        SHOP_CAR = 3; // 小黄车
        KTV = 4; // KTV
        VOICE_PARTY = 5; // 聊天室
        FANSTOP = 6; // 粉条
        PK = 7;//PK
        LAST_VIEWED = 8; //最近看过
        GAME = 9;//游戏
        DISTRICT_RANK = 10; //小时榜
        HOT_LIVE = 11;//热门直播
        PAID_LIVE = 12;//付费直播
        COURSE = 13;//小课堂
        THEATER =14;//放映厅
        TEAM_PK=15; //团战pk
        BOOKED = 16;//已预约
      }

    enum LiveFormat {
        UNKNOWN2 = 0;
        LIVE_FORMAT_NOMAL = 1;//普通直播间
        LIVE_FORMAT_VOICE_PARTY = 2;// 聊天室
        LIVE_FORMAT_KTV = 3;// KTV
    }
    //直播间一级样式信息
    enum LiveStyle {
        STYLE_UNKNOWN = 0;
        SIMPLE = 1;//简易直播间
        NORMAL = 2;//正常直播间
        LITE = 3;//lite直播间
        FOURTH_TAB = 4;//第四tab运营直播间
        FLOATING_WINDOW = 5;//直播小窗
    }
    //直播间二级样式信息，是一级样式信息的细分
    enum LiveSubStyle {
        SUBTYPE_UNKNOWN = 0;
        SIMPLE_ONLINE = 1;//简易直播间（不支持点赞、评论、分享等）
        SIMPLE_V2 = 2;//简易直播间2.0（支持点赞、评论、分享等）
    }
    //直播间一级类型，一级类型按照业务领域划分，有优先级判断顺序，且判断结果唯一
    enum LiveStreamMainType {
        UNKNOWN_LIVE_MAIN_TYPE = 0;
        LIVE_SHOW = 1; // 秀场直播间
        LIVE_GAME = 2; // 游戏直播间
        LIVE_SHOP = 3; // 电商直播间
        LIVE_LOCAL_LIFE = 4; // 本地生活直播间
        LIVE_SELL_HOUSE = 5; // 房产直播间
        LIVE_RECRUIT = 6; // 招聘直播间
        BUSINESS_CHANNEL = 7; // 商业化通路，例如：商业化小铃铛、小程序、商业化招商、商业化蓝v等等
    }
    // 直播间二级类型，在一级类型下的细分
    enum LiveStreamSubType {
        UNKNOWN_LIVE_SUB_TYPE = 0;
        LIVE_SHOP_CART = 1; // 开启挂车，电商主播开启挂车一级类型归属电商、非电商主播开启挂车归属其它一级类型
        LIVE_LOCAL_LIFE_PURE = 2; // 本地生活-开启团购
        LIVE_SELL_HOUSE_PURE = 3; // 本地生活-开启卖房
        LIVE_RECRUIT_PURE = 4; // 招聘-开启快聘
        LIVE_BLIND_DATE = 5; // 开启相亲
        LIVE_GR_ACCOUNT = 6; // GR主播
        LIVE_PR_ACCOUNT = 7; // PR主播
        LIVE_BULLET_SCREEN_PLAY = 8; // 弹幕小玩法
        LIVE_SPORT = 9; // 体育赛事，赛事直播间使用游戏多tab能力，但一级类型归属于秀场
        LIVE_HEALTH = 10; // 健康垂类，一级类型归属秀场
        LIVE_PAID = 11; // 付费直播
        LIVE_COURSE = 12; // 课程直播
        LIVE_SHOW_PURE = 13; // 单纯的秀场直播间
        LIVE_GAME_COMPETITION = 14; // 游戏赛事直播间
        BUSINESS_AD_BELL = 15; // 小铃铛
        BUSINESS_AD_ECO = 16; // 商业生态，包括小程序、商业化招商、商业化蓝v等等
        LIVE_GAME_NORMAL = 17; // 普通游戏直播间
        LIVE_VOICE_PARTY_SUB_TYPE = 18; // 聊天室直播间,为解决namespace下的命名冲突增加SUB_TYPE后缀
    }

    string identity = 1; // 内容Id
    string name = 2; // 内容名称
    string host = 3; // host 的地址
    string port = 4; // 端口号
    string url = 5; // 拉流地址
    string ip = 6; // ip 地址
    bool is_anchor = 7; // 是否是主播
    string anchor_user_id = 8; // 主播uid
    uint64 audience_number = 9; // 观众数
    string game_id = 10; // 游戏id
    string game_name = 11; // 游戏名称
    string live_stream_id = 12; // 直播间id
    LiveEntranceType entrance_type = 13; // 废弃，请不要使用，直播入口类型
    SourceType source_type = 14; // 废弃，请不要使用，使用 LiveSourceType 类型的字段，直播来源
    string source_url = 15; // 直播来源具体的url，一般用于快协议打开直播
    string session_id = 16; // 直播session id，同一次观看行为的多次上报可以根据这个聚合
    LiveStreamContentType content_type = 17; // 直播的内容类型，音悦台直播等
    LiveSourceType source_type_new = 18; // 来源
    string distince = 19; //距离
    ExternalIcon external_icon = 20;//feed流外显图标
    bool friend = 21;//是否好友
    bool my_follow = 22;//是否我的关注
    string audience_number_string = 23; //直播观众数，字符串类型
    string server_exp_tag = 24; //推荐后端的参数
    LiveFormat live_format = 25;//直播形式
    bool kuaishou_musician = 26;//是否快手音乐人
    string reco_text = 27;//运营推荐的文案
    bool from_live = 28;//是否来自直播间
    uint64 show_index_plus_one = 29; //直播间所在位置，从1开始自增（双feed流下，左1右2， 上下滑模式下 滑动到的第几个上报几）
    uint64 live_operation_type = 30; //直播运营类型，具体值服务端下发，判断是运营直播还是普通直播，0：普通推荐直播，1：因为兼容问题，所有运营类型统称是1， 2：特殊重大活动，3：小时榜，4：工会，5：签约主播，10：工会、签约主播常规推荐
    LiveSourceType refer_live_source_type = 31; //二级来源入口字段
    string aggregation_session_id = 32; //聚合入口的会话ID
    string live_icon_type = 33; //feed流外显card的标签，客户端透穿，对应服务端字段 LiveCoverWidgetType
    string live_reco_label = 34; //feed流外显推荐理由标签外漏，单列左下的标记，例：你的好友、你的关注、特别关注、你打赏过等
    string live_icon_text_type = 35; //feed流外显card左上角的标签文案，客户端透传，对应服务端字段coverWidgets.textInfo.textType
    string live_icon_reason_text_type = 36; //feed流外显card左上角的二级标签类型，客户端透传，对应服务端字段coverWidgets.reasonTextInfo.textType
    string live_icon_reason_extra_info = 37; //feed流外显card左上角的二级标签信息，客户端透传，对应服务端字段coverWidgets.reasonTextInfo.extraInfo,包含rankType：榜单类型、rank：所在排名信息
    string live_icon_text_extra_info = 38; //feed流外显card左上角标签的二级信息，应用于电商主播的标签，客户端透传，对应服务端字段coverWidgets.textInfo.extraParam
    bool is_normal_play = 39; // 是否是正常直播间
    LiveStyle live_style = 40; //直播间样式
    FeedLogContext feed_log_ctx = 41; // feed透传数据
    string union_live_author = 42;// feed嘉宾分发时嘉宾ID
    bool is_fullscreen=43;//是否旋转后屏幕
    bool is_landscape=44;//是否横屏流样式
    LiveStreamMainType live_main_type=45;//直播间一级类型
    repeated LiveStreamSubType live_sub_type=46;//直播间二级类型
    LiveSubStyle live_sub_style=47;//直播间二级样式信息
}

// 直播聊天室放映厅信息
message LiveVoicePartyTheaterPackage {

    // 观众离开放映厅原因
    enum LeaveTheaterReason {
        UNKNOWN = 0;
        AUDIENCE_EXIT_LIVE = 1;// 退出直播间
        AUTHOR_CLOSE_THEATER = 2;// 主播关闭放映厅
        AUTHOR_CLOSE_VOICE_PARTY = 3;// 主播关闭聊天室
        AUTHOR_CLOSE_LIVE = 4;// 主播关播
    }
    // 主播离开放映厅剧集原因
    enum AuthorLeavetheaterSeriesReason {
        UNKNOWN1 = 0;
        SERIES_END = 1;// 剧集正常播完
        CHANGE = 2;//主播切换
        CLOSE_THEATER = 3;//主播关闭放映厅
        CLOSE_VOICE_PARTY = 4;//关播聊天室
        CLOSE_LIVE = 5;//关闭直播
    }
    //观众离开放映厅剧集原因
    enum AudienceLeavetheaterSeriesReason {
        UNKNOWN2 = 0;
        AUTHOR_OPERATE = 1;//主播操作
        EXIT = 2;//观众主动退出
    }

    //剧类型
    enum DramaType {
        UNKNOWN3 = 0;
        TUBE = 1;//小剧场
        ACFUN = 2;//A站
        FILM = 3;//影视资源
        LIVE = 4;//直播
    }

    string theater_id = 1; // 放映厅id
    uint64 enter_theater_timestamp = 2; // 观众进入放映厅观看时间戳
    uint64 leave_theater_timestamp = 3; // 观众离开放映厅观看时间戳
    uint64 enter_theater_series_timestamp = 4; // 主播或观众进入观看剧集的时间戳
    uint64 leave_theater_series_timestamp = 5; // 主播或观众离开观看剧集时间戳
    string series_id = 6;//剧id
    uint32 index = 7;//第几集
    LeaveTheaterReason leave_theater_reason = 8;//观众离开放映厅原因
    AuthorLeavetheaterSeriesReason author_leave_theater_series_reason = 9;//主播离开放映厅剧集原因
    AudienceLeavetheaterSeriesReason audience_leave_theater_series_reason = 10;//观众离开放映厅剧集原因
    uint64 fullscreen_duration = 11;//全屏时长ms，
    uint64 pause_duration = 12;//主播暂停时长ms
    uint64 clearscteen_duration = 13;//清屏时长ms
    uint64 seek_count = 14;// 主播拖动次数
    uint64 theater_mic_start_timestamp = 15;//观众在放映厅期间麦上的开始时间戳
    uint64 theater_mic_end_timestamp = 16;//观众在放映厅期间麦上的结束时间戳
    uint64 theater_close_mic_duration = 17;// 观众在放映厅期间麦上的闭麦时长,单位ms
    DramaType drama_type = 18;//剧类型
    uint64 landscape_duration = 19;//横屏观看时长ms
}


// 聊天室团战Pk数据
message LiveVoicePartyTeamPkPackage {
    // 观看团战结束原因
    enum PlayTeamPkEndReason {
        UNKNOWN = 0;
        EXIT_LIVE = 1;// 退出直播间
        CLOSE_TEAMPK = 2;// 主播关闭团战
        CLOSE_VOICE_PARTY = 3;// 主播关闭聊天室
        CLOSE_LIVE = 4;// 主播关播
    }
    //战队持方
    enum TeamHolder {
        UNKNOWN2 = 0;
        YELLO = 1;//黄方
        BULE = 2;//蓝方
    }
    //pk状态
    enum PkStatus {
        UNKNOWN3 = 0;
        INITIAL = 1;//未开始
        MEDIUM = 2;//pk中
        PUNISH = 3;//惩罚中
        END = 4;//pk结束,目前无此场景
    }
    string team_pk_room_id = 1; // 团战id
    uint64 play_teampk_start_timestamp = 2; // 开始观看团战时间戳
    uint64 play_teampk_end_timestamp = 3; // 结束观看团战时间戳
    uint64 play_pk_start_timestamp = 4; // 开始观看团战某一轮pk时间戳
    uint64 play_pk_end_timestamp = 5; // 结束观看团战某一轮pk时间戳
    string pk_id = 6;//每一轮的pk_id
    PlayTeamPkEndReason play_teampk_end_reason = 7;//观看团战结束原因
    TeamHolder team_holder = 8;// 战队持方
    PkStatus pk_status = 9;//PK状态
    uint64 teampk_mic_start_timestamp = 10;//在麦上的开始时间戳
    uint64 teampk_mic_end_timestamp = 11;//在麦上的结束时间戳
    uint64 teampk_close_mic_duration = 12;// 观众在团战pk期间麦上的闭麦时长,单位ms
}

message UserPackage {

    enum AccountType {
        UNKNOWN = 0; // 未知
        NORMAL = 1; // 普通用户
        BUSINESS_ACCOUNT = 2; // 商家号用户
    }

    string identity = 1; // 快手用户Id
    string kwai_id = 2; // 快手号, 新增的用户唯一标识，用户可以自定义，目的是弱化原有的数字编号，原来显示数字编号的地方都会被替换成显示快手号
    uint32 index = 3; // 用户位置
    string params = 4; // 记录用户相关补充信息，比如直播间送礼数、用户特殊标签等
    bool promotion_tag = 5; // 记录用户是否带推广标识，true代表是，false代表否
    AccountType accountType = 6; // 账户类型
    string avatar_status = 7; // 头像的状态
    string respack_id = 8; // 主播标签id;
}

// 批量记录用户相关信息
message BatchUserPackage {
    repeated UserPackage user_package = 1;
}

message ScreenPackage {
    enum Orientation {
        UNKNOWN1 = 0;
        PORTRAIT = 1; // 竖屏
        LANDSCAPE = 2; // 横屏
    }

    Orientation orientation = 1; // 屏幕方向
}

// 作品的信息, 这里是已经发布到我们平台的作品信息，是针对这个作品的所属，不包含视频的基础信息
message PhotoPackage {
    enum Type {
        UNKNOWN1 = 0;//未知
        PHOTO = 1;//视频
        LIVE_STREAM = 2;//直播
        MOMENT = 3;//说说
        ARTICLE = 4;//文章
        IMAGE = 5;//图片
        ATLAS=6;// 图集
        APPLET=7;// 小程序
        PANORAMIC=8;//全景视频，已废弃
        MOOD=9;//心情视频
    }
    enum AtlasType {
        UNKNOWN= 0;
        HORIZONTAL= 1;//横向多图
        VERTICAL= 2;//竖向长图
        SINGLE= 3;//单图
    }
    enum PhotoType {
        UNKNOWN3= 0;
        COMMON= 1;//普通视频
        PANORAMIC_PHOTO= 2;//全景视频
    }
    enum SubType{
        UNKNOWN2 = 0;
        LONG_ARTICLE = 1;  // 子类型：长文
    }
    enum VideoPlayStatus {
	    UNKNOWN4 = 0;
        USER_PLAY = 1; // 用户手动播放
        USER_PAUSE = 2; // 用户手动暂停
    }
 
    Type type = 1; // 内容的类型
    string identity = 2; // 内容 id
    uint64 author_id = 3; //废弃 作品作者的快手用户 id
    string exp_tag = 4; // 推荐后端的参数
    uint64 index = 5; // 视频位置id
    string llsid = 6; // 曝光id
    string keyword = 7; // 标签关键字
    uint32 vertical_index = 8; // 视频纵向位置id
    string s_author_id = 9; // 作品作者的快手用户 id
    bool full_screen_display = 10; // 在视频播放页是否全屏展示，true代表是，false代表否
    uint64 draw_time = 11; // 绘制时长
    bool tag_owner = 12; // 是否为作品标签的发起者
    bool share_identify = 13; // 是否有转发标识
    string server_exp_tag = 14; // 代替exp_tag的字段，内容包含exp_tag，author_id，photo_id/live_stream_id
    string is_top = 15; // 是否置顶 1:是 0:否
    string is_clip = 16; // 作品封面是否被裁剪
    SubType subtype = 17; // 子类型，长文需求增加
    AtlasType atlastype=18;//图集的子类型
    string extra_info = 19;//业务扩展参数,json格式
    bool support_landscape_mode = 20;//是否支持横屏播放模式，true代表是，false代表否
    bool landscape_screen_display =21;//视频是否横屏播放，true代表是，false代表否
    PhotoType photo_type = 22;  // 视频的类型，PANORAMIC代表全景视频，全景视频需要增加
    string source = 23;  // 来源页（对应click2补充）
    string report_context = 24;  // 该字段存放客户端和服务端之前的透传字段，主要用于日志上报，reco推荐的场景（对应click2补充）
    VideoPlayStatus video_play_stat=25; //作品播放状态，USER_PLAY代表用户手动播放，USER_PAUSE代表用户手动暂停
    FeedLogContext feed_log_ctx = 26; // feed透传数据
    bool is_live_pictures = 27; // 记录图文是否为实况图，true代表是，false代表否
}

// 视频的原始参数，包含视频的编码参数，比特率等详细信息
message VideoPackage {

    enum Codec {
        UNKNOWN1 = 0;
        HEVC = 1;
        H264 = 2;
    }

    string file_path = 1; // 视频文件的本地地址
    uint64 file_size = 2; // 文件大小, 单位 bytes
    Codec codec = 3; // 文件编码参数
    uint64 duration = 4; // 视频长度，单位 ms
    float bitrate = 5; // 视频比特率
    string x264_params = 6; // H264参数

    uint32 video_width = 7; // 视频宽
    uint32 video_height = 8; // 视频高
    int32 skip_transcode_code = 9; // 跳过转码为0，不能跳过转码则为转码原因
}

message AtlasPackage {
    enum Type {
        UNKNOWN1 = 0;
        HORIZONTAL = 1; // 图集
        VERTICAL = 2; // 长图
        SINGLE= 3;//单图
    }

    Type type = 1; // 类型
    uint64 count = 2; // 总页数
    uint64 viewed_count = 3; // 浏览过的页数
}

// 本地音乐的原始参数
message LocalMusicPackage {
    string file_path = 1; // 音乐文件的原始地址
    uint64 file_size = 2; // 文件大小，单位bytes
    uint64 duration = 3; // 音乐长度，单位ms
    string title = 4; // 音乐名
    string artist = 5; // 音乐作者名
    string album = 6; // 音乐所属专辑名
}

message SoundEffectPackage {
    enum Type {
        UNKNOWN1 = 0; // 未知
        EMPTY = 1; // 无示例特效
        STUDIO = 2; // 录音棚
        KTV = 3; // KTV
        STAGE = 4; // 舞台
        CONCERT = 5; // 音乐会
        KID = 6; // 小孩特效
        UNCLE = 7; // 大叔特效
        CHORUS = 8; // 合唱
        CLASSIC = 9; // 经典
        POP = 10; // 流行
        HEAVY = 11; // 重低音
        REVERB = 12; // 混响
        BATHROOM = 13; // 浴室
        RECORD = 14; // 老唱片
        LIGHT = 15; // 悠远
        SUPER_STAR = 16; // 巨星
    }
    string name = 1; // 内容的名称
    uint32 reverb_level = 2; // 音效的级别
    Type type = 3; // 声音特效类型
}

message MessagePackage {

    enum Type {
        UNKNOWN1 = 0;
        LONG_CONNECTION = 1; // 长链接
    }

    enum AggregationType {
        UNKNOWN2 = 0;
        REWARD = 1; // 打赏
        COMMENT = 2; // 评论
        AT = 3; // @消息
        JOIN = 4; // 同框消息
        COMMENT_LIKE = 5; // 评论点赞
        LIKE = 6; // 作品点赞
        FOLLOW = 7; // 关注
        USE_MUSIC = 8; // 使用配乐
        INFORM = 9; // 戳消息
        TOKEN = 10; // 口令
    }

    enum Status {
        UNKNOWN3 = 0;
        LATEST = 1; // 最新
        READ = 2; // 已读
    }

    Type type = 1;
    string identity = 2; // 消息的 id
    bool aggregation = 3; // 是否聚合消息，true代表是，false代表否
    AggregationType aggregation_type = 4; // 聚合消息类型
    Status status = 5; // 消息状态
    string message_type = 6; // 消息类型
    string message_subtype = 7; // 上报消息子类型
    string relationship_type = 8; // 对于非聚合消息，上报消息触发者与消息接收者的关系链类型（比如消息“XXX评论了你”，这个XXX是消息触发者，你是消息接收者）
    string has_relationship_name = 9; // 如果消息触发者和消息接受者具有关系链类型，则上报能否取到该关系链类型对应的名字
    string story_id = 10; // story id
    string author_id = 11; // 作者id
}

message GiftPackage {
    enum Type {
        UNKNOWN1 = 0;
        NORMAL = 1; // 普通礼物
        DRAWING_GIFT = 2; // 手绘礼物
        MAGIC_GIFT = 3; // 魔法表情礼物
        GIFT_WHEEL_GRASS =4;//礼物转盘幸运草礼物
        GIFT_WHEEL_PROP=5;//礼物转盘抽中道具礼物
    }

// 礼物面板弹起的来源(原因)
    enum GiftBoxSourceType {
        UNKNOWN = 0; // 未知
        DISTRICT_RANK = 1; //地区小时榜、废弃(用24、25)
        NOMAL = 2 ; //右下角普通送礼入口
        VOICE_PARTY_PERSONAL_CARD = 3 ;//聊天室个人卡片送礼入口
        VOICE_PARTY_MIC = 4; //聊天室麦序位
        VOICE_PARTY_EXCLUSIVE_GIFT = 5; //聊天室专属礼物
        VOICE_PARTY_CONTRIBUTIOIN_LIST = 6; //聊天室贡献榜送礼支持
        TOP_USER_LIST_SEND_GIFT_HINT = 7; //在线观众面板送礼引导
        PK_FIRST_BLOOD = 8; //pk首杀
        TO_LIGHT_UP_BUTTON = 9;//勋章熄灭去点亮按钮
        USER_PANEL_BOTTOM_BAR=10;//在线观众面板-底栏"去送礼"按钮
        TOP_PRIVILEGE_PANEL_BOTTOM_BAR=11;//榜一特权介绍面板-底栏"去送礼"按钮
        USER_PANEL_WEEK_BOTTOM_BAR=12;//在线观众面板-周榜底栏"去送礼"按钮
        TOP_PRIVILEGE_PANEL_WEEK_BOTTOM_BAR=13;//周榜特权介绍面板-底栏"去送礼"按钮
        PEAK_NIGHT_RANK_HELP_BUTTON=14;//巅峰之夜榜单助力按钮
        PEAK_NIGHT_TOP_CARD=15;//巅峰之夜顶部卡片入口
        LIVE_AUDIENCE_QUESTION_ENTRANCE=16;//直播间观众提问入口
        LIVE_GIFT_RED_PACKET_PANEL=18;//直播间礼物红包弹层-点击送礼
        LIVE_RED_PACKET_RAIN_GAIN_SUCCEED_CARD=19;//直播间红包雨抢到红包礼物弹层-点击送主播
        LIVE_PK_GIFTS_START_CARD = 20;//礼物暴击开始
        LIVE_GAME_RANK_LIST_WEEK = 21; //游戏直播间-排行榜-贡献周榜
        LIVE_GAME_RANK_LIST_FANS = 22; //游戏直播间-排行榜-粉丝榜
        LIVE_WISHLIST = 23; //心愿单面板
        LIVE_HOURLY_NATIONAL_RANK_LIST = 24; //直播间小时榜-全国榜
        LIVE_HOURLY_DISTRICT_RANK_LIST = 25; //直播间小时榜-地区榜
        LIVE_FANS_GROUP_TASK_CARD = 26; //粉丝团任务面板
        LIVE_AUDIENCE_CHAT_PROSONAL_CARD_SEND_GIFT = 27;//连麦用户个人卡片送礼入口
        LIVE_AUDIENCE_CHAT_ACHIEVEMENT_RANK_SEND_GIFT = 28;//连麦用户贡献榜送礼入口
        LIVE_DRAW_GIFTS_PANEL = 29; //直播间礼物绘制面板
        LIVE_HOURLY_APPEARANCE_RANK_LIST = 30; //直播间小时榜-颜值榜
        LIVE_HOURLY_TALENT_RANK_LIST = 31; //直播间小时榜-才艺榜
        LIVE_ELECTRICITY_TRUST_CARD_SEND_GIFT_TASK = 32; // 电商半屏信任卡去送礼任务
        LIVE_COMMON_RED_PACK_RESULT_PANE = 33;//普通快币红包
        LIVE_ARROW_RED_PACK_RESULT_PANEL = 34;//穿云箭红包
        LIVE_HOURLY_GZONE_RANK_LIST = 35;//游戏小时
        LIVE_GZONE_KSHELL_RANK = 36;//游戏直播间猫粮榜
        LIVE_COMMENT_FEED_AREA = 37; //评论区高频行为 引导送礼
        LIVE_CHEAP_CARD=38;// 小礼物送礼提示弹窗
        LIVE_AUDIENCE_LIMIT_TIME_TASK_CARD=39;// 观众端限时任务卡片
        LIVE_AUDIENCE_LIMIT_TIME_TASK_BUTTON=40;// 观众端限时任务底部icon入口
        LIVE_HOURLY_POPULARITY_RANK_LIST=41;//直播间小时榜-人气榜
        LIVE_TAKE_A_SHOT_POPOP_SEND_AUTHOR=42;//拍一拍弹窗-送给主播按钮（废弃）
        LIVE_PK_INFORMATION_CARD=43;//pk信息条（pk段位赛引入）
        LIVE_MAKE_KWAI_COIN = 44;//观众观播任务页面 引导送礼
        NEBULA_H5_PAGE_TASK_CENTER = 45; //首页侧边栏h5任务页，点击跳转直播间自动打开送礼面板(仅快手极速版)
        SEND_GIFT_CARD_GIFT_BUTTON =46;//礼物槽位礼物按钮
        LIVE_ARROW_CONDITICON_RED_PACK_RESULT_PANEL=47;//穿云箭条件红包结果页
        AUDIENCE_PANEL_LIVE_MEMBER_BOTTOM_BAR=48;//在线观众面板-间内会员榜单底栏"去送礼"按钮
        LIVE_COMMENT_NOTICE=49;//直播间评论区强消息提醒
        AUDIENCE_RANK_PANEL_LIST_EFFECT=50;//在线观众面板-上榜特效点击抢榜x按钮调起礼物面板
        MORE_COMMENT_CHEAP_GIFT_BUTTON=51;//底部栏小礼物-高频评论推荐礼物
        CONVERGENCE_PK_RANK_HELP_BUTTON=52;//凝聚力pk榜单去助力按钮
        MULTI_PK_HELP=53;//多主播pk助力按钮
        MULTI_LINE_WISH_GIFT=54;//多主播连线礼物大作战标签
        LIVE_LITE_GIFT_BUTTON=55;//lite简易直播间吊起礼物面板送礼按钮
        LITE_SEND_GIFT_CARD_GIFT_BUTTON =56;//LITE直播间礼物槽位礼物按钮
        LITE_CHEAP_GIFT=57;//lite简易直播间快捷礼物
        LIVE_GZONE_KING_RUNE = 58; // 游戏直播王者铭文同步后送礼
        LIVE_BOTTOM_PENDANT_SEND_GIFT_BUTTON = 59;//直播间右下角挂件点击后送礼
        LITE_AUDIENCE_PANEL_LIVE_MEMBER_BOTTOM_BAR = 60;//LITE在线观众面板-间内会员榜单底栏"去送礼"按钮
        LITE_HIGH_GUIDE_GIFT=61;//lite直播间高频引导送礼
        LITE_QUICK_ACTIVITY_GIFT=62;//lite直播间快捷互动送礼
        LIVE_GIFT_ACHIEVEMENT_SEIZE_TITLE_BUTTON=63;//礼物成就墙页面-夺下冠名弹窗夺下冠名按钮点击
        LIVE_GIFT_ACHIEVEMENT_CONTINUE_SEND_BUTTON=64;//礼物成就墙页面-继续赠送弹窗继续赠送按钮点击
        LIVE_GIFT_ACHIEVEMENT_STRONG_NOTICE_BUTTON=65;//礼物成就墙-观众端直播间争夺冠名强消息提醒点击
        LIVE_SHARE_RED_PACK_RESULT_PANEL=66;//分享红包结果页-送主播礼物
        LIVE_FANS_GROUP_RED_PACK_RESULT_PANEL=67;//粉丝团红包结果页-送主播礼物
        LIVE_H5_PAGE_COMMON=68;//直播间活动h5页面吊起礼物面板
        LIVE_DATE_GUIDE_SEND_POPUP_CHAT=69;//直播间相亲引导-上麦送礼弹窗
        LIVE_DATE_GUIDE_SEND_POPUP_THANK_ANTHOR=70;//直播间相亲引导-感谢红娘送礼弹窗
        LIVE_COMMENT_SECTION_SEND_GIFT=71;//普通直播间评论区送礼消息-点击礼物名称吊起礼物面板 
        LITE_COMMENT_SECTION_SEND_GIFT=72;//LITE直播间评论区送礼消息-点击礼物名称吊起礼物面板
        LITE_CHEAP_GIFT_S=73;//lite简易直播间快捷礼物二号位
        LITE_FANS_GIFT_S=74;//lite简易直播间粉丝团礼物位置
        LIVE_COIN_TASK=75;//肝帝任务-直播间内h5送礼任务-包含lite
        LITE_QUICK_GIFT_WATCH_OR_LIKE=76;//lite快捷互动送礼-互动时长触发
        LITE_QUICK_GIFT_PK=77;//lite快捷互动送礼-pk暴击触发
        LIVE_TASK_RED_PACKET=78;//任务红包红包结果页-送主播礼物
        LIVE_TASK_RED_PACKET_TASK_PAGE=79;//任务红包抢红包页-送票抢红包
        LIVE_INTERACTIVE_GAMEPLAY_QUICK_SEND=80;//间内互动玩法-快捷送礼按钮
        HEAT_PROGRAMME_WANT=81;//互动节目单想看按钮掉起礼物面板
        LIVE_COIN_TASK_ADD=82;//肝帝任务-直播间内h5补签-包含lite
        LIVE_BOTTOM_SEND_TOGETHER_BUTTON=83;//氛围礼物-底部一起送按钮
        VOICE_PARTY_CONTRIBUTIOIN_TEAMPK_LIST=84;//聊天室团战贡献榜送礼支持
        LIVE_POPULAY_TICKET_POPUP_SEND_PAID=85;//人气票送礼引导弹窗送出付费票
        VOICE_PARTY_CONTRIBUTIOIN_GUESTPK_LIST=86;//聊天室九宫格贡献榜送礼支持
        LIVE_INSTANT_DEATH_GIFT_RED_PACKET=87;//秒杀类红包-礼物红包结果页-送主播礼物
        LIVE_INSTANT_DEATH_KWAI_COIN_RED_PACKET=88;//秒杀类红包-快币红包结果页-送主播礼物
        LIVE_DATE_AUTO_SEND_POPUP_THANK_ANTHOR=89;//直播间相亲引导-感谢红娘送礼自动弹出弹窗
        LIVE_GIFT_ACHIEVEMENT_OWN_IAMGE_PAGE=90;//礼物成就馆-专属形象礼物页送礼
        LIVE_PAY_USER_TASK_H5=91;//付费用户-任务体系页新增任务
        LIVE_GIFT_NEW_ACTIVITY_SEND_ALL=92;//礼物上新活动-送全套
        R4ORAIN_QUICK_SEND=93;//24CNY-红包雨跳转直播送礼
        LIVE_COUPON_TASK_CARD=94;//任务卡片-发快币弹窗送礼
        WALLET_PAGE_KWBI_COIN_TICKET=95;//24CNY-钱包页使用快币券
        WISH_SEND=96;//24CNY_祈福直播间扣费
        JINBI_TASK_BUTTON=97;//做任务领金币页送礼物按钮
        JINBI_TASK_POPUP=98;//做任务领金币页送礼物弹窗
        BATTLE_PAY_TASK=99;//24CNY-打投会场任务吊起
        BATTLE_USER_PAGE_BUTTON=100;//24CNY-打投会场投票按钮吊起
        PK_TASK_SYSTEM=101;//pk彩蛋卡片
        LIVE_POPULAR_RANK_BOTTOM_BUTTON=102;//人气榜底部为他助力按钮
        SHOW_PARTY_DOUBLE_BOTTOM=103;//团播工具礼物pk时，底部双快捷礼物按钮
        SHOW_PARTY_PK_ITEM=104;//团播工具礼物pk时，pk条送礼
        SHOW_PARTY_RANK=105;//团播工具团员榜单送礼
        SHOW_PARTY_RANK_QUICK=106;//团员工具团员榜单快捷区送礼
        SHOW_PARTY_GIFT_COUNT=107;//团员工具礼物计票器,榜单送礼
        LIVE_FIRST_SEND_POPULAR_TICKET_PENDANT=108;//人气票首送强化态挂件
        LIVE_PAY_BOTTOM=109;//付费投票底部按钮
        SHOW_PARTY_CONTRIBUTE_LIST_SEND_GIFT=110;//团播观众贡献榜底部送礼按钮
        LIVE_PK_RED_PACKET_SEND_GIFT=111;//pk任务红包送礼
        SHOW_PARTY_GROUP_SEND=112;//团员工具成组送礼
        LIVE_GIFT_PANEL_GIFT_DETAIL_H5=113;//礼物详情h5页送礼（包含：童话日记等礼物）
        LIVE_RECHARGE_PAY_SEND_GIFT=114;//礼物面板送礼余额不足充值后自动送礼
        LIVE_WZ_GAME_CARD=115;//游戏信息条打开礼物面板
        LIVE_POPULAY_RANK_HONER=116;//人气荣誉墙页面助力按钮
        SHOW_PARTY_CONTRIBUTE_LIST_SOLOPK_SEND_GIFT=117;//团员pk贡献榜-solopk
        SHOW_PARTY_CONTRIBUTE_LIST_BRAWLPK_SEND_GIFT=118;//团员pk贡献榜-大乱斗pk
        LIVE_DAPING_NOTICE=119;//直播收礼工具-用户端强提醒
        SHOW_PARTY_CONTRIBUTE_LIST_RANK_SEND_GIFT=120;//团员排名-观众贡献榜送礼按钮
        SHOW_PARTY_CONTRIBUTE_LIST_SOLOPK_QUICK=121;//团员pk快捷送礼-solopk
        SHOW_PARTY_CONTRIBUTE_LIST_BRAWLPK_QUICK=122;//团员pk快捷送礼-大乱斗pk
        LIVE_COMMENT_REPRESS_PAY_POPUP=123;//评论区点击表情吊起梗评论开通弹窗-打开礼物面板
        LIVE_COMMENT_BUTTON_PAY_POPUP=124;//底部评论按钮吊起梗评论开通弹窗-打开礼物面板
        LIVE_GONGGE_RANK=125;//宫格榜单点击助力打开礼物面板
        LIVE_TUANBO_RANK=126;//团播榜单点击助力打开礼物面板
        SHOW_PARTY_CONTRIBUTE_LIST_LIMIT_TASK_SEND_GIFT=127;//团员pk贡献榜-限时任务
        LIVE_SUPER_SEND_GIFT_NOTICE=128;//直播新送礼触达容器
        GRID_PROGRAM_LIST_ORDER=129;//宫格-节目单点才艺
        LIVE_PK_JIANFEN_GIFT=130;//pk减分时刻模块点击吊起礼物面板
        LIVE_PAY_GROUP_SEND_CARD=131;//付费投票成组送
        LIVE_SPRING_ACTIVCITY_MUDAN_H5=132;//春日活动-花香牡丹礼物介绍详情页
        LIVE_TEAM_MEMBER_PANEL_GIFT_BUTTON=133;//本场团员面板送礼按钮
        LIVE_POPULAR_VOTE_HELP=134;//免费人气票获取临时指示器半屏弹窗
        LIVE_ACTIVITY_GIFT_H5_YULIU_yi=135;//直播营收活动礼物h5-吊起礼物面板统一位置1
        LIVE_ACTIVITY_GIFT_H5_YULIU_er=136;//直播营收活动礼物h5-吊起礼物面板统一位置2
        LIVE_JINBI_TASK_YIQI_CHANGQIAN_TASK=137;//金币长签任务一期-点击送礼任务吊起礼物面板
        LIVE_JINBI_TASK_ERQI_JIANNEI_TASK=138;//金币间内任务二期-点击送礼任务吊起礼物面板
        LIVE_JINBI_TASK_REWARD_POPUP=139;//直播间金币打卡任务发奖弹窗
        LIVE_GIFT_NEW_PAGE=140;//礼物上线页吊起礼物面板
        LIVE_JILI_TASK_EXCHANGE_KWAI_COIN=141;//直播激励任务兑换快币成功页吊起礼物面板
        LIVE_LIANPING_HEYAN_GONGXIANRANK_SEND=142;//连屏合演贡献榜单底部送礼按钮吊起礼物面板
        LIVE_LIANPING_HEYAN_ITEM_SEND=143;//连屏合演列表送礼按钮吊起礼物面板
    }

    enum GiftEntryType {
        UNKNOWN2 = 0; // 未知
        DIRECT_SEND_GRASS = 1; //直接购买幸运草
        SEND_GRASS_AND_LOTTERY = 2; //赠送幸运草并抽奖
        RELIGHT_FANS_GROUP_GIFT = 3; //重新点亮粉丝团礼物
        PANEL_SEND_GIFT = 4; //礼物面板送礼
        SEND_CHEAP_GIFT_DIALOG=5;//送礼确认弹窗（覆盖外漏小礼物、礼物面板、评论区等吊起礼物确认弹窗场景）
        CHEAP_GIFT_BUTTON=6;//小礼物按钮
        SANDEAPY_GIFT_BUTTON=7; //闪电拍按钮
        WISHLIST_GIFT_BUTTON = 8;//心愿单送礼按钮
        WALL_THUMP_COMMENT_NOTICE = 9; //一元壁咚消息提醒
        FANS_GROUP_PANEL_SEND_GIFT = 10; //粉丝团面板送礼
        JOIN_FANS_GROUP_DIALOG = 11;//加入粉丝团弹窗
        GZONE_INTERACTION_SEND_GIFT_COMMENT_NOTICE = 12; //游戏直播间破冰送礼提醒
        GUARDIAN_GIFT_PANEL_NOTICE = 13; //守护面板送礼提醒
        LIVE_COMMENT_NOTICE_SEND_GIFT_BUTTON = 14; //强消息卡片送礼
        LIVE_HOURLY_RANK_LIST=15;//直播间小时榜
        LIVE_POPULARITY_BOOST_CARD=16;//人气助力面板
        LIVE_CRIT_TIME_HIGH=17;//PK暴击时刻-高档位
        LIVE_CRIT_TIME_LOW=18;//PK暴击时刻-低档位
        LIVE_STEAL_TOWER_TIME_HIGH=19;//PK偷塔时刻-高档位
        LIVE_STEAL_TOWER_TIME_LOW=20;//PK偷塔时刻-低档位
        LIVE_CONTIMUOUS_SEND_GIFT_BUTTON=21;//连送按钮（春节礼物玩法-蓄力礼物）
        LIVE_CNY_COMMENT=22;//新春主题评论
        LIVE_RED_PACKET_PANEL=23;//直播间红包封皮
        LIVE_MORE_COMMENT_CHEAP_GIFT_BUTTON=24;//底部栏小礼物-高频评论推荐礼物
        LIVE_CONVERGENCE_PK_RANK_HELP_BUTTON=25;//凝聚力pk榜单去助力按钮
        LIVE_GZONE_BLIND_BOX_COMMENT_NOTICE_BUTTON = 26; //游戏直播盲盒强提醒送礼按钮
        LIVE_LITE_CHEAP_GIFT = 27;//lite简易直播间快捷礼物
        LIVE_BOTTOM_PENDANT_SEND_GIFT=28;//直播间右下角挂件点击后进行送礼；
        LIVE_LITE_HIGH_GUIDE_GIFT=29;//lite直播间高频引导送礼
        LIVE_LITE_QUICK_ACTIVITY_GIFT=30;//lite直播间快捷互动送礼
        LIVE_DIY_GIFT=31;//直播间diy礼物送出
        LIVE_DATE_GUIDE_SEND_POPUP=32;//直播间相亲引导送礼弹窗（上麦、感谢红娘等送礼引导）
        LIVE_DINGZHI_GIFT_BUY=33;//定制礼物购买
        LIVE_LITE_CHEAP_GIFT_S = 34;//lite简易直播间快捷礼物二号位
        LIVE_LITE_FANS_GIFT_S = 35;//lite简易直播间粉丝团礼物位置
        LIVE_LITE_QUICK_GIFT_WATCH_OR_LIKE=36;//lite快捷互动送礼-互动时长触发
        LIVE_LITE_QUICK_GIFT_PK=37;//lite快捷互动送礼-pk暴击触发
        LIVE_INTERACTIVE_GAMEPLAY_QUICK_SEND_GIFT=38;//互动玩法快捷送礼区
        LIVE_BOTTOM_SEND_TOGETHER=39;//氛围礼物-底部一起送
        LIVE_POPULAY_TICKET_SEND_POPUP=40;//人气票送礼引导弹窗
        LIVE_MAIN_FAN_SIGNAL_DEVICE=41;//主粉号令器
        LIVE_R4ORAIN_QUICK_SEND=42;//24CNY-红包雨跳转直播送礼
        LIVE_PAI_POPUP=43;//拍一拍弹窗送礼
        LIVE_WISH_SEND=44;//24CNY_祈福直播间扣费
        LIVE_JINBI_TASK=45;//金币任务页面
        DANMU_FREE_GIFT_BUTTON=46;//弹幕直播间底部免费礼物领取按钮
        LIVE_STRENGTHEN_NOTICE=47;//直播间强化态通知-直接送礼
        LIVE_SHOW_PARTY_DOUBLE_BOTTOM=48;//礼物pk时，底部双快捷礼物按钮
        LIVE_SHOW_PARTY_RANK_QUICK=49;//团员榜单-快捷送礼
        LIVE_SHOW_PARTY_GIFT_COUNT=50;//礼物计数器-快捷送礼
        LIVE_PK_TASK_BOTTOM_BUTTON=51;//pk任务系统底部快捷送礼区域
        LIVE_PAY_VOTE_GIFT=52;//直播付费投票送礼
        LIVE_GAMEPLAY_SEND_GIFT_POPUP=53;//游戏直播间高光时刻送礼
        LIVE_GAME_SEND_AREA=54;//游戏直播底部道具送礼区域
        LIVE_OPEN_TREASURE_BOX_POP_UP=55;//开宝箱送礼弹窗
        LIVE_GRID_PROGRAM_LIST_ORDER=56;//宫格-节目单点才艺
        LIVE_GIFT_STICKERS=57;//礼物贴纸点击送礼
    }

    Type type = 1; // 该类型字段废弃，礼物类型
    string identity = 2; // 礼物Id
    uint32 position = 3; // 礼物所在的位置
    uint64 magic_face_id = 4; // 魔法表情礼物的 id
    uint32 total_count = 5; // 礼物个数
    bool is_drawing_gift = 6; // 是否为手绘礼物
    bool is_packet_gift = 7; // 是否为背包礼物
    bool is_combo_send = 8; // 是否为连送发送
    bool to_anchor = 9;  // 是否发送给主播
    GiftBoxSourceType source_type = 10; //礼物弹窗的来源
    bool is_local_has_material = 11; //本地是否有该魔法礼物素材
    string local_high_definition_materials = 12; //本地礼物高清资源素材id集合，数组类型
    GiftEntryType gift_entry_type =13;//送礼入口类型
}

message PaymentPackage {
    enum Provider {
        UNKNOWN1 = 0;
        BAIDU = 1; // 百度支付
        WECHAT = 2; // 微信支付
        ALIPAY = 3; // 支付宝支付
        IAP = 4; // 苹果 IAP 支付
    }

    string identity = 1; // 支付订单的Id
    Provider provider = 2; // 支付方式
    string currency = 3; // 币种
}

message CommentPackage {
    enum RecallType {
        UNKNOWN_RECALL = 0;
        LIKED = 1; // 点赞数超过阈值且过人审
        OTHER = 2; // 其他置顶策略
    }

    enum CommentTag {
        NONE = 0; //无标签
        SELECT = 1; //精选
        AUTHOR = 2; //作者
        AUTHOR_LIKED = 3; //作者喜欢
        FOLLOWING = 4; //关注
    }

    string identity = 1; // 评论的 id
    string reply_identity = 2; // 该条评论回复的评论的 id
    bool pasted = 3; // 是否是粘贴的评论
    string author_id = 4; // 评论的作者id
    bool hot = 5; // 是否为热门评论，true代表是，false代表否
    bool child_comment = 6; // 是否为子评论，true代表是，false代表否
    uint32 index = 7; // 评论的位置
    uint32 child_comment_count = 8; // 子评论条数，仅一级评论时上报
    RecallType recall_type = 9; // 评论召回策略（服务端透传）(废弃)（服务端返回值多变，所以定义新的string参数）
    string recall_type_new = 10; // 评论召回策略（服务端透传）
    string tag_type = 11; // 评论标识类型
    uint64 show_length = 12; // 评论曝光时长，单位毫秒
    bool is_first_show = 13; // 是否直接曝光，true代表是，false代表否
    string reply_author_id = 14; // 该条评论回复的评论的作者id
    uint32 at_user_cnt = 15; // 该条评论@好友的个数
    bool is_edited = 16; // 夸夸评论是否被编辑过
    uint32 edit_status = 17; // 区分非夸夸评论、夸夸评论未编辑、夸夸评论编辑三种状态
    reserved 18; //原评论表情id字段类型不正确，废弃掉
    string emotion_id = 19; //评论包含的表情id
    string emotion_biz_type = 20; //服务端的表情来源类型 6 -第三方表情 闪萌；7 - 外部表情转换得到的内部表情
    bool god = 21; // 是否为神评论，true代表是，false代表否
    string comment_mark = 22; //评论戳，HOT:热门戳，GOD:神评戳
    string comment_user_label = 23; //评论用户标签，多个标签通过&连接
    bool down_comment = 24; // 是否为下沉评论，true代表是，false代表否
    string sever_params=25;//用于记录sever下发的字段，是一个json格式
    bool hidden_comment = 26; // 是否为折叠评论，true代表是，false代表否
    string author_head_info=27;//评论用户的头像信息，记录外显作者的加v标识
    CommentTag comment_tag =28;//评论所带的标签
    bool if_have_reply_button = 29; //是否带有回复按钮，true代表是，false代表否
    string click_position = 30; //点击位置
    string comment_tab = 31;//评论所属分tab
    bool is_head_pendant = 32;// 评论用户头像是否有挂件，true代表是，false代表否
    string picture_id = 33; //评论包含的图片id
    string picture_biz_type = 34; //服务端的图片来源类型
    bool is_picture_slide = 35; //是否上下滑曝光图片评论，true代表是，false代表否
    string comment_type = 36; // 评论类型，用于区分单条评论包含的内容如文字、图片、emoji等，json格式。
    string comment_photo_id = 37; //评论的视频id
    string comment_photo_source = 38; //评论的视频来源，客户端透传文案
    string reco_cmt_info = 39; //reco对于评论的下发策略信息
    string image_num = 40; //为图片评论的时候进行上报，大于1时为多图，不为图评时不上报该参数
    string image_type_num = 41; //上报所有图片类型的数量，其中区分静图和live

}

message DanmakuPackage {
    enum DanmakuType{
        UNKNOWN1 = 0; //未知类型
        COMMENT = 1; //评论类型
        NORMAL = 2; // 普通发布类型
        PLUS = 3; //+1类型
        PRESET = 4; //预置词类型
        STICKER = 5; //贴纸类型
        INSTILL = 6; //运营灌入类型
    }

    string danmaku_id = 1; // 弹幕id
    string danmaku_user_id = 2; // 该条弹幕发布作者uid
    bool highcopy = 3; // 是否高+1
    bool highlike = 4; // 是否高点赞
    uint32 danmaku_body_count = 5; // 弹幕内容字数   
    string server_params = 6; // 拓展字段，供服务端下发
    uint32 danmaku_show_num = 7; //弹幕曝光次数
    uint64 danmaku_show_duration = 8; //弹幕曝光时长，单位ms
    DanmakuType danmaku_type = 9; //弹幕类型
    string danmaku_color_id = 10; // 弹幕颜色id

}   

message SearchResultPackage {
    enum ContentType {
        UNKONWN1 = 0;
        USER = 1; // 用户搜索
        MUSIC_TAG = 2; // 音乐标签（标签的一种类型）
        TOPIC_TAG = 3; // 话题标签（标签的一种类型）
        POI_TAG = 4; // 地址标签（标签的一种类型）
        MAGIC_FACE_TAG = 5; // 魔法表情标签（标签的一种类型）
        MUSIC = 6; // 音乐（结果类型，区分于用户、标签）
        PHOTO = 7; // 短视频
        LIVE_STREAM = 8; // 直播
        GIF_STICKER = 9; // GIF贴纸
        CREATIVITY = 10; // 创意标签
        MOMENT = 11; // 说说
        PERSONAL_CHAT = 12; // 单聊
        GROUP_CHAT = 13; // 多人聊天
        PUBLIC_GROUP_CHAT = 14; // 公开群聊
    }

    enum Type {
        UNKONWN2 = 0;
        RECOMMEND = 1; // 推荐
        SEARCH = 2; // 搜索
        ASSOCIATIVE_WORD = 3; // 联想词
        TRENDING_WORD = 4; // 热词
        SEARCH_PUSH = 5; // push引导
        SEARCH_SILENT = 6; // 静默搜索词
        SEARCH_GUESS = 7; // 猜你想搜
        SEARCH_HISTORY = 8;//搜索历史词
    }

    string content_id = 1; // 内容唯一标识
    uint32 position = 2; // 结果在列表的位置
    ContentType content_type = 3; // 搜索的内容类型
    string keyword = 4; // 搜索关键词
    Type type = 5; // 查找页曝光类型
    string name = 6; // 记录tag或者用户或者音乐的名称
    string exp_tag = 7; // 推荐后端的参数
    string llsid = 8; // 曝光id
    uint32 count = 9; // 记录用户或者标签包含的视频量;
    repeated PhotoPackage photo_package = 10; // 曝光的视频id和作者id
    string music_type = 11; // 音乐类型参数
    bool allow_to_collect = 12; // 是否允许被收藏
    string secondary_type = 13; // 二级分类，用于记录tab下二级类目
    bool follow_user = 14; // 是否是关注用户
    repeated IMPersonalSessionPackage im_personal_session_package = 15; // 曝光的私信单聊信息
    repeated IMGroupSessionPackage im_group_session_package = 16; // 曝光的私信群聊信息
    uint64 show_time = 17;//搜索结果的真实曝光时间毫秒
}

// 海外专用，主站请勿使用
message ThirdPartyRecommendUserListItemPackage {
    enum Source {
        UNKNOWN1 = 0;
        CONTACTS = 1; // 通讯录联系人
        FACEBOOK = 2; // facebook好友
        TWITTER = 3; // twitter好友
        QQ = 4; // QQ好友
        WEIBO = 5; // 新浪微博好友
        VK = 6; // VK好友
    }

    Source source = 1; // 社交平台
    string user_id = 2; // 本条用户id
    uint64 position = 3; // 处于推荐列表的绝对位置
}

// 海外专用，主站请勿使用
message BannerPackage {
    string identity = 1; // BannerId
}

message ProfilePackage {
    enum Style {
        UNKNOWN1 = 0;
        GRID = 1; // 横排
        VERTICAL = 2; // 竖排
    }

    enum Tab {
        UNKNOWN2 = 0;
        PHOTO = 1; // 作品
        PRIVACY = 2; // 私密
        LIKE = 3; // 喜欢
        MUSIC = 4; // 音乐
        MOMENT = 5; // 说说
        COLLECT = 6; // 收藏
        AT = 7; // @功能
        ARTICLE = 8; //长文
        AD_BUSINESS_CUSTOM = 9; //商家号自定义模块类型tab
        PYMK=10;//profile页推荐区域
        SHOP=11;//商家
        LIVE_PLAY_BACK=12;//直播回放
        MAGIC_FACE=13;//魔法表情
        ACFUN=14;//A站
        ALBUM=15;//相册
        COMMODITY=16;//商品
        LONG_VIDEO=17;//长视频
        NEWS=18;//动态
        AT_ME = 19;//被@
        CREATION=20;//创作
	BAMBOO_NOTE = 21;//创作者app笔记
        FLOW_PLAYLET = 22;//短剧
        RECOMMEND = 23; // 推荐
        COMMENT = 24; //评论
        HOUSE = 25; //房产&泛行业
        RECRUIT = 26; // 快聘
    }

    string visited_uid = 1; // 被访问用户的uid
    Style style = 2; // 作品展示方式
    Tab tab = 3; // tab类型
}

message ThirdPartyBindPackage {
    ThirdPartyPlatform platform = 1; // 第三方账号绑定
}

message LoginSourcePackage {
    enum Source {
        UNKNOWN3 = 0;
        IMPORT = 1; // 导入到快手
        THREE_DIMENSION_TOUCH_SHOT = 2; // iOS 3D touch点击拍摄
        RE_LOGIN = 3; // 重新登录，如账号异常等
        HOME_LOGIN_BUTTON = 4; // 首页登录按钮
        HOME_VIEW_LIVE_FEED = 5; // 首页查看直播FEED
        HOME_FOLLOW_RECOMMEND_USER = 6; // 首页关注推荐的用户
        FEED_DETAIL_REPLY_COMMENT = 7; // 作品详情页回复某条评论
        FEED_DETAIL_COMMENT_FEED = 8; // 作品详情页评论FEED
        FEED_DETAIL_REPORT_COMMENT = 9; // 作品详情页举报某条评论
        FEED_DETAIL_AT_USER = 10; // 作品详情页评论时@其他用户
        FEED_DETAIL_REPORT_FEED = 11; // 作品详情页举报作品
        FEED_DETAIL_CHANGE_FEED_VISIBILITY = 12; // 作品详情页更改FEED为隐私或所有人可见
        FEED_DETAIL_DELETE_FEED = 13; // 作品详情页删除作品
        FEED_DETAIL_FOLLOW_USER = 14; // 作品详情页关注用户
        FEED_DETAIL_REDUCE_SIMILAR_FEED = 15; // 作品详情页减少类似作品
        FEED_DETAIL_SHARE = 16; // 作品详情页分享
        FEED_DETAIL_BLACK_LIST = 17; // 作品详情页加入黑名单
        FEED_DETAIL_LIKE = 18; // 作品详情页点赞
        FEED_DETAIL_UNLIKE = 19; // 作品详情页取消点赞
        FANS_LIST_FOLLOW = 20; // 粉丝列表页关注用户
        LIKER_LIST_FOLLOW = 21; // 在对某作品点赞的用户列表页关注某用户
        LOCAL_ALBUM_DETAIL_SHARE = 22; // 本地作品集详情分享
        RECOMMEND_USERLIST_FOLLOW = 23; // 推荐用户列表页关注用户
        PROFILE_SEND_MESSAGE = 24; // PROFILE页发私信
        PROFILE_VIEW_LIVE_FEED = 25; // PROFILE页查看直播FEED
        PROFILE_FOLLOW = 26; // PROFILE页关注用户
        PROFILE_LIKE = 27; // PROFILE页点赞
        PROFILE_UNLIKE = 28; // PROFILE页取消点赞
        PROFILE_SHARE_FEED = 29; // PROFILE页分享作品
        PROFILE_SHARE_USER = 30; // PROFILE页分享用户
        PROFILE_REPORT = 31; // PROFILE页举报用户
        PROFILE_BLACK_LIST = 32; // PROFILE页加入黑名单
        PROFILE_DELETE_FEED = 33; // PROFILE页删除作品
        PROFILE_CHANGE_FEED_VISIBILITY = 34; // PROFILE页更改FEED为隐私或所有人可见
        PROFILE_REPORT_FEED = 35; // PROFILE页举报作品
        PREVIEW_FINISH = 36; // 预览页点击完成按钮
        LIVE_AUDIENCE_SHARE = 37; // 观众分享直播
        LIVE_AUDIENCE_COMMENT = 38; // 直播观众发布评论
        LIVE_AUDIENCE_LIKE = 39; // 直播观众点赞
        LIVE_AUDIENCE_FOLLOW = 40; // 直播查看其他观众的个人信息点关注
        LIVE_AUDIENCE_AT = 41; // 直播查看其他观众的个人信息点@ 
        LIVE_ANCHOR_FOLLOW = 42; // 关注直播主播
        LIVE_DEPOSIT = 43; // 直播送礼充值
        LIVE_SENT_GIFT = 44; // 直播送礼
        LIVE_CLOSED_ANCHOR_FOLLOW = 45; // 直播结束页关注主播
        LIVE_MORE_BACKLIST = 46; // 直播更多拉黑
        LIVE_MORE_NEGATIVE = 47; // 直播更多减少类似直播
        LIVE_MORE_INFORM = 48; // 直播更多举报
        HOME_VIDEO_BROWSE_LONG = 49; // 首页浏览视频达到一定时长
        PORTAL = 50; // 通过跳转协议打开一个需要登录的页面引发的登录请求
        JS_BRIDGE = 51; // web通过jsbridge调起登录流程
        FEED_DETAIL_HATE = 52; // 作品详情页踩
        FEED_DETAIL_UNHATE = 53; // 作品详情页取消踩
        THIRD_AUTH = 54; // 第三方授权登陆
        HOME_RED_PACK_BANNER_CLICK = 55; // 首页未登录状态下点击红包banner
        SF2018_LANDING_PAGE_LOADING = 56; // 春节活动落地页页加载时
        FEED_DETAIL_COMMENT_LIKE = 57; // 作品详情页评论点赞
        SAME_FRAME = 58; // 同框拍摄
        FEED_DETAIL_LIKE_COMMENT = 59; // 作品详情页条评论点赞 (与参数57重复，废弃)
        TAG_SHARE_CLICK = 60; // 标签详情页标签分享点击
        MUSIC_TAG_SINGER_FOLLOW = 61; // 音乐标签关注歌手
        PROFILE_MOMENT = 62; // 个人页动态
        TAG_MOMENT = 63; // 标签详情页动态
        FOLLOW_SHOOT = 64; // 跟拍
        NEARBY_HOT_SITE_SHOOT = 65; // 热点页点击拍摄
        PROFILE_SHOOT = 66; // profile页点击拍摄
        TAG_COLLECT_CLICK = 67; // 标签页点击收藏
        TAG_CAMERA_RECORD_CLICK = 68; // 标签页点击拍摄
        LIVE_RED_PACKET_RAIN = 69; // 直播红包雨
        HOME_TAB_CLICK = 70; // 首页tab点击
        KARAOKE_DUET = 71; // 合唱
        LIVE_WATCHING_LIST = 72; // 直播间观众列表
        FEED_DETAIL_BGM = 73; // 详情页配乐
        LIVE_KSHELL_GUESS = 74; // 直播快贝竞猜
        LIVE_VOTE = 75; // 直播间投票
        FEED_DETAIL_POST_ENTRANCE = 76; // 详情页生产入口
        H5_SEND_MESSAGE = 77; // H5发私信 的枚举值
        CLICK_ACTIVITE_REWARD_BUTTON_LOGIN = 78; // 点击活动领取进入
        GZONE_GAME_SUBSCRIBE = 79; // 游戏聚合页游戏订阅
        BOTTOM_MESSAGE = 80; // 底导点击消息
        BOTTOM_PROFILE = 81; // 底导点击我
        BOTTOM_SHOOT = 82; // 底导点击拍摄
        BOTTOM_SEARCH = 83; // 底导点击搜索
        FEED_FOLLOW_SHARE = 84; //feed流关注页点击分享
        CANCEL_ACCOUNT_BUTTON_LOGIN=85; // 取消注销账户跳转登录
        NEBULA_NEWUSER_POPUP=86;//极速版的登录引导红包弹窗
        NEBULA_TIMER=87;// 极速版的红包计时器
        ADD_ACCOUNT_IN_SETTING=88; // 添加账户在设置页
        ADD_ACCOUNT_IN_SWITCH_OR_ADD_ACCOUNT=89; // 添加账户在切换或添加账号页
        MUSIC_STATION_KWAI_VOICE_PENDANT=90; // 音悦台快手好声音挂件入口
        THANOS_FEED_HOT_POST_ENTRANCE = 91; // 滑滑板发现页生产入口
        THANOS_FEED_DETAIL_POST_ENTRANCE = 92; // 滑滑板详情页生产入口
        NEBULA_GENERAL_GUIDE_POPUP = 93; // 极速版新用户普通引导登录弹窗
        NEBULA_INVITE_CODE_GUIDE_POPUP = 94; // 极速版新用户邀请码引导登录弹窗
        SF2020_CURTAIN = 95; // 幕布
        SF2020_PENDANT = 96; // 挂件
        SF2020_UNPACK_RED = 97; //拆红包
        SF2020_FLASH_SCREEN = 98; //闪屏
        SF2020_SHARE_H5 = 99; //H5 分享
        SF2020_SHARE_TOKEN = 100; // 口令分享
        SF2020_LOOK_DIALOG = 101; //【去看看】弹窗
        SIGN_IN = 102; //冷启动
        SF2020_PICTURES_OF_FAMILY = 103; //全家福
        SF2020_PUSH = 104; //PUSH
        SF2020_THANKS_RED_PACK_SHARE = 105; //感恩红包
        SF2020_MILLION_RED_PACK_SHARE = 106; //老虎机
        SF2020_LIVE_REFLOW_DIALOG = 107; //直播回流
        SF2020_MY_RED_PACK_WALLET = 108; //红包页
        SF2020_LIVE_FOLLOW = 109; //2020春节直播间内关
        SF2020_PUSH_TO_RED_PACK = 110; //PUSH打开主人态红包
        SF2020_LIVE_QUIZ = 111; //直播答题
        LIVE_CHAT = 112;//直播连麦
        HOME_CAMERA_RECORD_CLICK = 113; // 未登录点击拍摄
        NASA_DISCOVER_SEARCH = 114; //大屏版-底导-发现页点击搜素框
        LOCAL_RANK_FOLLOW = 115; //同城榜单关注
        NASA_DISCOVER_CHANNEL = 116; //大屏版 - 底导发现 - 垂类
        NASA_DETAIL_POST_BTN = 117; //大屏版 - 详情页 - 拍摄按钮  点击拍摄按钮
        LIVE_VOICE_PARTY = 118; //直播聊天室
        VIDEO_PLAY_LANDING_GUIDE = 119; // 视频播放后引导登录
        FEED_STAY_LANDING_GUIDE=120; // 停留feed引导登录
        GUIDED_POPUP_SECOND = 121; // 未登录设备引导优化二期的弹窗登录
        GUIDED_STAR_PLAY_LIST=122;//明星打榜引导登陆
        SEARCH_ENTRANCE = 123; //未登录状态下在搜索入口之后的其他页面使用3min登录面板弹出
        popup_56yuan = 124; //56元红包弹窗引导登录
        LIKE_PHOTO_3_TIMES = 125; //点赞3次引导登录
        SAVE_TO_ALBUM = 126; //长按蒙层后点击保存到相册引导登录
        THIRD_OAUTH = 127; //第三方开放平台授权登陆调起快手app，未登陆时使用
    	COMMUNITY_GUIDE = 128; //话题圈首次引导，点击引导页标签时
        COMMUNITY_PAGE_PHOTO = 129; //话题圈首页点击作品相关操作（点赞、评论、分享等）
        COMMUNITY_TAG_PUBLISH = 130; //话题圈详情页，点击拍摄按钮
        COMMUNITY_TAG_JOIN = 131; //话题圈详情页，点击加入按钮
        COMMUNITY_TAG_PHOTO = 132; //话题圈具体话题页最近tag点击作品相关操作（点赞、评论、分享等）
        COMMUNITY_LIST_MY_JOINED = 133; //话题圈列表，从热门话题圈切换到我加入的
        COMMUNITY_FRIEND_FOLLOW = 134; //圈友列表点击关注
        COMMUNITY_PAGE_CLICK_TAG = 135;  //话题圈页，点击具体的话题
        COMMUNITY_PAGE_VIEW_MORE_TAG = 136;   //话题圈页，点击查看更多话题圈
        SOGAME_PAGE_LOGIN = 137;  //小游戏提示引导登录
        COMMUNITY_PAGE_PUBLISH = 138; //话题圈首页，点击拍摄按钮
        NATIONAL_DAY_NEARBY_SHAKE = 139; //国庆同城摇一摇活动触发登录
        POPUP_SURPRISE = 140; //(看视频)惊喜红包弹窗引导登录
        H5 = 141;//H5页面
        ENLIVEN_POPULARITY = 142; //拉活好友人气助力弹窗
        MERCHANT_HOME = 143;//快手小店首页
        COLUMN_BISERIAL_NEGATIVE_FEEDBACK = 144;//栏目双列点击某个卡片上的负反馈按钮
        CONDITION_RED_PACKET_PANDENT = 145;//条件红包挂件进入登录
        COLUMN_DETAIL_PUBLISH_BARRAGE = 146;//栏目详情页发布弹幕
        LIVE_PLAY_BACK = 147;//直播回放
        PC_TOP_LOGIN_BUTTON = 148;//PC官网顶导右侧登录按钮
        PC_TOP_SEARCH_BUTTON = 149;//PC官网_顶导搜索按钮
        PC_AUTHOR_HEAD_BUTTON = 150;//PC官网_作者头像按钮
        PC_DETAIL_COMMENT_BUTTON = 151;//PC官网_详情页评论按钮
        PC_DETAIL_REPLY_COMMENT_BUTTON = 152;//PC官网_详情页回复某条评论按钮
        PC_DETAIL_LIKE = 153;//PC官网_详情页点赞
        PC_DETAIL_COMMENT_LIKE = 154;//PC官网_详情页评论点赞
        PC_DETAIL_FOLLOW_AUTHOR = 155;//PC官网_详情页关注作者
        PC_DETAIL_SHARE = 156;//PC官网_详情页分享
        PC_TOP_MYFOLLOW_BUTTON = 157;//PC官网_顶导"我的关注"按钮
        GZONE_LIVE_TAB = 158;//游戏直播间tab下登录引导按钮
        GZONE_LIVE_HOT_COMMENT = 159;//游戏直播间热词条目点击（快捷弹幕）
        LIVE_CARD_PRESS_REPORT = 160;//直播卡片长按点击举报
        KUAISHOU_LOGO_WATERMARK = 161;// 首页快手logo水印
        RETURN_AWARD_CARD = 162;//回归用户礼包组件
        PC_BOTTOM_LOGIN_BUTTON = 163;//PC官网_底部登录按钮
        PC_PROFILE_FOLLOW_BUTTON = 164;//PC官网_profile页左侧关注按钮
        PC_DAMAKU_INPUT_BUTTON = 165;//PC官网_弹幕输入框按钮
        PC_DAMAKU_LIKE_BUTTON = 166;//PC官网_弹幕点赞按钮
        NEW_USER_CASH_RED_POPUP = 167;//新回首日现金红包
        TASK_PAGE_TOP_LOGIN_BUTTON = 168;//任务页顶部登录按钮
        TASK_PAGE_SIGN_IN = 169;//任务页签到
        TASK_PAGE_TASK_LIST = 170;//任务页任务列表
        BOTTOM_FRIEND = 171;//底导朋友页
        SOLITAIRE = 172; //视频接龙
        FIND_LIVE_TAB_MY_LIVE_BUTTON = 173;// 发现页直播tab下我的直播入口
        HOME_FOLLOW_LOGIN_BUTTON = 174;//首页关注登陆按钮
        FINISH_PLAY_AND_FOLLOW = 175;//未登录情况下，完播视频会提示关注，点击关注回跳转到登录页
        MIDDLE_LOGIN_OR_SIGNUP_BUTTON = 176;//未登录情况下，出现在页面中下位置的登录注册按钮，点击后跳转到登录页
        LIMIT_REWARD_OPEN_POPUP = 177;//限时奖励开启弹窗（未登录情况时，点击跳转登录页面）
        TIME_LIMITED_BENEFITS_GUIDE_POPUP = 178;//限时福利引导弹窗红包（未登录情况时，点击跳转登录页面）
        CASH_SIGN_IN_PUSH = 179;//多日领现金push点击后会跳转登录页（未登录情况时，点击跳转登录页面）
        RECREATION = 180; // 在分享面板点击二创转发入口时，如果还没有登录，需要跳转登录
        NOT_WIFI_REMIND_DATA_CHARGES_POPUP = 181;// 非WIFI提醒弹窗（出现在屏幕中部，未登录情况时，点击跳转登录页面）
        GET_DATA_CHARGES_REMIND_POPUP = 182;// 非WIFI提醒领取流量弹窗（为吸底样式，未登录情况时，点击跳转登录页面）
        ISP_GET_DATA_POPUP = 183;// 免流量获得弹窗 （为红包样式，未登录情况时，点击跳转登录页面）
        VIDEO_DETAIL_POI_COLLECTION = 184; //视频详情页POI收藏
        KGI_LIKE_PHOTO = 185; //点赞作品（记录端智能下发）
        FOLLOW_COLLECTION_PHOTO = 186; //关注合集作品
        BDD_PAGE_MAIN_BUTTON = 187; //冰墩墩活动页主邀请按钮（可能被其他实物奖励邀请复用）
        BDD_PAGE_GUIDE_LOGIN_POPUP = 188; //冰墩墩活动页引导登录弹窗按钮（可能被其他实物奖励邀请复用）
        SHARE_SHARE_PANEL_POPUP = 189; //转发（分享弹窗上的按钮）
        MESSAGE_FRIEND_SHARE_PANEL_POPUP = 190; //私信（废弃，分享弹窗上的按钮）
        REPORT_SHARE_PANEL_POPUP = 191; //举报（废弃，分享弹窗上的按钮）
        COLLECT_SHARE_PANEL_POPUP = 192; //收藏（废弃，分享弹窗上的按钮）
        PHOTO_TOGETHER_SHARE_PANEL_POPUP = 193; //合拍（废弃，分享弹窗上的按钮） 
        WELCOME_BACK_LOGIN_POPUP = 194; //欢迎回来登录弹窗
        INTELLIGENT_ENGINE_LAUNCH_POP = 195; //端智能触发的弹窗（本次为激励）
        GIFT_PANEL_UNLOCK_CARD=196;//礼物面板礼物绑定卡片
        SURPRISE_PACKET_POPUP = 197; //惊喜红包弹窗
        LOGIN_ENTRANCE_SHARE_PANEL_POPUP = 198;//分享弹窗的登录入口（未登录态在分享面板新增入口）
        LOAD_OFF_KEEP_STAY_KEEP = 199;//主站极速版长按icon卸载挽留页面
        FIND_LOGINSOURCE = 200;//主站双列发现的登录来源
        DOUBLE_NEARBY_LOGINSOURCE =201;//主站双列同城登录来源
        DOWNLOAD_RELOAD = 202;//卸载重装直接登录来源
        KS_FEATURE_LOGIN_ENTRANCE = 203;//快手-精选页的登录入口
        EXIT_LOGIN_REFLOW_REMIND_LOGIN = 204;//退登回流第二天登录提醒
        LIVE_CNY23 = 205; //23春节活动
        IMMERSE_GUIDE_LOGIN_POPUP = 206;//上下滑引导登录左下按钮
        CLICK_SHOP_ENTRANCE = 207;//直播间小黄车icon
        SINGLE_LOGIN_GUIDE_POPUP = 208;//单列引导登录弹窗
        MESSAGE_LOGIN_GUIDE_REDPOINT = 209;//底导消息引导登录红点
        ENCOURAGE_LOGIN_POPUP = 210;//未登录态进入激励页出登录领金币弹窗
        COMMENT_ICON = 211;//评论入口点击触发登录
        COMMENT_PANEL_ONE_SLIDE = 212;//评论面板一滑触发登录
        FINK_TAB_ADD_LOGIN_BUTTON = 213;//发现页放底导左上角新增登录按钮
        NEAR_TAB_ADD_LOGIN_BUTTON = 214;//同城页放底导左上角新增登录按钮
        ENCOURAGE_FEATURE_CARD_LOGIN = 215;//单列激励大卡触发登录
        LIVE_ATMOSPHERE_GIFTS = 216;//直播礼物KRN页面
        LOCAL_NEARBY_SOCIAL_CENTER = 217;//同城社交中心场页面
        FEED_CORONA_VIP_PAY = 218;//放映厅会员支付弹窗登录校验
        FEED_CORONA_ACTIVITY_PAY = 219;//放映厅活动支付弹窗登录校验
        ICON_SHORTCUT_ACTION = 220;//3D touch快捷菜单
        LOCALLIFE_TRANSACTION_MAKEORDER = 221;//本地生活交易链路提交订单页面
        LOCAL_SLIDE_BIU_BUTTON = 222;//同城详情页biu私信发送
        MERCHANT_YUEKA = 223;//电商省钱月卡登录校验
        RECRUIT_MANAGER = 224;//快聘管家登陆校验
        JS_CJ_FEED_PENDANT = 225;//极速版承接组公域红包挂件
        JS_CJ_FEED_SIGN = 226;//极速版承接组公域签到
        JS_CJ_FEED_INPUSH_INDEX = 227;//极速版承接组公域提现inpush
        JS_CJ_FEED_GUIDE_END = 228;//极速版承接组公域激励任务引导结束
        PYMK_XINHUI_FOLLOW_LOGIN = 229;//单列feed中「你可能认识的人」关注按钮拉登录
        KWAISHOP_C_SHOPLIST = 230;//个人店铺页点击登录
        LIVE_DRAGON_NIGHT_ROOM = 231;//24cny除夕主会场直播间
        LIGHT_DRAGON_LANTERN = 232;//24cny预热主会场页面
        MERCHANT_ITEMSELF_DETAIL = 233;//商品详情页
        LIVE_WISH_ROOM = 234;//24CNY祈福直播间
        KWAI_FOLLOW_WIDGET = 235;//快手关注小组件
        MEMORY_OF_YEAR = 236;//24元旦年度回忆
        CNY2024_KOI = 237;//24CNY新春锦鲤
        GZONE_WATCH_PLAY = 238;//游戏直播边看边玩
        CNY2024_UG = 239;//24CNY增长分会场
        FORCED_LOGIN_NEW = 240;//新设备强制登录
        XINHUI_DAKA_LOGIN = 241;//新回打卡领现金引导登录
        XINHUI_GUAJIAN_LOGIN = 242;//新回打卡领现金挂件引导登录
        LIEBIAN_VIDEO_LOGIN = 243;//看视频领现金未登录引导登录挂件
        ENCOURAGE_ACTIVEUSER_FEED_CARD = 244;//活跃用户签到领金币未登录引导登陆大卡
        PREVIEW_BOTTOM_ENTER_LIVE_LOGIN = 245;//通过简易直播间底部卡片交互进间内前登录
        PREVIEW_TOPRIGHT_ENTER_LIVE_LOGIN = 246;//通过简易直播间进间内参与红包、幸运星前登录
        UG_MERCHANT_NUR_FEED_CARD = 247;//从电商新人权益卡片引导启动登录页
        AD_READING_PAY = 248;//从小说阅读中的付费页面跳转到登录页面
        ENCOURAGE_NEWUSER_LOGIN = 249;//点击看视频挂件或赚钱页底Bar跳转登录
        NEGATIVE_PANEL_PRESS_AUDIO_VIDEO_FEEDBACK = 250;//从负反馈面板点击音视频反馈跳转到登录页面
        AOYUN_2024_LOGIN_JINGCAI = 251;//24巴黎夏奥-竞猜会场登录
        AOYUN_2024_LOGIN_MAIN = 252;//24巴黎夏奥-主会场登录
        UG_NR_WITHDRAWAL_FEED_CARD = 253;//30日新回未登录设备点击立领1元提现大卡跳转登录
        OLY24_LIVE_BOTTOM_WIDGET = 254; // 夏奥-超级直播间底部挂件
        XIANSHIMIAOSHA_LOGIN = 255 ;//限时秒杀页面未登录用户点击登录按钮跳转到登录页面
        CHAOLIUFUSHI_LOGIN = 256 ;//潮流服饰页面未登录用户点击登录按钮跳转到登录页面
        DIJIATEMAI_LOGIN = 257;//低价特卖页面未登录用户点击登录按钮跳转到登录页面
        DAPAIDABU_LOGIN = 258;//大牌大补页面未登录用户点击登录按钮跳转到登录页面
        MERCHANT_BUYER_MALL_LOGIN_BUTTON = 259;//商城页面未登录用户点击登录按钮跳转到登录页面
        BUYER_HOME_PAGE_LOGIN_BUTTON = 260 ;//买家首页未登录用户点击登录按钮跳转到登录页面
        BUYER_HOME_PAGE_ROUTER_FORCE_LOGIN = 261;//买家首页路由强制登录
        KWAI_MERCHANT_WIDGET = 262;//快手电商小组件
        LOCALLIFE_TRANSACTION_ORDERDETAIL = 263;//本地生活交易订单详情
        ENCOURAGE_PAGE_KRN_LOGIN = 264;//激励KRN赚钱页
        LIVE_LUCKY_STAR = 265;//幸运星挂件点击后跳转登陆页面
        SLIDE_LOGIN_GUIDE_POPUP = 266;//未登录设备当日第N次启动精选单列下滑视频后引导登录
        ACCOUNT_ERROR_AUTO_LOGIN = 267;//客户端本地登录数据异常，退出登录并自动跳转登录
        STARPLAN_LOGIN = 268;//进入星火计划页面未登陆快手，直接跳转登陆页
        TUBE_CENTER_HOME_SINGLE_LOGIN_PAY = 269;//短剧中心页点击付费内容跳转登录页面
        BAOHUO_ENCOURAGE_PAGE_REDPACKET=270;//保活-简易激励页面点击红包拉登录
        PROFILE_LOGIN_POP = 271;//客态P页半屏登录弹框
        SHARE_TOKEN_INNOVATION_ASSIST_LOGIN = 272;//口令回流，未登录的情况下进行登陆操作
        KS_LIVE_MEMOIRS_LOGIN= 273;//直播年度回忆录页面调起登录
        COMMENT_PICTURE_EDIT_SEND_LOGIN=274; //评论发送图片编辑图片调起登录
        KS_NEW_YEAR_MEMORY_LOGIN2024 = 275;//年度回忆活动封面跳转登陆页
        ME_TAB_RED_DOT=276;//我tab红点引导登录
        GUEST_LOGIN_BUTTON=277;//公域游客登录按钮
        LEFT_BOTTOM_LOGIN_FIND_LIKE = 278;//左下信息区_查看点赞作品
        FEED_AI_CHAT = 279;//对话发送时引导登陆
        NEW_MUSIC_PANEL = 280;//点击面板时引导登陆
        LOGOUT_POP = 281;//主动退登出半屏登录弹框
        IN_PUSH = 282;//in-push登录
        LEFT_BOTTOM_PHONE_QUICK_LOGIN = 283;//新安装-手机号免授权快速登录
        LEFT_BOTTOM_HISTORT_QUICK_LOGIN = 284;//退登-历史账号免授权快速登录
        MP_LOGIN = 285;//快手app小程序调起登录
        MULTI_HISTROY_LOGIN = 286;//多历史账号快速登录
        NEWRE_NEARBY_PEOPLE_CARD = 287;//新回附近的人卡片
        KING_PROFILE_JUMP_VIDEO_SELECTION = 288;//王者个人主页-视频管理页登陆场景
        PUBLIC_DOMAIN_SIGN_IN_POP_UP_WINDOW= 289;//公域签到弹窗
        LITE_SECOND_COMMENT= 290;//简易直播间2.0评论
        LITE_SECOND_LIKE= 291;//简易直播间2.0点赞
        XINHUI_POP_UP_WINDOW= 292;//30新回88元弹窗
        XINHUI_KUAIHUA_CARD= 293;//新回快划卡片-关注引导登录
        ECO_PHOTO_TO_LIVE_CARD = 294;//电商短视频互动区卖点透出
        XINHUI_SOUSUO_POPUP = 295;//新回搜索激励促留
        XINHUI_KUAIHUA_END= 296;//新回快划卡片-结果页引导登录

    }

    enum ActionType {
        UNKNOWN1 = 0; // 未知
        LOGIN = 1; // 登录
        SIGNUP = 2; // 注册
    }

    Source source = 1; // 触发注册弹窗来源行为
    ActionType action_type = 2; // tab类型
    string portal_url = 3; // 如果source是PORTAL类型，此字段表示portal的url
    string source_ext_info = 4;//放置更多登陆来源的可扩展参数
}

// 记录标签及所属作品的相关参数
message TagPackage {
    enum Type {
        UNKONWN1 = 0; // 未知
        MUSIC = 1; // 音乐标签
        TOPIC = 2; // 话题标签
        POI = 3; // 地址标签
        MAGIC_FACE = 4; // 魔法表情标签
        UGC_MUSIC = 5; // UGC音乐标签
        RICH_TOPIC = 6; // rich话题标签
        SAME_FRAME = 7; // 同框标签
        CREATIVITY = 8; // 创意标签
        CHORUS = 9; // 合唱标签
        SERIES = 10; // 剧集标签
        KUAISHAN = 11; // 快闪标签
        LIVE_AGGR_VERTICAL = 12; // 直播聚合垂类标签
        SHOPPING_CART = 13; //购物车标签
        KARAOKE = 14; //独唱标签
        INTELLIGENT_ALBUM_LIST=15; //时光影集
        ONE_BUTTON_PUBLISH=16;//一键出片
        PUBLISH_SAME_PHOTO=17;//做同款大片
        ON_BUTTON_SAME_PHOTO=18;//一键出片&做同款大片
        KWAIYING=19; // 快影
        VIDEO_SOLITAIRE=20; //视频接龙
        FRAME_ANCHOR=21; //同框锚点
        AD_LABEL=22; //广告标
    }
    string identity = 1; // Tag id
    string name = 2; // tag名称
    string exp_tag = 3; // 推荐后端的参数
    uint64 index = 4; // tag位置id
    string llsid = 5; // 曝光id
    uint64 photo_count = 6; // 标签包含视频量;
    Type type = 7; // tag类型
    repeated PhotoPackage photo_package = 8; // 记录标签中视频id和作者id
    string secondary_type = 9; // tag二级类型（上报不同类型标签的细分类型，比如音乐标签的music_type）
    string params = 10; // 标签扩展参数, 格式为json 字符串
}

// 记录直播展示广播(穿云箭消息)相关参数
message LiveBroadcastPacakge {
    string to_live_stream_id = 1; // 接受广播信息的直播间id(接受穿云消息的直播间ID)
    string receive_broadcast_audience_id = 2; // 接收广播消息的观众id(接收穿云消息的观众ID)
    string exp_tag = 3; // 推荐后端的参数
    string broadcast_info = 4; // 透传广播信息
    string to_anchor_user_id = 5; // 接受广播信息直播间的主播ID(接受穿云消息直播间的主播ID)
}

message EffectPackage {
    string name = 1; // 特效名称
    string location = 2; // 加入特效的位置
    string duration = 3; // 特效时长
    int64 id = 4; // 特效id
    string group_name = 5; // 特效所在分组名称
}

// 功能开关设置参数，例如：水印开关设置，name=watremark，on=true，代表水印开关打开，on=false，代表关闭
message FeatureSwitchPackage {
    string name = 1; // 名称
    bool on = 2; // 开关状态
}

// 批量记录功能开关设置参数，例如：水印开关设置，name=watremark，on=true，代表水印开关打开，on=false，代表关闭
message BatchFeatureSwitchPackage {
    repeated FeatureSwitchPackage feature_switch_package = 1; // 记录功能开关状态
}

// 从电脑导入音乐相关参数
message ImportMusicFromPCPackage {
    enum NetworkStatus {
        UNKONWN1 = 0; // 未知
        WIFI_CONNECTED = 1; // WIFI连接
        WIFI_NO_CONNECTION = 2; // WIFI未连接
    }
    enum UploadStatus {
        UNKONWN2 = 0; // 未知
        UPLOADING = 1; // 上传中
        BEFORE_UPLOADING = 2; // 未上传
        UPLOAD_COMPLETED = 3; // 上传完成
    }
    NetworkStatus network_status = 1; // 网络状态
    UploadStatus upload_status = 2; // 音乐上传状态
}

// 直播观众相关参数
message LiveAudiencePacakge {
    string identity = 1; //直播观众id
    uint32 index = 2; // 直播观众位置id
}

// 电商链接相关参数
message ECommerceLinkPacakge {
    string identity = 1; //链接id
}

// 记录批量曝光的评论
message CommentShowPackage {
    repeated CommentPackage comment_package = 1; // 记录曝光评论相关信息
}

// 记录批量上屏的弹幕
message DanmakuShowPackage {
    repeated DanmakuPackage danmaku_package = 1; // 记录批量弹幕相关信息
}

// 记录批量曝光的标签
message TagShowPackage {
    repeated TagPackage tag_package = 1; // 记录曝光标签相关信息
}

// 记录批量曝光的视频
message PhotoShowPackage {
    repeated PhotoPackage photo_package = 1; // 记录曝光视频相关信息
}

// 记录单次访问的内容相关信息
message VisitDetailPackage {
    uint64 client_timestamp = 1; // 访问时间戳
    string url = 2; // 记录用户访问的视频或者直播或者个人主页的url
}

// 记录批量访问的内容相关信息
message BatchVisitDetailPackage {
    repeated VisitDetailPackage visit_detail_package = 1;
}

// 记录歌手相关信息
message SingerDetailPackage {
    string identity = 1; // 歌手id
    string name = 2; // 歌手昵称
    uint32 index = 3; // 歌手位置
}

// 记录歌曲相关信息
message MusicDetailPackage {
    string identity = 1; // 歌曲id
    string name = 2; // 歌曲名称
    uint32 index = 3; // 歌曲位置
    string type = 4; // 歌曲类型
    uint64 category_id = 5; //分类id，用于标识客户端音乐来源
    string exp_tag = 6; // 歌曲exp_tag，后端推荐参数
    string llsid = 7; // 曝光id
    uint64 c_source = 8; // 歌曲的版权类型
    string params = 9; // 记录歌曲补充信息
}

// 批量记录歌曲相关信息
message BatchMusicDetailPackage {
    repeated MusicDetailPackage music_detail_package = 1;
}

// 记录feed流曝光数量
message FeedShowCountPackage {
    enum Type {
        UNKONWN1 = 0; // 未知
        PHOTO = 1; // 视频
        LIVE = 2; // 直播
    }
    Type type = 1; // 类型
    uint32 count = 2; // 曝光数量
}

// 记录各类型feed流曝光数量
message BatchFeedShowCountPackage {
    repeated FeedShowCountPackage feed_show_count_package = 1;
}

// 记录视频编辑详细操作信息
message VideoEditOperationPackage {
    enum Type {
        UNKONWN1 = 0; // 未知
        CUT = 1; // 剪切
        FILTER = 2; // 滤镜
        MUSIC = 3; // 音乐
        EFFECT = 4; // 特效
        MAGIC = 5; // 魔法
        SUBTITLE = 6; // 字幕
        STICKER = 7; // 贴纸
        TRANSITION = 8; // 转场
        COVER = 9; // 封面
        TEXT = 10; // 文字
        MAGIC_FINGER = 11; // 魔法手指
        THEME = 12; // 主题
        TONE_TUNING = 13; // 调音
        DURATION = 14; // 照片电影的时长
        BORDER = 15; // 相框编辑
        BEAUTY = 16; // 编辑美颜
        VOICE_CHANGE = 17; // 变声
        PRETTIFY = 18; // 美化
        SEGMENT = 19; // 片段编辑
        AICUTSTYLE = 20; // 智能剪辑风格编辑
        CROP = 21; // 图片的裁剪功能
        MAKEUP = 22; // 美妆
        BODY = 23; // 美体
        SORT = 24; // 图片排序
        FINETUNING = 25; //图片细调
        ATLAS_TEMPLATE = 26; //图片模版
    }
    Type type = 1; // 编辑类型
    string sub_type = 2; // 编辑小类
    string name = 3; // 编辑功能名称
    string value = 4; // 编辑的结果
    string extra_message = 5; // 附加信息
}

// 记录视频各编辑项状态
message VideoEditFeaturesStatusPackage {
    bool cut = 1; // 是否有剪切
    uint32 filter_index = 2; // 选中的滤镜所在位置，从1开始，未使用则为0  废弃，使用string参数记录
    uint32 music_index = 3; // 选中的配乐类型所在位置，从1开始，未使用则为0  废弃，使用string参数记录
    uint32 effect_index = 4; // 选择的特效类型所在位置，从1开始，未使用则为0  废弃，使用string参数记录
    uint32 magic_index = 5; // 选择的魔法类型所在位置，从1开始，未使用则为0  废弃，使用string参数记录
    bool subtitle = 6; // 是否有字幕
    uint32 sticker_index = 7; // 选择的贴纸类型所在位置，从1开始，未使用则为0  废弃，使用string参数记录
    uint32 transition_index = 8; // 选择的转场类型所在位置，从1开始，未使用则为0  废弃，使用string参数记录
    repeated string filter = 9; // 具体选中的滤镜
    repeated string music = 10; // 选中的配乐类型录
    repeated string effect = 11; // 选择的特效类型
    repeated string magic = 12; // 选择的魔法类型
    repeated string sticker = 13; // 选择的贴纸类型
    repeated string transition = 14; // 选择的转场类型
    repeated string magic_finger = 15; // 选择的魔法手指类型
    string cover = 16; // 选择封面的pts(指的是封面在视频中的哪一帧)
    repeated string mosaic = 17; // 具体使用的马赛克
    uint32 frame_duration = 18; // 多图编辑的每帧时长
    string background = 19; // 添加的背景相关信息
    string crop = 20; // 大小裁剪的信息
    string trim = 21; // 时长截取信息
}

// 记录商品明细信息
message CommodityDetailPackage {
    string id = 1; // 商品id
    string name = 2; // 名称
    uint32 index = 3; // 位置
    bool selected = 4; // 是否被选中，true代表选中，false代表未选中
    uint32 item_type = 5; // 商品类型
    bool has_coupon = 6; // 商品是否带优惠券
    bool is_seckill = 7; // 是否秒杀商品
    uint32 is_in_explanation = 8; // 是否在讲解中0否,1是
    uint32 is_playback = 9; // 是否有回放0否,1是
    uint32 is_snapup = 10; // 是否是去抢购 0否,1是
    uint32 is_more_cheap= 11; //是否享受多件优惠 0否,1是
    uint32 is_new_exclusive= 12; //是否是新人专享 0否,1是
    uint32 is_full_reduction= 13; //是否享受店铺满减 0否,1是
    uint32 is_sandea_py_item=14; //是否为闪电拍商品
    uint32 is_search_flash= 15; //是否为搜索闪烁 0否,1是
    uint32 market_good_type=16; //营销商品类型
    uint32 item_activity_type=17; //商品的活动类型
    uint32 status=18;  //商品状态
    uint32 inventory=19; //商品库存
    string price=20; //商品价格
    string light_spot=21; //商品亮点
    string activity_id=22; //活动ID
    uint32 sku_num=23; //sku个数(指一个商品对应sku个数)
    string seller_id=24; //卖家id
    string carrier_entry=25; //入口来源(后端下发)
    string carrier_id=26; //推广id(后端下发)
    string carrier_type=27; //推广类型(后端下发)
    string rights_name=28; //权益名称（透传文本）
}

// 批量记录商品明细信息
message BatchCommodityDetailPackage {
    repeated CommodityDetailPackage commodity_detail_package = 1;
}

// 记录魔法表情明细信息
message MagicFacePackage {
    string name = 1; // 名称
    uint64 start_time = 2; // 开始插入的时间，单位：毫秒
    uint64 duration = 3; // 插入时长，单位：毫秒
    string id = 4; // 魔表id
    uint32 group_id = 5; // 魔表分组
    uint32 index = 6; // 魔表位置
    uint32 segment_index = 7; // 视频拍摄中添加魔表的位置，例如第几段添加了魔表
    uint32 type = 8; //魔表类型
    string parent_id = 9; // 子魔表对应的父魔表的id
    string magic_name = 10; //魔表名称
    string magic_face_params = 11; // 魔表相关参数，格式是json
    bool show_cover_tag = 12; //盘点魔表是否带角标
    string strategy_id = 13; // 魔表策略下发类型
    string req_id = 14;// 魔表请求request_id
    string log_params = 15; // 魔表策略推荐相关参数，格式是json

}

// 记录批量魔法表情曝光数量
message MagicFaceShowPackage {
    repeated MagicFacePackage magic_face_package = 1;
}

// 记录视频拍摄页or主播端作品编辑操作
message ProductionEditOperationPackage {
    enum Type {
        UNKONWN1 = 0; // 未知
        FLASH_LIGHT = 1; // 闪光灯
        NIGHT = 2; // 夜间
        GEAR_SPEED = 3; // 倍速
        BEAUTY = 4; // 美颜
        CAMERA = 5; // 摄像头
        MAGIC_FACE = 6; // 魔表
        MUSIC = 7; //配乐
        FILTER = 8; // 滤镜
        VOICE_CHANGE = 9; // 变声
        MAKEUP = 10; // 美妆
        STYLE = 11; // 风格
        BODY = 12; // 美体
    }
    Type type = 1; // 记录功能大类，不能为空
    string sub_type = 2; // 记录功能二级分类，没有则为空（以埋点需求为准）
    string name = 3; // 记录功能具体名称，没有则为空（以埋点需求为准）
    string params = 4; // 记录功能状态，没有则为空（以埋点需求为准）
    int32 tab_id = 5; // 记录所属分组id，没有则为空（以埋点需求为准）
    string tab_name = 6; // 记录所属分组name，没有则为空（以埋点需求为准）
}

// 记录美颜子功能项状态
message BeautySubFeaturesPackage {
    enum SubFeatures {
        UNKONWN1 = 0; // 未知
        SMOOTH_SKIN = 1; // 磨皮
        SKIN_COLOR = 2; // 肤色
        THIN_FACE = 3; // 瘦脸
        JAW = 4; // 下巴
        ENLARGE_EYE = 5; // 大眼
        WRINKLE = 6; // 去法令纹
        EYE_BAG = 7; // 去黑眼圈
        EYE_BRIGHTEN = 8; // 亮眼
        TEETH_BRIGHTEN = 9; // 白牙
        BEAUTIFY_LIPS = 10; // 美唇
        NOSE_SHADOW = 11; // 鼻影
        CUT_FACE = 12; // 削脸
        TINY_FACE = 13; // 小脸
        SHORT_FACE = 14; // 短脸
        NARROW_FACE = 15; // 窄脸
        THIN_LOWER_JAW = 16; // 瘦下颌
        LOWER_JAWBONE = 17; // 下颌骨
        THIN_CHEEKBONE = 18; // 瘦颧骨
        EYE_DISTANCE = 19; // 眼间距
        THIN_NOSE = 20; // 瘦鼻
        LONG_NOSE = 21; // 长鼻
        PHILTRUM = 22; // 人中
        EYE_WIDTH = 23; // 眼宽
        EYE_HEIGHT = 24; // 眼高
        EYE_CORNER = 25; // 眼角
        MOUTH = 26; // 嘴形
        MOUTH_WIDTH = 27; // 嘴宽
        MOUTH_HEIGHT = 28; // 嘴扁
        FORE_HEAD = 29; // 额头
        CLARITY = 30; // 清晰
        STEREO = 31; // 立体
        HAIR_LINE = 32; // 发际线
        SKIN_SMOOTH = 33; //匀肤
        NOSE_BRIDGE = 34; //山根
        TEMPLE = 35; //太阳穴
        EYEBROW_SIZE = 36; //眉毛粗细
        EYE_POSITION = 37; //眼睛位置
        NOSE_LENGTH = 38; //长短鼻
        JAW_THIN = 39; //瘦下颌
        FACE_SHORT = 40; //短脸
        BRIGHT = 41; // 美白
        BLOND = 42; // 白皙
        RUDDY = 43; // 红润
        SHRINK_HEAD = 44; //小头
        HIGH_SKULL = 45; // 高颅顶 
        FRECKLE_ACNE = 46; // 祛斑痘
        WOCAN = 47; // 卧蚕
        WATER_SKIN = 48; // 水光针
        JAW_LINE = 49; // 下颌线
        DUDU_LIP = 50; //  嘟嘟唇
        APPLE_FACE = 51; // 苹果肌
        DOUBLE_EYE_LID = 52; // 双眼皮
        HIGH_NOSE = 53; //隆鼻
        REMAJI = 54; //热玛吉
        THIN_BODY = 55;//瘦身
        THIN_LOIN = 56;//瘦腰
        LONG_LEG = 57;//长腿
        MOUTH_POSITION=58;//嘴位
        SMILE_LIPS=59;//微笑唇
        HIGH_FOREHEAD=60;//额高
        WIDTH_FOREHEAD=61;//额宽
        COURT=62;//上庭
        MID_COURT=63;//中庭
        LOWER_COURT=64;//下庭
        AI_WHITENING=65;//AI美白
        ADD_LIGHT=66;//补光
    }
    SubFeatures sub_features = 1; // 记录子功能项
    string name = 2; // 记录具体拉杆值（默认：default；如果有调整记录调整后的值，如30%）
    float value = 3; // 浮点值
    bool is_adjusted = 4; //是否调整过
    string params = 5; //补充参数
}

// 记录美颜各功能项状态
message BeautyStatusPackage {
    uint32 type = 1; // 记录具体选择的套系
    repeated BeautySubFeaturesPackage beauty_sub_features_package = 2; // 记录子功能项及状态值
    string segment_index = 3; // 套系位置
    bool use_quick_beauty = 4; // 是否使用一键美颜
}

// 记录美体各功能项状态
message BodyUsePackage{
    string item = 1; // 位置(废弃)
    float value = 2; // sdk值(废弃)
    bool use_quick_body = 3; //使用一键美体
    repeated BodySubFeaturesPackage body_sub_features_package = 4;// 记录子功能项及状态值
}

// 记录美体子功能项状态
message BodySubFeaturesPackage{
    string item = 1; // 位置
    float value = 2; // sdk值
}

// 记录美妆各功能项状态
message BeautyMakeUpStatusPackage {
    enum PrimaryType {
        UNKONWN1 = 0; // 未知
        NATURAL = 1; // 自然
        LOVELY = 2; // 可爱
        VIGOUR = 3; // 元气
        ELEGANT = 4; // 优雅
        DOMINEERING = 5; // 御姐
        NEUTRAL = 6; // 中性
    }
    PrimaryType primary_type = 1; // 记录具体选择的套系(废弃，使用3-4参数来记录)
    repeated BeautyMakeUpSubFeaturesPackage beauty_make_up_sub_features_package = 2; // 记录子级功能及具体设置值
    string primary_type_new = 3; // 记录一级类型
    string primary_index = 4; // 记录一级套系位置
    string primary_live_value = 5; // 记录直播美化套系的滑杆值
}

// 记录美妆子功能项状态
message BeautyMakeUpSubFeaturesPackage {
    enum SecondaryType {
        UNKONWN1 = 0; // 未知
        LIPSTICK = 1; // 口红
        EYEBROWS = 2; // 眉毛
        CHEEK = 3; // 腮红
        CONTOUR = 4; // 修容
        EYESHADOW = 5; // 眼影
        EYELINER = 6; // 眼线
        LASH = 7; // 睫毛
        EYELIDS = 8; // 双眼皮
        PUPIL = 9; // 美瞳
    }
    SecondaryType secondary_type = 1; // 记录二级功能项(废弃，使用4-5的参数来记录)
    string third_type = 2; // 记录三级功能项
    string value = 3; // 记录具体拉杆值（默认：default；如果有调整记录调整后的值，如30%）
    string secodary_type_new = 4; // 记录二级类型
    string secodary_index = 5; // 记录二级位置
    string third_index = 6; // 记录三级位置
    string segment_index = 7; // 功能位置
    string tab_id = 8; // 分类id
}

// 批量记录美妆各功能项状态
message BatchBeautyMakeUpStatusPackage {
    repeated BeautyMakeUpStatusPackage beauty_make_up_status_use_package = 1; // 记录美妆各功能状态
}

// 记录滤镜相关信息
message FilterDetailPackage {
    string id = 1; // 滤镜id,点击取消按钮/选择正常、原图时为"-1"
    string name = 2; // 滤镜name,点击取消按钮/选择正常、原图时为"正常"
    uint32 index = 3; // 滤镜位置，从1开始
    uint32 segment_index = 4; // 视频拍摄中添加滤镜的位置，例如第几段添加了滤镜
    bool is_adjusted = 5; // 是否手动调节滑杆
    float value = 6; // 具体拉杆值(0-1的浮点数)
    bool is_commonly_used = 7; // 是否是常用滤镜,是true,否false
    int32 tab_id = 8; // 滤镜所在tab的id,点击取消按钮/选择正常、原图时为"-1"
    string tab_name = 9; // 滤镜所在tab的name,点击取消按钮/选择正常、原图时为"无"
}

// 批量记录滤镜相关信息
message BatchFilterDetailPackage {
    repeated FilterDetailPackage filter_detail_package = 1; // 记录滤镜相关信息
}

// 记录视频拍摄中各功能使用情况
message CameraRecordFeaturesStatusPackage {
    bool beauty = 1; // 是否使用美颜
    repeated MagicFacePackage magic_face_package = 2; // 拍摄中使用的魔法表情
    string magic_music = 3; // 拍摄中使用的魔法音乐（废弃，相关信息上报到魔表package中）
    string music = 4; // 选择的音乐（废弃，使用6来记录选择音乐的信息）
    FeatureSwitchPackage beats_switch_package = 5; // 记录动感节拍开关状态，name=beatsEnabled
    MusicDetailPackage music_detail_package = 6; // 记录选择音乐的明细信息
    BeautyStatusPackage beauty_status_package = 7; // 拍摄结束时如果使用了美颜，记录使用的明细信息，后期废弃，使用beauty_use_package
    repeated FilterDetailPackage filter_use_package = 8; // 拍摄中使用的滤镜
    repeated BeautyMakeUpStatusPackage beauty_make_up_status_detail_package = 9; // 记录美妆各功能使用情况
    repeated StyleStatusPackage style_status_package = 10; // 记录风格各功能使用情况
    repeated BeautyStatusPackage beauty_use_package = 11; //美颜使用信息,多段拍时会有多个值
    repeated BodyUsePackage body_user_package = 12; //美体使用信息
}

// 记录k歌录制作品明细信息
message KSongDetailPackage {
    enum Type {
        UNKONWN1 = 0; // 未知
        AUDIO = 1; // 音频
        MV = 2; // MV
    }
    enum Model {
        UNKONWN2 = 0; // 未知
        WHOLE_SONG = 1; // 整首歌曲
        HOT_CLIP = 2; // 热门片段
        FREE_CHOICE = 3; // 自由选择
        DUET = 4; // 合唱
    }
    Type type = 1; // 作品类型
    bool cover = 2; // 作品是否使用了封面
    Model model = 3; // 录制模式
}

// 记录视频播放进度条拖动信息
message PhotoSeekBarDragPackage {
    uint64 start_time = 1; // 开始拖动时，视频进度条所处时刻 单位：毫秒
    uint64 end_time = 2; // 拖动完成时，视频进度条所处时刻 单位：毫秒
    uint64 cost = 3; // 一次拖拽耗时
}

message BatchSeekBarDragPackage {
    repeated PhotoSeekBarDragPackage seek_bar_drag_package = 1;
}

// 功能元素停留时长
message FeaturesElementStayLengthPackage {
    string name = 1; // 名称
    uint64 duration = 2; // 打开或者停留时长，单位 ms
}

// 记录视频编辑效果信息
message EditEffectPackage {
    string id = 1; // 功能项id
    uint32 index = 2; // 功能项位置
}

// 记录批量视频编辑效果信息
message BatchEditEffectPackage {
    repeated EditEffectPackage edit_effect_package = 1;
}

// 记录照片电影主题明细信息
message ThemePackage {
    string name = 1; // 名称
    string id = 2; // id
    uint32 index = 3; // 主题位置
}

// 批量记录照片电影主题相关信息
message BatchThemePackage {
    repeated ThemePackage theme_package = 1;
}

// 海外专用，主站请勿使用
// 拍摄过程中状态的信息
message RecordInfoPackage {
    enum Action {
        UNKNOWN1 = 0;
        STOP_RECORD = 1;
        FINISH_RECORD = 2;
        CANCEL_RECORD = 3;
        START_RECORD = 4;
        PAUSE_RECORD = 5;
        OPEN_CAMERA = 6;
        RECORDING = 7;
        FRAME_PROCESS = 8;
    }
    enum EncodeType {
        UNKNOWN2 = 0;
        MEDIA_MUXER = 1;
        FFMPEG = 2;
    }
    EncodeType encode_type = 1; // 编码方式
    uint64 cost = 2; // 点击拍摄到开始录制的耗时（单位：ms）
    Action action = 3; // 拍摄状态
}

// 海外专用，主站请勿使用
// 编辑拍摄视频fps
message RecordFpsInfoPackage {
    enum Type {
        UNKNOWN1 = 0;
        PREVIEW_FPS = 1;
        RECORD_FPS = 2;
    }
    enum CameraType {
        UNKNOWN2 = 0;
        FRONT = 1; // 前置摄像头
        BACKGROUND = 2; // 背后摄像头
    }
    enum EncodeType {
        UNKNOWN3 = 0;
        MEDIA_MUXER = 1;
        FFMPEG = 2;
    }
    Type type = 1; // fps类型
    uint64 min_fps = 2; // 最小fps
    uint64 max_fps = 3; // 最大fps
    uint64 average = 4; // 平均fps
    repeated MagicFacePackage magic_face_package = 5; // 魔法表情情况
    bool beauty = 6; // 是否开启美颜
    CameraType camera_type = 7; // 前置还是后置
    uint64 width = 8; // 相机宽
    uint64 height = 9; // 相机高
    EncodeType encode_type = 10; // 编码方式
    bool change_preview_size = 11; // 是否更调整过大小
    uint64 duration_ms = 12; // 录制时长Ms
}

// 海外专用，主站请勿使用
// 裁剪页导入视频信息
message VideoClipDetailPackage {
    bool is_clipped = 1; // 是否剪裁
    bool is_rotated = 2; // 是否旋转
    VideoSegmentPackage origin_video_package = 3; // 导入裁剪页视频信息
    VideoSegmentPackage clipped_video_package = 4; // 到处裁剪页视频信息
    float speed = 5; //视频变速信息
}

// 拍摄、导入、编辑状态下，视频相关信息
message VideoSegmentPackage {
    enum EncodeType {
        UNKNOWN1 = 0;
        MEDIA_MUXER = 1; // 硬编
        FFMPEG = 2; // 软编
    }
    enum DecodeType {
        UNKNOWN2 = 0;
        HARDWARE_DECODING = 1; // 硬解
        SOFTWARE_DECODING = 2; // 软解
    }
    uint32 width = 1; // 视频宽
    uint32 height = 2; // 视频高
    uint64 duration = 3; // 视频时长
    float avg_fps = 4; // 平均fps
    float max_fps = 5; // 最大fps
    float min_fps = 6; // 最小fps
    EncodeType encode_type = 7; // 视频编码方式，已废弃
    DecodeType decode_type = 8; // 视频解码方式
}

// 拍摄、导入、编辑状态下，照片相关信息
message PhotoSegmentPackage {
    uint32 width = 1; // 图片宽
    uint32 height = 2; // 图片高
}

// 相册导入视频信息
message ImportOriginVideoPackage {
    VideoSegmentPackage origin_video_package = 1; // 导入视频的信息
}

// 相册导入图片信息
message ImportOriginPhotoPackage {
    bool is_clipped = 1; // 是否剪裁
    bool is_rotated = 2; // 是否旋转
    repeated PhotoSegmentPackage origin_photo_segment_package = 3; // 每张原始图片的信息
    repeated PhotoSegmentPackage clipped_photo_segment_package = 4; // 每张导出图片的信息
}

// 视频拍摄结束编码信息
message VideoEncodingDetailPackage {
    enum EncodeType {
        UNKNOWN1 = 0;
        MEDIA_MUXER = 1; // 硬编
        FFMPEG = 2; // 软编
    }

    repeated VideoSegmentPackage encode_segment_package = 1; // 每段视频的信息，需要考虑多段拍摄的情况
    EncodeType encode_type = 2; // 编码方式
}

// 导入和编辑过程中，视频播放态信息
message VideoPreviewInfoPackage {
    enum PlayScene {
        UNKNOWN1 = 0;
        ALBUM_PREVIEW = 1; // 相册选择
        EDIT_PREVIEW = 2; // 视频编辑
        POST_PREVIEW = 3; // 视频发布预览
    }
    enum Player {
        UNKNOWN2 = 0;
        IJKPLAYER = 1; // ijkplayer
        AVPLAYER = 2; // avplayer，ios在相册页播放视频使用
        EDITORSDK = 3; // sdk
    }
    PlayScene scene = 1; // 视频播放的场景
    Player player = 2; // 视频播放器信息
    VideoSegmentPackage video_info_package = 3; // 播放态视频的信息
    uint64 prepare_duration = 4; // 播放等待时间，埋点上报是退出时上报，所以time_cost不等于prepare_duration
}

// 配乐效果调节明细信息
message MusicAdjustDetailPackage {
    string human_voice_adjust_default = 1; // 人声调节默认值
    string human_voice_adjust_offset = 2; // 人声调节偏移值，左移为-1、-2，右移为1、2
    uint32 human_voice_volume = 3; // 人声音量
    uint32 accompaniment_volume = 4; // 伴奏音量
    bool noise_reduction_on = 5; // 音乐降噪开关，true代表打开，false代表关闭
    bool headset_return_on = 6; // 耳返开关，true代表打开，false代表关闭
    string reverberation_effect = 7; // 混响效果
}

// 私信信息
message ChatPackage {
    string send_user_id = 1; // 发送私信的user_id
    uint64 send_time = 2; // 私信发送时间, 单位 ms
    uint64 show_time = 3; // 私信曝光时间, 单位 ms
    string message_id = 4; // 记录私信id
    string message_content = 5; // 私信内容 (只上报前20个字符，方便查询)
    string receive_user_id = 6; // 接收私信的user_id
}

// 初始化方法耗时
message InitMethodCostPackage {
    string method = 1; // 方法名
    string params = 2; // 参数,一个序列化的map
}

// 视频水印相关信息
message VideoWatermarkDetailPackage {
    enum Type {
        UNKNOWN1 = 0;
        LOCAL_WATERMARK = 1; // 本地打水印
        DOWNLOAD = 2; // 废弃，服务端下载带水印版本
        NO_WATERMARK = 3; // 本地不打水印，服务端返回notNeedWatermark为true
        SERVER_WATERMARK = 4; // 服务端下载有水印
        SERVER_NO_WATERMARK = 5; // 服务端下载无水印
    }

    Type type = 1;
    uint64 duration = 2; // 视频长度，单位 ms
    uint64 cost = 3; // 耗时（本地打印，上报编码时长，服务端下载，上报下载时长），单位 ms
    uint64 length = 4; // 视频文件长度, 单位 byte
    string download_url = 5; // 视频下载地址
}

// 记录表情相关信息
message StickerInfoPackage {
    enum Type {
        UNKNOWN1 = 0;
        EMOJI = 1; // 基础表情符
        CUSTOM = 2; // 自定义表情
        ARTIST = 3; // 第三方表情
        BOMB = 4; // 表情炸弹
        OFFICIAL = 5; // 官方表情
        ACTIVITY=6;//互动表情
    }

    Type type = 1; // 表情一级类型
    string secondary_type = 2; // 二级类型
    string id = 3; // 表情id
    uint32 page_index = 4; // 所在页面位置
    uint32 index = 5; // 在某一页的位置
}

// 记录批量表情相关信息
message BatchStickerInfoPackage {
    repeated StickerInfoPackage batch_info_package = 1; // 表情相关信息
}

// 记录一键登录sdk验证结果
message ChinaMobileQuickLoginValidateResultPackage {
    enum AuthType {
        UNKONWN1 = 0; // 未知
        WIFI_GATEWAY_AUTHENTICATION = 1; // wifi网关鉴权
        GATEWAY_AUTHENTICATION = 2; // 网关鉴权
        SMS_UPWARD_AUTHENTICATION = 3; // 短信上行鉴权
        SMS_AUTHENTICATION_CODE_LOGIN = 4; // 短信验证码登录
    }

    enum Channel {
        PREFETCH = 0; // 预取
        QUICK_LOGIN = 1; // 快速登录
    }

    string result_code = 1; // 结果返回码，必填项
    string token = 2; // 临时凭证，成功时返回
    AuthType auth_type = 3; // 鉴权类型
    string auth_type_description = 4; // 鉴权类型描述
    string open_id = 5; // 用户身份唯一标识
    Channel channel = 6; // 区分参数为预取或一键登录
}

// 直播Pk参数
message LivePkPackage {
    enum EndReason {
        UNKNOWN_REASON = 0; // 未知原因 
        STREAM_END = 1; // 对方直播已关闭
        END_PLAY_IN_ADVANCE = 2; // play提前结束
        END_PUNISH_IN_ADVANCE = 3; // punish提前结束
        HEARTBEAT_TIME_OUT = 4; // 心跳超时
        PK_CLOSE = 5; // 对方Pk已关闭
        ARYA_STOP = 6; // Arya stop结束
        END_SINGAL_TIME_OUT = 7; // 结束信令超时
        NORMAL_END = 8; // 正常退出，结束页选再来一局，随机，退出
        OPPONENT_HEARTBEAT_TIME_OUT = 9; // 对手心跳超时
        CONNECT_CANCEL = 10; // 匹配或邀请过程中，主动取消 PK
        START_ROUND_PK = 11;//切换赛制
    }
    enum EntranceType {
        UNKONWN_ENTRANCE = 0; // 未知
        SEND_INVITATION = 1; // 邀请好友
        ACCEPT_INVITATION = 2; // 接受好友邀请
        RANDOM_MATCH = 3; // 随机匹配
        PLAY_AGAIN = 4; // 再来一局
        CITYWIDE_MATCH = 5; // 同城匹配
        ACQIEREMENT_MATCH = 6; // 才艺匹配
        GAME_MATCH = 7; // 游戏匹配
    }
    enum OpponentType {
        UNKONWN_OPPONENT = 0; // 未知
        FRIEND = 1; // 好友
        RANDOM_OPPONENT = 2; // 路人
    }

    enum AudienceWatchEndReason {
        UNKNOWN = 0;
        AUDIENCE_EXIT_LIVE = 1;// 观众退出直播间
        LIVE_PK_END = 2;//pk结束
        LIVE_END = 3;// 主播关播
        AUTHOR_START_ROUND_PK = 4;//主播切换赛制
        WAIT_END_SIGNAL_TIMEOUT = 5;//倒计时后因为超时而结束
    }

    OpponentType opponentType = 1; // 对手类型
    EntranceType entranceType = 2; // pk入口类型
    string user_id = 3; // 当前用户id
    string opponent_user_id = 4; // 对手用户id
    string live_stream_id = 5; // 直播流id
    string room_id = 6; // room id
    string pk_id = 7; // pk id
    uint32 pk_score = 8; // pk比分
    uint32 opponent_pk_score = 9; // 对手pk比分
    uint32 online_pk_friend_number = 10; // pk列表好友人数
    uint32 opponent_watcher_number = 11; // 对手直播观看人数
    EndReason endReason = 12; // pk结束的原因
    string displayed_opponent_watcher_number = 13; // 对手直播观看人数
    uint64 pk_close_microphone_duration = 14; // 给对方直播闭麦总时长，单位ms
    string end_feedbacks = 15; // pk结束反馈，格式为1,2,3，不同选项之间用逗号分隔
    uint64 pk_connect_timestamp = 16; // pk 进入连接状态的时间戳，点击邀请，匹配，换个对手，再来一局
    uint64 pk_play_timestamp = 17; // pk 计分开始的时间戳
    uint64 pk_end_timestamp = 18; // pk 结束的时间戳
    string interest_common = 19; // 共同兴趣标签
    uint32 pk_loser_punish_magic_face_id = 20; // pk败方使用的惩罚魔表
    uint64 audience_watch_start_time = 21;//开始观看pk时间戳，
    uint64 audience_watch_end_time = 22;// 结束观看pk时间戳，
    AudienceWatchEndReason audience_watch_end_reason =23;//观众观看结束原因
    string game_id = 24; // 游戏id
    string game_name = 25; // 游戏名称

}

// 直播资源文件信息
message LiveResourceFilePackage {
    string type = 1; // 资源文件类型
    string version = 2; // 资源文件版本
    bool is_zip_file = 3; // 是否是zip压缩文件
}

// 直播粉丝团的粉丝状态信息
message LiveFansGroupPackage {
    // deprecated
    enum FansStatus {
        NOT_IN_GROUP = 0; // 不在粉丝团内
        ACTIVE = 1; // 在粉丝团且在活跃状态
        INACTIVE = 2; // 在粉丝团但不在活跃状态
    }
    // 因为服务端改了各status值，所以这里使用新类型和新字段表示粉丝团状态
    enum FansStatusV2 {
        STATUS_UNKNOWN = 0; // 未知
        STATUS_ACTIVE = 1; // 在粉丝团且在活跃状态
        STATUS_INACTIVE = 2; // 在粉丝团但不在活跃状态
        STATUS_UNFOLLOWED = 3; // 未加入粉丝团
    }
    string fans_user_id = 1; // 粉丝的用户id
    uint64 intimacy_score = 2; // 粉丝的亲密值
    uint32 intimacy_level = 3; // 粉丝的等级
    FansStatus intimacy_status = 4; // deprecated, 粉丝的状态
    FansStatusV2 intimacy_status_v2 = 5; // 粉丝的状态
}

// 直播主播端通过私信长连接请求的相关信息
message LiveRobotRequestPackage {
    uint64 send_request_timestamp = 1; // 发送请求的时间戳
    uint64 receive_response_timestamp = 2; // 收到响应的时间戳
}

// 直播机器人语音识别过程的信息，包括本地唤醒、服务端确认真唤醒、服务端发给客户端sendStop、
// 服务端返回识别结果的时机，每个网络请求开始和结束的时间，识别的结果
message LiveRobotSpeechRecognitionPackage {
    // 识别结果
    enum RecognitionResult {
        UNKNOWN = 0; // 未知
        WAKEUP_FALSE = 1; // 假唤醒
        NOT_CLEAR = 2; // 语音不清晰
        FINISH = 3; // 识别到了指令
        NOT_UNDERSTAND = 4; // 识别失败
        NO_RESOURCE = 5; // 没有资源，目前只用于音乐场景识别到了指令但是没有歌曲资源
        NO_SUPPORT = 6; // 不支持此操作
        LOCAL_WAKEUP_TIMEOUT = 7; // 客户端在本地唤醒状态超时
        SERVER_WAKEUP_TIMEOUT = 8; // 客户端在真唤醒状态超时
        WAITING_FOR_COMMAND_TIMEOUT = 9; // 客户端在等待指令状态超时
    }
    // 指令场景，直接copy自live_mmu.proto
    enum SpeechRobotSkillType {
        SKILL_UNKNOWN = 0;
        SKILL_MUSIC = 1; // 音乐
        SKILL_PK = 2; // pk
        SKILL_JOKE = 3; // 讲笑话
        SKILL_STORY = 4; // 讲故事
        SKILL_PACKET = 5; // 红包
        SKILL_MAGIC = 6; // 魔法表情
        SKILL_FUNCTION = 7; // 功能
        SKILL_CHAT = 8; // 闲聊
        SKILL_THUMP_UP = 11; // 点赞
        SKILL_FOLLOW = 12; // 关注
        SKILL_FOREGROUND = 13; // 回到前台
        SKILL_GRAB_PACKER = 14; // 抢红包
        SKILL_COMMENT = 15; // 评论
        SKILL_REWARD = 16; // 打赏
    }

    // 指令行为，直接copy自live_mmu.proto
    enum SpeechRobotActionType {
        ACTION_UNKNOWN = 0;
        // 音乐领域
        ACTION_MUSIC_PLAY_SONG = 1001; // 播放某首歌曲
        ACTION_MUSIC_PLAY_SINGER = 1002; // 听某个歌手的歌曲
        ACTION_MUSIC_PLAY_STYLE = 1003; // 播放某种类型的歌曲
        ACTION_MUSIC_PLAY_SINGER_SONG = 1004; // 播放给定歌手的给定歌曲
        ACTION_MUSIC_PLAY = 1005; // 歌曲泛需求
        ACTION_MUSIC_LIKE = 1008; // 添加喜欢
        ACTION_MUSIC_UNLIKE = 1009; // 取消喜欢
        ACTION_MUSIC_LAST_SONG = 1013; // 上一首
        ACTION_MUSIC_NEXT_SONG = 1014; // 下一首
        ACTION_MUSIC_CHANGE_SONG = 1015; // 换一首
        // 找人pk领域
        ACTION_PK_CASUAL_PK = 2001; // 随便pk
        ACTION_PK_CITY_PK = 2002; // 同城pk
        ACTION_PK_FRIEND_PK = 2003; // 选择好友pk
        ACTION_PK_TALENT_PK = 2004; // 才艺pk
        // 讲笑话
        ACTION_JOKE_JOKE_PLAY = 3001; // 讲个笑话
        // 讲故事
        ACTION_STORY_STORY_LAY = 4001; // 讲个故事
        // 红包
        ACTION_PACKET_PACKET_OPEN = 5001; // 发个红包，追加发红包
        // 魔法表情
        ACTION_MAGIC_MAGIC_OPEN = 6001; // 打开魔法表情
        ACTION_MAGIC_MAGIC_CLOSE = 6002; // 关闭魔法表情
        ACTION_MAGIC_MAGIC_CHANGE = 6003; // 更换魔法表情
        // 功能
        ACTION_FUNCTION_FUNCTION_OPEN = 7001; // 打开某功能
        ACTION_FUNCTION_FUNCTION_CLOSE = 7002; // 关闭某功能
        ACTION_FUNCTION_VOLUME_UP = 7005; // 音量调高
        ACTION_FUNCTION_VOLUME_DOWN = 7006; // 音量调低
        ACTION_FUNCTION_PLAY_STOP = 7003; // 暂停
        ACTION_FUNCTION_PLAY_ON = 7004; // 继续播放
        ACTION_FUNCTION_EXIT_PLAY = 7007; // 退出播放
        // 闲聊
        ACTION_CHAT_CHAT_PLAY = 8001; // 闲聊

        // 观众侧定义
        ACTION_THUMP_UP = 11001; // 点赞
        ACTION_FOLLOW = 12001; // 关注当前主播
        ACTION_FOREGROUND_BACK = 13001; // 回到前台
        ACTION_GRAB_PACKET = 14001; // 抢红包
        ACTION_COMMENT = 15001; // 给主播文字评论, 无评论内容
        ACTION_COMMENT_FULL = 15002; // 给主播文字评论, 有内容
        ACTION_COMMENT_VOICE = 15003; // 给主播语音评论
        ACTION_REWARD = 16001; // 给主播打赏, 没有礼物信息, 需要推荐
        ACTION_REWARD_NAME = 16002; // 给主播打赏, 没有礼物数量
        ACTION_REWARD_COUNT = 16003; // 给主播打赏, 没有礼物名字
        ACTION_REWARD_FULL = 16004; // 给主播打赏
        ACTION_REWARD_CONFIRM = 16005; // 打赏确认
        ACTION_REWARD_CANCEL = 16006; // 取消打赏
        ACTION_REWARD_AGAIN = 16007; // 再一次打赏
        ACTION_REWARD_OTHER = 16008; // 其他 跳出或者没有学会 播放语音
        ACTION_REWARD_NOT_FOUND = 16009; // 找不到要送的礼物
        ACTION_REWARD_SILENT = 16010; // 某些情况需要静音结束session
    }

    string session_id = 1; // 语音识别会话id
    repeated LiveRobotRequestPackage request_packages = 2; // 通过私信长连接发送的各请求的信息
    uint64 local_wake_up_timestamp = 3; // 本地唤醒的时间戳
    uint64 server_wake_up_true_timestamp = 4; // 收到服务端确认真唤醒的时间戳
    uint64 server_wake_up_false_timestamp = 5; // 收到服务端确认假唤醒的时间戳
    uint64 receive_send_stop_timestamp = 6; // 收到服务端send_stop的时间戳
    uint64 finish_timestamp = 7; // 收到服务端finish或not_clear或unexpected时的时间戳
    RecognitionResult recognition_result = 8; // 识别结果
    SpeechRobotSkillType skill = 9; // 指令场景类型
    SpeechRobotActionType action = 10; // 指令行为类型
    string slots = 11; // 指令行为具体参数
}

// 直播机器人tts请求相关信息
message LiveRobotTtsPackage {
    // 分片语音最终播放结束的状态
    enum AudioStreamPlayStatus{
        UNKNOWN = 0;
        FINISHED = 1; // 成功播放完成
        CANCELED = 2; // 被打断
        FAILED = 3; // 网络请求失败等问题导致播放失败
    }
    string session_id = 1; // tts会话id
    repeated LiveRobotRequestPackage request_packages = 2; // 通过私信长连接发送的各请求的信息
    AudioStreamPlayStatus play_status = 3;
}

// 记录动态信息
message MomentMessagePackage {
    string id = 1; // 消息id
    string author_id = 2; // 作者id
    string status = 3; // 状态
    string tag_id = 4; // 话题id
    uint32 index = 5; // 位置
    string reason = 6; // 推荐理由
    string picture_id = 7; // 图片id
    string type = 8; // 内容类型
    string location = 9; // 说说位置信息
    string at_id = 10; // 说说被@的人的id
}

// 批量记录动态相关信息
message BatchMomentMessagePackage {
    repeated MomentMessagePackage moment_message_package = 1;
}

// 电喵直播-游戏/游戏分类参数
message GameZoneGamePackage {

    enum DownloadType {
        UNKNOWN1 = 0;
        SEARCH = 1;
        DETAIL = 2;
    }
    string category_id = 1; // 游戏分类id
    string category_name = 2; // 游戏分类名
    string game_id = 3; // 游戏id
    string game_name = 4; // 游戏名
    string game_score = 5; // 游戏评分
    uint32 position = 6; // 列表中位置
    bool is_fullscreen = 8; // 是否全屏
    uint32 live_room_count = 9; // 直播间数
    uint32 review_count = 10; // 鉴玩数
    string live_room_count_text = 12; // 直播间数text
    string review_count_text = 13; // 鉴玩数text
    string download_channel_name = 14; // 下载渠道名称
    string download_channel_package = 15; // 下载渠道包名
    DownloadType download_channel_type = 16; // 下载类型
    string game_package_name = 17; // 游戏包名
    uint64 game_package_size = 18; // 游戏安装包大小
}

// 音乐加载统计信息
message MusicLoadingStatusPackage {
    enum FileType {
        UNKNOWN1 = 0;
        WHOLE = 1; // 歌曲完整文件
        SNIPPET = 2; // 歌曲片段
    }

    FileType music_file_type = 1; // 歌曲文件类型
    string music_loading_mode = 2; // 歌曲加载模式
    string music_id = 3; // 歌曲id
    string music_name = 4; // 歌曲名称
    uint64 music_duration = 5; // 歌曲(或片段)时长，单位ms
    string download_url = 6; // 下载路径
    uint64 loading_duration = 7; // 加载时长，单位ms
}

// 侧边栏信息
message MorelistPackage {
    enum MorelistType {
        UNKNOWN = 0;
        MUSIC_STATION = 1; // 音悦台侧边栏
        RECO_LIVE = 2; //直播推荐侧边栏
        FOLLOW_LIVE = 3; //直播间-我的关注侧边栏
        FOLLOW_LIVE_REVISION = 4; // 直播间-关注页侧边栏改版
        FEATURED_FEED = 5; // 精选视频侧边栏
        PROFILE_FEED = 6; // 个人主页支持侧边栏feed快速浏览
        CAMERA_CHAIN_LIVE = 7; //锁链直播侧边栏
        LIVE_MORE_SQUARE = 8; //更多直播广场侧边栏
        RETURN_FIRST = 9;//返回第一个直播间
    }

    MorelistType morelist_type = 1; //侧边栏类型
    MorelistContentPackage morelist_content_package = 2; // 侧边栏里内容信息
    uint64 morelist_show_start_time = 3; //侧边栏曝光开始时间
    uint64 morelist_show_end_time = 4; //侧边栏曝光结束时间
}

//  侧边栏里内容信息
message MorelistContentPackage {
    enum ContentType {
        UNKNOWN0 = 0;
        LIVE_STREAM = 1; // 直播间
        PHOTO = 3; // 短视频
    }

    enum ContentSource {
        UNKNOWN1 = 0;
        FOLLOW = 1; //我的关注
        RECO = 2; //为你推荐
    }

    string author_id = 1; //作者ID
    string content_id = 2; //内容ID，如果是直播间，是直播间ID，如果是视频，是视频ID
    ContentType content_type = 3; //内容类型
    ContentSource content_source = 4; //内容来源
}

//直播间admin操作包
message LiveAdminOperatePackage {
    //操作类型
    enum OperateOrRecordType {
        UNKNOWN0 = 0;
        SET_SENSITIVE_WORD = 1; //敏感词设置
        SET_ADMIN = 2; //管理员设置
        ADMIN_OPERATE_RECORD = 3; //管理员操作记录
        BLACKLIST_RECORD = 4; //拉黑记录
        NO_SPEAKING_RECORD = 5; //禁言记录
        KICK_USER_RECORD = 6; //踢人记录
        SENSITIVE_WORD_RECORD = 7; //敏感词记录

    }

    //切换tab类型
    enum SwitchTabType {
        UNKNOWN1 = 0;
        CLICK = 1; //点击切换
        SLIDE = 2; //滑动切换
    }

    //来源页面类型
    enum PageSourceType {
        UNKNOWN2 = 0;
        LIVE_PUSH = 1; //直播推流页
        LIVE_ADMIN_OPERATE_RECORD_PAGE = 2; //管理员操作页
    }

    repeated OperateOrRecordType operate_type = 1; //操作类型
    OperateOrRecordType record_type = 2; //记录类型
    SwitchTabType switch_tab_type = 3; //切换tab类型
    PageSourceType page_source_type = 4; //来源页面类型
}

// 记录用户关注状态
message UserStatusPackageV2 {
    string id = 1; // 用户id
    bool followed = 2; // 是否被关注，true代表是，false代表否
}

// 记录八卦消息参数
message GossipMessagePackageV2 {

    enum Type {
        UNKNOWN1 = 0;
        PHOTO_LIKE = 1; // 作品点赞
        FOLLOW = 2; // 关注
        MOMENT = 3; // 说说
        RECOMMEND = 4; // 推荐
        USER_RECOMMEND = 5; // 用户推荐
        COMMENT = 6; // 评论
        INTEREST_PHOTO = 7; // 你可能感兴趣的作品
        PHOTO_SHARE = 8; // 分享的视频
        VIEWED_PHOTO=9;//看过的视频
        VIEWING_LIVE=10;//正在看直播
        SINGLE_PHOTO_LIKE=11;//单人点赞作品
        NEWS_AGGREGATE=12;//多动态聚合
    }

    string id = 1; // 消息id
    uint32 index = 2; // 消息在列表中的位置
    bool aggregation = 3; // 是否聚合消息，true代表是，false代表否
    uint32 count = 4; // 聚合消息条数
    repeated UserStatusPackageV2 user_status_package = 5; // 记录消息中用户id及是否被关注
    Type type = 6; // 八卦消息类型
    repeated PhotoPackage photo_package = 7; //
    string realtion_type = 8; // 关系链类型
    bool has_arrow = 9; // 右上角箭头标识
    string unread = 10; // 是否为未读消息
    string ext_params = 11; // 其他信息
    string aggregate=12;//动态是否聚合，动态所属分组维度，1-是/0-否

}

// 直播连麦信息
message LiveChatPackageV2 {
    string user_id = 1; // 当前用户id
    string peer_id = 2; // 连麦另一方id
    string live_stream_id = 3; // 直播流id
    uint32 apply_users_number = 4; // 申请连麦人数
    bool is_friend = 5; // 是否为好友
    uint64 gift_ks_coin = 6; // 送礼快币数
}

// 直播间弹幕信息
message LiveBarrageInfoPackage {
    // 弹幕位置类型
    enum BarragePosType {
        UNKNOWN = 0;
        CLOSE = 1; // 弹幕关闭
        TOP = 2; // 顶部弹幕
        HALF_SCREEN = 3; // 半屏弹幕
        FULL_SCREEN = 4; // 全屏弹幕
    }

    // 弹幕字体大小类型
    enum BarrageTextSize {
        UNKNOWN1 = 0;
        SMALLEST = 1; // 最小
        SMALL = 2; // 较小
        STANDARD = 3; // 标准
        LARGER = 4; // 较大
        LARGEST = 5; // 最大
    }
    BarragePosType barrage_pos_type = 1; // 弹幕位置类型
    BarrageTextSize barrage_text_size = 2; // 弹幕字体大小
    uint32 barrage_alpha = 3; // 弹幕透明度
}

// 直播聊天室信息
message LiveVoicePartyPackageV2 {

    // 直播聊天室的角色
    enum Role {
        UNKNOWN = 0;
        ANCHOR = 1; // 主播
        AUDIENCE = 2; // 观众
        GUEST = 3; // 嘉宾
        SINGER = 4; // KTV模式下的C位歌手 
    }

    // 嘉宾上麦序的原因
    enum EnterMicSeatReason {
        UNKNOWN5 = 0;
        GUEST_APPLY_MANUAL_ACCEPT = 1; // 嘉宾申请，主播手动同意，废弃
        GUEST_APPLY_AUTO_ACCEPT = 2; // 嘉宾申请，主播自动同意，废弃
        ANCHOR_MANUAL_INVITE = 3; // 主播手动邀请，观众需要同意，废弃
        ANCHOR_AUTO_INVITE = 4; // 主播自动邀请，废弃
        KTV_STAGE = 5; // KTV上舞台位
        MANUAL_MIC = 6; // 点击“上麦”按钮上麦
        AUTO_MIC = 7; // 观众进入房间自动上麦
        CLICK_EMPTY_MIC = 8; //点击空麦序位上麦
        MANUAL_INVITE = 9; // 主播手动邀请，观众无需同意
        JOIN_PK_TEAM = 10; // 点击加入pk战队上麦,废弃，不使用这个字段
        ANCHOR_PK_TEAM_INVITE = 11;//主播根据战队邀请
        AUDIENCE_JOIN_PK_TEAM = 12; // 观众通过战队上麦
        CLOSE_FROM_ANCHOR_LIVE_ENTER_OTHER_LIVE = 13; //从主播推流页关闭直播进入聊天室
    }

    // 嘉宾离开麦序的原因
    enum LeaveMicSeatReason {
        UNKNOWN1 = 0;
        VOICE_PARTY_END = 1; // 聊天室关闭
        LEAVE_MIC_SEAT = 2; // 主动下麦
        FORCE_LEAVE_MIC_SEAT = 3; // 被主播下麦
        KICK_OUT = 4; // 被踢出直播间
        LEAVE_LIVE = 5; // 离开直播间
        PLAY_OTHER_PLAYER = 6; //未离开直播间时播放其他播放器
        AUDIENCE_LEAVE_KTV_STAGE = 7; // KTV模式下观众离开C位
        AUTHOR_LOCK_MIC = 8; //主播封麦
        LOOK_ONLY = 9; //是否加入聊天弹层上的围观（自动上麦后点击确认弹层上的去看看下麦）
        DISCARD_JOIN_PK_TEAM = 10; // 为了跟content_wrapper统一,补一个废弃字段，不使用这个字段
        SWITCH_VOICE_PARTY_TYPE=11;//  切换聊天室玩法类型
    }

    // 主播观众离开聊天室原因
    enum LeaveVoicePartyReason {
        UNKNOWN2 = 0;
        VOICE_PARTY_END1 = 1; // 聊天室关闭
        LEAVE_LIVE1 = 2; // 离开直播间
    }

    // C位歌手离开KTV原因
    enum LeaveKTVStageReason {
        UNKNOWN3 = 0;
        BGM_END = 1; // BGM播放完成
        PLAY_NEXT = 2; // 切歌
        NO_HEARTBEAT = 3; // 丢失心跳
        LEAVE_LIVE2 = 4; // 离开直播间
        LEAVE_KTV1 = 5; // KTV关闭
        VOICE_PARTY_END3 = 6; // 聊天室关闭
        PLAY_OTHER_PLAYER1 = 7; //未离开直播间时播放其他播放器
    }

    // 主播关闭KTV原因
    enum LeaveKTVReason {
        UNKNOWN4 = 0;
        VOICE_PARTY_END2 = 1; // 聊天室关闭
        LEAVE_KTV = 2; // 主动离开KTV
        LEAVE_LIVE3 = 3; // 主播关播
    }

    // 开启聊天室的页面
    enum EntryPage {
        LIVE_PUSH = 0; // 从正常直播切换进入聊天室，老版本都是这个入口，所以作为默认值
        LIVE_COVER = 1; // 在直播封面页，选择多人语聊作为开播类型
    }

    // 聊天室模式
    enum Mode {
        CHAT = 0; // 基础聊天室模式
        KTV = 1; // KTV模式
    }

    // 触发歌手播放BGM的方式
    enum SingerPlayBgmTrigger {
        ARYA_BROADCAST = 0; // 通过 Arya 发送 NOTIFY_PLAY 的广播发送
        PERSISTENT_CONNECTION = 1; // 通过长连接消息
    }

    //麦序的状态
    enum MicStatus{
        UNKNOWN6 = 0;
        CLOSE_MIC = 1; // 默认态
        LOCK_MIC = 2; // 闭麦
        INVITE_MIC = 3; // 锁麦
    }

    //邀请按钮的位置
    enum InviteMicChannel {
        UNKNOWN7 = 0;
        ONLINE_AUDIENCE_LIST = 1; // 在线观众列表
        SEARCH = 2; // 搜索
        PERSONAL_CARD= 3; // 个人卡片
    }

    //麦位名称的类型
    enum MicType {
        NONE = 0;//没有设置麦位名称
        NAME = 1;//设置了麦位名称
        VIP = 2;//设置了贵宾位名称
    }


    string voice_party_id = 1; // 聊天室id
    Role role = 2; // 角色，分为主播，嘉宾和观众
    uint32 guest_number = 3; // 聊天室麦序嘉宾数
    bool is_mic_open = 4; // 是否是开麦状态
    string selected_mic_seat_user_id = 5; // 被选中麦序用户的id
    uint32 selected_mic_seat_user_index = 6; // 被选中麦序用户的index，从1开始
    uint32 applied_audience_number = 7; // 申请进入聊天室的观众数
    LeaveMicSeatReason leave_mic_seat_reason = 8; // 嘉宾离开麦序的原因
    LeaveVoicePartyReason leave_voice_party_reason = 9; // 主播观众离开聊天室的原因
    uint64 enter_voice_party_timestamp = 10; // 主播观众进入语音聊天室时间戳，单位是ms
    uint64 leave_voice_party_timestamp = 11; // 主播观众离开语音聊天室时间戳，单位是ms
    uint64 enter_mic_seat_timestamp = 12; // 嘉宾进入麦序时间戳，单位是ms
    uint64 leave_mic_seat_timestamp = 13; // 嘉宾离开麦序时间戳，单位是ms
    EntryPage entry_page = 14; // 开启聊天室的页面，目前只有封面页和直播页
    Mode mode = 15; // 聊天室模式
    uint64 enter_ktv_stage_timestamp = 16; // 进入KTV C位时间戳，单位是ms
    uint64 leave_ktv_stage_timestamp = 17; // 离开KTV C位时间戳，单位是ms
    uint32 ktv_order_list_song_number = 18; // KTV歌单歌曲数
    uint32 ktv_order_list_audience_number = 19; // KTV点歌观众数
    string ktv_id = 20; //KTV id
    LeaveKTVStageReason leave_ktv_stage_reason = 21; // 离开KTV C位原因
    uint32 ktv_total_sung_song_number = 22; // KTV唱过歌曲数
    uint32 ktv_total_sung_singer_number = 23; // KTV唱过歌手数
    LeaveKTVReason leave_ktv_reason = 24; // 离开KTV模式的原因
    uint32 ktv_self_order_song_number = 25; // KTV点歌观众数
    uint32 ktv_self_order_song_index = 26; // KTV观众点歌歌曲序号  
    uint32 ktv_self_sung_song_number = 27; // KTV歌手唱过的歌曲数
    bool ktv_is_singer_singing = 28; // KTV歌手是否正在唱歌
    uint64 enter_ktv_timestamp = 29; // 进入 KTV 时间戳，单位是ms
    uint64 leave_ktv_timestamp = 30; // 离开 KTV 时间戳，单位是ms
    SingerPlayBgmTrigger singer_play_bgm_trigger = 31; // 触发歌手播放 BGM 的方式
    uint64 audio_begin_timestamp = 32; // 聊天室音频段，开始时间戳，单位是ms
    uint64 audio_end_timestamp = 33; // 聊天室音频段，结束时间戳，单位是ms
    uint64 video_begin_timestamp = 34; // 聊天室视频段，开始时间戳，单位是ms
    uint64 video_end_timestamp = 35; // 聊天室视频段，结束时间戳，单位是ms
    EnterMicSeatReason enter_mic_seat_reason = 36; // 嘉宾上麦序的原因
    string channel_id = 37; // 频道id
    string channel_name = 38; // 频道name
    string topic_id = 39;//话题id
    string topic_name = 40;//话题name
    bool is_channel_select = 41;//是否频道默认选中
    MicStatus mic_status = 42; //麦序的状态
    uint32 server_mic_status = 43; //麦序态，后端下发的
    uint64 mic_set_duration = 44;//闭麦或封麦设置的时长,单位ms
    uint64 close_mic_duration = 45;// 观众在麦上的闭麦时长,单位ms
    InviteMicChannel invite_mic_channel = 46;//邀请按钮的位置
    string theater_id = 47; //放映厅id
    string teampk_id = 48;//废弃
    string team_pk_room_id = 49;//团战pkid
    MicType mic_type = 50;//离开麦位时，是否在麦位名称/VIP位置上
    uint32 mic_name_num = 51;//离开聊天室时，麦位名称的数量
    uint32 mic_vip_num = 52;//离开聊天室时，贵宾座位的数量



}

// 音乐播放统计信息
message MusicPlayStatPackageV2 {
    enum MusicPlayMode {
        UNKNOWN = 0;
        WHOLE = 1; // 播放整段
        HOT_CLIP = 2; // 播放热门片段
    }

    MusicPlayMode music_play_mode = 1; // 歌曲播放模式
    string music_id = 2; // 歌曲id
    string music_name = 3; // 歌曲名称
    string music_type = 4; // 歌曲类型
    uint32 music_index = 5; // 歌曲位置
    string singer_user_id = 6; // 歌手的uid
    uint64 music_duration = 7; // 歌曲(或片段)时长，单位ms
    uint64 played_duration = 8; // 歌曲(或片段)播放时长，单位ms
}

// 长图、图集和照片电影编辑信息
message AtlasEditPackageV2 {
    enum Type {
        UNKNOWN1 = 0;
        HORIZONTAL = 1; // 图集
        VERTICAL = 2; // 长图
        PHOTO_MOVIE = 3; // 照片电影
    }

    Type type = 1; // 类型
    uint64 imported_count = 2; // 导入图片数
    uint64 clipped_count = 3; // 裁剪后图片数
}

// 消息信息
message NotificationPackageV2 {
    string notify_id = 1; // 消息id
    string type = 2; // 消息类型
    string aggregate = 3; // 是否为聚合消息
    string unread = 4; // 是否为未读消息
    string can_follow = 5; // 是否显示关注按钮
    string follow_request_status = 6; // 关注请求状态
    string from_id = 7; // 消息来源user_id
    string text = 8; // 消息文案
    string click_area = 9; // 点击消息的区域
    string ext_params = 10; // 其他信息
    string name = 11; // 名称
    uint32 index = 12; // 位置
    float value = 13; // 值
    string status = 14; // 状态 
}

// 剧集信息
message SeriesPackageV2 {
    uint64 series_id = 1; // 剧集id (废弃)
    string series_name = 2; // 剧集名称
    uint64 author_id = 3; // 作者id
    uint32 episode_count = 4; // 当前剧集数
    bool is_series_ended = 5; // 是否剧终
    uint32 index = 6; // 剧集位置
    repeated PhotoPackage photo_package = 7; // 剧集包含视频信息
    string s_series_id = 8; // 剧集id
    string recommendation = 9; // 剧集推荐理由
    uint64 tag_id = 10; // 剧集标签
    string tag_name = 11; // 剧集标签名
}

// 批量剧集信息
message BatchSeriesPackageV2 {
    repeated SeriesPackageV2 series_package = 1; // 剧集信息
}

// 埋点更多信息
message MoreInfoPackageV2 {
    string id = 1; // id
    string identity = 2; // 标识符
    string type = 3; // 类型
    string name = 4; // 名称
    string index = 5; // 顺序
    string vlaue = 6; // 值
    string status = 7; // 状态
    string text = 8; // 文本
    string tag = 9; // 标签
    string params = 10; // 参数
    string extra_info = 11; // 服务端下发的拓展字段
}

// 批量的埋点更多信息
message BatchMoreInfoPackageV2 {
    repeated MoreInfoPackageV2 more_info_package = 1; // 埋点更多信息
}

// 创新业务，记录「标的物」相关的信息
message ItemBaseInfoPackage {
    string root_category_name = 1; // 区分创新业务（汽车/房产/家装/快聘）
    string item_id = 2; // 实体ID（职位ID/房源ID）
    string exp_tag = 3; // 推荐exptag
    string params = 4; // json2str，记录标的物的扩展信息
}


// 目标用户信息
message TargetUserPackageV2 {
    // 与作者关系
    enum RelationshipType {
        UNKNOWN1 = 0; // 陌生
        FRIEND = 1; // 好友
        FOLLOWED = 2; // 粉丝
        FOLLOWING = 3; // 关注
        MYSELF = 4; // 作者自己
        STRANGER = 5; // 陌生人
        PYML = 6; // 可能感兴趣的人
    }

    string identity = 1; // 快手用户Id
    bool is_friend = 2; // 当前用户与此用户是否是好友关系
    RelationshipType relationship_type = 3; // 与作者关系
}

// 收藏信息
message CollectionPackageV2 {
    string identity = 1; // 标识符
    string id = 2; // id
    string name = 3; // 名称
    string index = 4; // 位置
    string value = 5; // 值
    string cnt = 6; // 数量
    string status = 7; // 状态，其中0代表收藏项有效，1代表收藏项失效
    string type = 8; // 类型
    string secondary_type = 9; // 二级类型
    repeated PhotoPackage photo_package = 10; // 
    string photo_count = 11; // 包含视频的数量
    string exp_tag = 12; // 推荐后端的参数
    string llsid = 13; // 曝光id
    string params = 14; // 扩展参数,格式为json字符串
}

// 批量收藏信息
message BatchCollectionPackageV2 {
    repeated CollectionPackageV2 collection_package = 1; // 收藏信息
}

// 放置常用区分参数
message CustomV2 {
    string btn_name = 1; // 按钮名称
    string index = 2; // 顺序参数，从 0 开始
    string conversion_id = 3; // 商家平台转换工具 id
    string status = 4; // 状态
    string source = 5; // 来源
    string activity_id = 6; // 活动id
    string spread_type = 7; // 粉条推广类型（普通、电商、线下、快推）
    string order_status = 8; // 订单状态（未投放、投放中、投放结束）
    string order_id = 9; // 订单id
    string coupon_id = 10; //优惠券id
    string identity = 11; //各类 id
    string sub_business_line = 12; //子业务线
    string buy_type = 13; // 购买类型
    string taget_photo_type = 14; // 作品类型
    string promotion_type = 15; // 推广类型
    string charge_type = 16; // 计费类型
    string is_photo = 17; // 是否有作品（1：是，0：否
    string coupon_user_id = 18; //发布优惠券商家的用户id
    string brand = 19; //品牌
    string model = 20; //车型
    string tab_name = 21; //栏目名称
    string sub_tab_name = 22; //子栏目名称
    string duration = 23; //时长
    uint64 llsid = 24; // 请求id
    bool is_realtime = 25; // 开屏广告是否实时请求,true:实时请求,false:预缓存请求
    string fail_type = 26; // 开屏请求失败原因
    uint64 splash_id = 27; // 创意id,实时请求时上报
    string splash_id_new = 28; // 新创意id信息，针对adx会拼接上有效期
    string poi_id = 29; //表示地理位置 id
    string author_id = 30; // 主播id
    string photo_id = 31; // 作品id
    string live_stream_id = 32; // 直播间id
    string goods_id = 33; // 商品id
}

// 商业化业务信息
message BusinessPackageV2 {
    string business_line = 1; // 快接单、快享、快直播、信息流、品牌广告、粉条、直播粉条、商家平台
    CustomV2 custom = 2; // 放置常用区分参数
}

// 本地回忆影集信息
message LocalIntelligentAlbumPackage {
    string main_album_caption = 1; // 影集的主标题名称（根据聚类规则生成，例如"去年的今天"）
    string subtitle_album_caption = 2; // 影集的副标题（根据聚类规则生成，例如"2019年1月"或"萌宠"等等，可能为空值）
    uint32 picture_count = 3; // 影集中包含图片的数量（例如：影集是由20张图片生成的，此字段值为20）
    uint32 video_count = 4; // 影集中包含视频的数量（例如：影集是由3段视频生成的，此字段值为3）
    uint64 album_begin_timestamp = 5; // 影集包含内容的开始时间戳，单位ms
    uint64 album_end_timestamp = 6; // 影集包含内容的结束时间戳，单位ms
    string album_location = 7; // 影集内容地理位置信息描述（由客户端上报的int型地理位置转译生成，可能为空值）
    string cluster_method = 8; // 影集生成使用的聚合方法（生成影集时所使用的聚合方法的名字）
    string cluster_rule = 9; // 影集类型
    bool is_local = 10; // 是否是常驻地
    string album_label = 11; // 影集标签
    uint32 alnum_position = 12; // 影集的位置
}
// 批量本地回忆影集信息
message BatchLocalIntelligentAlbumPackage {
    repeated LocalIntelligentAlbumPackage local_intelligent_album_package = 1; //批量智能影集信息
}

// 私信用户信息包
message IMUserPackage {

    UserPackage user_package = 1;

    int32 follow_num = 2; // 关注用户数
    int32 friend_num = 3; //好友数

    int32 private_session_num = 4; //单聊会话数
    int32 group_session_num = 5; //多人聊天数会话数
    int32 unread_massage_num = 6; //未读消息数

    string params = 7; // 扩展字段（json格式）
    int32 fans_num = 8; //粉丝数

    int32 public_group_seesion_num = 9; //公开群聊会话数
    int32 session_num = 10; // 曝光的私信会话数;
}

// 私信单聊
message IMPersonalSessionPackage {

    enum RelationshipType {
        UNKNOWN1 = 0; //  陌生
        FRIEND = 1; //  好友
        FOLLOWED = 2; //  粉丝
        FOLLOWING = 3; //  关注
    }

    string receive_user_id = 1; // 接收者用户ID
    RelationshipType relationship = 2; // 关系类型

    int32 is_top = 3; //会话是否置顶（1:置顶，0未置顶）
    int32 is_mute = 4; //会话是否被屏蔽（1:屏蔽，0未屏蔽）
    int32 position = 5; //会话位置,从1开始，由上往下递增
    int32 unread_massage_num = 6; //未读消息数量
    string params = 7; // 扩展字段（json格式）
}

// 私信群聊
message IMGroupSessionPackage {

    string group_id = 1; // 群聊ID
    int32 group_type = 2; //群类型（默认3:私密群，4:公开群）

    int32 is_top = 3; //会话是否置顶（1:置顶，0未置顶）
    int32 is_mute = 4; //会话是否被屏蔽（1:屏蔽，0未屏蔽）
    int32 position = 5; //会话位置,从1开始，由上往下递增
    int32 unread_massage_num = 6; //未读消息数量

    int32 member_num = 7; //群聊成员数
    int32 user_role = 8; //用户在该群角色

    string params = 9; // 扩展字段（json格式）
    string owner_id = 10; //群主ID
    string second_tag = 11; // 群聊二级分类
    string label = 12; //群特征（服务端下发list，1:粉丝团）
}

// 私信消息信息包
message IMMessagePackage {

    enum MessageType {
        TEXT = 0; // 普通消息，文本与内嵌表情 (string)
        HTML_TEXT = 1; // HTML文本消息(目前客户端未使用)
        IMAGE = 2; // 图片     （客户端原型使用Image，log未定义）
        PROFILE = 3; // 个人主页（客户端原型使用Profile，log未定义）
        PHOTO = 4; // 用户作品  （客户端原型使用Photo，log未定义）
        OFFICIAL_FEEDBACK = 6; // 官方账号发送的文本消息（客户端原型使用Text，string)
        USER_FEEDBACK = 7; // 用户发送反馈的文本消息    （客户端原型使用RichText，log未定义）
        EMOTION = 8; // 第三方表情（客户端原型使用Emoticon，IMMessageEmoticonPackage）
        LINK = 9; // 分享的链接（客户端原型使用Link，IMMessageLinkPackage）
        MULTI_IMAGE_LINK = 10; // 多图链接消息模板     （客户端原型使用MultiImageLink，IMMessageMultiImageLinkPackage）
        TYPE_RICH_TEXT = 11; // 带type的RichText     （客户端原型使用RichText，log未定义）
        VOICE = 12; // 语音   （客户端原型使用Voice，log未定义）
        RECALLED = 13; // 被撤回的消息类型
        CUSTOM_EMOTION = 14; // 自定义表情（用户上传、骰子、动效等）（客户端原型使用EMOTICON，IMMessageEmoticonPackage）
        LOCAL_NEWS = 15; // 本地消息动态
        POKE = 16; // poke消息（客户端原型使用Poke，log未定义）
        VIDEO = 17; // 视频消息（客户端原型使用Video，log未定义）
        DIALING = 18; // 拨打语音消息 
        WHATSUP = 19; // 在干嘛消息
        PHOTO_TIME_NOW = 20; //拍此刻消息
        TIETIE = 21;//亲密贴贴消息
        GROUP_WHATSUP = 22; // 群聊在干嘛消息
        GROUP_PHOTO_TIME_NOW = 23; // 群聊拍此刻消息
        PAI_YI_PAI = 24; // 拍一拍
        TAKE_PAT = 25; // 拍一拍二期
        PLACE_HOLDER = 100; // 占位符 虾头提供的SDK依赖于该值..., 先占着吧...
        REPLACE = 101; // 替换已下发消息类型，目前是替换RECALLED撤回的消息
        NOTICE = 200; // 通知
        INVITATION_NOTICE = 201; // 群申请通知；消息体(android)在sdk的pb中, iOS还是在此文件中 Invitation
        MULTI_EMOTION_NOTICE = 1202; // 入群发消息引导通知(表情消息)
        RICH_TEXT = 1017; // 用户富文本消息
        GMSK = 1018; // 小游戏消息
        REQUEST_FOLLOW = 1032; // 求回关
        SERVICE_COMMODITY_CARD=2000;//商品卡片
        SERVICE_ORDER_CARD=2002;//订单卡片
        INFOMATION_CARD=1019;//私信通用多功能卡片
        CUSTOMER_EVALUATION_CARD = 501;//客服评价卡片
        PRE_ORDER=2003;//订单预发送卡片
        PRE_COMMODITY=2001;//商品预发送卡片
        PRE_QUESTION=2004;//预设问题卡片
        CHECK_ORDER=3000;//核单卡片
        CS_PICK_QUESTION=502;//商家开场白用户提问卡片
        CS_USER_REPLY=503;//开场白卡片的用户回复卡片
        COMMON_TOOLS=1020;//自助工具消息
        REPLY_EVALUATION=1021;//话术评价
        BEGIN_BROADCAST_CARD=1022;//开播卡片
        DISCOUNT_COUPON=1023;//优惠券
        CITE_MESSAGE=1024;//引用消息
        WELCOME_CARD=1025;//欢迎新人卡片
        BLIND_BOX=1026;//盲盒
        COMMODITY_RECOMMEND_CARD=2009;//快聘职位推荐卡片
        RETURN_CHARGE=504;//退货补运费消息
    }

    string message_id = 1; // 消息ID
    MessageType type = 2; // 消息类型
    string send_user_id = 3; // 发送私信的user_id
    string receive_user_id = 4; // 接收私信的user_id
    string group_id = 5; // 接收私信的group_id

    uint32 has_at = 6; // 是否at用户

    repeated IMMessageLinkPackage im_message_link_package = 7; //私信链接卡片信息
    IMMessageEmoticonPackage im_message_emoticon_package = 8; //私信表情
    IMMessageMultiImageLinkPackage im_message_multi_image_link_package = 9; //私信链接卡片信息

    string params = 10; // 扩展字段（json格式）

}

// 私信表情
message IMMessageEmoticonPackage {
    enum Type {
        UNKNOWN = 0;
        BASIC = 1; // 基础表情emoji
        IMAGE = 2; // 静态图片表情
        GIF = 3; // gif图片表情
        SPECIAL_EFFECT = 4; // 特效表情
        SCRIPT = 5; // 脚本表情
    }
    enum BizType {
        BIZ_UNKNOWN = 0;
        BIZ_BASIC = 1; // 基础表情
        THIRD_PARTY = 2; // 第三方
        UGC = 3; // 用户上传表情
        SCRIPT_DICE = 4; // 脚本骰子表情
        OUT_GIF = 6;//外部gif表情
    }

    string id = 1; // 表情id
    string package_id = 2; // 表情包id
    string name = 3; // 表情名
    Type type = 4; // 表情类型
    BizType biz_type = 5; // 业务类型
    string url = 6; // 跳转 url
}

// 私信链接卡片信息
message IMMessageLinkPackage {
    enum Style {
        DEFAULT = 0; // 默认样式
        BLUR = 1; // 虚化背景样式
    }
    string url = 1; // 跳转url
    string icon_url = 2; // 头像跳转url
    string title = 3; // 标题
    string desc = 4;  // 描述
    string name = 5;  // 类型描述
    Style style = 6; // 卡片样式
}

// 私信多图链接消息
message IMMessageMultiImageLinkPackage {

    enum SourceType {
        option deprecated = true;
        UNKNOWN_SOURCE_TYPE = 0;
        NORMAL = 1;
        GAME_WEB = 2; // 支持游戏引擎的WebActivity
    }

    string url = 1; // 跳转 url
    SourceType source_type = 2; // 通过scheme区分
    string source_name = 3; // 如：小游戏
    string title = 4; // 保卫胡萝卜
    string icon_url = 5; // 左上角小图标
    string desc = 6; //描述
    repeated string image_urls = 7;
    string err_image_url = 8; // 如果图片加载失败，使用此url图片
}

// 记录风格各功能项状态
message StyleStatusPackage {
    string style_id = 1; // 套系id,选择原图时为"-1"，选择自定义时为 "-2"
    string name = 2; // 套系名称
    repeated AdjustSilderItemPackage adjust_slider_item_package = 3; // 记录风格调节滑杆项
}

// 记录风格调节滑杆项
message AdjustSilderItemPackage {
    string item = 1; // 滑杆调节项，美颜为beauty,妆容为makeup
    bool is_adjusted = 2; // 是否手动调节力度
    float value = 3; // 具体拉杆值（0-1的浮点数）
}

// 商家号主页参数集
message BusinessProfilePackage {
    string visited_user_id = 1; // 商家主页被访问商家的用户 id
}

// 红包专用的package
message RedPackPackage {
    enum RedPackType {
        UNKNOWN1 = 0;
        COMMON_RED_PACK = 1; // 普通红包
        SHARE_RED_PACK = 2; // 分享红包
        FOLLOW_RED_PACK = 3; // 关注红包
        TOKEN_RED_PACK = 4; //口令红包（即猜口令，已下线）
        ARROW_RED_PACK = 5; //穿云箭红包
        THANKS_RED_PACK = 6;//感恩红包
        MILLION_RED_PACK = 7; //百万红包
        AUDIENCE_RED_PACKET = 8;//观众发红包
        FANS_GROUP_RED_PACKET = 9;//粉丝团红包
        GIFT_RED_PACKET = 10;//礼物红包
        SURPRISE_RED_PACK = 11;//彩蛋红包
        COLLECT_CARDS_RED_PACK = 12;//集卡红包(春节PK万能卡活动红包),已废弃
        FUQI_RED_PACK = 13;//福气红包(春节福气活动-福气红包),已废弃
        LIVE_ACTIVITY_COMMON_RED_PACK = 14;//秒杀类红包，包括直播活动红包、观众礼物红包、快币红包等红包类型
        LIVE_PORTAL_RED_PACK=15;//直播传送门红包(废弃)
        LIVE_COMMON_CONDITICON_REDPACK=16;//普通条件红包
        FANS_INCREASE_RED_PACK=17;// 涨粉红包（智能红包3.1，运行在广告系统之上）
        ARROW_CONDITICON_RED_PACK=18;//穿云箭条件红包
        PASSWORD_RED_PACKET=19;//口令红包（评论）
        LIVE_TASK_LEEE=20;//任务红包
        BULLET_PLAY_LEEE=21;//弹幕红包
        LIVE_FANS_GROUP_SUPER=22;//超粉团红包
        LIVE_FANS_GROUP_JINFEN=23;//金粉团红包
        LIVE_FANS_GROUP_ZUANFEN=24;//钻粉团红包
        LIVE_AUDIENCE_CONDITICON_KWAI_COIN=25;//观众端-快币红包
    }

    enum DrawPrizePage {
        UNKNOWN0 = 0;//未知
        DRAW_PRIZE_ROLL_PAGE = 1;//抽奖动画页(开奖中)
        DRAW_PRIZE_RESULT_PAGE = 2;//开奖结果页(开奖后)
        DRAW_PRIZE_BEFORE_PAGE = 3;//显示倒计时(开奖前)
        DRAW_VIEW_ALL_LIST_PAGE = 4;//查看大家手气列表页（开奖后）
        DRAW_PRIZE_BEFORE_BUTTON = 5;//开奖前页面（中间为引导参与按钮，非倒计时态)
    }

    string red_pack_id = 1; // 红包id
    uint32 red_pack_count = 2; // 主播设置的红包个数
    uint64 red_pack_time = 3; // 主播设置的红包倒计时，毫秒
    RedPackType red_pack_type = 4; //红包类型
    DrawPrizePage draw_prize_page = 5;//抽奖页面
    uint32 error_code = 6;//红包设置失败code
    string error_msg = 7;//红包设置失败信息
    string red_pack_money_count = 8;//红包的快币数
}



//直播地区小时榜package
message DistrictRankPackage {

    enum RankType {
        UNKNOWN0 = 0;//未知
        NATION = 1;//全国榜
        DISTRICT = 2;//地区榜（省份榜）
        NOT_IN_RANK = 3;//未上榜
        BUSINESS = 4;//电商榜
        APPEARANCE = 5;//颜值榜
        GZONE = 6;//游戏榜
        POPULARITY = 7;//人气榜
        AUDIENCE = 8;//观众榜
        CITY=9;//城市榜
        RECRUIT=10;//招聘榜
        GZONE_SUB_RANK = 11; // 游戏版区榜
        DANMU = 12; //弹幕玩法榜单
        NEW_RANK = 13;// 新人榜榜单
        HOUR_RANK= 14;// 小时榜
        DANMU_ANCHOR=15;//弹幕主播榜
        DANMU_GAMEPLAY=16;//弹幕玩法榜
        GIFT_RANK=17;//礼物榜
        GONGGE_RANK=18;//宫格榜
        TUANBO_RANK=19;//团播榜
        QUXIAN_RANK=20;//区县榜
    }

    string anchor_user_id = 1; // 主播uid
    string live_stream_id = 2; // 直播间id
    uint32 rank = 3;//排名
    string text = 4;//排名通知消息文本
    uint32 is_districtrank_expand_gift_dialog = 5;//地区小时榜礼物弹窗展开，1，是，0，否
    RankType rank_type = 6;//榜单类型
}


// 下载资源使用包基本信息
message ResourceProgressPackage {
    string name = 1; // 资源包名称
    double progress = 2; // 下载进度
}

// 下载资源使用包
message DownloadResourcePackage {
    repeated ResourceProgressPackage resource_progress_package = 1;
}

// 资源位下发内容对应的订单信息
message KsOrderInfoPackage {
    string ks_order_id = 1; //订单id
}

// 直播分享基本信息
message LiveSharePackage {
    //分享渠道，废弃
    enum ShareChannel {
        UNKNOWN0 = 0;
        WECHAT = 1; // 微信
        WECHAT_TIMELINE = 2; //朋友圈
        QQ = 3; //QQ
        QQ_ZONE = 4; //QQ空间
        SINA_WEIBO = 5; //微博
    }

    //分享引导触发规则
    enum GuideTriggerRule {
        UNKNOWN1 = 0;
        PLAY_LIVE_SATISFIED = 1; //观看直播时间满足要求
        SEND_GIFT = 2;//有过送礼
        SHARE_COUNT_SATISFIED = 3; //单位时间内分享量大于一定值
    }

    // 直播分享面板弹起来源
    enum ShareBoxSourceType {
        UNKNOWN2 = 0;
        DEFAULT_SHARE = 1; // 右下角默认分享
        SHARE_RED_PACK = 2; //分享红包
        RED_PACK_RAIN = 3; //红包雨分享
        THANKS_RED_PACK = 4; //感恩红包分享
        MILLION_RED_PACK = 5; //百万红包分享
        RED_PACK_RAIN_BEFORE = 6;//红包雨开奖前分享
        RED_PACK_RAIN_AFTER = 7;//红包雨开奖后分享
        QUIZ_DIALOG = 8;//答题弹窗分享
        QUIZ_REVIVE_CARD_PANEL=9;//复活卡面板分享
    }

    ShareChannel share_channel = 1; //分享渠道,废弃
    GuideTriggerRule guide_trigger_rule = 2; //分享引导触发规则
    ThirdPartyPlatform third_party_platform = 3; //分享的第三方平台
    ShareBoxSourceType share_box_source_type = 4;//直播分享面板弹起来源
}

// 记录快闪明细信息
message KuaishanVideoPackage {
    uint32 tab_id = 1; // 模板所在tab的id
    string tab_name = 2; // 模板所在tab的name
    uint32 template_id = 3; // 模板id
    string template_name = 4; // 模板name
    uint32 template_index = 5; // 模板所在位置，从0开始，切tab后位置从0开始
}

// 批量记录快闪信息
message BatchKuaishanVideoPackage {
    repeated KuaishanVideoPackage kuaishan_video_package = 1; // 快闪明细信息
}


//第三方应用/游戏信息package
message ThirdPartyAppPackage {
    string id = 1; // 第三方应用/游戏的id
    string name = 2; // 第三方应用/游戏的名称
}
//直播宠物机器人package
message LiveRobotPackage {
    // 宠物性别
    enum PetSex {
        UNKNOWN0 = 0;
        F = 1; // 女
        M = 2; // 男
    }
    //
    enum RobotType {
        UNKNOWN1 = 0;
        VOICE_ROBOT = 1;// 语音机器人
        PET_ROBOT = 2; // 宠物机器人
    }

    enum RobotStatus {
        UNKNOWN2 = 0;
        UPGRADE_INCOMPLETE = 1;//宠物机器人升级未完成
        UPGRADE_COMPLETE = 2;//宠物机器人升级已完成
        OPEN = 3;//机器人已启用
    }

    // 个人档案页技能状态
    enum MotorSkillStatus {
        UNKNOWN4 = 0;
        SKILL_UNLOCK = 1;// 解锁
        SKILL_LOCK = 2; // 未解锁
    }

   // 赚口粮任务完成状态
    enum EarnTaskStatus {
       UNKNOWN6 = 0;
       COMPLETE = 1;//已完成
       INCOMPLETE =2;//未完成
    }
    PetSex pet_sex=1;//宠物性别
    RobotType robot_type =2 ;//机器人类型
    RobotStatus robot_status = 3;//机器人升级和开启状态
    uint32 motor_skill_id = 4;// 个人档案页技能ID
    MotorSkillStatus motor_skill_status=5;//个人档案页技能状态
    uint32 earn_task_type=6;// 赚口粮任务类型
    EarnTaskStatus earn_task_status =7;// 赚口粮任务完成状态
}

// SearchResultPackageV2 search_result_package = 138; // 某条搜索结果相关信息 删除重复
message ContentPackage {
    UserPackage user_package = 1; // 用户信息
    LiveStreamPackage live_stream_package = 2; // 直播相关信息
    ScreenPackage screen_package = 3; // 屏幕方向信息
    PaymentPackage payment_package = 4; // 支付信息
    GiftPackage gift_package = 5; // 礼物相关信息
    SoundEffectPackage sound_effect_package = 6; // 音效相关信息
    MessagePackage message_package = 7; // 通用的消息
    PhotoPackage photo_package = 8; // 快手作品的信息
    VideoPackage video_package = 9; // 视频的信息
    CommentPackage comment_package = 10; // 评论的信息
    LocalMusicPackage local_music_package = 11; // 本地音乐的信息
    SearchResultPackage search_result_package = 12; // 某条搜索结果相关信息
    ThirdPartyRecommendUserListItemPackage third_party_recommend_user_list_item_package = 13; // 社交平台好友推荐列表中的一条信息 海外专用，主站请勿使用
    AtlasPackage atlas_package = 14; // 图集长图浏览信息
    BannerPackage banner_package = 15; // 作废, deprecated，banner 统计使用 ElementPackage.type = BANNER 海外专用，主站请勿使用
    ProfilePackage profile_package = 16; // 个人资料页的埋点信息
    ThirdPartyBindPackage third_party_bind_package = 17; // 第三方账号绑定
    LoginSourcePackage login_source_package = 18; // 触发注册弹窗来源行为
    PhotoPackage refer_photo_package = 19; // 来源作品信息
    TagPackage tag_package = 20; // 标签及所属作品的相关信息
    LiveBroadcastPacakge live_broadcast_package = 21; // 直播展示广播相关参数
    EffectPackage effect_package = 22; // 特效相关参数
    FeatureSwitchPackage feature_switch_package = 23; // 功能开关设置参数
    ImportMusicFromPCPackage import_music_from_pc_package = 24; // 从电脑导入音乐相关参数
    LiveAudiencePacakge live_audience_package = 25; // 直播观众相关参数
    ECommerceLinkPacakge e_commerce_link_package = 26; // 电商链接相关参数
    CommentShowPackage comment_show_package = 27; // 记录批量曝光的评论
    TagShowPackage tag_show_package = 28; // 记录批量曝光的标签
    PhotoShowPackage photo_show_package = 29; // 记录批量曝光的视频
    BatchVisitDetailPackage batch_visit_detail_package = 30; // 记录批量访问的内容相关信息
    SingerDetailPackage singer_detail_package = 31; // 记录歌手相关信息
    MusicDetailPackage music_detail_package = 32; // 记录歌曲相关信息
    BatchFeedShowCountPackage batch_feed_show_count_package = 34; // 记录各类型feed流曝光数量
    VideoEditOperationPackage video_edit_operation_package = 36; // 记录视频编辑详细操作信息
    VideoEditFeaturesStatusPackage video_edit_features_status_package = 37; // 记录视频各编辑项状态
    BatchFeatureSwitchPackage batch_feature_switch_package = 38; // 批量记录功能开关状态
    CommodityDetailPackage commodity_detail_package = 39; // 记录商品明细信息
    BatchUserPackage batch_user_package = 40; // 记录批量用户信息
    CameraRecordFeaturesStatusPackage camera_record_features_status_package = 41; // 记录视频拍摄各功能使用信息
    KSongDetailPackage k_song_detail_package = 42; // 记录k歌录制作品明细信息
    PhotoSeekBarDragPackage photo_seek_bar_drag_package = 44; // 记录视频播放进度条拖动相关信息
    MagicFaceShowPackage magic_face_show_package = 46; // 记录魔法表情曝光信息
    ProductionEditOperationPackage production_edit_operation_package = 47; // 记录视频拍摄or主播端作品编辑操作
    FeaturesElementStayLengthPackage features_element_stay_length_package = 48; // 记录功能元素停留时长
    BeautyStatusPackage beauty_status_package = 49; // 记录美颜功能项及状态值
    BatchMusicDetailPackage batch_music_detail_package = 50; // 批量记录歌曲相关信息
    BatchEditEffectPackage batch_edit_effect_package = 51; // 批量记录视频编辑功能相关信息
    BatchThemePackage batch_theme_package = 53; // 批量记录照片电影主题相关信息
    BatchCommodityDetailPackage batch_commodity_detail_package = 54; // 批量记录商品相关信息
    BatchFilterDetailPackage batch_filter_detail_package = 57; // 批量记录滤镜明细信息
    RecordInfoPackage record_info_package = 58; // 拍摄阶段的信息 海外专用，主站请勿使用
    RecordFpsInfoPackage record_fps_info_package = 59; // 编辑拍摄视频fps 海外专用，主站请勿使用
    VideoPreviewInfoPackage video_preview_info_package = 61; // 视频在生产流程中进入播放态的状态
    ImportOriginVideoPackage import_origin_video_packge = 62; // 导入视频信息
    ImportOriginPhotoPackage import_origin_photo_package = 63; // 导入图片信息
    VideoClipDetailPackage video_clip_detail_package = 65; // 裁剪页导入视频信息 海外专用，主站请勿使用
    VideoEncodingDetailPackage video_encoding_detail_package = 66; // 视频拍摄结束编码信息
    BatchSeekBarDragPackage batch_seek_bar_package = 68; // 特效页和封面页拖拽时间轴信息
    MusicAdjustDetailPackage music_adjust_detail_package = 70; // 记录音乐调节明细信息
    ChatPackage chat_package = 71; // 记录私信信息
    InitMethodCostPackage init_method_cost_package = 72; // 初始化方法耗时
    VideoWatermarkDetailPackage video_watermark_detail_package = 73; // 视频下载相关信息
    ChinaMobileQuickLoginValidateResultPackage china_mobile_quick_login_validate_result_package = 75; // 记录一键登录sdk验证结果
    BeautyMakeUpStatusPackage beauty_make_up_status_pacakge = 76; // 记录美妆各功能项状态
    BatchBeautyMakeUpStatusPackage batch_beauty_make_up_status_package = 77; // 批量记录美妆各功能项状态
    BatchStickerInfoPackage batch_sticker_info_package = 78; // 批量记录表情相关信息
    LivePkPackage live_pk_package = 79; // 直播pk相关信息
    BatchMomentMessagePackage batch_moment_message_package = 80; // 批量动态消息信息
    GameZoneGamePackage game_zone_game_package = 85; // 电喵直播游戏明细
    MusicLoadingStatusPackage music_loading_status_package = 89; // 音乐启播信息统计
    MorelistPackage morelist_package = 91; // 侧边栏信息
    LiveChatPackageV2 live_chat_package = 109; // 直播连麦信息
    LiveVoicePartyPackageV2 live_voice_party_package = 113; // 直播聊天室信息
    MusicPlayStatPackageV2 music_play_stat_package = 116; // 音乐播放统计信息
    AtlasEditPackageV2 atlas_edit_package = 123; // 长图、图集和照片电影编辑信息
    NotificationPackageV2 notification_package = 124; // 消息信息
    SeriesPackageV2 series_package = 127; // 单条剧集信息
    BatchSeriesPackageV2 batch_series_package = 128; // 批量剧集信息
    MoreInfoPackageV2 more_info_package = 129; // 埋点更多信息
    BatchMoreInfoPackageV2 batch_more_info_package = 130; // 批量的埋点更多信息
    GossipMessagePackageV2 gossip_message_package = 133; // 记录八卦消息参数
    TargetUserPackageV2 target_user_package = 136; // 目标用户信息
    CollectionPackageV2 collection_package = 140; // 收藏信息
    BatchCollectionPackageV2 batch_collection_package = 141; // 批量收藏信息
    BusinessPackageV2 business_package = 147; // 商业化业务信息
    LiveResourceFilePackage live_resource_file_package = 149; // 直播资源文件信息
    LiveBarrageInfoPackage live_barrage_info_package = 150; // 直播间弹幕信息
    LocalIntelligentAlbumPackage local_intelligent_album_package = 151; // 智能影集相关属性
    BatchLocalIntelligentAlbumPackage batch_local_intelligent_album_package = 152; // 批量智能影集信息
    IMUserPackage im_user_package = 153; // 私信用户信息包
    IMPersonalSessionPackage im_personal_session_package = 154; // 私信单聊
    IMGroupSessionPackage im_group_session_package = 155; // 私信群聊
    IMMessagePackage im_message_package = 156; // 私信消息信息包
    LiveFansGroupPackage live_fans_group_package = 157; // 直播粉丝团的粉丝状态信息
    StyleStatusPackage style_status_package = 159; // 单独上报风格明细信息
    MomentMessagePackage moment_message_package = 160; // 记录动态相关信息
    BusinessProfilePackage business_profile_package = 161; // 商家号主页参数集
    RedPackPackage red_package = 162; // 红包专用的package
    DownloadResourcePackage download_resource_package = 165; // 资源下载包信息
    LiveAdminOperatePackage live_admin_operate_package = 166; //直播间admin操作包信息
    LiveRobotSpeechRecognitionPackage live_robot_speech_recognition_package = 167; // 直播机器人场景语音识别过程信息
    LiveRobotTtsPackage live_robot_tts_package = 168; // 直播机器人场景文字转语音播报过程信息
    KsOrderInfoPackage ks_order_info_package = 169; // 资源位下发内容对应的订单信息
    LiveSharePackage live_share_package = 170; //直播分享基本信息
    BatchKuaishanVideoPackage batch_kuaishan_video_package = 172; // 批量快闪信息
    DistrictRankPackage district_rank_package =173;//直播地区小时榜信息
    ThirdPartyAppPackage third_party_app_package = 174;  //第三方应用/游戏信息package
    LiveRobotPackage live_robot_package = 175;//直播宠物机器人信息
    LiveVoicePartyTheaterPackage live_voice_party_theater_package = 177; // 直播聊天室放映厅相关信息
    LiveVoicePartyTeamPkPackage live_voice_party_teampk_package = 178; // 直播聊天室团战pk相关信息
    KuaishanVideoPackage kuaishan_video_package = 179; // 快闪明细信息
    BodyUsePackage body_user_package = 180;//美体相关信息
    DanmakuShowPackage danmaku_show_package = 181; // 记录批量上报的弹幕
    DanmakuPackage danmaku_package = 182; // 记录单条弹幕相关信息
    ItemBaseInfoPackage item_base_info_package = 183; // 创新业务，记录「标的物」相关的信息
}
