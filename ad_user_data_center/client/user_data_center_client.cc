#include "teams/ad/ad_user_data_center/client/user_data_center_client.h"

#include <memory>
#include <set>
#include <string>
#include <utility>
#include <vector>
#include <limits>
#include "dragon/src/core/common_reco_util.h"

#include "dragon/src/core/single_pipeline_executor.h"
#include "dragon/src/interop/kuiba_sample_attr.h"
#include "dragon/src/util/ktrace_util.h"
#include "ks/common_reco/util/key_sign_util.h"
#include "ks/reco_proto/action/sample_list.kess.grpc.pb.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/engine_base/auto_bid/data_frame_util.h"
namespace ks {
namespace ad_user_data_center {

void ExtractUserDataFromContextReq(const ks::platform::ReadableRecoContextInterface* dragon_context,
                                   ks::platform::CommonRecoRequest* common_req,
                                   const std::string& attr_prefix) {
  if (dragon_context == nullptr || common_req == nullptr) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "extract_user_data", "args_null",
                                       absl::StrCat(dragon_context != nullptr),
                                       absl::StrCat(common_req != nullptr));
    return;
  }
  auto sdk_table_meta =
      ks::engine_base::table_schema::KconfUtil::userDataFeatureSdkConfig()->data().GetSdkTablesMeta();
  if (sdk_table_meta == nullptr) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "extract_user_data", "meta_null");
    return;
  }
  auto it = sdk_table_meta->find(UserDataCenterClient::user_table_name);
  if (it == sdk_table_meta->end()) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "extract_user_data", "table_null");
    return;
  }
  absl::flat_hash_set<std::string> dup_user_columns;
  const auto& column_list = it->second.column_list();
  auto* common_data = common_req->mutable_common_data();
  std::vector<ks::platform::AttrValue*> attr_accessors;
  for (const auto& column : column_list) {
    if (column.column_name() == "") continue;
    if (dup_user_columns.contains(column.column_name())) continue;
    dup_user_columns.insert(column.column_name());
    auto attr_name = attr_prefix + column.column_name();
    auto attr = dragon_context->GetCommonAttr(attr_name);
    if (attr == nullptr) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "extract_user_data", "attr_not_exists",
                                         attr_name);
      continue;
    }
    attr_accessors.push_back(attr);
  }
  std::vector<ks::platform::CommonRecoResult> items;
  items.push_back(ks::platform::CommonRecoResult(0, 0, 0));
  auto fill_attr_succ_count = ks::platform::RecoUtil::BuildPackedTableColumns(
      items.begin(), items.end(), attr_accessors, common_req->mutable_common_data());
  return;
}

using AttrValueType = ks::platform::PackedItemAttrValue;
std::string MsgToJson(const ::google::protobuf::Message& msg) {
  std::string json_str;
  google::protobuf::util::JsonPrintOptions options;
  options.add_whitespace = true;
  google::protobuf::util::MessageToJsonString(msg, &json_str);
  return json_str;
}
std::set<std::string> UserDataCenterClient::GetNeededColNames() {
  std::set<std::string> col_names{};
  if (sdk_table_meta_ == nullptr) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "client", "meta_null");
    return col_names;
  }
  auto it = sdk_table_meta_->find(user_table_name);
  if (it == sdk_table_meta_->end()) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "client", "meta_not_found", user_table_name);
    return col_names;
  }
  const auto& column_list = it->second.column_list();
  for (const auto& column : column_list) {
    col_names.insert(column.column_name());
  }
  return col_names;
}

void UserDataCenterClient::BuildAndSendReq(int64_t user_id, bool enable_use_clotho_data) {
  std::set<std::string> col_names = GetNeededColNames();
  if (col_names.empty()) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "client", "col_names_empty");
    return;
  }
  BuildAndSendReqWithColNames(user_id, col_names, enable_use_clotho_data);
}

void UserDataCenterClient::BuildAndSendReqWithColNames(int64_t user_id,
                                                       const std::set<std::string>& col_names,
                                                       bool enable_use_clotho_data) {
  if (user_id == 0) return;
  colume_names_ = col_names;
  auto ret_table_columns = request_.add_return_table_columns();
  ret_table_columns->set_table(user_table_name);
  ret_table_columns->mutable_columns()->Assign(colume_names_.cbegin(), colume_names_.cend());

  request_.set_user_id(user_id);
  request_.set_request_type("default");
  request_.set_use_data_table(true);
  request_.set_request_num(100);

  auto get_column = [](const std::string& colume_name, ks::platform::DataTable* table,
                       platform::PackedItemAttrValue::ValueType value_type) {
    auto column = table->add_columns();
    column->set_value_type(value_type);
    column->set_name(colume_name);
    return column;
  };

  auto set_int_column_value = [](ks::platform::PackedItemAttrValue* column, int64_t value) {
    std::string* payload = column->mutable_value();
    payload->append(reinterpret_cast<const char*>(&value), sizeof(int64_t));
    column->add_value_length(1);
  };

  auto set_string_column_value = [](ks::platform::PackedItemAttrValue* column, std::string value) {
    std::string* payload = column->mutable_value();
    payload->append(value.data(), value.size());
    column->add_value_length(value.size());
  };

  auto* common_table = request_.mutable_common_data();
  auto table_columns_col = get_column("columns_" + user_table_name, common_table, AttrValueType::STRING);
  set_string_column_value(table_columns_col, absl::StrJoin(colume_names_, ","));

  auto table_enable_use_clotho_data_col =
      get_column(enable_use_clotho_data_name, common_table, AttrValueType::INT64);
  set_int_column_value(table_enable_use_clotho_data_col, enable_use_clotho_data);

  {
    auto item_data = request_.add_item_data();
    item_data->set_name(user_table_name);
    item_data->add_item_list()->set_item_key(user_id);
    auto user_id_column = get_column("user_id", item_data, AttrValueType::INT64);
    set_int_column_value(user_id_column, user_id);
  }
  static auto& kess_client = ks::ad_base::AdKessClient::Instance();
  auto dynamic_kess_name = kess_name;
  if (!ks::engine_base::table_schema::KconfUtil::userDataCenterKessName()->empty()) {
    dynamic_kess_name = ks::engine_base::table_schema::KconfUtil::userDataCenterKessName()->data();
  }
  auto client = kess_client.ClientOfKey<ks::platform::kess::CommonRecoLeafService>(dynamic_kess_name);
  auto options = ks::ad_base::OptionsFromMilli(client.first->time_out);
  auto* client_event_loop = kess_client.PickOneEventLoop();
  waiter_ =
      client.second->SelectOne().AsyncRecommend(options, request_, &response_, client_event_loop).GetWaiter();
}
void UserDataCenterClient::WaitAndParseResp(ks::platform::MutableRecoContextInterface* context,
                                            const std::string& attr_prefix) {
  if (!waiter_.IsValid()) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "client", "waiter_fail");
    return;
  }
  ::grpc::Status status;
  waiter_.Get(&status, &std::ignore);
  if (!status.ok()) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "client", "rpc_fail",
                                       absl::StrCat(status.error_message()));
    return;
  }
  for (const ks::platform::DataTable& item_data : response_.item_data()) {
    if (item_data.name() == user_table_name) {
      table_ = ExtractDataFrame(item_data);
      break;
    }
  }
  if (table_ == nullptr) return;
  if (context == nullptr) return;
  auto user_id = request_.user_id();
  absl::optional<size_t> index = table_->GetItemAttrIndex(user_id);
  if (!index.has_value()) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "client", "userid_not_found");
    return;
  }
  std::for_each(colume_names_.cbegin(), colume_names_.cend(), [&](const std::string& col_name) {
    auto attr = table_->GetOrInsertAttr(col_name);
    auto common_attr_name = attr_prefix + col_name;
    auto is_zero = false;
    switch (attr->value_type) {
      case ks::platform::AttrType::INT8:
      case ks::platform::AttrType::INT16:
      case ks::platform::AttrType::INT32:
      case ks::platform::AttrType::INT: {
        auto ret = attr->GetIntValue(index.value());
        auto value = ret.value();
        if (value == 0) is_zero = true;
        context->SetIntCommonAttr(common_attr_name, ret.value());
      } break;
      case ks::platform::AttrType::FLOAT16:
      case ks::platform::AttrType::FLOAT32:
      case ks::platform::AttrType::FLOAT: {
        auto ret = attr->GetDoubleValue(index.value());
        auto value = ret.value();
        if (std::abs(value) < std::numeric_limits<double>::epsilon()) is_zero = true;
        context->SetDoubleCommonAttr(common_attr_name, ret.value());
      } break;
      case ks::platform::AttrType::STRING: {
        auto ret = attr->GetStringValue(index.value()).value_or("");
        if (ret.empty()) {
          is_zero = true;
          break;
        }
        context->SetStringCommonAttr(common_attr_name, std::string(ret));
      } break;
      case ks::platform::AttrType::INT32_LIST:
      case ks::platform::AttrType::INT16_LIST:
      case ks::platform::AttrType::INT8_LIST:
      case ks::platform::AttrType::INT_LIST: {
        auto ret = attr->GetIntListValue(index.value());
        std::vector<int64_t> v{ret->begin(), ret->end()};
        if (v.empty()) is_zero = true;
        context->SetIntListCommonAttr(common_attr_name, std::move(v));
      } break;
      case ks::platform::AttrType::FLOAT_LIST:
      case ks::platform::AttrType::FLOAT32_LIST:
      case ks::platform::AttrType::FLOAT16_LIST: {
        auto ret = attr->GetDoubleListValue(index.value());
        std::vector<double> v{ret->begin(), ret->end()};
        if (v.empty()) is_zero = true;
        context->SetDoubleListCommonAttr(common_attr_name, std::move(v));
      } break;
      case ks::platform::AttrType::STRING_LIST: {
        auto ret = attr->GetStringListValue(index.value());
        std::vector<std::string> v{ret->begin(), ret->end()};
        if (v.empty()) is_zero = true;
        context->SetStringListCommonAttr(common_attr_name, std::move(v));
      } break;
      default:
        ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "client", "attr_type_error",
                                           common_attr_name);
        break;
    }
    ks::infra::PerfUtil::CountLogStash(1, "ad.user_data_center", "client", "coverage_rate", common_attr_name,
                                       absl::StrCat(is_zero));
  });
}

std::shared_ptr<ks::platform::DataFrame> UserDataCenterClient::ExtractDataFrame(
    const ks::platform::DataTable& item_data) {
  auto table = std::make_shared<ks::platform::DataFrame>("user_data");
  std::vector<platform::CommonRecoResult> items;
  for (const auto& item : item_data.item_list()) {
    auto& result = table->AddCommonRecoResult(item.item_key(), item.reason(), 0.0, 0);
    items.emplace_back(result);
  }
  for (const auto& attr_value : item_data.columns()) {
    auto* attr_accessor = table->GetOrInsertAttr(attr_value.name());
    platform::RecoUtil::ExtractPackedItemAttrToContext(attr_accessor, attr_value, &items);
  }
  return table;
}
}  // namespace ad_user_data_center
}  // namespace ks
