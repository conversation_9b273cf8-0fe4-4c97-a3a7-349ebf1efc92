# coding=utf-8
from dragonfly.modular.module import module
from dragonfly.common_leaf_dsl import LeafFlow
from dragonfly.modular.data_manager import data_manager, ab_param as ab, kconf_param as kconf
from front_server_dragonfly.utils.ad_front_server_globals import *
import front_server_dragonfly.utils.ad_front_server_utils as utils
import front_server_dragonfly.pipeline.common_admit as common_admit
from front_server_dragonfly.ad_front_server_mixin import AdFrontServerMixin
from rpc_client.ad_server_client.ad_server_client import param as ad_server_rpc_params
from rpc_client.ad_server_client.ad_server_client_stage1_nodiff import param as ad_server_rpc_params_stage1_nodiff
from rpc_client.ad_server_client.ad_server_client_stage1 import param as ad_server_rpc_params_stage1
from rpc_client.ad_server_client.ad_server_client_stage2 import param as ad_server_rpc_params_stage2
from rpc_client.ad_server_client.ad_server_client_stage3 import param as ad_server_rpc_params_stage3
from rpc_client.ad_rank_client import param as ad_rank_rpc_params
from rpc_client.ad_rank_client import param_with_attr as ad_rank_rpc_param_with_attr
from rpc_client.ad_rank_client import param_with_attr_diff as ad_rank_rpc_param_with_attr_diff
from fake_node_output_mixin import FakeNodeOutputMixin
from ad_rpc_client_mixin import AdRpcClientMixin
from dragonfly.ext.ad_dynamic_compute.ad_dynamic_compute_mixin import AdDynamicComputeMixin
from dragonfly.ext.ad_base.ad_base_mixin import AdBaseApiMixin
from dragonfly.ext.ad_router.ad_router_mixin import AdRouterApiMixin
import front_server_dragonfly.utils.ad_front_server_utils as utils

class AdFrontServerPsLab(LeafFlow, AdFrontServerMixin, AdRpcClientMixin, FakeNodeOutputMixin, AdDynamicComputeMixin, AdBaseApiMixin, AdRouterApiMixin):
  def __init__(self, **kwargs):
    LeafFlow.__init__(self, **kwargs)

  @module()
  def ad_server_use_rpc_client(self):
    self.traffic_drop_by_dynamics_cpr_threshold_enricher(
        cpr_name="universe_predict_cpm",
        job_id_name="universe_cpr_threshold_for_ab_group",
        strategy_kconf_name="ad.universeAdAutoParam.cprThreshold",
        pv_admit_attrs=[
          dict(admit_filed_name="universe_log_cpr_ab_group", admit_value_column="universe_log_cpr_ab_group"),
        ],
        output_attrs=dict(is_drop_name="universe_filtered_by_cpm_threshold",
          cpr_thr_name="universe_predict_cpm_threshold_new")
      )\
      .traffic_drop_by_dynamics_cpr_threshold_enricher(
        cpr_name="universe_predict_cpm",
        job_id_name="universe_cpr_threshold_for_ab_group_old",
        strategy_kconf_name="ad.universeAdAutoParam.cprThresholdOld",
        pv_admit_attrs=[],
        output_attrs=dict(is_drop_name="universe_filtered_by_cpm_threshold_old",
          cpr_thr_name="universe_predict_cpm_threshold_old")
      )\
      .traffic_drop_by_dynamics_cpr_threshold_enricher(
        cpr_name="universe_predict_cpm",
        job_id_name="universe_cpr_threshold_for_ab_group_for_traffic_type",
        strategy_kconf_name="ad.universeAdAutoParam.cprThresholdForTrafficType",
        pv_admit_attrs=[],
        output_attrs=dict(is_drop_name="universe_filtered_by_cpm_threshold_for_traffic_type",
          cpr_thr_name="universe_predict_cpm_threshold_for_traffic_type")
      )\
      .traffic_drop_by_dynamics_cpr_threshold_enricher(
        cpr_name="universe_predict_cpm",
        job_id_name="universe_cpr_threshold_for_ab_group_for_traffic_type_old",
        strategy_kconf_name="ad.universeAdAutoParam.cprThresholdForTrafficTypeOld",
        pv_admit_attrs=[],
        output_attrs=dict(is_drop_name="universe_filtered_by_cpm_threshold_for_traffic_type_old",
          cpr_thr_name="universe_predict_cpm_threshold_for_traffic_type_old")
      )\
      .get_abtest_params(
        biz_name='AD_DSP',
        user_id = '{{abtest_user_id}}',
        device_id = '{{abtest_device_id}}',
        session_id = '{{llsid}}',
        ab_params=[("enable_traffic_bracket_by_dynamics_cpr_by_group", False)]
      )\
      .if_("enable_traffic_bracket_by_dynamics_cpr_by_group == 1")\
        .traffic_bracket_by_dynamics_cpr_enricher(
          cpr_name="universe_predict_cpm_for_grade_quality",
          job_id_name="universe_cpr_for_grade_quality",
          bracket_map_name="dynamic_traffic_grade_level_ratio",
          default_index="3000000",
          strategy_kconf_name="ad.universeAdAutoParam.cprGradeRatio",
          pv_admit_attrs=[dict(admit_filed_name="universe_log_cpr_ab_group", admit_value_column="universe_log_cpr_ab_group")],
          output_attrs=dict(index_name="final_group_config_index_new")
        )\
      .else_()\
        .traffic_bracket_by_dynamics_cpr_enricher(
          cpr_name="universe_predict_cpm_for_grade_quality",
          job_id_name="universe_cpr_for_grade_quality",
          bracket_map_name="dynamic_traffic_grade_level_ratio",
          default_index="3000000",
          strategy_kconf_name="ad.universeAdAutoParam.cprGradeRatio",
          pv_admit_attrs=[],
          output_attrs=dict(index_name="final_group_config_index_new")
        )\
      .end_()\
      .traffic_bracket_by_dynamics_timeout_threshold_enricher(
        timeout_name="universe_timeout",
        job_id_name="universe_pid_solver_control",
        strategy_kconf_name="ad.universeAdAutoParam.pidSolverResult",
        pv_admit_attrs=[
          dict(admit_filed_name="universe_pid_solver_tp_ratio", admit_value_column="universe_pid_solver_tp_ratio"),
        ],
        output_attrs=dict(
          index_name="universe_pid_solver_group_index",
          explore_index_name="universe_pid_solver_explore_group_index"
        )
      )\
      .ad_lib_retrieval_node()\
      .if_("ad_server_ksn ~= nil") \
        .if_("enable_move_request_jk_to_front == 1") \
          .ad_rpc_client_mixer(**ad_server_rpc_params_stage3, save_async_status_to = "ad_server_result") \
        .else_if_("enable_move_request_rank_to_front == 1") \
          .ad_rpc_client_mixer(**ad_server_rpc_params_stage2, save_async_status_to = "ad_server_result") \
        .else_if_("enable_move_build_ad_response_to_front == 1") \
          .ad_rpc_client_mixer(**ad_server_rpc_params_stage1, save_async_status_to = "ad_server_result") \
        .else_if_("enable_move_build_ad_response_to_front_diff == 1") \
          .ad_rpc_client_mixer(**ad_server_rpc_params_stage1_nodiff, save_async_status_to = "ad_server_result") \
        .else_() \
          .ad_rpc_client_mixer(**ad_server_rpc_params, save_async_status_to = "ad_server_result") \
        .end_() \
      .end_()\
      .dynamic_compute_log_observer(
        biz_type_name="UNIVERSE_BIZ_TYPE",
        app_name="FRONT_APP_TYPE",
        job_id_name="universe_cpr_threshold_for_ab_group",
        enable_name="universe_enable_log_predict_cpm",
        tag_field_attrs=[dict(field_name="universe_log_cpr_ab_group", str_field_value_column="universe_log_cpr_ab_group")],
        metric_data_attrs=[
          dict(metric_name="predict_cpm",metric_value_column="universe_predict_cpm", metric_value=0.001),
          dict(metric_name="predict_cpm_for_traffic_type",metric_value_column="universe_predict_cpm_for_traffic_type", metric_value=0.001),
        ]
      )\
      .if_("enable_log_cpm_for_grade_quality_by_group == 1")\
        .dynamic_compute_log_observer(
          biz_type_name="UNIVERSE_BIZ_TYPE",
          app_name="FRONT_APP_TYPE",
          job_id_name="universe_cpr_for_grade_quality",
          enable_name="universe_enable_log_predict_cpm_grade_quality",
          tag_field_attrs=[dict(field_name="universe_log_cpr_ab_group", str_field_value_column="universe_log_cpr_ab_group")],
          metric_data_attrs=[dict(metric_name="predict_cpm",metric_value_column="universe_predict_cpm_for_grade_quality", metric_value=0.001)]
        )\
      .else_()\
        .dynamic_compute_log_observer(
          biz_type_name="UNIVERSE_BIZ_TYPE",
          app_name="FRONT_APP_TYPE",
          job_id_name="universe_cpr_for_grade_quality",
          enable_name="universe_enable_log_predict_cpm_grade_quality",
          tag_field_attrs=[],
          metric_data_attrs=[dict(metric_name="predict_cpm",metric_value_column="universe_predict_cpm_for_grade_quality", metric_value=0.001)]
        )\
      .end_()
    return self

  @module()
  def pslab_graph_test_universe_style_request_v2(self, name):
    """
    .case_("TestUniverseStyleRequestV2").pslab_graph_test_universe_style_request_v2(name = "TestUniverseStyleRequestV2")\
    """
    self\
      .user_info_collection()\
      .universe_ac_fetcher()\
      .ad_style_forward_handler()\
      .universe_style_post_proc()\
      .universe_data_post_proc()\
      .post_proc()\

    return self

  @module()
  def pslab_graph_universe_debug(self, name):
    """
    .case_("UniverseDebug").pslab_graph_universe_debug(name = "UniverseDebug")\
    """
    self\
      .user_info_collection()\
      .universe_debug_node()\
      .ad_style_forward_handler()\
      .universe_style_post_proc()\
      .universe_data_post_proc()\
      .post_proc()\

    return self

  @module()
  def pslab_graph_universe_request_v2(self, name):
    """
    .case_("UniverseRequestV2").pslab_graph_universe_request_v2(name = "UniverseRequestV2")\

    把 universe_query_mock_node 调整到 universe_style_post_proc 前
    """
    self\
      .universe_query_rewrite_node(downstream_processor = "UniverseRequestV2::dummy")\
      .universe_request_rta_node(downstream_processor = "UniverseRequestV2::dummy")\
      .universe_request_quality_reckon_node(downstream_processor = "UniverseRequestV2::dummy")\
      .universe_antispam_node(downstream_processor = "UniverseRequestV2::dummy")\
      .if_("enable_universe_get_router_user_info == 1") \
        .ad_router_get_user_info_enricher(
          save_async_status_to = "router_user_info_status"
        ) \
      .end_if_()  \
      .user_info_collection()\
      .dummy_node(
        async_status_attr = 'router_user_info_status',
        name = "dummy"
      ).get_kconf_params(
        kconf_configs = [
          {
            "kconf_key": "ad.frontserver2.enableLogCpmForGradeQualityByGroup",
            "export_common_attr": "enable_log_cpm_for_grade_quality_by_group",
            "value_type": "bool",
            "default_value": False
          }
        ]
      ) \
      .common_admit_flow()\
      .ad_server_use_rpc_client()\
      .ad_lib_retrieval_wait()\
      .ad_lib_retrieval_parse_post()\
      .after_retrieval_init()\
      .universe_cpm_threshold()\
      .universe_auction()\
      .ad_style_forward_handler()\
      .universe_bidding_node()\
      .ad_pos_select()\
      .forward_handler()\
      .forward_handler_wait()\
      .ad_filter_node()\
      .universe_agg_scene_adjust_node()\
      .universe_carousel_scene_adjust_node()\
      .universe_query_mock_node()\
      .universe_style_post_proc()\
      .universe_data_post_proc()\
      .post_proc()\

    return self

utils.bind_functions(AdFrontServerPsLab, common_admit)

utils.bind_functions(AdFrontServerPsLab, utils)

utils.AttrValueSetHelper.bind_attr_setter(AdFrontServerPsLab,
                                    AdmitInvalidType,
                                    "invalid_type",
                                    attr_type="common_attr")

utils.AttrValueSetHelper.bind_attr_setter(AdFrontServerPsLab,
                                    int,
                                    "should_be_filtered_by_cpm_thres",
                                    attr_type="common_attr")

utils.AttrValueSetHelper.bind_attr_setter(AdFrontServerPsLab,
                                    UniverseSkipFilteredByCpmThresType,
                                    "universe_skip_filtered_by_cpm_thres_type",
                                    attr_type="common_attr")
