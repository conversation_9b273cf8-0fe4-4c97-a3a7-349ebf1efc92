pv_item_table_traffic = {
    "item_tables": ["pv_item_table"],
    "remote_table_relation": {
        "wt_universe_position" : {
            "item_table_key_attr" : "position_id",
            "local_field_alias" : {
                "physical_pos_id":"physical_pos_id",
                "new_media_protection_pos_imp": "new_media_protection_pos_imp"
            },
        },
    },
    "local_field_type": {
        "physical_pos_id" : "int",
        "new_media_protection_pos_imp" : "int",
    },
}

feature_index_table_traffic = {
    "remote_service": "ad-feature-proxy-prerank",
    "request_tables": {
        "pv_item_table" : pv_item_table_traffic,
    }
}

def gen_item_to_common_attr_position():
    attrs = []
    for from_attr, to_attr in pv_item_table_traffic['remote_table_relation']['wt_universe_position']['local_field_alias'].items():
        attr = {}
        attr['from_item'] = from_attr
        attr['to_common'] = 'fi_' + to_attr
        attrs.append(attr)
    return attrs

if __name__ == "__main__":
    attrs = gen_item_to_common_attr_position()
    print(attrs)