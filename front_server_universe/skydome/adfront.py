#!/usr/bin/env python3
# coding=utf-8
import os
import sys

def find_root(p):
  if os.path.exists(p+'/KBUILD_WORKSPACE'):
    return p
  elif p == '.' or p == '/':
    raise 'could not find KBUILD_WORKSPACE from ' + os.path.abspath(__file__)
  else:
    return find_root(os.path.dirname(p))
code_root=os.path.abspath(find_root(os.path.split(os.path.abspath(__file__))[0]));
sys.path.append( code_root + '/dragon/')
sys.path.append( code_root + '/teams/ad/engine_base/dragon_node/py/')
sys.path.append( code_root + '/teams/ad/dragon_exts')
sys.path.append( code_root)
# sys.path.append(ad_dragon_path)
os.environ["CHECK_TALBE_DEPENDENCY"] = "true"
os.environ["ENABLE_EMBEDDED_FLOW"] = "true"

from dragonfly.common_leaf_dsl import LeafService
from dragonfly.modular.module import module
from dragonfly.visualization.dag import *
import config.adfront_config as adfront_config
from front_server_dragonfly.ad_api_mixin import *
from ad_front_server_pslab import AdFrontServerPsLab
from dragonfly.modular.data_manager import data_manager, ab_param as ab
from dragonfly.common_leaf_dsl import current_flow
from typing_extensions import Self
from teams.ad.grid.remote_table.remote_table_mixin import RemoteGridMixin
from schema_manager.feature_schema_traffic import *

# Request type enum
RequestType_default = 1
RequestType_main_flow = 2

# Flow definition

class AdFrontServerFlow(AdFrontServerPsLab, AdFrontMixin, RemoteGridMixin):
  def __init__(self, **kwargs):
    AdFrontServerPsLab.__init__(self, **kwargs)

  @module()
  def pslab_graph_testing(self):
    self.switch_("front_graph_name")\
      .case_("TestUniverseStyleRequestV2").pslab_graph_test_universe_style_request_v2(name = "TestUniverseStyleRequestV2")\
      .case_("UniverseDebug").pslab_graph_universe_debug(name = "UniverseDebug")\
    .end_switch_()
    return self;

  def pslab_graph_flow(self):
    self.get_abtest_params(
        biz_name='AD_DSP',
        ab_params=[
          ("front_server_common_attr_idx", 0),
          ("enable_universe_request_grid_traffic", 0),
        ],
    ) \
    .get_kconf_params(
      kconf_configs=[
        {
          "kconf_key": "ad.frontserver2.enableUniverseFrontRequestGrid",
          "export_common_attr": "enable_request_grid",
          "default_value": False
        }
      ]
    )\
    .if_("enable_universe_request_grid_traffic == 1 and enable_request_grid == 1") \
      .set_attr_value(
        common_attrs=[
          {
            "name": "request_grid_traffic",
            "type": "int",
            "value": 1
          }
        ]
      ) \
    .end_if_() \
    .if_("request_grid_traffic == 1") \
      .ad_front_process_prepare_init_mixer(
        embedded_pipeline = "context_init_check_flow"
      ) \
      .build_user_info_mixer(
        downstream_processor = "ad_front_process_prepare_post_proc_mixer"
      ) \
      .request_remote_table_opt(
          **feature_index_table_traffic,
          no_check=True
        ) \
      .copy_attr(
          item_table = 'pv_item_table',
          attrs = gen_item_to_common_attr_position()
       ) \
      .ad_front_process_prepare_post_proc_mixer(name='ad_front_process_prepare_post_proc_mixer') \
    .else_() \
      .ad_front_process_prepare_mixer(
        embedded_pipeline = "context_init_check_flow"
      )\
    .end_if_() \
    .if_("front_process_status == 1")\
      .switch_("front_graph_name")\
        .case_("UniverseRequestV2").pslab_graph_universe_request_v2(name = "UniverseRequestV2")\
        .default_().pslab_graph_testing()\
      .end_switch_()\
      .ad_front_process_post_mixer()\
    .end_if_()
    return self


# Service definition
# 定义service维度的一些配置, add_leaf_flows 方法将各个 flow 绑定到对应的 request_type 下
default_flow = AdFrontServerFlow(name="default", item_table="item_table")
with default_flow, data_manager:
  req_type = RequestType_default
  default_flow.pslab_graph_flow()

def CookService(service):
  service.return_common_attrs([
  ])
  service.return_item_attrs([
  ])
  service.CHECK_UNUSED_ATTR = False
  service.AUTO_INJECT_META_DATA = False
  current_folder = os.path.dirname(os.path.abspath(__file__))
  service.add_leaf_flows(leaf_flows=[default_flow], request_type="default", as_default=True)  \
         .draw(to_dragonfly_viz = True, mode = "remote")
         #.draw(dag_folder=current_folder)


service = LeafService(kess_name="ad-front-server")
CookService(service)

#gen_service_graph_def(service, mod = "remote" if os.environ.get('BUILD_USER_ID', '') != '' else "local")


current_folder = os.path.dirname(os.path.abspath(__file__))
service.build(output_file=os.path.join(current_folder, "../pub/ad_front_server/config/config.json"),
              extra_fields=adfront_config.config)
