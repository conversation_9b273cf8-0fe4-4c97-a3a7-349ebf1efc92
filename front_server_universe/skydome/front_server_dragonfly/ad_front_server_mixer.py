#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import LeafMixer
from dragonfly.common_leaf_util import check_arg, strict_types

#node's name AdFilterNode
class AdFilterNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_filter_node"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"ad_style_forward_handler","universe_bidding_node"})

#node's name AdLibRetrievalNode
class AdLibRetrievalNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_lib_retrieval_node"

#node's name AdLibRetrievalParsePost
class AdLibRetrievalParsePost(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_lib_retrieval_parse_post"

#node's name AdLibRetrievalWait
class AdLibRetrievalWait(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_lib_retrieval_wait"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"ad_server_result"})

#node's name AdPosSelect
class AdPosSelect(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_pos_select"

#node's name AdStyleForwardHandler
class AdStyleForwardHandler(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_style_forward_handler"
  @strict_types
  def is_async(self) -> bool:
    return True
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"ad_style_forward_handler"})

#node's name AfterRetrievalInit
class AfterRetrievalInit(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "after_retrieval_init"

#node's name ForwardHandler
class ForwardHandler(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "forward_handler"

#node's name ForwardHandlerWait
class ForwardHandlerWait(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "forward_handler_wait"

#node's name PostProc
class PostProc(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "post_proc"

#node's name UniverseAcFetcher
class UniverseAcFetcher(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_ac_fetcher"

#node's name UniverseAntispamNode
class UniverseAntispamNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_antispam_node"
  @strict_types
  def is_async(self) -> bool:
    return True
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"universe_antispam_node"})

#node's name UniverseAuction
class UniverseAuction(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_auction"

#node's name UniverseBiddingNode
class UniverseBiddingNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_bidding_node"
  @strict_types
  def is_async(self) -> bool:
    return True
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"universe_bidding_node"})

#node's name UniverseCpmThreshold
class UniverseCpmThreshold(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_cpm_threshold"

#node's name UniverseDataPostProc
class UniverseDataPostProc(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_data_post_proc"

#node's name UniverseDebugNode
class UniverseDebugNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_debug_node"

#node's name UniverseQueryMockNode
class UniverseQueryMockNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_query_mock_node"

#node's name UniverseAggSceneAdjustNode
class UniverseAggSceneAdjustNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_agg_scene_adjust_node"

#node's name UniverseCarouselSceneAdjustNode
class UniverseCarouselSceneAdjustNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_carousel_scene_adjust_node"

#node's name UniverseQueryRewriteNode
class UniverseQueryRewriteNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_query_rewrite_node"
  @strict_types
  def is_async(self) -> bool:
    return True
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"universe_query_rewrite_node"})

#node's name UniverseRequestRtaNode
class UniverseRequestRtaNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_request_rta_node"
  @strict_types
  def is_async(self) -> bool:
    return True
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"universe_request_rta_node"})

#node's name UniverseRequestQualityReckonNode
class UniverseRequestQualityReckonNode(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_request_quality_reckon_node"
  @strict_types
  def is_async(self) -> bool:
    return True
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"universe_request_quality_reckon_node"})

#node's name UniverseStylePostProc
class UniverseStylePostProc(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "universe_style_post_proc"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    # 该 processor 依赖的 common_attrs 集合
    return set({"ad_style_forward_handler"})

#node's name UserInfoCollection
class UserInfoCollection(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_info_collection"
