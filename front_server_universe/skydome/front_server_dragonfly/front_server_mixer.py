#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.common_leaf_processor import Leaf<PERSON><PERSON><PERSON>, LeafMixerEmbedFLow, embedded_flow
from dragonfly.common_leaf_dsl import LeafFlow
from dragonfly.common_leaf_util import check_arg, strict_types
from front_server_dragonfly.pipeline.context_init_check import *

class AdFrontProcessPrepareMixer(LeafMixerEmbedFLow):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_front_process_prepare_mixer"

  @embedded_flow
  def context_init_check_flow(flow : LeafFlow):
    context_init_check(flow)

class AdFrontProcessPrepareInitMixer(LeafMixerEmbedFLow):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_front_process_prepare_init_mixer"

  @embedded_flow
  def context_init_check_flow(flow : LeafFlow):
    context_init_check(flow)

class BuildUserInfoMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "build_user_info_mixer"

  @strict_types
  def is_async(self) -> bool:
    return True

class AdFrontProcessPreparePostProcMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_front_process_prepare_post_proc_mixer"

class AdFrontProcessPostMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "ad_front_process_post_mixer"

class FormulaDotMixer(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "formula_dot_mixer"

class CalcPriceRatioFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_price_ratio_formula"

class CalcFinalPriceFormula(LeafMixer):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "calc_final_price_formula"
