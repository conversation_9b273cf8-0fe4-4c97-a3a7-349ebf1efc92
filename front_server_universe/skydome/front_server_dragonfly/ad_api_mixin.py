
#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from front_server_dragonfly.front_server_mixer import *
class AdFrontMixin(CommonLeafBaseMixin):
  def ad_front_process_prepare_mixer(self, **kwargs):
    """
    AdFrontProcessPrepareMixer
    front 图化 Request 预处理部分
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .ad_front_process_prepare_mixer()
    ```
    """
    self._add_processor(AdFrontProcessPrepareMixer(kwargs))
    return self

  def ad_front_process_prepare_init_mixer(self, **kwargs):
    """
    AdFrontProcessPrepareInitMixer
    front 图化 Request 预处理部分, 在获取用户画像和请求用户正排之前
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .ad_front_process_prepare_init_mixer()
    ```
    """
    self._add_processor(AdFrontProcessPrepareInitMixer(kwargs))
    return self

  def build_user_info_mixer(self, **kwargs):
    """
    BuildUserInfoMixer
    """
    self._add_processor(BuildUserInfoMixer(kwargs))
    return self

  def ad_front_process_prepare_post_proc_mixer(self, **kwargs):
    """
    AdFrontProcessPreparePostProcMixer
    front 图化 Request 预处理部分, 在获取用户画像和请求用户正排之后
    """
    self._add_processor(AdFrontProcessPreparePostProcMixer(kwargs))
    return self

  def ad_front_process_post_mixer(self, **kwargs):
    """
    AdFrontProcessPostMixer
    front 图化 Request 后处理部分
    参数配置
    ------
    原有node算子迁移，暂无参数

    调用示例
    ------
    ``` python
    .ad_front_process_post_mixer()
    ```
    """
    self._add_processor(AdFrontProcessPostMixer(kwargs))
    return self

  def formula_dot_mixer(self, **kwargs):
    """
    FormulaDotMixer
    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名
    `dot_info`: [json] 必配项 打点详细信息

    调用示例
    ------
    ``` python
    .formula_dot_mixer(
      item_table = "ad_list_item_table",
      dot_info = [{
          "item_attr" : "NormalCpm", # 需打印字段
          "dot_name" : "cpm",
          "describe": "[=origin_cpm*ratio]"
          "formula" : "rb",  #所属公式
          "coefficient" : 1}])
    ```
    """
    self._add_processor(FormulaDotMixer(kwargs))
    return self

  def calc_price_ratio_formula(self, **kwargs):
    """
    CalcPriceRatioFormula
    price ratio 计算公式
    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 计算所用到的公式

    `output_formula_type`: [enum] 输出当前计算的公式

    调用示例
    ------
    ``` python
    .calc_price_ratio_formula(
      item_table = "ad_list_item_table",
      input_formula_type_list=["NormalPriceRatioFormula"],
      output_formula_type = "CalcNormalPriceRatioFormula")
    ```
    """
    self._add_processor(CalcPriceRatioFormula(kwargs))
    return self

  def calc_final_price_formula(self, **kwargs):
    """
    CalcFinalPriceFormula
    final price 计算公式
    ------
    参数配置
    ------
    `item_table`: [string] 必配项 item表名

    `input_formula_type_list`: [list] 计算所用到的公式

    `output_formula_type`: [enum] 输出当前计算的公式

    调用示例
    ------
    ``` python
    .calc_final_price_formula(
        item_table="ad_list_item_table",
        input_formula_type_list = [
          "CalcNormalOriginPriceFormula",
          "CalcNormalPriceRatioFormula"
        ],
        output_formula_type = "CalcNormalFinalPriceFormula")
    ```
    """
    self._add_processor(CalcFinalPriceFormula(kwargs))
    return self
