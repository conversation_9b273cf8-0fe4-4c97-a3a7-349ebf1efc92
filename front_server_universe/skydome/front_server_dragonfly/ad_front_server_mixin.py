#!/usr/bin/env python3
# coding=utf-8
import os
import sys
from dragonfly.ext.common_leaf_base_mixin import CommonLeafBaseMixin
from front_server_dragonfly.ad_front_server_mixer import *
from front_server_dragonfly.ad_front_server_enricher import *
class AdFrontServerMixin(CommonLeafBaseMixin):


  def ad_filter_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name AdFilterNode
    """
    self._add_processor(AdFilterNode(kwargs))
    return self

  def ad_lib_retrieval_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name AdLibRetrievalNode
    """
    self._add_processor(AdLibRetrievalNode(kwargs))
    return self

  def ad_lib_retrieval_wait(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name AdLibRetrievalWait
    """
    self._add_processor(AdLibRetrievalWait(kwargs))
    return self

  def ad_lib_retrieval_parse_post(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name AdLibRetrievalParsePost
    """
    self._add_processor(AdLibRetrievalParsePost(kwargs))
    return self

  def ad_pos_select(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name AdPosSelect
    """
    self._add_processor(AdPosSelect(kwargs))
    return self

  def ad_style_forward_handler(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name AdStyleForwardHandler
    """
    self._add_processor(AdStyleForwardHandler(kwargs))
    return self

  def after_retrieval_init(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name AfterRetrievalInit
    """
    self._add_processor(AfterRetrievalInit(kwargs))
    return self

  def forward_handler(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name ForwardHandler
    """
    self._add_processor(ForwardHandler(kwargs))
    return self

  def forward_handler_wait(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name ForwardHandlerWait
    """
    self._add_processor(ForwardHandlerWait(kwargs))
    return self

  def post_proc(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name PostProc
    """
    self._add_processor(PostProc(kwargs))
    return self

  def universe_ac_fetcher(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseAcFetcher
    """
    self._add_processor(UniverseAcFetcher(kwargs))
    return self

  def universe_antispam_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseAntispamNode
    """
    self._add_processor(UniverseAntispamNode(kwargs))
    return self

  def universe_auction(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseAuction
    """
    self._add_processor(UniverseAuction(kwargs))
    return self

  def universe_bidding_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseBiddingNode
    """
    self._add_processor(UniverseBiddingNode(kwargs))
    return self

  def universe_cpm_threshold(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseCpmThreshold
    """
    self._add_processor(UniverseCpmThreshold(kwargs))
    return self

  def universe_data_post_proc(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseDataPostProc
    """
    self._add_processor(UniverseDataPostProc(kwargs))
    return self

  def universe_debug_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseDebugNode
    """
    self._add_processor(UniverseDebugNode(kwargs))
    return self

  def universe_query_mock_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseQueryMockNode
    """
    self._add_processor(UniverseQueryMockNode(kwargs))
    return self

  def universe_query_rewrite_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseQueryRewriteNode
    """
    self._add_processor(UniverseQueryRewriteNode(kwargs))
    return self

  def universe_request_rta_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseRequestRtaNode
    """
    self._add_processor(UniverseRequestRtaNode(kwargs))
    return self

  def universe_request_quality_reckon_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseRequestQualityReckonNode
    """
    self._add_processor(UniverseRequestQualityReckonNode(kwargs))
    return self

  def universe_style_post_proc(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseStylePostProc
    """
    self._add_processor(UniverseStylePostProc(kwargs))
    return self

  def user_info_collection(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UserInfoCollection
    """
    self._add_processor(UserInfoCollection(kwargs))
    return self

  def universe_agg_scene_adjust_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseAggSceneAdjustNode
    """
    self._add_processor(UniverseAggSceneAdjustNode(kwargs))
    return self

  def universe_carousel_scene_adjust_node(self, **kwargs):
    """
    auto generate by dragon_node.cc for node's name UniverseCarouselSceneAdjustNode
    """
    self._add_processor(UniverseCarouselSceneAdjustNode(kwargs))
    return self

  def client_limit_check(self, **kwargs):
    '''
    AdClientLimitCheckEnricher
    判断是否命中限流
    ------
    参数配置
    `qps_attr_column`: 输入pb列 message指针
    `tag`: 标识 需要保证不同策略下不同
    `output_attr_column`: 需要填充的common_attr列 要保证pb中name和attr_name一致
    ------
    调用示例
    ------
    ``` python
    self.client_limit_check(
      qps_attr = '',
      token_num_attr = '',
      tag = '',
      output_attr = 'is_limit',
    )
    ```
    '''
    self._add_processor(AdClientLimitCheckEnricher(kwargs))
    return self

  def get_service_info(self, **kwargs):
    '''
    ServiceDataEnricher
    获取服务相关信息
    ------
    参数配置
    `instance_num_attr`: [string] 实例数
    `prod_instance_num_attr`: [string] prod 实例数
    `preonline_instance_num_attr`: [string] preonline 实例数
    `prt_instance_num_attr`: [string] prt 实例数
    `ksp_service_group_attr`: [string] group 分组
    `kws_service_stage_attr`: [string] stage
    `kws_service_paz_attr`: [string] paz
    `kws_service_az_attr`: [string] az
    `kws_service_region_attr`: [string] 区域
    ------
    调用示例
    ------
    ``` python
    self.get_service_info(
      instance_num_attr = 'instance_num'
    )
    ```
    '''
    self._add_processor(ServiceDataEnricher(kwargs))
    return self

  def flow_check(self, **kwargs):
    '''
    FlowCheckEnricher
    流量判断算子 目前支持 ContainsFlowTypeAnyOf AcceptFlowType 接口
    ------
    参数配置
    `method`: [string] 方法
      - method = 'AcceptFlowType'
        `output_attr`: [string] 输出列
        `flow_type_attr`: [string] 当前pv的 flow_type

      - method = 'ContainsFlowTypeAnyOf'
        `output_attr`: [string] 输出列
        `cur_flow_types`: [string list] 允许的 flow_type
        `cur_deployment`: [string] 当前 ksn 的 deployment
    ------
    调用示例
    ------
    ``` python
    self.flow_check(
      method = 'ContainsFlowTypeAnyOf',
      output_attr = 'flow_type_match'
    )
    ```
    '''
    self._add_processor(FlowCheckEnricher(kwargs))
    return self

  def rank_cache_check(self, **kwargs):
    '''
    RankCacheStatusEnricher
    精排缓存检查
    ------
    参数配置
    `key_attr`: [string] key 列名
    ------
    调用示例
    ------
    ``` python
    self.rank_cache_check(
      key_attr = 'ud_cache_key',
      output_attr = 'is_hit_rank_cache'
    )
    ```
    '''
    self._add_processor(RankCacheStatusEnricher(kwargs))
    return self



