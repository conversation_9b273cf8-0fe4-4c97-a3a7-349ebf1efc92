#include "teams/ad/front_server_universe/engine/node/post_proc.h"

#include <algorithm>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/container/flat_hash_set.h"
#include "absl/strings/substitute.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "dynamic_kafka_client/dynamic_kafka_client.h"
#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/ad_base/src/log_record/ack_util.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/redis/redis_util.h"
#include "teams/ad/ad_base/src/util/fill_realtime_user_info.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_pack_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/universe_delivered_qpon_info.pb.h"
#include "teams/ad/engine_base/bg_thread/flow_statistics_minute.h"
#include "teams/ad/engine_base/budget_common/server_show_manager.h"
#include "teams/ad/engine_base/diff_test_tool/diff_test_tool.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/utils/ad_utility.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/utility/front_logging.h"
#include "teams/ad/front_server_universe/util/utility/ksn_util.h"
#include "redis_proxy_client/redis_pipeline_client.h"

using ks::ad_base::ack_util::AckUtil;
using ks::ad_base::ack_util::CandidateLogType;
using kuaishou::ad::AdResult;
using kuaishou::ad::CommonTypeEnum;
using kuaishou::ad::ContextInfoCommonAttr;
using kuaishou::ad::algorithm::UsedItem;
using kuaishou::ad::algorithm::UsedItemForDebug;

DECLARE_bool(enable_no_diff_check);
extern int32_t front_grpc_port;

namespace ks::front_server {

void PostProc::Initialize(ContextData* context) {
  auto* response_data = context_->GetMutableContextData<ContextData>();
  now_ts_ = base::GetTimestamp();
  now_ms_ = now_ts_ / 1000;
  model_cmd_ = FrontKconfUtil::model_cmd();

  session_data_ = context;
  dsp_ad_response_ = session_data_->mutable_ad_response();

  ad_list_ = session_data_->mutable_ad_list();
  dsp_ad_request_ = session_data_->mutable_ad_request();
  local_llsid = session_data_->get_llsid();
  is_invalid_context_ack = false;
  has_send_all_station = false;
}

bool PostProc::ProcessInner() {
  TimeRecorder timer(context_->GetMutableContextData<ContextData>(),
                     static_cast<int32_t>(kuaishou::ad::AdFrontNodeType::FRONT_NODE_FRONT_SERVICE_TYPE));
  auto* context = context_->GetMutableContextData<ContextData>();
  if (context == nullptr) {
    TLOG(ERROR) << "get ranking result failed, context is null";
    return false;
  }
  int64 start_ts = base::GetTimestamp();

  // step 1: 初始化
  Initialize(context);

  // step 3: 给新的 predict server 返回结果
  SendAckData();

  // 把联盟小系统应用商店产生的数据发送到 kafka
  SendUniverseTinyDataToKafka();

  // 更新 qcpx 下发券信息的 redis
  UpdateUniverseDeliveredQponRedis();

  // step 4: 后处理，做统计等收尾工作
  Statistics();

  // ========== 混排服务迁移 BEGIN ==========
  // 对于需要请求混排服务的请求，在这里打包混排服务需要的数据
  SetTransparentDataForAdPackService();
  // ========== 混排服务迁移 END ===========

  // 处理给上游的 trace 信息
  FillSimpleTraceInfo();
  LogPreviewInfo();

  PERF_FINAL_AD_LIST(session_data_);
  Clear();
  auto end_ts = base::GetTimestamp();
  falcon::Stat("front_server.post_proc_latency", end_ts - start_ts);
  context->dot_perf->Interval(end_ts - start_ts, "ad_server_postproc_latency");
  return true;
}

void PostProc::FillSimpleTraceInfo() {
  if (KS_UNLIKELY(session_data_ == nullptr || session_data_->get_front_server_response() == nullptr)) {
    return;
  }
  ::google::protobuf::RepeatedPtrField<::kuaishou::ad::DspAdInfo>* ad_dsp_ptr = nullptr;
  // 只处理三大接口的结果
  if (session_data_->get_front_server_response()->explore_response().ad_dsp_size() > 0) {
    ad_dsp_ptr = session_data_->mutable_front_server_response()->mutable_explore_response()->mutable_ad_dsp();
  } else if (session_data_->get_front_server_response()->nearby_response().ad_dsp_size() > 0) {
    ad_dsp_ptr = session_data_->mutable_front_server_response()->mutable_nearby_response()->mutable_ad_dsp();
  } else if (session_data_->get_front_server_response()
                 ->follow_response()
                 .follow_dsp_response()
                 .ad_dsp_size() > 0) {
    ad_dsp_ptr = session_data_->mutable_front_server_response()
                     ->mutable_follow_response()
                     ->mutable_follow_dsp_response()
                     ->mutable_ad_dsp();
  }
  if (ad_dsp_ptr != nullptr && KS_LIKELY(session_data_->get_ad_request() != nullptr)) {
    for (int i = 0; i < ad_dsp_ptr->size(); i++) {
      auto* ad_dsp = ad_dsp_ptr->Mutable(i);
      ks::engine_base::FillDspSimpleTrace(*(session_data_->get_ad_request()), ad_dsp);
    }
  }
}

// 只有联盟小系统的流量才执行这个函数
void PostProc::SendUniverseTinyDataToKafka() {
  // 联盟小系统流量发
  if (!session_data_->get_ud_is_universe_tiny_flow()) {
    return;
  }

  static std::shared_ptr<ad_base::AdKafkaProducer> kafka_producer = nullptr;
  static std::once_flag flag;
  std::call_once(flag, [] {
    kafka_producer = std::make_shared<ad_base::AdKafkaProducer>();
    kafka_producer->InitWithTopic("universe_app_store_data", engine_base::DependDataLevel::WEAK_DEPEND);
  });
  if (nullptr == kafka_producer) {
    LOG(ERROR) << "kafka producer is nullptr, topic: universe_app_store_data";
    return;
  }
  kuaishou::ad::UniverseTinyDataInfo universe_tiny_data_info;
  // 给 message 的字段赋值
  universe_tiny_data_info.set_llsid(session_data_->get_llsid());
  universe_tiny_data_info.set_visitor_id(session_data_->get_ad_request()->ad_user_info().id());
  universe_tiny_data_info.set_device_id(session_data_->get_ad_request()->ad_user_info().device_id());
  if (session_data_->get_ad_request()->universe_ad_request_info().imp_info().size() > 0) {
    universe_tiny_data_info.set_pos_id(
        session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).position_id());
  }
  universe_tiny_data_info.set_app_id(session_data_->copy_app_id());
  int32_t ad_style = session_data_->get_ud_ad_style();
  universe_tiny_data_info.set_ad_style(ad_style);
  universe_tiny_data_info.set_cooperation_mode(
      static_cast<int32>(session_data_->get_ad_request()->universe_ad_request_info().cooperation_mode()));
  universe_tiny_data_info.set_medium_uid(
      session_data_->get_ad_request()->universe_ad_request_info().medium_uid());
  universe_tiny_data_info.set_origin_search_query(
      session_data_->get_front_server_request()->universe_request().query());
  universe_tiny_data_info.set_search_query(session_data_->copy_ud_search_query());
  universe_tiny_data_info.mutable_query_feature()->CopyFrom(
      session_data_->get_front_server_request()->universe_request().query_feature());
  for (const auto& item : session_data_->get_ad_response()->app_name_prefix_item_list()) {
    auto *app_name_prefix_item = universe_tiny_data_info.add_app_name_prefix_item_list();
    app_name_prefix_item->set_app_name(item.app_name());
    app_name_prefix_item->set_package_name(item.package_name());
    for (const auto& category : item.categorys()) { app_name_prefix_item->add_categorys(category); }
    for (const auto& match_type : item.match_types()) { app_name_prefix_item->add_match_types(match_type); }
    app_name_prefix_item->set_score(item.score());
  }

  // target_host
  if (!ad_list_->Empty()) {
    for (const auto& ad : ad_list_->Ads()) {
      if (ad && ad->GetResult() && ad->GetResult()->ad_source_type() == kuaishou::ad::AdSourceType::DSP) {
        const auto& online_join_params_transparent =
            ad->GetResult()->ad_deliver_info().online_join_params_transparent();

        const std::string& target_hostname = (online_join_params_transparent.target_shard_type() !=
                                              kuaishou::ad::AdEnum_TargetServerShardType_COLD_SHARD)
                                                 ? online_join_params_transparent.target_hostname()
                                                 : online_join_params_transparent.cold_target_hostname();
        universe_tiny_data_info.set_target_hostname(target_hostname);
        break;
      }
    }
  }

  // pkg_name
  const auto& athena_request = session_data_->get_front_server_request()->universe_request();
  const auto& athena_device_info = athena_request.device_info();
  for (const auto& item : athena_device_info.app_package()) {
    universe_tiny_data_info.add_pkg_name_list(item.pkg_name());
  }
  // 将 universe_tiny_data_info 序列化写 kafka
  std::string message;
  universe_tiny_data_info.SerializeToString(&message);
  kafka_producer->Produce(message);
  // 打点记录
  session_data_->dot_perf->Count(1, "universe_tiny_data_info_kafka_num");
}

void PostProc::SendAckData() {
  // 压测流量不 ack
  if (session_data_->IsTestRequest()) {
    return;
  }
  const auto& spdm_ctx = session_data_->get_spdm_ctx();
  // 联盟发送逻辑
  ks::ad_base::ad_ack_info_record::AdAckInfoRecord ack_recorder(
      dsp_ad_request_->ad_request_flow_type(), kuaishou::ad::AdEnum_DetailRequestFlowType_DETAIL_FLOW_UNKNOWN,
      session_data_->get_llsid(), session_data_->get_user_id(), 0);
  /*** 旧的 ack 逻辑 ***/
  SetAckInfoForAdPackServiceUniverse(&ack_recorder);
  SendDspAckDataFlowNewUniverse();

  DetailCPMAckForPredictServer();
  UnionLtrAckForPredictServer();

  auto new_creative_ack_kafka_llsid_set = FrontKconfUtil::newCreativeAckKafkaLlsidSetUniverse();
  if (new_creative_ack_kafka_llsid_set &&
      new_creative_ack_kafka_llsid_set->find(session_data_->get_llsid() % 100) !=
          new_creative_ack_kafka_llsid_set->end()) {
    AckForNewCreativeResponseKafka();
  }
}

void PostProc::SendDspAckDataFlowNewUniverse() {
  std::string cluster_name = "adAckInfoUniverse";
  if (session_data_->get_llsid() < 0) {
    // 会有 llsid 为 -1 的请求，需要在这里过滤掉
    return;
  }
  if (ad_list_->Size() <= 0) {
    return;
  }
  uint64_t delivery_time = base::GetTimestamp() / 1000;
  // front 发送的 ack 数据转化为 新结构
  kuaishou::ad::algorithm::SampleSpec sample_spec;
  AckUtil::ConvertUsedItemToSampleSpec(session_data_->get_used_item(), &sample_spec);
  // 发送到 kafka 必须填充用户信息
  // 旧逻辑写 redis 时没有填用户信息，这里需要重新填一下
  AckUtil::FillUserInfo(dsp_ad_request_->ad_user_info(), dsp_ad_request_->reco_user_info(),
                        true, &sample_spec, "dsp");
  AckUtil::FillSampleSpecControlMeta(&sample_spec, true);
  // ack 正负样本分离，这里发送 采样样本到 kafka（可能包含正样本）
  const auto llsid_str = absl::StrCat(session_data_->get_llsid());
  sample_spec.set_engine_delivery_timestamp(delivery_time);
  static bool is_yz_deploy = ks::infra::KEnv::GetKWSInfo()->GetAZ() == "YZ";
  auto switch_ratio = ks::ad_base::AdRandom::GetInt(0, 99);
  auto candidate_log_type = CandidateLogType::UNIVERSE_DSP_CANDIDATE_LOG;
  if (switch_ratio < FrontKconfUtil::ackKafkaYzSwitchRatioNew() && is_yz_deploy) {
    candidate_log_type = CandidateLogType::UNIVERSE_DSP_CANDIDATE_LOG_YZ;
  }
  AckUtil::AckSampleLogSendKafka(
      sample_spec, "dsp", llsid_str, candidate_log_type,
      static_cast<int32_t>(ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request()))),
      front_grpc_port);
  // 如果不是 混排流量，需要在 front 写正样本到 redis
  if (!ks::ad_base::IsMixRankSupportType(session_data_->get_sub_page_id())) {
    std::vector<AdResult*> results;
    std::vector<ItemMeta> positive_items;
    // front 发送的正样本数据
    kuaishou::ad::algorithm::SampleSpec positive_samples;
    ad_list_->GetAllWithFunc([this, &results](kuaishou::ad::AdResult* ad_result, RankAdCommon* ad_common) {
      results.push_back(ad_result);
    });

    for (const auto& selected_rank_info : selected_rank_infos_) {
      ItemMeta item_meta;
      item_meta.set_item_id(selected_rank_info.rank_info.item_id());
      item_meta.mutable_rank_info()->CopyFrom(selected_rank_info.rank_info);
      positive_items.push_back(item_meta);
    }
    AckUtil::FillServerShowAckInfo(results, positive_items, session_data_->get_user_id(),
                                   session_data_->get_sub_page_id(), &positive_samples);
    positive_samples.set_engine_delivery_timestamp(delivery_time);
    AckUtil::AckServerShowSendRedis(
        positive_samples, "dsp", absl::StrCat(session_data_->get_llsid()),
        static_cast<int32_t>(ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request()))),
        cluster_name);
    // 发送 Debug 数据流
    kuaishou::ad::algorithm::UsedItemForDebug used_item_debug;
    AckUtil::FillAckDataForDebug(&used_item_debug, positive_samples, session_data_->get_llsid(), true);
    AckUtil::AckServerShowDebugSendKafka(
        used_item_debug, session_data_->get_llsid(), CandidateLogType::ACK_DEBUG_LOG, false,
        static_cast<int32_t>(ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request()))),
        front_grpc_port);
    LOG_EVERY_N(INFO, FrontKconfUtil::logFreqAckRebuild())
        << " , llsid: " << session_data_->get_llsid() << ", sample_spec: " << sample_spec.ShortDebugString()
        << "\n universe ack rebuild, llsid: " << session_data_->get_llsid()
        << ", positive_samples: " << positive_samples.ShortDebugString()
        << "\n universe ack rebuild, llsid: " << session_data_->get_llsid()
        << ", used_item: " << session_data_->get_used_item().ShortDebugString();
  }
  // 最后一步: 填充透传数据给 ad_pack 的时候
  // 使用 SampleSpec 结构构造 front 返回的所有广告的 ack 数据
  // 这个数据要透传到 adpack, 处理时需要用到 selected_rank_infos_
  // 最后一步逻辑见:SetTransparentDataForAdPackService
}
void PostProc::SendDspAckDataFlowNewUniverseLtr() {
  std::string cluster_name = "adAckInfoUniverseLtr";
  if (session_data_->get_llsid() < 0) {
    // 会有 llsid 为 -1 的请求，需要在这里过滤掉
    return;
  }
  if (ad_list_->Size() <= 0) {
    return;
  }
  int sample_tag = session_data_->get_union_ltr_sample_tag();
  auto int_common_attr =
      session_data_->mutable_union_ltr_used_item()->mutable_context()->add_info_common_attr();
  int_common_attr->set_name_value(kuaishou::ad::ContextInfoCommonAttr::UNIVERSE_PV_SAMPLE_TAG);
  int_common_attr->set_type(kuaishou::ad::CommonTypeEnum::INT_ATTR);
  int_common_attr->set_int_value(sample_tag);
  session_data_->dot_perf->Count(1, "front_pv_sample_tag", std::to_string(sample_tag));
  if (SPDM_enableUniverseSampleCachedTag()) {
    int32_t cache_type = session_data_->get_is_universe_cached_res() ? 4 : 2;
    auto cpm_tag_attr =
        session_data_->mutable_union_ltr_used_item()->mutable_context()->add_info_common_attr();
    cpm_tag_attr->set_name_value(kuaishou::ad::ContextInfoCommonAttr::FRONT_SERVER_FILTERED_TYPE);
    cpm_tag_attr->set_type(kuaishou::ad::CommonTypeEnum::INT_ATTR);
    cpm_tag_attr->set_int_value(cache_type);
  }
  // front 发送的 ack 数据转化为 新结构
  kuaishou::ad::algorithm::SampleSpec sample_spec;
  AckUtil::ConvertUsedItemToSampleSpec(session_data_->get_union_ltr_used_item(), &sample_spec);
  // 发送到 kafka 必须填充用户信息
  // 旧逻辑写 redis 时没有填用户信息，这里需要重新填一下
  //  清空 reco_user_info 节省存储空间
  kuaishou::newsmodel::UserInfo reco_user_info_cp;
  reco_user_info_cp.Clear();
  AckUtil::FillUserInfo(dsp_ad_request_->ad_user_info(), reco_user_info_cp,
                        true, &sample_spec, "dsp");
  AckUtil::FillSampleSpecControlMeta(&sample_spec, true);
  // ack 正负样本分离，这里发送 采样样本到 kafka（可能包含正样本）
  const auto llsid_str = absl::StrCat(session_data_->get_llsid());
  static bool is_yz_deploy = ks::infra::KEnv::GetKWSInfo()->GetAZ() == "YZ";
  auto candidate_log_type = CandidateLogType::UNIVERSE_DSP_LTR_CANDIDATE_LOG;
  auto switch_ratio = ks::ad_base::AdRandom::GetInt(0, 99);
  if (switch_ratio < FrontKconfUtil::ackKafkaYzSwitchRatioNew() && is_yz_deploy) {
    candidate_log_type = CandidateLogType::UNIVERSE_DSP_LTR_CANDIDATE_LOG_YZ;
  }
  AckUtil::AckSampleLogSendKafka(
      sample_spec, "dsp", llsid_str, candidate_log_type,
      static_cast<int32_t>(ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request()))),
      front_grpc_port);
  // 如果不是 混排流量，需要在 front 写正样本到 redis
  if (!ks::ad_base::IsMixRankSupportType(session_data_->get_sub_page_id())) {
    std::vector<AdResult*> results;
    std::vector<ItemMeta> positive_items;
    // front 发送的正样本数据
    kuaishou::ad::algorithm::SampleSpec positive_samples;
    ad_list_->GetAllWithFunc([this, &results](kuaishou::ad::AdResult* ad_result, RankAdCommon* ad_common) {
      results.push_back(ad_result);
    });
    for (const auto& selected_rank_info : selected_rank_infos_) {
      ItemMeta item_meta;
      item_meta.set_item_id(selected_rank_info.rank_info.item_id());
      item_meta.mutable_rank_info()->CopyFrom(selected_rank_info.rank_info);
      positive_items.push_back(item_meta);
    }
    AckUtil::FillServerShowAckInfo(results, positive_items, session_data_->get_user_id(),
                                   session_data_->get_sub_page_id(), &positive_samples);
    AckUtil::AckServerShowSendRedis(
        positive_samples, "dsp", absl::StrCat(session_data_->get_llsid()),
        static_cast<int32_t>(ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request()))),
        cluster_name);
    LOG_EVERY_N(INFO, FrontKconfUtil::logFreqAckRebuild())
        << "universe ack rebuild ltr, llsid: " << session_data_->get_llsid()
        << ", ltr sample_spec: " << sample_spec.ShortDebugString()
        << "\n universe ack rebuild ltr, llsid: " << session_data_->get_llsid()
        << ", ltr positive_samples: " << positive_samples.ShortDebugString()
        << "\n universe ack rebuild ltr, llsid: " << session_data_->get_llsid()
        << ", union_ltr_used_item: " << session_data_->get_union_ltr_used_item().ShortDebugString();
  }
  // 最后一步: 填充透传数据给 ad_pack 的时候
  // 使用 SampleSpec 结构构造 front 返回的所有广告的 ack 数据
  // 这个数据要透传到 adpack, 处理时需要用到 selected_rank_infos_
  // 最后一步逻辑见:SetTransparentDataForAdPackService
}

void PostProc::Statistics() {
  // 统计结果中 adx 广告数量
  bool has_dsp_ad = false;
  int merchant_ad_count = 0;  // 统计小店通 merchant 的广告数量

  auto ad_request_type =
      static_cast<int32_t>(ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request())));

  // 分钟级统计有广告请求
  if (ad_list_->Size() > 0 && !session_data_->IsTestRequest()) {
    ks::ad_server::FlowStatisticsMinute::GetInstance()->IncrMinuteStatistic(true);
  }
  ad_list_->GetAllWithFunc([&](kuaishou::ad::AdResult* ad_result, RankAdCommon* ad_common) {
    const auto& ad = *ad_result;
    if (ad.ad_source_type() == kuaishou::ad::ADX) {
    } else {
      has_dsp_ad = true;
      if (ad.ad_deliver_info().ad_base_info().campaign_type() ==
          kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
        merchant_ad_count++;
        session_data_->dot_perf->Count(1, "ad_front_merchant_ad_count");
      }
      if (ad.ad_deliver_info().ad_base_info().real_time_type() == kuaishou::ad::AdEnum::RTA) {
        falcon::Inc("ad_server.rta_ad_count", 1);
        ks::ad_base::AdPerf::CountLogStash(
            1, "ad.ad_server", "rta_ad_count",
            kuaishou::ad::RtaSourceType_Name(ad.ad_deliver_info().ad_base_info().rta_source_type()));
      }
      if (ad.ad_deliver_info().ad_base_info().campaign_type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN) {
        session_data_->dot_perf->Count(
            1, "front_server.post_proc.dpa_ad_count",
            kuaishou::ad::DpaSourceType_Name(ad.ad_deliver_info().ad_base_info().dpa_source_type()),
            kuaishou::ad::AdActionType_Name(ad.ad_deliver_info().ad_base_info().ocpc_action_type()),
            std::to_string(ad.ad_deliver_info().ad_base_info().campaign_sub_type()));
      }
      if (ad.ad_deliver_info().price() <= 0) {
        session_data_->dot_perf->Count(1, "front_server.post_proc.price_zero");
      }
    }
    // 统计出广告位置
    session_data_->dot_perf->Count(1, "ad_pos_info_count", absl::StrCat(ad.ad_deliver_info().pos()),
                                   kuaishou::ad::AdSourceType_Name(ad.ad_source_type()),
                                   kuaishou::ad::AdEnum::RefreshDirection_Name(
                                       ks::ad_base::GetRefreshDirection(*(session_data_->get_ad_request()))));

    // 打印一下软硬广广告队列
    session_data_->dot_perf->Count(1, "ad_queue_type_num", "dsp",
                                   absl::StrCat(ad_result->ad_deliver_info().ad_queue_type()));
    session_data_->dot_perf->Interval(ad_common->ad_price.price, "replay", "dsp",
                                      absl::StrCat(ad_result->ad_deliver_info().ad_queue_type()));
    if (ad.ad_deliver_info().ad_base_info().display_info().empty()) {
      session_data_->dot_perf->Count(
          1, "response_display_info_empty", "dsp",
          kuaishou::ad::AdSourceType_Name(ad.ad_deliver_info().ad_base_info().ad_source_type()));
      TLOG_EVERY_N(INFO, FrontKconfUtil::logDisplayInfoNullFrequency())
          << "display info null, creative id:" << ad.ad_deliver_info().ad_base_info().creative_id()
          << ".  display info:" << ad.ad_deliver_info().ad_base_info().display_info();
    }
  });

  falcon::Inc("ad_server.merchant_ad_count", merchant_ad_count);
  int adx_result_count = 0;
  int auto_target_num = 0;
  auto account_record_list = engine_base::AdKconfUtil::accountLevelRecordList();

  ad_list_->GetAllWithFunc([&](kuaishou::ad::AdResult* ad_result, RankAdCommon* ad_common) {
    const auto& ad = *ad_result;
    const auto& ad_base_info = ad.ad_deliver_info().ad_base_info();
    const uint64 price = ad.ad_deliver_info().price();
    const auto charge_action_type = ad_base_info.charge_action_type();
    bool is_ali_outer_ad =
        (ad_base_info.campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE);
    falcon::Stat("ad_server.ad_price", price);
    falcon::Stat(absl::Substitute("ad_server.ad_price_$0", charge_action_type).c_str(), price);
    if (price > 10000 && charge_action_type == kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION) {
      // 点击计费 price > 10 元认为是异常
      falcon::Inc("ad_server.price_abnormal_count", 1);
      falcon::Stat("ad_server.price_abnormal", price);
      const auto& ad_base_info = ad.ad_deliver_info().ad_base_info();
    }
    falcon::Inc(absl::Substitute("ad_server.total_price_ad_flow_type_$0", ad_request_type).c_str(), price);
    falcon::Inc(absl::Substitute("ad_server.total_predict_charge_ad_flow_type_$0", ad_request_type).c_str(),
                ad.ad_deliver_info().predict_charge());
    falcon::Inc(absl::Substitute("ad_server.total_price_charge_action_type_$0", charge_action_type).c_str(),
                price);
    falcon::Inc(
        absl::Substitute("ad_server.total_predict_charge_charge_action_type_$0", charge_action_type).c_str(),
        ad.ad_deliver_info().predict_charge());
    // 记录粉条硬广数量
    if (ad_base_info.account_type() ==
            kuaishou::ad::AdEnum::AdDspAccountType::AdEnum_AdDspAccountType_ACCOUNT_FANSTOP_TEMU ||
        ad_base_info.account_type() ==
            kuaishou::ad::AdEnum::AdDspAccountType::AdEnum_AdDspAccountType_ACCOUNT_FANSTOP) {
      TLOG_EVERY_N(INFO, 1000) << "return fanstop ad, account id:" << ad_base_info.account_id();
      falcon::Inc("ad_server.fanstop_ad_count", 1);
      session_data_->dot_perf->Count(1, "ad_server_fans_ad_count");
    }

    // 记录给高粉用户(网红)出的快享推广广告 account_id
    if (session_data_->get_ad_request()->front_internal_data().is_star_user()) {
      TLOG_EVERY_N(INFO, 100) << " star_request_ad_res, result_size: " << ad_list_->Size()
                              << " ad_account_id: " << ad_base_info.account_id();
      ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_server", "ad_account_of_item_for_star",
                                         absl::StrCat(ad_base_info.account_id()));
    }
    if (is_ali_outer_ad) {
      session_data_->dot_perf->Count(1, "ad_server_ali_outer_ad_count");
    }
    if (ad.ad_source_type() == kuaishou::ad::ADX) {
      session_data_->dot_perf->Count(1, "ad_server.post_proc.adx_ad_count",
                                     kuaishou::ad::AdEnum_BidType_Name(ad_base_info.bid_type()),
                                     kuaishou::ad::AdxSourceType_Name(ad_base_info.adx_source_type()));
      adx_result_count++;
    }
    auto subpage_id = ad.ad_deliver_info().universe_ad_deliver_info().sub_page_id();
    if (ad.ad_deliver_info().online_join_params_transparent().smart_matching_pred() > 0) {
      auto_target_num++;
    }
    ks::ad_base::AdPerf::CountLogStash(
        1, "ad.ad_server", "post_proc_subpage_id",
        absl::StrCat(ad.ad_deliver_info().universe_ad_deliver_info().sub_page_id()));
    auto deliver_info = ad.ad_deliver_info();
    if (session_data_->get_llsid() % 100 < 20) {
      session_data_->dot_perf->Interval(deliver_info.bonus_cpm(), "ad.ad_rank.benefit.bonus_cpm.final_front",
                                        absl::StrCat(deliver_info.bonus_cpm_tag()));
    }
  });

  // 广告数统计
  falcon::Inc("ad_server.ad_count", ad_list_->Size());
  ks::ad_base::AdPerf::CountLogStash(ad_list_->Size(), "ad.ad_server", "ad_count");
  if (adx_result_count > 0) {
    session_data_->dot_perf->Interval(adx_result_count, "ad_server.post_proc.adx_ad_num");
  }
  if (auto_target_num > 0) {
    session_data_->dot_perf->Interval(auto_target_num, "ad_front.delivery_auto_target_num");
  }
  // 记录给高粉用户(网红)出的快享推广广告
  if (session_data_->get_ad_request()->front_internal_data().is_star_user()) {
    falcon::Inc("ad_server.ad_of_item_for_star", ad_list_->Size());
  }
  if (dsp_ad_request_->product() == KUAISHOU_ANTMAN) {
    falcon::Inc("ad_server.antman.ad_count");
    session_data_->dot_perf->Count(1, "ad_server_antman_ad_count");
    if (session_data_->get_is_unlogin_user()) {
      session_data_->dot_perf->Count(1, "ad_server_unlogin_antman_ad_count");
    }
  }

  // 将用户在场景上的请求频次写入 redis
  WriteUserSceneRequestFreqToRedis();
}

void PostProc::WriteUserSceneRequestFreqToRedis() {
  if (!SPDM_enable_universe_user_scene_freq_v3(session_data_->get_spdm_ctx())) {
    return;
  }

  auto* redis_client = ks::ad_base::KconfRedis::Instance().GetAdRedisPipelineClient(
      "adUniversePvFreq", engine_base::DependDataLevel::WEAK_DEPEND);
  if (redis_client) {
    uint64_t user_id = session_data_->mutable_ad_request()->mutable_ad_user_info()->id();
    int64_t ad_style = session_data_->get_ud_ad_style();
    absl::Time now = absl::Now();
    std::string format = "%Y-%m-%d";
    std::string formatted_date = absl::FormatTime(format, now, absl::LocalTimeZone());
    std::string redis_key;
    if (SPDM_enable_user_req_freq_app_id(session_data_->get_spdm_ctx())) {
      const std::string& app_id = session_data_->get_pos_manager().GetRequestAppId();
      redis_key = absl::StrCat(user_id, "_", ad_style, "_", app_id, "_", formatted_date);
    } else {
      redis_key = absl::StrCat(user_id, "_", ad_style, "_", formatted_date);
    }
    std::string value = absl::StrCat(session_data_->get_ad_request()->ad_user_info().request_freq());
    redis_client->SetEx(redis_key, value, 24 * 60 * 60);
    session_data_->dot_perf->Count(1, "write_user_scene_request_freq_to_redis");
    LOG_EVERY_N(INFO, 10000) << " write_user_scene_request_freq_to_redis redis_key: " << redis_key
                             << " value: " << value;
  }
}

void PostProc::UpdateUniverseDeliveredQponRedis() {
  session_data_->dot_perf->Count(1, "universe_delivered_qpon_redis_admit",
                                 std::to_string(session_data_->get_ud_universe_delivered_qpon_redis_admit()));
  if (!SPDM_enable_universe_delivered_qpon_info(session_data_->get_spdm_ctx())) {
    return;
  }
  uint64_t user_id = session_data_->get_ad_request()->ad_user_info().id();
  int32_t day = session_data_->get_bd().day;

  auto* redis_client = ks::ad_base::KconfRedis::Instance().GetAdRedisPipelineClient(
      *FrontKconfUtil::universeDeliveredQponRedis(), engine_base::DependDataLevel::WEAK_DEPEND);
  if (redis_client) {
    bool need_update_delivered_qpon_info = false;
    bool need_incr_delivery_cnt = false;

    kuaishou::ad::UniverseDeliveredQponInfo delivered_qpon_info;
    if (day == session_data_->get_ud_universe_delivered_qpon_info_day()) {
      // 处理下请求和返回跨天的 corner case
      delivered_qpon_info.CopyFrom(session_data_->get_ad_request()->universe_delivered_qpon_info());
    }
    // 解析已有的 author_id 和 item_id 集合，讨论是否需要写 redis
    absl::flat_hash_set<int64_t> existed_autor_ids;
    absl::flat_hash_set<int64_t> existed_item_ids;
    for (const auto& info : delivered_qpon_info.items()) {
      if (info.author_id() > 0) {
        existed_autor_ids.insert(info.author_id());
      }
      if (info.item_id() > 0) {
        existed_item_ids.insert(info.item_id());
      }
    }

    ad_list_->GetAllWithFunc([&](kuaishou::ad::AdResult* ad_result, RankAdCommon* ad_common) {
      const auto& ad = *ad_result;
      const auto& ad_base_info = ad.ad_deliver_info().ad_base_info();
      const auto& online_join_params_pb = ad.ad_deliver_info().online_join_params_pb();
      const auto& ad_rank_trans_info = online_join_params_pb.ad_rank_trans_info();
      const auto& qpon_info = ad_rank_trans_info.qpon_info();
      if (session_data_->get_is_closure_flow() &&
          session_data_->is_closure_ad(ad_base_info.campaign_type(), ad_base_info.ocpc_action_type())) {
        return;
      }

      if (online_join_params_pb.coupon_template_id() > 0 && qpon_info.has_qpon()) {
        // 有 qcpx 券，则需要增加频控计数，讨论是否需要写一致性 redis
        need_incr_delivery_cnt = true;
        int64_t author_id = 0;
        int64_t item_id = 0;
        auto campaign_type = ad_base_info.campaign_type();
        if (campaign_type == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
          author_id = ad_base_info.author_id();
        } else if (campaign_type == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
          if (!absl::SimpleAtoi(ad_base_info.merchant_product_id(), &item_id)) {
            item_id = 0;
          }
        }
        if (session_data_->get_ud_get_universe_delivered_qpon_info_success() &&
            ((author_id > 0 && !existed_autor_ids.contains(author_id)) ||
             (item_id > 0 && !existed_item_ids.contains(item_id)))) {
          need_update_delivered_qpon_info = true;
          auto* new_info = delivered_qpon_info.add_items();
          new_info->set_author_id(author_id);
          new_info->set_item_id(item_id);
          new_info->set_universe_inner_qcpx_cause(ad_rank_trans_info.universe_inner_qcpx_cause());
          new_info->set_coupon_type(online_join_params_pb.coupon_type());
          new_info->set_coupon_template_id(online_join_params_pb.coupon_template_id());
          new_info->set_coupon_amount(online_join_params_pb.discount_amount());
          new_info->set_threshold(online_join_params_pb.coupon_threshold());
          new_info->set_reduce_amount(online_join_params_pb.reduce_amount());
          new_info->set_capped_amount(online_join_params_pb.capped_amount());
          new_info->set_bid_qpon_ratio(qpon_info.bid_qpon_ratio());
          new_info->set_uplift_cvr_ratio(qpon_info.uplift_cvr_ratio());
          new_info->set_uplift_ctr_ratio(qpon_info.uplift_ctr_ratio());
        }
        // 如果 reponse 里多个广告对应同一个 item_id/author_id，这里取靠前的
        if (author_id > 0) {
          existed_autor_ids.insert(author_id);
        }
        if (item_id > 0) {
          existed_item_ids.insert(item_id);
        }
      }
    });
    if (need_update_delivered_qpon_info) {
      std::string redis_key = absl::StrCat("udqpon_", day, "_", user_id);
      std::string value = delivered_qpon_info.SerializeAsString();
      int32_t ttl_hour = 24 - session_data_->get_bd().hour;
      redis_client->SetEx(redis_key, value, ttl_hour * 60 * 60);
      session_data_->dot_perf->Count(1, "write_qpon_info_to_redis");
    }
    if (need_incr_delivery_cnt) {
      std::string redis_key = absl::StrCat("udqponfreq_", day, "_", user_id);
      int32_t ttl_hour = 24 - session_data_->get_bd().hour;
      redis_client->IncrByEx(redis_key, 1, ttl_hour * 60 * 60);
      session_data_->dot_perf->Count(1, "write_qpon_freq_to_redis");
    }
  }
}

void PostProc::Clear() {
  session_data_ = nullptr;
  dsp_ad_request_ = nullptr;
  dsp_ad_response_ = nullptr;
  ad_list_ = nullptr;
  is_candidate = true;
  disable_dsp_no_mix_ack = false;
  disable_innerloop_no_mix_ack = false;
  selected_rank_infos_.clear();
  selected_rank_infos_inner_loop_.clear();
  serialized_reco_user_info.clear();
}

void PostProc::AckForNewCreativeResponseKafka() {
  if (session_data_->IsTestRequest()) {
    return;
  }

  int64 ack_ts = base::GetTimestamp();
  ContextData* response_data = context_->GetMutableContextData<ContextData>();
  if (!dsp_ad_response_->has_new_creative_used_item()) {
    return;
  }
  auto& new_creative_used_item = dsp_ad_response_->new_creative_used_item();
  if (new_creative_used_item.item_id_size() <= 1) {
    falcon::Inc("ad_server.ack_new_creative_used_item_kafka_empty", 1);
    return;
  }
  const std::string key_prefix = "new_creative_detail";
  std::string topic_name = "ad_server_new_creative_response";
  auto rt = WriteDataAckToKafka(new_creative_used_item, key_prefix, absl::StrCat(session_data_->get_llsid()),
                                topic_name);
  if (rt < 0) {
    falcon::Inc("ad_server.ack_new_creative_used_item_kafka_fail", 1);
    TLOG(WARNING) << "Act new_creative_used_item kafka failed.";
  }
}

void PostProc::DetailCPMAckForPredictServer() {
  if (!dsp_ad_response_->has_detail_used_item()) {
    return;
  }
  bool is_fill_detail_used_item = false;
  // for context & userinfo feature : Quoality Model
  const auto& detail_used_item = session_data_->get_ad_response()->detail_used_item();
  session_data_->mutable_detail_used_item()->CopyFrom(detail_used_item);
  session_data_->mutable_detail_used_item()->mutable_context()->CopyFrom(
      session_data_->get_predict_request().context());
  // [xiaowentao] 把价值分模型预估的 fill_rate 和 predict_cpm 传到训练样本流
  auto *fill_rate_attr = session_data_->mutable_detail_used_item()->mutable_context()->add_info_common_attr();
  fill_rate_attr->set_name_value(ContextInfoCommonAttr::UNIVERSE_QUALITY_PRED_FILL_RATE);
  fill_rate_attr->set_type(CommonTypeEnum::FLOAT_ATTR);
  fill_rate_attr->set_float_value(session_data_->get_universe_quality_data_fill_rate());
  auto *pred_cpm = session_data_->mutable_detail_used_item()->mutable_context()->add_info_common_attr();
  pred_cpm->set_name_value(ContextInfoCommonAttr::UNIVERSE_QUALITY_PRED_CPM);
  pred_cpm->set_type(CommonTypeEnum::FLOAT_ATTR);
  pred_cpm->set_float_value(session_data_->get_universe_quality_data_predict_cpm());
  if (session_data_->get_enable_detail_cpm_kafka()
      || session_data_->get_enable_win_rate_kafka()) {
    auto *int_common_attr =
        session_data_->mutable_detail_used_item()->mutable_context()->add_info_common_attr();
    int_common_attr->set_name_value(kuaishou::ad::ContextInfoCommonAttr::CANDIDATE_QUEUE_SIZE);
    int_common_attr->set_type(kuaishou::ad::CommonTypeEnum::INT_ATTR);
    int64_t rank_size = session_data_->get_ud_universe_elastic_info().max_prerank_num;
    session_data_->dot_perf->Count(1, "prerank_size", absl::StrCat("rank_size_", rank_size));
    int_common_attr->set_int_value(rank_size);
    auto is_uplift_sample = !session_data_->get_enable_use_full_computing_power() &&
      session_data_->get_enable_detail_cpm_kafka();
    if (is_uplift_sample) {
      // [xiaowentao] 将粗排和精排队列 Quota 大小传入联盟价值分 uplift 模型样本流用于训练
      auto *recall_size_attr =
          session_data_->mutable_detail_used_item()->mutable_context()->add_info_common_attr();
      recall_size_attr->set_name_value(kuaishou::ad::ContextInfoCommonAttr::UNI_QUALITY_RECALL_SIZE);
      recall_size_attr->set_type(kuaishou::ad::CommonTypeEnum::INT_ATTR);
      int64_t recall_size = session_data_->get_ud_raw_max_retrieval_creatives();
      session_data_->dot_perf->Count(1, "raw_recall_size", absl::StrCat("recall_size_", recall_size));
      recall_size_attr->set_int_value(recall_size);

      auto *attr = session_data_->mutable_detail_used_item()->mutable_context()->add_info_common_attr();
      attr->set_name_value(kuaishou::ad::ContextInfoCommonAttr::UNI_QUALITY_RANK_SIZE);
      attr->set_type(kuaishou::ad::CommonTypeEnum::INT_ATTR);
      int64_t raw_rank_size = session_data_->get_ud_raw_max_prerank_num();
      session_data_->dot_perf->Count(1, "raw_rank_size", absl::StrCat("raw_rank_size_", raw_rank_size));
      attr->set_int_value(raw_rank_size);
    }
    double fill_rate = session_data_->get_universe_quality_data_fill_rate();
    // 筛选出采样并且经过模型预估的 pv
    if (fill_rate > 1e-10) {
      auto *pv_tag_attr =
        session_data_->mutable_detail_used_item()->mutable_context()->add_info_common_attr();
      pv_tag_attr->set_name_value(kuaishou::ad::ContextInfoCommonAttr::UNIVERSE_PV_SAMPLE_TAG);
      pv_tag_attr->set_type(kuaishou::ad::CommonTypeEnum::INT_ATTR);
      if (session_data_->get_enable_use_full_computing_power()) {
        session_data_->dot_perf->Interval(1, "hit_enableUseNewValueScoreSimple");
        pv_tag_attr->set_int_value(2);
        if (FrontKconfUtil::predictCpmSampleOptControl()) {
          if (session_data_->get_is_model_predict_failure()) {
            pv_tag_attr->set_int_value(3);
          }
        }
        // [xiaowentao] 价值分样本流，超时样本标签为 4
        auto timeout =
            std::min(session_data_->UniverseRequest().tmax(), session_data_->UniverseRequest().timeout());
        double process_time = 1.0 * (base::GetTimestamp() - session_data_->get_start_ts()) / 1000.0;
        if (timeout > 0 && process_time >= timeout) {
          pv_tag_attr->set_int_value(4);
        }
        if (session_data_->get_enable_detail_cpm_timeout_kafka()) {
          pv_tag_attr->set_int_value(5);
        }
        session_data_->dot_perf->Count(1, "front_cpm_timeout_tag4",
          timeout > 0 && process_time >= timeout ? "1" : "0");
      } else {
        pv_tag_attr->set_int_value(1);
        if (session_data_->get_enable_detail_cpm_uplift_kafka()) {
          pv_tag_attr->set_int_value(6);
        }
      }
      // [xiaowentao] 竞胜率模型的起点是下发，所以要求样本是有效下发样本
      if (session_data_->get_enable_win_rate_kafka()
          && session_data_->get_ad_list().Size() > 0) {
        pv_tag_attr->set_int_value(7);
        session_data_->dot_perf->Count(1, "front_win_rate_kafka_sample");
      }
    }
    int32_t cache_type = session_data_->get_is_universe_cached_res() ? 4 : 2;
    auto *cpm_tag_attr = session_data_->mutable_detail_used_item()->mutable_context()->add_info_common_attr();
    cpm_tag_attr->set_name_value(kuaishou::ad::ContextInfoCommonAttr::FRONT_SERVER_FILTERED_TYPE);
    cpm_tag_attr->set_type(kuaishou::ad::CommonTypeEnum::INT_ATTR);
    cpm_tag_attr->set_int_value(cache_type);
    session_data_->dot_perf->Count(1, "front_pv_detail_used_item");
  }
  if (session_data_->get_ad_response()->has_detail_used_item() && detail_used_item.item_id_size() == 0) {
    is_fill_detail_used_item = true;
  }

  session_data_->mutable_ad_list()->GetAllWithFunc(
      [this, &is_fill_detail_used_item](kuaishou::ad::AdResult* ad_result, RankAdCommon* ad_common) {
        if ((session_data_->get_enable_detail_cpm_kafka() || session_data_->get_enable_win_rate_kafka())
            && is_fill_detail_used_item) {
          auto* ad_rank_info = session_data_->mutable_detail_used_item()->add_ad_rank_infos();
          auto creative_id = ad_result->ad_deliver_info().ad_base_info().creative_id();
          ad_rank_info->set_item_id(creative_id);
          ad_rank_info->set_cpm(ad_result->ad_deliver_info().price() * 1000);
          session_data_->mutable_detail_used_item()->add_item_id(creative_id);
          auto *used_item_context = session_data_->mutable_detail_used_item()->mutable_context();
          if (0 == used_item_context->raw_cpm()) {
            if (session_data_->get_enable_win_rate_kafka()) {
              // 竞胜率样本流使用分成后出价
              int64_t rtb_ecpm = ad_result->ad_deliver_info().ad_base_info().rtb_ecpm();
              double media_share_ratio =
                session_data_->get_ad_request()->universe_ad_request_info().share_ratio() * 1.0 / 10000;
              if (!ad_result->ad_deliver_info().ad_base_info().universe_dynamic_share_rewrite_share_ratio()) {
                rtb_ecpm = static_cast<int64_t>(rtb_ecpm * media_share_ratio);
              }
              used_item_context->set_raw_cpm(rtb_ecpm);
            } else {
              // 价值分样本流使用精排 cpm
              used_item_context->set_raw_cpm(ad_result->ad_deliver_info().cpm());
            }
          }
        }
      });
  int rt;
  rt = WriteDetailCpmAckToKafka(session_data_->get_detail_used_item());
  if (rt != 0) {
    falcon::Inc("ad_server.detail.ack_kafka_fail", 1);
    TLOG_EVERY_N(INFO, 10000) << "DETAIL cpm Act kafka failed.";
  }
}

int PostProc::WriteDetailCpmAckToKafka(const kuaishou::ad::algorithm::UsedItem& detail_used_item) {
  // kafka 降级开关
  auto ratio = ks::ad_base::AdRandom::GetInt(0, 100);
  if (ratio > FrontKconfUtil::universeCpmAckRatio()) {
    falcon::Inc("ad_server.universe.cpm_ack_degrade", 1);
    return 0;
  }

  static std::shared_ptr<::ks::infra::kfk::Producer> producer = nullptr;
  std::string cpm_ack_kafka_config = "universe_cpm_ack_kafka_config";
  static bool is_yz_deploy = ks::infra::KEnv::GetKWSInfo()->GetAZ() == "YZ";
  auto switch_ratio = ks::ad_base::AdRandom::GetInt(0, 99);
  if (switch_ratio < FrontKconfUtil::ackKafkaYzSwitchRatio() && is_yz_deploy) {
    cpm_ack_kafka_config = "universe_cpm_ack_kafka_config_yz";
  }
  auto* config = DynamicJsonConfig::GetConfig()->Get(cpm_ack_kafka_config);
  if (config == nullptr) {
    TLOG_EVERY_N(WARNING, 1000) << "detail_ack_kafka_config is nullptr";
    return -1;
  }
  std::string logical_topic_id = config->GetString("kafka_topic", "");
  // Kafka producer.
  if (producer == nullptr) {
    producer = ks::infra::kfk::DynamicKafkaClient::GetProducerByLogicalTopicId(logical_topic_id);
    if (producer == nullptr) {
      TLOG_EVERY_N(ERROR, 1000) << "detail_Encounter nullptr when try to get kafka producer, topic id: "
                                << logical_topic_id;
      return -2;
    }
  }

  std::string key_prefix = config->GetString("detail_key_prefix", "");
  if (key_prefix.empty()) {
    TLOG_EVERY_N(WARNING, 1000) << "detail_key_prefix not set";
    return -3;
  }
  std::string llsid_str = absl::StrCat(session_data_->get_llsid());
  std::string key = key_prefix + "_" + llsid_str;
  std::string value;
  if (!detail_used_item.SerializeToString(&value)) {
    TLOG_EVERY_N(WARNING, 1000) << "value SerializeToString fail";
    return -4;
  }
  falcon::Stat("ad_server.detail.ack_kafka_value_size", value.size());
  falcon::Inc("ad_server.detail.ack_set_kafka_num", 1);
  auto kafka_start_ts = base::GetTimestamp();
  static std::shared_ptr<ad_base::AdKafkaProducer> new_producer = nullptr;
  static std::once_flag flag;
  std::call_once(flag, [&logical_topic_id] {
    new_producer = std::make_shared<ad_base::AdKafkaProducer>();
    new_producer->InitWithTopic(logical_topic_id, engine_base::DependDataLevel::WEAK_DEPEND);
  });
  if (nullptr == new_producer) {
    LOG(ERROR) << "WriteDetailCpmAckToKafka new_producer is nullptr, topic: " << logical_topic_id;
    return -1;
  }
  auto err_code = new_producer->Produce(key, value);
  auto kafka_end_ts = base::GetTimestamp();
  falcon::Stat("ad_server.detail.ack_write_kafka_latency", (kafka_end_ts - kafka_start_ts) / 1000);
  if (err_code != ks::ad_base::AdKafkaStatus::SUCCESS) {
    TLOG_EVERY_N(WARNING, 1000) << "new Write kafka error. code:" << err_code;
    return -1;
  }
  if (FLAGS_enable_no_diff_check) {
    ks::engine_base::ReportInfoLogToRedis("ad_pv_cpm_ack_ps_universe", front_grpc_port, value);
  }

  TLOG_EVERY_N(INFO, 1000) << "detail_ack_cpm_ps set kafka success, key:" << key
                           << ", value size:" << value.size();
  return 0;
}

void PostProc::UnionLtrAckForPredictServer() {
  // 联盟且采样流量才下发
  if (!session_data_->get_enable_union_ltr_sample()) {
    return;
  }

  if (session_data_->get_enable_union_ltr_sample() &&
      (!session_data_->get_ad_response()->has_union_ltr_used_item() ||
        session_data_->get_ad_response()->union_ltr_used_item().ad_rank_infos_size() <= 0)) {
    return;
  }

  ks::ad_base::ad_ack_info_record::AdAckInfoRecord ack_recorder(
      dsp_ad_request_->ad_request_flow_type(), dsp_ad_request_->detail_request_flow_type(),
      session_data_->get_llsid(), session_data_->get_user_id(), 0, true);
  SetAckInfoForAdPackServiceUniverseLtr(&ack_recorder);
  SendDspAckDataFlowNewUniverseLtr();
}

int PostProc::WriteDataAckToKafka(const kuaishou::ad::algorithm::UsedItem& used_item,
                                  const std::string& key_prefix, const std::string& llsid_str,
                                  const std::string& logical_topic_id) {
  static std::shared_ptr<::ks::infra::kfk::Producer> producer = nullptr;
  // Kafka producer.
  if (producer == nullptr) {
    producer = ks::infra::kfk::DynamicKafkaClient::GetProducerByLogicalTopicId(logical_topic_id);
    if (producer == nullptr) {
      TLOG_EVERY_N(ERROR, 1000) << "Encounter nullptr when try to get kafka producer, topic id: "
                                << logical_topic_id;
      return -1;
    }
  }

  if (key_prefix.empty()) {
    TLOG_EVERY_N(WARNING, 1000) << "key_prefix not set";
    return -1;
  }
  std::string key = key_prefix + "_" + llsid_str;
  std::string value;
  falcon::Inc(absl::Substitute("ad_server.ack_kafka_$0", logical_topic_id).data());
  falcon::Stat(absl::Substitute("ad_server.ack_kafka_$0_item_size", logical_topic_id).data(),
               used_item.item_id_size());
  falcon::Stat(absl::Substitute("ad_server.ack_kafka_$0_rank_info_size", logical_topic_id).data(),
               used_item.ad_rank_infos_size());

  if (!used_item.SerializeToString(&value)) {
    TLOG_EVERY_N(WARNING, 1000) << "value SerializeToString fail";
    return -1;
  }

  falcon::Stat(absl::Substitute("ad_server.ack_kafka_$0_value_size", logical_topic_id).data(), value.size());

  auto kafka_start_ts = base::GetTimestamp();
  static std::shared_ptr<ad_base::AdKafkaProducer> new_producer = nullptr;
  static std::once_flag flag;
  std::call_once(flag, [&logical_topic_id] {
    new_producer = std::make_shared<ad_base::AdKafkaProducer>();
    new_producer->InitWithTopic(logical_topic_id, engine_base::DependDataLevel::WEAK_DEPEND);
  });
  if (nullptr == new_producer) {
    LOG(ERROR) << "WriteDataAckToKafka new_producer is nullptr, topic: " << logical_topic_id;
    return -1;
  }
  auto err_code = new_producer->Produce(key, value);
  auto kafka_end_ts = base::GetTimestamp();
  falcon::Stat(absl::Substitute("front_server.ack_kafka_$0_latency", logical_topic_id).data(),
               (kafka_end_ts - kafka_start_ts) / 1000);
  if (err_code != ks::ad_base::AdKafkaStatus::SUCCESS) {
    TLOG_EVERY_N(WARNING, 1000) << "new Write kafka error. code:" << err_code;
    return -1;
  }
  if (FLAGS_enable_no_diff_check) {
    ks::engine_base::ReportInfoLogToRedis(logical_topic_id, front_grpc_port, value);
  }

  TLOG_EVERY_N(INFO, 1000) << "ack set kafka success, key:" << key << ", value size:" << value.size();
  return 0;
}

void PostProc::SetAckInfoForAdPackServiceUniverse(
    ks::ad_base::ad_ack_info_record::AdAckInfoRecord* p_ack_recorder) {
  if (p_ack_recorder == nullptr)
    return;

  // 打包公共库发送 ack 需要的数据
  kuaishou::ad::AdAckInfo transparent_ack_info;
  std::vector<ks::ad_base::ad_ack_info_record::AckPackInfo> ack_pack_info_vec;

  SetTransparentAckInfo(&transparent_ack_info);
  SetAckPackInfoForAd(&ack_pack_info_vec);

  // 填充数据
  p_ack_recorder->BuildAckData(ack_pack_info_vec, transparent_ack_info, dsp_ad_request_->ad_user_info(),
                               false, true);
  selected_rank_infos_ = p_ack_recorder->GetSelectedRankInfos();
  session_data_->mutable_used_item()->set_user_id(session_data_->get_user_id());
  // 保存在 context_data，透传下去
  session_data_->mutable_used_item()->Swap(p_ack_recorder->GetUsedItem());
}

void PostProc::SetAckInfoForAdPackServiceUniverseLtr(
    ks::ad_base::ad_ack_info_record::AdAckInfoRecord* p_ack_recorder) {
  if (p_ack_recorder == nullptr) {
    return;
  }
  // 打包公共库发送 ack 需要的数据
  kuaishou::ad::AdAckInfo transparent_ack_info;
  std::vector<ks::ad_base::ad_ack_info_record::AckPackInfo> ack_pack_info_vec;
  SetTransparentAckInfo(&transparent_ack_info, true);
  SetAckPackInfoForAd(&ack_pack_info_vec, true);
  // 填充数据
  p_ack_recorder->BuildAckData(ack_pack_info_vec, transparent_ack_info, dsp_ad_request_->ad_user_info(),
                               false, true);
  selected_rank_infos_ = p_ack_recorder->GetSelectedRankInfos();
  session_data_->mutable_union_ltr_used_item()->set_user_id(session_data_->get_user_id());
  session_data_->mutable_union_ltr_used_item()->Swap(p_ack_recorder->GetUsedItem());
}

void PostProc::SetTransparentAckInfo(kuaishou::ad::AdAckInfo* p_ack_info, bool is_union_ltr_used_item) {
  if (p_ack_info == nullptr) {
    return;
  }
  if (is_union_ltr_used_item && dsp_ad_response_->has_union_ltr_used_item()) {
    if (dsp_ad_response_->union_ltr_used_item().ad_rank_infos_size() > 0) {
      p_ack_info->mutable_used_item()->CopyFrom(dsp_ad_response_->union_ltr_used_item());
    }
  } else if (dsp_ad_response_->has_used_item()) {
    p_ack_info->mutable_used_item()->CopyFrom(dsp_ad_response_->used_item());
  }
  if (dsp_ad_response_->has_new_creative_used_item()) {
    p_ack_info->mutable_new_creative_used_item()->CopyFrom(dsp_ad_response_->new_creative_used_item());
  }
  if (dsp_ad_response_->has_detail_used_item()) {
    p_ack_info->mutable_detail_used_item()->CopyFrom(dsp_ad_response_->detail_used_item());
  }
}

void PostProc::SetAckPackInfoForAd(
    std::vector<ks::ad_base::ad_ack_info_record::AckPackInfo>* p_ack_pack_info_vec, bool is_universe_ltr) {
  if (p_ack_pack_info_vec == nullptr) {
    return;
  }
  p_ack_pack_info_vec->reserve(ad_list_->Size());
  for (RankAdCommon* ad : session_data_->mutable_ad_list()->Ads()) {
    auto* ad_result = ad->GetResult();
    if (!ad_result) {
      continue;
    }
    AppendOnePackInfo(*ad_result, p_ack_pack_info_vec);
    if (is_universe_ltr) {
      break;
    }
  }
}

void PostProc::AppendOnePackInfo(
    const AdResult& result_item,
    std::vector<ks::ad_base::ad_ack_info_record::AckPackInfo>* p_ack_pack_info_vec) {
  p_ack_pack_info_vec->emplace_back();

  ks::ad_base::ad_ack_info_record::AckPackInfo& ack_pack_info = p_ack_pack_info_vec->back();
  ack_pack_info.ad_result.CopyFrom(result_item);
  ks::ad_base::ad_ack_info_record::CreativeInfo& creative_info = ack_pack_info.creative_info;
  ks::ad_base::ad_ack_info_record::RankPassThroughInfo& rank_pass_through =
      ack_pack_info.rank_pass_through_info;

  int64_t creative_id = result_item.ad_deliver_info().ad_base_info().creative_id();
  // 构造 creative info
  const auto& style_info = session_data_->get_style_info_resp()->style_info();
  auto iter = style_info.find(creative_id);
  if (iter != style_info.end()) {
    creative_info.ad_pos_type = iter->second.material().pos_type();
    creative_info.create_source_type = iter->second.creative().create_source_type();
    const auto& stick_titles = iter->second.creative().parse_field().stick_titles();
    auto title_iter = stick_titles.begin();
    for (; title_iter != stick_titles.end(); ++title_iter) {
      creative_info.stick_titles[title_iter->first] = title_iter->second;
    }
    const auto& sticker_styles = iter->second.creative().parse_field().sticker_styles();
    const auto *style_iter = sticker_styles.begin();
    creative_info.sticker_styles.reserve(sticker_styles.size());
    for (; style_iter != sticker_styles.end(); ++style_iter) {
      creative_info.sticker_styles.emplace_back(*style_iter);
    }
  }
  // 构造 rank pass through
  const auto* rank_result = GetAdRankResult(session_data_, result_item);
  if (rank_result) {
    rank_pass_through.rank_result.CopyFrom(*rank_result);
  }

  const auto* rank_info = GetAdRankInfo(session_data_, result_item);
  if (rank_info) {
    rank_pass_through.session_rank_info.CopyFrom(*rank_info);
  }
}

void PostProc::SetTransparentDataForAdPackService() {
  // req_pack_info 填充 透传数据给下游使用

  // 先填充透传给 ad_pack 的 ab 实验参数
  SetTransparentAbtestParamsForAdPackService();
  auto* athena_explore_resp = session_data_->mutable_front_server_response()->mutable_explore_response();
  // 先将透传的数据写到 context_data 下面，后面需要的时候再写到 resp 或 req 中
  auto* mutable_pack_info = session_data_->mutable_trans_pack_info();

  // 透传 front_request_type
  mutable_pack_info->set_front_request_type(
      static_cast<int>(session_data_->get_front_server_request()->type()));
  // 透传 trace log 采样标识至 adpack
  mutable_pack_info->set_trace_log_sampling_flag(session_data_->get_ad_request()->trace_log_sampling_flag());
  mutable_pack_info->set_trace_log_sampling_flag_v2_table(
      session_data_->get_ad_request()->trace_log_sampling_flag_v2_table());

  // 1. 添充需要回传的 redis 数据
  session_data_->GetLastPvAdInfo((*session_data_->get_ad_request()), false,
                                 mutable_pack_info->mutable_ad_user_last_pv_info());
  mutable_pack_info->mutable_fans_top_refresh_info()->Swap(
      session_data_->mutable_ad_request()->mutable_fans_top_refresh_info());
  mutable_pack_info->set_last_filter_pv_number(0);
  // 2. 添加参数，用于 mix_rank 判断是否调用混排
  athena_explore_resp->mutable_mix_req_info()->set_req_ad_pack(false);
  // 3. 添加参数, 用户 mix_rank 计算广告位间隔
  auto* ad_pos_history_info = athena_explore_resp->mutable_mix_req_info()->mutable_ad_pos_history_info();
  ad_pos_history_info->set_last_explore_pv_timestamp(
      session_data_->get_ad_request()->front_internal_data().last_explore_pv_timestamp());
  ad_pos_history_info->set_last_explore_pv_page_size(
      session_data_->get_ad_request()->front_internal_data().last_explore_pv_page_size());
  ad_pos_history_info->set_last_explore_pv_last_ad_pos(
      session_data_->get_ad_request()->front_internal_data().last_explore_pv_last_ad_pos());
  ad_pos_history_info->set_first_screen_ad_shw_timestamp(
      session_data_->get_ad_request()->front_internal_data().first_screen_ad_shw_timestamp());
  athena_explore_resp->mutable_mix_req_info()->set_page_size(session_data_->get_ad_request()->page_size());
  athena_explore_resp->mutable_mix_req_info()->set_refresh_direction(
      ks::ad_base::GetRefreshDirection(*(session_data_->get_ad_request())));
  athena_explore_resp->mutable_mix_req_info()->set_page_id(session_data_->get_page_id());
  athena_explore_resp->mutable_mix_req_info()->set_sub_page_id(session_data_->get_sub_page_id());
  // 4. 将 fake_id 传给混排
  if (session_data_->get_ad_request()->ad_user_info().is_unlogin_user()) {
    athena_explore_resp->mutable_mix_req_info()->set_fake_user_id(
        session_data_->get_ad_request()->ad_user_info().id());
  }
  // 5. 将 trace log 采样标识传给混排
  athena_explore_resp->mutable_mix_req_info()->set_trace_log_sampling_flag(
      session_data_->get_ad_request()->trace_log_sampling_flag());

  // 6. 填充 ack 数据
  auto* mutable_ad_ack_info = mutable_pack_info->mutable_ad_ack_info();

  // 将 front 返回的广告的 item 数据透传到 adpack
  int fanstop_returned_ack_num = 0;
  int native_returned_ack_num = 0;
  for (const auto& selected_rank_info : selected_rank_infos_) {
    if (selected_rank_info.source_type == kuaishou::ad::FANS_TOP_V2) {
      ++fanstop_returned_ack_num;
    } else if (selected_rank_info.promotion_type == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
      ++native_returned_ack_num;
    }

    auto* item_meta = mutable_ad_ack_info->add_front_returned_ack_data();
    item_meta->set_item_id(selected_rank_info.rank_info.item_id());
    item_meta->mutable_rank_info()->CopyFrom(selected_rank_info.rank_info);
  }
  for (const auto& selected_rank_info : selected_rank_infos_inner_loop_) {
    auto* inner_loop_candidate_item = mutable_ad_ack_info->mutable_inner_loop_candidate_item();
    auto* positive_sample = inner_loop_candidate_item->add_positive_samples();
    positive_sample->set_item_id(selected_rank_info.rank_info.item_id());
    positive_sample->mutable_rank_info()->CopyFrom(selected_rank_info.rank_info);
  }
  // 7. 填充创意过滤染色数据
  if (session_data_->get_ad_request()->debug_param().creative_id_size() > 0) {
    mutable_pack_info->set_debug_color_creative_id(
        session_data_->get_ad_request()->debug_param().creative_id(0));
  }
  if (session_data_->get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    // 预览模式下, 该字段设置为 true, reco rpc 不请求 mix-rank
    mutable_pack_info->set_is_skip_mix_rank(true);
    mutable_pack_info->set_ac_fetcher_type(static_cast<int32>(session_data_->get_ac_fetcher_type()));
    TLOG(INFO) << "[preview ad]: user_id: " << session_data_->get_user_id() << ", is_skip_mix_rank: true";
  }
  mutable_pack_info->set_search_has_mingtou_ad(false);
  mutable_pack_info->mutable_abtest_mapping_id()->CopyFrom(session_data_->get_abtest_mapping_id());
  session_data_->dot_perf->Count(fanstop_returned_ack_num, "fanstop_used_item_positive_size", "mix");
  session_data_->dot_perf->Count(native_returned_ack_num, "native_used_item_positive_size", "mix");

  // 8. 填充其他数据
  auto* mutable_other_params = mutable_pack_info->mutable_other_params();
  mutable_other_params->set_rtb_coff(session_data_->get_universe_rtb_coff());
  mutable_other_params->set_is_universe_rtb_flow(session_data_->get_is_universe_rtb_flow());

  LOG_EVERY_N(INFO, 1000000)
      << "ad_pos_history_info,last_explore_pv_timestamp = "
      << ad_pos_history_info->last_explore_pv_timestamp()
      << " last_explore_pv_page_size=" << ad_pos_history_info->last_explore_pv_page_size()
      << " last_explore_pv_last_ad_pos=" << ad_pos_history_info->last_explore_pv_last_ad_pos()
      << " first_screen_ad_shw_timestamp=" << ad_pos_history_info->first_screen_ad_shw_timestamp()
      << " page_size= " << athena_explore_resp->mutable_mix_req_info()->page_size()
      << " refresh_direction= " << athena_explore_resp->mutable_mix_req_info()->refresh_direction()
      << " requesttype= " << ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request()));
  // AdMixedInfo 是广告级的数据，放在了 data_postproc 里面
  // server_show 和 trace_always_log 分别在各自的发送逻辑里填充到了透传数据中
  LOG_IF_EVERY_N(INFO, FrontKconfUtil::enableTestLog(), 1000)
      << "ad_pack_test: llsid: " << session_data_->get_llsid()
      << ", ad_ack_info: " << mutable_ad_ack_info->ShortDebugString()
      << ", last_pv_info: " << mutable_pack_info->ad_user_last_pv_info().ShortDebugString();
}

void PostProc::SetTransparentAbtestParamsForAdPackService() {
  const auto& spdm_ctx = session_data_->get_spdm_ctx();
  ::google::protobuf::Map<::std::string, ::google::protobuf::int64>* mutable_abtest_params_int = nullptr;
  ::google::protobuf::Map<::std::string, ::std::string>* mutable_abtest_params_string = nullptr;
  mutable_abtest_params_int =
      session_data_->mutable_trans_pack_info()->mutable_trans_abtest_params()->mutable_abtest_params_int();
  mutable_abtest_params_string =
      session_data_->mutable_trans_pack_info()->mutable_trans_abtest_params()->mutable_abtest_params_string();
  // 填入透传参数
  (*mutable_abtest_params_int)["pacing_bonus_sample_rate"] = 10;
  (*mutable_abtest_params_int)["RL_bonus_sample_rate"] = 5;
  (*mutable_abtest_params_int)["enable_pacing_bonus_ratio"] = 0;
  (*mutable_abtest_params_int)["enable_rl_bonus_record"] = false;
  (*mutable_abtest_params_int)["enable_bonus_ctrl_strategy"] = static_cast<int64>(true);
  (*mutable_abtest_params_int)["enable_ecom_ad_drop_bonus_cpm"] = 0;
  (*mutable_abtest_params_int)["enable_xiaodian_order_drop_bonus_cpm_v2"] = false;
  (*mutable_abtest_params_int)["enable_ecom_drop_bonus_cpm_v2"] = false;
  (*mutable_abtest_params_int)["bouns_ctrl_sta_conf_exp_tag"] = 0;
  (*mutable_abtest_params_int)["enable_dsp_ack_new_flow"] = 1;
  (*mutable_abtest_params_int)["enable_ad_pack_chargeinfo"] = 1;
  (*mutable_abtest_params_string)["pacing_bonus_name"] = "all_2";
  (*mutable_abtest_params_int)["enable_fanstop_client_browsed_info"] = true;
  (*mutable_abtest_params_int)["use_fanstop_24hour_browsed"] = static_cast<int64>(true);
  (*mutable_abtest_params_int)["fanstop_live_server_show_ttl_seconds"] = 600;
  (*mutable_abtest_params_int)["disable_record_inner_delivery_log"] = 0;
  (*mutable_abtest_params_int)["ack_migrate_to_pack"] = static_cast<int64>(ack_migrate_to_pack_);
  (*mutable_abtest_params_int)["enable_ad_material_derived_photo"] = false;
  (*mutable_abtest_params_int)["enable_ad_material_nieuwland_derived_photo"] = false;
}

void PostProc::LogPreviewInfo() {
  if (session_data_->get_ac_fetcher_type() != kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    return;
  }
  TLOG(INFO) << "[preivew ad]: user_id: " << session_data_->get_user_id()
             << ", preview_type: " << static_cast<int>(session_data_->get_preview_type())
             << ", preview_creative_id: " << session_data_->get_preview_creative_id();
  TLOG(INFO) << "[preview ad]: ad_response: " << session_data_->get_ad_response()->ShortDebugString();
  TLOG(INFO) << "[preivew ad]: front_response: "
             << session_data_->get_front_server_response()->ShortDebugString();
}

}  // namespace ks::front_server
