#include "teams/ad/front_server_universe/engine/node/universe_data_postproc.h"

#include <algorithm>
#include <map>

#include "absl/strings/substitute.h"
#include "absl/time/clock.h"
#include "base/encoding/url_encode.h"
#include "base/time/timestamp.h"
#include "glog/logging.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/front_server_universe/util/ad_pos_manager/universe_pos_resource_v2.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/response_check/ad_response_valid_check.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/utility/front_logging.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"

using kuaishou::ad::DebugAbtest;

namespace ks::front_server {

void UniverseDataPostProc::Initialize(ContextData* context) {
  session_data_ = context;
  dsp_ad_request_ = session_data_->mutable_ad_request();
  dsp_ad_response_ = session_data_->mutable_ad_response();
  local_llsid = session_data_->get_llsid();
  waken_ks_app_ = ks::front_server::UniverseData::GetKsWakenApp(context);
}

void UniverseDataPostProc::Clear() {
  session_data_ = nullptr;
  dsp_ad_request_ = nullptr;
  dsp_ad_response_ = nullptr;
}

bool UniverseDataPostProc::ProcessInner() {
  TimeRecorder timer(context_->GetMutableContextData<ContextData>(),
                     static_cast<int32_t>(kuaishou::ad::AdFrontNodeType::FRONT_NODE_DATA_POSTPROC_TYPE));

  auto* context = context_->GetMutableContextData<ContextData>();
  strategy_manager = context_->GetContextSystem<StrategyManager>();
  if (context == nullptr) {
    TLOG(ERROR) << "get ranking result failed, context is null";
    return false;
  }

  Initialize(context);  // 初始化

  if (session_data_->get_ud_request_universe_tiny()) {
    UniverseQueryStat(session_data_->mutable_ad_list(), session_data_,
                      engine_base::UniverseQueryStatCommon::FRONT_OUT);
  }

  auto* response = context_->GetRPCResponse<FrontServerResponse>();
  FillAthenaAdResponse(response->mutable_universe_response(), context->UniverseRequest());
  if (context->UniverseRequest().universe_debug_param().aggregate_page_new_link_switch() ||
      session_data_->UniverseRequest().universe_task_type() == 4) {
    std::map<int64_t, kuaishou::ad::AdLog> ad_log_map;
    auto* style_info_map = session_data_->mutable_style_info_resp()->mutable_style_info();
    session_data_->mutable_ad_list()->GetAllWithFunc([&](kuaishou::ad::AdResult* ad_result,
                                                         RankAdCommon* ad_common) {
      auto *task_data = response->mutable_universe_response()->add_task_data();
      const auto& base_info = ad_result->ad_deliver_info().ad_base_info();
      task_data->set_account_id(base_info.account_id());
      task_data->set_dsp_creative_id(base_info.creative_id());
      task_data->set_dark_ad(base_info.ad_callback_passback().placement_type() !=
                             ::kuaishou::ad::AdEnum_PlacementType_PT_UNION);
      task_data->set_dsp_campaign_type(base_info.campaign_type());
      task_data->mutable_ad_log_pb()->CopyFrom(ad_result->ad_log());
      // 改成 CPA 计费
      auto encode_charge_info_string = task_data->mutable_ad_log_pb()->mutable_client_ad_log()->charge_info();
      serving_base::AesCrypter aes_crypter;
      std::string aes_charge_info_str;
      std::string raw_charge_info_str;
      if (base::Base64Decode(encode_charge_info_string, &aes_charge_info_str)) {
        if (aes_crypter.Decrypt(aes_charge_info_str, &raw_charge_info_str)) {
          kuaishou::ad::AdChargeInfo raw_charge_info;
          if (raw_charge_info.ParseFromString(raw_charge_info_str)) {
            auto& style_info = (*style_info_map)[base_info.creative_id()];
            raw_charge_info.mutable_ad_deliver_info()->set_price(style_info.unit().cpa_bid());
            auto* base_info = raw_charge_info.mutable_ad_deliver_info()->mutable_ad_base_info();
            base_info->mutable_ad_callback_passback()->set_charge_action_type(
                style_info.unit().ocpx_action_type());
            base_info->mutable_ad_callback_passback()->set_bid_type(kuaishou::ad::AdEnum::CPA);
            base_info->set_bid_type(kuaishou::ad::AdEnum::CPA);
            base_info->set_charge_action_type(style_info.unit().ocpx_action_type());
            base_info->add_charge_action_list(style_info.unit().ocpx_action_type());

            raw_charge_info.SerializeToString(&raw_charge_info_str);
            base::Base64Encode(aes_crypter.Encrypt(raw_charge_info_str), &encode_charge_info_string);
            task_data->mutable_ad_log_pb()->mutable_client_ad_log()->set_charge_info(
                encode_charge_info_string);
          }
        }
      }
      ad_log_map[task_data->dsp_creative_id()].CopyFrom(task_data->ad_log_pb());
    });
    for (auto& imp_ad_info : *response->mutable_universe_response()->mutable_imp_ad_info()) {
      for (auto& ad_info : *imp_ad_info.mutable_ad_info()) {
        ad_info.mutable_ad_log()->CopyFrom(ad_log_map[ad_info.ad_base_info().creative_id()]);
        auto& style_info = (*style_info_map)[ad_info.ad_base_info().creative_id()];
        auto* callback_passback = ad_info.mutable_ad_base_info()->mutable_ad_callback_passback();
        callback_passback->set_charge_action_type(style_info.unit().ocpx_action_type());
        callback_passback->set_bid_type(kuaishou::ad::AdEnum::CPA);
        callback_passback->set_price(style_info.unit().cpa_bid());
        callback_passback->set_unit_type(style_info.unit().unit_type());
      }
    }
  }

  if (dsp_ad_response_->has_debug_str() && dsp_ad_response_->debug_str().creative_debug_size() > 0 &&
      !session_data_->IsTestRequest()) {
    std::string page_id = absl::StrCat(session_data_->get_page_id());
    if (session_data_->get_ud_request_universe_tiny()) {
      page_id = "19100";
    }
    DebugReply(dsp_ad_response_->debug_str(), "universe",
      kuaishou::log::ad::AdTraceFilterCondition::NOT_IN_ROARING_INDEX, page_id);
  }

  session_data_->dot_perf->Interval((absl::GetCurrentTimeNanos() - session_data_->get_start_time_ns()) / 1000,
                                    "process_latency", "universe", "all");
  if (FrontKconfUtil::UniverseLatencyAppSet()->count(session_data_->copy_ud_app_id()) > 0) {
    session_data_->dot_perf->Interval(
        (absl::GetCurrentTimeNanos() - session_data_->get_start_time_ns()) / 1000, "process_latency",
        "universe", session_data_->copy_ud_app_id());
  }
  TLOG_EVERY_N(INFO, FrontKconfUtil::logReqDetailFrequency())
      << " request detail user id = " << dsp_ad_request_->reco_user_info().id();
  int32_t adcount = session_data_->get_ud_res_adnum();
  for (const auto& imp_ad_info : response->universe_response().imp_ad_info()) {
    adcount += imp_ad_info.ad_info_size();
    session_data_->set_ud_res_adnum(adcount);
  }
  session_data_->dot_perf->Count(adcount, "front_server_ad_dsp_adcount");
  if (session_data_->get_ud_is_universe_aggregation_scene()) {
    session_data_->dot_perf->Interval(session_data_->mutable_ad_list()->Size(), "universe_agg_scene_adjust",
                                      "agg_scene_ad_num_final",
                                      absl::StrCat(session_data_->get_ud_pos_id()));  // NOLINT
  }
  if (adcount > 0) {
    session_data_->dot_perf->Count(1, "front_server_ad_response_count");
  }
  session_data_->LogInfo(*response);

  session_data_->dot_perf->Interval(session_data_->get_ud_universe_pos_cpm_bound(), "universe_pos_cpm_bound");
  session_data_->dot_perf->Interval(session_data_->get_universe_rtb_coff() * 10e6, "universe_rtb_coff");
  PERF_FINAL_AD_LIST(session_data_);
  Clear();
  return true;
}

void UniverseDataPostProc::FillAthenaAdResponse(AthenaAdResponse* res, const AthenaAdRequest& athena_req) {
  if (!res) {
    return;
  }
  std::unordered_map<std::string, std::vector<AdResult>> ad_grid_info;
  int32_t ad_style = session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() == 0
                         ? 0
                         : session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).ad_style();
  const std::string& componentized_exp_group = session_data_->copy_ud_componentized_exp_group();
  res->set_llsid(session_data_->get_llsid());
  res->set_admit_invalid_type(session_data_->GetAdInvalidFlag());
  res->mutable_admit_info()->set_admit_invalid_type(session_data_->GetAdInvalidFlag());
  res->mutable_admit_info()->set_quality_score(session_data_->get_universe_quality_data_predict_cpm());
  res->mutable_admit_info()->set_skip_cpm_quality_filter(
      !session_data_->get_should_be_filtered_by_cpm_thres());
  res->mutable_admit_info()->set_antispam_code(session_data_->get_antispam_code());
  res->mutable_admit_info()->set_is_hit_rank_cache(session_data_->get_is_universe_cached_res());
  res->mutable_admit_info()->set_user_id(session_data_->get_user_id());
  res->mutable_admit_info()->set_elastic_config_group_idx(
      session_data_->get_ud_universe_elastic_info().final_config_group_index);
  res->mutable_admit_info()->set_timeout(session_data_->UniverseRequest().timeout());
  res->mutable_splash_policy()->CopyFrom(dsp_ad_response_->global_ext_data().splash_policy());
  res->mutable_debug_message()->CopyFrom(dsp_ad_response_->debug_str());
  if (session_data_->get_ud_meet_componentized_precondition() &&
      session_data_->get_ud_hit_componentized_monitor_exp_whitelist()) {
    DebugAbtest debug_ab_info;
    debug_ab_info.set_value_type(4);
    debug_ab_info.set_value_str(componentized_exp_group);
    (*res->mutable_debug_message()->mutable_debug_adtest_params())["componentExpGroup"] = debug_ab_info;
  }
  res->set_style_component_flag(ks::front_server::UniverseData::GetStyleComponentFlag(session_data_));
  // 聚合竞价标识
  res->set_is_aggregation_bidding(session_data_->get_ud_carousel_admit());
  auto* style_info_map = session_data_->mutable_style_info_resp()->mutable_style_info();
  int dpa_num = 0;
  bool deliver_live_audience_ads = false;

  session_data_->mutable_ad_list()->GetAllWithFunc(
      [this](kuaishou::ad::AdResult* ad_result, RankAdCommon*  /*ad_common*/) {
        CheckAdLabel(ad_result);
        SetTransparentAdPackAdInfo(*session_data_, ad_result, *(session_data_->get_style_info_resp()));
      });

  static std::string source_type_prefix = "DSP.UNIVERSE";
  session_data_->mutable_ad_list()->GetAllWithFunc(
      [this, &ad_grid_info](kuaishou::ad::AdResult* ad_result, RankAdCommon* ad_common) {
        const auto& universe_ad_deliver_info = ad_result->ad_deliver_info().universe_ad_deliver_info();
        int64_t account_id = ad_result->ad_deliver_info().ad_base_info().account_id();
        int64_t campaign_id = ad_result->ad_deliver_info().ad_base_info().campaign_id();
        int64_t unit_id = ad_result->ad_deliver_info().ad_base_info().unit_id();
        int64_t creative_id = ad_result->ad_deliver_info().ad_base_info().creative_id();
        auto campaign_type = ad_result->ad_deliver_info().ad_base_info().campaign_type();
        auto ocpx_action_type = ad_result->ad_deliver_info().ad_base_info().ocpc_action_type();
        auto industry_parent_id_v3 = ad_result->ad_deliver_info().ad_base_info().first_industry_id_v3();
        auto account_type = ad_result->ad_deliver_info().ad_base_info().account_type();
        auto promotion_type = ad_result->ad_deliver_info().ad_base_info().promotion_type();
        auto ad_queue_type = ks::ad_base::GetAdQueueType(ad_result->ad_deliver_info());
        auto is_author_follow =
            ad_result->ad_deliver_info().online_join_params_transparent().is_author_follow();
        AdResponseValidCheck checker(session_data_);
        if (!checker.IsVaildAdResponse(*ad_result, "universe")) {
          session_data_->mutable_ad_select_stage_infos()->SetAdResponseFilterInfo(
              ad_common->IsHardAd(), ad_result->ad_deliver_info().ad_base_info().creative_id(),
              kuaishou::log::ad::AdTraceFilterCondition::DATAPROC_INVALID_TYPE_FILTER,
              kuaishou::ad::AdFrontNodeType::FRONT_NODE_DATA_POSTPROC_TYPE);
          ad_common->SetAdInValid();
          return;
        }
        auto key = absl::Substitute(
            "$0_$1_$2_$3", universe_ad_deliver_info.page_id(), universe_ad_deliver_info.sub_page_id(),
            universe_ad_deliver_info.grid_unit_id(), universe_ad_deliver_info.pos_id());
        ad_grid_info[key].push_back(*ad_result);
      },
      true);

  for (auto& item : ad_grid_info) {
    auto *imp_ad_grid_info = res->add_imp_ad_info();
    const auto& universe_ad_deliver_info = item.second[0].ad_deliver_info().universe_ad_deliver_info();

    imp_ad_grid_info->set_page_id(universe_ad_deliver_info.page_id());
    imp_ad_grid_info->set_sub_page_id(universe_ad_deliver_info.sub_page_id());
    imp_ad_grid_info->set_grid_pos(universe_ad_deliver_info.grid_pos());
    imp_ad_grid_info->set_grid_unit_id(universe_ad_deliver_info.grid_unit_id());
    imp_ad_grid_info->set_type(universe_ad_deliver_info.type());
    imp_ad_grid_info->set_pos_id(universe_ad_deliver_info.pos_id());
    imp_ad_grid_info->set_ad_style(universe_ad_deliver_info.ad_style());
    imp_ad_grid_info->set_sub_type(universe_ad_deliver_info.sub_type());
    imp_ad_grid_info->set_app_id(session_data_->copy_ud_app_id());
    imp_ad_grid_info->set_medium_uid(
        session_data_->get_ad_request()->universe_ad_request_info().medium_uid());
    imp_ad_grid_info->set_cooperation_mode(session_data_->get_ud_cooperation_mode());

    for (auto& ad_result : item.second) {
      const auto& campaign_type = ad_result.ad_deliver_info().ad_base_info().campaign_type();
      const auto& ocpx_action_type = ad_result.ad_deliver_info().ad_base_info().ocpc_action_type();
      auto& style_info = (*style_info_map)[ad_result.ad_deliver_info().ad_base_info().creative_id()];
      auto *ad_info = imp_ad_grid_info->add_ad_info();
      auto* pos_info = ad_info->mutable_pos_info();
      pos_info->set_source_type(ad_result.ad_source_type());
      pos_info->set_pos_id(universe_ad_deliver_info.pos_id());
      pos_info->set_ad_queue_type(ks::ad_base::GetAdQueueType(ad_result.ad_deliver_info()));
      if (session_data_->borrow_ud_app_id() != "kuaishou" &&
          session_data_->borrow_ud_app_id() != "kuaishou_nebula") {
        // 联盟的流量强制写死为 50
        pos_info->set_pos_id(50);
      }
      kuaishou::ad::AdDataV2 ad_data_v2;
      const std::string& ad_data_v2_json_str = ad_result.ad_deliver_info().ad_base_info().ad_data_v2();
      if (!::google::protobuf::util::JsonStringToMessage(ad_data_v2_json_str, &ad_data_v2).ok()) {
        LOG(WARNING) << "JsonStringToMessage fail, ad_data_v2_json_str:" << ad_data_v2_json_str;
      }
      FillSspMediemInfo(universe_ad_deliver_info.pos_id(), ad_info);
      FillAdBaseInfo(ad_result, ad_info, *imp_ad_grid_info, ad_data_v2);
      FillAdMaterialInfo(ad_result, ad_info, *imp_ad_grid_info);
      FillAdConversionInfo(ad_result, *imp_ad_grid_info, ad_info, style_info, ad_data_v2);
      FillAdTrackInfo(ad_result, ad_info);
      FillAdLog(ad_result, ad_info);
      FillAppComplianceInfo(ad_result, *imp_ad_grid_info, ad_info, athena_req, style_info);
      FillAdStyleInfo(ad_result, *imp_ad_grid_info, ad_info, athena_req, style_info);
      FillPlayDetailInfo(*imp_ad_grid_info, ad_result, ad_info);
      ad_info->mutable_advertiser_info()->set_user_id(ad_result.ad_deliver_info().ad_base_info().author_id());
      // 填充原始 AdResult, 因为上游是 API,为了向前兼容保留 AdResult 对象
      ad_info->mutable_original_ad_result()->CopyFrom(ad_result);
      PERF_DATA(absl::Substitute("$0_$1_universe_athena_perf_ad_count",
                                 universe_ad_deliver_info.page_id(), universe_ad_deliver_info.sub_page_id()),
                1);
      if (universe_ad_deliver_info.sub_page_id() == ********) {
        if (ad_info->ad_base_info().ad_show_enable()) {
          PERF_DATA("nebula_app_start_ad_show_true", 1);
        } else {
          PERF_DATA("nebula_app_start_ad_show_false", 1);
        }
      }

      if (!session_data_->UniverseRequest().device_info().ua_ks().empty() &&
          (SPDM_enable_universe_update_user_agent(session_data_->get_spdm_ctx()) ||
           FrontKconfUtil::sdkFlowWhiteAccountUpdateUA()->count(style_info.account().id()) ||
           FrontKconfUtil::sdkFlowWhiteProductUpdateUA()->count(style_info.account().product_name()))) {
        // 暗投预算判断
        bool is_dark_ad = false;
        std::unordered_set<int64_t> resource_ids;
        const kuaishou::ad::tables::Unit* p_unit = &(style_info.unit());
        base::Json resource_json(StringToJson(p_unit->resource_ids()));
        if (resource_json.IsArray()) {
          for (auto* item : resource_json.array()) {
            int64_t id = item->IntValue(-1);
            if (id > 0) {
              resource_ids.insert(id);
            }
          }
        }
        if (!(p_unit->resource_id() == 5 || p_unit->resource_id() == 10 ||
              ad_base::stl::HasKey(5, resource_ids) || ad_base::stl::HasKey(10, resource_ids))) {
          is_dark_ad = true;
        }
        // SDK 流量判断
        static absl::flat_hash_set<int32_t> sdk_flow_mode_set{1, 4, 6, 8};
        int32_t cooperation_mode = session_data_->get_pos_manager().GetCooperationMode();
        bool sdk_flow = sdk_flow_mode_set.contains(cooperation_mode);
        if (sdk_flow && is_dark_ad) {
          // 填充字段
          ad_info->set_ks_ua_pattern(true);
        }
      }
      if (!session_data_->UniverseRequest().device_info().ua_ks().empty() &&
          FrontKconfUtil::universeKsUaDspIdSet()->count(
              ad_result.ad_deliver_info().ad_base_info().rta_source_type())) {  // NOLINT
        // 填充字段
        ad_info->set_ks_ua_pattern(true);
      }

      if (campaign_type == kuaishou::ad::AdEnum::DPA_CAMPAIGN) {
        dpa_num++;
      }
      if (FrontKconfUtil::universeLiveAudienceRateLimitOcpxTypes()->count(ocpx_action_type) > 0) {
        deliver_live_audience_ads = true;
      }
      if (session_data_->get_ud_hit_componentized_monitor_exp_whitelist() &&
          ks::front_server::UniverseData::GetStyleComponentFlag(session_data_)) {
        const auto& ad_style_prefers = ad_data_v2.origin_style_info().universe_info().ad_style_prefers();
        for (const auto& ad_style_prefer : ad_style_prefers) {
          std::string subtag;
          if (ad_style_prefer.type() == 10002) {
            subtag = "playcard_componentized";
          } else if (ad_style_prefer.type() == 10003) {
            subtag = "endcard_componentized";
          }
          if (!subtag.empty()) {
            session_data_->dot_perf->Count(1, subtag, absl::StrCat(ad_style_prefer.material_id()),
                                           componentized_exp_group, absl::StrCat(ad_style),
                                           absl::StrCat(campaign_type));
          }
        }
      }
    }
  }

  falcon::Stat("front_server.dpa_num_after_front_server", dpa_num);
  if (deliver_live_audience_ads) {
    session_data_->dot_perf->Count(1, "universe_live_audience_ad_deliver");
  }
  std::sort(res->mutable_imp_ad_info()->begin(), res->mutable_imp_ad_info()->end(),
            [](ImpAdGridInfo& lhs, ImpAdGridInfo& rhs) {
              int32_t lhs_grid_unit_id = 0, rhs_grid_unit_id = 0;
              if (!absl::SimpleAtoi(lhs.grid_unit_id(), &lhs_grid_unit_id) ||
                  !absl::SimpleAtoi(rhs.grid_unit_id(), &rhs_grid_unit_id)) {
                LOG(ERROR) << "ImpAdGridInfo convert error, lhs_grid_unit_id: " << lhs_grid_unit_id
                           << ", rhs_grid_unit_id: " << rhs_grid_unit_id;
                return true;
              }
              return lhs_grid_unit_id < rhs_grid_unit_id;
            });

  // 激励视频再看一个：第一个广告 taskType 为 4，标识有再看一个
  const auto& task_type_list = session_data_->UniverseRequest().accept_task_type();
  if (std::count(task_type_list.begin(), task_type_list.end(), 4) && ad_style == 2 &&
      res->imp_ad_info_size() == 2 && res->mutable_imp_ad_info(0)->ad_info_size() > 0 &&
      res->mutable_imp_ad_info(1)->ad_info_size() > 0) {
    if (res->imp_ad_info(0).ad_info(0).ad_base_info().extra_click_reward()) {
      res->mutable_imp_ad_info()->RemoveLast();
    } else {
      res->mutable_imp_ad_info(0)->mutable_ad_info(0)->mutable_ad_base_info()->set_task_type(4);
      res->mutable_imp_ad_info(1)
          ->mutable_ad_info(0)
          ->mutable_ad_log()
          ->mutable_client_ad_log()
          ->mutable_client_params()
          ->set_universe_second_ad(true);
    }
  }
}

void UniverseDataPostProc::FillServerShowFrequency(kuaishou::ad::universe::AdBaseInfo* ad_base_info) {
  static const int64_t ONE_DAY_TIMESPAN = 24 * 3600L * 1000000L;
  static const int64_t TWO_DAY_TIMESPAN = 2 * ONE_DAY_TIMESPAN;
  static const int64_t THREE_DAY_TIMESPAN = 3 * ONE_DAY_TIMESPAN;
  static const int64_t SEVEN_DAY_TIMESPAN = 7 * ONE_DAY_TIMESPAN;
  int64_t now = base::GetTimestamp();
  int32 freq_1{0}, freq_2{0}, freq_3{0}, freq_7{0};
  const auto& ad_user_info = dsp_ad_request_->ad_user_info();
  for (int i = 0; i < ad_user_info.ad_browsed_info_size(); ++i) {
    int64_t ts = ad_user_info.ad_browsed_info(i).timestamp();
    if (ts <= 0) {
      continue;
    }
    if (now - ts <= ONE_DAY_TIMESPAN) {
      freq_1++;
    } else if (now - ts <= TWO_DAY_TIMESPAN) {
      freq_2++;
    } else if (now - ts <= THREE_DAY_TIMESPAN) {
      freq_3++;
    } else if (now - ts <= SEVEN_DAY_TIMESPAN) {
      freq_7++;
    }
  }
  freq_2 += freq_1;
  freq_3 += freq_2;
  freq_7 += freq_3;

  // 数组分别存 一日频数，二日频数，三日频数和七日频数
  ad_base_info->mutable_client_optimize_params()->add_freq_server_show(freq_1);
  ad_base_info->mutable_client_optimize_params()->add_freq_server_show(freq_2);
  ad_base_info->mutable_client_optimize_params()->add_freq_server_show(freq_3);
  ad_base_info->mutable_client_optimize_params()->add_freq_server_show(freq_7);
}

void UniverseDataPostProc::FillAdBaseInfo(const AdResult& ad_result, AdInfo* ad_info,
                                          const ImpAdGridInfo& imp_ad_grid_info,
                                          const kuaishou::ad::AdDataV2& ad_data_v2) {
  kuaishou::ad::universe::AdBaseInfo* ad_base_info = ad_info->mutable_ad_base_info();
  const auto first_industry_id =
    ad_result.ad_deliver_info().online_join_params_transparent().first_industry_id_v3();
  const auto second_industry_id = ad_result.ad_deliver_info().ad_base_info().industry_id_v3();
  const auto& product_name = ad_result.ad_deliver_info().ad_base_info().product_name();
  const auto account_id = ad_result.ad_deliver_info().ad_base_info().account_id();
  ad_base_info->set_unit_name(ad_result.ad_deliver_info().ad_base_info().unit_name());
  ad_base_info->set_campaign_id(ad_result.ad_deliver_info().ad_base_info().campaign_id());
  ad_base_info->set_unit_id(ad_result.ad_deliver_info().ad_base_info().unit_id());
  ad_base_info->set_creative_id(ad_result.ad_deliver_info().ad_base_info().creative_id());
  ad_base_info->set_campaign_type(ad_result.ad_deliver_info().ad_base_info().campaign_type());
  ad_base_info->set_photo_id(ad_result.ad_deliver_info().ad_base_info().photo_id());
  ad_base_info->set_pic_id(ad_result.ad_deliver_info().ad_base_info().pic_id());
  ad_base_info->set_ad_pos(ad_result.ad_deliver_info().universe_ad_deliver_info().ad_pos());
  ad_base_info->set_ad_source_type(ad_result.ad_source_type());
  ad_base_info->set_app_icon_url(ad_result.ad_deliver_info().ad_base_info().app_icon_url());
  ad_base_info->set_is_request_universe_tiny(session_data_->get_ud_request_universe_tiny());
  //  非下载类取消兜底
  if (ad_base_info->app_icon_url().empty() &&
      ad_result.ad_deliver_info().ad_base_info().campaign_type() == kuaishou::ad::AdEnum::APP) {
    // ios 大部分没有 icon ，客户端兜底图案不够美观，重新下发一个兜底图案，后续版本会去掉兜底图渲染
    ad_base_info->set_app_icon_url("https://static.yximgs.com/udata/pkg/ad-universe/universe_icon.png");
  }
  ad_base_info->set_app_name(ad_result.ad_deliver_info().ad_base_info().app_name());
  ad_base_info->set_app_package_name(ad_result.ad_deliver_info().ad_base_info().package_name());
  ad_base_info->set_app_score(ad_result.ad_deliver_info().ad_base_info().app_score());
  ad_base_info->set_adx_source_type(ad_result.ad_deliver_info().ad_base_info().adx_source_type());
  ad_base_info->set_rta_source_type(ad_result.ad_deliver_info().ad_base_info().rta_source_type());
  if (FrontKconfUtil::replaceDescriptionAccountSet()->data().IsHit(first_industry_id, second_industry_id,
    product_name, account_id)) {
    ad_base_info->set_ad_source_description("广告");
  } else if (FrontKconfUtil::ReplaceDescriptionAppSet()->count(session_data_->copy_ud_app_id()) > 0 &&
      (ad_result.ad_source_type() == kuaishou::ad::DSP)) {
    ad_base_info->set_ad_source_description("快手广告");
  } else {
    ad_base_info->set_ad_source_description(
        ad_result.ad_deliver_info().ad_base_info().adx_source_description());
  }
  ad_base_info->set_encode_charge_info(ad_result.encode_charge_info());
  ad_base_info->set_account_id(account_id);
  ad_base_info->set_campaign_name(ad_result.ad_deliver_info().ad_base_info().campaign_name());
  ad_base_info->set_delivery_start_ts(ad_result.ad_deliver_info().ad_base_info().delivery_start_ts());
  ad_base_info->set_delivery_end_ts(ad_result.ad_deliver_info().ad_base_info().delivery_end_ts());
  ad_base_info->set_callback_passback(ad_result.ad_deliver_info().ad_base_info().callback_passback());
  ad_base_info->set_ad_show_enable(ad_result.ad_deliver_info().ad_base_info().ad_show_enable());
  ad_base_info->set_ad_data_v2(ad_result.ad_deliver_info().ad_base_info().ad_data_v2());
  ad_base_info->set_short_display_info(ad_result.ad_deliver_info().ad_base_info().short_display_info());
  ad_base_info->set_play_n(ad_result.ad_deliver_info().ad_base_info().play_n());
  ad_base_info->mutable_ad_rtb_info()->set_ecpm(ad_result.ad_deliver_info().ad_base_info().rtb_ecpm());
  ad_base_info->mutable_ad_rtb_info()->set_rtb_virtual_cpm(ad_result.ad_deliver_info().rtb_virtual_price());
  // [xiaowentao] 写入标志到 athena，如果为 true 的话，OpenAPI 后端将对该请求的 rtb_ecpm 和 virtual_price 都不
  // 乘以分成系数，front server 传过去的已经是乘好了的
  ad_base_info->mutable_ad_rtb_info()->set_universe_dynamic_share_skip_apply_ratio(
      ad_result.ad_deliver_info().ad_base_info().universe_dynamic_share_rewrite_share_ratio());
  if (session_data_->get_is_universe_adn_flow()) {
    ad_base_info->mutable_ad_rtb_info()->set_ecpm(0);
    ad_base_info->mutable_ad_rtb_info()->set_rtb_virtual_cpm(0);
  }
  ad_base_info->set_product_name(product_name);
  ad_base_info->set_first_industry_id(first_industry_id);
  ad_base_info->set_second_industry_id(second_industry_id);
  ad_base_info->set_cal_price(ad_result.ad_deliver_info().price() +
                              ad_result.ad_deliver_info().price_audience());
  if (!session_data_->get_ud_white_traffic_ids().empty()) {
    const auto& material_info = ad_result.ad_deliver_info().ad_base_info().material_info();
    int64_t rule_id =
        material_info.material_feature_size() > 0 ? material_info.material_feature(0).rule_id() : 0;
    auto* hit_traffic_ids = ad_base_info->mutable_hit_traffic_ids();
    engine_base::AdCreativeAuditV2::GetInstance()->get_hit_traffics_creative_rule(
        session_data_->get_ud_white_traffic_ids(), absl::StrCat(ad_base_info->creative_id(), "_", rule_id),
        hit_traffic_ids);
  }
  if (session_data_->get_ud_is_universe_aggregation_scene()) {
    ad_base_info->set_is_neo_scan_aggregation_ad(true);
  }

  // 预览广告一级行业使用映射下发
  if (session_data_->get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    int64_t industry_id = ad_result.ad_deliver_info().ad_base_info().industry_id_v3();
    const auto industry_map_ptr = FrontKconfUtil::previewIndustryIdMap();
    if (industry_map_ptr) {
      ad_base_info->set_first_industry_id(industry_map_ptr->data().GetFirstIndustryId(industry_id));
    }
  }
  ad_base_info->set_server_exp_tag(ad_result.ad_deliver_info().ad_base_info().server_exp_tag());
  auto* style_info_map = session_data_->mutable_style_info_resp()->mutable_style_info();
  auto& style_info = (*style_info_map)[ad_result.ad_deliver_info().ad_base_info().creative_id()];
  if (style_info.valid()) {
    ad_base_info->set_app_id(style_info.unit().app_id());
    ad_base_info->set_package_id(style_info.unit().package_id());
    if (style_info.campaign().type() == kuaishou::ad::AdEnum::APP_ADVANCE ||
        style_info.campaign().type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) {
      ad_base_info->set_app_id(ad_result.ad_deliver_info().ad_base_info().ad_app_id());
      ad_base_info->set_package_id(ad_result.ad_deliver_info().ad_base_info().ad_package_id());
    }
    ad_base_info->set_csite(style_info.unit().resource_id());
  }

  JsonObject object;
  if (ad_result.item_ad_result().doc_use_name()) {
    object.set("doc_use_name", true);
  } else {
    object.set("doc_use_name", false);
  }
  object.set("star_user_name", ad_result.item_ad_result().star_user_name());
  ad_base_info->set_extend_field(object.ToString());

  Json display_json(StringToJson(ad_result.ad_deliver_info().ad_base_info().display_info()));
  ad_base_info->set_ad_action_description(display_json.GetString("actionBar", ""));

  // 修改 actionBar 文案
  const int64_t ocpx_action_type = ad_result.ad_deliver_info().ad_base_info().ocpc_action_type();
  const auto& app_label_map = FrontKconfUtil::universeAppLabelMap()->data().app_label_map();
  auto iter = app_label_map.find(ocpx_action_type);
  if (iter != app_label_map.end()) {
    ad_base_info->set_install_app_label(iter->second.install_app_label());
    ad_base_info->set_open_app_label(iter->second.open_app_label());
  }

  auto white_list = FrontKconfUtil::universeActionBarAppLabelWhiteList();
  const auto& account_white_list = white_list->data().account_id();
  const auto& product_white_list = white_list->data().product_name();
  bool is_in_white_list =
      (std::count(account_white_list.begin(), account_white_list.end(), ad_base_info->account_id()) > 0) ||
      (std::count(product_white_list.begin(), product_white_list.end(), ad_base_info->product_name()) > 0);
  if (is_in_white_list) {
    auto get_value_from_config_string = [](const std::string& config,
                                           const std::string& name) -> std::string {
      if (config.empty()) {
        return std::string{};
      }
      base::Json display_config(base::StringToJson(config));
      if (display_config.IsObject()) {
        return display_config.GetString(name, "");
      }
      return std::string{};
    };
    std::string action_bar = get_value_from_config_string(style_info.creative().display_info(), "actionBar");
    ad_base_info->set_install_app_label(action_bar);
    ad_base_info->set_open_app_label(action_bar);
  }

  // actionBar 文案增加实验
  std::string il = session_data_->get_spdm_ctx().TryGetString(
      absl::Substitute("universe_install_label_$0", ocpx_action_type), "");
  std::string ol = session_data_->get_spdm_ctx().TryGetString(
      absl::Substitute("universe_open_label_$0", ocpx_action_type), "");
  if (!il.empty())
    ad_base_info->set_install_app_label(il);
  if (!ol.empty())
    ad_base_info->set_open_app_label(ol);

  // 联盟磁力金牛速推广告填充默认 action_bar 信息
  if (style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      style_info.account().account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP &&
      style_info.campaign().promotion_type() == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
    if (style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
        style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST) {
      ad_base_info->set_open_app_label("立即关注");
    } else if (style_info.unit().ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED) {
      ad_base_info->set_open_app_label("立即购买");
    }
  }

  // 短视频下发加密 photo_id
  if (style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      style_info.has_photo_status()) {
    ad_base_info->set_encrypted_photo_id(style_info.photo_status().encrypted_photo_id());
  }
  ad_base_info->set_is_account_live(ad_result.ad_deliver_info().ad_base_info().is_account_live());
  std::string ad_description = display_json.GetString("description", "");
  // 珍珠港广告描述暂时去掉换行符 避免前端显示问题
  if (ad_result.ad_deliver_info().universe_ad_deliver_info().app_id() == "pearl") {
    ad_description.erase(std::remove(ad_description.begin(), ad_description.end(), '\n'),
                         ad_description.end());
  }
  ad_base_info->set_ad_description(ad_description);
  if (ad_base_info->campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
      ad_base_info->campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
    ad_base_info->mutable_ks_version_info()->set_kwai_version(session_data_->copy_ud_ks_version());
    ad_base_info->mutable_ks_version_info()->set_nebula_version(session_data_->copy_ud_nebula_version());
  }

  ad_base_info->set_author_id(ad_result.ad_deliver_info().ad_base_info().author_id());
  // 直播推广 直播直投下 修改文案
  if ((ad_result.ad_deliver_info().ad_base_info().campaign_type() ==
       kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) &&
      ad_result.ad_deliver_info().ad_base_info().item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
    ad_base_info->set_install_app_label("立即安装");
    ad_base_info->set_open_app_label("进入直播间");
    if (ad_result.ad_deliver_info().ad_base_info().ocpc_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
        ad_result.ad_deliver_info().ad_base_info().ocpc_action_type() ==
            kuaishou::ad::AD_MERCHANT_FOLLOW_FAST) {
      ad_base_info->set_ad_action_description("去关注");
    } else {
      ad_base_info->set_ad_action_description("去看看");
    }
  }
  // 代投 分销字段下发
  if (ad_result.ad_deliver_info().ad_base_info().campaign_type() ==
          kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      style_info.has_unit_support_info() && style_info.has_account()) {
    ad_base_info->set_unit_put_type(style_info.unit_support_info().put_type());
    ad_base_info->set_unit_support_type(style_info.unit_support_info().support_type());
    ad_base_info->set_account_user_id(style_info.account().user_id());
  }
  // 下发标志
  if (session_data_->get_ud_p2live_to_live_cids().count(ad_base_info->creative_id())) {
    ad_base_info->set_is_universe_show_live_stream(true);
  }
  // 直播推广 下发新样式标志
  if (ad_result.ad_deliver_info().ad_base_info().campaign_type() ==
      kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
    int32_t sdk_type = session_data_->get_ad_request()->universe_ad_request_info().sdk_type();
    const std::string& sdk_version =
        session_data_->get_ad_request()->universe_ad_request_info().sdk_version();
    bool is_sdk_3322 = ((sdk_type == 1 && engine_base::CompareAppVersion(sdk_version, "3.3.22") >= 0) ||
                        (sdk_type == 2 && engine_base::CompareAppVersion(sdk_version, "3.3.29") >= 0));
    bool is_live_new_style =
        ad_result.ad_deliver_info().ad_base_info().item_type() ==
            kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE ||
        (ad_result.ad_deliver_info().ad_base_info().item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE &&
         is_sdk_3322);
    /* 场景: 激励/全屏
     * 物料类型: 直播直投 sdk 3322+ 短视频推广所有流量
     * 实验组流量下发新样式标志
     */
    if (is_live_new_style && (imp_ad_grid_info.ad_style() == 2 || imp_ad_grid_info.ad_style() == 3 ||
                              (imp_ad_grid_info.ad_style() == 23 &&
                               ad_result.ad_deliver_info().ad_base_info().ad_rollout_size() == 1))) {
      ad_base_info->set_universe_live_new_style(true);
    }
  }

  auto url = ad_result.ad_deliver_info().ad_base_info().url();
  switch (ad_result.ad_deliver_info().ad_base_info().campaign_type()) {
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_APP:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MERCHANT_APP_PROMOTE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_LIVE_STREAM_PROMOTE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_FANSTOP_LIVE_TO_ALL:
      ad_base_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::APP_DOWNLOAD);  // 下载广告
      break;
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_TAOBAO:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_LANDING_PAGE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_SITE_PAGE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_CID:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_SEARCH:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_DPA_CAMPAIGN:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_WX_MINI_APP:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_KWAI_SERIAL_PROMOTION:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION:
      // 联盟小店通广告填充 ad_operation_type
      if (ad_result.ad_deliver_info().ad_base_info().is_universe_jinniu_to_deeplink()) {
        // 金牛广告转成 deeplink 方式
        ad_base_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::APP_DOWNLOAD);  // 下载广告
        break;
      }
      ad_base_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::OPEN_HTML5);  // 落地页广告
      break;
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_APP_ADVANCE:
      ad_base_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::APP_DOWNLOAD);  // deeplink
      break;
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE:
      ad_base_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::APP_DOWNLOAD);
      break;
    default:
      ad_base_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::UNKNOWN_CONVERSION_TYPE);
      break;
  }
  if (ad_result.ad_deliver_info().ad_base_info().campaign_type() ==
      ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
    auto ocpc_action_type = ad_result.ad_deliver_info().ad_base_info().ocpc_action_type();
    if (ocpc_action_type == kuaishou::ad::CID_ROAS ||
        ocpc_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID) {
      ad_base_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::OPEN_HTML5);
    } else {
      ad_base_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::APP_DOWNLOAD);
    }
  }
  if (style_info.unit().app_download_type() == 1) {
    ad_base_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::OPEN_HTML5);
  }
  // 设置 sdk 的 abtest 参数
  ad_base_info->set_exp_param(session_data_->copy_ud_sdk_exp_param());

  // 设置点击激励标志
  if (ad_data_v2.interaction_info().interactive_style() ==
          kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_UNKNOWN &&
      engine_base::AdKconfUtil::universeClickRewardConf()->data().CheckMaterial(
          session_data_->get_ud_end_card_id())) {
    ad_base_info->set_extra_click_reward(true);
  }

  if (ad_result.ad_deliver_info().ad_base_info().has_inspire_ad_info()) {
    ad_base_info->mutable_inspire_ad_info()->set_inspire_ad_bill_time(
        ad_result.ad_deliver_info().ad_base_info().inspire_ad_info().inspire_ad_bill_time());
  }
  if (ad_result.ad_deliver_info().ad_base_info().has_actionbar_color()) {
    ad_base_info->set_actionbar_color(ad_result.ad_deliver_info().ad_base_info().actionbar_color());
  }
  ad_base_info->set_version(ad_result.ad_deliver_info().ad_base_info().version());
  ad_base_info->mutable_ad_callback_passback()->CopyFrom(
      ad_result.ad_deliver_info().ad_base_info().ad_callback_passback());

  // 内容联盟与 feed 混排的场景下发 ecpm scvr 和频次等
  // API 使用但不会向媒体透传这个结构下的字段
  if (imp_ad_grid_info.ad_style() == 7 || imp_ad_grid_info.ad_style() == 9) {
    auto* optimize_params = ad_base_info->mutable_client_optimize_params();
    // API 根据先验 cpm 与后验 cpm 控制内容 feed 流中广告与内容出现的次序，千次单位元
    optimize_params->set_ecpm(ad_result.ad_deliver_info().cpm());
    // scvr : 历史问题在预估时填到了 cvr 字段，大部分是会填充的
    optimize_params->set_scvr(ad_result.ad_deliver_info().predict_info().cvr());
    // 历史频次：近 24 2*24 3*24 7*24 小时的 server_show 频次
    FillServerShowFrequency(ad_base_info);
  }
  if ((ad_result.ad_deliver_info().ad_base_info().campaign_type() ==
       ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) &&
      !waken_ks_app_.empty()) {
    ad_base_info->set_waken_ks_app(waken_ks_app_);
  }
  if (ad_result.ad_deliver_info().ad_base_info().campaign_type() ==
          ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
      !waken_ks_app_.empty()) {
    ad_base_info->set_live_stream_id(ad_result.ad_deliver_info().ad_base_info().live_stream_id());
    ad_base_info->set_waken_ks_app(waken_ks_app_);
  }
  if (ad_result.ad_deliver_info().ad_base_info().campaign_type() ==
          ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION &&
      !waken_ks_app_.empty()) {
    ad_base_info->set_waken_ks_app(waken_ks_app_);
  }
  ad_base_info->set_item_type(ad_result.ad_deliver_info().ad_base_info().item_type());

  if ((imp_ad_grid_info.ad_style() == 2 || imp_ad_grid_info.ad_style() == 12)) {
    bool is_in_black_list = false;
    auto black_list = FrontKconfUtil::universeInspireDeepBlackList();
    auto& account_black_list = black_list->data().account_id();
    auto& product_black_list = black_list->data().product_name();
    is_in_black_list =
        (std::count(account_black_list.begin(), account_black_list.end(), ad_base_info->account_id()) > 0) ||
        (std::count(product_black_list.begin(), product_black_list.end(), ad_base_info->product_name()) > 0);
    session_data_->dot_perf->Count(1, "universe_inspire_is_in_black_list",
                                   is_in_black_list ? "true" : "false");
    auto& task_type_list = session_data_->UniverseRequest().accept_task_type();
    auto campaign_type = ad_result.ad_deliver_info().ad_base_info().campaign_type();
    if (campaign_type == kuaishou::ad::AdEnum::APP) {
      if (std::count(task_type_list.begin(), task_type_list.end(), 2) && !is_in_black_list) {
        ad_base_info->set_task_type(2);
      } else {
        ad_base_info->set_task_type(1);
      }
    } else if ((campaign_type == kuaishou::ad::AdEnum::SITE_PAGE ||
                campaign_type == kuaishou::ad::AdEnum::AD_CID) &&
               std::count(task_type_list.begin(), task_type_list.end(), 3) && !is_in_black_list) {
      ad_base_info->set_task_type(3);
    }
  }
  if (!session_data_->get_ad_request()->ad_user_info().is_universe_fake_user()) {
    int64_t author_id = style_info.account().user_id();
    if (session_data_->get_follow_ids().find(author_id) != session_data_->get_follow_ids().end()) {
      ad_base_info->set_have_been_followed(true);
      session_data_->dot_perf->Count(1, "front_server_universe_find_follow");
    }
  }
  if (style_info.has_unit_small_shop_merchant_support_info()) {
    ad_base_info->set_item_id(style_info.unit_small_shop_merchant_support_info().item_id());
  }
  const auto material_info = ad_result.ad_deliver_info().ad_base_info().material_info();
  if (ad_result.ad_deliver_info().ad_base_info().item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE &&
      material_info.material_feature_size() > 0 && material_info.material_feature(0).rule_id() == 1) {
    ad_base_info->set_universe_live_type(kuaishou::ad::universe::AdBaseInfo_UniverseLiveType_LIVE_ORIGIN);
  }
  if (ad_result.ad_deliver_info().ad_base_info().campaign_type() ==
          kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE &&
      material_info.material_feature_size() > 0) {
    // 直播推广 下发样式标志
    int64_t rule_id = material_info.material_feature(0).rule_id();
    const auto& universe_live_style_config = FrontKconfUtil::universeLiveStyleMap()->data().conf_map();
    auto style_iter = universe_live_style_config.find(rule_id);
    if (style_iter != universe_live_style_config.end()) {
      if (session_data_->get_spdm_ctx().TryGetBoolean(style_iter->second.ab_key(), false)) {
        ad_base_info->set_universe_live_style(
            static_cast<::kuaishou::ad::universe::AdBaseInfo_UniverseLiveStyle>(
                style_iter->second.style_value()));
      }
    }
  }

  // 判断是否下发聚合中间页样式
  const auto& universe_ad_request_info = session_data_->get_ad_request()->universe_ad_request_info();
  const std::string& sdk_version = universe_ad_request_info.sdk_version();
  bool is_hit_new_white_list =
      FrontKconfUtil::aggregatePageProductNameAccountWhiteList()->data().IsInWhiteList(
          ad_base_info->product_name(), ad_base_info->account_id());
  bool is_hit_splash_aggregate_black_list =
      FrontKconfUtil::splashAggregatePageMutexBlackList()->data().IsInBlackList(
          imp_ad_grid_info.ad_style(), imp_ad_grid_info.app_id(), imp_ad_grid_info.pos_id());
  if (session_data_->UniverseRequest().universe_task_type() == 0 && is_hit_new_white_list &&
      (ad_base_info->campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE ||
       ad_base_info->campaign_type() == kuaishou::ad::AdEnum::AD_CID ||
       ad_base_info->campaign_type() == kuaishou::ad::AdEnum::TAOBAO) &&
      !is_hit_splash_aggregate_black_list &&
      engine_base::CompareAppVersion(sdk_version, *FrontKconfUtil::unionMidPageDefaultVersionControl()) >=
          0 &&
      universe_ad_request_info.sdk_type() == 1) {
    ad_base_info->set_is_union_bidding(true);
    session_data_->dot_perf->Count(1, "union_mid_page", "first_req_hit", "3");
  }

  ad_base_info->set_is_site(ad_result.ad_deliver_info().ad_base_info().is_site());
  if (imp_ad_grid_info.ad_style() == 23 && ad_result.ad_deliver_info().ad_base_info().ad_rollout_size() > 0) {
    ad_base_info->set_ad_rollout_size(ad_result.ad_deliver_info().ad_base_info().ad_rollout_size());
  }
  ad_base_info->set_account_type(ad_result.ad_deliver_info().ad_base_info().account_type());
  bool is_skip_qcpx_mode = session_data_->get_is_closure_flow() &&
      session_data_->is_closure_ad(ad_base_info->campaign_type(), ad_result.ad_deliver_info().ad_base_info().ocpc_action_type());   // NOLINT
  if (!is_skip_qcpx_mode) {
    ad_base_info->set_coupon_scope(ad_result.ad_deliver_info().ad_base_info().coupon_scope());
  } else {
    ad_base_info->set_is_closure(true);
  }
}

void UniverseDataPostProc::FillAdMaterialInfo(const AdResult& ad_result, AdInfo* ad_info,
                                              const ImpAdGridInfo&  /*imp_ad_grid_info*/) {
  auto* ad_material_info = ad_info->mutable_ad_material_info();
  ad_material_info->CopyFrom(ad_result.ad_deliver_info().ad_base_info().material_info());

  // 直播直投广告需要区分原生和剪裁
  if (ad_result.ad_deliver_info().ad_base_info().item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE &&
      ad_material_info->material_type() ==
          ::kuaishou::ad::AdEnum_AdMaterialType::AdEnum_AdMaterialType_PHOTO_MATERIAL) {
    bool is_origin_live =
        ad_material_info->material_feature_size() > 0 && ad_material_info->material_feature(0).rule_id() == 1;
    ad_material_info->set_material_type(
        is_origin_live ? ::kuaishou::ad::AdEnum_AdMaterialType::AdEnum_AdMaterialType_LIVE_ORIGIN_MATERIAL
                       : ::kuaishou::ad::AdEnum_AdMaterialType::AdEnum_AdMaterialType_PHOTO_MATERIAL);
    if (ad_material_info->material_feature_size() > 0) {
      ad_material_info->mutable_material_feature(0)->set_photo_id(1);
      ad_material_info->mutable_material_feature(0)->set_material_feature_type(
          is_origin_live
              ? ::kuaishou::ad::AdEnum_AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_LIVE_FEATURE
              : ::kuaishou::ad::AdEnum_AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE);
    }
  } else if (ad_material_info->material_type() ==
             ::kuaishou::ad::AdEnum_AdMaterialType::AdEnum_AdMaterialType_PHOTO_MATERIAL) {
    //  视频广告素材类型也填充为视频素材 目前只支持单视频广告 后续该逻辑应该迁移到 adserver
    auto photo_id = ad_result.ad_deliver_info().ad_base_info().photo_id();
    if (photo_id == 0) {
      photo_id = 1;
    }

    if (ad_material_info->material_feature_size() > 0) {
      ad_material_info->mutable_material_feature(0)->set_photo_id(photo_id);
      ad_material_info->mutable_material_feature(0)->set_material_feature_type(
          ::kuaishou::ad::AdEnum_AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE);
    }
  } else {
    //  图片广告素材类型全部填充为图片素材 目前不支持单个广告视频和图片混合素材 后续该逻辑应该迁移到 adserver
    for (auto& item : *(ad_material_info->mutable_material_feature())) {
      item.set_material_feature_type(
          ::kuaishou::ad::AdEnum_AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE);
    }
  }
}

void UniverseDataPostProc::FillAdConversionInfo(const AdResult& ad_result,
                                                const ImpAdGridInfo& imp_ad_grid_inf, AdInfo* ad_info,
                                                const StyleInfoItem& style_info,
                                                const kuaishou::ad::AdDataV2& ad_data_v2) {
  auto* ad_conversion_info = ad_info->mutable_ad_conversion_info();
  const auto& url = ad_result.ad_deliver_info().ad_base_info().url();
  const auto& schema_url = ad_result.ad_deliver_info().ad_base_info().schema_url();
  const auto& h5_url = ad_result.ad_deliver_info().ad_base_info().h5_url();
  bool is_sdk = session_data_->get_ad_request()->universe_ad_request_info().cooperation_mode() == 1;
  bool is_universe_feed = imp_ad_grid_inf.ad_style() == 7;
  bool forbidden_uni_feed_upload_url = is_sdk && is_universe_feed;
  const auto campaign_type = ad_result.ad_deliver_info().ad_base_info().campaign_type();
  const auto ocpc_action_type = ad_result.ad_deliver_info().ad_base_info().ocpc_action_type();

  ad_conversion_info->set_app_download_type(style_info.unit().app_download_type());
  if (ad_data_v2.has_market_uri()) {
    ad_conversion_info->set_market_url(ad_data_v2.market_uri());
    for (const auto& item : *FrontKconfUtil::brand2Detailbrand()) {
      if (item.second.find(session_data_->copy_ud_hit_phone_brand()) != std::string::npos) {
        ad_conversion_info->set_market_brand(item.first);
      }
    }
  }
  //  快享 自助中间页 url
  ad_conversion_info->set_mid_page_url(ad_data_v2.detail_mid_page().page_url());
  // 填充 deeplink url
  if (!schema_url.empty()) {
    // TODO(CBB) 以下逻辑非常 trick 容易出 bad case 后续和业务确认后会删除
    std::vector<std::string> split_url = absl::StrSplit(schema_url, "&");
    auto contain_backurl = [](std::string str) -> bool {
      std::string url_flag = "backurl";
      return (std::equal(url_flag.begin(), url_flag.end(), str.begin()));
    };
    split_url.erase(std::remove_if(split_url.begin(), split_url.end(), contain_backurl), split_url.end());
    std::string url_hack = absl::StrJoin(split_url, "&");
    ad_conversion_info->set_deeplink_url(url_hack);
  }
  // 联盟出的直播广告，兜底快手/极速版的下载链接
  if (ad_result.ad_deliver_info().ad_base_info().item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE ||
      ad_result.ad_deliver_info().ad_base_info().item_type() ==
          kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE) {
    ad_conversion_info->set_app_download_url(url);
  }

  // sdk 会优先打开 deeplink url, 为空再根据 下载类 非下载类广告打开下载 url 或者 h5 url
  switch (ad_info->ad_base_info().ad_operation_type()) {
    case ::kuaishou::ad::AdConversionType::APP_DOWNLOAD:
      // 替换 ios 下载链接 为解决客户端 bug 添加的逻辑
      if (session_data_->get_ud_client_id() == IPHONE) {
        ad_conversion_info->set_app_download_url(ReplaceIosAppDownloadUrl(url));
      } else {
        ad_conversion_info->set_app_download_url(url);
        // 极速版激励视频，有 h5 中间页时填充 h5 中间页
        if (ad_result.ad_deliver_info().universe_ad_deliver_info().page_id() ==
                ks::ad_base::AdPageId::kNebulaInspire &&
            ad_result.ad_deliver_info().universe_ad_deliver_info().sub_page_id() ==
                ks::ad_base::AdSubPageId::kNebulaInspireScene) {
          if (!h5_url.empty()) {
            ad_conversion_info->set_h5_url(h5_url);
          }
        }
      }
      // 广告主自己上传下载详情页
      if (ad_data_v2.has_h5_url() && !forbidden_uni_feed_upload_url) {
        ad_conversion_info->set_h5_type(AdConversionInfo::APP_DOWNLOAD_UPLOAD);
        ad_conversion_info->set_h5_url(ad_data_v2.h5_url());
      } else {
        // 广告主没有上传的下载详情页
        // h5_url : API 填空串， SDK 填前端渲染的拼接详情页
        //          内容 SDK 支持单独设计的拼接落地页和兜底落地页
        // h5_type : API 服务没有向媒体披露填 UNKNOWN ， SDK 区分是否是有抓取
        if (!is_sdk) {
          ad_conversion_info->set_h5_url("");
          ad_conversion_info->set_h5_type(AdConversionInfo::UNKNOWN);
          break;
        }
        ad_conversion_info->set_h5_url(*FrontKconfUtil::h5Url());
        if (ad_data_v2.has_app_detail_info() &&
            ad_data_v2.app_detail_info().cdn_screen_short_urls_size() > 0) {  //  抓取成功
          ad_conversion_info->set_h5_type(AdConversionInfo::APP_DOWNLOAD_BUILD);
          if (is_universe_feed) {
            ad_conversion_info->set_h5_url(*FrontKconfUtil::universeDefaultBuildH5Url());
            falcon::Inc("front_server.set_uni_app_detail_info_succ", 1);
          }
          falcon::Inc("front_server.set_app_detail_info_succ", 1);
        } else {  //  抓取失败
          ad_conversion_info->set_h5_type(AdConversionInfo::UNKNOWN);
          if (is_universe_feed) {
            ad_conversion_info->set_h5_url(*FrontKconfUtil::universeDefaultH5Url());
            falcon::Inc("front_server.set_uni_app_detail_info_fail", 1);
          }
          falcon::Inc("front_server.set_app_detail_info_fail", 1);
        }
      }
      break;
    case ::kuaishou::ad::AdConversionType::OPEN_HTML5:
      ad_conversion_info->set_h5_url(url);
      ad_conversion_info->set_h5_type(AdConversionInfo::NON_APP_DOWNLOAD);
      break;
    default:
      break;
  }
  // 下载兜底逻辑修复
  if ((campaign_type == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
      campaign_type == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
      campaign_type == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)
      && ocpc_action_type != kuaishou::ad::CID_ROAS
      && ocpc_action_type != kuaishou::ad::CID_EVENT_ORDER_PAID) {
    if (session_data_->get_is_ios_platform()) {
      ad_conversion_info->set_app_download_url(*FrontKconfUtil::kuaishouIosAppDownloadUrl());
    } else if (session_data_->get_ad_request()->ad_user_info().platform() == "android") {
      ad_conversion_info->set_app_download_url(*FrontKconfUtil::kuaishouAppDownloadUrl());
    } else if (session_data_->get_ad_request()->ad_user_info().platform() == "harmony") {
      ad_conversion_info->set_app_download_url(*FrontKconfUtil::kuaishouHarmonyAppDownloadUrl());
    }
  }

  // 试玩广告配置, 一期从 kconf 配置读取
  const auto& universePlayableConfig = FrontKconfUtil::universePlayableConfig();
  const auto& unit_config_map = universePlayableConfig->data().unit_config_map();
  const int64_t unit_id = ad_result.ad_deliver_info().ad_base_info().unit_id();
  auto iter = unit_config_map.find(unit_id);
  const int64_t creative_id = ad_result.ad_deliver_info().ad_base_info().creative_id();
  if (iter != unit_config_map.end()) {
    ad_conversion_info->set_playable_url(iter->second.playable_url());
    ad_conversion_info->set_playable_style_info(iter->second.playable_style_info());
  }

  // 联盟 playable 广告放开明投流量限制，即如果改广告位 playable 广告
  // 但是本次流量为非 playable 流量，改广告依然可以投放，但是要去掉 playable 元素
  const auto& unit_support_info = style_info.unit_support_info();
  if (session_data_->IsUnionPlayableExpand()) {
    // 试玩广告填充
    if (unit_support_info.playable_switch() == 2) {
      ad_conversion_info->set_playable_url(unit_support_info.playable_url());
      ad_conversion_info->set_playable_orientation(unit_support_info.playable_orientation());
    }
    const auto& play_info = style_info.play_info();
    if ((play_info.upload_source() == 2 || (play_info.upload_source() == 3)) && play_info.play_url() != "") {
      ad_conversion_info->set_playable_url(play_info.play_url());
      ad_conversion_info->set_playable_orientation(play_info.play_orientation());
    }
  }
  if (!ad_conversion_info->playable_url().empty()) {
    std::string replace_url;
    base::FastStringReplace(ad_conversion_info->playable_url(), "__PLAYABLE_TYPE__", "union", true,
                            &replace_url);
    if (replace_url != ad_conversion_info->playable_url()) {
      ad_conversion_info->set_playable_url(replace_url);
    } else {
      std::string::size_type pos = ad_conversion_info->playable_url().find('?');
      if (pos != std::string::npos) {
        std::string temp{ad_conversion_info->playable_url()};
        ad_conversion_info->set_playable_url(temp.insert(pos + 1, "type=union&"));
      } else {
        ad_conversion_info->set_playable_url(ad_conversion_info->playable_url() + "?type=union");
      }
    }
  }
  // 短视频推广命中小程序链路时填充信息
  bool check_merchant_reco = campaign_type == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE;
  if (session_data_->borrow_media_wx_small_app_id() != "" && check_merchant_reco) {
    auto* small_app_jump_info = ad_conversion_info->mutable_small_app_jump_info();
    const auto& jump_small_app_info =
        engine_base::AdKconfUtil::universeInnerLoopJumpSmallAppConfig()->data().jump_small_app_info();
    // 跳转小程序相关字段填充
    std::string orig_exp_tag =
        GetServerExpTagv2(style_info, session_data_->mutable_ad_request(),
                          session_data_->get_front_server_request()->type(), session_data_->get_llsid());
    switch (style_info.unit().ocpx_action_type()) {
      case kuaishou::ad::EVENT_ORDER_PAIED:
      case kuaishou::ad::AD_MERCHANT_ROAS: {
        const std::string& item_page_url =
            absl::Substitute("$0$1&serverExpTag=$2&visitorId=$3", jump_small_app_info.pay_path_prefix(),
                             style_info.unit_small_shop_merchant_support_info().item_id(), orig_exp_tag,
                             session_data_->get_user_id());
        small_app_jump_info->set_small_app_jump_url(item_page_url);
      } break;
      case kuaishou::ad::AD_MERCHANT_FOLLOW:
      case kuaishou::ad::AD_MERCHANT_FOLLOW_FAST: {
        const std::string& profile_page_url = absl::Substitute(
            "$0uid=$1&followDrawer=1&serverExpTag=$2&visitorId=$3", jump_small_app_info.follow_path_prefix(),
            style_info.account().user_id(), orig_exp_tag, session_data_->get_user_id());
        small_app_jump_info->set_small_app_jump_url(profile_page_url);
      } break;
      default:
        break;
    }
    session_data_->dot_perf->Count(1, "universe_inner_jump_wx",
                                   absl::StrCat(style_info.unit().ocpx_action_type()));

    small_app_jump_info->set_origin_id(jump_small_app_info.origin_id());
    small_app_jump_info->set_media_small_app_id(session_data_->copy_media_wx_small_app_id());
  } else if (!session_data_->borrow_media_wx_small_app_id().empty() &&
             style_info.unit().web_uri_type() == kuaishou::ad::AdEnum_UnitWebUriType_WX_PAGE) {
    auto* small_app_jump_info = ad_conversion_info->mutable_small_app_jump_info();
    small_app_jump_info->set_small_app_jump_url(style_info.unit().schema_uri());
    small_app_jump_info->set_origin_id(style_info.unit_support_info().schema_id());
    small_app_jump_info->set_media_small_app_id(session_data_->copy_media_wx_small_app_id());
  }

  // 建站小游戏组建下发媒体微信小程序 id
  if (style_info.has_magic_site_page()) {
    session_data_->set_media_wx_small_app_id(session_data_->copy_ud_wechat_app_id());
    Json json(StringToJson(style_info.magic_site_page().page_component()));
    if (!json.IsArray()) {
      LOG_EVERY_N(ERROR, 100) << "json format error, json not object";
    } else {
      for (auto *const item : json.array()) {
        int32_t id = item->GetInt("type", -1);
        int32_t sub_id = item->GetInt("subType", -1);
        if (id == 34 && sub_id == 0 && !session_data_->borrow_media_wx_small_app_id().empty()) {
          auto* small_app_jump_info = ad_conversion_info->mutable_small_app_jump_info();
          small_app_jump_info->set_media_small_app_id(session_data_->copy_media_wx_small_app_id());
        }
      }
    }
    ad_conversion_info->set_web_uri_source_type(style_info.unit().web_uri_type());
  }
  // 由广告主指定的直跳小游戏
  bool is_origin_small_game_direct_jump =
      IsSmallGameComponent(style_info.magic_site_page().page_component()) &&
      (style_info.magic_site_page().direct_call_type() == 1);
  // 是否命中直跳创意 广告主指定直跳 or 暗切直跳
  if ((SPDM_enable_small_app_direct_jump(session_data_->get_spdm_ctx()) &&
       session_data_->get_force_direct_call_cids().count(creative_id))) {
    std::string deep_link_url = style_info.magic_site_page().deep_link_url();
    std::string deep_link_url_decode;
    // decode 获取 path 和 original_id
    if (base::DecodeUrlComponent(deep_link_url.c_str(), &deep_link_url_decode)) {
      std::vector<absl::string_view> str_vec = absl::StrSplit(deep_link_url_decode, "params=");
      if (str_vec.size() == 2) {
        base::Json deep_link_info(base::StringToJson(std::string(str_vec[1])));
        if (deep_link_info.IsObject()) {
          std::string original_id = deep_link_info.GetString("originalID", "");
          std::string path = deep_link_info.GetString("path", "");
          if (!original_id.empty() && !path.empty()) {
            std::string direct_jump_source = is_origin_small_game_direct_jump ? "origin" : "force_switch";
            session_data_->dot_perf->Count(1, "small_app_direct_jump_num", direct_jump_source);
            auto* small_app_jump_info = ad_conversion_info->mutable_small_app_jump_info();
            small_app_jump_info->set_origin_id(original_id);
            small_app_jump_info->set_small_app_jump_url(path);
            ad_conversion_info->clear_web_uri_source_type();
            ad_conversion_info->clear_h5_url();
          }
        }
      }
    }
  }
  // 直播涨粉自动预约标志
  bool enable_universe_auto_follow_for_live_ad_for_ad_live_audience_quality =
      (campaign_type == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
       campaign_type == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) &&
      style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY;
  bool enable_fast_follow_auto_follow =
      style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST;
  if ((style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
       enable_fast_follow_auto_follow) &&
      campaign_type == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
    ad_conversion_info->set_auto_follow_for_live_ad(true);
  } else if (enable_universe_auto_follow_for_live_ad_for_ad_live_audience_quality) {
    ad_conversion_info->set_auto_follow_for_live_ad(true);
  }
  // 快聊私信链路支持
  if (campaign_type == kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION) {
    // api 通过 author_id 拿到对应 h5 链接，此时引擎侧拿不到 h5，通过 h5_type 标识走私信链路的广告
    ad_conversion_info->set_h5_type(AdConversionInfo::SIXIN);
  }
  if (campaign_type == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE &&
      IsSiXinServicePageComponent(style_info.magic_site_page().page_component())) {
    // 对于收集销售线索类，咨询页组件的广告填写 das 传的私信 h5_url
    std::string magic_site_page_h5_url;
    base::FastStringReplace(style_info.magic_site_page().h5_url(), kLandingPageId,
                            absl::StrCat(style_info.magic_site_page().id()), true, &magic_site_page_h5_url);
    ad_conversion_info->set_h5_url(magic_site_page_h5_url);
    ad_conversion_info->set_h5_type(AdConversionInfo::SIXIN);
  }

  if (campaign_type == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) {
    auto* deeplink_url = ad_conversion_info->mutable_deeplink_url();
    static const char* from_str = "from=dsp_union";
    static const auto from_str_len = strlen(from_str);
    if (auto param_begin = deeplink_url->find("from="); param_begin != std::string::npos) {
      const auto param_end = deeplink_url->find('&', param_begin);
      if (param_end != std::string::npos) {
        deeplink_url->replace(param_begin, param_end - param_begin, from_str, from_str_len);
      }
    } else {
      if (deeplink_url->find("?") == std::string::npos) {
        deeplink_url->append("?");
      } else {
        deeplink_url->append("&");
      }
      deeplink_url->append(from_str);
    }
    {
      nlohmann::json json_obj;
      json_obj["app_id"] = session_data_->get_pos_manager().GetRequestAppId();
      // pos_manager 的 pos_id 在 StylePostProc 里会被改掉，这里不能用
      json_obj["pos_id"] = session_data_->get_ud_pos_id();
      json_obj["platform"] = session_data_->get_ad_request()->ad_user_info().platform();
      deeplink_url->append("&ext=");
      std::string json_str = json_obj.dump();
      deeplink_url->append(base::EncodeUrlComponent(json_str.data()));
    }
  }
}

void UniverseDataPostProc::FillAdTrackInfo(const AdResult& ad_result, AdInfo* ad_info) {
  // TODO(CBB) fill track operation type
  std::map<kuaishou::ad::AdActionType, std::vector<std::string>> track_map;
  for (const auto& item : ad_result.ad_deliver_info().track()) {
    track_map[item.type()].emplace_back(item.url());
  }
  for (const auto& item : track_map) {
    auto track_info = ad_info->add_ad_track_info();
    track_info->set_type(item.first);
    for (const auto& url : item.second) { track_info->add_url(url); }
  }
}

void UniverseDataPostProc::FillAdLog(const AdResult& ad_result, AdInfo* ad_info) {
  ad_info->mutable_ad_log()->CopyFrom(ad_result.ad_log());
}

void UniverseDataPostProc::FillAppComplianceInfo(const AdResult& ad_result,
                                                 const ImpAdGridInfo&  /*imp_ad_grid_inf*/, AdInfo* ad_info,
                                                 const AthenaAdRequest&  /*athena_req*/,
                                                 const StyleInfoItem& style_info) {
  if (ad_info == nullptr) {
    return;
  }

  if (ad_result.ad_source_type() == kuaishou::ad::ADX) {
    if (ad_result.has_ad_deliver_info() && ad_result.ad_deliver_info().has_app_compliance_info()) {
      ad_info->mutable_app_compliance_info()->set_app_version(
          ad_result.ad_deliver_info().app_compliance_info().app_version());
      ad_info->mutable_app_compliance_info()->set_developer_name(
          ad_result.ad_deliver_info().app_compliance_info().developer_name());
      ad_info->mutable_app_compliance_info()->set_app_privacy_url(
          ad_result.ad_deliver_info().app_compliance_info().app_privacy_url());
      ad_info->mutable_app_compliance_info()->set_app_permission(
          ad_result.ad_deliver_info().app_compliance_info().app_permission());
      ad_info->mutable_app_compliance_info()->set_package_size(
          ad_result.ad_deliver_info().app_compliance_info().package_size());
      // dsp > kconf
      std::string function_introduction =
          ad_result.ad_deliver_info().app_compliance_info().function_introduction();
      if (!function_introduction.empty()) {
        ad_info->mutable_app_compliance_info()->set_function_introduction(function_introduction);
      } else if (!ad_result.ad_deliver_info().ad_base_info().package_name().empty()) {
        ad_info->mutable_app_compliance_info()->set_function_introduction(
            engine_base::AdKconfUtil::universeAppIntroductionConfig()->data().GetIntroduction(
                ad_result.ad_deliver_info().ad_base_info().package_name()));
      }
    }
  } else {
    if (style_info.has_app_release() && style_info.app_release().app_id() != 0 && style_info.has_account() &&
        style_info.account().id() != 0) {
      ad_info->mutable_app_compliance_info()->set_app_version(style_info.app_release().real_app_version());
      ad_info->mutable_app_compliance_info()->set_developer_name(style_info.account().corporation_name());
      ad_info->mutable_app_compliance_info()->set_app_privacy_url(style_info.app_release().app_privacy_url());
      ad_info->mutable_app_compliance_info()->set_app_permission(
          style_info.app_release().permission_information());
      ad_info->mutable_app_compliance_info()->set_package_size(style_info.app_release().package_size());
      // dsp > kconf
      if (!style_info.app_release().function_introduction().empty()) {
        ad_info->mutable_app_compliance_info()->set_function_introduction(
            style_info.app_release().function_introduction());
      } else if (!style_info.app_release().package_name().empty()) {
        ad_info->mutable_app_compliance_info()->set_function_introduction(
            engine_base::AdKconfUtil::universeAppIntroductionConfig()->data().GetIntroduction(
                style_info.app_release().package_name()));
      }
      ad_info->mutable_app_compliance_info()->set_record_number(style_info.app_release().record_number());
    }
  }
}

void UniverseDataPostProc::FillAdStyleInfo(const AdResult& ad_result, const ImpAdGridInfo& imp_ad_grid_inf,
                                           AdInfo* ad_info, const AthenaAdRequest&  /*athena_req*/,
                                           const StyleInfoItem&  /*style_info*/) {
  if (ad_info == nullptr) {
    return;
  }
  // 仅 SDK 可以解析 ad_style_info
  if (imp_ad_grid_inf.cooperation_mode() != 1 && imp_ad_grid_inf.cooperation_mode() != 4 &&
      imp_ad_grid_inf.cooperation_mode() != 6) {
    return;
  }

  auto universe_dynamic_adstyle_map = FrontKconfUtil::universeDynamicAdStyle();

  int32_t ad_style = imp_ad_grid_inf.ad_style();

  if (ad_result.ad_deliver_info().ad_base_info().material_info().material_feature_size() == 0) {
    TLOG_EVERY_N(WARNING, 10000) << "material_size is null";
    return;
  }

  const Size& material_size =
      ad_result.ad_deliver_info().ad_base_info().material_info().material_feature(0).material_size();

  std::string version_key = "1";
  const auto& universe_ad_request_info = session_data_->get_ad_request()->universe_ad_request_info();
  bool is_sdk = universe_ad_request_info.cooperation_mode() == 1;
  if (is_sdk && universe_ad_request_info.has_sdk_version()) {
    const std::string& sdk_version = universe_ad_request_info.sdk_version();
    if (universe_ad_request_info.sdk_type() == 2) {
      // 内容 sdk 3.3.19 及以上版本支持线索通组件化表单
      if (engine_base::CompareAppVersion(sdk_version, "3.3.19") >= 0) {
        version_key = "2";
      }
    }
  }

  int32_t mock_ad_style = ad_style;
  if (ad_style == 23 && ad_result.ad_deliver_info().ad_base_info().ad_rollout_size() == 1) {
    mock_ad_style = 3;
  } else if (ad_style == 23 && ad_result.ad_deliver_info().ad_base_info().ad_rollout_size() == 2) {
    mock_ad_style = 13;
  }
  std::string universe_dynamic_adstyle_key = absl::Substitute(
      "universe_dynamic_adstyle_$0_$1_$2_$3_$4", mock_ad_style,
      ad_result.ad_deliver_info().ad_base_info().campaign_type(),
      material_size.width() > material_size.height() ? "horiz" : "vertical", version_key, "default");

  if (universe_dynamic_adstyle_map->count(universe_dynamic_adstyle_key) <= 0) {
    universe_dynamic_adstyle_key =
        absl::Substitute("universe_dynamic_adstyle_$0_$1_$2_1_default", mock_ad_style,
                         ad_result.ad_deliver_info().ad_base_info().campaign_type(),
                         material_size.width() > material_size.height() ? "horiz" : "vertical");
  }

  if (universe_dynamic_adstyle_map->count(universe_dynamic_adstyle_key) <= 0) {
    ad_info->set_ad_style_info(*FrontKconfUtil::defaultUniverseDynamicAdStyle());
  } else {
    ad_info->set_ad_style_info(universe_dynamic_adstyle_map->at(universe_dynamic_adstyle_key));
  }

  // 激励视频激活目标 APP 任务类型样式覆盖
  if (ad_info->ad_base_info().task_type() == 2 &&
      !(*FrontKconfUtil::inspireAppUniverseDynamicAdStyle()).empty()) {
    ad_info->set_ad_style_info(*FrontKconfUtil::inspireAppUniverseDynamicAdStyle());
  }
  // 激励视频激活目标 非下载类 任务样式类型覆盖
  if (ad_info->ad_base_info().task_type() == 3 &&
      !(*FrontKconfUtil::inspireNoAppUniverseDynamicAdStyle()).empty()) {
    ad_info->set_ad_style_info(*FrontKconfUtil::inspireNoAppUniverseDynamicAdStyle());
  }

  bool sdk_flag = universe_ad_request_info.cooperation_mode() == 1 ||
                  universe_ad_request_info.cooperation_mode() == 4 ||
                  universe_ad_request_info.cooperation_mode() == 6;
  if (sdk_flag && universe_ad_request_info.has_sdk_version()) {
    const std::string& sdk_version = universe_ad_request_info.sdk_version();

    bool is_sdk_allow = (universe_ad_request_info.sdk_type() == 1 &&
                         engine_base::CompareAppVersion(sdk_version, "3.3.18") >= 0) ||
                        (universe_ad_request_info.sdk_type() == 2 &&
                         engine_base::CompareAppVersion(sdk_version, "3.3.25") >= 0);
    // 直电广告样式覆盖
    if (is_sdk_allow && ad_info->ad_base_info().first_industry_id() == 1022 &&
        (ad_style == 2 || ad_style == 3 ||
         (ad_style == 23 && ad_result.ad_deliver_info().ad_base_info().ad_rollout_size() == 1)) &&
        !(*FrontKconfUtil::inspireJinniuUniverseDynamicAdStyle()).empty()) {
      ad_info->set_ad_style_info(*FrontKconfUtil::inspireJinniuUniverseDynamicAdStyle());
    }
  }
}

void UniverseDataPostProc::FillPlayDetailInfo(const ImpAdGridInfo& imp_ad_grid_inf, const AdResult& ad_result,
                                              AdInfo* ad_info) {
  if (ad_info == nullptr) {
    return;
  }
  // 激励/全屏视频填充
  const auto& ad_style = imp_ad_grid_inf.ad_style();
  if (ad_style != 2 && ad_style != 12 && ad_style != 3 &&
      (ad_style != 23 || ad_result.ad_deliver_info().ad_base_info().ad_rollout_size() != 1)) {
    return;
  }
  // 仅 SDK 可以解析
  auto cooperation_mode = imp_ad_grid_inf.cooperation_mode();
  if (cooperation_mode != 1 && cooperation_mode != 4 && cooperation_mode != 6 && cooperation_mode != 8) {
    return;
  }
  if (session_data_->get_ud_play_card_compatible()) {
    ad_info->set_play_detail_info(session_data_->copy_ud_play_card_content());
  } else {
    auto universe_play_card_new_style_map = FrontKconfUtil::universePlayCardNewStyle();
    std::string play_card_new_style = session_data_->copy_ud_play_card_new_style();
    if (universe_play_card_new_style_map->count(play_card_new_style) != 0) {
      ad_info->set_play_detail_info(universe_play_card_new_style_map->at(play_card_new_style));
    }
  }
}

void UniverseDataPostProc::FillSspMediemInfo(int64_t pos_id, AdInfo* ad_info) {
  const AdUniversePosition& universe_position =
      UniversePosResourceV2::GetInstance()->GetAdUniversePosition(pos_id);
  if (universe_position.position_id() == 0) {
    TLOG_EVERY_N(WARNING, 10000) << "pos_id error:" << pos_id;
    ad_info->mutable_ssp_mediem_info()->set_skip_second(0);
    ad_info->mutable_ssp_mediem_info()->set_mute(1);
    return;
  }
  ad_info->mutable_ssp_mediem_info()->set_skip_second(universe_position.skip_ad_mode());
  ad_info->mutable_ssp_mediem_info()->set_mute(universe_position.voice());
}

}  // namespace ks::front_server
