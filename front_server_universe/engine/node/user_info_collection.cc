#include "teams/ad/front_server_universe/engine/node/user_info_collection.h"

#include <sys/types.h>

#include <algorithm>
#include <cstdlib>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "absl/strings/str_cat.h"
#include "absl/strings/substitute.h"
#include "base/encoding/base64.h"
#include "glog/logging.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/eds.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/session_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/universe/universe_feature_freq.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/universe_delivered_qpon_info.pb.h"
#include "teams/ad/ad_proto/kuaishou/newsmodel/user_profile.pb.h"
#include "teams/ad/bid_server/bid_server_universe/proto/bid_message.pb.h"
#include "teams/ad/bid_server/bid_universe_adx/kconf/kconf.h"
#include "teams/ad/bid_server/bid_universe_adx/proto/bid_message.pb.h"
#include "teams/ad/engine_base/frequency_capping/session_res.h"
#include "teams/ad/front_server_universe/engine/context_data/utility.h"
#include "teams/ad/front_server_universe/util/ad_user_info/ad_user_info_builder.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/utility/async_redis_helper.h"
#include "teams/ad/front_server_universe/util/utility/ksn_util.h"
#include "teams/ad/front_server_universe/util/utility/strategy_util.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"

namespace ks::front_server {

bool UserInfoCollection::NodeInitialize() {
  session_data_ = context_->GetMutableContextData<ContextData>();
  int32_t ad_style = session_data_->get_ud_ad_style();
  auto* p_strategy_manager = context_->GetContextSystem<StrategyManager>();
  if (session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() > 0) {
    render_type_ = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).render_type();
  }
  self_render_style_ = ad_style == 1 || ad_style == 13;

  return nullptr != session_data_ && session_data_->get_pass_pre_filter() &&
           nullptr != p_strategy_manager;
}

bool UserInfoCollection::Skip() {
  const auto& perf_conf = engine_base::AdKconfUtil::universeTinyPerfConf()->data();
  if (session_data_->get_ud_is_universe_tiny_flow() &&
      perf_conf.Contains("skip_user_info_collection", session_data_->get_ud_pos_id())) {
    universe_rtb_cpm_bid_coff_params_.Init(session_data_);
    async_redis_helper_.AsyncGet(&universe_rtb_cpm_bid_coff_params_);
    if (session_data_->get_is_universe_rtb_flow()) {
      FillUniverseRtbBidCoffInfo();
    }
    return true;
  }

  return false;
}

// 要整理好, ad_user_info 中的哪些字段是在 context_data.cc 中填充好的
// context_data.cc 中, 有哪些数据依赖画像服务, 要挪到这里来
bool UserInfoCollection::ProcessInner() {
  TimeRecorder timer(
      context_->GetMutableContextData<ContextData>(),
      static_cast<int32_t>(kuaishou::ad::AdFrontNodeType::FRONT_NODE_USER_INFO_COLLECTION_TYPE));
  if (!NodeInitialize()) {
    return false;
  }
  if (Skip()) {
    return true;
  }

  kuaishou::ad::AdSessionRequest session_req;
  kuaishou::ad::AdSessionResponse session_resp;
  ks::engine_base::FreqRequestContext rpc_ctx;
  rpc_ctx.ad_request_type = ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request()));
  rpc_ctx.request_app_id = session_data_->get_ad_request()->universe_ad_request_info().app_id();
  if (session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() != 0) {
    for (int i = session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() - 1; i >= 0;
         --i) {
      const auto& imp = session_data_->get_ad_request()->universe_ad_request_info().imp_info(i);
      if (imp.ad_num() == 0) {
        continue;
      }
      session_data_->mutable_request_inventory_pos_id()->push_back(imp.position_id());
    }
  }
  rpc_ctx.request_inventory_pos_id = session_data_->mutable_request_inventory_pos_id();
  rpc_ctx.page_id = session_data_->get_page_id();
  rpc_ctx.sub_page_id = session_data_->get_sub_page_id();
  rpc_ctx.merge_negative_session = false;
  rpc_ctx.enable_short_play_action = false;
  const std::string exp_param = SPDM_freq_strategy_exp_tag(session_data_->get_spdm_ctx());
  bool is_session_seven_day_exp = (exp_param == "photo_7day");
  session_req.set_is_session_seven_day_exp(is_session_seven_day_exp);
  engine_base::SessionRes session_res(&rpc_ctx, &session_req, &session_resp);
  // 异步 redis 调用
  AsyncRedisInit();

  if (NeedRequestSessionSvr()) {
    // 异步调 session
    session_res.StartAdSession(*(session_data_->get_ad_request()), false);
  }
  session_data_->mutable_ad_request()->set_front_request_ts(base::GetTimestamp());

  // 用户信任分
  FillAdCredictUserScoreValue();

  FillContextCommonData();

  // 获取用户已转化过的信息流广告 unit
  GetConvertedUnitAsyncV2();

  SetRequestDebugInfo();

  FillIdsAndShieldLive();

  if (session_data_->get_is_universe_inner_loop_admit_traffic()) {
    FillUniverseInnerLoopUserTagInfo();
  }

  if (session_data_->get_ud_ad_style() == 2) {
    FillUniverseClickRewardFreqInfo();
  }
  // 样式聚合轮播 优先级低于 内循环聚合页
  if (session_data_->get_ud_carousel_pv_admit() &&
      !session_data_->get_ud_is_universe_aggregation_scene()) {
    FillUniverseCarouselFreqInfo();
  }

  if (session_data_->get_ud_ad_style() == 4 || session_data_->get_ud_ad_style() == 2 ||
       session_data_->get_ud_ad_style() == 3 || session_data_->get_ud_ad_style() == 13 ||
       session_data_->get_ud_ad_style() == 23 || session_data_->get_ud_ad_style() == 1 ||
       session_data_->get_ud_ad_style() == 6) {
    FillUniverseInteractiveStyleFreqInfo();
  }

  if (session_data_->get_ud_ad_style() == 1 && render_type_ == 2) {
    FillUniverseFeatureIntervalInfo();
  }
  FillAdBrowseInfo();
  if (SPDM_enableUniversePassNewFreqMap()) {
    FillAdBrowseInfoSimple();
  }

  FillUniverseDeliveredQponInfo();

  FillUniverseFirePokeInfo();
  if (SPDM_enable_universe_llm_u2p_v2(session_data_->get_spdm_ctx())) {
    FillUniverseLlmU2pV2Info();
  }
  if (SPDM_enable_quality_model_default_strategy(session_data_->get_spdm_ctx())
      || SPDM_enable_fix_new_media_strategy(session_data_->get_spdm_ctx())
      || SPDM_enable_fix_new_media_strategy_v2(session_data_->get_spdm_ctx())) {
    FillUniverseQualityModelInfo();
  }
  if (SPDM_enable_universe_ecpm_win_explore(session_data_->get_spdm_ctx())) {
    FillUniverseEcpmWinExploreInfo();
  }
  if (SPDM_enable_universe_fill_active_w_level(session_data_->get_spdm_ctx())) {
    FillUniverseActiveWLevelInfo();
  }
  if (session_data_->get_is_universe_rtb_flow()) {
    FillUniverseRtbBidCoffInfo();
  }

  if (session_data_->get_is_universe_inner_loop_admit_traffic()) {
    FillUniverseReservationInfo();
  }

  if (SPDM_enable_universe_inner_u2x_redis_fill(session_data_->get_spdm_ctx())) {
    FillUniverseInnerU2xInfo();
  }

  FillStrategyCrowdInfo();
  FillAbtestMappingId();

  FillHonorDevice();
  FillHuaweiDevice();
  FillUniverseTinyQueryRecalledProductIds();
  FillUniverseComponentizedStyleFreq();

  // 一定要放在最后!
  async_redis_helper_.WaitAll();
  session_data_->mutable_ad_request()->mutable_ad_session_response_pack()->set_local(true);
  if ((NeedRequestSessionSvr() && session_res.Wait())) {
    session_data_->mutable_ad_request()->mutable_ad_session_response_pack()->mutable_response()->Swap(
        &session_resp);
    if (SPDM_enable_merge_fanstop_session_info(session_data_->get_spdm_ctx())) {
      // 信息流曝光频控合并粉条曝光频控
      MergeFanstopSessionInfoToResponse();
    }
  }

  return true;
}

void UserInfoCollection::FillAdBrowseInfo() {
  std::vector<std::string> value;
  auto ret = async_redis_helper_.Wait(&ad_browsed_info_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    auto* ad_user_info = session_data_->mutable_ad_request()->mutable_ad_user_info();
    for (const auto& item : value) {
      kuaishou::ad::AdBrowsedInfo browsed_ad_info;
      if (browsed_ad_info.ParseFromString(item)) {
        ad_user_info->add_ad_browsed_info()->Swap(&browsed_ad_info);
      } else {
        TLOG(WARNING) << "invalid seralize string: " << item;
      }
    }
    session_data_->dot_perf->Count(1, "user_info_collection", "ad_browsed_info");
  }
}

void UserInfoCollection::FillAdBrowseInfoSimple() {
  std::vector<std::string> value;
  auto& universe_browsed_freq_config_ids = session_data_->get_ud_universe_browsed_freq_config_ids();
  auto ret = async_redis_helper_.Wait(&ad_browsed_info_params_simple_, &value);
  auto multi_key_size = universe_browsed_freq_config_ids.size();
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && value.size() == multi_key_size) {
    auto* universe_browsed_freq_map =
        session_data_->mutable_ad_request()->mutable_universe_browsed_freq_map();
    for (int32_t i = 0; i < multi_key_size; i++) {
      auto timestamp_second = atoll(value[i].c_str()) / 1000 / 1000;
      if (timestamp_second <= 0) {
        continue;
      }
      universe_browsed_freq_map->insert({universe_browsed_freq_config_ids.at(i), timestamp_second});
    }
    session_data_->dot_perf->Count(1, "user_info_collection", "ad_browsed_info_simple");
  }
}

void UserInfoCollection::MergeFanstopSessionInfoToResponse() {
  if (session_data_ == nullptr) {
    return;
  }
  auto* default_session_info = session_data_->mutable_ad_request()
                                   ->mutable_ad_session_response_pack()
                                   ->mutable_response()
                                   ->mutable_user_action_info()
                                   ->mutable_ad_action_info();
  for (auto & itr : *default_session_info) {
    auto action_type = itr.action_type();
    if (action_type != kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION) {
      continue;
    }
    auto* ad_detail_info = itr.mutable_ad_detail_info();
    if (fanstop_session_info_.find(action_type) != fanstop_session_info_.end()) {
      auto action_type_name = kuaishou::ad::AdActionType_Name(action_type);
      session_data_->dot_perf->Interval(ad_detail_info->size(), "merge_fanstop_session", "before_size",
                                        action_type_name);
      ad_detail_info->MergeFrom(
          {fanstop_session_info_[action_type].begin(), fanstop_session_info_[action_type].end()});
      session_data_->dot_perf->Interval(ad_detail_info->size(), "merge_fanstop_session", "after_size",
                                        action_type_name);
    }
  }
}
void UserInfoCollection::FillAbtestMappingId() {
  auto* ad_request = session_data_->mutable_ad_request();
  if (!session_data_->get_front_server_request() || !ad_request) {
    return;
  }
  session_data_->dot_perf->Count(
      1, "abtest_mapping_id_perf",
      kuaishou::ad::FrontRequestType_Name(session_data_->get_front_server_request()->type()),
      (!session_data_->get_abtest_mapping_id().mapping_ids().empty() ? "valid" : "invalid"));
  if (!session_data_->get_abtest_mapping_id().mapping_ids().empty()) {
    ad_request->mutable_ad_user_info()->mutable_abtest_mapping_id()->CopyFrom(
        session_data_->get_abtest_mapping_id());
  }
}

bool UserInfoCollection::NeedRequestSessionSvr() const {
  // 非联盟流量都会请求 sessionSvr
  // 联盟的内容广告会请求 sessionSvr 数据，其他的需要额外添加
  static absl::flat_hash_set<int32_t> kUniverseContent = {7, 8, 9, 12};
  return kUniverseContent.contains(session_data_->get_ud_ad_style());
}

void UserInfoCollection::FillUniverseFirePokeInfo() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&universe_fire_poke_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    std::string fire_poke_str;
    kuaishou::ad::FirePoke* fire_poke = session_data_->mutable_ad_request()->mutable_universe_fire_poke();
    if (!(base::Base64Decode(value, &fire_poke_str))) {
      LOG(ERROR) << "FirePoke: decode64 failed!";
      return;
    }
    if (!fire_poke->ParseFromString(fire_poke_str)) {
      LOG(ERROR) << "FirePoke: parse failed!";
      return;
    }
    session_data_->dot_perf->Count(1, "universe_fire_poke_info_count");
  }
}

void UserInfoCollection::FillUniverseLlmU2pInfo() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&universe_llm_u2p_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    if (value.empty()) {
      session_data_->dot_perf->Count(1, "universe_llm_u2p_count", "bad");
      LOG_EVERY_N(ERROR, 10000) << "universe_llm_u2p get value failed!";
      return;
    }
    auto* universe_ad_request_info = session_data_->mutable_ad_request()->mutable_universe_ad_request_info();
    universe_ad_request_info->set_universe_llm_products(value);
    session_data_->dot_perf->Count(1, "universe_llm_u2p_count", "ok");
    return;
  }
}

void UserInfoCollection::FillUniverseLlmU2pV2Info() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&universe_llm_u2p_v2_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    if (value.empty()) {
      session_data_->dot_perf->Count(1, "universe_llm_u2p_v2_count", "bad");
      LOG_EVERY_N(ERROR, 10000) << "universe_llm_u2p_v2 get value failed!";
      return;
    }
    auto* universe_ad_request_info = session_data_->mutable_ad_request()->mutable_universe_ad_request_info();
    universe_ad_request_info->set_universe_llm_products_v2(value);
    session_data_->dot_perf->Count(1, "universe_llm_u2p_v2_count", "ok");
    return;
  }
}

void UserInfoCollection::FillUniverseInnerU2xInfo() {
  if (!universe_inner_u2x_params_.IsAllow()) {
    return;
  }
  // 仅目标人群写入 u2x 数据
  bool is_u2x_target_crowd = false;
  int64_t filter_user_tag = 1ULL << SPDM_universe_inner_u2x_redis_u_level(session_data_->get_spdm_ctx());
  is_u2x_target_crowd = (((session_data_->get_ad_request()->ad_user_info().universe_inner_loop_user_tag()) &
                          filter_user_tag) > 0);
  if (is_u2x_target_crowd) {
    std::string u2x_value;
    auto ret = async_redis_helper_.Wait(&universe_inner_u2x_params_, &u2x_value);
    if (ret != ks::infra::KS_INF_REDIS_NO_ERROR || u2x_value.empty()) {
      return;
    }
    auto* universe_ad_request_info = session_data_->mutable_ad_request()->mutable_universe_ad_request_info();
    universe_ad_request_info->set_universe_inner_u2x_infos(u2x_value);
  }
}

void UserInfoCollection::FillUniverseQualityModelInfo() {
  std::string cpm_pos_value;
  std::string cpm_app_value;
  std::string cpm_industry_value;
  std::string cpm_style_value;
  std::string fill_pos_value;
  std::string fill_app_value;
  std::string fill_industry_value;
  std::string fill_style_value;
  auto cpm_pos_ret = async_redis_helper_.Wait(&universe_cpm_model_pos_params_, &cpm_pos_value);
  auto cpm_app_ret = async_redis_helper_.Wait(&universe_cpm_model_app_params_, &cpm_app_value);
  auto cpm_industry_ret = async_redis_helper_.Wait(&universe_cpm_model_industry_params_, &cpm_industry_value);
  auto cpm_style_ret = async_redis_helper_.Wait(&universe_cpm_model_style_params_, &cpm_style_value);
  auto fill_pos_ret = async_redis_helper_.Wait(&universe_fill_model_pos_params_, &fill_pos_value);
  auto fill_app_ret = async_redis_helper_.Wait(&universe_fill_model_app_params_, &fill_app_value);
  auto fill_industry_ret = async_redis_helper_.Wait(&universe_fill_model_industry_params_,
      &fill_industry_value);
  auto fill_style_ret = async_redis_helper_.Wait(&universe_fill_model_style_params_, &fill_style_value);
  auto* universe_ad_request_info = session_data_->mutable_ad_request()->mutable_universe_ad_request_info();
  if (cpm_pos_ret == ks::infra::KS_INF_REDIS_NO_ERROR && !cpm_pos_value.empty()
      && SPDM_enable_quality_model_default_strategy(session_data_->get_spdm_ctx())) {
    universe_ad_request_info->set_universe_cpm_model_value(cpm_pos_value);
  } else if (cpm_app_ret == ks::infra::KS_INF_REDIS_NO_ERROR && !cpm_app_value.empty()) {
    universe_ad_request_info->set_universe_cpm_model_value(cpm_app_value);
  } else if (cpm_industry_ret == ks::infra::KS_INF_REDIS_NO_ERROR && !cpm_industry_value.empty()) {
    universe_ad_request_info->set_universe_cpm_model_value(cpm_industry_value);
  } else if (cpm_style_ret == ks::infra::KS_INF_REDIS_NO_ERROR && !cpm_style_value.empty()) {
    universe_ad_request_info->set_universe_cpm_model_value(cpm_style_value);
  }
  if (fill_pos_ret == ks::infra::KS_INF_REDIS_NO_ERROR && !fill_pos_value.empty()
      && SPDM_enable_quality_model_default_strategy(session_data_->get_spdm_ctx())) {
    universe_ad_request_info->set_universe_fill_model_value(fill_pos_value);
  } else if (fill_app_ret == ks::infra::KS_INF_REDIS_NO_ERROR && !fill_app_value.empty()) {
    universe_ad_request_info->set_universe_fill_model_value(fill_app_value);
  } else if (fill_industry_ret == ks::infra::KS_INF_REDIS_NO_ERROR && !fill_industry_value.empty()) {
    universe_ad_request_info->set_universe_fill_model_value(fill_industry_value);
  } else if (fill_style_ret == ks::infra::KS_INF_REDIS_NO_ERROR && !fill_style_value.empty()) {
    universe_ad_request_info->set_universe_fill_model_value(fill_style_value);
  }
}

void UserInfoCollection::FillUniverseEcpmWinExploreInfo() {
  std::vector<std::string> values;

  auto ret = async_redis_helper_.Wait(&universe_ecpm_win_explore_params_, &values);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    if (values.empty()) {
      session_data_->dot_perf->Count(1, "universe_ecpm_win_explore_count", "bad");
      LOG_EVERY_N(ERROR, 10000) << "universe_ecpm_win_explore_count get value failed!";
      return;
    }
    for (std::string value : values) {
      std::vector<std::string> tokens = absl::StrSplit(value, ":", absl::SkipEmpty());
      if (tokens.size() != 2) {
        continue;
      }
      try {
        if (std::stoll(tokens[0]) != session_data_->get_ud_ad_style()) {
          continue;
        }
        double wrank_score = std::stod(tokens[1]);
        auto* universe_ad_request_info =
            session_data_->mutable_ad_request()->mutable_universe_ad_request_info();
        universe_ad_request_info->set_universe_ecpm_win_explore_wrank_score(wrank_score);
        session_data_->dot_perf->Count(1, "universe_ecpm_win_explore_count", "ok");
        break;
      } catch (...) { continue; }
    }
    return;
  }
}

void UserInfoCollection::FillUniverseActiveWLevelInfo() {
  std::string value;
  auto ret = async_redis_helper_.Wait(&universe_active_w_level_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    std::vector<std::string> active_w_level;
    base::SplitString(value, ",", &active_w_level);
    if (active_w_level.size() != 2) {
      session_data_->dot_perf->Count(1, "universe_universe_active_w_level_count", "bad");
      LOG_EVERY_N(ERROR, 10000) << "universe_universe_active_w_level get value failed!";
      return;
    }
    // [xiaowentao] value 格式：联盟活跃分层,主站用户价值 w 分层
    // 联盟活跃分层：
    // H 高活跃，7 天活跃 \in [4, 7]
    // L 低活跃，7 天活跃 \in [1, 3]
    // N 数据非法
    // 主站用户价值 w 分层，W5 为高价值，W1 为低价值，W1、W2、W3、W4、W5-、W5+，N 为空数据
    auto active_level = active_w_level[0];
    auto w_level = active_w_level[1];
    auto* universe_ad_request_info = session_data_->mutable_ad_request()->mutable_universe_ad_request_info();
    universe_ad_request_info->set_universe_user_w_level(w_level);
    universe_ad_request_info->set_universe_user_active_level(active_level);
    session_data_->dot_perf->Count(1, "universe_universe_active_w_level_count", "ok");
    return;
  }
}

void UserInfoCollection::FillUniverseRtbBidCoffInfo() {
  auto fill_redis_info = [this](double* cpm_bid_coff) -> bool {
    std::string value;
    auto ret = async_redis_helper_.Wait(&universe_rtb_cpm_bid_coff_params_, &value);
    if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      session_data_->dot_perf->Count(1, "universe_rtb_cpm_bid_coff_count", "ok");
      ::kuaishou::ad::BidUniverseAdx bid_universe_adx;
      if (!bid_universe_adx.ParseFromString(value)) {
        LOG(ERROR) << "BidUniverseAdx: parse failed!";
        return false;
      }
      if (bid_universe_adx.cpm_bid_coff() > 0) {
        *cpm_bid_coff = bid_universe_adx.cpm_bid_coff();
        return true;
      }
    } else {
      session_data_->dot_perf->Count(1, "universe_rtb_cpm_bid_coff_count", "bad", std::to_string(ret));
    }
    return false;
  };
  double cpm_bid_coff = engine_base::AdKconfUtil::universeCpmBidCoff();
  fill_redis_info(&cpm_bid_coff);
  const auto& coff_kconf = FrontKconfUtil::universeRtbCpmCoff()->data();

  if (coff_kconf.global_control() && coff_kconf.global_ratio() > 0 && coff_kconf.global_ratio() < 2) {
    cpm_bid_coff = coff_kconf.global_ratio();
  }
  if (coff_kconf.partial_control()) {
    auto iter = coff_kconf.app_id_2_ratio().find(session_data_->get_pos_manager().GetRequestAppId());
    if (iter != coff_kconf.app_id_2_ratio().end() && iter->second > 0 && iter->second < 2) {
      cpm_bid_coff = iter->second;
    }
  }

  // 固定分成 pos 的系数为 1.0
  if (ks::bid_server::bid_universe_adx::AdKconfUtil::adUniverseShareParamsConfig() != nullptr) {
    const auto& conf = ks::bid_server::bid_universe_adx::AdKconfUtil::adUniverseShareParamsConfig()->data();
    if (conf.IsPosSpecialShare(session_data_->get_ud_pos_id())) {
      LOG_EVERY_N(INFO, 10000) << "bid_universe_adx special pos front: " << session_data_->get_ud_pos_id();
      cpm_bid_coff = 1.0;
      session_data_->dot_perf->Count(1, "skip_special_pos_front");
    }
  }

  session_data_->set_universe_rtb_coff(cpm_bid_coff);

  if (session_data_->mutable_ad_request()->mutable_universe_ad_request_info()->imp_info_size() > 0) {
    auto *imp_info =
        session_data_->mutable_ad_request()->mutable_universe_ad_request_info()->mutable_imp_info(0);
    imp_info->set_rtb_cpm_bid_coff(session_data_->get_universe_rtb_coff());
  }
}

void UserInfoCollection::FillUniverseClickRewardFreqInfo() {
  std::vector<std::string> value;
  kuaishou::ad::AdDetailInfo ad_detail_info;
  int64_t timestamp_of_one_hour_ago = base::GetTimestamp() / 1000 - 3600 * 1000;
  auto* ad_user_info = session_data_->mutable_ad_request()->mutable_ad_user_info();
  auto ret = async_redis_helper_.Wait(&universe_click_reward_freq_params_, &value);

  ad_user_info->set_click_reward_switch(false);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    int32_t cnt_of_one_day = value.size();
    int32_t cnt_of_one_hour = 0;
    if (cnt_of_one_day > SPDM_maxUniverseClickRewardCntOneDay()) {
      ad_user_info->set_click_reward_switch(false);
      return;
    }
    for (const auto& v : value) {
      if (ad_detail_info.ParseFromString(v) && ad_detail_info.timestamp() > timestamp_of_one_hour_ago) {
        cnt_of_one_hour++;
      }
    }
    if (cnt_of_one_hour > SPDM_maxUniverseClickRewardCntOneHour()) {
      ad_user_info->set_click_reward_switch(false);
      return;
    }
    ad_user_info->set_click_reward_switch(true);
  } else if (ret == ks::infra::KS_INF_REDIS_ERR_NONEXIST) {
    ad_user_info->set_click_reward_switch(true);
  }
}

void UserInfoCollection::FillUniverseCarouselFreqInfo() {
  std::vector<std::string> value;
  kuaishou::ad::AdDetailInfo ad_detail_info;
  int64_t now_ts = base::GetTimestamp() / 1000;
  // 一小时前
  int64_t one_hour_ago_ts = now_ts - 3600 * 1000;
  // 上次曝光时间
  int64_t last_ts = 0;
  // 当天零点
  const auto& bd = session_data_->get_bd();
  int64_t today_ts = now_ts - bd.hour * 3600 - bd.minute * 60 - bd.second;
  auto ret = async_redis_helper_.Wait(&universe_carousel_params_, &value);
  bool need_freq_control = false;
  // 频控配置
  int64_t daily_imp_threshold = SPDM_carousel_daily_imp_threshold(session_data_->get_spdm_ctx());
  int64_t hourly_imp_threshold = SPDM_carousel_hourly_imp_threshold(session_data_->get_spdm_ctx());
  int64_t carousel_imp_interval = SPDM_carousel_imp_interval(session_data_->get_spdm_ctx());
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    int32_t cnt_of_one_day = 0;
    int32_t cnt_of_one_hour = 0;
    for (int i = 0; i < value.size(); i++) {
      if (!ad_detail_info.ParseFromString(value[i])) {
        continue;
      }
      if (ad_detail_info.timestamp() > one_hour_ago_ts) {
        cnt_of_one_hour++;
      }
      if (ad_detail_info.timestamp() > today_ts) {
        cnt_of_one_day++;
      }
      if (i == value.size() - 1) {
        last_ts = ad_detail_info.timestamp();
      }
    }
    // 频控过滤
    if (cnt_of_one_day > daily_imp_threshold || cnt_of_one_hour > hourly_imp_threshold ||
        now_ts - last_ts < carousel_imp_interval) {
      need_freq_control = true;
    }
  }
  if (!need_freq_control) {
    session_data_->set_ud_carousel_user_admit(true);
    // 修改广告数 和 request_imp_info 保证返回广告数量
    if (session_data_->mutable_ad_request()->universe_ad_request_info().imp_info_size() > 0) {
      auto* imp_info =
          session_data_->mutable_ad_request()->mutable_universe_ad_request_info()->mutable_imp_info(0);
      session_data_->set_ud_origin_ad_num(imp_info->ad_num());
      imp_info->set_ad_num(SPDM_universe_carousel_ad_num(session_data_->get_spdm_ctx()));
      session_data_->mutable_pos_manager()->RefreshAdRequestTypeAndRequestImpInfos(
          *(session_data_->get_ad_request()));
    }
  }
}

void UserInfoCollection::FillUniverseInteractiveStyleFreqInfo() {
  std::vector<std::string> value;
  kuaishou::ad::AdDetailInfo ad_detail_info;
  int32_t ad_style = session_data_->get_ud_ad_style();
  const auto& self_render_style_freq_config = FrontKconfUtil::selfRenderStyleFreqConfig()->data();
  int64_t pos_id = session_data_->get_ud_pos_id();
  const std::string& app_id = session_data_->get_ad_request()->universe_ad_request_info().app_id();
  int64_t medium_uid = session_data_->get_ad_request()->universe_ad_request_info().medium_uid();

  auto* ad_user_info = session_data_->mutable_ad_request()->mutable_ad_user_info();
  auto ret = async_redis_helper_.Wait(&universe_interactive_style_freq_params_, &value);

  auto* mutable_freq = ad_user_info->mutable_universe_style_freq_data();
  mutable_freq->set_interactive_style_combo(false);
  mutable_freq->set_interactive_style_shake(false);
  mutable_freq->set_interactive_style_rotate(false);
  mutable_freq->set_interactive_style_slide(false);
  mutable_freq->set_interactive_style_combo2(false);
  mutable_freq->set_interactive_style_combo3(false);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    std::array<int32_t, kuaishou::ad::AdEnum::UniverseFreqStyleType_MAX + 1> cnt_of_one_day{0};

    for (const auto& v : value) {
      if (ad_detail_info.ParseFromString(v)) {
        cnt_of_one_day[ad_detail_info.freq_style_type()]++;
      }
    }

    for (int i = 1; i < cnt_of_one_day.size(); i++) {
      // 非自渲染频控
      if (!self_render_style_ || render_type_ == 2) {
        switch (i) {
          case kuaishou::ad::AdEnum::UNIVERSE_FREQ_STYLE_TYPE_COMBO:
            if (cnt_of_one_day[i] < SPDM_splash_combo_frequency_threshold(session_data_->get_spdm_ctx())) {
              mutable_freq->set_interactive_style_combo(true);
            }
            break;
          case kuaishou::ad::AdEnum::UNIVERSE_FREQ_STYLE_TYPE_SHAKE:
            if ((ad_style == 2 &&
                 cnt_of_one_day[i] < SPDM_inspire_shake_frequency_threshold(session_data_->get_spdm_ctx())) ||
                (ad_style == 4 &&
                 cnt_of_one_day[i] < SPDM_splash_shake_frequency_threshold(session_data_->get_spdm_ctx())) ||
                (ad_style == 1 &&
                 cnt_of_one_day[i] < SPDM_feedShakeCountDaily(session_data_->get_spdm_ctx()))) {
              mutable_freq->set_interactive_style_shake(true);
            }
            break;
          case kuaishou::ad::AdEnum::UNIVERSE_FREQ_STYLE_TYPE_ROTATE:
            if ((ad_style == 4 &&
                 cnt_of_one_day[i] < SPDM_splash_rotate_frequency_threshold(session_data_->get_spdm_ctx())) ||
                (ad_style == 2 && cnt_of_one_day[i] < SPDM_inspire_rotate_frequency_threshold(
                                                          session_data_->get_spdm_ctx())) ||
                (ad_style == 3 && cnt_of_one_day[i] < SPDM_fullscreen_rotate_frequency_threshold(
                                                          session_data_->get_spdm_ctx())) ||
                (ad_style == 23 && cnt_of_one_day[i] < SPDM_fullscreen_rotate_frequency_threshold(
                                                           session_data_->get_spdm_ctx())) ||
                (ad_style == 1 && cnt_of_one_day[i] < SPDM_universe_feed_rotate_frequency_threshold(
                                                          session_data_->get_spdm_ctx())) ||
                (ad_style == 6 && cnt_of_one_day[i] < SPDM_universe_draw_rotate_frequency_threshold(
                                                          session_data_->get_spdm_ctx()))) {
              mutable_freq->set_interactive_style_rotate(true);
            }
            break;
          case kuaishou::ad::AdEnum::UNIVERSE_FREQ_STYLE_TYPE_SLIDE:
            if (ad_style == 4 &&
                cnt_of_one_day[i] < SPDM_splash_slide_frequency_threshold(session_data_->get_spdm_ctx())) {
              mutable_freq->set_interactive_style_slide(true);
            }
            break;
          case kuaishou::ad::AdEnum::UNIVERSE_FREQ_STYLE_TYPE_COMBO2:
            if ((ad_style == 4 &&
                 cnt_of_one_day[i] < SPDM_splash_combo2_frequency_threshold(session_data_->get_spdm_ctx())) ||
                (ad_style == 13 && cnt_of_one_day[i] < SPDM_interstitial_combo2_frequency_threshold(
                                                           session_data_->get_spdm_ctx())) ||
                (ad_style == 23 && cnt_of_one_day[i] < SPDM_interstitial_combo2_frequency_threshold(
                                                           session_data_->get_spdm_ctx()))) {
              mutable_freq->set_interactive_style_combo2(true);
            }
            break;
          case kuaishou::ad::AdEnum::UNIVERSE_FREQ_STYLE_TYPE_COMBO3:
            if ((ad_style == 3 && cnt_of_one_day[i] < SPDM_fullscreen_combo3_frequency_threshold(
                                                          session_data_->get_spdm_ctx())) ||
                (ad_style == 23 && cnt_of_one_day[i] < SPDM_fullscreen_combo3_frequency_threshold(
                                                           session_data_->get_spdm_ctx())) ||
                (ad_style == 2 && cnt_of_one_day[i] < SPDM_inspire_combo3_frequency_threshold(
                                                          session_data_->get_spdm_ctx()))) {
              mutable_freq->set_interactive_style_combo3(true);
            }
            break;
          default:
            LOG_EVERY_N(INFO, 10000) << "unknown type:" << i;
            break;
        }
      } else {  // 自渲染频控
        switch (i) {
          case kuaishou::ad::AdEnum::UNIVERSE_FREQ_STYLE_TYPE_ROTATE:
            if (ad_style == 1 && cnt_of_one_day[i] < self_render_style_freq_config.GetRotateFreqThreshold(
                                                         pos_id, app_id, medium_uid)) {
              mutable_freq->set_interactive_style_rotate(true);
            }
            break;
          case kuaishou::ad::AdEnum::UNIVERSE_FREQ_STYLE_TYPE_COMBO2:
            if (ad_style == 13 && cnt_of_one_day[i] < self_render_style_freq_config.GetRotateFreqThreshold(
                                                          pos_id, app_id, medium_uid)) {
              mutable_freq->set_interactive_style_combo2(true);
            }
            break;
          default:
            LOG_EVERY_N(INFO, 10000) << "self render unknown type:" << i;
            break;
        }
      }
    }
  } else if (ret == ks::infra::KS_INF_REDIS_ERR_NONEXIST) {
    mutable_freq->set_interactive_style_combo(true);
    mutable_freq->set_interactive_style_shake(true);
    mutable_freq->set_interactive_style_rotate(true);
    mutable_freq->set_interactive_style_slide(true);
    mutable_freq->set_interactive_style_combo2(true);
    mutable_freq->set_interactive_style_combo3(true);
  }
}

void UserInfoCollection::FillUniverseFeatureIntervalInfo() {
  auto* mutable_freq =
      session_data_->mutable_ad_request()->mutable_ad_user_info()->mutable_universe_style_freq_data();
  std::vector<std::string> value;
  auto ret = async_redis_helper_.Wait(&universe_feature_interval_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    // key 为 fearure_id
    std::vector<int64_t> features;
    for (const auto& v : value) {
      int64_t feature_id = 0;
      if (absl::SimpleAtoi(v, &feature_id) && feature_id > 0) {
        features.emplace_back(feature_id);
      }
    }
    auto CheckUniverseFeatureInterval = [&](int64_t type, int64_t interval_threshold) {
      // key 为 fearure_id
      // 1001 静态，1002 扭动，1003 摇动
      for (int i = 0; i < features.size() && i < interval_threshold; i++) {
        if (features[i] == type) {
          return false;
        }
      }
      return true;
    };
    if (mutable_freq->interactive_style_rotate() &&
        CheckUniverseFeatureInterval(
            1002, SPDM_universe_feed_rotate_interval_threshold(session_data_->get_spdm_ctx()))) {
      mutable_freq->set_interactive_style_rotate(true);
    } else {
      mutable_freq->set_interactive_style_rotate(false);
    }
    if (mutable_freq->interactive_style_shake() &&
        CheckUniverseFeatureInterval(1003, SPDM_feedShakeInterval(session_data_->get_spdm_ctx()))) {
      mutable_freq->set_interactive_style_shake(true);
    } else {
      mutable_freq->set_interactive_style_shake(false);
    }
  }
}

void UserInfoCollection::FillUniverseInnerLoopUserTagInfo() {
  std::string value;
  uint64_t user_tag = 0;
  auto ret = async_redis_helper_.Wait(&universe_inner_loop_user_tag_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty() && absl::SimpleAtoi(value, &user_tag)) {
    session_data_->dot_perf->Count(1, "front_server.universe_innder_loop_user_tag", "true");
  } else {
    session_data_->dot_perf->Count(1, "front_server.universe_innder_loop_user_tag", "false");
  }

  auto* ad_user_info = session_data_->mutable_ad_request()->mutable_ad_user_info();
  ad_user_info->set_universe_inner_loop_user_tag(user_tag);

  FillUniverseDeliveredQponInfoInit();
}

void UserInfoCollection::FillUniverseDeliveredQponInfoInit() {
  // 依赖于 universe_inner_loop_user_tag, Init 必须放在 FillUniverseInnerLoopUserTagInfo 之后
  session_data_->set_ud_universe_delivered_qpon_redis_admit(false);
  if (SPDM_enable_universe_delivered_qpon_info(session_data_->get_spdm_ctx())) {
    auto user_tag = session_data_->get_ad_request()->ad_user_info().universe_inner_loop_user_tag();
    bool is_high_value_user = false;
    auto user_tag_set_ptr = FrontKconfUtil::UniverseQcpxUserTagList();
    if (SPDM_enable_universe_qcpx_admit_u0_front(session_data_->get_spdm_ctx())) {
      user_tag_set_ptr = FrontKconfUtil::UniverseQcpxUserTagListV2();
    }
    if (user_tag_set_ptr != nullptr) {
      for (int64_t bit_pos : *user_tag_set_ptr) {
        uint64_t filter_user_tag = 1ULL << bit_pos;
        is_high_value_user = ((user_tag & filter_user_tag) > 0);
        if (is_high_value_user) {
          session_data_->set_ud_universe_delivered_qpon_redis_admit(true);
          break;
        }
      }
    }
  }

  universe_delivered_qpon_info_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&universe_delivered_qpon_info_params_);
  universe_delivered_qpon_cnt_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&universe_delivered_qpon_cnt_params_);
}

void UserInfoCollection::FillUniverseDeliveredQponInfo() {
  // 一致性 redis
  std::string value;
  async_redis_helper_.AsyncGet(&universe_delivered_qpon_info_params_);
  auto ret = async_redis_helper_.Wait(&universe_delivered_qpon_info_params_, &value);
  bool key_not_exist = (ret == ks::infra::KS_INF_REDIS_ERR_NONEXIST) && value.empty();
  bool parse_ok = false;
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty()) {
    kuaishou::ad::UniverseDeliveredQponInfo delivered_qpon_info;
    if (delivered_qpon_info.ParseFromString(value)) {
      parse_ok = true;
      session_data_->mutable_ad_request()->mutable_universe_delivered_qpon_info()->CopyFrom(
          delivered_qpon_info);  // NOLINT
    }
  }
  // 如果访问 redis 失败或者解析失败，则后续不能在错误的结果上追加写一条数据
  bool success = key_not_exist || parse_ok;
  session_data_->set_ud_get_universe_delivered_qpon_info_success(success);

  session_data_->dot_perf->Count(1, "front_server.universe_delivered_qpon_info", std::to_string(success));

  // 频控 redis
  int64_t delivery_cnt = 0;
  bool delivery_cnt_success = false;
  value.clear();
  async_redis_helper_.AsyncGet(&universe_delivered_qpon_cnt_params_);
  ret = async_redis_helper_.Wait(&universe_delivered_qpon_cnt_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !value.empty() && absl::SimpleAtoi(value, &delivery_cnt)) {
    delivery_cnt_success = true;
  }
  session_data_->mutable_ad_request()->set_universe_qpon_delivery_daily_cnt(delivery_cnt);
  session_data_->dot_perf->Count(1, "front_server.universe_delivered_qpon_cnt",
                                 std::to_string(delivery_cnt_success));
}

void UserInfoCollection::FillUniverseReservationInfo() {
  std::vector<std::string> reservation_list;
  auto ret = async_redis_helper_.Wait(&universe_live_reservation_info_params_, &reservation_list);
  int64_t reservation_id = 0;
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && !reservation_list.empty()) {
    for (const std::string& reserve_str : reservation_list) {
      if (absl::SimpleAtoi(reserve_str, &reservation_id)) {
        session_data_->mutable_ad_request()->add_reservation_ids(reservation_id);
      }
    }
    session_data_->dot_perf->Count(1, "front_server.universe_live_reservation_info_succ");
  } else {
    session_data_->dot_perf->Count(1, "front_server.universe_live_reservation_info_fail");
  }
}

void UserInfoCollection::AsyncRedisInit() {
  universe_click_reward_freq_params_.Init(session_data_);
  async_redis_helper_.AsyncZsetRange(&universe_click_reward_freq_params_);
  universe_interactive_style_freq_params_.Init(session_data_);
  async_redis_helper_.AsyncZsetRange(&universe_interactive_style_freq_params_);
  universe_feature_interval_params_.Init(session_data_);
  async_redis_helper_.AsyncListRange(&universe_feature_interval_params_);
  ad_browsed_info_params_.Init(session_data_);
  async_redis_helper_.AsyncListRange(&ad_browsed_info_params_);

  if (SPDM_enableUniversePassNewFreqMap()) {
    auto browsed_info_freq_filter = engine_base::AdKconfUtil::universeBrowsedInfoFreqFilterNew()->data();
    *(session_data_->mutable_ud_universe_browsed_freq_config_ids()) =
        std::move(browsed_info_freq_filter.GetConfigIds());
    ad_browsed_info_params_simple_.Init(session_data_);
    async_redis_helper_.AsyncMGet(&ad_browsed_info_params_simple_);
  }
  universe_fire_poke_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&universe_fire_poke_params_);
  universe_llm_u2p_params_.Init(session_data_);
  universe_llm_u2p_v2_params_.Init(session_data_);
  if (SPDM_enable_universe_llm_u2p_v2(session_data_->get_spdm_ctx())) {
    async_redis_helper_.AsyncGet(&universe_llm_u2p_v2_params_);
  }
  universe_cpm_model_pos_params_.Init(session_data_);
  universe_cpm_model_app_params_.Init(session_data_);
  universe_cpm_model_industry_params_.Init(session_data_);
  universe_cpm_model_style_params_.Init(session_data_);
  universe_fill_model_pos_params_.Init(session_data_);
  universe_fill_model_app_params_.Init(session_data_);
  universe_fill_model_industry_params_.Init(session_data_);
  universe_fill_model_style_params_.Init(session_data_);
  if (SPDM_enable_quality_model_default_strategy(session_data_->get_spdm_ctx())
      || SPDM_enable_fix_new_media_strategy(session_data_->get_spdm_ctx())
      || SPDM_enable_fix_new_media_strategy_v2(session_data_->get_spdm_ctx())) {
    async_redis_helper_.AsyncGet(&universe_cpm_model_pos_params_);
    async_redis_helper_.AsyncGet(&universe_cpm_model_app_params_);
    async_redis_helper_.AsyncGet(&universe_cpm_model_industry_params_);
    async_redis_helper_.AsyncGet(&universe_cpm_model_style_params_);
    async_redis_helper_.AsyncGet(&universe_fill_model_pos_params_);
    async_redis_helper_.AsyncGet(&universe_fill_model_app_params_);
    async_redis_helper_.AsyncGet(&universe_fill_model_industry_params_);
    async_redis_helper_.AsyncGet(&universe_fill_model_style_params_);
  }
  universe_ecpm_win_explore_params_.Init(session_data_);
  if (SPDM_enable_universe_ecpm_win_explore(session_data_->get_spdm_ctx())) {
    async_redis_helper_.AsyncListRange(&universe_ecpm_win_explore_params_);
  }
  universe_active_w_level_params_.Init(session_data_);
  if (SPDM_enable_universe_fill_active_w_level(session_data_->get_spdm_ctx())) {
    async_redis_helper_.AsyncGet(&universe_active_w_level_params_);
  }
  universe_carousel_params_.Init(session_data_);
  async_redis_helper_.AsyncZsetRange(&universe_carousel_params_);
  universe_cpm_bound_params_.Init(session_data_);
  universe_rtb_cpm_bid_coff_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&universe_rtb_cpm_bid_coff_params_);

  converted_unit_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&converted_unit_params_);
  debug_info_params_.Init(session_data_);
  async_redis_helper_.AsyncListRange(&debug_info_params_);
  // 内循环人群资产策略人群数据
  inner_strategy_crowd_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&inner_strategy_crowd_params_);
  universe_inner_u2x_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&universe_inner_u2x_params_);
  // 激励电商已购人群
  ad_credict_user_score_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&ad_credict_user_score_params_);

  // 联盟流量内循环用户 tag
  universe_inner_loop_user_tag_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&universe_inner_loop_user_tag_params_);

  // 联盟直播预约获取
  universe_live_reservation_info_params_.Init(session_data_);
  async_redis_helper_.AsyncListRange(&universe_live_reservation_info_params_);

  // universe_user_scene_freq_params_.Init(session_data_);
  // async_redis_helper_.AsyncGet(&universe_user_scene_freq_params_);

  universe_tiny_user_query_recalled_product_.Init(session_data_);
  async_redis_helper_.AsyncZsetRangeByMicro(&universe_tiny_user_query_recalled_product_);
  universe_tiny_user_query_recalled_product_longer_.Init(session_data_);
  async_redis_helper_.AsyncZsetRangeByMicro(&universe_tiny_user_query_recalled_product_longer_);

  universe_componentized_style_freq_.Init(session_data_);
  async_redis_helper_.AsyncZsetRange(&universe_componentized_style_freq_);
}

void UserInfoCollection::SetRequestDebugInfo() {
  // 一定的几率去请求 redis 查看是否有 debug 设置
  FillDebugInfo(session_data_->mutable_ad_request()->mutable_debug_param());
}

void UserInfoCollection::FillDebugInfo(kuaishou::ad::DebugParam* debug_param) {
  if (!debug_info_params_.IsAllow()) {
    return;
  }
  std::vector<std::string> values;
  auto ret = async_redis_helper_.Wait(&debug_info_params_, &values);
  if (ret || values.empty()) {
    return;
  }
  ad_base::AdRandomShuffle::Shuffle(values.begin(), values.end());
  int64_t creative_id = 0;
  for (const auto& item : values) {
    if (base::StringToInt64(item, &creative_id) && creative_id > 0) {
      debug_param->add_creative_id(creative_id);
      return;
    }
  }
}

void UserInfoCollection::FillIdsAndShieldLive() {
  const std::string& ad_client_info = session_data_->get_ad_request()->ad_client_info();
  Json json{base::StringToJson(ad_client_info)};
  std::string photo_ids = json.GetString("photoIds", "");
  std::string livestream_ids = json.GetString("liveStreamIds", "");
  bool enable_shield_live_ad = json.GetBoolean("enableShieldLiveAd", false);
  session_data_->mutable_ad_request()->mutable_ad_user_info()->set_enable_shield_live_ad(
      enable_shield_live_ad);
  if (enable_shield_live_ad) {
    session_data_->dot_perf->Count(1, "enable_shield_live_ad");
  }
  if (!photo_ids.empty()) {
    std::vector<std::string> tokens;
    base::SplitString(photo_ids, ",", &tokens);
    auto* ad_user_info = session_data_->mutable_ad_request()->mutable_ad_user_info();
    for (const auto& token : tokens) { ad_user_info->add_encrypt_photo_ids(token); }
  }

  if (!livestream_ids.empty()) {
    std::vector<std::string> tokens;
    base::SplitString(livestream_ids, ",", &tokens);
    auto* ad_user_info = session_data_->mutable_ad_request()->mutable_ad_user_info();
    for (const auto& token : tokens) { ad_user_info->add_encrypt_live_stream_ids(token); }
  }
}

void UserInfoCollection::FillContextCommonData() {
  if (nullptr == session_data_->get_ad_request()) {
    return;
  }
  const kuaishou::ad::AdRequest& ad_request = *(session_data_->get_ad_request());
  session_data_->mutable_ad_request()->mutable_front_internal_data()->set_is_lahuo_user(
      this->IsLahuoUserV2());
  session_data_->mutable_ad_request()->mutable_front_internal_data()->set_is_valid_unlogin_traffic(
      IsValidUnlogInTraffic(ad_request, session_data_->mutable_spdm_ctx(),
                            ad_request.front_internal_data().is_lahuo_user()));
  session_data_->dot_perf->Count(
      1, "ad.front_server.is_valid_unlogin_traffic_lahuo",
      absl::StrCat(ad_request.front_internal_data().is_lahuo_user()),
      absl::StrCat(session_data_->get_ad_request()->front_internal_data().is_valid_unlogin_traffic()),
      absl::StrCat(ad_request.ad_user_info().is_unlogin_user()));
  // 依赖画像数据
  session_data_->mutable_ad_request()->mutable_front_internal_data()->set_is_star_user(
      ad_request.ad_user_info().fan_count() > FrontKconfUtil::minFanCount());
  // 新设备归因渠道是否信息流
  auto dsp_channel_config = FrontKconfUtil::dspChannelConfig();
  if (ad_request.reco_user_info().has_extra_info() &&
      ad_request.reco_user_info().extra_info().has_new_device_attribution_channel() &&
      !ad_request.reco_user_info().extra_info().new_device_attribution_channel().empty() &&
      dsp_channel_config->count(ad_request.reco_user_info().extra_info().new_device_attribution_channel()) >
          0) {
    session_data_->dot_perf->Count(1, "new_device_attribution_channel_is_dsp");
  }
  // 新设备
  session_data_->mutable_ad_request()->mutable_front_internal_data()->set_is_new_device(
      ad_request.ad_user_info().new_device());
  session_data_->dot_perf->Count(
      1, "front_server_is_new_device_user_num",
      absl::Substitute("is_new_device_$0",
                       session_data_->get_ad_request()->front_internal_data().is_new_device()),
      absl::Substitute("is_new_user_$0",
                       session_data_->get_ad_request()->front_internal_data().is_new_user()));

  session_data_->mutable_ad_request()->mutable_front_internal_data()->set_reco_refresh_times(
      GetUserRefreshTimes(ad_request));
  session_data_->mutable_ad_request()->mutable_front_internal_data()->set_is_reflux_device(
      IsRefluxDevice(ad_request));
  session_data_->mutable_ad_request()->mutable_front_internal_data()->set_is_new_device_reflux_user(
      IsNewDeviceRefluxUser(ad_request));
  session_data_->mutable_ad_request()->mutable_front_internal_data()->set_is_nr_tnu_user(
      IsNrTnuUser(session_data_));
  //  将拉活用户 传给 adserver
  session_data_->mutable_ad_request()->set_is_lahuo_user(
      session_data_->get_ad_request()->front_internal_data().is_lahuo_user());
  session_data_->mutable_ad_request()->set_is_valid_unlogin_traffic(
      session_data_->get_ad_request()->front_internal_data().is_valid_unlogin_traffic());
}

// 这种开关切换新老代码, 会有只改旧代码, 没改新代码的情况, 没问题的话周二都推全, 避免前述问题.
bool UserInfoCollection::IsLahuoUserV2() {
  bool is_lahuo_user = false;

  const auto& deep_link_detail =
      session_data_->get_ad_request()->reco_user_info().extra_info().deep_link_detail().details();
  bool is_lahuo_from_userinfo =
      !deep_link_detail.empty() && deep_link_detail[0].find("type=lahuo") != std::string::npos;
  is_lahuo_user = is_lahuo_from_userinfo;

  if (is_lahuo_user) {
    session_data_->dot_perf->Count(1, "ad.front_server.IsLahuoUserNew", absl::StrCat(is_lahuo_from_userinfo),
                                   "userinfo");
  } else {
    session_data_->dot_perf->Count(1, "ad.front_server.IsLahuoUserNew", absl::StrCat(is_lahuo_user), "all");
  }
  return is_lahuo_user;
}

void UserInfoCollection::FillStrategyCrowdInfo() {
  if (!inner_strategy_crowd_params_.IsAllow()) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&inner_strategy_crowd_params_, &value);
  if (ret != ks::infra::KS_INF_REDIS_NO_ERROR || value.empty()) {
    return;
  }
  std::string pb_str;
  kuaishou::ad::AdUserInfo user_info;
  if (base::Base64Decode(value, &pb_str) && user_info.ParseFromString(pb_str)) {
    session_data_->mutable_ad_request()->mutable_ad_user_info()->mutable_strategy_crowd_info()->CopyFrom(
        user_info.strategy_crowd_info());
  }
}

void UserInfoCollection::FillHonorDevice() {
  if (!session_data_->get_ad_request()) {
    return;
  }
  // 判断是否为荣耀设备
  auto device_match_honor = [](const std::string& device) {
    if (device.empty()) {
      return false;
    }
    return device.find("honor") != std::string::npos || device.find("荣耀") != std::string::npos;
  };

  if (session_data_->get_ad_request()->has_ad_user_info()) {
    if (session_data_->get_ad_request()->ad_user_info().platform() == "ios") {
      return;
    }
    for (const auto& device_info : session_data_->get_ad_request()->ad_user_info().device_info()) {
      std::string device_mod = absl::AsciiStrToLower(device_info.device_mod());
      std::string readable_mod = absl::AsciiStrToLower(device_info.readable_mod());
      if (device_match_honor(device_mod) || device_match_honor(readable_mod)) {
        session_data_->set_is_honor_device(true);
        return;
      }
    }
  }

  if (session_data_->get_ad_request()->has_reco_user_info()) {
    std::string device_mod =
        absl::AsciiStrToLower(session_data_->get_ad_request()->reco_user_info().device_basic_info().mod());
    if (device_match_honor(device_mod)) {
      session_data_->set_is_honor_device(true);
      return;
    }
  }
}

void UserInfoCollection::FillHuaweiDevice() {
  auto blocker_map = FrontKconfUtil::huaweiAdBlockerMap();
  auto huawei_device_list = FrontKconfUtil::huaweiDeviceList();
  if (blocker_map == nullptr || huawei_device_list == nullptr) {
    return;
  }
  auto iter = blocker_map->find(session_data_->get_sub_page_id());
  if (iter == blocker_map->end()) {
    return;
  }
  for (const auto& device_info : session_data_->get_ad_request()->ad_user_info().device_info()) {
    std::string device_mod = device_info.device_mod();
    device_mod = absl::AsciiStrToLower(device_mod);
    for (auto device_name : *huawei_device_list) {
      if (device_mod.find(device_name) != std::string::npos) {
        session_data_->dot_perf->Count(1, "huawei_device_marker_hit", device_name);
        return;
      }
    }
  }
}

void UserInfoCollection::GetConvertedUnitAsyncV2() {
  if (!converted_unit_params_.IsAllow()) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&converted_unit_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    if (!session_data_->mutable_ad_request()->mutable_ad_user_info()->mutable_ad_cov_event()->ParseFromString(
            value)) {
      session_data_->mutable_ad_request()->mutable_ad_user_info()->clear_ad_cov_event();
    }
  }
}

void UserInfoCollection::FillUniverseTinyQueryRecalledProductIds() {
  int64_t product_id_num = FrontKconfUtil::universeTinyQueryRecalledProductNum();
  if (universe_tiny_user_query_recalled_product_.IsAllow()) {
    std::vector<std::string> redis_result;
    auto ret = async_redis_helper_.Wait(&universe_tiny_user_query_recalled_product_, &redis_result);
    std::reverse(redis_result.begin(), redis_result.end());
    for (int i = 0; i < redis_result.size() && i < product_id_num; i++) {
      int64_t product_id = 0;
      if (absl::SimpleAtoi(redis_result[i], &product_id)) {
        session_data_->mutable_ad_request()
            ->mutable_ad_user_info()
            ->add_universe_user_query_recalled_product_ids(product_id);  // NOLINT
      }
    }
    session_data_->dot_perf->Interval(redis_result.size(), "universe_tiny_visit_user_history_query_product",
                                      redis_result.size() ? "found" : "not_found");
  }
  if (universe_tiny_user_query_recalled_product_longer_.IsAllow()) {
    std::vector<std::string> redis_result;
    auto ret = async_redis_helper_.Wait(&universe_tiny_user_query_recalled_product_longer_, &redis_result);
    std::reverse(redis_result.begin(), redis_result.end());
    for (int i = 0; i < redis_result.size() && i < product_id_num; i++) {
      int64_t product_id = 0;
      if (absl::SimpleAtoi(redis_result[i], &product_id)) {
        session_data_->mutable_ad_request()
            ->mutable_ad_user_info()
            ->add_universe_user_query_recalled_product_ids_longer(product_id);
      }
    }
    session_data_->dot_perf->Interval(redis_result.size(),
                                      "universe_tiny_visit_user_history_query_product_longer",
                                      redis_result.size() ? "found" : "not_found");
  }
}

void UserInfoCollection::FillAdCredictUserScoreValue() {
  if (!ad_credict_user_score_params_.IsAllow()) {
    return;
  }
  std::string value;
  auto ret = async_redis_helper_.Wait(&ad_credict_user_score_params_, &value);
  if (ret != ks::infra::KS_INF_REDIS_NO_ERROR || value.empty()) {
    session_data_->dot_perf->Count(1, "ad.front_server.ad_credict_user_score_redis_failed");
    session_data_->set_has_ad_credict_user_score_redis(false);
    return;
  }
  session_data_->set_has_ad_credict_user_score_redis(true);
}

void UserInfoCollection::FillUniverseComponentizedStyleFreq() {
  if (!universe_componentized_style_freq_.IsAllow()) {
    return;
  }
  std::vector<std::string> value;
  kuaishou::ad::universe::FreqDetailInfo freq_detail_info;
  auto ret = async_redis_helper_.Wait(&universe_componentized_style_freq_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    auto* component_imp_freq =
        session_data_->mutable_ad_request()->mutable_ad_user_info()->mutable_component_imp_freq();  // NOLINT
    component_imp_freq->clear();
    for (const auto& v : value) {
      if (freq_detail_info.ParseFromString(v)) {
        (*component_imp_freq)[freq_detail_info.feature_id()]++;
      }
    }
  }
}

void UserInfoCollection::Clear() {
  // params 添加了 is_allow 之后有状态，所有的 params 都务必 Clear()！！！
  converted_unit_params_.Clear();
  debug_info_params_.Clear();
  universe_inner_loop_user_tag_params_.Clear();
  universe_delivered_qpon_info_params_.Clear();
  universe_live_reservation_info_params_.Clear();
  inner_strategy_crowd_params_.Clear();
  universe_inner_u2x_params_.Clear();
  universe_click_reward_freq_params_.Clear();
  universe_carousel_params_.Clear();
  universe_fire_poke_params_.Clear();
  universe_active_w_level_params_.Clear();
  universe_llm_u2p_params_.Clear();
  universe_llm_u2p_v2_params_.Clear();
  universe_cpm_model_pos_params_.Clear();
  universe_cpm_model_app_params_.Clear();
  universe_cpm_model_industry_params_.Clear();
  universe_cpm_model_style_params_.Clear();
  universe_fill_model_pos_params_.Clear();
  universe_fill_model_app_params_.Clear();
  universe_fill_model_industry_params_.Clear();
  universe_fill_model_style_params_.Clear();
  universe_ecpm_win_explore_params_.Clear();
  universe_cpm_bound_params_.Clear();
  universe_rtb_cpm_bid_coff_params_.Clear();
  universe_interactive_style_freq_params_.Clear();
  universe_feature_interval_params_.Clear();
  ad_browsed_info_params_.Clear();
  ad_browsed_info_params_simple_.Clear();
  universe_user_scene_freq_params_.Clear();
  universe_tiny_user_query_recalled_product_.Clear();
  universe_tiny_user_query_recalled_product_longer_.Clear();
  ad_credict_user_score_params_.Clear();
  universe_componentized_style_freq_.Clear();
  fanstop_session_info_.clear();
  kuaishou::reco::MixSessionMeta().Swap(&mix_session_meta_);
}
}  // namespace ks::front_server
