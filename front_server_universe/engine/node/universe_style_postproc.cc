#include "teams/ad/front_server_universe/engine/node/universe_style_postproc.h"

#include <algorithm>
#include <limits>
#include <map>
#include <set>
#include <utility>
#include <vector>

#include "absl/strings/match.h"
#include "absl/strings/substitute.h"
#include "base/encoding/base64.h"
#include "base/encoding/url_encode.h"
#include "base/hash_function/md5.h"
#include "glog/logging.h"
#include "google/protobuf/util/json_util.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "serving_base/crypt/aes_crypter.h"
#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/ad_base/src/container/stl_helper.h"
#include "teams/ad/ad_base/src/log_record/util.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_jinniu_info.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/engine_base/cache_loader/ad_traffic_creative_rule_v2.h"
#include "teams/ad/engine_base/utils/pb_empty_value_check.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/front_server_universe/common/fill_ext_data.h"
#include "teams/ad/front_server_universe/engine/strategy/style_postproc/common_style_postproc_strategy.h"
#include "teams/ad/front_server_universe/engine/strategy/style_postproc/dynamic_creative_strategy.h"
#include "teams/ad/front_server_universe/engine/strategy/style_postproc/merchant_style_strategy.h"
#include "teams/ad/front_server_universe/engine/utils/ad_pack/base_info_pack/common_base_info_pack.h"
#include "teams/ad/front_server_universe/engine/utils/merchant/merchant_logic.h"
#include "teams/ad/front_server_universe/engine/utils/target_check.h"
#include "teams/ad/front_server_universe/util/adx/adx_common_util.h"
#include "teams/ad/front_server_universe/util/color/action_bar_color.h"
#include "teams/ad/front_server_universe/util/creative_parser/creative_parser.h"
#include "teams/ad/front_server_universe/util/merchant_util/merchant_style_util.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/track/tracking.h"
#include "teams/ad/front_server_universe/util/utility/front_logging.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"

namespace ks::front_server {

static const int16_t kRedisOpTimeoutMsUniverseStylePos = 10;
static const int phoneSpecificVerticalHeight = 1920;
static const int phoneSpecificBannerHeight = 170;
static const absl::flat_hash_set<kuaishou::ad::AdActionType> kwai_serial_opcxs = {
    kuaishou::ad::AD_ROAS, kuaishou::ad::AD_PURCHASE, kuaishou::ad::EVENT_KEY_INAPP_ACTION,
    kuaishou::ad::AD_IAA_ROAS};
namespace {

bool IsProgrammaticCreative(const kuaishou::ad::tables::Creative& creative) {
  return creative.create_source_type() == kuaishou::ad::AdEnum::CUSTOMIZED_PROGRAMMATIC_CREATIVE ||
         creative.create_source_type() == kuaishou::ad::AdEnum::PACKAGE_PROGRAMMATIC_CREATIVE;
}

}  // namespace

bool UniverseStylePostProc::CheckCreativeRuleWhiteListNew(const int64_t& creative_id,
                                                          const int32_t& rule_id) {
  // 未命中先审后投 直接返回
  if (session_data_->get_ud_white_traffic_ids().empty()) {
    return true;
  }
  bool valid_check = engine_base::AdCreativeAuditV2::GetInstance()->traffic_material_valid_check(
      session_data_->get_ud_white_traffic_ids(), creative_id, rule_id);
  return valid_check;
}

void UniverseStylePostProc::Initialize(ContextData* context) {
  session_data_ = context;
  is_ios_platform_ = (session_data_->get_ad_request()->ad_user_info().platform() == "ios");
  app_version_ = session_data_->get_ad_request()->ad_user_info().platform_version();
  auto* p_strategy_manager = context_->GetContextSystem<StrategyManager>();
  common_style_post_strategy_ =
      p_strategy_manager->GetStrategy<CommonStylePostStrategy>(AdStrategyType::CommonStylePostStrategy);
  white_creative_rule_ids_set_.clear();
  white_playcard_ids_ = absl::StrSplit(
      session_data_->get_spdm_ctx().TryGetString("union_white_playcard_ids", "101010,201010,102010,202010"),
      ',', absl::SkipWhitespace());
  universe_tiny_search_white_list_ =
      std::make_shared<UniverseTinySearchWhiteList>(FrontKconfUtil::universeTinySearchWhiteList()->data());
  traffic_id_ = 0;
  traffic_type_ = 0;
  local_llsid = session_data_->get_llsid();
  merchant_style_strategy_ =
      p_strategy_manager->GetStrategy<MerchantStyleStrategy>(AdStrategyType::MerchantStyle);
  deliver_packer_params_.Initialize(session_data_);
  ad_style_mgr_params_.Reset(session_data_->mutable_spdm_ctx());

  dpa_descrtption_info_ = FrontKconfUtil::dpaDescrtptionInfo();
}

void UniverseStylePostProc::Clear() {
  session_data_ = nullptr;
  fanstop_authors_delivery_.clear();
  fanstop_base_info_pack_.Clear();
}

bool UniverseStylePostProc::ProcessInner() {
  TimeRecorder timer(context_->GetMutableContextData<ContextData>(),
                     static_cast<int32_t>(kuaishou::ad::AdFrontNodeType::FRONT_NODE_STYLE_POSTPROC_TYPE));

  if (ad_base::AdRandom::GetInt(1, 100) > FrontKconfUtil::checkStyleDiffRatio()) {
    return true;
  }

  auto* context = context_->GetMutableContextData<ContextData>();
  if (context == nullptr) {
    TLOG(ERROR) << "style proc failed, context is null";
    return false;
  }

  Initialize(context);

  // 联盟填充用户关注列表
  FillUniverseFollowList();
  auto* style_info_map = session_data_->mutable_style_info_resp()->mutable_style_info();

  // 添充公共数据
  common_style_post_strategy_->FillCommonResult();

  session_data_->dot_perf->Interval(session_data_->get_ad_list().Size(), "universe_result_size");
  const auto& pos_id = session_data_->get_ud_pos_id();
  SetDebugInfo(*(session_data_->get_ad_request()), session_data_, session_data_->mutable_ad_list(),
               session_data_->mutable_spdm_ctx());

  int dpa_num_before_process = 0;
  int dpa_num_after_process = 0;
  auto request_imp_info_size = session_data_->get_pos_manager().request_imp_infos.size();
  const auto raw_request_imp_info_size = request_imp_info_size;
  const auto ad_style = session_data_->get_ud_ad_style();
  // 处理 server_show 广告
  // candidate 数据流的正样本 & server_show 的数据流信息都在这里填充
  for (int i = 0; i < session_data_->get_ad_list().Size(); i++) {
    RankAdCommon* ad_common = session_data_->mutable_ad_list()->At(i);
    if (!ad_common) {
      continue;
    }
    auto* item = ad_common->GetResult();
    if (!item) {
      continue;
    }
    if (request_imp_info_size <= 0) {
      session_data_->mutable_ad_select_stage_infos()->SetAdResponseFilterInfo(
          item->ad_deliver_info().ad_queue_type() == kuaishou::ad::AdEnum_AdQueueType_HARD_AD_QUEUE,
          item->ad_deliver_info().ad_base_info().creative_id(),
          kuaishou::log::ad::AdTraceFilterCondition::RESPONSE_FULL_FILTER,
          kuaishou::ad::AdFrontNodeType::FRONT_NODE_STYLE_POSTPROC_TYPE);
      session_data_->mutable_ad_list()->GetRangeWithFunc(
          [this](kuaishou::ad::AdResult*  /*ad_result*/, RankAdCommon* ad) { ad->SetAdInValid(); }, i, -1);
      break;
    }
    if (item->ad_deliver_info().ad_base_info().campaign_type() ==
        kuaishou::ad::AdEnum_CampaignType_DPA_CAMPAIGN) {
      dpa_num_before_process++;
    }

    const auto* p_rank_result = GetAdRankResult(session_data_, *item);
    uint64_t creative_id = item->ad_deliver_info().ad_base_info().creative_id();
    auto& style_info = (*style_info_map)[creative_id];
    if (!p_rank_result) {
      LOG(ERROR) << "front_server.no_ad_rank_result.creative_id:" << creative_id;
      session_data_->mutable_ad_select_stage_infos()->SetAdResponseFilterInfo(
          item->ad_deliver_info().ad_queue_type() == kuaishou::ad::AdEnum_AdQueueType_HARD_AD_QUEUE,
          item->ad_deliver_info().ad_base_info().creative_id(),
          kuaishou::log::ad::AdTraceFilterCondition::REQUEST_FORWARD_INVALID_FILTER,
          kuaishou::ad::AdFrontNodeType::FRONT_NODE_STYLE_POSTPROC_TYPE);
      ad_common->SetAdInValid();
      continue;
    }

    // 新增联盟内循环广告屏蔽实验
    bool is_skip_search_dark_ad_flow =
        SPDM_enable_distribut_flow_skip_search_ad_dark(session_data_->get_spdm_ctx()) &&
        FrontKconfUtil::universeStoreDistributSkipDarkPosId()->count(session_data_->get_ud_pos_id());
    bool is_skip_all_dark_ad_flow =
        SPDM_enable_universe_store_distribut_skip_dark(session_data_->get_spdm_ctx()) &&
        FrontKconfUtil::universeSkipAllDarkAdPosId()->count(session_data_->get_ud_pos_id());
    if ((is_skip_search_dark_ad_flow || is_skip_all_dark_ad_flow) &&
        (ad_common->is_universe_search_ads() ||
         (ad_common->is_non_universe_ads() && style_info.unit_support_info().quick_search() == 1))) {
      SetSearchAdInfo(style_info, item);  // 搜索快投 & 直投 x 厂商分发流量
    }
    // 搜索广告暗投联盟
    if ((ad_common->is_universe_search_ads() ||
         (!session_data_->borrow_ud_search_query().empty() &&
          (session_data_->get_ud_ad_style() == 19 || session_data_->get_ud_ad_style() == 20) &&
          (!session_data_->get_universe_tiny_search_black_list()->IsHit(
              style_info.creative().account_id(), style_info.unit().ocpx_action_type(),
              style_info.unit().deep_conversion_type(),
              style_info.account().product_name()))  // NOLINT
          && ad_common->is_non_universe_ads() && style_info.unit_support_info().quick_search() == 1))) {
      SetSearchAdInfo(style_info, item);
    }
    // 搜索暗投 x 非厂商 只对放出的增量记收 （快投 x 白名单账户 x 白名单优化目标 x 非厂商）
    if (SPDM_universe_record_quick_search_budget(session_data_->get_spdm_ctx()) &&
        engine_base::AdKconfUtil::universeSearchDarkConfig()) {
      auto search_black_data_config = engine_base::AdKconfUtil::universeSearchDarkConfig()->data();
      if (!session_data_->get_ud_is_universe_tiny_flow() &&
          (  // 非厂商 x 白名单 x 优化目标
              search_black_data_config.QuickSearchIsHitWhite(style_info.creative().account_id(),
                                                             style_info.unit().ocpx_action_type()) &&
              ad_common->is_non_universe_ads() &&
              style_info.unit_support_info().quick_search() == 1)) {  // 暗投 x 快投
        SetSearchAdInfo(style_info, item);
      }
    }

    const auto& rank_result = *p_rank_result;
    FillApiExtData fill_api_data;
    CreativeParser creative_parser;
    creative_parser.Parse(style_info.creative(), session_data_->get_llsid());
    if (!SetMaterialInfo(style_info, creative_parser, item)) {
      session_data_->mutable_ad_select_stage_infos()->SetAdResponseFilterInfo(
          ad_common->IsHardAd(), item->ad_deliver_info().ad_base_info().creative_id(),
          kuaishou::log::ad::AdTraceFilterCondition::MATERIAL_FILTER,
          kuaishou::ad::AdFrontNodeType::FRONT_NODE_STYLE_POSTPROC_TYPE);
      ad_common->SetAdInValid();
      session_data_->dot_perf->Count(1, "front_server.material_replace_filter_ad_count",
                                     absl::StrCat(pos_id));
      if (session_data_->get_ad_list().Size() == 0) {
        session_data_->dot_perf->Count(1, "front_server.material_replace_filter_pv_count",
                                       absl::StrCat(pos_id));
      }
      continue;
    }

    if (!session_data_->get_ud_white_traffic_ids().empty()) {
      auto* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
      auto* material_info = base_info->mutable_material_info();
      if (material_info->material_feature_size() > 0) {
        auto* feature = material_info->mutable_material_feature(0);
        int32_t rule_id = feature->rule_id();
        int64_t creative_id = style_info.creative().id();
        if (!CheckCreativeRuleWhiteListNew(creative_id, rule_id)) {
          session_data_->mutable_ad_select_stage_infos()->SetAdResponseFilterInfo(
              ad_common->IsHardAd(), item->ad_deliver_info().ad_base_info().creative_id(),
              kuaishou::log::ad::AdTraceFilterCondition::MATERIAL_FILTER,
              kuaishou::ad::AdFrontNodeType::FRONT_NODE_STYLE_POSTPROC_TYPE);
          ad_common->SetAdInValid();
          session_data_->dot_perf->Count(1, "front_server.creative_audit_filter_ad_count",
                                         session_data_->copy_ud_white_traffic_ids_str());
          continue;
        }
      }
    }
    const auto& task_type_list = session_data_->UniverseRequest().accept_task_type();
    bool is_playble_ad =
        style_info.unit_support_info().playable_switch() == 2 || style_info.play_info().upload_source() == 2;
    if (is_playble_ad && std::count(task_type_list.begin(), task_type_list.end(), 4) && ad_style == 2) {
      if (request_imp_info_size == 2) {
        // 当前试玩广告是第一个广告，不填充第二个广告
        request_imp_info_size--;
      } else if (request_imp_info_size == 1) {
        // 当前试玩广告是第二个广告，删除第一个广告
        auto* first_ad = session_data_->mutable_ad_list()->FirstValidAd();
        if (first_ad) {
          first_ad->SetAdInValid();
        }
      }
    }

    // 注意，下方有些地方依赖扣费信息，所以价格修正和设置需要放在最前面 ！！！
    FixBidAndPrice(style_info, item);

    CommonAdBaseInfoPack common_base_pack(session_data_, &creative_parser, &style_info, item, ad_common);
    common_base_pack.Process();
    fanstop_base_info_pack_.Process(&style_info, session_data_, item, &fanstop_authors_delivery_);

    SetDspBaseInfo(context, style_info, creative_parser, item);
    if (ks::engine_base::AdKconfUtil::enableSetMaterialInfoInFront()) {
      SetAdxBaseInfo(style_info, item);
      SetAdxMaterialInfo(item);
      SetDpaMaterialInfo(item);
    }
    SetActionBarStyle(item);
    SetDynamicCreative(style_info, creative_parser, item, &fill_api_data);
    SetBaseDynamicCreative(item);
    FillApiData(item, &fill_api_data);
    SetSlideInfo(item);
    SetAdDataV2(&style_info, item);

    SetActionBarColor(style_info, item);
    request_imp_info_size--;
    session_data_->set_grid_unit_id(session_data_->get_grid_unit_id() + 1);
    common_deliver_info_pack_.Process(&style_info, &creative_parser, item, &deliver_packer_params_,
                                      ad_common);
    SetUniverseAdDataV2Fix(item);
    // callback 填充用到 deliverinfo 将 deliverinfo 的填充提前
    SetCallbackPassback(style_info, item, ad_common);
    // 开屏场景 增加 derived_creative_id
    SetDerivedInfo(style_info, item);
    SetTrackInfo(style_info, item, ad_common);
    SetOriginalLandingPageUrl(item);
    SetPackedInfo(style_info, item);
    TargetCheck tc(*session_data_, style_info);
    tc.SetTargetTag(item);

    // 所有 deliverinfo 字段填充都放到 chargeinfo 前面
    SetEncodeChargeInfo(style_info, item);

    // 对应中台流量需要构建 AdLog 用于上报透传
    BuildAdLog(*session_data_->get_ad_request(), style_info, item, ad_common);

    RecordAdShowInfo(item, rank_result);

    if (item->ad_deliver_info().ad_base_info().campaign_type() ==
        kuaishou::ad::AdEnum_CampaignType_DPA_CAMPAIGN) {
      dpa_num_after_process++;
    }
    ad_style_mgr_.SetAdDataV2Later(item, session_data_);
    // 字段兜底校验
    if (!AdInfoCheck(style_info, item)) {
      session_data_->mutable_ad_select_stage_infos()->SetAdResponseFilterInfo(
          ad_common->IsHardAd(), item->ad_deliver_info().ad_base_info().creative_id(),
          kuaishou::log::ad::AdTraceFilterCondition::MATERIAL_FILTER,
          kuaishou::ad::AdFrontNodeType::FRONT_NODE_STYLE_POSTPROC_TYPE);
      ad_common->SetAdInValid();
      session_data_->dot_perf->Count(1, "front_server.universe_ad_info_check");
    }
    if (SPDM_enablePbEmptyValueCheck()) {
      engine_base::PbEmptyValueChecker::Instance().Check(*ad_style_mgr_.GetResultPb());
      engine_base::PbEmptyValueChecker::Instance().Check(*item);
    }
  }
  session_data_->mutable_ad_list()->Compact();

  falcon::Stat("front_server.dpa_num_before_style_postproc", dpa_num_before_process);
  falcon::Stat("front_server.dpa_num_after_style_postproc", dpa_num_after_process);

  PERF_FINAL_AD_LIST(session_data_);

  Clear();
  return true;
}

bool UniverseStylePostProc::AdInfoCheck(const StyleInfoItem& style_info, AdResult* item) {
  int64_t pos_id = session_data_->get_ud_pos_id();
  const std::string& app_id = session_data_->copy_ud_app_id();
  int32_t media_uid = session_data_->get_ad_request()->universe_ad_request_info().medium_uid();
  const auto& package_name = item->ad_deliver_info().ad_base_info().package_name();
  const auto& deeplink = item->ad_deliver_info().ad_base_info().schema_url();

  if (FrontKconfUtil::universeDownloadAdFilterPosList()->count(pos_id) &&
      session_data_->get_ud_request_universe_tiny() &&
      (item->ad_deliver_info().ad_base_info().campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE ||
       style_info.campaign().type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP)) {
    if (package_name.empty() || deeplink.empty()) {
      session_data_->dot_perf->Count(1, "universe_ad_info_check", "ad_advance_package_deeplink_empty");
      return false;
    }
  }

  if (FrontKconfUtil::universeInstalledAppFilterAppId()->count(app_id) &&
      session_data_->get_ud_app_set().count(package_name)) {
    session_data_->dot_perf->Count(1, "universe_ad_info_check", "installed_app");
    return false;
  }

  if (FrontKconfUtil::universePkgWhiteListFilterPosId()->count(pos_id) &&
      !session_data_->get_ud_pkg_white_list().empty() &&
      session_data_->get_ud_pkg_white_list().count(package_name) == 0) {
    session_data_->dot_perf->Count(1, "universe_ad_info_check", "pkg_white_list");
    return false;
  }

  return true;
}

bool UniverseStylePostProc::SetNativeMaterialInfoForSplashDark(const StyleInfoItem& style_info,
                                                               const CreativeParser&  /*creative_parser*/,
                                                               AdMaterialInfo* material_info) {
  if (!material_info) {
    return false;
  }

  double ratio = 16.0 / 9.0;
  int64_t height = 0;
  int64_t width = 0;
  if (session_data_->get_ad_request()->has_universe_ad_request_info() &&
      session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() > 0) {
    height = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).height();
    width = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).width();
    if (height > 0 && width > 0) {
      ratio = static_cast<double>(height) / width;
    }
  }

  auto* feature = material_info->mutable_material_feature(0);
  const auto& material_str = style_info.creative().creative_support_info().splash_pictures();
  base::Json material_json(StringToJson(material_str));
  if (!material_json.IsArray()) {
    LOG_EVERY_N(WARNING, 1000) << "material_str is not json, " << material_str;
    return false;
  }

  bool has_selected = false;
  bool select_cur_item = false;
  double global_ratio_diff = std::numeric_limits<double>::max();
  int32_t global_width_diff = std::numeric_limits<int>::max();
  for (auto* item : material_json.array()) {
    select_cur_item = false;
    if (!item->IsObject()) {
      continue;
    }

    if (item->GetInt("photoId", 0) > 0) {  // 开屏视频，暂不支持
      continue;
    }

    int64_t cover_height = item->GetInt("height", 0);
    int64_t cover_width = item->GetInt("width", 0);
    if (cover_height <= 0 || cover_width <= 0) {
      continue;
    }

    double cover_ratio = static_cast<double>(cover_height) / cover_width;
    double abs_diff = std::fabs(cover_ratio - ratio);
    // 比例相同时，选宽差距最小的
    if (std::fabs(abs_diff - global_ratio_diff) < 0.0001) {
      int32_t width_diff = std::abs(cover_width - width);
      if (width_diff < global_width_diff) {
        global_width_diff = width_diff;
        has_selected = true;
        select_cur_item = true;
      }
    } else if (abs_diff < global_ratio_diff) {
      global_ratio_diff = abs_diff;
      global_width_diff = std::abs(cover_width - width);
      has_selected = true;
      select_cur_item = true;
    }
    if (select_cur_item) {
      feature->mutable_material_size()->set_height(cover_height);
      feature->mutable_material_size()->set_width(cover_width);
      feature->set_material_url(item->GetString("coverUrl", ""));
    }
  }

  material_info->set_material_type(AdEnum::AdMaterialType::AdEnum_AdMaterialType_SINGLE_PAGE_MATERIAL);
  feature->set_material_feature_type(
      AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE);

  feature->set_rule_id(2);
  feature->set_photo_id(0);
  return true;
}

bool UniverseStylePostProc::SetNativeMaterialInfo(const StyleInfoItem& style_info,
                                                  const CreativeParser& creative_parser,
                                                  AdMaterialInfo* material_info) {
  if (!material_info) {
    return false;
  }
  auto* feature = material_info->mutable_material_feature(0);

  if (style_info.material().cover_height() != 0 && style_info.material().cover_width() != 0) {
    feature->mutable_material_size()->set_height(style_info.material().cover_height());
    feature->mutable_material_size()->set_width(style_info.material().cover_width());
  } else if (style_info.creative().cover_height() != 0 && style_info.creative().cover_width() != 0) {
    feature->mutable_material_size()->set_height(style_info.creative().cover_height());
    feature->mutable_material_size()->set_width(style_info.creative().cover_width());
  }

  // style_info.material.type 取值为 0, 2
  if (style_info.material().type() == AdEnum::AdMaterialType::AdEnum_AdMaterialType_SINGLE_PAGE_MATERIAL) {
    material_info->set_material_type(style_info.material().type());
    feature->set_material_feature_type(
        AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE);
    feature->set_material_url(style_info.material().material_url());
    std::string cover_url = UniverseGetCover(style_info);
    if (!cover_url.empty()) {
      material_info->set_cover_url(cover_url);
    } else {
      material_info->set_cover_url(style_info.material().cover_url());
    }
    feature->set_rule_id(2);
    feature->set_photo_id(0);
  } else {
    material_info->set_material_type(AdEnum::AdMaterialType::AdEnum_AdMaterialType_PHOTO_MATERIAL);
    feature->set_material_feature_type(
        AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE);
    if (style_info.creative().photo_id() != 0) {
      feature->set_cover_url(UniverseGetCover(style_info));
      feature->set_photo_id(style_info.creative().photo_id());
      feature->set_material_time_duration(style_info.creative().duration() / 1000);
    } else {
      feature->set_cover_url(creative_parser.GetCoverUrl());
      feature->set_photo_id(creative_parser.GetPhotoId());
      feature->set_material_time_duration(creative_parser.GetDuration() / 1000);
      feature->mutable_material_size()->set_height(creative_parser.GetCoverSize().height());
      feature->mutable_material_size()->set_width(creative_parser.GetCoverSize().width());
    }
    if (style_info.has_creative() &&
        (style_info.creative().live_creative_type() ==
             kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT ||
         style_info.creative().live_creative_type() ==
             kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE)) {
      feature->set_photo_id(1);
    }
    feature->set_rule_id(1);
  }

  return true;
}

int32_t UniverseStylePostProc::CheckMaterialInfo(const AdMaterialInfo& material_info) {
  const auto& feature = material_info.material_feature(0);
  if (material_info.material_type() == AdEnum::AdMaterialType::AdEnum_AdMaterialType_PHOTO_MATERIAL) {
    if (feature.material_feature_type() !=
        AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE) {
      return 1;
    }
    if (feature.photo_id() == 0) {
      return 2;
    }
  }

  if (material_info.material_type() == AdEnum::AdMaterialType::AdEnum_AdMaterialType_SINGLE_PAGE_MATERIAL) {
    if (feature.material_feature_type() !=
        AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE) {
      return 3;
    }
    if (feature.photo_id() != 0) {
      return 4;
    }
  }

  if (material_info.material_type() ==
      AdEnum::AdMaterialType::AdEnum_AdMaterialType_SMALL_IMAGE_GROUP_MATERIAL) {
    if (material_info.material_feature_size() < 2) {
      return 5;
    }
  }

  return 0;
}

bool UniverseStylePostProc::SetMaterialInfoFromStyleServer(const StyleInfoItem& forward_style_info,
                                                           const CreativeParser& creative_parser,
                                                           AdResult* ad_result) {
  int ad_style = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).ad_style();
  int media_uid = session_data_->get_ad_request()->universe_ad_request_info().medium_uid();
  session_data_->dot_perf->Count(1, "front_server.ad_style_server_all_pv", absl::StrCat(ad_style));
  auto cooperation_mode = session_data_->get_ad_request()->universe_ad_request_info().cooperation_mode();

  bool endcard_flag =
      (ad_style == 2 || ad_style == 3 || ad_style == 12 ||
       (ad_style == 23 && ad_result->ad_deliver_info().ad_base_info().ad_rollout_size() == 1));
  base::Json sdk_exp_param_json(base::StringToJson(session_data_->copy_ud_sdk_exp_param()));

  auto& style_material_map = *session_data_->mutable_style_material_map();
  auto style_server_ad_set = session_data_->mutable_ud_style_server_ad_set();
  auto* base_info = ad_result->mutable_ad_deliver_info()->mutable_ad_base_info();
  base_info->clear_material_info();
  bool ret = false;
  if (style_material_map.find(base_info->creative_id()) != style_material_map.end()) {
    auto* material_info = base_info->mutable_material_info();
    auto* feature = material_info->add_material_feature();

    if (creative_parser.GetCreativeMaterialType() ==
        kuaishou::ad::AdEnum_CreativeMaterialType_SPLASH_IMAGES) {
      SetNativeMaterialInfoForSplashDark(forward_style_info, creative_parser, material_info);
    } else {
      SetNativeMaterialInfo(forward_style_info, creative_parser, material_info);
    }

    for (auto& style_info : style_material_map[base_info->creative_id()]) {
      auto fill_material = [&]() {
        material_info->set_actual_material_id(style_info.material_id);
        auto material_type = style_info.parse_field().material_type();
        material_info->set_derivative_material_type(material_type);
        feature->mutable_material_size()->set_height(style_info.parse_field().cover_height());
        feature->mutable_material_size()->set_width(style_info.parse_field().cover_width());
        feature->set_rule_id(style_info.parse_field().rule_id());
        feature->set_template_id(style_info.parse_field().template_id());
        feature->set_material_exp_tag(style_info.parse_field().exp_tag());
        feature->set_material_file_size(style_info.parse_field().video_bytes());
        // 视频素材
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_SCREEN ||
            material_type == kuaishou::ad::AdEnum::HORIZONTAL_SCREEN) {
          material_info->set_material_type(AdEnum::AdMaterialType::AdEnum_AdMaterialType_PHOTO_MATERIAL);
          feature->set_material_url(style_info.parse_field().video_url());
          feature->set_material_feature_type(
              AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE);
          feature->set_cover_url(style_info.parse_field().cover_url());
          feature->set_material_time_duration(style_info.parse_field().duration() / 1000);

          if (feature->photo_id() == 0) {
            feature->set_photo_id(1);
          }
        }

        // 横版图片素材
        if (material_type == kuaishou::ad::AdEnum::HORIZONTAL_IMAGE ||
            material_type == kuaishou::ad::AdEnum::BANNER_IMAGE ||
            material_type == kuaishou::ad::AdEnum::HORIZONTAL_SMALL_IMAGE) {
          material_info->set_material_type(
              AdEnum::AdMaterialType::AdEnum_AdMaterialType_SINGLE_PAGE_MATERIAL);  // NOLINT
          feature->set_material_url(style_info.parse_field().material_url());
          if (style_info.parse_field().compress_material_url() != "") {
            feature->set_material_url(style_info.parse_field().compress_material_url());
          }
          if (SPDM_enable_phone_material_fill(session_data_->get_spdm_ctx()) &&
              FrontKconfUtil::universePhoneMaterialFillUids()->count(media_uid) &&
              !style_info.parse_field().phone_specific_url().empty()) {
            feature->set_material_url(style_info.parse_field().phone_specific_url());
            if (material_type == kuaishou::ad::AdEnum::BANNER_IMAGE) {
              feature->mutable_material_size()->set_height(phoneSpecificBannerHeight);
            }
          }
          feature->set_photo_id(0);
          feature->set_material_feature_type(
              AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE);
        }

        // 竖版图片素材 区分合作方式
        if (material_type == kuaishou::ad::AdEnum::VERTICAL_IMAGE) {
          material_info->set_material_type(
              AdEnum::AdMaterialType::AdEnum_AdMaterialType_SINGLE_PAGE_MATERIAL);  // NOLINT
          if (FrontKconfUtil::universePhoneMaterialAllFillUids()->count(media_uid) &&
              style_info.parse_field().phone_specific_url().empty()) {
            return;
          }
          // 标准宽高
          if ((cooperation_mode == 2 || cooperation_mode == 5 || cooperation_mode == 7 ||
               cooperation_mode == 9) &&
              !FrontKconfUtil::reverseAPICooperationUids()->count(media_uid)) {
            feature->set_material_url(style_info.parse_field().cover_standard());
            if (style_info.parse_field().compress_cover_standard() != "") {
              feature->set_material_url(style_info.parse_field().compress_cover_standard());
            }
            feature->mutable_material_size()->set_height(1334);
            feature->mutable_material_size()->set_width(750);
          } else {  // 默认
            feature->set_material_url(style_info.parse_field().material_url());
            if (style_info.parse_field().compress_material_url() != "") {
              feature->set_material_url(style_info.parse_field().compress_material_url());
            }
          }
          if ((SPDM_enable_phone_material_fill(session_data_->get_spdm_ctx()) &&
               FrontKconfUtil::universePhoneMaterialFillUids()->count(media_uid) &&
               !style_info.parse_field().phone_specific_url().empty()) ||
              FrontKconfUtil::universePhoneMaterialAllFillUids()->count(media_uid)) {
            feature->set_material_url(style_info.parse_field().phone_specific_url());
            feature->mutable_material_size()->set_height(phoneSpecificVerticalHeight);
          }
          feature->set_photo_id(0);
          feature->set_material_feature_type(
              AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE);
        }

        // 横版组图素材
        if (material_type == kuaishou::ad::AdEnum::HORIZONTAL_SMALL_IMAGE_GROUP) {
          int32_t template_id_support_pic_num = 5;
          auto iter = FrontKconfUtil::templateImageCountConfig()->find(style_info.pos_template_id);
          if (iter != FrontKconfUtil::templateImageCountConfig()->end()) {
            template_id_support_pic_num = iter->second;
          }

          material_info->set_material_type(kuaishou::ad::AdEnum_AdMaterialType_SMALL_IMAGE_GROUP_MATERIAL);
          size_t pic_num =
              std::min(style_info.parse_field().picture_group_size(), template_id_support_pic_num);
          if (pic_num > 0) {
            feature->set_material_url(style_info.parse_field().picture_group(0).material_url());
            feature->set_material_time_duration(0);
            feature->set_photo_id(0);
            feature->set_material_feature_type(
                AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE);
          }
          for (size_t i = 1; i < pic_num; i++) {
            auto* next_feature = material_info->add_material_feature();
            next_feature->CopyFrom(*feature);
            next_feature->set_material_url(style_info.parse_field().picture_group(i).material_url());
          }
        }
      };

      switch (style_info.parse_field().style_material_type()) {
        case kuaishou::ad::AdEnum::MATERIAL_TYPE_NATIVE: {
          material_info->set_derivative_material_type(forward_style_info.creative().creative_material_type());
          session_data_->dot_perf->Count(1, "front_server.ad_style_server_use_native",
                                         absl::StrCat(ad_style));
          break;
        }

        case kuaishou::ad::AdEnum::MATERIAL_TYPE_FORWARD: {
          // 使用正排物料
          session_data_->dot_perf->Count(1, "front_server.ad_style_server_use_forward",
                                         absl::StrCat(ad_style));
          fill_material();
          break;
        }

        case kuaishou::ad::AdEnum::MATERIAL_TYPE_BOTTOM: {
          // 使用兜底物料
          session_data_->dot_perf->Count(1, "front_server.ad_style_server_use_bottom",
                                         absl::StrCat(ad_style));
          fill_material();
          break;
        }

        default:
          session_data_->dot_perf->Count(1, "front_server.ad_style_server_unknown_type",
                                         absl::StrCat(ad_style));
          break;
      }

      if (endcard_flag && sdk_exp_param_json.IsObject()) {
        // endcard 兼容不支持 matrix 的旧版本 sdk
        if (!style_info.endcard_content.empty()) {
          base::Json endcard_json(base::StringToJson(style_info.endcard_content));
          if (endcard_json.IsObject()) {
            sdk_exp_param_json.set("endCardNewStyle", endcard_json.GetInt("id", 0));
            session_data_->set_ud_end_card_id(endcard_json.GetInt("id", 0));
          }
        } else if (style_info.parse_field().end_card_id64() > 0) {
          sdk_exp_param_json.set("endCardNewStyle", style_info.parse_field().end_card_id64());
          session_data_->set_ud_end_card_id(style_info.parse_field().end_card_id64());
        } else if (style_info.parse_field().end_card_id() >= 0) {
          sdk_exp_param_json.set("endCardNewStyle", style_info.parse_field().end_card_id());
          session_data_->set_ud_end_card_id(style_info.parse_field().end_card_id());
        }
        session_data_->dot_perf->Count(1, "front_server.ad_style_server_end_card_count",
                                       // absl::StrCat(style_info.parse_field().end_card_id64()),
                                       absl::StrCat(ad_style));

        // playcard 迁移实验走 style_server
        if (!style_info.playcard_content.empty()) {
          session_data_->set_ud_play_card_content(style_info.playcard_content);
          session_data_->set_ud_play_card_compatible(true);
        }
        if (style_info.parse_field().play_card_id64() > 0) {
          session_data_->set_ud_play_card_new_style(absl::StrCat(style_info.parse_field().play_card_id64()));
        } else if (style_info.parse_field().play_card_id() >= 0) {
          session_data_->set_ud_play_card_new_style(absl::StrCat(style_info.parse_field().play_card_id()));
        }
        session_data_->dot_perf->Count(1, "playcard_select_pv", "style_server",
                                       absl::StrCat(style_info.parse_field().play_card_id64()));
      }

      if ((ad_style == 13 || (ad_style == 23 && base_info->ad_rollout_size() == 2)) &&
          sdk_exp_param_json.IsObject()) {
        if (style_info.interstitial_style_id >= 0) {
          sdk_exp_param_json.set("interstitialStyleId", style_info.interstitial_style_id);
          session_data_->dot_perf->Count(1, "front_server.interstitial_style_id",
                                         absl::StrCat(style_info.interstitial_style_id),
                                         absl::StrCat(ad_style));
        }
        if (style_info.interstitial_button_type >= 0) {
          sdk_exp_param_json.set("interstitialButtonType", style_info.interstitial_button_type);
          session_data_->dot_perf->Count(1, "front_server.interstitial_button_type",
                                         absl::StrCat(style_info.interstitial_button_type),
                                         absl::StrCat(ad_style));
        }
      }
      session_data_->set_ud_sdk_exp_param(base::JsonToString(sdk_exp_param_json.get()));

      // 字段校验
      int32_t check_ret = CheckMaterialInfo(*material_info);
      if (check_ret > 0) {
        session_data_->dot_perf->Count(1, "front_server.ad_style_server_material_info_error",
                                       absl::StrCat(ad_style), absl::StrCat(check_ret));
        return false;
      }

      ret = true;
      session_data_->dot_perf->Count(1, "front_server.ad_style_server_material_replace",
                                     absl::StrCat(ad_style));
      style_server_ad_set->insert(base_info->creative_id());
      break;
    }
  }

  return ret;
}

void UniverseStylePostProc::SetPackedInfo(const StyleInfoItem& style_info, kuaishou::ad::AdResult* item) {
  if (item->ad_source_type() != kuaishou::ad::DSP) {
    return;
  }
  std::map<std::string, std::string> replace_macro_map;
  int scene = style_info.unit().resource_id();
  if (GetRequestScene(style_info, engine_base::UNIVERSE_SCENE)) {
    scene = engine_base::UNIVERSE_SCENE;
  }
  replace_macro_map.insert(std::make_pair(kCSITEKeyWords, absl::StrCat(scene)));
  ad_style_mgr_.SetTransMacros(make_pair(kCSITEKeyWords, absl::StrCat(scene)));
  replace_macro_map.insert(make_pair(kAIDKeyWords, absl::StrCat(style_info.unit().id())));
  replace_macro_map.insert(make_pair(kCIDKeyWords, absl::StrCat(style_info.creative().id())));
  replace_macro_map.insert(make_pair(kDIDKeyWords, absl::StrCat(style_info.campaign().id())));
  replace_macro_map.insert(make_pair(kAccountKeyword, absl::StrCat(style_info.account().id())));
  if (style_info.campaign().name().length() > 0) {
    std::string encode_campaign_name;
    base::FastEncodeUrlComponent(style_info.campaign().name().c_str(), &encode_campaign_name);
    replace_macro_map.insert(make_pair(kDNAMEKeyWords, encode_campaign_name));
  }
  if (style_info.has_unit_small_shop_merchant_support_info() &&
      !style_info.unit_small_shop_merchant_support_info().outer_id().empty()) {
    replace_macro_map.insert(
        make_pair(kProductIdKeyWords, style_info.unit_small_shop_merchant_support_info().outer_id()));
  }
  if (style_info.unit().dpa_type() > 1 && !style_info.unit().unit_support_info().dpa_outer_id().empty()) {
    replace_macro_map.insert(
        make_pair(kProductIdKeyWords, style_info.unit().unit_support_info().dpa_outer_id()));
  }
  // rta trace request id
  if (item->ad_deliver_info().ad_base_info().rta_source_type() !=
      kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    const auto& online_join_params_transparent = item->ad_deliver_info().online_join_params_transparent();
    if (online_join_params_transparent.trace_request_id() > 0) {
      replace_macro_map.insert(
          make_pair(kTraceReqIdKeyWords, absl::StrCat(online_join_params_transparent.trace_request_id())));
      session_data_->dot_perf->Count(1, "rta_ad_url_pack_has_trace_id");
    } else {
      session_data_->dot_perf->Count(1, "rta_ad_url_pack_miss_trace_id");
    }
    replace_macro_map.insert(
        make_pair(kRtaBidKeyWords, absl::StrCat(online_join_params_transparent.rta_sta_tag())));
    replace_macro_map.insert(
        make_pair(kRtaFeatureIdKeyWords, absl::StrCat(online_join_params_transparent.rta_feature_id())));
    replace_macro_map.insert(make_pair(kRequestKeyword, absl::StrCat(session_data_->get_llsid())));
    replace_macro_map.insert(make_pair(
        kRtaBidRatio, absl::StrCat(online_join_params_transparent.first_click_score() / 1000000.0f)));
    session_data_->dot_perf->Count(
        1, "rta_ad_trace_campare",
        online_join_params_transparent.trace_request_id() == session_data_->get_llsid() ? "true" : "false",
        absl::StrCat(item->ad_deliver_info().ad_base_info().rta_source_type()));
  }
  std::string account_mul_exp = common_style_post_strategy_->GetAccountMulExpValue(style_info);
  if (!account_mul_exp.empty()) {
    replace_macro_map.insert(make_pair(kVIDKeyWords, account_mul_exp));
    ad_style_mgr_.SetTransMacros(make_pair(kVIDKeyWords, account_mul_exp));
  }

  ReplaceUrlMacro(item->mutable_ad_deliver_info()->mutable_ad_base_info()->mutable_url(), replace_macro_map);
  ReplaceUrlMacro(item->mutable_ad_deliver_info()->mutable_ad_base_info()->mutable_schema_url(),
                  replace_macro_map);
  ReplaceUrlMacro(item->mutable_ad_deliver_info()->mutable_ad_base_info()->mutable_h5_url(),
                  replace_macro_map);
  ad_style_mgr_.SetUrlReplace(item, replace_macro_map);
}

void UniverseStylePostProc::SetAdDataV2(StyleInfoItem* style_info, kuaishou::ad::AdResult* item) {
  ad_style_mgr_.Process(session_data_, item, style_info, &ad_style_mgr_params_);
  session_data_->set_curr_ad_data_v2(ad_style_mgr_.GetResultPb());
}

void UniverseStylePostProc::SetUniverseAdDataV2Fix(kuaishou::ad::AdResult* item) {
  if (true || item->ad_deliver_info().ad_base_info().ad_data_v2().size() > 0) {
    ad_style_mgr_.ProcessUniverseFix(item);
    session_data_->set_curr_ad_data_v2(ad_style_mgr_.GetResultPb());
  }
}

void UniverseStylePostProc::SetActionBarStyle(kuaishou::ad::AdResult* item) {
  // action bar style 旧样式
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  kuaishou::ad::AdEnum::ActionBarStyle action_bar_style =
      kuaishou::ad::AdEnum_ActionBarStyle_TEST_ACTIONBAR_STYLE_A;
  base_info->clear_action_bar_style();
  base_info->set_action_bar_style(action_bar_style);
  return;
}
void UniverseStylePostProc::SetAdxMaterialInfo(AdResult* item) {
  if (item->ad_source_type() != kuaishou::ad::ADX) {
    return;
  }
  auto* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  if (!base_info->has_material_info()) {
    return;
  }
  auto material_info = base_info->mutable_material_info();
  material_info->set_derivative_material_type(base_info->creative_material_type());
  auto* features = material_info->mutable_material_feature();
  for (auto& feature : *features) {
    if (feature.photo_id() != 0 &&  // 话题页默认 photo_id 为 0
        (!base_info->has_topic_type() || base_info->topic_type() == kuaishou::ad::UNKNOWN_TOPIC_TYPE)) {
      material_info->set_cover_url(base_info->cover_url());
      feature.set_cover_url(base_info->cover_url());
      feature.mutable_material_size()->set_height(base_info->cover_size().height());
      feature.mutable_material_size()->set_width(base_info->cover_size().width());
      feature.set_material_url(base_info->photo_url());
      feature.set_material_feature_type(
          AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE);
    } else {
      feature.set_photo_id(0);
      feature.set_material_feature_type(
          AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE);
    }
  }
}

void UniverseStylePostProc::SetDpaMaterialInfo(AdResult* item) {
  if (item->ad_deliver_info().ad_base_info().campaign_type() !=
      kuaishou::ad::AdEnum_CampaignType_DPA_CAMPAIGN) {
    return;
  }

  auto* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  base_info->clear_material_info();
  auto *material_info = base_info->mutable_material_info();
  auto* feature = material_info->add_material_feature();
  if (base_info->photo_id() != 0) {
    material_info->set_cover_url(base_info->cover_url());
    material_info->set_material_type(AdEnum::AdMaterialType::AdEnum_AdMaterialType_PHOTO_MATERIAL);
    feature->set_photo_id(base_info->photo_id());
    feature->set_cover_url(base_info->cover_url());
    feature->mutable_material_size()->set_height(base_info->cover_size().height());
    feature->mutable_material_size()->set_width(base_info->cover_size().width());
    feature->set_material_url(base_info->photo_url());
    feature->set_material_feature_type(
        AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE);
    feature->set_material_time_duration(base_info->photo_duration() / 1000);
  } else {
    material_info->set_material_type(AdEnum::AdMaterialType::AdEnum_AdMaterialType_SINGLE_PAGE_MATERIAL);
    feature->set_photo_id(0);
    feature->set_material_feature_type(
        AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PAGE_FEATURE);
  }

  // 激励/全屏视频播放页模版化
  int ad_style = session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() == 0
                     ? 0
                     : session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).ad_style();
  bool is_inspire = ad_style == 2 || ad_style == 12;
  bool is_full_screen = (ad_style == 3 || (ad_style == 23 && base_info->ad_rollout_size() == 1));
  if (is_inspire || is_full_screen) {
    auto universe_play_card_new_style_map = FrontKconfUtil::universePlayCardNewStyle();
    if (universe_play_card_new_style_map && universe_play_card_new_style_map->size() != 0) {
      std::set<std::string> template_ids;
      for (const auto& item : *universe_play_card_new_style_map) {
        std::string id = item.first;
        if (white_playcard_ids_.count(id) <= 0) {
          continue;
        }
        int32_t sub_id = 0;
        if (!absl::SimpleAtoi(id.substr(1, 2), &sub_id)) {
          LOG(ERROR) << "UniverseStylePostProc convert error, sub_id: " << id.substr(1, 2);
          continue;
        }
        // 模板 ID 命名规则，xaabby，aa:单数代表激励模板，双数代表全屏模板
        if (is_inspire && (sub_id & 1)) {
          template_ids.insert(id);
        }
        if (is_full_screen && !(sub_id & 1)) {
          template_ids.insert(id);
        }
      }
      if (template_ids.size() != 0) {
        auto select_index = ad_base::AdRandom::GetInt(0, template_ids.size() - 1);
        auto iter = std::next(template_ids.begin(), select_index);
        session_data_->set_ud_play_card_new_style(*iter);
      }
    }
  }
}

void UniverseStylePostProc::SetAdxBaseInfo(const StyleInfoItem& style_info, AdResult* item) {
  if (item->ad_source_type() != kuaishou::ad::ADX) {
    return;
  }
  auto* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto* deliver_info = item->mutable_ad_deliver_info();
  // 默认值是 1
  deliver_info->mutable_universe_ad_deliver_info()->set_type(1);
  base_info->set_new_creative_tag(static_cast<int>(ks::ad_server::NewCreative::NoNewMaterial));
  // 4. 是否海量创意判断
  auto is_large_amount_creative = [&]() {
    // 如果下游已经传了就不要再处理了
    if (base_info->large_amount_creative()) {
      return true;
    }
    auto adx_large_creative_list = FrontKconfUtil::adxLargeCreativeList();
    return adx_large_creative_list->find(static_cast<int64_t>(base_info->adx_source_type())) !=
           adx_large_creative_list->end();
  };
  base_info->set_large_amount_creative(is_large_amount_creative());
  // 5.  设置 tag_id
  if (base_info->adx_ext_data().length() > 0) {
    ks::Json json_ext_data(StringToJson(base_info->adx_ext_data()));
    std::string tag_id = json_ext_data.GetString("tagId", "");
    if (tag_id.length() > 0) {
      base_info->set_tag_id(tag_id);
    }
  }

  base_info->set_ad_request_times(session_data_->get_ad_request()->ad_user_session_info().ad_request_times());
  base_info->set_is_end_show(true);
  base_info->set_charge_action_type(GetAdxChargeActionType(base_info));
  if (base_info->photo_transform_type() == kuaishou::ad::AdEnum::UNKNOWN_REPLACY_TYPE) {
    base_info->set_photo_transform_type(kuaishou::ad::AdEnum::NO_REPLACE);
  }
}

void UniverseStylePostProc::SetDspBaseInfo(ContextData* context, const StyleInfoItem& style_info,
                                           const CreativeParser& creative_parser,
                                           kuaishou::ad::AdResult* item) {
  if (item->ad_source_type() != kuaishou::ad::DSP) {
    return;
  }

  // dpa 不请求正排，不走该逻辑
  if (IsDpaAd(&(item->ad_deliver_info().ad_base_info()))) {
    return;
  }

  const auto& creative = style_info.creative();
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  falcon::Inc("front_server_universe.mh_set_dsp_base_info", 1);
  base_info->set_campaign_name(style_info.campaign().name());
  base_info->set_campaign_sub_type(style_info.campaign().sub_type());
  if (base_info->speed() == static_cast<int32_t>(kuaishou::ad::AdEnum::UNKNOWN_SPEED_TYPE)) {
    base_info->set_speed(style_info.unit().speed());
  }
  base_info->set_cover_url_backup(creative_parser.GetCoverUrlBackup());
  const auto& schema_url = style_info.unit().schema_uri();
  if (!schema_url.empty()) {
    std::vector<std::string> split_url = absl::StrSplit(schema_url, "&");
    for (auto it = split_url.begin(); it != split_url.end();) {
      if ((*it).find("=kwai") != (*it).npos) {
        it = split_url.erase(it);
      } else {
        ++it;
      }
    }
    auto contain_pagefloatkey = [](const std::string& str) -> bool {
      static const std::string url_flag = "pagefloatkey";
      return (std::equal(url_flag.begin(), url_flag.end(), str.begin()));
    };
    split_url.erase(std::remove_if(split_url.begin(), split_url.end(), contain_pagefloatkey),
                    split_url.end());

    std::string url_hack = absl::StrJoin(split_url, "&");
    base_info->set_schema_url(url_hack);
  }
  if (base_info->schema_url() == style_info.unit().schema_uri()) {
    if (session_data_->get_is_ios_platform() && !style_info.unit_support_info().ulink().empty()) {
      base_info->set_schema_url(style_info.unit().unit_support_info().ulink());
      session_data_->dot_perf->Count(1, "dsp_ulink_replaced");
    }
  }
  std::string url;
  std::string orig_url = base_info->url();
  if (style_info.has_unit()) {
    const auto& unit = style_info.unit();
    if (style_info.has_app_release()) {
      session_data_->dot_perf->Count(1, "ad_app_table_url_status_check",
                                     (style_info.app_release().url().empty() ? "empty" : "not_empty"));
    }
    if (style_info.has_app_release() && !style_info.app_release().url().empty()) {
      url = style_info.app_release().url();
    } else {
      url = unit.uri();
    }
    if (style_info.campaign().type() == kuaishou::ad::AdEnum::SITE_PAGE ||
        base_info->campaign_type() == kuaishou::ad::AdEnum::AD_CID) {
      url = unit.uri();
    }
    if (unit.web_uri_type() == kuaishou::ad::AdEnum_UnitWebUriType_PROGRAMMING_LANDING_PAGE_TYPE) {
      if (session_data_->get_style_material_land_page_map().count(base_info->creative_id()) > 0) {
        const auto& style_row =
            session_data_->get_style_material_land_page_map().at(base_info->creative_id());
        const auto& style_info = style_row.first;
        const auto& magic_site_page_info = style_row.second;
        const ks::Json style_content(StringToJson(style_info.style_content()));
        const std::string style_url = style_content.GetString("url");
        if (!style_url.empty()) {
          url = style_url;
          base_info->set_pg_lp_page_id(style_info.material_id());
          base_info->set_pg_lp_page_group_id(unit.unit_support_info().group_id());
          session_data_->dot_perf->Count(1, "prog_landing_page_use_select_url", "universe");
        }
        if (VLOG_IS_ON(123)) {
          LOG(INFO) << "Landing page url of " << base_info->creative_id() << " is " << url;
        }
        if (style_info.is_site()) {
          base_info->set_is_site(style_info.is_site());
          base_info->set_site_id(style_info.site_id());
          if (magic_site_page_info.id() == style_info.site_id()) {
            base_info->set_magic_site_page_type(magic_site_page_info.magic_site_page_type());
            base_info->set_page_source_type(magic_site_page_info.page_source_type());
            session_data_->dot_perf->Count(1, "site_ad_fill_site_info", "universe",
                                           "prog_landing_page_select");
          } else {
            session_data_->dot_perf->Count(1, "site_ad_miss_site_info", "universe",
                                           "prog_landing_page_select");
          }
        }
      } else {
        // 使用兜底 url， https://docs.corp.kuaishou.com/d/home/<USER>
        url = unit.uri();
        base_info->set_pg_lp_page_id(unit.site_id());
        base_info->set_pg_lp_page_group_id(unit.unit_support_info().group_id());
        session_data_->dot_perf->Count(1, "prog_landing_page_use_default_url", "universe");
      }
    }
    if (!base_info->is_site()) {
      if (style_info.has_creative()) {
        if (style_info.creative().site_id() != 0 && style_info.has_magic_site_page() &&
            style_info.magic_site_page().id() == style_info.creative().site_id()) {
          base_info->set_is_site(style_info.creative().is_site());
          base_info->set_site_id(style_info.creative().site_id());
          const auto& magic_site_page_info = style_info.magic_site_page();
          base_info->set_magic_site_page_type(magic_site_page_info.magic_site_page_type());
          base_info->set_page_source_type(magic_site_page_info.page_source_type());
          session_data_->dot_perf->Count(1, "site_ad_fill_site_info", "universe", "landing_page_creative");
        }
      }
    }
    if (!base_info->is_site() && unit.is_site()) {
      base_info->set_is_site(unit.is_site());
      base_info->set_site_id(unit.site_id());
      if (style_info.has_magic_site_page() && style_info.magic_site_page().id() == unit.site_id()) {
        const auto& magic_site_page_info = style_info.magic_site_page();
        base_info->set_magic_site_page_type(magic_site_page_info.magic_site_page_type());
        base_info->set_page_source_type(magic_site_page_info.page_source_type());
        session_data_->dot_perf->Count(1, "site_ad_fill_site_info", "universe", "landing_page_unit");
      } else {
        session_data_->dot_perf->Count(1, "site_ad_miss_site_info", "universe", "landing_page_unit");
      }
    }
    base_info->set_original_landing_page_url(url);
  }
  // 判断是否为联盟内循环图文物料
  bool is_esp_image =
      (style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
       style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) &&
      (creative.creative_material_type() == kuaishou::ad::AdEnum::VERTICAL_IMAGE ||
       creative.creative_material_type() == kuaishou::ad::AdEnum::HORIZONTAL_IMAGE);
  if (style_info.has_campaign() &&
      style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE) {
    const auto conf_ptr = FrontKconfUtil::UniverseMerchantKSAppInfo();
    for (const auto& node : conf_ptr->data().app_list()) {
      if (session_data_->get_ud_app_set().count(node.app_pkg()) > 0) {
        // 未安装 app 时，兜底下载逻辑
        base_info->set_app_name(node.app_name());
        base_info->set_package_name(node.app_pkg());
      }
    }
  }
  // 联盟小店通添加 schemaUrl
  if (style_info.has_campaign() &&
      (style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
       style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE)
      && style_info.unit().ocpx_action_type() != kuaishou::ad::CID_ROAS
      && style_info.unit().ocpx_action_type() != kuaishou::ad::CID_EVENT_ORDER_PAID) {
    bool is_merchant_reco =
        style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE;
    if (style_info.account().user_id() <= 0) {
      TLOG_EVERY_N(ERROR, 1000) << "MERCHANT_RECO_PROMOTE user_id <=0 create_id : "
                                << style_info.creative().id()
                                << " account_id: " << style_info.account().user_id();
      session_data_->dot_perf->Count(1, "front_server_universe_merchant_no_user_id_count");
    }

    const auto conf_ptr = FrontKconfUtil::UniverseMerchantKSAppInfo();
    for (const auto& node : conf_ptr->data().app_list()) {
      if (is_merchant_reco && session_data_->get_ad_request()->ad_user_info().is_universe_fake_user()) {
        break;
      }
      if (session_data_->get_ud_app_set().count(node.app_pkg()) > 0) {
        // 未安装 app 时，兜底下载逻辑
        base_info->set_app_name(node.app_name());
        base_info->set_package_name(node.app_pkg());
        // 此处在之后统一填充。
        url = node.app_url();
        std::string sch_url;
        std::string orig_exp_tag;
        if (is_esp_image) {
          orig_exp_tag = GetServerExpTagForUniverseEspImage(style_info, session_data_->mutable_ad_request(),
                                                            session_data_->get_front_server_request()->type(),
                                                            session_data_->get_llsid());
        } else {
          orig_exp_tag = GetServerExpTagv2(style_info, session_data_->mutable_ad_request(),
                                           session_data_->get_front_server_request()->type(),
                                           session_data_->get_llsid());
        }
        if (orig_exp_tag.empty()) {
          TLOG_EVERY_N(ERROR, 1000) << "MERCHANT_RECO_PROMOTE exp_tag is empty";
          session_data_->dot_perf->Count(1, "front_server_universe_merchant_goods_no_exp_tag_count");
        }

        // 填充付费模型
        auto enable_ad_storewide_roas_fix_landing_page_url =
            style_info.unit().ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS;
        if (style_info.has_unit() && style_info.has_unit_small_shop_merchant_support_info() &&
                (style_info.unit().ocpx_action_type() == kuaishou::ad::EVENT_GOODS_VIEW ||
                 style_info.unit().ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
                 style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                 style_info.unit().ocpx_action_type() == kuaishou::ad::AD_FANS_TOP_ROI) ||
            enable_ad_storewide_roas_fix_landing_page_url) {
          session_data_->dot_perf->Count(1, "front_server_universe_merchant_goods_play_total_count");
          std::string landing_page_url = absl::Substitute(
              node.goods_landing_page_url(), style_info.unit_small_shop_merchant_support_info().item_id());
          std::string encode_landing_page_url;
          absl::StrAppend(&landing_page_url, "&serverExpTag=", orig_exp_tag);
          base::FastEncodeUrlComponent(landing_page_url.c_str(), &encode_landing_page_url);
          std::string replaced_landing_url =
              base::StringReplace(encode_landing_page_url, "%5F%5FCALLBACK%5F%5F", "__CALLBACK__", true);
          sch_url = node.goods_app_schema() + replaced_landing_url + node.goods_schema_params();
          // TODO(wanglei10): deeplink 填充逻辑重构
        } else if (style_info.has_unit() && style_info.has_unit_small_shop_merchant_support_info() &&
                   style_info.unit().ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK) {
          std::string encode_exp_tag;
          base::FastEncodeUrlComponent(orig_exp_tag.c_str(), &encode_exp_tag);
          sch_url = absl::StrCat(node.app_schema(), style_info.account().user_id(), "?");
          absl::StrAppend(&sch_url, "serverExpTag=", encode_exp_tag);
        } else {
          // deeplink 逻辑
          std::string encode_exp_tag;
          base::FastEncodeUrlComponent(orig_exp_tag.c_str(), &encode_exp_tag);
          sch_url = node.app_schema() + absl::StrCat(base_info->author_id()) + node.schema_params();

          absl::StrAppend(&sch_url, "&serverExpTag=", encode_exp_tag);
          LOG_EVERY_N(INFO, 10000) << "universe merchant exptag sch_url: " << sch_url;
          session_data_->dot_perf->Count(1, "front_server_universe_merchant_play_total_count");
        }
        base_info->set_schema_url(sch_url);
        // 复用金牛 deeplink 开关 走填充下载链接逻辑
        base_info->set_is_universe_jinniu_to_deeplink(true);
        break;
      }
    }
  }
  // 联盟建站支持快应用投放
  if (style_info.campaign().type() == kuaishou::ad::AdEnum::SITE_PAGE ||
      base_info->campaign_type() == kuaishou::ad::AdEnum::AD_CID) {
    // 快应用直跳
    if (style_info.magic_site_page().direct_call_type() == 2) {
      session_data_->dot_perf->Count(1, "magic_site_page_direct_call", "universe");
      base_info->set_schema_url(style_info.magic_site_page().quick_app_url());
    }
  }
  if (base_info->campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE) {
    bool is_only_nebula = session_data_->get_ud_app_set().count("com.smile.gifmaker") <= 0 &&
                          session_data_->get_ud_app_set().count("com.kuaishou.nebula") > 0;
    if (is_only_nebula) {
      base_info->set_schema_url(absl::StrCat("ksnebula", base_info->schema_url().substr(4)));
    }
  }

  // 安全连接需要透传，暂时无法迁移
  if (style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_APP && !orig_url.empty()) {
    url = orig_url;
  }
  base_info->set_url(url);
  base_info->set_cover_url(creative_parser.GetCoverUrl());
  base_info->set_screenshots(creative.screenshots());
  if (!IsProgrammaticCreative(creative)) {
    base_info->set_material_id(creative.material_id());
  }

  base_info->set_display_info(creative.display_info());
  base_info->set_contrast_display_info(creative.display_info());

  const auto& unit = style_info.unit();
  base_info->set_unit_type(unit.unit_type());
  if (style_info.has_app_release()) {
    const auto& app_release = style_info.app_release();
    base_info->set_app_name(app_release.app_name());
    base_info->set_package_name(app_release.package_name());
    base_info->set_app_icon_url(app_release.app_icon_url());
    base_info->set_use_sdk(app_release.use_sdk());
  } else {
    base::Json json(base::StringToJson(unit.diverse_data()));
    if (json.IsObject()) {
      base_info->set_app_name(json.GetString("appName", ""));
      base_info->set_package_name(json.GetString("appPackageName", ""));
      if (style_info.campaign().type() == kuaishou::ad::AdEnum::APP_ADVANCE ||
          style_info.campaign().type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) {
        base_info->set_ad_app_id(json.GetInt("appId", 0));
      }
    }
    base_info->set_app_icon_url(unit.app_icon_url());
  }

  // 联盟流量金牛电商广告特殊处理
  if ((style_info.unit().ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
       style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) &&
      style_info.unit().convert_id() == 0 && static_cast<int32>(style_info.unit().taobao_url_type()) == 4) {
    int32_t jinniu_branch_test = 6;  // 仅 h5
    const auto& universe_jinniu_ab_config = FrontKconfUtil::universeJinniuAbConfig()->data().ab_map();
    for (const auto& config : universe_jinniu_ab_config) {
      if (session_data_->get_spdm_ctx().TryGetBoolean(config.first, false)) {
        jinniu_branch_test |= (1 << config.second);
      }
    }
    session_data_->set_jinniu_branch_test(jinniu_branch_test);
    for (auto& node : ks::ad_base::kconf::universeJinniuConfig()->universe_jinniu_config_list) {
      if (node.app_name == "金牛H5配置") {
        std::string channel =
            GetRequestScene(style_info, engine_base::UNIVERSE_SCENE) ? "102101103" : "102101104";
        kuaishou::ad::AdJinniuDeviceInfo deviceInfo;
        deviceInfo.set_user_id(session_data_->get_ad_request()->ad_user_info().id());
        deviceInfo.set_app_id(ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())));
        deviceInfo.set_locale(session_data_->get_ad_request()->ad_user_info().region());
        deviceInfo.set_channel(channel);  // 渠道信息，新标准
        deviceInfo.set_channel_type("1");
        deviceInfo.set_channel_attr(ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())));

        if (session_data_->get_ad_request()->ad_user_info().device_info().size() > 0) {
          const auto& oneDevice = session_data_->get_ad_request()->ad_user_info().device_info().Get(0);
          deviceInfo.set_manufacturer(oneDevice.brand());
          deviceInfo.set_model(oneDevice.device_mod());
          deviceInfo.set_system_version(oneDevice.os_version());
          deviceInfo.set_idfa(oneDevice.idfa());
          deviceInfo.set_imei(oneDevice.imei());
          deviceInfo.set_android_id(oneDevice.android_id());
          deviceInfo.set_uuid(oneDevice.device_id());
        }

        std::string device_info_string;
        std::string encode_device_info_string;
        bool code = deviceInfo.SerializeToString(&device_info_string);

        serving_base::AesCrypter aes_crypter;
        base::URLSafeBase64Encode(aes_crypter.Encrypt(device_info_string), &encode_device_info_string);
        if (KS_UNLIKELY(encode_device_info_string.empty())) {
          falcon::Inc("ad_server.jinniu_device_info_encode_failed", 1);
        }

        std::string url = base::StringReplace(style_info.unit().uri(), "layoutType=1", "layoutType=4", true) +
                          base::StringReplace(
                              base::StringReplace(
                                  base::StringReplace(
                                      node.add_param_v2, "__APPID__",
                                      ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())), true),
                                  "__CHANNEL__", channel, true),
                              "__DEVICEINFO__", encode_device_info_string, true);
        url = base::StringReplace(url, "__OUTSIDE__", "1", true);
        url = base::StringReplace(url, "__ABFLAG__", absl::StrCat(session_data_->get_jinniu_branch_test()),
                                  true);

        base_info->set_url(url + node.schema_add_param);
        base_info->set_is_universe_jinniu_to_deeplink(false);
        falcon::Inc("ad_server.universe_jinniu_to_h5");
        break;
      }
    }
  }
  if (style_info.unit().app_download_type() == 1) {
    base_info->set_url(style_info.unit().uri());
  }
  // 直营电商样式优化-商品图片素材补充
  if (style_info.campaign().type() != kuaishou::ad::AdEnum::APP &&
      style_info.has_card_show_data()) {
    if (base_info->app_icon_url().empty()) {
      base_info->set_app_icon_url(style_info.card_show_data().picture());
    }
  }
  if (base_info->topic_type() != kuaishou::ad::UNKNOWN_TOPIC_TYPE) {
    base_info->set_topic_type(unit.topic_type());
    base_info->set_topic_content(unit.topic_content());
    base_info->set_main_title(creative.main_title());
    base_info->set_sub_title(creative.sub_title());
    base_info->set_topic_icon_url(creative.topic_icon_url());
    base_info->set_show_ad_tag(creative.show_ad_tag());
  }

  if ((base_info->campaign_type() == kuaishou::ad::AdEnum_CampaignType_SITE_PAGE ||
       base_info->campaign_type() == kuaishou::ad::AdEnum::AD_CID) &&
      !unit.uri().empty()) {
    base_info->set_h5_url(unit.uri());
  } else if (!creative.download_page_url().empty()) {
    base_info->set_h5_url(creative.download_page_url());
  }
  if (base_info->campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      creative.creative_style_type() == kuaishou::ad::AdEnum_TrolleyType_YELLOW_TROLLEY) {
    base_info->set_trolley_type(kuaishou::ad::AdEnum_TrolleyType_YELLOW_TROLLEY);
  }

  const auto& account = style_info.account();
  if (account.product_name().length() > 0) {
    ad_base::TargetKeyConvertor conv_;
    base_info->set_product_id(abs(conv_(account.product_name())));
    base_info->set_product_name(account.product_name());
  }

  // 依据营业执照编号更改广告标
  if (!account.license_no().empty()) {
    auto license_adslogo_map = FrontKconfUtil::licenseAdslogoMap();
    auto license_adslogo_map_iter = license_adslogo_map->find(account.license_no());
    if (license_adslogo_map_iter != license_adslogo_map->end() &&
        license_adslogo_map_iter->second.size() > 0) {
      session_data_->dot_perf->Count(1, "licenseAdslogo");
      base_info->set_dsp_source_description(license_adslogo_map_iter->second);
      base_info->set_adx_source_description(license_adslogo_map_iter->second);
    }
  }

  merchant_util::SetMerchantDspBaseInfo(session_data_, style_info, base_info);
  MerchantAddLiveEventTime(base_info, style_info);
  MerchantMarketingAddFlags(base_info, style_info, session_data_, item->ad_deliver_info().ad_queue_type());
  merchant_style_strategy_->SetDspBaseInfo(base_info, style_info);
  bool enable_follow_for_wx = style_info.has_unit() &&
                              (style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
                               style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW_FAST);
  // 联盟小店作品推广添加跳转小程序信息
  if (style_info.has_campaign() &&
      style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      style_info.has_unit() && style_info.has_unit_small_shop_merchant_support_info() &&
      (style_info.unit().ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
       style_info.unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS || enable_follow_for_wx)) {
    session_data_->set_media_wx_small_app_id(session_data_->copy_ud_wechat_app_id());
  }
  // 联盟外循环支持跳转微信小程序
  if (style_info.has_unit() &&
      style_info.unit().web_uri_type() == kuaishou::ad::AdEnum_UnitWebUriType_WX_PAGE) {
    session_data_->set_media_wx_small_app_id(session_data_->copy_ud_wechat_app_id());
  }
  // 判断短视频引流广告是否可以展示直播流
  if (!ks::front_server::UniverseData::GetStyleComponentFlag(session_data_) &&
      base_info->campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE &&
      base_info->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE &&
      CheckEnableP2liveToLiveStream(*base_info)) {
    session_data_->mutable_ud_p2live_to_live_cids()->emplace(base_info->creative_id());
  }
  // 判断小游戏广告是否可以直跳: 媒体白名单 && ab && 产品白名单 && deep_link_url 非空 && 小游戏组件
  bool exchange_direct_jump_url{false};
  exchange_direct_jump_url =
      SPDM_enable_small_app_direct_jump(session_data_->get_spdm_ctx()) && style_info.has_magic_site_page() &&
      FrontKconfUtil::universeSmallAppDirectConfig()->data().Hit(
          item->ad_deliver_info().ad_base_info().product_name(), style_info.unit().ocpx_action_type(),
          item->ad_deliver_info().ad_base_info().bid_type()) &&
      !session_data_->borrow_ud_wechat_app_id().empty() &&
      !style_info.magic_site_page().deep_link_url().empty() &&
      (style_info.magic_site_page().direct_call_type() == 1 ||
       IsSmallGameComponent(style_info.magic_site_page().page_component()) ||
       IsSmallGameOldComponent(style_info.magic_site_page().page_component()));
  if (exchange_direct_jump_url) {
    session_data_->mutable_force_direct_call_cids()->insert(style_info.creative().id());
  }
}

void UniverseStylePostProc::SetDynamicCreative(const StyleInfoItem& style_info,
                                               const CreativeParser&  /*creative_parser*/,
                                               kuaishou::ad::AdResult* item, FillApiExtData* fill_api_data) {
  if (item->ad_source_type() != kuaishou::ad::DSP) {
    return;
  }
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  base_info->mutable_dynamic_creative_info()->clear_wpt();
  base_info->clear_dynamic_creative();
  base_info->clear_dynamic_package_list();
  // 动态创意
  DynamicCreativeStrategy dcs;
  if (!style_info.creative().extra_cover_data().empty()) {
    ks::Json extra_cover_data(ks::StringToJson(style_info.creative().extra_cover_data()));
    dcs.FillJson(extra_cover_data, *(session_data_->get_ad_request()), session_data_);
    if (dcs.is_dynamic_creative_) {
      base_info->mutable_dynamic_creative_info()->CopyFrom(dcs.GetDynamicCreativeTypes());
      base_info->set_dynamic_creative(true);
    }
  }
  falcon::Inc("front_server.mh_set_dynamic_creative_2", 1);
  fill_api_data->AddJson("cover", dcs.cover_value_);
  // 动态创意词包
  if (dcs.compose_type_ == 2 && !style_info.creative().extra_cover_data().empty()) {
    base_info->set_dynamic_package_list(dcs.cover_value_.get("dynamicPackageList", "").asString());
  }
}

void UniverseStylePostProc::SetBaseDynamicCreative(kuaishou::ad::AdResult* item) {
  if (item->ad_source_type() != kuaishou::ad::DSP && item->ad_source_type() != kuaishou::ad::ADX) {
    return;
  }
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  const std::string& display_info_str = base_info->display_info();
  if (display_info_str.empty()) {
    return;
  }
  base::Json display_info_json(base::StringToJson(display_info_str));
  if (!display_info_json.IsObject()) {
    LOG(WARNING) << "display_info_str[" << display_info_str << "] is not json";
    return;
  }

  std::string description_str = display_info_json.GetString("description", "");
  if (!description_str.empty()) {
    DynamicCreativeStrategy dcs;
    std::string new_description_str =
        dcs.SloganReplace(*(session_data_->get_ad_request()), session_data_, description_str);
    if (!new_description_str.empty()) {
      display_info_json.set("description", new_description_str);
    }
  }
  base_info->set_display_info(display_info_json.ToString());
}

void UniverseStylePostProc::FillApiData(kuaishou::ad::AdResult* item, FillApiExtData* fill_api_data) {
  if (item->ad_source_type() != kuaishou::ad::DSP) {
    return;
  }
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  fill_api_data->AddJson("useTrackType", ::Json::Int(0));
  if (base_info->action_bar_style() == kuaishou::ad::AdEnum_ActionBarStyle_TEST_ACTIONBAR_STYLE_G) {
    base_info->set_action_bar_style(kuaishou::ad::AdEnum_ActionBarStyle_TEST_ACTIONBAR_STYLE_A);
  } else if (base_info->action_bar_style() == kuaishou::ad::AdEnum_ActionBarStyle_TEST_ACTIONBAR_STYLE_H) {
    ::Json::Int animation_type(2);
    fill_api_data->AddJson("animationType", animation_type);
    fill_api_data->AddJson("displayInfo", "立即预约");
  }
  base_info->set_ad_data(fill_api_data->GetExtDataString());
}

void UniverseStylePostProc::SetSlideInfo(kuaishou::ad::AdResult* item) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  base_info->clear_action_bar_load_time();
  base_info->clear_item_hide_label();
  base_info->clear_label_style();
  base_info->set_action_bar_load_time(3000);
  base_info->set_item_hide_label(false);
  base_info->set_label_style(0);
}

void UniverseStylePostProc::SetActionBarColor(const StyleInfoItem& style_info, kuaishou::ad::AdResult* item) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  std::string color;
  if (!base_info->app_name().empty()) {
    base_info->set_new_style(true);
  }

  const auto& campaign = style_info.campaign();
  // 直播直投兜底
  int32_t ad_style = session_data_->get_ud_ad_style();
  if (ad_style == 7 && base_info->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
    base_info->set_actionbar_color("FE3666");
  }
  bool is_download =
      (campaign.type() == kuaishou::ad::AdEnum::APP || campaign.type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN);
  const auto& photo_status = style_info.photo_status();
  if (photo_status.ByteSize() <= 0) {
    return;
  }
  if (is_download) {
    if (photo_status.main_color().size() == 6) {
      bool succ = action_bar::GetNearestColorBySpaceDist(photo_status.main_color(), &color);
      if (succ) {
        base_info->set_actionbar_color(color);
      }
    }
  }
  const auto& parse_field = photo_status.parse_field();
  if (parse_field.top5_color_size() == 0) {
    return;
  }
  base_info->set_new_style(true);
  base_info->set_actionbar_color(action_bar::DefaultColor(is_download));
  for (const auto& item : parse_field.top5_color()) {
    bool succ = action_bar::ProcessExp(item.r(), item.g(), item.b(), item.ratio(), is_download, &color);
    if (succ) {
      base_info->set_actionbar_color(color);
      break;
    }
  }
}

#define STR(x) #x
#define CHARGE_TYPE_FALLBACK(BASE_INFO, CHARGE_INFO, PERF_NAME)  {                          \
  if (BASE_INFO.universe_cpm_boost_tag()== 1 || BASE_INFO.universe_cpa_boost_tag() == 1) {  \
    if (CHARGE_INFO.charge_type() != CallbackChargeType::kUniverseManualBid1) {             \
      if (CHARGE_INFO.charge_type() != CallbackChargeType::kAccColdStart &&                 \
          CHARGE_INFO.charge_type() != CallbackChargeType::kRtaBidRatio) {                  \
        CHARGE_INFO.set_charge_type(CallbackChargeType::kUniverseManualBid1);               \
        session_data_->dot_perf->Count(1, STR(PERF_NAME##_set_charge_type),                 \
                                        absl::StrCat(CHARGE_INFO.charge_type()),            \
                                        absl::StrCat(BASE_INFO.universe_cpm_boost_tag()),   \
                                        absl::StrCat(BASE_INFO.universe_cpa_boost_tag()));  \
      }                                                                                     \
      session_data_->dot_perf->Count(1, STR(PERF_NAME##_charge_type_error),                 \
                                      absl::StrCat(CHARGE_INFO.charge_type()),              \
                                      absl::StrCat(BASE_INFO.universe_cpm_boost_tag()),     \
                                      absl::StrCat(BASE_INFO.universe_cpa_boost_tag()));    \
    }                                                                                       \
  } else {                                                                                  \
    if (CHARGE_INFO.charge_type() == CallbackChargeType::kUniverseManualBid1) {             \
      session_data_->dot_perf->Count(1, STR(PERF_NAME##_charge_type_error),                 \
                                      absl::StrCat(CHARGE_INFO.charge_type()),              \
                                      absl::StrCat(BASE_INFO.universe_cpm_boost_tag()),     \
                                      absl::StrCat(BASE_INFO.universe_cpa_boost_tag()));    \
    }                                                                                       \
  }                                                                                         \
}

void UniverseStylePostProc::SetCallbackPassback(const StyleInfoItem& style_info, kuaishou::ad::AdResult* item,
                                                RankAdCommon* ad_common) {
  const kuaishou::ad::AdDeliverInfo& ad_deliver_info = item->ad_deliver_info();
  const kuaishou::ad::UniverseAdDeliverInfo& universe_ad_deliver_info =
      ad_deliver_info.universe_ad_deliver_info();
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  std::string callback_passback_json_str;
  kuaishou::ad::AdBaseInfo::AdCallbackPassback callback_passback;
  callback_passback.set_charge_action_type(base_info->charge_action_type());
  callback_passback.set_price(ad_deliver_info.price());
  callback_passback.set_bid_type(base_info->bid_type());
  callback_passback.set_ad_source_type(item->ad_source_type());
  // Todo: 从正排服务中获取(trace util)
  callback_passback.set_convert_id(style_info.trace_util().id());
  callback_passback.set_convert_type(style_info.trace_util().type());
  callback_passback.set_bid_server_group_idx(item->target_bid().group_idx());
  callback_passback.set_ocpc_action_type(base_info->ocpc_action_type());
  falcon::Inc("front_server.mh_set_callback_passback", 1);
  callback_passback.set_item_type(base_info->item_type());
  if (callback_passback.price() < 0) {
    callback_passback.set_price(0);
  }
  callback_passback.set_media_price(ad_deliver_info.media_price());
  bool ali_exclude_flag = false;

  const auto* p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result) {
    if (p_rank_result->ad_rank_trans_info().ali_exclude_flag() == 2) {  // NOLINT
      ali_exclude_flag = true;
    }
  }

  bool rta_exclude_flag = false;
  if (item->ad_deliver_info().ad_base_info().rta_source_type() !=
      kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    rta_exclude_flag = item->ad_deliver_info().ad_base_info().rta_info().is_dynamic_bidding();
  }

  if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::unit_acc_cold_start ||
      FrontKconfUtil::testAccExploreChargeInfo()) {
    callback_passback.set_charge_type(CallbackChargeType::kAccColdStart);
    falcon::Inc("front_server.acc_explore_callback_hit");
  } else if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::unit_recovery_start) {
    callback_passback.set_charge_type(CallbackChargeType::kRecoveryStart);  // 表示一键复苏
  } else if (ali_exclude_flag &&
             base_info->campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE) {
    callback_passback.set_charge_type(CallbackChargeType::kAliDeepCvr);
  } else if (rta_exclude_flag) {
    callback_passback.set_charge_type(CallbackChargeType::kRtaBidRatio);
    session_data_->dot_perf->Count(1, "rta_set_callback_passback");
  } else if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::ecpc) {
    callback_passback.set_charge_type(CallbackChargeType::kEcpc);
  } else if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::ocpm2_min_cost) {
    callback_passback.set_charge_type(CallbackChargeType::ocpm2MinCost);
  } else if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::universe_manual_bid_1) {
    callback_passback.set_charge_type(CallbackChargeType::kUniverseManualBid1);
  } else if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::universe_manual_bid_2) {
    callback_passback.set_charge_type(CallbackChargeType::kUniverseManualBid2);
  }
  auto& base_info_obj = *base_info;
  CHARGE_TYPE_FALLBACK(base_info_obj, callback_passback, callback_passback);
  // 联盟单独处理
  bool quick_search_is_hit_white = false;  // 是否命中快投白名单
  if (engine_base::AdKconfUtil::universeSearchDarkConfig()) {
    auto search_black_data_config = engine_base::AdKconfUtil::universeSearchDarkConfig()->data();
    quick_search_is_hit_white = search_black_data_config.QuickSearchIsHitWhite(
        style_info.creative().account_id(), style_info.unit().ocpx_action_type());
  }

  bool is_skip_search_dark_ad_flow =
      SPDM_enable_distribut_flow_skip_search_ad_dark(session_data_->get_spdm_ctx()) &&
      FrontKconfUtil::universeStoreDistributSkipDarkPosId()->count(session_data_->get_ud_pos_id());
  bool is_skip_all_dark_ad_flow =
      SPDM_enable_universe_store_distribut_skip_dark(session_data_->get_spdm_ctx()) &&
      FrontKconfUtil::universeSkipAllDarkAdPosId()->count(session_data_->get_ud_pos_id());
  if (GetRequestScene(style_info, engine_base::UNIVERSE_SCENE)) {
    callback_passback.set_ad_scene(kuaishou::ad::AdEnum::UNIVERSE_SCENE);
  } else if ((is_skip_search_dark_ad_flow || is_skip_all_dark_ad_flow) &&
             (GetRequestScene(style_info, engine_base::SEARCH_SCENE) ||
              (ad_common->is_non_universe_ads() && style_info.unit_support_info().quick_search() == 1))) {
    callback_passback.set_ad_scene(kuaishou::ad::AdEnum::SEARCH_SCENE);  // 搜索快投 & 直投 x 厂商分发流量
  } else if (SPDM_universe_record_quick_search_budget(session_data_->get_spdm_ctx()) &&
             !session_data_->get_ud_is_universe_tiny_flow() &&
             (quick_search_is_hit_white && ad_common->is_non_universe_ads() &&
              style_info.unit_support_info().quick_search() == 1)) {
    callback_passback.set_ad_scene(kuaishou::ad::AdEnum::SEARCH_SCENE);  // 快投 x 非厂商
  } else if (GetRequestScene(style_info, engine_base::COVER_FEED_SCENE) &&
             !GetRequestScene(style_info, engine_base::THONAS_SCENE)) {
    callback_passback.set_ad_scene(kuaishou::ad::AdEnum::FEED_SCENE);
  } else if ((GetRequestScene(style_info, engine_base::SEARCH_SCENE) ||
              (!session_data_->borrow_ud_search_query().empty() &&
               (session_data_->get_ud_ad_style() == 19 || session_data_->get_ud_ad_style() == 20) &&
               (!session_data_->get_universe_tiny_search_black_list()->IsHit(
                   style_info.creative().account_id(), style_info.unit().ocpx_action_type(),
                   style_info.unit().deep_conversion_type(), style_info.account().product_name())) &&
               ad_common->is_non_universe_ads() && style_info.unit_support_info().quick_search() == 1))) {
    callback_passback.set_ad_scene(kuaishou::ad::AdEnum::SEARCH_SCENE);
  } else {
    if (GetRequestScene(style_info, engine_base::INSPIRE_SCENE)) {
      callback_passback.set_ad_scene(kuaishou::ad::AdEnum::INSPIRE_SCENE);
    } else if (GetRequestScene(style_info, engine_base::SPLASH_SCENE)) {
      callback_passback.set_ad_scene(kuaishou::ad::AdEnum::SPLASH_SCENE);
    } else {
      callback_passback.set_ad_scene(kuaishou::ad::AdEnum::UP_DOWN_SLIDING);
    }
  }
  bool is_pt_union = false;
  if (base_info->adx_ext_data().length() > 0) {
    ks::Json json_ext_data(StringToJson(base_info->adx_ext_data()));
    is_pt_union = json_ext_data.GetBoolean("isPtUnion", false);
  }
  if ((item->ad_source_type() != kuaishou::ad::ADX &&
       GetRequestScene(style_info, engine_base::UNIVERSE_SCENE, item)) ||
      (item->ad_source_type() == kuaishou::ad::ADX && is_pt_union)) {
    // 只有联盟明投才设置这个参数为联盟 用于报表区分
    callback_passback.set_placement_type(::kuaishou::ad::AdEnum_PlacementType_PT_UNION);
  } else {
    callback_passback.set_placement_type(::kuaishou::ad::AdEnum_PlacementType_PT_KWAI);
  }

  callback_passback.set_pos_id(universe_ad_deliver_info.pos_id());
  callback_passback.set_adx_source_type(base_info->adx_source_type());
  callback_passback.set_reco_client_browse_type(
      session_data_->get_ad_request()->reco_request_info().browse_type());
  callback_passback.set_virtual_creative_id(base_info->creative_id());
  if (style_info.has_unit()) {
    callback_passback.set_unit_type(style_info.unit().unit_type());
  }
  if (IsDpaAd(base_info)) {
    // dpa 不请求正排，从 base_info 里面取 unit_type
    callback_passback.set_unit_type(base_info->unit_type());
  }

  callback_passback.set_cover_id(base_info->cover_id());
  callback_passback.set_creative_material_type(base_info->creative_material_type());
  // 多端适配生成创意，填充原始创意相关 id
  if (base_info->derived_exchange()) {
    callback_passback.set_derived_exchange(base_info->derived_exchange());
    callback_passback.set_derived_creative_id(base_info->derived_creative_id());
    callback_passback.set_derived_photo_id(base_info->derived_photo_id());
    callback_passback.set_derived_cover_id(base_info->derived_cover_id());
    callback_passback.set_derived_material_id(base_info->derived_material_id());
  }

  callback_passback_json_str.clear();
  ::google::protobuf::util::MessageToJsonString(callback_passback, &callback_passback_json_str);
  TLOG_EVERY_N(INFO, FrontKconfUtil::logReqDetailFrequency())
      << "callback json str: " << callback_passback_json_str;

  base_info->set_version(1);
  base_info->mutable_ad_callback_passback()->CopyFrom(callback_passback);
  base_info->set_callback_passback(callback_passback_json_str);
  base_info->clear_callback_passback();
}

std::string UniverseStylePostProc::UniverseGetCover(const StyleInfoItem& style_info) {
  if (!style_info.has_creative()) {
    return "";
  }
  int32_t compose_type = 0;
  std::string bgUrl;
  base::Json extra_cover_data(base::StringToJson(style_info.creative().extra_cover_data()));
  if (extra_cover_data.IsObject()) {
    compose_type = extra_cover_data.GetInt("composeType", 0);
    bgUrl = extra_cover_data.GetString("bgUrl");
  }
  if ((compose_type == 1 || compose_type == 2) && !bgUrl.empty()) {
    return bgUrl;
  }
  if (!style_info.creative().cover_url_frontend().empty()) {
    // 中台流量可能无法解析 pkg 格式，使用广告主上传的原图
    return style_info.creative().cover_url_frontend();
  }
  return "";
}

bool UniverseStylePostProc::SetMaterialInfo(const StyleInfoItem& style_info,
                                            const CreativeParser& creative_parser, AdResult* ad_result) {
  if (session_data_->get_ud_is_universe_tiny_flow()) {
    return true;
  }
  if (ad_result->ad_source_type() != kuaishou::ad::DSP) {
    return true;
  }
  // dpa 不请求正排，不走该逻辑
  if (IsDpaAd(&(ad_result->ad_deliver_info().ad_base_info()))) {
    return true;
  }
  // 创意优选， 客户端样式参数迁移到这里
  std::string sdk_exp_param =
      "{\"drawActionBarTimes\":\"2,1,2\",\"videoBlackAreaClick\":0,\"videoBlackAreaNewStyle\":0,"
      "\"showVideoAtH5\":0,\"endCardNewStyle\":2, \"endCardNewStyleBackgroundColor\":\"rgba(0,0,0,0.7)\", "
      "\"endCardNewStyleBlur\":10}";  // NOLINT

  std::string sdk_version = session_data_->UniverseRequest().sdk_version();
  static const std::string lowest_sdk_version = "3.2.0";
  bool valid_sdk_version = engine_base::CompareAppVersion(sdk_version, lowest_sdk_version) >= 0;
  bool is_ads_sdk = (session_data_->UniverseRequest().sdk_type() == 1);
  if (valid_sdk_version && is_ads_sdk) {
    sdk_exp_param =
        "{\"drawActionBarTimes\":\"2,1,2\",\"videoBlackAreaClick\":0,\"videoBlackAreaNewStyle\":0,"
        "\"showVideoAtH5\":0,\"endCardNewStyle\":2,\"endCardNewStyleBackgroundColor\":\"rgba(0,0,0,0.7)\","
        "\"endCardNewStyleBlur\":10}";  // NOLINT
  }
  base::Json sdk_exp_param_json(base::StringToJson(sdk_exp_param));
  const auto ad_style = session_data_->get_ud_ad_style();

  // 插屏样式替换实验
  if ((ad_style == 13 ||
       (ad_style == 23 && ad_result->ad_deliver_info().ad_base_info().ad_rollout_size() == 2))) {
    // 实验 key 类似 interstitial_style_id_str:1:2:4 切割出每个:后面的值入存入容器
    std::vector<std::string> interstitial_style_ids = absl::StrSplit("0:5:6", ":");
    auto capacity = interstitial_style_ids.size();
    int64_t interstitial_style_id = 0;
    if (capacity) {
      int64_t interstitial_style_id_index = ad_base::AdRandom::GetInt(0, capacity - 1);
      base::StringToInt64(interstitial_style_ids[interstitial_style_id_index], &interstitial_style_id);
    }
    if (sdk_exp_param_json.IsObject() && interstitial_style_id != 0) {
      sdk_exp_param_json.set("interstitialStyleId", interstitial_style_id);
      // 随机选择按钮类型
      int64_t button_type = ad_base::AdRandom::GetInt(0, 3);
      sdk_exp_param_json.set("interstitialButtonType", button_type);
    }
  }

  sdk_exp_param = base::JsonToString(sdk_exp_param_json.get());
  session_data_->set_ud_sdk_exp_param(sdk_exp_param);

  // 激励/全屏视频播放页模版化
  bool is_inspire = ad_style == 2 || ad_style == 12;
  bool is_full_screen =
      (ad_style == 3 ||
       (ad_style == 23 && ad_result->ad_deliver_info().ad_base_info().ad_rollout_size() == 1));
  if (is_inspire || is_full_screen) {
    auto universe_play_card_new_style_map = FrontKconfUtil::universePlayCardNewStyle();
    if (universe_play_card_new_style_map && universe_play_card_new_style_map->size() != 0) {
      std::set<std::string> template_ids;
      for (const auto& item : *universe_play_card_new_style_map) {
        std::string id = item.first;
        int32_t sub_id = 0;
        if (!absl::SimpleAtoi(id.substr(1, 2), &sub_id)) {
          LOG(ERROR) << "UniverseStylePostProc convert error, sub_id: " << id.substr(1, 2);
          continue;
        }
        // 模板 ID 命名规则，xaabby，aa:单数代表激励模板，双数代表全屏模板
        if (is_inspire && (sub_id & 1)) {
          template_ids.insert(id);
        }
        if (is_full_screen && !(sub_id & 1)) {
          template_ids.insert(id);
        }
      }
      if (template_ids.size() != 0) {
        auto select_index = ad_base::AdRandom::GetInt(0, template_ids.size() - 1);
        auto iter = std::next(template_ids.begin(), select_index);
        session_data_->set_ud_play_card_new_style(*iter);
      }
    }
  }

  if (session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() == 0) {
    return true;
  }
  return SetMaterialInfoFromStyleServer(style_info, creative_parser, ad_result);
}

void UniverseStylePostProc::SetTrackInfo(const StyleInfoItem& style_info, AdResult* item,
                                         RankAdCommon* ad_common) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  item->mutable_ad_deliver_info()->clear_track();
  if (item->ad_source_type() == kuaishou::ad::DSP &&
      base_info->campaign_type() != kuaishou::ad::AdEnum_CampaignType_DPA_CAMPAIGN) {
    DSPCommonTrack(style_info, item);
    falcon::Inc("frontserver_universe.set_track_info_for_dsp");
  } else if (item->ad_source_type() == kuaishou::ad::ADX) {
    ADXCommonTrack(style_info, item);
    falcon::Inc("frontserver_universe.set_track_info_for_adx");
  } else if (base_info->campaign_type() == kuaishou::ad::AdEnum_CampaignType_DPA_CAMPAIGN) {
    if (IsDpaAd(base_info)) {
      item->mutable_ad_deliver_info()->mutable_track()->CopyFrom(
          item->ad_deliver_info().ad_base_info().dpa_style_info().dpa_tracks());
    } else {
      DSPCommonTrack(style_info, item);
    }
    falcon::Inc("frontserver.set_track_info_for_dpa");
  }
  if (item->ad_deliver_info().ad_base_info().rta_source_type() !=
      kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    auto* track = item->mutable_ad_deliver_info()->mutable_track();
    for (auto & iter : *track) {
      if (absl::StrContains(iter.url(), kRequestKeyword)) {
        std::string new_url;
        base::FastStringReplace(iter.url(), kRequestKeyword, absl::StrCat(session_data_->get_llsid()), true,
                                &new_url);
        TLOG_EVERY_N(INFO, 1000) << "rta_url: " << iter.url() << ". rta_new_url: " << new_url;
        iter.set_url(new_url);
      }
      if (absl::StrContains(iter.url(), kRtaCpaValidKeyword)) {
        std::string new_url;
        int32 flag = 4;
        if (ad_common) {
          auto* p_rank_result = ad_common->GetRankResult();
          if (p_rank_result && (p_rank_result->rank_made_info().multi_touch_score() > 0 ||
                                p_rank_result->rank_made_info().rta_second_direct_bid() > 0)) {
            flag = 260;
            LOG_EVERY_N(INFO, 1000) << "ReplaceRtaStatus multi_touch_score: "
                                    << p_rank_result->rank_made_info().multi_touch_score()  // NOLINT
                                    << " rta_second_direct_bid: "
                                    << p_rank_result->rank_made_info().rta_second_direct_bid();
          }
        }
        base::FastStringReplace(iter.url(), kRtaCpaValidKeyword, absl::StrCat(flag), true, &new_url);
        LOG_EVERY_N(INFO, 1000) << "ReplaceRtaStatus rta_url: " << iter.url() << ". new_url: " << new_url;
        iter.set_url(new_url);
      }
    }
  }
}

void UniverseStylePostProc::DSPCommonTrack(const StyleInfoItem& style_info, AdResult* item) {
  std::string impression_url = style_info.creative().impression_url();
  std::string click_url = style_info.creative().click_url();
  std::string actionbar_click_url = style_info.creative().actionbar_click_url();
  std::string photo_50_impression_1s_url = style_info.creative().photo_p50_impression_t1s_url();
  std::string live_track_url = style_info.creative().live_track_url();
  std::string ad_photo_played_t3s_url;
  std::string play_t3s_url = style_info.creative().ad_photo_played_t3s_url();  // 新增 3s 监测链接
  std::string play_t10s_url = style_info.creative().creative_support_info().ad_photo_played_t10s_url();
  std::string play_end_url = style_info.creative().creative_support_info().ad_photo_played_finish_url();

  bool hit_old_3s_monitor_whitelist =
      style_info.unit().resource_id() == 5
          ? FrontKconfUtil::universeOld3sMonitorWhitelistDirect()->count(style_info.creative().account_id())
          : FrontKconfUtil::universeOld3sMonitorWhitelistNonDirect()->count(
                style_info.creative().account_id());

  std::map<std::string, std::string> replace_macro_map;
  int scene = style_info.unit().resource_id();
  if (FrontKconfUtil::enableRequestSceneForTrack()) {
    if (GetRequestScene(style_info, engine_base::UNIVERSE_SCENE)) {
      scene = engine_base::UNIVERSE_SCENE;
    }
  }

  replace_macro_map.insert(std::make_pair(kAccountKeyword, absl::StrCat(style_info.creative().account_id())));
  replace_macro_map.insert(std::make_pair(kCSITEKeyWords, absl::StrCat(scene)));
  ad_style_mgr_.SetTransMacros(make_pair(kCSITEKeyWords, absl::StrCat(scene)));
  replace_macro_map.insert(std::make_pair(kAIDKeyWords, absl::StrCat(style_info.creative().unit_id())));
  replace_macro_map.insert(std::make_pair(kCIDKeyWords, absl::StrCat(style_info.creative().id())));
  replace_macro_map.insert(std::make_pair(kDIDKeyWords, absl::StrCat(style_info.creative().campaign_id())));

  std::string account_mul_exp = common_style_post_strategy_->GetAccountMulExpValue(style_info);
  if (!account_mul_exp.empty()) {
    replace_macro_map.insert(make_pair(kVIDKeyWords, account_mul_exp));
    ad_style_mgr_.SetTransMacros(make_pair(kVIDKeyWords, account_mul_exp));
  }

  std::string encode_campaign_name;
  base::FastEncodeUrlComponent(style_info.campaign().name().c_str(), &encode_campaign_name);
  replace_macro_map.insert(make_pair(kDNAMEKeyWords, encode_campaign_name));

  // __BRAND__ : 手机品牌，取不到品牌信息时填充 other
  const auto& ad_user_info = session_data_->get_ad_request()->ad_user_info();
  std::string brand = ad_user_info.device_info_size() > 0 ? ad_user_info.device_info(0).brand() : "other";
  std::string encode_brand;
  base::FastEncodeUrlComponent(brand.c_str(), &encode_brand);
  replace_macro_map.insert(std::make_pair(kBrandIdKeyword, encode_brand));

  // __MEDIA_INDUSTRY__ : 媒体一级行业 2.0 ID （旧媒体行业对于新接入媒体只填 0 须改用 2.0 版）
  replace_macro_map.insert(std::make_pair(
      kMediaIndustryKeyword,
      absl::StrCat(session_data_->get_ad_request()->universe_ad_request_info().medium_industry_id_v2())));
  if (style_info.has_unit_small_shop_merchant_support_info() &&
      !style_info.unit_small_shop_merchant_support_info().outer_id().empty()) {
    replace_macro_map.insert(
        make_pair(kProductIdKeyWords, style_info.unit_small_shop_merchant_support_info().outer_id()));
  }
  if (style_info.unit().dpa_type() > 1 && !style_info.unit().unit_support_info().dpa_outer_id().empty()) {
    replace_macro_map.insert(
        make_pair(kProductIdKeyWords, style_info.unit().unit_support_info().dpa_outer_id()));
  }

  base::Json json_obj(base::StringToJson(style_info.unit().unit_support_info().adv_card_ids()));
  int is_advance_creative = 0;
  if (json_obj.IsObject() && json_obj.IsArray() && json_obj.size() > 0) {
    is_advance_creative = 1;
  }
  replace_macro_map.insert(std::make_pair(kACCREATIVEKeywords, absl::StrCat(is_advance_creative)));
  session_data_->dot_perf->Count(1, "advance_creative_micro_replace", absl::StrCat(is_advance_creative));
  // rta trace request id
  if (item->ad_deliver_info().ad_base_info().rta_source_type() !=
      kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    const auto& online_join_params_transparent = item->ad_deliver_info().online_join_params_transparent();
    if (online_join_params_transparent.trace_request_id() > 0) {
      replace_macro_map.insert(
          make_pair(kTraceReqIdKeyWords, absl::StrCat(online_join_params_transparent.trace_request_id())));
      session_data_->dot_perf->Count(1, "rta_ad_track_url_pack_has_trace_id");
    } else {
      session_data_->dot_perf->Count(1, "rta_ad_track_url_pack_miss_trace_id");
    }
    replace_macro_map.insert(make_pair(kRtaBidKeyWords,
          absl::StrCat(online_join_params_transparent.rta_sta_tag())));
    replace_macro_map.insert(
        make_pair(kRtaBidKeyWords, absl::StrCat(online_join_params_transparent.rta_sta_tag())));
    replace_macro_map.insert(
        make_pair(kRtaFeatureIdKeyWords, absl::StrCat(online_join_params_transparent.rta_feature_id())));
    replace_macro_map.insert(make_pair(kRequestKeyword, absl::StrCat(session_data_->get_llsid())));
    replace_macro_map.insert(make_pair(
        kRtaBidRatio, absl::StrCat(online_join_params_transparent.first_click_score() / 1000000.0f)));
    session_data_->dot_perf->Count(
        1, "rta_ad_trace_campare",
        online_join_params_transparent.trace_request_id() == session_data_->get_llsid() ? "true" : "false",
        absl::StrCat(item->ad_deliver_info().ad_base_info().rta_source_type()));
  }

  ReplaceUrlMacro(&impression_url, replace_macro_map);
  ReplaceUrlMacro(&click_url, replace_macro_map);
  ReplaceUrlMacro(&actionbar_click_url, replace_macro_map);
  ReplaceUrlMacro(&photo_50_impression_1s_url, replace_macro_map);
  ReplaceUrlMacro(&ad_photo_played_t3s_url, replace_macro_map);
  ReplaceUrlMacro(&live_track_url, replace_macro_map);
  ReplaceUrlMacro(&play_t3s_url, replace_macro_map);
  ReplaceUrlMacro(&play_t10s_url, replace_macro_map);
  ReplaceUrlMacro(&play_end_url, replace_macro_map);

  const auto& impression_report_click = FrontKconfUtil::universeImpressionReportClickConfig()->data();
  int64_t account_id = style_info.creative().account_id();
  const std::string& product_name = style_info.account().product_name();
  const std::string& app_id = session_data_->copy_ud_app_id();
  int64_t pos_id = session_data_->get_ud_pos_id();
  bool hit_impression_report_click = impression_report_click.Hit(product_name, account_id, app_id, pos_id);
  AddTrack(item, kuaishou::ad::AD_PHOTO_IMPRESSION, Tracking::AddCallbackParam(impression_url),
           Tracking::GetTrackOperationType(impression_url, account_id));
  if (!hit_old_3s_monitor_whitelist) {
    AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(click_url),
             Tracking::GetTrackOperationType(click_url, account_id));
  }
  if (hit_impression_report_click) {
    AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(actionbar_click_url),
           Tracking::GetTrackOperationType(actionbar_click_url, account_id));
  } else {
    AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(actionbar_click_url),
            Tracking::GetTrackOperationType(actionbar_click_url, account_id));
  }
  AddTrack(item, kuaishou::ad::AD_PHOTO_50_IMPRESSION_1S,
           Tracking::AddCallbackParam(photo_50_impression_1s_url),
           Tracking::GetTrackOperationType(photo_50_impression_1s_url, account_id));
  AddTrack(item, kuaishou::ad::AD_PHOTO_PLAYED_3S, Tracking::AddCallbackParam(ad_photo_played_t3s_url),
           Tracking::GetTrackOperationType(ad_photo_played_t3s_url, account_id));
  if (style_info.campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
    AddTrack(item, kuaishou::ad::AD_LIVE_IMPRESSION, Tracking::AddCallbackParam(live_track_url),
             Tracking::GetTrackOperationType(live_track_url, account_id));
  }

  // 3s 监测链接，10s 监测链接，完播链接
  bool is_api = session_data_->get_front_server_request()->universe_request().api_coop();
  if (is_api) {
    bool multi_track_flag =
        FrontKconfUtil::universeImpressionMultiTrackWhiteList()->data().Hit(product_name, account_id, 1);
    if (hit_old_3s_monitor_whitelist) {
      AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(click_url),
               Tracking::GetTrackOperationType(click_url, account_id));
      if (multi_track_flag) {
        AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(click_url),
                 Tracking::GetTrackOperationType(click_url, account_id));
      }
    } else {
      AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(play_t3s_url),
               Tracking::GetTrackOperationType(play_t3s_url, account_id));
      AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(play_t10s_url),
               Tracking::GetTrackOperationType(play_t10s_url, account_id));
      AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(play_end_url),
               Tracking::GetTrackOperationType(play_end_url, account_id));
      if ((multi_track_flag || (SPDM_enablePlay3sToImpression() && hit_impression_report_click))
          && !play_t3s_url.empty()) {
        AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(play_t3s_url),
                 Tracking::GetTrackOperationType(play_t3s_url, account_id));
      }
    }
  } else {
    // SDK 接入
    auto material_info = item->ad_deliver_info().ad_base_info().material_info();
    if (material_info.material_feature_size() > 0 &&
        material_info.material_feature(0).material_feature_type() ==
            AdEnum::AdMaterialFeatureType::AdEnum_AdMaterialFeatureType_PHOTO_FEATURE) {
      // 视频素材
      bool multi_track_flag =
          FrontKconfUtil::universeImpressionMultiTrackWhiteList()->data().Hit(product_name, account_id, 3);
      if (hit_old_3s_monitor_whitelist) {
        AddTrack(item, kuaishou::ad::AD_PHOTO_PLAYED_NS, Tracking::AddCallbackParam(click_url),
                 Tracking::GetTrackOperationType(click_url, account_id));
        AddTrack(item, kuaishou::ad::AD_PHOTO_PLAYED_3S, Tracking::AddCallbackParam(click_url),
                 Tracking::GetTrackOperationType(click_url, account_id));
        if (multi_track_flag) {
          AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(click_url),
                   Tracking::GetTrackOperationType(click_url, account_id));
        }
      } else {
        AddTrack(item, kuaishou::ad::AD_PHOTO_PLAYED_NS, Tracking::AddCallbackParam(play_t3s_url),
                 Tracking::GetTrackOperationType(play_t3s_url, account_id));
        AddTrack(item, kuaishou::ad::AD_PHOTO_PLAYED_3S, Tracking::AddCallbackParam(play_t3s_url),
                 Tracking::GetTrackOperationType(play_t3s_url, account_id));
        AddTrack(item, kuaishou::ad::AD_PHOTO_PLAYED_NS, Tracking::AddCallbackParam(play_t10s_url),
                 Tracking::GetTrackOperationType(play_t10s_url, account_id));
        AddTrack(item, kuaishou::ad::AD_PHOTO_PLAYED_10S, Tracking::AddCallbackParam(play_t10s_url),
                 Tracking::GetTrackOperationType(play_t10s_url, account_id));
        AddTrack(item, kuaishou::ad::AD_ITEM_PLAY_END, Tracking::AddCallbackParam(play_end_url),
                 Tracking::GetTrackOperationType(play_end_url, account_id));
        if (multi_track_flag && !play_t3s_url.empty()) {
          AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(play_t3s_url),
                   Tracking::GetTrackOperationType(play_t3s_url, account_id));
        }
      }
    } else {
      // 图文素材
      bool multi_track_flag =
          FrontKconfUtil::universeImpressionMultiTrackWhiteList()->data().Hit(product_name, account_id, 2);
      if (hit_old_3s_monitor_whitelist) {
        AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(click_url),
                 Tracking::GetTrackOperationType(click_url, account_id));
        if (multi_track_flag) {
          AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(click_url),
                   Tracking::GetTrackOperationType(click_url, account_id));
        }
      } else {
        AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(play_t3s_url),
                 Tracking::GetTrackOperationType(play_t3s_url, account_id));
        AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(play_t10s_url),
                 Tracking::GetTrackOperationType(play_t10s_url, account_id));
        AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(play_end_url),
                 Tracking::GetTrackOperationType(play_end_url, account_id));
        if (multi_track_flag && !play_t3s_url.empty()) {
          AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(play_t3s_url),
                   Tracking::GetTrackOperationType(play_t3s_url, account_id));
        }
      }
    }
  }

  // 增加非电商直播推广检测链接
  if ((IsNonMerchantPhotoToLiveStream(style_info) || IsNonMerchantLiveStream(style_info))) {
    AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(live_track_url),
             Tracking::GetTrackOperationType(live_track_url, account_id));
  }
  if (!hit_old_3s_monitor_whitelist && GetRequestScene(style_info, engine_base::SPLASH_SCENE)) {
    item->mutable_ad_deliver_info()->clear_track();
    AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(impression_url),
             Tracking::GetTrackOperationType(impression_url, account_id));
    AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(click_url),
             Tracking::GetTrackOperationType(click_url, account_id));
    return;
  }
  // 第三方监测实验，作用范围：广告主在白名单中的联盟直投视频广告
  if (!GetRequestScene(style_info, engine_base::UNIVERSE_SCENE)) {  // 非联盟直投广告不实验
    return;
  }
  if (item->ad_deliver_info().ad_base_info().material_info().material_type() !=
      AdEnum::AdMaterialType::AdEnum_AdMaterialType_PHOTO_MATERIAL) {
    return;  // 非视频广告实验
  }
  auto white_list = *(FrontKconfUtil::UniverseP3sTrackUrlWhiteAccount());
  // 在白名单中的 account 才进行实验
  if (white_list.find(style_info.creative().account_id()) != white_list.end()) {
    item->mutable_ad_deliver_info()->clear_track();
    if (hit_impression_report_click) {
      AddTrack(item, kuaishou::ad::AD_ITEM_IMPRESSION, Tracking::AddCallbackParam(actionbar_click_url),
               Tracking::GetTrackOperationType(actionbar_click_url, account_id));
    } else {
      AddTrack(item, kuaishou::ad::AD_ITEM_CLICK, Tracking::AddCallbackParam(actionbar_click_url),
              Tracking::GetTrackOperationType(actionbar_click_url, account_id));
    }
    AddTrack(item, kuaishou::ad::AD_PHOTO_PLAYED_NS, Tracking::AddCallbackParam(actionbar_click_url),
             Tracking::GetTrackOperationType(actionbar_click_url, account_id));
  }
}

void UniverseStylePostProc::AddTrack(AdResult* item, const kuaishou::ad::AdActionType& type,
                                     const std::string& url,
                                     const kuaishou::ad::AdTrackInfo::TrackUrlOperationType& operation_type) {
  if (url.compare("") > 0) {
    auto* adTrackInfo = item->mutable_ad_deliver_info()->add_track();
    adTrackInfo->set_type(type);
    adTrackInfo->set_url(url);
    adTrackInfo->set_track_url_operation_type(operation_type);
  }
}

bool UniverseStylePostProc::GetRequestScene(const StyleInfoItem& style_info,
                                            ks::engine_base::EngineRequestScene scene, AdResult* ad_result) {
  std::unordered_set<int64_t> resource_ids;
  const kuaishou::ad::tables::Unit* p_unit = &(style_info.unit());
  base::Json resource_json(StringToJson(p_unit->resource_ids()));
  if (resource_json.IsArray()) {
    for (auto* item : resource_json.array()) {
      int64_t id = item->IntValue(-1);
      if (id > 0) {
        resource_ids.insert(id);
      }
      // 联盟优选为明投
      if (id == engine_base::UNIVERSE_OPT_SCENE) {
        resource_ids.insert(engine_base::UNIVERSE_SCENE);
      }
    }
  }
  if (p_unit->resource_id() == scene || ad_base::stl::HasKey(scene, resource_ids)) {
    return true;
  }
  return false;
}

void UniverseStylePostProc::ADXCommonTrack(const StyleInfoItem& style_info, AdResult* item) {
  auto adx_source_type = item->ad_deliver_info().ad_base_info().adx_source_type();
  std::string price_string;
  FormatADXPrice(*item, &price_string);
  bool enable_replace_caid = false;
  base::JsonArray json_array;
  base::JsonObject json_obj;
  if (session_data_->get_ad_request()->ad_user_info().device_info_size() > 0 &&
      !session_data_->get_ad_request()->ad_user_info().device_info(0).current_caid().empty()) {
    enable_replace_caid = true;
    auto device_info = session_data_->get_ad_request()->ad_user_info().device_info(0);
    json_obj.set("kenyid", device_info.current_caid());
    json_obj.set("kenyid_MD5", MD5String(device_info.current_caid()));
    json_obj.set("version", atoi(device_info.current_caid_version().c_str()));
    json_array.append(json_obj);
    json_obj.set("kenyid", device_info.last_caid());
    json_obj.set("kenyid_MD5", MD5String(device_info.last_caid()));
    json_obj.set("version", atoi(device_info.last_caid_version().c_str()));
    json_array.append(json_obj);
  }
  for (const auto& ad_common_track : item->ad_deliver_info().ad_base_info().adx_style_info().adx_tracks()) {
    if (ad_common_track.url().compare("") > 0) {
      auto* adTrackInfo = item->mutable_ad_deliver_info()->add_track();
      adTrackInfo->set_type(ad_common_track.type());
      adTrackInfo->set_track_url_operation_type(ad_common_track.track_url_operation_type());
      std::string new_url;
      if (adx_source_type == kuaishou::ad::ADX_GDT_DSP) {
        base::FastStringReplace(ad_common_track.url(), gdtWinPriceKeywords, price_string, true, &new_url);
      } else {
        base::FastStringReplace(ad_common_track.url(), kWinPriceKeywords, price_string, true, &new_url);
      }
      if (enable_replace_caid) {
        std::string temp_url = new_url;
        new_url.clear();
        base::FastStringReplace(temp_url, kCaidKeywords, json_array.ToString(), true, &new_url);
      }
      adTrackInfo->set_url(new_url);
      adTrackInfo->set_adx_win_notice(ad_common_track.adx_win_notice());
    }
  }
  std::string win_notice_url = item->ad_deliver_info().ad_base_info().adx_style_info().win_notice_url();
  if (!(win_notice_url.empty())) {
    std::string new_win_notice_url{};
    if (adx_source_type == kuaishou::ad::ADX_GDT_DSP) {
      base::FastStringReplace(win_notice_url, gdtWinPriceKeywords, price_string, true, &new_win_notice_url);
    } else {
      base::FastStringReplace(win_notice_url, kWinPriceKeywords, price_string, true, &new_win_notice_url);
    }
    item->mutable_ad_deliver_info()->set_win_notice_url(new_win_notice_url);
  }
}

bool UniverseStylePostProc::MinimumAppVersion(const std::string& min_main_app_version,
                                              const std::string& app_version) const {
  return engine_base::CompareAppVersion(app_version, min_main_app_version) >= 0;
}

void UniverseStylePostProc::BuildAdLog(const kuaishou::ad::AdRequest& request,
                                       const StyleInfoItem& style_info, AdResult* item,
                                       const RankAdCommon* ad_common) {
  serving_base::AesCrypter aes_crypter;
  // 以下针对中台流量构建 adlog， 只根据 chargerserver 保留计费关键字段
  // 参见 https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=197261463
  auto* mutable_ad_log = item->mutable_ad_log();
  mutable_ad_log->set_device_id(request.ad_user_info().device_id());
  mutable_ad_log->set_app_ver(request.ad_user_info().platform_version());
  mutable_ad_log->set_network(request.ad_user_info().network());
  if (request.ad_user_info().device_info_size() > 0) {
    mutable_ad_log->set_photo_model(request.ad_user_info().device_info(0).brand());
  }
  if (request.ad_user_info().platform() == "android") {
    mutable_ad_log->set_client_id(2);
  } else if (request.ad_user_info().platform() == "ios") {
    mutable_ad_log->set_client_id(1);
  } else if (request.ad_user_info().platform() == "harmony" && SPDM_enableHarmonyClientId()) {
    // client_id 对应 device_os_id，鸿蒙和安卓保持一致，用 originial_platform 和安卓区分开
    mutable_ad_log->set_client_id(2);
    mutable_ad_log->set_original_platform(session_data_->UniverseRequest().device_info().origin_kpf());
    session_data_->dot_perf->Count(1, "front_server.harmony_client_id");
  }

  const auto& ad_deliver_info = item->ad_deliver_info();
  const auto& ad_base_info = item->ad_deliver_info().ad_base_info();
  auto *mutable_client_ad_log = mutable_ad_log->mutable_client_ad_log();

  auto* mutable_hit_tag = mutable_ad_log->mutable_server_delivery_info()->mutable_target_hit_tag();
  mutable_hit_tag->set_region_adcode(ad_deliver_info.target_hit_tag().region_adcode());
  mutable_hit_tag->set_region(request.ad_user_info().region());
  // 如果是 feed 场景暗投到联盟需要 openapi mock photoclick 埋点
  if (ad_base_info.ad_callback_passback().ad_scene() == kuaishou::ad::AdEnum::FEED_SCENE &&
      ad_base_info.ad_callback_passback().placement_type() == kuaishou::ad::AdEnum::PT_KWAI) {
    mutable_ad_log->set_universe_need_mock_photo_click(true);
  }

  if (request.has_ad_user_info() && request.ad_user_info().device_info_size() > 0) {
    mutable_client_ad_log->set_imei(request.ad_user_info().device_info(0).imei());
    mutable_client_ad_log->set_idfa(request.ad_user_info().device_info(0).idfa());
    mutable_client_ad_log->set_oaid(request.ad_user_info().device_info(0).oaid());
    mutable_client_ad_log->set_android_id(request.ad_user_info().device_info(0).android_id());
  }
  mutable_client_ad_log->set_creative_id(ad_base_info.creative_id());
  mutable_client_ad_log->set_llsid(session_data_->get_llsid());
  if (SPDM_enableCheckBudgetDeliveryType() && style_info.campaign().budget_delivery_type() == 1) {
    mutable_client_ad_log->set_source_type(kuaishou::ad::DSP);
  } else {
    mutable_client_ad_log->set_source_type(item->ad_source_type());
  }
  mutable_client_ad_log->set_photo_id(ad_base_info.photo_id());
  mutable_client_ad_log->set_author_id(ad_base_info.author_id());
  kuaishou::ad::AdDataV2 ad_data_v2;
  std::string ad_data_v2_json_str = ad_base_info.ad_data_v2();
  base::Json ext_json{base::StringToJson("{}")};
  if (::google::protobuf::util::JsonStringToMessage(ad_data_v2_json_str, &ad_data_v2).ok() &&
      ad_data_v2.has_live_stream_id() && ad_data_v2.live_stream_id().size() > 0) {
    ext_json.set("liveStreamId", ad_data_v2.live_stream_id());
  }
  ext_json.set("ad_style", session_data_->get_ud_ad_style());
  bool is_phone_traffic =
      FrontKconfUtil::universePhoneUids()->count(request.universe_ad_request_info().medium_uid());  // NOLINT
  bool is_universe_tiny = session_data_->get_ud_request_universe_tiny();
  UniverseDetailedTraffic universe_detailed_traffic;
  switch (session_data_->get_sub_page_id()) {
    case 19000001:
      universe_detailed_traffic = static_cast<UniverseDetailedTraffic>(is_phone_traffic ? 2 : 1);
      break;
    case 19000002:
      universe_detailed_traffic = static_cast<UniverseDetailedTraffic>(2 + (is_phone_traffic ? 2 : 1));
      break;
    case 19000003:
      universe_detailed_traffic = static_cast<UniverseDetailedTraffic>(4 + (is_phone_traffic ? 2 : 1));
      break;
    default:
      universe_detailed_traffic = static_cast<UniverseDetailedTraffic>(is_phone_traffic ? 2 : 1);
      break;
  }
  ext_json.set("universe_detailed_traffic", universe_detailed_traffic);
  if (ad_base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_KWAI_SERIAL_PROMOTION &&
      kwai_serial_opcxs.count(ad_base_info.ocpc_action_type())) {
    ext_json.set("promotionUserId", static_cast<int64_t>(ad_base_info.author_id()));
  }
  // 首位广告标记
  if (session_data_->get_ud_carousel_admit() && ad_common && ad_common->get_is_universe_carousel_first_ad()) {
    ext_json.set("is_universe_carousel_top_ad", true);
  }
  mutable_client_ad_log->set_ext_data(base::JsonToString(ext_json.get()));
  auto *mutable_universe_client_ad_log = mutable_client_ad_log->mutable_universe_client_ad_log();
  auto *mutable_client_params = mutable_client_ad_log->mutable_client_params();
  mutable_universe_client_ad_log->set_app_id(ad_deliver_info.universe_ad_deliver_info().app_id());
  mutable_universe_client_ad_log->set_medium_uid(request.universe_ad_request_info().medium_uid());
  mutable_universe_client_ad_log->set_page_id(ad_deliver_info.universe_ad_deliver_info().page_id());
  auto sub_page_id = is_universe_tiny ? 19000002 : ad_deliver_info.universe_ad_deliver_info().sub_page_id();
  mutable_universe_client_ad_log->set_sub_page_id(sub_page_id);
  mutable_universe_client_ad_log->set_type(ad_deliver_info.universe_ad_deliver_info().type());
  mutable_universe_client_ad_log->set_pos_id(ad_deliver_info.universe_ad_deliver_info().pos_id());
  mutable_universe_client_ad_log->set_vistor_id(request.universe_ad_request_info().real_user_id());
  mutable_universe_client_ad_log->set_cooperation_mode(request.universe_ad_request_info().cooperation_mode());
  mutable_universe_client_ad_log->set_sdk_version(session_data_->UniverseRequest().sdk_version());
  mutable_universe_client_ad_log->set_sdk_type(session_data_->UniverseRequest().sdk_type());

  if (is_universe_tiny && SPDM_enableUniverseTinyLogQueryType()) {
    const auto* p_rank_result = GetAdRankResult(session_data_, *item);
    if (p_rank_result) {
      mutable_client_params->set_universe_query_type(
          p_rank_result->ad_rank_trans_info().universe_tiny_query_type());
    }
    mutable_client_params->set_search_query(session_data_->copy_ud_search_query());
  }

  if ((ad_base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) &&
      ad_base_info.item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
    mutable_universe_client_ad_log->set_is_live_stream_cut_ad(true);
    mutable_client_params->set_live_room_pattern(kuaishou::ad::SIMPLIFIED_LIVE_ROOM_PATTERN);
  }
  if (ad_base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
      ad_base_info.item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
    mutable_universe_client_ad_log->set_is_universe_outer_live_ad(true);
    mutable_client_params->set_live_room_pattern(kuaishou::ad::SIMPLIFIED_LIVE_ROOM_PATTERN);
  }
  if (session_data_->get_ud_p2live_to_live_cids().count(ad_base_info.creative_id())) {
    mutable_universe_client_ad_log->set_is_live_stream_cut_ad(true);
    mutable_client_params->set_live_room_pattern(kuaishou::ad::SIMPLIFIED_LIVE_ROOM_PATTERN);
  }
  mutable_client_params->mutable_universe_operation_datas()->CopyFrom(
      ad_deliver_info.universe_operation_datas());
  if (!session_data_->UniverseRequest().device_info().client_device_id().empty()) {
    mutable_universe_client_ad_log->mutable_request_device_info()->set_device_id(
        session_data_->UniverseRequest().device_info().client_device_id());
  }
  if (ad_base_info.has_material_info() && ad_base_info.material_info().material_feature_size() > 0) {
    mutable_universe_client_ad_log->set_rule_id(ad_base_info.material_info().material_feature(0).rule_id());
  }
  //  生成 charge info
  std::string encode_charge_info_string;
  kuaishou::ad::AdChargeInfo charge_info;
  std::string charge_info_string;
  int64_t visitor_id = 0;
  if (absl::SimpleAtoi(request.universe_ad_request_info().real_user_id(), &visitor_id)) {
    charge_info.set_visitor_id(visitor_id);
    mutable_ad_log->set_visitor_id(visitor_id);
  }
  charge_info.set_device_id(request.ad_user_info().device_id());

  // 加速探索广告填充 charge_type
  bool ali_exclude_flag = false;
  const auto* rank_result = GetAdRankResult(session_data_, *item);
  if (rank_result) {
    if (rank_result->ad_rank_trans_info().ali_exclude_flag() == 2) {  // NOLINT
      ali_exclude_flag = true;
    }
  }
  bool rta_exclude_flag = false;
  if (item->ad_deliver_info().ad_base_info().rta_source_type() !=
      kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    rta_exclude_flag = item->ad_deliver_info().ad_base_info().rta_info().is_dynamic_bidding();
  }

  if (ad_base_info.ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::unit_acc_cold_start) {
    charge_info.set_charge_type(CallbackChargeType::kAccColdStart);
  } else if (ad_base_info.ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::unit_recovery_start) {
    charge_info.set_charge_type(CallbackChargeType::kRecoveryStart);  // 表示一键复苏
  } else if (ali_exclude_flag &&
             ad_base_info.campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE) {
    charge_info.set_charge_type(CallbackChargeType::kAliDeepCvr);
  } else if (rta_exclude_flag) {
    charge_info.set_charge_type(CallbackChargeType::kRtaBidRatio);
    session_data_->dot_perf->Count(1, "rta_set_build_ad_log");
  } else if (ad_base_info.ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::ecpc) {
    charge_info.set_charge_type(CallbackChargeType::kEcpc);
  } else if (ad_base_info.ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::ocpm2_min_cost) {
    charge_info.set_charge_type(CallbackChargeType::ocpm2MinCost);
  } else if (ad_base_info.ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::universe_manual_bid_1) {
    charge_info.set_charge_type(CallbackChargeType::kUniverseManualBid1);
  } else if (ad_base_info.ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::universe_manual_bid_2) {
    charge_info.set_charge_type(CallbackChargeType::kUniverseManualBid2);
  }
  CHARGE_TYPE_FALLBACK(ad_base_info, charge_info, charge_info);
  auto *mutable_ad_deliver_info = charge_info.mutable_ad_deliver_info();
  auto *mutable_ad_base_info = mutable_ad_deliver_info->mutable_ad_base_info();
  auto *mutable_dmp_package_hit_info = mutable_ad_deliver_info->mutable_dmp_package_hit_info();
  mutable_ad_deliver_info->mutable_cmd_key_id()->CopyFrom(ad_deliver_info.cmd_key_id());
  mutable_ad_deliver_info->mutable_cmd_id()->CopyFrom(ad_deliver_info.cmd_id());
  mutable_ad_deliver_info->set_price(ad_deliver_info.price());
  mutable_ad_deliver_info->set_price_undiscount(ad_deliver_info.price_undiscount());
  mutable_ad_deliver_info->set_virtual_price(ad_deliver_info.virtual_price());
  // [xiaowentao] ADN 虚拟金补贴精确值（浮点数），用于修复 1 厘以下 ADN 补贴金截断问题
  mutable_ad_deliver_info->set_virtual_price_precise(ad_deliver_info.virtual_price_precise());
  mutable_ad_deliver_info->set_joint_bidding_virtual_price(ad_deliver_info.joint_bidding_virtual_price());
  mutable_ad_deliver_info->set_joint_bidding_strategy_type(ad_deliver_info.joint_bidding_strategy_type());
  mutable_ad_deliver_info->set_union_strategy_tag(ad_deliver_info.union_strategy_tag());
  mutable_ad_deliver_info->set_server_time_ms(ad_deliver_info.server_time_ms());
  mutable_ad_deliver_info->set_ad_audience_type(ad_deliver_info.ad_audience_type());
  mutable_ad_deliver_info->set_budget_tag(ad_deliver_info.budget_tag());
  mutable_ad_deliver_info->set_price_audience(ad_deliver_info.price_audience());
  mutable_ad_deliver_info->set_price_audience(ad_deliver_info.price_audience());
  mutable_ad_deliver_info->mutable_rank_cmd()->CopyFrom(ad_deliver_info.rank_cmd());
  mutable_ad_deliver_info->mutable_universe_ad_deliver_info()->CopyFrom(
      ad_deliver_info.universe_ad_deliver_info());
  mutable_ad_deliver_info->mutable_pid_tag()->CopyFrom(ad_deliver_info.pid_tag());
  mutable_ad_deliver_info->set_cpm(ad_deliver_info.cpm());
  mutable_ad_deliver_info->set_rank_benefit(ad_deliver_info.rank_benefit());
  mutable_ad_deliver_info->set_predict_unify_ctr(ad_deliver_info.predict_unify_ctr());
  mutable_ad_deliver_info->set_predict_unify_cvr(ad_deliver_info.predict_unify_cvr());
  mutable_ad_deliver_info->set_predict_unify_deep_cvr(ad_deliver_info.predict_unify_deep_cvr());
  mutable_ad_deliver_info->set_twin_bid_strategy(ad_deliver_info.twin_bid_strategy());
  mutable_ad_deliver_info->set_conv_nextstay(ad_deliver_info.conv_nextstay());
  mutable_ad_deliver_info->set_is_skip_bid_server(ad_deliver_info.is_skip_bid_server());
  mutable_ad_deliver_info->set_skip_bid_server_tag(ad_deliver_info.skip_bid_server_tag());
  mutable_ad_deliver_info->set_region_id(request.ad_user_info().region());
  mutable_ad_deliver_info->mutable_target_hit_tag()->set_region_adcode(
      ad_deliver_info.target_hit_tag().region_adcode());
  mutable_ad_deliver_info->mutable_predict_info()->set_ctr(ad_deliver_info.predict_info().ctr());
  mutable_ad_deliver_info->mutable_predict_info()->set_app_conversion_rate(
      ad_deliver_info.predict_info().app_conversion_rate());
  mutable_ad_deliver_info->mutable_predict_info()->set_landingpage_rate(
      ad_deliver_info.predict_info().landingpage_rate());
  mutable_ad_deliver_info->mutable_predict_info()->set_server_show_cvr(
      ad_deliver_info.predict_info().server_show_cvr());
  mutable_ad_deliver_info->mutable_predict_info()->set_click2_conv(
      ad_deliver_info.predict_info().click2_conv());
  mutable_ad_deliver_info->mutable_predict_info()->set_click2_lps(
      ad_deliver_info.predict_info().click2_lps());
  mutable_ad_deliver_info->set_group_tag(ad_deliver_info.group_tag());
  mutable_ad_deliver_info->set_deep_group_tag(ad_deliver_info.deep_group_tag());
  mutable_ad_deliver_info->set_brand_user_group(0);
  mutable_ad_deliver_info->set_gid(ad_deliver_info.gid());
  mutable_ad_deliver_info->set_ad_queue_type(ks::ad_base::GetAdQueueType(item->ad_deliver_info()));
  mutable_ad_deliver_info->set_ecpc_unify_input(ad_deliver_info.ecpc_unify_input());
  mutable_ad_deliver_info->mutable_ecpc_unify_inputs()->CopyFrom(ad_deliver_info.ecpc_unify_inputs());
  mutable_ad_deliver_info->mutable_ecpc_unify_targets()->CopyFrom(ad_deliver_info.ecpc_unify_targets());
  mutable_ad_deliver_info->set_rta_bid(ad_deliver_info.rta_bid());
  if (ad_deliver_info.rta_bid() > 0) {
    mutable_ad_deliver_info->set_rta_bid_flag(1);
  }
  mutable_ad_deliver_info->set_media_price(ad_deliver_info.media_price());
  mutable_ad_deliver_info->mutable_charge_tag()->CopyFrom(ad_deliver_info.charge_tag());
  mutable_ad_deliver_info->set_ad_monitor_type(ad_deliver_info.ad_monitor_type());
  mutable_ad_deliver_info->set_landing_page_component(ad_deliver_info.landing_page_component());
  mutable_ad_deliver_info->set_rta_sta_tag(ad_deliver_info.rta_sta_tag());
  mutable_ad_deliver_info->set_price_before_billing_separate(ad_deliver_info.price_before_billing_separate());
  mutable_ad_deliver_info->set_price_after_billing_separate(ad_deliver_info.price_after_billing_separate());
  mutable_ad_deliver_info->mutable_bidword_params()->CopyFrom(ad_deliver_info.bidword_params());
  mutable_ad_deliver_info->mutable_ad_base_info()->set_dark_source_type_id(
      ad_deliver_info.ad_base_info().dark_source_type_id());
  mutable_ad_base_info->set_is_strict_native_ad(ad_base_info.is_strict_native_ad());
  mutable_ad_base_info->set_mini_app_id_platform(ad_base_info.mini_app_id_platform());
  mutable_ad_base_info->set_kwai_book_id(ad_base_info.kwai_book_id());
  mutable_ad_base_info->set_bid_type(ad_base_info.bid_type());
  mutable_ad_base_info->set_charge_action_type(ad_base_info.charge_action_type());
  mutable_ad_base_info->set_ocpc_action_type(ad_base_info.ocpc_action_type());
  mutable_ad_base_info->set_campaign_id(ad_base_info.campaign_id());
  mutable_ad_base_info->set_unit_id(ad_base_info.unit_id());
  mutable_ad_base_info->set_account_id(ad_base_info.account_id());
  mutable_ad_base_info->set_creative_id(ad_base_info.creative_id());
  mutable_ad_base_info->set_bid(ad_base_info.bid());
  mutable_ad_base_info->set_adx_source_type(ad_base_info.adx_source_type());
  mutable_ad_base_info->set_material_id(ad_base_info.material_id());
  mutable_ad_base_info->set_auction_bid(ad_base_info.auction_bid());
  mutable_ad_base_info->set_cpa_bid(ad_base_info.cpa_bid());
  mutable_ad_base_info->set_auto_cpa_bid(ad_base_info.auto_cpa_bid());
  mutable_ad_base_info->set_cover_id(ad_base_info.cover_id());
  mutable_ad_base_info->set_campaign_type(ad_base_info.campaign_type());
  mutable_ad_base_info->set_item_type(ad_base_info.item_type());
  mutable_ad_base_info->set_target_id(ad_base_info.target_id());
  mutable_ad_base_info->set_deep_conversion_type(ad_base_info.deep_conversion_type());
  mutable_ad_base_info->set_deep_conversion_bid(ad_base_info.deep_conversion_bid());
  mutable_ad_base_info->set_budget_reuse_type(ad_base_info.budget_reuse_type());
  mutable_ad_base_info->set_ad_strategy_tag(ad_base_info.ad_strategy_tag());
  mutable_ad_base_info->set_merchant_product_id(ad_base_info.merchant_product_id());
  mutable_ad_base_info->set_merchant_item_put_type(ad_base_info.merchant_item_put_type());
  mutable_ad_base_info->set_account_type(ad_base_info.account_type());
  mutable_ad_base_info->set_promotion_type(ad_base_info.promotion_type());
  mutable_ad_base_info->set_server_exp_tag(ad_base_info.server_exp_tag());
  mutable_ad_base_info->set_ry_ad_style(ad_base_info.ry_ad_style());
  mutable_ad_base_info->set_unit_source_type(ad_base_info.unit_source_type());
  mutable_ad_base_info->set_front_service_stage(ad_base_info.front_service_stage());
  mutable_ad_base_info->set_front_service_az(ad_base_info.front_service_az());
  // 联盟账户调价相关字段
  mutable_ad_base_info->set_is_account_bidding(ad_base_info.is_account_bidding());
  mutable_ad_base_info->set_aggre_key(ad_base_info.aggre_key());
  mutable_ad_base_info->set_bid_coef(ad_base_info.bid_coef());
  mutable_ad_base_info->set_cpa_coef(ad_base_info.cpa_coef());
  if (ad_base_info.derived_exchange()) {
    mutable_ad_base_info->set_derived_exchange(ad_base_info.derived_exchange());
    mutable_ad_base_info->set_derived_creative_id(ad_base_info.derived_creative_id());
    mutable_ad_base_info->set_derived_photo_id(ad_base_info.derived_photo_id());
    mutable_ad_base_info->set_derived_cover_id(ad_base_info.derived_cover_id());
    mutable_ad_base_info->set_derived_material_id(ad_base_info.derived_material_id());
  }
  mutable_ad_base_info->set_pg_lp_page_id(ad_base_info.pg_lp_page_id());
  mutable_ad_base_info->set_pg_lp_page_group_id(ad_base_info.pg_lp_page_group_id());
  mutable_ad_base_info->set_is_site(ad_base_info.is_site());
  mutable_ad_base_info->set_site_id(ad_base_info.site_id());
  mutable_ad_base_info->set_magic_site_page_type(ad_base_info.magic_site_page_type());
  mutable_ad_base_info->set_page_source_type(ad_base_info.page_source_type());
  mutable_ad_base_info->set_creative_photo_source(ad_base_info.creative_photo_source());
  if (ad_base_info.account_type() == AdEnum::ACCOUNT_ESP_MOBILE) {
    mutable_ad_base_info->mutable_fanstop_ext_info()->CopyFrom(ad_base_info.fanstop_ext_info());
    mutable_ad_base_info->set_order_type(ad_base_info.order_type());
    mutable_ad_base_info->set_order_tab(ad_base_info.order_tab());
    mutable_ad_base_info->set_source_id(ad_base_info.source_id());
    mutable_ad_base_info->set_live_order_source(ad_base_info.live_order_source());
    mutable_ad_base_info->set_charge_mode(ad_base_info.charge_mode());
    if (SPDM_universeEspMobileSoruceTypeFix()) {
      mutable_ad_base_info->set_ad_source_type(ad_base_info.ad_source_type());
    }
  }
  if ((ad_base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
       ad_base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE)) {
    mutable_ad_base_info->set_is_account_live(ad_base_info.is_account_live());
    mutable_ad_base_info->set_live_start_time(ad_base_info.live_start_time());
    mutable_ad_base_info->set_live_end_time(ad_base_info.live_end_time());
    mutable_ad_base_info->set_auto_deliver_type(ad_base_info.auto_deliver_type());
    mutable_ad_base_info->set_scene_oriented_type(ad_base_info.scene_oriented_type());
    mutable_ad_base_info->set_creative_build_type(ad_base_info.creative_build_type());
    mutable_ad_base_info->set_account_create_source(ad_base_info.account_create_source());
    mutable_ad_base_info->set_put_type(ad_base_info.put_type());
    mutable_ad_base_info->set_project_cpa_bid(style_info.ecom_hosting_project().cpa_bid());
    mutable_ad_base_info->set_project_roi_ratio(style_info.ecom_hosting_project().roi_ratio());
    mutable_ad_base_info->set_project_ocpx_action_type(style_info.ecom_hosting_project().ocpx_action_type());
    mutable_ad_base_info->set_project_speed(style_info.ecom_hosting_project().speed());
  }
  if ((ad_base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
       ad_base_info.campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE)) {
    mutable_ad_base_info->set_combo_order_id(ad_base_info.combo_order_id());
    mutable_ad_base_info->set_combo_type(ad_base_info.combo_type());
    mutable_ad_base_info->set_ad_sub_source_type(ad_base_info.ad_sub_source_type());
    mutable_ad_base_info->set_auto_deliver_related_id(ad_base_info.auto_deliver_related_id());
  }
  if (ad_base_info.account_type() == AdEnum::ACCOUNT_FANSTOP_TEMU) {
    mutable_ad_base_info->set_ad_sub_source_type(kuaishou::ad::FANS_TOP_TEMU);
  }
  if (ad_base_info.account_type() == AdEnum::ACCOUNT_SOCIAL) {
    mutable_ad_base_info->set_ad_sub_source_type(kuaishou::ad::AD_SOCIAL_ORDER);
    mutable_ad_base_info->set_ad_source_type(ad_base_info.ad_source_type());
    mutable_ad_base_info->set_charge_mode(ad_base_info.charge_mode());
  }
  // 客户二级行业
  {
    mutable_ad_base_info->set_client_second_industry_name_city_hash(
        ad_base_info.client_second_industry_name_city_hash());
    LOG_EVERY_N(INFO, 1000000) << "client_second_industry_name_city_hash_universe_style"
                               << ad_base_info.client_second_industry_name_city_hash();
  }
  mutable_ad_base_info->set_explore_ext_type(ad_base_info.explore_ext_type());
  if (mutable_ad_base_info) {
    mutable_ad_base_info->set_receivable_price(ad_deliver_info.price());
  }

  // gimbal 打折前
  if (ad_base_info.gimbal_price_before() > 0) {
    mutable_ad_base_info->set_gimbal_price_before(ad_base_info.gimbal_price_before());
  } else {
    mutable_ad_base_info->set_gimbal_price_before(ad_deliver_info.price());
  }
  mutable_ad_base_info->set_explore_put_type(ad_base_info.explore_put_type());
  mutable_ad_base_info->set_explore_budget_status(ad_base_info.explore_budget_status());
  mutable_ad_base_info->set_auto_bid_explore(ad_base_info.auto_bid_explore());
  mutable_ad_base_info->set_auction_bid_explore_total(ad_base_info.auction_bid_explore_total());
  mutable_ad_base_info->set_rank_benefit_explore_origin(ad_base_info.rank_benefit_explore_origin());
  mutable_ad_base_info->set_is_increment_explore(ad_base_info.is_increment_explore());
  mutable_ad_base_info->set_universe_explore_sample_type(ad_base_info.universe_explore_sample_type());
  // 代投 分销字段下发
  mutable_ad_base_info->set_account_user_id(ad_base_info.account_user_id());
  if ((ad_base_info.campaign_type() == AdEnum::KWAI_PROMOTION_CONSULTATION &&
       ad_base_info.charge_mode() == AdEnum::ORDER) ||
      (ad_base_info.campaign_type() == AdEnum::KWAI_PROMOTION_LOCAL_STORE_ORDER)) {
    mutable_ad_base_info->set_ad_sub_source_type(kuaishou::ad::BUSINESS_FANSTOP);
  }
  if (SPDM_enableCheckBudgetDeliveryType() && style_info.campaign().budget_delivery_type() == 1) {
    mutable_ad_base_info->set_ad_sub_source_type(kuaishou::ad::ROI_ORDER);
    mutable_ad_base_info->set_ad_source_type(::kuaishou::ad::DSP);
  }
  if (ad_base_info.campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
    mutable_ad_base_info->set_series_id(ad_base_info.series_id());
  }
  // 暂时 callback_passback 和 ad_callback_passback 都传，下游升级完干掉这个
  mutable_ad_base_info->set_callback_passback(ad_base_info.callback_passback());
  auto* mutable_effect_prediction = mutable_ad_base_info->mutable_effect_prediction();
  mutable_effect_prediction->set_min_conversion_cnt(ad_base_info.effect_prediction().min_conversion_cnt());
  mutable_effect_prediction->set_max_conversion_cnt(ad_base_info.effect_prediction().max_conversion_cnt());
  mutable_effect_prediction->set_timestamp(ad_base_info.effect_prediction().timestamp());
  // 回传智能平台 2.0 字段
  mutable_ad_base_info->set_auto_manage(ad_base_info.auto_manage());
  mutable_ad_base_info->set_account_auto_manage(ad_base_info.account_auto_manage());
  mutable_ad_base_info->set_auto_manage_type(ad_base_info.auto_manage_type());
  mutable_ad_base_info->set_account_constraint_value(ad_base_info.account_constraint_value());
  mutable_ad_base_info->set_auax_constraint_value(ad_base_info.auax_constraint_value());
  mutable_ad_base_info->set_auto_adjust(ad_base_info.auto_adjust());
  mutable_ad_base_info->set_auto_build(ad_base_info.auto_build());
  mutable_ad_base_info->mutable_ad_callback_passback()->CopyFrom(ad_base_info.ad_callback_passback());
  mutable_ad_base_info->set_version(ad_base_info.version());
  mutable_ad_base_info->set_use_sdk(ad_base_info.use_sdk());
  mutable_ad_base_info->set_photo_id(ad_base_info.photo_id());
  // 直播直投清空 photo_id
  if (ad_base_info.item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
    mutable_ad_base_info->set_photo_id(0);
  }
  mutable_ad_base_info->set_pic_id(ad_base_info.pic_id());
  mutable_ad_base_info->set_author_id(ad_base_info.author_id());
  mutable_ad_base_info->set_package_name(ad_base_info.package_name());
  mutable_ad_base_info->set_roi_ratio(ad_base_info.roi_ratio());
  mutable_ad_base_info->set_unit_type(ad_base_info.unit_type());
  mutable_ad_base_info->set_new_creative_tag(ad_base_info.new_creative_tag());
  mutable_ad_base_info->set_creative_material_type(ad_base_info.creative_material_type());
  mutable_ad_base_info->set_merchandise_id(ad_base_info.merchandise_id());
  mutable_ad_base_info->set_merchandise_type(ad_base_info.merchandise_type());
  mutable_ad_base_info->set_is_universe_opt(ad_base_info.is_universe_opt());
  mutable_ad_base_info->set_speed_type(ad_base_info.speed_type());
  mutable_ad_base_info->set_industry_id_v3(ad_base_info.industry_id_v3());
  mutable_ad_base_info->set_first_industry_id_v3(ad_base_info.first_industry_id_v3());
  mutable_ad_base_info->set_dpa_type(ad_base_info.dpa_type());
  mutable_ad_base_info->set_product_name(ad_base_info.product_name());
  mutable_ad_base_info->set_periodic_delivery_type(ad_base_info.periodic_delivery_type());
  mutable_ad_base_info->set_campaign_begin_time(ad_base_info.campaign_begin_time());
  mutable_ad_base_info->set_campaign_end_time(ad_base_info.campaign_end_time());
  mutable_ad_base_info->set_periodic_days(ad_base_info.periodic_days());
  if (ad_base_info.bid_type() == kuaishou::ad::AdEnum::MCB) {
    mutable_ad_base_info->set_mcb_cpa_bid(ad_base_info.mcb_cpa_bid());
    mutable_ad_base_info->set_mcb_roi_ratio(ad_base_info.mcb_roi_ratio());
    mutable_ad_base_info->set_performance_fix_ratio(ad_base_info.performance_fix_ratio());
  }
  mutable_client_ad_log->set_live_stream_id(absl::StrCat(ad_base_info.live_stream_id()));
  if (ad_base_info.has_rta_source_type() &&
      ad_base_info.rta_source_type() != kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    mutable_ad_base_info->set_rta_source_type(ad_base_info.rta_source_type());
  }
  mutable_ad_base_info->set_is_site(ad_base_info.is_site());
  mutable_ad_base_info->set_site_id(ad_base_info.site_id());
  mutable_ad_base_info->set_rta_feature_id(ad_deliver_info.online_join_params_transparent().rta_feature_id());
  mutable_ad_base_info->set_magic_site_page_type(ad_base_info.magic_site_page_type());
  mutable_ad_base_info->set_page_source_type(ad_base_info.page_source_type());
  mutable_ad_base_info->set_budget_smart_allocation(ad_base_info.budget_smart_allocation());
  mutable_ad_base_info->set_dsp_version(ad_base_info.dsp_version());
  mutable_ad_base_info->set_marketing_objective(ad_base_info.marketing_objective());
  mutable_ad_base_info->set_delivery_scenario(ad_base_info.delivery_scenario());
  mutable_ad_base_info->set_delivery_method(ad_base_info.delivery_method());
  mutable_ad_base_info->set_managed_strategy(ad_base_info.managed_strategy());
  mutable_ad_base_info->set_ad_type(ad_base_info.ad_type());
  if (ad_base_info.dpa_product_id() > 0) {
    mutable_ad_base_info->set_dpa_product_id(ad_base_info.dpa_product_id());
  }
  if (SPDM_enableAddRelType()) {
    mutable_ad_base_info->set_rel_campaign_id(style_info.campaign().rel_campaign_id());
    mutable_ad_base_info->set_explore_material_type(style_info.campaign().explore_material_type());
    mutable_ad_base_info->set_rel_type(style_info.campaign().rel_type());
  }

  // 这里 proto 字段拼写有误，日志产出时会做改写，正确的字段名为 sdpa_first_category_id
  mutable_ad_base_info->set_spda_first_category_id(ad_base_info.spda_first_category_id());
  mutable_ad_base_info->set_spda_second_category_id(ad_base_info.spda_second_category_id());
  mutable_ad_base_info->set_spda_third_category_id(ad_base_info.spda_third_category_id());
  mutable_ad_base_info->set_spda_category_id(ad_base_info.spda_category_id());
  bool is_skip_qcpx_mode = session_data_->get_is_closure_flow() &&
      session_data_->is_closure_ad(ad_base_info.campaign_type(), ad_base_info.ocpc_action_type());
  if (!is_skip_qcpx_mode) {
    mutable_ad_base_info->set_qcpx_put_type(ad_base_info.qcpx_put_type());
    mutable_ad_base_info->set_coupon_scope(ad_base_info.coupon_scope());
    mutable_ad_base_info->set_coupon_strategy_type(ad_base_info.coupon_strategy_type());
  }

  mutable_ad_base_info->set_photo_source(ad_base_info.photo_source());
  session_data_->dot_perf->Count(1, "sub_conversion_path", "build_ad_log");

  // 为应收数据流透传联盟所需数据
  kuaishou::ad::OnlineJoinParams online_join_pb;
  online_join_pb.set_conv_nextstay(ad_deliver_info.predict_info().deep_cvr_info().deep_cvr());
  online_join_pb.set_twin_bid_strategy(ad_deliver_info.online_join_params_transparent().twin_bid_strategy());
  online_join_pb.set_predict_unify_deep_cvr(ad_deliver_info.predict_unify_deep_cvr());
  online_join_pb.set_is_universe_opt(ad_base_info.is_universe_opt());
  online_join_pb.set_predict_unify_ctr(ad_deliver_info.predict_unify_ctr());
  online_join_pb.set_predict_unify_cvr(ad_deliver_info.predict_unify_cvr());
  bool is_playable =
      (style_info.unit_support_info().playable_switch() == 2 || style_info.play_info().upload_source() == 2);
  is_playable |= (style_info.play_info().upload_source() == 3);
  online_join_pb.set_is_playable(is_playable);
  online_join_pb.set_is_skip_bid_server(ad_deliver_info.is_skip_bid_server());

  std::string online_join_data_str;
  ::google::protobuf::util::MessageToJsonString(online_join_pb, &online_join_data_str);
  mutable_ad_deliver_info->set_online_join_params(online_join_data_str);

  charge_info.SerializeToString(&charge_info_string);
  if (charge_info_string.empty()) {
    session_data_->dot_perf->Count(1, "front_server.universe_ad_log_charge_info_empty_num");
  }
  base::Base64Encode(aes_crypter.Encrypt(charge_info_string), &encode_charge_info_string);
  mutable_client_ad_log->set_charge_info(encode_charge_info_string);
}

kuaishou::ad::AdActionType UniverseStylePostProc::GetAdxChargeActionType(
    const kuaishou::ad::AdBaseInfo* base_info) {
  kuaishou::ad::AdActionType charge_action_type =
      GetChargeActionType(base_info->bid_type(), base_info->ocpc_action_type());
  switch (base_info->bid_type()) {
    case kuaishou::ad::AdEnum::CPM:
    case kuaishou::ad::AdEnum::OCPM:
      charge_action_type = kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION;
      break;
    case kuaishou::ad::AdEnum::CPC:
    case kuaishou::ad::AdEnum::OCPC:
      charge_action_type = kuaishou::ad::AdActionType::AD_ITEM_CLICK;
      break;
    default:
      break;
  }
  return charge_action_type;
}

kuaishou::ad::AdActionType UniverseStylePostProc::GetChargeActionType(
    kuaishou::ad::AdEnum::BidType bid_type, kuaishou::ad::AdActionType action_type) {
  switch (bid_type) {
    case kuaishou::ad::AdEnum::CPM:
    case kuaishou::ad::AdEnum::OCPM:
      return kuaishou::ad::AdActionType::AD_PHOTO_IMPRESSION;
    case kuaishou::ad::AdEnum::CPC:
    case kuaishou::ad::AdEnum::OCPC:
      return kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION;
    case kuaishou::ad::AdEnum::CPT:
      return kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
    case kuaishou::ad::AdEnum::CPC2:  // CPC2 计费点
      return kuaishou::ad::AdActionType::AD_ITEM_CLICK;
    case kuaishou::ad::AdEnum::CPA:
      if (action_type == kuaishou::ad::AdActionType::AD_PURCHASE) {
        return kuaishou::ad::AdActionType::EVENT_PAY;
      }
      if (action_type == kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE) {
        TLOG(ERROR) << "Unknown cpa charge action_type: << " << action_type << "!!!";
        falcon::Inc("front_server.unknown_cpa_charge_action_type", 1);
      }
      return action_type;
    default:
      TLOG(ERROR) << "Unknown bid_type << " << bid_type << "!!!";
      falcon::Inc("front_server.unknown_bid_type_for_charge_action_type", 1);
      return kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
  }
}

void UniverseStylePostProc::FixBidAndPrice(const StyleInfoItem& style_info, kuaishou::ad::AdResult* result) {
  session_data_->mutable_ad_price()->Clear();
  int64_t creative_id = result->ad_deliver_info().ad_base_info().creative_id();
  const auto* p_rank_result = GetAdRankResult(session_data_, *result);
  if (!p_rank_result) {
    falcon::Inc(absl::Substitute("front_server.no_ad_rank_result.creative_id.$0", creative_id).data(), 1);
    TLOG(ERROR) << "creative_id=" << creative_id << ", no rank_result, so not fill ad_deliver_info";
    return;
  }

  const auto& rank_result = *p_rank_result;
  session_data_->mutable_ad_price()->bid_type = result->ad_deliver_info().ad_base_info().bid_type();
  session_data_->mutable_ad_price()->price = rank_result.rank_price_info().price();
  session_data_->mutable_ad_price()->cpm = rank_result.rank_price_info().cpm();
  session_data_->mutable_ad_price()->price_audience = rank_result.rank_price_info().price_audience();

  PriceUpLimitProtect(style_info, result);
  SetAdPrice(rank_result, style_info, result);
}

// 判断计费点，并做计费上限保护
void UniverseStylePostProc::PriceUpLimitProtect(const StyleInfoItem& style_info,
                                                kuaishou::ad::AdResult* result) {
  int64 creative_id = result->ad_deliver_info().ad_base_info().creative_id();
  const auto& target_bid = result->target_bid();

  if (target_bid.cpa_bid() <= 0) {
    return;
  }
  auto charge_action_type = result->ad_deliver_info().ad_base_info().charge_action_type();
  auto price_up_limit_map = FrontKconfUtil::priceUpLimitByChargeActionType();
  if (price_up_limit_map == nullptr) {
    return;
  }
  std::string charge_action_type_name = kuaishou::ad::AdActionType_Name(charge_action_type);
  auto iter = price_up_limit_map->find(charge_action_type_name);
  if (iter != price_up_limit_map->end()) {
    double up_thr = iter->second * target_bid.cpa_bid();
    if (session_data_->get_ad_price().price > up_thr) {
      double old_price = session_data_->get_ad_price().price;
      session_data_->mutable_ad_price()->price = up_thr;
      session_data_->mutable_ad_price()->price_bounded = true;
      falcon::Inc("front_server.price_exceed_up_limit_exception", 1);
      TLOG_EVERY_N(ERROR, 10000) << "unit_id=" << style_info.unit().id()
                                 << " account_id=" << style_info.account().id()
                                 << " charge_action_type=" << charge_action_type << " "
                                 << charge_action_type_name << " old_price=" << old_price
                                 << " price=" << session_data_->get_ad_price().price << " thr=" << up_thr
                                 << " cpa_bid=" << target_bid.cpa_bid() << " up_ratio=" << iter->second;
    }
  }
}

void UniverseStylePostProc::SetAdPrice(const kuaishou::ad::RankResult&  /*rank_result*/,
                                       const StyleInfoItem& style_info, kuaishou::ad::AdResult* result) {
  int64 creative_id = result->ad_deliver_info().ad_base_info().creative_id();
  // 第三方人群包如果没配置计费比例的话，默认收 5%
  const auto paid_audience_charge_ratio_config = FrontKconfUtil::paidAudienceChargeRatio();
  double charge_ratio = 0.05;
  // DEBUG TLOG
  auto is_paid_audience_used = [&]() {
    std::unordered_set<int64> user_paid_audience_set;
    for (int i = 0; i < session_data_->get_ad_request()->ad_user_info().tp_paid_audience_size(); ++i) {
      auto paid_audience = session_data_->get_ad_request()->ad_user_info().tp_paid_audience(i);
      if (paid_audience > 0) {
        user_paid_audience_set.insert(paid_audience);
      }
    }
    for (int i = 0; i < style_info.target().extend_fields().paid_audience_size(); ++i) {
      int64_t paid_audience_id = style_info.target().extend_fields().paid_audience(i);
      if (user_paid_audience_set.count(paid_audience_id) > 0) {
        return true;
      }
    }
    return false;
  };

  bool has_paid_audience = (style_info.target().extend_fields().paid_audience_size() != 0);
  bool paid_audience_used = is_paid_audience_used();
  if (style_info.has_target() && has_paid_audience && !paid_audience_used) {
    falcon::Inc("ad_server.paid_audience_break_count");
  }
  auto paid_audience_type = style_info.target().paid_audience_type();
  if (has_paid_audience && paid_audience_used &&
      paid_audience_type != kuaishou::ad::AdEnum::UNKNOWN_TP_AUDIENCE_TYPE) {
    auto charge_ratio_iter =
        paid_audience_charge_ratio_config->find(static_cast<int64_t>(paid_audience_type));
    if (charge_ratio_iter == paid_audience_charge_ratio_config->end()) {
      TLOG_EVERY_N(WARNING, 1000) << "Maybe new paid_audience_type, unit_id:" << style_info.unit().id()
                                  << " paid_audience_type:" << static_cast<int>(paid_audience_type);
    } else {
      charge_ratio = charge_ratio_iter->second;
    }
    session_data_->mutable_ad_price()->price_audience =
        session_data_->mutable_ad_price()->price * charge_ratio;
  }
}

void UniverseStylePostProc::SetEncodeChargeInfo(const StyleInfoItem& style_info, AdResult* ad_result) {
  // TODO(liulong03) price 的兜底先放到这里，等 adFront 会依赖 adPack 的返回值以后，这里进行删除
  // chargeInfo 生成之前处理价格兜底
  ks::ad_base::util::PriceProtection(
      session_data_->get_sub_page_id(), session_data_->get_ad_request()->ad_request_flow_type(), ad_result,
      session_data_->get_universe_rtb_coff(), session_data_->get_is_universe_rtb_flow());
  kuaishou::ad::AdBaseInfo* base_info = ad_result->mutable_ad_deliver_info()->mutable_ad_base_info();
  kuaishou::ad::AdChargeInfo charge_info;
  auto* context = context_->GetMutableContextData<ContextData>();
  AddSesionExpChargeInfo(&charge_info, context);
  std::string charge_info_string;
  std::string encode_charge_info_string;
  std::string origin_charge_info_string;
  int64_t visitor_id = 0;
  if (absl::SimpleAtoi(session_data_->get_ad_request()->universe_ad_request_info().real_user_id(),
                       &visitor_id)) {
    charge_info.set_visitor_id(visitor_id);
  }
  charge_info.set_device_id(session_data_->get_ad_request()->ad_user_info().device_id());
  charge_info.set_interactive_form(
      static_cast<int64>(ks::ad_base::GetInteractiveForm(*(session_data_->get_ad_request()))));
  charge_info.mutable_ad_deliver_info()->CopyFrom(ad_result->ad_deliver_info());
  bool ali_exclude_flag = false;

  const auto* p_rank_result = GetAdRankResult(session_data_, *ad_result);
  if (p_rank_result) {
    if (p_rank_result->ad_rank_trans_info().ali_exclude_flag() == 2) {  // NOLINT
      ali_exclude_flag = true;
    }
  }

  bool rta_exclude_flag = false;
  if (ad_result->ad_deliver_info().ad_base_info().rta_source_type() !=
      kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    rta_exclude_flag = ad_result->ad_deliver_info().ad_base_info().rta_info().is_dynamic_bidding();
  }

  if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::unit_acc_cold_start) {
    charge_info.set_charge_type(CallbackChargeType::kAccColdStart);
  } else if (base_info->ad_bid_server_group_tag() == ad_base::AutoBidGroupTags::unit_recovery_start) {
    charge_info.set_charge_type(CallbackChargeType::kRecoveryStart);  // 表示一键复苏
  } else if (ali_exclude_flag &&
             base_info->campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE) {
    charge_info.set_charge_type(CallbackChargeType::kAliDeepCvr);
  } else if (rta_exclude_flag) {
    charge_info.set_charge_type(CallbackChargeType::kRtaBidRatio);
    session_data_->dot_perf->Count(1, "rta_set_charge_info");
  }

  CleanChargeInfo(session_data_, &charge_info);
  // 清除部分 chargeinfo 字段，与现有 adserver 逻辑一致
  charge_info.mutable_ad_deliver_info()->mutable_ad_base_info()->clear_alert_net_mobile();
  charge_info.mutable_ad_deliver_info()
      ->mutable_ad_base_info()
      ->mutable_client_style_info()
      ->clear_landing_page_actionbar_info();
  charge_info.mutable_ad_deliver_info()->mutable_ad_base_info()->clear_target_user_id();
  bool code = charge_info.SerializeToString(&charge_info_string);

  ChargeInfoByteSizeAlert(session_data_->dot_perf,
                          ks::ad_base::GetAdRequestType(*session_data_->get_ad_request()),
                          GetAdDspType(*ad_result), ad_result->ad_deliver_info().ad_monitor_type(),
                          charge_info, session_data_->get_byte_size_dot());

  serving_base::AesCrypter aes_crypter;
  base::Base64Encode(aes_crypter.Encrypt(charge_info_string), &encode_charge_info_string);
  if (KS_UNLIKELY(encode_charge_info_string.empty())) {
    falcon::Inc("front_server.charge_info_encode_failed", 1);
  }
  origin_charge_info_string = ad_result->encode_charge_info();
  ad_result->set_encode_charge_info(encode_charge_info_string);
}

#undef CHARGE_TYPE_FALLBACK

void UniverseStylePostProc::RecordAdShowInfo(AdResult* item, const RankResult& rank_result) {
  // 对 prerank 出来的广告统计顺位
  auto pos_in_prerank = item->ad_deliver_info().online_join_params_transparent().pos_in_prerank();
  if (pos_in_prerank >= 0) {
    falcon::Stat("front_server.rank_ad_select_pos_in_prerank", pos_in_prerank);
    falcon::Inc("front_server.rank_ad_select_by_prerank", 1);
    ks::ad_base::AdPerf::SetLogStash(
        pos_in_prerank, "ad.front_server", "ad_selected_pos_in_prerank",
        absl::StrCat(static_cast<int64>(ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request())))));
  }

  // 对 rank 出来的广告统计顺位
  auto pos_in_rank = item->ad_deliver_info().online_join_params_transparent().pos_in_rank();
  if (pos_in_rank >= 0) {
    falcon::Stat("front_server.rank_ad_select_pos_in_rank", pos_in_rank);
    ks::ad_base::AdPerf::SetLogStash(
        pos_in_rank, "ad.front_server", "ad_selected_pos_in_rank",
        absl::StrCat(static_cast<int64>(ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request())))));
  }
  const std::string& app_id_name = ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request()));
  const std::string request_type_name = kuaishou::ad::AdEnum_AdRequestFlowType_Name(
      ks::ad_base::GetAdRequestType(*(session_data_->get_ad_request())));  // NOLINT
  const std::string interative_form_name =
      absl::StrCat(ks::ad_base::GetInteractiveForm(*(session_data_->get_ad_request())));
  ks::ad_base::AdPerf::CountLogStash(1, "ad.front_server", "ad_count_ext", request_type_name,
                                     interative_form_name);

  if (0 < item->ad_deliver_info().online_join_params_transparent().new_creative_tag()) {
    falcon::Inc("front_server.is_new_creative", 1);
    session_data_->dot_perf->Count(1, "ad_server_new_creative_ad_count");
  }
  auto bid_type = item->target_bid().bid_type();
  auto ocpx_action_type = item->target_bid().ocpx_action_type();
  auto ad_source_type = item->ad_deliver_info().ad_base_info().ad_source_type();
  auto rank_benifit = rank_result.rank_price_info().rank_benifit();
  auto real_benifit = rank_result.rank_price_info().real_benifit();
  auto ctr = rank_result.predict_origin_info().ctr();
  auto click2_conv = rank_result.predict_origin_info().click2_conv();
  auto click2_lps = rank_result.predict_origin_info().click2_lps();
  auto bonus_cpm = rank_result.rank_price_info().bonus_cpm();
  auto multi_retrieval_tag = item->ad_deliver_info().online_join_params_transparent().multi_retrieval_tag();
  auto pred_score = item->ad_deliver_info().online_join_params_transparent().pred_score();
  auto rtb_ecpm = item->ad_deliver_info().ad_base_info().rtb_ecpm();
  session_data_->dot_perf->Count(1, "front_server_ad_count", kuaishou::ad::AdEnum_BidType_Name(bid_type),
                                 kuaishou::ad::AdActionType_Name(ocpx_action_type),
                                 kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Interval(
      session_data_->get_ad_price().cpm, "ad_server_cpm", kuaishou::ad::AdEnum_BidType_Name(bid_type),
      kuaishou::ad::AdActionType_Name(ocpx_action_type), kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Interval(
      session_data_->get_ad_price().price, "ad_server_price", kuaishou::ad::AdEnum_BidType_Name(bid_type),
      kuaishou::ad::AdActionType_Name(ocpx_action_type), kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Interval(
      rank_benifit, "ad_server_rank_benifit", kuaishou::ad::AdEnum_BidType_Name(bid_type),
      kuaishou::ad::AdActionType_Name(ocpx_action_type), kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Set(
      pos_in_prerank, "ad_server_pos_in_prerank", kuaishou::ad::AdEnum_BidType_Name(bid_type),
      kuaishou::ad::AdActionType_Name(ocpx_action_type), kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Set(
      pos_in_rank, "ad_server_pos_in_rank", kuaishou::ad::AdEnum_BidType_Name(bid_type),
      kuaishou::ad::AdActionType_Name(ocpx_action_type), kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Interval(
      real_benifit, "ad_server_real_benifit", kuaishou::ad::AdEnum_BidType_Name(bid_type),
      kuaishou::ad::AdActionType_Name(ocpx_action_type), kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Interval(
      static_cast<int64_t>(ctr * 1000), "ad_server_ctr", kuaishou::ad::AdEnum_BidType_Name(bid_type),
      kuaishou::ad::AdActionType_Name(ocpx_action_type), kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Interval(static_cast<int64_t>(click2_conv * 1000), "ad_server_click2_conv",
                                    kuaishou::ad::AdEnum_BidType_Name(bid_type),
                                    kuaishou::ad::AdActionType_Name(ocpx_action_type),
                                    kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Interval(static_cast<int64_t>(click2_lps * 1000), "ad_server_click2_lps",
                                    kuaishou::ad::AdEnum_BidType_Name(bid_type),
                                    kuaishou::ad::AdActionType_Name(ocpx_action_type),
                                    kuaishou::ad::AdSourceType_Name(ad_source_type));
  session_data_->dot_perf->Interval(
      bonus_cpm, "ad_server_bonus_cpm", kuaishou::ad::AdEnum_BidType_Name(bid_type),
      kuaishou::ad::AdActionType_Name(ocpx_action_type), kuaishou::ad::AdSourceType_Name(ad_source_type));
  auto maybe_tag =
      ::ks::ad_target::multi_retr::RetrievalTag::_from_integral_nothrow(multi_retrieval_tag);  // NOLINT
  if (maybe_tag) {
    session_data_->dot_perf->Interval(pred_score, "ad_server_multi_retr_score",
                                      maybe_tag->_to_string());  // NOLINT
  }

  if (session_data_->get_is_universe_rtb_flow()) {
    session_data_->dot_perf->Interval(static_cast<int64_t>(rtb_ecpm * 1000), "ad_server_rtb_ecpm",
                                      kuaishou::ad::AdEnum_BidType_Name(bid_type),
                                      kuaishou::ad::AdActionType_Name(ocpx_action_type),
                                      kuaishou::ad::AdSourceType_Name(ad_source_type));
  }
}

void UniverseStylePostProc::FillUniverseFollowList() {
  session_data_->mutable_follow_ids()->clear();
  auto universe_ext_data = session_data_->get_ad_response()->global_ext_data().universe_ext_data();
  session_data_->mutable_follow_ids()->insert(universe_ext_data.follow_list().begin(),
                                              universe_ext_data.follow_list().end());
}

// 判断短视频引流是否可以以直播流形式展示
bool UniverseStylePostProc::CheckEnableP2liveToLiveStream(const kuaishou::ad::AdBaseInfo& ad_base_info) {
  // 原生直播标志
  if (session_data_->get_ad_request()->universe_live_support_mode() != 1) {
    return false;
  }
  // 白名单主播判断
  if (FrontKconfUtil::livingAuthorWhiteList()->count(ad_base_info.author_id()) == 0) {
    return false;
  }
  // 优化目标准入
  if (FrontKconfUtil::universeP2liveToLiveWhiteOcpxType()->count(ad_base_info.ocpc_action_type()) == 0) {
    return false;
  }
  const auto& request_info = session_data_->get_ad_request()->universe_ad_request_info();
  if (request_info.imp_info_size() == 0) {
    return false;
  }
  const auto& imp_info = request_info.imp_info(0);
  int32_t ad_style = session_data_->get_ud_ad_style();
  int32_t render_type = imp_info.render_type();
  int32_t sdk_type = request_info.sdk_type();
  const std::string& sdk_version = request_info.sdk_version();
  const std::string& app_id = session_data_->copy_ud_app_id();
  int64_t pos_id = session_data_->get_ud_pos_id();
  const auto& support_material_types = session_data_->get_ud_creative_material_types();

  // 请求维度判断
  bool is_origin_live_pv = false;
  if (ad_style == 2) {
    is_origin_live_pv = true;
  } else if (ad_style == 3 || (ad_style == 23 && ad_base_info.ad_rollout_size() == 1)) {
    is_origin_live_pv = true;
  } else if (ad_style == 1 && SPDM_enable_p2live_show_live_stream_1(session_data_->get_spdm_ctx())) {
    // 获取模板列表
    std::vector<int32_t> pos_template_ids;
    for (const auto& id : imp_info.template_ids()) { pos_template_ids.emplace_back(id); }
    if (imp_info.template_ids().empty()) {
      pos_template_ids.emplace_back(imp_info.template_id());
    }
    // sdk 版本限制 &&  (sdk 渲染 template_id 限制 || 自渲染媒体白名单控制 素材类型限制 )
    bool sdk_admit = (sdk_type == 1 && engine_base::CompareAppVersion(sdk_version, "3.3.36") >= 0) ||
                     (sdk_type == 2 && engine_base::CompareAppVersion(sdk_version, "3.3.33") >= 0);
    bool material_type_admit =
        render_type == 2 ||
        (render_type == 1 && (support_material_types.count(kuaishou::ad::AdEnum::VERTICAL_SCREEN) ||
                              support_material_types.count(kuaishou::ad::AdEnum::HORIZONTAL_SCREEN)));
    is_origin_live_pv = sdk_admit && material_type_admit &&
                        engine_base::AdKconfUtil::universeFeedLiveControlConfig()->data().CheckFeedLiveAdmit(
                            app_id, pos_id, render_type, pos_template_ids);
  } else if ((ad_style == 13 || (ad_style == 23 && ad_base_info.ad_rollout_size() == 2)) &&
             SPDM_enable_p2live_show_live_stream_13(session_data_->get_spdm_ctx())) {
    // 插屏支持内循环原生直播 sdk 版本 + 直播标志 + ad_style + ab
    bool sdk_admit = (sdk_type == 1 && engine_base::CompareAppVersion(sdk_version, "3.3.37") >= 0) ||
                     (sdk_type == 2 && engine_base::CompareAppVersion(sdk_version, "3.3.33") >= 0);
    bool material_type_admit = support_material_types.count(kuaishou::ad::AdEnum::VERTICAL_SCREEN);
    is_origin_live_pv =
        sdk_admit && material_type_admit &&
        engine_base::AdKconfUtil::universeFeedLiveControlConfig()->data().CheckInterstitialLiveAdmit(
            app_id, pos_id, render_type);
  }
  return is_origin_live_pv;
}

void UniverseStylePostProc::SetDerivedInfo(const StyleInfoItem& style_info, kuaishou::ad::AdResult* item) {
  kuaishou::ad::AdBaseInfo* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  // 灰投广告填充原始创意相关 id
  if (IsRtbGray(style_info)) {
    base_info->set_derived_exchange(true);
    base_info->set_derived_creative_id(style_info.creative().id());
    base_info->set_derived_photo_id(style_info.creative().photo_id());
    base_info->set_derived_cover_id(base_info->cover_id());
    int64_t material_id;
    if (absl::SimpleAtoi(style_info.creative().material_id(), &material_id)) {
      base_info->set_derived_material_id(material_id);
    }
    base_info->set_is_splash_gray(true);
  } else if (IsInnerRtbGray(style_info)) {
    base_info->set_derived_exchange(true);
    base_info->set_derived_creative_id(
        style_info.creative().creative_support_info().auto_deliver_related_id());
    base_info->set_derived_photo_id(style_info.creative().creative_support_info().shadow_photo_id());
    base_info->set_is_splash_gray(true);
  }
  base_info->set_auto_deliver_type(style_info.creative().creative_support_info().auto_deliver_type());
}

void UniverseStylePostProc::SetSearchAdInfo(const StyleInfoItem& style_info, AdResult* item) {
  bool is_quick_search = style_info.unit_support_info().quick_search() == 1;
  // 非厂商流量的落表在 UniverseQueryMockNode 中进行处理
  // 厂商流量的落表在这里进行处理
  if (session_data_->get_ud_is_universe_tiny_flow()) {
    item->mutable_ad_deliver_info()->mutable_bidword_params()->set_match_type(0);
    bool is_extend_search = style_info.unit_support_info().extend_search() == 1;
    item->mutable_ad_deliver_info()->mutable_bidword_params()->set_extend_search(is_extend_search);

    item->mutable_ad_deliver_info()->mutable_bidword_params()->set_quick_search(is_quick_search ? 1 : 0);
  }
  auto dark_source_type = is_quick_search ? kuaishou::ad::AdEnum::DARK_SOURCE_QUICK_SEARCH
                                          : kuaishou::ad::AdEnum::DARK_SOURCE_SEARCH;
  if (style_info.unit_support_info().search_population_retargeting() > 0) {
    dark_source_type = kuaishou::ad::AdEnum::DARK_SOURCE_UNIVERSE_SEARCH_WHITE;
  }
  item->mutable_ad_deliver_info()->mutable_ad_base_info()->set_dark_source_type_id(
      static_cast<int>(dark_source_type));
}

}  // namespace ks::front_server
