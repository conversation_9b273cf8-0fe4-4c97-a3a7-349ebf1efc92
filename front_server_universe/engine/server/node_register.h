#pragma once
#include "teams/ad/front_server_universe/engine/node/ad_pos_select.h"
#include "teams/ad/front_server_universe/engine/node/user_info_collection.h"
#include "teams/ad/front_server_universe/engine/node/forward_handler.h"
#include "teams/ad/front_server_universe/engine/node/forward_handler_wait.h"
#include "teams/ad/front_server_universe/engine/node/post_proc.h"
#include "teams/ad/front_server_universe/engine/node/universe_ac_fetcher.h"
#include "teams/ad/front_server_universe/engine/node/universe_data_postproc.h"
#include "teams/ad/front_server_universe/engine/node/universe_style_postproc.h"
#include "teams/ad/front_server_universe/engine/node/universe_query_rewrite_node.h"
#include "teams/ad/front_server_universe/engine/node/universe_request_rta_node.h"
#include "teams/ad/front_server_universe/engine/node/universe_antispam_node.h"
#include "teams/ad/front_server_universe/engine/node/ad_libretrieval_node.h"
#include "teams/ad/front_server_universe/engine/node/ad_libretrieval_wait.h"
#include "teams/ad/front_server_universe/engine/node/ad_libretrieval_parse_post.h"
#include "teams/ad/front_server_universe/engine/node/universe_request_quality_reckon_node.h"
#include "teams/ad/front_server_universe/engine/node/ad_style_forward_handler.h"
#include "teams/ad/front_server_universe/engine/node/ad_filter_node.h"
#include "teams/ad/front_server_universe/engine/node/universe_debug_node.h"
#include "teams/ad/front_server_universe/engine/node/universe_cpm_threshold.h"
#include "teams/ad/front_server_universe/engine/node/universe_auction.h"
#include "teams/ad/front_server_universe/engine/node/universe_bidding_node.h"
#include "teams/ad/front_server_universe/engine/node/universe_query_mock_node.h"
#include "teams/ad/front_server_universe/engine/node/universe_agg_scene_adjust_node.h"
#include "teams/ad/front_server_universe/engine/node/after_retrieval_init.h"
#include "teams/ad/front_server_universe/engine/node/universe_carousel_scene_adjust_node.h"
#include "teams/ad/engine_base/dragon_node/skydome_node.h"
#include "teams/ad/engine_base/dragon_node/rpc_client.h"
namespace ks {
namespace front_server {
// 下面是 dragon 框架依赖的
using base::JsonFactoryClass;
AD_NODE_REGISTER_TO_DRAGON(AdLibRetrievalNode);
AD_NODE_REGISTER_TO_DRAGON(AdLibRetrievalWait);
AD_NODE_REGISTER_TO_DRAGON(AdLibRetrievalParsePost);
AD_NODE_REGISTER_TO_DRAGON(ForwardHandler);
AD_NODE_REGISTER_TO_DRAGON(ForwardHandlerWait);
AD_NODE_REGISTER_TO_DRAGON(UniverseStylePostProc);
AD_NODE_REGISTER_TO_DRAGON(UniverseDataPostProc);
AD_NODE_REGISTER_TO_DRAGON(PostProc);
AD_NODE_REGISTER_TO_DRAGON(AdPosSelect);
AD_NODE_REGISTER_TO_DRAGON(UniverseAcFetcher);
AD_NODE_REGISTER_TO_DRAGON(UserInfoCollection);
AD_NODE_REGISTER_TO_DRAGON(UniverseAntispamNode);
AD_NODE_REGISTER_TO_DRAGON(UniverseRequestQualityReckonNode);
AD_NODE_REGISTER_TO_DRAGON(UniverseBiddingNode);
AD_NODE_REGISTER_TO_DRAGON(AdStyleForwardHandler);
AD_NODE_REGISTER_TO_DRAGON(UniverseQueryRewriteNode);
AD_NODE_REGISTER_TO_DRAGON(UniverseRequestRtaNode);
AD_NODE_REGISTER_TO_DRAGON(AdFilterNode);
AD_NODE_REGISTER_TO_DRAGON(UniverseDebugNode);
AD_NODE_REGISTER_TO_DRAGON(UniverseCpmThreshold);
AD_NODE_REGISTER_TO_DRAGON(UniverseAuction);
AD_NODE_REGISTER_TO_DRAGON(UniverseQueryMockNode);
AD_NODE_REGISTER_TO_DRAGON(UniverseAggSceneAdjustNode);
AD_NODE_REGISTER_TO_DRAGON(UniverseCarouselSceneAdjustNode);
AD_NODE_REGISTER_TO_DRAGON(AfterRetrievalInit);
FACTORY_REGISTER(JsonFactoryClass, AdRpcClientMixer, ks::engine_base::dragon::RpcClient);
}  // namespace front_server
}  // namespace ks
