#include "teams/ad/front_server_universe/engine/utils/ad_pack/base_info_pack/common_base_info_pack.h"

#include <vector>
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/util/check_reco_request_type.h"
#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/front_server_universe/engine/context_data/ad_common.h"
#include "teams/ad/front_server_universe/engine/utils/ad_pack/micros.h"
#include "teams/ad/front_server_universe/engine/utils/merchant/merchant_logic.h"
#include "teams/ad/front_server_universe/util/merchant_util/merchant_style_util.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
#include "teams/ad/engine_base/search/util/card_style/card_style_utiils.h"
#include "teams/ad/engine_base/p2p_cache_loader/industry_playlet_sdpa.h"
#include "teams/ad/engine_base/auto_bid/bid_trans_utils.h"
namespace ks {
namespace front_server {

bool CommonAdBaseInfoPack::Initialize() {
  if (creative_parser_ == nullptr) {
    LOG_EVERY_N(WARNING, 100) << "creative parser is nullptr";
    return false;
  }
  if (style_info_ == nullptr) {
    LOG_EVERY_N(WARNING, 100) << "style info is nullptr";
    return false;
  }
  if (ad_result_ == nullptr) {
    LOG_EVERY_N(WARNING, 100) << "ad result is nullptr";
    return false;
  }

  return true;
}

void CommonAdBaseInfoPack::Process() {
  if (!Initialize()) {
    return;
  }
  if (ad_result_->ad_source_type() != kuaishou::ad::BRAND &&
      ad_result_->ad_source_type() != kuaishou::ad::ADX &&
      !IsDpaAd(&(ad_result_->ad_deliver_info().ad_base_info()))) {
    // 品牌 和 adx 不在 front 请求 正排
    // dpa 有单独的正排
    SetAdBaseData();
    SetLiveData();
    SetPhotoId();
    SetInspireBonusTime();
    SetCoverData();
    SetAgentData();
  }
  SetClientStyleInfo();
  SetCommonBaseData();
}

void CommonAdBaseInfoPack::SetAdBaseData() {
  SHOW_STYLE_INFO(style_info_)
  const auto& target_bid = ad_result_->target_bid();

  auto *rank_result = GetAdRankResult(session_data_, *ad_result_);
  auto *rank_info = GetAdRankInfo(session_data_, *ad_result_);
  ad_base_info_->set_account_id(unit.account_id());
  ad_base_info_->set_campaign_id(unit.campaign_id());
  ad_base_info_->set_unit_id(unit.id());
  ad_base_info_->set_creative_id(creative.id());
  ad_base_info_->set_target_id(unit.target_id());
  ad_base_info_->set_bid(unit.bid());
  ad_base_info_->set_pic_id(creative.pic_id());
  if (account.ad_crm_account_operator_define_label().define_product().size() > 0) {
    ad_base_info_->set_define_product(account.ad_crm_account_operator_define_label().define_product());
  }

  if (unit.bid_type() == kuaishou::ad::AdEnum::CPM) {
    ad_base_info_->set_bid(ad_base_info_->bid() / 1000);
  }
  if (rank_result != nullptr) {
    ad_base_info_->set_auction_bid(rank_result->rank_bid_info().auction_bid());
  }
  // 加速探索增量广告使用 ad_common
  if (ad_common_ != nullptr && ad_common_->ad_price.is_increment_explore) {
    ad_base_info_->set_auction_bid(ad_common_->bid_info.auction_bid);
  }

  if (ad_result_->ad_source_type() == kuaishou::ad::FANS_TOP_V2) {
    // cpa_bid 在粉条 nobid 情况下不使用索引值覆盖
    if (ad_base_info_->bid_strategy() != kuaishou::ad::AdEnum_BidStrategy_SMART_BID_STRATEGY) {
      ad_base_info_->set_cpa_bid(unit.bid());
    }
    // TODO(shengmingyang): 粉条不调价的部分 auto_cpa_bid 为 0，用 bid 覆盖；
    // 有调价的部分 auto_cpa_bid 单位为分，在这里改成厘，给天枢用，未来最好是在前面统一
    if (ad_base_info_->auto_cpa_bid() <= 0) {
      ad_base_info_->set_auto_cpa_bid(unit.bid());
    } else {
      ad_base_info_->set_auto_cpa_bid(ad_base_info_->auto_cpa_bid() * 10);
    }
    // 写入频控标签  最终由 session_server 消费
    if (creative.extend_fields().freq_control_tag().size() > 0) {
      *(ad_base_info_->mutable_freq_control_tag()) =
              {creative.extend_fields().freq_control_tag().begin(),
              creative.extend_fields().freq_control_tag().end()};
    }
  } else if (ad_result_->ad_source_type() == kuaishou::ad::DSP) {
    if (creative.extend_fields().freq_control_tag().size() > 0) {
      // 流量助推频控
      *(ad_base_info_->mutable_freq_control_tag()) =
          {creative.extend_fields().freq_control_tag().begin(),
           creative.extend_fields().freq_control_tag().end()};
    }
  }
  ad_base_info_->set_ocpc_action_type(unit.ocpx_action_type());

  if (account.ad_crm_account_operator_define_label().define_product().size() > 0) {
    ad_base_info_->set_define_product(account.ad_crm_account_operator_define_label().define_product());
  }

  auto func_is_universe_opt = [&] (const std::string &str_resource_ids) -> bool {
    base::Json resource_json(StringToJson(str_resource_ids));
    if (resource_json.IsArray()) {
      for (auto *item : resource_json.array()) {
        int64_t id = item->IntValue(-1);
        if (id == engine_base::UNIVERSE_OPT_SCENE) {
          return true;
        }
      }
    }
    return false;
  };
  ad_base_info_->set_is_universe_opt(func_is_universe_opt(unit.resource_ids()));

  ad_base_info_->set_account_user_id(account.user_id());
  ad_base_info_->set_author_id(account.user_id());
  ad_base_info_->set_campaign_type(campaign.type());
  ad_base_info_->set_budget_smart_allocation(campaign.budget_smart_allocation());
  ad_base_info_->set_app_detail_type(creative.app_detail_type());
  ad_base_info_->set_h5_app(unit.h5_app());
  ad_base_info_->set_ocpc_stage(unit.ocpc_stage());
  ad_base_info_->set_account_type(account.account_type());
  ad_base_info_->set_licence_id_num(account.licence_id_num());
  ad_base_info_->set_create_source_type(campaign.create_source_type());
  if (unit.app_id() != 0 && style_info_->has_app_release()) {
    ad_base_info_->set_h5_app(app_release.h5_app());
    ad_base_info_->set_app_score(app_release.app_score());
  }
  // 该字段默认一定要设置为 true， 否则客户端不展示广告标
  ad_base_info_->set_show_ad_tag(true);
  ad_base_info_->set_industry_id(account.industry_id());
  ad_base_info_->set_cpc_type(unit.cpc_type());
  ad_base_info_->set_new_industry_id(account.new_industry_id());
  ad_base_info_->set_industry_id_v3(account.industry_id_v3());
  if ((ad_result_->ad_deliver_info().online_join_params_transparent().speed_type() ==
          kuaishou::ad::AdEnum::SPEED_NO_BID ||
      ad_base_info_->bid_type() == kuaishou::ad::AdEnum::MCB) ||
      (FrontKconfUtil::UaaGameAccountWhiteSet()->count(ad_base_info_->account_id()))) {
    // get from target
    ad_base_info_->set_roi_ratio(target_bid.roi_ratio());
    // 全站计划下的 nobid 单元不用 target 也不用正排出价，而是填充计划出价
    if (style_info_->unit().ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS &&
        style_info_->has_ecom_hosting_project()) {
      ad_base_info_->set_roi_ratio(style_info_->ecom_hosting_project().roi_ratio());
    }
  } else {  // get from index
    ad_base_info_->set_roi_ratio(unit.roi_ratio());
  }
  ad_base_info_->set_charge_action_type(GetChargeActionType(*style_info_, ad_result_));
  ad_base_info_->set_explore_bid_type(style_info_->unit_support_info().explore_bid_type());
  ad_base_info_->set_explore_put_type(style_info_->unit_support_info().explore_put_type());
  if (ad_common_ != nullptr) {
    ad_base_info_->set_explore_budget_status(ad_common_->is_in_acc_explore_status_aggr());
    ad_base_info_->set_auto_bid_explore(ad_common_->auto_bid_explore());
    ad_base_info_->set_auction_bid_explore_total(ad_common_->auction_bid_explore_total());
    ad_base_info_->set_rank_benefit_explore_origin(ad_common_->rank_benefit_explore_origin());
    ad_base_info_->set_is_increment_explore(ad_common_->ad_price.is_increment_explore);
    ad_base_info_->set_universe_explore_sample_type(ad_common_->base.universe_explore_sample_type);
  }
  ad_base_info_->set_live_order_source(style_info_->campaign_fanstop_support_info().live_order_source());
  if (ad_base_info_->photo_transform_type() ==  kuaishou::ad::AdEnum::UNKNOWN_REPLACY_TYPE) {
    ad_base_info_->set_photo_transform_type(kuaishou::ad::AdEnum::NO_REPLACE);
  }
  if (IsProgrammaticCreative(creative) &&
      !creative_parser_->GetMaterialId().empty()) {
    ad_base_info_->set_material_id(creative_parser_->GetMaterialId());
  }
  if (creative.item_material1() > 0) {
    ad_base_info_->set_item_material1(creative.item_material1());
  }

  ad_base_info_->set_description_title_id(creative_parser_->GetDescriptionTitleId());
  ad_base_info_->set_creative_material_type(creative_parser_->GetCreativeMaterialType());

  ad_base_info_->set_ad_request_times(session_data_->get_ad_request()->ad_user_session_info()
                                                        .ad_request_times());

  ad_base_info_->set_is_end_show(true);

  ad_base_info_->set_deep_conversion_type(target_bid.deep_conversion_type());
  if (ad_base_info_->deep_conversion_type() == 0) {
    if (FrontKconfUtil::fixDeepConversionTypeWhitelist()->count(ad_base_info_->account_id()) > 0) {
      ad_base_info_->set_deep_conversion_type(unit.deep_conversion_type());
    }
  }

  ad_base_info_->set_ry_ad_style(kuaishou::ad::AdBaseInfo::HARD_AD_STYLE);

  // adserver 传来部分商品 id 是错的，这里重新赋值
  if (style_info_->has_unit_small_shop_merchant_support_info()) {
    const auto& merchant_info = style_info_->unit_small_shop_merchant_support_info();
    const auto& item_type = merchant_info.item_type();
    if (item_type == AdEnum::MERCHANT_PUT_PRODUCT ||
        item_type == AdEnum::MERCHANT_PUT_LOCAL_PRODUCT) {
      ad_base_info_->set_merchant_product_id(std::to_string(merchant_info.item_id()));
    } else if (item_type == AdEnum::MERCHANT_LIVE_PROMOTE) {
      ad_base_info_->set_merchant_product_id(std::to_string(merchant_info.product_id()));
    }
  }
  if (ad_base_info_->merchant_product_id() == std::to_string(ad_base_info_->author_id())) {
    ad_base_info_->clear_merchant_product_id();
  }

  // 综合电商打印创意层级关联的商品和商品库
  if (merchant_util::IsCompEcomAds(account.new_industry_id(), account.industry_id_v3())) {
    ad_base_info_->set_merchant_product_id(creative.merchant_product_id());
    ad_base_info_->set_merchant_library_id(creative.merchant_library_id());
  }
  ad_base_info_->set_taobao_url_type(unit.taobao_url_type());
  ad_base_info_->set_explore_ext_type(unit.unit_support_info().explore_ext_type());
  ad_base_info_->set_first_industry_id_v3(
      ad_result_->ad_deliver_info().online_join_params_transparent().first_industry_id_v3());
  if (ad_base_info_->first_industry_id_v3() == 0
    && style_info_->has_industry_v3()) {
    ad_base_info_->set_first_industry_id_v3(style_info_->industry_v3().parent_id());
  }

  // 搜索广告 关联作者召回
  if (session_data_->get_pos_manager().IsSearchRequest()) {
    const auto &relevant_author = ad_result_->ad_deliver_info()
                                      .online_join_params_transparent()
                                      .ad_target_trans_info()
                                      .search_query_info()
                                      .relevant_author();
    ad_base_info_->mutable_search_relevant_author()->CopyFrom(relevant_author);
  }

  ad_base_info_->set_simultaneous_optimization_selected(
    unit_support_info.simultaneous_optimization_selected());
  ad_base_info_->set_simultaneous_optimization_type(unit_support_info.simultaneous_optimization_type());
  ad_base_info_->set_scene_oriented_type(style_info_->campaign().scene_oriented_type());
  auto &feed_flow_pos_premium = FrontKconfUtil::adPosPremium()->data().feed_flow_pos_premium;
  auto &thanos_flow_pos_premium = FrontKconfUtil::adPosPremium()->data().thanos_flow_pos_premium;
  ad_base_info_->set_enable_pos_premium(unit_support_info.extend_fields().pos_premium_flag());
  if (feed_flow_pos_premium.count(session_data_->get_page_id())) {
    ad_base_info_->set_premium_flow_type(kuaishou::ad::AdEnum_PremiumPosType_FEED_HIGH_QUALITY_FLOW);
    ad_base_info_->set_premium_coefficient(unit_support_info.extend_fields().feed_premium_coefficient());
  } else if (thanos_flow_pos_premium.count(session_data_->get_page_id())) {
    ad_base_info_->set_premium_flow_type(kuaishou::ad::AdEnum_PremiumPosType_THANOS_HIGH_QUALITY_FLOW);
    ad_base_info_->set_premium_coefficient(unit_support_info.extend_fields().thanos_premium_coefficient());
  } else {
    ad_base_info_->set_premium_flow_type(kuaishou::ad::AdEnum_PremiumPosType_OTHERS_FLOW);
    ad_base_info_->set_premium_coefficient(1.0);
  }
  // 套餐相关
  if (style_info_->campaign().promotion_type() == kuaishou::ad::AdEnum::SPECIALTY_PROMOTION
      || style_info_->campaign().promotion_type() == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
    if (style_info_->campaign().combo_order_id() > 0) {
      ad_base_info_->set_combo_order_id(style_info_->campaign().combo_order_id());
      ad_base_info_->set_combo_type(kuaishou::ad::AdEnum::COMBO_DAILY_LIVE_STREAM);
      ad_base_info_->set_ad_sub_source_type(kuaishou::ad::COMBO_ORDER);
    }
  }
  if (style_info_->account().account_type() == AdEnum::ACCOUNT_SOCIAL) {
    ad_base_info_->set_ad_sub_source_type(kuaishou::ad::AD_SOCIAL_ORDER);
  }
  if (style_info_->account().account_type() == AdEnum::ACCOUNT_FANSTOP_TEMU) {
    ad_base_info_->set_ad_sub_source_type(kuaishou::ad::FANS_TOP_TEMU);
    ad_base_info_->set_ks_order_id(
        style_info_->unit_support_info().ks_order_id());
  }
  if ((style_info_->campaign().type() == AdEnum::KWAI_PROMOTION_CONSULTATION &&
        style_info_->campaign().charge_mode() == AdEnum::ORDER)
      || (style_info_->campaign().type() == AdEnum::KWAI_PROMOTION_LOCAL_STORE_ORDER)) {
    ad_base_info_->set_ad_sub_source_type(kuaishou::ad::BUSINESS_FANSTOP);
  }

  if (style_info_->account().account_type() == AdEnum::ACCOUNT_SELF_SERVICE) {
    ad_base_info_->set_ad_sub_source_type(kuaishou::ad::DSP_SELF_SERVICE);
    ad_base_info_->set_ks_order_id(
        style_info_->campaign_fanstop_support_info().ks_order_id());
  }

  if (style_info_->campaign().type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
    ad_base_info_->set_series_id(style_info_->unit_support_info().series_id());
    if (SPDM_enable_playlet_inner_rpc(session_data_->get_spdm_ctx())) {
      ad_base_info_->set_is_playlet_inner(1);
    }
  }

  // 磁力金牛移动端重构项目二期，新增 PC 端订单投放和移动端计划投放组合
  if (style_info_->account().account_type() == AdEnum::ACCOUNT_ESP &&
      ad_base::IsFanstopCampaign(style_info_->campaign().type())) {
    ad_base_info_->set_ad_sub_source_type(kuaishou::ad::ESP_MOBILE);
  }
  if (style_info_->account().account_type() == AdEnum::ACCOUNT_ESP_MOBILE &&
      (style_info_->campaign().type() == AdEnum::MERCHANT_RECO_PROMOTE
      || style_info_->campaign().type() == AdEnum::LIVE_STREAM_PROMOTE)) {
    ad_base_info_->clear_ad_sub_source_type();
  }

  if (style_info_->account().account_type() == AdEnum::ACCOUNT_ESP_MOBILE &&
      style_info_->campaign().charge_mode() == AdEnum::ORDER &&
      (style_info_->campaign().type() == AdEnum::MERCHANT_RECO_PROMOTE
       || style_info_->campaign().type() == AdEnum::LIVE_STREAM_PROMOTE)) {
    ad_base_info_->set_ad_sub_source_type(kuaishou::ad::ESP_MOBILE);
  }
  if (style_info_->account().account_type() == AdEnum::ACCOUNT_ESP &&
      style_info_->campaign().charge_mode() == AdEnum::ORDER &&
      (style_info_->campaign().type() == AdEnum::MERCHANT_RECO_PROMOTE
       || style_info_->campaign().type() == AdEnum::LIVE_STREAM_PROMOTE)) {
    ad_base_info_->set_ad_sub_source_type(kuaishou::ad::ESP_MOBILE);
  }
  auto* mutable_effect_prediction = ad_base_info_->mutable_effect_prediction();
  mutable_effect_prediction->set_min_conversion_cnt(campaign.extend_fields().effect_prediction().min());
  mutable_effect_prediction->set_max_conversion_cnt(campaign.extend_fields().effect_prediction().max());
  mutable_effect_prediction->set_timestamp(campaign.extend_fields().effect_prediction().timestamp());

  if (rank_result != nullptr) {
    ad_base_info_->set_mcb_cpa_bid(rank_result->ad_rank_trans_info().mcb_cpa_bid());
    ad_base_info_->set_mcb_roi_ratio(rank_result->ad_rank_trans_info().mcb_roi_ratio());
    ad_base_info_->set_performance_fix_ratio(rank_result->ad_rank_trans_info().performance_fix_ratio());
    ad_base_info_->set_constraint_action_type(rank_result->ad_rank_trans_info().constraint_action_type());
    ad_base_info_->set_constraint_r(rank_result->ad_rank_trans_info().constraint_r());
    ad_base_info_->set_constraint_cpa(rank_result->ad_rank_trans_info().constraint_cpa());
    ad_base_info_->set_constraint_roi(rank_result->ad_rank_trans_info().constraint_roi());
    ad_base_info_->set_budget_coef(rank_result->ad_rank_trans_info().budget_coef());
    ad_base_info_->set_cpa_coef(rank_result->ad_rank_trans_info().cpa_coef());
    ad_base_info_->set_unify_ltv(rank_result->ad_rank_trans_info().unify_ltv());
    if (campaign.type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
        ad_base_info_->set_unify_ltv(rank_result->predict_origin_info().merchant_ltv());
    }
    ad_base_info_->set_is_account_bidding(rank_result->ad_rank_trans_info().is_account_bidding());
    ad_base_info_->set_bid_coef(rank_result->ad_rank_trans_info().bid_coef());
    ad_base_info_->set_price_ratio(rank_result->ad_rank_trans_info().auto_bid_ratio());
    ad_base_info_->set_aggre_key(rank_result->ad_rank_trans_info().aggre_key());
    if (style_info_->campaign().auto_manage()) {
      ad_base_info_->set_gimbal_type(rank_result->ad_rank_trans_info().gimbal_type());
      ad_base_info_->set_gimbal_ratio(rank_result->ad_rank_trans_info().gimbal_ratio());
    }
    // 内循环透传 gimbal_ratio 到 adpack
    if (rank_result->ad_rank_trans_info().gimbal_type() == 2 &&
        rank_result->ad_rank_trans_info().gimbal_ratio() > 0) {
      ad_base_info_->set_gimbal_type(rank_result->ad_rank_trans_info().gimbal_type());
      ad_base_info_->set_gimbal_ratio(rank_result->ad_rank_trans_info().gimbal_ratio());
    }
    {
      ad_base_info_->set_client_second_industry_name_city_hash(
              rank_result->client_second_industry_name_city_hash());
    }
  }
  const kuaishou::ad::AdBidTransInfo& ad_bid_trans_info =
          ad_result_->ad_deliver_info().online_join_params_transparent()
                    .ad_target_trans_info().ad_bid_trans_info();
  double bid_gimbal_ratio = engine_base::GetBidTransInfoValueWithDefault<double>(
    ad_bid_trans_info, "bid_gimbal_ratio", 0.0);
  int32_t bid_gimbal_type = engine_base::GetBidTransInfoValueWithDefault<int32>(
    ad_bid_trans_info, "bid_gimbal_type", 0);
  if (ad_base_info_->account_id() % 100 < SPDM_bidGimbalTailNumber() &&
    bid_gimbal_ratio > 0.01) {
      if (bid_gimbal_type > 0) {
        ad_base_info_->set_gimbal_type(bid_gimbal_type);
      } else {
        ad_base_info_->set_gimbal_type(0);
      }
      ad_base_info_->set_gimbal_ratio(bid_gimbal_ratio);
  }

  ad_base_info_->set_corporation_name(style_info_->account().corporation_name());
  ad_base_info_->set_pic_id(creative.pic_id());
  ad_base_info_->set_charge_mode(style_info_->campaign().charge_mode());
  ad_base_info_->set_account_create_source(account.create_source());
  std::string ocpx_str =
    absl::StrCat(static_cast<int64_t>(style_info_->unit().ocpx_action_type()));
  if (style_info_->account().extend_fields().adjust_price_caliber_info_size() > 0) {
    for (const auto& adjust_price_caliber_item :
        style_info_->account().extend_fields().adjust_price_caliber_info()) {
      if (adjust_price_caliber_item.shadow_opt_goal() == ocpx_str) {
        ad_base_info_->set_price_caliber_type(
            adjust_price_caliber_item.price_caliber_type());
        session_data_->dot_perf->Count(1, "adjust_price_caliber",
            ocpx_str, absl::StrCat(adjust_price_caliber_item.price_caliber_type()));
        break;
      }
    }
  }
  ad_base_info_->set_twin_bid_priority(style_info_->unit_support_info().twin_bid_priority());
  session_data_->dot_perf->Count(1, "twin_bid_priority",
      absl::StrCat(style_info_->unit_support_info().twin_bid_priority()));
  if (!IsInnerLoopCampaign(*style_info_)) {
    // 外循环可以跳转 p 页的广告：行业直播、助推、dsp 原生
    if ((campaign.type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE ||
        campaign.type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE ||
        campaign.type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
        creative.outer_loop_native() == kuaishou::ad::AdEnum_OuterLoopNative_OPEN_NATIVE)
        && account.account_type() != kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_SELF_SERVICE) {
      ad_base_info_->set_is_outer_jump_profile_ad(true);
    }
  }
  if (creative.outer_loop_native() == kuaishou::ad::AdEnum_OuterLoopNative_OPEN_NATIVE &&
      style_info_->creative().creative_support_info().kol_user_id() > 0 &&
      style_info_->creative().creative_support_info().kol_user_id() !=
      style_info_->creative().creative_support_info().photo_author_id()) {
    ad_base_info_->set_is_kol_ad(true);
  }

  ad_base_info_->set_marketing_objective(style_info_->campaign().marketing_objective());
  ad_base_info_->set_delivery_scenario(style_info_->campaign().delivery_scenario());
  ad_base_info_->set_delivery_method(style_info_->campaign().delivery_method());
  ad_base_info_->set_managed_strategy(style_info_->campaign().managed_strategy());
  ad_base_info_->set_dsp_version(style_info_->campaign().dsp_version());

  {
    ad_base_info_->set_minigame_app_id("");
    ad_base_info_->set_minigame_from_type("");
    ad_base_info_->set_minigame_from_type_v2("");
    if (style_info_->has_mini_app()) {
      ad_base_info_->set_mini_app_id_platform(style_info_->mini_app().mini_app_id_platform());
      session_data_->dot_perf->Count(1, "mini_app_id_platform");
    }
    // 临时兼容白名单逻辑 通过 schema_uri 字段获取 appId
    // schema_url : kwai://sogame/launch?appId=ks713820544773120544&from=dsp_kuaishou_default_w
    // 当满足 1、 mini_app_id_platform 为空
    //        2、 营销目标为小程序
    //        3、 schema_uri 不为空
    // 做兜底兼容逻辑
    if (ad_base_info_->mini_app_id_platform().empty() &&
        campaign.type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE &&
        !unit.schema_uri().empty()) {
      session_data_->dot_perf->Count(1, "empty_mini_app_id_platform");
      std::vector<std::string> uri_parts =
        absl::StrSplit(unit.schema_uri(), "appId=", absl::SkipEmpty());
      if (uri_parts.size() > 1) {
        std::vector<std::string> app_id_parts =
             absl::StrSplit(uri_parts[1], "&", absl::SkipEmpty());
        if (app_id_parts.size() && app_id_parts[0].size() > 2 &&
            app_id_parts[0].substr(0, 2) == "ks") {
          ad_base_info_->set_mini_app_id_platform(app_id_parts[0]);
          session_data_->dot_perf->Count(1, "compat_mini_app_id_platform");
        }
      }
    }
  }
  ad_base_info_->set_ad_type(style_info_->campaign().ad_type());

  // 剧量计划下发剧粒度信息
  auto playlet_sdpa_ = ks::engine_base::IndustryPlayletSdpaIns().GetData();
  if (playlet_sdpa_ && playlet_sdpa_.get()) {
    auto playlet_sdpa_ptr_ = playlet_sdpa_->find(ad_base_info_->unit_id());
    if (playlet_sdpa_ptr_ != playlet_sdpa_->end()) {
      auto& playlet_sdpa_data = playlet_sdpa_ptr_->second;
      static constexpr int64_t AUTO_BID_TABLE_HASH_CODE = (1UL << 63) - 1;
      int64_t tube_name_hash = base::CityHash64(playlet_sdpa_data.playlet_name.c_str(),
                                                playlet_sdpa_data.playlet_name.length());
      tube_name_hash = tube_name_hash & AUTO_BID_TABLE_HASH_CODE;
      ad_base_info_->set_tube_name_hash(tube_name_hash);
      if (rank_info != nullptr) {
        ad_base_info_->set_tube_status(rank_info->tube_status());
      }
      session_data_->dot_perf->Count(1, "debug_p2p_info", "IndustryPlayletSdpaIns",
          "has_value");
    } else {
      session_data_->dot_perf->Count(1, "debug_p2p_info", "IndustryPlayletSdpaIns",
          "empty");
    }
  }
  session_data_->dot_perf->Count(1, "debug_p2p_info", "IndustryPlayletSdpaIns",
      "empty");

  // playlet smart offers
  const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
  if (ad_data_v2 != nullptr) {
    ad_base_info_->set_has_smart_offer(ad_data_v2->has_smart_offer());
    ad_base_info_->set_smart_offer_value(ad_data_v2->smart_offer_value());
  }
  if (KS_LIKELY(ad_data_v2 != nullptr)) {
    ad_base_info_->set_display_ecpm(ad_data_v2->display_ecpm());
  }
  ad_base_info_->set_kwai_book_id(unit_support_info.kwai_book_id());
  bool is_skip_qcpx_mode = session_data_->get_is_closure_flow() &&
      session_data_->is_closure_ad(campaign.type(), unit.ocpx_action_type());
  if (!is_skip_qcpx_mode) {
    ad_base_info_->set_qcpx_put_type(style_info_->unit().unit_support_info().qcpx_put_type());
    if (rank_result != nullptr) {
      ad_base_info_->set_coupon_scope(rank_result->ad_rank_trans_info().coupon_scope());
      ad_base_info_->set_coupon_strategy_type(rank_result->ad_rank_trans_info().coupon_strategy_type());
    }
  }

  session_data_->dot_perf->Count(1, "sub_conversion_path", "set_ad_base_data");
}   // NOLINT

void CommonAdBaseInfoPack::SetCoverData() {
  ad_base_info_->set_cover_id(creative_parser_->GetCoverId());
  ad_base_info_->mutable_cover_size()->CopyFrom(creative_parser_->GetCoverSize());
  ad_base_info_->set_cover_url_backup(creative_parser_->GetCoverUrlBackup());
  ad_base_info_->set_dup_cover_id(creative_parser_->GetDupCoverId());
  ad_base_info_->set_bg_cover_id(creative_parser_->GetBgCoverId());
  ad_base_info_->set_cover_sticker_style_id(creative_parser_->GetCoverStickerStyleId());
  ad_base_info_->set_cover_title_id(creative_parser_->GetCoverTitleId());
}

void CommonAdBaseInfoPack::SetAgentData() {
  SHOW_STYLE_INFO(style_info_)
  if (campaign.type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
      IsNonMerchantLivePromote(*style_info_)) {
    if (FrontKconfUtil::enableLiveAgent()) {
      ad_base_info_->set_put_type(unit_support_info.put_type());
      ad_base_info_->set_author_id(unit_support_info.live_user_id());
    }
    return;
  }

  ad_base_info_->set_put_type(unit_support_info.put_type());
  ad_base_info_->set_author_id(GetAuthorID(session_data_, *style_info_));
  ad_base_info_->set_sub_put_type(unit_support_info.sub_put_type());
}

void CommonAdBaseInfoPack::SetInspireBonusTime() {
  int32 bonus_time = 0;
  // 运营激励视频单独配置
  if (ks::ad_base::IsOperationInspire(*(session_data_->get_ad_request()))) {
    ad_base_info_->mutable_inspire_ad_info()->set_inspire_ad_bill_time(
                                    FrontKconfUtil::operationInspireAdBillTime());
  }
  if (ks::ad_base::IsInspireLive(session_data_->get_sub_page_id(), session_data_->get_ad_request())) {
    ad_base_info_->set_bonus_time(60 * 1000);
  }
}

void CommonAdBaseInfoPack::SetCommonBaseData() {
  SHOW_STYLE_INFO(style_info_)
  const auto& target_bid = ad_result_->target_bid();

  // 设置托管相关字段
  ad_base_info_->set_hosting_cpa_bid(style_info_->hosting_project().cpa_bid());
  ad_base_info_->set_hosting_roi_ratio(style_info_->hosting_project().roi_ratio());
  ad_base_info_->set_hosting_scene(style_info_->hosting_project().hosting_scene());

  // 设置 support_project_info
  ad_base_info_->set_support_project_id(
      session_data_->get_ad_request()->support_project_info().support_project_id());
  // 下发广告主推广的 app_id，用于归因
  ad_base_info_->set_ad_app_id(app_release.app_id());
  if (ad_base_info_->ad_source_type() != kuaishou::ad::AdSourceType::BRAND) {
    ad_base_info_->set_ad_package_id(app_release.package_id());
  }
  ad_base_info_->set_merchandise_id(unit.unit_support_info().merchandise_id());
  ad_base_info_->set_merchandise_type(unit.unit_support_info().merchandise_type());

  // set rta_feature_id
  ad_base_info_->set_rta_feature_id(
    ad_result_->ad_deliver_info().online_join_params_transparent().rta_feature_id());

  ad_base_info_->set_ad_source_type(ad_result_->ad_source_type());
  ad_base_info_->set_bid_server_id(target_bid.bid_server_id());
  ad_base_info_->set_speed_type(ad_result_->ad_deliver_info().online_join_params_transparent().speed_type());
  ad_base_info_->set_ad_gap_type(GetMaSourceType(*ad_result_));
  if (ad_result_->ad_source_type() == kuaishou::ad::FANS_TOP_V2) {
    auto *fanstop_ext = ad_base_info_->mutable_fanstop_ext_info();
    fanstop_ext->set_is_fanstop_inner_operation(
                      style_info_->campaign_fanstop_support_info().is_inner_delivery());
    fanstop_ext->set_is_new_fanstop_inner(style_info_->campaign_fanstop_support_info().is_new_inner());
    fanstop_ext->set_priority_level(
        style_info_->unit_fanstop_support_info().priority_level());
    if (style_info_->campaign_fanstop_support_info().order_type() ==
            kuaishou::ad::AdEnum_EspOrderType_MERCHANT_OPERATE_ORDER_TYPE) {
      fanstop_ext->set_is_new_fanstop_merchant_inner(true);
    }
  }

  // 不弹窗实验
  ad_base_info_->set_alert_net_mobile(true);
  if (ad_result_->ad_source_type() == kuaishou::ad::DSP) {
    ad_base_info_->set_alert_net_mobile(false);
  }
  // 智能托管回传标记
  ad_base_info_->set_unit_source_type(unit.unit_source_type());
  // 回传智能平台 2.0 字段
  ad_base_info_->set_auto_manage(campaign.auto_manage());
  ad_base_info_->set_auto_adjust(campaign.auto_adjust());
  ad_base_info_->set_auto_build(campaign.auto_build());
  if (ad_result_->ad_source_type() != kuaishou::ad::ADX) {
    ad_base_info_->set_is_strict_native_ad(IsStrictNativeAd(session_data_, *style_info_));
  }
  if (account.account_support_info().account_auto_manage() ==
      kuaishou::ad::AdEnum::ACCOUNT_AUTO_MANAGE_OPEN) {
    ad_base_info_->set_account_auto_manage(account.account_support_info().account_auto_manage());
    ad_base_info_->set_auto_manage_type(account.account_support_info().auto_manage_type());
    if (account.account_support_info().auto_manage_type() == kuaishou::ad::AdEnum::AUTO_MANAGE_DEFAULT) {
      if (account.account_support_info().extend_fields().constraint_info_ocpx_action_type_size() > 0 &&
          account.account_support_info().extend_fields().constraint_info_ocpx_action_type_size() ==
              account.account_support_info().extend_fields().constraint_info_value_size()) {
        int i = 0;
        for (; i < account.account_support_info().extend_fields().constraint_info_ocpx_action_type_size();
             ++i) {
          if (account.account_support_info().extend_fields().constraint_info_ocpx_action_type(i) ==
              static_cast<int64_t>(unit.ocpx_action_type())) {
            break;
          }
        }
        if (i < account.account_support_info().extend_fields().constraint_info_value_size()) {
          ad_base_info_->set_account_constraint_value(
              account.account_support_info().extend_fields().constraint_info_value(i));
        }
      }
    } else if (static_cast<kuaishou::ad::AdEnum::AutoManageType>(
                   style_info_->auax_simple_project().auto_manage_type()) ==
               account.account_support_info().auto_manage_type()) {
      if (style_info_->auax_simple_project().parse_fields().ocpx_action_type_constraint_type_size() > 0 &&
          style_info_->auax_simple_project().parse_fields().ocpx_action_type_constraint_type_size() ==
              style_info_->auax_simple_project().parse_fields().ocpx_action_type_constraint_value_size()) {
        int i = 0;
        for (; i < style_info_->auax_simple_project().parse_fields().ocpx_action_type_constraint_type_size();
             ++i) {
          if (style_info_->auax_simple_project().parse_fields().ocpx_action_type_constraint_type(i) ==
              static_cast<int64_t>(unit.ocpx_action_type())) {
            break;
          }
        }
        if (i < style_info_->auax_simple_project().parse_fields().ocpx_action_type_constraint_value_size()) {
          ad_base_info_->set_auax_constraint_value(
              style_info_->auax_simple_project().parse_fields().ocpx_action_type_constraint_value(i));
        }
      }
    }
  }
  ad_base_info_->set_periodic_delivery_type(campaign.periodic_delivery_type());
  ad_base_info_->set_campaign_begin_time(campaign.begin_time());
  ad_base_info_->set_campaign_end_time(campaign.end_time());
  ad_base_info_->set_periodic_days(campaign.periodic_days());
  {
    int64 mini_series_id = session_data_->common_r_->GetIntCommonAttr("mini_series_id").value_or(0);
    ad_base_info_->set_mini_series_id(mini_series_id);
  }
  {
    int64 mini_series_author_id = session_data_->common_r_->GetIntCommonAttr("mini_series_author_id").value_or(0); // NOLINT
    ad_base_info_->set_mini_series_author_id(mini_series_author_id);
  }
}

void CommonAdBaseInfoPack::SetClientStyleInfo() {
  // 落地页前置引导增强，配置相应的 action bar 的展开时机，和显示文案(老样式)
  std::string NLPDisplayInfo = "推荐";
  int32_t NLPShowTime = 3000;
  int32_t NLPPopUpPercent = 30;
  ad_base_info_->mutable_client_style_info()->mutable_landing_page_actionbar_info()
                                              ->set_action_bar_display_info(NLPDisplayInfo);
  ad_base_info_->mutable_client_style_info()->mutable_landing_page_actionbar_info()
                                              ->set_action_bar_show_time(NLPShowTime);
  ad_base_info_->mutable_client_style_info()->mutable_landing_page_actionbar_info()
                                              ->set_pop_up_percent(NLPPopUpPercent);
}

void CommonAdBaseInfoPack::SetLiveData() {
  SHOW_STYLE_INFO(style_info_)

  // 默认短视频
  ad_base_info_->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO);
  if (creative.live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE ||
      IsNonMerchantPhotoToLiveStream(*style_info_)) {
    ad_base_info_->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE);
  } else if (creative.live_creative_type() ==
                  kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE ||
      IsNonMerchantLiveStream(*style_info_)) {
    ad_base_info_->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_LIVE);
  } else if (creative.live_creative_type() ==
                  kuaishou::ad::AdEnum_LiveCreativeType_PIC_TO_LIVE_STREAM_CREATIVE_TYPE) {
    ad_base_info_->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE);
  }

  ad_base_info_->set_is_account_live(style_info_->live_stream_user_info().is_live());

  if (campaign.type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
      style_info_->has_unit_support_info()) {
    ad_base_info_->set_conversion_type(ConvertConversionType(unit_support_info.conversion_type()));
  }
  if (campaign.type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
    ad_base_info_->set_server_exp_tag(GetServerExpTagv2(*style_info_, session_data_->mutable_ad_request(),
                                                        session_data_->get_front_server_request()->type(),
                                                        session_data_->get_llsid()));
    if (account.account_type() == kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP) {
      ad_base_info_->set_promotion_type(campaign.promotion_type());
      if (style_info_->has_unit_small_shop_merchant_support_info()) {
        ad_base_info_->set_merchant_item_put_type(
                      style_info_->unit_small_shop_merchant_support_info().item_type());
      }
    }
  }

  if (campaign.type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
      style_info_->live_stream_user_info().is_live()) {
    ad_base_info_->set_live_stream_id(style_info_->live_stream_user_info().live_stream_id());
    falcon::Inc("front_server.style_set_photo_live_id");
  }
  if (campaign.type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
      IsNonMerchantLivePromote(*style_info_)) {
    ad_base_info_->set_live_stream_id(style_info_->live_stream_user_info().live_stream_id());
    ad_base_info_->set_ext_type(1);
    ad_base_info_->set_server_exp_tag(GetServerExpTagv2(*style_info_, session_data_->mutable_ad_request(),
                                                        session_data_->get_front_server_request()->type(),
                                                        session_data_->get_llsid()));
    ad_base_info_->set_destination_type(kuaishou::ad::AdEnum_JumpDestinationType_JUMP_DESTINATION_LIVE_ROOM);
  } else if (campaign.type() ==
                  kuaishou::ad::AdEnum_CampaignType_DETAIL_LIVE_STREAM_PROMOTE_CAMPAIGN) {
    ad_base_info_->set_live_stream_id(style_info_->live_stream_user_info().live_stream_id());
    ad_base_info_->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_LIVE);
  }
  // 联盟 esp 图文特殊处理
  bool is_esp_image =
      (campaign.type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
        campaign.type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) &&
      (creative.creative_material_type() == kuaishou::ad::AdEnum::VERTICAL_IMAGE||
        creative.creative_material_type() == kuaishou::ad::AdEnum::HORIZONTAL_IMAGE);
  if (is_esp_image) {
    ad_base_info_->set_server_exp_tag(GetServerExpTagForUniverseEspImage(
        *style_info_, session_data_->mutable_ad_request(),
        session_data_->get_front_server_request()->type(), session_data_->get_llsid()));
  }

  if (IsFansTopLive(*style_info_)) {
    if (style_info_->unit_fanstop_support_info().item_type() > kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE) {  // NOLINT
      ad_base_info_->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO);
    } else {
      ad_base_info_->set_item_type(style_info_->unit_fanstop_support_info().item_type());
    }
    ad_base_info_->set_live_stream_id(style_info_->live_stream_user_info().live_stream_id());
  }

  if (campaign.type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE
      && style_info_->live_stream_user_info().is_live()) {
    ad_base_info_->set_live_stream_id(style_info_->live_stream_user_info().live_stream_id());
    if (creative.live_creative_type()
          == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT ) {
      ad_base_info_->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_LIVE);
    } else {
      ad_base_info_->set_item_type(kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE);
    }
  }
}

void CommonAdBaseInfoPack::SetPhotoId() {
  SHOW_STYLE_INFO(style_info_)

  uint64_t photo_id = creative_parser_->GetPhotoId();

  if (creative.live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE
      || creative.live_creative_type()
        == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
    photo_id = ad_base_info_->photo_id();
    if (creative.live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
      GetFakePhotoIdToLive(creative.live_creative_type(), &photo_id);
    }
  }

  if (style_info_->has_material() &&
      style_info_->material().id() > 0 &&
      style_info_->material().photo_id() > 0) {
    photo_id = style_info_->material().photo_id();
  }

  // 程序化创意 1.0 从 delivery_package 中获取
  // 程序化创意 2.0 从 creative 中获取
  ad_base_info_->set_photo_id(photo_id);
  ad_base_info_->set_dup_photo_id(creative_parser_->GetDupPhotoId());
  ad_base_info_->set_dup_photo_id_a(creative_parser_->GetDupPhotoIdA());
  ad_base_info_->set_dup_photo_id_b(creative_parser_->GetDupPhotoIdB());
  auto replace_photo_id =
    ad_result_->ad_deliver_info().online_join_params_transparent().ad_target_trans_info().replace_photo_id();
  if (replace_photo_id > 0) {
    ad_base_info_->set_counterpart_photo_id(replace_photo_id);
    session_data_->dot_perf->Count(1, "replace_photo_id");
  }
}

kuaishou::ad::AdActionType CommonAdBaseInfoPack::GetChargeActionType(
                                                const StyleInfoItem &style_info,
                                                kuaishou::ad::AdResult *item) {
  auto* base_info = item->mutable_ad_deliver_info()->mutable_ad_base_info();
  auto is_direct_ecom = [&] () -> bool {
    constexpr int ECOM_INDUSTRY_ID_V3 = 1022;  // LOCAL STATIC FOR UNITY BUILD
    return style_info.ad_industry().parent_id() == ECOM_INDUSTRY_ID_V3;
  };

  if (is_direct_ecom() && item->ad_deliver_info().ad_base_info().is_assemble_page()) {
    return kuaishou::ad::AD_DELIVERY_NO_CHARGE;
  }
  // adx 的 unit 不参与后面的试验，要不和第三方会有 gap
  if (item->ad_source_type() == kuaishou::ad::ADX) {
    return GetAdxChargeActionType(base_info);
  }
  // 对于 amd ocpc 和 cpc 的计费点设在 p3s
  if ((style_info.creative().live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE ||
      IsNonMerchantLiveStream(style_info)) &&
      (base_info->bid_type() == kuaishou::ad::AdEnum::CPC ||
        base_info->bid_type() == kuaishou::ad::AdEnum::OCPC)) {
    return kuaishou::ad::AD_LIVE_PLAYED_3S;
  }
  if (base_info->bid_type() == kuaishou::ad::AdEnum::CPA) {
    return GetChargeActionType(base_info->bid_type(),
                               base_info->ocpc_action_type(),
                               base_info->campaign_type(),
                               base_info->item_type());
  }
  // 买首改计费方式
  if (session_data_->get_pos_manager().IsBuyerHomePageTraffic()) {
    if (base_info->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO) {
      return kuaishou::ad::AdActionType::AD_PHOTO_IMPRESSION;
    }
    if (base_info->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
      return kuaishou::ad::AdActionType::AD_LIVE_IMPRESSION;
    }
  }
  // 商城改计费方式
  if (session_data_->get_pos_manager().IsMallTraffic()) {
    if (base_info->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO) {
      return kuaishou::ad::AdActionType::AD_PHOTO_IMPRESSION;
    }
    if (base_info->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
      return kuaishou::ad::AdActionType::AD_LIVE_IMPRESSION;
    }
  }
  // 万合改计费方式
  if (session_data_->get_enable_wanhe_charge_action_type_subpage_id() &&
      session_data_->get_pos_manager().IsWanhe()) {
    // 修复之前生效了 CPC 广告，且考虑新接入的软广
    if (base_info->bid_type() == kuaishou::ad::AdEnum::OCPM_DSP ||
        base_info->bid_type() == kuaishou::ad::AdEnum::MCB) {
      return kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION;
    }
  }
  // delivery 计费有一定危险，用计算 auction_bid 时所标记的字段判断
  // CPC2 不会进入这里，因为 bid type 已经修改为了 cpc2
  // 依赖 rank_server 返回结果
  auto *p_rank_result = GetAdRankResult(session_data_, *item);
  if (p_rank_result) {
    // 联盟不走这里
    if (p_rank_result->rank_base_info().is_server_show_ocpm()) {
      return kuaishou::ad::AdActionType::AD_DELIVERY;
    }
  }
  if (base_info->is_server_show_ocpm_test()) {
    return kuaishou::ad::AdActionType::AD_DELIVERY;
  }
  // 联盟分支
  if (base_info->bid_type() == kuaishou::ad::AdEnum::OCPM_DSP ||
       base_info->bid_type() == kuaishou::ad::AdEnum::MCB) {
    if (ks::ad_base::GetMediumAttribute(*(session_data_->get_ad_request())) == 2 ||
        ks::ad_base::GetMediumAttribute(*(session_data_->get_ad_request())) == 4) {
      falcon::Inc("front_server.universe_charge_client_show");
      return kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION;
    }
    falcon::Inc("front_server.charge_client_show");
    return kuaishou::ad::AdActionType::AD_PHOTO_IMPRESSION;
  }
  // CPM(nizhihao) 出价, 单双列有差异
  if (base_info->bid_type() == kuaishou::ad::AdEnum::CPM) {
    return kuaishou::ad::AdActionType::AD_PHOTO_IMPRESSION;
  }
  if (ks::ad_base::GetMediumAttribute(*(session_data_->get_ad_request())) == 2 ||
      ks::ad_base::GetMediumAttribute(*(session_data_->get_ad_request())) == 4) {
    return kuaishou::ad::AdActionType::AD_ITEM_CLICK;
  }
  if (session_data_->get_pos_manager().IsSearchRequest() &&
      base_info->bid_type() == kuaishou::ad::AdEnum::CPC) {
    return kuaishou::ad::AdActionType::AD_ITEM_CLICK;
  }
  // 所以，CPC2 直接在这里写，应该就行了
  return GetChargeActionType(base_info->bid_type(),
                             base_info->ocpc_action_type(),
                             base_info->campaign_type(),
                             base_info->item_type());
}

kuaishou::ad::AdActionType CommonAdBaseInfoPack::GetChargeActionType(
                                            kuaishou::ad::AdEnum::BidType bid_type,
                                            kuaishou::ad::AdActionType action_type,
                                            kuaishou::ad::AdEnum::CampaignType campaign_type,
                                            kuaishou::ad::AdEnum::ItemType item_type) {
  switch (bid_type) {
    case kuaishou::ad::AdEnum::CPM:
      return kuaishou::ad::AdActionType::AD_PHOTO_IMPRESSION;
    case kuaishou::ad::AdEnum::OCPM:
      return kuaishou::ad::AdActionType::AD_PHOTO_IMPRESSION;
    case kuaishou::ad::AdEnum::CPC:
      return kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION;
    case kuaishou::ad::AdEnum::OCPC:
      return kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION;
    case kuaishou::ad::AdEnum::CPT:
      return kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
    case kuaishou::ad::AdEnum::CPC2:    // CPC2 计费点
      return kuaishou::ad::AdActionType::AD_ITEM_CLICK;
    case kuaishou::ad::AdEnum::CPA:
      if (action_type == kuaishou::ad::AdActionType::AD_PURCHASE) {
        return kuaishou::ad::AdActionType::EVENT_PAY;
      }
      if (action_type == kuaishou::ad::AdActionType::AD_BUTTON_CLICK_CONSULT) {
        // NOTE(yangshaowei03) 快招完成 ocpx_action_type 到计费点映射
        if (campaign_type == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO ||
            (campaign_type == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE &&
             item_type == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE)) {
          return kuaishou::ad::AdActionType::AD_ITEM_CLICK;
        } else if (campaign_type == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return kuaishou::ad::AdActionType::AD_BUTTON_CLICK;
        }
      }
      return action_type;
    case kuaishou::ad::AdEnum::OCPM_DSP:
      return kuaishou::ad::AdActionType::AD_DELIVERY;
    default:
      return kuaishou::ad::AdActionType::UNKNOWN_ACTION_TYPE;
  }
}

kuaishou::ad::AdActionType CommonAdBaseInfoPack::GetAdxChargeActionType(
                                          const kuaishou::ad::AdBaseInfo *base_info) {
  kuaishou::ad::AdActionType charge_action_type = GetChargeActionType(base_info->bid_type(),
                                                            base_info->ocpc_action_type(),
                                                            base_info->campaign_type(),
                                                            base_info->item_type());
  switch (base_info->bid_type()) {
    case kuaishou::ad::AdEnum::CPM:
    case kuaishou::ad::AdEnum::OCPM:
      charge_action_type = kuaishou::ad::AdActionType::AD_ITEM_IMPRESSION;
      break;
    case kuaishou::ad::AdEnum::CPC:
    case kuaishou::ad::AdEnum::OCPC:
      charge_action_type = kuaishou::ad::AdActionType::AD_ITEM_CLICK;
      break;
    default:
      break;
  }
  return charge_action_type;
}

kuaishou::ad::AdConversionType CommonAdBaseInfoPack::ConvertConversionType(
                                    kuaishou::ad::AdEnum::ConversionType source_type) {
  kuaishou::ad::AdConversionType dest_type = kuaishou::ad::UNKNOWN_CONVERSION_TYPE;
  switch (source_type) {
    case kuaishou::ad::AdEnum::CONVERION_APP_DOWNLOAD:
      dest_type = kuaishou::ad::APP_DOWNLOAD;
      break;
    case kuaishou::ad::AdEnum::CONVERION_OPEN_HTML5:
      dest_type = kuaishou::ad::OPEN_HTML5;
      break;
    case kuaishou::ad::AdEnum::CONVERION_OPEN_HTML5_JS_TAOBAO:
      dest_type = kuaishou::ad::OPEN_HTML5_JS_TAOBAO;
      break;
    case kuaishou::ad::AdEnum::CONVERION_APP_ADVANCE:
      dest_type = kuaishou::ad::APP_ADVANCE;
      break;
    case kuaishou::ad::AdEnum::CONVERSION_TOPIC:
      dest_type = kuaishou::ad::CONVERSION_TOPIC;
      break;
    case kuaishou::ad::AdEnum::CONVERSION_OPEN_LIVE:
      dest_type = kuaishou::ad::CONVERSION_OPEN_LIVE;
      break;
    case kuaishou::ad::AdEnum::CONVERSION_OPEN_PROFILE:
      dest_type = kuaishou::ad::CONVERSION_OPEN_PROFILE;
      break;
    default:
      dest_type = kuaishou::ad::UNKNOWN_CONVERSION_TYPE;
      break;
  }
  return dest_type;
}

bool CommonAdBaseInfoPack::IsProgrammaticCreative(const kuaishou::ad::tables::Creative &creative) {
  return creative.create_source_type() == kuaishou::ad::AdEnum::CUSTOMIZED_PROGRAMMATIC_CREATIVE ||
         creative.create_source_type() == kuaishou::ad::AdEnum::PACKAGE_PROGRAMMATIC_CREATIVE;
}

}  // namespace front_server
}  // namespace ks
