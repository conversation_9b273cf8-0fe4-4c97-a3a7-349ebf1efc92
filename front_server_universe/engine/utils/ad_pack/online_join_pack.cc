#include "teams/ad/front_server_universe/engine/utils/ad_pack/online_join_pack.h"

#include <vector>
#include <utility>
#include <set>
#include <string>

#include "teams/ad/ad_base/src/log_record/util.h"
#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_simplify_trace_log.pb.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/front_server_universe/util/adx/adx_common_util.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
#include "third_party/abseil/absl/strings/substitute.h"
#include "teams/ad/front_server_universe/bg_task/smart_compute_power/smart_compute_power_inner.h"
#include "teams/ad/engine_base/p2p_cache_loader/industry_playlet_sdpa.h"

namespace ks {
namespace front_server {

using namespace google::protobuf;  // NOLINT
void OnlineJoinPack::Process(
    ContextData* context, AdResult* ad_result, OnlineJoinParams* original_online_join_pb,
    const RankAdCommon* ad_common) {
  if (!Initialize(context, ad_result, original_online_join_pb, ad_common)) {
    return;
  }

  if (!GetRankResult()) {
    // 获取 rank_result 失败，直接返回
    return;
  }

  // 公共逻辑，各请求的独立判断逻辑不要加在这个函数里面
  SetCommonData();

  // 不同请求独立的逻辑
  SetIndependentData();

  // 添加一个 online join 的裁剪
  CompressOnlineJoin();
}

bool OnlineJoinPack::Initialize(ContextData* context, AdResult* ad_result,
                                OnlineJoinParams* original_online_join_pb,
                                const RankAdCommon* ad_common) {
  if (context == nullptr || ad_result == nullptr || original_online_join_pb == nullptr ||
      context->get_ad_request() == nullptr) {
    LOG(WARNING) << "Fail to do initialization, because input is invalid.";
    return false;
  }

  session_data_ = context;
  result_ = ad_result;
  online_join_pb_ = original_online_join_pb;
  ad_request_ = session_data_->mutable_ad_request();
  const auto& style_info_map = session_data_->get_style_info_resp()->style_info();
  int64_t creative_id = result_->ad_deliver_info().ad_base_info().creative_id();
  const auto& iter = style_info_map.find(creative_id);
  if (iter == style_info_map.end()) {
    return false;
  }
  style_info_ = &(iter->second);
  target_bid_ = result_->mutable_target_bid();
  ad_deliver_info_ = result_->mutable_ad_deliver_info();
  ad_base_info_ = result_->mutable_ad_deliver_info()->mutable_ad_base_info();
  online_join_params_transparent_ =
      result_->mutable_ad_deliver_info()->mutable_online_join_params_transparent();
  ad_common_ = ad_common;
  brand_online_join_pb_.Clear();

  return true;
}

void OnlineJoinPack::SetCommonData() {
  SetBehaviorIntent();
  SetCorePopulation();
  SetTransparentInfo();
  SetStyleData();
  SetLiveInfo();
  SetDataFromOnlineJoinParamsTransparent();
  SetRankResultTransparent();
  SetAdDeliverInfoData();
  SetAdBaseInfoData();
  SetInfoFromContextData();
  SetTargetBidData();
  SetAdDataV2Data();
  SetAdxData();
  SetMerchantInfo();
  SetPecCouponInfo();
  SetEnvInfo();
  // 设置软硬广队列
  SetAdQueueType();
  SetOtherData();
  SetExploreInfo();
  SetRequestData();
  // 落地页组件信息
  SetLpComponent();
  SetSmartComputeScore();
  SetDynamicShareExpId();
}

void OnlineJoinPack::CompressOnlineJoin() {
  ClearPbDefaultField(online_join_pb_, true);
  auto online_join_params_white_list = FrontKconfUtil::adLogFullOnlineJoinParamsWhiteList();
  char ret[1000] = {0};
  auto underscore_to_hump = [&](const std::string &src) -> std::string {
    // char *ret = new char[src.length() + 1];
    memset(ret, 0, 1000);
    const char *cstr = src.c_str();
    for (int i = 0, j = 0; i < src.length() && i < 1000; i++) {
      // 简单点，不考虑头尾是 _ 和连续 _ 的情况，这种情况自行修改格式
      if (src.at(i) == '_' && i + 1 < src.length()) {
        ret[j++] = absl::ascii_toupper(cstr[++i]);
      } else {
        ret[j++] = cstr[i];
      }
    }
    std::string ret_str(ret);
    // if (ret != nullptr) delete []ret;
    return std::move(ret_str);
  };
  const Reflection *ref = online_join_pb_->GetReflection();
  if (ref == nullptr) return;
  typedef std::vector<const FieldDescriptor *> FieldList;
  FieldList field_list;
  ref->ListFields(*online_join_pb_, &field_list);
  bool clear_field = false;
  for (FieldList::const_iterator it = field_list.begin(); it != field_list.end(); ++it) {
    clear_field = false;
    const FieldDescriptor *field = *it;
    // 2 不在白名单的进行清理
    if (online_join_params_white_list->count(underscore_to_hump(field->name())) <= 0) {
      // debug_str2 = absl::StrCat(debug_str2,",", underscore_to_hump(field->name()));
      ref->ClearField(online_join_pb_, field);
    }
  }
}

void OnlineJoinPack::SetLpComponentForSku() {
  std::string page_component_str = style_info_->magic_site_sku_page().page_component();
  if (page_component_str.empty() || page_component_str == "[]") {
    return;
  }
  base::Json page_component_js(base::StringToJson(page_component_str));
  if (page_component_js.IsArray()) {
    bool has_form = false, has_weixin = false, has_service = false;
    int64_t form_sub_type = -1, weixin_sub_type = -1, service_sub_type = -1;
    for (auto iter = page_component_js.array_begin();
              iter != page_component_js.array_end(); ++iter) {
      int64_t type = (*iter)->GetInt("type", -1);
      int64_t sub_type = (*iter)->GetInt("subType", -1);
      if (type == 14) {
        has_form = true;
        form_sub_type = sub_type;
      }
      if (type == 15) {
        has_weixin = true;
        weixin_sub_type = sub_type;
      }
      if (type == 11) {
        has_service = true;
        service_sub_type = sub_type;
      }
    }
    if (has_form && has_weixin && has_service) {
      online_join_pb_->set_view_component_type(-5);
      online_join_pb_->set_sub_type(-5);
    } else if (has_form && has_weixin) {
      online_join_pb_->set_view_component_type(-4);
      online_join_pb_->set_sub_type(-4);
    } else if (has_weixin && has_service) {
      online_join_pb_->set_view_component_type(-3);
      online_join_pb_->set_sub_type(-3);
    } else if (has_form && has_service) {
      online_join_pb_->set_view_component_type(-2);
      online_join_pb_->set_sub_type(-2);
    } else if (has_form) {
      online_join_pb_->set_view_component_type(14);
      online_join_pb_->set_sub_type(form_sub_type);
    } else if (has_weixin) {
      online_join_pb_->set_view_component_type(15);
      online_join_pb_->set_sub_type(form_sub_type);
    } else if (has_service) {
      online_join_pb_->set_view_component_type(11);
      online_join_pb_->set_sub_type(form_sub_type);
    } else {
      online_join_pb_->set_view_component_type(-1);
      online_join_pb_->set_sub_type(-1);
    }
  }
}

void OnlineJoinPack::SetLpComponent() {
  std::string page_component_str = style_info_->magic_site_page().page_component();
  if (page_component_str.empty() || page_component_str == "[]") {
    return;
  }
  base::Json page_component_js(base::StringToJson(page_component_str));
  if (page_component_js.IsArray()) {
    bool has_form = false, has_weixin = false, has_service = false;
    int64_t form_sub_type = -1, weixin_sub_type = -1, service_sub_type = -1;
    for (auto iter = page_component_js.array_begin();
              iter != page_component_js.array_end(); ++iter) {
      int64_t type = (*iter)->GetInt("type", -1);
      int64_t sub_type = (*iter)->GetInt("subType", -1);
      if (type == 14) {
        has_form = true;
        form_sub_type = sub_type;
      }
      if (type == 15) {
        has_weixin = true;
        weixin_sub_type = sub_type;
      }
      if (type == 11) {
        has_service = true;
        service_sub_type = sub_type;
      }
    }
    if (has_form && has_weixin && has_service) {
      online_join_pb_->set_view_component_type(-5);
      online_join_pb_->set_sub_type(-5);
    } else if (has_form && has_weixin) {
      online_join_pb_->set_view_component_type(-4);
      online_join_pb_->set_sub_type(-4);
    } else if (has_weixin && has_service) {
      online_join_pb_->set_view_component_type(-3);
      online_join_pb_->set_sub_type(-3);
    } else if (has_form && has_service) {
      online_join_pb_->set_view_component_type(-2);
      online_join_pb_->set_sub_type(-2);
    } else if (has_form) {
      online_join_pb_->set_view_component_type(14);
      online_join_pb_->set_sub_type(form_sub_type);
    } else if (has_weixin) {
      online_join_pb_->set_view_component_type(15);
      online_join_pb_->set_sub_type(form_sub_type);
    } else if (has_service) {
      online_join_pb_->set_view_component_type(11);
      online_join_pb_->set_sub_type(form_sub_type);
    } else {
      online_join_pb_->set_view_component_type(-1);
      online_join_pb_->set_sub_type(-1);
    }
  }
}

void OnlineJoinPack::SetRequestData() {
  bool is_close = ks::ad_base::util::IsClosePersonaliseRecommend(*(session_data_->get_ad_request()));
  online_join_pb_->set_is_close_personalise_recommend(is_close);
  online_join_pb_->set_user_group_tag(session_data_->get_ad_request()->ad_user_info().user_group_tag());
  online_join_pb_->set_user_risk_level(
      session_data_->get_ad_request()->reco_user_info().feature_collection().risk_level());
  online_join_pb_->set_corona_video_tab_type(session_data_->get_ad_request()->corona_video_info().tab_type());
  online_join_pb_->set_ad_personal_switch_status(
      static_cast<int32_t>(session_data_->get_ad_request()->ad_personal_switch_status()));
  online_join_pb_->set_request_freq(session_data_->get_ad_request()->ad_user_info().request_freq());
}

void OnlineJoinPack::SetExploreInfo() {
  online_join_pb_->set_explore_budget(style_info_->unit_support_info().explore_budget());
  online_join_pb_->set_explore_budget_status(style_info_->unit_support_info().explore_budget_status());
  online_join_pb_->set_explore_bid_type(style_info_->unit_support_info().explore_bid_type());
  online_join_pb_->set_extends_account_id(style_info_->unit_support_info().extends_account_id());
  online_join_pb_->set_extends_unit_id(style_info_->unit_support_info().extends_unit_id());
}

void OnlineJoinPack::SetOtherData() {
  online_join_pb_->set_ad_trace_dsp_type(kuaishou::ad::AdTraceDspType_Name(GetAdDspType(*result_)));
  online_join_pb_->set_reco_ip(ad_request_->reco_user_info().location().ip());
  if (ad_request_->ad_user_info().used_gps_cache_timestamp() != 0) {
    online_join_pb_->set_reco_gps_ts(ad_request_->ad_user_info().used_gps_cache_timestamp());
  } else {
    online_join_pb_->set_reco_gps_ts(ad_request_->reco_user_info().location().lat_lon_time());
  }
  online_join_pb_->set_region_source_type(ad_request_->ad_user_info().adcode().region_source_type());

  // 智能托管 project bid/roi_ratio
  online_join_pb_->set_hosting_project_bid(style_info_->hosting_project().cpa_bid());
  online_join_pb_->set_hosting_roi_ratio(style_info_->hosting_project().roi_ratio());
  online_join_pb_->set_hosting_scene(style_info_->hosting_project().hosting_scene());
  online_join_pb_->set_hosting_project_id(style_info_->hosting_project().id());
  if (ks::ad_base::IsLongVideoPatchRequest(session_data_->get_sub_page_id())) {
    if (session_data_->get_front_server_request()->universe_request().imp_info().ad_pos_info_size() > 0) {
      Json imp_ext_data(StringToJson(session_data_->get_front_server_request()->universe_request()
                                          .imp_info()
                                          .ad_pos_info(0)
                                          .imp_ext_data()));
      online_join_pb_->set_relative_photo_id(imp_ext_data.GetInt("photo_id", 0));
      // 兼容下 IOS
      if (online_join_pb_->relative_photo_id() == 0) {
        int64_t relative_photo_id = 0;
        base::StringToInt64(imp_ext_data.GetString("photoId", "0"), &relative_photo_id);
        online_join_pb_->set_relative_photo_id(relative_photo_id);
      }
    }
  }

  if (style_info_->magic_site_page().direct_call_type() != 0) {
    online_join_pb_->set_direct_call_type(style_info_->magic_site_page().direct_call_type());
  }

  online_join_pb_->set_degrade_level(absl::StrCat(session_data_->get_degrade_level()));
  online_join_pb_->set_auax_mapping_ocpx_action_type(
        style_info_->campaign().auax_mapping_ocpx_action_type());

  online_join_pb_->set_rel_campaign_id(style_info_->campaign().rel_campaign_id());
  online_join_pb_->set_explore_material_type(style_info_->campaign().explore_material_type());
  online_join_pb_->set_rel_type(style_info_->campaign().rel_type());
}

void OnlineJoinPack::SetAdQueueType() {
  online_join_pb_->set_ad_queue_type(ks::ad_base::GetAdQueueType(result_->ad_deliver_info()));
}
void OnlineJoinPack::SetIndependentData() {
  SetUniverseData();
}

bool OnlineJoinPack::GetRankResult() {
  // 非白名单和品牌广告获取 rankResult 失败时返回 false
  // 是否有 rankServer 透传结果
  bool has_rank_result = !(result_->ad_source_type() == kuaishou::ad::BRAND);
  if (IsAdxWhiteUser(session_data_->get_ad_request()->ad_user_info().id())) {
    // 白名单用户不请求精排，故没有 rankServer 透传结果
    has_rank_result = false;
  }

  if (has_rank_result) {
    auto *ad_rank_result = GetAdRankResult(session_data_, *result_);
    if (!ad_rank_result) {
      falcon::Inc("front_server.zt_no_ad_rank_result", 1);
      return false;
    }
    rank_result_ = ad_rank_result;
  } else {
    // has_rank_result 为 false 下的兜底逻辑.
    rank_result_ = &rank_result_backup_;
    rank_info_ = &rank_info_backup_;
  }
  return true;
}

void OnlineJoinPack::SetStyleData() {
  if (style_info_->has_creative()) {
    online_join_pb_->set_site_id(style_info_->creative().site_id());
    auto age_in_hour = (base::GetTimestamp() / 1000 - style_info_->creative().create_time()) / 1000 / 3600;
    online_join_pb_->set_age_in_hour(age_in_hour);
    online_join_pb_->set_creative_type(style_info_->creative().creative_type());
    session_data_->dot_perf->Interval(age_in_hour, "front_server.age_in_hour");
  }
  auto get_convert_type = [&] () {
    if (style_info_->unit().convert_id() > 0) {
      return static_cast<kuaishou::ad::AdCallbackLog_ConvertType>(style_info_->trace_util().type());
    } else if (style_info_->unit().convert_id() == 0 &&
               static_cast<int32>(style_info_->unit().taobao_url_type()) == 4) {  // 金牛
      return kuaishou::ad::AdCallbackLog_ConvertType::AdCallbackLog_ConvertType_ECOM;
    } else if (style_info_->unit().convert_id() == 0 &&
               static_cast<int32>(style_info_->unit().web_uri_type()) == 2) {  // 自建站
      return kuaishou::ad::AdCallbackLog_ConvertType::AdCallbackLog_ConvertType_SITE_DIY;
    }
    return kuaishou::ad::AdCallbackLog_ConvertType::AdCallbackLog_ConvertType_UNKNWON;
  };
  // 创意优选打点
  if (style_info_->has_creative()) {
    const auto style_iter =
        session_data_->get_style_data_for_online_join().find(style_info_->creative().id());
    if (style_iter != session_data_->get_style_data_for_online_join().end()) {
      for (const auto& sd : style_iter->second) {
        online_join_pb_->add_style_data()->CopyFrom(sd);
      }
    }
  }
  online_join_pb_->set_convert_type(get_convert_type());
  online_join_pb_->set_campaign_sub_type(style_info_->campaign().sub_type());
  online_join_pb_->set_auto_target(style_info_->unit().auto_target());
  online_join_pb_->set_fiction_id(style_info_->unit_support_info().fiction_id());
  online_join_pb_->set_behavior_intention_target(style_info_->unit().behavior_intention_target());
  online_join_pb_->set_ad_app_id(style_info_->app_release().app_id());  // 广告主推广的 app_id
  online_join_pb_->set_audience_prediction_num(style_info_->unit().audience_prediction_num());
  online_join_pb_->set_internal_invest_plan_id(style_info_->campaign().internal_invest_plan_id());
  online_join_pb_->set_real_app_name(style_info_->app_release().real_app_name());
  // 一键复苏
  online_join_pb_->set_explore_version(style_info_->unit_support_info().explore_version());
  // 若不处于辅助探索, reference_unit_ids 为空或者 [] ，如果处于辅助探索，
  // 那么形如 [0] 的 josn string 的长度会大于 2,
  bool reference = style_info_->unit_support_info().reference_unit_ids().size() > 2 &&
                    style_info_->unit().study_status() == 1;
  online_join_pb_->set_in_reference(reference);
  // 设置离线创意分级标签
  int32_t exp_id = SPDM_program_creative_exp_type(session_data_->get_spdm_ctx());
  for (const auto& creative_label : style_info_->creative().extend_fields().creative_extend_score().creative_label()) {  // NOLINT
    if (creative_label.exp_id() == exp_id) {
      online_join_pb_->set_creative_label_from_creative_server(creative_label.label());
      falcon::Inc(
          absl::StrCat("front_server.creative_label_from_creative_server_cnt_",
                       creative_label.label()).data(), 1);
      LOG_EVERY_N(INFO, 1000) << "epx_id: " << exp_id << ", label: " << creative_label.label();
      break;
    }
  }
  online_join_pb_->set_enable_ska(style_info_->unit_support_info().use_ska());
  online_join_pb_->set_playable_id(style_info_->unit_support_info().playable_id());
  online_join_pb_->set_unit_source_type(style_info_->unit().unit_source_type());
  online_join_pb_->set_wechat_page_tag(style_info_->magic_site_page().page_tag());
  online_join_pb_->set_live_creative_type(style_info_->creative().live_creative_type());
  online_join_pb_->set_photo_info_md5(std::to_string(style_info_->photo_status().parse_field().md5_uint()));
  if (style_info_->has_account()) {
    online_join_pb_->set_city_product_id(style_info_->account().city_product_id());
  }

  auto dpa_type = ad_base_info_->dpa_type();

  if (dpa_type == 2 || dpa_type == 4) {
    // SDPA 逻辑, 从正排 style_info 获取字段
    online_join_pb_->set_dpa_outer_id(style_info_->unit_support_info().dpa_outer_id());
    online_join_pb_->set_dpa_industry_id(style_info_->unit_support_info().dpa_industry_id());
    online_join_pb_->set_dpa_id(style_info_->unit_support_info().dpa_id());
    online_join_pb_->set_library_id(style_info_->unit().library_id());
  }
  online_join_pb_->set_agent_type(style_info_->agent().crm_agent_type());

  if ((ad_deliver_info_->is_expansion() == kuaishou::ad::IsExpansion::NOT_EXPANSION
       || ad_deliver_info_->is_expansion() == kuaishou::ad::IsExpansion::EXPANSION
       || ad_deliver_info_->is_expansion() == kuaishou::ad::IsExpansion::ESP_EXPANSION) &&
      (style_info_->target().celebrity_label().size() > 0
       || style_info_->target().purchase_intention_label().size() > 0
       || style_info_->target().extend_fields().population_size() > 0
       || style_info_->target().extend_fields().audience_size() > 0
       || style_info_->target().extend_fields().paid_audience_size() > 0)) {
    online_join_pb_->set_is_hit_soft_target(true);
  } else {
    online_join_pb_->set_is_hit_soft_target(false);
  }

  online_join_pb_->set_creative_das_data(style_info_->creative().creative_support_info().creative_das_data());
  online_join_pb_->set_unit_das_data(style_info_->unit_support_info().unit_das_data());
  CleanJsonData(online_join_pb_->mutable_creative_das_data(),
                   FrontKconfUtil::creativeDasDataWhiteSet().get());
  CleanJsonData(online_join_pb_->mutable_unit_das_data(),
                   FrontKconfUtil::unitDasDataWhiteSet().get());
  online_join_pb_->set_target_type(style_info_->unit_support_info().target_type());
  online_join_pb_->set_target_setting_type(style_info_->unit_support_info().target_setting_type());
  online_join_pb_->set_target_extend(style_info_->unit_support_info().target_extend());
  online_join_pb_->set_scene_oriented_type(style_info_->campaign().scene_oriented_type());
  online_join_pb_->set_auto_target_modified(style_info_->target().auto_target_modified());
  online_join_pb_->set_ad_risk_level(style_info_->photo_status().risk_level());
  online_join_pb_->set_ad_live_risk_level(style_info_->live_stream_user_info().risk_level());
  if (style_info_->has_live_stream_user_info()) {
    online_join_pb_->set_is_recruiting_live(style_info_->live_stream_user_info().is_recruiting_live());
  }
  if (style_info_->campaign().type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
    online_join_pb_->set_series_id(style_info_->unit_support_info().series_id());
  }
  if (style_info_->has_creative() &&
      style_info_->creative().has_extend_fields()) {
    session_data_->dot_perf->Interval(style_info_->creative().extend_fields().risk_labels_size(),
                                      "front_risk_label_size");
    online_join_pb_->mutable_risk_labels()
                    ->CopyFrom(style_info_->creative().extend_fields().risk_labels());
  }
  online_join_pb_->set_is_trace_api_detection(session_data_->get_is_trace_api_detection());
  if (style_info_->has_magic_site_page() &&
      style_info_->magic_site_page().has_adlp_data_report_info()) {
    online_join_pb_->set_adlp_data_report_info(style_info_->magic_site_page().adlp_data_report_info());
  }
  if (style_info_->has_magic_site_page() &&
      style_info_->magic_site_page().has_page_report_info()) {
    session_data_->dot_perf->Count(1, "universe_fill_page_report_info", "magic_site_page");
    online_join_pb_->set_page_report_info(style_info_->magic_site_page().page_report_info());
  }
  if (style_info_->has_magic_site_page()) {
    if (style_info_->magic_site_page().has_conversion_path()) {
      online_join_pb_->set_conversion_path(style_info_->magic_site_page().conversion_path());
      session_data_->dot_perf->Count(1, "conversion_path", "magic_site_page");
    }
    if (style_info_->magic_site_page().has_sub_conversion_path()) {
      online_join_pb_->set_sub_conversion_path(style_info_->magic_site_page().sub_conversion_path());
      session_data_->dot_perf->Count(1, "sub_conversion_path", "magic_site_page");
    }
  }
  online_join_pb_->set_download_page_type(style_info_->unit_support_info().download_page_type());
  online_join_pb_->set_download_page_url(style_info_->unit_support_info().download_page_url());
  online_join_pb_->set_account_auto_manage(style_info_->account_support_info().account_auto_manage());
  online_join_pb_->set_periodic_delivery_type(style_info_->campaign().periodic_delivery_type());
}

void OnlineJoinPack::SetLiveInfo() {
  if (style_info_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
        IsNonMerchantLivePromote(*style_info_)) {
    if (style_info_->creative().live_creative_type() ==
              kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE ||
          IsNonMerchantLiveStream(*style_info_)) {
      online_join_pb_->set_is_live(true);
    } else {
      online_join_pb_->set_is_live_to_photo(true);  // 是否为短视频带直播
    }
    online_join_pb_->set_live_p3s(
            rank_result_->predict_origin_info().live_play_3s_rate());  // 直播展现到 p3s
    online_join_pb_->set_live_audience_slide(
            rank_result_->predict_origin_info().ad_live_audience());  // 直播展现到 liveplaystarted
    online_join_pb_->set_photo2live_pay_rate(
            rank_result_->predict_origin_info().photo2live_pay_rate());  // 直播展现到 liveplaystarted
    online_join_pb_->set_live_goods_view(
            rank_result_->predict_origin_info().live_goods_view_rate());  // 直播商品访问率
    online_join_pb_->set_live_order_paied(
            rank_result_->predict_origin_info().live_order_paid_rate());  // 直播商品购买率
    online_join_pb_->set_predict_unify_deep_cvr(
            rank_result_->predict_origin_info().live_order_paid_rate());  // 直播
    online_join_pb_->set_live_server_show_p3s_slide(
            rank_result_->predict_origin_info().live_server_show_play_3s_slide());  // 直播滑滑 p3s 率
    online_join_pb_->set_live_server_show_p3s_feed(
            rank_result_->predict_origin_info().live_server_show_play_3s_feed());
    online_join_pb_->set_auto_roas(rank_result_->ad_rank_trans_info().auto_roas());
  }

  // 粉条直播透传 live_order_source
  static std::set<AdEnum::CampaignType> fanstop_live_types{
      AdEnum::AD_FANSTOP_LIVE_TO_ALL, AdEnum::AD_FANSTOP_LIVE_TO_FANS, AdEnum::AD_FANSTOP_LIVE_TO_SHOW};
  if (fanstop_live_types.count(style_info_->campaign().type())) {
    online_join_pb_->set_live_order_source(
        style_info_->campaign_fanstop_support_info().live_order_source());
    VLOG_IF(2, online_join_pb_->live_order_source() != 0)
        << ", campaign_type = " << style_info_->campaign().type()
        << ", ad_source_type = " << result_->ad_source_type()
        << ", live_order_source = " << online_join_pb_->live_order_source()
        << ", account_type = " << result_->ad_deliver_info().ad_base_info().account_type();
  }
  if (style_info_->magic_site_sku_page().id() > 0) {
    if (style_info_->magic_site_sku_page().parse_fields().sku_ids_size() > 0) {
      online_join_pb_->set_bs_live_spu_id(style_info_->magic_site_sku_page().parse_fields().sku_ids(0));
    }
    if (style_info_->magic_site_sku_page().direct_call_type() != 0) {
      online_join_pb_->set_direct_call_type(style_info_->magic_site_sku_page().direct_call_type());
    }
    if (style_info_->magic_site_sku_page().has_adlp_data_report_info()) {
      online_join_pb_->set_adlp_data_report_info(style_info_->magic_site_sku_page().adlp_data_report_info());
    }
    if (style_info_->magic_site_sku_page().has_page_report_info()) {
      session_data_->dot_perf->Count(1, "universe_fill_page_report_info", "magic_site_sku_page");
      online_join_pb_->set_page_report_info(style_info_->magic_site_sku_page().page_report_info());
    }
    online_join_pb_->set_wechat_page_tag(style_info_->magic_site_sku_page().page_tag());
    SetLpComponentForSku();
  }
}

void OnlineJoinPack::SetTransparentInfo() {
  online_join_pb_->mutable_ad_target_trans_info()->CopyFrom(
                    online_join_params_transparent_->ad_target_trans_info());
  online_join_pb_->mutable_ad_server_trans_info()->CopyFrom(
                    online_join_params_transparent_->ad_server_trans_info());
  online_join_pb_->mutable_ad_server_trans_info()->set_stay_time_scores("");
  online_join_pb_->mutable_ad_rank_trans_info()->CopyFrom(
                    rank_result_->ad_rank_trans_info());
  if (SPDM_enableRemoveOnlineJoinPriceRecord()) {
    online_join_pb_->mutable_ad_rank_trans_info()->mutable_adjust_price_record()->clear();
  }
}

void OnlineJoinPack::SetDataFromOnlineJoinParamsTransparent() {
  // [xiaowentao] 竞胜率模型结果打点
  online_join_pb_->mutable_win_rate_model_exp_info()->CopyFrom(
    session_data_->get_ud_win_rate_model_exp_info());
  if (SPDM_enable_dynamic_share_log(session_data_->get_spdm_ctx())) {
    online_join_pb_->mutable_ad_rank_trans_info()->mutable_universe_dynamic_share_exp_info()->CopyFrom(
      session_data_->get_ud_dynamic_share_exp_info());
  }
  online_join_pb_->set_work_flow_type(online_join_params_transparent_->work_flow_type());
  online_join_pb_->set_ad_strategy_tag(online_join_params_transparent_->ad_strategy_tag());
  online_join_pb_->set_dynamic_retrieval_queue_size(
                      online_join_params_transparent_->dynamic_retrieval_queue_size());
  online_join_pb_->set_dynamic_prerank_queue_size(
                      online_join_params_transparent_->dynamic_prerank_queue_size());
  online_join_pb_->set_is_search_celebrity(online_join_params_transparent_->is_search_celebrity());
  online_join_pb_->set_is_search_mid_page(online_join_params_transparent_->is_search_mid_page());
  online_join_pb_->set_calculate_gap_target(online_join_params_transparent_->calculate_gap_target());
  online_join_pb_->set_hostname(online_join_params_transparent_->ad_server_hostname());
  online_join_pb_->set_pred_score(online_join_params_transparent_->pred_score());
  online_join_pb_->set_ecpm_ctr_score(online_join_params_transparent_->ecpm_ctr_score());
  online_join_pb_->set_ecpm_cvr_score(online_join_params_transparent_->ecpm_cvr_score());
  online_join_pb_->set_recall_sort_type(online_join_params_transparent_->recall_sort_type());
  online_join_pb_->set_twin_bid_strategy(online_join_params_transparent_->twin_bid_strategy());
  online_join_pb_->set_speed_type(online_join_params_transparent_->speed_type());
  online_join_pb_->set_auto_cpa_bid_modify_code(online_join_params_transparent_->auto_cpa_bid_modify_code());
  online_join_pb_->set_auto_roas_modify_tag(online_join_params_transparent_->auto_roas_modify_tag());
  online_join_pb_->set_rank_hostname(online_join_params_transparent_->rank_hostname());
  online_join_pb_->set_programmatic_retrieval_type(
                      online_join_params_transparent_->programmatic_retrieval_type());
  online_join_pb_->set_is_skip_bid_server(online_join_params_transparent_->is_skip_bid_server() ||
                                          online_join_pb_->is_skip_bid_server());
  online_join_pb_->set_union_strategy_tag(online_join_params_transparent_->union_strategy_tag());
  online_join_pb_->set_skip_bid_server_tag(online_join_params_transparent_->skip_bid_server_tag());
  // dsp 目前没有设置
  online_join_pb_->set_deep_twin_bound(online_join_params_transparent_->deep_twin_bound());
  online_join_pb_->set_retrieval_tag_new(online_join_params_transparent_->retrieval_tag_new());
  online_join_pb_->set_multi_retrieval_tag(online_join_params_transparent_->multi_retrieval_tag());
  online_join_pb_->mutable_multi_retrieval_bitset()->CopyFrom(
                      online_join_params_transparent_->multi_retrieval_bitset());
  online_join_pb_->set_prerank_type(online_join_params_transparent_->prerank_type());
  online_join_pb_->set_smart_matching_pred(online_join_params_transparent_->smart_matching_pred());
  online_join_pb_->set_creative_activity_type(
                      online_join_params_transparent_->creative_activity_type());
  online_join_pb_->set_target_server_shard_type(
                      online_join_params_transparent_->target_shard_type());
  online_join_pb_->set_is_lookalike(online_join_params_transparent_->is_lookalike());
  online_join_pb_->mutable_prerank_dynamic_info()->CopyFrom(
      online_join_params_transparent_->prerank_dynamic_info());
  online_join_pb_->set_auto_deep_cpa_bid(target_bid_->auto_deep_cpa_bid());
  online_join_pb_->set_dynamic_ad_pos_gap_date(online_join_params_transparent_->dynamic_ad_pos_gap_date());
  online_join_pb_->set_cur_req_scene_str(online_join_params_transparent_->cur_req_scene_str());
  const std::string &target_hostname =
    (online_join_params_transparent_->target_shard_type() !=
        kuaishou::ad::AdEnum_TargetServerShardType_COLD_SHARD)
              ? online_join_params_transparent_->target_hostname()
              : online_join_params_transparent_->cold_target_hostname();
  online_join_pb_->set_target_hostname(target_hostname);
  online_join_pb_->set_merchant_dsp_hostname(online_join_params_transparent_->merchant_hostname());
  if (online_join_params_transparent_->target_order() != 0) {
    online_join_pb_->set_target_order(
          online_join_params_transparent_->target_order());
    session_data_->dot_perf->Interval(online_join_params_transparent_->target_order(),
                                      "front_server.target_order");
    falcon::Stat("front_server.target_order", online_join_params_transparent_->target_order());
  }
  // set look alike
  std::set<int64_t> lookalike;
  bool hit_lookalike_user = false;
  const auto &user_info = session_data_->get_ad_request()->ad_user_info();
  for (const auto &upload_population_orientation : style_info_->populations()) {
    if (upload_population_orientation.population_type() == 5) {
      lookalike.insert(upload_population_orientation.orientation_id());
    }
  }
  if (lookalike.size() > 0) {
    for (const int64_t pop_id : user_info.orientation()) {
      if (lookalike.find(pop_id) != lookalike.end()) {
        hit_lookalike_user = true;
        break;
      }
    }
  }
  if (hit_lookalike_user != online_join_params_transparent_->hit_lookalike_user()) {
    falcon::Inc("front_server.hit_lookalike_user_diff", 1);
  }
  online_join_pb_->set_hit_lookalike_user(hit_lookalike_user);
  // 外循环数据下发
  online_join_pb_->set_ecom_quality(online_join_params_transparent_->ecom_quality());
  online_join_pb_->set_first_click_score(online_join_params_transparent_->first_click_score());
  online_join_pb_->set_calibrated_cvr(online_join_params_transparent_->calibrated_cvr());
  online_join_pb_->set_ptds_search_label(online_join_params_transparent_->ptds_search_label());
  online_join_pb_->set_account_mark(online_join_params_transparent_->account_mark());
  online_join_pb_->set_rta_trace_id(online_join_params_transparent_->rta_trace_req_id());
  online_join_pb_->set_rta_strategy_id(online_join_params_transparent_->rta_strategy_id());
  online_join_pb_->set_multi_touch_score(rank_result_->rank_made_info().multi_touch_score());
  online_join_pb_->set_rta_second_direct_bid(rank_result_->rank_made_info().rta_second_direct_bid());
  online_join_pb_->set_rta_feature_id(
      online_join_params_transparent_->rta_feature_id());
  online_join_pb_->set_trace_request_id(
      online_join_params_transparent_->trace_request_id());
  online_join_pb_->set_adx_cpm_precise_bid(
      online_join_params_transparent_->adx_cpm_precise_bid());
  online_join_pb_->set_rta_sta_tag(
      online_join_params_transparent_->rta_sta_tag());
  online_join_pb_->set_rta_ratio(
      online_join_params_transparent_->first_click_score() / 1000000.0);
  if (online_join_params_transparent_->ad_direct_merchant_biz() !=
      kuaishou::ad::AdEnum::MERCH_BIZ_NONE) {
    online_join_pb_->set_ad_direct_merchant_biz(
        online_join_params_transparent_->ad_direct_merchant_biz());
    online_join_pb_->set_ad_direct_merchant_stage(
        online_join_params_transparent_->ad_direct_merchant_stage());
    auto key_str = absl::StrCat(kuaishou::ad::AdEnum_AdDirectMerchantBiz_Name(
                    online_join_params_transparent_->ad_direct_merchant_biz()),
                   "#", kuaishou::ad::AdEnum_AdDirectMerchantStage_Name(
                    online_join_params_transparent_->ad_direct_merchant_stage()));
    session_data_->dot_perf->Interval(1, "front_server.ad_direct_merchant_info", key_str);
  }
}

void OnlineJoinPack::SetRankResultTransparent() {
  online_join_pb_->set_auction_bid_conv(rank_result_->rank_bid_info().auction_bid_conv());
  online_join_pb_->set_auction_bid_ecpc(rank_result_->rank_bid_info().auction_bid_ecpc());
  online_join_pb_->set_auction_bid_deep(rank_result_->rank_bid_info().auction_bid_deep());
  online_join_pb_->set_real_sctr(rank_result_->rank_bid_info().real_sctr());
  online_join_pb_->set_pre_ntr(rank_result_->predict_origin_info().ntr());
  online_join_pb_->set_cpm_thr(rank_result_->rank_reserve_info().cpm_thr());
  online_join_pb_->set_cpm_tag(static_cast<int64>(rank_result_->rank_reserve_info().cpm_tag()));
  online_join_pb_->set_server_show_cvr(rank_result_->predict_origin_info().server_show_cvr());
  online_join_pb_->set_clientshow_click2(rank_result_->predict_origin_info().clientshow_click2());
  online_join_pb_->set_click2_conv(rank_result_->predict_origin_info().click2_conv());
  online_join_pb_->set_click2_lps(rank_result_->predict_origin_info().click2_lps());
  online_join_pb_->set_click2_purchase(rank_result_->predict_origin_info().click2_purchase());
  online_join_pb_->set_click2_shop_action(rank_result_->predict_origin_info().click2_shop_action());
  online_join_pb_->set_click2_nextstay(rank_result_->predict_origin_info().click2_nextstay());
  online_join_pb_->set_click2_credit_rate(rank_result_->predict_origin_info().click2_credit_rate());
  online_join_pb_->set_credit_eval_rate(rank_result_->predict_origin_info().credit_eval_rate());
  online_join_pb_->set_click2_purchase_device(rank_result_->predict_origin_info().click2_purchase_device());
  online_join_pb_->set_click_purchase_device(rank_result_->predict_origin_info().click_purchase_device());
  online_join_pb_->set_click_insur_purchase_rate(
                    rank_result_->predict_origin_info().click_insur_purchase_rate());
  online_join_pb_->set_c2_insur_purchase_rate(rank_result_->predict_origin_info().c2_insur_purchase_rate());
  online_join_pb_->set_is_skip_bid_server(rank_result_->rank_base_info().is_skip_bid_server() ||
                                          online_join_pb_->is_skip_bid_server());
  online_join_pb_->set_sort_tag(rank_result_->rank_price_info().sort_tag());
  LOG_EVERY_N(INFO, 100000) << "online join pb sort tag: " << rank_result_->rank_price_info().sort_tag();
  online_join_pb_->set_new_creative_app_conversion_rate(
                    rank_result_->predict_origin_info().new_creative_app_conversion_rate());
  online_join_pb_->set_jinjian_credit_grant(rank_result_->predict_origin_info().credit_conv_grant_rate());
  online_join_pb_->set_prod_apr(rank_result_->predict_origin_info().prod_apr());
  online_join_pb_->set_server_client_show_rate(
                    rank_result_->predict_origin_info().server_client_show_rate());
  online_join_pb_->set_server_show_ctr(rank_result_->predict_origin_info().server_show_ctr());
  online_join_pb_->set_origin_price(rank_result_->rank_price_info().origin_price());
  online_join_pb_->set_next_price(rank_result_->rank_price_info().next_price());
  online_join_pb_->set_price_undiscount(rank_result_->rank_price_info().price_undiscount());
  online_join_pb_->set_conv_ltv(rank_result_->predict_origin_info().conv_ltv());
  online_join_pb_->set_purchase_ltv(rank_result_->predict_origin_info().purchase_ltv());
  online_join_pb_->set_auto_roas(rank_result_->ad_rank_trans_info().auto_roas());
  online_join_pb_->set_prophet_boost_exp_tag(rank_result_->ad_rank_trans_info().prophet_boost_exp_tag());
  online_join_pb_->set_prophet_boost_ratio(rank_result_->ad_rank_trans_info().prophet_boost_ratio());
  online_join_pb_->set_finance_unify_lps_cvr(rank_result_->ad_rank_trans_info().finance_unify_lps_cvr());
  online_join_pb_->set_finance_jinjian_credit_ratio(
          rank_result_->ad_rank_trans_info().finance_jinjian_credit_ratio());
  online_join_pb_->set_finance_use_credit_ratio(
          rank_result_->ad_rank_trans_info().finance_use_credit_ratio());
  online_join_pb_->set_edu_lps_cvr(rank_result_->ad_rank_trans_info().edu_lps_cvr());
  online_join_pb_->set_landingpage_submit_rate_c2(
          rank_result_->ad_rank_trans_info().landingpage_submit_rate_c2());
  online_join_pb_->set_clue_lps_cvr(rank_result_->ad_rank_trans_info().clue_lps_cvr());
  online_join_pb_->set_clue_clk_lps_cvr(rank_result_->ad_rank_trans_info().clue_clk_lps_cvr());
  online_join_pb_->set_sdpa_unify_cvr(rank_result_->ad_rank_trans_info().sdpa_unify_cvr());
  online_join_pb_->set_edu_lps_deep_ecpc_rate(rank_result_->ad_rank_trans_info().edu_lps_deep_ecpc_rate());
  online_join_pb_->set_fin_credit_roi_rate(rank_result_->ad_rank_trans_info().fin_credit_roi_rate());
  online_join_pb_->set_building_conv_rate(rank_result_->ad_rank_trans_info().building_conv_rate());
  online_join_pb_->set_lps_deep_generalization_score(rank_result_->ad_rank_trans_info().lps_deep_generalization_score());  // NOLINT
  online_join_pb_->set_lps_deep_generalization_score_cmd_id(rank_result_->ad_rank_trans_info().lps_deep_generalization_score_cmd_id());  // NOLINT
  online_join_pb_->set_ad_adx_thanos_cvr_exp(
                  rank_result_->ad_rank_trans_info().ad_adx_thanos_cvr_exp());
  online_join_pb_->set_ecom_app_conversion_rate(
    rank_result_->ad_rank_trans_info().ecom_app_conversion_rate());
  online_join_pb_->set_car_purchase_rate(rank_result_->ad_rank_trans_info().car_purchase_rate());
  online_join_pb_->set_common_ctr_cali_rate(
                  rank_result_->ad_rank_trans_info().common_ctr_cali_rate());
  online_join_pb_->set_common_cvr_cali_rate(
                  rank_result_->ad_rank_trans_info().common_cvr_cali_rate());
  online_join_pb_->set_common_dcvr_cali_rate(
                  rank_result_->ad_rank_trans_info().common_dcvr_cali_rate());
  online_join_pb_->set_offline_calibrate_ctr_score(
                  rank_result_->ad_rank_trans_info().offline_calibrate_ctr_score());
  online_join_pb_->set_offline_calibrate_cvr_score(
                  rank_result_->ad_rank_trans_info().offline_calibrate_cvr_score());
  online_join_pb_->set_offline_calibrate_dcvr_score(
                  rank_result_->ad_rank_trans_info().offline_calibrate_dcvr_score());
  online_join_pb_->set_inner_cpr(rank_result_->predict_origin_info().inner_cpr());
  online_join_pb_->set_merchant_ltv(rank_result_->predict_origin_info().merchant_ltv());
  online_join_pb_->set_live_p3s_ltv(rank_result_->predict_origin_info().live_p3s_ltv());
  online_join_pb_->set_order_paied(rank_result_->predict_origin_info().c1_order_paied());  // 短视频商品购买率
  online_join_pb_->set_is_retarget_ad(rank_result_->rank_base_info().is_retarget_ad());
  online_join_pb_->set_retarget_tool_tag(rank_result_->rank_base_info().retarget_tool_tag());
  online_join_pb_->set_c1_order_paid(rank_result_->predict_origin_info().c1_order_paied());
  online_join_pb_->set_inner_uplift_order_paid(rank_result_->predict_origin_info().inner_uplift_order_paid());
  online_join_pb_->set_spu_order_paid(rank_result_->predict_origin_info().spu_order_paid());
  online_join_pb_->set_author_order_paid(rank_result_->predict_origin_info().author_order_paid());
  online_join_pb_->set_before_heritage_cvr(rank_result_->predict_origin_info().before_heritage_cvr());
  online_join_pb_->set_product_min_price(rank_result_->rank_base_info().product_min_price());
  online_join_pb_->set_before_heritage_cmd_id(rank_result_->predict_origin_info().before_heritage_cmd_id());
  online_join_pb_->set_ltv_after_calibration(rank_result_->predict_origin_info().ltv_after_calibration());
  online_join_pb_->set_wtr(rank_result_->predict_origin_info().wtr());
  online_join_pb_->set_cvr(rank_result_->predict_origin_info().cvr());
  online_join_pb_->set_click_app_invoked(rank_result_->predict_origin_info().click_app_invoked());
  online_join_pb_->set_inner_loop_ecpc_imp_conv_rate(rank_result_->ad_rank_trans_info().inner_loop_ecpc_imp_conv_rate());  // NOLINT
  online_join_pb_->set_predict_future_cvr(rank_result_->ad_rank_trans_info().predict_future_cvr());
  online_join_pb_->set_future_cvr_adjust(rank_result_->ad_rank_trans_info().future_cvr_adjust());
  online_join_pb_->set_effective_play_7s(rank_result_->ad_rank_trans_info().effective_play_7s());  // NOLINT
  online_join_pb_->set_video_effective_play_7s(rank_result_->ad_rank_trans_info().video_effective_play_7s());  // NOLINT
  online_join_pb_->set_short_video_effective_play_7s(rank_result_->ad_rank_trans_info().short_video_effective_play_7s());  // NOLINT
  online_join_pb_->set_live_audience(rank_result_->predict_origin_info().ad_live_audience());
  online_join_pb_->set_shop_jump(rank_result_->predict_origin_info().shop_jump());
  online_join_pb_->set_live_play_1m(rank_result_->predict_origin_info().live_play_1m_rate());
  online_join_pb_->set_shop_cart_rate(rank_result_->predict_origin_info().shop_cart_rate());
  online_join_pb_->set_photo2live_rate(rank_result_->predict_origin_info().photo2live_rate());
  online_join_pb_->set_predict_auc_score_base(rank_result_->rank_rank_info().predict_auc_score_base());
  online_join_pb_->set_auc_cmd_id_base(rank_result_->rank_rank_info().auc_cmd_id_base());
  online_join_pb_->set_predict_auc_score_exp(rank_result_->rank_rank_info().predict_auc_score_exp());
  online_join_pb_->set_auc_cmd_id_exp(rank_result_->rank_rank_info().auc_cmd_id_exp());
  online_join_pb_->set_predict_auc_score_base_fusion(rank_result_->rank_rank_info().predict_auc_score_base_fusion()); // NOLINT
  online_join_pb_->set_auc_cmd_id_base_fusion(rank_result_->rank_rank_info().auc_cmd_id_base_fusion());  // NOLINT
  online_join_pb_->set_predict_auc_score_exp_fusion(rank_result_->rank_rank_info().predict_auc_score_exp_fusion());  // NOLINT
  online_join_pb_->set_auc_cmd_id_exp_fusion(rank_result_->rank_rank_info().auc_cmd_id_exp_fusion());  // NOLINT
  online_join_pb_->set_predict_fusion_base_param_w1(rank_result_->rank_rank_info().predict_fusion_base_param_w1());  // NOLINT
  online_join_pb_->set_predict_fusion_base_param_w2(rank_result_->rank_rank_info().predict_fusion_base_param_w2());  // NOLINT
  online_join_pb_->set_predict_fusion_exp_param_w1(rank_result_->rank_rank_info().predict_fusion_exp_param_w1());  // NOLINT
  online_join_pb_->set_predict_fusion_exp_param_w2(rank_result_->rank_rank_info().predict_fusion_exp_param_w2());  // NOLINT
  online_join_pb_->set_next_benefit(rank_result_->rank_price_info().next_benifit());
  online_join_pb_->set_auc_hit_tag(rank_result_->rank_rank_info().auc_hit_tag());
  online_join_pb_->set_pos_in_rank(rank_result_->rank_rank_info().pos_in_rank());
  online_join_pb_->set_fanstop_reco_wtr(rank_result_->predict_origin_info().reco_wtr());
  online_join_pb_->set_reco_vtr(rank_result_->predict_origin_info().reco_vtr());
  online_join_pb_->set_playtime(rank_result_->predict_origin_info().playtime());
  online_join_pb_->set_nday_refund_rate(rank_result_->predict_origin_info().nday_refund_rate());
  online_join_pb_->set_follow_fnl(rank_result_->predict_origin_info().follow_fnl());
  online_join_pb_->mutable_rank_cmd_model()->insert({ "ad_dsp_item_imp_ecom_cmd",
    "/ad/dsp/sim/landingpage_submit_rate:dsp_lps_djx_sim_add_com_fea_new" });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "c1_direct_ecom_rate",
    "/ad/dsp/sim/landingpage_submit_rate:dsp_lps_djx_sim_add_com_fea_new" });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "c1_nebula_order_paied_cmd",
    "/ad/dsp/sim/landingpage_submit_rate:dsp_lps_crp_utype_rta_v0" });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "c1_order_paid_rate",
    "/ad/dsp/sim/landingpage_submit_rate:dsp_lps_crp_utype_rta_v0" });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "server_show_merchant_follow_ratio",
    "/ad/dsp/photo_follow_rate:kai_photo_wtr_search_clip_v12" });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "c1_merchant_follow_rate",
    "/ad/dsp/photo_follow_rate:kai_photo_wtr_search_clip_v12" });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "ecom_roas_cvr",
    "/ad/dsp/sim/landingpage_submit_rate:dsp_lps_zd_cross_eds_ltvcopy" });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "ad_dsp_merchant_jinniu_ltv",
    "/ad/dsp/merchant_jinniu_order_ltv:dsp_lps_gmv_zyt_kai_v1"});
  online_join_pb_->mutable_rank_cmd_model()->insert({ "ad_dsp_merchant_reco_ltv",
    "/ad/dsp/merchant_jinniu_order_ltv:dsp_lps_ltv_stage2_wce2" });
  online_join_pb_->mutable_rank_cmd_model()->insert({ "ad_dsp_server_show_item_imp_inner_ecom",
    "/ad/dsp/rank/sctr:dsp_ctr_zr_ecom_res_kai2_0423_vali" });
  online_join_pb_->set_leverage_score(rank_result_->rank_price_info().leverage_score());
  online_join_pb_->set_mix_unify_gpm(rank_result_->rank_price_info().mix_unify_gpm());
  online_join_pb_->set_outer_u_ctr(rank_result_->predict_origin_info().outer_u_ctr());
  online_join_pb_->set_outer_u_cvr(rank_result_->predict_origin_info().outer_u_cvr());
  online_join_pb_->set_outer_u_noctcvr(rank_result_->predict_origin_info().outer_u_noctcvr());

  if (ad_base_info_->promotion_type() == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
    // falcon::Inc("front_server.ad_pack_online_join_pb.native");
    LOG_EVERY_N(INFO, 10000) << "ad_pack_online_join_pb creative_id = " << ad_base_info_->creative_id()
        << ", merchant_ltv = " << online_join_pb_->merchant_ltv()
        << ", server_show_ctr = " << online_join_pb_->server_show_ctr()
        << ", c1_order_paied = " << online_join_pb_->c1_order_paid()
        << ", wtr = " << online_join_pb_->wtr()
        << ", ad_live_audience = " << online_join_pb_->live_audience()
        << ", shop_jump = " << online_join_pb_->shop_jump();
  }

  // 注意注意注意！！！！
  // 注意注意注意！！！！
  // 注意注意注意！！！！
  // 以下字段的赋值都是带有判断逻辑的，若新加字段没有任何判断逻辑，请写在这段注释之前
  if (rank_result_->predict_origin_info().conv_nextstay() > 0) {
    online_join_pb_->set_conv_nextstay(rank_result_->predict_origin_info().conv_nextstay());
  }
  if (engine_base::IsSingleBidPurchase(target_bid_->ocpx_action_type())) {
    online_join_pb_->set_deep_rate(rank_result_->predict_origin_info().click_purchase_rate_single_bid());
    online_join_pb_->set_click2_deep_rate(
                      rank_result_->predict_origin_info().click2_purchase_rate_single_bid());
  } else {
    online_join_pb_->set_deep_rate(rank_result_->predict_origin_info().deep_rate());
    online_join_pb_->set_click2_deep_rate(rank_result_->predict_origin_info().click2_deep_rate());
  }
  online_join_pb_->set_price_bounded(session_data_->get_ad_price().price_bounded);
  bool is_bonus = rank_result_->rank_price_info().rank_benifit() - rank_result_->rank_price_info().bonus_cpm()
                  < rank_result_->rank_price_info().next_benifit();
  online_join_pb_->set_is_bonus(is_bonus);
  online_join_pb_->set_general_review(rank_result_->rank_base_info().feature_index_info().general_review());
  online_join_pb_->set_hot_review(rank_result_->rank_base_info().feature_index_info().hot_review());
  online_join_pb_->set_topk_review(rank_result_->rank_base_info().feature_index_info().topk_review());
  if (ad_base_info_->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE &&
      rank_result_->rank_stat_info().live_release_time() > 0) {
    online_join_pb_->set_coldboot_gap_time((base::GetTimestamp() / 1000000 -
    rank_result_->rank_stat_info().live_release_time()) / 60);
  } else if ((ad_base_info_->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO ||
      ad_base_info_->item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE) &&
      rank_result_->rank_stat_info().photo_release_time() > 0) {
    online_join_pb_->set_coldboot_gap_time((base::GetTimestamp() / 1000000 -
    rank_result_->rank_stat_info().photo_release_time()) / 60);
  }

  online_join_pb_->set_item_impression_nextstay(
                    rank_result_->predict_origin_info().item_impression_nextstay());
  // 填充 unify_deep_cvr
    // 联盟专用
  SetUniverseUnifyDeepCvr();
  online_join_pb_->set_c1_merchant_follow(rank_result_->predict_origin_info().c1_merchant_follow());
  online_join_pb_->set_cvr(rank_result_->predict_origin_info().cvr());
  online_join_pb_->set_landingpage_rate(rank_result_->predict_origin_info().landingpage_submit_rate());
  if (rank_result_->predict_origin_info().conv2_purchase() > 0) {
    online_join_pb_->set_conv2_purchase(rank_result_->predict_origin_info().conv2_purchase());
  }
  online_join_pb_->set_universe_ctr(rank_result_->predict_origin_info().ctr());
  online_join_pb_->set_c2_order_paid(rank_result_->predict_origin_info().c2_order_paied());
  SetUnifyRValue();
}

void OnlineJoinPack::SetUnifyRValue() {
  online_join_pb_->set_predict_unify_sctr(rank_result_->rank_rank_info().unify_sctr_value());
  online_join_pb_->set_model_sctr_start(
      kuaishou::ad::AdActionType_Name(rank_result_->rank_rank_info().sctr_start()));
  online_join_pb_->set_model_sctr_end(
      kuaishou::ad::AdActionType_Name(rank_result_->rank_rank_info().sctr_end()));
  online_join_pb_->set_predict_unify_ctr(rank_result_->rank_rank_info().ctr_value());
  online_join_pb_->set_model_ctr_start(kuaishou::ad::AdActionType_Name(
          rank_result_->rank_rank_info().ctr_start()));
  online_join_pb_->set_model_ctr_end(kuaishou::ad::AdActionType_Name(
          rank_result_->rank_rank_info().ctr_end()));
  online_join_pb_->set_predict_unify_cvr(rank_result_->rank_rank_info().cvr_value());
  online_join_pb_->set_model_cvr_start(kuaishou::ad::AdActionType_Name(
          rank_result_->rank_rank_info().cvr_start()));
  online_join_pb_->set_model_cvr_end(kuaishou::ad::AdActionType_Name(
          rank_result_->rank_rank_info().cvr_end()));
  online_join_pb_->set_predict_unify_deep_cvr(
      rank_result_->rank_rank_info().deep_cvr_value());
  online_join_pb_->set_deep_model_cvr_start(kuaishou::ad::AdActionType_Name(
          rank_result_->rank_rank_info().deep_cvr_start()));
  online_join_pb_->set_deep_model_cvr_end(kuaishou::ad::AdActionType_Name(
          rank_result_->rank_rank_info().deep_cvr_end()));
}

void OnlineJoinPack::SetUniverseUnifyDeepCvr() {
  // set predict_unify_deep_cvr 瀑布流直接取对应 deep_cvr , 联盟计算二跳到深度转化行为
  switch (target_bid_->deep_conversion_type()) {
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY:
      if (online_join_params_transparent_->twin_bid_strategy() ==
          kuaishou::ad::AdEnum::MIN_OCPC_DEEP_DEVICE ||
          online_join_params_transparent_->twin_bid_strategy() ==
          kuaishou::ad::AdEnum::ONLY_DEEP_DEVICE) {
        online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click2_purchase_device());
      } else {
        online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click2_purchase());
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY:
      online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click2_nextstay());
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT:
      online_join_pb_->set_predict_unify_deep_cvr(
                rank_result_->predict_origin_info().click2_credit_rate());
      break;
    default:
      break;
  }
}

void OnlineJoinPack::SetAdDeliverInfoData() {
  online_join_pb_->set_rewarded_mingtou_rerank_bid_tag(
    ad_deliver_info_->online_join_params_transparent().rewarded_mingtou_rerank_bid_tag());
  if (online_join_pb_->rewarded_mingtou_rerank_bid_tag()) {
    online_join_pb_->set_rewarded_mingtou_delta_price(
    ad_deliver_info_->online_join_params_transparent().rewarded_mingtou_delta_price());
  }
  online_join_pb_->set_pre_conv2_purchase(ad_deliver_info_->predict_info().pre_conv2_purchase());
  online_join_pb_->set_bonus_cpm(ad_deliver_info_->bonus_cpm());
  online_join_pb_->set_bonus_cpm_tag(ad_deliver_info_->bonus_cpm_tag());
  online_join_pb_->set_bonus_cpm_project_id(ad_deliver_info_->bonus_cpm_project_id());
  online_join_pb_->set_is_black_bonus_project(ad_deliver_info_->is_black_bonus_project());
  online_join_pb_->mutable_pid_tag()->CopyFrom(ad_deliver_info_->pid_tag());
  online_join_pb_->set_rank_type(ad_deliver_info_->rank_type());
  online_join_pb_->set_pre_conv_rate(ad_deliver_info_->predict_info().pre_conversion_rate());
  online_join_pb_->set_pre_lps_rate(ad_deliver_info_->predict_info().pre_landingpage_rate());
  online_join_pb_->set_pre_delivery_rate(ad_deliver_info_->predict_info().pre_delivery_rate());
  online_join_pb_->set_prerank_cpm_ltr(ad_deliver_info_->online_join_params_transparent().prerank_cpm_ltr());
  online_join_pb_->set_is_auto_expansion(static_cast<int32_t>(ad_deliver_info_->is_expansion()));
  online_join_pb_->set_pre_server_show_cvr(ad_deliver_info_->predict_info().pre_server_show_cvr());
  online_join_pb_->set_pre_click2_conv(ad_deliver_info_->predict_info().pre_click2_conv());
  online_join_pb_->set_pre_click2_lps(ad_deliver_info_->predict_info().pre_click2_lps());
  online_join_pb_->set_prerank_thanos_xdt_order_paied(ad_deliver_info_->predict_info().prerank_thanos_xdt_order_paied());  // NOLINT
  online_join_pb_->set_pre_deep_rate(ad_deliver_info_->predict_info().pre_deep_rate());
  online_join_pb_->set_pre_click2_deep_rate(ad_deliver_info_->predict_info().pre_click2_deep_rate());
  online_join_pb_->set_pre_merchant_follow(ad_deliver_info_->predict_info().pre_merchant_follow());
  online_join_pb_->set_pre_c2_merchant_follow(ad_deliver_info_->predict_info().pre_c2_merchant_follow());
  online_join_pb_->set_pre_purchase(ad_deliver_info_->predict_info().pre_purchase());
  online_join_pb_->set_pre_c2_purchase(ad_deliver_info_->predict_info().pre_c2_purchase());
  online_join_pb_->set_prerank_universe_invoke(ad_deliver_info_->predict_info().prerank_universe_invoke());
  online_join_pb_->set_prerank_universe_c1_event_order(
                       ad_deliver_info_->predict_info().prerank_universe_c1_event_order());
  if (ad_deliver_info_->predict_info().pre_conv_nextstay() > 0) {
    online_join_pb_->set_pre_conv_nextstay(
        ad_deliver_info_->predict_info().pre_conv_nextstay());
  }
  online_join_pb_->set_deep_cvr_click(
        ad_deliver_info_->predict_info().deep_cvr_info().deep_cvr_click());
  online_join_pb_->set_ori_hostpage_pos_id(ad_deliver_info_->pos());
  online_join_pb_->set_prerank_merchant_ltv(ad_deliver_info_->predict_info().prerank_merchant_ltv());
  online_join_pb_->set_landingpage_rate(ad_deliver_info_->predict_info().landingpage_rate());
  online_join_pb_->set_pre_ctr(ad_deliver_info_->predict_info().pre_ctr());
  online_join_pb_->set_tax_rate(0);
  online_join_pb_->set_tax_amount(0);
  online_join_pb_->set_disable_ad_mark(ad_deliver_info_->disable_ad_mark());
  online_join_pb_->set_plc_biz_type(style_info_->photo_status().plc_biz_type());
}

void OnlineJoinPack::SetAdBaseInfoData() {
  online_join_pb_->set_photo_transform_type(ad_base_info_->photo_transform_type());
  if (ad_base_info_->photo_transform_type() == kuaishou::ad::AdEnum::UNKNOWN_REPLACY_TYPE) {
    online_join_pb_->set_photo_transform_type(ad_base_info_->photo_transform_type());
  }
  online_join_pb_->set_unit_type(ad_base_info_->unit_type());
  online_join_pb_->set_is_30d_sell_flag(ad_base_info_->is_30d_sell_flag());
  online_join_pb_->set_virtual_creative_id(ad_base_info_->virtual_creative_id());
  online_join_pb_->set_bg_cover_id(ad_base_info_->bg_cover_id());
  // online_join_pb_->set_prophet_boost_exp_tag(ad_base_info_->prophet_boost_exp_tag());
  // online_join_pb_->set_prophet_boost_ratio(ad_base_info_->prophet_boost_ratio());
  online_join_pb_->set_cover_title_id(ad_base_info_->cover_title_id());
  online_join_pb_->set_cover_sticker_style_id(ad_base_info_->cover_sticker_style_id());
  online_join_pb_->set_description_title_id(ad_base_info_->description_title_id());
  online_join_pb_->set_display_info(ad_base_info_->display_info());
  online_join_pb_->set_action_bar_display_info(ad_base_info_->client_style_info()
                                                         .landing_page_actionbar_info()
                                                         .action_bar_display_info());
  online_join_pb_->set_new_creative_tag(ad_base_info_->new_creative_tag());
  online_join_pb_->set_industry_id(ad_base_info_->new_industry_id());
  if (ad_base_info_->counterpart_photo_id() > 0) {
    online_join_pb_->set_counterpart_photo_id(ad_base_info_->counterpart_photo_id());
    session_data_->dot_perf->Count(1, "replace_photo_id_online_param");
  }
  online_join_pb_->set_industry_id_v3(ad_base_info_->industry_id_v3());

  // 素材类型
  if (ad_base_info_->has_material_info()) {
    if (ad_base_info_->material_info().material_feature_size() > 0) {
      const auto& feature = ad_base_info_->material_info().material_feature(0);
      online_join_pb_->mutable_material_feature()->mutable_material_size()->set_height(
          feature.material_size().height());
      online_join_pb_->mutable_material_feature()->mutable_material_size()->set_width(
          feature.material_size().width());
      online_join_pb_->mutable_material_feature()->set_material_feature_type(
          feature.material_feature_type());
      online_join_pb_->mutable_material_feature()->set_material_time_duration(
          feature.material_time_duration());
      online_join_pb_->set_imp_material_type(ad_base_info_->material_info().derivative_material_type());
      online_join_pb_->set_rule_id(feature.rule_id());
      online_join_pb_->set_material_exp_tag(feature.material_exp_tag());
      online_join_pb_->set_template_id(feature.template_id());
      const auto& sdk_exp_param = session_data_->copy_ud_sdk_exp_param();
      base::Json sdk_exp_param_json(base::StringToJson(sdk_exp_param));
      bool is_horizontal = feature.material_size().width() > feature.material_size().height();
      std::string style_code = is_horizontal ? absl::Substitute("$0$1$2", 1,
                        sdk_exp_param_json.GetInt("interstitialStyleId", 0),
                        sdk_exp_param_json.GetInt("interstitialButtonType", 0))
                        : "200";
      online_join_pb_->set_interstitial_style_code(style_code);
      online_join_pb_->set_actual_material_id(ad_base_info_->material_info().actual_material_id());
      online_join_pb_->set_material_type_origin(style_info_->creative().creative_material_type());
    }
  }
  auto author_id = result_->ad_deliver_info().ad_base_info().author_id();
  if (session_data_->get_follow_ids().find(author_id) != session_data_->get_follow_ids().end()) {
    online_join_pb_->set_is_author_fans(1);
    result_->mutable_ad_deliver_info()->mutable_online_join_params_transparent()->set_is_fan_follow(true);
    result_->mutable_ad_deliver_info()->mutable_online_join_params_transparent()->set_is_author_follow(1);
  } else {
    online_join_pb_->set_is_author_fans(2);
    result_->mutable_ad_deliver_info()->mutable_online_join_params_transparent()->set_is_author_follow(2);
  }
  if (ad_base_info_->campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
      ad_base_info_->campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
      ad_base_info_->industry_id_v3() == 1022) {  // 内循环调起 app_name 落表
    online_join_pb_->set_app_name(ad_base_info_->app_name());
  }

  online_join_pb_->set_allow_search_bigv_live_card(
      ad_base_info_->allow_search_bigv_live_card());
  if (::ks::ad_base::IsMallTabReuqest(session_data_->get_sub_page_id()) ||
        session_data_->get_page_id() == ks::ad_base::AdPageId::kBuyerHomePagePageId) {
    online_join_pb_->set_merchant_card_type(result_->ad_deliver_info().ad_base_info().merchant_card_type());
  }
  auto playlet_sdpa_ = ks::engine_base::IndustryPlayletSdpaIns().GetData();
  if (playlet_sdpa_ && playlet_sdpa_.get()) {
    auto playlet_sdpa_ptr_ = playlet_sdpa_->find(ad_base_info_->unit_id());
    if (playlet_sdpa_ptr_ != playlet_sdpa_->end()) {
      auto& playlet_sdpa_data = playlet_sdpa_ptr_->second;
      online_join_pb_->set_playlet_name(playlet_sdpa_data.playlet_name);
      online_join_pb_->set_playlet_starring(playlet_sdpa_data.playlet_starring);
      online_join_pb_->set_playlet_min_charge(playlet_sdpa_data.playlet_min_charge);
      online_join_pb_->set_playlet_plot(playlet_sdpa_data.playlet_plot);
    }
  }
}

void OnlineJoinPack::SetInfoFromContextData() {
  online_join_pb_->set_register_timestamp(
      session_data_->get_ad_request()->ad_user_info().register_timestamp());
  online_join_pb_->set_first_active_time_ms(
      session_data_->get_ad_request()->ad_user_info().first_active_time_ms());
  online_join_pb_->set_page_size(session_data_->get_ad_request()->page_size());
  online_join_pb_->set_cold_start(session_data_->get_ad_request()->reco_request_info().pcursor().length());
  online_join_pb_->set_interactive_form(
      static_cast<int64>(ks::ad_base::GetInteractiveForm(*(session_data_->get_ad_request()))));
  online_join_pb_->set_refresh_direction(
      static_cast<int64>(ks::ad_base::GetRefreshDirection(*(session_data_->get_ad_request()))));
  online_join_pb_->set_last_pv_timestamp(
      session_data_->get_ad_request()->front_internal_data().last_explore_pv_timestamp());
  online_join_pb_->set_last_page_size(
      session_data_->get_ad_request()->front_internal_data().last_explore_pv_page_size());
  online_join_pb_->set_last_ad_pos(
      session_data_->get_ad_request()->front_internal_data().last_explore_pv_last_ad_pos());
  online_join_pb_->set_last_first_screen_ad_timestamp(
      session_data_->get_ad_request()->front_internal_data().first_screen_ad_shw_timestamp());
  online_join_pb_->set_browse_ad_interval_seconds(
      session_data_->get_ad_request()->front_internal_data().browse_ad_interval_seconds());
  online_join_pb_->set_browse_ads_num(session_data_->get_ad_request()
                                    ->front_internal_data().browse_ads_num());
  online_join_pb_->set_sdk_version(session_data_->get_ad_request()
                                    ->universe_ad_request_info().sdk_version());
  online_join_pb_->set_sdk_type(session_data_->get_ad_request()
                                    ->universe_ad_request_info().sdk_type());
  online_join_pb_->set_protocol_version(session_data_->get_ad_request()
                                    ->universe_ad_request_info().protocol_version());
  for (const auto& predict_score : session_data_->get_ad_request()->predict_score()) {
    online_join_pb_->add_predict_score()->CopyFrom(predict_score);
  }
  online_join_pb_->set_enable_person_ad_pos_gap_exptag(0);
  online_join_pb_->set_sess_engage_one_tenth(0);
  online_join_pb_->set_sess_engage_two_tenth(0);
  online_join_pb_->set_sess_engage_third_tenth(0);
  online_join_pb_->set_sess_engage_avg_play_time(0);
  online_join_pb_->set_sess_engage_effect_count(0);
  online_join_pb_->set_reco_fresh_type_bit(
      session_data_->get_ad_request()->reco_user_info().reco_fresh_type_bit());
  online_join_pb_->set_is_live_stream_core_user(false);
  online_join_pb_->set_order_conv_type(session_data_->get_ad_request()->ad_user_info().order_conv_type());
  online_join_pb_->set_buyer_effective_type(
      session_data_->get_ad_request()->ad_user_info().buyer_effective_type());
  online_join_pb_->set_user_refund_score(session_data_->get_ad_request()->ad_user_info().user_refund_score());
  online_join_pb_->set_user_badcomn_score(
      session_data_->get_ad_request()->ad_user_info().user_badcomn_score());
  online_join_pb_->set_user_level_v2_risk(
      session_data_->get_ad_request()->ad_user_info().user_level_v2_risk());
  online_join_pb_->set_outer_adload_group_level(std::to_string(
      session_data_->get_ad_request()->ad_user_info().outer_adload_group_level()));
  if (session_data_->get_pos_manager().IsInnerExplore() ||
      ad_base::IsWanhe(session_data_->get_sub_page_id())) {
      online_join_pb_->set_relative_photo_id(session_data_->get_ad_request()->relative_photo_id());
  }
  online_join_pb_->set_playlet_user_tag(
  session_data_->get_ad_request()->ad_user_info().smart_offer_user_tag());
  online_join_pb_->set_playlet_control_score(
  session_data_->get_ad_request()->ad_user_info().offer_control_score());
  online_join_pb_->set_playlet_treatment_score(
  session_data_->get_ad_request()->ad_user_info().offer_treatment_score());
}

void OnlineJoinPack::SetTargetBidData() {
  const auto &unit = style_info_->unit();
  online_join_pb_->set_cpa_ratio(1.0);
  online_join_pb_->set_deep_risk_control_rate(
      target_bid_->deep_risk_control_rate());
  online_join_pb_->set_deep_flow_control_rate(target_bid_->deep_flow_control_rate());
  online_join_pb_->set_deep_min_coef(target_bid_->deep_min_coef());
  online_join_pb_->set_deep_min_bid_coef(target_bid_->deep_min_bid_coef());
  if (online_join_params_transparent_->speed_type() == kuaishou::ad::AdEnum::SPEED_NO_BID ||
      ad_base_info_->bid_type() == kuaishou::ad::AdEnum::MCB) {  // get from target
    online_join_pb_->set_roi_ratio(target_bid_->roi_ratio());
    // 全站计划下的 nobid 单元不用 target 也不用正排出价，而是填充计划出价
    if (style_info_->unit().ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS &&
        style_info_->has_ecom_hosting_project()) {
      online_join_pb_->set_roi_ratio(style_info_->ecom_hosting_project().roi_ratio());
    }
  } else {  // get from index
    online_join_pb_->set_roi_ratio(unit.roi_ratio());
  }
}

void OnlineJoinPack::SetAdDataV2Data() {
  // 设置下载类中间页自动填充埋点
  const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
  online_join_pb_->set_is_download_landing_page_mould(
      ad_data_v2->h5_control_info().is_download_landing_page_mould());
  if (ad_data_v2->h5_control_info().is_download_landing_page_mould()) {
    falcon::Inc("front_server.is_download_landing_page_mould_res");
  }
  // 设置下载中间页 url type
  if (result_->ad_deliver_info().ad_base_info().campaign_type()
      == ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_APP) {
    int32 download_mid_url_type = -1;
    if (ad_data_v2->has_h5_url()) {
      const auto& h5_url = ad_data_v2->h5_url();
      // 系统提供的模版 url
      const auto& download_mid_page_url_type_map = FrontKconfUtil::kuaishouDownloadMidPageUrlTypeMap();
      const auto& iter = download_mid_page_url_type_map->find(h5_url);
      if (iter != download_mid_page_url_type_map->end()) {
        download_mid_url_type = iter->second;
      } else {
        download_mid_url_type = 10;
      }
    }
    online_join_pb_->set_download_mid_url_type(download_mid_url_type);
    falcon::Inc(absl::StrCat("front_server.download_mid_url_type_", download_mid_url_type).data());
  }

  // ad_server 填充空，此处保持一致，填充为空
  online_join_pb_->set_display_info_origin(std::string());
  online_join_pb_->mutable_knews_params_transparent()->set_inspire_action_type(
      ad_data_v2->inspire_ad_info().inspire_action().type());
  // web card type
  const auto& origin_style_info = ad_data_v2->origin_style_info();
  if (origin_style_info.ad_prefer_style_size() > 0) {
    online_join_pb_->set_web_card_type(origin_style_info.ad_prefer_style(0).resource_type());
  }
  online_join_pb_->set_magicsite_page_form(style_info_->magic_site_page().page_form());
  // playlet smart offers
  online_join_pb_->set_has_smart_offer(ad_data_v2->has_smart_offer());
  online_join_pb_->set_smart_offer_value(ad_data_v2->smart_offer_value());
}

void OnlineJoinPack::SetAdxData() {
  if (result_->ad_source_type() == kuaishou::ad::ADX) {
    online_join_pb_->set_adx_tag_id(ad_base_info_->tag_id());
    online_join_pb_->set_adx_winprice(GetAdxWinPrice(*result_));
  }
  online_join_pb_->set_adx_req_admit_fail_reason(
      session_data_->get_ad_response()->adx_req_admit_fail_reason());
}

void OnlineJoinPack::SetEnvInfo() {
  online_join_pb_->set_kws_env(ks::ad_base::util::GeAdServiceVersion().kws_env());
  // 联盟 front_hostname 增加实例名 以'/‘分割。格式: host/pod/region/az/stage
  online_join_pb_->set_front_hostname(serving_base::GetHostName() + '/' +
                                      ks::ad_base::util::GetMyPodName() + '/' +
                                      ks::ad_base::util::GeAdServiceVersion().kws_env());
}

void OnlineJoinPack::SetUniverseData() {
  // 联盟专用
  kuaishou::ad::AdEnum::ConversionType ad_operation_type
                  = ::kuaishou::ad::AdEnum_ConversionType_UNKNOWN_CONVERSION;
  switch (result_->ad_deliver_info().ad_base_info().campaign_type()) {
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_APP:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MERCHANT_APP_PROMOTE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE:
      ad_operation_type = kuaishou::ad::AdEnum_ConversionType_CONVERION_APP_DOWNLOAD;  // 下载广告
      break;
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_TAOBAO:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_LANDING_PAGE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_SITE_PAGE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_WX_MINI_APP:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_CID:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_SEARCH:
      if (result_->ad_deliver_info().ad_base_info().is_universe_jinniu_to_deeplink()) {
        ad_operation_type = kuaishou::ad::AdEnum_ConversionType_CONVERION_APP_DOWNLOAD;  // 下载广告
        break;
      }
      ad_operation_type = kuaishou::ad::AdEnum_ConversionType_CONVERION_OPEN_HTML5;  // 落地页广告
      break;
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_APP_ADVANCE:
      if (!(ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())) == "kuaishou" ||
            ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())) == "kuaishou_nebula")) {
        ad_operation_type = kuaishou::ad::AdEnum_ConversionType_CONVERION_APP_DOWNLOAD;  // 下载广告
      } else {
        ad_operation_type = kuaishou::ad::AdEnum_ConversionType_CONVERION_OPEN_HTML5;  // deeplink
      }
      break;
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE:
      if (engine_base::AdKconfUtil::enableMerchantSetH5AdOperationType()) {
        ad_operation_type = kuaishou::ad::AdEnum_ConversionType_CONVERION_OPEN_HTML5;
      }
      break;
    default:
      break;
  }
  if (session_data_->UniverseRequest().universe_task_type() > 0) {
    online_join_pb_->set_universe_task_type(session_data_->UniverseRequest().universe_task_type());
  }
  const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
  if (ad_operation_type == kuaishou::ad::AdEnum_ConversionType_CONVERION_APP_DOWNLOAD) {
    if (ad_data_v2->has_h5_url()) {  //  广告主自己上传下载详情页
      online_join_pb_->set_universe_h5_type(kuaishou::ad::AdEnum_LandingPageType_APP_DOWNLOAD_UPLOAD);
    } else {  //  无下载详情页
      if (ad_data_v2->has_app_detail_info()
        && ad_data_v2->app_detail_info().cdn_screen_short_urls_size() > 0) {  //  抓取成功
        online_join_pb_->set_universe_h5_type(kuaishou::ad::AdEnum_LandingPageType_APP_DOWNLOAD_BUILD);
      } else {  //  抓取失败
        online_join_pb_->set_universe_h5_type(kuaishou::ad::AdEnum_LandingPageType_TYPE_UNKNOWN);
      }
    }
  } else if (ad_operation_type == kuaishou::ad::AdEnum_ConversionType_CONVERION_OPEN_HTML5) {
    online_join_pb_->set_universe_h5_type(::kuaishou::ad::AdEnum_LandingPageType_NON_APP_DOWNLOAD);
  }

  if (session_data_->get_ad_request()->trace_log_sampling_flag_v2_table() > 0) {
    online_join_pb_->set_is_trace_log_flow(true);
  }

  if (!ad_data_v2->market_uri().empty()) {
    online_join_pb_->set_is_jump_app_market(true);
  }
  online_join_pb_->set_is_universe_fake_user(
                          session_data_->get_ad_request()->ad_user_info().is_universe_fake_user());
  online_join_pb_->set_cooperation_mode(static_cast<int32>(
                          session_data_->get_ad_request()->universe_ad_request_info().cooperation_mode()));

  // 金牛广告 H5 实验分路标志, 0 为快手用户走 deeplink, 非快手用户过滤金牛广告
  // 1 为快手和非快手用户都走 H5
  // 2 为快手用户走 deeplink, 非快手用户走 H5
  if (session_data_->get_jinniu_branch_test() >= 0) {
    online_join_pb_->set_jinniu_branch_test(session_data_->get_jinniu_branch_test());
  }
  // 直营电商（金牛）下发 pay_type 付款方式字段
  if (style_info_->has_unit_small_shop_merchant_support_info()) {
    online_join_pb_->set_ecom_product_pay_type(
                  style_info_->unit_small_shop_merchant_support_info().pay_type());
  }
  const auto& sdk_exp_param = session_data_->copy_ud_sdk_exp_param();
  Json sdk_exp_param_json(StringToJson(sdk_exp_param));
  online_join_pb_->set_endcard_new_style(sdk_exp_param_json.GetInt("endCardNewStyle", 0));  // endCard 样式
  online_join_pb_->set_endcard_new_style_64(sdk_exp_param_json.GetInt("endCardNewStyle", 0));  // endCard 样式
  if (!session_data_->borrow_ud_play_card_new_style().empty()) {  // playCard 样式
    int64_t playcard_new_style = 0;
    if (!absl::SimpleAtoi(session_data_->borrow_ud_play_card_new_style(), &playcard_new_style)) {
      LOG(ERROR) << "SetUniverseData convert error, playcard_new_style: "
                  << session_data_->borrow_ud_play_card_new_style();
    } else {
      online_join_pb_->set_playcard_new_style(playcard_new_style);
      online_join_pb_->set_playcard_new_style_64(playcard_new_style);
    }
  }
  if (session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() > 0) {
    for (const auto& pos_seq_id : session_data_->get_ad_request()
                  ->universe_ad_request_info().imp_info(0).pos_seq_ids()) {
      online_join_pb_->add_pos_seq_ids(pos_seq_id);
    }
  }

  online_join_pb_->set_is_universe_opt(func_is_universe_opt(style_info_->unit().resource_ids()));
  online_join_pb_->set_is_universe_preload_check(session_data_->get_ad_request()->preload_check());
  bool is_playable = (style_info_->unit_support_info().playable_switch() == 2 ||
                      style_info_->play_info().upload_source() == 2);
  is_playable |= (style_info_->play_info().upload_source() == 3);
  online_join_pb_->set_is_playable(is_playable);

  online_join_pb_->set_origin_invalid_type(
      static_cast<int>(session_data_->get_ud_universe_elastic_info().origin_invalid_type));

  // 视频广告的创意物料类型, 1 为竖版视频, 2 为横版视频等等
  online_join_pb_->set_universe_inspire_video_creative_material_type(
                                style_info_->creative().creative_material_type());
  //联盟专用
  online_join_pb_->set_shadow_creative_id(online_join_params_transparent_->shadow_creative_id());
  if (session_data_->get_enable_universe_fix_elastic_idx()) {
    online_join_pb_->set_universe_elastic_config_group_idx(
        session_data_->get_ud_universe_elastic_info().final_config_group_index);
  } else {
    online_join_pb_->set_universe_elastic_config_group_idx(
        online_join_params_transparent_->universe_elastic_config_group_idx());
  }
  if (session_data_->get_is_universe_cached_res()) {
    online_join_pb_->set_universe_elastic_config_group_idx_for_cache(
        online_join_params_transparent_->universe_elastic_config_group_idx());
  }
  online_join_pb_->set_universe_raw_elastic_config_group_idx(session_data_->copy_ud_raw_config_group_index());

  online_join_pb_->set_universe_elastic_search_elapsed_time(
        online_join_params_transparent_->universe_elastic_search_elapsed_time());
  online_join_pb_->set_universe_bid_boost_coef(online_join_params_transparent_->universe_bid_boost_coef());
  online_join_pb_->set_universe_price_discount_coef(
    online_join_params_transparent_->universe_price_discount_coef());
  online_join_pb_->set_raw_auction_bid(online_join_params_transparent_->raw_auction_bid());
  online_join_pb_->set_raw_cpm(online_join_params_transparent_->raw_cpm());
  online_join_pb_->set_universe_industry_audience_bonus_type(
    online_join_params_transparent_->universe_industry_audience_bonus_type());
  online_join_pb_->set_universe_industry_audience_before_auction_bid(
    online_join_params_transparent_->universe_industry_audience_before_auction_bid());
  online_join_pb_->set_universe_industry_audience_after_auction_bid(
    online_join_params_transparent_->universe_industry_audience_after_auction_bid());
  online_join_pb_->set_auto_roas(online_join_params_transparent_->auto_roas());
  online_join_pb_->set_is_universe_system_bonus(online_join_params_transparent_->is_universe_system_bonus());
  online_join_pb_->set_universe_system_bonus_type(online_join_params_transparent_->universe_system_bonus_type());  // NOLINT
  online_join_pb_->set_cost_ratio_virtual_bonus(online_join_params_transparent_->cost_ratio_virtual_bonus());
  online_join_pb_->set_universe_inner_strategy_ratio(
                    rank_result_->ad_rank_trans_info().universe_inner_strategy_ratio());
  online_join_pb_->set_universe_inner_strategy_tag(
                    rank_result_->ad_rank_trans_info().universe_inner_strategy_tag());
  online_join_pb_->set_universe_tiny_query_type(
                    rank_result_->ad_rank_trans_info().universe_tiny_query_type());
  online_join_pb_->set_universe_quality_score(
                    rank_result_->ad_rank_trans_info().universe_quality_score());
  online_join_pb_->set_universe_inner_bound_explore_ratio(
                    rank_result_->ad_rank_trans_info().universe_inner_bound_explore_ratio());
  online_join_pb_->set_universe_win_ecpm_ratio(
                    rank_result_->ad_rank_trans_info().universe_win_ecpm_ratio());
  online_join_pb_->set_universe_w_level_crowd_ratio(
                    rank_result_->ad_rank_trans_info().universe_w_level_crowd_ratio());
  online_join_pb_->set_universe_win_ecpm_for_top_ratio(
                    rank_result_->ad_rank_trans_info().universe_win_ecpm_for_top_ratio());
  online_join_pb_->set_universe_llm_u2p_ecpc_raito(
                    rank_result_->ad_rank_trans_info().universe_llm_u2p_ecpc_raito());
  online_join_pb_->set_universe_llm_u2p_v2_ecpc_raito(
                    rank_result_->ad_rank_trans_info().universe_llm_u2p_v2_ecpc_raito());
  online_join_pb_->set_universe_ks_pay_crowd_explore_ratio(
                    rank_result_->ad_rank_trans_info().universe_ks_pay_crowd_explore_ratio());
  online_join_pb_->set_universe_ks_active_crowd_explore_ratio(
                    rank_result_->ad_rank_trans_info().universe_ks_active_crowd_explore_ratio());
  online_join_pb_->set_universe_cheap_flow_game_explore_ratio(
                    rank_result_->ad_rank_trans_info().universe_cheap_flow_game_explore_ratio());
  online_join_pb_->set_universe_cheap_flow_new_game_explore_ratio(
                    rank_result_->ad_rank_trans_info().universe_cheap_flow_new_game_explore_ratio());
  if (session_data_->get_ud_p2live_to_live_cids().count(ad_base_info_->creative_id())) {
    online_join_pb_->set_is_universe_show_live_stream(true);
  }
  online_join_pb_->set_universe_system_bonus_start_time(
    online_join_params_transparent_->universe_system_bonus_start_time());
  online_join_pb_->set_up_model_no_pay_rate(rank_result_->ad_rank_trans_info().up_model_no_pay_rate());
  online_join_pb_->set_up_model_low_pay_rate(rank_result_->ad_rank_trans_info().up_model_low_pay_rate());
  online_join_pb_->set_up_model_medium_pay_rate(
      rank_result_->ad_rank_trans_info().up_model_medium_pay_rate());
  online_join_pb_->set_up_model_high_pay_rate(rank_result_->ad_rank_trans_info().up_model_high_pay_rate());
  online_join_pb_->set_c1_conv_fix(online_join_params_transparent_->c1_conv_fix());
  online_join_pb_->set_ug_pltv7(online_join_params_transparent_->ug_pltv7());
  online_join_pb_->set_ug_roi_goal(online_join_params_transparent_->ug_roi_goal());
  online_join_pb_->set_ug_roi_bid_coef(online_join_params_transparent_->ug_roi_bid_coef());
  online_join_pb_->set_ug_roi_discount_coef(online_join_params_transparent_->ug_roi_discount_coef());
  online_join_pb_->set_ug_roi_ecpm_truth(online_join_params_transparent_->ug_roi_ecpm_truth());
  online_join_pb_->set_ug_roi_deep_cvr_truth(online_join_params_transparent_->ug_roi_deep_cvr_truth());
  online_join_pb_->set_is_universe_cached_res(session_data_->get_is_universe_cached_res());
  online_join_pb_->set_device_id_hash_key(session_data_->copy_device_id_hash_key());
  online_join_pb_->set_universe_hold_and_cache(session_data_->get_ud_hold_and_cache());
  online_join_pb_->set_universe_write_in_cache(session_data_->get_ud_write_in_cache());
  online_join_pb_->mutable_universe_traffic_quality_score()->set_fill_rate(
    session_data_->get_universe_quality_data_fill_rate());
  online_join_pb_->mutable_universe_traffic_quality_score()->set_pos_cpm(
      session_data_->get_universe_quality_data_pos_post_cpm());
  online_join_pb_->mutable_universe_traffic_quality_score()->set_quality_score(
      session_data_->get_universe_quality_data_predict_cpm());
  online_join_pb_->mutable_universe_traffic_quality_score()->set_model_predict_cpm(
      session_data_->get_universe_quality_data_model_predict_cpm());
  online_join_pb_->set_physical_pos_id(session_data_->get_ud_physical_pos_id());
  online_join_pb_->set_up_model_high_ltv_rate(online_join_params_transparent_->up_model_high_ltv_rate());
  online_join_pb_->set_resource_id(online_join_params_transparent_->resource_id());
  online_join_pb_->set_target_factor(rank_result_->ad_rank_trans_info().target_factor());
  // 判断是否非标广告 改成 ad_server 透传
  if (online_join_params_transparent_->is_universe_feibiao()) {
    online_join_pb_->set_is_universe_feibiao(true);
  }
  // 直营电商广告起量标识 后续透传
  if (online_join_params_transparent_->universe_ad_push_start_type() > 0) {
    online_join_pb_->set_universe_ad_push_start_type(
          online_join_params_transparent_->universe_ad_push_start_type());
  }
  online_join_pb_->set_universe_media_level(online_join_params_transparent_->universe_media_level());
  if ((ks::ad_base::GetMediumAttribute(*(session_data_->get_ad_request())) == 2) ||
      (ks::ad_base::GetMediumAttribute(*(session_data_->get_ad_request())) == 4)) {
    online_join_pb_->set_universe_pos_target_cpm(online_join_params_transparent_->universe_pos_target_cpm());
    online_join_pb_->set_universe_pos_cpm_bound(online_join_params_transparent_->universe_pos_cpm_bound());
    online_join_pb_->set_universe_pos_org_target_cpm
                    (online_join_params_transparent_->universe_pos_org_target_cpm());
    online_join_pb_->set_universe_pos_first_ecpm(online_join_params_transparent_->universe_pos_first_ecpm());
    online_join_pb_->set_original_scvr(online_join_params_transparent_->original_scvr());
    online_join_pb_->set_calibrated_scvr(online_join_params_transparent_->calibrated_scvr());
    online_join_pb_->set_refactor_deep_cvr(online_join_params_transparent_->refactor_deep_cvr());
    LOG_EVERY_N(INFO, 5000) << "write universe valiables to online join param, "
          <<  "universe_pos_target_cpm:"<< online_join_params_transparent_->universe_pos_target_cpm()
          << ",universe_pos_cpm_bound:"<< online_join_params_transparent_->universe_pos_cpm_bound()
          << ",universe_pos_first_ecpm:"<< online_join_params_transparent_->universe_pos_first_ecpm()
          << ",original_scvr:" << online_join_params_transparent_->original_scvr()
          << ",calibrated_scvr:" << online_join_params_transparent_->calibrated_scvr();
  }
  if (ad_base_info_->has_package_name() && !ad_base_info_->package_name().empty() &&
      session_data_->get_ud_app_set().count(ad_base_info_->package_name()) > 0) {
    online_join_pb_->set_is_installed_app(true);
  }

  bool style_component_flag = ks::front_server::UniverseData::GetStyleComponentFlag(session_data_);
  online_join_pb_->set_universe_style_component_flag(style_component_flag);
  if (result_->ad_deliver_info().ad_base_info().style_info_size() > 0) {
    const auto& style_info = result_->ad_deliver_info().ad_base_info().style_info(0);
    // playcard
    if (style_info.play_card_id64() > 0) {
      auto* style_data = online_join_pb_->add_style_data();
      style_data->set_style_type(style_component_flag ? 10002 : 2);
      style_data->set_style_material_id(style_info.play_card_id64());
    } else if (style_info.play_card_id() > 0) {
      auto* style_data = online_join_pb_->add_style_data();
      style_data->set_style_type(style_component_flag ? 10002 : 2);
      style_data->set_style_material_id(style_info.play_card_id());
    }
    // endcard
    if (style_info.end_card_id64() > 0) {
      auto* style_data = online_join_pb_->add_style_data();
      style_data->set_style_type(style_component_flag ? 10003 : 3);
      style_data->set_style_material_id(style_info.end_card_id64());
    } else if (style_info.end_card_id() > 0) {
      auto* style_data = online_join_pb_->add_style_data();
      style_data->set_style_type(style_component_flag ? 10003 : 3);
      style_data->set_style_material_id(style_info.end_card_id());
    }
    // 回流页样式
    auto reflow_iter = style_info.extend_style().find(kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_REFLOW_STYLE);
    if (reflow_iter != style_info.extend_style().end() && reflow_iter->second.id() > 0) {
      auto* style_data = online_join_pb_->add_style_data();
      style_data->set_style_type(4);
      style_data->set_style_material_id(reflow_iter->second.id());
    }
    // 新插屏广告铺开大小
    auto rollout_iter =
        style_info.extend_style().find(kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_AD_ROLLOUT_SIZE);
    if (rollout_iter != style_info.extend_style().end() && rollout_iter->second.id() > 0) {
      online_join_pb_->set_ad_rollout_size(rollout_iter->second.id());
    }
    // 组件化样式
    auto component_iter =
      style_info.extend_style().find(kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_COMPONENT_STYLE);
    if (component_iter != style_info.extend_style().end() && component_iter->second.id() > 0) {
      auto* style_data = online_join_pb_->add_style_data();
      style_data->set_style_type(100);
      style_data->set_style_material_id(component_iter->second.id());
    }
    if (result_->ad_deliver_info().ad_base_info().ad_source_type() == kuaishou::ad::AdSourceType::ADX
        || style_component_flag) {
      online_join_pb_->set_playcard_new_style_64(style_info.play_card_id64());
      online_join_pb_->set_endcard_new_style_64(style_info.end_card_id64());
    }
  }
  online_join_pb_->set_hit_cache_level(result_->ad_deliver_info().hit_cache_level());

  // 样式优选标记位：
  // 0x1 : 广告素材被替换
  int32_t style_server_tag = 0;
  if (session_data_->get_ud_style_server_ad_set().count(ad_base_info_->creative_id()) > 0) {
    style_server_tag |= 0x1;
  }
  online_join_pb_->set_style_server_tag(style_server_tag);
  online_join_pb_->set_universe_forced_impression_bonus(
                    session_data_->get_ud_universe_forced_impression_bonus());

  // 联盟内循环 pv 资源重分配 ratio 落表
  online_join_pb_->set_inner_pv_allocation_strategy_ratio(
                    session_data_->get_ud_inner_pv_allocation_strategy_ratio());

  // 联盟内循环高价值人群首位探索 ratio 落表
  online_join_pb_->set_universe_inner_hv_top1_explore_threshold_ratio(
                    session_data_->get_ud_universe_inner_hv_top1_explore_threshold_ratio());
  online_join_pb_->set_universe_inner_hv_top1_explore_cpm_ratio(
                    session_data_->get_ud_universe_inner_hv_top1_explore_cpm_ratio());

  // 样式物料 id
  auto iter = session_data_->get_style_material_map().find(ad_base_info_->creative_id());
  if (iter != session_data_->get_style_material_map().end() &&
      iter->second.size() > 0) {
    online_join_pb_->set_style_material_id(iter->second[0].id);
    online_join_pb_->set_bind_account_id(iter->second[0].bind_account_id);
  }
  // 跳转微信小程序标志 deeplink 为空且 跳转 wx 小程序字段非空
  if (!ad_base_info_->is_universe_jinniu_to_deeplink() &&
      !session_data_->borrow_media_wx_small_app_id().empty()) {
    online_join_pb_->set_is_wx_small_app_xdt_ad(true);
  }

  // 联盟内循环 user_tag 落表
  uint64 user_tag = session_data_->get_ad_request()->ad_user_info().universe_inner_loop_user_tag();
  if (user_tag > 0) {
    online_join_pb_->set_union_inner_loop_user_tag(user_tag);
  }
  bool is_origin_small_game_direct_jump = IsSmallGameComponent(style_info_->magic_site_page().page_component())  // NOLINT
                                          && (style_info_->magic_site_page().direct_call_type() == 1);
  if (session_data_->get_force_direct_call_cids().count(style_info_->creative().id()) > 0) {
    online_join_pb_->set_is_wechat_game_direct_call(true);
  }

  int32_t universe_live_audience_cali_tag =
                  rank_result_->ad_rank_trans_info().universe_live_audience_cali_tag();
  int32_t universe_live_pay_rate_cali_tag =
                  rank_result_->ad_rank_trans_info().universe_live_pay_rate_cali_tag();
  if (universe_live_audience_cali_tag > 0) {
    online_join_pb_->set_universe_live_audience_cali_tag(universe_live_audience_cali_tag);
  }
  if (universe_live_pay_rate_cali_tag > 0) {
    online_join_pb_->set_universe_live_pay_rate_cali_tag(universe_live_pay_rate_cali_tag);
  }
  int32_t universe_video_pay_rate_cali_tag =
                  rank_result_->ad_rank_trans_info().universe_video_pay_rate_cali_tag();
  if (universe_video_pay_rate_cali_tag > 0) {
    online_join_pb_->set_universe_video_pay_rate_cali_tag(universe_video_pay_rate_cali_tag);
  }

  if (session_data_->get_universe_live_reserve_uplift_exp_author_id() > 0) {
    online_join_pb_->set_universe_live_reserve_uplift_exp_author_id(
        session_data_->get_universe_live_reserve_uplift_exp_author_id());
  }

  int64_t unit_ad_counter_item_impression =
                  rank_result_->ad_rank_trans_info().unit_ad_counter_item_impression();
  online_join_pb_->set_unit_ad_counter_item_impression(unit_ad_counter_item_impression);
  // 腾快通媒体信息落表
  if (session_data_->get_ud_universe_imp_info().size() > 0) {
    const auto& outpos_param = session_data_->get_ud_universe_imp_info()[0].outpos_param;
    online_join_pb_->set_site_set_id(outpos_param.site_set_id());
    online_join_pb_->set_third_ad_style(outpos_param.third_ad_style());
    for (int32_t creative_spec : outpos_param.third_creative_material_type()) {
      online_join_pb_->add_creative_specs(creative_spec);
    }
  }
  online_join_pb_->set_univ_quality_flow_type(
    static_cast<int32>(session_data_->get_universe_quality_data_flag()));
  online_join_pb_->set_universe_predict_cpm_threshold(session_data_->get_universe_predict_cpm_threshold());
  online_join_pb_->set_traffic_marking(session_data_->get_traffic_marking());
  if (session_data_->get_enable_fill_cache_origin_llsid()) {
    online_join_pb_->set_cache_origin_llsid(session_data_->get_cache_origin_llsid());
  }
  if (session_data_->get_universe_traffic_control_context()->enable_fill_universe_use_physical_pos &&
      session_data_->get_is_universe_cached_res()) {  // 目前仅命中缓存时落表
    online_join_pb_->set_use_physical_pos(
        session_data_->get_universe_traffic_control_context()->UsePhysicalPos());
  }
  for (const auto item : session_data_->get_universe_traffic_types()) {
    online_join_pb_->add_universe_traffic_types(item);
  }
  online_join_pb_->set_antispam_code(session_data_->get_ud_origin_antispam_code());
  const base::Json ext_json(base::StringToJson(session_data_->copy_antispam_ext()));
  int32_t antispam_score = -1;
  if (ext_json.IsObject() && ext_json.get()) {
    antispam_score = ext_json.GetInt("score", -1);
  }
  online_join_pb_->set_antispam_score(antispam_score);
  online_join_pb_->set_antispam_ext(session_data_->copy_antispam_ext());

  for (auto id : session_data_->UniverseRequest().universe_attach_info().crowd_package_ids()) {
    online_join_pb_->add_crowd_package_ids(id);
  }
  online_join_pb_->set_universe_antispam_from_api(session_data_->get_universe_antispam_from_api());
  online_join_pb_->set_screen_direction(
                            session_data_->UniverseRequest().device_info().screen_direction());
  const auto& universe_inner_admit_info =
              session_data_->get_ad_request()->ad_user_info().universe_inner_admit_info();
  online_join_pb_->set_is_universe_inner_ks_admit_pv(
                      universe_inner_admit_info.is_allow_for_merhcant_reco());
  online_join_pb_->set_is_universe_inner_wx_admit_pv(
                      universe_inner_admit_info.is_allow_non_fake_user_jump_wx());
  auto universe_adn_profit = online_join_pb_->mutable_universe_adn_profit_info();
  universe_adn_profit->set_is_hit(session_data_->get_universe_adn_profit_info().is_hit);
  universe_adn_profit->set_is_write(session_data_->get_universe_adn_profit_info().is_write);
  universe_adn_profit->set_is_read(session_data_->get_universe_adn_profit_info().is_read);
  universe_adn_profit->set_origin_cpm(session_data_->get_universe_adn_profit_info().origin_cpm);
  universe_adn_profit->set_aim_cpm(session_data_->get_universe_adn_profit_info().aim_cpm);
  universe_adn_profit->set_origin_win_rate(session_data_->get_universe_adn_profit_info().origin_win_rate);
  universe_adn_profit->set_aim_win_rate(session_data_->get_universe_adn_profit_info().aim_win_rate);
  universe_adn_profit->set_fail_number(session_data_->get_universe_adn_profit_info().fail_number);
  universe_adn_profit->set_roi(session_data_->get_universe_adn_profit_info().roi);

  // caid
  online_join_pb_->set_current_caid(session_data_->UniverseRequest().device_info().current_caid());
  online_join_pb_->set_last_caid(session_data_->UniverseRequest().device_info().last_caid());
  online_join_pb_->set_paid(session_data_->UniverseRequest().device_info().paid());

  // 这里是将归一化以后的 search_query 落表
  online_join_pb_->set_search_query(session_data_->copy_ud_search_query());
  // 这里是将没有归一化的 search_query 落表
  online_join_pb_->set_origin_search_query(
      session_data_->get_front_server_request()->universe_request().query());

  online_join_pb_->set_app_distribute_type(session_data_->copy_ud_app_distribute_type());
  online_join_pb_->set_request_universe_tiny(session_data_->get_ud_request_universe_tiny());
  online_join_pb_->set_universe_adx_fake_second_price_ratio(
    session_data_->get_ud_universe_adx_fake_second_price_ratio());

  online_join_pb_->set_pdd_second_price_param(session_data_->copy_ud_pdd_second_price_param());
  online_join_pb_->set_universe_skip_filtered_by_cpm_thres_type(
      session_data_->get_universe_skip_filtered_by_cpm_thres_type());
  auto& universe_ext_data = session_data_->get_ad_response()->global_ext_data().universe_ext_data();
  online_join_pb_->set_universe_material_fill_ratio(universe_ext_data.material_fill_ratio());
  online_join_pb_->set_universe_derivative_material_fill_ratio(universe_ext_data.derivative_material_fill_ratio());  // NOLINT
  online_join_pb_->set_front_start_time_stamp(session_data_->get_start_ts());
  online_join_pb_->set_front_end_time_stamp(base::GetTimestamp());
  online_join_pb_->set_timeout(session_data_->UniverseRequest().timeout());
  online_join_pb_->set_tmax(session_data_->UniverseRequest().tmax());
  if (session_data_->get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    online_join_pb_->set_is_universe_preview_ad(true);
  }
  if (style_info_->has_creative() &&
      style_info_->creative().app_grade() == 3 &&
      result_->ad_source_type() != kuaishou::ad::ADX) {
    // 暗投判断
    bool is_universe_dark = true;
    base::Json resource_json(StringToJson(style_info_->unit().resource_ids()));
    if (resource_json.IsArray()) {
      for (auto* item : resource_json.array()) {
        if (item) {
          int64_t id = item->IntValue(-1);
          if (id == 5 || id == 10) {
            is_universe_dark = false;
            break;
          }
        }
      }
    }
    online_join_pb_->set_universe_dark_c_ad(is_universe_dark);
    // 暗投 C 类创意跳过暗投控比标识
    if (SPDM_enable_dark_c_skip_dark_control(session_data_->get_spdm_ctx()) && is_universe_dark &&
        FrontKconfUtil::universeDarkCSkipDarkControlConf()->count(
          style_info_->account().product_name()) > 0) {
      online_join_pb_->set_c_ad_skip_dark_control(true);
    }
  }
  // 样式聚合非首位广告标识
  if (session_data_->get_ud_carousel_admit() && result_
      && !result_->ad_deliver_info().online_join_params_transparent().is_universe_carousel_first_ad()) {  // NOLINT
    online_join_pb_->set_universe_carousel_non_first_ad(true);
  }
  if (session_data_->get_ud_universe_imp_info().size() > 0) {
    online_join_pb_->set_quick_tag(session_data_->get_ud_universe_imp_info()[0].quick_tag);
  }
  online_join_pb_->set_is_fast_app(result_->ad_deliver_info().ad_base_info().is_fast_app());
}

void OnlineJoinPack::SetBehaviorIntent() {
  // set behavior intent
  int64_t hit_behavior_intent = 0;
  const auto& style_target = style_info_->target();
  if (session_data_->get_ad_request()->ad_user_info().special_field().behavior_interest_changed()) {
    hit_behavior_intent |= 1;
  }
  if (style_target.extend_fields().behavior_interest_keyword_size() > 0) {
    hit_behavior_intent |= 1 << 1;
  }
  const auto& target_tag = result_->ad_deliver_info().target_hit_tag();
  if (target_tag.behavior_interest_size() > 0) {
    hit_behavior_intent |= 1 << 2;
  }
  online_join_pb_->set_hit_behavior_intent(hit_behavior_intent);
}

void OnlineJoinPack::SetCorePopulation() {
  const auto &user_info = session_data_->get_ad_request()->ad_user_info();
  const auto &author_list = user_info.core_population_label();
  // 广告主快手 id
  auto author_id = GetAuthorID(session_data_, *style_info_);
  for (auto aid : author_list) {
    if (aid == author_id) {
      // 实际命中高质量涨粉
      online_join_pb_->set_core_population_label(style_info_->target().core_population_label());
      online_join_pb_->set_core_population_category_ids(style_info_->target().core_population_category_ids());
      break;
    }
  }
  // 空值检查
  if (online_join_pb_->core_population_category_ids().empty()) {
    online_join_pb_->set_core_population_category_ids("[]");
  }
  if (online_join_pb_->core_population_label().empty()) {
    online_join_pb_->set_core_population_label("[]");
  }
  int user_tag = -1;
  const std::string &sensitive_user_redis_result =
    session_data_->get_ad_request()->diversity_sensitive_user_info();
  if (sensitive_user_redis_result.size() > 0) {
    base::Json imp_json(base::StringToJson(sensitive_user_redis_result));
    if (imp_json.IsObject()) {
      user_tag = imp_json.GetInt("is_dpp_crowd", -1);
    }
  }
  if (user_tag > 0) {
    online_join_pb_->set_diversity_user_tag(user_tag);
  }
}

void OnlineJoinPack::SetMerchantInfo() {
  auto campaign_type = style_info_->campaign().type();
  using kuaishou::ad::AdEnum;
  if (campaign_type != AdEnum::TAOBAO
      && campaign_type != AdEnum::LIVE_STREAM_PROMOTE
      && campaign_type != AdEnum::MERCHANT_RECO_PROMOTE
      && campaign_type != AdEnum::AD_POP_RECRUIT_LIVE) {
    return;
  }
  if (style_info_->has_unit_small_shop_merchant_support_info()) {
    online_join_pb_->set_merchant_spu_id(style_info_->unit_small_shop_merchant_support_info().spu_id());
    online_join_pb_
        ->set_creative_build_type(style_info_->unit_small_shop_merchant_support_info().creative_build_type());  // NOLINT
    // 直营电商（金牛）下发 pay_type 付款方式字段
    online_join_pb_->set_ecom_product_pay_type(
        style_info_->unit_small_shop_merchant_support_info().pay_type());
    online_join_pb_->set_product_label_type(
        style_info_->unit_small_shop_merchant_support_info().product_label_type());
    online_join_pb_->set_product_label_end_time(
        style_info_->unit_small_shop_merchant_support_info().product_label_end_time());
  }

  if (style_info_->has_small_shop_spu()) {
    online_join_pb_->set_merchant_index_time(style_info_->small_shop_spu().create_time());
  }
  if (style_info_->has_live_stream_user_info()) {
    online_join_pb_->set_account_is_live(style_info_->live_stream_user_info().is_live());
  }
  if (style_info_->has_industry_v3()) {
    online_join_pb_->set_first_industry_id_v3(style_info_->industry_v3().parent_id());
  }
  const auto& ad_user_info_ = session_data_->get_ad_request()->ad_user_info();
  if (ad_user_info_.has_hist_gmv()) {
    online_join_pb_->set_hist_gmv(ad_user_info_.hist_gmv());
  }
}

void OnlineJoinPack::SetPecCouponInfo() {
  auto campaign_type = result_->ad_deliver_info().ad_base_info().campaign_type();
  auto ocpc_action_type = result_->ad_deliver_info().ad_base_info().ocpc_action_type();
  if (session_data_->get_is_closure_flow() &&
      session_data_->is_closure_ad(campaign_type, ocpc_action_type)) {
    return;
  }
  const kuaishou::ad::AdDataV2* ad_data_v2 = session_data_->get_curr_ad_data_v2();
  const auto& origin_style_info = ad_data_v2->origin_style_info();
  auto& coupon_info = origin_style_info.merchant_info().coupon_info();
  online_join_pb_->set_coupon_template_id(coupon_info.coupon_template_id());
  online_join_pb_->set_coupon_threshold(coupon_info.coupon_rule().threshold());
  online_join_pb_->set_discount_amount(coupon_info.coupon_rule().discount_amount());
  online_join_pb_->set_coupon_type(coupon_info.coupon_type());
  online_join_pb_->set_reduce_amount(coupon_info.coupon_rule().reduce_amount());
  online_join_pb_->set_capped_amount(coupon_info.coupon_rule().capped_amount());
  const auto& ad_base_info = result_->ad_deliver_info().ad_base_info();
  auto author_id = ad_base_info.author_id();
  const auto& ad_rank_trans_info = rank_result_->ad_rank_trans_info();
  const auto& qpon_info = ad_rank_trans_info.qpon_info();
  if (SPDM_enableUniverseQcpxPerf()) {
    if (qpon_info.has_qpon()) {
      session_data_->dot_perf->Interval(coupon_info.coupon_rule().discount_amount(), "qcpx", "coupon_info",
          "discount_amount", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Interval(coupon_info.coupon_rule().threshold(), "qcpx", "coupon_info",
          "coupon_threshold", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Interval(coupon_info.coupon_rule().reduce_amount(), "qcpx", "coupon_info",
          "reduce_amount", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Interval(coupon_info.coupon_rule().capped_amount(), "qcpx", "coupon_info",
          "capped_amount", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Count(1, "qcpx", "coupon_type", absl::StrCat(coupon_info.coupon_type()),
          kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Interval(qpon_info.uplift_cvr_ratio() * 1e6, "qcpx", "qpon_info",
          "uplift_cvr_ratio", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Interval(qpon_info.uplift_ctr_ratio() * 1e6, "qcpx", "qpon_info",
          "uplift_ctr_ratio", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Interval(qpon_info.uplift_ratio() * 1e6, "qcpx", "qpon_info",
          "uplift_ratio", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Interval(qpon_info.bid_qpon_ratio() * 1e6, "qcpx", "qpon_info",
          "bid_qpon_ratio", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Interval(qpon_info.uplift_cvr_ratio() * qpon_info.uplift_ctr_ratio() *
          qpon_info.bid_qpon_ratio() * 1e9, "qcpx", "qpon_info",
          "uplift_cpm_ratio", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Count(1, "qcpx", "universe_inner_qcpx_cause",
          absl::StrCat(ad_rank_trans_info.universe_inner_qcpx_cause()),
          kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Count(1, "qcpx", "qpon_type",
          kuaishou::ad::AdEnum_QponType_Name(qpon_info.qpon_type()),
          kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
          kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
    }
    if (session_data_->get_ad_rank_pass_through().universe_inner_qcpx_pv_filter_reason() ==
          kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_QCPX_UNKNOWN_TYPE_PV &&
          ad_rank_trans_info.universe_inner_qcpx_filter_reason() ==
          kuaishou::ad::AdEnum::UniverseInnerQcpxAdFilterReason_Name(
            kuaishou::ad::AdEnum_UniverseInnerQcpxAdFilterReason_QCPX_UNKNOWN_TYPE_AD)) {
      session_data_->dot_perf->Interval(1, "qcpx", "coupon_request_number",
        kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
        kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Count(1, "qcpx", "coupon_request_number_count",
        kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
        kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
    }
    if ((ad_base_info.campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE
        || ad_base_info.campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE)
        && (style_info_->unit().ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED
        || style_info_->unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS
        || style_info_->unit().ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS
        || style_info_->unit().ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI)) {
      session_data_->dot_perf->Count(1, "qcpx", "ad_filter_reason",
        ad_rank_trans_info.universe_inner_qcpx_filter_reason(),
        kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
        kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Interval(0, "qcpx", "coupon_request_number",
        kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
        kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
      session_data_->dot_perf->Count(1, "qcpx", "inner_deep_request_number_count",
        kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()),
        kuaishou::ad::AdActionType_Name(style_info_->unit().ocpx_action_type()));
    }
  }
  if (FrontKconfUtil::pecMultiCouponConfig()->data().IsAuthorHit(author_id) > 0) {
    online_join_pb_->set_is_pec_coupon_white_author(true);
    session_data_->dot_perf->Count(1, "pec_coupon.hit_author_count");
  } else {
    online_join_pb_->set_is_pec_coupon_white_author(false);
  }

  if (FrontKconfUtil::livePecMultiCouponConfig()->data().IsAuthorHit(author_id) > 0) {
    online_join_pb_->set_is_live_pec_coupon_white_author(true);
    session_data_->dot_perf->Count(1, "pec_coupon.live_hit_author_count");
  } else {
    online_join_pb_->set_is_live_pec_coupon_white_author(false);
  }

  if (FrontKconfUtil::p2lPecMultiCouponConfig()->data().IsAuthorHit(author_id) > 0) {
    online_join_pb_->set_is_p2l_pec_coupon_white_author(true);
    session_data_->dot_perf->Count(1, "pec_coupon.p2l_hit_author_count");
  } else {
    online_join_pb_->set_is_p2l_pec_coupon_white_author(false);
  }
}

void OnlineJoinPack::SetSmartComputeScore() {
  auto iter = FrontKconfUtil::dcaf_biz_sub_page_id()->find(absl::StrCat(session_data_->get_sub_page_id()));
  if (iter != FrontKconfUtil::dcaf_biz_sub_page_id()->end()) {
    ModelRespScore scores_struct;
    auto* instance = ks::front_server::SmartComputePowerInnerUtil::GetInstance();
    if (instance->GetRouterScore(session_data_->get_llsid(), &scores_struct)) {
      auto fill_score = [](const std::vector<double>& scores_list,
                           ::google::protobuf::RepeatedField<double>* dst_field) {
        if (dst_field != nullptr) {
          for (const auto& score : scores_list) { dst_field->Add(score); }
        }
      };
      fill_score(scores_struct.unify_scores, online_join_pb_->mutable_dcaf_rank_unify_cmd());
      fill_score(scores_struct.outer_hard_scores, online_join_pb_->mutable_dcaf_rank_outer_hard_cmd());
      fill_score(scores_struct.inner_soft_photo_scores,
                  online_join_pb_->mutable_dcaf_rank_inner_soft_photo_cmd());
      fill_score(scores_struct.inner_hard_photo_scores,
                  online_join_pb_->mutable_dcaf_rank_inner_hard_photo_cmd());
    }

    auto print_repeated = [] (const ::google::protobuf::RepeatedField< double >& array) {
      std::stringstream ss;
      ss << "[";
      for (const auto& item : array) {
        ss << item << " ";
      }
      ss << "]";
      return ss.str();
    };
    LOG_EVERY_N(INFO, 10000) << "zt-test-online join smart compute score. "
                             << " llsid: " << session_data_->get_llsid()
                             << ", unify: " << print_repeated(online_join_pb_->dcaf_rank_unify_cmd())
                             << ", outer_hard: "
                             << print_repeated(online_join_pb_->dcaf_rank_outer_hard_cmd())
                             << ", inner_soft_photo: "
                             << print_repeated(online_join_pb_->dcaf_rank_inner_soft_photo_cmd())
                             << ", inner_hard_photo: "
                             << print_repeated(online_join_pb_->dcaf_rank_inner_hard_photo_cmd());
  }
}

void OnlineJoinPack::SetDynamicShareExpId() {
  if (FrontKconfUtil::enableSkipPidUniverseDynamicShare()) {
    auto& dynamic_share_exp_id =
      SPDM_universe_dynamic_share_exp_id(session_data_->get_spdm_ctx());
      online_join_pb_->set_dynamic_share_exp_id(dynamic_share_exp_id);
    session_data_->dot_perf->Count(1, "online_join.set_dynamic_share_exp_id", dynamic_share_exp_id);
  }
}

}  // namespace front_server
}  // namespace ks
