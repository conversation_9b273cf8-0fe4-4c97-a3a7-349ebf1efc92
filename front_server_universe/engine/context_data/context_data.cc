#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include <sys/types.h>

#include <algorithm>
#include <stdexcept>

#include "absl/strings/str_cat.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/encoding/base64.h"
#include "base/hash_function/md5.h"
#include "base/time/timestamp.h"
#include "falcon/counter.h"
#include "gflags/gflags_declare.h"
#include "google/protobuf/repeated_field.h"
#include "infra/location/src/location/region.h"
#include "kenv/context.h"
#include "ks/base/abtest/abtest_globals.h"
#include "ks/base/abtest/abtest_instance.h"
#include "ks/base/abtest/session_context_factory.h"
#include "ks/base/abtest/single_file_dynamic_config.h"
#include "ks/serving_util/dynamic_config.h"
#include "ks/util/json.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "serving_base/region/region_dict.h"
#include "serving_base/utility/system_util.h"
#include "teams/ad/ad_base/src/admit/admit.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_base/src/common/enum.h"
#include "teams/ad/ad_base/src/common/multi_retrieval_tag.h"
#include "teams/ad/ad_base/src/common/utility.h"
#include "teams/ad/ad_base/src/kconf_flags/kconf_udf.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/log_record/ad_pv_record_util.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/util/check_reco_request_type.h"
#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/engine_base/search/util/string/query_string_normalize.h"
#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_pack_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ssp/ad_ssp.pb.h"
#include "teams/ad/ad_proto/kuaishou/newsmodel/reco_user_info.pb.h"
#include "teams/ad/ad_proto/kuaishou/search/search_realtime_action.pb.h"
#include "teams/ad/engine_base/cache_loader/support_project_p2p_cache.h"
#include "teams/ad/engine_base/cache_loader/support_project_ratio_limit.h"
#include "teams/ad/engine_base/fanstop_common/util.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/kconf/special_regions.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/engine_base/rta_retrieval/rta_kconf_proxy.h"
#include "teams/ad/engine_base/spdm/spdm_switches.h"
#include "teams/ad/engine_base/universe/rank/utils/universe_media_cpm_bound_realtime.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/engine_base/search/util/card_style/card_style_utiils.h"
#include "teams/ad/engine_base/flow_control/flow_checker.h"
#include "teams/ad/bid_server/bid_server_universe/proto/bid_message.pb.h"
#include "teams/ad/bid_server/bid_universe_adx/proto/bid_message.pb.h"
#include "teams/ad/front_server_universe/bg_task/universe_elastic_control/universe_agg_pos_cluster.h"
#include "teams/ad/front_server_universe/bg_task/universe_elastic_control/universe_agg_pos_cluster_v2.h"
#include "teams/ad/front_server_universe/engine/context_data/utility.h"
#include "teams/ad/front_server_universe/engine/utils/data_adapter/user_info_adapter.h"
#include "teams/ad/front_server_universe/trace/ad_front_simplify_always_log.h"
#include "teams/ad/front_server_universe/engine/strategy/retrieval_rta_ads.h"
#include "teams/ad/front_server_universe/util/ad_user_info/ad_user_info_builder.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/kconf/kconf_data.pb.h"
#include "teams/ad/front_server_universe/util/request_merger/dsp_request_merger.h"
#include "teams/ad/front_server_universe/engine/strategy/strategy_manager.h"
#include "teams/ad/ad_base/src/common/scoped_guard.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/universe_rank_cache/universe_top_pos_stat.h"
#include "teams/ad/front_server_universe/util/utility/ksn_util.h"
#include "teams/ad/front_server_universe/util/utility/redis_util.h"
#include "teams/ad/front_server_universe/util/utility/strategy_util.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
#include "base/common/map_util-inl.h"
#include "teams/ad/front_server_universe/engine/node/forward_handler_common.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adx/track/adx_track.pb.h"
#include "teams/ad/front_server_universe/util/ad_pos_manager/universe_pos_resource_v2.h"
#include "teams/ad/front_server_universe/processor/process_prepare.h"
#include "teams/ad/front_server_universe/processor/process_prepare_init.h"

DECLARE_int32(ksp_group_deploy_type);
DEFINE_bool(open_chain_log, false, "open chain log");
DEFINE_UDF_KCONF(ks::front_server::kconf::ComboPressConfig, "ad.engine.comboPressConfig", comboPressConfig);

namespace ks {
namespace front_server {
// DEFINE_bool(open_chain_log, false, "open chain log");
static const auto kLocalTimeZoneCd = absl::LocalTimeZone();
#define _Attr_(v) #v
const char * ContextData::attr_names_[CommonIdx::MAX_ATTR_NUM] = { ALL_COMMON_ATTRS };
#undef _Attr_

using ks::kess::rpc::grpc::Options;

ContextData::ContextData() {
  Clear();  // 也是无语了, 必须先 Clear 才能用, 回头再定位是哪个变量的问题
}

void ContextData::UniverseAbtestInitialize() {
  set_abtest_user_id(get_ad_request()->ad_user_info().id());
  set_abtest_device_id(get_ad_request()->ad_user_info().device_id());
  set_user_id(get_abtest_user_id());
  set_device_id(copy_abtest_device_id());
  set_app_id(copy_ud_app_id());
  mutable_spdm_ctx()->Reset(get_llsid(), get_user_id(), copy_device_id(), ks::AbtestBiz::AD_DSP,
      get_abtest_mapping_id());
  ab_user_info = ks::AbtestUserInfo::Builder(get_user_id(), copy_device_id(), get_llsid())
                .SetMappingId(get_abtest_mapping_id()).Build();
}

bool ContextData::EnableSimplifyAlwaysLog() const {
  return true;
}

int64_t ContextData::GetAdWhiteCreativeID() {
  // 信息流获取用户预览 cid，平迁自 InitWhiteCreativeList
  set_is_new_preview_logic(true);
  if (GetUserPreviewAd()) {
    return get_user_preview_info().creative_id();
  }
  return -1;
}

bool ContextData::GetUserPreviewAd() {
  // 信息流获取用户预览 cid
  auto *redis_client = ks::ad_base::KconfRedis::Instance().GetAdRedisClient(
      AD_USER_EXPERIENCE, ad_base::DependDataLevel::WEAK_DEPEND);
  if (redis_client == nullptr) {
    return false;
  }
  std::string value{};
  std::string key;
  key = absl::Substitute("$0_$1", "universe_creative_preview", get_user_id());
  auto code = redis_client->Get(key, &value);
  if (code == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR &&
      mutable_user_preview_info()->ParseFromString(value)) {
    if (get_user_preview_info().creative_id() > 0 &&
        get_user_preview_info().unit_id() > 0) {
      set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
      TLOG(INFO) << "[preview ad] get user " << get_user_id()
                << " preview info: "
                << get_user_preview_info().ShortDebugString();
      return true;
    }
    TLOG(INFO) << "[preview ad] user " << get_user_id()
               << " preview data invalid, user_preview_info: " << get_user_preview_info().ShortDebugString();
    return false;
  }
  return false;
}

bool ContextData::GetAdCreativeIdFromKconf(int64_t* unit_id, int64_t* creative_id) {
  const auto& ptr = FrontKconfUtil::tempPreviewConfig();
  const auto& user_creatives_map = ptr->data().user_creatives();
  auto iter = user_creatives_map.find(std::to_string(get_user_id()));
  if (iter != user_creatives_map.end()) {
    for (const auto& id : iter->second.creative_id()) {
      *creative_id = id;
    }
  }
  const auto& user_units_map = ptr->data().user_units();
  auto itr = user_units_map.find(std::to_string(get_user_id()));
  if (itr != user_units_map.end()) {
    for (const auto& id : itr->second.unit_id()) {
      *unit_id = id;
    }
  }
  return (*unit_id > 0 && *creative_id > 0);
}

FrontServerScene ContextData::GetUnifyScene() const {
  if (get_is_trace_api_detection()) {
    return FrontServerScene::TRACEAPI;
  }

  return FrontServerScene::UNIVERSE;
}

void ContextData::InitWhiteCreativeList() {
  // 命中冷启动第一刷不走白名单
  if (IsColdStartNoAdReq()) {
    dot_perf->Count(1, "cold_start_pass_white_list");
    TLOG(INFO) << "[preview ad]: cold start pass white list";
    return;
  }
  // 软广在这配置
  int64_t unit_id = 0;
  int64_t creative_id = 0;
  if (GetAdCreativeIdFromKconf(&unit_id, &creative_id)) {
    mutable_user_preview_info()->set_creative_id(creative_id);
    mutable_user_preview_info()->set_unit_id(unit_id);
    set_preview_type(PreviewType::kPreviewAd);
    set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
    TLOG(INFO) << "[preview ad]: kconf ad preview, creative:" << creative_id
              << " unit:" << unit_id;
  }
  {
    // 广告体验, 老板模式
    const auto& boss_preview_info = FrontKconfUtil::bossModeAdPreview()->data().user_info();
    auto iter = boss_preview_info.find(get_user_id());
    const auto current_date_str = absl::FormatTime("%4Y%2m%2d%2H", absl::Now(), kLocalTimeZoneCd);
    int64 current_date = 0;
    base::StringToInt64(current_date_str, &current_date);

    if (iter != boss_preview_info.end()) {
      auto rand = ad_base::AdRandom::GetInt(1, 100);
      TLOG(INFO) << "[boss mode preview ad] get_user_id(): " << get_user_id()
                << ", preview_info: " << iter->second.ShortDebugString() << ", rank_num: " << rand
                << ", current_date: " << current_date;
      if (current_date > iter->second.expire_time() || rand > iter->second.prob()) {
        // 过时间了, 或没命中概率，不出体验
        set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::UNKONWN_FETCHER_TYPE);
        mutable_user_preview_info()->Clear();
        dot_perf->Count(1, "ad_preview_boss_mode", "online");
        return;
      }
      set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
      set_preview_type(PreviewType::kPreviewAd);
      set_is_new_preview_logic(true);
      mutable_user_preview_info()->set_creative_id(iter->second.creative_id());
      mutable_user_preview_info()->set_unit_id(iter->second.unit_id());
      dot_perf->Count(1, "ad_preview_boss_mode", "preview");
      return;
    }
  }

  // 1.0 正式体验重构链路
  auto ad_creative_id = GetAdWhiteCreativeID();
  if (ad_creative_id != -1) {
    set_preview_creative_id(ad_creative_id);
    set_preview_type(PreviewType::kPreviewAd);
    set_ac_fetcher_type(kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE);
  }
}

bool ContextData::IsTestRequest() const {
  // 为了临时测试，可以指定某台机器放开测试可写操作
  if (FrontKconfUtil::enableOpenTest()) {
    return false;
  }
  // 测试环境和压测流量先都当成测试流量，不能进行数据的写操作
  return (get_for_test() || get_fake_user_type() != ::kuaishou::ad::UserFakeType::NORMAL_USER);
}

int64_t ContextData::GetBrowseAdIntervalSecs(const kuaishou::ad::AdRequest &ad_req) {
  int64_t max_timestamp_ms = 0;
  for (int i = 0; i < ad_req.ad_user_info().ad_browsed_info_size(); ++i) {
    const auto& ad_browsed_info = ad_req.ad_user_info().ad_browsed_info(i);
    const int64_t timestamp = ad_browsed_info.timestamp() / 1000;
    max_timestamp_ms = std::max(timestamp, max_timestamp_ms);
  }
  int64_t now_ms = base::GetTimestamp() / 1000;
  return (now_ms - max_timestamp_ms) / 1000;
}

void ContextData::FillAdRequestExtInfo() {
  if (!ad_request) {
    LOG_EVERY_N(INFO, 1000) << "TTTT:" << (get_ad_request() == nullptr);
    return;
  }

  auto press_config = kconf_comboPressConfig();
  if (!press_config || press_config->world().empty()) {
    return;
  }

  SingleWorldExperimentInfo info = get_spdm_ctx().Raw()->TryGetExpGroupInfo(press_config->world());
  if (press_config->exp().empty() || info.experimentId == press_config->exp()) {
    mutable_ad_request()->mutable_ext_info()->set_combo_group(info.groupId);
  }
  LOG_EVERY_N(INFO, 1000) << "TTTT: user_id" << get_ad_request()->ad_user_info().id()
                          << " combo_group:" << info.groupId << " ext_info"
                          << get_ad_request()->ext_info().ShortDebugString()
                          << " config:" << kconf_comboPressConfig()->ShortDebugString();
}

void ContextData::SetAdServiceTimeout() {
  int32_t tmax{0};
  static absl::flat_hash_set<int32_t> sdk_flow_mode_set{1, 4, 6, 8};
  if (get_sub_page_id() == 19000002) {  // 小系统厂商流量
    tmax = 10000;   // 特殊处理
  } else if (get_sub_page_id() == 19000003) {  // 低耗时流量
    tmax = UniverseRequest().timeout();
  } else if (get_sub_page_id() == 19000001 &&  // 大系统 SDK 流量 NOLINT
      SPDM_enable_relax_timeout_setting(get_spdm_ctx()) &&
      sdk_flow_mode_set.contains(get_pos_manager().GetCooperationMode())) {
    tmax = 700;   // 特殊处理
  }
  const auto& conf = FrontKconfUtil::universeTimeOutConfig()->data().GetTimeOut(tmax);
  if (conf.ad_server_timeout > 0) {
    auto* info = mutable_ad_request()->add_time_out_info();
    info->set_time_out_ms(conf.ad_server_timeout);
    info->set_ad_service_type(kuaishou::ad::TimeOutInfo_AdServiceType_AD_SERVER_TYPE);
  }
  if (conf.ad_target_timeout > 0) {
    auto* info = mutable_ad_request()->add_time_out_info();
    info->set_time_out_ms(conf.ad_target_timeout);
    info->set_ad_service_type(kuaishou::ad::TimeOutInfo_AdServiceType_AD_TARGET_TYPE);
  }
  if (conf.ad_rank_timeout > 0) {
    auto* info = mutable_ad_request()->add_time_out_info();
    info->set_time_out_ms(conf.ad_rank_timeout);
    info->set_ad_service_type(kuaishou::ad::TimeOutInfo_AdServiceType_AD_RANK_TYPE);
  }
  if (conf.ad_style_timeout > 0) {
    auto* info = mutable_ad_request()->add_time_out_info();
    info->set_time_out_ms(conf.ad_style_timeout);
    info->set_ad_service_type(kuaishou::ad::TimeOutInfo_AdServiceType_AD_STYLE_TYPE);
  }
}

void ContextData::UniverseInitializeAfterExtDeps() {
  AfterBuildUserInfo();
  AsyncRedisInit();
  for (const auto& id : get_front_server_request()->universe_request().device_info().industry_id()) {
    mutable_ad_request()->mutable_ad_user_info()->add_industry_id(id);
  }
  mutable_ad_request()->mutable_universe_debug_param()->CopyFrom(UniverseRequest().universe_debug_param());
  set_ud_is_universe_tiny_flow(get_sub_page_id() == 19000002);

  set_universe_tiny_search_black_list(std::make_shared<engine_base::UniverseTinySearchBlackList>(
      engine_base::AdKconfUtil::universeTinySearchBlackList()->data()));
  set_enable_direct_search_skip_budget_control_universe(
    SPDM_enable_direct_search_skip_budget_control_universe(get_spdm_ctx()));
  set_enable_quick_search_skip_budget_control_universe(
    SPDM_enable_quick_search_skip_budget_control_universe(get_spdm_ctx()));

  set_enable_fill_cache_origin_llsid(true);

  mutable_ad_request()->set_gid(UniverseRequest().user_info().gid());
  // 联盟先单独设置一下 sub_page_id, 后面统一再进行联盟的迁移
  mutable_ad_request()->set_sub_page_id(get_sub_page_id());
  mutable_pos_manager()->Initialize(*get_ad_request());
  mutable_pos_manager()->SetInteractiveForm(get_ad_request()->reco_request_info().browse_type(),
                                 get_ad_request()->reco_request_info().source(),
                                 kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNKNOWN);
  int32_t cooperation_mode = get_pos_manager().GetCooperationMode();
  static absl::flat_hash_set<int32_t> bidding_coop_mode_set{4, 5, 6, 7, 8, 9};
  static absl::flat_hash_set<int32_t> adn_coop_mode_set{1, 2, 4, 5};
  set_is_universe_rtb_flow(bidding_coop_mode_set.contains(cooperation_mode));
  set_is_universe_adn_flow(adn_coop_mode_set.contains(cooperation_mode));
  // 下游服务超时时间特殊设置
  SetAdServiceTimeout();

  set_enable_universe_fix_elastic_idx(true);

  const auto& request_imp_infos = get_pos_manager().request_imp_infos;
  if (request_imp_infos.size() > 0) {
    set_ud_pos_id(request_imp_infos[0].pos_id);
    set_medium_industry_id_v2(get_pos_manager().GetMediumIndustryIdV2());
    set_medium_sub_industry_id_v2(get_pos_manager().GetMediumSubIndustryIdV2());
  } else {  // 过滤掉
    set_pass_pre_filter(false);
    dot_perf->Count(1, "universe_filter_by_no_imp");
  }

  // 标记头层流量
  if (get_is_universe_adn_flow()) {
    int64_t tmp_top_pos_id = get_ud_top_pos_id();
    int64_t tmp_top_pos_cpm_floor = get_ud_top_pos_cpm_floor();
    UniverseTopPosStat::Instance()->UpdateAndGet(get_ud_physical_pos_id(), get_ud_pos_id(),
                                                 get_ud_position_cpm_floor(), &tmp_top_pos_id,
                                                 &tmp_top_pos_cpm_floor);
    set_ud_top_pos_id(tmp_top_pos_id);
    set_ud_top_pos_cpm_floor(tmp_top_pos_cpm_floor);
    bool is_top_pos = (get_ud_top_pos_id() == get_ud_pos_id());
    set_ud_is_top_pos(is_top_pos);
    dot_perf->Count(1, "ad_front.top_pos_count",
                    get_ud_is_top_pos() ? "top_pos" : "non_top_pos",
                    copy_ud_cache_abtest_key());
  }

  // 生成 cache key
  {
    auto device_id_t = UniverseRequest().device_info().device_id();
    if (SPDM_enable_universe_use_new_hash_did(get_spdm_ctx())) {
      device_id_t = copy_device_id();
    }

    auto pos_id = get_ud_pos_id();
    if (get_ud_physical_pos_id() > 0) {
      pos_id = get_ud_physical_pos_id();
    }
    if (get_universe_traffic_control_context()->is_use_new_cache_key) {
      auto user_id_str_bottom = get_ad_request()->ad_user_info().id() > 0
                                    ? absl::StrCat(get_ad_request()->ad_user_info().id())
                                    : get_universe_traffic_control_context()->device_id;  // device_id 兜底
      auto pos_id_bottom = get_ud_pos_id();
      auto physical_pos_id_bottom = get_ud_physical_pos_id() > 0 ? get_ud_physical_pos_id() : get_ud_pos_id();
      std::string tmp_ud_cache_key = copy_ud_cache_key();
      UniverseTrafficControl::Instance().FillRankCacheKey(
          __func__, get_universe_traffic_control_context()->device_id, user_id_str_bottom,
          get_pos_manager().GetCooperationMode(), physical_pos_id_bottom, pos_id_bottom,
          get_universe_traffic_control_context()->new_cache_key_eles, tmp_ud_cache_key);
      set_ud_cache_key(tmp_ud_cache_key);
    } else {
      if (get_ad_request()->ad_user_info().id() != 0) {
        set_ud_cache_key(absl::Substitute("$0_$1", pos_id, get_ad_request()->ad_user_info().id()));
      } else if (!device_id_t.empty()) {
        set_ud_cache_key(absl::Substitute("$0_$1", pos_id, device_id_t));
      }
    }

    // 精排 Cache 前置做相关标记
    UniverseTrafficControl::Instance().RankCacheMark(get_ad_request(), mutable_universe_traffic_control_context(),  // NOLINT
                                                     get_ad_request()->ad_user_info().id(),
                                                     device_id_t,
                                                     get_ud_pos_id(),
                                                     pos_id, get_pos_manager().GetCooperationMode());
  }

  {
    MissDimension miss_dimension;
    miss_dimension.cooperation_mode = cooperation_mode;
    miss_dimension.industry_id = mutable_ad_request()->mutable_universe_ad_request_info()->medium_industry_id_v2();  // NOLINT
    UniverseTrafficControl::Instance().RankCacheMarkByMissDimension(get_ud_pos_id(), miss_dimension);
  }

  set_perf_ab_param_bool(
    get_spdm_ctx().TryGetBoolean(*(engine_base::AdKconfUtil::perfAbParam()), false));

  //  频控数据初始化
  SetInventoryPosInfo(*get_ad_request());
  // front_service VisitGraph 依赖于此, 不能迁移
  InitWhiteCreativeList();
  if (get_for_test()) {  //测试环境填充部分用户画像
    auto imei = get_front_server_request()->universe_request().device_info().imei();
    auto oaid = get_front_server_request()->universe_request().device_info().oaid();
    auto idfa = get_front_server_request()->universe_request().device_info().idfa();
    if (!imei.empty() && FrontKconfUtil::testWhiteListUniverse()->IsTestStyle(imei)) {
      set_test_device_id(imei);
    } else if (!oaid.empty() && FrontKconfUtil::testWhiteListUniverse()->IsTestStyle(oaid)) {
      set_test_device_id(oaid);
    } else if (!idfa.empty() && FrontKconfUtil::testWhiteListUniverse()->IsTestStyle(idfa)) {
      set_test_device_id(idfa);
    }
    LOG(INFO) << "Universe test device id: " << borrow_test_device_id();
  }
  set_medium_valid_err_code(MediumParameterCheck());

  // 人群包策略打点
  {
    for (const auto& tag_info : get_ad_request()->ad_user_info().universe_tag_info()) {
      if (tag_info.id() == 3) {
        const auto &arr_longs = tag_info.arr_long();
        for (const auto& id_long : arr_longs) {
          mutable_ud_crowd_package_id_set()->emplace(id_long);
        }
      } else {
        mutable_ud_crowd_package_id_set()->emplace(tag_info.id());
      }
    }
    CrowdStrategyDot("universe_front_before_admit");
  }

  bool is_device_id_equal =
      UniverseRequest().universe_attach_info().ad_device_id() == get_ad_request()->ad_user_info().device_id();
  dot_perf->Count(1, "universe_api_device_id_diff", is_device_id_equal ? "equal" : "diff");
  if (!is_device_id_equal) {
    LOG_EVERY_N(INFO, 1000) << "llsid: " << UniverseRequest().llsid()
        << ", user_info get_device_id(): " << get_ad_request()->ad_user_info().device_id()
        << ", api_device_id: " << UniverseRequest().universe_attach_info().ad_device_id();
  }
  set_universe_antispam_from_api(UniverseRequest().universe_attach_info().is_antispam_check());
  if (get_universe_antispam_from_api()) {
    set_antispam_code(UniverseRequest().universe_attach_info().antispam_code());
    set_antispam_ext(UniverseRequest().universe_attach_info().antispam_ext());
  }
  // 从 kconf 中获取兜底阈值
  auto hour = absl::Now().In(absl::LocalTimeZone()).hour;
  const auto& cpm_by_time_map = FrontKconfUtil::cpmByTime();
  auto itrt = cpm_by_time_map->find(hour);
  if (itrt == cpm_by_time_map->end()) {
    set_universe_predict_cpm_threshold(0.05);
  } else {
    set_universe_predict_cpm_threshold(itrt->second);
  }
  set_universe_predict_cpm(-1);
  set_universe_predict_cpm_for_grade_quality(-1);
  set_universe_predict_cpm_for_traffic_type(-1);
  set_universe_enable_log_predict_cpm(false);
  set_universe_log_cpr_ab_group(SPDM_dynamic_quality_score_threshold_group_actuality(get_spdm_ctx()));
  set_universe_pid_solver_tp_ratio(std::to_string(SPDM_universe_pid_solver_tp_ratio(get_spdm_ctx())));
  auto timeout = UniverseRequest().timeout();
  if (timeout == 0) {
    timeout = 480;
  }
  set_universe_timeout(timeout);
  std::string default_ratio_level =
    "10:1000000,13:1000001,17:1000002,24:1000003,35:1000004,60:1000005,90:1000006,100:1000007";
  auto ratio_level_string = get_spdm_ctx().TryGetString(
      *ks::engine_base::AdKconfUtil::universeDynamicTrafficGradeLevelRatioExpTag(), default_ratio_level);
  set_dynamic_traffic_grade_level_ratio(ratio_level_string);

  FillAdRequestExtInfo();
  // 组件化全链路监控相关字段初始化
  int32_t ad_style = 0;
  int32_t ad_rollout_size = 0;
  if (get_ad_request()->universe_ad_request_info().imp_info_size() > 0) {
    const auto& imp_info = get_ad_request()->universe_ad_request_info().imp_info(0);
    ad_style = imp_info.ad_style();
    ad_rollout_size = imp_info.ad_rollout_size();
  }
  set_ud_ad_rollout_size(ad_rollout_size);
  std::string ab_param_name;
  if (ad_style == 4) {
    ab_param_name = "switch_to_componentized_style_splash";
  } else if (ad_style == 13 || ad_style == 23 && ad_rollout_size == 2) {
    ab_param_name = "switch_to_componentized_style_interstitial";
  } else if (ad_style == 3 || ad_style == 23) {
    ab_param_name = "switch_to_componentized_style_fullscreen";
  } else {
    ab_param_name = "switch_to_componentized_style";
  }
  set_ud_meet_componentized_precondition(
      ks::front_server::UniverseData::IsMeetComponentizedPrecondition(this));
  const auto& ab_result = ks::abtest::AbtestInstance::GetResult(ks::AbtestBiz::AD_DSP,
                                                      ab_param_name, ab_user_info);
  const std::string& exp_name = ab_result.GetGroupHit().GetExperiment();
  const std::string& exp_group = ab_result.GetGroupHit().GetGroup();
  set_ud_componentized_exp_group(exp_group);
  set_ud_hit_componentized_monitor_exp_whitelist(exp_name == "Componentized_Style3"
                        && FrontKconfUtil::universeComponentizedMonitorExpWhitelist()->count(exp_group) > 0);
  // 媒体是否禁用传感器数据
  mutable_ad_request()->set_medium_disable_sensor(get_front_server_request()->universe_request().medium_disable_sensor());  // NOLINT

  // 透传给 style-server 的数据加在这
  auto* style_server_ext_info = mutable_ad_request()->mutable_ad_style_request_ext_info();
  style_server_ext_info->set_medium_disable_slide(
                          get_front_server_request()->universe_request().medium_disable_slide());
  style_server_ext_info->set_medium_disable_shake(
                          get_front_server_request()->universe_request().medium_disable_shake());
  style_server_ext_info->set_medium_disable_rotate(
                          get_front_server_request()->universe_request().medium_disable_rotate());
  const auto& athena_imp_info = get_front_server_request()->universe_request().imp_info();
  if (athena_imp_info.ad_pos_info_size() > 0) {
    style_server_ext_info->mutable_material_size()->CopyFrom(
                            athena_imp_info.ad_pos_info(0).material_size());
  }

  // 填写超时实验相关参数，便于打点查看效果
  mutable_ud_timeout_exp_info()->FillParams(this);

  // 设置给 adx 透传的数据
  mutable_ad_request()->mutable_adx_ext_info()->set_boot_mark(UniverseRequest().device_info().boot_mark());
  mutable_ad_request()->mutable_adx_ext_info()->set_update_mark(UniverseRequest().device_info().update_mark());  // NOLINT
}

void ContextData::CrowdStrategyDot(const std::string& stage) {
  auto id_white_set = FrontKconfUtil::universeCrowdPackageMonitorWhiteSet();
  dot_perf->Count(1, "universe_crowd_package_monitor", stage, "all_pv");
  for (auto crowd_id : *id_white_set) {
    if (get_ud_crowd_package_id_set().contains(crowd_id)) {
      dot_perf->Count(1, "universe_crowd_package_monitor", stage,
                      "crowd_id_hit_num", absl::StrCat(crowd_id));
    }
  }
}

void ContextData::FillDeviceId() {
  const auto& device_info = UniverseRequest().device_info();
  auto ab_user = ks::AbtestUserInfo::Builder(0, "", UniverseRequest().llsid()).Build();
  bool enable_universe_use_device_id = ks::abtest::AbtestInstance::GetBoolean(
                      ks::AbtestBiz::AD_DSP,
                      "enable_universe_use_device_id", ab_user, false);
  std::string did;
  if (enable_universe_use_device_id) {
    did = device_info.device_id();
  }
  did = ks::ad_base::GetUniverseValidDid(UniverseRequest(), enable_universe_use_device_id);
  mutable_ad_request()->mutable_ad_user_info()->set_device_id(did);

  set_device_id_hash_key(ks::ad_base::GetUniverseValidDid(UniverseRequest(), false, true));
  {  // TODO(yinliang) 推全后，把这段代码删除，并把上面的 GetUniverseValidDid 的 is_hash_key 参数置成 false。
    auto ab_user_tmp = ks::AbtestUserInfo::Builder(0, did, UniverseRequest().llsid()).Build();
    bool enable_universe_use_new_hash_did = ks::abtest::AbtestInstance::GetBoolean(
        ks::AbtestBiz::AD_DSP, "enable_universe_use_new_hash_did", ab_user_tmp, false);
    if (enable_universe_use_new_hash_did) {
      set_device_id_hash_key(did);
    }
  }
}

bool ContextData::UniversePreFilter(const kuaishou::ad::universe::AthenaAdRequest& request) {
  // 如果本次请求对应的广告位都被关闭 直接过滤掉本次 pv
  if (get_ud_universe_imp_info().empty()) {
    auto product_name = request.app_info().name();
    dot_perf->Count(1, "front_server_prefilter", "empty_universe_imp_info", "universe", product_name);
    return false;
  }
  return true;
}

// 返回 true 表示通过过滤，可以进行后续处理，false 表示未通过过滤
auto ContextData::PreFilter(const FrontServerRequest& request) -> bool {
  return UniversePreFilter(request.universe_request());
}



void ContextData::InitializeAfterExtDeps(ks::platform::AddibleRecoContextInterface* mix_context) {
  // 此时已经获取到 (1 用户画像数据 和 (2 策略正排数据
  // 异常判断
  ad_base::ScopeGuard guard([&]() {
    if (mutable_ad_request() &&
        mutable_ad_request()->universe_ad_request_info().imp_info_size() == 0) {
      if (dot_perf) {
        dot_perf->Count(1, "universe_imp_info_empty", absl::StrCat(get_context_init_error()));
      }
      LOG(INFO) << "universe_imp_info_empty, request:" << get_front_server_request()->ShortDebugString();
      set_context_init_error(true);
    }
  });

  UniverseInitializeAfterExtDeps();
  AfterUniverseInitialize(mix_context);
}

void ContextData::Initialize(
    const kuaishou::ad::FrontServerRequest& front_server_request, const std::string& peer_info,
    std::shared_ptr<UniverseTrafficControlContext>&& utcc,
    AdFrontProcessPrepareMixer* mixer,
    ks::platform::AddibleRecoContextInterface* mix_context) {
  InitializeBeforeExtDeps(front_server_request, std::move(utcc), mixer, mix_context);

  // 同步调用画像
  AdUserInfoBuilder::BuildUserInfo(*this, AD_FLOW_TYPE ::AD_UNIVERSE_FLOW,
    mutable_ad_request()->mutable_ad_user_info());

  InitializeAfterExtDeps(mix_context);
}

void ContextData::AfterUniverseInitialize(ks::platform::AddibleRecoContextInterface* mix_context) {
  using google::protobuf::Arena;
  const auto* front_req = get_front_server_request();
  if (!front_req) {
    LOG_EVERY_N(ERROR, 10000) << "front_request is nullptr";
    set_context_init_error(true);
    return;
  }
  ks::front_server::UniverseData::GetAppListV3(get_ad_request()->ad_user_info(), get_front_server_request()->universe_request(), this);  // NOLINT

  mutable_ad_select_stage_infos()->Initialize(this);

  // 广告个性化推荐状态监控
  ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "ad_personal",
                                     absl::StrCat(get_front_server_request()->type()),
                                     absl::StrCat(CheckAdPersonalLabel(*get_front_server_request())));

  set_enable_kxy_subsidy_global_nc(SPDM_enable_kxy_subsidy_global_nc(get_spdm_ctx()));

  set_iap_ocpx_set(FrontKconfUtil::ocpcActionTypeKminiGameIAP());

  set_enable_rank_pass_through_v2(false);

  // 万合改曝光计费
  set_enable_wanhe_charge_action_type_with_fanstop(
      SPDM_enable_wanhe_charge_action_type_with_fanstop(get_spdm_ctx()));
  set_enable_wanhe_charge_action_type_subpage_id(false);
  if (FrontKconfUtil::wanheChargeActionTypeSubPageId() &&
      FrontKconfUtil::wanheChargeActionTypeSubPageId()->count(get_sub_page_id()) > 0) {
    set_enable_wanhe_charge_action_type_subpage_id(true);
  }

  set_has_ad_credict_user_score_redis(false);
  set_is_multi_quota_flow(ks::ad_base::IsThanosMixRequest(get_sub_page_id())
      || ks::ad_base::IsFeedExploreRequest(get_sub_page_id())
      || ks::ad_base::IsInnerExploreRequest(get_sub_page_id()));

  bool enable_split_shelf_merchant = (ks::ad_base::IsShelfMerchantRequest(get_sub_page_id()) &&
                                      SPDM_enable_split_shelf_merchant(get_spdm_ctx())) ||
                                     (ks::ad_base::IsMallTabReuqest(get_sub_page_id()) &&
                                      SPDM_enable_split_shelf_merchant_mall(get_spdm_ctx())) ||
                                     (ks::ad_base::IsBuyerHomeRequest(get_sub_page_id()) &&
                                      SPDM_enable_split_shelf_merchant_buyer(get_spdm_ctx())) ||
                                     (ks::ad_base::IsGuessYouLikeRequest(get_sub_page_id()) &&
                                      SPDM_enable_split_shelf_merchant_guess(get_spdm_ctx()));
  common_w_->SetIntCommonAttr("enable_split_shelf_merchant", enable_split_shelf_merchant);

  SetRankMigrationSwitches();
  if (GetBoolCommonAttr(common_attr::enable_move_build_ad_response_to_front)) {
    common_w_->SetPtrCommonAttr(
      common_attr::rank_response, Arena::CreateMessage<kuaishou::ad::AdRankResponse>(mutable_arena()));
    common_w_->SetPtrCommonAttr(
      common_attr::ad_response_new, Arena::CreateMessage<AdResponse>(mutable_arena()));
    common_w_->SetPtrCommonAttr(
      common_attr::jk_response,
      Arena::CreateMessage<kuaishou::ad::adx::track::inner::TrackResponse>(mutable_arena()));
    common_w_->SetPtrCommonAttr(
      common_attr::rank_request, Arena::CreateMessage<kuaishou::ad::AdRankRequest>(mutable_arena()));
  }

  set_enable_inspire_style_form(SPDM_enable_inspire_style_form(get_spdm_ctx()));

  set_print_factor_info_random_num(ad_base::AdRandom::GetInt(0, 15));
  set_enable_print_all_factor_info(SPDM_enablePrintAllFactorInfo());

  // 临时加一个兜底吧，后面 ad_request 初始化全部迁移好了以后再统一处理 user_id
  if (get_user_id() <= 0) {
    set_user_id(get_ad_request()->ad_user_info().id());
  }
  set_is_unlogin_user(get_ad_request()->ad_user_info().is_unlogin_user());
  set_app_version(get_ad_request()->ad_user_info().platform_version());
  set_fake_user_type(get_ad_request()->fake_type());
  set_is_ios_platform(get_ad_request()->ad_user_info().platform() == "ios");
  // 在这里统一对 request 请求量进行打点
  dot_perf->Count(1, "front_request_num", "",
                  kuaishou::ad::AdEnum_AdRequestFlowType_Name(front_req->flow_type()),
                  ::kuaishou::ad::UserFakeType_Name(get_ad_request()->fake_type()), "", false);
  dot_perf->Count(
      1,
      "request_page_size_num",
      absl::StrCat(get_ad_request()->page_size()),
      kuaishou::ad::AdEnum::RefreshDirection_Name(ks::ad_base::GetRefreshDirection(*get_ad_request())),
      absl::Substitute("$0_$1_$2",
                        get_ad_request()->cold_start(),
                        get_ad_request()->first_load_more(),
                        static_cast<int64_t>(get_ad_request()->front_internal_data().cold_start_request_type())));  // NOLINT
  // 数据采样；落 kafka 数据不被模型阈值过滤 [联盟]
  auto ratio = ks::ad_base::AdRandom::GetInt(0, 100);
  // [xiaowentao] 打开新的样本流，则抽取 2 倍流量（6%）作为样本，所以样本都会过 cpm 门槛，并对其中：
  // 1/2 标记满算力，用于训练价值分模型
  // 1/2 随机分配算力，用于训练价值分 uplift 模型
  if (ratio < 2 * FrontKconfUtil::frontUniverseCpmKafkaRatio()) {
    set_enable_detail_cpm_kafka(true);
    if (ratio >= FrontKconfUtil::frontUniverseCpmKafkaRatio()) {
      set_enable_use_full_computing_power(true);
    }
  }
  auto ratio_offset = 2 * FrontKconfUtil::frontUniverseCpmKafkaRatio();
  if (SPDM_enableDetailCpmTimeoutKafka()) {
    if (ratio - ratio_offset >= 0 &&
        ratio - ratio_offset < FrontKconfUtil::DetailCpmTimeoutKafkaRatio()) {
      set_enable_detail_cpm_kafka(true);
      set_enable_use_full_computing_power(true);
      set_enable_detail_cpm_timeout_kafka(true);
    }
    ratio_offset += FrontKconfUtil::DetailCpmTimeoutKafkaRatio();
  }
  if (SPDM_enableDetailCpmUpliftKafka()) {
    if (ratio - ratio_offset >= 0 &&
        ratio - ratio_offset < FrontKconfUtil::DetailCpmUpliftKafkaRatio()) {
      set_enable_detail_cpm_kafka(true);
      set_enable_detail_cpm_uplift_kafka(true);
    }
    ratio_offset += FrontKconfUtil::DetailCpmUpliftKafkaRatio();
  }
  // 竞胜率模型样本流
  if (SPDM_enableWinRateModelKafka()) {
    if (ratio - ratio_offset >= 0 &&
        ratio - ratio_offset < FrontKconfUtil::DetailCpmWinRateKafkaRatio()) {
      set_enable_win_rate_kafka(true);
    }
  }
  //  取联盟竞胜率 ab 参数
  if (SPDM_enable_universe_rtb_win_rate_predict_v2(get_spdm_ctx())) {
    WinRateModelParamsParse();
  }
  if (!get_pos_manager().request_imp_infos.empty()) {
    std::string key;
    if (get_abtest_user_id() != 0) {
      key = absl::Substitute("$0_$1", get_ud_pos_id(), get_abtest_user_id());
    } else {
      key = absl::Substitute("$0_$1", get_ud_pos_id(), borrow_abtest_device_id());
    }
    universe_request_merge_info.universe_merge_req_key = base::CityHash64(key.data(), key.size());
  }
  if (ks::ad_base::AdRandom::GetInt(0, 100) < FrontKconfUtil::frontUnionLtrSampleRatio()) {
    set_enable_union_ltr_sample(true);
    set_union_ltr_sample_tag(1);
  }
  if (!get_enable_union_ltr_sample() &&
        ks::ad_base::AdRandom::GetInt(0, 100) < FrontKconfUtil::universeSampleIncrRatio()) {
    set_enable_union_ltr_sample(true);
    set_union_ltr_sample_tag(4);
  }
  mutable_ad_request()->set_enable_union_ltr_sample(get_enable_union_ltr_sample());
  AsyncRedisPostProc();
  ks::front_server::UniverseData::FillUniverseKsInstalledAndRegisterInfo(this);
  mutable_ad_request()->set_is_universe_aggregation_scene(get_ud_is_universe_aggregation_scene());
  // 修改广告数
  if (get_ud_is_universe_aggregation_scene() &&
      get_ad_request()->universe_ad_request_info().imp_info_size() > 0) {
    auto* imp_info = mutable_ad_request()->mutable_universe_ad_request_info()->mutable_imp_info(0);
    imp_info->set_ad_num(SPDM_universe_agg_scene_ad_num(get_spdm_ctx()));
    // 重新赋值
    mutable_pos_manager()->RefreshAdRequestTypeAndRequestImpInfos(*get_ad_request());
    dot_perf->Count(1, "front_server.universe_aggregation_scene_update_ad_num_new");
  }
  ks::front_server::UniverseData::SetUniverseCarouselInfo(
      get_spdm_ctx(), get_ad_request()->universe_ad_request_info(), this);
  // 不在这里获得先审后投白名单
  ks::front_server::UniverseData::GetUniverseWhiteTrafficInfoNew(*get_ad_request(), this);
  dot_perf->Count(1, "universe_white_creative_rule_ids_set", copy_ud_white_traffic_ids_str());
  set_is_universe_inner_loop_admit_traffic(
      get_ad_request()->ad_user_info().universe_inner_admit_info().is_allow_for_merhcant_reco() ||
      get_ad_request()->ad_user_info().universe_inner_admit_info().is_allow_for_live_stream() ||
      get_ad_request()->ad_user_info().universe_inner_admit_info().is_allow_non_fake_user_jump_wx());
  // 如果开启了 front server 准入策略，则透传 kafka 采样开关至 ad server
  mutable_ad_request()->set_enable_detail_cpm_kafka(get_enable_detail_cpm_kafka());
  mutable_ad_request()->set_enable_win_rate_kafka(get_enable_win_rate_kafka());
  // 打印一下
  dot_perf->Count(1, "request_type",
                  kuaishou::ad::AdEnum_AdRequestFlowType_Name(
                      ks::ad_base::GetAdRequestType(get_page_id(), get_sub_page_id())));
  YLOG_PB("adFrontServer", "first", get_llsid(), get_user_id(), ks::ad::ylog::LOG_TYPE::request,
          *front_req);
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_chain_log &&
      ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName().empty()) {
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceLog(
        "request", true, "adFrontServer", "", get_user_id(), get_llsid(),
        ks::ad_base::massage_to_json(*front_req));
  }

  // 投中负向信号配置
  const auto& photo_negative_kconf_config = FrontKconfUtil::outerloopAbNegFilterRateConfig();
  const auto& ab_neg_rate_config = photo_negative_kconf_config->data().ab_config();
  auto ab_neg_rate_config_iter = ab_neg_rate_config.find("exp5");
  if (ab_neg_rate_config_iter != ab_neg_rate_config.end()) {
    set_photo_negative_tags(
      std::make_shared<kconf::OuterNativeNegFilterRateConfig>(ab_neg_rate_config_iter->second));
  }
  set_outer_native_diff_author_open_neg(FrontKconfUtil::outerNativeDiffAuthorOpenNeg());
}

void ContextData::InitializeDragonContext(ks::platform::AddibleRecoContextInterface* context) {
  bool need_clear = (common_r_ == nullptr);
  common_w_ = context;
  common_r_ = context;
  lazy_init_attr_ = true;
  attrs_.fill(nullptr);
  if (!lazy_init_attr_) {
    #define __Attr  Attr
    #include "context_data-context_data.init.extra"  // NOLINT
    #undef __Attr
  }

  if (need_clear) {
    Clear();  // 首次调用时初始化一下
  }
  // 设置 diff 和 切换开关 临时变量
  change_context_shared_ptr_source_ = FrontKconfUtil::changeContextSharedPtrSource();
  change_universe_quality_data_source_ = FrontKconfUtil::changeUniverseQualityDataSource();
  // UniverseData diff 和切换开关 临时变量

  change_context_string_source_v2_ = FrontKconfUtil::changeContextStringSourceV2();
  set_for_test(ks::ad_base::AdKessClient::Instance().IsTestEnv());

  is_dragon_context_initialized_ = true;
}

void ContextData::FillUniverseUserSceneFreqV2() {
  if (!mutable_universe_user_scene_freq_params()->IsAllow()) {
    return;
  }
  std::string value_str;
  auto ret = mutable_async_redis_helper()->Wait(mutable_universe_user_scene_freq_params(), &value_str);
  LOG_EVERY_N(INFO, 5000) << "FillUniverseUserSceneFreq: here1"
                          << "value: " << value_str;
  int64_t value_int = 0;
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR && absl::SimpleAtoi(value_str, &value_int)) {
    mutable_ad_request()->mutable_ad_user_info()
                                       ->set_request_freq(static_cast<int32_t>(value_int + 1));
    LOG_EVERY_N(INFO, 5000) << "FillUniverseUserSceneFreq: here2"
                            << "value: " << value_int;
    dot_perf->Count(1, "universe_user_scene_freq", "success");
  } else {
    mutable_ad_request()->mutable_ad_user_info()->set_request_freq(1);
    dot_perf->Count(1, "universe_user_scene_freq", "fail");
  }
}

void ContextData::AsyncRedisInit() {
  mutable_universe_cpm_bound_params()->Init(this);
  mutable_async_redis_helper()->AsyncGet(mutable_universe_cpm_bound_params());
  mutable_universe_user_scene_freq_params()->Init(this);
  mutable_async_redis_helper()->AsyncGet(mutable_universe_user_scene_freq_params());
}

void ContextData::AsyncRedisPostProc() {
  FillUniverseCpmBoundInfoV2();
  FillUniverseUserSceneFreqV2();
  // 一定要放在最后!
  mutable_async_redis_helper()->WaitAll();
}

void ContextData::FillUniverseCpmBoundInfoV2() {
  auto fill_redis_info = [this] () -> bool {
    std::string value;
    auto ret = mutable_async_redis_helper()->Wait(mutable_universe_cpm_bound_params(), &value);
    if (ret == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
      dot_perf->Count(1, "universe_cpm_bound_count", "ok");
      kuaishou::ad::AdUniverseMultiPidCache pid_infos;
      if (!value.empty() && !pid_infos.ParseFromString(value)) {
        LOG(ERROR) << "AdUniverseMultiPidCache: parse failed!";
        return false;
      }

      std::string group_tag = *(engine_base::AdKconfUtil::universeMediaCpmBoundGroupTag());
      if (pid_infos.pid_cache().find(group_tag) == pid_infos.pid_cache().end()) {
        LOG_EVERY_N(ERROR, 100) << "front_servermedia_pos_cpm_bound_loader_with_grouptag_failed";
        return false;
      } else {
        auto iter = pid_infos.pid_cache().find(group_tag);
        set_ud_cpm_bound(iter->second.cpm_bound() / 1000.0);
        set_ud_target_cpm(iter->second.target_cpm() / 1000.0);
        set_ud_org_target_cpm(iter->second.org_target_cpm() / 1000.0);

        return !((get_ud_cpm_bound() <= 0.0) || (get_ud_target_cpm() <= 0.0));
      }
    } else {
      dot_perf->Count(1, "universe_cpm_bound_count", "bad", std::to_string(ret));
    }
    return false;
  };
  // 分/千次转化成元
  double pos_cpm_floor = get_ud_position_cpm_floor() / 100.0;
  if (!fill_redis_info()) {
    // 否则查询 kconf 文件中配置的固定 cpm bound 值
    auto pos_cpm_bound_map = FrontKconfUtil::universeFlowPosIdCpmBound();
    if (pos_cpm_bound_map == nullptr) {
      return;
    }
    std::string kconf_key = absl::StrCat(get_ud_pos_id());
    auto pos_iter = pos_cpm_bound_map->find(kconf_key);
    if (pos_iter != pos_cpm_bound_map->end()) {
      set_ud_cpm_bound(pos_iter->second);
      // 监控每个 pos_id 的 cpm_bound   值
      dot_perf->Interval((get_ud_cpm_bound()) * 1000,
          "universe_kconf_pos_cpm_bound", absl::StrCat(get_ud_pos_id()));
    } else {
      set_ud_target_cpm(pos_cpm_floor);
      set_ud_cpm_bound(get_ud_target_cpm());
    }
  }
  set_ud_universe_pos_target_cpm(get_ud_target_cpm() * kBenifitFactor);
  set_ud_universe_pos_org_target_cpm(
      get_ud_org_target_cpm() * kBenifitFactor);
  set_ud_universe_pos_cpm_bound(get_ud_cpm_bound() * kBenifitFactor);
  if (mutable_ad_request()->mutable_universe_ad_request_info()->imp_info_size() > 0) {
    auto imp_info = mutable_ad_request()->mutable_universe_ad_request_info()->mutable_imp_info(0);
    imp_info->set_target_cpm(get_ud_target_cpm());
    imp_info->set_org_target_cpm(get_ud_org_target_cpm());
    imp_info->set_cpm_bound(get_ud_cpm_bound());
  }
}

bool ContextData::WinRateModelParamsParse() {
    std::string universe_rtb_win_rate_model_params =
      SPDM_universe_rtb_win_rate_model_params(get_spdm_ctx());
    std::vector<std::string> model_params = absl::StrSplit(universe_rtb_win_rate_model_params, ',');
    if (model_params.size() != 3) {
      LOG_EVERY_N(INFO, 10000) << "parase model parasm error " << universe_rtb_win_rate_model_params;
      return false;
    }
    auto &universe_win_rate_model_param_map = *mutable_universe_win_rate_model_param_map();
    for (std::string model_param : model_params) {
      std::vector<std::string> key_value = absl::StrSplit(model_param, '=');
      if (key_value.size() != 2) {
        LOG_EVERY_N(INFO, 10000) << "parase model parasm error, "
                      << universe_rtb_win_rate_model_params << ", model_param " << model_param;
        return false;
      }
      universe_win_rate_model_param_map.insert({key_value[0], key_value[1]});
    }
    return true;
}

bool ContextData::IsPreviewOrDebug() {
  if (borrow_tab_name() == "universe" && UniverseRequest().universe_debug_param().debug_mode()) {
    return true;
  }
  if (get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    return true;
  }

  return false;
}

ContextData::~ContextData() {
}

void ContextData::BaseDataInit(const kuaishou::ad::FrontServerRequest &front_server_request) {
  // 1. dot_perf 初始化
  set_page_id(front_server_request.page_id());
  set_sub_page_id(front_server_request.sub_page_id());
  // 联盟的兜底一下
  set_page_id(19000);
  static std::set<int64_t> universe_sub_page_id_set{19000001, 19000002, 19000003};
  if (universe_sub_page_id_set.count(get_sub_page_id()) <= 0) {
    set_sub_page_id(19000001);
  }
  // dot_perf 的初始化放到最前面
  dot_perf.reset(new ks::ad_base::Dot(Product::UNKNOWN_PRODUCT,
                                      kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNKNOWN,
                                      AdEnum::INTERACTIVE_UNKNOWN_FROM, get_sub_page_id()));
  // 添加一个打点的采样
  set_byte_size_dot(ks::ad_base::AdRandom::GetInt(0, 10000) <= FrontKconfUtil::dotSizeSampleRate());
}

// Detail/Explore Context Build, allow_unlogin = true
// FansTopRequest can use this, allow_unlogin = false;
void ContextData::Build(const ::kuaishou::ad::universe::AthenaAdRequest& athena_ad_request) {
  set_llsid(athena_ad_request.llsid());
  ks::front_server::UniverseData::Init(athena_ad_request, this);
  auto universe_ad_switch = FrontKconfUtil::universeAdSwitch2();
  for (auto iter = get_ud_universe_imp_info().begin();
       iter != get_ud_universe_imp_info().end();) {
    if (!universe_ad_switch->Allowed(borrow_ud_app_id(),
                                     iter->page_id,
                                     iter->sub_page_id)) {
      // 对应广告位被关闭
      iter = mutable_ud_universe_imp_info()->erase(iter);
    } else {
      iter++;
    }
  }
  dot_perf->Count(1, Product_Name(Product::UNKNOWN_PRODUCT), "universe", "process");
}

void ContextData::AfterBuildUserInfo() {
  // 联盟到这里 ad_request.ad_user_info().id() 才有值
  // SetTraceLogSamplingFlag 和 SetTraceLogV2TableSamplingFlag 依赖 uid
  utility::SetTraceLogSamplingFlag(mutable_ad_request(), GetUnifyScene());
  utility::SetTraceLogV2TableSamplingFlag(mutable_ad_request());
  UniverseAbtestInitialize();
  const auto& cache_abtest_world_name = *engine_base::AdKconfUtil::universeCacheAbtestWorldName();
  set_ud_cache_abtest_key(
    absl::StrCat(get_spdm_ctx().Raw()->TryGetExperimentId(cache_abtest_world_name), "_",
                 get_spdm_ctx().Raw()->TryGetGroupId(cache_abtest_world_name)));
  const auto& dynamic_cpm_threshold_abtest_world_name =
    *FrontKconfUtil::universeDynamicCpmThresholdAbtestWorldName();
  set_ud_dynamic_cpm_threshold_abtest_key(
    absl::StrCat(get_spdm_ctx().Raw()->TryGetExperimentId(dynamic_cpm_threshold_abtest_world_name), "_",
                 get_spdm_ctx().Raw()->TryGetGroupId(dynamic_cpm_threshold_abtest_world_name)));
  if (get_ad_request()->ad_user_info().id() <= 0) {
    SetAdInvalidFlag(static_cast<int>(ks::ad_base::AdmitInvalidType::UNIVERSE_UID_ZERO_FILTER));
  }
  if (get_ad_request()->ad_user_info().has_id()) {
    set_user_id(get_ad_request()->ad_user_info().id());
  }
  if (!get_front_server_request()->universe_request().device_info().paid().empty()) {
    mutable_ad_request()->set_paid(get_front_server_request()->universe_request().device_info().paid());
    if (get_ad_request()->ad_user_info().device_info_size() > 0) {
      mutable_ad_request()->mutable_ad_user_info()->mutable_device_info(0)->set_paid2(
          get_front_server_request()->universe_request().device_info().paid());
    }
  }
  if (SPDM_usePaidFromUniverseRequest() && get_ad_request()->ad_user_info().device_info_size() > 0) {
    std::string api_paid_1_5;
    std::string dmp_paid_1_5;
    api_paid_1_5 = get_front_server_request()->universe_request().device_info().paid5();
    if (get_ad_request()->ad_user_info().device_info(0).paid4_size() > 0) {
      dmp_paid_1_5 = get_ad_request()->ad_user_info().device_info(0).paid4(0);
    }
    auto* device_info = mutable_ad_request()->mutable_ad_user_info()->mutable_device_info(0);
    device_info->clear_paid4();
    device_info->add_paid4(api_paid_1_5);
    device_info->add_paid4(dmp_paid_1_5);
  }
  if (get_ad_request()->ad_user_info().device_info_size() > 0) {
    if (!get_front_server_request()->universe_request().device_info().current_taid().empty()) {
      mutable_ad_request()->mutable_ad_user_info()->mutable_device_info(0)->set_current_taid(
        get_front_server_request()->universe_request().device_info().current_taid());
    }
    if (!get_front_server_request()->universe_request().device_info().last_taid().empty()) {
      mutable_ad_request()->mutable_ad_user_info()->mutable_device_info(0)->set_last_taid(
        get_front_server_request()->universe_request().device_info().last_taid());
    }
  }
  if (get_ad_request()->ad_user_info().device_info_size() > 0) {
    if (!get_front_server_request()->universe_request().device_info().ua_ks().empty()) {
      mutable_ad_request()->mutable_ad_user_info()->mutable_device_info(0)->set_us_ks_pattern(
        get_front_server_request()->universe_request().device_info().ua_ks());
    }
  }

  mutable_ad_request()->mutable_ad_user_info()->set_page(::kuaishou::ad::AdEnum::AdSlotType::AdEnum_AdSlotType_HOT);  // NOLINT
  mutable_ad_request()->mutable_ad_user_info()->set_tk_version(get_front_server_request()->universe_request().tk_version());  // NOLINT
  if (get_front_server_request()->universe_request().device_info().has_screen_size()) {
    const auto& screen_size = get_front_server_request()->universe_request().device_info().screen_size();
    const auto& height = screen_size.height();
    const auto& width = screen_size.width();
    if (height > 0 && width > 0) {
      mutable_ad_request()->mutable_ad_user_info()->set_screen_direction_from_size(height >= width ? 1 : 2);
    }
  }

  if (SPDM_enable_universe_get_router_user_info(get_spdm_ctx())) {
    auto universe_predict_request =
    google::protobuf::Arena::CreateMessage<kuaishou::ad::algorithm::UniversePredictRequest>(mutable_arena());
    const auto& ad_user_info = get_ad_request()->ad_user_info();
    auto* ps_request_user_info = universe_predict_request->mutable_ad_user_info();

    universe_predict_request->set_llsid(get_llsid());
    universe_predict_request->set_user_id(ad_user_info.id());
    universe_predict_request->set_item_type(kuaishou::ad::algorithm::ItemType::AD_DSP);
    universe_predict_request->set_app_id(copy_ud_app_id());

    ps_request_user_info->set_device_id(ad_user_info.device_id());
    ps_request_user_info->set_is_unlogin_user(get_ad_request()->ad_user_info().is_unlogin_user());

    common_w_->SetPtrCommonAttr("universe_predict_request", universe_predict_request);
    common_w_->SetIntCommonAttr("enable_universe_get_router_user_info", true);
  }
  // fill reco user info fields used by ad_server
  UserInfoAdapter::FillRecoUserInfoFromAthenaRequest(get_front_server_request()->universe_request(),
                                                     mutable_ad_request(), get_spdm_ctx());
  // fill installed app info
  UserInfoAdapter::FillInstalledAppInfoFromAthenaRequest(
    get_front_server_request()->universe_request(), mutable_ad_request());

  // 传递 debug 信息
  mutable_ad_request()->mutable_debug_param()->CopyFrom(get_front_server_request()->universe_request().debug_param());  // NOLINT
  FillUniverseAdRequestInfo();
}

bool ContextData::IsUgMonopolizeFlow() const {
  auto ug_monopolize_flow = engine_base::AdKconfUtil::ugMonopolizeFlowCpm();
  if (get_pos_manager().request_imp_infos.empty()) {
    return false;
  }
  return ug_monopolize_flow->count(get_ud_pos_id()) != 0;
}

void ContextData::FillUniverseAdRequestInfo() {
  auto *instance = UniversePosResourceV2::GetInstance();
  const kuaishou::ad::ssp::AdUniverseMedium& ad_universe_medium =
      instance->GetAdUniverseMedium(copy_ud_app_id());
  const AdUniverseAutoBlackList& ad_universe_auto_black_list =
      instance->GetAdUniverseAutoBlackList(ad_universe_medium.uid());
  if (ad_universe_medium.app_id().empty()) {
    LOG_EVERY_N(WARNING, 10000) << "app_id error:" << borrow_ud_app_id();
    return;
  }
  for (const auto& predict_score : get_front_server_request()->universe_request().predict_score()) {
    mutable_ad_request()->add_predict_score()->CopyFrom(predict_score);
  }
  mutable_ad_request()->set_universe_live_support_mode(UniverseRequest().universe_live_support_mode());
  // 聚合页一期 初始化
  set_ud_is_universe_aggregation_scene(UniverseRequest().is_neo_scan_aggregation_scene());
  mutable_ad_request()->set_is_universe_aggregation_scene(get_ud_is_universe_aggregation_scene());
  auto *universe_ad_request_info = mutable_ad_request()->mutable_universe_ad_request_info();

  std::string search_query_tmp = get_front_server_request()->universe_request().query();
  engine_base::string_util::strNormalizeUtf8(search_query_tmp,
  engine_base::string_util::SNO_TO_LOWER | engine_base::string_util::SNO_TO_HALF |
  engine_base::string_util::SNO_TO_SIMPLIFIED);
  universe_ad_request_info->set_search_query(search_query_tmp);
  set_ud_search_query(search_query_tmp);

  universe_ad_request_info->set_app_id(copy_ud_app_id());
  universe_ad_request_info->set_real_user_id(std::to_string(get_ad_request()->ad_user_info().id()));
  universe_ad_request_info->set_sdk_version(copy_ud_sdk_version());
  universe_ad_request_info->set_sdk_api_version(copy_ud_sdk_api_version());
  universe_ad_request_info->set_protocol_version(copy_ud_protocol_version());
  universe_ad_request_info->set_medium_uid(ad_universe_medium.uid());
  universe_ad_request_info->set_medium_attribute(ad_universe_medium.medium_attribute());
  universe_ad_request_info->set_medium_type(ad_universe_medium.medium_type());
  universe_ad_request_info->set_medium_industry_id(ad_universe_medium.industry_id());
  universe_ad_request_info->set_medium_sub_industry_id(ad_universe_medium.sub_industry_id());
  universe_ad_request_info->set_medium_game_category_id(ad_universe_medium.game_category_id());
  universe_ad_request_info->set_medium_industry_id_v2(ad_universe_medium.industry_id_v2());
  universe_ad_request_info->set_medium_sub_industry_id_v2(ad_universe_medium.sub_industry_id_v2());
  universe_ad_request_info->set_sdk_type(get_front_server_request()->universe_request().sdk_type());
  universe_ad_request_info->mutable_query_feature()->CopyFrom(
      get_front_server_request()->universe_request().query_feature());
  universe_ad_request_info->set_medium_package_name(
      get_front_server_request()->universe_request().app_info().package_name());
  universe_ad_request_info->set_universe_media_small_app_id(ad_universe_medium.wx_app_id());
  set_ud_wechat_app_id(ad_universe_medium.wx_app_id());
  if (UniverseRequest().universe_task_type() > 0) {
    universe_ad_request_info->set_universe_task_type(UniverseRequest().universe_task_type());
  }
  bool already_generate_request_type = false;
  for (const auto& item : get_ud_universe_imp_info()) {
    auto pos_id = item.pos_id;
    if (pos_id == 0) {
      // NOTE: 为了兼容老 1.0 协议
      LOG_EVERY_N(INFO, 10000) << "sub page id:" << item.sub_page_id;
      falcon::Inc("ad_athena.universe_trans_sub_page_id_to_pos_id");
      pos_id = item.sub_page_id;
    }

    const AdUniversePosition& universe_position = instance->GetAdUniversePosition(pos_id);
    if (universe_position.position_id() == 0) {
      LOG_EVERY_N(WARNING, 1) << "pos_id error:" << pos_id;
      dot_perf->Count(1, "pos_id_error");
      continue;
    }
    auto *imp_info = universe_ad_request_info->add_imp_info();
    imp_info->set_page_id(get_page_id());
    imp_info->set_quick_tag(item.quick_tag);
    imp_info->set_sub_page_id(get_sub_page_id());
    imp_info->set_action(item.action);
    imp_info->set_cpm_bid_floor(item.cpm_bid_floor);
    // 厘/千次 转化成 分/千次
    imp_info->set_position_cpm_floor(universe_position.cpm_floor() / 10);
    set_ud_position_cpm_floor(universe_position.cpm_floor() / 10);
    imp_info->set_position_id(pos_id);

    // 添加物理广告位信息
    {
      int64_t physical_position_id = 0;
      if (universe_position.physical_position_ids_size() > 0) {
        physical_position_id = universe_position.physical_position_ids()[0];
      }
      imp_info->set_physical_position_id(physical_position_id);
      dot_perf->Count(1, "ad_front.physical_pos_id_ratio", "media_mark",
                      physical_position_id == 0 ? "0" : "1");
      if (physical_position_id == 0 && SPDM_enable_person_mark_physical_posid(get_spdm_ctx())) {
        UniverseAggPosCluster::GetInstance()->GetClusterPosId(pos_id, &physical_position_id);
      } else if (physical_position_id == 0) {
        bool request_grid_traffic = common_w_->GetIntCommonAttr("request_grid_traffic")
          .value_or(0);
        if (request_grid_traffic) {
          physical_position_id = get_fi_physical_pos_id();
        } else {
          UniverseAggPosClusterV2::GetInstance()->GetClusterPosId(pos_id, &physical_position_id);
        }
      }
      dot_perf->Count(1, "ad_front.physical_pos_id_ratio", "media_algorithm_mark",
                      physical_position_id == 0 ? "0" : "1", copy_ud_cache_abtest_key());
      set_ud_physical_pos_id(physical_position_id);
    }

    // 添加模版列表
    imp_info->mutable_template_ids()->CopyFrom(universe_position.template_ids());
    imp_info->mutable_sub_template_ids()->CopyFrom(
                                          universe_position.sub_template_id());

    bool video_material_filter = false;
    if (universe_position.ad_style() == 13 ||
        universe_position.ad_style() == 16) {
      video_material_filter = engine_base::CompareAppVersion(UniverseRequest().sdk_version(), "3.3.22") < 0;
    }
    const auto& athena_imp_info = get_front_server_request()->universe_request().imp_info();
    if (athena_imp_info.ad_pos_info_size() > 0 &&
        athena_imp_info.ad_pos_info(0).creative_material_types_size() > 0) {
      for (const auto& creative_type : athena_imp_info.ad_pos_info(0).creative_material_types()) {
        imp_info->add_creative_material_types(
            static_cast<kuaishou::ad::AdEnum_CreativeMaterialType>(creative_type));
        mutable_ud_creative_material_types()->emplace(creative_type);
      }
    } else {
      for (const auto& creative_type : universe_position.creative_material_types()) {
        if (universe_position.content_type() == 0) {
          imp_info->add_creative_material_types(
          static_cast<kuaishou::ad::AdEnum_CreativeMaterialType>(creative_type));
          mutable_ud_creative_material_types()->emplace(creative_type);
        } else if (universe_position.content_type() == 2) {
          if (!video_material_filter || creative_type == 3) {
            imp_info->add_creative_material_types(
            static_cast<kuaishou::ad::AdEnum_CreativeMaterialType>(creative_type));
            mutable_ud_creative_material_types()->emplace(creative_type);
          }
        }
      }
    }

    // 添加厂商流量建议出价系数
    if (athena_imp_info.ad_pos_info_size() > 0) {
      for (const auto& item : athena_imp_info.ad_pos_info()) {
        imp_info->mutable_increase_ratio_list()->CopyFrom(item.increase_ratio_list());
        if (imp_info->increase_ratio_list_size() > 0) {
          dot_perf->Interval(imp_info->increase_ratio_list_size(), "front_server.increase_ratio_list_size");
        }
      }
    }

    // 临时: creative_material_type 不包含 3 不出原生图文广告，fix 后删除
    if ((get_ud_creative_material_types().count(5) && get_ud_creative_material_types().count(6)) &&
        get_ud_creative_material_types().count(3) == 0) {
      imp_info->add_creative_material_types(
          static_cast<kuaishou::ad::AdEnum_CreativeMaterialType>(3));
      mutable_ud_creative_material_types()->emplace(3);
    }
    imp_info->set_ad_style(universe_position.ad_style());
    // ad_style 16 完全对齐 13
    if (universe_position.ad_style() == 16) {
      imp_info->set_ad_style(13);
    }

    if (FrontKconfUtil::universeTinyWhitePosId()->count(get_ud_pos_id())) {
      auto pos_info_kconf = FrontKconfUtil::universeAppStorePosInfo()->data();
      set_ud_is_app_store_flow(pos_info_kconf.IsAppStoreFlow(get_ud_pos_id()));
      if (get_ud_is_app_store_flow()) {
        imp_info->set_ad_style(pos_info_kconf.GetAdStyle(get_ud_pos_id()));
        imp_info->set_app_distribute_type(pos_info_kconf.GetDistributeType(get_ud_pos_id()));
      }
      set_ud_app_distribute_type(imp_info->app_distribute_type());
    }
    set_ud_ad_style(imp_info->ad_style());

    imp_info->set_render_type(universe_position.render_type());
    imp_info->set_template_id(universe_position.template_id());
    imp_info->set_test_status(universe_position.test_status());
    imp_info->set_rewarded_type(universe_position.rewarded_type());
    imp_info->set_rewarded_num(universe_position.rewarded_num());
    imp_info->set_create_time(universe_position.create_time());
    imp_info->set_traffic_id(universe_position.traffic_id());
    imp_info->set_traffic_type(universe_position.traffic_type());
    if (universe_position.ad_rollout_size() > 0) {
      imp_info->set_ad_rollout_size(universe_position.ad_rollout_size());
    }
    imp_info->mutable_traffic_info()->CopyFrom(item.traffic_infos);
    imp_info->mutable_out_pos_param()->CopyFrom(item.outpos_param);
    universe_ad_request_info->set_cooperation_mode(universe_position.cooperation_mode());  // pos_id 维度
    set_ud_cooperation_mode(universe_position.cooperation_mode());
    if (item.ad_num > 0) {
      auto pos_id = get_ad_request()->universe_ad_request_info().imp_info().empty() ?
        0 : get_ad_request()->universe_ad_request_info().imp_info(0).position_id();
      if (universe_position.ad_style() == 11) {
      // 内容聚合页广告最大广告数设置为 12
        imp_info->set_ad_num(item.ad_num > 12 ? 12 : item.ad_num);
      } else if (universe_position.ad_style() == 5) {
        // banner 最大广告数为 5
        imp_info->set_ad_num(item.ad_num > 5 ? 5 : item.ad_num);
      } else {
        imp_info->set_ad_num(item.ad_num > 5 ? 5 : item.ad_num);
      }
      // 激励视频再看一个
      const auto& task_type_list = UniverseRequest().accept_task_type();
      if (std::count(task_type_list.begin(), task_type_list.end(), 4) &&
          universe_position.ad_style() == 2) {
        imp_info->set_ad_num(2);
      }
    } else {
      imp_info->set_ad_num(1);
    }
    if (get_ud_is_universe_aggregation_scene()) {   // 激励聚合场景调整广告数
      int64_t ad_num = SPDM_universe_agg_scene_ad_num(get_spdm_ctx());
      imp_info->set_ad_num(ad_num);
    }
    if (UniverseRequest().universe_debug_param().aggregate_page_new_link_switch()) {
      imp_info->set_ad_num(item.ad_num);
    }
    if (!already_generate_request_type && imp_info->ad_num() != 0) {
      already_generate_request_type = true;
    }
    for (auto& pos_seq_id : item.pos_seq_ids) {
      imp_info->add_pos_seq_ids(pos_seq_id);
    }
    for (auto& pkg : item.pkg_white_list) {
      imp_info->add_pkg_white_list(pkg);
      mutable_ud_pkg_white_list()->emplace(pkg);
    }
    for (auto& pkg : ad_universe_auto_black_list.package()) {
      imp_info->add_pkg_black_list(pkg);
    }
  }
  if (universe_ad_request_info->imp_info_size() > 0 &&
      get_front_server_request()->universe_request().imp_info().ad_pos_info_size() > 0) {
    auto *imp_info = universe_ad_request_info->mutable_imp_info(0);
    const auto& pos_info = get_front_server_request()->universe_request().imp_info().ad_pos_info(0);
    for (const auto& pkg : pos_info.pkg_black_list()) {
      imp_info->add_pkg_black_list(pkg);
    }
    for (const auto& industry_id : pos_info.white_industry_ids()) {
      imp_info->add_white_industry_ids(industry_id);
    }
    for (const auto& industry_id : pos_info.black_industry_ids()) {
      imp_info->add_black_industry_ids(industry_id);
    }
    for (const auto& creative_id : pos_info.black_creative_ids()) {
      imp_info->add_black_creative_ids(creative_id);
    }
    imp_info->set_ad_score(pos_info.ad_score());
    imp_info->set_support_channel_pkg(pos_info.support_channel_pkg());
    imp_info->mutable_suggest_pkg_white_list()->CopyFrom(pos_info.suggest_pkg_white_list());
    imp_info->set_suggest_pkg_relevance(pos_info.suggest_pkg_relevance());
  }

  if (universe_ad_request_info->medium_attribute() == 2
      || universe_ad_request_info->medium_attribute() == 4) {
      mutable_ad_request()->set_ad_request_flow_type(kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_UNIVERSE);
  }
  static std::set<int32_t> ad_style_sets = {1, 2};
  if (SPDM_enable_closure_support_mode(get_spdm_ctx()) && ad_style_sets.count(get_ud_ad_style()) &&
      UniverseRequest().closure_support_mode()) {
    set_is_closure_flow(true);
    universe_ad_request_info->set_closure_support_mode(true);
  }
  // do not delete this line! adserver use reco user info id to judge logined or not
  mutable_ad_request()->mutable_reco_user_info()->set_id(get_ad_request()->ad_user_info().id());
  mutable_ad_request()->mutable_universe_ad_request_info()->set_ext_data(copy_ud_universe_ext());

  mutable_ad_request()->set_preload_check(UniverseRequest().preload_check());
  mutable_ad_request()->mutable_universe_ad_request_info()->set_preload_check(UniverseRequest().preload_check());  // NOLINT

  mutable_ad_request()->set_extra_request_ctx("{\"llsid\": " + std::to_string(get_llsid()) + "}");
}

// operator<< 重载定义在 util/common/log_info.h 中
void ContextData::LogInfo(const FrontServerResponse& front_response) {
  LOG_EVERY_N(INFO, 50) << "FrontRequestType:"
                        << kuaishou::ad::FrontRequestType_Name(get_front_server_request()->type())
                        << "|llsid:" << get_llsid() << "|user_id:" << get_user_id()
                        << "|pass_pre_filter:" << get_pass_pre_filter() << "|device_id:" << copy_device_id()
                        << "|total_cost:" << (base::GetTimestamp() - get_start_time_us()) << "us"
                        << "|pos_id:" << get_ud_pos_id() << "|ad_num:" << get_ud_res_adnum()
                        << "|invalid_flag:" << GetAdInvalidFlag()
                        << "|search_query:" << borrow_ud_search_query()
                        << "|app_distribute_type:" << borrow_ud_app_distribute_type()
                        << "|request_universe_tiny:" << get_ud_request_universe_tiny();
}

std::ostream& operator<<(std::ostream& os, const std::vector<int32_t>& v) {
  os << "size: " << v.size() << "| ";
  for (int32_t pos : v) {
    os << pos << ",";
  }
  return os;
}

bool ContextData::IsSkipCommonAdmit() const {
  if (UniverseRequest().universe_debug_param().aggregate_page_new_link_switch()) {
    dot_perf->Count(1, "front_server.aggregate_page_new_link");
    return true;
  }
  if (UniverseRequest().universe_task_type() > 0) {
    dot_perf->Count(1, "front_server.aggregate_page_link",
        std::to_string(UniverseRequest().universe_task_type()));
    return true;
  }
  auto appid = get_ad_request()->universe_ad_request_info().app_id();
  if (UniverseSkipAdmitFilter(absl::StrCat(UniverseRequest().user_info().user_id()),
                              UniverseRequest().device_info().oaid(),
                              UniverseRequest().device_info().imei(),
                              UniverseRequest().device_info().idfa(),
                              UniverseRequest().device_info().device_id())) {
    return true;
  }
  if (SPDM_universeAppIdAdmitWhiteListReplace()) {
    auto conf = FrontKconfUtil::skipCommonAdmitAppIds();
    if (std::count(conf->begin(), conf->end(), appid) > 0) {
      dot_perf->Count(1, "front_server.skip_common_admit_new", appid);
      return true;
    }
  } else {
    auto did = get_ad_request()->ad_user_info().device_id();
    auto config = FrontKconfUtil::skipCommonAdmitConfig();
    auto iter = config->data().app_device_white_list().find(appid);
    if (iter == config->data().app_device_white_list().end()) {
      return false;
    }
    if ((std::count(iter->second.device_id().begin(),
                  iter->second.device_id().end(),
                  "*") > 0)
      || (std::count(iter->second.device_id().begin(),
                  iter->second.device_id().end(),
                  did) > 0)) {
      dot_perf->Count(1, "front_server.skip_common_admit", appid);
      return true;
    }
  }
  return false;
}

bool ContextData::UniverseSkipAdmitFilter(const std::string& uid, const std::string& oaid,
     const std::string& imei, const std::string& idfa, const std::string& did) const {
  const auto &admit_id_config = FrontKconfUtil::universeSkipAdmitFilterId()->data();
  std::unordered_set<std::string> userid_lists(admit_id_config.user_id().begin(),
                                                admit_id_config.user_id().end());
  std::unordered_set<std::string> oaid_lists(admit_id_config.oaid().begin(),
                                                admit_id_config.oaid().end());
  std::unordered_set<std::string> imei_lists(admit_id_config.imei().begin(),
                                                admit_id_config.imei().end());
  std::unordered_set<std::string> idfa_lists(admit_id_config.idfa().begin(),
                                                admit_id_config.idfa().end());
  std::unordered_set<std::string> device_id_lists(admit_id_config.device_id().begin(),
                                                admit_id_config.device_id().end());

  return userid_lists.count(uid) || oaid_lists.count(oaid) || imei_lists.count(imei)
      || idfa_lists.count(idfa) || device_id_lists.count(did);
}

void ContextData::LazyInitAttr(const CommonIdx &idx) {
  if (!lazy_init_attr_) {
    return;
  }
#define __Attr(x) break; case (x): (*attrs_[idx])
  switch (idx) {
    case (CommonIdx::for_test):  // 占位, 为了后面的宏能展开
      #include "context_data-context_data.init.extra"  // NOLINT
      break;
    default:
      break;
  }
#undef __Attr
}

bool ContextData::PassKspGroupCheck(const FrontServerRequest& request) {
  // 此处不过滤测试环境的请求
  if (get_for_test()) {
    return true;
  }

  auto ksp_group = ks::ad_base::AdKessClient::Instance().GetKspGroup();
  bool temp_pre_filter_status = get_pass_pre_filter();
  if (SPDM_universeFlowTypeCheckAdvanced() &&
      !ks::engine_base::FlowChecker::Instance()->AcceptFlowType(request.flow_type())) {
    temp_pre_filter_status = false;
  }
  LOG_EVERY_N(INFO, 100000) << "ksp_group: " << ksp_group
          << ", request_name: " << kuaishou::ad::FrontRequestType_Name(request.type());
  ksp_group = ksp_group.empty() ? "default" : ksp_group;
  set_pass_pre_filter(temp_pre_filter_status);
  return get_pass_pre_filter();
}

void ContextData::ParseRankResult(const AdResponse *ad_resp, bool overwrite_tag) {
  if (!ad_resp) {
    return;
  }
  // preivew 请求不会走 rank，可以继续用 ad_rank_pass_through
  auto &rank_2_front_info = *mutable_rank_2_front_info();
  rank_2_front_info.Init(this);
  rank_2_front_info.Parse(*ad_resp);

  const std::string &rank_result_b64 = ad_resp->ad_rank_result_pass_through();
  if (rank_result_b64.empty()) {
    return;
  }

  std::string pass_through_str;
  kuaishou::ad::AdRankPassThrough tmp;
  if (!(base::Base64Decode(rank_result_b64, &pass_through_str)
      && tmp.ParseFromString(pass_through_str))) {
    falcon::Inc("front_server.ad_rank_result_parse_failed", 1);
    return;
  }

  mutable_ad_rank_pass_through()->set_universe_inner_qcpx_pv_filter_reason(
    tmp.universe_inner_qcpx_pv_filter_reason());

  int start_index = get_ad_rank_pass_through().ad_rank_result_size();
  auto &creative_id_2_idx = *mutable_creative_id_2_idx();
  for (int i = 0; i < tmp.ad_rank_result_size(); ++i) {
    int64_t creative_id = tmp.ad_rank_result(i).creative_id();
    creative_id_2_idx[creative_id] = i + start_index;
    mutable_ad_rank_pass_through()->add_ad_rank_result()->Swap(tmp.mutable_ad_rank_result(i));
    mutable_ad_rank_pass_through()->add_rank_info()->Swap(tmp.mutable_rank_info(i));
  }

  if (overwrite_tag) {
    mutable_ad_rank_pass_through()->mutable_group_tag()->swap(*tmp.mutable_group_tag());
    mutable_ad_rank_pass_through()->mutable_deep_group_tag()->swap(*tmp.mutable_deep_group_tag());
  }
  falcon::Stat("front_server.rank_result_size", get_ad_rank_pass_through().ad_rank_result_size());
  falcon::Stat("front_server.rank_info_size", get_ad_rank_pass_through().rank_info_size());
}

void ContextData::GetLastPvAdInfo(const AdRequest& ad_req,
                                  const bool enable_dynamic_pos,
                                  kuaishou::ad::UserLastPvInfo* last_pv_info) {
  const auto& front_internal_data = ad_req.front_internal_data();
  last_pv_info->set_last_explore_pv_timestamp(front_internal_data.last_explore_pv_timestamp());
  last_pv_info->set_last_explore_pv_page_size(front_internal_data.last_explore_pv_page_size());
  last_pv_info->set_last_explore_pv_last_ad_pos(front_internal_data.last_explore_pv_last_ad_pos());
  last_pv_info->set_first_screen_ad_shw_timestamp(front_internal_data.first_screen_ad_shw_timestamp());
  if (IsColdStartNoAdReq()) {
    last_pv_info->set_last_pv_is_cold_start(true);
  } else {
    last_pv_info->set_last_pv_is_cold_start(false);
  }
  last_pv_info->set_disable_dynamic_pos(!enable_dynamic_pos);
}

void ContextData::SetInventoryPosInfo(const kuaishou::ad::AdRequest &ad_req) {
  if (ad_req.universe_ad_request_info().imp_info_size() != 0) {
    auto &request_inventory_pos_id = *mutable_request_inventory_pos_id();
    for (int i = ad_req.universe_ad_request_info().imp_info_size() - 1; i >= 0; --i) {
      const auto& imp = ad_req.universe_ad_request_info().imp_info(i);
      if (imp.ad_num() == 0) {
        continue;
      }
      request_inventory_pos_id.push_back(imp.position_id());
    }
  }
}

const ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode ContextData::MediumParameterCheck() const {
  auto athena_ad_request = UniverseRequest();
  auto appid = athena_ad_request.app_info().app_id();
  auto package_name = athena_ad_request.app_info().package_name();
  auto os_type =  athena_ad_request.device_info().os_type();
  const auto& app_id_white_list = FrontKconfUtil::universeAppidWhiteList();
  const bool is_white_app_id = app_id_white_list->count(appid);

  if (os_type != kuaishou::ad::universe::DeviceInfo::IOS
    && os_type != kuaishou::ad::universe::DeviceInfo::ANDROID
    && os_type != kuaishou::ad::universe::DeviceInfo::HARMONY) {
    return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::ERROR_OS_TYPE;
  }

  auto *instance = UniversePosResourceV2::GetInstance();
  const kuaishou::ad::ssp::AdUniverseMedium& ad_universe_medium =
      instance->GetAdUniverseMedium(copy_ud_app_id());
  // 媒体维度校验
  if (ad_universe_medium.app_id().empty()) {
    // app_id 未注册或者已经删除
    return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::APPID_NOT_EXISTED;
  }
  if (ad_universe_medium.prohibit_status() != 0 ||
      (!is_white_app_id && ad_universe_medium.final_status() != 1)) {
    // app_id 被封禁
    return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::APPID_BLOCKED;
  }
  // 前期有一个 appid 对应多个包以及操作系统的情况
  auto register_package_name = ad_universe_medium.package_name();
  register_package_name.erase(std::remove_if(register_package_name.begin(),
      register_package_name.end(),
      [](unsigned char x){return std::isspace(x);}),
      register_package_name.end());
  auto whitelist = FrontKconfUtil::universeSspPackageCheckWhitelist();
  if (!register_package_name.empty() &&
      package_name.find(register_package_name) == std::string::npos &&
      (whitelist == nullptr || whitelist->count(appid) == 0)) {
    // 包名与注册的不一致
    return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::PACKAGENAME_ERROR;
  }

  // 账号维度校验
  const kuaishou::ad::ssp::AdUniverseAccount& universe_account =
      instance->GetAdUniverseAccount(ad_universe_medium.uid());
  if (universe_account.uid() == 0) {
    // uid 未注册或者已经删除
    return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::ACCOUNT_INVALID;
  }
  if (universe_account.prohibit_status() != 0 ||
      (!is_white_app_id && universe_account.final_status() != 1)) {
    // uid 被封禁
    return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::ACCOUNT_BLOCKED;
  }

  // 广告位维度的校验,遇到一个不符合则 check 失败(目前就一个)
  for (const auto& item : athena_ad_request.imp_info().ad_pos_info()) {
    auto pos_id = item.pos_id();
    if (pos_id == 0) {
      pos_id = item.sub_page_id();  // 兼容 1.0 版本没 pos_id 的情况
    }

    const AdUniversePosition& universe_position = instance->GetAdUniversePosition(pos_id);
    if (universe_position.position_id() == 0) {
      // pos_id 未注册或者已经删除
      return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::POSID_NOT_EXISTED;
    }
    if (universe_position.prohibit_status() != 0 ||
        (!is_white_app_id && universe_position.final_status() != 1)) {
      // pos_id 封禁
      return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::POSID_BLOCKED;
    }
    if (universe_position.app_id() != appid) {
      // pos_id 对应的 appid 与传进来的不一致
      return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::POSID_APPID_NOT_MATCH;
    }
  }
  return ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::SUCCESS;
}

// 判断是否为试玩流量
bool ContextData::IsUnionPlayableExpand() {
  if (get_ad_request() == nullptr ||
      get_ad_request()->universe_ad_request_info().imp_info_size() == 0) {
    return false;
  }
  bool is_ads_sdk = get_ad_request()->universe_ad_request_info().sdk_type() == 1;
  bool is_content_sdk = get_ad_request()->universe_ad_request_info().sdk_type() == 2;
  bool valid_sdk_version_336 =
          engine_base::CompareAppVersion(
                  get_ad_request()->universe_ad_request_info().sdk_version(),
                  "3.3.6") >= 0;
  bool valid_sdk_version_3321 =
          engine_base::CompareAppVersion(
                  get_ad_request()->universe_ad_request_info().sdk_version(),
                  "3.3.21") >= 0;
  bool valid_sdk_version_3310 =
          engine_base::CompareAppVersion(
                  get_ad_request()->universe_ad_request_info().sdk_version(),
                  "3.3.10") >= 0;
  bool valid_sdk_version_3313 =
          engine_base::CompareAppVersion(
                  get_ad_request()->universe_ad_request_info().sdk_version(),
                  "3.3.13") >= 0;
  bool valid_sdk_version_3323 =
          engine_base::CompareAppVersion(
                  get_ad_request()->universe_ad_request_info().sdk_version(),
                  "3.3.23") >= 0;
  int32_t ad_style = get_ad_request()->universe_ad_request_info().imp_info(0).ad_style();
  bool is_inspire = ad_style == 2 || ad_style == 12;
  if (valid_sdk_version_336 && is_ads_sdk && is_inspire) {  // 只有激励视频请求出试玩广告
    return true;
  }
  if (valid_sdk_version_3321 && is_ads_sdk && ad_style == 3) {
    return true;
  }
  if (valid_sdk_version_3310 && is_content_sdk && is_inspire) {
    return true;
  }
  if (valid_sdk_version_3313 && is_content_sdk && ad_style == 3) {
    return true;
  }
  if (ad_style == 13 && FrontKconfUtil::playAbleSdkMinVersionConfig()) {
    const std::string &table_screen_ads_sdk_min_version =
            FrontKconfUtil::playAbleSdkMinVersionConfig()->data().table_screen_ads_sdk();
    const std::string &table_screen_content_sdk_min_version =
            FrontKconfUtil::playAbleSdkMinVersionConfig()->data().table_screen_content_sdk();
    if (is_ads_sdk && !table_screen_ads_sdk_min_version.empty() &&
        engine_base::CompareAppVersion(
                get_ad_request()->universe_ad_request_info().sdk_version(),
                table_screen_ads_sdk_min_version) >= 0) {
      return true;
    }
    if (is_content_sdk && !table_screen_content_sdk_min_version.empty() &&
        engine_base::CompareAppVersion(
                get_ad_request()->universe_ad_request_info().sdk_version(),
                table_screen_content_sdk_min_version) >= 0) {
      return true;
    }
  }
  return false;
}

static const std::unordered_set<kuaishou::log::ad::AdTraceFilterCondition> invalid_filter_set{
    kuaishou::log::ad::AdTraceFilterCondition::ADX_RETRIEVAL_RESULTS,
    kuaishou::log::ad::AdTraceFilterCondition::LUCKY_ONE,
    kuaishou::log::ad::AdTraceFilterCondition::FRONT_LUCKY_ONE};

void ContextData::RecordHardLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition) {
  if (invalid_filter_set.count(condition)) {
    return;
  }
  set_last_filter_condition(condition);
}

void ContextData::RecordSoftLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition) {
  if (!invalid_filter_set.count(condition)) {
    set_soft_last_filter_condition(condition);
  }
}

// !!!! 所有的成员变量都需要重新 Init
void ContextData::Clear() {
  spdm_ctx.Clear();
  ad_list.Clear();
  ks::front_server::UniverseData::Clear(this);
  // 联盟请求合并相关
  if (universe_request_merge_info.need_erase) {
    DspRequestMerger::Instance().EraseKey(universe_request_merge_info.universe_merge_req_key);
  }
  universe_request_merge_info.Clear();
  change_context_shared_ptr_source_ = false;
  is_dragon_context_initialized_ = false;
  change_context_string_source_v2_ = false;
}

bool ContextData::IsColdStartNoAdReq() const {
  if (get_ad_request() && get_ad_request()->has_front_internal_data() &&
      get_ad_request()->front_internal_data().has_cold_start_request_type()) {
    return get_ad_request()->front_internal_data().cold_start_request_type() ==
            kuaishou::ad::FrontInternalData_ColdStartRequestType_kColdStartNoAd;
  }
  return false;
}

int ContextData::GetFanstopInvalidFlag() const {
  if (!get_ad_request()) return 0;
  return mutable_ad_request()->mutable_front_internal_data()->fanstop_invalid_flag();
}

int ContextData::GetAdInvalidFlag() const {
  if (!get_ad_request()) return 0;
  return mutable_ad_request()->mutable_front_internal_data()->ad_invalid_flag();
}

void ContextData::SetAdInvalidFlag(int val) {
  if (!get_ad_request()) return;
  mutable_ad_request()->mutable_front_internal_data()->set_ad_invalid_flag(val);
}

void ContextData::SetPassPreFilter(bool val) {
  if (!get_ad_request()) return;
  mutable_ad_request()->mutable_front_internal_data()->set_pass_pre_filter(val);
}

bool ContextData::GetPassPreFilter() const {
  if (!get_ad_request()) return false;
  return mutable_ad_request()->mutable_front_internal_data()->pass_pre_filter();
}

void ContextData::SetRankMigrationSwitches() {
  bool enable_move_build_ad_response_to_front = false;
  bool enable_move_build_ad_response_to_front_diff = false;
  bool enable_move_request_rank_to_front = false;
  bool enable_move_request_jk_to_front = false;
  common_w_->SetIntCommonAttr(
    common_attr::enable_move_build_ad_response_to_front,
    enable_move_build_ad_response_to_front);
  common_w_->SetIntCommonAttr(
    common_attr::enable_move_build_ad_response_to_front_diff,
    enable_move_build_ad_response_to_front_diff);
  common_w_->SetIntCommonAttr(
    common_attr::enable_move_request_rank_to_front,
    enable_move_request_rank_to_front);
  common_w_->SetIntCommonAttr(
    common_attr::enable_move_request_jk_to_front,
    enable_move_request_jk_to_front);
}

#include "context_data-context_data.cc.inl"  // NOLINT
}  // namespace front_server
}  // namespace ks
