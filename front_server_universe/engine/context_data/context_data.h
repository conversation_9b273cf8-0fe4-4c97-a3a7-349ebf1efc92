#pragma once

#include <map>
#include <memory>
#include <ostream>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/time/timestamp.h"
#include "absl/strings/string_view.h"
#include "base/common/basic_types.h"

#include "dragon/src/core/common_reco_context_interface.h"
#include "glog/logging.h"
#include "google/protobuf/arena.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "ks/base/abtest/session_context.h"
#include "nlohmann/json.hpp"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_base/src/better_enums/better_enums.h"
#include "teams/ad/ad_base/src/bthread/bthread_task.h"
#include "teams/ad/ad_base/src/clock_cache/expired_clock_cache.h"
#include "teams/ad/ad_base/src/common/ad_session_context.h"
#include "teams/ad/ad_base/src/common_data/freq_data.h"
#include "teams/ad/ad_base/src/dot/dot.h"
#include "teams/ad/ad_base/src/klog/ad_engine_chain_trace_log.h"
#include "teams/ad/ad_base/src/klog/ad_ylog.h"
#include "teams/ad/ad_base/src/klog/klog.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_base.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_base/src/redis/redis_util.h"
#include "teams/ad/ad_base/src/spdm_lib/src/context.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_forward_index.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_pack_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_rank_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_style_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/adsocial/ad_social_follow_reco.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common_ad_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/engine_trace_log/ad_simplify_always_log.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/fanstop/fans_top_follow_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/front_service.pb.h"
#include "teams/ad/ad_proto/maven/corerpc/photo/photo_author_service.kess.grpc.pb.h"
#include "teams/ad/engine_base/kconf/rta_config.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/front_server_universe/engine/context_data/ad_common.h"
#include "teams/ad/front_server_universe/engine/context_data/ad_list.h"
#include "teams/ad/front_server_universe/engine/context_data/base_data.h"
#include "teams/ad/front_server_universe/engine/strategy/enums.h"
#include "teams/ad/front_server_universe/trace/ad_front_simplify_always_log.h"
#include "teams/ad/front_server_universe/trace/ad_perf_info_log.h"
#include "teams/ad/front_server_universe/trace/ad_select_stage_info_log.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/request_merger/dsp_request_merger.h"
#include "teams/ad/front_server_universe/util/universe_traffic_control/universe_traffic_control.h"
#include "teams/ad/front_server_universe/util/utility/front_logging.h"
#include "teams/ad/front_server_universe/util/utility/strategy_util.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
#include "teams/ad/engine_base/fanstop_common/fans_def.h"
#include "teams/ad/front_server_universe/engine/context_data/common_attrs.h"
#include "teams/ad/front_server_universe/util/utility/async_redis_helper.h"
#include "teams/ad/front_server_universe/engine/utils/data_adapter/request_adapter.h"
using kuaishou::ad::AdEnum;
using kuaishou::ad::FrontServerRequest;
using kuaishou::ad::FrontServerResponse;
using kuaishou::ad::AdRequest;
using kuaishou::ad::AdResponse;
using kuaishou::ad::AdRankPassThrough;
using kuaishou::fanstop::FansTopRequest;
using kuaishou::fanstop::FansTopResponse;
using kuaishou::ad::forward_index::GetStyleInfoReq;
using kuaishou::ad::forward_index::GetStyleInfoResp;
using kuaishou::ad::forward_index::GetCreativeReq;
using kuaishou::ad::forward_index::GetCreativeResp;
using kuaishou::ad::forward_index::GetUnitReq;
using kuaishou::ad::forward_index::GetUnitResp;
using kuaishou::ad::forward_index::CreativeItem;
using kuaishou::ad::forward_index::UnitItem;
using kuaishou::ad::forward_index::UserIdPreviewInfo;
using kuaishou::ad::forward_index::UserIdPreviewResp;
using kuaishou::ad::FollowSocialRequest;
using kuaishou::ad::FollowSocialResponse;
using kuaishou::ad::FollowFanstopRequest;
using kuaishou::ad::FollowFanstopResponse;
using kuaishou::ad::AdPackRequest;
using kuaishou::ad::AdPackResponse;
using kuaishou::ad::tables::AdStyleMaterial;
using kuaishou::ad::tables::AdMagicSitePageDas;
using kuaishou::ad::tables::AdMatrixStyleMaterial;
using kuaishou::ad::tables::AdCreativePreview;

namespace ks {
namespace front_server {
using ks::ad_base::AdmitInvalidType;
class BaseRetrievalHandler;
struct ForwardHandlerCommon;
class RetrievalRtaAdsStrategy;
class StrategyManager;
class AdFrontProcessPrepareMixer;
class AdFrontProcessPrepareInitMixer;
// TODO(wanglei10): 待删除

#define _Attr_(v) v
enum CommonIdx : int {
  ALL_COMMON_ATTRS,  // 这个是上面的宏展开, 不要在这里加变量
  MAX_ATTR_NUM
};
#undef _Attr_
// 请求的 context，记录请求级的参数
struct ContextData final {
 private:
  ContextData(const ContextData& other) = delete;
  ContextData& operator = (const ContextData& other) = delete;
  static const char * attr_names_[CommonIdx::MAX_ATTR_NUM];
  mutable std::array<ks::platform::ItemAttr*, CommonIdx::MAX_ATTR_NUM> attrs_;
  // 遗留结构
  ks::spdm::Context spdm_ctx;
  AdList ad_list;
  UniverseRequestMergeInfo universe_request_merge_info;  // Clear 问题
  // shared ptr
  std::shared_ptr<const ks::AbtestUserInfo> ab_user_info;  // 给代码中 abtestInstance 构建 user_info
  std::shared_ptr<engine_base::UniverseTinySearchBlackList> universe_tiny_search_black_list;
  std::shared_ptr<kconf::OuterNativeNegFilterRateConfig> photo_negative_tags;
  std::shared_ptr<absl::flat_hash_set<int64_t>> outer_native_diff_author_open_neg;
  std::shared_ptr<absl::flat_hash_set<std::string>> iap_ocpx_set;
  std::shared_ptr<UniverseTrafficControlContext> universe_traffic_control_context;

  bool lazy_init_attr_ = false;
  void LazyInitAttr(const CommonIdx &idx);

  bool change_context_shared_ptr_source_ = false;
  bool change_context_string_source_v2_ = false;
  bool change_universe_quality_data_source_ = false;

 public:
  // dragon commonattr, 读写分离, 建立 ContextData 和 dragon attr 之间的关系,
  // 1. 如果上下文中有 dragon, 请直接使用 dragon 的接口
  // 2. 如果上下文中没有 dragon, 可使用 context 获取合适的 common_r_, common_w_ 进行读写
  ks::platform::MutableRecoContextInterface* common_w_ = nullptr;
  ks::platform::ReadableRecoContextInterface* common_r_ = nullptr;
  ks::platform::ItemAttr& Attr(const CommonIdx &idx) const {
    if (attrs_[idx] == nullptr) {
      // 暂不考虑线程安全, 认为这是在单线程使用的
      attrs_[idx] = common_w_->GetCommonAttrAccessor(attr_names_[idx]);
      // 沙雕了, 大量 get 函数是 const 的, 所以必须保持函数的 const 属性
      const_cast<ContextData*>(this)->LazyInitAttr(idx);
    }
    return *attrs_[idx];
  }

  bool is_dragon_context_initialized_ = false;

  #include "context_data-context_data.inl"  // NOLINT
  #include "context_data-resource_white_box_info.inl"  // NOLINT
  #include "context_data-universe_data.inl"  // NOLINT
  // ------ 原变量都放下面 ----------------------------------------------
  std::unique_ptr<ks::ad_base::Dot> dot_perf;  // 暂不 attr

 public:
  ContextData();
  ~ContextData();

  void Initialize(const kuaishou::ad::FrontServerRequest& front_server_request,
                  const std::string& peer_info,
                  std::shared_ptr<UniverseTrafficControlContext>&& utcc,
                  AdFrontProcessPrepareMixer* init_mixer,
                  ks::platform::AddibleRecoContextInterface* mix_context);
  void InitializeDragonContext(ks::platform::AddibleRecoContextInterface* context);
  void InitializeAfterExtDeps(ks::platform::AddibleRecoContextInterface* mix_context);

 public:
  // TODO(jiangyuzhen03): 后续考虑类似地把 attr 交互封装一下
  inline void SetBoolCommonAttr(const std::string& key, bool val) {
    common_w_->SetIntCommonAttr(key, val ? 1 : 0, false, false);
  }

  inline bool GetBoolCommonAttr(const std::string& key, bool _default = false) const {
    return common_r_->GetIntCommonAttr(key).value_or(_default ? 1 : 0) == 0 ? false : true;
  }

  template<typename MessageType>
  bool GetPbListCommonAttr(const std::string& key, std::vector<MessageType>* dest) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(dest, false);
    auto optional = common_r_->GetStringListCommonAttr(key);
    if (!optional.has_value()) {
      return false;
    }
    auto str_list = optional.value();
    dest->clear();
    dest->reserve(str_list.size());
    for (const auto& str : str_list) {
      dest->emplace_back();
      if (!dest->back().ParseFromString(std::string(str))) {
        dest->clear();
        return false;
      }
    }
    return true;
  }

  template<typename MessageType>
  bool GetPbListCommonAttr(const std::string& key, ::google::protobuf::RepeatedPtrField<MessageType>* dest) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(dest, false);
    auto optional = common_r_->GetStringListCommonAttr(key);
    if (!optional.has_value()) {
      return false;
    }
    auto str_list = optional.value();
    dest->Clear();
    for (const auto& str : str_list) {
      if (!dest->Add()->ParseFromString(std::string(str))) {
        dest->Clear();
        return false;
      }
    }
    return true;
  }

  template<typename MessageType>
  bool GetPbMessageCommonAttr(const std::string& key, MessageType* dest) {
    ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(dest, false);
    auto optional = common_r_->GetStringCommonAttr(key);
    if (!optional.has_value()) {
      return false;
    }
    return dest->ParseFromString(std::string(optional.value()));
  }

  template<typename EnumType>
  EnumType GetEnumCommonAttr(const std::string& key, EnumType default_value = static_cast<EnumType>(0)) {
    return static_cast<EnumType>(
        common_r_->GetIntCommonAttr(key).value_or(static_cast<int64_t>(default_value)));
  }

  template<typename KT, typename VT, template<typename, typename, typename...> typename MP>
  bool GetMapCommonAttr(const std::string& key, MP<KT, VT>* dest) {
    auto key_key = key + ".key";
    auto val_key = key + ".val";
    absl::Span<const KT> key_list =
      common_r_->GetListCommonAttr<KT>(key_key).value_or(absl::Span<const KT>());
    absl::Span<const VT> val_list =
      common_r_->GetListCommonAttr<VT>(key_key).value_or(absl::Span<const VT>());
    if (key_list.size() != val_list.size()) {
      return false;
    }
    for (int64_t i = 0; i < key_list.size(); ++i) {
      dest->insert({key_list[i], val_list[i]});
    }
    return true;
  }

  template<typename KT, typename VT, template<typename, typename, typename...> typename MP>
  void SetMapCommonAttr(const std::string& key, const MP<KT, VT>& mp) {
    auto key_key = key + ".key";
    auto val_key = key + ".val";
    std::vector<KT> key_list;
    std::vector<VT> val_list;
    for (const auto& [k, v] : mp) {
      key_list.push_back(k);
      val_list.push_back(v);
    }
    common_w_->SetListCommonAttr<KT>(key_key, std::move(key_list), false, false);
    common_w_->SetListCommonAttr<VT>(val_key, std::move(val_list), false, false);
  }

 private:
  // 一些基础数据需要留在 context data 的，先单独包一下
  void BaseDataInit(const kuaishou::ad::FrontServerRequest& front_server_request);
  void UniverseAbtestInitialize();
  bool UniversePreFilter(const kuaishou::ad::universe::AthenaAdRequest& request);
  bool WinRateModelParamsParse();
  bool GetAdCreativeIdFromKconf(int64_t* unit_id, int64_t* creative_id);
  bool IsColdStartNoAdReq() const;
  void InitWhiteCreativeList();
  int64_t GetAdWhiteCreativeID();
  bool GetUserPreviewAd();
  bool UniverseSkipAdmitFilter(const std::string& user_id, const std::string& oaid,
     const std::string& imei, const std::string& idfa, const std::string& device_id) const;
  void FillDeviceId();
  void FillUniverseAdRequestInfo();
  const ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode MediumParameterCheck() const;
  bool IsPreviewOrDebug();
  void SetAdServiceTimeout();
  void FillUniverseCpmBoundInfoV2();
  void FillUniverseUserSceneFreqV2();
  void AsyncRedisInit();
  void AsyncRedisPostProc();

  void UniverseInitializeAfterExtDeps();
  void AfterUniverseInitialize(ks::platform::AddibleRecoContextInterface* mix_context);
  void AfterBuildUserInfo();

 public:
  void SetInventoryPosInfo(const kuaishou::ad::AdRequest &ad_request);

  bool IsUgMonopolizeFlow() const;
  FrontServerScene GetUnifyScene() const;
  bool PreFilter(const FrontServerRequest& request);
  bool IsTestRequest() const;
  void RecordHardLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition);
  void RecordSoftLastCondition(kuaishou::log::ad::AdTraceFilterCondition condition);

  bool EnableSimplifyAlwaysLog() const;
  // 计算广告位 & rank 对部分长时间不看广告用户 cpm 门槛打折 使用
  int64_t GetBrowseAdIntervalSecs(const kuaishou::ad::AdRequest &ad_request);

  bool IsSkipCommonAdmit() const;
  void CrowdStrategyDot(const std::string& stage);
  void GetLastPvAdInfo(const AdRequest& ad_request,
                       const bool enable_dynamic_pos,
                       kuaishou::ad::UserLastPvInfo* last_pv_info);

  void Clear();

  void Build(const ::kuaishou::ad::universe::AthenaAdRequest& athena_ad_request);

  // 公域单列混排流量
  int GetFanstopInvalidFlag() const;
  int GetAdInvalidFlag() const;
  void SetAdInvalidFlag(int val);
  void SetPassPreFilter(bool val);
  bool GetPassPreFilter() const;
  void SetRankMigrationSwitches();

  static bool GetAppId(const kuaishou::ad::AthenaFollowRequest& request, std::string* app_id) {
    if (request.product() == Product::KUAISHOU) {
      *app_id = "kuaishou";
    } else if (request.product() == Product::KUAISHOU_NEBULA) {
      *app_id = "kuaishou_nebula";
    } else {
      falcon::Inc("front_server.failed_to_get_app_id");
      return false;
    }
    return true;
  }

 public:
  bool PassKspGroupCheck(const FrontServerRequest& request);
  bool IsUnionPlayableExpand();

  void FillAdRequestExtInfo();

  void LogInfo(const FrontServerResponse& front_response);
  // 添加额外参数，防止 fanstop 调用此函数覆盖信息流的数据
  // 后续如果 tag 不一样可能需要重构
  void ParseRankResult(const AdResponse *ad_response, bool overwrite_tag = true);

  kuaishou::ad::FrontRequestType RequestType() const {
      return get_front_server_request()->type();
  }

  std::string RequestShortDebugString() const {
    return get_front_server_request()->ShortDebugString();
  }

  const bool is_closure_ad(kuaishou::ad::AdEnum::CampaignType campaign_type,
                           kuaishou::ad::AdActionType ocpx_action_type) const {
    static absl::flat_hash_set<int32_t> campaign_types{
      kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE
    };
    static absl::flat_hash_set<int32_t> ocpx_action_types{
      kuaishou::ad::EVENT_ORDER_PAIED,
      kuaishou::ad::AD_MERCHANT_ROAS,
      kuaishou::ad::AD_MERCHANT_T7_ROI,
      kuaishou::ad::AD_STOREWIDE_ROAS
    };
    return campaign_types.count(campaign_type) > 0 &&
           ocpx_action_types.count(ocpx_action_type) > 0;
  }

  const kuaishou::ad::AthenaExploreRequest& ExploreRequest() const {
    return get_front_server_request()->explore_request();
  }

  const kuaishou::ad::AthenaFollowRequest& FollowRequest() const {
    return get_front_server_request()->follow_request();
  }

  const kuaishou::ad::AthenaNearbyRequest& NearbyRequest() const {
    return get_front_server_request()->nearby_request();
  }

  const kuaishou::ad::universe::AthenaAdRequest& UniverseRequest() const {
    return get_front_server_request()->universe_request();
  }

  const std::string TabName() const {
    return copy_tab_name();
  }

  // Initialize 之后才可以用
  google::protobuf::Arena* GetArena() {
    return mutable_arena();
  }

  kuaishou::ad::ServiceDeployName GetDeployName() const {
    return kuaishou::ad::ServiceDeployName::AD_FRONT_UNIVERSE;
  }

  // 模板实现必须在头文件中
  template<typename MixerType>
  void InitializeBeforeExtDeps(const kuaishou::ad::FrontServerRequest& front_server_request,
      std::shared_ptr<UniverseTrafficControlContext>&& utcc,
      MixerType* mixer,
      ks::platform::AddibleRecoContextInterface* mix_context) {
    set_front_server_request(const_cast<FrontServerRequest*>(&front_server_request));
    auto *arena = front_server_request.GetArena();
    set_arena(arena);
    using google::protobuf::Arena;
    // 在 front_grpc.cc 中申请好了
    BaseDataInit(front_server_request);
    set_ad_pack_request(Arena::CreateMessage<kuaishou::ad::AdPackRequest>(arena));

    // time
    auto time_now = absl::Now();
    *mutable_bd() = time_now.In(absl::LocalTimeZone());
    // front 接收到的所有请求打点
    dot_perf->Count(1, "front_request_num_before_filter",
                    GetTabName(get_front_server_request()->type()));
    if (get_byte_size_dot()) {
      dot_perf->Interval(get_front_server_request()->ByteSize(), "front_server_request_size",
                         kuaishou::ad::AdEnum::AdRequestFlowType_Name(front_server_request.flow_type()));
    }

    Build(front_server_request.universe_request());
    AdRequest* tmp_ad_request = nullptr;
    set_req_adapter_code(RequestAdapter::BuildAdRequest(mutable_front_server_request(), &tmp_ad_request,
      arena, GetUnifyScene()));
    set_ad_request(tmp_ad_request);
    // 嵌入式 flow
    if (SPDM_universeContextInitFilterUpdate()) {
      // prepare
      bool universe_imp_info_empty = get_ud_universe_imp_info().empty();
      mix_context->SetIntCommonAttr("universe_imp_info_empty", universe_imp_info_empty);

      std::string pipeline_name = mixer->GetEmbeddingPipeline(mix_context, "embedded_pipeline");
      if (pipeline_name.empty() ||
          !mixer->EmbeddedPipelineExecute(mix_context, pipeline_name)) {
        // flow 执行失败 但未设置 context_init_error 时 return 会导致 core 先保护下
        if (!get_context_init_error()) {
          dot_perf->Count(1, "new_context_init_exit");
          set_context_init_error(true);
        }
        return;
      }
    } else {
      // 此处做 PreFilter,避免后续无效初始化
      set_pass_pre_filter(PreFilter(front_server_request));
      if (!get_pass_pre_filter()) {
        set_context_init_error(true);
        dot_perf->Count(1, "pass_pre_filter_num");
        return;
      }

      // 目前有部分流量打错机器，在这里判断流量是否在对的机器上\
      // 不符合的请求在初始化之前过滤掉
      if (!PassKspGroupCheck(front_server_request)) {
        set_context_init_error(true);
        set_pass_pre_filter(false);
        dot_perf->Count(1, "ksp_group_check_filter", "total");
        return;
      }
      if (get_req_adapter_code() != ReqAdapterCode::kAdapterSuccess) {
        set_context_init_error(true);
        set_pass_pre_filter(false);
        dot_perf->Count(1, "ad_request_adapter_error", absl::StrCat(get_req_adapter_code()));
        return;
      }
    }
    local_llsid = get_llsid();
    set_tab_name(GetTabName(get_front_server_request()->type()));
    // 把 message 都托管到 arena 上设置到 attr 里面
#define REGISTER_ARENA_ATTR(type, attr) \
      Attr(CommonIdx::attr).SetPtrValue(0, Arena::CreateMessage<type>(arena))
    REGISTER_ARENA_ATTR(AdResponse, ad_response);
    REGISTER_ARENA_ATTR(GetStyleInfoReq, style_info_req);
    REGISTER_ARENA_ATTR(GetStyleInfoResp, style_info_resp);
#undef REGISTER_ARENA_ATTR

    set_start_time_ns(absl::GetCurrentTimeNanos());
    set_start_time_us(base::GetTimestamp());

    set_universe_traffic_control_context(std::move(utcc));
    mix_context->SetIntCommonAttr("utcc_cached_ranking_cache_data_not_null",
                  get_universe_traffic_control_context()->cached_ranking_cache_data != nullptr);
    FillDeviceId();  // 放在画像前面，画像成功会将该字段覆盖。主要用于画像失败的情况，降低对画像的依赖
  }
};

struct TimeRecorder {
  TimeRecorder(ContextData* session_data, int32 node_type) {
    enter_time_ = base::GetTimestamp();
    session_data_ = session_data;
    time_cost_.set_node_type(node_type);
  }
  ~TimeRecorder() {
    if (session_data_) {
      time_cost_.set_time_cost_ms((base::GetTimestamp() - enter_time_) / 1000);
      time_cost_.set_time_enter_ms((enter_time_ - session_data_->get_start_ts()) / 1000);
      session_data_->mutable_node_time_cost()->push_back(time_cost_);
      session_data_->dot_perf->Interval(time_cost_.time_cost_ms(),
          "front_node_cost_time", kuaishou::ad::AdFrontNodeType_Name(time_cost_.node_type()),
          session_data_->get_ud_is_universe_tiny_flow() ? "tiny" : "normal");
      session_data_->dot_perf->Interval(time_cost_.time_enter_ms(),
          "front_node_enter_cost_time", kuaishou::ad::AdFrontNodeType_Name(time_cost_.node_type()),
          session_data_->get_ud_is_universe_tiny_flow() ? "tiny" : "normal");
      time_cost_.Clear();
    }
    session_data_ = nullptr;
  }
  int64_t enter_time_ = 0;
  ContextData* session_data_ = nullptr;
  kuaishou::ad::PerfInfo::NodeTimeCost time_cost_;
};

}  // namespace front_server
}  // namespace ks
