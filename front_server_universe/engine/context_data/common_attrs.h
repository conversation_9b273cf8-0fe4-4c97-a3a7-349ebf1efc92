#pragma once
// cat skydome/schema_manager/auto_gen_context_data.py|grep :|awk -F'"' '{print "_Attr_("$2"), \\"}' >> engine/context_data/common_attrs.h  // NOLINT
#define ALL_COMMON_ATTRS \
  _Attr_(front_server_request), \
  _Attr_(front_server_response), \
  _Attr_(ad_pack_request), \
  _Attr_(for_test), \
  _Attr_(debug_data), \
  _Attr_(is_ios_platform), \
  _Attr_(pass_pre_filter), \
  _Attr_(context_init_error), \
  _Attr_(app_version), \
  _Attr_(trans_pack_info), \
  _Attr_(last_filter_condition), \
  _Attr_(soft_last_filter_condition), \
  _Attr_(llsid), \
  _Attr_(user_id), \
  _Attr_(abtest_user_id), \
  _Attr_(abtest_device_id), \
  _Attr_(device_id), \
  _Attr_(device_id_hash_key), \
  _Attr_(start_time_ns), \
  _Attr_(start_time_us), \
  _Attr_(start_ts), \
  _Attr_(enable_detail_cpm_kafka), \
  _Attr_(enable_detail_cpm_timeout_kafka), \
  _Attr_(enable_detail_cpm_uplift_kafka), \
  _Attr_(enable_win_rate_kafka), \
  _Attr_(enable_use_full_computing_power), \
  _Attr_(enable_union_ltr_sample), \
  _Attr_(union_ltr_sample_tag), \
  _Attr_(should_be_filtered_by_cpm_thres), \
  _Attr_(universe_skip_filtered_by_cpm_thres_type), \
  _Attr_(enable_search_brand_one_ad_list), \
  _Attr_(enable_combo_search_req_ad_pack), \
  _Attr_(universe_request_merge_info), \
  _Attr_(has_retrieve_dsp), \
  _Attr_(jinniu_branch_test), \
  _Attr_(req_adapter_code), \
  _Attr_(is_trace_api_detection), \
  _Attr_(app_id), \
  _Attr_(request_inventory_pos_id), \
  _Attr_(enable_kxy_subsidy_global_nc), \
  _Attr_(abtest_mapping_id), \
  _Attr_(is_support_gyroscope), \
  _Attr_(ad_request), \
  _Attr_(ad_response), \
  _Attr_(style_info_req), \
  _Attr_(style_info_resp), \
  _Attr_(rta_ads_stra_start), \
  _Attr_(fake_user_type), \
  _Attr_(ac_fetcher_type), \
  _Attr_(preview_creative_id), \
  _Attr_(user_preview_info), \
  _Attr_(is_new_preview_logic), \
  _Attr_(preview_type), \
  _Attr_(ad_strateg_tag_map), \
  _Attr_(ad_rank_pass_through), \
  _Attr_(rank_2_front_info), \
  _Attr_(trace_always_log), \
  _Attr_(simplify_always_log), \
  _Attr_(spdm_ctx), \
  _Attr_(retrieval_handler_list), \
  _Attr_(forward_handler_common), \
  _Attr_(universe_antispam_from_api), \
  _Attr_(bd), \
  _Attr_(predict_request), \
  _Attr_(predict_response), \
  _Attr_(rta_req_filter_reason), \
  _Attr_(detail_used_item), \
  _Attr_(union_ltr_used_item), \
  _Attr_(used_item), \
  _Attr_(new_creative_used_item), \
  _Attr_(style_material_map), \
  _Attr_(style_material_land_page_map), \
  _Attr_(style_data_for_online_join), \
  _Attr_(is_unlogin_user), \
  _Attr_(force_direct_call_cids), \
  _Attr_(universe), \
  _Attr_(enable_quick_search_skip_budget_control_universe), \
  _Attr_(enable_direct_search_skip_budget_control_universe), \
  _Attr_(pos_manager), \
  _Attr_(is_pv_recalled), \
  _Attr_(pv_recalled_reason), \
  _Attr_(byte_size_dot), \
  _Attr_(creative_id_2_idx), \
  _Attr_(enable_rank_pass_through_v2), \
  _Attr_(ad_price), \
  _Attr_(grid_unit_id), \
  _Attr_(test_device_id), \
  _Attr_(media_wx_small_app_id), \
  _Attr_(is_search_debug_white_list), \
  _Attr_(page_id), \
  _Attr_(sub_page_id), \
  _Attr_(universe_live_reserve_uplift_exp_author_id), \
  _Attr_(universe_inner_filter_uplift_tag), \
  _Attr_(low_quality_ads_filter_tag), \
  _Attr_(ad_klog), \
  _Attr_(need_outer_retrieval), \
  _Attr_(antispam_code), \
  _Attr_(antispam_ext), \
  _Attr_(universe_predict_cpm_threshold), \
  _Attr_(universe_rtb_coff), \
  _Attr_(work_flow_type), \
  _Attr_(universe_win_rate_model_param_map), \
  _Attr_(is_universe_cached_res), \
  _Attr_(is_model_predict_failure), \
  _Attr_(is_universe_inner_loop_admit_traffic), \
  _Attr_(traffic_marking), \
  _Attr_(universe_traffic_types), \
  _Attr_(medium_valid_err_code), \
  _Attr_(universe_quality_data), \
  _Attr_(universe_adn_profit_info), \
  _Attr_(ad_list), \
  _Attr_(enable_cache_for_leveled_traffic), \
  _Attr_(traffic_level), \
  _Attr_(enable_wanhe_charge_action_type_subpage_id), \
  _Attr_(enable_wanhe_charge_action_type_with_fanstop), \
  _Attr_(ad_select_stage_infos), \
  _Attr_(ad_perf_info), \
  _Attr_(follow_ids), \
  _Attr_(curr_ad_data_v2), \
  _Attr_(is_universe_rtb_flow), \
  _Attr_(is_universe_adn_flow), \
  _Attr_(enable_universe_fix_elastic_idx), \
  _Attr_(perf_ab_param_bool), \
  _Attr_(tab_name), \
  _Attr_(node_time_cost), \
  _Attr_(book_id), \
  _Attr_(has_ad_credict_user_score_redis), \
  _Attr_(is_multi_quota_flow), \
  _Attr_(enable_inspire_style_form), \
  _Attr_(is_honor_device), \
  _Attr_(print_factor_info_random_num), \
  _Attr_(enable_print_all_factor_info), \
  _Attr_(formula_info), \
  _Attr_(cache_origin_llsid), \
  _Attr_(enable_fill_cache_origin_llsid), \
  _Attr_(degrade_level), \
  _Attr_(arena), \
  _Attr_(service_deploy_name), \
  _Attr_(jk_response), \
  _Attr_(white_box_flow_tag), \
  _Attr_(ad_monitor_type_set), \
  _Attr_(universe_predict_cpm), \
  _Attr_(universe_predict_cpm_for_grade_quality), \
  _Attr_(universe_predict_cpm_for_traffic_type), \
  _Attr_(universe_enable_log_predict_cpm), \
  _Attr_(universe_enable_log_predict_cpm_grade_quality), \
  _Attr_(universe_predict_cpm_threshold_new), \
  _Attr_(universe_predict_cpm_threshold_old), \
  _Attr_(universe_predict_cpm_threshold_for_traffic_type), \
  _Attr_(universe_predict_cpm_threshold_for_traffic_type_old), \
  _Attr_(universe_filtered_by_cpm_threshold), \
  _Attr_(universe_filtered_by_cpm_threshold_old), \
  _Attr_(universe_filtered_by_cpm_threshold_for_traffic_type), \
  _Attr_(universe_filtered_by_cpm_threshold_for_traffic_type_old), \
  _Attr_(universe_pid_solver_group_index), \
  _Attr_(universe_pid_solver_explore_group_index), \
  _Attr_(medium_industry_id_v2), \
  _Attr_(medium_sub_industry_id_v2), \
  _Attr_(universe_pid_solver_tp_ratio), \
  _Attr_(universe_timeout), \
  _Attr_(universe_log_cpr_ab_group), \
  _Attr_(dynamic_traffic_grade_level_ratio), \
  _Attr_(item_type_set), \
  _Attr_(universe_cpm_bound_params), \
  _Attr_(universe_user_scene_freq_params), \
  _Attr_(async_redis_helper), \
  _Attr_(universe_tiny_search_black_list), \
  _Attr_(photo_negative_tags), \
  _Attr_(outer_native_diff_author_open_neg), \
  _Attr_(iap_ocpx_set), \
  _Attr_(universe_traffic_control_context), \
  _Attr_(ud_sdk_version), \
  _Attr_(ud_sdk_api_version), \
  _Attr_(ud_tk_version), \
  _Attr_(ud_protocol_version), \
  _Attr_(ud_app_id), \
  _Attr_(ud_is_sdk_flow), \
  _Attr_(ud_pos_id), \
  _Attr_(ud_physical_pos_id), \
  _Attr_(ud_ad_style), \
  _Attr_(ud_cooperation_mode), \
  _Attr_(ud_cpm_bid_floor), \
  _Attr_(ud_position_cpm_floor), \
  _Attr_(ud_client_id), \
  _Attr_(ud_universe_imp_info), \
  _Attr_(ud_universe_ext), \
  _Attr_(ud_creative_material_types), \
  _Attr_(ud_app_set), \
  _Attr_(ud_pkg_white_list), \
  _Attr_(ud_ks_version), \
  _Attr_(ud_nebula_version), \
  _Attr_(ud_is_installed_wx), \
  _Attr_(ud_res_adnum), \
  _Attr_(ud_p_athena_ad_request), \
  _Attr_(ud_sdk_exp_param), \
  _Attr_(ud_universe_elastic_info), \
  _Attr_(ud_play_card_new_style), \
  _Attr_(ud_play_card_compatible), \
  _Attr_(ud_play_card_content), \
  _Attr_(ud_end_card_id), \
  _Attr_(ud_style_server_ad_set), \
  _Attr_(ud_crowd_package_id_set), \
  _Attr_(ud_cpm_bound), \
  _Attr_(ud_target_cpm), \
  _Attr_(ud_org_target_cpm), \
  _Attr_(ud_universe_pos_cpm_bound), \
  _Attr_(ud_universe_pos_target_cpm), \
  _Attr_(ud_universe_pos_org_target_cpm), \
  _Attr_(ud_discount_config_), \
  _Attr_(ud_discount_valid_config), \
  _Attr_(ud_white_traffic_ids), \
  _Attr_(ud_white_traffic_ids_str), \
  _Attr_(ud_cache_abtest_key), \
  _Attr_(ud_dynamic_cpm_threshold_abtest_key), \
  _Attr_(ud_top_pos_id), \
  _Attr_(ud_top_pos_cpm_floor), \
  _Attr_(ud_is_top_pos), \
  _Attr_(ud_phy_pos_sign), \
  _Attr_(ud_cache_key), \
  _Attr_(ud_universe_flow_explore_type), \
  _Attr_(ud_hold_and_cache), \
  _Attr_(ud_write_in_cache), \
  _Attr_(ud_p2live_to_live_cids), \
  _Attr_(ud_wechat_app_id), \
  _Attr_(ud_hit_phone_brand), \
  _Attr_(ud_is_universe_aggregation_scene), \
  _Attr_(ud_universe_custom_agg_scene), \
  _Attr_(ud_is_app_store_flow), \
  _Attr_(ud_app_distribute_type), \
  _Attr_(ud_search_query), \
  _Attr_(ud_is_universe_tiny_flow), \
  _Attr_(ud_dot_pos_id), \
  _Attr_(ud_request_universe_tiny), \
  _Attr_(ud_universe_forced_impression_bonus), \
  _Attr_(ud_universe_inner_hv_top1_explore_threshold_ratio), \
  _Attr_(ud_universe_inner_hv_top1_explore_cpm_ratio), \
  _Attr_(ud_inner_pv_allocation_strategy_ratio), \
  _Attr_(ud_pdd_second_price_param), \
  _Attr_(ud_universe_query_stat), \
  _Attr_(ud_universe_adx_fake_second_price_ratio), \
  _Attr_(ud_style_component_flag_for_preview), \
  _Attr_(ud_meet_componentized_precondition), \
  _Attr_(ud_componentized_exp_group), \
  _Attr_(ud_hit_componentized_monitor_exp_whitelist), \
  _Attr_(ud_universe_delivered_qpon_redis_admit), \
  _Attr_(ud_carousel_pv_admit), \
  _Attr_(ud_carousel_user_admit), \
  _Attr_(ud_carousel_admit), \
  _Attr_(ud_origin_ad_num), \
  _Attr_(ud_get_universe_delivered_qpon_info_success), \
  _Attr_(ud_universe_delivered_qpon_info_day), \
  _Attr_(ud_raw_max_retrieval_creatives), \
  _Attr_(ud_raw_max_prerank_num), \
  _Attr_(ud_raw_config_group_index), \
  _Attr_(ud_universe_bonus_control_pid_value), \
  _Attr_(ud_ad_rollout_size), \
  _Attr_(ud_rtb_ecpm_before_dynamic_share), \
  _Attr_(ud_rtb_virtual_cpm_before_dynamic_share), \
  _Attr_(ud_timeout_exp_info), \
  _Attr_(ud_win_rate_model_exp_info), \
  _Attr_(ud_dynamic_share_exp_info), \
  _Attr_(ud_origin_antispam_code), \
  _Attr_(ud_universe_browsed_freq_config_ids), \
  _Attr_(enable_price_precise_fix_pv), \
  _Attr_(universe_quality_data_flag), \
  _Attr_(universe_quality_data_fill_rate), \
  _Attr_(universe_quality_data_pos_post_cpm), \
  _Attr_(universe_quality_data_predict_cpm), \
  _Attr_(universe_quality_data_model_predict_cpm), \
  _Attr_(universe_quality_data_smart_compute_power_vector), \
  _Attr_(is_closure_flow),  \
  _Attr_(universe_quality_data_rtb_win_rate_model_params),  \
  _Attr_(fi_physical_pos_id), \
  _Attr_(fi_new_media_protection_pos_imp)
// !!! 最后一个不要逗号

namespace ks {
namespace front_server {
namespace common_attr {

const char fanstop_attr[] = "fanstop_attr";
const char rank_request_str[] = "ad_server_response.rank_request";
const char rank_request[] = "rank_request";
const char ad_server_rank_response[] = "ad_server_response.rank_response";
const char ad_server_jk_response[] = "ad_server_response.jk_response";
const char ad_server_item_table[] = "global_ad_table";
const char rank_response[] = "rank_response";
const char rank_response_str[] = "ad_rank_response";
const char ad_response_new[] = "ad_response_new";
const char jk_response[] = "jk_response";
const char enable_move_build_ad_response_to_front_diff[] = "enable_move_build_ad_response_to_front_diff";
const char enable_move_build_ad_response_to_front[] = "enable_move_build_ad_response_to_front";
const char enable_move_request_rank_to_front[] = "enable_move_request_rank_to_front";
const char enable_move_request_jk_to_front[] = "enable_move_request_jk_to_front";

// ad_response 构造上移透传补充字段
const char target_resp_version_info[] = "target_response.version_info";  // [x] StringList(::kuaishou::ad::AdServiceVersion)  //NOLINT
const char retrieval_target_unit_num[] = "session_data.retrieval_target_unit_num";  // [x] int64
const char auto_param_info[] = "session_data.auto_param_info";  // [x] String(kuaishou::ad::AutoParamTrace)
const char reco_user_info_str[] = "reco_user_info_str";
const char adx_req_admit_fail_reason[] = "adx_req_admit_fail_reason";
const char multi_debug_infos[] = "multi_debug_infos";
const char retrieval_selected_unit_num[] = "retrieval_selected_unit_num";
const char retrieval_ad_count[] = "retrieval_ad_count";

const char merchant_retrieval_target_unit_num[] = "merchant_retrieval_target_unit_num";
const char merchant_retrieval_selected_unit_num[] = "merchant_retrieval_selected_unit_num";
const char merchant_retrieval_ad_count[] = "merchant_retrieval_ad_count";

const char fake_flag[] = "fake_flag";
const char multi_retrieval_info[] = "multi_retrieval_info";
const char invalid_flag[] = "invalid_flag";
const char final_config_group_index[] = "final_config_group_index";

}  // namespace common_attr

}  // namespace front_server
}  // namespace ks
