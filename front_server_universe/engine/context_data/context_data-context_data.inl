// auto gen private member 361 for ContextData

inline absl::flat_hash_map<int64_t, int64_t >* mutable_ad_strateg_tag_map() {
  return Attr(CommonIdx::ad_strateg_tag_map).GetMutablePtrValue<absl::flat_hash_map<int64_t, int64_t >>();
}
inline const absl::flat_hash_map<int64_t, int64_t >& get_ad_strateg_tag_map() const {
  return *Attr(CommonIdx::ad_strateg_tag_map).GetPtrValue<absl::flat_hash_map<int64_t, int64_t >>();
}

inline absl::flat_hash_map<std::string, std::string >* mutable_formula_info() {
  return Attr(CommonIdx::formula_info).GetMutablePtrValue<absl::flat_hash_map<std::string, std::string >>();
}
inline const absl::flat_hash_map<std::string, std::string >& get_formula_info() const {
  return *Attr(CommonIdx::formula_info).GetPtrValue<absl::flat_hash_map<std::string, std::string >>();
}

inline AsyncRedisHelper* mutable_async_redis_helper() {
  return Attr(CommonIdx::async_redis_helper).GetMutablePtrValue<AsyncRedisHelper>();
}
inline const AsyncRedisHelper& get_async_redis_helper() const {
  return *Attr(CommonIdx::async_redis_helper).GetPtrValue<AsyncRedisHelper>();
}
inline UniverseCpmBoundParams* mutable_universe_cpm_bound_params() {
  return Attr(CommonIdx::universe_cpm_bound_params).GetMutablePtrValue<UniverseCpmBoundParams>();
}
inline const UniverseCpmBoundParams& get_universe_cpm_bound_params() const {
  return *Attr(CommonIdx::universe_cpm_bound_params).GetPtrValue<UniverseCpmBoundParams>();
}
inline UniverseUserSceneFreqParams* mutable_universe_user_scene_freq_params() {
  return Attr(CommonIdx::universe_user_scene_freq_params).GetMutablePtrValue<UniverseUserSceneFreqParams>();
}
inline const UniverseUserSceneFreqParams& get_universe_user_scene_freq_params() const {
  return *Attr(CommonIdx::universe_user_scene_freq_params).GetPtrValue<UniverseUserSceneFreqParams>();
}

inline absl::Time::Breakdown* mutable_bd() {
  return Attr(CommonIdx::bd).GetMutablePtrValue<absl::Time::Breakdown>();
}
inline const absl::Time::Breakdown& get_bd() const {
  return *Attr(CommonIdx::bd).GetPtrValue<absl::Time::Breakdown>();
}

inline AdCreativePreview* mutable_user_preview_info() {
  return Attr(CommonIdx::user_preview_info).GetMutablePtrValue<AdCreativePreview>();
}
inline const AdCreativePreview& get_user_preview_info() const {
  return *Attr(CommonIdx::user_preview_info).GetPtrValue<AdCreativePreview>();
}

inline AdFrontStageInfoLog* mutable_ad_select_stage_infos() {
  return Attr(CommonIdx::ad_select_stage_infos).GetMutablePtrValue<AdFrontStageInfoLog>();
}
inline const AdFrontStageInfoLog& get_ad_select_stage_infos() const {
  return *Attr(CommonIdx::ad_select_stage_infos).GetPtrValue<AdFrontStageInfoLog>();
}

inline AdList* mutable_ad_list() {
  return Attr(CommonIdx::ad_list).GetMutablePtrValue<AdList>();
}
inline const AdList& get_ad_list() const {
  return *Attr(CommonIdx::ad_list).GetPtrValue<AdList>();
}

inline AdPerfInfoLog* mutable_ad_perf_info() {
  return Attr(CommonIdx::ad_perf_info).GetMutablePtrValue<AdPerfInfoLog>();
}
inline const AdPerfInfoLog& get_ad_perf_info() const {
  return *Attr(CommonIdx::ad_perf_info).GetPtrValue<AdPerfInfoLog>();
}

// ad_request just declare funcs, impl in cc.inl
void set_ad_request(AdRequest * v);
AdRequest * mutable_ad_request() const ;
const AdRequest * get_ad_request() const;

// ad_response just declare funcs, impl in cc.inl
AdResponse * mutable_ad_response() const ;
const AdResponse * get_ad_response() const;

inline void set_byte_size_dot(bool v) {
  Attr(CommonIdx::byte_size_dot).SetIntValue(0, v, false, false);
}
inline bool get_byte_size_dot() const {
  return Attr(CommonIdx::byte_size_dot).GetIntValue().value_or(false);
}

inline void set_is_closure_flow(bool v) {
  Attr(CommonIdx::is_closure_flow).SetIntValue(0, v, false, false);
}

inline bool get_is_closure_flow() const {
  return Attr(CommonIdx::is_closure_flow).GetIntValue().value_or(false);
}

inline void set_context_init_error(bool v) {
  Attr(CommonIdx::context_init_error).SetIntValue(0, v, false, false);
}
inline bool get_context_init_error() const {
  return Attr(CommonIdx::context_init_error).GetIntValue().value_or(false);
}
inline void set_enable_cache_for_leveled_traffic(bool v) {
  Attr(CommonIdx::enable_cache_for_leveled_traffic).SetIntValue(0, v, false, false);
}
inline bool get_enable_cache_for_leveled_traffic() const {
  return Attr(CommonIdx::enable_cache_for_leveled_traffic).GetIntValue().value_or(0);
}

inline void set_enable_combo_search_req_ad_pack(bool v) {
  Attr(CommonIdx::enable_combo_search_req_ad_pack).SetIntValue(0, v, false, false);
}
inline bool get_enable_combo_search_req_ad_pack() const {
  return Attr(CommonIdx::enable_combo_search_req_ad_pack).GetIntValue().value_or(false);
}

inline void set_enable_detail_cpm_kafka(bool v) {
  Attr(CommonIdx::enable_detail_cpm_kafka).SetIntValue(0, v, false, false);
}
inline bool get_enable_detail_cpm_kafka() const {
  return Attr(CommonIdx::enable_detail_cpm_kafka).GetIntValue().value_or(false);
}

inline void set_enable_win_rate_kafka(bool v) {
  Attr(CommonIdx::enable_win_rate_kafka).SetIntValue(0, v, false, false);
}
inline bool get_enable_win_rate_kafka() const {
  return Attr(CommonIdx::enable_win_rate_kafka).GetIntValue().value_or(false);
}

inline void set_enable_detail_cpm_timeout_kafka(bool v) {
  Attr(CommonIdx::enable_detail_cpm_timeout_kafka).SetIntValue(0, v, false, false);
}
inline bool get_enable_detail_cpm_timeout_kafka() const {
  return Attr(CommonIdx::enable_detail_cpm_timeout_kafka).GetIntValue().value_or(false);
}

inline void set_enable_detail_cpm_uplift_kafka(bool v) {
  Attr(CommonIdx::enable_detail_cpm_uplift_kafka).SetIntValue(0, v, false, false);
}
inline bool get_enable_detail_cpm_uplift_kafka() const {
  return Attr(CommonIdx::enable_detail_cpm_uplift_kafka).GetIntValue().value_or(false);
}

inline void set_enable_use_full_computing_power(bool v) {
  Attr(CommonIdx::enable_use_full_computing_power).SetIntValue(0, v, false, false);
}
inline bool get_enable_use_full_computing_power() const {
  return Attr(CommonIdx::enable_use_full_computing_power).GetIntValue().value_or(false);
}

inline void set_enable_direct_search_skip_budget_control_universe(bool v) {
  Attr(CommonIdx::enable_direct_search_skip_budget_control_universe).SetIntValue(0, v, false, false);
}
inline bool get_enable_direct_search_skip_budget_control_universe() const {
  return Attr(CommonIdx::enable_direct_search_skip_budget_control_universe).GetIntValue().value_or(false);
}

inline void set_enable_fill_cache_origin_llsid(bool v) {
  Attr(CommonIdx::enable_fill_cache_origin_llsid).SetIntValue(0, v, false, false);
}
inline bool get_enable_fill_cache_origin_llsid() const {
  return Attr(CommonIdx::enable_fill_cache_origin_llsid).GetIntValue().value_or(false);
}

inline void set_enable_inspire_style_form(bool v) {
  Attr(CommonIdx::enable_inspire_style_form).SetIntValue(0, v, false, false);
}
inline bool get_enable_inspire_style_form() const {
  return Attr(CommonIdx::enable_inspire_style_form).GetIntValue().value_or(false);
}

inline void set_enable_kxy_subsidy_global_nc(bool v) {
  Attr(CommonIdx::enable_kxy_subsidy_global_nc).SetIntValue(0, v, false, false);
}
inline bool get_enable_kxy_subsidy_global_nc() const {
  return Attr(CommonIdx::enable_kxy_subsidy_global_nc).GetIntValue().value_or(false);
}

inline void set_enable_print_all_factor_info(bool v) {
  Attr(CommonIdx::enable_print_all_factor_info).SetIntValue(0, v, false, false);
}
inline bool get_enable_print_all_factor_info() const {
  return Attr(CommonIdx::enable_print_all_factor_info).GetIntValue().value_or(false);
}
inline void set_enable_quick_search_skip_budget_control_universe(bool v) {
  Attr(CommonIdx::enable_quick_search_skip_budget_control_universe).SetIntValue(0, v, false, false);
}
inline bool get_enable_quick_search_skip_budget_control_universe() const {
  return Attr(CommonIdx::enable_quick_search_skip_budget_control_universe).GetIntValue().value_or(false);
}

inline void set_enable_rank_pass_through_v2(bool v) {
  Attr(CommonIdx::enable_rank_pass_through_v2).SetIntValue(0, v, false, false);
}
inline bool get_enable_rank_pass_through_v2() const {
  return Attr(CommonIdx::enable_rank_pass_through_v2).GetIntValue().value_or(false);
}

inline void set_enable_search_brand_one_ad_list(bool v) {
  Attr(CommonIdx::enable_search_brand_one_ad_list).SetIntValue(0, v, false, false);
}
inline bool get_enable_search_brand_one_ad_list() const {
  return Attr(CommonIdx::enable_search_brand_one_ad_list).GetIntValue().value_or(false);
}

inline void set_enable_union_ltr_sample(bool v) {
  Attr(CommonIdx::enable_union_ltr_sample).SetIntValue(0, v, false, false);
}
inline bool get_enable_union_ltr_sample() const {
  return Attr(CommonIdx::enable_union_ltr_sample).GetIntValue().value_or(false);
}

inline void set_union_ltr_sample_tag(int v) {
  Attr(CommonIdx::union_ltr_sample_tag).SetIntValue(0, v, false, false);
}
inline int get_union_ltr_sample_tag() const {
  return Attr(CommonIdx::union_ltr_sample_tag).GetIntValue().value_or(0);
}

inline void set_enable_universe_fix_elastic_idx(bool v) {
  Attr(CommonIdx::enable_universe_fix_elastic_idx).SetIntValue(0, v, false, false);
}
inline bool get_enable_universe_fix_elastic_idx() const {
  return Attr(CommonIdx::enable_universe_fix_elastic_idx).GetIntValue().value_or(false);
}

inline void set_enable_wanhe_charge_action_type_subpage_id(bool v) {
  Attr(CommonIdx::enable_wanhe_charge_action_type_subpage_id).SetIntValue(0, v, false, false);
}
inline bool get_enable_wanhe_charge_action_type_subpage_id() const {
  return Attr(CommonIdx::enable_wanhe_charge_action_type_subpage_id).GetIntValue().value_or(false);
}

inline void set_enable_wanhe_charge_action_type_with_fanstop(bool v) {
  Attr(CommonIdx::enable_wanhe_charge_action_type_with_fanstop).SetIntValue(0, v, false, false);
}
inline bool get_enable_wanhe_charge_action_type_with_fanstop() const {
  return Attr(CommonIdx::enable_wanhe_charge_action_type_with_fanstop).GetIntValue().value_or(false);
}

inline void set_for_test(bool v) {
  Attr(CommonIdx::for_test).SetIntValue(0, v, false, false);
}
inline bool get_for_test() const {
  return Attr(CommonIdx::for_test).GetIntValue().value_or(0);
}

inline void set_has_ad_credict_user_score_redis(bool v) {
  Attr(CommonIdx::has_ad_credict_user_score_redis).SetIntValue(0, v, false, false);
}
inline bool get_has_ad_credict_user_score_redis() const {
  return Attr(CommonIdx::has_ad_credict_user_score_redis).GetIntValue().value_or(false);
}

inline void set_has_retrieve_dsp(bool v) {
  Attr(CommonIdx::has_retrieve_dsp).SetIntValue(0, v, false, false);
}
inline bool get_has_retrieve_dsp() const {
  return Attr(CommonIdx::has_retrieve_dsp).GetIntValue().value_or(false);
}

inline void set_is_honor_device(bool v) {
  Attr(CommonIdx::is_honor_device).SetIntValue(0, v, false, false);
}
inline bool get_is_honor_device() const {
  return Attr(CommonIdx::is_honor_device).GetIntValue().value_or(false);
}

inline void set_is_ios_platform(bool v) {
  Attr(CommonIdx::is_ios_platform).SetIntValue(0, v, false, false);
}
inline bool get_is_ios_platform() const {
  return Attr(CommonIdx::is_ios_platform).GetIntValue().value_or(false);
}

inline void set_is_multi_quota_flow(bool v) {
  Attr(CommonIdx::is_multi_quota_flow).SetIntValue(0, v, false, false);
}
inline bool get_is_multi_quota_flow() const {
  return Attr(CommonIdx::is_multi_quota_flow).GetIntValue().value_or(false);
}

inline void set_is_new_preview_logic(bool v) {
  Attr(CommonIdx::is_new_preview_logic).SetIntValue(0, v, false, false);
}
inline bool get_is_new_preview_logic() const {
  return Attr(CommonIdx::is_new_preview_logic).GetIntValue().value_or(false);
}
inline void set_is_pv_recalled(bool v) {
  Attr(CommonIdx::is_pv_recalled).SetIntValue(0, v, false, false);
}
inline bool get_is_pv_recalled() const {
  return Attr(CommonIdx::is_pv_recalled).GetIntValue().value_or(false);
}

inline void set_is_search_debug_white_list(bool v) {
  Attr(CommonIdx::is_search_debug_white_list).SetIntValue(0, v, false, false);
}
inline bool get_is_search_debug_white_list() const {
  return Attr(CommonIdx::is_search_debug_white_list).GetIntValue().value_or(false);
}

inline void set_is_support_gyroscope(bool v) {
  Attr(CommonIdx::is_support_gyroscope).SetIntValue(0, v, false, false);
}
inline bool get_is_support_gyroscope() const {
  return Attr(CommonIdx::is_support_gyroscope).GetIntValue().value_or(false);
}

inline void set_is_trace_api_detection(bool v) {
  Attr(CommonIdx::is_trace_api_detection).SetIntValue(0, v, false, false);
}
inline bool get_is_trace_api_detection() const {
  return Attr(CommonIdx::is_trace_api_detection).GetIntValue().value_or(false);
}

inline void set_is_universe_adn_flow(bool v) {
  Attr(CommonIdx::is_universe_adn_flow).SetIntValue(0, v, false, false);
}
inline bool get_is_universe_adn_flow() const {
  return Attr(CommonIdx::is_universe_adn_flow).GetIntValue().value_or(false);
}

inline void set_is_universe_cached_res(bool v) {
  Attr(CommonIdx::is_universe_cached_res).SetIntValue(0, v, false, false);
}
inline bool get_is_universe_cached_res() const {
  return Attr(CommonIdx::is_universe_cached_res).GetIntValue().value_or(false);
}

inline void set_is_model_predict_failure(bool v) {
  Attr(CommonIdx::is_model_predict_failure).SetIntValue(0, v, false, false);
}
inline bool get_is_model_predict_failure() const {
  return Attr(CommonIdx::is_model_predict_failure).GetIntValue().value_or(false);
}

inline void set_is_universe_inner_loop_admit_traffic(bool v) {
  Attr(CommonIdx::is_universe_inner_loop_admit_traffic).SetIntValue(0, v, false, false);
}
inline bool get_is_universe_inner_loop_admit_traffic() const {
  return Attr(CommonIdx::is_universe_inner_loop_admit_traffic).GetIntValue().value_or(false);
}
inline void set_is_universe_rtb_flow(bool v) {
  Attr(CommonIdx::is_universe_rtb_flow).SetIntValue(0, v, false, false);
}
inline bool get_is_universe_rtb_flow() const {
  return Attr(CommonIdx::is_universe_rtb_flow).GetIntValue().value_or(false);
}

inline void set_is_unlogin_user(bool v) {
  Attr(CommonIdx::is_unlogin_user).SetIntValue(0, v, false, false);
}
inline bool get_is_unlogin_user() const {
  return Attr(CommonIdx::is_unlogin_user).GetIntValue().value_or(false);
}

inline void set_need_outer_retrieval(bool v) {
  Attr(CommonIdx::need_outer_retrieval).SetIntValue(0, v, false, false);
}
inline bool get_need_outer_retrieval() const {
  return Attr(CommonIdx::need_outer_retrieval).GetIntValue().value_or(true);
}

inline void set_pass_pre_filter(bool v) {
  Attr(CommonIdx::pass_pre_filter).SetIntValue(0, v, false, false);
}
inline bool get_pass_pre_filter() const {
  return Attr(CommonIdx::pass_pre_filter).GetIntValue().value_or(false);
}

inline void set_perf_ab_param_bool(bool v) {
  Attr(CommonIdx::perf_ab_param_bool).SetIntValue(0, v, false, false);
}
inline bool get_perf_ab_param_bool() const {
  return Attr(CommonIdx::perf_ab_param_bool).GetIntValue().value_or(false);
}

inline void set_rta_ads_stra_start(bool v) {
  Attr(CommonIdx::rta_ads_stra_start).SetIntValue(0, v, false, false);
}
inline bool get_rta_ads_stra_start() const {
  return Attr(CommonIdx::rta_ads_stra_start).GetIntValue().value_or(false);
}

inline void set_should_be_filtered_by_cpm_thres(bool v) {
  Attr(CommonIdx::should_be_filtered_by_cpm_thres).SetIntValue(0, v, false, false);
}
inline bool get_should_be_filtered_by_cpm_thres() const {
  return Attr(CommonIdx::should_be_filtered_by_cpm_thres).GetIntValue().value_or(true);
}

inline void set_universe_antispam_from_api(bool v) {
  Attr(CommonIdx::universe_antispam_from_api).SetIntValue(0, v, false, false);
}
inline bool get_universe_antispam_from_api() const {
  return Attr(CommonIdx::universe_antispam_from_api).GetIntValue().value_or(false);
}

// curr_ad_data_v2 just declare funcs, impl in cc.inl
void set_curr_ad_data_v2(const kuaishou::ad::AdDataV2 * v);
const kuaishou::ad::AdDataV2 * get_curr_ad_data_v2() const;

inline DebugData* mutable_debug_data() {
  return Attr(CommonIdx::debug_data).GetMutablePtrValue<DebugData>();
}
inline const DebugData& get_debug_data() const {
  return *Attr(CommonIdx::debug_data).GetPtrValue<DebugData>();
}

inline void set_degrade_level(DegradeLevel v) {
  // degrade_level = v;
  Attr(CommonIdx::degrade_level).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const DegradeLevel get_degrade_level() const {
  return static_cast<DegradeLevel>(Attr(CommonIdx::degrade_level).GetIntValue().value_or(
    static_cast<int32_t>(DegradeLevel::kLevelDefault)));
}

inline void set_universe_predict_cpm_threshold(double v) {
  Attr(CommonIdx::universe_predict_cpm_threshold).SetDoubleValue(0, v, false, false);
}
inline double get_universe_predict_cpm_threshold() const {
  return Attr(CommonIdx::universe_predict_cpm_threshold).GetDoubleValue().value_or(0.0);
}

inline void set_universe_rtb_coff(double v) {
  Attr(CommonIdx::universe_rtb_coff).SetDoubleValue(0, v, false, false);
}
inline double get_universe_rtb_coff() const {
  return Attr(CommonIdx::universe_rtb_coff).GetDoubleValue().value_or(1.0);
}

// forward_handler_common just declare funcs, impl in cc.inl
void set_forward_handler_common(ForwardHandlerCommon * v);
ForwardHandlerCommon * mutable_forward_handler_common() const ;
const ForwardHandlerCommon * get_forward_handler_common() const;

// front_server_request_ just declare funcs, impl in cc.inl
void set_front_server_request(FrontServerRequest * v);
FrontServerRequest * mutable_front_server_request() const ;
const FrontServerRequest * get_front_server_request() const;

// front_server_response_ just declare funcs, impl in cc.inl
void set_front_server_response(FrontServerResponse * v);
FrontServerResponse * mutable_front_server_response() const ;
const FrontServerResponse * get_front_server_response() const;

// style_info_req just declare funcs, impl in cc.inl
GetStyleInfoReq * mutable_style_info_req() const ;
const GetStyleInfoReq * get_style_info_req() const;

// style_info_resp just declare funcs, impl in cc.inl
GetStyleInfoResp * mutable_style_info_resp() const ;
const GetStyleInfoResp * get_style_info_resp() const;

// arena_ just declare funcs, impl in cc.inl
void set_arena(google::protobuf::Arena * v);
google::protobuf::Arena * mutable_arena() const ;
const google::protobuf::Arena * get_arena() const;

inline void set_grid_unit_id(int32_t v) {
  Attr(CommonIdx::grid_unit_id).SetIntValue(0, v, false, false);
}
inline int32_t get_grid_unit_id() const {
  return Attr(CommonIdx::grid_unit_id).GetIntValue().value_or(0);
}

inline void set_jinniu_branch_test(int32_t v) {
  Attr(CommonIdx::jinniu_branch_test).SetIntValue(0, v, false, false);
}
inline int32_t get_jinniu_branch_test() const {
  return Attr(CommonIdx::jinniu_branch_test).GetIntValue().value_or(-1);
}

inline void set_print_factor_info_random_num(int32_t v) {
  Attr(CommonIdx::print_factor_info_random_num).SetIntValue(0, v, false, false);
}
inline int32_t get_print_factor_info_random_num() const {
  return Attr(CommonIdx::print_factor_info_random_num).GetIntValue().value_or(0);
}

inline void set_pv_recalled_reason(int32_t v) {
  Attr(CommonIdx::pv_recalled_reason).SetIntValue(0, v, false, false);
}
inline int32_t get_pv_recalled_reason() const {
  return Attr(CommonIdx::pv_recalled_reason).GetIntValue().value_or(0);
}

inline void set_traffic_level(int32_t v) {
  Attr(CommonIdx::traffic_level).SetIntValue(0, v, false, false);
}
inline int32_t get_traffic_level() const {
  return Attr(CommonIdx::traffic_level).GetIntValue().value_or(-1);
}

inline void set_universe_skip_filtered_by_cpm_thres_type(int32_t v) {
  Attr(CommonIdx::universe_skip_filtered_by_cpm_thres_type).SetIntValue(0, v, false, false);
}
inline int32_t get_universe_skip_filtered_by_cpm_thres_type() const {
  return Attr(CommonIdx::universe_skip_filtered_by_cpm_thres_type).GetIntValue().value_or(0);
}

inline void set_abtest_user_id(int64_t v) {
  Attr(CommonIdx::abtest_user_id).SetIntValue(0, v, false, false);
}
inline int64_t get_abtest_user_id() const {
  return Attr(CommonIdx::abtest_user_id).GetIntValue().value_or(0);
}

inline void set_antispam_code(int64_t v) {
  Attr(CommonIdx::antispam_code).SetIntValue(0, v, false, false);
}
inline int64_t get_antispam_code() const {
  return Attr(CommonIdx::antispam_code).GetIntValue().value_or(0);
}

inline void set_cache_origin_llsid(int64_t v) {
  Attr(CommonIdx::cache_origin_llsid).SetIntValue(0, v, false, false);
}
inline int64_t get_cache_origin_llsid() const {
  return Attr(CommonIdx::cache_origin_llsid).GetIntValue().value_or(0);
}

inline void set_llsid(int64_t v) {
  Attr(CommonIdx::llsid).SetIntValue(0, v, false, false);
}
inline int64_t get_llsid() const {
  return Attr(CommonIdx::llsid).GetIntValue().value_or(-1);
}

inline void set_low_quality_ads_filter_tag(int64_t v) {
  Attr(CommonIdx::low_quality_ads_filter_tag).SetIntValue(0, v, false, false);
}
inline int64_t get_low_quality_ads_filter_tag() const {
  return Attr(CommonIdx::low_quality_ads_filter_tag).GetIntValue().value_or(0);
}

inline void set_page_id(int64_t v) {
  Attr(CommonIdx::page_id).SetIntValue(0, v, false, false);
}
inline int64_t get_page_id() const {
  return Attr(CommonIdx::page_id).GetIntValue().value_or(0);
}

inline void set_preview_creative_id(int64_t v) {
  Attr(CommonIdx::preview_creative_id).SetIntValue(0, v, false, false);
}
inline int64_t get_preview_creative_id() const {
  return Attr(CommonIdx::preview_creative_id).GetIntValue().value_or(0);
}

inline void set_start_time_ns(int64_t v) {
  Attr(CommonIdx::start_time_ns).SetIntValue(0, v, false, false);
}
inline int64_t get_start_time_ns() const {
  return Attr(CommonIdx::start_time_ns).GetIntValue().value_or(0);
}

inline void set_start_time_us(int64_t v) {
  Attr(CommonIdx::start_time_us).SetIntValue(0, v, false, false);
}
inline int64_t get_start_time_us() const {
  return Attr(CommonIdx::start_time_us).GetIntValue().value_or(0);
}

inline void set_start_ts(int64_t v) {
  Attr(CommonIdx::start_ts).SetIntValue(0, v, false, false);
}
inline int64_t get_start_ts() const {
  return Attr(CommonIdx::start_ts).GetIntValue().value_or(0);
}

inline void set_medium_industry_id_v2(int64_t v) {
  auto val = v; Attr(CommonIdx::medium_industry_id_v2).SetIntValue(0, val, false, false);
}

inline int64_t get_medium_industry_id_v2() {
  return Attr(CommonIdx::medium_industry_id_v2).GetIntValue().value_or(0);
}

inline void set_medium_sub_industry_id_v2(int64_t v) {
  auto val = v; Attr(CommonIdx::medium_sub_industry_id_v2).SetIntValue(0, val, false, false);
}

inline int64_t get_medium_sub_industry_id_v2() {
  return Attr(CommonIdx::medium_sub_industry_id_v2).GetIntValue().value_or(0);
}

inline void set_sub_page_id(int64_t v) {
  Attr(CommonIdx::sub_page_id).SetIntValue(0, v, false, false);
}
inline int64_t get_sub_page_id() const {
  return Attr(CommonIdx::sub_page_id).GetIntValue().value_or(0);
}

inline void set_traffic_marking(int64_t v) {
  Attr(CommonIdx::traffic_marking).SetIntValue(0, v, false, false);
}
inline int64_t get_traffic_marking() const {
  return Attr(CommonIdx::traffic_marking).GetIntValue().value_or(0);
}

inline void set_universe_inner_filter_uplift_tag(int64_t v) {
  Attr(CommonIdx::universe_inner_filter_uplift_tag).SetIntValue(0, v, false, false);
}
inline int64_t get_universe_inner_filter_uplift_tag() const {
  return Attr(CommonIdx::universe_inner_filter_uplift_tag).GetIntValue().value_or(0);
}

inline void set_universe_live_reserve_uplift_exp_author_id(int64_t v) {
  Attr(CommonIdx::universe_live_reserve_uplift_exp_author_id).SetIntValue(0, v, false, false);
}
inline int64_t get_universe_live_reserve_uplift_exp_author_id() const {
  return Attr(CommonIdx::universe_live_reserve_uplift_exp_author_id).GetIntValue().value_or(0);
}

inline void set_user_id(int64_t v) {
  Attr(CommonIdx::user_id).SetIntValue(0, v, false, false);
}
inline int64_t get_user_id() const {
  return Attr(CommonIdx::user_id).GetIntValue().value_or(0);
}

inline void set_work_flow_type(int64_t v) {
  Attr(CommonIdx::work_flow_type).SetIntValue(0, v, false, false);
}
inline int64_t get_work_flow_type() const {
  return Attr(CommonIdx::work_flow_type).GetIntValue().value_or(0);
}

inline ks::abtest2::AbtestMappingId* mutable_abtest_mapping_id() {
  return Attr(CommonIdx::abtest_mapping_id).GetMutablePtrValue<ks::abtest2::AbtestMappingId>();
}
inline const ks::abtest2::AbtestMappingId& get_abtest_mapping_id() const {
  return *Attr(CommonIdx::abtest_mapping_id).GetPtrValue<ks::abtest2::AbtestMappingId>();
}

inline ks::ad_base::KLog* mutable_ad_klog() {
  return Attr(CommonIdx::ad_klog).GetMutablePtrValue<ks::ad_base::KLog>();
}
inline const ks::ad_base::KLog& get_ad_klog() const {
  return *Attr(CommonIdx::ad_klog).GetPtrValue<ks::ad_base::KLog>();
}

inline ks::ad_base::PosManagerBase* mutable_pos_manager() {
  return Attr(CommonIdx::pos_manager).GetMutablePtrValue<ks::ad_base::PosManagerBase>();
}
inline const ks::ad_base::PosManagerBase& get_pos_manager() const {
  return *Attr(CommonIdx::pos_manager).GetPtrValue<ks::ad_base::PosManagerBase>();
}

inline void set_rta_req_filter_reason(const kuaishou::ad::AdEnum_RtaReqFilterReason v) {
  Attr(CommonIdx::rta_req_filter_reason).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const kuaishou::ad::AdEnum_RtaReqFilterReason get_rta_req_filter_reason() const {
  return static_cast<kuaishou::ad::AdEnum_RtaReqFilterReason>(Attr(CommonIdx::rta_req_filter_reason).GetIntValue().value_or(0));
}

// ad_pack_request_ just declare funcs, impl in cc.inl
void set_ad_pack_request(kuaishou::ad::AdPackRequest * v);
kuaishou::ad::AdPackRequest * mutable_ad_pack_request() const ;
const kuaishou::ad::AdPackRequest * get_ad_pack_request() const;

inline kuaishou::ad::AdRankPassThrough* mutable_ad_rank_pass_through() {
  return Attr(CommonIdx::ad_rank_pass_through).GetMutablePtrValue<kuaishou::ad::AdRankPassThrough>();
}
inline const kuaishou::ad::AdRankPassThrough& get_ad_rank_pass_through() const {
  return *Attr(CommonIdx::ad_rank_pass_through).GetPtrValue<kuaishou::ad::AdRankPassThrough>();
}

inline kuaishou::ad::algorithm::UniversePredictRequest* mutable_predict_request() {
  return Attr(CommonIdx::predict_request).GetMutablePtrValue<kuaishou::ad::algorithm::UniversePredictRequest>();
}
inline const kuaishou::ad::algorithm::UniversePredictRequest& get_predict_request() const {
  return *Attr(CommonIdx::predict_request).GetPtrValue<kuaishou::ad::algorithm::UniversePredictRequest>();
}

inline kuaishou::ad::algorithm::UniversePredictResponse* mutable_predict_response() {
  return Attr(CommonIdx::predict_response).GetMutablePtrValue<kuaishou::ad::algorithm::UniversePredictResponse>();
}
inline const kuaishou::ad::algorithm::UniversePredictResponse& get_predict_response() const {
  return *Attr(CommonIdx::predict_response).GetPtrValue<kuaishou::ad::algorithm::UniversePredictResponse>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_detail_used_item() {
  return Attr(CommonIdx::detail_used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_detail_used_item() const {
  return *Attr(CommonIdx::detail_used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_new_creative_used_item() {
  return Attr(CommonIdx::new_creative_used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_new_creative_used_item() const {
  return *Attr(CommonIdx::new_creative_used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_union_ltr_used_item() {
  return Attr(CommonIdx::union_ltr_used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_union_ltr_used_item() const {
  return *Attr(CommonIdx::union_ltr_used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::algorithm::UsedItem* mutable_used_item() {
  return Attr(CommonIdx::used_item).GetMutablePtrValue<kuaishou::ad::algorithm::UsedItem>();
}
inline const kuaishou::ad::algorithm::UsedItem& get_used_item() const {
  return *Attr(CommonIdx::used_item).GetPtrValue<kuaishou::ad::algorithm::UsedItem>();
}

inline kuaishou::ad::TransparentPackInfo* mutable_trans_pack_info() {
  return Attr(CommonIdx::trans_pack_info).GetMutablePtrValue<kuaishou::ad::TransparentPackInfo>();
}
inline const kuaishou::ad::TransparentPackInfo& get_trans_pack_info() const {
  return *Attr(CommonIdx::trans_pack_info).GetPtrValue<kuaishou::ad::TransparentPackInfo>();
}

inline void set_fake_user_type(const kuaishou::ad::UserFakeType v) {
  Attr(CommonIdx::fake_user_type).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const kuaishou::ad::UserFakeType get_fake_user_type() const {
  return static_cast<kuaishou::ad::UserFakeType>(
    Attr(CommonIdx::fake_user_type).GetIntValue().value_or(static_cast<int64_t>(::kuaishou::ad::UserFakeType::NORMAL_USER)));
}

inline kuaishou::log::ad::AdDspSimplifyAlwaysLog* mutable_simplify_always_log() {
  return Attr(CommonIdx::simplify_always_log).GetMutablePtrValue<kuaishou::log::ad::AdDspSimplifyAlwaysLog>();
}
inline const kuaishou::log::ad::AdDspSimplifyAlwaysLog& get_simplify_always_log() const {
  return *Attr(CommonIdx::simplify_always_log).GetPtrValue<kuaishou::log::ad::AdDspSimplifyAlwaysLog>();
}

inline kuaishou::log::ad::AdDspTraceAlwaysLog* mutable_trace_always_log() {
  return Attr(CommonIdx::trace_always_log).GetMutablePtrValue<kuaishou::log::ad::AdDspTraceAlwaysLog>();
}
inline const kuaishou::log::ad::AdDspTraceAlwaysLog& get_trace_always_log() const {
  return *Attr(CommonIdx::trace_always_log).GetPtrValue<kuaishou::log::ad::AdDspTraceAlwaysLog>();
}

inline void set_last_filter_condition(kuaishou::log::ad::AdTraceFilterCondition v) {
  // last_filter_condition_ = v;
  Attr(CommonIdx::last_filter_condition).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline kuaishou::log::ad::AdTraceFilterCondition get_last_filter_condition() const {
  return static_cast<kuaishou::log::ad::AdTraceFilterCondition>
    (Attr(CommonIdx::last_filter_condition).GetIntValue().value_or(
      static_cast<int64_t>(kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION)));
}
inline void set_soft_last_filter_condition(kuaishou::log::ad::AdTraceFilterCondition v) {
  // soft_last_filter_condition_ = v;
  Attr(CommonIdx::soft_last_filter_condition).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline kuaishou::log::ad::AdTraceFilterCondition get_soft_last_filter_condition() const {
  return static_cast<kuaishou::log::ad::AdTraceFilterCondition>
    (Attr(CommonIdx::soft_last_filter_condition).GetIntValue().value_or(
      static_cast<int64_t>(kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION)));
}

inline void set_preview_type(const PreviewType v) {
  Attr(CommonIdx::preview_type).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const PreviewType get_preview_type() const {
  return static_cast<PreviewType>(Attr(CommonIdx::preview_type).GetIntValue().value_or(static_cast<int64_t>(PreviewType::kPreviewAd)));
}

inline Rank2FrontInfo* mutable_rank_2_front_info() {
  return Attr(CommonIdx::rank_2_front_info).GetMutablePtrValue<Rank2FrontInfo>();
}
inline const Rank2FrontInfo& get_rank_2_front_info() const {
  return *Attr(CommonIdx::rank_2_front_info).GetPtrValue<Rank2FrontInfo>();
}

inline void set_req_adapter_code(const ReqAdapterCode v) {
  Attr(CommonIdx::req_adapter_code).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const ReqAdapterCode get_req_adapter_code() const {
  return static_cast<ReqAdapterCode>(Attr(CommonIdx::req_adapter_code).GetIntValue().value_or(static_cast<int64_t>(ReqAdapterCode::kAdapterSuccess)));
}

inline std::shared_ptr<const ks::AbtestUserInfo >* mutable_ab_user_info() {
  return &ab_user_info;
}
inline const std::shared_ptr<const ks::AbtestUserInfo >& get_ab_user_info() const {
  return ab_user_info;
}

inline void set_universe_tiny_search_black_list(std::shared_ptr<engine_base::UniverseTinySearchBlackList> v) {
  universe_tiny_search_black_list = v;
  Attr(CommonIdx::universe_tiny_search_black_list).SetPtrValue(0, std::move(v));
}
inline engine_base::UniverseTinySearchBlackList* mutable_universe_tiny_search_black_list() {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::universe_tiny_search_black_list).GetMutablePtrValue<engine_base::UniverseTinySearchBlackList>();
  }
  return universe_tiny_search_black_list.get();
}
inline const engine_base::UniverseTinySearchBlackList* get_universe_tiny_search_black_list() const {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::universe_tiny_search_black_list).GetPtrValue<engine_base::UniverseTinySearchBlackList>();
  }
  return universe_tiny_search_black_list.get();
}

inline void set_photo_negative_tags(std::shared_ptr<kconf::OuterNativeNegFilterRateConfig> v) {
  photo_negative_tags = v;
  Attr(CommonIdx::photo_negative_tags).SetPtrValue(0, std::move(v));
}
inline kconf::OuterNativeNegFilterRateConfig* mutable_photo_negative_tags() {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::photo_negative_tags).GetMutablePtrValue<kconf::OuterNativeNegFilterRateConfig>();
  }
  return photo_negative_tags.get();
}
inline const kconf::OuterNativeNegFilterRateConfig* get_photo_negative_tags() const {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::photo_negative_tags).GetPtrValue<kconf::OuterNativeNegFilterRateConfig>();
  }
  return photo_negative_tags.get();
}

inline void set_outer_native_diff_author_open_neg(std::shared_ptr<absl::flat_hash_set<int64_t>> v) {
  outer_native_diff_author_open_neg = v;
  Attr(CommonIdx::outer_native_diff_author_open_neg).SetPtrValue(0, std::move(v));
}
inline absl::flat_hash_set<int64_t>* mutable_outer_native_diff_author_open_neg() {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::outer_native_diff_author_open_neg).GetMutablePtrValue<absl::flat_hash_set<int64_t>>();
  }
  return outer_native_diff_author_open_neg.get();
}
inline const absl::flat_hash_set<int64_t>* get_outer_native_diff_author_open_neg() const {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::outer_native_diff_author_open_neg).GetPtrValue<absl::flat_hash_set<int64_t>>();
  }
  return outer_native_diff_author_open_neg.get();
}

inline void set_iap_ocpx_set(std::shared_ptr<absl::flat_hash_set<std::string>> v) {
  iap_ocpx_set = v;
  Attr(CommonIdx::iap_ocpx_set).SetPtrValue(0, std::move(v));
}
inline absl::flat_hash_set<std::string>* mutable_iap_ocpx_set() {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::iap_ocpx_set).GetMutablePtrValue<absl::flat_hash_set<std::string>>();
  }
  return iap_ocpx_set.get();
}
inline const absl::flat_hash_set<std::string>* get_iap_ocpx_set() const {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::iap_ocpx_set).GetPtrValue<absl::flat_hash_set<std::string>>();
  }
  return iap_ocpx_set.get();
}

inline void set_universe_traffic_control_context(std::shared_ptr<UniverseTrafficControlContext> v) {
  universe_traffic_control_context = v;
  Attr(CommonIdx::universe_traffic_control_context).SetPtrValue(0, std::move(v));
}
inline UniverseTrafficControlContext* mutable_universe_traffic_control_context() {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::universe_traffic_control_context).GetMutablePtrValue<UniverseTrafficControlContext>();
  }
  return universe_traffic_control_context.get();
}
inline const UniverseTrafficControlContext* get_universe_traffic_control_context() const {
  if (change_context_shared_ptr_source_) {
    return Attr(CommonIdx::universe_traffic_control_context).GetPtrValue<UniverseTrafficControlContext>();
  }
  return universe_traffic_control_context.get();
}

inline void set_abtest_device_id(const std::string& v) {
  auto val = v; Attr(CommonIdx::abtest_device_id).SetStringValue(0, std::move(val), false, false);
}
inline std::string copy_abtest_device_id() const {
  return std::string(Attr(CommonIdx::abtest_device_id).GetStringValue().value_or(""));
}

inline absl::string_view borrow_abtest_device_id() const {
  return Attr(CommonIdx::abtest_device_id).GetStringValue().value_or("");
}

inline void set_antispam_ext(const std::string& v) {
  auto val = v; Attr(CommonIdx::antispam_ext).SetStringValue(0, std::move(val), false, false);
}
inline std::string copy_antispam_ext() const {
  return std::string(Attr(CommonIdx::antispam_ext).GetStringValue(0).value_or(""));
}

inline absl::string_view borrow_antispam_ext() const {
  return Attr(CommonIdx::antispam_ext).GetStringValue(0).value_or("");
}

inline void set_app_id(const std::string& v) {
  auto val = v; Attr(CommonIdx::app_id).SetStringValue(0, std::move(val), false, false);
}
inline std::string copy_app_id() const {
  return std::string(Attr(CommonIdx::app_id).GetStringValue(0).value_or(""));
}

inline absl::string_view borrow_app_id() const {
  return Attr(CommonIdx::app_id).GetStringValue(0).value_or("");
}

inline void set_app_version(const std::string& v) {
  auto val = v; Attr(CommonIdx::app_version).SetStringValue(0, std::move(val), false, false);
}
inline std::string copy_app_version() const {
  return std::string(Attr(CommonIdx::app_version).GetStringValue(0).value_or(""));
}

inline absl::string_view borrow_app_version() const {
  return Attr(CommonIdx::app_version).GetStringValue(0).value_or("");
}

inline void set_device_id(const std::string& v) {
  auto val = v; Attr(CommonIdx::device_id).SetStringValue(0, std::move(val), false, false);
}
inline std::string copy_device_id() const {
  return std::string(Attr(CommonIdx::device_id).GetStringValue(0).value_or(""));
}
inline absl::string_view borrow_device_id() const {
  return Attr(CommonIdx::device_id).GetStringValue(0).value_or("");
}

inline void set_device_id_hash_key(const std::string& v) {
  auto val = v; Attr(CommonIdx::device_id_hash_key).SetStringValue(0, std::move(val), false, false);
}
inline std::string copy_device_id_hash_key() const {
  return std::string(Attr(CommonIdx::device_id_hash_key).GetStringValue(0).value_or(""));
}
inline absl::string_view borrow_device_id_hash_key() const {
  return Attr(CommonIdx::device_id_hash_key).GetStringValue(0).value_or("");
}

inline void set_media_wx_small_app_id(const std::string& v) {
  auto val = v; Attr(CommonIdx::media_wx_small_app_id).SetStringValue(0, std::move(val), false, false);
}
inline std::string copy_media_wx_small_app_id() const {
  return std::string(Attr(CommonIdx::media_wx_small_app_id).GetStringValue(0).value_or(""));
}
inline absl::string_view borrow_media_wx_small_app_id() const {
  return Attr(CommonIdx::media_wx_small_app_id).GetStringValue(0).value_or("");
}

inline void set_tab_name(const std::string& v) {
  auto val = v; Attr(CommonIdx::tab_name).SetStringValue(0, std::move(val), false, false);
}
inline std::string copy_tab_name() const {
  return std::string(Attr(CommonIdx::tab_name).GetStringValue(0).value_or(""));
}
inline absl::string_view borrow_tab_name() const {
  return Attr(CommonIdx::tab_name).GetStringValue(0).value_or("");
}

inline void set_test_device_id(const std::string& v) {
  auto val = v; Attr(CommonIdx::test_device_id).SetStringValue(0, std::move(val), false, false);
}
inline std::string copy_test_device_id() const {
  return std::string(Attr(CommonIdx::test_device_id).GetStringValue(0).value_or(""));
}
inline absl::string_view borrow_test_device_id() const {
  return Attr(CommonIdx::test_device_id).GetStringValue(0).value_or("");
}

inline std::unordered_map<int64, int >* mutable_creative_id_2_idx() {
  return Attr(CommonIdx::creative_id_2_idx).GetMutablePtrValue<std::unordered_map<int64, int >>();
}
inline const std::unordered_map<int64, int >& get_creative_id_2_idx() const {
  return *Attr(CommonIdx::creative_id_2_idx).GetPtrValue<std::unordered_map<int64, int >>();
}

inline std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >* mutable_style_material_land_page_map() {
  return Attr(CommonIdx::style_material_land_page_map).GetMutablePtrValue<std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >>();
}
inline const std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >& get_style_material_land_page_map() const {
  return *Attr(CommonIdx::style_material_land_page_map).GetPtrValue<std::unordered_map<int64_t, std::pair<AdStyleMaterial, AdMagicSitePageDas> >>();
}

inline std::unordered_map<int64_t, std::vector<AdStyleMaterialSub> >* mutable_style_material_map() {
  return Attr(CommonIdx::style_material_map).GetMutablePtrValue<std::unordered_map<int64_t, std::vector<AdStyleMaterialSub> >>();
}
inline const std::unordered_map<int64_t, std::vector<AdStyleMaterialSub> >& get_style_material_map() const {
  return *Attr(CommonIdx::style_material_map).GetPtrValue<std::unordered_map<int64_t, std::vector<AdStyleMaterialSub> >>();
}

inline std::unordered_map<int64_t, std::vector<kuaishou::ad::StyleData> >* mutable_style_data_for_online_join() {
  return Attr(CommonIdx::style_data_for_online_join).GetMutablePtrValue<std::unordered_map<int64_t, std::vector<kuaishou::ad::StyleData> >>();
}
inline const std::unordered_map<int64_t, std::vector<kuaishou::ad::StyleData> >& get_style_data_for_online_join() const {
  return *Attr(CommonIdx::style_data_for_online_join).GetPtrValue<std::unordered_map<int64_t, std::vector<kuaishou::ad::StyleData> >>();
}

inline std::unordered_map<std::string, std::string >* mutable_universe_win_rate_model_param_map() {
  return Attr(CommonIdx::universe_win_rate_model_param_map).GetMutablePtrValue<std::unordered_map<std::string, std::string >>();
}
inline const std::unordered_map<std::string, std::string >& get_universe_win_rate_model_param_map() const {
  return *Attr(CommonIdx::universe_win_rate_model_param_map).GetPtrValue<std::unordered_map<std::string, std::string >>();
}

inline std::unordered_set<uint64 >* mutable_follow_ids() {
  return Attr(CommonIdx::follow_ids).GetMutablePtrValue<std::unordered_set<uint64 >>();
}
inline const std::unordered_set<uint64 >& get_follow_ids() const {
  return *Attr(CommonIdx::follow_ids).GetPtrValue<std::unordered_set<uint64 >>();
}

inline std::unordered_set<uint64 >* mutable_force_direct_call_cids() {
  return Attr(CommonIdx::force_direct_call_cids).GetMutablePtrValue<std::unordered_set<uint64 >>();
}
inline const std::unordered_set<uint64 >& get_force_direct_call_cids() const {
  return *Attr(CommonIdx::force_direct_call_cids).GetPtrValue<std::unordered_set<uint64 >>();
}

inline std::vector<BaseRetrievalHandler * >* mutable_retrieval_handler_list() {
  return Attr(CommonIdx::retrieval_handler_list).GetMutablePtrValue<std::vector<BaseRetrievalHandler * >>();
}
inline const std::vector<BaseRetrievalHandler * >& get_retrieval_handler_list() const {
  return *Attr(CommonIdx::retrieval_handler_list).GetPtrValue<std::vector<BaseRetrievalHandler * >>();
}

inline std::vector<int64_t >* mutable_request_inventory_pos_id() {
  return Attr(CommonIdx::request_inventory_pos_id).GetMutablePtrValue<std::vector<int64_t >>();
}
inline const std::vector<int64_t >& get_request_inventory_pos_id() const {
  return *Attr(CommonIdx::request_inventory_pos_id).GetPtrValue<std::vector<int64_t >>();
}

inline std::vector <::kuaishou::ad::AdEnum_UniverseTrafficType >* mutable_universe_traffic_types() {
  return Attr(CommonIdx::universe_traffic_types).GetMutablePtrValue<std::vector <::kuaishou::ad::AdEnum_UniverseTrafficType >>();
}
inline const std::vector <::kuaishou::ad::AdEnum_UniverseTrafficType >& get_universe_traffic_types() const {
  return *Attr(CommonIdx::universe_traffic_types).GetPtrValue<std::vector <::kuaishou::ad::AdEnum_UniverseTrafficType >>();
}

inline std::vector<kuaishou::ad::PerfInfo::NodeTimeCost >* mutable_node_time_cost() {
  return Attr(CommonIdx::node_time_cost).GetMutablePtrValue<std::vector<kuaishou::ad::PerfInfo::NodeTimeCost >>();
}
inline const std::vector<kuaishou::ad::PerfInfo::NodeTimeCost >& get_node_time_cost() const {
  return *Attr(CommonIdx::node_time_cost).GetPtrValue<std::vector<kuaishou::ad::PerfInfo::NodeTimeCost >>();
}

inline void set_book_id(uint64_t v) {
  Attr(CommonIdx::book_id).SetIntValue(0, v, false, false);
}
inline uint64_t get_book_id() const {
  return Attr(CommonIdx::book_id).GetIntValue().value_or(0);
}

inline UniverseAdnProfitInfo* mutable_universe_adn_profit_info() {
  return Attr(CommonIdx::universe_adn_profit_info).GetMutablePtrValue<UniverseAdnProfitInfo>();
}
inline const UniverseAdnProfitInfo& get_universe_adn_profit_info() const {
  return *Attr(CommonIdx::universe_adn_profit_info).GetPtrValue<UniverseAdnProfitInfo>();
}

inline UniverseAdPrice* mutable_ad_price() {
  return Attr(CommonIdx::ad_price).GetMutablePtrValue<UniverseAdPrice>();
}
inline const UniverseAdPrice& get_ad_price() const {
  return *Attr(CommonIdx::ad_price).GetPtrValue<UniverseAdPrice>();
}

inline UniverseQualityData* mutable_universe_quality_data() {
  return Attr(CommonIdx::universe_quality_data).GetMutablePtrValue<UniverseQualityData>();
}
inline const UniverseQualityData& get_universe_quality_data() const {
  return *Attr(CommonIdx::universe_quality_data).GetPtrValue<UniverseQualityData>();
}

inline UniverseRequestMergeInfo* mutable_universe_request_merge_info() {
  return Attr(CommonIdx::universe_request_merge_info).GetMutablePtrValue<UniverseRequestMergeInfo>();
}
inline const UniverseRequestMergeInfo& get_universe_request_merge_info() const {
  return *Attr(CommonIdx::universe_request_merge_info).GetPtrValue<UniverseRequestMergeInfo>();
}

inline void set_ac_fetcher_type(const kuaishou::ad::AdTargetResponse_AcFetcherType v) {
  Attr(CommonIdx::ac_fetcher_type).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline const kuaishou::ad::AdTargetResponse_AcFetcherType get_ac_fetcher_type() const {
  return static_cast<kuaishou::ad::AdTargetResponse_AcFetcherType>(Attr(CommonIdx::ac_fetcher_type).GetIntValue().value_or(static_cast<int64_t>(kuaishou::ad::AdTargetResponse::UNKONWN_FETCHER_TYPE)));
}

inline ks::spdm::Context* mutable_spdm_ctx() {
  return Attr(CommonIdx::spdm_ctx).GetMutablePtrValue<ks::spdm::Context>();
}
inline const ks::spdm::Context& get_spdm_ctx() const {
  return *Attr(CommonIdx::spdm_ctx).GetPtrValue<ks::spdm::Context>();
}

inline void set_medium_valid_err_code(::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode v) {
  // medium_valid_err_code = v;
  Attr(CommonIdx::medium_valid_err_code).SetIntValue(0, static_cast<int64_t>(v), false, false);
}
inline ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode get_medium_valid_err_code() const {
  return static_cast<::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCode>
    (Attr(CommonIdx::medium_valid_err_code).GetIntValue().value_or(
      static_cast<int64_t>(::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::SUCCESS)));
}

inline void set_universe_enable_log_predict_cpm(bool v) {
  Attr(CommonIdx::universe_enable_log_predict_cpm).SetIntValue(0, v, false, false);
}
inline bool get_universe_enable_log_predict_cpm() const {
  return Attr(CommonIdx::universe_enable_log_predict_cpm).GetIntValue().value_or(true);
}

inline void set_universe_enable_log_predict_cpm_grade_quality(bool v) {
  Attr(CommonIdx::universe_enable_log_predict_cpm_grade_quality).SetIntValue(0, v, false, false);
}
inline bool get_universe_enable_log_predict_cpm_grade_quality() const {
  return Attr(CommonIdx::universe_enable_log_predict_cpm_grade_quality).GetIntValue().value_or(true);
}

inline void set_universe_predict_cpm(double v) {
  Attr(CommonIdx::universe_predict_cpm).SetDoubleValue(0, v, false, false);
}
inline double get_universe_predict_cpm() const {
  return Attr(CommonIdx::universe_predict_cpm).GetDoubleValue().value_or(0);
}

inline void set_universe_predict_cpm_for_grade_quality(double v) {
  Attr(CommonIdx::universe_predict_cpm_for_grade_quality).SetDoubleValue(0, v, false, false);
}
inline double get_universe_predict_cpm_for_grade_quality() const {
  return Attr(CommonIdx::universe_predict_cpm_for_grade_quality).GetDoubleValue().value_or(0);
}

inline void set_universe_predict_cpm_for_traffic_type(double v) {
  Attr(CommonIdx::universe_predict_cpm_for_traffic_type).SetDoubleValue(0, v, false, false);
}
inline double get_universe_predict_cpm_for_traffic_type() const {
  return Attr(CommonIdx::universe_predict_cpm_for_traffic_type).GetDoubleValue().value_or(0);
}

inline void set_universe_predict_cpm_threshold_new(double v) {
  Attr(CommonIdx::universe_predict_cpm_threshold_new).SetDoubleValue(0, v, false, false);
}
inline double get_universe_predict_cpm_threshold_new() const {
  return Attr(CommonIdx::universe_predict_cpm_threshold_new).GetDoubleValue().value_or(0.0);
}

inline void set_universe_predict_cpm_threshold_old(double v) {
  Attr(CommonIdx::universe_predict_cpm_threshold_old).SetDoubleValue(0, v, false, false);
}
inline double get_universe_predict_cpm_threshold_old() const {
  return Attr(CommonIdx::universe_predict_cpm_threshold_old).GetDoubleValue().value_or(0.0);
}

inline void set_universe_predict_cpm_threshold_for_traffic_type(double v) {
  Attr(CommonIdx::universe_predict_cpm_threshold_for_traffic_type).SetDoubleValue(0, v, false, false);
}
inline double get_universe_predict_cpm_threshold_for_traffic_type() const {
  return Attr(CommonIdx::universe_predict_cpm_threshold_for_traffic_type).GetDoubleValue().value_or(0.0);
}

inline void set_universe_predict_cpm_threshold_for_traffic_type_old(double v) {
  Attr(CommonIdx::universe_predict_cpm_threshold_for_traffic_type_old).SetDoubleValue(0, v, false, false);
}
inline double get_universe_predict_cpm_threshold_for_traffic_type_old() const {
  return Attr(CommonIdx::universe_predict_cpm_threshold_for_traffic_type_old).GetDoubleValue().value_or(0.0);
}

inline void set_enable_price_precise_fix_pv(bool v) {
  Attr(CommonIdx::enable_price_precise_fix_pv).SetIntValue(0, v, false, false);
}
inline bool get_enable_price_precise_fix_pv() const {
  return Attr(CommonIdx::enable_price_precise_fix_pv).GetIntValue().value_or(false);
}

inline void set_universe_filtered_by_cpm_threshold(bool v) {
  Attr(CommonIdx::universe_filtered_by_cpm_threshold).SetIntValue(0, v, false, false);
}
inline bool get_universe_filtered_by_cpm_threshold() const {
  return Attr(CommonIdx::universe_filtered_by_cpm_threshold).GetIntValue().value_or(true);
}

inline void set_universe_filtered_by_cpm_threshold_old(bool v) {
  Attr(CommonIdx::universe_filtered_by_cpm_threshold_old).SetIntValue(0, v, false, false);
}
inline bool get_universe_filtered_by_cpm_threshold_old() const {
  return Attr(CommonIdx::universe_filtered_by_cpm_threshold_old).GetIntValue().value_or(true);
}

inline void set_universe_filtered_by_cpm_threshold_for_traffic_type(bool v) {
  Attr(CommonIdx::universe_filtered_by_cpm_threshold_for_traffic_type).SetIntValue(0, v, false, false);
}
inline bool get_universe_filtered_by_cpm_threshold_for_traffic_type() const {
  return Attr(CommonIdx::universe_filtered_by_cpm_threshold_for_traffic_type).GetIntValue().value_or(true);
}

inline void set_universe_filtered_by_cpm_threshold_for_traffic_type_old(bool v) {
  Attr(CommonIdx::universe_filtered_by_cpm_threshold_for_traffic_type_old).SetIntValue(0, v, false, false);
}
inline bool get_universe_filtered_by_cpm_threshold_for_traffic_type_old() const {
  return Attr(CommonIdx::universe_filtered_by_cpm_threshold_for_traffic_type_old).GetIntValue().value_or(true);
}

inline void set_universe_log_cpr_ab_group(const std::string& v) {
  auto val = v; Attr(CommonIdx::universe_log_cpr_ab_group).SetStringValue(0, std::move(val), false, false);
}

inline std::string copy_universe_log_cpr_ab_group() const {
  return std::string(Attr(CommonIdx::universe_log_cpr_ab_group).GetStringValue(0).value_or(""));
}

inline absl::string_view borrow_universe_log_cpr_ab_group() const {
  return Attr(CommonIdx::universe_log_cpr_ab_group).GetStringValue(0).value_or("");
}

inline void set_universe_pid_solver_group_index(const std::string& v) {
  auto val = v; Attr(CommonIdx::universe_pid_solver_group_index).SetStringValue(0, std::move(val), false, false);
}

inline std::string copy_universe_pid_solver_group_index() const {
  return std::string(Attr(CommonIdx::universe_pid_solver_group_index).GetStringValue(0).value_or(""));
}

inline absl::string_view borrow_universe_pid_solver_group_index() const {
  return Attr(CommonIdx::universe_pid_solver_group_index).GetStringValue(0).value_or("");
}

inline void set_universe_pid_solver_explore_group_index(const std::string& v) {
  auto val = v; Attr(CommonIdx::universe_pid_solver_explore_group_index).SetStringValue(0, std::move(val), false, false);
}

inline std::string copy_universe_pid_solver_explore_group_index() const {
  return std::string(Attr(CommonIdx::universe_pid_solver_explore_group_index).GetStringValue(0).value_or(""));
}

inline absl::string_view borrow_universe_pid_solver_explore_group_index() const {
  return Attr(CommonIdx::universe_pid_solver_explore_group_index).GetStringValue(0).value_or("");
}

inline void set_universe_pid_solver_tp_ratio(const std::string& v) {
  auto val = v; Attr(CommonIdx::universe_pid_solver_tp_ratio).SetStringValue(0, std::move(val), false, false);
}

inline std::string copy_universe_pid_solver_tp_ratio() const {
  return std::string(Attr(CommonIdx::universe_pid_solver_tp_ratio).GetStringValue(0).value_or(""));
}

inline absl::string_view borrow_universe_pid_solver_tp_ratio() const {
  return Attr(CommonIdx::universe_pid_solver_tp_ratio).GetStringValue(0).value_or("");
}

inline void set_universe_timeout(int v) {
  Attr(CommonIdx::universe_timeout).SetIntValue(0, v, false, false);
}
inline int get_universe_timeout() const {
  return Attr(CommonIdx::universe_timeout).GetIntValue().value_or(0);
}

inline void set_dynamic_traffic_grade_level_ratio(const std::string& v) {
  auto val = v; Attr(CommonIdx::dynamic_traffic_grade_level_ratio).SetStringValue(0, std::move(val), false, false);
}

inline void set_universe_quality_data_fill_rate(double v) {
  mutable_universe_quality_data()->fill_rate = v;
  Attr(CommonIdx::universe_quality_data_fill_rate).SetDoubleValue(0, v, false, false);
}
inline double get_universe_quality_data_fill_rate() const {
  if (change_universe_quality_data_source_) {
    return Attr(CommonIdx::universe_quality_data_fill_rate).GetDoubleValue().value_or(0.0);
  }
  return Attr(CommonIdx::universe_quality_data).GetPtrValue<UniverseQualityData>()->fill_rate;
}

inline void set_universe_quality_data_pos_post_cpm(double v) {
  mutable_universe_quality_data()->pos_post_cpm = v;
  Attr(CommonIdx::universe_quality_data_pos_post_cpm).SetDoubleValue(0, v, false, false);
}

inline double get_universe_quality_data_pos_post_cpm() const {
  if (change_universe_quality_data_source_) {
    return Attr(CommonIdx::universe_quality_data_pos_post_cpm).GetDoubleValue().value_or(0.0);
  }
  return Attr(CommonIdx::universe_quality_data).GetPtrValue<UniverseQualityData>()->pos_post_cpm;
}

inline void set_universe_quality_data_predict_cpm(double v) {
  mutable_universe_quality_data()->predict_cpm = v;
  Attr(CommonIdx::universe_quality_data_predict_cpm).SetDoubleValue(0, v, false, false);
}

inline double get_universe_quality_data_predict_cpm() const {
  if (change_universe_quality_data_source_) {
    return Attr(CommonIdx::universe_quality_data_predict_cpm).GetDoubleValue().value_or(0.0);
  }
  return Attr(CommonIdx::universe_quality_data).GetPtrValue<UniverseQualityData>()->predict_cpm;
}

inline void set_universe_quality_data_model_predict_cpm(double v) {
  mutable_universe_quality_data()->model_predict_cpm = v;
  Attr(CommonIdx::universe_quality_data_model_predict_cpm).SetDoubleValue(0, v, false, false);
}

inline double get_universe_quality_data_model_predict_cpm() const {
  if (change_universe_quality_data_source_) {
    return Attr(CommonIdx::universe_quality_data_model_predict_cpm).GetDoubleValue().value_or(0.0);
  }
  return Attr(CommonIdx::universe_quality_data).GetPtrValue<UniverseQualityData>()->model_predict_cpm;
}

inline void set_universe_quality_data_flag(const UniverseRequestQualityReckonFlag v) {
  mutable_universe_quality_data()->flag = v;
  Attr(CommonIdx::universe_quality_data_flag).SetIntValue(0, v, false, false);
}

inline UniverseRequestQualityReckonFlag get_universe_quality_data_flag() const {
  if (change_universe_quality_data_source_) {
    return static_cast<UniverseRequestQualityReckonFlag>(Attr(CommonIdx::universe_quality_data_flag).GetIntValue().value_or(0));
  }
  return Attr(CommonIdx::universe_quality_data).GetPtrValue<UniverseQualityData>()->flag;
}

inline std::pair<std::vector<double>*, std::vector<double>*> mutable_universe_quality_data_smart_compute_power_vector() {
  return {
    Attr(CommonIdx::universe_quality_data_smart_compute_power_vector).GetMutablePtrValue<std::vector<double>>(),
    &(Attr(CommonIdx::universe_quality_data).GetMutablePtrValue<UniverseQualityData>()->smart_compute_power_vector)
  };
}

inline const std::vector<double>& get_universe_quality_data_smart_compute_power_vector() {
  if (change_universe_quality_data_source_) {
    return *Attr(CommonIdx::universe_quality_data_smart_compute_power_vector).GetPtrValue<std::vector<double>>();
  }
  return Attr(CommonIdx::universe_quality_data).GetPtrValue<UniverseQualityData>()->smart_compute_power_vector;
}

inline std::pair<std::vector<double>*, std::vector<double>*> mutable_universe_quality_data_rtb_win_rate_model_params() const {
  return {
    Attr(CommonIdx::universe_quality_data_rtb_win_rate_model_params).GetMutablePtrValue<std::vector<double>>(),
    &(Attr(CommonIdx::universe_quality_data).GetMutablePtrValue<UniverseQualityData>()->rtb_win_rate_model_params)
  };
}

inline const std::vector<double>& get_universe_quality_data_rtb_win_rate_model_params() {
  if (change_universe_quality_data_source_) {
    return *Attr(CommonIdx::universe_quality_data_rtb_win_rate_model_params).GetPtrValue<std::vector<double>>();
  }
  return Attr(CommonIdx::universe_quality_data).GetPtrValue<UniverseQualityData>()->rtb_win_rate_model_params;
}

inline int64_t get_fi_physical_pos_id() const {
  return Attr(CommonIdx::fi_physical_pos_id).GetIntValue().value_or(0);
}

inline int64_t get_fi_new_media_protection_pos_imp() const {
  return Attr(CommonIdx::fi_new_media_protection_pos_imp).GetIntValue().value_or(0);
}