#include "teams/ad/front_server_universe/processor/process_prepare_post_proc.h"

#include <algorithm>
#include <string>
#include <unordered_map>
#include <utility>
#include <map>
#include <vector>

#include "absl/strings/str_cat.h"
#include "absl/time/clock.h"
#include "base/encoding/url_encode.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_athena/utils/utility/perf_util.h"
#include "teams/ad/ad_base/src/common/deploy_utils.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/klog/klog.h"
#include "teams/ad/ad_base/src/ktrace/local_ktrace_span.h"
#include "teams/ad/ad_base/src/pb_helper/pb_helper.h"
#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_proto/common/product_platform.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_pack_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"
#include "teams/ad/engine_base/fanstop_common/pv_debug/trace_info_io.h"
#include "teams/ad/engine_base/flow_control/flow_checker.h"
#include "teams/ad/engine_base/dragon_node/skydome_node.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/trace/ad_front_simplify_trace_log.h"
#include "teams/ad/front_server_universe/util/ad_server_show/ad_server_show_stat.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/universe_rank_cache/universe_rank_cache.h"
#include "teams/ad/front_server_universe/util/universe_traffic_control/universe_traffic_control.h"
#include "teams/ad/front_server_universe/util/utility/ksn_util.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
DECLARE_int32(ksp_group_deploy_type);
DECLARE_bool(open_chain_log);

namespace ks {
namespace ad_base {
DECLARE_string(perf_name_space);
}  // namespace ad_base
}  // namespace ks

namespace ks {
namespace front_server {
namespace {
DEFINE_JSON_NODE_KCONF(ad.engine, kessGrpcClients);
}

bool AdFrontProcessPreparePostProcMixer::InitProcessor() {
  return true;
}

void AdFrontProcessPreparePostProcMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  auto context_data_ptr = context->GetMutablePtrCommonAttr<ContextData>("context_data_from_ps");
  auto front_req =
      context->GetMutablePtrCommonAttr<kuaishou::ad::FrontServerRequest>("front_request");
  auto front_resp =
      context->GetMutablePtrCommonAttr<kuaishou::ad::FrontServerResponse>("front_response");
  auto start_ts = context->GetIntCommonAttr("front_start_us").value_or(0);
  if (context_data_ptr == nullptr || front_req == nullptr || front_resp == nullptr) {
    LOG(ERROR) << "context_data from ps is nullptr";
    context->SetIntCommonAttr("context_init_error", 1);
    return;
  }
  ContextData& context_data = *(context_data_ptr);
  context_data.InitializeAfterExtDeps(context);
  if (context_data.get_context_init_error()) {
    context_data.dot_perf->Count(1, "context_init_error");
    return;
  }
  YLOG_PB("adFrontServer", "first", context_data.get_llsid(), context_data.get_user_id(),
          ks::ad::ylog::LOG_TYPE::request, *front_req);
  // send chain kafka
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_chain_log &&
      ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName().empty()) {
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceLog(
        "request", true, "adFrontServer", "", context_data.get_user_id(), context_data.get_llsid(),
        ks::ad_base::massage_to_json(*front_req));
  }
  context_data.set_front_server_response(front_resp);
  context_data.set_start_ts(start_ts);
  context_data.dot_perf->Interval(base::GetTimestamp() - start_ts, "front_server.context_init_latency",
                                  ad_base::util::GetServiceVersionName(), ad_base::util::GetMyPodName());
  if (!SPDM_universeFlowTypeCheckAdvanced() &&
      !ks::engine_base::FlowChecker::Instance()->AcceptFlowType(front_req->flow_type())) {
    return;
  }
  // 解析请求
  ParseRequest(*front_req);
  const auto& device_info = front_req->universe_request().device_info();
  context_data.mutable_ad_klog()->Init(ContextInfo::GetServiceName(), serving_base::GetHostName(),
                            std::to_string(context_data.get_llsid()), device_info.device_id(),
                            device_info.imei(), device_info.imei_md5(), device_info.oaid(),
                            device_info.android_id(), device_info.android_id_md5(), device_info.idfa());

  std::function<int32_t()> time_left_func = nullptr;
  context_data.mutable_ad_klog()->AddLog(
      context_data.mutable_ad_klog()->IsWhite() ? front_req->ShortDebugString() : "");

  // 请求处理
  std::string graph_name = GetGraphName(context_data);
  context_data.dot_perf->Count(1, "ad_front.graph_name", graph_name,
      kuaishou::ad::AdEnum_AdRequestFlowType_Name(front_req->flow_type()));
  ks::ad_base::LocalKtraceSpan::AddTagToTopSpan("graph", graph_name);
  YLOG_LANE_ID("adFrontServer.graphName", context_data.get_llsid(), graph_name);
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_chain_log &&
      ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName().empty()) {
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceOther(
        "adFrontServer.graphName", context_data.get_llsid(), graph_name);
  }

  // 媒体封禁提前到这
  if (context_data.get_medium_valid_err_code() !=
          ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::SUCCESS) {
    if (front_resp != nullptr) {
      front_resp->mutable_universe_response()->set_error_code(context_data.get_medium_valid_err_code());
      context_data.dot_perf->Count(1, "ad_front.error_request_front_new",
          absl::StrCat(context_data.get_medium_valid_err_code()),
          context_data.UniverseRequest().app_info().app_id());
      context->SetStringCommonAttr("ad_front_response", front_resp->SerializeAsString());
      return;
    }
  }

  // 用于流程控制 1 能进图
  context->SetIntCommonAttr("front_process_status", 1);
  // 相关信息注入
  context->SetIntCommonAttr("is_multi_quota_flow", context_data.get_is_multi_quota_flow());
  context->SetStringCommonAttr("front_graph_name", graph_name);
  return;
}

std::string AdFrontProcessPreparePostProcMixer::GetTestAndPreviewGraphName(const ContextData& context_data) {
  // 测试白名单
  if (context_data.get_for_test()) {
    if (FrontKconfUtil::testWhiteListUniverse()->IsTestStyle(context_data.copy_test_device_id())) {
      LOG(INFO) << "test device id : " << context_data.borrow_test_device_id() << " in universe whitelist";
      if (context_data.RequestType() != kuaishou::ad::FrontRequestType::UNIVERSE_REQUEST) {
        context_data.dot_perf->Count(1, "misaligned_graph_and_request_type", "TestUniverseStyleRequestV2");
      }
      return "TestUniverseStyleRequestV2";
    }
  }
  if (context_data.get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    // 体验白名单
    LOG(INFO) << "userid : " << context_data.get_user_id() << " is in preview list";
    if (context_data.RequestType() != kuaishou::ad::FrontRequestType::UNIVERSE_REQUEST) {
      context_data.dot_perf->Count(1, "misaligned_graph_and_request_type", "TestUniverseStyleRequestV2");
    }
    return "TestUniverseStyleRequestV2";
  }
  return "";
}

std::string AdFrontProcessPreparePostProcMixer::GetGraphName(const ContextData& context_data) {
  if (context_data.UniverseRequest().universe_debug_param().debug_mode()) {
    if (context_data.RequestType() != kuaishou::ad::FrontRequestType::UNIVERSE_REQUEST) {
      context_data.dot_perf->Count(1, "misaligned_graph_and_request_type", "UniverseDebug");
    }
    return "UniverseDebug";
  }
  if (context_data.get_for_test() ||
      context_data.get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    auto graph_name = GetTestAndPreviewGraphName(context_data);
    if (!graph_name.empty()) {
      ContextData * context = const_cast<ContextData *>(&context_data);
      if (context != nullptr)
        context->set_enable_rank_pass_through_v2(false);
      return graph_name;
    }
  }

  if (context_data.get_is_trace_api_detection()) {
    return "TraceApiDetectionRequest";
  }

  return "UniverseRequestV2";
}

// 框架初始化
void AdFrontProcessPreparePostProcMixer::ParseRequest(const kuaishou::ad::FrontServerRequest& request) {
  ks::ad_athena::PerfRequest(request.from(), &request.universe_request());
}

}  // namespace front_server
}  // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdFrontProcessPreparePostProcMixer,
    ::ks::front_server::AdFrontProcessPreparePostProcMixer);
