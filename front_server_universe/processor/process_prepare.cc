#include "teams/ad/front_server_universe/processor/process_prepare.h"

#include <algorithm>
#include <string>
#include <utility>

#include "absl/strings/str_cat.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_athena/utils/utility/perf_util.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/klog/klog.h"
#include "teams/ad/ad_base/src/ktrace/local_ktrace_span.h"
#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_proto/common/product_platform.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_pack_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"
#include "teams/ad/engine_base/flow_control/flow_checker.h"
#include "teams/ad/engine_base/dragon_node/skydome_node.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/util/ad_server_show/ad_server_show_stat.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/universe_traffic_control/universe_traffic_control.h"
#include "teams/ad/front_server_universe/util/utility/ksn_util.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
DECLARE_int32(ksp_group_deploy_type);
DECLARE_bool(open_chain_log);

namespace ks {
namespace ad_base {
DECLARE_string(perf_name_space);
}  // namespace ad_base
}  // namespace ks

namespace ks {
namespace front_server {
namespace {
DEFINE_JSON_NODE_KCONF(ad.engine, kessGrpcClients);
}
AdFrontProcessPrepareMixer::AdFrontProcessPrepareMixer() {
  ks::ad_athena::SetAdFullLinkPosInfoEvent("ad.toAthena");
}

bool AdFrontProcessPrepareMixer::InitProcessor() {
  return true;
}

void AdFrontProcessPrepareMixer::OnPipelineExit(ks::platform::ReadableRecoContextInterface* context) {
  if (ps_context_ != nullptr) {
    ps_context_->Clear();
  }
}

void AdFrontProcessPrepareMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  ps_context_ = nullptr;
  auto llsid = context->GetIntCommonAttr("llsid");
  auto user_id = context->GetIntCommonAttr("user_id");
  auto request_str = context->GetStringCommonAttr("ad_front_request");
  int64 start_ts = base::GetTimestamp();
  auto rpc_arena = std::make_shared<google::protobuf::Arena>();
  auto front_req =
      google::protobuf::Arena::CreateMessage<kuaishou::ad::FrontServerRequest>(rpc_arena.get());
  auto front_resp =
      google::protobuf::Arena::CreateMessage<::kuaishou::ad::FrontServerResponse>(rpc_arena.get());
  int64_t front_time_out_ms = 480;
  context->SetPtrCommonAttr("rpc_arena", rpc_arena);  // 先设置, 保障晚释放
  context->SetIntCommonAttr("front_process_status", 0);
  if (!request_str) {
    LOG_EVERY_N(INFO, 100) << "TTTT request not found "
              << " user_id:" << user_id.value_or(0) << " llsid: " << llsid.value_or(0);
    return;
  }
  if (!front_req->ParseFromArray(request_str->data(), request_str->size())) {
    LOG_EVERY_N(INFO, 100) << "TTTT parse error "
              << " user_id:" << user_id.value_or(0) << " llsid: " << llsid.value_or(0);
    return;
  }
  ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "process_count");

  // front 超时情况打点
  if (kessGrpcClients() != nullptr) {
    context->SetIntCommonAttr("front_start_us", start_ts);
    // 根据配置获取 time_out
    auto config_json = kessGrpcClients()->data;
    if (config_json != nullptr && config_json->IsArray()) {
      for (auto it = config_json->array_begin(); it != config_json->array_end(); ++it) {
        const base::Json* json = *it;
        if (json != nullptr && json->IsObject()) {
          std::string kess_name = json->GetString("kess_name", "");
          if (kess_name == "ad-front-server-universe") {
            front_time_out_ms = json->GetInt("time_out", 480);
            context->SetIntCommonAttr("front_time_out_ms", front_time_out_ms);
            break;
          }
        }
      }
    }
  }

  // 联盟流量控制, 平迁 openapi 的流量策略
  std::shared_ptr<UniverseTrafficControlContext> universe_traffic_control_context{
      new UniverseTrafficControlContext()};
  bool is_universe_traffic_control_target_flow = UniverseTrafficControl::Instance().IsTargetTraffic(
      *front_req, universe_traffic_control_context);
  if (is_universe_traffic_control_target_flow &&
      !UniverseTrafficControl::Instance().Allow(*front_req, front_resp,
                                                universe_traffic_control_context)) {
    if (front_resp != nullptr) {
      context->SetStringCommonAttr("ad_front_response", front_resp->SerializeAsString());
    }
    // 命中反向 cache 的超时情况打点
    if (front_resp != nullptr) {
      int64_t front_total_cost_us = base::GetTimestamp() - start_ts;
      std::string status_str = front_total_cost_us < front_time_out_ms * 1000 ? "0" : "1";
      std::string summary_str = absl::StrCat("use_cache:0,neg_cache:1,invalid_flag:",
                                            front_resp->universe_response().admit_invalid_type());
      ks::infra::PerfUtil::IntervalLogStash(front_total_cost_us,
                                            "ad.ad_front",
                                            "universe_down_stream_node_timeout",
                                            "front_server",
                                            status_str,
                                            "no_ads",
                                            summary_str);
    }
    return;
  }

  front_resp->set_type(front_req->type());

  // 注入 front_server_request,front_server_response
  thread_local std::thread::id thread_id = std::this_thread::get_id();
  ps_context_ = &ContextPoolTypeMore::Instance().AcquireContext(thread_id);
  ps_context_->ResetRpc(front_req, front_resp);  // 移除 GetRPCResponse 后可以不设置
  ContextData& context_data = *(ps_context_->GetMutableContextData<ContextData>());
  context_data.InitializeDragonContext(context);  // 先初始化 dragon 的 context
  context_data.Initialize(*front_req, "", std::move(universe_traffic_control_context),
                          this, context);
  if (context_data.get_context_init_error()) {
    context_data.dot_perf->Count(1, "context_init_error");
    return;
  }
  YLOG_PB("adFrontServer", "first", context_data.get_llsid(), context_data.get_user_id(),
          ks::ad::ylog::LOG_TYPE::request, *front_req);
  // send chain kafka
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_chain_log &&
      ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName().empty()) {
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceLog(
        "request", true, "adFrontServer", "", context_data.get_user_id(), context_data.get_llsid(),
        ks::ad_base::massage_to_json(*front_req));
  }
  context_data.set_front_server_response(front_resp);
  context_data.set_start_ts(start_ts);
  context_data.dot_perf->Interval(base::GetTimestamp() - start_ts, "front_server.context_init_latency",
                                  ad_base::util::GetServiceVersionName(), ad_base::util::GetMyPodName());
  if (!SPDM_universeFlowTypeCheckAdvanced() &&
      !ks::engine_base::FlowChecker::Instance()->AcceptFlowType(front_req->flow_type())) {
    return;
  }
  // 解析请求
  ParseRequest(*front_req);
  const auto& device_info = front_req->universe_request().device_info();
  context_data.mutable_ad_klog()->Init(ContextInfo::GetServiceName(), serving_base::GetHostName(),
                            std::to_string(context_data.get_llsid()), device_info.device_id(),
                            device_info.imei(), device_info.imei_md5(), device_info.oaid(),
                            device_info.android_id(), device_info.android_id_md5(), device_info.idfa());

  std::function<int32_t()> time_left_func = nullptr;
  context_data.mutable_ad_klog()->AddLog(
      context_data.mutable_ad_klog()->IsWhite() ? front_req->ShortDebugString() : "");

  // 请求处理
  std::string graph_name = GetGraphName(context_data);
  context_data.dot_perf->Count(1, "ad_front.graph_name", graph_name,
      kuaishou::ad::AdEnum_AdRequestFlowType_Name(front_req->flow_type()));
  ks::ad_base::LocalKtraceSpan::AddTagToTopSpan("graph", graph_name);
  YLOG_LANE_ID("adFrontServer.graphName", context_data.get_llsid(), graph_name);
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_chain_log &&
      ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName().empty()) {
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceOther(
        "adFrontServer.graphName", context_data.get_llsid(), graph_name);
  }

  // 媒体封禁提前到这
  if (context_data.get_medium_valid_err_code() !=
          ::kuaishou::ad::UniverseErrorCodeMessage::UniverseErrorCodeMessage::SUCCESS) {
    if (front_resp != nullptr) {
      front_resp->mutable_universe_response()->set_error_code(context_data.get_medium_valid_err_code());
      context_data.dot_perf->Count(1, "ad_front.error_request_front_new",
          absl::StrCat(context_data.get_medium_valid_err_code()),
          context_data.UniverseRequest().app_info().app_id());
      context->SetStringCommonAttr("ad_front_response", front_resp->SerializeAsString());
      return;
    }
  }

  // 用于流程控制 1 能进图
  context->SetIntCommonAttr("front_process_status", 1);
  // 相关信息注入
  context->SetIntCommonAttr("is_universe_traffic_control_target_flow",
      is_universe_traffic_control_target_flow);
  context->SetIntCommonAttr("is_multi_quota_flow", context_data.get_is_multi_quota_flow());
  context->SetStringCommonAttr("front_graph_name", graph_name);
  context->SetPtrCommonAttr("front_request", front_req);
  context->SetPtrCommonAttr("front_response", front_resp);
  // 大部分算子只有 context 存在的时候才运行
  ks::engine_base::dragon::SkydomeBaseNode::RegisterAdContext(context, ps_context_);
  return;
}

std::string AdFrontProcessPrepareMixer::GetTestAndPreviewGraphName(const ContextData& context_data) {
  // 测试白名单
  if (context_data.get_for_test()) {
    if (FrontKconfUtil::testWhiteListUniverse()->IsTestStyle(context_data.copy_test_device_id())) {
      LOG(INFO) << "test device id : " << context_data.borrow_test_device_id() << " in universe whitelist";
      if (context_data.RequestType() != kuaishou::ad::FrontRequestType::UNIVERSE_REQUEST) {
        context_data.dot_perf->Count(1, "misaligned_graph_and_request_type", "TestUniverseStyleRequestV2");
      }
      return "TestUniverseStyleRequestV2";
    }
  }
  if (context_data.get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    // 体验白名单
    LOG(INFO) << "userid : " << context_data.get_user_id() << " is in preview list";
    if (context_data.RequestType() != kuaishou::ad::FrontRequestType::UNIVERSE_REQUEST) {
      context_data.dot_perf->Count(1, "misaligned_graph_and_request_type", "TestUniverseStyleRequestV2");
    }
    return "TestUniverseStyleRequestV2";
  }
  return "";
}

std::string AdFrontProcessPrepareMixer::GetGraphName(const ContextData& context_data) {
  if (context_data.UniverseRequest().universe_debug_param().debug_mode()) {
    if (context_data.RequestType() != kuaishou::ad::FrontRequestType::UNIVERSE_REQUEST) {
      context_data.dot_perf->Count(1, "misaligned_graph_and_request_type", "UniverseDebug");
    }
    return "UniverseDebug";
  }
  if (context_data.get_for_test() ||
      context_data.get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE) {
    auto graph_name = GetTestAndPreviewGraphName(context_data);
    if (!graph_name.empty()) {
      ContextData * context = const_cast<ContextData *>(&context_data);
      if (context != nullptr)
        context->set_enable_rank_pass_through_v2(false);
      return graph_name;
    }
  }

  if (context_data.get_is_trace_api_detection()) {
    return "TraceApiDetectionRequest";
  }

  return "UniverseRequestV2";
}

// 框架初始化
void AdFrontProcessPrepareMixer::ParseRequest(const kuaishou::ad::FrontServerRequest& request) {
  ks::ad_athena::PerfRequest(request.from(), &request.universe_request());
}

}  // namespace front_server
}  // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdFrontProcessPrepareMixer,
    ::ks::front_server::AdFrontProcessPrepareMixer);
