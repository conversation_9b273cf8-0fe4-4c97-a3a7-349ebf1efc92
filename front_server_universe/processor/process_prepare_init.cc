#include "teams/ad/front_server_universe/processor/process_prepare_init.h"

#include <algorithm>
#include <memory>
#include <string>
#include <utility>

#include "absl/strings/str_cat.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_athena/utils/utility/perf_util.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_proto/common/product_platform.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/util/ad_server_show/ad_server_show_stat.h"
#include "teams/ad/front_server_universe/util/universe_traffic_control/universe_traffic_control.h"
#include "teams/ad/front_server_universe/util/utility/ksn_util.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
DECLARE_int32(ksp_group_deploy_type);
DECLARE_bool(open_chain_log);

namespace ks {
namespace ad_base {
DECLARE_string(perf_name_space);
}  // namespace ad_base
}  // namespace ks

namespace ks {
namespace front_server {
namespace {
DEFINE_JSON_NODE_KCONF(ad.engine, kessGrpcClients);
}
AdFrontProcessPrepareInitMixer::AdFrontProcessPrepareInitMixer() {
  ks::ad_athena::SetAdFullLinkPosInfoEvent("ad.toAthena");
}

bool AdFrontProcessPrepareInitMixer::InitProcessor() {
  return true;
}

void AdFrontProcessPrepareInitMixer::OnPipelineExit(ks::platform::ReadableRecoContextInterface* context) {
  if (ps_context_ != nullptr) {
    ps_context_->Clear();
  }
}

void AdFrontProcessPrepareInitMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  ps_context_ = nullptr;
  auto llsid = context->GetIntCommonAttr("llsid");
  auto user_id = context->GetIntCommonAttr("user_id");
  auto request_str = context->GetStringCommonAttr("ad_front_request");
  int64 start_ts = base::GetTimestamp();
  auto rpc_arena = std::make_shared<google::protobuf::Arena>();
  auto front_req =
      google::protobuf::Arena::CreateMessage<kuaishou::ad::FrontServerRequest>(rpc_arena.get());
  auto front_resp =
      google::protobuf::Arena::CreateMessage<::kuaishou::ad::FrontServerResponse>(rpc_arena.get());
  int64_t front_time_out_ms = 480;
  context->SetPtrCommonAttr("rpc_arena", rpc_arena);  // 先设置, 保障晚释放
  context->SetIntCommonAttr("front_process_status", 0);
  context->SetPtrCommonAttr("front_request", front_req);
  context->SetPtrCommonAttr("front_response", front_resp);
  if (!request_str) {
    LOG_EVERY_N(INFO, 100) << "TTTT request not found "
              << " user_id:" << user_id.value_or(0) << " llsid: " << llsid.value_or(0);
    return;
  }
  if (!front_req->ParseFromArray(request_str->data(), request_str->size())) {
    LOG_EVERY_N(INFO, 100) << "TTTT parse error "
              << " user_id:" << user_id.value_or(0) << " llsid: " << llsid.value_or(0);
    return;
  }
  ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_front", "process_count");

  // front 超时情况打点
  context->SetIntCommonAttr("front_start_us", start_ts);
  if (kessGrpcClients() != nullptr) {
    // 根据配置获取 time_out
    auto config_json = kessGrpcClients()->data;
    if (config_json != nullptr && config_json->IsArray()) {
      for (auto it = config_json->array_begin(); it != config_json->array_end(); ++it) {
        const base::Json* json = *it;
        if (json != nullptr && json->IsObject()) {
          std::string kess_name = json->GetString("kess_name", "");
          if (kess_name == "ad-front-server-universe") {
            front_time_out_ms = json->GetInt("time_out", 480);
            context->SetIntCommonAttr("front_time_out_ms", front_time_out_ms);
            break;
          }
        }
      }
    }
  }

  // 联盟流量控制, 平迁 openapi 的流量策略
  std::shared_ptr<UniverseTrafficControlContext> universe_traffic_control_context{
      new UniverseTrafficControlContext()};
  bool is_universe_traffic_control_target_flow = UniverseTrafficControl::Instance().IsTargetTraffic(
      *front_req, universe_traffic_control_context);
  if (is_universe_traffic_control_target_flow &&
      !UniverseTrafficControl::Instance().Allow(*front_req, front_resp,
                                                universe_traffic_control_context)) {
    if (front_resp != nullptr) {
      context->SetStringCommonAttr("ad_front_response", front_resp->SerializeAsString());
    }
    // 命中反向 cache 的超时情况打点
    if (front_resp != nullptr) {
      int64_t front_total_cost_us = base::GetTimestamp() - start_ts;
      std::string status_str = front_total_cost_us < front_time_out_ms * 1000 ? "0" : "1";
      std::string summary_str = absl::StrCat("use_cache:0,neg_cache:1,invalid_flag:",
                                            front_resp->universe_response().admit_invalid_type());
      ks::infra::PerfUtil::IntervalLogStash(front_total_cost_us,
                                            "ad.ad_front",
                                            "universe_down_stream_node_timeout",
                                            "front_server",
                                            status_str,
                                            "no_ads",
                                            summary_str);
    }
    return;
  }

  front_resp->set_type(front_req->type());

  // 信息注入
  context->SetIntCommonAttr("is_universe_traffic_control_target_flow",
    is_universe_traffic_control_target_flow);

  // 注入 front_server_request,front_server_response
  thread_local std::thread::id thread_id = std::this_thread::get_id();
  ps_context_ = &ContextPoolTypeMore::Instance().AcquireContext(thread_id);
  ps_context_->ResetRpc(front_req, front_resp);  // 移除 GetRPCResponse 后可以不设置
  ContextData* context_data_ptr = ps_context_->GetMutableContextData<ContextData>();
  context->SetPtrCommonAttr("context_data_from_ps", context_data_ptr);
  ContextData& context_data = *(context_data_ptr);
  context_data.InitializeDragonContext(context);  // 先初始化 dragon 的 context
  context_data.InitializeBeforeExtDeps(*front_req, std::move(universe_traffic_control_context),
                                       this, context);
  // 大部分算子只有 context 存在的时候才运行
  ks::engine_base::dragon::SkydomeBaseNode::RegisterAdContext(context, ps_context_);

  // 策略正排接入相关
  auto* pv_item_table = context->GetOrInsertDataTable("pv_item_table");
  auto item = pv_item_table->AddCommonRecoResult(llsid.value_or(0), 0, 0, 0);
  auto position_id_attr = pv_item_table->GetOrInsertAttr("position_id");
  item.SetIntAttr(position_id_attr, context_data.get_ud_pos_id());
  context->SetIntCommonAttr("position_id", context_data.get_ud_pos_id());
  return;
}

}  // namespace front_server
}  // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdFrontProcessPrepareInitMixer,
    ::ks::front_server::AdFrontProcessPrepareInitMixer);
