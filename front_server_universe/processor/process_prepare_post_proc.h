#pragma once
#include <string>

#include "base/common/basic_types.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/engine/server/init.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"

namespace ks {
namespace front_server {

class AdFrontProcessPreparePostProcMixer : public ::ks::platform::CommonRecoBaseMixer {
 public:
  AdFrontProcessPreparePostProcMixer() {}
  ~AdFrontProcessPreparePostProcMixer() = default;
  // dragon 接口
  void Mix(ks::platform::AddibleRecoContextInterface* context) override;

 private:
  bool InitProcessor() override;
  // 解析请求
  void ParseRequest(const kuaishou::ad::FrontServerRequest& front_server_request);

  std::string GetGraphName(const ContextData& context_data);
  std::string GetTestAndPreviewGraphName(const ContextData& context_data);

  const std::string host_{serving_base::GetHostName()};

 private:
  DISALLOW_COPY_AND_ASSIGN(AdFrontProcessPreparePostProcMixer);
};  // class AdFrontProcessPreparePostProcMixer

}  // namespace front_server
}  // namespace ks
