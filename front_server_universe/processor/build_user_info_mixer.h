#pragma once

#include <string>
#include <utility>

#include "dragon/src/processor/base/common_reco_base_mixer.h"
#include "dragon/src/processor/base/common_reco_base_processor.h"


namespace ks {
namespace front_server {

class BuildUserInfoMixer : public ::ks::platform::CommonRecoBaseMixer {
 public:
  BuildUserInfoMixer() {}
  ~BuildUserInfoMixer() = default;
  // dragon 接口
  void Mix(ks::platform::AddibleRecoContextInterface* context) override;
  bool InitProcessor() override {
    return true;
  }

  template <typename Response, typename Callback>
  void RegisterAsyncCallbackExternal(ks::platform::ReadableRecoContextInterface *context,
    ks::kess::rpc::grpc::Future<Response *> future, Callback &&callback) {
    RegisterAsyncCallback(context, std::move(future), std::forward<Callback>(callback));
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(BuildUserInfoMixer);
};

}  // namespace front_server
}  // namespace ks
