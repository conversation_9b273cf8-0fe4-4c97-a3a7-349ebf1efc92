#include "teams/ad/front_server_universe/processor/build_user_info_mixer.h"

#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/util/ad_user_info/ad_user_info_builder.h"

namespace ks {
namespace front_server {

void BuildUserInfoMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  ContextData* context_data_ptr = context->GetMutablePtrCommonAttr<ContextData>("context_data_from_ps");
  if (context_data_ptr == nullptr) {
    LOG(ERROR) << "context_data from ps is nullptr";
    context->SetIntCommonAttr("context_init_error", 1);
    return;
  }
  // 异步调用用户画像
  ContextData& session_data_ = *(context_data_ptr);
  AdUserInfoBuilder::BuildUserInfo(session_data_, AD_FLOW_TYPE::AD_UNIVERSE_FLOW,
    session_data_.mutable_ad_request()->mutable_ad_user_info(), this, context);
}

}  // namespace front_server
}  // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, BuildUserInfoMixer,
    ::ks::front_server::BuildUserInfoMixer);
