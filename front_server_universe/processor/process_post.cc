#include "teams/ad/front_server_universe/processor/process_post.h"

#include <algorithm>
#include <string>
#include <unordered_map>
#include <utility>
#include <map>
#include <vector>

#include "absl/strings/str_cat.h"
#include "absl/time/clock.h"
#include "base/encoding/url_encode.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_athena/utils/utility/perf_util.h"
#include "teams/ad/ad_base/src/common/deploy_utils.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/klog/klog.h"
#include "teams/ad/ad_base/src/ktrace/local_ktrace_span.h"
#include "teams/ad/ad_base/src/pb_helper/pb_helper.h"
#include "teams/ad/ad_base/src/util/fill_service_version.h"
#include "teams/ad/ad_proto/common/product_platform.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_pack_service.kess.grpc.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_trace_log.pb.h"
#include "teams/ad/engine_base/fanstop_common/pv_debug/trace_info_io.h"
#include "teams/ad/engine_base/flow_control/flow_checker.h"
#include "teams/ad/engine_base/dragon_node/skydome_node.h"
#include "teams/ad/front_server_universe/engine/context_data/context_data.h"
#include "teams/ad/front_server_universe/trace/ad_front_simplify_trace_log.h"
#include "teams/ad/front_server_universe/util/ad_server_show/ad_server_show_stat.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/universe_rank_cache/universe_rank_cache.h"
#include "teams/ad/front_server_universe/util/universe_traffic_control/universe_traffic_control.h"
#include "teams/ad/front_server_universe/util/utility/ksn_util.h"
#include "teams/ad/front_server_universe/util/utility/utils.h"
#include "teams/ad/front_server_universe/util/universe_computility_data_log/universe_computility_data_log.h"
#include "teams/ad/front_server_universe/util/universe_qcpx_delivery_data_log/universe_qcpx_delivery_data_log.h"
#include "teams/ad/ad_proto/kuaishou/ad/universe_delivered_qpon_info.pb.h"
#include "teams/ad/front_server_universe/bg_task/universe_win_rate_model_data/win_rate_model_data.h"

DECLARE_int32(ksp_group_deploy_type);
DECLARE_bool(open_chain_log);

namespace ks {
namespace ad_base {
DECLARE_string(perf_name_space);
}  // namespace ad_base
}  // namespace ks

namespace ks {
namespace front_server {
AdFrontProcessPostMixer::AdFrontProcessPostMixer() {
  ks::ad_athena::SetAdFullLinkPosInfoEvent("ad.toAthena");
}

bool AdFrontProcessPostMixer::InitProcessor() {
  return true;
}
void AdFrontProcessPostMixer::Mix(ks::platform::AddibleRecoContextInterface* context) {
  auto front_request =
      context->GetMutablePtrCommonAttr<kuaishou::ad::FrontServerRequest>("front_request");
  auto front_response =
      context->GetMutablePtrCommonAttr<kuaishou::ad::FrontServerResponse>("front_response");

  auto ps_context = ks::engine_base::dragon::SkydomeBaseNode::GetAdContext<AdContext>(context);
  if (!ps_context) {
    return;
  }
  auto* context_data = ps_context->GetMutableContextData<ContextData>();
  if (!context_data) {
    return;
  }

  // front 超时情况打点
  {
    int64_t front_time_out_ms = 480;
    int64_t front_total_cost_us = 0;
    /*
      summary_str 含义：
      use_cache 是否使用精排缓存
      neg_cache 是否命中负向缓存
      invalid_flag 可用来标识被流量优选过滤等情况，含义见 AdmitInvalidType
    */
    std::string summary_str = "";
    std::string status_str = "";
    front_time_out_ms = context->GetIntCommonAttr("front_time_out_ms").value_or(480);
    auto front_start_us = context->GetIntCommonAttr("front_start_us").value_or(0);
    if (front_start_us == 0) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.ad_front", "invalid_front_start_us");
    } else {
      front_total_cost_us = base::GetTimestamp() - front_start_us;
      status_str = front_total_cost_us < front_time_out_ms * 1000 ? "0" : "1";
    }
    summary_str = absl::StrCat("use_cache:", context_data->get_is_universe_cached_res(),
                              ",neg_cache:0",
                              ",invalid_flag:", context_data->GetAdInvalidFlag());
    context_data->dot_perf->Interval(front_total_cost_us,
      "universe_down_stream_node_timeout",
      "front_server",
      status_str,
      context_data->get_ad_list().Size() > 0 ? "has_ads" : "no_ads",
      summary_str);
  }

  ks::infra::PerfUtil::IntervalLogStash(1, "ad.ad_pack", "statistic_pvr", "request_num", "", "", "", "",
                                        absl::StrCat(context_data->get_sub_page_id()));
  std::string abort_node_name(context->GetStringCommonAttr("abort_node_name").value_or(""));
  if (!abort_node_name.empty()) {
    context_data->dot_perf->Count(1, "abort_node_name", abort_node_name);
  } else {
    context_data->dot_perf->Count(1, "abort_node_name", "all_pass");
  }
  if (context_data->get_enable_combo_search_req_ad_pack() &&
      (context_data->get_ad_list().Size() == 0 ||
       context_data->get_ad_request()->search_info().request_source() == 1)) {
    context_data->set_enable_combo_search_req_ad_pack(false);
    context_data->dot_perf->Count(1, "combo_delivery_fix");
  }
  bool is_universe_traffic_control_target_flow =
      context->GetIntCommonAttr("is_universe_traffic_control_target_flow").value_or(0);
  int64_t total_cost_us = 0;
  int64_t total_node_ts = 0;

  context_data->mutable_ad_klog()->AddLog(
      context_data->mutable_ad_klog()->IsWhite() ? front_response->ShortDebugString() : "");
  if (context_data->GetPassPreFilter()) {
    Post(*front_response, *context_data);
  }
  // 联盟流量控制, 平迁 openapi 的流量策略
  if (is_universe_traffic_control_target_flow && front_request != nullptr) {
    int64_t reason = static_cast<int64_t>(context_data->GetAdInvalidFlag());
    UniverseTrafficControl::Instance().OpenApiMigratedMark(*front_request, reason);
  }

  LogInfo(*front_request, *front_response, context_data);
  // 单独加一个针对 dryrun/ponstonline 指标使用的打点，其它的不要在这个时面打，后期前面无用的打点慢慢下线
  LogDryrunInfo(*front_request, *front_response, context_data);

  RequestAdPackService(*context_data, front_response);

  SendComputilityData(context_data);

  // [xiaowentao] 写入竞胜率模型的结果到 redis，用于后续的动态分成
  UniverseWinRateModelData::GetInstance()->WriteWinRateModelDataToRedis(context_data);

  SendUniverseQcpxDeliveryLogInfo(context_data);

  context_data->mutable_ad_klog()->Produce();
  context_data->mutable_ad_klog()->Clear();

  total_cost_us = base::GetTimestamp() - context_data->get_start_ts();
  auto &ad_perf_info = *context_data->mutable_ad_perf_info();
  ad_perf_info.SetServerTime(total_cost_us / 1000);
  const auto &node_time_cost = context_data->get_node_time_cost();
  for (int i = 0; i < node_time_cost.size() && i < 50; i++) {
    auto* node = ad_perf_info.perf_info.add_node_cost();
    node->CopyFrom(node_time_cost[i]);
  }
  AdFrontSimplifyTraceLogManager::GetInstance()->SendTraceLog(context_data);

  bool is_long_tail_handle = total_cost_us > FrontKconfUtil::LongTailTimeThreshold();
  // add front response log
  YLOG_PB("adFrontServer", "first", context_data->get_llsid(), context_data->get_user_id(),
          ks::ad::ylog::LOG_TYPE::response, *(front_response));
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_chain_log &&
      ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName().empty()) {
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceLog(
        "response", true, "adFrontServer", "", context_data->get_user_id(), context_data->get_llsid(),
        ks::ad_base::massage_to_json(*(front_response)));
  }
  context_data->dot_perf->Interval(total_cost_us - total_node_ts,
                                   "front_server.front_without_node_cost_latency",
                                   is_long_tail_handle ? "1" : "0");
  context_data->dot_perf->Interval(total_cost_us, "front_server.total_cost_latency",
                                   is_long_tail_handle ? "1" : "0",
                                   context_data->get_is_universe_cached_res() ? "1" : "0", "", "", false);
  context_data->dot_perf->Interval(total_cost_us, "front_server.total_cost_latency_with_ab",
      absl::StrCat(SPDM_max_hard_quota(context_data->get_spdm_ctx()),
      ":", SPDM_max_soft_quota(context_data->get_spdm_ctx())));
  if (context_data->get_ud_is_universe_tiny_flow()) {
    context_data->dot_perf->Interval(total_cost_us, "front_server.universe_tiny_flow_total_cost");
  }

  if (context_data->get_is_universe_adn_flow()) {
    context_data->dot_perf->Count(1, "top_pos_non_update_cache",
        context_data->get_ud_is_top_pos() ? "top_pos" : "non_top_pos",
        context_data->copy_ud_cache_abtest_key(),
        absl::StrCat("invalid_flag_", context_data->GetAdInvalidFlag()));
  }
  context_data->dot_perf->Interval(
      total_cost_us, "universe_hold_and_cache_cost_time",
      context_data->get_ud_hold_and_cache() ? "hold_and_cache" : "normal");
  if (ad_base::AdRandom::GetInt(0, 999) < FrontKconfUtil::showNodeTimecostRatio()) {
    context_data->dot_perf->Count(1, "ad_invalid_flag",
        std::to_string(context_data->GetAdInvalidFlag()));
    context_data->dot_perf->Count(1, "universe_merge_req_erase_key",
        std::to_string(context_data->get_universe_request_merge_info().need_erase));
  }
  bool has_timeout_field = context_data->UniverseRequest().timeout() > 0;
  std::string status_str = total_cost_us < context_data->UniverseRequest().timeout() * 1000 ? "0" : "1";
  std::string summary_str = absl::StrCat("use_cache:", context_data->get_is_universe_cached_res(),
                              has_timeout_field ? ",has_timeout" : ",no_timeout",
                              ",invalid_flag:", context_data->GetAdInvalidFlag(),
                              context_data->get_ad_list().Size() > 0 ? ",has_ads" : ",no_ads",
                              ",cooperation_mode:", context_data->get_pos_manager().GetCooperationMode());
  std::string exp_str = context_data->get_ud_timeout_exp_info().ToString();
  context_data->dot_perf->Count(1, "universe_down_stream_timeout_case", status_str,
      std::to_string(context_data->UniverseRequest().timeout() / 10 * 10),  // 缩小监控指标维度
      summary_str, exp_str);

  LOG_EVERY_N(INFO, 100) << "DRAGON  llsid: " << context_data->get_llsid()
                         << " explore dsp size: " << front_response->explore_response().ad_dsp_size()
                         << " follow dsp size: "
                         << front_response->follow_response().follow_dsp_response().ad_dsp_size()
                         << " universe size" << front_response->universe_response().imp_ad_info_size()
                         << " nearby size" << front_response->nearby_response().ad_dsp_size()
                         << ", cost_time: " << total_cost_us;
  context->SetStringCommonAttr("ad_front_response", front_response->SerializeAsString());
  return;
}

void AdFrontProcessPostMixer::SendComputilityData(ContextData* context_data) {
  UniverseComputilityData log_info;
  log_info.set_llsid(context_data->get_llsid());
  log_info.set_user_id(context_data->get_user_id());
  log_info.set_device_id(context_data->copy_device_id());
  log_info.set_write_timestamp(base::GetTimestamp());
  log_info.set_sub_page_id(context_data->get_sub_page_id());
  log_info.set_cooperation_mode(context_data->get_ud_cooperation_mode());
  log_info.set_ad_style(context_data->get_ud_ad_style());
  log_info.set_medium_uid(context_data->get_ad_request()->universe_ad_request_info().medium_uid());
  log_info.set_app_id_str(context_data->copy_ud_app_id());
  log_info.set_pos_id(context_data->get_ud_pos_id());
  log_info.set_quality_predict_cpm(context_data->get_universe_quality_data_predict_cpm());
  log_info.set_invalid_flag(context_data->GetAdInvalidFlag());
  log_info.set_is_cache(context_data->get_is_universe_cached_res());
  log_info.set_tmax(context_data->UniverseRequest().tmax());
  log_info.set_timeout(context_data->UniverseRequest().timeout());

  // 填充实验参数
  #define ADD_AB_BOOL_FLAT(FLAT) if (SPDM_##FLAT(context_data->get_spdm_ctx())) {\
    log_info.add_ab_flats(#FLAT);\
  }
  #define ADD_AB_FLAT(FLAT) { auto param = SPDM_##FLAT(context_data->get_spdm_ctx());\
  auto flat = absl::StrCat(#FLAT, "_", param);\
  log_info.add_ab_flats(flat);\
  }
  log_info.add_ab_flats("enable_universe_pid_control");
  ADD_AB_FLAT(universe_pid_solver_tp_ratio)
  // 填充算力决策字段
  auto* computility_agent_info = log_info.add_computility_agent_infos();
  computility_agent_info->CopyFrom(context_data->get_ad_response()->computility_agent_info());
  computility_agent_info->set_final_config_group_index(context_data->get_ad_request()->final_config_group_index());   // NOLINT
  computility_agent_info->set_retrieval_quota(context_data->get_ud_universe_elastic_info().max_retrieval_creatives);   // NOLINT
  computility_agent_info->set_prerank_quota(context_data->get_ud_universe_elastic_info().max_prerank_num);   // NOLINT
  computility_agent_info->set_rank_quota(context_data->get_ud_universe_elastic_info().max_retrieval_rank_num);   // NOLINT
  computility_agent_info->set_white_retrieval_tag_strategy(context_data->get_ud_universe_elastic_info().white_retrieval_tag_strategy);   // NOLINT
  computility_agent_info->set_raw_config_group_index(context_data->copy_ud_raw_config_group_index());

  // 填充模块耗时字段
  for (auto& moudle_info : context_data->get_ad_response()->moudle_time_infos()) {
    log_info.add_moudle_time_infos()->CopyFrom(moudle_info);
  }
  auto* front_time_info = log_info.add_moudle_time_infos();
  front_time_info->set_moudle_type(kuaishou::ad::UniverseComputilityData_MoudleType_AD_FRONT);
  if (!context_data->UniverseRequest().api_ad_cache() && context_data->UniverseRequest().timeout() > 0) {
    front_time_info->set_time_out(context_data->UniverseRequest().timeout());
  } else {
    front_time_info->set_time_out(480);
  }
  front_time_info->set_inner_process_time(base::GetTimestamp() - context_data->get_start_ts());
  front_time_info->set_ad_size(context_data->get_ad_list().Size());
  // 填充广告计费数据
  for (int i = 0; i < context_data->get_ad_list().Size(); i++) {
    RankAdCommon *ad_common = context_data->mutable_ad_list()->At(i);
    if (!ad_common) {
      continue;
    }
    auto *item = ad_common->GetResult();
    if (!item) {
      continue;
    }
    if (SPDM_enable_dynamic_share_log_server_show(context_data->get_spdm_ctx())) {
      if (0 == i) {
        // [xiaowentao] 动态分成下发阶段打点
        auto& exp_info = context_data->get_ud_dynamic_share_exp_info();
        auto rtb_coff = context_data->get_universe_rtb_coff();
        if (SPDM_enable_dynamic_share_log(context_data->get_spdm_ctx())) {
          computility_agent_info->set_base_rtb_ecpm(exp_info.base_rtb_ecpm());
          computility_agent_info->set_base_media_share(exp_info.base_media_share() * rtb_coff);
          computility_agent_info->set_opt_rtb_ecpm(exp_info.opt_rtb_ecpm());
          computility_agent_info->set_opt_media_share(exp_info.opt_media_share());
          computility_agent_info->set_opt_win_rate(exp_info.opt_win_rate());
        } else {
          // base 组，也使用分成 PID 调整后的分成系数
          auto share_ratio =
            context_data->get_ad_request()->universe_ad_request_info().share_ratio() * 1.0 / 10000;
          computility_agent_info->set_base_media_share(share_ratio);
          computility_agent_info->set_base_rtb_ecpm(ad_common->rtb_ecpm());
          computility_agent_info->set_opt_media_share(share_ratio * rtb_coff);
        }
      }
    }
    if (SPDM_enable_write_universe_dcaf_quota(context_data->get_spdm_ctx())) {
      if (i == 0) {
        const auto& ad_server_trans =
          item->ad_deliver_info().online_join_params_transparent().ad_server_trans_info();
        computility_agent_info->set_max_prerank_mum_before_dcaf(
          ad_server_trans.max_prerank_num_before_dcaf());
        computility_agent_info->set_max_prerank_num_dcaf(ad_server_trans.max_prerank_num());
        computility_agent_info->set_max_retrieval_creatives_before_dcaf(
          ad_server_trans.max_retrieval_creatives_before_dcaf());
        computility_agent_info->set_max_retrieval_creatives_dcaf(
          ad_server_trans.max_retrieval_creatives());
      }
    }
    if (SPDM_enable_dcaf_timeout_log(context_data->get_spdm_ctx())) {
      if (i == 0) {
        const auto& ad_server_trans =
          item->ad_deliver_info().online_join_params_transparent().ad_server_trans_info();
        computility_agent_info->set_dcaf_pred_time_prerank(
          ad_server_trans.dcaf_pred_time_prerank());
        computility_agent_info->set_dcaf_pred_time_rank(
          ad_server_trans.dcaf_pred_time_rank());
      }
    }
    auto* ad_info_log = log_info.add_ad_log_infos();
    ad_info_log->set_creative_id(item->ad_deliver_info().ad_base_info().creative_id());
    ad_info_log->set_price(item->ad_deliver_info().price());
    ad_info_log->set_cpa_bid(item->ad_deliver_info().ad_base_info().cpa_bid());
    ad_info_log->set_auto_cpa_bid(item->ad_deliver_info().ad_base_info().auto_cpa_bid());
  }
  UniverseComputilityDataLog::GetInstance()->Record(std::move(log_info));
}

void AdFrontProcessPostMixer::SendUniverseQcpxDeliveryLogInfo(ContextData* context_data) {
  auto ts = base::GetTimestamp();

  UniverseQcpxDeliveryLogInfo log_info_pv;
  log_info_pv.set_pv_filter_reason(
        context_data->get_ad_rank_pass_through().universe_inner_qcpx_pv_filter_reason());
  if (SPDM_enableUniverseQcpxPerf()) {
    context_data->dot_perf->Count(1, "qcpx", "pv_filter_reason",
      kuaishou::ad::AdEnum_UniverseInnerQcpxPvFilterReason_Name(
        context_data->get_ad_rank_pass_through().universe_inner_qcpx_pv_filter_reason()));
  }
      kuaishou::ad::AdEnum::UniverseInnerQcpxAdFilterReason ad_filter_reason;
  log_info_pv.set_delivery_timestamp(ts);
  log_info_pv.set_visitor_id(context_data->get_ad_request()->ad_user_info().id());
  log_info_pv.set_llsid(context_data->get_llsid());
  log_info_pv.set_device_id(context_data->get_ad_request()->ad_user_info().device_id());
  // device_os_id 和 ad_log 对齐，ios 为 1，android 和 harmony 为 2
  int32_t device_os_id = context_data->get_ad_request()->ad_user_info().platform() == "ios" ? 1 : 2;
  log_info_pv.set_device_os_id(device_os_id);
  log_info_pv.set_pos_id(context_data->get_ud_pos_id());
  log_info_pv.set_medium_app_id(context_data->copy_ud_app_id());
  log_info_pv.set_medium_app_version(context_data->get_ad_request()->ad_user_info().platform_version());
  log_info_pv.set_medium_cooperation_mode(context_data->get_ud_cooperation_mode());
  log_info_pv.set_medium_uid(context_data->get_ad_request()->universe_ad_request_info().medium_uid());
  log_info_pv.set_union_ad_style(context_data->get_ud_ad_style());

  if (context_data->get_ad_list().Size() <= 0) {
    // 无广告填充
    if (SPDM_enableNoAdsSendUniverseQcpxDeliveryLogInfo()) {
      UniverseQcpxDeliveryDataLog::GetInstance()->Record(std::move(log_info_pv));
    }
    context_data->dot_perf->Count(1, "universe_qcpx_delivery_log_info_qps", "no_ad");
  } else {
    context_data->mutable_ad_list()->GetAllWithFunc([&](
      kuaishou::ad::AdResult *ad_result, RankAdCommon *ad_common) {
        if (ad_result == nullptr || ad_common == nullptr) {
          return;
        }
        const auto &ad = *ad_result;
        const auto &ad_deliver_info = ad.ad_deliver_info();
        const auto &ad_base_info = ad_deliver_info.ad_base_info();
        const auto &online_join_params_pb = ad.ad_deliver_info().online_join_params_pb();
        const auto &ad_rank_trans_info = online_join_params_pb.ad_rank_trans_info();
        const auto &qpon_info = ad_rank_trans_info.qpon_info();

        UniverseQcpxDeliveryLogInfo log_info;
        log_info.CopyFrom(log_info_pv);

        log_info.set_coupon_template_id(online_join_params_pb.coupon_template_id());
        log_info.set_discount_amount(online_join_params_pb.discount_amount());
        log_info.set_coupon_threshold(online_join_params_pb.coupon_threshold());
        log_info.set_coupon_type(online_join_params_pb.coupon_type());
        log_info.set_coupon_strategy_type(ad_base_info.coupon_strategy_type());
        if (kuaishou::ad::AdEnum::UniverseInnerQcpxAdFilterReason_Parse(
            ad_rank_trans_info.universe_inner_qcpx_filter_reason(), &ad_filter_reason)) {
          log_info.set_ad_filter_reason(ad_filter_reason);
        }
        bool coupon_delivery = online_join_params_pb.coupon_template_id() > 0 &&
                                qpon_info.has_qpon();
        log_info.set_coupon_delivery_cnt(coupon_delivery);
        log_info.set_creative_id(ad_base_info.creative_id());
        int64_t merchant_product_id = 0;
        if (!absl::SimpleAtoi(ad_base_info.merchant_product_id(), &merchant_product_id)) {
          merchant_product_id = 0;
        }
        log_info.set_merchant_product_id(merchant_product_id);
        log_info.set_item_type(ad_base_info.item_type());
        log_info.set_account_type(ad_base_info.account_type());
        log_info.set_account_id(ad_base_info.account_id());
        log_info.set_campaign_id(ad_base_info.campaign_id());
        log_info.set_author_id(ad_base_info.author_id());
        log_info.set_campaign_type(ad_base_info.campaign_type());
        log_info.set_ocpc_action_type(ad_base_info.ocpc_action_type());
        log_info.set_unit_id(ad_base_info.unit_id());
        log_info.set_product_name(ad_base_info.product_name());
        log_info.set_cpm(ad_deliver_info.cpm());
        log_info.set_auction_bid(ad_base_info.auction_bid());
        log_info.set_price(ad_deliver_info.price());
        log_info.set_ctr(ad_deliver_info.predict_info().ctr());
        log_info.set_cvr(ad_deliver_info.predict_info().cvr());
        log_info.set_bid(ad_base_info.bid());
        log_info.set_cpa_bid(ad_base_info.cpa_bid());
        log_info.set_auto_cpa_bid(ad_base_info.auto_cpa_bid());
        if (ad_common->is_non_universe_ads()) {
          log_info.set_placement_type(::kuaishou::ad::AdEnum_PlacementType_PT_KWAI);
        } else {
          log_info.set_placement_type(::kuaishou::ad::AdEnum_PlacementType_PT_UNION);
        }

        UniverseQcpxDeliveryDataLog::GetInstance()->Record(std::move(log_info));
        context_data->dot_perf->Count(1, "universe_qcpx_delivery_log_info_qps", "has_ad");
    });
  }
  context_data->dot_perf->Interval(base::GetTimestamp() - ts, "universe_qcpx_delivery_log_info_latency");
}

void AdFrontProcessPostMixer::Post(const FrontServerResponse& front_server_response,
                              const ContextData& context_data) {
  if (context_data.get_ad_request()->has_debug_param() &&
      context_data.get_ad_request()->debug_param().has_debug_mode() &&
      context_data.get_ad_request()->debug_param().debug_mode()) {
    return;
  }
  AdServerShowStatManager::GetAdServerShowStatManager().RecordServerShowStat(
      *context_data.get_ad_request(), context_data.get_ad_list(), context_data,
      (absl::GetCurrentTimeNanos() - context_data.get_start_time_ns()) / 1000000);
}

// 记录广告请求响应信息 && 记录 tracelog
void AdFrontProcessPostMixer::LogInfo(const kuaishou::ad::FrontServerRequest &request,
                            const kuaishou::ad::FrontServerResponse &response,
                            ContextData *context_data) const {
  int64_t ad_count = 0;                 // 用于打印各广告位的 ad_count，方便统计
  int64_t fanstop_photo_ad_count = 0;   // 粉条视频广告数量打点
  int64_t fanstop_live_ad_count = 0;    // 粉条直播广告数量打点
  int64_t inspire_live_count = 0;       // 激励直播广告数量打点
  int64_t inspire_guarantee_count = 0;  // 激励直播兜底广告数量打点
  int flash_dsp_cnt = 0;
  std::unordered_set<std::string> ad_dsp_type;
  auto universe_ad_count = [&](const kuaishou::ad::universe::AdInfo& ad_info) {
    const auto& ad_result = ad_info.original_ad_result();
    const auto& ad_deliver_info = ad_info.original_ad_result().ad_deliver_info();
    const auto& ad_base_info = ad_deliver_info.ad_base_info();
    const auto& target_shard_type = ad_deliver_info.online_join_params_transparent().target_shard_type();
    if (target_shard_type == kuaishou::ad::AdEnum::FANSTOP_SHARD ||
        target_shard_type == kuaishou::ad::AdEnum::MERCHANT_SHARD) {
      ad_dsp_type.insert(kuaishou::ad::AdTraceDspType_Name(GetAdDspType(ad_result)));
    }
    std::string source_type(kuaishou::ad::AdSourceType_Name(ad_result.ad_source_type()));
    absl::StrAppend(&source_type, ".", kuaishou::ad::AdEnum_CampaignType_Name(ad_base_info.campaign_type()));
    const std::string& queue_type = kuaishou::ad::AdEnum_AdQueueType_Name(ad_deliver_info.ad_queue_type());
    const std::string& item_type = kuaishou::ad::AdEnum_ItemType_Name(ad_base_info.item_type());
    const std::string& bid_type = kuaishou::ad::AdEnum_BidType_Name(ad_base_info.bid_type());
    const std::string& item_reason = std::to_string(ad_deliver_info.reason());
    std::string error_type;
    VLOG(1) << "[debug] result, llsid:" << context_data->get_llsid()
            << ", visitor_id:" << context_data->get_user_id()
            << ", creative_id:" << ad_base_info.creative_id()
            << ", source_type:" << kuaishou::ad::AdSourceType_Name(ad_result.ad_source_type())
            << ", sub_source_type:" << kuaishou::ad::AdSubSourceType_Name(ad_base_info.ad_sub_source_type())
            << ", liked_biz_type:"
            << kuaishou::ad::AdEnum::FansTopLikedBizType_Name(
                   ad_base_info.fanstop_ext_info().fans_top_liked_biz_type())
            << ", charge_mode:" << kuaishou::ad::AdEnum::CampaignChargeMode_Name(ad_base_info.charge_mode())
            << ", reason:" << ad_deliver_info.reason()
            << ", item_type:" << kuaishou::ad::AdEnum::ItemType_Name(ad_base_info.item_type())
            << ", shard_type:" << kuaishou::ad::AdEnum::TargetServerShardType_Name(target_shard_type);

    if (ad_result.ad_source_type() == kuaishou::ad::AdSourceType::FANS_TOP_V2) {
      VLOG(2) << "[fanstop] final new fans result, llsid:" << context_data->get_llsid()
              << ", visitor_id:" << context_data->get_user_id()
              << ", creative_id:" << ad_base_info.creative_id() << ", photo_id:" << ad_base_info.photo_id()
              << ", live_id:" << ad_base_info.live_stream_id()
              << ", display_sytle:" << ad_base_info.fanstop_ext_info().display_style().ShortDebugString();
      if (ad_base_info.item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
        ++fanstop_live_ad_count;
      } else {
        ++fanstop_photo_ad_count;
      }
      if (ad_base_info.fanstop_ext_info().is_fanstop_inner_operation()) {
        source_type.append(".INNER");
      } else if (ad_base_info.fanstop_ext_info().is_new_fanstop_inner()) {
        source_type.append(".NEW_INNER");
      } else {
        source_type.append(".NORMAL");
      }
      if (ad_base_info.author_id() == 0) {
        error_type = "invalid_author_id";
      }
      if (ad_base_info.ad_sub_source_type() != kuaishou::ad::ESP_MOBILE &&
          ad_base_info.ks_order_id().empty()) {
        error_type = "invalid_ks_order_id";
      }
      if (ad_base_info.live_stream_id() > 0 && ad_base_info.photo_id() > 0 &&
          ad_base_info.item_type() != kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE) {
        error_type = "invalid_p2l_item_type";
      }
    } else {
      if (ad_result.ad_source_type() == kuaishou::ad::AdSourceType::DSP) {
        if (ad_base_info.promotion_type() != kuaishou::ad::AdEnum_CampaignPromotionType_UNKNOWN_PROMOTION) {
          source_type.append(".").append(
              kuaishou::ad::AdEnum_CampaignPromotionType_Name(ad_base_info.promotion_type()));
        }
        if (ad_base_info.promotion_type() == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
          flash_dsp_cnt++;
        }
      }
      ++ad_count;
    }
    if (ad_base_info.account_id() <= 0) {
      error_type = "invalid_account_id";
    }
    context_data->mutable_ad_select_stage_infos()->SetFinalUniverseResponseInfo(
        ad_info.ad_base_info().creative_id(),
        ad_result.ad_deliver_info().ad_queue_type() == kuaishou::ad::AdEnum_AdQueueType_HARD_AD_QUEUE);
    if (context_data->get_byte_size_dot()) {
      context_data->dot_perf->Interval(ad_result.ByteSize(), "ad_result_size",
                                       kuaishou::ad::AdEnum::AdRequestFlowType_Name(request.flow_type()),
                                       kuaishou::ad::AdTraceDspType_Name(GetAdDspType(ad_result)),
                                       absl::StrCat(ad_result.ad_deliver_info().ad_monitor_type()));
      context_data->dot_perf->Interval(ad_result.ad_deliver_info().ad_base_info().ad_data_v2().length(),
                                       "ad_data_v2_size",
                                       kuaishou::ad::AdEnum::AdRequestFlowType_Name(request.flow_type()),
                                       kuaishou::ad::AdTraceDspType_Name(GetAdDspType(ad_result)),
                                       absl::StrCat(ad_result.ad_deliver_info().ad_monitor_type()));
      context_data->dot_perf->Interval(ad_result.ad_deliver_info().online_join_params().length(),
                                       "online_join_size",
                                       kuaishou::ad::AdEnum::AdRequestFlowType_Name(request.flow_type()),
                                       kuaishou::ad::AdTraceDspType_Name(GetAdDspType(ad_result)),
                                       absl::StrCat(ad_result.ad_deliver_info().ad_monitor_type()));
      context_data->dot_perf->Interval(ad_info.ByteSize(), "ad_info_size",
                                       kuaishou::ad::AdEnum::AdRequestFlowType_Name(request.flow_type()),
                                       kuaishou::ad::AdTraceDspType_Name(GetAdDspType(ad_result)),
                                       absl::StrCat(ad_result.ad_deliver_info().ad_monitor_type()));
    }
  };
  ks::ad_athena::PerfResponse(request.from(), &request.universe_request(), &response.universe_response());
  for (const auto& imp_ad_info : response.universe_response().imp_ad_info()) {
    for (const auto& ad_info : imp_ad_info.ad_info()) {
      universe_ad_count(ad_info);
    }
  }

  if (context_data->get_byte_size_dot()) {
    context_data->dot_perf->Interval(response.ByteSize(), "ad_front_response_size",
                                     kuaishou::ad::AdEnum::AdRequestFlowType_Name(request.flow_type()));
  }
  // 收口处统一打印一下 front 的 ad_count
  if (context_data->dot_perf != nullptr) {
    context_data->dot_perf->Count(ad_count, "front_result_num", "",
                                  "", "", "", false);
    context_data->dot_perf->Count(fanstop_photo_ad_count + fanstop_live_ad_count,
                                  "front_fanstop_result_num", ad_base::util::GetServiceVersionName(),
                                  ad_base::util::GetMyPodName(), context_data->TabName(), "", false);
    context_data->dot_perf->Count(fanstop_photo_ad_count, "front_fanstop_photo_result_num",
                                  ad_base::util::GetServiceVersionName(), ad_base::util::GetMyPodName(),
                                  context_data->TabName(), "", false);
    context_data->dot_perf->Count(fanstop_live_ad_count, "front_fanstop_live_result_num",
                                  ad_base::util::GetServiceVersionName(), ad_base::util::GetMyPodName(),
                                  context_data->TabName(), "", false);
    if (context_data->get_ac_fetcher_type() == kuaishou::ad::AdTargetResponse::PREVIEW_ADS_TYPE
        && ad_count == 0
        && fanstop_photo_ad_count == 0
        && fanstop_live_ad_count == 0) {
      ks::ad_base::AdPerf::CountLogStash(
          1, "ad.ad_front", "ac_fetcher_ad_num", "ad_front", "failed",
          absl::StrCat(context_data->get_user_id()),
          absl::StrCat(static_cast<int64_t>(
              context_data->get_last_filter_condition() !=
                      kuaishou::log::ad::AdTraceFilterCondition::UNKNOWN_AD_TRACE_FILTER_CONDITION
                  ? context_data->get_last_filter_condition()
                  : context_data->get_soft_last_filter_condition())),
          absl::StrCat(context_data->get_sub_page_id()));
    }
    if (ad_dsp_type.size() > 0) {
      context_data->dot_perf->Count(1, "front_server.inner_loop_output_all");
    }
    for (auto const& type : ad_dsp_type) {
      context_data->dot_perf->Count(1, "front_server.inner_loop_output", type);
    }
    if (ks::ad_base::IsInspireLive(context_data->get_sub_page_id(), context_data->get_ad_request())) {
      context_data->dot_perf->Count(1, "front_fanstop_inspire_live_result_req",
                                    response.nearby_response().ad_dsp_size() > 0 ? "has_result"
                                                                                 : "no_result");
      if (inspire_guarantee_count > 0) {
        context_data->dot_perf->Interval(inspire_guarantee_count, "front_inspire_live_guarantee_num");
      }
    }
  }
}

void AdFrontProcessPostMixer::LogDryrunInfo(const kuaishou::ad::FrontServerRequest &request,
                                       const kuaishou::ad::FrontServerResponse &response,
                                       ContextData *context_data) const {
  int64_t inner_ad_count = 0;  // 内循环 ad_count
  int64_t outer_ad_count = 0;  // 外循环 ad_count
  int64_t brand_ad_count = 0;  // 品牌 ad_count
  int64_t outer_live_ad_count = 0;
  int64_t hard_ad_count = 0;  // 硬广 ad_count
  int64_t soft_ad_count = 0;  // 软广 ad_count
  int64_t outer_hard_ad_count = 0;
  int64_t outer_soft_ad_count = 0;
  int64_t inner_hard_ad_count = 0;
  int64_t inner_soft_ad_count = 0;
  std::string normal_pos_list;
  std::string native_pos_list;
  std::string inner_fanstop_pos_list;
  std::string exp_tag = "exp";
  auto SetAdInfo = [&](const kuaishou::ad::AdResult &ad_result) {
    auto &deliver_info = ad_result.ad_deliver_info();
    if (ad_result.ad_source_type() == kuaishou::ad::AdSourceType::BRAND) {
      brand_ad_count++;
    } else if (deliver_info.ad_monitor_type() <= 999) {
      outer_ad_count++;
      if (deliver_info.ad_base_info().campaign_type()  == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE) {
        outer_live_ad_count++;
        context_data->dot_perf->Interval(deliver_info.rank_benefit(),
                                      "front_dryrun_rank_benefit", "outer_live", "",
                                       "", "", false);
      }
      context_data->dot_perf->Interval(deliver_info.rank_benefit(),
                                      "front_dryrun_rank_benefit", "outer", "",
                                       "", "", false);
      context_data->dot_perf->Count(
          deliver_info.price(), "front_dryrun_price", "outer", "", "",
          kuaishou::ad::AdEnum::BidType_Name(deliver_info.ad_base_info().bid_type()), false);
    } else {
      inner_ad_count++;
      context_data->dot_perf->Interval(deliver_info.rank_benefit(),
                                       "front_dryrun_rank_benefit", "inner", "",
                                       "", "", false);
      context_data->dot_perf->Count(
          deliver_info.price(), "front_dryrun_price", "inner", "", "",
          kuaishou::ad::AdEnum::BidType_Name(deliver_info.ad_base_info().bid_type()), false);
    }
    if (deliver_info.ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
      native_pos_list = absl::StrCat(native_pos_list, "_", deliver_info.pos());
      soft_ad_count++;
      context_data->dot_perf->Interval(deliver_info.rank_benefit(),
          "front_dryrun_rank_benefit", "soft", "",
          "", "", false);
      context_data->dot_perf->Count(
          deliver_info.price(), "front_dryrun_price", "soft", "", "",
          kuaishou::ad::AdEnum::BidType_Name(deliver_info.ad_base_info().bid_type()), false);
    } else if (deliver_info.ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
      normal_pos_list = absl::StrCat(normal_pos_list, "_", deliver_info.pos());
      hard_ad_count++;
      context_data->dot_perf->Interval(deliver_info.rank_benefit(),
          "front_dryrun_rank_benefit", "hard", "",
          "", "", false);
      context_data->dot_perf->Count(
          deliver_info.price(), "front_dryrun_price", "hard", "", "",
          kuaishou::ad::AdEnum::BidType_Name(deliver_info.ad_base_info().bid_type()), false);
    } else {
      inner_fanstop_pos_list = absl::StrCat(inner_fanstop_pos_list, "_", deliver_info.pos());
    }
  };
  auto SetQuotaDebugAdInfo = [&](const kuaishou::ad::AdResult &ad_result) {
    auto &deliver_info = ad_result.ad_deliver_info();
    if (ad_result.ad_source_type() != kuaishou::ad::AdSourceType::BRAND) {
      if (deliver_info.ad_monitor_type() <= 999) {
        if ((deliver_info.ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE)) {
          outer_soft_ad_count++;
          context_data->dot_perf->Interval(deliver_info.rank_benefit(),
              "quota_debug_rank_benefit", "outer_soft", exp_tag, "", "", false);
          context_data->dot_perf->Interval(deliver_info.price(),
              "quota_debug_price", "outer_soft", exp_tag, "", "", false);
        } else {
          outer_hard_ad_count++;
          context_data->dot_perf->Interval(deliver_info.rank_benefit(),
              "quota_debug_rank_benefit", "outer_hard", exp_tag, "", "", false);
          context_data->dot_perf->Interval(deliver_info.price(),
              "quota_debug_price", "outer_hard", exp_tag, "", "", false);
        }
      } else {
        if ((deliver_info.ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE)) {
          inner_soft_ad_count++;
          context_data->dot_perf->Interval(deliver_info.rank_benefit(),
              "quota_debug_rank_benefit", "inner_soft", exp_tag, "", "", false);
          context_data->dot_perf->Interval(deliver_info.price(),
              "quota_debug_price", "inner_soft", exp_tag, "", "", false);
        } else {
          inner_hard_ad_count++;
          context_data->dot_perf->Interval(deliver_info.rank_benefit(),
              "quota_debug_rank_benefit", "inner_hard", exp_tag, "", "", false);
          context_data->dot_perf->Interval(deliver_info.price(),
              "quota_debug_price", "inner_hard", exp_tag, "", "", false);
        }
      }
    }
  };

  for (const auto &imp_ad_info : response.universe_response().imp_ad_info()) {
    for (const auto &item : imp_ad_info.ad_info()) {
      SetAdInfo(item.original_ad_result());
      SetQuotaDebugAdInfo(item.original_ad_result());
    }
  }
  if (context_data->dot_perf != nullptr) {
    // 1. 队列大小
    context_data->dot_perf->Interval(outer_ad_count * 1000000, "front_dryrun_result_size",
                                     "outer", "", "", "", false);
    context_data->dot_perf->Interval(inner_ad_count * 1000000, "front_dryrun_result_size",
                                     "inner", "", "", "", false);
    context_data->dot_perf->Interval(soft_ad_count * 1000000, "front_dryrun_result_size",
                                     "soft", "", "", "", false);
    context_data->dot_perf->Interval(hard_ad_count * 1000000, "front_dryrun_result_size",
                                     "hard", "", "", "", false);
    context_data->dot_perf->Interval(brand_ad_count * 1000000, "front_dryrun_result_size",
                                     "brand", "", "", "", false);
    context_data->dot_perf->Interval(outer_live_ad_count * 1000000, "front_dryrun_result_size",
                                     "outer_live", "", "", "", false);
    context_data->dot_perf->Interval(inner_hard_ad_count,
        "quota_debug_ad_count", "inner_hard", exp_tag, "", "", false);
    context_data->dot_perf->Interval(inner_soft_ad_count,
        "quota_debug_ad_count", "inner_soft", exp_tag, "", "", false);
    context_data->dot_perf->Interval(outer_hard_ad_count,
        "quota_debug_ad_count", "outer_hard", exp_tag, "", "", false);
    context_data->dot_perf->Interval(outer_soft_ad_count,
        "quota_debug_ad_count", "outer_soft", exp_tag, "", "", false);

    // 2. 用于做填充率计算
    if (outer_ad_count > 0) {
      context_data->dot_perf->Count(1, "front_dryrun_result_num",
                                    "outer", "", "", "", false);
    }
    if (inner_ad_count > 0) {
      context_data->dot_perf->Count(1, "front_dryrun_result_num",
                                    "inner", "", "", "", false);
    }
    if (soft_ad_count > 0) {
      context_data->dot_perf->Count(1, "front_dryrun_result_num",
                                    "soft", "", "", "", false);
    }
    if (hard_ad_count > 0) {
      context_data->dot_perf->Count(1, "front_dryrun_result_num",
                                    "hard", "", "", "", false);
    }
    if (brand_ad_count > 0) {
      context_data->dot_perf->Count(1, "front_dryrun_result_num",
                                    "brand", "", "", "", false);
    }
    if (outer_live_ad_count > 0) {
      context_data->dot_perf->Count(1, "front_dryrun_result_num",
                                    "outer_Live", "", "", "", false);
    }
    if (soft_ad_count > 0 || hard_ad_count > 0) {
      context_data->dot_perf->Count(1, "full_ad_request_num", "",
                                    kuaishou::ad::AdEnum_AdRequestFlowType_Name(request.flow_type()), "", "",
                                    false);
    }
    context_data->dot_perf->Count(
        1, "ad_front_result_ad_pos",
        absl::StrCat(native_pos_list, ",", normal_pos_list, ",", inner_fanstop_pos_list));
  }
}

void AdFrontProcessPostMixer::RequestAdPackService(ContextData& context_data,    // NOLINT
                                              kuaishou::ad::FrontServerResponse* front_server_response) {
  if (context_data.get_ad_pack_request() == nullptr || front_server_response == nullptr) {
    return;
  }
  // 未通过 prefilter 的不请求 adpack
  if (!context_data.GetPassPreFilter()) {
    context_data.dot_perf->Count(1, "front_skip_fill_req_pack_info");
    return;
  }

  auto* ad_pack_req = context_data.mutable_ad_pack_request();
  // ====== 请求 ad_pack 时，清理无用 & 冗余数据 =====
  CleanTransparentPackInfo(context_data, context_data.mutable_trans_pack_info());

  BuildAdPackRequest(&context_data);

  ks::ad_base::Proto pb;
  auto ad_request_config = FrontKconfUtil::adPackAdRequestClean()->data;
  // true 为白名单模式，配置中的字段不清理
  pb.CleanFiledByName(ad_pack_req->mutable_ad_request(), ad_request_config.get(), true);
  if (context_data.EnableSimplifyAlwaysLog()) {
    ALWAYS_LOG_TRACER->AlwaysLogConvert(&context_data,
                                      ad_pack_req->mutable_req_pack_info()->mutable_trace_always_log());
    ad_pack_req->mutable_req_pack_info()->mutable_simplify_trace_always_log()->CopyFrom(
      context_data.get_simplify_always_log());
  }

  // ====== 清理结束 =====
  StatisticTransparentPackInfoSize(context_data, ad_pack_req->req_pack_info());
  // 使用 AdPack 发送
  SendLogByAdPack(&context_data);
}

void AdFrontProcessPostMixer::SendLogByAdPack(ContextData* context) {
  if (context->get_ad_request()->ad_user_info().id() % 100 >=
      FrontKconfUtil::frontRpcDegrade()->data().GetRpcDegradeRatio("adPack")) {
    return;
  }
  if (context->get_ad_pack_request() == nullptr) {
    return;
  }
  auto* ad_pack_request_ = context->mutable_ad_pack_request();
  if (context->get_ad_pack_request()->req_pack_info()
          .ad_server_show_log()
          .universe_ad_request_info()
          .imp_info_size() <= 0) {
    return;
  }
  auto* front_server_request_ = context->mutable_front_server_request();
  auto* front_server_response_ = context->mutable_front_server_response();
  const auto start_ts = base::GetTimestamp();
  YLOG_PB_MODULE("adFrontServer", "Second", context->get_llsid(), context->get_user_id(), "adPackServer",
                 ks::ad::ylog::LOG_TYPE::request, *(ad_pack_request_));
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_chain_log  && \
    ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName().empty()) {
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceLog("request", \
    true, "adFrontServer", "adPackServer", context->get_user_id(), context->get_llsid(), \
    ks::ad_base::massage_to_json(*(ad_pack_request_)));
  }
  kuaishou::ad::AdPackResponse ad_pack_response;
  auto kAdPackServiceClientName = GetAdPackServiceName(context);

  auto client = ks::ad_base::AdKessClient::ClientOfKey<::mix::kuaishou::ad::kess::AdPackService>(
                                                kAdPackServiceClientName);
  if (FrontKconfUtil::enableUniverseAdPackOpt()) {
    // 处理上次请求的，避免队列增长内存泄露
    WAIT_ASYNC_RESULT_WITH_TAG("async_ad_pack_service");
    auto stub = client.second->SelectOne();
    std::function<void(const kuaishou::ad::AdPackResponse& res, int status)> callback =
          [this](const kuaishou::ad::AdPackResponse& res, int status) { };
    AD_HALF_ASYNC_KESS_CALL_WITH_STUB(::mix::kuaishou::ad::kess::AdPackService,
        GetAdResult, kuaishou::ad::AdPackResponse, *ad_pack_request_,
        callback, client.first->time_out, stub, "async_ad_pack_service");
    if (context->get_byte_size_dot()) {
      context->dot_perf->Interval(ad_pack_request_->ByteSize(), "ad_pack_request_size");
    }
    context->dot_perf->Interval(base::GetTimestamp() - start_ts, "front_to_adpack_latency", "asnyc");
    return;
  }
  auto grpc_status = client.second->SelectOne()->GetAdResult(
                                      ad_base::OptionsFromMilli(client.first->time_out),
                                      *ad_pack_request_, &ad_pack_response);
  context->mutable_ad_perf_info()->SetDownStreamServiceStatus(kuaishou::ad::ServiceDeployName::AD_PACK,
                                                   base::GetTimestamp() - start_ts, grpc_status.error_code());
  context->dot_perf->Interval(base::GetTimestamp() - start_ts, "front_to_adpack_latency", context->TabName()),
      base::IntToString(static_cast<int>(grpc_status.error_code()));
  context->dot_perf->Count(1, "front_to_adpack_dsp_size", base::IntToString(ad_pack_request_->ad_dsp_size()));
  if (!grpc_status.ok()) {
    LOG(WARNING)
        << "ad_pack rpc failed, error_code: " << static_cast<int>(grpc_status.error_code())
        << ", error_msg: " << grpc_status.error_message()
        << ", error_detail: " << grpc_status.error_details();
    falcon::Inc("ad_front.ad_pack_service_failed", 1);
  }
  if (context->get_byte_size_dot()) {
    context->dot_perf->Interval(ad_pack_request_->ByteSize(), "ad_pack_request_size");
  }

  YLOG_PB_MODULE("adFrontServer", "Second", context->get_llsid(), context->get_user_id(), "adPackServer",
                 ks::ad::ylog::LOG_TYPE::response, ad_pack_response);
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_chain_log  && \
    ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName().empty()) {
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceLog("response", \
    true, "adFrontServer", "adPackServer", context->get_user_id(), context->get_llsid(), \
    ks::ad_base::massage_to_json(ad_pack_response));
  }
  return;
}

std::string AdFrontProcessPostMixer::GetAdPackServiceName(ContextData* context) {
  return "ad-pack-server-universe";
}

/**
 * 不是混排流量, front 要请求 adpack 时, 构造 adpack request
 **/
void AdFrontProcessPostMixer::BuildAdPackRequest(ContextData* context) {
  auto* ad_pack_request = context->mutable_ad_pack_request();
  auto* front_server_request = context->mutable_front_server_request();
  auto* front_server_response = context->mutable_front_server_response();
  if (ad_pack_request == nullptr || front_server_request == nullptr || front_server_response == nullptr) {
    return;
  }

  ::google::protobuf::RepeatedPtrField<::kuaishou::ad::DspAdInfo> *dsp_ad_info = nullptr;
  bool dark_mode = false;
  int32 page_size = -1;
  ::kuaishou::ad::FullLinkRequestTag full_link_tag;

  // universe 接口专用
  std::vector<std::pair<::kuaishou::ad::AdResult*, ::kuaishou::ad::universe::AdInfo*>> universe_interface_ad;

  // 其余流量统一都走的 universe 接口
  // 包括: universe 接口 & splash & merchant & search & galaxy & knews & universe
  std::map<uint64, kuaishou::ad::AdResult*> tmp_map;

  auto* universe_interface_resp = front_server_response->mutable_universe_response();
  const auto& universe_interface_req = front_server_request->universe_request();
  // 由于 universe 接口中没有 ad_resulst 结构，因此需要通过返回的广告的 creative_id
  context->mutable_ad_list()->GetAllWithFunc(
      [&tmp_map](kuaishou::ad::AdResult* ad_result, RankAdCommon* ad_common) {
        tmp_map.emplace(ad_common->creative_id(), ad_result);
      });
  for (int imp_idx = 0; imp_idx < universe_interface_resp->imp_ad_info_size(); ++imp_idx) {
    auto* imp_ad_info = universe_interface_resp->mutable_imp_ad_info(imp_idx);
    for (int ad_idx = 0; ad_idx < imp_ad_info->ad_info_size(); ++ad_idx) {
      const auto& creative_id = imp_ad_info->ad_info(ad_idx).ad_base_info().creative_id();
      auto map_iter = tmp_map.find(creative_id);
      if (map_iter != tmp_map.end()) {
        universe_interface_ad.emplace_back(
            std::make_pair(map_iter->second, imp_ad_info->mutable_ad_info(ad_idx)));
        if (context->get_enable_combo_search_req_ad_pack()) {  // 搜索下发回传 fix
          auto* ad_info = imp_ad_info->mutable_ad_info(ad_idx);
          std::string ad_result_content;
          map_iter->second->SerializeToString(&ad_result_content);
          base::Base64Encode(ad_result_content, ad_info->mutable_ad_result_content_string());
          if (ad_info->ad_result_content_string().empty()) {
            context->dot_perf->Count(1, "test_zd_front", "result_str_empty");
          }
        }
      }
    }
  }
  full_link_tag = universe_interface_req.full_link_tag();

  /********** 填充 adpack request 数据  **********/
  if (dsp_ad_info != nullptr) {
    // 发现 & 同城 & 关注 特有
    ad_pack_request->mutable_ad_dsp()->CopyFrom(*dsp_ad_info);
  }
  if (universe_interface_ad.size() != 0) {
    // 走 universe 接口的流量在这里转换
    for (const auto& item : universe_interface_ad) {
      auto* ad_dsp = ad_pack_request->add_ad_dsp();
      // 更新下 front 的请求结束时间戳
      item.first->mutable_ad_deliver_info()->mutable_online_join_params_pb()
          ->set_front_end_time_stamp(base::GetTimestamp());
      ad_dsp->mutable_ad_result()->CopyFrom(*(item.first));
      ad_dsp->mutable_pos_info()->CopyFrom(item.second->pos_info());
      ad_dsp->set_pos(item.first->ad_deliver_info().pos());
    }
  }
  ad_pack_request->set_dark_mode(dark_mode);
  ad_pack_request->set_page_size(page_size);
  ad_pack_request->mutable_full_link_tag()->CopyFrom(full_link_tag);
  // copy ad_request
  ad_pack_request->mutable_ad_request()->CopyFrom(*(context->get_ad_request()));
  // copy req_pack_info = ad_server_show + fans_server_show, 可 swap
  ad_pack_request->mutable_req_pack_info()->Swap(context->mutable_trans_pack_info());
  auto* mutable_service_info = ad_pack_request->mutable_source_service_info();
  if (ad_utility::any_of(FLAGS_ksp_group_deploy_type, +ks::ad_base::DeployType::archimedes)) {
    mutable_service_info->set_service_type(kuaishou::ad::SourceServiceType::SST_ARCHIMEDES_FRONT_SERVICE);
  } else {
    mutable_service_info->set_service_type(kuaishou::ad::SourceServiceType::AD_FRONT_SERVICE);
  }
  mutable_service_info->set_host_name(serving_base::GetHostName());
  mutable_service_info->set_pod_name(ks::ad_base::util::GetMyPodName());
}

void AdFrontProcessPostMixer::CleanTransparentPackInfo(const ContextData& context_data,
                                                  kuaishou::ad::TransparentPackInfo* trans_info) {
  auto* mutable_ad_server_show = trans_info->mutable_ad_server_show_log();
  auto* mutable_fanstop_show_log = trans_info->mutable_fanstop_show_log();
  auto* mutable_ad_ack_info = trans_info->mutable_ad_ack_info();
  auto* mutable_always_log = trans_info->mutable_trace_always_log();

  ks::ad_base::Proto pb;
  auto ad_server_show_config = FrontKconfUtil::transparentAdServerShowLogClean()->data;
  pb.CleanFiledByName(mutable_ad_server_show, ad_server_show_config.get(), false);
  // 清理透传的 fanstop_server_show
  auto fanstop_server_show_config = FrontKconfUtil::transparentFanstopServerShowLogClean()->data;
  pb.CleanFiledByName(mutable_fanstop_show_log, fanstop_server_show_config.get(), false);
  // 清理透传的 ad_ack_info
  auto ad_ack_info_config = FrontKconfUtil::transparentAdAckInfoClean()->data;
  pb.CleanFiledByName(mutable_ad_ack_info, ad_ack_info_config.get(), false);
  // 清理透传的 trace_always_log
  auto always_log_config = FrontKconfUtil::transparentTraceAlwaysLogClean()->data;
  pb.CleanFiledByName(mutable_always_log, always_log_config.get(), false);
}

void AdFrontProcessPostMixer::StatisticTransparentPackInfoSize(
    const ContextData& context_data, const kuaishou::ad::TransparentPackInfo& trans_info) {
  auto empty_size = FrontKconfUtil::emptyDataSize();
  const auto* front_server_response = context_data.get_front_server_response();
  if (front_server_response == nullptr) {
    return;
  }
  if (context_data.get_byte_size_dot()) {
    // 数据大小打点统计
    auto trans_info_size = trans_info.ByteSize();
    auto ad_server_show_size = trans_info.ad_server_show_log().ByteSize();
    auto fanstop_server_show_size = trans_info.fanstop_show_log().ByteSize();
    auto ad_ack_info_size = trans_info.ad_ack_info().ByteSize();
    auto always_log_size = trans_info.trace_always_log().ByteSize();
    context_data.dot_perf->Interval(trans_info_size, "transparent_ad_pack_info_size", "total");
    context_data.dot_perf->Interval(ad_server_show_size, "transparent_ad_pack_info_size",
                                    "ad_server_show_size");
    context_data.dot_perf->Interval(fanstop_server_show_size, "transparent_ad_pack_info_size",
                                    "fanstop_server_show_size");
    context_data.dot_perf->Interval(ad_ack_info_size, "transparent_ad_pack_info_size", "ad_ack_info_size");
    context_data.dot_perf->Interval(always_log_size, "transparent_ad_pack_info_size", "always_log_size");
  }
}
}  // namespace front_server
}  // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdFrontProcessPostMixer, ::ks::front_server::AdFrontProcessPostMixer);
