#pragma once
#include <string>

#include "base/common/basic_types.h"
#include "teams/ad/front_server_universe/engine/server/init.h"
#include "dragon/src/processor/base/common_reco_base_mixer.h"

namespace ks {
namespace front_server {

class AdFrontProcessPrepareInitMixer : public ::ks::platform::CommonRecoBaseMixerEmbedPipeline {
 public:
  AdFrontProcessPrepareInitMixer();
  ~AdFrontProcessPrepareInitMixer() = default;
  // dragon 接口
  void Mix(ks::platform::AddibleRecoContextInterface* context) override;
  void OnPipelineExit(ks::platform::ReadableRecoContextInterface* context);
  std::string GetEmbeddingPipeline(const platform::ReadableRecoContextInterface* context, const std::string& name) const {  // NOLINT
    return GetStringProcessorParameter(context, name);
  }
  bool EmbeddedPipelineExecute(platform::AddibleRecoContextInterface *context, const std::string &pipeline_name) {  // NOLINT
    return RunEmbeddedPipeline(context, pipeline_name);
  }

 private:
  bool InitProcessor() override;

 private:
  AdContext *ps_context_ = nullptr;
  DISALLOW_COPY_AND_ASSIGN(AdFrontProcessPrepareInitMixer);
};  // class AdFrontProcessPrepareInitMixer

}  // namespace front_server
}  // namespace ks
