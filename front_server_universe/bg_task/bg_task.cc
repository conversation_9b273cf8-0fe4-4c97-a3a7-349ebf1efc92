
#include "base/common/sleep.h"
#include "glog/logging.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_base/src/clock_cache/expired_clock_cache.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"
#include "teams/ad/ad_base/src/common/scoped_guard.h"
#include "teams/ad/ad_base/src/common/utility.h"
#include "teams/ad/ad_base/src/container/singleton.h"
#include "teams/ad/engine_base/bg_thread/flow_statistics_minute.h"
#include "teams/ad/engine_base/cache_loader/ad_traffic_creative_rule_v2.h"
#include "teams/ad/engine_base/kafka/cpm_bound_log_manager.h"
#include "teams/ad/engine_base/p2p_cache_loader/universe_ecom_bid_p2p.h"
#include "teams/ad/front_server_universe/bg_task/bg_task.h"
#include "teams/ad/front_server_universe/bg_task/smart_compute_power/smart_compute_power_inner.h"
#include "teams/ad/front_server_universe/bg_task/ad_universe_benefit_allocation_data/ad_universe_benefit_allocation_data.h"
#include "teams/ad/front_server_universe/bg_task/universe_elastic_control/universe_agg_pos_cluster.h"
#include "teams/ad/front_server_universe/bg_task/universe_elastic_control/universe_agg_pos_cluster_v2.h"
#include "teams/ad/front_server_universe/bg_task/universe_flow_strategy/universe_allowance_strategy_data.h"
#include "teams/ad/front_server_universe/bg_task/universe_benefit_stats/universe_benefit_stats.h"
#include "teams/ad/front_server_universe/bg_task/universe_new_creative_data/universe_new_creative_data.h"
#include "teams/ad/front_server_universe/bg_task/universe_operation_bound_explore/universe_operation_bound_explore.h"
#include "teams/ad/front_server_universe/bg_task/universe_operation_discount/universe_operation_discount.h"
#include "teams/ad/front_server_universe/bg_task/universe_server_show_ratio_cali_data/universe_server_show_ratio_cali_p2p.h"
#include "teams/ad/front_server_universe/util/ad_pos_manager/universe_pos_resource_v2.h"
#include "teams/ad/front_server_universe/bg_task/universe_app_package2product/universe_app_package2product.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/universe_record_auction_log/universe_auction_log_manager.h"
#include "teams/ad/front_server_universe/util/utility/ksn_util.h"
#include "teams/ad/front_server_universe/bg_task/universe_new_media_protection/universe_new_media_protection_pos_imp_p2p.h"
#include "teams/ad/front_server_universe/bg_task/universe_new_media_protection/universe_new_media_protection_post_cpm_p2p.h"
#include "teams/ad/front_server_universe/util/universe_traffic_control/universe_traffic_control.h"
#include "teams/ad/front_server_universe/bg_task/universe_crowd_strategy/universe_crowd_strategy.h"
#include "teams/ad/front_server_universe/bg_task/universe_active_crowd_strategy/universe_active_crowd_strategy.h"
#include "teams/ad/front_server_universe/util/universe_computility_data_log/universe_computility_data_log.h"
#include "teams/ad/front_server_universe/util/universe_qcpx_delivery_data_log/universe_qcpx_delivery_data_log.h"

DEFINE_bool(search_enable_query_sensitive_check, true, "enable search query sensitive check");
DECLARE_int32(ksp_group_deploy_type);

namespace ks {
namespace front_server {
bool StartBgTask() {
  tf::Executor executor;
  tf::Taskflow taskflow;
  auto start_ts = base::GetTimestamp();
  taskflow.emplace([](tf::Subflow &subflow) { RegisterBgTask(subflow); }).name("load_universe");
  executor.run(taskflow).wait();
  BgTaskStopMgr::Instance().WaitForReady();
  LOG(INFO) << "StartBgTask Finish, latency: " << (base::GetTimestamp() - start_ts) / 1000000 << " s";
  return true;
}
void RegisterBgTask(tf::Subflow &subflow) {   // NOLINT
  StartTask(subflow, UniversePosResourceV2::GetInstance());

  StartTask(ks::ad_server::FlowStatisticsMinute::GetInstance());
  if (!ad_utility::any_of(FLAGS_ksp_group_deploy_type, +ks::ad_base::DeployType::archimedes)) {
    // 内粉部署不需要
    StartTask(ks::front_server::SmartComputePowerInnerUtil::GetInstance());
  }

  StartWeakTask(subflow, ks::front_server::UniverseBenefitStat::GetInstance());
  // 加载运营平台数据——计费打折，平台让利
  StartTask(subflow, GetUniverseOperationDiscountContainer());
    // 加载运营平台数据——内循环门槛顶价，广告主承担
  StartWeakTask(subflow, GetUniverseOperationBoundExploreContainer());
  // 加载新创意冷启动基础数据
  StartWeakTask(subflow, UniverseNewCreativeData::GetInstance());
  // 加载联盟新媒体 pos 的曝光数据
  StartTask(subflow, ks::front_server::UniverseNewMediaProtectionPosImp::GetInstance());
  // 加载联盟新媒体比 pos 粒度更粗的平均 cpm 数据，用于赋值
  StartTask(subflow, ks::front_server::UniverseNewMediaProtectionPostCpm::GetInstance());
  // 加载人工挖掘物理广告位
  StartTask(subflow, UniverseAggPosCluster::GetInstance());
  StartTask(subflow, UniverseAggPosClusterV2::GetInstance());
  // 加载 app 包名到产品名的映射数据
  StartTask(subflow, UniverseAppPackage2Product::GetInstance());
  // 加载补贴金实时花费数据
  StartWeakTask(subflow, UniverseAllowanceStrategyData::GetInstance());
  // 加载主站转化人群
  StartWeakTask(subflow, ks::front_server::UniverseCrowdStrategy::GetInstance());
  StartWeakTask(subflow, ks::front_server::UniverseActiveCrowdStrategy::GetInstance());
  // 加载发送 cpm_bound_log 的管理器
  LOG(INFO) << "Start CpmBoundLogManager Load";
  ks::ad_base::Singleton<ks::engine_base::CpmBoundLogManager>::instance().Start();
  subflow.emplace([&]() {
    while (!ks::ad_base::Singleton<ks::engine_base::CpmBoundLogManager>::instance().IsReady()) {
    }
    LOG(INFO) << "CpmBoundLogManager Load finish, latency:"
              << ks::ad_base::Singleton<ks::engine_base::CpmBoundLogManager>::instance().GetInitLatency();
  });

  // 加载发送 universe auction log 数据
  LOG(INFO) << "Start UniverseAuctionLogManager Load";
  ks::ad_base::Singleton<UniverseAuctionLogManager>::instance().Start();
  subflow.emplace([&]() {
    while (!ks::ad_base::Singleton<UniverseAuctionLogManager>::instance().IsReady()) {
    }
    LOG(INFO) << "kUniverseAuctionLogManager Load finish, latency:"
              << ks::ad_base::Singleton<UniverseAuctionLogManager>::instance().GetInitLatency();
  });

  // 加载算力数据下发线程
  LOG(INFO) << "Start UniverseComputilityDataLog Load";
  UniverseComputilityDataLog::GetInstance()->Start();
  subflow.emplace([&]() {
    while (!UniverseComputilityDataLog::GetInstance()->IsReady()) {
    }
    LOG(INFO) << "UniverseComputilityDataLog Load finish, latency:"
              << UniverseComputilityDataLog::GetInstance()->GetInitLatency();
  });

  UniverseQcpxDeliveryDataLog::GetInstance()->Start();
  subflow.emplace([&]() {
    while (!UniverseQcpxDeliveryDataLog::GetInstance()->IsReady()) {
    }
    LOG(INFO) << "UniverseQcpxDeliveryDataLog Load finish, latency:"
              << UniverseQcpxDeliveryDataLog::GetInstance()->GetInitLatency();
  });

  //  加载联盟下发到曝光率预估系数
  StartWeakTask(subflow, UniverseServerShowRatioCaliP2p::GetInstance());
  StartWeakTask(subflow, engine_base::UniverseEcomBidP2P::GetInstance());
  // 加载先审后投二期白名单
  StartWeakTask(subflow, engine_base::AdCreativeAuditV2::GetInstance());
  // 加载联盟补贴分配比例
  StartTask(subflow, UniverseBenefitAllocationData::GetInstance());

  ::google::FlushLogFiles(::google::INFO);
}

void StopBgTask() {
  BgTaskStopMgr::Instance().Stop();
  UniverseTrafficControl::Instance().Stop();
  LOG(INFO) << "UniverseTrafficControl Stopped";

  ::google::FlushLogFiles(::google::INFO);
  ks::ad_base::Singleton<ks::engine_base::CpmBoundLogManager>::instance().Stop();
  LOG(INFO) << "CpmBoundLogManager Stopped";
  ks::ad_base::Singleton<UniverseAuctionLogManager>::instance().Stop();
  LOG(INFO) << "UniverseAuctionLogManager Stopped";
  UniverseComputilityDataLog::GetInstance()->Stop();
  LOG(INFO) << "UniverseComputilityDataLog Stopped";
  UniverseQcpxDeliveryDataLog::GetInstance()->Stop();
  LOG(INFO) << "UniverseQcpxDeliveryDataLog Stopped";
  // 等待 15 秒，确保线程完全退出，防止 core
  base::SleepForMilliseconds(15000);
}

}  // namespace front_server
}  // namespace ks
