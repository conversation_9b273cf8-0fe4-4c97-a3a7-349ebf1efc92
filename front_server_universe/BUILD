os.environ['BUILD_AD_WITH_DRAGON']='1'
cc_library(
    name = 'context_data',
    srcs = [
      "engine/strategy/admit/*.cc",
      "engine/context_data/*.cc",
      "engine/utils/data_adapter/*.cc",
    ],
    deps = [
      ":ad_user_info",
      ":utility",
      "//teams/ad/ad_base/src/attrs/BUILD:fanstop_common_attr",
      "//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis",
      "//ks/base/abtest/BUILD:common",
      "//teams/ad/ad_base/src/jemalloc_hook/BUILD:link_jemalloc",
      "//teams/ad/ad_base/src/kafka/BUILD:kafka",
      "//teams/ad/ad_base/src/util/BUILD:util",
      "//third_party/abseil/BUILD:abseil",
      "//third_party/rapidjson/BUILD:rapidjson",
      "//teams/ad/ad_base/src/pos_manager/BUILD:ad_pos_manager_base",
      "//teams/ad/engine_base/BUILD:bg_thread",
      "//teams/ad/engine_base/BUILD:universe_query_stat_common",
      "//teams/ad/pslab/BUILD:refactor",
      "//teams/ad/ad_base/src/spdm_lib/BUILD:spdm_lib",
      "//teams/ad/engine_base/BUILD:reco_user_info_wrapper",
      "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
      "//teams/ad/engine_base/BUILD:rta_retrieval",
      "//teams/ad/engine_base/BUILD:search_string_util",
      "//serving_base/jansson/BUILD:json",
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_adsocial_ad_social_follow_reco__proto",
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_fanstop_fans_top_follow_service__proto",
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_engine_trace_log_engine_chain_log__proto",
      "//teams/ad/ad_proto/kuaishou/BUILD:search_search_realtime_action__proto",
      "//teams/ad/engine_base/BUILD:biz_common",
      "//teams/ad/engine_base/BUILD:advertiser_side_shunt_ab",
      "//teams/ad/ad_base/src/auto_param/BUILD:auto_param",
      "//teams/ad/ad_proto/maven/BUILD:corerpc_photo_photo_author_service__proto",
    ],
    cppflags = [
        '-Iinfra/redis_proxy_client/src',
        '-Iinfra/netr/src/',
        '-Ithird_party/abseil/',
        '-Ithird_party/libev/',
        '-Ithird_party/glog',
        '-Ithird_party/protobuf_v3/src',
        '-Ithird_party/taskflow',
        '-Ithird_party/nlohmann_json/include',
    ]
)

cc_library(
  name = 'universe_record_auction_log',
  srcs = [
    "util/universe_record_auction_log/*.cc"
  ],
  deps = [
    "//teams/ad/ad_base/src/kafka/BUILD:kafka",
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    '-Ithird_party/glog/',
    '-Iinfra/netr/src/',
    '-Ithird_party/libev/',
    '-Ithird_party/protobuf_v3/src',
  ]
)

cc_library(
  name = 'universe_computility_data_log',
  srcs = [
    "util/universe_computility_data_log/*.cc"
  ],
  deps = [
    "//teams/ad/ad_base/src/kafka/BUILD:kafka",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_universe_computility_data__proto"
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    '-Ithird_party/glog/',
    '-Iinfra/netr/src/',
    '-Ithird_party/libev/',
    '-Ithird_party/protobuf_v3/src',
  ]
)

cc_library(
  name = 'universe_qcpx_delivery_data_log',
  srcs = [
    "util/universe_qcpx_delivery_data_log/*.cc"
  ],
  deps = [
    "//teams/ad/ad_base/src/kafka/BUILD:kafka",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_universe_delivered_qpon_info__proto"
  ],
  cppflags = [
    '-Ithird_party/abseil/',
    '-Ithird_party/glog/',
    '-Iinfra/netr/src/',
    '-Ithird_party/libev/',
    '-Ithird_party/protobuf_v3/src',
  ]
)

cc_library(
    name = 'utility',
    srcs = [
      "util/utility/*.cc",
      "engine/utils/*.cc",
    ],
    deps = [
      ":ad_pos_manager",
      "//infra/falcon_counter/BUILD:falcon_counter",
      "//teams/ad/ad_index/BUILD:ad_index_api",
      "//teams/ad/ad_base/src/kess/BUILD:kess_client",
      "//teams/ad/ad_base/src/dot/BUILD:ad_base_dot",
      "//teams/ad/ad_base/src/pos_manager/BUILD:ad_pos_manager_base",
      "//third_party/abseil/BUILD:abseil",
      "//infra/ccbase/BUILD:ccbase",
      "//infra/location/BUILD:location",
      "//infra/kenv/BUILD:kenv",
      "//infra/ktrace/BUILD:ktrace",
      "//infra/dynamic_kafka_client/BUILD:dynamic_kafka_client",
      "//teams/ad/ad_base/src/util/BUILD:util",
      '//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader',
      "//third_party/glog/BUILD:glog",
      "//third_party/grpc-v1100/BUILD:grpc++",
      "//third_party/gflags/BUILD:gflags",
      "//third_party/jsoncpp/BUILD:jsoncpp",
      "//third_party/folly/BUILD:folly",
      "//base/time/BUILD:time",
      "//base/common/BUILD:base",
      "//base/thread/BUILD:thread",
      "//base/encoding/BUILD:encoding",
      "//base/hash_function/BUILD:hash_function",
      "//base/random/BUILD:random",
      "//base/file/BUILD:file",
      "//ks/base/perfutil/BUILD:perfutil",
      "//ks/util/BUILD:util",
      "//serving_base/util/BUILD:util",
      "//serving_base/region/BUILD:region",
      '//teams/ad/engine_base/BUILD:diff_test_tool',
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_ssp_ad_ssp__proto",
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_charge_ad_charge_service__proto",
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_style_service__proto",
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_dsp_ad_dsp_base_rpc_service__proto",
      "//teams/ad/engine_base/BUILD:ad_utility",
    ],
    cppflags = [
      "-Iinfra/redis_proxy_client/src",
      "-Iinfra/netr/src/",
      "-Ithird_party/libev/",
      "-Ithird_party/glog",
      "-Ithird_party/abseil/",
    ]
)

cc_library (
  name = "smart_compute_power",
  srcs = [
    "./bg_task/smart_compute_power/*.cc",
  ],
  deps = [
    "//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis",
    "//third_party/nlohmann_json/BUILD:nlohmann_json",
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
  ],
  cppflags = [
    "-Iinfra/redis_proxy_client/src",
  ]
)

cc_library (
  name = "win_rate_model_data",
  srcs = [
    "./bg_task/universe_win_rate_model_data/*.cc",
  ],
  deps = [
    "//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis",
    "//third_party/nlohmann_json/BUILD:nlohmann_json",
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
  ],
  cppflags = [
    "-Iinfra/redis_proxy_client/src",
  ]
)

proto_library(
    name = "kconf_data_proto",
    srcs = [
      "./util/kconf/*.proto",
    ],
)

proto_library(
  name = 'processor_proto',
  srcs = [
    'processor/proto/formula_type.proto',
  ],
)

cc_library(
    name = 'kconf',
    srcs = [
        "./util/kconf/kconf_split/*.cc",
        "./util/kconf/kconf_data.cc",
    ],
    deps = [
        '//teams/ad/ad_base/src/serialization/BUILD:serialization',
        "//teams/ad/ad_base/src/kconf/BUILD:kconf_node",
        "//teams/ad/bid_server/bid_universe_adx/BUILD:kconf",
        "//third_party/rapidjson/BUILD:rapidjson",
        "//base/hash_function/BUILD:hash_function",
        ":kconf_data_proto",
    ],
    cppflags = [
        '-Ithird_party/rapidjson/include/',
    ]
)

cc_library(
    name = 'adx',
    srcs = [
      './util/adx/*.cc'
    ],
    deps = [
      ":kconf",
      "//third_party/abseil/BUILD:abseil",
      "//teams/ad/ad_base/src/crypt/BUILD:crypt",
      '//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader',
    ],
    cppflags = [
      '-Ithird_party/abseil/',
    ]
)

cc_library(
    name = 'track',
    srcs = [
      './util/track/*.cc'
    ],
    deps = [
      ":kconf",
      "//third_party/abseil/BUILD:abseil",
    ],
    cppflags = [
      '-Ithird_party/rapidjson/include/',
      '-Ithird_party/abseil/',
    ]
)

cc_library(name = "ad_user_info",
           srcs = [
             "./util/ad_user_info/*.cc",
             "./util/grpc/*.cc",
           ],
           deps = [
             ":kconf",
             ":utility",
             ":universe_traffic_control",
            "//teams/ad/ad_base/src/kafka/BUILD:kafka",
            "//infra/redis_proxy_client/BUILD:redis_client",
            "//third_party/abseil/BUILD:abseil",
            '//teams/ad/ad_base/src/dot/BUILD:ad_base_dot',
            "//teams/ad/ad_base/src/pos_manager/BUILD:ad_pos_manager_base",
            '//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader',
            '//teams/ad/ad_base/src/kconf/BUILD:kconf_node',
            '//teams/ad/picasso/BUILD:picasso_sdk',
            '//teams/ad/engine_base/BUILD:user_profile_cache',
           ],
           cppflags = [
            "-Iinfra/redis_proxy_client/src",
            "-Iinfra/netr/src/",
            "-Ithird_party/libev/",
            "-Ithird_party/abseil/",
            "-Ithird_party/glog",
            "-Ithird_party/boost/",
           ]
           )

cc_library(name = "ad_pos_manager",
           srcs = [
              "util/ad_pos_manager/*.cc",
           ],
           deps = [
             ":kconf",
            "//teams/ad/ad_base/src/kafka/BUILD:kafka",
            "//infra/redis_proxy_client/BUILD:redis_client",
            "//third_party/abseil/BUILD:abseil",
            '//teams/ad/ad_base/src/dot/BUILD:ad_base_dot',
            "//teams/ad/ad_base/src/pos_manager/BUILD:ad_pos_manager_base",
            "//third_party/nlohmann_json/BUILD:nlohmann_json",
            '//teams/ad/ad_base/src/kconf/BUILD:kconf_node',
            '//teams/ad/picasso/BUILD:picasso_sdk',
            '//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader',
            "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_style_service__proto",
            "//teams/ad/ad_proto/kuaishou/BUILD:ad_ssp_ad_ssp__proto",
           ],
           cppflags = [
            "-Iinfra/redis_proxy_client/src",
            "-Iinfra/netr/src/",
            "-Ithird_party/libev/",
            "-Ithird_party/abseil/",
            "-Ithird_party/boost/",
            '-Ithird_party/nlohmann_json/include',
           ]
           )

cc_library(
    name = 'ad_server_show',
    srcs = [
      './util/ad_server_show/*.cc'
    ],
    deps = [
      ":utility",
      "//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis",
      "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_request_user_profile_log__proto",
      "//teams/ad/ad_base/src/kafka/BUILD:kafka",
      "//infra/falcon_counter/BUILD:falcon_counter",
      "//teams/ad/ad_base/src/kess/BUILD:kess_client",
      "//teams/ad/ad_base/src/dot/BUILD:ad_base_dot",
      "//teams/ad/ad_base/src/pos_manager/BUILD:ad_pos_manager_base",
      "//third_party/abseil/BUILD:abseil",
      '//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader',
      "//teams/ad/ad_base/src/util/BUILD:util",
      "//third_party/nlohmann_json/BUILD:nlohmann_json",
    ],
    cppflags = [
      "-Iinfra/redis_proxy_client/src",
      "-Iinfra/netr/src/",
      "-Ithird_party/libev/",
      "-Ithird_party/abseil/",
      '-Ithird_party/nlohmann_json/include',
    ]
)

cc_library(
  name = 'universe_operation_discount',
  srcs = [
    'bg_task/universe_operation_discount/*.cc',
  ],
  deps = [
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    '//teams/ad/engine_base/BUILD:universe_operation_config',
  ]
)

cc_library(
  name = 'universe_operation_bound_explore',
  srcs = [
    'bg_task/universe_operation_bound_explore/*.cc',
  ],
  deps = [
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    '//teams/ad/engine_base/BUILD:universe_operation_config',
  ]
)

cc_library(
  name = 'ad_universe_benefit_allocation_data',
  srcs = [
    'bg_task/ad_universe_benefit_allocation_data/*.cc'
  ],
  deps = [
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
  name = 'universe_new_creative_data',
  srcs = [
    'bg_task/universe_new_creative_data/*.cc',
  ],
  deps = [
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
  name = 'universe_new_media_protection',
  srcs = [
    'bg_task/universe_new_media_protection/*.cc',
  ],
  deps = [
    "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
  ]
)

cc_library(
    name = 'universe_flow_strategy',
    srcs = [
      'bg_task/universe_flow_strategy/*.cc'
    ],
    deps = [
      "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
    name = 'universe_server_show_ratio_cali',
    srcs = [
      'bg_task/universe_server_show_ratio_cali_data/*.cc'
    ],
    deps = [
      "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
    name = 'universe_app_package2product',
    srcs = [
      'bg_task/universe_app_package2product/*.cc'
    ],
    deps = [
      "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
    name = 'universe_crowd_strategy',
    srcs = [
      'bg_task/universe_crowd_strategy/*.cc'
    ],
    deps = [
      "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
    name = 'universe_active_crowd_strategy',
    srcs = [
      'bg_task/universe_active_crowd_strategy/*.cc'
    ],
    deps = [
      "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader",
    ]
)

cc_library(
    name = 'universe_benefit_stats',
    srcs = [
      'bg_task/universe_benefit_stats/*.cc'
    ],
    deps = [
      "//teams/ad/ad_base/src/redis_cache_loader/BUILD:redis_cache_loader",
      "//teams/ad/engine_base/BUILD:kconf",
    ]
)

cc_library(
    name = 'creative_parser',
    srcs = [
      './util/creative_parser/*.cc'
    ],
    deps = [
      ":utility",
      "//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis",
      "//infra/falcon_counter/BUILD:falcon_counter",
      "//third_party/abseil/BUILD:abseil",
      "//teams/ad/ad_base/src/dot/BUILD:ad_base_dot",
      '//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader',
    ],
    cppflags = [
      '-Ithird_party/abseil/',
      '-Ithird_party/glog/',
    ]
)

cc_library(
    name = 'response_check',
    srcs = [
      './util/response_check/*.cc'
    ],
    deps = [
      ":kconf",
      ":context_data"
    ],
    cppflags = [
    ]
)

cc_library(name = "front_service",
           srcs = [
             "engine/node/plugins/plugin_base.cc",
             "engine/node/plugins/universe_auction/*.cc",
             "engine/node/plugins/universe_bidding/*.cc",
             "engine/node/plugins/universe_cpm_threshold/*.cc",
             "engine/node/plugins/ad_filter/*.cc",
             "util/merchant_util/*.cc",
             "util/spdm/*.cc",
             "common/*.cc",
             "engine/*.cc",
             "engine/core/*.cc",
             "engine/strategy/*.cc",
             "engine/strategy/style_postproc/*.cc",
             "engine/node/*.cc",
             "engine/node/plugins/admit/*.cc",
             "engine/utils/ad_pack/deliver_info_pack/*.cc",
             "engine/utils/ad_pack/*.cc",
             "engine/utils/ad_pack/base_info_pack/*.cc",
             "engine/utils/merchant/*.cc",
             "engine/utils/universe/*.cc",
             "engine/utils/*.cc",
             "engine/utils/ad_libretrieval/*.cc",
             "util/color/*.cc",
             "util/ad_style_data/*.cc",
           ],
           excludes = [
             "engine/*_test.cc",
             "engine/*_server.cc",
             "engine/external_library.cc"
           ],
           deps = [
             ":context_data",
             ":trace",
             ":ad_server_show",
             ":kconf",
             ":utility",
             ":ad_user_info",
             ":ad_pos_manager",
             ":smart_compute_power",
             ":win_rate_model_data",
             ":creative_parser",
             ":adx",
             ":track",
             ":response_check",
             "//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis",
             ":universe_client_limiter_controller",
             ":universe_server_show_ratio_cali",
             ":universe_app_package2product",
             ":universe_crowd_strategy",
             ":universe_active_crowd_strategy",
             "//teams/ad/engine_base/BUILD:expansion_checker",
             "//infra/falcon_counter/BUILD:falcon_counter",
             "//infra/utility/BUILD:aes",
             "//teams/ad/ad_base/src/admit/BUILD:ad_base_admit",
             "//teams/ad/ad_base/src/utility/BUILD:util",
             "//teams/ad/engine_base/BUILD:pb_empty_value_check",
             "//teams/ad/engine_base/BUILD:dragon_kess_convert_message",
             '//teams/ad/engine_base/BUILD:p2p_cache_loader',
             "//teams/ad/engine_base/BUILD:cache_loader",
             "//teams/ad/engine_base/BUILD:dragon_node_warpper",
             "//teams/ad/ad_base/src/kafka/BUILD:kafka",
             "//teams/ad/ad_base/src/kess/BUILD:kess_client",
             "//teams/ad/ad_base/src/log_record/BUILD:log_record",
             "//teams/ad/ad_athena/utils/BUILD:perf_util",
             "//teams/ad/dragon_exts/src/BUILD:ad_dynamic_compute_processors",
             "//teams/ad/dragon_exts/src/BUILD:ad_base_processors",
             "//teams/ad/dragon_exts/src/BUILD:ad_router_processors",
             "//ks/serving_util/BUILD:serving_util",
             "//infra/kess_grpc-v1100/BUILD:kess-rpc",
             "//infra/kess_grpc-v1100/BUILD:kess-framework",
             "//third_party/abseil/BUILD:abseil",
             "//teams/ad/ad_base/src/trace_filter/BUILD:trace_filter",
             "//teams/ad/picasso/BUILD:picasso_sdk",
             "//ks/base/abtest/BUILD:common",
             "//serving_base/crypt/BUILD:crypt",
             "//ks/serving_util/BUILD:serving_util",
             "//teams/ad/ad_base/src/geohash/BUILD:location",
             "//teams/ad/ad_base/src/dot/BUILD:ad_base_dot",
             "//teams/ad/ad_base/src/pb_helper/BUILD:pb_helper",
             "//teams/ad/pslab/BUILD:refactor",
             "//third_party/libcuckoo/BUILD:cuckoo",
             "//third_party/nlohmann_json/BUILD:nlohmann_json",
             "//third_party/boost/BUILD:boost_serialization",
             "//infra/traffic_record/BUILD:grpc_traffic_record",
             "//teams/ad/ad_base/src/traffic_record/BUILD:traffic_record",
             "//teams/ad/engine_base/BUILD:budget_common",
             "//teams/ad/engine_base/BUILD:fanstop_pv_debug",
             '//teams/ad/engine_base/BUILD:utils',
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_pid_tag_enum__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_counter_ad_counter_service__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_rank_service__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_bid_server_auction_log__proto",
             '//teams/ad/ad_base/src/common/BUILD:auto_bid_group_tags',
             '//teams/ad/ad_base/src/redis/BUILD:ad_redis',
             "//teams/ad/engine_base/BUILD:cache_loader_front_server",
             "//teams/ad/engine_base/BUILD:tag_extra_timeout_parser",
             "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader_proto",
             "//teams/ad/ad_base/src/common/BUILD:ad_session_context",
             "//teams/ad/ad_base/src/spdm/BUILD:spdm",
             "//teams/ad/engine_base/BUILD:session_res",
             "//teams/ad/forward_index/BUILD:forward_client_lib",
             "//teams/ad/ad_base/src/utility/BUILD:util",
             "//teams/ad/engine_base/BUILD:kconf",
             "//teams/ad/engine_base/BUILD:kafka_consumer",
             "//teams/ad/engine_base/BUILD:style_app_download_info",
             "//third_party/nlohmann_json/BUILD:nlohmann_json",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_query_parser_service__proto",
             "//teams/ad/ad_proto/maven/BUILD:corerpc_user_user_cache_service__proto",
             "//teams/ad/ad_proto/maven/BUILD:corerpc_user_user_count__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_eds__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_cpm_bound_log__proto",
             "//teams/ad/ad_proto/maven/BUILD:corerpc_livestream_live_stream_display_count_query__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:photo_plc_feature_entry__proto",
             "//teams/ad/ad_proto/maven/BUILD:kwaishop_selection_user_service__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_material__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_antispam_service_union_ad_request_service__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:antispam_antispam_ad_union_request_service__proto",
             "//teams/ad/ad_proto/maven/BUILD:merchant_photo_merchant_photo_item_info_service__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_adx_track_adx_track__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_grpc_service__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_universe_universe_feature_freq__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_universe_universe_bid_loss__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_near_by_grpc_service__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_algorithm_model_ad_search_cover__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_grpc_service__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_charge__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_consume__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:fanstop_fans_top_trace_log__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_front_service__proto",
             "//teams/ad/ad_proto/maven/BUILD:detail_kwaishop_product_detail_service__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_ad_task_log__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_industry_opt_playlet_opt__proto",
             "//teams/ad/ad_proto/kuaishou/BUILD:ad_search_ads_search_ad_trending_base__proto",
             "//teams/ad/ad_proto/maven/BUILD:kwaishop_product_c_baseinfo_service__proto",
             "//teams/ad/ad_proto/maven/BUILD:c_kwaishop_product_dynamicattr_service__proto",
             '//teams/ad/engine_base/BUILD:cache_loader_sas',
             "//teams/aiplatform/inference_sdk/proto/engine/BUILD:engine_proto",
             "//teams/ad/engine_base/BUILD:ad_utility",
             "//teams/ad/engine_base/BUILD:search_kconf",
             "//teams/ad/engine_base/BUILD:search_card_style_util",
             "//teams/ad/engine_base/BUILD:data_frame_util",
             "//teams/ad/ad_user_data_center/BUILD:user_data_center_client",
             "//teams/reco-arch/colossusdb/client/BUILD:kconf_client",
             "//teams/reco-arch/colossusdb/client/BUILD:sim_client",
             "//teams/ad/ad_user_data_center/BUILD:user_data_center_client",
             "//teams/ad/ad_proto/kuaishou/BUILD:reco_mix_rank_mix_common__proto",
             "//teams/ad/engine_base/BUILD:proto_extension_helper",
             "//learning/kuiba/predict_base/BUILD:common_predict_client",
             "//ks/reco_pub/reco/predict/clients/BUILD:api",
             "//teams/ad/ad_dynamic_compute/BUILD:pid_solver_sdk",
             "//teams/ad/ad_dynamic_compute/BUILD:dynamic_share_solver_sdk",
             "//teams/ad/grid/BUILD:remote_table",
           ],
           cppflags = [
                   '-Ithird_party/glog/',
                   '-Ithird_party/abseil/',
                   '-Iinfra/redis_proxy_client/src',
                   '-Iinfra/netr/src/',
                   '-Iinfra/utility/src/',
                   '-Ithird_party/libev/',
                   '-I.build/pb/c++/teams/ad/ad_proto/kuaishou/ad',
                   '-Ithird_party/nlohmann_json/include',
           ],
           link_all_symbols = True,
           )

cc_library(name = "bg_task",
           srcs = [
            "bg_task/*.cc"
           ],
           deps = [
            ":universe_record_auction_log",
            ":universe_computility_data_log",
            ":universe_qcpx_delivery_data_log",
            ":smart_compute_power",
            ":win_rate_model_data",
            ":universe_operation_discount",
            ":universe_operation_bound_explore",
            ":universe_new_creative_data",
            ":ad_universe_benefit_allocation_data",
            ":universe_new_media_protection",
            ":universe_flow_strategy",
            ":universe_benefit_stats",
            ":ad_pos_manager",
            "//teams/ad/engine_base/BUILD:kafka_consumer",
            "//teams/ad/engine_base/BUILD:bg_thread",
            "//teams/ad/engine_base/BUILD:budget_common",
            "//teams/ad/engine_base/BUILD:fanstop_pv_debug",
            "//teams/ad/engine_base/BUILD:cache_loader_front_server",
            "//teams/ad/engine_base/BUILD:p2p_cache_loader",
            "//teams/ad/engine_base/BUILD:tag_extra_timeout_parser",
            "//teams/ad/ad_base/src/timer_task/BUILD:p2p_cache_loader_proto",
            '//teams/ad/engine_base/BUILD:cache_loader_sas',
            "//teams/ad/engine_base/BUILD:style_app_download_info",
            "//teams/ad/engine_base/BUILD:search_risk_control_user",
           ],
           cppflags = [
            '-Ithird_party/taskflow/',
           ]
)

cc_binary(name = "ad_front_server",
          srcs = ["engine/front_server.cc",
                  "processor/framework/*.cc",
                  "processor/*.cc",
                  #"processor/select/*.cc",
                  #"processor/utils/*.cc",
                  #"processor/formula/*.cc",
                  ],
          deps = [
            ":kconf",
            ":front_service",
            ":bg_task",
            "//serving_base/utility/BUILD:signal",
            "//serving_base/thp_component/BUILD:kthp",
            "//base/strings/BUILD:strings",
            "//base/common/BUILD:base",
            "//teams/ad/ad_base/src/kess/BUILD:kess_client",
            "//teams/ad/ad_base/src/profile/BUILD:hotspots_service",
            "//teams/ad/ad_base/src/klog/BUILD:klog",
            "//teams/ad/engine_base/BUILD:knews_pos_util",
            "//teams/ad/engine_base/BUILD:billing_separate",
            "//third_party/abseil/BUILD:abseil",
            "//infra/kess_grpc-v1100/BUILD:kess-rpc",
            "//infra/kess_grpc-v1100/BUILD:kess-framework",
            "//teams/ad/engine_base/BUILD:flow_checker",
            "//teams/ad/engine_base/BUILD:client_limiter",
            "//teams/ad/ad_base/src/abtest/BUILD:abtest_mocker",
            ":universe_traffic_control",
            "//dragon/src/BUILD:framework_base_with_service",
            ':processor_proto',
          ],
          cppflags = [
            '-Ithird_party/abseil/',
            '-Ithird_party/glog/',
            '-Iinfra/netr/src/',
            '-Ithird_party/libev/',
            '-Ithird_party/abseil/',
            '-Iinfra/redis_proxy_client/src',
            '-I.build/pb/c++/teams/ad/ad_proto/kuaishou/ad'
          ],
          ldflags = [
          ]
          )

cc_library(
  name = 'trace',
  srcs = [
    "trace/*.cc"
  ],
  deps = [
    "//teams/ad/engine_base/BUILD:creative_debug_util",
    "//ks/base/container/BUILD:container",
    "//teams/ad/ad_proto/kuaishou/BUILD:ad_splash_trace_log__proto",
    ":context_data",
  ]
)

cc_library(
  name='universe_traffic_control',
  srcs = [
    './util/universe_traffic_control/*.cc'
  ],
  deps = [
    ':kconf',
    ':universe_client_limiter_controller',
    '//ks/base/abtest/BUILD:common',
    '//teams/ad/ad_base/src/clock_cache/BUILD:clock_cache',
    '//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis',
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
    "//teams/ad/engine_base/BUILD:client_limiter",
    "//third_party/tbb/BUILD:tbb",
    "//third_party/tbb/BUILD:tbbmalloc",
  ]
)

cc_library(
  name='universe_client_limiter_controller',
  srcs = [
    './util/universe_client_limiter_controller/*.cc'
  ],
  deps = [
    ':kconf',
    '//ks/base/abtest/BUILD:common',
    '//teams/ad/ad_base/src/clock_cache/BUILD:clock_cache',
    '//teams/ad/ad_base/src/redis/BUILD:ad_kconf_redis',
    "//teams/ad/ad_base/src/kess/BUILD:kess_client",
  ]
)
