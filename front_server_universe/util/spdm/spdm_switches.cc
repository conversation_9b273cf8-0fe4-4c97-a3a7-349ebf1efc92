#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace front_server {

// 定义 kconf 开关:  SPDM_KCONF_BOOL(path, key);
// 定义 abtest 开关: SPDM_ABTEST_BOOL(key, AbtestBiz);

// 以下为 kconf 开关定义.
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePlay3sToImpression);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFillLiveStreamCoreUser);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePrintAllFactorInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUseDebugPageId);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAllScene);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableFixPriceLow);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableLogAdStid);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, disableCacheLiveAd);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUniverseRegionAdcodeOpt);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enablePbEmptyValueCheck);
SPDM_KCONF_BOOL(ad.adFlowControl, enableEspBudgetPartition);
SPDM_KCONF_BOOL(ad.adFlowControl, enableOnlyEspBudgetPartition);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableRemoveOnlineJoinPriceRecord);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUserContextAllStation);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableAddRelType);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, universePreviewServerShowRecordFix);
SPDM_ABTEST_BOOL(enable_universe_store_search_skip_dark, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dark_c_skip_dark_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_store_distribut_skip_dark, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_update_user_agent, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_relax_timeout_setting, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_distribut_flow_skip_search_ad_dark, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kwai_app_by_product_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_kwai_app_installed_reverse_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(kwai_active_data_ttl_judge, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_BOOL(enable_universe_get_router_user_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_closure_support_mode, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_reservation_ad_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_app_ad_in_front, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.frontserver, enableUniversePreviewStyleByKconf);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUniverseSampleCachedTag);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, universePreviewStyleCheck);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, universePreviewDisableOriginLive);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableNewInnerFanstopStid);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSimplifyDiscountPrice);
SPDM_KCONF_BOOL(ad.frontserver, enableFixRtbBouns);
SPDM_KCONF_BOOL(ad.frontserver, enableUniverseRtbCpmBouns);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableSearchTraceHotQuery);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUpdateSearchSampleTag);
SPDM_KCONF_BOOL(ad.frontserver, enableSplashAdxPackOnlineJoinParams);
SPDM_KCONF_BOOL(ad.adRank, disableAllPerf);
SPDM_KCONF_BOOL(ad.frontserver2, enableNoAdsSendUniverseQcpxDeliveryLogInfo);
SPDM_KCONF_BOOL(ad.frontserver2, enableAddProjectFieldsToAdBaseInfo);
SPDM_KCONF_BOOL(ad.frontserver2, enableMoveBuildAdResponseToFrontDiffDefault);
SPDM_KCONF_BOOL(ad.frontserver2, enableMoveBuildAdResponseToFrontDiffSearch);
SPDM_KCONF_BOOL(ad.frontserver2, enableMoveBuildAdResponseToFrontDiffSplash);
SPDM_KCONF_BOOL(ad.frontserver2, enableHarmonyMinimumAppVersion);
SPDM_KCONF_BOOL(ad.frontserver2, enableHarmonyClientId);
SPDM_KCONF_BOOL(ad.frontserver2, universeEspMobileSoruceTypeFix);
SPDM_KCONF_BOOL(ad.frontserver2, enableUniverseTinyLogQueryType);
SPDM_KCONF_BOOL(ad.frontserver2, enableCheckBudgetDeliveryType);
SPDM_KCONF_BOOL(ad.frontserver2, enableUniverseUseCommonPidControl);
SPDM_KCONF_BOOL(ad.frontserver2, enableUniversePassNewFreqMap);
SPDM_KCONF_BOOL(ad.frontserver2, enableUniverseFreqGlobalSync);
SPDM_KCONF_BOOL(ad.frontserver2, enableUniverseFixUniverseElasticInfo);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableUniverseCarouselPerfRecord);
SPDM_KCONF_BOOL(ad.adRank3, enableUniverseQcpxPerf);
SPDM_KCONF_BOOL(ad.frontserver2, universeAppIdAdmitWhiteListReplace);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, universeContextInitFilterUpdate);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, universeFlowTypeCheckAdvanced);
SPDM_KCONF_BOOL(ad.frontserver2, usePaidFromUniverseRequest);
// ------------------------------ kconf int32 参数定义 ---------------------------------
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, requestDebugInfoRatio, 1);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, allStationDataFlowRate, 500);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, maxUniverseClickRewardCntOneDay, 30);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, maxUniverseClickRewardCntOneHour, 10);
SPDM_KCONF_INT32(ad.adFrontDiffSwitches, bidGimbalTailNumber, 0);
SPDM_KCONF_INT32(ad.frontserver2, universePidControlRandomExploreRate, 1);
// ------------------------------ kconf double 参数定义 ---------------------------------
// ------------------------------ kconf string 参数定义 ---------------------------------

// 以下为 abtest 开关定义.
SPDM_ABTEST_INT64(universe_hold_cache_remaining_ms, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_BOOL(enable_rtb_win_rate_loss_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_rtb_win_rate_predict_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_smart_compute_uplift, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_stid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_pid_solver_tp_ratio, ks::AbtestBiz::AD_DSP, 99);
SPDM_ABTEST_BOOL(disable_univerese_front_adx_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_pid_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(api_ad_cache_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_timeout_with_post_data_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_timeout_with_post_data_v4, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_timeout_level_v2, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_STRING(universe_timeout_exp_tag_not_combo, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_timeout_exp_tag_for_combo, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_android_app_platform_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reco_user_info_trim_for_ps, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_cpm_bound_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_residence_adcode_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_inner_rpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_rerank_add_author_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_rerank_add_dup_photo_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_client_ai_is_high_value_use_threshold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bidding_cpm_bound_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_interstitial_carousel, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_honor_market_replace_url, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_next_n_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(client_ai_high_value_use_cpm_or_rankbenifit, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(client_ai_high_value_use_threshold_value, ks::AbtestBiz::AD_DSP, 1000000);
SPDM_ABTEST_BOOL(enable_client_ai_is_high_value_use_profile, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(client_ai_high_value_user_days, ks::AbtestBiz::AD_DSP, 7);
SPDM_ABTEST_INT64(client_ai_high_value_user_pctiles, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_INT64(live_random_move_thresh, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_universe_age_expand, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pinduoduo_second_price_add_kconf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_product_bonus_post_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_dynamic_pos,  ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kxy_subsidy_global_nc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_person_mark_physical_posid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_univ_inner_loop_filter_uplift_front_exit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_agg_scene_algo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_agg_scene_discount_ab_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_elastic_control_config_swift, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(enable_universe_agg_scene_price_top_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(enable_universe_agg_scene_cpm_top_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_universe_agg_scene_algo_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_agg_scene_discount_ab_tag_v2, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_INT64(enable_universe_agg_scene_price_top_num_v2, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(enable_universe_agg_scene_cpm_top_num_v2, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(universe_carousel_scene_discount_ab_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_INT64(enable_universe_carousel_scene_cpm_top_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(enable_universe_carousel_scene_price_top_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(dac_user_type, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_BOOL(enable_click_after_recommend, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_rtb_bouns_upgrade, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rtb_presonalized_upgrade, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_nobid_second_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_cost_cap_second_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(force_reco_rb_thrd, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_BOOL(enable_force_reco_rb_thrd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_same_second_industry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_force_reco_sort_by_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_valid_flag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_app_detail, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(max_hard_quota, ks::AbtestBiz::AD_DSP, 8);
SPDM_ABTEST_INT64(max_soft_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(disable_cache_all_live_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guided_feedback_soft_ad_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_nobid_charge_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(inner_cost_cap_charge_ratio_upper, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(inner_live_nobid_charge_ratio_upper, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(merchant_video_pay_charge_ratio_upper, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_BOOL(enable_p2live_show_live_stream_1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_p2live_show_live_stream_13, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_agg_scene_discount_ratio, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_INT64(universe_agg_scene_ad_num, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_BOOL(universe_record_quick_search_budget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_delivered_qpon_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(cache_remove_live_type, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_universe_use_new_hash_did, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(splash_combo_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(inspire_shake_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(splash_shake_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(splash_rotate_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(splash_slide_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(splash_combo2_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(fullscreen_combo3_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(inspire_combo3_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(interstitial_combo2_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(inspire_rotate_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(fullscreen_rotate_frequency_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_BOOL(store_delivery_skip_key_word_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(store_delivery_skip_vivo_adx_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_feed_rotate_frequency_threshold, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(universe_feed_rotate_interval_threshold, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_INT64(universe_draw_rotate_frequency_threshold, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(feedShakeCountDaily, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(feedShakeInterval, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_BOOL(enable_universe_inner_cpm_bound_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_forced_impression_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_forced_impression_cheap_flow_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_crowd_forced_based_cpm_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_pay_crowd_adn_bound_forced_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_pay_crowd_adn_target_forced_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_active_crowd_adn_target_forced_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_w_level_crowd_adn_fill, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_llm_u2p_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_llm_u2p_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_llm_u2p_ecpc_raito, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_universe_ecpm_win_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_w_level_crowd_bidding_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_w_level_purchase_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_valid_w_level, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(universe_w_level_crowd_adn_max_delta_cpm, ks::AbtestBiz::AD_DSP, 200000000);
SPDM_ABTEST_DOUBLE(universe_w_level_crowd_adn_max_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_w_level_crowd_explore_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(universe_inner_top_explore_high_u_crowd_tag, ks::AbtestBiz::AD_DSP, 41);
SPDM_ABTEST_STRING(universe_ecpm_win_explore_rank_key, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_elastic_control_config_v2, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(universe_ecpm_win_explore_type_opt, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_universe_app_fill_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_app_fill_explore_upper_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_universe_inner_high_u_crowd_top_threshold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_inner_pv_allocation, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_inner_pv_allocation_ecpm_ratio_upbound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_inner_pv_allocation_ecpm_add_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(universe_inner_pv_allocation_ecpm_delta_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(universe_inner_high_u_crowd_threshold_max_ratio, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_INT64(universe_inner_high_u_crowd_threshold_max_delta_cpm, ks::AbtestBiz::AD_DSP, 100000000);
SPDM_ABTEST_BOOL(enable_universe_inner_high_u_crowd_top_cpm_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_inner_high_u_crowd_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_BOOL(enable_universe_qcpx_admit_u0_front, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(click_after_reco_commerce_weight,  ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(click_after_reco_rele_threshold,  ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_bs_dynamix_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_read_cache_compare_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_universe_fill_compare_ratio_check, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_read_cache_fill_compare_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_universe_predict_cpm_adjust_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_threshold, ks::AbtestBiz::AD_DSP, 100000000);
SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_fill_rate_threshold, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_threshold_layer2, ks::AbtestBiz::AD_DSP, 100000000);
SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_fill_rate_threshold_layer2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_value, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_value_layer2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_universe_inner_high_u_to_high_level, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_outer_high_u_to_high_level, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_user_scene_freq_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_cpm_filter_whole, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_DOUBLE(C_bu_min_filter_price, ks::AbtestBiz::AD_DSP, 300);
// [zhangxingyu03]
SPDM_ABTEST_BOOL(enable_inspire_live_one_more, ks::AbtestBiz::AD_DSP);
// [jiyang]
SPDM_ABTEST_BOOL(enable_small_app_direct_jump, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(ad_wise_user_group_prefix, ks::AbtestBiz::AD_DSP, "cpm");
SPDM_ABTEST_STRING(merchant_user_group_tag_prefix, ks::AbtestBiz::AD_DSP, "merchant");
SPDM_ABTEST_STRING(rta_user_score_prefix, ks::AbtestBiz::AD_DSP, "rta");
SPDM_ABTEST_BOOL(enable_inner_explore_min_page_size_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_explore_min_page_size_admit, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(carousel_daily_imp_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(carousel_hourly_imp_threshold, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(carousel_imp_interval, ks::AbtestBiz::AD_DSP, 3600);
SPDM_ABTEST_INT64(universe_carousel_ad_num, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_DOUBLE(user_group_server_show_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_wanhe_charge_action_type_with_fanstop, ks::AbtestBiz::AD_DSP)
SPDM_ABTEST_BOOL(enable_inspire_style_form, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_mall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_buyer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_guess, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_phone_material_fill, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merge_fanstop_session_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reco_user_info_ip_upgrade_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_filter_material_by_size, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_universe_new_creative_bonus_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_quick_search_skip_budget_control_universe, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_direct_search_skip_budget_control_universe, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(smart_offer_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_ranking_cache_read_plugin_types, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(universe_ranking_cache_write_plugin_types, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(C_activity, ks::AbtestBiz::AD_DSP, "ActivityA");
SPDM_ABTEST_DOUBLE(smart_offer_lower_bound_pay, ks::AbtestBiz::AD_DSP, 2.9);
SPDM_ABTEST_DOUBLE(smart_offer_lower_bound_offer, ks::AbtestBiz::AD_DSP, 2.9);
SPDM_ABTEST_DOUBLE(smart_offer_upper_bound_pay, ks::AbtestBiz::AD_DSP, 25.0);
SPDM_ABTEST_DOUBLE(smart_offer_upper_bound_offer, ks::AbtestBiz::AD_DSP, 25.0);
SPDM_ABTEST_DOUBLE(smart_offer_default_roi, ks::AbtestBiz::AD_DSP, 3.0);
SPDM_ABTEST_DOUBLE(smart_offer_min_pay_amount, ks::AbtestBiz::AD_DSP, 7.9);
SPDM_ABTEST_INT64(smart_offer_lessons_adjust, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(smart_offer_coins_adjust, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(retention_discount, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(retention_subsidy, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(pay_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(watch_task_checkpiont, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(watch_task_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(watch_task_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_second_price_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_early_return_offers, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(C_second_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(Paid_retention, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_watch_task_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_watch_task, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_you_like_live_card, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_pass_cpm_filter, ks::AbtestBiz::AD_DSP);
// [xiaowentao] 按照实验组来选择是否写入 FillUniverseActiveWLevelInfo
SPDM_ABTEST_BOOL(enable_universe_fill_active_w_level, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_quality_enhance_ratio_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_req_freq_app_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_write_universe_dcaf_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_dynamic_share_exp_id, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_dynamic_share_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_share_log_server_show, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_share_fix_virtual_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_share_by_cpm_interval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_share_cpm_clip, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_share_cpm_clip_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(dynamic_share_cpm_clip_thre, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_BOOL(enable_max_bonus_win_rate_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_share_rtb_coff_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_share_rtb_coff_fix_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_dcaf_rank_version_id, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(dynamic_quality_score_threshold_group_actuality, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(universe_dcaf_prerank_version_id, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(universe_disable_all_antispam, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_disable_freq_antispam, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(universe_rtb_win_rate_model_params, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(freq_strategy_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(program_creative_exp_type, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_fix_adx_cpm_zero_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_new_media_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_new_media_strategy_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_quality_model_default_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_detail_cpm_timeout_kafka, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_detail_cpm_uplift_kafka, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_cache_compare_ratio_check, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(universe_read_cache_compare_sdk_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(universe_read_cache_compare_api_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// huangwenbin
SPDM_ABTEST_BOOL(enable_universe_inner_u2x_redis_fill, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(universe_inner_u2x_redis_u_level, ks::AbtestBiz::AD_DSP, 0);
// [xiaowentao] ADX-CPM 计费截断修复
SPDM_KCONF_BOOL(ad.frontserver2, enableFixAdxCpmFix1Cpm);
// [caowenli] 通信行业探索开关
SPDM_ABTEST_BOOL(enable_universe_industry_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(adn_max_industry_explore_ratio, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(rtb_max_industry_explore_ratio, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_DOUBLE(step_industry_explore_win_rate_model, ks::AbtestBiz::AD_DSP, 0.01);
SPDM_ABTEST_DOUBLE(roi_limit_industry_explore_win_rate_model, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_INT64(max_step_industry_explore_win_rate_model, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_DOUBLE(opt_win_rate_limit_industry_explore_win_rate_model, ks::AbtestBiz::AD_DSP, 0.001);
SPDM_ABTEST_DOUBLE(cal_industry_explore_win_rate_model, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_KCONF_BOOL(ad.frontserver2, enableMaxBonusKconfNewField);
// [xiaowentao] 价值分和动态算力样本流走老的超时降档位
SPDM_KCONF_BOOL(ad.frontserver2, enableDetailCpmNewKafkaTimeoutDegrade);
SPDM_KCONF_BOOL(ad.frontserver2, enableDetailCpmTimeoutKafka);
SPDM_KCONF_BOOL(ad.frontserver2, enableDetailCpmUpliftKafka);
SPDM_KCONF_BOOL(ad.frontserver2, enableWinRateModelKafka);
SPDM_KCONF_BOOL(ad.frontserver2, enableMaxBonusAbExp);
SPDM_ABTEST_BOOL(max_bonus_is_exp_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_industry_explore_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(adn_max_industry_explore_ratio_new, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(rtb_max_industry_explore_ratio_new, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_DOUBLE(step_industry_explore_win_rate_model_new, ks::AbtestBiz::AD_DSP, 0.01);
SPDM_ABTEST_DOUBLE(roi_limit_industry_explore_win_rate_model_new, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_INT64(max_step_industry_explore_win_rate_model_new, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_DOUBLE(opt_win_rate_limit_industry_explore_win_rate_model_new, ks::AbtestBiz::AD_DSP, 0.001);
SPDM_ABTEST_DOUBLE(cal_industry_explore_win_rate_model_new, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_price_auction_bid_precise, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_admit_by_uplift, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_admit_by_uplift_lowest, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_admit_by_uplift_assign_level, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dcaf_timeout_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(price_auction_bid_precise_ratio, ks::AbtestBiz::AD_DSP, 0);
}  // namespace front_server
}  // namespace ks
