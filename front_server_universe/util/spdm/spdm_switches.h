#pragma once

#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace front_server {

// SPDM 使用文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIDGfeGPH2sQAxiBwuiaJ4

// 声明 kconf 开关:  DECLARE_SPDM_KCONF_BOOL(key);  // [owner] 开关用途/作用
// 声明 abtest 开关: DECLARE_SPDM_ABTEST_BOOL(key);  // [owner] 开关用途/作用

// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.

// 以下为 kconf 开关声明.
DECLARE_SPDM_KCONF_BOOL(enablePlay3sToImpression);     // [zhangyunhao03] 播放 3s 改为曝光上报
DECLARE_SPDM_KCONF_BOOL(universePreviewStyleCheck);     // [wanglei10] 联盟白盒预览增加正排数据校验
DECLARE_SPDM_KCONF_BOOL(universePreviewDisableOriginLive);     // [wanglei10] 联盟白盒预览支持原生直播
DECLARE_SPDM_KCONF_BOOL(enableFixPriceLow);  // [xiaoyan] bidding 出价计费类型修复
DECLARE_SPDM_KCONF_BOOL(disableCacheLiveAd);  // [duxiaomeng] 客户端不缓存直播广告和粉条硬广
DECLARE_SPDM_KCONF_BOOL(enableEspBudgetPartition);  // [liming11] 磁力金牛是否使用独立的预算隔离
DECLARE_SPDM_KCONF_BOOL(enableOnlyEspBudgetPartition);  // [liming11] 磁力金牛是否只使用独立的预算隔离
DECLARE_SPDM_KCONF_BOOL(enableRemoveOnlineJoinPriceRecord);   // [wangjiabin05] onlinejoin 不下发 pricerecord
DECLARE_SPDM_KCONF_BOOL(enableUserContextAllStation);        // [wangjiabin05] 未胜出 ack 信息是否填充用户信息
DECLARE_SPDM_KCONF_BOOL(enableUniversePreviewStyleByKconf);  // [wanglei10] 联盟白盒预览优先出 kconf 配置模板
DECLARE_SPDM_KCONF_BOOL(enableSimplifyDiscountPrice);  // [heqian]
DECLARE_SPDM_KCONF_BOOL(disableAllPerf);  // [heqian] perf 总开关
DECLARE_SPDM_KCONF_BOOL(enableAddRelType);  // [zhangyunhao03] 添加多素材追投字段
DECLARE_SPDM_KCONF_BOOL(enablePrintAllFactorInfo);  // [wangjiabin05] 是否打印所有因子信息
DECLARE_SPDM_KCONF_BOOL(enablePbEmptyValueCheck);   // [liuyibo05] pb 空值检查
DECLARE_SPDM_KCONF_BOOL(enableUniverseRegionAdcodeOpt);  // [liuyibo05] 地理位置报表回显逻辑优化
DECLARE_SPDM_KCONF_BOOL(enableUseDebugPageId);  // [baizongyao] debug_key 统一使用 page_id 前缀
DECLARE_SPDM_KCONF_BOOL(enableAllScene);
DECLARE_SPDM_KCONF_BOOL(enableFixRtbBouns);   // [xiaoyan] 修复 rtb 补贴的下发字段
DECLARE_SPDM_KCONF_BOOL(enableUniverseRtbCpmBouns);   // [pengzhengbo03] 基于目标 ecpm 的 rtb 补贴开关
DECLARE_SPDM_KCONF_BOOL(enableNewInnerFanstopStid);  // [chenqi07] 新内粉的 stid 填充逻辑与老内粉保持一致
DECLARE_SPDM_KCONF_BOOL(universeEspMobileSoruceTypeFix);  // [wanglei10] 联盟磁力金牛移动端 source_type 下发
DECLARE_SPDM_KCONF_BOOL(enableUniverseTinyLogQueryType)  // [jiangyifan] 厂商搜索新增搜索词匹配度，搜索词字段
DECLARE_SPDM_KCONF_BOOL(enableUniverseUseCommonPidControl)  // [jiangyifan] 算力通用化日志开关 for pid 调控
DECLARE_SPDM_KCONF_BOOL(enableUniversePassNewFreqMap)  // [jiangyifan] 透传新频控信息
DECLARE_SPDM_KCONF_BOOL(enableUniverseFreqGlobalSync)  // [jiangyifan] 使用分布式锁解频控并发问题开关
DECLARE_SPDM_KCONF_BOOL(enableUniverseFixUniverseElasticInfo)  // [jiangyifan] 透传弹性分组档位修复  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_univerese_front_adx_filter)  // [jiangyifan] 应用直投去掉过滤
DECLARE_SPDM_ABTEST_BOOL(enable_universe_pid_control)  // [jiangyifan] pid 调控开关
DECLARE_SPDM_KCONF_BOOL(enableCheckBudgetDeliveryType)  // [jiangyifan] 联盟适配全站锁预算开关
DECLARE_SPDM_KCONF_BOOL(enableSplashAdxPackOnlineJoinParams);  // [zhangzhicong]开屏 adx 填充 OnlineJoinParams
DECLARE_SPDM_KCONF_BOOL(universePreviewServerShowRecordFix);  // [wanglei10] 联盟预览落 servershow 修复
DECLARE_SPDM_KCONF_BOOL(enableUniverseQcpxPerf);  // [yangzhanwu] 联盟 qcpx 打点开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_store_search_skip_dark);     // [liuyibo05] 联盟小系统商店搜索流量跳过暗投控比 NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_store_distribut_skip_dark);     // [liuyibo05] 联盟商店分发流量跳过暗投控比 NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_distribut_flow_skip_search_ad_dark);     // [liuyibo05] 支持搜索预算联盟商店分发流量跳过暗投控比 NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_reservation_ad_opt);     // [liuyibo05] 支持直播、短视频直播预约流量准入逻辑优化   NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_mini_app_ad_in_front);     // [liuyibo05] 支持快小游广告流量准入逻辑优化
DECLARE_SPDM_KCONF_BOOL(enableSearchTraceHotQuery);  // [weiyilong] 搜索高商 Query 提高采样率
DECLARE_SPDM_KCONF_BOOL(enableUpdateSearchSampleTag);  // [chudawei] 支持搜索精排样本缺失修复的实验
DECLARE_SPDM_KCONF_BOOL(enableAddProjectFieldsToAdBaseInfo);  // [jiangyuzhen03] 新增 project 维度若干字段到 AdBaseInfo  //NOLINT
DECLARE_SPDM_KCONF_BOOL(enableHarmonyMinimumAppVersion);  // [tanghaihong] 鸿蒙系统 MinimumAppVersion 的返回值  //NOLINT
DECLARE_SPDM_KCONF_BOOL(enableHarmonyClientId);  // [tanghaihong] 鸿蒙流量 ad_log 写 client_id  //NOLINT
DECLARE_SPDM_KCONF_BOOL(enableUniverseCarouselPerfRecord);  // [wanglei10] 聚合轮播打点开关
DECLARE_SPDM_KCONF_BOOL(enableUniverseSampleCachedTag)  // [dengtiancong] 联盟缓存采样标志
DECLARE_SPDM_KCONF_BOOL(universeAppIdAdmitWhiteListReplace)  // [wanglei10] 跳过准入 app 白名单精简
DECLARE_SPDM_KCONF_BOOL(universeContextInitFilterUpdate)  // [wanglei10] 联盟 context_init filter 逻辑更新
DECLARE_SPDM_KCONF_BOOL(universeFlowTypeCheckAdvanced)  // [wanglei10] flow_type 判断前置
DECLARE_SPDM_KCONF_BOOL(usePaidFromUniverseRequest)  // [libaolin03] 使用 universe_request 中的 paid 1.5 字段
// ------------------------------ kconf int32 参数定义 ---------------------------------
DECLARE_SPDM_KCONF_INT32(requestDebugInfoRatio);  // [baizongyao] debug 采样比率
DECLARE_SPDM_KCONF_INT32(allStationDataFlowRate);  // [wangjiabin05] ack 全站特征
DECLARE_SPDM_KCONF_INT32(maxUniverseClickRewardCntOneDay);  // [zhangyunhao03] 联盟点击激励广告每日频控
DECLARE_SPDM_KCONF_INT32(maxUniverseClickRewardCntOneHour);  // [zhangyunhao03] 联盟点击激励广告每小时频控
DECLARE_SPDM_KCONF_BOOL(enableLogAdStid);  // [baizongyao] stid 标识
DECLARE_SPDM_KCONF_INT32(bidGimbalTailNumber);  // [zhaoziyou03] bid_gimbal 生效尾号
DECLARE_SPDM_KCONF_INT32(universePidControlRandomExploreRate);  // [jiangyifan05] pid 调控探索流量百分比大小
// ------------------------------ kconf double 参数定义 ---------------------------------
// ------------------------------ kconf string 参数定义 ---------------------------------
DECLARE_SPDM_KCONF_BOOL(enableMoveBuildAdResponseToFrontDiffDefault);  // [jiangyuzhen03] rank 上移一阶段：构造 ad_response 上移双跑(default)； // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableMoveBuildAdResponseToFrontDiffSearch);  // [jiangyuzhen03] rank 上移一阶段：构造 ad_response 上移双跑(search)； // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableMoveBuildAdResponseToFrontDiffSplash);  // [jiangyuzhen03] rank 上移一阶段：构造 ad_response 上移双跑(splash)； // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_dark_c_skip_dark_control);  // [liuyibo05] 暗投 C 类创意跳过暗投控比逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_kwai_app_by_product_info);  // 支持补充快手包名
DECLARE_SPDM_ABTEST_INT64(kwai_active_data_ttl_judge);  // [liuyibo05] 快手活跃数据有效期判断
DECLARE_SPDM_ABTEST_BOOL(enable_closure_support_mode);   // [liuyibo05] 联盟支持闭环电商转化链路
DECLARE_SPDM_ABTEST_BOOL(enable_universe_update_user_agent);   // 联盟支持 SDK 流量更新 User-Agent
DECLARE_SPDM_ABTEST_BOOL(enable_fill_timeout_with_post_data_v3);  // [liuyibo05] 后验数据填充 timeout
DECLARE_SPDM_ABTEST_BOOL(enable_fill_timeout_with_post_data_v4);  // [liuyibo05] 后验数据填充 timeout
DECLARE_SPDM_ABTEST_BOOL(enable_universe_get_router_user_info);  // [liuyibo05] 联盟获取 router 用户信息
DECLARE_SPDM_ABTEST_INT64(universe_timeout_level_v2);  // [liuyibo05] 联盟超时数据选择，默认选择 p995
DECLARE_SPDM_ABTEST_INT64(universe_hold_cache_remaining_ms);  // [liuyibo05] 联盟广告缓存剩余时间
DECLARE_SPDM_ABTEST_INT64(universe_pid_solver_tp_ratio);  // [jiangyifan05] 联盟 pid 调控选取的 RT 分位
DECLARE_SPDM_ABTEST_STRING(universe_timeout_exp_tag_not_combo);  // [tanghaihong] 联盟超时实验不进 combo 的实验组标识  // NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_timeout_exp_tag_for_combo);  // [tanghaihong] 联盟超时实验进 combo 的实验组标识  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(api_ad_cache_opt);  // [lichanggang] 联盟异步缓存的流量不走超时约束限制
DECLARE_SPDM_ABTEST_BOOL(enable_universe_residence_adcode_target);  // [wanglei10] 联盟居住地定向
DECLARE_SPDM_ABTEST_BOOL(enable_kxy_subsidy_global_nc);  // [jiangnan07] 快小游 c 补全局首充用户
DECLARE_SPDM_ABTEST_BOOL(enable_android_app_platform_opt);  // [liuyibo05] android app 版本信息优化
DECLARE_SPDM_ABTEST_BOOL(universe_kwai_app_installed_reverse_exp);  // [liuyibo05] app_package 快手应用安装态反向实验   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_cpm_bound_fix);  // [zhangyunhao03] cpm 门槛修复
DECLARE_SPDM_ABTEST_DOUBLE(universe_read_cache_compare_ratio);   // [xiecong03]  读缓存时对比时的比例
DECLARE_SPDM_ABTEST_BOOL(enable_universe_fill_compare_ratio_check);
// [caowenli] 是否开启读缓存时 fill_compare_ratio
DECLARE_SPDM_ABTEST_DOUBLE(universe_read_cache_fill_compare_ratio);
// [caowenli] 读缓存时 fill_compare_ratio
DECLARE_SPDM_ABTEST_BOOL(enable_universe_predict_cpm_adjust_weight);
// [caowenli] 是否开启 predict_cpm 权重调整
DECLARE_SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_threshold);
// [caowenli] 第一层 model_predict_cpm 阈值
DECLARE_SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_fill_rate_threshold);
// [caowenli] 第一层 fill_rate 阈值
DECLARE_SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_threshold_layer2);
// [caowenli] 第二层 model_predict_cpm 阈值
DECLARE_SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_fill_rate_threshold_layer2);
// [caowenli] 第二层 fill_rate 阈值
DECLARE_SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_value);
// [caowenli] 第一层权重
DECLARE_SPDM_ABTEST_DOUBLE(universe_predict_cpm_adjust_weight_value_layer2);  // [caowenli] 第二层权重
DECLARE_SPDM_ABTEST_BOOL(enable_universe_user_scene_freq_v3);  // [xiecong03] 请求频次实验开关
DECLARE_SPDM_ABTEST_DOUBLE(universe_cpm_filter_whole);  // [xiecong03] 大盘 cpm 门槛过滤分级实验
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_high_u_to_high_level);  // [xiecong03] 去除 bidding 流量的门槛
DECLARE_SPDM_ABTEST_BOOL(enable_universe_outer_high_u_to_high_level);  // [xiecong03] 外循环高价值人群走最高档
DECLARE_SPDM_ABTEST_BOOL(enable_rtb_presonalized_upgrade);  // [xiaoyan]  利润补贴打折实验
DECLARE_SPDM_ABTEST_STRING(universe_ranking_cache_read_plugin_types);   // [yinliang]  读缓存准入插件
DECLARE_SPDM_ABTEST_STRING(universe_ranking_cache_write_plugin_types);  // [yinliang]  写缓存准入插件
DECLARE_SPDM_ABTEST_BOOL(enable_universe_rtb_bouns_upgrade);  // [xiaoyan]  rtb 补贴竞胜率升级
DECLARE_SPDM_ABTEST_BOOL(enable_guided_feedback_soft_ad_photo)    // [mengfangyuan] 优质软广下发引导式负反馈
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_stid);  // [yushengkai]
DECLARE_SPDM_ABTEST_BOOL(enable_bidding_cpm_bound_explore);  // [wanglei10] 联盟 bidding 门槛探索开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_interstitial_carousel);  // [wanglei10] 联盟插屏场景样式聚合放量开关
DECLARE_SPDM_ABTEST_DOUBLE(force_reco_rb_thrd);
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_rb_thrd);
DECLARE_SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_same_second_industry);
DECLARE_SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_update);  // [zhaokun03] 短剧强出避让策略升级
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_sort_by_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_relax_timeout_setting);     // [liuyibo05] 联盟超时配置放宽，配置档位摸高实验
DECLARE_SPDM_ABTEST_BOOL(enable_universe_honor_market_replace_url);  // 荣耀直投 x 荣耀手机优先跳荣耀商店
DECLARE_SPDM_ABTEST_BOOL(enable_client_rerank_add_author_id);  // [wuyinhao] 端智能增加下发作者 ID
DECLARE_SPDM_ABTEST_BOOL(enable_client_rerank_add_dup_photo_id);  // [wuyinhao] 端智能增加下发 dup photo id
// [wanglei10] 联盟样式聚合天级别曝光上限
DECLARE_SPDM_ABTEST_INT64(carousel_daily_imp_threshold);
// [wanglei10] 联盟样式聚合小时级别曝光上限
DECLARE_SPDM_ABTEST_INT64(carousel_hourly_imp_threshold);
// [wanglei10] 联盟样式聚合报关间隔
DECLARE_SPDM_ABTEST_INT64(carousel_imp_interval);
// [wanglei10] 联盟样式聚合 下发广告数
DECLARE_SPDM_ABTEST_INT64(universe_carousel_ad_num);
// [wuyinhao] 端智能是否使用 cpm 判断高价值人群
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_is_high_value_use_threshold);
// [wuyinhao] 端智能使用 cpm 还是 rank_benefit 作为门槛 0 表示 rank_benefit ，1 表示 cpm
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_use_cpm_or_rankbenifit);
// [wuyinhao] 端智能是否使用 cpm 或 rank_benefit 的门槛值
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_use_threshold_value);
// [wuyinhao] 端智能是否使用用户标签来表示高价值人群
DECLARE_SPDM_ABTEST_BOOL(enable_client_ai_is_high_value_use_profile);
// [wuyinhao] 端智能使用多少天的高价值人群标签
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_user_days);
// [wuyinhao] 端智能高价值人群标签使用前 n%
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_user_pctiles);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_inner_rpc);
DECLARE_SPDM_ABTEST_BOOL(enable_reco_user_info_trim_for_ps)   // reco_user_info 裁剪 for ps
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_valid_flag);  //  [wangjiabin05] 精排 ps 预取请求考虑软广准入
DECLARE_SPDM_ABTEST_BOOL(disable_app_detail);  //  [wangjiabin05] 禁用 app detail
DECLARE_SPDM_ABTEST_INT64(max_hard_quota);  //  [wangjiabin05] 最大硬广 quota
DECLARE_SPDM_ABTEST_INT64(max_soft_quota);  //  [wangjiabin05] 最大软广 quota
DECLARE_SPDM_ABTEST_BOOL(enable_p2live_show_live_stream_1);  // [wanglei10] 联盟信息流场景 短视频引流转直播流
DECLARE_SPDM_ABTEST_BOOL(enable_p2live_show_live_stream_13);  // [wanglei10] 联盟插屏场景 短视频引流转直播流
DECLARE_SPDM_ABTEST_BOOL(enable_universe_age_expand);   // [liuyibo05] 联盟年龄定向扩展实验
// [zengzhengda] 联盟防爆量策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_rtb_win_rate_loss_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_rtb_win_rate_predict_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_smart_compute_uplift);
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_next_n_ad);
// [wanglei10] 联盟修复应用商店直投相关开关
// [huangwenbin] 联盟内循环
DECLARE_SPDM_ABTEST_BOOL(enable_univ_inner_loop_filter_uplift_front_exit);  // 联盟内循环广告屏蔽实验开关 front 出口阶段  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_agg_scene_algo);  // 联盟内循环聚合页控制开关  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(enable_universe_agg_scene_price_top_num);  // 联盟内循环聚合页计费 TopX  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(enable_universe_agg_scene_cpm_top_num);  // 联盟内循环聚合页报价累加 TopN //  NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_agg_scene_discount_ab_tag);  // 联盟内循环聚合页打折位次与折扣系数获取的 key 值 //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_agg_scene_algo_v2);  // 联盟内循环聚合页二期控制开关  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(enable_universe_agg_scene_price_top_num_v2);  // 联盟内循环聚合页二期计费 TopX  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(enable_universe_agg_scene_cpm_top_num_v2);  // 联盟内循环聚合页二期报价累加 TopN //  NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_agg_scene_discount_ab_tag_v2);  // 联盟内循环聚合页二期打折位次与折扣系数获取的 key 值 //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_pv_allocation);  // 联盟内循环 pv 资源分配扶持 控制开关  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_inner_pv_allocation_ecpm_delta_threshold);  // 联盟内循环 pv 资源分配扶持 ecpm 门槛  //  NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_pv_allocation_ecpm_ratio_upbound);  // 联盟内循环 pv 资源分配扶持 ecpm 系数上界  //  NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_pv_allocation_ecpm_add_ratio);  // 联盟内循环 pv 资源分配扶持 ecpm 系数增量  //  NOLINT
// [huangwenbin] 联盟厂商
DECLARE_SPDM_ABTEST_BOOL(enable_universe_app_fill_explore);  // 联盟厂商填充探索实验开关
DECLARE_SPDM_ABTEST_DOUBLE(universe_app_fill_explore_upper_ratio);  // 联盟厂商填充探索系数上界
// [chendongdong] 样式聚合轮播报价
DECLARE_SPDM_ABTEST_INT64(enable_universe_carousel_scene_cpm_top_num);  // 联盟样式聚合轮播报价 TopX  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(enable_universe_carousel_scene_price_top_num);  // 联盟样式聚合轮播报价累加 TopN //  NOLINT
DECLARE_SPDM_ABTEST_STRING(universe_carousel_scene_discount_ab_tag);  // 联盟样式聚合轮播报价 key 值 //  NOLINT
DECLARE_SPDM_ABTEST_INT64(dac_user_type);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_product_bonus_post_filter);  // [liyilin06] 联盟 iaa 产品补贴过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_pinduoduo_second_price_add_kconf);   // [yangjinhui] 联盟 pdd 伪二价支持配置
DECLARE_SPDM_ABTEST_BOOL(enable_person_mark_physical_posid);  // [zhangyunhao03] 使用算法挖掘的物理 pos
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_dynamic_pos);  // [wangyangrui] 猜你喜欢动态广告位
DECLARE_SPDM_ABTEST_BOOL(enable_click_after_recommend);  // [duantao] 点后推
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_cost_cap_second_bid);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_one_more);  // [zhangxingyu03] 激励直播生效再看一个和换一个
//  [gaowei03] 联盟内循环 cost_cap next_cpm
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_nobid_second_bid)   // [gaowei03] 联盟内循环 nobid next_cpm
DECLARE_SPDM_ABTEST_INT64(cache_remove_live_type);  // [yinliang] 精排缓存删除直播的类型
DECLARE_SPDM_ABTEST_BOOL(enable_universe_use_new_hash_did);  // [yinliang] 联盟缓存 did 升级
DECLARE_SPDM_ABTEST_BOOL(enable_small_app_direct_jump);  // [wanglei10] 小程序广告直跳
// [wuhan12] 联盟内循环高价值人群首位探索人群 tag_id
DECLARE_SPDM_ABTEST_INT64(universe_inner_top_explore_high_u_crowd_tag);
// [wuhan12] 联盟内循环高价值人群首位探索填充策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_high_u_crowd_top_threshold);
// [wuhan12] 联盟内循高价值人群环首位探索填充策略最大顶价系数
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_high_u_crowd_threshold_max_ratio);
// [wuhan12] 联盟内循高价值人群环首位探索填充策略最大顶价增量 cpm
DECLARE_SPDM_ABTEST_INT64(universe_inner_high_u_crowd_threshold_max_delta_cpm);
// [wuhan12] 联盟内循环高价值人群首位探索顶价策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_high_u_crowd_top_cpm_explore);
// [wuhan12] 联盟内循环高价值人群首位探索顶价系数
DECLARE_SPDM_ABTEST_DOUBLE(universe_inner_high_u_crowd_cpm_ratio);
// [wuhan12] 联盟 qcpx front u0 人群准入开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_qcpx_admit_u0_front);
//  [fengyajuan] 联盟二价计费考虑二位 boost 后计价
DECLARE_SPDM_ABTEST_INT64(universe_agg_scene_ad_num);   // [wanglei10] 联盟激励聚合场景返回广告数
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_cpm_bound_explore);  // [wanglei10] 联盟内循环顶价配置
DECLARE_SPDM_ABTEST_BOOL(enable_universe_forced_impression_exp);  // [yangjinhui] 联盟强出逻辑开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_forced_impression_cheap_flow_exp);  // [huoyan03] 联盟低价流量强出逻辑开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_crowd_forced_based_cpm_exp);  // [huoyan03] 联盟人群强出逻辑开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_pay_crowd_adn_bound_forced_exp);  // [huoyan03] 联盟人群强出逻辑开关-主站付费人群  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_pay_crowd_adn_target_forced_exp);  // [huoyan03] 联盟人群强出逻辑开关-主站付费人群  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_active_crowd_adn_target_forced_exp);  // [huoyan03] 联盟人群强出逻辑开关-主站激活人群 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_w_level_crowd_adn_fill);  // [dingjinren] 联盟 w 人群填充开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_llm_u2p_v2);  // [huoyan03] 联盟大模型推理 ecpc  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_llm_u2p_v3);  // [huoyan03] 联盟大模型推理 ecpc  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_llm_u2p_ecpc_raito);  // [huoyan03] 联盟大模型推理 ecpc 参数 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_ecpm_win_explore);  // [xuxuejian]联盟竞败数据探索  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_w_level_crowd_bidding_explore);  // [dingjinren] 联盟 w 人群 bidding 探索开关  // NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_valid_w_level);  // [dingjinren] 联盟 w 人群策略生效人群  // NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_w_level_crowd_adn_max_delta_cpm);  // [dingjinren] 联盟 w 人群填充策略限制 delta_cpm  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_w_level_crowd_adn_max_ratio);  // [dingjinren] 联盟 w 人群填充策略限制 ratio  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(universe_w_level_crowd_explore_ratio);  // [dingjinren] 联盟 w 人群探索策略系数  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_universe_w_level_purchase_ad);  // [dingjinren] 联盟 w 人群策略是否生效付费广告  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inner_nobid_charge_ratio_upper);  // [gaowei03] 联盟内循环 nobid 计费最大比
DECLARE_SPDM_ABTEST_DOUBLE(merchant_video_pay_charge_ratio_upper);  // [gaowei03] 联盟内循环支付计费最大比
DECLARE_SPDM_ABTEST_DOUBLE(inner_cost_cap_charge_ratio_upper);   // [gaowei03] 联盟内循环 cost_cap 计费最大比
DECLARE_SPDM_ABTEST_DOUBLE(inner_live_nobid_charge_ratio_upper);
DECLARE_SPDM_ABTEST_STRING(universe_ecpm_win_explore_rank_key);   // [xuxuejian] 联盟竞败数据顶价高rank人群实验 key  // NOLINT
DECLARE_SPDM_ABTEST_INT64(universe_ecpm_win_explore_type_opt);   // [xuxuejian] 联盟竞败数据顶价类型优化  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(C_bu_min_filter_price);
DECLARE_SPDM_ABTEST_DOUBLE(universe_agg_scene_discount_ratio);  // [wanglei10] 联盟激励聚合场景非首位广告打折系数  // NOLINT
DECLARE_SPDM_ABTEST_INT64(splash_combo_frequency_threshold);  // [zhangyunhao03] 组合样式频控
DECLARE_SPDM_ABTEST_INT64(inspire_shake_frequency_threshold);  // [zhangyunhao03] 激励视频摇动频控
DECLARE_SPDM_ABTEST_INT64(splash_shake_frequency_threshold);  // [zhangyunhao03] 开屏摇动频控
DECLARE_SPDM_ABTEST_INT64(splash_rotate_frequency_threshold);  // [zhangyunhao03] 开屏转动频控
DECLARE_SPDM_ABTEST_INT64(splash_slide_frequency_threshold);  // [zhangyunhao03] 开屏滑动频控
DECLARE_SPDM_ABTEST_INT64(splash_combo2_frequency_threshold);  // [zhangyunhao03] 开屏组合样式 2 频控
DECLARE_SPDM_ABTEST_INT64(fullscreen_combo3_frequency_threshold);  // [shanminghui] 全屏 combo3 频控
DECLARE_SPDM_ABTEST_INT64(inspire_combo3_frequency_threshold);  // [shanminghui] 激励 combo3 频控
DECLARE_SPDM_ABTEST_INT64(interstitial_combo2_frequency_threshold);  // [zhangyunhao03] 插屏组合样式 2 频控
DECLARE_SPDM_ABTEST_INT64(inspire_rotate_frequency_threshold);  // [zhangyunhao03] 激励视频扭动频控
DECLARE_SPDM_ABTEST_INT64(fullscreen_rotate_frequency_threshold);  // [zhangyunhao03] 全屏视频扭动频控
DECLARE_SPDM_ABTEST_BOOL(store_delivery_skip_key_word_filter);  // [yangzhanwu] 商店分发流量跳过 front 联盟过滤关键词   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(store_delivery_skip_vivo_adx_filter);  // [yangzhanwu] 商店分发流量跳过 vivo adx 过滤
DECLARE_SPDM_ABTEST_INT64(universe_feed_rotate_frequency_threshold);  // [yangzhanwu] 联盟信息流扭动频控
DECLARE_SPDM_ABTEST_INT64(universe_feed_rotate_interval_threshold);  // [yangzhanwu] 联盟信息流扭动间隔控制
DECLARE_SPDM_ABTEST_INT64(universe_draw_rotate_frequency_threshold);  // [yangzhanwu] 联盟 draw 流扭动频控
DECLARE_SPDM_ABTEST_INT64(feedShakeCountDaily);  // [yangzhanwu] 联盟信息流摇动频控
DECLARE_SPDM_ABTEST_INT64(feedShakeInterval);  // [yangzhanwu] 联盟信息流摇动间隔控制
DECLARE_SPDM_ABTEST_BOOL(disable_cache_all_live_ad);  //  [songchao] 所有直播广告参与缓存 pk 开关
// // 行业直播红包类计费比
// DECLARE_SPDM_ABTEST_BOOL(enable_industry_live_price_ratio);
// [zhengchaofan] 搜索广告点后推商业得分权重
DECLARE_SPDM_ABTEST_DOUBLE(click_after_reco_commerce_weight);
DECLARE_SPDM_ABTEST_DOUBLE(click_after_reco_rele_threshold);
DECLARE_SPDM_ABTEST_BOOL(enable_bs_dynamix_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(
    user_group_server_show_ratio_lower_bound);  //  [shoulifu03]  分人群 adload 控制兜底曝光系数
DECLARE_SPDM_ABTEST_STRING(ad_wise_user_group_prefix);
DECLARE_SPDM_ABTEST_STRING(merchant_user_group_tag_prefix);
DECLARE_SPDM_ABTEST_STRING(rta_user_score_prefix);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_min_page_size_admit);  // [yesiqi] 发现页内流最小可请求广告 page_size 准入开关//NOLINT
DECLARE_SPDM_ABTEST_INT64(inner_explore_min_page_size_admit);  // [yesiqi] 发现页内流最小可请求广告 page_size
// [jinhui05]
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_charge_action_type_with_fanstop);  // [zhaokun03] 万合粉条广告切曝光计费
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_style_form);   // [xiaoyuhao] 表单激励开关
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant);   // [xiaoyuhao] 货架电商流量拆分
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_mall);   // [xiaoyuhao] 货架电商流量拆分-商城
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_buyer);   // [xiaoyuhao] 货架电商流量拆分-买首
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_guess);   // [xiaoyuhao] 货架电商流量拆分-猜喜
DECLARE_SPDM_ABTEST_BOOL(enable_phone_material_fill);  // [shanminghui] 是否开启厂商素材补齐
DECLARE_SPDM_ABTEST_BOOL(enable_reco_user_info_ip_upgrade_v2);  // [shanminghui] 升级 AdRequest 中 reco_user_info 获取 ip 的逻辑   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_filter_material_by_size);  // [shanminghui] 按照精确尺寸过滤素材
DECLARE_SPDM_ABTEST_BOOL(disable_universe_new_creative_bonus_strategy);  // [shanminghui] 禁用联盟新创意补贴策略 (依赖长 lag p2p)  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_quick_search_skip_budget_control_universe);  // [tanghaihong] 联盟小系统快投广告跳过暗投控比 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_direct_search_skip_budget_control_universe);  // [tanghaihong] 联盟小系统直投广告跳过暗投控比 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(universe_record_quick_search_budget);  // [zhongziwei] 快投接入非厂商 走搜索记收开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_delivered_qpon_info);  // [tanghaihong] 联盟 qcpx 频控 & 一致性 redis
DECLARE_SPDM_KCONF_BOOL(enableNoAdsSendUniverseQcpxDeliveryLogInfo);  // [tanghaihong] 联盟无填充时 qcpx 下发信息落表开关 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_merge_fanstop_session_info);  // [zhangruikang] 曝光频控合并
// [yangxuan06] 短剧 C 补配置
DECLARE_SPDM_ABTEST_STRING(smart_offer_exp_tag);
DECLARE_SPDM_ABTEST_STRING(C_activity);
DECLARE_SPDM_ABTEST_STRING(universe_elastic_control_config_v2);
DECLARE_SPDM_ABTEST_STRING(universe_elastic_control_config_swift);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_lower_bound_pay);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_lower_bound_offer);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_upper_bound_pay);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_upper_bound_offer);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_default_roi);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_min_pay_amount);
DECLARE_SPDM_ABTEST_INT64(smart_offer_lessons_adjust);
DECLARE_SPDM_ABTEST_INT64(smart_offer_coins_adjust);
DECLARE_SPDM_ABTEST_INT64(retention_discount);
DECLARE_SPDM_ABTEST_INT64(retention_subsidy);
DECLARE_SPDM_ABTEST_INT64(pay_threshold);
DECLARE_SPDM_ABTEST_INT64(watch_task_checkpiont);
DECLARE_SPDM_ABTEST_INT64(watch_task_ratio_lower_bound);
DECLARE_SPDM_ABTEST_INT64(watch_task_ratio_upper_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_watch_task_price);
DECLARE_SPDM_ABTEST_BOOL(enable_watch_task);
DECLARE_SPDM_ABTEST_BOOL(Paid_retention);
DECLARE_SPDM_ABTEST_BOOL(enable_second_price_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_early_return_offers);
DECLARE_SPDM_ABTEST_BOOL(C_second_price);
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_live_card);  // [xiaoyuhao] 猜喜请求 live-target
// [rongyu03] gimbal type 修复
DECLARE_SPDM_ABTEST_BOOL(enable_universe_pass_cpm_filter);
// [xiaowentao] 按照实验组来选择是否写入 FillUniverseActiveWLevelInfo
DECLARE_SPDM_ABTEST_BOOL(enable_universe_fill_active_w_level);
DECLARE_SPDM_ABTEST_BOOL(enable_user_req_freq_app_id);
// [xiaowentao] dcaf 结果写入 ComputilityData
DECLARE_SPDM_ABTEST_BOOL(enable_write_universe_dcaf_quota);
// [xiaowentao] 联盟动态分成
DECLARE_SPDM_ABTEST_STRING(universe_dynamic_share_exp_id);
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_share_log);
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_share_log_server_show);
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_share_fix_virtual_bonus);
// 分成系数档位分 cpm 区间
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_share_by_cpm_interval);
// 动态分成系数对动态分成后出价 cpm 低于阈值的不生效
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_share_cpm_clip);
DECLARE_SPDM_ABTEST_DOUBLE(dynamic_share_cpm_clip_thre);
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_share_cpm_clip_v2);
// [xiaowentao] 补贴系统优化，引入竞胜率模型补贴 PID 输出值做调整
DECLARE_SPDM_ABTEST_BOOL(enable_max_bonus_win_rate_roi);
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_share_rtb_coff_fix);
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_share_rtb_coff_fix_v2);
DECLARE_SPDM_KCONF_BOOL(enableUniverseRtbWinRatePredictV2);
DECLARE_SPDM_ABTEST_STRING(universe_dcaf_rank_version_id);
DECLARE_SPDM_ABTEST_STRING(dynamic_quality_score_threshold_group_actuality);
DECLARE_SPDM_ABTEST_STRING(universe_dcaf_prerank_version_id);
DECLARE_SPDM_ABTEST_BOOL(universe_disable_all_antispam);
DECLARE_SPDM_ABTEST_BOOL(universe_disable_freq_antispam);
DECLARE_SPDM_ABTEST_STRING(universe_rtb_win_rate_model_params);
DECLARE_SPDM_ABTEST_STRING(freq_strategy_exp_tag);
DECLARE_SPDM_ABTEST_INT64(program_creative_exp_type);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_adx_cpm_zero_bid);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_new_media_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_new_media_strategy_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_quality_model_default_strategy);
DECLARE_SPDM_KCONF_BOOL(enableFrontFixCmdKey);
// huangwenbin
DECLARE_SPDM_ABTEST_BOOL(enable_universe_inner_u2x_redis_fill);
DECLARE_SPDM_ABTEST_INT64(universe_inner_u2x_redis_u_level);
// [xiaowentao] 价值分样本流超时减档
DECLARE_SPDM_ABTEST_BOOL(enable_universe_detail_cpm_timeout_kafka);
// [xiaowentao] uplift 价值分样本流跳过缓存
DECLARE_SPDM_ABTEST_BOOL(enable_universe_detail_cpm_uplift_kafka);
DECLARE_SPDM_KCONF_BOOL(enableFixAdxCpmFix1Cpm);
// [caowenli] 通信行业探索开关
DECLARE_SPDM_ABTEST_BOOL(enable_universe_industry_explore);
DECLARE_SPDM_ABTEST_INT64(adn_max_industry_explore_ratio);
DECLARE_SPDM_ABTEST_INT64(rtb_max_industry_explore_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(step_industry_explore_win_rate_model);
DECLARE_SPDM_ABTEST_DOUBLE(roi_limit_industry_explore_win_rate_model);
DECLARE_SPDM_ABTEST_INT64(max_step_industry_explore_win_rate_model);
DECLARE_SPDM_ABTEST_DOUBLE(opt_win_rate_limit_industry_explore_win_rate_model);
DECLARE_SPDM_ABTEST_DOUBLE(cal_industry_explore_win_rate_model);
DECLARE_SPDM_ABTEST_BOOL(enable_universe_cache_compare_ratio_check);
DECLARE_SPDM_ABTEST_DOUBLE(universe_read_cache_compare_sdk_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(universe_read_cache_compare_api_ratio);
DECLARE_SPDM_KCONF_BOOL(enableMaxBonusKconfNewField);
DECLARE_SPDM_KCONF_BOOL(enableDetailCpmNewKafkaTimeoutDegrade);
DECLARE_SPDM_KCONF_BOOL(enableDetailCpmTimeoutKafka);
DECLARE_SPDM_KCONF_BOOL(enableDetailCpmUpliftKafka);
DECLARE_SPDM_KCONF_BOOL(enableWinRateModelKafka);
// [caowenli] 通信行业探索开关新版
DECLARE_SPDM_ABTEST_BOOL(enable_universe_industry_explore_new);
DECLARE_SPDM_ABTEST_INT64(adn_max_industry_explore_ratio_new);
DECLARE_SPDM_ABTEST_INT64(rtb_max_industry_explore_ratio_new);
DECLARE_SPDM_ABTEST_DOUBLE(step_industry_explore_win_rate_model_new);
DECLARE_SPDM_ABTEST_DOUBLE(roi_limit_industry_explore_win_rate_model_new);
DECLARE_SPDM_ABTEST_INT64(max_step_industry_explore_win_rate_model_new);
DECLARE_SPDM_ABTEST_DOUBLE(opt_win_rate_limit_industry_explore_win_rate_model_new);
DECLARE_SPDM_ABTEST_DOUBLE(cal_industry_explore_win_rate_model_new);
// [xiaowentao] price 和 auction bid 精度问题修复
DECLARE_SPDM_ABTEST_BOOL(enable_price_auction_bid_precise);
// [xiaowentao] 使用 uplift 模型的输出作为价值分的准入门槛
DECLARE_SPDM_ABTEST_BOOL(enable_admit_by_uplift);
// 补贴金策略开启 AB 实验
DECLARE_SPDM_KCONF_BOOL(enableMaxBonusAbExp);
DECLARE_SPDM_ABTEST_BOOL(max_bonus_is_exp_group);
// 使用最低档位对应的 uplift 价值分作为准入门槛
DECLARE_SPDM_ABTEST_BOOL(enable_admit_by_uplift_lowest);
DECLARE_SPDM_ABTEST_BOOL(enable_admit_by_uplift_assign_level);
DECLARE_SPDM_ABTEST_BOOL(enable_dcaf_timeout_log);  // [xiaowentao] 记录 DCAF 超时降档的数据
// 百分比，10 代表 10%
DECLARE_SPDM_ABTEST_INT64(price_auction_bid_precise_ratio);
}  // namespace front_server
}  // namespace ks
