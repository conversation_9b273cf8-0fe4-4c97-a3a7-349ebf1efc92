#include "teams/ad/front_server_universe/util/ad_style_data/ad_style_mgr.h"

#include <algorithm>
#include <cstdint>
#include <map>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "absl/strings/str_cat.h"
#include "absl/time/clock.h"
#include "base/encoding/base64.h"
#include "base/encoding/url_encode.h"
#include "base/hash_function/md5.h"
#include "base/strings/utf_char_iterator.h"
#include "google/protobuf/util/json_util.h"
#include "google/protobuf/util/message_differencer.h"
#include "ks/base/abtest/abtest_instance.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "serving_base/crypt/aes_crypter.h"
#include "teams/ad/ad_base/src/common/common.h"
#include "teams/ad/ad_base/src/common/multi_retrieval_tag.h"
#include "teams/ad/ad_base/src/crypt/ad_aes_crypter.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/math/random/random_shuffle.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/ad_base/src/redis/redis_util.h"
#include "teams/ad/ad_base/src/util/check_reco_request_type.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_forward_index.kess.grpc.pb.h"
#include "teams/ad/ad_query_parser_server/src/util/utf8.h"
#include "teams/ad/engine_base/cache_loader/union_playable_product_post_data_p2p_cache.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/engine_base/p2p_cache_loader/style/app_download_info.h"
#include "teams/ad/engine_base/utils/utils.h"
#include "teams/ad/front_server_universe/engine/context_data/ad_common.h"
#include "teams/ad/front_server_universe/engine/strategy/style_postproc/dynamic_creative_strategy.h"
#include "teams/ad/front_server_universe/engine/utils/ad_pack/base_info_pack/common_base_info_pack.h"
#include "teams/ad/front_server_universe/engine/utils/merchant/merchant_logic.h"
#include "teams/ad/front_server_universe/util/color/action_bar_color.h"
#include "teams/ad/front_server_universe/util/kconf/kconf.h"
#include "teams/ad/front_server_universe/util/kconf/kconf_data.pb.h"
#include "teams/ad/front_server_universe/util/merchant_util/merchant_style_util.h"
#include "teams/ad/front_server_universe/util/spdm/spdm_switches.h"
#include "teams/ad/front_server_universe/util/utility/rank_macros.h"
#include "teams/ad/front_server_universe/util/utility/redis_util.h"
#include "teams/ad/engine_base/p2p_cache_loader/industry_playlet_sdpa.h"

using kuaishou::ad::AdActionType_Name;
using kuaishou::ad::AdDataV2_InspireAdInfo_InspireAction_InspireActionType;
using kuaishou::ad::AdEnum;

namespace ks {
namespace front_server {

const std::string kReplaceActionbarColorMacro = "##{#ACTIONBAR_BG_COLOR}";  // NOLINT
const std::string kClickAfterWordsTitle = "你可能感兴趣";    // NOLINT
enum {
  kNotAllowComment = 1u,           // 关闭评论
  kNotAllowAttention = 1u << 1,    // 关闭关注
  kNotAllowOpenProfile = 1u << 2,  // 关闭打开 profile 页
  kNotAllowForword = 1u << 3,      // 关闭转化
  kNotAllowLike = 1u << 4,         // 关闭点赞
  kClosePlayed = 1u << 5           // 关闭播放数
};

static const std::map<std::string, int> hex_str_map{
    {"0", 0},  {"1", 1},  {"2", 2},  {"3", 3},  {"4", 4},  {"5", 5},  {"6", 6},  {"7", 7},
    {"8", 8},  {"9", 9},  {"a", 10}, {"b", 11}, {"c", 12}, {"d", 13}, {"e", 14}, {"f", 15},
    {"A", 10}, {"B", 11}, {"C", 12}, {"D", 13}, {"E", 14}, {"F", 15}};

AdStyleMgr::AdStyleMgr()
    : session_data_(nullptr), ad_result_(nullptr), forward_index_item_(nullptr),
      ad_request_(nullptr), actionbar_style_(1), animation_type_(2), actionbar_color_(), display_info_(),
      aes_crypter_(),
      is_multi_screen_info_(false), ad_data_v2(), h5_data_json_(base::StringToJson("{}")) {}

bool AdStyleMgr::Initialize(
    ContextData* context_data,
    AdResult* ad_result,
    StyleInfoItem* style_item,
    AdStyleMgrParams* params) {
  ad_data_v2.Clear();

  if (context_data == nullptr || ad_result == nullptr || style_item == nullptr ||
      context_data->get_ad_request() == nullptr) {
    LOG_EVERY_N(INFO, 100000) << (ad_result == nullptr) << (context_data == nullptr)
                              << (context_data->get_ad_request() == nullptr) << (style_item == nullptr);
    return false;
  }

  session_data_ = context_data;
  ad_result_ = ad_result;
  forward_index_item_ = style_item;
  ad_request_ = session_data_->mutable_ad_request();
  actionbar_style_ = 1;
  animation_type_ = 2;
  actionbar_color_.clear();
  expose_tag_.clear();
  display_info_.clear();
  is_multi_screen_info_ = false;
  // base::Json 这货没有 clear 之类的函数做清空.
  h5_data_json_.~Json();
  new (&h5_data_json_) base::Json(base::StringToJson("{}"));
  params_ = params;

  return true;
}

void AdStyleMgr::SetAdDataV2Later(AdResult* item, ContextData* session_data_) {
  if (session_data_ == nullptr || item == nullptr) {
    return;
  }
  std::string ad_data_v2_json_str;
  ::google::protobuf::util::MessageToJsonString(ad_data_v2, &ad_data_v2_json_str);
  item->mutable_ad_deliver_info()->mutable_ad_base_info()->set_ad_data_v2(ad_data_v2_json_str);
  item->mutable_ad_deliver_info()->mutable_ad_base_info()->set_display_ecpm(ad_data_v2.display_ecpm());
}

void AdStyleMgr::SetUrlReplace(AdResult* item, const std::map<std::string, std::string> &replace_macro_map) {
  ReplaceUrlMacro(ad_data_v2.mutable_h5_url(), replace_macro_map);
  session_data_->dot_perf->Count(1, "ad_data_v2_replace_h5_url", "replaced");
}

bool AdStyleMgr::MinimumAppVersion(const std::string& ios_version, const std::string& android_version) const {
  std::string app_version = ad_request_->reco_user_info().app_version();
  if (ad_request_->ad_user_info().platform() == "ios") {
    return engine_base::CompareAppVersion(app_version, ios_version) >= 0;
  } else if (ad_request_->ad_user_info().platform() == "harmony") {
    // [tanghaihong] 鸿蒙系统暂时和安卓保持一致
    if (SPDM_enableHarmonyMinimumAppVersion()) {
      return engine_base::CompareAppVersion(app_version, android_version) >= 0;
    } else {
      return false;
    }
  }
  return engine_base::CompareAppVersion(app_version, android_version) >= 0;
}

bool AdStyleMgr::IsDspDownload() const {
  return forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::APP;
}

bool AdStyleMgr::IsAdvanceDownload() const {
  return forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::APP_ADVANCE &&
     forward_index_item_->has_app_release() && forward_index_item_->app_release().app_id() != 0 &&
     forward_index_item_->unit().app_download_type() == 0;
}

bool AdStyleMgr::IsAdxDownload() const {
  return ad_result_->ad_source_type() == kuaishou::ad::ADX &&
         ad_result_->ad_deliver_info().ad_base_info().campaign_type() == kuaishou::ad::AdEnum::APP;
}

bool AdStyleMgr::Prepare() {
  if (!PrepareDefaultDisplayInfo()) {
    return false;
  }
  if (!PrepareDefaultActionbarColor()) {
    return false;
  }
  PrepareExposeTag();
  return true;
}

bool AdStyleMgr::PrepareDefaultDisplayInfo() {
  const auto convert_to_display_info = [](const std::string& config) {
    base::Json display_config(base::StringToJson(config));
    if (display_config.IsObject()) {
      return display_config.GetString("actionBar", "");
    }
    return std::string();
  };

  display_info_ = convert_to_display_info(ad_result_->ad_deliver_info().ad_base_info().display_info());
  return true;
}

bool AdStyleMgr::PrepareDefaultActionbarColor() {
  // 增加保底颜色，以避免在客户端因没有 actionbarBgColor 不展示广告
  actionbar_color_ = "FF5000";
  auto ad_result = ad_result_;

  std::string color;
  auto& photo_status = forward_index_item_->photo_status();
  if (IsDspDownload()) {
    // 老的七选一逻辑
    if (photo_status.main_color().size() == 6) {
      if (action_bar::GetNearestColorBySpaceDist(photo_status.main_color(), &color)) {
        actionbar_color_ = color;
      }
    }
  }

  const auto& top5_color_vec = photo_status.parse_field().top5_color();
  actionbar_color_ = action_bar::DefaultColor(IsDspDownload());
  for (auto& item : top5_color_vec) {
    bool succ = action_bar::ProcessExp(item.r(), item.g(), item.b(), item.ratio(), IsDspDownload(), &color);
    if (succ) {
      actionbar_color_ = color;
      break;
    }
  }
  return true;
}

bool AdStyleMgr::PrepareExposeTag() {
  expose_tag_ = "";
  for (const auto& tag :
       forward_index_item_->creative().extend_fields().extra_display_info().new_expose_tag()) {
    if (!expose_tag_.empty()) {
      absl::StrAppend(&expose_tag_, " ", tag.text());
    } else {
      absl::StrAppend(&expose_tag_, tag.text());
    }
  }
  if (expose_tag_.empty()) {
    expose_tag_ = forward_index_item_->creative().extend_fields().extra_display_info().expose_tag();
  }
  return true;
}

void AdStyleMgr::Process(
    ContextData* context_data,
    AdResult* ad_result,
    StyleInfoItem* style_item,
    AdStyleMgrParams* params) {
  if (!Initialize(context_data, ad_result, style_item, params)) {
    return;
  }

  if (!Prepare()) {
    VLOG(2) << "[fanstop]adstyle prepare failed for creative_id:"
            << ad_result_->ad_deliver_info().ad_base_info().creative_id();
    return;
  }

  actionbar_style_ = SetActionbarStyle();
  SetLandingPageInfo();
  SetFreqControlClientFlag();
  SetCommentActionbarInfo();
  SetPlayInfo();
  SetCoverStickerInfo();
  if (ad_result_->ad_source_type() == kuaishou::ad::ADX) {
    SetAdPageButtonControl();
  } else {
    SetDspAdPageButtonControl();
  }
  SetAdCoverPageButtonControl();
  SetUniverseAppDetailInfo();
  SetIosAppId();
  SetConvertInfo();
  SetCoverActionBarInfo();
  if (ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())) == "kuaishou_nebula") {
    SetAppDetailInfo();
  }

  SetAdtestForAdForms();
  SetNegativeMenuInfo();
  SetMarketUri();
  SetTrolleyView();
  SetAdLabelOptimization();
  SetInsureCardInfo();
  if (ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request())) == "kuaishou_nebula") {
    ad_data_v2.set_enable_app_download_pause(true);
    ad_data_v2.set_ad_after_caption(1);
  }
  SetPhotoPlayedReportTime();
  SetLiveComponent();
  SetCommonControlInfo();

  if (IsDspDownload()) {
    SetH5DataV2();
  } else {
    SetH5Data();
  }
  SetBridgeInfo();
  SetOriginStyleInfo();
  ResetActionbarTime();
  SetClickUrl();
  SetMerchantProductInfo();
  SetPosInfo();
  SetInsVideoLiveInfo();
  SetUniverseInteractiveStyle();
  SetOriginStyleInfoUniverse();
  SetUniversePreferStyle();
  SetAdxCardInfo();
  SetAdFreqInfo();
  SetAdxAppInfo();
  SetWechatDirectCall();
  SetRtaMacros();
  SetAdSmartOffersInfo();
  SetKxySubsidyInfo();
  ad_data_v2.set_enable_ska(forward_index_item_->unit().unit_support_info().use_ska());
  if (session_data_->get_pos_manager().IsRecruitSimilarPositionPage()) {
    SetRecruitSimilarPositionInfo();
  }
  SetBizInfo();
  SetPhotoSource();
}

void AdStyleMgr::SetFreqControlClientFlag() {
  // 除内粉外都设置为 true
  // 需求 doc: https://docs.corp.kuaishou.com/d/home/<USER>
  if (!ad_result_->ad_deliver_info().ad_base_info().fanstop_ext_info().is_fanstop_inner_operation() &&
      !ad_result_->ad_deliver_info().ad_base_info().fanstop_ext_info().is_new_fanstop_inner()) {
    ad_data_v2.set_is_negetive_filter(true);
  }
}

void AdStyleMgr::ProcessUniverseFix(AdResult* ad_result) {
  if (ad_result == nullptr) {
    return;
  }
  auto* origin_style_info = ad_data_v2.mutable_origin_style_info();
  origin_style_info->mutable_universe_info()->set_type(
      ad_result_->ad_deliver_info().universe_ad_deliver_info().type());
  origin_style_info->mutable_universe_info()->set_sub_type(
      ad_result_->ad_deliver_info().universe_ad_deliver_info().sub_type());
}

void AdStyleMgr::SetUniversePreferStyle() {
  auto* origin_style_info = ad_data_v2.mutable_origin_style_info();

  if (FrontKconfUtil::enableStylePreferTest()) {
    auto test_conf = FrontKconfUtil::stylePreferTestConf();
    for (auto iter = test_conf->begin(); iter != test_conf->end(); iter++) {
      int64_t type = iter->first;
      int64_t material_id = iter->second;
      auto* new_style = origin_style_info->mutable_universe_info()->add_ad_style_prefers();
      new_style->set_type(type);
      new_style->set_material_id(material_id);
    }
    return;
  }

  if (ad_result_->ad_deliver_info().ad_base_info().style_info_size() > 0) {
    const auto& style_info = ad_result_->ad_deliver_info().ad_base_info().style_info(0);
    bool style_component_flag = ks::front_server::UniverseData::GetStyleComponentFlag(session_data_);
    // playcard
    if (style_info.play_card_id64() > 0) {
      auto* new_style = origin_style_info->mutable_universe_info()->add_ad_style_prefers();
      new_style->set_type(style_component_flag ? 10002 : 2);
      new_style->set_material_id(style_info.play_card_id64());
    } else if (style_info.play_card_id() > 0) {
      auto* new_style = origin_style_info->mutable_universe_info()->add_ad_style_prefers();
      new_style->set_type(style_component_flag ? 10002 : 2);
      new_style->set_material_id(style_info.play_card_id());
    }

    // 回流页样式
    auto reflow_iter = style_info.extend_style().find(kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_REFLOW_STYLE);
    if (reflow_iter != style_info.extend_style().end() && reflow_iter->second.id() > 0) {
      auto* new_style = origin_style_info->mutable_universe_info()->add_ad_style_prefers();
      new_style->set_type(4);
      new_style->set_material_id(reflow_iter->second.id());
    }

    // 组件化样式 - endcard
    auto component_iter =
        style_info.extend_style().find(kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_COMPONENT_STYLE);
    auto component_hori_iter =
        style_info.extend_style().find(kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_COMPONENT_STYLE_HORI);
    if (component_iter != style_info.extend_style().end() && component_iter->second.id() > 0) {
      auto* new_style = origin_style_info->mutable_universe_info()->add_ad_style_prefers();
      new_style->set_type(100);
      new_style->set_material_id(component_iter->second.id());
    } else if (component_hori_iter != style_info.extend_style().end() &&
               component_hori_iter->second.id() > 0) {
      auto* new_style = origin_style_info->mutable_universe_info()->add_ad_style_prefers();
      new_style->set_type(103);
      new_style->set_material_id(component_hori_iter->second.id());
    } else if (style_info.end_card_id64() > 0) {
      auto* new_style = origin_style_info->mutable_universe_info()->add_ad_style_prefers();
      new_style->set_type(style_component_flag ? 10003 : 3);
      new_style->set_material_id(style_info.end_card_id64());
    } else if (style_info.end_card_id() > 0) {
      auto* new_style = origin_style_info->mutable_universe_info()->add_ad_style_prefers();
      new_style->set_type(style_component_flag ? 10003 : 3);
      new_style->set_material_id(style_info.end_card_id());
    }
  }
}

void AdStyleMgr::SetUniverseInteractiveStyle() {
  auto ad_style = session_data_->get_ud_ad_style();
  if (ad_style != 4 && ad_style != 13 && ad_style != 3 && ad_style != 1 && ad_style != 2 && ad_style != 23 &&
       ad_style != 6) {
    // 非开屏/插屏流量直接返回
    return;
  }

  const std::string& ad_style_str = absl::StrCat(ad_style);
  if (ad_result_->ad_deliver_info().ad_base_info().style_info_size() > 0) {
    const auto& style_info = ad_result_->ad_deliver_info().ad_base_info().style_info(0);

    for (const auto & iter : style_info.extend_style()) {
      if (iter.second.id() <= 0) {
        continue;
      }

      switch (iter.first) {
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_SPLASH_SHAKE_STYLE:
          ad_data_v2.mutable_splash_info()->mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_SHAKE);
          ad_data_v2.mutable_splash_info()
              ->mutable_interaction_info()
              ->mutable_shake_info()
              ->set_component_index(iter.second.id());
          session_data_->dot_perf->Count(1, "universe_interactive_style", "splash_shake",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_SPLASH_ROTATE_STYLE:
          ad_data_v2.mutable_splash_info()->mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_ROTATE);
          ad_data_v2.mutable_splash_info()
              ->mutable_interaction_info()
              ->mutable_rotate_info()
              ->set_component_index(iter.second.id());
          session_data_->dot_perf->Count(1, "universe_interactive_style", "splash_rotate",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_SPLASH_SLIDE_STYLE:
          ad_data_v2.mutable_splash_info()->mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_SLIDE);
          ad_data_v2.mutable_splash_info()
              ->mutable_interaction_info()
              ->mutable_slide_info()
              ->set_component_index(iter.second.id());
          session_data_->dot_perf->Count(1, "universe_interactive_style", "splash_slide",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_SPLASH_ACTION_BAR:
          ad_data_v2.mutable_splash_info()->mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_ACTIONBAR);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "splash_action_bar",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_INTERSTITIAL_SHAKE:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_SHAKE);
          ad_data_v2.mutable_interaction_info()->mutable_shake_info()->set_component_index(iter.second.id());
          session_data_->dot_perf->Count(1, "universe_interactive_style", "interstitial_shake",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_FULLSCREEN_SHAKE:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_SHAKE);
          ad_data_v2.mutable_interaction_info()->mutable_shake_info()->set_component_index(iter.second.id());
          session_data_->dot_perf->Count(1, "universe_interactive_style", "fullscreen_shake",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_EXPLORE_SHAKE: {
          int32_t coop_mode = session_data_->get_pos_manager().GetCooperationMode();
          static absl::flat_hash_set<int32_t> coop_mode_set{1, 4, 6, 8};
          int32_t render_type = 0;
          if (session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() > 0) {
            render_type =
                session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).render_type();
          }
          // sdk 合作方式，信息流 sdk 渲染的流量
          if (coop_mode_set.count(coop_mode) && render_type == 2) {
            ad_data_v2.mutable_interaction_info()->set_interactive_style(
                kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_SHAKE);
            ad_data_v2.mutable_interaction_info()->mutable_shake_info()->set_component_index(
                iter.second.id());
            session_data_->dot_perf->Count(1, "universe_interactive_style", "explore_shake",
                                           absl::StrCat(iter.second.id()), ad_style_str);
          }
          break;
        }
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_SPLASH_COMBO_STYLE:
          ad_data_v2.mutable_splash_info()->mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_COMBO);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "splash_combo",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_INSPIRE_SHAKE_STYLE:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_SHAKE);
          ad_data_v2.mutable_interaction_info()->mutable_shake_info()->set_component_index(iter.second.id());
          session_data_->dot_perf->Count(1, "universe_interactive_style", "inspire_shake",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_SPLASH_COMBO2_STYLE:
          ad_data_v2.mutable_splash_info()->mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_COMBO2);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "splash_combo2",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_INTERSTITIAL_COMBO2_STYLE:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_COMBO2);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "interstitial_combo2",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_INSPIRE_ROTATE:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_ROTATE);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "inspire_rotate",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_FULLSCREEN_ROTATE:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_ROTATE);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "fullscreen_rotate",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_FULLSCREEN_COMBO3:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
            kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_COMBO3);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "fullscreen_combo3",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_INSPIRE_COMBO3:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
            kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_COMBO3);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "inspire_combo3",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_SPLASH_SLIDE_ACTION_BAR:
          ad_data_v2.mutable_splash_info()->mutable_interaction_info()->set_interactive_style(
            kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_SLIDE_ACTION_BAR);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "splash_slide_action_bar",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_FEED_ROTATE_STYLE:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_ROTATE);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "feed_rotate",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_SELF_RENDER_ROTATE:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_ROTATE);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "self_render_rotate",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        case kuaishou::ad::AdEnum::EXTEND_STYLE_TYPE_DRAW_ROTATE_STYLE:
          ad_data_v2.mutable_interaction_info()->set_interactive_style(
              kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_ROTATE);
          session_data_->dot_perf->Count(1, "universe_interactive_style", "draw_rotate",
                                         absl::StrCat(iter.second.id()), ad_style_str);
          break;
        default:
          session_data_->dot_perf->Count(1, "universe_interactive_style", "unknown_style_name",
                                         absl::StrCat(iter.first), ad_style_str);
      }
    }
  }
  if (ad_style == 4 && ad_data_v2.splash_info().interaction_info().interactive_style() == 0) {
    const auto bottom_action_bar =
                    kuaishou::ad::AdDataV2_SplashInfo_InteractionInfo_InteractiveStyle_STATIC_ACTION_BAR;
    ad_data_v2.mutable_splash_info()->mutable_interaction_info()->set_interactive_style(bottom_action_bar);
  }
}

void AdStyleMgr::SetInsVideoLiveInfo() {
  if ((forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE ||
       forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE ||
       forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::AD_POP_RECRUIT_LIVE) &&
      forward_index_item_->has_live_stream_user_info()) {
    auto live_stream_id_encrypted = forward_index_item_->live_stream_user_info().live_stream_id_encrypted();
    ad_data_v2.set_live_stream_id(live_stream_id_encrypted);
    ad_data_v2.set_put_type(ad_result_->ad_deliver_info().ad_base_info().put_type());
  }
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE) {
    ad_data_v2.set_live_stream_id(forward_index_item_->live_stream_user_info().live_stream_id_encrypted());
  }
}

void AdStyleMgr::SetAdFreqInfo() {
  auto industry_id = ad_result_->ad_deliver_info().ad_base_info().industry_id_v3();
  ad_data_v2.mutable_rate_info()->set_second_industry_id(industry_id);
  auto product_name = ad_result_->ad_deliver_info().ad_base_info().product_name();
  int64_t product_hash = base::CityHash64(product_name.data(), product_name.size());
  ad_data_v2.mutable_rate_info()->set_product_hash_id(product_hash);
  // 增加作者 ID
  if (SPDM_enable_client_rerank_add_author_id(session_data_->get_spdm_ctx())) {
    auto author_id = ad_result_->ad_deliver_info().ad_base_info().author_id();
    ad_data_v2.mutable_rate_info()->set_author_id(author_id);
  }
  if (SPDM_enable_client_rerank_add_dup_photo_id(session_data_->get_spdm_ctx())) {
    auto dup_photo_id = ad_result_->ad_deliver_info().ad_base_info().dup_photo_id();
    if (dup_photo_id == 0) {
      dup_photo_id = ad_result_->ad_deliver_info().ad_base_info().photo_id();
    }
    ad_data_v2.mutable_rate_info()->set_dup_photo_id(dup_photo_id);
  }
}

void AdStyleMgr::SetAppDetailInfo() {
  // appscore 为 0-50 5 分一个台阶 转化为 appstar 0-5 0.5 一个台阶
  auto app_star = forward_index_item_->app_release().app_score() / 10.0;
  // 大于 3 分才展示
  if (app_star >= 3.0) {
    ad_data_v2.mutable_app_detail_info()->set_app_star(app_star);
  }
}

void AdStyleMgr::SetActionbarTime(kuaishou::ad::AdDataV2_ActionBarInfo* p_action_bar_info) {
  p_action_bar_info->set_action_bar_load_time(1);
  p_action_bar_info->set_real_show_delay_time(3000);
  p_action_bar_info->set_downloaded_action_bar_load_time(1000);
  if (ad_result_->ad_source_type() == kuaishou::ad::ADX) {
    auto& adx_style_info = ad_result_->ad_deliver_info().ad_base_info().adx_style_info();
    if (adx_style_info.adx_action_bar_load_time() > 0) {
      p_action_bar_info->set_action_bar_load_time(adx_style_info.adx_action_bar_load_time());
    }
    if (adx_style_info.adx_action_bar_hide_proportion() > 0.0) {
      p_action_bar_info->set_action_bar_hide_proportion(adx_style_info.adx_action_bar_hide_proportion());
    }
  }
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE &&
      forward_index_item_->creative().live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) {
    p_action_bar_info->set_action_bar_load_time(5000);
  }
  // 非电商直播推广，视频引流直播
  if (IsNonMerchantLivePromote(*forward_index_item_) &&
      IsNonMerchantPhotoToLiveStream(*forward_index_item_)) {
    p_action_bar_info->set_action_bar_load_time(5000);
  }
}

void AdStyleMgr::SetCoverActionBarInfo() {
  if (ad_result_->ad_source_type() == kuaishou::ad::ADX) {
    ad_data_v2.mutable_cover_action_bar_info()->set_display_info(
        ad_result_->ad_deliver_info().ad_base_info().cover_action_bar_info().display_info());
  }
}

int AdStyleMgr::SetActionbarStyle() {
  auto* p_action_bar_info = ad_data_v2.mutable_action_bar_info();

  SetActionbarTime(p_action_bar_info);
  // 默认为 1
  int actionbar_style = 1;

  p_action_bar_info->set_action_bar_style(actionbar_style);
  p_action_bar_info->set_action_bar_color(actionbar_color_);
  p_action_bar_info->set_display_info(display_info_);
  // 主站双 feed 展示 commentActionBar 时不显示在评论区上方
  p_action_bar_info->set_without_floating_to_comment(true);
  return actionbar_style;
}

void AdStyleMgr::SetLandingPageInfo() {
  if (actionbar_style_ != 1)
    return;
  const auto ad_source_type = ad_result_->ad_source_type();
  auto* p_landing_page_info = ad_data_v2.mutable_landing_page_info();
  auto config = FrontKconfUtil::landingPageVersionControl();
  bool control_min_version = false;
  if (config != nullptr && config->data().enable_control() &&
      !MinimumAppVersion(config->data().ios_min_version(), config->data().android_min_version())) {
    control_min_version = true;
  }
  // 给前置落地页加一个最小版本号控制
  if (!control_min_version) {
    if (ad_result_->ad_deliver_info().ad_base_info().landing_page_style() !=
        kuaishou::ad::AdEnum::UNKNOWN_STYLE) {
      p_landing_page_info->set_landing_page_style(
          static_cast<int32>(ad_result_->ad_deliver_info().ad_base_info().landing_page_style()));
      actionbar_style_ = 5;
    } else {
      // ios 下 boss 详情页的广告都是落地页前置
      if (ad_request_->ad_user_info().platform() == "ios" &&
          forward_index_item_->creative().app_detail_type() !=
              kuaishou::ad::AdEnum::UNKNOWN_APP_DETAIL_TYPE) {
        p_landing_page_info->set_landing_page_style(
            static_cast<int32>(kuaishou::ad::AdEnum::PREPOSE_LANDING_PAGE_ADX_STYLE));
        actionbar_style_ = 5;
      } else {
        auto& unit = forward_index_item_->unit();
        if (unit.video_landing_page()) {
          p_landing_page_info->set_landing_page_style(
              static_cast<int32>(kuaishou::ad::AdEnum::PREPOSE_LANDING_PAGE_ADX_STYLE));
          actionbar_style_ = 5;
        }
      }
    }
  }
  if (actionbar_style_ == 5) {
    ad_data_v2.mutable_action_bar_info()->set_action_bar_style(actionbar_style_);
    // 落地页前置引导增强，配置相应的 actionbar 的展开时机，和显示文案
    p_landing_page_info->set_action_bar_display_info("推荐");
    p_landing_page_info->set_action_bar_show_time(3000);
    p_landing_page_info->set_pop_landing_page_height_pct(30);
  }
  p_landing_page_info->set_comment_tag_visible(true);
  if (ad_result_->ad_source_type() == kuaishou::ad::ADX) {
    p_landing_page_info->set_comment_tag_visible(false);
  }
}

void AdStyleMgr::SetWechatDirectCall() {
  ad_data_v2.set_direct_call_type(forward_index_item_->magic_site_page().direct_call_type());
  ad_data_v2.set_func_id(forward_index_item_->magic_site_page().func_id());
}

void AdStyleMgr::SetPlayInfo() {
  const auto& app_id = ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request()));
  const auto& account = forward_index_item_->account();
  auto* p_play_end_info = ad_data_v2.mutable_play_end_info();
  p_play_end_info->set_play_end_style(0);
  p_play_end_info->set_show_end_option(true);
  // 主版播放结束样式设置
  if (app_id == "kuaishou") {
    std::string ios_ver = "8.1.30";
    std::string android_ver = "8.1.30";
    if (MinimumAppVersion(ios_ver, android_ver)) {
      p_play_end_info->set_play_end_style(1);
    }
  }

  // 直播推广设置播放结束页
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE &&
      forward_index_item_->creative().live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) {
    p_play_end_info->set_show_end_option(true);
  }

  p_play_end_info->set_action_bar_color(actionbar_color_);
  if (p_play_end_info->play_end_style() == 2) {
    p_play_end_info->set_score_bright_star_color(action_bar::GameColorProcess(actionbar_color_, 1, 0.1));
  }
  p_play_end_info->set_tag_color(action_bar::GameColorProcess(actionbar_color_, 1, -0.1));
}

bool AdStyleMgr::UseCommentForDoubleFeed() {
  // abtest 主站双 feed 保持与滑滑模式相同， style 和 location 设置为 2
  // 双 feed 落地页前置不下发 comment_actionbar
  if (MinimumAppVersion("7.6.20", "7.6.20") && (actionbar_style_ != 5)) {
    return true;
  }
  return false;
}
void AdStyleMgr::SetCommentActionbarInfo() {
  // 对于主 app 滑滑模式，style 和 location 都返回 2
  if (UseCommentForDoubleFeed()) {
    auto* p_comment_actionbar_info = ad_data_v2.mutable_comment_action_bar_info();
    p_comment_actionbar_info->set_action_bar_style(2);
    p_comment_actionbar_info->set_action_bar_color(actionbar_color_);
    p_comment_actionbar_info->set_display_info(display_info_);
    p_comment_actionbar_info->set_action_bar_location(2);
    if (ad_result_->ad_source_type() == kuaishou::ad::BRAND) {
      p_comment_actionbar_info->set_comment_action_bar_title(ad_result_->ad_deliver_info()
                                                                 .ad_base_info()
                                                                 .brand_ad_data_v2()
                                                                 .comment_action_bar_info()
                                                                 .comment_action_bar_title());
    }
    return;
  }
  if (!(actionbar_style_ == 2 || actionbar_style_ == 3))
    return;
  auto* p_comment_actionbar_info = ad_data_v2.mutable_comment_action_bar_info();
  p_comment_actionbar_info->set_action_bar_style(actionbar_style_);
  p_comment_actionbar_info->set_action_bar_color(actionbar_color_);
  p_comment_actionbar_info->set_display_info(display_info_);
  p_comment_actionbar_info->set_action_bar_location(animation_type_);
}

void AdStyleMgr::SetCoverStickerInfo() {
  if (forward_index_item_ == nullptr) {
    return;
  }

  DynamicCreativeStrategy dcs;
  auto& creative = forward_index_item_->creative();
  auto title_id = ad_result_->ad_deliver_info().ad_base_info().cover_title_id();
  auto sticker_id = ad_result_->ad_deliver_info().ad_base_info().cover_sticker_style_id();
  // 程序化广告填充
  if (creative.create_source_type() == kuaishou::ad::AdEnum::ADVANCED_PROGRAMMED_CREATIVE) {
    bool find = false;
    for (const auto& iter : creative.extend_fields().delivery_package().sticker_titles()) {
      if (sticker_id > 0 && iter.title_id() == title_id) {
        int32_t sticker_location = 2;
        dcs.FillJson(*(ad_request_), creative.cover_url(), iter.title(), sticker_location, sticker_id,
                     session_data_);
        find = true;
        break;
      }
    }
    if (!find) {
      for (const auto& iter : creative.extend_fields().review_through_cover_slogans_info()) {
        if (sticker_id > 0 && iter.slogan_id() == title_id) {
          int32_t sticker_location = 2;
          dcs.FillJson(*(ad_request_), creative.cover_url(), iter.slogan(), sticker_location, sticker_id,
                       session_data_);
          find = true;
          break;
        }
      }
    }
    if (!find) {
      return;
    }
  } else {
    if (creative.extra_cover_data().empty())
      return;
    auto json_data = ks::StringToJson(creative.extra_cover_data());
    if (!json_data)
      return;
    ks::Json extra_cover_data(json_data);
    dcs.FillJson(extra_cover_data, *(ad_request_), session_data_);
    if (!dcs.is_dynamic_creative_)
      return;
  }
  // TODO(liangming) 这里可以直接 dcs 透出这些变量，没必要从 json 里解析了
  auto* p_cover_sticker_info = ad_data_v2.mutable_cover_sticker_info();
  p_cover_sticker_info->set_bg_url(dcs.cover_value_.get("bgUrl", "").asString());
  std::string cover_sticker_title = dcs.cover_value_.get("stickerTitle", "").asString();
  cover_sticker_title = dcs.SloganReplace(*(ad_request_), session_data_, cover_sticker_title);
  p_cover_sticker_info->set_sticker_title(cover_sticker_title);
  const auto& json_sticker_value = dcs.cover_value_.get("coverSticker", ::Json::Value(""));
  auto* p_sticker_value = p_cover_sticker_info->mutable_cover_sticker();
  p_sticker_value->set_sticker_location(
      json_sticker_value.get("stickerLocation", ::Json::Int64(0)).asInt64());
  p_sticker_value->set_sticker_style(json_sticker_value.get("stickerStyle", ::Json::Int64(0)).asInt64());
}

void AdStyleMgr::SetAdPageButtonControl() {
  const auto ad_source_type = ad_result_->ad_source_type();
  const int64_t account_id = forward_index_item_->account().id();
  // 非 ADX 广告不要走这个逻辑， 因为 ADX 默认就是全部关闭权限，需要打开的 account 单独配置
  auto ad_page_button_control = FrontKconfUtil::adpagebuttoncontrol();
  int32_t adpageButtonControl = 0;
  // 默认允许评论，没有加白不允许评论的 adx account
  if (ad_page_button_control->allow_comment.find(account_id) == ad_page_button_control->allow_comment.end() &&
      ad_source_type == kuaishou::ad::ADX) {
    adpageButtonControl |= kNotAllowComment;
  }
  // 默认允许关注，没有加白不允许关注的 adx account
  if (ad_page_button_control->allow_attention.find(account_id) ==
          ad_page_button_control->allow_attention.end() &&
      ad_source_type == kuaishou::ad::ADX) {
    adpageButtonControl |= kNotAllowAttention;
  }
  // 默认允许进 profile，没有加白不允许进 profile 页 的 adx account
  if (ad_page_button_control->allow_open_profile.find(account_id) ==
          ad_page_button_control->allow_open_profile.end() &&
      ad_source_type == kuaishou::ad::ADX) {
    adpageButtonControl |= kNotAllowOpenProfile;
  }
  // 默认允许转发，没有加白不允许转发的 adx account
  if (ad_page_button_control->allow_forword.find(account_id) == ad_page_button_control->allow_forword.end() &&
      ad_source_type == kuaishou::ad::ADX) {
    adpageButtonControl |= kNotAllowForword;
  }
  // 默认允许点赞，没有加白不允许点赞的 adx account
  if (ad_page_button_control->allow_like.find(account_id) == ad_page_button_control->allow_like.end() &&
      ad_source_type == kuaishou::ad::ADX) {
    adpageButtonControl |= kNotAllowLike;
  }
  // adx 是否需要关闭播放数，只有海量的创意是需要配置不显示播放数
  if (ad_source_type == kuaishou::ad::ADX &&
      ad_result_->ad_deliver_info().ad_base_info().large_amount_creative()) {
    adpageButtonControl |= kClosePlayed;
  }
  if (FrontKconfUtil::commentDisableAccountList()->count(forward_index_item_->account().id()) > 0 &&
      ks::ad_base::GetAdQueueType(ad_result_->ad_deliver_info()) == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
    adpageButtonControl |= kNotAllowComment;
  }
  ad_data_v2.set_adpagebuttoncontrol(adpageButtonControl);
}

void AdStyleMgr::SetUniverseAppDetailInfo() {
  if (session_data_->get_ud_is_universe_tiny_flow()) {
    return;
  }
  std::string url;
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::SITE_PAGE ||
      forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::AD_CID ||
      forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::AD_WX_MINI_APP) {
    url = forward_index_item_->unit().uri();
    if (!url.empty()) {
      ad_data_v2.set_h5_url(url);
    }
  }
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum::APP) {
    url = forward_index_item_->creative().download_page_url();
    if (!url.empty()) {
      ad_data_v2.set_h5_url(url);
    } else {
      if (SetAppDetailInfoV2()) {
        LOG_EVERY_N(INFO, 100000) << "Download_page_url is empty.SetAppDetailInfoV2 success";
      }
    }
  }
}

void AdStyleMgr::SetDspAdPageButtonControl() {
  // NOTE(heqian): notAllowOpenProfile 名称与含义不对等， notAllowOpenProfile 为进入 profile 页白名单
  SetAdPageProfileButtonControl();
  SetAdPageAttentionButtonControl();

  // 如果是 DPA 广告，这里需要单独控制权限
  if (IsDpaAd(&(ad_result_->ad_deliver_info().ad_base_info()))) {
    SetDpaButtonControl();
  }
  if (IsNonMerchantPhotoToLiveStream(*forward_index_item_)) {
    int32_t adpageButtonControl = ad_data_v2.adpagebuttoncontrol();
    adpageButtonControl |= kNotAllowOpenProfile;
    ad_data_v2.set_adpagebuttoncontrol(adpageButtonControl);
  }
  if (forward_index_item_->campaign().type() ==
          kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION) {
    int32_t adpageButtonControl = ad_data_v2.adpagebuttoncontrol();
    if (forward_index_item_->creative().creative_support_info().kol_user_id() !=
        forward_index_item_->creative().creative_support_info().photo_author_id()) {
      adpageButtonControl |= kNotAllowOpenProfile;
      adpageButtonControl |= kNotAllowAttention;
    } else {
      adpageButtonControl &= (~kNotAllowOpenProfile);
      adpageButtonControl &= (~kNotAllowAttention);
    }
    ad_data_v2.set_adpagebuttoncontrol(adpageButtonControl);
  }
  if (FrontKconfUtil::commentDisableAccountList()->count(forward_index_item_->account().id()) > 0 &&
      ks::ad_base::GetAdQueueType(ad_result_->ad_deliver_info()) == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
    int32_t adpageButtonControl = ad_data_v2.adpagebuttoncontrol();
    adpageButtonControl |= kNotAllowComment;
    ad_data_v2.set_adpagebuttoncontrol(adpageButtonControl);
  }
}
void AdStyleMgr::SetDpaButtonControl() {
  int32_t adpageButtonControl = ad_data_v2.adpagebuttoncontrol();
  // 添加权限
  if (FrontKconfUtil::addDpaButtonControlValue() > 0) {
    adpageButtonControl |= FrontKconfUtil::addDpaButtonControlValue();
  }
  // 删除权限
  if (FrontKconfUtil::delDpaButtonControlValue() > 0) {
    adpageButtonControl &= (~(FrontKconfUtil::delDpaButtonControlValue()));
  }
  ad_data_v2.set_adpagebuttoncontrol(adpageButtonControl);
}
void AdStyleMgr::SetAdPageAttentionButtonControl() {
  int32_t adpageButtonControl = ad_data_v2.adpagebuttoncontrol();
  auto not_allow_open_profile_set = FrontKconfUtil::notAllowOpenProfile();
  const int64_t account_id = forward_index_item_->account().id();
  if (not_allow_open_profile_set == nullptr) {
    return;
  }
  if (not_allow_open_profile_set->find(account_id) == not_allow_open_profile_set->end()) {
    adpageButtonControl |= kNotAllowAttention;
  }
  // 小店通短视频 展示关注
  // 磁力金牛移动版硬广短视频和 pc 版同步
  static std::set<AdEnum::CampaignType> fanstop_photo_types{
      AdEnum::AD_FANSTOP_TO_ALL, AdEnum::AD_FANSTOP_TO_FANS, AdEnum::AD_FANSTOP_TO_SHOW};
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
      (ad_result_->ad_deliver_info().ad_base_info().ry_ad_style() ==
           kuaishou::ad::AdBaseInfo::HARD_AD_STYLE &&
       fanstop_photo_types.count(forward_index_item_->campaign().type()))) {
    adpageButtonControl &= (~(kNotAllowAttention));
    auto item_type = merchant_util::GetSmallShopItemType(*forward_index_item_);
    if (item_type == kuaishou::ad::AdEnum_MerchantItemPutType_MERCHANT_LANDING_PAGE) {
      // 金牛转小店跳落地页不展示关注
      adpageButtonControl |= kNotAllowAttention;
    }
  }
  // 直播展示关注，包括电商与非电商直播
  static std::set<AdEnum::CampaignType> fanstop_live_types{
      AdEnum::AD_FANSTOP_LIVE_TO_ALL, AdEnum::AD_FANSTOP_LIVE_TO_FANS, AdEnum::AD_FANSTOP_LIVE_TO_SHOW};
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
      IsNonMerchantLivePromote(*forward_index_item_) ||
      (ad_result_->ad_deliver_info().ad_base_info().ry_ad_style() ==
           kuaishou::ad::AdBaseInfo::HARD_AD_STYLE &&
       fanstop_live_types.count(forward_index_item_->campaign().type()))) {
    adpageButtonControl &= (~(kNotAllowAttention));
  }
  ad_data_v2.set_adpagebuttoncontrol(adpageButtonControl);
}

void AdStyleMgr::SetAdPageProfileButtonControl() {
  int32_t adpageButtonControl = 0;
  auto not_allow_open_profile_set = FrontKconfUtil::notAllowOpenProfile();
  if (not_allow_open_profile_set == nullptr) {
    return;
  }
  const int64_t account_id = forward_index_item_->account().id();
  std::string download_page = "immerse";
  if (ad_request_->reco_request_info().dark_mode()) {
    download_page = "immerse";
  } else {
    download_page = "standard";
  }
  if (!MinimumAppVersion("6.7.1", "6.7.1")) {
    adpageButtonControl &= (~(kNotAllowOpenProfile));
  } else {
    // 新逻辑 版本大于 > 6.7.1 且命中 abtest , 且 命中账号白名单, 且不是 ios 的 滑滑板 上下滑 进 转化页
    if (not_allow_open_profile_set->find(account_id) == not_allow_open_profile_set->end()) {
      adpageButtonControl |= kNotAllowOpenProfile;
      if (ad_request_->ad_user_info().platform() != "ios" && IsDspDownload()) {
        if (IsDspDownload()) {
          std::string url{};
          url = forward_index_item_->creative().download_page_url();
          if (!url.empty()) {
            adpageButtonControl |= kNotAllowOpenProfile;
            ad_data_v2.set_h5_url(url);
          } else if (MinimumAppVersion("9.3.30", "9.3.30")) {
            adpageButtonControl |= kNotAllowOpenProfile;
          } else {
            adpageButtonControl &= (~(kNotAllowOpenProfile));
          }
        } else {
          adpageButtonControl &= (~(kNotAllowOpenProfile));
        }
      }
    }
  }

  // 磁力金牛移动版硬广短视频和 pc 版同步
  static std::set<AdEnum::CampaignType> fanstop_photo_types{
      AdEnum::AD_FANSTOP_TO_ALL, AdEnum::AD_FANSTOP_TO_FANS, AdEnum::AD_FANSTOP_TO_SHOW};
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
      (ad_result_->ad_deliver_info().ad_base_info().ry_ad_style() ==
           kuaishou::ad::AdBaseInfo::HARD_AD_STYLE &&
       fanstop_photo_types.count(forward_index_item_->campaign().type()))) {
    bool enable_to_landingpage = false;
    auto item_type = merchant_util::GetSmallShopItemType(*forward_index_item_);
    if (item_type == kuaishou::ad::AdEnum_MerchantItemPutType_MERCHANT_LANDING_PAGE) {
      // 金牛转小店跳落地页
      enable_to_landingpage = true;
    }
    // 白名单内 unit_id 跳落地页
    if (FrontKconfUtil::headIconLandingPageUnitIDSet()->count(forward_index_item_->unit().id()) > 0) {
      enable_to_landingpage = true;
    }
    if (enable_to_landingpage) {
      adpageButtonControl |= kNotAllowOpenProfile;
    } else {
      adpageButtonControl &= (~(kNotAllowOpenProfile));
    }
  }
  // 所有的直播广告需要进入 profile 页，包括电商直播与非电商直播
  static std::set<AdEnum::CampaignType> fanstop_live_types{
      AdEnum::AD_FANSTOP_LIVE_TO_ALL, AdEnum::AD_FANSTOP_LIVE_TO_FANS, AdEnum::AD_FANSTOP_LIVE_TO_SHOW};
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
      IsNonMerchantLivePromote(*forward_index_item_) ||
      (ad_result_->ad_deliver_info().ad_base_info().ry_ad_style() ==
           kuaishou::ad::AdBaseInfo::HARD_AD_STYLE &&
       fanstop_live_types.count(forward_index_item_->campaign().type()))) {
    adpageButtonControl &= (~(kNotAllowOpenProfile));
  }
  // 关注页 dsp 广告跳转不进入落地页
  if (ad_request_->ad_request_flow_type() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_FOLLOW &&
      ad_result_->ad_source_type() == kuaishou::ad::AdSourceType::DSP) {
    adpageButtonControl &= (~(kNotAllowOpenProfile));
  }
  ad_data_v2.set_adpagebuttoncontrol(adpageButtonControl);
}

void AdStyleMgr::SetAdCoverPageButtonControl() {
  // 封面页权限控制，默认和详情页权限控制一样，目前只有搜索单列使用
  int32_t ad_cover_page_button_control = ad_data_v2.adpagebuttoncontrol();
  ad_data_v2.set_ad_cover_page_button_control(ad_cover_page_button_control);
}

void AdStyleMgr::SetIosAppId() {
  // 先设置成默认值
  ad_data_v2.set_ios_app_id(forward_index_item_->app_release().ios_app_id());
  ad_data_v2.set_ios_page_id(forward_index_item_->app_release().ios_page_id());
}

void AdStyleMgr::SetConvertInfo() {
  int32_t convert_id = forward_index_item_->trace_util().id();
  int32_t convert_type = forward_index_item_->trace_util().type();
  auto* p_convert_info = ad_data_v2.mutable_convert_info();
  p_convert_info->set_convert_id(convert_id);
  p_convert_info->set_convert_type(convert_type);
}

std::string AdStyleMgr::GetValueFromConfigString(const std::string& config, const std::string& name) {
  if (config.empty()) {
    return std::string{};
  }
  base::Json display_config(base::StringToJson(config));
  if (display_config.IsObject()) {
    return display_config.GetString(name, "");
  }
  return std::string{};
}

void AdStyleMgr::SetAdxCardInfo() {
  if (ad_result_ == nullptr || ad_result_->ad_source_type() != kuaishou::ad::ADX) {
    return;
  }

  auto& ad_data_json_str = ad_result_->ad_deliver_info().ad_base_info().ad_data_v2();
  kuaishou::ad::AdDataV2 adx_ad_data_v2;
  if (!::google::protobuf::util::JsonStringToMessage(ad_data_json_str, &adx_ad_data_v2).ok()) {
    return;
  }

  if (!adx_ad_data_v2.has_origin_style_info() ||
      adx_ad_data_v2.origin_style_info().ad_prefer_style().empty()) {
    return;
  }

  auto* origin_style_info = ad_data_v2.mutable_origin_style_info();
  int adx_prefer_style_count = 0;
  for (int i = 0; i != adx_ad_data_v2.origin_style_info().ad_prefer_style_size(); ++i) {
    auto* p_ad_prefer_style = origin_style_info->add_ad_prefer_style();
    p_ad_prefer_style->Swap(adx_ad_data_v2.mutable_origin_style_info()->mutable_ad_prefer_style(i));
    ++adx_prefer_style_count;
  }

  session_data_->dot_perf->Count(adx_prefer_style_count, "adx_prefer_style_count");
}

void AdStyleMgr::SetWebCardCommonInfo(AdStyleMgr::WebCardEnum card_type) {
  auto webcard_config = FrontKconfUtil::webcardInfo()->data;
  if (nullptr == webcard_config) {
    return;
  }

  auto* web_card_info = ad_data_v2.mutable_ad_web_card_info();
  web_card_info->set_card_delay_time(
      forward_index_item_->creative().extend_fields().extra_display_info().card_appear_second() * 1000);

  web_card_info->set_card_type(card_type);
  std::string card_type_str = std::to_string(card_type);
  std::string card_url = webcard_config->GetString(card_type_str);
  web_card_info->set_card_url(card_url);

  kuaishou::ad::AdDataV2_WebCardInfo_CardData card_data;
  auto display_info = card_data.mutable_displayinfo();
  display_info->set_description(
      forward_index_item_->creative().extend_fields().extra_display_info().pre_description());
  display_info->set_action_bar(
      GetValueFromConfigString(forward_index_item_->creative().display_info(), "actionBar"));
  display_info->set_action_bar_bg_color(actionbar_color_);

  std::string card_data_json_str{};
  ::google::protobuf::util::MessageToJsonString(card_data, &card_data_json_str);
  web_card_info->set_card_data(card_data_json_str);

  LOG_EVERY_N(INFO, 1000) << "after fill web card info: " << card_data_json_str;
}

void AdStyleMgr::SetInsureCardInfo() {
  // 分屏广告屏蔽
  if (is_multi_screen_info_) {
    return;
  }

  // 内容联盟 sdk 3.3.17 及以上版本准入
  const auto& universe_ad_request_info = session_data_->get_ad_request()->universe_ad_request_info();
  if (universe_ad_request_info.sdk_type() != 2 ||
      engine_base::CompareAppVersion(universe_ad_request_info.sdk_version(), "3.3.17") < 0) {
    return;
  }

  bool insure_card = false;
  bool component_card = false;  // 二期卡片
  insure_card = forward_index_item_->creative().extend_fields().extra_display_info().financial_front_card();
  const auto& extra_display_info = forward_index_item_->creative().extend_fields().extra_display_info();
  int64_t component_id = forward_index_item_->unit().component_id();
  bool has_component_card = extra_display_info.component_card();
  if (has_component_card == true && component_id != 0) {
    component_card = true;
  }
  if (!insure_card && !component_card) {  // 如果一期卡片和二期卡片都不满足的话
    return;
  }
  auto webcard_config = FrontKconfUtil::webcardInfo()->data;
  if (nullptr == webcard_config) {
    return;
  }

  if (component_card) {
    SetWebCardCommonInfo(INSURE_CARD_V2);
    auto component_card_config = FrontKconfUtil::componentCard()->data;
    if (nullptr == component_card_config) {
      return;
    }
    std::string component_str = std::to_string(INSURE_COMPONENT);
    std::string component_card_url = component_card_config->GetString(component_str);
    base::Json component_info(base::StringToJson("{}"));
    bool res = component_info.set("componentId", std::to_string(component_id));
    res = component_info.set("componentCardUrl", component_card_url);
    std::string component_color;
    action_bar::RevertComponentColor(actionbar_color_, &component_color);
    component_info.set("componentColor", component_color);
    res = h5_data_json_.set("componentInfo", component_info);
  }
}

void AdStyleMgr::SetAdtestForAdForms() {
  ad_data_v2.set_ab_test_for_ad_forms(params_->ab_test_for_ad_forms);
}

void AdStyleMgr::SetNegativeMenuInfo() {
  std::shared_ptr<NegativeMenusInfoPb> negative_menu_info =  FrontKconfUtil::NegativeMenuInfo();
  if (!negative_menu_info) {
    LOG(ERROR) << "parse nebula_webcard_ios_enabledgative_menu_info error!";
    return;
  }
  // 修改 不喜欢 文案
  std::string new_industry_id = forward_index_item_->ad_industry().industry_name();
  std::string app_name{};
  if (IsDspDownload()) {
    app_name = forward_index_item_->app_release().app_name();
  } else {
    app_name = ad_result_->ad_deliver_info().ad_base_info().product_name();
  }
  if (negative_menu_info->negative_menu_info_pb == nullptr) {
    LOG_EVERY_N(INFO, 10000) << "negative menu info kconf is empty!";
    return;
  }
  auto negative_menu_info_pb = *negative_menu_info->negative_menu_info_pb;
  if (!new_industry_id.empty() || !app_name.empty()) {
    for (auto iter = negative_menu_info_pb.mutable_negative_menus()->begin();
         iter != negative_menu_info_pb.mutable_negative_menus()->end(); ++iter) {
      if (!new_industry_id.empty() && iter->id() == 8) {
        iter->set_name(absl::Substitute("$0$1$2", "不喜欢: ", new_industry_id, "广告"));
      }
      if (!app_name.empty() && iter->id() == 7) {
        iter->set_name(absl::Substitute("$0$1", "不喜欢: ", app_name));
      }
    }
  }
  ad_data_v2.mutable_negative_menu_info()->CopyFrom(negative_menu_info_pb);
}

bool AdStyleMgr::MeetAppStoreRequirements() {
  // 手机子品牌 如 华为 huawei vivo 荣耀
  std::vector<std::string> allowed_phone_brands;
  // 子品牌拼接的字符串 huawe -> 华为,荣耀
  std::string allowed_phone_str;
  // 有投放的商店总品牌 比如 vivo,huawei
  std::string app_store;
  app_store = forward_index_item_->unit().app_store();

  // 无限制
  if (app_store.empty())
    return true;

  std::vector<std::string> ad_brands;
  ad_brands = absl::StrSplit(app_store, ",");

  ks::front_server::kconf::OfflineAppStore offline_app_store;
  std::set<std::string> offline_ad_brands;
  std::string offline_app_stores = forward_index_item_->app_release().offline_app_stores();
  if (!offline_app_stores.empty()) {
    // offline_app_stores = [string1, string] 的格式
    std::string json_str = "{\"offline_app_stores\":" + offline_app_stores + "}";
    auto status = ::google::protobuf::util::JsonStringToMessage(json_str, &offline_app_store);
    if (!status.ok()) {
      LOG_EVERY_N(WARNING, 1000) << "offline_app_store parse failed:" << json_str;
      session_data_->dot_perf->Count(1, "offline_app_store_error");
    } else {
      for (int i = 0; i < offline_app_store.offline_app_stores_size(); i++) {
        offline_ad_brands.insert(offline_app_store.offline_app_stores(i));
      }
    }
  }

  // "vivo" : "vivo",
  // "huawei" : "华为,荣耀,honor,huawei",
  const auto brand_2_detail_map = FrontKconfUtil::brand2Detailbrand();
  for (const auto& ad_brand : ad_brands) {
    auto detail_iter = brand_2_detail_map->find(ad_brand);
    if (detail_iter != brand_2_detail_map->end()) {
      if (offline_ad_brands.count(ad_brand) > 0) {
        continue;
      }
      allowed_phone_str += detail_iter->second + ",";
    }
  }

  // reco 新增比较稳定获取 device
  auto mod = absl::AsciiStrToLower(ad_request_->reco_user_info().device_basic_info().mod());

  bool is_side_window = session_data_->get_pos_manager().IsSideWindow();
  // 联盟产品白名单
  bool universe_white_product = false;
  if (forward_index_item_->has_account()) {
    const auto white_product_list = FrontKconfUtil::universeMarketWhiteProductList();
    if (white_product_list && white_product_list->find(forward_index_item_->account().product_name()) !=
                                  white_product_list->end()) {
      universe_white_product = true;
    }
  }
  mod.clear();
  for (const auto& device_info : ad_request_->ad_user_info().device_info()) {
    mod += device_info.device_mod() + ",";
    mod += device_info.readable_mod() + ",";
  }

  // 联盟集群 mod 转小写
  mod = absl::AsciiStrToLower(mod);
  ad_data_v2.add_device_info(mod);

  if (allowed_phone_str.empty())
    return false;

  if (allowed_phone_str.size() > 0) {
    allowed_phone_str.pop_back();
    allowed_phone_str = absl::AsciiStrToLower(allowed_phone_str);
    allowed_phone_brands = absl::StrSplit(allowed_phone_str, ",");
  }
  // deviceinfo 里面寻找对应手机品牌,用 vivo ,荣耀等详细品牌去做查找
  auto phone_match_lamdba = [&allowed_phone_brands](ContextData* context_data, const std::string& device) {
    if (device.empty())
      return false;
    return std::any_of(allowed_phone_brands.cbegin(), allowed_phone_brands.cend(),
                       [&device, context_data](const std::string& phone_brand) {
                         if (phone_brand.empty()) {
                           return false;
                         }
                         if (device.find(phone_brand) != std::string::npos) {
                           context_data->set_ud_hit_phone_brand(phone_brand);
                           return true;
                         }
                         return false;
                       });
  };

  if (phone_match_lamdba(session_data_, mod)) {
    return true;
  }
  return false;
}

void AdStyleMgr::SetMarketUri() {
  if (ad_base::IsXifan(session_data_->get_sub_page_id()) &&
      forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE &&
      ad_request_->ad_user_info().platform() != "ios") {
    // xifan fill ks mini program market_uri
    ad_data_v2.set_market_uri(*FrontKconfUtil::xifanKsMiniProgramMarketUri());
    return;
  }
  if (forward_index_item_->unit().use_app_market()) {
    ad_data_v2.set_use_app_market(true);
    session_data_->dot_perf->Count(1, "use_app_market_num");
  }
  bool apk_use_market = false;
  std::string package_name;
  auto& app_release = forward_index_item_->app_release();
  if (!app_release.sys_package_name().empty()) {
    package_name = app_release.sys_package_name();
  }
  if (package_name.empty() && !app_release.package_name().empty()) {
    package_name = app_release.package_name();
  }
  // 应用直投广告是否下发 market_uri
  bool use_app_market =
      ad_request_->ad_user_info().platform() != "ios" && forward_index_item_->unit().use_app_market() &&
      (IsDspDownload() || IsAdvanceDownload()) && MeetAppStoreRequirements();
  // 非应用直投广告是否下发 market_uri
  int64_t medium_uid = session_data_->get_ad_request()->universe_ad_request_info().medium_uid();
  apk_use_market =
      FrontKconfUtil::mediaFlowApkUseMarketConfigV2()->data().IsHit(medium_uid, package_name);
  if (!package_name.empty() && (use_app_market || apk_use_market)) {
    // 荣耀走下面 uri 替换逻辑
    ad_data_v2.set_market_uri(absl::Substitute("$0$1", "market://details?id=", package_name));
    session_data_->dot_perf->Count(1, "market_uri_filled");
  }

  // adx 支持商店直投，下发厂商唤起链接
  if (ad_request_->ad_user_info().platform() == "android") {
    if (ad_result_->ad_deliver_info().ad_base_info().adx_market_direct()) {
      ad_data_v2.set_market_uri(absl::Substitute(
          "$0$1", "market://details?id=", ad_result_->ad_deliver_info().ad_base_info().package_name()));
    }
  }

  if (ad_request_->ad_user_info().platform() != "ios" && IsAdxDownload()) {
    if (!MeetAppStoreRequirements()) {
      falcon::Inc("front_server.ad_style.adx_fail_meet_app_store.cnt");
      return;
    }
    ad_data_v2.set_market_uri(absl::Substitute(
        "$0$1", "market://details?id=", ad_result_->ad_deliver_info().ad_base_info().package_name()));
  }

  // 直投荣耀商店 x 荣耀手机 x 应用商店没下架 x ((sdk x 版本) || (api x 白名单))跳转荣耀商店 替换原有 url
  bool is_allow_sdk_api  = (SPDM_enable_universe_honor_market_replace_url(session_data_->get_spdm_ctx()) &&
          IsAllowSdkOrApi(session_data_));
  {
    std::string market_uri = ad_data_v2.market_uri();
    if (!market_uri.empty() && session_data_->get_is_honor_device() &&
        (IsHonorAppStore(session_data_, *forward_index_item_) || apk_use_market) &&
      ((engine_base::CompareAppVersion(session_data_->copy_app_version(), "12.1.10") >= 0 &&
      !SPDM_enable_universe_honor_market_replace_url(session_data_->get_spdm_ctx())) ||
       is_allow_sdk_api)) {
      market_uri = base::StringReplace(market_uri, "market://", "honormarket://", false);
      ad_data_v2.set_market_uri(market_uri);
      session_data_->dot_perf->Count(1, "set_universe_honor_replace_market_url");
    }
  }
}

bool AdStyleMgr::SetAppDetailInfoV2() {
  auto redis_pipeline = ks::ad_base::KconfRedis::Instance().GetAdRedisPipelineClient(
      AD_KWAI_USER_RAW_PROFILE, ad_base::DependDataLevel::STRONG_DEPEND);
  if (redis_pipeline == nullptr) {
    LOG_EVERY_N(WARNING, 10000) << "redis_client is nullptr";
    return false;
  }
  if (!IsDspDownload()) {
    return false;
  }
  auto& app_release = forward_index_item_->app_release();
  std::string app_package_name =
      app_release.package_name().empty() ? app_release.sys_package_name() : app_release.package_name();
  const auto& app_black_list = FrontKconfUtil::appPackageDetailInfoBlackList();
  if (app_black_list && app_black_list->find(app_package_name) != app_black_list->end()) {
    session_data_->dot_perf->Count(1, "app_detail_info_status", "hit_black_list", app_package_name);
    return false;
  }
  std::string key_app_info = ad_base::kAppDetailInfoPrefix + app_package_name;
  std::string key_pic_info = ad_base::kAppDetailPicInfoPrefix + app_package_name;
  std::string value_app_info{}, value_pic_info{};

  ks::infra::RedisErrorCode code_app, code_pic;
  static const int32_t kNewRedisTimeOutMs = 5;
  ks::infra::RedisResponse<std::string> value_app_info_resp = redis_pipeline->Get(key_app_info);
  ks::infra::RedisResponse<std::string> value_pic_info_resp = redis_pipeline->ListIndex(key_pic_info, 0);
  code_app = value_app_info_resp.Get(&value_app_info, kNewRedisTimeOutMs);
  code_pic = value_pic_info_resp.Get(&value_pic_info, kNewRedisTimeOutMs);

  if (code_app == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR &&
      code_pic == ks::infra::RedisErrorCode::KS_INF_REDIS_NO_ERROR) {
    base::Json value_pic_info_json(base::StringToJson(value_pic_info));
    base::Json value_app_info_json(base::StringToJson(value_app_info));
    std::string app_name = value_app_info_json.GetString("name", "");
    auto cdnScreenshotUrls = value_app_info_json.Get("cdnScreenshotUrls");
    if (cdnScreenshotUrls && !app_name.empty() && cdnScreenshotUrls->IsArray() &&
        cdnScreenshotUrls->size() > 0) {
      auto* p_app_detail_info = ad_data_v2.mutable_app_detail_info();
      // 过滤重复的 url
      std::unordered_set<std::string> dup_cdn_urls;
      for (int item = 0; item < cdnScreenshotUrls->size(); ++item) {
        std::string cdn_url = cdnScreenshotUrls->GetString(item, "");
        if (!cdn_url.empty() && dup_cdn_urls.find(cdn_url) == dup_cdn_urls.end()) {
          p_app_detail_info->add_cdn_screen_short_urls(cdn_url);
          dup_cdn_urls.insert(cdn_url);
        }
      }
      if (auto category = value_app_info_json.Get("category")) {
        if (category->IsArray() && category->array().size() > 0) {
          p_app_detail_info->set_app_category(category->GetString(0, ""));
        }
      }
      if (value_app_info_json.GetBoolean("is_official", false)) {
        p_app_detail_info->set_official_tag("官方");
      }
      p_app_detail_info->set_app_score(value_app_info_json.GetNumber("score", 0.0));
      p_app_detail_info->set_update_time(std::to_string(value_app_info_json.GetInt("update_time", 0)));
      p_app_detail_info->set_w(value_pic_info_json.GetInt("width", 0));
      p_app_detail_info->set_h(value_pic_info_json.GetInt("height", 0));
      p_app_detail_info->set_app_size(value_app_info_json.GetNumber("size", 0.0));
      p_app_detail_info->set_app_name(app_name);
      p_app_detail_info->set_developer_name(value_app_info_json.GetString("developer_name", ""));
      p_app_detail_info->set_app_version(value_app_info_json.GetString("version", ""));
      p_app_detail_info->set_app_description(value_app_info_json.GetString("description", ""));
      p_app_detail_info->set_download_num(std::to_string(value_app_info_json.GetInt("download_num", 0)));
      p_app_detail_info->set_app_icon_url(forward_index_item_->unit().app_icon_url());

      auto* p_action_bar_info = ad_data_v2.mutable_action_bar_info();
      p_action_bar_info->set_action_bar_style(12);  // 覆盖 action_bar_style
      if (!value_app_info_json.GetBoolean("has_ads", true)) {
        p_action_bar_info->set_action_bar_tag("无广告");
      }
      session_data_->dot_perf->Count(1, "app_detail_info_status", "succ_app", app_package_name);
      session_data_->dot_perf->Count(1, "app_detail_info_status", "succ");
      return true;
    }
  }
  session_data_->dot_perf->Count(1, "app_detail_info_status", "fail", app_package_name);
  return false;
}

void AdStyleMgr::SetAdLabelOptimization() {
  auto& display_info = forward_index_item_->creative().extend_fields().extra_display_info();
  auto* extra_display_info = ad_data_v2.mutable_extra_display_info();
  if (display_info.new_expose_tag_size() > 0) {
    extra_display_info->set_show_style(3);
    for (auto& tag : display_info.new_expose_tag()) {
      auto* expose_tag = extra_display_info->add_expose_tag_info_list();
      expose_tag->set_text(tag.text());
      expose_tag->set_url(tag.url());
      expose_tag->set_can_click(true);
    }
  } else if (!display_info.expose_tag().empty()) {
    extra_display_info->set_show_style(3);
    auto* expose_tag = extra_display_info->add_expose_tag_info_list();
    expose_tag->set_text(display_info.expose_tag());
    expose_tag->set_can_click(true);
  }

  // 综合电商推荐理由，由 style_server 控制
  bool merchant_expose = false;
  kuaishou::ad::MerchantStyleInfo merchant_style;
  merchant_style.Clear();
  auto status = ::google::protobuf::util::JsonStringToMessage(
      ad_result_->ad_deliver_info().ad_base_info().merchant_style_info(), &merchant_style);
  if (status.ok()) {
    for (int i = 0; i < merchant_style.expose_tag_size(); ++i) {
      if (extra_display_info->expose_tag_info_list_size() <= 2 && !merchant_style.expose_tag(i).empty()) {
        auto* expose_tag = extra_display_info->add_expose_tag_info_list();
        expose_tag->set_text(merchant_style.expose_tag(i));
        expose_tag->set_can_click(true);
        merchant_expose = true;
      }
    }
    if (merchant_style.expose_tag_size() > 0 && merchant_expose) {
      extra_display_info->set_show_style(3);
      session_data_->dot_perf->Count(1, "front_server.merchant_style_expose_tag");
    }
  }
  return;
}

void AdStyleMgr::SetTrolleyView() {
  ad_data_v2.set_webview_type(kuaishou::ad::AdEnum_WebViewType_DEFAULT_TYPE);
}

void AdStyleMgr::SetPhotoPlayedReportTime() {
  if (forward_index_item_->campaign().type() != kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
    return;
  }
  auto merchant_photo_style_conf = FrontKconfUtil::merchantPhotoStyleConf();
  if (merchant_photo_style_conf) {
    const auto& time_set = merchant_photo_style_conf->data().played_report_time_set;
    for (const auto& time : time_set) {
      ad_data_v2.add_played_report_time(time);
    }
  }
}

void AdStyleMgr::SetLiveComponent() {
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
      IsNonMerchantLivePromote(*forward_index_item_)) {
    ad_data_v2.set_enable_jump_to_live(true);

    // 设置直播整体时长埋点
    auto live_total_played_ms_set = FrontKconfUtil::liveTotalPlayedmsSet();
    ad_data_v2.clear_played_total_report_time();
    for (int32_t play_ms : *live_total_played_ms_set) {
      ad_data_v2.add_played_total_report_time(play_ms);
    }
    // 小店通直播增加直播时间上报
    auto merchant_live_style_conf = FrontKconfUtil::merchantLiveStyleConf();
    if (merchant_live_style_conf != nullptr) {
      auto& played_report_time_list = merchant_live_style_conf->played_report_time;
      if (!played_report_time_list.empty()) {
        ad_data_v2.clear_played_report_time();
        for (auto time : played_report_time_list) {
          ad_data_v2.add_played_report_time(time);
        }
      }
    }
  }
  if (IsFansTopV2Live()) {
    // 设置直播整体时长埋点
    auto live_total_played_ms_set = FrontKconfUtil::liveTotalPlayedmsSet();
    ad_data_v2.clear_played_total_report_time();
    for (int32_t play_ms : *live_total_played_ms_set) {
      ad_data_v2.add_played_total_report_time(play_ms);
    }
    auto live_creative_type = forward_index_item_->creative().live_creative_type();
    std::shared_ptr<absl::flat_hash_set<int64_t>> config;
    if (live_creative_type == AdEnum::LIVE_STREAM_CREATIVE_TYPE) {
      config = FrontKconfUtil::livePlayedmsSet();
    } else if (live_creative_type == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) {
      config = FrontKconfUtil::assoPlayedmsSet();
    }

    if (config != nullptr) {
      for (auto time : *config) {
        ad_data_v2.add_played_report_time(time);
        if (live_creative_type == AdEnum::PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) {
          ad_result_->mutable_ad_deliver_info()
              ->mutable_ad_base_info()
              ->mutable_fanstop_ext_info()
              ->add_live_played_report_time(time);
        }
      }
    }
  }
}

bool AdStyleMgr::IsFansTopV2Live() const {
  using kuaishou::ad::AdEnum;
  auto campaign_type = forward_index_item_->campaign().type();
  return campaign_type == AdEnum::AD_FANSTOP_LIVE_TO_ALL ||
         campaign_type == AdEnum::AD_FANSTOP_LIVE_TO_SHOW || campaign_type == AdEnum::AD_FANSTOP_LIVE_TO_FANS;
}

void AdStyleMgr::SetCommonControlInfo() {
  if (FrontKconfUtil::disallowDownloadDialogAccountSet()->count(forward_index_item_->account().id()) > 0) {
    ad_data_v2.mutable_h5_control_info()->set_disallow_show_download_dialog(true);
  }
}

bool AdStyleMgr::IsGameAd() {
  auto industry_id_v3 = forward_index_item_->account().industry_id_v3();
  return (industry_id_v3 >= 1222 && industry_id_v3 <= 1232);
}

void AdStyleMgr::FillFakeStyleData(const int64_t style_type, const int64_t resource_type) {
  // fake style data 用于后验数据积累
  const std::string& release_name =
      (forward_index_item_->app_release().real_app_name().empty()
      ? forward_index_item_->app_release().app_name()
      : forward_index_item_->app_release().real_app_name());
  int64_t hash_app_name = base::CityHash64(release_name.c_str(), release_name.length());
  kuaishou::ad::StyleData sd;
  sd.set_style_type(style_type);
  sd.set_resource_type(resource_type);
  sd.set_style_material_id(hash_app_name);
  (*session_data_->mutable_style_data_for_online_join())[forward_index_item_->creative().id()].push_back(sd);
}

void AdStyleMgr::SetH5DataV2() {
  const auto& appid = ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request()));
  const std::string d_json_str = h5_data_json_.ToString();
  if (d_json_str.size() > 0) {
    ad_data_v2.set_h5_data(d_json_str);
  }
  session_data_->dot_perf->Count(1, "download_page_type",
      absl::StrCat(forward_index_item_->creative().download_page_type()));
  if (forward_index_item_->magic_site_page().page_form() == 1
      && forward_index_item_->magic_site_page().wechat_game_deep_link().empty()) {
    ad_data_v2.mutable_h5_control_info()->set_h5_display_type(1);
  }
  if (ad_data_v2.h5_url().empty()) {
    session_data_->dot_perf->Count(1, "h5_empty_v2");
    LOG_EVERY_N(INFO, 1000) << "h5 url empty, cid, " << forward_index_item_->creative().id();
  }

  // 安卓下载类，加一个默认兜底页面; 兜底更换 ab 实验
  if (IsDspDownload() && ad_data_v2.h5_url().empty() &&
      (ad_request_->ad_user_info().platform() == "android" ||
       ad_request_->ad_user_info().platform() == "harmony") &&
      MinimumAppVersion("9.3.30", "9.3.30")) {
    ad_data_v2.set_h5_url(*FrontKconfUtil::kuaishouDefaultH5Url());
    // 应用商店地址为空才填充兜底中间页
    if (ad_data_v2.market_uri().empty()) {
      if (ad_data_v2.h5_control_info().h5_display_type() == 1) {
        FillFakeStyleData(203, 100);
      } else {
        FillFakeStyleData(203, 0);
      }
    }
  }
}

void AdStyleMgr::SetH5Data() {
  const auto& appid = ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request()));
  const std::string& url = forward_index_item_->creative().download_page_url();
  if (IsAdvanceDownload() && !url.empty()) {
    ad_data_v2.set_h5_url(url);
  }
  const std::string d_json_str = h5_data_json_.ToString();
  if (d_json_str.size() > 0) {
    ad_data_v2.set_h5_data(d_json_str);
  }

  // 安卓下载类，加一个默认兜底页面; 兜底更换 ab 实验
  if (IsDspDownload() && ad_data_v2.h5_url().empty() && ad_request_->ad_user_info().platform() == "android" &&
      MinimumAppVersion("9.3.30", "9.3.30")) {
    if (!forward_index_item_->app_release().app_detail_img().empty()) {
      LOG_EVERY_N(INFO, 100) << "app_detail_img: " << forward_index_item_->app_release().app_detail_img();
      ad_data_v2.set_h5_url(params_->kuaishou_default_h5_url);
    } else {
      ad_data_v2.set_h5_url(*FrontKconfUtil::kuaishouDefaultH5Url());
      // 应用商店地址为空才填充兜底中间页
      if (ad_data_v2.market_uri().empty()) {
        if (ad_data_v2.h5_control_info().h5_display_type() == 1) {
          FillFakeStyleData(203, 100);
        } else {
          FillFakeStyleData(203, 0);
        }
      }
    }
  }

  session_data_->dot_perf->Count(1, "download_page_type",
                                 absl::StrCat(forward_index_item_->creative().download_page_type()));

  if (forward_index_item_->magic_site_page().page_form() == 1
      && forward_index_item_->magic_site_page().wechat_game_deep_link().empty()) {
    ad_data_v2.mutable_h5_control_info()->set_h5_display_type(1);
  }
}

void AdStyleMgr::SetBridgeInfo() {
  if (FrontKconfUtil::wechatMiniProgramAccountSet()->count(forward_index_item_->account().id()) > 0) {
    auto* ad_bridge_info = ad_data_v2.mutable_ad_bridge_info();
    ad_bridge_info->mutable_kwai_ad_third()->set_open_wechat_mini_program(true);
  }
}

void AdStyleMgr::SetOriginStyleInfo() {
  if (session_data_->get_ud_is_universe_tiny_flow()) {
    return;
  }
  const auto& app_id = ks::ad_base::GetRequestAppId(*(session_data_->get_ad_request()));
  const auto& account = forward_index_item_->account();
  auto* origin_style_info = ad_data_v2.mutable_origin_style_info();

  const std::string card_item = forward_index_item_->unit_support_info().adv_card_ids();
  base::Json card_item_json(base::StringToJson(card_item));
  if (card_item_json.IsArray()) {
    for (const auto& item : card_item_json.array()) {
      int64_t card_item_id = item->IntValue(-1);
      if (card_item_id > 0) {
        origin_style_info->add_unit_advance_card_item_ids(card_item_id);
      }
    }
  }
  auto &style_data_for_online_join = *session_data_->mutable_style_data_for_online_join();
  for (auto& info : ad_result_->ad_deliver_info().ad_base_info().style_info()) {
    auto matrix_style_material_id = info.matrix_style_material_id();
    if (matrix_style_material_id == 0) {
      continue;
    }
    auto* mutable_prefer_info = origin_style_info->add_prefer_info();
    mutable_prefer_info->set_display_type(2);
    if (info.display_type() != 0) {
      mutable_prefer_info->set_display_type(info.display_type());
    }
    mutable_prefer_info->set_material_id(matrix_style_material_id);
    mutable_prefer_info->set_prefer_type(info.matrix_style_type());
    mutable_prefer_info->set_label_type(info.label_style_type());
    kuaishou::ad::StyleData sd;
    sd.set_matrix_style_material_id(matrix_style_material_id);
    style_data_for_online_join[forward_index_item_->creative().id()].push_back(sd);
  }
  origin_style_info->set_action_bar_bg_color(actionbar_color_);
  std::string actionbar_tag_color;
  action_bar::RevertComponentColor(actionbar_color_, &actionbar_tag_color);
  origin_style_info->set_action_bar_tag_color(actionbar_tag_color);
  SetMerchantOriginStyleInfo(origin_style_info);
  if (FrontKconfUtil::enableSetSurveyStyleOriginInfo()) {
    SetSurveyOriginStyleInfoOpt(origin_style_info);
  }

  std::string action_bar_description =
      GetValueFromConfigString(forward_index_item_->creative().display_info(), "description");
  DynamicCreativeStrategy dcs;
  action_bar_description = dcs.SloganReplace(*(ad_request_), session_data_, action_bar_description);
  origin_style_info->set_action_bar_desc(action_bar_description);
  std::string action_bar_text =
      GetValueFromConfigString(forward_index_item_->creative().display_info(), "actionBar");
  origin_style_info->set_action_bar_text(action_bar_text);
  origin_style_info->set_app_name(forward_index_item_->app_release().app_name());
  origin_style_info->set_app_score(forward_index_item_->app_release().app_score());
  origin_style_info->set_app_icon_url(forward_index_item_->app_release().app_icon_url());
  origin_style_info->set_product_name(forward_index_item_->account().product_name());
  origin_style_info->set_product_icon_url(forward_index_item_->app_release().app_icon_url());
  origin_style_info->set_show_play_end_info(ad_data_v2.play_end_info().show_end_option());
  auto* p_rank_result = GetAdRankResult(session_data_, *ad_result_);
  auto base_info = ad_result_->ad_deliver_info().ad_base_info();
  if (ad_result_->ad_source_type() == kuaishou::ad::AdSourceType::BRAND) {
    origin_style_info->set_action_bar_text(ad_result_->ad_deliver_info()
                                               .ad_base_info()
                                               .brand_ad_data_v2()
                                               .origin_style_info()
                                               .action_bar_text());
    origin_style_info->set_action_bar_desc(ad_result_->ad_deliver_info()
                                               .ad_base_info()
                                               .brand_ad_data_v2()
                                               .origin_style_info()
                                               .action_bar_desc());
    origin_style_info->set_app_name(
        ad_result_->ad_deliver_info().ad_base_info().brand_ad_data_v2().origin_style_info().app_name());
    origin_style_info->set_app_score(
        ad_result_->ad_deliver_info().ad_base_info().brand_ad_data_v2().origin_style_info().app_score());
    origin_style_info->set_app_icon_url(
        ad_result_->ad_deliver_info().ad_base_info().brand_ad_data_v2().origin_style_info().app_icon_url());
    origin_style_info->set_product_name(
        ad_result_->ad_deliver_info().ad_base_info().brand_ad_data_v2().origin_style_info().product_name());
    origin_style_info->set_product_icon_url(ad_result_->ad_deliver_info()
                                                .ad_base_info()
                                                .brand_ad_data_v2()
                                                .origin_style_info()
                                                .product_icon_url());
    origin_style_info->set_show_play_end_info(ad_result_->ad_deliver_info()
                                                  .ad_base_info()
                                                  .brand_ad_data_v2()
                                                  .origin_style_info()
                                                  .show_play_end_info());
    if (ad_result_->ad_deliver_info()
            .ad_base_info()
            .brand_ad_data_v2()
            .origin_style_info()
            .has_product_icon_width()) {
      origin_style_info->set_product_icon_width(ad_result_->ad_deliver_info()
                                                    .ad_base_info()
                                                    .brand_ad_data_v2()
                                                    .origin_style_info()
                                                    .product_icon_width());
    }
    if (ad_result_->ad_deliver_info()
            .ad_base_info()
            .brand_ad_data_v2()
            .origin_style_info()
            .has_product_icon_height()) {
      origin_style_info->set_product_icon_height(ad_result_->ad_deliver_info()
                                                     .ad_base_info()
                                                     .brand_ad_data_v2()
                                                     .origin_style_info()
                                                     .product_icon_height());
    }
    if (ad_result_->ad_deliver_info()
            .ad_base_info()
            .brand_ad_data_v2()
            .origin_style_info()
            .has_ad_brand_info()) {
      origin_style_info->mutable_ad_brand_info()->CopyFrom(ad_result_->ad_deliver_info()
                                                               .ad_base_info()
                                                               .brand_ad_data_v2()
                                                               .origin_style_info()
                                                               .ad_brand_info());
    }
    if (ad_result_->ad_deliver_info()
            .ad_base_info()
            .brand_ad_data_v2()
            .origin_style_info()
            .ad_brand_info()
            .has_item_id() &&
        ad_result_->ad_deliver_info()
                .ad_base_info()
                .brand_ad_data_v2()
                .origin_style_info()
                .ad_brand_info()
                .item_id() > 0) {
      origin_style_info->mutable_ad_brand_info()->set_item_id(ad_result_->ad_deliver_info()
                                                                  .ad_base_info()
                                                                  .brand_ad_data_v2()
                                                                  .origin_style_info()
                                                                  .ad_brand_info()
                                                                  .item_id());
    }
    if (ad_result_->ad_deliver_info()
            .ad_base_info()
            .brand_ad_data_v2()
            .origin_style_info()
            .search_info()
            .ad_style_type() != 0) {
      origin_style_info->mutable_search_info()->set_ad_style_type(ad_result_->ad_deliver_info()
                                                                      .ad_base_info()
                                                                      .brand_ad_data_v2()
                                                                      .origin_style_info()
                                                                      .search_info()
                                                                      .ad_style_type());
    }
  }
  VLOG(4) << "Brand Origin style: " << origin_style_info->Utf8DebugString();
  origin_style_info->set_consult_id(forward_index_item_->unit_support_info().consult_id());
  // 品牌高级卡片
  if (ad_result_->ad_source_type() == kuaishou::ad::AdSourceType::BRAND) {
    origin_style_info->mutable_brand_activity_card_info()->CopyFrom(ad_result_->ad_deliver_info()
                                                                        .ad_base_info()
                                                                        .brand_ad_data_v2()
                                                                        .origin_style_info()
                                                                        .brand_activity_card_info());
  }
  origin_style_info->set_second_industry_name(forward_index_item_->ad_industry().industry_name());

  /* ** 满足以下条件的设置字段 wechat_game_icon_url，来源于表 AdMagicSitePageDas
   * campaign_type = 'site_page'
   * landing_page_component IN ('小游戏新组件')
   * first_industry_id_v3 = '游戏'
   */
  const auto& magic_site_das = forward_index_item_->magic_site_page();
  if ((ad_result_->ad_deliver_info().ad_base_info().campaign_type() ==
          kuaishou::ad::AdEnum_CampaignType_SITE_PAGE ||
          ad_result_->ad_deliver_info().ad_base_info().campaign_type() ==
          kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_WX_MINI_APP ||
          ad_result_->ad_deliver_info().ad_base_info().campaign_type() ==
          kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_CID) &&
      ad_result_->ad_deliver_info().ad_base_info().first_industry_id_v3() == 1018 &&
      (GetPageComponent(magic_site_das.page_component(), session_data_->dot_perf) &
        PageComponent::SMALL_GAME_NEW) == PageComponent::SMALL_GAME_NEW) {
    session_data_->dot_perf->Count(1, "hit_wegame_icon_url", "ad_matched");
    if (!magic_site_das.wechat_game_icon_url().empty()) {
      session_data_->dot_perf->Count(1, "hit_wegame_icon_url", "url_filled");
      origin_style_info->set_wechat_game_icon_url(magic_site_das.wechat_game_icon_url());
    }
  }
}

void AdStyleMgr::SetOriginStyleInfoUniverse() {
  // 联盟透传 matrix 需要用到的信息
  auto* origin_style_info = ad_data_v2.mutable_origin_style_info();

  // 联盟修改 actionBar 文案
  const int64_t ocpx_action_type = ad_result_->ad_deliver_info().ad_base_info().ocpc_action_type();
  const auto& app_label_map = FrontKconfUtil::universeAppLabelMap()->data().app_label_map();
  auto iter = app_label_map.find(ocpx_action_type);
  if (iter != app_label_map.end()) {
    origin_style_info->set_action_bar_install_app_label(iter->second.install_app_label());
    origin_style_info->set_action_bar_open_app_label(iter->second.open_app_label());
  }

  auto white_list = FrontKconfUtil::universeActionBarAppLabelWhiteList();
  auto& account_white_list = white_list->data().account_id();
  auto& product_white_list = white_list->data().product_name();
  bool is_in_white_list = (std::count(account_white_list.begin(),
                                account_white_list.end(),
                                ad_result_->ad_deliver_info().ad_base_info().account_id()) > 0)
                  || (std::count(product_white_list.begin(),
                                product_white_list.end(),
                                ad_result_->ad_deliver_info().ad_base_info().product_name()) > 0);
  if (is_in_white_list) {
    origin_style_info->set_action_bar_install_app_label(
        GetValueFromConfigString(forward_index_item_->creative().display_info(), "actionBar"));
    origin_style_info->set_action_bar_open_app_label(
        GetValueFromConfigString(forward_index_item_->creative().display_info(), "actionBar"));
  }

  if (ad_data_v2.interaction_info().interactive_style() ==
      kuaishou::ad::AdDataV2_InteractionInfo_InteractiveStyle_UNKNOWN &&
      engine_base::AdKconfUtil::universeClickRewardConf()->data().CheckMaterial(
          session_data_->get_ud_end_card_id())) {
    origin_style_info->mutable_universe_info()->set_extra_click_reward(true);
  }

  origin_style_info->mutable_universe_info()->set_exp_param(session_data_->copy_ud_sdk_exp_param());
  origin_style_info->mutable_universe_info()->set_type(
      ad_result_->ad_deliver_info().universe_ad_deliver_info().type());
  origin_style_info->mutable_universe_info()->set_sub_type(
      ad_result_->ad_deliver_info().universe_ad_deliver_info().sub_type());

  Json display_json(StringToJson(ad_result_->ad_deliver_info().ad_base_info().display_info()));
  std::string ad_description = display_json.GetString("description", "");
  std::string ad_action_description = display_json.GetString("actionBar", "");
  origin_style_info->mutable_universe_info()->set_ad_description(ad_description);
  origin_style_info->mutable_universe_info()->set_ad_action_description(ad_action_description);
  if ((ad_result_->ad_deliver_info().ad_base_info().campaign_type() ==
           kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) &&
      ad_result_->ad_deliver_info().ad_base_info().item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE) {
    if (ad_result_->ad_deliver_info().ad_base_info().ocpc_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
        ad_result_->ad_deliver_info().ad_base_info().ocpc_action_type() ==
            kuaishou::ad::AD_MERCHANT_FOLLOW_FAST) {
      origin_style_info->mutable_universe_info()->set_ad_action_description("去关注");
    } else {
      origin_style_info->mutable_universe_info()->set_ad_action_description("去看看");
    }
  }

  FillPlayDetailInfo();
  FillAdStyleInfo();
  FillDeepInspireAdStyleInfo();
  FillAdConversionInfo();
}

void AdStyleMgr::FillPlayDetailInfo() {
  // 激励/全屏视频填充
  if (session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() <= 0) {
    return;
  }
  const auto& ad_style = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).ad_style();
  if (ad_style != 2 && ad_style != 12 && ad_style != 3 && ad_style != 23) {
    return;
  }
  // 仅 SDK 可以解析
  auto cooperation_mode = session_data_->get_ad_request()->universe_ad_request_info().cooperation_mode();
  if (cooperation_mode != 1 && cooperation_mode != 4 && cooperation_mode != 6 && cooperation_mode != 8) {
    return;
  }
  auto universe_play_card_new_style_map = FrontKconfUtil::universePlayCardNewStyle();
  std::string play_card_new_style = session_data_->copy_ud_play_card_new_style();

  auto* origin_style_info = ad_data_v2.mutable_origin_style_info();
  if (session_data_->get_ud_play_card_compatible()) {
    origin_style_info->mutable_universe_info()->set_play_detail_info(
        session_data_->copy_ud_play_card_content());
  } else if (universe_play_card_new_style_map->count(play_card_new_style) != 0) {
    origin_style_info->mutable_universe_info()->set_play_detail_info(
        universe_play_card_new_style_map->at(play_card_new_style));
  }
}

void AdStyleMgr::FillAdStyleInfo() {
  if (session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() <= 0 ||
      !forward_index_item_) {
    return;
  }
  const auto& ad_style = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).ad_style();
  // 仅 SDK 可以解析 ad_style_info
  auto cooperation_mode = session_data_->get_ad_request()->universe_ad_request_info().cooperation_mode();
  // 合作类型 ADX_SDK 填充样式信息
  if (cooperation_mode != 1 && cooperation_mode != 4 && cooperation_mode != 6) {
    return;
  }

  auto universe_dynamic_adstyle_map = FrontKconfUtil::universeDynamicAdStyle();

  if (ad_result_->ad_deliver_info().ad_base_info().material_info().material_feature_size() == 0) {
    TLOG_EVERY_N(WARNING, 10000) << "material_size is null";
    return;
  }

  const auto& material_size =
      ad_result_->ad_deliver_info().ad_base_info().material_info().material_feature(0).material_size();

  std::string version_key = "1";
  const auto& universe_ad_request_info = session_data_->get_ad_request()->universe_ad_request_info();
  bool is_sdk = universe_ad_request_info.cooperation_mode() == 1;
  if (is_sdk && universe_ad_request_info.has_sdk_version()) {
    const std::string& sdk_version = universe_ad_request_info.sdk_version();
    if (universe_ad_request_info.sdk_type() == 2) {
      // 内容 sdk 3.3.19 及以上版本支持线索通组件化表单
      if (engine_base::CompareAppVersion(sdk_version, "3.3.19") >= 0) {
        version_key = "2";
      }
    }
  }

  int32_t mock_ad_style = ad_style;
  if (ad_style == 23 && ad_result_->ad_deliver_info().ad_base_info().ad_rollout_size() == 1) {
    mock_ad_style = 3;
  } else if (ad_style == 23 && ad_result_->ad_deliver_info().ad_base_info().ad_rollout_size() == 2) {
    mock_ad_style = 13;
  }
  std::string universe_dynamic_adstyle_key = absl::Substitute(
      "universe_dynamic_adstyle_$0_$1_$2_$3_$4", mock_ad_style,
      ad_result_->ad_deliver_info().ad_base_info().campaign_type(),
      material_size.width() > material_size.height() ? "horiz" : "vertical", version_key, "default");

  if (universe_dynamic_adstyle_map->count(universe_dynamic_adstyle_key) <= 0) {
    universe_dynamic_adstyle_key =
        absl::Substitute("universe_dynamic_adstyle_$0_$1_$2_1_default", mock_ad_style,
                         ad_result_->ad_deliver_info().ad_base_info().campaign_type(),
                         material_size.width() > material_size.height() ? "horiz" : "vertical");
  }

  auto* origin_style_info = ad_data_v2.mutable_origin_style_info();

  if (universe_dynamic_adstyle_map->count(universe_dynamic_adstyle_key) <= 0) {
    origin_style_info->mutable_universe_info()->set_ad_style_info(
        *FrontKconfUtil::defaultUniverseDynamicAdStyle());
  } else {
    origin_style_info->mutable_universe_info()->set_ad_style_info(
        universe_dynamic_adstyle_map->at(universe_dynamic_adstyle_key));
  }
  bool sdk_flag =
      (universe_ad_request_info.cooperation_mode() == 1 || universe_ad_request_info.cooperation_mode() == 4 ||
       universe_ad_request_info.cooperation_mode() == 6);
  if (sdk_flag && universe_ad_request_info.has_sdk_version()) {
    const std::string& sdk_version = universe_ad_request_info.sdk_version();
    bool is_sdk_allow = (universe_ad_request_info.sdk_type() == 1 &&
                         engine_base::CompareAppVersion(sdk_version, "3.3.18") >= 0) ||
                        (universe_ad_request_info.sdk_type() == 2 &&
                         engine_base::CompareAppVersion(sdk_version, "3.3.25") >= 0);
    // 直电广告样式覆盖
    if (is_sdk_allow && (ad_style == 2 || ad_style == 3 ||
        (ad_style == 23 && ad_result_->ad_deliver_info().ad_base_info().ad_rollout_size() == 1)) &&
        ad_result_->ad_deliver_info().ad_base_info().first_industry_id_v3() == 1022 &&
        !(*FrontKconfUtil::inspireJinniuUniverseDynamicAdStyle()).empty()) {
      origin_style_info->mutable_universe_info()->set_ad_style_info(
          *FrontKconfUtil::inspireJinniuUniverseDynamicAdStyle());
    }
  }
}

void AdStyleMgr::FillDeepInspireAdStyleInfo() {
  if (session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() <= 0) {
    return;
  }
  const auto& ad_style = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).ad_style();
  if (ad_style != 2 && ad_style != 12) {
    return;
  }

  auto* origin_style_info = ad_data_v2.mutable_origin_style_info();
  auto& task_type_list = session_data_->UniverseRequest().accept_task_type();
  auto campaign_type = ad_result_->ad_deliver_info().ad_base_info().campaign_type();
  if (campaign_type == kuaishou::ad::AdEnum::APP) {
    if (std::count(task_type_list.begin(), task_type_list.end(), 2)) {
      origin_style_info->mutable_universe_info()->set_task_type(2);
    } else {
      origin_style_info->mutable_universe_info()->set_task_type(1);
    }
  } else if ((campaign_type == kuaishou::ad::AdEnum::SITE_PAGE ||
              campaign_type == kuaishou::ad::AdEnum::AD_CID ||
              campaign_type == kuaishou::ad::AdEnum::AD_WX_MINI_APP) &&
             std::count(task_type_list.begin(), task_type_list.end(), 3)) {
    origin_style_info->mutable_universe_info()->set_task_type(3);
  }

  // 激励视频激活目标 APP 任务类型样式覆盖
  if (origin_style_info->universe_info().task_type() == 2 &&
      !(*FrontKconfUtil::inspireAppUniverseDynamicAdStyle()).empty()) {
    origin_style_info->mutable_universe_info()->set_ad_style_info(
        *FrontKconfUtil::inspireAppUniverseDynamicAdStyle());
  }
  // 激励视频激活目标 非下载类 任务样式类型覆盖
  if (origin_style_info->universe_info().task_type() == 3 &&
      !(*FrontKconfUtil::inspireNoAppUniverseDynamicAdStyle()).empty()) {
    origin_style_info->mutable_universe_info()->set_ad_style_info(
        *FrontKconfUtil::inspireNoAppUniverseDynamicAdStyle());
  }
}

void AdStyleMgr::FillAdConversionInfo() {
  if (session_data_->get_ad_request()->universe_ad_request_info().imp_info_size() <= 0) {
    return;
  }
  auto* universe_info = ad_data_v2.mutable_origin_style_info()->mutable_universe_info();
  auto* ad_conversion_info = universe_info->mutable_ad_conversion_info();
  auto ocpc_action_type = ad_result_->ad_deliver_info().ad_base_info().ocpc_action_type();

  switch (ad_result_->ad_deliver_info().ad_base_info().campaign_type()) {
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_APP:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MERCHANT_APP_PROMOTE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_LIVE_STREAM_PROMOTE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_FANSTOP_LIVE_TO_ALL:
      universe_info->set_ad_operation_type(
              ::kuaishou::ad::AdConversionType::APP_DOWNLOAD);  // 下载广告
      break;
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_TAOBAO:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_LANDING_PAGE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_SITE_PAGE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_WX_MINI_APP:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_CID:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_SEARCH:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_DPA_CAMPAIGN:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_KWAI_SERIAL_PROMOTION:
      // 联盟小店通广告填充 ad_operation_type
      if (ocpc_action_type == kuaishou::ad::CID_ROAS ||
          ocpc_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID) {
        universe_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::OPEN_HTML5);
        break;
      } else if (ad_result_->ad_deliver_info().ad_base_info().is_universe_jinniu_to_deeplink()) {
        // 金牛广告转成 deeplink 方式
        universe_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::APP_DOWNLOAD);  // 下载广告
        break;
      }
      universe_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::OPEN_HTML5);  // 落地页广告
      break;
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_APP_ADVANCE:
      universe_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::APP_DOWNLOAD);  // deeplink
      break;
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO:
    case ::kuaishou::ad::AdEnum_CampaignType::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE:
      universe_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::APP_DOWNLOAD);
      break;
    default:
      universe_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::UNKNOWN_CONVERSION_TYPE);
      break;
  }
  const auto& ad_style = session_data_->get_ad_request()->universe_ad_request_info().imp_info(0).ad_style();

  const auto& url = ad_result_->ad_deliver_info().ad_base_info().url();
  const auto& schema_url = ad_result_->ad_deliver_info().ad_base_info().schema_url();
  const auto& h5_url = ad_result_->ad_deliver_info().ad_base_info().h5_url();
  bool is_sdk = session_data_->get_ad_request()->universe_ad_request_info().cooperation_mode() == 1;
  bool is_universe_feed = ad_style == 7;
  bool forbidden_uni_feed_upload_url =
      is_sdk && is_universe_feed;

  if (ad_data_v2.has_market_uri()) {
    ad_conversion_info->set_market_url(ad_data_v2.market_uri());
  }
  // 填充 deeplink url
  if (!schema_url.empty()) {
    // TODO(CBB) 以下逻辑非常 trick 容易出 bad case 后续和业务确认后会删除
    std::vector<std::string> split_url = absl::StrSplit(schema_url, "&");
    auto contain_backurl = [](std::string str) -> bool {
      std::string url_flag = "backurl";
      return (std::equal(url_flag.begin(), url_flag.end(), str.begin()));
    };
    split_url.erase(std::remove_if(split_url.begin(), split_url.end(), contain_backurl), split_url.end());
    std::string url_hack = absl::StrJoin(split_url, "&");
    ad_conversion_info->set_deeplink_url(url_hack);
  }
  // 联盟出的直播广告，兜底快手/极速版的下载链接
  if (ad_result_->ad_deliver_info().ad_base_info().item_type() == kuaishou::ad::AdEnum_ItemType_ITEM_LIVE ||
      ad_result_->ad_deliver_info().ad_base_info().item_type() ==
          kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE) {
    ad_conversion_info->set_app_download_url(url);
  }

  // sdk 会优先打开 deeplink url, 为空再根据 下载类 非下载类广告打开下载 url 或者 h5 url
  switch (universe_info->ad_operation_type()) {
    case ::kuaishou::ad::AdConversionType::APP_DOWNLOAD:
      // 替换 ios 下载链接 为解决客户端 bug 添加的逻辑
      if (session_data_->get_ud_client_id() == IPHONE) {
        ad_conversion_info->set_app_download_url(ReplaceIosAppDownloadUrl(url));
      } else {
        ad_conversion_info->set_app_download_url(url);
        // 极速版激励视频，有 h5 中间页时填充 h5 中间页
        if (ad_result_->ad_deliver_info().universe_ad_deliver_info().page_id() ==
                ks::ad_base::AdPageId::kNebulaInspire &&
            ad_result_->ad_deliver_info().universe_ad_deliver_info().sub_page_id() ==
                ks::ad_base::AdSubPageId::kNebulaInspireScene) {
          if (!h5_url.empty()) {
            ad_conversion_info->set_h5_url(h5_url);
          }
        }
      }
      // 广告主自己上传下载详情页
      if (ad_data_v2.has_h5_url() && !forbidden_uni_feed_upload_url) {
        ad_conversion_info->set_h5_type(AdConversionInfoUniverse::APP_DOWNLOAD_UPLOAD);
        ad_conversion_info->set_h5_url(ad_data_v2.h5_url());
      } else {
        // 广告主没有上传的下载详情页
        // h5_url : API 填空串， SDK 填前端渲染的拼接详情页
        //          内容 SDK 支持单独设计的拼接落地页和兜底落地页
        // h5_type : API 服务没有向媒体披露填 UNKNOWN ， SDK 区分是否是有抓取
        if (!is_sdk) {
          ad_conversion_info->set_h5_url("");
          ad_conversion_info->set_h5_type(AdConversionInfoUniverse::UNKNOWN);
          break;
        }
        ad_conversion_info->set_h5_url(*FrontKconfUtil::h5Url());
        if (ad_data_v2.has_app_detail_info() &&
            ad_data_v2.app_detail_info().cdn_screen_short_urls_size() > 0) {  //  抓取成功
          ad_conversion_info->set_h5_type(AdConversionInfoUniverse::APP_DOWNLOAD_BUILD);
          if (is_universe_feed) {
            ad_conversion_info->set_h5_url(*FrontKconfUtil::universeDefaultBuildH5Url());
            falcon::Inc("front_server.set_uni_app_detail_info_succ", 1);
          }
          falcon::Inc("front_server.set_app_detail_info_succ", 1);
        } else {  //  抓取失败
          ad_conversion_info->set_h5_type(AdConversionInfoUniverse::UNKNOWN);
          if (is_universe_feed) {
            ad_conversion_info->set_h5_url(*FrontKconfUtil::universeDefaultH5Url());
            falcon::Inc("front_server.set_uni_app_detail_info_fail", 1);
          }
          falcon::Inc("front_server.set_app_detail_info_fail", 1);
        }
      }
      break;
    case ::kuaishou::ad::AdConversionType::OPEN_HTML5:
      ad_conversion_info->set_h5_url(url);
      ad_conversion_info->set_h5_type(AdConversionInfoUniverse::NON_APP_DOWNLOAD);
      break;
    default:
      break;
  }
  // 下载兜底逻辑修复
  if ((ad_result_->ad_deliver_info().ad_base_info().campaign_type() ==
          kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
      ad_result_->ad_deliver_info().ad_base_info().campaign_type() ==
          kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
      ad_result_->ad_deliver_info().ad_base_info().campaign_type() ==
          kuaishou::ad::AdEnum_CampaignType_MINI_APP_CAMPAIGN_TYPE)
      && ocpc_action_type != kuaishou::ad::CID_ROAS
      && ocpc_action_type != kuaishou::ad::CID_EVENT_ORDER_PAID) {
    universe_info->set_ad_operation_type(::kuaishou::ad::AdConversionType::APP_DOWNLOAD);
    if (session_data_->get_is_ios_platform()) {
      ad_conversion_info->set_app_download_url(*FrontKconfUtil::kuaishouIosAppDownloadUrl());
    } else if (session_data_->get_ad_request()->ad_user_info().platform() == "android") {
      ad_conversion_info->set_app_download_url(*FrontKconfUtil::kuaishouAppDownloadUrl());
    } else if (session_data_->get_ad_request()->ad_user_info().platform() == "harmony") {
      ad_conversion_info->set_app_download_url(*FrontKconfUtil::kuaishouHarmonyAppDownloadUrl());
    }
  }

  // 试玩广告配置, 一期从 kconf 配置读取
  const auto& universePlayableConfig = FrontKconfUtil::universePlayableConfig();
  const auto& unit_config_map = universePlayableConfig->data().unit_config_map();
  const int64_t unit_id = ad_result_->ad_deliver_info().ad_base_info().unit_id();
  auto iter = unit_config_map.find(unit_id);
  const int64_t creative_id = ad_result_->ad_deliver_info().ad_base_info().creative_id();
  if (iter != unit_config_map.end()) {
    ad_conversion_info->set_playable_url(iter->second.playable_url());
    ad_conversion_info->set_playable_style_info(iter->second.playable_style_info());
  }

  // 联盟 playable 广告放开明投流量限制，即如果改广告位 playable 广告
  // 但是本次流量为非 playable 流量，改广告依然可以投放，但是要去掉 playable 元素
  const auto& unit_support_info = forward_index_item_->unit_support_info();
  if (session_data_->IsUnionPlayableExpand()) {
    // 试玩广告填充
    if (unit_support_info.playable_switch() == 2) {
      ad_conversion_info->set_playable_url(unit_support_info.playable_url());
      ad_conversion_info->set_playable_orientation(unit_support_info.playable_orientation());
    }
    const auto& play_info = forward_index_item_->play_info();
    if ((play_info.upload_source() == 2 || (play_info.upload_source() == 3)) &&
        play_info.play_url() != "") {
      ad_conversion_info->set_playable_url(play_info.play_url());
      ad_conversion_info->set_playable_orientation(play_info.play_orientation());
    }
  }

  if (!ad_conversion_info->playable_url().empty()) {
    std::string replace_url;
    base::FastStringReplace(ad_conversion_info->playable_url(), "__PLAYABLE_TYPE__", "union", true,
                            &replace_url);
    if (replace_url != ad_conversion_info->playable_url()) {
      ad_conversion_info->set_playable_url(replace_url);
    } else {
      std::string::size_type pos = ad_conversion_info->playable_url().find('?');
      if (pos != std::string::npos) {
        std::string temp{ad_conversion_info->playable_url()};
        ad_conversion_info->set_playable_url(temp.insert(pos + 1, "type=union&"));
      } else {
        ad_conversion_info->set_playable_url(ad_conversion_info->playable_url() + "?type=union");
      }
    }
  }
}

void AdStyleMgr::SetMerchantOriginStyleInfo(kuaishou::ad::AdDataV2_OriginStyleInfo* origin_style_info) {
  auto creative_id = forward_index_item_->creative().id();

  auto campaign_type = forward_index_item_->campaign().type();
  if (campaign_type == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE ||
      campaign_type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE ||
      forward_index_item_->account().account_type() == AdEnum::ACCOUNT_ESP_MOBILE) {
    int64_t author_id = forward_index_item_->account().user_id();
    author_id = GetAuthorID(session_data_, *forward_index_item_);
    if (forward_index_item_->has_unit_small_shop_merchant_support_info()) {
      const auto& support_info = forward_index_item_->unit_small_shop_merchant_support_info();
      origin_style_info->mutable_merchant_info()->set_merchant_ext_data(support_info.merchant_ext_data());
    }
    // 优惠券信息
    bool is_skip_qcpx_mode = session_data_->get_is_closure_flow() &&
        session_data_->is_closure_ad(campaign_type, forward_index_item_->unit().ocpx_action_type());
    auto* p_rank_result = GetAdRankResult(session_data_, *ad_result_);
    if (p_rank_result && !is_skip_qcpx_mode) {
      const auto& coupon_info = p_rank_result->coupon_info();
      auto* coupon_info_data_v2 = origin_style_info->mutable_merchant_info()->mutable_coupon_info();
      coupon_info_data_v2->set_coupon_template_id(coupon_info.coupon_template_id());
      coupon_info_data_v2->set_coupon_type(coupon_info.coupon_type());
      auto* coupon_rule = coupon_info_data_v2->mutable_coupon_rule();
      coupon_rule->set_threshold_type(coupon_info.threshold_type());
      coupon_rule->set_threshold(coupon_info.threshold());
      coupon_rule->set_discount_amount(coupon_info.discount_amount());
      coupon_rule->set_threshold_upper(coupon_info.threshold_upper());
      coupon_rule->set_discount_amount_upper(coupon_info.discount_amount_upper());
      coupon_rule->set_reduce_amount(coupon_info.reduce_amount());
      coupon_rule->set_capped_amount(coupon_info.capped_amount());
    }
  }

  if (forward_index_item_->campaign().type() == AdEnum::LIVE_STREAM_PROMOTE ||
      forward_index_item_->account().account_type() == AdEnum::ACCOUNT_ESP_MOBILE) {
    auto author_id = forward_index_item_->account().user_id();

    auto live_stream_info = origin_style_info->mutable_merchant_info()->mutable_live_stream_info();
    if (forward_index_item_->has_live_stream_user_info()) {
      live_stream_info->set_display_online_count(0);
    }
    if (forward_index_item_->creative().has_creative_support_info()) {
      live_stream_info->set_live_theme(forward_index_item_->creative().creative_support_info().live_theme());
    }
  }
}

void AdStyleMgr::SetMerchantProductInfo() {
  // 小店通小黄车需求
  if (forward_index_item_->campaign().type() == AdEnum::MERCHANT_RECO_PROMOTE ||
      forward_index_item_->account().account_type() == AdEnum::ACCOUNT_ESP_MOBILE) {
    auto item_type = merchant_util::GetSmallShopItemType(*forward_index_item_);
    if (item_type != AdEnum::MERCHANT_PUT_PRODUCT) {
      return;
    }
    auto product_info =
        ad_data_v2.mutable_origin_style_info()->mutable_merchant_info()->mutable_product_info();
    auto item_id = merchant_util::GetSmallShopItemId(*forward_index_item_);
    if (item_id == 0) {
      return;
    }
    if (forward_index_item_->has_unit_small_shop_merchant_support_info()) {
      const auto& support_info = forward_index_item_->unit_small_shop_merchant_support_info();
      product_info->set_product_label_type(support_info.product_label_type());
      product_info->set_product_label_end_time(support_info.product_label_end_time());
    }
  }
}

void AdStyleMgr::ResetActionbarTime() {
  auto* p_action_bar_info = ad_data_v2.mutable_action_bar_info();
  int64_t action_bar_load_time = p_action_bar_info->action_bar_load_time();
  int64_t real_show_delay_time = p_action_bar_info->real_show_delay_time();
  int64_t downloaded_action_bar_load_time = p_action_bar_info->downloaded_action_bar_load_time();
  if (ks::ad_base::GetInteractiveForm(*(session_data_->get_ad_request())) ==
      kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS) {
    p_action_bar_info->set_action_bar_load_time(5000);
    p_action_bar_info->set_real_show_delay_time(7000);
    p_action_bar_info->set_downloaded_action_bar_load_time(3000);
  } else {
    p_action_bar_info->set_action_bar_load_time(1);
    p_action_bar_info->set_real_show_delay_time(3000);
    p_action_bar_info->set_downloaded_action_bar_load_time(1000);
  }
}

bool AdStyleMgr::MatchPcLiveScene() {
  if (forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
      forward_index_item_->campaign().type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
    return true;
  }
  return false;
}

void AdStyleMgr::SetClickUrl() {
  if (IsNonMerchantPhotoToLiveStream(*forward_index_item_) ||
      (forward_index_item_->account().account_type() == AdEnum::ACCOUNT_ESP_MOBILE
       && !MatchPcLiveScene())) {
    // 视频引流直播推广下发加密的 live_stream_id
    ad_data_v2.mutable_item_click_urls()
        ->mutable_avatar_click_urls()
        ->mutable_jump_live_info()
        ->set_live_stream_ids(forward_index_item_->live_stream_user_info().live_stream_id_encrypted());
    ad_data_v2.mutable_item_click_urls()
        ->mutable_avatar_click_urls()
        ->mutable_jump_live_info()
        ->set_to_live_type(1);
    ad_data_v2.mutable_item_click_urls()
        ->mutable_avatar_click_urls()
        ->mutable_jump_live_info()
        ->set_bind_ad_to_live_stream_ids(
            forward_index_item_->live_stream_user_info().live_stream_id_encrypted());
    // action bar 的和 avtar 的一样
    ad_data_v2.mutable_item_click_urls()->mutable_action_bar_click_urls()->CopyFrom(
        ad_data_v2.item_click_urls().avatar_click_urls());
    // 设置是否在直播状态
    ad_data_v2.set_show_live_icon(forward_index_item_->live_stream_user_info().is_live());
    ad_data_v2.mutable_jump_live_info()->CopyFrom(
        ad_data_v2.item_click_urls().avatar_click_urls().jump_live_info());
  }

  int64_t landing_page_component;
  auto *p_rank_result = GetAdRankResult(session_data_, *ad_result_);
  if (p_rank_result) {
    landing_page_component = p_rank_result->rank_base_info().landing_page_component();
  } else {
    landing_page_component = GetPageComponent(
        forward_index_item_->magic_site_page().page_component(), session_data_->dot_perf);
  }
  if (landing_page_component <= 0
      && forward_index_item_->magic_site_page().extend_fields().landing_page_component() > 0) {
    landing_page_component =
      forward_index_item_->magic_site_page().extend_fields().landing_page_component();
  }
  if (forward_index_item_->campaign().type() ==
          kuaishou::ad::AdEnum_CampaignType_KWAI_PROMOTION_CONSULTATION) {
    AddPromotionConsultationClickUrl();
  } else if (ad_result_->ad_deliver_info().ad_base_info().first_industry_id_v3() == 1018 &&
             landing_page_component == 2 &&
             FrontKconfUtil::wechatGameBlackList()->count(
              ad_result_->ad_deliver_info().ad_base_info().product_name()) == 0) {
    // wechat small game jump
    std::string direct_jump_url = ad_result_->ad_deliver_info().ad_base_info().schema_url();
    // replace __DEEP_LINK_PROTOCOL__
    std::map<std::string, std::string> replace_macro_map;
    replace_macro_map.insert(std::make_pair(kDIDKeyWords,
      std::to_string(ad_result_->ad_deliver_info().ad_base_info().campaign_id())));
    replace_macro_map.insert(std::make_pair(kAIDKeyWords,
      std::to_string(ad_result_->ad_deliver_info().ad_base_info().unit_id())));
    replace_macro_map.insert(std::make_pair(kCIDKeyWords,
      std::to_string(ad_result_->ad_deliver_info().ad_base_info().creative_id())));

    ReplaceUrlMacro(&direct_jump_url, replace_macro_map);

    // direct jump
    auto jump_enum_list = FrontKconfUtil::wechatGameDirectJumpAreaEnumList();
    auto* mutable_item_click_list = ad_data_v2.add_item_click_list();
    for (int i = 0; i < jump_enum_list->size(); i++) {
      mutable_item_click_list->add_item_click_types((*jump_enum_list)[i]);
    }
    mutable_item_click_list->mutable_item_click_url()->set_app_link(direct_jump_url);
    // remove schema_url, client fall back to default h5_url
    ad_result_->mutable_ad_deliver_info()->mutable_ad_base_info()->set_schema_url("");
    session_data_->dot_perf->Count(1, "ad_wechat_small_game_new_jumping");
  } else {
    auto* action_bar_click_urls = ad_data_v2.mutable_item_click_urls()->mutable_action_bar_click_urls();
    std::string consult_info = forward_index_item_->unit_support_info().consult_info();
    base::Json consult_json(base::StringToJson(consult_info));
    std::string url = "";
    std::string app_link = "";
    if (consult_json.IsObject()) {
      url = consult_json.GetString("url", "");
      app_link = consult_json.GetString("appLink", "");
    }
    action_bar_click_urls->set_url(url);
    action_bar_click_urls->set_app_link(app_link);
  }
}

void AdStyleMgr::AddPromotionConsultationClickUrl() {
  // 私信咨询场景，视频作者和蓝 v 用户一致，不填充信息
  if (forward_index_item_->creative().creative_support_info().kol_user_id() ==
      forward_index_item_->creative().creative_support_info().photo_author_id()) {
    return;
  }
  // 增加私信咨询页面跳转
  {
    auto* mutable_item_click_list = ad_data_v2.add_item_click_list();
    mutable_item_click_list->add_item_click_types(
        static_cast<int32>(kuaishou::ad::ItemClickType::ACTION_BAR_CLICK));
    mutable_item_click_list->add_item_click_types(
        static_cast<int32>(kuaishou::ad::ItemClickType::COMMENT_ITEM_CLICK));
    mutable_item_click_list->add_item_click_types(
        static_cast<int32>(kuaishou::ad::ItemClickType::BOTTOM_ICON_CLICK));
    mutable_item_click_list->add_item_click_types(
        static_cast<int32>(kuaishou::ad::ItemClickType::PLAYEND_ACTION_BAR_CLICK));
    mutable_item_click_list->add_item_click_types(
        static_cast<int32>(kuaishou::ad::ItemClickType::CARD_ACTION_BAR_CLICK));

    std::string consult_info = forward_index_item_->unit_support_info().consult_info();
    base::Json consult_json(base::StringToJson(consult_info));
    std::string url = "";
    std::string app_link = "";
    if (consult_json.IsObject()) {
      url = consult_json.GetString("url", "");
      app_link = consult_json.GetString("appLink", "");
    }
    auto* mutable_item_click_url = mutable_item_click_list->mutable_item_click_url();
    mutable_item_click_url->set_url(url);
    mutable_item_click_url->set_app_link(app_link);
  }
}

void AdStyleMgr::SetPosInfo() {
  int64_t pos_id = 0;
  if (ad_request_->universe_ad_request_info().imp_info_size() > 0) {
    pos_id = ad_request_->universe_ad_request_info().imp_info(0).position_id();
  }
  if (pos_id == 313 || pos_id == 314) {
    pos_id = ad_result_->ad_deliver_info().universe_ad_deliver_info().pos_id();
  }
  ad_data_v2.set_pos_id(pos_id);
}

void AdStyleMgr::SetSurveyOriginStyleInfoOpt(kuaishou::ad::AdDataV2_OriginStyleInfo* origin_style_info) {
  if (!session_data_->get_has_ad_credict_user_score_redis()) {
    session_data_->dot_perf->Count(1, "get_survey_exp", "fail");
    return;
  }
  session_data_->dot_perf->Count(1, "get_survey_exp", "succ");
  auto survey_id_list = FrontKconfUtil::surveyIdList();
  int64_t survey_id_list_size = survey_id_list->size();
  if (survey_id_list_size <= 0) {
    return;
  }
  int32_t rand_select_index = ks::ad_base::AdRandom::GetInt(0, survey_id_list_size - 1);
  int64_t survey_id = (*survey_id_list)[rand_select_index];
  origin_style_info->set_render_survery_status(true);
  origin_style_info->add_survey_ids(survey_id);

  origin_style_info->mutable_survey_info()->set_survey_id(survey_id);
}

void AdStyleMgr::SetAdxAppInfo() {
  if (ad_result_->ad_source_type() != kuaishou::ad::ADX) {
    return;
  }
  auto* origin_style_info = ad_data_v2.mutable_origin_style_info();
  if (origin_style_info != nullptr) {
    if (ad_result_->ad_deliver_info().has_app_compliance_info()) {
      origin_style_info->mutable_adx_app_compliance_info()->CopyFrom(
        ad_result_->ad_deliver_info().app_compliance_info());
    }
    origin_style_info->set_adx_app_name(ad_result_->ad_deliver_info().ad_base_info().app_name());
  }
}

void AdStyleMgr::FillSmartOffersInfo(
    kuaishou::ad::industry_opt::AdSmartOffersInfo* p_ad_smart_offers_info) {
  p_ad_smart_offers_info->set_visitor_id(session_data_->get_user_id());
  p_ad_smart_offers_info->set_llsid(session_data_->get_llsid());
  std::string exp_tag = SPDM_smart_offer_exp_tag(session_data_->get_spdm_ctx());
  double lower_bound_pay = SPDM_smart_offer_lower_bound_pay(session_data_->get_spdm_ctx());
  double lower_bound_offer = SPDM_smart_offer_lower_bound_offer(session_data_->get_spdm_ctx());
  double upper_bound_pay = SPDM_smart_offer_upper_bound_pay(session_data_->get_spdm_ctx());
  double upper_bound_offer = SPDM_smart_offer_upper_bound_offer(session_data_->get_spdm_ctx());
  double default_roi = SPDM_smart_offer_default_roi(session_data_->get_spdm_ctx());
  double min_pay_amount = SPDM_smart_offer_min_pay_amount(session_data_->get_spdm_ctx());
  int64_t lessons_adjust = SPDM_smart_offer_lessons_adjust(session_data_->get_spdm_ctx());
  int64_t coins_adjust = SPDM_smart_offer_coins_adjust(session_data_->get_spdm_ctx());
  bool enable_second_price_bound = SPDM_enable_second_price_bound(session_data_->get_spdm_ctx());
  bool enable_early_return_offers = SPDM_enable_early_return_offers(session_data_->get_spdm_ctx());
  bool C_second_price = SPDM_C_second_price(session_data_->get_spdm_ctx());
  const auto& smart_offer_conf = FrontKconfUtil::adSmartOfferConfig();
  const auto& smart_offer_value = FrontKconfUtil::adSmartOfferValues();
  const auto& series_episode_price_map = FrontKconfUtil::adSmartOfferSeriesEpisodePriceMap();
  const auto& series_whitelist =  FrontKconfUtil::adSmartOfferSeriesWhiteList();
  const std::string overall_tag = "0";
  const uint64_t offer_timestamp = session_data_->get_ad_request()->ad_user_info().offer_timestamp();
  uint32_t user_tag = session_data_->get_ad_request()->ad_user_info().smart_offer_user_tag();
  auto playlet_sdpa_ = ks::engine_base::IndustryPlayletSdpaIns().GetData();
  const int64_t unit_id = ad_result_->ad_deliver_info().ad_base_info().unit_id();
  std::string playlet_name;
  if (playlet_sdpa_ && playlet_sdpa_.get()) {
    auto playlet_sdpa_ptr = playlet_sdpa_->find(unit_id);
    if (playlet_sdpa_ptr != playlet_sdpa_->end()) {
      playlet_name = playlet_sdpa_ptr->second.playlet_name;
    }
  }
  std::shared_ptr<kconf::AdSmartOfferValues::SmartOfferValue> offer_conf;
  if (smart_offer_conf != nullptr && smart_offer_value != nullptr) {
    auto match_exp_list = smart_offer_conf->data().exp_list().find(exp_tag);
    if (match_exp_list != smart_offer_conf->data().exp_list().end()) {
      auto match_user_conf = match_exp_list->second.user_conf().find(absl::StrCat(user_tag));
      if (match_user_conf == match_exp_list->second.user_conf().end()) {
        match_user_conf = match_exp_list->second.user_conf().find(overall_tag);
      }
      if (match_user_conf != match_exp_list->second.user_conf().end()) {
        std::string offer_version = match_user_conf->second;
        auto offer_version_iter = smart_offer_value->data().version().find(offer_version);
        if (offer_version_iter != smart_offer_value->data().version().end()) {
            offer_conf = std::make_shared<
                kconf::AdSmartOfferValues::SmartOfferValue>(offer_version_iter->second);
        }
      }
    }
  }
  if (offer_conf == nullptr) {
    session_data_->dot_perf->Count(1, "smart_offer_config_is_null_error");
    return;
  }
  double unify_ltv = 0.0;
  double pay_weight = 1.0;
  double offer_weight = 1.0;
  const int pay_trans = 1000;
  auto* p_rank_result = GetAdRankResult(session_data_, *ad_result_);
  if (p_rank_result) {
    unify_ltv = p_rank_result->ad_rank_trans_info().unify_ltv();
  }
  if (offer_conf->default_ratio() > 0) {
    default_roi = offer_conf->default_ratio();
  }
  if (offer_conf->pay_weight() != 0) {
    pay_weight = offer_conf->pay_weight();
  }
  if (offer_conf->offer_weight() != 0) {
    offer_weight = offer_conf->offer_weight();
  }
  if (default_roi <= 0) {
    session_data_->dot_perf->Count(1, "smart_offer_roi_config_error");
    default_roi = 3;
  }
  double offer_result = (floor((1 - 1 / default_roi) * unify_ltv * pay_weight +
    offer_conf->pay_adjust()) - 1 + 0.9);
  double offer_value = floor(1 / default_roi * unify_ltv * offer_weight + offer_conf->offer_adjust());
  double offer_control_score = session_data_->get_ad_request()->ad_user_info().offer_control_score();
  double offer_treatment_score = session_data_->get_ad_request()->ad_user_info().offer_treatment_score();
  auto activity_interval = offer_conf->activity_interval();
  if (offer_conf->min_conf_pay() > 0 && offer_result < offer_conf->min_conf_pay()) {
    offer_result = offer_conf->min_conf_pay();
    offer_value = offer_conf->min_conf_offer();
    session_data_->dot_perf->Count(1, "smart_offer_less_than_min_conf_pay");
  }
  if (offer_conf->max_conf_pay() > 0 && offer_result > offer_conf->max_conf_pay()) {
    offer_result = offer_conf->max_conf_pay();
    offer_value = offer_conf->max_conf_offer();
    session_data_->dot_perf->Count(1, "smart_offer_greater_than_max_conf_pay");
  }
  offer_result = std::max(std::min(offer_result, upper_bound_pay), lower_bound_pay);
  offer_value = std::max(std::min(offer_value, upper_bound_offer), lower_bound_offer);
  if ((offer_result + offer_value) < min_pay_amount) {
    offer_result = min_pay_amount - floor(offer_value);
    session_data_->dot_perf->Count(1, "smart_offer_use_min_pay_amount");
  }
  p_ad_smart_offers_info->set_offer_result(offer_result * pay_trans);
  p_ad_smart_offers_info->set_offer_value(offer_value * pay_trans);
  if (offer_result <= 10) {
    p_ad_smart_offers_info->set_package_lessons(20 + lessons_adjust);
    p_ad_smart_offers_info->set_ms_coin_amount(2000 + coins_adjust);
  } else if (10 < offer_result && offer_result <= 15) {
    p_ad_smart_offers_info->set_package_lessons(30 + lessons_adjust);
    p_ad_smart_offers_info->set_ms_coin_amount(3000 + coins_adjust);
  } else {
    p_ad_smart_offers_info->set_package_lessons(40 + lessons_adjust);
    p_ad_smart_offers_info->set_ms_coin_amount(4000 + coins_adjust);
  }
  for (const auto& kv : offer_conf->tiered_value()) {
    auto* tired_discounts_info = p_ad_smart_offers_info->add_tiered_discounts_info();
    tired_discounts_info->set_payment_thd(kv.first * pay_trans);
    tired_discounts_info->set_discount_value(kv.second * pay_trans);
    tired_discounts_info->set_offer_value(kv.second * pay_trans);
  }
  p_ad_smart_offers_info->set_activity_interval(activity_interval);
  p_ad_smart_offers_info->set_model_score(floor(unify_ltv));
  if (offer_timestamp > 0 && (base::GetTimestamp()/1000 - offer_timestamp < activity_interval)) {
    p_ad_smart_offers_info->set_without_offer(true);
    session_data_->dot_perf->Count(1, "smart_offer_without_by_freq");
    if (enable_early_return_offers) {
      return;
    }
  }
  if (SPDM_C_activity(session_data_->get_spdm_ctx()) == "NoActivity") {
    p_ad_smart_offers_info->set_without_offer(true);
    session_data_->dot_perf->Count(1, "smart_offer_without_by_ab");
    if (enable_early_return_offers) {
      return;
    }
  }

  // 剩余全集需求
  if (offer_conf->package_discount_size() > 0) {
    for (const auto& package_discount_i : offer_conf->package_discount()) {
      auto* package_discount = p_ad_smart_offers_info->add_package_discount();
      package_discount->set_interval(package_discount_i.interval());
      package_discount->set_discount_ratio(package_discount_i.discount_ratio());
      package_discount->set_offer_ratio(package_discount_i.offer_ratio());
      for (const auto& price_i : package_discount_i.single_price_range()) {
        package_discount->add_single_price_range(price_i);
      }
    }
  }

  // 付费挽留需求
  if (SPDM_Paid_retention(session_data_->get_spdm_ctx())) {
    auto* retain_info = p_ad_smart_offers_info->mutable_retain_info();
    retain_info->set_discount_ratio(SPDM_retention_discount(session_data_->get_spdm_ctx()));
    retain_info->set_offer_ratio(SPDM_retention_subsidy(session_data_->get_spdm_ctx()));
    retain_info->set_interval(SPDM_pay_threshold(session_data_->get_spdm_ctx()));
  }

  // 观看有礼需求
  if (SPDM_enable_watch_task_price(session_data_->get_spdm_ctx()) && series_episode_price_map != nullptr &&
      SPDM_watch_task_checkpiont(session_data_->get_spdm_ctx()) > 0) {
    const int watch_task_checkpiont = SPDM_watch_task_checkpiont(session_data_->get_spdm_ctx());
    const int watch_task_ratio_lower_bound = SPDM_watch_task_ratio_lower_bound(session_data_->get_spdm_ctx());
    const int watch_task_ratio_upper_bound = SPDM_watch_task_ratio_upper_bound(session_data_->get_spdm_ctx());
    int64_t episode_price = FrontKconfUtil::adSmartOfferSeriesEpisodeDefaultPrice();
    const int64_t series_id = forward_index_item_->unit_support_info().series_id();
    auto series_episode_price_map_itr = series_episode_price_map->find(absl::StrCat(series_id));
    if (series_episode_price_map_itr != series_episode_price_map->end() &&
      series_episode_price_map_itr->second > 0) {
        episode_price = series_episode_price_map_itr->second;
    }
    double rand_ratio = ks::ad_base::AdRandom::GetInt(watch_task_ratio_lower_bound,
                                                          watch_task_ratio_upper_bound);
    int64_t total_amount = watch_task_checkpiont * rand_ratio * episode_price / 100;
    if (series_whitelist->find(series_id) != series_whitelist->end() && user_tag == 0) {
      ad_data_v2.mutable_serial_pay_info()->set_watch_task_total_amount(total_amount);
      if (SPDM_enable_watch_task(session_data_->get_spdm_ctx())) {
        ad_data_v2.mutable_serial_pay_info()->set_watch_task_checkpoint(watch_task_checkpiont);
      }
    }
  }

  // 第二专属价需求
  if (C_second_price && p_rank_result) {
    const auto playlet_prices = p_rank_result->ad_rank_trans_info().playlet_price_str();
    const auto playlet_commodity_ids = p_rank_result->ad_rank_trans_info().playlet_commodity_id_str();
    int32_t second_min_price = INT_MAX;
    int64_t second_min_commodity_id = 0;
    int32_t min_price_thd = 9;
    int32_t second_price_lower_bound = 1500;
    int32_t second_price_upper_bound = 4500;
    int32_t commodity_min_price = INT_MAX;
    int32_t commodity_min_id = 0;

    if (playlet_commodity_ids.size() > 0 && playlet_commodity_ids.size() == playlet_prices.size()) {
      for (int i  = 0; i < playlet_prices.size(); ++i) {
        if (playlet_prices[i] < commodity_min_price) {
          commodity_min_price = playlet_prices[i];
          commodity_min_id = playlet_commodity_ids[i];
        }
        if (enable_second_price_bound && playlet_prices[i] < second_min_price &&
        playlet_prices[i] > second_price_lower_bound && playlet_prices[i] < second_price_upper_bound) {
          second_min_price = playlet_prices[i];
          second_min_commodity_id = playlet_commodity_ids[i];
        }
      }
      session_data_->dot_perf->Count(1, "smart_offer_commodity_has_found");
    } else {
      session_data_->dot_perf->Count(1, "smart_offer_commodity_doesnt_find");
    }

    double second_offer_result;
    double second_offer_value;
    int second_lessons_res;
    int second_coin_res;
    if (offer_result < min_price_thd) {
      second_offer_result = floor(offer_result * 1.5) + 0.9;
      second_offer_value = floor(offer_value * 1.5);
      second_lessons_res = p_ad_smart_offers_info->package_lessons() + 20;
      second_coin_res = p_ad_smart_offers_info->ms_coin_amount() + 2000;
    } else {
      second_offer_result = floor(offer_result * 0.5) + 0.9;
      second_offer_value = floor(offer_value * 0.5);
      second_lessons_res = p_ad_smart_offers_info->package_lessons() > 10 ?
        p_ad_smart_offers_info->package_lessons()/2 - 5 : 10;
      second_coin_res = p_ad_smart_offers_info->ms_coin_amount() > 1000 ?
        p_ad_smart_offers_info->ms_coin_amount()/2 - 500 : 1000;
    }
    auto* specific_offer_infos = p_ad_smart_offers_info->add_specific_offer_infos();
    specific_offer_infos->set_offer_result(second_offer_result * pay_trans);
    specific_offer_infos->set_offer_value(second_offer_value * pay_trans);
    specific_offer_infos->set_package_lessons(second_lessons_res);
    specific_offer_infos->set_ms_coin_amount(second_coin_res);
    specific_offer_infos->set_interval(p_ad_smart_offers_info->activity_interval());
    if (commodity_min_id > 0) {
      specific_offer_infos->set_commodity_id(commodity_min_id);
    }
    if (enable_second_price_bound && second_min_commodity_id > 0 &&
      second_min_price < second_price_upper_bound &&
      second_min_price > second_price_lower_bound) {
      specific_offer_infos->set_no_user_right_change(true);
      specific_offer_infos->set_commodity_id(second_min_commodity_id);
    }
  }

  //  C 补超小额面板过滤需求  包含剧豆和组合打包俩场景
  if (p_rank_result) {
    const auto playlet_prices = p_rank_result->ad_rank_trans_info().playlet_price_str();
    double C_bu_min_filter_price = SPDM_C_bu_min_filter_price(session_data_->get_spdm_ctx());
    int32 now_min_price = 0;
    if (playlet_prices.size() > 0) {
      now_min_price = *std::min_element(playlet_prices.begin(), playlet_prices.end());
    }
  }

  session_data_->dot_perf->Interval(unify_ltv, "model_score_result", absl::StrCat(user_tag));
  session_data_->dot_perf->Interval(
    p_ad_smart_offers_info->offer_result(), "model_offer_result", absl::StrCat(user_tag));
  session_data_->dot_perf->Interval(
    p_ad_smart_offers_info->offer_value(), "model_offer_value", absl::StrCat(user_tag));
  session_data_->dot_perf->Count(1, "ad_smart_offers_info", "without_offer",
                                 absl::StrCat(p_ad_smart_offers_info->without_offer()));
  session_data_->dot_perf->Interval(
    offer_control_score * 1000, "offer_control_score", absl::StrCat(user_tag));
  session_data_->dot_perf->Interval(
    offer_treatment_score * 1000, "offer_treatment_score", absl::StrCat(user_tag));
  return;
}

void AdStyleMgr::SetKxySubsidyInfo() {
  const auto& ocpc_action_type = ad_result_->ad_deliver_info().ad_base_info().ocpc_action_type();
  if (!IsGameAd() || forward_index_item_->campaign().type() != kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE ||
      (session_data_->get_enable_kxy_subsidy_global_nc() && session_data_->get_iap_ocpx_set() != nullptr &&
       session_data_->get_iap_ocpx_set()->count(kuaishou::ad::AdActionType_Name(ocpc_action_type)) <= 0)) {
    return;
  }
  auto product_name = ad_result_->ad_deliver_info().ad_base_info().product_name();
  if (session_data_->get_enable_kxy_subsidy_global_nc()) {
    ad_data_v2.mutable_origin_style_info()->set_ad_kxy_subsidies_user_tag(1);
  }
}

void AdStyleMgr::SetAdSmartOffersInfo() {
  kuaishou::ad::industry_opt::AdSmartOffersInfo ad_smart_offers_info;
  FillSmartOffersInfo(&ad_smart_offers_info);
  std::string ad_smart_offers_info_string;
  std::string encode_ad_smart_offers_info_string;
  ad_smart_offers_info.SerializeToString(&ad_smart_offers_info_string);
  if (!ad_smart_offers_info_string.empty()) {
    serving_base::AesCrypter aes_crypter;
    base::Base64Encode(aes_crypter.Encrypt(ad_smart_offers_info_string),
        &encode_ad_smart_offers_info_string);
    ad_data_v2.mutable_origin_style_info()->set_ad_smart_offers_info(
        encode_ad_smart_offers_info_string);
    if (ad_smart_offers_info.without_offer() == true) {
      ad_data_v2.set_has_smart_offer(false);
    } else {
      ad_data_v2.set_has_smart_offer(true);
      ad_data_v2.set_smart_offer_value(ad_smart_offers_info.offer_value());
      ad_data_v2.set_smart_offer_result(ad_smart_offers_info.offer_result());
      ad_data_v2.set_smart_offer_lessons(ad_smart_offers_info.package_lessons());
      ad_data_v2.set_smart_offer_activity_interval(ad_smart_offers_info.activity_interval());
    }
    LOG_EVERY_N(INFO, 10000) << "debug ad_smart_offers_info, " << encode_ad_smart_offers_info_string;
  }
}

void AdStyleMgr::SetTransMacros(std::pair<std::string, std::string> trans_macros_pair) {
  auto* trans_macros = ad_data_v2.mutable_trans_macros();
  if (trans_macros_pair.first == std::string(kVIDKeyWords)) {
    trans_macros->set_vid(trans_macros_pair.second);
    session_data_->dot_perf->Count(1, "trans_macros_replaced", "vid");
  } else if (trans_macros_pair.first == std::string(kCSITEKeyWords)) {
    trans_macros->set_csite(trans_macros_pair.second);
    session_data_->dot_perf->Count(1, "trans_macros_replaced", "csite");
  }
  return;
}

void AdStyleMgr::SetRtaMacros() {
  if (ad_result_->ad_deliver_info().ad_base_info().rta_source_type()
      == kuaishou::ad::RtaSourceType::UNKNOWN_RTA_SOURCE_TYPE) {
    return;
  }
  const auto &online_join_params_transparent = ad_result_->ad_deliver_info().online_join_params_transparent();
  auto* rta_macros = ad_data_v2.mutable_rta_macros();
  rta_macros->set_feature_id(online_join_params_transparent.rta_feature_id());
  rta_macros->set_rta_bid_ratio(std::to_string(online_join_params_transparent.first_click_score()));
  rta_macros->set_trace_req_id(std::to_string(online_join_params_transparent.trace_request_id()));
  rta_macros->set_rta_bid_sta_id(std::to_string(online_join_params_transparent.rta_sta_tag()));
  rta_macros->set_req_id(std::to_string(session_data_->get_llsid()));
  session_data_->dot_perf->Count(1, "rta_macros_filled");
}

void AdStyleMgr::SetRecruitSimilarPositionInfo() {
  // 联调预览
  if (session_data_->get_ac_fetcher_type() != kuaishou::ad::AdTargetResponse::UNKONWN_FETCHER_TYPE) {
    if (KS_UNLIKELY(
      ad_request_ == nullptr ||
      !ad_request_->has_recruit_info() ||
      !ad_request_->recruit_info().has_job_id())) {
      session_data_->dot_perf->Count(
        1, "recruit_similar_pos",
        "fill_response_preview", "field_miss", "job_address");
      return;
    }
    // 当前请求 职位 id x 3
    for (int i = 0; i < 3; ++i) {
      ad_data_v2.add_job_id_list(ad_request_->recruit_info().job_id());
    }
    session_data_->dot_perf->Count(
      1, "recruit_similar_pos",
      "fill_response_preview", "success_ad_num");
    return;
  }
  // 从 rank 中透传结果
  auto const * const ad_rank_result = GetAdRankResult(session_data_, *ad_result_);
  if (KS_UNLIKELY(
      ad_rank_result == nullptr ||
      !ad_rank_result->has_ad_rank_trans_info())) {
    return;
  }
  ad_data_v2.mutable_job_id_list()->CopyFrom(
    ad_rank_result->ad_rank_trans_info().job_id_list());
  session_data_->dot_perf->Interval(
      ad_data_v2.job_id_list_size(), "recruit_similar_pos",
      "fill_response", "job_id_list_size");
}

void AdStyleMgr::SetBizInfo() {
  auto biz_info = ad_data_v2.mutable_biz_info();
  biz_info->set_kwai_book_id(forward_index_item_->unit_support_info().kwai_book_id());
}

void AdStyleMgr::SetPhotoSource() {
  ad_result_->mutable_ad_deliver_info()->mutable_ad_base_info()->set_photo_source(
    forward_index_item_->photo_status().photo_source());
}
}  // namespace front_server
}  // namespace ks
