#include "teams/ad/ad_rank_search/processor/factor/mcb_strategy/mcb_strategy.h"

#include <algorithm>
#include <vector>
#include <tuple>
#include <set>
#include <string>
#include <unordered_map>

#include "teams/ad/ad_proto/kuaishou/ad/pid_tag_enum.pb.h"
#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/ad_rank_search/common/context_data.h"
#include "teams/ad/ad_rank_search/processor/factor/ranking_data.h"
#include "teams/ad/ad_rank_search/processor/factor/billing_separate.h"
#include "teams/ad/ad_rank_search/data/p2p_data/account_id_2_cash_rate/account_id_2_cash_rate_p2p.h"
#include "teams/ad/ad_rank_search/default/params/calc_benefit_param.h"
#include "teams/ad/ad_rank_search/utils/utility/calc_benefit_util.h"
#include "teams/ad/ad_rank_search/utils/kconf/kconf.h"
#include "teams/ad/ad_rank_search/utils/spdm/spdm_switches.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/ad_base/src/common/common.h"

DECLARE_bool(is_adrank_offline_diff_test);

namespace ks {
namespace ad_rank {
using ks::engine_base::PredictType;

bool MCBStrategy::Process() {
    return true;
}

void MCBStrategy::InitMCBParams(ContextData* context_data) {
  mcb_params.is_rewarded = context_data->get_is_rewarded();
  mcb_params.mcb_conv_nextstay_ratio = RankKconfUtil::mcbConvNextstayRatio();
  mcb_params.lps_super_deep_factor = RankKconfUtil::mcbLpsSuperDeepFactor();
  mcb_params.roas_twin_conv_upper_bound = RankKconfUtil::roasTwinConvLtvBidUpperBound();
  mcb_params.rewarded_keyaction_discount = RankKconfUtil::mcbRewardedKeyActionDiscount();
  mcb_params.search_discount = RankKconfUtil::mcbSearchDiscount();
  mcb_params.enable_auto_bid = true;
  mcb_params.mcb_calibration_min_rate =
      context_data->get_spdm_ctx().TryGetDouble("mcb_calibration_min_rate", 0.6);
  mcb_params.mcb_calibration_max_rate =
      context_data->get_spdm_ctx().TryGetDouble("mcb_calibration_max_rate", 1.5);
  // 自动调参相关开关
  mcb_params.stable_cpm_zoom_factor_value =
      context_data->get_spdm_ctx().TryGetDouble("stable_cpm_zoom_factor_value", 1.0);
  mcb_params.lower_cpm_zoom_factor_boundary = RankKconfUtil::lowerCpmZoomFactorBoundary();
  mcb_params.upper_cpm_zoom_factor_boundary = RankKconfUtil::upperCpmZoomFactorBoundary();
  mcb_params.revenue_optimize_cost_constraint =
      context_data->get_spdm_ctx().TryGetDouble("revenue_optimize_cost_constraint", 1.0);
  if (context_data->get_pos_manager_base().request_imp_infos.size() >0) {
    pos_id = context_data->get_pos_manager_base().request_imp_infos[0].pos_id;
  }
  static std::set<int> inspire_set_for_play = {22816, 22817};
  is_inspire_pos_id = (inspire_set_for_play.find(pos_id) != inspire_set_for_play.end());
  // 深度中台模型实验参数
  mcb_params.deep_middle_model_ensemble_rate = RankKconfUtil::deepMiddleModelEnsambleRate();
  mcb_params.enable_deep_middle_model_exp = SPDM_enable_deep_middle_model_exp(context_data->get_spdm_ctx());
  mcb_params.enable_deep_middle_model_kac_exp =
      SPDM_enable_deep_middle_model_kac_exp(context_data->get_spdm_ctx());
  mcb_params.is_single_col = context_data->get_is_thanos_request();

  // 关键行为 2.0 实验参数
  mcb_params.enable_key_action2_dsp = SPDM_enable_key_action2_dsp(context_data->get_spdm_ctx());
  mcb_params.key_action_ltv0_gamma =
      context_data->get_spdm_ctx().TryGetDouble("key_action_ltv0_gamma", 1.0);
  mcb_params.key_action_new_gamma =
      context_data->get_spdm_ctx().TryGetDouble("key_action_new_gamma", 1.0);
  mcb_params.key_action_cold_start_extreme_gamma =
      context_data->get_spdm_ctx().TryGetDouble("key_action_cold_start_extreme_gamma", 0.5);
  mcb_params.key_action_retention_gamma =
      context_data->get_spdm_ctx().TryGetDouble("key_action_retention_gamma", 1.0);
  mcb_params.key_action_account_map = RankKconfUtil::keyActionAccountMap();
  mcb_params.key_action_account_map2 = RankKconfUtil::keyActionAccountMap2();
  mcb_params.key_action_cold_start_extreme_account_map =
      RankKconfUtil::keyActionColdStartExtremeAccountMap();
  mcb_params.key_action_retention_account_map = RankKconfUtil::keyActionRetentionAccountMap();
  // mcb_params.search_inspire_weight_map = RankKconfUtil::mcbEcpcCalibrationInit();
  mcb_params.search_inspire_weight_map = RankKconfUtil::productOcpxSearchAndInspireWeight();
  mcb_params.key_action_post_target_roi_product_map = RankKconfUtil::keyActionPostTargetRoi();
  mcb_params.merge_bid_account_tail = RankKconfUtil::mergeBidAccountIdTail();
  mcb_params.skip_discount_ratio = RankKconfUtil::skipDiscountRatio();
  mcb_params.purchase_roas_fuse_conf_ptr = RankKconfUtil::purchaseRoiFuseConf();
  mcb_params.roas_twin_deep_upper_bound = RankKconfUtil::roasTwinDeepLtvBidUpperBound();
  mcb_params.seven_days_pay_times_account_set_ = RankKconfUtil::sevenDaysPayTimesAccountSet();
  mcb_params.account_event_7day_set_ = RankKconfUtil::accountEvent7DaySet();
  mcb_params.pay_times_alpha = SPDM_pay_times_weight_1d(context_data->get_spdm_ctx());
  mcb_params.pay_times_beta = SPDM_pay_times_weight_2and7(context_data->get_spdm_ctx());
  mcb_params.enable_long_ratio_pay_times_2and7 =
      SPDM_enable_long_ratio_pay_times_2and7(context_data->get_spdm_ctx());
  mcb_params.upper_r_long_ratio_7d_repurchase_param =
      SPDM_upper_r_long_ratio_7d_repurchase_param(context_data->get_spdm_ctx());
  // 七日 ROAS 系列
  mcb_params.enable_pay_amount_2and7 = SPDM_enable_pay_amount_2and7(context_data->get_spdm_ctx());
  mcb_params.pay_amount_alpha = SPDM_pay_amount_weight_1d(context_data->get_spdm_ctx());
  mcb_params.pay_amount_beta = SPDM_pay_amount_weight_2and7(context_data->get_spdm_ctx());
  mcb_params.enable_long_ratio_seven_day_roas = SPDM_enable_long_ratio_seven_day_roas(context_data->get_spdm_ctx());  // NOLINT
  mcb_params.upper_origin_pay_amount_7d = SPDM_upper_origin_pay_amount_7d(context_data->get_spdm_ctx());

  // 首日 ROI 系列
  mcb_params.enable_long_ratio_ad_roas = SPDM_enable_long_ratio_ad_roas(context_data->get_spdm_ctx());
  mcb_params.enable_mini_game_billing_seperate =
           SPDM_enable_mini_game_billing_seperate(context_data->get_spdm_ctx());
  mcb_params.iap_game_twin_bid_group_tag =
           SPDM_iap_game_twin_bid_group_tag(context_data->get_spdm_ctx());
  mcb_params.enable_iap_7d_value_allocate =
           SPDM_enable_iap_7d_value_allocate(context_data->get_spdm_ctx());
  mcb_params.enable_iap_7d_value_allocate_v2 =
           SPDM_enable_iap_7d_value_allocate_v2(context_data->get_spdm_ctx());
  mcb_params.enable_iap_7d_use_auto_roas =
           SPDM_enable_iap_7d_use_auto_roas(context_data->get_spdm_ctx());
  mcb_params.iap_long_value_target_ratio =
           SPDM_iap_long_value_target_ratio(context_data->get_spdm_ctx());
  mcb_params.final_iap_7r_boost_ratio =
           SPDM_final_iap_7r_boost_ratio(context_data->get_spdm_ctx());
  mcb_params.enable_iap_twin_bid_divide_ocpc =
           SPDM_enable_iap_twin_bid_divide_ocpc(context_data->get_spdm_ctx());
  mcb_params.iap_twin_bid_default_long_value_iap_ratio =
           SPDM_iap_twin_bid_default_long_value_iap_ratio(context_data->get_spdm_ctx());
  mcb_params.iap_twin_bid_default_long_value_iaap_ratio =
           SPDM_iap_twin_bid_default_long_value_iaap_ratio(context_data->get_spdm_ctx());
  mcb_params.iap_twin_bid_default_long_value_boost_ratio =
           SPDM_iap_twin_bid_default_long_value_boost_ratio(context_data->get_spdm_ctx());
  mcb_params.iap_game_long_value_ratio_lower_bound =
           SPDM_iap_game_long_value_ratio_lower_bound(context_data->get_spdm_ctx());
  mcb_params.iap_game_long_value_ratio_upper_bound =
           SPDM_iap_game_long_value_ratio_upper_bound(context_data->get_spdm_ctx());
  mcb_params.iap_twin_bid_decay_weight =
           SPDM_iap_twin_bid_decay_weight(context_data->get_spdm_ctx());
  mcb_params.iap_twin_bid_req_threshold =
           SPDM_iap_twin_bid_req_threshold(context_data->get_spdm_ctx());
  mcb_params.enable_iap_twin_bid_unify_post_ratio =
           SPDM_enable_iap_twin_bid_unify_post_ratio(context_data->get_spdm_ctx());
  mcb_params.default_moving_iap_game_long_value_ratio =
           SPDM_default_moving_iap_game_long_value_ratio(context_data->get_spdm_ctx());
  mcb_params.enable_iap_7d_value_twin_bid =
           SPDM_enable_iap_7d_value_twin_bid(context_data->get_spdm_ctx());
  mcb_params.enable_iap_7d_value_twin_bid_v2 =
           SPDM_enable_iap_7d_value_twin_bid_v2(context_data->get_spdm_ctx());
  mcb_params.enable_iap_7r_allocate =
           SPDM_enable_iap_7r_allocate(context_data->get_spdm_ctx());
  mcb_params.final_iap_twin_bid_ratio_upper_bound =
           SPDM_final_iap_twin_bid_ratio_upper_bound(context_data->get_spdm_ctx());
  mcb_params.final_iap_twin_bid_ratio_lower_bound =
           SPDM_final_iap_twin_bid_ratio_lower_bound(context_data->get_spdm_ctx());
  mcb_params.final_iap_twin_bid_boost_ratio =
           SPDM_final_iap_twin_bid_boost_ratio(context_data->get_spdm_ctx());
  mcb_params.enable_iap_billing_seperate = SPDM_enable_iap_billing_seperate(context_data->get_spdm_ctx());
  mcb_params.mini_game_billing_seperate_ratio =
           SPDM_mini_game_billing_seperate_ratio(context_data->get_spdm_ctx());
  mcb_params.iap_billing_seperate_ratio = SPDM_iap_billing_seperate_ratio(context_data->get_spdm_ctx());

  mcb_params.enable_playlet_iap_cut_point_to_click =
      SPDM_enable_playlet_iap_cut_point_to_click(context_data->get_spdm_ctx());
  mcb_params.playlet_calibration_ratio_max = SPDM_playlet_calibration_ratio_max(context_data->get_spdm_ctx());
  mcb_params.enable_outer_u_cvr =
            SPDM_enable_outer_u_cvr(context_data->get_spdm_ctx());
  mcb_params.upper_ad_roas_long_ratio_pay_amount_7d =
      SPDM_upper_ad_roas_long_ratio_pay_amount_7d(context_data->get_spdm_ctx());
  mcb_params.enable_roas_twin_game_ad_mcb_node =
      SPDM_enable_roas_twin_game_ad_mcb_node(context_data->get_spdm_ctx());
  mcb_params.disable_roas_twin_game_ecpc =
      SPDM_disable_roas_twin_game_ecpc(context_data->get_spdm_ctx());
  mcb_params.enable_long_ratio_roas_twin_game_ad =
      SPDM_enable_long_ratio_roas_twin_game_ad(context_data->get_spdm_ctx());
  mcb_params.enable_uax_aigc_bid_calibration =
      SPDM_enable_uax_aigc_bid_calibration(context_data->get_spdm_ctx());
  // cid roi
  mcb_params.enable_cid_ad_bid_mcb =
      SPDM_enable_cid_ad_bid_mcb(context_data->get_spdm_ctx());
  // 长线纠偏系数
  mcb_params.enable_seven_day_longratio_cali_ratio =
      SPDM_enable_seven_day_longratio_cali_ratio(context_data->get_spdm_ctx());
  mcb_params.seven_day_longratio_cali_ratio = SPDM_seven_day_longratio_cali_ratio(context_data->get_spdm_ctx());  // NOLINT
  mcb_params.enable_ad_roas_longratio_cali_ratio =
      SPDM_enable_ad_roas_longratio_cali_ratio(context_data->get_spdm_ctx());
  mcb_params.ad_roas_longratio_cali_ratio = SPDM_ad_roas_longratio_cali_ratio(context_data->get_spdm_ctx());
  mcb_params.enable_bs_cover_all_industry_live_type =
      SPDM_enable_bs_cover_all_industry_live_type(context_data->get_spdm_ctx());
  mcb_params.enable_bs_live_flow = SPDM_enable_bs_live_flow(context_data->get_spdm_ctx());
  mcb_params.enable_white_account_skip_account_bidding =
      SPDM_enable_white_account_skip_account_bidding(context_data->get_spdm_ctx());
  mcb_params.enable_white_product_skip_account_bidding =
      SPDM_enable_white_product_skip_account_bidding(context_data->get_spdm_ctx());
  mcb_params.ab_skip_account = RankKconfUtil::skipAccountBiddingWhiteAccount();
  mcb_params.ab_skip_product = RankKconfUtil::skipAccountBiddingWhiteProduct();
  mcb_params.playlet_roi_formula_account_tail = RankKconfUtil::playletROIFormulaAccountTail();
  std::string user_tag = context_data->get_rank_request()->ad_request().ad_user_info().duanju_plot();
  if (user_tag.size() <= 0 || user_tag.empty() || user_tag == "5") {
    mcb_params.playlet_ltv_default_value = SPDM_playlet_ltv_low_bound(context_data->get_spdm_ctx());
  } else {
    mcb_params.playlet_ltv_default_value = SPDM_playlet_ltv_default_value(context_data->get_spdm_ctx());
  }
  mcb_params.playlet_ltv_low_bound = SPDM_playlet_ltv_low_bound(context_data->get_spdm_ctx());
  mcb_params.playlet_ltv_up_bound = SPDM_playlet_ltv_up_bound(context_data->get_spdm_ctx());
  // 主站发现页内流曝光系数移除
  mcb_params.enable_explore_inner_sctr_migrate =
      SPDM_enable_explore_inner_sctr_migrate(context_data->get_spdm_ctx());
  mcb_params.explore_inner_sctr_migrate_lower =
      SPDM_explore_inner_sctr_migrate_lower(context_data->get_spdm_ctx());
  mcb_params.explore_inner_sctr_migrate_upper =
      SPDM_explore_inner_sctr_migrate_upper(context_data->get_spdm_ctx());
  mcb_params.fix_hard_unify_sctr_click =
      SPDM_fix_hard_unify_sctr_click(context_data->get_spdm_ctx());
  // 游戏
  mcb_params.game_iaa_roi7_deep_roi_coef =
      SPDM_game_iaa_roi7_deep_roi_coef(context_data->get_spdm_ctx());
  mcb_params.game_iaa_roi7_deep_pacing_coef =
      SPDM_game_iaa_roi7_deep_pacing_coef(context_data->get_spdm_ctx());
  mcb_params.game_iaa_roi7_deep_min_shallow_coef =
      SPDM_game_iaa_roi7_deep_min_shallow_coef(context_data->get_spdm_ctx());
  mcb_params.game_iaa_roi7_deep_min_deep_coef =
      SPDM_game_iaa_roi7_deep_min_deep_coef(context_data->get_spdm_ctx());
  std::string minigame_bidding_strategy_type_str =
    SPDM_minigame_bidding_strategy_type(context_data->get_spdm_ctx());
  minigame_bidding_strategy_type =
    GetStrategyTypeFromString(minigame_bidding_strategy_type_str);
  mcb_params.game_iaa_deep_avg_pacing_alpha =
      SPDM_game_iaa_deep_avg_pacing_alpha(context_data->get_spdm_ctx());
  mcb_params.game_iaa_deep_avg_pacing_beta =
      SPDM_game_iaa_deep_avg_pacing_beta(context_data->get_spdm_ctx());
}

void MCBStrategy::UpdateAdMCBParams(const AdCommon& ad,
              const ContextData* context_data, ks::engine_base::MCBParams *p_mcb_params) {
  mcb_params.budget_coef = ad.get_budget_coef() > 0.0 ? ad.get_budget_coef() : 1.0;
  mcb_params.auto_cpa_bid = ad.get_auto_cpa_bid() > 0 ? ad.get_auto_cpa_bid() :
    ad.get_product_cpa_bid() / mcb_params.budget_coef;
  // p+q  预计算，性能优化
  mcb_params.bid_strategy_group = ad.get_bid_strategy_group();
}

static void AdjustButtonClickAlphaBeta(ContextData* context_data, Params* params, AdCommon* p_ad,
    double* alpha, double* beta, double* hour) {
  if (nullptr == alpha || nullptr == beta || nullptr == hour ||
        nullptr == p_ad || nullptr == params || nullptr == context_data) {
    return;
  }
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (nullptr == params_) {
    return;
  }
  const auto& non_merchant_params = params_->non_merchant_live_promote_params;
  int64 live_start_ts = std::max(p_ad->get_live_start_ts(), p_ad->get_live_release_time() *  1000);
  int64 now_time = context_data->get_current_timestamp_nodiff() / 1000;
  int64 live_start_hour = (now_time - live_start_ts) / 3600000;
  p_ad->Attr(ItemIdx::button_click_twin_live_start_hour).SetIntValue(
    p_ad->AttrIndex(), live_start_hour, false, false);
  double a = *alpha;
  double b = *beta;
  double button_click_twin_bid_hour = *hour;
  if (non_merchant_params.enable_button_click_twin_bid_v1) {
    double k = (1 - a) / button_click_twin_bid_hour;
    a = k * live_start_hour + a;
    a = std::min(std::max(1.0, a), non_merchant_params.button_click_twin_bid_alpha_max);
  } else if (non_merchant_params.enable_button_click_twin_bid_v2) {
    double k = - a / button_click_twin_bid_hour;
    a = k * live_start_hour + a;
    a = std::min(1.0, std::max(non_merchant_params.button_click_twin_bid_alpha_min, a));
  }
  *alpha = a;
  *beta = b;
}

static void OuterLivePlaytimeStrategy(
    ks::engine_base::MCBParams* p_mcb_params,
    Params* params,
    ContextData* context_data,
    AdCommon* p_ad,
    double* p_auction_bid) {
  if (nullptr == p_mcb_params || nullptr == context_data ||
        nullptr == p_ad || nullptr == p_auction_bid ||
        nullptr == params) {
    return;
  }
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (nullptr == params_) {
    return;
  }

  if (!p_ad->Is(AdFlag::is_industry_live)) {
    return;
  }

  const auto& non_merchant_params = params_->non_merchant_live_promote_params;

  if ((non_merchant_params.enable_outer_live_playtime_strategy)) {
    RANK_DOT_COUNT(context_data, 1, "ad_rank.industry_live_playtime_strategy",
      kuaishou::ad::AdEnum_BidType_Name(p_ad->get_bid_type()),
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
  } else {
    return;
  }

  // 尾号实验
  if (non_merchant_params.enable_outer_live_id_tail_number) {
    auto live_id = p_ad->get_live_stream_id();
    int tail_num = live_id - (live_id / 10) * 10;
    auto outer_live_id_tail_number_set = non_merchant_params.outer_live_id_tail_number_set;
    if (outer_live_id_tail_number_set->find(tail_num) == outer_live_id_tail_number_set->end()) {
        return;
    }
    // LOG_EVERY_N(INFO, 1)<<"lhq_debug1" << "live_id:" << live_id << "\ttail:" << tail_num;
  }

  if (nullptr == non_merchant_params.outer_live_playtime_strategy_config) {
    return;
  }

  // 设置生效 ocpc
  if (non_merchant_params.enable_outer_live_ocpc_conf) {
    auto& ocpc_action_type_set = non_merchant_params.outer_live_playtime_strategy_config->
      data().ocpc_action_type_set();
    if (std::find(ocpc_action_type_set.begin(), ocpc_action_type_set.end(), p_ad->get_ocpx_action_type())
       == ocpc_action_type_set.end()) {
      return;
    }
  }

  double playtime_up = non_merchant_params.playtime_up_default;
  double alpha_up = non_merchant_params.playtime_alpha_up_default;
  double beta_up = non_merchant_params.playtime_beta_up_default;
  double beta_low = non_merchant_params.playtime_beta_low_default;
  double lambda = non_merchant_params.playtime_lambda_default;
  double decay_hour = non_merchant_params.playtime_decay_hour_default;
  double playtime_threshold = non_merchant_params.playtime_threshold_default;
  std::string formula = non_merchant_params.playtime_formula_default;

  // 设置总体实验配置参数
  if (non_merchant_params.outer_live_playtime_strategy_exp_name != "") {
    auto& exp_configs = non_merchant_params.outer_live_playtime_strategy_config->
      data().exp_config();
    if (!exp_configs.empty()) {
      auto exp_config_itr = exp_configs.find(non_merchant_params.outer_live_playtime_strategy_exp_name);
      if (exp_config_itr != exp_configs.end()) {
          playtime_up = exp_config_itr->second.playtime_up();
          alpha_up = exp_config_itr->second.alpha_up();
          beta_up = exp_config_itr->second.beta_up();
          beta_low = exp_config_itr->second.beta_low();
          lambda = exp_config_itr->second.lambda();
          decay_hour = exp_config_itr->second.decay_hour();
          playtime_threshold = exp_config_itr->second.playtime_threshold();
          formula = exp_config_itr->second.formula();
      }
    }
  }

  // 分 ocpc 配置实验参数
  if (non_merchant_params.enable_outer_live_ocpc_conf &&
      non_merchant_params.outer_live_playtime_strategy_exp_name != "") {
    auto& ocpc_config = non_merchant_params.outer_live_playtime_strategy_config->
      data().ocpc_config();
    if (!ocpc_config.empty()) {
        auto ocpc_config_itr = ocpc_config.find(non_merchant_params.outer_live_playtime_strategy_exp_name);
        if (ocpc_config_itr != ocpc_config.end()) {
            auto& ocpc_conf = ocpc_config_itr->second.exp_config();
            std::string ocpc_type = absl::StrCat(p_ad->get_ocpx_action_type());
            auto config_iter = ocpc_conf.find(ocpc_type);
            if (config_iter != ocpc_conf.end()) {
              playtime_up = config_iter->second.playtime_up();
              alpha_up = config_iter->second.alpha_up();
              beta_up = config_iter->second.beta_up();
              beta_low = config_iter->second.beta_low();
              lambda = config_iter->second.lambda();
              decay_hour = config_iter->second.decay_hour();
              playtime_threshold = config_iter->second.playtime_threshold();
              formula = config_iter->second.formula();
            }
        }
    }
  }

  double auction_bid = *p_auction_bid;
  double predict_playtime = p_ad->get_predict_score(PredictType::PredictType_playtime);
  double ctr = p_ad->get_predict_score(PredictType::PredictType_non_merchant_live_ctr);
  double cvr = p_ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
  double sctr =  p_ad->get_predict_score(PredictType::PredictType_non_merchant_live_sctr);

  double predict_playtime_norm =  predict_playtime / (playtime_up + 0.1);
  double auction_bid_playtime = predict_playtime_norm * auction_bid / (cvr + 1e-8);


  // 预估时长
  p_ad->Attr(ItemIdx::button_click_twin_button_ctr).SetDoubleValue(
    p_ad->AttrIndex(), predict_playtime, false, false);
  p_ad->Attr(ItemIdx::button_click_twin_auction_bid_median).SetDoubleValue(
    p_ad->AttrIndex(), auction_bid_playtime, false, false);
  p_ad->Attr(ItemIdx::button_click_twin_auction_bid_origin).SetDoubleValue(
    p_ad->AttrIndex(), auction_bid, false, false);

  if (formula == "v2" && non_merchant_params.enable_outer_live_ocpc_conf) {
    auto  cpa_bid = p_ad->get_auto_cpa_bid();
    double playtime_bid = predict_playtime_norm;
    if (sctr > 1e-10) {
        playtime_bid *= sctr;
    }
    if (ctr > 1e-10) {
        playtime_bid *= ctr;
    }
    auction_bid_playtime = playtime_bid * cpa_bid;
  }

  // 计算时长的系数
  int64 live_start_ts = std::max(p_ad->get_live_start_ts(), p_ad->get_live_release_time() *  1000);
  int64 now_time = context_data->get_current_timestamp_nodiff() / 1000;
  double live_start_hour = 1.0 * (now_time - live_start_ts) / 3600000;

  // 开播时长
  p_ad->Attr(ItemIdx::button_click_twin_price_ratio).SetDoubleValue(
    p_ad->AttrIndex(), live_start_hour, false, false);

  if (predict_playtime < playtime_threshold && non_merchant_params.enable_outer_live_ocpc_conf) {
    return;
  }

  double k = alpha_up / (decay_hour + 0.01);
  double alpha = std::max(alpha_up - k * live_start_hour, 0.0);
  double auction_bid_new = auction_bid;
  auction_bid_new = alpha * lambda * auction_bid_playtime + (1 - alpha) *auction_bid;
  auction_bid_new = std::min(std::max(auction_bid_new, beta_low * auction_bid), beta_up * auction_bid);
  *p_auction_bid = auction_bid_new;

  p_ad->Attr(ItemIdx::button_click_twin_alpha).SetDoubleValue(
    p_ad->AttrIndex(), alpha, false, false);
  p_ad->Attr(ItemIdx::button_click_twin_auction_bid).SetDoubleValue(
    p_ad->AttrIndex(), auction_bid_new, false, false);

  // LOG_EVERY_N(INFO, 1) << "OuterLivePlaytimelhq\t" << p_ad->get_bid_type() << "\t" <<
  //   p_ad->get_ocpx_action_type() << "\tauction_bid:" << auction_bid << "\tauction_bid_new:" <<
  //   auction_bid_new <<"\tplaytime:" << predict_playtime << "," << predict_playtime_norm<< "\tcvr:"
  //   << cvr << "\talpha:"<<alpha<<"\tauction_bid_playtime:"<<auction_bid_playtime<<","<<
  //   ctr*predict_playtime*cpa_bid<<"\texp_name:"<< non_merchant_params.outer_live_playtime_strategy_exp_name
  //   <<"\tlambda:" << lambda << "\tbeta:" << beta_low<<","<<beta_up << "\tdecay_hour:" << decay_hour <<
  //   "\talpha_up:" << alpha_up<< "\tlive_start_hour:" << live_start_hour;
}

static void OuterLiveButtonClickTwin(
    ks::engine_base::MCBParams* p_mcb_params,
    Params* params,
    ContextData* context_data,
    AdCommon* p_ad,
    double* p_auction_bid) {
  if (nullptr == p_mcb_params || nullptr == context_data ||
        nullptr == p_ad || nullptr == p_auction_bid ||
        nullptr == params) {
    return;
  }
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (nullptr == params_) {
    return;
  }

  if (!p_ad->Is(AdFlag::is_industry_live)) {
    return;
  }

  const auto& non_merchant_params = params_->non_merchant_live_promote_params;

  if ((non_merchant_params.enable_outer_live_twin_bid_flow &&
        p_ad->get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP) ||
      (non_merchant_params.enable_button_click_twin_bid_mcb_strategy &&
        p_ad->get_bid_type() == kuaishou::ad::AdEnum::MCB) ||
      (non_merchant_params.enable_button_click_twin_bid_strategy_with_param_kconf)) {
    RANK_DOT_COUNT(context_data, 1, "ad_rank.industry_live_buttion_click_strategy",
      kuaishou::ad::AdEnum_BidType_Name(p_ad->get_bid_type()),
      kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()),
      non_merchant_params.enable_button_click_twin_bid_strategy_with_param_kconf?
        absl::StrCat(p_ad->get_account_id()):"-1");
  } else {
    return;
  }

  if (!(non_merchant_params.enable_outer_live_twin_bid_flow ||
          non_merchant_params.outer_live_twin_bid_account_white->count(p_ad->get_account_id()) > 0)) {
    return;
  }

  if (non_merchant_params.outer_live_twin_bid_ocpx_set->find(p_ad->get_ocpx_action_type()) ==
        non_merchant_params.outer_live_twin_bid_ocpx_set->end()) {
    return;
  }
  auto iter = non_merchant_params.button_click_account_cpa_map->find(p_ad->get_account_id());
  if (iter == non_merchant_params.button_click_account_cpa_map->end()) {
    return;
  }

  // 使用 kconf 中 alpha beta bid 数据
  if (non_merchant_params.enable_button_click_twin_bid_strategy_with_param_kconf && nullptr ==
    non_merchant_params.button_click_account_params_config_map) {
    return;
  }

  int64_t cpa_click = iter->second;
  double auction_bid = *p_auction_bid;
  double ctr = p_ad->get_predict_score(PredictType::PredictType_non_merchant_live_ctr);
  double button_ctr = p_ad->get_predict_score(PredictType::PredictType_non_merchant_live_button_click);
  double price_ratio = non_merchant_params.button_click_cpa_price_ratio;
  double auction_bid_click = 0.0;
  if (non_merchant_params.button_click_simple_room_begin) {
    // 曝光作为起点
    auction_bid_click = cpa_click * button_ctr * price_ratio;
  } else {
    // 进人作为起点
    auction_bid_click = cpa_click * ctr * button_ctr * price_ratio;
  }

  // 参数默认值，参数默认值下，策略等同于不生效 (仅针对现有策略 2 成立)
  double alpha = non_merchant_params.button_click_twin_bid_alpha_default;
  double beta = non_merchant_params.button_click_twin_bid_beta_default;
  double hour = non_merchant_params.button_click_twin_bid_hour_default;

  // 如果策略全面覆盖，那么参数为实验参数指定值
  if (non_merchant_params.enable_outer_live_twin_bid_flow ||
      non_merchant_params.enable_button_click_twin_bid_mcb_strategy) {
    alpha = non_merchant_params.button_click_twin_bid_alpha;
    beta = non_merchant_params.button_click_twin_bid_beta;
    hour = non_merchant_params.button_click_twin_bid_hour;
  }

  // 圈出价配置策略 kconf ： outerLiveDspComponentClickActionTypeConfig
  // 圈出价配置下，策略参数被替换
  if (non_merchant_params.enable_button_click_twin_bid_strategy_with_action_kconf && nullptr !=
    non_merchant_params.button_click_action_params_config_map) {
    auto& exp_configs = non_merchant_params.button_click_action_params_config_map->
      data().exp_config();
    if (!exp_configs.empty()) {
      auto exp_config_itr = exp_configs.find(non_merchant_params.button_click_twin_bid_action_exp_name);
      if (exp_config_itr != exp_configs.end()) {
        auto& action_configs = exp_config_itr->second.action_config();
        std::string action_type = absl::StrCat(p_ad->get_ocpx_action_type());
        auto config_iter = action_configs.find(action_type);
        if (config_iter != action_configs.end()) {
          alpha = config_iter->second.alpha();
          beta = config_iter->second.beta();
          hour = config_iter->second.hour();
        }
      }
    }
  }

  // 圈客户策略 kconf : outerLiveDspComponentClickConfig
  // 圈客户配置下，策略参数被替换
  if (non_merchant_params.enable_button_click_twin_bid_strategy_with_param_kconf) {
    auto& exp_configs = non_merchant_params.button_click_account_params_config_map->
      data().exp_config();
    if (!exp_configs.empty()) {
      auto exp_config_itr = exp_configs.find(non_merchant_params.button_click_twin_bid_exp_name);
      if (exp_config_itr != exp_configs.end()) {
        auto& account_configs = exp_config_itr->second.account_config();
        std::string account_id_bid_type = absl::StrCat(p_ad->get_account_id(),
          "_", p_ad->get_bid_type());
        auto config_iter = account_configs.find(account_id_bid_type);
        if (config_iter != account_configs.end()) {
          alpha = config_iter->second.alpha();
          beta = config_iter->second.beta();
          hour = config_iter->second.hour();
        }
      }
    }
  }

  p_ad->Attr(ItemIdx::button_click_twin_alpha_origin).SetDoubleValue(
    p_ad->AttrIndex(), alpha, false, false);
  p_ad->Attr(ItemIdx::button_click_twin_beta_origin).SetDoubleValue(
    p_ad->AttrIndex(), beta, false, false);
  p_ad->Attr(ItemIdx::button_click_twin_ctr).SetDoubleValue(
    p_ad->AttrIndex(), ctr, false, false);
  p_ad->Attr(ItemIdx::button_click_twin_cpa_click).SetIntValue(
    p_ad->AttrIndex(), cpa_click, false, false);

  if (!non_merchant_params.enable_outer_live_playtime_strategy) {
    p_ad->Attr(ItemIdx::button_click_twin_button_ctr).SetDoubleValue(
      p_ad->AttrIndex(), button_ctr, false, false);
    p_ad->Attr(ItemIdx::button_click_twin_auction_bid_origin).SetDoubleValue(
      p_ad->AttrIndex(), auction_bid, false, false);
  }

  AdjustButtonClickAlphaBeta(context_data, params, p_ad, &alpha, &beta, &hour);

  double auction_bid_new = auction_bid;
  if (non_merchant_params.enable_button_click_twin_bid_v1) {
    auction_bid_new = std::min(std::max(alpha * auction_bid_click, auction_bid), beta * auction_bid);
  } else if (non_merchant_params.enable_button_click_twin_bid_v2) {
    auction_bid_new = std::min(alpha * auction_bid_click + (1 - alpha) * auction_bid, beta * auction_bid);
  }

  auction_bid = auction_bid_new;
  *p_auction_bid = auction_bid;
  if (!non_merchant_params.enable_outer_live_playtime_strategy) {
    p_ad->Attr(ItemIdx::button_click_twin_auction_bid_median).SetDoubleValue(
      p_ad->AttrIndex(), auction_bid, false, false);
    p_ad->Attr(ItemIdx::button_click_twin_alpha).SetDoubleValue(
      p_ad->AttrIndex(), alpha, false, false);
    p_ad->Attr(ItemIdx::button_click_twin_auction_bid).SetDoubleValue(
      p_ad->AttrIndex(), auction_bid_click, false, false);
    p_ad->Attr(ItemIdx::button_click_twin_price_ratio).SetDoubleValue(
      p_ad->AttrIndex(), price_ratio, false, false);
  }

  p_ad->Attr(ItemIdx::button_click_twin_beta).SetDoubleValue(
    p_ad->AttrIndex(), beta, false, false);
}

static void OuterLiveBS(
    ks::engine_base::MCBParams* p_mcb_params,
    Params* params,
    ContextData* context_data,
    AdCommon* p_ad,
    double* p_auction_bid) {
  if (nullptr == p_mcb_params || nullptr == context_data ||
        nullptr == p_ad || nullptr == p_auction_bid ||
        nullptr == params) {
    return;
  }
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (nullptr == params_) {
    return;
  }
  auto& mcb_params = *p_mcb_params;
  double &auction_bid = *p_auction_bid;

  if (!p_ad->get_is_account_bidding()) {
    return;
  }

  if (!mcb_params.enable_bs_cover_all_industry_live_type) {
    return;
  }

  if (mcb_params.enable_bs_live_flow ||
        (mcb_params.enable_white_account_skip_account_bidding &&
          nullptr != mcb_params.ab_skip_account &&
          mcb_params.ab_skip_account->count(p_ad->get_account_id())) ||
        (mcb_params.enable_white_product_skip_account_bidding &&
          nullptr != mcb_params.ab_skip_product &&
          mcb_params.ab_skip_product->count(p_ad->get_product_name()))) {
    p_ad->set_mcb_r(mcb_params.r);
    p_ad->set_live_fake_mcb(true);
    params_->billing_separate_strategy_->Process(context_data, p_ad, params, &auction_bid);
    // 曝光系数移除
    if (p_ad->get_ad_queue_type() == kuaishou::ad::AdEnum::HARD_AD_QUEUE) {
      utility::MulHardSctr(context_data, p_ad, mcb_params.enable_explore_inner_sctr_migrate, &auction_bid,
                           "all.all.mcb1", mcb_params.fix_hard_unify_sctr_click);
    }
    RANK_DOT_COUNT(context_data, 1, "ad_rank.industry_live_fake_mcb",
                   kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()));
  }
}

static void OuterLiveAdjustScore(
    ks::engine_base::MCBParams* p_mcb_params,
    Params* params,
    ContextData* context_data,
    AdCommon* p_ad,
    double* p_auction_bid) {
  if (!context_data->get_disable_ecpm_strategy__6_billing_separate()) {
    // 首发
    OuterLiveBS(p_mcb_params, params, context_data, p_ad, p_auction_bid);
  }

  // 组件点击双出价
  OuterLiveButtonClickTwin(p_mcb_params, params, context_data, p_ad, p_auction_bid);

  // 时长策略
  OuterLivePlaytimeStrategy(p_mcb_params, params, context_data, p_ad, p_auction_bid);
}

void MCBStrategy::MCBBenifit(AdCommon *p_ad, ContextData *context_data, Params* params) {
  auto& ad = *p_ad;
  // 1. mcb 参数填充
  UpdateAdMCBParams(ad, context_data, &mcb_params);

  // 2. 执行调价逻辑 & LOG
  double auction_bid = p_ad->get_auction_bid();
  // mcb cpa_bid
  GetMCBCpaBid(ad, context_data, &mcb_params);
  // mcb r
  GetMCBR(ad, context_data, &mcb_params);
  // auction_bid
  auction_bid = mcb_params.CalcMCBAuctionBid(auction_bid);
  // 曝光系数移除
  utility::MulHardSctr(context_data, p_ad, mcb_params.enable_explore_inner_sctr_migrate, &auction_bid,
                       "all.all.mcb", mcb_params.fix_hard_unify_sctr_click);
  double auction_bid_init = auction_bid;
  double server_client_show_rate = 1.0;
  auto* params_ = dynamic_cast<CalcBenefitParams*>(params);
  if (context_data->get_enable_server_show_rate_unify()) {
    server_client_show_rate = ad.get_server_client_show_rate_ocpm();
  } else {
    server_client_show_rate = ad.get_server_client_show_rate_mcb();
    if (params_ != nullptr) {
      double server_client_show_rate_account_ocpm_reconstruct = ad.get_server_client_show_rate_account_ocpm();    // NOLINT
      double server_client_show_rate_account_ocpm_white_reconstruct = ad.get_server_client_show_rate_account_ocpm_white();   // NOLINT

      if (CalcBenefitUtility::IsAccountBiddingSet(*params_, ad)) {
        server_client_show_rate = server_client_show_rate_account_ocpm_white_reconstruct;    // deal
      } else if (ad.get_is_account_bidding()) {
        server_client_show_rate = server_client_show_rate_account_ocpm_reconstruct;
      }
    }
  }

  // 曝光系数迁移 front
  std::string app_id = context_data->get_pos_manager_base().GetRequestAppId();
  if (context_data->get_is_thanos_mix_request() ||
      params_->enable_explore_inner_sctr_migrate && context_data->get_is_explore_feed_inner()) {
    ad.set_server_show_rate(server_client_show_rate);
    if (app_id == "kuaishou") {
      server_client_show_rate = context_data->get_unify_server_show_rate();
    } else if (app_id == "kuaishou_nebula") {
      server_client_show_rate = context_data->get_nebula_unify_server_show_rate();
    }
  }
  // 发现页双列硬广用曝光系数对齐软广
  if (SPDM_enable_explore_feed_hard_unify_server_show_rate(context_data->get_spdm_ctx())
    && context_data->get_sub_page_id() == ********) {
    context_data->set_unify_server_show_rate(
        SPDM_explore_feed_hard_server_show_rate_mcb(context_data->get_spdm_ctx()));
    server_client_show_rate = context_data->get_unify_server_show_rate();
    RANK_DOT_STATS(context_data, server_client_show_rate, "ad_rank.explore_feed",
    "hard_server_show_rate", "mcb");
  }
  // 曝光系数移除
  if (context_data->get_is_explore_feed_inner()) {
    server_client_show_rate *= utility::GetHardSctr1Factor(
        context_data, p_ad, params_->enable_explore_inner_sctr_migrate,
        params_->explore_inner_sctr_migrate_lower, params_->explore_inner_sctr_migrate_upper, "all.all.mcb");
  }
  // 外循环直播半分离
  OuterLiveAdjustScore(&mcb_params, params_, context_data, p_ad, &auction_bid);
  if ((context_data->get_is_default_deploy() ||
       context_data->common_r_->GetIntCommonAttr("is_splash_new_flow_with_ab").value_or(0)) &&
       SPDM_enable_ecpc_max_min(context_data->get_spdm_ctx())) {
    if (SPDM_fix_ecpc_max_min(context_data->get_spdm_ctx())) {
      ad.set_unify_ecpm_ratio(1.0);
    }
    ad.BoostUnifyEcpmRatio(ad.Attr(ItemIdx::ecpc_max_min_ratio).GetDoubleValue(ad.AttrIndex()).value_or(1.0)); // NOLINT
    auction_bid *= ad.get_unify_ecpm_ratio();
  } else {
    // 硬广 ecpc 连乘 框架
    auction_bid *= ad.get_unify_ecpc();
  }

  if (context_data->get_is_search_request() && params_ != nullptr) {
    std::string ocpx_type_str = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
    if (context_data->get_is_search_inner_stream()) {
      // boost 逻辑对齐 - 内流：规整前 auction_bid & unify_ctr 都带 boost，规整后只有 auction_bid 带
      auction_bid *= p_ad->get_search_bid_boost();
      if (SPDM_enable_search_mcb_boost_align(context_data->get_spdm_ctx()) &&
          !SPDM_enable_search_regulate_boost_scope(context_data->get_spdm_ctx())) {
        ad.AdjustUnifyCtrValue(ad.get_unify_ctr_info().value * ad.get_search_bid_boost());
      }
      if (context_data->get_monitor_sample()) {
        RANK_DOT_STATS(context_data, p_ad->get_search_bid_boost() * 1000000,
            "ad_rank.ad_rank_bid_boost_mcb",
          ocpx_type_str, context_data->get_search_interactive_form());
      }
    } else {
      // boost 逻辑对齐 - 外流：规整前都不带 boost，规整后 auction_bid 带
      if (SPDM_enable_search_mcb_boost_align(context_data->get_spdm_ctx()) &&
          SPDM_enable_search_regulate_boost_scope(context_data->get_spdm_ctx())) {
        auction_bid *= p_ad->get_search_bid_boost();
        if (context_data->get_monitor_sample()) {
          RANK_DOT_STATS(context_data, p_ad->get_search_bid_boost() * 1000000,
              "ad_rank.ad_rank_bid_boost_mcb",
            ocpx_type_str, context_data->get_search_interactive_form());
        }
      }
    }
    if (!ks::infra::kenv::IsStressTestFlow()) {
      // 对 auction_bid_init 为 0 的情况进行监控.
      RANK_DOT_COUNT(context_data, 1, "ad_rank.auction_bid_init_cnt",
                     context_data->get_search_rank_exp_name(),
                     context_data->get_search_interactive_form(), "mcb_ad_list");
      // double 能保证 15 位的精度
      if (auction_bid_init < 1e-10) {
        RANK_DOT_COUNT(context_data, 1, "ad_rank.auction_bid_init_zero",
                       context_data->get_search_rank_exp_name(),
                       context_data->get_search_interactive_form(), "mcb_ad_list");
      }
    }
  }
  // 阿里 RTA 外投白名单账户 auction_bid 处理
  if (ad.get_ali_outer_bid_type() == AliOuterBidType::ALI_UNION &&
      ad.get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT) {
    auction_bid = ad.get_ali_deep_cvr() * ad.get_unify_ctr_info().value;
  }
  // ocpm2.0 支持 ecpc
  if (params_ != nullptr) {
    if (params_->enable_ecpc_whitelist_opt) {
      ad.set_ecpc_ratio(ad.get_ecpc_adjust_ratio());
      auction_bid *= ad.get_ecpc_ratio();
    } else if (ad.get_is_account_bidding()) {
      ad.set_ecpc_ratio(ad.get_ecpc_adjust_ratio());
      // 只开放部分
      if (params_->ecpc_ajust_tag_available_set->count(ad.get_ecpc_adjust_tag()) > 0) {
        if (mcb_params.disable_roas_twin_game_ecpc &&
            params_->disable_roas_twin_game_ecpc_tag_set != nullptr &&
            params_->disable_roas_twin_game_ecpc_tag_set->count(ad.get_ecpc_adjust_tag()) > 0 &&
            mcb_params.enable_roas_twin_game_ad_mcb_node && ad.Is(AdFlag::is_roas_twin_game_ad)) {
          auction_bid *= 1.0;
        } else {
          auction_bid *= ad.get_ecpc_ratio();
        }
      } else {
        ad.set_ecpc_ratio(1.0);
        ad.ClearEcpcAdjustRatio();
      }
    } else if (ad.get_bid_type() == kuaishou::ad::AdEnum::MCB) {
      // MCB 放开 ecpc 修改性价比
      ad.set_ecpc_ratio(ad.get_ecpc_adjust_ratio());
      // 只开放部分
      if (params_->ecpc_ajust_tag_available_set_mcb->count(ad.get_ecpc_adjust_tag()) > 0) {
        auction_bid *= ad.get_ecpc_ratio();
      } else {
        ad.set_ecpc_ratio(1.0);
        ad.ClearEcpcAdjustRatio();
      }
    }
  }
  // 万合场景改曝光计费，下掉 server_client_show_rate
  if (params_->enable_wanhe_charge_action_type_subpage_id &&
      context_data->get_pos_manager_base().IsWanhe()) {
    if (!ad.get_is_wanhe_charge_action_type() && p_ad->get_server_show_ctr() > 0) {
      auction_bid = auction_bid / p_ad->get_server_show_ctr();
      ad.set_is_wanhe_charge_action_type(true);
      server_client_show_rate = 1.0;
      RANK_DOT_STATS(context_data, auction_bid * 1000000, "wanhe_ad_rank_charge_type", "MCB_auction");
      RANK_DOT_STATS(context_data, p_ad->get_server_show_ctr() * 1000000,
                     "wanhe_ad_rank_charge_type", "MCB_sctr");
    } else {
      RANK_DOT_STATS(context_data, p_ad->get_server_show_ctr() * 1000000,
                     "wanhe_ad_rank_charge_type", "no_MCB_sctr");
    }
  }
  if (context_data->get_is_incentive() && params_->enable_incentive_sctr_migrate) {
    server_client_show_rate = utility::GetHardSctr1Factor(
      context_data, p_ad, params_->enable_incentive_sctr_migrate,
      1.0, params_->incentive_sctr_migrate_upper, "incentive.all.mcb");
  }
  ad.set_cpm(static_cast<int64>(auction_bid * server_client_show_rate * kBenifitFactor));
  ad.set_origin_cpm(static_cast<int64>(auction_bid * kBenifitFactor));

  // 搜索广告激励
  if (context_data->get_rank_request()->ad_request().search_info().request_source() == 2) {
    // 广告盒子双列任务 set_cpm
    if (context_data->get_pos_manager_base().IsSearchInspireFeedRequest()) {
      if (SPDM_enable_search_inspire_ad_box_boost_optimize(context_data->get_spdm_ctx())) {
        auction_bid = auction_bid_init;
        auction_bid *= CalcBenefitUtility::GetSearchInspireAdBoxBoost(context_data, p_ad);
        ad.set_cpm(static_cast<int64>(auction_bid * server_client_show_rate * kBenifitFactor));
        ad.set_origin_cpm(static_cast<int64>(auction_bid_init * server_client_show_rate * kBenifitFactor));
      }
    }
    // 广告盒子单列任务 set_cpm
    if (context_data->get_pos_manager_base().IsSearchInspireThanosRequest()) {
      auction_bid = auction_bid_init;
      if (SPDM_enable_search_inspire_single_col_boost_optimize(context_data->get_spdm_ctx())) {
        auction_bid *= CalcBenefitUtility::GetSearchInspireAdBoxBoost(context_data, p_ad);
      }
      ad.set_cpm(static_cast<int64>(auction_bid * kBenifitFactor));
      ad.set_origin_cpm(static_cast<int64>(auction_bid_init * kBenifitFactor));
    }
    // 短剧单列任务 set_cpm
    if (context_data->get_pos_manager_base().IsSearchInspireDuanjuThanosRequest()) {
      if (SPDM_enable_search_inspire_duanju_thanos_nebula(context_data->get_spdm_ctx()) ||
            SPDM_enable_search_inspire_duanju_thanos_main(context_data->get_spdm_ctx())) {
        auction_bid = auction_bid_init;
        if (SPDM_enable_search_inspire_duanju_boost_optimize(context_data->get_spdm_ctx())) {
          auction_bid *= CalcBenefitUtility::GetSearchInspireAdBoxBoost(context_data, p_ad);
        }
        ad.set_cpm(static_cast<int64>(auction_bid * kBenifitFactor));
        ad.set_origin_cpm(static_cast<int64>(auction_bid_init * kBenifitFactor));
      }
    }
  }
  // bid_info
  ad.set_precise_auction_bid(auction_bid);
  ad.SetAuctionBid(static_cast<int64>(std::round(auction_bid)), AuctionBidModifyTag::MCBBenifit);
  ad.set_mcb_r(mcb_params.r);
  bool is_roi = (ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
                   ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS);
  ad.set_final_server_client_show_rate(server_client_show_rate);
  if (ad.get_bid_type() == kuaishou::ad::AdEnum::MCB) {
    if (static_cast<int32_t>(ad.get_constraint_roi()) == 5 ||   // ABO_CAP
        static_cast<int32_t>(ad.get_constraint_roi()) == 6) {  // COST_CAP
      if (is_roi && ad.get_roi_ratio() <= 0) {
        ad.set_roi_ratio(ad.get_product_roi_ratio());
      } else if (!is_roi && ad.get_cpa_bid() <= 0) {
        ad.SetCpaBid(ad.get_product_cpa_bid(), ad_base::AutoCpaBidModifyTagType::kNobid);
      }
    } else {
      ad.set_cpa_bid(ad.get_auto_cpa_bid());
    }
  }
  // base
  ad.set_is_server_show_ocpm(true);
  // 产出 auto_cpa_bid
  if (ad.get_bid_strategy_group() == kuaishou::ad::AdEnum::MAX_CONV_STRATEGY) {
    if (ad.get_deep_conversion_type()   // ltv 重写
        == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
       ad.get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
         ad.get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) {
      ad.SetAutoCpaBid(mcb_params.auto_cpa_bid,
                ad_base::AutoCpaBidModifyTagType::kGamePayROI9);
    } else if (ad.get_auto_cpa_bid() <= 0) {
      ad.SetAutoCpaBid(mcb_params.cpa_bid / mcb_params.budget_coef,
        ad_base::AutoCpaBidModifyTagType::kNobid);
    }
  }
  if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES &&
      ad.get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP) {
    ad.SetAutoCpaBid(mcb_params.auto_cpa_bid,
        ad_base::AutoCpaBidModifyTagType::kPayTimes);
  }
  if (ad.get_is_purchase_pay_test()) {
    ad.SetAutoCpaBid(mcb_params.auto_cpa_bid,
                              ad_base::AutoCpaBidModifyTagType::kSocialPayROIBidModify);
    RANK_DOT_STATS(session_data_, p_ad->get_auction_bid() * 1000000, "ad_rank.purchase_pay_calc_cpm",
                   "auction_bid_mcb", "true", kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
    RANK_DOT_STATS(session_data_, p_ad->get_cpm(), "ad_rank.purchase_pay_calc_cpm", "cpm_mcb", "true",
                   kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
  }
  if (mcb_params.enable_roas_twin_game_ad_mcb_node && ad.Is(AdFlag::is_roas_twin_game_ad)) {
    if (ad.Is(AdFlag::is_wechat_game_ad) && ad.get_raw_conv_ltv() > 0 &&
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) {
      ad.SetAutoCpaBid(mcb_params.auto_cpa_bid, ad_base::AutoCpaBidModifyTagType::kGamePayROI7);
    } else {
      ad.SetAutoCpaBid(mcb_params.auto_cpa_bid, ad_base::AutoCpaBidModifyTagType::kGamePayROI12);
    }
  }
  const auto* ad_bounder = session_data_->mutable_ad_bounder();
  if (ad_bounder && ad_bounder->Enable()) {
    ad_bounder->SetAutoCpaBid(&ad, ad.get_auto_cpa_bid());
  }
  // 产出 longratio_w
  SetUnifyFillLongRatioWeight(&mcb_params, auction_bid_init, p_ad);
}

void MCBStrategy::GetMCBCpaBid(const AdCommon& ad,
    const ContextData *context_data,
    ks::engine_base::MCBParams *p_mcb_params) {
  auto& mcb_params = *p_mcb_params;

  float cpa_bid = 0.0;
  float roi_ratio = 0.0;

  // 兜底前置到 build_rank_request
  if (ad.get_bid_strategy_group() == kuaishou::ad::AdEnum::MAX_CONV_STRATEGY) {
    cpa_bid = ad.get_product_cpa_bid();
    roi_ratio = ad.get_product_roi_ratio();
  } else {
    cpa_bid = static_cast<float>(ad.get_cpa_bid());
    roi_ratio = static_cast<float>(ad.get_roi_ratio());
  }
  if (roi_ratio > 0) {
    if (ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
      cpa_bid = ad.get_unify_ltv_info().value * 1000 / roi_ratio; // NOLINT

      if (ad.get_auto_roas() <= 0) {
        p_mcb_params->auto_cpa_bid = cpa_bid;
        LOG_EVERY_N(WARNING, 100000) << "roi auto roas less than zero, auto_roas = "
                                     << ad.get_auto_roas()
                                     << "roi_ratio = " << roi_ratio;
      } else {
        p_mcb_params->auto_cpa_bid =
          ad.get_unify_ltv_info().value * 1000 / ad.get_auto_roas();  // NOLINT
      }
      double origin_pay_amount_7d = ad.get_predict_score(PredictType::PredictType_7_day_game_conv_ltv);
      double origin_pay_amount_1d = ad.get_predict_score(PredictType::PredictType_1_day_pay_amount);
      double origin_pay_amount_2_7d = ad.get_predict_score(PredictType::PredictType_2_7_day_pay_amount);
      // 将拆分逻辑移动到 set unify ltv
      double unifyltv = ad.get_unify_ltv_info().value;

      // base: 七日模型拆分首日和 2-7 日
      if (mcb_params.enable_pay_amount_2and7) {
        origin_pay_amount_7d = mcb_params.pay_amount_alpha * origin_pay_amount_1d +
                                mcb_params.pay_amount_beta * origin_pay_amount_2_7d;
      }
      // exp: 七日专项之长线回收
      if (mcb_params.enable_long_ratio_seven_day_roas) {
        const std::string& key = absl::Substitute("$0_ltv_exp", ad.get_product_name());
        SevenDayRoasModifyRValue(ad, p_mcb_params, key, &unifyltv);
      }
      double auto_roas = ad.get_auto_roas() > 0 ? ad.get_auto_roas() : roi_ratio;
      p_mcb_params->auto_cpa_bid = unifyltv * 1000 / auto_roas;
    } else if (ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
               ad.get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
               ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
               ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
               ad.get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
               ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
               ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
               ad.get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) {
      cpa_bid = ad.get_unify_ltv_info().value * 1000 / roi_ratio;
      if (ad.get_auto_roas() <= 0) {
        p_mcb_params->auto_cpa_bid = cpa_bid;
        LOG_EVERY_N(WARNING, 100000) << "roi auto roas less than zero, auto_roas = "
                                     << ad.get_auto_roas()
                                     << "roi_ratio = " << roi_ratio;
      } else {
          // minigame iaa 7 日 roi
          bool enable_game_bidding_formula_new =
            SPDM_enable_game_bidding_formula_new(session_data_->get_spdm_ctx());
          bool is_iaa_roi7_target_account = utility::IsGameIaaRoi7TargetAccount(session_data_, ad);
          if ((enable_game_bidding_formula_new
                && ad.Is(AdFlag::is_iaa_game_ad)
                && is_iaa_roi7_target_account)
                || ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS) {
            GameBiddingFormula(context_data, p_mcb_params, ad);
          } else {
            // minigame iaa 首日 roi
            p_mcb_params->auto_cpa_bid = ad.get_unify_ltv_info().value * 1000 /  // NOLINT
                ad.get_auto_roas();
          }
      }
      // 小游戏 排序计费分离
      if (mcb_params.enable_iap_billing_seperate && ad.Is(AdFlag::is_iaap_game_ad)) {
        p_mcb_params->auto_cpa_bid = cpa_bid;
        p_mcb_params->auto_cpa_bid *= mcb_params.iap_billing_seperate_ratio;
      }
      if (mcb_params.enable_mini_game_billing_seperate && ad.get_industry_parent_id_v3() == 1018
            && (ad.get_landing_page_component() == 1 || ad.get_landing_page_component() == 2)) {
        p_mcb_params->auto_cpa_bid = cpa_bid;
        p_mcb_params->auto_cpa_bid *= mcb_params.mini_game_billing_seperate_ratio;
      }

      // 小游戏首 R 倍率双出价
      const auto& iap_game_twin_bid_conf = engine_base::AdKconfUtil::iapGameTwinBidConf()->data();
      const auto& iap_twin_bid_default_long_value_ratio_map =
                                 iap_game_twin_bid_conf.iap_twin_bid_default_long_value_ratio_map;
      const auto& iap_twin_bid_long_value_ratio_map =
                                 iap_game_twin_bid_conf.iap_twin_bid_long_value_ratio_map;
      const auto& iap_twin_bid_default_twin_bid_alpha_map =
                                 iap_game_twin_bid_conf.iap_twin_bid_default_twin_bid_alpha_map;
      auto iap_twin_bid_default_long_value_ratio_iter =
         iap_twin_bid_default_long_value_ratio_map.find(mcb_params.iap_game_twin_bid_group_tag);
      auto iap_twin_bid_long_value_ratio_map_iter =
                 iap_twin_bid_long_value_ratio_map.find(mcb_params.iap_game_twin_bid_group_tag);
      auto iap_twin_bid_default_twin_bid_alpha_iter =
           iap_twin_bid_default_twin_bid_alpha_map.find(mcb_params.iap_game_twin_bid_group_tag);
      bool iap_game_twin_bid_admit = iap_twin_bid_default_long_value_ratio_iter !=
          iap_twin_bid_default_long_value_ratio_map.end() && iap_twin_bid_long_value_ratio_map_iter
          != iap_twin_bid_long_value_ratio_map.end() && iap_twin_bid_default_twin_bid_alpha_iter
          != iap_twin_bid_default_twin_bid_alpha_map.end();
      if (mcb_params.enable_iap_7d_value_allocate && ad.Is(AdFlag::is_iaap_game_ad)) {
        if (iap_game_twin_bid_admit) {
          double iap_twin_bid_default_twin_bid_alpha =
                                              iap_twin_bid_default_twin_bid_alpha_iter->second;
          const auto& product_long_value_ratio_map =
                                                iap_twin_bid_long_value_ratio_map_iter->second;
          // 目标倍率 优先级 1. 大盘统一 2. iap / iaap 3. 部分产品
          double iap_twin_bid_default_long_value_ratio =
                                            iap_twin_bid_default_long_value_ratio_iter->second;
          if (mcb_params.enable_iap_twin_bid_divide_ocpc) {
            if (ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS) {
              iap_twin_bid_default_long_value_ratio =
                                          mcb_params.iap_twin_bid_default_long_value_iap_ratio;
            } else if (ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) {
              iap_twin_bid_default_long_value_ratio =
                                         mcb_params.iap_twin_bid_default_long_value_iaap_ratio;
            }
          }
          auto product_iter = product_long_value_ratio_map.find(ad.get_product_name());
          auto account_iter = product_long_value_ratio_map.find(absl::StrCat(ad.get_account_id()));
          if (account_iter != product_long_value_ratio_map.end()) {
            iap_twin_bid_default_long_value_ratio = account_iter->second;
          }
          if (product_iter != product_long_value_ratio_map.end()) {
            iap_twin_bid_default_long_value_ratio = product_iter->second;
          }
          iap_twin_bid_default_long_value_ratio *=
                                         mcb_params.iap_twin_bid_default_long_value_boost_ratio;
          session_data_->dot_perf->Interval(iap_twin_bid_default_long_value_ratio * 1000,
                                           "iap_twin_bid_default_long_value_ratio",
                                            mcb_params.iap_game_twin_bid_group_tag, ad.get_product_name());
          // 预估倍率 - 仅考虑付费倍率
          double origin_conv_1_day_pay_amount = ad.get_optimal_subsidy_ltv() > 0.0001 ?
          ad.get_optimal_subsidy_ltv() : ad.get_predict_score(PredictType::PredictType_game_conv_ltv);
          double origin_conv_7_day_pay_amount = origin_conv_1_day_pay_amount +
                                    ad.get_predict_score(PredictType::PredictType_2_7_day_pay_amount);
          double predict_iap_game_long_value_ratio = 1.0;
          if (origin_conv_1_day_pay_amount > 0.0001) {
            predict_iap_game_long_value_ratio = origin_conv_7_day_pay_amount / origin_conv_1_day_pay_amount;
          }
          predict_iap_game_long_value_ratio = std::max(mcb_params.iap_game_long_value_ratio_lower_bound,
            std::min(mcb_params.iap_game_long_value_ratio_upper_bound, predict_iap_game_long_value_ratio));
          session_data_->dot_perf->Interval(predict_iap_game_long_value_ratio * 1000,
                                            "predict_iap_game_long_value_ratio",
                                            mcb_params.iap_game_twin_bid_group_tag, ad.get_product_name());
          // 滑动平均预估倍率 or 基于后验倍率
          double moving_iap_game_long_value_ratio = 1.0;
          std::string allocate_dim = absl::StrCat(mcb_params.iap_game_twin_bid_group_tag, "_",
                                                 ad.get_ocpx_action_type(), "_", ad.get_product_name());
          moving_iap_game_long_value_ratio =
                      ks::ad_rank::RankingData::GetInstance()->GetGameSameIndustyAvgLtv(
                      allocate_dim, predict_iap_game_long_value_ratio,
                      mcb_params.iap_twin_bid_decay_weight, mcb_params.iap_twin_bid_req_threshold, true);
          if (mcb_params.enable_iap_twin_bid_unify_post_ratio) {
            moving_iap_game_long_value_ratio = mcb_params.default_moving_iap_game_long_value_ratio;
          }
          session_data_->dot_perf->Interval(moving_iap_game_long_value_ratio * 1000,
                                              "moving_iap_game_long_value_ratio",
                                              mcb_params.iap_game_twin_bid_group_tag, ad.get_product_name());
          double final_iap_twin_bid_ratio = 1.0;
          double mini_game_ltv7 = ad.get_predict_score(PredictType::PredictType_outer_game_conv_ltv_7);;
          double mini_game_roi1 = roi_ratio;
          double mini_game_roi7 = mini_game_roi1;
          if (mcb_params.enable_iap_7d_value_twin_bid) {
            // 首 R 倍率融合双出价
            if (iap_twin_bid_default_long_value_ratio > 1.0) {
              final_iap_twin_bid_ratio =
                      (1 + iap_twin_bid_default_twin_bid_alpha / iap_twin_bid_default_long_value_ratio *
                            (predict_iap_game_long_value_ratio - iap_twin_bid_default_long_value_ratio));
            }
          } else if (mcb_params.enable_iap_7d_value_twin_bid_v2) {
            // 滑动窗口倍率出价
            if (moving_iap_game_long_value_ratio > 1.0) {
              final_iap_twin_bid_ratio = predict_iap_game_long_value_ratio / moving_iap_game_long_value_ratio;
            }
          } else if (mcb_params.enable_iap_7r_allocate) {
            // 7R 出价
            if (mcb_params.enable_iap_7d_use_auto_roas && ad.get_auto_roas() > 0.000001) {
              mini_game_roi1 = ad.get_auto_roas();
            }
            // by 产品、出价目标、大盘
            mini_game_roi7 = mini_game_roi1 * iap_twin_bid_default_long_value_ratio;
            if (ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) {
              if (SPDM_enable_search_outer_cmd_independent(session_data_->get_spdm_ctx())) {
                mini_game_ltv7 = ad.get_predict_score(PredictType::PredictType_outer_game_conv_ltv_7) +
                          ad.get_predict_score(PredictType::PredictType_search_industry_game_iaa_ltv7);
              } else {
                mini_game_ltv7 = ad.get_predict_score(PredictType::PredictType_outer_game_conv_ltv_7) +
                                ad.get_predict_score(PredictType::PredictType_industry_game_iaa_ltv7);
              }
            }
          }
          session_data_->dot_perf->Interval(final_iap_twin_bid_ratio * 1000, "final_iap_twin_bid_ratio",
                                              mcb_params.iap_game_twin_bid_group_tag, ad.get_product_name());
          // 截断
          final_iap_twin_bid_ratio = std::min(mcb_params.final_iap_twin_bid_ratio_upper_bound,
                        std::max(mcb_params.final_iap_twin_bid_ratio_lower_bound, final_iap_twin_bid_ratio));
          p_mcb_params->auto_cpa_bid *= final_iap_twin_bid_ratio;

          // 7r reset auto_cpa_bid
          if (mcb_params.enable_iap_7r_allocate && mini_game_roi7 > 0.000001) {
            session_data_->dot_perf->Interval(p_mcb_params->auto_cpa_bid * 1000,
                                      "mini_game_7r_allocate_before", mcb_params.iap_game_twin_bid_group_tag);
            // todo: 增加约束
            p_mcb_params->auto_cpa_bid = mini_game_ltv7 * 1000 / mini_game_roi7;
            session_data_->dot_perf->Interval(p_mcb_params->auto_cpa_bid * 1000,
                                       "mini_game_7r_allocate_after", mcb_params.iap_game_twin_bid_group_tag);
          }
          // boost
          p_mcb_params->auto_cpa_bid *= mcb_params.final_iap_twin_bid_boost_ratio;
        } else {
          session_data_->dot_perf->Count(1,
                                     "iap_game_twin_bid_admit_fail", mcb_params.iap_game_twin_bid_group_tag);
        }
      }

      // 小游戏暗测 7r 出价
      if (mcb_params.enable_iap_7d_value_allocate_v2 && ad.Is(AdFlag::is_iaap_game_ad)) {
        double mini_game_ltv7 = 0.0;
        double mini_game_roi7 = roi_ratio * mcb_params.iap_long_value_target_ratio;  // 首日 roi
        if (ad.get_auto_roas() <= 0.000001) {
          session_data_->dot_perf->Count(1, "mini_game_auto_roas_zero");
        }
        if (mcb_params.enable_iap_7d_use_auto_roas && ad.get_auto_roas() > 0.000001) {
          mini_game_roi7 = ad.get_auto_roas() * mcb_params.iap_long_value_target_ratio;
        }
        if (mini_game_roi7 > 0.000001) {
          if (ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS) {
            mini_game_ltv7 = ad.get_predict_score(PredictType::PredictType_outer_game_conv_ltv_7);
          } else if (ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) {
            if (SPDM_enable_search_outer_cmd_independent(session_data_->get_spdm_ctx())) {
                mini_game_ltv7 = ad.get_predict_score(PredictType::PredictType_outer_game_conv_ltv_7) +
                              ad.get_predict_score(PredictType::PredictType_search_industry_game_iaa_ltv7);
            } else {
              mini_game_ltv7 = ad.get_predict_score(PredictType::PredictType_outer_game_conv_ltv_7) +
                              ad.get_predict_score(PredictType::PredictType_industry_game_iaa_ltv7);
            }
          }
          session_data_->dot_perf->Interval(
                                        p_mcb_params->auto_cpa_bid * 1000, "mini_game_auto_cpa_bid_before");
          p_mcb_params->auto_cpa_bid = mini_game_ltv7 * 1000 / mini_game_roi7;
          p_mcb_params->auto_cpa_bid *= mcb_params.final_iap_7r_boost_ratio;
          session_data_->dot_perf->Interval(
                                         p_mcb_params->auto_cpa_bid * 1000, "mini_game_auto_cpa_bid_after");
        } else {
          session_data_->dot_perf->Count(1, "mini_game_roi7_zereo");
        }
        session_data_->dot_perf->Count(1, "mini_game_7r_total_cnt");
      }

      // 首日 ROI 的长线优化实验，限制在游戏行业
      if (ad.Is(AdFlag::is_game_ad) && mcb_params.enable_long_ratio_ad_roas) {
        const std::string& key = absl::Substitute("$0_ad_roas_game_ltv_exp", ad.get_product_name());
        AdRoasGameModifyAutoCpaBid(ad, p_mcb_params, key, roi_ratio);
      }
      // 短剧切分点改成点击
      if ((mcb_params.enable_playlet_iap_cut_point_to_click && ad.Is(AdFlag::is_paid_duanju_ad_v3))
          && mcb_params.enable_outer_u_cvr && ad.get_playlet_ctr() > 0.0) {
        double click_unify_ltv = ad.get_playlet_unify_ltv();
        if (ad.get_auto_roas() <= 0) {
          p_mcb_params->auto_cpa_bid = click_unify_ltv * 1000 / roi_ratio;
        } else {
          p_mcb_params->auto_cpa_bid = click_unify_ltv * 1000 / ad.get_auto_roas();
        }
      }
    } else if (ad.get_deep_conversion_type()
        == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) {
      // 适配 ocpm 的付费 roi 双出价
      if ((ad.get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP &&
          ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) ||
          (ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION)) {
        // 默认为激活切分点， PredictType_game_conv_ltv 换算为系统出价
        double auto_roas = ad.get_auto_roas() > 0 ? ad.get_auto_roas() : roi_ratio;
        double origin_conv_ltv = ad.get_predict_score(PredictType::PredictType_game_conv_ltv);
        p_mcb_params->auto_cpa_bid =
          std::min(origin_conv_ltv * 1000 / auto_roas,
                   cpa_bid * mcb_params.roas_twin_conv_upper_bound);
        if (ad.get_is_purchase_pay_test()) {
          // 代表付费切分点， PredictType_purchase_ltv 换算为系统出价
          double raw_origin_conv_ltv = origin_conv_ltv;
          origin_conv_ltv = ad.get_predict_score(PredictType::PredictType_purchase_ltv);
          session_data_->dot_perf->Count(1, "ad_rank.roi_ltv_predict",
                                         absl::StrCat("conv_", raw_origin_conv_ltv <= 0),
                                         absl::StrCat("purchase_", origin_conv_ltv <= 0),
                                         absl::StrCat(session_data_->get_pos_manager_base().GetSubPageId()));
          p_mcb_params->auto_cpa_bid =
            std::min(origin_conv_ltv * 1000 / auto_roas,
                     cpa_bid * mcb_params.roas_twin_deep_upper_bound);
          session_data_->dot_perf->Interval(static_cast<int64>(cpa_bid), "ad_rank.roi_bid", "cpa_bid",
                                            ad_base::GetBidGroupByEnum(ad.get_group_idx()),
                                            kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
          session_data_->dot_perf->Interval(static_cast<int64_t>(p_mcb_params->auto_cpa_bid),
                                            "ad_rank.roi_bid", "adjust_cpa_bid",
                                            ad_base::GetBidGroupByEnum(ad.get_group_idx()),
                                            kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
          session_data_->dot_perf->Interval(static_cast<int64_t>(origin_conv_ltv * 1000000),
                                            "ad_rank.roi_cvr", "purchase_ltv",
                                            ad_base::GetBidGroupByEnum(ad.get_group_idx()),
                                            kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
          // 代表融合出价策略
          double roi_auto_cpa_bid = mcb_params.auto_cpa_bid;
          double purchase_auto_cpa_bid = cpa_bid * roi_ratio / auto_roas;
          auto coef = ad.get_deep_min_bid_coef();
          p_mcb_params->auto_cpa_bid = purchase_auto_cpa_bid * std::max(0.0, 1-coef)
              + roi_auto_cpa_bid * std::min(1.0, coef);
          session_data_->dot_perf->Interval(static_cast<int64_t>(auto_roas * 1000000.0 / roi_ratio),
                                            "ad_rank.roi_bid", "auto_roas_ratio",
                                            ad_base::GetBidGroupByEnum(ad.get_group_idx()),
                                            kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
          session_data_->dot_perf->Interval(static_cast<int64_t>(coef * 1000000), "ad_rank.roi_bid",
                                            "deep_min_bid_coef",
                                            ad_base::GetBidGroupByEnum(ad.get_group_idx()),
                                            kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
          session_data_->dot_perf->Interval(static_cast<int64_t>(p_mcb_params->auto_cpa_bid),
                                            "ad_rank.roi_bid", "fuse_adjust_cpa_bid",
                                            ad_base::GetBidGroupByEnum(ad.get_group_idx()),
                                            kuaishou::ad::AdEnum_AdQueueType_Name(ad.get_ad_queue_type()));
        }
      } else {
        cpa_bid = ad.get_unify_deep_cvr_info().value * 1000 / roi_ratio;
      }
      // ROI 双游戏广告
      if (mcb_params.enable_roas_twin_game_ad_mcb_node && ad.Is(AdFlag::is_roas_twin_game_ad)) {
        // 无 diff 迁移
        double origin_conv_ltv = ad.get_predict_score(PredictType::PredictType_game_conv_ltv);
        double auto_roas = ad.get_auto_roas() > 0 ? ad.get_auto_roas() : roi_ratio;
        double adjust_cpa_bid = std::min(origin_conv_ltv * 1000 / auto_roas,
                                         ad.get_cpa_bid() * mcb_params.roas_twin_conv_upper_bound);
        if (ad.Is(AdFlag::is_wechat_game_ad) && ad.get_raw_conv_ltv() > 0 &&
            ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) {
          // 小游戏 ltv 自动校准覆盖付费 ROI 双
          double cal_cpa_bid = std::min(ad.get_unify_ltv_info().value * 1000 / auto_roas,
                                        ad.get_cpa_bid() * mcb_params.roas_twin_conv_upper_bound);
          p_mcb_params->auto_cpa_bid = cal_cpa_bid;
        } else {
          p_mcb_params->auto_cpa_bid = adjust_cpa_bid;
        }

        // ROI 双长线回收实验
        if (mcb_params.enable_long_ratio_roas_twin_game_ad) {
          const std::string& key = absl::Substitute("$0_ad_roas_game_ltv_exp", ad.get_product_name());
          AdRoasGameModifyAutoCpaBid(ad, p_mcb_params, key, roi_ratio);
          double deep_auto_cpa_bid = p_mcb_params->auto_cpa_bid;
          p_mcb_params->auto_cpa_bid = std::min(deep_auto_cpa_bid,
                                                ad.get_cpa_bid() * mcb_params.roas_twin_conv_upper_bound);
        }
      }
    }

    // AD_CID_ROAS
    if (mcb_params.enable_cid_ad_bid_mcb && ad.get_ocpx_action_type() == kuaishou::ad::AD_CID_ROAS) {
      double auto_roas = ad.get_auto_roas() > 0 ? ad.get_auto_roas() : roi_ratio;
      double merchant_ltv = ad.get_unify_ltv_info().value;
      double unify_cvr = ad.get_unify_cvr_info().value;
      if (auto_roas > 0 && unify_cvr > 0) {
        p_mcb_params->auto_cpa_bid = merchant_ltv * 1000 / unify_cvr / auto_roas;
        RANK_DOT_STATS(session_data_, auto_roas * 1000,
                       "ad.ad_rank.enable_cid_bid_roas_exp.auto_roas.mcb");
        RANK_DOT_STATS(session_data_, merchant_ltv * 1000,
                       "ad.ad_rank.enable_cid_bid_roas_exp.merchant_ltv.mcb");
        RANK_DOT_STATS(session_data_, unify_cvr * 1000,
                       "ad.ad_rank.enable_cid_bid_roas_exp.unify_cvr.mcb");
      } else {
        RANK_DOT_COUNT(session_data_, 1, "ad.ad_rank.enable_cid_bid_roas_exp.miss_cvr.mcb");
      }
    }
  }
  // uax aigc 校准
  if (mcb_params.enable_uax_aigc_bid_calibration) {
    bool is_ocpm_aigc = (ad.get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP && (
        ad.get_photo_source_aigc() == 16 || ad.get_photo_source_aigc() == 17));
    RANK_DOT_COUNT(session_data_, 1, "ad.ad_rank.is_ocpm_aigc", absl::StrCat(is_ocpm_aigc));
    if (is_ocpm_aigc) {
      double aigc_bid_coef = ad.get_aigc_bid_coef();
      // UAX
      if (ad.get_auto_manage() == kuaishou::ad::AdEnum::AUTO_MANAGE_OPEN &&
          aigc_bid_coef > 0) {
        p_mcb_params->auto_cpa_bid *= aigc_bid_coef;
        RANK_DOT_STATS(session_data_, aigc_bid_coef * 1000000,
                       "ad.ad_rank.enable_uax_aigc_bid_calibration",
                       kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type()),
                       kuaishou::ad::AdCallbackLog_EventType_Name(ad.get_deep_conversion_type()));
      }
    }
  }
  mcb_params.cpa_bid = static_cast<double>(cpa_bid);
  mcb_params.roi_ratio = static_cast<double>(roi_ratio);
  mcb_params.deep_cpa_bid = static_cast<double>(ad.get_deep_cpa_bid());
}

void MCBStrategy::GetMCBR(
    const AdCommon& ad, const ContextData *context_data, ks::engine_base::MCBParams *p_mcb_params) {
  auto& mcb_params = *p_mcb_params;

  double ctr = ad.get_unify_ctr_info().value;
  double cvr = ad.get_unify_cvr_info().value;
  // 深度激励加上 uplift cvr
  if (session_data_->get_is_rewarded() &&
      (ad.Is(AdFlag::IsHardAd) ||
      (SPDM_enable_native_deep_reward_effect_cpm(session_data_->get_spdm_ctx()) && ad.Is(AdFlag::IsNativeAd)))) {  // NOLINT
      if (ad.get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE ||
               SPDM_enable_order_deep_incentive_effect_cpm(session_data_->get_spdm_ctx())) {
      // 下单激励先跳过
      cvr += ad.get_pec_uplift_cvr();
      if (SPDM_enable_invoked_deep_incentive_adjust_cvr(session_data_->get_spdm_ctx())) {
        cvr *= ad.get_deep_incentive_cvr_adjust_ratio();
      }
    }
  }
  double r = ctr * cvr;
  auto deep_conversion_type = ad.get_deep_conversion_type();
  mcb_params.deep_r = ad.get_unify_deep_cvr_info().value;

  // 2. r
  // 短剧切分点改成点击
  if ((mcb_params.enable_playlet_iap_cut_point_to_click && ad.Is(AdFlag::is_paid_duanju_ad_v3))
      && mcb_params.enable_outer_u_cvr && ad.get_playlet_ctr() > 0.0) {
    r =  ctr * ad.get_playlet_ctr();
    // 还原校准系数，应用到切分点改造实验，打平基线。 cvr = playlet_ctr * playlet_cvr * factor
    double playlet_ctcvr = ad.get_playlet_ctr() * ad.get_playlet_cvr();
    if (playlet_ctcvr > 0.0) {
      double factor = std::min(std::max(0.0, cvr / playlet_ctcvr), mcb_params.playlet_calibration_ratio_max);
      r *= factor;
    }
  } else if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE
      && ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) {
    r = ctr * cvr * mcb_params.deep_r;
  } else if (ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION) {
    r = ctr * cvr * mcb_params.deep_r;
  } else if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
    // baseV1: 7d 优化
    // baseV2: 拆分首日 & 2_7d 优化
    r = ctr * cvr * std::max(mcb_params.deep_r, 0.0);
    // exp1: 长线回收 7d 优化(覆盖 baseV2)
    if (mcb_params.enable_long_ratio_pay_times_2and7) {
      const std::string& key = absl::Substitute("$0", ad.get_product_name());
      SevenDayPaytimesLongRatioInfo seven_day_long_ratio_info;
      bool is_p2p_succeed =
          SevenDayPaytimesLongRatio::GetInstance()->GetProportion(key, &seven_day_long_ratio_info);
      if (is_p2p_succeed) {
        double limited_r_con_long_ratio_7d_repurchase =
            GetLongRatioPredictBound(ad, p_mcb_params, seven_day_long_ratio_info);
        r = ctr * cvr * limited_r_con_long_ratio_7d_repurchase;
      }
    }
  } else if (mcb_params.deep_r > 0
    && ad.get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION) {
    // 获客双出价支持三条链路，表单/IM/企微
    r = ctr * cvr * mcb_params.deep_r;
  } else if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION) {
    if (mcb_params.is_single_col && mcb_params.enable_deep_middle_model_exp &&
        mcb_params.enable_deep_middle_model_kac_exp) {
      r = ctr * cvr * mcb_params.deep_r;
    } else {
      double post_target_roi = 0;
      double p_ltv0 = ad.get_predict_score(PredictType::PredictType_key_action_ltv0);
      if (mcb_params.key_action_post_target_roi_product_map != nullptr &&
           mcb_params.key_action_post_target_roi_product_map->find(ad.get_product_name()) !=
            mcb_params.key_action_post_target_roi_product_map->end()) {
        post_target_roi =
          mcb_params.key_action_post_target_roi_product_map->find(ad.get_product_name())->second;
      }
      r = ctr * cvr * ad.get_predict_score(PredictType::PredictType_conv_key_inapp_action_rate);
    }
  } else if (mcb_params.bid_strategy_group == kuaishou::ad::AdEnum::MAX_CONV_STRATEGY &&
             deep_conversion_type == kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY) {
    r = ctr * cvr * mcb_params.deep_r * mcb_params.mcb_conv_nextstay_ratio;
  } else if (ad.Is(AdFlag::is_deep_conv_deep_valid_clues) ||
             (!SPDM_enable_real_deep_wechat_connected_lps_cvr_set(context_data->get_spdm_ctx()) &&
    ad.get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_WECHAT_CONNECTED)) {
    r = ctr * cvr * ad.get_predict_score(PredictType::PredictType_lps_valid_clues) * mcb_params.lps_super_deep_factor;  // NOLINT
  } else if (ad.Is(AdFlag::is_deep_valid_clues) ||
             (!SPDM_enable_real_deep_wechat_connected_lps_cvr_set(context_data->get_spdm_ctx()) &&
              ad.get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED)) {
    // 兜底
    if (mcb_params.deep_r <= 0) {
      mcb_params.deep_r = ad.get_predict_score(PredictType::PredictType_lps_valid_clues);
    }
    r = ctr * cvr * mcb_params.deep_r;
  } else if (ad.Is(AdFlag::is_retention)) {
    if (mcb_params.deep_r <= 0) {
      if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY) {
        mcb_params.deep_r = ad.get_conv_nextstay();
      } else if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_24H_STAY) {
        mcb_params.deep_r = ad.get_predict_score(PredictType::PredictType_conv_24h_stay);
      } else if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY) {
        mcb_params.deep_r = ad.get_predict_score(PredictType::PredictType_conv_7_day_stay);
      }
    }
    r = ctr * cvr * mcb_params.deep_r;
  } else if ((deep_conversion_type == kuaishou::ad::AdCallbackLog_EventType_EVENT_RETENTION_DAYS
            && ad.get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) ||
            ad.get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS) {
    r = ctr * cvr * mcb_params.deep_r;  // 每留排序
  }

  bool is_reward_shadow = mcb_params.is_rewarded && !ad.Is(AdFlag::is_rewarded_ads);  // 激励暗投
  bool is_search_shadow =
    session_data_->get_pos_manager_base().IsSearchRequest() && !ad.Is(AdFlag::is_search_bidword);  // 搜索暗投
  if (mcb_params.is_rewarded || session_data_->get_pos_manager_base().IsSearchRequest()) {  // NOLINT
    double discount_ratio =
      is_reward_shadow ? mcb_params.rewarded_keyaction_discount : mcb_params.search_discount;
    std::string prefix = is_reward_shadow ? "Inspire" : "Search";

    prefix = mcb_params.is_rewarded ? "Inspire" : "Search";
    if (mcb_params.is_rewarded) {
      discount_ratio = mcb_params.rewarded_keyaction_discount;
    } else {
      discount_ratio = mcb_params.search_discount;
    }

    const std::string& action_type = kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type());
    const std::string& product_name = ad.get_product_name();
    const std::string& key = absl::Substitute("$0_$1_$2", prefix, product_name, action_type);
    std::string key_industry = "null";
    key_industry = absl::Substitute("$0_$1_$2", prefix, ad.get_second_industry_id_v5(),
                                      action_type);

    auto iter = mcb_params.search_inspire_weight_map->find(key);
    if (iter != mcb_params.search_inspire_weight_map->end()) {
      discount_ratio = iter->second;
    }
    iter = mcb_params.search_inspire_weight_map->find(key_industry);
    if (iter != mcb_params.search_inspire_weight_map->end()) {
      discount_ratio = iter->second;
    }
    const std::string& playlet_industry_stable_config = "Inspire_playlet2012_AD_ROAS";
    auto iter_playlet = (mcb_params.search_inspire_weight_map->find(playlet_industry_stable_config));
    bool is_playlet = (ad.get_second_industry_id_v5() == 2012);
    if ( is_inspire_pos_id && is_playlet &&
        iter_playlet != mcb_params.search_inspire_weight_map->end()) {
      discount_ratio = iter_playlet->second;
    }
    discount_ratio = std::min(1.0, discount_ratio);

    bool valid_discount = discount_ratio > DBL_EPSILON && discount_ratio < 10;
    bool skip_discount_ratio = mcb_params.skip_discount_ratio &&
            (mcb_params.merge_bid_account_tail->count(ad.get_account_id() % 100) > 0);
    if (valid_discount && !skip_discount_ratio) {
      r = r * discount_ratio;
    }
    RANK_DOT_STATS(context_data, discount_ratio * 1000,
      "ad_rank.mcb_search_inspire_product_ocpx_weitht",
      valid_discount ? "valid" : "invalid",
      prefix, product_name, action_type);
  }
mcb_params.r = r;
}

// MCB 适配软广逻辑：计算 ecpm 即可，auction bid 计算、字段填充在外面执行
double MCBStrategy::MCBBenifitForOuterNative(AdCommon *p_ad, const ContextData *context_data) {
  auto& ad = *p_ad;

  // 1. mcb 参数填充
  // 后面会给这个值赋值，为了避免问题，这里重新获取， really wired
  UpdateAdMCBParams(ad, context_data, &mcb_params);

  // 2. 执行调价逻辑 & LOG
  double ecpm_yuan = 0.0;
  GetMCBCpaBid(ad, context_data, &mcb_params);
  GetMCBR(ad, context_data, &mcb_params);
  ecpm_yuan = mcb_params.CalcMCBAuctionBid(ecpm_yuan);

  bool is_roi = (ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
                  ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS);
  if (static_cast<int32_t>(ad.get_constraint_roi()) == 5 ||   // ABO_CAP
      static_cast<int32_t>(ad.get_constraint_roi()) == 6) {  // COST_CAP
    if (is_roi && ad.get_roi_ratio() <= 0) {
      ad.set_roi_ratio(ad.get_product_roi_ratio());
    } else if (!is_roi && ad.get_cpa_bid() <= 0) {
      ad.SetCpaBid(ad.get_product_cpa_bid(), ad_base::AutoCpaBidModifyTagType::kNobid);
    }
  } else if (ad.get_bid_type() == kuaishou::ad::AdEnum::MCB) {
    ad.set_cpa_bid(ad.get_auto_cpa_bid());
  }

  // 产出 auto_cpa_bid
  ad.set_mcb_r(mcb_params.r);
  if (ad.get_bid_strategy_group() == kuaishou::ad::AdEnum::MAX_CONV_STRATEGY) {
    if (ad.get_deep_conversion_type()   // ltv 重写
        == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP ||
       ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP ||
       ad.get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS) {
      ad.SetAutoCpaBid(mcb_params.auto_cpa_bid,
                ad_base::AutoCpaBidModifyTagType::kGamePayROI9);
    } else if (ad.get_auto_cpa_bid() <= 0) {
      ad.SetAutoCpaBid(mcb_params.cpa_bid / mcb_params.budget_coef,
        ad_base::AutoCpaBidModifyTagType::kNobid);
    }
  }

  if (ad.get_is_purchase_pay_test()) {
    ad.SetAutoCpaBid(mcb_params.auto_cpa_bid,
                              ad_base::AutoCpaBidModifyTagType::kSocialPayROIBidModify);
  }
  if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES &&
      ad.get_bid_type() == kuaishou::ad::AdEnum::OCPM_DSP) {
    ad.SetAutoCpaBid(mcb_params.auto_cpa_bid,
        ad_base::AutoCpaBidModifyTagType::kPayTimes);
  }
  if (mcb_params.enable_roas_twin_game_ad_mcb_node && ad.Is(AdFlag::is_roas_twin_game_ad)) {
    if (ad.Is(AdFlag::is_wechat_game_ad) && ad.get_raw_conv_ltv() > 0 &&
        ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) {
      ad.SetAutoCpaBid(mcb_params.auto_cpa_bid, ad_base::AutoCpaBidModifyTagType::kGamePayROI7);
    } else {
      ad.SetAutoCpaBid(mcb_params.auto_cpa_bid, ad_base::AutoCpaBidModifyTagType::kGamePayROI12);
    }
  }
  const auto* ad_bounder = session_data_->mutable_ad_bounder();
  if (ad_bounder && ad_bounder->Enable()) {
    ad_bounder->SetAutoCpaBid(&ad, ad.get_auto_cpa_bid());
  }
  // 产出 longratio_w
  SetUnifyFillLongRatioWeight(&mcb_params, ecpm_yuan, p_ad);
  // 3. 返回结果
  return ecpm_yuan;
}

double MCBStrategy::GetLongRatioPredictBound(const AdCommon& ad, ks::engine_base::MCBParams* p_mcb_params,
                                             const SevenDayPaytimesLongRatioInfo& seven_day_long_ratio_info) {
  auto& mcb_params = *p_mcb_params;
  double r_1d_pay_times = ad.get_predict_score(PredictType::PredictType_1_day_pay_times);
  double r_2_7d_pay_times = ad.get_predict_score(PredictType::PredictType_2_7_day_pay_times);
  double r_purchase = ad.get_predict_score(PredictType::PredictType_purchase);
  double alpha = mcb_params.pay_times_alpha;
  double beta = mcb_params.pay_times_beta;
  double r_con_long_ratio_7d_repurchase = std::max(seven_day_long_ratio_info.w1 * r_1d_pay_times +
                                                   seven_day_long_ratio_info.w2 * r_2_7d_pay_times +
                                                   seven_day_long_ratio_info.w0 * r_purchase, 0.0);
  // bound method 1
  double base_r_con_7d_repurchase;
  if (alpha * r_1d_pay_times + beta * r_2_7d_pay_times > r_purchase) {
    base_r_con_7d_repurchase = std::max(alpha * r_1d_pay_times + beta * r_2_7d_pay_times - r_purchase, 0.0);
  } else {
    base_r_con_7d_repurchase = std::max(alpha * r_1d_pay_times + beta * r_2_7d_pay_times + r_purchase, 0.0);
  }
  double upper_r_con_long_ratio_7d_repurchase =
      base_r_con_7d_repurchase * 10.0;
  double lower_r_con_long_ratio_7d_repurchase =
      base_r_con_7d_repurchase * 0.001;
  double limited_r_con_long_ratio_7d_repurchase = std::max(
      std::min(r_con_long_ratio_7d_repurchase, upper_r_con_long_ratio_7d_repurchase),
      lower_r_con_long_ratio_7d_repurchase);

  // bound method 2
  limited_r_con_long_ratio_7d_repurchase = std::max(
      std::min(r_con_long_ratio_7d_repurchase, mcb_params.upper_r_long_ratio_7d_repurchase_param), 0.0);
  return limited_r_con_long_ratio_7d_repurchase;
}

void MCBStrategy::SevenDayRoasModifyRValue(const AdCommon& ad, ks::engine_base::MCBParams* p_mcb_params,
                                           const std::string& key, double* origin_pay_amount_7d) {
  auto& mcb_params = *p_mcb_params;
  SevenDayPaytimesLongRatioInfo seven_day_long_ratio_info;
  bool is_p2p_succeed =
      SevenDayPaytimesLongRatio::GetInstance()->GetProportion(key, &seven_day_long_ratio_info);
  if (!is_p2p_succeed) {
    return;
  }
  double origin_pay_amount_1d = ad.get_predict_score(PredictType::PredictType_1_day_pay_amount);
  double origin_pay_amount_2_7d = ad.get_predict_score(PredictType::PredictType_2_7_day_pay_amount);
  double origin_pay_amount_7d_long_ratio = seven_day_long_ratio_info.w1 * origin_pay_amount_1d +
                                           seven_day_long_ratio_info.w2 * origin_pay_amount_2_7d;
  if (mcb_params.enable_seven_day_longratio_cali_ratio) {
    origin_pay_amount_7d_long_ratio *= mcb_params.seven_day_longratio_cali_ratio;
  }
  *origin_pay_amount_7d = std::max(
    std::min(origin_pay_amount_7d_long_ratio, mcb_params.upper_origin_pay_amount_7d), 0.0);
}

void MCBStrategy::AdRoasGameModifyAutoCpaBid(const AdCommon& ad, ks::engine_base::MCBParams* p_mcb_params,
                                           const std::string& key, const double roi_ratio) {
  auto& mcb_params = *p_mcb_params;
  SevenDayPaytimesLongRatioInfo seven_day_long_ratio_info;
  bool is_p2p_succeed =
      SevenDayPaytimesLongRatio::GetInstance()->GetProportion(key, &seven_day_long_ratio_info);
  if (!is_p2p_succeed) {
    return;
  }
  double origin_conv_ltv = ad.get_predict_score(PredictType::PredictType_game_conv_ltv);
  double origin_pay_amount_2_7d = ad.get_predict_score(PredictType::PredictType_2_7_day_pay_amount);
  double ad_roas_long_ratio_pay_amount_7d = seven_day_long_ratio_info.w1 * origin_conv_ltv +
                                            seven_day_long_ratio_info.w2 * origin_pay_amount_2_7d;
  if (mcb_params.enable_ad_roas_longratio_cali_ratio) {
    ad_roas_long_ratio_pay_amount_7d *= mcb_params.ad_roas_longratio_cali_ratio;
  }
  double ad_roas_long_ratio_pay_amount_7d_limit = std::max(
    std::min(ad_roas_long_ratio_pay_amount_7d, mcb_params.upper_ad_roas_long_ratio_pay_amount_7d), 0.0);
  double auto_roas = ad.get_auto_roas() > 0 ? ad.get_auto_roas() : roi_ratio;
  p_mcb_params->auto_cpa_bid = std::min(ad_roas_long_ratio_pay_amount_7d_limit, 2000.0) * 1000 / auto_roas;
}
void MCBStrategy::SetUnifyFillLongRatioWeight(ks::engine_base::MCBParams* p_mcb_params,
                                              const double& auction_bid_init, AdCommon* p_ad) {
  const auto& mcb_params = *p_mcb_params;
  const auto& ad = *p_ad;
  // 1 准入
  std::string key;
  if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
    key = absl::Substitute("$0", ad.get_product_name());
  } else if (ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
    key = absl::Substitute("$0_ltv_exp", ad.get_product_name());
  } else if ((ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS && ad.Is(AdFlag::is_game_ad)) ||
             ad.Is(AdFlag::is_roas_twin_game_ad)) {
    // ROI 单 or 付费激活 ROI 双游戏行业
    key = absl::Substitute("$0_ad_roas_game_ltv_exp", ad.get_product_name());
  }

  SevenDayPaytimesLongRatioInfo seven_day_long_ratio_info;
  bool is_p2p_succeed =
      SevenDayPaytimesLongRatio::GetInstance()->GetProportion(key, &seven_day_long_ratio_info);
  if (!is_p2p_succeed) {
    return;
  }

  // 2 获取实验前和实验后参数
  double r_init = 0.0;
  double r_longrato = 0.0;
  if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
    double r_1d_pay_times = ad.get_predict_score(PredictType::PredictType_1_day_pay_times);
    double r_2_7d_pay_times = ad.get_predict_score(PredictType::PredictType_2_7_day_pay_times);
    double r_purchase = ad.get_predict_score(PredictType::PredictType_purchase);
    double r_con_long_ratio_7d_repurchase = std::max(seven_day_long_ratio_info.w1 * r_1d_pay_times +
                                                     seven_day_long_ratio_info.w2 * r_2_7d_pay_times +
                                                     seven_day_long_ratio_info.w0 * r_purchase, 0.0);
    r_longrato = std::max(
        std::min(r_con_long_ratio_7d_repurchase, mcb_params.upper_r_long_ratio_7d_repurchase_param), 0.0);
    r_init = std::max(mcb_params.pay_times_alpha * r_1d_pay_times +
                      mcb_params.pay_times_beta * r_2_7d_pay_times - r_purchase,
                     0.0);
  } else if (ad.get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
    double origin_pay_amount_1d = ad.get_predict_score(PredictType::PredictType_1_day_pay_amount);
    double origin_pay_amount_2_7d = ad.get_predict_score(PredictType::PredictType_2_7_day_pay_amount);
    double origin_pay_amount_7d_long_ratio = seven_day_long_ratio_info.w1 * origin_pay_amount_1d +
                                             seven_day_long_ratio_info.w2 * origin_pay_amount_2_7d;
    r_longrato = std::max(
      std::min(origin_pay_amount_7d_long_ratio, mcb_params.upper_origin_pay_amount_7d), 0.0);
    r_init = mcb_params.pay_amount_alpha * origin_pay_amount_1d +
             mcb_params.pay_amount_beta * origin_pay_amount_2_7d;
  } else if ((ad.get_ocpx_action_type() == kuaishou::ad::AD_ROAS && ad.Is(AdFlag::is_game_ad)) ||
             ad.Is(AdFlag::is_roas_twin_game_ad)) {
    // ROI 单 or 付费激活 ROI 双游戏行业
    double origin_conv_ltv = ad.get_predict_score(PredictType::PredictType_game_conv_ltv);
    double origin_pay_amount_2_7d = ad.get_predict_score(PredictType::PredictType_2_7_day_pay_amount);
    double ad_roas_long_ratio_pay_amount_7d = seven_day_long_ratio_info.w1 * origin_conv_ltv +
                                              seven_day_long_ratio_info.w2 * origin_pay_amount_2_7d;
    r_longrato = std::max(
      std::min(ad_roas_long_ratio_pay_amount_7d, mcb_params.upper_ad_roas_long_ratio_pay_amount_7d), 0.0);
    r_init = origin_conv_ltv;
  }

  // 3 填充到 bidInfo
  p_ad->set_longratio_w0(seven_day_long_ratio_info.w0);
  p_ad->set_longratio_w1(seven_day_long_ratio_info.w1);
  p_ad->set_longratio_w2(seven_day_long_ratio_info.w2);
  p_ad->set_longratio_ratio(r_init > 0.0 ? r_longrato / r_init : 0.0);
  p_ad->set_auction_bid_init(auction_bid_init);
}

void MCBStrategy::GameBiddingFormula(const ContextData *context_data,
    ks::engine_base::MCBParams* p_mcb_params,
    const AdCommon& ad) {
  double iaa_roi7_ratio_coef =
    SPDM_minigame_iaa_roi7_ratio_coef(context_data->get_spdm_ctx());
  // 七日 roi
  std::string minigame_roi7_exp_tag =
        SPDM_minigame_roi7_exp_tag(session_data_->get_spdm_ctx());
  session_data_->dot_perf->Count(1, "ad_rank.minigame_iaa_roi7_enter_test",
                                    absl::StrCat(ad.get_ocpx_action_type()));
  double roi_ratio = static_cast<float>(ad.get_roi_ratio());
  double pltv7;
  if (SPDM_enable_search_outer_cmd_independent(context_data->get_spdm_ctx())) {
    pltv7 = ad.get_predict_score(PredictType::PredictType_search_industry_game_iaa_ltv7);
  } else {
    pltv7 = ad.get_predict_score(PredictType::PredictType_industry_game_iaa_ltv7);
  }
  double ctr = ad.get_unify_ctr_info().value;
  double cvr = ad.get_unify_cvr_info().value;
  double roi7_ratio = roi_ratio * iaa_roi7_ratio_coef;
  session_data_->dot_perf->Interval(pltv7,
      "ad_rank.minigame_iaa_roi7_pltv7", absl::StrCat(ad.get_ocpx_action_type()), minigame_roi7_exp_tag);
  session_data_->dot_perf->Interval(roi7_ratio,
      "ad_rank.minigame_iaa_roi7_roi7_ratio", minigame_roi7_exp_tag);
  // 7r 出价默认走 DEEP_ROI
  if (ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS) {
    minigame_bidding_strategy_type = MiniGameBiddingStrategyType::DEEP_ROI;
  }
  if (minigame_bidding_strategy_type == MiniGameBiddingStrategyType::DEEP_ROI) {
    if (ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS && ad.get_auto_roas() > 0) {
      double game_iaa_roi7_deep_roi_online_coef =
        SPDM_game_iaa_roi7_deep_roi_online_coef(context_data->get_spdm_ctx());
      p_mcb_params->auto_cpa_bid = pltv7 * 1000 *game_iaa_roi7_deep_roi_online_coef / ad.get_auto_roas();
      session_data_->dot_perf->Interval(p_mcb_params->auto_cpa_bid,
              "ad_rank.minigame_iaa_roi7_deep_roi_direct",
              absl::StrCat(ad.get_ocpx_action_type()),
              minigame_roi7_exp_tag);
    } else {
      if (roi7_ratio > 0) {
        p_mcb_params->auto_cpa_bid = pltv7 /
              roi7_ratio * 1000 * mcb_params.game_iaa_roi7_deep_roi_coef;
        session_data_->dot_perf->Interval(p_mcb_params->auto_cpa_bid,
              "ad_rank.minigame_iaa_roi7_deep_roi", minigame_roi7_exp_tag);
      }
    }
  } else if (minigame_bidding_strategy_type == MiniGameBiddingStrategyType::DEEP_MIN) {
    if (ad.get_auto_roas() > 0 && roi7_ratio > 0) {
      double ltv7_bid = pltv7 / roi7_ratio * 1000 * mcb_params.game_iaa_roi7_deep_min_deep_coef;
      double ltv1_bid = ad.get_unify_ltv_info().value / ad.get_auto_roas()
          * 1000 * mcb_params.game_iaa_roi7_deep_min_shallow_coef;
      p_mcb_params->auto_cpa_bid = std::min(ltv1_bid, ltv7_bid);
        session_data_->dot_perf->Interval(p_mcb_params->auto_cpa_bid,
                              "ad_rank.minigame_iaa_roi7_deep_min_auto_cpa_bid",
                              minigame_roi7_exp_tag);
      session_data_->dot_perf->Interval(ltv1_bid,
                              "ad_rank.minigame_iaa_roi7_deep_min_ltv1_bid",
                              minigame_roi7_exp_tag);
      session_data_->dot_perf->Interval(ltv7_bid,
                              "ad_rank.minigame_iaa_roi7_deep_min_ltv7_bid",
                              minigame_roi7_exp_tag);
    }
  } else if (minigame_bidding_strategy_type == MiniGameBiddingStrategyType::DEEP_AVG_PACING) {
    double pltv1 = ad.get_unify_ltv_info().value;
    double alpha = mcb_params.game_iaa_deep_avg_pacing_alpha;
    double beta = mcb_params.game_iaa_deep_avg_pacing_beta;
    const auto& game_iaa_roi7_dynamic_adjust_config = RankKconfUtil::gameIaaRoi7DynamicAdjustConfig();
    std::string game_iaa_roi7_dynamic_adjust_exp_tag =
      SPDM_game_iaa_rou7_dynamic_adjust_exp_tag(session_data_->get_spdm_ctx());
    int64_t game_iaa_product_ltv7_ema_req_threshold = 500;
    double game_iaa_product_ltv7_ema_decay_weight = 0.99;
    double game_iaa_deep_avg_pacing_upper_bound = 5.0;
    double game_iaa_deep_avg_pacing_lower_bound = 0.5;
    double game_iaa_ltv7_std_scale_ratio = 1.0;
    double game_iaa_ltv7_avg_scale_ratio = 1.0;
    if (game_iaa_roi7_dynamic_adjust_config != nullptr) {
      auto match_exp_list =
          game_iaa_roi7_dynamic_adjust_config->data().exp_list().find(game_iaa_roi7_dynamic_adjust_exp_tag);
      if (match_exp_list != game_iaa_roi7_dynamic_adjust_config->data().exp_list().end()) {
        game_iaa_deep_avg_pacing_upper_bound = match_exp_list->second.upper_bound();
        game_iaa_deep_avg_pacing_lower_bound = match_exp_list->second.lower_bound();
        game_iaa_product_ltv7_ema_decay_weight = match_exp_list->second.decay_weight();
        game_iaa_product_ltv7_ema_req_threshold = match_exp_list->second.req_threshold();
        game_iaa_ltv7_std_scale_ratio = match_exp_list->second.scale_ratio();
      }
    }
    double pltv7_moving_avg = pltv7;  //  默认平均值
    double pltv7_moving_std = 1e-6;  //  默认标准差
    pltv7_moving_avg = ks::ad_rank::RankingData::GetInstance()->GetGameIaaProuctLtv7AvgMoving(
                ad.get_product_name(),
                pltv7,
                game_iaa_product_ltv7_ema_decay_weight,
                game_iaa_product_ltv7_ema_req_threshold);
    pltv7_moving_std = ks::ad_rank::RankingData::GetInstance()->GetGameIaaProuctLtv7StdMoving(
                ad.get_product_name(),
                pltv7,
                game_iaa_product_ltv7_ema_req_threshold);
    double ratio = 1.0;
    if (pltv7_moving_std > 0 && game_iaa_ltv7_std_scale_ratio > 0) {
      ratio = (pltv7 - pltv7_moving_avg) / (3 * pltv7_moving_std * game_iaa_ltv7_std_scale_ratio) + 1.0; // NOLINT
      // 首日 roi 出价
      double origin_cpa_bid = ad.get_unify_ltv_info().value * 1000 /  // NOLINT
              ad.get_auto_roas();
      double deep_avg_pacing_ratio = 1.0;
      deep_avg_pacing_ratio = std::min(std::max(alpha * ratio + beta, game_iaa_deep_avg_pacing_lower_bound),
            game_iaa_deep_avg_pacing_upper_bound);
      // 扰动
      p_mcb_params->auto_cpa_bid = origin_cpa_bid * deep_avg_pacing_ratio;
      session_data_->dot_perf->Interval(deep_avg_pacing_ratio,
        "ad_rank.minigame_iaa_roi7_deep_avg_pacing", minigame_roi7_exp_tag);
      session_data_->dot_perf->Interval(p_mcb_params->auto_cpa_bid,
        "ad_rank.minigame_iaa_roi7_deep_avg_pacing", minigame_roi7_exp_tag);
    }
  }
}
MCBStrategy::MiniGameBiddingStrategyType MCBStrategy::GetStrategyTypeFromString(const std::string& strategy) {
    static const std::unordered_map<std::string, MiniGameBiddingStrategyType> strategyMap = {
        {"DEEP_ROI", MiniGameBiddingStrategyType::DEEP_ROI},
        {"DEEP_PACING", MiniGameBiddingStrategyType::DEEP_PACING},
        {"DEEP_MIN", MiniGameBiddingStrategyType::DEEP_MIN},
        {"DEEP_AVG_PACING", MiniGameBiddingStrategyType::DEEP_AVG_PACING},
        {"DEEP_COMBINE", MiniGameBiddingStrategyType::DEEP_COMBINE},
    };

    auto it = strategyMap.find(strategy);
    if (it != strategyMap.end()) {
        return it->second;
    }
    return MiniGameBiddingStrategyType::INVALID;
}

}  // namespace ad_rank
}  // namespace ks


