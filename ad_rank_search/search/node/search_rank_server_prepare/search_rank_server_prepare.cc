#include "teams/ad/ad_rank_search/search/node/search_rank_server_prepare/search_rank_server_prepare.h"

#include <algorithm>
#include <cstdint>
#include <iterator>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>
#include <set>

#include "base/encoding/base64.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "absl/strings/str_join.h"
#include "glog/logging.h"
#include "glog/stl_logging.h"
#include "teams/ad/ad_rank_search/common/enum.h"
#include "teams/ad/ad_rank_search/common/macro.h"
#include "teams/ad/ad_rank_search/engine/server/node_register.h"
#include "teams/ad/ad_rank_search/utils/kconf/kconf.h"
#include "teams/ad/ad_rank_search/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank_search/utils/utility/eds_request_prepare.h"
#include "teams/ad/ad_rank_search/wrappers/rank_request_parser.h"
#include "teams/ad/engine_base/kconf/kconf.h"
const double epsilon = 0.000001;
namespace ks {
namespace ad_rank {
REGISTER_NODE(SearchRankServerPrepare);
using ::operator<<;
void SearchRankServerPrepare::Init() {
  session_data_ = context_->GetMutableContextData<ContextData>();
  photo_id_vec_.clear();
  author_id_vec_.clear();
  photo_vec_keys_.clear();
  author_vec_keys_.clear();
  q2a_inner_photo_id_vec_.clear();
  q2a_outer_photo_id_vec_.clear();
  q2a_serial_photo_id_vec_.clear();
  q2a_inner_photo_vec_keys_.clear();
  q2a_outer_photo_vec_keys_.clear();
  q2a_serial_photo_vec_keys_.clear();
}

bool SearchRankServerPrepare::ProcessInner() {
  auto proc_start_ts = base::GetTimestamp();
  Init();
  if (!session_data_) return false;
  query_ = session_data_->get_rank_request()->ad_request().search_info().query();
  if (!SPDM_enableRankSearchCrossFeature()) {
    return true;
  }
  ks::infra::RedisResponse<std::vector<std::string>> photo_feature_res;
  ks::infra::RedisResponse<std::vector<std::string>> author_feature_res;
  GetReqCrossFeatureKeys();

  auto get_asyn_handler = [&] (const std::string& redis_name, std::vector<std::string>& keys,
                          ks::infra::RedisResponse<std::vector<std::string>>* feature_res) {
    auto *client =
        ks::ad_base::KconfRedis::Instance().GetAdRedisPipelineClient(redis_name,
                                            ks::engine_base::DependDataLevel::WEAK_DEPEND);
    if (client == nullptr) {
      LOG_EVERY_N(ERROR, 10000) << "can not access cross_feature redis"
                                << redis_name;
      return;
    }
    *feature_res = client->MGet(keys);
  };
  get_asyn_handler(*RankKconfUtil::photoCrossFeatureRedis(), photo_vec_keys_, &photo_feature_res);
  get_asyn_handler(*RankKconfUtil::liveCrossFeatureRedis(), author_vec_keys_, &author_feature_res);

  auto photo_cross_feature_map_ptr = std::make_unique<std::unordered_map<int64_t,
                                QueryFeaInfo>>();
  auto author_cross_feature_map_ptr = std::make_unique<std::unordered_map<int64_t,
                                QueryFeaInfo>>();
  AsyncGetCrossFeature(photo_id_vec_, &photo_feature_res,
                    photo_cross_feature_map_ptr.get(), "photo_");
  AsyncGetCrossFeature(author_id_vec_, &author_feature_res,
                    author_cross_feature_map_ptr.get(), "author_");
  session_data_->dot_perf->Interval(photo_cross_feature_map_ptr.get()->size(),
                                    "search_rank_prepare", "photo_end_map_size");
  session_data_->dot_perf->Interval(author_cross_feature_map_ptr.get()->size(),
                                      "search_rank_prepare", "author_end_map_size");
  session_data_->common_w_->SetPtrCommonAttr("photo_cross_feature_map",
          std::move(photo_cross_feature_map_ptr));
  session_data_->common_w_->SetPtrCommonAttr("author_cross_feature_map",
          std::move(author_cross_feature_map_ptr));

  ks::infra::RedisResponse<std::vector<std::string>> q2a_inner_feature_res;
  ks::infra::RedisResponse<std::vector<std::string>> q2a_outer_feature_res;
  ks::infra::RedisResponse<std::vector<std::string>> q2a_serial_feature_res;
  GetReqQueryToAdFeaV2Keys();

  get_asyn_handler(*RankKconfUtil::q2AFeatureRedis(), q2a_inner_photo_vec_keys_,
                    &q2a_inner_feature_res);
  get_asyn_handler(*RankKconfUtil::q2AFeatureRedis(), q2a_outer_photo_vec_keys_,
                    &q2a_outer_feature_res);
  get_asyn_handler(*RankKconfUtil::q2AFeatureRedis(), q2a_serial_photo_vec_keys_,
                    &q2a_serial_feature_res);

  auto inner_q2a_feature_map_ptr = std::make_unique<std::unordered_map<int64_t,
                                QueryFeaInfo>>();
  auto outer_q2a_feature_map_ptr = std::make_unique<std::unordered_map<std::string,
                                QueryFeaInfo>>();
  auto serial_q2a_feature_map_ptr = std::make_unique<std::unordered_map<int64_t,
                                QueryFeaInfo>>();
  AsyncGetCrossFeature(q2a_inner_photo_id_vec_, &q2a_inner_feature_res,
                        inner_q2a_feature_map_ptr.get(), "inner_");
  AsyncGetCrossFeature(q2a_outer_photo_id_vec_, &q2a_outer_feature_res,
                      outer_q2a_feature_map_ptr.get(), "outer_");
  AsyncGetCrossFeature(q2a_serial_photo_id_vec_, &q2a_serial_feature_res,
                      serial_q2a_feature_map_ptr.get(), "serial_");
  session_data_->dot_perf->Interval(inner_q2a_feature_map_ptr.get()->size(),
                                    "search_rank_prepare", "inner_q2a_map_size");
  session_data_->dot_perf->Interval(outer_q2a_feature_map_ptr.get()->size(),
                                    "search_rank_prepare", "outer_q2a_map_size");
  session_data_->dot_perf->Interval(serial_q2a_feature_map_ptr.get()->size(),
                                    "search_rank_prepare", "serial_q2a_map_size");
  session_data_->common_w_->SetPtrCommonAttr("inner_q2a_feature_map",
            std::move(inner_q2a_feature_map_ptr));
  session_data_->common_w_->SetPtrCommonAttr("outer_q2a_feature_map",
          std::move(outer_q2a_feature_map_ptr));
  session_data_->common_w_->SetPtrCommonAttr("serial_q2a_feature_map",
          std::move(serial_q2a_feature_map_ptr));
  RANK_DOT_STATS(session_data_, session_data_->get_current_timestamp_nodiff() - proc_start_ts,
            "node_statistic_latency", "SearchRankServerPrepare");

  return true;
}

template <typename T>
void SearchRankServerPrepare::AsyncGetCrossFeature(const std::vector<T>& id_vec,
                ks::infra::RedisResponse<std::vector<std::string>>* feature_res,
                std::unordered_map<T, QueryFeaInfo>* feature_map,
                const std::string& ad_type_name) {
  std::vector<std::string> vals_vec;
  auto ret = feature_res->Get(&vals_vec);
  if (ret != ks::infra::KS_INF_REDIS_NO_ERROR) {
    session_data_->dot_perf->Count(1, "search_rank_prepare",
      absl::StrCat(ad_type_name, "_redis_ret_error"), std::to_string(ret));
    return;
  }
  if (vals_vec.size() != id_vec.size()) {
    LOG(ERROR) << "Failed to MGet redis score, error: "
                << ", key size: " << id_vec.size()
                << ", value size: " << vals_vec.size();
    session_data_->dot_perf->Count(1, "search_rank_prepare",
          absl::StrCat(ad_type_name, "_feature_redis"), "miss");
    return;
  }
  int32_t value_empty_cnt = 0;
  int32_t decode_fail_cnt = 0;
  int32_t parse_fail_cnt = 0;
  int32_t parse_ok_cnt = 0;
  for (int32_t i = 0; i < vals_vec.size(); i++) {
    if (vals_vec[i].empty()) {
      value_empty_cnt++;
      continue;
    }
    std::string decode;
    if (!base::Base64Decode(vals_vec[i], &decode)) {
      decode_fail_cnt++;
      continue;
    }
    kuaishou::ad::search_ads::QueryFeaInfo query_feature_info;
    if (!query_feature_info.ParseFromString(decode)) {
      parse_fail_cnt++;
      continue;
    }
    parse_ok_cnt++;
    LOG_EVERY_N(INFO, 100000) << "search_cross_feature_info, "
                              << ad_type_name << "id: " << id_vec[i]
                              << ", value_str: " << vals_vec[i]
                              << ", feature_info: " << query_feature_info.ShortDebugString();
    (*feature_map)[id_vec[i]] = std::move(query_feature_info);
  }
  if (value_empty_cnt) {
    session_data_->dot_perf->Interval(value_empty_cnt, "search_rank_prepare",
                                      absl::StrCat(ad_type_name, "_redis_value_empty"));
  }
  if (decode_fail_cnt) {
    session_data_->dot_perf->Interval(decode_fail_cnt, "search_rank_prepare",
                                      absl::StrCat(ad_type_name, "_decode_failed"));
  }
  if (parse_fail_cnt) {
    session_data_->dot_perf->Interval(parse_fail_cnt, "search_rank_prepare",
                                      absl::StrCat(ad_type_name, "_parse_failed"));
  }
  if (parse_ok_cnt) {
    session_data_->dot_perf->Interval(parse_ok_cnt, "search_rank_prepare",
                                      absl::StrCat(ad_type_name, "_parse_ok"));
  }
}



void SearchRankServerPrepare::GetReqCrossFeatureKeys() {
  std::unordered_set<int64_t> author_id_set;
  std::unordered_set<int64_t> photo_id_set;
  int32_t find_photo_cnt = 0;
  int32_t find_author_cnt = 0;
  int32_t find_other_cnt = 0;
  int32_t live_find_author_fail_cnt = 0;
  const auto& photo_key_prefix = session_data_->get_spdm_ctx().
      TryGetString("search_rank_photo_cross_feature_prefix", "base");
  const auto& author_key_prefix = session_data_->get_spdm_ctx().
      TryGetString("search_rank_author_cross_feature_prefix", "base");

  auto try_to_insert_photo = [&](const int64_t photo_id) {
    if (photo_id_set.insert(photo_id).second) {
      const auto& key_str = absl::StrCat(photo_key_prefix, "_", query_, "_", photo_id);
      uint64_t hash_key = base::CityHash64(key_str.c_str(), key_str.length());
      photo_vec_keys_.push_back(std::to_string(hash_key));
      photo_id_vec_.push_back(photo_id);
    }
  };
  auto try_to_insert_author = [&](const int64_t author_id) {
    if (author_id_set.insert(author_id).second) {
      const auto& key_str = absl::StrCat(author_key_prefix, "_", query_, "_", author_id);
      uint64_t hash_key = base::CityHash64(key_str.c_str(), key_str.length());
      author_vec_keys_.push_back(std::to_string(hash_key));
      author_id_vec_.push_back(author_id);
    }
  };
  auto get_keys_from_ad_list = [&](const AdList* ad_list, const std::string& ad_list_name) {
    for (auto p_ad : ad_list->Ads()) {
      auto live_stream_id = p_ad->get_live_stream_id();
      auto photo_id = p_ad->get_photo_id();
      if (photo_id > 0) {
        find_photo_cnt++;
        try_to_insert_photo(photo_id);
        continue;
      } else if (live_stream_id > 0) {
        auto author_id = p_ad->get_author_id();
        if (author_id > 0) {
          find_author_cnt++;
          try_to_insert_author(author_id);
        } else {
          live_find_author_fail_cnt++;
        }
      } else {
        find_other_cnt++;
      }
    }
  };
  // 当前搜索仍存在多队列，故都需遍历
  get_keys_from_ad_list(session_data_->mutable_live_ad_list(), "author_ad_list");
  get_keys_from_ad_list(session_data_->mutable_fanstop_ad_list(), "fanstop_ad_list");
  get_keys_from_ad_list(session_data_->mutable_ad_list(), "ad_list");

  session_data_->dot_perf->Interval(photo_vec_keys_.size(), "search_rank_prepare",
                                      "req_redis_photo_key_size");
  session_data_->dot_perf->Interval(author_vec_keys_.size(), "search_rank_prepare",
                                      "req_redis_author_key_size");
  session_data_->dot_perf->Interval(find_photo_cnt, "search_rank_prepare", "find_photo_cnt");
  session_data_->dot_perf->Interval(find_author_cnt, "search_rank_prepare", "find_author_cnt");
  session_data_->dot_perf->Interval(find_other_cnt, "search_rank_prepare", "find_other_cnt");
  session_data_->dot_perf->Interval(live_find_author_fail_cnt, "search_rank_prepare",
                                      "live_find_author_fail_cnt");
}



void SearchRankServerPrepare::GetReqQueryToAdFeaV2Keys() {
  std::unordered_set<std::string> outer_photo_id_set;
  std::unordered_set<int64_t> inner_photo_id_set;
  std::unordered_set<int64_t> serial_photo_id_set;

  int32_t find_inner_cnt = 0;
  int32_t find_outer_cnt = 0;
  int32_t find_serial_cnt = 0;
  int32_t find_other_cnt = 0;

  auto try_to_insert_inner = [&](const int64_t merchant_product_id) {
    if (inner_photo_id_set.insert(merchant_product_id).second) {
      const auto& key_str = absl::StrCat("2-", query_, "-", merchant_product_id);
      uint64_t hash_key = base::CityHash64(key_str.c_str(), key_str.length());
      q2a_inner_photo_vec_keys_.push_back(std::to_string(hash_key));
      q2a_inner_photo_id_vec_.push_back(merchant_product_id);
    }
  };

  auto try_to_insert_outer = [&](const std::string product_name) {
    if (outer_photo_id_set.insert(product_name).second) {
      const auto& key_str = absl::StrCat("1-", query_, "-", product_name);
      uint64_t hash_key = base::CityHash64(key_str.c_str(), key_str.length());
      q2a_outer_photo_vec_keys_.push_back(std::to_string(hash_key));
      q2a_outer_photo_id_vec_.push_back(product_name);
    }
  };

  auto try_to_insert_serial = [&](const int64_t serial_id) {
    if (serial_photo_id_set.insert(serial_id).second) {
      const auto& key_str = absl::StrCat("3-", query_, "-", serial_id);
      uint64_t hash_key = base::CityHash64(key_str.c_str(), key_str.length());
      q2a_serial_photo_vec_keys_.push_back(std::to_string(hash_key));
      q2a_serial_photo_id_vec_.push_back(serial_id);
    }
  };

  auto get_keys_from_ad_list = [&](const AdList* ad_list, const std::string& ad_list_name) {
    for (auto p_ad : ad_list->Ads()) {
      auto merchant_product_id = p_ad->get_merchant_product_id();
      auto product_name = p_ad->get_product_name();
      auto serial_id = p_ad->Attr(ItemIdx::fd_UNIT_series_id).GetIntValue(p_ad->AttrIndex()).value_or(0);
      if (merchant_product_id > 0) {
        find_inner_cnt++;
        try_to_insert_inner(merchant_product_id);
      } else if (serial_id > 0) {
        try_to_insert_serial(serial_id);
        find_serial_cnt++;
      } else if (product_name != "") {
        try_to_insert_outer(product_name);
        find_outer_cnt++;
      } else {
        find_other_cnt++;
      }
    }
  };
  // 当前搜索仍存在多队列，故都需遍历
  get_keys_from_ad_list(session_data_->mutable_live_ad_list(), "author_ad_list");
  get_keys_from_ad_list(session_data_->mutable_fanstop_ad_list(), "fanstop_ad_list");
  get_keys_from_ad_list(session_data_->mutable_ad_list(), "ad_list");

  session_data_->dot_perf->Interval(photo_vec_keys_.size(), "search_rank_prepare",
                                      "req_redis_photo_key_size");
  session_data_->dot_perf->Interval(author_vec_keys_.size(), "search_rank_prepare",
                                      "req_redis_author_key_size");
  session_data_->dot_perf->Interval(find_inner_cnt, "search_rank_prepare", "find_inner_cnt");
  session_data_->dot_perf->Interval(find_outer_cnt, "search_rank_prepare", "find_outer_cnt");
  session_data_->dot_perf->Interval(find_serial_cnt, "search_rank_prepare", "find_serial_cnt");
  session_data_->dot_perf->Interval(find_other_cnt, "search_rank_prepare", "find_other_cnt");
}

}  // namespace ad_rank
}  // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SearchRankServerPrepare, ::ks::ad_rank::SearchRankServerPrepare);
