
#include <vector>
#include <memory>
#include <unordered_map>
#include <unordered_set>

#include "base/encoding/base64.h"
#include "teams/ad/ad_rank_search/search/node/search_ranking_prepare/search_context_feature_builder.h"
#include "teams/ad/ad_rank_search/common/context_data.h"
#include "google/protobuf/repeated_field.h"
#include "teams/ad/ad_rank_search/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank_search/processor/model/utility.h"

using kuaishou::ad::ContextInfoCommonAttr;
using kuaishou::ad::CommonTypeEnum;

namespace ks {
namespace ad_rank {

static std::unordered_map<int64_t, int32_t> feas_map = {
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_SHOW_CNT,
      ContextInfoCommonAttr::Q_1D_SHOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_CLICK_CNT,
      ContextInfoCommonAttr::Q_1D_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_PLAY_CNT,
      ContextInfoCommonAttr::Q_1D_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_LONG_VIEW_CNT,
      ContextInfoCommonAttr::Q_1D_LONG_VIEW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_FIRST_CLICK_CNT,
      ContextInfoCommonAttr::Q_1D_FIRST_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_FOLLOW_CNT,
      ContextInfoCommonAttr::Q_1D_FOLLOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_LIKE_CNT,
      ContextInfoCommonAttr::Q_1D_LIKE_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_PV,
      ContextInfoCommonAttr::Q_1D_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_CTR,
      ContextInfoCommonAttr::Q_1D_CTR},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_LVTR,
      ContextInfoCommonAttr::Q_1D_LVTR},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_SHOW_CNT,
      ContextInfoCommonAttr::Q_7D_SHOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_CLICK_CNT,
      ContextInfoCommonAttr::Q_7D_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_PLAY_CNT,
      ContextInfoCommonAttr::Q_7D_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_LONG_VIEW_CNT,
      ContextInfoCommonAttr::Q_7D_LONG_VIEW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_FIRST_CLICK_CNT,
      ContextInfoCommonAttr::Q_7D_FIRST_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_FOLLOW_CNT,
      ContextInfoCommonAttr::Q_7D_FOLLOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_LIKE_CNT,
      ContextInfoCommonAttr::Q_7D_LIKE_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_PV,
      ContextInfoCommonAttr::Q_7D_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_CTR,
      ContextInfoCommonAttr::Q_7D_CTR},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_LVTR,
      ContextInfoCommonAttr::Q_7D_LVTR},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CLUSTER_ID_1W,
      ContextInfoCommonAttr::QUERY_CLUSTER_ID_1W},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CLUSTER_ID_10W,
      ContextInfoCommonAttr::QUERY_CLUSTER_ID_10W},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CLUSTER_ID_100W,
      ContextInfoCommonAttr::QUERY_CLUSTER_ID_100W},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CLUSTER_ID_1000W,
      ContextInfoCommonAttr::QUERY_CLUSTER_ID_1000W},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UV_1D,
      ContextInfoCommonAttr::DUP_UV_1D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UV_7D,
      ContextInfoCommonAttr::DUP_UV_7D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UV_30D,
      ContextInfoCommonAttr::DUP_UV_30D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UQV_1D,
      ContextInfoCommonAttr::DUP_UQV_1D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UQV_7D,
      ContextInfoCommonAttr::DUP_UQV_7D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UQV_30D,
      ContextInfoCommonAttr::DUP_UQV_30D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::COMBO_SEARCH_SOURCE,
      ContextInfoCommonAttr::COMBO_SEARCH_SOURCE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::IS_FRESHNESS_SEARCH,
      ContextInfoCommonAttr::IS_FRESHNESS_SEARCH},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::CATEGORY_ID_LIST,
      ContextInfoCommonAttr::CATEGORY_ID_LIST},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_TYPE,
      ContextInfoCommonAttr::QUERY_TYPE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::PIC_INTENT,
      ContextInfoCommonAttr::PIC_INTENT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::TF_IDF,
      ContextInfoCommonAttr::TF_IDF},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::INTENT_AUTHOR_NAME,
      ContextInfoCommonAttr::INTENT_AUTHOR_NAME},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::VERTICAL_SOURCE,
      ContextInfoCommonAttr::VERTICAL_SOURCE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::IS_EPIDEMIC_SEARCH,
      ContextInfoCommonAttr::IS_EPIDEMIC_SEARCH},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SHOW_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_SHOW_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_FOLLOW_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_FOLLOW_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_LIKE_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_LIKE_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_PLAY_CNT,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_SEARCH_PHOTO_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_CNT,
      ContextInfoCommonAttr::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_NOW_CNT,
      ContextInfoCommonAttr::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_NOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_PLAY_DURATION,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_SEARCH_PHOTO_PLAY_DURATION},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SHORTPLAY,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_DO_NOTHING_SHORTPLAY,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_DO_NOTHING_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_OR_FOLLOW_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_OR_FOLLOW_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_FOLLOW_OR_LIKE_SHORTPLAY,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_FOLLOW_OR_LIKE_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_FOLLOW,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_NO_COMP_FOLLOW,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_NO_COMP_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_NO_FOLLOW,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_NO_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_NO_COMP_NO_FOLLOW,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_NO_COMP_NO_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_NO_COMP_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_NO_COMP_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_NO_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_NO_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_NO_COMP_NO_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_NO_COMP_NO_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_ABNORM_REPEAT_PLAY_CNT,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_ABNORM_REPEAT_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_EARLY_SKIPED_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_EARLY_SKIPED_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_MID_SKIPED_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_MID_SKIPED_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_MIN_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_MIN_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_MAX_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_MAX_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_MIN_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_MIN_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_MAX_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_MAX_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_BAD_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_BAD_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_ABNORM_PLAY_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_ABNORM_PLAY_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_DC_FLAG,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_DC_FLAG},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_STAT_FEATURE_PV,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SHOW_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_SHOW_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_FOLLOW_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_FOLLOW_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_LIKE_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_LIKE_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_PLAY_CNT,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_SEARCH_PHOTO_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_CNT,
      ContextInfoCommonAttr::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_NOW_CNT,
      ContextInfoCommonAttr::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_NOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_PLAY_DURATION,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_SEARCH_PHOTO_PLAY_DURATION},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SHORTPLAY,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_DO_NOTHING_SHORTPLAY,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_DO_NOTHING_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_OR_FOLLOW_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_OR_FOLLOW_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_FOLLOW_OR_LIKE_SHORTPLAY,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_FOLLOW_OR_LIKE_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_FOLLOW,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_NO_COMP_FOLLOW,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_NO_COMP_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_NO_FOLLOW,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_NO_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_NO_COMP_NO_FOLLOW,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_NO_COMP_NO_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_NO_COMP_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_NO_COMP_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_NO_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_NO_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_NO_COMP_NO_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_NO_COMP_NO_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_ABNORM_REPEAT_PLAY_CNT,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_ABNORM_REPEAT_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_EARLY_SKIPED_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_EARLY_SKIPED_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_MID_SKIPED_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_MID_SKIPED_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_MIN_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_MIN_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_MAX_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_MAX_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_MIN_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_MIN_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_MAX_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_MAX_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_BAD_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_BAD_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_ABNORM_PLAY_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_ABNORM_PLAY_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_DC_FLAG,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_DC_FLAG},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::NGRAM_STAT_FEATURE_PV,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::PICSET_QUERY_CONSUM_PV,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::PICSET_QUERY_CONSUM_UV,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_UV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_SHOW_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_SHOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_PLAY_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_LONG_PLAY_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_LONG_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_CLICK_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_FOLLOW_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_FOLLOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_DOWNLOAD_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_DOWNLOAD_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_DOWNLOAD_FIRST_FRAME_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_DOWNLOAD_FIRST_FRAME_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_AVG_PLAY_DURATION,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_AVG_PLAY_DURATION},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_LIKE_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_LIKE_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_DOWNLOAD_CLICK_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_DOWNLOAD_CLICK_CNT}
    };
  static std::unordered_map<int64_t, int64_t> feas_map_str = {
      {::kuaishou::ad::search_ads::QueryFeaItemInfo::TF_IDF,
       ContextInfoCommonAttr::TF_IDF},
      {::kuaishou::ad::search_ads::QueryFeaItemInfo::INTENT_AUTHOR_NAME,
       ContextInfoCommonAttr::INTENT_AUTHOR_NAME},
      {::kuaishou::ad::search_ads::QueryFeaItemInfo::VERTICAL_SOURCE,
       ContextInfoCommonAttr::VERTICAL_SOURCE}};

bool SearchContextFeatureBuilder::InitProcessor() {
  context_table_name_common_ = "rank_context_feature_common";
  return true;
}

bool SearchContextFeatureBuilder::PvInit(ks::platform::AddibleRecoContextInterface* context) {
  // todo: add switch
  auto ps_context_wrapper = context->GetMutablePtrCommonAttr<AdContextWrapper>("ad_context_wrapper");
  if (!ps_context_wrapper || !ps_context_wrapper->Get()) {
    return false;
  }
  auto ps_context = ps_context_wrapper->Get();
  ad_context_ = ps_context->GetMutableContextData<ContextData>();
  if (!ad_context_) {return false;}
  return true;
}

void SearchContextFeatureBuilder::Mix(ks::platform::AddibleRecoContextInterface* context) {
  if (!PvInit(context)) {
    LOG_EVERY_N(ERROR, 10000) << "failed to init SearchContextFeatureBuilder";
    return;
  }

  auto* context_feature_table = context->GetOrInsertDataTable(context_table_name_common_);
  if (!context_feature_table) {
    LOG_EVERY_N(ERROR, 1000) << "BuildCommonContextFeatureTable: can not create context feature table.";
    return;
  }

  auto& reco_results = context_feature_table->GetCommonRecoResults();
  if (reco_results.size() != 1) {
    LOG_EVERY_N(ERROR, 1000) << "BuildInnerNativeContextFeatureTable: "
               << "common reco results size != 1";
    return;
  }
  auto& item = reco_results.back();
  FillContextInfo(context, context_feature_table, item);
}

void SearchContextFeatureBuilder::FillSearchPhotoInfo(::ks::platform::DataFrame *context_feature_table,
    const ::ks::platform::CommonRecoResult &item) {
  auto recall_features = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();
  recall_features->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_SEARCH_RECALL_FEATURE_SET);
  recall_features->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_MULTI_ATTR);
  auto* recall_features_attr = context_feature_table->GetOrInsertAttr(
    "ContextInfoCommonAttr_Name_SEARCH_RECALL_FEATURE_SET");
  item.SetExtraAttr(recall_features_attr, recall_features);
  auto* recall_features_map = recall_features->mutable_map_int64_multi_value();

  auto bidword = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();
  bidword->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_SEARCH_AD_BIDWORD);
  bidword->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_STRING_ATTR);
  auto* bidword_attr = context_feature_table->GetOrInsertAttr(
    "ContextInfoCommonAttr_Name_SEARCH_AD_BIDWORD");
  item.SetExtraAttr(bidword_attr, bidword);
  auto& bidword_map = (*bidword->mutable_map_int64_string_value());

  auto kbox_type_calc = [&] (const auto* p_ad) -> int {
    int ktype = 0;
    if (p_ad->get_is_search_celebrity()) {
      ktype = 1;
    } else if (p_ad->get_allow_search_app_card()) {
      ktype = 2;
    } else if (p_ad->get_allow_search_form_card()) {
      ktype = 3;
    } else if (p_ad->get_is_search_kbox_item()) {
      ktype = 4;
    } else if (p_ad->get_is_search_kbox_live()) {
      ktype = 5;
    } else if (p_ad->get_is_search_kbox_local_life()) {
      ktype = 6;
    } else if (p_ad->get_is_search_kbox_series()) {
      ktype = 7;
    } else if (p_ad->get_is_search_kbox_mini_game()) {
      ktype = 8;
    }
    return ktype;
  };

  auto set_list_fea = [&] (const AdList& ad_list) {
    for (size_t i = 0; i < ad_list.Size(); ++i) {
      auto *p_ad = ad_list.At(i);
      if (p_ad == nullptr)
        continue;
      int64 creative_id = p_ad->get_creative_id();
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_multi_retrieval_tag());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_multi_overlay_tag());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_multi_overlay_tag_extend());
      (*recall_features_map)[creative_id].add_int_value(
          static_cast<int64>(p_ad->get_search_relevance_score() * 1000));
      (*recall_features_map)[creative_id].add_int_value(static_cast<int64>(p_ad->get_match_type()));
      (*recall_features_map)[creative_id].add_int_value(static_cast<int64>(p_ad->get_sub_match_type()));
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_recall_strategy_type());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_sub_recall_strategy_type());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_extend_type());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_sub_extend_type());
      (*recall_features_map)[creative_id].add_int_value(static_cast<int64>(p_ad->get_qr_score() * 1000));
      std::string rewrite_query = p_ad->get_rewrite_query();
      int64 rewrite_query_sign = base::CityHash64(rewrite_query.c_str(), rewrite_query.length());
      (*recall_features_map)[creative_id].add_int_value(rewrite_query_sign);
      int ktype = kbox_type_calc(p_ad);
      (*recall_features_map)[creative_id].add_int_value(ktype);
      (*recall_features_map)[creative_id].add_int_value(
          p_ad->Attr(ItemIdx::is_search_series_card).GetIntValue(p_ad->AttrIndex()).value_or(0));
      bidword_map[creative_id] = p_ad->get_search_bidword();
    }
  };
  set_list_fea(ad_context_->get_ad_list());
  set_list_fea(ad_context_->get_live_ad_list());
  set_list_fea(ad_context_->get_native_ad_list());
  set_list_fea(ad_context_->get_fanstop_ad_list());
}

void SearchContextFeatureBuilder::FillSearchQueryToken(const ::kuaishou::ad::AdRequest &ad_request,
    ::ks::platform::DataFrame *context_feature_table,
    const ::ks::platform::CommonRecoResult &item) {
  if (ad_request.query_token_size() > 0) {
    auto query_token = std::make_shared<kuaishou::ad::ContextInfoCommonAttr>();
    query_token->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_SEARCH_QUERY_TOKEN);
    query_token->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_STRING_FLOAT_ATTR);
    auto* query_token_attr = context_feature_table->GetOrInsertAttr(
    "ContextInfoCommonAttr_Name_SEARCH_QUERY_TOKEN");
    item.SetExtraAttr(query_token_attr, query_token);

    auto& query_token_map = (*query_token->mutable_map_string_float_value());

    for (size_t i = 0; i < ad_request.query_token_size(); ++i) {
      const auto& qt = ad_request.query_token(i);
      query_token_map[qt.value()] = qt.weight();
    }
  }
}

void SearchContextFeatureBuilder::FillSearchReferPhotoId(const ::kuaishou::ad::AdRequest &ad_request,
    ::ks::platform::DataFrame *context_feature_table,
    const ::ks::platform::CommonRecoResult &item) {
  const auto& ext_params =
      ad_request.search_info().combo_search_params().ext_params();
  if (!ext_params.empty()) {
    base::Json ext_params_json(StringToJson(ext_params));
    uint64_t refer_photo_id = 0;
    if (ext_params_json.IsObject()) {
      base::StringToUint64(ext_params_json.GetString("refer_photo_id", "0"),
                           &refer_photo_id);
      if (refer_photo_id != 0) {
        ADD_CONTEXT_UINT_COMMON_ATTR(
                                 static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_REFER_PHOTO_ID),
                                 refer_photo_id);
      }
    }
  }
}

void SearchContextFeatureBuilder::FillSearchQuerySource(const ::kuaishou::ad::AdRequest &ad_request,
    ::ks::platform::DataFrame *context_feature_table,
    const ::ks::platform::CommonRecoResult &item) {
  const std::unordered_map<std::string, int64_t> query_source_map = {{"INSPIRE", 1}};
  int64_t query_source = 0;
  if (ad_request.search_info().ad_query_source() != "" &&
      query_source_map.count(ad_request.search_info().ad_query_source()) > 0) {
    query_source = query_source_map.at(ad_request.search_info().ad_query_source());
  }
  ADD_CONTEXT_INT_COMMON_ATTR(static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_AD_QUERY_SOURCE),
                           query_source);
}

void SearchContextFeatureBuilder::FillSearchQueryFeatureInfos(const ::kuaishou::ad::AdRequest &ad_request,
    ::ks::platform::DataFrame *context_feature_table,
    const ::ks::platform::CommonRecoResult &item) {
  const auto& query_feas = ad_request.search_query_transport_info().query_fea_infos();

  // 新增 query-hot-sequence 特征
  const auto& query_hot_sequence_info = ad_request.search_query_transport_info().query_hot_sequence_info();
  std::vector<int64_t> query_hot_seq_photo_ids;
  std::vector<int64_t> query_hot_seq_photo_imps;
  std::vector<int64_t> query_hot_seq_photo_clks;
  std::vector<int64_t> query_hot_seq_info_sources;
  std::vector<int64_t> query_hot_seq_item_types;

  // 新增 query 大模型语义 id 特征
  const auto& query_quantize_id_info = ad_request.search_query_transport_info().query_quantize_id();
  std::string query_quantize_id = "0";

  for (const auto &query_fea_info : query_feas) {
    // query 一级分类
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CAT_1) {
      std::vector<int64_t> query_cats;
      std::unordered_set<int64_t> query_sets;
      for (const auto& cat : query_fea_info.int_list_value()) {
        if (!query_sets.count(cat)) {
          query_cats.push_back(cat);
          query_sets.insert(cat);
        }
      }
      if (query_cats.size() > 0) {
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_QUERY_CATEGORY_CLASS1),
          query_cats);
      }
    }
    // query 二级分类
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CAT_2) {
      std::vector<int64_t> query_cats;
      std::unordered_set<int64_t> query_sets;
      for (const auto& cat : query_fea_info.int_list_value()) {
        if (!query_sets.count(cat)) {
          query_cats.push_back(cat);
          query_sets.insert(cat);
        }
      }
      if (query_cats.size() > 0) {
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_QUERY_CATEGORY_CLASS2),
          query_cats);
      }
    }
    // query 三级分类
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CAT_3) {
      std::vector<int64_t> query_cats;
      std::unordered_set<int64_t> query_sets;
      for (const auto& cat : query_fea_info.int_list_value()) {
        if (!query_sets.count(cat)) {
          query_cats.push_back(cat);
          query_sets.insert(cat);
        }
      }
      if (query_cats.size() > 0) {
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_QUERY_CATEGORY_CLASS3),
            query_cats);
      }
    }
    // relevance twin tower query embedding
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::SEARCH_QUERY_BERT_EMB) {
      std::vector<float> query_rele_emb;
      for (const auto& emb : query_fea_info.float_list_value()) {
        query_rele_emb.push_back(emb);
      }
      if (query_rele_emb.size() == 256) {
        ADD_CONTEXT_FLOAT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY_BERT_EMB),
          query_rele_emb);
      }
    }
    // query akg embedding
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::AKG_QUERY_EMB) {
      std::vector<float> akg_emb;
      for (const auto& emb : query_fea_info.float_list_value()) {
        akg_emb.push_back(emb);
      }
      if (akg_emb.size() == 64) {
        ADD_CONTEXT_FLOAT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_AKG_QUERY_EMBEDDING),
        akg_emb);
      }
    }
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QIN_SEARCH_EMB) {
      std::vector<float> qin_emb;
      for (const auto& emb : query_fea_info.float_list_value()) {
        qin_emb.push_back(emb);
      }
      if (qin_emb.size() == 64) {
        ADD_CONTEXT_FLOAT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QIN_QUERY_EMB),
          qin_emb);
      }
    }
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QIN_ITEM_LIST) {
      std::vector<int64_t> photo_ids, author_ids, durations, play_times,
          photo_tags, channels, labels, timestamps;
      for (const auto& qin_item : query_fea_info.qin_sim_items()) {
        photo_ids.push_back(qin_item.photo_id());
        author_ids.push_back(qin_item.author_id());
        durations.push_back(qin_item.duration());
        play_times.push_back(qin_item.play_time());
        photo_tags.push_back(qin_item.tag());
        channels.push_back(qin_item.channel());
        labels.push_back(qin_item.label());
        timestamps.push_back(qin_item.timestamp());
      }
      if (photo_ids.size() != 0 &&
          photo_ids.size() == author_ids.size() &&
          photo_ids.size() == durations.size() &&
          photo_ids.size() == play_times.size() &&
          photo_ids.size() == photo_tags.size() &&
          photo_ids.size() == channels.size() &&
          photo_ids.size() == labels.size() &&
          photo_ids.size() == timestamps.size()) {
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QIN_PHOTO_ID),
          photo_ids);
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QIN_AUTHOR_ID),
          author_ids);
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QIN_DURATION),
          durations);
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QIN_PLAY_TIME),
          play_times);
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QIN_PHOTO_TAG),
          photo_tags);
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QIN_CHANNEL),
          channels);
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QIN_LABEL),
          labels);
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QIN_TIMESTAMP),
          timestamps);
      }
    }

    if (feas_map.find(query_fea_info.name_value()) != feas_map.end()) {
      int64_t value = query_fea_info.int_value();
      auto context_name = feas_map[query_fea_info.name_value()];
      ADD_CONTEXT_INT_COMMON_ATTR(context_name,
                                  value);
    }

    if (feas_map_str.find(query_fea_info.name_value()) != feas_map_str.end()) {
      std::string value = query_fea_info.string_value();
      auto context_name = feas_map_str[query_fea_info.name_value()];
      ADD_CONTEXT_STRING_COMMON_ATTR(context_name,
                                  value);
    }

    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::CATEGORY_ID_LIST) {
      std::vector<int64_t> query_cats;
      std::unordered_set<int64_t> query_sets;
      for (const auto& cat : query_fea_info.int_list_value()) {
        if (!query_sets.count(cat)) {
          query_cats.push_back(cat);
          query_sets.insert(cat);
        }
      }
      if (query_cats.size() > 0) {
        ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::CATEGORY_ID_LIST),
          query_cats);
      }
    }
  }

  if (query_quantize_id_info.size() != 0) {
    // vector 第一个元素是大模型语义 id, 之后元素是改写 id
    query_quantize_id = query_quantize_id_info[0];
  }
  ADD_CONTEXT_STRING_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY_QUANTIZE_ID),
                      query_quantize_id);
  for (const auto &query_info_piece : query_hot_sequence_info.query_hot_sequence_detail_info()) {
    int64_t photo_id = static_cast<int64_t>(query_info_piece.photo_id());
    int64_t photo_imp_count = static_cast<int64_t>(query_info_piece.photo_imp_count());
    int64_t photo_clk_count = static_cast<int64_t>(query_info_piece.photo_clk_count());
    int64_t info_source = static_cast<int64_t>(query_info_piece.info_source());
    int64_t item_type = static_cast<int64_t>(query_info_piece.item_type());
    query_hot_seq_photo_ids.push_back(photo_id);
    query_hot_seq_photo_imps.push_back(photo_imp_count);
    query_hot_seq_photo_clks.push_back(photo_clk_count);
    query_hot_seq_info_sources.push_back(info_source);
    query_hot_seq_item_types.push_back(item_type);
  }
  if (query_hot_seq_photo_ids.size() != 0
      && query_hot_seq_photo_ids.size() == query_hot_seq_photo_imps.size()
      && query_hot_seq_photo_imps.size() == query_hot_seq_photo_clks.size()
      && query_hot_seq_photo_clks.size() == query_hot_seq_info_sources.size()
      && query_hot_seq_info_sources.size() == query_hot_seq_item_types.size()) {
    ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_PHOTO_ID_14D),
      query_hot_seq_photo_ids);
    ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_PHOTO_IMP_14D),
      query_hot_seq_photo_imps);
    ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_PHOTO_CLK_14D),
      query_hot_seq_photo_clks);
    ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_SOURCE_ID_14D),
      query_hot_seq_info_sources);
    ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_ITEM_TYPE_14D),
      query_hot_seq_item_types);
  }
}

void SearchContextFeatureBuilder::FillSearchPhotoInfoFromContext(
    ::ks::platform::MutableRecoContextInterface *context,
    const ::kuaishou::ad::AdRequest &ad_request,
    ::ks::platform::DataFrame *context_feature_table,
    const ::ks::platform::CommonRecoResult &item) {
    std::vector<int64_t> values;
    std::vector<int64_t> lengths;
    std::vector<int64_t> creative_ids;

    const auto* query_photo_feas = context->GetPtrCommonAttr<std::unordered_map<int64,
        kuaishou::ad::search_ads::QueryFeaInfo>>("photo_cross_feature_map");
    const auto* query_live_feas = context->GetPtrCommonAttr<std::unordered_map<int64,
        kuaishou::ad::search_ads::QueryFeaInfo>>("author_cross_feature_map");
    auto set_list_fea = [&] (const AdList& ad_list) {
      for (auto* p_ad : ad_list.Ads()) {
        int64 creative_id = p_ad->get_creative_id();
        auto author_id = p_ad->get_author_id();
        auto photo_id = p_ad->get_photo_id();
        int64_t total = 0;
        if (query_photo_feas) {
          auto photo_it = query_photo_feas->find(photo_id);
          if (photo_it != query_photo_feas->end()) {
            for (const auto & feature : photo_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        if (query_live_feas) {
          auto live_it = query_live_feas->find(author_id);
          if (live_it != query_live_feas->end()) {
            for (const auto & feature : live_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        creative_ids.push_back(creative_id);
        lengths.push_back(total);
      }
  };
  set_list_fea(ad_context_->get_ad_list());
  set_list_fea(ad_context_->get_live_ad_list());
  set_list_fea(ad_context_->get_fanstop_ad_list());
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY2AD_STAT_FEA), values);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY2AD_STAT_LENGTH), lengths);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
          static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_QUERY2AD_STAT_KEY), creative_ids);
}

// search info 提取的特征汇总在这个函数
void SearchContextFeatureBuilder::FillSearchFeaturesFromSearchInfor(
    const ::kuaishou::ad::AdRequest &ad_request,
    ::ks::platform::DataFrame *context_feature_table,
    const ::ks::platform::CommonRecoResult &item) {
    // 内流 trigger photo/author
    const auto inner_stream_params_v2 = ad_request.search_info().inner_stream_params_v2();
    if (!inner_stream_params_v2.empty()) {
      std::string inner_stream_params_v2_str;
      kuaishou::search::SeClientRealTimeActionList inner_stream_params_v2_pb;
      if (base::Base64Decode(inner_stream_params_v2, &inner_stream_params_v2_str)) {
        if (inner_stream_params_v2_pb.ParseFromString(inner_stream_params_v2_str)) {
            // see: message InnerStreamParams
            auto params = inner_stream_params_v2_pb.inner_stream_params();

            int64_t refer_photo_id = 0;
            if (absl::SimpleAtoi(params.refer_photo_id(), &refer_photo_id)) {
              ADD_CONTEXT_INT_COMMON_ATTR(
                static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_PHOTO_ID),
                                       refer_photo_id);
            }
            ADD_CONTEXT_UINT_COMMON_ATTR(
              static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_USER_ID),
                                     params.refer_user_id());
            int64_t refer_live_id = 0;
            if (absl::SimpleAtoi(params.refer_live_id(), &refer_live_id)) {
                ADD_CONTEXT_INT_COMMON_ATTR(
                  static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_LIVE_ID),
                                     refer_live_id);
            }
            int64_t refer_goods_id = 0;
            if (absl::SimpleAtoi(params.refer_goods_id(), &refer_goods_id)) {
              ADD_CONTEXT_INT_COMMON_ATTR(
                static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_GOODS_ID),
                                     refer_goods_id);
            }
            ADD_CONTEXT_INT_COMMON_ATTR(
              static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_POSITION),
                                     static_cast<int64_t>(params.pos()));
            ADD_CONTEXT_INT_COMMON_ATTR(
              static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_PAGE),
                                     static_cast<int64_t>(params.page()));
        }
      }
    }
}

void SearchContextFeatureBuilder::FillSearchQueryToAdInfoFromContext(
    ::ks::platform::MutableRecoContextInterface *context,
    const ::kuaishou::ad::AdRequest &ad_request,
    ::ks::platform::DataFrame *context_feature_table,
    const ::ks::platform::CommonRecoResult &item) {
    std::vector<int64_t> values;
    std::vector<int64_t> lengths;
    std::vector<int64_t> creative_ids;

    const auto* inner_q2a_feas = context->GetPtrCommonAttr<std::unordered_map<int64,
        kuaishou::ad::search_ads::QueryFeaInfo>>("inner_q2a_feature_map");
    const auto* outer_q2a_feas = context->GetPtrCommonAttr<std::unordered_map<std::string,
        kuaishou::ad::search_ads::QueryFeaInfo>>("outer_q2a_feature_map");
    const auto* serial_q2a_feas = context->GetPtrCommonAttr<std::unordered_map<int64,
        kuaishou::ad::search_ads::QueryFeaInfo>>("serial_q2a_feature_map");
    auto set_list_fea = [&] (const AdList& ad_list) {
      for (auto* p_ad : ad_list.Ads()) {
        if (p_ad == nullptr) {
          continue;
        }
        int64 creative_id = p_ad->get_creative_id();
        auto merchant_product_id = p_ad->get_merchant_product_id();
        auto product_name = p_ad->get_product_name();
        auto serial_id = p_ad->Attr(ItemIdx::fd_UNIT_series_id).GetIntValue(p_ad->AttrIndex()).value_or(0);

        int64_t total = 0;
        if (inner_q2a_feas) {
          auto merchant_product_it = inner_q2a_feas->find(merchant_product_id);
          if (merchant_product_it != inner_q2a_feas->end()) {
            for (const auto & feature : merchant_product_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        if (outer_q2a_feas) {
          auto product_name_it = outer_q2a_feas->find(product_name);
          if (product_name_it != outer_q2a_feas->end()) {
            for (const auto & feature : product_name_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        if (serial_q2a_feas) {
          auto serial_id_it = serial_q2a_feas->find(serial_id);
          if (serial_id_it != serial_q2a_feas->end()) {
            for (const auto & feature : serial_id_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        creative_ids.push_back(creative_id);
        lengths.push_back(total);
      }
  };
  set_list_fea(ad_context_->get_ad_list());
  set_list_fea(ad_context_->get_fanstop_ad_list());
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_Q2A_V2_ID), values);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_Q2A_V2_LENGTH), lengths);
  ADD_CONTEXT_INT_LIST_COMMON_ATTR(
    static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_Q2A_V2_KEY), creative_ids);
}

void SearchContextFeatureBuilder::FillContextInfo(::ks::platform::MutableRecoContextInterface *context,
    ::ks::platform::DataFrame *context_feature_table, const ::ks::platform::CommonRecoResult &item) {
  auto &ad_request = ad_context_->get_rank_request()->ad_request();
  if (ad_context_->get_pos_manager_base().IsSearchRequest()) {
    ADD_CONTEXT_STRING_COMMON_ATTR(static_cast<int32_t>(ContextInfoCommonAttr::QUERY),
                              ad_request.search_info().query());

    ADD_CONTEXT_STRING_COMMON_ATTR(static_cast<int32_t>(ContextInfoCommonAttr::TAB_KEYWORD),
                              ad_request.search_info().tab_keyword());

    ADD_CONTEXT_INT_COMMON_ATTR(static_cast<int32_t>(ContextInfoCommonAttr::PAGE_NUM),
                              static_cast<int64_t>(ad_request.search_info().page_num()));

    ADD_CONTEXT_INT_COMMON_ATTR(static_cast<int32_t>(ContextInfoCommonAttr::PAGE_SIZE),
                              static_cast<int64_t>(ad_request.search_info().page_size()));

    ADD_CONTEXT_INT_COMMON_ATTR(static_cast<int32_t>((ContextInfoCommonAttr::SEARCH_SRC)),
                              static_cast<int64_t>(ad_request.search_info().search_source()));

    ADD_CONTEXT_INT_COMMON_ATTR(static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_FROM_PAGE),
                              static_cast<int64_t>(ad_request.search_info().from_page()));

    ADD_CONTEXT_STRING_COMMON_ATTR(static_cast<int32_t>(ContextInfoCommonAttr::SEARCH_ENTER_SOURCE),
                              ad_request.search_info().combo_search_params().enter_source());

    FillSearchPhotoInfo(context_feature_table, item);

    FillSearchQueryToken(ad_request, context_feature_table, item);

    FillSearchReferPhotoId(ad_request, context_feature_table, item);

    FillSearchQuerySource(ad_request, context_feature_table, item);

    // query 侧特征收敛到这里
    FillSearchQueryFeatureInfos(ad_request, context_feature_table, item);

    if (SPDM_enableRankSearchCrossFeature()) {
      FillSearchPhotoInfoFromContext(context, ad_request, context_feature_table, item);
    }
    FillSearchFeaturesFromSearchInfor(ad_request, context_feature_table, item);
    FillSearchQueryToAdInfoFromContext(context, ad_request, context_feature_table, item);
  }
}

}   // namespace ad_rank
}   // namespace ks

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SearchContextFeatureBuilder, ::ks::ad_rank::SearchContextFeatureBuilder);
