#include "teams/ad/ad_rank_search/search/node/search_unify_cxr/plugin/amd_unify_cxr.h"

#include <math.h>
#include <algorithm>
#include "teams/ad/ad_rank_search/data/p2p_data/universe_conv_ratio_author_post_data_p2p/universe_conv_ratio_author_post_data_p2p.h"

namespace ks {
namespace ad_rank {

const char* SearchAmdUnifyCxr::Name() {
  return "SearchAmdUnifyCxr";
}

void SearchAmdUnifyCxr::Clear() {}

bool SearchAmdUnifyCxr::IsRun(
    const ContextData* session_data, const Params* params,
    AdRankUnifyScene pos, const AdList* ad_list) {
  return ad_list->Size() > 0;
}

StraRetCode SearchAmdUnifyCxr::Process(ContextData* session_data,
        Params* params, AdRankUnifyScene pos, AdList* ad_list) {
  auto* reserve_threshold_params = dynamic_cast<AmdReserveThresholdParams*>(params);
  ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(reserve_threshold_params, StraRetCode::SUCC);
  for (auto* p_ad : ad_list->Ads()) {
    if (p_ad == nullptr) {
      continue;
    }
    SEARCH_AD_LIST_SKIP_PHOTO_AD(session_data, p_ad)
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad)
    // 只处理直播广告
    bool is_live = (p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD);
    if (session_data->IsSplashTraffic()) {
      is_live = p_ad->Is(AdFlag::is_amd_live_campaign) || p_ad->get_queue_type() == RankAdListType::NORMAL_LIVE_AD;  // NOLINT
    }
    if (!is_live) {
      continue;
    }
    if (session_data->IsSplashTraffic()) {
      GetUnifyRate(session_data, reserve_threshold_params, p_ad);
    } else {
      GetUnifyRateV2(session_data, reserve_threshold_params, p_ad);
    }
    EnableUnifyCxr(session_data, reserve_threshold_params, p_ad);
    LOG_EVERY_N(INFO, 1000000) << "unify_cxr_strategy amd.ctr=" << p_ad->get_unify_ctr()
      << ",amd.cvr=" << p_ad->get_unify_cvr()
      << ",p_ad_unify_ctr=" << p_ad->get_unify_ctr_info().value
      << ",cvr=" << p_ad->get_unify_cvr_info().value
      << ",llsid=" << session_data->get_llsid()
      << ",user_id=" <<  session_data->get_user_id()
      << ",unit_id=" <<  p_ad->get_unit_id()
      << ",creative_id=" << p_ad->get_creative_id();
  }
  return StraRetCode::SUCC;
}

void SearchAmdUnifyCxr::GetUnifyRateV2(ContextData* session_data_,
        AmdReserveThresholdParams* params, AdCommon *p_ad) {
  auto &ad = *p_ad;
  // 单列
  bool is_single_col = session_data_->get_is_thanos_request();
  bool is_live = ad.Is(AdFlag::is_amd_live_campaign);
  const auto& order_paied_jingxuan_cali_map = RankKconfUtil::orderPaiedJingXuanCaliMap();
  const auto& roas_jingxuan_cali_map = RankKconfUtil::roasJingXuanCaliMap();

  double origin_server_client_show_rate =
      ad.get_predict_score(engine_base::PredictType::PredictType_server_client_show_rate);
  double server_show_ctr = ad.get_server_show_ctr();

  if (session_data_->get_rank_request()->ad_request().search_info().request_source() == 2) {
    // 搜索激励广告盒子双列任务 sctr 默认为 1.0
    if (session_data_->get_pos_manager_base().IsSearchInspireFeedRequest()) {
      origin_server_client_show_rate = 1.0;
      server_show_ctr = 1.0;
    }
  }

  int32_t server_client_show_rate_cmd_id =
      ad.get_predict_cmd_id(engine_base::PredictType::PredictType_server_client_show_rate);

  if (is_live) {
    double ctr = 0.0;
    if (ad.Is(AdFlag::is_amd_direct_live)) {  // 直投
      // 搜索单列进人率为 1
      if (session_data_->get_is_search_request() && is_single_col && params->enable_search_sigle) {
        ctr = server_show_ctr;
      } else {
        ctr = server_show_ctr * ad.get_live_audience();
      }
      ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                     kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                     ad.get_server_show_ctr_cmd_id(),
                     ad.get_live_audience_cmd_id());
    } else {  // 作品引流
      if (!is_single_col) {  // 双列
        ctr = origin_server_client_show_rate * ad.get_ctr() * ad.get_cvr();
        if (SPDM_enable_search_p2l_to_live_ecpm(session_data_->get_spdm_ctx())) {
          ctr = origin_server_client_show_rate * ad.get_ctr();
        }
        ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
            kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
            server_client_show_rate_cmd_id, ad.get_ctr_cmd_id());
      } else {   // 单列 作品引流
        // 搜索单双列请求同一个模型
        if (session_data_->get_is_search_request() && params->enable_search_sigle) {
          ctr = origin_server_client_show_rate * ad.get_cvr();
        } else {
          ctr = server_show_ctr * ad.get_cvr();
        }
        ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                       kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                       ad.get_server_show_ctr_cmd_id(), ad.get_cvr_cmd_id());
      }
    }
  }

  switch (ad.get_ocpx_action_type()) {
    case kuaishou::ad::AD_MERCHANT_FOLLOW:
    case kuaishou::ad::AD_MERCHANT_FOLLOW_FAST:
    case kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY:
    case kuaishou::ad::AD_FANS_TOP_FOLLOW:
      if (is_live) {
        if (ad.Is(AdFlag::is_amd_direct_live)) {
          ad.SetUnifyCtr(server_show_ctr, kuaishou::ad::AD_DELIVERY,
                         kuaishou::ad::AD_LIVE_IMPRESSION,
                         ad.get_server_show_ctr_cmd_id());
          ad.SetUnifyCvr(ad.get_live_p3s_wtr(), kuaishou::ad::AD_LIVE_IMPRESSION,
                         ad.get_ocpx_action_type(),
                         ad.get_live_p3s_wtr_cmd_id());
        } else {
          if (is_single_col) {
            ad.SetUnifyCtr(server_show_ctr, kuaishou::ad::AD_DELIVERY,
                           kuaishou::ad::AD_ITEM_IMPRESSION,
                           ad.get_server_show_ctr_cmd_id());
            ad.SetUnifyCvr(ad.get_item_impression_wtr(),
                           kuaishou::ad::AD_ITEM_IMPRESSION,
                           ad.get_ocpx_action_type(),
                           ad.get_item_impression_wtr_cmd_id());
          } else {
            ad.SetUnifyCtr(
                origin_server_client_show_rate * ad.get_ctr(),
                kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                server_client_show_rate_cmd_id, ad.get_ctr_cmd_id());
            ad.SetUnifyCvr(ad.get_item_impression_wtr(),
                           kuaishou::ad::AD_ITEM_IMPRESSION,
                           ad.get_ocpx_action_type(),
                           ad.get_item_impression_wtr_cmd_id());
          }
        }
      }
      break;
    case kuaishou::ad::EVENT_ORDER_PAIED:
    case kuaishou::ad::CID_EVENT_ORDER_PAID:
      if (is_live) {
        // ad.set_unify_cvr(ad.get_live_order_paid());
        // 针对作品引流，模型同时请求了直投模型和作品引流模型
        // 后续会将 ad.get_photo2live_pay_rate() 去掉，只保留 ad.get_live_order_paid()
        double c1_order_paied_rate = 1.0;
        // 主站精选页
        if (session_data_->get_sub_page_id() == 10011001) {
            auto iter = order_paied_jingxuan_cali_map->find(ad.get_author_id());
            if (iter != order_paied_jingxuan_cali_map->end()) {
                c1_order_paied_rate = iter->second;
            }
        }
        if (ad.Is(AdFlag::is_amd_direct_live)) {
          if (session_data_->get_is_search_good_card()) {
            // 搜索商品卡
            if (!SPDM_disable_search_goods_live_ecpm(session_data_->get_spdm_ctx())) {
              double ctr = ad.get_live_audience();
              ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                      kuaishou::ad::AD_LIVE_CLICK,
                      ad.get_live_audience_cmd_id());
              ad.SetUnifyCvr(ad.get_live_order_paid() * c1_order_paied_rate,
                        kuaishou::ad::AD_LIVE_CLICK,
                ad.get_ocpx_action_type(), ad.get_live_order_paid_cmd_id());
            } else {
              if (SPDM_enable_search_goods_tab_fix_bug(session_data_->get_spdm_ctx())) {
                ad.SetUnifyCvr(ad.get_live_order_paid() * c1_order_paied_rate,
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_ocpx_action_type(), ad.get_live_order_paid_cmd_id());
              }
            }
          } else {
            ad.SetUnifyCvr(ad.get_live_order_paid() * c1_order_paied_rate,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                ad.get_ocpx_action_type(), ad.get_live_order_paid_cmd_id());
          }
        } else {
          ad.SetUnifyCvr(ad.get_photo2live_pay_rate() * c1_order_paied_rate,
               kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
              ad.get_ocpx_action_type(), ad.get_live_order_paid_cmd_id());

          // 垂搜短引转直投, ctr 走垂搜，cvr 走综搜
          if (SPDM_enable_goods_p2l_to_direct(session_data_->get_spdm_ctx()) &&
              session_data_->get_is_search_good_card()) {
            double ctr = ad.get_ctr();
            double cvr = ad.get_live_order_paid();
            ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                      kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                      ad.get_ctr_cmd_id());
            ad.set_cvr(1.0);  // 样式策略会做一些 mock, 在这里直接屏蔽进人概率
            ad.SetUnifyCvr(cvr,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        kuaishou::ad::EVENT_ORDER_PAIED,
                        ad.get_live_order_paid_cmd_id());
          }
        }
      }
      break;
    case kuaishou::ad::AD_MERCHANT_ROAS:
    case kuaishou::ad::AD_MERCHANT_T7_ROI:
    case kuaishou::ad::AD_STOREWIDE_ROAS:
    case kuaishou::ad::AD_FANS_TOP_ROI:
    case kuaishou::ad::AD_LIVE_AUDIENCE:
    case kuaishou::ad::AD_LIVE_AUDIENCE_QUALITY:
    case kuaishou::ad::AD_AUDIENCE_FAST:
    case kuaishou::ad::AD_LIVE_AUDIENCE_FAST:
    case kuaishou::ad::CID_ROAS:
      if (is_live) {
        double c1_order_paied_rate = 1.0;
        // 主站精选页
        if (session_data_->get_sub_page_id() == 10011001
            && (ad.get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
                ad.get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI)) {
            auto iter = roas_jingxuan_cali_map->find(ad.get_author_id());
            if (iter != roas_jingxuan_cali_map->end()) {
                c1_order_paied_rate = iter->second;
            }
        }
        ad.SetUnifyCvr(1.0 * c1_order_paied_rate, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
              kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED);
      }
      break;
    default:
      break;
  }


  {
    if (session_data_->get_is_search_good_card() && ad.Is(AdFlag::is_inner_live_roas)) {
        // 搜索商品卡
        if (!SPDM_disable_search_goods_live_ecpm(session_data_->get_spdm_ctx())) {
          double ctr = ad.get_live_audience();
          ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                      kuaishou::ad::AD_LIVE_CLICK,
                      ad.get_live_audience_cmd_id());
          ad.SetUnifyLtv(ad.get_live_p3s_ltv(), kuaishou::ad::AD_LIVE_CLICK, kuaishou::ad::AD_MERCHANT_ROAS,
                      ad.get_live_p3s_ltv_cmd_id());
        }

        // 垂搜短引转直投, ctr 走垂搜，cvr 走综搜
        if (!ad.Is(AdFlag::is_amd_direct_live) &&
            SPDM_enable_goods_p2l_to_direct(session_data_->get_spdm_ctx())) {
          double ctr = ad.get_ctr();
          double cvr = 1.0;
          double ltv = ad.get_live_p3s_ltv();
          ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                    ad.get_ctr_cmd_id());
          ad.set_cvr(1.0);  // 样式策略会做一些 mock, 在这里直接屏蔽进人概率
          ad.SetUnifyCvr(cvr,
                      kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                      kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED);
          ad.SetUnifyLtv(ltv,
                      kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                      ad.get_ocpx_action_type(),
                      ad.get_live_p3s_ltv_cmd_id());
        }
    } else {
      ad.SetUnifyLtv(ad.get_live_p3s_ltv(), kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_MERCHANT_ROAS,
                     ad.get_live_p3s_ltv_cmd_id());
    }
    // 搜索 7 日 ROI
    if (ad.Is(AdFlag::is_inner_live_t7_roi) &&
        session_data_->get_is_search_request()) {
      if (session_data_->get_is_search_good_card() &&
          SPDM_enable_card_live_T7(session_data_->get_spdm_ctx())) {
        if (!SPDM_disable_search_goods_live_ecpm(session_data_->get_spdm_ctx())) {
          double ctr = ad.get_live_audience();
          ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_LIVE_CLICK,
                    ad.get_live_audience_cmd_id());
        }

        // 垂搜短引转直投, ctr 走垂搜，cvr 走综搜
        if (!ad.Is(AdFlag::is_amd_direct_live) &&
            SPDM_enable_goods_p2l_to_direct(session_data_->get_spdm_ctx())) {
          double ctr = ad.get_ctr();
          double cvr = 1.0;
          ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                    ad.get_ctr_cmd_id());
          ad.set_cvr(1.0);  // 样式策略会做一些 mock, 在这里直接屏蔽进人概率
          ad.SetUnifyCvr(cvr,
                      kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                      kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED);
        }
      }
      // 0h ~ 2 hour : realtime model
      double gmv_0h_2h = ad.get_predict_score(PredictType::PredictType_inner_live_roas_7days_0_2h);
      // 2h ~ 7 days : daily model
      double gmv_2h_7d = ad.get_predict_score(PredictType::PredictType_inner_live_roas_7days_3d_7d);
      ad.SetUnifyLtv(gmv_0h_2h+gmv_2h_7d, kuaishou::ad::AD_ITEM_IMPRESSION,
      kuaishou::ad::AD_MERCHANT_ROAS);
    }

    /* 本地全站搜索 GMV 模型 PCOC 校准 */
    if (session_data_->get_is_search_request() && params->enable_lsp_storewide_search_pcoc_cali) {
      auto it = params->lsp_storewide_author_search_pcoc_cali_conf->find(ad.get_author_id());
      if (it != params->lsp_storewide_author_search_pcoc_cali_conf->end()) {
        double cali_ratio = it->second;
        cali_ratio = std::max(cali_ratio, params->lsp_storewide_search_pcoc_cali_lower_bound);
        cali_ratio = std::min(cali_ratio, params->lsp_storewide_search_pcoc_cali_upper_bound);
        double gmv = ad.get_live_p3s_ltv() * cali_ratio;
        ad.SetUnifyLtv(gmv, kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_MERCHANT_ROAS,
                       ad.get_live_p3s_ltv_cmd_id());
      }
    }
    /* 本地全站 GMV 模型 PCOC 校准 */
  }

  RANK_DOT_COUNT(session_data_, 1, "ad_rank.inner_hard_amd_unify",
                 kuaishou::ad::AdEnum::ItemType_Name(ad.get_item_type()),
                 kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type()));
  if (ad.get_unify_ctr_info().value < FLT_EPSILON) {
    RANK_DOT_COUNT(session_data_, 1, "ad_rank.inner_hard_live_unify_ctr_zero",
                   kuaishou::ad::AdEnum::ItemType_Name(ad.get_item_type()),
                   kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type()));
  }
  if (ad.get_unify_cvr_info().value < FLT_EPSILON) {
    RANK_DOT_COUNT(session_data_, 1, "ad_rank.inner_hard_live_unify_cvr_zero",
                   kuaishou::ad::AdEnum::ItemType_Name(ad.get_item_type()),
                   kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type()));
  }
  if (ad.get_unify_ltv_info().value < FLT_EPSILON) {
    RANK_DOT_COUNT(session_data_, 1, "ad_rank.inner_hard_live_unify_ltv_zero",
                   kuaishou::ad::AdEnum::ItemType_Name(ad.get_item_type()),
                   kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type()));
  }
}

void SearchAmdUnifyCxr::GetUnifyRate(ContextData* session_data_,
        AmdReserveThresholdParams* params, AdCommon *p_ad) {{
  double live_audience_coef = 1.0;
  double live_p3s_ltv_coef = 1.0;
  double live_order_paid_coef = 1.0;
  const auto& order_paied_jingxuan_cali_map = RankKconfUtil::orderPaiedJingXuanCaliMap();
  const auto& roas_jingxuan_cali_map = RankKconfUtil::roasJingXuanCaliMap();
  auto &ad = *p_ad;
  double default_splash_server_show_ctr = ad.get_server_show_ctr();
  bool is_live = ad.Is(AdFlag::is_amd_live_campaign);
  double click2_deep_rate = ad.get_predict_score(PredictType::PredictType_click2_deep_rate);
  auto click2_deep_rate_cmd_id = ad.get_predict_cmd_id(PredictType::PredictType_click2_deep_rate);
  switch (ad.get_ocpx_action_type()) {
    case kuaishou::ad::AD_CONVERSION:
      ad.SetUnifyCtr(default_splash_server_show_ctr, kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_IMPRESSION);
      ad.SetUnifyCvr(ad.get_app_conversion_rate(), kuaishou::ad::AD_ITEM_IMPRESSION,
          ad.get_ocpx_action_type(), ad.get_app_conversion_rate_cmd_id());
      break;
    case kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED:
      ad.SetUnifyCtr(default_splash_server_show_ctr, kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_IMPRESSION);
      ad.SetUnifyCvr(ad.get_landingpage_submit_rate(), kuaishou::ad::AD_ITEM_IMPRESSION,
          ad.get_ocpx_action_type(), ad.get_landingpage_submit_rate_cmd_id());
      break;
    case kuaishou::ad::AD_ITEM_CLICK:
      ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
      ad.SetUnifyCvr(1.0,
          ad.get_ocpx_action_type(), ad.get_ocpx_action_type());
      break;
    case kuaishou::ad::EVENT_APP_INVOKED:
      ad.SetUnifyCtr(default_splash_server_show_ctr, kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_IMPRESSION);
      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click_app_invoked),
          kuaishou::ad::AD_ITEM_IMPRESSION,
          ad.get_ocpx_action_type(),
          ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked));
      break;
    case kuaishou::ad::EVENT_APPOINT_FORM:
    case kuaishou::ad::EVENT_APPOINT_JUMP_CLICK:
      ad.SetUnifyCtr(ad.get_server_show_ctr(),
          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
          ad.get_server_show_ctr_cmd_id());
      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_c2_game_appoint_rate),
          kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
          ad.get_predict_cmd_id(PredictType::PredictType_c2_game_appoint_rate));
      break;
    case kuaishou::ad::EVENT_REGISTER:
    case kuaishou::ad::EVENT_ORDER_SUBMIT:
      ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
      ad.SetUnifyCvr(click2_deep_rate,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
          click2_deep_rate_cmd_id);
      break;
    case kuaishou::ad::AD_PURCHASE:
      ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
                     kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click2_purchase_rate_single_bid),  // NOLINT
                     kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
                     ad.get_predict_cmd_id(PredictType::PredictType_click2_purchase_rate_single_bid));  // NOLINT
      break;
    case kuaishou::ad::EVENT_PAY_UNION:
    case kuaishou::ad::AD_MERCHANT_ROAS:
    case kuaishou::ad::CID_ROAS:
    case kuaishou::ad::AD_MERCHANT_T7_ROI:
    case kuaishou::ad::AD_STOREWIDE_ROAS:
      if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
        if (ad.get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE) {
          ad.SetUnifyCtr(ad.get_live_server_show_play_3s_slide(), kuaishou::ad::AD_DELIVERY,
                         kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_live_server_show_play_3s_slide_cmd_id());
        } else {
          ad.SetUnifyCtr(ad.get_live_audience() * live_audience_coef, kuaishou::ad::AD_DELIVERY,
                kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                ad.get_server_show_ctr_cmd_id(), ad.get_cvr_cmd_id());
        }
        double c1_order_paied_rate = 1.0;
        // 主站精选页
        if (session_data_->get_sub_page_id() == 10011001) {
            auto iter = roas_jingxuan_cali_map->find(ad.get_author_id());
            if (iter != roas_jingxuan_cali_map->end()) {
                c1_order_paied_rate = iter->second;
            }
        }
        ad.SetUnifyCvr(ad.get_live_p3s_ltv() * live_p3s_ltv_coef * c1_order_paied_rate,
            kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
            ad.get_ocpx_action_type());
      } else {
        ad.SetUnifyCtr(default_splash_server_show_ctr,
                       kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION);
        ad.SetUnifyCvr(ad.get_c1_order_paied(),
                      kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                      ad.get_c1_order_paied_cmd_id());
      }
      break;
    case kuaishou::ad::AD_ROAS:
      ad.SetUnifyCtr(default_splash_server_show_ctr, kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_IMPRESSION);
      ad.SetUnifyCvr(ad.get_app_conversion_rate(),
          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
          ad.get_app_conversion_rate_cmd_id());
      break;
    case kuaishou::ad::AD_MERCHANT_FOLLOW:
    case kuaishou::ad::AD_MERCHANT_FOLLOW_FAST:
    case kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY:
      if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
        if (ad.get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE) {
          ad.SetUnifyCtr(ad.get_live_server_show_play_3s_slide(), kuaishou::ad::AD_DELIVERY,
                         kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_live_server_show_play_3s_slide_cmd_id());
          ad.SetUnifyCvr(ad.get_live_p3s_wtr(), kuaishou::ad::AD_LIVE_PLAYED_3S,
                         ad.get_ocpx_action_type(), ad.get_live_p3s_wtr_cmd_id());
        } else {
          ad.SetUnifyCtr(default_splash_server_show_ctr, kuaishou::ad::AD_DELIVERY,
                         kuaishou::ad::AD_ITEM_IMPRESSION);
          ad.SetUnifyCvr(ad.get_item_impression_wtr(), kuaishou::ad::AD_ITEM_IMPRESSION,
                         ad.get_ocpx_action_type(), ad.get_item_impression_wtr_cmd_id());
        }
      } else {
        ad.SetUnifyCtr(default_splash_server_show_ctr, kuaishou::ad::AD_DELIVERY,
                       kuaishou::ad::AD_ITEM_IMPRESSION);
        ad.SetUnifyCvr(ad.get_nebula_merchant_follow(), kuaishou::ad::AD_ITEM_IMPRESSION,
                       ad.get_ocpx_action_type(), ad.get_nebula_merchant_follow_cmd_id());
      }
      break;
    case kuaishou::ad::EVENT_NEXTDAY_STAY:
      ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
      ad.SetUnifyCvr(click2_deep_rate,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
          click2_deep_rate_cmd_id);
      break;
    case kuaishou::ad::EVENT_VALID_CLUES:
      ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
      ad.SetUnifyCvr(click2_deep_rate,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
          click2_deep_rate_cmd_id);
      break;
    case kuaishou::ad::EVENT_MULTI_CONVERSION:
      ad.SetUnifyCtr(default_splash_server_show_ctr, kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_IMPRESSION);
      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
          kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
          ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
      break;
    case kuaishou::ad::EVENT_7_DAY_PAY_TIMES:
      ad.SetUnifyCtr(default_splash_server_show_ctr,
                     kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION);
      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click2_purchase_rate_single_bid),  // NOLINT
                     kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                     ad.get_predict_cmd_id(PredictType::PredictType_click2_purchase_rate_single_bid));  // NOLINT
      break;
    case kuaishou::ad::EVENT_ADD_WECHAT:
      ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
      ad.SetUnifyCvr(click2_deep_rate,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
          click2_deep_rate_cmd_id);
      break;
    case kuaishou::ad::AD_APPROXIMATE_PURCHASE:
      ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
      ad.SetUnifyCvr(ad.get_click2_prod_apr(),
          kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
          ad.get_click2_prod_apr_cmd_id());
      break;
    case kuaishou::ad::EVENT_ORDER_PAIED:
    case kuaishou::ad::CID_EVENT_ORDER_PAID:
      if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
        if (ad.get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE) {
          ad.SetUnifyCtr(ad.get_live_server_show_play_3s_slide(), kuaishou::ad::AD_DELIVERY,
                         kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_live_server_show_play_3s_slide_cmd_id());
        } else if (ad.get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_CREATIVE_TYPE) {
          ad.SetUnifyCtr(ad.get_photo2live_pay_rate() * live_audience_coef, kuaishou::ad::AD_DELIVERY,
                kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                ad.get_photo2live_pay_rate_cmd_id());
        } else {
          ad.SetUnifyCtr(ad.get_live_audience() * live_audience_coef, kuaishou::ad::AD_DELIVERY,
                kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                ad.get_server_show_ctr_cmd_id(), ad.get_cvr_cmd_id());
        }
        double c1_order_paied_rate = 1.0;
        // 主站精选页
        if (session_data_->get_sub_page_id() == 10011001) {
            auto iter = order_paied_jingxuan_cali_map->find(ad.get_author_id());
            if (iter != order_paied_jingxuan_cali_map->end()) {
                c1_order_paied_rate = iter->second;
            }
        }
        ad.SetUnifyCvr(ad.get_live_order_paid() * live_order_paid_coef * c1_order_paied_rate,
            kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
            ad.get_ocpx_action_type(), ad.get_live_order_paid_cmd_id());
      } else {
        ad.SetUnifyCtr(default_splash_server_show_ctr, kuaishou::ad::AD_DELIVERY,
                       kuaishou::ad::AD_ITEM_IMPRESSION);
        ad.SetUnifyCvr(ad.get_c1_order_paied(),
                       kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                       ad.get_c1_order_paied_cmd_id());
      }
      break;
    case kuaishou::ad::EVENT_GOODS_VIEW:
      if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE) {
        if (ad.get_live_creative_type() ==
            kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE) {
          ad.SetUnifyCtr(ad.get_live_server_show_play_3s_slide(), kuaishou::ad::AD_DELIVERY,
                         kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_live_server_show_play_3s_slide_cmd_id());
        } else {
          ad.SetUnifyCtr(ad.get_server_show_ctr() * ad.get_cvr(), kuaishou::ad::AD_DELIVERY,
                         kuaishou::ad::AD_LIVE_PLAYED_3S,
                         ad.get_server_show_ctr_cmd_id(), ad.get_cvr_cmd_id());
        }
        ad.SetUnifyCvr(ad.get_live_goods_view(), kuaishou::ad::AD_LIVE_PLAYED_3S,
                       ad.get_ocpx_action_type(), ad.get_live_goods_view_cmd_id());
      } else {
        ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
        ad.SetUnifyCvr(1.0,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type());
      }
      break;
    case kuaishou::ad::EVENT_JINJIAN:
    case kuaishou::ad::AD_CREDIT_GRANT:
      ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
      ad.SetUnifyCvr(click2_deep_rate,
          kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
          click2_deep_rate_cmd_id);
      break;
    case kuaishou::ad::AD_LIVE_PLAYED_3S:
      if (ad.get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE) {
        ad.SetUnifyCtr(ad.get_live_server_show_play_3s_slide(), kuaishou::ad::AD_DELIVERY,
                       kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_live_server_show_play_3s_slide_cmd_id());
      } else {
        ad.SetUnifyCtr(ad.get_server_show_ctr() * ad.get_cvr(), kuaishou::ad::AD_DELIVERY,
                       kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_server_show_ctr(), ad.get_cvr_cmd_id());
      }
      ad.SetUnifyCvr(1.0, ad.get_ocpx_action_type(),
                     ad.get_ocpx_action_type());
      break;
    case kuaishou::ad::AD_LIVE_AUDIENCE:
    case kuaishou::ad::AD_LIVE_AUDIENCE_QUALITY:
    case kuaishou::ad::AD_AUDIENCE_FAST:
    case kuaishou::ad::AD_LIVE_AUDIENCE_FAST:
      if (is_live) {
        double ctr = 0.0;
        if (ad.Is(AdFlag::is_amd_direct_live)) {  // 直投
          ctr = ad.get_server_show_ctr() * ad.get_live_audience();
          ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                              kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                              ad.get_server_show_ctr_cmd_id(),
                              ad.get_live_audience_cmd_id());
        } else {
          ad.SetUnifyCtr(ad.get_live_audience() * live_audience_coef, kuaishou::ad::AD_DELIVERY,
                              kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                              ad.get_server_show_ctr_cmd_id(), ad.get_cvr_cmd_id());
        }
        ad.SetUnifyCvr(1.0, ad.get_ocpx_action_type(), ad.get_ocpx_action_type());
      }
      break;
    default:
      ad.SetUnifyCtr(default_splash_server_show_ctr, kuaishou::ad::AD_DELIVERY,
          kuaishou::ad::AD_ITEM_IMPRESSION);
      ad.SetUnifyCvr(ad.get_app_conversion_rate(), kuaishou::ad::AD_ITEM_IMPRESSION,
          ad.get_ocpx_action_type(), ad.get_app_conversion_rate_cmd_id());
      break;
  }
  switch (ad.get_deep_conversion_type()) {
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY:
      ad.SetUnifyDeepCvr(ad.get_conv_nextstay(), kuaishou::ad::AD_CONVERSION,
                         kuaishou::ad::AdActionType::EVENT_NEXTDAY_STAY,
                         ad.get_conv_nextstay_cmd_id());
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY:
      ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                         kuaishou::ad::AD_CONVERSION,
                         kuaishou::ad::AdActionType::EVENT_PAY,
                         ad.get_predict_cmd_id(PredictType::PredictType_purchase));
      break;
    default:
      break;
  }
  double server_show_client_ratio =
    session_data_->get_spdm_ctx().TryGetDouble("server_show_client_ratio", 0.6);
  ad.ModifySplashRtbUnifyCtrPv(RUnifyTag::AD_SCTR_RESET, server_show_client_ratio, 1.0, 0.0);
  }
}

void SearchAmdUnifyCxr::EnableUnifyCxr(ContextData* session_data_,
        AmdReserveThresholdParams* params, AdCommon *p_ad) {
  // 直接推全类型
  switch (p_ad->get_ocpx_action_type()) {
    case kuaishou::ad::EVENT_APPOINT_FORM:
    case kuaishou::ad::EVENT_APPOINT_JUMP_CLICK:
    case kuaishou::ad::EVENT_AD_WATCH_TIMES:
    case kuaishou::ad::EVENT_GOODS_VIEW:
    case kuaishou::ad::AD_MERCHANT_FOLLOW:
    case kuaishou::ad::AD_MERCHANT_FOLLOW_FAST:
    case kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY:
    case kuaishou::ad::EVENT_ORDER_PAIED:
    case kuaishou::ad::CID_EVENT_ORDER_PAID:
    case kuaishou::ad::AD_LIVE_PLAYED_3S:
    case kuaishou::ad::AD_LIVE_AUDIENCE:
    case kuaishou::ad::AD_LIVE_AUDIENCE_QUALITY:
    case kuaishou::ad::AD_LIVE_AUDIENCE_FAST:
    case kuaishou::ad::AD_AUDIENCE_FAST:
      p_ad->set_enable_unify_rvalue(true);
      break;
    case kuaishou::ad::AD_MERCHANT_ROAS:
    case kuaishou::ad::CID_ROAS:
    case kuaishou::ad::AD_MERCHANT_T7_ROI:
    case kuaishou::ad::AD_STOREWIDE_ROAS:
    case kuaishou::ad::AD_FANS_TOP_ROI:
      p_ad->set_enable_unify_rvalue(false);
      break;
    default:
      p_ad->set_enable_unify_rvalue(false);
      break;
  }
}
}  // namespace ad_rank
}  // namespace ks
