#include "teams/ad/ad_rank_search/search/node/search_unify_cxr/plugin/native_unify_cxr.h"

#include <string>
#include <algorithm>
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_rank_search/processor/factor/ranking_data.h"
#include "teams/ad/ad_rank_search/common/ad_common.h"
#include "teams/ad/ad_rank_search/common/context_data.h"
#include "teams/ad/ad_rank_search/default/params/native_unify_fill_cxr_params.h"
#include "teams/ad/ad_rank_search/data/p2p_data/universe_conv_ratio_author_post_data_p2p/universe_conv_ratio_author_post_data_p2p.h"

using kuaishou::ad::AdEnum;
using kuaishou::ad::AdActionType;
using kuaishou::ad::AdActionType_Name;
using ks::engine_base::PredictType;

namespace ks {
namespace ad_rank {

const char* SearchNativeUnifyFillCxr::Name() {
  return "SearchNativeUnifyFillCxr";
}

void SearchNativeUnifyFillCxr::Clear() {}

bool SearchNativeUnifyFillCxr::IsRun(
    const ContextData* session_data, const Params* params,
    AdRankUnifyScene pos, const AdList* ad_list) {
  return ad_list->Size() > 0;
}

StraRetCode SearchNativeUnifyFillCxr::Process(
    ContextData* session_data, Params* params, AdRankUnifyScene pos, AdList* ad_list) {
  NativeUnifyFillCxrParams* cxr_params = dynamic_cast<NativeUnifyFillCxrParams*>(params);
  ASSERT_NOT_NULL_OTHERWISE_RETURN_WITH(cxr_params, StraRetCode::SUCC);
  /**********
  * [约定] ctr/cvr 切分点
  * 1. 纯作品：AD_ITEM_IMPRESSION
  * 2. 作品引流默认为：AD_STANDARD_LIVE_PLAYED_STARTED；也可能为：AD_ITEM_IMPRESSION
  * 3. 直投直播默认为：AD_STANDARD_LIVE_PLAYED_STARTED；也可能为：AD_LIVE_IMPRESSION/AD_LIVE_PLAYED_STARTED
  **********/
  for (auto p_ad : ad_list->Ads()) {
    SEARCH_AD_LIST_SKIP_LIVE_AD(session_data, p_ad)
    SEARCH_AD_LIST_SKIP_PHOTO_AD(session_data, p_ad)
    // NOTE: 执行顺序不能变，必须先填 ctr，再填 cvr，再监控
    if (p_ad->Is(AdFlag::is_inner_loop_ad)) {
      SetUnifyCtr(cxr_params, session_data, p_ad);
    } else {
      SetOuterUnifyCtr(cxr_params, session_data, p_ad);
    }
    SetUnifySctr(cxr_params, session_data, p_ad);
    SetUnifyCvr(cxr_params, session_data, p_ad);
    SetUnifyLtv(cxr_params, session_data, p_ad);
    SetUnifyDeepCvr(cxr_params, session_data, p_ad);
    SetMcbConstraintR(cxr_params, p_ad);
    SetAdxUnifyCtr(cxr_params, session_data, p_ad);
    MonitorUnifyCxr(cxr_params, session_data, p_ad);
  }
  bool enable_item_card_soft_queue_jichen = SPDM_enable_item_card_soft_queue_jichen(session_data->get_spdm_ctx());  // NOLINT
  bool enable_item_card_jichen = SPDM_enable_item_card_jichen(session_data->get_spdm_ctx());
  if (session_data->get_pos_manager_base().IsGuessYouLike() &&
     enable_item_card_jichen && enable_item_card_soft_queue_jichen) {
      GetCvrLtvPred(session_data, ad_list);
  }
  return StraRetCode::SUCC;
}

void SearchNativeUnifyFillCxr::GetCvrLtvPred(ContextData* session_data, AdList* ad_list) {
  auto* gyl_soft_queue_predict_result_map_ptr = session_data->mutable_gyl_soft_queue_predict_result_map();
  if (!gyl_soft_queue_predict_result_map_ptr) {return;}
  auto& gyl_soft_queue_predict_result_map = *gyl_soft_queue_predict_result_map_ptr;
  for (auto p_ad : ad_list->Ads()) {
    if (p_ad->get_creative_material_type() != kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD) {
      const std::string& spu_cvr_key =
              absl::Substitute("cvr_spu_id_$0", absl::StrCat(p_ad->get_spu_id_v2()));
      auto spu_cvr_iter = gyl_soft_queue_predict_result_map.find(spu_cvr_key);
      if (spu_cvr_iter != gyl_soft_queue_predict_result_map.end()) {
        gyl_soft_queue_predict_result_map[spu_cvr_key] =
         std::max(spu_cvr_iter->second, p_ad->get_unify_cvr_info().value);
      } else {
        gyl_soft_queue_predict_result_map[spu_cvr_key] = p_ad->get_unify_cvr_info().value;
      }
      const std::string& author_spu_cvr_key =  absl::Substitute("author_$0_cvr_spu_id_$1",
              absl::StrCat(p_ad->get_author_id()), absl::StrCat(p_ad->get_spu_id_v2()));
      auto author_spu_cvr_iter = gyl_soft_queue_predict_result_map.find(author_spu_cvr_key);
      if (author_spu_cvr_iter != gyl_soft_queue_predict_result_map.end()) {
        gyl_soft_queue_predict_result_map[author_spu_cvr_key] =
            std::max(author_spu_cvr_iter->second, p_ad->get_unify_cvr_info().value);
      } else {
        gyl_soft_queue_predict_result_map[author_spu_cvr_key] = p_ad->get_unify_cvr_info().value;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS) {
        const std::string& spu_ltv_key =
            absl::Substitute("ltv_spu_id_$0", absl::StrCat(p_ad->get_spu_id_v2()));
        auto spu_ltv_iter = gyl_soft_queue_predict_result_map.find(spu_ltv_key);
        if (spu_ltv_iter != gyl_soft_queue_predict_result_map.end()) {
          gyl_soft_queue_predict_result_map[spu_ltv_key] =
            std::max(spu_ltv_iter->second, p_ad->get_unify_ltv_info().value);
        } else {
          gyl_soft_queue_predict_result_map[spu_ltv_key] = p_ad->get_unify_ltv_info().value;
        }
        const std::string& author_spu_ltv_key =  absl::Substitute("author_$0_ltv_spu_id_$1",
              absl::StrCat(p_ad->get_author_id()), absl::StrCat(p_ad->get_spu_id_v2()));
        auto author_spu_ltv_iter = gyl_soft_queue_predict_result_map.find(author_spu_ltv_key);
        if (author_spu_ltv_iter != gyl_soft_queue_predict_result_map.end()) {
          gyl_soft_queue_predict_result_map[author_spu_ltv_key] =
              std::max(author_spu_ltv_iter->second, p_ad->get_unify_ltv_info().value);
        } else {
          gyl_soft_queue_predict_result_map[author_spu_ltv_key] = p_ad->get_unify_ltv_info().value;
        }
      }
    }
  }
}

void SearchNativeUnifyFillCxr::SetAdxUnifyCtr(const NativeUnifyFillCxrParams* cxr_params,
                                        const ContextData* session_data,
                                        AdCommon* ad) {
  if (cxr_params == nullptr || !cxr_params->enable_native_adx_biding_exp) {
    return;
  }
  if (ad->get_ad_source_type() != kuaishou::ad::ADX) {
    return;
  }
  if (!session_data->get_is_thanos_request()) {
    return;
  }
  if (ad->get_bid_type() != AdEnum::CPC) {
    return;
  }
  double ctr;
  int32_t cmd_id_1 = 0;
  int32_t cmd_id_2 = 0;
  AdActionType ctr_start = AdActionType::AD_ITEM_IMPRESSION;
  AdActionType ctr_end = AdActionType::AD_ITEM_CLICK;
  ctr = ad->get_predict_score(PredictType::PredictType_cvr);
  cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_cvr);
  // 4. 设置 unify ctr
  ad->SetUnifyCtr(ctr, ctr_start, ctr_end, cmd_id_1, cmd_id_2);
}

void SearchNativeUnifyFillCxr::SetUnifyCtr(const NativeUnifyFillCxrParams* cxr_params,
    const ContextData* session_data, AdCommon* ad) {
  /**********
  * [注意] ctr 起点
  * 软广排序阶段的 ecpm 默认为 client ecpm，sctr 仅用于计算计费金额
  * 因此 ctr 起点默认取客户端首曝光，而非 AD_DELIVERY；使用 server ecpm 的场景需要特殊处理
  **********/
  // 0. 初始化相关变量
  bool is_feed = session_data->get_is_feed();  // 双列
  bool start_from_server_show = session_data->get_is_search_request();  // 目前只有搜索用 server ecpm

  double ctr;
  int32_t cmd_id_1 = 0;
  int32_t cmd_id_2 = 0;
  AdActionType ctr_start;
  AdActionType ctr_end;
  // NOTE: 由于 item_type 字段默认值是 ITEM_PHOTO，最稳妥的判断顺序是 live->p2l->photo
  if (ad->Is(AdFlag::is_live)) {  // 1. 直投直播
    if (ad->get_ocpx_action_type() == AdActionType::AD_LIVE_SHOP_LINK_JUMP ||
        ad->get_ocpx_action_type() == AdActionType::AD_FANS_TOP_FOLLOW ||
        ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW ||
        ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW_FAST ||
        ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW_QUALITY ||
        ad->get_ocpx_action_type() == AdActionType::AD_LIVE_PLAYED_1M) {
      // 1.1 商品点击、涨粉、播放一分钟目标的直投直播不乘进人率，ctr 取 1
      ctr = 1.0;
      if (is_feed) {
        ctr_start = AdActionType::AD_LIVE_IMPRESSION;
        ctr_end = AdActionType::AD_LIVE_IMPRESSION;
      } else {
        ctr_start = AdActionType::AD_LIVE_PLAYED_STARTED;
        ctr_end =  AdActionType::AD_LIVE_PLAYED_STARTED;
      }
    } else {
      // 1.2 其他直投直播 ctr 取直播间进人率，终点是标准直播间开始播放
      ctr = ad->get_live_audience();
      cmd_id_1 = ad->get_live_audience_cmd_id();
      if (is_feed) {
        ctr_start = AdActionType::AD_LIVE_IMPRESSION;
        ctr_end = AdActionType::AD_STANDARD_LIVE_PLAYED_STARTED;
      } else {
        ctr_start = AdActionType::AD_LIVE_PLAYED_STARTED;
        ctr_end = AdActionType::AD_STANDARD_LIVE_PLAYED_STARTED;
      }
    }
  } else {  // 2. 纯作品或作品引流
    // 2.1 是否以 item impression 为终点
    bool end_with_item_impression = false;
    if (ad->Is(AdFlag::is_p2l)) {
      if (ad->get_ocpx_action_type() == AdActionType::AD_LIVE_SHOP_LINK_JUMP ||
          ad->get_ocpx_action_type() == AdActionType::AD_FANS_TOP_FOLLOW ||
          ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW ||
          ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW_FAST ||
          ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_FOLLOW_QUALITY) {
        // 2.1.1 商品点击、涨粉目标的作品引流以 item impression 为终点
        end_with_item_impression = true;
      }
    } else {
      // 2.1.2 所有纯作品以 item impression 为终点
      end_with_item_impression = true;
    }
    // 2.2 若以 item impression 为终点
    if (end_with_item_impression) {
      if (is_feed) {
        ctr = ad->get_ctr();
        cmd_id_1 = ad->get_ctr_cmd_id();
        ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
        ctr_end = AdActionType::AD_ITEM_IMPRESSION;
      } else {
        // 单列 ctr 为 1
        ctr = 1.0;
        ctr_start = AdActionType::AD_ITEM_IMPRESSION;
        ctr_end = AdActionType::AD_ITEM_IMPRESSION;
      }
    } else {
      // 2.3 大部分作品引流终点不是 item impression，而是标准直播间开始播放
      if (is_feed) {  // 双列两段
        ctr = ad->get_ctr() * ad->get_live_audience();
        cmd_id_1 = ad->get_ctr_cmd_id();
        cmd_id_2 = ad->get_live_audience_cmd_id();
        ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
        ctr_end = AdActionType::AD_STANDARD_LIVE_PLAYED_STARTED;
      } else {
        ctr = ad->get_live_audience();
        cmd_id_1 = ad->get_live_audience_cmd_id();
        ctr_start = AdActionType::AD_ITEM_IMPRESSION;
        ctr_end = AdActionType::AD_STANDARD_LIVE_PLAYED_STARTED;
      }
    }
  }
  // 3. 特殊场景：以 server show 为起点
  // NOTE: 由于此场景较少，且 cmd_id 不支持三段式，此处不更新 cmd_id
  if (start_from_server_show) {
    // 搜索激励广告盒子 sctr 默认为 1.0
    if (session_data->get_rank_request()->ad_request().search_info().request_source() == 2) {
      ctr = ctr;
    } else {
      ctr *= ad->get_server_show_ctr();
    }
    ctr_start = AdActionType::AD_DELIVERY;
  }
  // 4. 设置 unify ctr
  ad->SetUnifyCtr(ctr, ctr_start, ctr_end, cmd_id_1, cmd_id_2);
}

void SearchNativeUnifyFillCxr::SetOuterUnifyCtr(const NativeUnifyFillCxrParams* cxr_params,
    const ContextData* session_data, AdCommon* ad) {
  double ctr;
  int32_t cmd_id_1 = 0;
  int32_t cmd_id_2 = 0;
  AdActionType ctr_start;
  AdActionType ctr_end;
  bool is_feed = session_data->get_is_feed();  // 双列
  bool enable_mini_app_roas_invoked_product =
        cxr_params->mini_app_roas_invoked_product_map != nullptr &&
        cxr_params->mini_app_roas_invoked_product_map->find(ad->get_product_name()) !=
        cxr_params->mini_app_roas_invoked_product_map->end();
  bool enable_mini_app_roas_invoked_account =
        cxr_params->mini_app_roas_invoked_account_map != nullptr &&
        cxr_params->mini_app_roas_invoked_account_map->find(ad->get_account_id()) !=
        cxr_params->mini_app_roas_invoked_account_map->end();
  bool enable_ad_rank_clk2purchase_roas_predict = cxr_params->enable_ad_rank_clk2purchase_roas_predict;
  bool enable_playlet_inovked_predict = cxr_params->enable_playlet_inovked_predict;
  bool enable_ad_rank_clk2purchase_predict = cxr_params->enable_ad_rank_clk2purchase_predict;
  bool disable_industry_clk2purchase = cxr_params->disable_industry_clk2purchase;
  bool enable_ad_rank_industry_server_show_cvr_predict =
        cxr_params->enable_ad_rank_industry_server_show_cvr_predict;
  bool enable_industry_clk2purchase_mix = cxr_params->enable_industry_clk2purchase_mix;
  if (ad->Is(AdFlag::is_live)) {
    // 1. 直投直播
    ctr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_ctr);
    cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_ctr);
    ctr_start = AdActionType::AD_LIVE_PLAYED_STARTED;
    ctr_end = AdActionType::AD_STANDARD_LIVE_PLAYED_STARTED;
  } else if (ad->Is(AdFlag::is_p2l)) {
    ctr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_ctr);
    cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_ctr);
    ctr_start = AdActionType::AD_ITEM_IMPRESSION;
    ctr_end = AdActionType::AD_STANDARD_LIVE_PLAYED_STARTED;
  } else if (ad->get_ocpx_action_type() == AdActionType::EVENT_JINJIAN ||
             ad->get_ocpx_action_type() == AdActionType::AD_CREDIT_GRANT ||
             ad->get_ocpx_action_type() == AdActionType::AD_ITEM_CLICK ||
             ad->get_ocpx_action_type() == AdActionType::EVENT_ADD_WECHAT) {
    if (is_feed) {
      ctr = ad->get_ctr();
      cmd_id_1 = ad->get_ctr_cmd_id();
      ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
      ctr_end = AdActionType::AD_ITEM_IMPRESSION;
    } else {
      ctr = ad->get_server_show_cvr();
      cmd_id_1 = ad->get_server_show_cvr_cmd_id();
      ctr_start = AdActionType::AD_ITEM_IMPRESSION;
      ctr_end = AdActionType::AD_ITEM_CLICK;
    }
  } else if (ad->get_ocpx_action_type() == AdActionType::AD_PURCHASE) {
    bool enable_app_envoke_product = cxr_params->app_invoked_purchase_product_map != nullptr &&
        cxr_params->app_invoked_purchase_product_map->find(ad->get_product_name()) !=
        cxr_params->app_invoked_purchase_product_map->end();
    bool enable_app_envoke_account = cxr_params->app_invoked_purchase_account_map != nullptr &&
        cxr_params->app_invoked_purchase_account_map->find(ad->get_account_id()) !=
        cxr_params->app_invoked_purchase_account_map->end();
    bool enable_app_conversion_purchase_product =
          cxr_params->app_conversion_purchase_product_map != nullptr &&
          cxr_params->app_conversion_purchase_product_map->find(ad->get_product_name()) !=
            cxr_params->app_conversion_purchase_product_map->end();
    bool enable_app_conversion_purchase_account =
          cxr_params->app_conversion_purchase_account_map != nullptr &&
          cxr_params->app_conversion_purchase_account_map->find(ad->get_account_id()) !=
            cxr_params->app_conversion_purchase_account_map->end();
    if (ad->get_second_industry_id_v5() == 2012 && enable_ad_rank_clk2purchase_predict &&
        (!disable_industry_clk2purchase || session_data->get_page_id() != 10002)) {
      if (enable_industry_clk2purchase_mix) {
        if (is_feed) {
          ctr = ad->get_ctr();
          cmd_id_1 = ad->get_ctr_cmd_id();
          ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
          ctr_end = AdActionType::AD_ITEM_IMPRESSION;
        } else {
          ctr = 1.0;
          cmd_id_1 = 0;
          ctr_start = AdActionType::AD_ITEM_IMPRESSION;
          ctr_end = AdActionType::AD_ITEM_IMPRESSION;
        }
      } else {
        if (is_feed) {
          if (enable_ad_rank_industry_server_show_cvr_predict) {
            ctr = ad->get_ctr() * ad->get_predict_score(PredictType::PredictType_industry_server_show_cvr);
          } else {
            ctr = ad->get_ctr() * ad->get_server_show_cvr();
          }
          cmd_id_1 = ad->get_ctr_cmd_id();
          cmd_id_2 = ad->get_server_show_cvr_cmd_id();
          ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
          ctr_end = AdActionType::AD_ITEM_CLICK;
        } else {
          if (enable_ad_rank_industry_server_show_cvr_predict) {
            ctr = ad->get_predict_score(PredictType::PredictType_industry_server_show_cvr);
          } else {
            ctr = ad->get_server_show_cvr();
          }
          cmd_id_1 = ad->get_server_show_cvr_cmd_id();
          ctr_start = AdActionType::AD_ITEM_IMPRESSION;
          ctr_end = AdActionType::AD_ITEM_CLICK;
        }
      }
    } else if ((ad->get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict) ||
                enable_mini_app_roas_invoked_product || enable_mini_app_roas_invoked_account) {
      if (is_feed) {
        ctr = ad->get_predict_score(PredictType::PredictType_click_app_invoked) * ad->get_ctr();
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click_app_invoked);
        cmd_id_2 = ad->get_ctr_cmd_id();
        ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
        ctr_end = AdActionType::EVENT_APP_INVOKED;
      } else {
        ctr = ad->get_predict_score(PredictType::PredictType_click_app_invoked);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click_app_invoked);
        ctr_start = AdActionType::AD_ITEM_IMPRESSION;
        ctr_end = AdActionType::EVENT_APP_INVOKED;
      }
    } else if (enable_app_conversion_purchase_product || enable_app_conversion_purchase_account ||
        ad->get_campaign_type() == AdEnum::APP) {
      if (is_feed) {
        ctr = ad->get_app_conversion_rate() * ad->get_ctr();
        cmd_id_1 = ad->get_app_conversion_rate_cmd_id();
        cmd_id_2 = ad->get_ctr_cmd_id();
        ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
        ctr_end = AdActionType::AD_CONVERSION;
      } else {
        ctr = ad->get_app_conversion_rate();
        cmd_id_1 = ad->get_app_conversion_rate_cmd_id();
        ctr_start = AdActionType::AD_ITEM_IMPRESSION;
        ctr_end = AdActionType::AD_CONVERSION;
      }
    } else if (ad->get_campaign_type() == AdEnum::APP_ADVANCE &&
        (enable_app_envoke_product || enable_app_envoke_account)) {
      if (is_feed) {
        ctr = ad->get_predict_score(PredictType::PredictType_click_app_invoked) * ad->get_ctr();
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click_app_invoked);
        cmd_id_2 = ad->get_ctr_cmd_id();
        ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
        ctr_end = AdActionType::EVENT_APP_INVOKED;
      } else {
        ctr = ad->get_predict_score(PredictType::PredictType_click_app_invoked);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click_app_invoked);
        ctr_start = AdActionType::AD_ITEM_IMPRESSION;
        ctr_end = AdActionType::EVENT_APP_INVOKED;
      }
    } else if (!is_feed && cxr_params->enable_lpsep_cut_imp &&
               (ad->get_campaign_type() == AdEnum::SITE_PAGE ||
                ad->get_campaign_type() == AdEnum::LANDING_PAGE ||
                ad->get_campaign_type() == AdEnum::TAOBAO ||
                ad->get_campaign_type() == AdEnum::MINI_APP_CAMPAIGN_TYPE)) {
      ctr = 1.0;
      cmd_id_1 = 0;
      ctr_start = AdActionType::AD_ITEM_IMPRESSION;
      ctr_end = AdActionType::AD_ITEM_IMPRESSION;
    } else if (is_feed) {
      ctr = ad->get_ctr();
      cmd_id_1 = ad->get_ctr_cmd_id();
      ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
      ctr_end = AdActionType::AD_ITEM_IMPRESSION;
    } else {
      ctr = ad->get_server_show_cvr();
      cmd_id_1 = ad->get_server_show_cvr_cmd_id();
      ctr_start = AdActionType::AD_ITEM_IMPRESSION;
      ctr_end = AdActionType::AD_ITEM_CLICK;
    }
    // 付费 roi 双出价在小程序、小说上，继承上面付费单出价的切分点
    bool use_purchase_result =
        (ad->get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict) ||
        enable_mini_app_roas_invoked_product ||
        enable_mini_app_roas_invoked_account ||
        enable_app_conversion_purchase_product ||
        enable_app_conversion_purchase_account;
    // 付费 roi 双出价特殊处理
    if (ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY
      && !use_purchase_result) {
      if (is_feed) {
        ctr = ad->get_ctr();
        cmd_id_1 = ad->get_ctr_cmd_id();
        ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
        ctr_end = AdActionType::AD_ITEM_IMPRESSION;
      } else {
        ctr = 1.0;
        cmd_id_1 = 0;
        ctr_start = AdActionType::AD_ITEM_IMPRESSION;
        ctr_end = AdActionType::AD_ITEM_IMPRESSION;
      }
    }
  } else if (ad->get_ocpx_action_type() == AdActionType::AD_ROAS) {
    if (ad->get_second_industry_id_v5() == 2012 && enable_ad_rank_clk2purchase_roas_predict &&
        (!disable_industry_clk2purchase || session_data->get_page_id() != 10002)) {
      if (is_feed) {
        if (enable_ad_rank_industry_server_show_cvr_predict) {
          ctr = ad->get_ctr() * ad->get_predict_score(PredictType::PredictType_industry_server_show_cvr);
        } else {
          ctr = ad->get_ctr() * ad->get_server_show_cvr();
        }
        cmd_id_1 = ad->get_ctr_cmd_id();
        cmd_id_2 = ad->get_server_show_cvr_cmd_id();
        ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
        ctr_end = AdActionType::AD_ITEM_CLICK;
      } else {
        if (enable_ad_rank_industry_server_show_cvr_predict) {
          ctr = ad->get_predict_score(PredictType::PredictType_industry_server_show_cvr);
        } else {
          ctr = ad->get_server_show_cvr();
        }
        cmd_id_1 = ad->get_server_show_cvr_cmd_id();
        ctr_start = AdActionType::AD_ITEM_IMPRESSION;
        ctr_end = AdActionType::AD_ITEM_CLICK;
      }
    } else {
      if (is_feed) {
        ctr = ad->get_ctr();
        cmd_id_1 = ad->get_ctr_cmd_id();
        ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
        ctr_end = AdActionType::AD_ITEM_IMPRESSION;
      } else {
        ctr = 1.0;
        cmd_id_1 = 0;
        ctr_start = AdActionType::AD_ITEM_IMPRESSION;
        ctr_end = AdActionType::AD_ITEM_IMPRESSION;
      }
    }
  } else {
    if (is_feed) {
      ctr = ad->get_ctr();
      cmd_id_1 = ad->get_ctr_cmd_id();
      ctr_start = AdActionType::AD_PHOTO_IMPRESSION;
      ctr_end = AdActionType::AD_ITEM_IMPRESSION;
    } else {
      ctr = 1.0;
      cmd_id_1 = 0;
      ctr_start = AdActionType::AD_ITEM_IMPRESSION;
      ctr_end = AdActionType::AD_ITEM_IMPRESSION;
    }
  }
  // 4. 设置 unify ctr
  ad->SetUnifyCtr(ctr, ctr_start, ctr_end, cmd_id_1, cmd_id_2);
}

void SearchNativeUnifyFillCxr::SetUnifyCvr(const NativeUnifyFillCxrParams* cxr_params,
    const ContextData* session_data, AdCommon* ad) {
  /**********
  * [约定]
  * 1. cvr 起点一律与 ctr 终点对齐
  * 2. 内循环 cvr 终点一律取 ocpx_action_type，即便可能与实际转化点不同
  * 3. 外循环 cvr 终点大部分取 ocpx_action_type，部分优化类型会用到 deep cvr
  **********/
  bool is_feed = session_data->get_is_feed();  // 双列

  double cvr;
  int32_t cmd_id_1 = 0;
  int32_t cmd_id_2 = 0;
  AdActionType cvr_start = ad->get_unify_ctr_info().e_type;
  AdActionType cvr_end = ad->get_ocpx_action_type();

  bool enable_playlet_inovked_predict = cxr_params->enable_playlet_inovked_predict;
  bool enable_mini_app_roas_invoked_product =
          cxr_params->mini_app_roas_invoked_product_map != nullptr &&
          cxr_params->mini_app_roas_invoked_product_map->find(ad->get_product_name()) !=
          cxr_params->mini_app_roas_invoked_product_map->end();
  bool enable_mini_app_roas_invoked_account =
      cxr_params->mini_app_roas_invoked_account_map != nullptr &&
      cxr_params->mini_app_roas_invoked_account_map->find(ad->get_account_id()) !=
      cxr_params->mini_app_roas_invoked_account_map->end();
  const auto& order_paied_jingxuan_cali_map = RankKconfUtil::orderPaiedJingXuanCaliMap();
  const auto& roas_jingxuan_cali_map = RankKconfUtil::roasJingXuanCaliMap();
  bool enable_ad_rank_clk2purchase_roas_predict = cxr_params->enable_ad_rank_clk2purchase_roas_predict;
  bool enable_ad_rank_clk2purchase_predict = cxr_params->enable_ad_rank_clk2purchase_predict;
  bool disable_industry_clk2purchase = cxr_params->disable_industry_clk2purchase;
  bool enable_industry_clk2purchase_mix = cxr_params->enable_industry_clk2purchase_mix;
  double industry_clk2purchase_ratio_soft = cxr_params->industry_clk2purchase_ratio_soft;
  double industry_clk2purchase_other_ratio_soft = cxr_params->industry_clk2purchase_other_ratio_soft;
  bool enable_outer_game_invoke_link = SPDM_enable_outer_game_invoke_link(session_data->get_spdm_ctx());
  const auto& game_invoke_link_config = RankKconfUtil::outerGameInvokeLinkConfig();
  double cid_roas_reset_cvr = SPDM_cid_roas_reset_cvr(session_data->get_spdm_ctx());
  switch (ad->get_ocpx_action_type()) {
    case AdActionType::AD_LIVE_AUDIENCE:
    case AdActionType::AD_LIVE_AUDIENCE_QUALITY:
    case AdActionType::AD_LIVE_AUDIENCE_FAST:
    case AdActionType::AD_AUDIENCE_FAST:
    case AdActionType::AD_FANS_TOP_LIVE_SHOW:  // CPM
      // 直播进人/曝光目标 cvr 取 1
      cvr = 1.0;
      break;
    case AdActionType::AD_FANS_TOP_PLAY:
    case AdActionType::AD_FANSTOP_PHOTO_SHOW:  // CPM
      if (is_feed) {
        // 作品播放/曝光目标双列 cvr 取 1
        cvr = 1.0;
      } else {
        // 单列 cvr 为长播，存在了 ctr 字段里
        cvr = ad->get_ctr();
        cmd_id_1 = ad->get_ctr_cmd_id();
        if (ad->Is(AdFlag::is_self_service_ad)) {
          cvr = ad->get_predict_score(PredictType::PredictType_ltr);
          cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_ltr);
        }
      }
      break;
    case AdActionType::AD_LIVE_EFFECTIVE_PLAY:
      // 只有磁力金牛 PC 短视频有，cvr 存在了直播进人率字段里
      cvr = ad->get_live_audience();
      cmd_id_1 = ad->get_live_audience_cmd_id();
      break;
    case AdActionType::AD_LIVE_SHOP_LINK_JUMP:
      cvr = ad->get_shop_jump();
      cmd_id_1 = ad->get_shop_jump_cmd_id();
      break;
    case AdActionType::AD_FANS_TOP_FOLLOW:
    case AdActionType::AD_MERCHANT_FOLLOW:
    case AdActionType::AD_MERCHANT_FOLLOW_FAST:
    case AdActionType::AD_MERCHANT_FOLLOW_QUALITY:
    case AdActionType::AD_MERCHANT_FOLLOW_ROI:
      cvr = ad->get_wtr();
      cmd_id_1 = ad->get_wtr_cmd_id();
      break;
    case AdActionType::AD_PHOTO_LIKE:
      cvr = ad->get_predict_score(PredictType::PredictType_ltr);
      cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_ltr);
      break;
    case AdActionType::AD_PHOTO_TO_PROFILE:
      cvr = ad->get_predict_score(PredictType::PredictType_profile_tr);
      cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_profile_tr);
      break;
    case AdActionType::AD_LIVE_RESERVATION_SUCCESS:
      cvr = ad->get_live_res_succ();
      cmd_id_1 = ad->get_live_res_succ_cmd_id();
      break;
    case AdActionType::AD_POI_CLICK:
    case AdActionType::AD_GOODS_CLICK:
      cvr = ad->get_lsp_photo_item_click();
      cmd_id_1 = ad->get_lsp_photo_item_click_cmd_id();
      break;
    case AdActionType::AD_ITEM_CLICK:
    case AdActionType::AD_ITEM_CLICK_DOWNLOAD:
    case AdActionType::AD_ITEM_CLICK_CLUE:
    case AdActionType::AD_ITEM_CLICK_TROLLEY:
    case AdActionType::AD_ITEM_CLICK_PROGRAM:
    case AdActionType::AD_ITEM_CLICK_POI:
    case AdActionType::AD_ITEM_CLICK_GAME:
    case AdActionType::AD_ITEM_CLICK_LIVE_RESERVATION:
      if (ad->Is(AdFlag::is_merchant_item_photo)) {
        // 磁力金牛 PC 短视频 item click 指直播预约目标
        cvr = ad->get_live_booking_rate();
        cmd_id_1 = ad->get_live_booking_rate_cmd_id();
      } else if (ad->Is(AdFlag::is_outer_loop_ad)) {
        if (is_feed) {
          cvr = ad->get_cvr();
          cmd_id_1 = ad->get_cvr_cmd_id();
        } else {
          cvr = 1.0;
        }
      } else {
        // 其他指 plc 点击
        cvr = ad->get_plc();
        cmd_id_1 = ad->get_plc_cmd_id();
      }
      break;
    case AdActionType::EVENT_PRIVATE_MESSAGE_SENT:
      if (ad->Is(AdFlag::is_self_service_ad)) {
        cvr = ad->get_landingpage_submit_rate();
        cmd_id_1 = ad->get_landingpage_submit_rate_cmd_id();
      } else if (ad->Is(AdFlag::is_industry_live)) {
        cvr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);
      }
      break;
    case AdActionType::AD_BUTTON_CLICK_CONSULT:
      cvr = ad->get_consult_ctr();
      cmd_id_1 = ad->get_consult_ctr_cmd_id();
      break;
    case AdActionType::AD_BUTTON_CLICK_DOWNLOAD:
      cvr = ad->get_download_ctr();
      cmd_id_1 = ad->get_download_ctr_cmd_id();
      break;
    case AdActionType::LEADS_SUBMIT:
      cvr = ad->get_leads_submit();
      cmd_id_1 = ad->get_leads_submit_cmd_id();
      if (ad->Is(AdFlag::is_self_service_ad)) {
        if (ad->Is(AdFlag::is_live_ad)) {
          cvr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
          cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);
        } else {
          cvr = ad->get_landingpage_submit_rate();
          cmd_id_1 = ad->get_landingpage_submit_rate_cmd_id();
        }
      } else if (ad->Is(AdFlag::is_industry_live)) {
        cvr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);
      }
      break;
    case AdActionType::AD_LIVE_PLAYED_1M:
      cvr = ad->get_live_play_1m();
      cmd_id_1 = ad->get_live_play_1m_cmd_id();
      break;
    case AdActionType::AD_LIVE_COMMENT:
      cvr = ad->get_cmtr();
      cmd_id_1 = ad->get_cmtr_cmd_id();
      break;
    case AdActionType::EVENT_ORDER_PAIED:
    case AdActionType::CID_EVENT_ORDER_PAID:
      if (ad->get_campaign_type() == AdEnum::FANS_LIVE_STREAM_PROMOTE) {
        cvr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);
      } else {
        double c1_order_paied_rate = 1.0;
        // 主站精选页
        if (session_data->get_sub_page_id() == 10011001) {
            auto iter = order_paied_jingxuan_cali_map->find(ad->get_author_id());
            if (iter != order_paied_jingxuan_cali_map->end()) {
                c1_order_paied_rate = iter->second;
            }
        }
        cvr = ad->get_order_paid() * c1_order_paied_rate;
        cmd_id_1 = ad->get_order_paid_cmd_id();
      }
      break;
    case AdActionType::AD_FANS_TOP_ROI:
      cvr = 1.0;
      break;
    case AdActionType::AD_MERCHANT_ROAS:
    case AdActionType::AD_MERCHANT_T7_ROI:
    case AdActionType::AD_STOREWIDE_ROAS:
    case kuaishou::ad::CID_ROAS:
      if (ad->Is(AdFlag::is_photo)) {
        // 磁力金牛短视频 gmv 以订单支付为起点
        // 磁力金牛短视频 gmv 从曝光起点切分点实验, cvr 取 1.0 by zed 20220928
        if (cxr_params->enable_roas_native_use_imp_ltv_switch_new &&
            !ad->Is(AdFlag::is_fanstop)) {
          cvr = 1.0;
        } else {
          double c1_order_paied_rate = 1.0;
          // 主站精选页
          if (session_data->get_sub_page_id() == 10011001) {
              auto iter = roas_jingxuan_cali_map->find(ad->get_author_id());
              if (iter != roas_jingxuan_cali_map->end()) {
                  c1_order_paied_rate = iter->second;
              }
          }
          cvr = ad->get_order_paid() * c1_order_paied_rate;
          cmd_id_1 = ad->get_order_paid_cmd_id();
        }
        if (session_data->get_is_search_request() && cxr_params->is_search_roi_model &&
            cxr_params->enable_search_jinniu_moble_roi_model) {
          cvr = cxr_params->search_photo_roi_value;
        }
      } else {
        // 其他以直播间进人为起点，cvr 取 1
        cvr = 1.0;
      }
      break;
    case AdActionType::AD_ROAS: {
      bool enable_mini_game_invoke_link = false;
      if (enable_outer_game_invoke_link) {
        bool is_mini_game = (((ad->get_landing_page_component() & 1) == 1 ||
                            (ad->get_landing_page_component() & 2) == 2) &&
                            ad->get_industry_parent_id_v3() == 1018);
        if (game_invoke_link_config != nullptr) {
          auto iter_product = game_invoke_link_config->find(ad->get_product_name());
          bool is_game_invoke_link_product = (iter_product != game_invoke_link_config->end());
          enable_mini_game_invoke_link = (is_game_invoke_link_product && is_mini_game);
        }
      }
      if (ad->get_campaign_type() == AdEnum::FANS_LIVE_STREAM_PROMOTE) {
        cvr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);
        cvr_end = AdActionType::AD_CONVERSION;
      } else if (ad->get_second_industry_id_v5() == 2012 && enable_ad_rank_clk2purchase_roas_predict &&
        (!disable_industry_clk2purchase || session_data->get_page_id() != 10002)) {
        cvr = ad->get_predict_score(PredictType::PredictType_industry_clk2_pay);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_industry_clk2_pay);
        cvr_end = AdActionType::AD_PURCHASE;
      } else if ((ad->get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict) ||
                enable_mini_app_roas_invoked_product || enable_mini_app_roas_invoked_account) {
        {
          cvr = ad->get_predict_score(PredictType::PredictType_click_app_invoked) *
                ad->get_predict_score(PredictType::PredictType_purchase);
          cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click_app_invoked);
          cmd_id_2 = ad->get_predict_cmd_id(PredictType::PredictType_purchase);
          cvr_end = AdActionType::AD_PURCHASE;
        }
      } else {
        if (enable_mini_game_invoke_link) {
          cvr = ad->get_predict_score(PredictType::PredictType_click_app_invoked);
          cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click_app_invoked);
          cvr_end = AdActionType::EVENT_APP_INVOKED;
        } else {
          cvr = ad->get_app_conversion_rate();
          cmd_id_1 = ad->get_app_conversion_rate_cmd_id();
          cvr_end = AdActionType::AD_CONVERSION;
        }
      }
      break;
    }
    case AdActionType::AD_CONVERSION:
    case AdActionType::EVENT_7_DAY_PAY_TIMES:
      if (ad->get_campaign_type() == AdEnum::FANS_LIVE_STREAM_PROMOTE) {
        cvr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);
      } else {
        cvr = ad->get_app_conversion_rate();
        cmd_id_1 = ad->get_app_conversion_rate_cmd_id();
      }
      cvr_end = AdActionType::AD_CONVERSION;
      break;
    case AdActionType::EVENT_RETENTION_DAYS:
      cvr = ad->get_app_conversion_rate();
      cmd_id_1 = ad->get_app_conversion_rate_cmd_id();
      cvr_end = AdActionType::AD_CONVERSION;
      break;
    case AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED:
      if (ad->get_campaign_type() == AdEnum::FANS_LIVE_STREAM_PROMOTE) {
        cvr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);
      } else {
        cvr = ad->get_landingpage_submit_rate();
        cmd_id_1 = ad->get_landingpage_submit_rate_cmd_id();
      }
      cvr_end = AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED;
      break;
    case AdActionType::AD_PURCHASE_CONVERSION:
      if (ad->get_campaign_type() == AdEnum::FANS_LIVE_STREAM_PROMOTE) {
        cvr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);
        cvr_end = AdActionType::AD_CONVERSION;
      }
      break;
    case AdActionType::AD_SEVEN_DAY_ROAS:
    case AdActionType::EVENT_KEY_INAPP_ACTION:
    case AdActionType::AD_IAA_ROAS:
    case AdActionType::AD_SERIAL_IAA_ROAS:
    case AdActionType::AD_ROAS_IAAP:
    case AdActionType::AD_IAA_7DAY_ROAS:
    case AdActionType::EVENT_24H_STAY:
    case AdActionType::EVENT_NEXTDAY_STAY:
      cvr = ad->get_app_conversion_rate();
      cmd_id_1 = ad->get_app_conversion_rate_cmd_id();
      cvr_end = AdActionType::AD_CONVERSION;
      break;
    case AdActionType::EVENT_REGISTER:
    case AdActionType::EVENT_ORDER_SUBMIT:
      cvr = ad->get_predict_score(PredictType::PredictType_deep_rate);
      cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_deep_rate);
      break;
    case AdActionType::AD_CID_ROAS:
      cvr = cid_roas_reset_cvr;
      cvr_start = AdActionType::EVENT_ORDER_SUBMIT;
      cvr_end = AdActionType::EVENT_ORDER_SUBMIT;
      cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_deep_rate);
      break;
    case AdActionType::EVENT_APP_INVOKED:
      cvr = ad->get_predict_score(PredictType::PredictType_click_app_invoked);
      cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click_app_invoked);
      break;
    case AdActionType::EVENT_PHONE_GET_THROUGH:
    case AdActionType::EVENT_INTENTION_CONFIRMED:
    case AdActionType::EVENT_WECHAT_CONNECTED:
    case AdActionType::EVENT_ORDER_SUCCESSED:
    case AdActionType::EVENT_MEASUREMENT_HOUSE:
    case AdActionType::EVENT_PHONE_CARD_ACTIVATE:
      cvr = ad->get_landingpage_submit_rate();
      cmd_id_1 = ad->get_landingpage_submit_rate_cmd_id();
      cvr_end = AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED;
      break;
    case AdActionType::EVENT_APPOINT_FORM:
    case AdActionType::EVENT_APPOINT_JUMP_CLICK:
      if (is_feed) {
        cvr = ad->get_predict_score(PredictType::PredictType_game_appoint_rate);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_game_appoint_rate);
      } else {
        cvr = ad->get_predict_score(PredictType::PredictType_c2_game_appoint_rate);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_c2_game_appoint_rate);
      }
      break;
    case AdActionType::EVENT_ADD_WECHAT:
    case AdActionType::EVENT_JINJIAN:
    case AdActionType::AD_CREDIT_GRANT:
      if (is_feed) {
        cvr = ad->get_predict_score(PredictType::PredictType_deep_rate);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_deep_rate);
      } else {
        cvr = ad->get_predict_score(PredictType::PredictType_click2_deep_rate);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click2_deep_rate);
      }
      break;
    case AdActionType::AD_PURCHASE: {
      bool enable_app_envoke_product = cxr_params->app_invoked_purchase_product_map != nullptr &&
          cxr_params->app_invoked_purchase_product_map->find(ad->get_product_name()) !=
          cxr_params->app_invoked_purchase_product_map->end();
      bool enable_app_envoke_account = cxr_params->app_invoked_purchase_account_map != nullptr &&
          cxr_params->app_invoked_purchase_account_map->find(ad->get_account_id()) !=
          cxr_params->app_invoked_purchase_account_map->end();
      bool enable_app_conversion_purchase_product =
            cxr_params->app_conversion_purchase_product_map != nullptr &&
            cxr_params->app_conversion_purchase_product_map->find(ad->get_product_name()) !=
              cxr_params->app_conversion_purchase_product_map->end();
      bool enable_app_conversion_purchase_account =
            cxr_params->app_conversion_purchase_account_map != nullptr &&
            cxr_params->app_conversion_purchase_account_map->find(ad->get_account_id()) !=
              cxr_params->app_conversion_purchase_account_map->end();
      if (ad->get_campaign_type() == AdEnum::FANS_LIVE_STREAM_PROMOTE) {
        cvr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_cvr);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);
        cvr_end = AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED;
      } else if (ad->get_second_industry_id_v5() == 2012 && enable_ad_rank_clk2purchase_predict &&
        (!disable_industry_clk2purchase || session_data->get_page_id() != 10002)) {
        if (enable_industry_clk2purchase_mix) {
          cvr = ad->get_predict_score(PredictType::PredictType_industry_server_show_cvr) *
                ad->get_predict_score(PredictType::PredictType_industry_clk2_pay) *
                industry_clk2purchase_ratio_soft +
                ad->get_predict_score(PredictType::PredictType_industry_server_show_cvr_other) *
                ad->get_predict_score(PredictType::PredictType_industry_clk2_pay_other) *
                industry_clk2purchase_other_ratio_soft;
        } else {
          cvr = ad->get_predict_score(PredictType::PredictType_industry_clk2_pay);
        }
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_industry_clk2_pay);
      } else if ((ad->get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict) ||
                  enable_mini_app_roas_invoked_product || enable_mini_app_roas_invoked_account) {
        cvr = ad->get_predict_score(PredictType::PredictType_purchase);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_purchase);
      } else if (enable_app_conversion_purchase_product || enable_app_conversion_purchase_account) {
        cvr = ad->get_predict_score(PredictType::PredictType_purchase);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_purchase);
      } else if (ad->get_campaign_type() == AdEnum::APP) {
        if (!is_feed && cxr_params->enable_deep_middle_model_exp
            && cxr_params->enable_deep_middle_model_convpay_exp &&
            ad->get_deep_conversion_type() !=
            kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) {
          cvr = (1 - cxr_params->deep_middle_model_ensemble_rate)
                * ad->get_predict_score(PredictType::PredictType_purchase) +
                cxr_params->deep_middle_model_ensemble_rate * ad->get_deep_middle_model_predict_rate();
        } else {
          cvr = ad->get_predict_score(PredictType::PredictType_purchase);
        }
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_purchase);
      } else if (ad->get_campaign_type() == AdEnum::APP_ADVANCE &&
          (enable_app_envoke_product || enable_app_envoke_account)) {
        cvr = ad->get_predict_score(PredictType::PredictType_purchase);
        cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_purchase);
      } else {
        if (is_feed) {
          cvr = ad->get_predict_score(PredictType::PredictType_click_purchase_rate_single_bid);
          cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click_purchase_rate_single_bid);
        } else {
          cvr = ad->get_predict_score(PredictType::PredictType_click2_purchase_rate_single_bid);
          cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_click2_purchase_rate_single_bid);
        }
      }
      // 付费 roi 双出价在小程序、小说上，继承上面付费单出价的切分点
      bool use_purchase_result =
          (ad->get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict) ||
          enable_mini_app_roas_invoked_product ||
          enable_mini_app_roas_invoked_account ||
          enable_app_conversion_purchase_product ||
          enable_app_conversion_purchase_account;
      // 付费 roi 双出价特殊处理
      if (ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY
        && !use_purchase_result) {
        if (ad->get_is_purchase_pay_test()) {
          cvr = ad->get_app_conversion_rate() * ad->get_predict_score(PredictType::PredictType_purchase);
          cmd_id_1 = ad->get_app_conversion_rate_cmd_id();
          cmd_id_2 = ad->get_predict_cmd_id(PredictType::PredictType_purchase);
          cvr_end = AdActionType::AD_PURCHASE;
        } else {
          cvr = ad->get_app_conversion_rate();
          cmd_id_1 = ad->get_app_conversion_rate_cmd_id();
          cvr_end = AdActionType::AD_CONVERSION;
        }
      }
      break;
    }
    default:
      // 不支持的投放目标，cvr 置 0
      cvr = 0.0;
      session_data->dot_perf->Count(1, "native_unify_invalid_ocpx",
          AdActionType_Name(ad->get_ocpx_action_type()));
  }
  // 粉条作品推广给更多人 CPM lowest cost 的 cvr 一律取 wtr
  if (ad->get_campaign_type() == AdEnum::AD_FANSTOP_TO_SHOW &&
      ad->get_bid_type() == AdEnum::CPM && ad->get_is_lowest_cost()) {
    cvr = ad->get_wtr();
    cmd_id_1 = ad->get_wtr_cmd_id();
    cvr_end = AdActionType::AD_FANS_TOP_FOLLOW;
  }
  // 设置 unify cvr
  ad->SetUnifyCvr(cvr, cvr_start, cvr_end, cmd_id_1, cmd_id_2);
}

void SearchNativeUnifyFillCxr::SetUnifyLtv(const NativeUnifyFillCxrParams* cxr_params,
    const ContextData* session_data, AdCommon* ad) {
  /**********
  * 对内循环来说，ltv 就是 gmv，只对 ROI 相关目标设置
  **********/
  double gmv;
  AdActionType gmv_start;
  AdActionType gmv_end = ad->get_ocpx_action_type();
  if (SPDM_enable_fix_undefine_ltv_info(session_data->get_spdm_ctx())) {
    gmv = 0.0;
    gmv_start = AdActionType::UNKNOWN_ACTION_TYPE;
  }

  bool enable_mini_app_roas_invoked_product =
      cxr_params->mini_app_roas_invoked_product_map != nullptr &&
      cxr_params->mini_app_roas_invoked_product_map->find(ad->get_product_name()) !=
      cxr_params->mini_app_roas_invoked_product_map->end();
  bool enable_mini_app_roas_invoked_account =
      cxr_params->mini_app_roas_invoked_account_map != nullptr &&
      cxr_params->mini_app_roas_invoked_account_map->find(ad->get_account_id()) !=
      cxr_params->mini_app_roas_invoked_account_map->end();
  bool enable_request_game_industry_pay_ltv
            = cxr_params->enable_request_game_industry_pay_ltv;
  bool enable_request_minigame_industry_pay_ltv
            = cxr_params->enable_request_minigame_industry_pay_ltv;
  bool enable_game_industry_pay_ltv_out = cxr_params->enable_game_industry_pay_ltv_out;
  bool enable_playlet_inovked_predict = cxr_params->enable_playlet_inovked_predict;
  bool game_industry_ensemble_pv_filter_lower_ratio
    = cxr_params->game_industry_ensemble_pv_filter_lower_ratio;
  bool game_industry_ensemble_pv_filter_upper_ratio
    = cxr_params->game_industry_ensemble_pv_filter_upper_ratio;
  bool enable_game_bagging_ltv = cxr_params->enable_game_bagging_ltv;

  switch (ad->get_ocpx_action_type()) {
    case AdActionType::AD_FANS_TOP_ROI:
        if (cxr_params->enable_roas_unify_fill && ad->Is(AdFlag::is_inner_live_roas)) {
        auto instance = UnivEspLiveAtvLoader::GetInstance();
        double atv = 0.0;
        if (instance) {
          instance->GetPostGmvPerOrder(ad->get_author_id(),
                            kuaishou::ad::AdActionType_Name(ad->get_ocpx_action_type()),
                            &atv);
        }
        atv = (atv > 0.0) ? atv / 1000.0 : cxr_params->const_roas_live_atv;
        if (ad->get_speed() != 5) {
          atv = atv * cxr_params->roas_live_soft_atv_rate;
        }
        gmv = ad->get_order_paid() * atv;
        ad->set_esplive_roas_tag(true);
      } else {
        gmv = ad->get_gmv();
      }
      if (ad->Is(AdFlag::is_inner_live_roas) &&
      !(session_data->get_is_search_request() && cxr_params->enable_search_live_roas_filter)) {
        gmv = ad->get_inner_live_roas();
        ad->set_esplive_roas_tag(true);
      }
      gmv_start = AdActionType::AD_STANDARD_LIVE_PLAYED_STARTED;
      break;
    case AdActionType::AD_MERCHANT_ROAS:
    case AdActionType::AD_MERCHANT_T7_ROI:
    case AdActionType::AD_STOREWIDE_ROAS:
    case kuaishou::ad::CID_ROAS:
       if (cxr_params->enable_roas_unify_fill &&
           (ad->Is(AdFlag::is_inner_live_roas) || ad->Is(AdFlag::is_inner_live_t7_roi))) {
        auto instance = UnivEspLiveAtvLoader::GetInstance();
        double atv = 0.0;
        if (instance) {
          instance->GetPostGmvPerOrder(ad->get_author_id(),
                            kuaishou::ad::AdActionType_Name(ad->get_ocpx_action_type()),
                            &atv);
        }
        atv = (atv > 0.0) ? atv / 1000.0 : cxr_params->const_roas_live_atv;
        if (ad->get_speed() != 5) {
          atv = atv * cxr_params->roas_live_soft_atv_rate;
        }
        gmv = ad->get_order_paid() * atv;
        ad->set_esplive_roas_tag(true);
      } else {
        gmv = ad->get_gmv();
      }
      if ((ad->Is(AdFlag::is_inner_live_roas) || ad->Is(AdFlag::is_inner_live_t7_roi))) {
        bool exec_roi_7 = ad->get_ocpx_action_type() == AdActionType::AD_MERCHANT_T7_ROI;
        gmv = exec_roi_7 ? ad->GetInnerLiveRoas7days() : ad->get_inner_live_roas();
        ad->set_esplive_roas_tag(true);
      }
      if (ad->Is(AdFlag::is_photo)) {
        // 磁力金牛短视频 gmv 以订单支付为起点
        gmv_start = AdActionType::EVENT_ORDER_PAIED;
        // 搜索的短视频直接切 imp -> roi
        if (session_data->get_is_search_request() && cxr_params->is_search_roi_model) {
          gmv_start = AdActionType::AD_ITEM_IMPRESSION;
        }
      } else {
        // 其他以直播间进人为起点
        gmv_start = AdActionType::AD_STANDARD_LIVE_PLAYED_STARTED;
      }
      break;
    case AdActionType::AD_MERCHANT_FOLLOW_ROI:
      gmv = ad->get_merchant_follow_ltv();
      gmv_start = AdActionType::AD_MERCHANT_FOLLOW;
      break;
    case AdActionType::AD_SEVEN_DAY_ROAS:
      if (cxr_params->enable_pay_amount_2and7) {
        gmv = (cxr_params->pay_amount_alpha *
        ad->get_predict_score(PredictType::PredictType_1_day_pay_amount) +
          cxr_params->pay_amount_beta *
          ad->get_predict_score(PredictType::PredictType_2_7_day_pay_amount));
      } else {
        gmv = ad->get_predict_score(PredictType::PredictType_7_day_game_conv_ltv);
      }
      gmv_start = AdActionType::AD_CONVERSION;
      break;
    case AdActionType::AD_ROAS:
      if (cxr_params->enable_request_industry_pay_ltv &&
          ad->Is(AdFlag::is_paid_duanju_ad) &&
          ((ad->get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict) ||
           enable_mini_app_roas_invoked_product ||
           enable_mini_app_roas_invoked_account)) {
        gmv = (1 - cxr_params->industry_pay_ltv_ensemble_weight) *
                ad->get_predict_score(PredictType::PredictType_game_conv_ltv) +
              cxr_params->industry_pay_ltv_ensemble_weight *
                ad->get_predict_score(PredictType::PredictType_industry_pay_ltv);
        if (cxr_params->disable_request_industry_pay_ltv) {
          gmv = ad->get_predict_score(PredictType::PredictType_game_conv_ltv);
        }
      } else if (ad->Is(AdFlag::is_game_ad) &&
        (enable_request_game_industry_pay_ltv || enable_request_minigame_industry_pay_ltv)) {
        gmv = ad->get_predict_score(PredictType::PredictType_game_conv_ltv);
        if (!((ad->get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict) ||
              enable_mini_app_roas_invoked_product || enable_mini_app_roas_invoked_account)) {
          double game_gmv = gmv;
          if (enable_request_game_industry_pay_ltv || enable_request_minigame_industry_pay_ltv) {
            game_gmv =
              ad->get_predict_score(PredictType::PredictType_game_industry_pay_ltv);
          }
          bool is_legalvalue_game_industry_pay_ltv = true;
          double filter_lower_ratio = 0.2;
          double filter_upper_ratio = 5.0;
          if (enable_game_bagging_ltv) {
            filter_upper_ratio = game_industry_ensemble_pv_filter_upper_ratio;
            filter_lower_ratio = game_industry_ensemble_pv_filter_lower_ratio;
          }
          if (enable_game_industry_pay_ltv_out) {
            is_legalvalue_game_industry_pay_ltv =
              (game_gmv < filter_upper_ratio * gmv) &&
              (game_gmv > filter_lower_ratio * gmv);
          }
          if (is_legalvalue_game_industry_pay_ltv) {
            if (enable_request_game_industry_pay_ltv && ad->Is(AdFlag::is_game_ad)) {
              gmv = (1 - cxr_params->game_industry_pay_ltv_ensemble_weight) * gmv +
                cxr_params->game_industry_pay_ltv_ensemble_weight * game_gmv;
              gmv = gmv * cxr_params->game_industry_pay_ltv_reweight_weight;
            }
            if (enable_request_minigame_industry_pay_ltv && ad->Is(AdFlag::is_wechat_game_ad)) {
              gmv = (1 - cxr_params->minigame_industry_pay_ltv_ensemble_weight) * gmv +
                cxr_params->minigame_industry_pay_ltv_ensemble_weight * game_gmv;
              gmv = gmv * cxr_params->minigame_industry_pay_ltv_reweight_weight;
            }
          }
          ad->set_game_bagging_ltv(gmv);
        }
      } else {
        gmv = ad->get_predict_score(PredictType::PredictType_game_conv_ltv);
      }
      if ((ad->get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict) ||
          enable_mini_app_roas_invoked_product || enable_mini_app_roas_invoked_account) {
        gmv_start = AdActionType::AD_PURCHASE;
      } else {
        gmv_start = AdActionType::AD_CONVERSION;
      }
      break;
    case AdActionType::AD_CID_ROAS:
      gmv = ad->get_predict_score(PredictType::PredictType_merchant_ltv);
      gmv_start = AdActionType::AD_ITEM_IMPRESSION;
      break;
    case AdActionType::AD_IAA_ROAS:
    case AdActionType::AD_SERIAL_IAA_ROAS:
      gmv_start = kuaishou::ad::AD_CONVERSION;
      gmv = ad->get_predict_score(PredictType::PredictType_key_action_ltv0);
      break;
    case AdActionType::AD_ROAS_IAAP:
      gmv_start = kuaishou::ad::AD_CONVERSION;
      gmv = ad->get_predict_score(PredictType::PredictType_key_action_ltv0) +
            ad->get_predict_score(PredictType::PredictType_game_conv_ltv);
      break;
    case AdActionType::AD_IAA_7DAY_ROAS:
      gmv_start = kuaishou::ad::AD_CONVERSION;
      if (SPDM_enable_search_outer_cmd_independent(session_data->get_spdm_ctx())) {
        gmv = ad->get_predict_score(PredictType::PredictType_search_industry_game_iaa_ltv7);
      } else {
        gmv = ad->get_predict_score(PredictType::PredictType_industry_game_iaa_ltv7);
      }
      break;
    default:
      return;
  }
  // 设置 unify ltv
  ad->SetUnifyLtv(gmv, gmv_start, gmv_end, ad->get_gmv_cmd_id());
}

void SearchNativeUnifyFillCxr::MonitorUnifyCxr(const NativeUnifyFillCxrParams* cxr_params,
    const ContextData* session_data, AdCommon* ad) {
  // 控制一下打点频率
  if (ad_base::AdRandom::GetDouble() >= cxr_params->native_unify_cxr_perf_ratio) {
    return;
  }
  // NOTE: 由于 item_type 字段默认值是 ITEM_PHOTO，最稳妥的判断顺序是 live->p2l->photo
  std::string item = ad->Is(AdFlag::is_live) ? "live" : (ad->Is(AdFlag::is_p2l) ? "p2l" : "photo");
  std::string ocpx = AdActionType_Name(ad->get_ocpx_action_type());
  std::string dtype = kuaishou::ad::AdCallbackLog_EventType_Name(ad->get_deep_conversion_type());
  // ctr
  double ctr = ad->get_unify_ctr_info().value * 1e6;
  std::string ctr_start = AdActionType_Name(ad->get_unify_ctr_info().s_type);
  std::string ctr_end = AdActionType_Name(ad->get_unify_ctr_info().e_type);
  std::string ctr_pair = absl::StrCat(ctr_start, "->", ctr_end);
  // cvr
  double cvr = ad->get_unify_cvr_info().value * 1e6;
  std::string cvr_start = AdActionType_Name(ad->get_unify_cvr_info().s_type);
  std::string cvr_end = AdActionType_Name(ad->get_unify_cvr_info().e_type);
  std::string cvr_pair = absl::StrCat(cvr_start, "->", cvr_end);
  // deep_cvr
  double deep_cvr = ad->get_unify_deep_cvr_info().value * 1e6;
  std::string deep_cvr_start = AdActionType_Name(ad->get_unify_deep_cvr_info().s_type);
  std::string deep_cvr_end = AdActionType_Name(ad->get_unify_deep_cvr_info().e_type);
  std::string deep_cvr_pair = absl::StrCat(deep_cvr_start, "->", deep_cvr_end);
  // gmv
  double gmv = ad->get_unify_ltv_info().value * 1e6;
  std::string gmv_start = AdActionType_Name(ad->get_unify_ltv_info().s_type);
  std::string gmv_end = AdActionType_Name(ad->get_unify_ltv_info().e_type);
  std::string gmv_pair = absl::StrCat(gmv_start, "->", gmv_end);
  // 正值才打点
  if (ctr > 0) {
    RANK_DOT_STATS(session_data, ctr, "native_unify_ctr", item, ocpx, ctr_pair);
  }
  if (cvr > 0) {
    RANK_DOT_STATS(session_data, cvr, "native_unify_cvr", item, ocpx, cvr_pair);
  }
  if (deep_cvr > 0) {
    if (ad->get_deep_conversion_type() != kuaishou::ad::AdCallbackLog_EventType_EVENT_UNKNOWN) {
      RANK_DOT_STATS(session_data, deep_cvr, "native_unify_deep_cvr", item, dtype, deep_cvr_pair);
    } else {
      RANK_DOT_STATS(session_data, deep_cvr, "native_unify_deep_cvr", item, ocpx, deep_cvr_pair);
    }
  }
  if (gmv > 0) {
    RANK_DOT_STATS(session_data, gmv, "native_unify_gmv", item, ocpx, gmv_pair);
  }
}

void SearchNativeUnifyFillCxr::SetMcbConstraintR(const NativeUnifyFillCxrParams* cxr_params,
    AdCommon* ad) {
  if (ad->get_bid_type() != AdEnum::MCB) {
    return;
  }
  // MCB 浅度约束
  const auto ctr_info = ad->get_unify_ctr_info();
  const auto cvr_info = ad->get_unify_cvr_info();
  const auto deep_cvr_info = ad->get_unify_deep_cvr_info();
  const auto ltv_info = ad->get_unify_ltv_info();
  const AdActionType ocpx_action_type = ad->get_ocpx_action_type();
  if ((deep_cvr_info.e_type != AdActionType::UNKNOWN_ACTION_TYPE ||
       ltv_info.e_type != AdActionType::UNKNOWN_ACTION_TYPE) &&
      cvr_info.e_type != ocpx_action_type) {  // 三段式
    ad->SetConstraintInfo(ctr_info.value * cvr_info.value, cvr_info.e_type);
  } else if (cvr_info.e_type != AdActionType::UNKNOWN_ACTION_TYPE &&
             ctr_info.e_type != ocpx_action_type) {  // 二段式
    ad->SetConstraintInfo(ctr_info.value, ctr_info.e_type);
  } else {
    ad->SetConstraintInfo(0, AdActionType::UNKNOWN_ACTION_TYPE);
  }
}

void SearchNativeUnifyFillCxr::SetUnifyDeepCvr(const NativeUnifyFillCxrParams* cxr_params,
     const ContextData* session_data, AdCommon* ad) {
  // 深度双出价类型 deep_cvr 设置
  switch (ad->get_deep_conversion_type()) {
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY:
      if (ad->get_ocpx_action_type() == AdActionType::EVENT_APP_INVOKED) {
        ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_appinvoke_nextstay),
                            AdActionType::EVENT_APP_INVOKED, AdActionType::EVENT_NEXTDAY_STAY,
                            ad->get_predict_cmd_id(PredictType::PredictType_appinvoke_nextstay));
      } else if (cxr_params->enable_deep_middle_model_exp
          && cxr_params->enable_deep_middle_model_nd_exp) {
        ad->SetUnifyDeepCvr((1 - cxr_params->deep_middle_model_ensemble_rate) *
                            ad->get_conv_nextstay() + cxr_params->deep_middle_model_ensemble_rate *
                            ad->get_deep_middle_model_predict_rate(),
                            AdActionType::AD_CONVERSION, AdActionType::EVENT_NEXTDAY_STAY,
                            ad->get_conv_nextstay_cmd_id());
      } else {
        ad->SetUnifyDeepCvr(ad->get_conv_nextstay(),
                            AdActionType::AD_CONVERSION, AdActionType::EVENT_NEXTDAY_STAY,
                            ad->get_conv_nextstay_cmd_id());
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_24H_STAY:
      ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_conv_24h_stay),
                          AdActionType::AD_CONVERSION, AdActionType::EVENT_24H_STAY,
                          ad->get_predict_cmd_id(PredictType::PredictType_conv_24h_stay));
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY:
      if (ad->get_ocpx_action_type() == AdActionType::EVENT_APP_INVOKED) {
        ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_purchase),
                            AdActionType::EVENT_APP_INVOKED, AdActionType::AD_PURCHASE,
                            ad->get_predict_cmd_id(PredictType::PredictType_purchase));
      } else {
        ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_purchase),
                            AdActionType::AD_CONVERSION, AdActionType::EVENT_PAY,
                            ad->get_predict_cmd_id(PredictType::PredictType_purchase));
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY:
      if (ad->get_is_purchase_pay_test()) {
        ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_purchase_ltv),
                            ad->get_ocpx_action_type(), AdActionType::AD_ROAS,
                            ad->get_predict_cmd_id(PredictType::PredictType_purchase_ltv));
        ad->SetUnifyLtv(ad->get_predict_score(PredictType::PredictType_purchase_ltv),
                        ad->get_ocpx_action_type(), AdActionType::AD_ROAS);
      } else {
        ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_game_conv_ltv),
                            ad->get_ocpx_action_type(), AdActionType::AD_ROAS,
                            ad->get_predict_cmd_id(PredictType::PredictType_game_conv_ltv));
        ad->SetUnifyLtv(ad->get_predict_score(PredictType::PredictType_game_conv_ltv),
                        ad->get_ocpx_action_type(), AdActionType::AD_ROAS);
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_RETENTION_DAYS:
      if (ad->get_ocpx_action_type() == AdActionType::AD_CONVERSION) {
        ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_everyday_stay),
            ad->get_ocpx_action_type(), AdActionType::EVENT_NEXTDAY_STAY,
            ad->get_predict_cmd_id(PredictType::PredictType_everyday_stay));
      }
      break;
    case kuaishou::ad::AdCallbackLog_EventType_EVENT_WEEK_STAY:
      if (ad->get_ocpx_action_type() == AdActionType::AD_CONVERSION) {
        ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_conv_7_day_stay),
            ad->get_ocpx_action_type(), AdActionType::EVENT_WEEK_STAY,
            ad->get_predict_cmd_id(PredictType::PredictType_conv_7_day_stay));
      }
      break;
    default:
      break;
  }

  // 深度单出价类型 deep_cvr 设置
  switch (ad->get_ocpx_action_type()) {
    case AdActionType::EVENT_PHONE_GET_THROUGH:
    case AdActionType::EVENT_INTENTION_CONFIRMED:
    case AdActionType::EVENT_ORDER_SUCCESSED:
    case AdActionType::EVENT_MEASUREMENT_HOUSE:
    case AdActionType::EVENT_PHONE_CARD_ACTIVATE:
      ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_lps_valid_clues),
                          AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED, ad->get_ocpx_action_type(),
                          ad->get_predict_cmd_id(PredictType::PredictType_lps_valid_clues));
      break;
    case AdActionType::EVENT_WECHAT_CONNECTED:
      if (!SPDM_enable_real_deep_wechat_connected_lps_cvr_set(session_data->get_spdm_ctx())) {
          ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_lps_valid_clues),
                          AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED, ad->get_ocpx_action_type(),
                          ad->get_predict_cmd_id(PredictType::PredictType_lps_valid_clues));
      }
      break;
    case AdActionType::EVENT_NEXTDAY_STAY:
      ad->SetUnifyDeepCvr(ad->get_conv_nextstay(),
                          AdActionType::AD_CONVERSION, ad->get_ocpx_action_type(),
                          ad->get_conv_nextstay_cmd_id());
      break;
    case AdActionType::EVENT_7_DAY_PAY_TIMES:
      if (cxr_params->enable_split_7day_paytimes_deep_r) {
        ad->SetUnifyDeepCvr(std::max(ad->get_predict_score(PredictType::PredictType_1_day_pay_times) +
          ad->get_predict_score(PredictType::PredictType_2_7_day_pay_times) -
          ad->get_predict_score(PredictType::PredictType_purchase), 0.0),
                              kuaishou::ad::AD_CONVERSION,
                              ad->get_ocpx_action_type(),
                              ad->get_predict_cmd_id(PredictType::PredictType_1_day_pay_times));
      } else {
        ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_7_day_pay_times),
                              kuaishou::ad::AD_CONVERSION,
                              ad->get_ocpx_action_type(),
                              ad->get_predict_cmd_id(PredictType::PredictType_7_day_pay_times));
      }
      break;
    case AdActionType::EVENT_24H_STAY:
      ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_conv_24h_stay),
                          AdActionType::AD_CONVERSION,
                          ad->get_ocpx_action_type(),
                          ad->get_predict_cmd_id(PredictType::PredictType_conv_24h_stay));
      break;
    case AdActionType::EVENT_KEY_INAPP_ACTION:
      if (cxr_params->enable_deep_middle_model_exp && cxr_params->enable_deep_middle_model_kac_exp) {
        ad->SetUnifyDeepCvr((1 - cxr_params->deep_middle_model_ensemble_rate) *
                            ad->get_predict_score(PredictType::PredictType_conv_key_inapp_action_rate) +  // NOLINT
                            cxr_params->deep_middle_model_ensemble_rate *
                            ad->get_deep_middle_model_predict_rate(),
                            AdActionType::AD_CONVERSION, ad->get_ocpx_action_type(),
                            ad->get_predict_cmd_id(PredictType::PredictType_conv_key_inapp_action_rate));  // NOLINT
      } else {
        ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_conv_key_inapp_action_rate),  // NOLINT
                            AdActionType::AD_CONVERSION, ad->get_ocpx_action_type(),
                            ad->get_predict_cmd_id(PredictType::PredictType_conv_key_inapp_action_rate));  // NOLINT
      }
      break;
    case AdActionType::AD_PURCHASE_CONVERSION:
      if (ad->get_campaign_type() == AdEnum::FANS_LIVE_STREAM_PROMOTE) {
        if (ad->get_deep_conversion_type() != kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) {
          ad->SetUnifyDeepCvr(
            ad->get_predict_score(PredictType::PredictType_non_merchant_live_deep_cvr),
            AdActionType::AD_CONVERSION, ad->get_ocpx_action_type(),
            ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_deep_cvr));
        }
      }
      break;
    case AdActionType::AD_PURCHASE:
      if (ad->get_campaign_type() == AdEnum::FANS_LIVE_STREAM_PROMOTE) {
        ad->SetUnifyDeepCvr(
            ad->get_predict_score(PredictType::PredictType_non_merchant_live_deep_cvr),
            AdActionType::AD_LANDING_PAGE_FORM_SUBMITTED, ad->get_ocpx_action_type(),
            ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_deep_cvr));
      }
      break;
    case AdActionType::EVENT_RETENTION_DAYS:
      ad->SetUnifyDeepCvr(ad->get_predict_score(PredictType::PredictType_everyday_stay),
        AdActionType::AD_CONVERSION, ad->get_ocpx_action_type(),
        ad->get_predict_cmd_id(PredictType::PredictType_everyday_stay));
      break;
    default:
      break;
  }
}

void SearchNativeUnifyFillCxr::SetUnifySctr(const NativeUnifyFillCxrParams* cxr_params,
    const ContextData* session_data, AdCommon* ad) {
  // MCB 成效预估需要用，算 auction bid 也需要
  if (ad->Is(AdFlag::is_inner_loop_ad)) {
    SetInnerloopUnifySctr(cxr_params, session_data, ad);
  } else {
    SetOutloopUnifySctr(cxr_params, session_data, ad);
  }
}

void SearchNativeUnifyFillCxr::SetInnerloopUnifySctr(const NativeUnifyFillCxrParams* cxr_params,
    const ContextData* session_data, AdCommon* ad) {
  // MCB 成效预估需要用，算 auction bid 也需要
  double sctr = 1.0;
  int32_t cmd_id_1 = 0;
  AdActionType sctr_start = AdActionType::AD_DELIVERY;
  AdActionType sctr_end = AdActionType::AD_ITEM_IMPRESSION;
  if (ad->get_bid_type() == AdEnum::OCPM_DSP || ad->get_bid_type() == AdEnum::MCB) {
    bool is_server_ecpm = session_data->get_is_search_request();  // 目前只有搜索用 server ecpm
    bool use_default_sctr;  // 是否使用固定的 sctr
    if (ad->Is(AdFlag::is_fanstop)) {
      // 粉条使用预估 sctr 的场景：发现/同城双列
      if ((session_data->get_is_explore_or_selected_request() || session_data->get_is_nearby_request()) &&
          session_data->get_is_feed()) {
        use_default_sctr = false;
      } else {
        use_default_sctr = true;  // 粉条默认用固定的 sctr，不请求模型
      }
    } else {
      use_default_sctr = false;  // 非粉条都用预估 sctr
    }
    sctr = is_server_ecpm ? 1.0 :
        (use_default_sctr ? cxr_params->fanstop_default_sctr : ad->get_server_show_ctr());
    // 一些使用 real sctr 的特殊逻辑
    if (ad->get_real_sctr() > 0.1) {
      if (ad->get_is_brand() || ad->get_campaign_type() == AdEnum::AD_FANSTOP_TO_FANS) {
        sctr = ad->get_real_sctr();
      }
    }
    // 聚星保量固定价格计费
    if (cxr_params->juxing_supplement_exp_account->count(ad->get_account_id())) {
      sctr = cxr_params->juxing_sctr;
    }
  }
  // 搜索激励广告盒子 sctr 默认为 1.0
  if (session_data->get_rank_request()->ad_request().search_info().request_source() == 2) {
    sctr = 1.0;
  }
  ad->SetUnifySctr(sctr, sctr_start, sctr_end, cmd_id_1);
}

void SearchNativeUnifyFillCxr::SetOutloopUnifySctr(const NativeUnifyFillCxrParams* cxr_params,
    const ContextData* session_data, AdCommon* ad) {
  // MCB 成效预估需要用，算 auction bid 也需要
  double sctr;
  int32_t cmd_id_1 = 0;
  AdActionType sctr_start = AdActionType::AD_DELIVERY;
  AdActionType sctr_end = AdActionType::AD_ITEM_IMPRESSION;
  if (ad->Is(AdFlag::is_live) || ad->Is(AdFlag::is_p2l)) {  // 直播直投或作品引流
    sctr = ad->get_predict_score(PredictType::PredictType_non_merchant_live_sctr);
    cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_non_merchant_live_sctr);
    if (ad->Is(AdFlag::is_live)) {
      sctr_end = AdActionType::AD_LIVE_PLAYED_STARTED;
    }
  } else if (session_data->get_is_feed() && !ad->Is(AdFlag::is_inner_loop_ad)) {  // 普通作品双列
    sctr = ad->get_predict_score(PredictType::PredictType_server_client_show_rate);
    cmd_id_1 = ad->get_predict_cmd_id(PredictType::PredictType_server_client_show_rate);
    sctr_end = AdActionType::AD_PHOTO_IMPRESSION;
  } else if (ad->get_server_show_ctr() > FLT_EPSILON) {  // 其他大部分普通作品
    sctr = ad->get_server_show_ctr();
    cmd_id_1 = ad->get_server_show_ctr_cmd_id();
  } else {  // 默认值兜底
    sctr = cxr_params->outer_native_default_sctr;
  }
  // 搜索激励广告盒子 sctr 默认为 1.0
  if (session_data->get_rank_request()->ad_request().search_info().request_source() == 2) {
    sctr = 1.0;
  }
  ad->SetUnifySctr(sctr, sctr_start, sctr_end, cmd_id_1);
}

}  // namespace ad_rank
}  // namespace ks
