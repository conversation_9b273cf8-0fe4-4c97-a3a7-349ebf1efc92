#include "teams/ad/ad_rank_search/search/node/search_unify_cxr/plugin/unify_cxr_plugin.h"

#include <math.h>
#include <algorithm>
#include <unordered_set>

#include "teams/ad/ad_base/src/common/auto_bid_group_tags.h"
#include "teams/ad/ad_rank_search/processor/factor/ranking_data.h"
#include "teams/ad/ad_rank_search/common/context_data.h"
#include "teams/ad/ad_rank_search/utils/kconf/kconf.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/ad_rank_search/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_rank_search/default/params/reserve_threshold_param.h"
#include "teams/ad/ad_rank_search/data/p2p_data/universe_conv_ratio_author_post_data_p2p/universe_conv_ratio_author_post_data_p2p.h"

namespace ks {
namespace ad_rank {

const char* SearchCpcUnifyPlugin::Name() {
  return "SearchCpcUnifyPlugin";
}
void SearchCpcUnifyPlugin::Clear() {}

bool SearchCpcUnifyPlugin::IsRun(const ContextData* session_data, const Params* params,
                              AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode SearchCpcUnifyPlugin::Process(ContextData* session_data, Params* reserve_threshold_params,
                                AdRankUnifyScene pos, AdList* ad_list) {
  bool is_single_col = session_data->get_is_thanos_request();
  for (auto p_ad : ad_list->Ads()) {
    if (p_ad == nullptr || p_ad->get_bid_type() != kuaishou::ad::AdEnum::CPC ||
        !p_ad->Is(AdFlag::is_search_bidword) ||
        p_ad->get_quick_search() > 0) {
      continue;
    }
    SEARCH_AD_LIST_SKIP_LIVE_AD(session_data, p_ad);
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad);
    auto& ad = *p_ad;
    double origin_server_client_show_rate =
        ad.get_predict_score(engine_base::PredictType::PredictType_server_client_show_rate);
    int32_t server_client_show_rate_cmd_id =
        ad.get_predict_cmd_id(engine_base::PredictType::PredictType_server_client_show_rate);
    if (is_single_col) {
      switch (ad.get_bid_type()) {
        case kuaishou::ad::AdEnum::CPC:
          ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
              kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
          ad.SetUnifyCvr(1.0,
              kuaishou::ad::AD_ITEM_CLICK, kuaishou::ad::AD_ITEM_CLICK);
          break;
        default:
          break;
      }
    } else {
      switch (ad.get_bid_type()) {
        case kuaishou::ad::AdEnum::CPC:
          ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
              kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
              ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
          ad.SetUnifyCvr(ad.get_cvr(),
              kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_ITEM_CLICK, ad.get_cvr_cmd_id());
          break;
        default:
          break;
      }
    }
  }
  return StraRetCode::SUCC;
}

const char* SearchPhotoUnfiyPlugin::Name() {
  return "SearchPhotoUnfiyPlugin";
}
void SearchPhotoUnfiyPlugin::Clear() {}

bool SearchPhotoUnfiyPlugin::IsRun(const ContextData* session_data, const Params* params,
                              AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode SearchPhotoUnfiyPlugin::Process(ContextData* session_data, Params* reserve_threshold_params,
                                AdRankUnifyScene pos, AdList* ad_list) {
  auto* params = dynamic_cast<ReserveThresholdParams*>(reserve_threshold_params);
  if (KS_UNLIKELY(params == nullptr)) {
    return StraRetCode::SUCC;
  }
  // 预处理好独立的 ocpx, 这样才能做到 ab 一次获取
  std::unordered_set<std::string> search_unify_ocpx_type;
  if (session_data->get_is_search_request()) {
    const auto& enable_search_ocpx_map = RankKconfUtil::enableSearchOcpxUnifyR();
    for (auto& k_v : *enable_search_ocpx_map) {
      bool enable_dependent = session_data->get_spdm_ctx().TryGetBoolean(k_v.second, false);
      if (enable_dependent) {
        search_unify_ocpx_type.insert(k_v.first);
      }
    }
  }
  bool is_single_col = session_data->get_is_thanos_request();
  for (auto* p_ad : ad_list->Ads()) {
    AD_LIST_SKIP_LIVE_AD(session_data, p_ad);
    AD_LIST_SKIP_POP_RECRUIT_AD(session_data, p_ad);
    SEARCH_AD_LIST_SKIP_LIVE_AD(session_data, p_ad)
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad)
    auto &ad = *p_ad;
    if (session_data->get_is_search_request() && !search_unify_ocpx_type.empty()) {
      std::string ocpx_type_str = kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type());
      if (search_unify_ocpx_type.find(ocpx_type_str) == search_unify_ocpx_type.end()) {
        continue;
      }
    }
    if (search_unify_ocpx_type.empty()) {
      continue;
    }
    // 授信单出价精排表单切分黑名单，因为有些产品名称回传表单，所以表单切分进行屏蔽，针对产品粒度
    // 搜索统一单双列 ctr 切分点到 AD_DELIVERY-->AD_ITEM_IMPRESSION
    double origin_server_client_show_rate = ad.get_predict_score(
        PredictType::PredictType_server_client_show_rate);
    int32_t server_client_show_rate_cmd_id = ad.get_predict_cmd_id(
        PredictType::PredictType_server_client_show_rate);
    switch (ad.get_ocpx_action_type()) {
      case kuaishou::ad::AD_CONVERSION:
        if (is_single_col) {
          ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
              kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
          ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                         kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                         ad.get_app_conversion_rate_cmd_id());
          // 主要去掉 SDPA 的激活预估值走 ensemble, 算法同学辛苦确认
        } else {
          ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
              kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
              ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
          ad.SetUnifyCvr(ad.get_app_conversion_rate(),
              kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
              ad.get_app_conversion_rate_cmd_id());
        }
        break;
      default:
        break;
    }
  }
  return StraRetCode::SUCC;
}

const char* SearchUnifyCxrPlugin::Name() {
  return "SearchUnifyCxrPlugin";
}

void SearchUnifyCxrPlugin::Clear() {}

bool SearchUnifyCxrPlugin::IsRun(const ContextData* session_data, const Params* params,
                           AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

// 搜索在统一 R 值有一些单独的修改，如果有修改请联系 @zhouxuan06 THX
StraRetCode SearchUnifyCxrPlugin::Process(ContextData* session_data, Params* reserve_threshold_params,
                                    AdRankUnifyScene pos, AdList* ad_list) {
  auto* params = dynamic_cast<ReserveThresholdParams*>(reserve_threshold_params);
  if (KS_UNLIKELY(params == nullptr)) {
    return StraRetCode::SUCC;
  }
  bool is_single_col = session_data->get_is_thanos_request();
  const auto purchase_roas_fuse_conf_ptr = RankKconfUtil::purchaseRoiFuseConf();
  const auto &fiction_app_conversion_purchase_product_map = RankKconfUtil::fictionConvPayWhiteList();
  const auto &fiction_app_conversion_purchase_account_map = RankKconfUtil::fictionConvPayAccountWhiteList();
  const auto &app_invoked_purchase_product_map = RankKconfUtil::appInvokedPurchaseProductWhiteList();
  const auto &app_invoked_purchase_account_map = RankKconfUtil::appInvokedPurchaseAccountWhiteList();
  const auto &mini_app_roas_invoked_product_map = RankKconfUtil::miniAppRoasInvokedProductWhiteList();
  const auto &mini_app_roas_invoked_account_map = RankKconfUtil::miniAppRoasInvokedAccountWhiteList();
  const auto &short_play_imp2pay_account_list = RankKconfUtil::shortPlayImp2payAccountList();
  const auto &short_play_clk2pay_account_list = RankKconfUtil::shortPlayClk2PayAccountList();
  const auto &short_play_clk2pay_roas_account_list = RankKconfUtil::shortPlayClk2PayRoasAccountList();
  const auto &fin_finance_lps_ensemble_industry =  RankKconfUtil::adFinFinanceLpsEnsembleIndustry();
  const auto &drop_high_ctr_account_map = RankKconfUtil::dropHighCtrAccountMap();
  const auto &edu_lps_ensemble_black_corp =  RankKconfUtil::adEduLpsEnsembleBlackCorp();
  bool enable_request_industry_pay_ltv =
        SPDM_enable_request_industry_pay_ltv(session_data->get_spdm_ctx());
  bool disable_request_industry_pay_ltv =
        SPDM_disable_request_industry_pay_ltv(session_data->get_spdm_ctx());
  double industry_pay_ltv_ensemble_weight =
        SPDM_industry_pay_ltv_ensemble_weight(session_data->get_spdm_ctx());
  bool enable_request_game_industry_pay_ltv =
        SPDM_enable_request_game_industry_pay_ltv(session_data->get_spdm_ctx());
  double game_industry_pay_ltv_ensemble_weight =
      SPDM_game_industry_pay_ltv_ensemble_weight(session_data->get_spdm_ctx());
  double game_industry_pay_ltv_reweight_weight =
      SPDM_game_industry_pay_ltv_reweight_weight(session_data->get_spdm_ctx());
  bool enable_request_minigame_industry_pay_ltv =
      SPDM_enable_request_minigame_industry_pay_ltv(session_data->get_spdm_ctx());
  double minigame_industry_pay_ltv_ensemble_weight =
      SPDM_minigame_industry_pay_ltv_ensemble_weight(session_data->get_spdm_ctx());
  double minigame_industry_pay_ltv_reweight_weight =
      SPDM_minigame_industry_pay_ltv_reweight_weight(session_data->get_spdm_ctx());
  bool enable_game_industry_pay_ltv_out =
      SPDM_enable_game_industry_pay_ltv_out(session_data->get_spdm_ctx());
  double game_industry_ensemble_pv_filter_lower_ratio =
      SPDM_game_industry_ensemble_pv_filter_lower_ratio(session_data->get_spdm_ctx());
  double game_industry_ensemble_pv_filter_upper_ratio =
      SPDM_game_industry_ensemble_pv_filter_upper_ratio(session_data->get_spdm_ctx());
  bool enable_game_bagging_ltv =
      SPDM_enable_game_bagging_ltv(session_data->get_spdm_ctx());

  // unify_cxr 合并迁移
  double deep_middle_model_ensemble_rate = RankKconfUtil::deepMiddleModelEnsambleRate();
  bool enable_deep_middle_model_exp = SPDM_enable_deep_middle_model_exp(session_data->get_spdm_ctx());
  bool enable_deep_middle_model_convpay_exp =
  SPDM_enable_deep_middle_model_convpay_exp(session_data->get_spdm_ctx());
  bool enable_deep_middle_model_kac_exp =
  SPDM_enable_deep_middle_model_kac_exp(session_data->get_spdm_ctx());

  bool enable_lpsep_cut_imp = false;

  // SDPA 统一模型 ensemble 参数: 开关 权重
  bool enable_sdpa_ensemble_invoked = SPDM_enable_sdpa_ensemble_invoked(session_data->get_spdm_ctx());
  double sdpa_ensemble_invoked_alpha =
    session_data->get_spdm_ctx().TryGetDouble("sdpa_ensemble_invoked_alpha", 1.0);
  double sdpa_ensemble_invoked_beta =
    session_data->get_spdm_ctx().TryGetDouble("sdpa_ensemble_invoked_beta", 0.0);
  bool enable_sdpa_ensemble_conversion = SPDM_enable_sdpa_ensemble_conversion(session_data->get_spdm_ctx());
  bool enable_sdpa_ecom_conv_main_pred = SPDM_enable_sdpa_ecom_conv_main_pred(session_data->get_spdm_ctx());
  bool enable_sdpa_novel_calibration = SPDM_enable_sdpa_novel_calibration(session_data->get_spdm_ctx());
  double sdpa_ensemble_conv_alpha =
    session_data->get_spdm_ctx().TryGetDouble("sdpa_ensemble_conv_alpha", 1.0);
  double sdpa_ensemble_conv_beta =
    session_data->get_spdm_ctx().TryGetDouble("sdpa_ensemble_conv_beta", 0.0);
  double novel_pos_sample_rate =
    session_data->get_spdm_ctx().TryGetDouble("novel_pos_sample_rate", 0.5);
  // 教育行业模型 ensemble 参数
  bool enable_edu_ensemble_imp_lps_ = SPDM_enable_edu_ensemble_imp_lps(session_data->get_spdm_ctx());
  bool enable_edu_ensemble_imp_lps_block_corp = SPDM_enable_edu_ensemble_imp_lps_block_corp(session_data->get_spdm_ctx());  // NOLINT
  double edu_ensemble_imp_lps_alpha =
    session_data->get_spdm_ctx().TryGetDouble("edu_ensemble_imp_lps_alpha", 1.0);
  double edu_ensemble_imp_lps_beta =
    session_data->get_spdm_ctx().TryGetDouble("edu_ensemble_imp_lps_beta", 0.0);

  // 网服行业 付费模型 ensemble 参数
  bool enable_wangfu_purchase_cmd = SPDM_enable_wangfu_purchase_cmd(session_data->get_spdm_ctx());
  bool enable_wangfu_purchase_ensemble = SPDM_enable_wangfu_purchase_ensemble(session_data->get_spdm_ctx());
  double wangfu_purchase_ensemble_alpha = SPDM_wangfu_purchase_ensemble_alpha(session_data->get_spdm_ctx());

  double sdpa_ensemble_ecom_conv_alpha =
    session_data->get_spdm_ctx().TryGetDouble("sdpa_ensemble_ecom_conv_alpha", 0.0);
  double sdpa_ensemble_ecom_conv_beta =
    session_data->get_spdm_ctx().TryGetDouble("sdpa_ensemble_ecom_conv_beta", 1.0);
  bool enable_car_ensemble_imp_lps_ = SPDM_enable_car_ensemble_imp_lps(session_data->get_spdm_ctx());
  bool enable_ensemble_imp_lps_fix_ = SPDM_enable_ensemble_imp_lps_fix(session_data->get_spdm_ctx());
  double car_ensemble_imp_lps_alpha =
    session_data->get_spdm_ctx().TryGetDouble("car_ensemble_imp_lps_alpha", 1.0);
  double car_ensemble_imp_lps_beta =
    session_data->get_spdm_ctx().TryGetDouble("car_ensemble_imp_lps_beta", 0.0);
  double finance_ensemble_imp_lps_alpha =
    session_data->get_spdm_ctx().TryGetDouble("finance_ensemble_imp_lps_alpha", 1.0);
  double finance_ensemble_imp_lps_beta =
    session_data->get_spdm_ctx().TryGetDouble("finance_ensemble_imp_lps_beta", 0.0);
  double default_high_ctr_threshold =
      session_data->get_spdm_ctx().TryGetDouble("high_ctr_threshold", 1.0);

  bool enable_maintower_ensemble_imp_lps_ = SPDM_enable_maintower_ensemble_imp_lps(session_data->get_spdm_ctx());  // NOLINT

  bool enable_ad_rank_clk2purchase_predict = SPDM_enable_ad_rank_clk2purchase_predict(session_data->get_spdm_ctx());  // NOLINT
  bool enable_ad_rank_industry_server_show_cvr_predict =
      SPDM_enable_ad_rank_industry_server_show_cvr_predict(session_data->get_spdm_ctx());
  bool enable_ad_rank_industry_purchase_predict =
      SPDM_enable_ad_rank_industry_purchase_predict(session_data->get_spdm_ctx());
  bool enable_playlet_inovked_predict =
      SPDM_enable_playlet_inovked_predict(session_data->get_spdm_ctx());
  bool enable_ad_rank_clk2purchase_roas_predict =
      SPDM_enable_ad_rank_clk2purchase_roas_predict(session_data->get_spdm_ctx());
  bool disable_industry_clk2purchase =
      SPDM_disable_industry_clk2purchase(session_data->get_spdm_ctx());
  bool disable_industry_clk2purchase_hard =
      SPDM_disable_industry_clk2purchase_hard(session_data->get_spdm_ctx());
  bool enable_industry_clk2purchase_mix =
      SPDM_enable_industry_clk2purchase_mix(session_data->get_spdm_ctx());
  double industry_clk2purchase_ratio = SPDM_industry_clk2purchase_ratio(session_data->get_spdm_ctx());
  double industry_clk2purchase_other_ratio = SPDM_industry_clk2purchase_other_ratio(session_data->get_spdm_ctx());  // NOLINT
  bool enable_uplift_order_paied = SPDM_enable_uplift_order_paied(session_data->get_spdm_ctx());
  bool enable_search_roas_one_stage = SPDM_enable_search_roas_one_stage(session_data->get_spdm_ctx());
  bool enable_split_7day_paytimes_deep_r = SPDM_enable_split_7day_paytimes_deep_r(session_data->get_spdm_ctx());  // NOLINT
  bool enable_pay_amount_2and7 = SPDM_enable_pay_amount_2and7(session_data->get_spdm_ctx());
  double uplift_order_high_bound = SPDM_uplift_order_high_bound(session_data->get_spdm_ctx());
  double uplift_order_low_bound = SPDM_uplift_order_low_bound(session_data->get_spdm_ctx());
  double uplift_coef = SPDM_uplift_coef(session_data->get_spdm_ctx());
  const auto& order_paied_jingxuan_cali_map = RankKconfUtil::orderPaiedJingXuanCaliMap();
  const auto& roas_jingxuan_cali_map = RankKconfUtil::roasJingXuanCaliMap();
  bool enable_outer_game_invoke_link = SPDM_enable_outer_game_invoke_link(session_data->get_spdm_ctx());
  const auto& game_invoke_link_config = RankKconfUtil::outerGameInvokeLinkConfig();
  double cid_roas_reset_cvr = SPDM_cid_roas_reset_cvr(session_data->get_spdm_ctx());
  bool enable_search_pay_split_to_impression =
      SPDM_enable_search_pay_split_to_impression(session_data->get_spdm_ctx());
  bool enable_ad_iaa_7day_roas_predict =
    SPDM_enable_ad_iaa_7day_roas_predict(session_data->get_spdm_ctx());

  // 预处理好独立的 ocpx, 这样才能做到 ab 一次获取
  std::unordered_set<std::string> search_unify_ocpx_type;
  if (session_data->get_is_search_request()) {
    const auto & enable_search_ocpx_map = RankKconfUtil::enableSearchOcpxUnifyR();
    for (auto& k_v : *enable_search_ocpx_map) {
      bool enable_dependent = session_data->get_spdm_ctx().TryGetBoolean(k_v.second, false);
      if (enable_dependent) {
        search_unify_ocpx_type.insert(k_v.first);
      }
    }
  }
  auto instance = UnivEspLiveAtvLoader::GetInstance();
  RANK_DOT_COUNT(session_data, 1, "ad_rank.unify_cxr_plugin");
  for (auto* p_ad : ad_list->Ads()) {
    AD_LIST_SKIP_LIVE_AD(session_data, p_ad);
    AD_LIST_SKIP_POP_RECRUIT_AD(session_data, p_ad);
    SEARCH_AD_LIST_SKIP_LIVE_AD(session_data, p_ad)
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad)
    auto &ad = *p_ad;
    if (session_data->get_is_search_request() && !search_unify_ocpx_type.empty()) {
      std::string ocpx_type_str = kuaishou::ad::AdActionType_Name(ad.get_ocpx_action_type());
      if (search_unify_ocpx_type.find(ocpx_type_str) != search_unify_ocpx_type.end()) {
        continue;
      }
    }
    // 单列
    bool is_live = ad.Is(AdFlag::is_amd_live_campaign);
        // 授信单出价精排表单切分黑名单，因为有些产品名称回传表单，所以表单切分进行屏蔽，针对产品粒度
     // 搜索统一单双列 ctr 切分点到 AD_DELIVERY-->AD_ITEM_IMPRESSION
    bool enable_fiction_app_conversion_purchase_product =
          fiction_app_conversion_purchase_product_map != nullptr &&
          fiction_app_conversion_purchase_product_map->find(ad.get_product_name()) !=
            fiction_app_conversion_purchase_product_map->end();
    bool enable_fiction_app_conversion_purchase_account =
          fiction_app_conversion_purchase_account_map != nullptr &&
          fiction_app_conversion_purchase_account_map->find(ad.get_account_id()) !=
            fiction_app_conversion_purchase_account_map->end();
    bool enable_purchase_cut_by_app_invoked_product =
          app_invoked_purchase_product_map != nullptr &&
          app_invoked_purchase_product_map->find(ad.get_product_name()) !=
            app_invoked_purchase_product_map->end();
    bool enable_purchase_cut_by_app_invoked_account =
          app_invoked_purchase_account_map != nullptr &&
          app_invoked_purchase_account_map->find(ad.get_account_id()) !=
            app_invoked_purchase_account_map->end();
    bool enable_mini_app_roas_invoked_product =
          mini_app_roas_invoked_product_map != nullptr &&
          mini_app_roas_invoked_product_map->find(ad.get_product_name()) !=
            mini_app_roas_invoked_product_map->end();
    bool enable_mini_app_roas_invoked_account =
          mini_app_roas_invoked_account_map != nullptr &&
          mini_app_roas_invoked_account_map->find(ad.get_account_id()) !=
            mini_app_roas_invoked_account_map->end();
    bool enable_short_play_clk2pay_account =
        short_play_clk2pay_account_list != nullptr &&
        short_play_clk2pay_account_list->find(ad.get_account_id()) != short_play_clk2pay_account_list->end();
    bool enable_short_play_clk2pay_roas_account =
        short_play_clk2pay_roas_account_list != nullptr &&
        short_play_clk2pay_roas_account_list->find(ad.get_account_id()) !=
        short_play_clk2pay_roas_account_list->end();
    bool enable_finance_lps_ensemble =
        fin_finance_lps_ensemble_industry != nullptr &&
        fin_finance_lps_ensemble_industry->find(ad.get_industry_id_v3()) !=
            fin_finance_lps_ensemble_industry->end();
    bool enable_drop_high_ctr_traffic = false;
    double high_ctr_threshold = default_high_ctr_threshold;
    if (drop_high_ctr_account_map != nullptr &&
        drop_high_ctr_account_map->find(ad.get_account_id()) !=
            drop_high_ctr_account_map->end()) {
        enable_drop_high_ctr_traffic = true;
        high_ctr_threshold = drop_high_ctr_account_map->at(ad.get_account_id());
    }
    bool enable_mini_game_invoke_link = false;
    if (enable_outer_game_invoke_link) {
      bool is_mini_game = (((ad.get_landing_page_component() & 1) == 1 ||
                          (ad.get_landing_page_component() & 2) == 2) &&
                          ad.get_industry_parent_id_v3() == 1018);
      if (game_invoke_link_config != nullptr) {
        auto iter_product = game_invoke_link_config->find(ad.get_product_name());
        bool is_game_invoke_link_product = (iter_product != game_invoke_link_config->end());
        enable_mini_game_invoke_link = (is_game_invoke_link_product && is_mini_game);
      }
    }

    double click2_deep_rate = ad.get_predict_score(PredictType::PredictType_click2_deep_rate);
    auto click2_deep_rate_cmd_id = ad.get_predict_cmd_id(PredictType::PredictType_click2_deep_rate);
    double origin_server_client_show_rate =
        ad.get_predict_score(PredictType::PredictType_server_client_show_rate);
    // 搜索激励盒子
    if (session_data->get_rank_request()->ad_request().search_info().request_source() == 2) {
      origin_server_client_show_rate = 1.0;
      if (session_data->get_pos_manager_base().IsSearchInspireThanosRequest()) {
        ad.set_server_show_ctr(1.0);
      }
    }
    int32_t server_client_show_rate_cmd_id =
        ad.get_predict_cmd_id(PredictType::PredictType_server_client_show_rate);
    auto corporation_name = std::string(
          ad.Attr(ItemIdx::fd_ACCOUNT_corporation_name).GetStringValue(ad.AttrIndex()).value_or(""));
    switch (ad.get_ocpx_action_type()) {
        case kuaishou::ad::AD_LIVE_EFFECTIVE_PLAY:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_cvr(),
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(), ad.get_cvr_cmd_id());
            } else {  // 处理双列
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(1.0,
                    kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_cvr_cmd_id());
            }
            break;
        case kuaishou::ad::AD_POI_CLICK:
        case kuaishou::ad::AD_GOODS_CLICK:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_lsp_photo_item_click(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_lsp_photo_item_click_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_lsp_photo_item_click(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_lsp_photo_item_click_cmd_id());
            }
            break;
        case kuaishou::ad::AD_ITEM_CLICK:
            if (is_single_col) {
                // 专推单列直播预约，使用 sctr * cvr 模型
                if (ad.get_promotion_type() == kuaishou::ad::AdEnum::SPECIALTY_PROMOTION ||
                    ad.get_promotion_type() == kuaishou::ad::AdEnum::FLASH_PROMOTION) {
                    ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                    ad.SetUnifyCvr(ad.get_cvr(),
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(), ad.get_cvr_cmd_id());
                } else {
                  if (session_data->get_is_search_request()) {
                    ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                    ad.SetUnifyCvr(ad.get_cvr(),
                                   kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                                   ad.get_cvr_cmd_id());
                  } else {
                    ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
                    ad.SetUnifyCvr(1.0,
                        ad.get_ocpx_action_type(), ad.get_ocpx_action_type());
                  }
                }
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_cvr(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(), ad.get_cvr_cmd_id());
            }
            if (ad.Is(AdFlag::is_self_service_ad)) {
                if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                }
                ad.SetUnifyCvr(ad.get_consult_ctr(),
                                kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_BUTTON_CLICK_CONSULT,
                                ad.get_consult_ctr_cmd_id());
            }
        break;
        case kuaishou::ad::AD_CONVERSION:
            if (is_single_col) {
                // 单列 （历史原因，会有兜底策略，分别塞不同的率）
                if ((ad.get_server_show_ctr() > 0.0 && ad.get_app_conversion_rate() > 0.0)) {
                    ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                    ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                                   kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                                   ad.get_app_conversion_rate_cmd_id());
                    // SDPA 的激活预估值走 ensemble
                    if (ad.Is(AdFlag::is_sdpa_ad) && enable_sdpa_ensemble_conversion && !ad.Is(AdFlag::is_sdpa_ecom_ad)) {  // NOLINT
                        // 小说正采样客户预估校准
                        double sdpa_conv_pred =
                            ad.get_predict_score(PredictType::PredictType_app_conversion_rate_sdpa_ensemble);
                        if (ad.get_industry_id_v3() == 2002 && enable_sdpa_novel_calibration &&
                            novel_pos_sample_rate > 0.0 && novel_pos_sample_rate < 1.0) {
                            sdpa_conv_pred *=
                            1.0 / (novel_pos_sample_rate + (1 - novel_pos_sample_rate) * sdpa_conv_pred);
                        }
                        ad.SetUnifyCvr(
                        sdpa_ensemble_conv_alpha *
                        ad.get_predict_score(PredictType::PredictType_app_conversion_rate) +
                        sdpa_ensemble_conv_beta * sdpa_conv_pred,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                        ad.get_predict_cmd_id(PredictType::PredictType_app_conversion_rate),
                        ad.get_predict_cmd_id(PredictType::PredictType_app_conversion_rate_sdpa_ensemble));
                    }
                    // SDPA 的电商激活预估值走 ensemble
                    if (ad.Is(AdFlag::is_sdpa_ecom_ad)) {
                        if (enable_sdpa_ecom_conv_main_pred) {
                          ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                                        ad.get_app_conversion_rate_cmd_id());
                        } else {
                          ad.SetUnifyCvr(
                            sdpa_ensemble_ecom_conv_alpha *
                            ad.get_predict_score(PredictType::PredictType_app_conversion_rate) +
                            sdpa_ensemble_ecom_conv_beta *
                            ad.get_predict_score(PredictType::PredictType_app_conversion_rate_sdpa_ensemble),
                            kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                            ad.get_predict_cmd_id(
                                PredictType::PredictType_app_conversion_rate),
                            ad.get_predict_cmd_id(
                                PredictType::PredictType_app_conversion_rate_sdpa_ensemble));
                        }
                    }
                } else {
                    ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                    ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                        ad.get_app_conversion_rate_cmd_id());
                    // SDPA 的激活预估值走 ensemble
                    if (ad.Is(AdFlag::is_sdpa_ad) && enable_sdpa_ensemble_conversion && !ad.Is(AdFlag::is_sdpa_ecom_ad)) {  // NOLINT
                        // 小说正采样客户预估校准
                        double sdpa_conv_pred =
                            ad.get_predict_score(PredictType::PredictType_app_conversion_rate_sdpa_ensemble);
                        if (ad.get_industry_id_v3() == 2002 && enable_sdpa_novel_calibration &&
                            novel_pos_sample_rate > 0.0 && novel_pos_sample_rate < 1.0) {
                            sdpa_conv_pred *=
                            1.0 / (novel_pos_sample_rate + (1 - novel_pos_sample_rate) * sdpa_conv_pred);
                        }
                        ad.SetUnifyCvr(
                        sdpa_ensemble_conv_alpha *
                        ad.get_predict_score(PredictType::PredictType_app_conversion_rate) +
                        sdpa_ensemble_conv_beta * sdpa_conv_pred,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                        ad.get_predict_cmd_id(PredictType::PredictType_app_conversion_rate),
                        ad.get_predict_cmd_id(PredictType::PredictType_app_conversion_rate_sdpa_ensemble));
                    }
                    // SDPA 的电商激活预估值走 ensemble
                    if (ad.Is(AdFlag::is_sdpa_ecom_ad)) {
                      if (enable_sdpa_ecom_conv_main_pred) {
                        ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                        ad.get_app_conversion_rate_cmd_id());
                      } else {
                        ad.SetUnifyCvr(
                        sdpa_ensemble_ecom_conv_alpha *
                        ad.get_predict_score(PredictType::PredictType_app_conversion_rate) +
                        sdpa_ensemble_ecom_conv_beta *
                        ad.get_predict_score(PredictType::PredictType_app_conversion_rate_sdpa_ensemble),
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                        ad.get_predict_cmd_id(
                            PredictType::PredictType_app_conversion_rate),
                        ad.get_predict_cmd_id(
                            PredictType::PredictType_app_conversion_rate_sdpa_ensemble));
                      }
                    }
                    //  break;
                }
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_app_conversion_rate_cmd_id());
            }
            // 搜索广告盒子调用信息流激活模型
            if (session_data->get_is_search_request() &&
                session_data->get_rank_request()->ad_request().search_info().request_source() == 2 &&
                session_data->get_pos_manager_base().IsSearchInspireThanosRequest()) {
                  ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_app_conversion_rate),
                    kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                    ad.get_predict_cmd_id(PredictType::PredictType_app_conversion_rate));
            }
        break;
        case kuaishou::ad::EVENT_APP_INVOKED:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            // SDPA 的唤端预估值走 ensemble
            if (ad.Is(AdFlag::is_sdpa_ad) && enable_sdpa_ensemble_invoked) {
                ad.SetUnifyCvr(
                sdpa_ensemble_invoked_alpha *
                ad.get_predict_score(PredictType::PredictType_click_app_invoked) +
                sdpa_ensemble_invoked_beta *
                ad.get_predict_score(PredictType::PredictType_click_app_invoked_sdpa_ensemble),
                kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked),
                ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked_sdpa_ensemble));
            } else {
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click_app_invoked),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked));
            }
        break;
        case kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED:
            if (is_single_col) {
                if ((ad.get_server_show_ctr() > 0.0 && ad.get_landingpage_submit_rate() > 0.0) ||
                   enable_ensemble_imp_lps_fix_) {
                    // 单列 （历史原因，会有兜底策略，分别塞不同的率）
                    ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                    if (ad.Is(AdFlag::is_jiaotong_ad) && enable_car_ensemble_imp_lps_) {
                        ad.SetUnifyCvr(car_ensemble_imp_lps_alpha *
                                    ad.get_landingpage_submit_rate()+
                                    car_ensemble_imp_lps_beta *
                                    ad.get_predict_score(PredictType::PredictType_car_ensemble_imp_lps),
                                   kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                                   ad.get_landingpage_submit_rate_cmd_id(),
                                   ad.get_predict_cmd_id(PredictType::PredictType_car_ensemble_imp_lps));
                    } else if (ad.Is(AdFlag::is_finance_ad) && enable_finance_lps_ensemble) {
                        ad.SetUnifyCvr(finance_ensemble_imp_lps_alpha *
                                    ad.get_landingpage_submit_rate() +
                                    finance_ensemble_imp_lps_beta *
                                    ad.get_predict_score(PredictType::PredictType_finance_ensemble_imp_lps),
                                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                                    ad.get_landingpage_submit_rate_cmd_id(),
                                    ad.get_predict_cmd_id(PredictType::PredictType_finance_ensemble_imp_lps));
                    } else if (enable_edu_ensemble_imp_lps_ && ad.Is(AdFlag::is_education_ad) &&
                               ad.get_predict_score(PredictType::PredictType_edu_ensemble_imp_lps) > 0.0 &&
                               (!enable_edu_ensemble_imp_lps_block_corp ||
                                edu_lps_ensemble_black_corp == nullptr ||
                                edu_lps_ensemble_black_corp->find(corporation_name) == edu_lps_ensemble_black_corp->end())) {  // NOLINT
                        ad.SetUnifyCvr(edu_ensemble_imp_lps_alpha *
                                    ad.get_landingpage_submit_rate() +
                                    edu_ensemble_imp_lps_beta *
                                    ad.get_predict_score(PredictType::PredictType_edu_ensemble_imp_lps),
                                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                                    ad.get_landingpage_submit_rate_cmd_id(),
                                    ad.get_predict_cmd_id(PredictType::PredictType_edu_ensemble_imp_lps));
                    } else {
                        ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                                   kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                                   ad.get_landingpage_submit_rate_cmd_id());
                    }
                } else {
                    ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                    ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                        ad.get_landingpage_submit_rate_cmd_id());
                }
                if (enable_maintower_ensemble_imp_lps_) {
                    ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                        ad.get_landingpage_submit_rate_cmd_id());
                }
            } else if (session_data->get_pos_manager_base().IsInspireMerchant()) {
                // 激励电商广告暂时不考虑 server_client_show_rate
                ad.SetUnifyCtr(ad.get_ctr(),
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_landingpage_submit_rate_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_landingpage_submit_rate_cmd_id());
            }
        break;
        case kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION:
          if (is_single_col) {
              ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                  kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
          } else {
              ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                  kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                  ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
          }
          ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
              kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
              ad.get_landingpage_submit_rate_cmd_id());
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_lps_acquisition),
              kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
              kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION,
              ad.get_predict_cmd_id(PredictType::PredictType_lps_acquisition));
          break;
        case kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT:
        case kuaishou::ad::LEADS_SUBMIT:
          if (is_single_col) {
            ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
          } else {
            ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                           kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                           ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
          }
          ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                         kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                         ad.get_landingpage_submit_rate_cmd_id());
          break;
        case kuaishou::ad::EVENT_APPOINT_FORM:
        case kuaishou::ad::EVENT_APPOINT_JUMP_CLICK:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_c2_game_appoint_rate),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_predict_cmd_id(PredictType::PredictType_c2_game_appoint_rate));
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_game_appoint_rate),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_predict_cmd_id(PredictType::PredictType_game_appoint_rate));
            }
        break;
        case kuaishou::ad::EVENT_REGISTER:
        case kuaishou::ad::EVENT_ORDER_SUBMIT:
          if (is_single_col) {
            if (ad.get_ali_outer_bid_type() == AliOuterBidType::ALI_UNION) {
              ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
              ad.SetUnifyCvr(click2_deep_rate,
                            kuaishou::ad::AD_ITEM_CLICK,
                            ad.get_ocpx_action_type(), click2_deep_rate_cmd_id);
            } else {
              ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
              ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                            kuaishou::ad::AD_ITEM_IMPRESSION,
                            ad.get_ocpx_action_type(),
                            ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            }
          } else {
            ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                        ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                          kuaishou::ad::AD_ITEM_IMPRESSION,
                          ad.get_ocpx_action_type(),
                          ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
          }
        break;
        case kuaishou::ad::AD_CID_ROAS:
          if (is_single_col) {
            ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                           kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            ad.SetUnifyCvr(cid_roas_reset_cvr,
                        kuaishou::ad::EVENT_ORDER_SUBMIT,
                        kuaishou::ad::EVENT_ORDER_SUBMIT,
                        ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            ad.SetUnifyLtv(ad.get_merchant_ltv(), kuaishou::ad::AD_ITEM_IMPRESSION,
                              kuaishou::ad::AD_CID_ROAS, ad.get_merchant_ltv_cmd_id());
          }
          break;
        case kuaishou::ad::AD_PURCHASE:
        case kuaishou::ad::EVENT_PAY_UNION:
        case kuaishou::ad::AD_MERCHANT_ROAS:
        case kuaishou::ad::AD_MERCHANT_T7_ROI:
        case kuaishou::ad::AD_STOREWIDE_ROAS:
        case kuaishou::ad::AD_FANS_TOP_ROI:
        case kuaishou::ad::CID_ROAS:
            if (ad.Is(AdFlag::is_reco_roas) ||
                ad.get_ocpx_action_type() == kuaishou::ad::AD_FANS_TOP_ROI) {
                ad.SetUnifyLtv(ad.get_merchant_ltv(), kuaishou::ad::EVENT_ORDER_PAIED,
                                 kuaishou::ad::AD_MERCHANT_ROAS, ad.get_merchant_ltv_cmd_id());
                if (!session_data->get_is_search_request()
                        && !session_data->get_is_splash_request()) {
                    double roi_post_ltv =  ad.get_payment_per_order()/1000.0;  // 换算成元
                    if (roi_post_ltv > 0) {
                        ad.SetUnifyLtv(roi_post_ltv, kuaishou::ad::EVENT_ORDER_PAIED,
                                 kuaishou::ad::AD_MERCHANT_ROAS, ad.get_merchant_ltv_cmd_id());
                    }
                }
                if (is_single_col) {
                  ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                  if (((session_data->get_spdm_ctx().TryGetBoolean(
                            "enable_neixunhuan_photo_roi_cvr_reset", false) ||
                                 (session_data->get_is_search_request() && enable_search_roas_one_stage)) &&
                         !session_data->get_pos_manager_base().IsInspireMerchant())) {
                    double c1_order_paied_rate = 1.0;
                    // 主站精选页
                    if (session_data->get_sub_page_id() == 10011001) {
                        auto iter = roas_jingxuan_cali_map->find(ad.get_author_id());
                        if (iter != roas_jingxuan_cali_map->end()) {
                            c1_order_paied_rate = iter->second;
                        }
                    }
                    ad.SetUnifyCvr(session_data->get_neixunhuan_photo_roi_cvr_reset_value() *
                                c1_order_paied_rate,
                                kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type());
                  } else {
                    double c1_order_paied_rate = 1.0;
                    if (session_data->get_pos_manager_base().IsInspireMerchant()) {
                      c1_order_paied_rate = 0.3;
                    }
                    ad.SetUnifyCvr(ad.get_c1_order_paied() * c1_order_paied_rate,
                                kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::EVENT_ORDER_PAIED,
                                ad.get_c1_order_paied_cmd_id());
                  }
                } else if (session_data->get_pos_manager_base().IsInspireMerchant()) {
                  // 激励电商广告暂时不考虑 server_client_show_rate
                  ad.SetUnifyCtr(ad.get_ctr(),
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ctr_cmd_id());
                  ad.SetUnifyCvr(ad.get_c1_order_paied(),
                              kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::EVENT_ORDER_PAIED,
                              ad.get_c1_order_paied_cmd_id());
                } else {
                  if (enable_drop_high_ctr_traffic && ad.get_ctr() > high_ctr_threshold) {
                    ad.SetUnifyCtr(0.0,
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                            ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                  } else {
                    ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                            ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                  }
                  // 搜索商品卡
                  if (session_data->get_is_search_request() && session_data->get_is_search_good_card()) {
                    ad.SetUnifyCtr(ad.get_ctr(),
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                        ad.get_ctr_cmd_id());
                    ad.SetUnifyCvr(session_data->get_neixunhuan_photo_roi_cvr_reset_value(),
                        kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type());
                         } else if (session_data->get_spdm_ctx().TryGetBoolean(
                                        "enable_neixunhuan_photo_roi_cvr_reset", false) ||
                         (session_data->get_is_search_request() && enable_search_roas_one_stage)) {
                    ad.SetUnifyCvr(session_data->get_neixunhuan_photo_roi_cvr_reset_value(),
                                kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type());
                  } else {
                    ad.SetUnifyCvr(ad.get_c1_order_paied(),
                                kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::EVENT_ORDER_PAIED,
                                ad.get_c1_order_paied_cmd_id());
                  }
                }
            } else if (ad.Is(AdFlag::is_jinniu_roas)) {
                ad.SetUnifyLtv(ad.get_merchant_ltv(), kuaishou::ad::EVENT_ORDER_PAIED,
                         kuaishou::ad::AD_MERCHANT_ROAS, ad.get_merchant_ltv_cmd_id());
                if (is_single_col) {
                  ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                  if (((session_data->get_spdm_ctx().TryGetBoolean(
                            "enable_neixunhuan_photo_roi_cvr_reset", false) ||
                             (session_data->get_is_search_request() && enable_search_roas_one_stage)) &&
                         !session_data->get_pos_manager_base().IsInspireMerchant())) {
                    ad.SetUnifyCvr(session_data->get_neixunhuan_photo_roi_cvr_reset_value(),
                                kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type());
                  } else {
                    ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                                kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                                ad.get_landingpage_submit_rate_cmd_id());
                  }
                } else if (session_data->get_pos_manager_base().IsInspireMerchant()) {
                  // 激励电商广告暂时不考虑 server_client_show_rate
                  ad.SetUnifyCtr(ad.get_ctr(),
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ctr_cmd_id());
                  ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                              kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                              ad.get_landingpage_submit_rate_cmd_id());
                } else {
                  ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                        ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                  // 搜索商品卡
                  if (session_data->get_is_search_request() &&
                             session_data->get_is_search_good_card()) {
                    ad.SetUnifyCtr(ad.get_ctr(),
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                        ad.get_ctr_cmd_id());
                    ad.SetUnifyCvr(session_data->get_neixunhuan_photo_roi_cvr_reset_value(),
                                kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type());
                    } else if (session_data->get_spdm_ctx().TryGetBoolean(
                                  "enable_neixunhuan_photo_roi_cvr_reset", false) ||
                             (session_data->get_is_search_request() && enable_search_roas_one_stage)) {
                    ad.SetUnifyCvr(session_data->get_neixunhuan_photo_roi_cvr_reset_value(),
                                kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type());
                  } else {
                    ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                                kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                                ad.get_landingpage_submit_rate_cmd_id());
                  }
                }
            } else {
                if (ad.get_second_industry_id_v5() == 2012 && !session_data->get_is_search_request() &&
                    (!disable_industry_clk2purchase || session_data->get_client_cpm_enable_switch()) &&
                    (enable_ad_rank_clk2purchase_predict || enable_short_play_clk2pay_account) &&
                    !disable_industry_clk2purchase_hard) {
                    if (enable_industry_clk2purchase_mix) {
                      if (enable_ad_rank_industry_server_show_cvr_predict) {
                        ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_industry_clk2_pay) *
                            ad.get_predict_score(PredictType::PredictType_industry_server_show_cvr) *
                            industry_clk2purchase_ratio +
                            ad.get_predict_score(PredictType::PredictType_industry_clk2_pay_other) *
                            ad.get_predict_score(PredictType::PredictType_industry_server_show_cvr_other) *
                            industry_clk2purchase_other_ratio,
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_PURCHASE,
                            ad.get_predict_cmd_id(PredictType::PredictType_industry_clk2_pay));
                      }
                      if (is_single_col) {
                          ad.SetUnifyCtr(ad.get_server_show_ctr(),
                              kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                              ad.get_server_show_ctr_cmd_id());
                      } else {
                          ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                              kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                              ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                      }
                    } else {
                      ad.SetUnifyCvr(ad.get_predict_score(
                          PredictType::PredictType_industry_clk2_pay),
                          kuaishou::ad::AD_ITEM_CLICK, kuaishou::ad::AD_PURCHASE,
                          ad.get_predict_cmd_id(PredictType::PredictType_industry_clk2_pay));
                      if (is_single_col) {
                          if (enable_ad_rank_industry_server_show_cvr_predict &&
                              session_data->get_client_cpm_enable_switch()) {
                            ad.SetUnifyCtr(
                                ad.get_predict_score(PredictType::PredictType_industry_server_show_cvr),
                                kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                                ad.get_predict_cmd_id(PredictType::PredictType_industry_server_show_cvr));
                          } else {
                            ad.SetUnifyCtr(ad.get_server_show_cvr(),
                                kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                                ad.get_server_show_cvr_cmd_id());
                          }
                      } else {
                          if (enable_ad_rank_industry_server_show_cvr_predict &&
                              session_data->get_client_cpm_enable_switch()) {
                            ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate *
                                ad.get_predict_score(PredictType::PredictType_industry_server_show_cvr),
                                kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                                ad.get_predict_cmd_id(PredictType::PredictType_industry_server_show_cvr));
                          } else {
                            ad.SetUnifyCtr(
                                ad.get_ctr() * origin_server_client_show_rate * ad.get_server_show_cvr(),
                                kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                                ad.get_ctr_cmd_id(),
                                server_client_show_rate_cmd_id);
                          }
                      }
                    }
                } else if ((ad.get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict) ||
                        enable_mini_app_roas_invoked_product || enable_mini_app_roas_invoked_account) {
                    if (ad.get_industry_id_v3() == 2012 && enable_ad_rank_industry_purchase_predict) {
                      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_industry_invoked_pay),
                              kuaishou::ad::EVENT_APP_INVOKED,
                              kuaishou::ad::AD_PURCHASE,
                              ad.get_predict_cmd_id(PredictType::PredictType_industry_invoked_pay));
                    } else {
                      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                              kuaishou::ad::EVENT_APP_INVOKED,
                              kuaishou::ad::AD_PURCHASE,
                              ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                    }
                    if (is_single_col) {
                        ad.SetUnifyCtr(ad.get_server_show_ctr()
                            * ad.get_predict_score(PredictType::PredictType_click_app_invoked),
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::EVENT_APP_INVOKED,
                            ad.get_server_show_ctr_cmd_id(),
                            ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked));
                    } else {
                        ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate
                                * ad.get_predict_score(PredictType::PredictType_click_app_invoked),
                                kuaishou::ad::AD_DELIVERY, kuaishou::ad::EVENT_APP_INVOKED,
                                ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                    }
                    // 搜索切分点对齐到 item imp 如有问题请联系 @zhouxuan06
                    if (session_data->get_is_search_request()) {
                      ad.SetUnifyCvr(
                          ad.get_predict_score(PredictType::PredictType_purchase) *
                          ad.get_predict_score(PredictType::PredictType_click_app_invoked),
                          kuaishou::ad::AD_ITEM_IMPRESSION,
                          kuaishou::ad::AD_PURCHASE,
                          ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked),
                          ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                      if (is_single_col) {
                        ad.SetUnifyCtr(ad.get_server_show_ctr(),
                                       kuaishou::ad::AD_DELIVERY,
                                       kuaishou::ad::AD_ITEM_IMPRESSION,
                                       ad.get_server_show_ctr_cmd_id());
                      } else {
                        ad.SetUnifyCtr(
                            ad.get_ctr() * origin_server_client_show_rate,
                            kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ctr_cmd_id(),
                            server_client_show_rate_cmd_id);
                      }
                    }
                } else if (enable_fiction_app_conversion_purchase_product ||
                    enable_fiction_app_conversion_purchase_account) {
                    if (is_single_col) {
                        ad.SetUnifyCtr(ad.get_server_show_ctr() * ad.get_app_conversion_rate(),
                                       kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_CONVERSION,
                                       ad.get_server_show_ctr_cmd_id(), ad.get_app_conversion_rate_cmd_id());
                    } else {
                        ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate
                                       * ad.get_app_conversion_rate(),
                                       kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_CONVERSION,
                                       ad.get_server_show_ctr_cmd_id(), ad.get_app_conversion_rate_cmd_id());
                    }
                    ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                                kuaishou::ad::AD_CONVERSION,
                                kuaishou::ad::AD_PURCHASE,
                                ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                    // 搜索切分点对齐到 item imp 如有问题请联系 @zhouxuan06
                    if (session_data->get_is_search_request()) {
                      if (is_single_col) {
                        ad.SetUnifyCtr(ad.get_server_show_ctr(),
                                       kuaishou::ad::AD_DELIVERY,
                                       kuaishou::ad::AD_ITEM_IMPRESSION,
                                       ad.get_server_show_ctr_cmd_id());
                      } else {
                        ad.SetUnifyCtr(ad.get_ctr() *
                                           origin_server_client_show_rate,
                                       kuaishou::ad::AD_DELIVERY,
                                       kuaishou::ad::AD_ITEM_IMPRESSION,
                                       ad.get_server_show_ctr_cmd_id(),
                                       server_client_show_rate_cmd_id);
                      }
                      ad.SetUnifyCvr(
                          ad.get_app_conversion_rate() *
                          ad.get_predict_score(PredictType::PredictType_purchase),
                          kuaishou::ad::AD_ITEM_IMPRESSION,
                          kuaishou::ad::AD_PURCHASE,
                          ad.get_app_conversion_rate_cmd_id(),
                          ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                    }
                } else if (is_single_col) {
                    if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                           p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP)) {
                        if (enable_search_pay_split_to_impression && session_data->get_is_search_request()) {
                          ad.SetUnifyCtr(ad.get_server_show_ctr(),
                                       kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_CONVERSION,
                                       ad.get_server_show_ctr_cmd_id(), ad.get_app_conversion_rate_cmd_id());
                        } else {
                          ad.SetUnifyCtr(ad.get_server_show_ctr() * ad.get_app_conversion_rate(),
                                       kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_CONVERSION,
                                       ad.get_server_show_ctr_cmd_id(), ad.get_app_conversion_rate_cmd_id());
                        }
                        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP &&
                            enable_deep_middle_model_exp && enable_deep_middle_model_convpay_exp &&
                            ad.get_deep_conversion_type() !=
                                kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY) {
                          ad.SetUnifyCvr((1 - deep_middle_model_ensemble_rate) *
                              ad.get_predict_score(PredictType::PredictType_purchase) +
                              deep_middle_model_ensemble_rate * ad.get_deep_middle_model_predict_rate(),
                              kuaishou::ad::AD_CONVERSION,
                              kuaishou::ad::AD_PURCHASE,
                              ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                        } else {
                            if (enable_search_pay_split_to_impression
                                && session_data->get_is_search_request()) {
                              ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                                kuaishou::ad::AD_ITEM_IMPRESSION,
                                kuaishou::ad::AD_PURCHASE,
                                ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                            } else {
                              ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                                kuaishou::ad::AD_CONVERSION,
                                kuaishou::ad::AD_PURCHASE,
                                ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                            }
                        }
                    } else if ((enable_purchase_cut_by_app_invoked_product ||
                                enable_purchase_cut_by_app_invoked_account) &&
                               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                               p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE) {
                        ad.SetUnifyCtr(ad.get_server_show_ctr() *
                            ad.get_predict_score(PredictType::PredictType_click_app_invoked),
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::EVENT_APP_INVOKED,
                            ad.get_server_show_ctr_cmd_id(),
                            ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked));
                        ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                                    kuaishou::ad::EVENT_APP_INVOKED,
                                    kuaishou::ad::AD_PURCHASE,
                                    ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                    } else {
                        // 表单付费曝光切分点实验
                        if (enable_lpsep_cut_imp &&
                            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE ||
                            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LANDING_PAGE ||
                            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::TAOBAO ||
                            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE)) {
                        ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                        ad.SetUnifyCvr(ad.get_predict_score(
                                    PredictType::PredictType_click2_purchase_rate_single_bid),
                                    kuaishou::ad::AD_ITEM_IMPRESSION,
                                    ad.get_ocpx_action_type(),
                                    ad.get_predict_cmd_id(
                                        PredictType::PredictType_click2_purchase_rate_single_bid));
                        } else {
                        ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
                                    kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
                        ad.SetUnifyCvr(ad.get_predict_score(
                                    PredictType::PredictType_click2_purchase_rate_single_bid),
                                    kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
                                    ad.get_predict_cmd_id(
                                        PredictType::PredictType_click2_purchase_rate_single_bid));
                        }
                    }
                } else {
                    ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                        ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                    ad.SetUnifyCvr(
                        ad.get_predict_score(PredictType::PredictType_click_purchase_rate_single_bid),  // NOLINT
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                        ad.get_predict_cmd_id(PredictType::PredictType_click_purchase_rate_single_bid));  // NOLINT
                    // 搜索付费单出价切换计费点
                    if (session_data->get_is_search_request() &&
                        p_ad->get_ocpx_action_type() ==
                            kuaishou::ad::AD_PURCHASE) {
                      // 激活类付费
                      if (p_ad->get_campaign_type() ==
                          kuaishou::ad::AdEnum::APP) {
                        ad.SetUnifyCtr(
                            ad.get_ctr() * origin_server_client_show_rate,
                            kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_ITEM_IMPRESSION,
                            ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                        ad.SetUnifyCvr(
                            ad.get_predict_score(PredictType::PredictType_purchase) *
                                ad.get_app_conversion_rate(),
                            kuaishou::ad::AD_ITEM_IMPRESSION,
                            kuaishou::ad::AD_PURCHASE,
                            ad.get_app_conversion_rate_cmd_id(),
                            ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                      } else if (p_ad->get_campaign_type() ==
                                 kuaishou::ad::AdEnum::APP_ADVANCE) {
                        // 唤端类付费
                        ad.SetUnifyCtr(
                            ad.get_ctr() * origin_server_client_show_rate,
                            kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_ITEM_IMPRESSION,
                            ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                        ad.SetUnifyCvr(
                            ad.get_predict_score(PredictType::PredictType_purchase) *
                                ad.get_predict_score(
                                    PredictType::PredictType_click_app_invoked),
                            kuaishou::ad::AD_ITEM_IMPRESSION,
                            kuaishou::ad::AD_PURCHASE,
                            ad.get_predict_cmd_id(
                                PredictType::PredictType_click_app_invoked),
                            ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                      } else {
                        // lps 类付费
                        ad.SetUnifyCtr(
                            ad.get_ctr() * origin_server_client_show_rate,
                            kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_ITEM_IMPRESSION,
                            ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                        ad.SetUnifyCvr(
                            ad.get_predict_score(
                                PredictType::PredictType_click2_purchase_rate_single_bid) *
                                ad.get_cvr(),
                            kuaishou::ad::AD_ITEM_IMPRESSION,
                            ad.get_ocpx_action_type(), ad.get_cvr_cmd_id(),
                            ad.get_predict_cmd_id(
                                PredictType::PredictType_click2_purchase_rate_single_bid));
                      }
                    } else if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE
                            && p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP)  {
                        ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate
                                       * ad.get_app_conversion_rate(),
                                       kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_CONVERSION,
                                       ad.get_server_show_ctr_cmd_id(), ad.get_app_conversion_rate_cmd_id());
                        ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                                        kuaishou::ad::AD_CONVERSION,
                                        kuaishou::ad::AD_PURCHASE,
                                        ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                        // 搜索切分点对齐到 item imp 如有问题请联系 @zhouxuan06
                        if (session_data->get_is_search_request()) {
                          ad.SetUnifyCtr(
                              ad.get_ctr() * origin_server_client_show_rate,
                              kuaishou::ad::AD_DELIVERY,
                              kuaishou::ad::AD_ITEM_IMPRESSION,
                              ad.get_server_show_ctr_cmd_id(),
                              server_client_show_rate_cmd_id);
                          if (enable_search_pay_split_to_impression) {
                            ad.SetUnifyCvr(
                              ad.get_predict_score(PredictType::PredictType_purchase),
                              kuaishou::ad::AD_ITEM_IMPRESSION,
                              kuaishou::ad::AD_PURCHASE,
                              ad.get_predict_cmd_id(PredictType::PredictType_purchase),
                              ad.get_app_conversion_rate_cmd_id());
                          } else {
                            ad.SetUnifyCvr(
                              ad.get_predict_score(PredictType::PredictType_purchase) *
                                  ad.get_app_conversion_rate(),
                              kuaishou::ad::AD_ITEM_IMPRESSION,
                              kuaishou::ad::AD_PURCHASE,
                              ad.get_predict_cmd_id(PredictType::PredictType_purchase),
                              ad.get_app_conversion_rate_cmd_id());
                          }
                        }
                    } else if ((enable_purchase_cut_by_app_invoked_product ||
                                enable_purchase_cut_by_app_invoked_account) &&
                               p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                               p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE) {
                        ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate
                            * ad.get_predict_score(PredictType::PredictType_click_app_invoked),
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::EVENT_APP_INVOKED,
                            ad.get_server_show_ctr_cmd_id(),
                            ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked));
                        ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                                    kuaishou::ad::EVENT_APP_INVOKED,
                                    kuaishou::ad::AD_PURCHASE,
                                    ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                        // 搜索切分点对齐到 item imp 如有问题请联系 @zhouxuan06
                        if (session_data->get_is_search_request()) {
                          ad.SetUnifyCtr(
                              ad.get_ctr() * origin_server_client_show_rate,
                              kuaishou::ad::AD_DELIVERY,
                              kuaishou::ad::AD_ITEM_IMPRESSION,
                              ad.get_server_show_ctr_cmd_id(),
                              server_client_show_rate_cmd_id);
                          ad.SetUnifyCvr(
                              ad.get_predict_score(PredictType::PredictType_purchase) *
                                  ad.get_predict_score(
                                      PredictType::PredictType_click_app_invoked),
                              kuaishou::ad::AD_ITEM_IMPRESSION,
                              kuaishou::ad::AD_PURCHASE,
                              ad.get_predict_cmd_id(
                                  PredictType::PredictType_click_app_invoked),
                              ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                        }
                    }
                }
                // 付费 roi 双出价在小程序、小说上，继承上面付费单出价的切分点
                bool use_purchase_result = enable_mini_app_roas_invoked_product ||
                    enable_mini_app_roas_invoked_account ||
                    enable_fiction_app_conversion_purchase_product ||
                    enable_fiction_app_conversion_purchase_account;
                // 付费 roi 双出价切换链路
                if (ad.get_deep_conversion_type() ==
                    kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
                    !use_purchase_result) {
                    if (is_single_col) {
                        ad.SetUnifyCtr(ad.get_server_show_ctr(),
                                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                                        ad.get_server_show_ctr_cmd_id());
                    } else {
                        ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                                        ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                    }
                    if (ad.get_is_purchase_pay_test()) {
                        ad.SetUnifyCvr(ad.get_app_conversion_rate() *
                                       ad.get_predict_score(PredictType::PredictType_purchase),
                                        kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_PURCHASE,
                                        ad.get_app_conversion_rate_cmd_id(),
                                        ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                    } else {
                        ad.SetUnifyCvr(ad.get_app_conversion_rate(), kuaishou::ad::AD_ITEM_IMPRESSION,
                                        kuaishou::ad::AD_CONVERSION, ad.get_app_conversion_rate_cmd_id());
                    }
                }
            }

            // 网服付费模型 ensemble （只包含激活付费和唤端付费）
            if (((ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
                (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE ||
                p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP) &&
                enable_wangfu_purchase_cmd &&
                enable_wangfu_purchase_ensemble &&
                RankKconfUtil::WangfuIndustryList() &&
                RankKconfUtil::WangfuIndustryList()->find(absl::StrCat(p_ad->get_industry_id_v3())) !=
                RankKconfUtil::WangfuIndustryList()->end()))) {
                // 非激活付费和唤端付费 ensemble 权重为 0
                if ((ad.get_unify_cvr_info().s_type != kuaishou::ad::AD_CONVERSION &&
                    ad.get_unify_cvr_info().s_type != kuaishou::ad::EVENT_APP_INVOKED)) {
                    wangfu_purchase_ensemble_alpha = 0.0;
                }

                double ori_cvr = ad.get_unify_cvr_info().value;
                double wangfu_cvr = ad.get_predict_score(PredictType::PredictType_wangfu_purchase);
                double new_cvr = ori_cvr;
                if (wangfu_cvr > 0) {
                    new_cvr = (1.0 - wangfu_purchase_ensemble_alpha) * ori_cvr +
                                    wangfu_purchase_ensemble_alpha * wangfu_cvr;
                }
                ad.AdjustUnifyCvrValue(new_cvr);

                std::string modelstart = absl::StrCat(ad.get_unify_cvr_info().s_type);
                std::string modelend = absl::StrCat(ad.get_unify_cvr_info().e_type);

                std::string mon_key = absl::StrCat(p_ad->get_industry_id_v3());
                RANK_DOT_STATS(session_data, static_cast<int64_t>(100 * wangfu_cvr),
                                "ad_rank.wangfu_purchase_rate",
                                modelstart + "_" + modelend,
                                mon_key);
                RANK_DOT_COUNT(session_data, 1,
                                "ad_rank.wangfu_purchase_rate_count",
                                modelstart + "_" + modelend,
                                mon_key);
                ad.set_wangfu_purchase_ensemble_is(true);
                ad.set_wangfu_purchase_ensemble_value(new_cvr);
            }
            if (is_live) {
                if (ad.Is(AdFlag::is_amd_direct_live)) {  // 直投
                    double ctr = ad.get_server_show_ctr() * ad.get_live_audience();
                    ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                                kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                                ad.get_server_show_ctr_cmd_id(),
                                ad.get_live_audience_cmd_id());
                } else {  // 作品引流
                  double ctr = 0.0;
                  if (!is_single_col) {  // 双列
                    ctr = origin_server_client_show_rate * ad.get_ctr() * ad.get_cvr();
                    ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                                   kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                                   server_client_show_rate_cmd_id, ad.get_ctr_cmd_id());
                  } else {  // 单列 作品引流
                    ctr = ad.get_server_show_ctr() * ad.get_cvr();
                    ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                                   kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                                   ad.get_server_show_ctr_cmd_id(), ad.get_cvr_cmd_id());
                  }
                }
                {
                  ad.SetUnifyLtv(ad.get_live_p3s_ltv(), kuaishou::ad::AD_ITEM_IMPRESSION,
                                 kuaishou::ad::AD_MERCHANT_ROAS);
                  if (!(session_data->get_is_search_request()
                      && params->enable_search_live_roas_filter)) {
                    double gmv = ad.get_inner_live_roas();
                    ad.SetUnifyLtv(gmv, kuaishou::ad::AD_ITEM_IMPRESSION,
                                    kuaishou::ad::AD_MERCHANT_ROAS);
                    ad.set_esplive_roas_tag(true);
                  }
                }
                ad.SetUnifyCvr(1.0,
                       kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                       kuaishou::ad::EVENT_ORDER_PAIED);
            }
        break;
        case kuaishou::ad::AD_ROAS:
          if (ad.get_second_industry_id_v5() == 2012 && !session_data->get_is_search_request() &&
              session_data->get_client_cpm_enable_switch() && !disable_industry_clk2purchase_hard &&
              (enable_ad_rank_clk2purchase_roas_predict || enable_short_play_clk2pay_roas_account)) {
              if (enable_request_industry_pay_ltv && ad.Is(AdFlag::is_paid_duanju_ad)) {
                double pay_ltv_score = (1 - industry_pay_ltv_ensemble_weight) *
                                            ad.get_predict_score(PredictType::PredictType_game_conv_ltv) +
                                       industry_pay_ltv_ensemble_weight *
                                            ad.get_predict_score(PredictType::PredictType_industry_pay_ltv);
                if (disable_request_industry_pay_ltv) {
                  pay_ltv_score = ad.get_predict_score(PredictType::PredictType_game_conv_ltv);
                }
                ad.SetUnifyLtv(pay_ltv_score, kuaishou::ad::AD_PURCHASE, kuaishou::ad::AD_ROAS);
              } else {
                ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                    kuaishou::ad::AD_PURCHASE, kuaishou::ad::AD_ROAS);
              }
              ad.SetUnifyDeepCvr(
                  ad.get_predict_score(PredictType::PredictType_industry_clk2_pay),
                  kuaishou::ad::AD_ITEM_CLICK, kuaishou::ad::AD_PURCHASE);
              ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_industry_clk2_pay),
                  kuaishou::ad::AD_ITEM_CLICK, kuaishou::ad::AD_PURCHASE,
                  ad.get_predict_cmd_id(PredictType::PredictType_industry_clk2_pay));
              if (is_single_col) {
                if (enable_ad_rank_industry_server_show_cvr_predict) {
                  ad.SetUnifyCtr(
                      ad.get_predict_score(PredictType::PredictType_industry_server_show_cvr),
                      kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                      ad.get_predict_cmd_id(PredictType::PredictType_industry_server_show_cvr));
                } else {
                  ad.SetUnifyCtr(ad.get_server_show_cvr(),
                      kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                      ad.get_server_show_cvr_cmd_id());
                }
              } else {
                if (enable_ad_rank_industry_server_show_cvr_predict) {
                  ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate *
                      ad.get_predict_score(PredictType::PredictType_industry_server_show_cvr),
                      kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                      ad.get_predict_cmd_id(PredictType::PredictType_industry_server_show_cvr));
                } else {
                  ad.SetUnifyCtr(
                      ad.get_ctr() * origin_server_client_show_rate * ad.get_server_show_cvr(),
                      kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                      ad.get_ctr_cmd_id(),
                      server_client_show_rate_cmd_id);
                }
              }
          } else if (!(enable_mini_app_roas_invoked_product || enable_mini_app_roas_invoked_account) &&
            !(ad.get_second_industry_id_v5() == 2012 && enable_playlet_inovked_predict)) {
            // 判断是否是测试唤端 ROI 白名单的链路
            {
                double pay_ltv_score = ad.get_predict_score(PredictType::PredictType_game_conv_ltv);
                double game_pay_ltv_score = pay_ltv_score;
                if (enable_request_game_industry_pay_ltv || enable_request_minigame_industry_pay_ltv) {
                    game_pay_ltv_score =
                        ad.get_predict_score(PredictType::PredictType_game_industry_pay_ltv);
                }
                bool is_legalvalue_game_industry_pay_ltv = true;
                double filter_lower_ratio = 0.2;
                double filter_upper_ratio = 5.0;
                if (enable_game_bagging_ltv) {
                    filter_upper_ratio = game_industry_ensemble_pv_filter_upper_ratio;
                    filter_lower_ratio = game_industry_ensemble_pv_filter_lower_ratio;
                }
                if (enable_game_industry_pay_ltv_out) {
                    is_legalvalue_game_industry_pay_ltv =
                        (game_pay_ltv_score < filter_upper_ratio * pay_ltv_score) &&
                        (game_pay_ltv_score > filter_lower_ratio * pay_ltv_score);
                }
                if (is_legalvalue_game_industry_pay_ltv) {
                    if (ad.Is(AdFlag::is_game_ad) && enable_request_game_industry_pay_ltv) {
                        pay_ltv_score = (1 - game_industry_pay_ltv_ensemble_weight) * pay_ltv_score +
                            game_industry_pay_ltv_ensemble_weight * game_pay_ltv_score;
                        pay_ltv_score = pay_ltv_score * game_industry_pay_ltv_reweight_weight;
                    }
                    if (ad.Is(AdFlag::is_wechat_game_ad) && enable_request_minigame_industry_pay_ltv) {
                        pay_ltv_score = (1 - minigame_industry_pay_ltv_ensemble_weight) * pay_ltv_score +
                            minigame_industry_pay_ltv_ensemble_weight * game_pay_ltv_score;
                        pay_ltv_score = pay_ltv_score * minigame_industry_pay_ltv_reweight_weight;
                    }
                }
                ad.set_game_bagging_ltv(pay_ltv_score);
                if (enable_mini_game_invoke_link) {
                  ad.SetUnifyLtv(pay_ltv_score, kuaishou::ad::EVENT_APP_INVOKED, kuaishou::ad::AD_ROAS);
                  ad.SetUnifyDeepCvr(pay_ltv_score, kuaishou::ad::EVENT_APP_INVOKED, kuaishou::ad::AD_ROAS);
                  if (is_single_col) {
                      ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                                     kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click_app_invoked),
                                     kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::EVENT_APP_INVOKED,
                                     ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked));
                  } else {
                      ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                                     kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                                     ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click_app_invoked),
                                     kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::EVENT_APP_INVOKED,
                                     ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked));
                  }
                } else {
                  ad.SetUnifyLtv(pay_ltv_score, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS);
                  ad.SetUnifyDeepCvr(pay_ltv_score, kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS);
                  if (is_single_col) {
                      ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                                          kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                      ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                                          ad.get_app_conversion_rate_cmd_id());
                  } else {
                      ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                                          ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                      ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                                          ad.get_app_conversion_rate_cmd_id());
                  }
                }
                if (ad.get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION
                    && session_data->get_is_search_request()) {
                    ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                        kuaishou::ad::AD_PURCHASE, kuaishou::ad::AD_ROAS);
                    ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click_app_invoked)
                                 * ad.get_predict_score(PredictType::PredictType_purchase),
                        kuaishou::ad::AD_ITEM_IMPRESSION,
                        kuaishou::ad::AD_PURCHASE,
                        ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                    ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                        kuaishou::ad::EVENT_APP_INVOKED, kuaishou::ad::AD_PURCHASE);
                }
            }
          } else {
            {
                if (enable_request_industry_pay_ltv && ad.Is(AdFlag::is_paid_duanju_ad)) {
                    double pay_ltv_score = (1 - industry_pay_ltv_ensemble_weight) *
                                                ad.get_predict_score(PredictType::PredictType_game_conv_ltv) +
                                        industry_pay_ltv_ensemble_weight *
                                            ad.get_predict_score(PredictType::PredictType_industry_pay_ltv);
                    if (disable_request_industry_pay_ltv) {
                      pay_ltv_score = ad.get_predict_score(PredictType::PredictType_game_conv_ltv);
                    }
                    ad.SetUnifyLtv(pay_ltv_score, kuaishou::ad::AD_PURCHASE, kuaishou::ad::AD_ROAS);
                } else {
                  ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                        kuaishou::ad::AD_PURCHASE, kuaishou::ad::AD_ROAS);
                }
                ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                        kuaishou::ad::EVENT_APP_INVOKED, kuaishou::ad::AD_PURCHASE);
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click_app_invoked)
                                * ad.get_predict_score(PredictType::PredictType_purchase),
                        kuaishou::ad::AD_ITEM_IMPRESSION,
                        kuaishou::ad::AD_PURCHASE,
                        ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                if (is_single_col) {
                    ad.SetUnifyCtr(ad.get_server_show_ctr(),
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                        ad.get_server_show_ctr_cmd_id(),
                        ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked));
                } else {
                    ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                            ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                }
                if (ad.get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION
                    && session_data->get_is_search_request()) {
                      ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                          kuaishou::ad::AD_PURCHASE, kuaishou::ad::AD_ROAS);
                      ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click_app_invoked)
                                  * ad.get_predict_score(PredictType::PredictType_purchase),
                          kuaishou::ad::AD_ITEM_IMPRESSION,
                          kuaishou::ad::AD_PURCHASE,
                          ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                      ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                          kuaishou::ad::EVENT_APP_INVOKED, kuaishou::ad::AD_PURCHASE);
                }
            }
          }
          break;
        case kuaishou::ad::EVENT_ENTER_MINI_PROGRAM:
        case kuaishou::ad::MINI_APP_ROAS:
            {
                ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                            kuaishou::ad::AD_PURCHASE, kuaishou::ad::AD_ROAS);
                ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                        kuaishou::ad::EVENT_APP_INVOKED, kuaishou::ad::AD_PURCHASE);
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_click_app_invoked)
                                * ad.get_predict_score(PredictType::PredictType_purchase),
                        kuaishou::ad::AD_ITEM_IMPRESSION,
                        kuaishou::ad::AD_PURCHASE,
                        ad.get_predict_cmd_id(PredictType::PredictType_purchase));
                if (is_single_col) {
                    ad.SetUnifyCtr(ad.get_server_show_ctr(),
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                        ad.get_server_show_ctr_cmd_id(),
                        ad.get_predict_cmd_id(PredictType::PredictType_click_app_invoked));
                } else {
                    ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                            ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                }
            }
        break;
        case kuaishou::ad::AD_SEVEN_DAY_ROAS:
            if (is_single_col) {
              ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                             kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
              ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                             kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                             ad.get_app_conversion_rate_cmd_id());
            } else {
              ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                             kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                             ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
              ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                             kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                             ad.get_app_conversion_rate_cmd_id());
            }
            if (enable_pay_amount_2and7) {
                ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_1_day_pay_amount) +
                    ad.get_predict_score(PredictType::PredictType_2_7_day_pay_amount),
                                        kuaishou::ad::AD_CONVERSION,
                                        ad.get_ocpx_action_type(),
                                        ad.get_predict_cmd_id(PredictType::PredictType_1_day_pay_amount));
                ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_1_day_pay_amount) +
                    ad.get_predict_score(PredictType::PredictType_2_7_day_pay_amount),
                                        kuaishou::ad::AD_CONVERSION,
                                        ad.get_ocpx_action_type(),
                                        ad.get_predict_cmd_id(PredictType::PredictType_1_day_pay_amount));
            } else {
                ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_7_day_game_conv_ltv),
                    kuaishou::ad::AD_CONVERSION, ad.get_ocpx_action_type());
                ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_7_day_game_conv_ltv),
                    kuaishou::ad::AD_CONVERSION, ad.get_ocpx_action_type());
            }
            break;
        case kuaishou::ad::EVENT_NEXTDAY_STAY:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                ad.get_app_conversion_rate_cmd_id());
            ad.SetUnifyDeepCvr(ad.get_conv_nextstay(), kuaishou::ad::AD_CONVERSION,
                ad.get_ocpx_action_type(),
                ad.get_conv_nextstay_cmd_id());
        break;
        case kuaishou::ad::EVENT_24H_STAY:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                           kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                           ad.get_app_conversion_rate_cmd_id());
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_conv_24h_stay),
                               kuaishou::ad::AD_CONVERSION, ad.get_ocpx_action_type(),
                               ad.get_predict_cmd_id(PredictType::PredictType_conv_24h_stay));
        break;
        case kuaishou::ad::EVENT_WEEK_STAY:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                ad.get_app_conversion_rate_cmd_id());
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_conv_7_day_stay),
                kuaishou::ad::AD_CONVERSION,
                ad.get_ocpx_action_type(),
                ad.get_predict_cmd_id(PredictType::PredictType_conv_7_day_stay));
        break;
        case kuaishou::ad::EVENT_PHONE_GET_THROUGH:
        case kuaishou::ad::EVENT_INTENTION_CONFIRMED:
        case kuaishou::ad::EVENT_ORDER_SUCCESSED:
        case kuaishou::ad::EVENT_MEASUREMENT_HOUSE:
        case kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                ad.get_landingpage_submit_rate_cmd_id());
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_lps_valid_clues),
                kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                ad.get_ocpx_action_type(),
                ad.get_predict_cmd_id(PredictType::PredictType_lps_valid_clues));
        break;
        case kuaishou::ad::EVENT_WECHAT_CONNECTED:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                ad.get_landingpage_submit_rate_cmd_id());
            if (!SPDM_enable_real_deep_wechat_connected_lps_cvr_set(session_data->get_spdm_ctx())) {
                ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_lps_valid_clues),
                kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                ad.get_ocpx_action_type(),
                ad.get_predict_cmd_id(PredictType::PredictType_lps_valid_clues));
            }
        break;
        case kuaishou::ad::EVENT_AD_WATCH_TIMES:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                               kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                           kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                           ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                               kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                               ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                               kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                               ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            }
        break;
        case kuaishou::ad::EVENT_AD_WATCH_5_TIMES:
        case kuaishou::ad::EVENT_AD_WATCH_10_TIMES:
        case kuaishou::ad::EVENT_AD_WATCH_20_TIMES:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                               kuaishou::ad::AD_ITEM_IMPRESSION,
                               ad.get_server_show_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                        ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                               kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                               ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                               kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                               ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            }
        break;
        case kuaishou::ad::EVENT_VALID_CLUES:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
                ad.SetUnifyCvr(click2_deep_rate, kuaishou::ad::AD_ITEM_CLICK,
                    ad.get_ocpx_action_type(), click2_deep_rate_cmd_id);
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            }
        break;
        case kuaishou::ad::EVENT_MULTI_CONVERSION:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                               kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                               kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                               ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                           kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                           ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            break;
        case kuaishou::ad::EVENT_7_DAY_PAY_TIMES:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                                kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                                kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                                ad.get_app_conversion_rate_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                                kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                                ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                                kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                                ad.get_app_conversion_rate_cmd_id());
            }
            if (enable_split_7day_paytimes_deep_r) {
              ad.SetUnifyDeepCvr(std::max(ad.get_predict_score(PredictType::PredictType_1_day_pay_times) +
                ad.get_predict_score(PredictType::PredictType_2_7_day_pay_times) -
                ad.get_predict_score(PredictType::PredictType_purchase), 0.0),
                                    kuaishou::ad::AD_CONVERSION,
                                    ad.get_ocpx_action_type(),
                                    ad.get_predict_cmd_id(PredictType::PredictType_1_day_pay_times));
              ad.SetUnifyLtv(std::max(ad.get_predict_score(PredictType::PredictType_1_day_pay_times) +
                ad.get_predict_score(PredictType::PredictType_2_7_day_pay_times) -
                ad.get_predict_score(PredictType::PredictType_purchase), 0.0),
                                    kuaishou::ad::AD_CONVERSION,
                                    ad.get_ocpx_action_type(),
                                    ad.get_predict_cmd_id(PredictType::PredictType_1_day_pay_times));
            } else {
              ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_7_day_pay_times),
                                    kuaishou::ad::AD_CONVERSION,
                                    ad.get_ocpx_action_type(),
                                    ad.get_predict_cmd_id(PredictType::PredictType_7_day_pay_times));
              ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_7_day_pay_times),
                              kuaishou::ad::AD_CONVERSION,
                              ad.get_ocpx_action_type(),
                              ad.get_predict_cmd_id(PredictType::PredictType_7_day_pay_times));
            }
            break;
        case kuaishou::ad::AD_IAA_ROAS:
        case kuaishou::ad::AD_SERIAL_IAA_ROAS:
          if (is_single_col) {
            ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
              kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
          } else {
            ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
              kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
              ad.get_ctr_cmd_id(),
              server_client_show_rate_cmd_id);
          }
          ad.SetUnifyCvr(ad.get_app_conversion_rate(),
            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
            ad.get_app_conversion_rate_cmd_id());
          ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_key_action_ltv0),
              kuaishou::ad::AD_CONVERSION, ad.get_ocpx_action_type());
          break;
        case kuaishou::ad::AD_ROAS_IAAP:
        {
          if (is_single_col) {
            ad.SetUnifyCtr(ad.get_server_show_ctr(),
              kuaishou::ad::AD_DELIVERY,
              kuaishou::ad::AD_ITEM_IMPRESSION,
              ad.get_server_show_ctr_cmd_id());
          } else {
            ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
              kuaishou::ad::AD_DELIVERY,
              kuaishou::ad::AD_ITEM_IMPRESSION,
              ad.get_ctr_cmd_id(),
              server_client_show_rate_cmd_id);
          }
          ad.SetUnifyCvr(ad.get_app_conversion_rate(),
            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
            ad.get_app_conversion_rate_cmd_id());
          double iaa_ltv = ad.get_predict_score(PredictType::PredictType_key_action_ltv0);
          double iap_ltv = ad.get_predict_score(PredictType::PredictType_game_conv_ltv);
          double hybrid_ltv = iaa_ltv + iap_ltv;
          ad.SetUnifyLtv(hybrid_ltv,
            kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS_IAAP);
          break;
        }
        case kuaishou::ad::AD_IAA_7DAY_ROAS:
        {
          if (enable_ad_iaa_7day_roas_predict) {
            if (is_single_col) {
              ad.SetUnifyCtr(ad.get_server_show_ctr(),
                kuaishou::ad::AD_DELIVERY,
                kuaishou::ad::AD_ITEM_IMPRESSION,
                ad.get_server_show_ctr_cmd_id());
            } else {
              ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                kuaishou::ad::AD_DELIVERY,
                kuaishou::ad::AD_ITEM_IMPRESSION,
                ad.get_ctr_cmd_id(),
                server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_app_conversion_rate(),
              kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
              ad.get_app_conversion_rate_cmd_id());
            if (SPDM_enable_search_iaa_7_day_roas(session_data->get_spdm_ctx())) {
              double iaa_ltv = ad.get_predict_score(PredictType::PredictType_search_industry_game_iaa_ltv7);
              ad.SetUnifyLtv(iaa_ltv,
              kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_IAA_7DAY_ROAS);
            } else {
              double iaa_ltv;
              if (SPDM_enable_search_outer_cmd_independent(session_data->get_spdm_ctx())) {
                iaa_ltv = ad.get_predict_score(PredictType::PredictType_search_industry_game_iaa_ltv7);
              } else {
                iaa_ltv = ad.get_predict_score(PredictType::PredictType_industry_game_iaa_ltv7);
              }
              ad.SetUnifyLtv(iaa_ltv,
              kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_IAA_7DAY_ROAS);
            }
          }
          break;
        }
        case kuaishou::ad::EVENT_KEY_INAPP_ACTION:
          if (is_single_col) {
            ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                          kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
          } else {
            ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                          ad.get_ctr_cmd_id(),
                          server_client_show_rate_cmd_id);
          }
          ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                          ad.get_app_conversion_rate_cmd_id());
          if (is_single_col && enable_deep_middle_model_exp && enable_deep_middle_model_kac_exp) {
            ad.SetUnifyDeepCvr((1 - deep_middle_model_ensemble_rate) *
                ad.get_predict_score(PredictType::PredictType_conv_key_inapp_action_rate) +
                deep_middle_model_ensemble_rate * ad.get_deep_middle_model_predict_rate(),
                kuaishou::ad::AD_CONVERSION, kuaishou::ad::EVENT_KEY_INAPP_ACTION,
                ad.get_predict_cmd_id(PredictType::PredictType_conv_key_inapp_action_rate));
          } else {
            ad.SetUnifyDeepCvr(
                ad.get_predict_score(PredictType::PredictType_conv_key_inapp_action_rate),
                kuaishou::ad::AD_CONVERSION, kuaishou::ad::EVENT_KEY_INAPP_ACTION,
                ad.get_predict_cmd_id(PredictType::PredictType_conv_key_inapp_action_rate));
          }
          break;
        case kuaishou::ad::EVENT_ADD_WECHAT:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
                               kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
                ad.SetUnifyCvr(click2_deep_rate,
                               kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
                               click2_deep_rate_cmd_id);
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                               kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                               ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                           kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                           ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            }
            break;
        case kuaishou::ad::AD_APPROXIMATE_PURCHASE:
            if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
                ad.SetUnifyCvr(ad.get_click2_prod_apr(),
                    kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
                    ad.get_click2_prod_apr_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_prod_apr(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_prod_apr_cmd_id());
            }
        break;
        case kuaishou::ad::AD_MERCHANT_FOLLOW:
        case kuaishou::ad::AD_FANS_TOP_FOLLOW:
        case kuaishou::ad::AD_MERCHANT_FOLLOW_FAST:
        case kuaishou::ad::AD_MERCHANT_FOLLOW_QUALITY:
            if (!is_single_col) {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_c1_merchant_follow(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_c1_merchant_follow_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_server_show_ctr(),
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_server_show_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_server_show_merchant_follow(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_server_show_merchant_follow_cmd_id());
            }
            if (is_live) {
                if (ad.Is(AdFlag::is_amd_direct_live)) {
                    ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                                    kuaishou::ad::AD_LIVE_IMPRESSION,
                                    ad.get_server_show_ctr_cmd_id());
                    ad.SetUnifyCvr(ad.get_live_p3s_wtr(), kuaishou::ad::AD_LIVE_IMPRESSION,
                                    ad.get_ocpx_action_type(),
                                    ad.get_live_p3s_wtr_cmd_id());
                    } else {
                    if (is_single_col) {
                        ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                                    kuaishou::ad::AD_ITEM_IMPRESSION,
                                    ad.get_server_show_ctr_cmd_id());
                        ad.SetUnifyCvr(ad.get_item_impression_wtr(),
                                    kuaishou::ad::AD_ITEM_IMPRESSION,
                                    ad.get_ocpx_action_type(),
                                    ad.get_item_impression_wtr_cmd_id());
                    } else {
                        ad.SetUnifyCtr(
                            origin_server_client_show_rate * ad.get_ctr(),
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                            server_client_show_rate_cmd_id, ad.get_ctr_cmd_id());
                        ad.SetUnifyCvr(ad.get_item_impression_wtr(),
                                    kuaishou::ad::AD_ITEM_IMPRESSION,
                                    ad.get_ocpx_action_type(),
                                    ad.get_item_impression_wtr_cmd_id());
                    }
                }
                {
                    ad.SetUnifyLtv(ad.get_live_p3s_ltv(), kuaishou::ad::AD_ITEM_IMPRESSION,
                            kuaishou::ad::AD_MERCHANT_ROAS);
                }
            }
        break;
        case kuaishou::ad::AD_MERCHANT_FOLLOW_ROI:
            if (!is_single_col) {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_c1_merchant_follow(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_c1_merchant_follow_cmd_id());
            } else {
                ad.SetUnifyCtr(ad.get_server_show_ctr(),
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_server_show_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_server_show_merchant_follow(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_server_show_merchant_follow_cmd_id());
            }
        break;
        case kuaishou::ad::EVENT_ORDER_PAIED:
        case kuaishou::ad::CID_EVENT_ORDER_PAID:
            if (is_live) {
                double ctr = 0.0;
                if (ad.Is(AdFlag::is_amd_direct_live)) {  // 直投
                    ctr = ad.get_server_show_ctr() * ad.get_live_audience();
                    ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                                kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                                ad.get_server_show_ctr_cmd_id(),
                                ad.get_live_audience_cmd_id());
                } else {  // 作品引流
                    if (!is_single_col) {  // 双列
                        ctr = origin_server_client_show_rate * ad.get_ctr() * ad.get_cvr();
                        ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                            server_client_show_rate_cmd_id, ad.get_ctr_cmd_id());
                    } else {   // 单列 作品引流
                        ctr = ad.get_server_show_ctr() * ad.get_cvr();
                        ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                                    kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                                    ad.get_server_show_ctr_cmd_id(), ad.get_cvr_cmd_id());
                    }
                }
                {
                    ad.SetUnifyLtv(ad.get_live_p3s_ltv(), kuaishou::ad::AD_ITEM_IMPRESSION,
                            kuaishou::ad::AD_MERCHANT_ROAS);
                }
                double c1_order_paied_rate = 1.0;
                // 主站精选页
                if (session_data->get_sub_page_id() == 10011001) {
                    auto iter = order_paied_jingxuan_cali_map->find(ad.get_author_id());
                    if (iter != order_paied_jingxuan_cali_map->end()) {
                        c1_order_paied_rate = iter->second;
                    }
                }
                ad.SetUnifyCvr(ad.get_live_order_paid() * c1_order_paied_rate,
                     kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                    ad.get_ocpx_action_type(), ad.get_live_order_paid_cmd_id());
            } else if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                        kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
                {
                  double c1_order_paied_rate = 1.0;
                  if (session_data->get_pos_manager_base().IsInspireMerchant()) {
                    c1_order_paied_rate = 0.5;
                  } else if (session_data->get_sub_page_id() == 10011001) {
                    // 主站精选页
                    auto iter = order_paied_jingxuan_cali_map->find(ad.get_author_id());
                    if (iter != order_paied_jingxuan_cali_map->end()) {
                        c1_order_paied_rate = iter->second;
                    }
                  }
                  if (enable_uplift_order_paied && !session_data->get_pos_manager_base().IsInspireMerchant()
                        && !session_data->get_is_search_request()) {
                      double raw_cvr_score = ad.get_c1_order_paied() * c1_order_paied_rate;
                      double uplift_cvr_score = ad.get_uplift_order_paied();
                      double new_score = uplift_coef * uplift_cvr_score +
                                            (1.0 - uplift_coef) * raw_cvr_score;
                      double new_score_cut = std::min(std::max(new_score,
                                                uplift_order_low_bound * raw_cvr_score),
                                                uplift_order_high_bound  * raw_cvr_score);
                      ad.SetUnifyCvr(new_score_cut,
                      kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                      ad.get_c1_order_paied_cmd_id());
                  } else if (session_data->get_is_model_explore()
                    && !session_data->get_pos_manager_base().IsInspireMerchant()
                    && !session_data->get_is_search_request()
                    && !session_data->get_pos_manager_base().IsGuessYouLike()
                    && ad.get_order_paid_explore() > 0) {
                      ad.SetUnifyCvr(ad.get_order_paid_explore() * c1_order_paied_rate,
                      kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                      ad.get_c1_order_paied_cmd_id());
                  } else {
                      ad.SetUnifyCvr(ad.get_c1_order_paied() * c1_order_paied_rate,
                      kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                      ad.get_c1_order_paied_cmd_id());
                  }
                }
            } else if (session_data->get_pos_manager_base().IsInspireMerchant()) {
                // 激励电商广告暂时不考虑 server_client_show_rate
                ad.SetUnifyCtr(ad.get_ctr(),
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ctr_cmd_id());
                ad.SetUnifyCvr(ad.get_c1_order_paied(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_c1_order_paied_cmd_id());
            } else {
                if (enable_drop_high_ctr_traffic && ad.get_ctr() > high_ctr_threshold) {
                    ad.SetUnifyCtr(0.0,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                        ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                } else {
                    ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                        ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                }
                if (enable_uplift_order_paied && !session_data->get_pos_manager_base().IsInspireMerchant()  // NOLINT
                      && !session_data->get_is_search_request()) {
                    double raw_cvr_score = ad.get_c1_order_paied();
                    double uplift_cvr_score = ad.get_uplift_order_paied();
                    double new_score = uplift_coef * uplift_cvr_score +  (1.0 - uplift_coef) * raw_cvr_score;
                    double new_score_cut = std::min(std::max(new_score,
                                                uplift_order_low_bound * raw_cvr_score),
                                                uplift_order_high_bound  * raw_cvr_score);
                    ad.SetUnifyCvr(new_score_cut,
                      kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                      ad.get_c1_order_paied_cmd_id());
                } else if (session_data->get_is_model_explore() && !session_data->get_is_search_request()
                        && ad.get_order_paid_explore() > 0) {
                    ad.SetUnifyCvr(ad.get_order_paid_explore(),
                      kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                      ad.get_c1_order_paied_cmd_id());
                } else if (session_data->get_is_search_good_card()) {
                    // 搜索商品卡
                    ad.SetUnifyCtr(ad.get_ctr(),
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                        ad.get_ctr_cmd_id());
                    ad.SetUnifyCvr(ad.get_c1_order_paied(),
                      kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
                      ad.get_c1_order_paied_cmd_id());
                } else {
                  ad.SetUnifyCvr(ad.get_c1_order_paied(),
                      kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                      ad.get_c1_order_paied_cmd_id());
                }
            }
        break;
        case kuaishou::ad::EVENT_GOODS_VIEW:
            if (is_live) {
                ad.SetUnifyCtr(ad.get_live_play_3s(), kuaishou::ad::AD_LIVE_IMPRESSION,
                    kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_live_play_3s_cmd_id());
                ad.SetUnifyCvr(ad.get_live_goods_view(),
                    kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_ocpx_action_type(),
                    ad.get_live_goods_view_cmd_id());
                {
                    ad.SetUnifyLtv(ad.get_live_p3s_ltv(), kuaishou::ad::AD_ITEM_IMPRESSION,
                    kuaishou::ad::AD_MERCHANT_ROAS);
                }
            } else if (is_single_col) {
                ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
                    kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
                ad.SetUnifyCvr(1.0,
                    kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type());
            } else {
                ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                    kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                    ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
                ad.SetUnifyCvr(ad.get_cvr(),
                    kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                    ad.get_cvr_cmd_id());
            }
        break;
        case kuaishou::ad::AD_LIVE_PLAYED_3S:
            if (is_live) {
                if (ad.get_live_creative_type() ==
                    kuaishou::ad::AdEnum_LiveCreativeType_LIVE_STREAM_CREATIVE_TYPE) {
                  ad.SetUnifyCtr(ad.get_live_play_3s(), kuaishou::ad::AD_LIVE_IMPRESSION,
                      kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_live_play_3s_cmd_id());
                  ad.SetUnifyCvr(1.0,
                      kuaishou::ad::AD_LIVE_PLAYED_3S, ad.get_ocpx_action_type());
                } else {
                  ad.SetUnifyCtr(ad.get_ctr(),
                      kuaishou::ad::AD_PHOTO_IMPRESSION, kuaishou::ad::AD_ITEM_IMPRESSION,
                      ad.get_ctr_cmd_id());
                  ad.SetUnifyCvr(ad.get_cvr(),
                      kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                      ad.get_cvr_cmd_id());
                }
                {
                    ad.SetUnifyLtv(ad.get_live_p3s_ltv(), kuaishou::ad::AD_ITEM_IMPRESSION,
                            kuaishou::ad::AD_MERCHANT_ROAS);
                }
            }
        break;
        case kuaishou::ad::EVENT_JINJIAN:
        case kuaishou::ad::AD_CREDIT_GRANT:
          if (is_single_col) {
            ad.SetUnifyCtr(ad.get_server_show_cvr(), kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_ITEM_CLICK, ad.get_server_show_cvr_cmd_id());
            ad.SetUnifyCvr(click2_deep_rate,
                    kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
                    click2_deep_rate_cmd_id);
          } else {
            ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                           kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                           ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_deep_rate),
                           kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                           ad.get_predict_cmd_id(PredictType::PredictType_deep_rate));
            if (session_data->get_is_search_request()) {
              ad.SetUnifyCtr(
                  ad.get_ctr() * origin_server_client_show_rate,
                  kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                  ad.get_ctr_cmd_id(), ad.get_cvr_cmd_id());

              ad.SetUnifyCvr(click2_deep_rate *  ad.get_cvr(), kuaishou::ad::AD_ITEM_IMPRESSION,
                             ad.get_ocpx_action_type(),
                             click2_deep_rate_cmd_id);
            }
          }
        break;
        case kuaishou::ad::AD_LIVE_AUDIENCE:
        case kuaishou::ad::AD_LIVE_AUDIENCE_QUALITY:
        case kuaishou::ad::AD_LIVE_AUDIENCE_FAST:
            if (is_live) {
                double ctr = 0.0;
                if (ad.Is(AdFlag::is_amd_direct_live)) {  // 直投
                    ctr = ad.get_server_show_ctr() * ad.get_live_audience();
                    ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                                kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                                ad.get_server_show_ctr_cmd_id(),
                                ad.get_live_audience_cmd_id());
                } else {  // 作品引流
                    if (!is_single_col) {  // 双列
                        ctr = origin_server_client_show_rate * ad.get_ctr() * ad.get_cvr();
                        ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                            kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                            server_client_show_rate_cmd_id, ad.get_ctr_cmd_id());
                    } else {   // 单列 作品引流
                        ctr = ad.get_server_show_ctr() * ad.get_cvr();
                        ad.SetUnifyCtr(ctr, kuaishou::ad::AD_DELIVERY,
                                    kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                                    ad.get_server_show_ctr_cmd_id(), ad.get_cvr_cmd_id());
                    }
                }
                {
                    ad.SetUnifyLtv(ad.get_live_p3s_ltv(), kuaishou::ad::AD_ITEM_IMPRESSION,
                            kuaishou::ad::AD_MERCHANT_ROAS);
                }
                ad.SetUnifyCvr(1.0, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                    kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED);
            }
        break;
        default:
        break;  // break
    }
  }
  return StraRetCode::SUCC;
}  // NOLINT

const char* SearchDeepUnifyCxrPlugin::Name() {
  return "SerachDeepUnifyCxrPlugin";
}

void SearchDeepUnifyCxrPlugin::Clear() {
}

bool SearchDeepUnifyCxrPlugin::IsRun(const ContextData* session_data, const Params* params,
                               AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode SearchDeepUnifyCxrPlugin::Process(ContextData* session_data, Params* reserve_threshold_params,
                                        AdRankUnifyScene pos, AdList* ad_list) {
  if (session_data == nullptr || ad_list == nullptr || ad_list->Size() <= 0) {
    return StraRetCode::SUCC;
  }
  bool is_single_col = session_data->get_is_thanos_request();
  double deep_middle_model_ensemble_rate = RankKconfUtil::deepMiddleModelEnsambleRate();
  bool enable_deep_middle_model_exp = SPDM_enable_deep_middle_model_exp(session_data->get_spdm_ctx());
  bool enable_deep_middle_model_nd_exp = SPDM_enable_deep_middle_model_nd_exp(session_data->get_spdm_ctx());
  const auto purchase_roas_fuse_conf_ptr = RankKconfUtil::purchaseRoiFuseConf();
  for (auto* p_ad : ad_list->Ads()) {
    AD_LIST_SKIP_LIVE_AD(session_data, p_ad)
    SEARCH_AD_LIST_SKIP_LIVE_AD(session_data, p_ad)
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad)
    auto& ad = *p_ad;
    // 每日留存单出价写 cvr pcvr
    if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_RETENTION_DAYS) {
        if (is_single_col) {
            ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
          } else {
            ad.SetUnifyCtr(ad.get_ctr() *
                         ad.get_predict_score(PredictType::PredictType_server_client_show_rate),
                         kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                         ad.get_ctr_cmd_id(),
                         ad.get_predict_cmd_id(PredictType::PredictType_server_client_show_rate));
          }
        ad.SetUnifyCvr(ad.get_app_conversion_rate(),
                        kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                        ad.get_app_conversion_rate_cmd_id());
        ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_everyday_stay),
                                kuaishou::ad::AD_CONVERSION, kuaishou::ad::EVENT_RETENTION_DAYS,
                                ad.get_predict_cmd_id(PredictType::PredictType_everyday_stay));
    }

    switch (ad.get_deep_conversion_type()) {
      case kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY:
        if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_appinvoke_nextstay),
                               kuaishou::ad::EVENT_APP_INVOKED,
                               kuaishou::ad::AdActionType::EVENT_NEXTDAY_STAY,
                               ad.get_predict_cmd_id(PredictType::PredictType_appinvoke_nextstay));
        } else {
          if (is_single_col && enable_deep_middle_model_exp && enable_deep_middle_model_nd_exp &&
              enable_deep_middle_model_nd_exp) {
            ad.SetUnifyDeepCvr((1 - deep_middle_model_ensemble_rate) * ad.get_conv_nextstay() +
                               deep_middle_model_ensemble_rate * ad.get_deep_middle_model_predict_rate(),
                               kuaishou::ad::AD_CONVERSION, kuaishou::ad::AdActionType::EVENT_NEXTDAY_STAY,
                               ad.get_conv_nextstay_cmd_id());
          } else {
            ad.SetUnifyDeepCvr(ad.get_conv_nextstay(),
                               kuaishou::ad::AD_CONVERSION, kuaishou::ad::AdActionType::EVENT_NEXTDAY_STAY,
                               ad.get_conv_nextstay_cmd_id());
          }
        }
        break;
      case kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION:
        if (ad.get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED) {
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_lps_acquisition),
                               kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                               kuaishou::ad::AdActionType::AD_EFFECTIVE_CUSTOMER_ACQUISITION,
                               ad.get_predict_cmd_id(PredictType::PredictType_lps_acquisition));
        }
        if (ad.get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_lps_acquisition),
                              kuaishou::ad::LEADS_SUBMIT,
                              kuaishou::ad::AdActionType::AD_EFFECTIVE_CUSTOMER_ACQUISITION,
                              ad.get_predict_cmd_id(PredictType::PredictType_lps_acquisition));
        }
        if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT) {
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_lps_acquisition),
                              kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT,
                              kuaishou::ad::AdActionType::AD_EFFECTIVE_CUSTOMER_ACQUISITION,
                              ad.get_predict_cmd_id(PredictType::PredictType_lps_acquisition));
        }
        if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) {
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_lps_acquisition),
                              kuaishou::ad::EVENT_WECHAT_CONNECTED,
                              kuaishou::ad::AdActionType::AD_EFFECTIVE_CUSTOMER_ACQUISITION,
                              ad.get_predict_cmd_id(PredictType::PredictType_lps_acquisition));
        }
        break;
      case kuaishou::ad::AdCallbackLog_EventType_EVENT_WEEK_STAY:
        if (ad.get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_conv_7_day_stay),
                             kuaishou::ad::AD_CONVERSION, kuaishou::ad::AdActionType::EVENT_WEEK_STAY,
                             ad.get_predict_cmd_id(PredictType::PredictType_conv_7_day_stay));
        }
        break;
      case kuaishou::ad::AdCallbackLog_EventType_EVENT_24H_STAY:
        ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_conv_24h_stay),
                           kuaishou::ad::AD_CONVERSION, kuaishou::ad::AdActionType::EVENT_24H_STAY,
                           ad.get_predict_cmd_id(PredictType::PredictType_conv_24h_stay));
        break;
      case kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN:
      case kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT:
        if (is_single_col) {
          ad.SetUnifyCtr(ad.get_server_show_cvr(),
                         kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                         ad.get_server_show_cvr_cmd_id());
          ad.SetUnifyCvr(ad.get_click2_lps(),
                         kuaishou::ad::AD_ITEM_CLICK, ad.get_ocpx_action_type(),
                         ad.get_click2_lps_cmd_id());
        } else {
          ad.SetUnifyCtr(ad.get_ctr() *
                         ad.get_predict_score(PredictType::PredictType_server_client_show_rate),
                         kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                         ad.get_ctr_cmd_id(),
                         ad.get_predict_cmd_id(PredictType::PredictType_server_client_show_rate));
          ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                         kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                         ad.get_landingpage_submit_rate_cmd_id());
        }
        break;
      case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY:
        if (ad.get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                               kuaishou::ad::EVENT_APP_INVOKED,
                               kuaishou::ad::AD_PURCHASE,
                               ad.get_predict_cmd_id(PredictType::PredictType_purchase));
        } else {
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                               kuaishou::ad::AD_CONVERSION,
                               kuaishou::ad::AdActionType::EVENT_PAY,
                               ad.get_predict_cmd_id(PredictType::PredictType_purchase));
        }
        break;
      case kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY:
        if ((ad.get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION)
              || (ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE)) {
          if (ad.get_is_purchase_pay_test()) {
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase_ltv),
                               ad.get_ocpx_action_type(), kuaishou::ad::AdActionType::AD_ROAS,
                               ad.get_predict_cmd_id(PredictType::PredictType_purchase_ltv));
            ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_purchase_ltv),
                           ad.get_ocpx_action_type(), kuaishou::ad::AdActionType::AD_ROAS);
          } else {
            if (ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE) {
              ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                                 kuaishou::ad::AD_CONVERSION, kuaishou::ad::AdActionType::AD_PURCHASE,
                                 ad.get_predict_cmd_id(PredictType::PredictType_purchase));
              ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                             kuaishou::ad::AdActionType::AD_PURCHASE, kuaishou::ad::AdActionType::AD_ROAS);
            } else {
              ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                                 ad.get_ocpx_action_type(), kuaishou::ad::AdActionType::AD_ROAS,
                                 ad.get_predict_cmd_id(PredictType::PredictType_game_conv_ltv));
              ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                             ad.get_ocpx_action_type(), kuaishou::ad::AdActionType::AD_ROAS);
            }
          }
        } else if (ad.get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION) {
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                                ad.get_ocpx_action_type(), kuaishou::ad::AdActionType::AD_ROAS,
                                ad.get_predict_cmd_id(PredictType::PredictType_game_conv_ltv));
          ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                          ad.get_ocpx_action_type(), kuaishou::ad::AdActionType::AD_ROAS);

        } else {
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_game_purchase_ltv),
                             ad.get_ocpx_action_type(), kuaishou::ad::AdActionType::AD_ROAS,
                             ad.get_predict_cmd_id(PredictType::PredictType_game_purchase_ltv));
          ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_purchase_ltv),
                         ad.get_ocpx_action_type(), kuaishou::ad::AdActionType::AD_ROAS);
        }
        break;
      case kuaishou::ad::AdCallbackLog_EventType_EVENT_RETENTION_DAYS:
        if (ad.get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_everyday_stay),
                                 ad.get_ocpx_action_type(),
                                 kuaishou::ad::AdActionType::EVENT_RETENTION_DAYS,
                                 ad.get_predict_cmd_id(PredictType::PredictType_everyday_stay));
        }
        break;
      default:
        break;
    }
  }
  return StraRetCode::SUCC;
}

const char* SearchNonMerchantUnifyCxrPlugin::Name() {
  return "NonMerchantUnifyCxrPlugin";
}

void SearchNonMerchantUnifyCxrPlugin::Clear() {
}

bool SearchNonMerchantUnifyCxrPlugin::IsRun(const ContextData* session_data, const Params* params,
                                      AdRankUnifyScene pos, const AdList* adlist) {
  return true;
}

StraRetCode SearchNonMerchantUnifyCxrPlugin::Process(ContextData* session_data,
    Params* reserve_threshold_params, AdRankUnifyScene pos, AdList* ad_list) {
  if (session_data == nullptr || ad_list == nullptr || ad_list->Size() <= 0) {
    return StraRetCode::SUCC;
  }
  if (ad_list->GetOuterLoopAdSize() < 1) {
    return StraRetCode::SUCC;
  }
  bool is_single_col = session_data->get_is_thanos_request();
  bool enable_split_7day_paytimes_deep_r = SPDM_enable_split_7day_paytimes_deep_r(session_data->get_spdm_ctx());  // NOLINT

  for (auto* p_ad : ad_list->Ads()) {
    if (p_ad == nullptr ||
        (p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
         p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_PHOTO &&
         p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE &&
         !p_ad->Is(AdFlag::is_self_service_ad))) {
      continue;
    }
    if (!p_ad->Is(AdFlag::is_outer_loop_ad)) continue;
    SEARCH_AD_LIST_SKIP_LIVE_AD(session_data, p_ad)
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad)
    auto& ad = *p_ad;
    double non_merchant_live_ctr =
      ad.get_predict_score(PredictType::PredictType_non_merchant_live_ctr);
    double non_merchant_live_sctr =
      ad.get_predict_score(PredictType::PredictType_non_merchant_live_sctr);
    double non_merchant_live_cvr =
      ad.get_predict_score(PredictType::PredictType_non_merchant_live_cvr);

    auto non_merchant_live_ctr_cmd_id =
      ad.get_predict_cmd_id(PredictType::PredictType_non_merchant_live_ctr);
    auto non_merchant_live_sctr_cmd_id =
      ad.get_predict_cmd_id(PredictType::PredictType_non_merchant_live_sctr);
    auto non_merchant_live_cvr_cmd_id =
      ad.get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr);

    double origin_server_client_show_rate =
        ad.get_predict_score(PredictType::PredictType_server_client_show_rate);
    int32_t server_client_show_rate_cmd_id =
        ad.get_predict_cmd_id(PredictType::PredictType_server_client_show_rate);


    switch (ad.get_ocpx_action_type()) {
      case kuaishou::ad::AD_ITEM_CLICK:
        if (is_single_col) {
          ad.ResetUnifyCxr();
          if (ad.get_live_creative_type() ==
                            kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                           kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                           non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
          } else if (ad.get_live_creative_type() ==
                            kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                           kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                           non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
          } else if (ad.Is(AdFlag::is_self_service_ad)) {
            ad.SetUnifyCtr(ad.get_consult_ctr(),
                           kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_CLICK,
                           ad.get_consult_ctr_cmd_id());
          }
          ad.SetUnifyCvr(1, ad.get_ocpx_action_type(), ad.get_ocpx_action_type());
        }
        break;
      case kuaishou::ad::AD_CONVERSION:
        if (is_single_col) {
          ad.ResetUnifyCxr();
          if (ad.get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(non_merchant_live_cvr,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                        non_merchant_live_cvr_cmd_id);
          } else if (ad.get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                          non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(non_merchant_live_cvr * non_merchant_live_ctr,
                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                          non_merchant_live_cvr_cmd_id, non_merchant_live_ctr_cmd_id);
          }
        } else {
          // 双列激励直播
          if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
              session_data->get_pos_manager_base().IsInspireLiveFeed()) {
            if (ad.get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
              ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                              kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                              non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
              ad.SetUnifyCvr(non_merchant_live_cvr,
                              kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                              non_merchant_live_cvr_cmd_id);
            }
          }
        }
        break;
      case kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED:
        if (is_single_col) {
          ad.ResetUnifyCxr();
          if (ad.get_live_creative_type() ==
                        kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(non_merchant_live_cvr,
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                          non_merchant_live_cvr_cmd_id);
          } else if (ad.get_live_creative_type() ==
                      kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_sctr,
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                            non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(non_merchant_live_cvr * non_merchant_live_ctr,
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                            non_merchant_live_cvr_cmd_id, non_merchant_live_ctr_cmd_id);
          }
        }
        break;
      case kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT:
        if (is_single_col && ad.Is(AdFlag::is_industry_live)) {
          ad.ResetUnifyCxr();
          if (ad.get_live_creative_type() ==
                        kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(non_merchant_live_cvr,
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT,
                          non_merchant_live_cvr_cmd_id);
          } else if (ad.get_live_creative_type() ==
                      kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_sctr,
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                            non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(non_merchant_live_cvr * non_merchant_live_ctr,
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT,
                            non_merchant_live_cvr_cmd_id, non_merchant_live_ctr_cmd_id);
          }
        }
        break;
      case kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION:
          if (is_single_col) {
              ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                  kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
          } else {
              ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                  kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                  ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
          }
          ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
              kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
              ad.get_landingpage_submit_rate_cmd_id());
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_lps_acquisition),
            kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
            kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION,
            ad.get_predict_cmd_id(PredictType::PredictType_lps_acquisition));
          break;
      case kuaishou::ad::EVENT_ORDER_PAIED:
      case kuaishou::ad::CID_EVENT_ORDER_PAID:
        ad.ResetUnifyCxr();
        ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                       kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                       non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
        ad.SetUnifyCvr(non_merchant_live_cvr,
                       kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                       kuaishou::ad::EVENT_ORDER_PAIED,
                       non_merchant_live_cvr_cmd_id);
        if (ad.get_live_creative_type() ==
              kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
          ad.SetUnifyCtr(non_merchant_live_sctr,
                         kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                         non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(non_merchant_live_cvr * non_merchant_live_ctr,
                         kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::EVENT_ORDER_PAIED,
                         non_merchant_live_cvr_cmd_id, non_merchant_live_ctr_cmd_id);
        }
        break;
      case kuaishou::ad::AD_PURCHASE:
      case kuaishou::ad::EVENT_PAY_UNION:
      case kuaishou::ad::AD_MERCHANT_ROAS:
      case kuaishou::ad::AD_MERCHANT_T7_ROI:
      case kuaishou::ad::AD_STOREWIDE_ROAS:
      case kuaishou::ad::CID_ROAS:
        ad.ResetUnifyCxr();
        if (ad.get_live_creative_type() ==
                            kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
          ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(non_merchant_live_cvr,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                        non_merchant_live_cvr_cmd_id);
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_non_merchant_live_deep_cvr),  // NOLINT
                            kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED, kuaishou::ad::AD_PURCHASE,
                            ad.get_predict_cmd_id(PredictType::PredictType_non_merchant_live_deep_cvr));  // NOLINT
        } else if (ad.get_live_creative_type() ==
                            kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
          ad.SetUnifyCtr(non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                          non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(non_merchant_live_cvr * non_merchant_live_ctr,
                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                          non_merchant_live_cvr_cmd_id, non_merchant_live_ctr_cmd_id);
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_non_merchant_live_deep_cvr),  // NOLINT
                              kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED, kuaishou::ad::AD_PURCHASE,
                              ad.get_predict_cmd_id(PredictType::PredictType_non_merchant_live_deep_cvr));  // NOLINT
        }
        break;
      case kuaishou::ad::AD_ROAS:
        ad.ResetUnifyCxr();
        if (ad.get_live_creative_type() ==
                kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
          ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(non_merchant_live_cvr,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                        non_merchant_live_cvr_cmd_id);
          // 双列激励直播
          if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
              session_data->get_pos_manager_base().IsInspireLiveFeed()) {
            if (ad.get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
              ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
              ad.SetUnifyCvr(non_merchant_live_cvr,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                        non_merchant_live_cvr_cmd_id);
            }
          }
        } else if (ad.get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
          ad.SetUnifyCtr(non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                          non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(non_merchant_live_cvr * non_merchant_live_ctr,
                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                          non_merchant_live_cvr_cmd_id, non_merchant_live_ctr_cmd_id);
        }
        ad.SetUnifyLtv(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                        kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS);
        ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_game_conv_ltv),
                        kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_ROAS);
        break;
      case kuaishou::ad::EVENT_7_DAY_PAY_TIMES:
        ad.ResetUnifyCxr();
        if (ad.get_live_creative_type() ==
                kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
          ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(non_merchant_live_cvr,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                        non_merchant_live_cvr_cmd_id);
          // 双列激励直播
          if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
              session_data->get_pos_manager_base().IsInspireLiveFeed()) {
            if (ad.get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
              ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
              ad.SetUnifyCvr(non_merchant_live_cvr,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                        non_merchant_live_cvr_cmd_id);
            }
          }
        } else if (ad.get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
          ad.SetUnifyCtr(non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                          non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(non_merchant_live_cvr * non_merchant_live_ctr,
                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                          non_merchant_live_cvr_cmd_id, non_merchant_live_ctr_cmd_id);
        }
        if (enable_split_7day_paytimes_deep_r) {
          ad.SetUnifyDeepCvr(std::max(ad.get_predict_score(PredictType::PredictType_1_day_pay_times) +
            ad.get_predict_score(PredictType::PredictType_2_7_day_pay_times) -
            ad.get_predict_score(PredictType::PredictType_purchase), 0.0),
                                kuaishou::ad::AD_CONVERSION,
                                ad.get_ocpx_action_type(),
                                ad.get_predict_cmd_id(PredictType::PredictType_1_day_pay_times));
        } else {
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_7_day_pay_times),
                                kuaishou::ad::AD_CONVERSION,
                                ad.get_ocpx_action_type(),
                                ad.get_predict_cmd_id(PredictType::PredictType_7_day_pay_times));
        }
        break;
      case kuaishou::ad::AD_PURCHASE_CONVERSION:
        ad.ResetUnifyCxr();
        if (ad.get_live_creative_type() ==
                kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
          ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(non_merchant_live_cvr,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                        non_merchant_live_cvr_cmd_id);
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_non_merchant_live_deep_cvr),  // NOLINT
                            kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_PURCHASE_CONVERSION,
                            ad.get_predict_cmd_id(PredictType::PredictType_non_merchant_live_deep_cvr));  // NOLINT
          // 双列激励直播
          if (ad.get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
              session_data->get_pos_manager_base().IsInspireLiveFeed()) {
            if (ad.get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
              ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
              ad.SetUnifyCvr(non_merchant_live_cvr,
                        kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                        non_merchant_live_cvr_cmd_id);
              ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_non_merchant_live_deep_cvr),  // NOLINT
                            kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_PURCHASE_CONVERSION,
                            ad.get_predict_cmd_id(PredictType::PredictType_non_merchant_live_deep_cvr));  // NOLINT
            }
          }
        } else if (ad.get_live_creative_type() ==
                          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
          ad.SetUnifyCtr(non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                          non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(non_merchant_live_cvr * non_merchant_live_ctr,
                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_CONVERSION,
                          non_merchant_live_cvr_cmd_id, non_merchant_live_ctr_cmd_id);
          ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_non_merchant_live_deep_cvr),  // NOLINT
                            kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_PURCHASE_CONVERSION,
                            ad.get_predict_cmd_id(PredictType::PredictType_non_merchant_live_deep_cvr));  // NOLINT
        }
        break;
      case kuaishou::ad::AD_LIVE_AUDIENCE:
        if (ad.Is(AdFlag::is_pop_recruit_ad) || ad.Is(AdFlag::is_self_service_ad)) {
          ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                        kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                        non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
          ad.SetUnifyCvr(1.0, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                         kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED);
        }
        break;
      case kuaishou::ad::AD_MERCHANT_FOLLOW:
        if (ad.Is(AdFlag::is_pop_recruit_ad) || p_ad->Is(AdFlag::is_self_service_ad)) {
          if (ad.Is(AdFlag::is_live_ad)) {  // 直投
            ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(ad.get_wtr(),
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_MERCHANT_FOLLOW,
                          ad.get_wtr_cmd_id());
          } else if (ad.Is(AdFlag::is_p2l)) {  // 引流
            ad.SetUnifyCtr(non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                          non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(ad.get_wtr(),
                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_MERCHANT_FOLLOW,
                          ad.get_wtr_cmd_id());
          } else {  // 作品
            if (is_single_col) {
              ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                  kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
              ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                  kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                  ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_wtr(),
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_MERCHANT_FOLLOW,
                            ad.get_wtr_cmd_id());
          }
        }
        break;
      case kuaishou::ad::AD_BUTTON_CLICK_CONSULT:
        if (ad.Is(AdFlag::is_pop_recruit_ad) || ad.Is(AdFlag::is_self_service_ad)) {
          if (ad.Is(AdFlag::is_live_ad)) {  // 直投
            ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(ad.get_consult_ctr(),
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          kuaishou::ad::AD_BUTTON_CLICK_CONSULT, ad.get_consult_ctr_cmd_id());
          } else if (ad.Is(AdFlag::is_p2l)) {  // 引流
            ad.SetUnifyCtr(non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                          non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(ad.get_consult_ctr(),
                          kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_BUTTON_CLICK_CONSULT,
                          ad.get_consult_ctr_cmd_id());
          } else {  // 作品
            if (is_single_col) {
              ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                  kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
              ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                  kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                  ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_consult_ctr(),
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::AD_BUTTON_CLICK_CONSULT,
                            ad.get_consult_ctr_cmd_id());
          }
        }
        break;
      case kuaishou::ad::LEADS_SUBMIT:
        if (ad.Is(AdFlag::is_pop_recruit_ad) || ad.Is(AdFlag::is_self_service_ad)) {
          if (ad.Is(AdFlag::is_live_ad)) {  // 直投
            ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
            if (ad.Is(AdFlag::is_pop_recruit_ad)) {
              ad.SetUnifyCvr(ad.get_leads_submit(),
                            kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::LEADS_SUBMIT,
                            ad.get_leads_submit_cmd_id());
            } else {
              ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_non_merchant_live_cvr),
                            kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::LEADS_SUBMIT,
                            ad.get_predict_cmd_id(PredictType::PredictType_non_merchant_live_cvr));
            }
          } else if (ad.Is(AdFlag::is_p2l)) {  // 引流
            ad.SetUnifyCtr(non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                          non_merchant_live_sctr_cmd_id);
            if (ad.Is(AdFlag::is_pop_recruit_ad)) {
              ad.SetUnifyCvr(ad.get_leads_submit(),
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::LEADS_SUBMIT,
                            ad.get_leads_submit_cmd_id());
            } else {
              ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::LEADS_SUBMIT,
                            ad.get_landingpage_submit_rate_cmd_id());
            }
          } else {  // 作品
            if (is_single_col) {
              ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                  kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
              ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                  kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                  ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            if (ad.Is(AdFlag::is_pop_recruit_ad)) {
              ad.SetUnifyCvr(ad.get_leads_submit(),
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::LEADS_SUBMIT,
                            ad.get_leads_submit_cmd_id());
            } else {
              ad.SetUnifyCvr(ad.get_landingpage_submit_rate(),
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::LEADS_SUBMIT,
                            ad.get_landingpage_submit_rate_cmd_id());
            }
          }
        } else if (is_single_col && ad.Is(AdFlag::is_industry_live)) {
          ad.ResetUnifyCxr();
          if (ad.get_live_creative_type() ==
                        kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_ctr * non_merchant_live_sctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          non_merchant_live_ctr_cmd_id, non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(non_merchant_live_cvr,
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          kuaishou::ad::LEADS_SUBMIT,
                          non_merchant_live_cvr_cmd_id);
          } else if (ad.get_live_creative_type() ==
                      kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
            ad.SetUnifyCtr(non_merchant_live_sctr,
                            kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                            non_merchant_live_sctr_cmd_id);
            ad.SetUnifyCvr(non_merchant_live_cvr * non_merchant_live_ctr,
                            kuaishou::ad::AD_ITEM_IMPRESSION, kuaishou::ad::LEADS_SUBMIT,
                            non_merchant_live_cvr_cmd_id, non_merchant_live_ctr_cmd_id);
          }
        }
        break;
      case kuaishou::ad::AD_FANS_TOP_PLAY:
        if (p_ad->Is(AdFlag::is_self_service_ad)) {
            if (is_single_col) {
              ad.SetUnifyCtr(ad.get_server_show_ctr(), kuaishou::ad::AD_DELIVERY,
                  kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_server_show_ctr_cmd_id());
            } else {
              ad.SetUnifyCtr(ad.get_ctr() * origin_server_client_show_rate,
                  kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_ITEM_IMPRESSION,
                  ad.get_ctr_cmd_id(), server_client_show_rate_cmd_id);
            }
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_ltr),
                           kuaishou::ad::AD_ITEM_IMPRESSION, ad.get_ocpx_action_type(),
                           ad.get_predict_cmd_id(PredictType::PredictType_ltr));
          }
        break;
      default:
        break;
    }

    // 搜素独立行业直播 ecpm
    if (SPDM_enable_search_independent_industry_live(session_data->get_spdm_ctx()) &&
        p_ad->get_campaign_type() == kuaishou::ad::AdEnum::FANS_LIVE_STREAM_PROMOTE) {
      switch (ad.get_ocpx_action_type()) {
        case kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED:
          if (is_single_col) {
            if (ad.get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
              // 单列短引
              auto sctr = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr  = 1.0;
              auto jinren = ad.get_predict_score(PredictType::PredictType_cvr);
              ad.SetUnifyCtr(sctr * ctr * jinren,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr),
                          ad.get_predict_cmd_id(PredictType::PredictType_cvr));
            } else {
              // 单列直投
              auto sctr = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr  = 1.0;
              ad.SetUnifyCtr(sctr * ctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr));
            }
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_landingpage_submit),
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_landingpage_submit));
          } else {
            if (ad.get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
              // 双列短引
              auto sctr   = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr    = ad.get_live_audience();
              auto jinren = ad.get_predict_score(PredictType::PredictType_cvr);
              if (SPDM_enable_search_p2l_to_live_ecpm(session_data->get_spdm_ctx())) {
                jinren = 1.0;
              }
              ad.SetUnifyCtr(sctr * ctr * jinren,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr),
                          ad.get_live_audience_cmd_id());
            } else {
              // 双列直投
              auto sctr   = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr    = ad.get_live_audience();
              ad.SetUnifyCtr(sctr * ctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr),
                          ad.get_live_audience_cmd_id());
            }
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_landingpage_submit),
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_landingpage_submit));
          }
          break;
        case kuaishou::ad::AD_CONVERSION:
          if (is_single_col) {
            if (ad.get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
              // 单列短引
              auto sctr = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr  = 1.0;
              auto jinren = ad.get_predict_score(PredictType::PredictType_cvr);
              ad.SetUnifyCtr(sctr * ctr * jinren,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr),
                          ad.get_predict_cmd_id(PredictType::PredictType_cvr));
            } else {
              // 单列直投
              auto sctr = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr  = 1.0;
              ad.SetUnifyCtr(sctr * ctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr));
            }
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_app_conversion_rate),
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                          ad.get_predict_cmd_id(PredictType::PredictType_app_conversion_rate));
          } else {
            if (ad.get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
              // 双列短引
              auto sctr   = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr    = ad.get_live_audience();
              auto jinren = ad.get_predict_score(PredictType::PredictType_cvr);
              if (SPDM_enable_search_p2l_to_live_ecpm(session_data->get_spdm_ctx())) {
                jinren = 1.0;
              }
              ad.SetUnifyCtr(sctr * ctr * jinren,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr),
                          ad.get_live_audience_cmd_id());
            } else {
              // 双列直投
              auto sctr   = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr    = ad.get_live_audience();
              ad.SetUnifyCtr(sctr * ctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr),
                          ad.get_live_audience_cmd_id());
            }
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_app_conversion_rate),
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                          ad.get_predict_cmd_id(PredictType::PredictType_app_conversion_rate));
          }
          break;
        case kuaishou::ad::AD_PURCHASE_CONVERSION:
          if (is_single_col) {
            if (ad.get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
              // 单列短引
              auto sctr = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr  = 1.0;
              auto jinren = ad.get_predict_score(PredictType::PredictType_cvr);
              ad.SetUnifyCtr(sctr * ctr * jinren,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr),
                          ad.get_predict_cmd_id(PredictType::PredictType_cvr));
            } else {
              // 单列直投
              auto sctr = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr  = 1.0;
              ad.SetUnifyCtr(sctr * ctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr));
            }
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_app_conversion_rate),
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                          ad.get_predict_cmd_id(PredictType::PredictType_app_conversion_rate));
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                            kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_PURCHASE_CONVERSION,
                            ad.get_predict_cmd_id(PredictType::PredictType_purchase));
          } else {
            if (ad.get_live_creative_type() == kuaishou::ad::AdEnum::PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
              // 双列短引
              auto sctr   = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr    = ad.get_live_audience();
              auto jinren = ad.get_predict_score(PredictType::PredictType_cvr);
              if (SPDM_enable_search_p2l_to_live_ecpm(session_data->get_spdm_ctx())) {
                jinren = 1.0;
              }
              ad.SetUnifyCtr(sctr * ctr * jinren,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr),
                          ad.get_live_audience_cmd_id());
            } else {
              // 双列直投
              auto sctr   = ad.get_predict_score(PredictType::PredictType_server_show_ctr);
              auto ctr    = ad.get_live_audience();
              ad.SetUnifyCtr(sctr * ctr,
                          kuaishou::ad::AD_DELIVERY, kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED,
                          ad.get_predict_cmd_id(PredictType::PredictType_server_show_ctr),
                          ad.get_live_audience_cmd_id());
            }
            ad.SetUnifyCvr(ad.get_predict_score(PredictType::PredictType_app_conversion_rate),
                          kuaishou::ad::AD_STANDARD_LIVE_PLAYED_STARTED, kuaishou::ad::AD_CONVERSION,
                          ad.get_predict_cmd_id(PredictType::PredictType_app_conversion_rate));
            ad.SetUnifyDeepCvr(ad.get_predict_score(PredictType::PredictType_purchase),
                            kuaishou::ad::AD_CONVERSION, kuaishou::ad::AD_PURCHASE_CONVERSION,
                            ad.get_predict_cmd_id(PredictType::PredictType_purchase));
          }
          break;
        default:
        break;
      }
    }
  }
  return StraRetCode::SUCC;
}

const char* SearchConstraintRPlugin::Name() {
  return "SearchConstraintRPlugin";
}

void SearchConstraintRPlugin::Clear() {}

bool SearchConstraintRPlugin::IsRun(
  const ContextData* session_data, const Params* params, AdRankUnifyScene pos, const AdList* adlist) {
    return true;
}

StraRetCode SearchConstraintRPlugin::Process(
  ContextData* session_data, Params* params, AdRankUnifyScene pos, AdList* ad_list) {
  if (session_data == nullptr || ad_list == nullptr || ad_list->Size() <= 0) {
    return StraRetCode::SUCC;
  }
  if (ad_list->GetOuterLoopAdSize() < 1) {
    return StraRetCode::SUCC;
  }
  for (auto* p_ad : ad_list->Ads()) {
    if (!p_ad || !p_ad->Is(AdFlag::is_outer_loop_ad)) continue;
    if (kuaishou::ad::DSP != p_ad->get_ad_source_type()) {
      continue;
    }
    SEARCH_AD_LIST_SKIP_LIVE_AD(session_data, p_ad)
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data, p_ad)
    AdCommon &ad = *p_ad;
    // MCB 浅度约束
    const auto ctr_info = ad.get_unify_ctr_info();
    const auto cvr_info = ad.get_unify_cvr_info();
    const auto deep_cvr_info = ad.get_unify_deep_cvr_info();
    const auto ltv_info = ad.get_unify_ltv_info();
    const kuaishou::ad::AdActionType ocpx_action_type = ad.get_ocpx_action_type();
    if ((deep_cvr_info.e_type != kuaishou::ad::UNKNOWN_ACTION_TYPE ||
        ltv_info.e_type != kuaishou::ad::UNKNOWN_ACTION_TYPE) &&
        cvr_info.e_type != ocpx_action_type) {  // 三段式
        ad.SetConstraintInfo(ctr_info.value * cvr_info.value, cvr_info.e_type);
    } else if (cvr_info.e_type != kuaishou::ad::UNKNOWN_ACTION_TYPE &&
               ctr_info.e_type != ocpx_action_type) {  // 二段式
        ad.SetConstraintInfo(ctr_info.value, ctr_info.e_type);
    } else {
        ad.SetConstraintInfo(0, kuaishou::ad::UNKNOWN_ACTION_TYPE);
    }
  }

  return StraRetCode::SUCC;
}
}  // namespace ad_rank
}  // namespace ks
