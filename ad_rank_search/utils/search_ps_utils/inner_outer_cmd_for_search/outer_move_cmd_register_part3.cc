#include "teams/ad/ad_rank_search/utils/search_ps_utils/inner_outer_cmd_for_search/outer_loop_cmd_register.h"

namespace ks {
namespace ad_rank {
using kuaishou::ad::AdEnum;
using ks::engine_base::PredictType;

// 迁移 Part.3
void RegisterOuterConfigCmdPart3(ks::ad_rank::CmdCurator* cmd_curator,
                                 ContextData* session_data) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_game_appoint_rate,
  "ad_dsp_game_appointment_cmd", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!(p_ctx->IsOcpc2Unit(p_ad)) &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK)) {
      return true;
    }
    return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click_purchase,
  "rank_ad_dsp_click_purchase", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (!p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_deep_conv_purchase) &&
        (p_ad->Is(AdFlag::is_insurance_ad) ||
         !(p_ad->get_twin_bid_strategy() == kuaishou::ad::AdEnum::ONLY_DEEP_DEVICE ||
           p_ad->get_twin_bid_strategy() == kuaishou::ad::AdEnum::MIN_OCPC_DEEP_DEVICE))) {
      return true;
    }
    return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click2_deep_rate,
  "ad_dsp_click2_credit_single_bid_v2", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
      return false;
    }
    if ((p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_credit)) ||
        (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN) ||
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT)) {
      return true;
    }
    return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_app_conversion_rate_sdpa_ecom_ensemble,
  "ad_dsp_sdpa_ecom_item_imp_conv", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->Is(AdFlag::is_sdpa_ecom_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
      return true;
    }
    return false;
  });

  // 用信率 完件->用信
  if (SPDM_enable_use_credit_cvr(session_data->get_spdm_ctx())) {
      cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_fin_use_credit_rate,
      "ad_dsp_fin_use_credit_rate", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((p_ctx->fin_use_credit_product_->count(p_ad->get_product_name()) > 0)
            && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT) {
          return true;
        }
        return false;
      });
  }

  // 小贷 ROI
  if (session_data->get_is_thanos_request()) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_fin_credit_roi_rate,
      "ad_dsp_fin_credit_roi_model", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->IsOcpc2Unit(p_ad) &&
            p_ctx->fin_credit_roi_white_product_ != nullptr &&
            p_ctx->fin_credit_roi_white_product_->count(p_ad->get_product_name()) > 0 &&
            p_ad->Is(AdFlag::is_finance_ad)) {
              return true;
        }
        return false;
  });
  }

  // 完件->授信 预估  cmd
  if (SPDM_enable_use_jinjian_credit_cvr(session_data->get_spdm_ctx())) {
      cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_fin_jinjian_credit_rate,
      "ad_dsp_fin_jinjian_credit_rate", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (( p_ctx->fin_jinjian_credit_product_ != nullptr
            && p_ctx->fin_jinjian_credit_product_->count(p_ad->get_product_name()) > 0)
            && p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT) {
          return true;
        }
        return false;
      });
  }

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_lps_acquisition,
  "ad_dsp_lps_acquisition", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    // 获客单出价, 表单 ecpc
    if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED
        && p_ctx->fin_edu_obtain_account_->count(p_ad->get_account_id()) > 0) ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION) {
      return true;
    }
    // 企微/IM 双出价
    if (p_ad->get_campaign_type() != kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_deep_conversion_type() ==
            kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION && (
        p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED
      )
    ) {
      return true;
    }
    //  获客双出价/双出价暗测
    return (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED &&
            p_ad->get_deep_conversion_type() ==
            kuaishou::ad::AdCallbackLog_EventType_EVENT_EFFECTIVE_CUSTOMER_ACQUISITION) ||
            p_ad->Is(AdFlag::is_tongxin_ad);
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_landingpage_submit,
   "ad_dsp_item_imp_lps_message", CMD_SOURCE_AD_DSP),
   [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->Is(AdFlag::is_self_service_ad) && !p_ad->Is(AdFlag::is_live) &&
         (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT)) {
       return true;
     }
     return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_landingpage_submit,
   "ad_dsp_item_imp_lps_wechat", CMD_SOURCE_AD_DSP),
   [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->Is(AdFlag::is_self_service_ad) && !p_ad->Is(AdFlag::is_live) &&
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) {
       return true;
     }
     return false;
  });
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_non_merchant_live_cvr,
  "ad_dsp_item_click_conv_photo", CMD_SOURCE_AD_DSP),
  [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
    if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
         p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||
         p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES)) {
      return true;
    }
    return false;
  });
}

}  // namespace ad_rank
}  // namespace ks
