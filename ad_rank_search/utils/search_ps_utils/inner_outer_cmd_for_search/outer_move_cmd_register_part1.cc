#include "teams/ad/ad_rank_search/utils/search_ps_utils/inner_outer_cmd_for_search/outer_loop_cmd_register.h"

namespace ks {
namespace ad_rank {
using kuaishou::ad::AdEnum;
using ks::engine_base::PredictType;

// 迁移 Part.1
void RegisterOuterConfigCmdPart1(ks::ad_rank::CmdCurator* cmd_curator,
                                 ContextData* session_data) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_7_day_game_conv_ltv,
      "ad_dsp_game_conv_ltv_seven_days_new", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
          return false;
        }
        if (p_ctx->enable_seven_day_ltv &&
          ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) ||
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS && p_ad->Is(AdFlag::is_roi_7d_roi_mcb_ecpc_ad)) ||  // NOLINT
           ((p_ctx->ad_roas_drop_admit_ != nullptr) &&
            (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
              != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
            p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
              != p_ctx->ad_roas_drop_admit_->admit().account_id().end())))) {
          return true;
        }
        return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_game_conv_ltv,
      "ad_dsp_game_conv_ltv", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->ad_roas_drop_admit_ != nullptr) {
            if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
               != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
                p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
               != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
               return true;
            }
        }
        return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_non_merchant_live_sctr,
      "ad_dsp_live_photo_sctr", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() ==
              kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT))) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->Is(AdFlag::is_self_service_ad)) {
          return true;
        }
        return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_non_merchant_live_ctr,
      "ad_dsp_item_imp_click_photo", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_p2l) && p_ad->Is(AdFlag::is_self_service_ad)) {
          return true;
        }
        return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_non_merchant_live_sctr,
      "ad_dsp_live_sctr", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
            p_ad->get_live_creative_type() ==
                kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT)) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live_ad) &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
          return true;
        }
        if (p_ad->Is(AdFlag::is_live_ad) && p_ad->Is(AdFlag::is_self_service_ad)) {
          return true;
        }
        return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_nextstay,
      "ad_dsp_server_conv_rention", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->ad_roas_drop_admit_ != nullptr) {
            if (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
               != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
                p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
               != p_ctx->ad_roas_drop_admit_->admit().account_id().end()) {
               return true;
            }
        }
        return false;
  });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_lps_valid_clues,
      "la_lps_valid_clues", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
          (!p_ctx->enable_real_deep_wechat_connected_lps_cvr_set &&
               p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE)) &&
          ((RankKconfUtil::laBidEnhancementDefaultWhiteList()->find(p_ad->get_product_name()) !=
                RankKconfUtil::laBidEnhancementDefaultWhiteList()->end() ||
            RankKconfUtil::laBidEnhancementProductWhiteList() ->find(p_ad->get_product_name()) !=
                RankKconfUtil::laBidEnhancementProductWhiteList()->end() ||
            (p_ad->Is(AdFlag::is_deep_conv_deep_valid_clues) ||
            (!p_ctx->enable_real_deep_wechat_connected_lps_cvr_set &&
      p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_WECHAT_CONNECTED)) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_INTENTION_CONFIRMED ||
            (!p_ctx->enable_real_deep_wechat_connected_lps_cvr_set &&
                 p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PHONE_CARD_ACTIVATE))) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED &&
            p_ad->Is(AdFlag::is_tongxin_ad))) {
          return true;
        }
        return false;
  });
  // SDPA 拉活付费模型单拆
  if (!SPDM_enable_sdpa_purchase_to_zhongtai(session_data->get_spdm_ctx())) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_purchase,
      "ad_rank_sdpa_app_invoked2_purchase", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_APP_ADVANCE &&
            p_ad->Is(AdFlag::is_sdpa_ad)) {
          return true;
        }
        return false;
  });
  }

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_7_day_stay,
      "ad_dsp_conv_week_stay", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (((p_ad->Is(AdFlag::is_deep_conv_retention) ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WEEK_STAY) &&
            (RankKconfUtil::sevenDayStayTwinAccountWhiteList()->find(p_ad->get_account_id()) !=
                RankKconfUtil::sevenDayStayTwinAccountWhiteList()->end() ||
            p_ad->get_enhance_conversion_type() ==
                kuaishou::ad::AdCallbackLog_EventType_EVENT_WEEK_STAY))) {
          return true;
        }
        return false;
      });
}

}  // namespace ad_rank
}  // namespace ks
