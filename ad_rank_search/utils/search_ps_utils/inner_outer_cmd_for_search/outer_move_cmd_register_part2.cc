#include "teams/ad/ad_rank_search/utils/search_ps_utils/inner_outer_cmd_for_search/outer_loop_cmd_register.h"

namespace ks {
namespace ad_rank {
using kuaishou::ad::AdEnum;
using ks::engine_base::PredictType;

// 迁移 Part.2
void RegisterOuterConfigCmdPart2(ks::ad_rank::CmdCurator* cmd_curator,
                                 ContextData* session_data) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_credit_conv_grant,
                                          "jinjian_to_credit_grant", CMD_SOURCE_AD_DSP),
                           [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
                             auto ad_ret = p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN ||
                                           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT ||
                                           p_ad->get_deep_conversion_type() ==
                                               kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN ||
                                           p_ad->get_deep_conversion_type() ==
                                               kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT;
                             return ad_ret;
                           });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_non_merchant_live_cvr,
    "ad_dsp_live_click_conv_live", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {  // NOLINT
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE_CONVERSION ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS ||
            p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY ||  // NOLINT
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
          return true;
        }
      }
      return false;
    });

  bool enable_conv_quality_ecpc =
      session_data->get_spdm_ctx().TryGetBoolean("enable_conv_quality_ecpc", false);
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_quality_score,
    "rank_conv_quality_score", CMD_SOURCE_AD_DSP),
    [enable_conv_quality_ecpc](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (enable_conv_quality_ecpc &&
          p_ad->Is(AdFlag::is_conv_quality_product_account_white_list_ad)) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click2_deep_rate,
    "rank_click2_deep_unified", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          p_ad->get_industry_parent_id_v3() == 1003) {
        return false;
      }
      auto kIsAliOuterDeliveryAccountList =
        (RankKconfUtil::aliOuterDeliveryAccountList()->find(p_ad->get_account_id()) !=
         RankKconfUtil::aliOuterDeliveryAccountList()->end() ||
         p_ad->get_campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE);

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          kIsAliOuterDeliveryAccountList) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          p_ad->Is(AdFlag::is_dpa)) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_REGISTER) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM) {
        return false;
      }
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_deep_unified) && !p_ad->Is(AdFlag::is_single_bid_purchase_v2) &&  // NOLINT
          !p_ad->Is(AdFlag::is_ad_watch_times) && !p_ad->Is(AdFlag::is_ad_watch_5_times) && !p_ad->Is(AdFlag::is_ad_watch_10_times) &&  // NOLINT
          !p_ad->Is(AdFlag::is_ad_watch_20_times) && !p_ad->Is(AdFlag::is_single_jinjian) && !p_ad->Is(AdFlag::is_single_credit) &&  // NOLINT
          !p_ad->Is(AdFlag::is_single_valid_clues) && !p_ad->Is(AdFlag::is_add_wechat) && !p_ad->Is(AdFlag::is_multi_conv) &&  // NOLINT
          p_ad->get_unit_type() != kuaishou::ad::AdEnum_UnitType_JK_UNIT && !p_ad->Is(AdFlag::is_deeper_conversion) &&  // NOLINT
          p_ad->get_ocpx_action_type() != kuaishou::ad::EVENT_KEY_INAPP_ACTION) {
        return true;
      }
      return false;
    });

  bool enable_mt_lps_exp_tmp =
      session_data->get_spdm_ctx().TryGetBoolean("enable_mt_lps_exp_tmp", false);
  bool enable_mt_lps_launch =
      session_data->get_spdm_ctx().TryGetBoolean("enable_mt_lps_launch", false);

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click2_deep_rate,
    "ad_dsp_click2_valid_clues_bid", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) &&
          (p_ad->Is(AdFlag::is_single_valid_clues) || p_ad->Is(AdFlag::is_deep_conv_valid_clues))) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click_app_invoked,
    "dpa_click_app_invoked", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->Is(AdFlag::is_dpa) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
        return true;
      }
      return false;
    });

  bool enable_mt_purchase_exp_tmp =
      session_data->get_spdm_ctx().TryGetBoolean("enable_mt_purchase_exp_tmp", false);
  bool enable_mt_purchase_launch =
      session_data->get_spdm_ctx().TryGetBoolean("enable_mt_purchase_launch", false);
  bool enable_pre_mt_purchase = SPDM_enable_pre_mt_purchase(session_data->get_spdm_ctx());

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_non_merchant_live_ctr,
    "ad_dsp_live_played_click_live", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
        p_ad->get_live_creative_type() ==
          kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_live_ad) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_AD_POP_RECRUIT_LIVE) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_live_ad) && p_ad->Is(AdFlag::is_self_service_ad)) {
        return true;
      }
      return false;
    });

  if (session_data->get_is_thanos_request()) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_cvr,
    "ad_adx_thanos_cvr", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_ad_source_type() == kuaishou::ad::ADX) {
        return true;
      }
      return false;
    });
  }

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_7_day_pay_times,
    "ad_dsp_7_day_multi_pay_times", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_1_day_pay_times,
    "ad_dsp_1_day_pay_times", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_2_7_day_pay_times,
    "ad_dsp_2_7_day_pay_times", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_1_day_pay_amount,
    "ad_dsp_1_day_pay_amount", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_2_7_day_pay_amount,
    "ad_dsp_2_7_day_pay_amount", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_appinvoke_nextstay,
    "ad_dsp_server_app_invoke_nextstay", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED &&
          p_ad->Is(AdFlag::is_deep_conv_retention)) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(
      new CmdWrapper(engine_base::PredictType::PredictType_cid_mcda_score,
                                    "ad_dsp_server_cid_mcda_boost_model",
                                    CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (p_ctx->session_data_->get_is_thanos_request() &&
            p_ctx->IsCidMcdaAd(p_ad)) {
          return true;
        }
        return false;
      });

  if (session_data->get_is_thanos_request()) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_server_show_cvr,
    "ad_dsp_server_show_cvr_ocpm", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if ((p_ad->get_ad_source_type() == kuaishou::ad::ADX)) {
        return false;
      }
      if (p_ctx->IsOcpc2Unit(p_ad) && !p_ctx->IsAdxLargeCreatives(p_ad)) {
        if (p_ad->Is(AdFlag::is_dpa)) {
          return false;
        }

        if (p_ad->Is(AdFlag::is_direct_ecom) ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) {
          return false;
        }

        if (p_ctx->IsOcpc2Unit(p_ad) &&
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
            (p_ctx->aliOuterDeliveryAccountList->count(p_ad->get_account_id()) > 0
              || p_ad->get_campaign_sub_type() ==
              kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE)) {
          return true;
        }


        if (RankKconfUtil::storyAdPurchaseWhitelist()->find(p_ad->get_product_name()) !=
            RankKconfUtil::storyAdPurchaseWhitelist()->end()) {
          return true;
        }

        if (p_ad->Is(AdFlag::is_single_bid_purchase_v2) ||
            p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_CREDIT_GRANT ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT ||
            p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_JINJIAN ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ADD_WECHAT ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_REGISTER) {
          return true;
        }
      }
      return false;
    });
  }

  if (session_data->common_r_
        ->GetIntCommonAttr("enable_clean_inefficient_cmdkey").value_or(0) == 0) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_non_merchant_live_cvr,
    "ad_dsp_live_click_lps_live", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_DIRECT_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE)) {
        return true;
      }
      if (p_ad->Is(AdFlag::is_live_ad) && p_ad->Is(AdFlag::is_self_service_ad) &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT) {
        return true;
      }
      return false;
    });
  }


  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_deep_rate,
    "ad_dsp_click_valid_clues_bid", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->IsOcpc2Unit(p_ad) && (p_ad->Is(AdFlag::is_single_valid_clues) || p_ad->Is(AdFlag::is_deep_conv_valid_clues))) {  // NOLINT
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_deep_rate,
    "ad_dsp_click_jinjian_single_bid", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_jinjian)) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_deep_rate,
    "ad_dsp_click_credit_single_bid", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_credit)) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_deep_rate,
    "click_deep_unified", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad)) {
        return false;
      }

      if (p_ad->get_unit_type() == kuaishou::ad::AdEnum_UnitType_JK_UNIT) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          p_ad->get_industry_parent_id_v3() == 1003) {
        return false;
      }
      bool IsAliOuterDeliveryAccountList =
        (RankKconfUtil::aliOuterDeliveryAccountList()->find(p_ad->get_account_id()) !=
          RankKconfUtil::aliOuterDeliveryAccountList()->end() ||
        p_ad->get_campaign_sub_type() == kuaishou::ad::AdEnum_CampaignSubType_ALI_SUB_CAMPAIGN_TYPE);

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          p_ad->get_industry_parent_id_v3() == 1003) {
        return false;
      }

      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT &&
          IsAliOuterDeliveryAccountList) {
        return false;
      }

      if (p_ad->Is(AdFlag::is_deep_unified) &&
          !p_ad->Is(AdFlag::is_single_bid_purchase) &&
          !p_ad->Is(AdFlag::is_single_jinjian) &&
          !p_ad->Is(AdFlag::is_single_credit) &&
          !p_ad->Is(AdFlag::is_single_valid_clues) &&
          !p_ad->Is(AdFlag::is_add_wechat) &&
          !p_ad->Is(AdFlag::is_multi_conv) &&
          !p_ad->Is(AdFlag::is_ad_watch_times) &&
          !p_ad->Is(AdFlag::is_ad_watch_5_times) &&
          !p_ad->Is(AdFlag::is_ad_watch_10_times) &&
          !p_ad->Is(AdFlag::is_ad_watch_20_times)) {
        return true;
      }
      return false;
    });

  if (session_data->get_is_thanos_request()) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_deep_rate,
    "click_order_submit_cmd", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) && p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUBMIT) {
        return true;
      }
      return false;
    });
  }

  if (session_data->get_is_thanos_request()) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_c2_game_appoint_rate,
    "ad_dsp_game_appointment_c2", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_FORM ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APPOINT_JUMP_CLICK)) {
        return true;
      }
      return false;
    });
  }

  if (session_data->get_is_thanos_request()) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_landingpage_submit,
    "ad_dsp_item_imp_lps_jinjian_credit_grant", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->IsOcpc2Unit(p_ad) &&
          (p_ad->Is(AdFlag::is_single_jinjian) || p_ad->Is(AdFlag::is_single_credit))) {
        return true;
      }
      return false;
    });
  }

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_non_merchant_live_cvr,
    "ad_dsp_item_click_lps_photo_to_live", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (p_ctx->enable_ad_dsp_live_lps_photo_to_live &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_FANS_LIVE_STREAM_PROMOTE &&
          p_ad->get_live_creative_type() == kuaishou::ad::AdEnum_LiveCreativeType_PHOTO_TO_LIVE_STREAM_FOR_NON_MERCHANT &&  // NOLINT
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE)) {
        return true;
      }
      return false;
    });
}  // NOLINT

}  // namespace ad_rank
}  // namespace ks
