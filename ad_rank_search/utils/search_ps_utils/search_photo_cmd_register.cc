#include "teams/ad/ad_rank_search/utils/search_ps_utils/search_photo_cmd_register.h"

#include <vector>
#include <string>
#include "absl/strings/str_split.h"
#include "teams/ad/ad_rank_search/utils/utility/utility.h"
#include "teams/ad/ad_rank_search/utils/spdm/spdm_switches.h"


namespace ks {
namespace ad_rank {
using ks::engine_base::PredictType;

#define IS_PHOTO(p_ad) {                             \
  if (p_ad->get_queue_type() != RankAdListType::NORMAL_PHOTO_AD) {  \
    return false;                                       \
  }                                                     \
}

using kuaishou::ad::AdActionType;
using kuaishou::ad::AdEnum;

void RegisterSearchPhotoCmd(ks::ad_rank::CmdCurator* cmd_curator, ContextData* session_data) {
  if (session_data->get_is_feed()) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_server_client_show_rate,
        "ad_dsp_server_client_show_search", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        return true;
      });
  }

  if (session_data->get_is_feed() && !session_data->get_is_search_good_card()) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_ctr, "app_ctr_search_cmd",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        return true;
      });
  }

  if (session_data->get_is_feed() && session_data->get_is_search_good_card()) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_ctr, "app_ctr_goods_search_cmd",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        return true;
      });
  }

  if (session_data->get_is_thanos_request() && !session_data->get_is_search_inner_stream()) {
      cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_server_show_ctr,
        "server_show_item_imp_ocpm_search_cmd", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        return true;
      });
  }


  // 搜索内外不同流 photo 独立 cmdkey
  if (session_data->get_is_search_inner_stream()) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_server_show_ctr,
        "search_sctr_photo_inner", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        return true;
      });
  }

  static const std::vector<PredictType> search_pos_sctr_all_pos_predicts = {
      PredictType::PredictType_search_sctr_pos0_sctr2,
      PredictType::PredictType_search_sctr_pos1_sctr2,
      PredictType::PredictType_search_sctr_pos2_sctr2,
      PredictType::PredictType_search_sctr_pos4_sctr2,
      PredictType::PredictType_search_sctr_pos5_sctr2,
      PredictType::PredictType_search_sctr_pos6_sctr2,
      PredictType::PredictType_search_sctr_pos7_sctr2,
      PredictType::PredictType_search_sctr_pos8_sctr2,
      PredictType::PredictType_search_sctr_pos9_sctr2,
      PredictType::PredictType_search_sctr_pos10_sctr2,
      PredictType::PredictType_search_sctr_page1_sctr2,
      PredictType::PredictType_search_sctr_pos_inner};
  static const std::vector<PredictType> search_pos_sctr_base_predicts = {
      PredictType::PredictType_search_sctr_pos1_sctr1,
      PredictType::PredictType_search_sctr_pos1_sctr2,
      PredictType::PredictType_search_sctr_pos2_sctr1,
      PredictType::PredictType_search_sctr_pos2_sctr2,
      PredictType::PredictType_search_sctr_pos5_sctr1,
      PredictType::PredictType_search_sctr_pos5_sctr2,
      PredictType::PredictType_search_sctr_pos7_sctr1,
      PredictType::PredictType_search_sctr_pos7_sctr2,
      PredictType::PredictType_search_sctr_pos8_sctr1,
      PredictType::PredictType_search_sctr_pos8_sctr2,
      PredictType::PredictType_search_sctr_page1_sctr1,
      PredictType::PredictType_search_sctr_page1_sctr2};
  const auto& search_pos_sctr_predicts = SPDM_enable_search_sctr_all_pos(session_data->get_spdm_ctx())
                                             ? search_pos_sctr_all_pos_predicts
                                             : search_pos_sctr_base_predicts;
  if (session_data->get_enable_search_pos_sctr2() &&
      session_data->get_is_feed() &&
      !session_data->get_is_search_live_tab() &&
      !session_data->get_is_search_good_tab()) {
    cmd_curator->RegisterCmd(
      new CmdWrapper(search_pos_sctr_predicts, "search_pos_sctr_cmd",
                     CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        return true;
      });
  }

  // ctr 分位置预估
  static const std::vector<PredictType> search_pos_ctr_predicts = {
      PredictType::PredictType_search_ctr_pos0,
      PredictType::PredictType_search_ctr_pos1,
      PredictType::PredictType_search_ctr_pos2,
      PredictType::PredictType_search_ctr_pos3,
      PredictType::PredictType_search_ctr_pos4,
      PredictType::PredictType_search_ctr_pos5,
      PredictType::PredictType_search_ctr_pos6,
      PredictType::PredictType_search_ctr_pos7,
      PredictType::PredictType_search_ctr_pos8,
      PredictType::PredictType_search_ctr_pos9,
      PredictType::PredictType_search_ctr_pos10,
      PredictType::PredictType_search_ctr_page1};
  if (session_data->get_is_feed() &&
      SPDM_enable_search_pos_ctr_v1(session_data->get_spdm_ctx()) &&
      !session_data->get_is_search_live_tab() && !session_data->get_is_search_good_tab()) {
    cmd_curator->RegisterCmd(
      new CmdWrapper(search_pos_ctr_predicts, "search_pos_ctr_cmd",
                     CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        return true;
      });
  }

  // 搜索广告(非短剧) 激活模型
  if (!(session_data->get_pos_manager_base().IsSearchInspireThanosRequest() &&
        session_data->get_rank_request()->ad_request().search_info().request_source() == 2)) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_app_conversion_rate,
        "app_conversion_rate_search_cmd",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (p_ctx->enable_search_series_iaa_conv &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION &&
           (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP)) {
            return false;
        }
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
            p_ad->Is(AdFlag::is_deep_unified))) {
          return true;
        }
        return false;
      });
  }

  // 短剧 IAA 激活模型
  bool enable_search_series_iaa_conv = SPDM_enable_search_series_iaa_conv(session_data->get_spdm_ctx());
  if (enable_search_series_iaa_conv) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_app_conversion_rate,
      "search_series_iaa_conv_cmd",
      CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      IS_PHOTO(p_ad)
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP)) {
            return true;
      }
      return false;
    });
  }

  // 广告盒子激活模型 cmd
  if (session_data->get_pos_manager_base().IsSearchInspireThanosRequest() &&
      session_data->get_rank_request()->ad_request().search_info().request_source() == 2) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_app_conversion_rate,
        "search_ad_adbox_conv_model",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION) {
          return true;
        }
        return false;
      });
  }


  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_app_conversion_rate,
        "app_conversion_rate_supp_search_cmd", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS)) {
          return true;
        }
        return false;
      });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_landingpage_submit,
        "landingpage_submit_rate_search_cmd", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_SUCCESSED ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_PRIVATE_MESSAGE_SENT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::LEADS_SUBMIT ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_EFFECTIVE_CUSTOMER_ACQUISITION) &&
             !p_ad->Is(AdFlag::is_direct_ecom))) {
          return true;
        }
        // 添加企业微信
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_WECHAT_CONNECTED) {
            return true;
        }
        return false;
      });

  if (!session_data->get_is_search_good_card()) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_search_rank_hc,
        "rank_hc_order_paid_search",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
             p_ad->Is(AdFlag::is_reco_roas) ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) {
          return true;
        }
        return false;
      });
  }

  bool enable_search_adbox_order_pay_model =
      SPDM_enable_search_adbox_order_pay_model(session_data->get_spdm_ctx());
  if (enable_search_adbox_order_pay_model &&
      session_data->get_pos_manager_base().IsSearchInspireThanosRequest() &&
      session_data->get_rank_request()->ad_request().search_info().request_source() == 2 &&
      !(session_data->get_is_search_good_card())) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_c1_order_paid,
        "search_adbox_order_paid",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
             p_ad->Is(AdFlag::is_reco_roas) ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) {
          return true;
        }
        return false;
      });
  }

  if (!(enable_search_adbox_order_pay_model &&
        session_data->get_pos_manager_base().IsSearchInspireThanosRequest() &&
        session_data->get_rank_request()->ad_request().search_info().request_source() == 2) &&
      !(session_data->get_is_search_good_card())) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_c1_order_paid,
        "c1_order_paid_search",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
             p_ad->Is(AdFlag::is_reco_roas) ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID &&
              !SPDM_enable_search_inner_cid(p_ctx->session_data_->get_spdm_ctx())))) {
          return true;
        }
        return false;
      });
  }


if(SPDM_enable_search_inner_cid(session_data->get_spdm_ctx())) {
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_c1_order_paid,
      "cid_order_paid_search",
      CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      IS_PHOTO(p_ad)
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID) {
        return true;
      }
      return false;
    });
}

  bool close_c1_order_paid_search_goods = SPDM_close_c1_order_paid_search_goods(session_data->get_spdm_ctx());
  if (!(enable_search_adbox_order_pay_model &&
        session_data->get_pos_manager_base().IsSearchInspireThanosRequest() &&
        session_data->get_rank_request()->ad_request().search_info().request_source() == 2) &&
        !close_c1_order_paid_search_goods) {
      cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_c1_order_paid,
        "c1_order_paid_search_goods",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if (  // 仅在 明投商品卡上生效, kbox、商品 Tab 走默认逻辑
            p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
             p_ad->Is(AdFlag::is_reco_roas) ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) {
          return true;
        }
        return false;
      });
  }

  if (!(enable_search_adbox_order_pay_model &&
        session_data->get_pos_manager_base().IsSearchInspireThanosRequest() &&
        session_data->get_rank_request()->ad_request().search_info().request_source() == 2) &&
      session_data->get_is_search_good_card()) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_c1_order_paid,
        "c1_order_paid_new_search_goods",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
             p_ad->Is(AdFlag::is_reco_roas) ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID &&
              !SPDM_enable_search_inner_cid(p_ctx->session_data_->get_spdm_ctx())))) {
          return true;
        }
        return false;
      });
  }


  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_game_conv_ltv,
        "ad_dsp_game_conv_ltv_search",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        // 付费短剧 roas cmdkey 独立
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
            return false;
        }

        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
             !(p_ctx->seven__days_ltv_account_map_->count(p_ad->get_account_id()) > 0)) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             p_ad->get_industry_parent_id_v3() == 1018)) {
          return true;
        }
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) {
            return true;
        }
        return false;
      });

  // 搜索 IAA_ROAS/IAAP ltv 模型
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_key_action_ltv0,
        "ad_key_action_ltv0_search",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        // 付费短剧独立 iaa ltv cmdkey
        if (!(p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
              p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP)) {
                return true;
        }
        return false;
      });
  // 付费短剧 IAA_ROAS/IAAP ltv 模型
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_key_action_ltv0,
        "ad_series_iaa_ltv0_search",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION &&
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SERIAL_IAA_ROAS ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP)) {
              return true;
        }
        return false;
      });


  // 搜索付费短剧  ROAS
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_game_conv_ltv,
        "search_series_roi_cmd",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
            p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
          return true;
        }
        return false;
    });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click_purchase_rate_single_bid,
        "click_purchase_single_bid_search",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (!p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_bid_purchase_v2) &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum::SITE_PAGE &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum::TAOBAO &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum::LANDING_PAGE) {
          return true;
        }
        return false;
      });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click_purchase_rate_single_bid,
        "lps_click_purchase_single_bid_search", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (((!p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_bid_purchase_v2)) &&
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE ||
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum::TAOBAO ||
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MINI_APP_CAMPAIGN_TYPE ||
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LANDING_PAGE)) {
          return true;
        }
        return false;
      });

  if (session_data->get_is_thanos_request()) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click2_purchase_rate_single_bid,
        "lps_rank_click2_purchase_single_bid_search", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if ((p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_bid_purchase_v2) &&
             (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::SITE_PAGE ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::TAOBAO ||
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LANDING_PAGE))) {
          return true;
        }
        return false;
      });
  }


  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click2_purchase_rate_single_bid,
        "lps_rank_click2_purchase_single_bid_search2", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
             p_ad->get_campaign_type() != kuaishou::ad::AdEnum::APP &&
             p_ad->get_campaign_type() != kuaishou::ad::AdEnum::APP_ADVANCE)) {
          return true;
        }
        return false;
      });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_purchase,
        "app_invoked2_purchase_search", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
            (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP ||
             p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE)) {
          return true;
        }
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS)) {
          return true;
        }
        return false;
      });

  // 付费短剧付费
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_purchase,
        "search_series_purchase_cmd", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
          return true;
        }
        return false;
      });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_purchase,
        "app_invoked_conversion_purchase_search", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CONVERSION ||
             p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED)
            && p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY) {
          return true;
        }
        return false;
      });

  if (session_data->get_is_thanos_request()) {
    cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click2_purchase_rate_single_bid,
        "rank_click2_purchase_single_bid_search", CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if ((p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_bid_purchase_v2)
             && p_ad->get_campaign_type() != kuaishou::ad::AdEnum::SITE_PAGE
             && p_ad->get_campaign_type() != kuaishou::ad::AdEnum::TAOBAO
             && p_ad->get_campaign_type() != kuaishou::ad::AdEnum::LANDING_PAGE)) {
          return true;
        }
        return false;
      });
  }

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_nextstay,
        "conv_rention_search_cmd",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (((p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_NEXTDAY_STAY ||
              p_ad->get_deep_conversion_type() == kuaishou::ad::AdCallbackLog_EventType_EVENT_24H_STAY) ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_NEXTDAY_STAY))) {
              bool enable_search_next_day_rs_filter_v2 =
                   SPDM_enable_search_next_day_rs_filter_v2(p_ctx->session_data_->get_spdm_ctx());
              if (enable_search_next_day_rs_filter_v2) {
                if (p_ctx->ad_next_stay_rs_admit_ != nullptr) {
                  auto account_id = p_ctx->ad_next_stay_rs_admit_->admit().find(p_ad->get_account_id());
                  if (account_id != p_ctx->ad_next_stay_rs_admit_->admit().end()) {
                    double rs_score = p_ad->get_search_relevance_score();
                    for (auto& iter : account_id->second.feature_value()) {
                      double left_interval = iter.left_interval();
                      double right_interval =  iter.right_interval();
                      LOG_EVERY_N(INFO, 100000) << "dpb@debug ad_next_stay_rs_admit_:"
                              << "rs_score is: " << std::to_string(rs_score)
                              << "; left_interval is: " << std::to_string(left_interval)
                              << "; right_interval is: " << std::to_string(right_interval);
                      if (left_interval >= 0.0 && right_interval >= 0.0 &&
                          left_interval < rs_score && rs_score < right_interval) {
                        return false;
                      }
                    }
                  }
                }
              }
          return true;
        }
        return false;
      });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click2_app_invoked,
        "search_click2_app_invoked",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (p_ctx->IsOcpc2Unit(p_ad) && !p_ad->Is(AdFlag::is_dpa) &&
             p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_APP_INVOKED) {
          return true;
        }
        if ((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS)) {
          return true;
        }
        return false;
      });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click_app_invoked,
        "search_click_app_invoked",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (((!p_ad->Is(AdFlag::is_dpa) && p_ad->get_ocpx_action_type() ==
                                     kuaishou::ad::EVENT_APP_INVOKED) ||
             (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
              p_ad->get_campaign_type() == kuaishou::ad::AdEnum::APP_ADVANCE))) {
          return true;
        }
        if (
            (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ENTER_MINI_PROGRAM ||
            p_ad->get_ocpx_action_type() == kuaishou::ad::MINI_APP_ROAS)) {
          return true;
        }
        return false;
      });

  // 付费短剧唤端 cmd
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_click_app_invoked,
        "search_series_invoked_cmd",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        // 付费短剧复用信息流预估链路 一跳唤端模型
        if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::AD_KWAI_SERIAL_PROMOTION) {
          return true;
        }
        return false;
      });

  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_conv_key_inapp_action_rate,
        "search_rank_key_action",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (((p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_KEY_INAPP_ACTION) ||
             (p_ctx->key_action_account_map_->count(p_ad->get_account_id()) > 0))) {
          return true;
        }
        return false;
      });
  cmd_curator->RegisterCmd(new CmdWrapper(PredictType::PredictType_cvr, "ad_dsp_cvr_cmd_search",
        CMD_SOURCE_AD_DSP),
      [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
        IS_PHOTO(p_ad)
        if (p_ad->get_ocpx_action_type() == AdActionType::AD_ITEM_CLICK ||
            (p_ad->get_bid_type() == kuaishou::ad::AdEnum::CPC &&
              p_ad->Is(AdFlag::is_search_bidword) &&
              p_ad->get_quick_search() == 0) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_PURCHASE &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum::APP_ADVANCE &&
              p_ad->get_campaign_type() != kuaishou::ad::AdEnum::APP) ||
            (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT ||
               p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN)) {
          return true;
        }
        return false;
      });

  if (!(session_data->get_is_search_good_card())) {
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_merchant_ltv,
                   "search_photo_roi", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      IS_PHOTO(p_ad)
      if (SPDM_enable_search_inner_cid(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
        return false;
      }
      if ((p_ad->Is(AdFlag::is_jinniu_roas) || p_ad->Is(AdFlag::is_reco_roas))) {
        return true;
      }
      return false;
    });
  }


  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_merchant_ltv,
                   "search_goods_roi", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (  // 仅在明投商品卡生效
          !p_ctx->close_search_goods_roi &&
          p_ad->get_creative_material_type() == kuaishou::ad::AdEnum_CreativeMaterialType_ITEM_CARD &&
          (p_ad->Is(AdFlag::is_jinniu_roas) || p_ad->Is(AdFlag::is_reco_roas))) {
        return true;
      }

      // GMV 回捞需要计算 egpm 准入订单目标 GMV 预估
      if (SPDM_enable_goods_search_gmv_callback(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ctx->session_data_->get_is_search_good_card() &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) {
          return true;
      }
      return false;
    });

  if (session_data->get_is_search_good_card()) {
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_merchant_ltv,
                   "search_photo_goods_roi", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (SPDM_enable_search_inner_cid(p_ctx->session_data_->get_spdm_ctx()) &&
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
        return false;
      }
      if ((p_ad->Is(AdFlag::is_jinniu_roas) || p_ad->Is(AdFlag::is_reco_roas))) {
        return true;
      }
      // GMV 回捞需要计算 egpm 准入订单目标 GMV 预估
      if (SPDM_enable_goods_search_gmv_callback(p_ctx->session_data_->get_spdm_ctx()) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_ORDER_PAIED ||
           p_ad->get_ocpx_action_type() == kuaishou::ad::CID_EVENT_ORDER_PAID)) {
          return true;
      }
      return false;
    });
  }


if(SPDM_enable_search_inner_cid(session_data->get_spdm_ctx())) {
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_merchant_ltv,
                    "cid_search_photo_roi",
                    CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      IS_PHOTO(p_ad)
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS) {
        return true;
      }
      return false;
    });
}

  // 涨粉
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_c1_merchant_follow,
                   "search_server_show_merchant_follow_ratio", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      IS_PHOTO(p_ad)
      if (((!p_ctx->IsOcpc2Unit(p_ad)) &&
          (p_ad->Is(AdFlag::is_merchant_follow_roi) || p_ad->Is(AdFlag::is_photo_ad_merchant_follow_quality) ||  // NOLINT
          p_ad->Is(AdFlag::is_photo_ad_merchant_follow_fast) ||
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_FOLLOW ||
          p_ad->Is(AdFlag::is_photo_ad_merchant_follow_mobile)))) {
        return true;
      }
      return false;
    });

  // 直播预约
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_cvr, "ad_dsp_search_cvr_cmd_ecom", CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      IS_PHOTO(p_ad)
      if (((p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_LIVE_STREAM_PROMOTE ||
          p_ad->get_campaign_type() == kuaishou::ad::AdEnum_CampaignType_MERCHANT_RECO_PROMOTE) &&
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ITEM_CLICK))) {
        return true;
      }
      return false;
    });

  // 搜索外循环 game iaa ltv7
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_search_industry_game_iaa_ltv7,
                   "ad_search_game_iaa_ltv7",
                   CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_IAA_7DAY_ROAS) {
        return true;
      }
      bool is_iaa_roi7_target_account = utility::IsGameIaaRoi7TargetAccount(p_ctx->session_data_, *p_ad);
      if ((p_ctx->enable_iaap_request_iaa_ltv7 &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS_IAAP) ||
          (p_ctx->enable_iaap_7r_request_iaa_ltv7 &&
          p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS_IAAP)) {
        return true;
      }
      if (p_ctx->enable_white_acount_request_industry_game_iaa_ltv7) {
        if (p_ctx->enable_request_industry_game_iaa_ltv7
            && (is_iaa_roi7_target_account || p_ctx->enable_request_industry_game_iaa_ltv7_all_product)
            && p_ad->Is(AdFlag::is_iaa_game_ltv)) {
            return true;  // 只对 game IAA ltv 样本进行 7r 打分
        }
      } else if (p_ctx->enable_request_industry_game_iaa_ltv7
          && p_ad->Is(AdFlag::is_iaa_game_ltv)) {
          return true;  // 只对 game IAA ltv 样本进行 7r 打分
      }
      return false;
    });

  // 搜索外循环 首日付费次数 独立
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_1_day_pay_times,
                   "ad_search_1_day_pay_times",
                   CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
        return true;
      }
      return false;
    });

  // 搜索外循环 七日付费次数 独立
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_7_day_pay_times,
                   "ad_search_7_day_pay_times",
                   CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_7_DAY_PAY_TIMES) {
        return true;
      }
      return false;
    });

  // 搜索外循环 七日 ROI 独立
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_7_day_game_conv_ltv,
                   "ad_search_game_conv_ltv_seven_days",
                   CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ctx->enable_seven_day_ltv &&
        ((p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) ||
          (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS && p_ad->Is(AdFlag::is_roi_7d_roi_mcb_ecpc_ad)) ||  // NOLINT
          ((p_ctx->ad_roas_drop_admit_ != nullptr) &&
          (p_ctx->ad_roas_drop_admit_->admit().product_name().find(p_ad->get_product_name())
            != p_ctx->ad_roas_drop_admit_->admit().product_name().end() ||
          p_ctx->ad_roas_drop_admit_->admit().account_id().find(p_ad->get_account_id())
            != p_ctx->ad_roas_drop_admit_->admit().account_id().end())))) {
        return true;
      }
      if (p_ad->get_admit_7r_conv_ltv()) {
        return true;
      }
      return false;
    });

  // 搜索外循环 七日 ROI 二阶段 cmd 预测独立
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_1_day_pay_amount,
                   "ad_search_1_day_pay_amount",
                   CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
        return true;
      }
      return false;
    });

  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_2_7_day_pay_amount,
                   "ad_search_2_7_day_pay_amount",
                   CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_SEVEN_DAY_ROAS) {
        return true;
      }
      return false;
    });

  // 搜索授信独立
  cmd_curator->RegisterCmd(
    new CmdWrapper(PredictType::PredictType_click2_deep_rate,
                   "ad_search_credit_single_bid",
                   CMD_SOURCE_AD_DSP),
    [](const AdCommon* p_ad, CmdCuratorContext* p_ctx) {
      if (!SPDM_enable_search_outer_cmd_independent(p_ctx->session_data_->get_spdm_ctx())) {
        return false;
      }
      if ((p_ctx->IsOcpc2Unit(p_ad) && p_ad->Is(AdFlag::is_single_credit)) ||
        (p_ad->get_ocpx_action_type() == kuaishou::ad::EVENT_JINJIAN) ||
        (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_CREDIT_GRANT)) {
        return true;
      }
      return false;
    });
}

}  // namespace ad_rank
}  // namespace ks
